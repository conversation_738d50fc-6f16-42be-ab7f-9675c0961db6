--LBELxx：一般分行
--LJELxx：簡易型分行
--LAELxx：營運中心
--LSELxx：授管處
--LDELxx：債管處
--LGELxx：稽核處 (總行G)
--LHELxx：徵信中心 (總行C)
--LOELxx：海外分行
--LMELxx：大陸分行 (目前Notes無)
--LFELxx：國外部/國金部/金控總部分行(X)
--LKELxx：國外部/國金部/金控總部分行
--LPELxx：海外分行(有海外總行的海外分行)
--LQELxx：海外總行(澳洲/加拿大)
--LRELxx：海外總行(泰國)


--select * from COM.BELSRLF where substr(ROLCODE,1,1)='L' order by ROLCODE,PGMCODE
--select ROLCODE,count(*) from COM.BELSRLF where substr(ROLCODE,1,1)='L' group by ROLCODE order by ROLCODE
--delete from COM.BELSRLF where ROLCODE in (select ROLCODE from COM.BELSRLE where TYPE='L')
delete from COM.BELSRLF where substr(ROLCODE,1,1)='L';


--################--
--##一般角色設定##--
--################--

--------------------
-- LBEL01 (在一般分行經辦權限)--
--------------------
----建檔維護(資訊處)--
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LBEL01', '3',316001 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LBEL01', '3',326997 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME, PGMDEPT) values ('LBEL01', '3',326998 , 7, 'system', current timestamp, '900');


--------------------
-- LBEL02 (在一般分行主管權限)--
--------------------
----建檔維護(資訊處)--
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LBEL02', '3',316001 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LBEL02', '3',326997 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME, PGMDEPT) values ('LBEL02', '3',326998 , 7, 'system', current timestamp, '900');
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LBEL02', '3',326999 , 6, 'system', current timestamp);


--------------------
-- LBEL06 (在分行內部查核權限)--
--------------------
--稽核工作底稿--
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LBEL06', '3',314001 , 7, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LBEL06', '3',324005 , 7, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LBEL06', '3',334009 , 7, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LBEL06', '3',334010 , 7, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LBEL06', '3',334011 , 7, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LBEL06', '3',334018 , 7, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LBEL06', '3',324006 , 7, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LBEL06', '3',334012 , 7, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LBEL06', '3',334013 , 7, 'system', current timestamp);


--------------------
-- LJEL01 (在簡易型分行經辦權限)--
--------------------
--(第四階段才開放)
--(套LBEL01)


--------------------
-- LJEL02 (在簡易型分行主管權限)--
--------------------
--(第四階段才開放)
--(套LBEL02)


--------------------
-- LJEL06 (在簡易型分行內部查核權限)--
--------------------
--(第四階段才開放)
--(套LBEL06)


--------------------
-- LAEL01 (在營運中心經辦權限)--
--------------------
--授信審查--
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL01', '3',319001 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL01', '3',329001 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL01', '3',329002 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL01', '3',329003 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL01', '3',339031 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL01', '3',339032 , 5, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL01', '3',339033 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL01', '3',339033 , 4, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL01', '3',339034 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL01', '3',339035 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL01', '3',339036 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL01', '3',339041 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL01', '3',339037 , 5, 'system', current timestamp);
--(授信異常通報)--
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL01', '3',339038 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL01', '3',329010 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL01', '3',339091 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL01', '3',339092 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL01', '3',339093 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL01', '3',329011 , 5, 'system', current timestamp);
--管理報表--
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL01', '3',315001 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL01', '3',325001 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL01', '3',325002 , 5, 'system', current timestamp);
----(關係戶往來彙總查詢)--
----INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL01', '3',325003 , 5, 'system', current timestamp);
--建檔維護--
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL01', '3',316001 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL01', '3',326001 , 5, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL01', '3',326004 , 5, 'system', current timestamp);


--------------------
-- LAEL02 (在營運中心主管權限)--
--------------------
--授信審查--
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL02', '3',319001 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL02', '3',329001 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL02', '3',329002 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL02', '3',329003 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL02', '3',339031 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL02', '3',339032 , 6, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL02', '3',339033 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL02', '3',339033 , 4, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL02', '3',339034 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL02', '3',339035 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL02', '3',339036 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL02', '3',339041 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL02', '3',339037 , 6, 'system', current timestamp);
--(授信異常通報)--
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL02', '3',339038 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL02', '3',329010 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL02', '3',339091 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL02', '3',339092 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL02', '3',339093 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL02', '3',329011 , 6, 'system', current timestamp);
--管理報表--
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL02', '3',315001 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL02', '3',325001 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL02', '3',325002 , 6, 'system', current timestamp);
--(關係戶往來彙總查詢)--
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL02', '3',325003 , 6, 'system', current timestamp);
--建檔維護--
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL02', '3',316001 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL02', '3',326999 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL02', '3',326001 , 6, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LAEL02', '3',326004 , 6, 'system', current timestamp);


--------------------
-- LAEL04 (在營運中心覆審權限)--
--------------------


--------------------
-- LSEL01 (在授管處經辦權限)--
--------------------
--授信審查--
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL01', '3',319001 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL01', '3',329001 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL01', '3',329002 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL01', '3',329003 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL01', '3',339051 , 5, 'system', current timestamp);
--(國內會簽)--
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL01', '3',339061 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL01', '3',339062 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL01', '3',339063 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL01', '3',339064 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL01', '3',339052 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL01', '3',339054 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL01', '3',339055 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL01', '3',339056 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL01', '3',339057 , 5, 'system', current timestamp);
--(授信異常通報)--
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL01', '3',339058 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL01', '3',329009 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL01', '3',339084 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL01', '3',329011 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL01', '3',329012 , 5, 'system', current timestamp);
--管理報表--
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL01', '3',315001 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL01', '3',325001 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL01', '3',325002 , 5, 'system', current timestamp);
--(關係戶往來彙總查詢)--
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL01', '3',325003 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL01', '3',325004 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL01', '3',335001 , 5, 'system', current timestamp);
--建檔維護--
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL01', '3',316001 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL01', '3',326001 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL01', '3',326002 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL01', '3',326003 , 5, 'system', current timestamp);


--------------------
-- LSEL02 (在授管處主管權限)--
--------------------
--授信審查--
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL02', '3',319001 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL02', '3',329001 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL02', '3',329002 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL02', '3',329003 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL02', '3',339051 , 6, 'system', current timestamp);
--(國內會簽)--
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL02', '3',339061 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL02', '3',339062 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL02', '3',339063 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL02', '3',339064 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL02', '3',339052 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL02', '3',339054 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL02', '3',339055 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL02', '3',339056 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL02', '3',339057 , 6, 'system', current timestamp);
--(授信異常通報)--
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL02', '3',339058 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL02', '3',329009 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL02', '3',339084 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL02', '3',329011 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL02', '3',329012 , 6, 'system', current timestamp);
--管理報表--
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL02', '3',315001 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL02', '3',325001 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL02', '3',325002 , 6, 'system', current timestamp);
--(關係戶往來彙總查詢)--
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL02', '3',325003 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL02', '3',325004 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL02', '3',335001 , 6, 'system', current timestamp);
--建檔維護--
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL02', '3',316001 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL02', '3',326999 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL02', '3',326001 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL02', '3',326002 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL02', '3',326003 , 6, 'system', current timestamp);


--------------------
-- LSEL03 (在授管處收件員權限)--
--------------------
--授信審查--
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL03', '3',319001 , 4, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL03', '3',329090 , 4, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LSEL03', '3',329091 , 4, 'system', current timestamp);


--------------------
-- LGEL01 (在稽核處經辦權限)--
--------------------
--稽核工作底稿--
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LGEL01', '3',314001 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LGEL01', '3',324004 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LGEL01', '3',334007 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LGEL01', '3',334008 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LGEL01', '3',334017 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LGEL01', '3',334019 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LGEL01', '3',324006 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LGEL01', '3',334012 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LGEL01', '3',334013 , 5, 'system', current timestamp);


--------------------
-- LGEL02 (在稽核處主管權限)--
--------------------
--稽核工作底稿--
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LGEL02', '3',314001 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LGEL02', '3',324004 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LGEL02', '3',334007 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LGEL02', '3',334008 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LGEL02', '3',334017 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LGEL02', '3',334019 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LGEL02', '3',324006 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LGEL02', '3',334012 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LGEL02', '3',334013 , 6, 'system', current timestamp);


--------------------
-- LOEL01 (在海外分行經辦權限)--
--------------------
--授信作業--
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL01', '3',311001 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL01', '3',321001 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL01', '3',321002 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL01', '3',321003 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL01', '3',331001 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL01', '3',331002 , 5, 'system', current timestamp);
--(呈授管處/營運中心)--
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL01', '3',331003 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL01', '3',331003 , 4, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL01', '3',331004 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL01', '3',331005 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL01', '3',331006 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL01', '3',331007 , 5, 'system', current timestamp);
--(授信異常通報)--
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL01', '3',331008 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL01', '3',321004 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL01', '3',331010 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL01', '3',331011 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL01', '3',331012 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL01', '3',321005 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL01', '3',321006 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL01', '3',331013 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL01', '3',331014 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL01', '3',331015 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL01', '3',321007 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL01', '3',321008 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL01', '3',331016 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL01', '3',331017 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL01', '3',331018 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL01', '3',321009 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL01', '3',331081 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL01', '3',331082 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL01', '3',331083 , 5, 'system', current timestamp);
--管理報表--
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL01', '3',315001 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL01', '3',325001 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL01', '3',325002 , 5, 'system', current timestamp);
--(關係戶往來彙總查詢)--
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL01', '3',325003 , 5, 'system', current timestamp);
--建檔維護--
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL01', '3',316001 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL01', '3',326997 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL01', '3',326003 , 5, 'system', current timestamp);


--------------------
-- LOEL02 (在海外分行主管權限)--
--------------------
--授信作業--
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL02', '3',311001 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL02', '3',321001 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL02', '3',321002 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL02', '3',321003 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL02', '3',331001 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL02', '3',331002 , 6, 'system', current timestamp);
--(呈授管處/營運中心)--
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL02', '3',331003 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL02', '3',331003 , 4, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL02', '3',331004 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL02', '3',331005 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL02', '3',331006 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL02', '3',331007 , 6, 'system', current timestamp);
--(授信異常通報)--
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL02', '3',331008 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL02', '3',321004 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL02', '3',331010 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL02', '3',331011 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL02', '3',331012 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL02', '3',321005 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL02', '3',321006 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL02', '3',331013 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL02', '3',331014 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL02', '3',331015 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL02', '3',321007 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL02', '3',321008 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL02', '3',331016 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL02', '3',331017 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL02', '3',331018 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL02', '3',321009 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL02', '3',331081 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL02', '3',331082 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL02', '3',331083 , 6, 'system', current timestamp);
--管理報表--
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL02', '3',315001 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL02', '3',325001 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL02', '3',325002 , 6, 'system', current timestamp);
--(關係戶往來彙總查詢)--
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL02', '3',325003 , 6, 'system', current timestamp);
--建檔維護--
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL02', '3',316001 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL02', '3',326997 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL02', '3',326999 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL02', '3',326003 , 6, 'system', current timestamp);


--------------------
-- LOEL06 (在海外分行內部查核權限)--
--------------------
--(套LBEL06)
--稽核工作底稿--
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL06', '3',314001 , 7, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL06', '3',324005 , 7, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL06', '3',334009 , 7, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL06', '3',334010 , 7, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL06', '3',334011 , 7, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL06', '3',334018 , 7, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL06', '3',324006 , 7, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL06', '3',334012 , 7, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LOEL06', '3',334013 , 7, 'system', current timestamp);



--####################--
--##特殊分行角色設定##--
--####################--

--------------------
-- LFEL01 (在國外部/國金部/金控總部分行經辦權限)----
-- LKEL01 (在國外部/國金部/金控總部分行經辦權限)----
--------------------
--企金授信--
  INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL01', '3',317001 , 5, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL01', '3',327001 , 5, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL01', '3',327002 , 5, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL01', '3',327003 , 5, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL01', '3',337001 , 5, 'system', current timestamp);
--(國內會簽)--
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL01', '3',337021 , 5, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL01', '3',337022 , 5, 'system', current timestamp);
--(國內提會)--
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL01', '3',337023 , 5, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL01', '3',337024 , 5, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL01', '3',337025 , 5, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL01', '3',337002 , 5, 'system', current timestamp);
--(呈授管處/營運中心)--
----INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL01', '3',337003 , 5, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL01', '3',337003 , 4, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL01', '3',337004 , 5, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL01', '3',337005 , 5, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL01', '3',337006 , 5, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL01', '3',337007 , 5, 'system', current timestamp);
--(授信異常通報)--
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL01', '3',337008 , 5, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL01', '3',327004 , 5, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL01', '3',337010 , 5, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL01', '3',337011 , 5, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL01', '3',337012 , 5, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL01', '3',327005 , 5, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL01', '3',327006 , 5, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL01', '3',337013 , 5, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL01', '3',337014 , 5, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL01', '3',337015 , 5, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL01', '3',327007 , 5, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL01', '3',327008 , 5, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL01', '3',337016 , 5, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL01', '3',337017 , 5, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL01', '3',337018 , 5, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL01', '3',327009 , 5, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL01', '3',337081 , 5, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL01', '3',337082 , 5, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL01', '3',337083 , 5, 'system', current timestamp);
  INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL01', '3',327010 , 5, 'system', current timestamp);
  INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL01', '3',337091 , 5, 'system', current timestamp);
  INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL01', '3',337092 , 5, 'system', current timestamp);
  INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL01', '3',337093 , 5, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL01', '3',327011 , 5, 'system', current timestamp);


--------------------
-- LFEL02 (在國外部/國金部/金控總部分行主管權限)----
-- LKEL02 (在國外部/國金部/金控總部分行主管權限)----
--------------------
--企金授信--
  INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL02', '3',317001 , 6, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL02', '3',327001 , 6, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL02', '3',327002 , 6, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL02', '3',327003 , 6, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL02', '3',337001 , 6, 'system', current timestamp);
--(國內會簽)--
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL02', '3',337021 , 6, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL02', '3',337022 , 6, 'system', current timestamp);
--(國內提會)--
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL02', '3',337023 , 6, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL02', '3',337024 , 6, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL02', '3',337025 , 6, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL02', '3',337002 , 6, 'system', current timestamp);
--(呈授管處/營運中心)--
----INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL02', '3',337003 , 6, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL02', '3',337003 , 4, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL02', '3',337004 , 6, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL02', '3',337005 , 6, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL02', '3',337006 , 6, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL02', '3',337007 , 6, 'system', current timestamp);
--(授信異常通報)--
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL02', '3',337008 , 6, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL02', '3',327004 , 6, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL02', '3',337010 , 6, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL02', '3',337011 , 6, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL02', '3',337012 , 6, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL02', '3',327005 , 6, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL02', '3',327006 , 6, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL02', '3',337013 , 6, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL02', '3',337014 , 6, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL02', '3',337015 , 6, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL02', '3',327007 , 6, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL02', '3',327008 , 6, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL02', '3',337016 , 6, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL02', '3',337017 , 6, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL02', '3',337018 , 6, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL02', '3',327009 , 6, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL02', '3',337081 , 6, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL02', '3',337082 , 6, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL02', '3',337083 , 6, 'system', current timestamp);
  INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL02', '3',327010 , 6, 'system', current timestamp);
  INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL02', '3',337091 , 6, 'system', current timestamp);
  INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL02', '3',337092 , 6, 'system', current timestamp);
  INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL02', '3',337093 , 6, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LKEL02', '3',327011 , 6, 'system', current timestamp);

--------------------
-- LFEL06 (在國外部/國金部/金控總部分行內部查核權限)----
-- LKEL06 (在國外部/國金部/金控總部分行內部查核權限)----
--------------------
--(第四階段才開放)
--(套LBEL06)


--------------------
-- LPEL01 (在海外分行(有總行)經辦權限)--
--------------------
--授信作業--
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL01', '3',311001 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL01', '3',321001 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL01', '3',321002 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL01', '3',321003 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL01', '3',331001 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL01', '3',331002 , 5, 'system', current timestamp);
--(呈總行)--
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL01', '3',331021 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL01', '3',331021 , 4, 'system', current timestamp);

--(提會待登錄)--
--(廢除)
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME, PGMDEPT) values ('LPEL01', '3',331025 , 5, 'system', current timestamp, 'Z03');
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME, PGMDEPT) values ('LPEL01', '3',331026 , 4, 'system', current timestamp, 'Z03');

--(呈授管處/營運中心)--
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL01', '3',331003 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL01', '3',331003 , 4, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL01', '3',331004 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL01', '3',331005 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL01', '3',331006 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL01', '3',331007 , 5, 'system', current timestamp);
--(授信異常通報)--
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL01', '3',331008 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL01', '3',321004 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL01', '3',331010 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL01', '3',331011 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL01', '3',331012 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL01', '3',321005 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL01', '3',321006 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL01', '3',331013 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL01', '3',331014 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL01', '3',331015 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL01', '3',321007 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL01', '3',321008 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL01', '3',331016 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL01', '3',331017 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL01', '3',331018 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL01', '3',321009 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL01', '3',331081 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL01', '3',331082 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL01', '3',331083 , 5, 'system', current timestamp);
--管理報表--
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL01', '3',315001 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL01', '3',325001 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL01', '3',325002 , 5, 'system', current timestamp);
--(關係戶往來彙總查詢)--
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL01', '3',325003 , 5, 'system', current timestamp);
--建檔維護--
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL01', '3',316001 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL01', '3',326997 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL01', '3',326003 , 5, 'system', current timestamp);


--------------------
-- LPEL02 (在海外分行(有總行)主管權限)--
--------------------
--授信作業--
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL02', '3',311001 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL02', '3',321001 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL02', '3',321002 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL02', '3',321003 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL02', '3',331001 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL02', '3',331002 , 6, 'system', current timestamp);
--(呈總行)--
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL02', '3',331021 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL02', '3',331021 , 4, 'system', current timestamp);

--(提會待登錄)--
--(廢除)
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME, PGMDEPT) values ('LPEL02', '3',331025 , 4, 'system', current timestamp, 'Z03');
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME, PGMDEPT) values ('LPEL02', '3',331026 , 6, 'system', current timestamp, 'Z03');

--(呈授管處/營運中心)--
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL02', '3',331003 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL02', '3',331003 , 4, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL02', '3',331004 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL02', '3',331005 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL02', '3',331006 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL02', '3',331007 , 6, 'system', current timestamp);
--(授信異常通報)--
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL02', '3',331008 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL02', '3',321004 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL02', '3',331010 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL02', '3',331011 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL02', '3',331012 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL02', '3',321005 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL02', '3',321006 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL02', '3',331013 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL02', '3',331014 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL02', '3',331015 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL02', '3',321007 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL02', '3',321008 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL02', '3',331016 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL02', '3',331017 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL02', '3',331018 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL02', '3',321009 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL02', '3',331081 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL02', '3',331082 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL02', '3',331083 , 6, 'system', current timestamp);
--管理報表--
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL02', '3',315001 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL02', '3',325001 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL02', '3',325002 , 6, 'system', current timestamp);
--(關係戶往來彙總查詢)--
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL02', '3',325003 , 6, 'system', current timestamp);
--建檔維護--
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL02', '3',316001 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL02', '3',326997 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL02', '3',326999 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LPEL02', '3',326003 , 6, 'system', current timestamp);


--------------------
-- LPEL06 (在海外分行(有總行)內部查核權限)--
--------------------
--(套LBEL06)


--------------------
-- LQEL01 (在海外總行(澳洲/加拿大)經辦權限)--
--------------------
--授信作業--
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL01', '3',311001 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL01', '3',321001 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL01', '3',321002 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL01', '3',321003 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL01', '3',331001 , 5, 'system', current timestamp);
--總行應無待覆核項目
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL01', '3',331002 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL01', '3',331022 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL01', '3',331023 , 5, 'system', current timestamp);
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL01', '3',331024 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL01', '3',331024 , 4, 'system', current timestamp);
--(呈授管處/營運中心)--
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL01', '3',331003 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL01', '3',331003 , 4, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL01', '3',331004 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL01', '3',331005 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL01', '3',331006 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL01', '3',331007 , 5, 'system', current timestamp);
--(授信異常通報)--
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL01', '3',331008 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL01', '3',321004 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL01', '3',331010 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL01', '3',331011 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL01', '3',331012 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL01', '3',321005 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL01', '3',321006 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL01', '3',331013 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL01', '3',331014 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL01', '3',331015 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL01', '3',321007 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL01', '3',321008 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL01', '3',331016 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL01', '3',331017 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL01', '3',331018 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL01', '3',321009 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL01', '3',331081 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL01', '3',331082 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL01', '3',331083 , 5, 'system', current timestamp);
--管理報表--
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL01', '3',315001 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL01', '3',325001 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL01', '3',325002 , 5, 'system', current timestamp);
--(關係戶往來彙總查詢)--
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL01', '3',325003 , 5, 'system', current timestamp);
--建檔維護--
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL01', '3',316001 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL01', '3',326997 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL01', '3',326003 , 5, 'system', current timestamp);


--------------------
-- LQEL02 (在海外總行(澳洲/加拿大)主管權限)--
--------------------
--授信作業--
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL02', '3',311001 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL02', '3',321001 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL02', '3',321002 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL02', '3',321003 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL02', '3',331001 , 6, 'system', current timestamp);
--總行應無待覆核項目
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL02', '3',331002 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL02', '3',331022 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL02', '3',331023 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL02', '3',331024 , 6, 'system', current timestamp);
--(呈授管處/營運中心)--
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL02', '3',331003 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL02', '3',331003 , 4, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL02', '3',331004 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL02', '3',331005 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL02', '3',331006 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL02', '3',331007 , 6, 'system', current timestamp);
--(授信異常通報)--
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL02', '3',331008 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL02', '3',321004 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL02', '3',331010 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL02', '3',331011 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL02', '3',331012 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL02', '3',321005 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL02', '3',321006 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL02', '3',331013 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL02', '3',331014 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL02', '3',331015 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL02', '3',321007 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL02', '3',321008 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL02', '3',331016 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL02', '3',331017 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL02', '3',331018 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL02', '3',321009 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL02', '3',331081 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL02', '3',331082 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL02', '3',331083 , 6, 'system', current timestamp);
--管理報表--
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL02', '3',315001 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL02', '3',325001 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL02', '3',325002 , 6, 'system', current timestamp);
--(關係戶往來彙總查詢)--
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL02', '3',325003 , 6, 'system', current timestamp);
--建檔維護--
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL02', '3',316001 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL02', '3',326997 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL02', '3',326999 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LQEL02', '3',326003 , 6, 'system', current timestamp);


--------------------
-- LQEL06 (在海外總行(澳洲/加拿大)內部查核權限)--
--------------------
--(套LBEL06)


--------------------
-- LREL01 (在海外總行(泰國)經辦權限)--
--------------------
--授信作業--
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL01', '3',311001 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL01', '3',321001 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL01', '3',321002 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL01', '3',321003 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL01', '3',331001 , 5, 'system', current timestamp);
--總行應無待覆核項目
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL01', '3',331002 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL01', '3',331022 , 5, 'system', current timestamp);
--(呈授管處/營運中心)--
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL01', '3',331003 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL01', '3',331003 , 4, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL01', '3',331027 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL01', '3',331028 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL01', '3',331004 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL01', '3',331005 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL01', '3',331006 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL01', '3',331007 , 5, 'system', current timestamp);
--(授信異常通報)--
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL01', '3',331008 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL01', '3',321004 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL01', '3',331010 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL01', '3',331011 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL01', '3',331012 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL01', '3',321005 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL01', '3',321006 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL01', '3',331013 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL01', '3',331014 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL01', '3',331015 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL01', '3',321007 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL01', '3',321008 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL01', '3',331016 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL01', '3',331017 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL01', '3',331018 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL01', '3',321009 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL01', '3',331081 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL01', '3',331082 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL01', '3',331083 , 5, 'system', current timestamp);
--管理報表--
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL01', '3',315001 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL01', '3',325001 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL01', '3',325002 , 5, 'system', current timestamp);
--(關係戶往來彙總查詢)--
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL01', '3',325003 , 5, 'system', current timestamp);
--建檔維護--
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL01', '3',316001 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL01', '3',326997 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL01', '3',326003 , 5, 'system', current timestamp);


--------------------
-- LREL02 (在海外總行(泰國)主管權限)--
--------------------
--授信作業--
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL02', '3',311001 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL02', '3',321001 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL02', '3',321002 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL02', '3',321003 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL02', '3',331001 , 6, 'system', current timestamp);
--總行應無待覆核項目
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL02', '3',331002 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL02', '3',331022 , 6, 'system', current timestamp);
--(呈授管處/營運中心)--
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL02', '3',331003 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL02', '3',331003 , 4, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL02', '3',331027 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL02', '3',331028 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL02', '3',331004 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL02', '3',331005 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL02', '3',331006 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL02', '3',331007 , 6, 'system', current timestamp);
--(授信異常通報)--
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL02', '3',331008 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL02', '3',321004 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL02', '3',331010 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL02', '3',331011 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL02', '3',331012 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL02', '3',321005 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL02', '3',321006 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL02', '3',331013 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL02', '3',331014 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL02', '3',331015 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL02', '3',321007 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL02', '3',321008 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL02', '3',331016 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL02', '3',331017 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL02', '3',331018 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL02', '3',321009 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL02', '3',331081 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL02', '3',331082 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL02', '3',331083 , 6, 'system', current timestamp);
--管理報表--
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL02', '3',315001 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL02', '3',325001 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL02', '3',325002 , 6, 'system', current timestamp);
--(關係戶往來彙總查詢)--
--INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL02', '3',325003 , 6, 'system', current timestamp);
--建檔維護--
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL02', '3',316001 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL02', '3',326997 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL02', '3',326999 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LREL02', '3',326003 , 6, 'system', current timestamp);


--------------------
-- LREL06 (在海外總行(泰國)內部查核權限)--
--------------------
--(套LBEL06)



--######################--
--##跨單位登入角色設定##--
--######################--

--------------------
-- LBEL01A (營運中心跨單位登入轄下分行經辦權限)--
--------------------
--(第四階段才開放)


--------------------
-- LBEL02A (營運中心跨單位登入轄下分行主管權限)--
--------------------
--(第四階段才開放)


--------------------
-- LSEL01A (營運中心跨單位登入授管處經辦權限)--
--------------------
--(第四階段才開放)


--------------------
-- LSEL02A (營運中心跨單位登入授管處主管權限)--
--------------------
--(第四階段才開放)


--------------------
-- LBEL01G (稽核處跨單位登入分行經辦權限)--
--------------------
--稽核工作底稿--
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LBEL01G', '3',314001 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LBEL01G', '3',324001 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LBEL01G', '3',334001 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LBEL01G', '3',334002 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LBEL01G', '3',334014 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LBEL01G', '3',324002 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LBEL01G', '3',334003 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LBEL01G', '3',334004 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LBEL01G', '3',334015 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LBEL01G', '3',324003 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LBEL01G', '3',334005 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LBEL01G', '3',334006 , 5, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LBEL01G', '3',334016 , 5, 'system', current timestamp);


--------------------
-- LBEL02G (稽核處跨單位登入分行主管權限)--
--------------------
--稽核工作底稿--
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LBEL02G', '3',314001 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LBEL02G', '3',324001 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LBEL02G', '3',334001 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LBEL02G', '3',334002 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LBEL02G', '3',334014 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LBEL02G', '3',324002 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LBEL02G', '3',334003 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LBEL02G', '3',334004 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LBEL02G', '3',334015 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LBEL02G', '3',324003 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LBEL02G', '3',334005 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LBEL02G', '3',334006 , 6, 'system', current timestamp);
INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, UPDATER, UPDTIME) values ('LBEL02G', '3',334016 , 6, 'system', current timestamp);

