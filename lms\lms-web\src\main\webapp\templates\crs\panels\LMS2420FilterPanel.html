<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="panelFragmentBody">
            <div id="filterBox" style="display:none">
                <form id="filterForm">
                    <div class="" id="">
                        <table class="tb2" border="0" cellpadding="0" cellspacing="0" width="100%">
                            <tbody>
                                <tr>
                                    <td style="text-align: right;width:30%;" class="hd1">
										<th:block th:text="#{'C242M01A.schTime'}">預約產生日期</th:block>&nbsp;&nbsp;
                                    </td>
                                    <td>
                                    	<input type="text" size="8" maxlength="10" class="date required " _requiredLength="10" id="schTimeBeg" name="schTimeBeg" />
										~
										<input type="text" size="8" maxlength="10" class="date required " _requiredLength="10" id="schTimeEnd" name="schTimeEnd" />
										<span class="text-red">(YYYY-MM-DD)</span>
                                    </td>
                                </tr>                                
                            </tbody>
                        </table>
                    </div>
                </form>
            </div>
            <script>
                var FilterAction = {
                    formId: "#filterForm",
                    gridId: "#gridview",
                    openBox: function(){
                        var $form = $(this.formId).reset();
                        
                        $("#filterBox").thickbox({
                            //query=查詢
                            title: i18n.def["query"],
                            width: 550,
                            height: 180,
                            modal: true,
                            i18n: i18n.def,
                            readOnly: false,
                            align: "center",
                            valign: "bottom",
                            buttons: {
                                "sure": function(){
                                    if (!$form.valid()) {
                                        return false;
                                    }
                                    FilterAction.reloadGrid(JSON.stringify($form.serializeData()));
                                    $.thickbox.close();
                                },
                                "cancel": function(){
                                    $.thickbox.close();
                                }
                            }
                        });
                    },
                    /**更新grid
                     *
                     * @param {Object} data 查詢條件
                     */
                    reloadGrid: function(data){
                        $(this.gridId).jqGrid("setGridParam", {
                            postData: {
                                formAction: "queryMain",
                                docStatus: viewstatus,
                                filetData: data
                            },
                            page: 1,
                            search: true
                        }).trigger("reloadGrid");
                    }
                };
            </script>
        </th:block>
    </body>
</html>
