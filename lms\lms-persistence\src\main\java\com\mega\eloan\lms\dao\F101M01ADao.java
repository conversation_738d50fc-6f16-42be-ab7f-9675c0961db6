/* 
 * F101M01ADao.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.F101M01A;

/**
 * <pre>
 * F101M01A dao interface.
 * </pre>
 * 
 * @since 2011/7/26
 * <AUTHOR> Wang
 * @version <ul>
 *          <li>2011/7/26,Sunkist Wang,new</li>
 *          <li>2011/8/24,Sunkist Wang,add 給信評引進的 findForOriginalMow()介面。
 *          <li>2011/8/25,Sunkist Wang,update for getF101M01AbyMainId介面。
 *          </ul>
 */
public interface F101M01ADao extends IGenericDao<F101M01A> {

//	/**
//	 * 信評引進財報資料取得財報可選擇清單資料
//	 * 
//	 * <ol>
//	 * <li>報表類型：年報。
//	 * <li>表單狀態：已確認。
//	 * <li>合併報表：否。
//	 * <li>財報資料來源：
//	 * <ul>
//	 * <li>
//	 * 1：會計師查核報告書
//	 * <li>2：會計師核閱報告書
//	 * <li>3：會計師稅務簽證
//	 * <li>5：營利事業所得稅結算申報書。
//	 * </ul>
//	 * </ol>
//	 * 
//	 * @param custId
//	 *            統一編號
//	 * @param dupNo
//	 *            重覆序號
//	 * @return List<F101M01A> 數筆符合的財報清單
//	 */
//	List<F101M01A> findForOriginalMow(String custId, String dupNo);

	/**
	 * get fss meta by mainId
	 * 
	 * @param mainId
	 *            mainId
	 * @return F101M01A
	 */
	F101M01A getF101M01AbyMainId(String mainId);

	/**
	 * 檢查是否有相同共用規則的鍵值之財報主檔。
	 * 
	 * @param custId
	 *            統一編號
	 * @param dupNo
	 *            重覆序號
	 * @param conso
	 *            合併/非合併
	 * @param publicFlag
	 *            公開/非公開
	 * @param year
	 *            年度
	 * @param periodType
	 *            報表類別
	 * @param mainId
	 *            meta's mainId
	 * @param docstatus
	 *            狀態
	 * @param type
	 *            一般/預估
	 * @return F101M01A
	 */
	F101M01A findDuplicateMeta(String custId, String dupNo, String conso,
			String publicFlag, String year, String periodType, String mainId,
			String docstatus, String type);

	/**
	 * 複製財報(call ces.copyF101)
	 * 
	 * @param mainId
	 *            文件編號
	 * @param docStatus
	 *            文件狀態
	 * @param randomCode
	 *            報表亂碼
	 * @param branchNo
	 *            分行代號
	 * @param userId
	 *            行員代碼
	 * @param newMianId
	 *            新的文件編號(mainId)
	 */
	void copyDocument(String mainId, String docStatus, String randomCode,
			String branchNo, String userId, String newMianId);
	
	/**
	 * 排序MainId,依照EDate,由大到小排序
	 * 
	 * @param mainIds
	 *            mainIds
	 * @return list
	 */
	List<String> getSortMainId(String[] mainIds);	
}
