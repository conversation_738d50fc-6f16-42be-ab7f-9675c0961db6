<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <body>
      <th:block th:fragment="panelFragmentBody">
      <script type="text/javascript">
      	  loadScript('pagejs/lns/LMS1601S02Panel');
      </script>
	  <!-- <script type="text/javascript" src="pagejs/lns/LMS1601S02Panel.js?r=20220302"></script> -->
            <div class="content">
                <form id="L160M01AForm" name="L160M01AForm">
                    <table width="100%" border="0">
                        <tr>
                            <td width="21%" class="tit1">
                                1、<span class="text-red">＊</span>
								<th:block th:text="#{'L160M01A.tType'}">授信契約書</th:block><th:block th:text="#{'L160M01A.guFromDate'}">期間</th:block>：<br>
								&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                <select id="tType" name="tType"  style="width: 160px;"  >
                                    <option value="1"><th:block th:text="#{'L160M01A.tType1'}">短期</th:block></option>
                                    <option value="2"><th:block th:text="#{'L160M01A.tType2'}">中長期</th:block></option>
									<!--J-110-0540_05097_B1001 Web e-Loan企金授信配合調整E-loan系統動用審核表部分內容-->
									<option value="3"><th:block th:text="#{'L160M01A.tType3'}">詳額度動用資訊一覽表</th:block></option>
                                </select>
                                
                            </td>
                            <td width="79%">
                            	<div id="show_not_tType3">
	                                <span class="readOnlyhide"><th:block th:text="#{'L160M01A.useDate'}">動用期限</th:block>：
	                                    <select id="useSelect" name="useSelect">
	                                        <option value="1">YYYY-MM-DD~YYYY-MM-DD</option>
	                                        <option value="2"><th:block th:text="#{'L160M01A.useMonth'}">自首動日起XX個月</th:block></option>
	                                        <option value="3"><th:block th:text="#{'L160M01A.useOther'}">其他</th:block></option>
	                                    </select>
	                                </span>
	                                <span class="text-red">＊</span>
	                                <th:block th:text="#{'L160M01A.useFromDate'}">動用期間</th:block>：<span id="the1" class="the">
	                                    <input id="useFromDate" name="useFromDate" type="text" size="11" maxlength="10" class="date required"/>
	                                    ~
	                                    <input id="useEndDate" name="useEndDate" type="text" size="11" maxlength="10" class="date required" />
	                                </span>
	                                <span id="the2" class="the" style="display:none"><th:block th:text="#{'L160M01A.useMonth1'}">自首動日起</th:block>
	                                    <input id="useMonth" name="useMonth" type="text" size="2" maxlength="2" class="number required" />
	                                    <th:block th:text="#{'L160M01A.useMonth2'}">個月</th:block>
	                                </span>
	                                <span id="the3" class="the" style="display:none"><th:block th:text="#{'L160M01A.useOther'}">其他</th:block>
	                                    <textarea id="useOther" name="useOther"  maxlength="900" maxlengthC="300" class="required"></textarea>
	                                </span>
	                                <br/>
	                                <span id="long" style="display:none"><span class="readOnlyhide"><th:block th:text="#{'L160M01A.lnDate'}">授信期限</th:block>：
	                                        <select id="lnSelect" name="lnSelect">
	                                            <option value="1">YYYY-MM-DD~YYYY-MM-DD</option>
	                                            <option value="2"><th:block th:text="#{'L160M01A.lnFromDate'}">自首動日起XX年XX個月</th:block></option>
	                                            <option value="3"><th:block th:text="#{'L160M01A.useOther'}">其他</th:block></option>
	                                        </select>
	                                    </span><span class="text-red">＊</span><th:block th:text="#{'L160M01A.lnEndDate'}">授信期間</th:block>：<span id="two1" class="two">
	                                        <input id="lnFromDate" name="lnFromDate" type="text" size="11" maxlength="11" class="date required"/>
	                                        ~
	                                        <input id="lnEndDate" name="lnEndDate" type="text" size="11" maxlength="10" class="date required" />
	                                    </span>
	                                    <span id="two2" class="two" style="display:none"><th:block th:text="#{'L160M01A.useMonth1'}">自首動日起</th:block>
	                                        <input id="lnYear" name="lnYear" type="text" size="2" maxlength="2" class="number required"/>
	                                        <th:block th:text="#{'L160M01A.lnYear'}">年</th:block>
	                                        <input id="lnMonth" name="lnMonth" type="text" size="2" class="number required" maxlength="2">
	                                        <th:block th:text="#{'L160M01A.useMonth2'}">個月</th:block>
	                                    </span>
	                                    <span id="two3" class="two" style="display:none"><th:block th:text="#{'L160M01A.useOther'}">其他</th:block>
	                                        <textarea id="lnOther" name="lnOther" maxlength="900" maxlengthC="300" class="required" ></textarea>
	                                    </span>
	                                </span>
								</div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                &nbsp;
                            </td>
                            <td>
                            </td>
                        </tr>
                        <tr style="height:10px">
                        </tr>
                        <tr>
                            <td class="tit1">
                                2、<th:block th:text="#{'L160M01A.signDate1'}">授信約定書簽約日期</th:block>：
                            </td>
                            <td>
                                <input id="signDate" name="signDate" type="text" size="12" maxlength="12" class="date" />
                                <span class="text-red">(<th:block th:text="#{'L160M01A.signDate2'}">(3至5年)</th:block>)</span>
                            </td>
                        </tr>
                        <tr style="height:15px">
                        </tr>
                        <tr>
                            <td class="tit1">
                                3、<th:block th:text="#{'L160M01A.guCurr'}">連帶保證書最高本金</th:block>：
                            </td>
                            <td>
                                <select id="guCurr" name="guCurr" combokey="Common_Currcy" space="true"/>
                                <input id="guAmt" name="guAmt" type="text" size="15" maxlength="15" class="number" />
                                <th:block th:text="#{'L160M01A.guAmt'}">元整</th:block>
                                <th:block th:text="#{'L160M01A.guFromDate'}">期間</th:block>：
                                <input id="guFromDate" name="guFromDate" type="text" size="12" maxlength="12" class="date"/>
                                ~
                                <input id="guEndDate" name="guEndDate" type="text" size="12" maxlength="12" class="date"/>
                                <span class="text-red">(<th:block th:text="#{'L160M01A.guEndDate'}">最長5年</th:block>)</span>
                            </td>
                        </tr>
                    </table>
                    <br/>
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <span class="ps1">※<th:block th:text="#{'L160M01A.message4'}">以下欄位請一定要輸入，若案件轉入催收系統時可依此計算違約金，不列印報表上</th:block></span>
                    <div style="margin-left:30px; ">
                        <table width="90%" border="1" cellspacing="0" rules="none">
                            <tr>
                                <td width="20%" class="tit1">
                                    &nbsp;&nbsp;&nbsp;<span class="text-red">＊</span>
                                    <th:block th:text="#{'L160M01A.dMonth1'}">違約金計算條件</th:block>：
                                </td>
                                <td width="80%">
                                    <th:block th:text="#{'L160M01A.message5'}">借戶如延遲還本或付息時，本金字到期日起，利息自繳息日起，逾期在</th:block>
                                    <input id="dMonth1" name="dMonth1" type="text" size="2" maxlength="2" value="6" class="required number" />
                                    <th:block th:text="#{'L160M01A.message6'}">個月以內部份</th:block>，
                                    <br/>
                                    <th:block th:text="#{'L160M01A.message7'}">按約定利率百分之</th:block>
                                    <input id="dRate1" name="dRate1" type="text" size="2" maxlength="2" value="10" class="required number" />
                                    ，<th:block th:text="#{'L160M01A.dMonth2'}">逾期超過</th:block>
                                    <input id="dMonth2" name="dMonth2" type="text" size="2" maxlength="2" value="6" class="required number" />
                                    <th:block th:text="#{'L160M01A.message8'}">個月部份，按約定利率百分之</th:block>
                                    <input id="dRate2" name="dRate2" type="text" size="2" maxlength="2" value="20" class="required number" />
                                    <th:block th:text="#{'L160M01A.dRate2'}">計違約金</th:block>。
                                    <br/>
                                </td>
                            </tr>
                            <tr>
                                <td class="tit1">
                                    <span class="text-red">＊</span>
                                    <th:block th:text="#{'L160M01A.dRateAdd'}">計收延遲利息加碼</th:block>：
                                </td>
                                <td>
                                    <th:block th:text="#{'L160M01A.dRateAdd'}">計收延遲利息加碼</th:block>
                                    <input id="dRateAdd" name="dRateAdd" type="text" size="4" maxlength="4" value="1" class="required number"/>
                                    %
                                </td>
                            </tr>
                        </table>
                    </div>
                    <br/>
                    <table width="100%" border="2" cellspacing="0" bordercolor="#000000" rules="rows">
                        <tr>
                            <td width="13%" class="tit1">
                                4、<th:block th:text="#{'L160M01A.blackDataDate'}">查核事項</th:block>
                            </td>
                            <td width="43%">
                            </td>
                            <td width="44%" align="right">
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <th:block th:text="#{'L160M01A.blackListTxtErr'}">黑名單查詢</th:block>
                                <br/>
                                <button id="selecttheblack" type="button" disabled>
                                    <span class="text-only"><th:block th:text="#{'L160M01A.bt02'}">查詢</th:block></span>
                                </button>
                            </td>
                            <td colspan="2">
                                <th:block th:text="#{'L160M01A.queryDataDate'}">資料查詢日期</th:block>：<span id="blackDataDate" class="field"></span>
                                <br/>
                                <textarea id="blackListTxtOK" name="blackListTxtOK" class="txt_mult" rows="4" cols="100" readonly= "readonly"></textarea>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <th:block th:text="#{'L160M01A.JoinMarketing'}">共同行銷</th:block>
                                <br/>
                                <button id="queryJoinMarketing" type="button">
                                    <span class="text-only"><th:block th:text="#{'L160M01A.bt02'}">查詢</th:block></span>
                                </button>
                            </td>
                            <td colspan="2">
                                <th:block th:text="#{'L160M01A.queryDataDate'}">資料查詢日期</th:block>：<span id="joinMarketingDate" class="field"></span>
                                <br/>
                                <textarea id="joinMarketing" name="joinMarketing" class="txt_mult" rows="4" cols="100" readonly= "readonly"></textarea>
                                <span class="text-red"><th:block th:text="#{'L160M01A.message60'}">注意資料來源為0024交易維護內容，如有疑問請先用0024交易維護再重新引入。</th:block></span>
                            </td>
                        </tr>
						<th:block th:if="${show_allocateFundList_Msg}">
						<tr>
							<td colspan="3">
								<p class="text-red" style="margin:5px 10px;">
									<th:block th:text="#{'AllocateFundsCheckList.text.precautions'}">
                                	「央行管制」購屋(或購地)案件撥款當日檢核表：請注意應於首次動撥日當日查詢聯徵中心B29及B33項目，
									並當日將動審表送呈覆核完成，Aloan當日方能撥款，上述作業皆需於同一日完成。
									</th:block>
									<a href="/lms-web/img/lms/AllocateFundsCheckList.xlsx" target="_blank" style="font-weight: bold"><th:block th:text="#{'AllocateFundsCheckList.file.download.excel'}">購屋購地央管案件撥款當日檢核表</th:block></a>
                                </p>
                            </td>
						</tr>
						</th:block>
                    </table>
                    <br/>
                    <table width="100%" border="2" cellspacing="0" bordercolor="#000000">
                        <tr>
                            <td colspan="6" class="tit1">
                                5、<th:block th:text="#{'L160M01A.otherCase'}">其他事項</th:block>：<th:block th:text="#{'L160M01A.message9'}">已收到(已完成)者請勾選「V」，未收到(未完成)者請勾選「X」，免附者請勾「免」</th:block>。
                                <br/>
								<span id="smeMateInfoText"> 
									<th:block th:text="#{'L160M01A.message114'}">※信保案件負責人客戶基本資料檔是否已建有配偶資料：</th:block>
								</span>
								<select name="smeMateInfo" id="smeMateInfo" class="required" disabled="true"><!--class="required"-->
										<option value=""></option>
										<option value="0">NA</option>
										<option value="1"><th:block th:text="#{'COMMON.CON1'}">有</th:block></option>
										<option value="2"><th:block th:text="#{'COMMON.CON2'}">無</th:block></option>
								</select> 
								
								<button id="reloadSmeMateInfo" type="button">
									<span class="text-only"><th:block th:text="#{'button.pullinAgain'}">重新引進</th:block></span>
								</button>
								<br/>
								<p id="NoSmeMateInfoText" style="font-weight:bold;color:red;"></p><br/>
                                <button id="allCast" type="button">
                                    <span class="text-only"><th:block th:text="#{'L160M01A.bt03'}">全部完成/收到</th:block></span>
                                </button>
                                <button id="allCancel"type="button">
                                    <span class="text-only"><th:block th:text="#{'L160M01A.bt04'}">全部免附</th:block></span>
                                </button>
								<button id="reloadAll"type="button">
                                    <span class="text-only"><th:block th:text="#{'button.pullinAgain'}">重新引進</th:block></span>
                                </button>
								<button type="button" id="allDetails" >
									<span class="text-only"><th:block th:text="#{'button.allDetails'}">產生資料清冊</th:block></span>
								</button>
								<!--TODO 引進
								<button type="button" id="importRPA" >
									<span class="text-only"><th:block th:text="#{'button.importRPA'}">引入RPA查詢結果</th:block></span>
								</button>
								-->
                            </td>
                        </tr>
                        <tr id="allBranch">
                            <td colspan="6">
                                <b><th:block th:text="#{'L160M01A.allItem'}">全行共同項目</th:block>:</b>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="6">
                                <b><th:block th:text="#{'L160M01A.selfItem'}">自行輸入項目</th:block>:</b>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <select id="itemCheck1" name="itemCheck1">
                                    <option value="0"><th:block th:text="#{'L160M01A.noNeed'}">免</th:block></option>
                                    <option value="1">Ｖ</option>
                                    <option value="2" selected="selected">Ｘ</option>
                                </select>
                            </td>
                            <td>
                                <textarea id="itemContent1" name="itemContent1" class="txt_mult" maxlengthC="300" rows="5" cols="30"></textarea>
                            </td>
                            <td>
                                <select id="itemCheck2" name="itemCheck2">
                                    <option value="0"><th:block th:text="#{'L160M01A.noNeed'}">免</th:block></option>
                                    <option value="1">Ｖ</option>
                                    <option value="2" selected="selected">Ｘ</option>
                                </select>
                            </td>
                            <td>
                                <textarea id="itemContent2" name="itemContent2" class="txt_mult" maxlengthC="300" rows="5" cols="30"></textarea>
                            </td>
                            <td>
                                <select id="itemCheck3" name="itemCheck3">
                                    <option value="0"><th:block th:text="#{'L160M01A.noNeed'}">免</th:block></option>
                                    <option value="1">Ｖ</option>
                                    <option value="2" selected="selected">Ｘ</option>
                                </select>
                            </td>
                            <td>
                                <textarea id="itemContent3" name="itemContent3" class="txt_mult" maxlengthC="300" rows="5" cols="30"></textarea>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <select id="itemCheck4" name="itemCheck4">
                                    <option value="0"><th:block th:text="#{'L160M01A.noNeed'}">免</th:block></option>
                                    <option value="1">Ｖ</option>
                                    <option value="2" selected="selected">Ｘ</option>
                                </select>
                            </td>
                            <td>
                                <textarea id="itemContent4" name="itemContent4" class="txt_mult" maxlengthC="300" rows="5" cols="30"></textarea>
                            </td>
                            <td>
                                <select id="itemCheck5" name="itemCheck5">
                                    <option value="0"><th:block th:text="#{'L160M01A.noNeed'}">免</th:block></option>
                                    <option value="1">Ｖ</option>
                                    <option value="2" selected="selected">Ｘ</option>
                                </select>
                            </td>
                            <td>
                                <textarea id="itemContent5" name="itemContent5" class="txt_mult" maxlengthC="300" rows="5" cols="30"></textarea>
                            </td>
                            <td>
                                <select id="itemCheck6" name="itemCheck6">
                                    <option value="0"><th:block th:text="#{'L160M01A.noNeed'}">免</th:block></option>
                                    <option value="1">Ｖ</option>
                                    <option value="2" selected="selected">Ｘ</option>
                                </select>
                            </td>
                            <td>
                                <textarea id="itemContent6" name="itemContent6" class="txt_mult" maxlengthC="300" rows="5" cols="30"></textarea>
                            </td>
                        </tr>
                    </table>
                    <br/>
                    <table class="A-creditChecking-tbl01 tbl04" width="100%">
                        <tr>
                            <th>
                                <div align="left">
                                    <th:block th:text="#{'L160M01A.sign'}">批示</th:block>
                                </div>
                            </th>
                            <th align="left">
                                <th:block th:text="#{'L160M01A.comm'}">審核意見</th:block>
                                <button id="getWord" type="button">
                                    <span class="text-only"><th:block th:text="#{'L160M01A.message47'}">片語</th:block></span>
                                </button>
                            </th>
                        </tr>
                        <tr>
                            <td>
                                <textarea name="sign" id="sign" cols="50" rows="3" class="txt_mult" maxlengthC="300" style="width: 412px; height: 66px;"></textarea>
                            </td>
                            <td>
                                <textarea name="comm" id="comm" cols="50" rows="3" class="txt_mult" maxlengthC="600" style="width: 412px; height: 66px;"></textarea>
                            </td>
                        </tr>
                    </table>
                    <table width="100%" border="0">
                        <tr style="height:15px">
                        </tr>
                        <tr>
                            <td width="3%" valign="top" class="tit1">
                                6、
                            </td>
                            <td class="tit1">
                                <th:block th:text="#{'L160M01A.message10'}">核准敘做文件應另影印交帳務、授信主管及會洽單位各抽存一份</th:block>
                                <br/>
                                （ <th:block th:text="#{'L160M01A.message11'}">即期、遠期信用狀、外銷貸款額度會外匯，買入光票會國外匯兌，透支會存款</th:block>）
                            </td>
                        </tr>
                        <tr style="height:15px">
                        </tr>
                        <tr>
                            <td width="3%" VALIGN="top" class="tit1">
                                7、
                            </td>
                            <td>
                                <span class="tit1"><th:block th:utext="#{'L160M01A.message12'}">會&nbsp;洽&nbsp;人&nbsp;員、單位：請簽章並抽存核准敘做文件影本</th:block></span>
                                <br/>
                                <th:block th:text="#{'L160M01A.message13'}">帳務（建檔）經辦</th:block>：
                                <br/>
                                <th:block th:text="#{'L160M01A.message14'}">外&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;匯</th:block>：
                                <br/>
                                <th:block th:text="#{'L160M01A.message15'}">國&ensp;&ensp;&ensp;外&ensp;&ensp;&ensp;匯&ensp;&ensp;兌</th:block>：
                                <br/>
                                <th:block th:text="#{'L160M01A.message16'}">存&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;款</th:block>：
                            </td>
                        </tr>
                    </table>
                    <br/>
                    <table width="100%" border="0">
                        <tr>
                            <td width="16%" class="tit1">
                                8、<th:block th:text="#{'L160M01A.otherfile'}">附加檔案</th:block>：
                            </td>
                            <td>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="2">
                                <div class="funcContainer">
                                    &nbsp;&nbsp;
                                    <button id="uploadFile" type="button">
                                        <span class="text-only"><th:block th:text="#{'L160M01A.bt05'}">選擇附加檔案</th:block></span>
                                    </button>
                                    <button id="deleteFile" type="button">
                                        <span class="text-only"><th:block th:text="#{'L160M01A.bt06'}">刪除</th:block></span>
                                    </button>
                                    <div id="gridfile"></div>
                                </div>
                            </td>
                        </tr>
                    </table>
                </form>
            </div>
            <div id="blackbox" style="display: none;">
                <table>
                    <tr>
                        <td>
                            <th:block th:text="#{'L160M01A.message17'}">請輸入英文名稱，以利黑名單查詢作業</th:block>：
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <input id="blackName" type="text" size="40"/>
                        </td>
                    </tr>
                </table>
            </div>
			<div id="openC801M01A" style="display:none;">
	            <td width="100%">
						<button type="button" id="pullinC801M01ABt">
	                         <span class="text-only"><th:block th:text="#{'button.pullinAgain'}">重新引進</th:block></span>
	                    </button>
	                    <button type="button" id="delC801M01ABt">
	                         <span class="text-only"><th:block th:text="#{'button.delete'}">刪除</th:block></span>
	                    </button>
	                    <div id="C801M01AGrid">             				        
						</div>
				</td>
			</div>	
	  </th:block>			
    </body>
</html>
