/* 
 * LPDFS01ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.LPDFS01ADao;
import com.mega.eloan.lms.model.LPDFS01A;

/** 授信 PDF 舊案明細檔 **/
@Repository
public class LPDFS01ADaoImpl extends LMSJpaDao<LPDFS01A, String>
	implements LPDFS01ADao {

	@Override
	public LPDFS01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<LPDFS01A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<LPDFS01A> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<LPDFS01A> findByIndex01(String mainId, String rptUNID, Integer rptSeq, String rptType, String rptFile){
		ISearch search = createSearchTemplete();
		List<LPDFS01A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (rptUNID != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "rptUNID", rptUNID);
		if (rptSeq != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "rptSeq", rptSeq);
		if (rptType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "rptType", rptType);
		if (rptFile != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "rptFile", rptFile);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
}