/* 
 * L170M01B.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import com.mega.eloan.common.model.IDocObject;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/** 一般授信資料檔 **/
@NamedEntityGraph(name = "L170M01B-entity-graph", attributeNodes = { @NamedAttributeNode("l170m01a")})
@Entity
@Table(name = "L170M01B", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class L170M01B extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 統一編號 **/
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 重覆序號 **/
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/** 額度序號 **/
	@Column(name = "CNTRNO", length = 12, columnDefinition = "CHAR(12)")
	private String cntrNo;

	/**
	 * 放款帳號
	 * <p/>
	 * 100/09/16新增
	 */
	@Column(name = "LOANNO", length = 14, columnDefinition = "CHAR(14)")
	private String loanNo;

	/**
	 * 資料日期
	 * <p/>
	 * 100/02/17補充說明<br/>
	 * 新增：<br/>
	 * 若為【產生所有授信資料】所產生時填入日期；若為【手動新增】所產生時則此欄為空。<br/>
	 * 刪除：<br/>
	 * 若為【刪除所有授信資料】時，只刪除本欄不為空的資料(即不含手動新增的資料)；若為【手動新增】所產生的資料需透過【手動刪除】才能刪除。
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "LNDATADATE", columnDefinition = "DATE")
	private Date lnDataDate;

	/** 授信科目(代碼) **/
	@Column(name = "LOANTP", length = 3, columnDefinition = "VARCHAR(3)")
	private String loanTP;

	/** 授信科目 **/
	@Column(name = "SUBJECT", length = 300, columnDefinition = "VARCHAR(300)")
	private String subject;

	/** 會計科目 **/
	@Column(name = "ACTCD", length = 8, columnDefinition = "VARCHAR(8)")
	private String actcd;

	/**
	 * 新貸/舊案
	 * <p/>
	 * 引自e-Loan的「額度資訊檔」(QUOTAINF)的「新貸註記」(NEWCASE)，如為新貸(=Y)，則畫面顯示【新貸】
	 */
	@Column(name = "NEWCASE", length = 1, columnDefinition = "VARCHAR(1)")
	private String newCase;

	/** 額度(幣別) **/
	@Column(name = "QUOTACURR", length = 3, columnDefinition = "VARCHAR(3)")
	private String quotaCurr;

	/**
	 * 額度(匯率)
	 * <p/>
	 * 100/11/11配合DW匯率檔調整<br/>
	 * DECIMAL(9,5)<br/>
	 * (DECIMAL(12,8)<br/>
	 * 國內：匯率檔ELF337(RATETBL)<br/>
	 * 折台幣匯率<br/>
	 * 海外：匯率檔(DW_FXRTHOVS)<br/>
	 * 折本位幣匯率
	 */
	@Column(name = "QUOTARATE", columnDefinition = "DECIMAL(12,8)")
	private BigDecimal quotaRate;

	/** 額度(金額) **/
	@Column(name = "QUOTAAMT", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal quotaAmt;

	/** 前日結欠餘額(幣別) **/
	@Column(name = "BALCURR", length = 3, columnDefinition = "VARCHAR(3)")
	private String balCurr;

	/**
	 * 前日結欠餘額(匯率)
	 * <p/>
	 * 100/11/11配合DW匯率檔調整<br/>
	 * DECIMAL(9,5)<br/>
	 * (DECIMAL(12,8)<br/>
	 * 國內：匯率檔ELF337(RATETBL)<br/>
	 * 折台幣匯率<br/>
	 * 海外：匯率檔(DW_FXRTHOVS)<br/>
	 * 折本位幣匯率
	 */
	@Column(name = "BALRATE", columnDefinition = "DECIMAL(12,8)")
	private BigDecimal balRate;

	/** 前日結欠餘額(金額) **/
	@Column(name = "BALAMT", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal balAmt;

	/** 動用期限或授信期間(起) **/
	@Temporal(TemporalType.DATE)
	@Column(name = "FROMDATE", columnDefinition = "DATE")
	private Date fromDate;

	/** 動用期限或授信期間(迄) **/
	@Temporal(TemporalType.DATE)
	@Column(name = "ENDDATE", columnDefinition = "DATE")
	private Date endDate;

	/**
	 * 擔保品名稱
	 * <p/>
	 * 64個全型字<br/>
	 * 引自擔保品系統「擔保品」大類，若擔保品大類為06(額度本票/備償票據)時不顯示名稱，亦不累加其估值及押值<br/>
	 * 顯示格式：「權利質權：定存單」、「保證：信用保證機構保證九成」
	 */
	@Column(name = "GUARANTEENAME", length = 4200, columnDefinition = "VARCHAR(4200)")
	private String guaranteeName;

	/**
	 * 勘估值(幣別)
	 * <p/>
	 * 引自擔保品系統，「勘估值」的幣別，原幣
	 */
	@Column(name = "ESTCURR", length = 3, columnDefinition = "VARCHAR(3)")
	private String estCurr;

	/**
	 * 勘估值(金額)
	 * <p/>
	 * 引自擔保品系統「勘估值」<br/>
	 * 單位：仟元
	 */
	@Column(name = "ESTAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal estAmt;

	/**
	 * 押值(幣別)
	 * <p/>
	 * 引自擔保品系統「押值」的幣別，原幣
	 */
	@Column(name = "LOANCURR", length = 3, columnDefinition = "VARCHAR(3)")
	private String loanCurr;

	/**
	 * 押值(金額)
	 * <p/>
	 * 引自擔保品系統「押值」<br/>
	 * 單位：仟元
	 */
	@Column(name = "LOANAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal loanAmt;

	/**
	 * 保險相關資料
	 * <p/>
	 * 128個全型字<br/>
	 * 引進擔保品及保險相關資料，並組成字串顯示：「1.放款值佔鑑估值成數XXX％。 2.擔保品設值第Ｘ順位抵押權 予本行。3.擔保品投保火險TWD
	 * 999,999,999千元，到期日YYYY-MM-DD，受益人為本行。」<br/>
	 * ※本欄不列印
	 */
	@Column(name = "INSMEMO", length = 900, columnDefinition = "VARCHAR(900)")
	private String insMemo;

	/**
	 * 重要敍作條件
	 * <p/>
	 * 128個全型字
	 */
	@Column(name = "MAJORMEMO", length = 2400, columnDefinition = "VARCHAR(2400)")
	private String majorMemo;

	/** 建立人員號碼 **/
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Date updateTime;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumns({
			@JoinColumn(name = "mainId", referencedColumnName = "mainId", insertable = false, updatable = false),
			@JoinColumn(name = "custId", referencedColumnName = "custId", insertable = false, updatable = false),
			@JoinColumn(name = "dupNo", referencedColumnName = "dupNo", insertable = false, updatable = false) })
	private L170M01A l170m01a;

	/** 不循環/循環 **/
	@Column(name = "REVOLVE", length = 1, columnDefinition = "CHAR(1)")
	private String revolve;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得統一編號 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定統一編號 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得重覆序號 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定重覆序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得額度序號 **/
	public String getCntrNo() {
		return this.cntrNo;
	}

	/** 設定額度序號 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/**
	 * 取得放款帳號
	 * <p/>
	 * 100/09/16新增
	 */
	public String getLoanNo() {
		return this.loanNo;
	}

	/**
	 * 設定放款帳號
	 * <p/>
	 * 100/09/16新增
	 **/
	public void setLoanNo(String value) {
		this.loanNo = value;
	}

	/**
	 * 取得資料日期
	 * <p/>
	 * 100/02/17補充說明<br/>
	 * 新增：<br/>
	 * 若為【產生所有授信資料】所產生時填入日期；若為【手動新增】所產生時則此欄為空。<br/>
	 * 刪除：<br/>
	 * 若為【刪除所有授信資料】時，只刪除本欄不為空的資料(即不含手動新增的資料)；若為【手動新增】所產生的資料需透過【手動刪除】才能刪除。
	 */
	public Date getLnDataDate() {
		return this.lnDataDate;
	}

	/**
	 * 設定資料日期
	 * <p/>
	 * 100/02/17補充說明<br/>
	 * 新增：<br/>
	 * 若為【產生所有授信資料】所產生時填入日期；若為【手動新增】所產生時則此欄為空。<br/>
	 * 刪除：<br/>
	 * 若為【刪除所有授信資料】時，只刪除本欄不為空的資料(即不含手動新增的資料)；若為【手動新增】所產生的資料需透過【手動刪除】才能刪除。
	 **/
	public void setLnDataDate(Date value) {
		this.lnDataDate = value;
	}

	/** 取得授信科目(代碼) **/
	public String getLoanTP() {
		return this.loanTP;
	}

	/** 設定授信科目(代碼) **/
	public void setLoanTP(String value) {
		this.loanTP = value;
	}

	/** 取得授信科目 **/
	public String getSubject() {
		return this.subject;
	}

	/** 設定授信科目 **/
	public void setSubject(String value) {
		this.subject = value;
	}

	/** 取得會計科目 **/
	public String getActcd() {
		return this.actcd;
	}

	/** 設定會計科目 **/
	public void setActcd(String value) {
		this.actcd = value;
	}

	/**
	 * 取得新貸/舊案
	 * <p/>
	 * 引自e-Loan的「額度資訊檔」(QUOTAINF)的「新貸註記」(NEWCASE)，如為新貸(=Y)，則畫面顯示【新貸】
	 */
	public String getNewCase() {
		return this.newCase;
	}

	/**
	 * 設定新貸/舊案
	 * <p/>
	 * 引自e-Loan的「額度資訊檔」(QUOTAINF)的「新貸註記」(NEWCASE)，如為新貸(=Y)，則畫面顯示【新貸】
	 **/
	public void setNewCase(String value) {
		this.newCase = value;
	}

	/** 取得額度(幣別) **/
	public String getQuotaCurr() {
		return this.quotaCurr;
	}

	/** 設定額度(幣別) **/
	public void setQuotaCurr(String value) {
		this.quotaCurr = value;
	}

	/**
	 * 取得額度(匯率)
	 * <p/>
	 * 100/11/11配合DW匯率檔調整<br/>
	 * DECIMAL(9,5)<br/>
	 * (DECIMAL(12,8)<br/>
	 * 國內：匯率檔ELF337(RATETBL)<br/>
	 * 折台幣匯率<br/>
	 * 海外：匯率檔(DW_FXRTHOVS)<br/>
	 * 折本位幣匯率
	 */
	public BigDecimal getQuotaRate() {
		return this.quotaRate;
	}

	/**
	 * 設定額度(匯率)
	 * <p/>
	 * 100/11/11配合DW匯率檔調整<br/>
	 * DECIMAL(9,5)<br/>
	 * (DECIMAL(12,8)<br/>
	 * 國內：匯率檔ELF337(RATETBL)<br/>
	 * 折台幣匯率<br/>
	 * 海外：匯率檔(DW_FXRTHOVS)<br/>
	 * 折本位幣匯率
	 **/
	public void setQuotaRate(BigDecimal value) {
		this.quotaRate = value;
	}

	/** 取得額度(金額) **/
	public BigDecimal getQuotaAmt() {
		return this.quotaAmt;
	}

	/** 設定額度(金額) **/
	public void setQuotaAmt(BigDecimal value) {
		this.quotaAmt = value;
	}

	/** 取得前日結欠餘額(幣別) **/
	public String getBalCurr() {
		return this.balCurr;
	}

	/** 設定前日結欠餘額(幣別) **/
	public void setBalCurr(String value) {
		this.balCurr = value;
	}

	/**
	 * 取得前日結欠餘額(匯率)
	 * <p/>
	 * 100/11/11配合DW匯率檔調整<br/>
	 * DECIMAL(9,5)<br/>
	 * (DECIMAL(12,8)<br/>
	 * 國內：匯率檔ELF337(RATETBL)<br/>
	 * 折台幣匯率<br/>
	 * 海外：匯率檔(DW_FXRTHOVS)<br/>
	 * 折本位幣匯率
	 */
	public BigDecimal getBalRate() {
		return this.balRate;
	}

	/**
	 * 設定前日結欠餘額(匯率)
	 * <p/>
	 * 100/11/11配合DW匯率檔調整<br/>
	 * DECIMAL(9,5)<br/>
	 * (DECIMAL(12,8)<br/>
	 * 國內：匯率檔ELF337(RATETBL)<br/>
	 * 折台幣匯率<br/>
	 * 海外：匯率檔(DW_FXRTHOVS)<br/>
	 * 折本位幣匯率
	 **/
	public void setBalRate(BigDecimal value) {
		this.balRate = value;
	}

	/** 取得前日結欠餘額(金額) **/
	public BigDecimal getBalAmt() {
		return this.balAmt;
	}

	/** 設定前日結欠餘額(金額) **/
	public void setBalAmt(BigDecimal value) {
		this.balAmt = value;
	}

	/** 取得動用期限或授信期間(起) **/
	public Date getFromDate() {
		return this.fromDate;
	}

	/** 設定動用期限或授信期間(起) **/
	public void setFromDate(Date value) {
		this.fromDate = value;
	}

	/** 取得動用期限或授信期間(迄) **/
	public Date getEndDate() {
		return this.endDate;
	}

	/** 設定動用期限或授信期間(迄) **/
	public void setEndDate(Date value) {
		this.endDate = value;
	}

	/**
	 * 取得擔保品名稱
	 * <p/>
	 * 64個全型字<br/>
	 * 引自擔保品系統「擔保品」大類，若擔保品大類為06(額度本票/備償票據)時不顯示名稱，亦不累加其估值及押值<br/>
	 * 顯示格式：「權利質權：定存單」、「保證：信用保證機構保證九成」
	 */
	public String getGuaranteeName() {
		return this.guaranteeName;
	}

	/**
	 * 設定擔保品名稱
	 * <p/>
	 * 64個全型字<br/>
	 * 引自擔保品系統「擔保品」大類，若擔保品大類為06(額度本票/備償票據)時不顯示名稱，亦不累加其估值及押值<br/>
	 * 顯示格式：「權利質權：定存單」、「保證：信用保證機構保證九成」
	 **/
	public void setGuaranteeName(String value) {
		this.guaranteeName = value;
	}

	/**
	 * 取得勘估值(幣別)
	 * <p/>
	 * 引自擔保品系統，「勘估值」的幣別，原幣
	 */
	public String getEstCurr() {
		return this.estCurr;
	}

	/**
	 * 設定勘估值(幣別)
	 * <p/>
	 * 引自擔保品系統，「勘估值」的幣別，原幣
	 **/
	public void setEstCurr(String value) {
		this.estCurr = value;
	}

	/**
	 * 取得勘估值(金額)
	 * <p/>
	 * 引自擔保品系統「勘估值」<br/>
	 * 單位：仟元
	 */
	public BigDecimal getEstAmt() {
		return this.estAmt;
	}

	/**
	 * 設定勘估值(金額)
	 * <p/>
	 * 引自擔保品系統「勘估值」<br/>
	 * 單位：仟元
	 **/
	public void setEstAmt(BigDecimal value) {
		this.estAmt = value;
	}

	/**
	 * 取得押值(幣別)
	 * <p/>
	 * 引自擔保品系統「押值」的幣別，原幣
	 */
	public String getLoanCurr() {
		return this.loanCurr;
	}

	/**
	 * 設定押值(幣別)
	 * <p/>
	 * 引自擔保品系統「押值」的幣別，原幣
	 **/
	public void setLoanCurr(String value) {
		this.loanCurr = value;
	}

	/**
	 * 取得押值(金額)
	 * <p/>
	 * 引自擔保品系統「押值」<br/>
	 * 單位：仟元
	 */
	public BigDecimal getLoanAmt() {
		return this.loanAmt;
	}

	/**
	 * 設定押值(金額)
	 * <p/>
	 * 引自擔保品系統「押值」<br/>
	 * 單位：仟元
	 **/
	public void setLoanAmt(BigDecimal value) {
		this.loanAmt = value;
	}

	/**
	 * 取得保險相關資料
	 * <p/>
	 * 128個全型字<br/>
	 * 引進擔保品及保險相關資料，並組成字串顯示：「1.放款值佔鑑估值成數XXX％。 2.擔保品設值第Ｘ順位抵押權 予本行。3.擔保品投保火險TWD
	 * 999,999,999千元，到期日YYYY-MM-DD，受益人為本行。」<br/>
	 * ※本欄不列印
	 */
	public String getInsMemo() {
		return this.insMemo;
	}

	/**
	 * 設定保險相關資料
	 * <p/>
	 * 128個全型字<br/>
	 * 引進擔保品及保險相關資料，並組成字串顯示：「1.放款值佔鑑估值成數XXX％。 2.擔保品設值第Ｘ順位抵押權 予本行。3.擔保品投保火險TWD
	 * 999,999,999千元，到期日YYYY-MM-DD，受益人為本行。」<br/>
	 * ※本欄不列印
	 **/
	public void setInsMemo(String value) {
		this.insMemo = value;
	}

	/**
	 * 取得重要敍作條件
	 * <p/>
	 * 128個全型字
	 */
	public String getMajorMemo() {
		return this.majorMemo;
	}

	/**
	 * 設定重要敍作條件
	 * <p/>
	 * 128個全型字
	 **/
	public void setMajorMemo(String value) {
		this.majorMemo = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}

	public void setL170m01a(L170M01A l170m01a) {
		this.l170m01a = l170m01a;
	}

	public L170M01A getL170m01a() {
		return l170m01a;
	}

	/** 設定不循環/循環 */
	public void setRevolve(String revolve) {
		this.revolve = revolve;
	}

	/** 取得不循環/循環 */
	public String getRevolve() {
		return revolve;
	}
}
