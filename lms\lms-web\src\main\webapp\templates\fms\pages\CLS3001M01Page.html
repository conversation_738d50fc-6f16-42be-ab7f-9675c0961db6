<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:wicket="http://wicket.apache.org/">
<body>
	<wicket:extend>
		<script type="text/javascript" src="pagejs/fms/CLS3001M01Page.js"></script>
        <div class="button-menu funcContainer" id="buttonPanel">
        	<!-- ===================================== -->
			<wicket:enclosure>
				<span wicket:id="_btnSave" />
				
				<button type="button" id="btnSave">
					<span class="ui-icon ui-icon-jcs-04" />
					<wicket:message key="button.save">儲存</wicket:message>
				</button>
				<button type="button" id="btnSend">
					<span class="ui-icon ui-icon-jcs-02" />
					<wicket:message key="button.send">呈主管覆核</wicket:message>
				</button>				
			</wicket:enclosure>
			<!-- ===================================== -->
			<wicket:enclosure>
				<span wicket:id="_btnWAIT_APPROVE" />
				
				<button type="button" id="btnAccept">
					<span class="ui-icon ui-icon-check" />
					<wicket:message key="button.check">覆核</wicket:message>
				</button>
			</wicket:enclosure>
			<!-- ===================================== -->
			
			<button type="button" id="btnExit" class="forview">
				<span class="ui-icon ui-icon-jcs-01"></span>
				<wicket:message key="button.exit">離開</wicket:message>
			</button>
		</div>	
			
		<div class="tit2 color-black">
            <table width="100%">
                <tr>
                    <td width="100%">
                    	<wicket:message key="doc.title">總處特殊控管額度資料</wicket:message>
					</td>
                </tr>
            </table>
        </div>
		
		<div style='padding:1em;'>
        <form id="tabForm">
        	<fieldset>
                <legend>
                    <b><wicket:message key="label.basicData">基本資料</wicket:message></b>
                </legend>
				<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
	                <tbody>				
						<tr style='vertical-align:top;'>
							<td class="hd1" nowrap>
	                            <wicket:message key="C900S02D.caseBrId">分行別</wicket:message>&nbsp;&nbsp;
	                        </td>
	                        <td ><span class='field' id="caseBrId" name="caseBrId" />&nbsp;
							<span class='' id="caseBrIdDesc" name="caseBrIdDesc" />
	                        </td>
							<td class="hd1">
	                            <wicket:message key="doc.docStatus">文件狀態</wicket:message>&nbsp;&nbsp;
	                        </td>
	                        <td><b><span class="color-red" id="docStatus"></span></b>&nbsp;
	                        </td>
	                    </tr>		
	                	<tr>
							<td width="20%" class="hd1" nowrap>
								<wicket:message key="C900S02D.custId">統一編號</wicket:message>&nbsp;&nbsp;
	                        </td>
	                        <td width="30%">
	                        	<span class='field' id="custId" name="custId" />-
								<span class='field' id="dupNo" name="dupNo" />
	                        </td>	
							<td width="20%" class="hd1" nowrap>
								<wicket:message key="C900S02D.custName">名稱</wicket:message>&nbsp;&nbsp;								
	                        </td>
	                        <td width="30%">
	                        	<input type="text" class="hide" id="mainOid" name="mainOid" />
	                            <span class='field' id="custName" name="custName" />
	                        </td>						
	                    </tr>										
						<tr>
							<td class="hd1" nowrap>
	                            <wicket:message key="C900S02D.category">類別</wicket:message>&nbsp;&nbsp;
	                        </td>
	                        <td colspan='3'>
	                        	<!--
								多個類別時
								<select id="category" name="category" codeType="C900S02D_category"  ></select>
								-->
								<input type="radio" id="category" name="category" codeType="C900S02D_category" class="required" />
								
	                        </td>
	                    </tr>				
						<tr style='vertical-align:top;'>
							<td class="hd1" nowrap>
	                            <wicket:message key="C900S02D.cntrNo">額度序號</wicket:message>&nbsp;
									<button type="button" id="btnImport">
										<wicket:message key="btn.import">覆核</wicket:message>
									</button>	
	                        </td>
	                        <td ><span class='field' id="cntrNo" name="cntrNo" />
	                        </td>
	                    	<td  class="hd1" nowrap>
	                            <wicket:message key="C900S02D.document_no">簽報書案號</wicket:message>&nbsp;&nbsp;
	                        </td>
	                        <td><span class='field' id="document_no" name="document_no" />
	                        </td>
	                    </tr>		
						<tr style='vertical-align:top;'>
							<td class="hd1" nowrap>
	                            <wicket:message key="C900S02D.applyAmt">申請額度金額</wicket:message>&nbsp;
	                        </td>
	                        <td ><span class='field' id="curr" name="curr" /> &nbsp;
							<input type="text" id="applyAmt" name="applyAmt" size="18" maxlength="19" integer="13" class="numeric"/> &nbsp;
							<wicket:message key="C900S02D.applyAmt.unit">元 </wicket:message>&nbsp;
	                        </td>
	                    	<td  class="hd1" nowrap>
	                            &nbsp;
	                        </td>
	                        <td>&nbsp;
	                        </td>
	                    </tr>
					</tbody>
	            </table>
			</fieldset>
			
			<fieldset>
                    <legend>
                        <b><wicket:message key="doc.docUpdateLog">文件異動紀錄</wicket:message></b>
                    </legend>
                    <div class="funcContainer">
                        <div wicket:id="_docLog">
                        </div>
                    </div>
                    <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tbody>
                            <tr>
                                <td width="20%" class="hd1">
                                    <wicket:message key="doc.creator">文件建立者</wicket:message>&nbsp;&nbsp;
                                </td>
                                <td width="30%">
                                    <span id="creator" name="creator"></span>
                                    (<span id="createTime" name="createTime"></span>)
                                </td>
                                <td width="20%" class="hd1">
                                    <wicket:message key="doc.lastUpdater">最後異動者</wicket:message>&nbsp;&nbsp;
                                </td>
                                <td width="30%">
                                    <span id="updater" name="updater"></span>
                                    (<span id="updateTime" name="updateTime"></span>)
                                </td>
                            </tr>                            
                        </tbody>
                    </table>
                </fieldset>
				
				
        </form>			
		</div>
		
		<!--============-->
		<div id="thickBoxImportCnrNoData" style="display:none">
			<table border='0' class='tb2'>
				<tr>
					<td class='hd2'><span id="label_queryId" />&nbsp;</td>
					<td><input type="text" id="queryId" name="queryId" maxlength="10" size="12" class="upText" />
						&nbsp;&nbsp;			
						<button type="button" id="btnFilterCntrNo">
		                    <span class="text-only"><wicket:message key="button.search">查詢</wicket:message></span>					
		                </button>			
					</td>
				</tr>	
				<tr>
					<td class='hd2'><span id="label_queryCntrNo" />&nbsp;</td>
					<td><input type="text" id="queryCntrNo" name="queryCntrNo" maxlength="12" size="14" class="upText" />
					</td>
				</tr>				
            </table>
			<div id='grid_importCnrNoData' />			
		</div>
	</wicket:extend>
</body>
</html>
