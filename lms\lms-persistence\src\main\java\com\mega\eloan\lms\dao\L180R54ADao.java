/* 
 * L180R54ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L180R54A;

/** 企金兆元振興融資方案月檔 **/
public interface L180R54ADao extends IGenericDao<L180R54A> {

	L180R54A findByOid(String oid);

	List<L180R54A> findByMainId(String mainId);

	List<L180R54A> findByIndex01(String mainId);

	List<L180R54A> findByIndex02(String isRevive, Date endDate);

	List<L180R54A> findByIndex03(String isRevive, String cltType, Date endDate);

	List<L180R54A> findByIndex04(String cntrMainId);

	public L180R54A findByMainIdAndCntrMainId(String mainId, String cntrMainId);
}