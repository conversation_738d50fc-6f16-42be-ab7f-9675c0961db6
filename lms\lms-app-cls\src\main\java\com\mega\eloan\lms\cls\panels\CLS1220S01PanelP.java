
package com.mega.eloan.lms.cls.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.DocLogPanel;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.cls.pages.CLS1220M01Page;
import com.mega.eloan.lms.cls.pages.CLS1220M03Page;
import com.mega.eloan.lms.cls.pages.CLS1220V01Page;
import com.mega.eloan.lms.dc.util.Util;
import com.mega.eloan.lms.model.C122M01A;

/**
 * <pre>
 * 線上信貸
 * </pre>
 * 
 * @since 2020/6/23
 * <AUTHOR>
 * @version <ul>
 *          <li>2020/6/23,EL08034,new
 *          </ul>
 */
public class CLS1220S01PanelP extends Panel {

	private static final long serialVersionUID = 1L;

	private C122M01A meta;

	/**
	 * @param id
	 */
	public CLS1220S01PanelP(String id) {
		super(id);
	}

	/**
	 * @param id
	 * @param updatePanelName
	 * @param meta
	 */
	public CLS1220S01PanelP(String id, boolean updatePanelName, C122M01A meta) {
		super(id, updatePanelName);
		this.meta = meta;
	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);
		renderJsI18N(CLS1220M01Page.class);
		renderJsI18N(CLS1220M03Page.class);
		renderJsI18N(CLS1220V01Page.class);

		String ploanplan = Util.trim(meta.getPloanPlan());
		model.addAttribute("show_C", ploanplan.startsWith("C") ? true : false);
		
		new DocLogPanel("_docLog").processPanelData(model, params);
		new CLS1220S06Panel01("_iPanelS").processPanelData(model, params);
	}
	
}
