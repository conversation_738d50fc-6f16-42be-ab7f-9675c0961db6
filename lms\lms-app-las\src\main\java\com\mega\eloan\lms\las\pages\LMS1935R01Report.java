package com.mega.eloan.lms.las.pages;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import com.mega.eloan.lms.base.pages.AbstractPdfReportPage;

/**
 * 稽核室 合併列印工作底稿基本內容
 * 
 * <AUTHOR>
 * 
 */
@Controller
@RequestMapping("/las/lms1935r01")
public class LMS1935R01Report extends AbstractPdfReportPage {

	public LMS1935R01Report() {
		super();
	}

	@Override
	public String getDownloadFileName() {
		return "lms1935r01.pdf";
	}

	@Override
	public String getFileDownloadServiceName() {
		return "lms1935r01rptservice";
	}
	
	//UPGRADE：或可參照LMS1200P01Page.java的方式return
	@Override
	protected String getViewName() {
		return null;
	}
}
