<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:wicket="http://wicket.apache.org/">
		<!--<script type="text/javascript">
			if(responseJSON.docType == "1"){
				alert(1);
				var Fname = "pagejs/lms/LMS1205S02Page.js";
				<!--document.write('<script type="text/javascript" src="pagejs/lms/LMS1205S02Page.js"><\/script>');
				document.write('<script type="text\/javascript" src="pagejs\/lms\/LMS1205S02Page.js"><\/script>');
			}else{
				alert("test");
			}
		</script>-->
<body>
	<wicket:panel>
		<fieldset>
			<legend>
				<b><wicket:message key="l120s01a.subtitle1">借款人明細</wicket:message></b>
			</legend>
			<div id="bowbtn" class="funcContainer">
				&nbsp;&nbsp;
				<button type="button"
					onclick="openList('thickboxaddborrow','新增借款人',500,240);">
					<span class="text-only"><wicket:message key="l120s01a.btn1">新增借款人</wicket:message></span>
				</button>
			</div>
			<div id="l120s01agrid" width="100%"
				style="margin-left: 10px; margin-right: 10px">
			</div>
		</fieldset>
		<div id="thickboxaddborrow" style="display: none;">
			<form id="addborrow">
				<!--<table class="A-creditChecking-tbl01 tbl09">-->
				<table class="tb2" width="100%" border="0" cellspacing="0"
					cellpadding="0">
					<tr style="display:none;">
						<td class="hd1"><wicket:message key="l120s01a.doctype">企/個金</wicket:message>&nbsp;&nbsp;</td>
						<td><label><input name="docType" class="max" maxlength="1"
							type="radio" value="1" /><wicket:message key="l120s01a.radio1">企業戶</wicket:message></label><label><input name="docType" class="max"
							maxlength="1" type="radio" value="2" /><wicket:message key="l120s01a.radio2">個人戶</wicket:message></label>
						</td>
					</tr>
					<tr><input type="hidden" id="buscd" name="buscd"/></tr>
					<tr style="display:none;">
						<td width="20%" class="hd1"><wicket:message
								key="l120s01a.typcd">客戶型態(區部別)</wicket:message>&nbsp;&nbsp;
						</td>
						<td width="80%"><label><input name="typCd" class="max" maxlength="1" type="radio"
							value="1" /><wicket:message key="l120s01a.radio3">DBU 客戶</wicket:message></label><label><input name="typCd" class="max" maxlength="1"
							type="radio" value="4" /><wicket:message key="l120s01a.radio4">OBU 客戶</wicket:message></label><label><input name="typCd" class="max"
							maxlength="1" type="radio" value="5" /><wicket:message key="l120s01a.radio5">海外同業</wicket:message></label><label><input name="typCd"
							class="max" maxlength="1" type="radio" value="5" checked="checked" /><wicket:message key="l120s01a.radio6">海外客戶</wicket:message></label><label><input
							name="typCd" class="max" maxlength="1" type="radio" value="0"
							checked style="display: none;" /></label>
						</td>
					</tr>
					<tr>
						<td width="20%" class="hd1">
							<input type="radio" name="rborrowA" checked="checked" value="1" onclick="$('._rborrowa').attr('disabled',false);$('._rborrowb').val('').attr('disabled',true);"/>
							<wicket:message key="l120s01a.custid">身分證統編</wicket:message>&nbsp;&nbsp;						
						</td>
						<td width="80%">
							<input id="custId" name="custId"
							class="max upText _rborrowa" size="10" maxlength="10" />
							&nbsp;&nbsp; 
							<input id="dupNo" name="dupNo" class="max" size="1" maxlength="1" disabled="true" />
							&nbsp;&nbsp;
							<button type="button" id="getCustData" name="getCustData">
							<span class="text-only"><wicket:message key="l120s01a.btn2">引進</wicket:message></span>
							</button>
						</td>
					</tr>
					<tr>
						<td width="20%" class="hd1">
							<input type="radio" name="rborrowA" value="2" onclick="$('._rborrowa').val('').attr('disabled',true);$('._rborrowb').attr('disabled',false);"/>
							<wicket:message key="l120s01a.custname">借款人姓名</wicket:message>&nbsp;&nbsp;
						</td>
						<td width="80%">
							<input type="text" class="max _rborrowb" id="custName" name="custName"
								maxlength="120" disabled="true" /> &nbsp;&nbsp;
							<!--
							<button type="button">
								<span class="text-only"><wicket:message key="l120s01a.btn3">取號</wicket:message></span>
							</button>
							-->
						</td>
					</tr>
					<tr>
						<td colspan="2" class="text-red">
							<wicket:message key="l120s01a.other16">說明...</wicket:message>							
						</td>
					</tr>
				</table>
			</form>
		</div>
		<div id="openDocaddborrow" style="display: none;">
			<div>
				<div wicket:id="lmss02a_panel" id="lmss02a_panel" open="true"></div>
			</div>
		</div>
		<script type="text/javascript" src="pagejs/lms/LMSS02APage01.js?r=20130901"></script>	
	</wicket:panel>
</body>
</html>
