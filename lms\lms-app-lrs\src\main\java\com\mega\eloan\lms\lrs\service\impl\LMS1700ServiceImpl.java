package com.mega.eloan.lms.lrs.service.impl;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.LrsUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.dao.L170M01ADao;
import com.mega.eloan.lms.dao.L170M01DDao;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.lms.service.FSS1010GService;
import com.mega.eloan.lms.lrs.constants.lrsConstants;
import com.mega.eloan.lms.lrs.pages.LMS1700M01Page;
import com.mega.eloan.lms.lrs.service.LMS1700Service;
import com.mega.eloan.lms.lrs.service.LMS1801Service;
import com.mega.eloan.lms.lrs.service.LMS1810Service;
import com.mega.eloan.lms.mfaloan.bean.ELF412;
import com.mega.eloan.lms.mfaloan.bean.ELF412B;
import com.mega.eloan.lms.mfaloan.bean.ELF412C;
import com.mega.eloan.lms.mfaloan.bean.ELF493;
import com.mega.eloan.lms.mfaloan.bean.ELF494;
import com.mega.eloan.lms.mfaloan.bean.ELF495;
import com.mega.eloan.lms.mfaloan.service.MisELF412BService;
import com.mega.eloan.lms.mfaloan.service.MisELF412CService;
import com.mega.eloan.lms.mfaloan.service.MisELF412Service;
import com.mega.eloan.lms.mfaloan.service.MisELF447nService;
import com.mega.eloan.lms.mfaloan.service.MisELLNGTEEService;
import com.mega.eloan.lms.mfaloan.service.MisElCUS25Service;
import com.mega.eloan.lms.mfaloan.service.MisStoredProcService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.F101M01A;
import com.mega.eloan.lms.model.L170A01A;
import com.mega.eloan.lms.model.L170M01A;
import com.mega.eloan.lms.model.L170M01B;
import com.mega.eloan.lms.model.L170M01C;
import com.mega.eloan.lms.model.L170M01D;
import com.mega.eloan.lms.model.L170M01E;
import com.mega.eloan.lms.model.L170M01F;
import com.mega.eloan.lms.model.L170M01G;
import com.mega.eloan.lms.model.L170M01H;
import com.mega.eloan.lms.model.L170M01J;
import com.mega.eloan.lms.model.L180M01A;
import com.mega.eloan.lms.model.L180M01B;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

@Service
public class LMS1700ServiceImpl extends AbstractCapService implements
		LMS1700Service {

	private static final int MAXLEN_ELF494_CHKITEM = StrUtils
			.getEntityFileldLegth(ELF494.class, "elf494_chkItem", 2400);

	private static Logger logger = LoggerFactory
			.getLogger(LMS1700ServiceImpl.class);

	@Resource
	ICustomerService customerService;

	@Resource
	L170M01ADao l170m01aDao;

	@Resource
	LMS1801Service lms1801Service;

	@Resource
	LMS1810Service lms1810Service;

	@Resource
	RetrialService retrialService;

	@Resource
	EloandbBASEService eloandbBASEService;

	@Resource
	CodeTypeService codeTypeService;

	@Resource
	MisdbBASEService misdbBASEService;

	@Resource
	MisELF412Service misELF412Service;

	@Resource
	MisElCUS25Service misElCUS25Service;

	@Resource
	MisStoredProcService misStoredProcService;

	@Resource
	MisELLNGTEEService misELLNGTEEService;

	@Resource
	FSS1010GService fss1010GService;

	@Resource
	L170M01DDao l170m01ddao;
	@Resource
	L170M01ADao l170m01adao;

	@Resource
	LMSService lmsService;

	@Resource
	MisELF412BService misELF412BService;

	@Resource
	MisELF447nService misELF447nService;

	@Resource
	MisELF412CService misELF412CService;

	@Override
	public String l170m01a_docStatusDesc(L170M01A meta) {
		if (Util.isNotEmpty(meta.getDocStatus())) {
			Properties prop_lms1700m01 = MessageBundleScriptCreator
					.getComponentResource(LMS1700M01Page.class);
			String docStatus = Util.trim(meta.getDocStatus());
			if (Util.equals(docStatus, RetrialDocStatusEnum.區中心_編製中.getCode())) {
				return prop_lms1700m01.getProperty("label.status_1");// 覆審組編製中
			} else if (Util.equals(docStatus,
					RetrialDocStatusEnum.區中心_待覆核.getCode())) {
				return prop_lms1700m01.getProperty("label.status_3");// 覆審組待覆核
			} else if (Util.equals(docStatus,
					RetrialDocStatusEnum.編製中.getCode())) {
				return prop_lms1700m01.getProperty("label.status_4");// 受檢單位編製中
			} else if (Util.equals(docStatus,
					RetrialDocStatusEnum.待覆核.getCode())) {
				return prop_lms1700m01.getProperty("label.status_5");// 受檢單位待覆核
			} else if (Util.equals(docStatus,
					RetrialDocStatusEnum.已覆核未核定.getCode())) {
				return prop_lms1700m01.getProperty("label.status_6");// 已覆核未核定
			} else if (Util.equals(docStatus,
					RetrialDocStatusEnum.已覆核已核定.getCode())) {
				return prop_lms1700m01.getProperty("label.status_9");// 已覆核已核定
			} else {
				return docStatus;
			}
		}
		return "";
	}

	@Override
	/**
	 * 參考 FLMS170M01 fnSaveCheck
	 */
	public String checkIncompleteMsg(L170M01A meta) {
		Properties prop_lms1700m01 = MessageBundleScriptCreator
				.getComponentResource(LMS1700M01Page.class);

		List<String> errMsg = new ArrayList<String>();
		if (true) {// chk_01
			if (meta.getRetrialDate() == null) {
				// ui_lms1700.msg09=請輸入
				errMsg.add(prop_lms1700m01.getProperty("ui_lms1700.msg09")
						+ prop_lms1700m01.getProperty("L170M01A.retrialDate"));
			}

			if (LrsUtil.compareRptVersion(meta.getRptId(), ">=",
					LrsUtil.V_20161101)) {

				// J-106-0145-006 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
				if (Util.notEquals(Util.trim(meta.getCtlType()),
						LrsUtil.CTLTYPE_自辦覆審)) {

					// J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
					if (Util.equals(Util.trim(meta.getRealCkFg()), "")) {
						// ui_lms1700.msg09=請輸入
						errMsg.add(prop_lms1700m01
								.getProperty("ui_lms1700.msg09")
								+ prop_lms1700m01
										.getProperty("L170M01A.realCkFg")); // 覆審控制檔實地覆審註記
					}

					// J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
					if (Util.equals(Util.trim(meta.getRealRpFg()), "")) {
						// ui_lms1700.msg09=請輸入
						errMsg.add(prop_lms1700m01
								.getProperty("ui_lms1700.msg09")
								+ prop_lms1700m01
										.getProperty("L170M01A.realRpFg")); // 本案是否為實地覆審報告表
					}

					// BGN J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
					StringBuffer realWarnMsg = new StringBuffer("");

					String eRealRpFg = meta.getRealRpFg();
					Date eRetrialDate = meta.getRetrialDate() == null ? null
							: meta.getRetrialDate();
					String eRealCkFg = meta.getRealCkFg();
					Date eRealDt = meta.getRealDt() == null ? null : meta
							.getRealDt();

					// 實地覆審註記為Y 但本案非實地覆審報告表 檢核有沒有超過半年
					if (Util.equals(Util.trim(eRealRpFg), "N")) {
						if (Util.equals(Util.trim(eRealCkFg), "Y")) {
							// 超過半年未實地覆審

							if (eRetrialDate != null && eRealDt != null) {
								Date retrialDate = eRetrialDate;
								Date thisRealDt = eRealDt;

								int addedMonth = 12;

								Date calcDate = CapDate.addMonth(thisRealDt,
										addedMonth);

								if (LMSUtil.cmpDate(retrialDate, ">", calcDate)) {

									// ui_lms1700.msg31=實地覆審基準日與本次覆審日期已逾12個月，本次覆審報告表必須為實地覆審覆審報告表。
									realWarnMsg.append(prop_lms1700m01
											.getProperty("ui_lms1700.msg31"));

								}
							}

						}
					}

					if (Util.notEquals(realWarnMsg.toString(), "")) {
						errMsg.add(realWarnMsg.toString());
					}
				}

				// END J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
			}

			if (true) {
				Map<String, List<L170M01E>> m_l170m01e = retrialService
						.findL170M01E_type(retrialService.findL170M01E(meta));
				L170M01E l170m01e_C = LrsUtil.firstElm(
						m_l170m01e.get(LrsUtil.M01E_FLAG_C), "T");
				L170M01E l170m01e_M = LrsUtil.firstElm(
						m_l170m01e.get(LrsUtil.M01E_FLAG_M), "T");
				boolean emptyC = (l170m01e_C == null ? true : Util.isEmpty(Util
						.trim(l170m01e_C.getCrdType())));
				boolean emptyM = (l170m01e_M == null ? true : Util.isEmpty(Util
						.trim(l170m01e_M.getCrdType())));
				if (emptyC && emptyM) {
					// ui_lms1700.msg15=信用評等相關欄位不得空白
					errMsg.add(prop_lms1700m01.getProperty("ui_lms1700.msg15"));
				}
				L170M01E l170m01e_exC = LrsUtil.firstElm(
						m_l170m01e.get(LrsUtil.M01E_FLAG_C), "L");
				L170M01E l170m01e_exM = LrsUtil.firstElm(
						m_l170m01e.get(LrsUtil.M01E_FLAG_M), "L");
				boolean emptyExC = (l170m01e_exC == null ? true : Util
						.isEmpty(Util.trim(l170m01e_exC.getCrdType())));
				boolean emptyExM = (l170m01e_exM == null ? true : Util
						.isEmpty(Util.trim(l170m01e_exM.getCrdType())));
				if (emptyExC && emptyExM) {
					// ui_lms1700.msg15=信用評等相關欄位不得空白
					errMsg.add(prop_lms1700m01.getProperty("label.ex")
							+ prop_lms1700m01.getProperty("ui_lms1700.msg15"));
				}
			}
		}
		if (true) {// chk_02
			List<L170M01B> l170m01b_list = retrialService
					.findL170M01B_orderBy(meta);
			if (CollectionUtils.isEmpty(l170m01b_list)) {
				errMsg.add(prop_lms1700m01.getProperty("ui_lms1700.msg20"));// 尚未產生授信資料
			}
		}
		if (true) {// chk_03

		}
		if (true) {// chk_04
			List<L170M01D> l170m01d_list = retrialService
					.findL170M01D_orderBySeq(meta);
			Map<String, L170M01D> m_L170M01D = retrialService
					.lrs_toMap_keyAs_itemNo(l170m01d_list);
			if (true) {
				if (true) {
					// 檢查
					L170M01D n017_or_b015 = null;
					if (Util.equals(meta.getCtlType(), LrsUtil.CTLTYPE_自辦覆審)) {
						n017_or_b015 = m_L170M01D.get(LrsUtil.B015_N017);
					} else if (Util.equals(meta.getCtlType(),
							LrsUtil.CTLTYPE_價金履約)) {

					} else {
						n017_or_b015 = m_L170M01D.get(LrsUtil.N017);
					}

					if (n017_or_b015 != null
							&& Util.equals("Y", n017_or_b015.getChkResult())
							&& Util.isEmpty(Util.trim(n017_or_b015
									.getChkPreReview()))) {
						// ui_lms1700.msg14=請選擇第{0}項是否有改善，並且輸入說明
						// J-108-0888_05097_B1001
						String strSeq = "";
						if (Util.notEquals(
								Util.trim(n017_or_b015.getItemSeqShow()), "")) {
							strSeq = Util.trim(n017_or_b015.getItemSeqShow());
						} else {
							strSeq = String.valueOf(n017_or_b015.getItemSeq());
						}

						errMsg.add(MessageFormat.format(
								prop_lms1700m01.getProperty("ui_lms1700.msg14"),
								String.valueOf(strSeq), getClass()));
					}
				}
				if (true) {// 一般項目
					List<String> lossList = new ArrayList<String>();
					for (L170M01D l170m01d : l170m01d_list) {
						if (Util.equals(LrsUtil.Z_電腦建檔資料,
								l170m01d.getItemType())) {
							continue;
						}
						// J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
						if (Util.equals(LrsUtil.Y_履約條件, l170m01d.getItemType())) {
							continue;
						}
						// J-106-0145-001 Web e-Loan
						// 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
						if (Util.equals(LrsUtil.X_土建融, l170m01d.getItemType())) {
							continue;
						}
						
						if (LrsUtil.compareRptVersion(meta.getRptId(), ">=",
								LrsUtil.V_20240601)) {
							// J-113-0204 新增及修正說明文句
							// 土建融實地複審無此項目
							if (Util.equals(meta.getRealRpFg(), "Y")) {
								if (Util.equals(l170m01d.getItemNo(),
										LrsUtil.N033)) {
									continue;
								}
							}
						}
						
						// J-108-0888_05097_B1001
						String strSeq = "";
						if (Util.notEquals(
								Util.trim(l170m01d.getItemSeqShow()), "")) {
							strSeq = Util.trim(l170m01d.getItemSeqShow());
						} else {
							strSeq = String.valueOf(l170m01d.getItemSeq());
						}

						if (Util.isEmpty(Util.trim(l170m01d.getChkResult()))) {
							lossList.add(String.valueOf(strSeq));
						}
					}
					if (CollectionUtils.isNotEmpty(lossList)) {
						// ui_lms1700.msg02=覆審項目：{0}未輸入
						errMsg.add(MessageFormat.format(
								prop_lms1700m01.getProperty("ui_lms1700.msg02"),
								StringUtils.join(lossList, "、"), getClass()));
					}
				}

				// J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
				if (Util.equals(Util.trim(meta.getRealRpFg()), "Y")) {
					L170M01D n011 = m_L170M01D.get(LrsUtil.N011);
					if (n011 != null) {
						boolean loss = false;
						if (true) {
							// 土建融
							String[] itemNoArr = { LrsUtil.XA1A };
							for (L170M01D l170m01d : l170m01d_list) {
								if (Util.notEquals(LrsUtil.X_土建融,
										l170m01d.getItemType())) {
									continue;
								}
								if (CrsUtil.inCollection(l170m01d.getItemNo(),
										itemNoArr)
										&& Util.isEmpty(Util.trim(l170m01d
												.getChkResult()))) {
									loss = true;
								}
							}
						}

						// 附表 應檢視事項
						// J-106-0145-001 Web e-Loan
						// 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
						L170M01D xa1A = m_L170M01D.get(LrsUtil.XA1A);
						if (xa1A != null) {

							ArrayList<String> subArr = new ArrayList<String>();
							subArr.add(LrsUtil.XA11);
							subArr.add(LrsUtil.XA12);
							subArr.add(LrsUtil.XA13);
							subArr.add(LrsUtil.XA14);

							for (String itemNo : subArr) {
								L170M01D subItem = m_L170M01D.get(itemNo);
								if (subItem != null
										&& Util.isEmpty(Util.trim(subItem
												.getChkResult()))) {
									loss = true;
								}
							}
						}

						if (loss) {
							// ui_lms1700.msg16=覆審項目第{0}項之附表欄位不得空白

							// J-108-0888_05097_B1001
							String strSeq = "";
							if (Util.notEquals(
									Util.trim(n011.getItemSeqShow()), "")) {
								strSeq = Util.trim(n011.getItemSeqShow());
							} else {
								strSeq = String.valueOf(n011.getItemSeq());
							}

							errMsg.add(MessageFormat.format(prop_lms1700m01
									.getProperty("ui_lms1700.msg16"), String
									.valueOf(strSeq), getClass()));
						}

					}
				}

				// 「是」或「否」均必須註明實際工程進度/或履約情形
				if (true) {
					L170M01D n012 = m_L170M01D.get(LrsUtil.N012);
					if (n012 != null
							&& Util.notEquals("K", n012.getChkResult())) {
						// (Util.equals("Y", n012.getChkResult())
						// || Util.equals("N", n012.getChkResult()))) {
						boolean loss = false;

						if (Util.isEmpty(Util.trim(n012.getChkText()))) {
							loss = true;
						}

						if (loss) {
							// ui_lms1700.msg34=覆審項目第{0}項之覆審內容說明不得空白

							// J-108-0888_05097_B1001
							String strSeq = "";
							if (Util.notEquals(
									Util.trim(n012.getItemSeqShow()), "")) {
								strSeq = Util.trim(n012.getItemSeqShow());
							} else {
								strSeq = String.valueOf(n012.getItemSeq());
							}

							errMsg.add(MessageFormat.format(prop_lms1700m01
									.getProperty("ui_lms1700.msg34"), String
									.valueOf(strSeq), getClass()));
						}

					}
				}

				if (true) {
					// J-113-0066 企金覆審，覆審內容之覆審項目新增及修正說明文句
					if (LrsUtil.compareRptVersion(meta.getRptId(), ">=",
							LrsUtil.V_20240401)) {
						L170M01D n013 = m_L170M01D.get(LrsUtil.N013);
						if (n013 != null
								&& Util.equals(Util.trim(n013.getChkResult()),
										"N")) {
							boolean loss = false;
							if (Util.isEmpty(Util.trim(n013.getChkText()))) {
								loss = true;
							}
							if (loss) {
								errMsg.add(MessageFormat.format(prop_lms1700m01
										.getProperty("ui_lms1700.msg34"),
										String.valueOf(n013.getItemSeqShow()),
										getClass()));
							}
						}
					}
				}
				
				if (true) {
					// J-113-0204 新增及修正說明文句
					if (LrsUtil.compareRptVersion(meta.getRptId(), ">=",
							LrsUtil.V_20240601)) {
						if (!Util.equals(meta.getRealRpFg(), "Y")) {
							L170M01D n033 = m_L170M01D.get(LrsUtil.N033);
							if (n033 != null
									&& Util.equals(
											Util.trim(n033.getChkResult()), "Y")) {
								boolean loss = false;
								if (Util.isEmpty(Util.trim(n033.getChkText()))) {
									loss = true;
								}
								if (loss) {
									errMsg.add(MessageFormat.format(
											prop_lms1700m01
													.getProperty("ui_lms1700.msg34"),
											String.valueOf(n033
													.getItemSeqShow()),
											getClass()));
								}
							}
						}
					}
				}
				
				if (true) {
					L170M01D n027 = m_L170M01D.get(LrsUtil.N027);
					if (n027 != null) {
						boolean loss = false;
						List<String> hideItemNoList = new ArrayList<String>();
						if (true) {
							// 限電腦建檔
							String[] itemNoArr = { LrsUtil.ZA11, LrsUtil.ZA12,
									LrsUtil.ZA13, LrsUtil.ZA21, LrsUtil.ZA22,
									LrsUtil.ZA23, LrsUtil.ZA24, LrsUtil.ZA31,
									LrsUtil.ZA32, LrsUtil.ZA33, LrsUtil.ZA34,
									LrsUtil.ZA35, LrsUtil.ZB1A, LrsUtil.ZB2A,
									LrsUtil.ZB40, LrsUtil.ZB50,
									LrsUtil.ZC10, LrsUtil.ZC20, LrsUtil.ZC3A,
									LrsUtil.ZC3B, LrsUtil.ZC3C, LrsUtil.ZC3D };
														
							if (LrsUtil.compareRptVersion(meta.getRptId(),
									">=", LrsUtil.V_20240601)) {
								// J-113-0204 新增及修正說明文句
								// 土建融實地複審無這些項目
								if (Util.equals(meta.getRealRpFg(), "Y")) {
									hideItemNoList.add(LrsUtil.ZA13);
									hideItemNoList.add(LrsUtil.ZB40);
									hideItemNoList.add(LrsUtil.ZB50);
									hideItemNoList.add(LrsUtil.ZB14);
									hideItemNoList.add(LrsUtil.ZB15);
									hideItemNoList.add(LrsUtil.ZC10);
									hideItemNoList.add(LrsUtil.ZC1A);
									hideItemNoList.add(LrsUtil.ZC20);
									hideItemNoList.add(LrsUtil.ZC30);
									hideItemNoList.add(LrsUtil.ZC3A);
									hideItemNoList.add(LrsUtil.ZC3B);
									hideItemNoList.add(LrsUtil.ZC3C);
									hideItemNoList.add(LrsUtil.ZC3D);
								}
							}
							
							for (L170M01D l170m01d : l170m01d_list) {
								if (Util.notEquals(LrsUtil.Z_電腦建檔資料,
										l170m01d.getItemType())) {
									continue;
								}
								
								if (hideItemNoList.contains(l170m01d
										.getItemNo())) {
									continue;
								}
								
								if (CrsUtil.inCollection(l170m01d.getItemNo(),
										itemNoArr)
										&& Util.isEmpty(Util.trim(l170m01d
												.getChkResult()))) {
									loss = true;
								}
							}
						}
						// 附表 案下授信額度[有/無]屬於對大陸地區之授信
						L170M01D za23 = m_L170M01D.get(LrsUtil.ZA23);
						if (za23 != null
								&& Util.equals("Y", za23.getChkResult())) {
							String[] subArr = { LrsUtil.ZA2A, LrsUtil.ZA2B,
									LrsUtil.ZA2C, LrsUtil.ZA2D, LrsUtil.ZA2E,
									LrsUtil.ZA2F };
							for (String itemNo : subArr) {
								L170M01D subItem = m_L170M01D.get(itemNo);
								if (subItem != null
										&& Util.isEmpty(Util.trim(subItem
												.getChkResult()))) {
									loss = true;
								}
							}
						}
						// 附表 案下授信額度[有/無]擔保品
						L170M01D zb1a = m_L170M01D.get(LrsUtil.ZB1A);
						if (zb1a != null
								&& Util.equals("Y", zb1a.getChkResult())) {
							String[] subArr = { LrsUtil.ZB11, LrsUtil.ZB12,
									LrsUtil.ZB13,LrsUtil.ZB14,LrsUtil.ZB15 };
							for (String itemNo : subArr) {
								L170M01D subItem = m_L170M01D.get(itemNo);
								
								if (hideItemNoList.contains(itemNo)) {
									continue;
								}
								
								if (subItem != null
										&& Util.isEmpty(Util.trim(subItem
												.getChkResult()))) {
									loss = true;
								}
							}
						}

						// J-111-0405 更動覆審系統內以下15式覆審報告表之文字內容。
						// 附表 授信戶案下[有/無]額度之融資業務分類A-LOAN註記為「#」或海外AS-400註記為「A0#」
						L170M01D zb2a = m_L170M01D.get(LrsUtil.ZB2A);
						if (zb2a != null
								&& Util.equals("Y", zb2a.getChkResult())) {
							String[] subArr = { LrsUtil.ZB21 };
							for (String itemNo : subArr) {
								L170M01D subItem = m_L170M01D.get(itemNo);
								if (subItem != null
										&& Util.isEmpty(Util.trim(subItem
										.getChkResult()))) {
									loss = true;
								}
							}
						}
						
						// J-112-0280  新增企金覆審報告之「附表」項下E-LOAN系統建檔增列3.授信管理系統-「風險權數試算明細」。
						// 設定哪些欄位要檢查
						L170M01D zb30 = m_L170M01D.get(LrsUtil.ZB30);
						if (   zb30 != null
							&& !Util.equals("K", zb30.getChkResult())) {
							
							String[] subArr = { LrsUtil.ZB30,
									            LrsUtil.ZB3A, LrsUtil.ZB3B, 
									            LrsUtil.ZB3C, LrsUtil.ZB3D,
									            LrsUtil.ZB3E, LrsUtil.ZB3J,
									            LrsUtil.ZB34, 
									            LrsUtil.ZB3N, LrsUtil.ZB3Q
									          };
							for (String itemNo : subArr) {
								L170M01D subItem = m_L170M01D.get(itemNo);
								if (  subItem != null
									&& Util.isEmpty(Util.trim(subItem.getChkResult()))) {
									loss = true;
								}
							}
							
						}
						
						L170M01D zb3e = m_L170M01D.get(LrsUtil.ZB3E);
						if ( zb3e != null ){
							String[] subArr={};
							if(Util.equals("Y", zb3e.getChkResult())){
								subArr= new String[]{ LrsUtil.ZB3F};	
							}else if(Util.equals("N", zb3e.getChkResult())){
								subArr= new String[]{ LrsUtil.ZB3G, LrsUtil.ZB3H, LrsUtil.ZB3I};	
							} 
						      
							for (String itemNo : subArr) {
								L170M01D subItem = m_L170M01D.get(itemNo);
								if (  subItem != null
									&& Util.isEmpty(Util.trim(subItem.getChkResult()))) {
									loss = true;
								}
							}

						}
						
						L170M01D zb34 = m_L170M01D.get(LrsUtil.ZB34);
						if (   zb34 != null
							&& Util.equals("Y", zb34.getChkResult())) {
							
							String[] subArr = { LrsUtil.ZB3K, LrsUtil.ZB3L, LrsUtil.ZB3M};
							for (String itemNo : subArr) {
								L170M01D subItem = m_L170M01D.get(itemNo);
								if (  subItem != null
									&& Util.isEmpty(Util.trim(subItem.getChkResult()))) {
									loss = true;
								}
							}
							
						}
						
						L170M01D zb3n = m_L170M01D.get(LrsUtil.ZB3N);
						if (   zb3n != null
							&& Util.equals("Y", zb3n.getChkResult())) {
							
							String[] subArr = { LrsUtil.ZB3O, LrsUtil.ZB3P };
							for (String itemNo : subArr) {
								L170M01D subItem = m_L170M01D.get(itemNo);
								if (  subItem != null
									&& Util.isEmpty(Util.trim(subItem.getChkResult()))) {
									loss = true;
								}
							}
							
						}
						
						L170M01D zb3q = m_L170M01D.get(LrsUtil.ZB3Q);
						if (   zb3q != null
							&& Util.equals("Y", zb3q.getChkResult())) {
							
							String[] subArr = { LrsUtil.ZB3R, LrsUtil.ZB3S };
							for (String itemNo : subArr) {
								L170M01D subItem = m_L170M01D.get(itemNo);
								if (  subItem != null
									&& Util.isEmpty(Util.trim(subItem.getChkResult()))) {
									loss = true;
								}
							}
							
						}
						
						L170M01D zc10 = m_L170M01D.get(LrsUtil.ZC10);
						if (   zc10 != null
							&& Util.equals("N", zc10.getChkResult())) {
							
							String[] subArr = { LrsUtil.ZC1A};
							for (String itemNo : subArr) {
								L170M01D subItem = m_L170M01D.get(itemNo);
								if (  subItem != null
									&& Util.isEmpty(Util.trim(subItem.getChkResult()))) {
									loss = true;
								}
							}
							
						}						
						
						if (loss) {
							// ui_lms1700.msg16=覆審項目第{0}項之附表欄位不得空白

							// J-108-0888_05097_B1001
							String strSeq = "";
							if (Util.notEquals(
									Util.trim(n027.getItemSeqShow()), "")) {
								strSeq = Util.trim(n027.getItemSeqShow());
							} else {
								strSeq = String.valueOf(n027.getItemSeq());
							}

							errMsg.add(MessageFormat.format(prop_lms1700m01
									.getProperty("ui_lms1700.msg16"), String
									.valueOf(strSeq), getClass()));
						}

					}
				}

				// J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
				if (true) {
					L170M01D n015 = m_L170M01D.get(LrsUtil.N015);
					if (n015 != null) {
						boolean loss = false;
						if (true) {
							// 限履約條件
							String[] itemNoArr = { LrsUtil.YA1A, LrsUtil.YB1A };
							for (L170M01D l170m01d : l170m01d_list) {
								if (Util.notEquals(LrsUtil.Y_履約條件,
										l170m01d.getItemType())) {
									continue;
								}
								if (CrsUtil.inCollection(l170m01d.getItemNo(),
										itemNoArr)
										&& Util.isEmpty(Util.trim(l170m01d
												.getChkResult()))) {
									loss = true;
								}
							}
						}
						// 附表 應檢視事項

						// J-109-0336 檢視事項及承諾事項之管控機制 - 第三項 always 都顯示
						// J-109-0336_002 檢視事項及承諾事項之管控機制 - YA12 為"否" 出現 YA13；
						// YB11.YB12任一為"否" 出現 YB13
						boolean hide_YA13 = true; // J-105-0287-003 修改Web
													// e-Loan國內企金授信覆審系統履行條件之檢核判斷
						boolean lossTxt = false;
						L170M01D ya1A = m_L170M01D.get(LrsUtil.YA1A);
						if (ya1A != null
								&& Util.equals("Y", ya1A.getChkResult())) {

							// J-105-0287-003 修改Web e-Loan國內企金授信覆審系統履行條件之檢核判斷
							L170M01D subItemYA11 = m_L170M01D.get(LrsUtil.YA11);
							L170M01D subItemYA12 = m_L170M01D.get(LrsUtil.YA12);
							// J-109-0336 檢視事項及承諾事項之管控機制 - 第三項 always 都顯示
							// J-109-0336_002 檢視事項及承諾事項之管控機制 - YA12 為"否" 出現
							// YA13； YB11.YB12任一為"否" 出現 YB13
							/*
							 * if (subItemYA11 != null) { if (Util.equals("N",
							 * subItemYA11.getChkResult())) { hide_YA13 = false;
							 * } }
							 */
							if (subItemYA12 != null) {
								if (Util.equals("N", subItemYA12.getChkResult())) {
									hide_YA13 = false;
								}
							}

							ArrayList<String> subArr = new ArrayList<String>();
							subArr.add(LrsUtil.YA11);
							subArr.add(LrsUtil.YA12);

							// J-109-0336 檢視事項及承諾事項之管控機制 - 第三項 always 都顯示
							// J-109-0336_002 檢視事項及承諾事項之管控機制 - YA12 為"否" 出現
							// YA13； YB11.YB12任一為"否" 出現 YB13
							if (!hide_YA13) {
								subArr.add(LrsUtil.YA13);
							}

							for (String itemNo : subArr) {
								L170M01D subItem = m_L170M01D.get(itemNo);
								if (subItem != null
										&& Util.isEmpty(Util.trim(subItem
												.getChkResult()))) {
									loss = true;
								}
								// J-109-0336 檢視事項及承諾事項之管控機制
								// J-109-0336_002 檢視事項及承諾事項之管控機制 - YA12.YA13
								// 均應說明
								if ((Util.equals(itemNo, LrsUtil.YA12) || Util
										.equals(itemNo, LrsUtil.YA13))
										&& subItem != null
										&& Util.isEmpty(Util.trim(subItem
												.getChkText()))) {
									lossTxt = true;
								}
							}
						}
						// 附表 承諾事項
						// J-109-0336 檢視事項及承諾事項之管控機制 - 第三項 always 都顯示
						boolean hide_YB13 = true; // J-105-0287-003 修改Web
													// e-Loan國內企金授信覆審系統履行條件之檢核判斷
						L170M01D yb1A = m_L170M01D.get(LrsUtil.YB1A);
						if (yb1A != null
								&& Util.equals("Y", yb1A.getChkResult())) {

							// J-109-0336 檢視事項及承諾事項之管控機制 - 第三項 always 都顯示
							L170M01D subItemYB11 = m_L170M01D.get(LrsUtil.YB11);
							L170M01D subItemYB12 = m_L170M01D.get(LrsUtil.YB12);

							if (subItemYB11 != null) {
								if (Util.equals("N", subItemYB11.getChkResult())) {
									hide_YB13 = false;
								}
							}
							if (subItemYB12 != null) {
								if (Util.equals("N", subItemYB12.getChkResult())) {
									hide_YB13 = false;
								}
							}

							ArrayList<String> subArr = new ArrayList<String>();
							subArr.add(LrsUtil.YB11);
							subArr.add(LrsUtil.YB12);
							if (!hide_YB13) {
								subArr.add(LrsUtil.YB13);
							}

							for (String itemNo : subArr) {
								L170M01D subItem = m_L170M01D.get(itemNo);
								if (subItem != null
										&& Util.isEmpty(Util.trim(subItem
												.getChkResult()))) {
									loss = true;
								}
								// J-109-0336 檢視事項及承諾事項之管控機制
								// J-109-0336_002 檢視事項及承諾事項之管控機制 -
								// YB11.YB12.YB13 均應說明
								if (subItem != null
										&& Util.isEmpty(Util.trim(subItem
												.getChkText()))) {
									lossTxt = true;
								}
							}
						}

						if (loss) {
							// ui_lms1700.msg16=覆審項目第{0}項之附表欄位不得空白

							// J-108-0888_05097_B1001
							String strSeq = "";
							if (Util.notEquals(
									Util.trim(n015.getItemSeqShow()), "")) {
								strSeq = Util.trim(n015.getItemSeqShow());
							} else {
								strSeq = String.valueOf(n015.getItemSeq());
							}

							errMsg.add(MessageFormat.format(prop_lms1700m01
									.getProperty("ui_lms1700.msg16"), String
									.valueOf(strSeq), getClass()));
						}

						// J-109-0336 檢視事項及承諾事項之管控機制
						if (lossTxt) {
							// ui_lms1700.msg37=覆審項目第{0}項之{1}不得空白

							String strSeq = "";
							if (Util.notEquals(
									Util.trim(n015.getItemSeqShow()), "")) {
								strSeq = Util.trim(n015.getItemSeqShow());
							} else {
								strSeq = String.valueOf(n015.getItemSeq());
							}

							errMsg.add(MessageFormat.format(prop_lms1700m01
									.getProperty("ui_lms1700.msg37"), String
									.valueOf(strSeq), "應檢視事項及承諾事項內容說明",
									getClass()));
						}
					}
				}							
				
				if (true) {
					// J-113-0066 企金覆審，覆審內容之覆審項目新增及修正說明文句
					L170M01D n032 = m_L170M01D.get(LrsUtil.N032);
					if (n032 != null
							&& Util.equals(Util.trim(n032.getChkResult()), "N")) {
						boolean loss = false;
						if (Util.isEmpty(Util.trim(n032.getChkText()))) {
							loss = true;
						}
						if (loss) {
							errMsg.add(MessageFormat.format(prop_lms1700m01
									.getProperty("ui_lms1700.msg34"), String
									.valueOf(n032.getItemSeqShow()), getClass()));
						}
					}
				}
				
				
				if (true) {
					// J-113-0066 企金覆審，覆審內容之覆審項目新增及修正說明文句
					L170M01D b017 = m_L170M01D.get(LrsUtil.B017);
					if (b017 != null
							&& Util.equals(Util.trim(b017.getChkResult()), "N")) {
						boolean loss = false;
						if (Util.isEmpty(Util.trim(b017.getChkText()))) {
							loss = true;
						}
						if (loss) {
							errMsg.add(MessageFormat.format(prop_lms1700m01
									.getProperty("ui_lms1700.msg34"), String
									.valueOf(b017.getItemSeq()), getClass()));
						}
					}
				}
				
				if (true) {
					// J-113-0066 企金覆審，覆審內容之覆審項目新增及修正說明文句
					L170M01D b018 = m_L170M01D.get(LrsUtil.B018);
					if (b018 != null
							&& Util.equals(Util.trim(b018.getChkResult()), "N")) {
						boolean loss = false;
						if (Util.isEmpty(Util.trim(b018.getChkText()))) {
							loss = true;
						}
						if (loss) {
							errMsg.add(MessageFormat.format(prop_lms1700m01
									.getProperty("ui_lms1700.msg34"), String
									.valueOf(b018.getItemSeq()), getClass()));
						}
					}
				}
					
			}												
		}
		if (true) {// chk_05 true空 false 有
			L170M01F l170m01f = meta.getL170m01f();
			if(Util.isNotEmpty(l170m01f)) {
				if (Util.isEmpty(Util.trim(l170m01f.getRetialComm()))) {
					// ui_lms1700.msg05=為確保本行債權，有無必要辦理保全措施
					errMsg.add(prop_lms1700m01.getProperty("ui_lms1700.msg09") + ""
							+ prop_lms1700m01.getProperty("ui_lms1700.msg05"));
				}
				if (Util.isEmpty(Util.trim(l170m01f.getConFlag()))) {
					// ui_lms1700.msg03=請輸入覆審結果為正常或異常
					errMsg.add(prop_lms1700m01.getProperty("ui_lms1700.msg03"));
				} else {
					if (Util.equals("2", l170m01f.getConFlag())
							&& Util.isEmpty(Util.trim(l170m01f.getCondition()))) {
						// ui_lms1700.msg04=覆審異常時，請輸入覆審意見
						errMsg.add(prop_lms1700m01.getProperty("ui_lms1700.msg04"));
					}
				}
			}
		}

		// J-108-0268 逾期情形
		boolean chkOverDue = this.chkOverDue(meta);
		if (chkOverDue) {
			// if(meta.getOvQryDt() == null){
			// ui_lms1700.msg36=覆審項目：借款本息繳納是否正常？ --> 請查詢逾期情形
			errMsg.add(prop_lms1700m01.getProperty("ui_lms1700.msg36"));
		}

		// chk_06
		// J-110-0308 覆審考核表
		if (!retrialService.hidePaFormPanel()) {
			if (Util.isEmpty(Util.nullToSpace(meta.getNeedPa()))) {
				// ui_lms1700.msg38=考評表-是否有須扣分情事不得空白
				errMsg.add(prop_lms1700m01.getProperty("ui_lms1700.msg38"));
			}
		}

		if (errMsg.size() > 0) {
			return StringUtils.join(errMsg, "<br/>");
		} else {
			return "";
		}
	}

	@Override
	public void fnGetChairman(L170M01A meta) {
		String custId = meta.getCustId();
		String dupNo = meta.getDupNo();

		Map<String, Object> m_CMFCUS25 = misElCUS25Service.findByPk(custId,
				dupNo);
		String chairman = LMSUtil.getNotEmptyVal_str(m_CMFCUS25, "SUP1CNM",
				"SUP3CNM");
		meta.setChairman(chairman);
	}

	@Override
	public void fnGetCustBusData(L170M01A meta) {
		String custId = meta.getCustId();
		String dupNo = meta.getDupNo();

		Map<String, Object> m_busCd_ecoNm = misdbBASEService
				.findCustBussDataByIdAndDup(custId, dupNo);
		meta.setTradeType(Util.trim(MapUtils.getString(m_busCd_ecoNm, "ECONM")));
		meta.setBusCd(Util.trim(MapUtils.getString(m_busCd_ecoNm, "BUSCD")));
		meta.setBussKind(Util.trim(MapUtils
				.getString(m_busCd_ecoNm, "BUSSKIND")));
	}

	@Override
	public void fnGetLastCES120DocBusCd(L170M01A meta) {
		Map<String, Object> map = eloandbBASEService
				.findC120M01A_bizModeForLrs(meta.getOwnBrId(),
						meta.getCustId(), meta.getDupNo());

		meta.setCesTradeType(Util.trim(MapUtils.getString(map, "BIZNAME")));
		meta.setCesBusCd(Util.trim(MapUtils.getString(map, "BIZMODE")));
		meta.setCesSN(Util.trim(MapUtils.getString(map, "SN")));
		meta.setCesCompleteDate((Date) MapUtils.getObject(map, "COMPLETEDATE"));

	}

	@Override
	public String gfnDB2calllnsp0150(int mode, String branch, String custId,
			String dupNo) {
		Map<String, Object> data = misStoredProcService.callLNSP0150(branch,
				custId, dupNo);
		String sp_return = MapUtils.getString(data, "SP_RETURN", "");
		String debugInfo = "";
		String ELF412_MAINCUST = "";
		// J-105-0346-001 Web e-Loan國內企金授信覆審報告表，主要授信戶增加判斷BTT建檔資料。
		String ELF412_MAINCUST_BTT = "";

		if ("YES".equals(sp_return)) {
			ELF412_MAINCUST = StringUtils.substring(
					MapUtils.getString(data, "SP_MAIN_CUST_FLAG", ""), 0, 1);
			debugInfo = " ,raw:" + ELF412_MAINCUST;

			// J-105-0346-001 Web e-Loan國內企金授信覆審報告表，主要授信戶增加判斷BTT建檔資料。
			// 當不符合主要授信戶時，多加判斷MISAOF
			// E 符合授信額度標準，不符合主要戶
			// N 不符合授信額度標準，不符合主要戶
			if (Util.equals("E", ELF412_MAINCUST)
					|| Util.equals("N", ELF412_MAINCUST)) {
				Map<String, Object> misAofMap = misdbBASEService
						.selByBrNoAndCustId(branch, custId);
				if (misAofMap != null && !misAofMap.isEmpty()) {
					ELF412_MAINCUST_BTT = Util.trim(MapUtils.getString(
							misAofMap, "AO_CUST_LN", ""));
					if (Util.equals(ELF412_MAINCUST_BTT, "Y")) {
						if (Util.equals("E", ELF412_MAINCUST)) {
							ELF412_MAINCUST = "Y"; // E 符合授信額度標準，不符合主要戶 => Y
													// 符合授信額度標準，符合主要戶
						} else if (Util.equals("N", ELF412_MAINCUST)) {
							ELF412_MAINCUST = "A"; // N 不符合授信額度標準，不符合主要戶 => A
													// 不符合授信額度標準，符合主要戶
						}
					}
				}
			}

			if (mode == 3) {
				// '兩個都要問，直接回傳原值給外面程式處理 EX: gfnSendCTLListToBranch
				// '不處理

				// p.s.應指UI上的[符合授信額度標準、主要授信戶]
			} else {
				if (Util.equals("Y", ELF412_MAINCUST)) {
					// still Y
				} else if (Util.equals("E", ELF412_MAINCUST)) {
					// '符合授信額度標準，不符合主要戶
					if (mode == 1) {
						// '要問符合授信額度標準
						ELF412_MAINCUST = "Y";
					} else if (mode == 2) {
						// '要問主要戶
						ELF412_MAINCUST = "N";
					}
				} else if (Util.equals("A", ELF412_MAINCUST)) {
					// '不符合授信額度標準，符合主要戶
					if (mode == 1) {
						// '要問符合授信額度標準
						ELF412_MAINCUST = "N";
					} else if (mode == 2) {
						// '要問主要戶
						ELF412_MAINCUST = "Y";
					}

				} else if (Util.equals("N", ELF412_MAINCUST)) {
					// still N
				} else {
					ELF412_MAINCUST = "N";
				}
			}

		}
		logger.trace("gfnDB2calllnsp0150(" + mode + ", " + branch + ", "
				+ custId + ", " + dupNo + ") rtn [" + ELF412_MAINCUST + "]"
				+ debugInfo + "，selByBrNoAndCustId=[" + ELF412_MAINCUST_BTT
				+ "]");

		return ELF412_MAINCUST;
	}

	@Override
	public boolean getRlt_Guarantor(L170M01A meta, List<L170M01H> insUpdList,
			List<L170M01H> delList) {
		return _getRlt_BorrowerOrGuarantor(meta, LrsUtil.M01H_FLAG_GN,
				insUpdList, delList);
	}

	@Override
	public boolean getRlt_Borrower(L170M01A meta, List<L170M01H> insUpdList,
			List<L170M01H> delList) {
		return _getRlt_BorrowerOrGuarantor(meta, LrsUtil.M01H_FLAG_C,
				insUpdList, delList);
	}

	private boolean _getRlt_BorrowerOrGuarantor(L170M01A meta,
			String m01h_flag, List<L170M01H> insUpdList, List<L170M01H> delList) {
		Set<String> cntrNo_list = new HashSet<String>();
		List<L170M01B> l170m01b_list = retrialService
				.findL170M01B_orderBy(meta);
		boolean hasCntrNO = false;
		if (CollectionUtils.isNotEmpty(l170m01b_list)) {
			hasCntrNO = true;
			for (L170M01B l170m01b : l170m01b_list) {
				cntrNo_list.add(Util.trim(l170m01b.getCntrNo()));
			}
		}
		Map<String, Map<String, String>> map = _gen_L170M01H(m01h_flag, meta,
				cntrNo_list);

		List<L170M01H> db_l170m01h = null;
		if (Util.equals(m01h_flag, LrsUtil.M01H_FLAG_GN)) {
			db_l170m01h = retrialService.findL170M01H_fmtSYS_debTypeGN(meta);
		} else if (Util.equals(m01h_flag, LrsUtil.M01H_FLAG_C)) {
			db_l170m01h = retrialService.findL170M01H_fmtSYS_debTypeC(meta);
		}

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Timestamp nowTS = CapDate.getCurrentTimestamp();

		_procL170M01H(insUpdList, delList, meta, map, db_l170m01h,
				user.getUserId(), nowTS);

		return hasCntrNO;
	}

	private Map<String, Map<String, String>> _gen_L170M01H(String m01h_flag,
			L170M01A meta, Collection<String> cntrNo_list) {

		String id = meta.getCustId();
		String dupNo = meta.getDupNo();
		Map<String, Map<String, String>> flag__idDup_name = new HashMap<String, Map<String, String>>();
		if (true) {
			Map<String, String> map_C = new HashMap<String, String>();
			Map<String, String> map_G = new HashMap<String, String>();
			Map<String, String> map_N = new HashMap<String, String>();
			for (String cntrNo : cntrNo_list) {
				for (Map<String, Object> item : misELLNGTEEService.findByLrs(
						id, dupNo, cntrNo)) {
					String LNGENM = Util.trim(item.get("LNGENM"));
					String LNGEFLAG = Util.trim(item.get("LNGEFLAG"));

					String key = LMSUtil.getCustKey_len10custId(
							Util.trim(item.get("LNGEID")),
							Util.trim(item.get("DUPNO1")));

					if (Util.equals(UtilConstants.lngeFlag.共同借款人, LNGEFLAG)) {
						map_C.put(key, LNGENM);
					} else if (Util.equals(UtilConstants.lngeFlag.連帶保證人,
							LNGEFLAG)) {
						map_G.put(key, LNGENM);
					} else if (Util.equals(UtilConstants.lngeFlag.ㄧ般保證人,
							LNGEFLAG)) {
						map_N.put(key, LNGENM);
					}
				}
			}
			// ===
			if (Util.equals(LrsUtil.M01H_FLAG_ALL, m01h_flag)) {
				flag__idDup_name.put(UtilConstants.lngeFlag.共同借款人, map_C);
				flag__idDup_name.put(UtilConstants.lngeFlag.連帶保證人, map_G);
				flag__idDup_name.put(UtilConstants.lngeFlag.ㄧ般保證人, map_N);
			} else if (Util.equals(LrsUtil.M01H_FLAG_C, m01h_flag)) {
				flag__idDup_name.put(UtilConstants.lngeFlag.共同借款人, map_C);
			} else if (Util.equals(LrsUtil.M01H_FLAG_GN, m01h_flag)) {
				flag__idDup_name.put(UtilConstants.lngeFlag.連帶保證人, map_G);
				flag__idDup_name.put(UtilConstants.lngeFlag.ㄧ般保證人, map_N);
			}
		}

		return flag__idDup_name;
	}

	private L170A01A _gen_L170A01A(String retrialBrNo, L170M01A l170m01a,
			String authType, String examBrNo, MegaSSOUserDetails user,
			Timestamp nowTS) {
		L170A01A o = new L170A01A();
		o.setMainId(l170m01a.getMainId());
		o.setPid(l170m01a.getPid());
		o.setOwnUnit(examBrNo);
		o.setOwner(user.getUserId());
		o.setAuthTime(nowTS);
		o.setAuthType(authType);
		o.setAuthUnit(examBrNo);
		return o;
	}

	@Override
	public L170M01A addNewL170(String retrialBrNo, String examBrNo,
			String custId, String dupNo, String custName,
			boolean autoGetLoanData, List<String> failMsgList, String ctlType) {

		L170M01A l170m01a = _basicL170M01A(examBrNo, custId, dupNo, custName,
				failMsgList, ctlType);
		if (l170m01a != null) {
			_genL170M01A(retrialBrNo, l170m01a, autoGetLoanData);
		}
		return l170m01a;
	}

	@Override
	public L170M01A _basicL170M01A(String examBrNo, String custId,
			String dupNo, String custName, List<String> failMsgList,
			String ctlType) {

		// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		if (Util.equals(ctlType, LrsUtil.CTLTYPE_自辦覆審)) {
			ELF412B elf412b = misELF412BService.findByPk(examBrNo, custId,
					dupNo);
			if (elf412b == null) {
				failMsgList.add(examBrNo + " 分行 " + custId + " " + dupNo
						+ " 無覆審控制檔資料");
				return null;
			}
		} else if (Util.equals(ctlType, LrsUtil.CTLTYPE_價金履約)) {
			// J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
			ELF412C elf412c = misELF412CService.findByPk(examBrNo, custId,
					dupNo);
			if (elf412c == null) {
				failMsgList.add(examBrNo + " 分行 " + custId + " " + dupNo
						+ " 無覆審控制檔資料");
				return null;
			}
		} else {
			ELF412 elf412 = misELF412Service.findByPk(examBrNo, custId, dupNo);
			if (elf412 == null) {
				failMsgList.add(examBrNo + " 分行 " + custId + " " + dupNo
						+ " 無覆審控制檔資料");
				return null;
			}
		}

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Timestamp nowTS = CapDate.getCurrentTimestamp();

		// ---
		L170M01A l170m01a = new L170M01A();
		l170m01a.setMainId(IDGenerator.getUUID());
		l170m01a.setTypCd(LrsUtil.isCustId_Z(custId) ? "4" : "1");
		l170m01a.setCustId(custId);
		l170m01a.setDupNo(dupNo);
		l170m01a.setCustName(custName);
		l170m01a.setOwnBrId(examBrNo);
		l170m01a.setCreator(user.getUserId());
		l170m01a.setCreateTime(nowTS);
		l170m01a.setUpdater(user.getUserId());
		l170m01a.setUpdateTime(nowTS);
		l170m01a.setTotQuotaCurr("TWD");
		l170m01a.setTotBalCurr("TWD");
		l170m01a.setFreeG("N");
		l170m01a.setFreeC("N");

		// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		l170m01a.setCtlType(ctlType);

		// // J-109-0313 小規模覆審 L170M01A
		// // 產生 L170M01A 先不用壓 因為上傳覆審控制檔的時候都會再押最新
		// String[] arr = lmsService.getOnlySmallBussCaseC(custId, dupNo);
		// l170m01a.setIsSmallBuss(arr[0]);
		// BigDecimal sbScore = (Util.equals(arr[0], "Y") ?
		// BigDecimal.valueOf(Double.valueOf(Util.trim(arr[2]))) : null);
		// l170m01a.setSbScore(sbScore);

		return l170m01a;
	}

	@Override
	public void _genL170M01A(String retrialBrNo, L170M01A l170m01a,
			boolean autoGetLoanData) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String examBrNo = l170m01a.getOwnBrId();
		String custId = l170m01a.getCustId();
		String dupNo = l170m01a.getDupNo();
		String ctlType = Util.trim(l170m01a.getCtlType());

		// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		ctlType = Util.equals(ctlType, "") ? LrsUtil.CTLTYPE_主辦覆審 : ctlType;

		Date nowTS = new Date();
		if (autoGetLoanData) {
			// L170M01B 一般授信資料檔
			retrialService.importLNtoL170M01B(l170m01a);
		}

		if (true) {// 處理 L170M01C 最近三次財務及業務資料檔
			L170M01C l170m01c = new L170M01C();
			l170m01c.setMainId(l170m01a.getMainId());
			l170m01c.setCustId(l170m01a.getCustId());
			l170m01c.setDupNo(l170m01a.getDupNo());
			l170m01c.setCreator(user.getUserId());
			l170m01c.setCreateTime(nowTS);
			LrsUtil.setDefaultRatio(l170m01c);
			retrialService.save(l170m01c);
		}
		if (true) {// 處理 L170M01D 覆審項目檔
			List<L170M01D> saveList = new ArrayList<L170M01D>();
			retrialService
					.importRetrialItemToL170M01D(l170m01a, saveList, null);
			if (CollectionUtils.isNotEmpty(saveList)) {
				for (L170M01D l170m01d : saveList) {
					retrialService.save(l170m01d);
				}
			}
		}

		String fcrdType = "";
		String fcrdArea = "";
		String fcrdPred = "";
		String fcrdGrad = "";
		if (Util.equals(ctlType, LrsUtil.CTLTYPE_自辦覆審)) {
			ELF412B elf412b = null;
			elf412b = misELF412BService.findByPk(examBrNo, custId, dupNo);
			if (elf412b != null) {// 處理 L170M01E 信用評等資料檔
				// 因 CreditGrade, MowGrade 會在之後的 impData 引入
				// 這裡處理 FcrdGrad
				fcrdType = Util.trim(elf412b.getElf412b_fcrdType());
				fcrdArea = Util.trim(elf412b.getElf412b_fcrdArea());
				fcrdPred = Util.trim(elf412b.getElf412b_fcrdPred());
				fcrdGrad = Util.trim(elf412b.getElf412b_fcrdGrad());
			}
		} else if (Util.equals(ctlType, LrsUtil.CTLTYPE_價金履約)) {
			// J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
			ELF412C elf412c = null;
			elf412c = misELF412CService.findByPk(examBrNo, custId, dupNo);
			if (elf412c != null) {// 處理 L170M01E 信用評等資料檔
				// 因 CreditGrade, MowGrade 會在之後的 impData 引入
				// 這裡處理 FcrdGrad
				fcrdType = Util.trim(elf412c.getElf412c_fcrdType());
				fcrdArea = Util.trim(elf412c.getElf412c_fcrdArea());
				fcrdPred = Util.trim(elf412c.getElf412c_fcrdPred());
				fcrdGrad = Util.trim(elf412c.getElf412c_fcrdGrad());
			}
		} else {
			ELF412 elf412 = null;
			elf412 = misELF412Service.findByPk(examBrNo, custId, dupNo);
			if (elf412 != null) {// 處理 L170M01E 信用評等資料檔
				// 因 CreditGrade, MowGrade 會在之後的 impData 引入
				// 這裡處理 FcrdGrad
				fcrdType = Util.trim(elf412.getElf412_fcrdType());
				fcrdArea = Util.trim(elf412.getElf412_fcrdArea());
				fcrdPred = Util.trim(elf412.getElf412_fcrdPred());
				fcrdGrad = Util.trim(elf412.getElf412_fcrdGrad());
			}
		}
		if (Util.isNotEmpty(fcrdType) && Util.isNotEmpty(fcrdGrad)) {
			L170M01E l170m01e_F = new L170M01E();
			LrsUtil.initl170m01e(l170m01e_F, l170m01a, user.getUserId(), "T");

			LrsUtil.setL170M01E_FCRD(l170m01e_F, fcrdType, fcrdArea, fcrdPred,
					fcrdGrad);
			retrialService.save(l170m01e_F);
		}

		if (true) {// 處理 L170M01F 覆審意見檔
			L170M01F l170m01f = new L170M01F();
			l170m01f.setMainId(l170m01a.getMainId());
			l170m01f.setCustId(l170m01a.getCustId());
			l170m01f.setDupNo(l170m01a.getDupNo());
			l170m01f.setRetialComm("N");
			l170m01f.setConFlag("1");
			l170m01f.setCreator(user.getUserId());
			l170m01f.setCreateTime(nowTS);
			retrialService.save(l170m01f);
		}
		// 尚無 L170M01G 覆審報告表簽章欄檔
		if (true) {// 處理 L170A01A
			retrialService.save(_gen_L170A01A(retrialBrNo, l170m01a, "1",
					retrialBrNo, user, l170m01a.getCreateTime()));
			if (Util.notEquals(retrialBrNo, examBrNo)) {
				retrialService.save(_gen_L170A01A(retrialBrNo, l170m01a, "4",
						examBrNo, user, l170m01a.getCreateTime()));
			}
		}

		// J-110-0308 覆審考核表
		if (l170m01a != null) {
			List<L170M01J> orgM01jList = retrialService
					.findL170M01JByMainId(l170m01a);
			if (orgM01jList != null && orgM01jList.size() > 0) {
				retrialService.deleteL170M01J(orgM01jList);
			}
			String paVer = retrialService.getPaFormVer(l170m01a);
			l170m01a.setPaVer(paVer);
			List<L170M01J> l170m01jList = new ArrayList<L170M01J>();
			LrsUtil.initL170m01j(l170m01jList, l170m01a, user.getUserId(), paVer);
			// 2022/01/24 授審連喬凱說 預設為否
			if (!retrialService.hidePaFormPanel()) { // 有顯示頁籤的才預設
				l170m01a.setNeedPa("N");
			}
			if (!l170m01jList.isEmpty()) {
				retrialService.saveL170M01J(l170m01jList);
			}
		}
		// 在 impData 裡，已在最後執行 save L170M01A
		// 在 01 時，已一併處理[l170m01e_C, l170m01e_M][L170M01H Guarantor Borrower]
		impData(l170m01a, "01", new HashMap<String, String>());

	}

	private List<L170M01H> _filter(List<L170M01H> db_l170m01h, String debType,
			String debIdDup) {
		List<L170M01H> r = new ArrayList<L170M01H>();
		for (L170M01H o : db_l170m01h) {
			if (Util.equals(debType, o.getDebType())
					&& Util.equals(
							debIdDup,
							LMSUtil.getCustKey_len10custId(o.getDebId(),
									o.getDebDupNo()))) {
				r.add(o);
			}
		}
		return r;
	}

	/**
	 * 處理的 type 限制為 map.keySet();
	 */
	private void _procL170M01H(List<L170M01H> ins_updList,
			List<L170M01H> delList, L170M01A meta,
			Map<String, Map<String, String>> flag__idDup_name,
			List<L170M01H> db_l170m01h, String userId, Timestamp nowTS) {
		/*
		 * k: debType v: 從債務人的統編+重複碼
		 */
		Map<String, Set<String>> debType__debIdDup = new HashMap<String, Set<String>>();
		if (db_l170m01h != null && db_l170m01h.size() > 0) {
			Set<String> lngeFlagSet = flag__idDup_name.keySet();
			for (L170M01H model : db_l170m01h) {
				String debType = model.getDebType();
				if (!lngeFlagSet.contains(debType)) {
					continue;
				}

				if (!debType__debIdDup.containsKey(debType)) {
					debType__debIdDup.put(debType, new HashSet<String>());
				}

				debType__debIdDup.get(debType).add(
						LMSUtil.getCustKey_len10custId(model.getDebId(),
								model.getDebDupNo()));
			}
		}

		for (String LNGEFLAG : flag__idDup_name.keySet()) {
			Map<String, String> map = flag__idDup_name.get(LNGEFLAG);
			Set<String> db_debIdDup = new HashSet<String>();
			if (debType__debIdDup.containsKey(LNGEFLAG)) {
				db_debIdDup.addAll(debType__debIdDup.get(LNGEFLAG));
			}

			// insert
			if (true) {
				Set<String> ins_idDup = LMSUtil.elm_onlyLeft(map.keySet(),
						db_debIdDup);
				if (ins_idDup.size() > 0) {
					for (String idDup : ins_idDup) {
						String debId = Util.trim(StringUtils.substring(idDup,
								0, 10));
						String debDupNo = Util.trim(StringUtils.substring(
								idDup, 10));
						String debName = map.get(idDup);

						L170M01H l170m01h = new L170M01H();
						l170m01h.setMainId(meta.getMainId());
						l170m01h.setCustId(meta.getCustId());
						l170m01h.setDupNo(meta.getDupNo());
						l170m01h.setDebType(LNGEFLAG);
						l170m01h.setDebId(debId);
						l170m01h.setDebDupNo(debDupNo);
						l170m01h.setCustName(debName);
						l170m01h.setCreator(userId);
						l170m01h.setCreateTime(nowTS);
						l170m01h.setUpdater(userId);
						l170m01h.setUpdateTime(nowTS);
						// ---
						ins_updList.add(l170m01h);
					}
				}
			}

			// update
			if (true) {
				Set<String> upd_idDup = LMSUtil.elm_join(map.keySet(),
						db_debIdDup);
				if (upd_idDup.size() > 0) {
					for (String deb_idDup : upd_idDup) {
						List<L170M01H> r = _filter(db_l170m01h, LNGEFLAG,
								deb_idDup);
						if (CollectionUtils.isNotEmpty(r)) {
							ins_updList.addAll(r);
						}
					}
				}
			}

			// delete
			if (true) {
				Set<String> del_idDup = LMSUtil.elm_onlyRight(map.keySet(),
						db_debIdDup);
				if (del_idDup.size() > 0) {
					for (String deb_idDup : del_idDup) {
						List<L170M01H> r = _filter(db_l170m01h, LNGEFLAG,
								deb_idDup);
						if (CollectionUtils.isNotEmpty(r)) {
							delList.addAll(r);
						}
					}
				}
			}
		}
	}

	@Override
	public void impData(L170M01A meta, String flag, Map<String, String> showItem) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String custId = meta.getCustId();
		String dupNo = meta.getDupNo();

		if (Util.equals(flag, "01")) {
			if (true) {
				if (CrsUtil.isNull_or_ZeroDate(meta.getRetrialDate())) {
					meta.setRetrialDate(new Date());
				}
				showItem.put("retrialDate",
						Util.trim(TWNDate.toAD(meta.getRetrialDate())));
			}
			// ---
			if (true) {

				// J-105-0287-003 修改Web e-Loan國內企金授信覆審系統履行條件之檢核判斷
				// 先抓ELF494 上次覆審報告表的覆審日期

				// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能 +
				// ctlType
				// 最近一次覆審報告表日期
				Date elf494_lrdate = null;
				List<Map<String, Object>> elf493_494_list = misdbBASEService
						.gfnGenerateCTL_FLMS180R12_with_ctlType(
								meta.getOwnBrId(),
								custId,
								dupNo,
								StringUtils.substring(
										showItem.get("retrialDate"), 0, 7),
								meta.getCtlType());
				if (!CollectionUtils.isEmpty(elf493_494_list)) {
					for (Map<String, Object> elf493_494 : elf493_494_list) {
						if (Util.notEquals(elf493_494.get("ELF494_LRDATE"), "")) {
							elf494_lrdate = Util.parseDate(elf493_494
									.get("ELF494_LRDATE"));
						}
						break;
					}
				}

				// 目前ELF412上次覆審日
				Date elf412_lrdate = null;

				// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
				if (Util.equals(meta.getCtlType(), LrsUtil.CTLTYPE_自辦覆審)) {
					ELF412B elf412b = misELF412BService.findByPk(
							meta.getOwnBrId(), custId, dupNo);
					if (elf412b != null) {
						if (CrsUtil.isNOT_null_and_NOTZeroDate(elf412b
								.getElf412b_lrDate())) {
							elf412_lrdate = elf412b.getElf412b_lrDate();
						}
					}
				} else if (Util.equals(meta.getCtlType(), LrsUtil.CTLTYPE_價金履約)) {
					// J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
					ELF412C elf412c = misELF412CService.findByPk(
							meta.getOwnBrId(), custId, dupNo);
					if (elf412c != null) {
						if (CrsUtil.isNOT_null_and_NOTZeroDate(elf412c
								.getElf412c_lrDate())) {
							elf412_lrdate = elf412c.getElf412c_lrDate();
						}
					}
				} else {
					ELF412 elf412 = misELF412Service.findByPk(
							meta.getOwnBrId(), custId, dupNo);
					if (elf412 != null) {
						if (CrsUtil.isNOT_null_and_NOTZeroDate(elf412
								.getElf412_lrDate())) {
							elf412_lrdate = elf412.getElf412_lrDate();
						}
					}
				}

				Date lastRetrialDate = null;
				if (elf494_lrdate != null && elf412_lrdate != null) {
					// 改以前次覆審報告表為主
					// 比對比較小的
					// if (LMSUtil.cmpDate(elf494_lrdate, "<=", elf412_lrdate))
					// {
					// lastRetrialDate = elf494_lrdate;
					// } else {
					// lastRetrialDate = elf412_lrdate;
					// }
					lastRetrialDate = elf494_lrdate;
				} else if (elf494_lrdate != null) {
					lastRetrialDate = elf494_lrdate;
				} else {
					lastRetrialDate = elf412_lrdate;
				}

				meta.setLastRetrialDate(lastRetrialDate);

				showItem.put("lastRetrialDate",
						Util.trim(TWNDate.toAD(lastRetrialDate)));

			}
			if (true) {
				Map<String, Object> custMap = customerService.findByIdDupNo(
						meta.getCustId(), meta.getDupNo());
				meta.setCustName(Util.trim(MapUtils.getString(custMap, "CNAME")));
			}
			// ====================================
			_impData_02_chairman(meta, showItem);
			// ---
			_impData_03_tradeTypeBusCd(meta, showItem);
			// ---
			_impData_04_CES_bizInfo(meta, showItem);
			// ---
			_impData_05_mLoanPerson(meta, showItem);
			// ---
			_impData_06_mLoanPersonA(meta, showItem);

			if (true) {
				List<L170M01H> insUpdList = new ArrayList<L170M01H>();
				List<L170M01H> delList = new ArrayList<L170M01H>();
				_impData_07_Guarantor(meta, showItem, insUpdList, delList);

				for (L170M01H del_o : delList) {
					retrialService.del(del_o);
				}
				for (L170M01H o : insUpdList) {
					retrialService.save(o);
				}
			}
			if (true) {
				List<L170M01H> insUpdList = new ArrayList<L170M01H>();
				List<L170M01H> delList = new ArrayList<L170M01H>();
				_impData_08_Borrower(meta, showItem, insUpdList, delList);

				for (L170M01H del_o : delList) {
					retrialService.del(del_o);
				}
				for (L170M01H o : insUpdList) {
					retrialService.save(o);
				}
			}
			if (true) {
				Map<String, List<L170M01E>> m_l170m01e = retrialService
						.findL170M01E_type(retrialService.findL170M01E(meta));

				L170M01E l170m01e_C = LrsUtil.firstElm(
						m_l170m01e.get(LrsUtil.M01E_FLAG_C), "T");
				L170M01E l170m01e_M = LrsUtil.firstElm(
						m_l170m01e.get(LrsUtil.M01E_FLAG_M), "T");

				if (notExistOrIsNotCust(l170m01e_C)) {
					l170m01e_C = new L170M01E();
					// ---
					LrsUtil.initl170m01e(l170m01e_C, meta, user.getUserId(),
							"T");
				}
				if (notExistOrIsNotCust(l170m01e_M)) {
					l170m01e_M = new L170M01E();
					// ---
					LrsUtil.initl170m01e(l170m01e_M, meta, user.getUserId(),
							"T");
				}

				// J-107-0245_09301_B1001 Web
				// e-Loan企金授信系統覆審報告中增列上次覆審日當時之「信用評等及信用風險內部評等」資訊。
				L170M01E l170m01e_exC = LrsUtil.firstElm(
						m_l170m01e.get(LrsUtil.M01E_FLAG_C), "L");
				L170M01E l170m01e_exM = LrsUtil.firstElm(
						m_l170m01e.get(LrsUtil.M01E_FLAG_M), "L");
				L170M01E l170m01e_exF = LrsUtil.firstElm(
						m_l170m01e.get(LrsUtil.M01E_FLAG_F), "L");
				if (notExistOrIsNotCust(l170m01e_exC)) {
					l170m01e_exC = new L170M01E();
					LrsUtil.initl170m01e(l170m01e_exC, meta, user.getUserId(),
							"L");
					LrsUtil.clear_l170m01e_ex(l170m01e_exC, "C");
				}
				if (notExistOrIsNotCust(l170m01e_exM)) {
					l170m01e_exM = new L170M01E();
					LrsUtil.initl170m01e(l170m01e_exM, meta, user.getUserId(),
							"L");
					LrsUtil.clear_l170m01e_ex(l170m01e_exM, "M");
				}
				if (notExistOrIsNotCust(l170m01e_exF)) {
					l170m01e_exF = new L170M01E();
					LrsUtil.initl170m01e(l170m01e_exF, meta, user.getUserId(),
							"L");
					// 前次外部評等類別 空值不用存一筆資料
				}
				// 取得最新取得之前次資料
				String exMainid = eloandbBASEService.findL170M01A_exMainid(
						meta.getMainId(), meta.getCustId(), meta.getDupNo(),
						meta.getOwnBrId());
				L170M01A exL170M01A = null;
				L170M01E l170m01e_C_L = null;
				L170M01E l170m01e_M_L = null;
				L170M01E l170m01e_F_L = null;
				if (Util.isNotEmpty(exMainid)) {
					exL170M01A = retrialService.findL170M01A_mainId(exMainid);
					Map<String, List<L170M01E>> ex_m_l170m01e = retrialService
							.findL170M01E_type(retrialService
									.findL170M01E(exL170M01A));
					l170m01e_C_L = LrsUtil.firstElm(
							ex_m_l170m01e.get(LrsUtil.M01E_FLAG_C), "T");
					l170m01e_M_L = LrsUtil.firstElm(
							ex_m_l170m01e.get(LrsUtil.M01E_FLAG_M), "T");
					l170m01e_F_L = LrsUtil.firstElm(
							ex_m_l170m01e.get(LrsUtil.M01E_FLAG_F), "T");

					if (!notExistOrIsNotCust(l170m01e_C_L)) {
						l170m01e_exC.setCrdType(l170m01e_C_L.getCrdType());
						l170m01e_exC.setGrade(l170m01e_C_L.getGrade());
					} else {
						LrsUtil.clear_l170m01e_ex(l170m01e_exC, "C");
					}
					if (!notExistOrIsNotCust(l170m01e_M_L)) {
						l170m01e_exM.setCrdType(l170m01e_M_L.getCrdType());
						l170m01e_exM.setGrade(l170m01e_M_L.getGrade());
					} else {
						LrsUtil.clear_l170m01e_ex(l170m01e_exM, "M");
					}
					if (!notExistOrIsNotCust(l170m01e_F_L)) {
						l170m01e_exF.setCrdType(l170m01e_F_L.getCrdType());
						l170m01e_exF.setFcrdArea(l170m01e_F_L.getFcrdArea());
						l170m01e_exF.setFcrdPred(l170m01e_F_L.getFcrdPred());
						l170m01e_exF.setGrade(l170m01e_F_L.getGrade());
						l170m01e_exF.setScore(l170m01e_F_L.getScore());
						// 確認有前次外部評等類別 再存
						retrialService.save(l170m01e_exF);
					} else {
						// 前次外部評等類別 空值不用存一筆資料
					}
				}
				retrialService.save(l170m01e_exC);
				retrialService.save(l170m01e_exM);

				retrialService.gfnCTL_Import_CRDTTBL(l170m01e_C, custId, dupNo);
				retrialService
						.gfnCTL_Import_MONTHBAL(l170m01e_M, custId, dupNo);
				if (l170m01e_M == null
						|| Util.isEmpty(Util.trim(l170m01e_M.getGrade()))) {
					// 表示無 Mow 的資料
				} else {
					// 在有 Mow 的資料，清掉 CrdtTbl 的資料
					LrsUtil.clear_l170m01e_C(l170m01e_C);
				}
				// ---
				retrialService.save(l170m01e_C);
				retrialService.save(l170m01e_M);

				_impData_09_CreditMow(showItem, l170m01e_C, l170m01e_M,
						l170m01e_exC, l170m01e_exM);
			}

			// J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
			_impData_10_RealCkData(meta, showItem);

		} else if (Util.equals(flag, "02")) {
			_impData_02_chairman(meta, showItem);

		} else if (Util.equals(flag, "03")) {
			_impData_03_tradeTypeBusCd(meta, showItem);
		} else if (Util.equals(flag, "04")) {
			_impData_04_CES_bizInfo(meta, showItem);

		} else if (Util.equals(flag, "05")) {
			_impData_05_mLoanPerson(meta, showItem);

		} else if (Util.equals(flag, "06")) {
			_impData_06_mLoanPersonA(meta, showItem);

		} else if (Util.equals(flag, "07")) {
			List<L170M01H> insUpdList = new ArrayList<L170M01H>();
			List<L170M01H> delList = new ArrayList<L170M01H>();
			_impData_07_Guarantor(meta, showItem, insUpdList, delList);

			for (L170M01H del_o : delList) {
				retrialService.del(del_o);
			}
			for (L170M01H o : insUpdList) {
				retrialService.save(o);
			}
		} else if (Util.equals(flag, "08")) {
			List<L170M01H> insUpdList = new ArrayList<L170M01H>();
			List<L170M01H> delList = new ArrayList<L170M01H>();
			_impData_08_Borrower(meta, showItem, insUpdList, delList);

			for (L170M01H del_o : delList) {
				retrialService.del(del_o);
			}
			for (L170M01H o : insUpdList) {
				retrialService.save(o);
			}
		} else if (Util.equals(flag, "09A") || Util.equals(flag, "09B")
				|| Util.equals(flag, "09C")) {

			Map<String, List<L170M01E>> m_l170m01e = retrialService
					.findL170M01E_type(retrialService.findL170M01E(meta));

			L170M01E l170m01e_C = LrsUtil.firstElm(
					m_l170m01e.get(LrsUtil.M01E_FLAG_C), "T");
			L170M01E l170m01e_M = LrsUtil.firstElm(
					m_l170m01e.get(LrsUtil.M01E_FLAG_M), "T");

			if (notExistOrIsNotCust(l170m01e_C)) {
				l170m01e_C = new L170M01E();
				// ---
				LrsUtil.initl170m01e(l170m01e_C, meta, user.getUserId(), "T");
			}
			if (notExistOrIsNotCust(l170m01e_M)) {
				l170m01e_M = new L170M01E();
				// ---
				LrsUtil.initl170m01e(l170m01e_M, meta, user.getUserId(), "T");
			}

			// J-107-0245_09301_B1001 Web
			// e-Loan企金授信系統覆審報告中增列上次覆審日當時之「信用評等及信用風險內部評等」資訊。
			L170M01E l170m01e_exC = LrsUtil.firstElm(
					m_l170m01e.get(LrsUtil.M01E_FLAG_C), "L");
			L170M01E l170m01e_exM = LrsUtil.firstElm(
					m_l170m01e.get(LrsUtil.M01E_FLAG_M), "L");
			L170M01E l170m01e_exF = LrsUtil.firstElm(
					m_l170m01e.get(LrsUtil.M01E_FLAG_F), "L");
			if (notExistOrIsNotCust(l170m01e_exC)) {
				l170m01e_exC = new L170M01E();
				LrsUtil.initl170m01e(l170m01e_exC, meta, user.getUserId(), "L");
				LrsUtil.clear_l170m01e_ex(l170m01e_exC, "C");
			}
			if (notExistOrIsNotCust(l170m01e_exM)) {
				l170m01e_exM = new L170M01E();
				LrsUtil.initl170m01e(l170m01e_exM, meta, user.getUserId(), "L");
				LrsUtil.clear_l170m01e_ex(l170m01e_exM, "M");
			}
			if (notExistOrIsNotCust(l170m01e_exF)) {
				l170m01e_exF = new L170M01E();
				LrsUtil.initl170m01e(l170m01e_exF, meta, user.getUserId(), "L");
				// 前次外部評等類別 空值不用存一筆資料
			}
			// 取得最新取得之前次資料
			String exMainid = eloandbBASEService.findL170M01A_exMainid(
					meta.getMainId(), meta.getCustId(), meta.getDupNo(),
					meta.getOwnBrId());
			L170M01A exL170M01A = null;
			L170M01E l170m01e_C_L = null;
			L170M01E l170m01e_M_L = null;
			L170M01E l170m01e_F_L = null;
			if (Util.isNotEmpty(exMainid)) {
				exL170M01A = retrialService.findL170M01A_mainId(exMainid);
				Map<String, List<L170M01E>> ex_m_l170m01e = retrialService
						.findL170M01E_type(retrialService
								.findL170M01E(exL170M01A));
				l170m01e_C_L = LrsUtil.firstElm(
						ex_m_l170m01e.get(LrsUtil.M01E_FLAG_C), "T");
				l170m01e_M_L = LrsUtil.firstElm(
						ex_m_l170m01e.get(LrsUtil.M01E_FLAG_M), "T");
				l170m01e_F_L = LrsUtil.firstElm(
						ex_m_l170m01e.get(LrsUtil.M01E_FLAG_F), "T");

				if (!notExistOrIsNotCust(l170m01e_C_L)) {
					l170m01e_exC.setCrdType(l170m01e_C_L.getCrdType());
					l170m01e_exC.setGrade(l170m01e_C_L.getGrade());
				} else {
					LrsUtil.clear_l170m01e_ex(l170m01e_exC, "C");
				}
				if (!notExistOrIsNotCust(l170m01e_M_L)) {
					l170m01e_exM.setCrdType(l170m01e_M_L.getCrdType());
					l170m01e_exM.setGrade(l170m01e_M_L.getGrade());
				} else {
					LrsUtil.clear_l170m01e_ex(l170m01e_exM, "M");
				}
				if (!notExistOrIsNotCust(l170m01e_F_L)) {
					l170m01e_exF.setCrdType(l170m01e_F_L.getCrdType());
					l170m01e_exF.setFcrdArea(l170m01e_F_L.getFcrdArea());
					l170m01e_exF.setFcrdPred(l170m01e_F_L.getFcrdPred());
					l170m01e_exF.setGrade(l170m01e_F_L.getGrade());
					l170m01e_exF.setScore(l170m01e_F_L.getScore());
					// 確認有前次外部評等類別 再存
					retrialService.save(l170m01e_exF);
				} else {
					// 前次外部評等類別 空值不用存一筆資料
				}
			}
			retrialService.save(l170m01e_exC);
			retrialService.save(l170m01e_exM);

			if (Util.equals(flag, "09A")) {
				retrialService.gfnCTL_Import_CRDTTBL(l170m01e_C, custId, dupNo);
				// 09A:信用評等
				LrsUtil.clear_l170m01e_M(l170m01e_M);
			} else if (Util.equals(flag, "09B")) {
				LrsUtil.clear_l170m01e_C(l170m01e_C);
				// 09B:信用風險內部評等
				retrialService
						.gfnCTL_Import_MONTHBAL(l170m01e_M, custId, dupNo);
			} else if (Util.equals(flag, "09C")) {
				LrsUtil.clear_l170m01e_C(l170m01e_C);
				// 09C:全部免辦
				LrsUtil.clear_l170m01e_M(l170m01e_M);
			}
			// ---
			retrialService.save(l170m01e_C);
			retrialService.save(l170m01e_M);

			_impData_09_CreditMow(showItem, l170m01e_C, l170m01e_M,
					l170m01e_exC, l170m01e_exM);
		} else if (Util.equals(flag, "10")) {
			_impData_10_RealCkData(meta, showItem);
		}

		// ---
		showItem.put("flag", flag);
		retrialService.save(meta);
	}

	private void _impData_02_chairman(L170M01A meta,
			Map<String, String> showItem) {
		fnGetChairman(meta);
		// ---
		showItem.put("chairman", Util.trim(meta.getChairman()));
	}

	private void _impData_03_tradeTypeBusCd(L170M01A meta,
			Map<String, String> showItem) {
		fnGetCustBusData(meta);
		// ---
		showItem.put("tradeType", Util.trim(meta.getTradeType()));
		showItem.put("busCd_bussKind", LrsUtil.get_s01_busCd_bussKind(meta));
	}

	private void _impData_04_CES_bizInfo(L170M01A meta,
			Map<String, String> showItem) {
		fnGetLastCES120DocBusCd(meta);
		// ---
		showItem.put("cesBizInfo", LrsUtil.get_s01_cesBizInfo(meta));
	}

	private void _impData_05_mLoanPerson(L170M01A meta,
			Map<String, String> showItem) {
		int mode = 1;
		meta.setMLoanPerson(gfnDB2calllnsp0150(mode, meta.getOwnBrId(),
				meta.getCustId(), meta.getDupNo()));
		// ---
		showItem.put("mLoanPerson",
				LrsUtil.decide_mLoanPerson(meta.getMLoanPerson()));
	}

	private void _impData_06_mLoanPersonA(L170M01A meta,
			Map<String, String> showItem) {
		int mode = 2;
		meta.setMLoanPersonA(gfnDB2calllnsp0150(mode, meta.getOwnBrId(),
				meta.getCustId(), meta.getDupNo()));
		// ---
		showItem.put("mLoanPersonA",
				LrsUtil.decide_mLoanPerson(meta.getMLoanPersonA()));
	}

	private void _impData_07_Guarantor(L170M01A meta,
			Map<String, String> showItem, List<L170M01H> insUpdList,
			List<L170M01H> delList) {
		boolean hasCntrNO = getRlt_Guarantor(meta, insUpdList, delList);
		showItem.put("rltGuarantor",
				LrsUtil.toStr_Guarantor(insUpdList, hasCntrNO));
	}

	private void _impData_08_Borrower(L170M01A meta,
			Map<String, String> showItem, List<L170M01H> insUpdList,
			List<L170M01H> delList) {
		boolean hasCntrNO = getRlt_Borrower(meta, insUpdList, delList);
		showItem.put("rltBorrower",
				LrsUtil.toStr_Borrower(insUpdList, hasCntrNO));
	}

	private void _impData_09_CreditMow(Map<String, String> showItem,
			L170M01E l170m01e_C, L170M01E l170m01e_M, L170M01E l170m01e_exC,
			L170M01E l170m01e_exM) {
		showItem.put("CreditType", Util.trim(l170m01e_C.getCrdType()));
		showItem.put("CreditGrade", Util.trim(l170m01e_C.getGrade()));
		showItem.put("exCreditType", Util.trim(l170m01e_exC.getCrdType()));
		showItem.put("exCreditGrade", Util.trim(l170m01e_exC.getGrade()));

		showItem.put("MowType", Util.trim(l170m01e_M.getCrdType()));
		showItem.put("MowGrade", Util.trim(l170m01e_M.getGrade()));
		showItem.put("exMowType", Util.trim(l170m01e_exM.getCrdType()));
		showItem.put("exMowGrade", Util.trim(l170m01e_exM.getGrade()));
	}

	private boolean notExistOrIsNotCust(L170M01E l170m01e) {
		if (l170m01e == null) {
			return true;
		} else {
			if (LrsUtil.isCustL170M01E(l170m01e)) {
				return false;
			} else {
				return true;
			}
		}
	}

	@Override
	public void delMeta(L170M01A meta) {
		meta.setDeletedTime(CapDate.getCurrentTimestamp());
		retrialService.save(meta);
	}

	@Override
	public List<L170M01A> findUnFinish(String ownBrId, String custId,
			String dupNo) {
		ISearch search = l170m01aDao.createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", ownBrId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		search.addSearchModeParameters(SearchMode.NOT_EQUALS, "docStatus",
				RetrialDocStatusEnum.已覆核已核定.getCode());
		// ---
		search.setMaxResults(Integer.MAX_VALUE);
		return l170m01aDao.find(search);
	}

	@Override
	public void up_to_mis(L170M01A meta) throws CapException {
		gfnUpdateReDocData(meta);
	}

	private void gfnUpdateReDocData(L170M01A meta) throws CapException {
		gfnWrite170M01ToChkList(meta);

		ELF494 elf494 = new ELF494();
		List<ELF495> elf495_list = new ArrayList<ELF495>();
		ELF412 elf412 = null;
		ELF412B elf412b = null;
		ELF412C elf412c = null; // J-107-0254_09301_B1001
								// 配合授審處增加「對合作房仲業價金履約保證額度覆審報告表」

		if (Util.equals(meta.getCtlType(), LrsUtil.CTLTYPE_自辦覆審)) {
			elf412b = gfnDB2UpELF412B(meta);
			gfnDB2UpELF494_ELF495(meta, elf412b, elf494, elf495_list);
		} else if (Util.equals(meta.getCtlType(), LrsUtil.CTLTYPE_價金履約)) {
			elf412c = gfnDB2UpELF412C(meta);
			gfnDB2UpELF494_ELF495(meta, elf412c, elf494, elf495_list);
		} else {
			elf412 = gfnDB2UpELF412(meta);
			gfnDB2UpELF494_ELF495(meta, elf412, elf494, elf495_list);
		}
		// ---

		if (Util.equals(meta.getCtlType(), LrsUtil.CTLTYPE_自辦覆審)) {
			retrialService.upELF412B_DelThenInsert(elf412b);
		} else if (Util.equals(meta.getCtlType(), LrsUtil.CTLTYPE_價金履約)) {
			retrialService.upELF412C_DelThenInsert(elf412c);
		} else {
			retrialService.upELF412_DelThenInsert(elf412);
		}

		retrialService.upELF494_DelThenInsert(elf494);
		retrialService.upELF495_DelThenInsert(elf494.getElf494_rptDocId(),
				elf495_list);
	}

	private void gfnWrite170M01ToChkList(L170M01A meta) throws CapException {
		boolean chg_l180m01b = false;

		L180M01A l180m01a = retrialService.findL180M01A(meta);
		if (l180m01a == null) {
			throw new CapException("pid[" + meta.getPid() + "] not found",
					getClass());
		}

		String ctlType = Util.equals(Util.trim(meta.getCtlType()), "") ? LrsUtil.CTLTYPE_主辦覆審
				: Util.trim(meta.getCtlType());
		L180M01B l180m01b = retrialService.findL180M01B(meta);
		if (l180m01b != null) {
			if (Util.equals(lrsConstants.docStatus1.要覆審,
					l180m01b.getDocStatus1())) {
			} else {
				// 把 不覆審 → 要覆審
				lms1810Service.l180m01b_reCtl(l180m01b);
				chg_l180m01b = true;
			}
		} else {
			// 不存在於覆審名單，新增
			l180m01b = lms1810Service.gfnDB2CTLInsertNewList(l180m01a,
					meta.getCustId(), meta.getDupNo(), meta.getCustName(),
					ctlType);
			l180m01b.setCreateBY(lrsConstants.CREATEBY.人工產生);
			l180m01b.setNewBy170M01("Y");
			// ---
			chg_l180m01b = true;
		}

		if (chg_l180m01b) {
			retrialService.save(l180m01b);
			retrialService.save(l180m01a);

			if (Util.isEmpty(Util.trim(l180m01b.getProjectNo()))) {
				// XXX 這裡的 component 要繼承自 AbstractEloanPage
				// 因為 NumberService 會去 AbstractEloanPage_zh_TW.properties 中取得
				// key==caseNumber 的格式
				retrialService.genProjectNo_append(l180m01a);
				// ---
				// 在塞入 projectSeq,projectNo 後，再查一次 DB 中的資料列
				l180m01b = retrialService.findL180M01B(meta);
			} else {
				// l180m01b 仍相同
			}

			if (Util.equals("Y", l180m01b.getNewBy170M01())) {
				// 人工新增的 L180M01B,要在取得 projectSeq,projectNo 才上傳 ELF493
				List<L180M01B> l180m01b_list = new ArrayList<L180M01B>();
				l180m01b_list.add(l180m01b);
				List<ELF493> elf493_list = lms1801Service.gfnDB2UpELF493(
						l180m01a, l180m01b_list, meta.getApprover());
				retrialService.upELF493_DelThenInsert(elf493_list);
			}
		}
		if (Util.isEmpty(Util.trim(meta.getProjectNo()))) {
			meta.setProjectNo(l180m01b.getProjectNo());
			meta.setProjectSeq(l180m01b.getProjectSeq());
			// ---
			retrialService.save(meta);
		}

		// J-109-0313 小規模覆審 - 以最新判斷為主
		String[] arr = lmsService.getOnlySmallBussCaseC(meta.getCustId(),
				meta.getDupNo());
		meta.setIsSmallBuss(arr[0]);
		BigDecimal sbScore = (Util.equals(arr[0], "Y") ? BigDecimal
				.valueOf(Double.valueOf(Util.trim(arr[2]))) : null);
		meta.setSbScore(sbScore);
		retrialService.save(meta);
		if (Util.isEmpty(Util.trim(l180m01b.getIsSmallBuss()))) {
			// 上線前產生的名單 => 補資料
			// J-109-0313 小規模覆審 - 判斷是否為純小規模
			l180m01b.setIsSmallBuss(arr[0]);
			meta.setSbScore(sbScore);
			retrialService.save(l180m01b);
		}
	}

	/**
	 * J-106-0145-002 Web e-Loan 國內企金授信管理系統修改實地覆審相關功能
	 * 
	 * 一般/土建融用
	 * 
	 * @param meta
	 * @param elf412
	 * @param elf494
	 * @param elf495_list
	 */
	private void gfnDB2UpELF494_ELF495(L170M01A meta, ELF412 elf412,
			ELF494 elf494, List<ELF495> elf495_list) {
		if (true) {
			elf494.setElf494_branch(meta.getOwnBrId());
			elf494.setElf494_custId(meta.getCustId());
			elf494.setElf494_dupNo(meta.getDupNo());
			elf494.setElf494_dbuObu(Util.equals("4", meta.getTypCd()) ? "OBU"
					: "DBU");
			elf494.setElf494_rptDocId(meta.getMainId());
			// J-106-0145-002 Web e-Loan 國內企金授信管理系統修改實地覆審相關功能
			elf494.setElf494_ctlType(Util.equals(Util.trim(meta.getCtlType()),
					"") ? LrsUtil.CTLTYPE_主辦覆審 : Util.trim(meta.getCtlType()));

			if (CrsUtil.isNOT_null_and_NOTZeroDate(meta.getLastRetrialDate())) {
				elf494.setElf494_llrDate(meta.getLastRetrialDate());
			} else {
				elf494.setElf494_llrDate(CapDate.parseDate(CapDate.ZERO_DATE));
			}
			if (CrsUtil.isNOT_null_and_NOTZeroDate(meta.getRetrialDate())) {
				elf494.setElf494_lrDate(meta.getRetrialDate());
			} else {
				elf494.setElf494_lrDate(CapDate.parseDate(CapDate.ZERO_DATE));
			}
			String projectNo = Util.trim(meta.getProjectNo());
			if (Util.isNotEmpty(projectNo)) {
				String yyyy = StringUtils.substring(projectNo, 0, 4);
				String extractProjectNo = LrsUtil.extractProjectNo(projectNo);

				elf494.setElf494_projNo(projectNo);
				elf494.setElf494_dataDtY(String.valueOf(Util.parseInt(yyyy) - 1911));
				elf494.setElf494_batchNo(StringUtils.substring(
						extractProjectNo, 0, 3));
				elf494.setElf494_sno(StringUtils.substring(extractProjectNo, 4,
						7));
			} else {
				elf494.setElf494_projNo("");
				elf494.setElf494_dataDtY("");
				elf494.setElf494_batchNo("");
				elf494.setElf494_sno("");
			}

			elf494.setElf494_mainCust(elf412.getElf412_mainCust());
			elf494.setElf494_crdType(elf412.getElf412_crdType());
			elf494.setElf494_crdtTbl(elf412.getElf412_crdtTbl());
			elf494.setElf494_mowType(elf412.getElf412_mowType());
			elf494.setElf494_mowTbl1(elf412.getElf412_mowTbl1());
			elf494.setElf494_nckdFlag(meta.getNCkdFlag());

			if (true) {
				L170M01F l170m01f = meta.getL170m01f();
				elf494.setElf494_retial(l170m01f.getRetialComm());
				elf494.setElf494_conFlag(l170m01f.getConFlag());
				if (l170m01f.getUpDate() != null) {
					elf494.setElf494_upDate(l170m01f.getUpDate());
				} else {
					elf494.setElf494_upDate(CapDate.getCurrentTimestamp());
				}
			}
			if (true) {
				String elf494_managerId = "";
				String elf494_bossId = "";
				String elf494_apprId = "";
				if (true) {
					List<L170M01G> l170m01g_list = retrialService
							.findL170M01G_l170m01a(meta);
					List<L170M01G> l170m01g_L1 = retrialService
							.findL170M01G_byBranchTypeStaffJob(l170m01g_list,
									lrsConstants.BRANCHTYPE.覆審單位, "L1");
					List<L170M01G> l170m01g_L4 = retrialService
							.findL170M01G_byBranchTypeStaffJob(l170m01g_list,
									lrsConstants.BRANCHTYPE.覆審單位, "L4");
					List<L170M01G> l170m01g_L5 = retrialService
							.findL170M01G_byBranchTypeStaffJob(l170m01g_list,
									lrsConstants.BRANCHTYPE.覆審單位, "L5");

					if (CollectionUtils.isNotEmpty(l170m01g_L1)) {
						elf494_apprId = l170m01g_L1.get(0).getStaffNo();
					}
					if (CollectionUtils.isNotEmpty(l170m01g_L4)) {
						elf494_bossId = l170m01g_L4.get(0).getStaffNo();
					}
					if (CollectionUtils.isNotEmpty(l170m01g_L5)) {
						elf494_managerId = l170m01g_L5.get(0).getStaffNo();
					}
				}

				elf494.setElf494_managerId(elf494_managerId);
				elf494.setElf494_bossId(elf494_bossId);
				elf494.setElf494_apprId(elf494_apprId);
			}

			elf494.setElf494_updater(elf412.getElf412_updater());
			elf494.setElf494_tmestamp(elf412.getElf412_tmestamp());

			// J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
			elf494.setElf494_realRpFg(Util.trim(meta.getRealRpFg()));
			if (meta.getRealDt() != null) {
				elf494.setElf494_realRpDt(meta.getRealDt());
			}

			if (true) {
				/*
				 * 依 itemSeq 來排序 ------ Case 9: tmp2 = "計收之利率是否符合本行定價政策相關規範？"
				 * tmp3_name = "result_"+Cstr(26) tmp4_name =
				 * "unusual_"+Cstr(26) ------ 以上程式表示：第9項取得 result_26 的值
				 */
				List<String> chkItemList = new ArrayList<String>();
				Properties prop_lms1700m01 = MessageBundleScriptCreator
						.getComponentResource(LMS1700M01Page.class);
				// 參考 gfnDB2SQLELF494_UPDATE
				for (L170M01D l170m01d : retrialService
						.findL170M01D_orderBySeq(meta)) {
					if (Util.equals(LrsUtil.Z_電腦建檔資料, l170m01d.getItemType())) {
						continue;
					}

					// J-105-0287-002 修改Web e-Loan國內企金授信覆審系統
					if (Util.equals(LrsUtil.Y_履約條件, l170m01d.getItemType())) {
						continue;
					}
					String itemDesc = LrsUtil.getPrintItemContent(l170m01d,
							prop_lms1700m01);
					String chooseResult = Util.trim(l170m01d.getChkResult());
					String memo = _prefixStr(l170m01d, prop_lms1700m01)
							+ _to_uploadTxt(l170m01d.getChkText());

					// J-108-0888_05097_B1001
					String strSeq = "";
					if (Util.notEquals(Util.trim(l170m01d.getItemSeqShow()), "")) {
						strSeq = Util.trim(l170m01d.getItemSeqShow());
					} else {
						strSeq = String.valueOf(l170m01d.getItemSeq());
					}

					chkItemList.add(Util.trim(String.valueOf(strSeq) + " "
							+ itemDesc + " " + chooseResult + " " + memo));
				}
				elf494.setElf494_chkItem(Util.truncateString(
						StringUtils.join(chkItemList, ";"),
						MAXLEN_ELF494_CHKITEM));
			}
			elf494.setElf494_fcrdType(elf412.getElf412_fcrdType());
			elf494.setElf494_fcrdArea(elf412.getElf412_fcrdArea());
			elf494.setElf494_fcrdPred(elf412.getElf412_fcrdPred());
			elf494.setElf494_fcrdGrad(elf412.getElf412_fcrdGrad());
		}

		if (true) {
			Set<String> upCntrNoSet = new HashSet<String>();
			List<L170M01B> l170m01b_list = retrialService
					.findL170M01B_orderBy(meta);
			if (CollectionUtils.isNotEmpty(l170m01b_list)) {
				for (L170M01B l170m01b : l170m01b_list) {
					ELF495 elf495 = new ELF495();
					elf495.setElf495_branch(elf494.getElf494_branch());
					elf495.setElf495_custId(elf494.getElf494_custId());
					elf495.setElf495_dupNo(elf494.getElf494_dupNo());
					elf495.setElf495_dbuObu(elf494.getElf494_dbuObu());
					// J-106-0145-002 Web e-Loan 國內企金授信管理系統修改實地覆審相關功能
					elf495.setElf495_ctlType(elf494.getElf494_ctlType());
					// ===
					if (true) {
						elf495.setElf495_cntrNo(l170m01b.getCntrNo());
						if (CrsUtil.isNOT_null_and_NOTZeroDate(l170m01b
								.getFromDate())) {
							elf495.setElf495_durBeg(l170m01b.getFromDate());
						} else {
							elf495.setElf495_durBeg(CapDate
									.parseDate(CapDate.ZERO_DATE));
						}
						if (CrsUtil.isNOT_null_and_NOTZeroDate(l170m01b
								.getEndDate())) {
							elf495.setElf495_durEnd(l170m01b.getEndDate());
						} else {
							elf495.setElf495_durEnd(CapDate
									.parseDate(CapDate.ZERO_DATE));
						}
						elf495.setElf495_curr(l170m01b.getQuotaCurr());
						elf495.setElf495_quota(l170m01b.getQuotaAmt());
					}
					// ===
					elf495.setElf495_rptDocId(elf494.getElf494_rptDocId());
					elf495.setElf495_llrDate(elf494.getElf494_llrDate());
					elf495.setElf495_lrDate(elf494.getElf494_lrDate());
					elf495.setElf495_projNo(elf494.getElf494_projNo());
					elf495.setElf495_dataDtY(elf494.getElf494_dataDtY());
					elf495.setElf495_batchNo(elf494.getElf494_batchNo());
					elf495.setElf495_sno(elf494.getElf494_sno());
					elf495.setElf495_updater(elf494.getElf494_updater());
					elf495.setElf495_tmestamp(elf494.getElf494_tmestamp());
					// ---
					if (upCntrNoSet.contains(elf495.getElf495_cntrNo())) {
						// 在 L170M01B 中
						// 可能同一個額度會有 2 個科目[購料放款、外銷放款]
						// 同一個額度、科目，會有2種幣別的餘額 [TWD、USD]
						continue;
					} else {
						upCntrNoSet.add(elf495.getElf495_cntrNo());
					}
					// ---
					elf495_list.add(elf495);
				}
			}
		}

		// 上次覆審評等資訊
		Map<String, List<L170M01E>> m_l170m01e = retrialService
				.findL170M01E_type(retrialService.findL170M01E(meta));
		String excreditType = "";
		String excreditGrade = "";
		String exmowType = "";
		String exmowGrade = "";
		String exfcrdType = "";
		String exfcrdArea = "";
		String exfcrdPred = "";
		String exfcrdGrad = "";
		Integer exfcrdScore = null;

		if (true) {
			L170M01E l170m01e_C = LrsUtil.firstElm(
					m_l170m01e.get(LrsUtil.M01E_FLAG_C), "L");
			if (l170m01e_C != null) {
				// elf494.setElf494_excrdType(l170m01e_C.getCrdType())
				excreditType = Util.trim(l170m01e_C.getCrdType());
				excreditGrade = Util.trim(l170m01e_C.getGrade());
			}
			L170M01E l170m01e_M = LrsUtil.firstElm(
					m_l170m01e.get(LrsUtil.M01E_FLAG_M), "L");
			if (l170m01e_M != null) {
				exmowType = Util.trim(l170m01e_M.getCrdType());
				exmowGrade = Util.trim(l170m01e_M.getGrade());
			}
			L170M01E l170m01e_F = LrsUtil.firstElm(
					m_l170m01e.get(LrsUtil.M01E_FLAG_F), "L");
			if (l170m01e_F != null) {
				exfcrdType = Util.trim(l170m01e_F.getCrdType());
				exfcrdArea = Util.trim(l170m01e_F.getFcrdArea());
				exfcrdPred = Util.trim(l170m01e_F.getFcrdPred());
				exfcrdGrad = Util.trim(l170m01e_F.getGrade());
				exfcrdScore = l170m01e_F.getScore();
			}

			if (Util.equals(UtilConstants.crdType.未評等, excreditType)
					|| Util.isEmpty(excreditType)) {
				excreditType = "";
				excreditGrade = "";
			} else if (Util.equals(UtilConstants.Type.無資料_C, excreditType)) {
				excreditType = "X";
				excreditGrade = "";
			}
			if (Util.equals(UtilConstants.Casedoc.CrdType2.免辦, exmowType)
					|| Util.isEmpty(exmowType)) {
				exmowType = "";
				exmowGrade = "";
			} else if (Util.equals(UtilConstants.Type.無資料_M, excreditType)) {
				exmowType = "X";
				exmowGrade = "";
			}
			if (Util.isEmpty(exfcrdGrad)
					|| (exfcrdScore != null && exfcrdScore == 90)) {
				exfcrdType = "";
				exfcrdArea = "";
				exfcrdPred = "";
				exfcrdGrad = "";
			}

			if (Util.isNotEmpty(excreditType)) {
				excreditType = LMSUtil.getDesc(
						retrialService.get_lrs_CrdtType_2to1(), excreditType);
			}
			if (Util.isNotEmpty(exmowType)) {
				exmowType = LMSUtil.getDesc(
						retrialService.get_lrs_MowType_2to1(), exmowType);
			}

			elf494.setElf494_excrdType(excreditType);
			elf494.setElf494_excrdtTbl(excreditGrade);
			elf494.setElf494_exmowType(exmowType);
			elf494.setElf494_exmowTbl1(exmowGrade);

			elf494.setElf494_exfcrdType(exfcrdType);
			elf494.setElf494_exfcrdArea(exfcrdArea);
			elf494.setElf494_exfcrdPred(exfcrdPred);
			elf494.setElf494_exfcrdGrad(exfcrdGrad);
		}
	}

	private String _prefixStr(L170M01D l170m01d, Properties prop_lms1700m01) {

		// 前次覆審有無應行改善事項？ 【無|有|－】
		if (Util.equals(LrsUtil.N017, l170m01d.getItemNo())
				|| Util.equals(LrsUtil.B015_N017, l170m01d.getItemNo())) {
			if (Util.equals("Y", l170m01d.getChkPreReview())) {
				return prop_lms1700m01.getProperty("label.Y3");// 已改善
			} else if (Util.equals("N", l170m01d.getChkPreReview())) {
				return prop_lms1700m01.getProperty("label.N3");// 未改善
			}
		}
		return "";
	}

	private String _to_uploadTxt(String s) {
		String chkItem_str = Util.trim(s);
		chkItem_str = StringUtils.replace(chkItem_str, "[", "〔");
		chkItem_str = StringUtils.replace(chkItem_str, "]", "〕");
		chkItem_str = StringUtils.replace(chkItem_str, "(", "（");
		chkItem_str = StringUtils.replace(chkItem_str, ")", "）");
		chkItem_str = StringUtils.replace(chkItem_str, "^", "︿");
		chkItem_str = StringUtils.replace(chkItem_str, ";", "；");
		chkItem_str = StringUtils.replace(chkItem_str, "'", "’");
		chkItem_str = StringUtils.replace(chkItem_str, ",", "，");
		chkItem_str = StringUtils.replace(chkItem_str, "%", "％");
		chkItem_str = StringUtils.replace(chkItem_str, "\r", "");
		chkItem_str = StringUtils.replace(chkItem_str, "\n", "");
		return chkItem_str;
	}

	private ELF412 gfnDB2UpELF412(L170M01A meta) {
		String creditType = "";
		String creditGrade = "";

		String mowType = "";
		String mowGrade = "";

		String fcrdType = "";
		String fcrdArea = "";
		String fcrdPred = "";
		String fcrdGrad = "";

		if (true) {
			Map<String, List<L170M01E>> m_l170m01e = retrialService
					.findL170M01E_type(retrialService.findL170M01E(meta));
			L170M01E l170m01e_C = LrsUtil.firstElm(
					m_l170m01e.get(LrsUtil.M01E_FLAG_C), "T");
			L170M01E l170m01e_M = LrsUtil.firstElm(
					m_l170m01e.get(LrsUtil.M01E_FLAG_M), "T");
			L170M01E l170m01e_F = LrsUtil.firstElm(
					m_l170m01e.get(LrsUtil.M01E_FLAG_F), "T");
			Integer fcrdScore = null;

			if (l170m01e_C != null) {
				creditType = Util.trim(l170m01e_C.getCrdType());
				creditGrade = Util.trim(l170m01e_C.getGrade());
			}

			if (l170m01e_M != null) {
				mowType = Util.trim(l170m01e_M.getCrdType());
				mowGrade = Util.trim(l170m01e_M.getGrade());
			}

			if (l170m01e_F != null) {
				fcrdType = Util.trim(l170m01e_F.getCrdType());
				fcrdArea = Util.trim(l170m01e_F.getFcrdArea());
				fcrdPred = Util.trim(l170m01e_F.getFcrdPred());
				fcrdGrad = Util.trim(l170m01e_F.getGrade());
				fcrdScore = l170m01e_F.getScore();
			}
			// ======
			if (Util.equals(UtilConstants.crdType.未評等, creditType)
					|| Util.isEmpty(creditType)) {
				creditType = "";
				creditGrade = "";
			}
			if (Util.equals(UtilConstants.Casedoc.CrdType2.免辦, mowType)
					|| Util.isEmpty(mowType)) {
				mowType = "";
				mowGrade = "";
			}
			if (Util.isEmpty(fcrdGrad)
					|| (fcrdScore != null && fcrdScore == 90)) {
				fcrdType = "";
				fcrdArea = "";
				fcrdPred = "";
				fcrdGrad = "";
			}
			// ======
			// 把 2 碼的 creditType,mowType 轉成 1碼的格式，來上傳
			if (Util.isNotEmpty(creditType)) {
				creditType = LMSUtil.getDesc(
						retrialService.get_lrs_CrdtType_2to1(), creditType);
			}
			if (Util.isNotEmpty(mowType)) {
				mowType = LMSUtil.getDesc(
						retrialService.get_lrs_MowType_2to1(), mowType);
			}
		}
		String mLoanPerson = "Y".equals(meta.getMLoanPerson()) ? "Y" : "N";

		ELF412 elf412 = misELF412Service.findByPk(meta.getOwnBrId(),
				meta.getCustId(), meta.getDupNo());
		if (elf412 != null) {
			// J-108-0078_05097_B1001
			// 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。
			// 2020/04 配合新冠肺炎紓困貸款專案，新增 J.純紓困貸款戶之首次覆審。 第一次J 之後都改為A
			if (Util.equals(elf412.getElf412_rckdLine(), "C")
					|| Util.equals(elf412.getElf412_rckdLine(), "I")
					|| Util.equals(elf412.getElf412_rckdLine(), "J")) {
				if (Util.isNotEmpty(Util.trim(elf412.getElf412_mdFlag()))) {
					if (Util.equals("10", elf412.getElf412_mdFlag())) {
						elf412.setElf412_rckdLine("F");
					} else {
						// '其他:目前當作是 1.異常戶
						elf412.setElf412_rckdLine("D");
					}
				} else {
					elf412.setElf412_rckdLine("A");
				}
			} else if (Util.equals(elf412.getElf412_rckdLine(), "E")) {
				elf412.setElf412_rckdLine("D");
			} else if (Util.equals(elf412.getElf412_rckdLine(), "G")) {
				elf412.setElf412_rckdLine("F");
			}

			elf412.setElf412_newAdd("");
			elf412.setElf412_newDate("");
			// J-108-0078_05097_B1001
			// 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。
			elf412.setElf412_isAllNew("");
			// 2020/04 配合新冠肺炎紓困貸款專案，新增 J.純紓困貸款戶之首次覆審。
			elf412.setElf412_newRescue("");
			elf412.setElf412_newRescueYM("");
			// 2021/08 J-110-0272 做過覆審即排除於抽樣名單
			elf412.setElf412_randomType("");

			if (Util.isEmpty(Util.trim(elf412.getElf412_uckdLine()))) {
				elf412.setElf412_uckdLine("N");
			}
			if (elf412.getElf412_uckdDt() == null) {
				elf412.setElf412_uckdDt(CapDate.parseDate(CapDate.ZERO_DATE));
			}

			elf412.setElf412_crdType(creditType);
			elf412.setElf412_crdtTbl(creditGrade);
			elf412.setElf412_mowType(mowType);
			elf412.setElf412_mowTbl1(mowGrade);

			elf412.setElf412_fcrdType(fcrdType);
			elf412.setElf412_fcrdArea(fcrdArea);
			elf412.setElf412_fcrdPred(fcrdPred);
			elf412.setElf412_fcrdGrad(fcrdGrad);

			elf412.setElf412_mainCust(mLoanPerson);
			retrialService.gfnCTL_Caculate_ELF412(elf412, null);

			if (CrsUtil.isNOT_null_and_NOTZeroDate(meta.getLastRetrialDate())) {
				elf412.setElf412_llrDate(meta.getLastRetrialDate());
			} else {
				if (CrsUtil.isNOT_null_and_NOTZeroDate(elf412
						.getElf412_lrDate())) {
					elf412.setElf412_llrDate(elf412.getElf412_lrDate());
				} else {
					elf412.setElf412_llrDate(CapDate
							.parseDate(CapDate.ZERO_DATE));
				}
			}

			// J-109-0313 小規模覆審 需覆審案件審完後 壓不覆審11
			if (Util.equals(Util.trim(meta.getIsSmallBuss()), "Y")) {
				elf412.setElf412_nckdFlag(LrsUtil.NCKD_11_小規模營業人_央行C方案_已抽樣覆審於次年起免辦覆審或未列於抽樣需覆審名單內);
				elf412.setElf412_nckdDate(new Date());
			} else {
				// J-109-0456 覆審500w&70% 新做增額審完後 不需再覆審
				// 2020/12/02 莊正枝襄理說 同時符合 小規 & 500% ===> 小規優先
				// J-110-0272 不覆審代碼12條件放寬
				// 新臺幣一千萬元以下且為十足擔保授信或經信用保證基金保證成數七成以上，均為不循環動用額度者，免再辦理覆審。倘含循環動用者，抽樣覆審。
				if (retrialService.isNckd_12(Util.trim(meta.getCustId()),
						Util.trim(meta.getDupNo()))) {
					elf412.setElf412_nckdFlag(LrsUtil.NCKD_12_有效額度NTD1000w以下信保七成以上或十足擔保之不循環案件_已於新作增貸後辦理一次覆審);
					elf412.setElf412_nckdDate(new Date());
				} else {
					elf412.setElf412_nckdFlag("");
					elf412.setElf412_nckdDate(CapDate
							.parseDate(CapDate.ZERO_DATE));
				}
			}
			elf412.setElf412_nckdMemo("");
			elf412.setElf412_nextNwDt(CapDate.parseDate(CapDate.ZERO_DATE));
			elf412.setElf412_nextLtDt(CapDate.parseDate(CapDate.ZERO_DATE));

		} else {
			elf412 = new ELF412();
			// ---
			elf412.setElf412_branch(meta.getOwnBrId());
			elf412.setElf412_custId(meta.getCustId());
			elf412.setElf412_dupNo(meta.getDupNo());
			// ---
			elf412.setElf412_rckdLine("A");
			elf412.setElf412_uckdLine("N");
			elf412.setElf412_uckdDt(CapDate.parseDate(CapDate.ZERO_DATE));

			elf412.setElf412_crdType(creditType);
			elf412.setElf412_crdtTbl(creditGrade);
			elf412.setElf412_mowType(mowType);
			elf412.setElf412_mowTbl1(mowGrade);
			elf412.setElf412_mainCust(mLoanPerson);

			elf412.setElf412_fcrdType(fcrdType);
			elf412.setElf412_fcrdArea(fcrdArea);
			elf412.setElf412_fcrdPred(fcrdPred);
			elf412.setElf412_fcrdGrad(fcrdGrad);

			retrialService.gfnCTL_Caculate_ELF412(elf412, null);

			if (CrsUtil.isNOT_null_and_NOTZeroDate(meta.getLastRetrialDate())) {
				elf412.setElf412_llrDate(meta.getLastRetrialDate());
			} else {
				elf412.setElf412_llrDate(CapDate.parseDate(CapDate.ZERO_DATE));
			}
		}

		// J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
		// 2016.11.01 聯貸案主辦行要實地覆審
		// 2017.07.01 實地覆審改為土建融要實地覆審
		if (LrsUtil
				.compareRptVersion(meta.getRptId(), ">=", LrsUtil.V_20170603)) {

			// 此段針對2017.07.01上線後新實地覆審之上傳處理
			if (Util.equals(Util.trim(meta.getRealRpFg()), "Y")) {
				// 實地覆審報告表
				elf412.setElf412_realCkFg("Y");
				if (meta.getRetrialDate() != null) {
					elf412.setElf412_realDt(meta.getRetrialDate());
				}
			} else if (Util.equals(Util.trim(meta.getRealRpFg()), "N")) {
				// 非實地覆審報告表
				if (Util.equals(Util.trim(elf412.getElf412_realCkFg()), "")) {
					// ELF412 realCkFg為空白
					if (Util.equals(Util.trim(meta.getRealCkFg()), "Y")
							|| Util.equals(Util.trim(meta.getRealCkFg()), "N")) {

						// 將覆審報告表實地覆審註記與日期補上傳ELF412
						elf412.setElf412_realCkFg(Util.trim(meta.getRealCkFg()));

						if (Util.equals(Util.trim(meta.getRealCkFg()), "Y")) {
							if (meta.getRealDt() != null) {
								elf412.setElf412_realDt(meta.getRealDt());
							}
						}
					}
				}
			} else {
				// 舊案沒有實地覆審報告註記 PASS
			}
		} else {
			// 此段針對2017.07.01上線後舊實地覆審之上傳處理

			String upMisToBk = Util.trim(lmsService
					.getSysParamDataValue("LMS_J1060145001_UPMISTOBK_ON"));
			if (Util.equals(upMisToBk, "Y")) {
				// J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
				if (Util.equals(Util.trim(meta.getRealRpFg()), "Y")) {
					// 實地覆審報告表
					elf412.setElf412_realCkFg_bk("Y");
					if (meta.getRetrialDate() != null) {
						elf412.setElf412_realDt_bk(meta.getRetrialDate());
					}
				} else if (Util.equals(Util.trim(meta.getRealRpFg()), "N")) {
					// 非實地覆審報告表
					if (Util.equals(Util.trim(elf412.getElf412_realCkFg()), "")) {
						// ELF412 realCkFg為空白
						if (Util.equals(Util.trim(meta.getRealCkFg()), "Y")
								|| Util.equals(Util.trim(meta.getRealCkFg()),
										"N")) {

							// 將覆審報告表實地覆審註記與日期補上傳ELF412
							elf412.setElf412_realCkFg_bk(Util.trim(meta
									.getRealCkFg()));

							if (Util.equals(Util.trim(meta.getRealCkFg()), "Y")) {
								if (meta.getRealDt() != null) {
									elf412.setElf412_realDt_bk(meta.getRealDt());
								}
							}
						}
					}
				} else {
					// 舊案沒有實地覆審報告註記 PASS
				}
			} else {
				if (Util.equals(Util.trim(meta.getRealRpFg()), "Y")) {
					// 實地覆審報告表
					elf412.setElf412_realCkFg("Y");
					if (meta.getRetrialDate() != null) {
						elf412.setElf412_realDt(meta.getRetrialDate());
					}
				} else if (Util.equals(Util.trim(meta.getRealRpFg()), "N")) {
					// 非實地覆審報告表
					if (Util.equals(Util.trim(elf412.getElf412_realCkFg()), "")) {
						// ELF412 realCkFg為空白
						if (Util.equals(Util.trim(meta.getRealCkFg()), "Y")
								|| Util.equals(Util.trim(meta.getRealCkFg()),
										"N")) {

							// 將覆審報告表實地覆審註記與日期補上傳ELF412
							elf412.setElf412_realCkFg(Util.trim(meta
									.getRealCkFg()));

							if (Util.equals(Util.trim(meta.getRealCkFg()), "Y")) {
								if (meta.getRealDt() != null) {
									elf412.setElf412_realDt(meta.getRealDt());
								}
							}
						}
					}
				} else {
					// 舊案沒有實地覆審報告註記 PASS
				}
			}

		}

		elf412.setElf412_lrDate(meta.getRetrialDate());
		elf412.setElf412_updater(Util.trim(meta.getApprover()));
		elf412.setElf412_tmestamp(CapDate.getCurrentTimestamp());
		LrsUtil.elf412_null_to_zeroDate(elf412);

		return elf412;
	}

	@Override
	public List<Map<String, Object>> excludeExpiredCesF101(
			List<Map<String, Object>> src_list) {
		List<Map<String, Object>> r = new ArrayList<Map<String, Object>>();
		int nowYear = Util.parseInt(StringUtils.substring(
				TWNDate.toAD(new Date()), 0, 4));
		for (Map<String, Object> rows : src_list) {
			int year = MapUtils.getIntValue(rows, "year", 0);
			if (year < nowYear - 3) {
				continue;
			}
			r.add(rows);
		}
		return r;
	}

	@Override
	public List<Map<String, Object>> getCesf101(String brNo, String custId,
			String dupNo) {
		List<Map<String, Object>> rows = eloandbBASEService
				.findCESF101A01AJoinCESF101M01A(custId, dupNo, brNo);
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Map<String, String> periodTypeMap = codeTypeService
				.findByCodeType("PeriodType");
		Map<String, String> consoGAAP = codeTypeService
				.findByCodeType("Common_YesNo10");
		Map<String, String> consoIFRS = codeTypeService
				.findByCodeType("IFRSConso");
		Map<String, String> tradeTypeGAAP = codeTypeService
				.findByCodeType("FssTradeType");
		Map<String, String> tradeTypeIFRS = codeTypeService
				.findByCodeType("IFRSTradeType");
		Map<String, String> amtUnitMap = codeTypeService
				.findByCodeType("CurrUnit");
		Map<String, String> gaapFlagMap = new HashMap<String, String>();
		if (true) {
			gaapFlagMap.put("0", "GAAP");
			gaapFlagMap.put("1", "IFRS");
			// J-109-0279_05097_B1001 e-Loan企金簽報書配合徵信IFRS改版與新增EAS會計準則相關修改
			gaapFlagMap.put("2", "EAS");
		}
		for (Map<String, Object> dataMap : rows) {

			Map<String, Object> data = new HashMap<String, Object>();
			data.put("mainId", Util.trim(dataMap.get("MAINID")));
			data.put("year", Util.trim(dataMap.get("YEAR")));
			data.put("sDate", (Date) dataMap.get("SDATE"));
			data.put("eDate", (Date) dataMap.get("EDATE"));
			data.put("curr", Util.trim(dataMap.get("CURR")));

			String gaapFlag = Util.trim(dataMap.get("GAAPFLAG"));
			String conso = Util.trim(dataMap.get("CONSO"));
			String tradeType = Util.trim(dataMap.get("TRADETYPE"));

			if (Util.equals("0", gaapFlag)) {// GAAP
				conso = LMSUtil.getDesc(consoGAAP, conso);
				tradeType = LMSUtil.getDesc(tradeTypeGAAP, tradeType);
			} else if (Util.equals("1", gaapFlag)) {// IFRS
				conso = LMSUtil.getDesc(consoIFRS, conso);
				tradeType = LMSUtil.getDesc(tradeTypeIFRS, tradeType);
			} else if (Util.equals("2", gaapFlag)) {// EAS
				// J-109-0279_05097_B1001 e-Loan企金簽報書配合徵信IFRS改版與新增EAS會計準則相關修改
				conso = LMSUtil.getDesc(consoIFRS, conso);
				tradeType = LMSUtil.getDesc(tradeTypeIFRS, tradeType);
			}
			data.put("gaapFlag", LMSUtil.getDesc(gaapFlagMap, gaapFlag));
			data.put("conso", Util.trim(conso));
			data.put("tradeType", Util.trim(tradeType));

			data.put("approveTime",
					TWNDate.toAD((Date) dataMap.get("APPROVETIME")));
			String periodType = Util.trim(dataMap.get("PERIODTYPE"));
			if (true) {
				if (Util.equals("9", periodType)) {// 其他
					String othType = Util.trim(dataMap.get("OTHTYPE"));
					if (Util.isNotEmpty(othType)) {
						periodType = othType;
					} else {
						periodType = LMSUtil.getDesc(periodTypeMap, periodType);
					}
				} else {
					periodType = LMSUtil.getDesc(periodTypeMap, periodType);
				}
			}
			data.put("periodType", periodType);
			data.put(
					"amtUnit",
					LMSUtil.getDesc(amtUnitMap,
							Util.trim(dataMap.get("AMTUNIT"))));

			list.add(data);
		}

		return list;
	}

	@Override
	public void getFinData(L170M01C l170m01c, String[] f101m01a_mainIds) {
		List<F101M01A> f101m01a_list = new ArrayList<F101M01A>();
		if (f101m01a_mainIds != null && f101m01a_mainIds.length > 0) {
			// 於此將傳入的 f101m01a_mainIds 用 eDate 去排序
			f101m01a_list = fss1010GService.findFssMetaList(f101m01a_mainIds);
		}
		if (true) {
			Map<String, Object> row1 = _get_F101S01A_B(0, f101m01a_list,
					l170m01c);
			if (MapUtils.isNotEmpty(row1)) {
				l170m01c.setFromDate1((Date) row1.get("fromDate"));
				l170m01c.setEndDate1((Date) row1.get("endDate"));
				l170m01c.setAmt11((BigDecimal) row1.get("amt1"));
				l170m01c.setAmt12((BigDecimal) row1.get("amt2"));
				l170m01c.setAmt13((BigDecimal) row1.get("amt3"));
				l170m01c.setRateDate1((Date) row1.get("rateDate"));
				l170m01c.setRate11((BigDecimal) row1.get("rate1"));
				l170m01c.setRate12((BigDecimal) row1.get("rate2"));
				l170m01c.setRate13((BigDecimal) row1.get("rate3"));
				l170m01c.setRate14((BigDecimal) row1.get("rate4"));
				// ===
				// 抓的 3 筆財報，其幣別、金額單位都應相同，以第1筆填入L170M01C
				l170m01c.setCurr(Util.trim(row1.get("curr")));
				l170m01c.setUnit((BigDecimal) row1.get("amtUnit"));
			} else {
				l170m01c.setFromDate1(null);
				l170m01c.setEndDate1(null);
				l170m01c.setAmt11(null);
				l170m01c.setAmt12(null);
				l170m01c.setAmt13(null);
				l170m01c.setRateDate1(null);
				l170m01c.setRate11(null);
				l170m01c.setRate12(null);
				l170m01c.setRate13(null);
				l170m01c.setRate14(null);
				// ===
				// 抓的 3 筆財報，其幣別、金額單位都應相同，以第1筆填入L170M01C
				l170m01c.setCurr(null);
				l170m01c.setUnit(null);
			}
		}

		if (true) {
			Map<String, Object> row2 = _get_F101S01A_B(1, f101m01a_list,
					l170m01c);
			if (MapUtils.isNotEmpty(row2)) {
				l170m01c.setFromDate2((Date) row2.get("fromDate"));
				l170m01c.setEndDate2((Date) row2.get("endDate"));
				l170m01c.setAmt21((BigDecimal) row2.get("amt1"));
				l170m01c.setAmt22((BigDecimal) row2.get("amt2"));
				l170m01c.setAmt23((BigDecimal) row2.get("amt3"));
				l170m01c.setRateDate2((Date) row2.get("rateDate"));
				l170m01c.setRate21((BigDecimal) row2.get("rate1"));
				l170m01c.setRate22((BigDecimal) row2.get("rate2"));
				l170m01c.setRate23((BigDecimal) row2.get("rate3"));
				l170m01c.setRate24((BigDecimal) row2.get("rate4"));
			} else {
				l170m01c.setFromDate2(null);
				l170m01c.setEndDate2(null);
				l170m01c.setAmt21(null);
				l170m01c.setAmt22(null);
				l170m01c.setAmt23(null);
				l170m01c.setRateDate2(null);
				l170m01c.setRate21(null);
				l170m01c.setRate22(null);
				l170m01c.setRate23(null);
				l170m01c.setRate24(null);
			}
		}

		if (true) {
			Map<String, Object> row3 = _get_F101S01A_B(2, f101m01a_list,
					l170m01c);
			if (MapUtils.isNotEmpty(row3)) {
				l170m01c.setFromDate3((Date) row3.get("fromDate"));
				l170m01c.setEndDate3((Date) row3.get("endDate"));
				l170m01c.setAmt31((BigDecimal) row3.get("amt1"));
				l170m01c.setAmt32((BigDecimal) row3.get("amt2"));
				l170m01c.setAmt33((BigDecimal) row3.get("amt3"));
				l170m01c.setRateDate3((Date) row3.get("rateDate"));
				l170m01c.setRate31((BigDecimal) row3.get("rate1"));
				l170m01c.setRate32((BigDecimal) row3.get("rate2"));
				l170m01c.setRate33((BigDecimal) row3.get("rate3"));
				l170m01c.setRate34((BigDecimal) row3.get("rate4"));
			} else {
				l170m01c.setFromDate3(null);
				l170m01c.setEndDate3(null);
				l170m01c.setAmt31(null);
				l170m01c.setAmt32(null);
				l170m01c.setAmt33(null);
				l170m01c.setRateDate3(null);
				l170m01c.setRate31(null);
				l170m01c.setRate32(null);
				l170m01c.setRate33(null);
				l170m01c.setRate34(null);
			}
		}
	}

	private Map<String, Object> _get_F101S01A_B(int idx,
			List<F101M01A> f101m01a_list, L170M01C l170m01c) {
		Map<String, Object> r = null;
		int size = f101m01a_list == null ? 0 : f101m01a_list.size();
		if (idx < size) {
			F101M01A f101m01a = f101m01a_list.get(idx);
			String mainId = f101m01a.getMainId();
			r = new HashMap<String, Object>();
			String curr = "";
			BigDecimal amtUnit = null;
			Date fromDate = null;
			Date endDate = null;
			BigDecimal amt1 = null;
			BigDecimal amt2 = null;
			BigDecimal amt3 = null;
			Date rateDate = null;
			BigDecimal rate1 = null;
			BigDecimal rate2 = null;
			BigDecimal rate3 = null;
			BigDecimal rate4 = null;
			// ---------
			String[] _subNoArr = _subNoArr(f101m01a);
			List<Map<String, Object>> row = eloandbBASEService
					.CESF101S01AByMainIdSubNo(mainId, _subNoArr[0],
							_subNoArr[1], _subNoArr[2]);
			for (Map<String, Object> dataMap : row) {
				curr = Util.trim(dataMap.get("CURR"));
				amtUnit = (BigDecimal) dataMap.get("AMTUNIT");
				fromDate = (Date) dataMap.get("SDATE");
				endDate = (Date) dataMap.get("EDATE");
				rateDate = (Date) dataMap.get("EDATE");

				String subNo = Util.trim(dataMap.get("SUBNO"));
				BigDecimal amt = (BigDecimal) dataMap.get("AMT");

				if (_subNoArr[0].equals(subNo)) {
					// 營業收入
					amt1 = amt;
				} else if (_subNoArr[1].equals(subNo)) {
					// 營業利益
					amt2 = amt;
				} else if (_subNoArr[2].equals(subNo)) {
					// 稅前淨利(淨損)
					amt3 = amt;
				}
			}

			rate1 = _getRatio(mainId, l170m01c.getRatioNo1());
			rate2 = _getRatio(mainId, l170m01c.getRatioNo2());
			rate3 = _getRatio(mainId, l170m01c.getRatioNo3());
			rate4 = _getRatio(mainId, l170m01c.getRatioNo4());
			// ---------
			r.put("curr", curr);
			r.put("amtUnit", amtUnit);
			r.put("fromDate", fromDate);
			r.put("endDate", endDate);
			r.put("amt1", amt1);
			r.put("amt2", amt2);
			r.put("amt3", amt3);
			r.put("rateDate", rateDate);
			r.put("rate1", rate1);
			r.put("rate2", rate2);
			r.put("rate3", rate3);
			r.put("rate4", rate4);
		}
		return r;
	}

	private BigDecimal _getRatio(String mainId, String ratioNo) {
		if (Util.isNotEmpty(Util.trim(ratioNo))) {
			Map<String, Object> dataMap = eloandbBASEService
					.CESF101S01BBymainIdAndratioNo(mainId, ratioNo);
			if (dataMap != null) {
				return (BigDecimal) dataMap.get("RATIO");
			}
		}
		return null;
	}

	private String[] _subNoArr(F101M01A f101m01a) {
		String subNo1 = "50000000";
		String subNo2 = "57000000";
		String subNo3 = "70000000";
		// ===============================================
		String gaapFlag = Util.trim(f101m01a.getGaapFlag());
		String tradeType = Util.trim(f101m01a.getTradeType());
		if (Util.equals("0", gaapFlag)) {// GAAP
			// the same
		} else if (Util.equals("1", gaapFlag)) {// IFRS
			if (Util.equals("M", tradeType)) {
				// '一般行業
				// 'a41000 營業收入
				// 'a61000 營業利益
				// 'a63000 稅前損益
				subNo1 = "41000";
				subNo2 = "61000";
				subNo3 = "63000";
			} else if (Util.equals("I", tradeType)) {
				// '壽險業
				// 'a47000 營業收入
				// 'a61000 營業利益
				// 'a63000 稅前損益
				subNo1 = "47000";
				subNo2 = "61000";
				subNo3 = "63000";
			} else if (Util.equals("P", tradeType)) {
				// '產險業
				// 'a47000 營業收入
				// 'a61000 營業利益
				// 'a63000 稅前損益
				subNo1 = "47000";
				subNo2 = "61000";
				subNo3 = "63000";
			} else if (Util.equals("S", tradeType)) {
				// '證券業
				// 'a44000 營業收入
				// 'a61000 營業利益
				// 'a63000 稅前損益
				subNo1 = "44000";
				subNo2 = "61000";
				subNo3 = "63000";
			} else if (Util.equals("L", tradeType)) {
				// '租賃業
				// 'a43000 營業收入
				// 'a61000 營業利益
				// 'a63000 稅前損益
				subNo1 = "43000";
				subNo2 = "61000";
				subNo3 = "63000";
			} else if (Util.equals("V", tradeType)) {
				// '投資業
				// 'a42000 營業收入
				// 'a61000 營業利益
				// 'a63000 稅前損益
				subNo1 = "42000";
				subNo2 = "61000";
				subNo3 = "63000";
			} else if (Util.equals("H", tradeType)) {
				// '金控業
				// 'a45000 營業收入
				// 'a61000 營業利益
				// 'a63000 稅前損益
				subNo1 = "45000";
				subNo2 = "61000";
				subNo3 = "63000";
			} else if (Util.equals("B", tradeType)) {
				// '銀行業
				// 'a46000 營業收入
				// 'a61000 營業利益
				// 'a63000 稅前損益
				subNo1 = "46000";
				subNo2 = "61000";
				subNo3 = "63000";
			} else {
				subNo1 = "41000";
				subNo2 = "61000";
				subNo3 = "63000";
			}
		} else if (Util.equals("2", gaapFlag)) {// EAS
			// J-109-0279_05097_B1001 e-Loan企金簽報書配合徵信IFRS改版與新增EAS會計準則相關修改
			if (Util.equals("M", tradeType)) {
				// '一般行業
				// 'a41000 營業收入
				// 'a61000 營業利益
				// 'a63000 稅前損益
				subNo1 = "41000";
				subNo2 = "61000";
				subNo3 = "63000";
			} else if (Util.equals("I", tradeType)) {
				// '壽險業
				// 'a47000 營業收入
				// 'a61000 營業利益
				// 'a63000 稅前損益
				subNo1 = "47000";
				subNo2 = "61000";
				subNo3 = "63000";
			} else if (Util.equals("P", tradeType)) {
				// '產險業
				// 'a47000 營業收入
				// 'a61000 營業利益
				// 'a63000 稅前損益
				subNo1 = "47000";
				subNo2 = "61000";
				subNo3 = "63000";
			} else if (Util.equals("S", tradeType)) {
				// '證券業
				// 'a44000 營業收入
				// 'a61000 營業利益
				// 'a63000 稅前損益
				subNo1 = "44000";
				subNo2 = "61000";
				subNo3 = "63000";
			} else if (Util.equals("L", tradeType)) {
				// '租賃業
				// 'a43000 營業收入
				// 'a61000 營業利益
				// 'a63000 稅前損益
				subNo1 = "43000";
				subNo2 = "61000";
				subNo3 = "63000";
			} else if (Util.equals("V", tradeType)) {
				// '投資業
				// 'a42000 營業收入
				// 'a61000 營業利益
				// 'a63000 稅前損益
				subNo1 = "42000";
				subNo2 = "61000";
				subNo3 = "63000";
			} else if (Util.equals("H", tradeType)) {
				// '金控業
				// 'a45000 營業收入
				// 'a61000 營業利益
				// 'a63000 稅前損益
				subNo1 = "45000";
				subNo2 = "61000";
				subNo3 = "63000";
			} else if (Util.equals("B", tradeType)) {
				// '銀行業
				// 'a46000 營業收入
				// 'a61000 營業利益
				// 'a63000 稅前損益
				subNo1 = "46000";
				subNo2 = "61000";
				subNo3 = "63000";
			} else {
				subNo1 = "41000";
				subNo2 = "61000";
				subNo3 = "63000";
			}
		}
		// ===============================================
		String[] arr = new String[3];
		arr[0] = subNo1;
		arr[1] = subNo2;
		arr[2] = subNo3;
		return arr;
	}

	@Override
	public void setFinToDoc(L170M01A meta) {
		L170M01C l170m01c = meta.getL170m01c();
		// ---
		List<String> f101m01a_mainId_list = new ArrayList<String>();
		if (true) {
			String brNo = Util.trim(meta.getOwnBrId());
			String custId = Util.trim(meta.getCustId());
			String dupNo = Util.trim(meta.getDupNo());
			List<Map<String, Object>> list = excludeExpiredCesF101(getCesf101(
					brNo, custId, dupNo));

			if (CollectionUtils.isNotEmpty(list)) {
				Map<String, Object> rows = _chooseF101M01A(list);

				String _gaapFlag = Util.trim(rows.get("gaapFlag"));
				String _conso = Util.trim(rows.get("conso"));
				String _curr = Util.trim(rows.get("curr"));
				String _amtUnit = Util.trim(rows.get("amtUnit"));

				// 同一[財報種類, 是否為合併財報, 幣別, 金額單位]
				f101m01a_mainId_list = _filter_f101m01a(list, _gaapFlag,
						_conso, _curr, _amtUnit);
			}
		}
		String[] f101m01a_mainIds = null;
		if (f101m01a_mainId_list.size() > 0) {
			f101m01a_mainIds = f101m01a_mainId_list
					.toArray(new String[f101m01a_mainId_list.size()]);
		}
		LrsUtil.setDefaultRatio(l170m01c);
		getFinData(l170m01c, f101m01a_mainIds);
		// ---
		retrialService.save(l170m01c);
		retrialService.save(meta);
	}

	private Map<String, Object> _chooseF101M01A(
			List<Map<String, Object>> src_list) {
		// 若同一 eDate, 先 個體, 再合併
		Date _eDate = (Date) src_list.get(0).get("eDate");
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		for (Map<String, Object> rows : src_list) {
			Date eDate = (Date) rows.get("eDate");
			if (_eDate != null && eDate != null
					&& LMSUtil.cmpDate(_eDate, "==", eDate)) {
				list.add(rows);
			}
		}

		if (list.size() == 1) {
			return list.get(0);
		}
		if (true) {
			// GAAP[0-否;1-是],IFRS[A-合併;B-個別;C-個體]
			Map<String, String> consoGAAP = codeTypeService
					.findByCodeType("Common_YesNo10");
			Map<String, String> consoIFRS = codeTypeService
					.findByCodeType("IFRSConso");

			List<String> ord = new ArrayList<String>();
			ord.add(LMSUtil.getDesc(consoIFRS, "B"));
			ord.add(LMSUtil.getDesc(consoIFRS, "C"));
			ord.add(LMSUtil.getDesc(consoGAAP, "0"));

			ord.add(LMSUtil.getDesc(consoIFRS, "A"));
			ord.add(LMSUtil.getDesc(consoGAAP, "1"));

			for (String desc_conso : ord) {
				for (Map<String, Object> rows : list) {
					if (Util.equals(desc_conso, rows.get("conso"))) {
						return rows;
					}
				}
			}
		}
		// 若都對應不到，回傳任1筆
		return list.get(0);
	}

	private List<String> _filter_f101m01a(List<Map<String, Object>> list,
			String _gaapFlag, String _conso, String _curr, String _amtUnit) {
		Map<String, List<String>> map_year_fin = new HashMap<String, List<String>>();
		for (Map<String, Object> rows : list) {
			String mainId = Util.trim(rows.get("mainId"));
			String year = Util.trim(rows.get("year"));
			String gaapFlag = Util.trim(rows.get("gaapFlag"));
			String conso = Util.trim(rows.get("conso"));
			String curr = Util.trim(rows.get("curr"));
			String amtUnit = Util.trim(rows.get("amtUnit"));

			if (Util.equals(_gaapFlag, gaapFlag) && Util.equals(_conso, conso)
					&& Util.equals(_curr, curr)
					&& Util.equals(_amtUnit, amtUnit)) {
				if (!map_year_fin.containsKey(year)) {
					map_year_fin.put(year, new ArrayList<String>());
				}
				map_year_fin.get(year).add(mainId);
			} else {
				continue;
			}
		}

		List<String> f101m01a_mainId_list = new ArrayList<String>();
		if (map_year_fin.size() == 1) {
			for (String year : map_year_fin.keySet()) {
				List<String> mainId_list = map_year_fin.get(year);
				if (CollectionUtils.isNotEmpty(mainId_list)) {
					f101m01a_mainId_list.addAll(mainId_list);
				}

			}
		} else {
			for (String year : map_year_fin.keySet()) {
				List<String> mainId_list = map_year_fin.get(year);
				if (CollectionUtils.isNotEmpty(mainId_list)) {
					f101m01a_mainId_list.add(mainId_list.get(0));
				}
			}
		}

		/*
		 * 若資料為 2014,2013,2012,2011
		 * 
		 * 依 endDate1, endDate2, endDate3 會由小到大排序 所以不能傳入 [2014,2013,2012,2011]
		 * ，這樣會傳回 [2011,2012,2013] 要傳入 [2014,2013,2012] 會傳回 [2012,2013,2014]
		 */
		int maxFinCnt = 3;
		if (f101m01a_mainId_list.size() > maxFinCnt) {
			List<String> r = new ArrayList<String>();
			for (int i = 0; i < maxFinCnt; i++) {
				r.add(f101m01a_mainId_list.get(i));
			}
			return r;
		} else {
			return f101m01a_mainId_list;
		}
	}

	@Override
	public void update_ptMgrId(L170M01A meta, String mgrId) {
		List<L170M01D> existItemList = retrialService
				.findL170M01D_orderBySeq(meta);

		retrialService.gfnSetData_ptLrs(mgrId, existItemList);
		retrialService.saveL170M01D(existItemList);
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<L170M01A> impBySelOrDate(String oids, String s_retrialDate,
			String unitNo, List<String> errMsg) {
		List<L170M01A> list = new ArrayList<L170M01A>();
		String param_oids = Util.trim(oids);
		if (Util.isEmpty(param_oids)) {
			Date retrialDate = CapDate.parseDate(s_retrialDate);
			if (retrialDate == null) {
				// 錯誤的日期
				errMsg.add("錯誤的日期" + s_retrialDate);
			} else {
				ISearch search = retrialService.getMetaSearch();
				search.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
						RetrialDocStatusEnum.區中心_編製中.getCode());
				search.addSearchModeParameters(SearchMode.EQUALS,
						"l170a01a.authUnit", unitNo);
				search.addSearchModeParameters(SearchMode.IS_NULL,
						"deletedTime", null);
				search.addSearchModeParameters(SearchMode.EQUALS,
						"retrialDate", TWNDate.toAD(retrialDate));

				// J-106-0145-006 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
				boolean ctlTypeA = retrialService.chkCtlTypeByBrNo(unitNo,
						LrsUtil.CTLTYPE_主辦覆審);

				boolean ctlTypeB = retrialService.chkCtlTypeByBrNo(unitNo,
						LrsUtil.CTLTYPE_自辦覆審);

				boolean ctlTypeC = retrialService.chkCtlTypeByBrNo(unitNo,
						LrsUtil.CTLTYPE_價金履約);

				if (ctlTypeA && ctlTypeB && ctlTypeC) {
					// 國外部
				} else if (!ctlTypeA && ctlTypeB) {
					// 一般分行 只有自辦覆審
					search.addSearchModeParameters(SearchMode.EQUALS,
							"ctlType", LrsUtil.CTLTYPE_自辦覆審);
				} else if (ctlTypeA || ctlTypeB) {
					// 營運中心
					// search.addSearchModeParameters(SearchMode.EQUALS,
					// "ctlType", ctlTypeA ? LrsUtil.CTLTYPE_主辦覆審
					// : LrsUtil.CTLTYPE_自辦覆審);

					search.addSearchModeParameters(SearchMode.IN, "ctlType",
							new String[] { LrsUtil.CTLTYPE_主辦覆審,
									LrsUtil.CTLTYPE_價金履約 });
				} else {
					// ??
					// search.addSearchModeParameters(SearchMode.EQUALS,
					// "ctlType", LrsUtil.CTLTYPE_主辦覆審);
					search.addSearchModeParameters(SearchMode.IN, "ctlType",
							new String[] { LrsUtil.CTLTYPE_主辦覆審,
									LrsUtil.CTLTYPE_價金履約 });
				}

				search.setMaxResults(Integer.MAX_VALUE);
				// ---
				Page<?> src_page = retrialService.findPage(L170M01A.class,
						search);
				list = (List<L170M01A>) src_page.getContent();
			}
		} else {
			String[] oid_arr = param_oids.split("\\|");
			// ok, 依 oid 去查詢
			for (String oid : oid_arr) {
				L170M01A l170m01a = retrialService.findL170M01A_oid(oid);
				if (l170m01a != null) {
					list.add(l170m01a);
				}
			}
		}
		if (errMsg.size() == 0 && CollectionUtils.isEmpty(list)) {
			errMsg.add("無覆審日期 " + s_retrialDate + " 之覆審報告表");
		}
		return list;
	}

	@Override
	public void replaceWithBef(L170M01A meta, L170M01A befMeta) {
		if (true) {
			// 更新L170M01F.condition
			L170M01F l170m01f = meta.getL170m01f();
			L170M01F befl170m01f = befMeta.getL170m01f();

			if (befl170m01f != null && l170m01f != null) {
				l170m01f.setCondition(befl170m01f.getCondition());
				// ---
				retrialService.save(l170m01f);
			}
		}
		if (true) {
			Date nowTS = new Date();
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			// 更新L170M01D.chkText
			List<L170M01D> l170m01d_list = retrialService
					.findL170M01D_orderBySeq(meta);
			Map<String, L170M01D> m_bef = retrialService
					.lrs_toMap_keyAs_itemNo(retrialService
							.findL170M01D_orderBySeq(befMeta));
			for (L170M01D l170m01d : l170m01d_list) {

				// J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
				if (Util.equals(LrsUtil.Z_電腦建檔資料, l170m01d.getItemType())
						|| Util.equals(LrsUtil.Y_履約條件, l170m01d.getItemType())) {
					// 不替代 Z_電腦建檔
					continue;
				}

				if (m_bef.containsKey(l170m01d.getItemNo())) {
					L170M01D befL170M01D = m_bef.get(l170m01d.getItemNo());
					if (befL170M01D != null) {
						l170m01d.setChkText(Util.trim(befL170M01D.getChkText()));
						l170m01d.setUpdater(user.getUserId());
						l170m01d.setUpdateTime(nowTS);
					}
				}
			}
			retrialService.saveL170M01D(l170m01d_list);
		}

		if (true) {
			// 更新L170M01B.majorMemo
			Map<String, String> key_majorMemoMap = new HashMap<String, String>();
			if (true) {
				List<L170M01B> befl170m01b_list = retrialService
						.findL170M01B_orderBy(befMeta);
				for (L170M01B befl170m01b : befl170m01b_list) {
					String key = _replaceWithBef_B_key(befl170m01b);
					if (Util.isEmpty(key)) {
						continue;
					}
					key_majorMemoMap.put(key,
							Util.trim(befl170m01b.getMajorMemo()));
				}
			}

			if (MapUtils.isNotEmpty(key_majorMemoMap)) {
				List<L170M01B> l170m01b_list = retrialService
						.findL170M01B_orderBy(meta);
				for (L170M01B l170m01b : l170m01b_list) {
					String key = _replaceWithBef_B_key(l170m01b);
					if (Util.isEmpty(key)) {
						continue;
					}
					if (key_majorMemoMap.containsKey(key)) {
						l170m01b.setMajorMemo(Util.trim(key_majorMemoMap
								.get(key)));
						// ===
						retrialService.save(l170m01b);
					}
				}
			}
		}
		retrialService.save(meta);
	}

	/**
	 * 額度序號+餘額幣別+科目代碼+科目名稱
	 */
	private String _replaceWithBef_B_key(L170M01B l170m01b) {
		String subjectName = Util.trim(l170m01b.getSubject());
		// 全形空白
		subjectName = subjectName.replaceAll("　", "");

		if (Util.isEmpty(subjectName)) {
			return "";
		}
		List<String> r = new ArrayList<String>();
		r.add(Util.trim(l170m01b.getCntrNo()));
		r.add(Util.trim(l170m01b.getBalCurr()));
		r.add(Util.trim(l170m01b.getLoanTP()));
		r.add(subjectName);
		return StringUtils.join(r, "-");
	}

	@Override
	public List<L170M01A> save_basicL170M01A(L180M01A l180m01a) {
		List<L170M01A> r = new ArrayList<L170M01A>();

		List<L180M01B> l180m01b_list = retrialService
				.findL180M01BDefaultOrder(l180m01a.getMainId());
		for (L180M01B l180m01b : l180m01b_list) {
			if (Util.equals(LrsUtil.NCKD_8_本次暫不覆審, l180m01b.getNewNCkdFlag())) {
				// '本次暫不覆審 不要傳送(99.9.3 郭慧珠提)
				continue;
			}

			List<String> failMsgList = new ArrayList<String>();
			L170M01A l170m01a = retrialService.findL170M01A(l180m01b);
			if (l170m01a == null) {
				l170m01a = _basicL170M01A(l180m01a.getBranchId(),
						l180m01b.getCustId(), l180m01b.getDupNo(),
						Util.trim(l180m01b.getElfCName()), failMsgList,
						Util.trim(l180m01b.getCtlType()));

				if (l170m01a != null) {
					// when no error
					l170m01a.setRetrialDate(l180m01a.getDefaultCTLDate());
					l170m01a.setProjectSeq(l180m01b.getProjectSeq());
					l170m01a.setProjectNo(l180m01b.getProjectNo());
					if (Util.isNotEmpty(Util.trim(l180m01b.getNewNCkdFlag()))
							&& Util.notEquals("0", l180m01b.getNewNCkdFlag())) {
						l170m01a.setNCkdFlag(l180m01b.getNewNCkdFlag());
					}
					l170m01a.setPid(l180m01b.getMainId());

					r.add(l170m01a);// XXX 參考 ALMS22003 :
									// gfnSendCTLListToBranch("1")
				}
			}
		}
		retrialService.saveL170M01A(r);

		return r;
	}

	// J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
	private void _impData_10_RealCkData(L170M01A meta,
			Map<String, String> showItem) {

		String custId = Util.trim(meta.getCustId());
		String dupNo = Util.trim(meta.getDupNo());
		String brNo = Util.trim(meta.getOwnBrId());
		ELF412 elf412 = misELF412Service.findByPk(meta.getOwnBrId(), custId,
				dupNo);
		if (elf412 != null) {

			meta.setRealCkFg(Util.equals(
					Util.trim(elf412.getElf412_realCkFg()), "") ? "X" : Util
					.trim(elf412.getElf412_realCkFg()));

			if (Util.equals(Util.trim(meta.getRealCkFg()), "Y")) {
				if (CrsUtil.isNOT_null_and_NOTZeroDate(elf412
						.getElf412_realDt())) {
					meta.setRealDt(elf412.getElf412_realDt());
				} else {
					meta.setRealDt(null);
				}
			} else {
				meta.setRealDt(null);
			}

		} else {
			meta.setRealCkFg("X");
			meta.setRealDt(null);
		}

		// 取得ALOAN實際註記與實地覆審基準日
		String aRealDt = "";
		String aRalCkFg = "";
		Map<String, String> minDataMap = retrialService.gfnGetAloanRealDt2(
				custId, dupNo, brNo);
		if (minDataMap != null && !minDataMap.isEmpty()) {
			aRealDt = MapUtils.getString(minDataMap, "realDt", "");
			aRalCkFg = MapUtils.getString(minDataMap, "realCkFg", "");
		}

		// 比對ELF412 與ALOAN
		if (Util.equals(Util.trim(meta.getRealCkFg()), "X")) {
			meta.setRealCkFg(aRalCkFg);
		}

		if (Util.equals(Util.trim(meta.getRealCkFg()), "Y")) {
			if (meta.getRealDt() == null) {
				if (Util.notEquals(aRealDt, "")) {
					meta.setRealDt(CapDate.parseDate(aRealDt));
				}
			}
		}

		showItem.put("realCkFg", meta.getRealCkFg());
		showItem.put("realDt", Util.trim(TWNDate.toAD(meta.getRealDt())));

	}

	/**
	 * 修改覆審報告表格式為最新版本 J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
	 * 
	 * J-106-0123-001 Web e-Loan國內企金覆審增加覆審項目「立約當日是否依規定查詢銀行法及金控法44條利害關係人之資料後再行簽約」
	 * 
	 */
	@Override
	public void update_l170m01a_rpid(JSONObject rq) throws CapException {
		// J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
		List<String> errMsg = new ArrayList<String>();

		List<L170M01A> list = impBySelOrDate(Util.trim(rq.optString("oids")),
				Util.trim(rq.optString("retrialDate")),
				Util.trim(rq.optString("unitNo")), errMsg);

		if (CollectionUtils.isEmpty(errMsg)) {

			// String latestRetrialItemVer = "";

			for (L170M01A l170m01a : list) {

				// 因為可能ctlType不同 所以每次都要重取latestRetrialItemVer
				String latestRetrialItemVer = "";
				
				// J-106-0145-006 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
				String ctlType = Util.trim(l170m01a.getCtlType());
				if (Util.equals(ctlType, "")) {
					ctlType = LrsUtil.CTLTYPE_主辦覆審;
				}

				if (Util.equals(latestRetrialItemVer, "")) {
					latestRetrialItemVer = retrialService
							.getLatestRetrialItemVer(l170m01a);
				}
								
				// J-106-0145-006 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
				if (Util.equals(ctlType, LrsUtil.CTLTYPE_自辦覆審)
						|| Util.equals(ctlType, LrsUtil.CTLTYPE_價金履約)) {
					List<L170M01D> saveList = new ArrayList<L170M01D>();
					List<L170M01D> delList = new ArrayList<L170M01D>();

					retrialService.importRetrialItemToL170M01D(l170m01a,
							saveList, delList);

					if (CollectionUtils.isNotEmpty(saveList)) {
						for (L170M01D l170m01d : saveList) {
							retrialService.save(l170m01d);
						}
					}

					if (CollectionUtils.isNotEmpty(delList)) {
						for (L170M01D l170m01d : delList) {
							retrialService.del(l170m01d);
						}
					}

					l170m01a.setRptId(latestRetrialItemVer);
					l170m01adao.save(l170m01a);

				} else {

					if (Util.notEquals(latestRetrialItemVer, "")) {

						// J-108-0128_05097_B1001 Web e-Loan企金授信覆審系統修改覆審報告表內容。
						// J-111-0031 更動覆審系統內以下九式覆審報告表之文字內容。
                        // J-111-0405 更動覆審系統內以下15式覆審報告表之文字內容。
						if (Util.equals(latestRetrialItemVer,
								LrsUtil.V_20170603)
								|| Util.equals(latestRetrialItemVer,
										LrsUtil.V_20190201)
								|| Util.equals(latestRetrialItemVer,
										LrsUtil.V_20190701)
								|| Util.equals(latestRetrialItemVer, LrsUtil.V_20220401)
                                || Util.equals(latestRetrialItemVer, LrsUtil.V_20220901)
                                // J-112-0280  新增企金覆審報告之「附表」項下E-LOAN系統建檔增列3.授信管理系統-「風險權數試算明細」。 
                                || Util.equals(latestRetrialItemVer, LrsUtil.V_20230706)
								// J-113-0066 企金覆審，覆審內容之覆審項目新增及修正說明文句
								|| Util.equals(latestRetrialItemVer,
										LrsUtil.V_20240401)
								// J-113-0204  新增及修正說明文句
								|| Util.equals(latestRetrialItemVer,
										LrsUtil.V_20240601)
                             ) {
							// J-106-0145-001 Web e-Loan
							// 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行

							List<L170M01D> l170m01ds = retrialService
									.findL170M01D_orderBySeq(l170m01a);

							Map<String, String> LRS_ITEM_DESC_MAP = retrialService
									.getLRS_ITEM_DESC_MAP();

							List<L170M01D> saveList = new ArrayList<L170M01D>();
							List<L170M01D> delList = new ArrayList<L170M01D>();

							retrialService.importRetrialItemToL170M01D(
									l170m01a, saveList, delList);

							if (CollectionUtils.isNotEmpty(saveList)) {
								for (L170M01D l170m01d : saveList) {
									retrialService.save(l170m01d);
								}
							}

							if (CollectionUtils.isNotEmpty(delList)) {
								for (L170M01D l170m01d : delList) {
									retrialService.del(l170m01d);
								}
							}

							l170m01a.setRptId(latestRetrialItemVer);
							l170m01adao.save(l170m01a);

						} else if (Util.equals(latestRetrialItemVer,
								LrsUtil.V_20170519)) {
							// J-106-0123-001 Web
							// e-Loan國內企金覆審增加覆審項目「立約當日是否依規定查詢銀行法及金控法44條利害關係人之資料後再行簽約」

							List<L170M01D> l170m01ds = retrialService
									.findL170M01D_orderBySeq(l170m01a);

							Map<String, String> LRS_ITEM_DESC_MAP = retrialService
									.getLRS_ITEM_DESC_MAP();

							// 新增
							String[] Y_ALL = { "N028" };
							for (String itemNo : Y_ALL) {

								Integer itemSeq = retrialService
										.getItemSeqByItemNo(
												latestRetrialItemVer, itemNo);

								L170M01D l170m01d = l170m01ddao.findByIndex02(
										l170m01a.getMainId(),
										l170m01a.getCustId(),
										l170m01a.getDupNo(), itemNo);

								if (l170m01d == null) {
									// 不存在就新增
									l170m01d = new L170M01D();
									l170m01d.setMainId(l170m01a.getMainId());
									l170m01d.setCustId(l170m01a.getCustId());
									l170m01d.setDupNo(l170m01a.getDupNo());
									l170m01d.setCreator(l170m01a.getCreator());
									l170m01d.setCreateTime(CapDate
											.getCurrentTimestamp());
									l170m01d.setUpdater(l170m01a.getUpdater());
									l170m01d.setUpdateTime(CapDate
											.getCurrentTimestamp());
									l170m01d.setItemType(LrsUtil.C_其他);
									l170m01d.setItemNo(itemNo);
									l170m01d.setItemSeq(itemSeq);
									l170m01d.setItemContent(Util
											.trim(LRS_ITEM_DESC_MAP.get(itemNo)));
									l170m01ddao.save(l170m01d);
								}

							}

							l170m01a.setRptId(latestRetrialItemVer);
							l170m01adao.save(l170m01a);

						} else if (Util.equals(latestRetrialItemVer,
								LrsUtil.V_20161101)) {

							// 2016-11-01
							/*
							 * 修改內容： V一.依據「授信覆審實施要點」第五條規定，
							 * 修改國內各營業單位第一組至第五組企業戶授信額度標準與關係企業授信額度合計標準
							 * ，及大型企業模型評等增加含境外與亞太、境外船舶航空器評等模型增加含亞太。
							 * V二.依據「授信覆審實施要點
							 * 」第七條規定，新增對本行主辦之企金戶聯貸案件(不含聯行間參貸案件)應每年辦理一次實地覆審， V三
							 * .未依規定期限或逾覆審期限之實地覆審案件，請於「未於規定期限覆審之企金案件控管表」列出。
							 * LMS180R19
							 * V四.免辦理覆審之案件請新增「企業戶之外勞保證中長期授信案件，除於新作後辦理一次覆審外
							 * ，免再辦理覆審 ，但嗣後如有增額、減額、變更條件或續約時，仍應依本要點第五條規定辦理一次覆審。
							 * 」。對未依規定期限覆審或逾覆審期限之案件
							 * ，請於「未於規定期限覆審之企金案件控管表」列出。另以十成定存擔保之免覆審案件 ，請增加含存款設質。
							 * V五.新增「授信案件實地覆審報告表（企金戶）」（附件三）。
							 * 六.請於「授信案件覆審報告表（企金戶）」（附件四）修改如下：
							 * （一）請刪除徵信事項第5項「個人授信額度達規定應徵提綜合所得稅繳款書及扣繳憑單者
							 * ，是否依規辦理？」。
							 * （二）債權確保第9項請改為「計收利率(或費率)是否符合本行訂價政策相關規範？」
							 * （三）債權確保第12項請改為
							 * 「中長期放款（含土建融之案件）之申貸計劃及其自籌款是否照預定進度執行？」
							 * （四）債權確保第17項請改為
							 * 「借戶是否依照約定條件履行？（核定條件若有「應檢視事項」或「承諾事項」須另勾選附
							 */

							if (Util.notEquals(latestRetrialItemVer,
									l170m01a.getRptId())) {
								List<L170M01D> l170m01ds = retrialService
										.findL170M01D_orderBySeq(l170m01a);

								Map<String, String> LRS_ITEM_DESC_MAP = retrialService
										.getLRS_ITEM_DESC_MAP();
								for (L170M01D l170m01d : l170m01ds) {

									if (Util.equals(l170m01d.getItemNo(),
											LrsUtil.N005)) {
										// 刪除N005
										l170m01ddao.delete(l170m01d);
									} else if (Util.equals(
											l170m01d.getItemNo(), LrsUtil.N011)
											|| Util.equals(
													l170m01d.getItemNo(),
													LrsUtil.N026)) {
										// 更新ITEMCONTENT
										l170m01d.setItemContent(Util
												.trim(LRS_ITEM_DESC_MAP
														.get(l170m01d
																.getItemNo())));
										l170m01d.setItemSeq(retrialService
												.getItemSeqByItemNo(
														latestRetrialItemVer,
														l170m01d.getItemNo()));
										l170m01ddao.save(l170m01d);
									} else {
										l170m01d.setItemSeq(retrialService
												.getItemSeqByItemNo(
														latestRetrialItemVer,
														l170m01d.getItemNo()));
										l170m01ddao.save(l170m01d);
									}

								}

								// 新增
								String[] Y_ALL = { "YA00", "YA1A", "YA11",
										"YA12", "YA13", "YB00", "YB1A", "YB11",
										"YB12", "YB13" };
								for (String itemNo : Y_ALL) {

									Integer itemSeq = retrialService
											.getItemSeqByItemNo(
													latestRetrialItemVer,
													itemNo);

									L170M01D l170m01d = l170m01ddao
											.findByIndex02(
													l170m01a.getMainId(),
													l170m01a.getCustId(),
													l170m01a.getDupNo(), itemNo);

									if (l170m01d == null) {
										// 不存在就新增
										l170m01d = new L170M01D();
										l170m01d.setMainId(l170m01a.getMainId());
										l170m01d.setCustId(l170m01a.getCustId());
										l170m01d.setDupNo(l170m01a.getDupNo());
										l170m01d.setCreator(l170m01a
												.getCreator());
										l170m01d.setCreateTime(CapDate
												.getCurrentTimestamp());
										l170m01d.setUpdater(l170m01a
												.getUpdater());
										l170m01d.setUpdateTime(CapDate
												.getCurrentTimestamp());
										l170m01d.setItemType(LrsUtil.Y_履約條件);
										l170m01d.setItemNo(itemNo);
										l170m01d.setItemSeq(itemSeq);
										l170m01d.setItemContent(Util
												.trim(LRS_ITEM_DESC_MAP
														.get(itemNo)));
										l170m01ddao.save(l170m01d);
									}

								}

								l170m01a.setRptId(latestRetrialItemVer);
								l170m01adao.save(l170m01a);
							}
						} else {
							// 目前不支援更新為其他版本

						}

					}
				} // LrsUtil.CTLTYPE_主辦覆審

			}
		} else {
			throw new CapMessageException(StringUtils.join(errMsg, "，"),
					getClass());
		}
	}

	// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
	private ELF412B gfnDB2UpELF412B(L170M01A meta) {
		String creditType = "";
		String creditGrade = "";

		String mowType = "";
		String mowGrade = "";

		String fcrdType = "";
		String fcrdArea = "";
		String fcrdPred = "";
		String fcrdGrad = "";

		if (true) {
			Map<String, List<L170M01E>> m_l170m01e = retrialService
					.findL170M01E_type(retrialService.findL170M01E(meta));
			L170M01E l170m01e_C = LrsUtil.firstElm(
					m_l170m01e.get(LrsUtil.M01E_FLAG_C), "T");
			L170M01E l170m01e_M = LrsUtil.firstElm(
					m_l170m01e.get(LrsUtil.M01E_FLAG_M), "T");
			L170M01E l170m01e_F = LrsUtil.firstElm(
					m_l170m01e.get(LrsUtil.M01E_FLAG_F), "T");
			Integer fcrdScore = null;

			if (l170m01e_C != null) {
				creditType = Util.trim(l170m01e_C.getCrdType());
				creditGrade = Util.trim(l170m01e_C.getGrade());
			}

			if (l170m01e_M != null) {
				mowType = Util.trim(l170m01e_M.getCrdType());
				mowGrade = Util.trim(l170m01e_M.getGrade());
			}

			if (l170m01e_F != null) {
				fcrdType = Util.trim(l170m01e_F.getCrdType());
				fcrdArea = Util.trim(l170m01e_F.getFcrdArea());
				fcrdPred = Util.trim(l170m01e_F.getFcrdPred());
				fcrdGrad = Util.trim(l170m01e_F.getGrade());
				fcrdScore = l170m01e_F.getScore();
			}
			// ======
			if (Util.equals(UtilConstants.crdType.未評等, creditType)
					|| Util.isEmpty(creditType)) {
				creditType = "";
				creditGrade = "";
			}
			if (Util.equals(UtilConstants.Casedoc.CrdType2.免辦, mowType)
					|| Util.isEmpty(mowType)) {
				mowType = "";
				mowGrade = "";
			}
			if (Util.isEmpty(fcrdGrad)
					|| (fcrdScore != null && fcrdScore == 90)) {
				fcrdType = "";
				fcrdArea = "";
				fcrdPred = "";
				fcrdGrad = "";
			}
			// ======
			// 把 2 碼的 creditType,mowType 轉成 1碼的格式，來上傳
			if (Util.isNotEmpty(creditType)) {
				creditType = LMSUtil.getDesc(
						retrialService.get_lrs_CrdtType_2to1(), creditType);
			}
			if (Util.isNotEmpty(mowType)) {
				mowType = LMSUtil.getDesc(
						retrialService.get_lrs_MowType_2to1(), mowType);
			}
		}
		String mLoanPerson = "Y".equals(meta.getMLoanPerson()) ? "Y" : "N";

		ELF412B elf412b = misELF412BService.findByPk(meta.getOwnBrId(),
				meta.getCustId(), meta.getDupNo());
		if (elf412b != null) {

			elf412b.setElf412b_rckdLine("A");

			if (Util.notEquals(elf412b.getElf412b_newAdd(), "")) {
				if (Util.notEquals(elf412b.getElf412b_newRptId(), "")) {
					elf412b.setElf412b_oldRptId(Util.trim(elf412b
							.getElf412b_newRptId()));
					elf412b.setElf412b_oldRptDt(elf412b.getElf412b_newRptDt());
				}
			}

			elf412b.setElf412b_newAdd("");
			elf412b.setElf412b_newDate("");
			elf412b.setElf412b_newRptId("");
			elf412b.setElf412b_newRptDt(null);

			if (Util.isEmpty(Util.trim(elf412b.getElf412b_uckdLine()))) {
				elf412b.setElf412b_uckdLine("N");
			}
			if (elf412b.getElf412b_uckdDt() == null) {
				elf412b.setElf412b_uckdDt(CapDate.parseDate(CapDate.ZERO_DATE));
			}

			elf412b.setElf412b_crdType(creditType);
			elf412b.setElf412b_crdtTbl(creditGrade);
			elf412b.setElf412b_mowType(mowType);
			elf412b.setElf412b_mowTbl1(mowGrade);

			elf412b.setElf412b_fcrdType(fcrdType);
			elf412b.setElf412b_fcrdArea(fcrdArea);
			elf412b.setElf412b_fcrdPred(fcrdPred);
			elf412b.setElf412b_fcrdGrad(fcrdGrad);

			elf412b.setElf412b_mainCust(mLoanPerson);
			retrialService.gfnCTL_Caculate_ELF412B(elf412b);

			if (CrsUtil.isNOT_null_and_NOTZeroDate(meta.getLastRetrialDate())) {
				elf412b.setElf412b_llrDate(meta.getLastRetrialDate());
			} else {
				if (CrsUtil.isNOT_null_and_NOTZeroDate(elf412b
						.getElf412b_lrDate())) {
					elf412b.setElf412b_llrDate(elf412b.getElf412b_lrDate());
				} else {
					elf412b.setElf412b_llrDate(CapDate
							.parseDate(CapDate.ZERO_DATE));
				}
			}

			elf412b.setElf412b_nckdFlag("");
			elf412b.setElf412b_nckdDate(CapDate.parseDate(CapDate.ZERO_DATE));
			elf412b.setElf412b_nckdMemo("");
			elf412b.setElf412b_nextNwDt(CapDate.parseDate(CapDate.ZERO_DATE));
			elf412b.setElf412b_nextLtDt(CapDate.parseDate(CapDate.ZERO_DATE));

		} else {
			elf412b = new ELF412B();
			// ---
			elf412b.setElf412b_branch(meta.getOwnBrId());
			elf412b.setElf412b_custId(meta.getCustId());
			elf412b.setElf412b_dupNo(meta.getDupNo());
			// ---
			elf412b.setElf412b_rckdLine("A");
			elf412b.setElf412b_uckdLine("N");
			elf412b.setElf412b_uckdDt(CapDate.parseDate(CapDate.ZERO_DATE));

			elf412b.setElf412b_crdType(creditType);
			elf412b.setElf412b_crdtTbl(creditGrade);
			elf412b.setElf412b_mowType(mowType);
			elf412b.setElf412b_mowTbl1(mowGrade);
			elf412b.setElf412b_mainCust(mLoanPerson);

			elf412b.setElf412b_fcrdType(fcrdType);
			elf412b.setElf412b_fcrdArea(fcrdArea);
			elf412b.setElf412b_fcrdPred(fcrdPred);
			elf412b.setElf412b_fcrdGrad(fcrdGrad);

			retrialService.gfnCTL_Caculate_ELF412B(elf412b);

			if (CrsUtil.isNOT_null_and_NOTZeroDate(meta.getLastRetrialDate())) {
				elf412b.setElf412b_llrDate(meta.getLastRetrialDate());
			} else {
				elf412b.setElf412b_llrDate(CapDate.parseDate(CapDate.ZERO_DATE));
			}

			// 抓本次覆審的額度序號

			List<L170M01B> l170m01b_list = retrialService
					.findL170M01B_orderBy(meta);
			if (!CollectionUtils.isEmpty(l170m01b_list)) {
				for (L170M01B l170m01b : l170m01b_list) {
					String custId = l170m01b.getCustId();
					String dupNo = l170m01b.getDupNo();
					String cntrNo = l170m01b.getCntrNo();
					Map<String, Object> elf447n = misELF447nService
							.findByMaxChkDate(custId, dupNo, cntrNo);

					if (elf447n != null && !elf447n.isEmpty()) {
						String caseLvl = MapUtils.getString(elf447n,
								"ELF447N_CASELEVEL", "");
						if (retrialService.chkNeedRealReview(custId, dupNo,
								caseLvl)) {

							String unid = MapUtils.getString(elf447n,
									"ELF447N_UNID", "");
							Date chkDate = Util.parseDate(MapUtils.getString(
									elf447n, "ELF447_CHKDATE", ""));

							elf412b.setElf412b_oldRptId(Util.trim(unid));
							elf412b.setElf412b_oldRptDt(chkDate);
							break;
						}

					}

				}

			}

		}

		elf412b.setElf412b_lrDate(meta.getRetrialDate());
		elf412b.setElf412b_updater(Util.trim(meta.getApprover()));
		elf412b.setElf412b_tmestamp(CapDate.getCurrentTimestamp());
		LrsUtil.elf412b_null_to_zeroDate(elf412b);

		return elf412b;
	}

	/**
	 * J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
	 * 
	 * 常董會實地覆審用
	 * 
	 * @param meta
	 * @param elf412b
	 * @param elf494
	 * @param elf495_list
	 */
	private void gfnDB2UpELF494_ELF495(L170M01A meta, ELF412B elf412b,
			ELF494 elf494, List<ELF495> elf495_list) {
		if (true) {
			elf494.setElf494_branch(meta.getOwnBrId());
			elf494.setElf494_custId(meta.getCustId());
			elf494.setElf494_dupNo(meta.getDupNo());
			elf494.setElf494_dbuObu(Util.equals("4", meta.getTypCd()) ? "OBU"
					: "DBU");
			elf494.setElf494_rptDocId(meta.getMainId());
			// J-106-0145-002 Web e-Loan 國內企金授信管理系統修改實地覆審相關功能
			elf494.setElf494_ctlType(Util.equals(Util.trim(meta.getCtlType()),
					"") ? LrsUtil.CTLTYPE_主辦覆審 : Util.trim(meta.getCtlType()));

			if (CrsUtil.isNOT_null_and_NOTZeroDate(meta.getLastRetrialDate())) {
				elf494.setElf494_llrDate(meta.getLastRetrialDate());
			} else {
				elf494.setElf494_llrDate(CapDate.parseDate(CapDate.ZERO_DATE));
			}
			if (CrsUtil.isNOT_null_and_NOTZeroDate(meta.getRetrialDate())) {
				elf494.setElf494_lrDate(meta.getRetrialDate());
			} else {
				elf494.setElf494_lrDate(CapDate.parseDate(CapDate.ZERO_DATE));
			}
			String projectNo = Util.trim(meta.getProjectNo());
			if (Util.isNotEmpty(projectNo)) {
				String yyyy = StringUtils.substring(projectNo, 0, 4);
				String extractProjectNo = LrsUtil.extractProjectNo(projectNo);

				elf494.setElf494_projNo(projectNo);
				elf494.setElf494_dataDtY(String.valueOf(Util.parseInt(yyyy) - 1911));
				elf494.setElf494_batchNo(StringUtils.substring(
						extractProjectNo, 0, 3));
				elf494.setElf494_sno(StringUtils.substring(extractProjectNo, 4,
						7));
			} else {
				elf494.setElf494_projNo("");
				elf494.setElf494_dataDtY("");
				elf494.setElf494_batchNo("");
				elf494.setElf494_sno("");
			}

			elf494.setElf494_mainCust(elf412b.getElf412b_mainCust());
			elf494.setElf494_crdType(elf412b.getElf412b_crdType());
			elf494.setElf494_crdtTbl(elf412b.getElf412b_crdtTbl());
			elf494.setElf494_mowType(elf412b.getElf412b_mowType());
			elf494.setElf494_mowTbl1(elf412b.getElf412b_mowTbl1());
			elf494.setElf494_nckdFlag(meta.getNCkdFlag());

			if (true) {
				L170M01F l170m01f = meta.getL170m01f();
				elf494.setElf494_retial(l170m01f.getRetialComm());
				elf494.setElf494_conFlag(l170m01f.getConFlag());
				if (l170m01f.getUpDate() != null) {
					elf494.setElf494_upDate(l170m01f.getUpDate());
				} else {
					elf494.setElf494_upDate(CapDate.getCurrentTimestamp());
				}
			}
			if (true) {
				String elf494_managerId = "";
				String elf494_bossId = "";
				String elf494_apprId = "";
				if (true) {
					List<L170M01G> l170m01g_list = retrialService
							.findL170M01G_l170m01a(meta);
					List<L170M01G> l170m01g_L1 = retrialService
							.findL170M01G_byBranchTypeStaffJob(l170m01g_list,
									lrsConstants.BRANCHTYPE.覆審單位, "L1");
					List<L170M01G> l170m01g_L4 = retrialService
							.findL170M01G_byBranchTypeStaffJob(l170m01g_list,
									lrsConstants.BRANCHTYPE.覆審單位, "L4");
					List<L170M01G> l170m01g_L5 = retrialService
							.findL170M01G_byBranchTypeStaffJob(l170m01g_list,
									lrsConstants.BRANCHTYPE.覆審單位, "L5");

					if (CollectionUtils.isNotEmpty(l170m01g_L1)) {
						elf494_apprId = l170m01g_L1.get(0).getStaffNo();
					}
					if (CollectionUtils.isNotEmpty(l170m01g_L4)) {
						elf494_bossId = l170m01g_L4.get(0).getStaffNo();
					}
					if (CollectionUtils.isNotEmpty(l170m01g_L5)) {
						elf494_managerId = l170m01g_L5.get(0).getStaffNo();
					}
				}

				elf494.setElf494_managerId(elf494_managerId);
				elf494.setElf494_bossId(elf494_bossId);
				elf494.setElf494_apprId(elf494_apprId);
			}

			elf494.setElf494_updater(elf412b.getElf412b_updater());
			elf494.setElf494_tmestamp(elf412b.getElf412b_tmestamp());

			// J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
			elf494.setElf494_realRpFg(Util.trim(meta.getRealRpFg()));
			if (meta.getRealDt() != null) {
				elf494.setElf494_realRpDt(meta.getRealDt());
			}

			if (true) {
				/*
				 * 依 itemSeq 來排序 ------ Case 9: tmp2 = "計收之利率是否符合本行定價政策相關規範？"
				 * tmp3_name = "result_"+Cstr(26) tmp4_name =
				 * "unusual_"+Cstr(26) ------ 以上程式表示：第9項取得 result_26 的值
				 */
				List<String> chkItemList = new ArrayList<String>();
				Properties prop_lms1700m01 = MessageBundleScriptCreator
						.getComponentResource(LMS1700M01Page.class);
				// 參考 gfnDB2SQLELF494_UPDATE
				for (L170M01D l170m01d : retrialService
						.findL170M01D_orderBySeq(meta)) {
					if (Util.equals(LrsUtil.Z_電腦建檔資料, l170m01d.getItemType())) {
						continue;
					}

					// J-105-0287-002 修改Web e-Loan國內企金授信覆審系統
					if (Util.equals(LrsUtil.Y_履約條件, l170m01d.getItemType())) {
						continue;
					}
					String itemDesc = LrsUtil.getPrintItemContent(l170m01d,
							prop_lms1700m01);
					String chooseResult = Util.trim(l170m01d.getChkResult());
					String memo = _prefixStr(l170m01d, prop_lms1700m01)
							+ _to_uploadTxt(l170m01d.getChkText());

					// J-108-0888_05097_B1001
					String strSeq = "";
					if (Util.notEquals(Util.trim(l170m01d.getItemSeqShow()), "")) {
						strSeq = Util.trim(l170m01d.getItemSeqShow());
					} else {
						strSeq = String.valueOf(l170m01d.getItemSeq());
					}

					chkItemList.add(Util.trim(String.valueOf(strSeq) + " "
							+ itemDesc + " " + chooseResult + " " + memo));
				}
				elf494.setElf494_chkItem(Util.truncateString(
						StringUtils.join(chkItemList, ";"),
						MAXLEN_ELF494_CHKITEM));
			}
			elf494.setElf494_fcrdType(elf412b.getElf412b_fcrdType());
			elf494.setElf494_fcrdArea(elf412b.getElf412b_fcrdArea());
			elf494.setElf494_fcrdPred(elf412b.getElf412b_fcrdPred());
			elf494.setElf494_fcrdGrad(elf412b.getElf412b_fcrdGrad());
		}

		if (true) {
			Set<String> upCntrNoSet = new HashSet<String>();
			List<L170M01B> l170m01b_list = retrialService
					.findL170M01B_orderBy(meta);
			if (CollectionUtils.isNotEmpty(l170m01b_list)) {
				for (L170M01B l170m01b : l170m01b_list) {
					ELF495 elf495 = new ELF495();
					elf495.setElf495_branch(elf494.getElf494_branch());
					elf495.setElf495_custId(elf494.getElf494_custId());
					elf495.setElf495_dupNo(elf494.getElf494_dupNo());
					elf495.setElf495_dbuObu(elf494.getElf494_dbuObu());
					// J-106-0145-002 Web e-Loan 國內企金授信管理系統修改實地覆審相關功能
					elf495.setElf495_ctlType(elf494.getElf494_ctlType());
					// ===
					if (true) {
						elf495.setElf495_cntrNo(l170m01b.getCntrNo());
						if (CrsUtil.isNOT_null_and_NOTZeroDate(l170m01b
								.getFromDate())) {
							elf495.setElf495_durBeg(l170m01b.getFromDate());
						} else {
							elf495.setElf495_durBeg(CapDate
									.parseDate(CapDate.ZERO_DATE));
						}
						if (CrsUtil.isNOT_null_and_NOTZeroDate(l170m01b
								.getEndDate())) {
							elf495.setElf495_durEnd(l170m01b.getEndDate());
						} else {
							elf495.setElf495_durEnd(CapDate
									.parseDate(CapDate.ZERO_DATE));
						}
						elf495.setElf495_curr(l170m01b.getQuotaCurr());
						elf495.setElf495_quota(l170m01b.getQuotaAmt());
					}
					// ===
					elf495.setElf495_rptDocId(elf494.getElf494_rptDocId());
					elf495.setElf495_llrDate(elf494.getElf494_llrDate());
					elf495.setElf495_lrDate(elf494.getElf494_lrDate());
					elf495.setElf495_projNo(elf494.getElf494_projNo());
					elf495.setElf495_dataDtY(elf494.getElf494_dataDtY());
					elf495.setElf495_batchNo(elf494.getElf494_batchNo());
					elf495.setElf495_sno(elf494.getElf494_sno());
					elf495.setElf495_updater(elf494.getElf494_updater());
					elf495.setElf495_tmestamp(elf494.getElf494_tmestamp());
					// ---
					if (upCntrNoSet.contains(elf495.getElf495_cntrNo())) {
						// 在 L170M01B 中
						// 可能同一個額度會有 2 個科目[購料放款、外銷放款]
						// 同一個額度、科目，會有2種幣別的餘額 [TWD、USD]
						continue;
					} else {
						upCntrNoSet.add(elf495.getElf495_cntrNo());
					}
					// ---
					elf495_list.add(elf495);
				}
			}
		}

		// 上次覆審評等資訊
		Map<String, List<L170M01E>> m_l170m01e = retrialService
				.findL170M01E_type(retrialService.findL170M01E(meta));
		String excreditType = "";
		String excreditGrade = "";
		String exmowType = "";
		String exmowGrade = "";
		String exfcrdType = "";
		String exfcrdArea = "";
		String exfcrdPred = "";
		String exfcrdGrad = "";
		Integer exfcrdScore = null;

		if (true) {
			L170M01E l170m01e_C = LrsUtil.firstElm(
					m_l170m01e.get(LrsUtil.M01E_FLAG_C), "L");
			if (l170m01e_C != null) {
				// elf494.setElf494_excrdType(l170m01e_C.getCrdType())
				excreditType = Util.trim(l170m01e_C.getCrdType());
				excreditGrade = Util.trim(l170m01e_C.getGrade());
			}
			L170M01E l170m01e_M = LrsUtil.firstElm(
					m_l170m01e.get(LrsUtil.M01E_FLAG_M), "L");
			if (l170m01e_M != null) {
				exmowType = Util.trim(l170m01e_M.getCrdType());
				exmowGrade = Util.trim(l170m01e_M.getGrade());
			}
			L170M01E l170m01e_F = LrsUtil.firstElm(
					m_l170m01e.get(LrsUtil.M01E_FLAG_F), "L");
			if (l170m01e_F != null) {
				exfcrdType = Util.trim(l170m01e_F.getCrdType());
				exfcrdArea = Util.trim(l170m01e_F.getFcrdArea());
				exfcrdPred = Util.trim(l170m01e_F.getFcrdPred());
				exfcrdGrad = Util.trim(l170m01e_F.getGrade());
				exfcrdScore = l170m01e_F.getScore();
			}

			if (Util.equals(UtilConstants.crdType.未評等, excreditType)
					|| Util.isEmpty(excreditType)) {
				excreditType = "";
				excreditGrade = "";
			} else if (Util.equals(UtilConstants.Type.無資料_C, excreditType)) {
				excreditType = "X";
				excreditGrade = "";
			}
			if (Util.equals(UtilConstants.Casedoc.CrdType2.免辦, exmowType)
					|| Util.isEmpty(exmowType)) {
				exmowType = "";
				exmowGrade = "";
			} else if (Util.equals(UtilConstants.Type.無資料_M, excreditType)) {
				exmowType = "X";
				exmowGrade = "";
			}
			if (Util.isEmpty(exfcrdGrad)
					|| (exfcrdScore != null && exfcrdScore == 90)) {
				exfcrdType = "";
				exfcrdArea = "";
				exfcrdPred = "";
				exfcrdGrad = "";
			}

			if (Util.isNotEmpty(excreditType)) {
				excreditType = LMSUtil.getDesc(
						retrialService.get_lrs_CrdtType_2to1(), excreditType);
			}
			if (Util.isNotEmpty(exmowType)) {
				exmowType = LMSUtil.getDesc(
						retrialService.get_lrs_MowType_2to1(), exmowType);
			}

			elf494.setElf494_excrdType(excreditType);
			elf494.setElf494_excrdtTbl(excreditGrade);
			elf494.setElf494_exmowType(exmowType);
			elf494.setElf494_exmowTbl1(exmowGrade);

			elf494.setElf494_exfcrdType(exfcrdType);
			elf494.setElf494_exfcrdArea(exfcrdArea);
			elf494.setElf494_exfcrdPred(exfcrdPred);
			elf494.setElf494_exfcrdGrad(exfcrdGrad);
		}
	}

	private ELF412C gfnDB2UpELF412C(L170M01A meta) {
		String creditType = "";
		String creditGrade = "";

		String mowType = "";
		String mowGrade = "";

		String fcrdType = "";
		String fcrdArea = "";
		String fcrdPred = "";
		String fcrdGrad = "";

		if (true) {
			Map<String, List<L170M01E>> m_l170m01e = retrialService
					.findL170M01E_type(retrialService.findL170M01E(meta));
			L170M01E l170m01e_C = LrsUtil.firstElm(
					m_l170m01e.get(LrsUtil.M01E_FLAG_C), "T");
			L170M01E l170m01e_M = LrsUtil.firstElm(
					m_l170m01e.get(LrsUtil.M01E_FLAG_M), "T");
			L170M01E l170m01e_F = LrsUtil.firstElm(
					m_l170m01e.get(LrsUtil.M01E_FLAG_F), "T");
			Integer fcrdScore = null;

			if (l170m01e_C != null) {
				creditType = Util.trim(l170m01e_C.getCrdType());
				creditGrade = Util.trim(l170m01e_C.getGrade());
			}

			if (l170m01e_M != null) {
				mowType = Util.trim(l170m01e_M.getCrdType());
				mowGrade = Util.trim(l170m01e_M.getGrade());
			}

			if (l170m01e_F != null) {
				fcrdType = Util.trim(l170m01e_F.getCrdType());
				fcrdArea = Util.trim(l170m01e_F.getFcrdArea());
				fcrdPred = Util.trim(l170m01e_F.getFcrdPred());
				fcrdGrad = Util.trim(l170m01e_F.getGrade());
				fcrdScore = l170m01e_F.getScore();
			}
			// ======
			if (Util.equals(UtilConstants.crdType.未評等, creditType)
					|| Util.isEmpty(creditType)) {
				creditType = "";
				creditGrade = "";
			}
			if (Util.equals(UtilConstants.Casedoc.CrdType2.免辦, mowType)
					|| Util.isEmpty(mowType)) {
				mowType = "";
				mowGrade = "";
			}
			if (Util.isEmpty(fcrdGrad)
					|| (fcrdScore != null && fcrdScore == 90)) {
				fcrdType = "";
				fcrdArea = "";
				fcrdPred = "";
				fcrdGrad = "";
			}
			// ======
			// 把 2 碼的 creditType,mowType 轉成 1碼的格式，來上傳
			if (Util.isNotEmpty(creditType)) {
				creditType = LMSUtil.getDesc(
						retrialService.get_lrs_CrdtType_2to1(), creditType);
			}
			if (Util.isNotEmpty(mowType)) {
				mowType = LMSUtil.getDesc(
						retrialService.get_lrs_MowType_2to1(), mowType);
			}
		}
		String mLoanPerson = "Y".equals(meta.getMLoanPerson()) ? "Y" : "N";

		ELF412C elf412c = misELF412CService.findByPk(meta.getOwnBrId(),
				meta.getCustId(), meta.getDupNo());
		if (elf412c != null) {

			elf412c.setElf412c_rckdLine("A");
			elf412c.setElf412c_newAdd("");
			elf412c.setElf412c_newDate("");

			if (Util.isEmpty(Util.trim(elf412c.getElf412c_uckdLine()))) {
				elf412c.setElf412c_uckdLine("N");
			}
			if (elf412c.getElf412c_uckdDt() == null) {
				elf412c.setElf412c_uckdDt(CapDate.parseDate(CapDate.ZERO_DATE));
			}

			elf412c.setElf412c_crdType(creditType);
			elf412c.setElf412c_crdtTbl(creditGrade);
			elf412c.setElf412c_mowType(mowType);
			elf412c.setElf412c_mowTbl1(mowGrade);

			elf412c.setElf412c_fcrdType(fcrdType);
			elf412c.setElf412c_fcrdArea(fcrdArea);
			elf412c.setElf412c_fcrdPred(fcrdPred);
			elf412c.setElf412c_fcrdGrad(fcrdGrad);

			elf412c.setElf412c_mainCust(mLoanPerson);
			retrialService.gfnCTL_Caculate_ELF412C(elf412c);

			if (CrsUtil.isNOT_null_and_NOTZeroDate(meta.getLastRetrialDate())) {
				elf412c.setElf412c_llrDate(meta.getLastRetrialDate());
			} else {
				if (CrsUtil.isNOT_null_and_NOTZeroDate(elf412c
						.getElf412c_lrDate())) {
					elf412c.setElf412c_llrDate(elf412c.getElf412c_lrDate());
				} else {
					elf412c.setElf412c_llrDate(CapDate
							.parseDate(CapDate.ZERO_DATE));
				}
			}

			elf412c.setElf412c_nckdFlag("");
			elf412c.setElf412c_nckdDate(CapDate.parseDate(CapDate.ZERO_DATE));
			elf412c.setElf412c_nckdMemo("");
			elf412c.setElf412c_nextNwDt(CapDate.parseDate(CapDate.ZERO_DATE));
			elf412c.setElf412c_nextLtDt(CapDate.parseDate(CapDate.ZERO_DATE));

		} else {
			elf412c = new ELF412C();
			// ---
			elf412c.setElf412c_branch(meta.getOwnBrId());
			elf412c.setElf412c_custId(meta.getCustId());
			elf412c.setElf412c_dupNo(meta.getDupNo());
			// ---
			elf412c.setElf412c_rckdLine("A");
			elf412c.setElf412c_uckdLine("N");
			elf412c.setElf412c_uckdDt(CapDate.parseDate(CapDate.ZERO_DATE));

			elf412c.setElf412c_crdType(creditType);
			elf412c.setElf412c_crdtTbl(creditGrade);
			elf412c.setElf412c_mowType(mowType);
			elf412c.setElf412c_mowTbl1(mowGrade);
			elf412c.setElf412c_mainCust(mLoanPerson);

			elf412c.setElf412c_fcrdType(fcrdType);
			elf412c.setElf412c_fcrdArea(fcrdArea);
			elf412c.setElf412c_fcrdPred(fcrdPred);
			elf412c.setElf412c_fcrdGrad(fcrdGrad);

			retrialService.gfnCTL_Caculate_ELF412C(elf412c);

			if (CrsUtil.isNOT_null_and_NOTZeroDate(meta.getLastRetrialDate())) {
				elf412c.setElf412c_llrDate(meta.getLastRetrialDate());
			} else {
				elf412c.setElf412c_llrDate(CapDate.parseDate(CapDate.ZERO_DATE));
			}
		}

		elf412c.setElf412c_lrDate(meta.getRetrialDate());
		elf412c.setElf412c_updater(Util.trim(meta.getApprover()));
		elf412c.setElf412c_tmestamp(CapDate.getCurrentTimestamp());
		LrsUtil.elf412c_null_to_zeroDate(elf412c);

		return elf412c;
	}

	private void gfnDB2UpELF494_ELF495(L170M01A meta, ELF412C elf412c,
			ELF494 elf494, List<ELF495> elf495_list) {
		if (true) {
			elf494.setElf494_branch(meta.getOwnBrId());
			elf494.setElf494_custId(meta.getCustId());
			elf494.setElf494_dupNo(meta.getDupNo());
			elf494.setElf494_dbuObu(Util.equals("4", meta.getTypCd()) ? "OBU"
					: "DBU");
			elf494.setElf494_rptDocId(meta.getMainId());
			// J-106-0145-002 Web e-Loan 國內企金授信管理系統修改實地覆審相關功能
			elf494.setElf494_ctlType(Util.equals(Util.trim(meta.getCtlType()),
					"") ? LrsUtil.CTLTYPE_主辦覆審 : Util.trim(meta.getCtlType()));

			if (CrsUtil.isNOT_null_and_NOTZeroDate(meta.getLastRetrialDate())) {
				elf494.setElf494_llrDate(meta.getLastRetrialDate());
			} else {
				elf494.setElf494_llrDate(CapDate.parseDate(CapDate.ZERO_DATE));
			}
			if (CrsUtil.isNOT_null_and_NOTZeroDate(meta.getRetrialDate())) {
				elf494.setElf494_lrDate(meta.getRetrialDate());
			} else {
				elf494.setElf494_lrDate(CapDate.parseDate(CapDate.ZERO_DATE));
			}
			String projectNo = Util.trim(meta.getProjectNo());
			if (Util.isNotEmpty(projectNo)) {
				String yyyy = StringUtils.substring(projectNo, 0, 4);
				String extractProjectNo = LrsUtil.extractProjectNo(projectNo);

				elf494.setElf494_projNo(projectNo);
				elf494.setElf494_dataDtY(String.valueOf(Util.parseInt(yyyy) - 1911));
				elf494.setElf494_batchNo(StringUtils.substring(
						extractProjectNo, 0, 3));
				elf494.setElf494_sno(StringUtils.substring(extractProjectNo, 4,
						7));
			} else {
				elf494.setElf494_projNo("");
				elf494.setElf494_dataDtY("");
				elf494.setElf494_batchNo("");
				elf494.setElf494_sno("");
			}

			elf494.setElf494_mainCust(elf412c.getElf412c_mainCust());
			elf494.setElf494_crdType(elf412c.getElf412c_crdType());
			elf494.setElf494_crdtTbl(elf412c.getElf412c_crdtTbl());
			elf494.setElf494_mowType(elf412c.getElf412c_mowType());
			elf494.setElf494_mowTbl1(elf412c.getElf412c_mowTbl1());
			elf494.setElf494_nckdFlag(meta.getNCkdFlag());

			if (true) {
				L170M01F l170m01f = meta.getL170m01f();
				elf494.setElf494_retial(l170m01f.getRetialComm());
				elf494.setElf494_conFlag(l170m01f.getConFlag());
				if (l170m01f.getUpDate() != null) {
					elf494.setElf494_upDate(l170m01f.getUpDate());
				} else {
					elf494.setElf494_upDate(CapDate.getCurrentTimestamp());
				}
			}
			if (true) {
				String elf494_managerId = "";
				String elf494_bossId = "";
				String elf494_apprId = "";
				if (true) {
					List<L170M01G> l170m01g_list = retrialService
							.findL170M01G_l170m01a(meta);
					List<L170M01G> l170m01g_L1 = retrialService
							.findL170M01G_byBranchTypeStaffJob(l170m01g_list,
									lrsConstants.BRANCHTYPE.覆審單位, "L1");
					List<L170M01G> l170m01g_L4 = retrialService
							.findL170M01G_byBranchTypeStaffJob(l170m01g_list,
									lrsConstants.BRANCHTYPE.覆審單位, "L4");
					List<L170M01G> l170m01g_L5 = retrialService
							.findL170M01G_byBranchTypeStaffJob(l170m01g_list,
									lrsConstants.BRANCHTYPE.覆審單位, "L5");

					if (CollectionUtils.isNotEmpty(l170m01g_L1)) {
						elf494_apprId = l170m01g_L1.get(0).getStaffNo();
					}
					if (CollectionUtils.isNotEmpty(l170m01g_L4)) {
						elf494_bossId = l170m01g_L4.get(0).getStaffNo();
					}
					if (CollectionUtils.isNotEmpty(l170m01g_L5)) {
						elf494_managerId = l170m01g_L5.get(0).getStaffNo();
					}
				}

				elf494.setElf494_managerId(elf494_managerId);
				elf494.setElf494_bossId(elf494_bossId);
				elf494.setElf494_apprId(elf494_apprId);
			}

			elf494.setElf494_updater(elf412c.getElf412c_updater());
			elf494.setElf494_tmestamp(elf412c.getElf412c_tmestamp());

			// J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
			elf494.setElf494_realRpFg(Util.trim(meta.getRealRpFg()));
			if (meta.getRealDt() != null) {
				elf494.setElf494_realRpDt(meta.getRealDt());
			}

			if (true) {
				/*
				 * 依 itemSeq 來排序 ------ Case 9: tmp2 = "計收之利率是否符合本行定價政策相關規範？"
				 * tmp3_name = "result_"+Cstr(26) tmp4_name =
				 * "unusual_"+Cstr(26) ------ 以上程式表示：第9項取得 result_26 的值
				 */
				List<String> chkItemList = new ArrayList<String>();
				Properties prop_lms1700m01 = MessageBundleScriptCreator
						.getComponentResource(LMS1700M01Page.class);
				// 參考 gfnDB2SQLELF494_UPDATE
				for (L170M01D l170m01d : retrialService
						.findL170M01D_orderBySeq(meta)) {
					String itemDesc = Util.trim(l170m01d.getItemContent());
					String chooseResult = Util.trim(l170m01d.getChkResult());
					String memo = _to_uploadTxt(l170m01d.getChkText());

					// J-108-0888_05097_B1001
					String strSeq = "";
					if (Util.notEquals(Util.trim(l170m01d.getItemSeqShow()), "")) {
						strSeq = Util.trim(l170m01d.getItemSeqShow());
					} else {
						strSeq = String.valueOf(l170m01d.getItemSeq());
					}

					chkItemList.add(Util.trim(String.valueOf(strSeq) + " "
							+ itemDesc + " " + chooseResult + " " + memo));
				}
				elf494.setElf494_chkItem(Util.truncateString(
						StringUtils.join(chkItemList, ";"),
						MAXLEN_ELF494_CHKITEM));
			}
			elf494.setElf494_fcrdType(elf412c.getElf412c_fcrdType());
			elf494.setElf494_fcrdArea(elf412c.getElf412c_fcrdArea());
			elf494.setElf494_fcrdPred(elf412c.getElf412c_fcrdPred());
			elf494.setElf494_fcrdGrad(elf412c.getElf412c_fcrdGrad());
		}

		if (true) {
			Set<String> upCntrNoSet = new HashSet<String>();
			List<L170M01B> l170m01b_list = retrialService
					.findL170M01B_orderBy(meta);
			if (CollectionUtils.isNotEmpty(l170m01b_list)) {
				for (L170M01B l170m01b : l170m01b_list) {
					ELF495 elf495 = new ELF495();
					elf495.setElf495_branch(elf494.getElf494_branch());
					elf495.setElf495_custId(elf494.getElf494_custId());
					elf495.setElf495_dupNo(elf494.getElf494_dupNo());
					elf495.setElf495_dbuObu(elf494.getElf494_dbuObu());
					// J-106-0145-002 Web e-Loan 國內企金授信管理系統修改實地覆審相關功能
					elf495.setElf495_ctlType(elf494.getElf494_ctlType());
					// ===
					if (true) {
						elf495.setElf495_cntrNo(l170m01b.getCntrNo());
						if (CrsUtil.isNOT_null_and_NOTZeroDate(l170m01b
								.getFromDate())) {
							elf495.setElf495_durBeg(l170m01b.getFromDate());
						} else {
							elf495.setElf495_durBeg(CapDate
									.parseDate(CapDate.ZERO_DATE));
						}
						if (CrsUtil.isNOT_null_and_NOTZeroDate(l170m01b
								.getEndDate())) {
							elf495.setElf495_durEnd(l170m01b.getEndDate());
						} else {
							elf495.setElf495_durEnd(CapDate
									.parseDate(CapDate.ZERO_DATE));
						}
						elf495.setElf495_curr(l170m01b.getQuotaCurr());
						elf495.setElf495_quota(l170m01b.getQuotaAmt());
					}
					// ===
					elf495.setElf495_rptDocId(elf494.getElf494_rptDocId());
					elf495.setElf495_llrDate(elf494.getElf494_llrDate());
					elf495.setElf495_lrDate(elf494.getElf494_lrDate());
					elf495.setElf495_projNo(elf494.getElf494_projNo());
					elf495.setElf495_dataDtY(elf494.getElf494_dataDtY());
					elf495.setElf495_batchNo(elf494.getElf494_batchNo());
					elf495.setElf495_sno(elf494.getElf494_sno());
					elf495.setElf495_updater(elf494.getElf494_updater());
					elf495.setElf495_tmestamp(elf494.getElf494_tmestamp());
					// ---
					if (upCntrNoSet.contains(elf495.getElf495_cntrNo())) {
						// 在 L170M01B 中
						// 可能同一個額度會有 2 個科目[購料放款、外銷放款]
						// 同一個額度、科目，會有2種幣別的餘額 [TWD、USD]
						continue;
					} else {
						upCntrNoSet.add(elf495.getElf495_cntrNo());
					}
					// ---
					elf495_list.add(elf495);
				}
			}
		}

		// 上次覆審評等資訊
		Map<String, List<L170M01E>> m_l170m01e = retrialService
				.findL170M01E_type(retrialService.findL170M01E(meta));
		String excreditType = "";
		String excreditGrade = "";
		String exmowType = "";
		String exmowGrade = "";
		String exfcrdType = "";
		String exfcrdArea = "";
		String exfcrdPred = "";
		String exfcrdGrad = "";
		Integer exfcrdScore = null;

		if (true) {
			L170M01E l170m01e_C = LrsUtil.firstElm(
					m_l170m01e.get(LrsUtil.M01E_FLAG_C), "L");
			if (l170m01e_C != null) {
				// elf494.setElf494_excrdType(l170m01e_C.getCrdType())
				excreditType = Util.trim(l170m01e_C.getCrdType());
				excreditGrade = Util.trim(l170m01e_C.getGrade());
			}
			L170M01E l170m01e_M = LrsUtil.firstElm(
					m_l170m01e.get(LrsUtil.M01E_FLAG_M), "L");
			if (l170m01e_M != null) {
				exmowType = Util.trim(l170m01e_M.getCrdType());
				exmowGrade = Util.trim(l170m01e_M.getGrade());
			}
			L170M01E l170m01e_F = LrsUtil.firstElm(
					m_l170m01e.get(LrsUtil.M01E_FLAG_F), "L");
			if (l170m01e_F != null) {
				exfcrdType = Util.trim(l170m01e_F.getCrdType());
				exfcrdArea = Util.trim(l170m01e_F.getFcrdArea());
				exfcrdPred = Util.trim(l170m01e_F.getFcrdPred());
				exfcrdGrad = Util.trim(l170m01e_F.getGrade());
				exfcrdScore = l170m01e_F.getScore();
			}

			if (Util.equals(UtilConstants.crdType.未評等, excreditType)
					|| Util.isEmpty(excreditType)) {
				excreditType = "";
				excreditGrade = "";
			} else if (Util.equals(UtilConstants.Type.無資料_C, excreditType)) {
				excreditType = "X";
				excreditGrade = "";
			}
			if (Util.equals(UtilConstants.Casedoc.CrdType2.免辦, exmowType)
					|| Util.isEmpty(exmowType)) {
				exmowType = "";
				exmowGrade = "";
			} else if (Util.equals(UtilConstants.Type.無資料_M, excreditType)) {
				exmowType = "X";
				exmowGrade = "";
			}
			if (Util.isEmpty(exfcrdGrad)
					|| (exfcrdScore != null && exfcrdScore == 90)) {
				exfcrdType = "";
				exfcrdArea = "";
				exfcrdPred = "";
				exfcrdGrad = "";
			}

			if (Util.isNotEmpty(excreditType)) {
				excreditType = LMSUtil.getDesc(
						retrialService.get_lrs_CrdtType_2to1(), excreditType);
			}
			if (Util.isNotEmpty(exmowType)) {
				exmowType = LMSUtil.getDesc(
						retrialService.get_lrs_MowType_2to1(), exmowType);
			}

			elf494.setElf494_excrdType(excreditType);
			elf494.setElf494_excrdtTbl(excreditGrade);
			elf494.setElf494_exmowType(exmowType);
			elf494.setElf494_exmowTbl1(exmowGrade);

			elf494.setElf494_exfcrdType(exfcrdType);
			elf494.setElf494_exfcrdArea(exfcrdArea);
			elf494.setElf494_exfcrdPred(exfcrdPred);
			elf494.setElf494_exfcrdGrad(exfcrdGrad);
		}
	}

	// J-108-0268 逾期情形
	@Override
	public boolean chkOverDue(L170M01A meta) {
		List<L170M01D> l170m01d_list = retrialService
				.findL170M01D_orderBySeq(meta);
		boolean chk = false;
		String ctlType = Util.trim(meta.getCtlType());
		if (Util.equals(ctlType, LrsUtil.CTLTYPE_主辦覆審)) {
			for (L170M01D l170m01d : l170m01d_list) {
				if (Util.equals(LrsUtil.N009, l170m01d.getItemNo())) {
					if (meta.getOvQryDt() == null) {
						chk = true;
						break;
					}
				}
			}
		} else if (Util.equals(ctlType, LrsUtil.CTLTYPE_自辦覆審)) {
			for (L170M01D l170m01d : l170m01d_list) {
				if (Util.equals(LrsUtil.B014, l170m01d.getItemNo())) {
					if (meta.getOvQryDt() == null) {
						chk = true;
						break;
					}
				}
			}
		}
		return chk;
	}

}
