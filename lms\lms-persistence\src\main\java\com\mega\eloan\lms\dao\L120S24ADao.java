/* 
 * L120S24ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S24A;

/** 風控風險權數主檔 **/
public interface L120S24ADao extends IGenericDao<L120S24A> {

	L120S24A findByOid(String oid);

	L120S24A findByRefMainIdAndCntrNo(String refMainId, String cntrNo);

	List<L120S24A> findByMainId(String mainId);

	List<L120S24A> findByIndex01(String mainId);

	List<L120S24A> findByMainIdCntrNo(String mainId, String cntrNo);

	/**
	 * 找到同一custId下非自己這筆，但bowrrowerClass_s24a值不一樣
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @param bowrrowerClass_s24a
	 * @param oid
	 * @return
	 */
	List<L120S24A> findDifferentBowrrowerClass(String mainId, String custId,
			String dupNo, String bowrrowerClass_s24a, String oid);
}