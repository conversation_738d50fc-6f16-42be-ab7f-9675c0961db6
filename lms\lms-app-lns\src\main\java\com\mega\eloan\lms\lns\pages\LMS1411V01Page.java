/* 
 * LMS1411V01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lns.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.panels.GridViewFilterPanel02;
import com.mega.eloan.lms.lms.pages.LMS1415M01Page;

import tw.com.jcs.auth.AuthType;

/**
 * <pre>
 * [國內企金]聯行額度明細表
 * </pre>
 * 
 * @since 2012/11/29
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/11/29,REX,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms1411v01")
public class LMS1411V01Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		setGridViewStatus(CreditDocStatusEnum.海外_編製中);

		LmsButtonEnum[] btn = null;
		if (this.getAuth(AuthType.Accept)) {
			btn = new LmsButtonEnum[] { LmsButtonEnum.View,
					LmsButtonEnum.Filter };
		} else {

			btn = new LmsButtonEnum[] { LmsButtonEnum.View,
					LmsButtonEnum.Delete, LmsButtonEnum.Filter };

		}
		//add(new CreditButtonPanel("_buttonPanel", null, btn));
		addToButtonPanel(model, btn);
		// 加上Button
		renderJsI18N(LMS1415M01Page.class);
		renderJsI18N(LMS1411V01Page.class);

		setupIPanel(new GridViewFilterPanel02(PANEL_ID), model, params);
	}

}
