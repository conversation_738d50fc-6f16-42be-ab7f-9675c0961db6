/* 
 * AbstractBasePage.java
 * 
 * Copyright (c) 2022 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package tw.com.iisi.cap.base.pages;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.ui.ModelMap;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.iisigroup.cap.component.PageParameters;
import com.iisigroup.cap.component.impl.CapMvcParameters;
import com.iisigroup.cap.utils.CapAppContext;

import tw.com.iisi.cap.utils.CapWebUtil;

/**
 * <pre>
 * Controller 最上層共用，PageParameters, ServletRequestAttributes 的共用存取 method，
 * 無 @RequestMapping 設定，讓所有需要 PageParameters, ServletRequestAttributes、卻不需要畫面共用處理的 controller 可以方便取得
 * </pre>
 * 
 * @since 2022年10月4日
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2022年10月4日,1104263,new
 *          </ul>
 */
public abstract class AbstractBasePage {

    /**
     * The Constant PRE_VALIDATE_MESSAGE.<br>
     * {@value #PRE_VALIDATE_MESSAGE}
     */
    public static final String PRE_VALIDATE_MESSAGE = "PRE_VALIDATE_MESSAGE";

    /**
     * 取得 HttpServlet Attributes
     * 
     * @return {@code (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()}
     */
    protected final ServletRequestAttributes getHttpServletAttr() {
        return (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
    }

    /**
     * 轉換 Resquest
     * 
     * @param req
     * @return
     */
    protected PageParameters convertRequest(HttpServletRequest req) {
        PageParameters params = CapAppContext.getBean("capMvcParameters");
        if (params == null) {
            params = new CapMvcParameters();
        }
        params.setRequestObject(req);
        params.put("docUrl", CapWebUtil.getDocUrl(getClass()));
        params.put("trigger", getClass().getSimpleName());
        getHttpServletAttr().getRequest().setAttribute("pageParameters", params);
        return params;
    }

    /**
     * 取得頁面參數
     * 
     * @return {@code (PageParameters) getHttpServletAttr().getRequest().getAttribute("pageParameters")}
     */
    protected PageParameters getPageParameters() {
        return (PageParameters) getHttpServletAttr().getRequest().getAttribute("pageParameters");
    }

    /**
     * 取得 Response
     * 
     * @return {@code getHttpServletAttr().getResponse()}
     */
    protected HttpServletResponse getResponse() {
        return getHttpServletAttr().getResponse();
    }

    /**
     * 前置處理
     * 
     * @param model
     * @return
     */
    protected boolean beforeExecute(ModelMap model) throws Exception {
        return true;
    }
}
