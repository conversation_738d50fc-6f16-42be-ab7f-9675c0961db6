var gridDfd = $.Deferred();
$(document).ready(function(){	
	A_1_8_1();
	A_1_8_2();
	gridviewCust();
	mainThick();
	initRORWAGrid();
	$("#formL120s04b").find("#grpGrrd").change(function(i){
		var $formL120s04b = $("#formL120s04b");
		var grpNo = $("#formL120s04b").find("#grpNo").html();
		var grpGrrd = $(this).val();

		//J-107-0087-001 Web e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。			
		var grpYear =  $formL120s04b.find("#grpYear").html();
		 
		if(grpYear){
			//判斷2017以後為新版，之前為舊版
			if(parseInt(grpYear, 10) >= 2017){
				// 建霖說：grpNo可以不用，只要集團評等(grpGrrd)符合條件就顯示 Miller eddedat 2012/11/27
				if(grpGrrd == "1" || grpGrrd == "2" || grpGrrd == "3" || grpGrrd == "4" || grpGrrd == "5" || grpGrrd == "6" || grpGrrd == "7"){
					// 顯示屬主要集團企業...
					$formL120s04b.find(".spectialHide").show();
				}else{
					// 隱藏屬主要集團企業...
					$formL120s04b.find(".spectialHide").hide();
				}			
			}else{
				// 建霖說：grpNo可以不用，只要集團評等(grpGrrd)符合條件就顯示 Miller eddedat 2012/11/27
				if(grpGrrd == "1" || grpGrrd == "2" || grpGrrd == "3" || grpGrrd == "4" || grpGrrd == "5"){
					// 顯示屬主要集團企業...
					$formL120s04b.find(".spectialHide").show();
				}else{
					// 隱藏屬主要集團企業...
					$formL120s04b.find(".spectialHide").hide();
				}			
			}
			
			
		}else{
			//如果沒有評等年度，以舊版執行
			// 建霖說：grpNo可以不用，只要集團評等(grpGrrd)符合條件就顯示 Miller eddedat 2012/11/27
			if(grpGrrd == "1" || grpGrrd == "2" || grpGrrd == "3" || grpGrrd == "4" || grpGrrd == "5"){
				// 顯示屬主要集團企業...
				$formL120s04b.find(".spectialHide").show();
			}else{
				// 隱藏屬主要集團企業...
				$formL120s04b.find(".spectialHide").hide();
			}			
		}
		
	});	

	$("#buttonSearch03").click(function(){
		if($("#formAdd").valid()){
	        $.ajax({
	            type: "POST",
	            handler: "lms9535formhandler",
	            data: {
	                formAction: "getCustData",
	                custId: $("#formAdd").find("#searchId03").val()
	            },
	            success: function(responseData03){
	                // alert(JSON.stringify(responseData));
				      var selJson03 = {
					       		item : responseData03.selCus,
					       		format : "{value} - {key}",
					       		space: true
					       	};
				      $("#selCus03").setItems(selJson03);				  
				      $("#showSel03").show();
	            }
	        });						
		}
	});
	
	//上傳檔案按鈕
	$("#uploadComFile").click(function(){
/*
		var count=$("#gridviewPare").jqGrid('getGridParam','records');
		if(count == 1){
			// other.msg173=最多只能產生(上傳)一筆借戶暨關係戶與本行授信往來比較表！
			return CommonAPI.showMessage(i18n.lmscommom["other.msg173"]);
		}
*/
		var limitFileSize=3145728;
		MegaApi.uploadDialog({
			fieldId:"RPTNoList",
            fieldIdHtml:"size='30'",
            fileDescId:"fileDesc",
            fileDescHtml:"size='30' maxlength='30'",
			subTitle:i18n.def('insertfileSize',{'fileSize':(limitFileSize/1048576).toFixed(2)}),
			limitSize:limitFileSize,
            width:320,
            height:190,			
			data:{
				mainId:$("#mainId").val()
			},
			success : function(obj) {
				$("#gridviewPare").trigger("reloadGrid");
			}
	   });
	});	
	//刪除檔案按鈕
	$("#deleteComFile").click(function(){
		var select  = $("#gridviewPare").getGridParam('selrow');		
		// confirmDelete=是否確定刪除?
		CommonAPI.confirmMessage(i18n.def["confirmDelete"],function(b){
			if(b){				
				var data = $("#gridviewPare").getRowData(select);
				if(data.oid == "" || data.oid == undefined || data.oid == null){		
					// TMMDeleteError=請先選擇需修改(刪除)之資料列
					CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
					return;
				}				
				$.ajax({
					handler : "lms9535formhandler",
					type : "POST",
					dataType : "json",
					data : {
						formAction : "deleteUploadFile",
						fileOid:data.oid
					},
					success : function(obj) {
						$("#gridviewPare").trigger("reloadGrid");
					}
				});
			}else{
				return ;
			}
		});
	});		
	gridviewPare("gridviewPare", "RPTNoList");	
	
	
	$(window).bind("beforeunload", function(){
		$.ajax({
			handler : "lms9535formhandler",
			type : "POST",
			dataType : "json",
			action : "deleteL120s04a",
			data : {
				mainId : $("#mainId").val()
			},
			success : function(json2){
				$.ajax({
					handler : "lms9535formhandler",
					type : "POST",
					dataType : "json",
					action : "deleteL120s04b",
					data : {
						mainId : $("#mainId").val()
					},
					success : function(json2){
			
					}
				})
			}
		});
	});
	
	//J-113-0183 新增RORWA計算
	$("#RORWACal").click(function(){
		var count=$("#gridview_A-1-8-1").jqGrid('getGridParam','records');
		if(count == 0){
			CommonAPI.showMessage(i18n.lmss07a["L1205S07.error14"]);
			return;
		}
		
		$("#RORWADetailThickbox").thickbox({
            title: i18n.lms9535v01["RORWACAL"],
            width: 900,
            height: 700,
            modal: true,
            readOnly: _openerLockDoc == "1" || thickboxOptions.readOnly,
            i18n: i18n.def,
            buttons : {
				"print" : function(){	
					printRORWA();
				},
				"close" : function() {
					 API.confirmMessage(i18n.def['flow.exit'], function(res){
							if(res){
								$.thickbox.close();
							}
				        });
				}
			}
        });
	});
	
	//J-113-0183
	$(".calcRiskOffInsAmt").change(function(){
		calcRiskOffInsAmt();
	});
	
	//J-113-0183
	$("#onlyGuar_s25a").change(function(){
		//本額度僅有保證科目、應收信用狀款項科目選「是」，資金成本為0
		if("Y" == DOMPurify.sanitize($("#onlyGuar_s25a").val())){
			$("#bisFtpRate").val("0");
		}
	});
	
	//J-113-0183
	setCurr();
	showBisCostRateType();
	$("select[name=bisApplyCurr]").change(function(){
		setCurr();
		impBisTwdRate();
	});
	$("#bisCostRateType").change(function(){
		showBisCostRateType();
	});
	
});

// 設定附加檔案Grid
function gridviewPare(fileGridId, fieldId){
	//檔案上傳grid
	$("#gridviewPare").iGrid({
		handler : 'lms9535gridhandler',
		height : 50,
		sortname : 'srcFileName',
		postData : {
			formAction : "queryfile",
			fieldId:fieldId,
			mainId:$("#mainThick").find("#mainId").val(),//$("#mainId").val(),
			needCngName : true
		},
		rowNum : 15,
		caption: "&nbsp;",
		hiddengrid : false,
		//expandOnLoad : true,	//只對subgrid有用
		//multiselect : true,
		colModel : [ {
			colHeader : i18n.lmscommom['other.msg152'],//other.msg152=報表名稱
			name : 'srcFileName',
			width : 120,
			align: "left",
			sortable : false,
			formatter : 'click',
			onclick : openFile
		}, {
			// other.msg176=上傳時間
			colHeader : i18n.lmscommom['other.msg176'],//L1205S07.index33=上傳時間
			name : 'uploadTime',
			width : 140,
			sortable : false
		}, {
			name : 'oid',
			hidden : true
		}]
	});		
}

/**
 * 產生主要關係戶與本行授信往來比較表(Excel)
 */
function creExcel(){
	API.confirmMessage(i18n.def["confirmRun"],function(b){
		if(b){
			//是的function
			$.ajax({
				handler : "lms9535formhandler",
				type : "POST",
				dataType : "json",
				action : "creExcel",
				data : {
					mainId : $("#mainId").val(),
					type : "1"
				},
				success : function(json) {
					// 更新ExcelGrid
					ugridviewPare();
				}
			});	
/*
	        $.form.submit({
	            url: "../../simple/FileProcessingService",
	            target: "_blank",
	            data: {
	                mainId: responseJSON.mainId,
	                fileDownloadName: "LMS1201M01A.xls",
	                serviceName: "lms1201xlsservice"
	            }
	        });
*/
		}else{
			//否的function
			//CommonAPI.showMessage(i18n.lmss07a["L1205S07.alert5"]);
		}
	})
}

function openFile(cellvalue, options, rowObject){
    $.capFileDownload({
        handler:"simplefiledwnhandler",
        data : {
            fileOid:rowObject.oid
        }
    });
}

/**
 * 主畫面ThickBox
 */
function mainThick(){
	
	//集團列管註記
	var obj1= CommonAPI.loadCombos("l1205s01_groupBadFlag");
	$("#tLMS1205S07Form03b").find("#groupBadFlag").setItems({
		item : obj1.l1205s01_groupBadFlag,
		format : "{key}",
		space: true
	});
		
	$("#mainThick").thickbox({ // 使用選取的內容進行彈窗
		title : i18n.lms9535v01["L1205S07.subindex8"],
		width : 900,
		height : 480,
		modal : true,
		i18n:i18n.def,
		buttons : {
			"close" : function() {
				// L1205S07.confirm4=關閉前會將已引進的往來彙總表刪除，是否確定要關閉？
				 API.confirmMessage(i18n.lms9535v01["L1205S07.confirm4"], function(res){
					if(res){
						$.ajax({
							handler : "lms9535formhandler",
							type : "POST",
							dataType : "json",
							action : "deleteL120s04a",
							data : {
								mainId : $("#mainId").val()
							},
							success : function(json1) {
								$.thickbox.close();
								$.ajax({
									handler : "lms9535formhandler",
									type : "POST",
									dataType : "json",
									action : "deleteL120s04b",
									data : {
										mainId : $("#mainId").val()
									},
									success : function(json2) {
										$.thickbox.close();
										$.thickbox.close();
										// 更新彙總表Grid
										uA_1_8_1();
										// 更新實績彙總表Grid
										uA_1_8_2();
										CommonAPI.showMessage(json2.NOTIFY_MESSAGE);
									}
								});
							}
						});
					}
		        });			
			}
		}
	});
	
	//J-113-0183
	var result_s = CommonAPI.loadCombos(["Common_Currcy","bisCostRateType","bisEstimatedType"]);
	//幣別
    $("#bisApplyCurr").setItems({
        item: result_s.Common_Currcy,
        format: "{value} - {key}"
    });
    
    $("#bisCostRateType").setItems({
        item: result_s.bisCostRateType,
        format: "{key}",
        space: false
    });
    $("#bisCostRateType").find("option[value=01]").attr("disabled", "disabled");
    $("#bisCostRateType").val("02");
    
    $("#bisEstimatedType").setItems({
        item: result_s.bisEstimatedType,
        format: "{key}"
    });
}

/**
 * 取得負責人、借款人名稱、隸屬集團等資訊並設定到畫面上
 */
function getAdvance(){
	var $tLMS1205S07Form03b = $("#tLMS1205S07Form03b");
	$.ajax({
		handler : "lms9535formhandler",
		type : "POST",
		dataType : "json",
		data : 
		{
			formAction : "getAdvanceData",
			custId : $tLMS1205S07Form03b.find("#custId").val(),
			dupNo : $tLMS1205S07Form03b.find("#dupNo").val()
		},
		success: function(json){
			$tLMS1205S07Form03b.setData(json.tLMS1205S07Form03b, false);
		}
	});
}

/**
 * 引進各關係戶往來彙總ThickBox
 */
function inputSearch() {
	var nowDate = new Date();
	var MM = nowDate.getMonth();
	var YY = nowDate.getFullYear();
	var SMM;
	var SYY;
	if(MM == 0){
		MM = 12;
	}
	
	if(MM ==12 ){
		SMM = MM - 5;
		YY = YY -1 ;
		SYY = YY; 
	}else if(MM > 5  && MM < 12  ){
		SMM = MM - 5;
		SYY = YY;
	}else{
		SMM = MM + 12 - 5;
		SYY = YY-1; 
	}
	
	var $tLMS1205S07Form03b = $("#tLMS1205S07Form03b");
	$tLMS1205S07Form03b.find("#queryDateS0").val(SYY);
	$tLMS1205S07Form03b.find("#queryDateS1").val(SMM);
	$tLMS1205S07Form03b.find("#queryDateE0").val(YY);
	$tLMS1205S07Form03b.find("#queryDateE1").val(MM);	
	$("#inputSearch").thickbox({ // 使用選取的內容進行彈窗
		title : i18n.lmss07a["L1205S07.thickbox8"],
		width : 550,
		height : 450,
		modal : true,
		align : 'center',
		valign: 'bottom',
		i18n:i18n.lmss07a,
		buttons : {
			"L1205S07.thickbox1" : function() {
				var $tLMS1205S07Form03b = $("#tLMS1205S07Form03b");
				var $LMS1205S07Form03 = $("#LMS1205S07Form03");				
				if($tLMS1205S07Form03b.valid()){
					var custName = $("#tLMS1205S07Form03b").find("#custName").html();
					if(custName == null || custName == undefined || custName == ""){
						// other.msg130=請點選「查詢」按鈕再執行本功能！
						CommonAPI.showMessage(i18n.lmscommom["other.msg130"]);
						return;
					}					
					if($tLMS1205S07Form03b.find("#queryDateS1").val()< 1
					|| $tLMS1205S07Form03b.find("#queryDateS1").val()> 12
					|| $tLMS1205S07Form03b.find("#queryDateE1").val()< 1
					|| $tLMS1205S07Form03b.find("#queryDateE1").val()> 12){
						CommonAPI.showMessage(i18n.lmss07a["l120v01.error3"]);
						return;
					}else if($tLMS1205S07Form03b.find("#queryDateS0").val()<=0
						   ||$tLMS1205S07Form03b.find("#queryDateE0").val()<=0){
						CommonAPI.showMessage(i18n.lmss07a["l120v01.error8"]);
						return;
					}else if($tLMS1205S07Form03b.find("#queryDateE0").val()-
						     $tLMS1205S07Form03b.find("#queryDateS0").val()<0){
						CommonAPI.showMessage(i18n.lmss07a["l120v01.error9"]);
						return;
					}else if(($tLMS1205S07Form03b.find("#queryDateE0").val()-
						      $tLMS1205S07Form03b.find("#queryDateS0").val()==0) &&
							 ($tLMS1205S07Form03b.find("#queryDateE1").val()-
						      $tLMS1205S07Form03b.find("#queryDateS1").val()<0)
							 ){
						CommonAPI.showMessage(i18n.lmss07a["l120v01.error9"]);
						return;		
					}else{
						$.thickbox.close();
				  		$.ajax({
				  			handler : "lms9535formhandler",
				  			type : "POST",
				  			dataType : "json",
				  			data : 
				  			{
				  				formAction : "saveL120s04a2",
								LMS1205S07Form03 : JSON.stringify($LMS1205S07Form03.serializeData()),
								custId : $tLMS1205S07Form03b.find("#custId").val(),
								dupNo : $tLMS1205S07Form03b.find("#dupNo").val(),
								custName : custName,
				  				queryDateS0 : $tLMS1205S07Form03b.find("#queryDateS0").val(),
				  				queryDateS1 : $tLMS1205S07Form03b.find("#queryDateS1").val(),
				  				queryDateE0 : $tLMS1205S07Form03b.find("#queryDateE0").val(),
				  				queryDateE1 : $tLMS1205S07Form03b.find("#queryDateE1").val(),
								mainId : $("#mainId").val()
				  			},
				  			success : function(json) {
								var $LMS1205S07Form03 = $("#LMS1205S07Form03");
				  				$LMS1205S07Form03.setData(json.LMS1205S07Form03);
								$("#mainId").val(json.mainId);
								uA_1_8_1();
				  				//$LMS1205S07Form03.find("#gridview_A-1-8-1").trigger("reloadGrid");								
				  			}
				  		});										
					}
				}			
			},
			// "刪除本頁": function() {alert("刪除本頁");},
			"L1205S07.thickbox2" : function() {
				 API.confirmMessage(i18n.def['flow.exit'], function(res){
						if(res){
							$.thickbox.close();
						}
			        });
			}
		}
	});
}

/**
 * 計算集團/關係企業合計
 */
function setTotal(){
	var count=$("#gridview_A-1-8-1").jqGrid('getGridParam','records');
	if(count == 0){
		CommonAPI.showMessage(i18n.lmss07a["L1205S07.error14"]);
		return;
	}	
	var $LMS1205S07Form03 = $("#LMS1205S07Form03");
	$.ajax({
		handler : "lms9535formhandler",
		type : "POST",
		dataType : "json",
		action : "saveTotal",
		data : {
			mainId : $("#mainId").val(),
			LMS1205S07Form03 : JSON.stringify($LMS1205S07Form03.serializeData()),
			queryDateS : $LMS1205S07Form03.find("#queryDateS").html(),
			queryDateE : $LMS1205S07Form03.find("#queryDateE").html()
		},
		success : function(json) {
			$LMS1205S07Form03.setData(json.LMS1205S07Form03);
			//$LMS1205S07Form03.find("#gridview_A-1-8-1").trigger("reloadGrid");
			uA_1_8_1();
		}
	});	
}

/**
 * 查詢並開啟往來實績彙總表ThickBox
 */
function tL120s04b(cellvalue, options, rowObject){
	var oid = rowObject.oid;
	// 進行查詢 
	$.ajax({		//查詢主要借款人資料
		handler : "lms9535formhandler",
		type : "POST",
		dataType : "json",
		action : "queryL120s04b",
		data : {
			oid : oid,
			custName : $("#tLMS1205S07Form03b").find("#custName").html(),
			mainId : $("#mainId").val()
		},
		success : function(json) {
			var $formL120s04b = $("#formL120s04b");

			/*
			 * var grpGrrd = $formL120s04b.find("#grpGrrd option:selected").val();
			// 建霖說：grpNo可以不用，只要集團評等(grpGrrd)符合條件就顯示 Miller eddedat 2012/11/27
			if(grpGrrd == "1" || grpGrrd == "2" || grpGrrd == "3" || grpGrrd == "4" || grpGrrd == "5"){
				// 顯示屬主要集團企業...
				$formL120s04b.find(".spectialHide").show();
			}else{
				// 隱藏屬主要集團企業...
				$formL120s04b.find(".spectialHide").hide();
			}*/
			
			// J-107-0087-001 Web
			// e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。
			var grpYear = json.formL120s04b.grpYear;
			var grpGrrd = json.formL120s04b.grpGrrd;
			if (grpYear) {
				// 判斷2017以後為新版，之前為舊版
				if (parseInt(grpYear, 10) >= 2017) {

					var obj = CommonAPI
							.loadCombos([ "GroupGrade2017" ]);

					// 評等等級
					$("#grpGrrd").setItems({
						item : obj.GroupGrade2017,
						format : "{key}"
					});

					// 建霖說：grpNo可以不用，只要集團評等(grpGrrd)符合條件就顯示 Miller
					// eddedat 2012/11/27
					if (grpGrrd == "1" || grpGrrd == "2"
							|| grpGrrd == "3" || grpGrrd == "4"
							|| grpGrrd == "5" || grpGrrd == "6"
							|| grpGrrd == "7") {
						// 顯示屬主要集團企業...
						$formL120s04b.find(".spectialHide").show();
					} else {
						// 隱藏屬主要集團企業...
						$formL120s04b.find(".spectialHide").hide();
					}
				} else {

					var obj = CommonAPI.loadCombos([ "GroupGrade" ]);

					// 評等等級
					$("#grpGrrd").setItems({
						item : obj.GroupGrade,
						format : "{key}"
					});

					// 建霖說：grpNo可以不用，只要集團評等(grpGrrd)符合條件就顯示 Miller
					// eddedat 2012/11/27
					if (grpGrrd == "1" || grpGrrd == "2"
							|| grpGrrd == "3" || grpGrrd == "4"
							|| grpGrrd == "5") {
						// 顯示屬主要集團企業...
						$formL120s04b.find(".spectialHide").show();
					} else {
						// 隱藏屬主要集團企業...
						$formL120s04b.find(".spectialHide").hide();
					}
				}

			} else {

				var obj = CommonAPI.loadCombos([ "GroupGrade" ]);

				// 評等等級
				$("#grpGrrd").setItems({
					item : obj.GroupGrade,
					format : "{key}"
				});

				// 如果沒有評等年度，以舊版執行
				// 建霖說：grpNo可以不用，只要集團評等(grpGrrd)符合條件就顯示 Miller eddedat
				// 2012/11/27
				if (grpGrrd == "1" || grpGrrd == "2" || grpGrrd == "3"
						|| grpGrrd == "4" || grpGrrd == "5") {
					// 顯示屬主要集團企業...
					$formL120s04b.find(".spectialHide").show();
				} else {
					// 隱藏屬主要集團企業...
					$formL120s04b.find(".spectialHide").hide();
				}
			}

			$formL120s04b.setData(json.formL120s04b);
			// J-111-0052 修改借戶暨關係戶與本行往來實績彙總表
            var showHins = json.formL120s04b.showHins;
            if(showHins == "Y"){
                $formL120s04b.find("#hinsDiv").show();
            } else {
                $formL120s04b.find("#hinsDiv").hide();
            }
			var grpNo = $formL120s04b.find("#grpNo").html();
			
				$("#tL120s04b").thickbox({ // 使用選取的內容進行彈窗
					title : i18n.lmss07a["L1205S07.grid43"],
					width : 965,
					height : 450,
					modal : true,
					i18n:i18n.def,
					buttons : {
						"saveData" : function() {
							var docDate6 = $("#docDate6").val();
							$("#_docDateYear").val(docDate6.substring(0, 4));
							$("#_docDateMonth").val(docDate6.substring(docDate6.indexOf("~")+1, docDate6.length-1));
							
							$("#tL120s04bDate").thickbox({
								title : "",
								width : 800,
								height : 200,
								modal : false,
								i18n:i18n.def,
								buttons : {
									"sure": function(){
										if(!$("#tL120s04bDateTmpForm").find("#_docDateYear,#_docDateMonth").valid()){
											return false;
										}
										var beginMonth = 1;
										var endMonth = parseInt($("#_docDateMonth").val(), 10);
										var year = parseInt($("#_docDateYear").val(), 10);										
										$("#docDate3,#docDate6").val(year+"/"+beginMonth+"~"+endMonth+"月");	
										$.thickbox.close();
										
										var demandAmt = RemoveStringComma($formL120s04b.find("#demandAmt").val());
										var megaAvgAmt = RemoveStringComma($formL120s04b.find("#megaAvgAmt").val());
										if(demandAmt == undefined || demandAmt == null || demandAmt == ""){
											// 沒設值自動補零
											demandAmt = 0;
											$formL120s04b.find("#demandAmt").val(demandAmt);
										}
										if(megaAvgAmt == undefined || megaAvgAmt == null || megaAvgAmt == ""){
											// 沒設值自動補零
											megaAvgAmt = 0;
											$formL120s04b.find("#megaAvgAmt").val(megaAvgAmt);
										}							
										if(megaAvgAmt == "0"){
											$formL120s04b.find("#demandAvgRate").val("N.A.");
										}else{
											$formL120s04b.find("#demandAvgRate").val
												(Math.round((demandAmt/megaAvgAmt)*10000)/100);
										}							
										if($formL120s04b.valid()){
											$formL120s04b.find(".numeric").each(function(i){
												$(this).val(RemoveStringComma($(this).val()));
											});
											// 進行報酬率計算
											for(var i=1; i<=6; i++){
												// D利潤貢獻(TWD)
												
												//var profitAmt = ($formL120s04b.find("#profitAmt" + i).val() == undefined || 
												//$formL120s04b.find("#profitAmt" + i).val() == null || 
												//$formL120s04b.find("#profitAmt" + i).val() == "")? 0 : parseInt($formL120s04b.find("#profitAmt" + i).val(),10);
												var profitAmt = 0;									
												if($formL120s04b.find("#profitAmt" + i).val() == undefined || 
												$formL120s04b.find("#profitAmt" + i).val() == null || 
												$formL120s04b.find("#profitAmt" + i).val() == ""){
													profitAmt = 0;
													$formL120s04b.find("#profitAmt" + i).val(profitAmt);
												}else {
													profitAmt = parseInt($formL120s04b.find("#profitAmt" + i).val(),10);
												}								
																								
												// A平均授信(TWD)
												//var avgLoanAmt = ($formL120s04b.find("#avgLoanAmt" + i).val() == undefined || 
												//$formL120s04b.find("#avgLoanAmt" + i).val() == null || 
												//$formL120s04b.find("#avgLoanAmt" + i).val() == "")? 0 : parseInt($formL120s04b.find("#avgLoanAmt" + i).val(),10);
												
												var avgLoanAmt = 0;
												if($formL120s04b.find("#avgLoanAmt" + i).val() == undefined || 
												$formL120s04b.find("#avgLoanAmt" + i).val() == null || 
												$formL120s04b.find("#avgLoanAmt" + i).val() == ""){
													avgLoanAmt = 0;
													$formL120s04b.find("#avgLoanAmt" + i).val(avgLoanAmt);
												} else {
													avgLoanAmt = parseInt($formL120s04b.find("#avgLoanAmt" + i).val(),10);
												}
												
												
												// B應收帳款無追索買方承購平均餘額(TWD)
												//var rcvBuyAvgAmt = ($formL120s04b.find("#rcvBuyAvgAmt" + i).val() == undefined || 
												//$formL120s04b.find("#rcvBuyAvgAmt" + i).val() == null || 
												//$formL120s04b.find("#rcvBuyAvgAmt" + i).val() == "")? 0 : parseInt($formL120s04b.find("#rcvBuyAvgAmt" + i).val(),10);
												
												var rcvBuyAvgAmt = 0;
												if($formL120s04b.find("#rcvBuyAvgAmt" + i).val() == undefined || 
												$formL120s04b.find("#rcvBuyAvgAmt" + i).val() == null || 
												$formL120s04b.find("#rcvBuyAvgAmt" + i).val() == ""){
													rcvBuyAvgAmt = 0;
													$formL120s04b.find("#rcvBuyAvgAmt" + i).val(rcvBuyAvgAmt);
												}else {
													rcvBuyAvgAmt = parseInt($formL120s04b.find("#rcvBuyAvgAmt" + i).val(),10);
												}						
												
												// C應收帳款無追索權賣方融資平均餘額(TWD)
												//var rcvSellAvgAmt = ($formL120s04b.find("#rcvSellAvgAmt" + i).val() == undefined || 
												//$formL120s04b.find("#rcvSellAvgAmt" + i).val() == null || 
												//$formL120s04b.find("#rcvSellAvgAmt" + i).val() == "")? 0 : parseInt($formL120s04b.find("#rcvSellAvgAmt" + i).val(),10);
												
												var rcvSellAvgAmt = 0;
												if($formL120s04b.find("#rcvSellAvgAmt" + i).val() == undefined || 
												$formL120s04b.find("#rcvSellAvgAmt" + i).val() == null || 
												$formL120s04b.find("#rcvSellAvgAmt" + i).val() == ""){
													rcvSellAvgAmt = 0;
													$formL120s04b.find("#rcvSellAvgAmt" + i).val(rcvSellAvgAmt);
												} else {
													rcvSellAvgAmt = parseInt($formL120s04b.find("#rcvSellAvgAmt" + i).val(),10);
												}
												
												// 報酬率% = D/(A-B+C)
												if((avgLoanAmt - rcvBuyAvgAmt + rcvSellAvgAmt) != 0){
													// 四捨五入取到小數點兩位																										
													if(i==3 || i == 6){
														//資料需要年化
														var num = new Number(profitAmt / (avgLoanAmt - rcvBuyAvgAmt + rcvSellAvgAmt)/(endMonth - beginMonth + 1)* 12 * 100);
														$formL120s04b.find("#profitRate" + i).html(parseFloat(num.toFixed(2)));
													}else{
														var num = new Number(profitAmt / (avgLoanAmt - rcvBuyAvgAmt + rcvSellAvgAmt) * 100);
														$formL120s04b.find("#profitRate" + i).html(parseFloat(num.toFixed(2)));
													}
																																	
												}
											}							
											// 進行儲存
											$.ajax({
												handler : "lms9535formhandler",
												type : "POST",
												dataType : "json",
												action : "saveL120s04b",
												data : {
													oid : oid,
													formL120s04b : JSON.stringify($formL120s04b.serializeData())
												},
												success : function(json) {
													//$.thickbox.close();
													$.thickbox.close();
													CommonAPI.showMessage(json.NOTIFY_MESSAGE);
												}
											});
										}
									}
								}
							
							});
						},
						"print" : function(){
							if($("#gridview_A-1-8-2").jqGrid('getGridParam','records') <= 0){
								// 報表無資料
								CommonAPI.showErrorMessage(i18n.msg('EFD0002'));
							}else{
								var count = 0;								
								var $tLMS1205S07Form03b = $("#tLMS1205S07Form03b");
								let urlStr = "../simple/FileProcessingService"; // 預設
								if($("#source").val() == "fms"){   
								    urlStr = "../../simple/FileProcessingService";
								}
								$.form.submit({
							        url: urlStr,
							        target: "_blank",
							        data: {
							        	mainId : $("#mainId").val(),
										custId : $tLMS1205S07Form03b.find("#custId").val(),
										dupNo : $tLMS1205S07Form03b.find("#dupNo").val(),
										custName : $tLMS1205S07Form03b.find("#custName").html(),
							        	rptOid : "R24" + "^" + "",
										fileDownloadName : "l120r01.pdf",
										serviceName : "lms9535r01rptservice"
							        }
							    });		
							}							
						},
						"close" : function() {
							 API.confirmMessage(i18n.def['flow.exit'], function(res){
									if(res){
										$.thickbox.close();
									}
						        });
						}
					}
				});			
		}
	});		
}

/**
 * 產生往來實績彙總表
 */
function createReport(){
	var count=$("#gridview_A-1-8-1").jqGrid('getGridParam','records');
	if(count > 0){
		// L1205S07.confirm4=執行引進後會刪除已存在之借戶暨關係戶與本行往來實績彙總表，是否確定執行？
		CommonAPI.confirmMessage(i18n.lmss07a["L1205S07.confirm4"], function(b){
			if (b) {					
				//是的function
				$.ajax({
					handler : "lms9535formhandler",
					type : "POST",
					dataType : "json",
					data : {
						formAction : "deleteL120s04b",
						mainId : $("#mainId").val()
					},
					success : function(json) {
						$.thickbox.close();
						// 開始產生實績彙總表
						importReport();
					}
				});				
			}				
		});		
	}else{
		// 報表無資料
		CommonAPI.showErrorMessage(i18n.msg('EFD0002'));		
	}
}

/**
 * 刪除往來實績彙總表
 */
function deleteL120s04a(){
	CommonAPI.confirmMessage(i18n.def["action_003"], function(b){
		if (b) {					
			//是的function
			$.ajax({
				handler : "lms9535formhandler",
				type : "POST",
				dataType : "json",
				data : {
					formAction : "deleteL120s04b",
					mainId : $("#mainId").val()
				},
				success : function(json) {
					// 更新實績彙總表Grid
					uA_1_8_2();
					//$("#LMS1205S07Form03").find("#gridview_A-1-8-2").trigger("reloadGrid");					
				}
			});				
		}				
	});
}

/**
 * 執行產生往來實績彙總表
 */
function importReport(){
	var $LMS1205S07Form03 = $("#LMS1205S07Form03");
	$.ajax({
		handler : "lms9535formhandler",
		type : "POST",
		dataType : "json",
		data : {
			formAction : "importL120s04b",
			mainId : $("#mainId").val(),
			custId : $LMS1205S07Form03.find("#custId").html(),
			dupNo : $LMS1205S07Form03.find("#dupNo").html(),
		  	queryDateS : $LMS1205S07Form03.find("#queryDateS").html(),
		  	queryDateE : $LMS1205S07Form03.find("#queryDateE").html()			
			
		},
		success : function(json) {
			// 更新實績彙總表Grid
			uA_1_8_2();
			//$("#LMS1205S07Form03").find("#gridview_A-1-8-2").trigger("reloadGrid");
		}
	});	
}

/**
 * 取消列印關係戶名單
 */
function cancelPrint(){
	var rows = $("#gridview_A-1-8-1").getGridParam('selarrrow');
	var list = "";
	var sign = ",";
	for (var i=0;i<rows.length;i++){	//將所有已選擇的資料存進變數list裡面
		if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0){
			var data = $("#gridview_A-1-8-1").getRowData(rows[i]);
			list += ((list == "") ? "" : sign ) + data.oid;
		}
	}
	if (list == "") {
		CommonAPI.showMessage(i18n.lmss07a["L1205S07.alert1"]);
		return;
	}	

	API.confirmMessage(i18n.lmss07a["L1205S07.confirm1"],function(b){
		if(b){
			//是的function
			$.ajax({
				handler : "lms9535formhandler",
				type : "POST",
				dataType : "json",
				action : "cancelPrint",
				data : {
					listOid : list,
					mainId : $("#mainId").val()
				},
				success : function(json) {
					uA_1_8_1();
					//$("#LMS1205S07Form03").find("#gridview_A-1-8-1").trigger("reloadGrid");
				}
			});
		}else{
			//否的function
			//CommonAPI.showMessage(i18n.lmss07a["L1205S07.alert3"]);
		}
	})	
}

/**
 * 恢復已取消列印關係戶名單
 */
function undoPrint(){
	var rows = $("#gridview_A-1-8-1").getGridParam('selarrrow');
	var list = "";
	var sign = ",";
	for (var i=0;i<rows.length;i++){	//將所有已選擇的資料存進變數list裡面
		if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0){
			var data = $("#gridview_A-1-8-1").getRowData(rows[i]);
			list += ((list == "") ? "" : sign ) + data.oid;
		}
	}
	if (list == "") {
		CommonAPI.showMessage(i18n.lmss07a["L1205S07.alert1"]);
		return;
	}
	API.confirmMessage(i18n.lmss07a["L1205S07.confirm2"],function(b){
		if(b){
			//是的function
			$.ajax({
				handler : "lms9535formhandler",
				type : "POST",
				dataType : "json",
				action : "undoPrint",
				data : {
					listOid : list,
					mainId : $("#mainId").val()
				},
				success : function(json) {
					uA_1_8_1();
					//$("#LMS1205S07Form03").find("#gridview_A-1-8-1").trigger("reloadGrid");
				}
			});
		}else{
			//否的function
			//CommonAPI.showMessage(i18n.lmss07a["L1205S07.alert4"]);
		}
	})	
}

/**
 * 刪除所有關係戶往來彙總表
 */
function deleteAllCust(){
	API.confirmMessage(i18n.lmss07a["L1205S07.confirm3"],function(b){
		if(b){
			//是的function
			$.ajax({
				handler : "lms9535formhandler",
				type : "POST",
				dataType : "json",
				action : "deleteL120s04a",
				data : {
					mainId : $("#mainId").val()
				},
				success : function(json) {
					var $LMS1205S07Form03 = $("#LMS1205S07Form03");
					$LMS1205S07Form03.setData(json.LMS1205S07Form03,false);
					uA_1_8_1();
					//$LMS1205S07Form03.find("#gridview_A-1-8-1").trigger("reloadGrid");

					//J-113-0183 清空RORWA計算
					resetRORWAContent();
				}
			});
		}else{
			//否的function
			//CommonAPI.showMessage(i18n.lmss07a["L1205S07.alert5"]);
		}
	})
}

function proPertyFormatter(cellvalue,otions,rowObject){
	 //登錄授信科目的格式化
	var itemName='';	
	if(cellvalue == null || 	
	   cellvalue == undefined || 
	   cellvalue == ""){
		//授信科目為空!!
	}else{
		var list = cellvalue.split("|");
		if(cellvalue){
		itemName = i18n.lms1405s02["L140M01a.type"+list[0]];
			if(cellvalue.length > 1){
	  			for(var i =1; i< list.length ;i++ ){
	  				var itemone = i18n.lms1405s02["L140M01a.type"+list[i]];
	  				itemName = itemName +"、"+itemone; 
	  			}
			}
		}
	} 		
 	return itemName;
}

/**
 * 關係戶往來彙總表明細ThickBox
 */
function test999(oid) {
	$("#borrower-data999").thickbox({ // 使用選取的內容進行彈窗
		title : i18n.lmss07a["L1205S07.thickbox9"],
		width : 960,
		height : 500,
		modal : true,
		i18n:i18n.def,
		buttons : {
			"reQuery" : function() {
				var $LMS1205S07Form03 = $("#LMS1205S07Form03");
				$.ajax({
					handler : "lms9535formhandler",
					type : "POST",
					dataType : "json",
					action : "rQueryL120s04a",
					data : {
						oid : oid,
						mainId : $("#mainId").val(),
		  				custId : $("#tLMS1205S07Form03").find("#custId").html(),
		  				dupNo : $("#tLMS1205S07Form03").find("#dupNo").html(),
		  				custName : $("#tLMS1205S07Form03").find("#custName").html(),
		  				queryDateS : $LMS1205S07Form03.find("#queryDateS").html(),
		  				queryDateE : $LMS1205S07Form03.find("#queryDateE").html()
					},
					success : function(obj) {
						var $tLMS1205S07Form03 = $("#tLMS1205S07Form03");	
						$tLMS1205S07Form03.setData(obj);
						for(o in obj.tLMS1205S07Form03.custRelation){
							$tLMS1205S07Form03.find("[name=custRelation]").each(function(i){
								if($(this).val() == obj.tLMS1205S07Form03.custRelation[o]){
									$(this).attr("checked",true);
								}
							});
						}
						$("LMS1205S07Form03").setData(obj.LMS1205S07Form03,false);
						uA_1_8_1();						
						//$("#gridview_A-1-8-1").trigger("reloadGrid");
					}
				});
			},
			"saveData" : function() {
				var $tLMS1205S07Form03 = $("#tLMS1205S07Form03");
				var $LMS1205S07Form03 = $("#LMS1205S07Form03");
				var count=$("#gridview_A-1-8-1").jqGrid('getGridParam','records');
				if(count == 0){
					CommonAPI.showMessage(i18n.lmss07a["L1205S07.error14"]);
					return;
				}
				var list = "";
				var sign = ",";
				if($tLMS1205S07Form03.valid()){
					var checkSub = false;
					var kind = 0;
					var checkCou = 0;
					$tLMS1205S07Form03.find("[name=custRelation]:checkbox:checked").each(function(i){
						if($(this).val() == 3){
							checkSub = true;
							kind = 1;
						}else if($(this).val() == 4){
							checkSub = true;
							kind = 2;
						}
						list += ((list == "") ? "" : sign ) + $(this).val();
						checkCou++;
					});
					if(checkCou == 0){
						CommonAPI.showMessage(i18n.lmss07a["L1205S07.error15"]);
						return;
					}
					if(checkSub && list.length > 1){
						if(kind == 1){
							// 集團企業合計
							CommonAPI.showMessage(i18n.lmss07a["L1205S07.error13"]);
						}else if(kind == 2){
							// 關係企業合計
							CommonAPI.showMessage(i18n.lmss07a["L1205S07.error12"]);
						}
						return;
					}
					$.ajax({		//查詢主要借款人資料
						handler : "lms9535formhandler",
						type : "POST",
						dataType : "json",
						action : "saveL120s04a",
						data : {
							tLMS1205S07Form03 : JSON.stringify($tLMS1205S07Form03.serializeData()),
							mainId : $("#mainId").val(),
			  				custId : $("#tLMS1205S07Form03").find("#custId").html(),
			  				dupNo : $("#tLMS1205S07Form03").find("#dupNo").html(),
			  				queryDateS : $LMS1205S07Form03.find("#queryDateS").html(),
			  				queryDateE : $LMS1205S07Form03.find("#queryDateE").html(),
							oid : oid,
							list : list
						},
						success : function(json) {
							uA_1_8_1();
							//$LMS1205S07Form03.find("#gridview_A-1-8-1").trigger("reloadGrid");
							oid = json.newOid;
						}
					});
					//$.thickbox.close();					
				}
			},
			// "刪除本頁": function() {alert("刪除本頁");},
			"close" : function() {
				 API.confirmMessage(i18n.def['flow.exit'], function(res){
					if(res){
						$.thickbox.close();
					}
		         });
			}
		}
	});
}

/**
 * 手動新增關係戶往來彙總表
 */
function addData() {
	$("#formAdd").reset();
	var count=$("#gridview_A-1-8-1").jqGrid('getGridParam','records');
	if(count == 0){
		CommonAPI.showMessage(i18n.lmss07a["L1205S07.error14"]);
		return;
	}	
	$("#addData").find("#showSel03").hide();
		$("#addData").thickbox({ // 使用選取的內容進行彈窗
		title : i18n.lmss07a["L1205S07.thickbox11"],
		width : 800,
		height : 200,
		modal : true,
		align : 'center',
		valign: 'bottom',
		i18n:i18n.lmss07a,
		buttons : {
			"L1205S07.thickbox1" : function() {				
				var gridCust = $("#gridview_A-1-8-1").getCol("custId");
				var gridName = $("#gridview_A-1-8-1").getCol("custName");
				if(!$("#selCus03").children().is("option")){
		  			CommonAPI.showMessage(i18n.lmss07a["L1205S07.alert7"]);
		  			return;					
				}else if($("#selCus03 option:eq(0)").attr("selected")){
		  			CommonAPI.showMessage(i18n.lmss07a["L1205S07.alert6"]);
		  			return;
				}else{
			  		var	custIdAll = $("#selCus03 option:selected").val();
					var custIdOnly = custIdAll.substring(0,custIdAll.length-1) + " " + custIdAll.substring(custIdAll.length-1);
					var	custName = $("#selCus03 option:selected").html();
					var custNameOnly = custName.substring(custIdAll.length+3);
/*
					var countCustId = 0;
					var countCustName = 0;					
					for(o in gridCust){
						if(custIdOnly == gridCust[o]){
							countCustId++;
						}
					}
*/
/*
					for(p in gridName){									
						if(custNameOnly == FtoH(gridName[p])){
							countCustName++;
						}
					}
*/
/*
					if(countCustId >= 1){	//&& countCustName >= 1
			  			CommonAPI.showMessage(i18n.lmss07a["L1205S07.alert8"]);
			  			return;
					}
*/					
					var $LMS1205S07Form03 = $("#LMS1205S07Form03");
			  		$.ajax({
			  			handler : "lms9535formhandler",
			  			type : "POST",
			  			dataType : "json",
			  			data : 
			  			{
			  				formAction : "addL120s04a",
			  				custIdAll : custIdAll,
							custName : custName,
							mainId : $("#mainId").val(),
			  				queryDateS : $LMS1205S07Form03.find("#queryDateS").html(),
			  				queryDateE : $LMS1205S07Form03.find("#queryDateE").html()
			  			},
			  			success : function(json) {
							var $tLMS1205S07Form03 = $("#tLMS1205S07Form03");		  				
			  				$tLMS1205S07Form03.setData(json);
			  				$tLMS1205S07Form03.find("#createBY2").html(DOMPurify.sanitize(i18n.lmss07a["L1205S07.createBY2"]));
							uA_1_8_1();
			  				//$("#LMS1205S07Form03").find("#gridview_A-1-8-1").trigger("reloadGrid");
			  				test999("");
			  			}
			  		});	  		
					$.thickbox.close();			  			
		  		}				
			},
			// "刪除本頁": function() {alert("刪除本頁");},
			"L1205S07.thickbox2" : function() {
				 API.confirmMessage(i18n.def['flow.exit'], function(res){
						if(res){
							$.thickbox.close();
						}
			        });
			}
		}
	});
}

/**
 * 讀取往來彙總以進行修改
 * @param cellvalue
 * @param options
 * @param rowObject
 */
function openDoc3(cellvalue, options, rowObject) {
	ilog.debug(rowObject);
	$("#tLMS1205S07Form03").reset();
	$.ajax({
		handler : "lms9535formhandler",
		type : "POST",
		dataType : "json",
		action : "queryL120s04a",
		data : {
			oid : rowObject.oid
		},
		success : function(obj) {
			var $tLMS1205S07Form03 = $("#tLMS1205S07Form03");
			$tLMS1205S07Form03.setData(obj);
			for(o in obj.tLMS1205S07Form03.custRelation){
				$tLMS1205S07Form03.find("[name=custRelation]").each(function(i){
					var $this = $(this);
					if($this.val() == obj.tLMS1205S07Form03.custRelation[o]){
						$this.attr("checked",true);
					}
				});
			}
			$tLMS1205S07Form03.setData(obj.LMS1205S07Form03,false);
			test999(obj.tLMS1205S07Form03.oid);
		}
	});	
}

function gridviewCust(){
	var gridView = $("#gridviewCust").iGrid({
		handler: 'lms9535gridhandler',
		//height: 345, //for 15 筆
		height: "230px", //for 10 筆
		//autoHeight: true,
		width: "100%",
		sortname : 'custName',
		postData : {
			formAction : "queryL120s01aById",
			custId : $("#custSearch").find("#searchId").val(),
			rowNum:10
		},
		caption: "&nbsp;",
		hiddengrid : false,
		// autofit: false,
		autowidth:true,
		colModel: [{
			  colHeader: i18n.lmss07a["L1205S07.grida"],//"借款人名稱"
			  name: 'custName',
			  width: 100,
			  sortable: true
		},{
		  name: 'oid',
		  hidden: true
		}],
			ondblClickRow: function(rowid){
		}
	});
}

/**
 * 關係戶往來實績彙總表ExcelGrid(更新)
 */
function ugridviewPare(){
    $("#gridviewPare").jqGrid("setGridParam", {
        postData: {
            formAction: "queryfile",
            mainId : $("#mainId").val(),
            rowNum : 15
        },
        search: true
    }).trigger("reloadGrid");
}

/**
 * 關係戶往來彙總表Grid(更新)
 */
function uA_1_8_1(){
    $("#gridview_A-1-8-1").jqGrid("setGridParam", {
        postData: {
            formAction: "queryL120s04a",
            mainId : $("#mainId").val(),
            rowNum:30
        },
        search: true
    }).trigger("reloadGrid");
}

/**
 * 關係戶往來彙總表Grid
 */
function A_1_8_1() {
	var gridA181 = $("#gridview_A-1-8-1")
			.iGrid({
				needPager: false,
				handler : 'lms9535gridhandler',
				// height: 345, //for 15 筆
				height : "300px", // for 10 筆
				// autoHeight: true,
				width : "100%",
				postData : {
					formAction : "queryL120s04a",
					mainId : $("#mainId").val()
					//rowNum:30
				},
				//rowNum:30,
				caption: "&nbsp;",
				hiddengrid : false,
				sortname : 'custRelation|profit|custId',
				sortorder:'asc|desc|asc',
				//sortname : 'custRelation',
				multiselect: true,
				//rownumbers : true,
				hideMultiselect:false,
				// autofit: false,
				autowidth : true,
				colModel : [ {
					colHeader : i18n.lmss07a["L1205S07.grid15"],//"需列印"
					name : 'prtFlag',
					align : "center",
					width : 40,
					sortable : false
				}, {
					colHeader : i18n.lmss07a["L1205S07.grid16"],//"關係戶統編"
					name : 'custId',
					width : 80,
					sortable : false,
					formatter : 'click',
					onclick : openDoc3
				}, {
					colHeader : i18n.lmss07a["L1205S07.grid17"],//"關係戶戶名"
					width : 160,
					name : 'custName',
					sortable : false
//				}, {
//					colHeader : i18n.lmss07a["L1205S07.grid18"],//"與借戶關係"
//					name : 'custRelation',
//					width : 10,
//					sortable : false,
//					align : "center"	
				}, {
					colHeader : i18n.lmss07a["L1205S07.grid18"],//"與借戶關係"
					name : 'custRelationIndex',
					width : 80,
					sortable : false,
					align : "center"
				}, 
				{
					colHeader : i18n.lmss07a["L1205S07.grid19"],//"貢獻度"
					name : 'profit',
					width : 80,
					sortable : false,
					align : "right",
					formatter : function(data) {
						if(data == null){
							return "";
						}else{
							// 加入撇節符號
							return util.addComma(data);	
						}
					}					
				}, 				
				{
					colHeader : i18n.lmss07a["L1205S07.grid20"],//"放款額度"
					name : 'loanQuota',
					width : 80,
					sortable : false,
					align : "right",
					formatter : function(data) {
						if(data == null){
							return "";
						}else{
							// 加入撇節符號
							return util.addComma(data);	
						}
					}					
				}, {
					colHeader : i18n.lmss07a["L1205S07.grid21"],//"放款餘額"
					name : 'loanAvgBal',
					width : 80,
					sortable : false,
					align : "right",
					formatter : function(data) {
						if(data == null){
							return "";
						}else{
							// 加入撇節符號
							return util.addComma(data);	
						}
					}					
				}, {
					colHeader : i18n.lmss07a["L1205S07.grid22"],//"活期存款"
					name : 'depTime',
					width : 80,
					sortable : false,
					align : "right",
					formatter : function(data) {
						if(data == null){
							return "";
						}else{
							// 加入撇節符號
							return util.addComma(data);	
						}
					}					
				}, {
		        	 colHeader: "&nbsp",//"檢核欄位",
		             name: 'chkYN',
		             width: 20,
		             sortable: false,
					 align:"center" 
		         },	{
					name : 'oid',
					hidden : true
				} ],
				ondblClickRow : function(rowid) {
					var data = gridA181.getRowData(rowid);
					openDoc3(null, null, data);
				}
			});
}

/**
 * 往來彙總實績表Grid(更新)
 */
function uA_1_8_2(){
    $("#gridview_A-1-8-2").jqGrid("setGridParam", {
        postData: {
            formAction: "queryL120s04b",
            mainId : $("#mainId").val(),
            rowNum:30
        },
        search: true
    }).trigger("reloadGrid");
}

/**
 * 往來彙總實績表Grid
 */
function A_1_8_2() {
	var gridA182 = $("#gridview_A-1-8-2")
			.iGrid({
				handler : 'lms9535gridhandler',
				// height: 345, //for 15 筆
				height : "50", // for 10 筆
				// autoHeight: true,
				width : "100%",
				postData : {
					formAction : "queryL120s04b",
					mainId : $("#mainId").val(),
					rowNum:30
				},
				rowNum:30,
				caption: "&nbsp;",
				hiddengrid : false,
				//sortname : 'custRelation',
				//multiselect: true,
				//rownumbers : true,
				//hideMultiselect:false,
				// autofit: false,
				autowidth : true,
				colModel : [ {
					colHeader : i18n.lmss07a["L1205S07.grid42"],//"報表名稱"
					width : 200,
					name : 'rptName',
					sortable : false,
					formatter : function(data) {
						// L1205S07.grid43=借戶暨關係戶與本行往來實績彙總表
						return i18n.lmss07a["L1205S07.grid43"];
					}								
				}, {
					colHeader : i18n.lmss07a["L1205S07.grid38"],//"建立日期"
					width : 200,
					name : 'createTime',
					sortable : false,
					formatter : 'click',
					onclick : tL120s04b					
				}, {
					name : 'oid',
					hidden : true
				} ],
				ondblClickRow : function(rowid) {
					var data = gridA182.getRowData(rowid);
					tL120s04b(null, null, data);
				}
			});
}

//往來彙總(產報表)
function printR14(){
	if($("#gridview_A-1-8-1").jqGrid('getGridParam','records') <= 0){
		// 報表無資料
		CommonAPI.showErrorMessage(i18n.msg('EFD0002'));
	}else{
		var count = 0;
		var $tLMS1205S07Form03b = $("#tLMS1205S07Form03b");
		var urlStr = "../simple/FileProcessingService"; // 預設
		if($("#source").val() == "fms"){    //
		    urlStr = "../../simple/FileProcessingService";
		}
		$.form.submit({
	        url: urlStr,    //"../simple/FileProcessingService",
	        target: "_blank",
	        data: {
	        	mainId : $("#mainId").val(),
				custId : $tLMS1205S07Form03b.find("#custId").val(),
				dupNo : $tLMS1205S07Form03b.find("#dupNo").val(),
				custName : $("#tLMS1205S07Form03b").find("#custName").html(),
				chairmanId : $("#tLMS1205S07Form03b").find("#chairmanId").html(),
				chairmanDupNo : $("#tLMS1205S07Form03b").find("#chairmanDupNo").html(),
				chairman : $("#tLMS1205S07Form03b").find("#chairman").html(),
				groupNo : $("#tLMS1205S07Form03b").find("#groupNo").html(),
				groupName : $("#tLMS1205S07Form03b").find("#groupName").html(),
				groupBadFlag : $tLMS1205S07Form03b.find("#groupBadFlag").val(),
				groupBadFlagText : $("#groupBadFlag").find("option:selected").text(),
	        	rptOid : "R14" + "^" + "",
				fileDownloadName : "l120r01.pdf",
				serviceName : "lms9535r01rptservice"
	        }
	    });	
	}
}

/**
 * 移除字符串中的逗號
 */
function RemoveStringComma(number){
    if (number == undefined || number == null || number == "") {
        return;
    } else {
        number = number.toString();
        if (number != undefined) {
            var pos = number.indexOf(",");
            while (pos != -1) {
                number = number.substring(0, pos) + number.substring(pos + 1, number.length);
                pos = number.indexOf(",");
            }
            return number;
        }
    }
}

/**
 * 移除字符串中的句號
 */
function RemoveStringEnd(number){
    if (number == undefined || number == null || number == "") {
        return;
    } else {
        number = number.toString();
        if (number != undefined) {
            var pos = number.indexOf(".");
            while (pos != -1) {
                number = number.substring(0, pos) + number.substring(pos + 1, number.length);
                pos = number.indexOf(".");
            }
            return number;
        }
    }
}

/**
 * J-107-0225_05097_B1001 Web e-Loan企金授信簽報書新增集團關係企業與本行授信往來條件比較表
 * 產生集團／關係企業與本行授信往來條件比較表(Excel)
 */
function creExcel2() {
	API.confirmMessage(i18n.def["confirmRun"], function(b) {
		if (b) {
			// 是的function
			$.ajax({
				handler : "lms9535formhandler",
				type : "POST",
				dataType : "json",
				action : "creExcel2",
				data : {
					mainId : $("#mainId").val(),
					type : "2",
					mCustId : $("#LMS1205S07Form03").find("#custId").val(),
					mDupNo : $("#LMS1205S07Form03").find("#dupNo").val(),
					mCustName : $("#LMS1205S07Form03").find("#custName").val()
				},
				success : function(json) {
					ugridviewPare();
				}
			});
			/*
			 * $.form.submit({ url: "../../simple/FileProcessingService",
			 * target: "_blank", data: { mainId: responseJSON.mainId,
			 * fileDownloadName: "LMS1201M01A.xls", serviceName:
			 * "lms1201xlsservice" } });
			 */
		} else {
			// 否的function
			// CommonAPI.showMessage(i18n.lmss07a["L1205S07.alert5"]);
		}
	})
}

//J-113-0183 e-Loan授信管理系統新增RORWA計算
function setCurr(){
	var curr = DOMPurify.sanitize($("select[name=bisApplyCurr]").val());
	$(".curr").html(curr);
}
function showBisCostRateType(){
    var value = DOMPurify.sanitize($("select[name=bisCostRateType]").val());
    if(value == "01"){
    	$(".showBisCostRateType01").show();
        $(".showBisCostRateType02").hide();
    } else {
    	$(".showBisCostRateType01").hide();
        $(".showBisCostRateType02").show();
    }
}
function openDirBox(boxId, height) {
    var $dirBox = $("#"+boxId);
    if(boxId=="bisCostRateTypeDirBox"){
    	if($("#bisCostRateType").val()=="01"){
		   $(".showBisCostRateType01").show();
		   $(".showBisCostRateType02").hide();
	    }else{
	       $(".showBisCostRateType01").hide();
	       $(".showBisCostRateType02").show();
	    }
    }
    $dirBox.thickbox({ // 使用選取的內容進行彈窗
        title: "",
        width: 500,
        height: height,
        modal: true,
        valign: "bottom",
        align: "center",
        i18n: i18n.def,
        buttons: {
            "close": function(){
                $.thickbox.close();
            }
        }
    });
}
//J-113-0183 計算個體/集團 預估非授信收益率
function calcBisEstimatedReturn() {
    $("#formBisDetail").find("#bisEstimatedReturn").val('');
    $("#formBisDetail").find("#bisEstimatedReturn_1").val('');

	 $.ajax({
         handler: "lms9535formhandler",
         data: $.extend($("#formBisDetail").serializeData(), {
             formAction: "calcBisEstimatedReturn"
         }),
         success: function(obj){
            $("#formBisDetail").find("#bisEstimatedReturn").val(DOMPurify.sanitize(obj.bisEstimatedReturn));
            $("#formBisDetail").find("#bisEstimatedReturn_1").val(DOMPurify.sanitize(obj.bisEstimatedReturn_1));
         }
     });
	 
}
//J-113-0183
function initRORWAGrid(){
	$("#bisFtpRateGrid").iGrid({
        handler: "lms1201gridhandler",
        needPager: false,
        multiselect: false,
        sortname: 'insCdCnm',
        sortorder: 'asc',
        postData : {
            formAction: "queryFtpRate"
        },
        colModel: [{
            colHeader: "項目",
            name: 'insCdCnm',
            align: "center",
            width: 80,
            sortable: false
        }, {
            colHeader: " ",
            name: 'insRt',
            align: "center",
            width: 20,
            sortable: false
        }]
    });
	
    $("#bisCapitalCostGrid").iGrid({
        handler: "lms1201gridhandler",
        needPager: false,
        multiselect: false,
        sortname: 'insCdCnm',
        sortorder: 'asc',
        postData : {
            formAction: "queryBisCapitalCost"
        },
        colModel: [{
            colHeader: "項目",
            name: 'insCdCnm',
            align: "center",
            width: 80,
            sortable: false
        }, {
            colHeader: "利率",
            name: 'insRt',
            align: "center",
            width: 20,
            sortable: false
        }, {
            name: 'insOrder',
            hidden: true 
        }]
    });
}
//J-113-0183 引進資金成本/FTP
function impBisFtpRate() {
    $("#bisFtpRateCurrBox").thickbox({
        title: i18n.lms9535v01['other.curr'],
        width: 100,
        height: 20,
        align: "center",
        valign: "bottom",
        i18n: i18n.def,
        open: function(){
            $("input[name='bisFtpRateCurr'][value='TWD']").attr("checked", true);
        },
        buttons: {
            "sure": function(){
                var bisFtpRateCurr = DOMPurify.sanitize($("input[name=bisFtpRateCurr]:checked").val());

                $.thickbox.close();

                $("#bisFtpRateGrid").jqGrid("setGridParam", {
                    postData: {
                        curr: bisFtpRateCurr
                    },
                    search: true
                }).trigger("reloadGrid");

                $("#impBisFtpRateThickBox").thickbox({ // 使用選取的內容進行彈窗
                    title: i18n.def['grid_selector'],
                    width: 500,
                    height: 250,
                    modal: true,
                    valign: "bottom",
                    align: "center",
                    i18n: i18n.def,
                    buttons: {
                        "sure": function(){
                            var rowId = $("#bisFtpRateGrid").getGridParam('selrow');
                            if (rowId) {
                                var data = $("#bisFtpRateGrid").getRowData(rowId);
                                $("#formBisDetail").find("#bisFtpRate").val(data.insRt);
                                $.thickbox.close();
                            } else {
                                CommonAPI.showMessage(i18n.def['grid.selrow']);
                            }
                        },
                        "cancel": function(){
                            $.thickbox.close();
                        }
                    }
                });
            },
            "cancel": function(){
                $.thickbox.close();
            }
        }
    });
}
//J-113-0183 引進資金成本/FTP
function impBisCapitalCost() {
    $("#bisFtpRateCurrBox").thickbox({
        title: i18n.lms9535v01['other.curr'],
        width: 100,
        height: 20,
        align: "center",
        valign: "bottom",
        i18n: i18n.def,
        open: function(){
            $("input[name='bisFtpRateCurr'][value='TWD']").attr("checked", true);
        },
        buttons: {
            "sure": function(){
                var bisFtpRateCurr = DOMPurify.sanitize($("input[name=bisFtpRateCurr]:checked").val());

                $.thickbox.close();

                $("#bisCapitalCostGrid").jqGrid("setGridParam", {
                    postData: {
                        curr: bisFtpRateCurr
                    },
                    search: true
                }).trigger("reloadGrid");

                $("#impBisCapitalCostThickBox").thickbox({ // 使用選取的內容進行彈窗
                    title: i18n.def['grid_selector'],
                    width: 500,
                    height: 250,
                    modal: true,
                    valign: "bottom",
                    align: "center",
                    i18n: i18n.def,
                    buttons: {
                        "sure": function(){
                            var rowId = $("#bisCapitalCostGrid").getGridParam('selrow');
                            if (rowId) {
                                var data = $("#bisCapitalCostGrid").getRowData(rowId);
                                $("#formBisDetail").find("#bisFtpRate").val(data.insRt);
                                $.thickbox.close();
                            } else {
                                CommonAPI.showMessage(i18n.def['grid.selrow']);
                            }
                        },
                        "cancel": function(){
                            $.thickbox.close();
                        }
                    }
                });
            },
            "cancel": function(){
                $.thickbox.close();
            }
        }
    });
}
//J-113-0183 引進非授信收益預估對象
function impBisEstimatedReturn() {
	var newApplyAmt = DOMPurify.sanitize($("#newApplyAmt").val()) ? DOMPurify.sanitize($("#newApplyAmt").val()) : 0;
	var bisTwdRate = DOMPurify.sanitize($("#bisTwdRate").val()) ? DOMPurify.sanitize($("#bisTwdRate").val()) : 0;
	$.ajax({
		handler : "lms9535formhandler",
		type : "POST",
		dataType : "json",
		data : {
			formAction : "impBisEstimatedReturn",
			mainId : $("#mainId").val(),
			queryDateE0 : $("#formBisDetail").find("#bisEstimatedDateYear").val(),
			queryDateE1 : $("#formBisDetail").find("#bisEstimatedDateMonth").val(),
			kind : "A",  //風控處改個體(借款人)；集團(借款人+集團企業) //$("#formBisDetail").find("#bisEstimatedType").val()
			custId : DOMPurify.sanitize($("#custId").val()),
			dupNo : DOMPurify.sanitize($("#dupNo").val()),
			newApplyAmt : newApplyAmt,
			bisTwdRate : bisTwdRate	
		},
		success : function(obj120) {
		    $("#formBisDetail").find("#bisEstimatedType").val('A');
			$("#formBisDetail").find("#bisNoneLoanProfit").val(DOMPurify.sanitize(obj120.bisNoneLoanProfit));
			$("#formBisDetail").find("#bisLoanBal").val(DOMPurify.sanitize(obj120.bisLoanBal));
			$("#formBisDetail").find("#bisFactAmtIncrease").val(DOMPurify.sanitize(obj120.bisFactAmtIncrease));
			$("#formBisDetail").find("#bisEstimatedReturn").val(DOMPurify.sanitize(obj120.bisEstimatedReturn));
			$("#formBisDetail").find("#bisNoneLoanProfit_1").val(DOMPurify.sanitize(obj120.bisNoneLoanProfit_1));
            $("#formBisDetail").find("#bisLoanBal_1").val(DOMPurify.sanitize(obj120.bisLoanBal_1));
            $("#formBisDetail").find("#bisFactAmtIncrease_1").val(DOMPurify.sanitize(obj120.bisFactAmtIncrease_1));
            $("#formBisDetail").find("#bisEstimatedReturn_1").val(DOMPurify.sanitize(obj120.bisEstimatedReturn_1));
		}
	});
}
//J-113-0183 額度幣別折台幣匯率
function impBisTwdRate(){
	$.ajax({
		handler : "lms9535formhandler",
		type : "POST",
		dataType : "json",
		data : {
			formAction : "impBisTwdRate",
			curr : DOMPurify.sanitize($("select[name=bisApplyCurr]").val())
		},
		success : function(obj) {
			$("#bisTwdRate").val(DOMPurify.sanitize(obj.bisTwdRate));
			$("#rateDate").val(DOMPurify.sanitize(obj.rateDate));
		}
	});
}
//J-113-0183 計算風險抵減後暴險額
function calcRiskOffInsAmt(){
	var newCreditLimitAmt = DOMPurify.sanitize($("#newCreditLimitAmt").val());
	var qualCollDiscVal = DOMPurify.sanitize($("#qualCollDiscVal").val());
	if(newCreditLimitAmt && qualCollDiscVal){
		var riskOffInsAmt = newCreditLimitAmt - qualCollDiscVal;
		if(!isNaN(riskOffInsAmt)){
			if(riskOffInsAmt > 0){
				$("#riskOffInsAmt").val(riskOffInsAmt);
			}else{
				$("#riskOffInsAmt").val(0);
			}
		}
	}
}
//J-113-0183 RORWA計算結果區塊內容之計算
function calcResult(){
	$.ajax({
		handler : "lms9535formhandler",
		type : "POST",
		data: $.extend($("#formBisDetail").serializeData(), {
            formAction: "calcResult"
        }),
		success : function(obj) {
			$("#bisRiskAdjReturn").val(DOMPurify.sanitize(obj.bisRiskAdjReturn));
			$("#bisRiskAdjReturn_1").val(DOMPurify.sanitize(obj.bisRiskAdjReturn_1));
			$("#bisRItemD_standard").val(DOMPurify.sanitize(obj.bisRItemD_standard));
			$("#bisRItemD_IRB").val(DOMPurify.sanitize(obj.bisRItemD_IRB));
			$("#bisRorwa_standard").val(DOMPurify.sanitize(obj.bisRorwa_standard));
			$("#bisRorwa_1_standard").val(DOMPurify.sanitize(obj.bisRorwa_1_standard));
			$("#bisRorwa_IRB").val(DOMPurify.sanitize(obj.bisRorwa_IRB));
			$("#bisRorwa_1_IRB").val(DOMPurify.sanitize(obj.bisRorwa_1_IRB));
			$("#bisBankWorkCost").val(DOMPurify.sanitize(obj.bisBankWorkCost));
			$("#bisRiskCost").val(DOMPurify.sanitize(obj.bisRiskCost));
		}
	});
}
//J-113-0183 列印RORWA計算
function printRORWA(){
	var count = 0;
	var $tLMS1205S07Form03b = $("#tLMS1205S07Form03b");
	var urlStr = "../simple/FileProcessingService"; // 預設
	if($("#source").val() == "fms"){
	    urlStr = "../../simple/FileProcessingService";
	}
	$.form.submit({
	    url: urlStr,
	    target: "_blank",
	    data: $.extend($("#formBisDetail").serializeData(),{
			custId : DOMPurify.sanitize($("#custId").val()),
			custName : DOMPurify.sanitize($("#custName").val()),
	    	rptOid : "RORWA" + "^" + "",
			fileDownloadName : "RORWA.pdf",
			serviceName : "lms9535r01rptservice"
	    })
	});	
}
//J-113-0183 清空RORWA計算
function resetRORWAContent(){
	$("#formBisDetail").find("input[type='text']").val("");
	$("#formBisDetail").find("input[type='checkbox'], input[type='radio']").attr("checked", false);
	$("#bisApplyCurr").val("");
	$("#bisEstimatedType").val("");
	$(".curr").html("");
}

