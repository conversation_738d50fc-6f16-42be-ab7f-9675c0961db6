var gridDfd = $.Deferred();
var Action = {
    fhandle: "cls1141m01formhandler",
    ghandle: "cls1141gridhandler",
    xlsFrm : 'xlsFrm',
    /**
     * 新增案件
     */
    addCase: function(){
        var $LMS1205V01Form = $("#LMS1205V01Form");
        $LMS1205V01Form.reset();
        var openThickbox = $("#LMS1205V01Thickbox").thickbox({
            title: i18n.cls1141v01["l120v01.thickbox3"],
            width: 700,
            height: 340,
            align: 'center',
            valign: 'bottom',
            modal: true,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var docKind = $LMS1205V01Form.find("[name='docKind']:checked").val();
                    var docCode = $LMS1205V01Form.find("[name='docCode']:checked").val();
                    var ngFlag = $LMS1205V01Form.find("[name='ngFlag']:checked").val();
                    var authLvl = $LMS1205V01Form.find("[name='authLvl']:checked").val();
                    
                    //檢核畫面欄位有class="required"者且不隱藏者為必要輸入欄位。
                    if (!$LMS1205V01Form.valid()) {
                        return;
                    }
                    
                    if (docKind && docCode && ngFlag) {
                        if (docCode == "4" && docKind != "2") {
                            //l120v01.error12=異常通報不可選擇授權內 ！
                            CommonAPI.showMessage(i18n.cls1141v01["l120v01.error12"]);
                            return false;
                        }
                        
                        if (userInfo.unitType != "1") {
                            if (docCode == "5" && docKind != "2") {
                                //l120v01.error13=團貸母案不可選擇分行授權內 ！
                                CommonAPI.showMessage(i18n.cls1141v01["l120v01.error13"]);
                                return false;
                            }
                        }
                        else {							
                            if (docCode == "5") {
								if (authLvl == "1") {
									// 1分行授權內  2總行授權內  3營運中心授權內  4母行授權內  5分行授權外  6營運中心授權外
									//l120v01.error13=團貸母案不可選擇分行授權內 ！
									CommonAPI.showMessage(i18n.cls1141v01["l120v01.error13"]);
									return false;
								}
                            }
                        }
                        
                        // 開始新增前的異常通報明細停權檢查
                        $.ajax({
                            type: "POST",
                            handler: Action.fhandle,
                            data: {
                                formAction: "checkIsStop",
                                docKind: docKind,
                                docCode: docCode,
                                isNew: true
                            },
                            success: function(responseData){
                                if (responseData.addMsg) {
                                    CommonAPI.confirmMessage(i18n.lmscommom["other.msg26a"], function(b){
                                        if (b) {
                                            $.ajax({
                                                handler: Action.fhandle,
                                                action: "addL120m01a",
                                                formId: "LMS1205V01Form",
                                                data: {
                                                    isStop: true
                                                },
                                                success: function(responseData){
                                                    var url = '..' + responseData.docURL + '/02';
                                                    $.form.submit({
                                                        url: url,
                                                        data: {
                                                            mainDocStatus: viewstatus,
                                                            mainId: responseData.mainId,
                                                            mainOid: responseData.oid,
                                                            docType: responseData.docType,
                                                            areaChk: "",
                                                            hqMeetFlag: "",
                                                            areaDocstatus: "",
                                                            ownBrId: responseData.ownBrId,
                                                            caseBrId: responseData.caseBrId,
                                                            authLvl: responseData.authLvl,
                                                            docStatus: responseData.docStatus,
                                                            oid: responseData.oid
                                                        },
                                                        //target: "_blank"
                                                        target: responseData.oid
                                                    });
                                                    $.thickbox.close();
                                                }
                                            });
                                        }
                                    });
                                }
                                else {
                                    $.ajax({
                                        handler: Action.fhandle,
                                        action: "addL120m01a",
                                        formId: "LMS1205V01Form",
                                        data: {},
                                        success: function(responseData){
                                            var url = '..' + responseData.docURL + '/02';
                                            $.form.submit({
                                                url: url,
                                                data: {
                                                    mainDocStatus: viewstatus,
                                                    mainId: responseData.mainId,
                                                    mainOid: responseData.oid,
                                                    docType: responseData.docType,
                                                    areaChk: "",
                                                    hqMeetFlag: "",
                                                    areaDocstatus: "",
                                                    ownBrId: responseData.ownBrId,
                                                    caseBrId: responseData.caseBrId,
                                                    authLvl: responseData.authLvl,
                                                    docStatus: responseData.docStatus,
                                                    oid: responseData.oid
                                                },
                                                //target: "_blank"
                                                target: responseData.oid
                                            });
                                            $.thickbox.close();
                                        }
                                    });
                                }
                            }
                        });
                        
                    }
                    else {
                        CommonAPI.showMessage(i18n.cls1141v01["l120v01.alert2"]);
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    /**
     *  修改審核層級
     */
    btnCaseLvl: function(){
        var rows = $("#gridview").getGridParam('selarrrow');
        var list = "";
        var sign = ",";
        for (var i = 0; i < rows.length; i++) { //將所有已選擇的資料存進變數list裡面
            if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0) {
                var data = $("#gridview").getRowData(rows[i]);
                list += ((list == "") ? "" : sign) + data.oid;
            }
        }
        if (list == "") {
            return CommonAPI.showMessage(i18n.lms9131v00['LMS913V00.error1']);
        }
        $.ajax({
            type: "POST",
            handler: "lms9131m01formhandler",
            data: {
                formAction: "Clear",
                listOid: list,
                sign: sign,
                flag: 'A'
            },
            success: function(json){
                CommonAPI.triggerOpener("gridview", "reloadGrid");
                $("#gridfile").trigger("reloadGrid");//更新Grid內容						
            }
        });
    },
    /**
     *  額度明細表傳送聯行
     */
    btnTableSend: function(){
        var id = $("#gridview").getGridParam('selrow');
        if (!id) {
            // action_005=請先選取一筆以上之資料列
            return API.showMessage(i18n.def["action_005"]);
        }

		var form = $("#tableSendBoxForm");
        $.ajax({
            handler: "cls1171m01formhandler",
            action: "queryBrankList",
            data: {},
            success: function(obj){
                form.find("#brankSelect").setItems({
                    item: obj,
                    format: "{value} {key}",
                    space: false
                });
            }
        });
        
        var data = $("#gridview").getRowData(id);
        $("#tableSendBox").thickbox({
            // sure1=額度明細表傳送聯行
            title: i18n.cls1141v01["sure1"],
            width: 460,
            height: 150,
            modal: true,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    $.ajax({
                        handler: "cls1171m01formhandler",
                        action: "copyCase",
                        data: {
                        
                            brank: $("#brankSelect").val(),
                            oid: data.oid
                        },
                        success: function(obj){
                            $("#gridview").trigger("reloadGrid");// 更新Grid內容
                        }
                    });
                    $.thickbox.close();
                },
                "cancel": function(){
                    API.confirmMessage(i18n.def['flow.exit'], function(res){
                        if (res) {
                            $.thickbox.close();
                        }
                    });
                }
            }
        });
    },
    /**
     * 案件格式變更
     */
    changeCase: function(){
        //當選取的為團貸案不可更改
        var $LMS1205V01Form = $("#LMS1205V01Form");
        $LMS1205V01Form.find("#cUnNormal").hide();
        var row = $("#gridview").getGridParam('selrow');
        var list = "";
        var data = $("#gridview").getRowData(row);
        list = data.oid;
        list = (list == undefined ? "" : list);
        if (list != "") {
            $.ajax({
                handler: Action.fhandle,
                action: "getCaseFormate",
                data: {
                    LMS1205V01Form: JSON.stringify($LMS1205V01Form.serializeData()),
                    oid: list
                },
                success: function(responseData){
                    $LMS1205V01Form.reset();
                    var odocKind = responseData.LMS1205V01Form.docKind;
                    var odocCode = responseData.LMS1205V01Form.docCode;
                    var ongFlag = responseData.LMS1205V01Form.ngFlag;
                    
                    $LMS1205V01Form.find("[name='docKind']").each(function(j){
                        if ($(this).val() == odocKind) {
                            $(this).prop("checked", true);
                        }
                    });
                    $("#LMS1205V01Form").find("[name='docCode']").each(function(k){
                        if ($(this).val() == responseData.LMS1205V01Form.docCode) {
                            $(this).prop("checked", true);
                            
                        }
                        //當如果是團貸不可變成一般案件
                        if (odocCode == "5") {
                            $(this).prop("disabled", "disabled");
                        }
                        else {
                            //鎖定團貸按鈕不可選擇
                            if ($(this).val() == "5") {
                                $(this).prop("disabled", "disabled");
                            }
                            else {
                                $(this).removeAttr("disabled", "disabled");
                            }
                        }
                    });
                    $LMS1205V01Form.find("[name='ngFlag']").each(function(k){
                        if ($(this).val() == ongFlag) {
                            $(this).prop("checked", true);
                        }
                    });
                    
                    
                    
                    
                    
                    var openThickbox = $("#LMS1205V01Thickbox").thickbox({
                        title: i18n.cls1141v01["sure2"],
                        width: 700,
                        height: 340,
                        align: 'center',
                        valign: 'bottom',
                        modal: true,
                        i18n: i18n.def,
                        buttons: {
                            "sure": function(){
                                var docKind = $LMS1205V01Form.find("[name='docKind']:checked").val();
                                var docCode = $LMS1205V01Form.find("[name='docCode']:checked").val();
                                var ngFlag = $LMS1205V01Form.find("[name='ngFlag']:checked").val();
                                if (docCode == "4" && docKind != "2") {
                                    //l120v01.error12=異常通報不可選擇授權內 ！
                                    CommonAPI.showMessage(i18n.cls1141v01["l120v01.error12"]);
                                    return false;
                                }
                                if ($LMS1205V01Form.find("[name='docKind']:checked").val() != undefined &&
                                $LMS1205V01Form.find("[name='docCode']:checked").val() != undefined &&
                                $LMS1205V01Form.find("[name='ngFlag']:checked").val() != undefined) {
                                    if ($LMS1205V01Form.find("[name='docKind']:checked").val() == odocKind &&
                                    $LMS1205V01Form.find("[name='docCode']:checked").val() == odocCode &&
                                    $LMS1205V01Form.find("[name='ngFlag']:checked").val() == ongFlag) {
                                        //l120v01.error10=變更無效！
                                        API.showMessage(i18n.cls1141v01["l120v01.error10"]);
                                        return;
                                    }
                                    $.ajax({
                                        handler: Action.fhandle,
                                        action: "changeCase",
                                        data: {
                                            oid: list
                                        },
                                        success: function(responseData){
                                            $("#gridview").trigger("reloadGrid"); // 更新Grid內容
                                            $.thickbox.close();
                                            $.thickbox.close();
                                        }
                                    });
                                }
                                else {
                                    API.showMessage(i18n.cls1141v01["l120v01.alert2"]);
                                    return;
                                }
                            },
                            "cancel": function(){
                                API.confirmMessage(i18n.def['flow.exit'], function(res){
                                    if (res) {
                                        var $LMS1205V01Form = $("#LMS1205V01Form");
                                        $LMS1205V01Form.find("#cUnNormal").show();
                                        $.thickbox.close();
                                    }
                                });
                            }
                        }
                    });
                }
            });
        }
        else {
            API.showMessage(i18n.cls1141v01["l120v01.alert1"]);
        }
    },
    
    /**
     * 條件變更/續約
     */
    changeCase2: function(){
        var $LMS1205V01Form = $("#LMS1205V01Form");
        var row = $("#gridview").getGridParam('selrow');
        var list = "";
        var data = $("#gridview").getRowData(row);
        list = data.oid;
        list = (list == undefined ? "" : list);
        if (list != "") {
            $.ajax({
                action: "getCaseFormate",
                handler: Action.fhandle,
                data: {
                    oid: list,
                    changeIf: true
                },
                success: function(responseData){
                    $("#LMS1205V01Form").reset();
                    
                    $("#LMS1205V01Form").find("[name='docKind']").each(function(j){
                        if ($(this).val() == responseData.LMS1205V01Form.docKind) {
                            $(this).prop("checked", true);
                        }
                    });
                    $("#LMS1205V01Form").find("[name='docCode']").each(function(k){
                        if ($(this).val() == responseData.LMS1205V01Form.docCode) {
                            $(this).prop("checked", true);
                            
                        }
                        //當如果是團貸不可變成一般案件
                        if (responseData.LMS1205V01Form.docCode == "5") {
                            $(this).prop("disabled", "disabled");
                        }
                        else {
                            //鎖定團貸按鈕不可選擇
                            if ($(this).val() == "5") {
                                $(this).prop("disabled", "disabled");
                            }
                            else {
                                $(this).removeAttr("disabled", "disabled");
                            }
                        }
                    });
                    $LMS1205V01Form.find("[name='ngFlag']").each(function(k){
                        if ($(this).val() == responseData.LMS1205V01Form.ngFlag) {
                            $(this).prop("checked", true);
                        }
                    });
                    
                    var openThickbox = $("#LMS1205V01Thickbox").thickbox({
                        title: i18n.cls1141v01["sure3"],
                        width: 700,
                        height: 340,
                        align: 'center',
                        valign: 'bottom',
                        modal: true,
                        i18n: i18n.def,
                        buttons: {
                            "sure": function(){
                                if ($("#LMS1205V01Form").find("[name='docKind']:checked").val() != undefined &&
                                $("#LMS1205V01Form").find("[name='docCode']:checked").val() != undefined &&
                                $LMS1205V01Form.find("[name='ngFlag']:checked").val() != undefined) {
                                    $.ajax({
                                        handler: Action.fhandle,
                                        action: "changeIf",
                                        data: {
                                            oid: list
                                        },
                                        success: function(responseData){
                                            // alert(JSON.stringify(responseData));
                                            $("#gridview").trigger("reloadGrid"); // 更新Grid內容
                                            $.thickbox.close();
                                            API.showMessage(responseData.runSuccess);
                                        }
                                    });
                                }
                                else {
                                    API.showMessage(i18n.cls1141v01["l120v01.alert2"]);
                                }
                            },
                            "cancel": function(){
                                API.confirmMessage(i18n.def['flow.exit'], function(res){
                                    if (res) {
                                        $.thickbox.close();
                                    }
                                });
                            }
                        }
                    });
                }
            });
        }
        else {
            API.showMessage(i18n.cls1141v01["l120v01.alert1"]);
        }
    },
    
    /**
     * 收件按鈕
     */
    getCase: function(){
    
        var ids = $("#gridview").getGridParam('selarrrow');
        if (ids == "") {
            // action_005=請先選取一筆以上之資料列
            return API.showMessage(i18n.def["action_005"]);
        }
        
        var oids = [];
        for (var i in ids) {
            oids.push($("#gridview").getRowData(ids[i]).oid);
        }
        
        $.ajax({
            handler: "cls1171m01formhandler",
            action: "getCase",
            data: {
            
                oids: oids
            },
            success: function(obj){
                $("#gridview").trigger("reloadGrid");
            }
        });
        
    },
    /**
     * 查詢使用者點擊的資料
     *
     * @param cellvalue
     * @param options
     * @param rowObject
     */
    openDoc: function(cellvalue, options, rowObject){
        var noOpenDoc = false;
        if (rowObject.areaDocstatus != null &&
        rowObject.areaDocstatus != undefined &&
        rowObject.areaDocstatus != "") {
            if (rowObject.areaBrId == userInfo.unitNo) {
                noOpenDoc = true;
            }
            else {
                noOpenDoc = false;
            }
        }
        if (rowObject.ownBrId != userInfo.unitNo) {
            noOpenDoc = true;
        }
        else {
            if (viewstatus == '05O') {
                // 已核准案件不鎖
                noOpenDoc = true;
            }
            else {
                noOpenDoc = false;
            }
        }
        
        var mainId = rowObject.mainId;
        var url = '..' + rowObject.docURL + '/02';
        var lockOption = {};
        if (noOpenDoc) {
            lockOption = {
                noOpenDoc: noOpenDoc
            };
        }
        $.form.submit({
            url: url,
            data: $.extend({
                mainDocStatus: viewstatus,
                mainId: mainId,
                mainOid: rowObject.oid,
                docType: rowObject.docType,
                docCode: rowObject.docCode,
                docKind: rowObject.docRslt,
                docURL: rowObject.docURL,
                ownBrId: rowObject.ownBrId,
                caseBrId: rowObject.caseBrId,
                authLvl: rowObject.authLvl,
                areaDocstatus: rowObject.areaDocstatus,
                areaBrId: rowObject.areaBrId,
                areaChk: rowObject.areaChk,
                docStatus: rowObject.uid,
                hqMeetFlag: rowObject.hqMeetFlag,
                oid: rowObject.oid
            }, lockOption),
            target: rowObject.oid
        });
    },
    login1: function(){
        $("#LMS1200V62Form1").reset();
        var ids = new Array();
        ids = $("#gridview").getGridParam('selarrrow');
        var list1 = "";
        var sign = ",";
        var count = 0;
        for (var id in ids) {
            var rows = $("#gridview").jqGrid('getRowData', ids[id]);
            if (rows.oid != 'undefined' && rows.oid != null && rows.oid != 0) {
                list1 += ((list1 == "") ? "" : sign) + rows.oid;
            }
            count++;
        }
        
        if (list1 == "") {
            API.showMessage(i18n.cls1141v01["l120v01.alert1"]);
            return;
        }
        else {
            $("#LMS1200V62Thickbox1").thickbox({
                title: i18n.cls1141v01["l120v01.thickbox7"],
                width: 500,
                height: 200,
                modal: true,
                valign: "bottom",
                align: "center",
                i18n: i18n.def,
                buttons: {
                    "sure": function(){
                        var $LMS1200V62Form1 = $("#LMS1200V62Form1");
                        if ($LMS1200V62Form1.valid()) {
                            if ($LMS1200V62Form1.find("#rptTitle1b").val() < 1 ||
                            $LMS1200V62Form1.find("#rptTitle1b").val() > 12) {
                                API.showMessage(i18n.cls1141v01["l120v01.error3"]);
                                return;
                            }
                            else 
                                if ($LMS1200V62Form1.find("#rptTitle1c").val() < 1 ||
                                $LMS1200V62Form1.find("#rptTitle1c").val() > 31) {
                                    API.showMessage(i18n.cls1141v01["l120v01.error4"]);
                                    return;
                                }
                                else 
                                    if ($LMS1200V62Form1.find("#rptTitle1d").val() <=
                                    0) {
                                        API.showMessage(i18n.cls1141v01["l120v01.error6"]);
                                        return;
                                    }
                                    else 
                                        if ($LMS1200V62Form1.find("#rptTitle1a").val() <= 0) {
                                            API.showMessage(i18n.cls1141v01["l120v01.error8"]);
                                            return;
                                        }
                                        else {
                                            $.ajax({
                                            
                                                handler: Action.fhandle,
                                                action: "login1",
                                                data: {
                                                
                                                    LMS1200V62Form1: JSON.stringify($LMS1200V62Form1.serializeData()),
                                                    oids: list1,
													isDelete : false,
                                                    caseName: $LMS1200V62Form1.find("#rptTitle1e option:selected").html()
                                                },
                                                success: function(responseData){
                                                    //更新授信簽報書Grid內容
                                                    $("#gridview").trigger("reloadGrid");
                                                }
                                            });
                                        }
                            $.thickbox.close();
                        }
                    },
                    "cancel": function(){
                        API.confirmMessage(i18n.def['flow.exit'], function(res){
                            if (res) {
                                $.thickbox.close();
                            }
                        });
                    },
                    "del": function(){
                        var $LMS1200V62Form1 = $("#LMS1200V62Form1");
	                    API.confirmMessage(i18n.def['confirmDelete'], function(res){
							if(res){
								$.ajax({
                                            
	                                handler: Action.fhandle,
	                                action: "login1",
	                                data: {
	                                
	                                    LMS1200V62Form1: JSON.stringify($LMS1200V62Form1.serializeData()),
	                                    oids: list1,
										isDelete : true,
	                                    caseName: $LMS1200V62Form1.find("#rptTitle1e option:selected").html()
	                                },
	                                success: function(responseData){
	                                    //更新授信簽報書Grid內容
	                                    $("#gridview").trigger("reloadGrid");
	                                }
	                            });
								
							}
							
							$.thickbox.close();
				        });
                    }
                }
            });
        }
    },
    
    login2: function(){
        $("#LMS1200V63Form1").reset();
        var ids = new Array();
        ids = $("#gridview").getGridParam('selarrrow');
        var list1 = "";
        var sign = ",";
        var count = 0;
        for (var id in ids) {
            var rows = $("#gridview").jqGrid('getRowData', ids[id]);
            if (rows.oid != 'undefined' && rows.oid != null && rows.oid != 0) {
                list1 += ((list1 == "") ? "" : sign) + rows.oid;
            }
            count++;
        }
        
        if (list1 == "") {
            API.showMessage(i18n.cls1141v01["l120v01.alert1"]);
            return;
        }
        else {
            $("#LMS1200V63Thickbox1").thickbox({
                title: i18n.cls1141v01["l120v01.thickbox8"],
                width: 500,
                height: 200,
                modal: true,
                valign: "bottom",
                align: "center",
                i18n: i18n.def,
                buttons: {
                    "sure": function(){
                        var $LMS1200V63Form1 = $("#LMS1200V63Form1");
                        if ($LMS1200V63Form1.valid()) {
                            if ($LMS1200V63Form1.find("#rptTitle1b").val() < 1 ||
                            $LMS1200V63Form1.find("#rptTitle1b").val() > 12) {
                                API.showMessage(i18n.cls1141v01["l120v01.error3"]);
                                return;
                            }
                            else 
                                if ($LMS1200V63Form1.find("#rptTitle1c").val() < 1 ||
                                $LMS1200V63Form1.find("#rptTitle1c").val() > 31) {
                                    API.showMessage(i18n.cls1141v01["l120v01.error4"]);
                                    return;
                                }
                                else 
                                    if ($LMS1200V63Form1.find("#rptTitle1d").val() <= 0) {
                                        API.showMessage(i18n.cls1141v01["l120v01.error6"]);
                                        return;
                                    }
                                    else 
                                        if ($LMS1200V63Form1.find("#rptTitle1a").val() <= 0) {
                                            API.showMessage(i18n.cls1141v01["l120v01.error8"]);
                                            return;
                                        }
                                        else {
                                            $.ajax({
                                            
                                                handler: Action.fhandle,
                                                action: "login2",
                                                data: {
                                                
                                                    LMS1200V63Form1: JSON.stringify($LMS1200V63Form1.serializeData()),
                                                    oids: list1,
													isDelete : false
                                                },
                                                success: function(responseData){
                                                    //更新授信簽報書Grid內容
                                                    $("#gridview").trigger("reloadGrid");
                                                }
                                            });
                                        }
                            $.thickbox.close();
                        }
                    },
                    "cancel": function(){
                        API.confirmMessage(i18n.def['flow.exit'], function(res){
                            if (res) {
                                $.thickbox.close();
                            }
                        });
                    },
	                "del": function(){
	                    var $LMS1200V63Form1 = $("#LMS1200V63Form1");
	                    API.confirmMessage(i18n.def['confirmDelete'], function(res){
							if(res){
								$.ajax({
                                            
                                    handler: Action.fhandle,
                                    action: "login2",
                                    data: {
                                    
                                        LMS1200V63Form1: JSON.stringify($LMS1200V63Form1.serializeData()),
                                        oids: list1,
										isDelete : true
                                    },
                                    success: function(responseData){
                                        //更新授信簽報書Grid內容
                                        $("#gridview").trigger("reloadGrid");
                                    }
                                });
							}
							
							$.thickbox.close();
				        });
	                }
                }
            });
        }
    },
    
    login3: function(){
        $("#LMS1200V64Form1").reset();
        var ids1 = new Array();
        ids1 = $("#gridview").getGridParam('selarrrow');
        var list1 = "";
        var sign1 = ",";
        var count1 = 0;
        for (var id1 in ids1) {
            var rows1 = $("#gridview").jqGrid('getRowData', ids1[id1]);
            if (rows1.oid != 'undefined' &&
            rows1.oid != null &&
            rows1.oid != 0) {
                list1 += ((list1 == "") ? "" : sign1) +
                rows1.oid;
            }
            count1++;
        }
        if (list1 == "") {
            API.showMessage(i18n.cls1141v01["l120v01.alert1"]);
            return;
        }
        else {
            $("#LMS1200V64Thickbox1").thickbox({
                title: i18n.cls1141v01["l120v01.thickbox9"],
                width: 500,
                height: 200,
                modal: true,
                valign: "bottom",
                align: "center",
                i18n: i18n.def,
                buttons: {
                    "sure": function(){
                        var $LMS1200V64Form1 = $("#LMS1200V64Form1");
                        if ($LMS1200V64Form1.valid()) {
                            if ($LMS1200V64Form1.find("#rptTitle1b").val() <
                            1 ||
                            $LMS1200V64Form1.find("#rptTitle1b").val() >
                            12) {
                                API.showMessage(i18n.cls1141v01["l120v01.error3"]);
                                return;
                            }
                            else 
                                if ($LMS1200V64Form1.find("#rptTitle1c").val() < 1 ||
                                $LMS1200V64Form1.find("#rptTitle1c").val() > 31) {
                                    API.showMessage(i18n.cls1141v01["l120v01.error4"]);
                                    return;
                                }
                                else 
                                    if ($LMS1200V64Form1.find("#rptTitle1d").val() <= 0) {
                                        API.showMessage(i18n.cls1141v01["l120v01.error7"]);
                                        return;
                                    }
                                    else 
                                        if ($LMS1200V64Form1.find("#rptTitle1e").val() <= 0) {
                                            API.showMessage(i18n.cls1141v01["l120v01.error6"]);
                                            return;
                                        }
                                        else 
                                            if ($LMS1200V64Form1.find("#rptTitle1a").val() <= 0) {
                                                API.showMessage(i18n.cls1141v01["l120v01.error8"]);
                                                return;
                                            }
                                            else {
                                                $.ajax({
                                                
                                                    handler: Action.fhandle,
                                                    action: "login3",
                                                    data: {
                                                    
                                                        LMS1200V64Form1: JSON.stringify($LMS1200V64Form1.serializeData()),
                                                        oids: list1,
														isDelete : false,
                                                        caseName: $LMS1200V64Form1.find("#rptTitle1f option:selected").html()
                                                    },
                                                    success: function(responseData){
                                                        //更新授信簽報書Grid內容
                                                        $("#gridview").trigger("reloadGrid");
                                                    }
                                                });
                                            }
                            $.thickbox.close();
                        }
                    },
                    "cancel": function(){
                        API.confirmMessage(i18n.def['flow.exit'], function(res){
                            if (res) {
                                $.thickbox.close();
                            }
                        });
                    },
	                "del": function(){
	                    var $LMS1200V64Form1 = $("#LMS1200V64Form1");
	                    API.confirmMessage(i18n.def['confirmDelete'], function(res){
							if(res){
								$.ajax({           
                                    handler: Action.fhandle,
                                    action: "login3",
                                    data: {
                                    
                                        LMS1200V64Form1: JSON.stringify($LMS1200V64Form1.serializeData()),
                                        oids: list1,
										isDelete : true,
                                        caseName: $LMS1200V64Form1.find("#rptTitle1f option:selected").html()
                                    },
                                    success: function(responseData){
                                        //更新授信簽報書Grid內容
                                        $("#gridview").trigger("reloadGrid");
                                    }
                                });
							}
							
							$.thickbox.close();
				        });
	                }
                }
            });
        }
    }    /**
     * 列印營運中心意見
     */
    ,
    printArea: function(){
        $("#printAreaGrid").resetSelection();
        $("#PrintAreaBox").thickbox({
            title: "",
            width: 770,
            height: 500,
            modal: true,
            i18n: i18n.def,
            buttons: {
                "print": function(){
                    var $grid = $("#printAreaGrid");
                    //var ids = $grid.getGridParam('selarrrow');
                    
                    var ids = $grid.getGridParam('selrow');
                    if (!ids || ids == "") {
                        // action_005=請先選取一筆以上之資料列
                        //grid.selrow=請先選擇一筆資料。
                        return API.showMessage(i18n.def["grid.selrow"]);
                    }
                    
                    var mainId = $grid.getRowData(ids).mainId;
                    
                    $.form.submit({
                        url: "../simple/FileProcessingService",
                        target: "_blank",
                        data: {
                            mainId: mainId,
                            rptOid: "R19" + "^^^^^",
                            otherData: "",
                            fileDownloadName: "LMS1205R109.pdf",
                            serviceName: "lms1201r01rptservice"
                        }
                    });
                    
                },
                "close": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    // 篩選
    openFilterBox: function(){
        var $filterForm = $("#filterForm");
        // 初始化
        $filterForm.reset();
        var thickTitle = "";
        if (viewstatus == "05O") {
            thickTitle = i18n.cls1141v01['l120v05.title01'];
        }
        else 
            if (viewstatus == "06O") {
                thickTitle = i18n.cls1141v01['l120v06.title01'];
            }
        
        if (userInfo.unitType == "2" || userInfo.unitType == "4") {
            //授管處、營運中心
            $filterForm.find("#a91t2").show();
            $filterForm.find("#s91t1f2").hide();
        }
        else {
            $filterForm.find("#a91t2").hide();
            $filterForm.find("#s91t2").hide();
        }
        
        $("#filterBox").thickbox({
            // l120v05.title01=已核准受理查詢
            title: thickTitle,
            width: 500,
            height: 410,
            modal: true,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if (!$("#filterForm").valid()) {
                        return;
                    }
                    //2012-09-06 黃建霖 begin
                    //只有在有輸入日期欄位時才要檢查
                    if ($.trim($("#fromDate").val()) != "" || $.trim($("#endDate").val()) != "") {
                        if ($.trim($("#endDate").val()) == "" ||
                        $.trim($("#fromDate").val()) == "") {
                            // l120v05.message03=請輸入日期
                            return API.showErrorMessage(i18n.cls1141v01["l120v05.message03"]);
                        }
                        
                        if ($("#fromDate").val() > $("#endDate").val()) {
                            // l120v05.message02=起始日期不能大於結束日期
                            return API.showErrorMessage(i18n.cls1141v01["l120v05.message02"]);
                        }
                    }
                    //2012-09-06 黃建霖 end
                    
                    grid();
                    $.thickbox.close();
                },
                "cancel": function(){
                    API.confirmMessage(i18n.def['flow.exit'], function(res){
                        if (res) {
                        
                        
                            //2012-09-06 黃建霖 begin
                            $filterForm.reset();
                            var end = CommonAPI.getToday().split("-");
                            
                            //var end = "2012-7-10".split("-");
                            var endDate = new Date(end[0], end[1], end[2]);
                            var fromDate = new Date(end[0], end[1]-1, end[2]);
                            fromDate.setMonth(fromDate.getMonth() - 12);
                            $("#fromDate").val(dateObjtoStr(fromDate));
                            $("#endDate").val(dateObjtoStr(endDate));
                            grid();
                            //2012-09-06 黃建霖 end	
                            $.thickbox.close();
                        }
                    });
                }
            }
        });
    },
    deleteCase: function(oid, reason, reasonOth){
        $.ajax({
            handler: Action.fhandle,
            action: "deleteL120m01a",
            data: {
                mainOid: $("#oid").val(),
                list: oid,
                reason: reason || "",
                reasonOth: reasonOth || ""
            },
            success: function(responseData){
                API.triggerOpener("gridview", "reloadGrid");
                $("#gridview").trigger("reloadGrid");// 更新Grid內容
            }
        });
    },
    /**
     * 查詢是否評等已被引用
     */
    queryDelectCase: function(mainId){
        var reslut = false;
        $.ajax({
            handler: "cls1141formhandler",
            async: false,
            action: 'queryDelectCase',
            formId: 'empty',
            data: {
                mainId: mainId
            },
            success: function(response){
                reslut = response.havaGrade;
            }
        });
        return reslut;
    },
    /**
     * 查詢是否有被進件管理綁定
     */
    queryDeleteC122M01ACase: function(mainId){
        var reslut = false;
        $.ajax({
            handler: "cls1141m01formhandler",
            async: false,
            action: 'queryDeleteC122M01ACase',
            formId: 'empty',
            data: {
                mainId: mainId
            },
            success: function(response){
                reslut = response.havaCase;
            }
        });
        return reslut;
    },
    callSrvImpXls: function(passdata){
        	Action.callSrvImpXls_batch(passdata);
    },
    callSrvImpXls_batch: function(passdata){
        var $form = $("#"+Action.xlsFrm);

        var my_timeout = 7200000;//ms
        if(true){//先延長 timer，不然在處理過程中，會 timeout
            timer_long_ajax_beg(my_timeout);
        }
        $.ajax({
            handler: Action.fhandle,
            action: "importClsAreaPriceExcel",
            timeout: my_timeout,
            data: $.extend({'act':'procC127M01A_xls'
                , 'jq_timeout': (my_timeout/1000)}, passdata ),
            success: function(json_callBatch){
                if(true){//恢復 timer
                    timer_long_ajax_end();
                }

                $form.find('#progress').html(100);

                if(json_callBatch.importReslut==="SUCCESS"){
                    if(true){
                        $form.setValue( {'excelId':passdata.excelId} );

                        if($form.find('#excelId').val().length>0){
                            $form.find('#downloadExcel').show();
                        }
                    }

                    MegaApi.showPopMessage(i18n.def["confirmTitle"], i18n.def['runSuccess']);
                    $form.find('#progressTr').hide().end().find("#btUploadExcel").hide();
                    //更新opener
                    CommonAPI.triggerOpener("gridview", "reloadGrid");
                }else{
                    API.showErrorMessage(json_callBatch.r.response);
                }
            }
        });
    },
	    /**
     * 初始化下拉選單
     *
     */
    initItem: function(){
    	ilog.debug("@_M > initItem");
        //產生下拉選單
        var $div = $("#createCntrNo_brmp_creditCheckForm").find("[itemType]");
        var allKey = [];
        $div.each(function(){
            allKey.push($(this).attr("itemType"));
        });
        var item = API.loadCombos(allKey);
        $div.each(function(){
            var $obj = $(this);
            var itemType = $obj.attr("itemType");
            if (itemType) {
                $obj.setItems({
                    space: $obj.attr("space") || true,
                    item: item[itemType],
                    format: "{value} - {key}",
                    size: $obj.attr("itemSize")
                });
            }
        });
    }
};

pageJsInit(function(){
$(function(){
	Action.initItem();
	
    var nowBranch = userInfo.unitNo;
    var unitNo = userInfo.unitNo;
    if ((unitNo == "007" || unitNo == "009" || unitNo == "025" ||
	unitNo == "011" ||
	unitNo == "201"|| unitNo == "940" || unitNo == "943"  || unitNo == "149") &&
	txCode == "338021") {
		// 特殊分行(國外部、國金部、金控總部分行、財務部、財富管理處)會簽中Grid
		spectialGrid();
	}
	else 
		if ((unitNo == "007" || unitNo == "009" || unitNo == "025" ||
		unitNo == "011" ||
		unitNo == "201"|| unitNo == "940" || unitNo == "943" || unitNo == "149") &&
		txCode == "338023") {
			// 特殊分行(國外部、國金部、金控總部分行、財務部、財富管理處)提授審會Grid
			mainGrid3("A");
		}
		else 
			if ((unitNo == "007" || unitNo == "009" || unitNo == "025" ||
			unitNo == "011" ||
			unitNo == "201"|| unitNo == "940" || unitNo == "943" || unitNo == "149") &&
			txCode == "338024") {
				// 特殊分行(國外部、國金部、金控總部分行、財務部、財富管理處)提催收會Grid
				mainGrid3("B");
			}
			else 
				if ((unitNo == "007" || unitNo == "009" || unitNo == "025" ||
				unitNo == "011" ||
				unitNo == "201" || unitNo == "940" || unitNo == "943" || unitNo == "149") &&
				txCode == "338025") {
					// 特殊分行(國外部、國金部、金控總部分行、財務部、財富管理處)提常董會Grid
					mainGrid2();
				}
				else 
					if (txCode == "338008") {
						// 異常通報案件Grid
						mainGrid0();
					}
					else 
						if (viewstatus == "05O") {
							switch (nowBranch) {
								case "920":
								case "922":
								case "931":
								case "932":
								case "933":
								case "934":
								case "935":
									// 營運中心已核准
									mainGrid1();
									break
								case "918":
									// 授管處已核准
									Action.openFilterBox();
									mainGrid1();
									break;
								default:
									// 海外分行已核准
									Action.openFilterBox();
									mainGridInit();
									break;
							}
						}
						else 
							if (viewstatus == "06O") {
								Action.openFilterBox();
								mainGridInit();
							}
							else 
								if (viewstatus == "0BO") {
									mainGrid();
								}
								else 
									if (viewstatus == "L0H") {
										mainGrid1();
									}
									else 
										if (txCode == "339062" || txCode == "339063" || txCode == "339064") {
											if (txCode == "339062") {
												mainGrid3(1);
											}
											else 
												if (txCode == "339063") {
													mainGrid3(2);
												}
												else {
													mainGrid2();
												}
										}
										else 
											if (viewstatus == "L1H" && userInfo.unitNo == "918") {
												// 授管處
												mainGrid4();
											}
											else 
												if (viewstatus == "L1C" &&
												(userInfo.unitNo == "920" ||
												userInfo.unitNo == "922" ||
												userInfo.unitNo == "931" ||
												userInfo.unitNo == "932" ||
												userInfo.unitNo == "933" ||
												userInfo.unitNo == "934" ||
												userInfo.unitNo == "935")) {
													// 營運中心
													mainGrid5();
												}
												else 
													if (txCode == "337091" || txCode == "337092" || txCode == "337093" || txCode == "339091" || txCode == "339092" || txCode == "339093") {
														if (txCode == "337093") {
															Action.openFilterBox();
														}
														// 海外聯貸案
														mainGridSea();
													}
													else {
														mainGrid();
													}
    
    $("#buttonPanel").find("#btnAdd").click(function(){

        var genL120M01A_ByTypeForm = $("#genL120M01A_ByTypeForm");
        genL120M01A_ByTypeForm.injectData({'genL120M01A_ByType':'1'});
        $("#genL120M01A_ByTypeBoxDiv").thickbox({
            title: '', width: 400, height: 230, align: 'center', valign: 'bottom', modal: true, i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var genL120M01A_ByType = genL120M01A_ByTypeForm.find("[name='genL120M01A_ByType']:checked").val();
                    
                    if (genL120M01A_ByType == "1") {
						$.thickbox.close();
						Action.addCase();
					}
					else if (genL120M01A_ByType == "2") {
						$.thickbox.close();
						
						$("#createCntrNo_fast_prodKind71_termGroup option[value='GA']").remove();
						$("#createCntrNo_fast_prodKind71_termGroup option[value='GB']").remove();
						
						var _form = "createCntrNo_fast_prodKind71Form";
						
						$("#" + _form).find("#tr_EsgGtype_fast_prodKind71").hide();
						$("#" + _form).reset();
						$("#" + _form).injectData({
							'createCntrNo_fast_prodKind71_dupNo': '0'
							,'createCntrNo_fast_prodKind71_esggnLoanFg': 'N'  //在 reset 後，綠色支出類型 會被清空
						});
						
						$("#createCntrNo_fast_prodKind71Div").thickbox({
							title: '',
							width: 720,
							height: 430,
							align: 'center',
							valign: 'bottom',
							modal: true,
							i18n: i18n.def,
							buttons: {
								"sure": function(){
									if (!$("#" + _form).valid()) {
										return;
									}
									var frmJSON = $("#"+_form).serializeData();
									var createCntrNo_fast_prodKind71_custId = $("#" + _form).find("#createCntrNo_fast_prodKind71_custId").val();
									var createCntrNo_fast_prodKind71_dupNo = $("#" + _form).find("#createCntrNo_fast_prodKind71_dupNo").val();
									var createCntrNo_fast_prodKind71_esggnLoanFg = frmJSON["createCntrNo_fast_prodKind71_esggnLoanFg"];
									var createCntrNo_fast_prodKind71_esggtype = $("#" + _form).find("#createCntrNo_fast_prodKind71_esggtype").val();
									var createCntrNo_fast_prodKind71_esggtypeZMemo = $("#" + _form).find("#createCntrNo_fast_prodKind71_esggtypeZMemo").val();
									prepare_gen_71_caseDoc_tabDoc(createCntrNo_fast_prodKind71_custId, createCntrNo_fast_prodKind71_dupNo
											, createCntrNo_fast_prodKind71_esggnLoanFg
											, createCntrNo_fast_prodKind71_esggtype, createCntrNo_fast_prodKind71_esggtypeZMemo).done(function(json_prepare_gen_71_caseDoc_tabDoc){
										//~~~~
										var json_param = {
											'docKind': '1' //授權別 1授權內
											,
											'docCode': '1' //案件別 1一般
											,
											'authLvl': '1' //授權等級{1:分行授權內 ; 3:營運中心授權內}
											,
											'ngFlag': '0' //協議案註記 0無
										};
										cfm_msg_checkIsStop(json_param).done(function(){
											dfd_addL120m01a(json_param).done(function(caseDoc_param){
												dfd_71_importLendCollateral(caseDoc_param, json_prepare_gen_71_caseDoc_tabDoc.c101_mainId).done(function(){
													dfd_71_setMainCust(caseDoc_param).done(function(json_71_setMainCust){
														dfd_71_caseDoc_saveAll(caseDoc_param).done(function(){
															dfd_71_gen_tabDoc(caseDoc_param.mainId, json_71_setMainCust.c120m01a_oid, _form).done(function(json_71_gen_tabDoc){
																dfd_71_upd_caseDoc___tabDoc_cntrNo_npl(caseDoc_param.mainId, json_71_gen_tabDoc.tabMainId).done(function(){
																	//L140M01A 還有 {IsSuspectedHeadAccount:疑似人頭戶案件} 的 table 要寫入
																	dfd_import_simplifyFlag_D___L120S15A_content(caseDoc_param.mainId).done(function(){
																		dfd_import_property7_cntrNo(caseDoc_param.mainId, json_71_setMainCust.c120m01a_oid, "N").done(function(){
																			dfd_unlockDoc(caseDoc_param.mainId).done(function(){
																				//=======================
																				$.thickbox.close();
																				//=======================
																				//在 Grid 出現 新增的簽報書
																				$("#gridview").trigger("reloadGrid");
																			});
																		});
																	});
																});
															});
														});
													});
												});
											});
										});
									});
								},
								"cancel": function(){
									$.thickbox.close();
								}
							}
						});
						
					} else if (genL120M01A_ByType == "3") {
						
						QuickReviewForCreditLoans.init();
						QuickReviewForCreditLoans.open();						
					} else if (genL120M01A_ByType == "3B") {
					    FastCreditLoans.init();
                        FastCreditLoans.open();
					}
				
				},
                "cancel": function(){
                    $.thickbox.close();
                    
                }
            }
        });        
    	
    }).end().find("#btnModify").click(function(){
        var row2 = $("#gridview").getGridParam('selrow');
        var list2 = "";
        var data2 = $("#gridview").getRowData(row2);
        list2 = data2.oid;
        // alert(list);
        if (list2 == "" || list2 == undefined) {
            API.showMessage(i18n.cls1141v01["l120v01.alert1"]);
            return;
        }
        Action.openDoc(null, null, data2);
    }).end().find("#btnDelete").click(function(){
        var row = $("#gridview").getGridParam('selrow');
        var list = "";
        var data = $("#gridview").getRowData(row);
        list = data.oid;
        if (list == undefined) {
            API.showMessage(i18n.cls1141v01["l120v01.alert1"]);
            return false;
        }
        // confirmDelete=是否確定刪除?
        API.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                if (Action.queryDelectCase(data.mainId)) {
                    var $deleteReasonForm = $("#deleteReasonForm");
                    var item = CommonAPI.loadCombos(["CLSDeleteReason"]);
                    $deleteReasonForm.find("#deleteReason").hide();
                    if ($deleteReasonForm.find("#deleteReasonSelect option").length == 0) {
                        $deleteReasonForm.find("#deleteReasonSelect").setItems({
                            item: item.CLSDeleteReason,
                            format: ' {value}-{key}',
                            fn: function(){
                                if ($(this).val() == "A4") {
                                    $deleteReasonForm.find("#deleteReason").show();
                                }
                                else {
                                    $deleteReasonForm.find("#deleteReason").hide();
                                    $deleteReasonForm.find("#deleteReason").val("");
                                }
                            }
                        });
                    }
                    $deleteReasonForm.reset();
                    $deleteReasonForm.find("#deleteReason").removeAttr("readonly");
                    $deleteReasonForm.find("#deleteReasonSelect").removeAttr("disabled");
                    $('#openDeleteReasonBox').thickbox({
                        //C101M01A.deleteReason=刪除原因
                        title: i18n.cls1141v01['C101M01A.deleteReason'],
                        width: 500,
                        height: 200,
                        align: 'center',
                        valign: 'bottom',
                        buttons: {
                            'sure': function(){
                                if ($deleteReasonForm.valid()) {
                                    $.thickbox.close();
                                    if (Action.queryDeleteC122M01ACase(data.mainId)){
                                        API.confirmMessage("已有關聯進件管理是否一併刪除關聯?", function(b){
                                            if (b) {
                                                Action.deleteCase(list, $deleteReasonForm.find("#deleteReasonSelect").val(), $deleteReasonForm.find("#deleteReason").val());
                                            }
                                        });
                                    }
                                    else{
                                        Action.deleteCase(list, $deleteReasonForm.find("#deleteReasonSelect").val(), $deleteReasonForm.find("#deleteReason").val());
                                    }
                                }
                            },
                            'cancel': function(){
                                $.thickbox.close();
                            }
                        }
                    });
                    
                }
                else if (Action.queryDeleteC122M01ACase(data.mainId)){
                    API.confirmMessage("已有關聯進件管理是否一併刪除關聯?", function(b){
                        if (b) {
                            Action.deleteCase(list, $deleteReasonForm.find("#deleteReasonSelect").val(), $deleteReasonForm.find("#deleteReason").val());
                        }
                    });
                }
                else {
                    Action.deleteCase(list);
                }
            }
        });
    }).end().find("#btnChange").click(function(){
        Action.changeCase2();
    }).end().find("#btnChangeCaseFormat").click(function(){
        Action.changeCase();
    }).end().find("#btnView").click(function(){
    
        var id = $("#gridview").getGridParam('selrow');
        if (!id) {
        
            // action_004=請先選擇需「調閱」之資料列
            return API.showMessage(i18n.def["action_004"]);
            
        }
        var result = $("#gridview").getRowData(id);
        Action.openDoc(null, null, result);
    }).end().find("#btnFilter").click(function(){
        Action.openFilterBox();
    }).end().find("#btnCaseLvl").click(function(){
        Action.btnCaseLvl();
    }).end().find("#btnPrintBook").click(function(){
        var row = $("#gridview").getGridParam('selrow');
        var list = "";
        var data = $("#gridview").getRowData(row);
        list = data.oid;
        if (list == undefined) {
            API.showMessage(i18n.cls1141v01["l120v01.alert1"]);
            return;
        }
        
       $.capFileDownload({
            handler: "lmsdownloadformhandler",
            data: {
                fileName: "CLSDoc15.htm",
                oid: list,
                txCode: txCode,
                isCls: true,
                docTempType: "LMSDoc8",
                fileDownloadName: "LMSDoc15.doc",
                serviceName: "lms1201docservice"
            }
        });
    }).end().find("#btnPrintNote").click(function(){
        var row = $("#gridview").getGridParam('selrow');
        var list = "";
        var data = $("#gridview").getRowData(row);
        list = data.oid;
        if (list == undefined) {
            API.showMessage(i18n.cls1141v01["l120v01.alert1"]);
            return;
        }
       
        $.form.submit({
        	url: __ajaxHandler,
     		target : "_blank",
     		data : {
     			_pa : 'lmsdownloadformhandler',
     			oid: list,
                txCode: txCode,
                isCls: true,
                docTempType: "CLSDoc1",
                fileDownloadName: "CLSCheck.doc",  // 下載附檔名, 在此不能命名為 docx (參考FileDownLoadConstant)
                serviceName: "lms1201docservice"
     		}
     	 });
    }).end().find("#btnUPCls").click(function(){
        // 房貸評分卡上傳DW
        var tGrid = $("#gridview");
        var datas = [];
        var rows = tGrid.getGridParam('selarrrow');
        for (var o in rows) {
            datas.push(tGrid.getRowData(rows[o]).oid);
        }
        if (datas.length > 0) {
        }
        else {
            MegaApi.showErrorMessage(i18n.def['confirmTitle'], i18n.def['action_005']);
            return undefined;
        }
        
        $.ajax({
            handler: Action.fhandle,
            action: "upDwByL120M01A",
            data: {
                oids: datas
            },
            success: function(responseData){
            }
        });
    }).end().find("#btnCaseCopy").click(function(){
        var row = $("#gridview").getGridParam('selrow');
        var list = "";
        var data = $("#gridview").getRowData(row);
        list = data.mainId;
        if (list == undefined) {
            API.showMessage(i18n.cls1141v01["l120v01.alert1"]);
            return;
        }
        $.ajax({
        
            handler: Action.fhandle,
            action: "copyLms120m01a",
            data: {
            
                list: list
            },
            success: function(responseData){
            }
        });
    }).end().find("#btnCreDoc1").click(function(){
        $("#LMS1200V64Thickbox2").thickbox({
            title: i18n.cls1141v01["sure0"],
            width: 500,
            height: 200,
            modal: true,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var val = $("input[name='creDoc']:checked").val();
                    if (val == "1") {
                        // 個案討論
                        var ids = new Array();
                        ids = $("#gridview").getGridParam('selarrrow');
                        var list = "";
                        var sign = ",";
                        var count = 0;
                        for (var id in ids) {
                            var rows = $("#gridview").jqGrid('getRowData', ids[id]);
                            if (rows.oid != 'undefined' && rows.oid != null && rows.oid != 0) {
                                list += ((list == "") ? "" : sign) + rows.oid;
                            }
                            count++;
                        }
                        if (list == "") {
                            API.showMessage(i18n.cls1141v01["l120v01.alert1"]);
                            return;
                        }
                        else 
                            if (count > 1) {
                                API.showMessage(i18n.cls1141v01["l120v01.error2"]);
                                return;
                            }
                            else {
                            
                                $.form.submit({
                                    url: "../simple/FileProcessingService",
                                    target: "_blank",
                                    data: {
                                        sign: sign,
                                        fileName: "LMSDoc4.htm",
                                        fileName2: "LMSDoc41.htm",
                                        oid: list,
                                        txCode: txCode,
                                        docTempType: "LMSDoc4",
                                        fileDownloadName: "LMSDoc4.doc",
                                        serviceName: "lms1201docservice"
                                    }
                                });
                                $.thickbox.close();
                            }
                    }
                    else 
                        if (val == "2") {
                            // 彙總討論
                            var ids = new Array();
                            ids = $("#gridview").getGridParam('selarrrow');
                            var list = "";
                            var sign = ",";
                            var count = 0;
                            for (var id in ids) {
                                var rows = $("#gridview").jqGrid('getRowData', ids[id]);
                                if (rows.oid != 'undefined' &&
                                rows.oid != null &&
                                rows.oid != 0) {
                                    list += ((list == "") ? "" : sign) +
                                    rows.oid;
                                }
                                count++;
                            }
                            if (list == "") {
                                API.showMessage(i18n.cls1141v01["l120v01.alert1"]);
                                return;
                            }
                            else 
                                if (count > 1) {
                                    API.showMessage(i18n.cls1141v01["l120v01.error2"]);
                                    return;
                                }
                                else {
                                
                                    $.form.submit({
                                        url: "../simple/FileProcessingService",
                                        target: "_blank",
                                        data: {
                                            sign: sign,
                                            fileName: "LMSDoc51.htm",
                                            fileName2: "LMSDoc52.htm",
                                            oid: list,
                                            txCode: txCode,
                                            docTempType: "LMSDoc5",
                                            fileDownloadName: "LMSDoc51.doc",
                                            serviceName: "lms1201docservice"
                                        }
                                    });
                                    $.thickbox.close();
                                }
                        }
                        else {
                            // 常董會授權總經理逕行核定案件
                            var ids = new Array();
                            ids = $("#gridview").getGridParam('selarrrow');
                            var list = "";
                            var sign = ",";
                            var count = 0;
                            for (var id in ids) {
                                var rows = $("#gridview").jqGrid('getRowData', ids[id]);
                                if (rows.oid != 'undefined' && rows.oid != null && rows.oid != 0) {
                                    list += ((list == "") ? "" : sign) + rows.oid;
                                }
                                count++;
                            }
                            if (list == "") {
                                API.showMessage(i18n.cls1141v01["l120v01.alert1"]);
                                return;
                            }
                            else 
                                if (count > 1) {
                                    API.showMessage(i18n.cls1141v01["l120v01.error2"]);
                                    return;
                                }
                                else {
                                
                                    $.form.submit({
                                        url: "../simple/FileProcessingService",
                                        target: "_blank",
                                        data: {
                                            sign: sign,
                                            fileName: "LMSDoc61.htm",
                                            fileName2: "LMSDoc62.htm",
                                            listOid: list,
                                            txCode: txCode,
                                            docTempType: "LMSDoc6",
                                            docTempFile: "LMSDoc61.doc",
                                            fileDownloadName: "LMSDoc61.doc",
                                            serviceName: "lms1201docservice"
                                        }
                                    });
                                    $.form.submit({
                                        url: "../simple/FileProcessingService",
                                        target: "_blank",
                                        data: {
                                            sign: sign,
                                            fileName: "LMSDoc61.htm",
                                            fileName2: "LMSDoc62.htm",
                                            listOid: list,
                                            txCode: txCode,
                                            docTempType: "LMSDoc6",
                                            docTempFile: "LMSDoc62.doc",
                                            fileDownloadName: "LMSDoc62.doc",
                                            serviceName: "lms1201docservice"
                                        }
                                    });
                                    $.thickbox.close();
                                }
                        }
                },
                "cancel": function(){
                    API.confirmMessage(i18n.def['flow.exit'], function(res){
                        if (res) {
                            $.thickbox.close();
                        }
                    });
                }
            }
        });
    }).end().find("#btnTableSend").click(function(){
        // 已核准 - 額度明細表傳送聯行
        Action.btnTableSend();
    }).end().find("#btnGetCase").click(function(){
        // 待收案件 - 收件
        Action.getCase();
    }).end().find("#btnPrintArea").click(function(){
        //列印營運中心意見
        Action.printArea();
    }).end().find("#btnLogin1").click(function(){
        Action.login1();
    }).end().find("#btnLogin2").click(function(){
        Action.login2();
    }).end().find("#btnLogin3").click(function(){
        Action.login3();
    }).end().find("#btnCreate").click(function(){
        if (txCode == "339062" || txCode == "338023") {
            // 授審會
            $("#LMS1200V62Thickbox").thickbox({
                title: i18n.cls1141v01["l120m01a.btnCreate"],
                width: 500,
                height: 200,
                modal: true,
                valign: "bottom",
                align: "center",
                i18n: i18n.def,
                buttons: {
                    "sure": function(){
                        var val = $("input[name='create']:checked").val();
                        if (val == "1") {
                            // 議程
                            var ids = new Array();
                            ids = $("#gridview").getGridParam('selarrrow');
                            var list = "";
                            var sign = ",";
                            var count = 0;
                            var rptTitles = [];
                            for (var id in ids) {
                                var rows = $("#gridview").jqGrid('getRowData', ids[id]);
                                if (rows.oid != 'undefined' && rows.oid != null && rows.oid != "") {
                                    list += ((list == "") ? "" : sign) + rows.oid;
                                    if (rows.rptTitle1 == 'undefined' || rows.rptTitle1 == null || rows.rptTitle1 == '') {
                                        API.showMessage(i18n.cls1141v01["l120v01.error5"]);
                                        return;
                                    }
                                    else {
                                        rptTitles.push(rows.rptTitle1);
                                    }
                                }
                                count++;
                            }
                            // 檢查所選是否皆為同會期(同會期才可多選產生議程)
                            var title = rptTitles[0];
                            var canCreate = true;
                            for (o in rptTitles) {
                                if (title != rptTitles[o]) {
                                    canCreate = false;
                                }
                            }
                            if (list == "") {
                                API.showMessage(i18n.cls1141v01["l120v01.alert1"]);
                                return;
                            }
                            else 
                                if (count > 1 && !canCreate) {
                                    API.showMessage(i18n.cls1141v01["l120v01.error2"]);
                                    return;
                                }
                                else {
                                    $.form.submit({
                                        url: "../simple/FileProcessingService",
                                        target: "_blank",
                                        data: {
                                            sign: sign,
                                            fileName: "LMSDoc11.htm",
                                            listOid: list,
                                            txCode: txCode,
                                            docTempType: "LMSDoc1",
                                            fileDownloadName: "LMSDoc11.doc",
                                            serviceName: "lms1201docservice"
                                        }
                                    });
                                    $.thickbox.close();
                                }
                        }
                        else 
                            if (val == "2") {
                                // 決議錄(同會期之全部文件)
                                //var ids = $("#gridview").jqGrid('getDataIDs');
                                var list = "";
                                var sign = ",";
                                var ids2 = new Array();
                                ids2 = $("#gridview").getGridParam('selarrrow');
                                var list2 = "";
                                var sRptTitle = "";
                                var count = 0;
                                for (var id2 in ids2) {
                                    var rows2 = $("#gridview").jqGrid('getRowData', ids2[id2]);
                                    if (rows2.oid != 'undefined' && rows2.oid != null && rows2.oid != "") {
                                        if (rows2.rptTitle1 == 'undefined' || rows2.rptTitle1 == null || rows2.rptTitle1 == '') {
                                        
                                        }
                                        else {
                                            list2 += ((list2 == "") ? "" : sign) + rows2.oid;
                                            sRptTitle += ((sRptTitle == "") ? "" : sign) + rows2.rptTitle1;
                                        }
                                    }
                                    count++;
                                }
                                if (count > 1) {
                                    API.showMessage(i18n.cls1141v01["l120v01.error2"]);
                                    return;
                                }
                                
                                if (sRptTitle == 'undefined' || sRptTitle == null || sRptTitle == '') {
                                    // 沒有登錄會期，跳過
                                    API.showMessage(i18n.cls1141v01["l120v01.error5"]);
                                    return;
                                }
                                list = ajaxPcTitle(list2);
                                $.thickbox.close();
                                $.form.submit({
                                    url: "../simple/FileProcessingService",
                                    target: "_blank",
                                    data: {
                                        sign: sign,
                                        fileName: "LMSDoc2.xml",
                                        listOid: list,
                                        selOid: list2,
                                        txCode: txCode,
                                        docTempType: "LMSDoc2",
                                        fileDownloadName: "LMSDoc2.doc",
                                        serviceName: "lms1201docservice"
                                    }
                                });
                            }
                            else {
                                // 決議錄(同會期之勾選文件)
                                var ids = new Array();
                                ids = $("#gridview").getGridParam('selarrrow');
                                var list = "";
                                var sign = ",";
                                for (var id in ids) {
                                    var rows = $("#gridview").jqGrid('getRowData', ids[id]);
                                    if (rows.oid != 'undefined' && rows.oid != null && rows.oid != "") {
                                        list += ((list == "") ? "" : sign) + rows.oid;
                                        if (rows.rptTitle1 == 'undefined' || rows.rptTitle1 == null || rows.rptTitle1 == '') {
                                            API.showMessage(i18n.cls1141v01["l120v01.error5"]);
                                            return;
                                        }
                                    }
                                }
                                if (list == "") {
                                    API.showMessage(i18n.cls1141v01["l120v01.alert1"]);
                                    return;
                                }
                                else {
                                    $.form.submit({
                                        url: "../simple/FileProcessingService",
                                        target: "_blank",
                                        data: {
                                            sign: sign,
                                            fileName: "LMSDoc2.xml",
                                            listOid: list,
                                            selOid: "",
                                            txCode: txCode,
                                            docTempType: "LMSDoc2",
                                            fileDownloadName: "LMSDoc2.doc",
                                            serviceName: "lms1201docservice"
                                        }
                                    });
                                    $.thickbox.close();
                                }
                            }
                    },
                    "cancel": function(){
                        API.confirmMessage(i18n.def['flow.exit'], function(res){
                            if (res) {
                                $.thickbox.close();
                            }
                        });
                    }
                }
            });
        }
        else 
            if (txCode == "339063" || txCode == "338024") {
                // 催收會
                $("#LMS1200V63Thickbox").thickbox({
                    title: i18n.cls1141v01["l120m01a.btnCreate"],
                    width: 500,
                    height: 200,
                    modal: true,
                    valign: "bottom",
                    align: "center",
                    i18n: i18n.def,
                    buttons: {
                        "sure": function(){
                            var val = $("input[name='create']:checked").val();
                            if (val == "1") {
                                // 議程
                                var ids = new Array();
                                ids = $("#gridview").getGridParam('selarrrow');
                                var list = "";
                                var sign = ",";
                                var count = 0;
                                var rptTitles = [];
                                for (var id in ids) {
                                    var rows = $("#gridview").jqGrid('getRowData', ids[id]);
                                    if (rows.oid != 'undefined' && rows.oid != null && rows.oid != "") {
                                        list += ((list == "") ? "" : sign) + rows.oid;
                                        if (rows.rptTitle1 == 'undefined' || rows.rptTitle1 == null || rows.rptTitle1 == '') {
                                            API.showMessage(i18n.cls1141v01["l120v01.error5"]);
                                            return;
                                        }
                                        else {
                                            rptTitles.push(rows.rptTitle1);
                                        }
                                    }
                                    count++;
                                }
                                // 檢查所選是否皆為同會期(同會期才可多選產生議程)
                                var title = rptTitles[0];
                                var canCreate = true;
                                for (o in rptTitles) {
                                    if (title != rptTitles[o]) {
                                        canCreate = false;
                                    }
                                }
                                if (list == "") {
                                    API.showMessage(i18n.cls1141v01["l120v01.alert1"]);
                                    return;
                                }
                                else 
                                    if (count > 1 && !canCreate) {
                                        API.showMessage(i18n.cls1141v01["l120v01.error2"]);
                                        return;
                                    }
                                    else {
                                        $.form.submit({
                                            url: "../simple/FileProcessingService",
                                            target: "_blank",
                                            data: {
                                                sign: sign,
                                                fileName: "LMSDoc11.xml",
                                                listOid: list,
                                                txCode: txCode,
                                                docTempType: "LMSDoc1",
                                                fileDownloadName: "LMSDoc11.doc",
                                                serviceName: "lms1201docservice"
                                            }
                                        });
                                        $.thickbox.close();
                                    }
                            }
                            else 
                                if (val == "2") {
                                    // 決議錄(同會期之全部文件)
                                    var ids = $("#gridview").jqGrid('getDataIDs');
                                    var list = "";
                                    var sign = ",";
                                    
                                    var ids2 = new Array();
                                    ids2 = $("#gridview").getGridParam('selarrrow');
                                    var list2 = "";
                                    var sRptTitle = "";
                                    var count = 0;
                                    for (var id2 in ids2) {
                                        var rows2 = $("#gridview").jqGrid('getRowData', ids2[id2]);
                                        if (rows2.oid != 'undefined' && rows2.oid != null && rows2.oid != "") {
                                            if (rows2.rptTitle1 == 'undefined' || rows2.rptTitle1 == null || rows2.rptTitle1 == '') {
                                            
                                            }
                                            else {
                                                list2 += ((list2 == "") ? "" : sign) + rows2.oid;
                                                sRptTitle += ((sRptTitle == "") ? "" : sign) + rows2.rptTitle1;
                                            }
                                        }
                                        count++;
                                    }
                                    if (count > 1) {
                                        API.showMessage(i18n.cls1141v01["l120v01.error2"]);
                                        return;
                                    }
                                    
                                    if (sRptTitle == 'undefined' || sRptTitle == null || sRptTitle == '') {
                                        // 沒有登錄會期，跳過
                                        API.showMessage(i18n.cls1141v01["l120v01.error5"]);
                                        return;
                                    }
                                    
                                    list = ajaxPcTitle(list2);
                                    $.thickbox.close();
                                    $.form.submit({
                                        url: "../simple/FileProcessingService",
                                        target: "_blank",
                                        data: {
                                            sign: sign,
                                            fileName: "LMSDoc21.xml",
                                            listOid: list,
                                            selOid: list2,
                                            txCode: txCode,
                                            docTempType: "LMSDoc2",
                                            fileDownloadName: "LMSDoc21.doc",
                                            serviceName: "lms1201docservice"
                                        }
                                    });
                                }
                                else {
                                    // 決議錄(同會期之勾選文件)
                                    var ids = new Array();
                                    ids = $("#gridview").getGridParam('selarrrow');
                                    var list = "";
                                    var sign = ",";
                                    for (var id in ids) {
                                        var rows = $("#gridview").jqGrid('getRowData', ids[id]);
                                        if (rows.oid != 'undefined' && rows.oid != null && rows.oid != "") {
                                            list += ((list == "") ? "" : sign) + rows.oid;
                                            if (rows.rptTitle1 == 'undefined' || rows.rptTitle1 == null || rows.rptTitle1 == '') {
                                                API.showMessage(i18n.cls1141v01["l120v01.error5"]);
                                                return;
                                            }
                                        }
                                    }
                                    if (list == "") {
                                        API.showMessage(i18n.cls1141v01["l120v01.alert1"]);
                                        return;
                                    }
                                    else {
                                        $.form.submit({
                                            url: "../simple/FileProcessingService",
                                            target: "_blank",
                                            data: {
                                                sign: sign,
                                                fileName: "LMSDoc21.xml",
                                                listOid: list,
                                                selOid: list2,
                                                txCode: txCode,
                                                docTempType: "LMSDoc2",
                                                fileDownloadName: "LMSDoc21.doc",
                                                serviceName: "lms1201docservice"
                                            }
                                        });
                                        $.thickbox.close();
                                    }
                                }
                        },
                        "cancel": function(){
                            API.confirmMessage(i18n.def['flow.exit'], function(res){
                                if (res) {
                                    $.thickbox.close();
                                }
                            });
                        }
                    }
                });
            }
            else 
                if (txCode == "339064" || txCode == "338025") {
                    // 常董會
                    $("#LMS1200V64Thickbox").thickbox({
                        title: i18n.cls1141v01["l120m01a.btnCreate"],
                        width: 500,
                        height: 200,
                        modal: true,
                        valign: "bottom",
                        align: "center",
                        i18n: i18n.def,
                        buttons: {
                            "sure": function(){
                                var val = $("input[name='create']:checked").val();
                                if (val == "1") {
                                    // 議程
                                    var ids = new Array();
                                    ids = $("#gridview").getGridParam('selarrrow');
                                    var list = "";
                                    var sign = ",";
                                    var count = 0;
                                    var rptTitles = [];
                                    for (var id in ids) {
                                        var rows = $("#gridview").jqGrid('getRowData', ids[id]);
                                        if (rows.oid != 'undefined' && rows.oid != null && rows.oid != 0) {
                                            list += ((list == "") ? "" : sign) + rows.oid;
                                            if (rows.rptTitle2 == 'undefined' || rows.rptTitle2 == null || rows.rptTitle2 == '') {
                                                API.showMessage(i18n.cls1141v01["l120v01.error5"]);
                                                return;
                                            }
                                            else {
                                                rptTitles.push(rows.rptTitle2);
                                            }
                                        }
                                        count++;
                                    }
                                    // 檢查所選是否皆為同會期(同會期才可多選產生議程)
                                    var title = rptTitles[0];
                                    var canCreate = true;
                                    for (o in rptTitles) {
                                        if (title != rptTitles[o]) {
                                            canCreate = false;
                                        }
                                    }
                                    if (list == "") {
                                        API.showMessage(i18n.cls1141v01["l120v01.alert1"]);
                                        return;
                                    }
                                    else 
                                        if (count > 1 && !canCreate) {
                                            API.showMessage(i18n.cls1141v01["l120v01.error2"]);
                                            return;
                                        }
                                        else {
                                            $.form.submit({
                                                url: "../simple/FileProcessingService",
                                                target: "_blank",
                                                data: {
                                                    sign: sign,
                                                    fileName: "LMSDoc1.xml",
                                                    listOid: list,
                                                    txCode: txCode,
                                                    docTempType: "LMSDoc1",
                                                    fileDownloadName: "LMSDoc1.doc",
                                                    serviceName: "lms1201docservice"
                                                }
                                            });
                                            $.thickbox.close();
                                        }
                                }
                                else 
                                    if (val == "2") {
                                        // 決議錄(同會期之全部文件)
                                        var ids = $("#gridview").jqGrid('getDataIDs');
                                        var list = "";
                                        var sign = ",";
                                        var ids2 = new Array();
                                        ids2 = $("#gridview").getGridParam('selarrrow');
                                        var list2 = "";
                                        var sRptTitle = "";
                                        var count = 0;
                                        for (var id2 in ids2) {
                                            var rows2 = $("#gridview").jqGrid('getRowData', ids2[id2]);
                                            if (rows2.oid != 'undefined' && rows2.oid != null && rows2.oid != "") {
                                                if (rows2.rptTitle2 == 'undefined' || rows2.rptTitle2 == null || rows2.rptTitle2 == '') {
                                                
                                                }
                                                else {
                                                    list2 += ((list2 == "") ? "" : sign) + rows2.oid;
                                                    sRptTitle += ((sRptTitle == "") ? "" : sign) + rows2.rptTitle2;
                                                }
                                            }
                                            count++;
                                        }
                                        if (count > 1) {
                                            API.showMessage(i18n.cls1141v01["l120v01.error2"]);
                                            return;
                                        }
                                        
                                        if (sRptTitle == 'undefined' || sRptTitle == null || sRptTitle == '') {
                                            // 沒有登錄會期，跳過
                                            API.showMessage(i18n.cls1141v01["l120v01.error5"]);
                                            return;
                                        }
                                        list = ajaxPcTitle(list2);
                                        $.thickbox.close();
                                        $.form.submit({
                                            url: "../simple/FileProcessingService",
                                            target: "_blank",
                                            data: {
                                                sign: sign,
                                                fileName: "LMSDoc3.xml",
                                                listOid: list,
                                                selOid: list2,
                                                txCode: txCode,
                                                docTempType: "LMSDoc2",
                                                fileDownloadName: "LMSDoc3.doc",
                                                serviceName: "lms1201docservice"
                                            }
                                        });
                                    }
                                    else {
                                        // 決議錄(同會期之勾選文件)
                                        var ids = new Array();
                                        ids = $("#gridview").getGridParam('selarrrow');
                                        var list = "";
                                        var sign = ",";
                                        var count = 0;
                                        for (var id in ids) {
                                            var rows = $("#gridview").jqGrid('getRowData', ids[id]);
                                            if (rows.oid != 'undefined' && rows.oid != null && rows.oid != 0) {
                                                list += ((list == "") ? "" : sign) + rows.oid;
                                                if (rows.rptTitle2 == 'undefined' || rows.rptTitle2 == null || rows.rptTitle2 == '') {
                                                    API.showMessage(i18n.cls1141v01["l120v01.error5"]);
                                                    return;
                                                }
                                            }
                                            count++;
                                        }
                                        if (list == "") {
                                            API.showMessage(i18n.cls1141v01["l120v01.alert1"]);
                                            return;
                                        }
                                        else 
                                            if (count > 1) {
                                                API.showMessage(i18n.cls1141v01["l120v01.error2"]);
                                                return;
                                            }
                                            else {
                                                $.form.submit({
                                                    url: "../simple/FileProcessingService",
                                                    target: "_blank",
                                                    data: {
                                                        sign: sign,
                                                        fileName: "LMSDoc3.xml",
                                                        listOid: list,
                                                        selOid: list2,
                                                        txCode: txCode,
                                                        docTempType: "LMSDoc2",
                                                        fileDownloadName: "LMSDoc3.doc",
                                                        serviceName: "lms1201docservice"
                                                    }
                                                });
                                                $.thickbox.close();
                                            }
                                    }
                            },
                            "cancel": function(){
                                API.confirmMessage(i18n.def['flow.exit'], function(res){
                                    if (res) {
                                        $.thickbox.close();
                                    }
                                });
                            }
                        }
                    });
                }
                else {
                    $("#createLMSThick").thickbox({
                        title: i18n.cls1141v01["l120m01a.btnCreate"],
                        width: 500,
                        height: 200,
                        modal: true,
                        valign: "bottom",
                        align: "center",
                        i18n: i18n.def,
                        buttons: {
                            "sure": function(){
                                var val = $("input[name='selectKind']:checked").val();
                                if (val == "1") {
                                    // 授信案件明細表
                                    var ids = new Array();
                                    ids = $("#gridview").getGridParam('selarrrow');
                                    var list = "";
                                    var sign = ",";
                                    for (var id in ids) {
                                        var rows = $("#gridview").jqGrid('getRowData', ids[id]);
                                        if (rows.oid != 'undefined' && rows.oid != null && rows.oid != 0) {
                                            list += ((list == "") ? "" : sign) + rows.oid;
                                        }
                                    }
                                    if (list == "") {
                                        API.showMessage(i18n.cls1141v01["l120v01.alert1"]);
                                        return;
                                    }
                                    else {
                                    
                                        $.form.submit({
                                            url: "../simple/FileProcessingService",
                                            target: "_blank",
                                            data: {
                                                sign: sign,
                                                fileName: "LMSDoc71.htm",
                                                listOid: list,
                                                txCode: txCode,
                                                docTempType: "LMSDoc7",
                                                fileDownloadName: "LMSDoc71.doc",
                                                serviceName: "lms1201docservice"
                                            }
                                        });
                                        $.thickbox.close();
                                    }
                                }
                                else {
                                // 本功能尚未實作
                                }
                            },
                            "cancel": function(){
                                API.confirmMessage(i18n.def['flow.exit'], function(res){
                                    if (res) {
                                        $.thickbox.close();
                                    }
                                });
                            }
                        }
                    });
                }
    }).end().find("#btnCntrNoControl").click(function(){
    	CntrNo_LN_CONTROL_Action.runQuery();
    }).end().find("#btnBatchSelectAprvProd69").click(function(){
    	//2020/05/20 add 勞工紓困案整批勾選
		
		//判斷是否為prod69
		var rowData = $("#gridview").getRowData();
		var resMaindId=[];
		for (var i = 0; i < rowData.length; i++) {
			resMaindId.push(rowData[i].mainId);
			
		}
		
		$.ajax({
	            handler: "cls1141m01formhandler",
	            data: {
	                formAction: "checkIsProd69",
	                mainIds: resMaindId
            	},
	            success: function(result){
					//alert(result.mainIdByIsProd69);
					for (var i = 0; i < rowData.length; i++) {
						if (result.mainIdByIsProd69.indexOf(rowData[i].mainId) > -1) {
							if ($("#gridview #" + (i + 1) + " input[type=checkbox]:checked").length == 0) {
								//排除已經選取
								//改用click方法
								//$("#jqg_gridview_" + (i + 1) ).attr('checked', 'checked');
								$("#gridview #" + (i + 1) ).click();
							}
						}
					}
	            }
	        });
    }).end().find("#btnBatchAprvProd69").click(function(){
		//2020/05/20 add 勞工紓困案整批覆核
        var ret = $("#gridview").jqGrid('getGridParam', 'selarrrow');
        ret && ret.length != 0  ? API.confirmMessage(i18n.def["confirmApprove"], function(result){
            if (result) {
				var resOid=[] , resAuthLvl=[];
				for( var i=0; i < ret.length; i++ ){
					resOid.push($("#gridview").getRowData(ret[i]).oid);
				}
                $("#aprv_dialog").thickbox({
                    open: function(){
                        //增加覆核預設值
                        $('input[name="queryButtonType"][value="8"]').prop("checked", true);
                    },
                    modal: false,
                    height: 100,
                    width: 300,
                    valign: 'bottom',
                    align: 'center',
                    buttons: {
						"sure": function(){
			                var value = $("[name=queryButtonType]:checked").val();
							
							var flowChkDateDfd = $.Deferred();
							
							//分行內-輸入核准日 checkDate
							var checkDate;
							
							if (value != "1") {
								//checkDate()
								$("#forCheckDate").val(CommonAPI.getToday());
							    $("#openChecDatekBox").thickbox({ // 使用選取的內容進行彈窗
							        title: i18n.cls1141v01["l120v01.chkDate"],
							        width: 100,
							        height: 100,
							        modal: true,
							        valign: "bottom",
							        align: "center",
							        readOnly: false,
							        i18n: i18n.def,
							        buttons: {
							            "sure": function(){
							                var forCheckDate = $("#forCheckDate").val();
							                if ($.trim(forCheckDate) == "") {
							                    return CommonAPI.showErrorMessage(i18n.cls1141v01["l120v01.chkDate"]);
							                }
											checkDate = forCheckDate;
											flowChkDateDfd && flowChkDateDfd.resolve();
											
	//							                flowAction({
	//							                            flowAction: "check",
	//							                            checkDate: forCheckDate
	//					                        });
							                $.thickbox.close();
							            },
							            
							            "cancel": function(){
							                $.thickbox.close();
							            }
							        }
							    });
							} else {
								flowChkDateDfd && flowChkDateDfd.resolve();
							}
							
							
							flowChkDateDfd.done(function(){
								$.ajax({
							            handler: "cls1141m01formhandler",
							            data: {
							                formAction: "flowActionBatch",
											flowAction: value == "1" ? "backFirst" : "BatchApprove",
							                oids: resOid,
											checkDate: checkDate
						            	},
							            success: function(){
											$.thickbox.close();
							                $("#gridview").trigger("reloadGrid");
							                API.showPopMessage(i18n.def["runSuccess"], $.thickbox.close());
							            }
							        });
							});
			            },
			            "cancel": function(){
			                API.confirmMessage(i18n.def['flow.exit'], function(res){
			                    if (res) {
			                        $.thickbox.close();
			                    }
			                });
        				}
					}
                });
            }
        }) : API.showErrorMessage(i18n.def.action_005);
    }).end().find("#btnImportClsAreaPriceExcel").click(function(){
        // 上傳分組授權金額控管表
        $('#progressTr').hide();
        $('#downloadExcel').hide();
        if ($('#excelId').val()){
            $('#downloadExcel').show();
        }
        //=================
        var btnObj = {};

        if( $("#btUploadExcel").length>0 ){
            btnObj['saveData'] = function() {
                if ($("#"+Action.xlsFrm).valid()) {
                    $.thickbox.close();
                    MegaApi.showPopMessage(i18n.def["confirmTitle"], i18n.def['runSuccess']);
                    Action.init();
                }
            };
        }
        btnObj['close'] = function() {
            $.thickbox.close();
        };
        //=================
//        if(!$("#buttonPanel").find("#btnSend").is("button")) {
//            $("#btUploadExcel").addClass(" ui-state-disabled ").attr("disabled", "true");
//        }else{
//
//        }
        $('#divXlsBox').thickbox({
            title: i18n.def['button.btUploadExcel'],
            width: 600,
            height: 250,
            align: 'center',
            valign: 'bottom',
            buttons: btnObj
        });
    }).end().find("#btnReBackApproveUnit").click(function(){
    	//J-113-0306 Eloan>企業授信>案件簽報書送呈區域中心審核後，若被退件，在「待補件/撤件」中之被撤件之案件，能否設計可以再撈到編製中重新簽報，以增進作業效率
    	//撤件案件重新傳回分行編製中
        var row = $("#gridview").getGridParam('selrow');
        var oid = "";
        var data = $("#gridview").getRowData(row);
        oid = data.oid;
        if (oid == undefined) {
            CommonAPI.showMessage(i18n.cls1141v01["l120v01.alert1"]);
            return;
        }
        // confirmDelete=撤件案件重新傳回分行編製中
        CommonAPI.confirmMessage(i18n.cls1141v01["l120v05.alert01"], function(b){
            if (b) {
                $.ajax({
                    type: "POST",
                    handler: Action.fhandle,
                    data: {
                        formAction: "reBackL120m01a",
                        oid: oid
                    },
                    success: function(responseData){
                    	//CommonAPI.showMessage(responseData.NOTIFY_MESSAGE);	                       
                    	$("#gridview").trigger("reloadGrid");// 更新Grid內容
                    }
                });
            }
        });
    });

    //build button
    $("#"+Action.xlsFrm).find('#downloadExcel').click(function(){
        //下載EXCEL
        $.capFileDownload({
            data: {
                fileOid: $('#excelId').val()
            }
        });
    }).end().find('#btUploadExcel').click(function(){
        var mainId = null;
        var oId = null;
        $.ajax({
            handler: Action.fhandle,
            action: "getmainId",
            type: 'post',
            async: false,
            success: function(responseData){
                mainId = responseData['mainId'];
            }
        });
        //上傳EXCEL
        MegaApi.uploadDialog({
            fieldId: "fileName",
            width: 320,
            height: 100,
            fileCheck: ['.xls'],
            data: {
                deleteDup: true,
//                uid: responseJSON.oid, //避免此檔案被列為此文件之附加檔案
                mainId: mainId
            },
            success: function(response){
                 Action.callSrvImpXls( {'excelId':response.fileKey ,'mainId':mainId} );
            }
        });
    });

    //列印
	if ($("#myGrid").length > 0) {
    $("#printAreaGrid").iGrid({
        handler: Action.ghandle,
        height: 350,
        sortname: 'caseDate',
		regional: "en" ,
        postData: {
            formAction: "queryPrintArea",
            rowNum: 15
        },
        rowNum: 15,
        //  multiselect: true,
        // hideMultiselect: false,
        colModel: [{
            colHeader: i18n.cls1141v01["l120m01a.casedate"], // 簽案日期
            align: "center",
            width: 70,
            sortable: true,
            name: 'caseDate'
        }, {
            colHeader: i18n.cls1141v01["l120m01a.custid"], // 統一編號
            align: "left",
            width: 80,
            sortable: true,
            name: 'custId'
        }, {
            colHeader: i18n.cls1141v01["l120m01a.custname"], // 客戶名稱
            align: "left",
            width: 120,
            sortable: true,
            name: 'custName'
        }, {
            colHeader: i18n.cls1141v01["l120m01a.caseno"], // 案件號碼
            align: "left",
            width: 150,
            sortable: true,
            name: 'caseNo'
        }, {
            colHeader: i18n.cls1141v01["l120m01a.docname"],// "文件名稱"(授權別+'案件簽報書'+案件別),
            name: 'docKind',
            align: "left",
            width: 110,
            sortable: true
        }, {
            colHeader: i18n.cls1141v01["l120m01a.docstatus"], // 目前文件狀態
            align: "left",
            width: 50,
            sortable: true,
            name: 'docStatus'
        }, {
            colHeader: i18n.cls1141v01["l120m01a.creatorname"], // 建立人員名稱
            align: "left",
            width: 90,
            sortable: true,
            name: (userInfo.unitNo == "920" || userInfo.unitNo == "922" ||
            userInfo.unitNo == "931" ||
            userInfo.unitNo == "932" ||
            userInfo.unitNo == "933" ||
            userInfo.unitNo == "934" ||
            userInfo.unitNo == "935") ? 'areaAppraiser' : (userInfo.unitNo == "918") ? 'hqAppraiser' : 'updater' // col.id
        }, {
            colHeader: "areaDocstatus",
            name: 'areaDocstatus',
            hidden: true
        }, {
            colHeader: "rptTitle1",
            name: 'rptTitle1',
            hidden: true
        }, {
            colHeader: "rptTitle2",
            name: 'rptTitle2',
            hidden: true
        }, {
            colHeader: "docType",
            name: 'docType',
            hidden: true
        }, {
            colHeader: "docRslt",
            name: 'docRslt',
            hidden: true
        }, {
            colHeader: "docCode",
            name: 'docCode',
            hidden: true
        }, {
            colHeader: "docURL",
            name: 'docURL',
            hidden: true
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }, {
            colHeader: "mainId",
            name: 'mainId',
            hidden: true
        }, {
            colHeader: "srcMainId",
            name: 'srcMainId',
            hidden: true
        }, {
            colHeader: "uid",
            name: 'uid',
            hidden: true
        }]
    });
	
	}
    $("#EsgGtypeClsDescPdf_fast_prodKind71").click(function(){
    	$.form.submit({
            url: webroot + "/app/simple/FileProcessingService",
            target: "_blank",
            data: {
                fileId: "EsgGtypeClsDesc.pdf",
                fileDownloadName: "EsgGtypeClsDesc.pdf",
                serviceName: "lmsfiledownloadservice"
            }
        });
	});
    $("#EsgGtypeClsDescPdf_brmp").click(function(){
    	$.form.submit({
            url: webroot + "/app/simple/FileProcessingService",
            target: "_blank",
            data: {
                fileId: "EsgGtypeClsDesc.pdf",
                fileDownloadName: "EsgGtypeClsDesc.pdf",
                serviceName: "lmsfiledownloadservice"
            }
        });
	});
    
	$("#createCntrNo_brmp_creditCheck_introduceSrc").change(function(){
		IntroductionSource_PaperlessSigning.change();
	});
	
	$("#megaCode").change(function(){
		IntroductionSource_PaperlessSigning.loadMegaSubUnitNo($(this).val());
	});
	
	$("#importCustOrComButton").click(function(){
		IntroductionSource_PaperlessSigning.importCustomerOrCompanyInfo();
	});
	
	$("#selectBranchLink").click(function(){
		IntroductionSource_PaperlessSigning.selectBranch();
	});
	//客戶統編基本檢核通過後, 取得該分行最新徵信資料之brmp發查客群相關
    //帶出termGroup(客群)、applyDBRType>>termGroupSub(客群子類別)
    //新增>> 專案信貸(歡喜信貸)
    $("#createCntrNo_fast_prodKind71Form").find("#createCntrNo_fast_prodKind71_custId").change(function(){
        prepare_gen_71_brmp_termGroupRule($("#createCntrNo_fast_prodKind71_custId").val(), $("#createCntrNo_fast_prodKind71_dupNo").val(),
                $("select#createCntrNo_fast_prodKind71_termGroup"), $("#createCntrNo_fast_prodKind71_termGroupSub"),
                $("#span_createCntrNo_fast_prodKind71_termGroupRuleResultText"));
    });
    $("#createCntrNo_fast_prodKind71Form").find("#createCntrNo_fast_prodKind71_dupNo").change(function(){
        prepare_gen_71_brmp_termGroupRule($("#createCntrNo_fast_prodKind71_custId").val(), $("#createCntrNo_fast_prodKind71_dupNo").val(),
                $("select#createCntrNo_fast_prodKind71_termGroup"), $("#createCntrNo_fast_prodKind71_termGroupSub"),
                $("#span_createCntrNo_fast_prodKind71_termGroupRuleResultText"));
    });
});
});



//營運中心 Grid顯示
function mainGrid5(){
    var L120M01aGrid = $("#gridview").iGrid({
        handler: Action.ghandle,
        width: 785,
        height: 350,
        sortname: 'caseDate|caseNo',
        sortorder: 'desc|asc',
        shrinkToFit: true,
        autowidth: false,
        postData: {
            formAction: "queryL120m01a",
            mainDocStatus: viewstatus,
            rowNum: 15
        },
        rowNum: 15,
        
        
        colModel: [{
            colHeader: i18n.cls1141v01["l120m01a.other1"], // 案件經辦
            align: "left",
            width: 70,
            sortable: true,
            formatter: 'click',
            onclick: Action.openDoc,
            name: 'areaAppraiser'
        }, {
            colHeader: i18n.cls1141v01["l120m01a.other2"], // 單位別
            align: "left",
            width: 80,
            sortable: true,
            //					formatter : 'click',
            //					onclick : openDoc,
            name: 'caseBrId'
        }, {
            colHeader: i18n.cls1141v01["l120m01a.other3"], // 放行日期
            align: "left",
            width: 120,
            sortable: true,
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d',
                newformat: 'Y-m-d'
            },
            
            name: 'areaSendInfo'
        }, {
            colHeader: i18n.cls1141v01["l120m01a.custname"], // 主要借款人
            align: "left",
            width: 150,
            sortable: true,
            
            
            name: 'custName'
        }, {
            colHeader: i18n.cls1141v01["l120m01a.caseno"], // 案件號碼
            align: "left",
            width: 150,
            sortable: true,
            
            
            name: 'caseNo'
        }, {
            colHeader: i18n.cls1141v01["l120m01a.docname"],// "文件名稱"(授權別+'案件簽報書'+案件別),
            name: 'docKind',
            align: "left",
            width: 100,
            sortable: true
        }, {
            colHeader: "areaDocstatus",
            name: 'areaDocstatus',
            hidden: true
        }, {
            colHeader: "areaBrId",
            name: 'areaBrId',
            hidden: true
        }, {
            colHeader: "docType",
            name: 'docType',
            hidden: true
        }, {
            colHeader: "docRslt",
            name: 'docRslt',
            hidden: true
        }, {
            colHeader: "docCode",
            name: 'docCode',
            hidden: true
        }, {
            colHeader: "docURL",
            name: 'docURL',
            hidden: true
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }, {
            colHeader: "mainid",
            name: 'mainId',
            hidden: true
        }, {
            colHeader: "ownBrId",
            name: 'ownBrId',
            hidden: true
        }, {
            colHeader: "authLvl",
            name: 'authLvl',
            hidden: true
        }, {
            colHeader: "areaChk",
            name: 'areaChk',
            hidden: true
        }, {
            colHeader: "hqMeetFlag",
            name: 'hqMeetFlag',
            hidden: true
        }, {
            colHeader: "uid",
            name: 'uid',
            hidden: true
        }],
        ondblClickRow: function(rowid){
            var data = L120M01aGrid.getRowData(rowid);
            Action.openDoc(null, null, data);
        }
    });
}

// 授管處 Grid顯示
function mainGrid4(){
    var L120M01aGrid = $("#gridview").iGrid({
        handler: Action.ghandle,
        width: 785,
        height: 350,
        //sortname: 'caseDate',
        sortname: 'caseDate|caseNo',
        sortorder: 'desc|asc',
        shrinkToFit: true,
        autowidth: false,
        postData: {
            formAction: "queryL120m01a",
            mainDocStatus: viewstatus,
            rowNum: 15
        },
        rowNum: 15,
        colModel: [{
            colHeader: i18n.cls1141v01["l120m01a.other1"], // 案件經辦
            align: "left",
            width: 70,
            sortable: true,
            formatter: 'click',
            onclick: Action.openDoc,
            name: 'hqAppraiser'
        }, {
            colHeader: i18n.cls1141v01["l120m01a.other2"], // 單位別
            align: "left",
            width: 80,
            sortable: true,
            name: 'caseBrId'
        }, {
            colHeader: i18n.cls1141v01["l120m01a.other3"], // 放行日期
            align: "left",
            width: 120,
            sortable: true,
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d',
                newformat: 'Y-m-d'
            },
            
            name: 'endDate'
        }, {
            colHeader: i18n.cls1141v01["l120m01a.custname"], // 主要借款人
            align: "left",
            width: 150,
            sortable: true,
            
            
            name: 'custName'
        }, {
            colHeader: i18n.cls1141v01["l120m01a.caseno"], // 案件號碼
            align: "left",
            width: 150,
            sortable: true,
            
            
            name: 'caseNo'
        }, {
            colHeader: i18n.cls1141v01["l120m01a.docname"],// "文件名稱"(授權別+'案件簽報書'+案件別),
            name: 'docKind',
            align: "left",
            width: 100,
            sortable: true
        }, {
            colHeader: "areaDocstatus",
            name: 'areaDocstatus',
            hidden: true
        }, {
            colHeader: "areaBrId",
            name: 'areaBrId',
            hidden: true
        }, {
            colHeader: "docType",
            name: 'docType',
            hidden: true
        }, {
            colHeader: "docRslt",
            name: 'docRslt',
            hidden: true
        }, {
            colHeader: "docCode",
            name: 'docCode',
            hidden: true
        }, {
            colHeader: "docURL",
            name: 'docURL',
            hidden: true
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }, {
            colHeader: "mainid",
            name: 'mainId',
            hidden: true
        }, {
            colHeader: "ownBrId",
            name: 'ownBrId',
            hidden: true
        }, {
            colHeader: "authLvl",
            name: 'authLvl',
            hidden: true
        }, {
            colHeader: "areaChk",
            name: 'areaChk',
            hidden: true
        }, {
            colHeader: "hqMeetFlag",
            name: 'hqMeetFlag',
            hidden: true
        }, {
            colHeader: "uid",
            name: 'uid',
            hidden: true
        }],
        ondblClickRow: function(rowid){
            var data = L120M01aGrid.getRowData(rowid);
            Action.openDoc(null, null, data);
        }
    });
}

function mainGrid3(kind){
    var L120M01aGrid = $("#gridview").iGrid({
        handler: Action.ghandle,
        width: 785,
        height: 350,
        sortname: 'caseDate|caseNo',
        sortorder: 'desc|asc',
        shrinkToFit: true,
        autowidth: false,
        postData: {
            formAction: "queryL120m01a3",
            mainDocStatus: viewstatus,
            kind: kind,
            rowNum: 15
        },
        rowNum: 15,
        multiselect: true,
        hideMultiselect: false,
        colModel: [{
            colHeader: i18n.cls1141v01["l120m01a.casedate"], // 簽案日期
            align: "center",
            width: 70,
            sortable: true,
            name: 'caseDate'
        }, {
            colHeader: i18n.cls1141v01["l120m01a.custid"], // 統一編號
            align: "left",
            width: 80,
            sortable: true,
            formatter: 'click',
            onclick: Action.openDoc,
            name: 'custId'
        }, {
            colHeader: i18n.cls1141v01["l120m01a.custname"], // 客戶名稱
            align: "left",
            width: 100,
            sortable: true,
            
            
            name: 'custName'
        }, {
            colHeader: i18n.cls1141v01["l120m01a.caseno"], // 案件號碼
            align: "left",
            width: 150,
            sortable: true,
            
            
            name: 'caseNo'
        }, {
            colHeader: i18n.cls1141v01["l120m01a.docname"],// "文件名稱"(授權別+'案件簽報書'+案件別),
            name: 'docKind',
            align: "left",
            width: 100,
            sortable: true
        }, {
            colHeader: i18n.cls1141v01["l120m01a.docstatus"], // 目前文件狀態
            align: "left",
            width: 50,
            sortable: true,
            
            
            hidden: true,
            name: 'docStatus'
        }, {
            colHeader: i18n.cls1141v01["l120m01a.creatorname"], // 建立人員名稱
            align: "left",
            width: 80,
            sortable: true,
            
            
            name: (userInfo.unitNo == "920" || userInfo.unitNo == "922" ||
            userInfo.unitNo == "931" ||
            userInfo.unitNo == "932" ||
            userInfo.unitNo == "933" ||
            userInfo.unitNo == "934" ||
            userInfo.unitNo == "935") ? 'areaAppraiser' : (userInfo.unitNo == "918") ? 'hqAppraiser' : 'updater' // col.id
        }, {
            colHeader: i18n.cls1141v01["l120m01a.rpttitle1"],
            name: 'rptTitle1',
            width: 130,
            sortable: true
        }, {
            colHeader: "areaDocstatus",
            name: 'areaDocstatus',
            hidden: true
        }, {
            colHeader: "areaBrId",
            name: 'areaBrId',
            hidden: true
        }, {
            colHeader: "rptTitle2",
            name: 'rptTitle2',
            hidden: true
        }, {
            colHeader: "docType",
            name: 'docType',
            hidden: true
        }, {
            colHeader: "docRslt",
            name: 'docRslt',
            hidden: true
        }, {
            colHeader: "docCode",
            name: 'docCode',
            hidden: true
        }, {
            colHeader: "docURL",
            name: 'docURL',
            hidden: true
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }, {
            colHeader: "mainId",
            name: 'mainId',
            hidden: true
        }, {
            colHeader: "srcMainId",
            name: 'srcMainId',
            hidden: true
        }, {
            colHeader: "ownBrId",
            name: 'ownBrId',
            hidden: true
        }, {
            colHeader: "authLvl",
            name: 'authLvl',
            hidden: true
        }, {
            colHeader: "areaChk",
            name: 'areaChk',
            hidden: true
        }, {
            colHeader: "hqMeetFlag",
            name: 'hqMeetFlag',
            hidden: true
        }, {
            colHeader: "uid",
            name: 'uid',
            hidden: true
        }, {
            colHeader: "caseBrId",
            name: 'caseBrId',
            hidden: true
        }],
        ondblClickRow: function(rowid){
            var data = L120M01aGrid.getRowData(rowid);
            Action.openDoc(null, null, data);
        }
    });
}

function mainGrid2(){
    var L120M01aGrid = $("#gridview").iGrid({
        handler: Action.ghandle,
        width: 785,
        height: 350,
        sortname: 'caseDate|caseNo',
        sortorder: 'desc|asc',
        shrinkToFit: true,
        autowidth: false,
        postData: {
            formAction: "queryL120m01a3",
            mainDocStatus: viewstatus,
            kind: "3",
            rowNum: 15
        },
        rowNum: 15,
        multiselect: true,
        hideMultiselect: false,
        colModel: [{
            colHeader: i18n.cls1141v01["l120m01a.casedate"], // 簽案日期
            align: "center",
            width: 70,
            sortable: true,
            name: 'caseDate'
        }, {
            colHeader: i18n.cls1141v01["l120m01a.custid"], // 統一編號
            align: "left",
            width: 80,
            sortable: true,
            formatter: 'click',
            onclick: Action.openDoc,
            name: 'custId'
        }, {
            colHeader: i18n.cls1141v01["l120m01a.custname"], // 客戶名稱
            align: "left",
            width: 100,
            sortable: true,
            
            
            name: 'custName'
        }, {
            colHeader: i18n.cls1141v01["l120m01a.caseno"], // 案件號碼
            align: "left",
            width: 150,
            sortable: true,
            
            
            name: 'caseNo'
        }, {
            colHeader: i18n.cls1141v01["l120m01a.docname"],// "文件名稱"(授權別+'案件簽報書'+案件別),
            name: 'docKind',
            align: "left",
            width: 100,
            sortable: true
        }, {
            colHeader: i18n.cls1141v01["l120m01a.docstatus"], // 目前文件狀態
            align: "left",
            width: 50,
            sortable: true,
            
            
            hidden: true,
            name: 'docStatus'
        }, {
            colHeader: i18n.cls1141v01["l120m01a.creatorname"], // 建立人員名稱
            align: "left",
            width: 80,
            sortable: true,
            name: (userInfo.unitNo == "920" || userInfo.unitNo == "922" ||
            userInfo.unitNo == "931" ||
            userInfo.unitNo == "932" ||
            userInfo.unitNo == "933" ||
            userInfo.unitNo == "934" ||
            userInfo.unitNo == "935") ? 'areaAppraiser' : (userInfo.unitNo == "918") ? 'hqAppraiser' : 'updater' // col.id
        }, {
            colHeader: i18n.cls1141v01["l120m01a.rpttitle2"], //常董會會期
            name: 'rptTitle2',
            width: 130,
            sortable: true
        }, {
            colHeader: "areaDocstatus",
            name: 'areaDocstatus',
            hidden: true
        }, {
            colHeader: "areaBrId",
            name: 'areaBrId',
            hidden: true
        }, {
            colHeader: "rptTitle2",
            name: 'rptTitle2',
            hidden: true
        }, {
            colHeader: "docType",
            name: 'docType',
            hidden: true
        }, {
            colHeader: "docRslt",
            name: 'docRslt',
            hidden: true
        }, {
            colHeader: "docCode",
            name: 'docCode',
            hidden: true
        }, {
            colHeader: "docURL",
            name: 'docURL',
            hidden: true
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }, {
            colHeader: "mainId",
            name: 'mainId',
            hidden: true
        }, {
            colHeader: "srcMainId",
            name: 'srcMainId',
            hidden: true
        }, {
            colHeader: "ownBrId",
            name: 'ownBrId',
            hidden: true
        }, {
            colHeader: "authLvl",
            name: 'authLvl',
            hidden: true
        }, {
            colHeader: "areaChk",
            name: 'areaChk',
            hidden: true
        }, {
            colHeader: "hqMeetFlag",
            name: 'hqMeetFlag',
            hidden: true
        }, {
            colHeader: "uid",
            name: 'uid',
            hidden: true
        }, {
            colHeader: "caseBrId",
            name: 'caseBrId',
            hidden: true
        }],
        ondblClickRow: function(rowid){
            var data = L120M01aGrid.getRowData(rowid);
            Action.openDoc(null, null, data);
        }
    });
}

function mainGrid1(){
    var L120M01aGrid = $("#gridview").iGrid({
        handler: Action.ghandle,
        width: 785,
        height: 350,
        sortname: 'caseDate|caseNo',
        sortorder: 'desc|asc',
        shrinkToFit: true,
        autowidth: false,
        postData: {
            formAction: "queryL120m01a",
            mainDocStatus: viewstatus,
            rowNum: 15
        },
        rowNum: 15,
        multiselect: true,
        hideMultiselect: false,
        colModel: [{
            colHeader: i18n.cls1141v01["l120m01a.casedate"], // 簽案日期
            align: "center",
            width: 70,
            sortable: true,
            name: 'caseDate'
        }, {
            colHeader: i18n.cls1141v01["l120m01a.custid"], // 統一編號
            align: "left",
            width: 80,
            sortable: true,
            formatter: 'click',
            onclick: Action.openDoc,
            name: 'custId'
        }, {
            colHeader: i18n.cls1141v01["l120m01a.custname"], // 客戶名稱
            align: "left",
            width: 120,
            sortable: true,
            
            
            name: 'custName'
        }, {
            colHeader: i18n.cls1141v01["l120m01a.caseno"], // 案件號碼
            align: "left",
            width: 150,
            sortable: true,
            
            
            name: 'caseNo'
        }, {
            colHeader: i18n.cls1141v01["l120m01a.docname"],// "文件名稱"(授權別+'案件簽報書'+案件別),
            name: 'docKind',
            align: "left",
            width: 140,
            sortable: true
        }, {
            colHeader: i18n.cls1141v01["l120m01a.docstatus"], // 目前文件狀態
            align: "left",
            width: 50,
            sortable: true,
            
            
            hidden: true,
            name: 'docStatus'
        }, {
            colHeader: i18n.cls1141v01["l120m01a.creatorname"], // 建立人員名稱
            align: "left",
            width: 90,
            sortable: true,
            
            
            name: (userInfo.unitNo == "920" || userInfo.unitNo == "922" ||
            userInfo.unitNo == "931" ||
            userInfo.unitNo == "932" ||
            userInfo.unitNo == "933" ||
            userInfo.unitNo == "934" ||
            userInfo.unitNo == "935") ? 'areaAppraiser' : (userInfo.unitNo == "918") ? 'hqAppraiser' : 'updater' // col.id
        }, {
            colHeader: "areaDocstatus",
            name: 'areaDocstatus',
            hidden: true
        }, {
            colHeader: "areaBrId",
            name: 'areaBrId',
            hidden: true
        }, {
            colHeader: "rptTitle1",
            name: 'rptTitle1',
            hidden: true
        }, {
            colHeader: "rptTitle2",
            name: 'rptTitle2',
            hidden: true
        }, {
            colHeader: "docType",
            name: 'docType',
            hidden: true
        }, {
            colHeader: "docRslt",
            name: 'docRslt',
            hidden: true
        }, {
            colHeader: "docCode",
            name: 'docCode',
            hidden: true
        }, {
            colHeader: "docURL",
            name: 'docURL',
            hidden: true
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }, {
            colHeader: "mainid",
            name: 'mainId',
            hidden: true
        }, {
            colHeader: "ownBrId",
            name: 'ownBrId',
            hidden: true
        }, {
            colHeader: "authLvl",
            name: 'authLvl',
            hidden: true
        }, {
            colHeader: "areaChk",
            name: 'areaChk',
            hidden: true
        }, {
            colHeader: "hqMeetFlag",
            name: 'hqMeetFlag',
            hidden: true
        }, {
            colHeader: "uid",
            name: 'uid',
            hidden: true
        }, {
            colHeader: "caseBrId",
            name: 'caseBrId',
            hidden: true
        }],
        ondblClickRow: function(rowid){
            var data = L120M01aGrid.getRowData(rowid);
            Action.openDoc(null, null, data);
        }
    });
}

/**
 * 異常通報案件Grid
 */
function mainGrid0(){
    var formMethod;
    formMethod = "queryL120m01a1";
    var L120M01aGrid = $("#gridview").iGrid({
        handler: Action.ghandle,
        width: 785,
        height: 350,
        sortname: 'caseDate|caseNo',
        sortorder: 'desc|asc',
        shrinkToFit: true,
        autowidth: false,
        
        
        //2012-09-06 黃建霖 begin
        postData: {
            formAction: formMethod,
            mainDocStatus: viewstatus,
            rowNum: 15
        },
        //2012-09-06 黃建霖 end
        
        
        
        rowNum: 15,
        // multiselect: true,hideMultiselect:false,
        colModel: [{
            colHeader: i18n.cls1141v01["l120m01a.approvetime"], // 核准日期
            align: "center",
            width: 65, // 設定寬度
            sortable: true, // 是否允許排序			
            name: 'endDate',
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d',
                newformat: 'Y-m-d'
            },
            hidden: viewstatus == '05O' ? false : true
        }, {
            colHeader: i18n.cls1141v01["l120m01a.casedate"], // 簽案日期
            align: "center",
            width: 70, // 設定寬度
            sortable: true, // 是否允許排序
            //			formatter : 'click',
            //			onclick : openDoc,
            name: 'caseDate' // col.id
        }, {
            colHeader: i18n.cls1141v01["l120m01a.custid"], // 統一編號
            align: "left",
            width: 80, // 設定寬度
            sortable: true, // 是否允許排序
            formatter: 'click',
            onclick: Action.openDoc,
            name: 'custId' // col.id
        }, {
            colHeader: i18n.cls1141v01["l120m01a.custname"], // 客戶名稱
            align: "left",
            width: 120, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'custName' // col.id
        }, {
            colHeader: i18n.cls1141v01["l120m01a.caseno"], // 案件號碼
            align: "left",
            width: 150, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'caseNo' // col.id
        }, {
            colHeader: i18n.cls1141v01["l120m01a.docname"],// "文件名稱"(授權別+'案件簽報書'+案件別),
            name: 'docKind',
            align: "left",
            width: 110,
            sortable: true
        }, {
            colHeader: i18n.cls1141v01["l120m01a.docstatus"], // 目前文件狀態
            align: "left",
            width: 50, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: viewstatus == "05O" ? "reEstFlag" : "docStatus"// col.id
        }, {
            colHeader: i18n.cls1141v01["l120m01a.creatorname"], // 建立人員名稱
            align: "left",
            width: 90, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: (userInfo.unitNo == "920" || userInfo.unitNo == "922" ||
            userInfo.unitNo == "931" ||
            userInfo.unitNo == "932" ||
            userInfo.unitNo == "933" ||
            userInfo.unitNo == "934" ||
            userInfo.unitNo == "935") ? 'areaAppraiser' : (userInfo.unitNo == "918") ? 'hqAppraiser' : 'updater' // col.id
        }, {
            colHeader: "areaDocstatus",
            name: 'areaDocstatus',
            hidden: true
        }, {
            colHeader: "rptTitle1",
            name: 'rptTitle1',
            hidden: true
        }, {
            colHeader: "rptTitle2",
            name: 'rptTitle2',
            hidden: true
        }, {
            colHeader: "docType",
            name: 'docType',
            hidden: true
        }, {
            colHeader: "docRslt",
            name: 'docRslt',
            hidden: true
        }, {
            colHeader: "docCode",
            name: 'docCode',
            hidden: true
        }, {
            colHeader: "docURL",
            name: 'docURL',
            hidden: true
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }, {
            colHeader: "mainId",
            name: 'mainId',
            hidden: true
        }, {
            colHeader: "srcMainId",
            name: 'srcMainId',
            hidden: true
        }, {
            colHeader: "ownBrId",
            name: 'ownBrId',
            hidden: true
        }, {
            colHeader: "authLvl",
            name: 'authLvl',
            hidden: true
        }, {
            colHeader: "areaChk",
            name: 'areaChk',
            hidden: true
        }, {
            colHeader: "hqMeetFlag",
            name: 'hqMeetFlag',
            hidden: true
        }, {
            colHeader: "uid",
            name: 'uid',
            hidden: true
        }, {
            colHeader: "caseBrId",
            name: 'caseBrId',
            hidden: true
        }],
        ondblClickRow: function(rowid){ // 當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = L120M01aGrid.getRowData(rowid);
            Action.openDoc(null, null, data);
        }
    });
}

function mainGrid(){
    var formMethod;
    formMethod = "queryL120m01a";
    var L120M01aGrid = $("#gridview").iGrid({
        handler: Action.ghandle,
        width: 785,
        height: 350,
        sortname: 'caseDate|caseNo',
        sortorder: 'desc|asc',
        shrinkToFit: true,
        autowidth: false,
        //2012-09-06 黃建霖 begin
        postData: {
            formAction: formMethod,
            mainDocStatus: viewstatus,
            rowNum: 15
        },
        //2012-09-06 黃建霖 end
        
        
        
        rowNum: 15,
        multiselect: viewstatus == "02O" ? true : false,
		// hideMultiselect:false,
        colModel: [{
            colHeader: i18n.cls1141v01["l120m01a.approvetime"], // 核准日期
            align: "center",
            width: 65,
            sortable: true,
            name: 'endDate',
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d',
                newformat: 'Y-m-d'
            },
            hidden: viewstatus == '05O' ? false : true
        }, {
            colHeader: i18n.cls1141v01["l120m01a.casedate"], // 簽案日期
            align: "center",
            width: 70,
            sortable: true,
            name: 'caseDate'
        }, {
            colHeader: i18n.cls1141v01["l120m01a.custid"], // 統一編號
            align: "left",
            width: 80,
            sortable: true,
            formatter: 'click',
            onclick: Action.openDoc,
            name: 'custId'
        }, {
            colHeader: i18n.cls1141v01["l120m01a.custname"], // 客戶名稱
            align: "left",
            width: 120,
            sortable: true,
            
            
            name: 'custName'
        }, {
            colHeader: i18n.cls1141v01["l120m01a.caseno"], // 案件號碼
            align: "left",
            width: 150,
            sortable: true,
            
            
            name: 'caseNo'
        }, {
            colHeader: i18n.cls1141v01["l120m01a.docname"],// "文件名稱"(授權別+'案件簽報書'+案件別),
            name: 'docKind',
            align: "left",
            width: 110,
            sortable: true
        }, {
            colHeader: i18n.cls1141v01["l120m01a.docstatus"], // 目前文件狀態
            align: "left",
            width: 50,
            sortable: true,
            
            
            name: viewstatus == "05O" ? "reEstFlag" : "docStatus"
        }, {
            colHeader: i18n.cls1141v01["l120m01a.creatorname"], // 建立人員名稱
            align: "left",
            width: 90,
            sortable: true,
            
            
            name: (userInfo.unitNo == "920" || userInfo.unitNo == "922" ||
            userInfo.unitNo == "931" ||
            userInfo.unitNo == "932" ||
            userInfo.unitNo == "933" ||
            userInfo.unitNo == "934" ||
            userInfo.unitNo == "935") ? 'areaAppraiser' : (userInfo.unitNo == "918") ? 'hqAppraiser' : 'updater' // col.id
        }, {
            colHeader: "areaDocstatus",
            name: 'areaDocstatus',
            hidden: true
        }, {
            colHeader: "rptTitle1",
            name: 'rptTitle1',
            hidden: true
        }, {
            colHeader: "rptTitle2",
            name: 'rptTitle2',
            hidden: true
        }, {
            colHeader: "docType",
            name: 'docType',
            hidden: true
        }, {
            colHeader: "docRslt",
            name: 'docRslt',
            hidden: true
        }, {
            colHeader: "docCode",
            name: 'docCode',
            hidden: true
        }, {
            colHeader: "docURL",
            name: 'docURL',
            hidden: true
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }, {
            colHeader: "mainId",
            name: 'mainId',
            hidden: true
        }, {
            colHeader: "srcMainId",
            name: 'srcMainId',
            hidden: true
        }, {
            colHeader: "ownBrId",
            name: 'ownBrId',
            hidden: true
        }, {
            colHeader: "authLvl",
            name: 'authLvl',
            hidden: true
        }, {
            colHeader: "areaChk",
            name: 'areaChk',
            hidden: true
        }, {
            colHeader: "hqMeetFlag",
            name: 'hqMeetFlag',
            hidden: true
        }, {
            colHeader: "uid",
            name: 'uid',
            hidden: true
        }, {
            colHeader: "caseBrId",
            name: 'caseBrId',
            hidden: true
        }],
        ondblClickRow: function(rowid){
            var data = L120M01aGrid.getRowData(rowid);
            Action.openDoc(null, null, data);
        }
    });
}

// 特殊分行(國外部、國金部、金控總部分行、財務部、財富管理處)會簽Grid
function spectialGrid(){
    var formMethod;
    formMethod = "querySpectial";
    var L120M01aGrid = $("#gridview").iGrid({
        handler: Action.ghandle,
        width: 785,
        height: 350,
        sortname: 'caseDate|caseNo',
        sortorder: 'desc|asc',
        shrinkToFit: true,
        autowidth: false,
        
        
        //2012-09-06 黃建霖 begin
        postData: {
            formAction: formMethod,
            mainDocStatus: viewstatus,
            rowNum: 15
        },
        //2012-09-06 黃建霖 end
        
        
        
        rowNum: 15,
        // multiselect: true,hideMultiselect:false,
        colModel: [{
            colHeader: i18n.cls1141v01["l120m01a.approvetime"], // 核准日期
            align: "center",
            width: 65, // 設定寬度
            sortable: true, // 是否允許排序			
            name: 'endDate',
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d',
                newformat: 'Y-m-d'
            },
            hidden: viewstatus == '05O' ? false : true
        }, {
            colHeader: i18n.cls1141v01["l120m01a.casedate"], // 簽案日期
            align: "center",
            width: 70, // 設定寬度
            sortable: true, // 是否允許排序
            //			formatter : 'click',
            //			onclick : openDoc,
            name: 'caseDate' // col.id
        }, {
            colHeader: i18n.cls1141v01["l120m01a.custid"], // 統一編號
            align: "left",
            width: 80, // 設定寬度
            sortable: true, // 是否允許排序
            formatter: 'click',
            onclick: Action.openDoc,
            name: 'custId' // col.id
        }, {
            colHeader: i18n.cls1141v01["l120m01a.custname"], // 客戶名稱
            align: "left",
            width: 120, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'custName' // col.id
        }, {
            colHeader: i18n.cls1141v01["l120m01a.caseno"], // 案件號碼
            align: "left",
            width: 150, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'caseNo' // col.id
        }, {
            colHeader: i18n.cls1141v01["l120m01a.docname"],// "文件名稱"(授權別+'案件簽報書'+案件別),
            name: 'docKind',
            align: "left",
            width: 110,
            sortable: true
        }, {
            colHeader: i18n.cls1141v01["l120m01a.docstatus"], // 目前文件狀態
            align: "left",
            width: 50, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: viewstatus == "05O" ? "reEstFlag" : "docStatus"// col.id
        }, {
            colHeader: i18n.cls1141v01["l120m01a.creatorname"], // 建立人員名稱
            align: "left",
            width: 90, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: (userInfo.unitNo == "920" || userInfo.unitNo == "922" ||
            userInfo.unitNo == "931" ||
            userInfo.unitNo == "932" ||
            userInfo.unitNo == "933" ||
            userInfo.unitNo == "934" ||
            userInfo.unitNo == "935") ? 'areaAppraiser' : (userInfo.unitNo == "918") ? 'hqAppraiser' : 'updater' // col.id
        }, {
            colHeader: "areaDocstatus",
            name: 'areaDocstatus',
            hidden: true
        }, {
            colHeader: "rptTitle1",
            name: 'rptTitle1',
            hidden: true
        }, {
            colHeader: "rptTitle2",
            name: 'rptTitle2',
            hidden: true
        }, {
            colHeader: "docType",
            name: 'docType',
            hidden: true
        }, {
            colHeader: "docRslt",
            name: 'docRslt',
            hidden: true
        }, {
            colHeader: "docCode",
            name: 'docCode',
            hidden: true
        }, {
            colHeader: "docURL",
            name: 'docURL',
            hidden: true
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }, {
            colHeader: "mainId",
            name: 'mainId',
            hidden: true
        }, {
            colHeader: "srcMainId",
            name: 'srcMainId',
            hidden: true
        }, {
            colHeader: "ownBrId",
            name: 'ownBrId',
            hidden: true
        }, {
            colHeader: "authLvl",
            name: 'authLvl',
            hidden: true
        }, {
            colHeader: "areaChk",
            name: 'areaChk',
            hidden: true
        }, {
            colHeader: "hqMeetFlag",
            name: 'hqMeetFlag',
            hidden: true
        }, {
            colHeader: "uid",
            name: 'uid',
            hidden: true
        }, {
            colHeader: "caseBrId",
            name: 'caseBrId',
            hidden: true
        }],
        ondblClickRow: function(rowid){ // 當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = L120M01aGrid.getRowData(rowid);
            Action.openDoc(null, null, data);
        }
    });
}

function mainGridInit(){

    var formMethod;
    formMethod = "queryL120m01a";
    var L120M01aGrid = $("#gridview").iGrid({
        handler: Action.ghandle,
        width: 780,
        height: 350,
        sortname: 'caseDate|caseNo',
        sortorder: 'desc|asc',
        shrinkToFit: true,
        autowidth: false,
        localFirst: true,
        multiselect: true,
        //2012-09-06 黃建霖 begin
        
        postData: {
            formAction: formMethod,
            mainDocStatus: viewstatus,
            rowNum: 15
        },
        
        //2012-09-06 黃建霖 end
        rowNum: 15,
        colModel: [{
            colHeader: i18n.cls1141v01["l120m01a.approvetime"], // 核准日期
            align: "center",
            width: 65,
            sortable: true,
            name: 'endDate',
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d',
                newformat: 'Y-m-d'
            },
            hidden: viewstatus == '05O' ? false : true
        }, {
            colHeader: i18n.cls1141v01["l120m01a.casedate"], // 簽案日期
            align: "center",
            width: 70,
            sortable: true,
            name: 'caseDate'
        }, {
            colHeader: i18n.cls1141v01["l120m01a.custid"], // 統一編號
            align: "left",
            width: 80,
            sortable: true,
            formatter: 'click',
            onclick: Action.openDoc,
            name: 'custId'
        }, {
            colHeader: i18n.cls1141v01["l120m01a.custname"], // 客戶名稱
            align: "left",
            width: 120,
            sortable: true,
            
            
            name: 'custName'
        }, {
            colHeader: i18n.cls1141v01["l120m01a.caseno"], // 案件號碼
            align: "left",
            width: 150,
            sortable: true,
            
            
            name: 'caseNo'
        }, {
            colHeader: i18n.cls1141v01["l120m01a.docname"],// "文件名稱"(授權別+'案件簽報書'+案件別),
            name: 'docKind',
            align: "left",
            width: 110,
            sortable: true
        }, {
            colHeader: i18n.cls1141v01["l120m01a.docstatus"], // 目前文件狀態
            align: "left",
            width: 50,
            sortable: true,
            
            
            name: viewstatus == "05O" ? "reEstFlag" : "docStatus"
        }, {
            colHeader: i18n.cls1141v01["l120m01a.creatorname"], // 建立人員名稱
            align: "left",
            width: 90,
            sortable: true,
            
            
            name: (userInfo.unitNo == "920" || userInfo.unitNo == "922" ||
            userInfo.unitNo == "931" ||
            userInfo.unitNo == "932" ||
            userInfo.unitNo == "933" ||
            userInfo.unitNo == "934" ||
            userInfo.unitNo == "935") ? 'areaAppraiser' : (userInfo.unitNo == "918") ? 'hqAppraiser' : 'updater' // col.id
        }, {
            colHeader: "areaDocstatus",
            name: 'areaDocstatus',
            hidden: true
        }, {
            colHeader: "rptTitle1",
            name: 'rptTitle1',
            hidden: true
        }, {
            colHeader: "rptTitle2",
            name: 'rptTitle2',
            hidden: true
        }, {
            colHeader: "docType",
            name: 'docType',
            hidden: true
        }, {
            colHeader: "docRslt",
            name: 'docRslt',
            hidden: true
        }, {
            colHeader: "docCode",
            name: 'docCode',
            hidden: true
        }, {
            colHeader: "docURL",
            name: 'docURL',
            hidden: true
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }, {
            colHeader: "mainId",
            name: 'mainId',
            hidden: true
        }, {
            colHeader: "srcMainId",
            name: 'srcMainId',
            hidden: true
        }, {
            colHeader: "ownBrId",
            name: 'ownBrId',
            hidden: true
        }, {
            colHeader: "authLvl",
            name: 'authLvl',
            hidden: true
        }, {
            colHeader: "areaChk",
            name: 'areaChk',
            hidden: true
        }, {
            colHeader: "hqMeetFlag",
            name: 'hqMeetFlag',
            hidden: true
        }, {
            colHeader: "uid",
            name: 'uid',
            hidden: true
        }, {
            colHeader: "caseBrId",
            name: 'caseBrId',
            hidden: true
        }],
        ondblClickRow: function(rowid){
            var data = L120M01aGrid.getRowData(rowid);
            Action.openDoc(null, null, data);
        }
    });
}


function mainGridSea(){
    var formMethod;
    formMethod = "queryL121m01a";
    var L120M01aGrid = $("#gridview").iGrid({
        handler: Action.ghandle,
        width: 785,
        height: 350,
        sortname: 'caseDate|caseNo',
        sortorder: 'desc|desc',
        shrinkToFit: true,
        autowidth: false,
        postData: {
            formAction: formMethod,
            mainDocStatus: viewstatus,
            rowNum: 15
        },
        rowNum: 15,
        colModel: [{
            colHeader: i18n.cls1141v01["l120m01a.casedate"], // 簽案日期
            align: "center",
            width: 70,
            sortable: true,
            name: 'caseDate'
        }, {
            colHeader: i18n.cls1141v01["l120m01a.custid"], // 統一編號
            align: "left",
            width: 80,
            sortable: true,
            formatter: 'click',
            onclick: Action.openDoc,
            name: 'custId'
        }, {
            colHeader: i18n.cls1141v01["l120m01a.custname"], // 客戶名稱
            align: "left",
            width: 120,
            sortable: true,
            
            
            name: 'custName'
        }, {
            colHeader: i18n.cls1141v01["l120m01a.caseno"], // 案件號碼
            align: "left",
            width: 150,
            sortable: true,
            
            
            name: 'caseNo'
        }, {
            colHeader: i18n.cls1141v01["l120m01a.docname"],// "文件名稱"(授權別+'案件簽報書'+案件別),
            name: 'docKind',
            align: "left",
            width: 140,
            sortable: true
        }, {
            colHeader: i18n.cls1141v01["l120m01a.docstatus"], // 目前文件狀態
            align: "left",
            width: 50,
            sortable: true,
            hidden: true,
            name: 'docStatus'
        }, {
            colHeader: i18n.cls1141v01["l120m01a.creatorname"], // 建立人員名稱
            align: "left",
            width: 90,
            sortable: true,
            name: (userInfo.unitNo == "920" || userInfo.unitNo == "922" ||
            userInfo.unitNo == "931" ||
            userInfo.unitNo == "932" ||
            userInfo.unitNo == "933" ||
            userInfo.unitNo == "934" ||
            userInfo.unitNo == "935") ? 'areaAppraiser' : (userInfo.unitNo == "918") ? 'hqAppraiser' : 'updater' // col.id
        }, {
            colHeader: "areaDocstatus",
            name: 'areaDocstatus',
            hidden: true
        }, {
            colHeader: "rptTitle1",
            name: 'rptTitle1',
            hidden: true
        }, {
            colHeader: "rptTitle2",
            name: 'rptTitle2',
            hidden: true
        }, {
            colHeader: "docType",
            name: 'docType',
            hidden: true
        }, {
            colHeader: "docRslt",
            name: 'docRslt',
            hidden: true
        }, {
            colHeader: "docCode",
            name: 'docCode',
            hidden: true
        }, {
            colHeader: "docURL",
            name: 'docURL',
            hidden: true
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }, {
            colHeader: "mainId",
            name: 'mainId',
            hidden: true
        }, {
            colHeader: "srcMainId",
            name: 'srcMainId',
            hidden: true
        }, {
            colHeader: "ownBrId",
            name: 'ownBrId',
            hidden: true
        }, {
            colHeader: "authLvl",
            name: 'authLvl',
            hidden: true
        }, {
            colHeader: "areaChk",
            name: 'areaChk',
            hidden: true
        }, {
            colHeader: "hqMeetFlag",
            name: 'hqMeetFlag',
            hidden: true
        }, {
            colHeader: "uid",
            name: 'uid',
            hidden: true
        }, {
            colHeader: "caseBrId",
            name: 'caseBrId',
            hidden: true
        }],
        ondblClickRow: function(rowid){
            var data = L120M01aGrid.getRowData(rowid);
            Action.openDoc(null, null, data);
        }
    });
}

function ajaxPcTitle(list2){
    var list = "";
    $.ajax({
        async: false,
        handler: Action.fhandle,
        action: "getRptTitles",
        data: {
            selOid: list2
        },
        success: function(responseData){
            list = responseData.resOids;
            // 如果只有一筆資料時
            if (list == "") {
                list = list2;
            }
        }
    });
    return list;
}

function grid(){
    $("#gridview").jqGrid("setGridParam", {
        postData: $.extend($("#filterForm").serializeData(), {
            handler: Action.ghandle,
            formAction: $("#gridview").getGridParam("postData").formAction,
            docStatus: viewstatus,
            mainDocStatus: viewstatus,
            rowNum: 15
        })
    }).trigger("reloadGrid");
}

function cfm_msg_checkIsStop(json_param){
	var my_dfd = $.Deferred();
	
	/* $.ajax({
        type: "POST",
        handler: Action.fhandle,
        data: $.extend({formAction: "checkIsStop", isNew: true}, json_param),
        success: function(responseData){
        	if (responseData.addMsg) {
        		CommonAPI.confirmMessage(i18n.lmscommom["other.msg26a"], function(b){
                    if (b) {
                    	my_dfd.resolve();
                    }
        		});
        	}else{
        		my_dfd.resolve();
        	}
        }
    }); */
	my_dfd.resolve();  
    
	return my_dfd.promise();
}

function prepare_gen_71_caseDoc_tabDoc(custId, dupNo, esggnLoanFg, esggtype, esggtypeZMemo){
	var my_dfd = $.Deferred();
	$.ajax({
        type: "POST",
        handler: "cls1141m01formhandler",
        data: {formAction: "prepare_gen_71_caseDoc_tabDoc", "custId":custId, "dupNo":dupNo
        	, "esggnLoanFg":esggnLoanFg
        	, "esggtype":esggtype, "esggtypeZMemo":esggtypeZMemo, "simplifyFlag":"B"},
        success: function(json){
        	my_dfd.resolve( json );
        }
    });
	return my_dfd.promise();	
}

function prepare_gen_71_brmp_caseDoc_tabDoc(custId, dupNo, termGroup, creditLoanPurpose, onlineCaseNo, custPos_custId, custPos_dupNo, custPos_rKindM, custPos_rKindD, lnYear, lnMonth
		, esggnLoanFg
		, esggtype, esggtypeZMemo, houseLoanFlag, usePlan){
	var my_dfd = $.Deferred(); //為了檢核 決策平台 連 EJCIC狀況，在 server 端就先 run API：headAccountValidation	
	$.ajax({
        type: "POST",
        handler: "cls1141m01formhandler",
        data: {formAction: "prepare_gen_71_brmp_caseDoc_tabDoc", "custId":custId, "dupNo":dupNo
        	, 'termGroup': termGroup, 'creditLoanPurpose':creditLoanPurpose, 'onlineCaseNo': onlineCaseNo
        	, "custPos_custId": custPos_custId, "custPos_dupNo":custPos_dupNo
        	, "custPos_rKindM": custPos_rKindM, "custPos_rKindD":custPos_rKindD
        	, "lnYear":lnYear, "lnMonth":lnMonth
        	, "esggnLoanFg":esggnLoanFg
        	, "esggtype":esggtype, "esggtypeZMemo":esggtypeZMemo,"houseLoanFlag":houseLoanFlag,"usePlan":usePlan, "simplifyFlag":"E"
        	},
        success: function(json){
        	my_dfd.resolve( json );
        }
    });
	return my_dfd.promise();	
}

function dfd_addL120m01a(json_param){
	var my_dfd = $.Deferred();
	//==============================================================
	// 因為 form 裡沒有 ID, 所以用  find("[name=?]").val(?); 去塞資料
	// $("#LMS1205V01Form").injectData(json_param);
	$.each(json_param, function(k, v) { 
		$("#LMS1205V01Form").find("[name="+k+"][value="+v+"]").trigger('click').prop("checked", true);; 
    });
	//==============================================================
	$.ajax({
        handler: "cls1141m01formhandler", action: "addL120m01a",
        formId: "LMS1205V01Form",
        success: function(json_resp_addL120m01a){
        	  
           	 var caseDoc_param = {};
           	 caseDoc_param["mainId"] = json_resp_addL120m01a.mainId;
           	 caseDoc_param["oid"] = json_resp_addL120m01a.mainOid;
           	 caseDoc_param["mainOid"]= caseDoc_param["oid"];
           	 caseDoc_param["docStatus"] = json_resp_addL120m01a.docStatus;
           	 caseDoc_param["mainDocStatus"] = caseDoc_param["docStatus"];
       	 
        	 ilog.debug("json_resp_addL120m01a"
        			 +"{mainDocStatus="+caseDoc_param.docStatus
        			 +",oid="+caseDoc_param.oid
        			 +",mainId="+caseDoc_param.mainId
        			 +" }");   
        	 my_dfd.resolve(caseDoc_param);
        }
    });
	return my_dfd.promise();	
}

function dfd_71_importLendCollateral(caseDoc_param, c101_mainId){
	return  $.ajax({handler: "cls1141formhandler", action: "importLendCollateral", formId: 'empty',
        	data:$.extend(caseDoc_param, { 'rows': c101_mainId}
        ),success: function(json_resp_importLendCollateral){
       	 	ilog.debug("{done}json_resp_importLendCollateral");
        }
    });
}

function dfd_71_importLendCollateral_custPos(caseDoc_param, custPos_c101_mainId){
	var my_dfd = $.Deferred()
	if(custPos_c101_mainId && custPos_c101_mainId != ""){
		$.ajax({handler: "cls1141formhandler", action: "importLendCollateral", formId: 'empty',
        	data:$.extend(caseDoc_param, { 'rows': custPos_c101_mainId}
        ),success: function(json_resp_importLendCollateral){
       	 	ilog.debug("{done}json_resp_importLendCollateral_custPos{custPos_c101_mainId='"+custPos_c101_mainId+"'}");
       	 	my_dfd.resolve();
        }
		});
	}else{
		ilog.debug("importLendCollateral_custPos{custPos_c101_mainId=''}");
		my_dfd.resolve();
	}
	return my_dfd.promise();
}

function dfd_71_setMainCust(caseDoc_param){
	var my_dfd = $.Deferred();  
	$.ajax({handler: "cls1141formhandler", action: "setMainCust", formId: 'empty', data:$.extend(caseDoc_param, {'custOid': ''} // c120m01a.oid 
    	),
    	success: function(json_resp_setMainCust){
    		ilog.debug("{done}json_resp_setMainCust");
    		if(json_resp_setMainCust.chose_c120m01a){
    			my_dfd.resolve({'c120m01a_oid':(json_resp_setMainCust.chose_c120m01a.c120m01a_oid||'') });	
    		}else{
    			my_dfd.resolve({'c120m01a_oid':''});
    		}
    		
    	}
    });	
	return my_dfd.promise();	
}

function dfd_71_caseDoc_saveAll(caseDoc_param){  	 
  	 return $.ajax({handler: "cls1141m01formhandler", action: "saveAll", formId: 'empty', data:$.extend(caseDoc_param, { 'page': '2'}
           ),success: function(json_resp_saveAll){
          	 ilog.debug("{done}json_resp_saveAll");          
           }
     });
}

function dfd_71_gen_tabDoc(caseMainId, debtor_createCntrNo_fast_prodKind71, param_formId){
	var my_dfd = $.Deferred();  
	$.ajax({
        handler: "cls1151m01formhandler",
		formId: 'empty',
        action: "createCntrNo_fast_prodKind_byParam",
        data: $.extend( {'prodKind':'71', 'caseMainId': caseMainId, 'debtor_createCntrNo_fast_prodKind71':debtor_createCntrNo_fast_prodKind71 }, $("#"+param_formId).serializeData()),
        success: function(obj){
        	ilog.debug("dfd_71_gen_tabDoc {tabMainId="+(obj.l140m01a_MainId||'')+" }{clsTerm:"+(obj.clsTerm?obj.clsTerm.infoStr:'')+"}"); 
        	my_dfd.resolve({'tabMainId':obj.l140m01a_MainId});     	
        	/*
        	 ref CLS1141S03Panel.js
					CLS1141S03Action.createCntrNo_fast_prodKind_byParam('71', 'createCntrNo_fast_prodKind71Form').done(function(json_cntrNo_fast){
                    	var cntrNo = CLS1141S03Action.getContractNo(json_cntrNo_fast.l140m01a_MainId);
    					var nplInfo = CLS1141S03Action.getBranchNplRatiosInfo(cntrNo);
    					CLS1141S03Action.updateCreditCardMembersLoanData(json_cntrNo_fast.l140m01a_MainId, cntrNo, nplInfo.npl, nplInfo.nplDate);
    					CLS1141S03Action.reloadGrid();
                      	$.thickbox.close();	
                    });
             	之後的 CLS1141S03Action.getContractNo(...) , CLS1141S03Action.getBranchNplRatiosInfo(...) , updateCreditCardMembersLoanData(...) 
             	在 dfd_71_upd_caseDoc___tabDoc_cntrNo_npl(...) 去做
        	*/
        }
    });
	return my_dfd.promise();
}

function brmp_creditCheck_new(caseMainId, debtor_createCntrNo_brmp_creditCheck, param_formId){
	var my_dfd = $.Deferred();  
	var frmJSON = $("#"+param_formId).serializeData();
	$.ajax({
        handler: "cls1141m01formhandler",
		formId: 'empty',
        action: "brmp_creditCheck_new",
        data: {'caseMainId': caseMainId
        	, 'custId': frmJSON["createCntrNo_brmp_creditCheck_custId"]
        	, 'dupNo': frmJSON["createCntrNo_brmp_creditCheck_dupNo"]
        	, 'custPos_custId': frmJSON["createCntrNo_brmp_creditCheck_custPos_custId"]
        	, 'custPos_dupNo': frmJSON["createCntrNo_brmp_creditCheck_custPos_dupNo"]
          	, 'custPos_rKindM': frmJSON["createCntrNo_brmp_creditCheck_custPos_rKindM"]
          	, 'custPos_rKindD': frmJSON["createCntrNo_brmp_creditCheck_custPos_rKindD"]
          	, 'custPos_isLiveWithBorrower': frmJSON["createCntrNo_brmp_creditCheck_custPos_isLiveWithBorrower"]
			, 'applyAmt': frmJSON["createCntrNo_brmp_creditCheck_amt"]
    		, 'lnYear': frmJSON["createCntrNo_brmp_creditCheck_lnYear"]
    		, 'lnMonth': frmJSON["createCntrNo_brmp_creditCheck_lnMonth"]
			, 'pConBegEnd_fg': frmJSON["createCntrNo_brmp_creditCheck_pConBegEnd_fg"]
			, 'onlineCaseNo': frmJSON["createCntrNo_brmp_creditCheck_onlineCaseNo"]		
			, 'introduceSrc': frmJSON["createCntrNo_brmp_creditCheck_introduceSrc"]
			, 'megaEmpNo': frmJSON["megaEmpNo"]
			, 'megaCode': frmJSON["megaCode"]
			, 'subUnitNo': frmJSON["subUnitNo"] || ''
			, 'subEmpNo': frmJSON["subEmpNo"]
			, 'subEmpNm': frmJSON["subEmpNm"]
			, 'introCustId': frmJSON["introCustId"]
			, 'introDupNo': frmJSON["introDupNo"]
			, 'introCustName': frmJSON["introCustName"]
			, 'creditLoanPurpose': frmJSON["createCntrNo_brmp_creditCheck_creditLoanPurpose"]
			, 'induceFlagOrNot': frmJSON["createCntrNo_brmp_creditCheck_induceFlagOrNot"]
			, 'termGroup': frmJSON["createCntrNo_brmp_creditCheck_termGroup"]
			, 'termGroupSub': frmJSON["createCntrNo_brmp_creditCheck_termGroupSub"]
			, 'coBrandedCardUserType01': frmJSON["createCntrNo_brmp_creditCheck_coBrandedCardUserType01"]
//            , 'lnap': frmJSON["createCntrNo_brmp_creditCheck_lnap"]
            , 'stageCount': frmJSON["createCntrNo_brmp_creditCheck_stageCount"]
//          , 'stageCount': $frm.find("#createCntrNo_brmp_creditCheck_stageCount").val() 此寫法因 radio 的 id, name 都相同，會抓錯
    		, 'otherAdjustAmt': frmJSON["createCntrNo_brmp_creditCheck_otherAdjustAmt"]
    		, 'compensationAmt': frmJSON["createCntrNo_brmp_creditCheck_compensationAmt"]
			, 'induceFlagXV': frmJSON["createCntrNo_brmp_creditCheck_induceFlagXV"]            
			, 'esggnLoanFg': frmJSON["createCntrNo_brmp_creditCheck_esggnLoanFg"]
			, 'esggtype': frmJSON["createCntrNo_brmp_creditCheck_esggtype"]
			, 'esggtypeZMemo': frmJSON["createCntrNo_brmp_creditCheck_esggtypeZMemo"]
			, 'houseLoanFlag': frmJSON["createCntrNo_brmp_creditCheck_houseLoanFlag"]
			, 'megaEmpBrNo': frmJSON["megaEmpBrNo"]
			, 'introducerName': frmJSON["introducerName"]
			, 'usePlan': frmJSON["createCntrNo_brmp_creditCheck_usePlan"]
        },
        success: function(obj){
        	ilog.debug("brmp_creditCheck_new {tabMainId="+(obj.l140m01a_MainId||'')+",rejectDesc="+(obj.rejectDesc||'')+",statusMissing="+(obj.statusMissing||'')+"}");
        	if(obj.l140m01a_MainId && obj.l140m01a_MainId != ""){
        		my_dfd.resolve({'tabMainId':(obj.l140m01a_MainId||'')
        			, 'rejectDesc': (obj.rejectDesc||'')
        			, 'statusMissing': (obj.statusMissing||'')
        			});     	
            	/*  之後的 CLS1141S03Action.getContractNo(...) , CLS1141S03Action.getBranchNplRatiosInfo(...) , updateCreditCardMembersLoanData(...) 
                 	在 dfd_71_upd_caseDoc___tabDoc_cntrNo_npl(...) 去做
            	*/	
        	}else{
        		ilog.debug("brmp_creditCheck_new {no} l140m01a_MainId");
        		//若無報價
        		my_dfd.reject();
        		//=======================
        		$.thickbox.close();
        		$("#gridview").trigger("reloadGrid");
        	}
        },
        error: function(obj){
        	ilog.debug("brmp_creditCheck_new {fail}");
        	//若 BRMP 執行失敗，讓  user 也能看到已產出的簽報書，免得一直 click
    		my_dfd.reject();
    		//=======================
    		$.thickbox.close();
    		$("#gridview").trigger("reloadGrid");
        }
    });
	
	
	return my_dfd.promise();
}

function brmp_isShowLiveWithBorrowerItem(relationType, relationship, formObject){

	if(relationType == '2' && relationship && relationship != 'X0'){
		formObject.find(".brmp_creditCheck_custPos_isLiveWithBorrowerSpan").each(function(){
			$(this).show();
		})
	}
	else{
		formObject.find(".brmp_creditCheck_custPos_isLiveWithBorrowerSpan").each(function(){
			$(this).hide();
		})
		formObject.find("input[name=createCntrNo_brmp_creditCheck_custPos_isLiveWithBorrower]").prop('checked', false);
	}
}

function dfd_71_upd_caseDoc___tabDoc_cntrNo_npl(caseMainId, tabMainId){
	return $.ajax({handler: "cls1141m01formhandler", action: "upd_caseDoc___tabDoc_cntrNo_npl", formId: 'empty', 
		 data:{'caseMainId': caseMainId, 'simplifyFlag' : 'D'
			 , 'tabMainId': tabMainId, 'tab_printSeq' : 1
		 },success: function(json){
			 ilog.debug("{done}json_resp_upd_caseDoc___tabDoc_cntrNo_npl , {cntrNo="+(json.cntrNo||'')+"}");
		 }
	});
}

function brmp_upd_caseDoc___tabDoc_cntrNo_npl(caseMainId, tabMainId){
	//之後在 L120M01A 會增加 simplifyFlag
	return $.ajax({handler: "cls1141m01formhandler", action: "upd_caseDoc___tabDoc_cntrNo_npl", formId: 'empty', 
		 data:{'caseMainId': caseMainId, 'simplifyFlag' : ''
			 , 'tabMainId': tabMainId, 'tab_printSeq' : 1
		 },success: function(json){
			 ilog.debug("{done}json_resp_upd_caseDoc___tabDoc_cntrNo_npl , {cntrNo="+(json.cntrNo||'')+"}");
		 }
	});
}

function dfd_import_simplifyFlag_D___L120S15A_content(caseMainId){
	return $.ajax({handler: "cls1141m01formhandler", action: "import_simplifyFlag_D___L120S15A_content", formId: 'empty', 
		 data:{'mainId': caseMainId
		 },success: function(json){
			 ilog.debug("{done}import_simplifyFlag_D___L120S15A_content");
		 }
	});
}

function dfd_import_property7_cntrNo(caseMainId, custOid, importBisFlag ){
	var my_dfd = $.Deferred();  
	$.ajax({handler: "cls1151m01formhandler", action: "addL140m01a", formId: 'empty', 
		 data:{'mainId': caseMainId, 'custOid': custOid
		 },success: function(json_addL140m01a){
			 ilog.debug("{done}dfd_import_property7_cntrNo___{addL140m01a}");
			 //J-111-0096 Web e-Loan消金因應不動產暴險以貸放比率(LTV)決定適用之風險權數，消金個人戶授信案件免填「風險權數」(取消現行系統要求風險權數影響數評估)
			 if(importBisFlag =="Y"){
				 $.ajax({handler: "cls1141m01formhandler", action: "getBis", formId: 'empty', 
					 data:{'mainId': caseMainId
						 , 'refresh' : false
						 , 'trigger_in_dfd_chain' : true
					 },success: function(json_getBis){
						 //因在 server 端寫 result.set(CapConstants.AJAX_NOTIFY_MESSAGE, ...)
						 $.thickbox.close();
						 ilog.debug("{done}dfd_import_property7_cntrNo___{getBis}");
						 //~~~
						 my_dfd.resolve();
					 }
			 	});
			}else{
				my_dfd.resolve();				 
			}
		 }
	});
	return my_dfd.promise();
}

function dfd_fast_checkSend_CLS_L120M01A_countValue(caseMainId){
	var my_dfd = $.Deferred();  
	$.ajax({handler: "cls1141m01formhandler", action: "fastCntrNo_checkSend_CLS_L120M01A_countValue", formId: 'empty', 
		 data:{'mainId': caseMainId
		 },success: function(json){
			 ilog.debug("{done_dfd_fast_checkSend_CLS_L120M01A_countValue}");
			 if(json.msg){
				 ilog.debug("msg="+json.msg);
			 }
			 my_dfd.resolve();
		 }
	});
	return my_dfd.promise();
}

function dfd_fastCntrNo_CLS_L120M01A_sendAML(caseMainId){
	var my_dfd = $.Deferred();  
	$.ajax({handler: "cls1141m01formhandler", action: "fastCntrNo_CLS_L120M01A_sendAML", formId: 'empty', 
		 data:{'mainId': caseMainId
		 },success: function(json){
			 ilog.debug("{done_dfd_fastCntrNo_CLS_L120M01A_sendAML}=>need_getAML="+(json.need_getAML||""));
			 if(json.msg){
				 ilog.debug("msg="+json.msg);
			 }
			 my_dfd.resolve(json);
		 }
	});
	return my_dfd.promise();
}

function dfd_fastCntrNo_CLS_L120M01A_getAML(caseMainId, json_sendAML){
	var my_dfd = $.Deferred();  
	if(json_sendAML.need_getAML=="Y"){
		$.ajax({handler: "cls1141m01formhandler", action: "fastCntrNo_CLS_L120M01A_getAML", formId: 'empty', 
			 data:{'mainId': caseMainId
			 },success: function(json){
				 ilog.debug("{done_dfd_fastCntrNo_CLS_L120M01A_getAML}");
				 if(json.msg){
					 ilog.debug("msg="+json.msg);
				 }
				 my_dfd.resolve();
			 }
		});	
	}else{
		my_dfd.resolve();
	}	
	return my_dfd.promise();
}

function dfd_unlockDoc(lockMainId){
	//需有 AuthType.Accept  return $.ajax({handler: 'unlockdocformhandler', action: "unlockDoc", formId: 'empty',
	return $.ajax({handler: 'cls1141m01formhandler', action: "unlockDoc_aft_genL120M01A", formId: 'empty',
        data: { 'lockMainId': lockMainId
        },
        success: function(responseData){
        	ilog.debug("{done}dfd_unlockDoc");                           
        }
    });
}

function prepare_gen_71_brmp_sum_compensationAmt(custId,dupNo){
	var my_dfd = $.Deferred(); //為了檢核 決策平台 連 EJCIC狀況，在 server 端就先 run API：headAccountValidation
	$.ajax({
        type: "POST",
        handler: "cls1141m01formhandler",
        data: {formAction: "prepare_gen_71_brmp_sum_compensationAmt", "custId":custId, "dupNo":dupNo},
        success: function(json){
        	if(json.compensationAmt>0){
        	    $("#createCntrNo_brmp_creditCheck_compensationAmt").val(json.compensationAmt);
        	}
        }
    });
	return my_dfd.promise();
}

$("input[type='radio'][name='docKind']").change(function(){
    $("input[type='radio'][name=authLvl]").removeAttr("checked");
});

$("input[type='radio'][name='docKind']:checked").trigger("change");

//=================
//專案信貸(歡喜信貸)
$("#createCntrNo_fast_prodKind71_esggtype").change(function(){
	if($(this).val()=="Z"){
		$("#div_createCntrNo_fast_prodKind71_esggtypeZ").show();		
	}else{
		$("#div_createCntrNo_fast_prodKind71_esggtypeZ").find(':input').not(':button, :submit, :reset, :hidden').not(':checkbox, :radio').val('');
		$("#div_createCntrNo_fast_prodKind71_esggtypeZ").hide();
	}
});

$("input[type='radio'][name='createCntrNo_fast_prodKind71_esggnLoanFg']").change(function(){
	if($(this).val()=="Y"){
		$("#tr_EsgGtype_fast_prodKind71").show();		
	}else if($(this).val()=="N"){
		$("#createCntrNo_fast_prodKind71Form").injectData({'createCntrNo_fast_prodKind71_esggtype':'', 'createCntrNo_fast_prodKind71_esggtypeZMemo':''});
		$("#tr_EsgGtype_fast_prodKind71").hide();
	}
});

function prepare_gen_71_brmp_termGroupRule(custId, dupNo, termGroupObj, termGroupSubObj, termGroupRuleResultTextObj){
	var my_dfd = $.Deferred();
	termGroupObj.val("");
    termGroupRuleResultTextObj.text(termGroupObj.find("option:selected").text());
	if(custId != "" && dupNo != ""){
		$.ajax({
	        type: "POST",
	        handler: "cls1141m01formhandler",
	        data: {formAction: "show_last_brmp_termGroupRule", "custId":custId, "dupNo":dupNo},
	        success: function(jsonparm){
	        	//console.log(jsonparm);
	        	var resultmsg = "";
	        	if(jsonparm.hasbrmp004){//有結果
	                if(jsonparm.brmp004data.result.termGroup != null && jsonparm.brmp004data.result.termGroup != ""){//最終客群分類 "X":不承作 , "S":小額 , "N":普惠 , "G":優質, "E":行員
	                	termGroupObj.val(jsonparm.brmp004data.result.termGroup);
	                	resultmsg += termGroupObj.find(":selected").text();
	                	if(jsonparm.brmp004data.result.applyDBRType != null){
		                	termGroupSubObj.val(jsonparm.brmp004data.result.applyDBRType);	
		                	if(jsonparm.brmp004data.result.applyDBRType == "A"){
	                    		resultmsg += " (DBR上限15倍)";
	                    	}
		                }
		                termGroupRuleResultTextObj.text(resultmsg);
	                }else{
	                	termGroupObj.val("");
		            	termGroupRuleResultTextObj.text(termGroupObj.find("option:selected").text());
	                	CommonAPI.showMessage(i18n.cls1141v01['L140S02A.chkTermGroup']);
	                }           
	            }else if(jsonparm.isBankMan){
                    termGroupObj.val("E");
                    termGroupRuleResultTextObj.text(termGroupObj.find("option:selected").text());
                }else{
	            	termGroupObj.val("");
	            	termGroupRuleResultTextObj.text(termGroupObj.find("option:selected").text());
	            	CommonAPI.showMessage(i18n.cls1141v01['L140S02A.chkTermGroup']);
	            }
	        }
	    });
	}
	return my_dfd.promise();
}

//=================
// brmp
$("input[type='radio'][name='createCntrNo_brmp_creditCheck_creditLoanPurpose']").change(function(){
	if($(this).val()=="G"){
		$("#div_createCntrNo_brmp_creditCheck_induceFlagOrNot").show();		
	}else{
		$("#createCntrNo_brmp_creditCheck_induceFlagOrNot").removeAttr("checked").trigger("change");
		$("#div_createCntrNo_brmp_creditCheck_induceFlagOrNot").hide();
	}
});


$("input[type='radio'][name='createCntrNo_brmp_creditCheck_esggnLoanFg']").change(function(){
	if($(this).val()=="Y"){
		$("#tr_EsgGtype_brmp").show();		
	}else if($(this).val()=="N"){
		$("#createCntrNo_brmp_creditCheckForm").injectData({'createCntrNo_brmp_creditCheck_esggtype':'', 'createCntrNo_brmp_creditCheck_esggtypeZMemo':''});
		$("#tr_EsgGtype_brmp").hide();
	}
});


$("#createCntrNo_brmp_creditCheck_induceFlagOrNot").change(function() {
	if($(this).is(':checked')){
		$("#createCntrNo_brmp_creditCheckForm").injectData({'createCntrNo_brmp_creditCheck_induceFlagXV':'V'});
        $("#tr_createCntrNo_brmp_creditCheck_induceFlagXV").show();		
	}else{
		$("#tr_createCntrNo_brmp_creditCheck_induceFlagXV").hide();
	}
});

$("#createCntrNo_brmp_creditCheck_esggtype").change(function(){
	if($(this).val()=="Z"){
		$("#div_createCntrNo_brmp_creditCheck_esggtypeZ").show();		
	}else{
		$("#div_createCntrNo_brmp_creditCheck_esggtypeZ").find(':input').not(':button, :submit, :reset, :hidden').not(':checkbox, :radio').val('');
		$("#div_createCntrNo_brmp_creditCheck_esggtypeZ").hide();
	}
});

//userInfo.unitNo
var $authLvlShow = $(".authLvlShow");
$("input[name=docKind]").click(function(){
    if ($(this).val() == "1") {
        if (userInfo.unitType == "1") {
            $authLvlShow.show();
        }
    }
    else {
        $authLvlShow.hide();
    }
});

var QuickReviewForCreditLoans = {
	
	open:function(){
		
		$.thickbox.close();
						
		var _form = "createCntrNo_credit_Loan_data_Form";
		$("#" + _form).reset();
		$("#" + _form).injectData({
			'createCntrNo_credit_Loan_data_dupNo': '0'
		});
		$("#createCntrNo_credit_Loan_data_Div").thickbox({
			title: '本次申貸資料',
			width: 450,
			height: 700,
			align: 'center',
			valign: 'bottom',
			modal: true,
			i18n: i18n.def,
			buttons: {
				"sure": function(){
					if (!$("#" + _form).valid()) {
						return;
					}
				},
				"cancel": function(){
					$.thickbox.close();
				}
			}
		});
	},
	
	init: function(){
		
		//是否填寫 本案購買本行理財商品無徵提聲明書或有勸誘事宜註記
		$('input[name="createCntrNo_credit_Loan_data_loan_purpose"]').change(function(){
			QuickReviewForCreditLoans.isShowFinancialProductsBuyingFlag();
		})
	},
	
	isShowFinancialProductsBuyingFlag: function(){
		
		var isShowFinancialProductsBuyingFlag = false;
		$('input[name="createCntrNo_credit_Loan_data_loan_purpose"]:checked').each(function() {
			if(this.value == 'M'){
		   		isShowFinancialProductsBuyingFlag = true;
		    }
		});
		
		if(isShowFinancialProductsBuyingFlag){
			$("#financialProductsBuyingFlagTr").show();
		}
		else{
			$("#financialProductsBuyingFlagTr").hide();
		}
	}

};

var FastCreditLoans = {

    init:function(){
        var brmpFormName  = "createCntrNo_brmp_creditCheckForm";

        $.ajax({
            type : "POST",
            handler: "cls1141m01formhandler",
            action: "getPloanPlan",
            success:function(responseData){
                // 使用專案
                $("#createCntrNo_brmp_creditCheck_usePlan").setItems({
                    // i18n : i18n.samplehome,
                    item : responseData.usePlanItem,
                    format : "{key}" // ,
                // value :
                });
            }
        });
        $('#'+brmpFormName).buildItem();

        $('#createCntrNo_brmp_creditCheck_custPos_rKindM').change(function(){
                var value = $(this).val() + '';
                var $form = $('#'+brmpFormName);
                $form.find('.brmp_creditCheck_custPos_rKindD').hide().val('');
                switch (value) {
                    case '1':
                    case '2':
                        var $obj = $form.find('#createCntrNo_brmp_creditCheck_custPos_rKindD' + value);
                        $obj.val($form.find('#createCntrNo_brmp_creditCheck_custPos_rKindD').val()).show();
                        break;
                    case '3':
                        var s = ($form.find('#createCntrNo_brmp_creditCheck_custPos_rKindD').val() || '').split('');
                        $form.find('#createCntrNo_brmp_creditCheck_custPos_rKindD31').val(s.length >= 2 ? s[0] : '').show();
                        $form.find('#createCntrNo_brmp_creditCheck_custPos_rKindD32').val(s.length >= 2 ? s[1] : '').show();
                        break;
                }

                //當關係類別為2-親屬關係, 非X0-本人時, 顯示與借款人同住選項
                brmp_isShowLiveWithBorrowerItem(value, $form.find('#createCntrNo_brmp_creditCheck_custPos_rKindD'+ value).val(), $form);

        });

        $('#'+brmpFormName).find('#createCntrNo_brmp_creditCheck_custPos_rKindD1,#createCntrNo_brmp_creditCheck_custPos_rKindD2').change(function(){
            $('#'+brmpFormName).find('#createCntrNo_brmp_creditCheck_custPos_rKindD').val($(this).val());
            var formObject = $('#'+brmpFormName);
            brmp_isShowLiveWithBorrowerItem(formObject.find('#createCntrNo_brmp_creditCheck_custPos_rKindM').val(), $(this).val(), formObject);
        });

        $('#'+brmpFormName).find('#createCntrNo_brmp_creditCheck_custPos_rKindD31,#createCntrNo_brmp_creditCheck_custPos_rKindD32').change(function(){
            $('#'+brmpFormName).find('#createCntrNo_brmp_creditCheck_custPos_rKindD').val($('#createCntrNo_brmp_creditCheck_custPos_rKindD31').val() + $('#createCntrNo_brmp_creditCheck_custPos_rKindD32').val());
        });
        $('#createCntrNo_brmp_imp_onlineCaseNo').click(function(){
            var $form = $('#'+brmpFormName);

            var createCntrNo_brmp_creditCheck_custId = $form.find("#createCntrNo_brmp_creditCheck_custId").val();
            if(createCntrNo_brmp_creditCheck_custId==""){
                API.showMessage("請先輸入「借款人統編」");
            }else{
                $.ajax({ type: "POST",handler: "cls1141m01formhandler",
                    data: {
                         'formAction': "get_latest_c122m01a_applyKind_P"
                        , 'custId' : createCntrNo_brmp_creditCheck_custId
                    },
                    success: function(json){
                        if(json.onlineCaseNo){
                            $form.injectData({'createCntrNo_brmp_creditCheck_onlineCaseNo':json.onlineCaseNo });
                            $form.injectData({'createCntrNo_brmp_creditCheck_usePlan':json.usePlan });
                        }else{
                            API.showMessage("客戶"+createCntrNo_brmp_creditCheck_custId+" 近期無「線上申貸」記錄");
                        }
                        //J-111-0160 無紙化簽報，檢核行銷為銀行行員，前端自動帶入引介來源
                        if(json.introduceSrc){
                            $form.injectData({'createCntrNo_brmp_creditCheck_introduceSrc':json.introduceSrc });
                            IntroductionSource_PaperlessSigning.change();
                            if(json.introduceSrc=="1"){
                                $form.injectData({'megaEmpNo':json.marketStaff });
                                $form.injectData({'megaEmpBrNo':json.megaEmpBrNo });
                                $form.injectData({'megaEmpBrNoName':json.megaEmpBrNoName });
                            }
                            if(json.introduceSrc=="3"){
                                IntroductionSource_PaperlessSigning.loadMegaSubUnitNo(json.megaCode);
                                $form.injectData({'megaCode':json.megaCode });
                                $form.injectData({'subEmpNo':json.subEmpNo });
                            }
                        }
                        else{
                            $form.injectData({'createCntrNo_brmp_creditCheck_introduceSrc':'' });
                            IntroductionSource_PaperlessSigning.change();
                        }
                    }
                });
            }
        });
        //新增>> 快速審核(信貸)
        $("#createCntrNo_brmp_creditCheckForm").find("#createCntrNo_brmp_creditCheck_custId").change(function(){
            prepare_gen_71_brmp_termGroupRule($("#createCntrNo_brmp_creditCheck_custId").val(), $("#createCntrNo_brmp_creditCheck_dupNo").val(),
                    $("select#createCntrNo_brmp_creditCheck_termGroup"), $("#createCntrNo_brmp_creditCheck_termGroupSub"),
                    $("#span_brmp_creditCheck_termGroupRuleResultText"));
        });
        $("#createCntrNo_brmp_creditCheckForm").find("#createCntrNo_brmp_creditCheck_dupNo").change(function(){
            prepare_gen_71_brmp_termGroupRule($("#createCntrNo_brmp_creditCheck_custId").val(), $("#createCntrNo_brmp_creditCheck_dupNo").val(),
                    $("select#createCntrNo_brmp_creditCheck_termGroup"), $("#createCntrNo_brmp_creditCheck_termGroupSub"),
                    $("#span_createCntrNo_fast_prodKind71_termGroupRuleResultText"));
        });
        $("#createCntrNo_brmp_creditCheck_custId").change(function(){
            prepare_gen_71_brmp_sum_compensationAmt($(this).val(),$("#createCntrNo_brmp_creditCheck_dupNo").val());
        });
    },
	open:function(){

		$.thickbox.close();

        var _form = "createCntrNo_brmp_creditCheckForm";
        $("#"+_form).reset();

        //J-112-0145_10702_B1001
        $("#" + _form).injectData({
            'createCntrNo_brmp_creditCheck_pConBegEnd_fg': '2'  //在 reset 後，預計12期
        });

        if(true){
            $("#"+_form).find("#createCntrNo_brmp_creditCheck_introduceSrc").removeOptions(["2","4","8"]);
        }
        $("#"+_form).find(".brmp_creditCheck_custPos_rKindD").hide();
        $("#"+_form).find(".brmp_creditCheck_custPos_isLiveWithBorrowerSpan").hide();
        $("#"+_form).find("#tr_EsgGtype_brmp").hide();
        $("#"+_form).injectData({'createCntrNo_brmp_creditCheck_dupNo':'0'
            , 'createCntrNo_brmp_creditCheck_otherAdjustAmt':'0'
            , 'createCntrNo_brmp_creditCheck_compensationAmt':'0'
            , 'createCntrNo_brmp_creditCheck_esggnLoanFg':'N'    //在 reset 後，綠色支出類型 會被清空
        });
        $("#"+_form).find("#div_createCntrNo_brmp_creditCheck_induceFlagOrNot").hide();
        $("#"+_form).find("#tr_createCntrNo_brmp_creditCheck_induceFlagXV").hide();

        $("#createCntrNo_brmp_creditCheckDiv").thickbox({
            title: '本次申貸資料', width: 860, height: 720, align: 'center', valign: 'bottom', modal: true, i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if (!$("#"+_form).valid()) {
                        return;
                    }
                    var frmJSON = $("#"+_form).serializeData();

                    var createCntrNo_brmp_creditCheck_custId = frmJSON["createCntrNo_brmp_creditCheck_custId"];
                    var createCntrNo_brmp_creditCheck_dupNo = frmJSON["createCntrNo_brmp_creditCheck_dupNo"];
                    var createCntrNo_brmp_creditCheck_termGroup = frmJSON["createCntrNo_brmp_creditCheck_termGroup"];
                    var createCntrNo_brmp_creditCheck_termGroupSub = frmJSON["createCntrNo_brmp_creditCheck_termGroupSub"];
                    var createCntrNo_brmp_creditCheck_creditLoanPurpose = frmJSON["createCntrNo_brmp_creditCheck_creditLoanPurpose"];
                    var createCntrNo_brmp_creditCheck_onlineCaseNo = frmJSON["createCntrNo_brmp_creditCheck_onlineCaseNo"];

                    var createCntrNo_brmp_creditCheck_custPos_custId = frmJSON["createCntrNo_brmp_creditCheck_custPos_custId"];
                    var createCntrNo_brmp_creditCheck_custPos_dupNo = frmJSON["createCntrNo_brmp_creditCheck_custPos_dupNo"];
                    var createCntrNo_brmp_creditCheck_custPos_rKindM = frmJSON["createCntrNo_brmp_creditCheck_custPos_rKindM"];
                    var createCntrNo_brmp_creditCheck_custPos_rKindD = frmJSON["createCntrNo_brmp_creditCheck_custPos_rKindD"];

                    var createCntrNo_brmp_creditCheck_lnYear = frmJSON["createCntrNo_brmp_creditCheck_lnYear"];
                    var createCntrNo_brmp_creditCheck_lnMonth = frmJSON["createCntrNo_brmp_creditCheck_lnMonth"];
                    var createCntrNo_brmp_creditCheck_esggnLoanFg = frmJSON["createCntrNo_brmp_creditCheck_esggnLoanFg"]
                    var createCntrNo_brmp_creditCheck_esggtype = frmJSON["createCntrNo_brmp_creditCheck_esggtype"]
                    var createCntrNo_brmp_creditCheck_esggtypeZMemo = frmJSON["createCntrNo_brmp_creditCheck_esggtypeZMemo"]
                    var createCntrNo_brmp_creditCheck_houseLoanFlag = frmJSON["createCntrNo_brmp_creditCheck_houseLoanFlag"]
                    var usePlan = frmJSON["createCntrNo_brmp_creditCheck_usePlan"]
                    //消金信貸，從債務人 應為 N:一般保證人
                    prepare_gen_71_brmp_caseDoc_tabDoc(createCntrNo_brmp_creditCheck_custId, createCntrNo_brmp_creditCheck_dupNo
                            , createCntrNo_brmp_creditCheck_termGroup, createCntrNo_brmp_creditCheck_creditLoanPurpose, createCntrNo_brmp_creditCheck_onlineCaseNo
                            , createCntrNo_brmp_creditCheck_custPos_custId, createCntrNo_brmp_creditCheck_custPos_dupNo
                            , createCntrNo_brmp_creditCheck_custPos_rKindM, createCntrNo_brmp_creditCheck_custPos_rKindD
                            , createCntrNo_brmp_creditCheck_lnYear, createCntrNo_brmp_creditCheck_lnMonth
                            , createCntrNo_brmp_creditCheck_esggnLoanFg
                            , createCntrNo_brmp_creditCheck_esggtype, createCntrNo_brmp_creditCheck_esggtypeZMemo,createCntrNo_brmp_creditCheck_houseLoanFlag,usePlan).done(function(json_prepare_gen_71_caseDoc_tabDoc){
                        //~~~~
                        var json_param = {'docKind':'1'  //授權別 1授權內
                            , 'docCode': '1'	//案件別 1一般
                            , 'authLvl': '1'	//授權等級{1:分行授權內 ; 3:營運中心授權內}
                            , 'ngFlag' : '0'	//協議案註記 0無
                            };
                        cfm_msg_checkIsStop(json_param).done(function(){
                            dfd_addL120m01a(json_param).done(function(caseDoc_param){
                               dfd_71_importLendCollateral(caseDoc_param, json_prepare_gen_71_caseDoc_tabDoc.c101_mainId).done(function(){
                                  dfd_71_setMainCust(caseDoc_param).done(function(json_71_setMainCust){
                                     dfd_71_importLendCollateral_custPos(caseDoc_param, json_prepare_gen_71_caseDoc_tabDoc.custPos_c101_mainId).done(function(){
                                         dfd_71_caseDoc_saveAll(caseDoc_param).done(function(){
                                                //產出 brmp 的l140m01a ===> 參考 dfd_71_gen_tabDoc (... )
                                                brmp_creditCheck_new(caseDoc_param.mainId, json_71_setMainCust.c120m01a_oid , _form ).done(function(json_71_gen_tabDoc){
                                                    brmp_upd_caseDoc___tabDoc_cntrNo_npl(caseDoc_param.mainId, json_71_gen_tabDoc.tabMainId).done(function(){

                                                       dfd_import_simplifyFlag_D___L120S15A_content(caseDoc_param.mainId).done(function(){
                                                          dfd_import_property7_cntrNo(caseDoc_param.mainId, json_71_setMainCust.c120m01a_oid , "N" ).done(function(){
                                                             dfd_fast_checkSend_CLS_L120M01A_countValue(caseDoc_param.mainId).done(function(){
                                                                dfd_fastCntrNo_CLS_L120M01A_sendAML(caseDoc_param.mainId).done(function(json_sendAML){
                                                                   dfd_fastCntrNo_CLS_L120M01A_getAML(caseDoc_param.mainId, json_sendAML).done(function(){
                                                                     dfd_unlockDoc(caseDoc_param.mainId).done(function(){
                                                                     //=======================
                                                                     $.thickbox.close();
                                                                     //=======================
                                                                     //在 Grid 出現 新增的簽報書
                                                                     $("#gridview").trigger("reloadGrid");
                                                                     if(json_71_gen_tabDoc.rejectDesc && json_71_gen_tabDoc.rejectDesc!=''){
                                                                        API.showErrorMessage( json_71_gen_tabDoc.rejectDesc );
                                                                     }else{
                                                                        if(json_71_gen_tabDoc.statusMissing && json_71_gen_tabDoc.statusMissing!=''){
                                                                            API.showErrorMessage( json_71_gen_tabDoc.statusMissing );
                                                                        }
                                                                     }
                                                                    });
                                                                   });
                                                                });
                                                             });
                                                         });
                                                      });
                                                   });
                                                });
                                          });

                                     });
                                  });
                               });
                            });
                        });
                    });
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
	}

};

function delayProgress(progress){
    setTimeout(function(){
        if (progress < 100) {
            $('#CaseType3Form').find('#progress').html(progress += 5);
            delayProgress(progress);
        }
    }, 800);
}

//$("input[type='radio'][name='createCntrNo_brmp_creditCheck_stageCount']").change(function(){
//	if($(this).val()=="1"){
//		$("[name=createCntrNo_brmp_creditCheck_pConBegEnd_fg]").removeAttr("required", "disabled");
//	}else{
////		$("[name=createCntrNo_brmp_creditCheck_pConBegEnd_fg]").attr("disabled", "disabled");
////		$("#createCntrNo_brmp_creditCheck_pConBegEnd_fg").filter('[value="Y"]').attr('checked', true);
//        $("[name=createCntrNo_brmp_creditCheck_pConBegEnd_fg]").addClass("required");
//	}
//});

var IntroductionSource_PaperlessSigning = {
	
	show: function(introduceSrc){

		switch (introduceSrc) {
		    case '1':
		        $("#employeeDiv").show();
		        break;
		    case '3':
		        $("#megaSubCompanyDiv").show();
		        break;
		    case '5':
				$("#customerOrCompanyDiv").show();
				$("#importCustOrComSpan").show();
		        break;
		    case '6':
		        $("#customerOrCompanyDiv").show();
				$("#importCustOrComSpan").show();
				break;
			case '9':
				$("#customerOrCompanyDiv").show();
				$("#importCustOrComSpan").show();
				break;
			case 'A':
				$("#customerOrCompanyDiv").show();
				$("#importCustOrComSpan").show();
				break;
		}
	},
	
	change: function(){

		$("#employeeDiv").hide();
		$("#megaSubCompanyDiv").hide();
		$("#customerOrCompanyDiv").hide();
		$("#introducerNameDiv").hide();
		
		//行員引介
		$("#employeeDiv").find("input:text").val("");
		//金控子公司員工引介
		$("#megaSubCompanyDiv").find("select").val("");
		$("#megaSubCompanyDiv").find("input:text").val("");
		//往來企金戶所屬員工, 本行客戶引介
		$("#customerOrCompanyDiv").find("input:text").val("");
		//引介人姓名
		$("#introducerNameDiv").find("input:text").val("");
		
		IntroductionSource_PaperlessSigning.show($("#createCntrNo_brmp_creditCheck_introduceSrc").val());
	},

	openBox: function(){

		var buttons = {};
		buttons[i18n.def.close] = function(){				
			$.thickbox.close();
        };
		
       	$("#openBox_realEstateIntroduction").thickbox({
            title: i18n.cls1151s01['page01.title.introductionRealEstateAgentInfo'],
            width: 550,
            height: 250,
            modal: true,
			align: "center",
			valign: 'bottom',
            i18n: i18n.def,
            buttons: buttons
        });
	},
	
	importCustomerOrCompanyInfo: function(){

		AddCustAction.open({
    		handler: 'cls1151m01formhandler',
			action : 'importCustomerOrCompanyInfo',
			data : {
            },
			callback : function(json){					
            	// 關掉 AddCustAction 的 
            	$.thickbox.close();					
				$("#introCustId").val(json.introCustId);
				$("#introDupNo").val(json.introDupNo);
				$("#introCustName").val(json.introCustName);
				var introduceSrc = $("#createCntrNo_brmp_creditCheck_introduceSrc").val();
				if(introduceSrc == '9' || introduceSrc == 'A'){
					$("#introducerName").val(json.introCustName)
				}
			}
		});
	},
	
	//帶入引介子公司分支代號
	loadMegaSubUnitNo: function(megaCode){

		var form = $("#createCntrNo_brmp_creditCheckForm");
		if (megaCode == '') {
			$("#subUnitNo").setItems({});
			$("#subEmpNo").val("");
			$("#subEmpNm").val("");
		}
		else {
			var key_sub_unitNo = ('LNF13E_SUB_UNITNO_' + megaCode);
			var item = CommonAPI.loadCombos(key_sub_unitNo);
			form.find("#subUnitNo").setItems({
				item: item[key_sub_unitNo],
				format: "{value} - {key}"
			});
		}
	},
	
	selectBranch: function(){
		var form = $("#createCntrNo_brmp_creditCheckForm");
		$.ajax({
            handler: "cls1220m10formhandler",
            action: "getAllOrgBrId",
         	success:function(responseData){
    			if(responseData.Success){ //成功
    				form.find("#selectBranch").setItems({
                        item: responseData.childMap,
                        space: true,
                        format: "{value} - {key}"
                    });
    			}
    		}
        }).done(function(){
			$("#selectBranchDiv").thickbox({
  	       	 	title: i18n.cls1141v01['page01.title.selectBranch'], width: 350, height: 100, align: 'center', valign: 'bottom', modal: true, 
				i18n: i18n.def,
  	            buttons: {
  	                "sure": function(){
  	                	var branchStr = $("#selectBranch option:selected").text();
						var branchArray = branchStr.split("-");
						$("#megaEmpBrNo").val($.trim(branchArray[0]));
						$("#megaEmpBrNoName").val($.trim(branchArray[1]));
						$.thickbox.close();
  	                },
  	                "cancel": function(){
  	               		$.thickbox.close();
  	                }
  	            }
  	        });
		});
	}
}

function dateObjtoStr(tDate){
    return tDate.getFullYear() + "-" + (tDate.getMonth() < 9 ? "0" : "") + (tDate.getMonth() + 1) + "-" + (tDate.getDate() < 10 ? "0" : "") + tDate.getDate();
}
