/* 
 * L730M01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L730M01A;

/** 授信報案考核表主檔 **/
public interface L730M01ADao extends IGenericDao<L730M01A> {

	L730M01A findByOid(String oid);
	
	L730M01A findByMainId(String mainId);
	
	List<L730M01A> findByDocStatus(String docStatus);
	
	L730M01A findByUniqueKey(String mainId);

	List<L730M01A> findByIndex01(String mainId);

	List<L730M01A> findByIndex02(String branchId, Date chkYM, String sysType, String chkUnit, String mainId);
}