/* 
 * LMS7110Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.service;

import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.model.L785M01A;

import tw.com.iisi.cap.model.GenericBean;

/**
 * <pre>
 * 停權解除維護Service
 * </pre>
 * 
 * @since 2013/1/21
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/1/21,Miller,new
 *          </ul>
 */
public interface LMS7850Service extends AbstractService {

	// 停權解除維護主檔

	/**
	 * 利用oid找尋停權解除維護主檔
	 * 
	 * @param oid
	 * @return
	 */
	L785M01A findL785m01aByOid(String oid);

	/**
	 * 利用mainId找尋停權解除維護主檔
	 * 
	 * @param mainId
	 * @return
	 */
	L785M01A findL785m01aByMainId(String mainId);

	/**
	 * 利用oid刪除停權解除維護主檔(邏輯刪除)
	 * 
	 * @param oid
	 */
	void deleteL785m01a(String oid);

	/**
	 * 
	 * 授管處停權解除flow
	 * 
	 * @param mainOid
	 *            文件編號
	 * @param model
	 *            資料表
	 * @param setResult
	 *            boolean
	 * @param next
	 *            執行的下個動作
	 * 
	 * @throws Throwable
	 */
	public void flowAction(String mainOid, GenericBean model,
			boolean setResult, String next) throws Throwable;

}
