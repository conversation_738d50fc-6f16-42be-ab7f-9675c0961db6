/* 
 * LMS9990ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.ctr.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.dao.DocFileDao;
import com.mega.eloan.common.enums.DocLogEnum;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.service.NumberService;
import com.mega.eloan.lms.ctr.service.LMS9990Service;
import com.mega.eloan.lms.dao.C999A01ADao;
import com.mega.eloan.lms.dao.C999M01ADao;
import com.mega.eloan.lms.dao.C999M01BDao;
import com.mega.eloan.lms.dao.C999M01CDao;
import com.mega.eloan.lms.dao.C999M01DDao;
import com.mega.eloan.lms.dao.C999S01ADao;
import com.mega.eloan.lms.dao.C999S01BDao;
import com.mega.eloan.lms.dao.C999S02ADao;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dao.L120S01ADao;
import com.mega.eloan.lms.dao.L120S01BDao;
import com.mega.eloan.lms.dao.L140M01ADao;
import com.mega.eloan.lms.dao.L140M01BDao;
import com.mega.eloan.lms.dao.L140M01CDao;
import com.mega.eloan.lms.dao.L140M01DDao;
import com.mega.eloan.lms.dao.L140M01FDao;
import com.mega.eloan.lms.dao.L140M01HDao;
import com.mega.eloan.lms.dao.L140M01NDao;
import com.mega.eloan.lms.dao.L140S01ADao;
import com.mega.eloan.lms.dao.L140S02ADao;
import com.mega.eloan.lms.dao.L140S02BDao;
import com.mega.eloan.lms.dao.L140S02CDao;
import com.mega.eloan.lms.dao.L140S02DDao;
import com.mega.eloan.lms.dao.L140S02EDao;
import com.mega.eloan.lms.dao.L140S02GDao;
import com.mega.eloan.lms.dao.L140S02HDao;
import com.mega.eloan.lms.dao.L140S02IDao;
import com.mega.eloan.lms.dao.L140S02JDao;
import com.mega.eloan.lms.dao.L140S02KDao;
import com.mega.eloan.lms.dao.L999A01ADao;
import com.mega.eloan.lms.dao.L999M01ADao;
import com.mega.eloan.lms.dao.L999M01BDao;
import com.mega.eloan.lms.dao.L999M01CDao;
import com.mega.eloan.lms.dao.L999M01DDao;
import com.mega.eloan.lms.dao.L999S01ADao;
import com.mega.eloan.lms.dao.L999S01BDao;
import com.mega.eloan.lms.dao.L999S02ADao;
import com.mega.eloan.lms.dao.L999S04ADao;
import com.mega.eloan.lms.dao.L999S04BDao;
import com.mega.eloan.lms.dao.L999S07ADao;
import com.mega.eloan.lms.eloandb.service.LmsCustdataService;
import com.mega.eloan.lms.lms.service.LMS1405Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C999A01A;
import com.mega.eloan.lms.model.C999M01A;
import com.mega.eloan.lms.model.C999M01B;
import com.mega.eloan.lms.model.C999M01C;
import com.mega.eloan.lms.model.C999M01D;
import com.mega.eloan.lms.model.C999S01A;
import com.mega.eloan.lms.model.C999S01B;
import com.mega.eloan.lms.model.C999S02A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120S01A;
import com.mega.eloan.lms.model.L120S01B;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01B;
import com.mega.eloan.lms.model.L140M01C;
import com.mega.eloan.lms.model.L140M01D;
import com.mega.eloan.lms.model.L140M01F;
import com.mega.eloan.lms.model.L140M01H;
import com.mega.eloan.lms.model.L140M01N;
import com.mega.eloan.lms.model.L140S01A;
import com.mega.eloan.lms.model.L140S02A;
import com.mega.eloan.lms.model.L140S02B;
import com.mega.eloan.lms.model.L140S02C;
import com.mega.eloan.lms.model.L140S02D;
import com.mega.eloan.lms.model.L140S02E;
import com.mega.eloan.lms.model.L140S02G;
import com.mega.eloan.lms.model.L140S02H;
import com.mega.eloan.lms.model.L140S02I;
import com.mega.eloan.lms.model.L140S02J;
import com.mega.eloan.lms.model.L140S02K;
import com.mega.eloan.lms.model.L999M01A;
import com.mega.eloan.lms.model.L999M01B;
import com.mega.eloan.lms.model.L999M01C;
import com.mega.eloan.lms.model.L999M01D;
import com.mega.eloan.lms.model.L999S01A;
import com.mega.eloan.lms.model.L999S01B;
import com.mega.eloan.lms.model.L999S02A;
import com.mega.eloan.lms.model.L999S04A;
import com.mega.eloan.lms.model.L999S04B;
import com.mega.eloan.lms.model.L999S07A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * <pre>
 * 企金約據書 Service
 * </pre>
 * 
 * @since 2012/2/11
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/2/11,Ice Lin add
 *          </ul>
 */

@Service
public class LMS9990ServiceImpl extends AbstractCapService implements
		LMS9990Service {
	@Resource
	TempDataService tempDataService;
	@Resource
	LmsCustdataService lmsCustdataService;

	@Resource
	C999M01ADao c999m01aDao;

	@Resource
	C999A01ADao c999a01aDao;

	@Resource
	C999M01BDao c999m01bDao;

	@Resource
	C999M01CDao c999m01cDao;

	@Resource
	C999M01DDao c999m01dDao;

	@Resource
	C999S01ADao c999s01aDao;

	@Resource
	C999S01BDao c999s01bDao;

	@Resource
	C999S02ADao c999s02aDao;

	@Resource
	L999A01ADao l999a01aDao;

	@Resource
	L999M01ADao l999m01aDao;

	@Resource
	L999M01BDao l999m01bDao;

	@Resource
	L999M01CDao l999m01cDao;

	@Resource
	L999M01DDao l999m01dDao;

	@Resource
	L999S01ADao l999s01aDao;

	@Resource
	L999S01BDao l999s01bDao;

	@Resource
	L999S02ADao l999s02aDao;

	@Resource
	L999S04ADao l999s04aDao;

	@Resource
	L999S04BDao l999s04bDao;

	@Resource
	L140S01ADao l140s01aDao;
	@Resource
	L140S02ADao l140s02aDao;
	@Resource
	L140S02BDao l140s02bDao;
	@Resource
	L140S02CDao l140s02cDao;
	@Resource
	L140S02DDao l140s02dDao;
	@Resource
	L140S02EDao l140s02eDao;
	@Resource
	L140S02GDao l140s02gDao;
	@Resource
	L140S02HDao l140s02hDao;
	@Resource
	L140S02IDao l140s02iDao;
	@Resource
	L140S02JDao l140s02jDao;
	@Resource
	L140S02KDao l140s02kDao;

	@Resource
	L999S07ADao l999s07aDao;
	
	@Resource
	DocLogService docLogService;

	@Resource
	DocFileDao docFileDao;

	@Resource
	BranchService branchService;

	@Resource
	LMS1405Service lms1405Service;

	@Resource
	MisdbBASEService misDBService;

	@Resource
	CodeTypeService codeTypeService;

	@Resource
	NumberService number;

	@Resource
	L140M01ADao l140m01aDao;

	@Resource
	L140M01BDao l140m01bDao;
	
	@Resource
	L140M01CDao l140m01cDao;
	
	@Resource
	L140M01DDao l140m01dDao;
	
	@Resource
	L140M01FDao l140m01fDao;
	
	@Resource
	L140M01NDao l140m01nDao;
	
	@Resource
	L140M01HDao l140m01hDao;
	
	@Resource
	L120M01ADao l120m01aDao;

	@Resource
	L120S01ADao l120s01aDao;

	@Resource
	L120S01BDao l120s01bDao;

	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L999M01A) {
					if (Util.isEmpty(((L999M01A) model).getRandomCode())) {
						((L999M01A) model).setUpdater(user.getUserId());
						((L999M01A) model).setUpdateTime(CapDate
								.getCurrentTimestamp());
						((L999M01A) model).setRandomCode(IDGenerator
								.getRandomCode());
						l999m01aDao.save((L999M01A) model);
						//
						// // 新增授權檔
						// L999A01A l999a01a = new L999A01A();
						// l999a01a.setAuthTime(CapDate.getCurrentTimestamp());
						// l999a01a.setAuthType(DocAuthTypeEnum.MODIFY.getCode());
						// l999a01a.setAuthUnit(user.getUnitNo());
						// l999a01a.setMainId(((L999M01A) model).getMainId());
						// l999a01a.setOwner(user.getUserId());
						// l999a01a.setOwnUnit(user.getUnitNo());
						// l999a01aDao.save(l999a01a);
					} else {
						// 當文件狀態為編製中時文件亂碼才變更
						if ((FlowDocStatusEnum.編製中.toString())
								.equals(((L999M01A) model).getDocStatus())) {
							((L999M01A) model).setRandomCode(IDGenerator
									.getRandomCode());
							((L999M01A) model).setUpdater(user.getUserId());
							((L999M01A) model).setUpdateTime(CapDate
									.getCurrentTimestamp());
							l999m01aDao.save((L999M01A) model);
							if (!"Y".equals(SimpleContextHolder
									.get(EloanConstants.TEMPSAVE_RUN))) {
								tempDataService
										.deleteByMainId(((L999M01A) model)
												.getMainId());
								docLogService.record(
										((L999M01A) model).getOid(),
										DocLogEnum.SAVE);
							}
						}

					}

				} else if (model instanceof L999M01B) {
					((L999M01B) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					((L999M01B) model).setUpdater(user.getUserId());
					((L999M01B) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					if (((L999M01B) model).getOid() == null) {
						((L999M01B) model).setCreator(user.getUserId());
						((L999M01B) model).setCreateTime(CapDate
								.getCurrentTimestamp());
					}
					l999m01bDao.save((L999M01B) model);
				} else if (model instanceof L999M01C) {
					((L999M01C) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					((L999M01C) model).setUpdater(user.getUserId());
					((L999M01C) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					if (((L999M01C) model).getOid() == null) {
						((L999M01C) model).setCreator(user.getUserId());
						((L999M01C) model).setCreateTime(CapDate
								.getCurrentTimestamp());
					}
					l999m01cDao.save((L999M01C) model);
				} else if (model instanceof L999M01D) {
					((L999M01D) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					((L999M01D) model).setUpdater(user.getUserId());
					((L999M01D) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					if (((L999M01D) model).getOid() == null) {
						((L999M01D) model).setCreator(user.getUserId());
						((L999M01D) model).setCreateTime(CapDate
								.getCurrentTimestamp());
					}
					l999m01dDao.save((L999M01D) model);
				} else if (model instanceof L999S01A) {
					((L999S01A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					((L999S01A) model).setUpdater(user.getUserId());
					((L999S01A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					if (((L999S01A) model).getOid() == null) {
						((L999S01A) model).setCreator(user.getUserId());
						((L999S01A) model).setCreateTime(CapDate
								.getCurrentTimestamp());
					}
					l999s01aDao.save((L999S01A) model);
				} else if (model instanceof L999S01B) {
					((L999S01B) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					((L999S01B) model).setUpdater(user.getUserId());
					((L999S01B) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					if (((L999S01B) model).getOid() == null) {
						((L999S01B) model).setCreator(user.getUserId());
						((L999S01B) model).setCreateTime(CapDate
								.getCurrentTimestamp());
					}
					l999s01bDao.save((L999S01B) model);
				} else if (model instanceof L999S02A) {
					((L999S02A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					((L999S02A) model).setUpdater(user.getUserId());
					((L999S02A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					if (((L999S02A) model).getOid() == null) {
						((L999S02A) model).setCreator(user.getUserId());
						((L999S02A) model).setCreateTime(CapDate
								.getCurrentTimestamp());
					}
					l999s02aDao.save((L999S02A) model);
				} else if (model instanceof L999S04A) {
					((L999S04A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					((L999S04A) model).setUpdater(user.getUserId());
					((L999S04A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					if (((L999S04A) model).getOid() == null) {
						((L999S04A) model).setCreator(user.getUserId());
						((L999S04A) model).setCreateTime(CapDate
								.getCurrentTimestamp());
					}
					l999s04aDao.save((L999S04A) model);
				} else if (model instanceof L999S04B) {
					((L999S04B) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					((L999S04B) model).setUpdater(user.getUserId());
					((L999S04B) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					if (((L999S04B) model).getOid() == null) {
						((L999S04B) model).setCreator(user.getUserId());
						((L999S04B) model).setCreateTime(CapDate
								.getCurrentTimestamp());
					}
					l999s04bDao.save((L999S04B) model);
				} else if (model instanceof C999M01A) {
					if (Util.isEmpty(((C999M01A) model).getRandomCode())) {
						((C999M01A) model).setUpdater(user.getUserId());
						((C999M01A) model).setUpdateTime(CapDate
								.getCurrentTimestamp());
						((C999M01A) model).setRandomCode(IDGenerator
								.getRandomCode());
						c999m01aDao.save((C999M01A) model);

						// 新增授權檔
						C999A01A c999a01a = new C999A01A();
						c999a01a.setAuthTime(CapDate.getCurrentTimestamp());
						c999a01a.setAuthType("1");
						c999a01a.setAuthUnit(user.getUnitNo());
						c999a01a.setMainId(((C999M01A) model).getMainId());
						c999a01a.setOwner(user.getUserId());
						c999a01a.setOwnUnit(user.getUnitNo());
						c999a01aDao.save(c999a01a);
					} else {
						// 當文件狀態為編製中時文件亂碼才變更
						if ((FlowDocStatusEnum.編製中.toString())
								.equals(((C999M01A) model).getDocStatus())) {
							((C999M01A) model).setRandomCode(IDGenerator
									.getRandomCode());
							((C999M01A) model).setUpdater(user.getUserId());
							((C999M01A) model).setUpdateTime(CapDate
									.getCurrentTimestamp());
							c999m01aDao.save((C999M01A) model);
							docLogService.record(((C999M01A) model).getOid(),
									DocLogEnum.SAVE);
						}

					}

				} else if (model instanceof C999M01B) {
					((C999M01B) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					((C999M01B) model).setUpdater(user.getUserId());
					((C999M01B) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					if (((C999M01B) model).getOid() == null) {
						((C999M01B) model).setCreator(user.getUserId());
						((C999M01B) model).setCreateTime(CapDate
								.getCurrentTimestamp());
					}
					c999m01bDao.save((C999M01B) model);
				} else if (model instanceof C999M01C) {
					((C999M01C) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					((C999M01C) model).setUpdater(user.getUserId());
					((C999M01C) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					if (((C999M01C) model).getOid() == null) {
						((C999M01C) model).setCreator(user.getUserId());
						((C999M01C) model).setCreateTime(CapDate
								.getCurrentTimestamp());
					}
					c999m01cDao.save((C999M01C) model);
				} else if (model instanceof C999M01D) {
					((C999M01D) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					((C999M01D) model).setUpdater(user.getUserId());
					((C999M01D) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					if (((C999M01D) model).getOid() == null) {
						((C999M01D) model).setCreator(user.getUserId());
						((C999M01D) model).setCreateTime(CapDate
								.getCurrentTimestamp());
					}
					c999m01dDao.save((C999M01D) model);
				} else if (model instanceof C999S01A) {
					((C999S01A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					((C999S01A) model).setUpdater(user.getUserId());
					((C999S01A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					if (((C999S01A) model).getOid() == null) {
						((C999S01A) model).setCreator(user.getUserId());
						((C999S01A) model).setCreateTime(CapDate
								.getCurrentTimestamp());
					}
					c999s01aDao.save((C999S01A) model);
				} else if (model instanceof C999S01B) {
					((C999S01B) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					((C999S01B) model).setUpdater(user.getUserId());
					((C999S01B) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					if (((C999S01B) model).getOid() == null) {
						((C999S01B) model).setCreator(user.getUserId());
						((C999S01B) model).setCreateTime(CapDate
								.getCurrentTimestamp());
					}
					c999s01bDao.save((C999S01B) model);
				} else if (model instanceof C999S02A) {
					((C999S02A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					((C999S02A) model).setUpdater(user.getUserId());
					((C999S02A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					if (((C999S02A) model).getOid() == null) {
						((C999S02A) model).setCreator(user.getUserId());
						((C999S02A) model).setCreateTime(CapDate
								.getCurrentTimestamp());
					}
					c999s02aDao.save((C999S02A) model);
				} else if (model instanceof L999S07A) {
					((L999S07A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					((L999S07A) model).setUpdater(user.getUserId());
					((L999S07A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					if (((L999S07A) model).getOid() == null) {
						((L999S07A) model).setCreator(user.getUserId());
						((L999S07A) model).setCreateTime(CapDate
								.getCurrentTimestamp());
					}
					l999s07aDao.save((L999S07A) model);
				}
			}
		}
	}

	@Override
	public void delete(GenericBean... entity) {
		for (GenericBean model : entity) {
			if (model instanceof L999M01A) {
				((L999M01A) model)
						.setDeletedTime(CapDate.getCurrentTimestamp());
				l999m01aDao.save((L999M01A) model);
			} else if (model instanceof L999M01C) {
				l999m01cDao.delete((L999M01C)model);
			}
		}
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == L999M01A.class) {
			return l999m01aDao.findPage(search);
		} else if (clazz == L999M01B.class) {
			return l999m01bDao.findPage(search);
		} else if (clazz == L999M01C.class) {
			return l999m01cDao.findPage(search);
		} else if (clazz == C999M01A.class) {
			return c999m01aDao.findPage(search);
		} else if (clazz == C999M01B.class) {
			return c999m01bDao.findPage(search);
		} else if (clazz == C999M01C.class) {
			return c999m01cDao.findPage(search);
		} else if (clazz == C999M01D.class) {
			return c999m01dDao.findPage(search);
		} else if (clazz == C999S01A.class) {
			return c999s01aDao.findPage(search);
		} else if (clazz == C999S01B.class) {
			return c999s01bDao.findPage(search);
		} else if (clazz == C999S02A.class) {
			return c999s02aDao.findPage(search);
		} else if (clazz == L140S01A.class) {
			return l140s01aDao.findPage(search);
		} else if (clazz == L140S02A.class) {
			return l140s02aDao.findPage(search);
		} else if (clazz == L140S02B.class) {
			return l140s02bDao.findPage(search);
		} else if (clazz == L140S02C.class) {
			return l140s02cDao.findPage(search);
		} else if (clazz == L140S02D.class) {
			return l140s02dDao.findPage(search);
		} else if (clazz == L140S02E.class) {
			return l140s02eDao.findPage(search);
		} else if (clazz == L140S02G.class) {
			return l140s02gDao.findPage(search);
		} else if (clazz == L140S02H.class) {
			return l140s02hDao.findPage(search);
		} else if (clazz == L140S02I.class) {
			return l140s02iDao.findPage(search);
		} else if (clazz == L140S02J.class) {
			return l140s02jDao.findPage(search);
		} else if (clazz == L140S02K.class) {
			return l140s02kDao.findPage(search);
		}
		return null;
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		if (clazz == L999M01A.class) {
			return (T) l999m01aDao.findByOid(oid);
		}
		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		return null;
	}

	@Override
	public Page<Map<String, Object>> listDupNoToCustId(String custId,
			ISearch search) throws CapException {
		List<Map<String, Object>> beanList = new ArrayList<Map<String, Object>>();
		Map<String, Object> data = null;
		List<Map<String, Object>> list = lmsCustdataService
				.findCustDataByCustId(custId);
		// 同身份證字號的人列表
		for (Map<String, Object> map : list) {
			data = new HashMap<String, Object>();
			data.put("custName", Util.nullToSpace(map.get("cName")));
			data.put("custId", Util.nullToSpace(map.get("custId")));
			data.put("dupNo", Util.nullToSpace(map.get("dupNo")));
			beanList.add(data);
		}

		return new Page<Map<String, Object>>(beanList, beanList.size(),
				search.getMaxResults(), search.getFirstResult());
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.ctr.service.LMS9990Service#findL999m01aByMainId(java
	 * .lang.String)
	 */
	@Override
	public L999M01A findL999m01aByMainId(String mainId) {
		return l999m01aDao.findByMainId(mainId);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.ctr.service.LMS9990Service#findL999m01bByMainIdType
	 * (java.lang.String, java.lang.String)
	 */
	@Override
	public L999M01B findL999m01bByMainIdType(String mainId, String type) {
		return l999m01bDao.findByUniqueKey(mainId, type);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.ctr.service.LMS9990Service#findL999s01aByMainId(java
	 * .lang.String)
	 */
	@Override
	public L999S01A findL999s01aByMainId(String mainId) {
		return l999s01aDao.findByMainId(mainId);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.ctr.service.LMS9990Service#findL999m01cByMainId(java
	 * .lang.String)
	 */
	@Override
	public List<L999M01C> findL999m01cByMainId(String mainId) {
		return l999m01cDao.findByMainId(mainId);
	}

	@Override
	public List<L999S04B> findL999s04bByMainId(String mainId) {
		return l999s04bDao.findByMainId(mainId);
	}

	@Override
	public L999S04B findL999s04bByMainIdType(String mainId, String type) {
		return l999s04bDao.findByUniqueKey(mainId, type);
	}

	@Override
	public L999S04A findL999s04aByMainId(String mainId) {
		return l999s04aDao.findByMainId(mainId);
	}

	@Override
	public List<L999S01B> findL999s01bByMainId(String mainId) {
		return l999s01bDao.findByMainId(mainId);
	}

	@Override
	public L999S02A findL999s02aByMainId(String mainId) {
		return l999s02aDao.findByMainId(mainId);
	}

	@Override
	public L999S01B findL999s01bByMainIdItemType(String mainId, String itemType) {
		return l999s01bDao.findByUniqueKey(mainId, itemType);
	}

	@Override
	public List<L999M01D> findL999m01dByMainId(String mainId) {
		return l999m01dDao.findByMainId(mainId);
	}

	@Override
	public L999M01D findL999m01dByMainIdItemType(String mainId, String itemType) {
		return l999m01dDao.findByUniqueKey(mainId, itemType);
	}

	@Override
	public void deleteUploadFile(String[] oids) {
		List<DocFile> docfile = docFileDao.findAllByOid(oids);
		docFileDao.delete(docfile);
	}

	@Override
	public List<L140M01A> findL140M01AByOids(String[] oids) {
		return l140m01aDao.findL140m01aListByOids(oids);
	}

	@Override
	public void saveAll(List<L999M01A> l999m01as, List<L999M01B> l999m01bs,
			List<L999M01C> l999m01cs, List<L999S01A> l999s01as,
			List<L999M01D> l999m01ds) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (L999M01A model : l999m01as) {
			if (Util.isEmpty(model.getOid())) {
				model.setCreateTime(CapDate.getCurrentTimestamp());
				model.setCreator(user.getUserId());
			}
			model.setUpdateTime(CapDate.getCurrentTimestamp());
			model.setUpdater(user.getUserId());
		}
		for (L999M01B model : l999m01bs) {
			if (Util.isEmpty(model.getOid())) {
				model.setCreateTime(CapDate.getCurrentTimestamp());
				model.setCreator(user.getUserId());
			}
			model.setUpdateTime(CapDate.getCurrentTimestamp());
			model.setUpdater(user.getUserId());
		}
		for (L999M01C model : l999m01cs) {
			if (Util.isEmpty(model.getOid())) {
				model.setCreateTime(CapDate.getCurrentTimestamp());
				model.setCreator(user.getUserId());
			}
			model.setUpdateTime(CapDate.getCurrentTimestamp());
			model.setUpdater(user.getUserId());
		}
		for (L999S01A model : l999s01as) {
			if (Util.isEmpty(model.getOid())) {
				model.setCreateTime(CapDate.getCurrentTimestamp());
				model.setCreator(user.getUserId());
			}
			model.setUpdateTime(CapDate.getCurrentTimestamp());
			model.setUpdater(user.getUserId());
		}

		for (L999M01D model : l999m01ds) {
			if (Util.isEmpty(model.getOid())) {
				model.setCreateTime(CapDate.getCurrentTimestamp());
				model.setCreator(user.getUserId());
			}
			model.setUpdateTime(CapDate.getCurrentTimestamp());
			model.setUpdater(user.getUserId());
		}
		l999m01aDao.save(l999m01as);
		l999m01bDao.save(l999m01bs);
		l999m01cDao.save(l999m01cs);
		l999m01dDao.save(l999m01ds);
		l999s01aDao.save(l999s01as);

	}

	@Override
	public void saveAll(List<GenericBean> genericbeans) {
		for (GenericBean genericbean : genericbeans) {
			this.save(genericbean);
		}
	}

	@Override
	public L140M01B findL140M01B(String mainId, String itemType) {
		return l140m01bDao.findByUniqueKey(mainId, itemType);
	}
	
	@Override
	public L999S07A findL999s07aByMainId(String mainId) {
		return l999s07aDao.findByMainId(mainId);
	}
	
	@Override
	public List<L999M01C> findL999M01CByOids(String[] oids) {
		return l999m01cDao.findByOids(oids);
	}
	
	@Override
	public L140M01A findL140M01ABymainId(String mainId) {
		return l140m01aDao.findByMainId(mainId);
	}
	
	@Override
	public L120M01A findL120M01ABymainId(String mainId) {
		return l120m01aDao.findByMainId(mainId);
	}
	
	@Override
	public L120S01A findL120s01aByUniqueKey(String mainId, String custId, String dupNo) {
		return l120s01aDao.findByUniqueKey(mainId, custId, dupNo);
	}
	
	@Override
	public L120S01B findL120s01bByUniqueKey(String mainId, String custId, String dupNo) {
    	return l120s01bDao.findByUniqueKey(mainId, custId, dupNo);
	}
	
	@Override
	public List<L140M01C> findL140m01cListByMainId(String mainId) {
		return l140m01cDao.findByMainId(mainId);
	}
	
	@Override
	public L140M01C findL140m01cByUniqueKey(String mainId, String loanTP) {
		return l140m01cDao.findByUniqueKey(mainId, loanTP);
	}
	
	@Override
	public List<L140M01D> findL140m01dByMainIdAndLmtTypeAndSubject(String mainId, String lmtType,
			String subject) {
		return l140m01dDao.findByMainIdAndLmtTypeAndSubject(mainId, lmtType, subject);
	}
	
	@Override
	public List<L140M01F> findL140m01fByMainId(String mainId) {
		return l140m01fDao.findByMainId(mainId);
	}
	
	@Override
	public List<L140M01N> findL140m01nByMainIdAndRateSeqOrderBy(String mainId, Integer rateSeq) {
		return l140m01nDao.findByMainIdAndRateSeqOrderByWithGrid(mainId, rateSeq);
	}
	
	@Override
	public L140M01H findL140m01hByUniqueKey(String mainId, Integer rateSeq) {
		return l140m01hDao.findByUniqueKey(mainId, rateSeq);
	}
}
