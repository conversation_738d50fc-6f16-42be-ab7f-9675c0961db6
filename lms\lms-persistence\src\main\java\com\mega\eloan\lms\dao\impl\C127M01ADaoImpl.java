/* 
 * C127M01ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.C127M01ADao;
import com.mega.eloan.lms.model.C127M01A;

import javax.persistence.Query;

/** 分組授權金額控管表 **/
@Repository
public class C127M01ADaoImpl extends LMSJpaDao<C127M01A, String>
	implements C127M01ADao {

	@Override
	public C127M01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C127M01A> findAll() {
//		Query query = entityManager
//				.createQuery("SELECT * FROM LMS.C127M01A");
//		List<C127M01A> list = query.getResultList();
		ISearch search = createSearchTemplete();
		search.setMaxResults(Integer.MAX_VALUE);
		List<C127M01A> list = createQuery(C127M01A.class, search)
				.getResultList();
		return list;
	}

	@Override
	public C127M01A findByOwnBrId(String ownBrId){
		ISearch search = createSearchTemplete();
		List<C127M01A> list = null;
		if (ownBrId != null){
			search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", ownBrId);
			return findUniqueOrNone(search);
		}
		return null;
	}
}