package com.mega.eloan.lms.eloandb.service.impl;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.sql.Types;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.dao.BELDFM01Dao;
import com.mega.eloan.common.dao.BELDFM02Dao;
import com.mega.eloan.common.enums.DocAuthTypeEnum;
import com.mega.eloan.common.exception.GWException;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.jdbc.EloanColumnMapRowMapper;
import com.mega.eloan.common.model.BELDFM01;
import com.mega.eloan.common.model.BELDFM02;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.context.CapParameter;
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;

@Service("eloandbBASEService")
public class EloandbBASEServiceImpl extends AbstractEloandbJdbc implements
		EloandbBASEService {

	@Resource(name = "eLoanSqlStatement")
	CapParameter sqlp;

	@Resource
	BELDFM01Dao beldfm01Dao;
	@Resource
	BELDFM02Dao beldfm02Dao;

	public int update(String sql) {
		return this.getJdbc().update(sql, new Object[] {});
	}

	@Override
	public int update(String sql, Object... objects) {
		return this.getJdbc().update(sql, objects);
	}

	private void _batchUpdate(String sqlId, int[] sqlTypes,
			final List<Object[]> batchValues) {
		this.getJdbc().batchUpdate(sqlId, sqlTypes, batchValues);
	}

	public List<Map<String, Object>> findL120M01A_MaxCaseSeq(String arg) {
		return this.getJdbc().queryForList("L120M01A.getMaxCaseSeq",
				new Object[] { arg });
	}

	@Override
	public List<Map<String, Object>> findC140M04A_Natural(String custId,
			String dupNo) {
		return this.getJdbc().queryForList("C140M04A.selByCustIdAndDupNo",
				new Object[] { custId, dupNo });
	}

	@Override
	public List<Map<String, Object>> findC140M04B_Corporate(String custId,
			String dupNo) {
		return this.getJdbc().queryForList("C140M04B.selByCustIdAndDupNo",
				new Object[] { custId, dupNo });
	}

	@Override
	public List<Map<String, Object>> findC140M01A_selUpload(
			String... cesMainIds) {
		String cesMainIdParams = Util.genSqlParam(cesMainIds);
		return this.getJdbc().queryForListByCustParam("C140M01A.selUpload",
				new Object[] { cesMainIdParams }, cesMainIds);
	}

	@Override
	public List<Map<String, Object>> findC120M01A_selUpload(
			String... cesMainIds) {
		String cesMainIdParams = Util.genSqlParam(cesMainIds);
		return this.getJdbc().queryForListByCustParam("C120M01A.selUpload",
				new Object[] { cesMainIdParams }, cesMainIds);
	}

	@Override
	public List<Map<String, Object>> findC140M01A_selCustData1(String mainId) {
		return this.getJdbc().queryForList("C140M01A.selCustData1",
				new Object[] { mainId });
	}

	@Override
	public List<Map<String, Object>> findC140JSON_selCustData2(String mainId) {
		return this.getJdbc().queryForList("C140JSON.selCustData2",
				new Object[] { mainId });
	}

	@Override
	public List<Map<String, Object>> findC120M01A_selCustData3(String mainId) {
		return this.getJdbc().queryForList("C120M01A.selCustData3",
				new Object[] { mainId });
	}

	@Override
	public List<Map<String, Object>> findC120M01A_selMainId(String caseBrId,
			String custId) {
		return this.getJdbc().queryForList("C120M01A.selMainId",
				new Object[] { caseBrId, custId });
	}

	@Override
	public List<Map<String, Object>> findC120M01A_selMainId1(String caseBrId,
			String custId, String dupNo) {
		return this.getJdbc().queryForList(
				"C120M01A.selMainId1",
				new Object[] { caseBrId, custId, dupNo, caseBrId, custId,
						dupNo, custId, dupNo });
	}

	@Override
	public List<Map<String, Object>> findC120M01A_selMainId2(String caseBrId,
			String custId, String dupNo) {
		return this.getJdbc().queryForList("C120M01A.selMainId2",
				new Object[] { caseBrId, custId, dupNo });
	}

	@Override
	public List<Map<String, Object>> findC120M01A_selMainId2s(String caseBrId,
			String mainId1, String mainId2) {
		return this.getJdbc().queryForList("C120M01A.selMainId2s",
				new Object[] { caseBrId, mainId1, mainId2 });
	}

	@Override
	public List<Map<String, Object>> findC120M01A_selMainId2sd(String caseBrId,
			String mainId1, String mainId2) {
		return this.getJdbc().queryForList("C120M01A.selMainId2sd",
				new Object[] { caseBrId, mainId1, mainId2 });
	}

	@Override
	public List<Map<String, Object>> findC120M01A_selMainId2ss(String caseBrId) {
		return this.getJdbc().queryForList("C120M01A.selMainId2ss",
				new Object[] { caseBrId });
	}

	@Override
	public List<Map<String, Object>> findC120M01A_selMainIda(String caseBrId,
			String mainId1, String mainId2) {
		return this.getJdbc().queryForList("C120M01A.selMainIda",
				new Object[] { caseBrId, mainId1, mainId2 });
	}

	@Override
	public List<Map<String, Object>> findC120M01A_selMainIdaForCls(
			String caseBrId, String mainId1, String mainId2) {
		return this.getJdbc().queryForList("C120M01A.selMainIdaForCLS",
				new Object[] { caseBrId, mainId1, mainId2 });
	}

	@Override
	public List<Map<String, Object>> findC120M01A_selMainId2sForCls(
			String caseBrId, String mainId1, String mainId2) {
		return this.getJdbc().queryForList("C120M01A.selMainId2sForCLS",
				new Object[] { caseBrId, mainId1, mainId2 });
	}

	@Override
	public List<Map<String, Object>> findC120M01A_selMainId2sdForCls(
			String caseBrId, String mainId1, String mainId2) {
		return this.getJdbc().queryForList("C120M01A.selMainId2sdForCLS",
				new Object[] { caseBrId, mainId1, mainId2 });
	}

	@Override
	public Map<String, Object> findC120M01A_bizModeForLrs(String brNo,
			String custId, String dupNo) {
		return this.getJdbc().queryForMap("C120M01A.sel_bizModeForLrs",
				new String[] { brNo, custId, dupNo, brNo, custId, dupNo });
	}

	@Override
	public List<Map<String, Object>> findC120M01A_selMainIdb(String caseBrId,
			String custId) {
		return this.getJdbc().queryForList("C120M01A.selMainIdb",
				new Object[] { caseBrId, custId });
	}

	@Override
	public List<Map<String, Object>> findC120M01A_selMainIdc(String caseBrId) {
		return this.getJdbc().queryForList("C120M01A.selMainIdc",
				new Object[] { caseBrId });
	}

	@Override
	public List<Map<String, Object>> findCms_selMainId(String branchId) {
		return this.getJdbc().queryForList("C100M01.selMainId",
				new Object[] { branchId });
	}

	@Override
	public List<Map<String, Object>> findCms_selMainId2(String mainId) {
		return this.getJdbc().queryForList("C100M01.selMainId2",
				new Object[] { mainId, mainId });
	}

	@Override
	public List<Map<String, Object>> findCms_selMainId3(String collTyp1) {
		return this.getJdbc().queryForList("C100M01.selMainId3",
				new Object[] { collTyp1 });
	}

	@Override
	public List<Map<String, Object>> findC120M01C_selL120s01e1(String mainId) {
		return this.getJdbc().queryForList("C120M01C.selL120s01e1",
				new Object[] { mainId });
	}

	@Override
	public List<Map<String, Object>> findC120M01C_selYear1(String mainId) {
		return this.getJdbc().queryForList("C120M01C.selYear1",
				new Object[] { mainId });
	}

	@Override
	public List<Map<String, Object>> findC120M01C_selL120s01e2(String mainId) {
		return this.getJdbc().queryForList("C120M01C.selL120s01e2",
				new Object[] { mainId }, 0, 3);
	}

	@Override
	public List<Map<String, Object>> findC120M01C_selYear2(String mainId) {
		return this.getJdbc().queryForList("C120M01C.selYear2",
				new Object[] { mainId }, 0, 3);
	}

	@Override
	public List<Map<String, Object>> findC120JSON_selL120s01f1(String mainId) {
		return this.getJdbc().queryForList("C120JSON.selL120s01f1",
				new Object[] { mainId });
	}

	@Override
	public List<Map<String, Object>> findC120JSON_selL120s05a(String mainId) {
		return this.getJdbc().queryForList("C120JSON.selL120s05a",
				new Object[] { mainId });
	}

	@Override
	public List<Map<String, Object>> findC140SFFF_selFfbody(String mainId) {
		return this.getJdbc().queryForList("C140SFFF.selFfbody",
				new Object[] { mainId });
	}

	@Override
	public List<Map<String, Object>> findL170BquotaAmtBymainId(String mainId) {
		return this.getJdbc().queryForList("L170M01B.selquotaAmtBymainId",
				new Object[] { mainId });
	}

	@Override
	public List<Map<String, Object>> findL170BquotaAmtBymainId2(String mainId) {
		return this.getJdbc().queryForList("L170M01B.selquotaAmtBymainId2",
				new Object[] { mainId });
	}

	@Override
	public List<Map<String, Object>> findLc241m01bQuotaAmtBymainId(String mainId) {
		return this.getJdbc().queryForList("L241M01B.selquotaAmtBymainId",
				new Object[] { mainId });
	}

	@Override
	public List<Map<String, Object>> findLc241m01bQuotaAmtBymainId2(
			String mainId) {
		return this.getJdbc().queryForList("L241M01B.selquotaAmtBymainId2",
				new Object[] { mainId });
	}

	@Override
	public List<Map<String, Object>> findC140M01A_selL120s01g(String mainId) {
		return this.getJdbc().queryForList("C140M01A.selL120s01g",
				new Object[] { mainId, mainId, mainId });
	}

	@Override
	public List<Map<String, Object>> findCESF101A01AJoinCESF101M01A(
			String custId, String dupNo, String brno) {

		return this.getJdbc().queryForList("CESF101.query",
				new Object[] { brno, custId, dupNo });
	}

	@Override
	public List<Map<String, Object>> CESF101S01ABymaiinId(String mainId) {

		return this.getJdbc().queryForList("CESF101S01A.queryBymainId",
				new Object[] { mainId });
	}

	@Override
	public List<Map<String, Object>> CESF101S01AByMainIdSubNo(String mainId,
			String subNo1, String subNo2, String subNo3) {
		return this.getJdbc().queryForList("CESF101S01A.queryBymainIdSubNo",
				new Object[] { mainId, subNo1, subNo2, subNo3 });
	}

	@Override
	public Map<String, Object> CESF101S01BBymainIdAndratioNo(String mainId,
			String ratioNo) {

		return this.getJdbc().queryForMap(
				"CESF101S01B.queryBymainIdandRatioNo",
				new Object[] { mainId, ratioNo });
	}

	@Override
	public Map<String, Object> getL170M01AEData(String custId, String dupNo,
			Date retrialDate) {

		return this.getJdbc().queryForMap("L170M01.getL170M01AEData",
				new Object[] { custId, dupNo, retrialDate });
	}

	@Override
	public List<Map<String, Object>> LMS491selByBranch(String branch) {
		return this.getJdbc().queryForList("LMS.LMS491selByBranch",
				new Object[] { branch });
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List findBusi(String mainId) {
		return this.getJdbc().queryForList("C140SDSC.selBusi",
				new Object[] { mainId });
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List selPctitle(String mainId) {
		return this.getJdbc().queryForList("C140M04A.selPctitle",
				new Object[] { mainId });
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List selCh6_lstock_inv(String mainId) {
		return this.getJdbc().queryForList("C140M04A.selCh6_lstock_inv",
				new Object[] { mainId });
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List selCh6_lstock_inv_JSON(String mainId) {
		return this.getJdbc().queryForList("C140M04A.selCh6_lstock_inv_JSON",
				new Object[] { mainId });
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List selCh6_lstock_inv_VAL(String mainId) {
		return this.getJdbc().queryForList("C140M04A.selCh6_lstock_inv_VAL",
				new Object[] { mainId });
	}

	@Override
	public List<Map<String, Object>> selCh6_LLand_inv(String mainId) {
		return this.getJdbc().queryForList("C140M04A.selCh6_LLand_inv",
				new Object[] { mainId });
	}

	@Override
	public Map<String, Object> findC120M01A_selOrderByUpdateTime(
			String caseBrId, String custId, String dupNo) {
		return this.getJdbc().queryForMap("C120M01A.selOrderByUpdateTime",
				new Object[] { caseBrId, custId, dupNo });
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List C140M01A_selCustname(String mainId) {
		return this.getJdbc().queryForList("C140M01A.selCustname",
				new Object[] { mainId });
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List C140M01A_selCustname2(String mainId) {
		return this.getJdbc().queryForList("C140M01A.selCustname2",
				new Object[] { mainId });
	}

	@Override
	public List<Map<String, Object>> C120M01A_selCustname(String mainId) {
		return this.getJdbc().queryForList("C120M01A.selCustname",
				new Object[] { mainId });
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List C100M01_selCustname(String mainId) {
		return this.getJdbc().queryForList("C100M01.selCustname",
				new Object[] { mainId });
	}

	@Override
	public List<Map<String, Object>> findlrDatellrDate(String custId,
			String dupNo, String brNo) {
		return this.getJdbc().queryForList(
				"L180M01BandL170M01A.findByPk",
				new Object[] { brNo, custId, dupNo, brNo, custId, dupNo, brNo,
						custId, dupNo });
	}

	@Override
	public List<Map<String, Object>> find170And180ByCntrno411ovs(
			String custId411ovs, String dupNo411ovs, String cntrno411ovs,
			Date cycmn411ovs) {
		return this.getJdbc().queryForList(
				"L170m01bJoinL180m01b.selByCntrno411ovs",
				new Object[] { custId411ovs, dupNo411ovs, cntrno411ovs,
						cycmn411ovs });
	}

	@Override
	public List<Map<String, Object>> find170And180ByCustId411ovs(
			String custId411ovs, String dupNo411ovs, Date cycmn411ovs) {
		return this.getJdbc().queryForList(
				"L170m01bJoinL180m01b.selByCustid411ovs",
				new Object[] { custId411ovs, dupNo411ovs, cycmn411ovs });
	}

	@Override
	public List<Map<String, Object>> findChkyn(String mainId) {
		return this.getJdbc().queryForList(
				"LMSMODEL.selChkyn",
				new Object[] { mainId, mainId, mainId, mainId, mainId, mainId,
						mainId, mainId, mainId });
	}

	@Override
	public List<Map<String, Object>> findCLSChkyn(String mainId) {
		return this.getJdbc()
				.queryForList(
						"CLSMODEL.selChkyn",
						new Object[] { mainId, mainId, mainId, mainId, mainId,
								mainId });
	}

	@Override
	public String C140M01A_selCesOid(String cesMainId) {
		String oid = "";
		Map<String, Object> map = this.getJdbc().queryForMap(
				"C140M01A.selCesOid", new Object[] { cesMainId });
		if (!map.isEmpty()) {
			oid = Util.trim(map.get("OID"));
		}
		return oid;
	}

	@Override
	public void C140M01A_copy(String mainId) {
		this.getJdbc().update("C140M01A.copy", new Object[] { mainId, mainId });
	}

	@Override
	public void C140M04A_copy(String mainId, String cesMainId) {
		this.getJdbc().update("C140M04A.copy",
				new Object[] { mainId, mainId, cesMainId });
	}

	@Override
	public void C140M04B_copy(String mainId, String cesMainId) {
		this.getJdbc().update("C140M04B.copy",
				new Object[] { mainId, mainId, cesMainId });
	}

	@Override
	public List<Map<String, Object>> F101M01A_selFss(String caseBrId,
			String custId, String dupNo) {
		return this.getJdbc().queryForList("F101M01A.selFss",
				new Object[] { caseBrId, custId, dupNo });
	}

	@Override
	public void C140M07A_copy(String mainId, String cesMainId, String tab) {
		this.getJdbc().update("C140M07A.copy",
				new Object[] { mainId, mainId, cesMainId, tab });
	}

	@Override
	public void C140S07A_copy(String mainId, String cesMainId) {
		this.getJdbc().update("C140S07A.copy",
				new Object[] { mainId, cesMainId });
	}

	@Override
	public void C140JSON_copy(String mainId, String cesMainId, String tab) {
		this.getJdbc().update("C140JSON.copy",
				new Object[] { mainId, mainId, cesMainId, tab });
	}

	@Override
	public void C140SDSC_copy(String mainId, String cesMainId, String tab) {
		this.getJdbc().update("C140SDSC.copy",
				new Object[] { mainId, mainId, cesMainId, tab });
	}

	@Override
	public void BDOCFILE_copy(String mainId, String cesMainId) {
		this.getJdbc().update("BDOCFILE.copy",
				new Object[] { mainId, cesMainId });
	}

	@Override
	public void C140SFFF_copy(String mainId, String cesMainId, String tab) {
		this.getJdbc().update("C140SFFF.copy",
				new Object[] { mainId, mainId, cesMainId, tab });
	}

	@Override
	public void BRELATED_copy(String mainId) {
		this.getJdbc().update("BRELATED.copy", new Object[] { mainId });
	}

	@Override
	public void C140M01A_updateA1(String cesOid, String mainId) {
		this.getJdbc().update(
				"C140M01A.updateA1",
				new Object[] { cesOid, cesOid, cesOid, cesOid, cesOid, cesOid,
						cesOid, cesOid, cesOid, cesOid, mainId });
	}

	@Override
	public void C140M01A_update61(String cesOid, String mainId) {
		this.getJdbc().update(
				"C140M01A.update61",
				new Object[] { cesOid, cesOid, cesOid, cesOid, cesOid, cesOid,
						mainId });
	}

	@Override
	public void C140M01A_update51(String cesOid, String mainId) {
		this.getJdbc().update("C140M01A.update51",
				new Object[] { cesOid, cesOid, cesOid, mainId });
	}

	@Override
	public void C140M01A_deleteA1(String mainId) {
		this.getJdbc().update("C140M01A.deleteA1", new Object[] { mainId });
	}

	@Override
	public void C140M01A_delete61(String mainId) {
		this.getJdbc().update("C140M01A.delete61", new Object[] { mainId });
	}

	@Override
	public void C140M04A_delete(String mainId) {
		this.getJdbc().update("C140M04A.delete", new Object[] { mainId });
	}

	@Override
	public void C140M04B_delete(String mainId) {
		this.getJdbc().update("C140M04B.delete", new Object[] { mainId });
	}

	@Override
	public void C140M07A_delete(String mainId) {
		this.getJdbc().update("C140M07A.delete", new Object[] { mainId });
	}

	@Override
	public void C140S07A_delete(String mainId) {
		this.getJdbc().update("C140S07A.delete", new Object[] { mainId });
	}

	@Override
	public void C140JSON_delete(String mainId, String tab) {
		this.getJdbc().update("C140JSON.delete", new Object[] { mainId, tab });
	}

	@Override
	public void C140SDSC_delete(String mainId, String tab) {
		this.getJdbc().update("C140SDSC.delete", new Object[] { mainId, tab });
	}

	@Override
	public void C140SFFF_delete(String mainId, String tab) {
		this.getJdbc().update("C140SFFF.delete", new Object[] { mainId, tab });
	}

	@Override
	public void deleteL784s07(String year, String month, String mainId) {

		this.getJdbc().update("L784s07.delete",
				new Object[] { year, month, mainId });
	}

	@Override
	public Map<String, Object> selL784m01abyRPTTYPEYear(String rpttype,
			String year) {

		return this.getJdbc().queryForMap("L784m01a.selByRPTTYPEYear",
				new Object[] { rpttype, year });
	}

	@Override
	public List<Map<String, Object>> selL784s07byBrnoYearMonth(String year,
			String month, String mainId) {

		return this.getJdbc().queryForList("L784s07.selSumByYearMonth",
				new Object[] { year, month, mainId });
	}

	@Override
	public List<Map<String, Object>> selL784s07byBrnoYearMonthBrNo(String brNo,
			String year, String month, String mainId) {

		return this.getJdbc().queryForList("L784s07.selSumByYearMonthBrNo",
				new Object[] { brNo, year, month, mainId });
	}

	@Override
	public List<Map<String, Object>> findL120M01AJoinL120A01A_selAreaSendInfo(
			String date, String brNo) {
		return this.getJdbc().queryForList(
				"L120M01AJoinL120A01A.selByBaseDate",
				new Object[] { brNo, date });
	}

	@Override
	public List<Map<String, Object>> findforNewReportType2ByBrNo(
			String ovUnitNo2, String docStatusCode, String docKind,
			String startDate, String endDate, String docStatusCode2,
			String typeCd1, String typeCd2) {
		List<Object> params = new ArrayList<Object>();
		// J-108-0042_05097_B1001 Web e-Loan企金授信修改海外分行「已敘做案件清單」之擷取規則
		// J-110-0403_05097_B1001
		// 新增按月提供近一年(上年度1月至今年度最近月份)分行授權內企金業務「已敘做授信案件清單」月報予稽核處
		String condition_caseBrId = "";
		String condition_docType = ""; // 1企金 2個金
		String condition_typCd = "";
		if (Util.equals(Util.trim(ovUnitNo2), "")) {
			// J-110-0403_05097_B1001
			// 新增按月提供近一年(上年度1月至今年度最近月份)分行授權內企金業務「已敘做授信案件清單」月報予稽核處
			// 整批模式-FOR稽核處批次傳送CSV
			condition_caseBrId = "";
			condition_docType = "AND L120MA.DOCTYPE = '1' "; // 送稽核處只要企金業務

			params.add(docStatusCode);
			params.add(startDate);
			params.add(endDate);
			params.add(startDate);
			params.add(endDate);
			params.add(docStatusCode2);

			if (Util.equals(typeCd1, "") && Util.equals(typeCd2, "")) {
				condition_typCd = "";
			} else {
				condition_typCd = "AND (L120MA.TYPCD = ? OR L120MA.TYPCD = ?)"; // 稽核只要國內(DBU
				// OBU)
				params.add(typeCd1);
				params.add(typeCd2);
			}

		} else {
			// 分行模式-FOR分行報表
			condition_caseBrId = "AND L120MA.CASEBRID = ? ";
			params.add(ovUnitNo2);
			// 海外每月產生的已敘做案件清單有包含消金
			condition_docType = "AND (L120MA.TYPCD = '5' OR (L120MA.TYPCD != '5' AND L120MA.DOCTYPE = '1'))";

			params.add(docStatusCode);
			params.add(startDate);
			params.add(endDate);
			params.add(startDate);
			params.add(endDate);
			params.add(docStatusCode2);

			condition_typCd = "AND (L120MA.TYPCD = ? OR L120MA.TYPCD = ?)";

			params.add(typeCd1);
			params.add(typeCd2);
		}

		return this.getJdbc().queryForAllListByCustParam(
				"L120M01AJoinL140M01A.selByBrNo",
				new Object[] { condition_caseBrId, condition_docType,
						condition_typCd }, params.toArray(new Object[0]));

	}

	@Override
	public List<Map<String, Object>> findforNewReportType2ByAccGroup(
			String ovUnitNo2, String docStatusCode, String docKind,
			String startDate, String endDate, String docStatusCode2,
			String typeCd1, String typeCd2) {
		// J-108-0042_05097_B1001 Web e-Loan企金授信修改海外分行「已敘做案件清單」之擷取規則
		return this.getJdbc().queryForListWithMax(
				"L120M01AJoinL140M01A.selByAccGroup",
				new Object[] { ovUnitNo2,
						docStatusCode, // docKind,
						startDate, endDate, startDate, endDate, docStatusCode2,
						typeCd1, typeCd2 });
	}

	/**
	 * J-112-0JJJ_05097_B1001 Web e-Loan日本地區分行簽報書新增管理行授權內案件權限及相關修改
	 * 
	 * @param docStatusCode
	 * @param country
	 * @param ovUnitNo2
	 * @param startDate
	 * @param endDate
	 * @param docStatusCode2
	 * @param typeCd1
	 * @param typeCd2
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findforNewReportType2ByCountryHead(
			String docStatusCode, String country, String ovUnitNo2,
			String startDate, String endDate, String docStatusCode2,
			String typeCd1, String typeCd2) {
		// J-108-0042_05097_B1001 Web e-Loan企金授信修改海外分行「已敘做案件清單」之擷取規則
		return this.getJdbc().queryForListWithMax(
				"L120M01AJoinL140M01A.selByCountryHead",
				new Object[] { docStatusCode, country, ovUnitNo2, ovUnitNo2,
						startDate, endDate, startDate, endDate, docStatusCode2,
						typeCd1, typeCd2 });
	}

	@Override
	public List<Map<String, Object>> findforNewReportType2ByArea(
			String ovUnitNo2, String docStatusCode, String docStatusCode2,
			String startDate, String endDate) {
		// J-108-0042_05097_B1001 Web e-Loan企金授信修改海外分行「已敘做案件清單」之擷取規則
		return this.getJdbc().queryForListWithMax(
				"L120M01AJoinL140M01A.selByArea",
				new Object[] { docStatusCode, docStatusCode2, ovUnitNo2,
						startDate, endDate, startDate, endDate });
	}

	// @Override
	// public int F101M01A_copy(String mainIds, String contents) {
	// return this.getJdbc().updateByCustParam("F101M01A.copy",
	// new Object[] { contents }, new Object[] { mainIds });
	// }
	//
	// @Override
	// public int F101M01A_delete(String oids, String contents) {
	// return this.getJdbc().updateByCustParam("F101M01A.delete",
	// new Object[] { contents }, new Object[] { oids });
	// }
	//
	// @Override
	// public int F101S01A_copy(String mainIds, String contents) {
	// return this.getJdbc().updateByCustParam("F101S01A.copy",
	// new Object[] { contents }, new Object[] { mainIds });
	// }
	//
	// @Override
	// public int F101S01A_delete(String oids, String contents) {
	// return this.getJdbc().updateByCustParam("F101S01A.delete",
	// new Object[] { contents }, new Object[] { oids });
	// }
	//
	// @Override
	// public int F101S01B_copy(String mainIds, String contents) {
	// return this.getJdbc().updateByCustParam("F101S01B.copy",
	// new Object[] { contents }, new Object[] { mainIds });
	// }
	//
	// @Override
	// public int F101S01B_delete(String oids, String contents) {
	// return this.getJdbc().updateByCustParam("F101S01B.delete",
	// new Object[] { contents }, new Object[] { oids });
	// }
	//
	// @Override
	// public void deleteTrust(String mainId, String custId, String dupNo) {
	// this.getJdbc().update("L120S01C.delTrust",
	// new Object[] { mainId, custId, dupNo });
	// }

	@Override
	public void L120S01E_delByMainIdCustData(String mainId, String custId,
			String dupNo) {
		this.getJdbc().update("L120S01E.delByMainIdCustData",
				new Object[] { mainId, custId, dupNo });
	}

	@Override
	public List<Map<String, Object>> L140M01A_selLihai(String custId,
			String dupNo, String caseBrid) {
		return this.getJdbc().queryForList("L140M01A.selLihai",
				new Object[] { custId, dupNo, caseBrid });
	}

	@Override
	public List<Map<String, Object>> findL140M01AByMainId(String mainId) {
		return this.getJdbc().queryForList("L140M01A.selByMainId",
				new Object[] { mainId, mainId });
	}

	@Override
	public List<Map<String, Object>> findL140M01AInfoByMainId(String mainId) {
		return this.getJdbc().queryForList("L140M01A.selInfoByMainId",
				new Object[] { mainId, mainId });
	}

	@Override
	public List<Map<String, Object>> findC140M01AByC120M01C(String mainId) {
		return this.getJdbc().queryForList("L140M01A.findByC120M01C",
				new Object[] { mainId });
	}

	@Override
	public void deleteL170M01EByTypeCN(String mainId) {
		this.getJdbc().update("L170M01E.deleteFromCN", new Object[] { mainId });
	}

	@Override
	public void updateForNck(String newNCkdMemo, Date newNextnwdt,
			String mainIdA, String mainIdB) {
		this.getJdbc().update("L180M01B.updateNewNck",
				new Object[] { newNCkdMemo, mainIdA, mainIdB });
	}

	@Override
	public void insertFlowWinst(String instId) {
		this.getJdbc().update("FLOW.insertWinst",
				new Object[] { instId, instId });

	}

	@Override
	public void insertFlowSeq(String instId) {
		this.getJdbc()
				.update("FLOW.insertSeq", new Object[] { instId, instId });

	}

	@Override
	public void delFlowWinst(String instId) {
		this.getJdbc().update("FLOW.delWinst", new Object[] { instId, instId });

	}

	@Override
	public void delFlowSeq(String instId) {
		this.getJdbc().update("FLOW.delSeq", new Object[] { instId, instId });

	}

	@Override
	public void insertFolwNewSeq(String instId) {
		this.getJdbc().update("FLOW.insertNewSeq",
				new Object[] { instId, instId });

	}

	@Override
	public void insertFolwNewSeqFor180M01A(String instId) {
		this.getJdbc().update("FLOW.insertNewSeqFor180M01A",
				new Object[] { instId, instId });

	}

	@Override
	public void delNowFlowWinst(String instId) {
		this.getJdbc().update("FLOW.delNowWinst", new Object[] { instId });

	}

	@Override
	public void delNowFlowSeq(String instId) {
		this.getJdbc().update("FLOW.delNowSeq", new Object[] { instId });

	}

	@Override
	public int findL140M01EByCustIdAndDupNoAndCntrno(String custId,
			String dupNo, String cntrNo) {
		return this.getJdbc().queryForInt(
				"L140M01E.findByCustIdAndDupNoAndCntrno",
				new Object[] { custId, dupNo, cntrNo });
	}

	// @Override
	// public List<Map<String, Object>> findL141M01ADistinctByCustIdAndDupNo(
	// String custId, String dupNo, String ownBrid) {
	// return this.getJdbc().queryForListByCustParam(
	// "L141M01A.findDistinctByCustIdAndDupNo", new Object[] {},
	// new Object[] {});
	// }

	@Override
	public Map<String, Object> selNowFlowWinst(String instid) {
		return this.getJdbc().queryForMap("FLOW.selNowWinst",
				new Object[] { instid });
	}

	@Override
	public List<Map<String, Object>> findL170M10AJoin(String docStatus,
			String brNo, String retrialDate1, String retrialDate2,
			String custId, String custName, ISearch search) {
		String str = Util.getOrderData(search.getOrderBy());
		StringBuffer sql = new StringBuffer();
		List<Object> params = new ArrayList<Object>();
		params.add(brNo);
		params.add(docStatus);
		if (!"".equals(Util.trim(retrialDate1))) {
			sql.append(" and L170M.retrialDate >= ? ");
			params.add(retrialDate1);
		}
		if (!"".equals(Util.trim(retrialDate2))) {
			sql.append(" and L170M.retrialDate <= ? ");
			params.add(retrialDate2);
		}

		if (!"".equals(Util.trim(custId))) {
			sql.append(" and L170M.custId = ? ");
			params.add(custId);
		}
		// J-108-0163_10702_B1001 配合授審處於企金及消金「授信覆審系統」增加「戶名」搜尋欄位
		if (!"".equals(Util.trim(custName))) {
			sql.append(" and L170M.custName like ? ");
			params.add("%" + custName + "%");
		}
		return this.getJdbc().queryForListByCustParam("L170M01A.selGrid",
				new Object[] { sql.toString(), str },
				params.toArray(new Object[0]));
	}

	@Override
	public List<Map<String, Object>> findDeleteTime(String tableName, int days) {
		return this.getJdbc().queryForListByCustParam("deleteByDELETEDTIME",
				new Object[] { tableName }, new Object[] { days });
	}

	@Override
	public int deleteByMainId(String table, String... mainIds) {
		String mainIdParamsJoin = Util.genSqlParam(mainIds);
		return this.getJdbc().updateByCustParam("deleteByMainID",
				new String[] { table, mainIdParamsJoin }, mainIds);
	}

	@Override
	public int deleteBRelatedByMainId1(String table, String... mainIds) {
		String mainIdParamsJoin = Util.genSqlParam(mainIds);
		return this.getJdbc().updateByCustParam("BRelate.deleteByMainID1",
				new String[] { table, mainIdParamsJoin }, mainIds);
	}

	@Override
	public List<Map<String, Object>> findRetrialKind_in_c240m01a_raw(
			String c240m01a_mainId, String ruleNo) {
		return this.getJdbc().queryForListWithMax(
				"findRetrialKind_in_c240m01a_raw",
				new Object[] { c240m01a_mainId, "%" + ruleNo + "%" });
	}

	@Override
	public List<Map<String, Object>> findC240M01BOrderByC241M01ASEQ(
			String mainId) {
		return this.getJdbc().queryForListWithMax(
				"findC240M01BOrderByC241M01ASEQ", new Object[] { mainId });
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.eloandb.service.EloandbBASEService#findL230S01AMaxDATADATE
	 * (java.lang.String, java.lang.String)
	 */
	@Override
	public Map<String, Object> findL230S01AMaxDATADATE(String srcMainId,
			String docstatus) {
		return this.getJdbc().queryForMap("L23S01A.findBySrcMainId",
				new Object[] { srcMainId, docstatus, srcMainId, docstatus });
	}

	@Override
	public Map<String, Object> findMaxProjectNo(String mainId) {
		return this.getJdbc().queryForMap("C241M01A.maxProjectNo",
				new Object[] { mainId });
	}

	@Override
	public void updateC241m01a(Date retrialdate, String mainId) {
		this.getJdbc().update("retrialdate.updateC241m01a",
				new Object[] { retrialdate, mainId });
	}

	@Override
	public void deleteL140M01JByL120S01(String mainId, String custId,
			String dupNo) {
		this.getJdbc().update("L140M01J.deleteByL120S01A",
				new Object[] { custId, dupNo, mainId });

	}

	@Override
	public Page<Map<String, Object>> findCESL120S01A(ISearch search,
			String brId, String custId, String dupNo, String cntrNo) {
		return this.getJdbc().queryForPage(
				search,
				"C120S01A.getRatio",
				new Object[] { brId, custId, dupNo, cntrNo, brId, custId,
						dupNo, cntrNo });
	}

	@Override
	public List<Map<String, Object>> selL120m01eCls(String[] docOids,
			String mainId) {
		String docOidParams = Util.genSqlParam(docOids);

		List<String> params = new ArrayList<String>();
		params.addAll(Arrays.asList(docOids));
		params.add(mainId);
		return this.getJdbc().queryForListByCustParam("selL120m01eCls",
				new Object[] { docOidParams }, params.toArray(new String[0]));
	}

	@Override
	// 取得 敘做無自用住宅購屋放款明細表 資料
	public List<Map<String, Object>> queryLoanList(String brno,
			String dateType, String date) {
		return this.getJdbc().queryForListWithMax("queryLoanList." + dateType,
				new String[] { date, brno });
	}

	@Override
	// 取得 已敘做個人消費金融業務月報表(授權內) 資料
	public List<Map<String, Object>> queryConsumerFinance(String brno,
			String dateType, String date) {
		return this.getJdbc()
				.queryForListWithMax("queryConsumerFinance." + dateType,
						new String[] { date, brno });
	}

	@Override
	public List<Map<String, Object>> findProducts(String mainId, String cntrNo) {
		return this.getJdbc().queryForList("findProducts",
				new String[] { mainId, cntrNo });
	}

	@Override
	public List<Map<String, Object>> findBorrower(String mainId, String cntrNo) {
		return this.getJdbc().queryForList("findBorrower",
				new String[] { mainId, cntrNo });
	}

	@Override
	public List<Map<String, Object>> findGuarantor(String mainId, String cntrNo) {
		return this.getJdbc().queryForList("findGuarantor",
				new String[] { mainId, cntrNo, mainId, cntrNo });
	}

	@Override
	public List<Map<String, Object>> queryReject(String brno, String bgnDate,
			String endDate) {
		return this.getJdbc().queryForList("queryReject",
				new String[] { brno, bgnDate, endDate });
	}

	@Override
	public List<Map<String, Object>> selExcel(String custId, String dupNo) {
		return this.getJdbc().queryForList("L140M01A.selExcel",
				new Object[] { custId, dupNo });
	}

	/**
	 * J-113-0069_05097_B1001 Web
	 * e-Loan企金授信「主要關係戶與本行授信往來比較表」，借戶及其相關關係企業於本次有簽報者，引進本次簽報內容
	 * 
	 * @param custId
	 * @param dupNo
	 * @param caseMainId
	 * @param itemType
	 * @return
	 */
	@Override
	public List<Map<String, Object>> selExcel_forThisReport(String custId,
			String dupNo, String caseMainId, String itemType) {
		return this.getJdbc().queryForList("L140M01A.selExcelForThisReport",
				new Object[] { custId, dupNo, caseMainId, itemType });
	}

	@Override
	// 取得 分行敘做房屋貸款月報(授權內)資料(主管覆核日) LMS9511ServiceImpl-findCLS180R03Data
	public List<Map<String, Object>> queryBrnoAmt(String brno, String dateType,
			String date) {
		return this.getJdbc().queryForListWithMax("queryBrnoAmt." + dateType,
				new String[] { date, brno });
	}

	@Override
	public Map<String, Object> monthTotal(String brno, String dateType,
			String date) {
		return this.getJdbc().queryForMap("monthTotal." + dateType,
				new String[] { brno, date });
	}

	@Override
	public void delBatchTblNowRpts(String mainId) {
		this.getJdbc().update("rpt.delBatchTbl", new Object[] { mainId });
	}

	@Override
	public void batchTblCopyToRptTbl(String mainId) {
		this.getJdbc().update("rpt.insetRptTbl", new Object[] { mainId });
	}

	@Override
	public void updateNowTyperptTbl(String rptNo, String branch) {
		this.getJdbc().update("rpt.updateRptTbl",
				new Object[] { rptNo, branch });
	}

	@Override
	// public List<Map<String, Object>> findOldCaseByCutId(String docType,
	// String custId, String dupNo) {
	// return this.getJdbc().queryForList("L140M01A.queryOldCase",
	// new Object[] { custId, dupNo,docType });
	// }
	public List<Map<String, Object>> findOldCaseByCutId(String custId,
			String dupNo, String docType) {
		return this.getJdbc().queryForList("L140M01A.queryOldCaseNoOverYear",
				new Object[] { custId, dupNo, docType });
	}

	@Override
	public List<Map<String, Object>> findLastByCntrNo(String cntrNo) {
		return this.getJdbc().queryForList("L140M01Q.findLastByCntrNo",
				new Object[] { cntrNo });
	}

	@Override
	public Map<String, Object> findCLSCaseByCutIdAndCntrNoOrdByUpdateTime(
			String custId, String dupNo, String cntrNo) {
		return this.getJdbc().queryForMap(
				"L140M01A.queryCLSCaseByCutIdAndCntrNoOrdByUpdateTime",
				new Object[] { custId, dupNo, cntrNo });
	}

	@Override
	public List<Map<String, Object>> queryLMS180R02AData(String startDate,
			String endDate, String branchId) {
		return this.getJdbc().queryForListWithMax("rpt.LMS180R02A",
				new Object[] { branchId, startDate, endDate });

	}

	@Override
	public Map<String, Object> queryC180R06Data(String prodKind,
			String[] docStatus, String bgnDate, String endDate) {
		if (docStatus != null) {
			return this.getJdbc().queryForMap(
					"queryNumAndAmt",
					new Object[] { docStatus[0], docStatus[1], bgnDate,
							endDate, prodKind });
		} else {
			return this.getJdbc().queryForMap("queryAppropriation",
					new Object[] { prodKind, bgnDate, endDate });
		}
	}

	@Override
	public List<Map<String, Object>> queryLMS180R12Data(String startDate,
			String endDate) {
		return this.getJdbc().queryForListWithMax("rpt.LMS180R12",
				new Object[] { startDate, endDate, startDate, endDate });
	}

	@Override
	public List<Map<String, Object>> queryLMS180R13Data(String startDate,
			String endDate) {
		return this.getJdbc().queryForListWithMax("rpt.LMS180R13",
				new Object[] { startDate, endDate, startDate, endDate });
	}

	@Override
	public List<Map<String, Object>> queryLMS161T02Data01(String startDate,
			String endDate, String branchId) {
		return this.getJdbc().queryForListWithMax("rpt.LMS161T0201",
				new Object[] { branchId, startDate, endDate });
	}

	@Override
	public List<Map<String, Object>> queryLMS161T02Data02(String startDate,
			String endDate, String branchId) {
		return this.getJdbc().queryForListWithMax("rpt.LMS161T0202",
				new Object[] { branchId, startDate, endDate });
	}

	@Override
	public Map<String, Object> getSysParamData(String param) {
		Map<String, Object> map = this.getJdbc().queryForMap(
				"get.sysParam.Data", new Object[] { param });
		if (map == null) {
			map = new HashMap<String, Object>();
		}
		return map;
	}

	@Override
	public List<Map<String, Object>> queryL140S01ADistinctByKey(String mainId) {
		return this.getJdbc().queryForList("L140S01A.queryDISTINCTByKey",
				new Object[] { mainId });
	}

	@Override
	public List<Map<String, Object>> findL140S02ADistinctByKey(String mainId) {
		return this.getJdbc().queryForList("L140S02A.queryDISTINCTByKey",
				new Object[] { mainId });
	}

	@Override
	public Map<String, Object> findTotPreparation(String kindNo) {
		final String[] prodKind = { "28", "35", "56", "59" };
		int kind = Util.parseInt(kindNo);
		if (kind >= 4) {
			BigDecimal countNum = Util.parseBigDecimal("0");
			BigDecimal countAmt = Util.parseBigDecimal("0");
			for (int i = 0; i < prodKind.length; i++) {
				Map<String, Object> data = this.getJdbc().queryForMap(
						"findTotPreparation.lms9541v02",
						new Object[] { kindNo });
				countNum = countNum.add(Util.parseBigDecimal(data.get("NUM")));
				countAmt = countAmt.add(Util.parseBigDecimal(data.get("AMT")));
			}
			Map<String, Object> result = new HashMap<String, Object>();
			result.put("num", countNum);
			result.put("amt", countAmt);
			return result;
		} else {
			return this.getJdbc().queryForMap("findTotPreparation.lms9541v02",
					new Object[] { prodKind[kind] });
		}

	}

	@Override
	public Map<String, Object> getL180R02A918LMSRPT(String bgnDate,
			String endDate) {
		return this.getJdbc().queryForMap("getL180R02ALMSRPT",
				new Object[] { bgnDate, endDate });
	}

	@Override
	public String queryL120M01EByOid(String oid) {
		String result = "";
		Map<String, Object> data = this.getJdbc().queryForMap(
				"L120M01A.queryL120M01EByOid", new Object[] { oid });
		if (data != null) {
			result = Util.trim(data.get("CASENO"));
		}
		return result;
	}

	private static final Logger logger = LoggerFactory
			.getLogger(EloandbBASEServiceImpl.class);

	@Override
	public void doWorkUnapp(String oid, boolean isSpecail,
			List<FlowInstance> histroyFlow) {
		// 當歷史記錄不為空刪除現況檔
		if (!histroyFlow.isEmpty()) {
			logger.debug("移除現況檔");
			this.getJdbc().update("FLOW.delNowWinst", new Object[] { oid });
			// 移除現況檔 目前資料{非正常資料才有可能}
			this.getJdbc().update("FLOW.delNowSeq", new Object[] { oid });
		}

		logger.info(" 1.取得流程主檔、歷史檔的資料移至 現況檔");
		this.getJdbc().update("FLOW.insertWinst", new Object[] { oid, oid });
		logger.info(" 2.取得流程明細、歷史檔的資料移至 現況檔");
		this.getJdbc().update("FLOW.insertSeq", new Object[] { oid, oid });
		logger.info(" 5.新增一筆流程明細檔");
		if (isSpecail) {
			this.getJdbc().update("FLOW.insertNewSeqFor180M01A",
					new Object[] { oid, oid });
		} else {
			this.getJdbc().update("FLOW.insertNewSeq",
					new Object[] { oid, oid });
		}

	}

	@Override
	public void doWorkUnappByAnyDocstatus(String oid, String docstatus,
			List<FlowInstance> histroyFlow, String ownBrId) {
		// 當歷史記錄為空刪除現況檔
		if (histroyFlow.isEmpty()) {

		} else {
			this.getJdbc().update("FLOW.delNowWinst", new Object[] { oid });
			this.getJdbc()
					.update("FLOW.insertWinst", new Object[] { oid, oid });
		}
		logger.debug("移除現況檔");
		// 移除現況檔 目前資料{非正常資料才有可能}
		this.getJdbc().update("FLOW.delNowSeq", new Object[] { oid });
		logger.info(" 1.取得流程主檔、歷史檔的資料移至 現況檔");

		logger.info(" 5.新增一筆流程明細檔");
		this.getJdbc().update("FLOW.insertAnyDocstatus",
				new Object[] { oid, docstatus, ownBrId });

	}

	@Override
	public Page<Map<String, Object>> queryL140S01AForGrade_G(ISearch search,
			String caseMainId, String mainId) {
		return this.getJdbc().queryForPage(search,
				"L140S01A.queryL140S01AForGrade",
				new Object[] { mainId, mainId, caseMainId });
	}

	@Override
	public Page<Map<String, Object>> queryL140S01AForGrade_notHouseLoan(
			ISearch search, String caseMainId, String mainId) {
		return this.getJdbc().queryForPage(search,
				"L140S01A.queryL140S01AForGrade_notHouseLoan",
				new Object[] { mainId, mainId, caseMainId });
	}

	@Override
	public Page<Map<String, Object>> queryL140S01AForGrade_cardLoan(
			ISearch search, String caseMainId, String mainId) {
		return this.getJdbc().queryForPage(search,
				"L140S01A.queryL140S01AForGrade_cardLoan",
				new Object[] { mainId, mainId, caseMainId });
	}

	@Override
	public List<Map<String, Object>> queryL140S01AForGrade_G(String caseMainId,
			String mainId) {
		return this.getJdbc().queryForList("L140S01A.queryL140S01AForGrade",
				new Object[] { mainId, mainId, caseMainId });
	}

	@Override
	public List<Map<String, Object>> queryL140S01AForGrade_notHouseLoan(
			String caseMainId, String mainId) {
		return this.getJdbc().queryForList(
				"L140S01A.queryL140S01AForGrade_notHouseLoan",
				new Object[] { mainId, mainId, caseMainId });
	}

	@Override
	public List<Map<String, Object>> queryL140S01AForGrade_cardLoan(
			String caseMainId, String mainId) {
		return this.getJdbc().queryForList(
				"L140S01A.queryL140S01AForGrade_cardLoan",
				new Object[] { mainId, mainId, caseMainId });
	}

	@Override
	public List<Map<String, Object>> findC140M04A_Natural(String custId,
			String dupNo, String mainId) {
		return this.getJdbc().queryForList(
				"C140M04A.selByCustIdAndDupNoAndMainId",
				new Object[] { custId, dupNo, mainId });
	}

	@Override
	public List<Map<String, Object>> findC140M04B_Corporate(String custId,
			String dupNo, String mainId) {
		return this.getJdbc().queryForList(
				"C140M04B.selByCustIdAndDupNoAndMainId",
				new Object[] { custId, dupNo, mainId });
	}

	@Override
	public Page<Map<String, Object>> findCESL140M01A(ISearch search,
			String brId, String custId, String dupNo) {
		return this.getJdbc().queryForPage(search,
				"C140M01A.selByCustIdAndDupNoAndBrId",
				new Object[] { custId, dupNo, brId });
	}

	@Override
	public String findL140M01C_MaxSeq(String mainId) {
		Map<String, Object> queryForMap = this.getJdbc().queryForMap(
				"getL140M01CMaxSeq", new Object[] { mainId });
		int seq = Util.parseInt(queryForMap.get("SUBJSEQ")) + 1;
		return String.valueOf(seq);
	}

	@Override
	public Map<String, Object> getC140M01A_Overview(String cesMainId) {
		return this.getJdbc().queryForMap("getC140M01AOverview",
				new Object[] { cesMainId });
	}

	@Override
	public Map<String, Object> getC140M01A_ChangeInShareCapital(String cesMainId) {
		return this.getJdbc().queryForMap("getC140M01AChangeInShareCapital",
				new Object[] { cesMainId });
	}

	@Override
	public Map<String, Object> getC140M01A_History(String cesMainId) {
		return this.getJdbc().queryForMap("getC140M01AHistory",
				new Object[] { cesMainId });
	}

	@Override
	public Map<String, Object> getC140C120M01A_EstaDate(String cesMainId) {
		return this.getJdbc().queryForMap("getC140C120M01AEstaDate",
				new Object[] { cesMainId });
	}

	@Override
	public Map<String, Object> getC140M01A_Ch6_LLand_inv(String cesMainId) {
		Map<String, Object> value = new HashMap<String, Object>();

		Map<String, Object> metaData = this.getJdbc().queryForMap(
				"getC140M01ACh6_LLand_inv", new Object[] { cesMainId });

		if (metaData != null && !metaData.isEmpty()) {
			String typeCd = (String) metaData.get("TYPCD");

			if (!"1".equals(typeCd)) {
				// obu，海外徵信報告
				// ch6_LStock_inv
				String ch6_LStock_inv = (String) metaData.get("ch6_LStock_inv");
				if ("1".equals(ch6_LStock_inv)) {
					value.put("inv", "1");
					String content = "";
					Map<String, Object> ch6Data = this.getJdbc().queryForMap(
							"C140M04A.selCh6_lstock_inv",
							new Object[] { cesMainId });
					if (ch6Data != null && !ch6Data.isEmpty()) {
						content = (String) ch6Data.get("FFBODY");
					}
					value.put("invMDscr", content);
				} else {
					value.put("inv", "2");
					value.put("invMDscr", "");
				}
			} else {
				// dbu徵信報告
				String ch6_LLand_inv = (String) metaData.get("ch6_LLand_inv");
				if ("1".equals(ch6_LLand_inv)) {
					value.put("inv", "1");
					String content = "";
					Map<String, Object> ch6Data = this.getJdbc().queryForMap(
							"C140M04A.selCh6_LLand_inv",
							new Object[] { cesMainId });
					if (ch6Data != null && !ch6Data.isEmpty()) {
						String ch6jsonString = (String) ch6Data.get("JSONOB");
						JSONObject ch6 = JSONObject.fromObject(ch6jsonString);
						content = ch6.optString("ch6LLandNote");
					}
					value.put("invMDscr", content);
				} else {
					value.put("inv", "2");
					value.put("invMDscr", "");
				}
			}
		}
		return value;
	}

	@Override
	public Page<Map<String, Object>> findL140M01OByMainId(ISearch search,
			String caseMainId) {
		return this.getJdbc().queryForPage(search, "queryL140M01OByCaseMainId",
				new Object[] { caseMainId });
	}

	@Override
	public List<Map<String, Object>> findApproverForL180R02A(String mainId) {
		return this.getJdbc().queryForListWithMax("rpt.L180R02AForApprover",
				new Object[] { mainId });
	}

	@Override
	public HashMap<String, HashSet<String>> findDISTINCTCntrNoByCustId(
			HashSet<String> custIdSet, String docType) {
		HashMap<String, HashSet<String>> custIdAndCntrNo = new HashMap<String, HashSet<String>>();
		// 已產生的額度序號不在產生
		HashSet<String> cntrNos = new HashSet<String>();
		for (String custIdKey : custIdSet) {
			String custId = Util.getLeftStr(custIdKey, 10);
			String dupNo = Util.getRightStr(custIdKey, 1);
			List<Map<String, Object>> rowData = this.getJdbc().queryForList(
					"L140M01A.queryOldCase",
					new Object[] { custId, dupNo, docType });
			for (Map<String, Object> row : rowData) {
				String cntrNo = Util.trim(row.get("cntrNo"));
				String property = Util.trim(row.get("PROPERTY"));
				if (cntrNos.contains(cntrNo)) {
					continue;
				}

				// 2014-01-24增加L140M01A.useDeadline 排除掉0及1/2的已過期資料
				// 0.不再動用
				// 1.YYYY-MM-DD～YYYY-MM-DD
				// 2.自核准日起MM個月
				// 3.自簽約日起MM個月
				// 4.自首次動用日起MM個月
				// 5.其他
				String useDeadline = Util.trim(row.get("useDeadline"));
				String desp1 = Util.trim(row.get("desp1"));
				String tApproveTime = Util.trim(row.get("tApproveTime"));
				String _overDay = "";
				String _getDay = CapDate.formatDate(new Date(), "yyyy-MM-dd");
				int diff = 0;
				if ("0".equals(useDeadline)) {
					continue;
				} else if ("1".equals(useDeadline)) {
					desp1 = StringUtils.left(desp1, 10);

					if (!"".equals(desp1)) {
						_overDay = (desp1);
						_getDay = CapDate.formatDate(new Date(), "yyyy-MM-dd");

						diff = CapDate.calculateDays(_getDay, _overDay,
								"yyyy-MM-dd", "yyyy-MM-dd");
						if (diff > 0) {
							continue;
						}
					}

				} else if ("2".equals(useDeadline)) {

					Date _oDay = CapDate.parseDate(tApproveTime);
					Calendar overDay = Calendar.getInstance();
					overDay.setTime(_oDay);
					overDay.add(Calendar.MONTH, Integer.parseInt(desp1));
					_overDay = CapDate.formatDate(overDay.getTime(),
							"yyyy-MM-dd");

					// 比較二個日期大小用
					diff = CapDate.calculateDays(_getDay, _overDay,
							"yyyy-MM-dd", "yyyy-MM-dd");

					if (diff > 0) {
						continue;
					}
				}

				cntrNos.add(cntrNo);
				// 因為是用最新日期當排序，當最新的那一筆已經取消 則 不再放進來
				if ("8".equals(property)) {
					continue;
				}
				if (!custIdAndCntrNo.containsKey(custIdKey)) {
					custIdAndCntrNo.put(custIdKey, new HashSet<String>());
				}
				custIdAndCntrNo.get(custIdKey).add(cntrNo);
			}
		}

		return custIdAndCntrNo;
	}

	@Override
	public List<Map<String, Object>> selToC999m01c(String... mainIds) {

		String mainIdParams = Util.genSqlParam(mainIds);

		return this.getJdbc().queryForListByCustParam("selToC999m01c",
				new Object[] { mainIdParams }, mainIds);
	}

	@Override
	public Map<String, Object> getL140S02AByCntrNo(String cntrNo) {
		return this.getJdbc().queryForMap("getL140S02AByCntrNo",
				new Object[] { cntrNo });
	}

	@Override
	public List<String> findC102M01AByMainId(String mainId) {
		List<Map<String, Object>> rows = this.getJdbc().queryForList(
				"L140S02F.queryC102M01AMaindIds", new Object[] { mainId });

		List<String> list = new ArrayList<String>();
		for (Map<String, Object> data : rows) {
			list.add(Util.trim(data.get("REFMAINID")));
		}
		return list;
	}

	@Override
	public Map<String, BigDecimal> getSUMByPRODKIND(String itemType,
			String caseMainid) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForList(
				"getSUMByPRODKIND", new Object[] { itemType, caseMainid });
		HashMap<String, BigDecimal> map = new HashMap<String, BigDecimal>();
		for (Map<String, Object> data : rowData) {
			map.put(Util.trim(data.get("PRODKIND")),
					Util.parseBigDecimal(data.get("LOANAMT")));
		}
		return map;
	}

	@Override
	public BigDecimal getSUMByRATE_N2(String itemType, String caseMainid,
			String custId, String dupNo) {
		Map<String, Object> queryForMap = this.getJdbc().queryForMap(
				"getSUMByRATE_N2",
				new Object[] { custId, dupNo, itemType, caseMainid });
		BigDecimal result = BigDecimal.ZERO;
		if (queryForMap != null) {
			result = Util.parseBigDecimal(queryForMap.get("LOANAMT"));
		}

		return result;
	}

	@Override
	public BigDecimal getSUMByRATE_N2_V2(String itemType, String caseMainid,
			String custId, String dupNo) {
		BigDecimal result = BigDecimal.ZERO;
		for (Map<String, Object> data : this.getJdbc().queryForListWithMax(
				"getSUMByRATE_N2_V2",
				new Object[] { custId, dupNo, itemType, caseMainid })) {
			String cntrNo = Util.trim(MapUtils.getString(data, "CNTRNO"));
			String commSno = Util.trim(MapUtils.getString(data, "COMMSNO"));
			BigDecimal loanAmt = (BigDecimal) MapUtils.getObject(data,
					"LOANAMT");
			if (loanAmt == null) {
				continue;
			}
			// 處理 共用額度 的計算
			if (Util.isNotEmpty(commSno)) {
				if (Util.equals(cntrNo, commSno)) {

				} else {
					continue;
				}
			}
			result = result.add(loanAmt);
		}
		return result;
	}

	@Override
	public BigDecimal getSUMByRATE_M3(String itemType, String caseMainid,
			String custId, String dupNo) {
		Map<String, Object> queryForMap = this.getJdbc().queryForMap(
				"getSUMByRATE_M3",
				new Object[] { custId, dupNo, itemType, caseMainid });
		BigDecimal result = BigDecimal.ZERO;
		if (queryForMap != null) {
			result = Util.parseBigDecimal(queryForMap.get("LOANAMT"));
		}

		return result;
	}

	@Override
	public BigDecimal getSUMByRATE_N2ByL140S02H(String itemType,
			String caseMainid, String custId, String dupNo) {
		Map<String, Object> queryForMap = this.getJdbc().queryForMap(
				"getSUMByRATE_N2ByL140S02H",
				new Object[] { custId, dupNo, itemType, caseMainid });
		BigDecimal result = BigDecimal.ZERO;
		if (queryForMap != null) {
			result = Util.parseBigDecimal(queryForMap.get("LOANAMT"));
		}

		return result;
	}

	@Override
	public BigDecimal getSUMByRATE_M3ByL140S02H(String itemType,
			String caseMainid, String custId, String dupNo) {
		Map<String, Object> queryForMap = this.getJdbc().queryForMap(
				"getSUMByRATE_M3ByL140S02H",
				new Object[] { custId, dupNo, itemType, caseMainid });
		BigDecimal result = BigDecimal.ZERO;
		if (queryForMap != null) {
			result = Util.parseBigDecimal(queryForMap.get("LOANAMT"));
		}

		return result;
	}

	@Override
	public List<String> selByCLSUPDW() {
		List<Map<String, Object>> rows = this.getJdbc().queryForListWithMax(
		// "selectByCLSUPDW", new Object[] {}); 若無參數，應傳入 null。若傳入 new Object[]
		// {} 在 log 會有ERROR出現
				"selectByCLSUPDW", null);

		List<String> list = new ArrayList<String>();
		for (Map<String, Object> data : rows) {
			list.add(Util.trim(data.get("OID")));
		}
		return list;
	}

	@Override
	public Map<String, String> findL140M01BByL120M01AMainId(String mainId,
			String L120M01C_itemType, String L140M01B_itemType) {
		List<Map<String, Object>> rows = this.getJdbc().queryForList(
				"queryL140M01BByL120M01AMainId",
				new Object[] { mainId, L120M01C_itemType, L140M01B_itemType });
		Map<String, String> result = new HashMap<String, String>();
		for (Map<String, Object> row : rows) {
			result.put(Util.trim(row.get("CNTRNO")),
					Util.trim(row.get("ITEMDSCR")));
		}
		return result;
	}

	@Override
	public Set<String> queryL140S02AGradCustByL120M01AMainId(String mainId) {
		List<Map<String, Object>> rows = this.getJdbc().queryForList(
				"queryL140S02AGradCustByL120M01AMainId",
				new Object[] { mainId });
		HashSet<String> result = new HashSet<String>();
		for (Map<String, Object> row : rows) {
			result.add(Util.trim(row.get("CUSTKEY")));
		}
		return result;
	}

	@Override
	public List<Map<String, Object>> findMailServiceProd59_part2_list(
			String kindNo, String version) {
		String sql = "findMailServiceProd59_part2";
		if (Util.equals("v2", version)) {
			sql = "findMailServiceProd59_part2_v2";
		}
		return this.getJdbc().queryForListWithMax(sql, new Object[] { kindNo });
	}

	@Override
	public Map<String, Object> findMailServiceProd59_part3(String kindNo) {
		return this.getJdbc().queryForMap("findMailServiceProd59_part3",
				new Object[] { kindNo });
	}

	@Override
	public List<Map<String, Object>> findMailService_l140s02f_ratePlan20() {
		return this.getJdbc().queryForListWithMax(
				"findMailService_l140s02f_ratePlan20", null);
	}

	@Override
	public Map<String, Object> selCES140ByCES120MainId(String ces120MainID) {
		return this.getJdbc().queryForMap("C120M01A.selCES140ByCES120MainId",
				new Object[] { ces120MainID });
	}

	@Override
	public Map<String, Object> selCES140Ch9FinInfor1(String ces140MainID,
			String schemaKind) {
		// return this.getJdbc().queryForMap("C140M01A.selCh9FinInfor1",
		// new Object[] { ces140MainID });

		String sql = getSqlBySqlId("C140M01A.selCh9FinInfor1");
		sql = MessageFormat.format(sql, new Object[] { schemaKind });
		return this.getJdbc().queryForMap(sql, new Object[] { ces140MainID });

	}

	@Override
	public Map<String, Object> selCES140Ch9FinYear(String ces140MainID,
			String schemaKind) {
		// return this.getJdbc().queryForMap("C140M01A.selCh9FinYear",
		// new Object[] { ces140MainID });

		String sql = getSqlBySqlId("C140M01A.selCh9FinYear");
		sql = MessageFormat.format(sql, new Object[] { schemaKind });
		return this.getJdbc().queryForMap(sql, new Object[] { ces140MainID });
	}

	@Override
	public List<Map<String, Object>> selCES140Ch9FINData(String ces140MainID,
			String schemaKind) {
		// return this.getJdbc().queryForList(
		// "C140M01A.selCh9FINData",
		// new Object[] { ces140MainID });

		String sql = getSqlBySqlId("C140M01A.selCh9FINData");
		sql = MessageFormat.format(sql, new Object[] { schemaKind });
		return this.getJdbc().queryForList(sql, new Object[] { ces140MainID });
	}

	/**
	 * 取得Sql
	 * 
	 * @param sqlId
	 *            sqlId
	 * @return sqlString
	 */
	public String getSqlBySqlId(String sqlId) {
		return sqlp.getValue(sqlId);
	}

	@Override
	public List<Map<String, Object>> listLMS180R18(String bgnDate,
			String endDate, String otherCondition, String areaNo) {

		return this.getJdbc()
				.queryForList("rpt.LMS180R18",
						new String[] { bgnDate, endDate, areaNo }, 0,
						Integer.MAX_VALUE); //

		// /*
		// TEST
		// return this.getJdbc().queryForList("rpt.LMS180R18T",
		// new String[] { },0,Integer.MAX_VALUE); //bgnDate, endDate, areaNo
		// */
	}

	@Override
	public List<Map<String, Object>> findC101M01A_queryScoreModel(
			String custId, String dupNo, String ownBrId, ISearch search) {
		String orderByStr = Util.getOrderData(search.getOrderBy());
		StringBuffer sql = new StringBuffer();
		HashMap<String, String> m = new HashMap<String, String>();
		List<Object> subparams = new ArrayList<Object>();
		List<Object> params = new ArrayList<Object>();
		m.put("custId", custId);
		m.put("dupNo", dupNo);
		m.put("ownBrId", ownBrId);

		for (String colName : m.keySet()) {
			if (Util.isNotEmpty(m.get(colName))) {
				sql.append(" and m.").append(colName).append(" = ? ");
				subparams.add(m.get(colName));
			}
		}
		params.addAll(subparams);
		params.addAll(subparams);
		params.addAll(subparams);

		return this.getJdbc().queryForAllListByCustParam(
				"CLS.findC101M01A_queryScoreModel",
				new Object[] { sql.toString(), sql.toString(), sql.toString(),
						orderByStr }, params.toArray(new Object[0]));
	}

	@Override
	public Map<String, Object> findL140M01AMAXUPDATE(String cntrNo) {
		return this.getJdbc().queryForMap("findL140M01AMAXUPDATE",
				new Object[] { cntrNo });
	}

	@Override
	public List<Map<String, Object>> findL130M01A_NeedSend() {
		return this.getJdbc().queryForList("L130M01A.selNeedSendY",
				new Object[] {});
	}

	@Override
	public List<Map<String, Object>> findL140M01RByL120M01A(String mainId) {
		return this.getJdbc().queryForList("L140M01R.findByL120M01A",
				new Object[] { mainId });
	}

	@Override
	public List<Map<String, Object>> findL120M01AByC160M01A(String mainId) {
		return this.getJdbc().queryForList("L120M01A.findByC160M01A",
				new Object[] { mainId });
	}

	@Override
	public List<Map<String, Object>> findL140M01RByL120M01AcaseNo(
			String mainId, String caseNo) {
		return this.getJdbc().queryForList("L140M01R.findByL120M01AcaseNo",
				new Object[] { mainId, caseNo });
	}

	@Override
	public List<Map<String, Object>> findL140M01RByL120M01AInfeeSrc3(
			String mainId) {
		return this.getJdbc().queryForList("L140M01R.findByL120M01AInfeeSrc3",
				new Object[] { mainId });
	}

	@Override
	public List<Map<String, Object>> findGridListByC240M01AMainid(
			String c240m01a_mainId) {
		return this.getJdbc().queryForListWithMax(
				"C241M01A.findGridListByC240M01AMainid",
				new Object[] { c240m01a_mainId });
	}

	@Override
	public List<Map<String, Object>> findL140M01AByC160M01A(String mainId) {
		return this.getJdbc().queryForList("L140M01A.findByC160M01A",
				new Object[] { mainId });
	}

	@Override
	public List<Map<String, Object>> findL140M01A_J_107_0327(String custId,
			String cntrNo) {
		// 抓前N筆即可
		return this.getJdbc().queryForList("L140M01A.queryJ-107-0327",
				new Object[] { custId + "%", cntrNo + "%" });
	}

	@Override
	public Map<String, Object> findLatestGrpCntrNo(String brId, String grpCntrNo) {
		return this.getJdbc().queryForMap("L140M01A.findLatestGrpCntrNo",
				new String[] { grpCntrNo, brId });
	}

	@Override
	public Set<String> findCrsMailBr(String retrialDate, boolean isArea) {
		Set<String> r = new HashSet<String>();

		List<Map<String, Object>> list = this.getJdbc().queryForList(
				isArea ? "C241M01A.mail_checkA" : "C241M01A.mail_checkB",
				new String[] { retrialDate });
		for (Map<String, Object> row : list) {
			r.add(Util.trim(row.get("OWNBRID")));
		}
		return r;
	}

	@Override
	public Set<String> findLrsMailBr(String retrialDate, boolean isArea) {
		Set<String> r = new HashSet<String>();

		List<Map<String, Object>> list = this.getJdbc().queryForList(
				isArea ? "L170M01A.mail_checkA" : "L170M01A.mail_checkB",
				new String[] { retrialDate });
		for (Map<String, Object> row : list) {
			r.add(Util.trim(row.get("OWNBRID")));
		}
		return r;
	}

	@Override
	public List<Map<String, Object>> findAllCNTRNOByL140M01AMainid(String mainId) {
		return this.getJdbc().queryForList("findAllCNTRNOByL140M01AMainid",
				new Object[] { mainId });
	}

	@Override
	public String findL140M01C_MaxSeqNum(String mainId) {
		Map<String, Object> queryForMap = this.getJdbc().queryForMap(
				"getL140M01CMaxSeqNum", new Object[] { mainId });
		int seq = Util.parseInt(queryForMap.get("SEQNUM")) + 1;
		return String.valueOf(seq);
	}

	// @Override
	// public List<Map<String, Object>> findL140M01C_SeqNumIsEmpty(String
	// mainId) {
	// return this.getJdbc().queryForList("getL140M01CSeqNumIsEmpty",
	// new Object[] { mainId });
	// }

	@Override
	public List<Map<String, Object>> findAllL140M01ACancelCntrnoDataByID(
			String custId, String dupNo) {
		return this.getJdbc().queryForList(
				"findAllL140M01ACancelCntrnoDataByID",
				new Object[] { custId, dupNo });
	}

	@Override
	public List<Map<String, Object>> findAllL140M01ACancelCntrnoDataByCntrNo(
			String cntrNo) {
		return this.getJdbc().queryForList(
				"findAllL140M01ACancelCntrnoDataByCntrNo",
				new Object[] { cntrNo });
	}

	/**
	 * J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
	 */
	@Override
	public List<Map<String, Object>> findL180M01B(String mainId, String type) {
		String sqlId = "L180M01B.findGridList";
		if (Util.equals(type, "S")) {
			sqlId = "L180M01B.findGridListForSmallBuss";
		} else if (Util.equals(type, "R")) {
			sqlId = "L180M01B.findGridListForRandom";
		}
		return this.getJdbc().queryForListWithMax(sqlId,
				new String[] { mainId, mainId });
	}

	/**
	 * J-109-0313 小規模覆審 取得名單檔中同ID最新報告表
	 */
	@Override
	public List<Map<String, Object>> findisSmallBuss_lrs(String mainId) {
		return this.getJdbc().queryForListWithMax("findisSmallBuss_lrs",
				new String[] { mainId, mainId });
	}

	/**
	 * J-110-0272 抽樣覆審 取得名單檔中同ID最新報告表，用於覆審名單產製xls
	 */
	@Override
	public List<Map<String, Object>> findL180M01BByL170M01A(String mainId) {
		return this.getJdbc().queryForListWithMax("findL180M01BByL170M01A",
				new String[] { mainId, mainId });
	}

	@Override
	public List<Map<String, Object>> findL120M01A_lrs(String brNo,
			List<String> custId_list) {
		// List<String> r = new ArrayList<String>();
		List<Object> params = new ArrayList<Object>();
		String custId_listParam = Util.genSqlParam(custId_list);
		params.add(brNo);
		params.addAll(custId_list);
		// for (String custId : custId_list) {
		// r.add("'" + Util.trim(custId) + "'");
		// }
		// if (r.size() == 0) {
		// r.add("''");
		// }
		// String custIds = StringUtils.join(r, ",");
		return this.getJdbc().queryForAllListByCustParam("findL120M01A_lrs",
				new Object[] { custId_listParam },
				params.toArray(new Object[0]));
	}

	@Override
	public List<Map<String, Object>> findL120M01A_lrs_ctlType_B(String brNo,
			List<String> custId_list) {
		String custIdParam = Util.genSqlParam(custId_list);
		List<Object> params = new ArrayList<Object>();
		params.addAll(custId_list);

		// List<String> r = new ArrayList<String>();
		// for (String custId : custId_list) {
		// r.add("'" + Util.trim(custId) + "'");
		// }
		// if (r.size() == 0) {
		// r.add("''");
		// }
		// String custIds = StringUtils.join(r, ",");
		params.add(brNo);
		params.add(brNo);
		return this.getJdbc().queryForAllListByCustParam(
				"findL120M01A_lrs_ctlType_B", new Object[] { custIdParam },
				params.toArray(new Object[0])); // brNo, brNo
	}

	@Override
	public List<Map<String, Object>> get_cntrDoc_from_VLUSEDOC01(
			String caseMainId, String useBrId, String itemType) {

		return this.getJdbc().queryForListWithMax(
				"find_cntrDoc_from_VLUSEDOC01",
				new String[] { caseMainId, useBrId, itemType });
	}

	@Override
	public BigDecimal findAllC900M01GAndStatus01(String grpCntrNo) {
		Map<String, Object> queryForMap = this.getJdbc().queryForMap(
				"findAllC900M01GAndStatus01", new Object[] { grpCntrNo });
		BigDecimal result = BigDecimal.ZERO;
		if (queryForMap != null) {
			result = Util.parseBigDecimal(queryForMap.get("APPLYAMT"));
		}
		return result;
	}

	@Override
	public BigDecimal findAllC900M01GAndStatus02(String grpCntrNo) {
		Map<String, Object> queryForMap = this.getJdbc().queryForMap(
				"findAllC900M01GAndStatus02", new Object[] { grpCntrNo });
		BigDecimal result = BigDecimal.ZERO;
		if (queryForMap != null) {
			result = Util.parseBigDecimal(queryForMap.get("APPROVEAMT"));
		}
		return result;
	}

	@Override
	public BigDecimal findAllC900M01GAndStatus03(String grpCntrNo) {
		Map<String, Object> queryForMap = this.getJdbc().queryForMap(
				"findAllC900M01GAndStatus03", new Object[] { grpCntrNo });
		BigDecimal result = BigDecimal.ZERO;
		if (queryForMap != null) {
			result = Util.parseBigDecimal(queryForMap.get("LOANAMT"));
		}
		return result;
	}

	@Override
	public List<Map<String, Object>> findCntrnoByL161S01A(String mainId) {
		// return this.getJdbc().queryForList("findCntrnoByL161S01A",
		// new Object[] { mainId });
		return this.getJdbc().queryForList("findCntrnoByL161S01A",
				new Object[] { mainId }, 0, Integer.MAX_VALUE);
	}

	@Override
	public List<Map<String, Object>> findL140InfoSinceWebApplyTS(String brNo,
			String custId, String dupNo, Timestamp applyTS) {
		return this.getJdbc().queryForList("findAllL140S02A_sinceWebApply",
				new Object[] { custId, dupNo,
						// custId, dupNo, applyTS,
						applyTS, brNo, TWNDate.toAD(applyTS) }, 0,
				Integer.MAX_VALUE);
	}

	@Override
	public void c121m01aclearUnUsedDoc() {
		this.getJdbc().update("c121m01a.clearUnUsedDoc", null);
	}

	@Override
	public List<Map<String, Object>> listLMS180R25(String bgnDate,
			String endDate, String[] areaNo, String sqlMode) {
		List<String> xx = new ArrayList<String>();
		xx.add(bgnDate);
		xx.add(endDate);
		xx.addAll(Arrays.asList(areaNo));
		String sqlParam = Util.genSqlParam(areaNo);
		return this.getJdbc().queryForAllListByCustParam(
				"rpt.LMS180R25_" + sqlMode, new String[] { sqlParam },
				xx.toArray(new String[0])); //

		// /*
		// TEST
		// return this.getJdbc().queryForList("rpt.LMS180R18T",
		// new String[] { },0,Integer.MAX_VALUE); //bgnDate, endDate, areaNo
		// */
	}

	/**
	 * 檢查額度序號是否有於簽報書額度明細表/聯行攤貸比例建檔(國內/海外簽報書檢核用)
	 * 
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @return
	 */
	@Override
	public int checkCntrnoExistEloan(String mainId, String custId,
			String dupNo, String cntrNo) {
		return this.getJdbc().queryForInt("checkCntrnoExistEloan",
				new Object[] { mainId, custId, dupNo, cntrNo, cntrNo });
	}

	@Override
	public List<Map<String, Object>> find140QByLoanTargetIsNull() {
		return this.getJdbc().queryForList("find140QByLoanTargetIsNull",
				new Object[] {}, 0, Integer.MAX_VALUE);
	}

	@Override
	public List<Map<String, Object>> find140QByAllCnLoanFgIsY() {
		return this.getJdbc().queryForList("find140QByAllCnLoanFgIsY",
				new Object[] {}, 0, Integer.MAX_VALUE);
	}

	@Override
	public List<Map<String, Object>> find140AFrom140QLoanTargetIsNull() {
		return this.getJdbc().queryForList("find140AFrom140QLoanTargetIsNull",
				new Object[] {}, 0, Integer.MAX_VALUE);
	}

	@Override
	public void updateL140M01QLoanTargetByMainId(String mainId,
			String loanTarget, String bloanTarget, String charCd) {
		List<Object> params = new ArrayList<Object>();
		StringBuffer contents = new StringBuffer("");

		if (Util.notEquals(Util.trim(loanTarget), "")) {
			contents.append("loanTarget=?");
			params.add(loanTarget);
			// contents.append("'");
		}

		if (Util.notEquals(Util.trim(bloanTarget), "")) {
			if (Util.equals(Util.trim(contents.toString()), "")) {
				contents.append("bloanTarget=?");
				params.add(bloanTarget);
				// contents.append("'");
			} else {
				contents.append(",bloanTarget=?");
				params.add(bloanTarget);
				// contents.append("'");
			}

		}

		if (Util.notEquals(Util.trim(charCd), "")) {
			if (Util.equals(Util.trim(contents.toString()), "")) {
				contents.append("charCd=?");
				params.add(charCd);
				// contents.append("'");
			} else {
				contents.append(",charCd=?");
				params.add(charCd);
				// contents.append("'");
			}
		}

		if (Util.equals(Util.trim(contents.toString()), "")) {
			return;
		}
		params.add(mainId);

		this.getJdbc().updateByCustParam("L140M01Q.updateLoanTarget",
				new Object[] { contents.toString() },
				params.toArray(new Object[0]));
	}

	/**
	 * J-104-0270-002 Web
	 * e-Loan國內授信管理系統OBU戶檢核要有聯徵虛擬統編才能送呈主管，但排除遠匯、衍生性金融商品與其他非授信業務
	 * 
	 * @param mainId
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findL140M01C_findNeedChkObuJcicId(
			String mainId) {
		return this.getJdbc().queryForList("L140M01C.findNeedChkObuJcicId",
				new Object[] { mainId });
	}

	/**
	 * G-104-0097-001 Web e-Loan
	 * 海外授信管理系統簽報書檢核對同一人、同一關係人、同一關係企業或集團之授信限額規定不得超過泰子行淨值25%。 清除信用風險遵循本案異動額度部分
	 * 
	 * @param mainId
	 */
	@Override
	public void setCaculate_L120S01M(String isCaculate, String mainId) {
		this.getJdbc().update("L120S01M.setCaculate",
				new Object[] { isCaculate, mainId });
	}

	/**
	 * G-104-0097-001 Web e-Loan
	 * 海外授信管理系統簽報書檢核對同一人、同一關係人、同一關係企業或集團之授信限額規定不得超過泰子行淨值25%。 清除信用風險遵循本案異動額度部分
	 * 
	 * @param mainId
	 */
	@Override
	public void cleanLoacal_L120S01N(String mainId) {
		this.getJdbc().update("L120S01N.cleanLocal", new Object[] { mainId });
	}

	/**
	 * G-104-0097-001 Web e-Loan
	 * 海外授信管理系統簽報書檢核對同一人、同一關係人、同一關係企業或集團之授信限額規定不得超過泰子行淨值25%。 清除信用風險遵循本案異動額度部分
	 * 
	 * @param mainId
	 */
	@Override
	public void cleanLoacal_L120S01O(String mainId) {
		this.getJdbc().update("L120S01O.cleanLocal", new Object[] { mainId });
	}

	/**
	 * G-104-0097-001 Web e-Loan
	 * 海外授信管理系統簽報書檢核對同一人、同一關係人、同一關係企業或集團之授信限額規定不得超過泰子行淨值25%。 清除信用風險遵循本案異動額度部分
	 * 
	 * @param mainId
	 */
	@Override
	public void setLocalCurrentAdjVal_L120S01O(String mainId, String custId,
			String dupNo, BigDecimal netValue) {
		this.getJdbc().update("L120S01O.setLocalCurrentAdjVal",
				new Object[] { netValue, mainId, custId, dupNo });
	}

	/**
	 * G-104-0097-001 Web e-Loan
	 * 海外授信管理系統簽報書檢核對同一人、同一關係人、同一關係企業或集團之授信限額規定不得超過泰子行淨值25%。 清除信用風險遵循本案異動額度部分
	 * 
	 * @param mainId
	 */
	@Override
	public List<Map<String, Object>> findAllL120S01NSumByMainId(String mainId) {
		return this.getJdbc().queryForList(
				"L120S01N.getAllL120S01NSumByMainId",
				new Object[] { mainId, mainId, mainId });
	}

	/**
	 * G-104-0097-001 Web e-Loan
	 * 海外授信管理系統簽報書檢核對同一人、同一關係人、同一關係企業或集團之授信限額規定不得超過泰子行淨值25%。 清除信用風險遵循本案異動額度部分
	 * 
	 * @param mainId
	 */
	@Override
	public void setLoacal_L120S01N(String mainId, String custId, String dupNo,
			String dataKind, BigDecimal localCurrentAdjValT,
			BigDecimal localCurrentTotal, BigDecimal shareOfNet) {
		// localCurrentAdjValT = ?,localCurrentTotal=?,shareOfNet = ? WHERE
		// MAINID =? AND DATAKIND = ? AND CUSTID = ? AND DUPNO
		this.getJdbc().update(
				"L120S01N.setLocal",
				new Object[] { localCurrentAdjValT, localCurrentTotal,
						shareOfNet, mainId, custId, dupNo, dataKind });
	}

	@Override
	public List<Map<String, Object>> findLms140QByComBrToBr01_Elf506Y01ToY04(
			String exDate, String fBranch, String tBranch) {
		return this.getJdbc().queryForList(
				"L140M01Q.getByComBrToBr01_Elf506Y01ToY04",
				new Object[] { exDate, fBranch, tBranch, fBranch }, 0,
				Integer.MAX_VALUE);
	}

	@Override
	public List<Map<String, Object>> listLMS180R26(String bgnDate,
			String endDate) {

		return this.getJdbc().queryForList(
				"rpt.LMS180R26",
				new String[] { bgnDate, endDate, endDate, endDate, endDate,
						endDate }, 0, Integer.MAX_VALUE);
	}

	@Override
	public List<Map<String, Object>> listLMS180R27(List<String> custQuery,
			List<String> custParam) {
		// J-111-0583_05097_B1001 Web
		// e-Loan企金授信提供各營運中心可自行列印轄下分行於指定期間內所簽報異常通報之「授信異常通報案件報送統計表」
		// return this.getJdbc().queryForAllListByCustParam("rpt.LMS180R27",
		// new Object[] { otherCondition },
		// new Object[] { bgnDate, endDate });

		return this.getJdbc().queryForAllListByCustParam("rpt.LMS180R27",
				custQuery.toArray(), custParam.toArray());

	}

	@Override
	public List<Map<String, Object>> listLMS1205(String[] brNoArrs) {
		return this.getJdbc().queryForListWithMax("modelCompare.listLMS1205",
				brNoArrs);
	}

	@Override
	public List<Map<String, Object>> listLMS180R28(String brNo,
			String retrialDateBeg, String retrialDateEnd) {
		return this.getJdbc().queryForListWithMax(
				"rpt.LMS180R28",
				new String[] { brNo, retrialDateBeg, retrialDateEnd, brNo,
						retrialDateBeg, retrialDateEnd });
	}

	@Override
	public List<Map<String, Object>> findC101M01A_queryScoreModel_C1(
			String custId, String dupNo, String ownBrId) {

		List<Object> params = new ArrayList<Object>();
		StringBuffer sql = new StringBuffer();
		HashMap<String, String> m = new HashMap<String, String>();
		m.put("custId", custId);
		m.put("dupNo", dupNo);
		m.put("ownBrId", ownBrId);

		for (String colName : m.keySet()) {
			if (Util.isNotEmpty(m.get(colName))) {
				sql.append(" and m.").append(colName).append(" = ? ");
				params.add(m.get(colName));

			}
		}

		return this.getJdbc().queryForAllListByCustParam(
				"LMS.findC101M01A_queryScoreModel_C1",
				new Object[] { sql.toString() }, params.toArray(new Object[0]));
	}

	@Override
	public List<Map<String, Object>> findC101M01A_queryScoreModel_C2(
			String custId, String dupNo, String ownBrId) {

		List<Object> params = new ArrayList<Object>();
		StringBuffer sql = new StringBuffer();
		HashMap<String, String> m = new HashMap<String, String>();
		m.put("custId", custId);
		m.put("dupNo", dupNo);
		m.put("ownBrId", ownBrId);

		for (String colName : m.keySet()) {
			if (Util.isNotEmpty(m.get(colName))) {
				sql.append(" and m.").append(colName).append(" = ? ");
				params.add(m.get(colName));
			}
		}

		return this.getJdbc().queryForAllListByCustParam(
				"LMS.findC101M01A_queryScoreModel_C2",
				new Object[] { sql.toString() }, params.toArray(new Object[0]));
	}

	@Override
	public List<Map<String, Object>> findG117M01A_FinancialAlert(String grpId) {
		return this.getJdbc().queryForList("getG117M01AByGrpId",
				new Object[] { grpId });
	}

	/**
	 * 刪除利率檔案特定日期所有資料 J-105-0185-001
	 * 請提供103年度及104年度國內所有分行之新作、增額及續約之授信件數及額度金額(包含企金及消金)
	 * 
	 * @param mainId
	 */
	@Override
	public void RATETBL_deleteByDateYmd(String dateYmd) {
		this.getJdbc().update("RATETBL.deleteByDateYmd",
				new Object[] { dateYmd });
	}

	/**
	 * 新增利率資料 J-105-0185-001 請提供103年度及104年度國內所有分行之新作、增額及續約之授信件數及額度金額(包含企金及消金)
	 * 
	 * @param mainId
	 */
	@Override
	public void RATETBL_insert(String curr, String dataYmd, BigDecimal endRate,
			String rateYmd) {
		this.getJdbc().update("RATETBL.insert",
				new Object[] { curr, dataYmd, endRate, rateYmd });
	}

	/**
	 * 取得e-Loan額度 J-105-0228-002 Web
	 * e-Loan企金授信簽報書新增私募基金報表新增列示近半年已核准簽報書尚未於a-Loan建檔之額度資料
	 * 
	 * @param custId
	 * @param dupNo
	 * @param endDate
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findCntrDocByCustIdForPEFund(
			String custId, String dupNo, String endDate) {
		return this.getJdbc().queryForList("getCntrDocDataByCustIdForPEFund",
				new Object[] { custId, dupNo, endDate });
	}

	/**
	 * 取得已核准未簽約報送資料 J-105-0214-001 Web e-Loan 管理報表新增授信簽案已核准未能簽約撥貸原因表。
	 * 
	 * @param custId
	 * @param dupNo
	 * @param endDate
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findL230ByDataDateAndBrNo(String bgnDate,
			String endDate, String brNo) {
		return this.getJdbc().queryForListWithMax("getL230ByDataDateAndBrNo",
				new Object[] { brNo, bgnDate, endDate });
	}

	@Override
	public List<Map<String, Object>> findDeletedMetaByMainId(String mainId) {
		return this.getJdbc().queryForListWithMax("getDeletedMetaByMainId",
				new Object[] { mainId });
	}

	/**
	 * J-105-0331-001 新增已核准授信額度辦理狀態通報彙總表
	 * 
	 * @param startDate
	 * @param endDate
	 * @param otherCondition
	 * @return
	 */
	@Override
	public List<Map<String, Object>> queryLMS180R31Data(String startDate,
			String endDate, String otherCondition, String otherParam1) {

		if (Util.notEquals(Util.trim(otherParam1), "")) {
			return this.getJdbc().queryForAllListByCustParam("rpt.LMS180R31",
					new Object[] { otherCondition },
					new Object[] { startDate, endDate, otherParam1 });
		} else {
			return this.getJdbc().queryForAllListByCustParam("rpt.LMS180R31",
					new Object[] { otherCondition },
					new Object[] { startDate, endDate });
		}

	}

	/**
	 * 依額度明細表MAINID取得最新一筆報送 J-105-0331-001 新增已核准授信額度辦理狀態通報彙總表
	 */
	@Override
	public Map<String, Object> findL230S01LastBySrcMainId(String srcMainId,
			String docstatus) {
		return this.getJdbc().queryForMap("L23S01A.findLastBySrcMainId",
				new Object[] { srcMainId, docstatus, srcMainId, docstatus });
	}

	/**
	 * 依額度明細表MAINID取得最新一筆已簽約報送(為了抓簽約日期) J-105-0331-001 新增已核准授信額度辦理狀態通報彙總表
	 */
	@Override
	public Map<String, Object> findL230S01LastBySrcMainIdAndNuseMemo(
			String srcMainId, String docstatus, String nuseMemo) {
		return this.getJdbc().queryForMap(
				"L23S01A.findLastBySrcMainIdAndNuseMemo",
				new Object[] { srcMainId, docstatus, srcMainId, docstatus,
						nuseMemo });
	}

	/**
	 * J-105-0340-001 Web e-Loan 交換票據抵用科目調整並上傳a-Loan
	 * 
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findZ15CntrDoc() {
		return this.getJdbc().queryForListWithMax("batch.doLmsBatch0007",
				new Object[] {});
	}

	@Override
	public List<Map<String, Object>> findCntrNoByC120S01B_comname(String comname) {
		return this.getJdbc().queryForListWithMax(
				"findCntrNoByC120S01B_comname",
				new String[] { "%" + comname + "%" });
	}

	@Override
	public List<Map<String, Object>> find715_CntrDoc(boolean is715) {
		if (is715) {
			return this.getJdbc().queryForListWithMax(
					"batch.doLmsBatch0008_002", new Object[] {});
		} else {
			return this.getJdbc().queryForListWithMax(
					"batch.doLmsBatch0008_001", new Object[] {});
		}
	}

	@Override
	public void updateL140m01cLoanTp(String mainId, String origLoanTp,
			String newLoanTp) {
		this.getJdbc().update("L140M01C.updateLoanTp",
				new String[] { newLoanTp, mainId, origLoanTp });
	}

	/**
	 * J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	 */
	@Override
	public List<Map<String, Object>> findC120S01D_selectGroupByCustId(
			List<String> cesMainIds) {
		String cesMainIdParams = Util.genSqlParam(cesMainIds);

		return this.getJdbc().queryForListByCustParam(
				"CES.C120S01D.selectGroupByCustId",
				new Object[] { cesMainIdParams },
				cesMainIds.toArray(new String[0]));
	}

	/**
	 * J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	 */
	@Override
	public List<Map<String, Object>> findC120S01D_selectGroupByCustName(
			List<String> cesMainIds) {
		String cesMainIdParams = Util.genSqlParam(cesMainIds);

		return this.getJdbc().queryForListByCustParam(
				"CES.C120S01D.selectGroupByCustName",
				new Object[] { cesMainIdParams },
				cesMainIds.toArray(new String[0]));
	}

	/**
	 * J-106-0082-001 Web e-Loan國內企金授信系統，額度明細表新增中小企業創新發展專案貸款
	 */
	@Override
	public List<Map<String, Object>> listLMS180R33(String bgnDate,
			String endDate) {

		return this.getJdbc().queryForListWithMax("rpt.LMS180R33",
				new Object[] { endDate });
	}

	/**
	 * J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
	 */
	@Override
	public List<Map<String, Object>> findLMSL120M01A(String brId,
			String custId, String dupNo) {
		return this.getJdbc().queryForList(
				"L120M01A.selByCustIdAndDupNoAndBrId",
				new Object[] { custId, dupNo, brId, brId });
	}

	@Override
	public Map<String, Object> findC900S02A_groupBy_ratePlan_companyId(
			String begDate, String endDate, String ratePlan, String companyId) {
		return this.getJdbc().queryForMap("J_106_0170_sql_2",
				new String[] { begDate, endDate, ratePlan, companyId });
	}

	@Override
	public List<Map<String, Object>> findC900S02A_groupBy_brNo(String begDate,
			String endDate, String companyId5) {
		return this.getJdbc().queryForListWithMax("J_106_0170_sql_3.V201811",
				new String[] { begDate, endDate, companyId5 });
	}

	@Override
	public List<Map<String, Object>> findLaaData(String approveDateBeg,
			String caseBrId) {
		String param = approveDateBeg + " 00:00:00";
		return this.getJdbc().queryForListWithMax("J_107_0074_sql_1",
				new String[] { param, caseBrId + "%", "%" });
	}

	@Override
	public List<Map<String, Object>> findLaaData_by_L120M01A(
			String l120m01a_mainId) {
		String param = "1911-01-01 00:00:00";
		return this.getJdbc().queryForListWithMax("J_107_0074_sql_1",
				new String[] { param, "%", l120m01a_mainId + "%" });
	}

	// J-107-0045-001 Web e-Loan企金授信簽報書配合海外啟用IFRS徵信報告調整財報引進相關功能。

	@Override
	public void C140JSON_copy1(String mainId, String cesMainId, String tab) {
		this.getJdbc().update("C140JSON.notChgUid.copy",
				new Object[] { mainId, mainId, cesMainId, tab });
	}

	@Override
	public void C140SDSC_copy1(String mainId, String cesMainId, String tab) {
		this.getJdbc().update("C140SDSC.notChgUid.copy",
				new Object[] { mainId, mainId, cesMainId, tab });
	}

	@Override
	public void C140SFFF_copy1(String mainId, String cesMainId, String tab) {
		this.getJdbc().update("C140SFFF.notChgUid.copy",
				new Object[] { mainId, mainId, cesMainId, tab });
	}

	/**
	 * J-107-0184_05097_B1001 Web e-loan企金授信簽報時提供以借款人查詢應簽報的額度明細表及該客戶所有的往來分行(
	 * 包含前次簽報書所簽報額度明細表所屬分行及現有有效額度的往來分行)等資訊,並於送呈前進行差異比對, 就存在差異時提供警示訊息,
	 * 以避免錯選授信案件授權層級情事。
	 */
	@Override
	public List<Map<String, Object>> findL140M01_byLastRptWithCustid(
			String custId, String dupNo, String caseBrId) {
		return this.getJdbc().queryForListWithMax(
				"L140M01A.getLastRptDocByCustId",
				new Object[] { custId, dupNo, custId, dupNo, caseBrId });
	}

	/**
	 * J-107-0196_05097_B1001 Web
	 * e-Loan企金授信系統管理報表新增以各營運中心之所載聯貸案企金案件，並按期產製報表，以供後續追蹤聯貸案件進度及收益。
	 * (1)營運中心之企金授信，案件簽報書屬呈總處。
	 */
	@Override
	public List<Map<String, Object>> listLMS180R36_1() {
		return this.getJdbc().queryForListWithMax("rpt.LMS180R36_AREA_1",
				new Object[] {});
	}

	/**
	 * J-107-0196_05097_B1001 Web
	 * e-Loan企金授信系統管理報表新增以各營運中心之所載聯貸案企金案件，並按期產製報表，以供後續追蹤聯貸案件進度及收益。
	 * (2)營運中心權限內已核准新做聯貸案件
	 */
	@Override
	public List<Map<String, Object>> listLMS180R36_2(String startDate,
			String endDate) {
		return this.getJdbc().queryForListWithMax("rpt.LMS180R36_AREA_2",
				new Object[] { startDate, endDate });
	}

	/**
	 * J-107-0233_05097_B1001 Web e-Loan企金授信修訂「放款定價合理性分析表」。
	 */
	@Override
	public List<Map<String, Object>> findL120S08APrintGroupOfMainIdCustIdPrintGroup(
			String mainId, String versionDate) {
		return this.getJdbc().queryForListWithMax("l120s08a.findPrintGroup",
				new Object[] { mainId, versionDate });
	}

	@Override
	public List<Map<String, Object>> findL120S08APrintGroupByOneCustId(
			String mainId, String versionDate, String curr, String custId,
			String dupNo) {
		return this.getJdbc().queryForListWithMax(
				"l120s08a.findPrintGroupByOneCustId",
				new Object[] { mainId, versionDate, curr, custId, dupNo });
	}

	/**
	 * J-107-0224_05097_B1001 Web e-Loan企金處新增企金授信案件敘做情形及比較表
	 * J-107-0358_05097_B1001 修改企金授信案件敘做情形表，自108年起納入個人土建融案件且興建房屋註記為非自用案件
	 * 
	 * 金授信案件敘做情形及比較表
	 * 
	 */
	@Override
	public List<Map<String, Object>> listLMS180R37(String startDate,
			String endDate) {
		return this.getJdbc().queryForListWithMax("rpt.LMS180R37.01",
				new Object[] { startDate, endDate, startDate, endDate });
	}

	/**
	 * J-107-0224_05097_B1001 Web e-Loan企金處新增企金授信案件敘做情形及比較表
	 * 
	 * 金授信案件敘做情形及比較表
	 * 
	 */
	@Override
	public List<Map<String, Object>> listLMS180R37_AreaBranch(String brNo,
			String areaId) {
		return this.getJdbc().queryForListWithMax("rpt.LMS180R37.02",
				new Object[] { brNo, areaId });
	}

	/**
	 * LMS180R38 企金已核准授信額度辦理狀態通報彙總表
	 * 
	 * @param startDate
	 * @param endDate
	 * @param otherCondition
	 * @return
	 */
	@Override
	public List<Map<String, Object>> queryLMS180R38Data(String startDate,
			String endDate, String otherCondition, String otherParam1) {

		if (Util.notEquals(Util.trim(otherParam1), "")) {
			return this.getJdbc().queryForAllListByCustParam("rpt.LMS180R38",
					new Object[] { otherCondition },
					new Object[] { startDate, endDate, otherParam1 });
		} else {
			return this.getJdbc().queryForAllListByCustParam("rpt.LMS180R38",
					new Object[] { otherCondition },
					new Object[] { startDate, endDate });
		}

	}

	@Override
	public List<Map<String, Object>> findC900S02C_fillData(String year_1st,
			String data_ym_1st, String data_ym_last) {
		return this.getJdbc().queryForListWithMax(
				"C900S02C.fillData",
				new Object[] { year_1st, data_ym_last, data_ym_1st,
						data_ym_last, year_1st, data_ym_last, year_1st,
						data_ym_last, data_ym_last, data_ym_last, });
	}

	@Override
	public List<Map<String, Object>> findC900S02C_fillData_exclude940(
			String year_1st, String data_ym_1st, String data_ym_last) {
		return this.getJdbc().queryForListWithMax(
				"C900S02C.fillData_exclude940",
				new Object[] { year_1st, data_ym_last, data_ym_1st,
						data_ym_last, year_1st, data_ym_last, year_1st,
						data_ym_last, data_ym_last, data_ym_last, });
	}

	@Override
	public List<Map<String, Object>> findC900S02C_fetch1(String flag,
			String data_ym, String beg, String end) {
		return this.getJdbc()
				.queryForListWithMax(
						"C900S02C.fetch_1",
						new Object[] { data_ym, flag, beg, end, data_ym, flag,
								beg, end });
	}

	@Override
	public List<Map<String, Object>> findC900S02C_fetch2(String flag,
			String data_ym, String beg, String end) {
		return this.getJdbc().queryForListWithMax(
				"C900S02C.fetch_2",
				new Object[] { data_ym, flag, beg, end, data_ym, flag, beg,
						end, beg, end });
	}

	@Override
	public void batchInsert_C900S02C(List<Object[]> batchValues) {
		_batchUpdate("C900S02C.insert", new int[] { Types.CHAR, Types.CHAR,
				Types.CHAR, Types.CHAR, Types.DATE, Types.DATE, Types.CHAR,
				Types.DECIMAL }, batchValues);
	}

	@Override
	public void batchInsert_C900M03A_dtl(String fn_D, List<Object[]> batchValues) {
		if (Util.equals(fn_D, "IMWM0017.D")) {
			_batchUpdate("C900S03A.insert", new int[] { Types.CHAR, Types.CHAR,
					Types.CHAR, Types.DECIMAL, Types.CHAR, Types.CHAR,
					Types.CHAR, Types.CHAR, Types.CHAR }, batchValues);
		} else if (Util.equals(fn_D, "IDWM0002.D")) {
			_batchUpdate("C900S03B.insert",
					new int[] { Types.CHAR, Types.CHAR, Types.CHAR,
							Types.DECIMAL, Types.CHAR, Types.CHAR, Types.CHAR,
							Types.CHAR, Types.CHAR, Types.CHAR, Types.CHAR },
					batchValues);
		}
	}

	@Override
	public void batchInsert_C900S03C(List<Object[]> batchValues) {
		_batchUpdate("C900S03C.insert", new int[] { Types.DATE, Types.CHAR,
				Types.CHAR,
				Types.CHAR, // 含oid共5個
				Types.CHAR, Types.CHAR, Types.CHAR, Types.CHAR, Types.DATE,
				Types.DATE }, batchValues);
	}

	@Override
	public void batchInsert_C900S03D(List<Object[]> batchValues) {
		_batchUpdate("C900S03D.insert", new int[] { Types.DATE, Types.CHAR,
				Types.CHAR }, batchValues);
	}

	@Override
	public void batchInsert_C900S03E(List<Object[]> batchValues) {
		_batchUpdate("C900S03E.insert", new int[] { Types.DATE, Types.CHAR,
				Types.CHAR }, batchValues);
	}

	@Override
	public void batchInsert_C900S02E_C900S02F(List<Object[]> batchValues_s02e,
			List<Object[]> batchValues_s02f) {
		_batchUpdate("C900S02E.insert", new int[] { Types.CHAR, Types.DATE,
				Types.CHAR, Types.CHAR, Types.CHAR, Types.CHAR, Types.CHAR,
				Types.CHAR, Types.CHAR }, batchValues_s02e);
		_batchUpdate("C900S02F.insert", new int[] { Types.CHAR, Types.CHAR,
				Types.CHAR, Types.CHAR, Types.CHAR }, batchValues_s02f);
	}

	@Override
	public void delete_C900M03A_dtl(String fn_D, String genDate, String genTime) {
		if (Util.equals(fn_D, "IMWM0017.D")) {
			this.getJdbc().update("C900S03A.delete",
					new String[] { fn_D, genDate, genTime });
		} else if (Util.equals(fn_D, "IDWM0002.D")) {
			this.getJdbc().update("C900S03B.delete",
					new String[] { fn_D, genDate, genTime });
		}
	}

	@Override
	public void delete_C900S03C(String cyc_mn) {
		this.getJdbc().update("C900S03C.delete", new String[] { cyc_mn });
	}

	@Override
	public void delete_C900S03D(String cyc_mn) {
		this.getJdbc().update("C900S03D.delete", new String[] { cyc_mn });
	}

	@Override
	public void delete_C900S03E(String cyc_mn) {
		this.getJdbc().update("C900S03E.delete", new String[] { cyc_mn });
	}

	@Override
	public List<Map<String, Object>> prep_text_from_C900S03C(
			boolean isDwCustRelFirstTotalRun, String cyc_mn, String rel_flag,
			String brNo) {
		if (isDwCustRelFirstTotalRun) {
			return this.getJdbc().queryForListWithMax(
					"prep_text_from_C900S03C_totCust",
					new Object[] { cyc_mn, rel_flag, brNo + "%" });
		} else {
			return this.getJdbc().queryForListWithMax(
					"prep_text_from_C900S03C_newCust",
					new Object[] { cyc_mn, rel_flag, cyc_mn, rel_flag,
							brNo + "%" });
		}
	}

	@Override
	public List<Map<String, Object>> prep_custKey_brNo_from_C900S03C_text(
			String cyc_mn, String rel_flag, String text) {
		return this.getJdbc().queryForListWithMax(
				"prep_custKey_brNo_from_C900S03C_text",
				new Object[] { cyc_mn, rel_flag, text });
	}

	/**
	 * J-107-0245_09301_B1001 Web e-Loan企金授信系統覆審報告中增列上次覆審日當時之「信用評等及信用風險內部評等」資訊。
	 * 另授信「額度」欄位，針對中長期不循環授信額度者，請修改以「有效額度」列示。
	 */
	@Override
	public String findL170M01A_exMainid(String mainId, String custId,
			String dupNo, String ownBrId) {
		Map<String, Object> queryForMap = this.getJdbc().queryForMap(
				"findL170M01A_exMainid",
				new Object[] { mainId, custId, dupNo, ownBrId });
		if (queryForMap == null || queryForMap.get("mainId") == null) {
			return null;
		} else {
			return queryForMap.get("mainId").toString();
		}
	}

	/**
	 * J-107-0225_05097_B1001 Web e-Loan企金授信簽報書新增集團關係企業與本行授信往來條件比較表 取得最新額度明細表
	 * 
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @return
	 */
	@Override
	public Map<String, Object> findL140M01A_lastByCustIdAndCntrNo(
			String custId, String dupNo, String cntrNo) {
		return this.getJdbc().queryForMap("L140M01A.getLastByCustIdAndCntrno",
				new String[] { cntrNo, cntrNo, custId, dupNo });
	}

    @Override
    public Map<String, Object> findL140M01ALastDerivEvalByCustId(
            String custId, String dupNo) {
        return this.getJdbc().queryForMap("L140M01A.getLastDerivEvalByCustId",
                new String[] { custId, dupNo });
    }

	/**
	 * J-113-0069_05097_B1001 Web
	 * e-Loan企金授信「主要關係戶與本行授信往來比較表」，借戶及其相關關係企業於本次有簽報者，引進本次簽報內容
	 * 
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @param caseMainId
	 * @param itemType
	 * @return
	 */
	@Override
	public Map<String, Object> findL140M01A_lastByCustIdAndCntrNo_forThisReport(
			String custId, String dupNo, String cntrNo, String caseMainId,
			String itemType) {
		return this.getJdbc().queryForMap(
				"L140M01A.getLastByCustIdAndCntrno_forThisReport",
				new String[] { cntrNo, cntrNo, custId, dupNo, caseMainId,
						itemType });
	}

	// LGD
	@Override
	public List<Map<String, Object>> findL140m01aLastByCntrNo(String cntrNo) {
		return this.getJdbc().queryForListWithMax("L140M01A.getLastByCntrno",
				new String[] { cntrNo });
	}

	/**
	 * J-107-0342_05097_B1001 Web e-Loan授信系統新增覆審考核相關報表
	 * 
	 * @param docType
	 * @param bgnDate
	 * @param endDate
	 * @param ctlType
	 * @param brNo
	 * @return
	 */
	@Override
	public Map<String, Object> findL180r19hHasDone(String docType,
			String bgnDate, String endDate, String ctlType, String brNo) {
		return this.getJdbc().queryForMap("L180R19H.chkDone",
				new Object[] { docType, bgnDate, endDate, ctlType, brNo });
	}

	/**
	 * J-107-0342_05097_B1001 Web e-Loan授信系統新增覆審考核相關報表
	 * 
	 * @param docType
	 * @param bgnDate
	 * @param endDate
	 * @param ctlType
	 * @param brNo
	 */
	@Override
	public void L180R19H_deleteExistData(String docType, String bgnDate,
			String endDate, String ctlType, String brNo) {
		this.getJdbc().update("L180R19H.deleteExistData",
				new Object[] { docType, bgnDate, endDate, ctlType, brNo });
	}

	/**
	 * J-107-0342_05097_B1001 Web e-Loan授信系統新增覆審考核相關報表
	 * 
	 * @param bgnDate
	 * @param endDate
	 * @param brNo
	 */
	public void LELF412A_deleteExistData(String bgnDate, String endDate,
			String brNo) {
		this.getJdbc().update("LELF412A.deleteExistData",
				new Object[] { bgnDate, endDate, brNo });
	}

	/**
	 * J-107-0342_05097_B1001 Web e-Loan授信系統新增覆審考核相關報表
	 * 
	 * @param bgnDate
	 * @param endDate
	 * @param brNo
	 */
	public void LELF412B_deleteExistData(String bgnDate, String endDate,
			String brNo) {
		this.getJdbc().update("LELF412B.deleteExistData",
				new Object[] { bgnDate, endDate, brNo });
	}

	/**
	 * J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
	 * 
	 * @param bgnDate
	 * @param endDate
	 * @param brNo
	 */
	public void LELF412C_deleteExistData(String bgnDate, String endDate,
			String brNo) {
		this.getJdbc().update("LELF412C.deleteExistData",
				new Object[] { bgnDate, endDate, brNo });
	}

	/**
	 * G-107-0642_05097_B1001 配合羅勇分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權
	 * 
	 * @param exDate
	 * @param fBranch
	 * @param tBranch
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findBrToBr01List(String exDate,
			String fBranch, String tBranch) {
		return this.getJdbc()
				.queryForList("BRTOBR01.findTransferBrNoAndCustId",
						new Object[] { exDate, fBranch, tBranch }, 0,
						Integer.MAX_VALUE);
	}

	/**
	 * G-107-0642_05097_B1001 配合羅勇分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權
	 * 
	 * @param exDate
	 * @param fBranch
	 * @param tBranch
	 * @param version
	 */
	@Override
	public void insertL120A01AFromBRTOBR01(String exDate, String fBranch,
			String tBranch, String version) {
		this.getJdbc().update(
				"L120A01A.insertFromBRTOBR01",
				new Object[] { tBranch, version, tBranch, fBranch, exDate,
						fBranch, fBranch, tBranch });
	}

	/**
	 * G-107-0642_05097_B1001 配合羅勇分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權
	 * 
	 * @param exDate
	 * @param fBranch
	 * @param tBranch
	 * @param version
	 */
	@Override
	public void updateL120M01ACaseBrIdFromBRTOBR01(String exDate,
			String fBranch, String tBranch, String version) {
		this.getJdbc().update("L120M01A.updateCaseBrIdFromBRTOBR01",
				new Object[] { tBranch, fBranch, exDate });
	}

	/**
	 * G-107-0642_05097_B1001 配合羅勇分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權
	 * 
	 * @param exDate
	 * @param fBranch
	 * @param tBranch
	 * @param version
	 */
	@Override
	public void updateL140m01aFromBRTOBR01(String exDate, String fBranch,
			String tBranch, String version) {
		this.getJdbc().update("L140M01A.updateCntrNoFromBRTOBR01",
				new Object[] { tBranch, tBranch, fBranch, exDate });
	}

	/**
	 * G-107-0642_05097_B1001 配合羅勇分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權
	 * 
	 * @param exDate
	 * @param fBranch
	 * @param tBranch
	 * @param version
	 */
	@Override
	public void updateL140m01eFromBRTOBR01(String exDate, String fBranch,
			String tBranch, String version) {
		this.getJdbc().update("L140M01E.updateShareNoFromBRTOBR01",
				new Object[] { tBranch, fBranch, exDate });
	}

	/**
	 * G-107-0642_05097_B1001 配合羅勇分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權
	 * 
	 * @param exDate
	 * @param fBranch
	 * @param tBranch
	 * @param version
	 */
	@Override
	public void deleteBrToBr01ByExDateAndFBranch(String exDate, String fbranch) {
		this.getJdbc().update("BRTOBR01.delByExDateAndFBranch",
				new Object[] { exDate, fbranch });
	}

	/**
	 * G-107-0642_05097_B1001 配合羅勇分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權
	 * 
	 * @param custId
	 * @param dupNo
	 * @param exDate
	 * @param fbranch
	 * @param fcustId
	 * @param fcustNo
	 * @param tbranch
	 * @param tcustId
	 * @param tcustNo
	 * @param cname
	 */
	@Override
	public void insertBrToBr01(String custId, String dupNo, String exDate,
			String fbranch, BigDecimal fcustId, BigDecimal fcustNo,
			String tbranch, BigDecimal tcustId, BigDecimal tcustNo,
			String cname, String chgCustId, String chgDupNo) {
		this.getJdbc().update(
				"BRTOBR01.insert",
				new Object[] { custId, dupNo, exDate, fbranch, fcustId,
						fcustNo, tbranch, tcustId, tcustNo, cname, chgCustId,
						chgDupNo });

	}

	@Override
	public List<Map<String, Object>> findClsApplyCntrDataForElf459(
			String approveTimeBeg, String approveTimeEnd, String cntrNo) {
		return this.getJdbc().queryForListWithMax(
				"findClsApplyCntrDataForElf459",
				new Object[] { approveTimeBeg, approveTimeEnd, cntrNo + "%" });
	}

	/**
	 * J-107-0178_05097_B1001 Web e-loan案件簽報書相關文件之資信簡表增加借保人之資信簡表之勾選(能勾選跨頁之資料)
	 * 
	 * @param caseBrId
	 * @param mainId1
	 * @param mainId2
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findC120M01A_selMainIdd(String caseBrId,
			String mainId1, String mainId2) {
		return this.getJdbc().queryForList("C120M01A.selMainIdd",
				new Object[] { mainId1, mainId2, caseBrId });
	}

	@Override
	public List<Map<String, Object>> selDistinctCntrnoByCustidDupno(
			String custId, String dupNo) {
		return this.getJdbc().queryForList(
				"L140M01A.selDistinctCntrnoByCustidDupno",
				new Object[] { custId, dupNo });
	}

	@Override
	public Map<String, Object> get_CLS180R19_data_by_grpCntrNo(String grpCntrNo) {
		return this.getJdbc().queryForMap("rpt.CLS180R19",
				new Object[] { grpCntrNo });
	}

	/**
	 * LMS180R40 簽報階段都更危老業務統計表
	 */
	@Override
	public List<Map<String, Object>> queryLMS180R40DataByCntrno(String cntrno,
			String bgnDate, String endDate, String custId, String filterValue) {
		StringBuffer where = new StringBuffer();
		List<Object> params = new ArrayList<Object>();
		where.append(" AND L140A.CNTRNO = ? ");
		params.add(cntrno);

		if (Util.notEquals(bgnDate, "") && Util.notEquals(endDate, "")) {
			where.append(" AND L120A.ENDDATE BETWEEN ? AND ? ");
			params.add(bgnDate);
			params.add(endDate);
		}
		if (Util.notEquals(custId, "")) {
			where.append(" AND L140A.CUSTID = ? ");
			params.add(custId);
		}
		where.append(" ORDER BY L120A.ENDDATE DESC ");

		return this.getJdbc().queryForListByCustParam(
				"rpt.queryLMS180R40DataByCntrno",
				new Object[] { where.toString() },
				params.toArray(new Object[0]));
	}

	/**
	 * J-107-0357_05097_B1001 Web e-Loan授信系統配合工業區及產業園區建廠優惠貸款專案，額度簽報新增「專案種類」與相關報表
	 */
	@Override
	public List<Map<String, Object>> listLMS180R41(String bgnDate,
			String endDate) {

		return this.getJdbc().queryForListWithMax("rpt.LMS180R41",
				new Object[] { endDate });
	}

	/**
	 * J-108-0040_05097_B1001 Web e-Loan企金授信新增108年度新核准往來客戶及新增放款額度統計表
	 * 
	 * @param bgnDate
	 * @param endDate
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findAllNewCustFromL140m01a(String bgnDate,
			String endDate) {

		return this.getJdbc().queryForListWithMax(
				"L140M01A.selAllNewCust",
				new Object[] { bgnDate, endDate, bgnDate, endDate, bgnDate,
						endDate, bgnDate, endDate });

	}

	/**
	 * 108年度新核准往來客戶及新增放款額度統計表
	 * 
	 * J-108-0040_05097_B1001 Web e-Loan企金授信新增108年度新核准往來客戶及新增放款額度統計表
	 */
	@Override
	public List<Map<String, Object>> listLMS180R42_01(String allBgnDate,
			String allEndDate, String thisMonthBgnDate,
			String thisMonthEndDate, String thisYearBgnDate,
			String thisYearEndDate, String lastYearBgnDate,
			String lastYearEndDate) {

		return this.getJdbc().queryForListWithMax(
				"rpt.LMS180R42_01",
				new Object[] { allBgnDate, allEndDate, thisMonthBgnDate,
						thisMonthEndDate, thisMonthBgnDate, thisMonthEndDate,
						thisYearBgnDate, thisYearEndDate, thisYearBgnDate,
						thisYearEndDate, lastYearBgnDate, lastYearEndDate,
						lastYearBgnDate, lastYearEndDate });
	}

	/**
	 * 本月新增核准額度前五大客戶
	 * 
	 * J-108-0040_05097_B1001 Web e-Loan企金授信新增108年度新核准往來客戶及新增放款額度統計表
	 */
	@Override
	public List<Map<String, Object>> listLMS180R42_02(String thisMonthBgnDate,
			String thisMonthEndDate) {

		return this.getJdbc().queryForListWithMax("rpt.LMS180R42_02",
				new Object[] { thisMonthBgnDate, thisMonthEndDate });
	}

	/**
	 * 本月全行新增核准中小企業戶數最多之前五名分行
	 * 
	 * J-108-0040_05097_B1001 Web e-Loan企金授信新增108年度新核准往來客戶及新增放款額度統計表
	 */
	@Override
	public List<Map<String, Object>> listLMS180R42_03(String thisMonthBgnDate,
			String thisMonthEndDate) {

		return this.getJdbc().queryForListWithMax("rpt.LMS180R42_03",
				new Object[] { thisMonthBgnDate, thisMonthEndDate });
	}

	/**
	 * 累計全行新增核准中小企業戶數最多之前五名分行
	 * 
	 * J-108-0040_05097_B1001 Web e-Loan企金授信新增108年度新核准往來客戶及新增放款額度統計表
	 */
	@Override
	public List<Map<String, Object>> listLMS180R42_04(String thisYearBgnDate,
			String thisYearEndDate) {

		return this.getJdbc().queryForListWithMax("rpt.LMS180R42_04",
				new Object[] { thisYearBgnDate, thisYearEndDate });
	}

	/**
	 * J-108-0040_05097_B1001 Web e-Loan企金授信新增108年度新核准往來客戶及新增放款額度統計表
	 */
	@Override
	public List<Map<String, Object>> listLMS180R42T(String bgnDate,
			String endDate) {

		return this.getJdbc().queryForListWithMax("rpt.LMS180R42T",
				new Object[] { bgnDate, endDate });
	}

	/**
	 * 國內分行每季新做無擔保中小企業戶授信額度明細表。 LMS180R43
	 * 
	 * M-108-0066_05097_B1001 Web e-Loan企金授信因應稽核處風險導向內部稽核制度填報風險監控指標需要新增企金處報表
	 * 
	 * @param bgnDate
	 * @param endDate
	 * @return
	 */
	@Override
	public List<Map<String, Object>> listLMS180R43(String bgnDate,
			String endDate) {
		return this.getJdbc().queryForListWithMax("rpt.LMS180R43",
				new Object[] { bgnDate, endDate });

	}

	/**
	 * 國內分行每季新作副總權限以上授信額度累計金額。 LMS180R44
	 * 
	 * M-108-0066_05097_B1001 Web e-Loan企金授信因應稽核處風險導向內部稽核制度填報風險監控指標需要新增企金處報表
	 * 
	 * @param bgnDate
	 * @param endDate
	 * @return
	 */
	@Override
	public List<Map<String, Object>> listLMS180R44(String bgnDate,
			String endDate) {
		return this.getJdbc().queryForListWithMax("rpt.LMS180R44",
				new Object[] { bgnDate, endDate });

	}

	/**
	 * 報表管理 -> 海外專用 918可全部海外分行 pdf10 10. 過去半年內董事會（或常董會）權限核定之企業戶授信案件名單 LMS9515R10
	 * J-108-0396_09301_B1001
	 */
	@Override
	public List<Map<String, Object>> queryPdf10Data(String ovUnitNo,
			String bgnDate, String endDate) {
		String where = "";
		List<String> list = new ArrayList<String>();
		list.add(bgnDate);
		list.add(endDate);
		// UtilConstants.BankNo.授管處=918
		if (Util.notEquals("918", ovUnitNo)) {
			where = " AND L120A.CASEBRID = ?";
			list.add(ovUnitNo);
		}

		String sql = getSqlBySqlId("rpt.Pdf10");
		Object[] args = new Object[] { where };
		sql = MessageFormat.format(sql, args);

		return this.getJdbc().queryAllForList(sql, list.toArray());
	}

	/**
	 * 依額度序號取得最新一筆報送J-108-0396-001
	 */
	@Override
	public Map<String, Object> findL230S01LastByCntrno(String cntrNo,
			String docstatus) {
		return this.getJdbc().queryForMap("L23S01A.findLastByCntrno",
				new Object[] { cntrNo, docstatus, cntrNo, docstatus });
	}

	// @Override
	// public List<Map<String, Object>> get_LMS_OTS_CSPERSCC_list(int
	// maxRecords) {
	//
	// String sql = MessageFormat.format(
	// getSqlBySqlId("LMS_OTS_CSPERSCC.get_LMS_OTS_CSPERSCC_list"),
	// new Object[] { maxRecords });
	// return this.getJdbc().queryAllForList(sql, new Object[] {});
	// }

	@Override
	public List<Map<String, Object>> getC101M01AByOwnBrid(String fristWord,
			String ownbrid) {
		return this.getJdbc().queryAllForList(
				getSqlBySqlId("LMS_c101m01a.getC101M01AByOwnBrid"),
				new Object[] { fristWord, ownbrid });
	}

	// @Override
	// public List<Map<String, Object>> get_OTS_CSPERSCC_NOT_RUN(int maxRecords,
	// String targetBrNo, String idPrefix) {
	// String inStr = " 'A','B','C' ";// ,'D','E','F','G' ";
	// return this.getJdbc().queryForListByCustParam("OTS_CSPERSCC.NOT_RUN",
	// new Object[] { inStr },
	// new Object[] { targetBrNo, idPrefix + "%" }, 0, maxRecords,
	// new EloanColumnMapRowMapper());
	//
	// }

	@Override
	public List<String> getL140s02fCMSid(String mainId, String itemType) {
		List<String> data = new ArrayList<String>();
		List<Map<String, Object>> datas = this.getJdbc().queryForList(
				"l140s02f.getCmsSrcOid", new Object[] { mainId, itemType });

		for (Map<String, Object> map : datas) {
			data.add(MapUtils.getString(map, "cmsSrcOid", ""));
		}

		return data;

	}

	/**
	 * LMS180R45 國內分行新核准往來企金客戶數統計表
	 * 
	 * J-108-0107_05097_B1001 國內分行新核准往來企金客戶數統計表(按分行列表)
	 * 
	 * Sheet1_分行彙總
	 * 
	 * @param bgnDate
	 * @param endDate
	 * @return
	 */
	@Override
	public List<Map<String, Object>> listLMS180R45_1(String thisMonthBgnDate,
			String thisMonthEndDate, String thisYearBgnDate,
			String thisYearEndDate) {
		return this.getJdbc().queryForListWithMax(
				"rpt.LMS180R45_1",
				new Object[] { thisYearBgnDate, thisYearEndDate,
						thisMonthBgnDate, thisMonthEndDate, thisMonthBgnDate,
						thisMonthEndDate });

	}

	/**
	 * LMS180R45 國內分行新核准往來企金客戶數統計表
	 * 
	 * J-108-0107_05097_B1001 國內分行新核准往來企金客戶數統計表(按分行列表)
	 * 
	 * Sheet2_分行明細
	 * 
	 * @param bgnDate
	 * @param endDate
	 * @return
	 */
	@Override
	public List<Map<String, Object>> listLMS180R45_2(String bgnDate,
			String endDate) {
		return this.getJdbc().queryForListWithMax("rpt.LMS180R45_2",
				new Object[] { bgnDate, endDate });

	}

	@Override
	public List<Map<String, Object>> doLmsBatch0019(String custId,
			String dupNo, String brId) {
		// String sqlText = MessageFormat.format(
		// getSqlBySqlId("BELDFM01.getSqlText"), new Object[] { custId,
		// dupNo, brId });
		// List<Map<String, Object>> r =
		// this.getJdbc().queryAllForList(sqlText);
		// String FINAL_SQL = r.get(0).get("FINAL_SQL").toString();

		// FIX CHECKMARX
		List<Map<String, Object>> maps = this.getJdbc().queryForListWithMax(
				"SELECT * FROM COM.BELDFM01", new Object[] {});
		List<String> sql = new ArrayList<String>();
		List<Object> params = new ArrayList<Object>();
		for (Map<String, Object> map : maps) {
			String sys_schema = StringEscapeUtils.escapeSql(MapUtils.getString(
					map, "SYS_SCHEMA"));
			String table_name = StringEscapeUtils.escapeSql(MapUtils.getString(
					map, "TABLE_NAME"));
			String column_id = StringEscapeUtils.escapeSql(MapUtils.getString(
					map, "COLUMN_ID"));
			String column_dupno = StringEscapeUtils.escapeSql(MapUtils
					.getString(map, "COLUMN_DUPNO"));
			String column_brid = StringEscapeUtils.escapeSql(MapUtils
					.getString(map, "COLUMN_BRID"));

			sql.add(MessageFormat
					.format("SELECT COUNT(1) AS SINGAL_CNT, ''{0}'' AS  SYS_SCHEMA FROM {0}.{1} WHERE {2}=? AND {3}=? AND {4}=? AND DOCSTATUS != ''DEL'' ",
							sys_schema, table_name, column_id, column_dupno,
							column_brid));
			params.add(custId);
			params.add(dupNo);
			params.add(brId);
		}
		StringBuffer finalSql = new StringBuffer();
		finalSql.append("SELECT SYS_SCHEMA, SUM(SINGAL_CNT) AS CNT  FROM ( ")
				.append(StringUtils.join(sql, " UNION "))
				.append(" ) Z GROUP BY SYS_SCHEMA");

		return this.getJdbc().queryAllForList(finalSql.toString(),
				params.toArray(new Object[0]));

	}

	/**
	 * 根據CUSTID,DUPNO,OWNID 設定刪除狀態及時間並保留原狀態 J-108-0086_05097_B1001
	 * e-Loan電子文件清理機制
	 * 
	 * @param dfm02
	 * @throws Exception
	 */
	@Override
	public void setDocStatusToDelByCustIdDupNo(BELDFM02 dfm02) throws Exception {
		List<BELDFM01> list = beldfm01Dao.findByIndex01("LMS", null);
		for (BELDFM01 dfm01 : list) {
			// UPDATE LMS.{0} SET DOCSTATUS_O=DOCSTATUS,DOCSTATUS=? WHERE {1} =
			// ? AND {2} = ? AND {3} = ? AND DOCSTATUS != ?
			String table_name = StringEscapeUtils.escapeSql(dfm01
					.getTable_name());
			String column_id = StringEscapeUtils
					.escapeSql(dfm01.getColumn_id());
			String column_dupno = StringEscapeUtils.escapeSql(dfm01
					.getColumn_dupno());
			String column_brid = StringEscapeUtils.escapeSql(dfm01
					.getColumn_brid());
			this.getJdbc().updateByCustParam(
					"cleanData.updateByCustIdDupNoOwnbrId",
					new Object[] { table_name, column_id, column_dupno,
							column_brid },
					new Object[] { "DEL", dfm02.getCustId(), dfm02.getDupNo(),
							dfm02.getOwnBrId(), "DEL" });
		}
		dfm02.setLms_done("Y");
		beldfm02Dao.save(dfm02);
	}

	/**
	 * 根據CUSTID,DUPNO,OWNID 設定刪除狀態及時間並保留原狀態 J-108-0086_05097_B1001
	 * e-Loan電子文件清理機制
	 * 
	 * @param dfm02
	 * @throws Exception
	 */
	@Override
	public void setDocStatusDelToDeleteTime(String currentTime)
			throws Exception {
		List<BELDFM01> list = beldfm01Dao.findByIndex01("LMS", null);
		for (BELDFM01 dfm01 : list) {
			// UPDATE LMS.{0} SET DELETEDTIME= ? WHERE DOCSTATUS = ?
			this.getJdbc().updateByCustParam(
					"cleanData.updateDeletedTimeByDocStatus",
					new Object[] { StringEscapeUtils.escapeSql(dfm01
							.getTable_name()) },
					new Object[] { currentTime, "DEL" });
		}

	}

	/**
	 * J-108-0086_05097_B1001 e-Loan電子文件清理機制
	 * 
	 * @param deletedTime
	 * @param brNo
	 * @param createTime
	 * @throws Exception
	 */
	@Override
	public void setDeleteTimeForReportL784m01a(String deletedTime, String brNo,
			String createTime) throws Exception {

		this.getJdbc().update("cleanData.updateDeletedTimeForReportL784m01a",
				new Object[] { deletedTime, brNo, brNo, createTime });

	}

	/**
	 * J-108-0086_05097_B1001 e-Loan電子文件清理機制
	 * 
	 * @param deletedTime
	 * @param brNo
	 * @param createTime
	 * @throws Exception
	 */
	@Override
	public void setDeleteTimeForReportLmsrpt(String deletedTime, String brNo,
			String createTime) throws Exception {

		this.getJdbc().update("cleanData.updateDeletedTimeForReportLmsrpt",
				new Object[] { deletedTime, brNo, createTime });

	}

	/**
	 * J-108-0116 共同行銷擔保品投保未結案明細表
	 */
	@Override
	public List<Map<String, Object>> listLMS180R46() {
		return this.getJdbc().queryForListWithMax("rpt.LMS180R46",
				new Object[] {});
	}

	@Override
	public List<Map<String, Object>> notifyMail_LMS180R46(String step,
			String user) {
		List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
		if (Util.equals("1", step)) {
			// 先取得需通知知user名單
			result = this.getJdbc().queryForListWithMax("L180R46A.getInfoUser",
					new Object[] {});
		} else if (Util.equals("2", step)) {
			// 依通知經辦取得名下案件
			result = this.getJdbc().queryForListWithMax(
					"L180R46A.getCaseByUser", new Object[] { user });
		}
		return result;
	}

	/**
	 * J-108-0210_05097_B1001 Web e-Loan企金授信新增上傳借款人基本資料的實收資本額
	 * 
	 * @param bgnDate
	 * @param endDate
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findL120m01aByEndDate(String bgnDate,
			String endDate) {
		return this.getJdbc().queryForListWithMax(
				"L120M01A.getApproveCaseByEndDate",
				new Object[] { bgnDate, endDate });

	}

	/**
	 * J-112-0456_05097_B1001 Web e-Loan授信系統增加簽報中屬不變的額度序號之簽報明細上送DW
	 * 
	 * @param bgnDate
	 * @param endDate
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findL120m01aByEndDateOnlyMainId(
			String bgnDate, String endDate) {
		return this.getJdbc().queryForListWithMax(
				"L120M01A.getApproveCaseByEndDateOnlyMainId",
				new Object[] { bgnDate, endDate });

	}

	/**
	 * J-113-0059 額度明細表約定融資註記欄位，配合於簽報書核准時寫入ELF506，回補舊案資料
	 * 
	 * @param cntrNo
	 * @return
	 */
	@Override
	public Map<String, Object> findLatestExceptFlagByCntrNo(String cntrNo) {
		return this.getJdbc().queryForMap(
				"L140M01A.getLatestExceptFlagByCntrNo",
				new Object[] { cntrNo, cntrNo });
	}
	
	/**
	 * J-113-0417 配合修改LLMLN998消金授信案件例外管理報表，ELF500新增寫入家庭所得，回補舊案資料
	 * 
	 * @param cntrNo
	 * @return
	 */
	@Override
	public Map<String, Object> findLatestFincomeByCntrNo(String cntrNo, String custId, String dupNo) {
		return this.getJdbc().queryForMap(
				"C120S01C.getLatestFincomeByCntrNo",
				new Object[] { custId, dupNo, cntrNo, custId, dupNo });
	}

	@Override
	public int updateBySQL(String sql, Object[] args) throws GWException {
		return this.getJdbc().updateBySQL(sql, args);
	}

	@Override
	public int updateByCustParam(String sqlId, Object[] msgFmtParam,
			Object[] args) throws GWException {
		return this.getJdbc().updateByCustParam(sqlId, msgFmtParam, args);
	}

	/**
	 * J-108-0166 企業社會責任貸放情形統計表 LMS180R47
	 */
	@Override
	public Map<String, Object> listLMS180R47_1(String bgn, String end) {
		// 因迄日需含當天故設定時間為23:59:59
		SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
		SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd 23:59:59");

		return this.getJdbc().queryForMap(
				"rpt.LMS180R47_1",
				new Object[] { sdf1.format(CapDate.getDate(bgn, "yyyy-MM-dd")),
						sdf2.format(CapDate.getDate(end, "yyyy-MM-dd")) });
	}

	@Override
	public List<Map<String, Object>> listLMS180R47_2(String bgn, String end) {
		// 因迄日需含當天故設定時間為23:59:59
		SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
		SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd 23:59:59");

		return this.getJdbc().queryForListWithMax(
				"rpt.LMS180R47_2",
				new Object[] { sdf1.format(CapDate.getDate(bgn, "yyyy-MM-dd")),
						sdf2.format(CapDate.getDate(end, "yyyy-MM-dd")) });
	}

	@Override
	public List<Map<String, Object>> listLMS180R47_2out(String bgn, String end) {
		// 因迄日需含當天故設定時間為23:59:59
		SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
		SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd 23:59:59");

		return this.getJdbc().queryForListWithMax(
				"rpt.LMS180R47_2out",
				new Object[] { sdf1.format(CapDate.getDate(bgn, "yyyy-MM-dd")),
						sdf2.format(CapDate.getDate(end, "yyyy-MM-dd")) });
	}

	@Override
	public List<Map<String, Object>> listLMS180R47_2kind(String mainId,
			String L140A_custId, String L140A_custDupNo) {
		return this.getJdbc().queryForListWithMax("rpt.LMS180R47_2kind",
				new Object[] { mainId, L140A_custId, L140A_custDupNo });
	}

	@Override
	public List<Map<String, Object>> listLMS180R47_2third(String bgn,
			String end, String L140A_custId, String L140A_custDupNo) {
		SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
		SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd 23:59:59");
		return this.getJdbc().queryForListWithMax(
				"rpt.LMS180R47_2third",
				new Object[] { sdf1.format(CapDate.getDate(bgn, "yyyy-MM-dd")),
						sdf2.format(CapDate.getDate(end, "yyyy-MM-dd")),
						L140A_custId, L140A_custDupNo, L140A_custId,
						L140A_custDupNo, L140A_custId, L140A_custDupNo });
	}

	@Override
	public Map<String, Object> listLMS180R47_3(String bgn, String end) {
		// 因迄日需含當天故設定時間為23:59:59
		SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
		SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd 23:59:59");

		return this.getJdbc().queryForMap(
				"rpt.LMS180R47_3",
				new Object[] { sdf1.format(CapDate.getDate(bgn, "yyyy-MM-dd")),
						sdf2.format(CapDate.getDate(end, "yyyy-MM-dd")) });
	}

	@Override
	public List<Map<String, Object>> listLMS180R47_4(String bgn, String end) {
		// 因迄日需含當天故設定時間為23:59:59
		SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
		SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd 23:59:59");

		return this.getJdbc().queryForListWithMax(
				"rpt.LMS180R47_4",
				new Object[] { sdf1.format(CapDate.getDate(bgn, "yyyy-MM-dd")),
						sdf2.format(CapDate.getDate(end, "yyyy-MM-dd")) });
	}

	@Override
	public List<Map<String, Object>> listLMS180R47_4factor(String bgn,
			String end, String custId, String dupNo) {
		// 因迄日需含當天故設定時間為23:59:59
		SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
		SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd 23:59:59");

		return this.getJdbc().queryForListWithMax(
				"rpt.LMS180R47_4factor",
				new Object[] { sdf1.format(CapDate.getDate(bgn, "yyyy-MM-dd")),
						sdf2.format(CapDate.getDate(end, "yyyy-MM-dd")),
						custId, dupNo });
	}

	@Override
	public List<Map<String, Object>> listLMS180R47_detail(String bgn, String end) {
		// 因迄日需含當天故設定時間為23:59:59
		SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
		SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd 23:59:59");

		return this.getJdbc().queryForListWithMax(
				"rpt.LMS180R47_detail",
				new Object[] { sdf1.format(CapDate.getDate(bgn, "yyyy-MM-dd")),
						sdf2.format(CapDate.getDate(end, "yyyy-MM-dd")) });
	}

	/**
	 * J-108-0243 微型企業
	 */
	@Override
	public List<Map<String, Object>> getJ10DefaultRateByType(String type) {
		return this.getJdbc().queryForListWithMax("getCesJ10DEFAULT_RATE",
				new Object[] { type });
	}

	/**
	 * J-108-0242_05097_B1001 Web e-Loan每月常董會報告事項彙總及申報案件數統計表新做案件之筆數統計再區分為新戶及原授信戶
	 * 
	 * @param cntrNo
	 * @param caseBrId
	 * @param startDate
	 * @param endDate
	 * @param docKind
	 * @param docType
	 * @param caseSeq
	 * @return
	 */
	@Override
	public List<Map<String, Object>> doLmsBatch0024_01(String cntrNo,
			String caseBrId, String startDate, String endDate, String docKind,
			String docType, BigDecimal caseSeq) {

		return this.getJdbc().queryForListWithMax("L120M01A.doLmsBatch0024_01",
				new Object[] { cntrNo, caseBrId, docKind, docType, caseSeq });
	}

	/**
	 * J-107-0342_05097_B1003 Web e-Loan授信系統新增覆審考核相關報表
	 * 
	 * 對區域營運中心授信覆審作業之管理績效考核表(附表一)_合計
	 * 
	 * @param bgnDate
	 * @param endDate
	 * @return
	 */
	@Override
	public List<Map<String, Object>> listLMS180R48_total(String bgnDate,
			String endDate) {

		return this.getJdbc().queryForListWithMax("rpt.LMS180R48.total",
				new Object[] { bgnDate, endDate });
	}

	/**
	 * J-107-0342_05097_B1003 Web e-Loan授信系統新增覆審考核相關報表
	 * 
	 * 對區域營運中心授信覆審作業之管理績效考核表(附表二)_明細
	 * 
	 * @param mdFlag
	 * @param bgnDate
	 * @param endDate
	 * @return
	 */
	@Override
	public List<Map<String, Object>> listLMS180R48_detail(String mdFlag,
			String bgnDate, String endDate) {

		StringBuffer sql = new StringBuffer("");
		if ("Y".equals(Util.trim(mdFlag))) {
			sql.append(" and isMdDue = 'Y' ");
		} else {
			sql.append(" and isMdDue != 'Y' ");
		}

		return this.getJdbc().queryForAllListByCustParam(
				"rpt.LMS180R48.detail", new Object[] { sql.toString() },
				new Object[] { bgnDate, endDate });

	}

	/**
	 * J-107-0342_05097_B1003 Web e-Loan授信系統新增覆審考核相關報表
	 * 
	 * @param brno
	 * @param custId
	 * @param dupNo
	 * @param baseDate
	 * @return
	 */
	@Override
	public Map<String, Object> findC241m01aClosestReChkRpt(String brno,
			String custId, String dupNo, String baseDate) {
		return this.getJdbc().queryForMap("LMS.C241M01A.findClosestReChkRpt",
				new Object[] { brno, custId, dupNo, baseDate });
	}

	@Override
	public List<Map<String, Object>> findCLS180R23_ratePlan20(
			String l120m01a_docStatus, String cntrNo) {
		return this.getJdbc().queryForListWithMax("findCLS180R23_ratePlan20",
				new String[] { l120m01a_docStatus + "%", cntrNo + "%" });
	}

	@Override
	public List<Map<String, Object>> findCLS180R25(String applyStatus,
			String brNo, String applyBegDate, String applyEndDate) {
		String p_begDate = applyBegDate;
		String p_endDate = applyEndDate;
		if (Util.isEmpty(p_begDate)) {
			p_begDate = "1911-01-01";
		}
		if (Util.isEmpty(p_endDate)) {
			p_endDate = "9999-12-31";
		}
		String ts_beg = p_begDate + " 00:00:00";
		String ts_end = p_endDate + " 23:59:59";
		return this.getJdbc().queryForListWithMax("findCLS180R25",
				new String[] { applyStatus + "%", brNo + "%", ts_beg, ts_end });
	}

	/**
	 * LMS180R49 國內分行新核准往來企金客戶數統計表(含舊戶)
	 * 
	 * J-108-0272_10702_B1001 Web e-Loan 新增「國內分行新核准往來企金客戶數統計表(含舊戶)」報表
	 * 
	 * Sheet1_營運中心彙總
	 * 
	 * @param bgnDate
	 * @param endDate
	 * @return
	 */
	@Override
	public List<Map<String, Object>> listLMS180R49_1(String thisMonthBgnDate,
			String thisMonthEndDate, String thisYearBgnDate,
			String thisYearEndDate, String lastYearBgnDate,
			String lastYearEndDate, String lastMonthBgnDate,
			String lastMonthEndDate) {
		return this.getJdbc().queryForListWithMax(
				"rpt.LMS180R49_1",
				new Object[] { lastYearBgnDate, thisYearEndDate,
						thisMonthBgnDate, thisMonthEndDate, thisMonthBgnDate,
						thisMonthEndDate, thisYearBgnDate, thisYearEndDate,
						thisYearBgnDate, thisYearEndDate, lastMonthBgnDate,
						lastMonthEndDate, lastMonthBgnDate, lastMonthEndDate,
						lastYearBgnDate, lastYearEndDate, lastYearBgnDate,
						lastYearEndDate });

	}

	/**
	 * LMS180R49 國內分行新核准往來企金客戶數統計表(含舊戶)
	 * 
	 * J-108-0272_10702_B1001 Web e-Loan 新增「國內分行新核准往來企金客戶數統計表(含舊戶)」報表
	 * 
	 * Sheet2_各組彙總
	 * 
	 * @param bgnDate
	 * @param endDate
	 * @return
	 */
	@Override
	public List<Map<String, Object>> listLMS180R49_2(String thisMonthBgnDate,
			String thisMonthEndDate, String thisYearBgnDate,
			String thisYearEndDate, String lastYearBgnDate,
			String lastYearEndDate, String lastMonthBgnDate,
			String lastMonthEndDate) {
		return this.getJdbc().queryForListWithMax(
				"rpt.LMS180R49_2",
				new Object[] { lastYearBgnDate, thisYearEndDate,
						thisMonthBgnDate, thisMonthEndDate, thisMonthBgnDate,
						thisMonthEndDate, thisYearBgnDate, thisYearEndDate,
						thisYearBgnDate, thisYearEndDate, lastMonthBgnDate,
						lastMonthEndDate, lastMonthBgnDate, lastMonthEndDate,
						lastYearBgnDate, lastYearEndDate, lastYearBgnDate,
						lastYearEndDate });

	}

	/**
	 * LMS180R49 國內分行新核准往來企金客戶數統計表(含舊戶)
	 * 
	 * J-108-0272_10702_B1001 Web e-Loan 新增「國內分行新核准往來企金客戶數統計表(含舊戶)」報表
	 * 
	 * Sheet3_分行明細
	 * 
	 * @param bgnDate
	 * @param endDate
	 * @return
	 */
	@Override
	public List<Map<String, Object>> listLMS180R49_3(String bgnDate,
			String endDate) {
		return this.getJdbc().queryForListWithMax("rpt.LMS180R49_3",
				new Object[] { bgnDate, endDate });

	}

	/**
	 * M-108-0296_05097_B1001 Web e-Loan配合總處經費分攤提供所需資料
	 * 
	 * 授信審查(企、消金)
	 * 
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	@Override
	public List<Map<String, Object>> doLmsBatch0025_01(String startDate,
			String endDate) {

		return this.getJdbc().queryForListWithMax("L120M01A.doLmsBatch0025_01",
				new Object[] { startDate, endDate, startDate, endDate });
	}

	/**
	 * M-108-0296_05097_B1001 Web e-Loan配合總處經費分攤提供所需資料
	 * 
	 * 授信覆審(企金)
	 * 
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	@Override
	public List<Map<String, Object>> doLmsBatch0025_02(String startDate,
			String endDate) {

		return this.getJdbc().queryForListWithMax("L170M01A.doLmsBatch0025_02",
				new Object[] { startDate, endDate });
	}

	/**
	 * M-108-0296_05097_B1001 Web e-Loan配合總處經費分攤提供所需資料
	 * 
	 * 授信覆審(消金)
	 * 
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	@Override
	public List<Map<String, Object>> doLmsBatch0025_03(String startDate,
			String endDate) {

		return this.getJdbc().queryForListWithMax("C241M01A.doLmsBatch0025_03",
				new Object[] { startDate, endDate });
	}

	/**
	 * M-108-0296_05097_B1003 Web e-Loan配合總處經費分攤提供所需資料
	 * 
	 * 授信審查(企、消金)
	 * 
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	@Override
	public List<Map<String, Object>> doLmsBatch0025_04(String startDate,
			String endDate) {

		return this.getJdbc().queryForListWithMax("L120M01A.doLmsBatch0025_04",
				new Object[] { startDate, endDate, startDate, endDate });
	}

	/**
	 * J-108-0303 連鎖店Chain store 取得主事業體額度序號
	 */
	@Override
	public Map<String, Object> findL140m01vById(String custId, String dupNo) {
		return this.getJdbc().queryForMap("L140M01V.findMainBizCntrNo",
				new Object[] { custId, dupNo });
	}

	/**
	 * J-108-0303 連鎖店Chain store 檢核
	 */
	@Override
	public Map<String, Object> chkMainBiz(String mCntrNo, String custId,
			String dupNo, String type) {
		Map<String, Object> result = new HashMap<String, Object>();
		if (Util.equals("1", type)) {
			// 主事業體僅能一個額度序號
			result = this.getJdbc().queryForMap("L140M01V.findMainBizCntrNo",// "L140M01V.chkMainBiz_1",
					new Object[] { custId, dupNo });
		} else if (Util.equals("2", type)) {
			// 主事業體無簽案
			// 1. distinct 子戶ID 2.已核准之加盟總額度
			result = this.getJdbc().queryForMap("L140M01V.chkMainBiz_2",
					new Object[] { custId, dupNo });
		} else if (Util.equals("3", type)) {
			// 主事業體有簽案 - 不超過主事業體控管
			result = this.getJdbc().queryForMap("L140M01V.chkMainBiz_3",
					new Object[] { mCntrNo, mCntrNo });
		}
		return result;
	}

	@Override
	public String chkChainStore(String cntrNo) {
		Map<String, Object> result = new HashMap<String, Object>();
		String chainStore = "";
		result = this.getJdbc().queryForMap("L140M01V.chkChainStore",
				new Object[] { cntrNo });
		if (result != null) {
			chainStore = Util.nullToSpace(result.get("CHAINSTORE"));
		}
		return chainStore;
	}

	/**
	 * J-108-0304 投資台灣三大方案專案貸款執行情形統計表 LMS180R50
	 */
	@Override
	public Map<String, Object> listLMS180R50_1(String bnDate, String endDate,
			String projClass, String brType, String branch) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

		Map<String, Object> result = new HashMap<String, Object>();
		String w1 = "";
		List<String> list = new ArrayList<String>();

		list.add(projClass);
		list.add(sdf.format(CapDate.getDate(endDate, "yyyy-MM-dd")));
		// if (Util.equals("1", brType)) {
		// w1 = "";
		// } else if (Util.equals("2", brType)) {
		// w1 =
		// " AND L12A.OWNBRID IN (SELECT C.BRNO AS BRNO  FROM COM.BELSBRN C  WHERE C.BRNGROUP =? AND C.BREKFLAG <> 'Y' ORDER BY C.BRNO)";
		// list.add(branch);
		// } else if (Util.equals("3", brType)) {
		// w1 = " AND L12A.OWNBRID =?";
		// list.add(branch);
		// } else {
		// w1 = "";
		// }
		list.add(sdf.format(CapDate.getDate(bnDate, "yyyy-MM-dd")));

		String sql = sqlp.getValue("rpt.LMS180R50_1", "rpt.LMS180R50_1");
		sql = MessageFormat.format(sql, new Object[] { w1 });
		List<Map<String, Object>> r = this.getJdbc().queryAllForList(sql,
				list.toArray());
		for (Map<String, Object> map : r) {
			result = map;
		}

		return result;
	}

	@Override
	public List<Map<String, Object>> listLMS180R50_2(String bnDate,
			String endDate, String projClass, String brType, String branch) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

		String w1 = "";
		List<String> list = new ArrayList<String>();

		list.add(projClass);
		list.add(sdf.format(CapDate.getDate(bnDate, "yyyy-MM-dd")));
		list.add(sdf.format(CapDate.getDate(endDate, "yyyy-MM-dd")));
		// if (Util.equals("1", brType)) {
		// w1 = "";
		// } else if (Util.equals("2", brType)) {
		// w1 =
		// " AND L12A.CASEBRID IN (SELECT C.BRNO AS BRNO  FROM COM.BELSBRN C  WHERE C.BRNGROUP =? AND C.BREKFLAG <> 'Y' ORDER BY C.BRNO)";
		// list.add(branch);
		// } else if (Util.equals("3", brType)) {
		// w1 = " AND L12A.CASEBRID =?";
		// list.add(branch);
		// } else {
		// w1 = "";
		// }

		String sql = sqlp.getValue("rpt.LMS180R50_2", "rpt.LMS180R50_2");
		sql = MessageFormat.format(sql, new Object[] { w1 });
		List<Map<String, Object>> r = this.getJdbc().queryAllForList(sql,
				list.toArray());

		return r;
	}

	@Override
	public List<Map<String, Object>> listCntr_c340m01a_ctrType_1_match(
			String l120m01a_docstatus, String brNo, String custId, String dupNo) {
		return this.getJdbc().queryForList("C340M01A.ctrType_1_match",
				new Object[] { l120m01a_docstatus + "%", brNo, custId, dupNo },
				0, Integer.MAX_VALUE);
	}

	@Override
	public List<Map<String, Object>> listCntr_c340m01a_ctrType_2_match(
			String l120m01a_docstatus, String brNo, String custId, String dupNo) {
		return this.getJdbc().queryForList("C340M01A.ctrType_2_match",
				new Object[] { l120m01a_docstatus + "%", brNo, custId, dupNo },
				0, Integer.MAX_VALUE);
	}

	@Override
	public List<Map<String, Object>> listCntr_c340m01a_ctrType_3_match(
			String l120m01a_docstatus, String brNo, String custId, String dupNo) {
		return this.getJdbc().queryForList("C340M01A.ctrType_3_match",
				new Object[] { l120m01a_docstatus + "%", brNo, custId, dupNo },
				0, Integer.MAX_VALUE);
	}

	@Override
	public List<Map<String, Object>> listCntr_c340m01a_ctrType_A_match(
			String l120m01a_docstatus, String brNo, String custId, String dupNo) {
		return this.getJdbc().queryForList("C340M01A.ctrType_A_match",
				new Object[] { l120m01a_docstatus + "%", brNo, custId, dupNo },
				0, Integer.MAX_VALUE);
	}

	@Override
	public List<Map<String, Object>> listCntr_c340m01a_ctrType_B_match(
			String l120m01a_docstatus, String brNo, String custId, String dupNo) {
		return this.getJdbc().queryForList("C340M01A.ctrType_B_match",
				new Object[] { l120m01a_docstatus + "%", brNo, custId, dupNo },
				0, Integer.MAX_VALUE);
	}

	@Override
	public List<Map<String, Object>> listCntr_c340m01a_ctrType_S_match(
			String l120m01a_docstatus, String brNo, String custId, String dupNo) {
		return this.getJdbc().queryForList("C340M01A.ctrType_S_match",
				new Object[] { l120m01a_docstatus + "%", brNo, custId, dupNo },
				0, Integer.MAX_VALUE);
	}

	@Override
	public List<Map<String, Object>> listCntr_c340m01a_ctrType_L_match(
			String l120m01a_docstatus, String brNo, String custId, String dupNo) {
		return this.getJdbc().queryForList("C340M01A.ctrType_L_match",
				new Object[] { l120m01a_docstatus + "%", brNo, custId, dupNo },
				0, Integer.MAX_VALUE);
	}

	/**
	 * G-107-0642_05097_B1001 配合羅勇分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權
	 * 
	 * @param exDate
	 * @param fBranch
	 * @param tBranch
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findBrToBr01ListByExDate(String exDate) {
		return this.getJdbc().queryForList(
				"BRTOBR01.findTransferBrNoAndCustIdByExDate",
				new Object[] { exDate }, 0, Integer.MAX_VALUE);
	}

	/**
	 * G-107-0642_05097_B1001 配合羅勇分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權
	 * 
	 * @param exDate
	 * @param fBranch
	 * @param tBranch
	 * @param version
	 */
	@Override
	public void deleteBrToBr01ByExDate(String exDate) {
		this.getJdbc().update("BRTOBR01.delByExDate", new Object[] { exDate });
	}

	/**
	 * J-109-0025 愛企貸專案統計表 LMS180R51
	 */
	@Override
	public List<Map<String, Object>> listLMS180R51(String bgnDate,
			String endDate, String projClass) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

		String bgn = sdf.format(CapDate.getDate(bgnDate, "yyyy-MM-dd"));
		String end = sdf.format(CapDate.getDate(endDate, "yyyy-MM-dd"));

		return this.getJdbc().queryForListWithMax("rpt.LMS180R51",
				new Object[] { projClass, bgn, end });
	}

	/**
	 * 本月全行新增核准企業戶數最多之前五名分行
	 * 
	 * J-109-0054_05097_B1001
	 * 國內分行新核准往來企金客戶數(LMS180R45)、新核准往來客戶及新增放款額度統計表(LMS180R42)內容修改
	 */
	@Override
	public List<Map<String, Object>> listLMS180R42_05(String thisMonthBgnDate,
			String thisMonthEndDate) {

		return this.getJdbc().queryForListWithMax("rpt.LMS180R42_05",
				new Object[] { thisMonthBgnDate, thisMonthEndDate });
	}

	/**
	 * 累計全行新增核准企業戶數最多之前五名分行
	 * 
	 * J-109-0054_05097_B1001
	 * 國內分行新核准往來企金客戶數(LMS180R45)、新核准往來客戶及新增放款額度統計表(LMS180R42)內容修改
	 */
	@Override
	public List<Map<String, Object>> listLMS180R42_06(String thisYearBgnDate,
			String thisYearEndDate) {

		return this.getJdbc().queryForListWithMax("rpt.LMS180R42_06",
				new Object[] { thisYearBgnDate, thisYearEndDate });
	}

	@Override
	public Map<String, Object> findVariousTotalFee(String mainId) {

		List<Map<String, Object>> list = this.getJdbc().queryForList(
				"L140M01R.getVariousFeeForSimplifyingCreditSignBook",
				new Object[] { mainId });
		Map<String, Object> m = new HashMap<String, Object>();
		for (Map<String, Object> map : list) {
			m.put(String.valueOf(map.get("feeno")), map.get("totalFee"));
		}

		return m;
	}

	/**
	 * 因應嚴重特殊傳染性肺炎影響事業資金紓困方貸款統計表--彙總
	 * 
	 * J-109-0077_05097_B1001 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
	 */
	@Override
	public List<Map<String, Object>> listLMS180R52_01(String brNos,
			String condition) {
		List<Object> params = new ArrayList<Object>();
		if (Util.isNotEmpty(condition)) {
			params.add(brNos);
		}
		return this.getJdbc().queryForAllListByCustParam("rpt.LMS180R52_01",
				new Object[] { condition }, params.toArray(new Object[0]));
	}

	/**
	 * 因應嚴重特殊傳染性肺炎影響事業資金紓困方貸款統計表--明細
	 * 
	 * J-109-0077_05097_B1001 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
	 */
	@Override
	public List<Map<String, Object>> listLMS180R52_02(String brNos,
			String condition) {
		List<Object> params = new ArrayList<Object>();
		if (Util.isNotEmpty(condition)) {
			params.add(brNos);
		}
		return this.getJdbc().queryForAllListByCustParam("rpt.LMS180R52_02",
				new Object[] { condition }, params.toArray(new Object[0]));

	}

	@Override
	public Page<Map<String, Object>> findCesWithBankSimpleCreditRatio(
			ISearch search, String brId, String custId, String dupNo) {
		return this.getJdbc().queryForPage(search,
				"C120S01A.getWithBankSimpleCreditRatio",
				new Object[] { brId, custId, dupNo, brId, custId, dupNo });
	}

	@Override
	public Page<Map<String, Object>> findCesWithBankSimpleCreditRatio(
			ISearch search, String cesMainId) {
		return this.getJdbc().queryForPage(search,
				"C120S01A.getWithBankSimpleCreditRatioByCesMainId",
				new Object[] { cesMainId });
	}

	@Override
	public Map<String, Object> findLastPrint_L140M01A(String cntrNo) {
		return this.getJdbc().queryForMap("findLastPrint_L140M01A",
				new String[] { cntrNo });
	}

	/**
	 * J-110-0005 其他敘做條件to貸後管理
	 */
	@Override
	public List<Map<String, Object>> findL140S09BtoPostLoanByL140M01A(String oid) {
		return this.getJdbc().queryForListWithMax(
				"L140S09B.findPostLoanByL140M01A", new Object[] { oid });
	}

	// J-112-0307
	// 於eloan貸後管理追蹤檢核表維護功能中，新增一鍵生成公司訪問紀錄表及借戶、負責人、所屬企業集團等關係戶於本行各項業務往來彙總表。
	@Override
	public Map<String, Object> findLatestL260S01D(String l260m01dOid,
			String custId, String dupNo) {
		return this.getJdbc().queryForMap("findLatestL260S01D",
				new Object[] { custId, dupNo });
	}
	
	@Override
	public List<Map<String, Object>> findLatestL260S01F(String cntrNo,
			String from602SUid, Date from602SApptime,
			BigDecimal from602SSeqno) {
		return this.getJdbc().queryForListWithMax(
				"findLatestL260S01F",
				new Object[] { cntrNo, from602SUid, from602SApptime,
						from602SSeqno });
	}

	// 2020/04 配合新冠肺炎紓困貸款專案，新增 J.純紓困貸款戶之首次覆審。
	@Override
	public Map<String, Object> getL161S01A_IsRescue(String cntrNo) {
		return this.getJdbc().queryForMap("getL161S01A_IsRescue",
				new Object[] { cntrNo });
	}

	/**
	 * 法令遵循自評授信案件明細報表-簽報書
	 * 
	 * J-109-0132_05097_B1001 e-Loan授信系統新增「法令遵循自評檢核表」之抽測筆數所需之各檢核項目授信案件明細報表
	 */
	@Override
	public List<Map<String, Object>> listLMS180R53_01(String bgnDate,
			String endDate) {

		return this.getJdbc().queryForListWithMax("rpt.LMS180R53",
				new Object[] { bgnDate, endDate });
	}

	@Override
	public List<Map<String, Object>> getEloanDataForLaborReliefLoanSummaryReport(
			String applyDate) {
		return this.getJdbc().queryForList(
				"getLaborReliefLoanSummaryReportData",
				new Object[] { applyDate, applyDate, applyDate });
	}

	@Override
	public List<Map<String, Object>> getEloanDataForLaborReliefLoanDetailReport(
			String applyDate, String custId) {
		return this.getJdbc().queryForListWithMax(
				"getLaborReliefLoanDetailReportData",
				new Object[] { applyDate, custId });
	}

	/**
	 * 引進當日已完成掃描/調查名單
	 * 
	 * @param custId
	 * @param dupNo
	 * @param qDate
	 * @param caseBrId
	 * @param mainId
	 * @param ncResultStr
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findL120s09bNcResultDoneByMainIdAndCustId(
			String custId, String dupNo, String qDateBgn, String qDateEnd,
			String caseBrId, String mainId, String[] ncResultStr) {
		String ncResultStrParam = Util.genSqlParam(ncResultStr);
		List<Object> params = new ArrayList<Object>();
		params.addAll(Arrays.asList(custId, dupNo, qDateBgn, qDateEnd,
				qDateBgn, qDateEnd, caseBrId, mainId));
		params.addAll(Arrays.asList(ncResultStr));
		return this.getJdbc().queryForAllListByCustParam(
				"L120S09B.findNcResultDoneByMainIdAndCustId",
				new Object[] { ncResultStrParam },
				params.toArray(new Object[0]));

	}

	@Override
	public List<Map<String, Object>> getOnlineApplicationDateForLaborReliefLoanDetailReportData(
			String applyDate, String custId) {
		return this.getJdbc().queryForListWithMax(
				"getOnlineApplicationDateForLaborReliefLoanDetailReportData",
				new Object[] { applyDate, custId });
	}

	@Override
	public List<Map<String, Object>> getLaborReliefPackage(Date laborVerDate) {
		return this.getJdbc().queryForList("C122M01A.GET_LABOR_RELIEF_PACKAGE",
				new Object[] { laborVerDate }, 0, Integer.MAX_VALUE);
	}

	@Override
	public List<Map<String, Object>> genLaborTrustData(int laborVer,
			Date laborVerDate) {

		String sql = "C122M01A.GET_LABOR_SEND_TRUST_V2";
		return this.getJdbc().queryForList(sql,
				new Object[] { laborVer, laborVerDate, laborVerDate }, 0,
				Integer.MAX_VALUE);
	}

	/**
	 * 信保整批貸款申請書回饋檔查詢
	 */
	@Override
	public List<Map<String, Object>> findC124m01aByCustIdBatchDate(
			String ownBrId, String custId, String bgnDate, String endDate) {

		StringBuffer sql = new StringBuffer();
		List<Object> params = new ArrayList<Object>();

		if (Util.isNotEmpty(ownBrId)) {
			sql.append(" and ownBrId = ? ");
			params.add(ownBrId);
		}

		if (Util.isNotEmpty(custId)) {
			sql.append(" and custId = ? ");
			params.add(custId);
		}

		if (!"".equals(Util.trim(bgnDate))) {
			sql.append(" and DATE(batchDate) >= ? ");
			params.add(bgnDate);
		}
		if (!"".equals(Util.trim(endDate))) {
			sql.append(" and DATE(batchDate) <= ? ");
			params.add(endDate);
		}

		return this.getJdbc().queryForListByCustParam(
				"C124M01A.selByCustIdAndBatchDate",
				new Object[] { sql.toString() }, params.toArray(new Object[0]),
				0, Integer.MAX_VALUE, new EloanColumnMapRowMapper());

	}

	/**
	 * 信保整批貸款通知單回饋檔查詢
	 */
	@Override
	public List<Map<String, Object>> findC125m01aByCustIdBatchDate(
			String ownBrId, String custId, String bgnDate, String endDate) {

		StringBuffer sql = new StringBuffer();
		List<Object> params = new ArrayList<Object>();

		if (Util.isNotEmpty(ownBrId)) {
			sql.append(" and ownBrId = ? ");
			params.add(ownBrId);
		}

		if (Util.isNotEmpty(custId)) {
			sql.append(" and custId = ? ");
			params.add(custId);
		}

		if (!"".equals(Util.trim(bgnDate))) {
			sql.append(" and DATE(batchDate) >= ? ");
			params.add(bgnDate);
		}
		if (!"".equals(Util.trim(endDate))) {
			sql.append(" and DATE(batchDate) <= ? ");
			params.add(endDate);
		}

		return this.getJdbc().queryForListByCustParam(
				"C125M01A.selByCustIdAndBatchDate",
				new Object[] { sql.toString() }, params.toArray(new Object[0]),
				0, Integer.MAX_VALUE, new EloanColumnMapRowMapper());

	}

	/**
	 * 「法令遵循自評授信案件明細報表」
	 * 
	 * @param cntrNo
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findLms180r53IndustryLandByCntrNo(
			String cntrNo) {
		return this.getJdbc().queryForListWithMax("rpt.LMS180R53.selByCntrNo",
				new Object[] { cntrNo });
	}

	@Override
	public Map<String, Object> getCustDataFormOnline(String custId, String dupNo) {
		return this.getJdbc().queryForMap("getCustDataFormOnline",
				new Object[] { custId, dupNo });
	}

	@Override
	public Map<String, Object> getCustDataFormBase(String custId, String dupNo) {
		return this.getJdbc().queryForMap("getCustDataFormBase",
				new Object[] { custId, dupNo });
	}

	@Override
	public Map<String, Object> getLastImporterData(String importerNo) {
		return this.getJdbc().queryForMap("getLastImporterData",
				new Object[] { importerNo });
	}

	@Override
	public List<Map<String, Object>> findActiveMajorByCertNo(String year,
			String word, String no) {

		return this.getJdbc().queryForList(
				"C900M01H.getLaaList",
				new Object[] { year, word, no, FlowDocStatusEnum.已核准.getCode(),
						FlowDocStatusEnum.待解除.getCode() });

	}

	@Override
	public List<Map<String, Object>> findCLS1220R07_1(String ownBrId) {
		return this.getJdbc().queryForListWithMax("findCLS1220R07_1",
				new Object[] { ownBrId + "%" });
	}

	@Override
	public List<Map<String, Object>> findCLS1220R07_2(String ownBrId,
			String createTimeSince) {
		return this.getJdbc().queryForListWithMax("findCLS1220R07_2",
				new Object[] { ownBrId + "%", createTimeSince });
	}

	@Override
	public List<Map<String, Object>> findCLS1220R08(String ownBrId,
			String ploanPlan, String csc_applyTS_beg, String csc_applyTS_end) {
		// AND C122.PLOANPLAN LIKE ?
		if (!Util.isNotEmpty(ploanPlan)) {
			// 產中鋼進件EXCEL，不指定ploanPlan則為C開頭全部
			ploanPlan = "C%";
		}

		// AND C122.APPLYTS BETWEEN ? AND ?
		if (Util.isEmpty(csc_applyTS_beg)) {
			// 起日預設值
			csc_applyTS_beg = "1911-01-01";
		}
		if (Util.isEmpty(csc_applyTS_end)) {
			// 迄日預設值
			csc_applyTS_end = "9999-12-31";
		}

		String sqlBeg = csc_applyTS_beg + " 00:00:00";
		String sqlEnd = csc_applyTS_end + " 23:59:59";

		return this.getJdbc().queryForListWithMax("findCLS1220R08",
				new Object[] { ownBrId, ploanPlan, sqlBeg, sqlEnd });
	}

	@Override
	public List<Map<String, Object>> findCLS1220R12(String ownBrId,
			String grpCntrNo) {
		StringBuffer sql = new StringBuffer();
		List<Object> params = new ArrayList<Object>();
		params.add(ownBrId);
		if (Util.isNotEmpty(grpCntrNo)) {
			sql.append(" and GRPCNTRNO = ? ");
			params.add(grpCntrNo);
		}

		return this.getJdbc().queryForListByCustParam("findCLS1220R12",
				new Object[] { sql.toString() }, params.toArray(new Object[0]),
				0, Integer.MAX_VALUE, new EloanColumnMapRowMapper());
	}

	@Override
	public Map<String, String> getGrpCntrNoList(String brno) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForList(
				"getGrpCntrNoList", new Object[] { brno });

		HashMap<String, String> map = new HashMap<String, String>();
		for (Map<String, Object> data : rowData) {
			map.put(Util.trim(data.get("GRPCNTRNO")),
					Util.trim(data.get("GRPCNTRNO")));
		}

		return map;
	}

	@Override
	public List<Map<String, Object>> query_cntrNo_for_C122M01A_ApplyKindPE(
			String c122_mainId) {
		return this.getJdbc().queryForListWithMax(
				"C122M01A.query_cntrNo_for_C122M01A_ApplyKindPE",
				new Object[] { c122_mainId });
	}

	@Override
	public List<Map<String, Object>> find_com_bgwdata_by_logsno(String logsno) {
		return this.getJdbc().queryForList("find_com_bgwdata_by_logsno",
				new Object[] { logsno });
	}

	@Override
	public List<Map<String, Object>> find_com_bgwdata_by_data(String data) {
		return this.getJdbc().queryForList("find_com_bgwdata_by_data",
				new Object[] { data });
	}

	@Override
	public List<Map<String, Object>> find_com_bgwdata_serviceId_txId_between_reqtime(
			String serviceId, String txId, String reqtimeBeg,
			String reqtimeEnd, String sno) {
		return this.getJdbc().queryForListWithMax(
				"find_com_bgwdata_serviceId_txId_between_reqtime",
				new Object[] { serviceId, txId, reqtimeBeg, reqtimeEnd, sno });
	}

	@Override
	public List<Map<String, Object>> getAccumulatedMortgageAppropriationAmount(
			String[] groupLoanMasterNoArr) {
		List<Object> params = new ArrayList<Object>();
		params.addAll(Arrays.asList("13506200", "14501500", "13500100",
				"14501000", "7", "8", "060", "05O"));
		params.addAll(Arrays.asList(groupLoanMasterNoArr));

		return this.getJdbc().queryForListByCustParam(
				"CLS180R50.getAccumulatedMortgageLoanAmount",
				new Object[] { Util.genSqlParam(groupLoanMasterNoArr) },
				params.toArray(new Object[0]));
	}

	@Override
	public Map<String, Object> getSmallBussInfo(String mainId) {
		return this.getJdbc().queryForMap("getSmallBussInfo",
				new Object[] { mainId });
	}

	@Override
	public Map<String, Object> getSmallBussInfoByL140m01aOid(String mainId,
			String cntrNoOid) {
		return this.getJdbc().queryForMap("getSmallBussInfoByL140m01aOid",
				new Object[] { mainId, cntrNoOid });
	}

	/**
	 * 取得資信簡表J10資料
	 * 
	 * @param cesMainIds
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findCesJ10(String... cesMainIds) {
		String cesMainIdParam = Util.genSqlParam(cesMainIds);
		return this.getJdbc().queryForListByCustParam("findCesJ10",
				new Object[] { cesMainIdParam }, cesMainIds);

	}

	/**
	 * J-109-0235_05097_B1003 Web e-loan國內企金授信新增兆元振興融資方案
	 * 
	 * @param bgnDate
	 * @param endDate
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findAllReviveFromL140m01a(String bgnDate,
			String endDate) {

		return this.getJdbc().queryForListWithMax("L140M01A.selRevive",
				new Object[] { bgnDate, endDate, bgnDate, endDate });

	}

	/**
	 * J-109-0235_05097_B1003 Web e-loan國內企金授信新增兆元振興融資方案
	 */
	@Override
	public List<Map<String, Object>> listLMS180R54T(String bgnDate,
			String endDate) {

		return this.getJdbc().queryForListWithMax("rpt.LMS180R54T",
				new Object[] { bgnDate, endDate });
	}

	/**
	 * J-109-0235_05097_B1003 Web e-loan國內企金授信新增兆元振興融資方案
	 */
	@Override
	public List<Map<String, Object>> findSysColumnsRemarks(String tableName) {
		return this.getJdbc().queryForListWithMax("sysibm.getSysColumns",
				new Object[] { tableName });
	}

	/**
	 * J-109-0235_05097_B1004 Web e-loan國內企金授信新增兆元振興融資方案
	 * 
	 * @param propertyStr
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findL140m01aHasReviveByCntrNo(
			String cntrNo, String propertyStr) {

		String sql = "";
		List<Object> params = new ArrayList<Object>();
		params.add(cntrNo);
		if (Util.isNotEmpty(propertyStr)) {

			String[] propertyStrArr = propertyStr.split(",");
			String[] sqlParams = new String[propertyStrArr.length];
			Arrays.fill(sqlParams, " T1.PROPERTY like ? ");
			sql = " AND (" + StringUtils.join(sqlParams, " or ") + ")";

			for (String s : propertyStrArr) {
				params.add("%|" + s + "|%");
			}
		}

		return this.getJdbc().queryForListByCustParam(
				"L140M01A.findHasReviveByCntrNo", new Object[] { sql },
				params.toArray(new Object[0]));

	}

	/**
	 * 兆元振興融資方案辦理情形統計表
	 * 
	 * J-109-0235_05097_B1003 Web e-loan國內企金授信新增兆元振興融資方案
	 */
	@Override
	public List<Map<String, Object>> listLMS180R54_01(String effectDate,
			String bgnDate, String endDate) {

		return this.getJdbc().queryForListWithMax("rpt.LMS180R54_01",
				new Object[] { effectDate, bgnDate, endDate });
	}

	/**
	 * 兆元振興融資方案辦理情形統計表
	 * 
	 * J-109-0235_05097_B1003 Web e-loan國內企金授信新增兆元振興融資方案
	 */
	@Override
	public List<Map<String, Object>> listLMS180R54_02(String effectDate,
			String bgnDate, String endDate) {

		return this.getJdbc().queryForListWithMax("rpt.LMS180R54_02",
				new Object[] { effectDate, bgnDate, endDate });
	}

	/**
	 * 兆元振興融資方案辦理情形統計表
	 * 
	 * J-109-0235_05097_B1003 Web e-loan國內企金授信新增兆元振興融資方案
	 */
	@Override
	public List<Map<String, Object>> listLMS180R54_03_1(String effectDate,
			String bgnDate, String endDate) {

		return this.getJdbc().queryForListWithMax("rpt.LMS180R54_03_01",
				new Object[] { effectDate, bgnDate, endDate });
	}

	/**
	 * 兆元振興融資方案辦理情形統計表
	 * 
	 * J-109-0235_05097_B1003 Web e-loan國內企金授信新增兆元振興融資方案 J-109-0419_05097_B1002
	 * 兆元振興方案相關報表格式調整
	 */
	@Override
	public List<Map<String, Object>> listLMS180R54_03_2(String effectDate,
			String bgnDate, String endDate) {

		return this.getJdbc().queryForListWithMax(
				"rpt.LMS180R54_03_02",
				new Object[] { effectDate, bgnDate, endDate, effectDate,
						bgnDate, endDate });
	}

	/**
	 * J-109-0247 最新簽報書之擔保品
	 */
	@Override
	public List<Map<String, Object>> getLatestCol(String custId,
			String bgnDate, String endDate) {
		SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
		SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd 23:59:59");

		return this.getJdbc().queryForListWithMax(
				"getLatestCol",
				new Object[] { custId,
						sdf1.format(CapDate.getDate(bgnDate, "yyyy-MM-dd")),
						sdf2.format(CapDate.getDate(endDate, "yyyy-MM-dd")) });
	}

	/**
	 * 兆元振興融資方案辦理情形預估報表(編製中)
	 * 
	 * J-109-0235_05097_B1003 Web e-loan國內企金授信新增兆元振興融資方案
	 */
	@Override
	public List<Map<String, Object>> listLMS180R55_01() {

		return this.getJdbc().queryForListWithMax("rpt.LMS180R55_01",
				new Object[] {});
	}

	/**
	 * 兆元振興融資方案辦理情形預估報表(已核准)
	 * 
	 * J-109-0235_05097_B1003 Web e-loan國內企金授信新增兆元振興融資方案
	 */
	@Override
	public List<Map<String, Object>> listLMS180R55_02(String effectDate) {

		return this.getJdbc().queryForListWithMax("rpt.LMS180R55_02",
				new Object[] { effectDate });
	}

	/**
	 * 兆元振興融資方案分行核准情形總表
	 * 
	 * J-109-0235_05097_B1003 Web e-loan國內企金授信新增兆元振興融資方案
	 */
	@Override
	public List<Map<String, Object>> listLMS180R56_01(String effectDate,
			String bgnDate, String endDate) {

		return this.getJdbc().queryForListWithMax("rpt.LMS180R56_01",
				new Object[] { effectDate, bgnDate, endDate });
	}

	/**
	 * J-108-0345 貸後管理 最新一筆維護資料之附件檔案
	 */
	@Override
	public List<Map<String, Object>> findL260M01DLatest() {
		return this.getJdbc().queryForListWithMax("findL260M01DLatest",
				new Object[] {});
	}

	/**
	 * 定期檢視 BDOCFILE By L260M01D
	 */
	@Override
	public List<Map<String, Object>> findBDocFileByL260M01D() {
		return this.getJdbc().queryForListWithMax("findBDocFileByL260M01D",
				new Object[] {});
	}

	/**
	 * 小規模營業人授信異常通報表
	 * 
	 * J-109-0315_05097_B1001 Web e-loan企金授信新增小規模營業人央行C方案授信案件之異常通報上一個月獲核定之異動名單
	 */
	@Override
	public List<Map<String, Object>> listLMS180R58_01(String bgnDate,
			String endDate) {

		return this.getJdbc().queryForListWithMax("rpt.LMS180R58_01",
				new Object[] { bgnDate, endDate });
	}

	/**
	 * 小規模營業人額度明細表，有關總處單位引介資訊，預設引進徵信資信簡表介接官網進件之資料。
	 * 
	 * @param cesMainId
	 * @return
	 */
	@Override
	public Map<String, Object> findC240m01aIntrByMainId(String cesMainId) {
		return this.getJdbc().queryForMap("C240M01A.findIntrDataByMainId",
				new Object[] { cesMainId });
	}

	/**
	 * J-109-0280 敘做條件
	 */
	@Override
	public List<Map<String, Object>> queryBizCatList(String mainId) {
		return this.getJdbc().queryForListWithMax("queryBizCatList",
				new Object[] { mainId });
	}

	@Override
	public List<Map<String, Object>> findL140S09AGroupByBizCat(String mainId) {
		return this.getJdbc().queryForListWithMax("findL140S09AGroupByBizCat",
				new Object[] { mainId });
	}

	@Override
	public List<Map<String, Object>> getApprovedGroupLoanBuildCaseMasterData(
			String endOfDate) {
		return this.getJdbc().queryForList(
				"CLS180R51.getApprovedGroupLoanBuildCaseMasterData",
				new Object[] { endOfDate, endOfDate });
	}

	@Override
	public List<Map<String, Object>> getGroupLoanBuildCaseMasterDataOfUnderReview(
			String endOfMonth) {
		return this.getJdbc().queryForList(
				"CLS180R51.getGroupLoanBuildCaseMasterDataOfUnderReview",
				new Object[] { endOfMonth, endOfMonth });
	}

	/**
	 * J-109-0304_10702_B1001 上傳elf457進件來源參數
	 **/
	@Override
	public List<Map<String, Object>> findLaaData2(String startDate,
			String endDate) {
		return this.getJdbc()
				.queryForListWithMax(
						"J_109_0304_sql_1",
						new String[] { startDate + " 00:00:00",
								endDate + " 00:00:00" });
	}

	/**
	 * J-109-0362 青年創業及啟動金貸款辦理情形總表 LMS180R59
	 */
	@Override
	public List<Map<String, Object>> listLMS180R59(String brNos,
			String condition) {
		List<Object> params = new ArrayList<Object>();
		if (Util.isNotEmpty(condition)) {
			params.add(brNos);
		}
		return this.getJdbc().queryForAllListByCustParam("rpt.LMS180R59",
				new Object[] { condition }, params.toArray(new Object[0]));
	}

	@Override
	public List<Map<String, Object>> findListbyL120S01Aseq(String tableName,
			String mainId) {
		if (Util.equals(tableName, "L120S01Q")) {
			return this.getJdbc().queryForListWithMax(
					"L120S01Q.findListbyL120S01Aseq", new Object[] { mainId });
		} else if (Util.equals(tableName, "L120S04D")) {
			return this.getJdbc().queryForListWithMax(
					"L120S04D.findListbyL120S01Aseq",
					new Object[] { mainId, mainId });
		} else if (Util.equals(tableName, "L120S16C")) {
			return this.getJdbc().queryForListWithMax(
					"L120S16C.findListbyL120S01Aseq", new Object[] { mainId });
		} else {
			return null;
		}
	}

	/**
	 * J-109-0371_05097_B1002 簡化青年創業及啟動金貸款簽報書簽案流程
	 */
	@Override
	public Map<String, Object> getLnType61Info(String mainId, String bgnDate) {
		return this.getJdbc().queryForMap("getLnType61Info",
				new Object[] { bgnDate, mainId });
	}

	@Override
	public Map<String, Object> getLnType61InfoByL140m01aOid(String mainId,
			String bgnDate, String l140m01aOid) {
		return this.getJdbc().queryForMap("getLnType61InfoByL140m01aOid",
				new Object[] { bgnDate, mainId, l140m01aOid });
	}

	/**
	 * J-109-0351_10702_B1002 e-Loan青創判斷消金借款人id相同且與企金申貸之青創事業體不同
	 */
	@Override
	public List<Map<String, Object>> getLnType61DocType1ByCustId(String custId,
			String bgnDate) {
		return this.getJdbc()
				.queryForListWithMax("getLnType61DocType1ByCustId",
						new Object[] { bgnDate, custId });
	}

	/**
	 * 以OBU MEGA ID 查詢股票代號(F股，資料由徵信處維護)
	 * 
	 * @param custid
	 * @param dupno
	 * @return
	 */
	@Override
	public Map<String, Object> findCesF106m01aByCustIdInMega(String custid) {
		return this.getJdbc().queryForMap("F106M01A.selByCustIdInMega",
				new Object[] { custid });
	}

	/**
	 * J-109-0351_05097_B1002 e-Loan企金「青年創業及啟動金貸款」簽報書修改
	 */
	@Override
	public List<Map<String, Object>> findClsLnType61ByChairmanId(
			String chairmanId, String bgnDate) {
		return this.getJdbc().queryForListWithMax(
				"findClsLnType61ByChairmanId",
				new Object[] { chairmanId, bgnDate });
	}

	/**
	 * 不符合中小企業定義(A01~A03)及貸款期間(A02:3年、A03:5年)，則系統(eLoan 與
	 * aLoan)不能讓該授信案成為經濟部紓困案件(A01~A03)
	 */
	@Override
	public Map<String, Object> getC140M01A_TotEmp(String cesMainId) {
		return this.getJdbc().queryForMap("getC140M01ATotEmp",
				new Object[] { cesMainId });
	}

	@Override
	public List<Map<String, Object>> getContractNoBySameWithGuarantorIdsInOtherCase(
			String l140m01a_mainId, List<String> idList) {

		if (idList.isEmpty()) {
			return new ArrayList<Map<String, Object>>();
		}

		String questionMark = "";
		for (int i = 0; i < idList.size(); i++) {
			questionMark += "?, ";
		}
		questionMark = questionMark.substring(0, questionMark.length() - 2);

		int paramSize = idList.size() + 1;

		Object[] params = new Object[paramSize];
		params[0] = l140m01a_mainId;
		for (int i = 1; i < paramSize; i++) {
			params[i] = idList.get(i - 1);
		}

		return getJdbc().queryForListByCustParam(
				"getContractNoBySameWithOtherCaseIds",
				new Object[] { questionMark }, params);
	}

	@Override
	public List<Map<String, Object>> getC120s01aDataSameWithLenderOrGuarantorInfo(
			String l120m01a_mainId, String custId, String coTel, String fTel,
			String mTel, String fTarGet, String CoTarGet) {
		return this.getJdbc().queryForListWithMax(
				"getC120s01aDataSameWithLenderOrGuarantorInfo",
				new Object[] { l120m01a_mainId, custId, coTel, fTel, mTel,
						fTarGet, CoTarGet });
	}

	@Override
	public List<Map<String, Object>> getC101s01aDataSameWithLenderOrGuarantorInfo(
			String c101s01a_mainId, String custId, String coTel, String fTel,
			String mTel, String fTarGet, String CoTarGet) {
		return this.getJdbc().queryForListWithMax(
				"getC101s01aDataSameWithLenderOrGuarantorInfo",
				new Object[] { c101s01a_mainId, custId, coTel, fTel, mTel,
						fTarGet, CoTarGet });
	}

	@Override
	public List<Map<String, Object>> getCheckResultForIsSuspectedHeadAccount(
			String l140m01a_mainId) {
		return this.getJdbc().queryForListWithMax(
				"L140MC1A.BCODETYPE.getCheckResultForIsSuspectedHeadAccount",
				new Object[] { l140m01a_mainId });
	}

	@Override
	public List<Map<String, Object>> getCesName(String mainId, String custId,
			String dupNo) {
		return this.getJdbc().queryForList("getCesName",
				new Object[] { mainId, custId, dupNo });
	}

	/**
	 * 小規模營業人兌付振興三倍券達888張名單--明細
	 * 
	 * J-109-0519_05097_B1001 Web e-Loan產生央行C方案借款人，且兌付振興三倍券達888張之名單
	 */
	@Override
	public List<Map<String, Object>> listLMS180R60_01(String brNos,
			String condition) {
		List<Object> params = new ArrayList<Object>();
		if (Util.isNotEmpty(condition)) {
			params.add(brNos);
		}
		return this.getJdbc().queryForAllListByCustParam("rpt.LMS180R60_01",
				new Object[] { condition }, params.toArray(new Object[0]));

	}

	@Override
	public List<Map<String, Object>> findC126m01aByAgntNo(String ownBrId,
			String agntNo, String applyTS_beg, String applyTS_end,
			String docStatus) {

		StringBuffer sql = new StringBuffer();
		List<Object> params = new ArrayList<Object>();
		if (Util.isNotEmpty(ownBrId)) {
			sql.append(" and ownBrId = ? ");
			params.add(ownBrId);
		}
		if (Util.isNotEmpty(agntNo)) {
			sql.append(" and agntNo = ? ");
			params.add(agntNo);
		}

		if (Util.isNotEmpty(applyTS_beg) && Util.isNotEmpty(applyTS_end)) {
			sql.append(" and approveTime between ? and ? ");
			params.add(applyTS_beg);
			params.add(applyTS_end);
		}

		if (Util.isNotEmpty(docStatus)) {
			sql.append(" and docStatus = ? ");
			params.add(docStatus);
		}

		return this.getJdbc().queryForListByCustParam(
				"C126M01A.selectByAgntNo", new Object[] { sql.toString() },
				params.toArray(new Object[0]), 0, Integer.MAX_VALUE,
				new EloanColumnMapRowMapper());

	}

	/**
	 * 額度專案種類明細表--明細
	 * 
	 * J-110-0018_05097_B1001 Web
	 * e-Loan簽報書額度明細表中增列「兆豐百億挺你專案」及「協助農地工廠合法化融資貸款」兩項專案，並產生統計報表
	 */
	@Override
	public List<Map<String, Object>> listLMS180R61_01(String brNos,
			String projClass, String condition) {
		List<Object> params = new ArrayList<Object>();
		params.add(projClass);
		if (Util.isNotEmpty(condition)) {
			params.add(brNos);
		}
		return this.getJdbc().queryForAllListByCustParam("rpt.LMS180R61_01",
				new Object[] { condition }, params.toArray(new Object[0]));

	}

	@Override
	public List<Map<String, Object>> findL140m01aAdcCaseNo(String type,
			String custId, String dupNo, String cntrNo, String adcCaseNo) {
		StringBuffer sb = new StringBuffer();
		List<Object> params = new ArrayList<Object>();
		if (Util.equals(type, "1")) {
			sb.append(" AND CUSTID = ? AND DUPNO = ? ");
			params.add(custId);
			params.add(dupNo);
			params.add(custId);
			params.add(dupNo);
		} else if (Util.equals(type, "2")) {
			sb.append(" AND CNTRNO = ? ");
			params.add(cntrNo);
			params.add(cntrNo);
		} else if (Util.equals(type, "C")) { // check adcCaseNo 是否存在
			sb.append(" AND ADCCASENO = ?");
			params.add(adcCaseNo);
			params.add(adcCaseNo);
		}

		return this.getJdbc().queryForListByCustParam(
				"L140M01A.selectAdcCaseNo",
				new Object[] { sb.toString(), sb.toString() },
				params.toArray(new Object[0]), 0, Integer.MAX_VALUE,
				new EloanColumnMapRowMapper());
	}

	// J-109-0304_10702_B1003 Web e-Loan消金檢核地號是否符合整批貸款基地地號
	@Override
	public List<Map<String, Object>> checkMatchL140S02M_LandNo(String landNo,
			String address) {
		return this.getJdbc().queryForListWithMax("checkMatchL140S02M_LandNo",
				new Object[] { landNo, address });
	}

	@Override
	public List<Map<String, Object>> getC126M01AByBranchNoAndCustId(
			String custId, String dupNo, String branchNo) {
		return getJdbc().queryForListWithMax(
				"C126M01A.getC126M01AByBranchNoAndCustId",
				new Object[] { custId, dupNo, branchNo });
	}

	@Override
	public List<Map<String, Object>> getC126M01AByBranchNoAndCustIdOnly(
			String custId, String branchNo) {
		return getJdbc().queryForListWithMax(
				"C126M01A.getC126M01AByBranchNoAndCustIdOnly",
				new Object[] { custId, branchNo });
	}

	/**
	 * 案件屬110年行銷名單來源客戶簽案資料--明細
	 * 
	 * J-110-0038_05097_B1001 Web e-Loan企金額度明細表新增「本案是否屬110年行銷名單來源客戶」並產生統計報表
	 */
	@Override
	public List<Map<String, Object>> listLMS180R62_01(String brNos,
			String condition) {

		List<Object> params = new ArrayList<Object>();
		if (Util.isNotEmpty(condition)) {
			params.add(brNos);
		}

		return this.getJdbc().queryForAllListByCustParam("rpt.LMS180R62_01",
				new Object[] { condition }, params.toArray(new Object[0]));

	}

	@Override
	public List<Map<String, Object>> find_ploan_no_ptaData(int shiftdays) {
		String begApllyTs = TWNDate.toAD(CapDate.shiftDays(
				CapDate.getCurrentTimestamp(), shiftdays))
				+ " 00:00:00";
		return getJdbc().queryForListWithMax("find_ploan_no_ptaData",
				new String[] { begApllyTs });
	}

	@Override
	public List<Map<String, Object>> findPloanCSCNoStkhData(String... ploanPlan) {
		String ploanPlanParams = Util.genSqlParam(ploanPlan);

		return this.getJdbc().queryForListByCustParam("findPloanCSCNoStkhData",
				new Object[] { ploanPlanParams }, ploanPlan);
	}

	/**
	 * J-112-0390 中鋼集團消貸程式優化(e-Loan) 取得中鋼徵信整批匯入名單需要發查外部系統的清單
	 * 
	 * @param startDate
	 * @param dataCount
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findCSGNeedSendOuterSysData(
			String startDate, int dataCount) {
		return this.getJdbc().queryForListWithMax(
				"findCSGNeedSendOuterSysData",
				new Object[] { startDate, dataCount });
	}

	/**
	 * I-110-0028_05097_B1002 Web e-Loan企金授信額度明細表配合進出口業務集中化修改小行可以敘作大行動審表
	 * 
	 * @param caseMainId
	 * @param useBrId
	 * @param itemType
	 * @return
	 */
	@Override
	public List<Map<String, Object>> get_cntrDoc_from_VLUSEDOC01_multiBr(
			String caseMainId, String[] useBrId, String itemType) {
		List<Object> params = new ArrayList<Object>();
		String useBridParam = Util.genSqlParam(useBrId);

		params.add(caseMainId);
		params.addAll(Arrays.asList(useBrId));
		params.add(itemType);
		// List<String> r = new ArrayList<String>();
		// for (String tb : useBrId) {
		// r.add("'" + Util.trim(tb) + "'");
		// }
		// if (r.size() == 0) {
		// r.add("''");
		// }

		// String brNos = StringUtils.join(r, ",");
		return this.getJdbc().queryForAllListByCustParam(
				"find_cntrDoc_from_VLUSEDOC01_MULTIBR",
				new Object[] { useBridParam }, params.toArray(new Object[0]));

	}

	@Override
	public List<Map<String, Object>> getLatestGroupLoanBuildCaseMasterDataWithin2Years(
			String startDate, String endDate) {
		return this.getJdbc().queryForListWithMax(
				"CLS180R51.getLatestGroupLoanBuildCaseMasterDataWithin2Years",
				new Object[] { startDate, endDate });
	}

	@Override
	public List<Map<String, Object>> getLatestGroupLoanBuildCaseMasterDataWithin2YearsByBranch(
			String startDate, String endDate, String branchNo) {
		return this
				.getJdbc()
				.queryForListWithMax(
						"CLS180R51.getLatestGroupLoanBuildCaseMasterDataWithin2YearsByBranch",
						new Object[] { startDate, endDate, branchNo });
	}

	@Override
	public List<Map<String, Object>> getSubAppropriationDataOfGroupLoanBuildCase(
			Set<String> grpCntrnoSet, String m_startDate, String m_endDate,
			String y_startDate, String y_endDate) {

		int size = grpCntrnoSet.size();
		Object[] params = new Object[size + 4];
		String questionMark = StringUtils.repeat("?,", size);

		int i = 0;
		for (String grpCntrNo : grpCntrnoSet) {
			params[i++] = grpCntrNo;
		}

		params[i++] = m_startDate;
		params[i++] = m_endDate;
		params[i++] = y_startDate;
		params[i++] = y_endDate;

		return this.getJdbc().queryForListByCustParam(
				"CLS180R51.getSubAppropriationDataOfGroupLoanBuildCase",
				new Object[] { questionMark.substring(0,
						questionMark.length() - 1) }, params);
	}

	/**
	 * J-109-0513_10702_B1001 匯出全行地政士黑名單
	 */
	@Override
	public List<Map<String, Object>> listCLS180R52() {

		return this.getJdbc().queryForListWithMax("rpt.CLS180R52",
				new Object[] {});

	}

	@Override
	public List<Map<String, Object>> listLMS180R64(List<String> custQuery,
			List<String> custParam) {
		// J-111-0583_05097_B1001 Web
		// e-Loan企金授信提供各營運中心可自行列印轄下分行於指定期間內所簽報異常通報之「授信異常通報案件報送統計表」
		// return this.getJdbc().queryForAllListByCustParam("rpt.LMS180R64",
		// new Object[] { otherCondition },
		// new Object[] { bgnDate, endDate });

		return this.getJdbc().queryForAllListByCustParam("rpt.LMS180R64",
				custQuery.toArray(), custParam.toArray());

	}

	/**
	 * 南京東路客戶移轉國外部
	 * 
	 * @param mainId
	 */
	@Override
	public void LNF078T_deleteByExDate(String exDate) {
		this.getJdbc()
				.update("LNF078T.deleteByExDate", new Object[] { exDate });
	}

	/**
	 * 南京東路客戶移轉國外部
	 * 
	 * @param mainId
	 */
	@Override
	public void LNF078T_insert(String CONTRACT_O, String LOAN_NO_O,
			String CUST_ID, String CONTRACT, String LOAN_NO, String CUSTID,
			String DUPNO, String BRANCH_O, String BRANCH, String EXDATE) {
		this.getJdbc().update(
				"LNF078T.insert",
				new Object[] { CONTRACT_O, LOAN_NO_O, CUST_ID, CONTRACT,
						LOAN_NO, CUSTID, DUPNO, BRANCH_O, BRANCH, EXDATE });
	}

	/**
	 * 南京東路客戶移轉國外部
	 * 
	 * 修改額度序號
	 * 
	 * @param table
	 * @param column
	 * @param exDate
	 * @return
	 */
	@Override
	public int updateCntrNoByLnf078t(String table, String column, String exDate) {
		return this.getJdbc().updateByCustParam("updateCntrNoByLnf078t",
				new Object[] { table, column, column, column },
				new Object[] { exDate, exDate });
	}

	/**
	 * 南京東路客戶移轉國外部
	 * 
	 * 修改帳號
	 * 
	 * @param table
	 * @param column
	 * @param exDate
	 * @return
	 */
	@Override
	public int updateLoanNoByLnf078t(String table, String column, String exDate) {
		return this.getJdbc().updateByCustParam("updateLoanNoByLnf078t",
				new Object[] { table, column, column, column },
				new Object[] { exDate, exDate });
	}

	/**
	 * 南京東路客戶移轉國外部
	 * 
	 * 增加授權
	 * 
	 * @param authTable
	 * @param mainTable
	 * @param docStatusArr
	 * @param fromBranch
	 * @param toBranch
	 * @param exDate
	 * @return
	 */
	@Override
	public int addAuthUnitByLnf078t(String authTable, String mainTable,
			String[] docStatusArr, String fromBranch, String toBranch,
			String exDate) {
		authTable = StringEscapeUtils.escapeSql(authTable);
		mainTable = StringEscapeUtils.escapeSql(mainTable);

		String docSqlParams = Util.genSqlParam(docStatusArr);

		List<Object> params = new ArrayList<Object>();
		params.add(toBranch);
		params.add(fromBranch);
		params.add(toBranch);
		params.add(exDate);
		params.add(fromBranch);
		params.add(toBranch);
		params.addAll(Arrays.asList(docStatusArr));
		return this.getJdbc().updateByCustParam(
				"addAuthUnitByLnf078t",
				new Object[] { authTable, authTable, authTable, mainTable,
						docSqlParams }, params.toArray(new Object[0]));
	}

	@Override
	public int addRetrialAuthUnitByLnf078T(String authTable, String mainTable,
			String[] docStatusArr, String fromBranch, String toBranch,
			String toBranchRetrialUnit, String exDate) {

		authTable = StringEscapeUtils.escapeSql(authTable);
		mainTable = StringEscapeUtils.escapeSql(mainTable);

		String docSqlParams = Util.genSqlParam(docStatusArr);

		List<Object> params = new ArrayList<Object>();
		params.add(toBranchRetrialUnit);
		params.add(fromBranch);
		params.add(toBranchRetrialUnit);
		params.add(exDate);
		params.add(fromBranch);
		params.add(toBranch);
		params.addAll(Arrays.asList(docStatusArr));
		return this.getJdbc().updateByCustParam(
				"addAuthUnitByLnf078t",
				new Object[] { authTable, authTable, authTable, mainTable,
						docSqlParams }, params.toArray(new Object[0]));
	}

	/**
	 * 南京東路客戶移轉國外部
	 * 
	 * 修改CASEBRID
	 * 
	 * @param docStatusArr
	 * @param fromBranch
	 * @param toBranch
	 * @param exDate
	 * @return
	 */
	@Override
	public int chgCaseBrIdByLnf078t(String[] docStatusArr, String fromBranch,
			String toBranch, String exDate) {

		String docSqlParams = Util.genSqlParam(docStatusArr);

		List<Object> params = new ArrayList<Object>();
		params.add(toBranch);
		params.add(exDate);
		params.add(fromBranch);
		params.add(toBranch);
		params.addAll(Arrays.asList(docStatusArr));
		params.add(fromBranch);
		return this.getJdbc().updateByCustParam("chgCaseBrIdByLnf078t",
				new Object[] { docSqlParams }, params.toArray(new Object[0]));
	}

	/**
	 * 南京東路客戶移轉國外部
	 * 
	 * @param exDate
	 * @return
	 */
	@Override
	public List<Map<String, Object>> LNF078T_selAllByExDate(String exDate) {
		return this.getJdbc().queryForListWithMax("LNF078T.selAllByExDate",
				new Object[] { exDate });
	}

	/**
	 * 
	 * 南京東路客戶移轉國外部
	 * 
	 * 修改C801M01A(財產清冊) LOANNO
	 * 
	 * @param newLoanNo
	 * @param oldLoanNo
	 * @return
	 */
	@Override
	public int updateC801m01aLoanNoByLnf078t(String newLoanNo, String oldLoanNo) {
		return this.getJdbc().update("C801M01A.updateLoanNoByLnf078t",
				new Object[] { oldLoanNo, newLoanNo, "%" + oldLoanNo + "%" });
	}

	/**
	 * 南京東路客戶移轉國外部
	 * 
	 * 修改OWNBRID
	 * 
	 * @param table
	 * @param column
	 * @param docStatusArr
	 * @param exDate
	 * @return
	 */
	@Override
	public int chgOwnBrIdByLnf078t(String table, String column,
			String[] docStatusArr, String exDate) {
		table = StringEscapeUtils.escapeSql(table);
		column = StringEscapeUtils.escapeSql(column);

		String docSqlParams = Util.genSqlParam(docStatusArr);

		List<Object> params = new ArrayList<Object>();
		params.add(exDate);
		params.addAll(Arrays.asList(docStatusArr));
		params.add(exDate);
		params.addAll(Arrays.asList(docStatusArr));
		// 組合成 ?,?,?
		return this.getJdbc().updateByCustParam(
				"updateOwnBrIdByLnf078t",
				new Object[] { table, column, docSqlParams, column,
						docSqlParams, column }, params.toArray(new Object[0]));
	}

	/**
	 * J-109-0479_05097_B1004 Web e-Loan簽報書增加各別流程控管階段的時間點並提供列印案件階段進度及統計excel下載
	 * J-111-0600_05097_B1002 Web e-Loan授信系統管理報表新增「授信簽報案件經區域營運中心接案進度控管表」
	 */
	@Override
	public List<Map<String, Object>> listLMS180R65(List<String> custQuery,
			List<String> custParam) {

		// String bgnDate,
		// String endDate) {

		// return this.getJdbc().queryForList("rpt.LMS180R65",
		// new String[] { bgnDate, endDate }, 0, Integer.MAX_VALUE);

		return this.getJdbc().queryForAllListByCustParam("rpt.LMS180R65",
				custQuery.toArray(), custParam.toArray());
	}

	/**
	 * J-110-0374 Web e-Loan 為加強區域營運中心授信案件之審查效率, 增加企/消金授信簽報案件經區域營運中心流程進度控管表
	 * J-111-0600_05097_B1001 Web e-Loan授信系統管理報表新增「授信簽報案件經區域營運中心接案進度控管表」
	 */
	@Override
	public List<Map<String, Object>> listLMS180R68(String docType,
			List<String> custQuery, List<String> custParam) {

		if (docType.equals("1")) {
			// 企金
			// return this.getJdbc().queryForList("rpt.LMS180R68",
			// new String[] { bgnDate, endDate }, 0, Integer.MAX_VALUE);

			return this.getJdbc().queryForAllListByCustParam("rpt.LMS180R68",
					custQuery.toArray(), custParam.toArray());

		} else {
			// 個金
			// return this.getJdbc().queryForList("rpt.CLS180R54",
			// new String[] { bgnDate, endDate }, 0, Integer.MAX_VALUE);

			return this.getJdbc().queryForAllListByCustParam("rpt.CLS180R54",
					custQuery.toArray(), custParam.toArray());
		}
	}

	@Override
	public Map<String, Object> findDocFileByMainIdAndBranchId(String mainId,
			String branchId) {
		return this.getJdbc().queryForMap("DocFile.findByMainIdAndBranchId",
				new Object[] { mainId, branchId });
	}

	@Override
	public int deleteC120S04WByMainIdAndDataCustomerNo(String mainId,
			String dataCustomerNo) {
		return this.getJdbc().update(
				"C120S04W.deleteByMainIdAndDataCustomerNo",
				new Object[] { mainId, dataCustomerNo });
	}

	/**
	 * 取得最新一筆借款人小規模額度明細表之簽報書
	 * 
	 * @param custId
	 * @param caseBrId
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findL120m01aHaveSmallBussC(String custId,
			String caseBrId) {
		return this.getJdbc().queryForListWithMax("L120M01A.selSmallBussC",
				new Object[] { custId, caseBrId });
	}

	/**
	 * 資信簡表央行轉融通小額申請書介接資料檔
	 */
	@Override
	public Map<String, Object> findCesC120s01kByMainId(String mainId) {
		return this.getJdbc().queryForMap("CES.C120S01K.selbyMainId",
				new Object[] { mainId });
	}

	/**
	 * 資信簡表BY MAINID
	 */
	@Override
	public Map<String, Object> findCesC120m01aByMainId(String mainId) {
		return this.getJdbc().queryForMap("CES.C120M01A.selbyMainId",
				new Object[] { mainId });
	}

	/**
	 * 取得資信簡表對應之線上進件
	 */
	@Override
	public Map<String, Object> findCesC240m01aByOid(String oid) {
		return this.getJdbc().queryForMap("CES.C240M01A.selbyOid",
				new Object[] { oid });
	}

	/**
	 * 更新徵信線上進件文件狀態(簽報書核准時)
	 * 
	 * Z01:不承做 03:已核貸
	 * 
	 * @param docStatus
	 * @param oid
	 */
	@Override
	public void updateC240m01aStatusByOid(String docStatus, String oid) {
		this.getJdbc().update("CES.C240M01A.updateStatusByOid",
				new Object[] { docStatus, oid });
	}

	/**
	 * 更新徵信線上進件分行顯示狀態 RPA簽報書處理中:B01 RPA簽報書完成:B02 RPA簽報書執行異常: B03 RPA動審表處理中:B04
	 * RPA動審表完成:B05 RPA動審表執行異常:B06 RPA信保查詢失敗:B07 RPA信保查詢完成:B08 簽報書已核准:B09
	 * 簽報書已婉卻:B10 動審表已核准:B11
	 * 
	 * @param status4Br
	 * @param oid
	 */
	@Override
	public void updateC240m01aStatus4BrByOid(String status4Br, String oid) {
		this.getJdbc().update("CES.C240M01A.updateStatus4BrByOid",
				new Object[] { status4Br, oid });
	}

	/**
	 * 取得小規模線上進件最新一筆
	 */
	@Override
	public Map<String, Object> findCesC240m01aLastByOwnBrIdAndCustId(
			String brNo, String custId) {
		return this.getJdbc().queryForMap(
				"CES.C240M01A.selLastbyOwnBrIdAndCustId",
				new Object[] { brNo, custId });
	}

	@Override
	public List<Map<String, Object>> findC120M01A_selMainIdaByCustId(
			String caseBrId, String custId, String dupNo) {
		return this.getJdbc().queryForList("C120M01A.selMainIdaByCustId",
				new Object[] { caseBrId, custId, dupNo });
	}

	@Override
	public Map<String, Object> findC122m01aAgreeQueryEJIpCount(
			String agreeQueryEJIp, String applyTs) {
		return this.getJdbc().queryForMap("C122M01A.getAgreeQueryEJIpCount",
				new Object[] { agreeQueryEJIp, applyTs });
	}

	@Override
	public Map<String, Object> findC122m01aAgreeQueryEJMtelCount(
			String agreeQueryEJMtel, String applyTs) {
		return this.getJdbc().queryForMap("C122M01A.getAgreeQueryEJMtelCount",
				new Object[] { agreeQueryEJMtel, applyTs });
	}

	/**
	 * 取得最新一筆借款人簽報書
	 * 
	 * @param custId
	 * @param caseBrId
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findL120m01aLastByCustId(String custId,
			String caseBrId) {
		return this.getJdbc().queryForListWithMax("L120M01A.selLastByCustId",
				new Object[] { custId, caseBrId });
	}

	/**
	 * 取得最新一筆借款人紓困動審表明細(篩選紓困類別)
	 * 
	 * @param cntrNo
	 * @param custId
	 * @param dupNo
	 * @param rescueItem
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findL161s01aLastRescueDataWithRescueItem(
			String cntrNo, String custId, String dupNo, String rescueItem) {
		return this.getJdbc().queryForListWithMax(
				"getL161s01a_LastRescueDataWithRescueItem",
				new Object[] { cntrNo, custId, dupNo, rescueItem });
	}

	/**
	 * 取得最新一筆借款人紓困動審表明細(不篩選紓困類別)
	 * 
	 * @param cntrNo
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findL161s01aLastRescueDataWithoutRescueItem(
			String cntrNo, String custId, String dupNo) {
		return this.getJdbc().queryForListWithMax(
				"getL161s01a_LastRescueDataWithoutRescueItem",
				new Object[] { cntrNo, custId, dupNo });
	}

	@Override
	public List<Map<String, Object>> getCntrNoByTable(String tableName) {
		StringBuffer sql = new StringBuffer();

		if (Util.equals(tableName, "L120S14A")) { // 小規合約書
			sql.append(" WHERE T1.DELETEDTIME IS NULL AND L140A.ISRESCUE = 'Y' AND L140A.ISCBREFIN = 'Y' AND L140A.ISSMALLBUSS = 'C'");
		} else if (Util.equals(tableName, "L120S14E")) { // 青創合約書
			sql.append(" WHERE T1.DELETEDTIME IS NULL AND L140A.LNTYPE = '61'");
		}

		return this.getJdbc().queryForListByCustParam("getCntrNoByTable",
				new Object[] { tableName, sql.toString() }, new Object[] {}, 0,
				Integer.MAX_VALUE, new EloanColumnMapRowMapper());
	}

	/**
	 * 取得最新一筆借款人紓困動審表明細(篩選掛件文號)
	 * 
	 * @param cntrNo
	 * @param custId
	 * @param dupNo
	 * @param rescueItem
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findL161s01aLastRescueDataWithRescueNo(
			String custId, String dupNo, String rescueNo) {
		return this.getJdbc().queryForListWithMax(
				"getL161s01a_LastRescueDataWithRescueNo",
				new Object[] { custId, dupNo, rescueNo });
	}

	public List<Map<String, Object>> findL140M01_byLastSmallBussCWithCustid(
			String custId, boolean onlyApprove) {

		String conditionStr = "";

		if (onlyApprove) {
			// 只抓已核准
			conditionStr = " AND T2.DOCSTATUS IN ('05O') ";
		} else {
			// 只排除複製的備份
			conditionStr = " AND T2.DOCSTATUS NOT IN ('BKL') ";
		}

		return this.getJdbc().queryForListByCustParam(
				"L140M01A.selLastSmallBussCByCustId",
				new Object[] { conditionStr }, new Object[] { custId });

	}

	@Override
	public List<Map<String, Object>> findL120M01AByL140M01A(String mainId) {
		return this.getJdbc().queryForList("L120M01A.findByL140M01A",
				new Object[] { mainId });
	}

	/**
	 * 檢查該簽報書下有沒有已對保之契約書
	 * 
	 * @param mainId
	 * @return
	 */
	@Override
	public List<Map<String, Object>> L120M01ACanDeleteOrNot(String mainId) {
		return this.getJdbc().queryForListWithMax("C120M01A.CanDeleteOrNot",
				new Object[] { mainId });
	}

	/**
	 * 檢查該簽報書之客戶上有幾筆未註銷之簽報書
	 * 
	 * @param mainId
	 * @return
	 */
	@Override
	public String FindAmountbyMainid(String mainId) {
		String result = "";
		Map<String, Object> data = this.getJdbc().queryForMap(
				"C120M01A.findAmountbyMainid", new Object[] { mainId });
		if (data != null) {
			result = Util.trim(data.get("CUN"));
		}
		return result;
	}

	/**
	 * 動審表-勞工紓困簽報書資料
	 * 
	 * @param custid
	 * @param ownbrid
	 * @return
	 */
	@Override
	public List<Map<String, Object>> getEloanCollateralFrom(String custid,
			String ownbrid) {
		return this.getJdbc().queryForList("getEloanCollateralFrom",
				new Object[] { custid, ownbrid });
	}

	/**
	 * J-110-0142 特定金錢信託案件量統計報表
	 */
	@Override
	public List<Map<String, Object>> listCLS180R42_01(String brNo) {
		return this.getJdbc().queryForListWithMax("rpt.CLS180R42_01",
				new Object[] { brNo + "%" });
	}

	/**
	 * J-110-0211_11557_B1002 配合海外東、阪行信義房屋專案，e-Loan授信管理系統新增控管措施，並開啟海外業務處即時查詢功能
	 * 
	 * @param custId
	 * @param caseBrId
	 * @return
	 */
	@Override
	public List<Map<String, Object>> queryCLS180R53Data(String parentId,
			String brNo, String startDate, String endDate) {
		List<Object> params = new ArrayList<Object>();
		String sql1 = "";
		String sql2 = "";

		// 有選擇總戶序號才需要下條件
		if (Util.isNotEmpty(parentId)) {
			sql1 = " AND G2.parentCntrNo = ? ";
			params.add(parentId);
		}
		params.add(startDate);
		params.add(endDate);

		// 若為0A8只可以看到0A8的資料，0A7為在地總行、942 海業處 可以看到0A7+0A8資料
		if ("0A8".equals(brNo)) {
			sql2 = " AND G4.CASEBRID = ? ";
			params.add(brNo);
		}
		return this.getJdbc().queryForAllListByCustParam("rpt.CLS180R53",
				new Object[] { sql1, sql2 }, params.toArray(new Object[0]));
	}

	@Override
	public int deleteC900m01nByNotInAssignEmpNo(String brNo,
			String[] assignEmpNos) {
		StringBuilder assignEmpNosSB = new StringBuilder();

		if (assignEmpNos.length == 0) {
			assignEmpNosSB.append("''");
		} else {
			for (String key : assignEmpNos) {
				assignEmpNosSB
						.append(assignEmpNosSB.toString().length() > 0 ? ","
								: "");
				assignEmpNosSB.append("'");
				assignEmpNosSB.append(key);
				assignEmpNosSB.append("'");
			}
		}

		return this.getJdbc().updateByCustParam(
				"C900M01N.deleteC900m01nByNotInAssignEmpNo",
				new Object[] { brNo, assignEmpNosSB }, new Object[] {});
	}

	public List<Map<String, Object>> findC900m01nByBrNoAndEmpNoOrderByTimeAndOrder(
			String brNo, String[] assignEmpNos) {
		StringBuilder assignEmpNosSB = new StringBuilder();

		if (assignEmpNos.length == 0) {
			assignEmpNosSB.append("''");
		} else {
			for (String key : assignEmpNos) {
				assignEmpNosSB
						.append(assignEmpNosSB.toString().length() > 0 ? ","
								: "");
				assignEmpNosSB.append("'");
				assignEmpNosSB.append(key);
				assignEmpNosSB.append("'");
			}
		}

		return this.getJdbc().queryForAllListByCustParam(
				"C900M01N.findByBrNoAndEmpNoOrderByTimeAndOrder",
				new Object[] { brNo, assignEmpNosSB }, new Object[] {});
	}

	/**
	 * 依借款人查詢項下已動審之特定紓困代碼類別
	 * 
	 * J-110-0288_05097_B1001 Web e-Loan配合辦理「行政院國家發展基金協助新創事業紓困融資加碼方案」，修改額度明細表欄位
	 * 
	 * @param custId
	 * @param dupNo
	 * @param rescueItem
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findL161s01aAllRescueDataWithRescueItemByCustId(
			String custId, String dupNo, String[] rescueItem) {
		String rescueItemParam = Util.genSqlParam(rescueItem);
		List<Object> params = new ArrayList<Object>();
		params.add(custId);
		params.add(dupNo);
		params.addAll(Arrays.asList(rescueItem));
		// StringBuffer rescueItemBuf = new StringBuffer();
		// for (String key : rescueItem) {
		// rescueItemBuf.append(rescueItemBuf.toString().length() > 0 ? ","
		// : "");
		// rescueItemBuf.append("'");
		// rescueItemBuf.append(key);
		// rescueItemBuf.append("'");
		// }

		return this.getJdbc()
				.queryForAllListByCustParam(
						"getL161s01a_allRescueDataWithRescueItemByCustId",
						new Object[] { rescueItemParam },
						params.toArray(new Object[0]));

	}

	/**
	 * 依借款人查詢同一動審表項下已動審之特定紓困代碼類別
	 * 
	 * J-110-0288_05097_B1001 Web e-Loan配合辦理「行政院國家發展基金協助新創事業紓困融資加碼方案」，修改額度明細表欄位
	 * 
	 * @param custId
	 * @param dupNo
	 * @param rescueItem
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findL161s01aAllRescueDataWithRescueItemByCustIdAndMainId(
			String mainId, String custId, String dupNo, String[] rescueItem) {
		String rescueItemParam = Util.genSqlParam(rescueItem);
		List<Object> params = new ArrayList<Object>();
		params.add(custId);
		params.add(dupNo);
		params.add(mainId);
		params.addAll(Arrays.asList(rescueItem));

		// StringBuffer rescueItemBuf = new StringBuffer();
		// for (String key : rescueItem) {
		// rescueItemBuf.append(rescueItemBuf.toString().length() > 0 ? ","
		// : "");
		// rescueItemBuf.append("'");
		// rescueItemBuf.append(key);
		// rescueItemBuf.append("'");
		// }

		return this
				.getJdbc()
				.queryForAllListByCustParam(
						"getL161s01a_allRescueDataWithRescueItemByCustIdAndMainId",
						new Object[] { rescueItemParam },
						params.toArray(new Object[0]));

	}

	@Override
	public List<Map<String, Object>> findC140M01A_selMainIdaByCustId(
			String caseBrId, String custId, String dupNo) {
		return this.getJdbc().queryForList("C140M01A.selMainIdaByCustId",
				new Object[] { caseBrId, custId, dupNo });
	}

	/**
	 * 徵信報告BY MAINID
	 */
	@Override
	public Map<String, Object> findCesC140m01aByMainId(String mainId) {
		return this.getJdbc().queryForMap("CES.C140M01A.selbyMainId",
				new Object[] { mainId });
	}

	@Override
	public List<Map<String, Object>> findL140M01_byLastStartUpReliefWithCustid(
			String custId, boolean onlyApprove, String[] rescueItem,
			String[] rescueItemSub) {

		String conditionStr = "";

		if (onlyApprove) {
			// 只抓已核准
			conditionStr = " AND T2.DOCSTATUS IN ('05O') ";
		} else {
			// 只排除複製的備份
			conditionStr = " AND T2.DOCSTATUS NOT IN ('BKL') ";
		}

		List<Object> params = new ArrayList<Object>();
		StringBuffer rescueItemBuf = new StringBuffer();
		if (rescueItem != null && rescueItem.length > 0) {

			// for (String key : rescueItem) {
			// rescueItemBuf
			// .append(rescueItemBuf.toString().length() > 0 ? ","
			// : "");
			// rescueItemBuf.append("'");
			// rescueItemBuf.append(key);
			// rescueItemBuf.append("'");
			// }
			// rescueItemBuf.insert(0, " RESCUEITEM IN (");
			// rescueItemBuf.append(")");

			rescueItemBuf.append(" RESCUEITEM IN (")
					.append(Util.genSqlParam(rescueItem)).append(")");
			params.addAll(Arrays.asList(rescueItem));
		}

		StringBuffer rescueItemSubBuf = new StringBuffer();
		if (rescueItemSub != null && rescueItemSub.length > 0) {

			// for (String key : rescueItemSub) {
			// rescueItemSubBuf
			// .append(rescueItemSubBuf.toString().length() > 0 ? ","
			// : "");
			// rescueItemSubBuf.append("'");
			// rescueItemSubBuf.append(key);
			// rescueItemSubBuf.append("'");
			// }
			// rescueItemSubBuf.insert(0, " RESCUEITEMSUB IN (");
			// rescueItemSubBuf.append(")");

			rescueItemSubBuf.append(" RESCUEITEMSUB IN (")
					.append(Util.genSqlParam(rescueItemSub)).append(")");
			params.addAll(Arrays.asList(rescueItemSub));
		}

		StringBuffer whereString = new StringBuffer("");
		if (Util.notEquals(rescueItemBuf.toString(), "")
				|| Util.notEquals(rescueItemSubBuf.toString(), "")) {

			if (Util.notEquals(rescueItemBuf.toString(), "")) {
				whereString.append(rescueItemBuf.toString());
			}

			if (Util.notEquals(rescueItemSubBuf.toString(), "")) {
				if (Util.notEquals(whereString.toString(), "")) {
					whereString.append(" OR ").append(
							rescueItemSubBuf.toString());
				} else {
					whereString.append(rescueItemSubBuf.toString());
				}
			}
		}

		if (Util.notEquals(whereString.toString(), "")) {
			whereString.insert(0, "AND ( ");
			whereString.append(") ");
		}

		params.add(custId);

		return this.getJdbc().queryForListByCustParam(
				"L140M01A.selLastLastStartUpReliefByCustId",
				new Object[] { whereString.toString(), conditionStr },
				params.toArray(new Object[0]));

	}

	/**
	 * Web e-Loan配合紐行Oracle系統建置，修改AML相關功能。 整批重送紐行ORACLE掃描
	 * 
	 * @return
	 */
	@Override
	public List<Map<String, Object>> doLmsBatch0047(String bgnDate,
			String endDate) {
		return this.getJdbc().queryForListWithMax("batch.doLmsBatch0047",
				new Object[] { bgnDate, endDate });
	}

	/**
	 * 保證人資料BY MAINID
	 */
	@Override
	public List<Map<String, Object>> findL140m01iOrderBy(String mainId,
			String rtype) {
		return this.getJdbc().queryForList("L140M01I.getL140m01iOrderBy",
				new Object[] { mainId, rtype });
	}

	/**
	 * 產品資訊資料BY MAINID
	 */
	@Override
	public List<Map<String, Object>> findL140s02aByc340m01b(String mainId) {
		return this.getJdbc().queryForList("L140S02A.findL140s02aByc340m01b",
				new Object[] { mainId });
	}

	/**
	 * J-110-0371 新版簽報書_個人 取得消金徵信相關資料
	 */
	public Map<String, Object> getCLSInfo(String brNo, String custId,
			String dupNo, boolean isOverSea) {
		String sqlId = "LMS.selC101ByMainId";
		if (isOverSea) {
			sqlId = "LMS.selC101ByMainId_Ovs";
		}
		return this.getJdbc().queryForMap(sqlId,
				new Object[] { brNo, custId, dupNo });
	}

	/**
	 * J-110-0363 貸後管理 取得ID階層資料
	 */
	@Override
	public List<Map<String, Object>> findL260MById(String clazz,
			String ownBrId, String custId, String dupNo, String[] docStatusArray) {
		String sql = "findL260M01CById";
		if (Util.equals(clazz, "L260M01C")) {
			sql = "findL260M01CById";
		} else if (Util.equals(clazz, "L260M01D")) {
			sql = "findL260M01DById";
		}
		List<Object> params = new ArrayList<Object>();
		params.addAll(Arrays.asList(new Object[] { ownBrId, custId, dupNo }));

		StringBuffer sb = new StringBuffer();
		if (docStatusArray != null && docStatusArray.length > 0) {
			// for (String docStatus : docStatusArray) {
			// sb.append(sb.toString().length() > 0 ? "," : "");
			// sb.append("'").append(docStatus).append("'");
			// }
			sb.append(Util.genSqlParam(docStatusArray));
			sb.insert(0, " AND A.DOCSTATUS IN (");
			sb.append(")");
			params.addAll(Arrays.asList(docStatusArray));
		}

		return this.getJdbc().queryForListByCustParam(sql,
				new Object[] { sb.toString() }, params.toArray(new Object[0]),
				0, Integer.MAX_VALUE, new EloanColumnMapRowMapper());
	}

	/**
	 * J-110-0326_05097_B1001 Web e-Loan 授信案件之簽報增加企業淨值為負的授權檢核提示訊息
	 */
	@Override
	public List<Map<String, Object>> findCesF101m01aByCustIdForNegativeNetWorth(
			String custId, String dupNo, String brNo) {
		return this.getJdbc().queryForList(
				"CES.F101M01A.getLastByCustIdForNegativeNetWorth",
				new Object[] { custId, dupNo, brNo });
	}

	@Override
	public List<Map<String, Object>> getSameScrivenerIntroductionInfo(
			String fromCaseDate, String toCaseDate, String l140m01a_mainId,
			String laaName, String laaNo) {
		return this.getJdbc().queryForList(
				"getSameAsScrivenerIntroductionInfo",
				new Object[] { fromCaseDate, toCaseDate, l140m01a_mainId,
						laaName, laaNo });
	}

	@Override
	public List<Map<String, Object>> getSameFinancialHoldingSubsidiaryIntroductionInto(
			String fromCaseDate, String toCaseDate, String l140m01a_mainId,
			String megaCode, String subEmpNo) {
		return this.getJdbc().queryForList(
				"getSameFinancialHoldingSubsidiaryIntroductionInto",
				new Object[] { fromCaseDate, toCaseDate, l140m01a_mainId,
						megaCode, subEmpNo });
	}

	@Override
	public List<Map<String, Object>> getSameMegaBankCustomersIntroductionInto(
			String fromCaseDate, String toCaseDate, String l140m01a_mainId,
			String introCustId) {
		return this.getJdbc().queryForList(
				"getSameMegaBankCustomersIntroductionInto",
				new Object[] { fromCaseDate, toCaseDate, l140m01a_mainId,
						introCustId });
	}

	@Override
	public List<Map<String, Object>> findCmsC100m01ByCntrNoForLgd(
			List<String> cntrNos) {

		String cntrNoParam = Util.genSqlParam(cntrNos);

		// StringBuilder sb = new StringBuilder("'");
		// for (String cntrNo : cntrNos) {
		// sb.append(sb.length() > 1 ? "','" : "").append(cntrNo);
		// }
		// sb.append("'");
		// ~~~
		return this.getJdbc().queryForAllListByCustParam(
				"CMS.C101M01.getCmsDataByCntrNo", new String[] { cntrNoParam },
				cntrNos.toArray(new String[0]));

	}

	@Override
	public List<Map<String, Object>> getCntrnoInfoOfSameAsGuarantorIdCase(
			List<String> idList) {

		if (idList.isEmpty()) {
			return new ArrayList<Map<String, Object>>();
		}

		String questionMark = "";
		for (int i = 0; i < idList.size(); i++) {
			questionMark += "?, ";
		}
		questionMark = questionMark.substring(0, questionMark.length() - 2);

		int paramSize = idList.size();

		Object[] params = new Object[paramSize];
		for (int i = 0; i < paramSize; i++) {
			params[i] = idList.get(i);
		}

		return getJdbc().queryForListByCustParam(
				"getCntrnoInfoOfSameAsGuarantorIdCase",
				new Object[] { questionMark }, params);
	}

	/**
	 * 查詢 集團企業授信條件比較表(含簽註意見)
	 */
	@Override
	public List<Map<String, Object>> findG120M01A_selByCustIdAndBrId(
			String custId, String brId) {
		return this.getJdbc().queryForList("G120M01A.selByCustIdAndBrId",
				new Object[] { custId, brId });
	}

	/**
	 * 新增集團企業授信條件比較表(含簽註意見)
	 */
	@Override
	public void g120M01A_createNew(Map<String, Object> g120m01aMap) {
		String[] cols = new String[] { "createTime", "creator", "custId",
				"custName", "docStatus", "docURL", "dupNo", "mainId",
				"ownBrId", "randomCode", "txCode", "typCd", "uid", "unitType",
				"updateTime", "updater", "dataColId", "dataType" };
		Object[] args = new Object[cols.length];
		for (int i = 0; i < cols.length; i++) {
			args[i] = g120m01aMap.get(cols[i]);
		}
		this.getJdbc().update("G120M01A.createNew", args);
	}

	/**
	 * 新增集團企業授信條件比較表(含簽註意見)A01A
	 */
	@Override
	public void g120A01A_createNew(String mainId, String uid, String ownBrid) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		this.getJdbc().update(
				"G120A01A.createNew",
				new Object[] { mainId, uid, CapDate.getCurrentTimestamp(),
						DocAuthTypeEnum.MODIFY.getCode(), ownBrid, ownBrid,
						user.getUserId() });
	}

	/**
	 * 新增集團企業授信條件比較表(含簽註意見)A01B
	 */
	@Override
	public void g120A01B_createNew(String mainId, String uid, String updateUnit) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String agentFlag = "0";
		String docCode = "BSG1200M01";
		String signId = user.getUserId();
		String signName = user.getUserCName();
		String title = "appraiser";
		this.getJdbc().update(
				"G120A01B.createNew",
				new Object[] { mainId, uid, agentFlag, docCode, signId,
						signName, title, updateUnit });
	}

	/**
	 * BDocLog
	 */
	@Override
	public void bDocLog_insert(String metaOid) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String actCode = "C";
		String actDesc = null;
		Timestamp logTime = CapDate.getCurrentTimestamp();
		String unitId = user.getUnitNo();
		String userId = user.getUserId();
		this.getJdbc().update(
				"BDocLog.insert",
				new Object[] { actCode, actDesc, logTime, metaOid, unitId,
						userId });
	}

	/**
	 * CES.BFLOWINST 新增集團企業授信條件比較表(含簽註意見)流程主檔
	 */
	@Override
	public void bFlowInst_insert(Map<String, Object> bFlowInstMap) {
		String[] cols = new String[] { "instId", "defName", "pInstId",
				"pState", "data" };
		Object[] args = new Object[cols.length];
		for (int i = 0; i < cols.length; i++) {
			args[i] = bFlowInstMap.get(cols[i]);
		}
		this.getJdbc().update("BFLOWINST.insert", args);
	}

	/**
	 * CES.BFLOWSEQ 新增額度序號資料到集團企業授信條件比較表(含簽註意見)流程紀錄檔
	 */
	@Override
	public void bFlowSeq_insert(Map<String, Object> bFlowSeqMap) {
		String[] cols = new String[] { "instId", "seq", "state", "userId",
				"roleId", "deptId", "beginTime", "endTime", "INSTDATA" };
		Object[] args = new Object[cols.length];
		for (int i = 0; i < cols.length; i++) {
			args[i] = bFlowSeqMap.get(cols[i]);
		}
		this.getJdbc().update("BFLOWSEQ.insert", args);
	}

	/**
	 * 新增額度序號資料到集團企業授信條件比較表(含簽註意見)
	 */
	@Override
	public void g120S03A_createNew(Map<String, Object> cntrnoData) {
		String[] cols = { "MAINID", "PID", "CUSTID", "DUPNO", "CUSTNAME",
				"CNTRNO", "QUOTA", "BEL", "ACCCODE", "GUARANTOR", "COLLATERAL",
				"RATE", "CREDITPROSPECTS", "AMTCURR", "L140MAINID", "SDATE",
				"EDATE" };
		Object[] vals = new Object[cols.length];
		for (int i = 0; i < cols.length; i++) {
			vals[i] = cntrnoData.get(cols[i]);
		}
		this.getJdbc().update("G120S03A.createNew", vals);
	}

	/**
	 * 國內營業單位海外信保基金基金案件表 LMS180R67
	 */
	@Override
	public List<Map<String, Object>> listLMS180R67(String bgnDate,
			String endDate) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

		String bgn = sdf.format(CapDate.getDate(bgnDate, "yyyy-MM-dd"));
		String end = sdf.format(CapDate.getDate(endDate, "yyyy-MM-dd"));

		// O-111-0072_05097_B1001 Web e-Loan企金授信每月傳送海外信保基金案件表到協銷資訊系統
		return this.getJdbc().queryForListWithMax("rpt.LMS180R67",
				new Object[] { bgn, end, bgn, end, bgn, end, bgn, end });
	}

	@Override
	public List<Map<String, Object>> findC120jsonByMainIdAndTab(String mainId,
			String tab, String subTab) {
		return this.getJdbc().queryForList("C120JSON.selByMainIdAndTab",
				new Object[] { tab, subTab, mainId });
	}

	@Override
	public void batchInsert_L290M01A(List<Object[]> batchValues) {
		_batchUpdate("L290M01A.insert", new int[] { Types.CHAR, Types.CHAR,
				Types.CHAR, Types.DECIMAL, Types.DECIMAL, Types.DECIMAL,
				Types.DECIMAL, Types.CHAR, Types.DATE, Types.DATE, Types.DATE,
				Types.CHAR, Types.DATE, Types.CHAR, Types.CHAR, Types.CHAR,
				Types.DECIMAL, Types.DECIMAL, Types.CHAR, Types.CHAR }, batchValues);
	}

	// J-110-0081_10702_B1001 Web e-Loan調整PLOAN報表、自動更新案件狀態、NOTES寄送通知信功能
	@Override
	public List<Map<String, Object>> findCLS180R26(String applyStatus,
			String brNo, String applyBegDate, String applyEndDate) {
		String p_begDate = applyBegDate;
		String p_endDate = applyEndDate;
		if (Util.isEmpty(p_begDate)) {
			p_begDate = "1911-01-01";
		}
		if (Util.isEmpty(p_endDate)) {
			p_endDate = "9999-12-31";
		}
		String ts_beg = p_begDate + " 00:00:00";
		String ts_end = p_endDate + " 23:59:59";
		return this.getJdbc().queryForListWithMax("findCLS180R26",
				new String[] { applyStatus + "%", brNo + "%", ts_beg, ts_end });
	}

	// 歡喜信貸案件明細表 CLS180R27
	@Override
	public List<Map<String, Object>> findCLS180R27(String brNo,
			String applyBegDate, String applyEndDate) {
		String p_begDate = applyBegDate;
		String p_endDate = applyEndDate;

		String ts_beg = p_begDate;
		// J-110-0486 修正日期改取endDate後一天，而非當天23:59:59，避免日期時分秒導致SQL錯誤
		String ts_end = Util.trim(TWNDate.toAD(CapDate.shiftDays(
				CapDate.parseDate(p_endDate), 1)));

		return this.getJdbc().queryForListWithMax(
				"findCLS180R27",
				new String[] { ts_beg, ts_end, ts_beg,
						ts_end, brNo + "%" });
	}

	// 歡喜信貸ESG明細表 CLS180R27B
	@Override
	public List<Map<String, Object>> findCLS180R27B(String brNo,
			String applyBegDate, String applyEndDate) {
		String p_begDate = applyBegDate;
		String p_endDate = applyEndDate;

		String ts_beg = p_begDate;
		// J-110-0486 修正日期改取endDate後一天，而非當天23:59:59，避免日期時分秒導致SQL錯誤
		String ts_end = Util.trim(TWNDate.toAD(CapDate.shiftDays(
				CapDate.parseDate(p_endDate), 1)));

		return this.getJdbc().queryForListWithMax(
				"findCLS180R27B",
				new String[] { p_begDate, p_endDate, ts_beg, ts_end, ts_beg,
						ts_end, brNo + "%" });
	}

	// 歡喜信貸KYC分案報表 CLS180R27C
	@Override
	public List<Map<String, Object>> findCLS180R27C(String starTime,
			String endTime) {
		return this.getJdbc().queryForListWithMax("findCLS180R27C",
				new String[] { starTime, endTime });
	}

	// 歡喜信貸婉拒案件自動發送簡訊失敗顧客清單 CLS180R27D
	@Override
	public List<Map<String, Object>> findCLS180R27D(String startDate,
			String endDate) {
		return this.getJdbc().queryForListWithMax("findCLS180R27D",
				new String[] { startDate, endDate });
	}

	/**
	 * 取得內部評等模型主檔MAINID
	 */
	@Override
	public Map<String, Object> findCesM100m01aByOid(String oid) {
		return this.getJdbc().queryForMap("CES.M100M01A.selByOid",
				new Object[] { oid });
	}

	@Override
	public Map<String, Object> findLastTimeCaseByCutIdAndCntrNoOrdByUpdateTime(
			String custId, String dupNo, String cntrNo, String docType,
			String mainId) {
		return this.getJdbc().queryForMap(
				"L140M01A.queryLastTimeCaseByCutIdAndCntrNoOrdByUpdateTime",
				new Object[] { docType, custId, dupNo, cntrNo, mainId });
	}

	@Override
	public List<Map<String, Object>> findCms_selByOid(String cmsOid) {
		return this.getJdbc().queryForList("C100M01.selByOid",
				new Object[] { cmsOid });
	}

	@Override
	public List<Map<String, Object>> findCLS722NonAppropriation(String begDate,
			String endDate) {
		String ts_beg = begDate + " 00:00:00";
		String ts_end = endDate + " 23:59:59";

		return this.getJdbc().queryForListWithMax("findCLS722NonAppropriation",
				new String[] { ts_beg, ts_end });
	}

	/*
	 * J-110-0308 覆審考核表_企金名單
	 */
	@Override
	public List<Map<String, Object>> getLrsMainIdListForL300S01A(
			String authUnit, String branchId, String bgnDate, String endDate) {
		// 因迄日需含當天故設定時間為23:59:59
		SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
		SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd 23:59:59");

		return this.getJdbc().queryForListWithMax(
				"getLrsMainIdListForL300S01A",
				new String[] { authUnit, branchId,
						sdf1.format(CapDate.getDate(bgnDate, "yyyy-MM-dd")),
						sdf2.format(CapDate.getDate(endDate, "yyyy-MM-dd")) });
	}

	@Override
	public List<Map<String, Object>> getLrsListForL300S01A(String authUnit,
			String branchId, String bgnDate, String endDate) {
		// 因迄日需含當天故設定時間為23:59:59
		SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
		SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd 23:59:59");

		return this.getJdbc().queryForListWithMax(
				"getLrsListForL300S01A",
				new String[] { authUnit, branchId,
						sdf1.format(CapDate.getDate(bgnDate, "yyyy-MM-dd")),
						sdf2.format(CapDate.getDate(endDate, "yyyy-MM-dd")) });
	}

	/*
	 * J-110-0308 覆審考核表_消金名單
	 */
	@Override
	public List<Map<String, Object>> getCrsMainIdListForL300S01A(
			String authUnit, String branchId, String bgnDate, String endDate) {
		// 因迄日需含當天故設定時間為23:59:59
		SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
		SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd 23:59:59");

		return this.getJdbc().queryForListWithMax(
				"getCrsMainIdListForL300S01A",
				new String[] { authUnit, branchId,
						sdf1.format(CapDate.getDate(bgnDate, "yyyy-MM-dd")),
						sdf2.format(CapDate.getDate(endDate, "yyyy-MM-dd")) });
	}

	@Override
	public List<Map<String, Object>> getCrsListForL300S01A(String authUnit,
			String branchId, String bgnDate, String endDate) {
		// 因迄日需含當天故設定時間為23:59:59
		SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
		SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd 23:59:59");

		return this.getJdbc().queryForListWithMax(
				"getCrsListForL300S01A",
				new String[] { authUnit, branchId,
						sdf1.format(CapDate.getDate(bgnDate, "yyyy-MM-dd")),
						sdf2.format(CapDate.getDate(endDate, "yyyy-MM-dd")) });
	}

	/*
	 * J-110-0308 覆審考核表_取得排名
	 */
	@Override
	public List<Map<String, Object>> getRankingBoard(String authUnit,
			String bgnDate, String endDate, String[] SpecialBranchList) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

		String brNosParam = Util.genSqlParam(SpecialBranchList);
		String sqlParam = "";
		if (Util.isNotEmpty(Util.nullToSpace(brNosParam))) {
			sqlParam = " OR B.BRNO IN (" + brNosParam + ")";
		}
		List<Object> params = new ArrayList<Object>();
		params.add(authUnit);
		params.add(sdf.format(CapDate.getDate(bgnDate, "yyyy-MM-dd")));
		params.add(sdf.format(CapDate.getDate(endDate, "yyyy-MM-dd")));
		params.add(authUnit);
		if (Util.isNotEmpty(Util.nullToSpace(sqlParam))) {
			params.addAll(Arrays.asList(SpecialBranchList));
		}
		/*
		 * List<String> r = new ArrayList<String>(); for (String brNo :
		 * SpecialBranchList) { r.add("'" + Util.trim(brNo) + "'"); } if
		 * (r.size() == 0) { r.add("''"); } String brNos = StringUtils.join(r,
		 * ",");
		 */

		return this.getJdbc().queryForAllListByCustParam(
				"L300M01A.getRankingBoard", new Object[] { sqlParam },
				params.toArray(new Object[0]));
		/*
		 * new String[] { authUnit, sdf.format(CapDate.getDate(bgnDate,
		 * "yyyy-MM-dd")), sdf.format(CapDate.getDate(endDate, "yyyy-MM-dd")),
		 * authUnit });
		 */
	}

	@Override
	public List<Map<String, Object>> findL120s21aByMainId(String mainId) {
		return this.getJdbc().queryForListWithMax("L120S21A.selByMainId",
				new Object[] { mainId });
	}

	@Override
	public List<Map<String, Object>> findL120s21bByMainId(String mainId) {
		return this.getJdbc().queryForListWithMax("L120S21B.selByMainId",
				new Object[] { mainId });
	}

	@Override
	public List<Map<String, Object>> findL120s21cByMainId(String mainId) {
		return this.getJdbc().queryForListWithMax("L120S21C.selByMainId",
				new Object[] { mainId });
	}

	@Override
	public List<Map<String, Object>> find_J_111_0022_CLSA(String param, int cnt) {
		return this.getJdbc().queryForList("select_J-111-0022_CLSA",
				new Object[] { param + "%" }, 1, cnt);
	}

	@Override
	public void batchUpdate_J_111_0022_CLSA(List<Object[]> batchValues) {
		_batchUpdate("update_J-111-0022_CLSA", new int[] { Types.CHAR },
				batchValues);
	}

	@Override
	public List<Map<String, Object>> find_J_111_0022_CLSB(String param, int cnt) {
		return this.getJdbc().queryForList("select_J-111-0022_CLSB",
				new Object[] { param + "%" }, 1, cnt);
	}

	@Override
	public void batchUpdate_J_111_0022_CLSB(List<Object[]> batchValues) {
		_batchUpdate("update_J-111-0022_CLSB", new int[] { Types.CHAR },
				batchValues);
	}

	/**
	 * LMS180R69 企金綠色授信暨ESG簽報案件明細表
	 * 
	 * Web e-Loan企金授信新增綠色支出、永續績效連結授信ESG
	 * 
	 * Sheet3_分行明細
	 * 
	 * @param bgnDate
	 * @param endDate
	 * @return
	 */
	@Override
	public List<Map<String, Object>> listLMS180R69_1(String bgnDate,
			String endDate, String brType, String brNo) {

		String w1 = "";
		if (Util.equals("2", brType)) {
			w1 = "WHERE BRNGROUP = ?";
			return this.getJdbc().queryForAllListByCustParam("rpt.LMS180R69_1",
					new Object[] { w1 },
					new Object[] { bgnDate, endDate, brNo });
		} else if (Util.equals("3", brType)) {
			w1 = "WHERE BRNO = ?";
			return this.getJdbc().queryForAllListByCustParam("rpt.LMS180R69_1",
					new Object[] { w1 },
					new Object[] { bgnDate, endDate, brNo });
		} else if (Util.equals("4", brType)) {
			// G-111-0168_05097_B1001 新增海外分(子)行「綠色授信」及「永續績效連結授信」等註記
			w1 = "WHERE (BRNO = ? OR COUNTRYTYPE != ? ) ";
			return this.getJdbc().queryForAllListByCustParam("rpt.LMS180R69_1",
					new Object[] { w1 },
					new Object[] { bgnDate, endDate, "025", "TW" });
		} else {
			w1 = "";
			return this.getJdbc().queryForAllListByCustParam("rpt.LMS180R69_1",
					new Object[] { w1 }, new Object[] { bgnDate, endDate });
		}

	}

	/**
	 * LMS180R76 授信案件曾涉及ESG風險因而有條件通過或未核准之情形表
	 * 
	 * J-112-0426_05097_B1002 為正確統計涉及ESG風險授信案件之審查結果新增按月產生報表。
	 * 
	 * 
	 * @param bgnDate
	 * @param endDate
	 * @return
	 */
	@Override
	public List<Map<String, Object>> listLMS180R76_1(String bgnDate,
			String endDate, String brType, String brNo) {

		String w1 = "";
		if (Util.equals("2", brType)) {
			// BY 營運中心
			w1 = "WHERE BRNGROUP = ?";
			return this.getJdbc().queryForAllListByCustParam("rpt.LMS180R76_1",
					new Object[] { w1 },
					new Object[] { bgnDate, endDate, brNo });
		} else if (Util.equals("3", brType)) {
			// BY 分行
			w1 = "WHERE BRNO = ?";
			return this.getJdbc().queryForAllListByCustParam("rpt.LMS180R76_1",
					new Object[] { w1 },
					new Object[] { bgnDate, endDate, brNo });
		} else if (Util.equals("4", brType)) {
			// BY 海業處
			w1 = "WHERE (BRNO = ? OR COUNTRYTYPE != ? ) ";
			return this.getJdbc().queryForAllListByCustParam("rpt.LMS180R76_1",
					new Object[] { w1 },
					new Object[] { bgnDate, endDate, "025", "TW" });
		} else if (Util.equals("5", brType)) {
			// BY 企金處
			w1 = "WHERE (BRNO != ? AND COUNTRYTYPE = ? ) ";
			return this.getJdbc().queryForAllListByCustParam("rpt.LMS180R76_1",
					new Object[] { w1 },
					new Object[] { bgnDate, endDate, "025", "TW" });
		} else {
			// BY 授管處
			w1 = "";
			return this.getJdbc().queryForAllListByCustParam("rpt.LMS180R76_1",
					new Object[] { w1 }, new Object[] { bgnDate, endDate });
		}

	}

	/**
	 * J-110-0540_05097_B1001 Web e-Loan企金授信配合調整E-loan系統動用審核表部分內容
	 */
	@Override
	public List<Map<String, Object>> findCmsCollType06ByCntrNo(String cntrNo) {
		return this.getJdbc().queryForListWithMax("CMS.findCollType06ByCntrNo",
				new Object[] { cntrNo });
	}

	/**
	 * J-110-0505_05097_B1001 Web
	 * e-Loan授信覆審系統，新增引進覆審案件最新之授信案件批覆書功能，產生之PDF放置於附加檔案中，以供調閱
	 * 
	 * @param cntrNo_list
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findLastL140m01AByCntrNos(
			Set<String> cntrNo_list) {

		String cntrNoParam = Util.genSqlParam(cntrNo_list
				.toArray(new String[0]));
		// List<String> r = new ArrayList<String>();
		// for (String cntrNo : cntrNo_list) {
		// r.add("'" + Util.trim(cntrNo) + "'");
		// }
		// if (r.size() == 0) {
		// r.add("''");
		// }
		// String cntrNos = StringUtils.join(r, ",");
		return this.getJdbc().queryForAllListByCustParam(
				"findLastL140m01AByCntrNos", new Object[] { cntrNoParam },
				cntrNo_list.toArray(new String[0]));
	}

	@Override
	public List<Map<String, Object>> findPrintL140M01AByOidForLMS1935(String oid) {
		return this.getJdbc().queryForListWithMax(
				"findPrintL140M01AByOidForLMS1935", new Object[] { oid });
	}

	@Override
	public List<Map<String, Object>> checkL192S01AHavaCntrNoForLMS1935(
			String oid) {
		return this.getJdbc().queryForListWithMax(
				"checkL192S01AHavaCntrNoForLMS1935", new Object[] { oid });
	}

	@Override
	public List<Map<String, Object>> findPrintL140M01AByOidForLMS1935WithoutCntrNo(
			String oid) {
		return this.getJdbc().queryForListWithMax(
				"findPrintL140M01AByOidForLMS1935WithoutCntrNo",
				new Object[] { oid });
	}

	/**
	 * J-110-0547 為控管先行動用之授信案件，增加先行動用呈核及控制表預定補全日期之通知功能。 抓出需要寫入ELF601貸後管理的先行動用案件
	 * 
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findL160m01ANeedElf601(String startDate,
			String endDate) {
		return this.getJdbc().queryForListWithMax("findL160m01ANeedElf601",
				new Object[] { startDate, endDate });
	}

	
	@Override
	public List<Map<String, Object>> findL120m01aWithNoL120m01l(String startDate,
			String endDate) {
		return this.getJdbc().queryForListWithMax("findL120m01aWithNoL120m01l",
				new Object[] { startDate, endDate });
	}
	
	/**
	 * J-110-0547 為控管先行動用之授信案件，增加先行動用呈核及控制表預定補全日期之通知功能。
	 * 抓出當日補全資料，需要將ELF601、ELF602壓成完成
	 * 
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findL160m01AElf601Complete(
			String startDate, String endDate) {
		return this.getJdbc().queryForListWithMax("findL160m01AElf601Complete",
				new Object[] { startDate, endDate });
	}

	/**
	 * 
	 * J-110-0505_05097_B1001 Web
	 * e-Loan授信覆審系統，新增引進覆審案件最新之授信案件批覆書功能，產生之PDF放置於附加檔案中，以供調閱
	 */
	@Override
	public List<Map<String, Object>> findLastL140m01AByCustId(String custId,
			String dupNo, Set<String> cntrNo_list) {
		String cntrNoParam = Util.genSqlParam(cntrNo_list
				.toArray(new String[0]));
		List<Object> params = new ArrayList<Object>();

		params.addAll(cntrNo_list);
		params.add(custId);
		params.add(dupNo);
		params.add(custId);
		params.add(dupNo);
		// String cntrNos = "";
		// if (cntrNo_list != null && !cntrNo_list.isEmpty()) {
		// List<String> r = new ArrayList<String>();
		// for (String cntrNo : cntrNo_list) {
		// r.add("'" + Util.trim(cntrNo) + "'");
		// }
		// if (r.size() == 0) {
		// r.add("''");
		// }
		// cntrNos = StringUtils.join(r, ",");
		// }

		return this.getJdbc().queryForAllListByCustParam(
				"findLastL140m01AByCustId", new Object[] { cntrNoParam },
				params.toArray(new Object[0]));

	}

	/**
	 * J-111-0107_05097_B1001 Web e-Loan企金增加借戶ESG外部綜合評分資料相關資料。
	 * 
	 * @param stkNo
	 * @return
	 */
	@Override
	public Map<String, Object> findL290m01aByStkNo(String stkNo) {
		return this.getJdbc().queryForMap("L290M01A.getBySTKNO",
				new Object[] { stkNo });
	}

	/**
	 * J-112-0337 配合授審處，在簽報書及常董會提案稿，社會責任與環境風險評估大項中，增加本行ESG風險評級結果
	 */
	@Override
	public Map<String, Object> findLastC290m01aByCustId(String custId) {
		return this.getJdbc().queryForMap("C290M01A.getLastByCustId",
				new Object[] { custId });
	}

	@Override
	public Map<String, Object> findLastC280m01aByCustId(String custId) {
		return this.getJdbc().queryForMap("C280M01A.getLastByCustId",
				new Object[] { custId });
	}

	@Override
	public Map<String, Object> findLastC280m01aHadSbtiByCustId(String custId) {
		return this.getJdbc().queryForMap("C280M01A.getLastHadSbtiByCustId",
				new Object[] { custId });
	}

	@Override
	public List<Map<String, Object>> findLastC300s01a(String custId) {
		return this.getJdbc().queryForListWithMax("C300S01A.getLastByCustId",
				new Object[] { custId });
	}

	/**
	 * J-111-0107_05097_B1001 Web e-Loan企金增加借戶ESG外部綜合評分資料相關資料。
	 * 
	 * @return
	 */
	@Override
	public Map<String, Object> findL290m01aMaxDate() {
		return this.getJdbc().queryForMap("L290M01A.getMaxDate",
				new Object[] {});
	}

	@Override
	public Map<String, Map<String, Object>> getCaseNoByCntrnoApprovedData(
			List<String> cntrNoList) {

		if (cntrNoList.isEmpty()) {
			return new HashMap<String, Map<String, Object>>();
		}

		String questionMark = "";
		for (int i = 0; i < cntrNoList.size(); i++) {
			questionMark += "?, ";
		}
		questionMark = questionMark.substring(0, questionMark.length() - 2);

		int paramSize = cntrNoList.size();
		Object[] params = new Object[paramSize];

		for (int i = 0; i < paramSize; i++) {
			params[i] = cntrNoList.get(i);
		}

		List<Map<String, Object>> list = getJdbc().queryForListByCustParam(
				"findCaseNoByCntrnoApprovedData",
				new Object[] { questionMark }, params);

		Map<String, Map<String, Object>> rtnMap = new HashMap<String, Map<String, Object>>();
		for (Map<String, Object> m : list) {
			rtnMap.put(String.valueOf(m.get("CNTRNO")), m);
		}

		return rtnMap;
	}

	/**
	 * J-110-0548 擔保品謄本 追蹤日(追蹤事項通知日期)前最新一筆擔保品
	 */
	@Override
	public Map<String, Object> findLastC101m01(String brNo, String cntrNo,
			String elf601_unid, String followDate) {
		return this.getJdbc().queryForMap("CMS.C101M01.getLastElandData",
				new Object[] { brNo, cntrNo, elf601_unid, followDate });
	}

	/**
	 * J-110-0548 擔保品謄本 依擔保品申請編號取得謄本資料
	 */
	@Override
	public Map<String, Object> findC101m01ByApplyNo(String applyNo) {
		return this.getJdbc().queryForMap("CMS.C101M01.getElandDataByApplyNo",
				new Object[] { applyNo });
	}

	@Override
	public List<Map<String, Object>> findC101m01List(String brNo,
			String cntrNo, String elf601_unid, String bgnDate, String endDate) {
		return this.getJdbc().queryForListWithMax(
				"CMS.C101M01.getElandDataList",
				new Object[] { brNo, cntrNo, elf601_unid, bgnDate, endDate });

	}

	@Override
	public List<Map<String, Object>> findC100s02aList(String c101m01MainId) {
		return this.getJdbc().queryForListWithMax(
				"CMS.C100S02A.getListByMainId", new Object[] { c101m01MainId });

	}

	@Override
	public Map<String, Object> findC100s02aByOid(String c100s02aOid) {
		return this.getJdbc().queryForMap("CMS.C100S02A.getByOid",
				new Object[] { c100s02aOid });
	}

	@Override
	public List<Map<String, Object>> findC101m29List(String brNo,
			String cntrNo, String elf601_unid, String bgnDate, String endDate) {
		SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
		SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd 23:59:59");

		return this.getJdbc().queryForListWithMax(
				"CMS.C101M29.getRaspDataList",
				new Object[] { brNo, cntrNo, elf601_unid,
						sdf1.format(CapDate.getDate(bgnDate, "yyyy-MM-dd")),
						sdf2.format(CapDate.getDate(endDate, "yyyy-MM-dd")) });

	}

	@Override
	public Map<String, Object> findC101m29ByOid(String c101m29Oid) {
		return this.getJdbc().queryForMap("CMS.C101M29.getByOid",
				new Object[] { c101m29Oid });
	}

	@Override
	public Map<String, Object> findC101m29Cnt(String brNo, String cntrNo,
			String elf601_unid, String bgnDate, String endDate) {
		SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
		SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd 23:59:59");

		return this.getJdbc().queryForMap(
				"CMS.C101M29.getRaspDataCnt",
				new Object[] { brNo, cntrNo, elf601_unid,
						sdf1.format(CapDate.getDate(bgnDate, "yyyy-MM-dd")),
						sdf2.format(CapDate.getDate(endDate, "yyyy-MM-dd")) });

	}

	/**
	 * J-111-0423_05097_B1001 Web
	 * e-Loan企金授信就海外分行承做永續績效連結授信案(如附件)，於E-Loan「永續績效連結授信」相關註記
	 * 
	 */
	@Override
	public void updateEsgDataFromLms2105v01ServiceImpl(String cntrNo,
			String esgSustainLoan, String esgSustainLoanType,
			String esgSustainLoanUnReach) {
		this.getJdbc()
				.update("UPDATE LMS.L140M01A SET ESGSUSTAINLOAN = ?,ESGSUSTAINLOANTYPE = ?,ESGSUSTAINLOANUNREACH=? WHERE CNTRNO =? ",
						new Object[] { esgSustainLoan, esgSustainLoanType,
								esgSustainLoanUnReach, cntrNo });

	}

	@Override
	public List<Map<String, Object>> cls_call_center_amort_cnt(String amort_ym) {
		return this.getJdbc().queryForListWithMax(
				"select_J-111-0455_call_center_amort_cnt",
				new Object[] { amort_ym,amort_ym });
	}

	@Override
	public int fill_call_center_amort_column(String amort_ym, Timestamp begTS) {
		return this.getJdbc().update(
				"update_J-111-0455_call_center_amort_column",
				new Object[] { amort_ym, begTS });
	}

	/**
	 * LMS180R72 企金授信核准案件BIS評估表
	 * 
	 * J-111-0443_05097_B1001 Web e-Loan企金授信開發授信BIS評估表
	 */
	@Override
	public List<Map<String, Object>> listLMS180R72_01(List<String> custQuery,
			List<String> custParam) {

		return this.getJdbc().queryForAllListByCustParam("rpt.LMS180R72_01",
				custQuery.toArray(), custParam.toArray());

	}
	
	@Override
	public List<Map<String, Object>> listLMS180R72_02(List<String> custQuery,
			List<String> custParam, String caseOutputType) {
		
		if("1".equals(caseOutputType)){//依額度明細
			return this.getJdbc().queryForAllListByCustParam("rpt.LMS180R72_02",
					custQuery.toArray(), custParam.toArray());
		} else {//依簽報書
			return this.getJdbc().queryForAllListByCustParam("rpt.LMS180R72_01_NEW",
					custQuery.toArray(), custParam.toArray());
		}

	}

	/**
	 * J-111-0411_05097_B1002 Web e-Loan企金授信新增不動產授信例外管理相關功能
	 * 
	 * @param bgnDate
	 * @param endDate
	 * @return
	 */
	@Override
	public List<Map<String, Object>> listLMS180R73_01(List<String> custQuery,
			List<String> custParam) {

		return this.getJdbc().queryForAllListByCustParam("rpt.LMS180R73_01",
				custQuery.toArray(), custParam.toArray());

	}

	/**
	 * J-111-0615_05097_B1001 Web e-Loan授信系統管理報表不動產授信例外管理報表修改
	 * 
	 * @param bgnDate
	 * @param endDate
	 * @return
	 */
	@Override
	public List<Map<String, Object>> listLMS180R73_02(List<String> custQuery,
			List<String> custParam) {

		return this.getJdbc().queryForAllListByCustParam("rpt.LMS180R73_02",
				custQuery.toArray(), custParam.toArray());

	}

	/**
	 * J-111-0535_05097_B1001 Web e-Loan企金授信配合「ESG綜效調查表 」建置，於簽報書增設相對應欄位
	 * 
	 * @param bgnDate
	 * @param endDate
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findCesEsgRejectByCustId(String custId,
			String dupNo) {
		return this.getJdbc().queryForListWithMax(
				"CES.C280M01A.getEsgRejectByCustId",
				new String[] { custId, dupNo });
	}

	@Override
	public Map<String, String> getC122m01aIntroductionBranchNoByApprovedSigningBook() {

		Map<String, String> rtnMap = new HashMap<String, String>();

		List<Map<String, Object>> list = this.getJdbc().queryForListWithMax(
				"getC122m01aIntroductionBranchNoByApprovedSigningBook",
				new Object[] {});
		for (Map<String, Object> map : list) {
			rtnMap.put(String.valueOf(map.get("CNTRNO")),
					String.valueOf(map.get("ORGBRID")));
		}

		return rtnMap;
	}

	/**
	 * J-111-06XX_05097_B1001 Web e-Loan授信系統不動產暨72-2相關資訊註記維護之頁面，增列補鍵產品種類33、34之功能
	 * 
	 * @param cntrNo
	 * @param prodKind
	 * @param adcCaseNo
	 */
	@Override
	public void updateAdcInfo(String cntrNo, String prodKind, String adcCaseNo) {
		this.getJdbc().update("LMS.L140M01A.update_PROD_KIND_ByCntrNo",
				new Object[] { prodKind, adcCaseNo, cntrNo });

	}

	/**
	 * J-112-0021_05097_B1001 Web e-Loan企金授信異常通報案件結果副知徵信單位
	 * 
	 * 全行最新資信簡表
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	@Override
	public Map<String, Object> findC120M01A_CompleteLast(String custId,
			String dupNo) {
		return this.getJdbc().queryForMap("C120M01A.findCompleteLast",
				new String[] { custId, dupNo, custId, dupNo });
	}

	/**
	 * J-112-0037_05097_B1004 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
	 * 
	 * @param codeType
	 * @param codeValue
	 * @param locale
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findCodeTypeOrderByCodeValueAsc(
			String codeType, String codeValue, String locale) {

		// SELECT * FROM com.BCODETYPE WHERE CODETYPE = 'lms140_lgdTotAmt_VER'
		// AND CODEVALUE LIKE 'TW%' AND LOCALE = 'zh_TW' ORDER BY codeValue DESC

		return this.getJdbc().queryForListWithMax(
				"COM.BCODETYPE.findCodeTypeOrderByCodeValue",
				new Object[] { codeType, "%" + codeValue + "%", locale });
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.eloandb.service.EloandbBASEService#getCaseLvlParamVersion
	 * (java.lang.String, java.lang.String, java.lang.String, java.lang.String)
	 */
	@Override
	public List<Map<String, Object>> getCaseLvlParamVersion(String codeType,
			String codeValue, String codeDesc, String locale) {

		// SELECT * FROM com.BCODETYPE WHERE CODETYPE = ? AND CODEVALUE = ? AND
		// CODEDESC LIKE ? AND LOCALE = ? ORDER BY codeValue ASC
		return this.getJdbc().queryForListWithMax(
				"COM.BCODETYPE.getCaseLvlParamVersion",
				new Object[] { codeType, codeValue, "%" + codeDesc + "%",
						locale });
	}

	// J-110-0330_11565_B1001 e-Loan AO帳戶管理員
	@Override
	public List<Map<String, Object>> findL140M01A_GroupByCust(String mainId) {
		return this.getJdbc().queryForListWithMax("findL140M01A_GroupByCust",
				new Object[] { mainId });
	}

	/**
	 * 經濟部協助中小型事業疫後振興專案貸款 & 經濟部協助中小企業轉型發展專案貸款
	 */
	@Override
	public List<Map<String, Object>> findL140M01_byLastCntrNoWithCustidRescueItem(
			List<String> custQuery, List<String> custParam) {

		return this.getJdbc().queryForAllListByCustParam(
				"L140M01A.selLastCntrNoByCustIdRescueItem",
				custQuery.toArray(), custParam.toArray());

	}

	/**
	 * 中小企業千億振興融資方案」統計報表
	 * 
	 * 表1-「中小企業千億振興融資方案」資金用途及各月辦理情形統計(已核准)
	 * 
	 * J-112-0200_05097_B1001 Web e-Loan系統新增「中小企業千億振興融資方案」統計報表
	 */
	@Override
	public List<Map<String, Object>> listLMS180R74_01(String effectDate,
			String bgnDate, String endDate) {

		return this.getJdbc().queryForListWithMax("rpt.LMS180R74_01",
				new Object[] { effectDate, bgnDate, endDate });
	}

	/**
	 * J-113-0125_05097_B1001
	 * 中小企業千億振興融資方案核准明細表篩選額度增列額度性質為「續約」之額度(包含變更條件、續約及增減額、續約)等
	 */
	@Override
	public List<Map<String, Object>> listLMS180R74_a_01(List<String> custQuery,
			List<String> custParam) {

		return this.getJdbc().queryForAllListByCustParam("rpt.LMS180R74_01",
				custQuery.toArray(), custParam.toArray());
	}

	/**
	 * 中小企業千億振興融資方案」統計報表
	 * 
	 * 表2-「中小企業千億振興融資方案」承作類別及金額累計統計表(已核准)
	 * 
	 * J-112-0200_05097_B1001 Web e-Loan系統新增「中小企業千億振興融資方案」統計報表
	 */
	@Override
	public List<Map<String, Object>> listLMS180R74_02(String effectDate,
			String bgnDate, String endDate) {

		return this.getJdbc().queryForListWithMax("rpt.LMS180R74_02",
				new Object[] { effectDate, bgnDate, endDate });
	}

	/**
	 * J-113-0125_05097_B1001
	 * 中小企業千億振興融資方案核准明細表篩選額度增列額度性質為「續約」之額度(包含變更條件、續約及增減額、續約)等
	 */
	@Override
	public List<Map<String, Object>> listLMS180R74_a_02(List<String> custQuery,
			List<String> custParam) {

		return this.getJdbc().queryForAllListByCustParam("rpt.LMS180R74_02",
				custQuery.toArray(), custParam.toArray());
	}

	/**
	 * 中小企業千億振興融資方案」統計報表
	 * 
	 * 表3-「中小企業千億振興融資方案」核准明細表(已核准)
	 * 
	 * J-112-0200_05097_B1001 Web e-Loan系統新增「中小企業千億振興融資方案」統計報表
	 */
	@Override
	public List<Map<String, Object>> listLMS180R74_03(String effectDate,
			String bgnDate, String endDate) {

		return this.getJdbc().queryForListWithMax("rpt.LMS180R74_03",
				new Object[] { effectDate, bgnDate, endDate });
	}

	/**
	 * J-113-0125_05097_B1001
	 * 中小企業千億振興融資方案核准明細表篩選額度增列額度性質為「續約」之額度(包含變更條件、續約及增減額、續約)等
	 */
	@Override
	public List<Map<String, Object>> listLMS180R74_a_03(List<String> custQuery,
			List<String> custParam) {

		return this.getJdbc().queryForAllListByCustParam("rpt.LMS180R74_03",
				custQuery.toArray(), custParam.toArray());
	}

	/**
	 * 中小企業千億振興融資方案」統計報表
	 * 
	 * 表4-「中小企業千億振興融資方案」簽案明細表(未核准暨已核准)
	 * 
	 * J-112-0200_05097_B1001 Web e-Loan系統新增「中小企業千億振興融資方案」統計報表
	 */
	@Override
	public List<Map<String, Object>> listLMS180R74_04(String effectDate,
			String bgnDate, String endDate) {

		return this.getJdbc().queryForListWithMax("rpt.LMS180R74_04",
				new Object[] { effectDate, bgnDate, endDate });
	}

	/**
	 * J-113-0125_05097_B1001
	 * 中小企業千億振興融資方案核准明細表篩選額度增列額度性質為「續約」之額度(包含變更條件、續約及增減額、續約)等
	 */
	@Override
	public List<Map<String, Object>> listLMS180R74_a_04(List<String> custQuery,
			List<String> custParam) {

		return this.getJdbc().queryForAllListByCustParam("rpt.LMS180R74_04",
				custQuery.toArray(), custParam.toArray());
	}

	/**
	 * J-112-0342 新增產生企金授信簽報案件明細檔
	 */
	@Override
	public List<Map<String, Object>> listLMS180R75(String mainId) {
		return this.getJdbc().queryForListWithMax("rpt.LMS180R75",
				new Object[] { mainId, mainId });
	}

	/**
	 * J-112-0342 新增產生企金授信簽報案件明細檔
	 */
	@Override
	public void batchInsert_LXLSR75A(List<Object[]> valList) {
		_batchUpdate("LXLSR75A.insert",
				new int[] { Types.CHAR, Types.VARCHAR }, valList);
	}

	/**
	 * J-112-0342 新增產生企金授信簽報案件明細檔
	 */
	@Override
	public void LXLSR75A_deleteByDocFileOid(String docFileOid) {
		this.getJdbc().update("LXLSR75A.deleteByDocFileOid",
				new Object[] { docFileOid });
	}

	/**
	 * J-112-0357 新增敘做條件異動比較表
	 */
	@Override
	public List<Map<String, Object>> listLMSDoc11A(String mainId) {
		return this.getJdbc().queryForListWithMax("rpt.LMSDoc11A",
				new Object[] { mainId });
	}

	@Override
	public Page<Map<String, Object>> queryOtherL140S11AByL140M01A(
			ISearch search, String caseMainId, String itemType, String cntrNo) {
		return this.getJdbc().queryForPage(search,
				"L140S11A.findOtherByL140M01A",
				new Object[] { caseMainId, itemType, cntrNo });
	}

	/**
	 * 
	 * J-111-0554 配合授審處增進管理效益，修改相關功能程式 add 列印擔保品設定資料表
	 */
	@Override
	public List<Map<String, Object>> findCMS_C100m01byCntrno(
			Set<String> cntrNo_list) {
		if (cntrNo_list == null || cntrNo_list.size() == 0) {
			cntrNo_list = new HashSet<String>();
			cntrNo_list.add("");
		}
		// String cntrNos = "";
		String cntrNoSqlParam = Util.genSqlParam(cntrNo_list
				.toArray(new String[0]));
		// if (cntrNo_list != null && !cntrNo_list.isEmpty()) {
		// List<String> r = new ArrayList<String>();
		// for (String cntrNo : cntrNo_list) {
		// r.add("'" + Util.trim(cntrNo) + "'");
		// }
		// if (r.size() == 0) {
		// r.add("''");
		// }
		// cntrNos = StringUtils.join(r, ",");
		// }

		return this.getJdbc().queryForAllListByCustParam(
				"findCMS_C100m01byCntrno", new Object[] { cntrNoSqlParam },
				cntrNo_list.toArray(new String[0]));
	}

	@Override
	public List<Map<String, Object>> findCMS_C100m01byMainId(
			Set<String> mainId_list) {
		if (mainId_list == null || mainId_list.size() == 0) {
			mainId_list = new HashSet<String>();
			mainId_list.add("");
		}
		// String mainIds = "";
		String mainIdSqlParam = Util.genSqlParam(mainId_list
				.toArray(new String[0]));
		// if (mainId_list != null && !mainId_list.isEmpty()) {
		// List<String> r = new ArrayList<String>();
		// for (String mainId : mainId_list) {
		// r.add("'" + Util.trim(mainId) + "'");
		// }
		// if (r.size() == 0) {
		// r.add("''");
		// }
		// mainIds = StringUtils.join(r, ",");
		// }

		return this.getJdbc().queryForAllListByCustParam(
				"findCMS_C100m01byMainId", new Object[] { mainIdSqlParam },
				mainId_list.toArray(new String[0]));
	}

	/**
	 * J-112-0196 動審表送呈檢查是否有已覆核登記之保證擔保品
	 */
	@Override
	public List<Map<String, Object>> findCmsCollType05ByCntrNo(String cntrNo) {
		return this.getJdbc().queryForListWithMax("CMS.findCollType05ByCntrNo",
				new Object[] { cntrNo });
	}

	/**
	 * J-112-0399 分行承作購置住宅貸款年限40年統計表 CLS180R60
	 */
	@Override
	public List<Map<String, Object>> findCLS180R60(Date startDate, Date toDate) {
		SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
		SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd 23:59:59");

		return this.getJdbc().queryForListWithMax("rpt.CLS180R60",
				new Object[] { sdf1.format(startDate), sdf2.format(toDate) });

	}

	/**
	 * J-112-0534 因應兆豐金控自113.1.1下架證券違約交割/上市櫃觀察名單，改引入T70 取得徵信系統的查詢結果
	 */
	@Override
	public List<Map<String, Object>> findC140M01A_selectT70Result(
			String cistId, String dupNo, List<String> cesMainIds) {

		String cesMainIdParams = Util.genSqlParam(cesMainIds);
		List<String> params = new ArrayList<String>();
		params.add(cistId);
		params.add(dupNo);
		params.addAll(cesMainIds);

		return this.getJdbc()
				.queryForListByCustParam("CES.C140M01A.selectT70Result",
						new Object[] { cesMainIdParams },
						params.toArray(new String[0]));
	}

	/**
	 * J-112-0534 因應兆豐金控自113.1.1下架證券違約交割/上市櫃觀察名單，改引入T70 取得徵信系統的查詢結果檔案
	 */
	@Override
	public Map<String, Object> findC140M01A_selectT70Html(String mainId,
			String custId) {

		return this.getJdbc().queryForMap("CES.C140M01A.selectT70Html",
				new Object[] { mainId, "T70", custId });
	}

	@Override
	public List<Map<String, Object>> findHPCLApplyDataForDW(String isFirstTime) {
		String conditionStr = "";
		if (!"Y".equals(isFirstTime)) {// 每日上傳對象不包含以下條件，初次及每週上傳才包含
			conditionStr = " AND ("
					+ " (C122M01A.DOCSTATUS NOT IN ('E02', 'F00', 'I00') "// 動用已覆核、已作廢、取消
					+ " AND C122M01A.DOCSTATUS NOT LIKE 'G%' "// 不承作
					+ " AND C122M01A.DOCSTATUS NOT LIKE 'Z%') "// 系統結案
					+ " OR C122M01A.IS_UPLOADED_DW_HPCL != 'Y') ";// 是否已上傳DW
		}
		return this.getJdbc().queryForAllListByCustParam(
				"findHPCLApplyDataForDW", new Object[] { conditionStr },
				new Object[] {});
	}

	@Override
	public void updateHPCLApplyDataFlagForDW(String[] c122m01aOidArry) {
		if (c122m01aOidArry == null || c122m01aOidArry.length == 0) {
			return;
		} else {
			int limit = 10000;// 如果筆數太多，每次只跑一萬筆
			int tmp = 0;

			ArrayList<List<String>> all = new ArrayList<List<String>>();
			List<String> sub = null;
			// 將傳進來的oid分批到不同陣列存起來
			while (tmp < c122m01aOidArry.length) {
				if (tmp % limit == 0) {
					sub = new ArrayList<String>();
					all.add(sub);
				}
				sub.add(c122m01aOidArry[tmp]);
				tmp++;
			}

			for (List<String> oids : all) {
				String sqlParam = Util.genSqlParam(oids);// 產生?號字串
				this.getJdbc().updateByCustParam(
						"updateHPCLApplyDataFlagForDW",
						new Object[] { sqlParam }, oids.toArray(new String[0]));
			}
		}
	}

	@Override
	public List<Map<String, Object>> findHPCLCaseReportDataForDW(
			String isFirstTime) {
		String conditionStr = "";
		if ("Y".equals(isFirstTime)) {
			conditionStr = " AND L140M01A.DOCSTATUS != '060' ";
		} else if ("D".equals(isFirstTime)) {
			return this.getJdbc().queryForAllListByCustParam(
					"findHPCLCaseReportDataToDeleteForDW", new Object[] {},
					new Object[] {});
		} else {
			conditionStr = " AND (L120M01A.DOCSTATUS NOT IN ('05O', '06O') OR L120M01A.IS_UPLOADED_DW_HPCL != 'Y') ";
		}
		return this.getJdbc().queryForAllListByCustParam(
				"findHPCLCaseReportDataForDW", new Object[] { conditionStr },
				new Object[] {});
	}

	@Override
	public void updateHPCLCaseReportDataFlagForDW(String[] l120m01aOidArry) {
		if (l120m01aOidArry == null || l120m01aOidArry.length == 0) {
			return;
		} else {
			int limit = 10000;// 如果筆數太多，每次只跑一萬筆
			int tmp = 0;

			ArrayList<List<String>> all = new ArrayList<List<String>>();
			List<String> sub = null;
			// 將傳進來的oid分批到不同陣列存起來
			while (tmp < l120m01aOidArry.length) {
				if (tmp % limit == 0) {
					sub = new ArrayList<String>();
					all.add(sub);
				}
				sub.add(l120m01aOidArry[tmp]);
				tmp++;
			}

			for (List<String> oids : all) {
				String sqlParam = Util.genSqlParam(oids);// 產生?號字串
				this.getJdbc().updateByCustParam(
						"updateHPCLCaseReportDataFlagForDW",
						new Object[] { sqlParam }, oids.toArray(new String[0]));
			}
		}
	}

	@Override
	public int queryProjClass22Count(String cntrNo) {
		return this.getJdbc().queryForInt("queryProjClass22Count",
				new Object[] { cntrNo });
	}

	/**
	 * J-113-0044_12473_B1001 取得內部評等試行新模型主檔
	 */
	@Override
	public List<Map<String, Object>> findTmpNewCesM100m01(String custId,
			String dupNo, String[] tmpNewMow) {
		String tmpNewMowParam = Util.genSqlParam(tmpNewMow);
		List<Object> params = new ArrayList<Object>();
		params.addAll(Arrays.asList(custId, dupNo));
		params.addAll(Arrays.asList(tmpNewMow));

		return this.getJdbc().queryForListByCustParam("findTmpNewCesM100m01",
				new Object[] { tmpNewMowParam }, params.toArray(new Object[0]));

	}

	/**
	 * J-113-0009房貸案件明細表 CLS180R61
	 */
	@Override
	public List<Map<String, Object>> findCLS180R61(Date startDate, Date toDate) {
		SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
		SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd 23:59:59");

		return this.getJdbc().queryForListWithMax("rpt.CLS180R61",
				new Object[] { sdf1.format(startDate), sdf2.format(toDate) });

	}

	/**
	 * J-113-0102_05097_B1001 修改e-Loan LGD之公司保證回收率估算規則
	 * 
	 * @param custId
	 * @return
	 */
	@Override
	public Map<String, Object> findC120M01A_selectGrarantorStkCatNm(
			String custId) {
		return this.getJdbc()
				.queryForMap("CES.C120M01A.selectGrarantorStkCatNm",
						new Object[] { custId });
	}

	@Override
    public List<Object[]> findSMEAList(Date startDate, Date endDate) {
        return null;
    }

	@Override
	public Map<String, Object> findOldestL140m01aByCntrNo(String cntrNo) {
		return this.getJdbc().queryForMap(
				"L140M01A.findClsOldestL140m01aByCntrNo",
				new Object[] { cntrNo });
	}


	@Override
	public Map<String, Object> findC260M01AByCustIdDupNo(String custId,
			String dupNo) {
		return this.getJdbc()
		.queryForMap("CES.findC260M01AByCustIdDupNo", new Object[] {custId, dupNo});
	}

	@Override
	public List<Map<String, Object>> findL140mc1bTotalBalanceOrCurrentApplyAmt(String l120m01a_mainId) {
		return this.getJdbc().queryForListWithMax("L140MC1B.findTotalBalanceOrCurrentApplyAmt", new Object[] {l120m01a_mainId});
	}
	
	@Override
	public List<Map<String, Object>> findProdKind59NewCase(String custId, String dupNo) {
		return this.getJdbc().queryForListWithMax("L140M01A.L14S02A.findProdKind59NewCase", new Object[] {custId, dupNo});
	}

	@Override
	public List<Map<String, Object>> findRpsO140m01aByCustIdandCntrNo(String custId, String dupNo, String cntrNo) {
		return this.getJdbc().queryForListWithMax("RPS.O140S01A.O140M01A.findApprovedCaseByCustIdandCntrNo", new Object[] {custId, dupNo,cntrNo});
	}

	@Override
	public void updateRpsO140S01A(String OverdueRec,String OverdueReason, String mainId) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String userId =user.getUserId();
		Date updateTime = new Date();
		this.getJdbc().update("RPS.O140S01A.updateOverdueRec",
				new Object[] { OverdueRec, OverdueReason,userId,updateTime, mainId });

	}

	@Override
	public List<Map<String, Object>> findL260M01A_L260M01DByFilter(StringBuilder condition, List<Object> paramValues) {
		return this.getJdbc().queryForList("L260M01A_L260M01DByFilter",condition.toString(),paramValues.toArray());
	}

}
