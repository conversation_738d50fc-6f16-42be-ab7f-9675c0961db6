function open_Q_page(param){
	
	var mainId = param.mainId;
	var custId = param.custId;
	var dupNo = param.dupNo;
	var handler = param.use_handler;
	
	$('#C101S01QDiv').buildItem();
	$('#C101S01QForm').readOnlyChilds();
	
	$.ajax({
		handler : handler,
		action : 'loadScore',
		formId : 'C101S01QForm', 
		data : {
			'mainId':mainId 
			, 'custId':custId
			, 'dupNo':dupNo
			, 'noOpenDoc':true
			, 'markModel':'2'
		},
		success : function(response) {
			$('#C101S01QForm').setValue(response.C101S01QForm);
			
			$('#C101S01QThickBox').thickbox({
				title : ((i18n.cls1131s01q['C101S01Q.title'] || '')+ DOMPurify.sanitize(response.C101S01QForm.varVer)),
				width : 800,
				height : 450,
				align : 'center',
				valign : 'bottom',
				buttons : {				
					'close' : function() {
						$.thickbox.close();
					}
				}
			});
			// default tab
			$('#C101S01QTab').tabs({
				selected : 0
			});
		}
	});
}

