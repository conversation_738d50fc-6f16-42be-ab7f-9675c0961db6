package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L120S01DDao;
import com.mega.eloan.lms.model.L120S01D;


/** 企金銀行法／金控法利害關係人檔 **/
@Repository
public class L120S01DDaoImpl extends LMSJpaDao<L120S01D, String>
	implements L120S01DDao {

	@Override
	public L120S01D findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}
	@Override
	public List<L120S01D> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L120S01D> list = createQuery(L120S01D.class,search).getResultList();
		return list;
	}
	@Override
	public L120S01D findByUniqueKey(String mainId,String custId,String dupNo){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
	
		return findUniqueOrNone(search);
	}
	@Override
	public List<L120S01D> findByCustIdDupId(String custId,String DupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", DupNo);
		List<L120S01D> list = createQuery(L120S01D.class,search).getResultList();
		return list;
	}
	@Override
	public int delModel(String mainId){
		Query query = getEntityManager().createNamedQuery("L120S01D.delModel");
		query.setParameter("MAINID", mainId); //設置參數
		return query.executeUpdate();
	}
}