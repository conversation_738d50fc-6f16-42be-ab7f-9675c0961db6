/* 
 * L140MM2ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140MM2A;

/** 天然及重大災害受災戶住宅補貼主檔 **/
public interface L140MM2ADao extends IGenericDao<L140MM2A> {

	L140MM2A findByOid(String oid);
	
	List<L140MM2A> findByMainId(String mainId);

	List<L140MM2A> findByIndex2A(String mainId, String ownBrId, String cntrNo);
	
	L140MM2A findByMainIdSeq(String mainId, Integer seq);
}