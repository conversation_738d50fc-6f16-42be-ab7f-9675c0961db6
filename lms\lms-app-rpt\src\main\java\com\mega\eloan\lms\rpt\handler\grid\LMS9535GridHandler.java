/* 
 * LMS9530GridHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.handler.grid;

import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Properties;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.eloan.lms.lms.panels.LMSS07Panel;
import com.mega.eloan.lms.model.L120S01A;
import com.mega.eloan.lms.model.L120S04A;
import com.mega.eloan.lms.model.L120S04B;
import com.mega.eloan.lms.rpt.service.LMS9535Service;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 管理報表查詢往來彙總Grid
 * </pre>
 * 
 * @since 2013/1/2
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/1/2,Miller,new
 *          </ul>
 */
@Scope("request")
@Controller("lms9535gridhandler")
public class LMS9535GridHandler extends AbstractGridHandler {

	@Resource
	LMS9535Service service9535;

	@Resource
	DocFileService docfileservice;

	/**
	 * 查詢L120S04AGrid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120s04a(ISearch pageSetting,
			PageParameters params) throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS07Panel.class);
		// 建立主要Search 條件
		// pageSetting.addOrderBy("custId");
		String mainid = Util.nullToSpace(params.getString("mainId"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainid);
		// 第三個參數為formatting
		Page<? extends GenericBean> page = service9535.findPage(L120S04A.class,
				pageSetting);
		List<L120S04A> list = (List<L120S04A>) page.getContent();

		if (list != null && !list.isEmpty()) {

			Collections.sort(list, new Comparator<L120S04A>() {

				@Override
				public int compare(L120S04A object1, L120S04A object2) {
					// TODO Auto-generated method stub
					int cr = 0;
					String[] resStr1 = Util.trim(object1.getCustRelation())
							.split(",");
					Arrays.sort(resStr1);
					String[] resStr2 = Util.trim(object2.getCustRelation())
							.split(",");
					Arrays.sort(resStr2);

					int a = resStr2[0].compareTo(resStr1[0]);

					String prtFlag1 = object1.getPrtFlag();
					String prtFlag2 = object2.getPrtFlag();
					int prtFlag = prtFlag2.compareTo(prtFlag1);

					if (prtFlag != 0) {
						cr = (prtFlag > 0) ? -1 : 5;
					} else if (a != 0) {
						cr = (a > 0) ? -2 : 4;
					} else {
						long b = (object2.getProfit() == null ? 0 : object2
								.getProfit())
								- (object1.getProfit() == null ? 0 : object1
										.getProfit());
						if (b != 0) {
							cr = (b > 0) ? 3 : -3;
						} else {
							int c = object2.getCustId().compareTo(
									object1.getCustId());
							if (c != 0) {
								cr = (c > 0) ? -4 : 2;
							} else {
								// String oid1 = object1.getOid();
								// String oid2 = object2.getOid();
								// int oidFlag = oid2.compareTo(oid2);
								// if(oidFlag != 0){
								// cr = (oidFlag > 0)? -5:1;
								// }
							}
						}
					}

					return cr;
				}
			});
		}

		for (int i = 0; i < list.size(); i++) {
			L120S04A model = list.get(i);
			if (Util.isEmpty(model.getCustId())) {
				model.setCustId("");
			} else {
				model.setCustId(model.getCustId() + " " + model.getDupNo());
			}
			if (UtilConstants.DEFAULT.是.equals(model.getChkYN())) {
				model.setChkYN("V");
			} else {
				model.setChkYN("X");
			}
			if ("1".equals(model.getPrtFlag())) {
				model.setPrtFlag("V");
			} else {
				model.setPrtFlag("X");
			}

			StringBuilder sb = new StringBuilder();
			String[] strs = Util.trim(model.getCustRelation()).split(",");
			// 對陣列進行排序
			Arrays.sort(strs);
			String custRelationIndex = "";
			for (String s : strs) {
				if (Util.equals(Util.trim(custRelationIndex), "")) {
					custRelationIndex = s;
				}
				if (sb.length() > 0)
					sb.append("/");
				sb.append(Util.trim(pop.getProperty("L1205S07.checkbox" + s)));
			}
			model.setCustRelationIndex(sb.toString());
			model.setCustRelation(custRelationIndex);

		}
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢L120S04AGrid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryL120s04b(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addOrderBy("custId");
		String mainid = Util.trim(params.getString("mainId"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainid);
		// 第三個參數為formatting
		Page<? extends GenericBean> page = service9535.findPage(L120S04B.class,
				pageSetting);
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 依客戶統編查詢L120S01AGrid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryL120s01aById(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		String custId = Util.nullToSpace(params.getString("custId"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);

		// 第三個參數為formatting
		Page<? extends GenericBean> page = service9535.findPage(L120S01A.class,
				pageSetting);
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢檔案上傳的grid
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryfile(ISearch pageSetting, PageParameters params) throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);
		// 查這份文件的MinId
		String mainid = Util.trim(params.getString("mainId"));
		String fieldId = params.getString("fieldId");
		boolean needCngName = params.getBoolean("needCngName");
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainid);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "fieldId",
				fieldId);
		Page<DocFile> page = docfileservice.readToGrid(pageSetting);
		List<DocFile> list = (List<DocFile>) page.getContent();
		if (needCngName) {
			for (DocFile file : list) {
				// other.msg61=借戶暨關係戶與本行授信往來比較表
				// J-107-0225_05097_B1001 Web e-Loan企金授信簽報書新增集團關係企業與本行授信往來條件比較表
				if (Util.equals(Util.trim(file.getSrcFileName()),
						"LMS1205R24A_B.xls")) {
					file.setSrcFileName(pop.getProperty("other.msg61_B")
							+ ".xls");
				} else {
					file.setSrcFileName(pop.getProperty("other.msg61") + ".xls");
				}

			}
		}
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}
}
