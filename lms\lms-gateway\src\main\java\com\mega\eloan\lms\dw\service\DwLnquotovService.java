/* 
 * DwLnquotovService.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dw.service;

import java.util.List;
import java.util.Map;

/**
 * <pre>
 * 海外 - 放款額度匯集日檔
 * 
 * 
 * </pre>
 * 
 * @since
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
public interface DwLnquotovService {

	public List<?> findDW_LNQUOTOV_L120S05A1(String grpNo);

	public List<?> findDW_LNQUOTOV_L120S05B1(String custId, String dupCode);

	public List<Map<String, Object>> findDW_LNQUOTOV_Lcamt(String custId,
			String cntrNo);

	public Map<String, ?> findDW_LNQUOTOV_Contract(String custIdandDupno,
			String branchId, String custid, String dupno);

	/**
	 * (覆審管理報表)-企金戶未出現於覆審名單
	 * 
	 * @param brNo
	 * @return
	 */
	public List<Map<String, Object>> findDW_LNQUOTOV_LaterRetr(String brNo);

	/**
	 * 查詢額度明細表原 額度序號
	 * 
	 * @param cntrNo
	 *            額度序號
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @return
	 */
	public int findByCntrNoAndCustIdAndDupNo(String cntrNo, String custId,
			String dupNo);
	
	/**
	 * 找到客戶最壞的戶況
	 * 
	 * @param custId
	 * @param dupNo
	 * @param brNo
	 * @return
	 */
	public List<Map<String, Object>> findLNF022selByCustIdBrNo(
			String custId,String dupNo,String brNo);

	/**
	 * 查詢國外餘額日期
	 * @param allCust
	 * @param cntrNo
	 * @return
	 */
	public List<?> findDW_LNQUOTOV_loan_date(String allCust, String cntrNo);
	
	public List<Map<String, Object>> selDistinctCntrnoByCustidDupno(String custId,String dupNo);
	
}
