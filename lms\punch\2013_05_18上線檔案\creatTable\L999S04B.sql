---------------------------------------------------------
-- LMS.L999S04B 中長期契約書授信內容及條件檔
---------------------------------------------------------

---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.L999S04B;
CREATE TABLE LMS.L999S04B (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)      not null,
	ITEMTY<PERSON><PERSON>      CHAR(1)       not null,
	ITEMCONTENT   VARCHAR(768) ,
	CREATO<PERSON>       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATE<PERSON><PERSON>    TIMESTAMP    ,

	constraint P_L999S04B PRIMARY KEY(OID)
) IN  EL_DATA_4KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XL999S04B01;
CREATE UNIQUE INDEX LMS.XL999S04B01 ON LMS.L999S04B   (MAINID, ITEMTYPE);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.L999S04B IS '中長期契約書授信內容及條件檔';
COMMENT ON LMS.L999S04B (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	ITEMTYPE      IS '項目', 
	ITEMCONTENT   IS '說明', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
