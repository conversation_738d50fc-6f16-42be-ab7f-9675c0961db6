/* 
 * L120S21BDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L120S21BDao;
import com.mega.eloan.lms.model.L120S21B;

/** LGD額度EAD檔 J-110-0986_05097_B1001 於簽報書新增LGD欄位 **/
@Repository
public class L120S21BDaoImpl extends LMSJpaDao<L120S21B, String> implements
		L120S21BDao {

	@Override
	public L120S21B findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120S21B> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addOrderBy("custId_s21b");
		search.addOrderBy("dupNo_s21b");
		search.setMaxResults(Integer.MAX_VALUE);
		List<L120S21B> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L120S21B> findByIndex01(String mainId) {
		ISearch search = createSearchTemplete();
		List<L120S21B> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addOrderBy("custId_s21b");
		search.addOrderBy("dupNo_s21b");
		search.setMaxResults(Integer.MAX_VALUE);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public L120S21B findByIndex02(String mainId, String cntrNo_s21b) {
		ISearch search = createSearchTemplete();

		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (cntrNo_s21b != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo_s21b",
					cntrNo_s21b);

		return findUniqueOrNone(search);
	}

	@Override
	public List<L120S21B> findByIndex03(String mainId, String custId_s21b,
			String dupNo_s21b) {
		ISearch search = createSearchTemplete();
		List<L120S21B> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (custId_s21b != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId_s21b",
					custId_s21b);
		if (dupNo_s21b != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo_s21b",
					dupNo_s21b);
		search.addOrderBy("custId_s21b");
		search.addOrderBy("dupNo_s21b");
		search.setMaxResults(Integer.MAX_VALUE);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L120S21B> findByCustIdAndBussType(String mainId,
			String custId_s21b, String dupNo_s21b, String bussType_s21b) {
		ISearch search = createSearchTemplete();
		List<L120S21B> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (bussType_s21b != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "bussType_s21b",
					bussType_s21b);
		if (custId_s21b != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId_s21b",
					custId_s21b);
		if (dupNo_s21b != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo_s21b",
					dupNo_s21b);

		search.addOrderBy("custId_s21b");
		search.addOrderBy("dupNo_s21b");
		search.setMaxResults(Integer.MAX_VALUE);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L120S21B> findByMainIdOrderByCustIdBussType(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addOrderBy("custId_s21b");
		search.addOrderBy("dupNo_s21b");
		search.addOrderBy("bussType_s21b");
		search.setMaxResults(Integer.MAX_VALUE);
		List<L120S21B> list = createQuery(search).getResultList();
		return list;
	}

}