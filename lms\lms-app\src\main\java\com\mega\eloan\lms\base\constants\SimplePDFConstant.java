/* 
 * SimplePDFConstant.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.constants;

/**
 * <pre>
 * 一些報表列印使用的變數名稱，將其列為常數。
 * </pre>
 * 
 * @since 2011/6/22
 * <AUTHOR> Wang
 * @version <ul>
 *          <li>2011/6/22,Sunkist,new
 *          </ul>
 */
public interface SimplePDFConstant {

    /**
     * <pre>
     * PrintTypeEnum enum.
     * </pre>
     */
    public enum PrintTypeEnum {
        PLURAL("p"), SINGULAR("s");

        private String code;

        PrintTypeEnum(String code) {
            this.code = code;
        }

        public String getCode() {
            return code;
        }
    }

    /**
     * js傳入的一些列印時要用資料。
     */
    public static final String PRINT_DATA = "printData";

    /**
     * 整本書中插入附件的位置，ex: ['sap110r01', 'ex', 'sap110r02']。
     */
    public static final String NOT_IN_ATTACH = "ex";

    /**
     * 附加附件的頁數。
     */
    public static final String EXTRA_INFO_COUNT = "extraInfoCount";

    /**
     * 總頁數。
     */
    public static final String TOTAL_BOOK_PAGE = "totalBookPage";

    /**
     * 各章節頁數。
     */
    public static final String CHAPTER_TOTAL_PAGE = "chapterTotalPage";

    public static final String INDEX = "index";

    /**
     * 代入要合併的那些章節，變數型態為String[]，ex: ['sap110r01', 'sap110r02']。
     */
    public static final String CHAPTER_ARRAY = "chapterAry";

    /**
     * 目錄的頁數。
     */
    public static final String DIRECTORY_PAGE_COUNT = "directoryPageCount";

}
