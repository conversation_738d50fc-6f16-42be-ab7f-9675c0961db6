package com.mega.eloan.lms.base.service;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import org.kordamp.json.JSONObject;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.gwclient.Brmp002O;
import com.mega.eloan.common.gwclient.Brmp002O.Brmp002O_result_quotationResult_calProcessObj;
import com.mega.eloan.common.model.ElAml;
import com.mega.eloan.common.model.ElAmlItem;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.lms.model.*;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.service.ICapService;

public interface CLSService extends ICapService {
	
	@SuppressWarnings("rawtypes")
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search);
	public ISearch getMetaSearch();
	
	public L120M01A findL120M01A_oid(String oid);
	public L120M01A findL120M01A_mainId(String mainId);
	public L120M01A findL120M01A_by_L140M01A_mainId(String l140m01a_mainId);
	public L120M01C findL120M01C_refMainId(String refMainId);
	public L120M01D findL120M01D_mainId_itemType(String mainId, String itemType);
	public List<L120M01E> findL120M01E_mainId(String mainId);
	public List<L120M01F> findL120M01F_mainId(String mainId);
	public L120M01I findL120M01I_mainId(String mainId);
	
	/**
	 * 目前在 編製中/待補件 的案件才產生
	 * @param l120m01a
	 * @return
	 */
	public L120M01I syncL120M01I_clsMinor(L120M01A l120m01a);
	public L120M01I syncL120M01I_bailout_flag(L120M01A l120m01a);
	
	public List<L130S02A> findL130S02A_mainIdSeqNo(String mainId, String seqNo);
	public L130S02A findL130S02A_uk(String mainId, String seqNo, String ctlType, String ctlItem);
	
	public List<L120M01A> findC121M01A_UsedInCaseDoc(String ratingId);
	public L140M01A findL140M01A_oid(String oid);
	public L140M01A findL140M01A_mainId(String mainId);
	public List<L140M01A> findL140M01A_l120m01aMainId(String l120m01aMainId);
	public List<L140M01A> findL140M01A_l120m01aMainId_itemType(String l120m01aMainId, String itemType);
	public List<L140M01A> findL140M01A_cntrNo(String cntrNo);
	
	/** 依 printSeq 排序
	 * @param caseMainId
	 * @param itemType 可以傳 null
	 * @param docStatus 可以傳 null
	 * @return
	 */
	public List<L140M01A> findL140M01A_byL120m01cMainIdForPrint(String caseMainId, String itemType, String docStatus);
		
	public L140M01B findL140M01B(String mainId, String itemType);
	public L140M01C findL140M01C_oid(String oid);	
	public List<L140M01C> findL140M01C(L140M01A l140m01a);
	
	
	
	//J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
	//public List<L140M01I> findL140M01I(L140M01A l140m01a);
	public List<L140M01I> findL140M01IWithRType(L140M01A l140m01a,String rType);
	
	public List<L140M01J> findL140M01J(L140M01A l140m01a);
	public L140M01M findL140M01M(String mainId);
	public List<L140M01O> findL140M01O(String mainId);
	
	//feeSrc=3 是備份資料，不應抓出
	@Deprecated
	public List<L140M01R> findL140M01R(String mainId);
	
	public List<L140M01R> findL140M01R_exclude_feeSrc3(String mainId);
	
	public L140M01T findL140M01T_oid(String oid);
	public L140M01T findL140M01T(String mainId, String flag, String estateType, String estateSubType);
	public Map<String, List<L140M01T>> findL140M01T_group_by_flag(String mainId);
	public List<L140M01T> sync_L140M01T_flag_Y(String mainId, String l140m01a_IsInstalment, List<L140S02A> l140s02a_list);
	public Map<String, String> queryOnLine772FlagByCntrnoSecond(L140M01A l140m01a) throws CapException;
	
	public L140M01Y findL140M01Y_oid(String oid);
	public L140M01Y findL140M01Y_uk(String mainId, String refType, String refValue, String refModel, String refMainId, String refOid);
	public L140M01Y findL140M01Y_refTypeELF459Srcflag1_1stItem(String l140m01a_mainId);
	public List<L140M01Y> findL140M01YOrderDefault(String mainId);
	public List<L140M01Y> findL140M01YOrderDefault(String mainId, String refType);
	public Map<String, List<L140M01Y>> findL140M01Y_group_by_refType(String mainId);
	
	public List<L140S01A> findL140S01A(L140M01A l140m01a);
	public L140M03A findL140M03A(L140M01A l140m01a);
	
	public List<L140M04A> findL140M04A(String l120m01a_mainId);
	public void delL140M04A(List<L140M04A> model_list);
	
	public List<L140MC1A> findL140MC1A(String mainId);
	public L140MC1A findL140MC1A(String mainId, String itemCode);
	
	public List<L140S02A> findL140S02A(L140M01A l140m01a);
	public L140S02A findL140S02A(String mainId, Integer seq);
	public L140S02A findL140S02A_by_C160S01C(C160S01C c160s01c);
	public List<L140S02B> findL140S02B(String mainId, Integer seq);
	public L140S02C findL140S02C(String mainId, Integer seq);
	public L140S02D findL140S02D_oid(String oid);
	public L140S02D findL140S02D_byMainIdSeqPhase(String mainId, Integer seq, Integer phase);
	public List<L140S02D> findL140S02D_orderByPhase(String mainId, Integer seq);
	public List<L140S02D> findL140S02D_orderByPhase(String mainId, Integer seq, String isUseBox);
	public L140S02E findL140S02E(String mainId, Integer seq);
	public List<L140S02E> findL140S02E(String mainId);
	public L140S02F findL140S02F(String mainId, Integer seq);
	public L140S02G findL140S02G(String mainId, Integer seq);
	public List<L140S02H> findL140S02H(String mainId, Integer seq);
	public List<L140S02N> findL140S02N(String mainId, Integer seq);
	public L140S02N findL140S02N_uk(String mainId, Integer seq, String itemType);
	public Map<String, L140S02N> findL140S02N_itemType_model(String mainId, Integer seq);
	//-------------
	public C121M01A findC121M01A_oid(String oid);
	public C121M01A findC121M01AByMainId(String mainId);
	public C121M01A findC121M01A(C120M01A c120m01a);
	
	public Set<String> findC121M01AMainIds(String l120m01a_mainId);
	/**
	 * 查出 被 copy 進簽報書的 ratingDoc(其 docStatus=ZZZ) 的 randomCode
	 */
	public Map<String, String> findC121M01A_mapKV_ratingId_randomCode(L120M01A l120m01a);
	/**
	 * 查出 被 copy 進簽報書的 ratingDoc(其 docStatus=ZZZ)
	 */
	public C121M01A findC121M01A_caseId_ratingId(String caseId, String ratingId);	
	
	public C121S01A findC121S01A(C121M01A meta);

	//日本
	public C121M01B findC121M01B_oid(String oid);
	public C121M01B findC121M01B_byC120M01A(C120M01A c120m01a);
	public C121M01F findC121M01F_byC120M01A(C120M01A c120m01a);
	public Map<String, C121M01B> findIdDup_C121M01B(String mainId, Set<String> idDup11Set); //減少query次數
	//澳洲
	public C121M01C findC121M01C_oid(String oid);
	public C121M01C findC121M01C_byC120M01A(C120M01A c120m01a);
	public C121M01G findC121M01G_byC120M01A(C120M01A c120m01a);
	public Map<String, C121M01C> findIdDup_C121M01C(String mainId, Set<String> idDup11Set); //減少query次數
	//泰國
	public C121M01D findC121M01D_oid(String oid);
	public C121M01D findC121M01D_byC120M01A(C120M01A c120m01a);
	public Map<String, C121M01D> findIdDup_C121M01D(String mainId, Set<String> idDup11Set); //減少query次數
	public Map<String, C121M01H> findIdDup_C121M01H(String mainId, Set<String> idDup11Set); //減少query次數
	public C121M01H findC121M01H_byC120M01A(C120M01A c120m01a);
	public List<C121M01E> findC121M01E_mainId(String mainId);
	
	public C123M01A findC123M01A_oid(String oid);
	public List<C124M01A> findC124M01A_byCustIdAndDupNo(String custId, String dupNo, Integer verNo);
	public List<C124M01A> findC124M01A_byCustIdAndDupNo(String custId, String dupNo, Integer verNo, String dataStatus);
	//-------------
	public C101M01A findC101M01A_mainId(String mainId);
	public C101M01A findC101M01A_brIdDup(String ownBrId, String custId, String dupNo);
	public C101S01A findC101S01A(C101M01A c101m01a);
	public C101S01B findC101S01B(C101M01A c101m01a);
	public C101S01C findC101S01C(C101M01A c101m01a);
	public C101S01D findC101S01D(C101M01A c101m01a);
	public C101S01E findC101S01E(C101M01A c101m01a);
	public List<C101S01E> findC101S01E_byIdDup(String custId, String dupNo);
	public C101S01E findC101S01E_idDup_latestOne(String custId, String dupNo);
	public C101S01J findC101S01J(C101M01A c101m01a);
	public C101S01J findC101S01J(String mainId, String custId, String dupNo);
	public C101S01G findC101S01G(C101M01A c101m01a);
	public List<C101S01H> findC101S01H(C101M01A c101m01a);
	public List<C101S01I> findC101S01I(C101M01A c101m01a);
	public C101S01Q findC101S01Q(C101M01A c101m01a);
	public C101S01R findC101S01R(C101M01A c101m01a);
	public List<C101S01S> findC101S01S_byIdDupDataType(String mainId, String custId, String dupNo, String dataType);
	public List<C101S01U> findC101S01U(String mainId, String custId, String dupNo);
	public List<C101S01U> findC101S01U_txid(String mainId, String custId, String dupNo, String txid);
	public List<C101S01U> findC101S01U_txid_sendTimeBefore(String mainId, String[] txid_arr, String sendTimeCmp);
	public void delC101S01U(List<C101S01U> model_list);
	public List<C101S01W> findC101S01W(String mainId, String custId, String dupNo);
	
	public C101S01X findC101S01X(String mainId, String custId, String dupNo);
	public List<C101S04W> findC101S04W_by_mainId_custId(String mainId, String custId);
	//-------------
	public C102M01A findC102M01A_oid(String oid);
	/**
	 * 從 notes e-loan 轉入的舊案, 有可能1個 mainId 找到2筆C102M01A
	 * @param mainId
	 * @return
	 */
	public List<C102M01A> findC102M01A_mainId(String mainId);
	
	
	public List<C120M01A> findC120M01A_mainId_orderBy_keymanCustposCustid(String mainId);
	public List<C120M01A> findC120M01A_ByC121M01A_orderBy_keymanCustposCustid(C121M01A meta);
	public List<C120M01A> findC120M01A_ByC121M01A_custPos_orderBy_keymanCustposCustid(C121M01A meta, String custPos);
	public List<C120M01A> findC120M01A_caseId(String caseId);
	public List<C120M01A> findC120M01A_caseId_ratingKind1(String caseId);
	public List<C120M01A> findC120M01A_caseId_keyMan_distinctIdDup(String caseId);
	public List<C120M01A> findC120M01A_caseId_keyMan_distinctIdDup_ratingKind1(String caseId);
	public List<C120M01A> findC120M01A_caseId_keyManIdDup(String caseId, String keyman_custId, String keyman_dupNo);
	public List<C120M01A> findC120M01A_caseId_keyManIdDup_ratingKind1(String caseId, String keyman_custId, String keyman_dupNo);
	public C120M01A findC120M01A_mainId_idDup(String mainId, String custId, String dupNo);
	public C120M01A findC120M01A_o_single_Y(String ownBrId, String custId, String dupNo);
	public C120M01A findExistKeyMan(L120M01A l120m01a);
	public C120M01A findExistKeyMan(String mainId);	
	public C120M01A findC120M01A_oid(String oid);
	public Map<String, C120M01A> findIdDup_C120M01A(String mainId, Set<String> idDup11Set); //減少query次數
	public Map<String, C120M01A> findIdDup_C120M01A(List<C120M01A> model_list); //減少query次數
	
	public C120S01A findC120S01A_oid(String oid);
	public List<C120S01A> findC120S01A_mainId_busCodeEmpty(String mainId);
	public C120S01A findC120S01A_idDup_latestIdv(String custId, String dupNo);
	public C120S01A findC120S01A(C120M01A c120m01a);
	public C120S01A findC120S01A(String mainId, String custId, String dupNo);
	/**
	 * @param mainId
	 * @param idDup11Set
	 * @return {k=idDup, v=null或C120S01A} 要判斷是否為 null
	 */
	public Map<String, C120S01A> findIdDup_C120S01A(String mainId, Set<String> idDup11Set); //減少query次數
	
	public C120S01B findC120S01B(C120M01A c120m01a);
	public C120S01B findC120S01B(String mainId, String custId, String dupNo);
	public Map<String, C120S01B> findIdDup_C120S01B(String mainId, Set<String> idDup11Set); //減少query次數
	
	public C120S01C findC120S01C(C120M01A c120m01a);
	public C120S01C findC120S01C(String mainId, String custId, String dupNo);
	public Map<String, C120S01C> findIdDup_C120S01C(String mainId, Set<String> idDup11Set); //減少query次數
	
	public C120S01D findC120S01D(C120M01A c120m01a);
	public C120S01D findC120S01D(String mainId, String custId, String dupNo);
	public Map<String, C120S01D> findIdDup_C120S01D(String mainId, Set<String> idDup11Set); //減少query次數
	
	public C120S01E findC120S01E(C120M01A c120m01a);
	public C120S01E findC120S01E(String mainId, String custId, String dupNo);
	public Map<String, C120S01E> findIdDup_C120S01E(String mainId, Set<String> idDup11Set); //減少query次數
	public List<C120S01E> findC120S01E_mainId_list(List<String> mainId_list);
	
	public C120S01G findC120S01G(String mainId, String custId, String dupNo);
	public Map<String, C120S01G> findIdDup_C120S01G(String mainId, Set<String> idDup11Set); //減少query次數
	
	public List<C120S01H> findC120S01H(String mainId, String custId, String dupNo);
	public void delC120S01H(String mainId, String custId, String dupNo);
	
	public List<C120S01I> findC120S01I(String mainId, String custId, String dupNo);
	public void delC120S01I(String mainId, String custId, String dupNo);
	
	public C120S01J findC120S01J(C120M01A c120m01a);
	public C120S01J findC120S01J(String mainId, String custId, String dupNo);
	public List<C120S01N> findC120S01N(String mainId, String custId, String dupNo);
	public List<C120S01P> findC120S01P(String mainId, String custId, String dupNo);
	public C120S01Q findC120S01Q(String mainId, String custId, String dupNo);
	public Map<String, C120S01Q> findIdDup_C120S01Q(String mainId, Set<String> idDup11Set); //減少query次數
	public C120S01R findC120S01R(String mainId, String custId, String dupNo);
	public Map<String, C120S01R> findIdDup_C120S01R(String mainId, Set<String> idDup11Set); //減少query次數
	public List<C120S01S> findC120S01S(String mainId, String custId, String dupNo);
	List<C120S01W> findC120S01W(String mainId, String custId, String dupNo);
	public List<C120S01S> findC120S01S_byIdDupDataType(String mainId, String custId, String dupNo, String dataType);
	public List<C120S01T> findC120S01T(String mainId, String custId, String dupNo);
	public List<C120S01U> findC120S01U(String mainId, String custId, String dupNo);
	public List<C120S01U> findC120S01U_txid(String mainId, String custId, String dupNo, String txid);
	
	public C120S01X findC120S01X(String mainId, String custId, String dupNo);
	public C120S01Z findC120S01Z(String mainId, String custId, String dupNo);
	//信用風險管理遵循主檔
	public L120S01M findL120S01M(C120S01E c120s01e);
	public Map<String, L120S01M> findIdDup_L120S01M(String mainId, Set<String> idDup11Set); //減少query次數
	
	public List<L120S03A> findL120S03A(String mainId);
	public L120S03A findL120S03A(String mainId, String cntrMainId, String cntrNo);
	public L120S03A findL120S03A_in_previouse_caseDoc(L140M01A l140m01a);
	public void setL120s03a_data(L120S03A l120s03a, String caseMainId, L140M01A l140m01a, double twdApplyAmt_unit_1000);
	public L120S03A startSetL120s03a(L120S03A l120s03a, L140M01A l140m01a, String crdFlag, double applyAmt);
	
	public List<L120S09A> findL120S09A(String mainId);
	public L120S09A findL120S09A_cls1131(L120S09B l120s09b);
	public L120S09B findL120S09B_oid(String oid);
	public L120S09B findL120S09B_mainId_latestOne(String mainId);
	
	/**
	在個金徵信送掃時, 會有 cls1131_mainId, refNo
	引入 個金授信簽報書, 會另產生 cls1141_mainId, 並 copy 個金徵信相關table (例如：C101S01J to C120S01J), 但並未 copy L120S09B 
	所以, 之前在個金簽報書, 要 print 個金徵信的 AML 查詢結果, 會用 【C120S01J.amlRefNo + createTime】 去串到最新的  L120S09B(無法用 C120S01J.mainId 去串)
	但因 P-108-0046 會在 refNo 後加上 uniqueKey的末6碼 
	*/
	@Deprecated
	public L120S09B findL120S09B_refNo_latestOne(String refNo);
	
	/** 在 P-108-0046 之前，只用 refNo；在 P-108-0046 之後，可能要用 oid 去找 => 為了同時處理(舊/新) */
	public L120S09B findL120S09B_refNo_or_oid(String amlRefNo, String amlRefOid);
	
	public List<L120S12A> findL120S12A(String mainId);
	public void delL120S12A(String mainId, List<L120S12A> model_list);
	
	public List<L120S18A> findL120S18A_byMainId_orderBySeq(String mainId);
	public List<L120S18A> findL120S18A_byMainId_custId_dupNo_orderBySeq(String mainId, String custId, String dupNo);
	public List<L120S18A> findL120S18A_tot_byMainId_orderBySeq(String mainId);
	public List<L120S19A> findL120S19A_byMainId(String mainId, String itemType);
	public L120S19A findL120S19A_byMainId_itemType_latest_itemVersion(String mainId, String itemType);
	public List<L120S19A> findByMainIdItemTypeOrderBy(String mainId,
			String itemType,boolean isDesc);
//	public L120S19A findL120S19A_byMainId_latestInput(String mainId);
//	public L120S19A findL120S19A_byMainId_latestOutput(String mainId);
	public boolean is_L120M01A_contains_brmpJsonData(L120M01A l120m01a);
	public void delL120S18A(String mainId, List<L120S18A> model_list);
	
	public List<L120S13A> findL120S13A(String mainId);
	public void delL120S13A(String mainId, List<L120S13A> model_list);
	
	public List<L120S15A> findL120S15A(String mainId);
	public void delL120S15A(String mainId, List<L120S15A> model_list);
	
	public List<L180R19H> findL180R19H_orderByDataDate(String docType, String cntrNo);
	public List<L800M01A> findL800M01A_brNo(); 
	
	
	public C310M01A findC310M01A_oid(String oid);
	public List<C310M01E> findC310M01E(String mainId);
	
//	public C320M01A findC320M01A_oid(String oid);
//	public C320M01B findC320M01B_oid(String oid);
//	public List<C320M01B> findC320M01BWithOrder(String mainId);
//	public List<C320M01E> findC320M01E(String mainId);
	
	public C340M01A findC340M01A_oid(String oid);
	public C340M01A findC340M01A_mainId(String mainId);
	public List<C340M01A> findC340M01A_ploanCtrNo(String ploanCtrNo);
	public List<C340M01A> findC340M01A_ploanCtrNo_OrderByCreateTimeAsc(String ploanCtrNo);
	public List<C340M01A> findC340M01A_custId(String custId,String ctrType);
	public List<C340M01B> findC340M01B(String mainId);
	public C340M01C findC340M01C(String mainId, String itemType);
	public List<C340M01C> findC340M01C(String mainId);
	
	public C360M01A findC360M01A_oid(String oid);
	public C360M01A findC360M01A_mainId(String mainId);
	
	public C900M01A findC900M01A_prodKind(String prodKind);
	public C900M01B findC900M01B_subjCode(String prodKind, String subjCode);
	public C900M01D findC900M01D_subjCode(String subjCode);
	public C900M01D findC900M01D_loanTP3(String loanTP3);
	public C900M01G findC900M01G_cntrNo_grpCntrNo(String cntrNo, String grpCntrNo);
	public C900M01G findC900M01G_cntrNo(String cntrNo);
	public List<C900M01G> findC900M01G_search(ISearch search);
	
	public C900M01J findC900M01J_oid(String oid);
	public C900M01J findC900M01J_mainId(String mainId);
	public C900M01J findActiveMajorC900M01JById(String custId);
	public String get_C900M01J_output_memo(C900M01J c900m01j);
	
	public C900M03A findC900M03A_fnGenDateGenTime(String fn, String genDate, String genTime);
	public C900M03A findC900M03A_fn_maxGenDate(String fn);
	public List<C900M03A> findC900M03A_fn_bfGenDate(String fn, String genDate);
	
	public C900S01B findC900S01B_itemCode(String itemCode);
	public C900S02D findC900S02D_oid(String oid);
	
	public C900S02E findC900S02E_mainId(String mainId);
	public List<C900S02F> findC900S02F_mainId_order(String mainId);
	
	public C900S03A findC900S03A_byData(String fn, String genDate, String genTime, String custId, String dupNo);
	public C900S03B findC900S03B_byData(String fn, String genDate, String genTime, String custId, String dupNo);
	public Map<String, Set<String>> findC900S03E_idDup_brNos(String cyc_mn);
	
	public C999M01A findC999M01A_oid(String oid);
	
	/** 乙方 */
	public List<C999M01B> findC999M01B_type1(C999M01A meta);
	
	/** 甲方 */
	public List<C999M01B> findC999M01B_type2(C999M01A meta);
	
	public C999S01B findC999S01B(C999M01A meta, String type);
	
	public void save(List<GenericBean> list);
	public void save(GenericBean... entity);
	public void daoSave(List<GenericBean> list);
	public void daoSave(GenericBean... entity);
	
	public void daoDelete(GenericBean... entity);
	
	public boolean verifyCurrAmt(String caseBrId, C120S01B c120s01b, C120S01C c120s01c, C120S01D c120s01d);
	
	public void clspage_srvPage_html(Map<String, Object> m, String formAttrTxFlag
			, C120M01A c120m01a
			, C120S01A c120s01a , C120S01B c120s01b , C120S01C c120s01c
			, C120S01D c120s01d, C120S01E c120s01e);
	
	public void clspage_html_srvPage(PageParameters params, C120M01A c120m01a
			, C120S01A c120s01a , C120S01B c120s01b , C120S01C c120s01c
			, C120S01D c120s01d, C120S01E c120s01e);
	
	public List<C120M01A> filter_shouldRating(List<C120M01A> list);	
	public List<C120M01A> filter_noneRating(List<C120M01A> list);
	
	public void delC120Relate(C120M01A c120m01a);
	public void delC120Relate(List<C120M01A> c120m01a_list);
	public void delC121Relate(C121M01A c121m01a);
	public C120M01A copyC120Relate(C120M01A src_c120m01a, String newMainId, Map<String, Object> colMap)throws CapException;
	
	/**
	 * 回傳的 key : [attchFileOid|attchFileOid2]-mainId-ownBrId-custId-dupNo-fieldId-oldBDocFileOid <br/>
	 * 回傳的 val : newBDocFileOid
	 * @param c120m01a
	 * @return
	 * @throws CapException
	 */
	public Map<String, String> copyC120AttchDocFile(String idxStr, C120M01A c120m01a)throws CapException;
	public void fixFildOid(String idxStr, Map<String, String> chg);
	public void removeC120M01A_attachFile(C120M01A exist_c120m01a);
	
	public C121M01A copyC121Relate(C121M01A src_c121m01a, String newMainId, Map<String, Object> colMap)throws CapException;
	
	public void syncRatingCustWithC120M01A(L120M01A l120m01a, boolean replaceEqIdDup, List<String> errList) throws CapException;
	public void mergeMultipliRatingCust(L120M01A l120m01a, List<C120M01A> c120m01a_src_list, List<String> errList);
	/**
	 * 原本， 2015東京XXX號的甲是4等。 且已引入 L140M01C
	 * <br>之後若退回重編，2015東京XXX號的甲變成3等
	 * <br>於 簽報書 重新引入 2015東京XXX號，會將舊 mainId 刪掉，產生新 mainId 
	 * <br>雖然 c121MainId不同，但還可用 [RatingId,CustId, DupNo]去比對，更新l140m01c的grade
	 */
	public void syncRatingGradeToL140M01C(L120M01A l120m01a);
	
	/**
	 * Meta 暫時僅限 C121M01A, L120M01A
	 */
	public void syncCustPosM(Meta meta, List<C120M01A> c120m01a_list, String c120m01a_oid);
	public void syncCustPosM(C121M01A meta, String c120m01a_oid);
	
	public boolean custPosHasRating(C120M01A c120m01a);
	
	public void clearL140M01C_ratingByOidList(List<String> l140m01c_oid_list);
	public void clearL140M01C_rating(List<L140M01C> l140m01c_list);
	
	public String convertRatingDF(String rating);
	public String returnRatingDoc_Approved_to_Editing(C121M01A c121m01a);
	public void logicalDeleteBDocFile(String docOid);
	
	public String checkOverSeaJcicNegativeInfo(Properties propRatingDocPage, L120M01A l120m01a, List<L140M01A> l140m01a_list) throws CapException ;
	public HashMap<String, String> jp_getChkItemRangeByCustId(List<L140M01A> listL140m01a);
	public String checkOverSeaSameL1CesLms(L120M01A l120m01a);
	public List<String> checkOverSea_c120s01e_qdate(String mainId, Set<String> idDup11Set
			, String l120m01a_RatingFlag, String p1);
	
	public void forceActive_L120M01A_ratingFlag(L120M01A l120m01a);
	public boolean allow_repayFund(L120M01A l120m01a, List<L140S02A> l140s02as);
	
	public BigDecimal proc_exRate(String inputCurr, String targetCurr, int scale, List<Map<String, Object>> dw_fxrth_list);
	public Map<String, Object> get0024_mapData(String custId, String dupNo);
	public String get0024_custName(Map<String, Object> map);
	public String get0024_custName(String custId, String dupNo);
	public String get0024_busCode(String custId, String dupNo);
	public String get0024_busCode(String l120m01a_mainId, String custId, String dupNo);
	public void fixImpRatingDoc(String ratingId);

	public C122M01A findC122M01A_oid(String oid);
	public C122M01A findC122M01A_mainId(String mainId);
	public C122M01A findC122M01A_ploanCaseNo_ploanCaseId(String ploanCaseNo, String ploanCaseId);
	public C122M01A findLatestC122M01A_for_C160S01D(String ownBrId, String custId, String[] applyKindArr, Date sinceDate, String exclude_statFlag);
	public List<C122M01A> findC122M01A_ploanCaseId(String ploanCaseId);	
	public C122M01A findC122M01A_fromL140M01Y_refTypeDocCode1ELF459Srcflag1_refModelC122M01A(L140M01Y l140m01y);
	public C122M01A findLatestC122M01A_by_brNo_custId_applyKind_sinceDate(String ownBrId, String custId, String applyKind, Date sinceDate);
	public C122M01A findLatestC122M01A_by_brNo_custId_applyKind_sinceDate(String ownBrId, String custId, String[] applyKindArr, Date sinceDate);
	public C122M01B findC122M01B_byMainIdItemType(String mainId, String itemType);
	public List<C122S01B> findC122S01B_mainIdBatchNo(String mainId, Integer batchNo);

	public C122M01E findC122M01E_refMainId(String refMainId);
	public C122M01E findC122M01E_by_C122M01A_forChinaSteelCorpHQ(C122M01A c122m01a);
	public Set<String> joinVarVer(C121M01A c121m01a);

	public C160M01A findC160M01A_mainId(String mainId);
	public List<C160M01B> findC160M01B_mainId(String mainId);
	public List<C160M01C> findC160M01C_mainId(String mainId);
	public C160M01E findC160M01E(String mainId, String staffNo, String staffJob);
	public List<C160S01B> findC160S01B(C160M01B c160m01b);
	/**
	 * @param rType 從債務人身分別
	 * @param c160m01b
	 * @return
	 */
	public List<C160S01B> findC160S01B(String rType, C160M01B c160m01b);
	public List<C160S01B> findC160S01B(String mainId, String refMainId);
	public C160S01C findC160S01C_mainId_refMainid_seq(String mainId, String refMainId, Integer seq);
	public List<C160S01C> findC160S01C_mainId_refMainid(String mainId, String refMainId);
	public C160S01D findC160S01D_oid(String oid);
	public List<C160S01D> findC160S01D_mainId(String mainId);
	public List<C160S01F> findC160S01F_byMainIdSeqRefMainid(String mainId, Integer seq, String refmainId);
	public Map<String, String> get_disasType_desc();

	public Map<String, String> get_codeTypeWithOrder(String codeType);
	public Map<String, String> get_codeTypeWithOrder(String codeType, String locale);
	public Map<String, String> get_codeType_codeDesc2WithOrder(String codeType, String codeDesc2,String locale);
		
	public void validate_adjustReason(Map<String, String> inputMap, 
			Map<String, String> errorMap, Map<String, String> confirmMap); 

	/**
	 * @param inputMap
	 * @param errorMap
	 * @param confirmMap
	 * @param mowType
	 * @param func 例如：LMSUtil.MOWTYPE_M_CHK01
	 */
	public void validate_adjustReason(Map<String, String> inputMap, 
			Map<String, String> errorMap, Map<String, String> confirmMap, String mowType, String func);
	/**
	 * 簽案時是否為利害關係人
	 */
	public boolean cls_is_relate(L120M01A l120m01a, L140M01A l140m01a);
	
	/**
	 * 【1】非團貸案件
	 * 【2】[主/共]為「利害關係人」 且
	 * 【3】科目為「消費者放款科目」外的「短放、中放、長放」等「無擔保科目」
	 * 才需輸入例外的原因
	 */	
	public boolean cls_need_l140m01a_unsecureFlag(L120M01A l120m01a, L140M01A l140m01a);
	
	public C900M01H findActiveMajorC900M01HByCertNo(String year, String word, String no);
	
	public String check0024_23_LUV_DEPT_2(String custId, String dupNo) throws CapException;

	/**
	 * @param l120m01a
	 * @param stepValue 可參考 ClsUtility.get_check_stepValue___calc_L140M01A_totValue()
	 * @return
	 */
	public String checkSend_L120M01A(L120M01A l120m01a, String stepValue);
	public String checkSend_L120M01A_Boss(L120M01A l120m01a,Map<String, String> BossMap);
	public String checkSend_L120M01A(L120M01A l120m01a, String stepValue, boolean addToAML);
	public String checkSend_C160M01A(C160M01A c160m01a);
	
	/**
	 * @param custId
	 * @param dupNo
	 * @return 	國籍碼
	 */
	public String get0024_ntCode(String custId, String dupNo);

	public void inject_CM1_AML_STATUS(L120S09A l120s09a);
	public void inject_luvRiskLevel(String brNo, L120S09A l120s09a);
	public List<String> cfmMsg_only_EL01(L120M01A l120m01a);	
	public List<String> cfmMsg_both_EL01_EL02(L120M01A l120m01a);
	public List<String> cfmMsg_only_EL01(C160M01A c160m01a);
	public List<String> cfmMsg_both_EL01_EL02(C160M01A c160m01a);
	
	//為了讓 RPA 能不被中斷，此提示不出現給「經辦」看，只在「主管覆核」時出現
	public List<String> labor_abnormalMsg(L120M01A l120m01a);
	
	/**
	 * 把 cntrNo 的主/從債務人，加入 L120S09A(custRelation 依 從債務人 身分決定)
	 * @param l120m01a
	 * @param l140m01as
	 */
	public void cls_importBlackListInner(L120M01A l120m01a, List<L140M01A> l140m01as);
	/**
	 * 把 cntrNo 的主/從債務人，加入 L120S09A(custRelation 依 從債務人 身分決定)
	 * @param c160m01a
	 */
	public void cls_importBlackListInner(C160M01A c160m01a);
	
	/**
	 * 同一個客戶，可能[在A額度當 共借人;在B額度當 連保人] => custRelation 同時具有N個身分<br/>
	 * 若 custEName 傳入空白, 之後要再 call 此類別下的 cls_syncL120S09A_c120eName(...) 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @param custName
	 * @param relation
	 * @param custEName
	 */
	public void cls_reSetL120S09A(String mainId, String custId, String dupNo,
			String custName, String relation, String custEName, String country);
	
	public void cls_syncL120S09A_c120eName(L120M01A l120m01a, List<L120S09A> l120s09a_list);
		
	public void cls_importRelatedEconomic(String l120m01a_mainId);
	
	public void query_AML(String unitNo, L120S09A l120s09a) throws CapException;
	public String get_importName(String importId);
	
	public C900S02B get_C900S02B_NextSeq(String brNo, String clientIP, String empNo, String sysType, String sysFunc) throws CapException;
	public String get_C900S02B_ref_cat_data(String amlRefNo, int type);
	public void inject_AMLRefNo(C160M01A c160m01a, String clientIP, String empNo) throws CapException;
	public void inject_AMLRefNo(C101M01A c101m01a, C101S01J c101s01j, String clientIP, String empNo) throws CapException;
	public boolean active_SAS_AML(String amlUnitNo);
	public boolean active_SAS_AML(C101M01A c101m01a);
	public boolean active_SAS_AML(L120M01A l120m01a);
	public boolean active_SAS_AML(C160M01A c160m01a);	
	public boolean is_aml_lockEdit_cls1131(L120S09B l120s09b);
	public boolean is_aml_lockEdit(String mainId);
	public boolean is_aml_case_finish(String l120s09b_ncResult);
	public boolean is_aml_case_finish(L120M01A l120m01a);
	public boolean is_aml_case_finish(C160M01A c160m01a);
	public String[] aml_split_checkResult_R_blackListCode_hitFlagYN(String split_checkResult);
	public boolean aml_isOverSea(String caseBrId, String unitNo);
	//=== send AML
	public ElAml aml_generateElAml(L120S09B l120s09b);
	//=== receive AML
	public void sync_ElAmlItem_to_L120S09A(boolean isOverSea, L120S09B l120s09b
			, ElAmlItem elAmlItem, L120S09A l120s09a);
	
//	public boolean is_function_on(String paramStr);
	
	/**
	 * select * from com.bcodetype where codetype='LMS_FUNC_ON_FLAG' and locale='zh_TW'	  
	 * @param paramStr 功能於「指定日」生效
	 * @return
	 */
	public boolean is_function_on_codetype(String paramStr);
	
	public List<C123M01A> findLastRecord(String ownBrId, String custId, String dupNo,String oid,String docStatus);
	
	public int count_C900M03A_dtl(String fn_D, String genDate, String genTime);
	public int count_C900S02E_cyc_mn(String cyc_mn);
	public int count_C900S02E_cyc_mn_no_chk_result(String cyc_mn);
	
	public int setDeletedtimeByCYC_MN_no_chk_result(String cyc_mn);
	public void delC123M01A(C123M01A c123m01a);
	public void delC310M01A(C310M01A meta);
//	public void delC320Relate(C320M01A meta);
	public void delOldC900S02B();
	
	/**
	 * 
	 * 上傳DW在借款人刪除時
	 * 
	 * @param l120m01a
	 *            簽報書主檔
	 * 
	 * @param c120m01a_oids
	 *            借款人oid 陣列 當為null 為刪除整筆簽報書
	 * @param reason
	 *            刪除原因
	 */
	public void upDwBydeleCust(L120M01A l120m01a, List<String> c120m01a_oids,
			String reason, String reasonOth);
	
	public boolean doCardLoanBr(String brNo);
//	public Set<String> getCardLoanBrNo();	
	
	void verify_GradeModel_upDW(C120M01A c120m01a, C120S01A c120s01a,
			C120S01B c120s01b, C120S01C c120s01c, C120S01G c120s01g,
			C120S01Q c120s01q, C120S01R c120s01r, C120S01E c120s01e);
	
	void verify_GradeModel_U_upDW(C120M01A c120m01a, C120S01A c120s01a,
			C120S01B c120s01b, C120S01C c120s01c, C101S01G_N c101s01g_n, 
			C101S01Q_N c101s01q_n, C101S01R_N c101s01r_n, C120S01E c120s01e);

	void verify_C121M01A(C121M01A c121m01a, C121S01A c121s01a,
			C120M01A c120m01a, C120S01A c120s01a, C120S01B c120s01b,
			C120S01C c120s01c, C120S01E c120s01e, GenericBean c121m01_grade, L140M01C l140m01c);
	
	/**
	 * 房貸評分卡上傳DW
	 * 
	 * @param l120m01a
	 *            簽報書主檔
	 * @return 筆數
	 */
	public int L120UploadDW(L120M01A l120m01a);
	public int L120UploadDWForOBU(L120M01A l120m01a);
	public int L120UploadClsRatingToMIS(L120M01A l120m01a);
	
	public void l120m01aDoBack(L120M01A l120m01a);
	
	public void upMisByCls(L120M01A meta);
	public void uploadDW_OVS(L120M01A l120m01a, L140M01A l140m01a);
	
	public List<C120M01A> findC120M01AByMainId(String mainId);
	
	public boolean is_caseDoc_only_apply_prodKind(String l120m01a_mainId, String prodKind);
	public boolean is_tabDoc_only_apply_prodKind(L140M01A l140m01a, String prodKind);
	public boolean is_only_apply_prod07(L120M01A l120m01a);
	public boolean is_only_apply_prod08(L120M01A l120m01a);
	public boolean is_only_apply_prod69(L120M01A l120m01a);
	public boolean is_only_apply_prod69(L140M01A l140m01a);
	public boolean is_only_apply_prod69(String l120m01a_mainId);
	public boolean is_only_apply_prod71(L120M01A l120m01a);
	public String check_laaData(L120M01A l120m01a, List<L140M01A> l140m01a_list);
	public boolean has_jcic_warning_flag(C120S01G model);
	public boolean has_jcic_warning_flag(C120S01Q model);
	public boolean has_jcic_warning_flag(C120S01R model);
	public String check_clsCase_jcicNegativeInfo_with_pos___normalCase(String authLvl, List<C120M01A> c120m01as, List<L140M01A> l140m01a_list);
	public String check_L140S02A_modelKind2_modelKind3___normalCase(L120M01A l120m01a, List<L140M01A> listL140m01a, Properties prop_LMSCommomPage);
	public List<String> check_AFS___normalCase(List<L140M01A> l140m01a_list);
	//依是否適用武漢肺炎展延方案，判斷不同的負面資訊項目
	public String check_clsCase_jcicNegativeInfo_with_pos___bailout(String authLvl, List<C120M01A> c120m01as, List<L140M01A> l140m01a_list);
	//依是否適用武漢肺炎展延方案，判斷評等結果
	public String check_L140S02A_modelKind2_modelKind3___bailout(L120M01A l120m01a, List<L140M01A> listL140m01a, Properties prop_LMSCommomPage);	

	public List<String> check_AFS___bailout(List<L140M01A> l140m01a_list);
	
//	public BigDecimal getProdKind69_GutPercent();
	public String getProdKind69_checkQNote();

	/**
	 * 建立簡訊內容,SLMS-00095每小時整點一次發送
	 * @param l140m01a
	 * @param smsContent
	 */
	public void createSMSByL140M01A(L140M01A l140m01a, String smsContent);

	
	/* statFlag{1:審核中, 2:已核准} */
	public void sync_c122m01a_applyKindD_statFlag_when_cls1131(C101M01A c101m01a);
	public void sync_c122m01a_applyKindBorD_statFlag(String brNo, Set<String> idDupNoSet, String new_statFlag, String caseMainId);
	public boolean del_sync_c122m01a_applyKindD(String mainId);
	
	/* statFlag{1:審核中, 2:已核准} */
	public void sync_c122m01a_applyKindPorE_relateColumn(String new_statFlag, List<L140M01A> l140m01a_list);
	
	public String getCtlFlagType(String laaCtlFlag);
	public Map<String, Object> findActiveMajorC900M01HByCertNo2(String year, String word, String no);
	
	@Deprecated //地政士移至 C101S01Y
	public String msg_Laa_html(C101S01J model, Properties prop_LMSCommomPage);
	@Deprecated //地政士移至 C120S01Y
	public String msg_Laa_html(C120S01J model, Properties prop_LMSCommomPage);
	
	public List<C120S01V> findC120S01VByMainid(String mainId,String CustID,String Dupno);
	public void delC120S01V(String mainId, List<C120S01V> model_list);
	public boolean checkC120S01V(L120M01A l120m01a,L140M01A l140m01a);
	public boolean isGroupLoanBuildCaseMasterAccount(String groupLoanMasterNo, String itemType, String loanType, String isBuilder);
	public String CheckC120S01VIsNull(JSONObject json,Properties prop_LMSCommomPage);
	public boolean checkC120S01VCaseDate(L120M01A l120m01a);
	
	public List<C120S04W> findC120S04W_by_mainId_custId(String mainId,String custId);
	
	/**
	 * J-109-0251_10702_B1001 Web e-Loan 授信管理系統調整地政士懲戒紀錄選項判斷邏輯
	 * 
	 * 判斷當選項為5(懲戒紀錄)，且有勾選 永慶房屋直營店或信義房屋名義仲介成交案件 及 懲戒紀錄是否屬地政士法第17條
	 * 
	 * @return
	 */
	public boolean checkLaaMatchRuleFlag(Map<String, Object> c900m01h);
	//J-109-0273_10702_B1001 Web e-Loan 調整申貸資料核對表內容
	public String findCheckListVersion(L120M01A l120m01a);
	public String checkListIsNull2(JSONObject json);
	public String checkListPrompt(JSONObject json);
	public String processSuspectedHeadAccountWarnMessage(L140M01A l140m01a);
	public String processSuspectedHeadAccountWarnMessage(String l120m01a_mainId);
	/**
	 * J-109-0426_05097_B1001 為加速青創貸款之案件簽報，擬比照勞工紓困貸款方式，申請簡化洗錢防制流程
	 * @param l120m01a
	 * @return
	 */
	public boolean is_only_apply_prod61(L120M01A l120m01a);
	
	
	/**
	 * J-109-0426_05097_B1001 為加速青創貸款之案件簽報，擬比照勞工紓困貸款方式，申請簡化洗錢防制流程
	 * @param l120m01a
	 * @return
	 */
	public boolean isAmlCanPass(L120M01A l120m01a);

	public boolean is_MoneyTrustLearn_done(String empId);
	public String check_l140m01a_projClass08_MoneyTrustLearn(String empId, L140M01A l140m01a);
	public String check_l140m01a_projClass08_MoneyTrustLearn(String empId, List<L140M01A> l140m01a_list);
	public String check_l140m01a_projClass08_MoneyTrustLearn_Boss(String empId,String mainId, List<L140M01A> l140m01a_list, L120M01A l120m01a_list);
	public String checkPureCreditCaseInMainlandChina(String docKind, String authLvl, L140M01Q l140m01q, String countryCode);
	
	public boolean isOpenPureCreditCaseCheck();
	public boolean isWithinBranchAuthorization(String docKind, String authLvl);
	public void trans_DW_RKtbl_cntrNo_loanNo(String[] cntrNoSplit, String[] loanNoSplit);

	public String getSiteIdByApiInquiry(String eloanSiteId); 
	
	public C120M01A getC120M01AByOid(String oid);
	
	public C101M01A getC101M01AByOid(String custId, String dupNo, String ownBrId);
	public void createSMS(Map<String, Object> map, String smsContent);
	public void changeC122M01AStatFlag(String brNo,String custId,String statFlag);
	public String findGrntPaperByCustId(String custId);
	public List<String> check69C160M01Err(String srcMainId,String mainId);
	public String findLaborContractIsReject(L120M01A l120m01a);
	public List<String> check71C160M01Err(String l120m01a_Mainid,String mainId,List<C160M01B> c160m01b_list);
	public List<L140M01O> getL140m01oByL120m01aMainId(String l120m01a_mainId);
	public L120M01D getL120m01dObject(String l120m01a_mainId, String itemType, String itemDescription);
	public String getWarnMessageForSuspectedHeadAccount(L140M01A l140m01a);
	public String can_use_simplifyFlag_E(L120M01A l120m01a);
	public String can_use_simplifyFlag_E_by_l120s19a_brmpOutput(Brmp002O brmpOutput);	
	public String control_722_cntrNo_mustInELF442(L140M01A l140m01a);
	public Map<String, String> get_codeType_codeDesc2WithOrder(String codeType,String locale);
	public void deleteL140M01R(List<L140M01R> model_list);
	public void deleteL120S19C(List<L120S19C> model_list);
	public List<L120S19C> findL120S19CByMainIdRole(String mainId, String role);
	public String getActingRoleForPaperlessSigning(String l120m01a_mainId);
	public String can_use_simplifyFlag_E_by_l120s19a_brmpOutput_ForPaperlessSigning(Brmp002O brmpOutput);
	public List<String> chkItem71_3To8(L120M01A l120m01a);
	public String findCheckL120S15AVersion(L120M01A l120m01a);
	
	public String calculateRate(Integer loan,Integer limitMonth,Integer bufferMonth,Integer cal_Type,List<Double> multipleRate
			,List<Integer> multipleRate_TimeStart,List<Integer> multipleRate_TimeEnd,List<Integer> costList)throws CapException;
	public boolean IsStakeholderInClsCase(List<L140S02A> l140s02as, L120M01A l120m01a, L140M01A l140m01a);
	public Map<String, Object> getBrmpResultParameter(String l120m01a_mainId) throws CapException;
	public Map<String, BigDecimal> getSystemComputedFee(Brmp002O brmpOutput);
	public String checkPaperlessSigning(String l120m01a_mainId) throws CapException;
	List<C101S01Y> findC101S01Y(String mainId, String custId, String dupNo);
	List<C120S01Y> findC120S01Y(String mainId, String custId, String dupNo);
	public String getCeilingAmountOfBrmpResult(Brmp002O_result_quotationResult_calProcessObj calProcessResult, Brmp002O brmp002o);
	public boolean checkIs_outOfBranchAuthorization_ForPaperlessSigning(Brmp002O brmp002o);
	C101S02A findC101S02AByItem(String mainId, String custId, String dupNo, String item);
	C120S02A findC120S02AByItem(String mainId, String custId, String dupNo, String item);
	C160S02A findC160S02AByItem(String mainId, String custId, String dupNo, String item);
	List<C120S02A> findC120S02A(String mainId, String custId, String dupNo);
	List<C160S02A> findC160S02A(String mainId, String custId, String dupNo);
	public List<L140M01A> findL140m01aListByL120m01cMainIdAndProperty(String caseMainId, String itemType, String property);
	public C120S02B findC120S02b(String mainId, String custId, String dupNo);

	public L120S19A findL120S19A_byMainId_itemType_first_itemVersion(String mainId, String itemType);
	public L120S19A findL120S19A_byMainId_firstInput(String mainId);
	public L120S19A findL120S19A_byMainId_firstOutput(String mainId);
	public void addC122S01F(C122M01A c122m01a, String docStstus, String memo, String refoid);
	public String getCheckAllocateFundsCheckListItemMessage();
	public C160M01C findC160M01C_mainId_itemCode_itemType(String mainId, String itemCode, String itemType);
	public C101S01H findC101S01HByEjcicInquiryRecord(String custId, String dupNo, String txId, String prodId, String qBranch, String qDate, String qEmpCode);
	public C101S01U findLatestC101S01UByCustIdDupNoTxid(String custId, String dupNo, String txid);
	public String checkIsEjcicB29AndB33InquiryLogForLms(List<L160M01B> l160m01bs);
	public String checkIsEjcicB29AndB33InquiryLogForCls(String mainId_c160s01b);
	public boolean isOnlyFor027BranchUsage();
	public C122M01A findC122m01aByL140m01y(String l140m01a_mainId);
	public boolean isCloseAddandDeleteFuncForSuspectedAgentAppCase();
	public LinkedHashMap<String, BigDecimal> get_latest_mis_MISLNRAT_by_curr(String curr);
	public String getClsGradeMessage(GenericBean model,String markModel, int type, String bailout_flag);
	public String getCheckItemRange(GenericBean model);
	
			/**
	 * 取得已上傳文件數位化系統資料
	 * @param mainId
	 * @return
	 */
	List<C900M01M> findByMainId(String mainId);
	
	/**
	 * 文件數位化-依案件取得上傳用caseno
	 * @param meta
	 * @param c900m01m
	 * @return
	 */
	public Set<String> genMEGAImageCaseNoByBean(GenericBean bean);
	
	/**
	 * 文件數位化-依案件產生上傳文字檔JSONOBJECT
	 * @param meta
	 * @param c900m01m
	 * @return
	 */
	public JSONObject genJSON4EloanUploadImage(C900M01M c900m01m);
	
	/**
	 * 文件數位化-依上傳紀錄檔內容與文件類型執行上傳文件數位化作業
	 * @param c900m01m
	 * @param bean
	 */
	public void eLoanUploadImageFile(C900M01M c900m01m, GenericBean bean);
	
	/**
	 * 依檔案OID尋找已上傳紀錄檔的批號，刪除文件數位化FTP上資料夾
	 * @param docFileOid
	 */
	public void delEloanUploadImageDir(String docFileOid);
	
	/**
	 * 尋找上傳紀錄BY案號及文件所屬ID
	 * @param caseNo
	 * @param stakeholderID
	 * @return
	 */
	List<C900M01M> findC900M01MByCaseNoId(String caseNo, String stakeholderID);
	
	/**
	 * 尋找上傳紀錄BY案號及DOCFILEOID
	 * @param caseNo
	 * @param docFileOid
	 * @return
	 */
	List<C900M01M> findC900M01MByCaseNoDocFileOid(String caseNo, String docFileOid);
	
	/**
	 * 簽報書編號轉成上傳用案號<BR>
	 * 用 "OD"+caseYear(四碼數字)+caseBrId(三碼數字)+"00"+caseSeq(五碼數字) 組成16碼的編號
	 * 
	 * @param l120m01a
	 * @return
	 */
	public String tranL120M01ACaseNoToMEGAImageCaseNo(Integer caseYear, Object ownBrid, Integer caseSeq);
	
	public void saveEjcicCommonObject(List<Map<String, Object>> list, String mainId, String custId, String dupNo, String qFuncSrc);
	public void keepHtmlDataToEjcicCommonOfStardardInquiry(String mainId,
			String custId, String dupNo, String txId, String url,
			String prodId, String qBranch, String qDate, String qEmpCode,
			String qEmpName, String qFuncSrc, String userId);
	public void saveEjcicCommonFromCPXQueryLog(List<Map<String, Object>> list,
			String mainId, String custId, String dupNo, String qFuncSrc,
			String userId);
	public void deleteEjcicCommon(String custId, String prodId, String txId, String qDate, String qFuncSrc, String userId);
	public List<EJCICCOMMON> findEjcicCommonByInquiryRecord(String custId, String prodId, String txId, String qDate, String qFuncSrc);
	public void cleanL120m01dItemForHittingSameBorrowerInfo(String l120m01a_mainId, String itemType);
	public String getSameAsOtherBorrowerInfoMessage(L140M01A l140m01a);
	String getSameAsOtherBorrowerInfoMessage(String l120m01a_mainId);
	
	public BigDecimal findLowRatePlan49ByGrade1(String grade,String projClass);
	public BigDecimal getTotalPlusRateByEjcic(String custId);
	public boolean checkRatePlan49With6R(L140S02D l140s02d, BigDecimal defLowRate, BigDecimal totalPlusRate);
	public boolean checkRateProd68NotA1With6R(L140S02D l140s02d, BigDecimal lowRate);
	public boolean collTypHaveType01(String l140m01aMainId);
	public int getCollTargetCount(List<L140M01A> l140m01as);
	public boolean showRiskEvaluateQA(L120M01A l120m01a);
	public String checkAssignCaseForNewHouseLoanWhenSave(L120M01A l120m01a);
	public String checkAssignCaseForNewHouseLoan(String l140m01aMainId);
	public String checkL140m01yAssignCase(String l140m01aMainId);
	public String checkProd68Sub303NewCaseWhenSave(L120M01A l120m01a);
	public String checkProd68Sub303NewCase(L120M01A l120m01a, L140M01A l140m01a);
	public String checkHualien0403WhenSave(L120M01A l120m01a);
	public String getUseHouseLoanGu(L140M01A l140m01a);
	public List<C101S01W> findC101S01WBy(String mainId, String custId, String dupNo, String[] incomeItemArray);
	public List<C120S01W> findC120S01WBy(String mainId, String custId, String dupNo, String[] incomeItemArray);
	boolean isAddAllocateFundsCheckListAndQueryEjcicB29B33(String cntrNo,
			L140M01M l140m01m, boolean isCheckEjcicB29B33);
	
	/**
	 * 上傳財力證明授權書(附電子簽章)檔案予FTP Server
	 * 
	 * @param request 所有排程參數
	 * @param result 排程執行結果
	 * @return JSONObject 排程執行結果
	 * @throws IOException
	 */
	public JSONObject uploadFinAgreementToFTPServer(JSONObject request, JSONObject result) throws IOException;
	
	/**
	 * 從FTP Server收取聯徵回傳之財力證明授權書授權結果
	 * 
	 * @param result 排程執行結果
	 * @return JSONObject 排程執行結果
	 * @throws IOException
	 */
	public JSONObject processJCICresponseFromFTPServer(JSONObject result) throws IOException;
	
	public  <T> T  to_outputObj(String jsonStr, Class<T> assign_class) throws CapException;
	
	/**
	 * 上傳信貸案件資料(進件管理為主)至DW Table：DW_ELOAN_APPLY
	 * 
	 * @param isFirstTime Y-每週上傳所有文件狀態之資料；N-每日上傳非已結案之資料
	 * @return JSONObject 排程執行結果
	 * @throws IOException
	 */
	public JSONObject uploadHPCLDataToDW_ELOAN_APPLY(String isFirstTime) throws IOException;
	
	/**
	 * 上傳信貸案件資料(簽報書為主)至DW Table：DW_ELOAN_REQUEST
	 * 
	 * @param isFirstTime Y-每週上傳所有文件狀態之資料；N-每日上傳非已結案之資料；D-於DW刪除簽報書為已刪除的資料
	 * @return JSONObject 排程執行結果
	 * @throws IOException
	 */
	public JSONObject uploadHPCLDataToDW_ELOAN_REQUEST(String isFirstTime) throws IOException;
	
	public void keep_htmlData_to_c101s01u(String mainId, String custId, String dupNo, String txId, String resp_html);
	
	public void keepHtmlT70File(String mainId, String custId, String dupNo);
	
	public void saveC101S02S(C101S02S entity);
	
	public List<L140M01C> findL140m01cListByMainId(String mainId);
	
	public String _dwDOCSTATUS(String docStatus);
	
	public Integer parseIntColumn(Object s);
	
	public Integer convert_dw_edu(C120S01A c120s01a);
	
	public String _convertBusi(String busi);

	public C101S02S findLatestC101S02S(String custId, String dupNo, String branchNo);
	public boolean isCloseBankMemberPreferentialRateM2_M3_N2();
	public BigDecimal _get_latest_mis_MISLNRAT_currTWD(String lrCode);
	public CapAjaxFormResult findByCodeTypeWithoutDesc2WithOrderBy(String codeType, String codeDesc2,
																   boolean needNoneValue);
	public String checkAssignCaseForNewProd71(L120M01A l120m01a);

	public int computePersonalDebtRatioDeduction(String l120m01a_mainId, String custId, String dupNo, String l140m01a_mainId, L140MC1B l140mc1b);
	public boolean setMortgageCheckingRatio(L120M01A l120m01a);
	boolean isOpenMortgageRatioCheck();
	public String checkIsBlockSigningCaseByProjectClass(String projectClass, String property);
	/**
	 * J-113-0435 動審表新增email驗證及提醒
	 * @param caseMianid
	 * @return
	 */
	public C340M01A findByCaseMainid_PloanCtrStatus9(String caseMianid);
	/**
	 * J-113-0349 新增綠色授信、社會責任授信提示訊息
	 * @param listL140m01a
	 * @return
	 */
	public List<String> cfmMsg_esg(String mainid);
	public int getAge(Date birthday);
}
