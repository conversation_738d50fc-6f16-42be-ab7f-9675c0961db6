<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd">

	<bean id="flowEngineFactory" class="tw.com.jcs.flow.FlowEngineFactory">
 		<property name="dataSource" ref="lms-db"/>
 		<property name="objectFactory">
 			<bean class="tw.com.jcs.flow.provider.impl.SpringObjectFactory" />
 		</property>
 		<property name="config">
			<map>
				<entry key="table.instance" value="LMS.BFLOWINST" />
				<entry key="table.sequence" value="LMS.BFLOWSEQ" />
				<entry key="table.instanceHistory" value="LMS.BFLOWINSTH" />
				<entry key="table.sequenceHistory" value="LMS.BFLOWSEQH" />
				<entry key="cacheSize" value="100" />
				<entry key="idProvider" value="com.mega.eloan.lms.base.flow.LmsIdProvider" />
				<entry key="definitionLocation" value="/flow" />
			</map>
		</property>
	</bean>

	<bean id="flowEngine" factory-bean="flowEngineFactory" factory-method="buildEngine" lazy-init="true" />
	<bean id="flowService" factory-bean="flowEngine" factory-method="getService" />

	<context:component-scan base-package="com.mega.eloan.lms.**.flow.**" />

</beans>
