/* 
 * C900M01ODaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.C900M01ODao;
import com.mega.eloan.lms.model.C900M01O;

/** 歡喜信貸自動派案維護-被指派分行清單檔**/
@Repository
public class C900M01ODaoImpl extends LMSJpaDao<C900M01O, String>
	implements C900M01ODao {

	@Override
	public C900M01O findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C900M01O> findListByOid(String oid){
		ISearch search = createSearchTemplete();
		List<C900M01O> list = null;
		if (oid != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<C900M01O> findByAssigneeBrchId(String assigneeBrchId){
		ISearch search = createSearchTemplete();
		List<C900M01O> list = null;
		if (assigneeBrchId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "assigneeBrchId", assigneeBrchId);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<C900M01O> findAll() {
		ISearch search = createSearchTemplete();
		return createQuery(search).getResultList();
	}
}