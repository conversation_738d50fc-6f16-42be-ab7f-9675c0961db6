/* 
 * C140M04BDaoImpl.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.C140M04BDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C140M04B;
import com.mega.eloan.lms.model.C140M04B_;

/**
 * <pre>
 * 徵信調查報告書第四章主檔 保證公司
 * </pre>
 * 
 * @since 2011/10/6
 * <AUTHOR>
 * @version <ul>
 *          <li>new
 *          </ul>
 */
@Repository
public class C140M04BDaoImpl extends LMSJpaDao<C140M04B, String> implements
		C140M04BDao {

	@Override
	public List<C140M04B> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS,C140M04B_.mainId.getName(), mainId);
		return find(search);
	}

	@Override
	public List<C140M04B> findByMainId(String mainId, String[] orderby) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS,C140M04B_.mainId.getName(), mainId);
		for(String orderBy : orderby){
			search.addOrderBy(orderBy);
		}
		return find(search);
	}
}// ;
