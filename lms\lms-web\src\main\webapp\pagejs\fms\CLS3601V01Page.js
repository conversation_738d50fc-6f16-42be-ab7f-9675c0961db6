var _handler = "cls3601formhandler";

$(function(){	
	var my_colModel = [{
        colHeader: "",name: 'oid', hidden: true
    }, {
    	colHeader: "",name: 'mainId', hidden: true   
    }, {
    	colHeader: "",name: 'itemType', hidden: true   
    }, {
        colHeader: i18n.cls3601v01["C360M01A.custId"], //統一編號
        align: "left", width: 80, sortable: true, name: 'custId',
        formatter: 'click', onclick: openDoc
    }, {
        colHeader: i18n.cls3601v01["C360M01A.custName"], //姓名/名稱
        align: "left", width: 110, sortable: true, name: 'custName'
    }, {
        colHeader: i18n.cls3601v01["C360M01A.itemType"], //修改項目
        align: "left", width: 90, sortable: false, name: 'itemTypeDesc'
    }, {
        colHeader: i18n.cls3601v01["C360M01A.cntrNo"], //額度序號
        align: "left", width: 100, sortable: true, name: 'cntrNo'
    }, {
        colHeader: i18n.cls3601v01["C360M01A.befDscr"], //修改前資料
        align: "left", width: 120, sortable: false, name: 'befDscr'
    }, {
        colHeader: i18n.cls3601v01["C360M01A.aftDscr"], //修改後資料
        align: "left", width: 120, sortable: false, name: 'aftDscr'
    }, {
    	colHeader: i18n.cls3601v01["C360M01A.updater"], //異動人員
        align: "left", width: 80, sortable: true, name: 'updater'
    }];
	
	if(viewstatus == "030"){
		//
		my_colModel.push({
			colHeader: i18n.cls3601v01["C360M01A.approver"], //核准人員
	        align: "left", width: 80, sortable: true, name: 'approver'
	    });
		my_colModel.push({
			colHeader: i18n.cls3601v01["C360M01A.approveTime"], //核准日期
	        align: "left", width: 100, sortable: true, name: 'approveTime'
	    });
	}
	
	
	var grid = $("#gridview").iGrid({
        handler: "cls3601gridhandler",
        height: 350,
        rowNum: 15,
        shrinkToFit: false,
        multiselect: false, 
        postData: {
            formAction: "queryMain",
            docStatus: viewstatus
        },
        colModel: my_colModel,
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = grid.getRowData(rowid);
            openDoc(null, null, data);
        }
    });
    
    function openDoc(cellvalue, options, rowObject){
    	var postData = {
    			'mainOid': rowObject.oid, 
    			'mainId': rowObject.mainId,
    			'mainDocStatus': viewstatus
    		}
    	
    	if (typeof noOpenDoc != 'undefined' && noOpenDoc == 'Y'){
    		postData['noOpenDoc'] = true;
    	};
    	ilog.debug("mainId="+rowObject.mainId +", itemType="+rowObject.itemType );
    	if(rowObject.itemType=="01"){
    		$.form.submit({
    			url : '../fms/cls3601m01/01',
    			data : postData,
    			target : rowObject.oid || '_blank'
    		});
    	}else if(rowObject.itemType=="02"){
    		$.form.submit({
    			url : '../fms/cls3601m02/01',
    			data : postData,
    			target : rowObject.oid || '_blank'
    		});
    	}
    }
    
    $("#buttonPanel").find("#btnFilter").click(function(){
    	var _id = "_div_cls3601v01_filter";
		var _form = _id+"_form";
		 	
		if ($("#"+_id).size() == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");
			dyna.push("<form id='"+_form+"'>");
			dyna.push("	<table class='tb2' >");		
			dyna.push("	<tr><td class='hd1' nowrap>"+i18n.cls3601v01["C360M01A.custId"]+"</td><td>"
					+"<input type='text' id='search_custId' name='search_custId'  maxlength='10' size='12'>"
					+"</td></tr>");
			dyna.push("	<tr><td class='hd1' nowrap>"+i18n.cls3601v01["C360M01A.cntrNo"]+"</td><td>"
					+"<input type='text' id='search_cntrNo' name='search_cntrNo'  maxlength='12' size='14'>"
					+"</td></tr>");
			dyna.push(" </table>");
			dyna.push("</form>");
			
			dyna.push("</div>");
			
		    $('body').append(dyna.join(""));
		}
		//clear data
		$("#"+_form).reset();
		
		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
			//ui_lms2401.msg02=請選擇不覆審原因
	       title: '篩選',
	       width: 550,
           height: 190,
           align: "center",
           valign: "bottom",
           modal: false,
           i18n: i18n.def,
           buttons: {
               "sure": function(){
                  $.thickbox.close();
                  //=============
                  grid.setGridParam({
  	                postData: $.extend(
  	                	{}
  	                	,$("#"+_form).serializeData()
  	                ),
  	                search: true
                  }).trigger("reloadGrid");
               },
               "cancel": function(){
            	   $.thickbox.close();
               }
           }
		});
    }).end().find("#btnAdd").click(function(){
    	$("#div_itemType").thickbox({
            title: "",
            width: 500, height: 200, align: 'center', valign: 'bottom', modal: true, i18n: i18n.def,
            buttons: {
                "sure": function(){
               	 	var $frm = $("#form_itemType");
                    if ($frm.valid()) {  
                        $.thickbox.close();
                        //~~~~~~~~~~~~~~~~~~~~~~~
                   	 	var val_itemType = $("#form_itemType #itemType").val();       	                        
                   	 	inputByItemType(val_itemType).done(function(json_inputByItemType){
	                   	 	$.ajax({
	    						handler : _handler,
	    						type : "POST",
	    						dataType : "json",
	    						data :$.extend({'formAction' : 'newC360M01A'}, json_inputByItemType),
	    						success : function(obj) {
	    				        	grid.trigger("reloadGrid");
	    				          	API.showMessage(i18n.def.runSuccess);      
	    						}
	    					});
	                   	});
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
		
    }).end().find("#btnDelete").click(function(){
    	var row = grid.getGridParam('selrow');		
		if(row){
			var data = grid.getRowData(row);
			CommonAPI.confirmMessage(i18n.def["confirmDelete"],function(b){
				if(b){
					$.ajax({
						handler : _handler,
						type : "POST",
						dataType : "json",
						data :{
							'formAction' : 'delC360M01A',
							'oid' : data.oid,
							'mainOid' : data.oid,
							'mainId' : data.mainId,
							'mainDocStatus' : viewstatus
						},
						success : function(obj) {
				        	grid.trigger("reloadGrid");
				          	API.showMessage(i18n.def.runSuccess);      
						}
					});
				}
			})				
		}else{
			API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
			return;
		}
    });

    function inputByItemType(val_itemType){
    	var my_dfd = $.Deferred();   
    	if(val_itemType=="01"){
    		$("#form_itemType01").reset();
    		
    		$("#form_itemType01").thickbox({
                title: "",
                width: 500, height: 200, align: 'center', valign: 'bottom', modal: true, i18n: i18n.def,
                buttons: {
                    "sure": function(){
                   	 	var $frm = $("#form_itemType01");
                        if ($frm.valid()) {  
                            $.thickbox.close();
                            //~~~~~~~~~~~~~~~~~~~~~~~
                       	 	var itemType01_cntrNo = $("#form_itemType01 #itemType01_cntrNo").val();       	                        
                       	 	my_dfd.resolve({'itemType':val_itemType, 'cntrNo':itemType01_cntrNo});
                        }
                    },
                    "cancel": function(){
                        $.thickbox.close();
                    }
                }
            });
    		
    	}else if(val_itemType=="02"){
    		$("#form_itemType02").reset();
    		
    		$("#form_itemType02").thickbox({
                title: "",
                width: 500, height: 200, align: 'center', valign: 'bottom', modal: true, i18n: i18n.def,
                buttons: {
                    "sure": function(){
                   	 	var $frm = $("#form_itemType02");
                        if ($frm.valid()) {  
                            $.thickbox.close();
                            //~~~~~~~~~~~~~~~~~~~~~~~
                       	 	var itemType02_cntrNo = $("#form_itemType02 #itemType02_cntrNo").val();       	                        
                       	 	my_dfd.resolve({'itemType':val_itemType, 'cntrNo':itemType02_cntrNo});
                        }
                    },
                    "cancel": function(){
                        $.thickbox.close();
                    }
                }
            });
    		
    	}
    	return my_dfd.promise();
    }
    
});
