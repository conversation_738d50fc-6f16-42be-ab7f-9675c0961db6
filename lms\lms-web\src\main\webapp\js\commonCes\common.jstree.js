(function($){
    $.jstree.plugin("iJstree", {
        __init: function(){
            this._change_state = this.change_state;
            this.change_state = this.__change_state;
        },
        defaults: {
            change_state: true
        },
        _fn: {
            set_json_data: function(array, expand){
                var _def = this.get_settings(), _tmpCS = _def.iJstree.change_state;
                _def.json_data.data = array ||
                [{
                    data: " "
                }];
                _def.iJstree.change_state = true;
                this._set_settings_not_extend(_def);
                this.refresh();
                var _lis = this.get_container().find("li");
                for (var i = _lis.length - 1; i >= 0; i--) {
                    this._repair_state(_lis.get(i));
                }
                _def.iJstree.change_state = _tmpCS;
                this[expand ? "open_all" : "close_all"]();
            },
            
            __change_state: function(obj, state){
                this._get_settings().iJstree.change_state && (this._change_state(obj, state));
            }
            
        }
    });
    // include the iJstree plugin by default
    $.jstree.defaults.plugins.push("iJstree");
})(jQuery);


$.extend($cap._fn, {
    iJstree: {
        rule: ".jstree",
        fn: {
        
            /**
             * 序列化樹狀資料
             * @param {Boolean} stringify
             */
            iJsTreeSerialize: function(stringify){
                var data = this.parseNode(this);
                return stringify ? JSON.stringify(data) : data;
            },
            
            /**
             * 序列化樹狀資料-指定節點
             * @param {jQuery} node
             */
            parseNode: function(obj){
                var _r = (obj || this).children("ul"), $root = (this.is(".jstree") &&
                this ||
                this.closest(".jstree"));
                var _res = [];
                if (_r.length) {
                    _r.each(function(i, e){
                        $(e).children("li").each(function(ii, ee){
                            ee = $(ee);
                            var _attr = {
                                oid: ee.attr("oid"),
                                name: $root.jstree("get_text", ee),
                                path: $root.jstree("get_path", ee).join("/")
                            };
                            ee.children("ul").length &&
                            (_attr["child"] = $root.parseNode(ee));
                            _res.push(_attr);
                        });
                    });
                }
                return _res;
            },
            
            /**
             * 於所選擇節點建立子節點
             * @param {JSON} attr jstree 參數
             */
            createNode: function(attr){
                this.jstree("get_path") && this.jstree("create", null, "last", attr || {});
                return this;
            },
            
            /**
             * 移除所選節點(root 不可移除)
             */
            removeNode: function(){
                if (this.jstree("get_path")) {
                    if (this.jstree("get_path").length == 1) {
                        API.showMessage(i18n.def["jstree.01"]);
                        return;
                    }
                    else 
                        if (this.jstree("get_selected").children("ul:eq(0)").children("li").length) {
                            API.showMessage(i18n.def["jstree.02"]);
                            return;
                        }
                    this.jstree("remove");
                }
                return this;
            },
            /**
             * 取得所選節點之Path
             * (ex: root/subnode/subnoe2)
             */
            getNodePath: function(){
                if (this.jstree("get_path")) 
                    return this.jstree("get_path").join("/");
            },
            
            /**
             * 更改所選節點之名稱
             */
            renameNode: function(){
                if (this.jstree("get_path")) {
                    if (this.jstree("get_path").length == 1) {
                        API.showMessage(i18n.def["jstree.03"]);
                        return;
                    }
                    this.jstree("rename");
                }
                return this;
            },
            
            /**
             * 設定新資料至樹狀結構
             * @param {JSON} data
             * @param {Boolean} expand
             */
            setJsonData: function(json, expand){
                this.jstree("set_json_data", json, expand);
                return this;
            },
            
            /**
             * 取得已選擇Checkbox資料
             * @param {Array} attribute 預另外取得之attr
             * @param {Boolean} stringify
             */
            getCheckedState: function(attribute, stringify){
                if (typeof(attribute) === "boolean" || typeof(attribute) === "undefined") {
                    stringify = attribute;
                    attribute = [];
                }
                var _c = [];
                this.find("li").each(function(i, e){
                    e = $(e);
                    var _attr = {
                        oid: e.attr("oid") || "",
                        checked: e.is(".jstree-checked,.jstree-undetermined")
                    
                    };
                    $.each(attribute, function(i, key){
                        _attr[key] = e.attr(key);
                    });
                    _c.push(_attr);
                });
                return stringify ? JSON.stringify(_c) : _c;
            },
            
            /**
             * 將樹展開
             */
            treeExpand: function(){
                this.jstree("open_all");
                return this;
            },
            
            /**
             * 將樹關閉
             */
            treeCollapse: function(){
                this.jstree("close_all");
                return this;
            }
            
        }
    }
});

/**
 * JsTree
 */
jQuery.fn.extend({

    /**
     * build tree by jsTree
     *
     * @param {Object}
     *            settings
     */
    iJsTree: function(settings){
        var $this = $(this);
        var _s = $.extend(true, {
            editor: false, //是否可編輯
            checkbox: false, //是否產生checkbox
            checkChange: true, //是否可更改checkbox 狀態
            // handler: "",
            data: [{ //樹狀data
                data: " "
            }],
            onclick: $.emptyFunction // click node 動作
        }, settings);
        
        
        if (typeof _s.data === "string") {
            _s.data = [{
                data: _s.data
            }];
        }
        
        $this = $this.on("loaded.jstree refresh.jstree create_node.jstree", function(event, data){
            $(this).find("li > a,li > ins").off('click').on('click.ijstree', function(e){
                var node = $(e.target).is("ins") ? $(this).siblings("a") : $(this);
                var _attr = {}, _p = $(this).parent(), $root = _p.closest(".jstree");
                _s.onclick.call(_p, {
                    oid: _p.attr("oid"),
                    name: $root.jstree("get_text", _p),
                    path: $root.jstree("get_path", _p).join("/"),
                    node: node,
                    root: $root,
                    checked: _p.is(".jstree-undetermined,.jstree-unchecked")
                }, e);
                e.preventDefault();
            }).filter("a").each(function(){
				$(this).attr("title",$(this).text());
			});
        }).on("refresh.jstree create_node.jstree", function(){
            var $types = $this.jstree("get_settings"), defaultIcon = $types.types.types["default"];
        }).jstree({
            plugins: (function(){
                var _p = ["themes", "json_data", "ui", "iJstree", "types"];
                _s.editor && _p.push("crrm");
                _s.checkbox && _p.push("checkbox");
                return _p;
            })(),
            
            core: {
                html_titles: false,
                animation: 200,
                initially_open: [],
                rtl: false,
                strings: {
                    loading: i18n.def["jstree.04"],
                    new_node: i18n.def["jstree.05"]
                }
            },
            "iJstree": {
                change_state: _s.checkChange
            },
            "json_data": {
                data: _s.data
            },
            "types": {
                "types": {
                    "default": {
                        "valid_children": "none",
                        "icon": {
                            "image": "../../img/folder-closed.gif"
                            //                            "image": "../../img/tree-doc.png",
                            //                            "position": "0px -16px"
                        }
                    },
                    "folder-open": {
                        "valid_children": ["default", "folder"],
                        "icon": {
                            "image": "../../img/folder-open.gif"
                            //                            "image": "../../img/tree-doc.png",
                            //                            "position": "0px 0px"
                        }
                    },
                    "file": {
                        "valid_children": ["default", "folder"],
                        "icon": {
                            "image": "../../img/file.gif"
                            //                            "image": "../../img/tree-doc.png",
                            //                            "position": "0px -32px"
                        }
                    },
                    "root": {
                        "valid_children": ["default", "file", "folder-open"],
                        "icon": {
                            "image": "../../img/folder-closed.gif"
                            //                            "image": "../../img/tree-doc.png",
                            //                            "position": "0px -16px"
                        
                        },
                        "start_drag": false,
                        "move_node": false,
                        "delete_node": false,
                        "remove": false
                    }
                }
            }
        
        
        });
        $.extend($this, $cap._fn.iJstree.fn);
        return $this;
    }
});
