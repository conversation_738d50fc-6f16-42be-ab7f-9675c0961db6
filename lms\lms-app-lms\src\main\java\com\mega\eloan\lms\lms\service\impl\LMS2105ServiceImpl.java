/* 
 * LMS2105ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.enums.DocLogEnum;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.service.FlowNameService;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dao.L210A01ADao;
import com.mega.eloan.lms.dao.L210M01ADao;
import com.mega.eloan.lms.dao.L210M01BDao;
import com.mega.eloan.lms.dao.L210S01ADao;
import com.mega.eloan.lms.dao.L210S01BDao;
import com.mega.eloan.lms.lms.service.LMS2105Service;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L210M01A;
import com.mega.eloan.lms.model.L210M01B;
import com.mega.eloan.lms.model.L210S01A;
import com.mega.eloan.lms.model.L210S01B;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.core.FlowException;
import tw.com.jcs.flow.service.FlowService;

/**
 * <pre>
 * 修改資料特殊流程
 * </pre>
 * 
 * @since 2012/01/10
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/01/10,REX,new
 *          </ul>
 */
@Service
public class LMS2105ServiceImpl extends AbstractCapService implements
		LMS2105Service {
	private static Logger logger = LoggerFactory
			.getLogger(LMS2105ServiceImpl.class);

	@Resource
	FlowService flowService;
	@Resource
	FlowNameService flowNameService;

	@Resource
	DocLogService docLogService;

	@Resource
	L210M01ADao l210m01aDao;

	@Resource
	L120M01ADao l120m01aDao;

	@Resource
	L210M01BDao l210m01bDao;

	@Resource
	L210A01ADao l210a01aDao;

	@Resource
	L210S01ADao l210s01aDao;

	@Resource
	L210S01BDao l210s01bDao;

	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L210M01A) {

					((L210M01A) model).setUpdater(user.getUserId());
					((L210M01A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					((L210M01A) model).setRandomCode(IDGenerator
							.getRandomCode());
					l210m01aDao.save((L210M01A) model);
					docLogService.record(((L210M01A) model).getOid(),
							DocLogEnum.SAVE);

				} else if (model instanceof L210M01B) {
					((L210M01B) model).setUpdater(user.getUserId());
					((L210M01B) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l210m01bDao.save((L210M01B) model);
				} else if (model instanceof L210S01A) {
					((L210S01A) model).setUpdater(user.getUserId());
					((L210S01A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l210s01aDao.save((L210S01A) model);
				} else if (model instanceof L210S01B) {
					((L210S01B) model).setUpdater(user.getUserId());
					((L210S01B) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l210s01bDao.save((L210S01B) model);

				}
			}
		}
	}

	@Override
	public void delete(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L210M01A) {
					((L210M01A) model).setDeletedTime(CapDate
							.getCurrentTimestamp());
					((L210M01A) model).setUpdater(user.getUserId());
					docLogService.record(((L210M01A) model).getOid(),
							DocLogEnum.DELETE);
					l210m01aDao.save((L210M01A) model);
				} else if (model instanceof L210M01B) {
					l210m01bDao.delete((L210M01B) model);
				} else if (model instanceof L210S01A) {
					l210s01aDao.delete((L210S01A) model);
				} else if (model instanceof L210S01B) {
					l210s01bDao.delete((L210S01B) model);
				}
			} else {
				logger.debug("\n delete =====>is NULL");
			}

		}

	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == L210M01A.class) {
			return l210m01aDao.findPage(search);
		} else if (clazz == L210M01B.class) {
			return l210m01bDao.findPage(search);
		} else if (clazz == L210S01A.class) {
			return l210s01aDao.findPage(search);
		} else if (clazz == L210S01B.class) {
			return l210s01bDao.findPage(search);
		}
		logger.debug("\n findPage =====>is NULL");
		return null;
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		if (clazz == L210M01A.class) {
			return (T) l210m01aDao.findByOid(oid);
		} else if (clazz == L210M01B.class) {
			return (T) l210m01bDao.findByOid(oid);
		} else if (clazz == L210S01A.class) {
			return (T) l210s01aDao.findByOid(oid);
		} else if (clazz == L210S01B.class) {
			return (T) l210s01bDao.findByOid(oid);
		}
		logger.debug("\n findModelByOid =====>is NULL");
		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		if (clazz == L210M01A.class) {
			return l210m01aDao.findByMainIds(mainId);
		} else if (clazz == L210M01B.class) {
			return l210m01bDao.findByMainId(mainId);
		} else if (clazz == L210S01A.class) {
			return l210s01aDao.findByMainId(mainId);
		} else if (clazz == L210S01B.class) {
			return l210s01bDao.findByMainId(mainId);
		}
		logger.debug("\n findListByMainId=====>is NULL");
		return null;
	}

	@Override
	public void saveL210s01as(List<L210S01A> l210s01as) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (L210S01A l210s01a : l210s01as) {
			l210s01a.setUpdater(user.getUserId());
			l210s01a.setUpdateTime(CapDate.getCurrentTimestamp());
		}
		l210s01aDao.save(l210s01as);

	}

	@Override
	public void saveL210s01bs(List<L210S01B> l210s01bs) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (L210S01B l210s01b : l210s01bs) {
			l210s01b.setUpdater(user.getUserId());
			l210s01b.setUpdateTime(CapDate.getCurrentTimestamp());
		}
		l210s01bDao.save(l210s01bs);
	}

	@Override
	public void saveNewL210m01a(List<L210S01A> l210s01as,
			List<L210S01B> l210s01bs, L210M01A l210m01a, L120M01A l120m01a) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		l210m01aDao.save(l210m01a);
		l120m01aDao.save(l120m01a);
		flowService.start("LMS2105Flow", l210m01a.getOid(), user.getUserId(),
				user.getUnitNo());
		if (!l210s01as.isEmpty()) {
			l210s01aDao.save(l210s01as);
		}

		if (!l210s01bs.isEmpty()) {
			l210s01bDao.save(l210s01bs);
		}

	}

	@Override
	public int findL210s01bMaxSeq(String mainId) {
		List<L210S01B> l210s01bs = l210s01bDao.findByMainId(mainId);
		int count = 0;
		int seqval = 0;
		for (L210S01B l210s01b : l210s01bs) {// 取出這個mainID底下的最大Seq
			seqval = l210s01b.getSeq();
			if (seqval > count) {
				count = seqval;
			}
		}
		return ++count;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public boolean deleteListByOid(Class clazz, String[] oids) {

		boolean flag = false;
		if (clazz == L210M01A.class) {
			List<L210M01A> l210m01as = l210m01aDao.findByOids(oids);
			l210m01aDao.delete(l210m01as);
			return true;
		} else if (clazz == L210S01A.class) {
			List<L210S01A> l210s01as = l210s01aDao.findByOids(oids);
			l210s01aDao.delete(l210s01as);
			return true;
		} else if (clazz == L210S01B.class) {
			List<L210S01B> l210s01bs = l210s01bDao.findByOids(oids);
			l210s01bDao.delete(l210s01bs);
			return true;
		}
		return flag;
	}

	@Override
	public List<L210S01A> findL210s01saByMainIdAndChgFlag(String mainId,
			String chgFlag) {
		return l210s01aDao.findByMainIdAndChgFlag(mainId, chgFlag);
	}

	@Override
	public L210S01A findL210s01saByMainIdAndChgFlagAndBrid(String mainId,
			String chgFlag, String shareBrId) {
		return l210s01aDao.findByMainIdAndChgFlag(mainId, shareBrId, chgFlag);
	}

	@Override
	public List<L210S01B> findL210s01bsByMainIdAndChgFlag(String mainId,
			String chgFlag) {
		return l210s01bDao.findByMainIdAndChgFlag(mainId, chgFlag);
	}

	@Override
	public void flowAction(String mainOid, GenericBean model,
			boolean setResult, String action) throws Throwable {
		if (model instanceof L210M01A) {
			save((L210M01A) model);
		}
		try {
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			FlowInstance inst = flowService.createQuery().id(mainOid).query();
			if (inst == null) {
				inst = flowService.start("LMS2105Flow",
						((L210M01A) model).getOid(), user.getUserId(),
						user.getUnitNo());
			}
			if (setResult) {
				inst.setAttribute("result", flowNameService.getKey(action));
				logger.debug(StrUtils.concat("\n FlowAction =====> ",
						flowNameService.getKey(action)));
			}
			inst.next();
		} catch (FlowException e) {
			Throwable t1 = e;
			while (t1.getCause() != null) {
				t1 = t1.getCause();
			}
			throw t1;
		}

	}

	@Override
	public L210M01A findL210M01AByMainId(String mainId) {
		return l210m01aDao.findByMainId(mainId);
	}
}
