﻿/*
* jQuery Watch 1.0.0
*
* Copyright (c) 2009 SolutionStream.com & <PERSON> (http://www.solutionstream.com/)
* Dual licensed under the MIT (MIT-LICENSE.txt)
* and GPL (GPL-LICENSE.txt) licenses.
* 
* ===============================================================================================
*
*	Provides an interface to objects matching the JavaScript 1.5 Object.prototype.watch 
*	method.  For those browsers that support it, uses the build in functionality, other 
*	browsers use a timer, to manually check for changes on each object/property/callback
*
*	EXAMPLE:
*			jQuery(o).watch("someProperty", function(propertyName, oldValue, newValue) {
*				alert("o." + propertyName + " changed from " + oldValue + " to " + newValue);
*/
(function($) {
	//jQuery extension method
	$.fn.watch = function(propertyName, callbackMethod) {
		for (var i = 0; i < this.length; i++) {
			var o = this[i];
			if (o.watch){
				o.watch(propertyName, callbackMethod); //pass to built in watch handler JS 1.5
			}
			else if (watchIt)
				watchIt(o, propertyName, callbackMethod); //use timer based watch/check
		}
	}

	//watch handler for IE
	if (typeof (Object.prototype.watch) == "undefined") {
		//private variable for watched objects
		var watchedObjects = [];

		//start the checkWatches method on dom-ready
		$(checkWatches);

		//repeating method to manually check for changes in watched objects/properties
		function checkWatches() {
			try {
				var x;
				for (var i = 0; i < watchedObjects.length; i++) {
					x = watchedObjects[i];
					if (x.obj[x.prop] != x.last || typeof (x.obj[x.prop]) != typeof (x.last)) {
						x.exec.call(x.obj, x.prop, x.last, x.obj[x.prop]);
						x.last = x.obj[x.prop];
					}
				}
			} catch (err) { }

			//re-run the check in .1 seconds
			watchTimer = window.setTimeout(checkWatches, 100);
		}

		//method to add the property/callback for watching.
		function watchIt(obj, propertyName, callbackMethod) {
			watchedObjects.push({
				"obj": obj,
				"prop": propertyName,
				"last": obj[propertyName],
				"exec": callbackMethod
			});
		}
	}
})(jQuery);