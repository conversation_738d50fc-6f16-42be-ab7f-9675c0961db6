var inits = {
    fhandle: "cls2501m01formhandler",
    ghaddle: "cls2501gridhandler"
};
// 驗證readOnly狀態
function checkReadonly(){
    var auth = (responseJSON ? responseJSON.Auth : {}); // 權限
    // auth.readOnly ||
    if (auth.readOnly || responseJSON.mainDocStatus != "01O") {
        return true;
    }
    return false;
}

$(function(){
	
	load_item().done(function(){
		
	    $.form.init({
	        formHandler: inits.fhandle,
			formAction:"queryC250M01A", 
	        loadSuccess: function(json){
					
					$('#CLS2501M01Form').injectData(json);	
					responseJSON.mainDocStatus = json.docStatusVal;
		            responseJSON.unitLoanCase = json.unitLoanCase;
		            var auth = (responseJSON ? responseJSON.Auth : {}); // 權限
		         
		            responseJSON.mainId = json.mainId;
		
		            $("#check1").show();
		            if (responseJSON.page == "01") {
		                $("#bossId").html(json.bossId);
		            }
		            // $("label").lockDoc();
		            if (checkReadonly()) {
		                $(".readOnlyhide").hide();
		                $("form").lockDoc();
		            }
					$("#openBox_L140M01M").show();
	            
	        }
	    });// close form init
	    
		loadOverdueDataGrid();
    });

	var $formObject = $("#CLS2501M01Form");
	var $othermemo = $formObject.find("#othermemo")
	var $BranchCommTable = $formObject.find("#BranchCommTable")
        $formObject.find("[name=lnflag]").click(function(){
          $othermemo.hide();
		  $BranchCommTable.hide();
          if ($(this).val() == "D") {
             $othermemo.show();			 
          } else {		  	
		  	$formObject.find("#othermemo").val("");						
		  }
		  
		  if ($(this).val() == "E") {
		  	$formObject.find("#othermemo").val("");
			$formObject.find("#branchComm").val("");             			 
          } else {		  	
			$BranchCommTable.show();			
		  }		  
        });
	
// 呈主管覆核 選授信主管人數
    $("#numPerson").change(function(){
        $('#bossItem').empty();
        var value = $(this).val();
        if (value) {
            var html = '';
            for (var i = 1; i <= value; i++) {
                var name = 'boss' + i;
                html += i + '. ' // +i18n.cls1161m01['manager.L3']
                // || '授信主管'
                html += '<select id="' + name + '" name="boss"' +
                '" class="required" CommonManager="kind:2;type:2"></select>';
                html += '<br/>';
            }
            $('#bossItem').append(html).find('select').each(function(){
                $(this).setItems({
                    item: item,
                    format: "{value} {key}"
                });
            });
        }
        
    });

    var btn = $("#buttonPanel");
    btn.find("#btnSave").click(function(showMsg){
        saveData(true);
    }).end().find("#btnDelete").click(function(){
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                $.ajax({
                    handler: inits.fhandle,
                    data: {
                        formAction: "delete",
                        mainOid: $("#oid").val()
                    }
                });
            }
        });
    }).end().find("#btnTest").click(function(){
        $.ajax({
            handler: inits.fhandle,
            data: {
                formAction: "TETSMIS",
                mainOid: $("#mainOid").val()
				}
            }).done(function(){
        });
    }).end().find("#btnSend").click(function(){
        saveData(false, sendBoss);
    }).end().find("#btnAccept").click(function(){
        flowAction({
            flowAction: true
        });
    }).end().find("#btnCheck").click(function(){
        openCheck();
    }).end().find("#btnPrint").click(function(){
        if (checkReadonly()) {
            printAction();
        }
        else {
            // saveBeforePrint=執行列印將自動儲存資料，是否繼續此動作?
            CommonAPI.confirmMessage(i18n.def["saveBeforePrint"], function(b){
                if (b) {
                    saveData(false, printAction);
                }
            });
        }
    });
    
    // 列印動作
    function printAction(){
        $.form.submit({
            url: "../../simple/FileProcessingService",
            target: "_blank",
            data: {
                mainId: responseJSON.mainId,
                mainOid: responseJSON.oid,   //$("#oid").val(),
                fileDownloadName: "cls2501r01.pdf",
                serviceName: "cls2501r01rptservice"
            }
        });
    }

    // 儲存的動作
    function saveData(showMsg, tofn){
    // 為檢查UI的值是否皆無異常
		if($("#CLS2501M01Form").valid()==false){
			return;
		}
		
                var allresult = {};
                var localresult = {};
                var selfresult = {};
                FormAction.open = true;
                $.ajax({
                    handler: 'cls2501m01formhandler',
                    data: {// 把資料轉成json
                        formAction: "saveC250M01A"
                        , page: responseJSON.page
                        , txCode: responseJSON.txCode
                        , showMsg: showMsg
						, CLS2501M01Form: JSON.stringify($("#CLS2501M01Form").serializeData())
                        //selfresult: JSON.stringify(selfresult)
                        //localresult: JSON.stringify(localresult)
                        //allresult: JSON.stringify(allresult)
						}
                    }).done(function(obj){
                        if (responseJSON.page == "01") {
                            $('body').injectData(obj);
                        }
                        
                        if (obj.tReCheckData == "Y") {
                            //L140MM1B.tReCheckData=本案屬自用住宅但其風險權數卻選擇100%，提醒！！請再次確認是否無誤！</br>(PS!並非自用住宅其風險權數即為45%，請依個案情況正確判斷！)
                            //API.showMessage(i18n.lms140mm01["L140MM1B.tReCheckData"]);
                        }
                        
                        
                        CommonAPI.triggerOpener("gridview", "reloadGrid");
                        if ($("#mainOid").val()) {
                            setRequiredSave(false);
                        }
                        else {
                            setRequiredSave(true);
                        }
                        
                        // 執行列印
                        if (!showMsg && tofn) {
                            tofn();
                        }
                });
    }
    
    //    
    function flowAction(sendData){
        $.ajax({
            handler: inits.fhandle,
            data: $.extend({
                formAction: "flowAction",
                mainOid: $("#mainOid").val()
            }, (sendData || {}))
			}).done(function(){
                CommonAPI.triggerOpener("gridview", "reloadGrid");
                API.showPopMessage(i18n.def["runSuccess"], window.close);
        });
    }
    var item;
    // 呈主管 - 編製中
    function sendBoss(){
        $.ajax({
            handler: "cls2501m01formhandler",
            action: "checkData",
            data: {}
			}).done(function(json){
                $('#managerItem').empty();
                $('#bossItem').empty();
                item = json.bossList;
                var bhtml = '1. <select id="boss1" name="boss" class="required" CommonManager="kind:2;type:2"></select>';
                $('#bossItem').append(bhtml).find('select').each(function(){
                    $(this).setItems({
                        item: item,
                        format: "{value} {key}"
                    });
                });
                var html = '<select id="manager" name="manager" class="required" CommonManager="kind:2;type:2"></select>';
                $('#managerItem').append(html).find('select').each(function(){
                    $(this).setItems({
                        item: item,
                        format: "{value} {key}"
                    });
                });
                
                // C250M01E.message27=是否呈主管覆核？
                CommonAPI.confirmMessage(i18n.cls2501m01["C250M01E.message27"], function(b){
                    if (b) {
                        $("#selectBossBox").thickbox({
                            // C250M01E.bt14=覆核
                            title: i18n.cls2501m01['C250M01E.bt14'],
                            width: 500,
                            height: 300,
                            modal: true,
                            readOnly: false,
                            valign: "bottom",
                            align: "center",
                            i18n: i18n.def,
                            buttons: {
                                "sure": function(){
                                
                                    var selectBoss = $("select[name^=boss]").map(function(){
                                        return $(this).val();
                                    }).toArray();
                                    
                                    for (var i in selectBoss) {
                                        if (selectBoss[i] == "") {
                                            // C250M01E.error2=請選擇
                                            // C250M01E.bossId=授信主管
                                            return CommonAPI.showErrorMessage(i18n.cls2501m01['C250M01E.error2'] +
                                            i18n.cls2501m01['C250M01E.bossId']);
                                        }
                                    }
                                    if ($("#manager").val() ==
                                    "") {
                                        // C250M01E.managerId=經副襄理
                                        return CommonAPI.showErrorMessage(i18n.cls2501m01['C250M01E.error2'] +
                                        i18n.cls2501m01['C250M01E.managerId']);
                                    }
                                    // 驗證是否有重複的主管
                                    if (checkArrayRepeat(selectBoss)) {
                                        // C250M01E.message31=主管人員名單重複請重新選擇
                                        return CommonAPI.showErrorMessage(i18n.cls2501m01['C250M01E.message31']);
                                    }
                                    
                                    flowAction({
                                        page: responseJSON.page,
                                        saveData: true,
                                        selectBoss: selectBoss,
                                        manager: $("#manager").val()
                                    });
                                    $.thickbox.close();
                                    
                                },
                                
                                "cancel": function(){
                                    $.thickbox.close();
                                }
                            }
                        });
                    }
                });
        });
    }
    
    // 待覆核 - 覆核
    function openCheck(){
        // thickboxOptions.readOnly= false;
        $("#openCheckBox").thickbox({ // 使用選取的內容進行彈窗
            // C250M01E.bt14=覆核
            title: i18n.cls2501m01['C250M01E.bt14'],
            width: 100,
            height: 100,
            modal: true,
            readOnly: false,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                
                    var val = $("[name=checkRadio]:checked").val();
                    if (!val) {
                        return CommonAPI.showMessage(i18n.cls2501m01['C250M01E.error2']);
                    }
                    $.thickbox.close();
                    switch (val) {
                        case "1":
                            // 一般退回到編製中01O
                            // C250M01E.message32=該案件是否退回經辦修改？要退回請按【確定】，不退回請按【取消】
                            CommonAPI.confirmMessage(i18n.cls2501m01['C250M01E.message32'], function(b){
                                if (b) {
                                    flowAction({
                                        flowAction: false
                                    });
                                }
                            });
                            
                            break;
                        case "3":
                            // C250M01E.message34=該案件是否確定執行核定作業
                            CommonAPI.confirmMessage(i18n.cls2501m01["C250M01E.message34"], function(b){
                                if (b) {
                                    checkDate();
                                }
                            });
                            break;
                    }
                    
                },
                
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
    
    // 輸入核定日期視窗
    function checkDate(){
        // 帶入今天日期
        $("#forCheckDate").val(CommonAPI.getToday());
        $("#openChecDatekBox").thickbox({ // 使用選取的內容進行彈窗
            // C250M01E.message38 = 請輸入核定日
            title: i18n.cls2501m01['C250M01E.message38'],
            width: 100,
            height: 100,
            modal: true,
            valign: "bottom",
            align: "center",
            readOnly: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var forCheckDate = $("#forCheckDate").val();
                    if ($.trim(forCheckDate) == "") {
                        // C250M01E.message38 = 請輸入核定日
                        return CommonAPI.showErrorMessage(i18n.cls2501m01['C250M01E.message38']);
                    }
                    flowAction({
                        flowAction: true,
                        checkDate: forCheckDate
                    });
                    $.thickbox.close();
                },
                
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
    // 檢查陣列內容是否重複
    function checkArrayRepeat(arrVal){
        var newArray = [];
        for (var i = arrVal.length; i--;) {
            var val = arrVal[i];
            if ($.inArray(val, newArray) == -1) {
                newArray.push(val);
            }
            else {
                return true;
            }
        }
        return false;
    }
	
	$("#showB29Ejcic").click(function(){

		$.ajax({
            handler: inits.fhandle,
			action: "getEjcicB29InquiryData",
            data: {
				mainId:responseJSON.mainId,
				custId:$("#custId").val(),
				dupNo:$("#dupNo").val()
			}
			}).done(function(json){
                if(json.b29Html != undefined && json.b29Html != ''){
					var w = window.open('', "_blank", "toolbar=1,scrollbars=1,resizable=1");
					var d = w.document.open();
					d.write(json.b29Html);
					d.close();
				} 
        });
		
	});
	
	function loadOverdueDataGrid(){
		var CntrnoGrid = $('#overdueRecordGrid').iGrid({
        handler: inits.ghaddle, //設定handler
        height: 50, //設定高度
        width: 500,
    	autowidth: true,
        action: 'queryOverduePaymentRecord',
        postData: {
        },
        rowNum: 15,
        rownumbers: true,
        colModel: [{
            colHeader: i18n.cls2501v01["C250M01A.accountBranch"], //帳務分行
            align: "left",
            width: 30, //設定寬度
            sortable: true, //是否允許排序
            name: 'accountBranch'
        }, {
            colHeader: i18n.cls2501v01["C250M01A.contractNo"], //額度序號
            align: "left",
            width: 80, //設定寬度
            sortable: true, //是否允許排序
            name: 'contractNo'
        },{
            colHeader: i18n.cls2501v01["C250M01A.account"], //帳號
            name: 'statusDes',
            align: "left",
            width: 80, //設定寬度
            sortable: true, //是否允許排序                
            name: 'account'
        }]
    });
	}
	
    
});


function load_item(){
	var dfd = $.Deferred();
	var $formObject = $("#CLS2501M01Form");
	
	var $div = $formObject.find("[itemType]");
    var allKey = [];
    $div.each(function(){
        allKey.push($(this).attr("itemType"));
    });
    var item = API.loadCombos(allKey);
	var len = $div.length;
    $div.each(function(index, element){
		if (index == len - 1) {
            dfd.resolve();
        }
		
        var $obj = $(this);
        var itemType = $obj.attr("itemType");
        if (itemType) {
            var format = $obj.attr("itemFormat") || "{key}";
            $obj.setItems({
                space: $obj.attr("space") || true,
                item: item[itemType],
                format: format,
                size: $obj.attr("itemSize")
            });
        }
    });
	
	return dfd.promise();	
}
