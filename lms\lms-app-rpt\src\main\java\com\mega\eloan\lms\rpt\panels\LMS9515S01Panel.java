/* 
 * LMS9515S01Panel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.panels;

import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * </pre>
 * 
 * @since 2011/12/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/12/30,jessica,new
 *          </ul>
 */
public class LMS9515S01Panel extends Panel {

	public LMS9515S01Panel(String id) {
		super(id);
	}

	public LMS9515S01Panel(String id, boolean updatePanelName) {
		super(id, updatePanelName);
	}
	
	/**/
	private static final long serialVersionUID = 1L;

}
