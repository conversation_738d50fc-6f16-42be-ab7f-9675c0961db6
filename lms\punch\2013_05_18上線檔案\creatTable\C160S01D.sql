---------------------------------------------------------
-- LMS.C160S01D 動審表匯入明細檔
---------------------------------------------------------


---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.C160S01D;
CREATE TABLE LMS.C160S01D (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)      not null,
	STAFFN<PERSON>       CHAR(6)       not null,
	CUSTID        VARCHAR(11)   not null,
	CUSTNAME      VARCHAR(120) ,
	<PERSON><PERSON><PERSON><PERSON>AM<PERSON>    DECIMAL(15,0),
	MONTH         DECIMAL(2,0) ,
	<PERSON><PERSON><PERSON><PERSON><PERSON>E    DATE         ,
	LNENDDATE     DATE         ,
	PAYWAY        CHAR(1)      ,
	PAYWAYAMT     DECIMAL(15,0),
	AUTORCT       CHAR(01)     ,
	RCTDATE       DATE         ,
	ACCNO         VARCHAR(16)  ,
	AUTOPAY       CHAR(01)     ,
	ATPAYNO       VARCHAR(16)  ,
	CNTRNO        VARCHAR(12)  ,
	RID1          VARCHAR(11)  ,
	RNAME1        VARCHAR(120) ,
	RKINDD1       VARCHAR(2)   ,
	RTYPE1        VARCHAR(1)   ,
	RID2          VARCHAR(11)  ,
	RNAME2        VARCHAR(120) ,
	RKINDD2       VARCHAR(2)   ,
	RTYPE2        VARCHAR(1)   ,
	USEFROMDATE   DATE         ,
	USEENDDATE    DATE         ,
	RESULT        VARCHAR(500) ,
	ANNUITY       DECIMAL(5,0) ,
	OTHINCOME     DECIMAL(5,0) ,
	MISFLAG       CHAR(1)      ,
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_C160S01D PRIMARY KEY(OID)
) IN EL_DATA_4KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XC160S01D01;
CREATE UNIQUE INDEX LMS.XC160S01D01 ON LMS.C160S01D   (MAINID, STAFFNO, CUSTID);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.C160S01D IS '動審表匯入明細檔';
COMMENT ON LMS.C160S01D (
	OID           IS 'oid', 
	MAINID        IS 'mainId', 
	STAFFNO       IS '職員', 
	CUSTID        IS '借款人統編', 
	CUSTNAME      IS '借款人名稱', 
	LOANTOTAMT    IS '授信額度合計金額', 
	MONTH         IS '期限-月', 
	LNFROMDATE    IS '授信期間-起始日期', 
	LNENDDATE     IS '授信期間-截止日期', 
	PAYWAY        IS '償還方式', 
	PAYWAYAMT     IS '每期攤還本金', 
	AUTORCT       IS '自動進帳', 
	RCTDATE       IS '進帳日期', 
	ACCNO         IS '進帳帳號', 
	AUTOPAY       IS '自動扣帳', 
	ATPAYNO       IS '扣款帳號', 
	CNTRNO        IS '額度序號', 
	RID1          IS '從債務人統編1', 
	RNAME1        IS '從債務人名稱1', 
	RKINDD1       IS '關係類別細項1', 
	RTYPE1        IS '相關身份1', 
	RID2          IS '從債務人統編2', 
	RNAME2        IS '從債務人名稱2', 
	RKINDD2       IS '關係類別細項2', 
	RTYPE2        IS '相關身份2', 
	USEFROMDATE   IS '動用期間-起始日期', 
	USEENDDATE    IS '動用期間-截止日期', 
	RESULT        IS '結果', 
	ANNUITY       IS '年薪', 
	OTHINCOME     IS '其他所得', 
	MISFLAG       IS '是否已上傳MIS', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);