package com.mega.eloan.lms.cls.handler.form;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.service.UserInfoService.SignEnum;
import com.mega.eloan.common.service.impl.BranchServiceImpl;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.cls.pages.CLS1021M01Page;
import com.mega.eloan.lms.cls.service.CLS1021Service;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.mfaloan.service.MisStoredProcService;
import com.mega.eloan.lms.model.C102M01A;
import com.mega.eloan.lms.model.C102M01B;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.iisi.cap.utils.CapWebUtil;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.core.FlowException;

/**
 * <pre>
 * 購置房屋擔保放款風險權數檢核表(自用住宅貸款檢核表)
 * </pre>
 * 
 * @since 2013/01/07
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/01/07,GaryChang,new</li>
 *          <li>2022/06,EL08034,J-111-0096 Web e-Loan消金因應不動產暴險以貸放比率(LTV)決定適用之風險權數，報表名稱改為「自用住宅貸款檢核表」</li>
 *          </ul>
 */
@Scope("request")
@Controller("cls1021m01formhandler")
@DomainClass(C102M01A.class)
public class CLS1021M01FormHandler extends AbstractFormHandler {

	private static final Logger LOGGER = LoggerFactory.getLogger(CLS1021M01FormHandler.class);
	
	@Resource
	MisStoredProcService misStoredProcService;

	@Resource
	MisCustdataService misCustdataService;

	@Resource
	CLSService clsService;
	
	@Resource
	LMSService lmsService;

	@Resource
	BranchService branchService;

	@Resource
	CLS1021Service cls1021service;

	@Resource
	UserInfoService userInfoService;

	Properties prop_cls1021m01 = MessageBundleScriptCreator.getComponentResource(CLS1021M01Page.class);

	/**
	 * 新增C102M01A 購置房屋擔保放款風險權數檢核表
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult newc102m01a(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		C102M01A c102m01a = new C102M01A();
		c102m01a.setDocStatus(CreditDocStatusEnum.海外_編製中.getCode());
		c102m01a.setOwnBrId(user.getUnitNo());
		c102m01a.setMainId(IDGenerator.getUUID());
		String txCode = Util.trim(params
				.getString(EloanConstants.TRANSACTION_CODE));
		c102m01a.setTxCode(txCode);
		c102m01a.setDocURL(CapWebUtil.getDocUrl(CLS1021M01Page.class));
		c102m01a.setDeletedTime(CapDate.getCurrentTimestamp());
		c102m01a.setRptId(LMSUtil.get_C102M01A_RPTID_latestVersion());
		cls1021service.save(c102m01a);
		CapAjaxFormResult result = new CapAjaxFormResult();
		inject_title(result, c102m01a.getRptId());
		return result.set(EloanConstants.OID, c102m01a.getOid());
	}

	private String valid_rptId(C102M01A c102m01a){
		if(Util.equals(c102m01a.getDocStatus(), CreditDocStatusEnum.海外_編製中)){
			if(Util.equals(Util.trim(c102m01a.getRptId()), LMSUtil.get_C102M01A_RPTID_latestVersion())){
				//ok
			}else{
				return ("「"+prop_cls1021m01.getProperty("C102M01A.rptId")+"」"
						+"之最新版本應為 "+LMSUtil.get_C102M01A_RPTID_latestVersion()+"。請重新產生文件。");
			}
		}
		return "";
	}
	/**
	 * 查詢C102M01A 購置房屋擔保放款風險權數檢核表
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryC102m01a(PageParameters params) throws CapException {
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);
		if (!Util.isEmpty(oid)) {
			C102M01A c102m01a = clsService.findC102M01A_oid(oid);
			//輸出到前端
			result = formatResultShow(result, c102m01a, page);
			inject_title(result, c102m01a.getRptId());
			
			String msg = valid_rptId(c102m01a);
			if(Util.isNotEmpty(msg)){
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, msg);	
			}			
		} else {
			// 開啟新案帶入起案的分行和目前文件狀態
			result.set(
					"docStatus",
					this.getMessage("docStatus."
							+ CreditDocStatusEnum.海外_編製中.getCode()));
			result.set("ownBrId", user.getUnitNo());
			result.set(
					"ownBrName",
					StrUtils.concat(" ",
							branchService.getBranchName(user.getUnitNo())));
			result.set("docStatusVal", CreditDocStatusEnum.海外_編製中.getCode());
		}
		return result;
	}

	private void inject_title(CapAjaxFormResult result, String c102m01a_rptId){
		//C102M01A.title01=購置房屋擔保放款風險權數檢核表
		//C102M01A.title01.V202208=自用住宅貸款檢核表
		
		String title = prop_cls1021m01.getProperty("C102M01A.title01");
		if(Util.equals(c102m01a_rptId, LMSUtil.C102M01A_RPTID_V202208)){
			title = prop_cls1021m01.getProperty("C102M01A.title01.V202208");
        }else if(Util.equals(c102m01a_rptId, LMSUtil.C102M01A_RPTID_V20171231)){
    		// title 仍照舊  display 購置房屋擔保放款風險權數檢核表                 		
    	}
		result.set("title", title);  
	}
	
	 /**
	 * 呈主管覆核(呈主管 主管覆核 拆2個method)
	 *
	 * <pre>
	 * @param params PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@SuppressWarnings({ "unchecked" })
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult flowAction(PageParameters params) throws CapException {
		// 儲存and檢核
		String oid = params.getString(EloanConstants.MAIN_OID);
		C102M01A c102m01a = clsService.findC102M01A_oid(oid);
		String[] formSelectBoss = params.getStringArray("selectBoss");	
		
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();	
		
		if (formSelectBoss != null && formSelectBoss.length > 0) {		
			
			String manager = Util.trim(params.getString("manager"));
			List<C102M01B> models = (List<C102M01B>) cls1021service
					.findListByMainId(C102M01B.class, c102m01a.getMainId());
			if (!models.isEmpty()) {
				cls1021service.deleteC102m01bs(models, false);
			}
			List<C102M01B> c102m01bs = new ArrayList<C102M01B>();
			for (String people : formSelectBoss) {
				C102M01B c102m01b = new C102M01B();
				c102m01b.setCreator(user.getUserId());
				c102m01b.setCreateTime(CapDate.getCurrentTimestamp());
				c102m01b.setMainId(c102m01a.getMainId());
				c102m01b.setBranchType(user.getUnitType());
				c102m01b.setBranchId(user.getUnitNo());
				// L1. 分行經辦 L3. 分行授信主管 L4. 分行覆核主管 L5. 經副襄理
				c102m01b.setStaffJob(UtilConstants.STAFFJOB.授信主管L3);
				c102m01b.setStaffNo(people);
				c102m01bs.add(c102m01b);
			}
			C102M01B managerC102m01b = new C102M01B();
			managerC102m01b.setCreator(user.getUserId());
			managerC102m01b.setCreateTime(CapDate.getCurrentTimestamp());
			managerC102m01b.setMainId(c102m01a.getMainId());
			managerC102m01b.setStaffJob(UtilConstants.STAFFJOB.單位授權主管L5);
			managerC102m01b.setStaffNo(manager);
			managerC102m01b.setBranchType(user.getUnitType());
			managerC102m01b.setBranchId(user.getUnitNo());
			c102m01bs.add(managerC102m01b);
			C102M01B apprC102m01b = new C102M01B();
			apprC102m01b.setCreator(user.getUserId());
			apprC102m01b.setCreateTime(CapDate.getCurrentTimestamp());
			apprC102m01b.setMainId(c102m01a.getMainId());
			apprC102m01b.setStaffJob(UtilConstants.STAFFJOB.經辦L1);
			apprC102m01b.setStaffNo(user.getUserId());
			apprC102m01b.setBranchType(user.getUnitType());
			apprC102m01b.setBranchId(user.getUnitNo());
			c102m01bs.add(apprC102m01b);
			cls1021service.saveC102m01bList(c102m01bs);
		}
		Boolean upMis = false;
		C102M01B c102m01bL4 = new C102M01B();
		// 如果有這個key值表示是輸入chekDate核准日期
		if (params.containsKey("checkDate")) {
			c102m01a.setApprover(user.getUserId());
			c102m01a.setApproveTime(CapDate.convertStringToTimestamp(params
					.getString("checkDate") + " 00:00:00"));
			upMis = true;
			C102M01B c102m01b = cls1021service.findC102m01b(
					c102m01a.getMainId(), user.getUnitType(), user.getUnitNo(),
					user.getUserId(), UtilConstants.STAFFJOB.執行覆核主管L4);
			if (c102m01b == null) {
				c102m01b = new C102M01B();
				c102m01b.setCreator(user.getUserId());
				c102m01b.setCreateTime(CapDate.getCurrentTimestamp());
				c102m01b.setMainId(c102m01a.getMainId());
				c102m01b.setStaffJob(UtilConstants.STAFFJOB.執行覆核主管L4);
				c102m01b.setStaffNo(user.getUserId());
				c102m01b.setBranchType(user.getUnitType());
				c102m01b.setBranchId(user.getUnitNo());
			}
			c102m01bL4 = c102m01b;
		}

		if (!Util.isEmpty(c102m01a)) {
			try {
				// 如果有這值表示非呈主管，要檢查覆核主管和文件最後更新者是否相同
				if (params.containsKey("flowAction")) {
					// 退回部檢查
					if (params.getBoolean("flowAction")) {
						C102M01B c102m01b = cls1021service.findC102m01b(
								c102m01a.getMainId(), user.getUnitType(),
								user.getUnitNo(), user.getUserId(),
								UtilConstants.STAFFJOB.經辦L1);
						
						if (c102m01b != null) {
//							 EFD0053=WARN|覆核人員不可與「經辦人員或其它覆核人員」為同一人|
							throw new CapMessageException(RespMsgHelper.getMessage("EFD0053"), getClass());
						} else {
							cls1021service.save(c102m01bL4);
							upMis = true;
						}
					}
				}
				cls1021service.flowAction(c102m01a.getOid(), c102m01a,
						params.containsKey("flowAction"),
						params.getAsBoolean("flowAction", false), upMis);
			} catch (FlowException t1) {
				logger.error(
						"[flowAction] cls1021Service.flowAction FlowException!!",
						t1);
				throw new CapMessageException(RespMsgHelper.getMessage(t1.getMessage()), getClass());
			} catch (Throwable t1) {
				logger.error(
						"[flowAction]  cls1021Service.flowAction EXCEPTION!!",
						t1);
				throw new CapMessageException(t1.getMessage(), getClass());
			}
		}

		return new CapAjaxFormResult();
	}

	/**
	 * 檢核資料是否已經有正確的登錄
	 * 
	 * <pre>
	 * @param params PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult checkData(PageParameters params) throws CapException {
		if(true){
			C102M01A c102m01a = clsService.findC102M01A_oid(Util.trim(params.getString(EloanConstants.OID)));
			String msg = valid_rptId(c102m01a);
			if(Util.isNotEmpty(msg)){
				throw new CapMessageException(msg, getClass());
			}			
		}
		
		// 儲存and檢核
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 查詢所選銀行的甲級主管、乙級主管清單
		SignEnum[] signs = { SignEnum.首長, SignEnum.單位主管, SignEnum.甲級主管,
				SignEnum.乙級主管 };
		Map<String, String> bossList = userInfoService.findByBrnoAndSignId(
				user.getUnitNo(), signs);
		result.set("bossList", new CapAjaxFormResult(bossList));
		return result;

	}

	/**
	 * 儲存C102M01A 購置房屋擔保放款風險權數檢核表
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveC102m01a(PageParameters params) throws CapException {
		// lmsService.uploadELLNSEEK(new L120M01A());
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", "N"));
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		String oid = Util.trim(params.getString(EloanConstants.MAIN_OID));
		String custName=Util.trim(params.getString("custName"));
		String aLoanAC=Util.trim(params.getString("aLoanAC"));
		String aLoanDate=Util.trim(params.getString("aLoanDate"));
		String formL160m01a = Util.trim(params.getString("C102M01AForm")); // 指定的form
		JSONObject jsonL160m01a = null;
		C102M01A c102m01a = null;
		Boolean showMsg = params.getAsBoolean("showMsg", false);
		if (Util.isEmpty(oid)) {
			c102m01a = new C102M01A();
			c102m01a.setDocStatus(CreditDocStatusEnum.海外_編製中.getCode());
			c102m01a.setOwnBrId(user.getUnitNo());
			c102m01a.setMainId(IDGenerator.getUUID());
			String txCode = Util.trim(params
					.getString(EloanConstants.TRANSACTION_CODE));
			c102m01a.setTxCode(txCode);
			c102m01a.setDocURL(CapWebUtil.getDocUrl(CLS1021M01Page.class));
			c102m01a.setRptId(LMSUtil.get_C102M01A_RPTID_latestVersion());
		} else {
			c102m01a = clsService.findC102M01A_oid(oid);
			c102m01a.setRandomCode(IDGenerator.getRandomCode());
		}
		
		c102m01a.setDeletedTime(null);

		String validate = null;
		switch (page) {
		case 1:
			jsonL160m01a = JSONObject.fromObject(formL160m01a);
			DataParse.toBean(jsonL160m01a, c102m01a);
			validate = Util.validateColumnSize(c102m01a, prop_cls1021m01, "C102M01A");
			if (validate != null) {
				Map<String, String> param = new HashMap<String, String>();
				param.put("colName", validate);
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
			}
			if (Util.equals(c102m01a.getChk11(), "Y")
					&& Util.equals(c102m01a.getChk12(), "Y")
					&& Util.equals(c102m01a.getChk13(), "Y")
					&& Util.equals(c102m01a.getChk14(), "Y")) {
				c102m01a.setSelfChk("Y");
			} else {
				c102m01a.setSelfChk("N");
			}

//			if (lmsService.isValidTWPID(c102m01a.getCustId())) {
//				c102m01a.setTypCd(UtilConstants.Casedoc.typCd.OBU);
//			} else if (lmsService.isValidTWBID(c102m01a.getCustId())) {
			if(true) {
				c102m01a.setTypCd(UtilConstants.Casedoc.typCd.DBU);
			}
			c102m01a.setCustName(custName);
			c102m01a.setALoanAC(aLoanAC);
			c102m01a.setALoanDate(CapDate.parseSQLDate(Util.getDate(aLoanDate)));
			cls1021service.save(c102m01a);
			result.set("randomCode", c102m01a.getRandomCode());
			
			// 是否為自用住宅(Y)  本案適用於風險權數(2->100%)
			// tReCheckData=='Y' 時，在 【CLS1021M01Page.js】 會出現提示訊息
			// (比例較少的案件, 自用住宅=Y, 卻選擇較高的風險權數 => 增加提示, 避免分行誤勾選) 
			if(true){
				String tReCheckDataMsg = "";
				
				Map<String, String> check_map = LMSUtil.get_C102M01A_checkMsg(c102m01a.getRptId(), Util.trim(c102m01a.getSelfChk()) 
						, c102m01a.getALoanDate(), Util.trim(c102m01a.getRskFlag())
						, prop_cls1021m01, "<br/>");
				String errMsg = Util.trim(MapUtils.getString(check_map, "E"));
				String cmfMsg = Util.trim(MapUtils.getString(check_map, "C"));
				if(Util.isNotEmpty(errMsg)){
					throw new CapMessageException(errMsg, getClass());
				}else{
					if(Util.isNotEmpty(cmfMsg)){
						tReCheckDataMsg = cmfMsg;
					}
				}
				
				if(Util.isNotEmpty(tReCheckDataMsg)){
					result.set("tReCheckDataMsg", tReCheckDataMsg);
				}
			}
			
			break;
		}
		if (showMsg) {
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
					.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));
		}

		result.set(EloanConstants.OID, CapString.trimNull(c102m01a.getOid()));
		result.set(EloanConstants.MAIN_OID,
				CapString.trimNull(c102m01a.getOid()));
		result.set(EloanConstants.MAIN_ID,
				CapString.trimNull(c102m01a.getMainId()));
		result.set("showTypCd",
				this.getMessage("typCd." + c102m01a.getTypCd()));
		result.set("showCustId",
				CapString.trimNull(c102m01a.getCustId())+" "+CapString.trimNull(c102m01a.getDupNo())+" "+CapString.trimNull(c102m01a.getCustName()));
		return result;
	}

	/**
	 * 格式化顯示訊息
	 * 
	 * @param mainId
	 *            購置房屋擔保放款風險權數檢核表mainId
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	private CapAjaxFormResult formatResultShow(CapAjaxFormResult result,
			C102M01A c102m01a, Integer page) throws CapException {
		String mainId = c102m01a.getMainId();

		switch (page) {
		case 1:
			result = DataParse.toResult(c102m01a);
			List<C102M01B> c102m01blist = (List<C102M01B>) cls1021service
					.findListByMainId(C102M01B.class, mainId);
			if (!Util.isEmpty(c102m01blist)) {
				// 取得人員職稱 L1. 分行經辦 L3. 分行授信主管 L4. 分行覆核主管 L5. 經副襄理L6. 總行經辦
				// L7.總行主管
				StringBuilder bossId = new StringBuilder("");
				for (C102M01B c102m01b : c102m01blist) {
					// 要加上人員代碼
					String type = Util.trim(c102m01b.getStaffJob());
					String userId = Util.trim(c102m01b.getStaffNo());
					String value = Util.trim(lmsService.getUserName(userId));
					if ("L1".equals(type)) {
						result.set("showApprId", userId + " " + value);
					} else if ("L3".equals(type)) {
						bossId.append(bossId.length() > 0 ? "<br/>" : "");
						bossId.append(userId);
						bossId.append(" ");
						bossId.append(value);
					} else if ("L4".equals(type)) {
						result.set("reCheckId", userId + " " + value);
					} else if ("L5".equals(type)) {
						result.set("managerId", userId + " " + value);
					} else if ("L6".equals(type)) {
						result.set("mainApprId", userId + " " + value);
					} else if ("L7".equals(type)) {
						result.set("mainReCheckId", userId + " " + value);
					}
				}
				result.set("bossId", bossId.toString());
			}
			result.set("ownBrName",
					" " + branchService.getBranchName(c102m01a.getOwnBrId()));

			StringBuilder cntrNo = new StringBuilder("");

			result.set("creator", lmsService.getUserName(c102m01a.getCreator()));
			result.set("updater", lmsService.getUserName(c102m01a.getUpdater()));
			result.set("docStatus",
					getMessage("docStatus." + c102m01a.getDocStatus()));
			result.set("cntrNo", cntrNo.toString());
			break;
		}// close switch case
		result.set("docStatusVal", c102m01a.getDocStatus());
		if (!Util.isEmpty(c102m01a.getCustId())) {
			result.set("typCd", this.getMessage("typCd." + c102m01a.getTypCd()));
			result.set("showTypCd",
					this.getMessage("typCd." + c102m01a.getTypCd()));
			result.set("showCustId", StrUtils.concat(c102m01a.getCustId()
					.toUpperCase(), " ", c102m01a.getDupNo().toUpperCase(),
					" ", c102m01a.getCustName()));
		}
		result.set("cntrNo", c102m01a.getCntrNo());
		result.set("randomCode", c102m01a.getRandomCode());
		result.set("rptId", Util.trim(c102m01a.getRptId()));
		
		
		result.set(EloanConstants.OID, CapString.trimNull(c102m01a.getOid()));
		result.set(EloanConstants.MAIN_OID,
				CapString.trimNull(c102m01a.getOid()));
		result.set(EloanConstants.MAIN_ID,
				CapString.trimNull(c102m01a.getMainId()));
		return result;
	}

	/**
	 * 刪除C102M01A 購置房屋擔保放款風險權數檢核表
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteC102m01a(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] oids = params.getStringArray("oids");
		LOGGER.debug("印出oids:"+oids);
		if (oids.length > 0) {
			if (cls1021service.deleteC102m01as(oids)) {
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
						.getMainMessage(UtilConstants.AJAX_RSP_MSG.刪除成功));
			}
		}
		return result;
	}

}
