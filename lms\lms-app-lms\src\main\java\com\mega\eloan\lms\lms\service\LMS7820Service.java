package com.mega.eloan.lms.lms.service;

/* 
 * LMS7820Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
import java.util.List;

import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.model.L782M01A;

/**
 * <pre>
 * 特殊登錄案件紀錄表
 * </pre>
 * 
 * @since 2011/12/8
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/12/8,REX,new
 *          </ul>
 */
public interface LMS7820Service extends AbstractService {

	/**
	 * 找出所有特殊登錄案件以提供列印
	 * 
	 * @return 特殊登錄案件紀錄表
	 */
	List<L782M01A> findByAll(String ownBrId,String releaseDateS,String releaseDateE);

}