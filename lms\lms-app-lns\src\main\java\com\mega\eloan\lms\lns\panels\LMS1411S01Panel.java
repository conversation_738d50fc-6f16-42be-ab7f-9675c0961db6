/* 
 * LMS1415S01Panel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lns.panels;

import com.mega.eloan.common.panels.Panel;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.DocLogPanel;

/**
 * <pre>
 * 聯行額度明細表 - 文件資訊
 * </pre>
 * 
 * @since 2011/12/1
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/12/1,REX,new
 *          </ul>
 */
public class LMS1411S01Panel extends Panel {

	private static final long serialVersionUID = -4024257163623646201L;

	public LMS1411S01Panel(String id) {
		super(id);
	}
	
	public LMS1411S01Panel(String id, boolean updatePanelName) {
		super(id, updatePanelName);

	}
	
	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);
		
		new DocLogPanel("_docLog").processPanelData(model, params);
	}
}
