/* 
 * L120S17ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S17A;

/** 授權外案件徵授信進度時程檔 **/
public interface L120S17ADao extends IGenericDao<L120S17A> {

	L120S17A findByOid(String oid);

	L120S17A findByMainId(String mainId);

	L120S17A findByIndex01(String mainId);
}