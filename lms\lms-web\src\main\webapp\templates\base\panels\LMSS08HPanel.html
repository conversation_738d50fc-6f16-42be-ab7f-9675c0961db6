<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="panelFragmentBody">
	            <tr class="lmss08jk_button">
	                <th class="hd2">
	                    <th:block th:text="#{'L120S08H.title'}">額度檢視表<s/th:block>
	                </th>
	                <td class="left2">
	                    <button id="lmss08h_generate" type="button">
	                        <span class="text-only"><span><th:block th:text="#{'L120S08H.bt04'}">產生</th:block></span></span>
	                    </button>
	                     
						 <button id="lmss08h_viewOldCase" type="button">
	                        <span class="text-only"><span><th:block th:text="#{'L120S08H.bt05'}">調閱</th:block></span></span>
	                    </button>
	                </td>
	                <td>
	                    <form id="formfile">
	                    	<div class="funcContainer">
								 
		                        <button type="button" id="lmss08h_uploadFile">
		                            <span class="text-only"><th:block th:text="#{'L120S08H.bt01'}"><!-- 選擇附加檔案--></th:block></span>
		                        </button>
		                        <button type="button" id="lmss08h_deleteFile">
		                            <span class="text-only"><th:block th:text="#{'L120S08H.bt02'}"><!-- 刪除--></th:block></span>
		                        </button>
								 
								<div id="lmss08h_gridfile"></div>
		                    </div>
						</form>
	                </td>
	            </tr>
        </th:block>
    </body>
</html>
