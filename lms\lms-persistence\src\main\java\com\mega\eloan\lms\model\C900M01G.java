/* 
 * C900M01G.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 團貸分戶明細檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C900M01G", uniqueConstraints = @UniqueConstraint(columnNames = {"cntrNo","GRPCNTRNO"}))
public class C900M01G extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 身分證統編 **/
	@Size(max=10)
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Size(max=1)
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 分戶額度序號 **/
	@Size(max=12)
	@Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)")
	private String cntrNo;

	/** 團貸總戶序號 **/
	@Size(max=12)
	@Column(name="GRPCNTRNO", length=12, columnDefinition="CHAR(12)")
	private String grpcntrno;
	
	/** 狀態{1:編製中, 2:簽報書覆核, 3:動審} **/
	@Size(max=1)
	@Column(name="STATUS", length=1, columnDefinition="CHAR(1)")
	private String status;

	/** 申請幣別 **/
	@Size(max=3)
	@Column(name="APPLYCURR", length=3, columnDefinition="CHAR(3)")
	private String ApplyCurr;

	/** 申請金額 **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="APPLYAMT", columnDefinition="DECIMAL(17,2)")
	private BigDecimal ApplyAmt;

	/** 核准幣別 **/
	@Size(max=3)
	@Column(name="APPROVECURR", length=3, columnDefinition="CHAR(3)")
	private String approveCurr;

	/** 核准金額 **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="APPROVEAMT", columnDefinition="DECIMAL(17,2)")
	private BigDecimal approveAmt;

	/** 動用幣別 **/
	@Size(max=3)
	@Column(name="LOANCURR", length=3, columnDefinition="CHAR(3)")
	private String LoanCurr;

	/** 動用金額 **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="LOANAMT", columnDefinition="DECIMAL(17,2)")
	private BigDecimal LoanAmt;

	/** 核定日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="APPROVEDATE", columnDefinition="DATE")
	private Date approveDate;

	/** 動用日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="LOANDATE", columnDefinition="DATE")
	private Date LoanDate;

	/** 不再動用{D:刪除} **/
	@Size(max=1)
	@Column(name="USEFLAG", length=1, columnDefinition="CHAR(1)")
	private String UseFlag;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 首撥日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="LNF_LOAN_DATE", columnDefinition="DATE")
	private Date lnf_loan_date;
	
	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得身分證統編 **/
	public String getCustId() {
		return this.custId;
	}
	/** 設定身分證統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得身分證統編重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}
	/** 設定身分證統編重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得分戶額度序號 **/
	public String getCntrNo() {
		return this.cntrNo;
	}
	/** 設定分戶額度序號 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/** 取得團貸總戶序號 **/
	public String getGrpcntrno() {
		return this.grpcntrno;
	}
	/** 設定團貸總戶序號 **/
	public void setGrpcntrno(String value) {
		this.grpcntrno = value;
	}
	
	/** 設定狀態{1:編製中, 2:簽報書覆核, 3:動審} **/
	public void setStatus(String status) {
		this.status = status;
	}
	
	/** 取得狀態{1:編製中, 2:簽報書覆核, 3:動審} **/
	public String getStatus() {
		return status;
	}

	/** 取得申請幣別 **/
	public String getApplyCurr() {
		return this.ApplyCurr;
	}
	/** 設定申請幣別 **/
	public void setApplyCurr(String value) {
		this.ApplyCurr = value;
	}

	/** 取得申請金額 **/
	public BigDecimal getApplyAmt() {
		return this.ApplyAmt;
	}
	/** 設定申請金額 **/
	public void setApplyAmt(BigDecimal value) {
		this.ApplyAmt = value;
	}

	/** 取得核准幣別 **/
	public String getApproveCurr() {
		return this.approveCurr;
	}
	/** 設定核准幣別 **/
	public void setApproveCurr(String value) {
		this.approveCurr = value;
	}

	/** 取得核准金額 **/
	public BigDecimal getApproveAmt() {
		return this.approveAmt;
	}
	/** 設定核准金額 **/
	public void setApproveAmt(BigDecimal value) {
		this.approveAmt = value;
	}

	/** 取得動用幣別 **/
	public String getLoanCurr() {
		return this.LoanCurr;
	}
	/** 設定動用幣別 **/
	public void setLoanCurr(String value) {
		this.LoanCurr = value;
	}

	/** 取得動用金額 **/
	public BigDecimal getLoanAmt() {
		return this.LoanAmt;
	}
	/** 設定動用金額 **/
	public void setLoanAmt(BigDecimal value) {
		this.LoanAmt = value;
	}

	/** 取得核定日期 **/
	public Date getApproveDate() {
		return this.approveDate;
	}
	/** 設定核定日期 **/
	public void setApproveDate(Date value) {
		this.approveDate = value;
	}

	/** 取得動用日期 **/
	public Date getLoanDate() {
		return this.LoanDate;
	}
	/** 設定動用日期 **/
	public void setLoanDate(Date value) {
		this.LoanDate = value;
	}

	/** 取得不再動用{D:刪除} **/
	public String getUseFlag() {
		return this.UseFlag;
	}
	/** 設定不再動用{D:刪除} **/
	public void setUseFlag(String value) {
		this.UseFlag = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 取得首撥日 **/
	public Date getLnf_loan_date() {
		return this.lnf_loan_date;
	}
	/** 設定首撥日 **/
	public void setLnf_loan_date(Date value) {
		this.lnf_loan_date = value;
	}
}
