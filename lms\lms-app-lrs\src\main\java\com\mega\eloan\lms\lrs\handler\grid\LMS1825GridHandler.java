/* 
 * LMS1825GridHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lrs.handler.grid;

import java.util.List;

import javax.annotation.Resource;

import com.iisigroup.cap.component.PageParameters;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.lrs.service.LMS1825Service;
import com.mega.eloan.lms.model.L182M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**<pre>
 * 預約產生名單
 * </pre>
 * @since  2011/9/21
 * <AUTHOR>
 * @version <ul>
 *           <li>2011/9/21,irene,new
 *          </ul>
 */
@Scope("request")
@Controller("lms1825gridhandler")
public class LMS1825GridHandler extends AbstractGridHandler {

	@Resource
	LMS1825Service service;
	
	@Resource
	UserInfoService userservice;
	
	/**
	 * 查詢Grid 覆審名單檔 資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	public CapGridResult query(ISearch pageSetting, PageParameters params) throws CapException {

		// 建立主要Search 條件
		ISearch search = createSearchTemplete();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String docStatus = Util.nullToSpace(params.getString(EloanConstants.DOC_STATUS));
		search.addSearchModeParameters(SearchMode.EQUALS, EloanConstants.DOC_STATUS,
				docStatus);
		search.addSearchModeParameters(SearchMode.EQUALS, "l182a01a.authUnit",
				user.getUnitNo());
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",null);
		pageSetting.addSearchModeParameters(search);
		
		Page page = service.findPage(L182M01A.class,pageSetting);
		List<L182M01A> list = page.getContent();
		for(L182M01A l182m01a : list){
			if (!Util.isEmpty(l182m01a.getUpdater())) {
				l182m01a.setUpdater(!Util.isEmpty(userservice.getUserName(l182m01a
						.getUpdater())) ? userservice.getUserName(l182m01a
						.getUpdater()) : l182m01a.getUpdater());
			}
		}
//		service.test();
		
		return new CapGridResult(list, page.getTotalRow());
	}

}
