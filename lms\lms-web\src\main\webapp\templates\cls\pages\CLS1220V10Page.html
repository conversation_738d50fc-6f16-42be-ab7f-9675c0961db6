<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:th="http://www.thymeleaf.org">
<body>
	<th:block th:fragment="innerPageBody">
		
		<div id='div_applyKindE_History' style='display:none;' >
			<form id='div_applyKindE_History_form'>
				<table class='tb2' width='95%' >
					<tr>
				  		<td class='hd2' width='20%' nowrap><span id="div_applyKindE_History_label_custId"></span> </td>				 
				  		<td width='80%'>
				  		<input type='text' id='search_custId' name='search_custId' maxlength='10' size='10' />
							&nbsp;&nbsp;&nbsp;&nbsp;
							<button type="button" id='filter_historyBtn'>
								<span class='text-only'><th:block th:text="#{'button.search'}"></th:block></span>
							</button>
				  		</td>						
					</tr>
					
				</table>
			</form>
			<div id='grid_applyKindE_History'></div>
		</div>
		<!-- J-112-0184 配合消金業務處程式修改申請(112) 第 1163 號，E-LOAN系統新增引介分行查詢功能 -->
		<div id='div_case_progress' style='display:none;' >
			<form id='filer_case_progress_form'>
				<table class='tb2' width='95%' >
					<tr>
                    	<td class='hd2' width='25%' nowrap>
                        	<b><th:block th:text="#{'case_progress.enterDataDate'}"><!--進件日期--></th:block></b> 
                        </td>
						<td>
							<input type="text" maxlength="10" size="10" class="date" name="tx_applyTS_start" id="tx_applyTS_start">
							~
							<input type="text" maxlength="10" size="10" class="date" name="tx_applyTS_end" id="tx_applyTS_end">
                            <span class="text-red">(YYYY-MM)</span>
						</td>	
                    </tr>
					<tr>
						<td class='hd2' nowrap>
							<th:block th:text="#{'C122M01A.custId'}"><!--身分證統編--></th:block>
						</td>				 
				  		<td>
							<input type='text' id='custId' name='custId' maxlength='10' size='10' />
						</td>
					</tr>
					<tr>
                    	<td class='hd2'>
                        	<th:block th:text="#{'case_progress.chooseBank'}"><!--引介分行--></th:block>
                        </td>
                        <td>
                        	<select id="brIdFilter"></select>
                        </td>
                    </tr>
					<tr>
						<td class="hd2">
                        	<th:block th:text="#{'case_progress.signMegaEmpName'}"><!--引介人員--></th:block> 
						</td>
						<td>
							<div id="SignDiv">
								<select id="introduceMegaEmp" name="introduceMegaEmp" class="boss"></select> 
							</div>
						</td>
					</tr>
					<tr>
						<td class='hd2'nowrap>
							<th:block th:text="#{'C122M01A.ploanCaseNo'}"><!--案件編號--></th:block>
						</td>
						<td>
							<input type='text' id='ploanCaseId' name='ploanCaseId' maxlength='30' size='30' />
						</td>	
					</tr>
				</table>
			</form>
			<span class='text-red'><th:block th:text="#{'case_progress.warnnMsg'}"><!--※此引介報表僅供參考，相關引介人員及引介分行計績，以消金處發函為主。--></th:block></span>
			<div id='grid_case_progress'></div>
		</div>
		<div id='div_changeOwnBrId_memo' style='display:none;' >
			<form id='div_changeOwnBrId_memo_form'>
				<table class='tb2' width='98%' >
					<tr>
				  		<td class='hd2' style='width:100px; vertical-align:top;' nowrap>
							<th:block th:text="#{'changeOwnBrId_memo'}">改分派備註</th:block> 
						</td>				 
				  		<td style='padding-right:12px; '>
				  			<textarea id="memo" name="memo" class="required " style="width:100%;height:140px" maxlength='900' maxlengthC='300' >
							</textarea>				 
						</td>				 
					</tr>					
				</table>
				<span class='text-red'>※輸入的備註，將會通知「對方分行」</span>
			</form>
		</div>
		
		
		<div id="C122M01A_CreateDiv" style="display:none">
            <form id="C122M01A_CreateForm">
            	<table class="tb2"  style="width:550px;">
					<tr>
						<td class="hd2 rt" style="width:100px;"><!-- hd1的 width 15% -->
                              	<th:block th:text="#{'C122M01A.applyKind'}"><!--案件類型--></th:block> 
						</td>
						<td>
							<input type='radio' id='ByType' name='ByType' value='E' checked="true"><th:block th:text="#{'C122M01A.applyKind.E2'}"><!--房貸--></th:block> 
							<input type='radio' id='ByType' name='ByType' value='P'><th:block th:text="#{'C122M01A.applyKind.P2'}"><!--信貸--></th:block>
							<input type='radio' id='ByType' name='ByType' value='IJ'><th:block th:text="#{'C122M01A.applyKind.IJ'}"><!--青創一百萬以下--></th:block>
							<input type='radio' id='ByType' name='ByType' value='O'><th:block th:text="#{'C122M01A.applyKind.O'}"><!--其他--></th:block>
						</td>
					</tr>
						
					<tr id="purposeTypeTr" name="purposeTypeTr">
						<td class="hd2 rt" style="width:100px;"><!-- hd1的 width 15% -->
                              	<th:block th:text="#{'C122M01A.purposeType_2'}"><!--類型--></th:block> 
						</td>
						<td>
							<input type='radio' id='purposeType' name='purposeType' value='1' checked="true"><th:block th:text="#{'C122M01A.applyKind.E.NEW'}"><!--新案--></th:block> 
							<input type='radio' id='purposeType' name='purposeType' value='2'><th:block th:text="#{'C122M01A.applyKind.E.RENEW_N'}"><!--續約/變更條件--></th:block>
						</td>
					</tr>
					<tr id="purchaseHouseTr" name="purchaseHouseTr">
						<td class="hd2 rt" style="width:100px;"><!-- hd1的 width 15% -->
                              	<th:block th:text="#{'C122M01A.purchaseHouse'}"><!--是否為購屋--></th:block> 
						</td>
						<td>
							<input type='radio' id='purchaseHouse' name='purchaseHouse' value='N'><th:block th:text="#{'C122M01A.No'}"><!--否--></th:block> 
							<input type='radio' id='purchaseHouse' name='purchaseHouse' value='Y' checked="true"><th:block th:text="#{'C122M01A.Yes'}"><!--是--></th:block>
						</td>
					</tr>
					
					<tr id="youngLoanTypeTr" name="youngLoanTypeTr" style="display:none">
						<td class="hd2 rt" style="width:100px;"><!-- hd1的 width 15% -->
                              	<th:block th:text="#{'C122M01A.youngLoanType'}"><!--青創申貸類型--></th:block> 
						</td>
						<td>
							<input type='radio' id='youngLoanType' name='youngLoanType' value='I' checked="true"><th:block th:text="#{'C122M01A.applyKind.I2'}"><!--一百萬以下--></th:block> 
							<input type='radio' id='youngLoanType' name='youngLoanType' value='J'><th:block th:text="#{'C122M01A.applyKind.J2'}"><!--一百萬以上--></th:block>
						</td>
					</tr>
					<tr>
						<td class="hd2 rt" style="width:100px;"><!-- hd1的 width 15% -->
							<th:block th:text="#{'C122M01A.custId'}"><!--身分證統編--></th:block>  	
						</td>
                        <td>
                        	<input type="text" id="custId" name="custId"  class="required" size='12' maxlength='10'>
									-
							<input type="text" id="dupNo" name="dupNo"  class="required" readonly="readonly" size='1' maxlength='1'>
							<button type="button" id="getCustName" >
                    			<span class="text-only"><th:block th:text="#{'button.importCust'}">引進客戶</th:block></span>
                			</button>
							
						</td>
					</tr>
					<tr>
						<td class="hd2 rt" style="width:100px;"><!-- hd1的 width 15% -->
							<th:block th:text="#{'C122M01A.custName'}"><!--借款人姓名--></th:block>  	
						</td>
                        <td>
                        	<input type="text" id="custName" name="custName"  class="required" readonly="readonly" size='12' maxlength='10'>
							
						</td>
					</tr>
					<tr>
						<td class="hd1">
							<th:block th:text="#{'C122M01A.Input.Amt'}"><!--申請金額--></th:block>  	
						</td>
						<td>
							<th:block th:text="#{'C122M01A.TWD'}"><!--TWD--></th:block>
								<input type="text" id="applyAmt" name="applyAmt" size="6" maxlength="6" integer="6" class="numeric required" />
							<th:block th:text="#{'C122M01A.TenThousand'}"><!--萬--></th:block>
						</td>
					</tr>
					<tr>
						<td class="hd1">
							<th:block th:text="#{'C122M01A.Input.Year'}"><!--借款年限--></th:block>  	
						</td>
						<td>
							<input type="text" id="Year" name="Year" size="4" maxlength="4" integer="4" class="numeric required" />
							<th:block th:text="#{'C122M01A.Year'}"><!--年--></th:block>
							
							<input type="text" id="Month" name="Month" size="2" maxlength="2" integer="2" class="numeric required" />
							<th:block th:text="#{'C122M01A.Month'}"><!--月--></th:block>
						</td>
					</tr>
					
					<tr>
						<td class="hd1">
							<th:block th:text="#{'C122M01A.Input.NowExtend'}"><!--寬限期--></th:block>  	
						</td>
						<td>
							<input type='radio' id='nowExtend' name='nowExtend' value='Y'><th:block th:text="#{'C122M01A.Y'}"><!--有--></th:block> 
							<input type='radio' id='nowExtend' name='nowExtend' value='N' checked="true"><th:block th:text="#{'C122M01A.N'}"><!--無--></th:block>
							
							<input type="text" id="extYear" name="extYear" size="4" maxlength="4" integer="4" class="numeric required" disabled="true"/>
							<th:block th:text="#{'C122M01A.Year'}"><!--年--></th:block>
						</td>
					</tr>


					<tr id="estFlagTr" name="estFlagTr">
						<td class="hd1">
                    		<span class="text-red">＊</span>
							<th:block th:text="#{'C122M01A.estFlag'}">有無擔保品</th:block>
                    	</td>
						<td>
                    		<select name="estFlag" id="estFlag" comboKey="C122M01A_estFlag" comboType="2" space="true"></select> 
						</td>
					</tr>
					
					<tr id="laaFlagTr" name="laaFlagTr">
						<td class="hd1">
                    		<span class="text-red">＊</span>
							<th:block th:text="#{'C122M01A.laaFlag'}">地政士引介</th:block>
                    	</td>
						<td>
                    		<input type="radio" id="laaFlag" name="laaFlag" value="Y"><th:block th:text="#{'C122M01A.Yes'}"><!--是--></th:block> 
							<input type="radio" id="laaFlag" name="laaFlag" value="N" checked="true"><th:block th:text="#{'C122M01A.No'}"><!--否--></th:block>
						</td>
					</tr>
					
				</table>
			</form>
		</div>
		
		<div id="filterBoxDiv" style="display:none">
            <form id="filterBoxForm">
            	<table class="tb2"  style="width:550px;">
					<tr>
						<td class='hd2' width='30%' nowrap><th:block th:text="#{'C122M01A.custId'}"><!--身分證統編--></th:block></td>
						<td><input type='text' id='filter_custId' name='filter_custId' maxlength='10' size='12' /></td>
					</tr>
					<tr>
						<td class='hd2' width='30%' nowrap><th:block th:text="#{'C122M01A.ploanCaseNo'}"><!--案件編號--></th:block></td>
						<td><input type='text' id='ploanCaseId' name='ploanCaseId' maxlength='30' size='30' /></td>
					</tr>
					<tr>
						<td class='hd2' width='30%' nowrap><th:block th:text="#{'C122M01A.applyKind'}"><!--案件類型--></th:block></td>
						<td><select id='applyKind' name='applyKind'>
							<option value=''><th:block th:text="#{'comboSpace'}"><!--請選擇--></th:block></option>
							<option value='B'><th:block th:text="#{'C122M01A.applyKind.B'}"><!--勞工紓困--></th:block></option>
							<option value='C'><th:block th:text="#{'C122M01A.applyKind.C'}"><!--卡友信貸--></th:block></option>
							<option value='H'><th:block th:text="#{'C122M01A.applyKind.H'}"><!--房貸增貸--></th:block></option>
							<option value='E'><th:block th:text="#{'C122M01A.applyKind.E2'}"><!--房貸--></th:block></option>
							<option value='P'><th:block th:text="#{'C122M01A.applyKind.P2'}"><!--信貸--></th:block></option>
							<option value='I'><th:block th:text="#{'C122M01A.applyKind.I'}"><!--青創一百萬以下--></th:block></option>
							<option value='J'><th:block th:text="#{'C122M01A.applyKind.J'}"><!--青創一百萬以上--></th:block></option>
							<option value='O'><th:block th:text="#{'C122M01A.applyKind.O'}"><!--其他--></th:block></option>
						</select></td>
					</tr>
					<tr id='incomTypeTr' style="display:none">
						<td class='hd2' width='30%' nowrap><th:block th:text="#{'C122M01A.incomType'}"><!--進件類型--></th:block></td>
						<td><select id='incomType' name='incomType'>
							<option value=''><th:block th:text="#{'comboSpace'}"><!--請選擇--></th:block></option>
							<option value='1'><th:block th:text="#{'C122M01A.incomType.1'}"><!--線下--></th:block></option>
							<option value='2'><th:block th:text="#{'C122M01A.incomType.2'}"><!--線上--></th:block></option>
						</select></td>
					</tr>
					
					
					<tr>
						<td class='hd2' width='30%' nowrap><th:block th:text="#{'C122M01A.applyTS'}"><!--進件日期--></th:block></td>
						<td>
						<input type='text' id='applyTS_beg' name='applyTS_beg' maxlength='10' class='date' /> ~ 
						<input type='text' id='applyTS_end' name='applyTS_end' maxlength='10' class='date' />
						</td>
					</tr>
					<tr>
						<td class='hd2' width='30%' nowrap><th:block th:text="#{'C122M01A.docStatus'}"><!--案件狀態--></th:block></td>
						<td>
							<select space="true" id="SdocStatus" name="SdocStatus" combokey="c122m01a_edit"  itemStyle="format:{value} - {key}" class="required"></select>
						</td>
					</tr>
					<tr>
						<td class='hd2' width='30%' nowrap><th:block th:text="#{'C122M01A.signMegaEmpName'}"><!--簽案人員--></th:block> 
						</td>
						<td>
							<div id="SignDiv">
								<select id="SignMegaEmp" name="SignMegaEmp" class="boss"></select> 
							</div>
						</td>
					</tr>
					<tr>
						<td class='hd2' width='30%' nowrap>
							<th:block th:text="#{'csc_ploanPlan'}">行銷方案</th:block> 
						</td>
						<td>
							<select space='true' combokey='ploan_plan' id='ploanPlan' name='ploanPlan'></select>
						</td>
					</tr>
				</table>
			</form>
		</div>
		
		<!-- 派案視窗 -->
		<div id="changeSignEmpDiv" style="display:none">
            <form id="changeSignEmpForm">
            	<table class="tb2"  style="width:300px;">
					<tr>
						<td class="hd2 rt" style="width:100px;"><!-- hd1的 width 15% -->
                              <th:block th:text="#{'C122M01A.signMegaEmpName'}"><!--簽案行員--></th:block> 
						</td>
						<td>
							<div id="SignDiv">
								<select id="chgSignMegaEmp" name="chgSignMegaEmp" class="boss"></select> 
							</div>
						</td>
					</tr>
					<tr id="MegaEmpTr">
						<td class="hd2 rt" style="width:100px;"><!-- hd1的 width 15% -->
                             <th:block th:text="#{'C122M01F.evaMegaEmp'}"><!--估價行員--></th:block> 
						</td>
						<td>
							<div id="evaMegaDiv">
								<select id="chgEvaMegaEmp" name="chgEvaMegaEmp" space="true" class="editable"></select> 
							</div>
						</td>
					</tr>
					<tr id="estUnitTr">
						<td class="hd2 rt" ><!-- hd1的 width 15% -->
							<th:block th:text="#{'C122M01F.estUnitName'}"><!--委外估價單位名稱--></th:block>
						</td>
						<td>
							<input type="text" id="chgEstUnitName" name="chgEstUnitName"/>
						</td>
					</tr>
				</table>
			</form>
		</div>
			
		<!-- 派案分行設定視窗 -->
		<div id="autoAssignDiv" style="display:none">
			<div>
				<select id="assigneeBrchIdOptions" name="assigneeBrchIdOptions" style="width:100%" class="required" ></select>
			</div>
			<div>
				<button type="button" id="addDesigneeBrch">
					<span class="text-only"><th:block th:text="#{'button.add'}">新增</th:block></span>
				</button>
				<button type="button" id="deleteDesigneeBrch">
					<span class="text-only"><th:block th:text="#{'button.delete'}">刪除</th:block></span>
				</button>
				<button type="button" id="unloadGird">
					<span class="text-only">清空清單</span>
				</button>
			</div><p><p>
			<div id="autoAssignGrid" ></div>
		</div>
		
		<!-- 一鍵分案視窗  勾選參與分案的經辦 -->
		<div id="c900m01nChooseEmpDiv" style="display:none">
			<div id='c900m01nChooseEmpGrid'></div>
		</div>	
		
		<!-- 編輯可被一鍵分案經辦視窗 -->
		<div id="c900m01nBrUserDiv" style="display:none">
			<div id='c900m01nBrUserGrid'></div>
		</div>		
		
		<!-- 實際分案視窗 -->
		<div id="c900m01nAssignCaseDiv" style="display:none">
			<span class="text-only"><th:block th:text="#{'C900M01N.totalCaseCount'}">總件數</th:block>:</span>
			<span id='c900m01nTotalCaseCount'></span>
			<div style="float:right;">
				<span class="text-only"><th:block th:text="#{'C900M01N.shouldTotalCount'}">已分配總件數</th:block>:</span>
				<span id='c900m01nShouldTotalCount'></span>
			</div>
			<div id='c900m01nAssignCaseGrid'></div>
		</div>		
	</th:block>
</body>
</html>
