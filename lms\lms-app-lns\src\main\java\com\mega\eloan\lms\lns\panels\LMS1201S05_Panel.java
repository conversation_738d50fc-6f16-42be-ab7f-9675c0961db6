package com.mega.eloan.lms.lns.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * 說明(企金授權外)
 * </pre>
 * 
 * @since 2012/1/19
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/19,<PERSON>,new
 *          </ul>
 */
public class LMS1201S05_Panel extends Panel {

	/**
	 * 
	 */
	private static final long serialVersionUID = -4024257163623646201L;

	public LMS1201S05_Panel(String id) {
		super(id);
//		add(new LMS1205S05Panel01("lms1205s05panel01"));
//		add(new LMS1205S05Panel02("lms1205s05panel02"));
//		add(new LMS1205S05Panel03("lms1205s05panel03"));
//		add(new LMS1205S05Panel04("lms1205s05panel04"));
//		add(new LMS1205S05Panel05("lms1205s05panel05"));
//		add(new LMS1205S05Panel06("lms1205s05panel06"));
//		add(new LMS1205S05Panel07("lms1205s05panel07"));
	}
	
	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);
		
		new LMSS05APanel("LMSS05APanel").processPanelData(model, params);
	}
}
