package com.mega.eloan.lms.mfaloan.bean;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import org.apache.wicket.markup.html.form.Check;

import tw.com.iisi.cap.model.GenericBean;

/** 額度資訊檔 **/
public class ELF506 extends GenericBean {

	private static final long serialVersionUID = 1L;

	/** 額度序號 **/
	@Size(max=12)
	@Column(name="ELF506_CNTRNO", length=12, columnDefinition="CHAR(12)", unique = true)
	private String elf506_cntrno;

	/** 大陸授信註記Y/N **/
	@Size(max=1)
	@Column(name="ELF506_CN_LOAN_FG", length=1, columnDefinition="CHAR(1)")
	private String elf506_cn_loan_fg;

	/** 1-直接2-間接/代碼 **/
	@Size(max=2)
	@Column(name="ELF506_DIRECT_FG", length=2, columnDefinition="CHAR(2)")
	private String elf506_direct_fg;

	/** 短期貿易授信註記Y/N **/
	@Size(max=1)
	@Column(name="ELF506_S_TRADE_FG", length=1, columnDefinition="CHAR(1)")
	private String elf506_s_trade_fg;

	/** 保證1比例 **/
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "ELF506_GUAR1_RATE", columnDefinition = "DECIMAL(3,0)")
	private BigDecimal elf506_guar1_rate;

	/** 保證2比例 **/
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "ELF506_GUAR2_RATE", columnDefinition = "DECIMAL(3,0)")
	private BigDecimal elf506_guar2_rate;

	/** 保證3比例 **/
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "ELF506_GUAR3_RATE", columnDefinition = "DECIMAL(3,0)")
	private BigDecimal elf506_guar3_rate;

	/** 擔保1比例 **/
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "ELF506_COLL1_RATE", columnDefinition = "DECIMAL(3,0)")
	private BigDecimal elf506_coll1_rate;

	/** 擔保2比例 **/
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "ELF506_COLL2_RATE", columnDefinition = "DECIMAL(3,0)")
	private BigDecimal elf506_coll2_rate;

	/** 擔保3比例 **/
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "ELF506_COLL3_RATE", columnDefinition = "DECIMAL(3,0)")
	private BigDecimal elf506_coll3_rate;

	/** 擔保4比例 **/
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "ELF506_COLL4_RATE", columnDefinition = "DECIMAL(3,0)")
	private BigDecimal elf506_coll4_rate;

	/** 擔保5比例 **/
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "ELF506_COLL5_RATE", columnDefinition = "DECIMAL(3,0)")
	private BigDecimal elf506_coll5_rate;

	/** 最近異動時間 **/
	@Column(name = "ELF506_MODIFYTIME", columnDefinition = "TIMESTAMP")
	private Timestamp elf506_modifytime;

	/** 建檔時間 **/
	@Column(name = "ELF506_CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp elf506_createtime;

	/** 建檔來源ELOAN/ALOAN **/
	@Size(max=5)
	@Column(name="ELF506_CREATEUNIT", length=5, columnDefinition="CHAR(5)")
	private String elf506_createunit;

	/** 最近異動來源ELOAN/ALOAN **/
	@Size(max=5)
	@Column(name="ELF506_MODIFYUNIT", length=5, columnDefinition="CHAR(5)")
	private String elf506_modifyunit;

	/** ELOAN簽案文號 **/
	@Size(max=20)
	@Column(name="ELF506_DOCUMENT_NO", length=20, columnDefinition="CHAR(20)")
	private String elf506_document_no;

	/** 大陸金融開十足擔保信用狀 **/
	@Size(max=1)
	@Column(name="ELF506_IGOL_FLAG", length=1, columnDefinition="CHAR(1)")
	private String elf506_igol_flag;

	/** 衍生性金融商品立約人身份別 **/
	@Size(max=1)
	@Column(name="ELF506_CN_TMU_FG", length=1, columnDefinition="CHAR(1)")
	private String elf506_cn_tmu_fg;

	/** 企業類別 **/
	@Size(max=1)
	@Column(name="ELF506_CN_BUS_KIND", length=1, columnDefinition="CHAR(1)")
	private String elf506_cn_bus_kind;

	/** 客戶統編(PV-IDNO) **/
	@Size(max=11)
	@Column(name="ELF506_CUST_ID", length=11, columnDefinition="CHAR(11)")
	private String elf506_cust_id;

	/** 是否屬銀行法72-2條控管對象 **/
	@Size(max=1)
	@Column(name="ELF506_722_FLAG", length=1, columnDefinition="CHAR(1)")
	private String elf506_722_flag;

	/** 72-2條異動來源ELOAN/ALOAN **/
	@Size(max=5)
	@Column(name="ELF506_722_MODUNIT", length=5, columnDefinition="CHAR(5)")
	private String elf506_722_modunit;

	/** 72-2條案號 **/
	@Size(max=20)
	@Column(name="ELF506_722_DOC_NO", length=20, columnDefinition="CHAR(20)")
	private String elf506_722_doc_no;

	/** 72-2條修改時間 **/
	@Column(name = "ELF506_722_MODTIME", columnDefinition = "TIMESTAMP")
	private Timestamp elf506_722_modtime;

	/** 72-2核准日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELF506_722_SDATE", columnDefinition="DATE")
	private Date elf506_722_sdate;

	/** 72-2本案是否為購置不動產｛空白-消金, X-不檢核, Y-企金戶, N-企金戶｝ **/
	@Size(max=1)
	@Column(name="ELF506_722_IS_BUY", length=1, columnDefinition="CHAR(1)")
	private String elf506_722_is_buy;

	/** 72-2本案符合之排除原因 **/
	@Size(max=3)
	@Column(name="ELF506_722_EX_ITEM", length=3, columnDefinition="CHAR(3)")
	private String elf506_722_ex_item;

	/** 新授信對象別 **/
	@Size(max=5)
	@Column(name="ELF506_LOAN_TARGET", length=5, columnDefinition="CHAR(5)")
	private String elf506_loan_target;

	/** 開狀行為 **/
	@Size(max=1)
	@Column(name="ELF506_IS_TYPE", length=1, columnDefinition="CHAR(1)")
	private String elf506_is_type;

	/** 保證人種類 **/
	@Size(max=1)
	@Column(name="ELF506_GRNT_TYPE", length=1, columnDefinition="CHAR(1)")
	private String elf506_grnt_type;

	/** 國際聯貸註記 **/
	@Size(max=1)
	@Column(name="ELF506_UNION_AREA3", length=1, columnDefinition="CHAR(1)")
	private String elf506_union_area3;

	/** 擔保分類 **/
	@Size(max=1)
	@Column(name="ELF506_GRNT_CLASS", length=1, columnDefinition="CHAR(1)")
	private String elf506_grnt_class;

	/** 其他信用增強種類 **/
	@Size(max=20)
	@Column(name="ELF506_OTHCRD_TYPE", length=20, columnDefinition="CHAR(20)")
	private String elf506_othcrd_type;

	/** 是否由非大陸地區本行聯行開具擔保信用狀十足保證 **/
	@Size(max=1)
	@Column(name="ELF506_NCN_SBLC_FG", length=1, columnDefinition="CHAR(1)")
	private String elf506_ncn_sblc_fg;
	
	/**
	 * 約定融資額度註記 J-105-0155-001 Web e-Loan國內、海外企金額度明細表增加『約定融資額度註記』欄位與上傳a-Loan功能
	 */
	@Column(name = "ELF506_EXCEPT", length = 1, columnDefinition = "CHAR(1)")
	private String elf506_except;
	
	/**
	 * 約定融資額度註記問答項目選擇[是]的項目
	 */
	@Column(name = "ELF506_EX_QA_Y", length = 2, columnDefinition = "CHAR(2)")
	private String elf506_ex_qa_y;
	
	/**
	 * 約定融資額度註記資本計提延伸問答(僅有條件及無條件可取消)
	 */
	@Column(name = "ELF506_EX_QA_PLUS", length = 1, columnDefinition = "CHAR(1)")
	private String elf506_ex_qa_plus;
	
	/**
	 * 是否純營建工程業之營運週轉金
	 */
	@Column(name = "ELF506_INSTALMENT", length = 1, columnDefinition = "CHAR(1)")
	private String elf506_instalment;

	/**
	 * 產品種類
	 */
	@Column(name = "ELF506_PROD_KIND", columnDefinition = "CHAR(2)")
	private String elf506_prod_kind;
	
	/**
	 * ADC案件編號
	 * <p/>
	 * ADC + 2021(西元年) + 007 (分行代號)+ 00001(流水號)
	 */
	@Column(name = "ELF506_ADC_CASENO", length = 15, columnDefinition = "CHAR(15)")
	private String elf506_adc_caseno;
	
	
	/** 取得額度序號 **/
	public String getElf506_cntrno() {
		return this.elf506_cntrno;
	}
	/** 設定額度序號 **/
	public void setElf506_cntrno(String value) {
		this.elf506_cntrno = value;
	}

	/** 取得大陸授信註記Y/N **/
	public String getElf506_cn_loan_fg() {
		return this.elf506_cn_loan_fg;
	}
	/** 設定大陸授信註記Y/N **/
	public void setElf506_cn_loan_fg(String value) {
		this.elf506_cn_loan_fg = value;
	}

	/** 取得1-直接2-間接/代碼 **/
	public String getElf506_direct_fg() {
		return this.elf506_direct_fg;
	}
	/** 設定1-直接2-間接/代碼 **/
	public void setElf506_direct_fg(String value) {
		this.elf506_direct_fg = value;
	}

	/** 取得短期貿易授信註記Y/N **/
	public String getElf506_s_trade_fg() {
		return this.elf506_s_trade_fg;
	}
	/** 設定短期貿易授信註記Y/N **/
	public void setElf506_s_trade_fg(String value) {
		this.elf506_s_trade_fg = value;
	}

	/** 取得保證1比例 **/
	public BigDecimal getElf506_guar1_rate() {
		return this.elf506_guar1_rate;
	}
	/** 設定保證1比例 **/
	public void setElf506_guar1_rate(BigDecimal value) {
		this.elf506_guar1_rate = value;
	}

	/** 取得保證2比例 **/
	public BigDecimal getElf506_guar2_rate() {
		return this.elf506_guar2_rate;
	}
	/** 設定保證2比例 **/
	public void setElf506_guar2_rate(BigDecimal value) {
		this.elf506_guar2_rate = value;
	}

	/** 取得保證3比例 **/
	public BigDecimal getElf506_guar3_rate() {
		return this.elf506_guar3_rate;
	}
	/** 設定保證3比例 **/
	public void setElf506_guar3_rate(BigDecimal value) {
		this.elf506_guar3_rate = value;
	}

	/** 取得擔保1比例 **/
	public BigDecimal getElf506_coll1_rate() {
		return this.elf506_coll1_rate;
	}
	/** 設定擔保1比例 **/
	public void setElf506_coll1_rate(BigDecimal value) {
		this.elf506_coll1_rate = value;
	}

	/** 取得擔保2比例 **/
	public BigDecimal getElf506_coll2_rate() {
		return this.elf506_coll2_rate;
	}
	/** 設定擔保2比例 **/
	public void setElf506_coll2_rate(BigDecimal value) {
		this.elf506_coll2_rate = value;
	}

	/** 取得擔保3比例 **/
	public BigDecimal getElf506_coll3_rate() {
		return this.elf506_coll3_rate;
	}
	/** 設定擔保3比例 **/
	public void setElf506_coll3_rate(BigDecimal value) {
		this.elf506_coll3_rate = value;
	}

	/** 取得擔保4比例 **/
	public BigDecimal getElf506_coll4_rate() {
		return this.elf506_coll4_rate;
	}
	/** 設定擔保4比例 **/
	public void setElf506_coll4_rate(BigDecimal value) {
		this.elf506_coll4_rate = value;
	}

	/** 取得擔保5比例 **/
	public BigDecimal getElf506_coll5_rate() {
		return this.elf506_coll5_rate;
	}
	/** 設定擔保5比例 **/
	public void setElf506_coll5_rate(BigDecimal value) {
		this.elf506_coll5_rate = value;
	}

	/** 取得最近異動時間 **/
	public Timestamp getElf506_modifytime() {
		return this.elf506_modifytime;
	}
	/** 設定最近異動時間 **/
	public void setElf506_modifytime(Timestamp value) {
		this.elf506_modifytime = value;
	}

	/** 取得建檔時間 **/
	public Timestamp getElf506_createtime() {
		return this.elf506_createtime;
	}
	/** 設定建檔時間 **/
	public void setElf506_createtime(Timestamp value) {
		this.elf506_createtime = value;
	}

	/** 取得建檔來源ELOAN/ALOAN **/
	public String getElf506_createunit() {
		return this.elf506_createunit;
	}
	/** 設定建檔來源ELOAN/ALOAN **/
	public void setElf506_createunit(String value) {
		this.elf506_createunit = value;
	}

	/** 取得最近異動來源ELOAN/ALOAN **/
	public String getElf506_modifyunit() {
		return this.elf506_modifyunit;
	}
	/** 設定最近異動來源ELOAN/ALOAN **/
	public void setElf506_modifyunit(String value) {
		this.elf506_modifyunit = value;
	}

	/** 取得ELOAN簽案文號 **/
	public String getElf506_document_no() {
		return this.elf506_document_no;
	}
	/** 設定ELOAN簽案文號 **/
	public void setElf506_document_no(String value) {
		this.elf506_document_no = value;
	}

	/** 取得大陸金融開十足擔保信用狀 **/
	public String getElf506_igol_flag() {
		return this.elf506_igol_flag;
	}
	/** 設定大陸金融開十足擔保信用狀 **/
	public void setElf506_igol_flag(String value) {
		this.elf506_igol_flag = value;
	}

	/** 取得衍生性金融商品立約人身份別 **/
	public String getElf506_cn_tmu_fg() {
		return this.elf506_cn_tmu_fg;
	}
	/** 設定衍生性金融商品立約人身份別 **/
	public void setElf506_cn_tmu_fg(String value) {
		this.elf506_cn_tmu_fg = value;
	}

	/** 取得企業類別 **/
	public String getElf506_cn_bus_kind() {
		return this.elf506_cn_bus_kind;
	}
	/** 設定企業類別 **/
	public void setElf506_cn_bus_kind(String value) {
		this.elf506_cn_bus_kind = value;
	}

	/** 取得客戶統編(PV-IDNO) **/
	public String getElf506_cust_id() {
		return this.elf506_cust_id;
	}
	/** 設定客戶統編(PV-IDNO) **/
	public void setElf506_cust_id(String value) {
		this.elf506_cust_id = value;
	}

	/** 取得是否屬銀行法72-2條控管對象 **/
	public String getElf506_722_flag() {
		return this.elf506_722_flag;
	}
	/** 設定是否屬銀行法72-2條控管對象 **/
	public void setElf506_722_flag(String value) {
		this.elf506_722_flag = value;
	}

	/** 取得72-2條異動來源ELOAN/ALOAN **/
	public String getElf506_722_modunit() {
		return this.elf506_722_modunit;
	}
	/** 設定72-2條異動來源ELOAN/ALOAN **/
	public void setElf506_722_modunit(String value) {
		this.elf506_722_modunit = value;
	}

	/** 取得72-2條案號 **/
	public String getElf506_722_doc_no() {
		return this.elf506_722_doc_no;
	}
	/** 設定72-2條案號 **/
	public void setElf506_722_doc_no(String value) {
		this.elf506_722_doc_no = value;
	}

	/** 取得72-2條修改時間 **/
	public Timestamp getElf506_722_modtime() {
		return this.elf506_722_modtime;
	}
	/** 設定72-2條修改時間 **/
	public void setElf506_722_modtime(Timestamp value) {
		this.elf506_722_modtime = value;
	}

	/** 取得72-2核准日期 **/
	public Date getElf506_722_sdate() {
		return this.elf506_722_sdate;
	}
	/** 設定72-2核准日期 **/
	public void setElf506_722_sdate(Date value) {
		this.elf506_722_sdate = value;
	}

	/** 取得72-2本案是否為購置不動產｛空白-消金, X-不檢核, Y-企金戶, N-企金戶｝ **/
	public String getElf506_722_is_buy() {
		return this.elf506_722_is_buy;
	}
	/** 設定72-2本案是否為購置不動產｛空白-消金, X-不檢核, Y-企金戶, N-企金戶｝ **/
	public void setElf506_722_is_buy(String value) {
		this.elf506_722_is_buy = value;
	}

	/** 取得72-2本案符合之排除原因 **/
	public String getElf506_722_ex_item() {
		return this.elf506_722_ex_item;
	}
	/** 設定72-2本案符合之排除原因 **/
	public void setElf506_722_ex_item(String value) {
		this.elf506_722_ex_item = value;
	}

	/** 取得新授信對象別 **/
	public String getElf506_loan_target() {
		return this.elf506_loan_target;
	}
	/** 設定新授信對象別 **/
	public void setElf506_loan_target(String value) {
		this.elf506_loan_target = value;
	}

	/** 取得開狀行為 **/
	public String getElf506_is_type() {
		return this.elf506_is_type;
	}
	/** 設定開狀行為 **/
	public void setElf506_is_type(String value) {
		this.elf506_is_type = value;
	}

	/** 取得保證人種類 **/
	public String getElf506_grnt_type() {
		return this.elf506_grnt_type;
	}
	/** 設定保證人種類 **/
	public void setElf506_grnt_type(String value) {
		this.elf506_grnt_type = value;
	}

	/** 取得國際聯貸註記 **/
	public String getElf506_union_area3() {
		return this.elf506_union_area3;
	}
	/** 設定國際聯貸註記 **/
	public void setElf506_union_area3(String value) {
		this.elf506_union_area3 = value;
	}

	/** 取得擔保分類 **/
	public String getElf506_grnt_class() {
		return this.elf506_grnt_class;
	}
	/** 設定擔保分類 **/
	public void setElf506_grnt_class(String value) {
		this.elf506_grnt_class = value;
	}

	/** 取得其他信用增強種類 **/
	public String getElf506_othcrd_type() {
		return this.elf506_othcrd_type;
	}
	/** 設定其他信用增強種類 **/
	public void setElf506_othcrd_type(String value) {
		this.elf506_othcrd_type = value;
	}

	/** 取得是否由非大陸地區本行聯行開具擔保信用狀十足保證 **/
	public String getElf506_ncn_sblc_fg() {
		return this.elf506_ncn_sblc_fg;
	}
	/** 設定是否由非大陸地區本行聯行開具擔保信用狀十足保證 **/
	public void setElf506_ncn_sblc_fg(String value) {
		this.elf506_ncn_sblc_fg = value;
	}
	
	public String getElf506_except() {
		return elf506_except;
	}
	public void setElf506_except(String elf506_except) {
		this.elf506_except = elf506_except;
	}
	
	public String getElf506_ex_qa_y() {
		return elf506_ex_qa_y;
	}
	public void setElf506_ex_qa_y(String elf506_ex_qa_y) {
		this.elf506_ex_qa_y = elf506_ex_qa_y;
	}
	
	public String getElf506_ex_qa_plus() {
		return elf506_ex_qa_plus;
	}
	public void setElf506_ex_qa_plus(String elf506_ex_qa_plus) {
		this.elf506_ex_qa_plus = elf506_ex_qa_plus;
	}
	
	public String getElf506_instalment() {
		return elf506_instalment;
	}
	public void setElf506_instalment(String elf506_instalment) {
		this.elf506_instalment = elf506_instalment;
	}
	
	public String getElf506_prod_kind() {
		return elf506_prod_kind;
	}
	public void setElf506_prod_kind(String elf506_prod_kind) {
		this.elf506_prod_kind = elf506_prod_kind;
	}
	
	public String getElf506_adc_caseno() {
		return elf506_adc_caseno;
	}
	public void setElf506_adc_caseno(String elf506_adc_caseno) {
		this.elf506_adc_caseno = elf506_adc_caseno;
	}
	
}
