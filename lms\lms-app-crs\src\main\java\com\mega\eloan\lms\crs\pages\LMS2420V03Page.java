package com.mega.eloan.lms.crs.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.crs.panels.LMS2420FilterPanel;

@Controller
@RequestMapping("/crs/lms2420v03")
public class LMS2420V03Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {

		setGridViewStatus(RetrialDocStatusEnum.預約單_處理成功);
		addToButtonPanel(model, LmsButtonEnum.Filter);
		renderJsI18N(LMS2420V01Page.class);

		setupIPanel(new LMS2420FilterPanel(PANEL_ID), model, params);

		model.addAttribute("loadScript", "loadScript('pagejs/crs/LMS2420V01Page');");
	}
}
