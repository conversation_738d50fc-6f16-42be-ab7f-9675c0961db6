package com.mega.eloan.lms.cls.service.impl;

import java.io.IOException;
import java.net.URISyntaxException;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.dao.C120S01SDao;

import tw.com.iisi.cap.exception.CapException;
import tw.com.jcs.common.Util;

@Service("cls1141MixPdfservice")
public class CLS1141MixPDFServiceImpl implements FileDownloadService {
	
	protected static final Logger LOGGER = LoggerFactory.getLogger(CLS1141MixPDFServiceImpl.class);

	@Resource
	C120S01SDao c120s01sDao;
	
	@Override
	public byte[] getContent(PageParameters params) throws CapException, IOException, URISyntaxException {
		
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String dataType = Util.trim(params.getString("dataType"));
		String fileSeq = Util.trim(params.getString("fileSeq"));

		String oid = Util.trim(params.getString("oid"));
		if(Util.isNotEmpty(oid)){
			return c120s01sDao.findByOid(oid).getReportFile();
		}
		
		return c120s01sDao.findByUniqueKey(mainId, custId, dupNo, dataType, fileSeq).getReportFile();
	}
}
