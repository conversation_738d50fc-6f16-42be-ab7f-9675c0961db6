/* 
 * DW_RKJCIC.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.mfaloan.bean;


import java.util.Date;
import javax.persistence.*;

import tw.com.iisi.cap.model.GenericBean;


/** 聯徵特殊負面資訊 **/
public class DW_RKJCIC extends GenericBean{

	private static final long serialVersionUID = 1L;

	/** 分行別 **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="BR_CD", length=3, columnDefinition="CHAR(3)", nullable=false,unique = true)
	private String br_cd;

	/** NOTES文件編號 **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="NOTEID", length=32, columnDefinition="CHAR(32)", nullable=false,unique = true)
	private String noteid;

	/** 客戶統一編號 **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="CUSTID", length=10, columnDefinition="CHAR(10)", nullable=false,unique = true)
	private String custid;

	/** 重複序號 **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)", nullable=false,unique = true)
	private String dupno;

	/** 評等模型類別 **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="MOWTYPE", length=1, columnDefinition="CHAR(1)", nullable=false,unique = true)
	private String mowtype;

	/** 模型版本-大版 **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="MOWVER1", columnDefinition="DECIMAL(5,0)", nullable=false,unique = true)
	private Integer mowver1;

	/** 模型版本-小版 **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="MOWVER2", columnDefinition="DECIMAL(5,0)", nullable=false,unique = true)
	private Integer mowver2;

	/** JCIC查詢日期 YYYY-MM-DD **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Temporal(TemporalType.DATE)
	@Column(name="JCIC_DATE", columnDefinition="DATE", nullable=false,unique = true)
	private Date jcic_date;

	/**  **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="ACCT_KEY", length=14, columnDefinition="CHAR(14)", nullable=false,unique = true)
	private String acct_key;
	
	/** 主借款人統一編號(CUSTKEY) **/
	@Column(name="CUST_KEY", length=10, columnDefinition="CHAR(10)")
	private String cust_key;

	/** 
	 * 相關身分 ( LNGEFLAG)<p/>
	 * M:主借款人C:共同借款人G:連帶保證人
	 */
	@Column(name="LNGEFLAG", length=1, columnDefinition="CHAR(1)")
	private String lngeflag;

	/** 
	 * 退票<p/>
	 * Y/N
	 */
	@Column(name="EVER_BAD_CHECK", length=1, columnDefinition="CHAR(1)")
	private String ever_bad_check;

	/** 
	 * 拒往<p/>
	 * Y/N
	 */
	@Column(name="REJECT_YN", length=1, columnDefinition="CHAR(1)")
	private String reject_yn;

	/** 票信查詢日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="CHECK_QDATE", columnDefinition="DATE")
	private Date check_qdate;

	/** 票信資料截止日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="END_DATE", columnDefinition="DATE")
	private Date end_date;

	/** 
	 * 信用卡強停<p/>
	 * Y/N
	 */
	@Column(name="CREDIT_FORCE_STOP", length=1, columnDefinition="CHAR(1)")
	private String credit_force_stop;

	/** 
	 * 催收呆帳紀錄<p/>
	 * Y/N
	 */
	@Column(name="BAD_DEBT", length=1, columnDefinition="CHAR(1)")
	private String bad_debt;

	/** 
	 * 逾期放款<p/>
	 * Y/N
	 */
	@Column(name="LOAN_PASTDUE_YN", length=1, columnDefinition="CHAR(1)")
	private String loan_pastdue_yn;
	
	/** 
	 * 消債條例信用註記<p/>
	 * Y/N
	 */
	@Column(name="NEGO_LAW", length=1, columnDefinition="CHAR(1)")
	private String nego_law;

	/** 
	 * 銀行公會債務協商註記<p/>
	 * Y/N
	 */
	@Column(name="NEGO_BANK", length=1, columnDefinition="CHAR(1)")
	private String nego_bank;

	/** 
	 * 其他補充註記<p/>
	 * Y/N
	 */
	@Column(name="OTHER_WARNING", length=1, columnDefinition="CHAR(1)")
	private String other_warning;

	/** 
	 * 近12個月授信帳戶出現遲延2次<p/>
	 * Y/N
	 */
	@Column(name="LN12_PAY_DELAY_TIMES", length=1, columnDefinition="CHAR(1)")
	private String ln12_pay_delay_times;

	/** 
	 * 近12個月信用卡繳款狀況出現(循環信用有延遲)2次<p/>
	 * Y/N
	 */
	@Column(name="CC12_REVOL_PAY_DELAY_TIMES", length=1, columnDefinition="CHAR(1)")
	private String cc12_revol_pay_delay_times;

	/** 
	 * 近12個月信用卡繳款狀況出現(未繳足最低金額)2次<p/>
	 * Y/N
	 */
	@Column(name="CC12_MINPAY_DELAY_TIMES", length=1, columnDefinition="CHAR(1)")
	private String cc12_minpay_delay_times;

	/** 
	 * 近12個月信用卡繳款狀況出現(全額逾期未繳)2次<p/>
	 * Y/N
	 */
	@Column(name="CC12_TOTPAY_DELAY_TIMES", length=1, columnDefinition="CHAR(1)")
	private String cc12_totpay_delay_times;

	/** 
	 * 近12個月信用卡有預借現金餘額家數2家<p/>
	 * Y/N
	 */
	@Column(name="CC12_CASH_ADV_TIMES", length=1, columnDefinition="CHAR(1)")
	private String cc12_cash_adv_times;

	/** 
	 * 近12個月現金卡有動用紀錄<p/>
	 * Y/N
	 */
	@Column(name="LN12_CASH_TIMES", length=1, columnDefinition="CHAR(1)")
	private String ln12_cash_times;

	/** 
	 * 文件狀態<p/>
	 * 編製中|1待覆核|2待母行覆核|2C核准|3婉卻|4呈區域授信中心|5呈總行法金處/授管處|6待補件|7提放審會|H1提常董會|H2審核中|A已會簽|B會簽中|C會簽待覆核|2A
	 */
	@Column(name="DOCSTATUS", length=2, columnDefinition="CHAR(2)")
	private String docstatus;

	/** 上傳資料日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="DATA_SRC_DT", columnDefinition="DATE")
	private Date data_src_dt;

	/** 卡友貸旗標 */
	@Column(name="C_FLAG", length=1, columnDefinition="CHAR(1)")
	private String c_flag;
	
	/** 取得分行別 **/
	public String getBr_cd() {
		return this.br_cd;
	}
	/** 設定分行別 **/
	public void setBr_cd(String value) {
		this.br_cd = value;
	}

	/** 取得NOTES文件編號 **/
	public String getNoteid() {
		return this.noteid;
	}
	/** 設定NOTES文件編號 **/
	public void setNoteid(String value) {
		this.noteid = value;
	}

	/** 取得客戶統一編號 **/
	public String getCustid() {
		return this.custid;
	}
	/** 設定客戶統一編號 **/
	public void setCustid(String value) {
		this.custid = value;
	}

	/** 取得重複序號 **/
	public String getDupno() {
		return this.dupno;
	}
	/** 設定重複序號 **/
	public void setDupno(String value) {
		this.dupno = value;
	}

	/** 取得評等模型類別 **/
	public String getMowtype() {
		return this.mowtype;
	}
	/** 設定評等模型類別 **/
	public void setMowtype(String value) {
		this.mowtype = value;
	}

	/** 取得模型版本-大版 **/
	public Integer getMowver1() {
		return this.mowver1;
	}
	/** 設定模型版本-大版 **/
	public void setMowver1(Integer value) {
		this.mowver1 = value;
	}

	/** 取得模型版本-小版 **/
	public Integer getMowver2() {
		return this.mowver2;
	}
	/** 設定模型版本-小版 **/
	public void setMowver2(Integer value) {
		this.mowver2 = value;
	}

	/** 取得JCIC查詢日期 YYYY-MM-DD **/
	public Date getJcic_date() {
		return this.jcic_date;
	}
	/** 設定JCIC查詢日期 YYYY-MM-DD **/
	public void setJcic_date(Date value) {
		this.jcic_date = value;
	}

	/** 取得主借款人統一編號(CUSTKEY) **/
	public String getCust_key() {
		return this.cust_key;
	}
	/** 設定主借款人統一編號(CUSTKEY) **/
	public void setCust_key(String value) {
		this.cust_key = value;
	}

	/** 
	 * 取得相關身分 ( LNGEFLAG)<p/>
	 * M:主借款人C:共同借款人G:連帶保證人
	 */
	public String getLngeflag() {
		return this.lngeflag;
	}
	/**
	 *  設定相關身分 ( LNGEFLAG)<p/>
	 *  M:主借款人C:共同借款人G:連帶保證人
	 **/
	public void setLngeflag(String value) {
		this.lngeflag = value;
	}

	/** 
	 * 取得退票<p/>
	 * Y/N
	 */
	public String getEver_bad_check() {
		return this.ever_bad_check;
	}
	/**
	 *  設定退票<p/>
	 *  Y/N
	 **/
	public void setEver_bad_check(String value) {
		this.ever_bad_check = value;
	}

	/** 
	 * 取得拒往<p/>
	 * Y/N
	 */
	public String getReject_yn() {
		return this.reject_yn;
	}
	/**
	 *  設定拒往<p/>
	 *  Y/N
	 **/
	public void setReject_yn(String value) {
		this.reject_yn = value;
	}

	/** 取得票信查詢日期 **/
	public Date getCheck_qdate() {
		return this.check_qdate;
	}
	/** 設定票信查詢日期 **/
	public void setCheck_qdate(Date value) {
		this.check_qdate = value;
	}

	/** 取得票信資料截止日 **/
	public Date getEnd_date() {
		return this.end_date;
	}
	/** 設定票信資料截止日 **/
	public void setEnd_date(Date value) {
		this.end_date = value;
	}

	/** 
	 * 取得信用卡強停<p/>
	 * Y/N
	 */
	public String getCredit_force_stop() {
		return this.credit_force_stop;
	}
	/**
	 *  設定信用卡強停<p/>
	 *  Y/N
	 **/
	public void setCredit_force_stop(String value) {
		this.credit_force_stop = value;
	}

	/** 
	 * 取得催收呆帳紀錄<p/>
	 * Y/N
	 */
	public String getBad_debt() {
		return this.bad_debt;
	}
	/**
	 *  設定催收呆帳紀錄<p/>
	 *  Y/N
	 **/
	public void setBad_debt(String value) {
		this.bad_debt = value;
	}
	
	/** 
	 * 取得逾期放款<p/>
	 * Y/N
	 */
	public String getLoan_pastdue_yn() {
		return this.loan_pastdue_yn;
	}
	/**
	 *  設定逾期放款<p/>
	 *  Y/N
	 **/
	public void setLoan_pastdue_yn(String value) {
		this.loan_pastdue_yn = value;
	}
	
	
	/** 
	 * 取得消債條例信用註記<p/>
	 * Y/N
	 */
	public String getNego_law() {
		return this.nego_law;
	}
	/**
	 *  設定消債條例信用註記<p/>
	 *  Y/N
	 **/
	public void setNego_law(String value) {
		this.nego_law = value;
	}

	/** 
	 * 取得銀行公會債務協商註記<p/>
	 * Y/N
	 */
	public String getNego_bank() {
		return this.nego_bank;
	}
	/**
	 *  設定銀行公會債務協商註記<p/>
	 *  Y/N
	 **/
	public void setNego_bank(String value) {
		this.nego_bank = value;
	}

	/** 
	 * 取得其他補充註記<p/>
	 * Y/N
	 */
	public String getOther_warning() {
		return this.other_warning;
	}
	/**
	 *  設定其他補充註記<p/>
	 *  Y/N
	 **/
	public void setOther_warning(String value) {
		this.other_warning = value;
	}

	/** 
	 * 取得近12個月授信帳戶出現遲延2次<p/>
	 * Y/N
	 */
	public String getLn12_pay_delay_times() {
		return this.ln12_pay_delay_times;
	}
	/**
	 *  設定近12個月授信帳戶出現遲延2次<p/>
	 *  Y/N
	 **/
	public void setLn12_pay_delay_times(String value) {
		this.ln12_pay_delay_times = value;
	}

	/** 
	 * 取得近12個月信用卡繳款狀況出現(循環信用有延遲)2次<p/>
	 * Y/N
	 */
	public String getCc12_revol_pay_delay_times() {
		return this.cc12_revol_pay_delay_times;
	}
	/**
	 *  設定近12個月信用卡繳款狀況出現(循環信用有延遲)2次<p/>
	 *  Y/N
	 **/
	public void setCc12_revol_pay_delay_times(String value) {
		this.cc12_revol_pay_delay_times = value;
	}

	/** 
	 * 取得近12個月信用卡繳款狀況出現(未繳足最低金額)2次<p/>
	 * Y/N
	 */
	public String getCc12_minpay_delay_times() {
		return this.cc12_minpay_delay_times;
	}
	/**
	 *  設定近12個月信用卡繳款狀況出現(未繳足最低金額)2次<p/>
	 *  Y/N
	 **/
	public void setCc12_minpay_delay_times(String value) {
		this.cc12_minpay_delay_times = value;
	}

	/** 
	 * 取得近12個月信用卡繳款狀況出現(全額逾期未繳)2次<p/>
	 * Y/N
	 */
	public String getCc12_totpay_delay_times() {
		return this.cc12_totpay_delay_times;
	}
	/**
	 *  設定近12個月信用卡繳款狀況出現(全額逾期未繳)2次<p/>
	 *  Y/N
	 **/
	public void setCc12_totpay_delay_times(String value) {
		this.cc12_totpay_delay_times = value;
	}

	/** 
	 * 取得近12個月信用卡有預借現金餘額家數2家<p/>
	 * Y/N
	 */
	public String getCc12_cash_adv_times() {
		return this.cc12_cash_adv_times;
	}
	/**
	 *  設定近12個月信用卡有預借現金餘額家數2家<p/>
	 *  Y/N
	 **/
	public void setCc12_cash_adv_times(String value) {
		this.cc12_cash_adv_times = value;
	}

	/** 
	 * 取得近12個月現金卡有動用紀錄<p/>
	 * Y/N
	 */
	public String getLn12_cash_times() {
		return this.ln12_cash_times;
	}
	/**
	 *  設定近12個月現金卡有動用紀錄<p/>
	 *  Y/N
	 **/
	public void setLn12_cash_times(String value) {
		this.ln12_cash_times = value;
	}

	/** 
	 * 取得文件狀態<p/>
	 * 編製中|1待覆核|2待母行覆核|2C核准|3婉卻|4呈區域授信中心|5呈總行法金處/授管處|6待補件|7提放審會|H1提常董會|H2審核中|A已會簽|B會簽中|C會簽待覆核|2A
	 */
	public String getDocstatus() {
		return this.docstatus;
	}
	/**
	 *  設定文件狀態<p/>
	 *  編製中|1待覆核|2待母行覆核|2C核准|3婉卻|4呈區域授信中心|5呈總行法金處/授管處|6待補件|7提放審會|H1提常董會|H2審核中|A已會簽|B會簽中|C會簽待覆核|2A
	 **/
	public void setDocstatus(String value) {
		this.docstatus = value;
	}

	/** 取得上傳資料日期 **/
	public Date getData_src_dt() {
		return this.data_src_dt;
	}
	/** 設定上傳資料日期 **/
	public void setData_src_dt(Date value) {
		this.data_src_dt = value;
	}

	/** 取得 **/
	public String getAcct_key() {
		return this.acct_key;
	}

	/** 設定 **/
	public void setAcct_key(String value) {
		this.acct_key = value;
	}

	/** 取得卡友貸旗標 */
	public String getC_flag() {
		return c_flag;
	}
	/** 設定卡友貸旗標 */
	public void setC_flag(String c_flag) {
		this.c_flag = c_flag;
	}
}
