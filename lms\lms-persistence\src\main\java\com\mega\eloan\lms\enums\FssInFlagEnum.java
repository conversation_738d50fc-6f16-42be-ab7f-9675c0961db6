/* 
 * InFlag.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.enums;

/**
 * <pre>
 * 是否引入enum。
 * </pre>
 * 
 * @since 2011/8/4
 * <AUTHOR> Wang
 * @version <ul>
 *          <li>2011/8/4,Sunkist Wang,new</li>
 *          </ul>
 */
public enum FssInFlagEnum {
    /**
     * 0 否
     */
    NOT("0"),
    /**
     * 1 EXCEL
     */
    EXCEL("1"),
    /**
     * 2 時報
     */
    TIME_INFO("2");

    private String code;

    FssInFlagEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public boolean isEquals(Object other) {
        if (other instanceof String) {
            return code.equals(other);
        } else {
            return super.equals(other);
        }
    }

    public static FssInFlagEnum getEnum(String code) {
        for (FssInFlagEnum enums : FssInFlagEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }
}
