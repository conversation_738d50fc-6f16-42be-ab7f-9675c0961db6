/* 
 * LMS7110Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.service;

import java.util.List;

import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.model.L918M01A;
import com.mega.eloan.lms.model.L918S01A;

import tw.com.iisi.cap.model.GenericBean;


/**<pre>
 * 停權解除維護Service
 * </pre>
 * @since  2013/1/21
 * <AUTHOR>
 * @version <ul>
 *           <li>2013/1/21,<PERSON>,new
 *          </ul>
 */
public interface LMS7110Service extends AbstractService {
	
	// 停權解除維護主檔
	
	/**
	 * 利用oid找尋停權解除維護主檔
	 * @param oid
	 * @return
	 */
	L918M01A findL918m01aByOid(String oid);
	
	/**
	 * 利用mainId找尋停權解除維護主檔
	 * @param mainId
	 * @return
	 */
	L918M01A findL918m01aByMainId(String mainId);
	
	/**
	 * 利用oid刪除停權解除維護主檔(邏輯刪除)
	 * @param oid
	 */
	void deleteL918m01a(String oid);

	// 停權解除維護明細檔
	/**
	 * 利用mainId找尋停權解除維護明細檔群組
	 * @param mainId
	 * @return
	 */
	List<L918S01A> findL918s01aByMainId(String mainId);
	
	/**
	 * 利用oid找尋停權解除維護明細檔
	 * @param oid
	 * @return
	 */
	L918S01A findL918s01aByOid(String oid);
	
	/**
	 * 利用oid刪除停權解除維護明細檔(邏輯刪除)
	 * @param oid
	 */
	void deleteL918s01a(String oid);
	
	/**
	 * 利用mainId刪除停權解除維護明細檔群組(邏輯刪除)
	 * @param mainId
	 */
	void delList918s01aByMainId(String mainId);
	
	/**
	 * 儲存停權主檔與停權明細檔群組
	 * @param meta 停權主檔
	 * @param list 停權明細檔
	 */
	public void saveMainAndSubList(L918M01A meta, List<L918S01A> list);
	
	/**
	 * 刪除停權主檔與停權明細檔群組
	 * @param meta 停權主檔
	 * @param list 停權明細檔
	 */
	public void delMainAndSubList(L918M01A meta, List<L918S01A> list);
	
	/**
	 * 儲存停權明細檔群組
	 * @param list 停權明細檔
	 */
	public void saveList918s01a(List<L918S01A> list);
	
	/**
	 * 
	 * 授管處停權解除flow
	 * 
	 * @param mainOid
	 *            文件編號
	 * @param model
	 *            資料表
	 * @param setResult
	 *            boolean
	 * @param next
	 *            執行的下個動作
	 * 
	 * @throws Throwable
	 */
	public void flowAction(String mainOid, GenericBean model,
			boolean setResult, String next) throws Throwable;	
}
