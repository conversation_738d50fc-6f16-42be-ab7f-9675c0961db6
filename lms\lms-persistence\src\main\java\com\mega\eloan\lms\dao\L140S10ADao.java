/* 
 * L140S10ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140S10A;

/** 其他敘作條件資訊檔 **/
public interface L140S10ADao extends IGenericDao<L140S10A> {

	L140S10A findByOid(String oid);
	
	List<L140S10A> findByMainId(String mainId);
	
	List<L140S10A> findByMainAndSequence(String mainId, int[] seqArray);

	L140S10A findMaxSeqNumByMainId(String mainId, String bizCat, Integer bizCatId);

	List<L140S10A> findExist(String mainId, String loanTPs, String bizCat, String bizItem);

	List<L140S10A> findByLoanTPs(String mainId, String loanTPs);

	List<L140S10A> findByBizCatAndId(String mainId, String bizCat, Integer bizCatId);

	List<L140S10A> findByMainIdAndSeqNum(String mainId, int[] seqArray);

	L140S10A findByMainAndBizCat(String mainId, String bizCat);

	L140S10A findMaxSequenceByMainId(String mainId);
}