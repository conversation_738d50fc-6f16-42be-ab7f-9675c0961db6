
package com.mega.eloan.lms.lms.pages;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.common.OverSeaUtil;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.pages.AbstractOverSeaCLSPage;
import com.mega.eloan.lms.base.panels.OverSeaCLSOuterPanel;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.dao.C121M01ADao;
import com.mega.eloan.lms.lms.panels.LMS1015S01Panel;
import com.mega.eloan.lms.lms.panels.LMS1015S02Panel;
import com.mega.eloan.lms.lms.panels.LMS1015S03Panel;
import com.mega.eloan.lms.lms.panels.LMS1015S04Panel;
import com.mega.eloan.lms.lms.panels.LMS1015S05Panel;
import com.mega.eloan.lms.lms.panels.LMS1015S06Panel;
import com.mega.eloan.lms.lms.panels.LMS1015S07Panel;
import com.mega.eloan.lms.model.C121M01A;

import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 日本消金信用評等模型
 * </pre>
 * 
 * @since 2015/3/1
 * <AUTHOR>
 * @version <ul>
 *          <li>2015/3/1,EL08034,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms1015m01/{page}")
public class LMS1015M01Page extends AbstractEloanForm {

	final String TAB_CTX = "_tabCtx";
	final String TAB_SIGN = "tab-";
	
	@Autowired
	CLSService clsService;
	
	@Resource
	C121M01ADao c121m01aDao;

	@Override
	public void execute(ModelMap model, PageParameters params) {
		// 依權限設定button
		addAclLabel(model,
				new AclLabel("_btnDOC_EDITING", params, getDomainClass(), AuthType.Modify, CreditDocStatusEnum.海外_編製中));
		addAclLabel(model, new AclLabel("_btnWAIT_APPROVE", params, getDomainClass(), AuthType.Accept,
				CreditDocStatusEnum.海外_待覆核));
		addAclLabel(model,
				new AclLabel("_btn_APPROVED", params, getDomainClass(), AuthType.Modify, CreditDocStatusEnum.海外_已核准));
		
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C121M01A meta = clsService.findC121M01A_oid(mainOid);	
		boolean showTab06 = true;
		if(meta != null){
			if(Util.equals(meta.getVarVer(), OverSeaUtil.V2_0_LOAN_JP)){
				showTab06 = false;
			}
		}
		
		model.addAttribute("tab_c121m01a_varver_1_0", showTab06);
		
		renderJsI18N(LMS1015M01Page.class);
		renderJsI18N(LMS1015V01Page.class);
		
		renderJsI18N(AbstractOverSeaCLSPage.class);
		new OverSeaCLSOuterPanel("divOverSeaCLSPanel").processPanelData(model, params);
		
		// tabs
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		String tabID = TAB_SIGN + Util.addZeroWithValue(page, 2); // 指定ID
		Panel panel = getPanel(page, params);
		panel.processPanelData(model, params);
		model.addAttribute("tabIdx", tabID);

	}// ;

	// 頁籤
	public Panel getPanel(int index, PageParameters params) {
		Panel panel = null;
		
		switch (index) {
		case 1:
			panel = new LMS1015S01Panel(TAB_CTX, true);
			break;
		case 2:
			panel = new LMS1015S02Panel(TAB_CTX, true);
			break;
		case 3:
			panel = new LMS1015S03Panel(TAB_CTX, true);
			break;
		case 4:
			panel = new LMS1015S04Panel(TAB_CTX, true);
			break;
		case 5:
			String mainOid = params.getString(EloanConstants.MAIN_OID);
			C121M01A meta = clsService.findC121M01A_oid(mainOid);	
			String varVer = Util.trim(meta.getVarVer());
			
			panel = new LMS1015S05Panel(TAB_CTX, true, varVer);
			break;
		case 6:
			panel = new LMS1015S06Panel(TAB_CTX, true);
			break;
		case 7:
			panel = new LMS1015S07Panel(TAB_CTX, true);
			break;
		default:
			panel = new LMS1015S01Panel(TAB_CTX, true);
			break;
		}

		return panel;
	}

	public Class<? extends Meta> getDomainClass() {
		return C121M01A.class;
	}

}
