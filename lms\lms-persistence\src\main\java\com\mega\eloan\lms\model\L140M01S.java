/* 
 * L140M01S.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;

import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 應收帳款買方額度資訊主檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L140M01S", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "type", "itemSeq", "custId", "dupNo" }))
public class L140M01S extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 類型
	 * <p/>
	 * 1.買方無追索
	 */
	@Size(max = 1)
	@Column(name = "TYPE", length = 1, columnDefinition = "CHAR(1)")
	private String type;

	/** 序號 **/
	@Digits(integer = 5, fraction = 0, groups = Check.class)
	@Column(name = "ITEMSEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer itemSeq;

	/** 買方統一編號 **/
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 買方重覆序號 **/
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/** 買方客戶名稱 **/
	@Size(max = 120)
	@Column(name = "CUSTNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String custName;

	/** 額度－幣別 **/
	@Size(max = 3)
	@Column(name = "CURR", length = 3, columnDefinition = "CHAR(3)")
	private String curr;

	/** 額度－金額 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "FACTAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal factAmt;

	/** 共用統一編號 **/
	@Size(max = 10)
	@Column(name = "CUSTID2", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId2;

	/** 共用重覆序號 **/
	@Size(max = 1)
	@Column(name = "DUPNO2", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo2;

	/** 預支價金成數（%） **/
	@Digits(integer = 5, fraction = 2, groups = Check.class)
	@Column(name = "RATIO", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal ratio;

	/** 備註-匯率 **/
	@Size(max = 600)
	@Column(name = "REFRATE", length = 600, columnDefinition = "VARCHAR(600)")
	private String refRate;

	/** 備註1 **/
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(name = "MEMO1", columnDefinition = "CLOB")
	private String memo1;

	/** 備註2 **/
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(name = "MEMO2", columnDefinition = "CLOB")
	private String memo2;

	/** 輸入資料檢誤完成(Y/N) **/
	@Size(max = 1)
	@Column(name = "CHKYN", length = 1, columnDefinition = "CHAR(1)")
	private String chkYN;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/** 國別 **/
	@Size(max = 2)
	@Column(name = "COUNTRY", length = 2, columnDefinition = "CHAR(2)")
	private String country;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得類型
	 * <p/>
	 * 1.買方無追索
	 */
	public String getType() {
		return this.type;
	}

	/**
	 * 設定類型
	 * <p/>
	 * 1.買方無追索
	 **/
	public void setType(String value) {
		this.type = value;
	}

	/** 取得序號 **/
	public Integer getItemSeq() {
		return this.itemSeq;
	}

	/** 設定序號 **/
	public void setItemSeq(Integer value) {
		this.itemSeq = value;
	}

	/** 取得買方統一編號 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定買方統一編號 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得買方重覆序號 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定買方重覆序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得買方客戶名稱 **/
	public String getCustName() {
		return this.custName;
	}

	/** 設定買方客戶名稱 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/** 取得額度－幣別 **/
	public String getCurr() {
		return this.curr;
	}

	/** 設定額度－幣別 **/
	public void setCurr(String value) {
		this.curr = value;
	}

	/** 取得額度－金額 **/
	public BigDecimal getFactAmt() {
		return this.factAmt;
	}

	/** 設定額度－金額 **/
	public void setFactAmt(BigDecimal value) {
		this.factAmt = value;
	}

	/** 取得共用統一編號 **/
	public String getCustId2() {
		return this.custId2;
	}

	/** 設定共用統一編號 **/
	public void setCustId2(String value) {
		this.custId2 = value;
	}

	/** 取得共用重覆序號 **/
	public String getDupNo2() {
		return this.dupNo2;
	}

	/** 設定共用重覆序號 **/
	public void setDupNo2(String value) {
		this.dupNo2 = value;
	}

	/** 取得預支價金成數（%） **/
	public BigDecimal getRatio() {
		return this.ratio;
	}

	/** 設定預支價金成數（%） **/
	public void setRatio(BigDecimal value) {
		this.ratio = value;
	}

	/** 取得備註-匯率 **/
	public String getRefRate() {
		return this.refRate;
	}

	/** 設定備註-匯率 **/
	public void setRefRate(String value) {
		this.refRate = value;
	}

	/** 取得備註1 **/
	public String getMemo1() {
		return this.memo1;
	}

	/** 設定備註1 **/
	public void setMemo1(String value) {
		this.memo1 = value;
	}

	/** 取得備註2 **/
	public String getMemo2() {
		return this.memo2;
	}

	/** 設定備註2 **/
	public void setMemo2(String value) {
		this.memo2 = value;
	}

	/** 取得輸入資料檢誤完成(Y/N) **/
	public String getChkYN() {
		return this.chkYN;
	}

	/** 設定輸入資料檢誤完成(Y/N) **/
	public void setChkYN(String value) {
		this.chkYN = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/**
	 * 設定國別
	 * 
	 */
	public void setCountry(String country) {
		this.country = country;
	}

	/**
	 * 取得國別
	 * 
	 */
	public String getCountry() {
		return country;
	}
}
