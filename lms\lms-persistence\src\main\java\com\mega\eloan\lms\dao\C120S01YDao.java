package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C120S01Y;

/** 個金地政士名單 **/
public interface C120S01YDao extends IGenericDao<C120S01Y> {

    C120S01Y findByOid(String oid);

    List<C120S01Y> findByList(String mainId, String custId, String dupNo);

    C120S01Y findLaaByList(String mainId, String custId, String dupNo, String laaYear, String laaWord, String laaNo);

	List<C120S01Y> findByCustIdDupId(String custId, String DupNo);

	public int deleteByOid(String oid);

	List<C120S01Y> findByMainId(String mainId);
}