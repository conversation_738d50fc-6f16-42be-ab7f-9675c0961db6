/* 
 * C900M01MDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C900M01M;

/** MEGAIMAGE文件數位化檔案上傳紀錄檔 **/
public interface C900M01MDao extends IGenericDao<C900M01M> {

	public C900M01M findByOid(String oid);
	
	public List<C900M01M> findByMainId(String mainId);
	
	public List<C900M01M> findByDocFileOid(String docFileOid);
	
}