/* 
 * LMS9121GridHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.handler.grid;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.lms.fms.service.LMS9121Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.jcs.common.Util;

@Scope("request")
@Controller("lms9121gridhandler")
public class LMS9121GridHandler extends AbstractGridHandler {
	@Resource
	LMS9121Service lms9121Service;

	/**
	 * 更新CntrNo 資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	public CapMapGridResult queryL912cntrNoupdate(ISearch pageSetting,
			PageParameters params) throws CapException {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "branchId",
				user.getUnitNo());
		String OldCntrNo = Util.trim(params.getString("oldcntrNo"));
		String NewCntrNo = Util.trim(params.getString("newcntrNo"));
		List<Map<String, Object>> map = lms9121Service.sntrnoupdate(OldCntrNo,
				NewCntrNo);	
		CapMapGridResult Result = new CapMapGridResult(map, map.size());

		Result.setPageCount(map.size(), map.size());

		return Result;
	}

	/**
	 * 更新custId,DupNo 資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	public CapMapGridResult queryL912custIdupdate(ISearch pageSetting,
			PageParameters params) throws CapException {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "branchId",
				user.getUnitNo());
		String OldCustId = Util.trim(params.getString("oldcustId"));
		String OlddupNo = Util.trim(params.getString("olddupNo"));
		String NewCustId = Util.trim(params.getString("newcustId"));
		String NewdupNo = Util.trim(params.getString("newdupNo"));
		List<Map<String, Object>> map = lms9121Service.custIdupdate(OldCustId,
				OlddupNo, NewCustId, NewdupNo);
		CapMapGridResult Result = new CapMapGridResult(map, map.size());

		Result.setPageCount(map.size(), map.size());

		return Result;
	}

}
