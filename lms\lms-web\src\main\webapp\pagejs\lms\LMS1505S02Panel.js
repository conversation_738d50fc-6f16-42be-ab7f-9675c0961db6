var initDfd = window.initDfd || $.Deferred();
initDfd.done(function(json){
    $selectTo = $(".selectTo");
    
    $selectTo.change(function(){
        $(this).val() == "0" ? $(this).next(".theother").show() : $(this).next(".theother").hide();//當人員選擇為自行輸入時出現輸入框
    });
    $("#chairMan,#lawsBoss").setItems({
        item: json.mainPeopleMap
    
    });
    //紀錄、帳戶管理員
    $("#accounting,#recorder").setItems({
        item: json.allPeopleMap
    });
    //L150M01a.useSelf=自行輸入
    $selectTo.append("<option value='0'>" + i18n.lms1505m01['L150M01a.useSelf'] + "</option>");
    $("#chairMan").val($.trim(json.chairMan));
    $("#lawsBoss").val($.trim(json.lawsBoss));
    $("#accounting").val($.trim(json.accounting));
    $("#recorder").val($.trim(json.recorder));
    
    //J-112-0057_05097_B1001 Web e-Loan授信管理系統, 調整授信審查處之小放會會議紀錄欄項名稱及格式
    //J-112-0057_05097_B1002 Web e-Loan授信管理系統, 調整授信審查處之小放會會議紀錄欄項名稱及格式
    //[下午 01:29] 金至忠(授信審查處,襄理)
    //授審處小會會議記錄格式, 幫忙把法令遵循主管加回
    if (userInfo.unitNo == "918") {
    	$(".hideFor918").show();   //$(".hideFor918").hide();
    }else{
    	$(".hideFor918").show();
    }
    
    
    //觸發隱藏顯示條件
    $selectTo.trigger("change");
    
    
    var grid = $("#gistGrid").iGrid({
        handler: inits.ghandle,
        height: 225,
        rowNum: 10,
        postData: {
            formAction: "queryCase",
			createType:json.createType
        },
        sortname: 'caseDate',
        colModel: [{
            colHeader: i18n.lms1505m01['L150M01a.caseDate'],// "簽案日期",
            name: 'caseDate',
            align: "center",
            width: 50,
            sortable: true
        }, {
            colHeader: i18n.lms1505m01['L150M01a.custName'],//"主要借款人",
            name: 'custName',
            align: "center",
            width: 100,
            sortable: true
        }, {
            colHeader: i18n.abstracteloan['doc.docStatus'],//"文件狀態",
            name: 'docStatus',
            align: "center",
            width: 70,
            sortable: true
        }, {
            name: 'gist',
            hidden: true
        }, {
            name: 'oid',
            hidden: true
        }]
    });
    
    //引進案由
    $("#readDocLog").click(function(){
        grid.resetSelection();
        $("#gistView").thickbox({ // 使用選取的內容進行彈窗
            //L150M01a.logngist=引進案由
            title: i18n.lms1505m01['L150M01a.logngist'],
            width: 600,
            height: 375,
            align: 'center',
            valign: 'bottom',
            i18n: i18n.def,
            modal: false,
            buttons: {
                "sure": function(){
                    var id = grid.getGridParam('selrow');
                    if (!id) {
                    
                        //action_005=請先選取一筆以上之資料列
                        return CommonAPI.showMessage(i18n.def['action_005']);
                    }
                    var oid = grid.getRowData(id).oid;
                    $.ajax({
                        handler: "lms1505m01formhandler",
                        data: {
                            formAction: "getCase",
                            oid: oid
                        }
                        
                    }).done(function(obj){
						  $("#gist").val(obj.gist);
					 });
                    $.thickbox.close();
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    });
    
    var grid2 = $("#selectPeopleLogGrid").iGrid({
        handler: inits.ghandle,
        height: 200,
        multiselect: true,
        rowNum: 100,
        hideMultiselect: false,
        postData: {
            formAction: "queryPeople"
        },
        sortname: 'userId',
        colModel: [{
            colHeader: i18n.lms1505m01["L150M01a.userId"],// "人員序號"
            name: 'userId',
            align: "center",
            width: 50,
            sortable: true
        }, {
            colHeader: i18n.lms1505m01["L150M01a.userName"],//"人員名稱",
            name: 'userName',
            align: "center",
            width: 50,
            sortable: true
        }]
    });
    
    //選擇出席人員
    $("#selectPeople").click(function(){
        //開啟畫面時帶原本畫面的選擇人員
    	//J-112-0586_05097_B1002 依據簽會-2023-2192「Web eLoan-Checkmarx弱點改善會議」按季追蹤弱點修正進度
        $("#allPeopleShow").html(DOMPurify.sanitize($("#present").val()));
        $("#selectPeopleView").thickbox({ // 使用選取的內容進行彈窗
            //L150M01a.present=出席人員
            title: i18n.lms1505m01["L150M01a.present"],
            width: 400,
            height: 400,
            align: 'center',
            valign: 'bottom',
            i18n: i18n.def,
            modal: false,
            buttons: {
                "sure": function(){
                    var ids = grid2.getGridParam('selarrrow');//取出選擇ID並放入陣列
                    var myArray = [];
                    
                    if (ids == '') {
                        //action_005=請先選取一筆以上之資料列
                        return CommonAPI.showMessage(i18n.def['action_005']);
                    }
                    
//                    for (var i in ids) {
//                        var ret = grid2.getRowData(ids[i]);//取得每個欄位的名稱 
//                        myArray[i] = $.trim(ret.userName);
//                    }//close for
					for (var i = 0; i < ids.length; i++) {
					    var ret = grid2.getRowData(ids[i]);
					    myArray.push($.trim(ret.userName));
					}

                    $.thickbox.close();
                    $("#present").val(myArray);
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    });
});
