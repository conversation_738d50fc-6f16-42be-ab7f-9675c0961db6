var pageAction = {
    handler: 'cls1221gridhandler',
    grid: null,
    oid: null,
    build: function(){
        pageAction.grid = $("#gridview").iGrid({
            // localFirst: true,
            handler: 'cls1221gridhandler',
            height: 400,
            action: 'queryViewC122M01E',
            rowNum: 17,
            rownumbers: true,
            sortname: 'grpCntrNo',
            sortorder: 'desc',
            postData: {
				
			},
            colModel: [{
                name: 'oid',
                hidden: true
            }, {
                colHeader: i18n.cls1220v12["C122M01E.grpCntrNo"], // 團貸母戶編號
                align: "left",
                width: 60, // 設定寬度
                sortable: true, // 是否允許排序
                name: 'grpCntrNo'
            }, {
                colHeader: i18n.cls1220v12["C122M01E.custId"], // 統一編號
                align: "left",
                width: 60, // 設定寬度
                sortable: false, // 是否允許排序
                name: 'custId'
            }, {
                colHeader: i18n.cls1220v12["C122M01E.custName"], // 姓名
                align: "left",
                width: 60, // 設定寬度
                sortable: true, // 是否允許排序
                name: 'custName'
            }, {
                colHeader: i18n.cls1220v12["C122M01E.empNo"], // 職工編號
                align: "center",
                width: 60, // 設定寬度
                sortable: true, // 是否允許排序
                name: 'empNo'
            }, {
                colHeader: i18n.cls1220v12["C122M01E.createTime"], // 建立日期
                align: "center",
                width: 80, // 設定寬度
                sortable: true, // 是否允許排序
                name: 'createTime'
            }]
        });
        
        // build button
        // 產生excel
        $("#buttonPanel").find("#btnCreateExl").click(function(){     
        	pageAction.openExcel();
        }) // 篩選
        .end().find('#btnFilter').click(function(){
            pageAction.openFilter();
        });
        
    },
    /**
     * 篩選
     */
    openFilter: function(){
    	var _id = "C122M01E_tb";
		var _form = _id+"_form";
		if ($("#"+_id).length == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");
			dyna.push("<form id='"+_form+"'>請輸入欲查詢之團貸母戶編號：");
			dyna.push("	<table class='tb2'><tr><td class='hd2'>團貸母戶編號&nbsp;&nbsp;&nbsp;&nbsp;</td>");
			dyna.push(" <td>");
			dyna.push(" <td><select id='grpCntrNo' name='grpCntrNo' combotype='2'>&nbsp;&nbsp;&nbsp;&nbsp;");
			dyna.push(" </td></tr></table>");
			dyna.push("</form>");
			
			dyna.push("</div>");
			
		     $('body').append(dyna.join(""));
		}
		//clear data
		$("#"+_form).reset();
    	
		$.ajax({
		       handler: "cls1220m01formhandler",
		       action: "getGrpCntrNoList"
		}).done(function(json){
			$("#grpCntrNo").setOptions({
				"": ""
			}, true);
			$("#grpCntrNo").setOptions(json, true);
		});
		 
    	$("#"+_id).thickbox({
			title : i18n.def['query'] || '篩選',
			width : 400, height : 150, modal : false,
			align : 'center', valign: 'bottom', i18n: i18n.def,
			buttons : {
				'sure' : function(){
					var $form = $('#'+_form);
					if ($form.valid()){	
						$.thickbox.close();
						pageAction.reloadGrid($form.serializeData());
					}
				}
			}
		});
    },
	/**
	 * 重整資料表
	 */
	reloadGrid : function(data){
		if (data){
			pageAction.grid.jqGrid("setGridParam", {
				postData : data,
				page : 1,
				search : true
			}).trigger("reloadGrid");
		}else{
			pageAction.grid.trigger('reloadGrid');
		}
	},
	/**
     * 產生Excel
     */
    openExcel: function(){
    	var _id = "C122M01E_tb_excel";
		var _form = _id+"_form_excel";
		if ($("#"+_id).length == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");
			dyna.push("<form id='"+_form+"'>請輸入欲查詢之團貸母戶編號：");
			dyna.push("	<table class='tb2'><tr><td class='hd2'>團貸母戶編號&nbsp;&nbsp;&nbsp;&nbsp;</td>");
			dyna.push(" <td>");
			dyna.push(" <td><select id='grpCntrNo_excel' name='grpCntrNo_excel' combotype='2'>&nbsp;&nbsp;&nbsp;&nbsp;");
			dyna.push(" </td></tr></table>");
			dyna.push("</form>");
			
			dyna.push("</div>");
			
		     $('body').append(dyna.join(""));
		}
		//clear data
		$("#"+_form).reset();
    	
		$.ajax({
		       handler: "cls1220m01formhandler",
		       action: "getGrpCntrNoList"
		}).done(function(json){
			$("#grpCntrNo_excel").setOptions({
				"": ""
			}, true);
			$("#grpCntrNo_excel").setOptions(json, true);
		});
		 
    	$("#"+_id).thickbox({
			title : i18n.def['query'] || '篩選',
			width : 400, height : 150, modal : false,
			align : 'center', valign: 'bottom', i18n: i18n.def,
			buttons : {
				'sure' : function(){
					$.form.submit({
                     	url: __ajaxHandler,
                  		target : "_blank",
                  		data : {
                  			_pa : 'lmsdownloadformhandler',
                  			'grpCntrNo': $("#grpCntrNo_excel").val(),
                            'fileDownloadName' : 'grpData.xls',
                  			'serviceName' : "cls1220r12rptservcie"
                  		}
                  	 });
                  	 $.thickbox.close();
                  	 
						//pageAction.reloadGrid($form.serializeData());
				}
			}
		});
    }
}

$(function(){
    pageAction.build();
});
