/* 
 * L120S21A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** LGD額度共用檔 J-110-0986_05097_B1001 於簽報書新增LGD欄位 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L120S21A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class L120S21A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/**
	 * 文件編號
	 * <p/>
	 * 簽報書MAINID
	 */
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 共用序號 **/
	@Size(max = 14)
	@Column(name = "CNTRNOCO_S21A", length = 14, columnDefinition = "CHAR(14)")
	private String cntrNoCo_s21a;

	/** 共用額度幣別 **/
	@Size(max = 3)
	@Column(name = "CURRCO_S21A", length = 3, columnDefinition = "CHAR(3)")
	private String currCo_s21a;

	/** 共用額度限額 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "FACTAMTCO_S21A", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal factAmtCo_s21a;

	/** 共用額度限額台幣 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "FACTAMTCOTWD_S21A", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal factAmtCoTwd_s21a;

	/** 本案授信戶額度序號 **/
	@Size(max = 12)
	@Column(name = "CNTRNO_S21A", length = 12, columnDefinition = "CHAR(12)")
	private String cntrNo_s21a;

	/** 現請額度幣別 **/
	@Size(max = 3)
	@Column(name = "CURRENTAPPLYCURR_S21A", length = 3, columnDefinition = "CHAR(3)")
	private String currentApplyCurr_s21a;

	/** 現請額度金額原幣 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "CURRENTAPPLYAMT_S21A", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal currentApplyAmt_s21a;

	/** 現請額度金額台幣 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "CURRENTAPPLYAMTTWD_S21A", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal currentApplyAmtTwd_s21a;

	/** 餘額幣別 **/
	@Size(max = 3)
	@Column(name = "BLCURR_S21A", length = 3, columnDefinition = "CHAR(3)")
	private String blCurr_s21a;

	/** 餘額金額 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "BLAMT_S21A", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal blAmt_s21a;

	/** 餘額金額台幣 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "BLAMTTWD_S21A", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal blAmtTwd_s21a;

	/** 應收利息幣別 **/
	@Size(max = 3)
	@Column(name = "RCVCURR_S21A", length = 3, columnDefinition = "CHAR(3)")
	private String rcvCurr_s21a;

	/** 應收利息金額 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "RCVINT_S21A", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal rcvInt_s21a;

	/** 應收利息等值台幣金額 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "RCVINTTWD_S21A", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal rcvIntTwd_s21a;

	/**
	 * 佔比
	 * <p/>
	 * ％
	 */
	@Digits(integer = 5, fraction = 2, groups = Check.class)
	@Column(name = "RATIO_S21A", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal ratio_s21a;

	/**
	 * 第一次分配結果
	 * <p/>
	 * 台幣
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "ALLOCATE1", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal allocate1;

	/**
	 * 分配後金額是否低於餘額
	 * <p/>
	 * Y/N
	 */
	@Size(max = 1)
	@Column(name = "ISLOWERBAL", length = 1, columnDefinition = "CHAR(1)")
	private String isLowerBal;

	/**
	 * 第二次分配結果
	 * <p/>
	 * 台幣
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "ALLOCATE2", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal allocate2;

	/** 分配後額度 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "ALLOCATE3", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal allocate3;

	/** 分配後額度序號額度 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "ALLOCATEF", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal allocateF;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/** EAD模型版本_大版 **/
	@Digits(integer = 4, fraction = 0, groups = Check.class)
	@Column(name = "EADVER1", columnDefinition = "DECIMAL(4,0)")
	private Integer eadVer1;

	/** EAD模型版本_小版 **/
	@Digits(integer = 4, fraction = 0, groups = Check.class)
	@Column(name = "EADVER2", columnDefinition = "DECIMAL(4,0)")
	private Integer eadVer2;

	/**
	 * 現請額度－是否循環使用
	 * <p/>
	 * 1/N.不循環使用<br/>
	 * 2/Y.循環使用
	 */
	@Column(name = "REUSE_S21A", length = 1, columnDefinition = "CHAR(1)")
	private String reUse_s21a;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/**
	 * 取得文件編號
	 * <p/>
	 * 簽報書MAINID
	 */
	public String getMainId() {
		return this.mainId;
	}

	/**
	 * 設定文件編號
	 * <p/>
	 * 簽報書MAINID
	 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得共用序號 **/
	public String getCntrNoCo_s21a() {
		return this.cntrNoCo_s21a;
	}

	/** 設定共用序號 **/
	public void setCntrNoCo_s21a(String value) {
		this.cntrNoCo_s21a = value;
	}

	/** 取得共用額度幣別 **/
	public String getCurrCo_s21a() {
		return this.currCo_s21a;
	}

	/** 設定共用額度幣別 **/
	public void setCurrCo_s21a(String value) {
		this.currCo_s21a = value;
	}

	/** 取得共用額度限額 **/
	public BigDecimal getFactAmtCo_s21a() {
		return this.factAmtCo_s21a;
	}

	/** 設定共用額度限額 **/
	public void setFactAmtCo_s21a(BigDecimal value) {
		this.factAmtCo_s21a = value;
	}

	/** 取得共用額度限額台幣 **/
	public BigDecimal getFactAmtCoTwd_s21a() {
		return this.factAmtCoTwd_s21a;
	}

	/** 設定共用額度限額台幣 **/
	public void setFactAmtCoTwd_s21a(BigDecimal value) {
		this.factAmtCoTwd_s21a = value;
	}

	/** 取得本案授信戶額度序號 **/
	public String getCntrNo_s21a() {
		return this.cntrNo_s21a;
	}

	/** 設定本案授信戶額度序號 **/
	public void setCntrNo_s21a(String value) {
		this.cntrNo_s21a = value;
	}

	/** 取得現請額度幣別 **/
	public String getCurrentApplyCurr_s21a() {
		return this.currentApplyCurr_s21a;
	}

	/** 設定現請額度幣別 **/
	public void setCurrentApplyCurr_s21a(String value) {
		this.currentApplyCurr_s21a = value;
	}

	/** 取得現請額度金額原幣 **/
	public BigDecimal getCurrentApplyAmt_s21a() {
		return this.currentApplyAmt_s21a;
	}

	/** 設定現請額度金額原幣 **/
	public void setCurrentApplyAmt_s21a(BigDecimal value) {
		this.currentApplyAmt_s21a = value;
	}

	/** 取得現請額度金額台幣 **/
	public BigDecimal getCurrentApplyAmtTwd_s21a() {
		return this.currentApplyAmtTwd_s21a;
	}

	/** 設定現請額度金額台幣 **/
	public void setCurrentApplyAmtTwd_s21a(BigDecimal value) {
		this.currentApplyAmtTwd_s21a = value;
	}

	/** 取得餘額幣別 **/
	public String getBlCurr_s21a() {
		return this.blCurr_s21a;
	}

	/** 設定餘額幣別 **/
	public void setBlCurr_s21a(String value) {
		this.blCurr_s21a = value;
	}

	/** 取得餘額金額 **/
	public BigDecimal getBlAmt_s21a() {
		return this.blAmt_s21a;
	}

	/** 設定餘額金額 **/
	public void setBlAmt_s21a(BigDecimal value) {
		this.blAmt_s21a = value;
	}

	/** 取得餘額金額台幣 **/
	public BigDecimal getBlAmtTwd_s21a() {
		return this.blAmtTwd_s21a;
	}

	/** 設定餘額金額台幣 **/
	public void setBlAmtTwd_s21a(BigDecimal value) {
		this.blAmtTwd_s21a = value;
	}

	/** 取得應收利息幣別 **/
	public String getRcvCurr_s21a() {
		return this.rcvCurr_s21a;
	}

	/** 設定應收利息幣別 **/
	public void setRcvCurr_s21a(String value) {
		this.rcvCurr_s21a = value;
	}

	/** 取得應收利息金額 **/
	public BigDecimal getRcvInt_s21a() {
		return this.rcvInt_s21a;
	}

	/** 設定應收利息金額 **/
	public void setRcvInt_s21a(BigDecimal value) {
		this.rcvInt_s21a = value;
	}

	/** 取得應收利息等值台幣金額 **/
	public BigDecimal getRcvIntTwd_s21a() {
		return this.rcvIntTwd_s21a;
	}

	/** 設定應收利息等值台幣金額 **/
	public void setRcvIntTwd_s21a(BigDecimal value) {
		this.rcvIntTwd_s21a = value;
	}

	/**
	 * 取得佔比
	 * <p/>
	 * ％
	 */
	public BigDecimal getRatio_s21a() {
		return this.ratio_s21a;
	}

	/**
	 * 設定佔比
	 * <p/>
	 * ％
	 **/
	public void setRatio_s21a(BigDecimal value) {
		this.ratio_s21a = value;
	}

	/**
	 * 取得第一次分配結果
	 * <p/>
	 * 台幣
	 */
	public BigDecimal getAllocate1() {
		return this.allocate1;
	}

	/**
	 * 設定第一次分配結果
	 * <p/>
	 * 台幣
	 **/
	public void setAllocate1(BigDecimal value) {
		this.allocate1 = value;
	}

	/**
	 * 取得分配後金額是否低於餘額
	 * <p/>
	 * Y/N
	 */
	public String getIsLowerBal() {
		return this.isLowerBal;
	}

	/**
	 * 設定分配後金額是否低於餘額
	 * <p/>
	 * Y/N
	 **/
	public void setIsLowerBal(String value) {
		this.isLowerBal = value;
	}

	/**
	 * 取得第二次分配結果
	 * <p/>
	 * 台幣
	 */
	public BigDecimal getAllocate2() {
		return this.allocate2;
	}

	/**
	 * 設定第二次分配結果
	 * <p/>
	 * 台幣
	 **/
	public void setAllocate2(BigDecimal value) {
		this.allocate2 = value;
	}

	/** 取得分配後額度 **/
	public BigDecimal getAllocate3() {
		return this.allocate3;
	}

	/** 設定分配後額度 **/
	public void setAllocate3(BigDecimal value) {
		this.allocate3 = value;
	}

	/** 取得分配後額度序號額度 **/
	public BigDecimal getAllocateF() {
		return this.allocateF;
	}

	/** 設定分配後額度序號額度 **/
	public void setAllocateF(BigDecimal value) {
		this.allocateF = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 取得EAD模型版本_大版 **/
	public Integer getEadVer1() {
		return this.eadVer1;
	}

	/** 設定EAD模型版本_大版 **/
	public void setEadVer1(Integer value) {
		this.eadVer1 = value;
	}

	/** 取得EAD模型版本_小版 **/
	public Integer getEadVer2() {
		return this.eadVer2;
	}

	/** 設定EAD模型版本_小版 **/
	public void setEadVer2(Integer value) {
		this.eadVer2 = value;
	}

	public void setReUse_s21a(String reUse_s21a) {
		this.reUse_s21a = reUse_s21a;
	}

	public String getReUse_s21a() {
		return reUse_s21a;
	}
}
