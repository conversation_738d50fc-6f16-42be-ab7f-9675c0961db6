package com.mega.eloan.lms.fms.handler.form;

import java.sql.Timestamp;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.DocLogEnum;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.formatter.UserNameFormatter;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.DocOpener;
import com.mega.eloan.common.model.DocOpener.OpenTypeCode;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.MISRows;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.fms.pages.CLS3001M01Page;
import com.mega.eloan.lms.mfaloan.bean.LNF07A;
import com.mega.eloan.lms.mfaloan.service.LNLNF07AService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C900S02D;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;


@Scope("request")
@Controller("cls3001formhandler")
public class CLS3001FormHandler extends AbstractFormHandler {

	@Resource
	CLSService clsService;
	
	@Resource
	ICustomerService iCustomerService;
	
	@Resource
	UserInfoService userInfoService;
	
	@Resource
	TempDataService tempDataService;
	
	@Resource
	DocCheckService docCheckService;
	
	@Resource
	DocLogService docLogService;
	
	@Resource
	UserInfoService userService;
	
	@Resource
	BranchService branchService;
	
	@Resource
	MisdbBASEService msisdbBASEService;
	
	@Resource
	LNLNF07AService lnLNF07AService;
	
	Properties prop = MessageBundleScriptCreator
		.getComponentResource(CLS3001M01Page.class);

	Properties prop_AbstractEloanPage = MessageBundleScriptCreator
		.getComponentResource(AbstractEloanPage.class);
	
	private static final String LNF07A_KEY_1 = "CLS_CONTROL_CNTRNO";
	
	@DomainAuth(AuthType.Modify)
	public IResult deleteMark(PageParameters params)
			throws CapException {
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		String KEY = "saveOkFlag";
		
		String list = params.getString("list");
		String docStatus = params.getString("docStatus");
		
		result.set(KEY, false);
		
		C900S02D meta = null;
		if (Util.isNotEmpty(list)) {
			meta = clsService.findC900S02D_oid(list);	
			
			if(meta!=null){
				
				List<DocOpener> docOpeners = docCheckService.findByMainId(meta.getMainId());
				for(DocOpener docOpener : docOpeners){
					if(OpenTypeCode.Writing.getCode().equals(docOpener.getOpenType())){
						HashMap<String, String> hm = new HashMap<String, String>();
						hm.put("userId", docOpener.getOpener());
						hm.put("userName",
								userInfoService.getUserName(docOpener.getOpener()));
						// 此文件正由 [${userId}-${userName}] 開啟中!<br/>系統將以唯讀狀態開啟此文件。
						throw new CapMessageException(RespMsgHelper.getMessage("EFD0009", hm), getClass());
					}
				}
				
				//判斷編制中刪除，其它 更新DeletedTime
				if(Util.equals(FlowDocStatusEnum.編製中.getCode(), docStatus)){
					clsService.daoDelete(meta);
					// 刪除文件異動記錄
					docLogService.deleteLog(meta.getOid());
				}
				result.set(KEY, true);
			}			
		}
		return defaultResult(params, meta, result);
	}
		
	private CapAjaxFormResult defaultResult(PageParameters params, C900S02D meta,
			CapAjaxFormResult result) throws CapException {		
		// required information
		result.set(EloanConstants.PAGE, Util.trim(params.getString(EloanConstants.PAGE)));
		result.set(EloanConstants.MAIN_OID, Util.trim(meta.getOid()));
		result.set(EloanConstants.MAIN_DOC_STATUS, Util.trim(meta.getDocStatus()));
		result.set(EloanConstants.MAIN_ID, Util.trim(meta.getMainId()));		
		return result;
	}
	
	@DomainAuth(value = AuthType.Query)
	public IResult query(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		UserNameFormatter userNameFormatter;
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C900S02D meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = clsService.findC900S02D_oid(mainOid);	
			
			LMSUtil.addMetaToResult(result, meta, new String[]{"custId", "dupNo", "custName", 
					"ownBrId", "createTime", "updateTime"
						, "category", "caseBrId", "cntrNo", "document_no", "curr", "applyAmt"
					});
			result.set("creator", Util.trim(userInfoService.getUserName(meta.getCreator())));
			result.set("updater", Util.trim(userInfoService.getUserName(meta.getUpdater())));
			result.set("caseBrIdDesc", caseBrIdDesc(meta.getCaseBrId()));
			
			String docStatus = meta.getDocStatus();			
			if(Util.equals(FlowDocStatusEnum.編製中.getCode(), docStatus)){
				docStatus = prop_AbstractEloanPage.getProperty("docStatus.010");
			}else if(Util.equals(FlowDocStatusEnum.待覆核.getCode(), docStatus)){
				docStatus = prop_AbstractEloanPage.getProperty("docStatus.020");
			}else if(Util.equals(FlowDocStatusEnum.已核准.getCode(), docStatus)){
				docStatus = prop_AbstractEloanPage.getProperty("docStatus.030");
			}
			result.set("docStatus", docStatus);
			
			
		}else{
			meta = new C900S02D();
			// common 欄位塞值
			meta.setDocStatus(FlowDocStatusEnum.編製中);
			
			//新增,畫面預設值
			result.set("docStatus", prop_AbstractEloanPage.getProperty("docStatus.010"));
			result.set("category", "A"); 
					
			// 文件異動紀錄區
			userNameFormatter = new UserNameFormatter(userService);
			result.set("creator", userNameFormatter.reformat(user.getUserId()));
			
		}
		
		return defaultResult(params, meta, result);
	}	
	
	private String caseBrIdDesc(String s){		
		String val = Util.trim(s);
		if(Util.isNotEmpty(val)){
			return branchService.getBranchName(val);
		}
		return "";
	}
	
	/**
	 * 儲存
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(AuthType.Modify)
	public IResult saveMain(PageParameters params)
			throws CapException {
		return _saveAction(params, "N");
	}
	
	private CapAjaxFormResult _saveAction(PageParameters params,String tempSave)
	throws CapException{
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, tempSave);
		boolean allowIncomplete = Util.equals("Y", params.getString("allowIncomplete"));
		//===
		String KEY = "saveOkFlag";
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		result.set(KEY, false);
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C900S02D meta = null;
		
		if(Util.isNotEmpty(mainOid)) {
			meta = clsService.findC900S02D_oid(mainOid);
			String docStatus = Util.trim(meta==null?"":meta.getDocStatus());
			if(Util.equals(FlowDocStatusEnum.已核准.getCode(), docStatus) ){
				//not change
			}else{
				
				meta.setUpdater(user.getUserId());
				meta.setUpdateTime(CapDate.getCurrentTimestamp());
				meta.setRandomCode(IDGenerator.getRandomCode());				
			}
		}else{
			Timestamp nowTS = CapDate.getCurrentTimestamp();
			
			meta = new C900S02D();
			//---			
			meta.setMainId(IDGenerator.getUUID());
			meta.setOwnBrId(user.getUnitNo());
			meta.setUnitType(user.getUnitType());		
			meta.setCreator(user.getUserId());
			meta.setCreateTime(nowTS);
			meta.setUpdater(user.getUserId());
			meta.setUpdateTime(nowTS);
			meta.setIsClosed(false);
			meta.setRandomCode(IDGenerator.getRandomCode());	

			if(Util.isEmpty(meta.getDocStatus())){
				meta.setDocStatus(FlowDocStatusEnum.編製中.getCode());
			}
		}		
		//---
		CapBeanUtil.map2Bean(params, meta, new String[] {"custId", "dupNo", "custName"
				, "category", "caseBrId", "cntrNo", "document_no", "curr", "applyAmt" });
		//---		
		if(true){
			String msg = checkIncompleteMsg(meta);
			if(Util.isNotEmpty(msg)){
				if(allowIncomplete){
					result.set("IncompleteMsg", msg);			
				}else{
					throw new CapMessageException(msg, getClass());	
				}
			}
		}
		clsService.save(meta);
		mainOid = meta.getOid();
		//===========================
		result.set(KEY, true);	
		
		return defaultResult(params, meta, result);
	}
	
	private String checkIncompleteMsg(C900S02D model){
		List<String> list = new ArrayList<String>();
		if(Util.isEmpty(Util.trim(model.getCategory()))){
			list.add(MessageFormat.format(prop.getProperty("msg.01"), prop.getProperty("C900S02D.category")));
		}
		if(Util.isEmpty(Util.trim(model.getCntrNo()))){
			list.add(MessageFormat.format(prop.getProperty("msg.01"), prop.getProperty("C900S02D.cntrNo")));
		}		
		return StringUtils.join(list, "<br/>"); 
	}
	
	@DomainAuth(AuthType.Modify + AuthType.Accept)
	public IResult flowAction(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String decisionExpr = Util.trim(params.getString("decisionExpr"));
		
		C900S02D meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = clsService.findC900S02D_oid(mainOid);
			String docStatus = Util.trim(meta.getDocStatus());
			
			String errMsg = "";
			String nextStatus = "";
			DocLogEnum _DocLogEnum = null;
			if(Util.equals(FlowDocStatusEnum.編製中.getCode(), docStatus)){				
				if(true){
					List<LNF07A> list = lnLNF07AService.sel_by_key1_key2_key3(LNF07A_KEY_1, convert_to_lnf07a_key(meta.getCategory()), meta.getCntrNo());
					if(list.size()>0){
						Map<String, String> map = clsService.get_codeTypeWithOrder("C900S02D_category");
						errMsg = "中心控制檔已有["+prop.getProperty("C900S02D.category")+"="+LMSUtil.getDesc(map, meta.getCategory())
						+"，"
						+prop.getProperty("C900S02D.cntrNo")+"="+meta.getCntrNo()+"]資料，無需重複建檔";
					}
				}
				//呈主管
				nextStatus = FlowDocStatusEnum.待覆核.getCode();
				_DocLogEnum = DocLogEnum.FORWARD;
			}else if(Util.equals(FlowDocStatusEnum.待覆核.getCode(), docStatus)){
				if(Util.equals("核定", decisionExpr) ){
					//檢查經辦和主管是否為同一人
					if(Util.equals(user.getUserId(), meta.getUpdater())){
						errMsg = RespMsgHelper.getMessage("EFD0053");	
					} else{
						nextStatus = FlowDocStatusEnum.已核准.getCode();
						_DocLogEnum = DocLogEnum.ACCEPT;	
					}
				}else if(Util.equals("退回", decisionExpr)){
					nextStatus = FlowDocStatusEnum.編製中.getCode();
					_DocLogEnum = DocLogEnum.BACK;
				}
			}else if(Util.equals(FlowDocStatusEnum.已核准.getCode(), docStatus)){	

			}
			
			if(Util.isNotEmpty(errMsg)){
				throw new CapMessageException(errMsg, getClass());
			}else{
				if(Util.isEmpty(nextStatus)){
					throw new CapMessageException("流程異常["+docStatus+"]", getClass());
				}	
			}
				
			if(true){
				if(Util.equals(nextStatus, FlowDocStatusEnum.已核准.getCode())){
					meta.setApprover(user.getUserId());
					meta.setApproveTime(CapDate.getCurrentTimestamp());
					if(true){ //上傳中心
						List<LNF07A> r = new ArrayList<LNF07A>();
						if(true){
							LNF07A lnf07a = new LNF07A();
							lnf07a.setLnf07a_key_1(LNF07A_KEY_1);							
							lnf07a.setLnf07a_key_2(convert_to_lnf07a_key(meta.getCategory()));
							lnf07a.setLnf07a_key_3(meta.getCntrNo());
							lnf07a.setLnf07a_key_4("");
							lnf07a.setLnf07a_key_5("");
							lnf07a.setLnf07a_content_1("C900S02D="+meta.getMainId());
							lnf07a.setLnf07a_content_2("Approved="+meta.getApprover()+","+TWNDate.toAD(meta.getApproveTime()));
							lnf07a.setLnf07a_content_3("");
							lnf07a.setLnf07a_content_4("");
							lnf07a.setLnf07a_content_5("");
							r.add(lnf07a);
						}
						
						for(LNF07A lnf07a : r){
							if(Util.isEmpty(Util.trim(lnf07a.getLnf07a_key_2()))){
								throw new CapMessageException("lnf07a_key_2不可空白", getClass());
							}
							if(Util.isEmpty(Util.trim(lnf07a.getLnf07a_key_3()))){
								throw new CapMessageException("lnf07a_key_3不可空白", getClass());
							}
						}						
						//===============
						lnLNF07AService.delete_by_key1_key2_key3(r);
						//===============
						MISRows<LNF07A> misRowsLNF07A = new MISRows<LNF07A>(LNF07A.class);
						misRowsLNF07A.setValues(r);

						msisdbBASEService.insert(misRowsLNF07A.getMsgFmtParam("LN"),
								misRowsLNF07A.getTypes(), misRowsLNF07A.getValues());
					}
				}else if(Util.equals(nextStatus, FlowDocStatusEnum.編製中.getCode())){
					meta.setApprover(null);
					meta.setApproveTime(null);
				}else if(Util.equals(nextStatus, FlowDocStatusEnum.待覆核.getCode())){
					meta.setApprover(null);
					meta.setApproveTime(null);
				}
				meta.setDocStatus(nextStatus);
				//用 daoSave 避免把 approver 寫到 updater
				clsService.daoSave(meta);
				

				if(_DocLogEnum!=null){
					docLogService.record(meta.getOid(), _DocLogEnum);	
				}				
			}
			tempDataService.deleteByMainId(meta.getMainId());
			docCheckService.unlockDocByMainIdUser(meta.getMainId(), user.getUserId());
		}
		return defaultResult( params, meta, result);
	}
	
	private String convert_to_lnf07a_key(String c900s02d_category){
		
		if(Util.equals("A", c900s02d_category) || Util.equals("B", c900s02d_category)){
			return "YUNGCHING_PROJ_201805";
		}
		return "unknown["+c900s02d_category+"]";
	}
	
}
