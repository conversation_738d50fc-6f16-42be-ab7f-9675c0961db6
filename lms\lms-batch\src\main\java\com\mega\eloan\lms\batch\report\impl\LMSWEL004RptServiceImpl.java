package com.mega.eloan.lms.batch.report.impl;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.TreeSet;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.inet.report.Engine;
import com.inet.report.ReportException;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.report.AbstractIISIReportService;
import com.mega.eloan.lms.batch.report.LMSWEL004RptService;
import com.mega.eloan.lms.dao.C122M01ADao;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapFormatException;
import tw.com.iisi.cap.formatter.NumericFormatter;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapMath;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

@Service("lmswel004rptservice")
public class LMSWEL004RptServiceImpl extends AbstractIISIReportService
		implements LMSWEL004RptService {

	@Resource
	BranchService branchService;
	@Resource
	C122M01ADao c122m01aDao;

	@Override
	public ReportData getReportParameter(PageParameters params, ReportData reportData,
			Engine engine) {

		NumericFormatter nf = new NumericFormatter("#,###.##");

		String applyKind = UtilConstants.C122_ApplyKind.H;
		Integer[] megaCNT = new Integer[8], sumCNT = new Integer[8];
		Arrays.fill(megaCNT, 0);// 將全行件數合計設成0
		BigDecimal[] megaAMT = new BigDecimal[8], sumAMT = new BigDecimal[8];
		Arrays.fill(megaAMT, BigDecimal.ZERO); // 將全行金額合計設成0

		String[] applyStatus = { UtilConstants.C122_ApplyStatus.不承做,
				UtilConstants.C122_ApplyStatus.轉臨櫃,
				UtilConstants.C122_ApplyStatus.已核准 };

		TreeSet<String> brNoSet = c122m01aDao
				.queryOwnBranchListC122M01AByApplyStatus(applyKind, applyStatus);
		List<List<String>> details = new ArrayList<List<String>>();

		// 執行報表的當月份
		String yyyy_mm = Util.getLeftStr(TWNDate.toAD(CapDate.getCurrentTimestamp()), 7);
		try {
			for (String brNo : brNoSet) {
				
				Arrays.fill(sumCNT, 0); // 先將該分行件數合計設成0
				Arrays.fill(sumAMT, BigDecimal.ZERO); // 先將該分行金額合計設成0

				// 先取當月資料
				List<Object[]> singleData = c122m01aDao.getCloseCaseByBranchAndMonth(applyKind, brNo, yyyy_mm);

				// 再取累計資料 => 此處依 applyStatus 去sum , 在以下的 Java 程式, 再去抓出[不承做, 轉臨櫃, 已核貸]的資料 
				List<Object[]> summaryData = c122m01aDao.getOnLineLoanByBranch(applyKind, brNo);

				if (singleData.isEmpty() && summaryData.isEmpty()) {
					continue;
				}

				List<String> detail = new ArrayList<String>();

				detail.add(brNo + " "+ branchService.getBranchName(brNo));

				for (String as : applyStatus) {
					boolean statusFlag = false;

					// 處理當月結案資料
					for (Object[] oArray : singleData) {
						String statusResult = CapString.trimNull(oArray[0]);

						Integer CNT = ((Integer) oArray[1]);
						BigDecimal AMT = ((BigDecimal) oArray[2]);

						if (as.equals(statusResult)) {
							if (as.equals(UtilConstants.C122_ApplyStatus.不承做)) {
								sumCNT[0] += CNT;
								sumAMT[0] = sumAMT[0].add(AMT);
							} else if (as.equals(UtilConstants.C122_ApplyStatus.轉臨櫃)) {
								sumCNT[1] += CNT;
								sumAMT[1] = sumAMT[1].add(AMT);
							} else if (as.equals(UtilConstants.C122_ApplyStatus.已核准)) {
								sumCNT[2] += CNT;
								sumAMT[2] = sumAMT[2].add(AMT);
							}

							sumCNT[3] += CNT; // 合計
							sumAMT[3] = sumAMT[3].add(AMT);

							detail.add(String.valueOf(CNT));
							detail.add(nf.reformat(CapMath.setScale(
									String.valueOf(AMT), 0)));
							statusFlag = true;
							break;
						}

					}
					if (!statusFlag) { // 沒資料補0
						detail.add("0");
						detail.add("0");
					}

					statusFlag = false;
					// 處理累計資料
					for (Object[] oArray : summaryData) {
						String statusResult = CapString.trimNull(oArray[0]);

						Integer CNT = ((Integer) oArray[1]);
						BigDecimal AMT = ((BigDecimal) oArray[2]);

						if (as.equals(statusResult)) {

							if (as.equals(UtilConstants.C122_ApplyStatus.不承做)) {
								sumCNT[4] += CNT;
								sumAMT[4] = sumAMT[4].add(AMT);
							} else if (as.equals(UtilConstants.C122_ApplyStatus.轉臨櫃)) {
								sumCNT[5] += CNT;
								sumAMT[5] = sumAMT[5].add(AMT);
							} else if (as.equals(UtilConstants.C122_ApplyStatus.已核准)) {
								sumCNT[6] += CNT;
								sumAMT[6] = sumAMT[6].add(AMT);
							}

							sumCNT[7] += CNT; // 合計
							sumAMT[7] = sumAMT[7].add(AMT);

							detail.add(String.valueOf(CNT));
							detail.add(nf.reformat(CapMath.setScale(
									String.valueOf(AMT), 0)));
							statusFlag = true;
							break;
						}

					}
					if (!statusFlag) { // 沒資料補0
						detail.add("0");
						detail.add("0");
					}
				} // for (String as : applyStatus) {

				detail.add(String.valueOf(sumCNT[3])); // 分行當月合計件數
				detail.add(nf.reformat(CapMath.setScale(
						String.valueOf(sumAMT[3]), 0))); // 分行當月合計金額

				detail.add(String.valueOf(sumCNT[7])); // 分行累計合計件數
				detail.add(nf.reformat(CapMath.setScale(
						String.valueOf(sumAMT[7]), 0))); // 分行累計合計金額

				details.add(detail);

				// 處理最後每種狀態的累計數
				for (int i = 0; i < 8; i++) {
					megaCNT[i] += sumCNT[i];
					megaAMT[i] = megaAMT[i].add(sumAMT[i]);
				}
			}
			// 全行合計
			for (int i = 0; i < 8; i++) {
				reportData.setField("cnt" + i, megaCNT[i]);
				reportData.setField(
						"amt" + i,
						nf.reformat(CapMath.setScale(
								CapMath.bigDecimalToString(megaAMT[i]), 0)));
			}

			reportData.addDetail(details);
		} catch (CapFormatException e) {
			e.printStackTrace();
		}

		reportData.setField("printDate", CapDate.getCurrentDate("yyyy-MM-dd"));
		return reportData;

	}

	@Override
	public String getReportDefinition() {
		return "report/lms/LLMEL004"; //(rpt 上的 FORM:LLMEL020) SLMS-00037
	}

	@Override
	public byte[] getContent(PageParameters params) throws CapException,
			FileNotFoundException, ReportException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
			baos = (ByteArrayOutputStream) generateReport(params);
			return baos.toByteArray();
		} finally {
			if (baos != null) {
				baos.close();
			}
		}
	}

}
