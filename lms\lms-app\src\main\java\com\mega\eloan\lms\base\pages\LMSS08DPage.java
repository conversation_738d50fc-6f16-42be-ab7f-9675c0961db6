package com.mega.eloan.lms.base.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractOutputPage;

/**
 * <pre>
 * 相關文件-擔保品分頁
 * </pre>
 * 
 * @since 2012/10/1
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/10/1,<PERSON>,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/baselmss08d/{page}")
public class LMSS08DPage extends AbstractOutputPage {

	@Override
	public String getOutputString(ModelMap model, PageParameters params) {
		setNeedHtml(true);		// need html
		return "&nbsp;";
	}

	@Override
	protected String getViewName() {
		// TODO Auto-generated method stub
		return null;
	}
}
