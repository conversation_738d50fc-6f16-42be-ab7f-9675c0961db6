/* 
 * L120S14C.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 本票授權書資料檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L120S14C", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L120S14C extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 
	 * 文件編號<p/>
	 * 新產生時：getUUID()
	 */
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * 文件亂碼<p/>
	 * 每次儲存：getRandomCode()
	 */
	@Size(max=32)
	@Column(name="RANDOMCODE", length=32, columnDefinition="CHAR(32)")
	private String randomCode;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 刪除註記<p/>
	 * 文件刪除時使用(非立即性刪除)
	 */
	@Column(name="DELETEDTIME", columnDefinition="TIMESTAMP")
	private Timestamp deletedTime;

	/** 
	 * 簽發日<p/>
	 * yyyy-MM-dd
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="ISSUEDATE", columnDefinition="DATE")
	private Date issueDate;

	/** 
	 * 簽發金額<p/>
	 * 預設 現請額度
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="ISSUEAMT", columnDefinition="DECIMAL(17,2)")
	private BigDecimal issueAmt;

	/** 立授權書人 **/
	@Size(max=150)
	@Column(name="PRINCIPAL_1", length=150, columnDefinition="VARCHAR(150)")
	private String principal_1;

	/** 
	 * 負責人<p/>
	 * 預設 負責人
	 */
	@Size(max=150)
	@Column(name="FRONTMAN", length=150, columnDefinition="VARCHAR(150)")
	private String frontMan;

	/** 
	 * 地址<p/>
	 * 預設 借款人地址
	 */
	@Size(max=192)
	@Column(name="PRINCIPALADDR_1", length=192, columnDefinition="VARCHAR(192)")
	private String principalAddr_1;

	/** 
	 * 立授權書人<p/>
	 * 預設 負責人
	 */
	@Size(max=150)
	@Column(name="PRINCIPAL_2", length=150, columnDefinition="VARCHAR(150)")
	private String principal_2;

	/** 地址 **/
	@Size(max=192)
	@Column(name="PRINCIPALADDR_2", length=192, columnDefinition="VARCHAR(192)")
	private String principalAddr_2;

	/** 立授權書人 **/
	@Size(max=150)
	@Column(name="PRINCIPAL_3", length=150, columnDefinition="VARCHAR(150)")
	private String principal_3;

	/** 地址 **/
	@Size(max=192)
	@Column(name="PRINCIPALADDR_3", length=192, columnDefinition="VARCHAR(192)")
	private String principalAddr_3;

	/** 立授權書人 **/
	@Size(max=150)
	@Column(name="PRINCIPAL_4", length=150, columnDefinition="VARCHAR(150)")
	private String principal_4;

	/** 地址 **/
	@Size(max=192)
	@Column(name="PRINCIPALADDR_4", length=192, columnDefinition="VARCHAR(192)")
	private String principalAddr_4;

    /** 額度序號 **/
    @Column(name = "CNTRNO", length = 12, columnDefinition = "CHAR(12)")
    private String cntrNo;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 
	 * 取得文件編號<p/>
	 * 新產生時：getUUID()
	 */
	public String getMainId() {
		return this.mainId;
	}
	/**
	 *  設定文件編號<p/>
	 *  新產生時：getUUID()
	 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得文件亂碼<p/>
	 * 每次儲存：getRandomCode()
	 */
	public String getRandomCode() {
		return this.randomCode;
	}
	/**
	 *  設定文件亂碼<p/>
	 *  每次儲存：getRandomCode()
	 **/
	public void setRandomCode(String value) {
		this.randomCode = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 
	 * 取得刪除註記<p/>
	 * 文件刪除時使用(非立即性刪除)
	 */
	public Timestamp getDeletedTime() {
		return this.deletedTime;
	}
	/**
	 *  設定刪除註記<p/>
	 *  文件刪除時使用(非立即性刪除)
	 **/
	public void setDeletedTime(Timestamp value) {
		this.deletedTime = value;
	}

	/** 
	 * 取得簽發日<p/>
	 * yyyy-MM-dd
	 */
	public Date getIssueDate() {
		return this.issueDate;
	}
	/**
	 *  設定簽發日<p/>
	 *  yyyy-MM-dd
	 **/
	public void setIssueDate(Date value) {
		this.issueDate = value;
	}

	/** 
	 * 取得簽發金額<p/>
	 * 預設 現請額度
	 */
	public BigDecimal getIssueAmt() {
		return this.issueAmt;
	}
	/**
	 *  設定簽發金額<p/>
	 *  預設 現請額度
	 **/
	public void setIssueAmt(BigDecimal value) {
		this.issueAmt = value;
	}

	/** 取得立授權書人 **/
	public String getPrincipal_1() {
		return this.principal_1;
	}
	/** 設定立授權書人 **/
	public void setPrincipal_1(String value) {
		this.principal_1 = value;
	}

	/** 
	 * 取得負責人<p/>
	 * 預設 負責人
	 */
	public String getFrontMan() {
		return this.frontMan;
	}
	/**
	 *  設定負責人<p/>
	 *  預設 負責人
	 **/
	public void setFrontMan(String value) {
		this.frontMan = value;
	}

	/** 
	 * 取得地址<p/>
	 * 預設 借款人地址
	 */
	public String getPrincipalAddr_1() {
		return this.principalAddr_1;
	}
	/**
	 *  設定地址<p/>
	 *  預設 借款人地址
	 **/
	public void setPrincipalAddr_1(String value) {
		this.principalAddr_1 = value;
	}

	/** 
	 * 取得立授權書人<p/>
	 * 預設 負責人
	 */
	public String getPrincipal_2() {
		return this.principal_2;
	}
	/**
	 *  設定立授權書人<p/>
	 *  預設 負責人
	 **/
	public void setPrincipal_2(String value) {
		this.principal_2 = value;
	}

	/** 取得地址 **/
	public String getPrincipalAddr_2() {
		return this.principalAddr_2;
	}
	/** 設定地址 **/
	public void setPrincipalAddr_2(String value) {
		this.principalAddr_2 = value;
	}

	/** 取得立授權書人 **/
	public String getPrincipal_3() {
		return this.principal_3;
	}
	/** 設定立授權書人 **/
	public void setPrincipal_3(String value) {
		this.principal_3 = value;
	}

	/** 取得地址 **/
	public String getPrincipalAddr_3() {
		return this.principalAddr_3;
	}
	/** 設定地址 **/
	public void setPrincipalAddr_3(String value) {
		this.principalAddr_3 = value;
	}

	/** 取得立授權書人 **/
	public String getPrincipal_4() {
		return this.principal_4;
	}
	/** 設定立授權書人 **/
	public void setPrincipal_4(String value) {
		this.principal_4 = value;
	}

	/** 取得地址 **/
	public String getPrincipalAddr_4() {
		return this.principalAddr_4;
	}
	/** 設定地址 **/
	public void setPrincipalAddr_4(String value) {
		this.principalAddr_4 = value;
	}

    /** 取得額度序號 **/
    public String getCntrNo() {
        return this.cntrNo;
    }

    /** 設定額度序號 **/
    public void setCntrNo(String value) {
        if (value != null) {
            // 一率轉大寫額度序號
            value = value.toUpperCase();
        }
        this.cntrNo = value;
    }
}
