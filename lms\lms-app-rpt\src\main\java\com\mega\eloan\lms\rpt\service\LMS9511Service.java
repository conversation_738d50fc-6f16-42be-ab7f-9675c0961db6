/* 
 * LMS9511Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.service;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import jxl.read.biff.BiffException;
import jxl.write.WriteException;
import jxl.write.biff.RowsExceededException;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.response.CapAjaxFormResult;

import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.model.L180R02A;
import com.mega.eloan.lms.model.L784S07A;
import com.mega.eloan.lms.model.LMSBATCH;
import com.mega.eloan.lms.model.LMSRPT;
import com.mega.sso.model.IBranch;

public interface LMS9511Service extends AbstractService {

	@SuppressWarnings("rawtypes")
	public <T extends GenericBean> T findModelByMainId(Class clazz,
			String mainId);

	/**
	 * 找出L180R02A資料
	 * 
	 * @param mainId
	 * @param areaBranchId
	 * @param audit
	 * @return
	 */
	public List<L180R02A> findL180R02AListByIndex02(String mainId,
			String areaBranchId, String audit);

	/**
	 * 找出LMSBATCH
	 * 
	 * @param mainId
	 * @return
	 */
	public LMSBATCH findLMSBATCHByMainId(String mainId);

	/**
	 * 檢核營運中心已敘做案件清單資料是否符合重新產生
	 * 
	 * @param remark
	 * @param unitNo
	 * @param userId
	 * @param rptNo
	 * @param bngDate
	 * @param endDate
	 * @return
	 */
	boolean checkL180R02AData(String unitNo, String rptNo, Date bngDate,
			Date endDate);

	/**
	 * 檢核已敘做案件清單資料是否符合重新產生
	 * 
	 * @param remark
	 * @param unitNo
	 * @param userId
	 * @param rptNo
	 * @param bngDate
	 * @param endDate
	 * @return
	 */
	boolean checkL180R01Data(String remark, String unitNo, String userId,
			String rptNo, Date bngDate, Date endDate);

	/**
	 * 確認是否有相同區間所產生的報表(必須將LMSRPT的該筆資料複製回LMSBATCH 再將LMSRPT資料刪除 並且將相關的子TABLE資料移除)
	 * 
	 * @param remark
	 * @param unitNo
	 * @param userId
	 * @param docType
	 * @param rptNo
	 * @param bngDate
	 * @param endDate
	 * @return
	 * @throws RowsExceededException
	 * @throws BiffException
	 * @throws WriteException
	 * @throws IOException
	 * @throws URISyntaxException
	 * @throws CapMessageException
	 * @throws Exception
	 */
	String checkAndReplaceBatchData(String debugStr, String remark,
			String lmsbatch_branch, String userId, String rptNo, Date bngDate,
			Date endDate) throws RowsExceededException, BiffException,
			WriteException, IOException, URISyntaxException,
			CapMessageException, Exception;

	/**
	 * 新增批次產生報表
	 * 
	 * @param remark
	 * @param unitNo
	 * @param docType
	 * @param rptNo
	 * @param bngDate
	 * @param endDate
	 * @param mainId
	 * @return
	 * @throws RowsExceededException
	 * @throws BiffException
	 * @throws WriteException
	 * @throws IOException
	 * @throws URISyntaxException
	 * @throws CapMessageException
	 * @throws Exception
	 */
	LMSBATCH addbatchData(String remark, String lmsbatch_branch, String rptNo,
			Date bngDate, Date endDate, String mainId)
			throws RowsExceededException, BiffException, WriteException,
			IOException, URISyntaxException, CapMessageException, Exception;

	/**
	 * 批次產生報表
	 * 
	 * @return Boolean
	 * @param String
	 *            mainId
	 * @param checkResult
	 *            是否在處理相同時間的資料
	 * @throws Exception
	 */
	Boolean execBatchData(String mainId, boolean checkResult) throws Exception;

	/**
	 * 產生批次的報表
	 * 
	 * @param batch
	 * @param clsRptName
	 * @return
	 * @throws Exception
	 */
	Boolean createFileForAddBatch(LMSBATCH batch, Map<String, String> lmsRptName, String tmpReMark)
			throws Exception;

	/**
	 * 傳送時，判斷 LMS.L784M01A.sendLastTime 是否有值
	 * 
	 * @param oid
	 * @return
	 */
	int cheaksendTime(LMSRPT lmsRpt);

	/**
	 * 傳送時，判斷 LMS.L784M01A.sendLastTime 是否有值
	 * 
	 * @param oid
	 * @return
	 */
	// int cheaksendTime(List<LMSRPT> lmsRptList);

	/**
	 * L784S07A-List
	 * 
	 * @param ovUnitNo
	 * @param dataDate
	 * @param caseDept
	 * @param ctype
	 * @return
	 * @throws CapException
	 */
	List<L784S07A> findL784S07AByMainId(String mainId) throws CapException;

	/**
	 * 取得L180R02A List
	 * 
	 * @param String
	 */
	List<L180R02A> findL180R02AListByMainId(String mainId);

	/**
	 * 儲存L784S07A model list
	 * 
	 * @param list
	 *            List<L784S07A>
	 */
	void savel784s07aList(List<L784S07A> list);

	/**
	 * 儲存LMSRPT model list
	 * 
	 * @param list
	 *            List<LMSRPT>
	 */
	void saveLmsRptList(List<LMSRPT> list);

	/**
	 * 儲存L180R02A model list
	 * 
	 * @param list
	 *            List<L180R02A>
	 */
	void savel180r02aList(List<L180R02A> list);

	/**
	 * 7. 刪除常董會及申報案件明細檔(L784S07裡特定年月資料
	 */
	void delete784s07(String year, String month, String mainId);

	/**
	 * 7. 常董會及申報案件統計表
	 * 
	 * @param brNo
	 * @param dataDate
	 * @param tCaseDept
	 * @return
	 */

	void findAndSaveLMS180R16Date(String userId, String apprYY, String apprMM,
			String tCaseDept, String mainId);

	/**
	 * 引進當期資料 常董會及申報案件統計表
	 * 
	 * @param dataDate
	 * @return
	 */
	void importLMS180R16Data(Date dataDate, Date dataStartDate);

	CapAjaxFormResult getProduct();

	/**
	 * 建立已敘做授信案件清單報表
	 * 
	 * @param batchtbl
	 */
	void updateLMS180R01(LMSBATCH batchtbl);

	/**
	 * 建立營運中心授權內外已核准/已婉卻授信案件
	 * 
	 * @param batchtbl
	 */
	void updateLMS180R02A(LMSBATCH batchtbl);

	/**
	 * 1. 授信契約已逾期控制表
	 * 
	 * @param OvUnitNo
	 * @param dataDate
	 * @param caseDept
	 * @param ctype
	 * @return
	 * @throws CapException
	 */
	List<Map<String, Object>> findType1ByBrNoAndDate(String OvUnitNo,
			Date dateStartDate, Date dateEndDate, String caseDept, String ctype)
			throws CapException;

	/**
	 * 3. 信保案件未動用屆期清單
	 * 
	 * @param brNo
	 * @param dataDate
	 * @param ctype2
	 * @param caseDept
	 * @return
	 * @throws CapException
	 */
	List<Map<String, Object>> findType3ByBrNoAndDate(String ovUnitNo,
			String benDate, String endDate, String caseDept)
			throws CapException;

	/**
	 * 5. 授信案件統計表(授管處、企劃處用)(Excel：無範本檔)
	 * 
	 * @param ovUnitNo
	 * @param benDate
	 * @param endDate
	 * @param otherCondition
	 * @return
	 * @throws CapException
	 */
	List<Map<String, Object>> findType5ByBrNoAndDate(List<IBranch> ovUnitNo,
			String benDate, String endDate, String otherCondition)
			throws CapException;

	/**
	 * 6. 營運中心每日授權外授信案件清單
	 * 
	 * @param brNo
	 * @param dataDate
	 * @param caseDept
	 * @param ctype
	 * @return
	 * @throws CapException
	 */
	List<Map<String, Object>> findType6ByBrNoAndDate(String brNo,
			String dataDate, String tCaseDept, String ctype)
			throws CapException;

	String buildCLS180R01Xls(LMSBATCH batchtbl);

	List<Map<String, Object>> findCLS180R02Data(LMSBATCH batchtbl);

	List<Map<String, Object>> findCLS180R03Data(LMSBATCH batchtbl,
			boolean by_brNo);

	List<Map<String, Object>> findCLS180R04Data(LMSBATCH batchtbl);

	String createCLS180R05PDF(LMSBATCH batchtbl);

	String createCLS180R06PDF(LMSBATCH batchtbl);

	List<Map<String, Object>> findCLS180R09Data(LMSBATCH batchtbl);

	List<Map<String, Object>> findCLS180R10Data(LMSBATCH batchtbl);

	String createExcel(String rptName, List<Map<String, Object>> data,
			LMSBATCH batchtbl, int firstRow, String countRow,
			String[] colOrder, int base, int zoom, boolean isTransverse);

	String buildCLS180R11Data(LMSBATCH batchtbl);

	/**
	 * 8. 各級授權範圍內承做授信案件統計表
	 * 
	 * @param brNo
	 * @param dataDate
	 * @param tCaseDept
	 * @param ctype
	 * @return
	 */
	List<Map<String, Object>> findType8ByBrNoAndDate(String brNo,
			Date dataDate, String ctype);

	/**
	 * 儲存DOCFILE資料
	 * 
	 * @param brNo
	 *            分行
	 * @param mainId
	 *            MAINID
	 * @param listName
	 *            檔名
	 * @param fileExtName
	 *            副檔名
	 * @return
	 */
	public DocFile saveDocFile(String brNo, String mainId, String listName,
			String fileExtName);

	/**
	 * 查詢LMS.BDOC是否有此筆資料
	 * 
	 * @param mainId
	 * @param FieldId
	 * @return
	 */
	List<DocFile> findDocFile(String mainId, String FieldId);

	/**
	 * J-108-0192_05097_B1001 Web e-Loan企金授信新增每季海外營業單位授信報案考核彙總表
	 * 
	 * type9-營業單位授信報案考核彙總表 抓全年跟抓指定月份
	 * 
	 * @param TWYM_START
	 *            本月/本季起始年月
	 * @param TWYM_END
	 *            本月/本季結束年月
	 * @param startTWYM
	 *            開始年月
	 * @param endTWYM
	 *            結束年月
	 * @param type
	 *            排序方式不同
	 * @return
	 * @throws CapException
	 */
	List<Map<String, Object>> findType9BystartYMendYM(String TWYM_START,
			String TWYM_END, String startTWYM, String endTWYM, String type)
			throws CapException;

	boolean deleteFile(String id, boolean useMainId);

	LMSRPT findByLMSRPT(String mainId);

	public List<LMSRPT> findLMSRptForRPTNO(String rptNo);

	public List<LMSRPT> findFileGrid(String mainId);

	public List<Map<String, Object>> findApproverForL180R02A(String mainId);

	public Date getStartDateForLMSRPT(String date);

	public Date getEndDateForLMSRPT(String date);

	public CapAjaxFormResult checkBatchData(CapAjaxFormResult result,
			String rptNo, String remark, Date bngDate, Date endDate,
			Properties prop);

	public String get_rptName(String codeTypeStr, LMSBATCH batch);

	String buildCLS250R01Xls(LMSBATCH batchtbl);

	public String buildCLS180R21Xls(LMSBATCH batchtbl);

	public String buildCLS180R22Xls(LMSBATCH batchtbl);

	public void sync_CLS180R17_data(Date endDate);

	public int send_CLS180R03B_to_Audit(String lmsRpt_mainId);

	/**
	 * J-111-0554_05097_B1001 Web e-Loan授信修改授信覆審作業系統中之相關事宜
	 * @param brNo
	 * @return
	 */
	public CapAjaxFormResult getStaffNo(String brNo);
}
