package com.mega.eloan.lms.base.service;

import org.kordamp.json.JSONObject;


/**
 * <pre>
 * 評分 Service
 * </pre>
 * 
 * @since 2012/10/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/10/30,Fantasy,new
 *          </ul>
 */
public interface ScoreServiceAU {
	JSONObject scoreAU(String type, JSONObject data, String varVer, String mowType2);
	void setAUDR(JSONObject auDR, JSONObject target);
	public String get_Version_AU();
	public boolean getModelType_3_0(String loanTP);
}
