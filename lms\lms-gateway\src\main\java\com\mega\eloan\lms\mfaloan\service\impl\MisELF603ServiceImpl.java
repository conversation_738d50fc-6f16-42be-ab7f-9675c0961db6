package com.mega.eloan.lms.mfaloan.service.impl;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.mfaloan.service.MisELF603Service;

/**
 * <pre>
 * 貸後追蹤分項紀錄檔 ELF603 (MIS.ELF603)
 * </pre>
 * 
 * @since 2024/05/07
 * @version <ul>
 *          2024/05/07,new
 *          </ul>
 */
@Service
public class MisELF603ServiceImpl extends AbstractMFAloanJdbc implements
		MisELF603Service {

	// 動審表覆核用
	@Override
	public void insertForInside(String uid, String apptime, String cntrNo, int seqno, 
			String esgtype, String esgmodel, String tracond, String traprofik,
			int tramonth, String content, String updater, String updatetime) {

		this.getJdbc().update(
				"ELF603.insertForInside",
				new Object[] {uid, apptime, cntrNo, seqno, esgtype, esgmodel, tracond, traprofik,
						tramonth, content, updater, updatetime});

	}

	@Override
	public void delByUidIdAppDate(String uid, String apptime) {
		this.getJdbc().update("ELF603.delByUidIdAppDate",
				new Object[] { uid, apptime });
	}
	
	@Override
	public void delByUid(String uid) {
		this.getJdbc().update("ELF603.delBUid",
				new Object[] { uid });
	}

	@Override
	public void delByUidIdAppDateCntrNo(String uid, String apptime,
			String cntrNo) {
		this.getJdbc().update("ELF603.delByUidIdAppDateCntrNo",
				new Object[] { uid, apptime, cntrNo });
	}

}
