/* 
 * L140S06A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Lob;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 內部規範資訊 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L140S06A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class L140S06A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 是否通過檢核
	 * <p/>
	 * Y/N
	 */
	@Size(max = 1)
	@Column(name = "CHECKYN", length = 1, columnDefinition = "CHAR(1)")
	private String checkYN;

	/**
	 * 未依銀行內部規定之條例
	 * <p/>
	 * COM.BCODETYPE CODETYPE=‘lms140_intReg’
	 */
	@Size(max = 2)
	@Column(name = "INTREG", length = 2, columnDefinition = "VARCHAR(2)")
	private String intReg;

	/** 未依銀行內部規定之說明 **/
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(name = "INTREGMEMO", columnDefinition = "CLOB")
	private String intRegMemo;

	/**
	 * 本項逾規定之類別
	 */
	@Size(max = 20)
	@Column(name = "INTREGREASON", length = 2, columnDefinition = "VARCHAR(20)")
	private String intRegReason;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/**
	 * 取得是否通過檢核
	 * <p/>
	 * Y/N
	 */
	public String getCheckYN() {
		return this.checkYN;
	}

	/**
	 * 設定是否通過檢核
	 * <p/>
	 * Y/N
	 **/
	public void setCheckYN(String value) {
		this.checkYN = value;
	}

	/**
	 * 取得未依銀行內部規定之條例
	 * <p/>
	 * COM.BCODETYPE CODETYPE=‘lms140_intReg’
	 */
	public String getIntReg() {
		return this.intReg;
	}

	/**
	 * 設定未依銀行內部規定之條例
	 * <p/>
	 * COM.BCODETYPE CODETYPE=‘lms140_intReg’
	 **/
	public void setIntReg(String value) {
		this.intReg = value;
	}

	/** 取得未依銀行內部規定之說明 **/
	public String getIntRegMemo() {
		return this.intRegMemo;
	}

	/** 設定未依銀行內部規定之說明 **/
	public void setIntRegMemo(String value) {
		this.intRegMemo = value;
	}

	/** 設定本項逾規定之類別 **/
	public void setIntRegReason(String intRegReason) {
		this.intRegReason = intRegReason;
	}

	/** 取得本項逾規定之類別 **/
	public String getIntRegReason() {
		return intRegReason;
	}
}
