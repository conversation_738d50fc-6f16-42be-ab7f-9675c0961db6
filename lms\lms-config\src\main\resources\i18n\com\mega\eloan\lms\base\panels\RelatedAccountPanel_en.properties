#==================================================
# \u5f80\u4f86\u5f59\u7e3d\u6a19\u984c\u540d\u7a31
#==================================================
L1205S07.index1=Credit Guarantee
L1205S07.index2=Not Credit Guaranteed
L1205S07.index3=(C denotes credit guarantee)
L1205S07.index4=Related Companies Total
L1205S07.index5=Group Total
L1205S07.index6=Current Deposit
L1205S07.index7=Outstanding Balance
L1205S07.index8=Credit Limit
L1205S07.index9=Contribution
L1205S07.index10=Item
L1205S07.index11=Principal Borrower
L1205S07.index12=Unit: TWD1,000
L1205S07.index13=Data Inquiry Period
L1205S07.index14=Please select a customer
L1205S07.index15=UBN (excluding repeated serial number)
L1205S07.index16=Customer Status
L1205S07.index17=No. Of Records
L1205S07.index18=Amount
L1205S07.index19=Compiling In Progress
L1205S07.index20=Document Status
L1205S07.index21=Starting Year/Month
L1205S07.index22=Ending Year/Month
L1205S07.index23=Document Status
L1205S07.index24=Compiling In Progress
L1205S07.index25=Borrower
L1205S07.index26=Reference Borrower
L1205S07.index27=Main Collateral
L1205S07.index28=Interest Rate
L1205S07.index29=Tenor
L1205S07.index30=Year
L1205S07.index31=Months
L1205S07.createBY1=System Generated
L1205S07.createBY2=Manually Generated
L1205S07.prtFlag1=Print
L1205S07.prtFlag2=Do Not Print
#==================================================
# \u5f80\u4f86\u5f59\u7e3d\u932f\u8aa4\u8996\u7a97\u8a0a\u606f\u5167\u5bb9
#==================================================
L1205S07.error1=Inquiry Start Year/Month
L1205S07.error2=Inquiry End Year/Month
L1205S07.error3=Earlier than the starting date of DW data range (currently no data)
L1205S07.error4=Earlier than the starting date of DW data range
L1205S07.error5=Earlier than the latest date of DW data range (currently no data)
L1205S07.error6=Later than the latest date of DW data range
L1205S07.error7=\u203bThe data warehouse updates last month's banking transactions on the 10th each month (which is different to DWC350, which updates immediately at the time data becomes available).
L1205S07.error8=Later than the starting date of DW data range
L1205S07.error9=Later than the starting date of DW data range (currently no data)
L1205S07.error10=Earlier than the latest date of DW data range
L1205S07.error14=Must be executed [Import Related Account Banking Summary] before executing this function!
L1205S07.error19=\u8cc7\u6599\u67e5\u8a62\u671f\u9593\u4e0d\u5f97\u70ba\u7a7a\u767d\uff01
L1205S07.error20=\u8cc7\u6599\u5009\u5132\u7121\u6700\u8fd1\u4e09\u500b\u6708{0}\uff5e{1}\u4e4b\u8cc7\u6599
L1205S07.error21=\u5f15\u9032\u501f\u6b3e\u4eba{0}&nbsp;{1}\u96c6\u5718\u8cc7\u8a0a\u932f\u8aa4
L1205S07.error22=\u5f15\u9032\u501f\u6b3e\u4eba{0}&nbsp;{1}\u96c6\u5718\u8a55\u7b49\u8cc7\u8a0a\u932f\u8aa4
L1205S07.error23=\u5f15\u9032{0}&nbsp;{1}\u696d\u52d9\u5f80\u4f86\u8cc7\u8a0a\u932f\u8aa4
L1205S07.error24=\u5f15\u9032{0}&nbsp;{1}\u5229\u6f64\u8ca2\u737b\u5ea6\u932f\u8aa4
L1205S07.error26 = \u8cc7\u6599\u5009\u5132\u7121\u98a8\u96aa\u6027\u8cc7\u7522\u5e73\u5747\u9918\u984d\u4e4b\u8cc7\u6599(DWADM.OTS_BSL2CSNET_AVG)
L1205S07.error27 = \u5f15\u9032{0}&nbsp;{1}\u98a8\u96aa\u6027\u8cc7\u7522\u5e73\u5747\u9918\u984d\u932f\u8aa4
#==================================================
# \u5f80\u4f86\u5f59\u7e3d\u8b66\u544a\u8996\u7a97\u8a0a\u606f\u5167\u5bb9
#==================================================
L1205S07.alert8=Additional data already exists, can not be added!
#==================================================
# \u5f80\u4f86\u5f59\u7e3dcheckbox\u540d\u7a31
#==================================================
L1205S07.checkbox1=Borrower
L1205S07.checkbox2=Borrower's Representative
L1205S07.checkbox3=Group Total
L1205S07.checkbox4=Related Companies Total
L1205S07.checkbox5=Group company
L1205S07.checkbox6=Related Company
#==================================================
# \u5f80\u4f86\u5f59\u7e3d\u8cc7\u6599\u57fa\u671f\u7d30\u90e8\u540d\u7a31
#==================================================
L1205S07.form1=Average Outstanding Balance
L1205S07.form2=Available Credit Line
L1205S07.form3=Transaction Volume
L1205S07.form4=Entrusted Asset Balance
L1205S07.form5=Fee Income
L1205S07.form6=Number of accounts
L1205S07.form7=Amount OF Credit Card Purchase
L1205S07.form8=Whether co-brand cards exist
L1205S07.form9=(including EDI)
#==================================================
# copy from LMS1205R01RptServiceImpl
L120S01C.CRDTITLE01=Credit Grade:
L120S01C.CRDTITLE02=Grade Date:
L120S01C.CRDTITLE03=Grading Unit:
L120S01C.CRDTITLE04=Model Grade:
L120S01C.CRDTITLE05=External Rating:
L120S01C.CRDTITLE06=Current Local Rating:
L120S01C.NOCRD01=Not Required
#J-113-0044_12473_B1001 \u4fe1\u7528\u98a8\u96aa\u5167\u90e8\u8a55\u7b49\u65b0\u589e\u8a66\u884c\u6a21\u578b
l120s01c.tmpNewMow=\u8a66\u884c\u6a21\u578b
l120s01c.forReference=\u66ab\u50c5\u4f9b\u53c3

grade01=Average
grade02=Above-average
grade03=Good
grade04=Satisfactory
grade05=Bad
grade06=High quality
grade07=Sound
grade08=Slightly lower than the average
grade09=Slightly weak
grade10=Slightly higher than the average
grade11=Best
grade12=Best Transnational enterprises
grade13=Best Transnational securities trader
grade14=Worst
grade15=extremely High quality
grade16=Breach of contract
grade17=Weak
grade18=Watch-list
grade19=extremely High quality
grade20=Worst
grade21=Very good

grade22=Somewhat poor
grade23=\u6975\u4f73\u4e4b\u8d85\u5927\u578b\u4f01\u696d\uff0c\u4e3b\u89c0\u8a55\u7b49\u66f4\u65b0\u5347\u7b49\u5f97\u6b64\u7b49\u7d1a
grade24=\u6975\u4f73\u4e4b\u4e2d\u578b\u4f01\u696d\uff0c\u4e3b\u89c0\u8a55\u7b49\u66f4\u65b0\u5347\u7b49\u5f97\u6b64\u7b49\u7d1a
grade25=\u6975\u4f73\u4e4b\u4e2d\u5c0f\u578b\u4f01\u696d\uff0c\u4e3b\u89c0\u8a55\u7b49\u66f4\u65b0\u5347\u5169\u7b49\u5f97\u6b64\u7b49\u7d1a
grade26=\u6975\u4f73\u4e4b\u4e2d\u5c0f\u578b\u4f01\u696d\uff0c\u4e3b\u89c0\u8a55\u7b49\u66f4\u65b0\u5347\u4e00\u7b49\u5f97\u6b64\u7b49\u7d1a
grade27=\u6975\u4f73\u4e4b\u5927\u578b\u4e0d\u52d5\u7522\u5546\uff0c\u4e3b\u89c0\u8a55\u7b49\u66f4\u65b0\u5347\u7b49\u5f97\u6b64\u7b49\u7d1a
grade28=\u6975\u4f73\u4e4b\u8d85\u5927\u5238\u5546\uff0c\u4e3b\u89c0\u8a55\u7b49\u66f4\u65b0\u5347\u7b49\u5f97\u6b64\u7b49\u7d1a
grade29=\u6975\u4f73\u4e4b\u6295\u8cc7\u516c\u53f8\uff0c\u4e3b\u89c0\u8a55\u7b49\u66f4\u65b0\u5347\u7b49\u5f97\u6b64\u7b49\u7d1a
grade30=\u6975\u4f73\u4e4b\u5927\u578b\u79df\u8cc3\u516c\u53f8\uff0c\u4e3b\u89c0\u8a55\u7b49\u66f4\u65b0\u5347\u7b49\u5f97\u6b64\u7b49\u7d1a
grade31=\u6975\u4f73\u4e4b\u5883\u5916\u8cbf\u6613\u63a7\u80a1\u4f01\u696d\uff0c\u96c6\u5718\u908f\u8f2f\u6216\u4e3b\u89c0\u8a55\u7b49\u66f4\u65b0\u5347\u5169\u7b49\u5f97\u6b64\u7b49\u7d1a
grade32=\u6975\u4f73\u4e4b\u5883\u5916\u8cbf\u6613\u63a7\u80a1\u4f01\u696d\uff0c\u96c6\u5718\u908f\u8f2f\u6216\u4e3b\u89c0\u8a55\u7b49\u66f4\u65b0\u5347\u4e00\u7b49\u5f97\u6b64\u7b49\u7d1a
grade33=\u6975\u4f73\u4e4b\u6d77\u5916\u8de8\u570b\u4f01\u696d\uff0c\u570b\u5bb6\u98a8\u96aa\u5206\u6790\u6216\u4e3b\u89c0\u8a55\u7b49\u66f4\u65b0\u5347\u7b49\u5f97\u6b64\u7b49\u7d1a
grade34=\u6975\u4f73\u4e4b\u6d77\u5916\u79df\u8cc3\u696d\uff0c\u7d93\u570b\u5bb6\u98a8\u96aa\u5206\u6790\u53ca\u4e3b\u89c0\u8a55\u7b49\u66f4\u65b0\u5347\u5169\u7b49\u5f97\u6b64\u7b49\u7d1a
grade35=\u6975\u4f73\u4e4b\u6d77\u5916\u79df\u8cc3\u696d\uff0c\u7d93\u570b\u5bb6\u98a8\u96aa\u5206\u6790\u6216\u4e3b\u89c0\u8a55\u7b49\u66f4\u65b0\u5347\u4e00\u7b49\u5f97\u6b64\u7b49\u7d1a
grade36=Very strong 
grade37=\u6975\u4f73\u4e4b\u6295\u8cc7\u516c\u53f8\uff0c\u4e3b\u89c0\u8a55\u7b49\u66f4\u65b0\u5347\u7b49\u5f97\u6b64\u7b49\u7d1a
grade38=Best property leasing& investment company only by subjective override
grade39=\u6975\u4f73\u4e4b\u5883\u5916\u4e2d\u578b/\u4e2d\u5c0f\u578b\u4f01\u696d\uff0c\u7d93\u570b\u5bb6\u98a8\u96aa\u5206\u6790\u53ca\u4e3b\u89c0\u8a55\u7b49\u66f4\u65b0\u5347\u5169\u7b49\u5f97\u6b64\u7b49\u7d1a
grade40=\u6975\u4f73\u4e4b\u5883\u5916\u4e2d\u578b/\u4e2d\u5c0f\u578b\u4f01\u696d\uff0c\u7d93\u570b\u5bb6\u98a8\u96aa\u5206\u6790\u6216\u4e3b\u89c0\u8a55\u7b49\u66f4\u65b0\u5347\u4e00\u7b49\u5f97\u6b64\u7b49\u7d1a


M1.grade.1=Top
M1.grade.2=Very strong
M1.grade.3=Strong

M2.grade.1=Top
M2.grade.2=Very strong
M2.grade.3=Strong

M3.grade.1=Top
M3.grade.2=Very strong
M3.grade.3=Strong

M4.grade.2=Top
M4.grade.3=Strong
MA.grade.2=Top
MA.grade.3=Strong
MB.grade.2=Top
MB.grade.3=Strong
MC.grade.2=Top
MC.grade.3=Strong

M6.grade.1=Top
M6.grade.2=Very strong
M6.grade.3=Strong

M8.grade.2=Top
M8.grade.3=Strong
MD.grade.2=Top
MD.grade.3=Strong
ME.grade.2=Top
ME.grade.3=Strong

M9.grade.2=Top
M9.grade.3=Strong

MG.grade.1=Top
MG.grade.2=Very strong
MG.grade.3=Strong

MH.grade.1=Top
MH.grade.2=Very strong
MH.grade.3=Strong
MJ.grade.1=Top
MJ.grade.2=Very strong
MJ.grade.3=Strong

MI.grade.2=Top
MI.grade.3=Strong

MK.grade.1=Top property leasing& investment company only by model overlay upgrade
MK.grade.2=Very strong property leasing& investment company only by model overlay upgrade
MK.grade.3=Strong

ML.grade.1=Top
ML.grade.2=Very strong
ML.grade.3=Strong

MM.grade.1=Top
MM.grade.2=Very strong
MM.grade.3=Strong

MN.grade.1=Top
MN.grade.2=Very strong
MN.grade.3=Strong

A0.grade.1=Top multinationals company
A0.grade.2=Very strong
A0.grade.3=Strong

A1.grade.2=Top medium company only by model overlay upgrade
A1.grade.3=Strong

A2.grade.2=Top SME only by model overlay upgrade
A2.grade.3=Strong SME only by model overlay upgrade

M5.grade.1=Strong
M5.grade.2=Good
M5.grade.3=Satisfactory
M5.grade.4=Weak

MF.grade.1=Strong
MF.grade.2=Good
MF.grade.3=Satisfactory
MF.grade.4=Weak

MO.grade.1=Strong
MO.grade.2=Good
MO.grade.3=Satisfactory
MO.grade.4=Weak

MP.grade.1=Top
MP.grade.2=Very strong
MP.grade.3=Strong

MQ.grade.1=Top Offshore large company upgraded by model overlay
MQ.grade.2=Very strong
MQ.grade.3=Strong

MR.grade.1=Top Offshore small/medium company upgraded by model overlay
MR.grade.2=Very strong Offshore small/medium company upgraded by model overlay
MR.grade.3=Strong

MS.grade.2=Top Offshore leasing company upgraded by model overlay
MS.grade.3=Strong Offshore leasing company upgraded by model overlay

MT.grade.1=Top
MT.grade.2=Very strong
MT.grade.3=Strong

MU.grade.1=Top large/medium company only by overlay upgrade
MU.grade.2=Very strong
MU.grade.3=Strong

MV.grade.1=Top small company only by overlay upgrade
MV.grade.2=Very strong small company only by overlay upgrade
MV.grade.3=Strong

MW.grade.1=Top company only by overlay upgrade
MW.grade.2=Very strong
MW.grade.3=Strong

MX.grade.1=Top company only by overlay upgrade
MX.grade.2=Very strong
MX.grade.3=Strong

MY.grade.1=Top company only by overlay upgrade
MY.grade.2=Very strong
MY.grade.3=Strong

Ma.grade.1=Top
Ma.grade.2=Very strong
Ma.grade.3=Strong

Mb.grade.1=
Mb.grade.2=Very strong
Mb.grade.3=Strong

Mc.grade.1=
Mc.grade.2=
Mc.grade.3=Strong

Md.grade.1=
Md.grade.2=
Md.grade.3=Strong

Mh.grade.1=
Mh.grade.2=
Mh.grade.3=Strong

Mi.grade.1=
Mi.grade.2=
Mi.grade.3=Strong

Mj.grade.1=Strong
Mj.grade.2=Good
Mj.grade.3=Satisfactory
Mj.grade.4=Weak

Mk.grade.1=Strong
Mk.grade.2=Good
Mk.grade.3=Satisfactory
Mk.grade.4=Weak

common.grade.4=\u76f8\u7576\u9ad8\u54c1\u8cea
common.grade.5=\u9ad8\u54c1\u8cea
common.grade.6=\u7565\u9ad8\u65bc\u5e73\u5747\u6c34\u6e96
common.grade.7=\u5e73\u5747\u6c34\u6e96
common.grade.8=\u7565\u4f4e\u65bc\u5e73\u5747\u6c34\u6e96
common.grade.9=\u7565\u70ba\u8584\u5f31
common.grade.10=\u5dee
common.grade.11=\u76f8\u7576\u5dee
common.grade.12=\u89c0\u5bdf\u6e05\u55ae
common.grade.13=\u89c0\u5bdf\u6e05\u55ae
common.grade.DF=\u9055\u7d04


tempGrade= Grade

