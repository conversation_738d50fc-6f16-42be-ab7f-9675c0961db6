/* 
 *ObsdbELF404Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.obsdb.service;

import java.math.BigDecimal;

/**
 * <pre>
 * ELF404
 * </pre>
 * 
 * @since
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
public interface ObsdbELF404Service {

	void insertElf404(String brno, String appryy, String apprmm, String ctype,
			String caseDept, String caseNo, String cntrNo, Integer citem1,
			Integer citem2, Integer citem3, Integer citem4, Integer citem5,
			BigDecimal appramt, BigDecimal time);

	void updateElf404(String brno, String appryy, String apprmm, String ctype,
			String caseNo, String cntrNo, Integer citem1, Integer citem2,
			Integer citem3, Integer citem4, Integer citem5, BigDecimal appramt,
			String updater, String caseDept, BigDecimal time);

	boolean selectElf404(String brno, String appryy, String apprmm,
			String ctype, String caseDept, String caseNo, String cntrNo);

	void insertElf4042(String brno, String appryy, String apprmm, String ctype,
			String caseNo, String cntrNo, int citem6, int citem7, int citem8,
			int citem9, int citem10, double appramt, String updater,
			String casedept, BigDecimal time);

	void updateElf4042(String brno, String appryy, String apprmm, String ctype,
			String caseNo, String cntrNo, int citem6, int citem7, int citem8,
			int citem9, int citem10, double appramt, String updater,
			String caseDept, BigDecimal time);

	boolean selectElf4042(String brno, String appryy, String apprmm,
			String ctype, String caseNo, String cntrNo);
}
