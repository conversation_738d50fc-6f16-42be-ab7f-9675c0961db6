package com.mega.eloan.lms.dc.action;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;

import com.mega.eloan.lms.dc.base.DCException;
import com.mega.eloan.lms.dc.conf.ViewListConfig;
import com.mega.eloan.lms.dc.util.TextDefine;
import com.mega.eloan.lms.dc.util.Util;

import lotus.domino.Database;
import lotus.domino.DxlExporter;
import lotus.domino.NotesFactory;
import lotus.domino.Session;
import lotus.domino.View;

/**
 * <pre>
 * DXLExport
 * </pre>
 * 
 * @since 2012/12/20
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/20,Bang,new
 *          <li>2012/12/24
 *          SandraPeng:將refMainId額度明細文件編號改取--doc.getItemValueString
 *          ("UNIDDOCID")改寫入文字檔
 *          <li>2012/12/25 SandraPeng:增加log以便檢視
 *          <li>2013/01/11 SandraPeng:當form只要有在設定檔中時，直接轉入；增加Log以便測試後的報告產出
 *          <li>2013/02/22,UFO,增加recycle機制
 *          <li>2013/03/07,UFO,取消local_var: dataBean，父類別取得
 *          <li>2013/03/22,Bang,View為VCLS10105Z(審核書+簽報書)
 *          或VCLS10130(批覆書)，保留所有LinkID於記憶體中，並將dxl取回 *
 *          </ul>
 */
public class DXLExport extends BaseAction {
	private String schema = "";

	private String dxlDirRootPath = "";
	private String logsDirPath = "";
	private String loadDB2DirPath = "";// 與db相關 load_db2 目錄所在位置路徑
	private String host = "";
	private String userId = "";
	private String userPsw = "";
	private String[] mainFormlist = null;
	private String[] form201 = new String[] { "FLMS110M01", "FLMS120M01",
			"FLMS130M01" };
	private String[] formSDetail = new String[] { "FLMS110S01", "FLMS120S01",
			"FLMS140S01", "FLMS140S02", "FLMS140S03", "FLMS140S04",
			"FLMS140S05", "FLMS140S06", "FLMS140S07" };
	private String[] formBaseCLS = new String[] { "FCLS106M01", "FCLS106M02",
			"FCLS107M01", "FCLS107M02", "FCLS108M01", "FCLS108M02",
			"FCLS109M01", "FCLS109M02", "FCLS109M03", "FCLS110M01",
			"FCLS110M02", "FCLS111M01", "FCLS111M02", "FCLS113M01",
			"FCLS113M02", "FCLS118M01", "FCLS118M02", "FCLS119M01",
			"FCLS119M02" };//所有審核書、 "FCLS114M01", "FCLS114M02", "FCLS115M01","FCLS715M01"
	private String[] formCLS114 = new String[]{"FCLS114M01", "FCLS114M02"};//簽報書
	private String[] formCLS15  = new String[]{"FCLS115M01", "FCLS715M01"};//額度明細表、批覆書
	private String[] formVCLS00101 = new String[] { "FCLS104S03" };//借款人資料
	@SuppressWarnings("unused")
	private String[] formVCLS00113 = new String[] { "FCLS104S04" };//借款人資料
	private String[] formVCLS09105 = new String[] { "FCLS104S12" };//擔保品
	private String[] formVCLS10111 = new String[] { "FCLS104T12" };//利率
	private String[] formVCLS10112 = new String[] { "FCLS104T21" };//償還方式
	private String[] formVCLS10123 = new String[] { "FCLS102M05","FCLS102M06" };//購屋房貸險權數檢核表授權
	private Map<String, String> hm201 = new HashMap<String, String>();
	private Set<String> key201Set = new HashSet<String>();
	// 個金
	private List<String> clsList = new ArrayList<String>();
	private List<String> cls114List = new ArrayList<String>();
	/**
	 * 初始化必要資訊及執行Export動作
	 * 
	 * @param schema
	 *            String:目前執行的系統名稱
	 * @param viewListName
	 *            String:目前執行的ViewList 2013-01-28 Modify By Bang:加入個金判斷
	 */
	public void doExport(String schema, String viewListName) {
		if (StringUtils.isBlank(schema)) {
			String errmsg = "讀取系統名稱錯誤,未指定要執行的系統名稱,請重新確認...";
			this.logger.error(errmsg);
			throw new DCException(errmsg);
		}
		this.schema = schema;
		this.logger.info("正在初始化 DXLExport 必要資訊...");

		// 2013-01-28
		if (TextDefine.SCHEMA_LMS.equalsIgnoreCase(this.schema)) {
			this.dxlDirRootPath = this.configData.getLmsDxlDirRootPath();// homePath\today\LMS
			this.logsDirPath = this.configData.getLmsLogsDirPath();// User當前工作目錄\log\logs\執行日期\LMS
			// User當前工作目錄\load_db2\執行日期\LMS
			this.loadDB2DirPath = this.configData.getLmsloadDB2DirPath()
					+ File.separator + "data";
			this.mainFormlist = this.configData.getLMSMainForm().split(";");// 在config中有設定的form
																			// name為本轉檔程式可被允許執行的form
		} else {
			this.dxlDirRootPath = this.configData.getClsDxlDirRootPath();// homePath\today\CLS
			this.logsDirPath = this.configData.getClsLogsDirPath();// User當前工作目錄\log\logs\執行日期\CLS
			// User當前工作目錄\load_db2\執行日期\CLS
			this.loadDB2DirPath = this.configData.getClsloadDB2DirPath()
					+ File.separator + "data";
			this.mainFormlist = this.configData.getCLSMainForm().split(";");// 在config中有設定的form
																			// name為本轉檔程式可被允許執行的form
		}
		Util.checkDirExist(this.loadDB2DirPath);

		this.host = this.configData.getHost();
		this.userId = this.configData.getUserId();
		this.userPsw = this.configData.getUserPassword();

		try {
			this.runExport(viewListName);
		} catch (Exception e) {
			this.logger.error("doExport() error! Exception: ", e);
			throw new DCException(e);
		}
	}

	/**
	 * 準備執行輸出.dxl檔
	 * 
	 * @param viewListName
	 *            String :目前要讀取的viewListName
	 * @throws FileNotFoundException
	 * @throws IOException
	 */
	private void runExport(String viewListName) throws FileNotFoundException,
			IOException {
		// 讀取viewList
		List<String> viewList = ViewListConfig.getInstance().getViewList(
				viewListName);
		if (null == viewList) {
			this.logger.error("DXLExport runExport-->viewListName :"
					+ viewListName + " is Null");
			return;
		}
		// LMS
		if (TextDefine.SCHEMA_LMS.equalsIgnoreCase(this.schema)) {
			for (int i = 0, size = viewList.size(); i < size; i++) {
				exportLMS(viewList.get(i));
			}
		}
		// CLS
		else {
			for (int i = 0, size = viewList.size(); i < size; i++) {
				exportCLS(viewList.get(i));
			}
		}
	}

	/**
	 * 開始輸出企金.dxl檔
	 * 
	 * @param nsfView
	 *            String :viewList中的資料
	 */
	private void exportLMS(String nsfViewData) {
		key201Set = hm201.keySet();
		long tt1 = System.currentTimeMillis();
		// 切割資料
		// Ex:nsfViewData = EL201\EL1LMSB1.NSF;VLMSDB201B;
		String[] str = nsfViewData.split(";");
		String nsf = str[0]; // NOTES NSF Name ,EX:EL201\EL1LMSB1.NSF
		String strBrn = str[0].substring(2, 5);// 分行名稱,EX:201
		String viewName = str[1];// View Name ,EX:VLMSDB201B

		// 建立轉出時必要目錄: Branch \ viewName \ REJECT & TEXT
		Util.checkDirExist(this.dxlDirRootPath + File.separator + strBrn
				+ File.separator + viewName + File.separator + "REJECT");
		Util.checkDirExist(this.dxlDirRootPath + File.separator + strBrn
				+ File.separator + viewName + File.separator + "TEXT");

		// PrintWriter logsCw = null;// 輸出log
		PrintWriter dxlCw = null;// 輸出DXL檔的資料
		StringBuffer logsb = new StringBuffer();
		Session session = null;
		Database nsfdb = null;
		View view = null;
		lotus.domino.Document doc = null;
		lotus.domino.Document tmpdoc = null;

		// 20121225 SandraPeng for測試新增計量變數
		int countBefor = 0; // 計算每一個view有多少筆
		int countAfter = 0; // 計算真正符合form name的筆數

		try {
			// L120M01C
			String db2Txt = this.dxlDirRootPath + File.separator + strBrn
					+ File.separator + viewName + File.separator
					+ this.configData.getTextPath() + File.separator
					+ "L120M01C.txt";
			PrintWriter loadCw = this.createWriter(db2Txt, null);

			// 建立各分行logs
			// logsCw = this.getLogWriter(viewName, strBrn);

			// logsCw.println(viewName + "_" + strBrn + "起始時間 :"
			// + Util.getNowTime());

			logsb.append(">").append(viewName).append(">").append(strBrn)
					.append("> 起始時間 :>").append(Util.getNowTime()).append(">");
			try {
				session = NotesFactory.createSession(this.host, this.userId,
						this.userPsw);

			} catch (Exception e) {
				String errmsg = strBrn + "分行,viewName:" + viewName
						+ " 無法連線, 連線結束時間 :" + Util.getNowTime() + "==>"
						+ this.host;
				this.logger.error(errmsg, e);
				// logsCw.println(errmsg);
				throw new DCException(errmsg, e);
			}

			DxlExporter dxl = session.createDxlExporter();

			nsfdb = session.getDatabase("", nsf);
			if (nsfdb.isOpen()) {
				// this.logger.info("目前正在執行 "+nsfdb.getTitle() + "-->" + strBrn
				// + "分行...");
			} else {
				this.logger.error("ERROR: " + nsf
						+ " Database does not exist ...");
			}

			view = nsfdb.getView(viewName);
			if (view == null) {
				String errmsg = strBrn + "分行, " + viewName
						+ " is null. please check...";
				this.logger.error(errmsg);
				System.err.println(errmsg);
				return;
			}
			doc = view.getFirstDocument(); // Read First doc
			dxl.setForceNoteFormat(false);
			dxl.setOutputDOCTYPE(false);
			dxl.setConvertNotesBitmapsToGIF(true);

			StringBuffer sbUNID = new StringBuffer();
			while (doc != null) {
				try {
					String strDxl = dxl.exportDxl(doc);
					if (strDxl.length() > 0) {
						// 取得form name
						String strForm = doc.getItemValueString("form");
						sbUNID.setLength(0);
						String parentUNID = doc.getParentDocumentUNID().trim();
						// 若有parentUNID則表示其為明細檔
						if (parentUNID.length() > 0) {
							sbUNID.append(strForm + "_" + parentUNID + "_"
									+ doc.getUniversalID());
						} else {
							sbUNID.append(strForm + "_" + doc.getUniversalID());
						}

						String dxlName = this.dxlDirRootPath + File.separator
								+ strBrn + File.separator + viewName
								+ File.separator + sbUNID.toString() + ".dxl";
						// 當strForm符合FLMS110M01、FLMS120M01、FLMS130M01其中之一時，產出簽報書.dxl
						// 並將UniversalID、UNIDDOCID記錄在集合物件(201B)中，準備下一段比對
						// logger.info("dxlName="+dxlName);
						countBefor++;
						if (Arrays.asList(this.form201).contains(strForm)) {
							/*String unid = Util
									.nullToSpace(doc.getUniversalID());*/
							String docId = Util.nullToSpace(doc
									.getItemValueString("UnidDocID"));

							// logsCw.println(unid + ";" + docId);

							dxlCw = this.createWriter(dxlName, dxlCw);
							dxlCw.println(strDxl);
							countAfter++;
							this.hm201.put(docId, docId);

						}
						// 當strForm符合FLMS140M01時將"RPTDocID"，
						// 與集合物件(201B)中的"UNIDDOCID"比對，符合的產生額度明細表DXL檔，放置於DXL目錄下，
						// 並產生一文字檔格式符合L120M01C額度明細關聯檔於[load_db2]，檔名為L120M01C.txt
						if (strForm.equals("FLMS140M01")
								|| strForm.equals("FLMS740M01")) {
							String rptDocId = Util.nullToSpace(doc
									.getItemValueString("RPTDocID"));
							// 20130415
							// Sandra若CNTRDOCID有值，以CNTRDOCID為140開頭所有的table的mainid；若無值，則取unid為mainid
							// 20130419
							// Sandra因CNTRDOCID會有重覆，建霖mail告知改用WEBELOANMAINID
							String cntrDocId = Util.nullToSpace(doc
									.getItemValueString("WEBELOANMAINID"));// 額度序號，20130419改用WEBELOANMAINID
							cntrDocId = cntrDocId.isEmpty() ? Util
									.nullToSpace(doc.getUniversalID())
									: cntrDocId;
							if (this.key201Set.contains(rptDocId)) {
								dxlCw = this.createWriter(dxlName, dxlCw);
								countAfter++;
								dxlCw.println(strDxl);
								// oid--系統產生;mainId文件--編號集合物件(201B)中的doc.getItemValueString("UNIDDOCID");itemType額度明細種類--"1"
								// refMainId額度明細文件編號--doc.getItemValueString("RPTDocID");creator--"domino";createTime--sysdate;updater;updateTime
								// 20121224 SandraPeng
								// 將refMainId額度明細文件編號改取--doc.getItemValueString("UNIDDOCID")將
								loadCw.println(new StringBuffer()
										.append(Util.getOID())
										.append(";")
										.append(this.hm201.get(rptDocId))
										// 簽報書key
										.append(";")
										.append(strForm.equals("FLMS140M01") ? "1"
												: "2")
										// itemType 2013-04-16 Sandra新增
										.append(";")
										.append(cntrDocId)
										// 額度明細表key 2013-04-16 Sandra修改
										.append(";")
										.append(this.configData.getTODAY())
										.append(";").append(Util.getCurrentTimestamp())
										.append(";")
										.append(this.configData.getTODAY())
										.append(";").append(Util.getCurrentTimestamp())
										.toString());
							}
						}
						// 當strForm符合formSDetail其中之一時，將"RPTDocID"，與集合物件(201B)中的"UNIDDOCID"比對，
						// 符合的產生借款人及額度利率結構利率化明細檔DXL檔，放置於DXL目錄下
						else if (Arrays.asList(this.formSDetail).contains(
								strForm)) {
							String rptDocId = Util.nullToSpace(doc
									.getItemValueString("RPTDocID"));
							if (key201Set.contains(rptDocId)) {
								dxlCw = this.createWriter(dxlName, dxlCw);
								countAfter++;
								dxlCw.println(strDxl);
							}
						}
						// 20130111 SandraPeng當form不在以上範圍，但有設定在設定檔中時，直接轉入
						else if (Arrays.asList(this.mainFormlist).contains(
								strForm)) {
							dxlCw = this.createWriter(dxlName, dxlCw);
							countAfter++;
							dxlCw.println(strDxl);
						}
					}
				} catch (Exception exc) {
					//增加顯示view名稱
					String errmsg = strBrn + "分行, Viewname："+view+",DOC Export error:"
							+ doc.getUniversalID();
					this.logger.error(errmsg, exc);
					// 2013/03/15 Modify By Bang :有錯誤不要中斷,記錄下來 其餘資料繼續執行
					//2013-06-11 Sandra 單筆轉檔時有問題就中斷
					 throw new DCException(errmsg, exc);
				}

				tmpdoc = view.getNextDocument(doc); // Read Next doc
				doc.recycle();
				doc = tmpdoc;

				if (countBefor % 300 == 0 || countAfter % 300 == 0) {
					// logsCw.println("======目前已取得共" + countBefor
					// + "筆，Export為DXL的共" + countAfter + "筆");
				}
			} // End of While
			view.refresh();

			long cost = System.currentTimeMillis() - tt1;
			// logsCw.println("\n 結束時間 :" + Util.getNowTime());
			// logsCw.println(strBrn + "分行Export DXL TOTAL TIME==> "
			// + Util.millis2minute(cost));
			// logsCw.print("取得總筆數" + countBefor + "筆");
			// logsCw.print("取回總筆數" + countAfter + "筆");

			logsb.append(", 結束時間 :>").append(Util.getNowTime())
					.append(">  TOTAL TIME==> ")
					.append(Util.millis2minute(cost)).append(">").append(cost)
					.append(">ms, 取得總筆數>").append(+countBefor)
					.append(">筆, 取回總筆數>").append(countAfter).append(">筆");
			this.logger.info(logsb.toString());
			logsb.setLength(0);
		} catch (Exception e) {
			String errmsg = " DXLExport在執行" + strBrn + "分行,ViewName名稱 :"
					+ viewName + " 時發生錯誤 :";
			this.logger.error(errmsg, e);
			throw new DCException(errmsg, e);
		} finally {
			// IOUtils.closeQuietly(logsCw);
			IOUtils.closeQuietly(dxlCw);
		}
	}

	/**
	 * 開始輸出個金.dxl檔
	 * 
	 * @param nsfView
	 *            String :viewList中的資料
	 */
	private void exportCLS(String nsfViewData) {
		long tt1 = System.currentTimeMillis();
		// 切割資料
		// Ex:nsfViewData = EL002\EL1CLSB1.NSF;VCLS00105;
		String[] str = nsfViewData.split(";");
		String nsf = str[0]; // NOTES NSF Name ,EX:EL201\EL1CLSB1.NSF
		String strBrn = str[0].substring(2, 5);// 分行名稱,EX:201
		String viewName = str[1];// View Name ,EX:VCLS00105

		// 建立轉出時必要目錄: Branch \ viewName \ REJECT & TEXT
		Util.checkDirExist(this.dxlDirRootPath + File.separator + strBrn
				+ File.separator + viewName + File.separator + "REJECT");
		Util.checkDirExist(this.dxlDirRootPath + File.separator + strBrn
				+ File.separator + viewName + File.separator + "TEXT");

		// PrintWriter logsCw = null;// 輸出log
		PrintWriter dxlCw = null;// 輸出DXL檔的資料
		StringBuffer logsb = new StringBuffer();
		Session session = null;
		Database nsfdb = null;
		View view = null;
		lotus.domino.Document doc = null;
		lotus.domino.Document tmpdoc = null;

		// 20121225 SandraPeng for測試新增計量變數
		int countBefor = 0; // 計算每一個view有多少筆
		int countAfter = 0; // 計算真正符合form name的筆數

		try {
			// 建立一檔案DXL_Key.txt，放置所有dxl的相關key值
			String keyTxt = this.dxlDirRootPath + File.separator + strBrn
					+ File.separator + viewName + File.separator
					+ this.configData.getTextPath() + File.separator
					+ "DXL_Key.txt";
			PrintWriter loadCw = this.createWriter(keyTxt, null);

			// 建立各分行logs
			// logsCw = this.getLogWriter(viewName, strBrn);
			// logsCw.println(viewName + "_" + strBrn + "起始時間 :"
			// + Util.getNowTime());

			logsb.append(viewName).append(">").append(strBrn)
					.append("> 起始時間 :>").append(Util.getNowTime()).append(">");

			try {
				session = NotesFactory.createSession(this.host, this.userId,
						this.userPsw);

			} catch (Exception e) {
				String errmsg = strBrn + "分行,viewName:" + viewName + " 無法連線"
						+ "\n 連線結束時間 :" + Util.getNowTime();
				this.logger.error(errmsg, e);
				// logsCw.println(errmsg);
				throw new DCException(errmsg, e);
			}

			DxlExporter dxl = session.createDxlExporter();

			nsfdb = session.getDatabase("", nsf);
			if (nsfdb.isOpen()) {
				// this.logger.info("目前正在執行 "+nsfdb.getTitle() + "-->" + strBrn
				// + "分行...");
			} else {
				this.logger.error("ERROR: " + nsf
						+ " Database   does   not   exist ...");
			}

			view = nsfdb.getView(viewName);
			if (view == null) {
				String errmsg = strBrn + "分行, " + viewName
						+ " is null. please check...";
				this.logger.error(errmsg);
				System.err.println(errmsg);
				return;
			}

			doc = view.getFirstDocument(); // Read First doc
			dxl.setForceNoteFormat(false);
			dxl.setOutputDOCTYPE(false);
			dxl.setConvertNotesBitmapsToGIF(true);

			StringBuffer sbUNID = new StringBuffer();
			while (doc != null) {
				try {
					boolean isChoose = false;
					String strDxl = dxl.exportDxl(doc);
					if (strDxl.length() > 0) {
						// 取得form name
						String strForm = doc.getItemValueString("form");
						sbUNID.setLength(0);
						String parentUNID = doc.getParentDocumentUNID().trim();
						// 若有parentUNID則表示其為明細檔
						if (parentUNID.length() > 0) {
							sbUNID.append(strForm + "_" + parentUNID + "_"
									+ doc.getUniversalID());
						} else {
							sbUNID.append(strForm + "_" + doc.getUniversalID());
						}

						String dxlName = this.dxlDirRootPath + File.separator
								+ strBrn + File.separator + viewName
								+ File.separator + sbUNID.toString() + ".dxl";
						countBefor++;

						// 當View為VCLS00105B或VCLS10130，保留所有LinkID於記憶體中，並將dxl取回
						// 0226:Sandra與明澤討論後，改取VCLS10105
						// if (viewName.equalsIgnoreCase("VCLS00105B")
						// 03-22:原VCLS10105、VCLS10132A將合併為VCLS10105Z,當View為VCLS10105Z(審核書+簽報書)
						// 或VCLS10130(批覆書)，保留所有LinkID於記憶體中，並將dxl取回
						// if (viewName.equalsIgnoreCase("VCLS10105Z")
						// || viewName.equalsIgnoreCase("VCLS10130")) {
						//formCLS114：簽報書、formCLS15：/額度明細表、批覆書
							String linkID = Util.nullToSpace(doc
									.getItemValueString("LinkID"));
						if (Arrays.asList(this.formBaseCLS).contains(strForm)) {
							
							this.clsList.add(linkID);//記錄LinkID以便後續取得其他相關資料
							isChoose = true;
						}
						//簽報書
						else if(Arrays.asList(this.formCLS114).contains(strForm)){
							this.cls114List.add(Util.nullToSpace(doc
									.getItemValueString("UnidDocID")));
							isChoose = true;
						}
						//額度明細表、批覆書
						else if(Arrays.asList(this.formCLS15).contains(strForm)){
							String rptDocId = Util.nullToSpace(doc
									.getItemValueString("RptDocID"));
								if(Util.nullToSpace(doc
										.getItemValueString("Sno")).length()==0){
									isChoose = false;
								}
								//團貸案，sno長度為2，若parentsno為空，則不export
								else if(Util.nullToSpace(doc
										.getItemValueString("Sno")).length()==2 && Util.nullToSpace(doc
										.getItemValueString("ParentSno")).length()==0){
									isChoose = false;//不exoprt，但是寫keylist
								}//20130626，若額度序號第4碼不為1，則不export
								else if(Util.nullToSpace(doc
										.getItemValueString("Sno")).length()>6 
										&&!Util.nullToSpace(doc
										.getItemValueString("Sno")).substring(3,4).equalsIgnoreCase("1")){
									isChoose = false;//不exoprt，但是寫keylist
								}else if(this.cls114List.contains(rptDocId)){//必須有對應的簽報書
									String tmpid =  linkID.equalsIgnoreCase("")?rptDocId:linkID;
									this.clsList.add(tmpid);
							isChoose = true;
								}else{
									isChoose = false;
						}
						}
						// 若View為VCLS10111或VCLS10112，將form.keyid與記憶體中的LinkID比對,
						// 若比對不到，則不export該筆資料,若比對得到，則取回dxl
						else if (Arrays.asList(this.formVCLS10111).contains(strForm)||
								Arrays.asList(this.formVCLS10112).contains(strForm)) {
//						else if (viewName.equalsIgnoreCase("VCLS10111")
//								|| viewName.equalsIgnoreCase("VCLS10112")) {
							String keyid = Util.nullToSpace(doc
									.getItemValueString("keyid"));
							if (this.clsList.contains(keyid)) {

								// 將每個dxl的key寫入一文字檔記錄，以利未來查詢
								isChoose = true;
							}
						}
						// 若View為VCL10123或VCLS00101或VCLS00113或VCLS09105，將form.LinkID與記憶體中的LinkID比對，
						// 2013-04-29 Sandra VCLS00113經與明澤確認後，無條件全數export
						// 若比對不到，則不export該筆資料,若比對得到，則取回dxl
						else if (Arrays.asList(this.formVCLS10123).contains(strForm)||
								Arrays.asList(this.formVCLS00101).contains(strForm)||
								Arrays.asList(this.formVCLS09105).contains(strForm)) {
							if (this.clsList.contains(linkID)) {
								isChoose = true;;
							}
						} else {
							isChoose = true;
						}
						if(isChoose){
							dxlCw = this.createWriter(dxlName, dxlCw);
							countAfter++;
							dxlCw.println(strDxl);
						}
						//所有的dxl都寫文字記錄檔，用isChoose
							this.writeDxlKey(doc, strBrn, viewName, strForm,
									loadCw,isChoose);
					
					}
				} catch (Exception e) {
					String errmsg = strBrn + "分行, DOC Export error:"
							+ doc.getUniversalID();
					this.logger.error(errmsg, e);
					// logsCw.println(errmsg);
					// 2013/03/21 Modify By Bang :有錯誤不要中斷,記錄下來 其餘資料繼續執行
					// throw new DCException(errmsg, exc);
				}

				tmpdoc = view.getNextDocument(doc); // Read Next doc
				doc.recycle();
				doc = tmpdoc;

				// if (countBefor % 300 == 0 || countAfter % 300 == 0) {
				// logsCw.println("======目前已取得共" + countBefor
				// + "筆，Export為DXL的共" + countAfter + "筆");
				// }
			} // End of While
			view.refresh();

			// logsCw.println("\n 結束時間 :" + Util.getNowTime());
			long cost = System.currentTimeMillis() - tt1;
			// logsCw.println(strBrn + "分行Export DXL TOTAL TIME===>"
			// + Util.millis2minute(cost));
			// logsCw.print("取得總筆數" + countBefor + "筆");
			// logsCw.print("取回總筆數" + countAfter + "筆");

			logsb.append(", 結束時間 :>").append(Util.getNowTime())
					.append(">  TOTAL TIME===>")
					.append(Util.millis2minute(cost)).append(">").append(cost)
					.append(" ms, 取得總筆數>").append(+countBefor)
					.append(">筆, 取回總筆數>").append(countAfter).append(">筆");
			this.logger.info(logsb.toString());
			logsb.setLength(0);

		} catch (Exception e) {
			String errmsg = " DXLExport在執行" + strBrn + "分行,ViewName名稱 :"
					+ viewName + " 時發生錯誤 :";
			this.logger.error(errmsg, e);
			throw new DCException(errmsg, e);
		} finally {
			// IOUtils.closeQuietly(logsCw);
			IOUtils.closeQuietly(dxlCw);
		}
	}

	private void writeDxlKey(lotus.domino.Document doc, String strBrn,
			String viewName, String strForm, PrintWriter loadCw,boolean isChoose)
			throws Exception {
		// 將每個dxl的key寫入一文字檔記錄，以利未來查詢
		//文字檔的內容為：分行別;viewname;formname;universalId;LinkID;UnidDocID;RptDocID;Sno1;Sno2;Sno3;Sno4;Sno;
		String linkID = Util.nullToSpace(doc.getItemValueString("LinkID"));
		String universalId = Util.nullToSpace(doc.getUniversalID());
		String KeyId = Util.nullToSpace(doc.getItemValueString("KeyID"));
		String unidDocId = Util
				.nullToSpace(doc.getItemValueString("UnidDocID"));
		String rptid = Util.nullToSpace(doc.getItemValueString("RPTID"));
		String rptdocid = Util.nullToSpace(doc.getItemValueString("RPTDocID"));
		String sno1 = Util.nullToSpace(doc.getItemValueString("Sno_1"));
		String sno2 = Util.nullToSpace(doc.getItemValueString("Sno_2"));
		String sno3 = Util.nullToSpace(doc.getItemValueString("Sno_3"));
		String sno4 = Util.nullToSpace(doc.getItemValueString("Sno_4"));
		String sno = Util.nullToSpace(doc.getItemValueString("Sno"));
		String parentSno = Util.nullToSpace(doc.getItemValueString("ParentSno"));

		StringBuffer sblog = new StringBuffer()
				// 分行別和viewname
				.append(strBrn).append(";")
				.append(viewName).append(";")
				.append(strForm).append(";")	// form name
				.append("unid=").append(universalId).append(";")
				.append("LinkID=").append(linkID).append(";")// LinkID
				.append("KeyID=").append(KeyId).append(";")
				.append("UnidDocID=").append(unidDocId).append(";")
				.append("RPTID=").append(rptid).append(";")
				.append("RPTDocID=").append(rptdocid).append(";")
				.append("Sno1=").append(sno1).append(";")
				.append("Sno2=").append(sno2).append(";")
				.append("Sno3=").append(sno3).append(";")
				.append("Sno4=").append(sno4).append(";")
				.append("Sno =").append(sno).append(";")
				.append("ParentSno=").append(parentSno).append(";");//配合團貸案新增
				
			if(!isChoose){
				sblog.append("此案件未export");
			}
		loadCw.println(sblog.toString());
	}

	/**
	 * Create PrintWriter
	 * 
	 * @param file
	 *            檔案名稱
	 * @param wirter
	 *            重用之PrintWriter
	 * @return {@link java.io.PrintWriter}
	 * @throws IOException
	 */
	private PrintWriter createWriter(String file, PrintWriter wirter)
			throws IOException {
		IOUtils.closeQuietly(wirter);
		wirter = new PrintWriter(new BufferedWriter(new OutputStreamWriter(
				new FileOutputStream(new File(file)))), true);
		return wirter;
	}

	/**
	 * @param viewName
	 *            View Name
	 * @param strBrn
	 *            Branch
	 * @return {@link java.io.PrintWriter}
	 * @throws Exception
	 */
	@SuppressWarnings("unused")
	private PrintWriter getLogWriter(String viewName, String strBrn)
			throws Exception {
		final String LOG_ROOT = this.logsDirPath + File.separator + "EXPORT";
		Util.checkDirExist(LOG_ROOT);

		String brnLogPath = LOG_ROOT + File.separator + TextDefine.LOG_EXPORT
				+ viewName + "_" + strBrn + TextDefine.ATTACH_LOG;

		return new PrintWriter(new BufferedWriter(new OutputStreamWriter(
				new FileOutputStream(new File(brnLogPath)))), true);
	}
}
