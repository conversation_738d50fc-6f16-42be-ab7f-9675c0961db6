/* 
 * L260S01BDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L260S01B;

/** 貸後管理餘屋貸款檔 **/
public interface L260S01BDao extends IGenericDao<L260S01B> {

	L260S01B findByOid(String oid);
	
	List<L260S01B> findByMainId(String mainId, boolean notIncDel);

	List<L260S01B> findByIndex01(String mainId, String cntrNo);
}