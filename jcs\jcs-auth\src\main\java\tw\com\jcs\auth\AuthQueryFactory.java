package tw.com.jcs.auth;

import org.springframework.jdbc.core.RowCallbackHandler;

/**
 * <pre>
 * 權限資料查詢
 * </pre>
 * 
 * @since 2022年12月21日
 * <AUTHOR> @version
 *          <ul>
 *          <li>2022年12月21日
 *          </ul>
 */
public interface AuthQueryFactory {

    /**
     * 使用者查詢
     * 
     * @param handler
     *            {@link org.springframework.jdbc.core.RowCallbackHandler}
     */
    void execUserQuery(RowCallbackHandler handler);

    /**
     * 單位查詢
     * 
     * @param handler
     *            {@link org.springframework.jdbc.core.RowCallbackHandler}
     */
    void execDeptQuery(RowCallbackHandler handler);

    /**
     * 角色功能查詢
     * 
     * @param handler
     *            {@link org.springframework.jdbc.core.RowCallbackHandler}
     */
    void execRoleAuthQuery(RowCallbackHandler handler);

    /**
     * 使用者角色查詢
     * 
     * @param handler
     *            {@link org.springframework.jdbc.core.RowCallbackHandler}
     */
    void execUserRoleQuery(RowCallbackHandler handler);

    /**
     * 功能查詢
     * 
     * @param handler
     *            {@link org.springframework.jdbc.core.RowCallbackHandler}
     */
    void execCodeQuery(RowCallbackHandler handler);

    /**
     * 分行查詢
     * 
     * @param handler
     *            {@link org.springframework.jdbc.core.RowCallbackHandler}
     */
    void execBranchQuery(RowCallbackHandler handler);

    /**
     * 角色查詢
     * 
     * @param handler
     *            {@link org.springframework.jdbc.core.RowCallbackHandler}
     */
    // add by fantasy 2012/02/29
    void execRoleQuery(RowCallbackHandler handler);

    /**
     * 文件查詢
     * 
     * @param handler
     *            {@link org.springframework.jdbc.core.RowCallbackHandler}
     */
    // add by fantasy 2012/04/13
    void execDocAuthQuery(RowCallbackHandler handler);

    /**
     * 取得權限系統代碼 C.擔保品 I.徵信 L.企金授信 S.消金授信 O.逾放催收 D.資料建檔
     * 
     * @return
     */
    String getSystemType();
}