/* 
 * C240M01BDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.C240M01BDao;
import com.mega.eloan.lms.model.C240M01B;

/** 消金覆審名單關聯檔 **/
@Repository
public class C240M01BDaoImpl extends LMSJpaDao<C240M01B, String>
	implements C240M01BDao {

	@Override
	public C240M01B findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C240M01B> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.setMaxResults(Integer.MAX_VALUE);
		List<C240M01B> list = createQuery(C240M01B.class,search).getResultList();
		return list;
	}
	
	@Override
	public C240M01B findByUniqueKey(String mainId, String refMainId){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "refMainId", refMainId);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C240M01B> findByIndex01(String mainId, String refMainId){
		ISearch search = createSearchTemplete();
		List<C240M01B> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (refMainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "refMainId", refMainId);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(C240M01B.class,search).getResultList();
		}
		return list;
	}
	
	@Override
	public void deleteByC240M01AMainid(String mainId){
		Query query = getEntityManager().createNamedQuery("C240M01B.delByC240m01aMainid");
		query.setParameter("mainId", mainId); // 設置參數
		query.executeUpdate();
	}
}