<?xml version="1.0" encoding="UTF-8"?>
<persistence version="2.0"
	xmlns="http://java.sun.com/xml/ns/persistence" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://java.sun.com/xml/ns/persistence http://java.sun.com/xml/ns/persistence/persistence_2_0.xsd">
	<persistence-unit name="pu-lms4com">
		<class>com.mega.eloan.common.model.AuditLog</class>
		<class>com.mega.eloan.common.model.AuditData</class>			
	
		<exclude-unlisted-classes>true</exclude-unlisted-classes>
	</persistence-unit>
</persistence>
