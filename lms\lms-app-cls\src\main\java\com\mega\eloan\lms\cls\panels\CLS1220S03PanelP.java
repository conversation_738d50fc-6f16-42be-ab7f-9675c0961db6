package com.mega.eloan.lms.cls.panels;

import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * 線上申貸原始資料(服務單位)
 * </pre>
 * 
 * @since 2020/6/23
 * <AUTHOR>
 * @version <ul>
 *          <li>2020/6/23,EL08034,new
 *          </ul>
 */
public class CLS1220S03PanelP extends Panel {

	private static final long serialVersionUID = 1L;

	/**
	 * @param id
	 */
	public CLS1220S03PanelP(String id) {
		super(id);		
	}

	/**
	 * @param id
	 * @param updatePanelName
	 */
	public CLS1220S03PanelP(String id, boolean updatePanelName) {
		super(id, updatePanelName);
	}

}
