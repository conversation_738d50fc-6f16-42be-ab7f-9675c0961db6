---------------------------------------------------------
-- LMS.C900M01A 個金產品名稱檔
---------------------------------------------------------


---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.C900M01A;
CREATE TABLE LMS.C900M01A (
	OID           CHAR(32)     ,
	PRODKIND      VARCHAR(2)    not null,
	PRODNM1       VARCHAR(60)  ,
	PRODNM2       VARCHAR(60)  ,
	CREATOR       CHAR(6)      ,
	CREA<PERSON>TI<PERSON>    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UP<PERSON><PERSON><PERSON><PERSON>    TIMESTAMP    ,

	constraint P_C900M01A PRIMARY KEY(PRODKIND)
) IN EL_DATA_4KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XC900M01A01;
--CREATE UNIQUE INDEX LMS.XC900M01A01 ON LMS.C900M01A   (PRODKIND);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.C900M01A IS '個金產品名稱檔';
COMMENT ON LMS.C900M01A (
	OID           IS 'oid', 
	PRODKIND      IS '產品代號', 
	PRODNM1       IS '產品名稱1', 
	PRODNM2       IS '產品名稱2', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
