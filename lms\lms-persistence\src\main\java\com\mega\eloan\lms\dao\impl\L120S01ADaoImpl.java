package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.L120S01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L120S01A;

/** 借款人主檔 **/
@Repository
public class L120S01ADaoImpl extends LMSJpaDao<L120S01A, String> implements
		L120S01ADao {

	@Override
	public L120S01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120S01A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addOrderBy("keyMan", true);
		search.addOrderBy("custShowSeqNum", false);
		search.addOrderBy("custId", false);
		search.addOrderBy("dupNo", false);
		List<L120S01A> list = createQuery(L120S01A.class, search)
				.getResultList();
		
		return list;
	}

	@Override
	public List<L120S01A> findByMainIdForOrder(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addOrderBy("keyMan", true);
		search.addOrderBy("custShowSeqNum", false);
		search.addOrderBy("custId", false);
		search.addOrderBy("dupNo", false);
		List<L120S01A> list = createQuery(L120S01A.class, search)
				.getResultList();
		return list;
	}

	@Override
	public L120S01A findByUniqueKey(String mainId, String custId, String dupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);

		return findUniqueOrNone(search);
	}

	@Override
	public int delModel(String mainId) {
		Query query = getEntityManager().createNamedQuery("L120S01A.delModel");
		query.setParameter("MAINID", mainId); // 設置參數
		return query.executeUpdate();
	}

	@Override
	public List<L120S01A> findByOids(String[] oids) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.IN, "oid", oids);
		List<L120S01A> list = createQuery(L120S01A.class, search)
				.getResultList();
		return list;
	}
	@Override
	public List<L120S01A> findByCustIdDupId(String custId,String DupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", DupNo);
		List<L120S01A> list = createQuery(L120S01A.class,search).getResultList();
		return list;
	}
}