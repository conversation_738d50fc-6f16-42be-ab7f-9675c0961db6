/* 
 * LPDFA01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.LPDFA01A;

/** 授信 PDF 舊案授權檔 **/
public interface LPDFA01ADao extends IGenericDao<LPDFA01A> {

	LPDFA01A findByOid(String oid);
	
	List<LPDFA01A> findByMainId(String mainId);

	List<LPDFA01A> findByIndex01(String mainId, String ownUnit, String authType, String authUnit);
}