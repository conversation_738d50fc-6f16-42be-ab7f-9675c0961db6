package com.mega.eloan.lms.base.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.CharUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.inet.report.ReportException;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.ContractDocUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dao.L120S01ADao;
import com.mega.eloan.lms.dao.L120S01BDao;
import com.mega.eloan.lms.dao.L140M01ADao;
import com.mega.eloan.lms.dao.L140M01BDao;
import com.mega.eloan.lms.dao.L140M01CDao;
import com.mega.eloan.lms.dao.L140M01DDao;
import com.mega.eloan.lms.dao.L140M01FDao;
import com.mega.eloan.lms.dao.L140M01HDao;
import com.mega.eloan.lms.dao.L140M01NDao;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.enums.L140M01NEnum;
import com.mega.eloan.lms.enums.L140M01NEnum.RateKindEnum;
import com.mega.eloan.lms.enums.L140M01NEnum.RateTypeEnum;
import com.mega.eloan.lms.enums.L140M01NEnum.SecNoOpEnum;
import com.mega.eloan.lms.mfaloan.service.LNLNF070Service;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.mfaloan.service.MisMislnratService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01C;
import com.mega.eloan.lms.model.L120S01A;
import com.mega.eloan.lms.model.L120S01B;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01B;
import com.mega.eloan.lms.model.L140M01C;
import com.mega.eloan.lms.model.L140M01D;
import com.mega.eloan.lms.model.L140M01F;
import com.mega.eloan.lms.model.L140M01H;
import com.mega.eloan.lms.model.L140M01N;
import com.mega.eloan.lms.model.L140S09A;
import com.mega.eloan.lms.model.L140S09B;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.formatter.NumericFormatter;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.PropUtil;
import tw.com.jcs.common.Util;

@Service("LmsContractDocWService")
public class LMSContractDocWServiceImpl implements FileDownloadService {

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(LMSContractDocServiceImpl.class);

	@Resource
	BranchService branchService;

	@Resource
	CodeTypeService codeTypeService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	LMSService lmsService;
	
	@Resource
	EloandbBASEService eloandbBASEService;

	@Resource
	MisCustdataService misCustdataService;

	@Resource
	MisMislnratService misMislnratService;

	@Resource
	MisdbBASEService misdbBASEService;

	@Resource
	LNLNF070Service lnlnf070Service;

	@Resource
	L140M01ADao l140m01aDao;

	@Resource
	L140M01BDao l140m01bDao;

	@Resource
	L140M01CDao l140m01cDao;

	@Resource
	L140M01DDao l140m01dDao;

	@Resource
	L140M01FDao l140m01fDao;

	@Resource
	L140M01NDao l140m01nDao;

	@Resource
	L140M01HDao l140m01hDao;

	@Resource
	L120M01ADao l120m01aDao;

	@Resource
	L120S01ADao l120s01aDao;

	@Resource
	L120S01BDao l120s01bDao;

	private final static String 換行符號 = "<w:br/>";

	@Override
	public byte[] getContent(PageParameters params) throws CapException,
			FileNotFoundException, ReportException, IOException, Exception {

		OutputStream outputStream = null;
		ByteArrayOutputStream baos = null;
		try {
			outputStream = this.creatDoc(params);
			if (outputStream != null) {
				baos = (ByteArrayOutputStream) outputStream;
			}
			return baos.toByteArray();
		} catch (Exception ex) {
			LOGGER.error("[getContent] Exception!!", ex);
		} finally {
			if (baos != null) {
				IOUtils.closeQuietly(baos);
			}
		}
		return null;
	}

	public OutputStream creatDoc(PageParameters params) {
		OutputStream outputStream = null;
		String contractType = params.getString("contractType");
		try {
			if (Util.equals(contractType, "W03")) {
				outputStream = this.getLMS_W03(params);
			} else if (Util.equals(contractType, "W04")) {
				outputStream = this.getLMS_W04(params);
			} else if (Util.equals(contractType, "W01")) {
				outputStream = this.getLMS_W01(params);
			}
		} catch (Exception e) {
			LOGGER.error(e.getMessage());
		}
		return outputStream;
	}

	/**
	 * 取得03.兆豐授信約定書Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getLMS_W03(PageParameters params) throws CapException {
		String templateName = "LMS_W03.xml";

		ByteArrayOutputStream baos = null;
		String mainId = Util.nullToSpace(params.getString("tabFormMainId")); // 額度明細表mainId

		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;
		try {
			// 取得XML範本檔案名稱
			// 讀檔
			content = Util.getFileContent(Util.trim(PropUtil
					.getProperty("loadFile.dir")) + "word/" + templateName);

			// default
			map.put("L140M01AcustName", "");
			map.put("L120S01AcustName", "");
			map.put("L120S01AcustName2", "");
			map.put("L120S01AcustId", "");
			map.put("L120S01BcmpAddr", "");
			map.put("L120S01Bchairman", "");
			map.put("L120S01BchairmanId", "");

			// 撈資料
			L140M01A l140m01a = null;
			l140m01a = l140m01aDao.findByMainId(mainId);
			if (l140m01a == null)
				l140m01a = new L140M01A();
			L120M01C l120m01c = l140m01a.getL120m01c();
			if (l120m01c == null)
				l120m01c = new L120M01C();
			L120M01A l120m01a = l120m01aDao.findByMainId(l120m01c.getMainId());
			if (l120m01a == null)
				l120m01a = new L120M01A();

			L120S01A l120s01a = null;
			l120s01a = l120s01aDao.findByUniqueKey(l120m01a.getMainId(),
					l140m01a.getCustId(), l140m01a.getDupNo());
			if (l120s01a == null)
				l120s01a = new L120S01A();

			L120S01B l120s01b = null;
			l120s01b = l120s01bDao.findByUniqueKey(l120m01a.getMainId(),
					l140m01a.getCustId(), l140m01a.getDupNo());
			if (l120s01b == null)
				l120s01b = new L120S01B();

			// 塞值
			map.put("L140M01AcustName", l140m01a.getCustName());
			Map<String, Object> map0024 = misCustdataService.findByIdDupNo(
					Util.trim(l120s01a.getCustId()),
					Util.trim(l120s01a.getDupNo()));
			if (map0024 != null) {
				map.put("L120S01AcustName", Util.trim(map0024.get("CNAME")));
				map.put("L120S01AcustName2", Util.trim(map0024.get("CNAME")));
				map.put("L120S01AcustId", Util.trim(map0024.get("CUSTID"))
						+ " " + Util.trim(map0024.get("DUPNO")));
				map.put("L120S01BcmpAddr", Util.trim(map0024.get("FULLADDR")));
			} else {
				map.put("L120S01AcustName",
						Util.nullToSpace(l120s01a.getCustName()));
				map.put("L120S01AcustName2",
						Util.nullToSpace(l120s01a.getCustName()));
				map.put("L120S01AcustId", Util.nullToSpace(l120s01a.getDupNo())
						+ " " + Util.nullToSpace(l120s01a.getDupNo()));
				map.put("L120S01BcmpAddr",
						Util.nullToSpace(l120s01b.getCmpAddr()));
			}
			map.put("L120S01Bchairman",
					Util.nullToSpace(l120s01b.getChairman()));
			map.put("L120S01BchairmanId",
					Util.nullToSpace(l120s01b.getChairmanId()) + " "
							+ Util.nullToSpace(l120s01b.getChairmanDupNo()));

			// baos = this.writeWordContent(content, map);
			map = this.replaceMapContStr(map); // 取代特定文字
			// 可能有換行的，不要呼叫convert_paramValue_for_XML_Predefined_entities
			// 會把< > 變成大寫的＜＞導致換行失效
			String traceStr = "【l140m01a.mainId=" + mainId + "】";
			String outputStr = join_word_template_param(traceStr, content, map);
			baos = this.writeWordContent(outputStr);

		} catch (FileNotFoundException e) {
			LOGGER.error(e.getMessage());
			throw new CapMessageException(e.getMessage(), getClass());
		} catch (IOException e) {
			LOGGER.error(e.getMessage());
			throw new CapMessageException(e.getMessage(), getClass());
		} catch (Exception e) {
			LOGGER.error(e.getMessage());
			throw new CapMessageException(e.getMessage(), getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 取得04.兆豐中長期契約書Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getLMS_W04(PageParameters params) throws CapException {
		String templateName = "LMS_W04.xml";

		ByteArrayOutputStream baos = null;
		String mainId = Util.nullToSpace(params.getString("tabFormMainId")); // 額度明細表mainId

		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;
		Map<String, String> currMap = null;
		Map<String, String> rateGetIntMap = null;
		try {
			currMap = codeTypeService.findByCodeType("Common_Currcy");
			if (currMap == null)
				currMap = new LinkedHashMap<String, String>();
			rateGetIntMap = codeTypeService
					.findByCodeType("lms1401s0204_rateGetInt");
			if (rateGetIntMap == null)
				rateGetIntMap = new LinkedHashMap<String, String>();

			// 1新台幣、2美金、3日幣、4歐元、5人民幣、6澳幣、7港幣、Z雜幣
			HashMap<String, String> moneyMap = new HashMap<String, String>();
			moneyMap.put("1", "TWD");
			moneyMap.put("2", "USD");
			moneyMap.put("3", "JPY");
			moneyMap.put("4", "EUR");
			moneyMap.put("5", "CNY");
			moneyMap.put("6", "AUD");
			moneyMap.put("7", "HKD");
			moneyMap.put("Z", "OTH");

			// 取得XML範本檔案名稱
			// 讀檔
			content = Util.getFileContent(Util.trim(PropUtil
					.getProperty("loadFile.dir")) + "word/" + templateName);

			// default
			map.put("L140M01AcustName", ""); // 立約人
			map.put("proxy", ""); // 甲方
			map.put("party", ""); // 乙方
			map.put("party2", "　　　"); // 乙方
			map.put("itemA", ""); // 授信用途
			map.put("itemB", ""); // 授信金額
			map.put("itemC", ""); // 動用方式及條件
			map.put("itemD", ""); // 撥款方式
			map.put("itemE", ""); // 償還期限及方式
			map.put("itemF", ""); // 利息手續費計付
			map.put("itemG", ""); // 違約金及遲延利息計付
			map.put("itemH", ""); // 期前清償違約金計付
			map.put("itemI", ""); // 承諾費
			map.put("itemJ", ""); // 其他個別商議條件
			map.put("baseRate", ""); // 基準利率
			map.put("proxyBoss", ""); // 甲方負責人
			map.put("proxyAddr", ""); // 甲方地址
			map.put("SpecialTerms", "");

			// 撈資料
			L140M01A l140m01a = null;
			l140m01a = l140m01aDao.findByMainId(mainId);
			if (l140m01a == null)
				l140m01a = new L140M01A();

			if (l140m01a.getL140m01b() != null) {
				for (L140M01B l140m01b : l140m01a.getL140m01b()) {
					// 樣板格式
					if (Util.equals(Util.trim(l140m01b.getItemType()),
							UtilConstants.Cntrdoc.l140m01bItemType.其他敘做條件)
							&& Util.equals(Util.trim(l140m01b.getFormatType()),
									"2")) {
						map = this.getW04Item(map, l140m01a, currMap);
					}
				}
			}

			List<Map<String, Object>> rows = misMislnratService
					.findMislnratByLRRate("S8", "TWD");
			String baseRate = "";
			if (rows.size() > 0) {
				baseRate = rows.get(0).get("LR_RATE").toString();
			}
			String baseRateStr = new NumericFormatter("##.#####")
					.reformat(baseRate);

			List<L140M01F> l140m01fs = l140m01fDao.findByMainId(mainId);

			// 利息計付
			if (l140m01fs != null && !l140m01fs.isEmpty()) {
				String firstLoanTP = this.getFirstLoanTP(new String[] { "H",
						"I", "N" }, l140m01a);
				for (L140M01F l140m01f : l140m01fs) {
					String[] TPList = Util.trim(l140m01f.getLoanTPList())
							.split(UtilConstants.Mark.SPILT_MARK);
					if (Arrays.asList(TPList).contains(firstLoanTP)) {
						String[] arrStr = this.getInterest(l140m01a, l140m01f,
								"", moneyMap, rateGetIntMap, currMap);
						StringBuffer sbAll = new StringBuffer();
						int listCount = 0;
						if (arrStr[0].length() > 0) {
							listCount++;
							sbAll.append(
									Util.toFullCharString(Integer
											.toString(listCount))
											+ "、"
											+ currMap.get("USD") + "：").append(
									arrStr[0]);
						}
						if (arrStr[1].length() > 0) {
							listCount++;
							sbAll.append((sbAll.length() > 0 ? 換行符號 : ""));
							sbAll.append(
									Util.toFullCharString(Integer
											.toString(listCount))
											+ "、"
											+ currMap.get("TWD") + "：").append(
									arrStr[1]);
						}
						if (arrStr[2].length() > 0) {
							listCount++;
							sbAll.append((sbAll.length() > 0 ? 換行符號 : ""));
							sbAll.append(arrStr[2]);
						}
						if (listCount < 2) {
							String allStr = sbAll.toString();
							allStr = allStr.replaceFirst(
									Util.toFullCharString(Integer
											.toString(listCount)) + "、", "");
							sbAll.setLength(0);
							sbAll.append(allStr);
						} else {
							if (sbAll.length() > 0) {
								sbAll.insert(0, 換行符號);
							}
						}
						map.put("item" + "F", sbAll.toString()); // CtrConstants.L999S04BItemType.利息手續費計付
					}
				}
			}

			// 塞值
			map.put("L140M01AcustName", l140m01a.getCustName());
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			IBranch branch = branchService.getBranch(user.getUnitNo());
			String branchName = branch.getBrName();
			map.put("proxy", "兆豐國際商業銀行股份有限公司" + branchName);
			map.put("party",
					Util.trim(l140m01a.getCustId()) + " "
							+ Util.trim(l140m01a.getDupNo()) + " "
							+ Util.trim(l140m01a.getCustName()));
			String bossname = Util.trim(userInfoService.getUserName(Util
					.trim(branch.getBrnMgr())));
			map.put("proxyBoss", bossname);
			map.put("proxyAddr", Util.trim(branch.getAddr()));
			map.put("baseRate", Util.parseBigDecimal(baseRateStr).toString()); // NumConverter.numberToChinese(

			// J-110-0372 共用樣版 - 特別條款
			map.put("SpecialTerms", this.getCommonCont(l140m01a.getMainId(), 43, true));
			/*
			map.put("SpecialTerms", "第" + NumConverter.numberToChinese(43) + "條：" 
					+ this.getCommonCont(l140m01a.getMainId(), 43, true));
			*/
			
			// baos = this.writeWordContent(content, map);
			map = this.replaceMapContStr(map); // 取代特定文字
			// 可能有換行的，不要呼叫convert_paramValue_for_XML_Predefined_entities
			// 會把< > 變成大寫的＜＞導致換行失效
			String traceStr = "【l140m01a.mainId=" + mainId + "】";
			String outputStr = join_word_template_param(traceStr, content, map);
			baos = this.writeWordContent(outputStr);

		} catch (FileNotFoundException e) {
			LOGGER.error(e.getMessage());
			throw new CapMessageException(e.getMessage(), getClass());
		} catch (IOException e) {
			LOGGER.error(e.getMessage());
			throw new CapMessageException(e.getMessage(), getClass());
		} catch (Exception e) {
			LOGGER.error(e.getMessage());
			throw new CapMessageException(e.getMessage(), getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	public String getFirstLoanTP(String[] bizCatArr, L140M01A l140m01a) {
		String firstLoanTP = "";

		HashSet<String> loanTPs = new HashSet<String>(); // 屬於樣版的科目

		// 取得樣版與科目對應
		String bizCat_LoanTP = Util.trim(lmsService
				.getSysParamDataValue("LMS_BIZCAT_LOANTP"));
		if (Util.notEquals(bizCat_LoanTP, "")) {
			JSONObject jsonBizCat_LoanTP = JSONObject.fromObject("{"
					+ bizCat_LoanTP + "}");
			if (jsonBizCat_LoanTP != null) {
				for (String bizCat : bizCatArr) {
					String loanTPList = Util
							.trim(jsonBizCat_LoanTP.get(bizCat));
					if (Util.isNotEmpty(loanTPList)) {
						String[] loanTPArr = StringUtils.split(loanTPList, "|");
						for (String loanTP : loanTPArr) {
							loanTPs.add(loanTP);
						}
					}
				}
			}
		}

		List<L140M01C> l140m01cs = l140m01cDao.findByMainId(l140m01a
				.getMainId());
		for (L140M01C l140m01c : l140m01cs) {
			String key = Util.getLeftStr(l140m01c.getLoanTP(), 3);
			if (loanTPs.contains(key)) {
				firstLoanTP = l140m01c.getLoanTP();
				break;
			}
		}

		return firstLoanTP;
	}

	public Map<String, String> getW04Item(Map<String, String> map,
			L140M01A l140m01a, Map<String, String> currMap)
			throws CapMessageException {
		HashSet<String> loanTPs = new HashSet<String>(); // 屬於中長期樣版的科目
		String[] bizCatA = new String[] { "H", "I", "N" }; // 屬於中長期的樣板代號
		// 取得樣版與科目對應
		String bizCat_LoanTP = Util.trim(lmsService
				.getSysParamDataValue("LMS_BIZCAT_LOANTP"));
		if (Util.notEquals(bizCat_LoanTP, "")) {
			JSONObject jsonBizCat_LoanTP = JSONObject.fromObject("{"
					+ bizCat_LoanTP + "}");
			if (jsonBizCat_LoanTP != null) {
				for (String bizCat : bizCatA) {
					String loanTPList = Util
							.trim(jsonBizCat_LoanTP.get(bizCat));
					if (Util.isNotEmpty(loanTPList)) {
						String[] loanTPArr = StringUtils.split(loanTPList, "|");
						for (String loanTP : loanTPArr) {
							loanTPs.add(loanTP);
						}
					}
				}
			}
		}

		boolean hasLoanTP = false;
		if (l140m01a.getL140m01c() != null) {
			for (L140M01C l140m01c : l140m01a.getL140m01c()) {
				if (loanTPs.contains(l140m01c.getLoanTP())) {
					hasLoanTP = true;
					break;
				}
			}
		}
		if (hasLoanTP) {
			HashMap<String, String[]> type = new HashMap<String, String[]>(); // 對應樣版項目
			type.put("A", new String[] { "H01", "I01", "N01" }); // CtrConstants.L999S04BItemType.授信用途
			type.put("C", new String[] { "H04", "I04", "N03" }); // CtrConstants.L999S04BItemType.動用方式及條件
			type.put("D", new String[] { "H08", "I08", "N08" }); // CtrConstants.L999S04BItemType.撥款方式
			type.put("E", new String[] { "H05", "I05", "N04" }); // CtrConstants.L999S04BItemType.償還期限及方式
			type.put("G", new String[] { "H09", "I09", "N09" }); // CtrConstants.L999S04BItemType.違約金及遲延利息計付
			type.put("H", new String[] { "H10", "I10", "N10" }); // CtrConstants.L999S04BItemType.期前清償違約金計付
			type.put("I", new String[] { "H11", "I11", "N11" }); // CtrConstants.L999S04BItemType.承諾費

			HashMap<String, String[]> usedItem = new HashMap<String, String[]>(); // 已經串過的資料
			usedItem.put("H", new String[] { "01", "04", "05", "08", "09",
					"10", "11" });
			usedItem.put("I", new String[] { "01", "04", "05", "08", "09",
					"10", "11" });
			usedItem.put("N", new String[] { "01", "03", "04", "08", "09",
					"10", "11" });

			for (String itemType : type.keySet()) {
				int seq = 1;
				StringBuffer sb = new StringBuffer();
				String[] codeArr = type.get(itemType);
				for (String code : codeArr) {
					String bizCat = (code.length() >= 1 ? code.substring(0, 1)
							: code);
					String bizItem = (code.length() >= 3 ? code.substring(1, 3)
							: code);
					List<L140S09A> l140s09as = lmsService.findL140s09a(
							l140m01a.getMainId(), "", bizCat, bizItem);
					for (L140S09A l140s09a : l140s09as) {
						List<L140S09B> l140s09bs = lmsService
								.findL140s09bByMainId(Util.trim(l140s09a
										.getOid()));
						for (L140S09B l140s09b : l140s09bs) {
							String str = l140s09b.getCont();
							sb.append((sb.length() > 0 ? 換行符號 : ""));
							sb.append(seq > 1 ? (seq + ". ") : "").append(str);
						}
					}
				}
				// 動用方式及條件 要多串 "動用先決條件"
				if (Util.equals(itemType, "C")) {
					StringBuffer sbC = new StringBuffer();
					int seqC = 0;
					String[] itemList = new String[] { "H03", "I03" };
					for (String item : itemList) {
						String bizCat = (item.length() >= 1 ? item.substring(0,
								1) : item);
						String bizItem = (item.length() >= 3 ? item.substring(
								1, 3) : item);
						List<L140S09A> l140s09as = lmsService.findL140s09a(
								l140m01a.getMainId(), "", bizCat, bizItem);
						for (L140S09A l140s09a : l140s09as) {
							List<L140S09B> l140s09bs = lmsService
									.findL140s09bByMainId(Util.trim(l140s09a
											.getOid()));
							for (L140S09B l140s09b : l140s09bs) {
								String str = l140s09b.getCont();
								sbC.append((sbC.length() > 0 ? 換行符號 : ""));
								seqC++;
								sbC.append(seqC > 1 ? ("(" + seqC + ")") : "")
										.append(str);
							}
						}
					}
					if (sbC.length() > 0) {
						seq++;
						sbC.insert(0, seq + ". 動用先決條件："
								+ (seqC > 1 ? (換行符號 + "(1)") : ""));
						sb.append((sb.length() > 0 ? 換行符號 : "")).append(sbC);
					}
				}
				if (sb.length() > 0 && seq > 1) {
					sb.insert(0, 換行符號 + "1. ");
				}
				map.put("item" + itemType, sb.toString());
			}

			// 承諾事項 & 其他未盡事宜
			HashMap<String, String[]> elseItem = new HashMap<String, String[]>();
			elseItem.put("H", new String[] { "06", "07", "12" });
			elseItem.put("I", new String[] { "06", "07", "12" });
			elseItem.put("N", new String[] { "05", "07" });
			StringBuffer sbElse = new StringBuffer();
			int seqAll = 1;
			for (String bizCat : bizCatA) {
				List<L140S09A> l140s09as = lmsService.findL140s09a(
						l140m01a.getMainId(), "", bizCat, "");
				for (L140S09A l140s09a : l140s09as) {
					String[] bizItemArr = elseItem.get(bizCat);
					String bizItemStr = Util.nullToSpace(l140s09a.getBizItem());
					// 剩下的資料都丟到 其他個別商議條件
					if (Arrays.asList(bizItemArr).contains(bizItemStr)) {
						List<L140S09B> l140s09bs = lmsService
								.findL140s09bByMainId(Util.trim(l140s09a
										.getOid()));
						for (L140S09B l140s09b : l140s09bs) {
							String str = l140s09b.getCont();
							sbElse.append((sbElse.length() > 0 ? 換行符號 : ""));
							sbElse.append(seqAll++).append(". ").append(str);
						}
					}
				}
			}
			if (sbElse.length() > 0) {
				sbElse.insert(0, 換行符號);
			}
			map.put("item" + "J", sbElse.toString()); // CtrConstants.L999S04BItemType.其他個別商議條件

			// 授信金額
			String content = MapUtils.getString(currMap,
					Util.trim(l140m01a.getCurrentApplyCurr()),
					Util.trim(l140m01a.getCurrentApplyCurr()))
					+ " "
					+ NumConverter.toChineseNumber(Util
							.parseBigDecimal(l140m01a.getCurrentApplyAmt()))
					+ "元整";
			map.put("item" + "B", content); // CtrConstants.L999S04BItemType.授信金額

		}

		return map;
	}

	/**
	 * 取得01.兆豐綜合授信契約書Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getLMS_W01(PageParameters params) throws CapException {
		String templateName = "LMS_W01.xml";

		ByteArrayOutputStream baos = null;
		String mainId = Util.nullToSpace(params.getString("tabFormMainId")); // 額度明細表mainId

		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;

		Map<String, String> yesNoMap = null;
		Map<String, String> currMap = null;
		Map<String, String> rateGetIntMap = null;
		Map<String, String> monTypeMap = null;
		Map<String, Object> brMap = null;
		try {
			DecimalFormat df = new DecimalFormat(
					"###,###,###,###,###,###,###,###,###,##0.####");
			yesNoMap = codeTypeService.findByCodeType("Common_YesNo1");
			if (yesNoMap == null)
				yesNoMap = new LinkedHashMap<String, String>();
			currMap = codeTypeService.findByCodeType("Common_Currcy");
			if (currMap == null)
				currMap = new LinkedHashMap<String, String>();
			rateGetIntMap = codeTypeService
					.findByCodeType("lms1401s0204_rateGetInt");
			if (rateGetIntMap == null)
				rateGetIntMap = new LinkedHashMap<String, String>();
			monTypeMap = codeTypeService.findByCodeType("lms1405s0204_monType");
			if (monTypeMap == null)
				monTypeMap = new LinkedHashMap<String, String>();

			// 1新台幣、2美金、3日幣、4歐元、5人民幣、6澳幣、7港幣、Z雜幣
			HashMap<String, String> moneyMap = new HashMap<String, String>();
			moneyMap.put("1", "TWD");
			moneyMap.put("2", "USD");
			moneyMap.put("3", "JPY");
			moneyMap.put("4", "EUR");
			moneyMap.put("5", "CNY");
			moneyMap.put("6", "AUD");
			moneyMap.put("7", "HKD");
			moneyMap.put("Z", "OTH");

			// 取得XML範本檔案名稱
			// 讀檔
			content = Util.getFileContent(Util.trim(PropUtil
					.getProperty("loadFile.dir")) + "word/" + templateName);

			// default
			map.put("L140M01AcustName", "");
			map.put("L120S01AcustName", "");
			map.put("L120S01AcustName2", "");
			map.put("L120S01AcustId", "");
			map.put("L120S01BcmpAddr", "");
			map.put("L120S01Bchairman", "");
			map.put("brBoss", "");
			map.put("brAddr", "");
			map.put("itemTypeCount", "");

			/*
			 * CtrConstants.L999S01BItemType. 購料借款 = "A"; 外銷借款 = "B"; 營運週轉借款 =
			 * "C"; 貼現 = "D"; 透支 = "E"; 委任票據保證 = "F"; 委任票據承兌 = "G"; 委任保證 = "H";
			 */
			String[] defaultArr = new String[] { "A", "B", "C", "D", "E", "F",
					"G", "H" };
			for (String type : defaultArr) {
				map.put("item" + type, "");
				map.put("reUse" + type, "");
				map.put("loanAmt" + type, ""); // D.E 為新台幣
				map.put("interest" + type, "");
			}
			map.put("totLoanAmt", "");
//			map.put("useDeadline", "");
            map.put("baseCurr", "");
			map.put("baseRate", "");
			map.put("dMonth1", "");
			map.put("dMonth2", "");
			map.put("dRate1", "");
			map.put("dRate2", "");
			map.put("dRateAdd1", "");
			map.put("dRateAdd2", "");
			// 利息計付 A.購料借款 特別分開幣別
			map.put("interestA_USD", "");
			map.put("interestA_TWD", "");
			map.put("interestA_OTH", "");
			map.put("rateGetInt", "");
			map.put("paStr", "");
			map.put("periodB05_01_1", "");
			map.put("periodB05_01_2", "");
			map.put("periodB05_01_3", "");
			map.put("periodB05_02_1", "");
			map.put("useWayB02_01_1", "");
			map.put("useWayB02_01_2", "");
			map.put("lmtDaysB", "");
			map.put("lmtPeriodC", "");
			map.put("useWayC", "");
			map.put("lmtDaysD", "");
			map.put("useWayO02_01_1", "");
			map.put("purposeG01_01_2", "");
			map.put("lmtPeriodE", "");
			map.put("cpStr", "");
			map.put("feeH", "");
			map.put("rangeH", "");
			map.put("wayH", "");
			map.put("OtherTerms", "");
			map.put("seqOT", "0");
			map.put("SpecialTerms", "");

			// 撈資料
			L140M01A l140m01a = null;
			l140m01a = l140m01aDao.findByMainId(mainId);
			if (l140m01a == null)
				l140m01a = new L140M01A();
			L120M01C l120m01c = l140m01a.getL120m01c();
			if (l120m01c == null)
				l120m01c = new L120M01C();
			L120M01A l120m01a = l120m01aDao.findByMainId(l120m01c.getMainId());
			if (l120m01a == null)
				l120m01a = new L120M01A();

			L120S01A l120s01a = null;
			l120s01a = l120s01aDao.findByUniqueKey(l120m01a.getMainId(),
					l140m01a.getCustId(), l140m01a.getDupNo());
			if (l120s01a == null)
				l120s01a = new L120S01A();

			L120S01B l120s01b = null;
			l120s01b = l120s01bDao.findByUniqueKey(l120m01a.getMainId(),
					l140m01a.getCustId(), l140m01a.getDupNo());
			if (l120s01b == null)
				l120s01b = new L120S01B();

			HashMap<String, String[]> hasItem = this.getHasItem(l140m01a);
			HashMap<String, String> hasItemYN = new HashMap<String, String>();
			HashMap<String, String> hasItemFirst = new HashMap<String, String>();
			for (String item : hasItem.keySet()) {
				String[] data = hasItem.get(item);
				String yn = data[0];
				String first = data[1];
				hasItemYN.put(item, yn);
				hasItemFirst.put(item, first);
			}

			List<Map<String, Object>> rows = misMislnratService
					.findMislnratByLRRate("S8", "TWD");
			String baseRate = "";
			if (rows.size() > 0) {
				baseRate = rows.get(0).get("LR_RATE").toString();
			}
			String baseRateStr = new NumericFormatter("##.#####")
					.reformat(baseRate);

            List<Map<String, Object>> rowsUSD = misMislnratService
                    .findMislnratByLRRate("S8", "USD");
            String baseRateUSD = "";
            if (rowsUSD.size() > 0) {
                baseRateUSD = rowsUSD.get(0).get("LR_RATE").toString();
            }
            String baseRateStrUSD = new NumericFormatter("##.#####")
                    .reformat(baseRateUSD);

			String reuse = Util.trim(l140m01a.getReUse());
			int reuseInt = Util.parseInt(reuse) - 1;
			String reuseStr = (Util.equals(reuse,
					UtilConstants.Cntrdoc.ReUse.不循環使用) ? yesNoMap.get(Integer
					.toString(reuseInt)) : "");

			List<L140M01F> l140m01fs = l140m01fDao.findByMainId(mainId);

			// 塞值
			map.put("L140M01AcustName", l140m01a.getCustName());
			Map<String, Object> map0024 = misCustdataService.findByIdDupNo(
					Util.trim(l120s01a.getCustId()),
					Util.trim(l120s01a.getDupNo()));
			if (map0024 != null) {
				map.put("L120S01AcustName", Util.trim(map0024.get("CNAME")));
				map.put("L120S01AcustName2", Util.trim(map0024.get("CNAME")));
				map.put("L120S01AcustId", Util.trim(map0024.get("CUSTID"))
						+ " " + Util.trim(map0024.get("DUPNO")));
				map.put("L120S01BcmpAddr", Util.trim(map0024.get("FULLADDR")));
			} else {
				map.put("L120S01AcustName",
						Util.nullToSpace(l120s01a.getCustName()));
				map.put("L120S01AcustName2",
						Util.nullToSpace(l120s01a.getCustName()));
				map.put("L120S01AcustId", Util.nullToSpace(l120s01a.getDupNo())
						+ " " + Util.nullToSpace(l120s01a.getDupNo()));
				map.put("L120S01BcmpAddr",
						Util.nullToSpace(l120s01b.getCmpAddr()));
			}
			map.put("L120S01Bchairman",
					Util.nullToSpace(l120s01b.getChairman()));
			IBranch branch = branchService.getBranch(l120m01a.getCaseBrId());
			map.put("brBoss", userInfoService.getUserName(branch.getBrnMgr()));
			brMap = misdbBASEService.findSYNBANK(l120m01a.getCaseBrId());
			if (brMap != null) {
				map.put("brAddr", Util.trim(brMap.get("BRNADDR")));
			}

			int hasCount = 0;
			for (String item : hasItemYN.keySet()) {
				String has = hasItemYN.get(item);
				if (Util.equals(has, "Y")) {
					hasCount++;
					map.put("item" + item, "■");
					map.put("reUse" + item, reuseStr);

					String loanAmt = "";
					String tempCurr = "";
					String firstLoanTP = hasItemFirst.get(item);
					List<L140M01D> l140m01ds = l140m01dDao
							.findByMainIdAndLmtTypeAndSubject(mainId, "1",
									firstLoanTP);
					L140M01D l140m01d = null; // 有資料取限額 無資料用現請額度
					if (l140m01ds != null && !l140m01ds.isEmpty()) {
						l140m01d = l140m01ds.get(0);
						if (Util.equals(item, "D") || Util.equals(item, "E")) {
							// D 貼現.E 透支 範本上寫死為新台幣
						} else {
							tempCurr = Util.trim(l140m01d.getLmtCurr());
						}
						loanAmt = MapUtils.getString(currMap, tempCurr,
								tempCurr)
//								+ " "
								+ NumConverter.toChineseNumber(Util
										.parseBigDecimal(l140m01d.getLmtAmt()))
								+ "元整";
					} else {
						l140m01d = new L140M01D();
						if (Util.equals(item, "D") || Util.equals(item, "E")) {
							// D 貼現.E 透支 範本上寫死為新台幣
						} else {
							tempCurr = Util
									.trim(l140m01a.getCurrentApplyCurr());
						}
						loanAmt = MapUtils.getString(currMap, tempCurr,
								tempCurr)
//								+ " "
								+ NumConverter.toChineseNumber(Util
										.parseBigDecimal(l140m01a
												.getCurrentApplyAmt())) + "元整";
					}
					map.put("loanAmt" + item, loanAmt);

					// 利息計付
					if (l140m01fs != null && !l140m01fs.isEmpty()) {
						for (L140M01F l140m01f : l140m01fs) {
							String[] TPList = Util.trim(
									l140m01f.getLoanTPList()).split(
									UtilConstants.Mark.SPILT_MARK);
							if (Arrays.asList(TPList).contains(firstLoanTP)) {

								String[] arrStr = this.getInterest(l140m01a,
										l140m01f, item, moneyMap,
										rateGetIntMap, currMap);

								if (Util.equals(item, "A")) { // 購料借款
									map.put("interestA_USD", arrStr[0]);
									map.put("interestA_TWD", arrStr[1]);
									map.put("interestA_OTH", arrStr[2]);
									String rgiStr = arrStr[3]
											+ (arrStr[3].length() > 0 ? "；"
													: "")
											+ arrStr[4]
											+ (arrStr[4].length() > 0 ? "；"
													: "") + arrStr[5];
									map.put("rateGetInt", rgiStr);
								} else {
									StringBuffer sbAll = new StringBuffer();
									int listCount = 0;
									if (arrStr[0].length() > 0) {
										listCount++;
										sbAll.append(
												Util.toFullCharString(Integer
														.toString(listCount))
														+ "、"
														+ currMap.get("USD")
														+ "：")
												.append(arrStr[0]);
									}
									if (arrStr[1].length() > 0) {
										listCount++;
										sbAll.append((sbAll.length() > 0 ? 換行符號
												: ""));
										sbAll.append(
												Util.toFullCharString(Integer
														.toString(listCount))
														+ "、"
														+ currMap.get("TWD")
														+ "：")
												.append(arrStr[1]);
									}
									if (arrStr[2].length() > 0) {
										listCount++;
										sbAll.append((sbAll.length() > 0 ? 換行符號
												: ""));
										sbAll.append(arrStr[2]);
									}
									if (listCount < 2) {
										String allStr = sbAll.toString();
										allStr = allStr.replaceFirst(
												Util.toFullCharString(Integer
														.toString(listCount))
														+ "、", "");
										sbAll.setLength(0);
										sbAll.append(allStr);
									}
									map.put("interest" + item, sbAll.toString());
								}

								if (Util.equals(item, "A") // 購料借款
										|| Util.equals(item, "F")) { // 委任票據保證
									L140M01H l140m01h = l140m01hDao
											.findByUniqueKey(
													l140m01a.getMainId(),
													l140m01f.getRateSeq());
									if (l140m01h != null) {
										String paType = l140m01h.getPaType();
										if (Util.isNotEmpty(paType)
												&& Util.notEquals(paType, "c")
												&& Util.equals(item, "A")) { // 購料借款
											String pa = "";
											if (Util.equals(paType, "1")) {
												pa = "年費率"
														+ df.format(Util
																.parseBigDecimal(l140m01h
																		.getPa1Rate()))
														+ "％，以每3個月為一期，按期計收，"
														+ (Util.equals(
																Util.trim(l140m01h
																		.getPa1MD()),
																"5") ? monTypeMap
																.get(Util
																		.trim(l140m01h
																				.getPa1MD()))
																.replace(
																		"X",
																		Integer.toString(l140m01h
																				.getPa1Mon()))
																: monTypeMap
																		.get(Util
																				.trim(l140m01h
																						.getPa1MD())));
											} else if (Util.equals(paType, "2")) {
												pa = "年費率"
														+ df.format(Util
																.parseBigDecimal(l140m01h
																		.getPa2Rate()))
														+ "％，按實際承兌日數計收，"
														+ (Util.equals(
																Util.trim(l140m01h
																		.getPa2MD()),
																"5") ? monTypeMap
																.get(Util
																		.trim(l140m01h
																				.getPa2MD()))
																.replace(
																		"X",
																		Integer.toString(l140m01h
																				.getPa2Mon()))
																: monTypeMap
																		.get(Util
																				.trim(l140m01h
																						.getPa2MD())));
											} else if (Util.equals(paType, "3")) {
												pa = "依規定計收";
											} else if (Util.equals(paType, "4")) {
												pa = l140m01h.getPaDes();
											}
											map.put("paStr", pa);
										}

										String cpType = l140m01h.getCpType();
										if (Util.isNotEmpty(cpType)
												&& Util.notEquals(cpType, "c")
												&& Util.equals(item, "F")) { // 委任票據保證
											String cp = "";
											if (Util.equals(cpType, "1")) {
												cp = "年費率"
														+ df.format(Util
																.parseBigDecimal(l140m01h
																		.getCp1Rate()))
														+ "％，每筆最低收費 新台幣"
														+ l140m01h.getCp1Fee()
														+ "元。";
											} else if (Util.equals(cpType, "2")) {
												cp = "年費率"
														+ df.format(Util
																.parseBigDecimal(l140m01h
																		.getCp2Rate1()))
														+ "％，若由本行簽證承銷，則保證費率為年費率"
														+ df.format(Util
																.parseBigDecimal(l140m01h
																		.getCp2Rate2()))
														+ "。";
											} else if (Util.equals(cpType, "3")) {
												cp = l140m01h.getCpDes();
											}
											map.put("cpStr", cp);
										}
									}
								}

								break;
							}
						}
					}

					map = this.setItemData(map, item, firstLoanTP, l140m01a);
				} else {
					map.put("item" + item, "□");
				}
			}
			map.put("itemTypeCount", Integer.toString(hasCount));
			map.put("totLoanAmt",
					MapUtils.getString(currMap,
							Util.trim(l140m01a.getCurrentApplyCurr()),
							Util.trim(l140m01a.getCurrentApplyCurr()))
//							+ " "
							+ NumConverter.toChineseNumber(Util
									.parseBigDecimal(l140m01a
											.getCurrentApplyAmt())) + "元整");
//			map.put("useDeadline", LMSUtil.getUseDeadline(Util
//					.nullToSpace(l140m01a.getUseDeadline()), Util
//					.nullToSpace(l140m01a.getDesp1()),
//					MessageBundleScriptCreator
//							.getComponentResource(LMSCommomPage.class)));
            map.put("baseCurr", "新台幣/美元");
			map.put("baseRate", Util.parseBigDecimal(baseRateStr).toString()
                    + "/" + Util.parseBigDecimal(baseRateStrUSD).toString());
			map.put("dMonth1", NumConverter.numberToChinese("6"));
			map.put("dMonth2", NumConverter.numberToChinese("6"));
			map.put("dRate1", NumConverter.numberToChinese("10"));
			map.put("dRate2", NumConverter.numberToChinese("20"));
			map.put("dRateAdd1", NumConverter.numberToChinese("1"));
			map.put("dRateAdd2", NumConverter.numberToChinese("1.5"));

			// 其他未盡事項
			StringBuffer sbOtherTerms = new StringBuffer();
			int seqOT = 0;
			if (map.get("OtherTerms") != null) {
				sbOtherTerms.append(map.get("OtherTerms"));
			}
			if (map.get("seqOT") != null) {
				seqOT = Integer.parseInt(map.get("seqOT"));
			}
			if (sbOtherTerms.length() > 0) {
				if (seqOT > 1) {
					sbOtherTerms.insert(0, "三、其他商議條款："
							+ 換行符號
							+ (sbOtherTerms.toString().startsWith("1. ") ? ""
									: "1. "));
				} else {
					sbOtherTerms.insert(0, "三、其他商議條款：");
				}
			}
			map.put("OtherTerms", sbOtherTerms.toString());
			if (map.get("loanAmtG") != null) {
				map.remove("loanAmtG");
			}
			if (map.get("interestG") != null) {
				map.remove("interestG");
			}
			if (map.get("seqOT") != null) {
				map.remove("seqOT");
			}
			
			// J-110-0372 共用樣版 - 特別條款
			map.put("SpecialTerms", this.getCommonCont(l140m01a.getMainId(), 6, false));
			/*
			map.put("SpecialTerms", NumConverter.numberToChinese(6) + "、" 
					+ this.getCommonCont(l140m01a.getMainId(), 6, false));
			*/

			// baos = this.writeWordContent(content, map);
			map = this.replaceMapContStr(map); // 取代特定文字
			// 可能有換行的，不要呼叫convert_paramValue_for_XML_Predefined_entities
			// 會把< > 變成大寫的＜＞導致換行失效
			String traceStr = "【l140m01a.mainId=" + mainId + "】";
			String outputStr = join_word_template_param(traceStr, content, map);
			baos = this.writeWordContent(outputStr);

		} catch (FileNotFoundException e) {
			LOGGER.error(e.getMessage());
			throw new CapMessageException(e.getMessage(), getClass());
		} catch (IOException e) {
			LOGGER.error(e.getMessage());
			throw new CapMessageException(e.getMessage(), getClass());
		} catch (Exception e) {
			LOGGER.error(e.getMessage());
			throw new CapMessageException(e.getMessage(), getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	public HashMap<String, String[]> getHasItem(L140M01A l140m01a) {
		HashMap<String, String[]> result = new HashMap<String, String[]>();
		HashMap<String, String[]> type = this.initTypeMap(); // 對應樣版

		HashMap<String, String[]> bizCatMap = new HashMap<String, String[]>(); // 對應樣版的科目
		bizCatMap.put("A", new String[] {}); // 購料借款
		bizCatMap.put("B", new String[] {}); // 外銷借款
		bizCatMap.put("C", new String[] {}); // 營運週轉借款
		bizCatMap.put("D", new String[] {});// 貼現
		bizCatMap.put("E", new String[] {}); // 透支
		bizCatMap.put("F", new String[] {}); // 委任票據保證
		bizCatMap.put("G", new String[] { "701", "702", "801", "802" }); // 委任票據承兌
		bizCatMap.put("H", new String[] {}); // 委任保證

		// 取得樣版與科目對應
		String bizCat_LoanTP = Util.trim(lmsService
				.getSysParamDataValue("LMS_BIZCAT_LOANTP"));
		if (Util.notEquals(bizCat_LoanTP, "")) {
			JSONObject jsonBizCat_LoanTP = JSONObject.fromObject("{"
					+ bizCat_LoanTP + "}");
			if (jsonBizCat_LoanTP != null) {
				for (String typeKey : type.keySet()) {
					HashSet<String> loanTPs = new HashSet<String>();
					String[] bizCatArr = type.get(typeKey);
					for (String bc : bizCatArr) {
						String loanTPList = Util
								.trim(jsonBizCat_LoanTP.get(bc));
						if (Util.isNotEmpty(loanTPList)) {
							String[] loanTPArr = StringUtils.split(loanTPList,
									"|");
							for (String loanTP : loanTPArr) {
								loanTPs.add(loanTP);
							}
						}
					}
					bizCatMap.put(typeKey,
							loanTPs.toArray(new String[loanTPs.size()]));
				}
			}
		}

		List<L140M01C> l140m01cs = l140m01cDao.findByMainId(l140m01a
				.getMainId());
		for (String itemType : bizCatMap.keySet()) {
			result.put(itemType, new String[] { "N", "" });
			String[] codeArr = bizCatMap.get(itemType);
			for (L140M01C l140m01c : l140m01cs) {// l140m01a.getL140m01c()) {
				String key = Util.getLeftStr(l140m01c.getLoanTP(), 3);
				if (Arrays.asList(codeArr).contains(key)) {
					result.put(itemType,
							new String[] { "Y", l140m01c.getLoanTP() });
					break;
				}
			}
		}

		return result;
	}

	public HashMap<String, String[]> initTypeMap() {
		HashMap<String, String[]> type = new HashMap<String, String[]>(); // 對應樣版
		type.put("A", new String[] { "B" }); // 購料借款
		type.put("B", new String[] { "C" }); // 外銷借款
		type.put("C", new String[] { "A" }); // 營運週轉借款
		type.put("D", new String[] { "O" }); // 貼現
		type.put("E", new String[] { "G" }); // 透支
		type.put("F", new String[] { "F" }); // 委任票據保證
		// type.put(CtrConstants.L999S01BItemType.委任票據承兌, new String[] { "" });
		type.put("H", new String[] { "D", "E" }); // 委任保證
		return type;
	}

	public Map<String, String> setItemData(Map<String, String> map,
			String item, String firstLoanTP, L140M01A l140m01a) {
		DecimalFormat df = new DecimalFormat(
				"###,###,###,###,###,###,###,###,###,##0.####");
		HashMap<String, String[]> type = this.initTypeMap(); // 對應樣版
		if (Util.equals(item, "A")) { // 購料借款
			List<L140S09A> l140s09as_B03 = lmsService.findL140s09a(
					l140m01a.getMainId(), "", "B", "03");
			for (L140S09A l140s09a : l140s09as_B03) {
				List<L140S09B> l140s09bs = lmsService.findL140s09bByMainId(Util
						.trim(l140s09a.getOid()));
				for (L140S09B l140s09b : l140s09bs) {
					String contNo = l140s09b.getContNo();
					if (Util.equals(contNo, "01")) {
						String str = Util.trim(l140s09b.getPeriodB03_01_1());
						if (str.endsWith("天")) {
							str = str.substring(0, str.length() - 1);
						}
						map.put("periodB05_01_1", str);
						map.put("periodB05_01_2", str);
						map.put("periodB05_01_3", str);
						map.put("periodB05_02_1", str);
					}
				}
			}
			// 檢查清償期限是否有樣版資料，若無則使用科目清償期限
			if (Util.isEmpty(map.get("periodB05_01_1"))) {
				L140M01C l140m01c = l140m01cDao.findByUniqueKey(
						l140m01a.getMainId(), firstLoanTP);
				if (Util.notEquals(l140m01c.getLmtOther(), "1")
						&& Util.isNotEmpty(Util.trim(l140m01c.getLmtDays()))) {
					String str = Util.trim(l140m01c.getLmtDays());
					map.put("periodB05_01_1", str);
					map.put("periodB05_01_2", str);
					map.put("periodB05_01_3", str);
					map.put("periodB05_02_1", str);
				}
			}

			List<L140S09A> l140s09as_B02 = lmsService.findL140s09a(
					l140m01a.getMainId(), "", "B", "02");
			for (L140S09A l140s09a : l140s09as_B02) {
				List<L140S09B> l140s09bs = lmsService.findL140s09bByMainId(Util
						.trim(l140s09a.getOid()));
				for (L140S09B l140s09b : l140s09bs) {
					String contNo = l140s09b.getContNo();
					if (Util.equals(contNo, "01")) {
						map.put("useWayB02_01_1", NumConverter
								.numberToChinese(df.format(Util
										.parseBigDecimal(l140s09b
												.getUseWayB02_01_1()))));
						map.put("useWayB02_01_2", NumConverter
								.toChineseNumber(Util.parseBigDecimal(l140s09b
										.getUseWayB02_01_2())));
					}
				}
			}
		} else if (Util.equals(item, "B") || Util.equals(item, "D")) { // B.外銷借款
																		// D.貼現
			L140M01C l140m01c = l140m01cDao.findByUniqueKey(
					l140m01a.getMainId(), firstLoanTP);
			if (Util.notEquals(l140m01c.getLmtOther(), "1")
					&& Util.isNotEmpty(Util.trim(l140m01c.getLmtDays()))) {
				map.put("lmtDays" + item, NumConverter.toChineseNumber(Util
						.trim(l140m01c.getLmtDays())));
			}

			if (Util.equals(item, "D")) { // 貼現
				List<L140S09A> l140s09as_O02 = lmsService.findL140s09a(
						l140m01a.getMainId(), "", "O", "02");
				for (L140S09A l140s09a : l140s09as_O02) {
					List<L140S09B> l140s09bs = lmsService
							.findL140s09bByMainId(Util.trim(l140s09a.getOid()));
					for (L140S09B l140s09b : l140s09bs) {
						String contNo = l140s09b.getContNo();
						if (Util.equals(contNo, "01")) {
							map.put("useWayO02_01_1", NumConverter
									.numberToChinese(df.format(Util
											.parseBigDecimal(l140s09b
													.getUseWayO02_01_1()))));
						}
					}
				}
			}
		} else if (Util.equals(item, "C")) { // 營運週轉借款
			List<L140S09A> l140s09as_A04 = lmsService.findL140s09a(
					l140m01a.getMainId(), "", "A", "04");
			String[] arrA04 = this.getCont(l140s09as_A04, 0);
			map.put("lmtPeriod" + item, arrA04[0]);

			List<L140S09A> l140s09as_A02 = lmsService.findL140s09a(
					l140m01a.getMainId(), "", "A", "02");
			String[] arrA02 = this.getCont(l140s09as_A02, 0);
			map.put("useWay" + item, arrA02[0]);
		} else if (Util.equals(item, "E")) { // 透支
			List<L140S09A> l140s09as_G01 = lmsService.findL140s09a(
					l140m01a.getMainId(), "", "G", "01");
			for (L140S09A l140s09a : l140s09as_G01) {
				List<L140S09B> l140s09bs = lmsService.findL140s09bByMainId(Util
						.trim(l140s09a.getOid()));
				for (L140S09B l140s09b : l140s09bs) {
					String contNo = l140s09b.getContNo();
					if (Util.equals(contNo, "01")) {
						map.put("purposeG01_01_2",
								Util.trim(l140s09b.getPurposeG01_01_2()));
					}
				}
			}

			List<L140S09A> l140s09as_G04 = lmsService.findL140s09a(
					l140m01a.getMainId(), "", "G", "04");
			String[] arr = this.getCont(l140s09as_G04, 0);
			map.put("lmtPeriod" + item, arr[0]);
		} else if (Util.equals(item, "H")) { // 委任保證
			// 兩個以上樣板
			String[] bizCatArr = type.get(item);
			StringBuffer sbFee = new StringBuffer();
			StringBuffer sbRange = new StringBuffer();
			StringBuffer sbWay = new StringBuffer();
			int feeCount = 0;
			int rangeCount = 0;
			int wayCount = 0;
			for (String bizCat : bizCatArr) {
				Map<String, String> feeMap = new HashMap<String, String>();
				feeMap.put("D", "06");
				feeMap.put("E", "04");
				List<L140S09A> l140s09asFee = lmsService.findL140s09a(
						l140m01a.getMainId(), "", bizCat, feeMap.get(bizCat));
				sbFee.append((sbFee.length() > 0 ? 換行符號 : ""));
				String[] feeArr = this.getCont(l140s09asFee, feeCount);
				sbFee.append(feeArr[0]);
				feeCount = feeCount + Integer.parseInt(feeArr[1]);

				Map<String, String> rangeMap = new HashMap<String, String>();
				rangeMap.put("D", "07");
				rangeMap.put("E", "05");
				List<L140S09A> l140s09asRange = lmsService.findL140s09a(
						l140m01a.getMainId(), "", bizCat, rangeMap.get(bizCat));
				sbRange.append((sbRange.length() > 0 ? 換行符號 : ""));
				String[] rangeArr = this.getCont(l140s09asRange, rangeCount);
				sbRange.append(rangeArr[0]);
				rangeCount = rangeCount + Integer.parseInt(rangeArr[1]);

				Map<String, String> wayMap = new HashMap<String, String>();
				wayMap.put("D", "08");
				wayMap.put("E", "06");
				List<L140S09A> l140s09asWay = lmsService.findL140s09a(
						l140m01a.getMainId(), "", bizCat, wayMap.get(bizCat));
				sbWay.append((sbWay.length() > 0 ? 換行符號 : ""));
				String[] wayArr = this.getCont(l140s09asWay, wayCount);
				sbWay.append(wayArr[0]);
				wayCount = wayCount + Integer.parseInt(wayArr[1]);
			}
			if (feeCount > 1) {
				if (!sbFee.toString().startsWith("1. ")) {
					sbFee.insert(0, "1. ");
				}
			}
			if (rangeCount > 1) {
				if (!sbRange.toString().startsWith("1. ")) {
					sbRange.insert(0, "1. ");
				}
			}
			if (wayCount > 1) {
				if (!sbWay.toString().startsWith("1. ")) {
					sbWay.insert(0, "1. ");
				}
			}
			map.put("fee" + item, sbFee.toString());
			map.put("range" + item, sbRange.toString());
			map.put("way" + item, sbWay.toString());
		}

		// 其他未盡事項
		if (Util.notEquals(item, "G")) { // 排除 委任票據承兌
			Map<String, String> initOthMap = new HashMap<String, String>(); // 樣版的未盡事項bizItem
			initOthMap.put("A", "03");
			initOthMap.put("B", "04");
			initOthMap.put("C", "02");
			initOthMap.put("D", "05");
			initOthMap.put("E", "03");
			initOthMap.put("F", "02");
			initOthMap.put("G", "03");
			initOthMap.put("H", "07");
			initOthMap.put("I", "07");
			initOthMap.put("J", "06");
			initOthMap.put("N", "07");
			initOthMap.put("O", "03");
			StringBuffer sbOtherTerms = new StringBuffer();
			int seqOT = 0;
			if (map.get("OtherTerms") != null) {
				sbOtherTerms.append(map.get("OtherTerms"));
			}
			if (map.get("seqOT") != null) {
				seqOT = Integer.parseInt(map.get("seqOT"));
			}
			String[] bizCatArr = type.get(item); // 取得該類別對應之樣版
			for (String bizCat : bizCatArr) {
				List<L140S09A> l140s09as = lmsService.findL140s09a(
						l140m01a.getMainId(), "", bizCat,
						initOthMap.get(bizCat));
				String[] contArr = this.getCont(l140s09as, seqOT);
				sbOtherTerms.append(((sbOtherTerms.length() > 0 && contArr[0]
						.length() > 0) ? 換行符號 : ""));
				sbOtherTerms.append(contArr[0]);
				seqOT = seqOT + Integer.parseInt(contArr[1]);
			}
			map.put("OtherTerms", sbOtherTerms.toString());
			map.put("seqOT", Integer.toString(seqOT));
		}

		return map;
	}

	public String[] getCont(List<L140S09A> l140s09as, int startSeq) {
		String[] strArr = new String[2];
		StringBuffer sb = new StringBuffer();
		int seq = 0;
		int s09aNum = l140s09as.size();
		boolean firstRount = true;
		for (L140S09A l140s09a : l140s09as) {
			List<L140S09B> l140s09bs = lmsService.findL140s09bByMainId(Util
					.trim(l140s09a.getOid()));
			int s09bNum = l140s09bs.size();
			if (firstRount && startSeq > 0 && s09aNum > 0 && s09bNum > 0) {
				seq = seq + startSeq;
				firstRount = false;
			}
			for (L140S09B l140s09b : l140s09bs) {
				String str = l140s09b.getCont();
				sb.append((sb.length() > 0 ? 換行符號 : ""));
				seq++;
				sb.append(
						(s09aNum > 1 || s09bNum > 1 || seq > 1) ? (seq + ". ")
								: "").append(str);
			}
		}

		strArr[0] = sb.toString();
		strArr[1] = Integer.toString(seq);
		return strArr;
	}

	public String[] getInterest(L140M01A l140m01a, L140M01F l140m01f,
			String item, HashMap<String, String> moneyMap,
			Map<String, String> rateGetIntMap, Map<String, String> currMap) {
		String[] strArr = new String[6];

		List<L140M01N> l140m01ns = l140m01nDao
				.findByMainIdAndRateSeqOrderByWithGrid(l140m01a.getMainId(),
						l140m01f.getRateSeq());
		StringBuffer sbUSD = new StringBuffer();
		int seqUSD = 0;
		StringBuffer sbTWD = new StringBuffer();
		int seqTWD = 0;
		StringBuffer sbOTH = new StringBuffer();
		int seqOTH = 0;
		boolean isA = (Util.equals(item, "A") ? true : false); // 購料借款
		String[] rgiArr = new String[] { "", "", "" };// rateGetInt TWD > USD >
														// OTH
		Set<String> typeSet = new HashSet<String>();
		for (L140M01N l140m01n : l140m01ns) {
			String rateType = Util.trim(l140m01n.getRateType());
			String rateCurr = MapUtils.getString(moneyMap, rateType, "OTH");
			typeSet.add(rateCurr);
		}
		int typeCount = typeSet.size();
		boolean nextLevel = (typeCount > 1 ? true : false);
		int seqCurr = 0;
		for (L140M01N l140m01n : l140m01ns) {
			String rateType = Util.trim(l140m01n.getRateType());
			String rateCurr = MapUtils.getString(moneyMap, rateType, "OTH");
			String rateDscr = this.setL140M01NStr(l140m01n);//Util.trim(l140m01n.getRateDscr());
			String rateGetInt = Util.trim(l140m01n.getRateGetInt());
			if (Util.equals(rateCurr, "USD")) {
				if (seqUSD == 0) {
					seqCurr++;
				}
				seqUSD++;
				sbUSD.append((sbUSD.length() > 0 ? 換行符號 : ""));
				sbUSD.append(
						(seqUSD > 1 ? ((isA || nextLevel) ? ("(" + seqUSD + ")")
								: (seqUSD + ". "))
								: "")).append(rateDscr);
				rgiArr[1] = rgiArr[1] + (seqOTH > 1 ? "、" : "")
						+ rateGetIntMap.get(rateGetInt);
			} else if (Util.equals(rateCurr, "TWD")) {
				if (seqTWD == 0) {
					seqCurr++;
				}
				seqTWD++;
				sbTWD.append((sbTWD.length() > 0 ? 換行符號 : ""));
				sbTWD.append(
						(seqTWD > 1 ? ((isA || nextLevel) ? ("(" + seqTWD + ")")
								: (seqTWD + ". "))
								: "")).append(rateDscr);
				rgiArr[0] = rgiArr[0] + (seqTWD > 1 ? "、" : "")
						+ rateGetIntMap.get(rateGetInt);
			} else {
				seqOTH++;
				sbOTH.append((sbOTH.length() > 0 ? 換行符號 : ""));
				if (isA) {
					sbOTH.append(
							(seqOTH > 1 ? ((isA || nextLevel) ? ("(" + seqOTH + ")")
									: (seqOTH + ". "))
									: ""))
							.append((Util.equals(rateCurr, "OTH") ? "雜幣"
									: currMap.get(rateCurr)) + "：")
							.append(rateDscr);
				} else {
					seqCurr++;
					sbOTH.append(
							Util.toFullCharString(Integer.toString(seqCurr))
									+ "、"
									+ ((Util.equals(rateCurr, "OTH") ? "雜幣"
											: currMap.get(rateCurr)) + "："))
							.append(rateDscr);
				}
				rgiArr[2] = rgiArr[2] + (seqOTH > 1 ? "、" : "")
						+ rateGetIntMap.get(rateGetInt);
			}
		}
		if (seqUSD > 1) {
			sbUSD.insert(0, ((isA || nextLevel) ? ((isA ? "" : 換行符號) + "(1)")
					: "1. "));
		}
		if (seqTWD > 1) {
			sbTWD.insert(0, ((isA || nextLevel) ? ((isA ? "" : 換行符號) + "(1)")
					: "1. "));
		}
		if (isA && seqOTH > 1) {
			sbOTH.insert(0, ((isA || nextLevel) ? ((isA ? "" : 換行符號) + "(1)")
					: "1. "));
		}

		strArr[0] = sbUSD.toString();
		strArr[1] = sbTWD.toString();
		strArr[2] = sbOTH.toString();
		strArr[3] = rgiArr[0];
		strArr[4] = rgiArr[1];
		strArr[5] = rgiArr[2];

		return strArr;
	}

	public Map<String, String> replaceMapContStr(Map<String, String> map) {
		for (String key : map.keySet()) {
			String contStr = map.get(key);
			contStr = contStr.replaceAll("借戶", "乙方");
			contStr = contStr.replaceAll("借款人", "乙方");
			contStr = contStr.replaceAll("本行", "甲方");
			map.put(key, contStr);
		}
		return map;
	}

	// 組成 利率 文字 參考 LMS1401ServiceImpl.java setL140M01NStr
	public String setL140M01NStr(L140M01N l140m01n) {
		String rateDscr = "";

		String[] currs = new String[] { "TWD", "USD", "JPY", "EUR", "AUD",
				"HKD", "CNY", "Z" };
		HashMap<String, LinkedHashMap<String, String>> rateMapALL = misMislnratService
				.findBaseRateByCurrs(currs);
		LinkedHashMap<String, String> rateMap = null;
		HashMap<String, String> rateMap2 = lnlnf070Service.getRateBy070();
		String rateType = l140m01n.getRateType();

		// 用來抓070的規則 臺幣為00開頭 其他幣別皆為 99
		String tempBase = "1".equals(rateType) ? "00" : "99";

		if ("Z".equals(rateType)) {
			rateMap = new LinkedHashMap<String, String>(
					codeTypeService.findByCodeType("L140M01N_OtherCurr"));
		} else {
			rateMap = rateMapALL.get(RateTypeEnum.coverToCurr(rateType));
		}
		LinkedHashMap<String, String> PrRatMap = lnlnf070Service
				.getPrRate(RateTypeEnum.coverToCurr(rateType));
		Map<String, CapAjaxFormResult> totalCodeType = codeTypeService
				.findByCodeType(new String[] { "lms1405s0204_count",
						"lms1401s0204_ratePeriod", "lms1405s0204_rateKind",
						"lms1401s0204_rateGetInt",
						"lms1401s0204_rateLimitType",
						"lms1401s0204_rateChgKind",
						"lms1401s0204_primeRatePlan" });

		CapAjaxFormResult countMap = totalCodeType.get("lms1405s0204_count");

		CapAjaxFormResult ratePeriodMap = totalCodeType
				.get("lms1401s0204_ratePeriod");
		CapAjaxFormResult rateKindMap = totalCodeType
				.get("lms1405s0204_rateKind");
		CapAjaxFormResult rateGetIntMap = totalCodeType
				.get("lms1401s0204_rateGetInt");
		CapAjaxFormResult rateLimitTypeMap = totalCodeType
				.get("lms1401s0204_rateLimitType");
		CapAjaxFormResult rateChgKindMap = totalCodeType
				.get("lms1401s0204_rateChgKind");

		String otherMemo = "";
		String recvRate = "";
		String secNoStr = this.getSecNoStr(l140m01n);

		String rateBase = Util.trim(l140m01n.getRateBase());
		StringBuffer intBase = new StringBuffer(0);
		String intBaseU012 = "";
		BigDecimal disYearRate = l140m01n.getDisYearRate();
		if ("01".equals(rateBase)) {
			String 自訂利率基礎key = Util.trim(l140m01n.getPrRate());
			String 自訂利率基礎文字 = Util.trim(PrRatMap.get(自訂利率基礎key));
			if ("U01".equals(自訂利率基礎key) || "U02".equals(自訂利率基礎key)) {
				intBaseU012 = this.forU01U02Str(countMap, l140m01n, intBase);
			} else {
				String 自訂天期key = Util.trim(l140m01n.getRatePeriod());
				String 自訂天期文字 = Util.trim(ratePeriodMap.get(自訂天期key));

				intBase.append("按");
				if ("B000".equals(自訂天期key) || "B001".equals(自訂天期key)) {
					// 無 | B000<br/>
					// 借款同天期|B001<br/>
					if ("B001".equals(自訂天期key)) {
						intBase.append(自訂天期文字);
					}
					// 無自訂利率天期
					intBase.append(自訂利率基礎文字);
				} else {
					// String peroidStr = "";
					String tmpRatePXStr = "";
					String[] spliteRatePeriod = 自訂天期key
							.split(UtilConstants.Mark.SPILT_MARK);
					StringBuffer peroidStrTemp = new StringBuffer(0);
					String mark = "";
					int countRatePeriod = spliteRatePeriod.length;
					for (String px : spliteRatePeriod) {
						--countRatePeriod;
						if (countRatePeriod == 0) {
							mark = "或";
						} else {
							mark = "、";
						}
						if (自訂利率基礎文字.indexOf("６１６５") > 0
								|| 自訂利率基礎文字.indexOf("５１３２８") > 0) {
							// l1401s02p04.msg006=天期
							if ("M".equals(Util.getLeftStr(px, 1))) {
								tmpRatePXStr = Util
										.toFullCharString(Integer.valueOf(Util
												.getRightStr(px, 2))
												* 30
												+ "天期");
								if ("M3".equals(Util.getLeftStr(px, 2))) {
									// l1401s02p04.msg007=平均
									tmpRatePXStr += "平均";
								}

								peroidStrTemp
										.append(peroidStrTemp.length() > 0 ? mark
												: "");
								peroidStrTemp.append(tmpRatePXStr);

							} else {
								peroidStrTemp
										.append(peroidStrTemp.length() > 0 ? mark
												: "");
								peroidStrTemp.append(Util.trim(ratePeriodMap
										.get(px)));
							}
						} else {
							peroidStrTemp
									.append(peroidStrTemp.length() > 0 ? mark
											: "");
							peroidStrTemp.append(Util.trim(ratePeriodMap
									.get(px)));
						}
					}

					intBase.append(自訂利率基礎文字);
					intBase.append(peroidStrTemp);
				}

				if ("C01".equals(自訂利率基礎key) || "U09".equals(自訂利率基礎key)) {
					// 固定利率時，直接顯示固定利率XX%
					intBase.setLength(0);
					// l1401s02p04.msg008=按固定利率
					intBase.append("按固定利率");
					BigDecimal attRate = l140m01n.getAttRate();
					intBase.append(NumConverter.addComma(attRate, ",##0.0####"));
					intBase.append("％");
				} else {
					if (Util.isNotEmpty(l140m01n.getDisYearOp())
							&& Util.isNotEmpty(disYearRate)
							&& BigDecimal.ZERO.compareTo(disYearRate) != 0) {
						String DisYearOpStr = Util.trim(countMap.get(l140m01n
								.getDisYearOp()));
						intBase.append(DisYearOpStr);
						intBase.append(NumConverter.addComma(disYearRate, ",##0.0####"));
						intBase.append("％");
					}
				}
			}
		} else if ("@2".equals(rateBase)) {
			intBase.append(Util.trim(l140m01n.getOtherRateDrc()));
		} else {
			String[] rateBaseArray = rateBase
					.split(UtilConstants.Mark.SPILT_MARK);
			String groupName = Util.trim(l140m01n.getGroupName());

			if (rateBaseArray.length <= 1
					&& UtilConstants.DEFAULT.是.equals(l140m01n.getRateSetAll())) {
				if (Util.isNotEmpty(groupName)) {
					intBase.append("按借款同天期").append(groupName);
				}
			} else {
				String rateName = "";
				int conutrateBaseArray = rateBaseArray.length;
				// 先將所有值拿出來排序
				LinkedHashMap<String, String> tempMap = new LinkedHashMap<String, String>();
				String rateBaseStr = "";
				for (String key : rateBaseArray) {
					if (rateMap.containsKey(key)) {
						rateBaseStr = rateMap.get(key);
					} else if (rateMap2.containsKey(tempBase + key)) {
						rateBaseStr = rateMap2.get(tempBase + key);
					} else {
						rateBaseStr = key;
					}
					tempMap.put(key, rateBaseStr);
				}

				for (String key : tempMap.keySet()) {

					rateName = tempMap.get(key);
					--conutrateBaseArray;
					if (intBase.length() == 0) {
						intBase.append("按");

						if (Util.isEmpty(groupName)) {
							intBase.append(rateName);
						} else {
							intBase.append(groupName);
							intBase.append(rateName.replace(groupName, ""));
						}
					} else {
						if (conutrateBaseArray != 0) {
							intBase.append("、");
						} else {
							intBase.append("或");
						}
						if (Util.isEmpty(groupName)) {
							intBase.append(rateName);
						} else {
							intBase.append(rateName.replace(groupName, ""));
						}
					}
				}
			}
			if (Util.isNotEmpty(l140m01n.getDisYearOp())
					&& Util.isNotEmpty(disYearRate)
					&& BigDecimal.ZERO.compareTo(disYearRate) != 0) {
				String DisYearOpStr = Util.trim(countMap.get(l140m01n
						.getDisYearOp())) + NumConverter.addComma(disYearRate, ",##0.0####") + "％";
				intBase.append(DisYearOpStr);
			}
			// l1401s02p04.msg010=目前為
			if (UtilConstants.DEFAULT.是.equals(l140m01n.getReRateSelAll())) {
				String nowRate = "(目前為"
						+ NumConverter.addComma(l140m01n.getReRateMin(), ",##0.0####");
				BigDecimal reRateMax = l140m01n.getReRateMax();
				if (Util.isNotEmpty(reRateMax)) {
					nowRate = nowRate + "~" + NumConverter.addComma(reRateMax, ",##0.0####");
				}
				nowRate = nowRate + "％)";
				intBase.append(nowRate);
			}
		}

		String rateMemo = Util.trim(l140m01n.getRateMemo());

		if (Util.isNotEmpty(rateMemo)) {
			rateMemo = "，" + rateMemo;
		}
		StringBuffer intChg = new StringBuffer();
		String rateGetInt = Util.trim(l140m01n.getRateGetInt());
		RateKindEnum rateKind = RateKindEnum.getEnum(l140m01n.getRateKind());
		if (rateKind != null) {
			switch (rateKind) {
			case 固定利率:
				// N-105-0127-001 Web e-Loan配合衍生性金融商品風險係數修改 U09為固定利率比照C01
				if (!"5".equals(rateGetInt)
						&& !("C01".equals(l140m01n.getPrRate()) || "U09"
								.equals(l140m01n.getPrRate()))) {
					// 固定利率+本息併付 不顯示固定利率
					intChg.append("，").append(
							rateKindMap.get(l140m01n.getRateKind()));
				}
				break;
			case 機動利率:

				break;
			case 定期浮動:
				// * 月 | M<br/>
				// * 年 | Y<br/>
				// l1401s02p04.msg011=每
				// l1401s02p04.msg012=個
				// l1401s02p04.msg013=利率調整乙次

				String rateChgKind = Util.trim(l140m01n.getRateChgKind());
				intChg.append("，")
						.append("每")
						.append(NumConverter.toChineseNumber(l140m01n
								.getRateChg1()));
				String rateChgKindStr = Util.trim(rateChgKindMap
						.get(rateChgKind));
				if ("Y".equals(rateChgKind)) {
					intChg.append(rateChgKindStr).append("利率調整乙次");
				} else if ("M".equals(rateChgKind)) {
					intChg.append("個").append(rateChgKindStr).append("利率調整乙次");
				} else {
					intChg.setLength(0);
				}
			}
		}

		recvRate = "";
		if (Util.isNotEmpty(rateGetInt)) {
			if ("1".equals(rateGetInt)) {
				recvRate = "";
			} else {
				recvRate = "，" + rateGetIntMap.get(rateGetInt);
			}
		}
		otherMemo = Util.trim(l140m01n.getUionMemo());
		if (Util.isNotEmpty(otherMemo)) {
			otherMemo = "，" + otherMemo;
		}
		String taxStr = "";
		String rateTax = Util.trim(l140m01n.getRateTax());
		// l1401s02p04.msg014=除以
		if (Util.isEmpty(rateTax)) {
			taxStr = "";
		} else if ("1".equals(rateTax)) {

			if (Util.equals(Util.trim(l140m01n.getRateTaxCodeDecideFuture()),
					"Y")) {
			} else {
				taxStr = "除以" + NumConverter.addComma(l140m01n.getRateTaxCode(), ",##0.0####");
			}

		} else {
			taxStr = "";
		}

		// l1401s02p04.msg026=目前all-in利率約為
		StringBuffer allInStr = new StringBuffer("");
		if (UtilConstants.DEFAULT.是.equals(l140m01n.getAllInRateSelAll())) {
			allInStr.append("(").append("目前all-in利率約為")
					.append(NumConverter.addComma(l140m01n.getAllInRateMinAf(), ",##0.0####"));
			BigDecimal reRateMax = l140m01n.getAllInRateMaxAf();
			if (Util.isNotEmpty(reRateMax)) {
				allInStr.append("~").append(NumConverter.addComma(reRateMax, ",##0.0####"));
			}
			allInStr.append("％)");
		}

		StringBuffer limitStr = new StringBuffer(0);
		String rateLimitType = Util.trim(l140m01n.getRateLimitType());
		String rateLimitRate = NumConverter.addComma(l140m01n.getRateLimitRate(), ",##0.0####");
		String rateLimit = Util.trim(l140m01n.getRateLimit());
		if ("0".equals(rateLimitType)) {

		} else if ("1".equals(rateLimitType)) {
			// l1401s02p04.msg015=惟稅前不得低於
			limitStr.append("，").append("惟稅前不得低於")
					.append(rateLimitRate).append("％");
		} else if ("11".equals(rateLimitType)) {
			// l1401s02p04.msg016=惟稅後不得低於
			limitStr.append("，").append("惟稅後不得低於")
					.append(rateLimitRate).append("％");
		} else if ("12".equals(rateLimitType)) {
			// l1401s02p04.msg017=惟不得低於聯行息加
			limitStr.append("，").append("惟不得低於聯行息加")
					.append(rateLimitRate).append("％");
		} else if ("13".equals(rateLimitType)) {
			String rateLimitCode = Util.trim(l140m01n.getRateLimitCode());
			String rateLimitCodeStr = "";
			if (rateMap.containsKey(rateLimitCode)) {
				rateLimitCodeStr = Util.trim(rateMap.get(rateLimitCode));
			} else if (rateMap2.containsKey(tempBase + rateLimitCode)) {
				rateLimitCodeStr = Util.trim(rateMap2.get(tempBase
						+ rateLimitCode));
			} else {
				rateLimitCodeStr = rateLimitCode;
			}

			if (!"2".equals(rateLimit)) {
				// l1401s02p04.msg018=惟不得低於
				limitStr.append("，").append("惟不得低於");
			} else {
				// l1401s02p04.msg019=與
				limitStr.append("與");
			}

			String rateLimitCountPr = NumConverter.addComma(l140m01n.getRateLimitCountPr(), ",##0.0####");
			if ("01".equals(rateLimitCode)) {
				limitStr.append(rateLimitCountPr).append("％");
			} else {
				String lowRateGroupName = Util.trim(l140m01n
						.getRateLimitMarket());

				if (UtilConstants.DEFAULT.是.equals(l140m01n
						.getRateLimitSetAll())) {
					if (Util.isNotEmpty(lowRateGroupName)) {
						// l1401s02p04.msg021=同天期
						limitStr.append("同天期").append(lowRateGroupName);
					}
				} else {
					limitStr.append(rateLimitCodeStr);
				}

				BigDecimal rateLimitCountRate = l140m01n
						.getRateLimitCountRate();
				if (Util.isNotEmpty(l140m01n.getRateLimitCountType())
						&& Util.isNotEmpty(rateLimitCountRate)
						&& BigDecimal.ZERO.compareTo(rateLimitCountRate) != 0) {
					String RateLimitCount = countMap.get(l140m01n
							.getRateLimitCountType())
							+ ""
							+ NumConverter.addComma(rateLimitCountRate, ",##0.0####") + "％";
					limitStr.append(RateLimitCount);
				}
			}

			if ("1".equals(l140m01n.getRateLimitTax())) {
				String RateLimitStr = "除以"
						+ NumConverter.addComma(l140m01n.getRateLimitTaxRate(), ",##0.0####");
				limitStr.append(RateLimitStr);
			}

			if ("2".equals(rateLimit)) {
				// l1401s02p04.msg022=孰高計收
				limitStr.append("孰高計收");
			}

		} else {
			limitStr.append("，").append(rateLimitTypeMap.get(rateLimitType));
		}

		String taxOtherStr = "";
		if ("1".equals(rateTax)) {
			if (Util.equals(Util.trim(l140m01n.getRateTaxCodeDecideFuture()),
					"Y")) {
				// l1401s02p04.msg027=稅負由借款人負擔
				taxOtherStr = "，稅負由借款人負擔";
			}
		}

		// 第二組下限利率
		StringBuffer limitStr2 = new StringBuffer(0);
//		StringBuffer limitStrDB22 = new StringBuffer(0);
		String rateLimitType2 = Util.equals(
				Util.trim(l140m01n.getRateLimitType2()), "") ? "0" : Util
				.trim(l140m01n.getRateLimitType2());
		String rateLimitRate2 = NumConverter.addComma(l140m01n.getRateLimitRate2(), ",##0.0####");
		String rateLimit2 = Util.trim(l140m01n.getRateLimit2());

		if ("0".equals(rateLimitType2)) {

		} else if ("1".equals(rateLimitType2)) {
			// l1401s02p04.msg015=惟稅前不得低於
			limitStr2.append("，或")
					.append("惟稅前不得低於")
					.append(rateLimitRate2).append("％");
		} else if ("11".equals(rateLimitType2)) {
			// l1401s02p04.msg016=惟稅後不得低於
			limitStr2.append("，或")
					.append("惟稅後不得低於")
					.append(rateLimitRate2).append("％");
		} else if ("12".equals(rateLimitType2)) {
			// l1401s02p04.msg017=惟不得低於聯行息加
			limitStr2.append("，或")
					.append("惟不得低於聯行息加")
					.append(rateLimitRate2).append("％");
		} else if ("13".equals(rateLimitType2)) {
			String rateLimitCode2 = Util.trim(l140m01n.getRateLimitCode2());
			String rateLimitCodeStr2 = "";

			if (rateMap.containsKey(rateLimitCode2)) {
				rateLimitCodeStr2 = Util.trim(rateMap.get(rateLimitCode2));
			} else if (rateMap2.containsKey(tempBase + rateLimitCode2)) {
				rateLimitCodeStr2 = Util.trim(rateMap2.get(tempBase
						+ rateLimitCode2));
			} else {
				rateLimitCodeStr2 = rateLimitCode2;
			}

			if (!"2".equals(rateLimit2)) {
				// l1401s02p04.msg018_1=不得低於
				// l1401s02p04.msg028=亦
				limitStr2.append("，").append("亦").append("不得低於");
			} else {
				// l1401s02p04.msg019=與
				// l1401s02p04.msg029=且
				limitStr2.append("，").append("且").append("與");
			}

			String rateLimitCountPr2 = NumConverter.addComma(l140m01n.getRateLimitCountPr2(), ",##0.0####");
			if ("01".equals(rateLimitCode2)) {
				limitStr2.append(rateLimitCountPr2).append("％");
			} else {
				String lowRateGroupName2 = Util.trim(l140m01n
						.getRateLimitMarket2());

				if (UtilConstants.DEFAULT.是.equals(l140m01n
						.getRateLimitSetAll2())) {
					if (Util.isNotEmpty(lowRateGroupName2)) {
						// l1401s02p04.msg021=同天期;
						limitStr2.append("同天期").append(lowRateGroupName2);
					}
				} else {
					limitStr2.append(rateLimitCodeStr2);
				}

				BigDecimal rateLimitCountRate2 = l140m01n
						.getRateLimitCountRate2();
				if (Util.isNotEmpty(l140m01n.getRateLimitCountType2())
						&& Util.isNotEmpty(rateLimitCountRate2)
						&& BigDecimal.ZERO.compareTo(rateLimitCountRate2) != 0) {
					String RateLimitCount2 = countMap.get(l140m01n
							.getRateLimitCountType2())
							+ ""
							+ NumConverter.addComma(rateLimitCountRate2, ",##0.0####") + "％";
					limitStr2.append(RateLimitCount2);
				}
			}

			if ("1".equals(l140m01n.getRateLimitTax2())) {
				String RateLimitStr2 = "除以"
						+ NumConverter.addComma(l140m01n.getRateLimitTaxRate2(), ",##0.0####");
				limitStr2.append(RateLimitStr2);
			}

			if ("2".equals(rateLimit2)) {
				// l1401s02p04.msg022=孰高計收
				limitStr2.append("孰高計收");
			}
		} else {
			limitStr2.append("，").append(rateLimitTypeMap.get(rateLimitType2));
		}

		
		/* ============================================
		 * 不組 "優惠利率" 文字
		 * ============================================
		 * */
		
		StringBuffer RateTxt = new StringBuffer(0);
		RateTxt.append(secNoStr).append(intBase);
		if (!"@2".equals(rateBase)) {
			if ("2".equals(rateLimit)) {
				RateTxt.append(taxStr).append(limitStr).append(limitStr2)
						.append(intBaseU012).append(rateMemo).append(intChg)
						.append(recvRate).append(taxOtherStr).append(otherMemo)
						.append("。").append(allInStr);
			} else {
				RateTxt.append(taxStr).append(intBaseU012).append(rateMemo)
						.append(intChg).append(recvRate).append(limitStr)
						.append(taxOtherStr).append(limitStr2)
						.append(otherMemo).append("。")
						.append(allInStr);
			}
		}
		rateDscr = RateTxt.toString();
		
		return rateDscr;
	}

	/**
	 * 取得段數文字
	 */
	// 參考 LMS1401ServiceImpl.java getSecNoStr
	private String getSecNoStr(L140M01N l140m01n) {
		StringBuffer secNoStr = new StringBuffer();
		String secNo = Util.trim(l140m01n.getSecNo());
		SecNoOpEnum SecNoOp = L140M01NEnum.SecNoOpEnum.getEnum(Util
				.trim(l140m01n.getSecNoOp()));
		// l1401s02p04.msg001=第{0}段
		secNoStr.append(MessageFormat.format("第{0}段", secNo));
		switch (SecNoOp) {
		case 全案:
			secNoStr.setLength(0);
			break;
		case 自動用日起迄月:
			// l1401s02p04.msg002=(自動用日起第{0}-{1}月)：
			secNoStr.append(MessageFormat.format("(自動用日起第{0}-{1}月)：",
					l140m01n.getSecBegMon(), l140m01n.getSecEndMon()));
			break;
		case YYYYMMDD:
			secNoStr.append("(");
			secNoStr.append(CapDate.formatDate(l140m01n.getSecBegDate(),
					UtilConstants.DateFormat.YYYY_MM_DD));
			secNoStr.append("-");
			secNoStr.append(CapDate.formatDate(l140m01n.getSecEndDate(),
					UtilConstants.DateFormat.YYYY_MM_DD));
			secNoStr.append(")：");
			break;
		case 自簽約日起迄月:
			// l1401s02p04.msg003=(自簽約日起第{0}-{1}月)：
			secNoStr.append(MessageFormat.format("(自簽約日起第{0}-{1}月)：",
					l140m01n.getSecBegMon(), l140m01n.getSecEndMon()));
			break;
		case 自動用日起至迄日:
			// l1401s02p04.msg030=(自動用日起至{0})：
			secNoStr.append(MessageFormat.format("(自動用日起至{0})：", 
					CapDate.formatDate(l140m01n.getSecEndDate(),
					UtilConstants.DateFormat.YYYY_MM_DD)));
			break;
		case 自簽約日起至迄日:
			// l1401s02p04.msg031=(自簽約日起至{0})：
			secNoStr.append(MessageFormat.format("(自簽約日起至{0})：", 
					CapDate.formatDate(l140m01n.getSecEndDate(),
					UtilConstants.DateFormat.YYYY_MM_DD)));
			break;
		case YYYYMMDD至迄日:
			// l1401s02p04.msg032=(自{0}至迄日)：
			secNoStr.append(MessageFormat.format("(自{0}至迄日)：",
					CapDate.formatDate(l140m01n.getSecBegDate(),
					UtilConstants.DateFormat.YYYY_MM_DD)));
			break;
		}
		return secNoStr.toString();
	}
	
	/**
	 * U01 U02 自訂利率
	 */
	// 參考 LMS1401ServiceImpl.java forU01U02Str
	private String forU01U02Str(CapAjaxFormResult countMap, L140M01N l140m01n,
			StringBuffer intBase) {
		String prRate = Util.trim(l140m01n.getPrRate());
		String usdMarkStr = "";
		String intBaseU012 = "";
		String theFullName = "";
		if ("1".equals(Util.trim(l140m01n.getUsdMarket()))) {
			usdMarkStr = "SIBOR";
			theFullName = "ＳＩＢＯＲ";
		} else {
			usdMarkStr = "LIBOR";
			theFullName = "ＬＩＢＯＲ";
		}

		if ("U01".equals(prRate) || "U02".equals(prRate)) {
			if (UtilConstants.DEFAULT.是.equals(l140m01n.getUsdSetAll())) {
				intBase.append("按借款同天期").append(usdMarkStr);
			} else {
				String usdMarketRate = Util.trim(l140m01n.getUsdMarketRate());
				String[] usdMarketRateArray = usdMarketRate
						.split(UtilConstants.Mark.SPILT_MARK);
				LinkedHashMap<String, String> rowData = lnlnf070Service
						.getCode();
				int intCount = 0;
				for (String key : usdMarketRateArray) {
					++intCount;
					String keyName = rowData.get(key);
					if (intCount != 1) {
						keyName = keyName.replace(theFullName, "");
					}
					if (Util.isEmpty(intBase)) {
						intBase.append("按").append(keyName);
					} else {
						if (intCount < usdMarketRateArray.length) {
							intBase.append("、").append(keyName);
						} else {
							intBase.append("或").append(keyName);
						}
					}
				}
			}
		}

		BigDecimal usdDesRate = l140m01n.getUsdDesRate();
		if (Util.isNotEmpty(usdDesRate)
				&& BigDecimal.ZERO.compareTo(usdDesRate) != 0) {
			if ("U01".equals(prRate)) {
				// l1401s02p04.msg023=與TAIFX差額逾{0}％部分由借戶負擔
				intBaseU012 = "，"
						+ usdMarkStr
						+ MessageFormat.format("與TAIFX差額逾{0}％部分由借戶負擔",
								NumConverter.addComma(usdDesRate, ",##0.0####"));
			}
			if ("U02".equals(prRate)) {
				// l1401s02p04.msg024=惟市場利率大幅波動時，另以市場利率議定
				intBaseU012 = "，惟市場利率大幅波動時，另以市場利率議定";
			}
		}

		return intBaseU012;
	}
	
	/** 組成共通樣版文字
	 * 
	 * @param mainId	L140M01A.mainId
	 * @param startSeq	起始序號
	 * @param needCh	是否需要"第xx條"
	 * @return
	 */
	public String getCommonCont(String mainId, int startSeq, boolean needCh) {
		StringBuffer str = new StringBuffer();
		List<Map<String, Object>> list = eloandbBASEService.queryBizCatList(mainId);
		int seq = 0;
		for (Map<String, Object> map : list) {
			String bizCat = Util.nullToSpace(map.get("BIZCAT"));
			Integer bizCatId = MapUtils.getIntValue(map, "BIZCATID", 1);
			List<L140S09A> l140s09as = lmsService.findL140s09aByBizCat(mainId, bizCat, bizCatId);
			if (l140s09as != null && !l140s09as.isEmpty()) {
				if(lmsService.isCommonBizCat(Util.nullToSpace(l140s09as.get(0).getBizCat()))){
					seq++;
					str.append((str.length() > 0 ? 換行符號 : ""));
					str.append((needCh ? "第" : ""));
					str.append(NumConverter.numberToChinese(startSeq));
					str.append((needCh ? "條：" : "、"));
					String loanTPsName = Util.nullToSpace(l140s09as.get(0).getLoanTPsName());
					/*
					str.append(seq > 1 ? (needCh ? "　　　　　" : "　　") : "")
						.append(Integer.toString(seq) + "．").append(loanTPsName);
					*/
					str.append(loanTPsName);
					String contStr = Util.nullToSpace(this.getCommonContStr(l140s09as, needCh));
					if(Util.isNotEmpty(loanTPsName) && str.length() > 0 
							&& Util.isNotEmpty(CapString.trimFullSpace(contStr))){
						str.append(換行符號).append(contStr);
					}
					startSeq++;
				}
			}
		}
		return str.toString();
	}
	
	// 參考 lmsService  getL140s09Str()
	public String getCommonContStr(List<L140S09A> l140s09as, boolean needCh) {
		Properties pop = MessageBundleScriptCreator.getComponentResource(LMSCommomPage.class);
		StringBuffer sb = new StringBuffer();
		for (L140S09A s09a : l140s09as) {
			String bizCat = Util.nullToSpace(s09a.getBizCat());
			if (!CapString.isEmpty(bizCat) && Util.equals(s09a.getIsPrint(), "Y")) {
				sb.append((sb.length() > 0 ? 換行符號 : ""));
				String s09bMainId = s09a.getOid();
				String bizCatType = bizCat;
				if (bizCat.length() >= 1) {
					bizCatType = bizCat.substring(0, 1);
				}
				String bizItem = Util.nullToSpace(s09a.getBizItem());
				Map<String, String> bizItemMap = codeTypeService.findByCodeType("bizItem" + bizCatType);
				if (bizItemMap == null) {
					bizItemMap = new LinkedHashMap<String, String>();
				}
				
				String itemStr = (Util.equals(bizItem, "XX") ? Util.nullToSpace(s09a.getItemStr()) : bizItemMap.get(bizItem));
				sb.append(needCh ? "　　　　　" : "　　").append(itemStr);
				
				List<L140S09B> l140s09bs = lmsService.findL140s09bByMainId(s09bMainId);
				if (l140s09bs.size() > 0) {
					sb.append("：");
				}
				for (L140S09B s09b : l140s09bs) {
					try {
						sb.append((sb.length() > 0 ? 換行符號 : "")).append(needCh ? "　　　　　　" : "　　　").append((Util.equals(s09b.getContNo(), "XX") ? 
								Util.nullToSpace(s09b.getCont()).replace("\r\n", 換行符號).replace("\r", 換行符號).replace("\n", 換行符號) 
								: lmsService.getL140S09bCont(s09b, pop)));
					} catch (CapMessageException e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
				}
			}
		}
		return sb.toString();
	}

	private ByteArrayOutputStream writeWordContent(String content)
			throws CapMessageException {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		OutputStreamWriter outWriter = null;
		try {
			outWriter = new OutputStreamWriter(baos, "UTF-8");
			outWriter.write(content);
			outWriter.close();
			return baos;
		} catch (UnsupportedEncodingException e) {
			LOGGER.error(e.getMessage());
			throw new CapMessageException(e.getMessage(), getClass());
		} catch (IOException i) {
			LOGGER.error(i.getMessage());
			throw new CapMessageException(i.getMessage(), getClass());
		}
	}

	private String join_word_template_param(String traceStr, String raw_srcStr,
			Map<String, String> passed_paramMap) {
		String srcStr = raw_srcStr;

		Pattern pattern_run = Pattern
				.compile("(?s)<w:r\\b[^>]*>(?:(?!<w:r\b).)*?</w:r\\b[^>]*>");

		Pattern pattern_runTextTagAndContent = Pattern
				.compile("(?s)<w:t\\b[^>]*>(?:(?!<w:t\b).)*?</w:t\\b[^>]*>");
		Pattern pattern_tag_rPr = Pattern
				.compile("(?s)<w:rPr\\b[^>]*>(?:(?!<w:rPr\b).)*?</w:rPr\\b[^>]*>");
		Pattern pattern_tag_w_t = Pattern.compile("<w:t\\b[^>]*>");
		Pattern pattern_tag_w_t_end = Pattern.compile("</w:t\\b[^>]*>");
		// ===========
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Map<String, String> paramMap = new LinkedHashMap<String, String>();
		for (String k : passed_paramMap.keySet()) {
			String injectParamVal = Util.trim(passed_paramMap.get(k));
			if (Util.isEmpty(injectParamVal)) {
				continue;
			}
			paramMap.put(k, injectParamVal);
		}

		if (Util.equals(user.getSsoUnitNo(), "900")) { // 為了 debug
			LOGGER.info("join_word_template_param>>>" + paramMap.toString());
		}

		for (String k : paramMap.keySet()) {
			String injectParamVal = Util.trim(paramMap.get(k));

			String[] arr_bookMark = split_ByBookMark(k, srcStr);
			if (arr_bookMark.length == 3) {
				String befStr_bookmark = arr_bookMark[0];
				String bookmarkTagAndContentStr = arr_bookMark[1];
				String aftStr_bookmark = arr_bookMark[2];
				// =============
				String[] arr_bookmarkTagAndContent = split_ByRunText(
						bookmarkTagAndContentStr, pattern_run);
				if (arr_bookmarkTagAndContent.length == 3) {
					String bookMarkTagBeg = arr_bookmarkTagAndContent[0];
					String runPrAndContentStr = arr_bookmarkTagAndContent[1];
					String bookMarkTagEnd = arr_bookmarkTagAndContent[2];
					// ~~~~~~
					String[] arr_runPrAndContent = ContractDocUtil
							.split_into_pre_match_aft_byFirstFind(
									runPrAndContentStr,
									pattern_runTextTagAndContent);
					if (arr_runPrAndContent.length == 3) {
						String new_runPrAndContent = "";
						String debug_str = "";
						int idx_rPr = arr_runPrAndContent[0].indexOf("<w:rPr");
						int idx_u = arr_runPrAndContent[0].indexOf("<w:u");
						if (idx_rPr > 0 && idx_u > idx_rPr) {
							// 有底線, 例如：帳號_________________
							new_runPrAndContent = addColorAndSetTextWithUnderLine(
									k, arr_runPrAndContent, injectParamVal,
									pattern_tag_w_t, pattern_tag_w_t_end,
									pattern_tag_rPr);
							debug_str = "addColorAndSetTextWithUnderLine";
						} else {
							// 無底線，例如：甲方 ○○○
							new_runPrAndContent = addColorAndSetText(k,
									arr_runPrAndContent, injectParamVal,
									pattern_tag_w_t, pattern_tag_w_t_end,
									pattern_tag_rPr);
							debug_str = "addColorAndSetText";
						}
						String chg_flag = Util.equals(runPrAndContentStr,
								new_runPrAndContent) ? "Eq" : "Diff";
						if (Util.equals(chg_flag, "Eq")) {
							LOGGER.info(traceStr + "[" + k + "][new vs old]["
									+ debug_str + "]=[" + chg_flag + "]");
							LOGGER.info(traceStr + "\t" + runPrAndContentStr);
						} else if (Util.equals(chg_flag, "Diff")) {

						}

						srcStr = befStr_bookmark + bookMarkTagBeg
								+ (new_runPrAndContent) + bookMarkTagEnd
								+ aftStr_bookmark;

					} else {
						LOGGER.error(traceStr + "[" + k
								+ "]arr_runPrAndContent.length="
								+ arr_runPrAndContent.length);
						LOGGER.error(traceStr + runPrAndContentStr);
					}
				} else {
					LOGGER.error(traceStr + "[" + k
							+ "]arr_bookmarkTagAndContent.length="
							+ arr_bookmarkTagAndContent.length);
					LOGGER.error(traceStr + bookmarkTagAndContentStr);
				}
			} else {
				LOGGER.error(traceStr + "[" + k + "]arr_bookMark.length="
						+ arr_bookMark.length);
				LOGGER.error(traceStr + srcStr);
			}
		}
		return srcStr;
	}

	private String[] split_ByBookMark(String bookMarkName, String srcStr) {
		String[] arr = null;
		String strPattern = "(?s)<w:bookmarkStart\\b[^>]*w:name=\""
				+ bookMarkName + "\"[^>]*>.*<w:bookmarkEnd\\b[^>]*>";

		strPattern = "(?s)<w:bookmarkStart\\b[^>]*w:name=\""
				+ bookMarkName
				+ "\"[^>]*>(?:(?!<w:bookmarkStart\b).)*?<w:bookmarkEnd\\b[^>]*>";

		arr = ContractDocUtil.split_into_pre_match_aft_byFirstFind(srcStr,
				Pattern.compile(strPattern));

		if (arr != null && arr.length == 3) {
			// ok
		} else {
			LOGGER.error("split_ByBookMark[" + bookMarkName + "]【" + strPattern
					+ "】【" + srcStr + "】");
		}
		return arr;
	}

	private String[] split_ByRunText(String srcStr, Pattern pattern_run) {
		List<String> list = new ArrayList<String>();
		Matcher matcher = pattern_run.matcher(srcStr);
		int idx_prev_beg = -1;
		int idx_prev_end = -1;
		while (matcher.find()) { // 若一個 bookmark 包含了N個Run
			int idx_beg = matcher.start();
			int idx_end = matcher.end();
			String part = matcher.group();

			if (idx_prev_beg == -1) {
				// 應抓到 <w:bookmarkStart
				// w:id="0"
				// w:name="zxcv"/>
				list.add(srcStr.substring(0, idx_beg));
			}
			// ~~~
			idx_prev_beg = idx_beg;
			idx_prev_end = idx_end;
			// ~~~
			list.add(part);
		}

		if (idx_prev_end != -1) {
			// 應抓到 <w:bookmarkEnd
			// w:id="0"/>
			list.add(srcStr.substring(idx_prev_end));
		}
		// ============================
		if (list.size() >= 3) {
			String parseStr = StringUtils.join(list, "");
			if (parseStr.length() == srcStr.length()) {
				// ok
				return list.toArray(new String[list.size()]);
			} else {
				LOGGER.error("diff_length[" + parseStr.length() + " vs "
						+ srcStr.length() + "][" + parseStr + "][" + srcStr
						+ "]");
			}
		}
		return new String[] { srcStr };
	}

	private String addColorAndSetTextWithUnderLine(String bookmark_name,
			String[] arr_runPrAndContent, String injectParamVal,
			Pattern pattern_tag_w_t, Pattern pattern_tag_w_t_end,
			Pattern pattern_tag_rPr) {
		String runTagBeg_plus_rPr = arr_runPrAndContent[0]; // <w:r
															// w:rsidRPr="asdf"><w:rPr>...</w:rPr>
		String org_runTagAndContentStr = arr_runPrAndContent[1]; // <w:t> qwert
																	// </w:t>
		String runTagEnd = arr_runPrAndContent[2]; // </w:r>
		// ~~~
		String[] text_arr = ContractDocUtil.split_tag_and_content(
				org_runTagAndContentStr, pattern_tag_w_t, pattern_tag_w_t_end);
		int org_space_cnt = 0;
		if (text_arr.length == 3) {
			org_space_cnt = text_arr[1].length();
		}

		int cnt_empty_bef = 0;
		int cnt_empty_aft = 0;
		int injectStrValCnt = 0;
		String str_empty_bef = "";
		String str_empty_aft = "";
		if (StringUtils.isAsciiPrintable(injectParamVal)) {
			injectStrValCnt = injectParamVal.length();
		} else {
			int sz = injectParamVal.length();
			for (int i = 0; i < sz; i++) {
				if (CharUtils.isAsciiPrintable(injectParamVal.charAt(i))) {
					++injectStrValCnt;
				} else {
					injectStrValCnt = (injectStrValCnt + 2);
				}
			}
		}
		if (true) {
			if (org_space_cnt > injectStrValCnt) {
				int val = (org_space_cnt - injectStrValCnt) / 2;
				cnt_empty_bef = val;
				cnt_empty_aft = val;
			}

			if (cnt_empty_bef == 0) {
				cnt_empty_bef = 1;
			}
			if (cnt_empty_aft == 0) {
				cnt_empty_aft = 1;
			}

			str_empty_bef = StringUtils.repeat(" ", cnt_empty_bef);
			str_empty_aft = StringUtils.repeat(" ", cnt_empty_aft);
		}
		// ====================================
		// 填入資料分為3段
		// 第1段 黑色底線
		// 第2段 藍色底線，且為injectParamVal
		// 第3段 黑色底線
		String p1 = runTagBeg_plus_rPr
				+ (text_arr[0] + str_empty_bef + text_arr[2]) + runTagEnd;
		String p2 = addColorAndSetText(bookmark_name, arr_runPrAndContent,
				injectParamVal, pattern_tag_w_t, pattern_tag_w_t_end,
				pattern_tag_rPr);
		String p3 = runTagBeg_plus_rPr
				+ (text_arr[0] + str_empty_aft + text_arr[2]) + runTagEnd;

		return p1 + p2 + p3;
	}

	private String addColorAndSetText(String bookmark_name,
			String[] arr_runPrAndContent, String injectParamVal,
			Pattern pattern_tag_w_t, Pattern pattern_tag_w_t_end,
			Pattern pattern_tag_rPr) {
		String runTagBeg_plus_rPr = arr_runPrAndContent[0]; // <w:r
															// w:rsidRPr="asdf"><w:rPr>...</w:rPr>
		String org_runTagAndContentStr = arr_runPrAndContent[1]; // <w:t> qwert
																	// </w:t>
		String runTagEnd = arr_runPrAndContent[2]; // </w:r>
		// ~~~
		String[] text_arr = ContractDocUtil.split_tag_and_content(
				org_runTagAndContentStr, pattern_tag_w_t, pattern_tag_w_t_end);
		if (text_arr.length == 3) {
			// 為了能儘快找到填入的字串，增加 <!-- bm -->
			String new_runTagAndContentStr = text_arr[0] + injectParamVal
					+ text_arr[2] + "<!--{" + bookmark_name + "}-->";
			return _add_blueColor_to_rPr(runTagBeg_plus_rPr, pattern_tag_rPr)
					+ (new_runTagAndContentStr) + runTagEnd;
		}

		return StringUtils.join(arr_runPrAndContent, "");
	}

	private String _add_blueColor_to_rPr(String runTagBeg_plus_rPr,
			Pattern pattern_tag_rPr) {
		/*
		 * 第1段 <w:r w:rsidRPr="...">
		 */
		/*
		 * 第2段 <w:rPr><w:rFonts w:ascii="標楷體" w:eastAsia="標楷體" w:hAnsi="標楷體"
		 * w:hint="eastAsia"/><w:b/><w:color w:val="FF0000"/><w:sz
		 * w:val="28"/><w:szCs w:val="28"/><w:u w:val="single"/></w:rPr>
		 */
		/*
		 * 第3段
		 */
		String[] arr = ContractDocUtil.split_into_pre_match_aft_byFirstFind(
				runTagBeg_plus_rPr, pattern_tag_rPr);
		if (arr.length == 3) {
			String rPr = arr[1];
			if (rPr.indexOf("<w:color ") >= 0) {
				int idx = rPr.indexOf("<w:color ");
				String endTag = ">";
				int idx_end = rPr.indexOf(endTag, idx);
				if (idx_end > idx) {
					rPr = rPr.substring(0, idx)
							+ rPr.substring(idx_end + endTag.length());
				}
			}

			if (rPr.indexOf("<w:color ") < 0) { // no <w:color
				int idx = rPr.lastIndexOf("</w:rPr");
				if (idx > 0) {
					return arr[0]
							+ (rPr.substring(0, idx)
									+ " <w:color w:val=\"0000FF\"/>" + rPr
									.substring(idx)) + arr[2];
				}
			}
		}
		return runTagBeg_plus_rPr;
	}
}