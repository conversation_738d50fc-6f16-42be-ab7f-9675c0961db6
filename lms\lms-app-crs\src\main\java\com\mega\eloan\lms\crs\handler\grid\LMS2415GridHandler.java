/* 
 * LMS2415GridHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.crs.handler.grid;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;


import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.formatter.CodeTypeFormatter;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.crs.panels.LMS2415S04Panel;
import com.mega.eloan.lms.crs.service.LMS2415Service;
import com.mega.eloan.lms.model.C241M01A;
import com.mega.eloan.lms.model.C241M01B;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * [個金]覆審報告表 Grid
 * </pre>
 * 
 * @since 2011/10/20
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/10/20,jessica,new
 *          </ul>
 */
@Scope("request")
@Controller("lms2415gridhandler")
public class LMS2415GridHandler extends AbstractGridHandler {
	
	@Resource
	LMS2415Service lms2415service;

	@Resource
	BranchService branch;

	@Resource
	CodeTypeService codeTypeService;

	@Resource
	RetrialService retrialService;
	
	/**
	 * 查詢Grid 覆審報告表(個金)
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */

	@SuppressWarnings("unchecked")
	public CapGridResult queryC241m01a(ISearch pageSetting,
			PageParameters params) throws CapException {
		Properties prop = MessageBundleScriptCreator.getComponentResource(LMS2415S04Panel.class);
		
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String docStatus = Util.trim(params.getString("docStatus"));
		String filetData = Util.trim(params.getString("filetData"));
		if (Util.isNotEmpty(filetData)) {
			JSONObject jsoniletData = JSONObject.fromObject(filetData);
			String DateStart = Util.trim(jsoniletData
					.getString("checkDateStart"));
			String DateEnd = Util.trim(jsoniletData.getString("checkDateEnd"));

			if (Util.isNotEmpty(DateStart)) {
				Date checkDateStart = Util.parseDate(DateStart);
				pageSetting.addSearchModeParameters(SearchMode.GREATER_EQUALS,
						"retrialDate", checkDateStart);
			}

			if (Util.isNotEmpty(DateEnd)) {
				Date checkDateEnd = Util.parseDate(DateEnd + " 23:59:59");
				pageSetting.addSearchModeParameters(SearchMode.LESS_EQUALS,
						"retrialDate", checkDateEnd);
			}
			String custId = Util.trim(jsoniletData.getString("custId"));
			if (Util.isNotEmpty(custId)) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"custId", custId);
			}
			//J-108-0163_10702_B1001 配合授審處於企金及消金「授信覆審系統」增加「戶名」搜尋欄位
			String custName = Util.trim(jsoniletData.getString("custName"));
			if (Util.isNotEmpty(custName)) {
				pageSetting.addSearchModeParameters(SearchMode.LIKE,
						"custName", "%" + Util.trim(custName) + "%");
			}
			//J-112-0163_09763_B1001 配合授審處於企金及消金「授信覆審系統」增加「分行」搜尋欄位
			String brId = Util.trim(jsoniletData.getString("brIdFilter"));
			if (Util.isNotEmpty(brId)) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"ownBrId", brId);
			}
		}
		
		boolean overSeaProgram = retrialService.overSeaProgram();
		if(overSeaProgram){
			switch (RetrialDocStatusEnum.getEnum(docStatus)) {
			case 已覆核已核定:
				Object reason[] ={RetrialDocStatusEnum.編製中.toString(),RetrialDocStatusEnum.已覆核未核定.toString()};
				pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
						reason);
				pageSetting.addSearchModeParameters(SearchMode.IS_NOT_NULL,
						"upDate", "");
				break;
				case 待覆核:
					pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
							docStatus);
				break;
//			case 已覆核未核定:
//				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
//						docStatus);
//				pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
//						"upDate", "");
//				break;
			default:
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
						docStatus);
				pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
						"upDate", "");
				break;
			}
		}else{
			if(docStatus.indexOf("|")<0){
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus", docStatus);	
			}else{
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "retrialYN", "Y");

				List<String> list = new ArrayList<String>();
				for(String s: docStatus.split("\\|")){
					list.add(s);
				}
				list.add("");
				SearchModeParameter param1 = new SearchModeParameter(SearchMode.IN, "docStatus", list.toArray(new String[list.size()]));
				SearchModeParameter param2 = new SearchModeParameter(SearchMode.IS_NULL, "docStatus", "");
				pageSetting.addSearchModeParameters(SearchMode.OR, param1, param2);
			}			
		}		
		
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"c241a01a.authUnit", user.getUnitNo());
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				null);
		Page<? extends GenericBean> page = lms2415service.findPage(
				C241M01A.class, pageSetting);
		List<C241M01A> list = (List<C241M01A>) page.getContent();
		for(C241M01A c241m01a : list)	{
			String docStatusIn = Util.trim(c241m01a.getDocStatus());
			
			if(overSeaProgram){
				if(docStatusIn.equals(RetrialDocStatusEnum.已覆核未核定.toString()) && Util.isNotEmpty(c241m01a.getUpDate())){
					docStatusIn = RetrialDocStatusEnum.已覆核已核定.toString();
				}
			}else{
				//...
			}
			
			if(Util.equals("1", c241m01a.getConFlag())){
				c241m01a.setCondition(prop.getProperty("label.conFlag.1")+" "+Util.trim(c241m01a.getCondition()));
			}
			
			RetrialDocStatusEnum e = RetrialDocStatusEnum.getEnum(Util.trim(docStatusIn));
			if (e != null) {
				docStatusIn = e.name();
			}
			c241m01a.setDocStatus(Util.nullToSpace(docStatusIn));
			
			c241m01a.setOwnBrId(c241m01a.getOwnBrId()
					+ branch.getBranchName(c241m01a.getOwnBrId()));
		}
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	@SuppressWarnings("unchecked")
	public CapGridResult queryC241m01b(ISearch pageSetting,
			PageParameters params) throws CapException {

		// 建立主要Search 條件
		String mainId = Util.trim(params.getString("mainId"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		Page<? extends GenericBean> page = lms2415service.findPage(
				C241M01B.class, pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		List<C241M01B> list = (List<C241M01B>) page.getContent();
		for (C241M01B c241m01b : list) {
			if (c241m01b.getLnDataDate() != null) {
				c241m01b.setCreator("");
			} else {
				c241m01b.setCreator("Y");
			}
			// if (c241m01b.getQuotaAmt() != null) {
			// c241m01b.setQuotaAmt(c241m01b.getQuotaAmt().divide(
			// new BigDecimal(1000)));
			// }
			// if (c241m01b.getBalAmt() != null) {
			// c241m01b.setBalAmt(c241m01b.getBalAmt().divide(
			// new BigDecimal(1000)));
			//
			// }
		}
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("ynReview", new CodeTypeFormatter(codeTypeService,
				"Common_YesNo")); // codeType格式化
		dataReformatter.put("reVolve", new CodeTypeFormatter(codeTypeService,
				"Common_YesNo")); // codeType格式化
		dataReformatter.put("quotaType", new CodeTypeFormatter(codeTypeService,
				"c241m01b_quotaType")); // codeType格式化
		dataReformatter.put("subjectNo", new CodeTypeFormatter(codeTypeService,
				"lms1405m01_SubItem")); // codeType格式化
		result.setDataReformatter(dataReformatter);
		return result;
	}

	/**
	 * 查詢需列印的grid
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryPrint(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.setDistinct(true);
		Page<Map<String, Object>> page = lms2415service.getBorrows(mainId,
				pageSetting);
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}
	
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public CapGridResult queryDocFile(ISearch pageSetting,
			PageParameters params) throws CapException {
		ISearch search = createSearchTemplete();
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		pageSetting.addSearchModeParameters(search);
		Page page = lms2415service.findPage(DocFile.class, pageSetting);

		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

}
