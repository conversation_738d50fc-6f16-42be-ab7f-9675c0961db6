/* 
 * L210S01ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L210S01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;

import com.mega.eloan.lms.model.L210S01A;


/** 自行聯貸攤貸比率檔 **/
@Repository
public class L210S01ADaoImpl extends LMSJpaDao<L210S01A, String> implements
		L210S01ADao {

	@Override
	public L210S01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L210S01A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L210S01A> list = createQuery(L210S01A.class, search)
				.getResultList();
		return list;
	}

	@Override
	public L210S01A findByUniqueKey(String mainId, String flag,
			String shareBrId, String chgFlag) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "flag", flag);
		search.addSearchModeParameters(SearchMode.EQUALS, "shareBrId",
				shareBrId);
		search.addSearchModeParameters(SearchMode.EQUALS, "chgFlag", chgFlag);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L210S01A> findByIndex01(String mainId, String flag,
			String shareBrId, String chgFlag) {
		ISearch search = createSearchTemplete();
		List<L210S01A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (flag != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "flag", flag);
		if (shareBrId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "shareBrId",
					shareBrId);
		if (chgFlag != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "chgFlag",
					chgFlag);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(L210S01A.class, search).getResultList();
		}
		return list;
	}

	@Override
	public List<L210S01A> findByOids(String[] oids) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.IN, "oid", oids);
		List<L210S01A> list = createQuery(L210S01A.class, search)
				.getResultList();
		return list;
	}

	@Override
	public List<L210S01A> findByMainIdAndChgFlag(String mainId, String chgFlag) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "chgFlag", chgFlag);
		search.addOrderBy("createTime");
		return createQuery(L210S01A.class, search).getResultList();
	}

	@Override
	public L210S01A findByMainIdAndChgFlag(String mainId, String shareBrId,
			String chgFlag) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "shareBrId",
				shareBrId);
		search.addSearchModeParameters(SearchMode.EQUALS, "chgFlag", chgFlag);
		return findUniqueOrNone(search);
	}
	
	@Override
	public List<L210S01A> findByCntrNo(String CntrNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "shareNo", CntrNo);
		search.addOrderBy("shareNo");
		List<L210S01A> list = createQuery(L210S01A.class,search).getResultList();
		
		return list;
	}
	@Override
	public List<L210S01A> findByCustIdDupId(String custId,String DupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", DupNo);
		List<L210S01A> list = createQuery(L210S01A.class,search).getResultList();
		return list;
	}
}