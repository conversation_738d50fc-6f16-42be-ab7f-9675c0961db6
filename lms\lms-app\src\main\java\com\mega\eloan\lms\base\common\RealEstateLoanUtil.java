package com.mega.eloan.lms.base.common;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

import org.apache.commons.lang.StringUtils;

import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.Util;

import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.panels.LMSL140M01MPanel;
import com.mega.eloan.lms.model.L140M01M;

public class RealEstateLoanUtil {

	public final static String latestVersion = UtilConstants.L140m01mVersion.VERSION_20240919;
	public final static String[] newVersions = new String[]{UtilConstants.L140m01mVersion.VERSION_20201208, 
																UtilConstants.L140m01mVersion.VERSION_20210319,
																UtilConstants.L140m01mVersion.VERSION_20210924,
																UtilConstants.L140m01mVersion.VERSION_20211217,
																UtilConstants.L140m01mVersion.VERSION_20230616,
																UtilConstants.L140m01mVersion.VERSION_20240614,
																UtilConstants.L140m01mVersion.VERSION_20240919};
	
	public final static String[] specificVersion = new String[]{UtilConstants.L140m01mVersion.VERSION_20211217, 
																UtilConstants.L140m01mVersion.VERSION_20230616,
																UtilConstants.L140m01mVersion.VERSION_20240614,
																UtilConstants.L140m01mVersion.VERSION_20240919};

	private final BigDecimal 七千萬 = new BigDecimal(70000000);
	private final BigDecimal 六千萬 = new BigDecimal(60000000);
	private final BigDecimal 四千萬 = new BigDecimal(40000000);
	
	private final static String[] SPECIFIC_CityId = new String[]{"A", "F", "H", "B", "D", "E", "J", "O"};//臺北市、新北市、桃園市、臺中市、臺南市、高雄市、新竹縣、新竹市
	
	private final static String[] THREE_OR_MORE_HOUSE_CODE = new String[]{"B", "C", "D", "F"};
	
	public final static String[] NO_GRACE_PERIOD_REASON_CODE = new String[]{"J", "L"};
	
	public final static String[] MUST_APPLY_LATEST_VERSION_PROPERTY = new String[]{UtilConstants.Cntrdoc.Property.新做, UtilConstants.Cntrdoc.Property.續約, 
																				   UtilConstants.Cntrdoc.Property.增額};
	
	private String version;
	private String custId;
	private String cbcCase;
	private Map<String, BigDecimal> loanRatioMap;
	private Map<String, String> ratioWordsMap;
	
	Properties prop = MessageBundleScriptCreator.getComponentResource(LMSL140M01MPanel.class);
	
	public RealEstateLoanUtil(String version, String custId, String cbcCase){
		this.version = version;
		this.custId = custId;
		this.cbcCase = cbcCase;
		this.setLoanRatioByVersion();
	}
	
	public String checkDetailOptionByVersion(String reasonCode, String isHighHouse, String cityId){
		
		if(UtilConstants.L140m01mVersion.VERSION_20201208.equals(version) && CapString.isEmpty(reasonCode)){
			
			if (UtilConstants.L140M01MCbcCase.自然人.equals(cbcCase) || UtilConstants.L140M01MCbcCase.公司法人.equals(cbcCase)) {
				return prop.getProperty("L140M01M.err019.mortgage.loan");// 央行對辦理不動產抵押貸款業務規定限制：需填寫，請確認！
			}
		}
		
		if((UtilConstants.L140m01mVersion.VERSION_20210319.equals(version) 
				|| UtilConstants.L140m01mVersion.VERSION_20210924.equals(version)
				|| UtilConstants.L140m01mVersion.VERSION_20211217.equals(version) 
				|| UtilConstants.L140m01mVersion.VERSION_20230616.equals(version)
				|| UtilConstants.L140m01mVersion.VERSION_20240614.equals(version)
				|| UtilConstants.L140m01mVersion.VERSION_20240919.equals(version))
				&& CapString.isEmpty(reasonCode)){
			
			if (UtilConstants.L140M01MCbcCase.自然人.equals(cbcCase)) {
				return prop.getProperty("L140M01M.err019.mortgage.loan");// 央行對辦理不動產抵押貸款業務規定限制：需填寫，請確認！
			}
		}
		
		String[] highHouseOption = new String[]{"A", "C"};
		String[] notHighHouseOption = new String[]{"B", "D", "E"};
		
		if ("Y".equals(isHighHouse) && Arrays.asList(notHighHouseOption).contains(reasonCode)) {
			return prop.getProperty("L140M01M.err017.mortgage.loan"); // 央行對辦理不動產抵押貸款業務規定限制：屬高價住宅貸款，請確認！
		}
		
		if ("N".equals(isHighHouse) && Arrays.asList(highHouseOption).contains(reasonCode)) {
			return prop.getProperty("L140M01M.err018.mortgage.loan"); // 央行對辦理不動產抵押貸款業務規定限制：非屬高價住宅貸款，請確認！
		}
		
		if(UtilConstants.L140m01mVersion.VERSION_20210924.equals(version)){
			
			if("E".equals(reasonCode) && !Arrays.asList(SPECIFIC_CityId).contains(cityId)){
				return prop.getProperty("L140M01M.err020.mortgage.loan"); // 央行對辦理不動產抵押貸款業務規定限制：非屬第2戶特定地區房貸，請確認！
			}
		}else if(UtilConstants.L140m01mVersion.VERSION_20211217.equals(version) || UtilConstants.L140m01mVersion.VERSION_20230616.equals(version) || UtilConstants.L140m01mVersion.VERSION_20240614.equals(version)){
			
			if("G".equals(reasonCode) && !Arrays.asList(SPECIFIC_CityId).contains(cityId)){
				return prop.getProperty("L140M01M.err020.mortgage.loan"); // 央行對辦理不動產抵押貸款業務規定限制：非屬第2戶特定地區房貸，請確認！
			}
		}
		
		return "";
	}
	
	public String checkNaturalPersonOrLegalPersonBuyingOption(String plusReason){
		
		if (custId.length() == 10) {
			
			if ("5".equals(cbcCase)) {
				// L140M01M.err001=本案為個金戶其央行房貸註記不應選擇[屬於公司法人購屋貸款]選項，請再次確認。
				return prop.getProperty("L140M01M.err001"); 
			}
			
			if ("7".equals(plusReason)) {
				// L140M01M.err003=本案為個金戶其央行房貸註記不應選擇[建物所有權取得日期在99/12/31以前
				return prop.getProperty("L140M01M.err003"); 
			}
		} 
		
		if (custId.length() < 10) {
			
			if ("1".equals(cbcCase)) {
				// L140M01M.err002=本案為企金戶其央行房貸註記不應選擇[屬於自然人購屋貸款]選項, 請再次確認。
				return prop.getProperty("L140M01M.err002"); 
			}
			
			if ("1".equals(plusReason)) {
				// L140M01M.err004=本案為企金戶其央行房貸註記不應選擇[建物所有權取得日期在99/06/25以前
				// (自然人使用)]選項，請再次確認。
				return prop.getProperty("L140M01M.err004"); 
			}
			
			if ("A".equals(plusReason)) {
				// L140M01M.err017=本案為企金戶其央行房貸註記不應選擇[經查自然人聯合徵信中心歸戶查詢,有一戶資金用途為1之房貸,惟建物座落非屬央行管制之特定地區]選項，請再次確認。
				return prop.getProperty("L140M01M.err017"); 
			}
		}
		
		return "";
	}
	
	public String isHighHouse(String cityId, String buildYN, BigDecimal appAmt, BigDecimal nowAMT, BigDecimal timeVal){
		
		// 建物謄本登記用途是否屬「住」類，或「住商」類等無營業登記
		// 且建物用途【是】屬住、或住商且無營業登記。
		if (UtilConstants.DEFAULT.是.equals(buildYN)) {
			
			// 當聯徵名下【有】用途為1之其他房貸資料 (case2 不需判斷)
			// 判斷是否為高價住宅
			if ("A".equals(cityId)) {

				if (appAmt.compareTo(七千萬) >= 0 || timeVal.compareTo(七千萬) >= 0 ) {
					return "Y";
				}
			} 
			else if ("F".equals(cityId)) {

				if (appAmt.compareTo(六千萬) >= 0 || timeVal.compareTo(六千萬) >= 0) {
					return "Y";
				}
			} 
			else {
				
				if (appAmt.compareTo(四千萬) >= 0 || timeVal.compareTo(四千萬) >= 0) {
					return "Y";
				}
			}
		}
		
		return "N";
	}
	
	private void setLoanRatioByVersion(){
		
		Map<String, BigDecimal> loanRatioMap = new HashMap<String, BigDecimal>();
		Map<String, String> ratioWordsMap = new HashMap<String, String>();
		
		if(UtilConstants.L140m01mVersion.VERSION_20201208.equals(version)){
			loanRatioMap.put("A", new BigDecimal(60));
			loanRatioMap.put("B", new BigDecimal(60));
			loanRatioMap.put("C", new BigDecimal(60));
			loanRatioMap.put("D", new BigDecimal(60));
			loanRatioMap.put("2", new BigDecimal(65));
			loanRatioMap.put("6", new BigDecimal(50));
			
			ratioWordsMap.put("A", "六");
			ratioWordsMap.put("B", "六");
			ratioWordsMap.put("C", "六");
			ratioWordsMap.put("D", "六");
			ratioWordsMap.put("2", "六.五");//購地貸款
			ratioWordsMap.put("6", "五");//餘屋貸款
		}
		
		if(UtilConstants.L140m01mVersion.VERSION_20210319.equals(version)){
			loanRatioMap.put("A", new BigDecimal(55));
			loanRatioMap.put("B", new BigDecimal(55));
			loanRatioMap.put("C", new BigDecimal(40));
			loanRatioMap.put("D", new BigDecimal(50));
			loanRatioMap.put("5", new BigDecimal(40));
			loanRatioMap.put("2", new BigDecimal(65));
			loanRatioMap.put("6", new BigDecimal(50));
			loanRatioMap.put("7", new BigDecimal(55));
			
			ratioWordsMap.put("A", "五.五");
			ratioWordsMap.put("B", "五.五");
			ratioWordsMap.put("C", "四");
			ratioWordsMap.put("D", "五");
			ratioWordsMap.put("5", "四");
			ratioWordsMap.put("2", "六.五");//購地貸款
			ratioWordsMap.put("6", "五");//餘屋貸款
			ratioWordsMap.put("7", "五.五");//工業區閒置土地抵押貸款
		}
		
		if(UtilConstants.L140m01mVersion.VERSION_20210924.equals(version)){
			loanRatioMap.put("A", new BigDecimal(55));
			loanRatioMap.put("B", new BigDecimal(55));
			loanRatioMap.put("C", new BigDecimal(40));
			loanRatioMap.put("D", new BigDecimal(50));
			loanRatioMap.put("E", new BigDecimal(100));
			loanRatioMap.put("5", new BigDecimal(40));
			loanRatioMap.put("2", new BigDecimal(60));
			loanRatioMap.put("6", new BigDecimal(50));
			loanRatioMap.put("7", new BigDecimal(50));
			
			ratioWordsMap.put("A", "五.五");
			ratioWordsMap.put("B", "五.五");
			ratioWordsMap.put("C", "四");
			ratioWordsMap.put("D", "五");
			ratioWordsMap.put("5", "四");
			ratioWordsMap.put("2", "六");//購地貸款
			ratioWordsMap.put("6", "五");//餘屋貸款
			ratioWordsMap.put("7", "五");//工業區閒置土地抵押貸款
		}
		
		if(UtilConstants.L140m01mVersion.VERSION_20211217.equals(version)){
			loanRatioMap.put("A", new BigDecimal(40));
			loanRatioMap.put("F", new BigDecimal(40));
			loanRatioMap.put("G", new BigDecimal(100));
			loanRatioMap.put("5", new BigDecimal(40));
			loanRatioMap.put("2", new BigDecimal(50));
			loanRatioMap.put("6", new BigDecimal(40));
			loanRatioMap.put("7", new BigDecimal(40));
			
			ratioWordsMap.put("A", "四");
			ratioWordsMap.put("F", "四");
			ratioWordsMap.put("5", "四");
			ratioWordsMap.put("2", "五");//購地貸款
			ratioWordsMap.put("6", "四");//餘屋貸款
			ratioWordsMap.put("7", "四");//工業區閒置土地抵押貸款
			ratioWordsMap.put("G", "十");
		}
		
		if(UtilConstants.L140m01mVersion.VERSION_20230616.equals(version)){
			loanRatioMap.put("A", new BigDecimal(40));
			loanRatioMap.put("F", new BigDecimal(40));
			loanRatioMap.put("G", new BigDecimal(70));
			loanRatioMap.put("5", new BigDecimal(40));
			loanRatioMap.put("2", new BigDecimal(50));
			loanRatioMap.put("6", new BigDecimal(40));
			loanRatioMap.put("7", new BigDecimal(40));
			loanRatioMap.put("H", new BigDecimal(100));//自然人第2戶特定地區房貸(有切結換屋者)
			
			ratioWordsMap.put("A", "四");
			ratioWordsMap.put("F", "四");
			ratioWordsMap.put("G", "七");
			ratioWordsMap.put("5", "四");
			ratioWordsMap.put("2", "五");//購地貸款
			ratioWordsMap.put("6", "四");//餘屋貸款
			ratioWordsMap.put("7", "四");//工業區閒置土地抵押貸款
			ratioWordsMap.put("H", "十");//自然人第2戶特定地區房貸(有切結換屋者)
		}

		if(UtilConstants.L140m01mVersion.VERSION_20240614.equals(version)){
			loanRatioMap.put("A", new BigDecimal(40));
			loanRatioMap.put("F", new BigDecimal(40));
			loanRatioMap.put("G", new BigDecimal(60));
			loanRatioMap.put("5", new BigDecimal(40));
			loanRatioMap.put("2", new BigDecimal(50));
			loanRatioMap.put("6", new BigDecimal(40));
			loanRatioMap.put("7", new BigDecimal(40));
			loanRatioMap.put("H", new BigDecimal(100));//自然人第2戶特定地區房貸(有切結換屋者)

			ratioWordsMap.put("A", "四");
			ratioWordsMap.put("F", "四");
			ratioWordsMap.put("G", "六");
			ratioWordsMap.put("5", "四");
			ratioWordsMap.put("2", "五");//購地貸款
			ratioWordsMap.put("6", "四");//餘屋貸款
			ratioWordsMap.put("7", "四");//工業區閒置土地抵押貸款
			ratioWordsMap.put("H", "十");//自然人第2戶特定地區房貸(有切結換屋者)
		}
		
		if(UtilConstants.L140m01mVersion.VERSION_20240919.equals(version)){
			loanRatioMap.put("A", new BigDecimal(30));
			loanRatioMap.put("F", new BigDecimal(30));
			loanRatioMap.put("I", new BigDecimal(50));
			loanRatioMap.put("J", new BigDecimal(100));//自然人第2戶購置房貸(有切結換屋者)
			loanRatioMap.put("K", new BigDecimal(100));//自然人名下有房屋者之第1戶購屋貸款(無寬限期）
			loanRatioMap.put("L", new BigDecimal(100));
			loanRatioMap.put("5", new BigDecimal(30)); //公司法人購屋
			loanRatioMap.put("2", new BigDecimal(50));
			loanRatioMap.put("6", new BigDecimal(30));
			loanRatioMap.put("7", new BigDecimal(40));

			ratioWordsMap.put("A", "三");
			ratioWordsMap.put("F", "三");
			ratioWordsMap.put("I", "五");
			ratioWordsMap.put("J", "十");
			ratioWordsMap.put("K", "十");
			ratioWordsMap.put("L", "十");
			ratioWordsMap.put("5", "三");//公司法人購屋
			ratioWordsMap.put("2", "五");//購地貸款
			ratioWordsMap.put("6", "三");//餘屋貸款
			ratioWordsMap.put("7", "四");//工業區閒置土地抵押貸款
		}
		
		this.loanRatioMap = loanRatioMap;
		this.ratioWordsMap = ratioWordsMap;
	}
	
	public String checkLoanRatioByVersion(L140M01M l140m01m, String lnf087_version, String property, String quantLoan){
		
		if(loanRatioMap == null || loanRatioMap.isEmpty()){
			return "";
		}
		
		String version = l140m01m.getVersion();
		
		BigDecimal PayPercent = (Util.isEmpty(l140m01m.getPayPercent()) ? BigDecimal.ZERO : l140m01m.getPayPercent()); // 貸款成數
		String reasonCode = l140m01m.getRealEstateLoanLimitReason();
		
		if (UtilConstants.L140M01MCbcCase.自然人.equals(cbcCase) && PayPercent.compareTo(loanRatioMap.get(reasonCode)) > 0) {
			
			/* 屬央行規範之不動產抵押貸款，貸款成數限鑑價及買價孰低{0}成！*/
			if (UtilConstants.L140m01mVersion.VERSION_20210319.equals(version)
					|| UtilConstants.L140m01mVersion.VERSION_20240919.equals(version) && Arrays.asList(RealEstateLoanUtil.NO_GRACE_PERIOD_REASON_CODE).contains(reasonCode)) {
				
				return MessageFormat.format(prop.getProperty("L140M01M.error.mortgage.loanRatio.version20210319"), ratioWordsMap.get(reasonCode));
			}
			/* 屬央行規範之不動產抵押貸款，貸款成數限鑑價及買價孰低{0}成且無寬限期！ */
			if(UtilConstants.L140m01mVersion.VERSION_20210924.equals(version) 
					|| UtilConstants.L140m01mVersion.VERSION_20211217.equals(version) 
					|| UtilConstants.L140m01mVersion.VERSION_20230616.equals(version)
					|| UtilConstants.L140m01mVersion.VERSION_20240614.equals(version)
					|| UtilConstants.L140m01mVersion.VERSION_20240919.equals(version)){
				
				return MessageFormat.format(prop.getProperty("L140M01M.error.mortgage.loanRatio.version21210924"), ratioWordsMap.get(reasonCode));
			}
		}
		
		if(UtilConstants.L140M01MCbcCase.公司法人.equals(cbcCase) && PayPercent.compareTo(loanRatioMap.get(cbcCase)) > 0){
			
			/* 屬央行規範之不動產抵押貸款，貸款成數限鑑價及買價孰低{0}成！*/
			if (UtilConstants.L140m01mVersion.VERSION_20210319.equals(version)) {
				return MessageFormat.format(prop.getProperty("L140M01M.error.mortgage.loanRatio.version20210319"), ratioWordsMap.get(cbcCase));
			}
			/* 屬央行規範之不動產抵押貸款，貸款成數限鑑價及買價孰低{0}成且無寬限期！ */
			if(UtilConstants.L140m01mVersion.VERSION_20210924.equals(version) 
					|| UtilConstants.L140m01mVersion.VERSION_20211217.equals(version) 
					|| UtilConstants.L140m01mVersion.VERSION_20230616.equals(version)
					|| UtilConstants.L140m01mVersion.VERSION_20240614.equals(version)
					|| UtilConstants.L140m01mVersion.VERSION_20240919.equals(version)){
				
				return MessageFormat.format(prop.getProperty("L140M01M.error.mortgage.loanRatio.version21210924"), ratioWordsMap.get(cbcCase));
			}
		}
		
		if (UtilConstants.L140M01MCbcCase.土地抵押貸款.equals(cbcCase)) {
			
			BigDecimal loanRatio = loanRatioMap.get(cbcCase);
			
			//購地貸款, 前一次無版本(舊版), 此次變更為 2021-12-17版及2023-06-16版, 不受最新版本貸放成數限制
			if(StringUtils.isBlank(lnf087_version) && Arrays.asList(RealEstateLoanUtil.specificVersion).contains(version)){
				loanRatio = new BigDecimal(100);
			}
			
			if(LMSUtil.isContainValue(property, UtilConstants.Cntrdoc.Property.變更條件) 
					&& UtilConstants.Usedoc.upType.增額.equals(quantLoan) 
					&& l140m01m.getActStartDate() != null){
				loanRatio = new BigDecimal(100);
			}

			// 都市計畫劃定之使用分區為【住宅區】或【商業區】
			String houseType = Util.trim(l140m01m.getHouseType());
			
			if (UtilConstants.DEFAULT.是.equals(l140m01m.getHouseYN()) 
					&& ("1".equals(houseType) || "2".equals(houseType)) 
						&& (PayPercent.compareTo(loanRatio) > 0)) {
				
				/* 屬央行規範之不動產抵押貸款，貸款成數限鑑價及買價孰低六.五成，且其中一成應俟動工興建後始得撥貸！ */
				return MessageFormat.format(prop.getProperty("L140M01M.error.buyingLand.loanRatio"), ratioWordsMap.get(cbcCase));
			}
		}

		if (UtilConstants.L140M01MCbcCase.餘屋貸款.equals(cbcCase)) {
			
			/* 屬央行規範之不動產抵押貸款，貸款成數限鑑價金額之{0}成！ */
			if (PayPercent.compareTo(loanRatioMap.get(cbcCase)) > 0) {
				return MessageFormat.format(prop.getProperty("L140M01M.error.unsoldHouseLoan.loanRatio"), ratioWordsMap.get(cbcCase));
			}
		}
		
		if (UtilConstants.L140M01MCbcCase.工業區閒置土地抵押貸款.equals(cbcCase)) {
			
			/* 屬央行規範之不動產抵押貸款，貸款成數限金融機構鑑價五.五成！！ */
			if (PayPercent.compareTo(loanRatioMap.get(cbcCase)) > 0) {
				return MessageFormat.format(prop.getProperty("L140M01M.error.industrialIdleLandLoan.loanRatio"), ratioWordsMap.get(cbcCase));
			}
		}
		
		return "";
	}

	public Map<String, BigDecimal> getLoanRatioMap() {
		return loanRatioMap;
	}

	public void setLoanRatioMap(Map<String, BigDecimal> loanRatioMap) {
		this.loanRatioMap = loanRatioMap;
	}

	public Map<String, String> getRatioWordsMap() {
		return ratioWordsMap;
	}

	public void setRatioWordsMap(Map<String, String> ratioWordsMap) {
		this.ratioWordsMap = ratioWordsMap;
	}
	
	public static boolean isNewVersion(String version){
		
		if(Arrays.asList(RealEstateLoanUtil.newVersions).contains(version)){
			return true;
		}
		
		return false;
	}

	public static boolean checkIsAddAllocateFundsCheckList(L140M01M l140m01m, boolean isCheckEjcicB29B33){
					
		String cbcCase = l140m01m.getCbcCase();
		String isHighHouse = l140m01m.getIsHighHouse();
		String isRenew = Util.trim(l140m01m.getIsRenew());
		String isPayOldQuota = Util.trim(l140m01m.getIsPayOldQuota());
		String realEstateLoanLimitReason = Util.trim(l140m01m.getRealEstateLoanLimitReason());
		String is3rdHignHouse = Util.trim(l140m01m.getIs3rdHignHouse());
		String isSaleCase = Util.trim(l140m01m.getIsSaleCase());
		String version = l140m01m.getVersion();
		
		if(isCheckEjcicB29B33 &&
				(UtilConstants.L140m01mVersion.VERSION_20230616.equals(version) || UtilConstants.L140m01mVersion.VERSION_20240614.equals(version))){
			String plusReason = l140m01m.getPlusReason();
			if("4".equals(cbcCase) && ("1".equals(plusReason) || "A".equals(plusReason))){
				return true;
			}
		}
		
		//是否續約/提前續約 + 是否以新額度償還舊額度(含轉貸)
		if(!"NN".equals(isRenew + isPayOldQuota)){
			return false;
		}
		
		//公司法人 or 購地貸款
		if("5".equals(cbcCase) || "2".equals(cbcCase)){
			return true;
		}
		
		//自然人
		if("1".equals(cbcCase)){
			
			//高價住宅
			if("Y".equals(isHighHouse)){
				return true;
			}
			
			//第3戶(含)以上購置房貸
			if(Arrays.asList(RealEstateLoanUtil.THREE_OR_MORE_HOUSE_CODE).contains(realEstateLoanLimitReason)){
				return true;
			}
			
			if("A".equals(realEstateLoanLimitReason) && "Y".equals(is3rdHignHouse)){
				return true;
			}
			
			if((UtilConstants.L140m01mVersion.VERSION_20230616.equals(version) || UtilConstants.L140m01mVersion.VERSION_20240614.equals(version))
					&& "G".equals(realEstateLoanLimitReason)){
				return true;
			}
			
			// 20240919_ver7版 I: 自然人第2戶購置房貸：即於金融聯合徵信中心歸戶查詢已有一戶以上用途代號為「1」(購置不動產)之住宅抵押貸款
			if("I".equals(realEstateLoanLimitReason)){
				return true;
			}
		}
		
		//工業區閒置土地
		if("7".equals(cbcCase) && "Y".equals(isSaleCase)){
			return true;
		}
		
		return false;
	}
	
	//是否為受限戶判斷
	public String checkIsLimitCust(boolean useExist_isLimitCust, String _LNF087_RESTRICT){
		
		String isLimitCust = !UtilConstants.L140M01MCbcCase.非央行自然人.equals(cbcCase) ? UtilConstants.DEFAULT.是 : UtilConstants.DEFAULT.否;// 
		
		if (useExist_isLimitCust && Util.isNotEmpty(_LNF087_RESTRICT)) {
			isLimitCust = _LNF087_RESTRICT;
		}
		
		return isLimitCust;
	}
	
	public String checkLstDateIsCorrect(String isClearLand, String isChgStDate, Date cbControlLstDate, Date lstDate, Date cstDate){
		
		if("Y".equals(isClearLand) && cbControlLstDate != null && lstDate != null){
			
			if(!"Y".equals(isChgStDate) && lstDate.compareTo(cbControlLstDate) != 0){
				//央行購住[最新預計動工日]需與撥貸未動工興建之空地貸款控管註記[最新預計動工日]相同
				return prop.getProperty("L140M01M.msg.error.lstDateMustSameAscbControlLstDate");
			}
			
			if("Y".equals(isChgStDate) && cstDate.compareTo(cbControlLstDate) != 0){
				//央行購住[最新預計動工日]需與撥貸未動工興建之空地貸款控管註記[變更預計動工日]相同
				return prop.getProperty("L140M01M.msg.error.lstDateMustSameAsCstDate");
			}
		}
		
		return "";
	}
}
