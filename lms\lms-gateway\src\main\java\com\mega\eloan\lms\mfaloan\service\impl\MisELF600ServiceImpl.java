package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import tw.com.jcs.common.Util;

import com.mega.eloan.lms.mfaloan.bean.ELF600;
import com.mega.eloan.lms.mfaloan.service.MisELF600Service;

@Service
public class MisELF600ServiceImpl extends AbstractMFAloanJdbc implements
		MisELF600Service {
	
	@Override
	public List<Map<String, Object>> selCntrnoByCustidDupno(String custId, String dupNo) {
		return this.getJdbc().queryForList(
				"ELF600.findContractByCustidDupno",	new Object[] { custId, dupNo });
	}
	
	@Override
	public Map<String, Object> getByCntrNo(String cntrNo) {
		return this.getJdbc().queryForMap("ELF600.findByContract",
				new String[] { cntrNo });
	}
	
	@Override
	public ELF600 findByContract(String contract) {
		Object[] argsObAry = new Object[1];
		ELF600 elf600 = new ELF600();

		argsObAry[0] = contract;

		// select * from mis.elf600 where ELF600_CONTRACT = ?
		List<ELF600> results = getJdbc().query("ELF600.findByContract", 
				argsObAry, elf600.new ELF600RM());

		return CollectionUtils.isEmpty(results) ? null : results.get(0);
	}

	@Override
	public void delete(String contract) {
		// delete from mis.elf600 where ELF600_CONTRACT = ?
		this.getJdbc().update("ELF600.delete", new String[] { contract });
	}

	@Override
	public void insert(ELF600 elf600) {

		this.getJdbc().update(
				"ELF600.insert",
				new Object[] { Util.trim(elf600.getElf600_cust_id()),
						Util.trim(elf600.getElf600_cust_dup()),
						Util.trim(elf600.getElf600_contract()),
						elf600.getElf600_use_date(),
						Util.trim(elf600.getElf600_usecd()),
						Util.trim(elf600.getElf600_usetype()),
						Util.trim(elf600.getElf600_landtype()),
						Util.trim(elf600.getElf600_landkind()),
						Util.trim(elf600.getElf600_idleland()),
						Util.trim(elf600.getElf600_ctltype()),
						elf600.getElf600_fstdate(),
						elf600.getElf600_lstdate(),
						elf600.getElf600_rrmovedt(),
						Util.trim(elf600.getElf600_elflag()),
						elf600.getElf600_enddate(),
						Util.trim(elf600.getElf600_documentno()),
						elf600.getElf600_cstdate(),
						Util.trim(elf600.getElf600_adoptfg()),
						elf600.getElf600_rateadd(),
						elf600.getElf600_custroa(),
						elf600.getElf600_relroa(),
						Util.trim(elf600.getElf600_cstreason()),
						Util.trim(elf600.getElf600_islegal()),
						Util.trim(elf600.getElf600_updater()),
						Util.trim(elf600.getElf600_universal_id()),
						Util.trim(elf600.getElf600_updfrom()),
						elf600.getElf600_loan_bal(),
						elf600.getElf600_act_st_date(),
						Util.trim(elf600.getElf600_control_cd())});
	}
	
	@Override
	public List<Map<String, Object>> getAll() {
		return this.getJdbc().queryForListWithMax("ELF600.findAll", new String[] {});
	}
	
	@Override
	public void updateByContract(ELF600 elf600) {

		this.getJdbc().update(
				"ELF600.updateByContract",
				new Object[] {
					elf600.getElf600_act_st_date(),
					Util.trim(elf600.getElf600_updfrom()),
					Util.trim(elf600.getElf600_universal_id()),
					elf600.getElf600_rrmovedt(),
					Util.trim(elf600.getElf600_updater()),
					Util.trim(elf600.getElf600_contract()),
				});
	}
}
