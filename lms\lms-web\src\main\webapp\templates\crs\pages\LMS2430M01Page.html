<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
<body>
	<th:block th:fragment="innerPageBody">
		<div class="button-menu funcContainer" id="buttonPanel">
			<th:block th:if="${_btnDOC_EDITING_visible}">
				<button type="button" id="btnSave">
					<span class="ui-icon ui-icon-jcs-04" ></span>
					<th:block th:text="#{'button.save'}">儲存</th:block>
				</button>
				<button type="button" id="btnSend">
					<span class="ui-icon ui-icon-jcs-02" ></span>
					<th:block th:text="#{'button.send'}">呈主管覆核</th:block>
				</button>
			</th:block>
			<th:block th:if="${_btnWAIT_APPROVE_visible}">
				<button type="button" id="btnAccept">
					<span class="ui-icon ui-icon-check" ></span>
					<th:block th:text="#{'button.accept'}">覆核</th:block>
				</button>				
			</th:block>			
			<button type="button" id="btnExit" class="forview">
				<span class="ui-icon ui-icon-jcs-01"></span>
				<th:block th:text="#{button.exit}">離開</th:block>
			</button>
		</div>
		<div class="tit2 color-black">
			<th:block th:text="#{doc.title}"></th:block><b>
			<span class="color-blue" id="custInfo"></span></b>
		</div>
		<div class="tabs doc-tabs">
			<ul>
				<li>
					<a href="#tab-01" goto="01">
					<b><th:block th:text="#{doc.docinfo}">文件資訊</th:block></b></a>
				</li>
			</ul>
			<div class="tabCtx-warp">
                <form id="tabForm">
					<div id="tabs-00" th:id="${tabID}" th:insert="~{${panelName}::${panelFragmentName}}"></div>
                </form>
			</div>
		</div>
		
		<input id='mainOid' name='mainOid' type="hidden"/>
		<input id='mainDocStatus' name='mainDocStatus' type="hidden"/>
		<input id='page' name='page' type="hidden" value="01"/>
		<script type="text/javascript">
			loadScript('pagejs/crs/LMS2430M01Page');
		</script>
	</th:block>
</body>
</html>
