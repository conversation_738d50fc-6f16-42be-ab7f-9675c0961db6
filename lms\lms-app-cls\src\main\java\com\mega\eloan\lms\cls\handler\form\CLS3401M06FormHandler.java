package com.mega.eloan.lms.cls.handler.form;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.ContractDocConstants;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.ContractDocService;
import com.mega.eloan.lms.cls.constants.ClsConstants;
import com.mega.eloan.lms.cls.pages.CLS3401M06Page;
import com.mega.eloan.lms.cls.service.CLS3401Service;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.model.C340M01A;
import com.mega.eloan.lms.model.C340M01B;
import com.mega.eloan.lms.model.C340M01C;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140S02A;
import com.mega.eloan.lms.model.L140S02D;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapMath;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 線上對保契約書 => 對應 ContractDocConstants.C340M01A_CtrType.Type_L 線上對保契約書勞工紓困
 * </pre>
 *
 * <AUTHOR>
 * @version <ul>
 *          <li>2021/05/31,EL08034,new
 *          </ul>
 */
@Scope("request")
@Controller("cls3401m06formhandler")
@DomainClass(C340M01A.class)
public class CLS3401M06FormHandler extends AbstractFormHandler {
	private static final DateFormat S_FORMAT = new SimpleDateFormat(UtilConstants.DateFormat.YYYY_MM_DD_HH_MM_SS);
	
	@Resource
	BranchService branchService;

	@Resource
	CLSService clsService;

	@Resource
	ICustomerService iCustomerService;

	@Resource
	TempDataService tempDataService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	CLS3401Service cls3401Service;

	@Resource
	ContractDocService contractDocService;
	
	@Resource
	DwdbBASEService dwdbBASEService;

	Properties prop_cls3401m06 = MessageBundleScriptCreator.getComponentResource(CLS3401M06Page.class);
	Properties prop_abstractEloanPage = MessageBundleScriptCreator.getComponentResource(AbstractEloanPage.class);

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult newC340M01A(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();

		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		// String ctrType = Util.trim(params.getString("ctrType", ContractDocConstants.C340M01A_CtrType.Type_L));
		// String caseMainId = Util.trim(params.getString("caseMainId"));
		String tabMainId = Util.trim(params.getString("tabMainId"));

		String txCode = Util.trim(params.getString(EloanConstants.TRANSACTION_CODE));
		
		List<C340M01A> c340m01as = clsService.findC340M01A_custId(custId,"L");
		if(c340m01as.size()>0){
			throw new CapMessageException("此ID("+custId+")已建立過勞工紓困對保契約書，如需重建請先刪除前一份對保契約書!", getClass());
		}
		if(clsService.is_function_on_codetype("checkNonAuthBankAcc")){
			List<Map<String, Object>> accounts = dwdbBASEService.getAccount_laborContract(custId,dupNo);
			int isNonAuthBankAcc=0;
			if (accounts != null && accounts.size() > 0) {
				Map<String, String> exclude_dpCate = clsService.get_codeTypeWithOrder("c340_ctrTypeA_exclude_dpCate");
				for (Map<String, Object> dataRow : accounts) {
					String accoutNo = MapUtils.getString(dataRow, "MR_PB_ACT_NO", "");
					String flag = MapUtils.getString(dataRow, "MR_PB_MSAC_FLAG", "");
					String dpCate = StringUtils.substring(accoutNo, 3, 5); //存款帳號第4~5碼是存款種類
					/*
					  例如：09-公司戶, 66-黃金存摺
					*/
					if(exclude_dpCate.containsKey(dpCate)){
						isNonAuthBankAcc++;
						continue;
					}
					//未提權網銀存戶
					if(Util.equals(flag, "1") || Util.equals(flag, "2") || Util.equals(flag, "3")){
						isNonAuthBankAcc++;
					}
				}
			}
			if(isNonAuthBankAcc==accounts.size()){
				throw new CapMessageException("此ID("+custId+")未有提權網銀帳號或實體帳號，不允許線上對保!", getClass());
			}
		}

		C340M01A c340m01a = cls3401Service.sys_create_C340RelateCtrTypeL(tabMainId, CreditDocStatusEnum.海外_編製中.getCode(), user.getUserId(), txCode);
		return defaultResult(params, c340m01a, result);
	}
	
	
	@DomainAuth(value = AuthType.Query)
	public IResult queryC340M01A(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C340M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = clsService.findC340M01A_oid(mainOid);

			String page = params.getString(EloanConstants.PAGE);

			if ("01".equals(page)) {
				C340M01C c340m01c = meta.getC340m01cs().get(0);
				String content = "";
				if (c340m01c == null || CapString.isEmpty(c340m01c.getJsonData())) {
					content = "{}";
				} else {
					content = c340m01c.getJsonData();
				}
				JSONObject jsContent = JSONObject.fromObject(content);
				//~~~~~~~~~~~~~~
				LMSUtil.addMetaToResult(result, meta,
						new String[] { "custId", "dupNo", "custName", "caseNo", "contrNumber", "contrPartyNm", "ploanCtrNo"
							, "ploanCtrExprDate", "ploanCtrSignTimeM", "ploanCtrSignTime1"
							, "ploanCtrBegDate", "ploanCtrEndDate", "ploanBorrowerIPAddr", "ploanStakeholderIPAddr"});
				String ownBrId = meta.getOwnBrId();
				result.set("ownBrId", StrUtils.concat(ownBrId, " ", branchService.getBranchName(ownBrId)));
				result.set("docStatus", getMessage("docStatus." + meta.getDocStatus()));
				result.set("ploanCtrStatus", getMessage("ploanCtrStatus." + Util.trim(meta.getPloanCtrStatus())));
				result.set("typCd", getMessage("typCd." + meta.getTypCd()));
				result.set("ctrTypeMapDesc", get_ctrType_desc(meta.getCtrType()));
				result.set("creator", _id_name(meta.getCreator()));
				result.set("createTime", Util.trim(TWNDate.valueOf(meta.getCreateTime())));
				result.set("updater", _id_name(meta.getUpdater()));
				result.set("updateTime", Util.trim(TWNDate.valueOf(meta.getUpdateTime())));
				result.set("rptId_desc", rptId_desc(meta));
				result.set("ploanCtrDcTime", meta.getPloanCtrDcTime()==null?"":S_FORMAT.format(meta.getPloanCtrDcTime()));
				result.set("ploanCtrDcUser", _id_name(meta.getPloanCtrDcUser()));
				result.set("ploanCtrBegDateToEndDate", Util.trim(TWNDate.toAD(meta.getPloanCtrBegDate()))
						+(meta.getPloanCtrBegDate()!=null&&meta.getPloanCtrEndDate()!=null?" ~ ":"")
						+Util.trim(TWNDate.toAD(meta.getPloanCtrEndDate())));
				List<C340M01B> c340m01b_list = clsService.findC340M01B(meta.getMainId());
				result.set("cntrNo", get_c340m01b_cntrNo(c340m01b_list));
				result.set("rateDesc", StringUtils.join(contractDocService.geCtrTypeA_rateDesc(jsContent), "</br>"));
			}
		}

		//在 defaultResult(...) 去讀  c340m01c
		return defaultResult(params, meta, result);
	}

	private String get_c340m01b_cntrNo(List<C340M01B> c340m01b_list) {
		List<String> cntrNo_list = new ArrayList<String>();
		for (C340M01B c340m01b : c340m01b_list) {
			cntrNo_list.add(c340m01b.getCntrNo());
		}
		return StringUtils.join(cntrNo_list, "、");
	}


	private String _id_name(String raw_id) {
		String id = Util.trim(raw_id);
		return Util.trim(id + " " + Util.trim(userInfoService.getUserName(id)));
	}

	/**
	 * 儲存
	 *
	 * @param params
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(AuthType.Modify)
	public IResult saveMain(PageParameters params) throws CapException {
		return _saveAction(params, "N");
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult tempSave(PageParameters params) throws CapException {
		return _saveAction(params, "Y");
	}

	private CapAjaxFormResult _saveAction(PageParameters params, String tempSave) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, tempSave);
		boolean allowIncomplete = Util.equals("Y", params.getString("allowIncomplete"));
		//===
		String KEY = "saveOkFlag";


		Map<String, Object> paramMap = new HashMap<String, Object>();
		// 整理進來的參數值
		for (String key : params.keySet()) {
			if (params.get(key) instanceof String[] && params.getStringArray(key).length > 1) {
				paramMap.put(key, params.getStringArray(key));
			} else {
				paramMap.put(key, params.getString(key));
			}
		}


		String[] page1Params = new String[] { "loanAmt"
				//, "preliminaryFee", "creditCheckFee"
				//, "annualPercentageRate"
				, ContractDocConstants.CtrTypeA.PLOAN_COURT_NAME
				, ContractDocConstants.CtrTypeA.PLOAN_BORROWERMOBILENUMBER
				, ContractDocConstants.CtrTypeA.PLOAN_BORROWEREMAIL
				};
		JSONObject tabJson = new JSONObject();
		for (String param : page1Params) {
			if (paramMap.containsKey(param)) {
				tabJson.put(param, paramMap.get(param));
			}else{
				throw new CapMessageException("lost["+param+"]", getClass());
			}
		}
		

		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set(KEY, false);
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C340M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			try {
				meta = clsService.findC340M01A_oid(mainOid);
				List<C340M01C> c340m01c_list = clsService.findC340M01C(meta.getMainId());
				//~~~~~~
				String page = params.getString(EloanConstants.PAGE);

				if ("01".equals(page)) {
					//這2欄在 前端 先不顯示
					//CapBeanUtil.map2Bean(params, meta, new String[] { "contrNumber", "contrPartyNm" });
				}

				C340M01C c340m01c = c340m01c_list.get(0);
				JSONObject targetJsonObject = DataParse.toJSON(c340m01c.getJsonData());
				

				//==================================================
				//此寫法會把 JsonData 全部蓋掉
//				c340m01c.setJsonData(tabJson.toString());

				//==================================================
				//只針對｛指定欄位｝去 update 值
				targetJsonObject.putAll(tabJson);				
				c340m01c.setJsonData(targetJsonObject.toString());
				clsService.save(c340m01c);


				meta.setDeletedTime(null);
				meta.setUpdater(user.getUserId());
				meta.setUpdateTime(CapDate.getCurrentTimestamp());
				meta.setRandomCode(IDGenerator.getRandomCode());

				if ("Y".equals(tempSave)) {
					cls3401Service.saveTemporaryMeta(meta);
				} else {
					cls3401Service.saveMeta(meta);
				}
				// ===
				if (Util.notEquals("Y", SimpleContextHolder.get(EloanConstants.TEMPSAVE_RUN))) {
					// 在tempSave<>Y,若有未填欄位,丟 CapMessageException, 讓
					// saveOkFlag==false

					String err_msg = check_data_relation(meta);
					String unKeyIn_column = unKeyIn_column(meta);
					if (allowIncomplete == false && Util.isNotEmpty(err_msg)) {
						throw new CapMessageException(err_msg, getClass());
					}

					if (allowIncomplete && (Util.isNotEmpty(err_msg) || Util.isNotEmpty(unKeyIn_column))) {
						result.set("IncompleteMsg", err_msg + ((Util.isNotEmpty(err_msg) && Util.isNotEmpty(
								unKeyIn_column)) ? "<br/>" : "") + unKeyIn_column);
					}
				}
				result.set(KEY, true);
			} catch (Exception e) {
				logger.error(StrUtils.getStackTrace(e));
				throw new CapException(e, getClass());
			}
		}

		return defaultResult(params, meta, result);
	}

	/**
	 * 呈主管覆核
	 *
	 * @param params
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(AuthType.Modify + AuthType.Accept)
	public IResult flowAction(PageParameters params) throws Throwable {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");

		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String decisionExpr = Util.trim(params.getString("decisionExpr"));

		C340M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = clsService.findC340M01A_oid(mainOid);

			if (CreditDocStatusEnum.海外_編製中.getCode().equals(meta.getDocStatus())) {
				List<C340M01C> c340m01c_list = clsService.findC340M01C(meta.getMainId());
				int cnt_actNo = 0;
				List<Object> loanAcct_list = null;
				if(c340m01c_list.size()>0){
					C340M01C c340m01c = c340m01c_list.get(0);
					String jsonData = Util.trim(c340m01c.getJsonData());
					JSONObject jsonObject = DataParse.toJSON(jsonData);
					
					loanAcct_list = LMSUtil.get_notEmpty_One_or_Multiple_Data(jsonObject, ContractDocConstants.CtrTypeL.PLOAN_ACCTNO_FLAG_LIST);
					cnt_actNo = loanAcct_list.size();
				}
				//
				if((cnt_actNo==0||(cnt_actNo==1 && Util.equals("[]", StringUtils.join(loanAcct_list, "")))) && clsService.is_function_on_codetype("c340_chkTypeL_actNo")){
					throw new CapMessageException("此客戶「無本行存款帳號」或「僅有數位存款帳號但未提升權限」，無法進行線上對保", this.getClass());
				}		
				
				if(true){
					String ploanCtrNo = Util.trim(meta.getPloanCtrNo());
					if(clsService.findC340M01A_ploanCtrNo(ploanCtrNo).size()>1){
						throw new CapMessageException(prop_cls3401m06.getProperty("C340M01A.ploanCtrNo")+"(" + ploanCtrNo + ")重複，請刪除此份契約，重新產出。", this.getClass());
					}
				}
				//~~~~~~~~~~
				String cntrNo = meta.getC340m01bs().get(0).getCntrNo();
				List<C340M01A> flowingC340M01A = cls3401Service.findFlowingC340M01A(cntrNo, meta.getCtrType());
				if (flowingC340M01A.size() > 0) {
					C340M01A pendingCase = flowingC340M01A.get(0);
					throw new CapMessageException("額度序號" + cntrNo 
							+ "（"+prop_cls3401m06.getProperty("C340M01A.ploanCtrNo")+"：" +pendingCase.getPloanCtrNo()
							+ (Util.equals(CreditDocStatusEnum.海外_待覆核.getCode(), pendingCase.getDocStatus())?"，e-Loan文件狀態："+prop_abstractEloanPage.getProperty("docStatus." + pendingCase.getDocStatus()):"")
							+"）"							
							+"尚未完成線上對保程序。"+"<br/>"
							+"因此本案契約（" +meta.getPloanCtrNo()+"）無法呈主管覆核", this.getClass());
				}
			}
			
			if (params.containsKey("decisionExpr")) {
				if ("ok".equals(decisionExpr)) {
					//J-110-0233_10702_B1009 Web e-Loan檢核申請書不承作案件
					L120M01A l120m01a = clsService.findL120M01A_mainId(meta.getCaseMainId());
					String laborContractIsRejectMsg=clsService.findLaborContractIsReject(l120m01a);
					if(Util.isNotEmpty(laborContractIsRejectMsg)){
						throw new CapMessageException(laborContractIsRejectMsg, getClass());
					}
					if (Util.equals(user.getUserId(), meta.getUpdater())) {
						// EFD0053' 覆核人員不可與「經辦人員或其它覆核人員」為同一人
						throw new CapMessageException(RespMsgHelper.getMessage("EFD0053"), getClass());
					}
				}
			}

			cls3401Service.flowAction(meta, params.containsKey("decisionExpr"), decisionExpr);

		}
		return defaultResult(params, meta, result);
	}

	@DomainAuth(AuthType.Modify)
	public IResult delC340M01A(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C340M01A c340m01a = clsService.findC340M01A_oid(mainOid);
		if (c340m01a != null) {
			c340m01a.setDeletedTime(CapDate.getCurrentTimestamp());
			clsService.save(c340m01a);
		}
		return result;
	}

	private String check_data_relation(C340M01A c340m01a) {
		//===========================
		// 若簽報書核准後，有被退回修改
		if (c340m01a == null) {
			return "查無 契約書資料";
		}
		L120M01A l120m01a = clsService.findL120M01A_mainId(c340m01a.getCaseMainId());
		if (l120m01a == null) {
			return "查無 簽報書資料" + "(" + c340m01a.getCaseMainId() + ")。" + "若退回且修改已核准簽報書，請重新產製契約書。";
		}
		for (C340M01B c340m01b : clsService.findC340M01B(c340m01a.getMainId())) {
			L140M01A l140m01a = clsService.findL140M01A_mainId(c340m01b.getTabMainId());
			if (l140m01a == null) {
				return "查無 額度明細表資料(" + c340m01b.getTabMainId() + ")。" + "若退回且修改已核准簽報書，請重新產製契約書。";
			}
		}
		
		String ploanCtrNo = Util.trim(c340m01a.getPloanCtrNo());
		if(clsService.findC340M01A_ploanCtrNo(ploanCtrNo).size()>1){
			return prop_cls3401m06.getProperty("C340M01A.ploanCtrNo")+"(" + ploanCtrNo + ")重複，請刪除此份契約，重新產出。";
		}
		return "";
	}

	@DomainAuth(AuthType.Modify)
	public IResult check_C340(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C340M01A meta = clsService.findC340M01A_oid(mainOid);
		if (true) {
			String err_msg = check_data_relation(meta);
			if (Util.isNotEmpty(err_msg)) {
				throw new CapMessageException(err_msg, getClass());
			}
		}
		if (true) {
			String msg = unKeyIn_column(meta);
			if (Util.isNotEmpty(msg)) {
				result.set("msg", msg);
			}
		}
		result.set("model_custId", Util.trim(meta.getCustId()));
		result.set("model_custName", Util.trim(meta.getCustName()));
		result.set("model_caseNo", Util.trim(meta.getCaseNo()));
		result.set("model_ctrTypeDesc", get_ctrType_desc(meta.getCtrType()));
		return result;
	}

	private String unKeyIn_column(C340M01A c340m01a) throws CapException {
		Map<String, Object> map = new HashMap<String, Object>();
		List<C340M01C> c340m01c_list = clsService.findC340M01C(c340m01a.getMainId());
		for (C340M01C c340m01c : c340m01c_list) {
			String jsonData = Util.trim(c340m01c.getJsonData());
			JSONObject jsonObject = DataParse.toJSON(jsonData);

			LMSUtil.addJsonToMap(map, jsonObject);
		}

		List<String> list = new ArrayList<String>();

		String[] cols_deliv = new String[] { "loanAmt", "loanPeriod", "preliminaryFee", "creditCheckFee"
				, "drawDownType", "repaymentMethod"
				//, "annualPercentageRate" 
				, ContractDocConstants.CtrTypeA.PLOAN_BORROWERMOBILENUMBER
//				, ContractDocConstants.CtrTypeA.PLOAN_BORROWEREMAIL   若老年人沒有 e-mail
				};
		
		for (String columnChk : cols_deliv) {
			if (CapString.isEmpty(MapUtils.getString(map, columnChk, ""))) {
				list.add(MessageFormat.format(prop_cls3401m06.getProperty("msg.unkeyin"),
						prop_cls3401m06.getProperty(columnChk)));
			}
		}

		if(clsService.is_function_on_codetype("chk_ploanCtr_input")){
			String borrowerMobileNumber = Util.trim(MapUtils.getString(map, ContractDocConstants.CtrTypeA.PLOAN_BORROWERMOBILENUMBER, ""));
			String borrowerEmail = Util.trim(MapUtils.getString(map, ContractDocConstants.CtrTypeA.PLOAN_BORROWEREMAIL, ""));
			if(Util.isNotEmpty(borrowerMobileNumber)){
				if(borrowerMobileNumber.length()!=10){
					list.add(prop_cls3401m06.getProperty("borrowerMobileNumber")+":"+borrowerMobileNumber+" 資料格式錯誤");
				}
			}
			if(Util.isNotEmpty(borrowerEmail)){
				if(borrowerEmail.indexOf("@")<=0){
					list.add(prop_cls3401m06.getProperty("borrowerEmail")+":"+borrowerEmail+" 資料格式錯誤");
				}
			}
		}
		List<C340M01B> c340m01b_list = clsService.findC340M01B(c340m01a.getMainId());
		if (c340m01b_list != null) {
			//產品種類
			String tabMainId = c340m01b_list.get(0).getTabMainId();
			L140M01A l140m01a = clsService.findL140M01A_mainId(tabMainId);
			List<L140S02A> l140s02as = clsService.findL140S02A(l140m01a);
			L140S02A l140s02a = l140s02as.get(0);

			BigDecimal origloanAmt = l140s02a.getLoanAmt() == null ? BigDecimal.ZERO : l140s02a.getLoanAmt();
			BigDecimal loanAmt = CapMath.getBigDecimal(MapUtils.getString(map, "loanAmt", ""));


			if (loanAmt.compareTo(origloanAmt) > 0) {
				list.add("借款金額(" + loanAmt + "元)" + " > 原核准額度金額(" + origloanAmt + "元)");
			}


			List<L140S02D> l140s02ds = clsService.findL140S02D_orderByPhase(l140m01a.getMainId(), l140s02a.getSeq(),
					"Y");
			for (L140S02D l140s02d : l140s02ds) {
				Integer phase = l140s02d.getPhase();
				String l140s02dOid = l140s02d.getOid();
				BigDecimal l140s02dNowRate = l140s02d.getNowRate() == null ? BigDecimal.ZERO : l140s02d.getNowRate();
				BigDecimal l140s02d_rateUser = l140s02d.getRateUser() == null ? BigDecimal.ZERO : l140s02d.getRateUser();
				if (phase == 1) {

					String rateRange1Oid = MapUtils.getString(map, "rateRange1Oid", "");
					if(Util.isEmpty(rateRange1Oid) && Util.isNotEmpty(Util.trim(map.get(ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE1_RATEDESC_M3)))){
						continue;
					}
					BigDecimal rateRange1Param03 = CapMath.getBigDecimal(
							MapUtils.getString(map, ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE1_FIXED_RATE , ""));
					BigDecimal rateRange1Param07 = CapMath.getBigDecimal(
							MapUtils.getString(map, ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE1_RESULTRATE , ""));
					if (l140s02dOid.equals(rateRange1Oid)) {
						if(CrsUtil.RATE_TYPE_01.equals(l140s02d.getRateType()) && Util.equals("C01", l140s02d.getRateUserType())){
							addList_when___inputRate_lt_approveRate(list, rateRange1Param03, l140s02d_rateUser);
						}else{
							addList_when___inputRate_lt_approveRate(list, rateRange1Param07, l140s02dNowRate);
						}						
					} else {
						list.add("利率資訊有誤，請刪除此筆資料，重新產生契約書");
					}

				} else if (phase == 2) {

					String rateRange2Oid = MapUtils.getString(map, "rateRange2Oid", "");
					if(Util.isEmpty(rateRange2Oid) && Util.isNotEmpty(Util.trim(map.get(ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE1_RATEDESC_M3)))){
						continue;
					}
					BigDecimal rateRange2Param03 = CapMath.getBigDecimal(
							MapUtils.getString(map, ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE2_FIXED_RATE , ""));
					BigDecimal rateRange2Param07 = CapMath.getBigDecimal(
							MapUtils.getString(map, ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE2_RESULTRATE , ""));
					if (l140s02dOid.equals(rateRange2Oid)) {
						if(CrsUtil.RATE_TYPE_01.equals(l140s02d.getRateType()) && Util.equals("C01", l140s02d.getRateUserType())){
							addList_when___inputRate_lt_approveRate(list, rateRange2Param03, l140s02d_rateUser);
						}else{
							addList_when___inputRate_lt_approveRate(list, rateRange2Param07, l140s02dNowRate);
						}						
					} else {
						list.add("利率資訊有誤，請刪除此筆資料，重新產生契約書");
					}
				}
			}

		}

		return StringUtils.join(list, "<br/>");
	}

	private void addList_when___inputRate_lt_approveRate(List<String> list, BigDecimal inputRate, BigDecimal approveRate){
		if(inputRate.compareTo(approveRate) < 0){
			list.add("利率:" + inputRate.stripTrailingZeros() + "%不得低於原核准額度利率:" + approveRate.stripTrailingZeros() + "%");	
		}
	}
	
	private String get_ctrType_desc(String ctrType) {
		if (Util.equals(ctrType, ContractDocConstants.C340M01A_CtrType.Type_L)) {
			return prop_cls3401m06.getProperty("C340M01A.ctrType.L");
		}
		return "";
	}
	private String rptId_desc(C340M01A meta) {
		String ctrType = Util.trim(meta.getCtrType());
		String rptId = Util.trim(meta.getRptId());
		if(Util.equals(ctrType, ContractDocConstants.C340M01A_CtrType.Type_L)){
			if(Util.equals(ContractDocConstants.C340M01A_RptId.CtrTypeL_V202106, rptId)){
				return "2021.06版本";
			}
		}
		return rptId;
	}

	private CapAjaxFormResult defaultResult(PageParameters params, C340M01A meta,
			CapAjaxFormResult result) throws CapException {
		// 在 UI 可調整加碼利率，連帶會影響｛計算結果利率｝，所以之前才會在 defaultResult(...) 裡去抓 c340m01c
		C340M01C c340m01c = clsService.findC340M01C(meta.getMainId(), ContractDocConstants.C340M01C_ItemType.TYPE_0);

		if (c340m01c != null) {
			JSONObject jsonData = c340m01c.getJsonData() == null ? new JSONObject() : JSONObject.fromObject(
					c340m01c.getJsonData());
			jsonData.remove("loanAcct");
			result.set("loanAcct", jsonData.optString("loanAcct_and_Flag"));
			result.putAll(jsonData);
			
			result.set(ContractDocConstants.CtrTypeA.PLOAN_LENDING_PLAN_OPTION, LMSUtil.getDesc(contractDocService.get_ploan_lendingPlanInfo_showOption(), Util.trim(result.get(ContractDocConstants.CtrTypeA.PLOAN_LENDING_PLAN_OPTION))));
			
			result.set("jsShow_rateRange1View_M3", Util.isEmpty(Util.trim(jsonData.optString(ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE1_RATEDESC_M3)))?"N":"Y");
		}
		
		C340M01C c340m01c_9 = clsService.findC340M01C(meta.getMainId(), "9");

		if (c340m01c_9 != null) {
			JSONObject jsonData = c340m01c_9.getJsonData() == null ? new JSONObject() : JSONObject.fromObject(
					c340m01c_9.getJsonData());
			if(jsonData!=null){
				String ploanStakeholder=jsonData.getString("stakeholderMap");
				JSONObject ploanStakeholder_jsonData=JSONObject.fromObject(ploanStakeholder);
				if(ploanStakeholder_jsonData!=null && ploanStakeholder_jsonData.getString("remain")!=null &&ploanStakeholder_jsonData.getString("law44")!=null && ploanStakeholder_jsonData.getString("law45")!=null){
					String signTime=meta.getPloanCtrSignTimeM()!=null?S_FORMAT.format(meta.getPloanCtrSignTimeM()):"";
					result.set("ploanStakeholder",signTime +" 銀行法:"+ploanStakeholder_jsonData.getString("remain")+"|44條:"+ploanStakeholder_jsonData.getString("law44")+"|45條:"+ploanStakeholder_jsonData.getString("law45"));
				}
				if(c340m01c.getJsonData()!=null){
					List<Object> loanAcct_list = LMSUtil.get_notEmpty_One_or_Multiple_Data(JSONObject.fromObject(c340m01c.getJsonData()), ContractDocConstants.CtrTypeL.PLOAN_ACCTNO_FLAG_LIST);
					result.set("loanAcct",StringUtils.join(loanAcct_list, ",")+"(選擇:"+ jsonData.getString("bankAcctNo")+")");
				}
			}
		}

		result.set(EloanConstants.PAGE, Util.trim(params.getString(EloanConstants.PAGE)));
		result.set(EloanConstants.MAIN_OID, Util.trim(meta.getOid()));
		result.set(EloanConstants.MAIN_DOC_STATUS, Util.trim(meta.getDocStatus()));
		result.set(EloanConstants.MAIN_ID, Util.trim(meta.getMainId()));
		result.set("custId", meta.getCustId());
		result.set("ctrType", meta.getCtrType());
		result.set("custInfo",
				Util.trim(meta.getCustId()) + " " + Util.trim(meta.getDupNo()) + " " + Util.trim(meta.getCustName()));
		result.set("ctrTypeHeaderDesc", get_ctrType_desc(meta.getCtrType()));
		return result;
	}


	/**
	 * 線上對保作廢
	 *
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Accept)
	public IResult inValidC340M01A(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C340M01A c340m01a = clsService.findC340M01A_oid(mainOid);
		if (c340m01a != null) {
			cls3401Service.inValidOnlineCtr(c340m01a);
			
			if(true){
				clsService.changeC122M01AStatFlag(c340m01a.getOwnBrId(), c340m01a.getCustId(),ClsConstants.C122M01A_StatFlag.已核貸);
			}
			
			result.set("ploanCtrStatus", getMessage("ploanCtrStatus." + Util.trim(c340m01a.getPloanCtrStatus())));
			result.set("ploanCtrDcTime", c340m01a.getPloanCtrDcTime()==null?"":S_FORMAT.format(c340m01a.getPloanCtrDcTime()));
			result.set("ploanCtrDcUser", _id_name(c340m01a.getPloanCtrDcUser()));
		}
		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult showJSON(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		String mainId = params.getString(EloanConstants.MAIN_ID);
		C340M01A meta = clsService.findC340M01A_mainId(mainId);
		if(meta==null){
			throw new CapMessageException("mainId="+mainId+" not found", getClass());
		}	
		if(true){	
			List<C340M01C> c340m01c_list = clsService.findC340M01C(meta.getMainId());
			for(C340M01C c340m01c : c340m01c_list){
				result.set("c340m01c_"+c340m01c.getItemType(), c340m01c.getJsonData());
			}
			result.set("mainId", meta.getMainId());
		}
		return result;
	}
	
	/**
	 * 判斷登入者是否僅有EL電銷權限
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult check_only_expermission(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		result.set("only_ex_permission", user.isEXAuth());//是否僅有電銷權限	
		return result;
	}
}
