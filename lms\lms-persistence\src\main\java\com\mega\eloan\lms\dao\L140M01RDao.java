/* 
 * L140M01RDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;
import com.mega.eloan.lms.model.L140M01R;


/** 各項費用檔 **/
public interface L140M01RDao extends IGenericDao<L140M01R> {

	List<L140M01R> findByMainId(String mainId);
	
	List<L140M01R> findByMainIdFeeSrc(String mainId, String feeSrc);

	public List<L140M01R> findByMainIdFeeSrcFeeNo(String mainId, String feeSrc,	String feeNo);
}