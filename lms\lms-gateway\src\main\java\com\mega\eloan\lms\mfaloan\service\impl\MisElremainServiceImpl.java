/* 
 * MisElremainServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.mfaloan.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import tw.com.jcs.common.Util;

import com.mega.eloan.lms.mfaloan.service.MisElremainService;

/**
 * <pre>
 * 利害關係人
 * </pre>
 * 
 * @since 2012/11/8
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/11/8,Fantasy,new
 *          </ul>
 */
@Service
public class MisElremainServiceImpl extends AbstractMFAloanJdbc implements
		MisElremainService {

	@Override
	public List<Map<String, Object>> findElremainByReidAndDupno(String reid,
			String dupno) {
		return this.getJdbc().queryForList("ELREMAIN.findByReidAndDupNo",
				new String[] { reid, dupno });
	}

	@Override
	public List<Map<String, Object>> findElresecdByCmpid(String cmpid) {
		List<Map<String, Object>> r = new ArrayList<Map<String, Object>>();
		
		for(Map<String, Object> item:this.getJdbc().queryForList("ELRESECD.findByCmpid_A",
				new String[] { cmpid, cmpid })){
			/*
			 ID         CMPID      REIDNM
			 */
			String ID = Util.trim(item.get("ID"));
			String CMPID = Util.trim(item.get("CMPID"));
			String REIDNM = Util.trim(item.get("REIDNM"));
			
			List<Map<String, Object>> result1List = this.getJdbc().queryForList("ELRESECD.findByCmpid_B",
					new String[] { ID });
			if(result1List!=null && result1List.size()>0){
				r.addAll(result1List);
			}
		}
		
		return r;
	}

	@Override
	public List<Map<String, Object>> findElrescomByCmpid(String cmpid) {
		List<Map<String, Object>> r = new ArrayList<Map<String, Object>>();
		BigDecimal stockThreshold = new BigDecimal("3");// 目前銀行法規定持股比例須大於(含) 3%時,才為關係人
		
		for(Map<String, Object> item:this.getJdbc().queryForList("ELRESCOM.findByCmpid_A",
				new String[] { cmpid, cmpid })){
			/*
			 ID         CMPID      REIDNM
			 */
			String ID = Util.trim(item.get("ID"));
			String CMPID = Util.trim(item.get("CMPID"));
			String REIDNM = Util.trim(item.get("REIDNM"));
			
			List<Map<String, Object>> result1List = this.getJdbc().queryForList("ELRESCOM.findByCmpid_B",
					new String[] { ID });
			if(result1List!=null && result1List.size()>0){
			for(Map<String, Object> result1:result1List){					
				String result1_RECTL = Util.trim(result1.get("RECTL"));				
				if(result1_RECTL.equals("5")){//本行轉投資事業
					//持股比例須大於(含) 3%時,才為關係人
					BigDecimal result1_RESKRT = Util.parseBigDecimal(result1.get("RESKRT"));
					if(result1_RESKRT.compareTo(stockThreshold)<0){
						continue;
					}
				} 
				//---
				r.add(result1);//single
			}
			}
		}		
		return r;
	}

	@Override
	public List<Map<String, Object>> findElrex45ByRelidAndLawno(String relId,
			String mageLawno) {
		return this.getJdbc().queryForList("ELREX45.findRpsByIdAndLawNo",
				new String[] { relId, mageLawno });
	}

	@Override
	public List<Map<String, Object>> selForCLS(String custId, String dupNo){
		return this.getJdbc().queryForList("MISELREMAIN.selForCLS",
				new String[] { custId, dupNo, custId, custId});
	}
}
