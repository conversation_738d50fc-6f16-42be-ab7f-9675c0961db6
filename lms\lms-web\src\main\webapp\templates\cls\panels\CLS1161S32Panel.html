<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:th="http://www.thymeleaf.org">
<body>
	<th:block th:fragment="panelFragmentBody">
		<div>	
		<table border='0' class='tb2'>
			<tr>
				<td class='hd2'><th:block th:text="#{'C160M03A.custId'}"></th:block>&nbsp;</td>							
				<td><input type="text" id="findBaseDataId" name="findBaseDataId" maxlength="10" size="10" class="upText" />
					&nbsp;&nbsp;			
					<button type="button" id="findBaseDataIdBt" class="forview">
	                    <span class="text-only"><th:block th:text="#{'button.filter'}">篩選</th:block></span>					
	                </button>			
				</td>
			</tr>				
        </table>
		</div>
		
		<fieldset>
			<legend><b><th:block th:text="#{'title.tab02'}">詳細資料</th:block></b></legend>
			<div id="gridview"></div>
		</fieldset>
		
		<script type="text/javascript">loadScript('pagejs/cls/CLS1161S32Panel');</script>
		
	</th:block>
</body>
</html>
