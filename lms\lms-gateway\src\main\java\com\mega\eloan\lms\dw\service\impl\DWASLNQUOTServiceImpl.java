/* 
 *DWASLNOVEROVSServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dw.service.impl;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.dw.service.DWASLNQUOTService;

/**
 * <pre>
 * 國內 - 放款額度匯集日檔
 * </pre>
 * 
 * @since 2012/4/5
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/4/5,REX,new
 *          </ul>
 */
@Service
public class DWASLNQUOTServiceImpl extends AbstractDWJdbc implements
		DWASLNQUOTService {

	@Override
	public int findByCntrNoAndCustIdAndDupNo(String cntrNo, String custId,
			String dupNo) {
		return this.getJdbc().queryForInt("DWASLNQUOT.selByContract",
				new Object[] { cntrNo, custId, dupNo });
	}

}
