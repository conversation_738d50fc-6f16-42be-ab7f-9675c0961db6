/* 
 * L120M01LDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120M01L;

/** 簽報書各業務審核層級記錄檔 **/
public interface L120M01LDao extends IGenericDao<L120M01L> {

	L120M01L findByOid(String oid);

	List<L120M01L> findByMainId(String mainId);

	List<L120M01L> findByIndex01(String mainId);

	/**
	 * 取得L120M01L資料by業務
	 * 
	 * @param mainId
	 * @param loanKind
	 * @return
	 */
	List<L120M01L> findByMainIdAndLoanKind(String mainId, String loanKind);

	/**
	 * 取得所有借款人合併的那一筆資料 L120M01L資料by業務
	 * 
	 * @param mainId
	 * @param loanKind
	 * @return
	 */
	L120M01L findIsFullCaseByMainIdAndLoanKind(String mainId, String loanKind);

	/**
	 * 取得某一借款人的某一業務 L120M01L資料
	 * 
	 * @param mainId
	 * @param loanKind
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	L120M01L findByMainIdAndLoanKindAndCust(String mainId, String loanKind,
			String custId, String dupNo);

	/**
	 * 取得某一借款人的全L120M01L資料
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	List<L120M01L> findByMainIdAndCust(String mainId, String custId,
			String dupNo);
}