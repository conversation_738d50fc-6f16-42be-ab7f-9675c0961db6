---------------------------------------------------------
-- LMS.L999S01A 綜合授信契約書檔
---------------------------------------------------------
---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.L999S01A;
CREATE TABLE LMS.L999S01A (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)     ,
	ITEMTYPE      VARCHAR(8)   ,
	TOTLOANAMT    DECIMAL(15,0),
	USEDSDATE     DATE         ,
	USED<PERSON>AT<PERSON>     DATE         ,
	<PERSON><PERSON><PERSON><PERSON>       DECIMAL(2,0) ,
	DRATE1        DECIMAL(2,0) ,
	D<PERSON>NTH2       DECIMAL(2,0) ,
	DRATE2        DECIMAL(2,0) ,
	DRATEADD1     DECIMAL(5,2) ,
	DRATEADD2     DECIMAL(5,2) ,
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_L999S01A PRIMARY KEY(OID)
) IN  EL_DATA_4KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.L999S01A IS '綜合授信契約書檔';
COMMENT ON LMS.L999S01A (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	ITEMTYPE      IS '授信種類', 
	TOTLOANAMT    IS '授信總額度', 
	USEDSDATE     IS '動用期間(起)', 
	USEDEDATE     IS '動用期間(迄)', 
	DMONTH1       IS '違約金及遲延利息(遲延還本或付息)-逾期', 
	DRATE1        IS '違約金及遲延利息(遲延還本或付息)-利率百分比', 
	DMONTH2       IS '違約金及遲延利息(遲延還本或付息)-逾期超過', 
	DRATE2        IS '違約金及遲延利息(遲延還本或付息)-利率百分比', 
	DRATEADD1     IS '違約金及遲延利息(未依約清償本金)-計收延遲利息加碼', 
	DRATEADD2     IS '違約金及遲延利息(未依約清償本金)-計收延遲利息加碼(保證債務)', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
