package com.mega.eloan.lms.crs.pages;

import com.iisigroup.cap.component.PageParameters;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;



import tw.com.jcs.common.Util;

import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.model.C241M01B;


/**
 * <AUTHOR> 開啟LnDetail 帳務明細資料
 *
 */
@Controller
@RequestMapping("/crs/lms2411m02")

public class LMS2411M02Page extends AbstractEloanForm {

	@Autowired
	RetrialService retrialService;
	
	public LMS2411M02Page() {
		super();
	}

	@Override
	public void execute(ModelMap model, PageParameters params) throws Exception {
		super.execute(model, params);
		String c241m01b_oid = Util.trim(params.getString("c241m01b_oid"));
		C241M01B c241m01b = retrialService.findC241M01B_oid(c241m01b_oid);
		boolean show_lcNo = false; 
		boolean show_dateOfReview = false;
		boolean show_sQuotasBal = false;
		if(c241m01b!=null){
			show_lcNo = Util.isNotEmpty(c241m01b.getLcNo());
			show_dateOfReview = CrsUtil.isNOT_null_and_NOTZeroDate(c241m01b.getDateOfReview());
			show_sQuotasBal = ( c241m01b.getSQuotaAmt()!=null || c241m01b.getSBalAmt()!=null );
		}
		model.addAttribute("show_lcNo", show_lcNo);
		model.addAttribute("show_dateOfReview", show_dateOfReview );
		model.addAttribute("show_sQuotasBal", show_sQuotasBal );
		renderJsI18N(LMS2411M02Page.class);
	}
	

	@Override
	public Class<? extends Meta> getDomainClass() {
		//C241M01B 不是 extends Meta, 而是 extends GenericBean
		return null;
	}

	@Override
	protected String getViewName() {
		return "common/pages/None";
	}
}
