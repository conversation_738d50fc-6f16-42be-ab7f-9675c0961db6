package com.mega.eloan.lms.dc.action;

import com.mega.eloan.lms.dc.conf.MainConfig;
import com.mega.eloan.lms.dc.util.DXLUtil;
import com.mega.eloan.lms.dc.util.Util;

/**
 * <pre>
 * ChkType
 * </pre>
 * 
 * @since 2012/12/20
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/20,Bang,new
 *          </ul>
 */
public class ChkType {

	/**
	 * 檢查目前執行的type類型
	 * 
	 * @param itemType
	 *            String: db2Xml中的type屬性欄位
	 * @param itemValue
	 *            String: 從.dxl中對應到db2XML後取得的值
	 * @param itemNum
	 *            int: db2Xml中的Num屬性欄位
	 * @param dxlXml
	 *            String: 已轉換為String型態之dxl檔
	 */
	public String chkAllType(String itemType, String itemValue, int itemNum,
			String dxlXml) throws Exception {
		String typeValue = itemValue;

		// 評等表類型
		if ("crdType".equalsIgnoreCase(itemType)) {
			typeValue = DXLUtil.codeConvert(itemType, itemValue);
		}
		// 傳入值為1、2、5 其中之一時，回傳1
		// 傳入值為3、4 其中之一時，回傳 2
		// 若以上皆非，請回傳原值
		else if ("docKind".equalsIgnoreCase(itemType)) {
			typeValue = DXLUtil.getDocKind(itemValue);
		}
		// 目前文件狀態
		else if ("docStatus".equalsIgnoreCase(itemType)) {
			typeValue = DXLUtil.codeConvert(itemType, itemValue);
		}
		// 利率基礎-加減年利率選項
		else if ("disYearOp".equalsIgnoreCase(itemType)) {
			typeValue = DXLUtil.codeConvert(itemType, itemValue);
		}
		// 區部別
		else if ("typCd".equalsIgnoreCase(itemType)) {
			typeValue = DXLUtil.codeConvert(itemType, itemValue);
		}
		// 連保人/保證人
		else if ("guarantorType".equalsIgnoreCase(itemType)) {
			typeValue = DXLUtil.codeConvert(itemType, itemValue);
		}
		// idn11
		else if ("idn11".equalsIgnoreCase(itemType)) {
			typeValue = DXLUtil.getIdn11(itemValue, itemNum);
		}

		// lmtOther
		else if ("lmtOther".equalsIgnoreCase(itemType)) {
			typeValue = DXLUtil.getLmtOther(itemValue);
		}
		// 按期計收,按X月預收
		else if ("numC2E".equalsIgnoreCase(itemType)) {
			typeValue = DXLUtil.codeConvert(itemType, itemValue);
		}
		// typer
		else if ("typer".equalsIgnoreCase(itemType)) {
			switch (itemNum) {
			case 0:
				typeValue = DXLUtil.getEmpName(itemValue);
				typeValue = DXLUtil.searchId(dxlXml, typeValue);
				break;
			case 1:
				typeValue = DXLUtil.getDateTime(itemValue);
				break;
			}
		}
		// 負責人欄類型
		else if ("posType".equalsIgnoreCase(itemType)) {
			typeValue = DXLUtil.codeConvert(itemType, itemValue);
		}
		// projectNo
		else if ("projectNo".equalsIgnoreCase(itemType)) {
			typeValue = DXLUtil.getProjectNo(itemValue, itemNum);
		}

		// substr
		else if ("substr".equalsIgnoreCase(itemType)) {
			typeValue = DXLUtil.getSubstr(itemValue, itemNum);
		}
		// charFull
		else if ("charFull".equalsIgnoreCase(itemType)) {
			typeValue = DXLUtil.getFullChar(itemValue);
		}
		// money
		else if ("money".equalsIgnoreCase(itemType)) {
			typeValue = DXLUtil.getMoney(itemValue);
		}
		// multiple: 數值欄位 x (num) 回傳
		else if ("multiple".equalsIgnoreCase(itemType)) {
			typeValue = DXLUtil.getMultiple(itemValue, itemNum);
		}

		// fillLeftZero
		else if ("fillLeftZero".equalsIgnoreCase(itemType)) {
			typeValue = DXLUtil.getFillLeftZero(itemValue, itemNum);
		}
		// grade
		else if ("grade".equalsIgnoreCase(itemType)) {
			typeValue = DXLUtil.getGrade(itemValue);
		}
		// otherCurr
		else if ("otherCurr".equalsIgnoreCase(itemType)) {
			typeValue = DXLUtil.getOtherCurr(itemValue);
		}
		// plusReason
		else if ("plusReason".equalsIgnoreCase(itemType)) {
			typeValue = DXLUtil.getPlusReason(itemValue);
		}
		// 類別
		else if ("rateType".equalsIgnoreCase(itemType)) {
			typeValue = DXLUtil.codeConvert(itemType, itemValue);
		}
		// reRate
		else if ("reRate".equalsIgnoreCase(itemType)) {
			typeValue = DXLUtil.getReRate(itemValue, itemNum);
		}
		// 現行額度-是否循環使用
		else if ("reUse".equalsIgnoreCase(itemType)) {
			typeValue = DXLUtil.codeConvert(itemType, itemValue);
		}
		// ref
		else if ("ref".equalsIgnoreCase(itemType)) {
			typeValue = DXLUtil.getRef(itemValue);
		}

		// 無 | 0 回傳2
		// 借款人 | Y 回傳1
		// 本行 | N 回傳0
		// 若以上皆非，請回傳原值
		else if ("usdRateTax".equalsIgnoreCase(itemType)) {
			typeValue = DXLUtil.getUsdRateTax(itemValue);
		}
		// 登記(實收)資本額-（單位）
		else if ("unit".equalsIgnoreCase(itemType)) {
			typeValue = DXLUtil.codeConvert(itemType, itemValue);
		}
		// 合計金額調整註記
		else if ("valueTune".equalsIgnoreCase(itemType)) {
			typeValue = DXLUtil.codeConvert(itemType, itemValue);
		}
		// 是否轉換YN
		else if ("yn".equalsIgnoreCase(itemType)) {
			typeValue = DXLUtil.getYesNo(itemValue);
		}
		// 不是Y的轉N
		else if ("YesNo".equalsIgnoreCase(itemType)) {
			typeValue = DXLUtil.getYesNo1(itemValue);
		}
		// Yn1
		else if ("Yn1".equalsIgnoreCase(itemType)) {
			typeValue = DXLUtil.getYn1(itemValue);
		}
		// 有無xxxxx與本行有利害關係人(者)
		else if ("Yn3".equalsIgnoreCase(itemType)) {
			typeValue = DXLUtil.codeConvert(itemType, itemValue);
		}
		// 應收帳款承購無追索權－買方，有無銀行法所稱與本行有利害關係人
		else if ("Yn5".equalsIgnoreCase(itemType)) {
			typeValue = DXLUtil.codeConvert(itemType, itemValue);
		}
		// ----日期類
		// cdate
		else if ("cdate".equalsIgnoreCase(itemType)) {
			typeValue = DXLUtil.parseRocDate(itemValue);
		}
		// YMD
		else if ("YMD".equalsIgnoreCase(itemType)) {
			typeValue = DXLUtil.getYMD(itemValue, itemNum);
		}
		// ym2ymd
		else if ("ym2ymd".equalsIgnoreCase(itemType)) {
			typeValue = DXLUtil.getYM2YMD(itemValue);
		}
		// sysdate
		else if ("sysdate".equalsIgnoreCase(itemType)) {
			typeValue = Util.getCurrentTimestamp();
		}
		// TODAY
		// 格式為NMMDDS
		// N：1碼可變更之英文字，目前請設為N，以免跟現行行員編號重複
		// MM:兩碼月
		// DD:兩碼日
		// S:1碼序號1~9、A~Z
		else if ("today".equalsIgnoreCase(itemType)) {
			typeValue = MainConfig.getInstance().getConfig().getTODAY();
		}
		return typeValue;
	}

}
