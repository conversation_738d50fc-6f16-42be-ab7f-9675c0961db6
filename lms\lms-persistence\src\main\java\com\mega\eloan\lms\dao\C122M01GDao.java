/* 
 * C122M01GDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import com.mega.eloan.lms.model.C122M01G;

import tw.com.iisi.cap.dao.IGenericDao;


/** 進件管理資料檔 **/
public interface C122M01GDao extends IGenericDao<C122M01G> {

	C122M01G findByOid(String oid);
	
	List<C122M01G> findByMainId(String mainId);
	
	C122M01G findByUniqueKey(String mainId);

	List<C122M01G> findByIndex01(String mainId);

    /**
     * 取得尚未被送至FTP Server之記錄
     *
     * @return List<C122M01G>
     */
	List<C122M01G> findOnlineCasesUnsentToJCICForIxmlCertifcate();

    /**
     * 取得指定送檔至FTP Server之記錄(補執行批次用)
     * @param startDate 起時-c122m01g.createTime(yyyy-MM-dd mm:ss:00)
     * @param endDate 迄時-c122m01g.createTime(yyyy-MM-dd mm:ss:59)
     * @param jcicStatusCode 指定之聯徵回傳狀態碼
     * @param onlyUnsent true-僅送出未被送出的
     *
     * @return List<C122M01G>
     */
	List<C122M01G> findOnlineCasesToRetryIxmlCertifcate(String startDate, String endDate, String jcicStatusCode, Boolean containUnsent);
	
	/**
	 * 取得未發查資料或發查失敗資料
	 * @return
	 */
	List<C122M01G> findUndoneQueryJcic(String startDate, String endDate);
	
	/**
	 * 取得已發查未回覆或重新發查資料
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	List<C122M01G> findDoneQueryJcic(String startDate, String endDate);
}