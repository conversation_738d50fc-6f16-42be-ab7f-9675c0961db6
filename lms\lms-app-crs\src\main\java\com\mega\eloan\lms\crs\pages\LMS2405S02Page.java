/* 
 * LMS2405M01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.crs.pages;

import com.iisigroup.cap.component.PageParameters;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;



import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.lms.crs.panels.LMS2415S01Panel;
import com.mega.eloan.lms.crs.panels.LMS2415S02Panel;

/**
 * <pre>
 * 覆審名單明細(報告表)
 * </pre>
 * 
 * @since 2012/04/19
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/04/19,irene
 *          </ul>
 */
@Controller
@RequestMapping("/crs/lms2405s02/{page}")

public class LMS2405S02Page extends AbstractEloanForm {
	public LMS2405S02Page() {
		super();
	}
	
	@Override
	public void execute(ModelMap model, PageParameters params) {
		new LMS2415S01Panel("tabsa-01").processPanelData(model, params);
		new LMS2415S02Panel("tabsa-02").processPanelData(model, params);
	}

	@Override
	public void afterExecute(ModelMap model, PageParameters parameters) {
		super.afterExecute(model, parameters);
		model.addAttribute("showHeader", false); // 不顯示 _headerPanel

	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return null;
	}

}
