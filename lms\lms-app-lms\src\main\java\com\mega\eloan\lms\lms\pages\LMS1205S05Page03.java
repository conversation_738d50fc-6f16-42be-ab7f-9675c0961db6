package com.mega.eloan.lms.lms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;

/**
 * <pre>
 * 說明(企金授權外) - 營運概況
 * </pre>
 * 
 * @since 2012/1/19
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/19,<PERSON>,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms1205S05C")
public class LMS1205S05Page03 extends AbstractEloanForm {

	@Override
	public void execute(ModelMap model, PageParameters params) throws Exception {
		
		super.execute(model, params);
		renderJsI18N(LMS1205S05Page03.class);
	}

	@Override
    public String getViewName() {
        return "common/pages/None";
    }

	private static final long serialVersionUID = 1L;

	@Override
	public Class<? extends Meta> getDomainClass() {
		return null;
	}
}
