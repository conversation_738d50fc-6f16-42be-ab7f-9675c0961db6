---------------------------------------------------------
-- LMS.L140M01N 額度利率結構利率化明細檔
---------------------------------------------------------
---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.L140M01N;
CREATE TABLE LMS.L140M01N (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)      not null,
	RATESEQ       DECIMAL(5,0)  not null,
	RATETY<PERSON><PERSON>      CHAR(1)       not null,
	SECNO         VARCHAR (2)  ,
	<PERSON>NOOP       CHAR(1)      ,
	SECBEGMON     DECIMAL(3,0) ,
	<PERSON><PERSON><PERSON><PERSON>     DECIMAL(3,0) ,
	SECBEGDATE    DATE         ,
	SECENDDATE    DATE         ,
	RATEBASE      VARCHAR(60)  ,
	RATESETALL    CHAR(1)      ,
	GROUPNAME     VARCHAR(120) ,
	TRATEMIN      DECIMAL(7,5) ,
	TRATEMAX      DECIMAL(7,5) ,
	CTRATEMIN     DECIMAL(7,5) ,
	CTRATEMAX     DECIMAL(7,5) ,
	PRRATE        VARCHAR(3)   ,
	RATEPERIOD    VARCHAR(300) ,
	DISYEAROP     CHAR(1)      ,
	DISYEARRATE   DECIMAL(7,5) ,
	RERATEMIN     DECIMAL(7,5) ,
	RERATEMAX     DECIMAL(7,5) ,
	RERATESELALL  CHAR(1)      ,
	ATTRATE       DECIMAL(7,5) ,
	OTHERRATEDRC  VARCHAR(3072),
	USDMARKET     CHAR(1)      ,
	USDSETALL     CHAR(1)      ,
	USDMARKETRATE VARCHAR(30)  ,
	USDRATETAX    CHAR(1)      ,
	USDINSIDERATE DECIMAL(7,5) ,
	USDDESRATE    DECIMAL(7,5) ,
	RATEMEMO      VARCHAR(3072),
	RATEKIND      CHAR(1)      ,
	RATEGETINT    CHAR(1)      ,
	RATECHGKIND   CHAR(1)      ,
	RATECHG1      DECIMAL(2,0) ,
	RATECHGDATE   DATE         ,
	UIONMEMO      VARCHAR(4096),
	RATETAX       CHAR(1)      ,
	RATETAXCODE   DECIMAL(7,5) ,
	RATELIMITTYPE CHAR(2)      ,
	RATELIMITRATE DECIMAL(7,5) ,
	RATELIMIT     CHAR(1)      ,
	RATELIMITCODE CHAR(2)      ,
	RATELIMITSETALL CHAR(1)      ,
	RATELIMITMARKET VARCHAR(120) ,
	RATELIMITCOUNTTYPE CHAR(1)      ,
	RATELIMITCOUNTRATE DECIMAL(7,5) ,
	RATELIMITCOUNTPR DECIMAL(7,5) ,
	RATELIMITTAX  CHAR(1)      ,
	RATELIMITTAXRATE DECIMAL(7,5) ,
	RATEDSCR      VARCHAR(4096),
	UPRATEDSCR    VARCHAR(4096),
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_L140M01N PRIMARY KEY(OID)
) IN EL_DATA_32KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XL140M01N01;
CREATE  INDEX LMS.XL140M01N01 ON LMS.L140M01N   (MAINID, RATESEQ, RATETYPE);


---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.L140M01N IS '額度利率結構利率化明細檔';
COMMENT ON LMS.L140M01N (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	RATESEQ       IS '序號', 
	RATETYPE      IS '類別', 
	SECNO         IS '段數', 
	SECNOOP       IS '適用期間選項', 
	SECBEGMON     IS '適用期間起月', 
	SECENDMON     IS '適用期間迄月', 
	SECBEGDATE    IS '適用期間起日', 
	SECENDDATE    IS '適用期間迄日', 
	RATEBASE      IS '利率基礎', 
	RATESETALL    IS '利率基礎-是否以借款同天期顯示文字', 
	GROUPNAME     IS '利率基礎-貨幣市場利率群組', 
	TRATEMIN      IS '利率基礎-牌告利率-最小值', 
	TRATEMAX      IS '利率基礎-牌告利率-最大值', 
	CTRATEMIN     IS '利率基礎-牌告利率-最小值(修改值)', 
	CTRATEMAX     IS '利率基礎-牌告利率-最大值(修改值)', 
	PRRATE        IS '利率基礎-自訂利率參考指標-指標名稱', 
	RATEPERIOD    IS '利率基礎-自訂利率天期', 
	DISYEAROP     IS '利率基礎-加減年利率選項', 
	DISYEARRATE   IS '利率基礎-加減年利率(%)', 
	RERATEMIN     IS '利率基礎-敘做利率最小值(%)', 
	RERATEMAX     IS '利率基礎-敘做利率最大值(%)', 
	RERATESELALL  IS '利率基礎-敘做利率(%) 組合文字時顯示此項目', 
	ATTRATE       IS '利率基礎-固定利率(%)', 
	OTHERRATEDRC  IS '利率基礎-其他(自行輸入)', 
	USDMARKET     IS '自訂利率參考指標U01、U02專用-與借款人敘做之市場利率代碼', 
	USDSETALL     IS '自訂利率參考指標U01、U02專用-是否以借款同天期顯示', 
	USDMARKETRATE IS '自訂利率參考指標U01、U02專用-(A)與借款人敘作之市場利率', 
	USDRATETAX    IS '自訂利率參考指標U01、U02專用-(B)稅賦負擔', 
	USDINSIDERATE IS '自訂利率參考指標U01、U02專用-(C)內含', 
	USDDESRATE    IS '自訂利率參考指標U01、U02專用-(D)S/LBOR與TAIFX差額逾', 
	RATEMEMO      IS '利率補充說明', 
	RATEKIND      IS '利率方式', 
	RATEGETINT    IS '收息方式', 
	RATECHGKIND   IS '利率變動方式', 
	RATECHG1      IS '變動週期', 
	RATECHGDATE   IS '指定下次變動日期', 
	UIONMEMO      IS '聯貸案專用說明', 
	RATETAX       IS '稅負洽收', 
	RATETAXCODE   IS '稅負洽收-扣稅負擔', 
	RATELIMITTYPE IS '限制條件/說明', 
	RATELIMITRATE IS '限制條件利率', 
	RATELIMIT     IS '下限利率', 
	RATELIMITCODE IS '下限利率-利率代碼', 
	RATELIMITSETALL IS '下限利率-是否以借款同天期顯示文字', 
	RATELIMITMARKET IS '下限利率-貨幣市場利率群組', 
	RATELIMITCOUNTTYPE IS '下限利率-加減年利率選項', 
	RATELIMITCOUNTRATE IS '下限利率-加減年利率(%)', 
	RATELIMITCOUNTPR IS '下限利率-自訂利率', 
	RATELIMITTAX  IS '下限利率-稅負負擔選項', 
	RATELIMITTAXRATE IS '下限利率-稅負負擔年率(%)', 
	RATEDSCR      IS '組成說明字串', 
	UPRATEDSCR    IS '上傳用文字', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
