/* 
 * CLS1131S01MPanel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.iisigroup.cap.utils.CapAppContext;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * <pre>
 * 個金徵信作業
 * </pre>
 * 
 * @since 2012/10/11
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/10/11,Fantasy,new
 *          </ul>
 */
public class CLS1131S01MPanel extends Panel {

	private static final long serialVersionUID = 1L;

	private CLSService clsService;
	
	
	/**
	 * @param id
	 */
	public CLS1131S01MPanel(String id) {
		super(id);
		this.clsService = CapAppContext.getBean("CLSService");
	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);
		
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		boolean HS_MARKMODEL_3 = clsService.doCardLoanBr(user.getUnitNo());
		model.addAttribute("HS_MARKMODEL_3", HS_MARKMODEL_3);
	}
}
