package com.mega.eloan.lms.base.pages;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.pages.AbstractOutputPage;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.model.L120M01A;

import tw.com.jcs.common.Util;

/**
 * <pre>
 * 相關文件-土建融案查詢分頁
 * </pre>
 * 
 * @since 2012/11/2
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/11/2,<PERSON>,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lmss08e/{page}")
public class LMSS08EPage extends AbstractOutputPage {

	@Autowired
	L120M01ADao l120m01aDao;

	@Override
	public String getOutputString(ModelMap model, PageParameters params) {
		String docType = Util.trim(params.getString("docType"));
		setNeedHtml(true);		// need html
		if(Util.isEmpty(docType)){
			String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
			L120M01A meta = l120m01aDao.findByMainId(mainId);
			if(meta == null){
				meta = new L120M01A();
			}
			docType = Util.trim(meta.getDocType());
		}
		
		if(UtilConstants.Casedoc.DocType.個金.equals(docType)){
			setJavascript(new String[] { "pagejs/cls/CLSS08EPage.js" });
		}else{
			setJavascript(new String[] { "pagejs/lns/LMSS08EPage.js" });
		}		
		return "&nbsp;";
	}

	@Override
	protected String getViewName() {
		// UPGRADE: 待確認是否一樣換成Thymeleaf的None.html頁面
		return "common/pages/None";
	}
}
