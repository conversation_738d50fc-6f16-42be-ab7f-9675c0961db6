/* 
 * CapMapGridResult.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package tw.com.iisi.cap.response;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.ServletResponse;

import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.iisigroup.cap.component.PageParameters;
import com.iisigroup.cap.component.impl.StringResponse;

import tw.com.iisi.cap.enums.IGridEnum;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.formatter.ADDateFormatter;
import tw.com.iisi.cap.formatter.ADDateTimeFormatter;
import tw.com.iisi.cap.formatter.IBeanFormatter;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.util.CapString;

/**
 * <pre>
 * Grid Result
 * 設置Grid內容
 * </pre>
 * 
 * @since 2011/10/26
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2011/10/26,iristu,new
 *          <li>2011/03/28,sunkist,update callback
 *          </ul>
 */
public class CapMapGridResult implements IGridResult<CapMapGridResult, Map<String, Object>> {

    private static Logger logger = LoggerFactory.getLogger(CapMapGridResult.class);

    /**
     * Grid資料內容
     */
    protected JSONObject resultMap;

    /**
     * 資料列
     */
    protected List<? extends Map<String, Object>> rowData;

    /**
     * 欄位
     */
    protected String[] columns;

    /**
     * 資料格式化
     */
    protected Map<String, IFormatter> dataReformatter;

    /**
     * 建構子
     */
    public CapMapGridResult() {
        resultMap = new JSONObject();
    }

    /**
     * 建構子
     * 
     * @param rowData
     *            資料列
     * @param records
     *            資料總筆數
     */
    public CapMapGridResult(List<Map<String, Object>> rowData, int records) {
        this(rowData, records, null);
    }

    /**
     * 建構子
     * 
     * @param rowData
     *            資料列
     * @param records
     *            資料總筆數
     * @param dataReformatter
     *            資料格式化
     */
    public CapMapGridResult(List<Map<String, Object>> rowData, int records, Map<String, IFormatter> dataReformatter) {
        resultMap = new JSONObject();
        setRowData(rowData);
        setRecords(records);
        setDataReformatter(dataReformatter);
    }

    /**
     * <pre>
     * 設定頁碼
     * </pre>
     * 
     * @param page
     *            頁碼
     * @return this
     */
    public CapMapGridResult setPage(int page) {
        resultMap.put(IGridEnum.PAGE.getCode(), page);
        resultMap.put("page", page); // 回傳前端jqgrid仍是讀取「page」的參數
        return this;
    }// ;

    /**
     * <pre>
     * 設定總頁數
     * </pre>
     * 
     * @param rowCount
     *            總筆數
     * @param pageRows
     *            一頁筆數
     * @return this
     */
    public CapMapGridResult setPageCount(int rowCount, int pageRows) {
        if (pageRows == 0)
            pageRows = 1;
        resultMap.put(IGridEnum.TOTAL.getCode(), rowCount / pageRows + (rowCount % pageRows > 0 ? 1 : 0));
        return this;
    }// ;

    /**
     * <pre>
     * 設定總筆數
     * </pre>
     * 
     * @param rowCount
     *            總筆數
     * @return this
     */
    public CapMapGridResult setRecords(int rowCount) {
        resultMap.put(IGridEnum.RECORDS.getCode(), rowCount);
        return this;
    }// ;

    /**
     * <pre>
     * 取得總筆數
     * </pre>
     * 
     * @return 總筆數
     */
    public Integer getRecords() {
        Object o = resultMap.get(IGridEnum.RECORDS.getCode());
        return o == null ? 0 : (Integer) o;
    }// ;

    /**
     * <pre>
     * 設定資料行
     * </pre>
     * 
     * @param rowData
     *            資料
     * @return this
     */
    public CapMapGridResult setRowData(List<? extends Map<String, Object>> rowData) {
        this.rowData = rowData;
        return this;
    }

    /*
     * 取得字串資料列
     * 
     * @see tw.com.iisi.cap.response.IResult#getResult()
     */
    @Override
    public String getResult() {
        resultMap.put(IGridEnum.PAGEROWS.getCode(), getRowDataToJSON());
        return resultMap.toString();
    }

    /*
     * 取得頁面Log訊息
     * 
     * @see tw.com.iisi.cap.response.IResult#getLogMessage()
     */
    @Override
    public String getLogMessage() {
        StringBuffer b = new StringBuffer();
        b.append("page=").append(resultMap.get(IGridEnum.PAGE.getCode())).append(",pagerow=").append(resultMap.get(IGridEnum.PAGEROWS.getCode())).append(",rowData=")
                .append(resultMap.get(IGridEnum.PAGEROWS.getCode()));
        return b.toString();
    }

    /*
     * 加入結果資料
     * 
     * @see tw.com.iisi.cap.response.IResult#add(tw.com.iisi.cap.response.IResult)
     */
    @Override
    public void add(IResult result) {
        JSONObject json = JSONObject.fromObject(result);
        resultMap.putAll(json);
    }

    /**
     * 加入 Reformat Data
     * 
     * @param key
     * @param formatter
     * @return
     * @throws CapException
     */
    protected CapMapGridResult addReformatData(String key, IFormatter formatter) throws CapException {
        if (dataReformatter == null) {
            dataReformatter = new HashMap<String, IFormatter>();
        }
        dataReformatter.put(key, formatter);
        return this;
    }

    /**
     * 設定欄位
     */
    public void setColumns(String[] columns) {
        this.columns = columns;
    }

    /**
     * 取得資料行
     */
    public List<? extends Map<String, Object>> getRowData() {
        return this.rowData;
    }

    /**
     * 取得 Data Reformatter
     */
    public Map<String, IFormatter> getDataReformatter() {
        return this.dataReformatter;
    }

    /**
     * 將資料行轉為 JSON array
     * 
     * @return
     */
    protected List<Object> getRowDataToJSON() {
        List<Object> rows = new JSONArray();
        Map<String, Object> row = new HashMap<String, Object>();
        if (rowData != null && !rowData.isEmpty()) {
            for (Map<String, Object> data : rowData) {
                try {
                    row.put(IGridEnum.CELL.getCode(), dataToJsonString(data));
                } catch (CapException e) {
                    logger.error(e.getMessage(), e);
                }
                rows.add(row);
            }
        }
        return rows;
    }

    /**
     * 資料轉成 JSON 字串
     * 
     * @param data
     * @return
     * @throws CapException
     */
    protected List<Object> dataToJsonString(Map<String, Object> data) throws CapException {
        List<Object> row = new JSONArray();
        for (String str : columns) {
            Object val = null;
            try {
                try {
                    /** column split regularre char **/
                    // 20191016,fix checkmarx ReDoS_From_Regex_Injection
                    String[] s = str.split("\\|");
                    val = s.length == 1 ? data.get(s[0]) : data.get(s[1]);
                    str = s[0];
                } catch (Exception e) {
                    val = "";
                }
                if (dataReformatter != null && dataReformatter.containsKey(str)) {
                    IFormatter callback = dataReformatter.get(str);
                    if (callback instanceof IBeanFormatter) {
                        val = callback.reformat(data);
                    } else {
                        val = callback.reformat(val);
                    }
                } else if (val instanceof Timestamp) {
                    val = new ADDateTimeFormatter().reformat(val);
                } else if (val instanceof Date || val instanceof Calendar) {
                    val = new ADDateFormatter().reformat(val);
                }
                row.add(String.valueOf(val));
            } catch (Exception e) {
                throw new CapException(e.getMessage(), e, getClass());
            }
        }
        return row;
    }

    /**
     * set DataReformatter
     * 
     * @param dataReformatter
     *            Map<String, IFormatter>
     */
    public void setDataReformatter(Map<String, IFormatter> dataReformatter) {
        this.dataReformatter = dataReformatter;
    }

    /*
     * 建立字串回應
     * 
     * @see tw.com.iisi.cap.response.IResult#respondResult(javax.servlet.ServletResponse)
     */
    @Override
    public void respondResult(ServletResponse response) throws CapException {
        new StringResponse("text/plain", "utf-8", getResult()).respond(response);
    }

    public String[] getGridColumnsFromParams(PageParameters params){
        JSONArray ja = JSONArray.fromObject(params.getString("_columnParam"));
        String []columns = new String[ja.size()];
        for(int i = 0 ;i<ja.size();i++){
            JSONObject jo = ja.getJSONObject(i);
            columns[i] = CapString.trimNull(jo.optString("name"));
        }
        return columns;
    }

}
