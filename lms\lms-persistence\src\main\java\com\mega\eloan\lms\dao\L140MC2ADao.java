/* 
 * L140MC2ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140MC2A;

/** 比對相同資料檢核明細紀錄檔 **/
public interface L140MC2ADao extends IGenericDao<L140MC2A> {

	L140MC2A findByOid(String oid);
	
	List<L140MC2A> findByMainId(String mainId);
	
	L140MC2A findByUniqueKey(String mainId, String custId, String dupNo);

	void deleteAll(List<L140MC2A> list);
}