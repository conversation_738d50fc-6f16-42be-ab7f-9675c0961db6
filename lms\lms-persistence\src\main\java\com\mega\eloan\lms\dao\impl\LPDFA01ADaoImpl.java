/* 
 * LPDFA01ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.LPDFA01ADao;
import com.mega.eloan.lms.model.LPDFA01A;

/** 授信 PDF 舊案授權檔 **/
@Repository
public class LPDFA01ADaoImpl extends LMSJpaDao<LPDFA01A, String>
	implements LPDFA01ADao {

	@Override
	public LPDFA01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<LPDFA01A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<LPDFA01A> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<LPDFA01A> findByIndex01(String mainId, String ownUnit, String authType, String authUnit){
		ISearch search = createSearchTemplete();
		List<LPDFA01A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (ownUnit != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "ownUnit", ownUnit);
		if (authType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "authType", authType);
		if (authUnit != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "authUnit", authUnit);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
}