/* 
 * VLUSEDOC01DaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.VLUSEDOC01Dao;
import com.mega.eloan.lms.model.VLUSEDOC01;

/** 動用審核表取得額度序號 **/
@Repository
public class VLUSEDOC01DaoImpl extends LMSJpaDao<VLUSEDOC01, String> implements
		VLUSEDOC01Dao {

	@Override
	public List<VLUSEDOC01> findUseCntrNo(String caseMainId, String[] useBrId,
			String itemType) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "caseMainId",
				caseMainId);
		// I-110-0028_05097_B1002 Web e-Loan企金授信額度明細表配合進出口業務集中化修改小行可以敘作大行動審表
		search.addSearchModeParameters(SearchMode.IN, "useBrId", useBrId);
		search.addSearchModeParameters(SearchMode.EQUALS, "itemType", itemType);
		search.setMaxResults(Integer.MAX_VALUE);
		List<VLUSEDOC01> list = createQuery(VLUSEDOC01.class, search)
				.getResultList();
		return list;
	}

	@Override
	public List<VLUSEDOC01> findUseCntrNo(String caseMainId, String[] cntrNos,
			String[] useBrId, String itemType) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "caseMainId",
				caseMainId);
		search.addSearchModeParameters(SearchMode.IN, "useCntrNo", cntrNos);
		// I-110-0028_05097_B1002 Web e-Loan企金授信額度明細表配合進出口業務集中化修改小行可以敘作大行動審表
		search.addSearchModeParameters(SearchMode.IN, "useBrId", useBrId);
		if (itemType != null) {
			search.addSearchModeParameters(SearchMode.EQUALS, "itemType",
					itemType);
		}
		search.setMaxResults(Integer.MAX_VALUE);
		List<VLUSEDOC01> list = createQuery(VLUSEDOC01.class, search)
				.getResultList();
		return list;
	}

	@Override
	public List<VLUSEDOC01> findByCustIdDupId(String custId, String DupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", DupNo);
		List<VLUSEDOC01> list = createQuery(VLUSEDOC01.class, search)
				.getResultList();
		return list;
	}

}