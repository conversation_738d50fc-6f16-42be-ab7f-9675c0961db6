package com.mega.eloan.lms.lrs.report.impl;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONObject;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.inet.report.ReportException;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.LrsUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.dao.L170M01DDao;
import com.mega.eloan.lms.lrs.constants.lrsConstants;
import com.mega.eloan.lms.lrs.pages.LMS1700M01Page;
import com.mega.eloan.lms.lrs.report.LMS1700R01RptService;
import com.mega.eloan.lms.model.L170M01A;
import com.mega.eloan.lms.model.L170M01B;
import com.mega.eloan.lms.model.L170M01C;
import com.mega.eloan.lms.model.L170M01D;
import com.mega.eloan.lms.model.L170M01E;
import com.mega.eloan.lms.model.L170M01F;
import com.mega.eloan.lms.model.L170M01G;
import com.mega.eloan.lms.model.L170M01H;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.PdfTools;
import tw.com.jcs.common.report.ReportGenerator;

@Service("lms1700r01rptservice")
public class LMS1700R01RptServiceImpl extends AbstractFormHandler implements
		FileDownloadService, LMS1700R01RptService {
	// 復原TFS J-111-0636_05097_B100X
	private static final int GRID_CNT = 2;
	private static final String OFF_BOX = "□";
	private static final String ON_BOX = "■";
	private static final String DEFAULT_FMT = "Y|N";

	@Resource
	RetrialService retrialService;

	@Resource
	BranchService branchService;

	@Resource
	CodeTypeService codeTypeService;

	@Resource
	LMSService lmsService;
	
	@Resource
	L170M01DDao l170m01dDao;

	@Override
	public byte[] getContent(PageParameters params) throws CapException,
			FileNotFoundException, ReportException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
			baos = (ByteArrayOutputStream) this.generateReport(params);
			return baos.toByteArray();
		} finally {
			if (baos != null) {
				baos.close();
			}

		}
	}

	@Override
	public String get_L170M01D_ITEMCONTENTS(List<L170M01D> l170m01d_list) {
		Properties prop_lms1700m01 = MessageBundleScriptCreator
				.getComponentResource(LMS1700M01Page.class);
		return _get_L170M01D_ITEMCONTENTS(l170m01d_list, prop_lms1700m01);
	}

	private String _get_L170M01D_ITEMCONTENTS(List<L170M01D> l170m01d_list,
			Properties prop_lms1700m01) {
		ArrayList<String> strList = new ArrayList<String>();
		boolean itemNotice = false;

		for (L170M01D l170m01d : l170m01d_list) {

			// J-108-0888_05097_B1001
			String strSeq = "";

			if (LrsUtil.compareRptVersion(l170m01d.getL170m01a().getRptId(),
					">=", LrsUtil.V_20190701)) {
				strSeq = Util.trim(l170m01d.getItemSeqShow());
			} else {
				strSeq = String.valueOf(l170m01d.getItemSeq());
			}

			// J-108-0128_05097_B1001 Web e-Loan企金授信覆審系統修改覆審報告表內容。
			if (LrsUtil.compareRptVersion(l170m01d.getL170m01a().getRptId(),
					">=", LrsUtil.V_20190701)
					&& Util.equals(LrsUtil.N029, l170m01d.getItemNo())) {
				// if (Util.equals(LrsUtil.V_20190201, l170m01d.getL170m01a()
				// .getRptId())
				// && Util.equals(LrsUtil.N029, l170m01d.getItemNo())) {
				// J-107-0290_09301_B1002 CTLTYPE_主辦覆審 第18項拆項 N025
				// 報表版本為 Ver20190201 且項目含有N029 印註明內容時要注意項次

				// 改成用ITEMSEQSHOW 來顯示項次
				itemNotice = false;
			} else if (LrsUtil.compareRptVersion(l170m01d.getL170m01a()
					.getRptId(), ">=", LrsUtil.V_20190201)
					&& Util.equals(LrsUtil.N029, l170m01d.getItemNo())) {
				// if (Util.equals(LrsUtil.V_20190201, l170m01d.getL170m01a()
				// .getRptId())
				// && Util.equals(LrsUtil.N029, l170m01d.getItemNo())) {
				// J-107-0290_09301_B1002 CTLTYPE_主辦覆審 第18項拆項 N025
				// 報表版本為 Ver20190201 且項目含有N029 印註明內容時要注意項次
				itemNotice = true;
			}

			if (Util.equals(LrsUtil.N008, l170m01d.getItemNo())) {
				L170M01A l170m01a = retrialService.findL170M01A_mainId(l170m01d
						.getMainId());
				if (LrsUtil.compareRptVersion(l170m01a.getRptId(), ">=",
						LrsUtil.V_20220401)) {
					// J-111-0031 更動覆審系統內以下九式覆審報告表之文字內容。
					// Z_DESC_N008_20220401
					// 註明撥貸入借戶指定存款帳戶後之資金流向...
					// ，並影印資金流向資料存覆審卷備查或確認E-LOAN授信管理系統貸後管理追蹤檢核表已存載。
					strList.add("※第" + strSeq + "項應"
							+ LrsUtil.Z_DESC_N008_20220401 + "。");
				} else if (LrsUtil.compareRptVersion(l170m01a.getRptId(), ">=",
						LrsUtil.V_20170603)) {
					// Z_DESC_N008_20170603 =
					// "註明撥貸入借戶指定存款帳戶後之資金流向，敘明撥貸資金轉出或匯出之借貸交易帳戶和金額是否符合申貸用途之詳情，並影印資金流向資料存覆審卷備查";
					strList.add("※第" + strSeq + "項應"
							+ LrsUtil.Z_DESC_N008_20170603 + "。");
				} else {
					// 註明調閱傳票、相關資料及資金流向詳情
					strList.add("※第" + strSeq + "項應" + LrsUtil.Z_DESC_N008
							+ "。");
				}

			}

			if (Util.equals(LrsUtil.N011, l170m01d.getItemNo())) {
				L170M01A l170m01a = retrialService.findL170M01A_mainId(l170m01d
						.getMainId());

				if (Util.equals(l170m01a.getRealRpFg(), "Y")
						&& LrsUtil.compareRptVersion(l170m01a.getRptId(), ">=",
								LrsUtil.V_20170603)) {
					// 土建融案件實地覆審結果，應於「附表」勾選檢視事項
					strList.add("※第" + strSeq + "項：" + LrsUtil.X_DESC_N011
							+ "。");
				}

			}

			if (Util.equals(LrsUtil.N027, l170m01d.getItemNo())) {
				// 除須查核各項授信電腦建檔資料覆核是否確實外，應於「附表」勾選所列項目之建檔資料是否正確
				// J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
				L170M01A l170m01a = retrialService.findL170M01A_mainId(l170m01d
						.getMainId());
				String rptId = "";
				if (l170m01a != null) {
					rptId = Util.trim(l170m01a.getRptId());
				}

				if (LrsUtil.compareRptVersion(rptId, ">=", LrsUtil.V_20190701)) {
					strList.add("※第"
							+ String.valueOf(retrialService
									.getItemSeqShowByItemNo(rptId, LrsUtil.N027))
							+ "及"
							+ String.valueOf(retrialService
									.getItemSeqShowByItemNo(rptId, LrsUtil.N015))
							+ "項：" + LrsUtil.Z_DESC_N027_PRT
							+ LrsUtil.Z_DESC_N015_PRT + "。");
				} else if (LrsUtil.compareRptVersion(rptId, ">=",
						LrsUtil.V_20161101)) {
					strList.add("※第"
							+ String.valueOf(retrialService.getItemSeqByItemNo(
									rptId, LrsUtil.N027))
							+ "及"
							+ String.valueOf(retrialService.getItemSeqByItemNo(
									rptId, LrsUtil.N015)) + "項："
							+ LrsUtil.Z_DESC_N027_PRT + LrsUtil.Z_DESC_N015_PRT
							+ "。");
				} else {
					strList.add("※第" + strSeq + "項" + LrsUtil.Z_DESC_N027_PRT
							+ "。");
				}

			}

			if (Util.equals(LrsUtil.B004_ref_N003, l170m01d.getItemNo())) {
				strList.add("第" + strSeq + "項：借戶或關係人營運或財務情況若有大幅變動或異常應作說明");
			}
			if (Util.equals(LrsUtil.B011_ref_N015, l170m01d.getItemNo())) {
				strList.add("第" + strSeq
						+ "項：核定條件有財務承諾或一般承諾，應敘明借戶是否依核定條件辦理之詳情。");
			}
		}
		String _prefix = "";
		for (L170M01D l170m01d : l170m01d_list) {

			// J-108-0888_05097_B1001
			String strSeq = "";
			if (Util.notEquals(Util.trim(l170m01d.getItemSeqShow()), "")) {
				strSeq = Util.trim(l170m01d.getItemSeqShow());
			} else {
				strSeq = String.valueOf(l170m01d.getItemSeq());
			}

			if (Util.equals(LrsUtil.Z_電腦建檔資料, l170m01d.getItemType())) {
				// 暫先不抓 Z_電腦建檔
				continue;
			}
			// J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
			if (Util.equals(LrsUtil.Y_履約條件, l170m01d.getItemType())) {
				// 暫先不抓 Y_履約條件
				continue;
			}

			// J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
			if (Util.equals(LrsUtil.X_土建融, l170m01d.getItemType())) {
				// 暫先不抓 X_土建融
				continue;
			}

			String chkText = Util.trim(l170m01d.getChkText());
			if (true) {
				/*
				 * TODO 消金覆審的 itemContents 由 PlainText 改成 HTML(進階)，企金覆審仍維持
				 * PlainText
				 */
			}

			if (Util.equals(LrsUtil.N012, l170m01d.getItemNo())) {
				if (Util.isNotEmpty(l170m01d.getChkText())) {
					// 註明實際工程進度/或履約情形
					strList.add(_prefix + strSeq + "項." + LrsUtil.Z_DESC_N012
							+ "：" + chkText);
				}
			} else if (Util.equals(LrsUtil.N025, l170m01d.getItemNo())
					|| Util.equals(LrsUtil.N029, l170m01d.getItemNo())) {
				// J-107-0290_09301_B1001 CTLTYPE_主辦覆審 第18項拆項 N025
				if (Util.isNotEmpty(l170m01d.getChkText())) {
					// 註明處理情形
					strList.add(_prefix
							+ (Util.equals(LrsUtil.N029, l170m01d.getItemNo()) ? "18"
									: strSeq) + "項."
							+ LrsUtil.Z_DESC_N025_PRINT + "：" + chkText);
				}
			} else if (Util.equals(LrsUtil.N017, l170m01d.getItemNo())
					|| Util.equals(LrsUtil.B015_N017, l170m01d.getItemNo())) {
				// J-106-0145-006 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
				String preStr = "";
				if (Util.equals("Y", l170m01d.getChkPreReview())) {
					preStr = prop_lms1700m01.getProperty("label.Y3.content");
				} else if (Util.equals("N", l170m01d.getChkPreReview())) {
					preStr = prop_lms1700m01.getProperty("label.N3.content");
				}

				if (Util.isNotEmpty(l170m01d.getChkText())
						|| Util.isNotEmpty(preStr)) {
					if (itemNotice) {
						strList.add(_prefix + (l170m01d.getItemSeq() - 1)
								+ "項." + preStr + chkText);
					} else {
						strList.add(_prefix + strSeq + "項." + preStr + chkText);
					}
				}
			} else if (LrsUtil.compareRptVersion(l170m01d.getL170m01a()
					.getRptId(), ">=", LrsUtil.V_20190701)
					&& (Util.equals(LrsUtil.N029, l170m01d.getItemNo()) || Util
							.equals(LrsUtil.N031, l170m01d.getItemNo()))) {

				// 拆項要特別處理
				String mainItemSeq = "";
				if (Util.equals(LrsUtil.N029, l170m01d.getItemNo())) {

				} else if (Util.equals(LrsUtil.N031, l170m01d.getItemNo())) {

				}
				if (Util.isNotEmpty(l170m01d.getChkText())) {
					strList.add(_prefix + strSeq + "項." + chkText);
				}

			} else {
				// J-107-0290_09301_B1002 CTLTYPE_主辦覆審 第18項拆項 N025
				// 報表版本為 Ver20190201 且項目含有N029 印註明內容時要注意項次
				if (itemNotice) {
					int itemSeqValue = l170m01d.getItemSeq();
					if (itemSeqValue >= 20) {
						if (Util.isNotEmpty(l170m01d.getChkText())) {
							strList.add(_prefix + (itemSeqValue - 1) + "項."
									+ chkText);
						}
					} else {
						if (Util.isNotEmpty(l170m01d.getChkText())) {
							strList.add(_prefix + l170m01d.getItemSeq() + "項."
									+ chkText);
						}
					}
				} else {
					if (Util.isNotEmpty(l170m01d.getChkText())) {
						strList.add(_prefix + strSeq + "項." + chkText);
					}
				}
			}
		}
		return StringUtils.join(strList, "\n\n");
	}

	private String _l170m01h_GN(L170M01A meta, boolean hasL170M01B) {
		if (Util.equals("Y", meta.getFreeG())) {
			L170M01H l170m01h_freeG = retrialService
					.findL170M01H_fmtFreeG_l170m01a(meta);
			return Util.trim(l170m01h_freeG != null ? l170m01h_freeG
					.getCustName() : "");
		} else {
			List<L170M01H> l170m01h_list = retrialService
					.findL170M01H_fmtSYS_debTypeGN(meta);
			boolean hasCntrNo = CollectionUtils.isNotEmpty(l170m01h_list);
			if (hasCntrNo == false) {
				hasCntrNo = hasL170M01B;
			}
			return LrsUtil.toStr_Guarantor(l170m01h_list, hasCntrNo);
		}
	}

	private String _l170m01h_C(L170M01A meta, boolean hasL170M01B) {
		if (Util.equals("Y", meta.getFreeC())) {
			L170M01H l170m01h_freeC = retrialService
					.findL170M01H_fmtFreeC_l170m01a(meta);
			return Util.trim(l170m01h_freeC != null ? l170m01h_freeC
					.getCustName() : "");
		} else {
			List<L170M01H> l170m01h_list = retrialService
					.findL170M01H_fmtSYS_debTypeC(meta);
			boolean hasCntrNo = CollectionUtils.isNotEmpty(l170m01h_list);
			if (hasCntrNo == false) {
				hasCntrNo = hasL170M01B;
			}
			return LrsUtil.toStr_Borrower(l170m01h_list, hasCntrNo);
		}
	}

	private OutputStream generateReport(PageParameters params)
			throws IOException, Exception {

		List<InputStream> list = new LinkedList<InputStream>();
		String[] dataSplit = Util.trim(params.getString("rptOid")).split("\\|");
		String param_l5DescFlag = Util.trim(params.getString("l5DescFlag"));
		boolean printR03 = true;
		if (Util.equals("N", params.getString("printR03"))) {
			printR03 = false;
		}
		OutputStream outputStream = null;
		Locale locale = null;

		Properties prop_abstractEloan = MessageBundleScriptCreator
				.getComponentResource(AbstractEloanPage.class);
		Properties prop_lms1700m01 = MessageBundleScriptCreator
				.getComponentResource(LMS1700M01Page.class);
		try {
			locale = LMSUtil.getLocale();
			Properties propEloanPage = MessageBundleScriptCreator
					.getComponentResource(AbstractEloanPage.class);

			for (String temp : dataSplit) {
				Map<InputStream, Integer> pdfNameMap = new LinkedHashMap<InputStream, Integer>();
				outputStream = null;
				String oid = temp.split("\\^")[0];
				L170M01A meta = retrialService.findL170M01A_oid(oid);
				List<L170M01B> l170m01b_list = retrialService
						.findL170M01B_orderBy(meta);
				L170M01C l170m01c = meta.getL170m01c();
				if (l170m01c == null) {
					l170m01c = new L170M01C();// notes 轉入的資料，可能是null
				}
				List<L170M01D> l170m01d_list = retrialService
						.findL170M01D_orderBySeq(meta);
				Map<String, List<L170M01E>> m_l170m01e = retrialService
						.findL170M01E_type(retrialService.findL170M01E(meta));
				L170M01F l170m01f = meta.getL170m01f();
				if (l170m01f == null) {
					l170m01f = new L170M01F();// notes 轉入的資料，可能是null
				}
				String l170m01h_GN = _l170m01h_GN(meta,
						CollectionUtils.isNotEmpty(l170m01b_list));
				String l170m01h_C = _l170m01h_C(meta,
						CollectionUtils.isNotEmpty(l170m01b_list));
				List<L170M01G> l170m01g_list = retrialService
						.findL170M01G_l170m01a(meta);

				int subLine = 13;
				if (true) {
					String l5DescFlag = param_l5DescFlag;

					// J-111-0622_05097_B1002 Web
					// e-Loan配合本行授信覆審作業須知111.12.1修訂，修改E-Loan系統企金授信覆審作業系統
					if (Util.isEmpty(l5DescFlag)) {
						if (LMSUtil.isSpecialBranch(meta.getOwnBrId())) {
							// J-111-0622_05097_B1002 Web
							// e-Loan配合本行授信覆審作業須知111.12.1修訂，修改E-Loan系統企金授信覆審作業系統
							// 如果是931覆審的007、201，覆審種類為 CTLTYPE A、C案件，要比照目前931的做法

							if (retrialService
									.isSpecialBranchChgTo931_lrs(meta)) {
								l5DescFlag = "Y";
							} else {
								l5DescFlag = "X";
							}

						} else {
							l5DescFlag = "Y";
						}
					}
					outputStream = genLMS1700R01(locale, meta, l170m01b_list,
							l170m01c, l170m01d_list, m_l170m01e, l170m01f,
							l170m01g_list, l170m01h_GN, l170m01h_C, l5DescFlag,
							prop_abstractEloan, prop_lms1700m01);

					if (outputStream != null) {
						pdfNameMap.put(
								new ByteArrayInputStream(
										((ByteArrayOutputStream) outputStream)
												.toByteArray()), subLine);
					} else {
						pdfNameMap.put(null, subLine);
					}
				}
				if (l170m01b_list.size() > GRID_CNT) {
					outputStream = genLMS1700R02(locale, meta, l170m01b_list,
							m_l170m01e, l170m01h_GN, l170m01h_C,
							prop_abstractEloan, prop_lms1700m01);
					if (outputStream != null) {
						pdfNameMap.put(
								new ByteArrayInputStream(
										((ByteArrayOutputStream) outputStream)
												.toByteArray()), subLine);
					}
				}
				if (printR03) {
					outputStream = genLMS1700R03(locale, meta, l170m01d_list,
							prop_abstractEloan, prop_lms1700m01);
					if (outputStream != null) {
						pdfNameMap.put(
								new ByteArrayInputStream(
										((ByteArrayOutputStream) outputStream)
												.toByteArray()), subLine);
					}
				}

				if (pdfNameMap != null && pdfNameMap.size() > 0) {
					outputStream = new ByteArrayOutputStream();
					PdfTools.mergeReWritePagePdf(pdfNameMap, outputStream,
							propEloanPage.getProperty("PaginationText"), true,
							locale, subLine);
					list.add(new ByteArrayInputStream(
							((ByteArrayOutputStream) outputStream)
									.toByteArray()));
				}
			}

			outputStream = new ByteArrayOutputStream();
			PdfTools.mergeReWritePagePdf(list, outputStream);
		} finally {

		}
		return outputStream;
	}

	/**
	 * gfnPrintFLMS170R01
	 */
	private OutputStream genLMS1700R01(Locale locale, L170M01A meta,
			List<L170M01B> l170m01b_list, L170M01C l170m01c,
			List<L170M01D> l170m01d_list,
			Map<String, List<L170M01E>> m_l170m01e, L170M01F l170m01f,
			List<L170M01G> l170m01g_list, String l170m01h_GN,
			String l170m01h_C, String l5DescFlag,
			Properties prop_abstractEloan, Properties prop_lms1700m01)
			throws FileNotFoundException, IOException, Exception {
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();

		String rptFile = "LMS1700R01_20141001";// default
		String tmpVerStr = "";
		if (Util.equals(meta.getCtlType(), LrsUtil.CTLTYPE_自辦覆審)) {
			rptFile = "LMS1700R01_B_201708";

			if (Util.equals(LrsUtil.V_B_202404, meta.getRptId())) {
				// J-113-0066 企金覆審，覆審內容之覆審項目新增及修正說明文句
				rptFile = "LMS1700R01_B_202404";
				tmpVerStr = Util.equals("Y", l5DescFlag) ? LrsUtil.V_RPT_1130401
						: LrsUtil.V_RPT_11304;
				String tmp = Util.equals("Y", l5DescFlag) ? (tmpVerStr + "起適用，區域營運中心或自辦覆審分行使用")
						: (tmpVerStr + "修訂，營業單位使用");
				rptVariableMap.put("L170M01A.RPTVER", "(" + tmp + ")");
			}
			
		} else if (Util.equals(meta.getCtlType(), LrsUtil.CTLTYPE_價金履約)) {
			rptFile = "LMS1700R01_C_201901";
		} else {
			if (Util.equals(LrsUtil.V_20141001, meta.getRptId())) {
				// 同 default
			} else if (Util.equals(LrsUtil.V_201412, meta.getRptId())) {
				// 同 default
			} else if (Util.equals(LrsUtil.V_20161101, meta.getRptId())) {
				// J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
				rptFile = "LMS1700R01_20161101";
			} else if (Util.equals(LrsUtil.V_20170519, meta.getRptId())) {
				// J-106-0123-001 Web
				// e-Loan國內企金覆審增加覆審項目「立約當日是否依規定查詢銀行法及金控法44條利害關係人之資料後再行簽約」
				rptFile = "LMS1700R01_20170519";
			} else if (Util.equals(LrsUtil.V_20170603, meta.getRptId())) {
				// J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
				if (Util.equals(Util.trim(meta.getRealRpFg()), "Y")) {
					rptFile = "LMS1700R01_R_20170603";
				} else {
					rptFile = "LMS1700R01_20170603";
				}

			} else if (Util.equals(LrsUtil.V_20190201, meta.getRptId())) {
				if (Util.equals(Util.trim(meta.getRealRpFg()), "Y")) {
					rptFile = "LMS1700R01_R_20190201";
				} else {
					rptFile = "LMS1700R01_20190201";
				}

			} else if (Util.equals(LrsUtil.V_20190701, meta.getRptId())
					|| Util.equals(LrsUtil.V_20220401, meta.getRptId())) {
				// J-108-0128_05097_B1001 Web e-Loan企金授信覆審系統修改覆審報告表內容。
				// J-111-0031 更動覆審系統內以下九式覆審報告表之文字內容。
				if (Util.equals(Util.trim(meta.getRealRpFg()), "Y")) {
					rptFile = "LMS1700R01_R_20190701";
				} else {
					rptFile = "LMS1700R01_20190701";
				}
			} else if (Util.equals(LrsUtil.V_20220901, meta.getRptId())
			// J-112-0280 新增企金覆審報告之「附表」項下E-LOAN系統建檔增列3.授信管理系統-「風險權數試算明細」。
					|| Util.equals(LrsUtil.V_20230706, meta.getRptId())) {
				// J-111-0405 更動覆審系統內以下15式覆審報告表之文字內容。
				if (Util.equals(Util.trim(meta.getRealRpFg()), "Y")) {
					rptFile = "LMS1700R01_R_20220901";
				} else {
					rptFile = "LMS1700R01_20220901";
				}
			} else if (Util.equals(LrsUtil.V_20240401, meta.getRptId())) {
				// J-113-0066 企金覆審，覆審內容之覆審項目新增及修正說明文句

				if (Util.equals(Util.trim(meta.getRealRpFg()), "Y")) {
					rptFile = "LMS1700R01_R_20240401";
				} else {
					rptFile = "LMS1700R01_20240401";
				}
				tmpVerStr = LrsUtil.V_RPT_1130401;
			} else if (Util.equals(LrsUtil.V_20240601, meta.getRptId())) {
				// J-113-0204  新增及修正說明文句

				if (Util.equals(Util.trim(meta.getRealRpFg()), "Y")) {
					rptFile = "LMS1700R01_R_20240401";
					tmpVerStr = LrsUtil.V_RPT_1130401;
				} else {
					rptFile = "LMS1700R01_20240601";
					tmpVerStr = LrsUtil.V_RPT_1130601;
				}				
			}

			// J-113-0066 企金覆審，覆審內容之覆審項目新增及修正說明文句
			// 報表實施日期
			if (LrsUtil.compareRptVersion(meta.getRptId(), ">=",
					LrsUtil.V_20240401)) {

				String tmpStr = "";
				tmpStr = "(" + tmpVerStr + "起適用，"
						+ (Util.equals("Y", l5DescFlag) ? "區域營運中心" : "自辦覆審分行")
						+ "使用)";

				rptVariableMap.put("L170M01A.RPTVER", tmpStr);
			}

		}

		ReportGenerator generator = new ReportGenerator("report/lrs/" + rptFile
				+ "_" + locale.toString() + ".rpt");

		OutputStream outputStream = null;

		try {
			if (true) {
				// 分行名稱
				rptVariableMap.put("BRANCHNAME", Util.trim(branchService
						.getBranchName(Util.trim(meta.getOwnBrId()))));
				rptVariableMap
				.put("BRANCHTYPE",
						(Util.equals("Y", l5DescFlag) ? "區域營運中心"
								: "自辦覆審營業單位"));
				rptVariableMap = this.setL170M01AData(rptVariableMap, meta,
						l170m01b_list, m_l170m01e, l170m01h_GN, l170m01h_C,
						prop_abstractEloan, prop_lms1700m01);
				rptVariableMap = this.setL170M01CData(rptVariableMap, l170m01c);
				if (Util.equals(meta.getCtlType(), LrsUtil.CTLTYPE_價金履約)) {
					rptVariableMap = this.setDynamicL170M01DData(
							rptVariableMap, l170m01d_list, prop_lms1700m01);
				} else {
					rptVariableMap = this.setL170M01DData(rptVariableMap,
							l170m01d_list, prop_lms1700m01,meta);
				}
				rptVariableMap = this.setL170M01F_GData(rptVariableMap, meta,
						l170m01f, l170m01g_list, prop_lms1700m01, l5DescFlag);
				generator.setLang(locale);
				generator.setVariableData(rptVariableMap);

				Map<String, String> map = Util.setColumnMap();
				map.put("ReportBean.column01", "L");// title 列
				titleRows.add(map);
			}
			if (true) {
				titleRows = this
						.setL170M01BData(titleRows, l170m01b_list, true);
			}

			if (true) {
				rptVariableMap.put("L170M01D.2STAFFJOBL5FLAG", l5DescFlag);

				Map<String, String> map = Util.setColumnMap();
				map.put("ReportBean.column01", "N");// 合計列
				titleRows.add(map);
			}
			generator.setRowsData(titleRows);
			outputStream = generator.generateReport();
		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
		}
		return outputStream;
	}

	private OutputStream genLMS1700R02(Locale locale, L170M01A meta,
			List<L170M01B> l170m01b_list,
			Map<String, List<L170M01E>> m_l170m01e, String l170m01h_GN,
			String l170m01h_C, Properties prop_abstractEloan,
			Properties prop_lms1700m01) throws FileNotFoundException,
			IOException, Exception {
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();

		String rptFile = "LMS1700R02";
		ReportGenerator generator = new ReportGenerator("report/lrs/" + rptFile
				+ "_" + locale.toString() + ".rpt");

		OutputStream outputStream = null;

		try {

			if (true) {
				// 分行名稱
				rptVariableMap.put("BRANCHNAME", Util.trim(branchService
						.getBranchName(Util.trim(meta.getOwnBrId()))));
				// 報表類別
				rptVariableMap.put("CTLTYPE", meta.getCtlType());
				rptVariableMap
						.put("CTLTYPENAME", (Util.equals(meta.getCtlType(),
								LrsUtil.CTLTYPE_價金履約) ? "對合作房仲業價金履約保證額度"
								: "授信案件"));
				rptVariableMap = this.setL170M01AData(rptVariableMap, meta,
						l170m01b_list, m_l170m01e, l170m01h_GN, l170m01h_C,
						prop_abstractEloan, prop_lms1700m01);

				generator.setLang(locale);
				generator.setVariableData(rptVariableMap);

				Map<String, String> map = Util.setColumnMap();
				map.put("ReportBean.column01", "L");// title 列
				titleRows.add(map);
			}
			if (true) {
				titleRows = this.setL170M01BData(titleRows, l170m01b_list,
						false);
			}

			generator.setRowsData(titleRows);
			outputStream = generator.generateReport();
		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
		}
		return outputStream;
	}

	private OutputStream genLMS1700R03(Locale locale, L170M01A meta,
			List<L170M01D> l170m01d_list, Properties prop_abstractEloan,
			Properties prop_lms1700m01) throws FileNotFoundException,
			IOException, Exception {
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();

		// J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
		String rptFile = "LMS1700R03_20141001";// default
		String rptFileVer = LrsUtil.V_RPT_11304;
		if (Util.equals(LrsUtil.V_20141001, meta.getRptId())) {
			// 同 default
		} else if (Util.equals(LrsUtil.V_201412, meta.getRptId())) {
			// 同 default
		} else if (Util.equals(LrsUtil.V_20161101, meta.getRptId())) {
			rptFile = "LMS1700R03_20161101";
		} else if (Util.equals(LrsUtil.V_20170519, meta.getRptId())) {
			// J-106-0123-001 Web
			// e-Loan國內企金覆審增加覆審項目「立約當日是否依規定查詢銀行法及金控法44條利害關係人之資料後再行簽約」
			rptFile = "LMS1700R03_20170519";
		} else if (Util.equals(LrsUtil.V_20170603, meta.getRptId())) {
			// J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
			rptFile = "LMS1700R03_20170603";
		} else if (Util.equals(LrsUtil.V_20190201, meta.getRptId())) {
			rptFile = "LMS1700R03_20170603";
		} else if (Util.equals(LrsUtil.V_20190701, meta.getRptId())
				|| Util.equals(LrsUtil.V_20220401, meta.getRptId())
				|| Util.equals(LrsUtil.V_20220901, meta.getRptId())
				// J-112-0280 新增企金覆審報告之「附表」項下E-LOAN系統建檔增列3.授信管理系統-「風險權數試算明細」。
				|| Util.equals(LrsUtil.V_20230706, meta.getRptId())) {
			// J-108-0128_05097_B1001 Web e-Loan企金授信覆審系統修改覆審報告表內容。
			// J-111-0031 更動覆審系統內以下九式覆審報告表之文字內容。
			// J-111-0405 更動覆審系統內以下15式覆審報告表之文字內容。
			rptFile = "LMS1700R03_20170603";
		} else if (Util.equals(LrsUtil.V_20240401, meta.getRptId())) {
			// J-113-0066 企金覆審，覆審內容之覆審項目新增及修正說明文句
			rptFile = "LMS1700R03_20240401";
			rptFileVer = LrsUtil.V_RPT_11304;
		} else if (Util.equals(LrsUtil.V_20240601, meta.getRptId())) {			
			// J-113-0204 新增及修正說明文句，避免之後只有更新附表，故多加入新欄位附表版本別
			rptFile = "LMS1700R03_20240401";
			rptFileVer = LrsUtil.V_RPT_11306;
			if (Util.equals("Y", meta.getRealRpFg())) {
				rptFileVer = LrsUtil.V_RPT_11304;
			}
		}
		// else if (Util.equals(LrsUtil.V_20220901, meta.getRptId())) {
		// // J-111-0405 更動覆審系統內以下15式覆審報告表之文字內容。
		// rptFile = "LMS1700R03_20220901";
		// }

		ReportGenerator generator = new ReportGenerator("report/lrs/" + rptFile
				+ "_" + locale.toString() + ".rpt");

		OutputStream outputStream = null;

		try {
			
			if (LrsUtil.compareRptVersion(meta.getRptId(), ">=",
					LrsUtil.V_20240401)) {
				// J-113-0066 企金覆審，覆審內容之覆審項目新增及修正說明文句
				rptVariableMap.put("L170M01A.RPTVER", "(" + rptFileVer + "修訂)");
			}
			
			rptVariableMap.put("L170M01A.RETRIALDATE",
					Util.trim(TWNDate.toAD(meta.getRetrialDate())));
			rptVariableMap.put("L170M01A.PROJECTNO",
					Util.trim(meta.getProjectNo()));
			if (true) {
				LinkedHashMap<String, String> typeCdMap = new LinkedHashMap<String, String>();
				typeCdMap.put("1", "DBU");
				typeCdMap.put("4", "OBU");
				String custIfo = "("
						+ Util.trim(LMSUtil.getDesc(typeCdMap, meta.getTypCd()))
						+ ")" + " " + Util.trim(meta.getCustId()) + " "
						+ Util.trim(meta.getCustName());
				rptVariableMap.put("CUST_INFO", custIfo);
			}

			// J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
			int chapter_num = 0;
			rptVariableMap.put("L170M01A.REALRPFG",
					Util.trim(meta.getRealRpFg()));

			// J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
			// X_土建融
			List<String> rN011 = new ArrayList<String>();
			StringBuffer nd11Memo = new StringBuffer("");
			if (LrsUtil.compareRptVersion(meta.getRptId(), ">=",
					LrsUtil.V_20170603) && Util.equals(meta.getRealRpFg(), "Y")) {
				chapter_num = chapter_num + 1;
				rptVariableMap.put("CHAPTER_NUM1",
						getChineseChapterNum(prop_lms1700m01, chapter_num));

				Set<String> _hide_X_ItemNo = _hide_X_ItemNo(l170m01d_list);

				for (L170M01D l170m01d : l170m01d_list) {
					if (Util.notEquals(LrsUtil.X_土建融, l170m01d.getItemType())) {
						continue;
					}
					String itemNo = l170m01d.getItemNo();
					String itemContent = replaceXnum(itemNo,
							l170m01d.getItemContent());
					String chkText = l170m01d.getChkText();
					// ===========
					// 當勾選 無，細項的項目 不出現
					if (_hide_X_ItemNo.contains(itemNo)) {
						continue;
					}
					
					// ===========
					// 補空白
					if (Util.equals(itemNo, LrsUtil.XA00)) {
						// rN011.add("<tr style='height:32px;'><td>" + ""
						// + "&nbsp;</td></tr>");
					}

					if (Util.equals(LrsUtil.XA1A, l170m01d.getItemNo())) {
						// String _str = toBox("Y2|N2", prop_lms1700m01,
						// l170m01d.getChkResult(), 1);
						// itemContent = StringUtils.replace(
						// l170m01d.getItemContent(),
						// LrsUtil.L170M01D_RPLC, _str)
						// + (Util.equals("N", l170m01d.getChkResult()) ? ""
						// : LrsUtil.Y_DESC_YA1A_Y);
					}

					if (CrsUtil.inCollection(itemNo, LrsUtil.X_TAB2)) {
						String chkResult_fmt = LrsUtil.chkResult_fmt(l170m01d);
						chkResult_fmt = "Y|N|K5";
						String _str = " "
								+ toBox(chkResult_fmt, prop_lms1700m01,
										l170m01d.getChkResult(), 1) + " ";
						itemContent = StringUtils.replace(itemContent,
								LrsUtil.L170M01D_RPLC, _str);
					}

					if (Util.equals(LrsUtil.XA1A, l170m01d.getItemNo())) {

						// chkText ="辦理情辦理情形說明辦理情辦理情形說明辦理情辦理情形說明辦理情辦理情形說明辦理情";

						if (Util.notEquals(Util.trim(chkText), "")) {
							String chkTextTitel = "";

							if (Util.equals(LrsUtil.XA1A, l170m01d.getItemNo())) {
								chkTextTitel = "不符情形說明：";
							}

							// nd11Memo.append("<p style ='margin:2px 0px 5px 50px'>");
							nd11Memo.append("(");
							nd11Memo.append(chkTextTitel);
							nd11Memo.append(chkText);
							nd11Memo.append(")");
							// nd11Memo.append("</p>");

						}

					}

					// ========================
					// 土建融項目縮排
					if (true) {
						if (CrsUtil.inCollection(itemNo, LrsUtil.X_TAB2)) {
							itemContent = "" + itemContent;
							itemContent = itemContent;

							rN011.add("<tr><td >" + itemContent + "</td></tr>"); // style='height:20px;'
							// &nbsp;
						}
					}
					// ========================

				}
				if (Util.notEquals(nd11Memo.toString(), "")) {
					rN011.add("<tr><td >" + "" + nd11Memo.toString()
							+ "</td></tr>");
				}
				rptVariableMap.put(
						"STRN011",
						"<table style='margin:0px' border='0'>"
								+ StringUtils.join(rN011, "") + "</table>");

			}

			// Z_電腦建檔資料
			List<String> r = new ArrayList<String>();
			if (true) {
				chapter_num = chapter_num + 1;
				rptVariableMap.put("CHAPTER_NUM2",
						getChineseChapterNum(prop_lms1700m01, chapter_num));
				Set<String> _hide_Z_ItemNo = _hide_Z_ItemNo(l170m01d_list);

				for (L170M01D l170m01d : l170m01d_list) {
					if (Util.notEquals(LrsUtil.Z_電腦建檔資料, l170m01d.getItemType())) {
						continue;
					}
					String itemNo = l170m01d.getItemNo();
					String itemContent = l170m01d.getItemContent();
					// ===========
					// 當勾選 無，細項的項目 不出現
					if (_hide_Z_ItemNo.contains(itemNo)) {
						continue;
					}

					// ===========
					// 補空白
					if (Util.equals(itemNo, LrsUtil.ZA20)
							|| Util.equals(itemNo, LrsUtil.ZA30)
							|| Util.equals(itemNo, LrsUtil.ZB00)) {
						r.add("<tr style='height:32px;'><td>" + ""
								+ "&nbsp;</td></tr>");
					}

					if (Util.equals(LrsUtil.ZA23, l170m01d.getItemNo())) {
						String _str = toBox("Y2|N2", prop_lms1700m01,
								l170m01d.getChkResult(), 1);
						itemContent = StringUtils.replace(
								l170m01d.getItemContent(),
								LrsUtil.L170M01D_RPLC, _str)
								+ (Util.equals("N", l170m01d.getChkResult()) ? ""
										: LrsUtil.Z_DESC_ZA23_Y);

					}
					if (Util.equals(LrsUtil.ZB1A, l170m01d.getItemNo())) {
						String _str = toBox("Y2|N2", prop_lms1700m01,
								l170m01d.getChkResult(), 1);
						itemContent = StringUtils.replace(
								l170m01d.getItemContent(),
								LrsUtil.L170M01D_RPLC, _str)
								+ (Util.equals("N", l170m01d.getChkResult()) ? ""
										: LrsUtil.Z_DESC_ZB1A_Y);
					}
					// J-111-0405 更動覆審系統內以下15式覆審報告表之文字內容。
					if (Util.equals(LrsUtil.ZB2A, l170m01d.getItemNo())) {
						String _str = toBox("Y2|N2", prop_lms1700m01,
								l170m01d.getChkResult(), 1);
						itemContent = StringUtils.replace(
								l170m01d.getItemContent(),
								LrsUtil.L170M01D_RPLC, _str)
								+ (Util.equals("N", l170m01d.getChkResult()) ? ""
										: LrsUtil.Z_DESC_ZB1A_Y);
					}

					if (CrsUtil.inCollection(itemNo, LrsUtil.Z_YNBOX)) {
						String chkResult_fmt = LrsUtil.chkResult_fmt(l170m01d);
						if (Util.equals(LrsUtil.ZB11, l170m01d.getItemNo())
								|| Util.equals(LrsUtil.ZB12,
										l170m01d.getItemNo())
								|| Util.equals(LrsUtil.ZB13,
										l170m01d.getItemNo())
								// J-112-0280
								// 新增企金覆審報告之「附表」項下E-LOAN系統建檔增列3.授信管理系統-「風險權數試算明細」。
								|| Util.equals(LrsUtil.ZB30,
										l170m01d.getItemNo())
								|| Util.equals(LrsUtil.ZB3E,
										l170m01d.getItemNo())
								|| Util.equals(LrsUtil.ZB3J,
										l170m01d.getItemNo())
								|| Util.equals(LrsUtil.ZB3S,
										l170m01d.getItemNo())
								// J-113-0204 新增及修正說明文句
								|| Util.equals(LrsUtil.ZB40,
										l170m01d.getItemNo())
								|| Util.equals(LrsUtil.ZB50,
										l170m01d.getItemNo())
								|| Util.equals(LrsUtil.ZB14,
										l170m01d.getItemNo())
								|| Util.equals(LrsUtil.ZB15,
										l170m01d.getItemNo())
								|| Util.equals(LrsUtil.ZC10,
										l170m01d.getItemNo())
								|| Util.equals(LrsUtil.ZC1A,
										l170m01d.getItemNo())
								|| Util.equals(LrsUtil.ZC20,
										l170m01d.getItemNo())
								|| Util.equals(LrsUtil.ZC3A,
										l170m01d.getItemNo())
								|| Util.equals(LrsUtil.ZC3B,
										l170m01d.getItemNo())
								|| Util.equals(LrsUtil.ZC3C,
										l170m01d.getItemNo())
								|| Util.equals(LrsUtil.ZC3D,
										l170m01d.getItemNo())										
						) {
							chkResult_fmt = "Y|N|K5";
						}
						String _str = " "
								+ toBox(chkResult_fmt, prop_lms1700m01,
										l170m01d.getChkResult(), 1) + " ";
						itemContent = StringUtils.replace(
								l170m01d.getItemContent(),
								LrsUtil.L170M01D_RPLC, _str);
					}

					// J-112-0280
					// 新增企金覆審報告之「附表」項下E-LOAN系統建檔增列3.授信管理系統-「風險權數試算明細」。
					if (Util.equals(LrsUtil.ZB3A, l170m01d.getItemNo())
							|| Util.equals(LrsUtil.ZB34, l170m01d.getItemNo())
							|| Util.equals(LrsUtil.ZB3N, l170m01d.getItemNo())
							|| Util.equals(LrsUtil.ZB3Q, l170m01d.getItemNo())) {
						String _str = toBox("Y2|N2", prop_lms1700m01,
								l170m01d.getChkResult(), 1);
						itemContent = StringUtils.replace(
								l170m01d.getItemContent(),
								LrsUtil.L170M01D_RPLC, _str);
					}

					// ========================
					// 電腦建檔項目縮排
					if (true) {
						if (CrsUtil.inCollection(itemNo, LrsUtil.Z_TAB1)) {
							itemContent = "　" + itemContent;
							if (Util.equals(LrsUtil.ZA30, itemNo)) {
								itemContent += ("<br>" + "　" + "<u>"
										+ LrsUtil.Z_NOTE_ZA30 + "</u>");
							}
						} else if (CrsUtil.inCollection(itemNo, LrsUtil.Z_TAB2)) {
							itemContent = "　　" + itemContent;
							// J-111-0405 更動覆審系統內以下15式覆審報告表之文字內容。
							if (Util.equals(LrsUtil.ZB21, itemNo)) {
								itemContent += ("<br>" + "　" + "<u>"
										+ LrsUtil.Z_NOTE_ZB21 + "</u>");
							}
						} else if (CrsUtil.inCollection(itemNo, LrsUtil.Z_TAB3)) {
							itemContent = "　　　" + itemContent;
						}
						
						List<String> realRpFgNoShow = new ArrayList<String>();					
						if (LrsUtil.compareRptVersion(meta.getRptId(), ">=",
								LrsUtil.V_20240601)) {
						    
							realRpFgNoShow = new ArrayList<String>();
							realRpFgNoShow.add(LrsUtil.ZA13);			
							realRpFgNoShow.add(LrsUtil.ZB14);
						    realRpFgNoShow.add(LrsUtil.ZB15);
						    realRpFgNoShow.add(LrsUtil.ZB40);
						    realRpFgNoShow.add(LrsUtil.ZB50);
						    realRpFgNoShow.add(LrsUtil.ZC10);
						    realRpFgNoShow.add(LrsUtil.ZC1A);
						    realRpFgNoShow.add(LrsUtil.ZC20);
						    realRpFgNoShow.add(LrsUtil.ZC00);
						    realRpFgNoShow.add(LrsUtil.ZC30);
						    realRpFgNoShow.add(LrsUtil.ZC3A);
						    realRpFgNoShow.add(LrsUtil.ZC3B);
						    realRpFgNoShow.add(LrsUtil.ZC3C);
						    realRpFgNoShow.add(LrsUtil.ZC3D);
						    
							if(Util.equals("Y",Util.trim(meta.getRealRpFg()))){
								
								if (realRpFgNoShow.contains(l170m01d.getItemNo())){
									continue;
								}
								
								if (Util.equals(LrsUtil.ZB10, l170m01d.getItemNo())){
									itemContent=StringUtils.replace(itemContent,LrsUtil.L170M01D_RPLCSEQ, "1.");
								}
								if (Util.equals(LrsUtil.ZB20, l170m01d.getItemNo())){
									itemContent=StringUtils.replace(itemContent,LrsUtil.L170M01D_RPLCSEQ, "2.");
								}
								if (Util.equals(LrsUtil.ZB30, l170m01d.getItemNo())){
									itemContent=StringUtils.replace(itemContent,LrsUtil.L170M01D_RPLCSEQ, "3.");
								}
								if(Util.equals(LrsUtil.ZB11, l170m01d.getItemNo())){
									itemContent=StringUtils.replace(itemContent,LrsUtil.L170M01D_RPLCSTR, "");						
								}
								
							}else{
								if (Util.equals(LrsUtil.ZB40, l170m01d.getItemNo())){
									itemContent=StringUtils.replace(itemContent,LrsUtil.L170M01D_RPLCSEQ, "1.");
								}
								if (Util.equals(LrsUtil.ZB50, l170m01d.getItemNo())){
									itemContent=StringUtils.replace(itemContent,LrsUtil.L170M01D_RPLCSEQ, "2.");
								}
								if (Util.equals(LrsUtil.ZB10, l170m01d.getItemNo())){
									itemContent=StringUtils.replace(itemContent,LrsUtil.L170M01D_RPLCSEQ, "3.");
								}
								if (Util.equals(LrsUtil.ZB20, l170m01d.getItemNo())){
									itemContent=StringUtils.replace(itemContent,LrsUtil.L170M01D_RPLCSEQ, "4.");
								}
								if (Util.equals(LrsUtil.ZB30, l170m01d.getItemNo())){
									itemContent=StringUtils.replace(itemContent,LrsUtil.L170M01D_RPLCSEQ, "5.");
								}
								if(Util.equals(LrsUtil.ZB11, l170m01d.getItemNo())){
									itemContent=StringUtils.replace(itemContent,LrsUtil.L170M01D_RPLCSTR, "土地");						
								}
							}
						    
						}
						
					}
					// ========================
					r.add("<tr><td style='height:20px;'>" + itemContent
							+ "&nbsp;</td></tr>");

				}

				rptVariableMap.put("STR",
						"<table border='0'>" + StringUtils.join(r, "")
								+ "</table>");

			}

			// J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
			List<String> rN015 = new ArrayList<String>();
			if (LrsUtil.compareRptVersion(meta.getRptId(), ">=",
					LrsUtil.V_20161101)) {
				chapter_num = chapter_num + 1;
				rptVariableMap.put("CHAPTER_NUM3",
						getChineseChapterNum(prop_lms1700m01, chapter_num));
				Set<String> _hide_Y_ItemNo = _hide_Y_ItemNo(l170m01d_list);

				for (L170M01D l170m01d : l170m01d_list) {
					if (Util.notEquals(LrsUtil.Y_履約條件, l170m01d.getItemType())) {
						continue;
					}
					String itemNo = l170m01d.getItemNo();
					String itemContent = l170m01d.getItemContent();
					String chkText = l170m01d.getChkText();
					// ===========
					// 當勾選 無，細項的項目 不出現
					if (_hide_Y_ItemNo.contains(itemNo)) {
						continue;
					}

					// ===========
					// 補空白
					if (Util.equals(itemNo, LrsUtil.YA00)
							|| Util.equals(itemNo, LrsUtil.YB00)) {
						// rN015.add("<tr style='height:32px;'><td>" + ""
						// + "&nbsp;</td></tr>");
					}

					if (Util.equals(LrsUtil.YA1A, l170m01d.getItemNo())) {
						String _str = toBox("Y2|N2", prop_lms1700m01,
								l170m01d.getChkResult(), 1);
						itemContent = StringUtils.replace(
								l170m01d.getItemContent(),
								LrsUtil.L170M01D_RPLC, _str)
								+ (Util.equals("N", l170m01d.getChkResult()) ? ""
										: LrsUtil.Y_DESC_YA1A_Y);
					}

					if (Util.equals(LrsUtil.YB1A, l170m01d.getItemNo())) {
						String _str = toBox("Y2|N2", prop_lms1700m01,
								l170m01d.getChkResult(), 1);
						itemContent = StringUtils.replace(
								l170m01d.getItemContent(),
								LrsUtil.L170M01D_RPLC, _str)
								+ (Util.equals("N", l170m01d.getChkResult()) ? ""
										: LrsUtil.Y_DESC_YA1A_Y);
					}

					if (CrsUtil.inCollection(itemNo, LrsUtil.Y_YNBOX)) {
						String chkResult_fmt = LrsUtil.chkResult_fmt(l170m01d);
						if (LrsUtil.compareRptVersion(meta.getRptId(), ">=",
								LrsUtil.V_20170603)) {
							if (Util.equals(LrsUtil.YA13, l170m01d.getItemNo())
									|| Util.equals(LrsUtil.YB11,
											l170m01d.getItemNo())
									|| Util.equals(LrsUtil.YB12,
											l170m01d.getItemNo())
									|| Util.equals(LrsUtil.YB13,
											l170m01d.getItemNo())) {
								chkResult_fmt = "Y|N|K5";
							} else {
								// J-109-0336 檢視事項及承諾事項之管控機制
								if (Util.equals(LrsUtil.YA11,
										l170m01d.getItemNo())) {
									chkResult_fmt = "Y|N";
								} else {
									chkResult_fmt = "Y|N|K5";
								}
							}
						} else {
							chkResult_fmt = "Y|N";
						}

						String _str = " "
								+ toBox(chkResult_fmt, prop_lms1700m01,
										l170m01d.getChkResult(), 1) + " ";
						itemContent = StringUtils.replace(
								l170m01d.getItemContent(),
								LrsUtil.L170M01D_RPLC, _str);
					}

					StringBuffer nd15Memo = new StringBuffer("");
					if (CrsUtil.inCollection(itemNo, LrsUtil.Y_TAB2)) {

						// chkText ="辦理情辦理情形說明辦理情辦理情形說明辦理情辦理情形說明辦理情辦理情形說明辦理情";

						if (Util.notEquals(Util.trim(chkText), "")) {
							String chkTextTitel = "說明：";

							/*
							 * // 因為說明欄位皆需輸入說明 if (Util.equals(LrsUtil.YA11,
							 * l170m01d.getItemNo()) ||
							 * Util.equals(LrsUtil.YB11, l170m01d.getItemNo()))
							 * { chkTextTitel = "說明："; } else if
							 * (Util.equals(LrsUtil.YA12, l170m01d.getItemNo())
							 * || Util.equals(LrsUtil.YB12,
							 * l170m01d.getItemNo())) { chkTextTitel =
							 * "未符合情形說明："; } else if (Util.equals(LrsUtil.YA13,
							 * l170m01d.getItemNo()) ||
							 * Util.equals(LrsUtil.YB13, l170m01d.getItemNo()))
							 * { chkTextTitel = "辦理情形說明："; }
							 */

							nd15Memo.append("<p style ='margin:2px 0px 5px 50px'>");
							nd15Memo.append("(");
							nd15Memo.append(chkTextTitel);
							nd15Memo.append(chkText);
							nd15Memo.append(")");
							nd15Memo.append("</p>");

						}

					}

					// ========================
					// 履約條件項目縮排
					if (true) {
						if (CrsUtil.inCollection(itemNo, LrsUtil.Y_TAB1)) {
							itemContent = "　" + itemContent;
						} else if (CrsUtil.inCollection(itemNo, LrsUtil.Y_TAB2)) {
							itemContent = "　　" + itemContent;
							itemContent = itemContent + nd15Memo.toString();
						}
					}
					// ========================
					rN015.add("<tr><td >" + itemContent + "</td></tr>"); // style='height:20px;'
																			// &nbsp;

				}
				rptVariableMap.put(
						"STRN015",
						"<table style='margin:0px' border='0'>"
								+ StringUtils.join(rN015, "") + "</table>");

			}

			if (LrsUtil.compareRptVersion(meta.getRptId(), ">=",
					LrsUtil.V_20161101)) {
				if (rN011.size() == 0 && rN015.size() == 0 && r.size() == 0) {
					return outputStream;
				}
			} else {
				if (r.size() == 0) {
					return outputStream;
				}
			}

			// ===
			// 底部
			rptVariableMap.put("L170M01A.RANDOMCODE",
					_get_randomCode(prop_abstractEloan, meta.getRandomCode()));

			generator.setLang(locale);
			generator.setVariableData(rptVariableMap);
			generator.setRowsData(titleRows);

			outputStream = generator.generateReport();
		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
		}
		return outputStream;
	}

	private Set<String> _hide_Z_ItemNo(List<L170M01D> l170m01d_list) {
		Set<String> r = new HashSet<String>();
		boolean hide_ZA23 = false;
		boolean hide_ZB1A = false;
		// J-111-0405 更動覆審系統內以下15式覆審報告表之文字內容。
		boolean hide_ZB2A = false;
		// J-112-0280 新增企金覆審報告之「附表」項下E-LOAN系統建檔增列3.授信管理系統-「風險權數試算明細」。
		boolean hide_ZB30 = false;
		String hide_ZB3E = "";
		boolean hide_ZB34 = false;
		boolean hide_ZB3N = false;
		boolean hide_ZB3Q = false;
		// J-113-0204  新增及修正說明文句
		boolean hide_ZC10 = false;

		for (L170M01D l170m01d : l170m01d_list) {
			if (Util.equals(l170m01d.getItemNo(), LrsUtil.ZA23)) {
				hide_ZA23 = Util.equals("N", l170m01d.getChkResult());
			} else if (Util.equals(l170m01d.getItemNo(), LrsUtil.ZB1A)) {
				hide_ZB1A = Util.equals("N", l170m01d.getChkResult());
			} else if (Util.equals(l170m01d.getItemNo(), LrsUtil.ZB2A)) {
				// J-111-0405 更動覆審系統內以下15式覆審報告表之文字內容。
				hide_ZB2A = Util.equals("N", l170m01d.getChkResult());
			}
			// J-112-0280 新增企金覆審報告之「附表」項下E-LOAN系統建檔增列3.授信管理系統-「風險權數試算明細」。
			else if (Util.equals(l170m01d.getItemNo(), LrsUtil.ZB30)) {
				hide_ZB30 = Util.equals("K", l170m01d.getChkResult());
			} else if (Util.equals(l170m01d.getItemNo(), LrsUtil.ZB3E)) {
				hide_ZB3E = Util.nullToSpace(l170m01d.getChkResult());
			} else if (Util.equals(l170m01d.getItemNo(), LrsUtil.ZB34)) {
				hide_ZB34 = Util.equals("N", l170m01d.getChkResult());
			} else if (Util.equals(l170m01d.getItemNo(), LrsUtil.ZB3N)) {
				hide_ZB3N = Util.equals("N", l170m01d.getChkResult());
			} else if (Util.equals(l170m01d.getItemNo(), LrsUtil.ZB3Q)) {
				hide_ZB3Q = Util.equals("N", l170m01d.getChkResult());
			}
			// J-113-0204  新增及修正說明文句
			else if (Util.equals(l170m01d.getItemNo(), LrsUtil.ZC10)) {
				if (Util.equals("Y", l170m01d.getChkResult())
						|| Util.equals("K", l170m01d.getChkResult())) {
					hide_ZC10 = true;
				}
			}

		}
		if (hide_ZA23) {
			r.add(LrsUtil.ZA2A);
			r.add(LrsUtil.ZA2B);
			r.add(LrsUtil.ZA2C);
			r.add(LrsUtil.ZA2D);
			r.add(LrsUtil.ZA2E);
			r.add(LrsUtil.ZA2F);
		}
		if (hide_ZB1A) {
			r.add(LrsUtil.ZB11);
			r.add(LrsUtil.ZB12);
			r.add(LrsUtil.ZB13);
			// J-113-0204  新增及修正說明文句
			r.add(LrsUtil.ZB14);
			r.add(LrsUtil.ZB15);
		}
		if (hide_ZB2A) {
			r.add(LrsUtil.ZB21);
		}
		// J-112-0280 新增企金覆審報告之「附表」項下E-LOAN系統建檔增列3.授信管理系統-「風險權數試算明細」。
		if (hide_ZB30) {
			r.add(LrsUtil.ZB3G);
			r.add(LrsUtil.ZB31);
			r.add(LrsUtil.ZB3A);
			r.add(LrsUtil.ZB3B);
			r.add(LrsUtil.ZB32);
			r.add(LrsUtil.ZB3C);
			r.add(LrsUtil.ZB3D);
			r.add(LrsUtil.ZB33);
			r.add(LrsUtil.ZB3E);
			r.add(LrsUtil.ZB3F);
			r.add(LrsUtil.ZB3G);
			r.add(LrsUtil.ZB3H);
			r.add(LrsUtil.ZB3I);
			r.add(LrsUtil.ZB3J);
			r.add(LrsUtil.ZB34);
			r.add(LrsUtil.ZB3K);
			r.add(LrsUtil.ZB3L);
			r.add(LrsUtil.ZB3M);
			r.add(LrsUtil.ZB35);
			r.add(LrsUtil.ZB3N);
			r.add(LrsUtil.ZB3O);
			r.add(LrsUtil.ZB3P);
			r.add(LrsUtil.ZB3Q);
			r.add(LrsUtil.ZB3R);
			r.add(LrsUtil.ZB3S);
		}
		if (hide_ZB3E.equals("Y")) {
			r.add(LrsUtil.ZB3G);
			r.add(LrsUtil.ZB3H);
			r.add(LrsUtil.ZB3I);
		} else if (hide_ZB3E.equals("N")) {
			r.add(LrsUtil.ZB3F);
		} else if (hide_ZB3E.equals("K")) {
			r.add(LrsUtil.ZB3F);
			r.add(LrsUtil.ZB3G);
			r.add(LrsUtil.ZB3H);
			r.add(LrsUtil.ZB3I);
		}
		if (hide_ZB34) {
			r.add(LrsUtil.ZB3K);
			r.add(LrsUtil.ZB3L);
			r.add(LrsUtil.ZB3M);
		}
		if (hide_ZB3N) {
			r.add(LrsUtil.ZB3O);
			r.add(LrsUtil.ZB3P);
		}
		if (hide_ZB3Q) {
			r.add(LrsUtil.ZB3R);
			r.add(LrsUtil.ZB3S);
		}		
		// J-113-0204  新增及修正說明文句
		if (hide_ZC10) {
			r.add(LrsUtil.ZC1A);			
		}
		
		return r;
	}

	// J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
	private Set<String> _hide_Y_ItemNo(List<L170M01D> l170m01d_list) {
		Set<String> r = new HashSet<String>();
		boolean hide_YA1A = false;
		boolean hide_YB1A = false;
		// J-109-0336 檢視事項及承諾事項之管控機制 - 第三項 always 都顯示
		// J-109-0336_002 檢視事項及承諾事項之管控機制 - YA12 為"否" 出現 YA13； YB11.YB12任一為"否" 出現
		// YB13
		boolean hide_YA13 = true;
		boolean hide_YB13 = true;
		for (L170M01D l170m01d : l170m01d_list) {
			if (Util.equals(l170m01d.getItemNo(), LrsUtil.YA1A)) {
				hide_YA1A = Util.equals("N", l170m01d.getChkResult());
			} else if (Util.equals(l170m01d.getItemNo(), LrsUtil.YB1A)) {
				hide_YB1A = Util.equals("N", l170m01d.getChkResult());
			}

			// J-105-0287-003 修改Web e-Loan國內企金授信覆審系統履行條件之檢核判斷
			// J-109-0336 檢視事項及承諾事項之管控機制 - 第三項 always 都顯示
			// J-109-0336_002 檢視事項及承諾事項之管控機制 - YA12 為"否" 出現 YA13；
			// YB11.YB12任一為"否" 出現 YB13

			// if (Util.equals(l170m01d.getItemNo(), LrsUtil.YA11)
			// || Util.equals(l170m01d.getItemNo(), LrsUtil.YA12)) {
			if (Util.equals(l170m01d.getItemNo(), LrsUtil.YA12)) {
				if (Util.equals("N", l170m01d.getChkResult())) {
					hide_YA13 = false;
				}
			}

			if (Util.equals(l170m01d.getItemNo(), LrsUtil.YB11)
					|| Util.equals(l170m01d.getItemNo(), LrsUtil.YB12)) {
				if (Util.equals("N", l170m01d.getChkResult())) {
					hide_YB13 = false;
				}
			}

		}
		if (hide_YA1A) {
			r.add(LrsUtil.YA11);
			r.add(LrsUtil.YA12);
			r.add(LrsUtil.YA13);
		} else {
			// J-105-0287-003 修改Web e-Loan國內企金授信覆審系統履行條件之檢核判斷
			// J-109-0336 檢視事項及承諾事項之管控機制 - 第三項 always 都顯示
			// J-109-0336_002 檢視事項及承諾事項之管控機制 - YA12 為"否" 出現 YA13；
			// YB11.YB12任一為"否" 出現 YB13
			if (hide_YA13) {
				r.add(LrsUtil.YA13);
			}
		}
		if (hide_YB1A) {
			r.add(LrsUtil.YB11);
			r.add(LrsUtil.YB12);
			r.add(LrsUtil.YB13);
		} else {
			// J-105-0287-003 修改Web e-Loan國內企金授信覆審系統履行條件之檢核判斷
			// J-109-0336 檢視事項及承諾事項之管控機制 - 第三項 always 都顯示
			if (hide_YB13) {
				r.add(LrsUtil.YB13);
			}
		}

		return r;
	}

	private String _get_randomCode(Properties prop_abstractEloan,
			String randomCode) {
		return prop_abstractEloan.getProperty("doc.docCode") + "："
				+ Util.trim(randomCode);
	}

	private Map<String, String> setL170M01AData(
			Map<String, String> rptVariableMap, L170M01A meta,
			List<L170M01B> l170m01b_list,
			Map<String, List<L170M01E>> m_l170m01e, String l170m01h_GN,
			String l170m01h_C, Properties prop_abstractEloan,
			Properties prop_lms1700m01) {
		rptVariableMap.put("L170M01A.RETRIALDATE",
				Util.trim(TWNDate.toAD(meta.getRetrialDate())));
		if (true) {
			String lastRetrialDate = prop_lms1700m01.getProperty("label.N2");// 無
			if (CrsUtil.isNOT_null_and_NOTZeroDate(meta.getLastRetrialDate())) {
				lastRetrialDate = TWNDate.toAD(meta.getLastRetrialDate());
			}
			rptVariableMap.put("L170M01A.LASTRETRIALDATE", lastRetrialDate);
		}
		rptVariableMap
				.put("L170M01A.PROJECTNO", Util.trim(meta.getProjectNo()));
		rptVariableMap.put("L170M01A.TRADETYPE", Util.trim(meta.getTradeType())
				+ "(" + LrsUtil.get_s01_busCd_bussKind(meta) + ")");
		if (true) {
			LinkedHashMap<String, String> typeCdMap = new LinkedHashMap<String, String>();
			typeCdMap.put("1", "DBU");
			typeCdMap.put("4", "OBU");
			rptVariableMap.put("L170M01A.TYPCD",
					Util.trim(LMSUtil.getDesc(typeCdMap, meta.getTypCd())));
		}
		rptVariableMap.put("L170M01A.CUSTID", Util.trim(meta.getCustId()));
		rptVariableMap.put("L170M01A.DUPNO", Util.trim(meta.getDupNo()));
		rptVariableMap.put("L170M01A.CUSTNAME", Util.trim(meta.getCustName()));

		rptVariableMap.put("L170M01A.CHAIRMAN", Util.trim(meta.getChairman()));

		// J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
		rptVariableMap.put("L170M01A.REALRPFG", Util.trim(meta.getRealRpFg()));

		// J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
		if (LrsUtil
				.compareRptVersion(meta.getRptId(), ">=", LrsUtil.V_20170603)) {

			if (Util.equals(Util.trim(meta.getRealRpFg()), "Y")) {
				// L170M01A.realRpFgStrY=（含土建融實地覆審）
				rptVariableMap.put("L170M01A.REALRPFGSTR",
						prop_lms1700m01.getProperty("L170M01A.realRpFgStrY"));

				String realDt = prop_lms1700m01.getProperty("label.N2");// 無
				if (CrsUtil.isNOT_null_and_NOTZeroDate(meta.getRealDt())) {
					realDt = TWNDate.toAD(meta.getRealDt());
				}
				rptVariableMap.put("L170M01A.REALDT",
						prop_lms1700m01.getProperty("L170M01A.realDt") + "："
								+ realDt);

			} else {
				// L170M01A.realRpFgStrN=一般
				rptVariableMap.put("L170M01A.REALRPFGSTR",
						prop_lms1700m01.getProperty("L170M01A.realRpFgStrN"));
				rptVariableMap.put("L170M01A.REALDT", "");
			}
		} else {
			if (Util.equals(Util.trim(meta.getRealRpFg()), "Y")) {
				rptVariableMap.put("L170M01A.REALRPFGSTR",
						prop_lms1700m01.getProperty("L170M01A.realRpFg1")); // L170M01A.realRpFg1=實地

				String realDt = prop_lms1700m01.getProperty("label.N2");// 無
				if (CrsUtil.isNOT_null_and_NOTZeroDate(meta.getRealDt())) {
					realDt = TWNDate.toAD(meta.getRealDt());
				}
				rptVariableMap.put("L170M01A.REALDT",
						prop_lms1700m01.getProperty("L170M01A.realDt") + "："
								+ realDt);

			} else {
				rptVariableMap.put("L170M01A.REALRPFGSTR", "");
				rptVariableMap.put("L170M01A.REALDT", "");
			}
		}

		// J-105-0287-001 END

		if (true) {
			String creditType = "";
			String creditGrade = "";
			String excreditType = "";
			String excreditGrade = "";

			String mowType = "";
			String mowGrade = "";
			String exmowType = "";
			String exmowGrade = "";

			String fcrdType = "";
			String fcrdArea = "";
			String fcrdPred = "";
			String fcrdGrad = "";
			String exfcrdType = "";
			String exfcrdArea = "";
			String exfcrdPred = "";
			String exfcrdGrad = "";
			if (true) {
				L170M01E l170m01e_C = LrsUtil.firstElm(
						m_l170m01e.get(LrsUtil.M01E_FLAG_C), "T");
				if (l170m01e_C != null) {
					creditType = l170m01e_C.getCrdType();
					creditGrade = Util.trim(l170m01e_C.getGrade());
				}
			}
			if (true) {
				L170M01E l170m01e_M = LrsUtil.firstElm(
						m_l170m01e.get(LrsUtil.M01E_FLAG_M), "T");
				if (l170m01e_M != null) {
					mowType = l170m01e_M.getCrdType();
					mowGrade = Util.trim(l170m01e_M.getGrade());
				}
			}
			if (true) {
				L170M01E l170m01e_F = LrsUtil.firstElm(
						m_l170m01e.get(LrsUtil.M01E_FLAG_F), "T");
				if (l170m01e_F != null) {
					fcrdType = l170m01e_F.getCrdType();
					fcrdArea = l170m01e_F.getFcrdArea();
					fcrdPred = l170m01e_F.getFcrdPred();
					fcrdGrad = l170m01e_F.getGrade();
				}
			}
			if (true) {
				L170M01E l170m01e_exC = LrsUtil.firstElm(
						m_l170m01e.get(LrsUtil.M01E_FLAG_C), "L");
				if (l170m01e_exC != null) {
					excreditType = l170m01e_exC.getCrdType();
					excreditGrade = Util.trim(l170m01e_exC.getGrade());
				}
			}
			if (true) {
				L170M01E l170m01e_exM = LrsUtil.firstElm(
						m_l170m01e.get(LrsUtil.M01E_FLAG_M), "L");
				if (l170m01e_exM != null) {
					exmowType = l170m01e_exM.getCrdType();
					exmowGrade = Util.trim(l170m01e_exM.getGrade());
				}
			}
			if (true) {
				L170M01E l170m01e_exF = LrsUtil.firstElm(
						m_l170m01e.get(LrsUtil.M01E_FLAG_F), "L");
				if (l170m01e_exF != null) {
					exfcrdType = l170m01e_exF.getCrdType();
					exfcrdArea = l170m01e_exF.getFcrdArea();
					exfcrdPred = l170m01e_exF.getFcrdPred();
					exfcrdGrad = l170m01e_exF.getGrade();
				}
			}
			// ----------------------------------
			rptVariableMap.put(
					"L170M01E.C_M",
					"信用評等："
							+ LMSUtil.getDesc(
									retrialService.get_lrs_CrdtType_2_withXX(),
									creditType)
							+ " "
							+ LMSUtil.getDesc(retrialService.get_lrs_CrdtTbl(),
									creditGrade)
							+ "   "
							+ "信用風險內部評等："
							+ LMSUtil.getDesc(
									retrialService.get_lrs_MowType_2_withXX(),
									mowType)
							+ " "
							+ LMSUtil.getDesc(retrialService.get_lrs_MowTbl(),
									mowGrade)
							+ "\n"
							+ "前次信用評等："
							+ LMSUtil.getDesc(
									retrialService.get_lrs_exType("exC"),
									excreditType)
							+ " "
							+ LMSUtil.getDesc(retrialService.get_lrs_CrdtTbl(),
									excreditGrade)
							+ "   "
							+ "前次信用風險內部評等："
							+ LMSUtil.getDesc(
									retrialService.get_lrs_exType("exM"),
									exmowType)
							+ " "
							+ LMSUtil.getDesc(retrialService.get_lrs_MowTbl(),
									exmowGrade));
			String o_l170m01_f = "";
			if (Util.isNotEmpty(fcrdType)) {
				// J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等
				String period = "-"
						+ LMSUtil.getDesc(retrialService.get_lrs_FcrdArea(),
								fcrdArea)
						+ LMSUtil.getDesc(retrialService.get_lrs_FcrdPred(),
								fcrdPred);
				if (UtilConstants.Casedoc.CrdType.FitchTW.equals(Util
						.trim(fcrdType))
						|| UtilConstants.Casedoc.CrdType.KBRA.equals(Util
								.trim(fcrdType))) {
					period = "";
				}
				o_l170m01_f = "外部評等："
						+ LMSUtil.getDesc(retrialService.get_lrs_FcrdType_2(),
								fcrdType) + period + "：" + fcrdGrad;
			}
			if (Util.isNotEmpty(exfcrdType)) {
				// J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等
				String periodEx = "-"
						+ LMSUtil.getDesc(retrialService.get_lrs_FcrdArea(),
								exfcrdArea)
						+ LMSUtil.getDesc(retrialService.get_lrs_FcrdPred(),
								exfcrdPred);
				if (UtilConstants.Casedoc.CrdType.FitchTW.equals(Util
						.trim(exfcrdType))
						|| UtilConstants.Casedoc.CrdType.KBRA.equals(Util
								.trim(exfcrdType))) {
					periodEx = "";
				}

				o_l170m01_f += (Util.equals("", o_l170m01_f) ? "" : "\n")
						+ "前次外部評等："
						+ LMSUtil.getDesc(retrialService.get_lrs_FcrdType_2(),
								exfcrdType) + periodEx + "：" + exfcrdGrad;
			}
			rptVariableMap.put("L170M01E.F", o_l170m01_f);
		}
		if (true) {
			rptVariableMap.put(
					"L170M01A.MLOANPERSON",
					toBox(DEFAULT_FMT, prop_lms1700m01, meta.getMLoanPerson(),
							0));
			rptVariableMap.put(
					"L170M01A.MLOANPERSONA",
					toBox(DEFAULT_FMT, prop_lms1700m01, meta.getMLoanPersonA(),
							0));
		}
		if (true) {
			rptVariableMap.put("L170M01A.RLTGUARANTOR", l170m01h_GN);
			String o_coBorrower = "";
			if (Util.isNotEmpty(l170m01h_C) && Util.notEquals(l170m01h_C, "無")) {
				o_coBorrower = l170m01h_C;
			}
			rptVariableMap.put("L170M01A.RLTCOBORROWER", o_coBorrower);
		}
		rptVariableMap.put("L170M01A.LNDATADATE",
				Util.trim(TWNDate.toAD(meta.getLnDataDate())));

		// ===
		// 額度資料
		if (true) {
			String quotaCurr = "";
			String quotaAmt = "";
			String balCurr = "";
			String balAmt = "";
			if (CollectionUtils.isNotEmpty(l170m01b_list)) {
				quotaCurr = meta.getTotQuotaCurr();
				balCurr = meta.getTotBalCurr();

				quotaAmt = CrsUtil.amtDivide1000(meta.getTotQuota());
				balAmt = CrsUtil.amtDivide1000(meta.getTotBal());
			}
			rptVariableMap.put("L170M01A.QUOTACURR", quotaCurr);
			rptVariableMap.put("L170M01A.QUOTAAMT", quotaAmt);
			rptVariableMap.put("L170M01A.BALCURR", balCurr);
			rptVariableMap.put("L170M01A.BALAMT", balAmt);
		}

		// ===
		// 底部

		// J-108-0128_05097_B1001 Web e-Loan企金授信覆審系統修改覆審報告表內容。
		// rptVariableMap.put(
		// "L170M01A.APPENDIX",
		// Util.trim(Util.trim(meta.getProjectNo()) + " "
		// + Util.trim(meta.getCustId())
		// + Util.trim(meta.getDupNo()) + " "
		// + Util.trim(meta.getCustName())));

		rptVariableMap.put("L170M01A.RANDOMCODE",
				_get_randomCode(prop_abstractEloan, meta.getRandomCode()));

		return rptVariableMap;
	}

	private List<Map<String, String>> setL170M01BData(
			List<Map<String, String>> titleRows, List<L170M01B> l170m01b_list,
			boolean isFirstPage) {

		if (CollectionUtils.isNotEmpty(l170m01b_list)) {
			int count = 0;
			for (L170M01B l170m01b : l170m01b_list) {
				if (isFirstPage && count >= GRID_CNT) {
					count++;
				} else if (!isFirstPage && count < GRID_CNT) {
					count++;
				} else {
					titleRows.add(toMap(l170m01b));
					count++;
				}
			}
		} else {
			if (isFirstPage) {

				L170M01B l170m01b = new L170M01B();

				// ---
				titleRows.add(toMap(l170m01b));
			}
		}
		return titleRows;
	}

	private Map<String, String> toMap(L170M01B l170m01b) {
		Map<String, String> map = Util.setColumnMap();
		map.put("ReportBean.column01", "Y");
		if (true) {
			String s_quotaCurr = "";
			String s_quota = "";
			if (l170m01b.getQuotaAmt() != null) {
				s_quotaCurr = Util.trim(l170m01b.getQuotaCurr());
				s_quota = CrsUtil.amtDivide1000(l170m01b.getQuotaAmt());
			}
			map.put("ReportBean.column02", s_quotaCurr);
			map.put("ReportBean.column03", s_quota);
		}

		if (true) {
			String s_balCurr = "";
			String s_bal = "";
			if (l170m01b.getBalAmt() != null) {
				s_balCurr = Util.trim(l170m01b.getBalCurr());
				s_bal = CrsUtil.amtDivide1000(l170m01b.getBalAmt());
			}
			map.put("ReportBean.column04", s_balCurr);
			map.put("ReportBean.column05", s_bal);
		}
		map.put("ReportBean.column06", Util.trim(l170m01b.getSubject()));
		map.put("ReportBean.column07", Util.trim(l170m01b.getCntrNo()));
		map.put("ReportBean.column08",
				Util.trim(TWNDate.toAD(l170m01b.getFromDate())));
		map.put("ReportBean.column09",
				Util.trim(TWNDate.toAD(l170m01b.getEndDate())));
		map.put("ReportBean.column10",
				Util.equals("Y", l170m01b.getNewCase()) ? "ｖ" : "");
		if (true) {
			List<String> r = new ArrayList<String>();
			String guaranteeName = Util.trim(l170m01b.getGuaranteeName());
			String majorMemo = Util.trim(l170m01b.getMajorMemo());
			if (Util.isNotEmpty(guaranteeName)) {
				r.add(guaranteeName);
			}
			if (Util.isNotEmpty(majorMemo)) {
				r.add(majorMemo);
			}
			map.put("ReportBean.column11", StringUtils.join(r, "\n"));
		}
		if (true) {
			String s_estCurr = "";
			String s_estAmt = "";
			String s_loanCurr = "";
			String s_loanAmt = "";
			if (Util.equals(l170m01b.getGuaranteeName(), LrsUtil.M01B_CMS_NONE)
					|| Util.equals(l170m01b.getGuaranteeName(),
							LrsUtil.M01B_CMS_SAMEAS)) {
				// 當為 無擔保品/同上, 不印出 估值/押值
			} else {
				if (l170m01b.getEstAmt() != null
						&& Util.isNotEmpty(Util.trim(l170m01b.getEstCurr()))) {
					s_estCurr = Util.trim(l170m01b.getEstCurr());
					s_estAmt = CrsUtil.amtDivide1000(l170m01b.getEstAmt());
				}
				if (l170m01b.getLoanAmt() != null
						&& Util.isNotEmpty(Util.trim(l170m01b.getLoanCurr()))) {
					s_loanCurr = Util.trim(l170m01b.getLoanCurr());
					s_loanAmt = CrsUtil.amtDivide1000_floor(l170m01b
							.getLoanAmt());
				}
			}
			map.put("ReportBean.column13", s_estCurr);
			map.put("ReportBean.column14", s_estAmt);
			map.put("ReportBean.column15", s_loanCurr);
			map.put("ReportBean.column16", s_loanAmt);
		}
		return map;
	}

	private Map<String, String> setL170M01CData(
			Map<String, String> rptVariableMap, L170M01C l170m01c) {
		String unit = "";
		if (l170m01c.getUnit() != null) {
			CodeType codeType = codeTypeService.findByCodeTypeAndCodeValue(
					"CurrUnit", String.valueOf(l170m01c.getUnit()));
			if (codeType != null) {
				unit = Util.trim(codeType.getCodeDesc());
			}
		}
		Map<String, String> finRationMap = retrialService.get_lrs_FinRatio();
		rptVariableMap.put("L170M01C.UNIT", unit);
		rptVariableMap.put("L170M01C.CURR", Util.trim(l170m01c.getCurr()));
		rptVariableMap.put("L170M01C.FROMDATE1",
				Util.trim(TWNDate.toAD(l170m01c.getFromDate1())));
		rptVariableMap.put("L170M01C.FROMDATE2",
				Util.trim(TWNDate.toAD(l170m01c.getFromDate2())));
		rptVariableMap.put("L170M01C.FROMDATE3",
				Util.trim(TWNDate.toAD(l170m01c.getFromDate3())));
		rptVariableMap.put("L170M01C.ENDDATE1",
				Util.trim(TWNDate.toAD(l170m01c.getEndDate1())));
		rptVariableMap.put("L170M01C.ENDDATE2",
				Util.trim(TWNDate.toAD(l170m01c.getEndDate2())));
		rptVariableMap.put("L170M01C.ENDDATE3",
				Util.trim(TWNDate.toAD(l170m01c.getEndDate3())));
		rptVariableMap.put("L170M01C.AMT11", _toStr_C_Amt(l170m01c.getAmt11()));
		rptVariableMap.put("L170M01C.AMT12", _toStr_C_Amt(l170m01c.getAmt12()));
		rptVariableMap.put("L170M01C.AMT13", _toStr_C_Amt(l170m01c.getAmt13()));
		rptVariableMap.put("L170M01C.AMT21", _toStr_C_Amt(l170m01c.getAmt21()));
		rptVariableMap.put("L170M01C.AMT22", _toStr_C_Amt(l170m01c.getAmt22()));
		rptVariableMap.put("L170M01C.AMT23", _toStr_C_Amt(l170m01c.getAmt23()));
		rptVariableMap.put("L170M01C.AMT31", _toStr_C_Amt(l170m01c.getAmt31()));
		rptVariableMap.put("L170M01C.AMT32", _toStr_C_Amt(l170m01c.getAmt32()));
		rptVariableMap.put("L170M01C.AMT33", _toStr_C_Amt(l170m01c.getAmt33()));
		rptVariableMap.put("L170M01C.RATEDATE1",
				Util.trim(TWNDate.toAD(l170m01c.getRateDate1())));
		rptVariableMap.put("L170M01C.RATEDATE2",
				Util.trim(TWNDate.toAD(l170m01c.getRateDate2())));
		rptVariableMap.put("L170M01C.RATEDATE3",
				Util.trim(TWNDate.toAD(l170m01c.getRateDate3())));
		rptVariableMap.put("L170M01C.RATIONO1", LMSUtil.getDesc(finRationMap,
				Util.trim(l170m01c.getRatioNo1())));
		rptVariableMap.put("L170M01C.RATIONO2", LMSUtil.getDesc(finRationMap,
				Util.trim(l170m01c.getRatioNo2())));
		rptVariableMap.put("L170M01C.RATIONO3", LMSUtil.getDesc(finRationMap,
				Util.trim(l170m01c.getRatioNo3())));
		rptVariableMap.put("L170M01C.RATIONO4", LMSUtil.getDesc(finRationMap,
				Util.trim(l170m01c.getRatioNo4())));
		rptVariableMap.put("L170M01C.RATE11",
				_toStr_C_Rate(l170m01c.getRate11()));
		rptVariableMap.put("L170M01C.RATE12",
				_toStr_C_Rate(l170m01c.getRate12()));
		rptVariableMap.put("L170M01C.RATE13",
				_toStr_C_Rate(l170m01c.getRate13()));
		rptVariableMap.put("L170M01C.RATE14",
				_toStr_C_Rate(l170m01c.getRate14()));
		rptVariableMap.put("L170M01C.RATE21",
				_toStr_C_Rate(l170m01c.getRate21()));
		rptVariableMap.put("L170M01C.RATE22",
				_toStr_C_Rate(l170m01c.getRate22()));
		rptVariableMap.put("L170M01C.RATE23",
				_toStr_C_Rate(l170m01c.getRate23()));
		rptVariableMap.put("L170M01C.RATE24",
				_toStr_C_Rate(l170m01c.getRate24()));
		rptVariableMap.put("L170M01C.RATE31",
				_toStr_C_Rate(l170m01c.getRate31()));
		rptVariableMap.put("L170M01C.RATE32",
				_toStr_C_Rate(l170m01c.getRate32()));
		rptVariableMap.put("L170M01C.RATE33",
				_toStr_C_Rate(l170m01c.getRate33()));
		rptVariableMap.put("L170M01C.RATE34",
				_toStr_C_Rate(l170m01c.getRate34()));

		return rptVariableMap;
	}

	/**
	 * 參考 LMS170M01 ReString
	 */
	private Map<String, String> setL170M01DData(
			Map<String, String> rptVariableMap, List<L170M01D> l170m01d_list,
			Properties prop_lms1700m01,L170M01A meta) {

		for (L170M01D l170m01d : l170m01d_list) {
			if (Util.equals(LrsUtil.Z_電腦建檔資料, l170m01d.getItemType())) {
				// 暫先不抓 Z_電腦建檔
				continue;
			}
			// J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
			if (Util.equals(LrsUtil.Y_履約條件, l170m01d.getItemType())) {
				// 暫先不抓 Y_履約條件
				continue;
			}

			// J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
			if (Util.equals(LrsUtil.X_土建融, l170m01d.getItemType())) {
				if (Util.notEquals(LrsUtil.XA1A, l170m01d.getItemNo())) {
					// 暫先不抓 X_土建融
					continue;
				} else {
					// LrsUtil.XA1A
					L170M01A l170m01a = retrialService
							.findL170M01A_mainId(l170m01d.getMainId());
					if (LrsUtil.compareRptVersion(l170m01a.getRptId(), ">=",
							LrsUtil.V_20170603)
							&& Util.equals(Util.trim(l170m01a.getRealRpFg()),
									"Y")) {
						// V_20170603 且 為實地覆審報告表 要印土建融XA1A
					} else {
						continue;
					}
				}
			}
			
			if (LrsUtil.compareRptVersion(meta.getRptId(), ">=",
					LrsUtil.V_20240601)
					&& Util.equals(Util.trim(meta.getRealRpFg()), "Y")
					&& Util.equals(LrsUtil.N033, l170m01d.getItemNo())) {
				// J-113-0204 新增及修正說明文句
				// 實地覆審表，沒有該項目
				continue;
			}

			rptVariableMap = getCHKYN_NOT_Z(l170m01d, rptVariableMap,
					prop_lms1700m01,meta);
		}
		rptVariableMap.put("L170M01D.ITEMCONTENTS",
				_get_L170M01D_ITEMCONTENTS(l170m01d_list, prop_lms1700m01));

		return rptVariableMap;
	}

	private Map<String, String> setL170M01F_GData(
			Map<String, String> rptVariableMap, L170M01A meta,
			L170M01F l170m01f, List<L170M01G> l170m01g_list,
			Properties prop_lms1700m01, String l5DescFlag) {
		String retialComm = toBox(DEFAULT_FMT, prop_lms1700m01,
				Util.trim(l170m01f.getRetialComm()), 3);
		String condition = Util.trim(l170m01f.getCondition());
		if (Util.equals("1", l170m01f.getConFlag())) {
			condition = "覆審正常" + (Util.isNotEmpty(condition) ? "，" : "")
					+ condition;
		}

		rptVariableMap.put("L170M01F.RETIALCOMM", retialComm);
		rptVariableMap.put("L170M01F.CONDITION", condition);
		rptVariableMap.put("L170M01F.BRANCHCOMM",
				Util.trim(l170m01f.getBranchComm()));
		rptVariableMap.put("L170M01G.1STAFFJOBL1",
				_toStrSign(l170m01g_list, lrsConstants.BRANCHTYPE.受檢單位, "L1"));

		// J-111-0033_05097_B1001 Web e-Loan
		// 企金覆審報告表(一般、含土建融、實地，分行自辦用及授管中心用)皆新曾帳戶管理員用印欄位
		rptVariableMap.put("L170M01G.1STAFFJOBL2",
				_toStrSign(l170m01g_list, lrsConstants.BRANCHTYPE.受檢單位, "L2"));

		rptVariableMap.put("L170M01G.1STAFFJOBL4",
				_toStrSign(l170m01g_list, lrsConstants.BRANCHTYPE.受檢單位, "L4"));
		rptVariableMap.put("L170M01G.1STAFFJOBL5",
				_toStrSign(l170m01g_list, lrsConstants.BRANCHTYPE.受檢單位, "L5"));
		rptVariableMap.put("L170M01G.2STAFFJOBL1",
				_toStrSign(l170m01g_list, lrsConstants.BRANCHTYPE.覆審單位, "L1"));
		rptVariableMap.put("L170M01G.2STAFFJOBL4",
				_toStrSign(l170m01g_list, lrsConstants.BRANCHTYPE.覆審單位, "L4"));
		rptVariableMap.put("L170M01G.2STAFFJOBL5",
				_toStrSign(l170m01g_list, lrsConstants.BRANCHTYPE.覆審單位, "L5"));

		if (Util.equals(meta.getCtlType(), LrsUtil.CTLTYPE_自辦覆審)) {
			// 20180102 郭慧珠 NOTES MAIL 實地覆審報告表 附件 附表五之1
			rptVariableMap.put(
					"L170M01G.1STAFFJOBL5NAME",
					(Util.equals("007", meta.getOwnBrId())
							|| Util.equals("201", meta.getOwnBrId())
							|| Util.equals("025", meta.getOwnBrId())
							|| Util.equals("149", meta.getOwnBrId()) ? "經副襄理"
							: "經副襄理"));
			rptVariableMap.put(
					"L170M01G.2STAFFJOBL5NAME",
					(Util.equals("007", meta.getOwnBrId())
							|| Util.equals("201", meta.getOwnBrId())
							|| Util.equals("025", meta.getOwnBrId())
							|| Util.equals("149", meta.getOwnBrId()) ? "經副襄理"
							: "單位主管"));
		} else if (Util.equals(meta.getCtlType(), LrsUtil.CTLTYPE_價金履約)) {
			rptVariableMap.put("L170M01G.1STAFFJOBL5NAME",
					(Util.equals("Y", l5DescFlag) ? "單位主管" : "經副襄理"));
			rptVariableMap.put("L170M01G.2STAFFJOBL5NAME",
					(Util.equals("Y", l5DescFlag) ? "正/副營運長" : "單位主管"));
		} else {
			rptVariableMap.put(
					"L170M01G.1STAFFJOBL5NAME",
					(Util.equals("007", meta.getOwnBrId())
							|| Util.equals("201", meta.getOwnBrId())
							|| Util.equals("025", meta.getOwnBrId())
							|| Util.equals("149", meta.getOwnBrId()) ? "經副襄理"
							: "經理"));
		}

		return rptVariableMap;
	}

	private String _toStrSign(List<L170M01G> l170m01g_list, String branchType,
			String staffJob) {
		List<String> r = new ArrayList<String>();
		List<L170M01G> filter_list = retrialService
				.findL170M01G_byBranchTypeStaffJob(l170m01g_list, branchType,
						staffJob);
		if (CollectionUtils.isNotEmpty(filter_list)) {
			for (L170M01G l170m01g : filter_list) {
				String staffNo = Util.trim(l170m01g.getStaffNo());
				String staffName = Util.trim(lmsService.getUserName(l170m01g
						.getStaffNo()));
				r.add(Util.trim(Util.isNotEmpty(staffName) ? staffName
						: staffNo));
			}
		}
		// return StringUtils.join(r, "、");
		return StringUtils.join(r, "\r");
	}

	/**
	 * 靠右對齊
	 */
	private String _toStr_C_Rate(BigDecimal d) {
		if (d == null) {
			return "";
		}
		return NumConverter.addComma(d) + "%";

	}

	/**
	 * 靠右對齊
	 */
	private String _toStr_C_Amt(BigDecimal d) {
		if (d == null) {
			return "";
		}
		return NumConverter.addComma(d);
	}

	private String toBox(String result_fmt, Properties prop_lms1700m01,
			String s, int spaceLen) {
		String addStr = Util.addSpaceWithValue("", spaceLen);

		String[] valSplit = Util.trim(result_fmt).split("\\|");
		if (valSplit == null || valSplit.length == 0) {
			return s;
		} else {
			List<String> r = new ArrayList<String>();
			for (String val : valSplit) {
				String showStr = (Util.equals(s, Util.getLeftStr(val, 1)) ? ON_BOX
						: OFF_BOX)
						+ prop_lms1700m01.getProperty("label." + val);
				r.add(showStr);
			}
			return StringUtils.join(r, addStr);
		}
	}

	private Map<String, String> getCHKYN_NOT_Z(L170M01D l170m01d,
			Map<String, String> rptVariableMap, Properties prop_lms1700m01,L170M01A meta) {

		Integer itemSeq = l170m01d.getItemSeq();
		if (true) {
			// 正常的項目塞值
			if (LrsUtil.compareRptVersion(meta.getRptId(), ">=",
					LrsUtil.V_20240601)
					&& Util.equals(Util.trim(meta.getRealRpFg()), "Y")
					&& (Util.equals(Util.trim(l170m01d.getItemType()), "B") || Util
							.equals(Util.trim(l170m01d.getItemType()), "C"))) {
				itemSeq = (l170m01d.getItemSeq() - 1);
			}
			rptVariableMap.put("L170M01D.ITEM" + itemSeq + ".DESC",
					LrsUtil.getPrintItemContent(l170m01d, prop_lms1700m01));
			
			String ra = "";
			String r1 = "";
			String rb = "";
			String r2 = "";

			String[] fmt = StringUtils.split(LrsUtil.chkResult_fmt(l170m01d),
					"|");
			if (fmt.length == 3) {
				ra = prop_lms1700m01.getProperty("label." + fmt[0]);
				rb = prop_lms1700m01.getProperty("label." + fmt[1]);

				if (Util.equals(l170m01d.getChkResult(),
						Util.getLeftStr(fmt[0], 1))) {
					r1 = "V";
				} else if (Util.equals(l170m01d.getChkResult(),
						Util.getLeftStr(fmt[1], 1))) {
					r2 = "V";
				} else if (Util.equals(l170m01d.getChkResult(),
						Util.getLeftStr(fmt[2], 1))) {
					r1 = "─";
					r2 = "─";
				}
			}
			rptVariableMap.put("L170M01D.ITEM" + itemSeq + ".RA", ra);
			rptVariableMap.put("L170M01D.ITEM" + itemSeq + ".R1", r1);
			rptVariableMap.put("L170M01D.ITEM" + itemSeq + ".RB", rb);
			rptVariableMap.put("L170M01D.ITEM" + itemSeq + ".R2", r2);
		}

		// J-106-0145-001 Web e-Loan
		// 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行************************************
		boolean n015_20170603 = false;
		L170M01A l170m01a = meta;
		if (Util.equals(LrsUtil.N015, l170m01d.getItemNo())) {
			//l170m01a = retrialService.findL170M01A_mainId(l170m01d.getMainId());
			if (LrsUtil.compareRptVersion(l170m01a.getRptId(), ">=",
					LrsUtil.V_20170603)) {
				n015_20170603 = true;
			}
		}

		if (n015_20170603) {
			// 若有違反承諾或約定事項是否依核定條件處置？
			String yResult = "K";
			L170M01D l170m01d_YA13 = l170m01dDao.findByUniqueKey(
					l170m01a.getMainId(), l170m01a.getCustId(),
					l170m01a.getDupNo(), "YA13");
			L170M01D l170m01d_YB13 = l170m01dDao.findByUniqueKey(
					l170m01a.getMainId(), l170m01a.getCustId(),
					l170m01a.getDupNo(), "YB13");
			if (l170m01d_YA13 != null && l170m01d_YB13 != null) {
				String ya13 = Util.trim(l170m01d_YA13.getChkResult());
				String yb13 = Util.trim(l170m01d_YB13.getChkResult());

				// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
				if (Util.equals(ya13, "N") || Util.equals(yb13, "N")) {
					yResult = "N";
				} else if (Util.equals(ya13, "Y") || Util.equals(yb13, "Y")) {
					yResult = "Y";
				} else {
					yResult = "K";
				}

			}

			String ra = "";
			String r1 = "";
			String rb = "";
			String r2 = "";

			String[] fmt = StringUtils.split("Y|N|K", "|");
			if (fmt.length == 3) {
				ra = prop_lms1700m01.getProperty("label." + fmt[0]);
				rb = prop_lms1700m01.getProperty("label." + fmt[1]);

				if (Util.equals(yResult, Util.getLeftStr(fmt[0], 1))) {
					r1 = "V";
				} else if (Util.equals(yResult, Util.getLeftStr(fmt[1], 1))) {
					r2 = "V";
				} else if (Util.equals(yResult, Util.getLeftStr(fmt[2], 1))) {
					r1 = "─";
					r2 = "─";
				}
			}
			// 20241119,07623,修正V_20240601實地覆審itemSeq會多一會變L170M01D.ITEM18_1.R1,導致L170M01D.ITEM17_1.RA..等無法印出
			rptVariableMap.put("L170M01D.ITEM" + itemSeq + "_1.RA", ra);
			rptVariableMap.put("L170M01D.ITEM" + itemSeq + "_1.R1", r1);
			rptVariableMap.put("L170M01D.ITEM" + itemSeq + "_1.RB", rb);
			rptVariableMap.put("L170M01D.ITEM" + itemSeq + "_1.R2", r2);
		}

		return rptVariableMap;
	}

	public String getChineseChapterNum(Properties prop_lms1700m01,
			int chapter_num) {

		// L170M01A.chapter_num_1=壹
		// L170M01A.chapter_num_2=貳
		// L170M01A.chapter_num_3=參

		return Util.trim(prop_lms1700m01.getProperty("L170M01A.chapter_num_"
				+ new BigDecimal(chapter_num).toPlainString()));
	}

	// J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
	private Set<String> _hide_X_ItemNo(List<L170M01D> l170m01d_list) {
		Set<String> r = new HashSet<String>();
		boolean hide_YA1A = false;
		boolean hide_YB1A = false;
		// J-109-0336 檢視事項及承諾事項之管控機制 - 第三項 always 都顯示
		// boolean hide_YA13 = true;
		// boolean hide_YB13 = true;
		for (L170M01D l170m01d : l170m01d_list) {
			if (Util.equals(l170m01d.getItemNo(), LrsUtil.YA1A)) {
				hide_YA1A = Util.equals("N", l170m01d.getChkResult());
			} else if (Util.equals(l170m01d.getItemNo(), LrsUtil.YB1A)) {
				hide_YB1A = Util.equals("N", l170m01d.getChkResult());
			}

			// J-105-0287-003 修改Web e-Loan國內企金授信覆審系統履行條件之檢核判斷
			// J-109-0336 檢視事項及承諾事項之管控機制 - 第三項 always 都顯示
			/*
			 * if (Util.equals(l170m01d.getItemNo(), LrsUtil.YA11) ||
			 * Util.equals(l170m01d.getItemNo(), LrsUtil.YA12)) { if
			 * (Util.equals("N", l170m01d.getChkResult())) { hide_YA13 = false;
			 * } }
			 * 
			 * if (Util.equals(l170m01d.getItemNo(), LrsUtil.YB11) ||
			 * Util.equals(l170m01d.getItemNo(), LrsUtil.YB12)) { if
			 * (Util.equals("N", l170m01d.getChkResult())) { hide_YB13 = false;
			 * } }
			 */

		}
		if (hide_YA1A) {
			r.add(LrsUtil.YA11);
			r.add(LrsUtil.YA12);
			r.add(LrsUtil.YA13);
		} else {
			// J-105-0287-003 修改Web e-Loan國內企金授信覆審系統履行條件之檢核判斷
			// J-109-0336 檢視事項及承諾事項之管控機制 - 第三項 always 都顯示
			// if (hide_YA13) {
			// r.add(LrsUtil.YA13);
			// }
		}
		if (hide_YB1A) {
			r.add(LrsUtil.YB11);
			r.add(LrsUtil.YB12);
			r.add(LrsUtil.YB13);
		} else {
			// J-105-0287-003 修改Web e-Loan國內企金授信覆審系統履行條件之檢核判斷
			// J-109-0336 檢視事項及承諾事項之管控機制 - 第三項 always 都顯示
			// if (hide_YB13) {
			// r.add(LrsUtil.YB13);
			// }
		}

		return r;
	}

	public String replaceXnum(String itemNo, String itemContent) {
		String rtnItemContent = "";
		if (Util.equals(itemNo, LrsUtil.XA11)) {
			rtnItemContent = StringUtils.replace(itemContent, "(1)", "一、");
		} else if (Util.equals(itemNo, LrsUtil.XA12)) {
			rtnItemContent = StringUtils.replace(itemContent, "(2)", "二、");
		} else if (Util.equals(itemNo, LrsUtil.XA13)) {
			rtnItemContent = StringUtils.replace(itemContent, "(3)", "三、");
		} else if (Util.equals(itemNo, LrsUtil.XA14)) {
			rtnItemContent = StringUtils.replace(itemContent, "(4)", "四、");
		} else {
			rtnItemContent = itemContent;
		}

		return rtnItemContent;
	}

	// J-107-0254_09301_B1001 配合授審處增加「對合作房仲業價金履約保證額度覆審報告表」
	private Map<String, String> setDynamicL170M01DData(
			Map<String, String> rptVariableMap, List<L170M01D> L170m01dList,
			Properties rptProperties) {
		StringBuilder stab1 = new StringBuilder();
		ArrayList<String> strList = new ArrayList<String>(); // ITEMCONTENTS
		JSONObject typeJson = new JSONObject();

		for (L170M01D l170m01d : L170m01dList) { // 備註說明
			String type = l170m01d.getItemType();

			int count = Util.parseInt((String) typeJson.get(type));
			typeJson.put(type, String.valueOf(++count));

			String itemSeq = l170m01d.getItemSeqShow();
			String itemNo = l170m01d.getItemNo();

			String item = "";
			item = rptProperties.getProperty("L170M01D.SPE") + itemSeq
					+ rptProperties.getProperty("L170M01D.ITEM");
			if (Util.equals("B005", itemNo)) {
				strList.add(item
						+ "："
						+ LrsUtil.CTLTYPE_C_B005_prefix
						+ (Util.isNotEmpty(l170m01d.getChkText()) ? (" " + l170m01d
								.getChkText()) : ""));
			} else if (Util.equals("B009", itemNo)) {
				strList.add(item
						+ "："
						+ LrsUtil.CTLTYPE_C_B009_prefix
						+ (Util.isNotEmpty(l170m01d.getChkText()) ? (" " + l170m01d
								.getChkText()) : ""));
			} else if (Util.isNotEmpty(l170m01d.getChkText())) {
				if (Util.isEmpty(itemSeq)) {
					strList.add(l170m01d.getChkText());
				} else {
					strList.add(item + "." + l170m01d.getChkText());
				}
			}
		}
		int itemRows = typeJson.getInt("A") + typeJson.getInt("B")
				+ typeJson.getInt("C");

		// Creat stab1 HTML CODE
		stab1.append("<style> table,td { border: 1px solid black; border-collapse: collapse;} </style>");
		stab1.append("<table border=\"1px\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 100%; border: 1px #000000 solid; \">");
		stab1.append("<tbody>");
		stab1.append("<tr height=\"1px\">");
		stab1.append("<td cellpadding=\"0\" cellspacing=\"0\" colspan=\"3\" style=\"width: 80%; text-align: center;border: 1px solid rgb(0, 0, 0);\">");
		stab1.append("<span style=\"display: inline-block; width: 100%;\">"
				+ rptProperties.getProperty("TITLE.RPTTITLE01") + "</span>");
		stab1.append("</td>");
		stab1.append("<td colspan=\"5\" style=\"width: 20%; border: 1px solid rgb(0, 0, 0); text-align: center;\">");
		stab1.append("<span style=\"display: inline-block; width: 100%;\">"
				+ rptProperties.getProperty("TITLE.RPTTITLE02") + "</span>");
		stab1.append("</td>");
		stab1.append("</tr>");

		int countTable = 0, countType = 0;
		String nowType = "";
		ArrayList<String> borderBottomNone = new ArrayList<String>(
				Arrays.asList("5", "7", "9", "10", "11"));
		for (L170M01D l170m01d : L170m01dList) {
			String ItemType = l170m01d.getItemType();
			String itemSeq = l170m01d.getItemSeqShow();
			String itemNo = l170m01d.getItemNo();

			if (countTable == 0) {
				stab1.append("<tr height=\"1px\">");
				countTable++;
			} else {
				stab1.append("<tr>");
			}

			if (Util.equals("", nowType) || Util.notEquals(nowType, ItemType)) {
				int rowspan = typeJson.getInt(ItemType);
				nowType = ItemType;
				String TTITLE = rptProperties.getProperty("TITLE.RPTTITLE."
						+ ItemType);
				stab1.append("<td rowspan=\""
						+ rowspan
						+ "\" style=\"text-align:center;border: 1px solid rgb(0, 0, 0); width: 2%; vertical-align: middle;text-align: center;\">");
				stab1.append("<span style=\"display: inline-block; width: 100%;\">"
						+ TTITLE + "</span>");
				stab1.append("</td>");
			}

			// 序號
			if (borderBottomNone.contains(itemSeq)) {
				stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 2%; vertical-align: middle;text-align: center;border-bottom-style:none;\">");
			} else if (Util.equals("", itemSeq)) {
				stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 2%; vertical-align: middle;text-align: center;border-top-style:none;border-bottom-style:none;\">");
			} else {
				stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 2%; vertical-align: middle;text-align: center;\">");
			}
			stab1.append("<span style=\"display: inline-block; width: 100%;\">"
					+ itemSeq + "</span>");
			stab1.append("</td>");

			// 項目內容
			if (borderBottomNone.contains(itemSeq)) {
				stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 76%; vertical-align: middle;border-bottom-style:none;\">");
			} else if (Util.equals("", itemSeq)) {
				stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 76%; vertical-align: middle;border-top-style:none;border-bottom-style:none;\">");
			} else {
				stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 76%; vertical-align: middle;\">");
			}
			if (Util.equals("B007", itemNo)) {
				stab1.append("<span style=\"display: inline-block; width: 100%;\">"
						+ l170m01d.getItemContent()
						+ "<br><br>"
						+ LrsUtil.CTLTYPE_C_B007_note + "</span>");
			} else {
				stab1.append("<span style=\"display: inline-block; width: 100%;\">"
						+ l170m01d.getItemContent() + "</span>");
			}
			stab1.append("</td>");

			// 覆審結果
			String ra = "";
			String r1 = "";
			String rb = "";
			String r2 = "";
			if (Util.equals("B014", itemNo)) {
				ra = rptProperties.getProperty("label.N");
				rb = rptProperties.getProperty("label.Y");
				if (Util.equals("N", l170m01d.getChkResult())) {
					r1 = "V";
				} else if (Util.equals("Y", l170m01d.getChkResult())) {
					r2 = "V";
				} else if (Util.equals("K", l170m01d.getChkResult())) {
					r1 = "&ndash;"; // -
					r2 = "&ndash;";
				}
			} else {
				ra = rptProperties.getProperty("label.Y");
				rb = rptProperties.getProperty("label.N");
				if (Util.equals("Y", l170m01d.getChkResult())) {
					r1 = "V";
				} else if (Util.equals("N", l170m01d.getChkResult())) {
					r2 = "V";
				} else if (Util.equals("K", l170m01d.getChkResult())) {
					r1 = "&ndash;"; // -
					r2 = "&ndash;";
				}
			}
			if (borderBottomNone.contains(itemSeq)) {
				stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 3%; vertical-align: middle;text-align: center;border-bottom-style:none;\">");
			} else if (Util.equals("", itemSeq)) {
				stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 3%; vertical-align: middle;text-align: center;border-top-style:none;border-bottom-style:none;\">");
			} else {
				stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 3%; vertical-align: middle;text-align: center;\">");
			}
			if (Util.equals("B007", itemNo)) {
				stab1.append("<span>" + ra + "<br><br><br><br></span>");
			} else {
				stab1.append("<span>" + ra + "</span>");
			}
			stab1.append("</td>");
			if (borderBottomNone.contains(itemSeq)) {
				stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 3%; vertical-align: middle;text-align: center;border-bottom-style:none;\">");
			} else if (Util.equals("", itemSeq)) {
				stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 3%; vertical-align: middle;text-align: center;border-top-style:none;border-bottom-style:none;\">");
			} else {
				stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 3%; vertical-align: middle;text-align: center;\">");
			}
			if (Util.equals("B007", itemNo)) {
				stab1.append("<span>" + r1 + "<br><br><br><br></span>");
			} else {
				stab1.append("<span>" + r1 + "</span>");
			}
			stab1.append("</td>");
			if (borderBottomNone.contains(itemSeq)) {
				stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 3%; vertical-align: middle;text-align: center;border-bottom-style:none;\">");
			} else if (Util.equals("", itemSeq)) {
				stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 3%; vertical-align: middle;text-align: center;border-top-style:none;border-bottom-style:none;\">");
			} else {
				stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 3%; vertical-align: middle;text-align: center;\">");
			}
			if (Util.equals("B007", itemNo)) {
				stab1.append("<span>" + rb + "<br><br><br><br></span>");
			} else {
				stab1.append("<span>" + rb + "</span>");
			}
			stab1.append("</td>");
			if (borderBottomNone.contains(itemSeq)) {
				stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 3%; vertical-align: middle;text-align: center;border-bottom-style:none;\">");
			} else if (Util.equals("", itemSeq)) {
				stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 3%; vertical-align: middle;text-align: center;border-top-style:none;border-bottom-style:none;\">");
			} else {
				stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 3%; vertical-align: middle;text-align: center;\">");
			}
			if (Util.equals("B007", itemNo)) {
				stab1.append("<span>" + r2 + "<br><br><br><br></span>");
			} else {
				stab1.append("<span>" + r2 + "</span>");
			}
			stab1.append("</td>");

			// 覆審結果內容
			if (Util.equals("A", ItemType) && countType == 0) {
				stab1.append("<td rowspan=\""
						+ itemRows
						+ "\" style=\"border: 1px solid rgb(0, 0, 0); width: 20%; vertical-align: top;\">");
				stab1.append("<span>" + StringUtils.join(strList, "<br><br>")
						+ "</span>");
				stab1.append("</td>");
				countType++;
			}
		}

		stab1.append("</tr>");
		stab1.append("</tbody>");
		stab1.append("</table>");
		rptVariableMap.put("L170M01D.strTable", stab1.toString());
		return rptVariableMap;
	}
}
