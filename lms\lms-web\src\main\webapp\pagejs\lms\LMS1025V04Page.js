pageJsInit(function() {
	$(function() {
		var mainId = "lms1025v04docfile000000000000000";
		var grid = $("#gridview").iGrid({
			handler: "lms1025gridhandler",
			action: "queryRatingTbl",
			needPager: false,
			height: '400',
			postData: {
				mainId: mainId
			},
			colModel: [{
				colHeader: i18n.lms1025v04["grid.001"],//"評等級數總表",
				name: "srcFileName",
				sortable: false,
				formatter: 'click',
				onclick: function(cellvalue, options, rowObject) {
					$.capFileDownload({
						target: "_self",
						handler: "simplefiledwnhandler",
						data: {
							fileOid: rowObject.oid
						}
					});
				}
			}, {
				colHeader: i18n.def["lastUpdater"],
				name: "fileDesc",
				sortable: false
			}, {
				colHeader: i18n.def["lastUpdateTime"],
				name: "uploadTime",
				sortable: false
			}, {
				name: "oid",
				hidden: true

			}]
		});
		$("#btnUpload1,#btnUpload2").click(function() {
			var thisId = $(this).attr("id");
			switch (thisId) {
				case 'btnUpload1':
					uploadFunction(0);
					break;
				case 'btnUpload2':
					uploadFunction(1);
					break;
			}
		})

		var uploadFunction = function(fileType) {
			var limitFileSize = 3145728;//3*1024*1024
			MegaApi.uploadDialog({
				handler: "lms1025v04fileuploadhandler",
				fieldId: "null",
				subTitle: i18n.def('insertfileSize', {
					'fileSize': (limitFileSize / 1048576).toFixed(2)
				}),
				fileCheck: ['doc', 'pdf', 'docx'],
				successMsg: true,
				height: 140,
				limitSize: limitFileSize,
				data: {
					deleteDup: true,
					fileType: fileType
				},
				}).done(function() {
					grid.trigger('reloadGrid');
					$.thickbox.close();
			});
		}
	});
});
