$(function(){
	var lnForm = $("#lnForm");
	$("#buttonPanelLNDetail").find("#btnSaveLNDetail").click(function(){		
		if(lnForm.valid()){
			return $.ajax({
                type: "POST",
                handler: "lms2411m01formhandler",
                data:$.extend( {
                		formAction: "saveLNDetail"
                    }, 
                    lnForm.serializeData()
                )                
                }).done(function(json_saveLNDetail){
                	if(json_saveLNDetail.saveLNFlag){
                		$("#gridviewC241M01B").trigger("reloadGrid");
                		
                		$.thickbox.close();
                	}
            });
		}
	}).end().find("#btnExitLNDetail").click(function(){		
		$.thickbox.close();
	});
	
	$("#btn_reImpEllngtee").click(function(){
		$.ajax({
            type: "POST",
            handler: "lms2411m01formhandler",
            data:$.extend( {
            		formAction: "impEllngtee"
                }, 
                lnForm.serializeData()
            )                
            }).done(function(json_impEllngtee){
            	//若原本欄位值為 XYZ, 而json 內的值為空白
            	//injectData 不會 set value
            	//所以先清空,再設值
            	lnForm.find("#coBorrower").val("");
            	lnForm.find("#guarantor").val("");
            	
            	lnForm.injectData(json_impEllngtee);
        });
	});	
});
