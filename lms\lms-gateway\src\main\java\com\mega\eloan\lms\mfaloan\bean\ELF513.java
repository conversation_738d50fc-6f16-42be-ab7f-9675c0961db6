/* 
 * ELF513.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.mfaloan.bean;

import java.util.Date;

import javax.persistence.Column;
import javax.validation.constraints.Digits;

import org.apache.wicket.markup.html.form.Check;

import tw.com.iisi.cap.model.GenericBean;

/** 續約次數檔 (此檔案只有ELF513一個檔案) **/
public class ELF513 extends GenericBean {

	private static final long serialVersionUID = 1L;

	/**
	 * 額度序號 CHAR(12) NOT NULL,
	 */
	@Column(name = "ELF513_CONTRACT", length = 12, columnDefinition = "CHAR(12)")
	private String elf513_contract;

	/**
	 * 續約次數 <br/>
	 * ELF513_RENEW_CNT DECIMAL(3, 0) NOT NULL
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "ELF513_RENEW_CNT", columnDefinition = "DECIMAL(3,0)")
	private Integer elf513_renew_cnt;

	/**
	 * 額度序號 CHAR(12) NOT NULL,
	 */
	public String getElf513_contract() {
		return elf513_contract;
	}

	/**
	 * 額度序號 CHAR(12) NOT NULL,
	 */
	public void setElf513_contract(String elf513_contract) {
		this.elf513_contract = elf513_contract;
	}

	/**
	 * 續約次數 <br/>
	 * ELF513_RENEW_CNT DECIMAL(3, 0) NOT NULL
	 */
	public Integer getElf513_renew_cnt() {
		return elf513_renew_cnt;
	}

	/**
	 * 續約次數 <br/>
	 * ELF513_RENEW_CNT DECIMAL(3, 0) NOT NULL
	 */
	public void setElf513_renew_cnt(Integer elf513_renew_cnt) {
		this.elf513_renew_cnt = elf513_renew_cnt;
	}

	/**
	 * 案件核准編號
	 * <p/>
	 * CHAR(20) NOT NULL,
	 */
	@Column(name = "ELF513_DOCUMENT_NO", length = 20, columnDefinition = "CHAR(20)")
	private String elf513_document_no;

	/**
	 * 案件核准編號
	 * <p/>
	 * CHAR(20) NOT NULL,
	 */
	public String getElf513_document_no() {
		return elf513_document_no;
	}

	/**
	 * 案件核准編號
	 * <p/>
	 * CHAR(20) NOT NULL,
	 */
	public void setElf513_document_no(String elf513_document_no) {
		this.elf513_document_no = elf513_document_no;
	}

	/**
	 * 最後維護時間
	 * <p/>
	 * TIMESTAMP NOT NULL
	 */
	@Column(name = "ELF513_TIMESTAMP", columnDefinition = "TIMESTAMP")
	private Date elf513_timestamp;

	/**
	 * 最後維護時間
	 * <p/>
	 * TIMESTAMP NOT NULL
	 */
	public Date getElf513_timestamp() {
		return elf513_timestamp;
	}

	/**
	 * 最後維護時間
	 * <p/>
	 * TIMESTAMP NOT NULL
	 */
	public void setElf513_timestamp(Date elf513_timestamp) {
		this.elf513_timestamp = elf513_timestamp;
	}

}
