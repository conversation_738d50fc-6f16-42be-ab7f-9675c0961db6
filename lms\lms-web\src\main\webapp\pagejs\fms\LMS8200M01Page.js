
var initDfd = $.Deferred(), inits = {
    fhandle: "lms8200m01formhandler",
    ghandle: "lms8200gridhandler"
};

var L820M01S = {
	data: {},
	proc_EJ_ST_query: function(txId, do_query){
		if(do_query=="Y"){
			return $.ajax({
				type : "POST", handler : "lms8200m01formhandler", formId: 'empty',
			    data : {
				   	'formAction' : "keep_EJ_ST_queryOutput" , async: false,//用「同步」的方式	
				   	'oid' : L820M01S.data.oid,
				   	'mainId' : L820M01S.data.mainId,
	                'custId': L820M01S.data.custId,
	            	'dupNo': L820M01S.data.dupNo,
				   	'txId' : txId 
				},
				success:function(json_rtn){
					ilog.debug("L820M01S1_proc_EJ_ST_query["+txId+"] done");
				    L820M01S.print_EJ_ST(json_rtn.txId);
				    $('#L820M01Form').find("#gridview1").trigger("reloadGrid");
				    LMSRpaAction._reloadFormdata();
				}
			});	
		}else{
			var my_dfd = $.Deferred();
			my_dfd.resolve();	
			return my_dfd.promise();
		}
	}, 
    runOneBtnQuery: function(){
    	//button.qry_API_QueryAll_confirmMessage=是否一鍵查詢 身分證領換補、受監護/輔助、財富管理往來？
    	MegaApi.confirmMessage(i18n.lms8200m01['button.qry_API_QueryAll_confirmMessage'], function(action){
            if (action){
            	L820M01S.proc_EJ_ST_query("ID_CARD_CHECK", "Y").done(function(){    
            		//console.log('trigger reload');
//            		$('#L820M01Form').find("#gridview1").trigger("reloadGrid");
//				    LMSRpaAction._reloadFormdata();
            	});
            	L820M01S.proc_EJ_ST_query("FA_QUERY", "Y").done(function(){      
//            		$('#L820M01Form').find("#gridview1").trigger("reloadGrid");
//				    LMSRpaAction._reloadFormdata();
            		
            	});
            	
            }
        });    
    	
    },
	print_EJ_ST: function(txId){
		//我應該不需要用到
		//API.showMessage("無法開啟["+txId+"]");
	}
}


var LMSRpaAction = {
    _isLoad: false,
	beforeGrid: null,
	afterGrid: null,
	fileGrid: null,
	isMegaInternalCntrNo: null,
	realEstateFormDetailData: {},
	_initEvent: function(){
		var defaultSelect = "<option value=''>" + i18n.def.comboSpace + "</option>";
		var $L820M01Form = $("#L820M01Form");
	
		$L820M01Form.find('#btnOnQuery').click(function(){
			L820M01S.runOneBtnQuery(); //一鍵查詢
        });
		
		$L820M01Form.find('#btn_qry_API_L820M01S').click(function(){
			//button.qry_API_L820M01S_confirmMessage=是否 查詢身分證領換補？
        	MegaApi.confirmMessage(i18n.lms8200m01['button.qry_API_L820M01S_confirmMessage'], function(action){
                if (action){ 
                	L820M01S.proc_EJ_ST_query("ID_CARD_CHECK", "Y").done(function(){    
                		console.log('trigger reload');
                		InterfaceSystemDataInquiry.grid1.trigger("reloadGrid");
                		//LMSRpaAction._reloadFormdata();
                	});
                }
            });        	 
        });
		
		$L820M01Form.find('#btn_qry_RPA_L820M01W').click(function(){
			//button.qry_RPA_L820M01W_confirmMessage=是否 查詢受監護/輔助？
        	MegaApi.confirmMessage(i18n.lms8200m01['button.qry_RPA_L820M01W_confirmMessage'], function(action){
                if (action){ 
                	L820M01S.proc_EJ_ST_query("FA_QUERY", "Y").done(function(){      
                		InterfaceSystemDataInquiry.grid1.trigger("reloadGrid");
                		//LMSRpaAction._reloadFormdata();
                	});
                }
            });        	 
        });
		
		$L820M01Form.find('#btn_qry_RPA_L820M01E').click(function(){
			//button.qry_RPA_L820M01E_confirmMessage=是否 查詢財富管理往來？
        	MegaApi.confirmMessage(i18n.lms8200m01['button.qry_API_L820M01E_confirmMessage'], function(action){
                if (action){ 
                	L820M01S.proc_EJ_ST_query("Wealth", "Y").done(function(){      
                		InterfaceSystemDataInquiry.grid1.trigger("reloadGrid");
                		//LMSRpaAction._reloadFormdata();
                	});
                }
            });        	 
        });
		
		$('#mainPanel').find('#btnPhrase').click(function(){
			$('#comm').val(i18n.lms8200m01['L820M01A.importPhrase']); //片語
        });
	},
	_reloadBeforeGrid:function(){
        this.beforeGrid.jqGrid("setGridParam", {//重新設定grid需要查到的資料    	            
            postData: {
                //mainId: responseJSON.mainId,//L820M01S.data.mainId,
                mainId: L820M01S.data.mainId,
                
                flag: "N"
            }
        }).trigger("reloadGrid");
	},
	_reloadAfterGrid:function(){
        this.afterGrid.jqGrid("setGridParam", {//重新設定grid需要查到的資料    	            
            postData: {
                mainId: responseJSON.mainId,//L820M01S.data.mainId,
                mainId: L820M01S.data.mainId,
                flag: "Y"
            }
        }).trigger("reloadGrid");
	},
	_initCaseGrid: function(obj){
		InterfaceSystemDataInquiry.grid1 = $('#mainPanel').find("#caseinf_gridview").iGrid({
			handler: inits.ghandle,
			height: 190,
			width: 930,
			autowidth: false,
			// async:false,
			needPager: false,
			postData: {
				formAction: "queryL820m01C"
				, 'mainId': obj.mainId
			}, 
			colModel: [{
				colHeader: '額度序號',
				name: 'cntrNo',
				width: 260,
				align: "left",
				sortable: false
			}, 
			{
				colHeader: '放款帳號',
				name: 'lnf030_loan_no',
				width: 260,
				align: 'left',
				sortable: false
			}, 
			{
				colHeader: '預計撥款日',
				name: 'estimateTime',
				align: 'center',
				width: 150,
				sortable: false
			},
			{
				colHeader: '實際撥款日',
				name: 'actualTime',
				width: 150,
				align: 'center',
				sortable: false
			},
			{
				name: 'fileSeq',
				hidden: true
			},
			{name: 'oid', hidden: true },
			{name: 'mainId', hidden: true},
			{name: 'custId', hidden: true},
			{name: 'dupNo', hidden: true}
			]
		})
	},
	_initRPAForm: function(){
		$.ajax({
		    handler: inits.fhandle,
		    data: {//把資料轉成json
		        formAction: "queryL820m01a",
		        oid: responseJSON.oid,
				cntrNo: responseJSON.cntrNo,
				mainId: responseJSON.mainId,
				custId: responseJSON.custId,
				dupNo: responseJSON.dupNo,
				custName: responseJSON.custName
		    },
		    success: function(obj){
		    	L820M01S.data = $.extend({
	                noOpenDoc: true
	            }, obj);
		    	
				$("#mainId").val(obj.mainId);
				$("#cntrNo").val(obj.cntrNo);		
				$('body').injectData(obj);
				LMSRpaAction._doRadio(obj);
				
				et = obj.etValue;
				//以房養老案件查詢結果維護
				InterfaceSystemDataInquiry.build(obj);
				//案件資訊
				LMSRpaAction._initCaseGrid(obj);

		    }
		});

	},
	_reloadFormdata: function(){
		$.ajax({
		    handler: inits.fhandle,
		    data: {//把資料轉成json
		        formAction: "queryL820m01a",
		        oid: responseJSON.oid,
				cntrNo: responseJSON.cntrNo,
				mainId: responseJSON.mainId,
				custId: responseJSON.custId,
				dupNo: responseJSON.dupNo,
				custName: responseJSON.custName
		    },
		    success: function(obj){
				LMSRpaAction._doRadio(obj);
		    }
		});
	},
	_doRadio: function(obj){
		//內政部戶政系統-身分證補換發查詢  /通過:Y  /未通過:N  /查詢中:I
		if (obj.ideSearchResult == "" || obj.ideSearchResult == null || obj.ideSearchResult == undefined || obj.ideSearchResult == "I") {
            $("[name='ej_ST_ID_CARD_CHECK'][value='I']").attr("checked", "checked");
        } else if (obj.ideSearchResult == 'Y') {
            $("[name='ej_ST_ID_CARD_CHECK'][value='Y']").attr("checked", "checked");
        } else if (obj.ideSearchResult == 'N') {
            $("[name='ej_ST_ID_CARD_CHECK'][value='N']").attr("checked", "checked");
        }
		//司法院家事查詢 /通過:Y  /未通過:N  /查詢中:I
		if (obj.FaSearchResult == "" || obj.FaSearchResult == null || obj.FaSearchResult == undefined || obj.FaSearchResult == "I") {
			$("[name='ej_ST_FA_QUERY'][value='I']").attr("checked", "checked");
        } else if (obj.FaSearchResult == 'Y') {
            $("[name='ej_ST_FA_QUERY'][value='Y']").attr("checked", "checked");
        } else if (obj.FaSearchResult == 'N') {
            $("[name='ej_ST_FA_QUERY'][value='N']").attr("checked", "checked");
        }
		//司法院家事查詢 /通過:Y  /未通過:N  /查詢中:I
		if (obj.WealthResult == "" || obj.WealthResult == null || obj.WealthResult == undefined) {
			$("[name='ej_ST_C350_QUERY'][value='I']").attr("checked", "checked");
        } else if (obj.WealthResult == 'Y') {
            $("[name='ej_ST_C350_QUERY'][value='Y']").attr("checked", "checked");
        } else if (obj.WealthResult == 'N') {
            $("[name='ej_ST_C350_QUERY'][value='N']").attr("checked", "checked");
        }
	},
	_init: function(){
        if (!this._isLoad) {
        	//以房養老案件查詢結果
            this._initRPAForm();
            this._initEvent();
            //this.initFileGrid();
            this._isLoad = true;   
            CommonAPI.triggerOpener("gridview", "reloadGrid");//刷新母頁面
        }
        else {
            this._reloadFormdata();
        }
    },
    setEditEstateBoxUI: function(){
    	
    },
    openEditEstateBox: function(cellvalue, options, data){},
	isShowFactoryBuyingOption: function(estateTypeCode){}
};

// 驗證readOnly狀態
function checkReadonly(){
    var auth = (responseJSON ? responseJSON.Auth : {}); // 權限
    // auth.readOnly ||
    if (auth.readOnly || responseJSON.mainDocStatus != "01O") {
        return true;
    }
    return false;
}

var et;
//import Data


$(document).ready(function(){
	//initData();
    if (checkReadonly()) {
        $(".readOnlyhide").hide();
        //$("form").lockDoc();//這會所全部
		_openerLockDoc="1";
    }
	LMSRpaAction._init();
	$("#check1").show();


	// 呈主管覆核 選授信主管人數
    $("#numPerson").change(function(){
        $('#bossItem').empty();
        var value = $(this).val();
        if (value) {
            var html = '';
            for (var i = 1; i <= value; i++) {
                var name = 'boss' + i;
                html += i + '. '
                // || '授信主管'
                html += '<select id="' + name + '" name="boss"' +
                '" class="required" CommonManager="kind:2;type:2" />';
                html += '<br/>';
            }
            $('#bossItem').append(html).find('select').each(function(){
                $(this).setItems({
                    item: item,
                    format: "{value} {key}"
                });
            });
        }     
    });

    
    $("input[name=queryType]").click(function(){
    	//J-112-0586_05097_B1002 依據簽會-2023-2192「Web eLoan-Checkmarx弱點改善會議」按季追蹤弱點修正進度
    	var queryType = DOMPurify.sanitize($(this).val());
        $(".queryInput").hide();
        $("#queryInput_" + queryType).show();
    });
	
	var btn = $("#buttonPanel");
    btn.find("#btnSave").click(function(showMsg){
        saveData(true);
    }).end().find("#btnSend").click(function(){
    	 //saveData(false, confirmMsg);//多層
    	 saveData(false, sendNoBoss);//單層
    }).end().find("#btnCheck").click(function(){
        openCheck();
    }).end().find("#btnPrint").click(function(){
        if (checkReadonly()) {
            printAction();
        }
        else {
            // saveBeforePrint=執行列印將自動儲存資料，是否繼續此動作?
            CommonAPI.confirmMessage(i18n.def["saveBeforePrint"], function(b){
                if (b) {
                    saveData(false, printAction);
                }
            });
        }
    });      
           
    //EVENT END***********************************************************************************************
	
	// 儲存的動作
    function saveData(showMsg, tofn){
		
		// 為檢查UI的值是否皆無異常
		if ($("#mainPanel").valid() == false) {
			return;
		}
		
		var allresult = {};
		var localresult = {};
		var selfresult = {};
		FormAction.open = true;
		$.ajax({
			handler: inits.fhandle,
			data: {// 把資料轉成json
				formAction: "saveL820m01a",
				oid: responseJSON.oid,
				page: responseJSON.page,
				txCode: responseJSON.txCode,
				showMsg: showMsg
			},
			success: function(obj){
		
				if (responseJSON.page == "01") {
					$('body').injectData(obj);
				}		
				
				CommonAPI.triggerOpener("gridview", "reloadGrid");
				if ($("#mainOid").val()) {
					setRequiredSave(false);
				}
				else {
					setRequiredSave(true);
				}

                // 執行列印
                if (!showMsg && tofn) {
                    tofn();
                }
			}
		});
    }
	
	var item;
	// 呈主管前 提示訊息
	function confirmMsg(){
	    var confirmMsg = getConfirmMsg();
        if (confirmMsg != null) {
            CommonAPI.confirmMessage(confirmMsg, function(b){
                if (b) {
                	sendNoBoss();
                } else {
                    return;
                }
            });
        } else {
            sendBoss();
        }
	}
    // 呈主管(不選簽章欄主管) - 編製中
    function sendNoBoss(){
        // 是否呈主管覆核？
        CommonAPI.confirmMessage(i18n.lms8200m01["L820M01B.message01"], function(b){
            if (b) {
                flowAction({
                    page: responseJSON.page,
                    saveData: true,
                    sendBoss: true
                    //                    selectBoss: selectBoss,
                    //                    manager: $("#manager").val()
                });
            }
        });
    }
	
	// 呈主管多層 - 編製中 
    function sendBoss(){
        $.ajax({
            handler: inits.fhandle,
            action: "checkData",
            data: {},
            success: function(json){
                $('#managerItem').empty();
                $('#bossItem').empty();
                item = json.bossList;
                var bhtml = '1. <select id="boss1" name="boss" class="required" CommonManager="kind:2;type:2"/>';
                $('#bossItem').append(bhtml).find('select').each(function(){
                    $(this).setItems({
                        item: item,
                        format: "{value} {key}"
                    });
                });
                var html = '<select id="manager" name="manager" class="required" CommonManager="kind:2;type:2" />';
                $('#managerItem').append(html).find('select').each(function(){
                    $(this).setItems({
                        item: item,
                        format: "{value} {key}"
                    });
                });
                
                // L820M01B.message01=是否呈主管覆核？
                CommonAPI.confirmMessage(i18n.lms8200m01["L820M01B.message01"], function(b){
                    if (b) {
                        $("#selectBossBox").thickbox({
                            // L820M01B.bt14=覆核
                            title: i18n.lms8200m01['L820M01B.bt14'],
                            width: 500,
                            height: 300,
                            modal: true,
                            readOnly: false,
                            valign: "bottom",
                            align: "center",
                            i18n: i18n.def,
                            buttons: {
                                "sure": function(){
                                
                                    var selectBoss = $("select[name^=boss]").map(function(){
                                        return $(this).val();
                                    }).toArray();
                                    
                                    for (var i in selectBoss) {
                                        if (selectBoss[i] == "") {
                                            // L820M01B.error2=請選擇
                                            // L820M01B.bossId=授信主管
                                            return CommonAPI.showErrorMessage(i18n.lms8200m01['checkSelect'] +
                                            i18n.lms8200m01['L820M01B.bossId']);
                                        }
                                    }
                                    if ($("#manager").val() == "") {
                                        // L820M01B.error2=請選擇
                                        // L820M01B.managerId=經副襄理
                                        return CommonAPI.showErrorMessage(i18n.lms8200m01['checkSelect'] +
                                        i18n.lms8200m01['L820M01B.managerId']);
                                    }
                                    // 驗證是否有重複的主管
                                    if (checkArrayRepeat(selectBoss)) {
                                        // L820M01B.message31=主管人員名單重複請重新選擇
                                        return CommonAPI.showErrorMessage(i18n.lms8200m01['L820M01B.message02']);
                                    }
                                    
                                    flowAction({
                                        page: responseJSON.page,
                                        saveData: true,
                                        selectBoss: selectBoss,
                                        manager: $("#manager").val()
                                    });
                                    $.thickbox.close();
                                    
                                },
                                
                                "cancel": function(){
                                    $.thickbox.close();
                                }
                            }
                        });
                    }
                });
            }
        });
    }
	
	// 待覆核 - 覆核
    function openCheck(){
        $("#openCheckBox").thickbox({ // 使用選取的內容進行彈窗
            // L820M01B.bt14=覆核
            title: i18n.lms8200m01['L820M01B.bt14'],
            width: 100,
            height: 100,
            modal: true,
            readOnly: false,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var val = $("[name=checkRadio]:checked").val();
                    if (!val) {
                        // checkSelect=請選擇
                        return CommonAPI.showMessage(i18n.lms8200m01['checkSelect']);
                    }
                    $.thickbox.close();
                    switch (val) {
                        case "1":
                            // 一般退回到編製中01O
                            // L820M01B.message03=該案件是否退回經辦修改？要退回請按【確定】，不退回請按【取消】
                            CommonAPI.confirmMessage(i18n.lms8200m01['L820M01B.message03'], function(b){
                                if (b) {
                                    flowAction({
                                        flowAction: false
                                    });
                                }
                            }); 
                            break;
                        case "3":
                            // L820M01B.message04=該案件是否確定執行核定作業
                            CommonAPI.confirmMessage(i18n.lms8200m01['L820M01B.message04'], function(b){
                                if (b) {
                                    //checkDate();
				                    flowAction({
				                        flowAction: true,
				                        checkDate: CommonAPI.getToday()//forCheckDate
				                    });
                                }
                            });
                            break;
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
	
	function flowAction(sendData){
        $.ajax({
            handler: inits.fhandle,
            data: $.extend({
                formAction: "flowAction",
                mainOid: $("#mainOid").val()
            }, (sendData || {})),
            success: function(){
                CommonAPI.triggerOpener("gridview", "reloadGrid");
				window.close();
            }
        });
    }
	
	// 輸入核定日期視窗
    function checkDate(){
        // 帶入今天日期
        $("#forCheckDate").val(CommonAPI.getToday());
        $("#openChecDatekBox").thickbox({ // 使用選取的內容進行彈窗
            // L820M01B.message05 = 請輸入核定日
            title: i18n.lms8200m01['L820M01B.message05'],
            width: 100,
            height: 100,
            modal: true,
            valign: "bottom",
            align: "center",
            readOnly: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var forCheckDate = $("#forCheckDate").val();
                    if ($.trim(forCheckDate) == "") {
                        // L820M01B.message05 = 請輸入核定日
                        return CommonAPI.showErrorMessage(i18n.lms8200m01['L820M01B.message05']);
                    }
                    flowAction({
                        flowAction: true,
                        checkDate: forCheckDate
                    });
                    $.thickbox.close();
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
	
	// 列印動作
    function printAction(){
        $.form.submit({
            url: "../../simple/FileProcessingService",
            target: "_blank",
            data: {
                mainId: responseJSON.mainId,
                mainOid: responseJSON.oid,
                fileDownloadName: "lms8200r01.pdf",
                serviceName: "lms8200r01rptservice"
            }
        });
    }
	
	// 檢查陣列內容是否重複
    function checkArrayRepeat(arrVal){
        var newArray = [];
        for (var i = arrVal.length; i--;) {
            var val = arrVal[i];
            if ($.inArray(val, newArray) == -1) {
                newArray.push(val);
            }
            else {
                return true;
            }
        }
        return false;
    }

    function getConfirmMsg(){
        var result = null;
        $.ajax({
            type: "POST",
            handler: inits.fhandle,
            async: false,
            data: {
                formAction: "getConfirmMsg",
                mainOid: $("#mainOid").val()
            },
            success: function(responseData){
                var confirmMsg = responseData.confirmMsg;
                if (confirmMsg != undefined && confirmMsg != null && confirmMsg != "") {
                    result = confirmMsg;
                }
            }
        });
        return result;
    }

});


var InterfaceSystemDataInquiry = {
	init: function (obj){	
		$("#dataArchivalRecordData").show();
	},
	grid1: null,
	build : function(obj){
		// 資料建檔記錄查詢
		//console.log("build...");
		//console.log(obj);
		InterfaceSystemDataInquiry.grid1 = $('#L820M01Form').find("#gridview1").iGrid({
			handler: inits.ghandle,
			height: 190,
			width: 930,
			autowidth: false,
			// async:false,
			needPager: false,
			postData: {
				formAction: "queryDataArchivalRecordData"
				, 'mainId': obj.mainId
				, 'custId': obj.custId
				, 'dupNo': obj.dupNo
			}, 
			colModel: [{
				colHeader: '資料類別',
				name: 'dataTypeDesc',
				width: 260,
				align: "left",
				sortable: false
			}, 
			{
				colHeader: '查詢時間',
				name: 'queryDate',
				width: 210,
				align: 'center',
				sortable: false
			}, 
			{
				colHeader: '報表檔案',
				name: 'link',
				align: 'center',
				width: 100,
				sortable: false,
				formatter: 'click',
				onclick: gridOneBtnQueryClickLink
			},
			{
				colHeader: '資料狀態',
				name: 'dataStatus',
				width: 140,
				align: 'center',
				sortable: false
			},
			{
				colHeader: '備註',
				name: 'remark',
				width: 220,
				align: 'left',
				sortable: false
			},
			{
				name: 'fileSeq',
				hidden: true
			},
			{  name: 'dataSrcMemo',hidden: true}
			,{  name: 'oid', hidden: true }
			,{  name: 'mainId', hidden: true}
			,{  name: 'dataType', hidden: true}
			,{  name: 'reportFileType', hidden: true}
			,{  name: 'dataStatusCode', hidden: true}
			]
		})
	},
	downloadDataArchivalRecordFile: function (dataType, fileSeq){
		$.form.submit({
			url: webroot + '/app/simple/FileProcessingService',
			target: "_blank",
			data: {
				'mainId': $("mainId").val(),
				'custId': $("custId").val(),
				'dupNo': $("dupNo").val(),
				'dataType': dataType,
				'fileSeq': fileSeq,
				'serviceName': CLS1131S01.handler == 'cls1131formhandler' ? 'cls1131MixPdfservice' : 'cls1141MixPdfservice',
				fileDownloadName: "xxx.pdf"
			}
		});
	}
}

function gridOneBtnQueryClickLink(cellvalue, options, rowObject){	
	var _oid = rowObject.oid;
	var _dataSrcMemo = rowObject.dataSrcMemo;
	var _dataType = rowObject.dataType;
	var _reportFileType = rowObject.reportFileType;
	
	ilog.debug("gridOneBtnQueryClickLink[_oid="+_oid+"][dataSrcMemo="+_dataSrcMemo+"][dataType="+_dataType+"][reportFileType="+_reportFileType+"]");
	if(_dataSrcMemo=="L820M01S" ){ //當來源是 RPS 相關的 data //行內_身分證驗證
		// JSON格式
		if(_reportFileType == "J"){
			$.form.submit({
	            url: webroot + '/app/fms/lms8200p03',
	            target: "_blank",
	            data: $.extend({
	            	dataType: _dataType
	            }, L820M01S.data)
	        });
		} else {
//			$.form.submit({
//				url: webroot + '/app/simple/FileProcessingService',
//				target: "_blank",
//				data: {
//					oid: _oid,
//					serviceName: _dataSrcMemo=="L820M01S" ? 'cls1131MixPdfservice' : 'cls1141MixPdfservice',
//							fileDownloadName: "xxx.pdf"
//				}
//			});
		}
	}else if(_dataSrcMemo=="L820M01W"){//RPA受監護輔助宣告查詢
		
		if(rowObject.dataStatusCode != 'A02'){
			API.showMessage(rowObject.dataStatus);
			return;
		}
		
		$.form.submit({
				url: webroot + '/app/simple/FileProcessingPage',
				target: "_blank",
				data: {
					oid: rowObject.oid,
					mainId: rowObject.mainId,
					serviceName: 'lms8200RpaFileService',
					fileDownloadName: "xxx.jpg",
					dataSource: _dataSrcMemo
				}
			});
	}else if(_dataSrcMemo=="L820M01E"){//財富管理往來查詢
		//開啟視窗呈現DW查到的資訊
		var l820m01eData;
		$.ajax({
		    handler: inits.fhandle,
		    action: "queryL820m01e",
		    data: {
		    	mainId: L820M01S.data.mainId,
		        custId: L820M01S.data.custId,
		        dupNo: L820M01S.data.dupNo
		    },
		    success: function(obj){
		    	l820m01eData = obj;
		    	console.log(obj);
		    	if(!$.isEmptyObject(obj.mainId)){
            		//L820M01E
		    		l820m01eData = obj;
            	}
		    }
		}).done(function(){
			var info_date = DOMPurify.sanitize(l820m01eData.dwQueryDate);
			if(info_date == "" || info_date == null){
				info_date = DOMPurify.sanitize(l820m01eData.queryTime);//舊資料問題, 沒有就先放查詢時間
			}
			if(info_date != "" && info_date != null){
				info_date = "資料日期: " + info_date.substring(0, 7);// 2024-12
			}
			var form01eForm = $("#L820M01EForm");
    		form01eForm.injectData(l820m01eData);
    		form01eForm.find("#span_c350_info_date").text(info_date);//郭襄臨時要求補上資料日
		});
		
		$("#queryC350Box").thickbox({
			 //L820M01E.details=財富管理往來查詢明細
		     title: i18n.lms8200m01["L820M01E.details"],
		     width: 530,
		     height: 530,
		     modal: true,
		     align: "center",
		     valign: "bottom",
		     readOnly: false,
		     i18n: i18n.def,
		     buttons: {
		         "sure": function(){
		        	 
		             $.thickbox.close();      
		         },

		     }
		});
	}else{
		ilog.debug("oid["+rowObject.oid+"], unKnown dataSrcMemo["+rowObject.dataSrcMemo+"]");
		API.showMessage("無法開啟未定義的連結");
	}
}