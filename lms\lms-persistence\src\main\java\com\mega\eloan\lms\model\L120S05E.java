/* 
 * L120S05A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.jcs.common.Arithmetic;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 借款人集團相關資料檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L120S05E", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "custId", "dupNo" }))
public class L120S05E extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 身分證統編 **/
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(01)")
	private String dupNo;

	/**
	 * 借款人是否為集團企業
	 * <p/>
	 * Y/N（是/否）
	 */
	@Column(name = "GRPFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String grpFlag;

	/**
	 * 資料來源
	 * <p/>
	 * 100/10/20新增<br/>
	 * 1.徵信報告之資字資信簡表<br/>
	 * 2.最新集團企業授信往來資料
	 */
	@Column(name = "GRPSRC", length = 1, columnDefinition = "CHAR(1)")
	private String grpSrc;

	/**
	 * 集團代號
	 * <p/>
	 * 集團名稱：9999ＸＸ集團，根據聯徵中心資料庫顯示，截至YYYY-MM-DD止該集團（含核心人物）於國內金融機構之授信額度（不包含海外分行及OBU
	 * ）為TWD999,999,999仟元，餘額為TWD999,999,999仟元。另截至YYYY-MM-
	 * DD止該集團於本行海外分行之總授信額度為TWD999
	 * ,999,999仟元，餘額為TWD999,999,999仟元。截至YYYY年底該集團之淨值TWD999
	 * ,999,999仟元、營收TWD999,999,999仟元。
	 */
	@Column(name = "GRPNO", length = 4, columnDefinition = "CHAR(4)")
	private String grpNo;

	/**
	 * 集團名稱
	 * <p/>
	 * 100/11/21新增
	 */
	@Column(name = "GRPNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String grpName;

	/**
	 * 單位(預留欄位)
	 * <p/>
	 * 預設：1000
	 */
	@Column(name = "AMTUNIT", columnDefinition = "DECIMAL(13,0)")
	private Long amtUnit;

	/**
	 * 幣別(預留欄位)
	 * <p/>
	 * 預設：TWD
	 */
	@Column(name = "CURR", length = 3, columnDefinition = "CHAR(3)")
	private String curr;

	/**
	 * 資料來源 (根據…顯示)
	 * <p/>
	 * 預設：聯徵中心資料庫<br/>
	 * eg.<br/>
	 * 1.國內授信：聯徵中心資料庫( - )<br/>
	 * 2.本行OBU授信：XXX(YYY/MM/DD)<br/>
	 * 3.本行海外分行授信：XXX(YYY/MM/DD)
	 */
	@Column(name = "ENDDSCR", length = 120, columnDefinition = "VARCHAR(120)")
	private String endDscr;

	/**
	 * 國內授信截止日期 (截至YYYY-MM-DD止)
	 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "ENDDATE", columnDefinition = "DATE")
	private Date endDate;

	/**
	 * 國內授信額度
	 * <p/>
	 * 單位：TWD仟元
	 */
	@Column(name = "FCLTAMT", columnDefinition = "DECIMAL(13,0)")
	private Long fcltAmt;

	/**
	 * 國內授信餘額
	 * <p/>
	 * 單位：TWD仟元
	 */
	@Column(name = "LNAMT", columnDefinition = "DECIMAL(13,0)")
	private Long lnAmt;

	/**
	 * 海外授信截止日期 (截至YYYY-MM-DD止)
	 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "ENDDATE2", columnDefinition = "DATE")
	private Date endDate2;

	/**
	 * 海外授信額度
	 * <p/>
	 * 單位：TWD仟元
	 */
	@Column(name = "FCLTAMT2", columnDefinition = "DECIMAL(13,0)")
	private Long fcltAmt2;

	/**
	 * 海外授信餘額
	 * <p/>
	 * 單位：TWD仟元
	 */
	@Column(name = "LNAMT2", columnDefinition = "DECIMAL(13,0)")
	private Long lnAmt2;

	/**
	 * 截止年底
	 * <p/>
	 * YYYY
	 */
	@Column(name = "ENDYEAR", columnDefinition = "DECIMAL(4,0)")
	private Integer endYear;

	/**
	 * 集團淨值
	 * <p/>
	 * 單位：TWD仟元
	 */
	@Column(name = "GRPNAMT", columnDefinition = "DECIMAL(13,0)")
	private Long grpnAmt;

	/**
	 * 集團營收
	 * <p/>
	 * 單位：TWD仟元
	 */
	@Column(name = "GRPRAMT", columnDefinition = "DECIMAL(13,0)")
	private Long grprAmt;

	/** 資料查詢日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "INQDATE", columnDefinition = "DATE")
	private Date inqDate;

	/**
	 * 授信總額度
	 * <p/>
	 * 單位：TWD仟元<br/>
	 * (totAmtA+totAmtB)
	 */
	@Column(name = "TOTAMT", columnDefinition = "DECIMAL(13,0)")
	private Long totAmt;

	/**
	 * 授信總額度(國內)
	 * <p/>
	 * 100/09/28調整<br/>
	 * 單位：TWD仟元
	 */
	@Column(name = "TOTAMTA", columnDefinition = "DECIMAL(13,0)")
	private Long totAmtA;

	/**
	 * 授信總額度(海外)
	 * <p/>
	 * 100/09/28調整<br/>
	 * 單位：TWD仟元
	 */
	@Column(name = "TOTAMTB", columnDefinition = "DECIMAL(13,0)")
	private Long totAmtB;

	/** 授信總額度占本行淨值 **/
	@Column(name = "TOTMEGA", columnDefinition = "DECIMAL(5,2)")
	private Double totMega;

	/**
	 * 無擔保授信總額度
	 * <p/>
	 * 單位：TWD仟元<br/>
	 * (crdAmtA+crdAmtB)
	 */
	@Column(name = "CRDAMT", columnDefinition = "DECIMAL(13,0)")
	private Long crdAmt;

	/**
	 * 無擔保授信總額度(國內)
	 * <p/>
	 * 100/09/28調整<br/>
	 * 單位：TWD仟元
	 */
	@Column(name = "CRDAMTA", columnDefinition = "DECIMAL(13,0)")
	private Long crdAmtA;

	/**
	 * 無擔保授信總額度(海外)
	 * <p/>
	 * 100/09/28調整<br/>
	 * 單位：TWD仟元
	 */
	@Column(name = "CRDAMTB", columnDefinition = "DECIMAL(13,0)")
	private Long crdAmtB;

	/** 無擔保授信總額度占本行淨值 **/
	@Column(name = "CRDMEGA", columnDefinition = "DECIMAL(5,2)")
	private Double crdMega;

	/**
	 * 授信總餘額
	 * <p/>
	 * 單位：TWD仟元<br/>
	 * (lntAmtA+lntAmtB)
	 */
	@Column(name = "LNTAMT", columnDefinition = "DECIMAL(13,0)")
	private Long lntAmt;

	/**
	 * 授信總餘額(國內)
	 * <p/>
	 * 100/09/28調整<br/>
	 * 單位：TWD仟元
	 */
	@Column(name = "LNTAMTA", columnDefinition = "DECIMAL(13,0)")
	private Long lntAmtA;

	/**
	 * 授信總餘額(海外)
	 * <p/>
	 * 100/09/28調整<br/>
	 * 單位：TWD仟元
	 */
	@Column(name = "LNTAMTB", columnDefinition = "DECIMAL(13,0)")
	private Long lntAmtB;

	/** 授信總餘額占本行淨值 **/
	@Column(name = "LNTMEGA", columnDefinition = "DECIMAL(5,2)")
	private Double lntMega;

	/**
	 * 無擔保授信總餘額
	 * <p/>
	 * 單位：TWD仟元<br/>
	 * (lncAmtA+lncAmtB)
	 */
	@Column(name = "LNCAMT", columnDefinition = "DECIMAL(13,0)")
	private Long lncAmt;

	/**
	 * 無擔保授信總餘額(國內)
	 * <p/>
	 * 100/09/28調整<br/>
	 * 單位：TWD仟元
	 */
	@Column(name = "LNCAMTA", columnDefinition = "DECIMAL(13,0)")
	private Long lncAmtA;

	/**
	 * 無擔保授信總餘額(海外)
	 * <p/>
	 * 100/09/28調整<br/>
	 * 單位：TWD仟元
	 */
	@Column(name = "LNCAMTB", columnDefinition = "DECIMAL(13,0)")
	private Long lncAmtB;

	/** 無擔保授信總餘額占本行淨值 **/
	@Column(name = "LNCMEGA", columnDefinition = "DECIMAL(5,2)")
	private Double lncMega;

	/**
	 * 不計入同一關係企業第一～四項者之合計額度
	 * <p/>
	 * 單位：TWD仟元<br/>
	 * (若扣除該集團依規可不計入同一關係企業第一～四項者之合計額度為TWD999,999,999仟元，占本行淨值的999.99%，合計餘額為TWD999
	 * ,999,999仟元，占本行淨值的999.99%。)
	 */
	@Column(name = "EXCAMT", columnDefinition = "DECIMAL(13,0)")
	private Long excAmt;

	/** 不計入同一關係企業第一～四項者之合計額度占本行淨值 **/
	@Column(name = "EXCMEGA", columnDefinition = "DECIMAL(5,2)")
	private Double excMega;

	/**
	 * 合計餘額
	 * <p/>
	 * 單位：TWD仟元
	 */
	@Column(name = "SUMAMT", columnDefinition = "DECIMAL(13,0)")
	private Long sumAmt;

	/** 占本行淨值 **/
	@Column(name = "SUMMEGA", columnDefinition = "DECIMAL(5,2)")
	private Double sumMega;

	/**
	 * 本行轉投資等非授信業務金額
	 * <p/>
	 * 單位：TWD仟元
	 */
	@Column(name = "OTHAMT", columnDefinition = "DECIMAL(13,0)")
	private Long othAmt;

	/** 非授信業務占本行淨值 **/
	@Column(name = "OTHMEGA", columnDefinition = "DECIMAL(5,2)")
	private Double othMega;

	/**
	 * 金融商品金額
	 * <p/>
	 * 單位：TWD仟元
	 */
	@Column(name = "FINAMT", columnDefinition = "DECIMAL(13,0)")
	private Long finAmt;

	/** 金融商品金額占本行淨值 **/
	@Column(name = "FINMEGA", columnDefinition = "DECIMAL(5,2)")
	private Double finMega;

	/**
	 * 曝險總額
	 * <p/>
	 * 單位：TWD仟元
	 */
	@Column(name = "RSKAMT", columnDefinition = "DECIMAL(13,0)")
	private Long rskAmt;

	/** 曝險總額占本行淨值 **/
	@Column(name = "RSKMEGA", columnDefinition = "DECIMAL(5,2)")
	private Double rskMega;

	/** 集團評等 **/
	@Column(name = "GRPGRRD", length = 2, columnDefinition = "CHAR(2)")
	private String grpGrrd;

	/** 集團評等說明 **/
	@Column(name = "GRPDSCR", length = 512, columnDefinition = "VARCHAR(512)")
	private String grpDscr;

	/**
	 * 本行對該集團授信限額
	 * <p/>
	 * 單位：TWD仟元
	 */
	@Column(name = "LMTAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal lmtAmt;

	/** 本行對該集團授信限額占本行淨值 **/
	@Column(name = "LMTMEGA", columnDefinition = "DECIMAL(5,2)")
	private Double lmtMega;

	/**
	 * 本行對該集團無擔保授信限額
	 * <p/>
	 * 單位：TWD仟元
	 */
	@Column(name = "GCRDAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal gcrdAmt;

	/** 本行對該集團無擔保授信限額占本行淨值 **/
	@Column(name = "GCRDMEGA", columnDefinition = "DECIMAL(5,2)")
	private Double gcrdMega;

	/**
	 * 該集團在全體金融機構授信總餘額?未?逾其淨值或營收
	 * <p/>
	 * Y/N（已/未）
	 */
	@Column(name = "GRPOVER", length = 1, columnDefinition = "CHAR(1)")
	private String grpOver;

	/**
	 * 輸入資料檢誤完成(Y/N)
	 * <p/>
	 * 100/12/05新增<br/>
	 * Y/N<br/>
	 * 預先檢核資料是否已登錄完整，供執行【呈主管覆核】時，可快速檢核資料是否完備用。
	 */
	@Column(name = "CHKYN", length = 1, columnDefinition = "CHAR(1)")
	private String chkYN;

	/** 建立人員號碼 **/
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Date updateTime;

	/**
	 * 本行買入集團企業無擔保債券有效額度
	 * <p/>
	 * 單位：TWD仟元
	 */
	@Column(name = "BONDFACTAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal bondFactAmt;

	/** 本行買入集團企業無擔保債券有效額度占本行淨值 **/
	@Column(name = "BONDFACTMEGA", columnDefinition = "DECIMAL(5,2)")
	private Double bondFactMega;

	/**
	 * 本行買入集團企業無擔保債券餘額
	 * <p/>
	 * 單位：TWD仟元
	 */
	@Column(name = "BONDBALAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal bondBalAmt;

	/** 本行買入集團企業無擔保債券餘額占本行淨值 **/
	@Column(name = "BONDBALMEGA", columnDefinition = "DECIMAL(5,2)")
	private Double bondBalMega;

	/** 集團財務警訊項目 **/
	@Column(name = "GRPFINALERT", length = 30, columnDefinition = "VARCHAR(30)")
	private String grpFinAlert;

	/** 集團財務警訊上傳年度 **/
	@Column(name = "GRPFINALERTYEAR", length = 30, columnDefinition = "VARCHAR(30)")
	private String grpFinAlertYear;

	/** 集團評等年度 **/
	@Column(name = "GRPYEAR", length = 30, columnDefinition = "VARCHAR(30)")
	private String grpYear;

	// J-107-0007-001 Web e-Loan國內、海外授信簽報書第八章新增相同集團企業評等等級之新臺幣及美元放款利率資訊

	/** 集團利率資料年月 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "CYCMN", columnDefinition = "DATE")
	private Date cycMn;

	/** 列管註記 **/
	@Column(name = "BADFG", length = 2, columnDefinition = "CHAR(02)")
	private String badFg;

	/** 企業集團評等 **/
	@Column(name = "GRPGRADE", length = 1, columnDefinition = "CHAR(01)")
	private String grpGrade;

	/** 集團數 **/
	@Column(name = "CNT", columnDefinition = "DECIMAL(5,0)")
	private BigDecimal cnt;

	/** 集團評等年度 **/
	@Column(name = "GRPYY", length = 4, columnDefinition = "CHAR(04)")
	private String grpYy;

	/** 總平均放款利率-新臺幣 **/
	@Column(name = "AVGTWDRT", columnDefinition = "DECIMAL(7,5)")
	private BigDecimal avgTwdRt;

	/** 總平均放款利率-美金 **/
	@Column(name = "AVGUSDRT", columnDefinition = "DECIMAL(7,5)")
	private BigDecimal avgUsdRt;

	/** 平均擔保放款利率-新臺幣 **/
	@Column(name = "AVGTWDSRT", columnDefinition = "DECIMAL(7,5)")
	private BigDecimal avgTwdSRt;

	/** 平均擔保放款利率-美金 **/
	@Column(name = "AVGUSDSRT", columnDefinition = "DECIMAL(7,5)")
	private BigDecimal avgUsdSRt;

	/** 平均無擔保放款利率-新臺幣 **/
	@Column(name = "AVGTWDNRT", columnDefinition = "DECIMAL(7,5)")
	private BigDecimal avgTwdNRt;

	/** 平均無擔保放款利率-美金 **/
	@Column(name = "AVGUSDNRT", columnDefinition = "DECIMAL(7,5)")
	private BigDecimal avgUsdNRt;

	/** 最高擔保放款利率-新臺幣 **/
	@Column(name = "MAXTWDSRT", columnDefinition = "DECIMAL(7,5)")
	private BigDecimal maxTwdSRt;

	/** 最高擔保放款利率-美金 **/
	@Column(name = "MAXUSDSRT", columnDefinition = "DECIMAL(7,5)")
	private BigDecimal maxUsdSRt;

	/** 最高無擔保放款利率-新臺幣 **/
	@Column(name = "MAXTWDNRT", columnDefinition = "DECIMAL(7,5)")
	private BigDecimal maxTwdNRt;

	/** 最高無擔保放款利率-美金 **/
	@Column(name = "MAXUSDNRT", columnDefinition = "DECIMAL(7,5)")
	private BigDecimal maxUsdNRt;

	/** 最低擔保放款利率-新臺幣 **/
	@Column(name = "MINTWDSRT", columnDefinition = "DECIMAL(7,5)")
	private BigDecimal minTwdSRt;

	/** 最低擔保放款利率-美金 **/
	@Column(name = "MINUSDSRT", columnDefinition = "DECIMAL(7,5)")
	private BigDecimal minUsdSRt;

	/** 最低無擔保放款利率-新臺幣 **/
	@Column(name = "MINTWDNRT", columnDefinition = "DECIMAL(7,5)")
	private BigDecimal minTwdNRt;

	/** 最低無擔保放款利率-美金 **/
	@Column(name = "MINUSDNRT", columnDefinition = "DECIMAL(7,5)")
	private BigDecimal minUsdNRt;

	/** 集團企業規模 **/
	@Column(name = "GRPSIZE", length = 1, columnDefinition = "CHAR(01)")
	private String grpSize;

	/** 集團企業規模級別 **/
	@Column(name = "GRPLEVEL", length = 1, columnDefinition = "CHAR(01)")
	private String grpLevel;

	// J-107-0395_05097_B1001 Web e-Loan企金授信簽報書修改第八章本行買入集團企業無擔保債券額度及餘額及計算之種類範圍

	/** 本行買入集團企業無擔保債券版本 **/
	@Column(name = "BONDFLAG", length = 1, columnDefinition = "CHAR(01)")
	private String bondFlag;

	/** 本行買入集團企業債票券餘額 TWD仟元 **/
	@Column(name = "BDBAL", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal bdBal;

	/** 本行買入集團企業債票券餘額佔本行淨值 **/
	@Column(name = "BDBALMEGA", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal bdBalMega;

	/** 買入無擔保債票券之餘額 TWD仟元 **/
	@Column(name = "BDBALN", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal bdBalN;

	/** 其中有保證人之無擔保債票券餘額佔本行淨值 **/
	@Column(name = "BDBALNMEGA", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal bdBalNMega;

	/** 其中有保證人之無擔保債票券餘額 TWD仟元 **/
	@Column(name = "BDBALNG", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal bdBalNg;

	/** 買入無擔保債票券屬本行保證者餘額 TWD仟元 **/
	@Column(name = "BDBALNOG", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal bdBalNog;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得身分證統編 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定身分證統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得身分證統編重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定身分證統編重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/**
	 * 取得借款人是否為集團企業
	 * <p/>
	 * Y/N（是/否）
	 */
	public String getGrpFlag() {
		return this.grpFlag;
	}

	/**
	 * 設定借款人是否為集團企業
	 * <p/>
	 * Y/N（是/否）
	 **/
	public void setGrpFlag(String value) {
		this.grpFlag = value;
	}

	/**
	 * 取得資料來源
	 * <p/>
	 * 100/10/20新增<br/>
	 * 1.徵信報告之資字資信簡表<br/>
	 * 2.最新集團企業授信往來資料
	 */
	public String getGrpSrc() {
		return this.grpSrc;
	}

	/**
	 * 設定資料來源
	 * <p/>
	 * 100/10/20新增<br/>
	 * 1.徵信報告之資字資信簡表<br/>
	 * 2.最新集團企業授信往來資料
	 **/
	public void setGrpSrc(String value) {
		this.grpSrc = value;
	}

	/**
	 * 取得集團代號
	 * <p/>
	 * 集團名稱：9999ＸＸ集團，根據聯徵中心資料庫顯示，截至YYYY-MM-DD止該集團（含核心人物）於國內金融機構之授信額度（不包含海外分行及OBU
	 * ）為TWD999,999,999仟元，餘額為TWD999,999,999仟元。另截至YYYY-MM-
	 * DD止該集團於本行海外分行之總授信額度為TWD999
	 * ,999,999仟元，餘額為TWD999,999,999仟元。截至YYYY年底該集團之淨值TWD999
	 * ,999,999仟元、營收TWD999,999,999仟元。
	 */
	public String getGrpNo() {
		return this.grpNo;
	}

	/**
	 * 設定集團代號
	 * <p/>
	 * 集團名稱：9999ＸＸ集團，根據聯徵中心資料庫顯示，截至YYYY-MM-DD止該集團（含核心人物）於國內金融機構之授信額度（不包含海外分行及OBU
	 * ）為TWD999,999,999仟元，餘額為TWD999,999,999仟元。另截至YYYY-MM-
	 * DD止該集團於本行海外分行之總授信額度為TWD999
	 * ,999,999仟元，餘額為TWD999,999,999仟元。截至YYYY年底該集團之淨值TWD999
	 * ,999,999仟元、營收TWD999,999,999仟元。
	 **/
	public void setGrpNo(String value) {
		this.grpNo = value;
	}

	/**
	 * 取得集團名稱
	 * <p/>
	 * 100/11/21新增
	 */
	public String getGrpName() {
		return this.grpName;
	}

	/**
	 * 設定集團名稱
	 * <p/>
	 * 100/11/21新增
	 **/
	public void setGrpName(String value) {
		this.grpName = value;
	}

	/**
	 * 取得單位(預留欄位)
	 * <p/>
	 * 預設：1000
	 */
	public Long getAmtUnit() {
		return this.amtUnit;
	}

	/**
	 * 設定單位(預留欄位)
	 * <p/>
	 * 預設：1000
	 **/
	public void setAmtUnit(Long value) {
		this.amtUnit = value;
	}

	/**
	 * 取得幣別(預留欄位)
	 * <p/>
	 * 預設：TWD
	 */
	public String getCurr() {
		return this.curr;
	}

	/**
	 * 設定幣別(預留欄位)
	 * <p/>
	 * 預設：TWD
	 **/
	public void setCurr(String value) {
		this.curr = value;
	}

	/**
	 * 取得資料來源 (根據…顯示)
	 * <p/>
	 * 預設：聯徵中心資料庫<br/>
	 * eg.<br/>
	 * 1.國內授信：聯徵中心資料庫( - )<br/>
	 * 2.本行OBU授信：XXX(YYY/MM/DD)<br/>
	 * 3.本行海外分行授信：XXX(YYY/MM/DD)
	 */
	public String getEndDscr() {
		return this.endDscr;
	}

	/**
	 * 設定資料來源 (根據…顯示)
	 * <p/>
	 * 預設：聯徵中心資料庫<br/>
	 * eg.<br/>
	 * 1.國內授信：聯徵中心資料庫( - )<br/>
	 * 2.本行OBU授信：XXX(YYY/MM/DD)<br/>
	 * 3.本行海外分行授信：XXX(YYY/MM/DD)
	 **/
	public void setEndDscr(String value) {
		this.endDscr = value;
	}

	/**
	 * 取得國內授信截止日期 (截至YYYY-MM-DD止)
	 **/
	public Date getEndDate() {
		return this.endDate;
	}

	/**
	 * 設定國內授信截止日期 (截至YYYY-MM-DD止)
	 **/
	public void setEndDate(Date value) {
		this.endDate = value;
	}

	/**
	 * 取得國內授信額度
	 * <p/>
	 * 單位：TWD仟元
	 */
	public Long getFcltAmt() {
		return this.fcltAmt;
	}

	/**
	 * 設定國內授信額度
	 * <p/>
	 * 單位：TWD仟元
	 **/
	public void setFcltAmt(Long value) {
		this.fcltAmt = value;
	}

	/**
	 * 取得國內授信餘額
	 * <p/>
	 * 單位：TWD仟元
	 */
	public Long getLnAmt() {
		return this.lnAmt;
	}

	/**
	 * 設定國內授信餘額
	 * <p/>
	 * 單位：TWD仟元
	 **/
	public void setLnAmt(Long value) {
		this.lnAmt = value;
	}

	/**
	 * 取得海外授信截止日期 (截至YYYY-MM-DD止)
	 **/
	public Date getEndDate2() {
		return this.endDate2;
	}

	/**
	 * 設定海外授信截止日期 (截至YYYY-MM-DD止)
	 **/
	public void setEndDate2(Date value) {
		this.endDate2 = value;
	}

	/**
	 * 取得海外授信額度
	 * <p/>
	 * 單位：TWD仟元
	 */
	public Long getFcltAmt2() {
		return this.fcltAmt2;
	}

	/**
	 * 設定海外授信額度
	 * <p/>
	 * 單位：TWD仟元
	 **/
	public void setFcltAmt2(Long value) {
		this.fcltAmt2 = value;
	}

	/**
	 * 取得海外授信餘額
	 * <p/>
	 * 單位：TWD仟元
	 */
	public Long getLnAmt2() {
		return this.lnAmt2;
	}

	/**
	 * 設定海外授信餘額
	 * <p/>
	 * 單位：TWD仟元
	 **/
	public void setLnAmt2(Long value) {
		this.lnAmt2 = value;
	}

	/**
	 * 取得截止年底
	 * <p/>
	 * YYYY
	 */
	public Integer getEndYear() {
		return this.endYear;
	}

	/**
	 * 設定截止年底
	 * <p/>
	 * YYYY
	 **/
	public void setEndYear(Integer value) {
		this.endYear = value;
	}

	/**
	 * 取得集團淨值
	 * <p/>
	 * 單位：TWD仟元
	 */
	public Long getGrpnAmt() {
		return this.grpnAmt;
	}

	/**
	 * 設定集團淨值
	 * <p/>
	 * 單位：TWD仟元
	 **/
	public void setGrpnAmt(Long value) {
		this.grpnAmt = value;
	}

	/**
	 * 取得集團營收
	 * <p/>
	 * 單位：TWD仟元
	 */
	public Long getGrprAmt() {
		return this.grprAmt;
	}

	/**
	 * 設定集團營收
	 * <p/>
	 * 單位：TWD仟元
	 **/
	public void setGrprAmt(Long value) {
		this.grprAmt = value;
	}

	/** 取得資料查詢日 **/
	public Date getInqDate() {
		return this.inqDate;
	}

	/** 設定資料查詢日 **/
	public void setInqDate(Date value) {
		this.inqDate = value;
	}

	/**
	 * 取得授信總額度
	 * <p/>
	 * 單位：TWD仟元<br/>
	 * (totAmtA+totAmtB)
	 */
	public Long getTotAmt() {
		return this.totAmt;
	}

	/**
	 * 設定授信總額度
	 * <p/>
	 * 單位：TWD仟元<br/>
	 * (totAmtA+totAmtB)
	 **/
	public void setTotAmt(Long value) {
		this.totAmt = value;
	}

	/**
	 * 取得授信總額度(國內)
	 * <p/>
	 * 100/09/28調整<br/>
	 * 單位：TWD仟元
	 */
	public Long getTotAmtA() {
		return this.totAmtA;
	}

	/**
	 * 設定授信總額度(國內)
	 * <p/>
	 * 100/09/28調整<br/>
	 * 單位：TWD仟元
	 **/
	public void setTotAmtA(Long value) {
		this.totAmtA = value;
	}

	/**
	 * 取得授信總額度(海外)
	 * <p/>
	 * 100/09/28調整<br/>
	 * 單位：TWD仟元
	 */
	public Long getTotAmtB() {
		return this.totAmtB;
	}

	/**
	 * 設定授信總額度(海外)
	 * <p/>
	 * 100/09/28調整<br/>
	 * 單位：TWD仟元
	 **/
	public void setTotAmtB(Long value) {
		this.totAmtB = value;
	}

	/** 取得授信總額度占本行淨值 **/
	public Double getTotMega() {
		return this.totMega;
	}

	/** 設定授信總額度占本行淨值 **/
	public void setTotMega(Double value) {
		this.totMega = value;
	}

	/**
	 * 取得無擔保授信總額度
	 * <p/>
	 * 單位：TWD仟元<br/>
	 * (crdAmtA+crdAmtB)
	 */
	public Long getCrdAmt() {
		return this.crdAmt;
	}

	/**
	 * 設定無擔保授信總額度
	 * <p/>
	 * 單位：TWD仟元<br/>
	 * (crdAmtA+crdAmtB)
	 **/
	public void setCrdAmt(Long value) {
		this.crdAmt = value;
	}

	/**
	 * 取得無擔保授信總額度(國內)
	 * <p/>
	 * 100/09/28調整<br/>
	 * 單位：TWD仟元
	 */
	public Long getCrdAmtA() {
		return this.crdAmtA;
	}

	/**
	 * 設定無擔保授信總額度(國內)
	 * <p/>
	 * 100/09/28調整<br/>
	 * 單位：TWD仟元
	 **/
	public void setCrdAmtA(Long value) {
		this.crdAmtA = value;
	}

	/**
	 * 取得無擔保授信總額度(海外)
	 * <p/>
	 * 100/09/28調整<br/>
	 * 單位：TWD仟元
	 */
	public Long getCrdAmtB() {
		return this.crdAmtB;
	}

	/**
	 * 設定無擔保授信總額度(海外)
	 * <p/>
	 * 100/09/28調整<br/>
	 * 單位：TWD仟元
	 **/
	public void setCrdAmtB(Long value) {
		this.crdAmtB = value;
	}

	/** 取得無擔保授信總額度占本行淨值 **/
	public Double getCrdMega() {
		return this.crdMega;
	}

	/** 設定無擔保授信總額度占本行淨值 **/
	public void setCrdMega(Double value) {
		this.crdMega = value;
	}

	/**
	 * 取得授信總餘額
	 * <p/>
	 * 單位：TWD仟元<br/>
	 * (lntAmtA+lntAmtB)
	 */
	public Long getLntAmt() {
		return this.lntAmt;
	}

	/**
	 * 設定授信總餘額
	 * <p/>
	 * 單位：TWD仟元<br/>
	 * (lntAmtA+lntAmtB)
	 **/
	public void setLntAmt(Long value) {
		this.lntAmt = value;
	}

	/**
	 * 取得授信總餘額(國內)
	 * <p/>
	 * 100/09/28調整<br/>
	 * 單位：TWD仟元
	 */
	public Long getLntAmtA() {
		return this.lntAmtA;
	}

	/**
	 * 設定授信總餘額(國內)
	 * <p/>
	 * 100/09/28調整<br/>
	 * 單位：TWD仟元
	 **/
	public void setLntAmtA(Long value) {
		this.lntAmtA = value;
	}

	/**
	 * 取得授信總餘額(海外)
	 * <p/>
	 * 100/09/28調整<br/>
	 * 單位：TWD仟元
	 */
	public Long getLntAmtB() {
		return this.lntAmtB;
	}

	/**
	 * 設定授信總餘額(海外)
	 * <p/>
	 * 100/09/28調整<br/>
	 * 單位：TWD仟元
	 **/
	public void setLntAmtB(Long value) {
		this.lntAmtB = value;
	}

	/** 取得授信總餘額占本行淨值 **/
	public Double getLntMega() {
		return this.lntMega;
	}

	/** 設定授信總餘額占本行淨值 **/
	public void setLntMega(Double value) {
		this.lntMega = value;
	}

	/**
	 * 取得無擔保授信總餘額
	 * <p/>
	 * 單位：TWD仟元<br/>
	 * (lncAmtA+lncAmtB)
	 */
	public Long getLncAmt() {
		return this.lncAmt;
	}

	/**
	 * 設定無擔保授信總餘額
	 * <p/>
	 * 單位：TWD仟元<br/>
	 * (lncAmtA+lncAmtB)
	 **/
	public void setLncAmt(Long value) {
		this.lncAmt = value;
	}

	/**
	 * 取得無擔保授信總餘額(國內)
	 * <p/>
	 * 100/09/28調整<br/>
	 * 單位：TWD仟元
	 */
	public Long getLncAmtA() {
		return this.lncAmtA;
	}

	/**
	 * 設定無擔保授信總餘額(國內)
	 * <p/>
	 * 100/09/28調整<br/>
	 * 單位：TWD仟元
	 **/
	public void setLncAmtA(Long value) {
		this.lncAmtA = value;
	}

	/**
	 * 取得無擔保授信總餘額(海外)
	 * <p/>
	 * 100/09/28調整<br/>
	 * 單位：TWD仟元
	 */
	public Long getLncAmtB() {
		return this.lncAmtB;
	}

	/**
	 * 設定無擔保授信總餘額(海外)
	 * <p/>
	 * 100/09/28調整<br/>
	 * 單位：TWD仟元
	 **/
	public void setLncAmtB(Long value) {
		this.lncAmtB = value;
	}

	/** 取得無擔保授信總餘額占本行淨值 **/
	public Double getLncMega() {
		return this.lncMega;
	}

	/** 設定無擔保授信總餘額占本行淨值 **/
	public void setLncMega(Double value) {
		this.lncMega = value;
	}

	/**
	 * 取得不計入同一關係企業第一～四項者之合計額度
	 * <p/>
	 * 單位：TWD仟元<br/>
	 * (若扣除該集團依規可不計入同一關係企業第一～四項者之合計額度為TWD999,999,999仟元，占本行淨值的999.99%，合計餘額為TWD999
	 * ,999,999仟元，占本行淨值的999.99%。)
	 */
	public Long getExcAmt() {
		return this.excAmt;
	}

	/**
	 * 設定不計入同一關係企業第一～四項者之合計額度
	 * <p/>
	 * 單位：TWD仟元<br/>
	 * (若扣除該集團依規可不計入同一關係企業第一～四項者之合計額度為TWD999,999,999仟元，占本行淨值的999.99%，合計餘額為TWD999
	 * ,999,999仟元，占本行淨值的999.99%。)
	 **/
	public void setExcAmt(Long value) {
		this.excAmt = value;
	}

	/** 取得不計入同一關係企業第一～四項者之合計額度占本行淨值 **/
	public Double getExcMega() {
		return this.excMega;
	}

	/** 設定不計入同一關係企業第一～四項者之合計額度占本行淨值 **/
	public void setExcMega(Double value) {
		this.excMega = value;
	}

	/**
	 * 取得合計餘額
	 * <p/>
	 * 單位：TWD仟元
	 */
	public Long getSumAmt() {
		return this.sumAmt;
	}

	/**
	 * 設定合計餘額
	 * <p/>
	 * 單位：TWD仟元
	 **/
	public void setSumAmt(Long value) {
		this.sumAmt = value;
	}

	/** 取得占本行淨值 **/
	public Double getSumMega() {
		return this.sumMega;
	}

	/** 設定占本行淨值 **/
	public void setSumMega(Double value) {
		this.sumMega = value;
	}

	/**
	 * 取得本行轉投資等非授信業務金額
	 * <p/>
	 * 單位：TWD仟元
	 */
	public Long getOthAmt() {
		return this.othAmt;
	}

	/**
	 * 設定本行轉投資等非授信業務金額
	 * <p/>
	 * 單位：TWD仟元
	 **/
	public void setOthAmt(Long value) {
		this.othAmt = value;
	}

	/** 取得非授信業務占本行淨值 **/
	public Double getOthMega() {
		return this.othMega;
	}

	/** 設定非授信業務占本行淨值 **/
	public void setOthMega(Double value) {
		this.othMega = value;
	}

	/**
	 * 取得金融商品金額
	 * <p/>
	 * 單位：TWD仟元
	 */
	public Long getFinAmt() {
		return this.finAmt;
	}

	/**
	 * 設定金融商品金額
	 * <p/>
	 * 單位：TWD仟元
	 **/
	public void setFinAmt(Long value) {
		this.finAmt = value;
	}

	/** 取得金融商品金額占本行淨值 **/
	public Double getFinMega() {
		return this.finMega;
	}

	/** 設定金融商品金額占本行淨值 **/
	public void setFinMega(Double value) {
		this.finMega = value;
	}

	/**
	 * 取得曝險總額
	 * <p/>
	 * 單位：TWD仟元
	 */
	public Long getRskAmt() {
		return this.rskAmt;
	}

	/**
	 * 設定曝險總額
	 * <p/>
	 * 單位：TWD仟元
	 **/
	public void setRskAmt(Long value) {
		this.rskAmt = value;
	}

	/** 取得曝險總額占本行淨值 **/
	public Double getRskMega() {
		return this.rskMega;
	}

	/** 設定曝險總額占本行淨值 **/
	public void setRskMega(Double value) {
		this.rskMega = value;
	}

	/** 取得集團評等 **/
	public String getGrpGrrd() {
		return this.grpGrrd;
	}

	/** 設定集團評等 **/
	public void setGrpGrrd(String value) {
		this.grpGrrd = value;
	}

	/** 取得集團評等說明 **/
	public String getGrpDscr() {
		return this.grpDscr;
	}

	/** 設定集團評等說明 **/
	public void setGrpDscr(String value) {
		this.grpDscr = value;
	}

	/**
	 * 取得本行對該集團授信限額
	 * <p/>
	 * 單位：TWD仟元
	 */
	public BigDecimal getLmtAmt() {
		return this.lmtAmt;
	}

	/**
	 * 設定本行對該集團授信限額
	 * <p/>
	 * 單位：TWD仟元
	 **/
	public void setLmtAmt(BigDecimal value) {
		this.lmtAmt = value;
	}

	/** 取得本行對該集團授信限額占本行淨值 **/
	public Double getLmtMega() {
		return this.lmtMega;
	}

	/** 設定本行對該集團授信限額占本行淨值 **/
	public void setLmtMega(Double value) {
		this.lmtMega = value;
	}

	/**
	 * 取得本行對該集團無擔保授信限額
	 * <p/>
	 * 單位：TWD仟元
	 */
	public BigDecimal getGcrdAmt() {
		return this.gcrdAmt;
	}

	/**
	 * 設定本行對該集團無擔保授信限額
	 * <p/>
	 * 單位：TWD仟元
	 **/
	public void setGcrdAmt(BigDecimal value) {
		this.gcrdAmt = value;
	}

	/** 取得本行對該集團無擔保授信限額占本行淨值 **/
	public Double getGcrdMega() {
		return this.gcrdMega;
	}

	/** 設定本行對該集團無擔保授信限額占本行淨值 **/
	public void setGcrdMega(Double value) {
		this.gcrdMega = value;
	}

	/**
	 * 取得該集團在全體金融機構授信總餘額?未?逾其淨值或營收
	 * <p/>
	 * Y/N（已/未）
	 */
	public String getGrpOver() {
		return this.grpOver;
	}

	/**
	 * 設定該集團在全體金融機構授信總餘額?未?逾其淨值或營收
	 * <p/>
	 * Y/N（已/未）
	 **/
	public void setGrpOver(String value) {
		this.grpOver = value;
	}

	/**
	 * 取得輸入資料檢誤完成(Y/N)
	 * <p/>
	 * 100/12/05新增<br/>
	 * Y/N<br/>
	 * 預先檢核資料是否已登錄完整，供執行【呈主管覆核】時，可快速檢核資料是否完備用。
	 */
	public String getChkYN() {
		return this.chkYN;
	}

	/**
	 * 設定輸入資料檢誤完成(Y/N)
	 * <p/>
	 * 100/12/05新增<br/>
	 * Y/N<br/>
	 * 預先檢核資料是否已登錄完整，供執行【呈主管覆核】時，可快速檢核資料是否完備用。
	 **/
	public void setChkYN(String value) {
		this.chkYN = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}

	/** 設定本行買入集團企業無擔保債券有效額度 **/
	public void setBondFactAmt(BigDecimal bondFactAmt) {
		this.bondFactAmt = bondFactAmt;
	}

	/** 取得本行買入集團企業無擔保債券有效額度 **/
	public BigDecimal getBondFactAmt() {
		return bondFactAmt;
	}

	/** 設定本行買入集團企業無擔保債券有效額度占本行淨值 **/
	public void setBondFactMega(Double bondFactMega) {
		this.bondFactMega = bondFactMega;
	}

	/** 取得本行買入集團企業無擔保債券有效額度占本行淨值 **/
	public Double getBondFactMega() {
		return bondFactMega;
	}

	/** 設定本行買入集團企業無擔保債券餘額 **/
	public void setBondBalAmt(BigDecimal bondBalAmt) {
		this.bondBalAmt = bondBalAmt;
	}

	/** 取得本行買入集團企業無擔保債券餘額 **/
	public BigDecimal getBondBalAmt() {
		return bondBalAmt;
	}

	/** 設定本行買入集團企業無擔保債券餘額占本行淨值 **/
	public void setBondBalMega(Double bondBalMega) {
		this.bondBalMega = bondBalMega;
	}

	/** 取得本行買入集團企業無擔保債券餘額占本行淨值 **/
	public Double getBondBalMega() {
		return bondBalMega;
	}

	/** 設定集團財務警訊項目 **/
	public void setGrpFinAlert(String grpFinAlert) {
		this.grpFinAlert = grpFinAlert;
	}

	/** 取得集團財務警訊項目 **/
	public String getGrpFinAlert() {
		return grpFinAlert;
	}

	/** 設定集團財務警訊上傳年度 **/
	public void setGrpFinAlertYear(String grpFinAlertYear) {
		this.grpFinAlertYear = grpFinAlertYear;
	}

	/** 取得集團財務警訊上傳年度 **/
	public String getGrpFinAlertYear() {
		return grpFinAlertYear;
	}

	/** 設定集團評等年度 **/
	public void setGrpYear(String grpYear) {
		this.grpYear = grpYear;
	}

	/** 取得集團評等年度 **/
	public String getGrpYear() {
		return grpYear;
	}

	public void setCycMn(Date cycMn) {
		this.cycMn = cycMn;
	}

	public Date getCycMn() {
		return cycMn;
	}

	public void setBadFg(String badFg) {
		this.badFg = badFg;
	}

	public String getBadFg() {
		return badFg;
	}

	public void setGrpGrade(String grpGrade) {
		this.grpGrade = grpGrade;
	}

	public String getGrpGrade() {
		return grpGrade;
	}

	public void setCnt(BigDecimal cnt) {
		this.cnt = cnt;
	}

	public BigDecimal getCnt() {
		return cnt;
	}

	public void setGrpYy(String grpYy) {
		this.grpYy = grpYy;
	}

	public String getGrpYy() {
		return grpYy;
	}

	public void setAvgTwdRt(BigDecimal avgTwdRt) {
		this.avgTwdRt = avgTwdRt;
	}

	public BigDecimal getAvgTwdRt() {
		return avgTwdRt == null ? null : Arithmetic.round(avgTwdRt, 4);
	}

	public void setAvgUsdRt(BigDecimal avgUsdRt) {
		this.avgUsdRt = avgUsdRt;
	}

	public BigDecimal getAvgUsdRt() {
		return avgUsdRt == null ? null : Arithmetic.round(avgUsdRt, 4);
	}

	public void setAvgTwdSRt(BigDecimal avgTwdSRt) {
		this.avgTwdSRt = avgTwdSRt;
	}

	public BigDecimal getAvgTwdSRt() {
		return avgTwdSRt == null ? null : Arithmetic.round(avgTwdSRt, 4);
	}

	public void setAvgUsdSRt(BigDecimal avgUsdSRt) {
		this.avgUsdSRt = avgUsdSRt;
	}

	public BigDecimal getAvgUsdSRt() {
		return avgUsdSRt == null ? null : Arithmetic.round(avgUsdSRt, 4);
	}

	public void setAvgTwdNRt(BigDecimal avgTwdNRt) {
		this.avgTwdNRt = avgTwdNRt;
	}

	public BigDecimal getAvgTwdNRt() {
		return avgTwdNRt == null ? null : Arithmetic.round(avgTwdNRt, 4);
	}

	public void setAvgUsdNRt(BigDecimal avgUsdNRt) {
		this.avgUsdNRt = avgUsdNRt;
	}

	public BigDecimal getAvgUsdNRt() {
		return avgUsdNRt == null ? null : Arithmetic.round(avgUsdNRt, 4);
	}

	public void setMaxTwdSRt(BigDecimal maxTwdSRt) {
		this.maxTwdSRt = maxTwdSRt;
	}

	public BigDecimal getMaxTwdSRt() {
		return maxTwdSRt == null ? null : Arithmetic.round(maxTwdSRt, 4);
	}

	public void setMaxUsdSRt(BigDecimal maxUsdSRt) {
		this.maxUsdSRt = maxUsdSRt;
	}

	public BigDecimal getMaxUsdSRt() {
		return maxUsdSRt == null ? null : Arithmetic.round(maxUsdSRt, 4);
	}

	public void setMaxTwdNRt(BigDecimal maxTwdNRt) {
		this.maxTwdNRt = maxTwdNRt;
	}

	public BigDecimal getMaxTwdNRt() {
		return maxTwdNRt == null ? null : Arithmetic.round(maxTwdNRt, 4);
	}

	public void setMaxUsdNRt(BigDecimal maxUsdNRt) {
		this.maxUsdNRt = maxUsdNRt;
	}

	public BigDecimal getMaxUsdNRt() {
		return maxUsdNRt == null ? null : Arithmetic.round(maxUsdNRt, 4);
	}

	public void setMinTwdSRt(BigDecimal minTwdSRt) {
		this.minTwdSRt = minTwdSRt;
	}

	public BigDecimal getMinTwdSRt() {
		return minTwdSRt == null ? null : Arithmetic.round(minTwdSRt, 4);
	}

	public void setMinUsdSRt(BigDecimal minUsdSRt) {
		this.minUsdSRt = minUsdSRt;
	}

	public BigDecimal getMinUsdSRt() {
		return minUsdSRt == null ? null : Arithmetic.round(minUsdSRt, 4);
	}

	public void setMinTwdNRt(BigDecimal minTwdNRt) {
		this.minTwdNRt = minTwdNRt;
	}

	public BigDecimal getMinTwdNRt() {
		return minTwdNRt == null ? null : Arithmetic.round(minTwdNRt, 4);
	}

	public void setMinUsdNRt(BigDecimal minUsdNRt) {
		this.minUsdNRt = minUsdNRt;
	}

	public BigDecimal getMinUsdNRt() {
		return minUsdNRt == null ? null : Arithmetic.round(minUsdNRt, 4);
	}

	/** 設定 **/
	public void setGrpSize(String grpSize) {
		this.grpSize = grpSize;
	}

	/** 取得 **/
	public String getGrpSize() {
		return grpSize;
	}

	/** 設定 **/
	public void setGrpLevel(String grpLevel) {
		this.grpLevel = grpLevel;
	}

	/** 取得 **/
	public String getGrpLevel() {
		return grpLevel;
	}

	/** 設定本行買入集團企業無擔保債券版本 **/
	public void setBondFlag(String bondFlag) {
		this.bondFlag = bondFlag;
	}

	/** 取得本行買入集團企業無擔保債券版本 **/
	public String getBondFlag() {
		return bondFlag;
	}

	/** 設定本行買入集團企業債票券餘額 **/
	public void setBdBal(BigDecimal bdBal) {
		this.bdBal = bdBal;
	}

	/** 取得本行買入集團企業債票券餘額 **/
	public BigDecimal getBdBal() {
		return bdBal;
	}

	/** 設定本行買入集團企業債票券餘額佔本行淨值 **/
	public void setBdBalMega(BigDecimal bdBalMega) {
		this.bdBalMega = bdBalMega;
	}

	/** 取得本行買入集團企業債票券餘額佔本行淨值 **/
	public BigDecimal getBdBalMega() {
		return bdBalMega;
	}

	/** 設定買入無擔保債票券之餘額 **/
	public void setBdBalN(BigDecimal bdBalN) {
		this.bdBalN = bdBalN;
	}

	/** 取得買入無擔保債票券之餘額 **/
	public BigDecimal getBdBalN() {
		return bdBalN;
	}

	/** 設定其中有保證人之無擔保債票券餘額佔本行淨值 **/
	public void setBdBalNMega(BigDecimal bdBalNMega) {
		this.bdBalNMega = bdBalNMega;
	}

	/** 取得其中有保證人之無擔保債票券餘額佔本行淨值 **/
	public BigDecimal getBdBalNMega() {
		return bdBalNMega;
	}

	/** 設定其中有保證人之無擔保債票券餘額 **/
	public void setBdBalNg(BigDecimal bdBalNg) {
		this.bdBalNg = bdBalNg;
	}

	/** 取得其中有保證人之無擔保債票券餘額 **/
	public BigDecimal getBdBalNg() {
		return bdBalNg;
	}

	/** 設定買入無擔保債票券屬本行保證者餘額 **/
	public void setBdBalNog(BigDecimal bdBalNog) {
		this.bdBalNog = bdBalNog;
	}

	/** 取得買入無擔保債票券屬本行保證者餘額 **/
	public BigDecimal getBdBalNog() {
		return bdBalNog;
	}

}
