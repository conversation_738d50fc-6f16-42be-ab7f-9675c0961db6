/*
 * 房貸 		AdjustAction				CLS1131S03Page.js
 * 非房貸	AdjustNotHouseLoanAction	CLS1131S06Page.js
 */
var AdjustAction = {
	handler : 'cls1131formhandler',
	data : {},
	ready : false,
	adjust : false,
	manual : false,
	/**
	 * 初始化
	 */
	init : function() {
		$('#adjustForm').setValue(); // reset
		AdjustAction.adjust = false;
		AdjustAction.manual = false;
	},
	/**
	 * 建置
	 */
	build : function() {
		var $form = $('#adjustForm');
		
		//讓頁面抓 codetype
		$form.buildItem();
		
		$form.find('input[name=adjustStatus]').click(function() {
			var $fm = $('#adjustForm');
			$fm.find('#adjustFlagDiv').hide();
			$fm.find('#adjustFlagSpan').html('理由為');
			$fm.find(".adjustFlag_4_cols").hide();
			$fm.find('#grade2').val('');
			$fm.find('#grade2').addClass('required').attr('disabled', false);
			$fm.find('#adjustFlagSpanDesc').attr("style","display:none;");
			$fm.find('#adjustFlagSpanDesc').html('');

			//手動時清除資料
			if (AdjustAction.manual){
				//$form.serializeData();
				$fm.find('input[name=adjustFlag]').attr('checked', false);
				$fm.find('#adjustReason').val('');
			}

			//addClass required add by fantasy 2013/07/09
			if (!$fm.find('#adjustReason').hasClass('required'))
				$fm.find('#adjustReason').addClass('required');
			
			switch ($(this).val()+''){
				case '1': //升等
					$fm.find('#adjustFlagDiv').show();
					$fm.find('#grade2').show();
					$fm.find('#adjustFlagSpan').html('勾選升等主要理由，並詳敘升等理由(如相關佐證等)');
					$fm.find('#adjustFlagSpanDesc').attr("style","display:inline-block;");
					$fm.find('#adjustFlagSpanDesc').html('應注意借款人是否存在有信用卡循環息/預計現金之聯徵等負面訊息，調升信用評等應審慎合理。');
					break;
				case '2': //降等
					$fm.find('#adjustFlagSpanDesc').attr("style","display:inline-block;");
					$fm.find('#adjustFlagSpanDesc').html('簽報案件時, 注意是否應調降擔保品放款值及核貸成數。');					
					break;
				case '3': //回復
					$fm.find('#grade2').removeClass('required').attr('disabled', true).val('');
					$fm.find('#adjustReason').removeClass('required');
					$fm.find('#adjustFlagSpanDesc').attr("style","display:none;");
					break;
			}

			AdjustAction.compute();
		});
		$form.find('input[name=adjustFlag]').click(function() {
				switch ($(this).val() + '') {
				case '1': // 淨資產
					$('#adjustFlagSpan1').html('淨資產');
					$('#adjustFlagSpan2').html(
							'(請以具體數據文字敘明，如年(月)所得金額、存款金額、租金收入金額、其他資產多寡或負債百分比等)');
					$(".adjustFlag_4_cols").hide();
					break;
				case '2': // 職業
					$('#adjustFlagSpan1').html('職業');
					$('#adjustFlagSpan2')
							.html('(請詳述，如任職公司職稱、規模、職業類別、年資、擔任公司負責人或工作前景等)');
					$(".adjustFlag_4_cols").hide();
					break;
				case '3': // 其它
					$('#adjustFlagSpan1').html('其它');
					$('#adjustFlagSpan2').html('(如與銀行的往來關係、對客戶之綜合評述或非屬前三類之理由等)');
					$(".adjustFlag_4_cols").hide();
					break;
				case '4': // 一般保證人資信佳
					$('#adjustFlagSpan1').html('一般保證人資信佳');
					$('#adjustFlagSpan2').html('(請詳述，並以具體數據文字敘明)');
					$(".adjustFlag_4_cols").show();
					break;
				}
				
			  //手動時清除資料
				if (AdjustAction.manual){
					$('#adjustForm').find('#adjustReason').val('');
				}
		});
		$form.find('#grade2').change(function() {
			AdjustAction.compute();
		});
		return true;
	},
	/**
	 * 開啟
	 */
	open : function(data) {
		if (!AdjustAction.ready)
			AdjustAction.ready = AdjustAction.build();
		// 初始化
		AdjustAction.init();
		// set data
		AdjustAction.data = $.extend(data || {}, {
			noOpenDoc : true,
			'markModel':'1'
		});
		// load data
		AdjustAction.load();
	},
	openThinkBox : function() {
		$('#adjustThickBox').thickbox({
			title : '調整房貸申請信用評等',
			width : 800,
			height : 450,
			modal : true,
			align : 'center',
			valign : 'bottom',
			i18n : i18n.def,
			buttons : {
				'sure' : function() {
					if ($('#adjustForm').valid()) {
						AdjustAction.save();
					}
				},
				'cancel' : function() {
					$.thickbox.close();
				}
			}
		});
	},
	openReadOnlyThinkBox : function() {
		$('#adjustForm').readOnlyChilds();
		
		$('#adjustThickBox').thickbox({
			title : '調整房貸申請信用評等',
			width : 800,
			height : 450,
			modal : true,
			align : 'center',
			valign : 'bottom',
			i18n : i18n.def,
			buttons : {
				'cancel' : function() {
					$.thickbox.close();
				}
			}
		});
	},
	/**
	 * 讀取資料
	 */
	load : function() {
		if (!$.isEmptyObject(AdjustAction.data)) {
			$.ajax({
				handler : AdjustAction.handler,
				action : 'loadAdjust',
				formId : 'empty', //
				data : AdjustAction.data,
				success : function(response) {
					if (response.adjustMsg) {
						// showErrorMessage showPopMessage
						MegaApi.showErrorMessage(i18n.def["confirmTitle"],
								response.adjustMsg);
					} else {
						AdjustAction.adjust = (response.adjust ? true : false);
						$('#adjustForm').setValue(response.adjustForm);
						
						// J-107-0104 當 升等理由 為一般保證人資信佳, 控制欄位顯示隱藏
						$('#adjustForm').find('input[name=adjustFlag]:checked').trigger("click");
						
						if(DOMPurify.sanitize(response.fromC120M01A)=="Y"){
							$(".area_cls1131s03page_c101").hide();
							$(".area_cls1131s03page_c120").show();
							AdjustAction.openReadOnlyThinkBox();
						}else{
							$(".area_cls1131s03page_c101").show();
							$(".area_cls1131s03page_c120").hide();
							if(response.lockAdjust=="Y"){
								AdjustAction.openReadOnlyThinkBox();	
							}else{
								AdjustAction.openThinkBox();	
							}							
						}
					}
					AdjustAction.manual = true;
				}
			});
		}
	},
	/**
	 * 儲存
	 */
	save : function() {
		if (!$.isEmptyObject(AdjustAction.data) && AdjustAction.checkGrade()) {
			$.ajax({handler : "lms1015m01formhandler",action : 'validateAdjustReason',				
				data : {'keyStr':'', 'mowType': 'M', 'adjustReason': $("#adjustForm").find("#adjustReason").val(), 
					'func': 'MOWTYPE_M_CHK01'
					},
				success : function(json) {
					procCfmMsg(json.adjRsnFmt_cfmObj).done(function(){
		        		alwaysConfirmAdjReason(json.adjRsnFmt_cnt
		        				, json.adjRsnFmt_alwaysCfmObj).done(function(){
		        			
	        					AdjustAction.upMowtypeMGrade_posN().done(function(){	        						
	        							$.ajax({
	    	        						handler : AdjustAction.handler,
	    	        						action : 'saveAdjust',
	    	        						formId : 'adjustForm', //
	    	        						data : AdjustAction.data,
	    	        						success : function(response) {
	    	        							$('#gradeDiv_markModel_1').setValue(response.gradeDiv_markModel_1);
	    	        							$.thickbox.close();
	    	        							MegaApi.showPopMessage(i18n.def["confirmTitle"],
	    	        									i18n.def["runSuccess"]);
	    	        						}
	    	        					});		
	        					});
		        				
		        		});
		        	});
				}
			});
		}
	},
	upMowtypeMGrade_posN : function(){
		var my_dfd = $.Deferred();    	
   	 
		$.ajax({handler : AdjustAction.handler,action : 'saveAdjustCheckMowtypeM', formId : 'adjustForm', data : AdjustAction.data,
			success : function(json) {
				//把一般保證人姓名, 帶至前端
				if(json.rtnObj){
					$("#adjustForm").injectData(json.rtnObj);
				}
				//=================================================
				if(json.confirmMsg){
					CommonAPI.confirmMessage(json.confirmMsg, function(b){
			            if (b) {
							my_dfd.resolve();	
			            } 
			        });
				}else{
					my_dfd.resolve();	
				}							
			}
		});
        
        return my_dfd.promise();
	},
	/**
	 * 計算最終評等
	 */
	compute : function() {
		var $form = $('#adjustForm');
		var value = parseInt($form.find('#grade1').html() || '0');
		$form.find('input[name=adjustStatus]:checked').each(function() {
			switch ($(this).val() + '') {
			case '1': // 調升
				value -= parseInt($form.find('#grade2').val() || '0');
				break;
			case '2': // 調降
				value += parseInt($form.find('#grade2').val() || '0');
				break;
			}
		});
		$form.find('#grade3').html(value);
		//AdjustAction.checkGrade();
	},
	/**
	 * 最終評等檢核
	 */
	checkGrade : function(){
		var $form = $('#adjustForm');
		var grade1 = parseInt($form.find('#grade1').html() || '0');
		var grade3 = parseInt($form.find('#grade3').html() || '0');
		if (grade3 >= 11 || grade3 <= 0) {
			MegaApi.showErrorMessage(i18n.def['confirmTitle'],'最終評等應介於1~10等');
			return false;
		}
		var adjustStatus = $form.find('input[name=adjustStatus]:checked').val()+'';
		if (adjustStatus === '1'){
			var grade = grade1 - grade3;
			var v = AdjustAction.adjust ? 3 : 2;
			if (grade > v){
				MegaApi.showErrorMessage(i18n.def['confirmTitle'],'調升不可超過'+v+'等');
				return false;
			}
		}
		return true;
	}
}