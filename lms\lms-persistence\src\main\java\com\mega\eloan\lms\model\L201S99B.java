package com.mega.eloan.lms.model;

import java.io.Serializable;
import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import com.mega.eloan.common.model.RelativeMeta;

/**
 * <pre>
 * L201S99B model.
 * </pre>
 * 
 * @since 2012/7/16
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/7/16,Tammy<PERSON><PERSON>,new
 *          </ul>
 */
@NamedEntityGraph(name = "L201S99B-entity-graph", attributeNodes = { @NamedAttributeNode("l201s99a") })
@Entity
@Table(name = "L201S99B", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class L201S99B extends RelativeMeta implements Serializable {
	private static final long serialVersionUID = 1L;

	@Column(length = 3)
	private String dataType;

	@Column(precision = 2)
	private BigDecimal serNo;

	@Column(precision = 2)
	private BigDecimal dataCnt;

	@Column(length = 1)
	private String sendFlag;

	@Column(length = 150)
	private String exeMsg;

	// bi-directional many-to-one association to L201S99A
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumns({
			@JoinColumn(name = "MAINID", referencedColumnName = "MAINID", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "PID", referencedColumnName = "UID", nullable = false, insertable = false, updatable = false) })
	private L201S99A l201s99a;

	public L201S99B() {
	}
	
	public L201S99B(L201S99A meta) {
		this.setMainId(meta.getMainId());
		this.setPid(meta.getUid());
	}

	public String getDataType() {
		return dataType;
	}

	public void setDataType(String dataType) {
		this.dataType = dataType;
	}

	public BigDecimal getSerNo() {
		return serNo;
	}

	public void setSerNo(BigDecimal serNo) {
		this.serNo = serNo;
	}

	public BigDecimal getDataCnt() {
		return dataCnt;
	}

	public void setDataCnt(BigDecimal dataCnt) {
		this.dataCnt = dataCnt;
	}

	public void setDataCnt(int dataCnt) {
		this.dataCnt = new BigDecimal(dataCnt);
	}

	public String getSendFlag() {
		return sendFlag;
	}

	public void setSendFlag(String sendFlag) {
		this.sendFlag = sendFlag;
	}

	public String getExeMsg() {
		return exeMsg;
	}

	public void setExeMsg(String exeMsg) {
		this.exeMsg = exeMsg;
	}

	public L201S99A getL201s99a() {
		return l201s99a;
	}

	public void setL201s99a(L201S99A l201s99a) {
		this.l201s99a = l201s99a;
	}
}