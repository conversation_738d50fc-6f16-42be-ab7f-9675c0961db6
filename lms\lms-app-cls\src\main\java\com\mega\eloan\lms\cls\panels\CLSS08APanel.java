package com.mega.eloan.lms.cls.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.panels.LMSS08APanel01;
import com.mega.eloan.lms.base.panels.LMSS08APanel02;
import com.mega.eloan.lms.base.panels.LMSS08CPanel;
import com.mega.eloan.lms.base.panels.LMSS08EPanel;
import com.mega.eloan.lms.base.panels.LMSS08FPanel;
import com.mega.eloan.lms.base.panels.LMSS08GPanel;
import com.mega.eloan.lms.base.panels.LMSS08LPanel;
import com.mega.eloan.lms.base.panels.LMSS08MPanel;
import com.mega.eloan.lms.model.L120M01A;

/**
 * <pre>
 * 相關文件(個金)
 * </pre>
 * 
 * @since 2012/1/19
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/19,<PERSON>,new
 *          </ul>
 */
public class CLSS08APanel extends Panel {

	/**
	 * 
	 */
	private static final long serialVersionUID = -4024257163623646201L;

	private L120M01A l120m01a;

	public CLSS08APanel(String id) {
		super(id);
	}

	public CLSS08APanel(String id, boolean updatePanelName, L120M01A l120m01a) {
		super(id, updatePanelName);
		this.l120m01a = l120m01a;
	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);

		new LMSS08APanel01("lmss08a_panel").processPanelData(model, params);
		new LMSS08APanel02("lmss08b_panel").processPanelData(model, params);
		new LMSS08CPanel("lmss08c_panel").processPanelData(model, params);
		// 2013_04_08 ,Rex,個金擔保品有差異所以做更改
		new CLSS08DPanel("lmss08d_panel").processPanelData(model, params);
		new LMSS08EPanel("lmss08e_panel").processPanelData(model, params);
		new LMSS08FPanel("lmss08f_panel").processPanelData(model, params);
		new LMSS08GPanel("lmss08g_panel").processPanelData(model, params);
		new LMSS08LPanel("lmss08l_panel").processPanelData(model, params);
		// J-112-0451  新增敘做條件異動比較表(與企金共用)
		new LMSS08MPanel("lmss08m_panel").processPanelData(model, params);
		// UPGRADE: 前端須配合改Thymeleaf的樣式
		// add(_getComp("DocTypeA", LMSUtil.isParentCase(model)));
		model.addAttribute("DocTypeA", LMSUtil.isParentCase(l120m01a));
	}

	// private Component _getComp(String id, boolean visible){
	// Label r = new Label(id, "");
	// r.setVisible(visible);
	// return r;
	// }
}
