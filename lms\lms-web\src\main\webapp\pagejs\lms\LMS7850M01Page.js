var LMS7840Action = {
	fhandle : "lms7850m01formhandler",
	ghandle : "lms1405gridhandler",
	gSaveOkCanSend : "N",
	autoRefreshStart : "N",   
	// 按鈕-開始查詢
	query : function(callFrom) {
		//開始查詢-- 1.蒐集查詢條件
		var $filterForm = $("#filterForm");
		// 初始化
	 
		//J-107-0069-001 e-Loan授信系統「同類授信對象」之搜尋條件請增加「模型評等等級」，並請開放區域營運中心可代營業單位搜尋全行符合條件之同類授信對象。
		$("#fxCrGrade").trigger("change");
		$("#fxIsCls" ).trigger("change");
		 
		//進入THICKBOX 前先把欄位內容記住，如果THCIKBOX 暗下取消，要把之前的舊值回復
		var bfFilterForm = $("#filterForm").serializeData();
		$("input[name='fxIsCls'][value='N']:radio" ).prop( "checked" , "checked" );   //塞值
		$("#fxIsCls" ).trigger("change");
		$("#filterBox7840")
				.thickbox(
						{
							// 開始查詢
							title : i18n.lms7850m01['button.search'],
							width : 700,
							height : 600,
							modal : true,
							valign : "bottom",
							i18n : i18n.def,
							align : "center",
							buttons : {
								"sure" : function() {

									if (!$("#filterForm").valid()) {
										return;
									}
									
									var fxIsCls = $("[name='fxIsCls']:radio:checked").val();
									
									// 2012-09-06 黃建霖 begin
									// 只有在有輸入日期欄位時才要檢查
									if ($.trim($("#fromDate").val()) != ""
											|| $.trim($("#endDate").val()) != "") {
										if ($.trim($("#endDate").val()) == ""
												|| $.trim($("#fromDate").val()) == "") {
											// l120v05.message03=請輸入日期
											return CommonAPI
													.showErrorMessage(i18n.lms7850m01["L784M01A.caseDate"]
															+ i18n.lms7850m01["l120v05.message03"]);
										}

										if ($("#fromDate").val() > $("#endDate")
												.val()) {
											// l120v05.message02=起始日期不能大於結束日期
											return CommonAPI
													.showErrorMessage(i18n.lms7850m01["L784M01A.caseDate"]
															+ i18n.lms7850m01["l120v05.message02"]);
										}
									}

									if ($.trim($("#approveDateS").val()) != ""
											|| $.trim($("#approveDateE").val()) != "") {
										if ($.trim($("#approveDateE").val()) == ""
												|| $.trim($("#approveDateS")
														.val()) == "") {
											// l120v05.message03=請輸入日期
											return CommonAPI
													.showErrorMessage(i18n.lms7850m01["L784M01A.approveTime"]
															+ i18n.lms7850m01["l120v05.message03"]);
										}

										if ($("#approveDateS").val() > $(
												"#approveDateE").val()) {
											// l120v05.message02=起始日期不能大於結束日期
											return CommonAPI
													.showErrorMessage(i18n.lms7850m01["L784M01A.approveTime"]
															+ i18n.lms7850m01["l120v05.message02"]);
										}
									}

									if(fxIsCls!="Y"){
										if ($.trim($("#fxLmtDays1").val()) != ""
											|| $.trim($("#fxLmtDays2").val()) != "") {

											if ($.trim($("#fxLmtDays1").val()) == "") {
												// l120v05.message04=請輸入
												return CommonAPI
														.showErrorMessage(i18n.lms7850m01["L784M01A.fxLmtDays"]
																+ i18n.lms7850m01["l120v05.message04"]);
											}
	
											if ($.trim($("#fxLmtDays1").val()) != ""
													&& $.trim($("#fxLmtDays2")
															.val()) != "") {
												if (parseInt(
														$("#fxLmtDays2").val(), 10) < parseInt(
														$("#fxLmtDays1").val(), 10)) {
													// l120v05.message01=起始天數不能大於結束天數
													return CommonAPI
															.showErrorMessage(i18n.lms7850m01["L784M01A.fxLmtDays"]
																	+ i18n.lms7850m01["l120v05.message01"]);
												}
											}
	
										}
										
										
										 //J-107-0069-001 e-Loan授信系統「同類授信對象」之搜尋條件請增加「模型評等等級」，並請開放區域營運中心可代營業單位搜尋全行符合條件之同類授信對象。
										 var crGrade = $("#fxCrGrade").val();  //取值
										 switch ( crGrade) {
								         case "A" :
								         case "B" :
								         case "C" :
								         case "D" :
								         case "E" :
								        	 //企業類別顯示 
								             if($.trim($( "#fxCrKind").val())==''){
			                                        //請輸入企業類別
													return CommonAPI
													.showErrorMessage(i18n.lms7850m01["l120v05.message04"]+i18n.lms7850m01["L784M01A.fxCrKind"]
															  );
								             } 
								             break;
								         default:
								        	//企業類別隱藏
								        	 $( "#fxCrKind").val('');
								             break;
										 }
										
									}else{
										if ($.trim($("#fxLnMonth1").val()) != ""
											|| $.trim($("#fxLnMonth2").val()) != "") {

										if ($.trim($("#fxLnMonth1").val()) == "") {
											// l120v05.message04=請輸入
											return CommonAPI
													.showErrorMessage(i18n.lms7850m01["L784M01A.fxLnMonth"]
															+ i18n.lms7850m01["l120v05.message04"]);
										}

										if ($.trim($("#fxLnMonth1").val()) != ""
												&& $.trim($("#fxLnMonth2")
														.val()) != "") {
											if (parseInt(
													$("#fxLnMonth2").val(), 10) < parseInt(
													$("#fxLnMonth1").val(), 10)) {
												// l120v05.message05=起始月不能大於結束月
												return CommonAPI
														.showErrorMessage(i18n.lms7850m01["L784M01A.fxLnMonth"]
																+ i18n.lms7850m01["l120v05.message05"]);
											}
										}
									}
									
									
									 

								}

									// 2012-09-06 黃建霖 end

									// 啟用進階查詢--要檢核
//									if ($filterForm.find("#fxLnSubject").val() == ""
//											&& $filterForm.find("#fxRateText")
//													.val() == ""
//											&& $filterForm
//													.find("#fxCollateral")
//													.val() == ""
//											&& $filterForm.find(
//													"#fxCollateral1").val() == ""
//											&& $filterForm.find("#fxBusCode")
//													.val() == ""
//											&& $filterForm.find("#fxCurr")
//													.val() == ""
//											&& $filterForm.find("#fxLmtDays1")
//													.val() == ""
//											&& $filterForm.find("#fxLmtDays2")
//													.val() == ""
//											&& $filterForm.find("#fxRateText1")
//													.val() == ""
//											&& $filterForm.find("#fxGuarantor")
//													.val() == ""
//											&& $filterForm.find(
//													"fxOtherCondition").val() == ""
//											&& $filterForm.find("#cntrNo")
//													.val() == ""
//											&& $filterForm.find("#custId")
//													.val() == ""
//											&& $filterForm.find("#custName")
//													.val() == "") {
//										return CommonAPI
//												.showErrorMessage(i18n.lms7850m01["L784M01A.fxMessage01"]);
//										// return
//										// CommonAPI.showErrorMessage("已啟用進階查詢，請至少輸入一個進階查詢條件");
//									}

									if ($filterForm.find("#fromDate").val() == ""
											&& $filterForm.find("#endDate")
													.val() == ""
											&& $filterForm
													.find("#approveDateS")
													.val() == ""
											&& $filterForm
													.find("#approveDateE")
													.val() == "") {
										return CommonAPI
												.showErrorMessage(i18n.lms7850m01["L784M01A.fxMessage02"]);
										// return
										// CommonAPI.showErrorMessage("已啟用進階查詢，請至少輸入一個日期查詢條件(期間不得超過一年)");
									}

									var diff1 = 0;
									if ($.trim($("#fromDate").val()) != ""
											&& $.trim($("#endDate").val()) != "") {
										var mDateS = $("#fromDate").val()
												.split("-");
										var mDateE = $("#endDate").val().split(
												"-");
										var diffDay = ((mDateE[0] - mDateS[0]) * 12)
												+ (mDateE[1] - mDateS[1]);
										diff1 = diffDay;
										if (diffDay > 12) {
											// 查詢起迄日期區間不得相差一年以上
											return CommonAPI
													.showErrorMessage(i18n.lms7850m01["L784M01A.caseDate"]
															+ i18n.lms7850m01["L784M01A.fxMessage03"]);
											// return false;
										}
									}

									var diff2 = 0;
									if ($.trim($("#approveDateS").val()) != ""
											&& $.trim($("#approveDateE").val()) != "") {
										var mDateS = $("#approveDateS").val()
												.split("-");
										var mDateE = $("#approveDateE").val()
												.split("-");
										var diffDay = ((mDateE[0] - mDateS[0]) * 12)
												+ (mDateE[1] - mDateS[1]);
										diff2 = diffDay;
										if (diffDay > 12) {
											// 查詢起迄日期區間不得相差一年以上
											return CommonAPI
													.showErrorMessage(i18n.lms7850m01["L784M01A.approveTime"]
															+ i18n.lms7850m01["L784M01A.fxMessage03"]);
											// return false;
										}
									}

									if ($.trim($("#fromDate").val()) != ""
										&& $.trim($("#endDate").val()) != ""
										&& $.trim($("#approveDateS").val()) != ""
										&& $.trim($("#approveDateE").val()) != "") {

										var minDateS = null;
										if($.trim($("#fromDate").val()) >= $.trim($("#approveDateS").val()) ){
											minDateS = $.trim($("#approveDateS").val()).split("-");
											
										}else{
											minDateS = $.trim($("#fromDate").val()).split("-");
											
										}
										
										var maxDateE = null;
										if($.trim($("#endDate").val()) >= $.trim($("#approveDateE").val()) ){
											maxDateE = $.trim($("#endDate").val()).split("-");
											
										}else{
											maxDateE = $.trim($("#approveDateE").val()).split("-");
											
										}
										
										var diffDay = ((maxDateE[0] - minDateS[0]) * 12)
										+ (maxDateE[1] - minDateS[1]);
										
										if (diffDay > 12) {
											// 總查詢起迄日期區間不得相差一年以上
											return CommonAPI
													.showErrorMessage(i18n.lms7850m01["L784M01A.caseDate"]
															+ "、"
															+ i18n.lms7850m01["L784M01A.approveTime"]
															+ i18n.lms7850m01["L784M01A.fxMessage04"]);
										}
									}
									
									
//									if ($.trim($("#fromDate").val()) != ""
//											&& $.trim($("#endDate").val()) != ""
//											&& $.trim($("#approveDateS").val()) != ""
//											&& $.trim($("#approveDateE").val()) != "") {
//
//										if ((diff1 + diff2) > 12) {
//											// 總查詢起迄日期區間不得相差一年以上
//											return CommonAPI
//													.showErrorMessage(i18n.lms7850m01["L784M01A.caseDate"]
//															+ "、"
//															+ i18n.lms7850m01["L784M01A.approveTime"]
//															+ i18n.lms7850m01["L784M01A.fxMessage04"]);
//										}
//									}

									//LMS7840Action.grid("queryL140m01a2", "Y");
									if(callFrom == "918"){
										LMS7840Action.sendBoss918();
									}else{
										LMS7840Action.sendBoss();
									}
									
									//$.thickbox.close();

								},
								"cancel" : function() {
									API.confirmMessage(i18n.def['flow.exit'],
											function(res) {
												if (res) {
													//舊值回復
													$filterForm.reset();
													$("#filterForm").injectData(bfFilterForm);
													$.thickbox.close();
												}
											});
								}
							}
						});
	},
	sendBoss: function(){
        //build buttons
        //呈主管覆核
        // L784M01A.fxMessage06=執行「確定」後，會先將本案送呈主管覆核，是否確定繼續?
		API.confirmMessage(i18n.lms7850m01['L784M01A.fxMessage06'], function(b){
            if (b) {
            	// 待覆核
            	LMS7840Action.save();
            	if (LMS7840Action.gSaveOkCanSend == "Y") {
            		LMS7840Action.flowAction({
                        flowAction: "sendStop"
                    });
            	}
            	LMS7840Action.gSaveOkCanSend = "N";  
            	$.thickbox.close();
            }
        });
        
    },
    sendBoss918: function(){
        //build buttons
        //呈主管覆核
        // L784M01A.fxMessage06_918=執行「確定」後，請直接於已核准案件中調閱結果，是否確定繼續?
		API.confirmMessage(i18n.lms7850m01['L784M01A.fxMessage06_918'], function(b){
            if (b) {
            	// 待覆核
            	LMS7840Action.save();
            	if (LMS7840Action.gSaveOkCanSend == "Y") {
            		LMS7840Action.flowAction918({
                        flowAction: "sendStop"
                    });
            	}
            	LMS7840Action.gSaveOkCanSend = "N";  
            	$.thickbox.close();
            }
        });
        
    },
    /**
     * 流程
     */
    flowAction: function(sendData){
        $.ajax({
            handler: LMS7840Action.fhandle,
            data: $.extend({
                formAction: "flowAction",
                mainOid: responseJSON.mainOid
            }, (sendData || {})),
        }).done(function () {
			CommonAPI.triggerOpener("gridview", "reloadGrid");
			API.showPopMessage(i18n.def["runSuccess"], window.close);
		});
    },
    /**
     * 流程-918，不用主管覆核
     */
    flowAction918: function(sendData){
        $.ajax({
            handler: LMS7840Action.fhandle,
            data: $.extend({
                formAction: "flowAction",
                mainOid: responseJSON.mainOid
            }, (sendData || {})),
        }).done(function () {
			//918授管處直接送到已核准，不用主管覆核
			LMS7840Action.flowAction({
				flowAction: "check"
			});

//                CommonAPI.triggerOpener("gridview", "reloadGrid");
//                API.showPopMessage(i18n.def["runSuccess"], window.close);
		});
    },
    /**
     * 儲存
     * @param {Object} data
     */
    save: function(data){
        
    	var $filterForm = $("#filterForm");
        
        $.ajax({
            async: false,
            handler: LMS7840Action.fhandle,
            action: 'saveL785m01a',
            data: {
                oid: responseJSON.oid,
                mainId: responseJSON.mainId,
                filterForm : JSON.stringify($("#filterForm")
						.serializeData())
            },
        }).done(function (result) {
			// CommonAPI.showMessage(result.NOTIFY_MESSAGE);
			LMS7840Action.gSaveOkCanSend="Y";
		});
            
           
        return true;
        
    },
	grid : function(action, newQuery) {
		
		//開始查詢-- 2.主要查詢程式
		
		var $filterForm = $("#filterForm");
		if (newQuery == "Y") {
			$.blockUI();
		} else {
			$.thickbox.close();
		}
		
		$("#gridview")
				.jqGrid(
						"setGridParam",
						{
							postData : $.extend($("#filterForm")
									.serializeData(), {
								handler : LMS7840Action.ghandle,
								formAction : action,
								rowNum : 15,
								newQuery : newQuery,
								filterForm : JSON.stringify($("#filterForm")
										.serializeData())
							}),
							loadComplete : function() {
								
								if (newQuery == "Y") {
									LMS7840Action.autoRefreshStart= "Y";
									LMS7840Action.getBatchRunResult();
									// 加入排程成功，請稍後重整頁面並檢視右上方「查詢結果」欄位內容！

									$.unblockUI();
									$("#gridview").jqGrid("setGridParam", {
										postData : {
											newQuery : "N"
										},
										loadComplete : function() {
											// 執行完後把loadComplete清空，要不然GRID
											// 的REFRESH也會觸發上面的setSelection
											LMS7840Action.getBatchRunResult();
										}

									})
									newQuery = "N";
	
									CommonAPI
											.showMessage(i18n.lms7850m01['L784M01A.fxMessage05']);
									
									$("#gridview").trigger("reloadGrid");
								}

							},
							loadError : function() {
								LMS7840Action.autoRefreshStart= "N";
								if (newQuery == "Y") {
									LMS7840Action.getBatchRunResult();
									$.unblockUI();
									$("#gridview").jqGrid("setGridParam", {
										postData : {
											newQuery : "N"
										},
										loadComplete : function() {
											// 執行完後把loadComplete清空，要不然GRID
											// 的REFRESH也會觸發上面的setSelection
											LMS7840Action.getBatchRunResult();
										}

									})
									newQuery = "N";
									$("#gridview").trigger("reloadGrid");
								}

							}
						}).trigger("reloadGrid");

	},
	dateObjtoStr : function(tDate) {
		return tDate.getFullYear() + "-" + (tDate.getMonth() < 9 ? "0" : "")
				+ (tDate.getMonth() + 1) + "-"
				+ (tDate.getDate() < 10 ? "0" : "") + tDate.getDate();
	},
	// 選擇人員清單
	selectPeople : function() {
		$("#selectPeopleBox").thickbox(
				{
					// 開始查詢
					title : i18n.lms7850m01['button.search'],
					width : 400,
					height : 170,
					modal : true,
					valign : "bottom",
					i18n : i18n.def,
					align : "center",
					buttons : {
						"sure" : function() {
							LMS7840Action.gridRelod($("#custIdSelect").val());
							var idName = $("#custIdSelect :selected").text()
									.split(" ");
							$("#dupNo").text(idName[0]);
							$("#custName").text(idName[1]);
							$.thickbox.close();
						}
					}
				});
	},
	gridRelod : function(dupNo) {
		$("#gridview").jqGrid("setGridParam", {
			postData : {
				custId : $("#inputCustId").val(),
				dupNo : dupNo
			},
			search : true
		}).trigger("reloadGrid");
	},
	printDoc : function(cellvalue, options, rowObject) {

		$.ajax({
			handler : LMS7840Action.fhandle,
			type : "POST",
			dataType : "json",
			data : {
				formAction : "openCntrDocPdf",
				tabFormMainId : rowObject.mainId
			},
		}).done(function (obj) {
			if (obj.isCls == "Y") {
				var itemType = obj.itemType;
				var tabFormOid = obj.tabFormOid;
				var typCd = "";
				if (obj.typCd == "5") {
					typCd = "5";
				} else {
					typCd = "1";
				}

				var pdfName = "CLS1151R01.pdf";

				var rptMainId = obj.rptMainId;

				var rType = "R12"

				var content = ""; // R12^10C4E73B266C11E3B4D7126EC0A83B82^73251209^0^006109290001^
				content = rType + "^" + tabFormOid + "^" + rowObject.custId
					+ "^" + rowObject.dupNo + "^" + rowObject.cntrNo
					+ "^" + rowObject.mainId + "^";
				pdfName = pdfName;

				// $.thickbox.close();
				$.form.submit({
					url : "../../simple/FileProcessingService",
					target : "_blank",
					data : {
						mainId : rptMainId,
						rptOid : content,
						fileDownloadName : pdfName,
						serviceName : "cls1141r01rptservice"
					}
				});

			} else {
				var itemType = obj.itemType;
				var tabFormOid = obj.tabFormOid;
				var typCd = "";
				if (obj.typCd == "5") {
					typCd = "5";
				} else {
					typCd = "1";
				}

				var pdfName = "LMS140" + typCd + "R01.pdf";

				var rptMainId = obj.rptMainId;

				var rType = "R12"
				if (itemType == "1") {
					pdfName = "LMS140" + typCd + "R01.pdf";
					rType = "R12"
				} else if (itemType == "2") {
					pdfName = "LMS140" + typCd + "R02.pdf";
					rType = "R13"
				} else {
					pdfName = "LMS140" + typCd + "R01.pdf";
					rType = "R12"
				}

				var content = ""; // R12^10C4E73B266C11E3B4D7126EC0A83B82^73251209^0^006109290001^
				content = rType + "^" + tabFormOid + "^" + rowObject.custId
					+ "^" + rowObject.dupNo + "^" + rowObject.cntrNo
					+ "^" + rowObject.mainId + "^";
				pdfName = pdfName;

				// $.thickbox.close();
				$.form.submit({
					url : "../simple/FileProcessingService",
					target : "_blank",
					data : {
						mainId : rptMainId,
						rptOid : content,
						fileDownloadName : pdfName,
						serviceName : "lms120" + typCd + "r01rptservice"
					}
				});
			}
		});

	},
	produceExcel : function() {

		$.form.submit({
			url : "../../simple/FileProcessingService",
			target : "_blank",
			data : {
				fileDownloadName : "LMS7840M01A.xls",
				logMainId : $("#logMainId").val(),
				serviceName : "lms7840xlsservice"
			}
		});

	},
	getBatchRunResult : function() {
		
		$.ajax({
			handler : LMS7840Action.fhandle,
			action : "getBatchRunResult",
			data : {
				oid: responseJSON.oid,
                mainId: responseJSON.mainId
            },
		}).done(function (sJson) {
			//頁首
			$("#caseDate").html(DOMPurify.sanitize(sJson.caseDate));

			//TITLE表格內容
			$("#titleDiv").find("#queryResult").val(sJson.result);
			$("#titleDiv").find("#queryConditon").html(DOMPurify.sanitize(sJson.itemDscr));
			$("#titleDiv").find("#createTime1").html(DOMPurify.sanitize(sJson.createTime));
			$("#titleDiv").find("#creator1").html(DOMPurify.sanitize(sJson.creator));
			$("#titleDiv").find("#updater1").html(DOMPurify.sanitize(sJson.updater));
			$("#titleDiv").find("#updateTime1").html(DOMPurify.sanitize(sJson.updateTime));
			$("#titleDiv").find("#approver1").html(DOMPurify.sanitize(sJson.approver));
			$("#titleDiv").find("#approveTime1").html(DOMPurify.sanitize(sJson.approveTime));
			$("#titleDiv").find("#caseNo").html(DOMPurify.sanitize(sJson.caseNo));
			// $("#queryErrMsg").val(sJson.execMsg);
			$("#titleDiv").find("#exeCount").val(sJson.exeCount);


			//頁尾
			$("#logMainId").val(sJson.logMainId);

			if(sJson.itemDscrJson){
				$("#filterForm").injectData(sJson.itemDscrJson);
			}

			if ($("#queryResult").val() == "N") {
				$("#openExecMsgBt").show();
				$("#showExeCount").hide();
			} else if ($("#queryResult").val() == "Y") {
				$("#openExecMsgBt").hide();
				$("#showExeCount").show();
			} else {
				$("#openExecMsgBt").hide();
				$("#showExeCount").hide();
			}

			if ($("#queryResult").val() != "Y"
				&& $("#queryResult").val() != "N" && $("#queryResult").val() ) {
				LMS7840Action.autoRefreshStart= "Y";
			} else{
				LMS7840Action.autoRefreshStart= "N";
			}
		});

	},
	selectFxCurr : function() {

		var item = API.loadOrderCombosAsList("Common_Currcy")["Common_Currcy"];
		$("#fxCurr_1").setItems({
			size : "1",
			item : convertItems(item),
			clear : true,
			itemType : 'checkbox',
			format : '{value} - {key}'
		});

		$("#loginFxCurr")
				.thickbox(
						{
							// L140M01a.select=選取關鍵字
							title : "",
							width : 350,
							height : 300,
							modal : true,
							align : "center",
							valign : "bottom",
							readOnly : false,
							i18n : i18n.def,
							buttons : {
								"sure" : function() {
									$.thickbox.close();
									var allCheacked = [];
									var allCheackedVal = [];
									$
											.each(
													$("#loginFxCurr :checkbox[name=fxCurr_1]:checked"),
													function(i, n) {
														allCheacked[i] = i18n.Common_Currcy[$(
																n).val()];
														allCheackedVal[i] = $(n)
																.val();
													});

									$("#fxCurrShow").val(allCheacked);
									$("#fxCurr").val(allCheackedVal.join("|"));
								},
								"cancel" : function() {
									$.thickbox.close();
								}
							}
						});
	},
	selectFxLnSubject : function() {

		var item = API.loadOrderCombosAsList("lms1405m01_SubItem")["lms1405m01_SubItem"];
		$("#fxLnSubject_1").setItems({
			size : "1",
			item : convertItems(item),
			clear : true,
			itemType : 'checkbox',
			format : '{value} - {key}'
		});

		$("[name=fxLnSubject_1]").removeAttr("disabled").removeAttr("checked");
		$("#loginFxLnSubject")
				.thickbox(
						{
							// L140M01a.select=選取關鍵字
							title : "",
							width : 350,
							height : 300,
							modal : true,
							align : "center",
							valign : "bottom",
							readOnly : false,
							i18n : i18n.def,
							buttons : {
								"sure" : function() {
									$.thickbox.close();
									var allCheacked = [];
									var allCheackedVal = [];
									$
											.each(
													$("#loginFxLnSubject :checkbox[name=fxLnSubject_1]:checked"),
													function(i, n) {
														allCheacked[i] = i18n.lms1405m01_SubItem[$(
																n).val()];
														allCheackedVal[i] = $(n)
																.val();
													});

									$("#fxLnSubjectShow").val(allCheacked);
									$("#fxLnSubject").val(
											allCheackedVal.join("|"));
								},
								"cancel" : function() {
									$.thickbox.close();
								}
							}
						});
	},
	selectFxRateText1 : function() {
		//
		$
				.ajax({
					handler : LMS7840Action.fhandle,
					data : {// 把資料轉成json
						formAction : "queryFxRateText"
					},
				}).done(function (obj) {
			var fxRateText1_1 = $("#fxRateText1_1");
			fxRateText1_1.setItems({
				item : obj,
				size : "1",
				clear : true,
				itemType : 'checkbox',
				space : false,
				format : "{value} - {key}"
			});
			$("#loginFxRateText1")
				.thickbox(
					{
						// L140M01a.select=選取關鍵字
						title : "",
						width : 500,
						height : 500,
						modal : true,
						align : "center",
						valign : "bottom",
						readOnly : false,
						i18n : i18n.def,
						buttons : {
							"sure" : function() {
								$.thickbox.close();
								var allCheacked = [];
								var allCheackedVal = [];
								$
									.each(
										$("#loginFxRateText1 :checkbox[name=fxRateText1_1]:checked"),
										function(i,
												 n) {
											allCheacked[i] = obj[$(
												n)
												.val()];
											allCheackedVal[i] = $(
												n)
												.val();
										});

								var fxRateText1Show = $("#fxRateText1Show");
								var fxRateText1 = $("#fxRateText1");
								fxRateText1Show.val(
									allCheacked);
								fxRateText1.val(
									allCheackedVal
										.join("|"));
							},
							"cancel" : function() {
								$.thickbox.close();
							}
						}
					});
		}); // close ajax
	},
	selectFxCollateral1 : function() {
		// 擔保品種類
		var item = API.loadOrderCombosAsList("lmsUseCms_collTyp1")["lmsUseCms_collTyp1"];
		$("#fxCollateral1_1").setItems({
			size : "1",
			item : convertItems(item),
			clear : true,
			itemType : 'checkbox',
			format : '{value} - {key}'
		});

		$("#loginFxCollateral1")
				.thickbox(
						{
							// L140M01a.select=選取關鍵字
							title : "",
							width : 350,
							height : 300,
							modal : true,
							align : "center",
							valign : "bottom",
							readOnly : false,
							i18n : i18n.def,
							buttons : {
								"sure" : function() {
									//J-112-0449_05097_B1001 Web e-Loan企金額度明細表新增主要用途查詢條件
									var has01 ="N";
									var only01 ="Y";
									$.thickbox.close();
									var allCheacked = [];
									var allCheackedVal = [];
									$.each($("#loginFxCollateral1 :checkbox[name=fxCollateral1_1]:checked"),
									   function(i, n) {
										allCheacked[i] = i18n.lmsUseCms_collTyp1[$(
												n).val()];
										allCheackedVal[i] = $(n)
												.val();
										
										//J-112-0449_05097_B1001 Web e-Loan企金額度明細表新增主要用途查詢條件
										if($(n).val() == "01" ){
											has01 ="Y"; 
										} else{
											only01 = "N";
										}

									});

									$("#fxCollateral1Show").val(allCheacked);
									$("#fxCollateral1").val(
											allCheackedVal.join("|"));
							 
									//J-112-0449_05097_B1001 Web e-Loan企金額度明細表新增主要用途查詢條件
									if(has01 == "Y" ){
										$(".showFxBldUse").show();
									}else{
										$("#fxBldUseShow").val('');
										$("#fxBldUse").val('');
										$(".showFxBldUse").hide();
									}
									
									if(only01 == "Y" ){
										$(".showFxOnlyLand").show();
									}else{
										$("input[name='fxOnlyLand']:radio:checked" ).prop( "checked" ,false); 
										$(".showFxOnlyLand").hide();
									}
								},
								"cancel" : function() {
									$.thickbox.close();
								}
							}
						});
	},
	selectFxBldUse : function() {
		//J-112-0449_05097_B1001 Web e-Loan企金額度明細表新增主要用途查詢條件
		// 建物使用用途
		var item = API.loadOrderCombosAsList("cms1010_bldUse")["cms1010_bldUse"];
		$("#fxBldUse_1").setItems({
			size : "1",
			item : convertItems(item),
			clear : true,
			itemType : 'checkbox',
			format : '{value} - {key}'
		});

		$("#loginFxBldUse")
				.thickbox(
						{
							// L140M01a.select=選取關鍵字
							title : "",
							width : 350,
							height : 300,
							modal : true,
							align : "center",
							valign : "bottom",
							readOnly : false,
							i18n : i18n.def,
							buttons : {
								"sure" : function() {
									$.thickbox.close();
									var allCheacked = [];
									var allCheackedVal = [];
									$
											.each(
													$("#loginFxBldUse :checkbox[name=fxBldUse_1]:checked"),
													function(i, n) {
														allCheacked[i] = i18n.cms1010_bldUse[$(
																n).val()];
														allCheackedVal[i] = $(n)
																.val();
													});

									$("#fxBldUseShow").val(allCheacked);
									$("#fxBldUse").val(
											allCheackedVal.join("|"));
								},
								"cancel" : function() {
									$.thickbox.close();
								}
							}
						});
	},
	openExecMsg : function() {
		$.ajax({
			type : "POST",
			handler : LMS7840Action.fhandle,
			data : {
				formAction : "getExecMsg",
				logMainId : $("#logMainId").val()
			},
		}).done(function (responseData) {
			$("#execMsg").val(responseData.execMsg);

			$("#loginExecMsg").thickbox({
				title : "",
				width : 700,
				height : 600,
				modal : true,
				align : "center",
				valign : "bottom",
				readOnly : false,
				i18n : i18n.def,
				buttons : {
					"cancel" : function() {
						$.thickbox.close();
					}
				}
			});
		});

	},
	autoRefreshViewStart : function(){
		setTimeout(function() {
			if(LMS7840Action.autoRefreshStart== "Y"){
				$("#gridview").trigger("reloadGrid");
			}
			LMS7840Action.autoRefreshViewStart();
		}, 10000);
		 
	},
	selectFxProdKind : function() {
		// 個金產品種類
		var item = API.loadOrderCombosAsList("lms2415m01_lnType")["lms2415m01_lnType"];
		var itemArr = [];
		item = convertItems(item);
		
		$.each(item,function(i, n) {
			//console.log("XX="+n.value + " "+n.desc);
			itemArr[n.value]=n.desc;
		});
		 
		$("#fxProdKind_1").setItems({
			size : "1",
			item : item,
			clear : true,
			itemType : 'checkbox',
			format : '{value} - {key}'
		});

		$("#loginFxProdKind")
				.thickbox(
						{
							// L140M01a.select=選取關鍵字
							title : "",
							width : 350,
							height : 300,
							modal : true,
							align : "center",
							valign : "bottom",
							readOnly : false,
							i18n : i18n.def,
							buttons : {
								"sure" : function() {
									$.thickbox.close();
									var allCheacked = [];
									var allCheackedVal = [];
									$.each( 
										$("#loginFxProdKind :checkbox[name=fxProdKind_1]:checked"),
										function(i, n) {
											allCheacked[i] = $(n).val()+"."+itemArr[$(n).val()];
											allCheackedVal[i] = $(n).val();
											
										});

									$("#fxProdKindShow").val(allCheacked);
									$("#fxProdKind").val(
											allCheackedVal.join("|"));
								},
								"cancel" : function() {
									$.thickbox.close();
								}
							}
						});
	},
	selectFxLnSubjectCls : function() {
		// 個金科目
		$
		.ajax({
			handler : LMS7840Action.fhandle,
			data : {// 把資料轉成json
				formAction : "queryClsSubject"
			},
		}).done(function (obj) {
			var fxLnSubjectCls_1 = $("#fxLnSubjectCls_1");
			fxLnSubjectCls_1.setItems({
				item : obj,
				size : "1",
				clear : true,
				itemType : 'checkbox',
				space : false,
				format : "{value} - {key}"
			});
			$("#loginFxLnSubjectCls")
				.thickbox(
					{
						// L140M01a.select=選取關鍵字
						title : "",
						width : 500,
						height : 500,
						modal : true,
						align : "center",
						valign : "bottom",
						readOnly : false,
						i18n : i18n.def,
						buttons : {
							"sure" : function() {
								$.thickbox.close();
								var allCheacked = [];
								var allCheackedVal = [];
								$
									.each(
										$("#loginFxLnSubjectCls :checkbox[name=fxLnSubjectCls_1]:checked"),
										function(i,
												 n) {
											allCheacked[i] = obj[$(
												n)
												.val()];
											allCheackedVal[i] = $(
												n)
												.val();
										});

								var fxLnSubjectClsShow = $("#fxLnSubjectClsShow");
								var fxLnSubjectCls = $("#fxLnSubjectCls");
								fxLnSubjectClsShow.val(
									allCheacked);
								fxLnSubjectCls.val(
									allCheackedVal
										.join("|"));
							},
							"cancel" : function() {
								$.thickbox.close();
							}
						}
					});
		}); // close ajax
	},
	selectFxRateTextCls : function() {
		// 個金利率
		$
		.ajax({
			handler : LMS7840Action.fhandle,
			data : {// 把資料轉成json
				formAction : "queryFxRateText"
			},
		}).done(function (obj) {
			var fxRateTextCls_1 = $("#fxRateTextCls_1");
			fxRateTextCls_1.setItems({
				item : obj,
				size : "1",
				clear : true,
				itemType : 'checkbox',
				space : false,
				format : "{value} - {key}"
			});
			$("#loginFxRateTextCls")
				.thickbox(
					{
						// L140M01a.select=選取關鍵字
						title : "",
						width : 500,
						height : 500,
						modal : true,
						align : "center",
						valign : "bottom",
						readOnly : false,
						i18n : i18n.def,
						buttons : {
							"sure" : function() {
								$.thickbox.close();
								var allCheacked = [];
								var allCheackedVal = [];
								$
									.each(
										$("#loginFxRateTextCls :checkbox[name=fxRateTextCls_1]:checked"),
										function(i,
												 n) {
											allCheacked[i] = obj[$(
												n)
												.val()];
											allCheackedVal[i] = $(
												n)
												.val();
										});

								var fxRateTextClsShow = $("#fxRateTextClsShow");
								var fxRateTextCls = $("#fxRateTextCls");

								fxRateTextClsShow.val(
									allCheacked);
								fxRateTextCls.val(
									allCheackedVal
										.join("|"));
							},
							"cancel" : function() {
								$.thickbox.close();
							}
						}
					});
		}); // close ajax
	}
	
	
};
$(function(){

	var $filterForm = $("#filterForm");

	$filterForm.reset();
	// HIDDEN 要手動清
	$filterForm.find("#fxCurr").val('');
	$filterForm.find("#fxLnSubject").val('');
	$filterForm.find("#fxRateText1").val('');
	$filterForm.find("#fxCollateral1").val('');
	
	
	// set default value
	var sysdate = CommonAPI.getToday().split("-");
	var endDate = new Date(sysdate[0], sysdate[1] - 1, sysdate[2]);
	var fromDate = new Date(sysdate[0], sysdate[1] - 1, sysdate[2]);
	fromDate.setMonth(fromDate.getMonth() - 12);

	$("#fromDate").val(LMS7840Action.dateObjtoStr(fromDate));
	$("#endDate").val(LMS7840Action.dateObjtoStr(endDate));

	if (userInfo.unitType == "4") {
		$("#docKind").attr('value', '2');
	}

	if (userInfo.unitType == "2" || userInfo.unitType == "4") {
		// 授管處、營運中心
		$filterForm.find("#a91t2").show();
		$filterForm.find("#s91t1f2").hide();
	} else {
		$filterForm.find("#a91t2").hide();
		$filterForm.find("#s91t2").hide();
	}
	
	$("input[name='fxIsCls'][value='N']:radio" ).prop( "checked" , "checked" );   //塞值
	 
	
	// $filterForm.find("#fxFlag").click(function(){
	//        
	// if ($filterForm.find("#fxFlag").prop('checked')) {
	// //啟用進階查詢
	// $filterForm.find("#s91t1f2").show();
	// }else{
	// $filterForm.find("#s91t1f2").hide();
	// }
	// })
	LMS7840Action.autoRefreshStart= "Y";

	$.ajax({
		type : "POST",
		handler : "codetypehandler",
		data : {
			formAction : "allBranchByUnitType"
		},
	}).done(function (responseData) {
		var json = {
			format : "{value} - {key}",
			item : responseData
		};

		$("#caseBrId").setItems(json);
	});

	// LMS7840Action.query();
	var btn = $("#buttonPanel");
	btn.find("#btnSearch").click(function(showMsg) {
		LMS7840Action.query();
	}).end().find("#btnSearch918").click(function() {
		LMS7840Action.query("918");
	}).end().find("#btnExit").click(function() {
		setCloseConfirm(true);
	}).end().find("#btnProduceExcel").click(function() {
		LMS7840Action.produceExcel();
	});

	$filterForm.find("#selectFxCurrBt").click(function() {
		LMS7840Action.selectFxCurr();
	})

	$filterForm.find("#selectFxLnSubjectBt").click(function() {
		LMS7840Action.selectFxLnSubject();
	})

	$filterForm.find("#selectFxRateText1Bt").click(function() {
		LMS7840Action.selectFxRateText1();
	})

	$filterForm.find("#selectFxCollateral1Bt").click(function() {
		LMS7840Action.selectFxCollateral1();
	})
	
	$filterForm.find("#selectFxBldUseBt").click(function() {
		//J-112-0449_05097_B1001 Web e-Loan企金額度明細表新增主要用途查詢條件
		LMS7840Action.selectFxBldUse();
	})

	$("#openExecMsgBt").click(function() {
		LMS7840Action.openExecMsg();
	})
	
	$("[name='fxIsCls']:radio").change(function() {
		 var fxIsCls = $("[name='fxIsCls']:radio:checked").val();  //取值
		 switch ( fxIsCls) {
         case "Y" :
        	 //國內個金
             $( ".showForLms").hide();
             $( ".showForCls").show();
             // var $thisInput = $(this).parents("td").next("td").find("input:not(.caseReadOnly),textarea:not(.caseReadOnly),select");
             $(".showForLms").find("input,textarea,select").val('');
             break;
         default:
        	 //企金
             $( ".showForLms").show();
             $( ".showForCls").hide();
             $(".showForCls").find("input,textarea,select").val('');
             break;
		 }
	})
	
	//J-112-0449_05097_B1001 Web e-Loan企金額度明細表新增主要用途查詢條件
	$("[name='fxOnlyLand']:radio").change(function() {
		 var fxOnlyLand = $("[name='fxOnlyLand']:radio:checked").val();  //取值
		 switch ( fxOnlyLand) {
         case "Y" :
        	 $('.showFxBldUse').hide();
             break;
         case "N" :
        	 $('.showFxBldUse').show();
             break;
		 }
	})
	
	//J-107-0069-001 e-Loan授信系統「同類授信對象」之搜尋條件請增加「模型評等等級」，並請開放區域營運中心可代營業單位搜尋全行符合條件之同類授信對象。
	$("#fxCrGrade").change(function() {
		 var crGrade = $("#fxCrGrade").val();  //取值
		 switch ( crGrade) {
         case "A" :
         case "B" :
         case "C" :
         case "D" :
         case "E" :
        	 //企業類別顯示 
             $( "#showCrKind").show();
             break;
         default:
        	//企業類別隱藏
        	 $( "#showCrKind").hide();
             break;
		 }
	})
	
	
	
	$filterForm.find("#selectFxProdKindBt").click(function() {
		LMS7840Action.selectFxProdKind();
	})
	
	$filterForm.find("#selectFxLnSubjectClsBt").click(function() {
		LMS7840Action.selectFxLnSubjectCls();
	})
	
	$filterForm.find("#selectFxRateTextClsBt").click(function() {
		LMS7840Action.selectFxRateTextCls();
	})
	
	
	                
	
	
	
	

	var grid = $("#gridview").iGrid({
		handler : LMS7840Action.ghandle,// 'lms7840gridhandler'
		height : 350,
		rowNum : 15,
		sortname : 'caseDate|ownBrId|caseNo|custId|cntrNo',
		sortorder : 'desc|asc|desc|asc|asc',
		postData : {
			formAction : "queryL140m01a2",
			callFrom : "lms7850m01page",
			mainId : responseJSON.mainId,
			newQuery : "N"
		},
		colModel : [ {
			colHeader : i18n.lms7850m01['L784M01A.ownBrId'],// 分行別,
			name : 'ownBrId',
			width : 26,
			align : "center",
			sortable : true
		}, {
			colHeader : i18n.lms7850m01['L784M01A.custId'],// 客戶統編
			name : 'custId',
			width : 50,
			sortable : true
		}, {
			colHeader : i18n.lms7850m01['L784M01A.dupNo'],// 重覆序號
			name : 'dupNo',
			width : 20,
			align : "center",
			sortable : false
		}, {
			colHeader : i18n.lms7850m01['L784M01A.custName'],// 戶名
			name : 'custName',
			width : 120,
			sortable : true
		}, {
			colHeader : i18n.lms7850m01['L784M01A.caseNo'],// 案號
			name : 'caseNo',
			width : 160,
			sortable : true
		}, {
			colHeader : i18n.lms7850m01['L784M01A.cntrNo'],// 額度序號
			name : 'cntrNo',
			width : 60,
			align : "center",
			formatter : 'click',
			onclick : LMS7840Action.printDoc,
			sortable : true
		}, {
			colHeader : i18n.lms7850m01['L784M01A.currentApplyCurr'],// 幣別
			name : 'currentApplyCurr',
			width : 20,
			align : "center",
			sortable : true
		}, {
			colHeader : i18n.lms7850m01['L784M01A.currentApplyAmt'],// 現請額度
			name : 'currentApplyAmt',
			align : "right",
			formatter : GridFormatter.number['addComma'],
			width : 60,
			sortable : true
		}, {
			colHeader : i18n.lms7850m01['L784M01A.caseDate'],// 簽案日期
			name : 'caseDate',
			width : 50,
			sortable : true,
			align : "center"
		}, {
			colHeader : "oid",
			name : 'oid',
			hidden : true
		}, {
			colHeader : "mainId",
			name : 'mainId',
			hidden : true
		} ],
		ondblClickRow : function(rowid) { // 當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
			var data = $("#gridview").getRowData(rowid);
			LMS7840Action.printDoc(null, null, data);
		},
		loadComplete : function() {

			LMS7840Action.getBatchRunResult();

		}
	});
	
	LMS7840Action.autoRefreshViewStart();
	
	
	
	
});
