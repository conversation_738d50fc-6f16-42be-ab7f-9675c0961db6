/* 
 * CLS1141V07Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.eloan.lms.base.panels.GridViewFilterPanel01;

/**
 * <pre>
 * 授信簽報書 - 陳覆陳述(個金)
 * </pre>
 * 
 * @since 2011/11/9
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/11/9,Miller Lin,new
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls1141v07")
public class CLS1141V07Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		// 設定文件狀態(交易代碼)
		setGridViewStatus(CreditDocStatusEnum.海外_陳復案_陳述案);

		addToButtonPanel(model, LmsButtonEnum.View, LmsButtonEnum.Filter);
		// 套用哪個i18N檔案
		renderJsI18N(CLS1141V01Page.class);
		renderJsI18N(LMSCommomPage.class);

		setupIPanel(new GridViewFilterPanel01(PANEL_ID), model, params);

		// UPGRADE: 待確認畫面是否正常
		model.addAttribute("hasHtml", false);
		model.addAttribute("loadScript",
				"loadScript('pagejs/cls/CLS1141V01Page');");
	}

}
