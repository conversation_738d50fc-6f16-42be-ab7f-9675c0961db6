.fg-buttonset{
	outline: 0;
    text-decoration: none !important;
    cursor: pointer;
    position: relative;
    text-align: center;
    zoom: 1;
    font-size: 13px !important;
    display: inline-block;
    margin-bottom: 2px;
    margin-top: 2px;
	margin-right:5px;
	*width: auto;
    *overflow: visible;
	background: url('../img/sprite.png') repeat-x 0 0 !important;
	border-width: 1px 3px 3px 0 !important;
    border-style: solid !important;
    border-color: #808080 !important;
}
.fg-buttonset .fg-child {
    border-width: 0 1px;
    border-style: solid;
    border-color: #808080;
    margin: 0 -1px;
    _margin: 0;
    display: block;
    *display:inline-block;
}
.ui-widget-content .fg-button,
.fg-button {
    display: inline-block;
    border: none;
    margin: 0;
	background: url('../img/sprite.png') repeat-x 0 0 !important;
}

.fg-button .ui-icon {
    margin: 0 5px 0 0;
    position: absolute;
    left: .2em;
    top: 50%;
    margin-top: -8px;
}

.ui-button-text {
	padding: 3px 5px 5px 20px !important;
    *padding-bottom:0px !important;
	*padding-right:5px !important;
	color:#000;
	font-weight:normal;
}
.fg-button .text-only{
	margin-left:-5px;
	padding: 3px 5px 5px 0px !important;
	*padding-right:10px !important;
}
.fg-buttonset:hover ,.ui-state-hover {
    border-color: #7D98B8 !important;
    background-position: 0 -1300px !important;
}
.fg-buttonset:hover .fg-child,.ui-state-hover .fg-child {
    border-color: #7D98B8 !important;
}

button.ui-corner-all{
	-moz-border-radius:0px !important;
	border-radius: 0px !important;
}

.ui-icon-jcs-01 { background-position:    0   -240px; }
.ui-icon-jcs-02 { background-position:  -16px -240px; }
.ui-icon-jcs-03 { background-position:  -32px -240px; }
.ui-icon-jcs-04 { background-position:  -48px -240px; }
.ui-icon-jcs-05 { background-position:  -64px -240px; }
.ui-icon-jcs-06 { background-position:  -80px -240px; }
.ui-icon-jcs-07 { background-position:  -96px -240px; }
.ui-icon-jcs-08 { background-position: -112px -240px; }
.ui-icon-jcs-09 { background-position: -128px -240px; }
.ui-icon-jcs-10 { background-position: -144px -240px; }
.ui-icon-jcs-11 { background-position: -160px -240px; }
.ui-icon-jcs-12 { background-position: -176px -240px; }
.ui-icon-jcs-13 { background-position: -192px -240px; }
.ui-icon-jcs-14 { background-position: -208px -240px; }
.ui-icon-jcs-15 { background-position: -224px -240px; }
.ui-icon-jcs-16 { background-position: -240px -240px; }

.ui-icon-jcs-21 { background-position: -32px -64px; }

.ui-icon-jcs-101 { background-position:    0   -256px; }
.ui-icon-jcs-102 { background-position:  -16px -256px; }
.ui-icon-jcs-103 { background-position:  -32px -256px; }
.ui-icon-jcs-104 { background-position:  -48px -256px; }
.ui-icon-jcs-105 { background-position:  -64px -256px; }
.ui-icon-jcs-106 { background-position:  -80px -256px; }
.ui-icon-jcs-107 { background-position:  -96px -256px; }
.ui-icon-jcs-108 { background-position: -112px -256px; }
.ui-icon-jcs-109 { background-position: -128px -256px; }
.ui-icon-jcs-110 { background-position: -144px -256px; }
.ui-icon-jcs-111 { background-position: -160px -256px; }
.ui-icon-jcs-112 { background-position: -176px -256px; }
.ui-icon-jcs-113 { background-position: -192px -256px; }
.ui-icon-jcs-114 { background-position: -208px -256px; }
.ui-icon-jcs-115 { background-position: -224px -256px; }
.ui-icon-jcs-116 { background-position: -240px -256px; }
                                                        
.ui-icon-jcs-201 { background-position:    0   -224px; }
.ui-icon-jcs-202 { background-position:  -16px -224px; }
.ui-icon-jcs-203 { background-position:  -32px -224px; }
.ui-icon-jcs-204 { background-position:  -48px -224px; }
.ui-icon-jcs-205 { background-position:  -64px -224px; }
.ui-icon-jcs-206 { background-position:  -80px -224px; }
.ui-icon-jcs-207 { background-position:  -96px -224px; }
.ui-icon-jcs-208 { background-position: -112px -224px; }
.ui-icon-jcs-209 { background-position: -128px -224px; }
.ui-icon-jcs-210 { background-position: -144px -224px; }
.ui-icon-jcs-211 { background-position: -160px -224px; }
.ui-icon-jcs-212 { background-position: -176px -224px; }
.ui-icon-jcs-213 { background-position: -192px -224px; }
.ui-icon-jcs-214 { background-position: -208px -224px; }
.ui-icon-jcs-215 { background-position: -224px -224px; }
.ui-icon-jcs-216 { background-position: -240px -224px; }

.ui-icon-jcs-308 { background-position: -112px -208px; }
.ui-icon-jcs-309 { background-position: -128px -208px; }
.ui-icon-jcs-310 { background-position: -144px -208px; }
.ui-icon-jcs-311 { background-position: -160px -208px; }
.ui-icon-jcs-312 { background-position: -176px -208px; }
.ui-icon-jcs-313 { background-position: -192px -208px; }
.ui-icon-jcs-314 { background-position: -208px -208px; }
.ui-icon-jcs-315 { background-position: -224px -208px; }
.ui-icon-jcs-316 { background-position: -240px -208px; }

.ui-icon-jcs-401 { background-position:    0   -192px; }
.ui-icon-jcs-402 { background-position:  -16px -192px; }
.ui-icon-jcs-403 { background-position:  -32px -192px; }
.ui-icon-jcs-404 { background-position:  -48px -192px; }
.ui-icon-jcs-405 { background-position:  -64px -192px; }
.ui-icon-jcs-406 { background-position:  -80px -192px; }
.ui-icon-jcs-407 { background-position:  -96px -192px; }
.ui-icon-jcs-408 { background-position: -112px -192px; }
.ui-icon-jcs-409 { background-position: -128px -192px; }
.ui-icon-jcs-410 { background-position: -144px -192px; }
.ui-icon-jcs-411 { background-position: -160px -192px; }
.ui-icon-jcs-412 { background-position: -176px -192px; }
.ui-icon-jcs-413 { background-position: -192px -192px; }
.ui-icon-jcs-414 { background-position: -208px -192px; }

