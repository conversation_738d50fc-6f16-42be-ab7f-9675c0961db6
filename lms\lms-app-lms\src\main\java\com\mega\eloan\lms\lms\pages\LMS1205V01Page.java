/* 
 * LMS1205V01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.pages.LMSCommomPage;

import tw.com.jcs.auth.AuthType;

/**
 * <pre>
 * 授信簽報書編制中
 * </pre>
 * 
 * @since 2011/10/19
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/10/19,<PERSON>,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms1205v01")
public class LMS1205V01Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		// 設定文件狀態(交易代碼)
		setGridViewStatus(CreditDocStatusEnum.海外_編製中);
		// 加上Button
		// 主管跟經辦都會出現的按鈕

		// 只有主管出現的按鈕
		if (this.getAuth(AuthType.Accept)) {
			addToButtonPanel(model, LmsButtonEnum.View);
		}
		// 只有經辦出現的按鈕
		if (this.getAuth(AuthType.Modify)) {
			addToButtonPanel(model, LmsButtonEnum.Add, LmsButtonEnum.Modify, LmsButtonEnum.Delete,
					LmsButtonEnum.ChangeCaseFormat, LmsButtonEnum.ChangeVer);
		}

		renderJsI18N(LMS1205V01Page.class);
		renderJsI18N(LMSCommomPage.class);
	}// ;


}
