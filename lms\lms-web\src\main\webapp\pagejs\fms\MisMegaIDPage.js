CommonAPI.openQueryBox({
	isInSide:false, 
    fn: function(obj){
        var $box = $("div#showCustNameBox");
        if (!$box.length) {
            $("body").append("<div id='showCustNameBox' name='showCustNameBox' style='display:none'>");
            $("#showCustNameBox").append("<div id='showContent' />");
        }
        var $content = $("#showContent")
        var msg = "";
        for (var key in obj) {
            if (key != "lname" && obj[key]) {
                switch (key) {
                    case "custid":
                        msg = i18n.def['compID'];
                        break;
                    case "dupno":
                        msg = i18n.def['dupNo'];
                        break;
                    case "name":
                        msg = i18n.def['compName'];
                        break;
                    case "buscd":
                        msg = i18n.def['creatCust.buscd'];
                        break;
                }
                $content.append("<span id='" + key + "' >" + msg + ":" + obj[key] + "</span><br/>");
            }
        }
        $("#showCustNameBox").thickbox({ // 使用選取的內容進行彈窗
            title: "",
            width: 300,
            height: 170,
            align: "center",
            valign: "bottom",
            i18n: i18n.def,
            modal: true,
            buttons: {
                "sure": function(){
                    $.thickbox.close();
                    $("#showCustNameBox").remove();
                }
            }
        });
    }
});
