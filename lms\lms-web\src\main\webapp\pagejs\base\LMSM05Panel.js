/**
 * 呈主管簽章欄共用元件js
 */
var LMSM05Panel_js_obj = {
	i18n_dom : i18n['lmsm05'],	
	isStrStartsWith: function(data, input){
		return data.substring(0, input.length) === input;
	},
	numPerson_change : function(val){
		var $selectBossForm = $("#selectBossForm");
		var $newBossSpan= $("#newBossSpan");
		
		//清空原本的
		$newBossSpan.empty();
		
		var newdiv = "";
		if(val > 1){			
			for(var i=2,count=val+1; i<count ;i++){				
				newdiv+=("<div>"+this.i18n_dom['l120m01a.no']+ i +this.i18n_dom['l120m01a.site']+"&nbsp;"
				+ DOMPurify.sanitize($selectBossForm.find("#L120M01F_L3").val())
				+"&nbsp;&nbsp;&nbsp;<select name=boss"+i+" class='boss'/></div>");
			}
			
			$newBossSpan.append(newdiv);
			var copyOption = $("#mainBoss").html();
			$("[name^=boss]").html(copyOption);
		}
	},
	replace_sign_descLabel : function(){
		//泰行的簽章欄,隨 docKind, authLvl 變動
		var caseBrId = $("#caseBrId").val();
		var authLvl = $("#authLvl").val();
		var dockind = $("#docKind").val();
		
		var $selectBossForm = $("#selectBossForm");

        if(this.isStrStartsWith(caseBrId,"Y01")){
			$selectBossForm.find("#TH_UnitTypeR").show();
			$selectBossForm.find("#TH_UnitTypeP_gt_authLvl_1").hide();
			
			$selectBossForm.find("#L120M01F_L2").val(this.i18n_dom['l120m01a.TH_L2']);
			$selectBossForm.find("#L120M01F_L3").val(this.i18n_dom['l120m01a.TH_UnitTypeR_L3']);
			$selectBossForm.find("#L120M01F_L5").val(this.i18n_dom['l120m01a.TH_UnitTypeR_L5']);
		}else if(this.isStrStartsWith(caseBrId,"Y02") 
				|| this.isStrStartsWith(caseBrId,"Y03") 
				|| this.isStrStartsWith(caseBrId,"Y04")
				|| this.isStrStartsWith(caseBrId,"Y05")){
				
			$selectBossForm.find("#TH_UnitTypeR").hide();
			
			if(dockind=="1" && authLvl=="1"){
				$selectBossForm.find("#TH_UnitTypeP_gt_authLvl_1").hide();
			}else{
				$selectBossForm.find("#TH_UnitTypeP_gt_authLvl_1").show();	
			}
			
			$selectBossForm.find("#L120M01F_L2").val(this.i18n_dom['l120m01a.TH_L2']);
			if(dockind=="1" && authLvl=="1"){
				$selectBossForm.find("#L120M01F_L3").val(this.i18n_dom['l120m01a.TH_UnitTypeP_L3_dockind_1_authlvl_1']);
				$selectBossForm.find("#L120M01F_L5").val(this.i18n_dom['l120m01a.TH_UnitTypeP_L5_dockind_1_authlvl_1']);
			}else if(dockind=="1" && authLvl=="2"){
				$selectBossForm.find("#L120M01F_L3").val(this.i18n_dom['l120m01a.TH_UnitTypeP_L3_dockind_1_authlvl_2']);
				$selectBossForm.find("#L120M01F_L5").val(this.i18n_dom['l120m01a.TH_UnitTypeP_L5_dockind_1_authlvl_2']);
			}else if((dockind=="1" && authLvl=="3")||dockind=="2"){
				$selectBossForm.find("#L120M01F_L3").val(this.i18n_dom['l120m01a.TH_UnitTypeP_L3_dockind_1_authlvl_3_dockind_2']);
				$selectBossForm.find("#L120M01F_L5").val(this.i18n_dom['l120m01a.TH_UnitTypeP_L5_dockind_1_authlvl_3_dockind_2']);
			}
		}else{
			$selectBossForm.find("#TH_UnitTypeR").hide();
			$selectBossForm.find("#TH_UnitTypeP_gt_authLvl_1").hide();
		}
		$selectBossForm.find("#L120M01F_L3_cnt").val($("#L120M01F_L3").val()+this.i18n_dom['l120m01a.L3_cnt']);
		$selectBossForm.find("#L120M01F_L3_1st").val($("#L120M01F_L3").val());	
			
		
        //J-105-0131-001 Web e-Loan海外簽報書，呈主管時簽章欄AO欄位開放支行人員可供選擇。
		//當海外總行加拿大 unitType為Q者，且案件為編製中，才會顯示"選擇其他分行人員"按鈕
		//要放在最後面，要不然會影響上面的this物件	
        $.ajax({
	        handler: "lms1105formhandler",
			async: false ,
	        type: "POST",
	        dataType: "json",
	        data: {
	            formAction: "getSignCondition",
				brNo: userInfo.unitNo,
				mainId: responseJSON.mainId
	        }
		}).done(function(obj){			
				if(obj.showBtnChoiceOthBrNo == "Y"){
					$("#btnChoiceOthBrNo").show();
				}else{	
					$("#btnChoiceOthBrNo").hide();
				}
	    });

			
		
	},
	replace_LMSS01Panel_LMS1205S01Form_appr01:function(json){
		var row_branchType1 = $("#LMS1205S01Form").find("fieldset.hasAdmin1").eq(0);
		var row_branchType2 = $("#LMS1205S01Form").find("fieldset.hasAdmin1").eq(1);
		
		var row_branchType1_td_label_L1 = row_branchType1.find("table tr td b.text-red").eq(3);
		var row_branchType1_td_label_L2 = row_branchType1.find("table tr td b.text-red").eq(2);
		var row_branchType1_td_label_L3 = row_branchType1.find("table tr td b.text-red").eq(1);
		var row_branchType1_td_label_L5 = row_branchType1.find("table tr td b.text-red").eq(0);
		var caseBrId = $("#caseBrId").val();
		var authLvl = $("#authLvl").val();
		var dockind = $("#docKind").val();
		
		if(this.isStrStartsWith(caseBrId,"Y01")){
			/*
			 * 單位/授權主管：L5, 授信/覆核主管：L3, 帳戶管理員：L2, 經辦：L1 
			 */
			//替換 L1 的說明文字
			row_branchType1_td_label_L1.html(this.i18n_dom['l120m01a.TH_L1']+"：");
			
			//替換 L2 的說明文字
			row_branchType1_td_label_L2.html(this.i18n_dom['l120m01a.TH_L2']+"：");
			
			//替換 L3 的說明文字 
			row_branchType1_td_label_L3.html(this.i18n_dom['l120m01a.TH_UnitTypeR_L3']+"：");
			
			//替換 L5 的說明文字
			row_branchType1_td_label_L5.html(this.i18n_dom['l120m01a.TH_UnitTypeR_L5']+"：");
			
			//插入LA, 在 L3 之前
			row_branchType1_td_label_L3.parent().before("<td width='12%'><b class='text-red'>"
					+this.i18n_dom['l120m01a.TH_UnitTypeR_LA']+"："+"</b></td><td width='13%'>"+json.vice_manager+"</td>");
			
			//原本 8 個 td,加入 LA 之後, 共10個。調整寬度
			row_branchType1.find("table tr td").each(function( index ) {
				$(this).css("width", "0%");
				if(index%2==0){
					//label
				}else{
					//value
					$(this).css("width", "12%");
				}
				});
		}else if(this.isStrStartsWith(caseBrId,"Y02") 
				|| this.isStrStartsWith(caseBrId,"Y03") 
				|| this.isStrStartsWith(caseBrId,"Y04")
				|| this.isStrStartsWith(caseBrId,"Y05")){
			if(dockind=="1" && authLvl=="1"){
				//do nothing
			}else{				
				//替換「總行」
				row_branchType2.find("table tr td").eq(0).html("<b class='text-red'>"+this.i18n_dom['l120m01a.TH_UnitTypeP_L5_Y01']+"："+"</b>");
				row_branchType2.find("table tr td").eq(1).html(json._manager);
				
				//之後用此欄位, 判定是否有總行資料，若有則顯示外框，無則隱藏
				json.hasAdmin = true;
			}
			//替換 L1 的說明文字
			row_branchType1_td_label_L1.html(this.i18n_dom['l120m01a.TH_L1']+"：");
			
			//替換 L2 的說明文字
			row_branchType1_td_label_L2.html(this.i18n_dom['l120m01a.TH_L2']+"：");

			//替換「分行」
			if(dockind=="1" && authLvl=="1"){
				row_branchType1_td_label_L3.html(this.i18n_dom['l120m01a.TH_UnitTypeP_L3_dockind_1_authlvl_1']+"：");
				row_branchType1_td_label_L5.html(this.i18n_dom['l120m01a.TH_UnitTypeP_L5_dockind_1_authlvl_1']+"：");
			}else if(dockind=="1" && authLvl=="2"){
				row_branchType1_td_label_L3.html(this.i18n_dom['l120m01a.TH_UnitTypeP_L3_dockind_1_authlvl_2']+"：");
				row_branchType1_td_label_L5.html(this.i18n_dom['l120m01a.TH_UnitTypeP_L5_dockind_1_authlvl_2']+"：");
			}else if((dockind=="1" && authLvl=="3")||dockind=="2"){
				row_branchType1_td_label_L3.html(this.i18n_dom['l120m01a.TH_UnitTypeP_L3_dockind_1_authlvl_3_dockind_2']+"：");
				row_branchType1_td_label_L5.html(this.i18n_dom['l120m01a.TH_UnitTypeP_L5_dockind_1_authlvl_3_dockind_2']+"：");
			}
		} else if(json.caseBrCountryType=="AU"){
			// doNothing
		} else {
			//走CA(加拿大流程的)，增加總行覆核相關的簽章欄(之前的版本只有覆核和經辦，這次增加單位主管，授信主管的欄位顯示)
			$("#headReAoName").text(this.i18n_dom['l120m01a.aoPerson']+"：");
			$("#headReCheckName").text(this.i18n_dom['l120m01a.headReCheckName']+"：");
			row_branchType2.find("table tr td").eq(0).html("<b class='text-red'>"+this.i18n_dom['l120m01a.viceManagerName']+"：</b>");
		}
		
	}
	
	
	
	
	
};
