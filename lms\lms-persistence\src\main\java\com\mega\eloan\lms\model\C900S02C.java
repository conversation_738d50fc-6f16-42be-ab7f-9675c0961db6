
 
package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;

/** 消金新做案件(當月、當年度累計)統計表 **/
@Entity
@Table(name="C900S02C", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class C900S02C implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(unique = true, nullable = false, length = 32, columnDefinition = "CHAR(32)")
	private String oid;
	
	/** 額度 **/
	@Column(name="DATA_YM", length=7, columnDefinition="CHAR(7)")
	private String data_ym;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;
	
	/** 分行 **/
	@Column(name = "CASEBRID", length = 3, columnDefinition = "CHAR(3)")
	private String caseBrId;
	
	/** 營運中心 **/
	@Column(name = "BRNGROUP", length = 3, columnDefinition = "CHAR(3)")
	private String brnGroup;
	
	/** 簽案日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "CASEDATE", columnDefinition = "DATE")
	private Date caseDate;
	
	/** 核准日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "ENDDATE", columnDefinition = "DATE")
	private Date endDate;
	
	/** 分組註記 **/
	@Column(name = "FLAG", length = 1, columnDefinition = "CHAR(1)")
	private String flag;
	
	/** 金額 **/
	@Column(name = "CASEAMT", columnDefinition = "DECIMAL(15,0)")
	private BigDecimal caseAmt;

	
	public String getOid() {
		return oid;
	}

	public void setOid(String oid) {
		this.oid = oid;
	}

	public String getData_ym() {
		return data_ym;
	}

	public void setData_ym(String data_ym) {
		this.data_ym = data_ym;
	}

	public String getMainId() {
		return mainId;
	}

	public void setMainId(String mainId) {
		this.mainId = mainId;
	}

	public String getCaseBrId() {
		return caseBrId;
	}

	public void setCaseBrId(String caseBrId) {
		this.caseBrId = caseBrId;
	}

	public String getBrnGroup() {
		return brnGroup;
	}

	public void setBrnGroup(String brnGroup) {
		this.brnGroup = brnGroup;
	}

	public Date getCaseDate() {
		return caseDate;
	}

	public void setCaseDate(Date caseDate) {
		this.caseDate = caseDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public String getFlag() {
		return flag;
	}

	public void setFlag(String flag) {
		this.flag = flag;
	}

	public BigDecimal getCaseAmt() {
		return caseAmt;
	}

	public void setCaseAmt(BigDecimal caseAmt) {
		this.caseAmt = caseAmt;
	}
}
