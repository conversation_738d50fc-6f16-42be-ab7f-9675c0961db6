/*
 * ADDateTimeFormatter.java
 *
 * Copyright (c) 2009-2011 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
 */
package tw.com.iisi.cap.formatter;

import java.text.SimpleDateFormat;
import java.util.Calendar;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapFormatException;

/**
 * <pre>
 * 西元年日期時間Format (yyyy-MM-dd HH:mm:ss)
 * </pre>
 * 
 * @since 2010/7/27
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2010/7/27,iristu,new
 *          <li>2011/8/02,sunkist,update {@link ADDateTimeFormatter#reformat(Object)} for Calendar.
 *          </ul>
 */
@SuppressWarnings("serial")
public class ADDateTimeFormatter implements IFormatter {

    /**
     * 時間格式
     */
    String DEF_PATTERN = "yyyy-MM-dd HH:mm:ss";

    /**
     * 格式
     */
    private String pattern;

    /**
     * 設置時間預設格式 <br>
     * {@code "yyyy-MM-dd HH:mm:ss"}
     */
    public ADDateTimeFormatter() {
        this.pattern = DEF_PATTERN;
    }

    /**
     * 設置時間格式
     * 
     * @param pattern
     */
    public ADDateTimeFormatter(String pattern) {
        this.pattern = pattern;
    }

    /*
     * 依照設置格式化時間
     * 
     * @see tw.com.iisi.cap.formatter.IFormatter#reformat(java.lang.Object)
     */
    @Override
    @SuppressWarnings("unchecked")
    public String reformat(Object in) throws CapFormatException {
        SimpleDateFormat _df = new SimpleDateFormat(pattern);
        if (in == null) {
            return CapConstants.EMPTY_STRING;
        }
        if (in instanceof Calendar) {
            in = ((Calendar) in).getTime();
        } else if (in instanceof String) {
            return (String) in;
        }
        return _df.format(in);
    }

}
