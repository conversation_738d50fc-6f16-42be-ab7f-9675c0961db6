<html xmlns:th="http://www.thymeleaf.org">
	<th:block th:fragment="fgF">
		<button id="btnFilter">
			<span class="ui-icon ui-icon-jcs-103"></span>
			<th:block th:text="#{button.filter}">篩選</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgA">
		<button id="btnAdd">
			<span class="ui-icon ui-icon-jcs-07"></span>
			<th:block th:text="#{button.add}">新增</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgM">
		<button id="btnModify">
			<span class="ui-icon ui-icon-jcs-101"></span>
			<th:block th:text="#{button.modify}">修改</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgD">
		<button id="btnDelete">
			<span class="ui-icon ui-icon-jcs-09"></span>
			<th:block th:text="#{button.delete}">刪除</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgV">
		<button id="btnView">
			<span class="ui-icon ui-icon-jcs-12"></span>
			<th:block th:text="#{button.view}">調閱</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgP">
		<button id="btnPrint">
			<span class="ui-icon ui-icon-jcs-03"></span>
			<th:block th:text="#{button.print}">預覽列印</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgC">
		<button id="btnCopy">
			<span class="ui-icon ui-icon-jcs-08"></span>
			<th:block th:text="#{button.copy}">複製</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgS">
		<button id="btnDeliver">
			<span class="ui-icon ui-icon-arrowreturnthick-1-e"></span>
			<th:block th:text="#{button.deliver}">傳送</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgPI">
		<button id="btnPullin">
			<span class="ui-icon ui-icon-jcs-14"></span>
			<th:block th:text="#{button.pullin}">引進</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgCCF">
		<button id="btnChangeCaseFormat">
			<span class="ui-icon ui-icon-jcs-15"></span>
			<th:block th:text="#{button.changecaseformat}">案件格式變更</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgCallCenterSendCreditCase">
		<button id="btnCallCenterSendCreditCase">
			<th:block th:text="#{button.CallCenterSendCreditCase}">傳送信貸案件</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgCM">
		<button id="btnConfirm">
			<span class="ui-icon ui-icon-jcs-116"></span>
			<th:block th:text="#{button.confirm}">確認</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgRC">
		<button id="btnRecover">
			<span class="ui-icon ui-icon-jcs-115"></span>
			<th:block th:text="#{button.Recover}">復原</th:block>
		</button>
	</th:block>
	<th:block th:fragment="btnUnlock">
		<button id="btnUnlock">
			<span class="ui-icon ui-icon-jcs-115"></span>
			<th:block th:text="#{button.unlock}">解除鎖定</th:block>
		</button>
	</th:block>
	
	<!-- 以下由 原本Lms CreditButtonPanel 抄來 -->
	
	<th:block th:fragment="fgUF">
	    <button id="btnUseFirstTable" class="only-ex-permission">
	    	<span class="ui-icon ui-icon-bookmark"></span><th:block th:text="#{button.UseFirstTable}"><!--先行動用待辦控制表--></th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgL">
	    <button id="btnLogeIN" class="only-ex-permission">
	    	<span class="ui-icon ui-icon-jcs-209"></span><th:block th:text="#{button.login}"><!--登錄--></th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgDF">
	    <button id="btnDataFix">
	    	<span class="ui-icon ui-icon-jcs-316"></span><th:block th:text="#{button.DataFix}"><!--資料修正--></th:block>
		</button>
	</th:block>	
	<th:block th:fragment="fgCR">
	    <button id="btnCreate">
	    	<span class="ui-icon ui-icon-jcs-15"></span><th:block th:text="#{button.create}">產生</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgCREXL">
	    <button id="btnCreateExl">
	    	<span class="ui-icon ui-icon-jcs-15"></span><th:block th:text="#{button.ProduceExcel}">產生Excel</th:block>
		</button>
	</th:block>	
	<th:block th:fragment="fgMCL">
	    <button id="btnCaseLvl">
	    	<span class="ui-icon ui-icon-jcs-101"></span><th:block th:text="#{button.modifyCaseLvl}">修改審核層級</th:block>
		</button>
	</th:block>	
	<th:block th:fragment="fgSE">
	    <button id="btnSearch">
	    	<span class="ui-icon ui-icon-jcs-102"></span><th:block th:text="#{button.search}">查詢</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgCH">
	    <button id="btnChange">
	    	<span class="ui-icon ui-icon-jcs-16"></span><th:block th:text="#{button.Change}">條件變更/續約</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgTS">
	    <button id="btnTableSend">
	    	<span class="ui-icon ui-icon-jcs-06"></span><th:block th:text="#{button.TableSend}">額度明細表傳送聯行</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgPB">
	    <button id="btnPrintBook">
	    	<span class="ui-icon ui-icon-jcs-03"></span><th:block th:text="#{button.PrintBook}">列印核貸通知書</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgPN">
	    <button id="btnPrintNote">
	    	<th:block th:text="#{button.PrintNote}">貸放前照會借款人作業檢核表</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgCC">
	    <button id="btnCaseCopy">
	    	<span class="ui-icon ui-icon-jcs-08"></span><th:block th:text="#{button.CaseCopy}">案件複製</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgL1">
	    <button id="btnLogin1">
	    	<span class="ui-icon ui-icon-jcs-316"></span><th:block th:text="#{button.login1}">登錄授審會會期</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgL2">
	    <button id="btnLogin2">
	    	<span class="ui-icon ui-icon-jcs-316"></span><th:block th:text="#{button.login2}">登錄催收會會期</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgL3">
	    <button id="btnLogin3">
	    	<span class="ui-icon ui-icon-jcs-316"></span><th:block th:text="#{button.login3}">登錄常董會會期</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgL4">
	    <button id="btnLogin4">
	    	<span class="ui-icon ui-icon-jcs-316"></span><th:block th:text="#{button.login4}">登錄審計委員會會期</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgCD1">
	    <button id="btnCreDoc1">
	    	<span class="ui-icon ui-icon-jcs-15"></span><th:block th:text="#{button.creDoc1}">常董稿</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgCCF1">
	    <button id="btnChangeCaseFormat1">
	    	<span class="ui-icon ui-icon-jcs-316"></span><th:block th:text="#{button.changecaseformat1}">授權外變更授權內</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgCTC">
	    <button id="btnCaseToChange">
	    	<span class="ui-icon ui-icon-jcs-10"></span><th:block th:text="#{button.casetochange}">案件改分派</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgSC">
	    <button id="btnSendCase">
	    	<span class="ui-icon ui-icon-jcs-15"></span><th:block th:text="#{button.sendcase}">提會</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgBC">
	    <button id="btnBackCase">
	    	<span class="ui-icon ui-icon-jcs-210"></span><th:block th:text="#{button.backcase}">撤件/陳復</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgBU">
	    <button id="btnBackUnit">
	    	<span class="ui-icon ui-icon-jcs-210"></span><th:block th:text="#{button.backunit}">退回分行更正</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgOL">
	    <button id="btnOpenLms">
	    	<span class="ui-icon ui-icon-jcs-07"></span><th:block th:text="#{button.openlms}">開啟授信報案考核表</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgRBU">
	    <button id="btnReBackUnit">
	    	<span class="ui-icon ui-icon-jcs-15"></span><th:block th:text="#{button.rebackunit}">重新傳回分行</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgS3">
	    <button id="btnSend3">
	    	<span class="ui-icon ui-icon-jcs-02"></span><th:block th:text="#{button.send3}">呈主管放行</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgGC">
	    <button id="btnGetCase">
	    	<span class="ui-icon ui-icon-jcs-208"></span><th:block th:text="#{button.getCase}">收件</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgPA">
	    <button id="btnPrintArea">
	    	<span class="ui-icon ui-icon-jcs-03"></span><th:block th:text="#{button.PrintArea}">列印營運中心意見</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgBD">
	    <button id="btnBackDoc">
	    	<span class="ui-icon ui-icon-jcs-03"></span><th:block th:text="#{button.cancelCheck}">取消覆核</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgFC">
		<button type="button" id="btnFCheck" >
			<span class="ui-icon ui-icon-jcs-106"></span>
			<th:block th:text="#{button.check}">覆核</th:block>
		</button>
	</th:block>
	
	<th:block th:fragment="fgUP">
		<button type="button" id="btnUPCls" >
			<span class="ui-icon ui-icon-jcs-106" ></span>
			<th:block th:text="#{button.UPCls}">上傳房貸評分卡</th:block>
		</button>
	</th:block>
	
	<th:block th:fragment="fgCNTRCTL">
		<button type="button" id="btnCntrNoControl" >		
			<th:block th:text="#{button.CntrNoControl}">查詢額度控管設定</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgUPDID">
		<button type="button" id="btnUpdCustId" >		
			<th:block th:text="#{button.UpdCustId}">修改客戶統編</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgUPTRANSFERID">
		<button type="button" id="btnUpTransferId" >		
			<th:block th:text="#{button.UpTransferId}">上傳海外分行移轉名單</th:block>
		</button>
		<button type="button" id="btnUpCapital" >		
			上傳實收資本額
		</button>
	    <button type="button" id="btnUpSole">上傳獨資合夥</button>
	    <button type="button" id="btnGetLatestCol">最新簽報書的擔保品欄位</button>
		<!--J-109-0519_05097_B1001 Web e-Loan產生央行C方案借款人，且兌付振興三倍券達888張之名單-->
		<button type="button" id="btnUpTripleCoupon">上傳兌付振興三倍券名單</button>
		
		<!--J-110-0304_05097_B1003 Web e-Loan授信覆審配合RPA作業修改-->
		<button type="button" id="btnUpRetrialL224File">RPA上傳覆審L224附檔</button>
		
		<!--J-111-0423_05097_B1001 Web e-Loan企金授信就海外分行承做永續績效連結授信案(如附件)，於E-Loan-->
		<button type="button" id="btnUpEsgFile">上傳ESG永續績效連結授信案</button>	
	</th:block>
	
	<!--J-111-0443_05097_B1001 Web e-Loan企金授信開發授信BIS評估表-->
	<th:block th:fragment="fgUPBISPARAM">
		<!--J-111-0443_05097_B1001 Web e-Loan企金授信開發授信BIS評估表-->
		<button type="button" id="btnUpBisParam">上傳BIS評估表參數</button>
	</th:block>
	
	<!--20230719 Web e-Loan企金授信批次信用保證統計表總表 -->
	<th:block th:fragment="fgUPBATGUTFILE">
		<button type="button" id="btnUpBatGutFile">上傳批次信用保證統計表</button>
	</th:block>
	
	<th:block th:fragment="fgBACKAPPROVE">
	    <button id="btnBackApprove">
	    	<span class="ui-icon ui-icon-jcs-12"></span><th:block th:text="#{button.backApprove}">已覆核案件退回</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgPE">
	    <button id="btnProduceExcel">
	    	<span class="ui-icon ui-icon-jcs-110"></span><th:block th:text="#{button.ProduceExcel}">產生Excel</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgBATCHAPRVPROD69">
		<button id="btnBatchSelectAprvProd69">
	    	<span class="ui-icon ui-icon-document"></span><th:block th:text="#{button.btnBatchSelectAprvProd69}">勞工紓困案整批勾選</th:block>
		</button>
	    <button id="btnBatchAprvProd69">
	    	<span class="ui-icon ui-icon-document"></span><th:block th:text="#{button.btnBatchAprvProd69}">勞工紓困案整批覆核</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgSaveRebate">
		<button id="btnSaveRebate">
	    	<span class="ui-icon ui-icon-document"></span><th:block th:text="#{button.SaveRebate}">儲存引介獎金</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgChangeVer">
	    <button id="btnChangeVer">
	        <span class="ui-icon ui-icon-document"></span><th:block th:text="#{button.changeVer}">版本變更</th:block>
	    </button>
	</th:block>
	<th:block th:fragment="fgSmallBussCRpa">
	    <button id="btnSmallBussCRpa">
	        <span class="ui-icon ui-icon-document"></span>RPA專用(央C)
	    </button>
	</th:block>
	<th:block th:fragment="fgStartUpReliefRpa">
	    <button id="btnStartUpReliefRpa">
	        <span class="ui-icon ui-icon-document"></span>RPA專用(新創)
	    </button>
	</th:block>
	<th:block th:fragment="fgImportClsAreaPriceExcel">
		<button id="btnImportClsAreaPriceExcel">
			<span class="ui-icon ui-icon-document"></span>消金分組授權金額上傳
		</button>
	</th:block>
	<th:block th:fragment="fgCRCSCEXL">
	    <button id="btnCreateCSCExcel">
	    	<span class="ui-icon ui-icon-jcs-15"></span><th:block th:text="#{button.CreateCSCExcel}">產生中鋼消貸Excel</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgCaseToChangeSignEmp">
	    <button id="btnCaseToChangeSignEmp">
	    	<span class="ui-icon ui-icon-jcs-10"></span><th:block th:text="#{button.CaseToChangeSignEmp}">改派簽案人員</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgCaseReturn">
	    <button id="btnCaseReturn">
	    	<span class="ui-icon ui-icon-jcs-12"></span><th:block th:text="#{button.CaseReturn}">案件回復</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgApproveUnestablshExl">
	    <button id="btnApproveUnestablshExl">
	    	<span class="ui-icon ui-icon-jcs-110"></span><th:block th:text="#{button.approveUnestablshExl}">查詢在途授信額度</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgAutoAssignMaintenance">
	    <button id="btnAutoAssignMaintenance">
	    	<span class="ui-icon ui-icon-jcs-12"></span><th:block th:text="#{button.AutoAssignMaintenance}">派案分行設定</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgOneButtonAssignCase">
	    <button id="btnOneButtonAssignCase">
	    	<span class="ui-icon ui-icon-jcs-309"></span><th:block th:text="#{button.OneButtonAssignCase}"> 一鍵分案</th:block>
		</button>
	</th:block>
	
	<th:block th:fragment="fgSingleMaintain">
	    <button id="btnSingleMaintain">
	    	<span class="ui-icon ui-icon-jcs-13"></span><th:block th:text="#{button.SingleMaintain}"> 單筆維護</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgBatchMaintain">
	    <button id="btnBatchMaintain">
	    	<span class="ui-icon ui-icon-jcs-13"></span><th:block th:text="#{button.BatchMaintain}"> 批次維護</th:block>
		</button>
	</th:block>
	<!--J-113-0306 案件簽報書送呈區域中心審核後，若被退件，在「待補件/撤件」中之被撤件之案件，能否設計可以再撈到編製中重新簽報，以增進作業效率-->
	<th:block th:fragment="fgReBackApproveUnit">
	    <button id="btnReBackApproveUnit">
	    	<span class="ui-icon ui-icon-jcs-12"></span><th:block th:text="#{button.ReBackApproveUnit}">已覆核案件退回</th:block>
		</button>
	</th:block>
	<!--J-113-0490_07623_B1001 公司訪談紀錄表批次調閱-->
	<th:block th:fragment="fgImportExcel940CustId">
	    <button id="btnImportExcel940CustId">
	        <span class="ui-icon ui-icon-jcs-12"></span><th:block th:text="#{button.ImportExcel940CustId}">公司訪談紀錄表CustId上傳批次</th:block>
	    </button>
	</th:block>	
	
	<!-- 以下由 原本Lms LasButtonPanel 抄來 -->
	<!-- 以下圖案放的不一樣  先註解 -->
	<!--
	<th:block th:fragment="fgF">
	    <button id="btnFilter">
	    	<span class="ui-icon ui-icon-jcs-103"></span><th:block th:text="#{button.filter}">篩選</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgP">
	    <button id="btnPrint">
	    	<span class="ui-icon ui-icon-jcs-109"></span><th:block th:text="#{button.print}">預覽列印</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgS">
	    <button id="btnDeliver">
	    	<span class="ui-icon ui-icon-jcs-216"></span><th:block th:text="#{button.deliver}">傳送</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgPI">
	    <button id="btnPullin">
	    	<span class="ui-icon ui-icon-jcs-208"></span><th:block th:text="#{button.pullin}">引進</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgCCF">
	    <button id="ChangeCaseFormat">
	    	<span class="ui-icon ui-icon-jcs-316"></span><th:block th:text="#{button.ChangeCaseFormat}">案件格式變更</th:block>
		</button>
	</th:block>
	-->
	<!-- ID名稱重複 先註解 -->
	<!--
	<th:block th:fragment="fgPA">
	    <button id="btnPrintAll">
	    	<span class="ui-icon ui-icon-jcs-03"></span><th:block th:text="#{button.PrintAll}">整批列印</th:block>
		</button>
	</th:block>
 	-->
 	
	<th:block th:fragment="fgTRA">
	    <button id="btnToReviewAll">
	    	<span class="ui-icon ui-icon-jcs-02"></span><th:block th:text="#{button.ToReviewAll}">整批呈主管覆核</th:block>
		</button>
	</th:block>
	
	<th:block th:fragment="fgRA">
	    <button id="btnReviewAll">
	    	<span class="ui-icon ui-icon-jcs-107"></span><th:block th:text="#{button.ReviewAll}">整批覆核</th:block>
		</button>
	</th:block>
	
	<th:block th:fragment="fgPAA">
	    <button id="btnPrintAllR01">
	    	<span class="ui-icon ui-icon-jcs-03"></span><th:block th:text="#{button.PrintAllAudit}">整批列印工作底稿</th:block>
		</button>
	</th:block>
	
	<th:block th:fragment="fgPAB">
	    <button id="btnPrintAllR02">
	    	<span class="ui-icon ui-icon-jcs-03"></span><th:block th:text="#{button.PrintAllBill}">整批列印對帳單</th:block>
		</button>
	</th:block>
	
	<th:block th:fragment="fgACAC">
	    <button id="btnApprCreditAndCase">
	    	<span class="ui-icon ui-icon-jcs-03"></span><th:block th:text="#{button.ApprCreditAndCase}">整批列印對帳單</th:block>
		</button>
	</th:block>
	
	<th:block th:fragment="fgSAG">
	    <button id="btnSendAllToG">
	    	<span class="ui-icon ui-icon-jcs-112"></span><th:block th:text="#{button.SendAllToG}">整批傳送稽核室</th:block>
		</button>
	</th:block>
	
	<!-- 以下由 原本Lms ReportButtonPanel 抄來 -->
	<!-- ID名稱重複 或圖片不一樣 先註解 -->
	<!-- 
	<th:block th:fragment="fgS">
	    <button id="btnSearch">
	    	<span class="ui-icon ui-icon-jcs-102"></span><th:block th:text="#{button.search}">查詢</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgF">
	    <button id="btnFilter">
	    	<span class="ui-icon ui-icon-jcs-103"></span><th:block th:text="#{button.filter}">篩選</th:block>
		</button>
	</th:block>
	
	-->
	
	<th:block th:fragment="fgCR2">
	    <button id="btnCreateReport">
	    	<span class="ui-icon ui-icon-jcs-07"></span><th:block th:text="#{button.CreateReport}">產生報表</th:block>
		</button>
	</th:block>
	
	<th:block th:fragment="fgU">
	    <button id="btnUpload">
	    	<span class="ui-icon ui-icon-jcs-10"></span><th:block th:text="#{button.upload}">上傳</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgPIR">
	    <button id="btnPullinReport">
	    	<span class="ui-icon ui-icon-jcs-14"></span><th:block th:text="#{button.pullinReport}">引進當期資料</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgSR">
	    <button id="btnSummaryReport">
	    	<span class="ui-icon ui-icon-jcs-14"></span><th:block th:text="#{button.CreateReport}">產生報表</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgSR2">
	    <button id="btnSummaryReport2">
	    	<span class="ui-icon ui-icon-jcs-07"></span><th:block th:text="#{button.CreateReport}">產生報表</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgSDR">
	    <button id="btnSendDocTypeReport">
	    	<span class="ui-icon ui-icon-jcs-216"></span><th:block th:text="#{button.SendDocTypeReport}">傳送法金處</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgSHR">
	    <button id="btnSendHqTypeReport">
	    	<span class="ui-icon ui-icon-jcs-216"></span><th:block th:text="#{button.SendHqTypeReport}">傳送總處</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgLE">
	    <button id="btnLongError">
	    	<span class="ui-icon ui-icon-jcs-111"></span><th:block th:text="#{button.LongError}">取消核備</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgLVM">
	    <button id="btnLongViewMemo">
	    	<span class="ui-icon ui-icon-check"></span><th:block th:text="#{button.LongViewMemo}">登錄/調閱核備註記</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgRP">
	    <button id="btnReturnPage">
	    	<span class="ui-icon ui-icon-jcs-115"></span><th:block th:text="#{button.ReturnPage}">返回</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgChg">
	    <button id="btnChange">
	    	<span class="ui-icon ui-icon-jcs-108"></span><th:block th:text="#{button.ChangeRow}">修改已選取資料</th:block>
		</button>
	</th:block>
	
	<!-- 以下由 原本Lms RetrialButtonPanel 抄來 -->
	<!-- ID名稱重複 或圖片不一樣 先註解 -->
	<!-- 
	<th:block th:fragment="fgSR">
	    <button id="btnSendRetrialReport">
	    	<span class="ui-icon ui-icon-jcs-216"></span><th:block th:text="#{button.SendRetrialReport}">傳送分行覆審報告表</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgF">
	    <button id="btnFilter">
	    	<span class="ui-icon ui-icon-jcs-103"></span><th:block th:text="#{button.filter}">篩選</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgP">
	    <button id="btnPrint">
	    	<span class="ui-icon ui-icon-jcs-109"></span><th:block th:text="#{button.print}">預覽列印</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgS">
	    <button id="btnDeliver">
	    	<span class="ui-icon ui-icon-jcs-216"></span><th:block th:text="#{button.deliver}">傳送</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgCCF">
	    <button id="btnChangeCaseFormat">
	    	<span class="ui-icon ui-icon-jcs-316"></span><th:block th:text="#{button.changecaseformat}">案件格式變更</th:block>
		</button>
	</th:block>
	-->
	
	<th:block th:fragment="fgPL">
	    <button id="btnProduceList">
	    	<span class="ui-icon ui-icon-jcs-07"></span><th:block th:text="#{button.ProduceList}">產生名單</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgMT">
	    <button id="btnMaintain">
	    	<span class="ui-icon ui-icon-jcs-113"></span><th:block th:text="#{button.Maintain}">覆審控制檔維護</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgAS">
	    <button id="btnAllSend">
	    	<span class="ui-icon ui-icon-jcs-107"></span><th:block th:text="#{button.AllSend}">整批覆核</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgERD">
	    <button id="btnExceptRetrialDate">
	    	<span class="ui-icon ui-icon-jcs-101"></span><th:block th:text="#{button.ExceptRetrialDate}">修改預計覆審日</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgPP">
	    <button id="btnProducePaper">
	    	<span class="ui-icon ui-icon-jcs-07"></span><th:block th:text="#{button.ProducePaper}">覆審工作底稿</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgRTCOMPILING">
	    <button id="btnReturnToCompiling">
	    	<span class="ui-icon ui-icon-jcs-115"></span><th:block th:text="#{button.ReturnToCompiling}">退回編製中</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgSRR">
	    <button id="btnSendRetrialReport">
	    	<span class="ui-icon ui-icon-jcs-216"></span><th:block th:text="#{button.SendRetrialReport}">傳送分行覆審報告表</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgSDBTT">
	    <button id="btnSendBtt">
	    	<th:block th:text="#{button.SendBtt}">重新上傳名單到BTT</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgPET">
	    <button id="btnProduceEvaluateTbl">
	    	<th:block th:text="#{button.ProduceEvaluateTbl}">產生覆審考核表</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgPRB">
	    <button id="btnProduceRankingBoard">
	    	<span class="ui-icon ui-icon-jcs-07"></span><th:block th:text="#{button.ProduceRankingBoard}">產生排名表</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgSTCD">
	    <button id="btnSendToCtrlDept">
	    	<span class="ui-icon ui-icon-jcs-216"></span><th:block th:text="#{button.SendToCtrlDept}">傳送授管處</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgSDEXMUT">
	    <button id="btnSendToExamUnit">
	    	<th:block th:text="#{button.SendToExamUnit}">移受檢單位登錄</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgBTAPRV">
	    <button id="btnBatchApproved">
	    	<th:block th:text="#{button.BatchApproved}">整批核准</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgBTTSK">
	    <button id="btnBatchTask">
	    	<th:block th:text="#{button.BatchTask}">整批作業</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgR">
	    <button id="btnReturn">
	    	<span class="ui-icon ui-icon-jcs-115"></span><th:block th:text="#{button.return}">退回</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgIED">
	    <button id="btnInsertExcelData">
	    	<span class="ui-icon ui-icon-jcs-07"></span><th:block th:text="#{button.InsertExcelData}">產生企金戶新增/增額名單</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgIIE">
	    <button id="btnInsertInExcelData">
	    	<span class="ui-icon ui-icon-jcs-07"></span><th:block th:text="#{button.InsertInExcelData}">產生企金戶未列於覆審名單</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgIUE">
	    <button id="btnInsertUnExcelData">
	    	<span class="ui-icon ui-icon-jcs-07"></span><th:block th:text="#{button.InsertUnExcelData}">產生未於規定期限辦理覆審之企金戶名單</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgILE">
	    <button id="btnInsertLateExcelData">
	    	<span class="ui-icon ui-icon-jcs-07"></span><th:block th:text="#{button.InsertLateExcelData}">產生新作增額逾期檢核表 </th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgIRE">
	    <button id="btnInsertRecExcelData">
	    	<span class="ui-icon ui-icon-jcs-07"></span><th:block th:text="#{button.InsertRecExcelData}">產生辦理最近授信檢核表</th:block>
		</button>
	</th:block>
	<th:block th:fragment="fgURD0519">
	    <button id="btnURD0519">
	        <span class="ui-icon ui-icon-jcs-101"></span>一次性修改
	    </button>
	</th:block>
	
	<th:block th:fragment="btnEmpty">
	</th:block>
	<th:block th:fragment="fgQueryCustLoanRecord">
		<button id="btnQueryCustLoanRecord">
			<span class="ui-icon ui-icon-jcs-101"></span>
			<th:block th:text="#{button.QueryCustLoanRecord}">查詢客戶申貸紀錄</th:block>
		</button>
	</th:block>
</html>