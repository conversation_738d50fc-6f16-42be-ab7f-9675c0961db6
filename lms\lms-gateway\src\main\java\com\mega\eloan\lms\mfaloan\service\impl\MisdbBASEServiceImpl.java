package com.mega.eloan.lms.mfaloan.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.sql.Types;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.annotation.NonTransactional;
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.Arithmetic;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.jdbc.EloanColumnMapRowMapper;
import com.mega.eloan.common.utils.CustomerIdCheckUtil;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.mfaloan.bean.ELF431;
import com.mega.eloan.lms.mfaloan.bean.ELF447N;
import com.mega.eloan.lms.mfaloan.bean.ELF493;
import com.mega.eloan.lms.mfaloan.bean.ELF513;
import com.mega.eloan.lms.mfaloan.bean.ELF601;
import com.mega.eloan.lms.mfaloan.bean.ELF602;
import com.mega.eloan.lms.mfaloan.bean.LNF13E;
import com.mega.eloan.lms.mfaloan.bean.LNF916S;
import com.mega.eloan.lms.mfaloan.bean.LNF917S;
import com.mega.eloan.lms.mfaloan.bean.LNF919S;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

@Service
public class MisdbBASEServiceImpl extends AbstractMFAloanJdbc implements
		MisdbBASEService {

	public List<Map<String, Object>> findBySQLForList(String sql,
			String... params) {
		return this.getJdbc().queryForList(sql, params);
	}

	public Map<String, Object> findSYNBANK(String brNo) {
		return this.getJdbc().queryForMap("MISSynBank.selBrank2",
				new String[] { brNo });
	}

	public List<Map<String, Object>> findCustDataByCltype(String[] custIds) {
		String custIdParams = Util.genSqlParam(custIds);
		return this.getJdbc().queryForAllListByCustParam(
				"MISCUSTDATA.CLTYPENOT1", new String[] { custIdParams }, custIds);
	}

	public List<Map<String, Object>> findLNF022V12(String grpNo) {
		return this.getJdbc().queryForList("MISELGUPQTA.selLNF022V12",
				new String[] { grpNo });
	}

	public List<Map<String, Object>> findLNF022V13(String grpNo) {
		return this.getJdbc().queryForList("MISGRPDIFF.getLNF022V13",
				new String[] { grpNo });
	}

	public List<Map<String, Object>> findRate(String sCurr1, String sCurr2) {
		return this.getJdbc().queryForList("MISRATETBL.selRate",
				new String[] { sCurr1, sCurr2 });
	}

	@Override
	public List<Map<String, Object>> findMISSynBankBy99() {
		return this.getJdbc().queryForList("MISSynBank.selBrankBy99",
				new String[] {});
	}

	@Override
	public List<Map<String, Object>> findMISSynBank(String branchCode) {
		return this.getJdbc().queryForList("MISSynBank.selBrank",
				new String[] { branchCode });
	}

	@Override
	public List<Map<String, Object>> findMISELFBKSNOBank() {
		return this.getJdbc().queryForList("MISELFBKSNO.selBrank",
				new String[] {});
	}

	public Map<String, Object> findMISStaffSelAll(String custId) {
		return this.getJdbc().queryForMap("MISStaff.selAll",
				new Object[] { custId });
	}

	public List<Map<String, Object>> findEllngteeSelLngenm(String branch,
			String custId, String dupNo, String cntrno) {
		return this.getJdbc().queryForList("Ellngtee.SelLngenm",
				new Object[] { branch, custId, dupNo, cntrno });
	}

	@Override
	public Map<String, Object> findElchklst(String branch, String custId,
			String dupNo) {
		return this.getJdbc().queryForMap("ELCHKLST.selLrdate",
				new Object[] { branch, custId, dupNo });
	}

	@Override
	public List<Map<String, Object>> findEllnseek(String custId, String dupNo) {
		return this.getJdbc().queryForList("ELLNSEEK.selByCustId",
				new Object[] { custId, dupNo });
	}

	public List<Map<String, Object>> findCUSTDATA_selCustData(String custId,
			String dupNo) {
		return this.getJdbc().queryForList("CUSTDATA.selCustData",
				new Object[] { custId, dupNo });
	}

	public List<Map<String, Object>> findCUSTDATA_selMCustId(String custId,
			String dupNo) {
		return this.getJdbc().queryForList("CUSTDATA.selMCustId",
				new Object[] { custId, dupNo });
	}

	public List<Map<String, Object>> findCUSTDATA_selMCustId2(String custId) {
		return this.getJdbc().queryForList("CUSTDATA.selMCustId2",
				new Object[] { custId });
	}

	public List<Map<String, Object>> findELCUS21_selAddrr(String custId,
			String dupNo) {
		return this.getJdbc().queryForList("ELCUS21.selAddrr",
				new Object[] { custId, dupNo });
	}

	public List<Map<String, Object>> findELCUS28_selTelno(String custId,
			String dupNo) {
		return this.getJdbc().queryForList("ELCUS28.selTelno",
				new Object[] { custId, dupNo });
	}

	public List<Map<String, Object>> findELCUS23_selMpno(String custId,
			String dupNo) {
		return this.getJdbc().queryForList("ELCUS23.selMpno",
				new Object[] { custId, dupNo });
	}

	public List<Map<String, Object>> findELCUS22_selMailaddr(String custId,
			String dupNo) {
		return this.getJdbc().queryForList("ELCUS22.selMailaddr",
				new Object[] { custId, dupNo });
	}

	public List<Map<String, Object>> findLNF022_selL120s05d1(String allCustId,
			String custId, String dupNo, String custName) {
		return this.getJdbc().queryForListByCustParam(
				"LNF022.selL120s05d1",
				new Object[] { "'" + allCustId + "'", "'" + custId + "'",
						"'" + dupNo + "'", "'" + custName + "'" },
				new Object[] { custId, dupNo });
	}

	@Override
	public List<Map<String, Object>> findLNF07AByKey2(String key) {
		return this.getJdbc().queryAllForList("LNF07A.findByKey2",
				new Object[] { key.substring(0, 4) + "%" });
	}

	@Override
	public int findLNF197BycntrNoAndCustId(String cntrNo, String custId,
			String dupNo) {
		return this.getJdbc().queryForInt("LNF197.selByContractAndCustId",
				new Object[] { cntrNo, custId + dupNo });
	}

	@Override
	public int findMISLN20BycntrNoAndCustId(String cntrNo, String custId,
			String dupNo) {
		return this.getJdbc().queryForInt(
				"MISLN20.selByContractAndCustId",
				new Object[] { cntrNo,
						Util.addSpaceWithValue(custId, 10) + dupNo });
	}

	@Override
	public List<Map<String, Object>> findMISMOWTBL1_selMow(String custId,
			String dupNo) {
		return this.getJdbc().queryForList("MISMOWTBL1.selMow",
				new Object[] { custId, dupNo });
	}

	@Override
	public String findICBCBRByBrId(String brId) {

		Map<String, Object> data = this.getJdbc().queryForMap(
				"MISICBCBR.selCBCBRNO", new Object[] { brId });
		if (data != null && !data.isEmpty()) {
			String chBrid = (String) data.get("CBCBRNO");
			return Util.isEmpty(chBrid) ? brId : chBrid.substring(0, 3);
		}
		return brId;
	}

	@Override
	public int countByLNF262_KINDAndLNF262_LOAN_NO(String kind, String loanNo) {
		return this.getJdbc().queryForInt(
				"LNLNF262.countByLNF262_KINDAndLNF262_LOAN_NO",
				new Object[] { kind, loanNo });
	}

	@Override
	public List<Map<String, Object>> findMisEldpfByCustid(String brno,
			String custId, String dupNo) {
		return this.getJdbc().queryForList("MIS.ELDPF.findByCustid",
				new Object[] { custId, dupNo, brno });
	}

	@Override
	public List<Map<String, Object>> findMisEldpfByCustid(String custId,
			String dupNo) {
		return this.getJdbc().queryForList("MIS.ELDPF.findByCustid2",
				new Object[] { custId, dupNo });
	}

	@Override
	public List<Map<String, Object>> findMisEldpfByAccount(String brno,
			String apcd, String seqno) {
		return this.getJdbc().queryForList("MIS.ELDPF.findByAccount",
				new Object[] { brno, apcd, seqno });
	}

	@Override
	public List<Map<String, Object>> findMisEldpfByAccount(String[] custIds,
			String brno, String apcd, String seqno) {
		StringBuilder sb = new StringBuilder("'");
		for (String custId : custIds) {
			sb.append(sb.length() >= 1 ? "','" : "").append(custId);
		}
		sb.append("'");
		return this.getJdbc().queryForListByCustParam(
				"MIS.ELDPF.findByAccount2", new Object[] { sb.toString() },
				new Object[] { brno, apcd, seqno });
	}

	@Override
	public Map<String, Object> searchRejectRecord(String custId, String dupNo) {
		return this.getJdbc().queryForMap("MIS.LNUNID.SearchRejectRecord",
				new Object[] { custId, dupNo });
	}

	@Override
	public List<Map<String, Object>> findLnf150ByCustid(String custId) {
		return this.getJdbc().queryForList("LN.LNF150.getELLNF",
				new Object[] { custId });
	}

	@Override
	public List<Map<String, Object>> findLnf022LoanbalByCustid(String custId) {
		return this.getJdbc().queryForList("LN.LNF022.GetLoanbal",
				new Object[] { custId });
	}

	@Override
	public Map<String, Object> getBankNetValue() {
		return this.getJdbc().queryForMap("MIS.ELRELIMT.getBankNetValue",
				new Object[] {});
	}

	@Override
	public List<Map<String, Object>> selStock(String custId) {
		return this.getJdbc().queryForList("MISSTKDATA.selStock",
				new String[] { custId });
	}

	@Override
	public List<Map<String, Object>> selStock2(String stockNum) {
		return this.getJdbc().queryForList("MISSTKDATA.selStock2",
				new String[] { stockNum });
	}

	@Override
	public Map<String, Object> selLastDate(String allCustId) {
		return getJdbc().queryForMap("MISLN20.selLastDate",
				new String[] { allCustId });
	}

	@Override
	public List<Map<String, Object>> selUnNormalClass() {
		return this.getJdbc().queryForList("LNF07A.selUnNormalClass",
				new String[] {});
	}

	@Override
	public List<Map<String, Object>> selUnNormalBC(String type) {
		return this.getJdbc().queryForList("LNF07A.selUnNormalBC",
				new String[] { type });
	}

	@Override
	public Map<String, Object> selUnNormalBCa(String type, String seqNo) {
		return this.getJdbc().queryForMap("LNF07A.selUnNormalBCa",
				new String[] { type, seqNo });
	}

	@Override
	public List<Map<String, Object>> selUnNormal1() {
		return this.getJdbc().queryForList("LNF07A.selUnNormal1",
				new String[] {});
	}

	@Override
	public Map<String, Object> selUnNormal1a(String seqNo) {
		return this.getJdbc().queryForMap("LNF07A.selUnNormal1a",
				new String[] { seqNo });
	}

	@Override
	public List<Map<String, Object>> selUnNormal2() {
		return this.getJdbc().queryForList("LNF07A.selUnNormal2",
				new String[] {});
	}

	@Override
	public Map<String, Object> selUnNormal2a(String seqNo) {
		return this.getJdbc().queryForMap("LNF07A.selUnNormal2a",
				new String[] { seqNo });
	}

	@Override
	public List<Map<String, Object>> selUnNormal3() {
		return this.getJdbc().queryForList("LNF07A.selUnNormal3",
				new String[] {});
	}

	@Override
	public Map<String, Object> selUnNormal3a(String seqNo) {
		return this.getJdbc().queryForMap("LNF07A.selUnNormal3a",
				new String[] { seqNo });
	}

	@Override
	public void delUnNormal1(String mainId) {
		this.getJdbc().update("LNFE0854.delUnNormal1", new Object[] { mainId });
	}

	/**
	 * J-109-0291_05097_B1001 簡化小規模營業人異常通報簽報流程
	 */
	@Override
	public void insertUnNormal1(String custId, String dupNo, String brno,
			String mainId, String caseNo, String approveDate,
			String promiseCase, int promiseRatio, String firstDate,
			String lastDate, long totRiskAmt, long lostAmt, String isClosed,
			String closeDate, String closeCaseNo, String closeMainId,
			String mdClass, String isMajor, String majorPt2, String majorPt3,
			String majorPt4, String caseType) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		this.getJdbc().update(
				"LNFE0854.uploadUnNormal1",
				new Object[] { custId, dupNo, brno, mainId, caseNo,
						approveDate, promiseCase, promiseRatio, firstDate,
						lastDate, totRiskAmt, lostAmt, user.getUserId(),
						isClosed, closeDate, closeCaseNo, closeMainId, mdClass,
						isMajor, majorPt2, majorPt3, majorPt4, caseType });

	}

	@Override
	public void update0854UnNormal5(String isClosed, String closeDate,
			String closeCaseNo, String closeMainId, String mainId,
			String custId, String dupNo, String caseBrid) {
		this.getJdbc().update(
				"LNFE0854.uploadUnNormal5",
				new Object[] { isClosed, closeDate, closeCaseNo, closeMainId,
						custId, dupNo, caseBrid, mainId });

	}

	@Override
	public void update0854UnNormal5a(String isClosed, String closeDate,
			String closeCaseNo, String closeMainId, String custId,
			String dupNo, String caseBrid, boolean canCloseAllBranch) {
		// J-110-0398_05097_B1001 Web e-Loan配合異常通報須知修訂, 修改異常通報之解除方式
		List<Object> params = new ArrayList<Object>();
		params.add(isClosed);
		params.add(closeDate);
		params.add(closeCaseNo);
		params.add(closeMainId);
		params.add(custId);
		params.add(dupNo);

		String updateMdBrno = "";
		if (canCloseAllBranch) {
			updateMdBrno = " "; // 全行未結案都一起結案
		} else {
			updateMdBrno = " AND LNFE0854_MDBRNO = ? "; // 只結案該分行
			params.add(caseBrid);
		}

		this.getJdbc().updateByCustParam(
				"LNFE0854.uploadUnNormal5a",
				new Object[] { updateMdBrno },
				params.toArray(new Object[0]));

	}

	@Override
	public void delUnNormal2(String mainId) {
		this.getJdbc().update("LNFE0855.delUnNormal2", new Object[] { mainId });
	}

	@Override
	public void insertUnNormal2(List<Object[]> DataList) {
		this.getJdbc().batchUpdate(
				"LNFE0855.uploadUnNormal2",
				new int[] { Types.CHAR, Types.CHAR, Types.CHAR, Types.CHAR,
						Types.CHAR, Types.CHAR, Types.VARCHAR }, DataList);

	}

	@Override
	public List<Map<String, Object>> selectUnNormal3(String mainId) {
		return this.getJdbc().queryForList("LNFE0856.selUnNormal3",
				new String[] { mainId });
	}

	@Override
	public void delUnNormal3(String mainId) {
		this.getJdbc().update("LNFE0856.delUnNormal3", new Object[] { mainId });
	}

	@Override
	public void insertUnNormal3(String custId, String dupNo, String brno,
			String mainId, String caseNo, String seqNo, String ndFlag,
			Date runDate, String setCurr, long setAmt, String doMemo,
			int headMonth, String stopFg, String stopCaseNo, String stopMainId,
			String docDscr) {
		String ndCode1 = null;
		String ndCode2 = null;
		if (Util.isNotEmpty(seqNo)) {
			ndCode1 = seqNo.substring(0, 1);
			ndCode2 = seqNo.substring(1);
		}
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		this.getJdbc().update(
				"LNFE0856.uploadUnNormal3",
				new Object[] { custId, dupNo, brno, mainId, caseNo, ndCode1,
						ndCode2, ndFlag,
						Util.isEmpty(runDate) ? "0001-01-01" : runDate,
						setCurr, setAmt, doMemo, user.getUserId(), headMonth,
						stopFg, stopCaseNo, stopMainId, docDscr });

	}

	@Override
	public List<Map<String, Object>> selLnfe0851Flag(Map<String, String[]> map) {
//		StringBuilder condition = new StringBuilder();
//		condition.setLength(0);
		List<String> condition =new ArrayList<String>();
		List<Object> params = new ArrayList<Object>();
		// 依照不同事件大類產生條件
		if (map.containsKey("A")) {
			String sql = " ( LNF07A_KEY_4 = ''A'' AND LNF07A_KEY_2 IN ({0}) )";
			String[] key2Val = map.get("A");
			String sqlParamString = MessageFormat.format(sql, Util.genSqlParam(key2Val));
			condition.add(sqlParamString);
			params.addAll(Arrays.asList(key2Val));

			// 一般事項
//			condition.append((condition.length() > 0) ? "OR (" : "(")
//					.append("LNF07A_KEY_4 = 'A' AND LNF07A_KEY_2 IN (")
//					.append(map.get("A")).append("))");
		}
		if (map.containsKey("B")) {
			String sql = " ( LNF07A_KEY_4 = ''B'' AND LNF07A_KEY_2 IN ({0}) )";
			String[] key2Val = map.get("B");
			String sqlParamString = MessageFormat.format(sql, Util.genSqlParam(key2Val));
			condition.add(sqlParamString);
			params.addAll(Arrays.asList(key2Val));
			// 解除異常通報
//			condition.append((condition.length() > 0) ? "OR (" : "(")
//					.append("LNF07A_KEY_4 = 'B' AND LNF07A_KEY_2 IN (")
//					.append(map.get("B")).append("))");
		}
		if (map.containsKey("C")) {
			String sql = " ( LNF07A_KEY_4 = ''C'' AND LNF07A_KEY_2 IN ({0}) )";
			String[] key2Val = map.get("C");
			String sqlParamString = MessageFormat.format(sql, Util.genSqlParam(key2Val));
			condition.add(sqlParamString);
			params.addAll(Arrays.asList(key2Val));
			// 停權
//			condition.append((condition.length() > 0) ? "OR (" : "(")
//					.append("LNF07A_KEY_4 = 'C' AND LNF07A_KEY_2 IN (")
//					.append(map.get("C")).append("))");
		}
		// if ("A".equals(Util.trim(bigKind))) {
		// // 一般事項
		// condition = "LNF07A_KEY_4 = 'A' AND LNF07A_KEY_2 IN ('00','01')";
		// } else if ("B".equals(Util.trim(bigKind))) {
		// // 解除異常通報
		// condition = "LNF07A_KEY_4 = 'B' AND LNF07A_KEY_2 IN ('01','03')";
		// } else if ("C".equals(Util.trim(bigKind))) {
		// // 停權
		// condition = "LNF07A_KEY_4 = 'C' AND LNF07A_KEY_2 IN ('01','02')";
		// }
		return this.getJdbc().queryForListByCustParam(
				"LNF07A.selLnfe0851Flag",
				new Object[] { StringUtils.join(condition, " OR ") }, params.toArray(new Object[0]));
	}

	public void updateUnNormal4(String mainId, String custId, String dupNo,
			String caseBrid, String caseNo, boolean canCloseAllBranch) {
		// J-110-0398_05097_B1001 Web e-Loan配合異常通報須知修訂, 修改異常通報之解除方式
		String updateMdBrno = "";
		if (canCloseAllBranch) {
			updateMdBrno = " "; // 全行未結案都一起結案
		} else {
			//updateMdBrno = " AND LNFE0851_MDBRNO= '" + caseBrid + "' "; // 只結案該分行
			updateMdBrno = " AND LNFE0851_MDBRNO=  ? "; // 只結案該分行
		}

		getJdbc().updateByCustParam(
				"LNFE0851.updateUnNormal4",
				new Object[] { updateMdBrno },
				canCloseAllBranch ?
						new Object[] { "Y", CapDate.parseSQLDate(new Date()), caseNo, mainId, custId, dupNo }:
						new Object[] { "Y", CapDate.parseSQLDate(new Date()), caseNo, mainId, custId, dupNo, caseBrid });

	}

	@Override
	public void update0851UnNormal5(String isClosed, String closeDate,
			String closeCaseNo, String closeMainId, String mainId,
			String custId, String dupNo, String caseBrid) {
		this.getJdbc().update(
				"LNFE0851.uploadUnNormal5",
				new Object[] { isClosed, closeDate, closeCaseNo, closeMainId,
						custId, dupNo, caseBrid, mainId });

	}

	@Override
	public boolean selUnNormal4(String custId, String dupNo, String brno) {
		List<Map<String, Object>> mapList = this.getJdbc().queryForList(
				"LNFE0851.selUnNormal4", new String[] { custId, dupNo, brno });
		if (mapList.isEmpty()) {
			return false;
		} else {
			return true;
		}
	}

	@Override
	public List<Map<String, Object>> selUnNormal4a(String custId, String dupNo) {

		return this.getJdbc().queryForList("LNFE0851.selUnNormal4a",
				new String[] { custId, dupNo });
	}

	@Override
	public void insertUnNormal4(String custId, String dupNo, String custName,
			String brno, long lostAmt, String collStat, String process,
			String sameIdea, Date createTime, String mdClass, String ndCode) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		this.getJdbc()
				.update("LNFE0851.uploadUnNormal4",
						new Object[] {
								custId,
								dupNo,
								custName,
								brno,
								lostAmt,
								// 因異常通報擴大欄位長度造成上傳LNFE0851資料過長，
								// 建霖說上傳 LNFE0851時全部截掉。 Miller added at
								// 2013/02/26
								Util.trimSizeInOS390(collStat, 102),
								Util.trimSizeInOS390(process, 202),
								Util.trimSizeInOS390(sameIdea, 202),
								CapDate.parseSQLDate(new Date()),
								user.getUserId().substring(1),
								"",
								"",
								"",
								"",
								createTime,
								(mdClass.length() > 0 && mdClass.charAt(0) == '0') ? mdClass
										.substring(1) : mdClass, "", null, "",
								"", ndCode });

	}

	/**
	 * J-109-0341_05097_B1001 Web e-Loan異常通報增加授信戶信評大幅貶落事項並傳送國內海外帳務系統
	 * 
	 * @param custId
	 * @param dupNo
	 * @param caseBrid
	 * @param ndCode
	 */
	@Override
	public void update0851UnNormaData(String custId, String dupNo,
			String caseBrid, String ndCode) {
		this.getJdbc().update("LNFE0851.updateUnNormaData",
				new Object[] { ndCode, custId, dupNo, caseBrid });

	}

	@Override
	public List<Map<String, Object>> selExcel(String custId, String dupNo,
			String brno) {
		return this.getJdbc().queryForList("LNFE0854.selExcel",
				new String[] { custId, dupNo, brno });
	}

	@Override
	public Map<String, Object> elf510_selStop(String brno, String docType) {
		return this.getJdbc().queryForMap("ELF510.selStop",
				new Object[] { brno, docType });
	}

	@Override
	public Map<String, Object> selAO(String brno, String custId) {
		return this.getJdbc().queryForMap("MISSTAFF.selAO",
				new Object[] { brno, custId });
	}

	@Override
	public int delete(Object[] msgFmtParam, Object[] data) {
		return this.getJdbc().updateByCustParam("delete", msgFmtParam, data);
	}

	@Override
	public void insert(Object[] msgFmtParam, int[] type, List<Object[]> lst) {
		this.getJdbc().batchUpdateByCustParam("insert", msgFmtParam, type, lst);
	}

	@Override
	public void update(Object[] msgFmtParam, int[] type, List<Object[]> lst) {
		this.getJdbc().batchUpdateByCustParam("update", msgFmtParam, type, lst);
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List<Map<String, Object>> find(Class clazz, Object[] msgFmtParam,
			Object[] data) {
		// String tableNm = clazz.getSimpleName().toUpperCase();
		return this.getJdbc().queryForListByCustParam("select", msgFmtParam,
				data);
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List<Map<String, Object>> findWithMax(Class clazz,
			Object[] msgFmtParam, Object[] data) {
		// String tableNm = clazz.getSimpleName().toUpperCase();
		return this.getJdbc().queryForAllListByCustParam("select", msgFmtParam,
				data);
	}

	@Override
	public List<Map<String, Object>> listLMS180R11(String bgnDate,
			String endDate, String[] otherCondition) {
		if (otherCondition == null || otherCondition.length == 0) {
			otherCondition = new String[] { "" };
		}
		List<Object> params = new ArrayList<Object>();
        String noBrParams = Util.genSqlParam(otherCondition);
		params.add(bgnDate);
		params.add(endDate);
		params.addAll(Arrays.asList(otherCondition));
		return this.getJdbc().queryForAllListByCustParam("rpt.LMS180R11",
				new String[] { noBrParams },
				params.toArray(new Object[0]));
	}

	@Override
	public HashMap<String, HashMap<String, String>> findDISTINCTCntrNoByCustId(
			HashSet<String> custIdSet) {
		StringBuffer custString = new StringBuffer();
		List<Object> params = new ArrayList<Object>();
		String custIdParams = Util.genSqlParam(custIdSet.toArray(new String[0]));
//		for (String key : custIdSet) {
//			custString.append(custString.length() > 0 ? "," : "");
//			custString.append("'");
//			custString.append(key);
//			custString.append("'");
//		}
		params.addAll(custIdSet);
		params.addAll(custIdSet);

		List<Map<String, Object>> rowData = this.getJdbc()
				.queryForAllListByCustParam(
						"findDISTINCTCntrNoByCustId",
						new Object[] { custIdParams,
								custIdParams }, params.toArray(new Object[0]));
		// 用來記錄已經產生的 custId + cntrno
		HashSet<String> keys = new HashSet<String>();
		HashMap<String, HashMap<String, String>> custIdAndCntrNo = new HashMap<String, HashMap<String, String>>();
		for (Map<String, Object> row : rowData) {
			String custId = Util.trim(row.get("custId"));
			String cntrNo = Util.trim(row.get("cntrNo"));
			String datasrc = Util.trim(row.get("DATASRC"));
			String key = custId + cntrNo;
			if (keys.contains(key)) {
				continue;
			} else {
				keys.add(key);
			}
			if (!custIdAndCntrNo.containsKey(custId)) {
				custIdAndCntrNo.put(custId, new HashMap<String, String>());
			}
			custIdAndCntrNo.get(custId).put(cntrNo, datasrc);
		}
		return custIdAndCntrNo;
	}

	@Override
	public List<Map<String, Object>> findGroupLoanByContractBrNo(
			String grpCntrNo, String brNo) {
		return this.getJdbc().queryForList("MIS.LNF030.FindByContractBrNo",
				new Object[] { grpCntrNo, brNo });
	}

	@Override
	public List<Map<String, Object>> findGroupLoanDetail(String custId,
			String grpCntrNo, String brNo) {
		return this.getJdbc().queryForList("MIS.LNF030.FindGroupLoanDetail",
				new Object[] { grpCntrNo, brNo, custId });
	}

	@Override
	public Map<String, Object> findBankData(String brno) {
		return this.getJdbc().queryForMap("ELF339.selectByBRNO",
				new Object[] { brno });
	}

	@Override
	public List<Map<String, Object>> findPteamappData(String custId,
			String DupNo, String Year, String Amtappno) {

		return this.getJdbc().queryForList("MIS.PTEAMAPP.findPteamappData",
				new Object[] { custId, DupNo, Year, Amtappno });
	}

	@Override
	public List<Map<String, Object>> findPteamappDataByGrpcntrno(
			String Grpcntrno) {

		return this.getJdbc().queryForList("MIS.PTEAMAPP.selByGrpcntrno",
				new Object[] { Grpcntrno });
	}

	@Override
	public List<Map<String, Object>> findELF447NByBuildname(String Buildname,
			Integer tmod) {
		List<Map<String, Object>> Result = new ArrayList<Map<String, Object>>();
		if (tmod == 1) {
			Result = this.getJdbc().queryForList("MIS.ELF447.findByBuildname1",
					new Object[] { Buildname });
		} else if (tmod == 2) {
			Buildname = Buildname + "%";
			Result = this.getJdbc().queryForList("MIS.ELF447.findByBuildname2",
					new Object[] { Buildname });
		}
		return Result;
	}

	@Override
	public List<Map<String, Object>> findGRPCMPByCustDup(String CustId,
			String DupNo) {
		List<Map<String, Object>> Result = new ArrayList<Map<String, Object>>();
		Result = this.getJdbc().queryForList("MIS.GRPCMP.findByCustDup",
				new Object[] { CustId, DupNo });
		return Result;
	}

	@Override
	public List<Map<String, Object>> findCUSTDATAByCustDup(String CustId,
			String DupNo) {
		List<Map<String, Object>> Result = new ArrayList<Map<String, Object>>();
		Result = this.getJdbc().queryForList("MIS.CUSTDATA.findByCustDup",
				new Object[] { CustId, DupNo });
		return Result;
	}

	/**
	 * 取得ELF431資料
	 * 
	 * @param BRNO
	 *            文件分行代碼
	 * @param CUSTID
	 *            客戶統編
	 * @param DUPNO
	 *            重複序號
	 * @param CNTRNO
	 *            額度序號
	 * @param KINDNO
	 *            產品種類
	 * 
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findELF431Data(String BRNO, String CUSTID,
			String DUPNO, String CNTRNO, String KINDNO) {
		List<Map<String, Object>> Result = new ArrayList<Map<String, Object>>();
		Result = this.getJdbc().queryForList("MIS.ELF431.findByBrNoCustDupno",
				new Object[] { BRNO, CUSTID, DUPNO, CNTRNO, KINDNO });
		return Result;
	}

	@Override
	public List<Map<String, Object>> selRlt(String custId) {
		List<Map<String, Object>> Result = new ArrayList<Map<String, Object>>();
		Result = this.getJdbc().queryForList("MISELREMAIN.selRlt1",
				new Object[] { custId, custId, custId });
		return Result;
	}

	@Override
	public Map<String, Object> selRlt(String custId, String dupNo) {
		Map<String, Object> Result = new HashMap<String, Object>();
		Result = this.getJdbc().queryForMap("MISELREMAIN.selRlt2",
				new Object[] { custId, dupNo });
		return Result;
	}

	@Override
	public ELF513 findELF513Data(String cntrNo) {

		Map<String, Object> rowData = this.getJdbc().queryForMap(
				"MIS.ELF513.findByCntrNo", new Object[] { cntrNo });
		if (rowData == null) {
			return null;
		} else {
			ELF513 model = new ELF513();
			DataParse.map2Bean(rowData, model);
			return model;
		}
	}

	@Override
	public List<ELF431> findELF431Data(String custId, String dupNo) {

		List<Map<String, Object>> rowData = this.getJdbc().queryForList(
				"MIS.ELF431.findByCustId", new Object[] { custId, dupNo });
		List<ELF431> list = new ArrayList<ELF431>();
		for (Map<String, Object> row : rowData) {
			ELF431 model = new ELF431();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}

	@Override
	public void updateELf513ByCntrNo(String document, int chgConTimes,
			String cntrNo) {
		this.getJdbc().update("MIS.ELF513.updateByCntrNo",
				new Object[] { document, chgConTimes, cntrNo });

	}

	@Override
	public void insetELf513ByCntrNo(String document, int chgConTimes,
			String cntrNo) {
		this.getJdbc().update("MIS.ELF513.insertByCntrNo",
				new Object[] { document, chgConTimes, cntrNo });
	}

	@Override
	public List<Map<String, Object>> findMISSTAFFByCustId(String custId,
			String staffId) {
		// 當配偶ID為空則帶入目前custId
		if (Util.isEmpty(staffId)) {
			staffId = custId;
		}
		return this.getJdbc().queryForList("MIS.MISSTAFFByCustId",
				new Object[] { custId + "%", staffId + "%" });
	}

	private List<Map<String, Object>> _findLNF02EByCustId(String custId,
			String staffId) {
		// 當配偶ID為空則帶入目前custId
		if (Util.isEmpty(staffId)) {
			staffId = custId;
		}
		return this.getJdbc().queryForList("LN.LNF02EByCustId",
				new Object[] { custId + "%", staffId + "%" });
	}

	@Override
	public boolean isBankMan(String custId) {
		return isBankMan(custId, custId);
	}

	@Override
	public boolean isBankMan(String custId, String staffId) {
		if (true) {
			List<Map<String, Object>> r1 = findMISSTAFFByCustId(custId, staffId);
			if (r1 != null && r1.size() > 0) {
				return true;
			}
		}
		if (true) {
			List<Map<String, Object>> r2 = _findLNF02EByCustId(custId, staffId);
			if (r2 != null && r2.size() > 0) {
				return true;
			}
		}

		return false;
	}

	@Override
	public boolean isBankMan_on_the_job(String givenId) {
		if (Util.isNotEmpty(givenId)) {
			List<Map<String, Object>> list = findMISSTAFFByCustId(givenId,
					givenId);
			for (Map<String, Object> row : list) {
				String status = MapUtils.getString(row, "MISSTAFF_STATUS");
				if (Util.equals(status, "1")) {
					return true;
				}
			}
		}
		return false;
	}

	@Override
	public int findELF447NByUnid(String ELF447N_UNID) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForList(
				"MIS.ELF447N_findByUnid", new String[] { ELF447N_UNID }, 0, 1);
		return rowData.size();
	}

	@Override
	public ELF447N findELF447NByUKey(String ELF447N_UNID,
			String ELF447N_CUSTID, String ELF447N_DUPNO, String ELF447N_CONTRACT) {

		List<Map<String, Object>> rowData = this.getJdbc().queryForList(
				"MIS.ELF447N_findByUKey",
				new Object[] { ELF447N_UNID, ELF447N_CUSTID, ELF447N_DUPNO,
						ELF447N_CONTRACT });
		if (rowData.size() > 0) {
			List<ELF447N> list = new ArrayList<ELF447N>();
			for (Map<String, Object> row : rowData) {
				ELF447N model = new ELF447N();
				DataParse.map2Bean(row, model);
				list.add(model);
			}
			return list.get(0);
		} else {
			return null;
		}
	}

	@Override
	public Map<String, BigDecimal> findMISELGHTAPPByAll() {
		List<Map<String, Object>> rowData = this.getJdbc().queryForList(
				"findMISELGHTAPPByAll", new Object[] {});
		HashMap<String, BigDecimal> map = new HashMap<String, BigDecimal>();
		for (Map<String, Object> data : rowData) {
			// kindno,totapp
			// TABLE上存的是萬元，所以要乘上10000
			map.put(this.getELF431_KINDNO(Util.trim(data.get("KINDNO"))),
					Arithmetic.mul(Util.parseBigDecimal(data.get("TOTAPP")),
							new BigDecimal("10000")));
		}
		return map;
	}

	@Override
	public Map<String, BigDecimal> findSUMBYELF431(HashSet<String> cntrNos) {
		HashMap<String, BigDecimal> map = new HashMap<String, BigDecimal>();

		String cntrParams = Util.genSqlParam(cntrNos.toArray(new String[0]));

//		StringBuffer cntrnoStr = new StringBuffer();
//		for (String cntrNo : cntrNos) {
//			if (Util.isNotEmpty(Util.trim(cntrNo))) {
//				cntrnoStr.append(cntrnoStr.length() > 0 ? "," : "");
//				cntrnoStr.append("'");
//				cntrnoStr.append(cntrNo);
//				cntrnoStr.append("'");
//			}
//		}

//		String cntrnoStrParam = cntrnoStr.toString();
		if (CollectionUtils.isNotEmpty(cntrNos)) {
			List<Map<String, Object>> rowData = this.getJdbc()
					.queryForListByCustParam("findSUMBYELF431",
							new Object[] { cntrParams }, cntrNos.toArray(new String[0]));

			for (Map<String, Object> data : rowData) {
				map.put(this.getELF431_KINDNO(Util.trim(data.get("KINDNO"))),
						Util.parseBigDecimal(data.get("FAV_LOAN")));
			}
		}
		return map;
	}

	/**
	 * 將ELF431_KINDNO 轉換成 產品種類
	 * 
	 * @param value
	 * @return
	 */
	private String getELF431_KINDNO(String value) {
		String prodkind = "";

		int kindNo = Util.parseInt(value);
		switch (kindNo) {
		case 1:
			prodkind = "28";
			break;
		case 2:
			prodkind = "35";
			break;
		case 3:
			prodkind = "56";
			break;
		case 4:
			prodkind = "59";
			break;
		}

		return prodkind;
	}

	@Override
	public BigDecimal find020ByCheckBankMan1(String custId) {
		/*
		 * 在 tfs 57759 {2013/7/1 下午 07:17:28 r30361} 增加這段 java 程式，並且抓
		 * LNF020_FACT_AMT 同版號 的 misSQL.xml SELECT SUM(B.LNF020_FACT_AMT) AS
		 * LNF020_FACT_AMT FROM => Java 與 SQL 兩邊是搭配的起來，所以有去抓A-LOAN的金額出來
		 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ 在 tfs 58001
		 * {2013/7/16 下午 03:35:56 r30856} 修改檢查行員優惠房貸sql misSQL.xml 把抓出的
		 * columnName 由 LNF020_FACT_AMT 改 LOAN_BAL 但
		 * find020ByCheckBankMan1(String custId) 抓取的columnName未改 =>
		 * 應該A-LOAN的金額都是抓到0
		 */
		Map<String, Object> queryForMap = this.getJdbc().queryForMap(
				"LNF030.SUMLNF030BYRATE", new Object[] { custId });
		BigDecimal result = BigDecimal.ZERO;
		if (queryForMap != null) {
			// result =
			// Util.parseBigDecimal(queryForMap.get("LNF020_FACT_AMT"));
			result = Util.parseBigDecimal(queryForMap.get("LOAN_BAL"));
		}

		return result;
	}

	@Override
	public BigDecimal find020ByCheckBankMan2(String custId) {
		Map<String, Object> queryForMap = this.getJdbc().queryForMap(
				"LNF030.SUMLNF030BYRATEISLONG", new Object[] { custId });
		BigDecimal result = BigDecimal.ZERO;
		if (queryForMap != null) {
			result = Util.parseBigDecimal(queryForMap.get("LNF020_FACT_AMT"));
		}

		return result;
	}

	@Override
	public BigDecimal find020ByCheckBankMan3(String custId) {
		Map<String, Object> queryForMap = this.getJdbc().queryForMap(
				"LNF030.SUMAMTBycheck", new Object[] { custId });
		BigDecimal result = BigDecimal.ZERO;
		if (queryForMap != null) {
			result = Util.parseBigDecimal(queryForMap.get("LOAN_BAL"));
		}
		return result;
	}

	@Override
	public Map<String, Object> findCustBussDataByIdAndDup(String custId,
			String dupNo) {
		return this.getJdbc().queryForMap("MISCUSTDATA.selBUSSDATAByIdAndDup",
				new Object[] { custId, dupNo });
	}

	@Override
	public List<Map<String, Object>> findLNF022_selL120s01n030(
			String allCustId, String custId, String dupNo, String custName) {
		return this.getJdbc().queryForListByCustParam(
				"LNF022.selL120s01n030",
				new Object[] { "'" + allCustId + "'", "'" + custId + "'",
						"'" + dupNo + "'", "'" + custName + "'" },
				new Object[] { custId, dupNo });
	}

	@Override
	public List<Map<String, Object>> findLNF022_selL120s01n010(String custId,
			String dupNo) {
		return this.getJdbc().queryForList("LNF022.selL120s01n010",
				new Object[] { custId, dupNo }, 0, 1);
	}

	@Override
	public List<Map<String, Object>> findLNF022_selL120s01n020(
			String allCustId, String custId, String dupNo, String custName) {
		return this.getJdbc().queryForListByCustParam(
				"LNF022.selL120s01n020",
				new Object[] { "'" + allCustId + "'", "'" + custId + "'",
						"'" + dupNo + "'", "'" + custName + "'" },
				new Object[] { custId, dupNo, custId, dupNo });
	}

	@Override
	public List<Map<String, Object>> findLNF022_selL120s01n080(
			String allCustId, String custId, String dupNo, String custName) {
		return this.getJdbc().queryForListByCustParam(
				"LNF022.selL120s01n080",
				new Object[] { "'" + allCustId + "'", "'" + custId + "'",
						"'" + dupNo + "'", "'" + custName + "'" },
				new Object[] { custId, dupNo, custId, dupNo });
	}

	/**
	 * G-104-0097-001 Web e-Loan
	 * 海外授信管理系統簽報書檢核對同一人、同一關係人、同一關係企業或集團之授信限額規定不得超過泰子行淨值25%。 取得集團授信明細
	 * 
	 * @param grpNo
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findLNF022_selL120s01n090(String grpNo) {
		return this.getJdbc().queryForList("LNF022.selL120s01n090",
				new Object[] { grpNo });
	}

	@Override
	public int deleteLNF09G(String lnf09g_key_1, String lnf09g_key_2,
			String lnf09g_key_3) {
		return this.getJdbc().update("LNF09G.delByKey1_Key2_Key3",
				new Object[] { lnf09g_key_1, lnf09g_key_2, lnf09g_key_3 });
	}

	@Override
	public List<Map<String, Object>> getCrsLNFE0854() {
		return this.getJdbc().queryForListWithMax("LNFE0854.getCrs", null);
	}

	@Override
	public Map<String, Object> getLrsLNFE0854(String custId, String dupNo) {
		return this.getJdbc().queryForMap("LNFE0854.getLrs",
				new String[] { custId, dupNo });
	}

	/**
	 * J-104-0192-001 修改Web e-Loan企金授信覆審異常通報發生後之覆審周期計算
	 */
	@Override
	public Map<String, Object> getLrsLNFE0854Next(String custId, String dupNo,
			String elf412_mddt, String elf412_lrdate) {
		return this.getJdbc().queryForMap("LNFE0854.getLrsNext",
				new String[] { custId, dupNo, elf412_mddt, elf412_lrdate });
	}

	// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
	@Override
	public List<Map<String, Object>> gfnGenerateCTL_FLMS180R12(String branch,
			String custId, String dupNo, String lrDate_yyyy_MM) {
		/*
		 * 因為可以人工改 LR_DATE 在此報表抓資料時, ORDER BY ELF494_LRDATE DESC
		 * 會有第1列ELF494_LRDATE, ELF494_LLRDATE 以及第2列ELF494_LRDATE
		 * 
		 * 優先用 第2列ELF494_LRDATE, 而非第1列ELF494_LLRDATE
		 */
		return this.getJdbc().queryForList(
				"gfnGenerateCTL_FLMS180R12_WITHOUT_CTLTYPE",
				new String[] { lrDate_yyyy_MM, branch, custId, dupNo }, 0, 2);
	}

	/**
	 * 查詢目前停權起迄
	 * 
	 * @param brno
	 *            分行
	 * @return
	 */
	@Override
	public List<Map<String, Object>> elf510_queryStop(String brno) {
		return this.getJdbc().queryForList("ELF510.queryStop",
				new Object[] { brno });
	}

	/**
	 * 918透過上傳EXCEL後捉A-LOAN的資再下傳EXCEL給918
	 */
	@Override
	public List<Map<String, Object>> findLNF155_findByLoanNo(String[] loanNos,
			String dataYYmm) {
		List<Object> params = new ArrayList<Object>();
		params.add(dataYYmm);
		String loanNoParams = Util.genSqlParam(loanNos);

		params.addAll(Arrays.asList(loanNos));
		return this.getJdbc().queryForAllListByCustParam(
				"LN.LNF155_findByLoanNo", new String[] { loanNoParams },
				params.toArray(new Object[0]));
	}

	@Override
	public List<Map<String, Object>> findLNF155_ELF459_SRCFLAG_1(String DataYYmm) {
		return this.getJdbc().queryForListWithMax("LN.LNF155.ELF459_SRCFLAG_1",
				new String[] { DataYYmm });
	}

	@Override
	public int update_elf459_srcflag(String srcflag, String cntrNo) {
		return this.getJdbc().update("ELF459.update_elf459_srcflag",
				new String[] { srcflag, cntrNo });
	}

	@Override
	public void batch_update_elf459_srcflag(List<Object[]> batchValues) {
		this.getJdbc().batchUpdate("ELF459.update_elf459_srcflag",
				new int[] { Types.CHAR, Types.CHAR }, batchValues);
	}

	/**
	 * 取得客戶項下a-Loan未銷戶額務序號(企金用)
	 */
	public List<Map<String, Object>> findAloanCntrnoByCustId(String custId) {
		return this.getJdbc().queryForList("MIS.queryCustAloanCntrno",
				new Object[] { custId, custId });
	}

	/**
	 * 取得客戶項下a-Loan已銷戶額務序號(企金用)
	 */
	public List<Map<String, Object>> findAloanCancelCntrnoByCntrno(String cntrNo) {
		return this.getJdbc().queryForList("MIS.queryCustAloanCancelCntrno",
				new Object[] { cntrNo, cntrNo });
	}

	/**
	 * 取得衍生性商品風險係數(信用轉換係數)
	 */
	@Override
	public Map<String, Object> selCreditConverFactor(String dervKind,
			String dervPeriod) {
		return this.getJdbc().queryForMap("LNF07A.selCreditConverFactor",
				new String[] { dervKind, dervPeriod });
	}

	/**
	 * 查詢企金elf383央行註記資訊byID
	 */
	@Override
	public List<Map<String, Object>> selL140M01MForQUOTAPPR(String custId) {
		return this.getJdbc().queryForList("QUOTAPPR.selL140M01MData",
				new String[] { custId });
	}

	/**
	 * 查詢企金elf383央行註記資訊byID/CNTRNO/SDATE
	 */
	@Override
	public Map<String, Object> selL140M01MForQUOTAPPR1(String custId,
			String cntrNo, String sdate) {
		return this.getJdbc().queryForMap("QUOTAPPR.selL140M01MData1",
				new String[] { custId, cntrNo, sdate });
	}

	/**
	 * 更新企金elf383央行註記資訊byID/CNTRNO/SDATE
	 */
	@Override
	public void updateL140M01MForQUOTAPPR(String CONTROLCD, BigDecimal LTVRATE,
			String LOCATIONCD, String JCICMARK, BigDecimal APPAMT,
			String PLUSREASON, String REG_PURPOSE, BigDecimal EST_AMT,
			BigDecimal LAWVAL, String COCOLL_FG, String PART_FUND,
			BigDecimal SUM_FACTAMT, String RESTRICT, String HP_HOUSE,
			String PLAN_AREA, String P_USETYPE, String P_LOANUSE,
			String COLL_CHAR, String KEEP_LAWVAL, String PLUS_MEMO,
			String SITE3NO, String SITE4NO, String COLL_CHAR_M, String SUPVNO,
			String VERSION, String HLOANLIMIT, String HLOANLIMIT_2,
			Date ENDDATE, String ISRENEW, String ISPAYOLDQUOTA,
			BigDecimal OLDQUOTA, BigDecimal PAYOLDAMT, String PAYOLDAMTITEM,
			String ISMATCHUNSOLDHOUSEITEM, String ISSALECASE, Date LSTDATE,
			BigDecimal TIMEVAL, String CUSTID, String CNTRNO, String SDATE) {
		this.getJdbc().update(
				"QUOTAPPR.updateL140M01MData",
				new Object[] { CONTROLCD, LTVRATE, LOCATIONCD, JCICMARK,
						APPAMT, PLUSREASON, REG_PURPOSE, EST_AMT, LAWVAL,
						COCOLL_FG, PART_FUND, SUM_FACTAMT, RESTRICT, HP_HOUSE,
						PLAN_AREA, P_USETYPE, P_LOANUSE, COLL_CHAR,
						KEEP_LAWVAL, PLUS_MEMO, SITE3NO, SITE4NO, COLL_CHAR_M,
						SUPVNO, VERSION, HLOANLIMIT, HLOANLIMIT_2, ENDDATE,
						ISRENEW, ISPAYOLDQUOTA, OLDQUOTA, PAYOLDAMT,
						PAYOLDAMTITEM, ISMATCHUNSOLDHOUSEITEM, ISSALECASE,
						LSTDATE, TIMEVAL, CUSTID, CNTRNO, SDATE });
	}

	/**
	 * 查詢LN.LNF023科目限額資訊
	 */
	@Override
	public List<Map<String, Object>> selC900M01FDataByLNF023(String cntrNo) {
		return this.getJdbc().queryForList("LNF023.selC900M01FData",
				new String[] { cntrNo });
	}

	/**
	 * Rpt[可疑代辦案件註記] 由MIS.ELF516中取得可疑代辦案件註記未編輯
	 */
	@Override
	public List<Map<String, Object>> findELF516_forRptDataNoEdit() {
		return this.getJdbc().queryForList("MIS.ELF516_ForRptDataNoEdit", null);
	}

	/**
	 * Rpt[可疑代辦案件註記] 由MIS.ELF516中取得可疑代辦案件註記已編輯
	 */
	@Override
	public List<Map<String, Object>> findELF516_forRptDataEdit(String begDate,
			String endDate) {
		return this.getJdbc().queryForList("MIS.ELF516_ForRptDataEdit",
				new String[] { begDate, endDate });
	}

	/**
	 * Rpt[可疑代辦案件註記] 由MIS.ELF516中取得可疑代辦案件註記ALL
	 */
	@Override
	public List<Map<String, Object>> findELF516_forRptDataAll(String begDate,
			String endDate) {
		return this.getJdbc().queryForList("MIS.ELF516_ForRptDataAll",
				new String[] { begDate, endDate });
	}

	@Override
	public Map<String, Object> getELF447N_ForClsByCntrno(String cntrNo) {
		return this.getJdbc().queryForMap("MIS.ELF447N_ForClsByCntrno",
				new String[] { cntrNo });

	}

	@Override
	public ELF447N getLatestELF447N_byCntrno(String cntrNo) {
		Map<String, Object> dataMap = getELF447N_ForClsByCntrno(cntrNo);
		if (!MapUtils.isEmpty(dataMap)) {
			ELF447N model = new ELF447N();
			DataParse.map2Bean(dataMap, model);
			return model;
		}
		return null;
	}

	/**
	 * 企金額度明細表整批引進最新資料作業_引進平均動用率
	 * 
	 * @param cntrNo
	 * @param date
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findMrateByCntrnoAndDate1(String cntrNo,
			String date) {
		return getJdbc().queryForList("MRATE.findByCntrnoAndDate1",
				new String[] { cntrNo, date }, 0, 1);
	}// ;

	@Override
	public List<Map<String, Object>> findELF493_docIdList(String branchId) {
		// 抓最新一筆
		return this.getJdbc().queryForList("ELF493.byBranch_af2014",
				new String[] { branchId });
	}

	@Override
	public List<ELF493> findELF493(String elf493_rptDocId,
			String elf493_docStus1) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForListWithMax(
				"ELF493.byRptDocId_docStus1",
				new String[] { elf493_rptDocId, elf493_docStus1 });
		List<ELF493> list = new ArrayList<ELF493>();
		for (Map<String, Object> row : rowData) {
			ELF493 model = new ELF493();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}

	@Override
	public BigDecimal importAvgURateByCustIdDate(String custId, String sDate,
			String eDate, String unitNo) {
		Map<String, Object> data = this.getJdbc().queryForMap(
				"MRATE.importAvgURateByCustIdDate",
				new Object[] { unitNo, custId, sDate, eDate, unitNo, custId,
						sDate, eDate });
		if (data != null && !data.isEmpty()) {
			Double value = (Double) data.get("RATE");
			if (value != null) {
				BigDecimal rate = new BigDecimal(value).setScale(2,
						RoundingMode.HALF_UP);
				// 平均動用率如大於100，以100算
				rate = rate.compareTo(BigDecimal.valueOf(100)) > 0 ? BigDecimal
						.valueOf(100) : rate;
				return rate;
			}

		}
		return null;
	}

	/**
	 * 查詢LN.LNF023已敘做科目
	 */
	@Override
	public List<Map<String, Object>> findLNF023_selByContract(String cntrNo) {
		return this.getJdbc().queryForList("LNF023.selByContract",
				new String[] { cntrNo });
	}

	@Override
	public Map<String, Object> findLNF035_selByLoanNo(String loanNo) {
		return this.getJdbc().queryForMap("LNF035.selByLoanNo",
				new String[] { loanNo });
	}

	/**
	 * G-104-0286 加強銀行法72-2條之相關控管 判斷LNF030 該帳號72-2情況
	 * 
	 * @param cntrNo
	 * @return
	 */
	@Override
	public Map<String, Object> flndLNF030_sel722ByCntrNo(String cntrNo) {
		return this.getJdbc().queryForMap("LNF030.sel722ByCntrno",
				new Object[] { cntrNo, cntrNo });
	}

	/**
	 * G-104-0286 加強銀行法72-2條之相關控管 判斷LNF030 該帳號是否為購置不動產
	 * 
	 * @param cntrNo
	 * @return
	 */
	@Override
	public Map<String, Object> flndLNF030_sel722_UseTypeByCntrNo(String cntrNo) {
		return this.getJdbc().queryForMap("LNF030.sel722UseTypeByCntrno",
				new Object[] { cntrNo });
	}

	/**
	 * G-104-0286 加強銀行法72-2條之相關控管 取得排除事項內容
	 * 
	 * @param cntrNo
	 * @return
	 */
	@Override
	public Map<String, Object> flndLNF030_sel722_ExItemByCntrNo(String cntrNo) {
		return this.getJdbc().queryForMap("LNF030.sel722ExItemByCntrno",
				new Object[] { cntrNo });
	}

	@Override
	public void updateELF085XMdbrnoByCustId(String newBrno, String custId,
			String dupNo, String oldBrno) {
		try {
			this.getJdbc().update("LNFE0854.updateMdbrnoByCustId",
					new Object[] { newBrno, custId, dupNo, oldBrno });
		} catch (Exception e) {
			System.out.println(e.toString());
			// 若無資料不處理
		}
		try {
			this.getJdbc().update("LNFE0855.updateMdbrnoByCustId",
					new Object[] { newBrno, custId, dupNo, oldBrno });
		} catch (Exception e) {
			System.out.println(e.toString());
			// 若無資料不處理
		}
		try {
			this.getJdbc().update("LNFE0856.updateMdbrnoByCustId",
					new Object[] { newBrno, custId, dupNo, oldBrno });
		} catch (Exception e) {
			System.out.println(e.toString());
			// 若無資料不處理
		}

	}

	/**
	 * J-105-0078-001 Web e-Loan授信信用風險管理「遵循檢核表」當地限額之關係企業名單，請改依AS400集團建檔資料。
	 * 取得客戶名稱 1.SQL取得CNAME(有LCNAME以LCNANE，沒有以CNAME) 與 ENAME 2.判斷CUSTID，若為OBU
	 * ID，LCNAME 與 ENAME 八成一樣則顯示ENAME
	 */
	@Override
	public String findCustFinalNameByIdAndDup(String custId, String dupNo) {
		Map<String, Object> data = this.getJdbc()
				.queryForMap("CUSTDATA.findAllNameByIdDupNo",
						new Object[] { custId, dupNo });

		String cname = Util.trim(data.get("CNAME"));
		String ename = Util.trim(data.get("ENAME"));
		String finalName = Util.toSemiCharString(CustomerIdCheckUtil.getName(
				custId, cname, ename));
		return finalName;

	}

	@Override
	public Map<String, Object> findLNF087(String cntrNo) {
		return this.getJdbc().queryForMap("LNF087.findByCONTRACT",
				new Object[] { cntrNo });
	}

	/**
	 * J-105-0179-001 Web e-Loan企金授信建立「往來異常通報戶」紀錄查詢及於簽報書上顯示查詢結果功能
	 * 取得最近一次異常通報紀錄(未結案優先顯示)
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findLnfe0854LastStatusByCustId(
			String custId, String dupNo) {
		return this.getJdbc().queryForList("LNFE0854.getLastStatusByCustId",
				new Object[] { custId, dupNo, custId, dupNo });
	}

	/**
	 * N-105-0127-001 Web e-Loan配合衍生性金融商品風險係數修改
	 */
	@Override
	public List<Map<String, Object>> findLnfe07BListByTradeType(
			String lnfe07BTradeType) {
		return this.getJdbc().queryForList("LNF07B.selDervPeriodByTradeType",
				new String[] { lnfe07BTradeType });
	}

	/**
	 * N-105-0127-001 Web e-Loan配合衍生性金融商品風險係數修改
	 */
	@Override
	public Map<String, Object> findLnfe07BByTradeTypeAndPERIOD(
			String tradeType, String periodType) {
		return this.getJdbc().queryForMap(
				"LNF07B.selDervNumByTradeTypeAndPERIOD",
				new Object[] { tradeType, periodType });
	}

	/**
	 * 修改ELCSECNT 統編 J-105-0202-001 Web e-Loan企金授信修改客戶統編。
	 * 
	 * @param orgCustId
	 * @param orgDupNo
	 * @param newCustId
	 * @param newDupNo
	 * @param cntrNo
	 */
	@Override
	public void update********_by_custIdAndDupNo_byRptDoc_approve(
			String orgCustId, String orgDupNo, String newCustId,
			String newDupNo, String documentNo, String cntrNo) {

		String fullCustKeyOrg = Util.getLeftStr(orgCustId + "          ", 10)
				+ orgDupNo;
		String fullCustKeyNew = Util.getLeftStr(newCustId + "          ", 10)
				+ newDupNo;

		// 上傳MIS、AS400********************************************************************************
		// 依照簽報書
		// ELF344(LNUNID) 本行婉卻資料檔 ----CUSTID DUPNO -CUSTID DUPNO
		this.getJdbc().update(
				"********.update_LNUNID_01",
				new Object[] { newCustId, newDupNo, orgCustId, orgDupNo,
						documentNo });
		// ELF404(ELCSECNT) 授信案件統計檔--COMID COMDUPNO -CNTRNO
		this.getJdbc()
				.update("********.update_ELCSECNT_01",
						new Object[] { newCustId, newDupNo, orgCustId,
								orgDupNo, cntrNo });
		// ELF461(ELLNSEEK) 企消金狀態報送檔--CUSTID DUPNO -CNTRNO
		this.getJdbc()
				.update("********.update_ELLNSEEK_01",
						new Object[] { newCustId, newDupNo, orgCustId,
								orgDupNo, cntrNo });

		// ELF447 授信授權人員檔 --ELF447_CUSTID ELF447_DUPNO ELF447_CONTRACT
		this.getJdbc()
				.update("********.update_ELF447_01",
						new Object[] { newCustId, newDupNo, orgCustId,
								orgDupNo, cntrNo });

		// ELF447N 授信案件狀態檔 --ELF447N_CUSTID ELF447N_DUPNO
		// ELF447N_CONTRACT
		this.getJdbc()
				.update("********.update_ELF447N_01",
						new Object[] { newCustId, newDupNo, orgCustId,
								orgDupNo, cntrNo });

		// ELF442 授信集團預約額度檔---------預約不異動

		// ELF506 大陸地區維護控管註記 --- ELF506_CUST_ID --ELF506_CNTRNO
		this.getJdbc().update("********.update_ELF506_01",
				new Object[] { fullCustKeyNew, cntrNo });

		// LNFE0851 往來異常戶匯總檔----不改，因為有送卡務中心跟風控
		// LNFE0854 異常通報追蹤事項主檔----不改，因為有送卡務中心跟風控
		// LNFE0855 異常通報追蹤事項主檔-說明檔----不改，因為有送卡務中心跟風控
		// LNFE0856 異常通報追蹤事項明細檔----不改，因為有送卡務中心跟風控

	}

	/**
	 * 修改ELCSECNT 統編 J-105-0202-001 Web e-Loan企金授信修改客戶統編。
	 * 
	 * @param orgCustId
	 * @param orgDupNo
	 * @param newCustId
	 * @param newDupNo
	 * @param cntrNo
	 */
	@Override
	public void update********_by_custIdAndDupNo_byDrawdown_approve(
			String orgCustId, String orgDupNo, String newCustId,
			String newDupNo, String documentNo, String cntrNo) {

		String fullCustKeyOrg = Util.getLeftStr(orgCustId + "          ", 10)
				+ orgDupNo;
		String fullCustKeyNew = Util.getLeftStr(newCustId + "          ", 10)
				+ newDupNo;

		// 上傳MIS、AS400********************************************************************************

		// ELF388(IQUOTAPP) 核准額度資料檔 --CUSTID DUPNO --QUOTANO
		this.getJdbc()
				.update("********.update_IQUOTAPP_01",
						new Object[] { newCustId, newDupNo, orgCustId,
								orgDupNo, cntrNo });

		// ELF421(IQUOTGUR) 保證人檔 --CUSTID DUPNO --QUOTANO
		this.getJdbc()
				.update("********.update_IQUOTGUR_01",
						new Object[] { newCustId, newDupNo, orgCustId,
								orgDupNo, cntrNo });

		// ELF389(IQUOTJON) 共同借款人檔 --CUSTID DUPNO --QUOTANO
		this.getJdbc()
				.update("********.update_IQUOTJON_01",
						new Object[] { newCustId, newDupNo, orgCustId,
								orgDupNo, cntrNo });

		// ELF422(QUOTAINF) 額度資訊檔 --CUSTID DUPNO --CNTRNO
		try {
			this.getJdbc().update(
					"********.update_QUOTAINF_01",
					new Object[] { newCustId, newDupNo, orgCustId, orgDupNo,
							cntrNo });
		} catch (Exception e) {

		}

		// ELF383(QUOTAPPR) 授信額度檔 --CUSTID DUPNO --CNTRNO
		this.getJdbc()
				.update("********.update_QUOTAPPR_01",
						new Object[] { newCustId, newDupNo, orgCustId,
								orgDupNo, cntrNo });

		// ELF384(QUOTSUB) 科(子)目及其限額檔 --CUSTID DUPNO --CNTRNO
		this.getJdbc()
				.update("********.update_QUOTSUB_01",
						new Object[] { newCustId, newDupNo, orgCustId,
								orgDupNo, cntrNo });

		// ELF385(QUOTUNIO) 聯貸案參貸比率檔 --無ID不需改

		// ELF503 企金結構化利率檔 --CUSTID DUPNO --CNTRNO
		this.getJdbc()
				.update("********.update_ELF503_01",
						new Object[] { newCustId, newDupNo, orgCustId,
								orgDupNo, cntrNo });

		// ELF517 與額度相關資訊維護檔 --無ID不需改

		// ELF401(ELLNGTEE) 主從債務人資料檔 --CUSTID DUPNO LNGEID DUPNO1
		// --CNTRNO
		try {
			this.getJdbc().update(
					"********.update_ELLNGTEE_01",
					new Object[] { newCustId, newDupNo, orgCustId, orgDupNo,
							cntrNo });
		} catch (Exception e) {

		}
		try {
			this.getJdbc().update(
					"********.update_ELLNGTEE_02",
					new Object[] { newCustId, newDupNo, orgCustId, orgDupNo,
							cntrNo });
		} catch (Exception e) {

		}
		// LNF164 授信利率檔 --LNF164_CUST_ID(11) LNF164_CONTRACT
		this.getJdbc().update("********.update_LNF164_01",
				new Object[] { fullCustKeyNew, fullCustKeyOrg, cntrNo });

		// ELF476(ELCRTBL) 企金簽案費率檔 --無ID不需改

	}

	/**
	 * 修改ELCSECNT 統編 J-105-0202-001 Web e-Loan企金授信修改客戶統編。
	 * 
	 * @param orgCustId
	 * @param orgDupNo
	 * @param newCustId
	 * @param newDupNo
	 * @param cntrNo
	 */
	@Override
	public void update********_by_custIdAndDupNo_byRptDoc_reject(
			String orgCustId, String orgDupNo, String newCustId,
			String newDupNo, String documentNo, String cntrNo) {

		String fullCustKeyOrg = Util.getLeftStr(orgCustId + "          ", 10)
				+ orgDupNo;
		String fullCustKeyNew = Util.getLeftStr(newCustId + "          ", 10)
				+ newDupNo;

		// 上傳MIS、AS400********************************************************************************
		// 依照簽報書
		// ELF344(LNUNID) 本行婉卻資料檔 ----CUSTID DUPNO -CUSTID DUPNO
		this.getJdbc().update(
				"********.update_LNUNID_01",
				new Object[] { newCustId, newDupNo, orgCustId, orgDupNo,
						documentNo });

	}

	/**
	 * 取得同業聯貸主辦By統編與分行 J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
	 * 
	 * @param custId
	 * @param dupNo
	 * @param brno
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findLNF020SyndTypeOneByCustIdAndBrno(
			String custId, String dupNo, String brno) {

		String fullCustKey = Util.getLeftStr(custId + "          ", 10) + dupNo;
		return this.getJdbc().queryForList(
				"MIS.MISLN20.getSyndTypeOneByCustIdAndBrNo",
				new Object[] { fullCustKey, brno });
	}

	/**
	 * J-105-0263-001 配合a-Loan新增利率比對報表，Web e-Loan企金核准時同步更新ELF500相同額度之資料
	 * 
	 * @param cntrNo
	 * @param custId
	 */
	@Override
	public void updateELf500_NOTVALID_ByCntrNo(String cntrNo, String custId,
			String dupNo) {
		this.getJdbc().update("MIS.ELF500.updateNOTVALIDByCntrNo",
				new Object[] { cntrNo, custId, dupNo });

	}

	/**
	 * J-105-0331-001 新增已核准授信額度辦理狀態通報彙總表
	 */
	@Override
	public Map<String, Object> findMaxSdateByCustIdAndCntrNoAndDoucmentNo(
			String custId, String dupNo, String cntrNo, String documentNo) {
		return this.getJdbc().queryForMap(
				"getMaxSdateByCustIdAndCntrNoAndDoucmentNo",
				new Object[] { custId, dupNo, cntrNo, documentNo, custId,
						dupNo, cntrNo, documentNo });
	}

	/**
	 * J-105-0331-001 新增已核准授信額度辦理狀態通報彙總表
	 */
	@Override
	public Map<String, Object> findLnf197AmtByContractAndCustId(String custId,
			String dupNo, String cntrNo) {

		String fullCustKey = Util.getLeftStr(custId + "          ", 10) + dupNo;

		return this.getJdbc().queryForMap("MIS.LNF197.getByContract",
				new Object[] { fullCustKey, cntrNo, fullCustKey, cntrNo });
	}

	/**
	 * J-105-0321-001 Web e-Loan授信管理系統增加營運中心轄下分行往來客戶有全行通報異常情形彙總表 LMS180R32
	 * 
	 * @param brnoArea
	 *
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findLMS180R32Data(String brnoArea) {

		return this.getJdbc().queryForListWithMax("rpt.LMS180R32",
				new Object[] { brnoArea, brnoArea });
	}

	/**
	 * J-105-0340-001 Web e-Loan 交換票據抵用科目調整並上傳a-Loan
	 * 
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @return
	 */
	@Override
	public Map<String, Object> findLastQuotapprOrderBySDate(String custId,
			String dupNo, String cntrNo) {
		return this.getJdbc().queryForMap(
				"batch.doLmsBatch0007.select.quotappr",
				new String[] { custId, dupNo, cntrNo });
	}

	/**
	 * J-105-0340-001 Web e-Loan 交換票據抵用科目調整並上傳a-Loan
	 * 
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @param sDate
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findLastQuotsubByCustIdCntrNoAndSDate(
			String custId, String dupNo, String cntrNo, String sDate) {

		return this.getJdbc().queryForListWithMax(
				"batch.doLmsBatch0007.select.quotsub",
				new Object[] { custId, dupNo, cntrNo, sDate });
	}

	/**
	 * J-105-0340-001 Web e-Loan 交換票據抵用科目調整並上傳a-Loan
	 * 
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @param sDate
	 */
	@Override
	public void updateQuotsubLoanTp(String custId, String dupNo, String cntrNo,
			String sDate, String orgLoanTp, String newLoanTp) {
		this.getJdbc().update(
				"batch.doLmsBatch0007.update.quotsub",
				new Object[] { newLoanTp, custId, dupNo, cntrNo, sDate,
						orgLoanTp });

	}

	/**
	 * J-105-0346-001 Web e-Loan國內企金授信覆審報告表，主要授信戶增加判斷BTT建檔資料。
	 * 
	 * @param brno
	 * @param custId
	 * @return
	 */
	@Override
	public Map<String, Object> selByBrNoAndCustId(String brno, String custId) {
		return this.getJdbc().queryForMap("MISAOF.selByBrNoAndCustId",
				new Object[] { brno, custId });
	}

	@Override
	public Map<String, Object> findCMFLUNVA_byUk(String custId, String dupNo) {
		return this.getJdbc().queryForMap("findCMFLUNVA_by_id_dup",
				new Object[] { custId, dupNo });
	}

	@Override
	public List<LNF916S> findLNF916S(Date procDateB, Date procDateE,
			String custId, String formId) {
		List<Map<String, Object>> list = this.getJdbc()
				.queryForListWithMax(
						"findLNF916S",
						new Object[] { TWNDate.toAD(procDateB),
								TWNDate.toAD(procDateE), (custId + "%"),
								(formId + "%") });
		return toLNF916S(list);
	}

	private List<LNF916S> toLNF916S(List<Map<String, Object>> rowData) {
		List<LNF916S> list = new ArrayList<LNF916S>();
		for (Map<String, Object> row : rowData) {
			LNF916S model = new LNF916S();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}

	@Override
	public List<LNF917S> findLNF917S(Date procDateB, Date procDateE,
			String custId, String formId) {
		List<Map<String, Object>> list = this.getJdbc()
				.queryForListWithMax(
						"findLNF917S",
						new Object[] { TWNDate.toAD(procDateB),
								TWNDate.toAD(procDateE), (custId + "%"),
								(formId + "%") });
		return toLNF917S(list);
	}

	private List<LNF917S> toLNF917S(List<Map<String, Object>> rowData) {
		List<LNF917S> list = new ArrayList<LNF917S>();
		for (Map<String, Object> row : rowData) {
			LNF917S model = new LNF917S();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}

	@Override
	public List<LNF919S> findLNF919S(Date procDateB, Date procDateE,
			String custId, String formId) {
		List<Map<String, Object>> list = this.getJdbc()
				.queryForListWithMax(
						"findLNF919S",
						new Object[] { TWNDate.toAD(procDateB),
								TWNDate.toAD(procDateE), (custId + "%"),
								(formId + "%") });
		return toLNF919S(list);
	}

	private List<LNF919S> toLNF919S(List<Map<String, Object>> rowData) {
		List<LNF919S> list = new ArrayList<LNF919S>();
		for (Map<String, Object> row : rowData) {
			LNF919S model = new LNF919S();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}

	@Override
	public List<Map<String, Object>> findLNF155_ym_br_cntrNo(
			String lnf155_data_ym, String lnf155_br_no,
			List<String> lnf155_contract_list) {

		String contract_listParams = Util.genSqlParam(lnf155_contract_list);
		List<Object> params = new ArrayList<Object>();
		params.add(lnf155_data_ym);
		params.add(lnf155_br_no);
		params.addAll(lnf155_contract_list);
//		StringBuilder sb = new StringBuilder("'");
//		for (String cntrNo : lnf155_contract_list) {
//			sb.append(sb.length() >= 1 ? "','" : "").append(cntrNo);
//		}
//		sb.append("'");
		// ~~~
		return this.getJdbc().queryForAllListByCustParam(
				"findLNF155_ym_br_cntrNo", new String[] { contract_listParams },
				params.toArray(new Object[0]));

	}

	/**
	 * J-105-0308-001 Web e-Loan國內海外企金授信管理系統，額度明細表產品種類新增「新創重點產業」。
	 */
	@Override
	public List<Map<String, Object>> findIsBusCdExistItwCode(String busCd) {

		return this.getJdbc().queryForListWithMax(
				"findLNF070_chkBusCdExistItwCode", new Object[] { busCd });
	}

	/**
	 * J-111-0129_05097_B1001 Web e-Loan企金授信額度明細表新增六大核心戰略產業及附屬細項
	 */
	@Override
	public List<Map<String, Object>> findIsBusCdExistItwCodeCoreBuss(
			String busCd) {

		return this.getJdbc().queryForListWithMax(
				"findLNF070_chkBusCdExistItwCodeCoreBuss",
				new Object[] { busCd });
	}

	/**
	 * J-106-0029-003 洗錢防制-新增實質受益人
	 *
	 * @param custId
	 * @param dupNo
	 *
	 * @return
	 */
	@Override
	public Map<String, Object> selCMFLUNBNByCustId(String custId, String dupNo) {
		return this.getJdbc().queryForMap("CMFLUNBN.selByCustId",
				new Object[] { custId, dupNo });
	}

	@Override
	public Map<String, Object> findImexEXFFAIT(String custId, String dupNo,
			String cntrNo) {
		return this.getJdbc().queryForMap("findIMEX.EXFFAITbyCustIdAndCntrno",
				new Object[] { custId, dupNo, cntrNo });
	}

	/**
	 * J-106-0110-001 Web e-Loan國內、海外企金簽報書修改第八章、第九章標題及「授信信用風險管理遵循檢核表」。
	 * 同一關係企業/集團企業建檔資料
	 * 
	 * @param allCustId
	 * @param custId
	 * @param dupNo
	 * @param custName
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findLNF022_selL120s05b1_A(
			String allCustId, String custId, String dupNo, String custName) {

		String newCustName = Util.trim(custName.replace("'", "''"));

		return this.getJdbc().queryForListByCustParam(
				"LNF022.selL120s05b1_A",
				new Object[] { "'" + allCustId + "'", "'" + custId + "'",
						"'" + dupNo + "'", "'" + newCustName + "'" },
				new Object[] { custId, dupNo });
	}

	/**
	 * 取得土建融案件By統編與分行 J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
	 * 
	 * @param custId
	 * @param dupNo
	 * @param brno
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findProd33And34ByCustIdAndBrNo(
			String custId, String dupNo, String brno) {

		// String fullCustKey = Util.getLeftStr(custId + "          ", 10) +
		// dupNo;
		return this.getJdbc().queryForListWithMax(
				"MIS.LNF020.getProd33And34ByCustIdAndBrNo",
				new Object[] { custId, dupNo, brno });
	}

	/**
	 * J-106-0145-002 Web e-Loan 國內企金授信管理系統修改實地覆審相關功能
	 * 
	 */
	@Override
	public void updateElf412_realCkFgAndRealDt1() {
		this.getJdbc().update("batch.doLmsBatch0009.update.elf412",
				new Object[] {});

	}

	/**
	 * J-106-0145-002 Web e-Loan 國內企金授信管理系統修改實地覆審相關功能
	 * 
	 */
	@Override
	public void updateElf412_realCkFgAndRealDt2() {
		this.getJdbc().update("batch.doLmsBatch0009.clear.elf412",
				new Object[] {});

	}

	/**
	 * J-106-0145-002 Web e-Loan 國內企金授信管理系統修改實地覆審相關功能
	 * 
	 */
	@Override
	public List<Map<String, Object>> batchForDoLmsBatch0010_queryElf412() {
		return getJdbc().queryForListWithMax(
				"batch.doLmsBatch0010.query.elf412", new String[] {});
	}

	/**
	 * J-106-0145-002 Web e-Loan 國內企金授信管理系統修改實地覆審相關功能
	 * 
	 */
	@Override
	public List<Map<String, Object>> batchForDoLmsBatch0010_queryLnf022(
			String branch, String custId, String dupNo) {
		String custIdDupNo = Util.addSpaceWithValue(custId, 10) + dupNo;
		return getJdbc().queryForListWithMax(
				"batch.doLmsBatch0010.query.lnf022",
				new String[] { branch, custIdDupNo, branch, custIdDupNo,
						branch, custIdDupNo, branch, custId, dupNo, branch,
						custIdDupNo, branch, custIdDupNo });
	}

	/**
	 * J-106-0117-001 增加金控法45條利害關係人比對範圍-實質關係人(授信以外交易)
	 */
	@Override
	public Map<String, Object> findRealRltByCustId(String custId, String dupNo) {
		return this.getJdbc().queryForMap("MIS.ELRESCOM.getRealRltByCustId",
				new Object[] { custId, dupNo });
	}

	/**
	 * J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
	 */
	@Override
	public List<Map<String, Object>> gfnGenerateCTL_FLMS180R12_with_ctlType(
			String branch, String custId, String dupNo, String lrDate_yyyy_MM,
			String ctlType) {
		/*
		 * 因為可以人工改 LR_DATE 在此報表抓資料時, ORDER BY ELF494_LRDATE DESC
		 * 會有第1列ELF494_LRDATE, ELF494_LLRDATE 以及第2列ELF494_LRDATE
		 * 
		 * 優先用 第2列ELF494_LRDATE, 而非第1列ELF494_LLRDATE
		 */
		return this.getJdbc()
				.queryForList(
						"ELF493.gfnGenerateCTL_FLMS180R12_WITH_CTLTYPE",
						new String[] { lrDate_yyyy_MM, branch, custId, dupNo,
								ctlType }, 0, 2);
	}

	/**
	 * J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
	 * 
	 */
	@NonTransactional
	@Override
	public void doLmsBatch0011() {
		this.getJdbc().update("batch.doLmsBatch0011.update.elf493",
				new Object[] {});

		this.getJdbc().update("batch.doLmsBatch0011.update.elf494",
				new Object[] {});

		this.getJdbc().update("batch.doLmsBatch0011.update.elf495",
				new Object[] {});

		this.getJdbc().update("batch.doLmsBatch0011.update.elf412b",
				new Object[] {});

	}

	@Override
	public List<Map<String, Object>> func_J_106_0170_sql_1A_eq_ratePlan(
			String beginDate, String endDate, String lnf033_company_id,
			String lnf033_ratePlan, boolean changeToLNF13E) {
		if (changeToLNF13E) {
			return this.getJdbc().queryForListWithMax(
					"J_106_0170_sql_1A.LNF13E",
					new String[] { beginDate, endDate, lnf033_company_id,
							lnf033_ratePlan + "%", "" });
		}
		return this.getJdbc().queryForListWithMax(
				"J_106_0170_sql_1A",
				new String[] { beginDate, endDate, lnf033_company_id,
						lnf033_ratePlan + "%", "" });
	}

	@Override
	public List<Map<String, Object>> func_J_106_0170_sql_1A_ne_ratePlan(
			String beginDate, String endDate, String lnf033_company_id,
			String lnf033_ratePlan, boolean changeToLNF13E) {
		if (changeToLNF13E) {
			return this.getJdbc().queryForListWithMax(
					"J_106_0170_sql_1A.LNF13E",
					new String[] { beginDate, endDate, lnf033_company_id, "%",
							lnf033_ratePlan });
		}
		return this.getJdbc().queryForListWithMax(
				"J_106_0170_sql_1A",
				new String[] { beginDate, endDate, lnf033_company_id, "%",
						lnf033_ratePlan });
	}

	@Override
	public List<Map<String, Object>> func_J_106_0170_sql_1B(String beginDate,
			String endDate, String cntrNo) {
		return this.getJdbc().queryForListWithMax("J_106_0170_sql_1B",
				new String[] { beginDate, endDate, cntrNo });
	}

	@Override
	public LNF13E findLNF13E_contract(String cntrNo) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForList(
				"LN.LNF13E.select", new String[] { cntrNo });
		List<LNF13E> list = toLNF13E(rowData);
		if (list.size() == 1) {
			return list.get(0);
		} else {
			return null;
		}
	}

	private List<LNF13E> toLNF13E(List<Map<String, Object>> rowData) {
		List<LNF13E> list = new ArrayList<LNF13E>();
		for (Map<String, Object> row : rowData) {
			LNF13E model = new LNF13E();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}

	/**
	 * G-106-0333-001 Web e-Loan 授信系統配合加拿大分行改制調整簽報書相關資料
	 * 
	 */
	@NonTransactional
	@Override
	public void doLmsBatch0012() {

		this.getJdbc()
				.update("INSERT INTO LN.LNF175 SELECT (CASE LNF175_BR_NO WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE LNF175_BR_NO END),LNF175_UNIT_CODE,LNF175_YEAR,LNF175_CLASS_CD,LNF175_CONTROL_CD,LNF175_SEQ_NO FROM LN.LNF175 WHERE LNF175_BR_NO IN ('Z01','Z03') AND LNF175_YEAR IN (17,18)",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELCSECNT SET  BRNO = (CASE BRNO WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE BRNO END) WHERE BRNO IN ('Z01','Z03')",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELCSECNT SET  CNTRNO = (CASE LEFT(CNTRNO,3)  WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE LEFT(CNTRNO,3) END) || RIGHT(CNTRNO,9) WHERE LEFT(CNTRNO,3) IN ('Z01','Z03')",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELLNSEEK SET  BRNO = (CASE BRNO WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE BRNO END) WHERE BRNO IN ('Z01','Z03')",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELLNSEEK SET  CNTRNO = (CASE LEFT(CNTRNO,3)  WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE LEFT(CNTRNO,3) END) || RIGHT(CNTRNO,9) WHERE LEFT(CNTRNO,3) IN ('Z01','Z03')",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF447 SET ELF447_CHKBRANCH = (CASE ELF447_CHKBRANCH WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE ELF447_CHKBRANCH END) WHERE ELF447_CHKBRANCH IN ('Z01','Z03')",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF447 SET ELF447_BRANCH = (CASE ELF447_BRANCH WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE ELF447_BRANCH END) WHERE ELF447_BRANCH IN ('Z01','Z03')",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF447 SET ELF447_CONTRACT = (CASE LEFT(ELF447_CONTRACT,3)  WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE LEFT(ELF447_CONTRACT,3) END) || RIGHT(ELF447_CONTRACT,9) WHERE LEFT(ELF447_CONTRACT,3) IN ('Z01','Z03')",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF447N SET ELF447N_BRANCH = (CASE ELF447N_BRANCH WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE ELF447N_BRANCH END) WHERE ELF447N_BRANCH IN ('Z01','Z03')",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF447N SET ELF447N_PROCESS_BR = (CASE ELF447N_PROCESS_BR WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE ELF447N_PROCESS_BR END) WHERE ELF447N_PROCESS_BR IN ('Z01','Z03')",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF447N SET ELF447N_REVIEWBR = (CASE ELF447N_REVIEWBR WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE ELF447N_REVIEWBR END) WHERE ELF447N_REVIEWBR IN ('Z01','Z03')",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF447N SET ELF447N_CONTRACT = (CASE LEFT(ELF447N_CONTRACT,3)  WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE LEFT(ELF447N_CONTRACT,3) END) || RIGHT(ELF447N_CONTRACT,9) WHERE LEFT(ELF447N_CONTRACT,3) IN ('Z01','Z03')",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF442 SET ELF442_BRANCH = (CASE ELF442_BRANCH WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE ELF442_BRANCH END) WHERE ELF442_BRANCH IN ('Z01','Z03')",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF442 SET ELF442_CNTRNO = (CASE LEFT(ELF442_CNTRNO,3)  WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE LEFT(ELF442_CNTRNO,3) END) || RIGHT(ELF442_CNTRNO,9) WHERE LEFT(ELF442_CNTRNO,3) IN ('Z01','Z03')",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.LNUNID SET REGBR = (CASE REGBR WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE REGBR END) WHERE REGBR IN ('Z01','Z03') ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF506 SET ELF506_CNTRNO = (CASE LEFT(ELF506_CNTRNO,3)  WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE LEFT(ELF506_CNTRNO,3) END) || RIGHT(ELF506_CNTRNO,9) WHERE LEFT(ELF506_CNTRNO,3) IN ('Z01','Z03')",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.IQUOTAPP SET QUOTANO = (CASE LEFT(QUOTANO,3)  WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE LEFT(QUOTANO,3) END) || RIGHT(QUOTANO,9) WHERE LEFT(QUOTANO,3) IN ('Z01','Z03') ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.IQUOTGUR SET QUOTANO = (CASE LEFT(QUOTANO,3)  WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE LEFT(QUOTANO,3) END) || RIGHT(QUOTANO,9) WHERE LEFT(QUOTANO,3) IN ('Z01','Z03') ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.IQUOTJON SET QUOTANO = (CASE LEFT(QUOTANO,3)  WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE LEFT(QUOTANO,3) END) || RIGHT(QUOTANO,9) WHERE LEFT(QUOTANO,3) IN ('Z01','Z03') ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.QUOTAINF SET BRANCH = (CASE BRANCH WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE BRANCH END) WHERE BRANCH IN ('Z01','Z03')    ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.QUOTAINF SET CNTRNO = (CASE LEFT(CNTRNO,3)  WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE LEFT(CNTRNO,3) END) || RIGHT(CNTRNO,9) WHERE LEFT(CNTRNO,3) IN ('Z01','Z03') ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.QUOTUNIO SET CNTRNO = (CASE LEFT(CNTRNO,3)  WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE LEFT(CNTRNO,3) END) || RIGHT(CNTRNO,9) WHERE LEFT(CNTRNO,3) IN ('Z01','Z03') ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.QUOTAPPR SET CNTRNO = (CASE LEFT(CNTRNO,3)  WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE LEFT(CNTRNO,3) END) || RIGHT(CNTRNO,9) WHERE LEFT(CNTRNO,3) IN ('Z01','Z03')   ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.QUOTAPPR SET CRDTBR = (CASE CRDTBR WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE CRDTBR END) WHERE CRDTBR IN ('Z01','Z03')    ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.QUOTAPPR SET MOWBR  = (CASE MOWBR WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE MOWBR END) WHERE MOWBR IN ('Z01','Z03')    ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.QUOTSUB  SET CNTRNO = (CASE LEFT(CNTRNO,3)  WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE LEFT(CNTRNO,3) END) || RIGHT(CNTRNO,9) WHERE LEFT(CNTRNO,3) IN ('Z01','Z03')   ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF517   SET ELF517_CONTRACT = (CASE LEFT(ELF517_CONTRACT,3)  WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE LEFT(ELF517_CONTRACT,3) END) || RIGHT(ELF517_CONTRACT,9) WHERE LEFT(ELF517_CONTRACT,3) IN ('Z01','Z03')   ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELLNGTEE SET BRNO = (CASE BRNO WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE BRNO END) WHERE BRNO IN ('Z01','Z03')   ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELLNGTEE SET CNTRNO = (CASE LEFT(CNTRNO,3)  WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE LEFT(CNTRNO,3) END) || RIGHT(CNTRNO,9) WHERE LEFT(CNTRNO,3) IN ('Z01','Z03')   ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELCRTBL  SET CNTRNO = (CASE LEFT(CNTRNO,3)  WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE LEFT(CNTRNO,3) END) || RIGHT(CNTRNO,9) WHERE LEFT(CNTRNO,3) IN ('Z01','Z03')   ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE LN.LNF164 SET LNF164_BR_NO = (CASE LNF164_BR_NO WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE LNF164_BR_NO END) WHERE LNF164_BR_NO IN ('Z01','Z03')    ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE LN.LNF164 SET LNF164_CONTRACT = (CASE LEFT(LNF164_CONTRACT,3)  WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE LEFT(LNF164_CONTRACT,3) END) || RIGHT(LNF164_CONTRACT,9) WHERE LEFT(LNF164_CONTRACT,3) IN ('Z01','Z03')",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE LN.LNFE0851 SET LNFE0851_MDBRNO = (CASE LNFE0851_MDBRNO WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE LNFE0851_MDBRNO END) WHERE LNFE0851_MDBRNO IN ('Z01','Z03')   ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE LN.LNFE0854 SET LNFE0854_MDBRNO = (CASE LNFE0854_MDBRNO WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE LNFE0854_MDBRNO END) WHERE LNFE0854_MDBRNO IN ('Z01','Z03')   ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE LN.LNFE0855 SET LNFE0855_MDBRNO = (CASE LNFE0855_MDBRNO WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE LNFE0855_MDBRNO END) WHERE LNFE0855_MDBRNO IN ('Z01','Z03')   ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE LN.LNFE0856 SET LNFE0856_MDBRNO = (CASE LNFE0856_MDBRNO WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE LNFE0856_MDBRNO END) WHERE LNFE0856_MDBRNO IN ('Z01','Z03')   ",
						new Object[] {});

		// *********************************************

		// 利害關係人
		this.getJdbc()
				.update("UPDATE MIS.ELREMAIN SET BRNO=CASE BRNO WHEN 'Z03' THEN '0D8' ELSE '0D7' END  where BRNO IN ('Z01','0E1','Z03')",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELREMAIN SET MDBRNO=CASE MDBRNO WHEN 'Z03' THEN '0D8' ELSE '0D7' END  where MDBRNO IN ('Z01','0E1','Z03')",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELREMAHT SET BRNO=CASE BRNO WHEN 'Z03' THEN '0D8' ELSE '0D7' END  where MDBRNO IN ('Z01','0E1','Z03')",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELREMAHT SET MDBRNO=CASE MDBRNO WHEN 'Z03' THEN '0D8' ELSE '0D7' END  where MDBRNO IN ('Z01','0E1','Z03')",
						new Object[] {});
		// 有利害關係人
		this.getJdbc()
				.update("UPDATE MIS.ELRESECD SET MDBRNO=CASE MDBRNO WHEN 'Z03' THEN '0D8' ELSE '0D7' END where MDBRNO IN ('Z01','0E1','Z03')",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELRECDHT SET MDBRNO=CASE MDBRNO WHEN 'Z03' THEN '0D8' ELSE '0D7' END where MDBRNO IN ('Z01','0E1','Z03')",
						new Object[] {});
		// 有利害關係人之企業
		this.getJdbc()
				.update("UPDATE MIS.ELRESCOM SET MDBRNO=CASE MDBRNO WHEN 'Z03' THEN '0D8' ELSE '0D7' END where MDBRNO IN ('Z01','0E1','Z03')",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELRECOHT SET MDBRNO=CASE MDBRNO WHEN 'Z03' THEN '0D8' ELSE '0D7' END where MDBRNO IN ('Z01','0E1','Z03')",
						new Object[] {});
		// 同一關係人
		this.getJdbc()
				.update("UPDATE MIS.ELCREPER SET BRNO=CASE BRNO WHEN 'Z03' THEN '0D8' ELSE '0D7' END where BRNO IN ('Z01','0E1','Z03')",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELCREPER SET LSTBRNO=CASE LSTBRNO WHEN 'Z03' THEN '0D8' ELSE '0D7' END where LSTBRNO  IN ('Z01','0E1','Z03')",
						new Object[] {});
		// 同一關係企業
		this.getJdbc()
				.update("UPDATE MIS.ELCRECOM SET BRNO=CASE BRNO WHEN 'Z03' THEN '0D8' ELSE '0D7' END where BRNO  IN ('Z01','0E1','Z03')",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELCRECOM SET LSTBRNO=CASE LSTBRNO WHEN 'Z03' THEN '0D8' ELSE '0D7' END where LSTBRNO  IN ('Z01','0E1','Z03')",
						new Object[] {});
		// 金管會AI370
		this.getJdbc()
				.update("UPDATE MIS.ELF441 SET ELF441_BR_NO=CASE ELF441_BR_NO WHEN 'Z03' THEN '0D8' ELSE '0D7' END where ELF441_BR_NO IN('Z01','0E1','Z03')",
						new Object[] {});
		// 評等
		this.getJdbc()
				.update("UPDATE MIS.ELF338N SET ELF338N_CRDTBR=CASE ELF338N_CRDTBR WHEN 'Z03' THEN '0D8' ELSE '0D7' END where ELF338N_CRDTBR IN ('Z01','0E1','Z03')",
						new Object[] {});
		// 財報
		this.getJdbc()
				.update("UPDATE MIS.FINTBL SET BRNO=CASE BRNO WHEN 'Z03' THEN '0D8' ELSE '0D7' END  WHERE BRNO IN ('Z01','0E1','Z03')",
						new Object[] {});
		// 集團
		this.getJdbc()
				.update("UPDATE MIS.GRPCMP SET BRANCH=CASE BRANCH WHEN 'Z03' THEN '0D8' ELSE '0D7' END where BRANCH IN ('Z01','0E1','Z03')",
						new Object[] {});
		// 徵信
		this.getJdbc()
				.update("UPDATE MIS.ELF445 SET ELF445_UNIT=CASE ELF445_UNIT WHEN 'Z03' THEN '0D8' ELSE '0D7' END where ELF445_UNIT IN ('Z01','0E1','Z03')",
						new Object[] {});

	}

	// J-106-0232-001 Web e-Loan國內、海外企金授信衍生性金融商品額度明細表新增淨值與額外信用增強
	@Override
	public Map<String, Object> findLNF197AllBycntrNoAndCustId(String custId,
			String dupNo, String cntrNo) {

		String fullCustKey = Util.getLeftStr(custId + "          ", 10) + dupNo;

		return this.getJdbc().queryForMap("LNF197.selAllByContractAndCustId",
				new Object[] { cntrNo, fullCustKey });

	}

	@Override
	public List<Map<String, Object>> gen_J_106_0266_RPT() {
		return this.getJdbc().queryForListWithMax("J_106_0266_sql",
				new String[] {});
	}

	@Override
	public List<Map<String, Object>> gen_J_107_0046_parent(String issuebrno,
			String custId, String grpCntrNo, String mGrpCntrNo,
			String p_dataYM_X, String p_dataYM_Y) {
		String param_issuebrno = issuebrno + "%";
		String param_custId = custId + "%";
		String param_grpCntrNo = grpCntrNo + "%";
		String param_mgrpcntrno = mGrpCntrNo + "%";

		return this.getJdbc().queryForListWithMax(
				"J_107_0046_sql_parent",
				new String[] { param_issuebrno, param_custId, param_grpCntrNo,
						param_mgrpcntrno, p_dataYM_X, p_dataYM_Y });
	}

	@Override
	public List<Map<String, Object>> gen_J_107_0046_child(String issuebrno,
			String custId, String grpCntrNo, String mGrpCntrNo,
			String p_dataYM_X, String p_dataYM_Y, String c_dataYM_X,
			String c_dataYM_Y, String lnf155_data_ym) {
		String param_issuebrno = issuebrno + "%";
		String param_custId = custId + "%";
		String param_grpCntrNo = grpCntrNo + "%";
		String param_mgrpcntrno = mGrpCntrNo + "%";

		return this.getJdbc().queryForListWithMax(
				"J_107_0046_sql_child",
				new String[] { lnf155_data_ym, param_issuebrno, param_custId,
						param_grpCntrNo, param_mgrpcntrno, p_dataYM_X,
						p_dataYM_Y, c_dataYM_X, c_dataYM_Y });
	}

	@Override
	public List<Map<String, Object>> gen_J_107_0046_childfor_factType3031(
			String issuebrno, String custId, String grpCntrNo,
			String mGrpCntrNo, String p_dataYM) {
		String param_issuebrno = issuebrno + "%";
		String param_custId = custId + "%";
		String param_grpCntrNo = grpCntrNo + "%";
		String param_mgrpcntrno = mGrpCntrNo + "%";

		return this.getJdbc().queryForListWithMax(
				"J_107_0046_sql_child_for_factType3031",
				new String[] { param_issuebrno, param_custId, param_grpCntrNo,
						param_mgrpcntrno, p_dataYM, p_dataYM, p_dataYM,
						p_dataYM });

	}

	// J-106-0278-002 Web e-Loan國內企金授信配合實地覆審作業，額度簽報明細表增加聯貸案件管理行之建檔及修改實地覆審相關檢核
	@Override
	public List<Map<String, Object>> gfnCTL_Get_SYND_CTLBR_A(String branch,
			String custId, String dupNo) {
		String custIdDupNo = Util.addSpaceWithValue(custId, 10) + dupNo;
		return getJdbc().queryForListWithMax(
				"LN020.gfnCTL_Chk_SYND_CTLBR_CTLTYPE_A",
				new String[] { custIdDupNo, branch, branch });
	}

	// J-106-0278-002 Web e-Loan國內企金授信配合實地覆審作業，額度簽報明細表增加聯貸案件管理行之建檔及修改實地覆審相關檢核
	@Override
	public List<Map<String, Object>> gfnCTL_Get_SYND_CTLBR_B(String branch,
			String custId, String dupNo) {
		String custIdDupNo = Util.addSpaceWithValue(custId, 10) + dupNo;
		return getJdbc().queryForListWithMax(
				"LN020.gfnCTL_Chk_SYND_CTLBR_CTLTYPE_B",
				new String[] { custIdDupNo, branch, branch });
	}

	// J-106-0246-002 Web e-Loan授信系統企金額度明細表產品種類為G1政府機構低利優惠放款時，覆核時通知授信行銷處。
	@Override
	public Map<String, Object> findProdClassIsG1() {
		return this.getJdbc().queryForMap("selProdClassIsG1", new String[] {});
	}

	@Override
	public Map<String, Object> findEscrowDataByLcNo(String lnf034_lc_no,
			String brNo) {
		return getJdbc().queryForMap("MIS.LNF034_LC_NO_escrowData",
				new String[] { lnf034_lc_no, brNo, brNo + "%" });
	}

	/**
	 * G-107-0115-001 Web e-Loan 授信系統配合巴箇行整併調整簽報書相關資料
	 * 
	 */
	@NonTransactional
	@Override
	public void doLmsBatch0013() {

		this.getJdbc().update(
				"UPDATE MIS.ELCSECNT SET  BRNO = '0A5' WHERE BRNO IN ('0A6') ",
				new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELCSECNT SET  CNTRNO = '0A5'||SUBSTR(CNTRNO,4,4)||( CASE SUBSTR(CNTRNO,8,1) WHEN '0' THEN '3' WHEN '5' THEN '8' ELSE SUBSTR(CNTRNO,8,1) END)||SUBSTR(CNTRNO,9,4) WHERE LEFT(CNTRNO,3) IN ('0A6') AND SUBSTR(CNTRNO,8,1) IN ('0','5') AND CNTRNO IS NOT NULL  ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELLNSEEK SET  BRNO = '0A5' WHERE BRNO IN ('0A6')   ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELLNSEEK SET  CNTRNO = '0A5'||SUBSTR(CNTRNO,4,4)||( CASE SUBSTR(CNTRNO,8,1) WHEN '0' THEN '3' WHEN '5' THEN '8' ELSE SUBSTR(CNTRNO,8,1) END)||SUBSTR(CNTRNO,9,4) WHERE LEFT(CNTRNO,3) IN ('0A6') AND SUBSTR(CNTRNO,8,1) IN ('0','5') AND CNTRNO IS NOT NULL  ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF447 SET ELF447_CHKBRANCH = '0A5' WHERE ELF447_CHKBRANCH IN ('0A6') ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF447 SET ELF447_BRANCH = '0A5' WHERE ELF447_BRANCH IN ('0A6') ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF447 SET ELF447_CONTRACT = '0A5'||SUBSTR(ELF447_CONTRACT,4,4)||( CASE SUBSTR(ELF447_CONTRACT,8,1) WHEN '0' THEN '3' WHEN '5' THEN '8' ELSE SUBSTR(ELF447_CONTRACT,8,1) END)||SUBSTR(ELF447_CONTRACT,9,4) WHERE LEFT(ELF447_CONTRACT,3) IN ('0A6') AND SUBSTR(ELF447_CONTRACT,8,1) IN ('0','5') AND ELF447_CONTRACT IS NOT NULL  ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF447N SET ELF447N_BRANCH = '0A5' WHERE ELF447N_BRANCH IN ('0A6') ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF447N SET ELF447N_PROCESS_BR = '0A5' WHERE ELF447N_PROCESS_BR IN ('0A6') ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF447N SET ELF447N_REVIEWBR = '0A5' WHERE ELF447N_REVIEWBR IN ('0A6') ",
						new Object[] {});

		this.getJdbc()
				.update("UPDATE MIS.ELF447N SET ELF447N_CONTRACT = '0A5'||SUBSTR(ELF447N_CONTRACT,4,4)||( CASE SUBSTR(ELF447N_CONTRACT,8,1) WHEN '0' THEN '3' WHEN '5' THEN '8' ELSE SUBSTR(ELF447N_CONTRACT,8,1) END)||SUBSTR(ELF447N_CONTRACT,9,4) WHERE LEFT(ELF447N_CONTRACT,3) IN ('0A6') AND SUBSTR(ELF447N_CONTRACT,8,1) IN ('0','5') AND ELF447N_CONTRACT IS NOT NULL  ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF442 SET ELF442_BRANCH = '0A5' WHERE ELF442_BRANCH IN ('0A6') ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF442 SET ELF442_CNTRNO = '0A5'||SUBSTR(ELF442_CNTRNO,4,4)||( CASE SUBSTR(ELF442_CNTRNO,8,1) WHEN '0' THEN '3' WHEN '5' THEN '8' ELSE SUBSTR(ELF442_CNTRNO,8,1) END)||SUBSTR(ELF442_CNTRNO,9,4) WHERE LEFT(ELF442_CNTRNO,3) IN ('0A6') AND SUBSTR(ELF442_CNTRNO,8,1) IN ('0','5') AND ELF442_CNTRNO IS NOT NULL  ",
						new Object[] {});
		this.getJdbc().update(
				"UPDATE MIS.LNUNID SET REGBR = '0A5' WHERE REGBR IN ('0A6') ",
				new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF506 SET ELF506_CNTRNO = '0A5'||SUBSTR(ELF506_CNTRNO,4,4)||( CASE SUBSTR(ELF506_CNTRNO,8,1) WHEN '0' THEN '3' WHEN '5' THEN '8' ELSE SUBSTR(ELF506_CNTRNO,8,1) END)||SUBSTR(ELF506_CNTRNO,9,4) WHERE LEFT(ELF506_CNTRNO,3) IN ('0A6') AND SUBSTR(ELF506_CNTRNO,8,1) IN ('0','5') AND ELF506_CNTRNO IS NOT NULL  ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.IQUOTAPP SET QUOTANO = '0A5'||SUBSTR(QUOTANO,4,4)||( CASE SUBSTR(QUOTANO,8,1) WHEN '0' THEN '3' WHEN '5' THEN '8' ELSE SUBSTR(QUOTANO,8,1) END)||SUBSTR(QUOTANO,9,4) WHERE LEFT(QUOTANO,3) IN ('0A6') AND SUBSTR(QUOTANO,8,1) IN ('0','5') AND QUOTANO IS NOT NULL  ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.IQUOTGUR SET QUOTANO = '0A5'||SUBSTR(QUOTANO,4,4)||( CASE SUBSTR(QUOTANO,8,1) WHEN '0' THEN '3' WHEN '5' THEN '8' ELSE SUBSTR(QUOTANO,8,1) END)||SUBSTR(QUOTANO,9,4) WHERE LEFT(QUOTANO,3) IN ('0A6') AND SUBSTR(QUOTANO,8,1) IN ('0','5') AND QUOTANO IS NOT NULL  ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.IQUOTJON SET QUOTANO = '0A5'||SUBSTR(QUOTANO,4,4)||( CASE SUBSTR(QUOTANO,8,1) WHEN '0' THEN '3' WHEN '5' THEN '8' ELSE SUBSTR(QUOTANO,8,1) END)||SUBSTR(QUOTANO,9,4) WHERE LEFT(QUOTANO,3) IN ('0A6') AND SUBSTR(QUOTANO,8,1) IN ('0','5') AND QUOTANO IS NOT NULL  ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.QUOTAINF SET BRANCH = '0A5' WHERE BRANCH IN ('0A6')    ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.QUOTAINF SET CNTRNO = '0A5'||SUBSTR(CNTRNO,4,4)||( CASE SUBSTR(CNTRNO,8,1) WHEN '0' THEN '3' WHEN '5' THEN '8' ELSE SUBSTR(CNTRNO,8,1) END)||SUBSTR(CNTRNO,9,4) WHERE LEFT(CNTRNO,3) IN ('0A6') AND SUBSTR(CNTRNO,8,1) IN ('0','5') AND CNTRNO IS NOT NULL  ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.QUOTUNIO SET CNTRNO = '0A5'||SUBSTR(CNTRNO,4,4)||( CASE SUBSTR(CNTRNO,8,1) WHEN '0' THEN '3' WHEN '5' THEN '8' ELSE SUBSTR(CNTRNO,8,1) END)||SUBSTR(CNTRNO,9,4) WHERE LEFT(CNTRNO,3) IN ('0A6') AND SUBSTR(CNTRNO,8,1) IN ('0','5') AND CNTRNO IS NOT NULL  ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.QUOTAPPR SET CNTRNO = '0A5'||SUBSTR(CNTRNO,4,4)||( CASE SUBSTR(CNTRNO,8,1) WHEN '0' THEN '3' WHEN '5' THEN '8' ELSE SUBSTR(CNTRNO,8,1) END)||SUBSTR(CNTRNO,9,4) WHERE LEFT(CNTRNO,3) IN ('0A6') AND SUBSTR(CNTRNO,8,1) IN ('0','5') AND CNTRNO IS NOT NULL  ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.QUOTAPPR SET CRDTBR = '0A5' WHERE CRDTBR IN ('0A6')    ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.QUOTAPPR SET MOWBR  = '0A5' WHERE MOWBR IN ('0A6')    ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.QUOTSUB  SET CNTRNO = '0A5'||SUBSTR(CNTRNO,4,4)||( CASE SUBSTR(CNTRNO,8,1) WHEN '0' THEN '3' WHEN '5' THEN '8' ELSE SUBSTR(CNTRNO,8,1) END)||SUBSTR(CNTRNO,9,4) WHERE LEFT(CNTRNO,3) IN ('0A6') AND SUBSTR(CNTRNO,8,1) IN ('0','5') AND CNTRNO IS NOT NULL  ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF517   SET ELF517_CONTRACT = '0A5'||SUBSTR(ELF517_CONTRACT,4,4)||( CASE SUBSTR(ELF517_CONTRACT,8,1) WHEN '0' THEN '3' WHEN '5' THEN '8' ELSE SUBSTR(ELF517_CONTRACT,8,1) END)||SUBSTR(ELF517_CONTRACT,9,4) WHERE LEFT(ELF517_CONTRACT,3) IN ('0A6') AND SUBSTR(ELF517_CONTRACT,8,1) IN ('0','5') AND ELF517_CONTRACT IS NOT NULL  ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELLNGTEE SET BRNO = '0A5' WHERE BRNO IN ('0A6')   ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELLNGTEE SET CNTRNO = '0A5'||SUBSTR(CNTRNO,4,4)||( CASE SUBSTR(CNTRNO,8,1) WHEN '0' THEN '3' WHEN '5' THEN '8' ELSE SUBSTR(CNTRNO,8,1) END)||SUBSTR(CNTRNO,9,4) WHERE LEFT(CNTRNO,3) IN ('0A6') AND SUBSTR(CNTRNO,8,1) IN ('0','5') AND CNTRNO IS NOT NULL  ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELCRTBL  SET CNTRNO = '0A5'||SUBSTR(CNTRNO,4,4)||( CASE SUBSTR(CNTRNO,8,1) WHEN '0' THEN '3' WHEN '5' THEN '8' ELSE SUBSTR(CNTRNO,8,1) END)||SUBSTR(CNTRNO,9,4) WHERE LEFT(CNTRNO,3) IN ('0A6') AND SUBSTR(CNTRNO,8,1) IN ('0','5') AND CNTRNO IS NOT NULL  ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE LN.LNF164 SET LNF164_BR_NO = '0A5' WHERE LNF164_BR_NO IN ('0A6')    ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE LN.LNF164 SET LNF164_CONTRACT = '0A5'||SUBSTR(LNF164_CONTRACT,4,4)||( CASE SUBSTR(LNF164_CONTRACT,8,1) WHEN '0' THEN '3' WHEN '5' THEN '8' ELSE SUBSTR(LNF164_CONTRACT,8,1) END)||SUBSTR(LNF164_CONTRACT,9,4) WHERE LEFT(LNF164_CONTRACT,3) IN ('0A6') AND SUBSTR(LNF164_CONTRACT,8,1) IN ('0','5') AND LNF164_CONTRACT IS NOT NULL  ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE LN.LNFE0851 SET LNFE0851_MDBRNO = '0A5' WHERE LNFE0851_MDBRNO IN ('0A6')   ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE LN.LNFE0854 SET LNFE0854_MDBRNO = '0A5' WHERE LNFE0854_MDBRNO IN ('0A6')   ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE LN.LNFE0855 SET LNFE0855_MDBRNO = '0A5' WHERE LNFE0855_MDBRNO IN ('0A6')   ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE LN.LNFE0856 SET LNFE0856_MDBRNO = '0A5' WHERE LNFE0856_MDBRNO IN ('0A6') ",
						new Object[] {});

		// *********************************************

		// 利害關係人
		this.getJdbc().update(
				"UPDATE MIS.ELREMAIN SET BRNO='0A5'  where BRNO = '0A6'",
				new Object[] {});
		this.getJdbc().update(
				"UPDATE MIS.ELREMAIN SET MDBRNO='0A5'  where MDBRNO = '0A6'",
				new Object[] {});
		this.getJdbc().update(
				"UPDATE MIS.ELREMAHT SET BRNO='0A5'  where MDBRNO = '0A6'",
				new Object[] {});
		this.getJdbc().update(
				"UPDATE MIS.ELREMAHT SET MDBRNO='0A5'  where MDBRNO = '0A6'",
				new Object[] {});
		// 有利害關係人
		this.getJdbc().update(
				"UPDATE MIS.ELRESECD SET MDBRNO='0A5' where MDBRNO = '0A6'",
				new Object[] {});
		this.getJdbc().update(
				"UPDATE MIS.ELRECDHT SET MDBRNO='0A5' where MDBRNO = '0A6'",
				new Object[] {});
		// 有利害關係人之企業
		this.getJdbc().update(
				"UPDATE MIS.ELRESCOM SET MDBRNO='0A5' where MDBRNO = '0A6'",
				new Object[] {});
		this.getJdbc().update(
				"UPDATE MIS.ELRECOHT SET MDBRNO='0A5' where MDBRNO = '0A6'",
				new Object[] {});
		// 同一關係人
		this.getJdbc().update(
				"UPDATE MIS.ELCREPER SET BRNO='0A5' where BRNO = '0A6'",
				new Object[] {});
		this.getJdbc().update(
				"UPDATE MIS.ELCREPER SET LSTBRNO='0A5' where LSTBRNO ='0A6'",
				new Object[] {});
		// 同一關係企業
		this.getJdbc().update(
				"UPDATE MIS.ELCRECOM SET BRNO='0A5' where BRNO ='0A6'",
				new Object[] {});
		this.getJdbc().update(
				"UPDATE MIS.ELCRECOM SET LSTBRNO='0A5' where LSTBRNO ='0A6'",
				new Object[] {});
		// 金管會AI370
		this.getJdbc()
				.update("UPDATE MIS.ELF441 SET ELF441_BR_NO='0A5' where ELF441_BR_NO ='0A6'",
						new Object[] {});
		// 評等
		this.getJdbc()
				.update("UPDATE MIS.ELF338N SET ELF338N_CRDTBR='0A5' where ELF338N_CRDTBR='0A6'",
						new Object[] {});
		// 財報
		this.getJdbc().update(
				"UPDATE MIS.FINTBL SET BRNO='0A5'  WHERE BRNO = '0A6'",
				new Object[] {});
		// 集團
		this.getJdbc().update(
				"UPDATE MIS.GRPCMP SET BRANCH='0A5' where BRANCH='0A6'",
				new Object[] {});
		// 徵信
		this.getJdbc()
				.update("UPDATE MIS.ELF445 SET ELF445_UNIT='0A5' where ELF445_UNIT='0A6'",
						new Object[] {});

	}

	/**
	 * J-107-0070-001 Web e-Loan 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
	 */
	@Override
	public List<Map<String, Object>> findCMFLUNSRByCustId(String custId,
			String dupNo) {
		return this.getJdbc().queryForListWithMax("CMFLUNSR.selByCustId",
				new Object[] { custId, dupNo });
	}

	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findLnf02pByCustIdForAppendixA(
			String custId, String dupNo) {
		String fullCustKey = Util.getLeftStr(custId + "          ", 10) + dupNo;
		return this.getJdbc().queryForListWithMax(
				"LNF02P.selByCustIdForAppendixA", new Object[] { fullCustKey });
	}

	@Override
	public List<Map<String, Object>> findLnf02mByContract(String contract) {
		return this.getJdbc().queryForListWithMax(
				"LNF02M.findLnf02mByContract", new Object[] { contract });
	}
	
	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findLnf02pByBuyerIdForAppendixB(
			String custId, String dupNo) {
		String fullCustKey = Util.getLeftStr(custId + "          ", 10) + dupNo;
		return this.getJdbc()
				.queryForListWithMax("LNF02P.selByBuyerIdForAppendixB",
						new Object[] { fullCustKey });
	}

	/**
	 * J-107-0184_05097_B1001 Web e-loan企金授信簽報時提供以借款人查詢應簽報的額度明細表及該客戶所有的往來分行(
	 * 包含前次簽報書所簽報額度明細表所屬分行及現有有效額度的往來分行)等資訊,並於送呈前進行差異比對, 就存在差異時提供警示訊息,
	 * 以避免錯選授信案件授權層級情事。 取得客戶項下a-Loan所有額務序號(企金用)
	 */
	@Override
	public List<Map<String, Object>> findAloanCntrnoByCntrnoIgnoreCancel(
			String cntrNo) {
		return this.getJdbc().queryForList(
				"MIS.queryCustAloanCntrnoIgnoreCancel",
				new Object[] { cntrNo, cntrNo });
	}

	/**
	 * J-107-0192_05097_B1001 針對企金異常通報戶之覆審，增加覆審名單之控管措施與報表。
	 */
	@Override
	public List<Map<String, Object>> findLnfe0854UnClosedByCustIdAndBrNo(
			String custId, String dupNo, String brNo) {
		return this.getJdbc().queryForListWithMax(
				"LNFE0854.selUnClosedByCustIdAndBrNo",
				new Object[] { custId, dupNo, brNo });
	}

	/**
	 * J-107-0192_05097_B1001 針對企金異常通報戶之覆審，增加覆審名單之控管措施與報表。
	 */
	@Override
	public Map<String, Object> findLnfe0854ByUnid(String mainId) {

		return this.getJdbc().queryForMap("LNFE0854.selByUnid",
				new Object[] { mainId });

	}

	/**
	 * J-107-0192_05097_B1001 針對企金異常通報戶之覆審，增加覆審名單之控管措施與報表。
	 */
	@Override
	public Map<String, Object> findMaxLrDateByBranchAndCustId(String custId,
			String dupNo, String brNo) {
		return this.getJdbc().queryForMap(
				"ELF412.selMaxLrDateByBranchAndCustId",
				new Object[] { brNo, custId, dupNo, brNo, custId, dupNo });
	}

	/**
	 * J-107-0263-001 Web e-Loan 授信系統配合國金部企金覆審清單檢核機制修改資料
	 */
	@NonTransactional
	@Override
	public void doLmsBatch0014() {
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='5' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='AEZ0001319'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='5' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='AEZ0001599'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='4' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='AEZ0001850'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='1' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='AUZ0015411'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='5' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='AUZ0017234'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='1' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='AUZ0020667'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='1' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='AUZ0031670'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='1' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='AUZ0032642'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='1' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='AUZ0033213'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='1' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='AUZ0036210'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='1' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='AUZ0036513'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='1' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='AUZ0037225'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='1' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='AUZ0037506'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='1' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='AUZ0038287'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='1' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='AUZ0039069'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='1' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='AUZ0039259'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='1' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='AUZ0039808'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='1' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='AUZ0041419'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='1' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='AUZ0041591'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='1' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='AUZ0042352'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='1' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='AUZ0043155'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='5' , ELF412_LRDATE='2018-09-19' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='BMZ0000677'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='5' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='BZZ0005125'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='5' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='CLZ0000206'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='5' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='CNZ0009038'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='5' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='CNZ0009430'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='1' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='DEZ0005167'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='1' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='DEZ0005217'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='5' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='EGZ0000064'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='5' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='ESZ0000851'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='1' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='FRZ0019036'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='1' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='FRZ0019107'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='1' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='FRZ0020272'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='5' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='GBZ0019438'",
						new Object[] {});
		// this.getJdbc().update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='4' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='HNZ0000043'",
		// new Object[] {});//
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='5' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='IDZ0000466'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='5' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='IDZ0000748'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='5' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='INZ0000070'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='4' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='INZ0000249'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='1' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='INZ0000634'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='4' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='INZ0000753'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='5' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='INZ0001036'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='5' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='INZ0001556'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='1' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='INZ0003180'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='5' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='INZ0003371'",
						new Object[] {});
		// this.getJdbc().update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='4' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='KNZ0000965'",
		// new Object[] {});//
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='5' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='KRZ0000135'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='1' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='KYZ0008214'",
						new Object[] {});
		// this.getJdbc().update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='4' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='MKZ0000016'",
		// new Object[] {});//
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='1' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='MYZ0000254'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='1' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='MYZ0004701'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='5' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='MYZ0004717'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='1' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='NLZ0014258'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='5' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='OMZ0000114'",
						new Object[] {});
		// this.getJdbc().update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='4' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='PAZ0001417'",
		// new Object[] {});//
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='5' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='PAZ0039878'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='1' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='PAZ0044648'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='5' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='PAZ0046364'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='5' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='PAZ0046385'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='5' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='PHZ0012575'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='4' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='PWZ0000016'",
						new Object[] {});
		// this.getJdbc().update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='4' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='PWZ0000022'",
		// new Object[] {});//
		// this.getJdbc().update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='4' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='PWZ0000037'",
		// new Object[] {});//
		// this.getJdbc().update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='4' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='PYZ0000022'",
		// new Object[] {});//
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='5' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='QAZ0000199'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='4' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='QAZ0000233'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='5' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='QAZ0000281'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='5' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='QAZ0000326'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='5' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='SAZ0006118'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='1' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='SGZ0011745'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='1' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='SGZ0043514'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='1' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='SGZ0046224'",
						new Object[] {});
		// this.getJdbc().update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='4' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='TDZ0000016'",
		// new Object[] {});//
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='5' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='THZ0080453'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='1' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='TRZ0000058'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='1' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='TRZ0000395'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='1' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='TRZ0000557'",
						new Object[] {});
		// this.getJdbc().update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='4' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='VCZ0001676'",
		// new Object[] {});//
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='1' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='VGZ0085703'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='1' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='VGZ0112201'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='5' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='VNZ0028886'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='1' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='ZAZ0000347'",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.ELF412 SET ELF412_NCKDFLAG='1' , ELF412_LRDATE='2018-09-18' WHERE ELF412_BRANCH='025' AND ELF412_CUSTID ='ZAZ0000819'",
						new Object[] {});
	}

	/**
	 * J-107-0213_05097_B1001 Web
	 * e-Loan國內企金覆審當同一分行對同一公司同時有自貸案額度及非「任管理行與主辦行」之聯行參貸額度時
	 * ,該自貸額度,覆審主辦單位不得維護為免辦理覆審之案件
	 * 
	 * @param custId
	 * @param dupNo
	 * @param branch
	 * @return
	 */
	@Override
	public List<Map<String, Object>> gfnCTL_Import_LNF020_Without_SYND(
			String branch, String custId, String dupNo) {
		String custIdDupNo = Util.addSpaceWithValue(custId, 10) + dupNo;
		return getJdbc().queryForListWithMax(
				"LN020.gfnCTL_Import_LNF020_Without_SYND",
				new String[] { branch, custIdDupNo, branch, custIdDupNo,
						branch, custIdDupNo, branch, custId, dupNo, branch,
						custIdDupNo, branch, custIdDupNo, branch });
	}

	/**
	 * J-107-0225_05097_B1001 Web e-Loan企金授信簽報書新增集團關係企業與本行授信往來條件比較表
	 * 集團企業--產生集團／關係企業與本行授信往來條件比較表
	 * 
	 * @param grpId
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findLnf022ByGrpId(String grpId) {
		return getJdbc().queryForListWithMax("LNF022.selContractByGrpId",
				new String[] { grpId });
	}

	/**
	 * J-107-0225_05097_B1001 Web e-Loan企金授信簽報書新增集團關係企業與本行授信往來條件比較表
	 * 關係企業--產生集團／關係企業與本行授信往來條件比較表
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findLnf022ByByElcrecomCustId(
			String custId, String dupNo) {
		return getJdbc().queryForListWithMax(
				"LNF022.selContractByElcrecomCustId",
				new String[] { custId, dupNo, custId, dupNo });
	}

	/**
	 * LMS180R39 企金共同行銷報表
	 * 
	 * @return
	 */
	@Override
	public List<Map<String, Object>> queryLMS180R39Data() {
		return this.getJdbc().queryForListWithMax("rpt.LMS180R39",
				new Object[] {});// .queryAllForList("rpt.LMS180R11");
	}

	/**
	 * LMS180R40 簽報階段都更危老業務統計表
	 * 
	 * @return
	 */
	@Override
	public List<Map<String, Object>> queryLMS180R40Data() {
		return this.getJdbc().queryForListWithMax("rpt.LMS180R40",
				new Object[] {});
	}

	/**
	 * J-107-0337_05097_B1001 Web
	 * e-Loan企金授信簽報書借款人基本資料，調整銀行法利害關係人可列出銀行所指派法人代表之單位、職稱及姓名且可顯示多筆
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	@Override
	public List<Map<String, Object>> selRltLevel1(String custId, String dupNo) {
		return getJdbc().queryForListWithMax("MISELREMAIN.selRltLevel1",
				new String[] { custId, dupNo, custId, dupNo });
	}

	/**
	 * G-107-0642_05097_B1001 配合羅勇分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權
	 * 
	 * @param cntrNo
	 * @return
	 */
	@Override
	public Map<String, Object> findElf515ByCntrNoForBt(String cntrNo) {

		return this.getJdbc().queryForMap("MIS.ELF515.findByCntrNoForBt15",
				new String[] { cntrNo, cntrNo });
	}

	/**
	 * G-107-0642_05097_B1001 配合羅勇分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權
	 * 
	 * @param newCntrNo
	 * @param modifyUnit
	 * @param oldCntrNo
	 * @return
	 */
	@Override
	public void updateElf515CntrNoByCntrNoForBt(String newCntrNo,
			String oldCntrNo, String modifyUnit) {
		this.getJdbc().update(
				"ELF515.updateCntrNoByCntrNoForBt15",
				new Object[] { oldCntrNo, newCntrNo, oldCntrNo, newCntrNo,
						modifyUnit, oldCntrNo, oldCntrNo });
	}

	/**
	 * G-107-0642_05097_B1001 配合羅勇分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權
	 * 
	 * @param lnf07Akey1
	 * @param exDate
	 * @param fBranch
	 * @param tBranch
	 * @return
	 */
	@NonTransactional
	@Override
	public void doLmsBatch0015_3(String lnf07Akey1, String exDate,
			String fBranch, String tBranch) {
		try {
			this.getJdbc().update("LNFE0854.updateMdbrnoByCustIdForBt15",
					new Object[] { tBranch, fBranch, lnf07Akey1, exDate });
		} catch (Exception e) {
			System.out.println(e.toString());
			// 若無資料不處理
		}
		try {
			this.getJdbc().update("LNFE0855.updateMdbrnoByCustIdForBt15",
					new Object[] { tBranch, fBranch, lnf07Akey1, exDate });
		} catch (Exception e) {
			System.out.println(e.toString());
			// 若無資料不處理
		}
		try {
			this.getJdbc().update("LNFE0856.updateMdbrnoByCustIdForBt15",
					new Object[] { tBranch, fBranch, lnf07Akey1, exDate });
		} catch (Exception e) {
			System.out.println(e.toString());
			// 若無資料不處理
		}
		try {
			this.getJdbc()
					.update("ELLNGTEE.updateCntrNoByCustIdForBt15",
							new Object[] { tBranch, fBranch + "%", lnf07Akey1,
									exDate });
		} catch (Exception e) {
			System.out.println(e.toString());
			// 若無資料不處理
		}
		try {
			this.getJdbc()
					.update("ELF447N.updateCntrNoByCustIdForBt15",
							new Object[] { tBranch, fBranch + "%", lnf07Akey1,
									exDate });
		} catch (Exception e) {
			System.out.println(e.toString());
			// 若無資料不處理
		}
		try {
			this.getJdbc()
					.update("ELF447.updateCntrNoByCustIdForBt15",
							new Object[] { tBranch, fBranch + "%", lnf07Akey1,
									exDate });
		} catch (Exception e) {
			System.out.println(e.toString());
			// 若無資料不處理
		}

	}

	@Override
	public List<Map<String, Object>> get_ELF488_newCust(String elf488_data_date) {
		return this.getJdbc().queryForListWithMax("ELF488.selNewCust", null);
	}

	@Override
	public Map<String, Object> get_LNF320(String lnf320_query_date) {
		return this.getJdbc().queryForMap("LNF320.getQueryDate",
				new String[] { lnf320_query_date });
	}

	@Override
	public List<Map<String, Object>> get_ELF490B_Rule1(String brno_area,
			String beg_yyyyMMdd, String end_yyyyMMdd) {
		return this.getJdbc().queryForListWithMax(
				"ELF490B.Rule1",
				new String[] { StringUtils.substring(end_yyyyMMdd, 0, 7),
						beg_yyyyMMdd, beg_yyyyMMdd, brno_area });
	}

	@Override
	public List<Map<String, Object>> get_ELF490B_Rule2(String brno_area,
			String beg_yyyyMMdd, String end_yyyyMMdd) {
		String beg_yyyyMM = Util
				.trim(StringUtils.substring(beg_yyyyMMdd, 0, 7));
		String end_yyyyMM = Util
				.trim(StringUtils.substring(end_yyyyMMdd, 0, 7));
		return this.getJdbc().queryForListWithMax(
				"ELF490B.Rule2",
				new String[] { StringUtils.substring(end_yyyyMMdd, 0, 7),
						beg_yyyyMMdd, beg_yyyyMMdd, beg_yyyyMM, end_yyyyMM,
						brno_area });
	}

	@Override
	public List<Map<String, Object>> get_ELF490B_Rule3(String brno_area,
			String beg_yyyyMMdd, String end_yyyyMMdd) {
		return this.getJdbc().queryForListWithMax("ELF490B.Rule3",
				new String[] { beg_yyyyMMdd, end_yyyyMMdd, brno_area });
	}

	@Override
	public List<Map<String, Object>> get_ELF490B_Rule4(String brno_area,
			String beg_yyyyMMdd, String end_yyyyMMdd) {
		return this.getJdbc().queryForListWithMax("ELF490B.Rule4",
				new String[] { beg_yyyyMMdd, end_yyyyMMdd, brno_area });
	}

	@Override
	public List<Map<String, Object>> get_ELF490B_Rule4_empNo_detail(
			String empNo, String beg_yyyyMMdd, String end_yyyyMMdd) {
		return this.getJdbc().queryForListWithMax("ELF490B.Rule4_empNo_detail",
				new String[] { beg_yyyyMMdd, end_yyyyMMdd, empNo });
	}

	@Override
	public List<Map<String, Object>> get_ELF490B_Rule5(String brno_area,
			String beg_yyyyMMdd, String end_yyyyMMdd) {
		return this.getJdbc().queryForListWithMax("ELF490B.Rule5",
				new String[] { beg_yyyyMMdd, end_yyyyMMdd, brno_area });
	}

	@Override
	public List<Map<String, Object>> get_ELF490B_Rule6(String brno_area,
			String beg_yyyyMMdd, String end_yyyyMMdd) {
		return this.getJdbc().queryForListWithMax("ELF490B.Rule6",
				new String[] { beg_yyyyMMdd, end_yyyyMMdd, brno_area });
	}

	@Override
	public List<Map<String, Object>> get_ELF490B_findBrNoAndEmpNo(
			String beg_yyyyMMdd, String end_yyyyMMdd, String elf490b_dataYM) {
		return this.getJdbc().queryForListWithMax(
				"ELF490B.findBrNoAndEmpNo",
				new String[] { StringUtils.substring(end_yyyyMMdd, 0, 7),
						beg_yyyyMMdd, beg_yyyyMMdd, elf490b_dataYM });
	}

	@Override
	public List<Map<String, Object>> get_ELF491_notify_cntrNo_lack_loanNo(
			String cancel_beg_date) {
		return this.getJdbc().queryForListWithMax(
				"ELF491.notify_cntrNo_lack_loanNo",
				new String[] { cancel_beg_date });
	}

	@Override
	public List<Map<String, Object>> get_ELF491_notify_over_crDate(
			String cr_date) {
		return this.getJdbc().queryForListWithMax("ELF491.notify_over_crDate",
				new String[] { cr_date });
	}

	@Override
	public List<Map<String, Object>> get_ELF491_notify_lack_lnf09g(
			String beg_date, String end_date) {
		return this.getJdbc().queryForListWithMax("ELF491.notify_lack_lnf09g",
				new String[] { beg_date, end_date, beg_date, end_date });
	}

	@Override
	public List<Map<String, Object>> selDistinctCntrnoByCustidDupno(
			String custId, String dupNo) {
		String CUST_ID = custId + dupNo;
		return this.getJdbc().queryForList(
				"LNF.selDistinctCntrnoByCustidDupno",
				new Object[] { CUST_ID, CUST_ID });
	}

	/**
	 * J-107-0357_05097_B1002 Web e-Loan授信系統配合工業區及產業園區建廠優惠貸款專案，額度簽報新增「專案種類」與相關報表
	 */
	@NonTransactional
	@Override
	public void doLmsBatch0016_1() {
		this.getJdbc()
				.update("UPDATE MIS.QUOTAPPR  set PROJ_CLASS = PROD_KIND   WHERE PROD_KIND  IN ('03','04','05','A6','Z1','Z2')",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE MIS.QUOTAPPR  set PROD_KIND = ''  WHERE PROD_KIND  IN ('03','04','05','A6','Z1','Z2')",
						new Object[] {});

	}

	/**
	 * J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
	 */
	@NonTransactional
	@Override
	public void doLmsBatch0016_2() {
		this.getJdbc()
				.update("INSERT INTO MIS.ELF412C (ELF412C_BRANCH, ELF412C_CUSTID ,ELF412C_DUPNO,ELF412C_MAINCUST,ELF412C_LLRDATE,ELF412C_LRDATE,ELF412C_RCKDLINE,ELF412C_NEWADD,ELF412C_NEWDATE,ELF412C_NCKDDATE,ELF412C_CANCELDT,ELF412C_UPDATER,ELF412C_TMESTAMP,ELF412C_UCKDLINE,ELF412C_UCKDDT,ELF412C_DATADT,ELF412C_NEXTNWDT,ELF412C_NEXTLTDT) VALUES ('069', '24310722' ,'0','Y','0001-01-01','0001-01-01','A','N','010708','0001-01-01','0001-01-01','900001',CURRENT TIMESTAMP,'N','0001-01-01','2019-01-30','0001-01-01','0001-01-01')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.ELF412C (ELF412C_BRANCH, ELF412C_CUSTID ,ELF412C_DUPNO,ELF412C_MAINCUST,ELF412C_LLRDATE,ELF412C_LRDATE,ELF412C_RCKDLINE,ELF412C_NEWADD,ELF412C_NEWDATE,ELF412C_NCKDDATE,ELF412C_CANCELDT,ELF412C_UPDATER,ELF412C_TMESTAMP,ELF412C_UCKDLINE,ELF412C_UCKDDT,ELF412C_DATADT,ELF412C_NEXTNWDT,ELF412C_NEXTLTDT) VALUES ('229', '24310722' ,'0','Y','0001-01-01','0001-01-01','A','N','010708','0001-01-01','0001-01-01','900001',CURRENT TIMESTAMP,'N','0001-01-01','2019-01-30','0001-01-01','0001-01-01')",
						new Object[] {});

		this.getJdbc()
				.update("INSERT INTO MIS.ELF412C (ELF412C_BRANCH, ELF412C_CUSTID ,ELF412C_DUPNO,ELF412C_MAINCUST,ELF412C_LLRDATE,ELF412C_LRDATE,ELF412C_RCKDLINE,ELF412C_NEWADD,ELF412C_NEWDATE,ELF412C_NCKDDATE,ELF412C_CANCELDT,ELF412C_UPDATER,ELF412C_TMESTAMP,ELF412C_UCKDLINE,ELF412C_UCKDDT,ELF412C_DATADT,ELF412C_NEXTNWDT,ELF412C_NEXTLTDT) VALUES ('235', '24310722' ,'0','Y','0001-01-01','0001-01-01','A','N','010708','0001-01-01','0001-01-01','900001',CURRENT TIMESTAMP,'N','0001-01-01','2019-01-30','0001-01-01','0001-01-01')",
						new Object[] {});

		this.getJdbc()
				.update("INSERT INTO MIS.ELF412C (ELF412C_BRANCH, ELF412C_CUSTID ,ELF412C_DUPNO,ELF412C_MAINCUST,ELF412C_LLRDATE,ELF412C_LRDATE,ELF412C_RCKDLINE,ELF412C_NEWADD,ELF412C_NEWDATE,ELF412C_NCKDDATE,ELF412C_CANCELDT,ELF412C_UPDATER,ELF412C_TMESTAMP,ELF412C_UCKDLINE,ELF412C_UCKDDT,ELF412C_DATADT,ELF412C_NEXTNWDT,ELF412C_NEXTLTDT) VALUES ('235', '13085136' ,'0','Y','0001-01-01','0001-01-01','A','N','010708','0001-01-01','0001-01-01','900001',CURRENT TIMESTAMP,'N','0001-01-01','2019-01-30','0001-01-01','0001-01-01')",
						new Object[] {});

		this.getJdbc()
				.update("INSERT INTO MIS.ELF412C (ELF412C_BRANCH, ELF412C_CUSTID ,ELF412C_DUPNO,ELF412C_MAINCUST,ELF412C_LLRDATE,ELF412C_LRDATE,ELF412C_RCKDLINE,ELF412C_NEWADD,ELF412C_NEWDATE,ELF412C_NCKDDATE,ELF412C_CANCELDT,ELF412C_UPDATER,ELF412C_TMESTAMP,ELF412C_UCKDLINE,ELF412C_UCKDDT,ELF412C_DATADT,ELF412C_NEXTNWDT,ELF412C_NEXTLTDT) VALUES ('235', '80293513' ,'0','N','0001-01-01','0001-01-01','A','N','010708','0001-01-01','0001-01-01','900001',CURRENT TIMESTAMP,'N','0001-01-01','2019-01-30','0001-01-01','0001-01-01')",
						new Object[] {});

		this.getJdbc()
				.update("INSERT INTO MIS.ELF412C (ELF412C_BRANCH, ELF412C_CUSTID ,ELF412C_DUPNO,ELF412C_MAINCUST,ELF412C_LLRDATE,ELF412C_LRDATE,ELF412C_RCKDLINE,ELF412C_NEWADD,ELF412C_NEWDATE,ELF412C_NCKDDATE,ELF412C_CANCELDT,ELF412C_UPDATER,ELF412C_TMESTAMP,ELF412C_UCKDLINE,ELF412C_UCKDDT,ELF412C_DATADT,ELF412C_NEXTNWDT,ELF412C_NEXTLTDT) VALUES ('008', '89471411' ,'0','Y','0001-01-01','0001-01-01','A','N','010708','0001-01-01','0001-01-01','900001',CURRENT TIMESTAMP,'N','0001-01-01','2019-01-30','0001-01-01','0001-01-01')",
						new Object[] {});

		this.getJdbc()
				.update("INSERT INTO MIS.ELF412C (ELF412C_BRANCH, ELF412C_CUSTID ,ELF412C_DUPNO,ELF412C_MAINCUST,ELF412C_LLRDATE,ELF412C_LRDATE,ELF412C_RCKDLINE,ELF412C_NEWADD,ELF412C_NEWDATE,ELF412C_NCKDDATE,ELF412C_CANCELDT,ELF412C_UPDATER,ELF412C_TMESTAMP,ELF412C_UCKDLINE,ELF412C_UCKDDT,ELF412C_DATADT,ELF412C_NEXTNWDT,ELF412C_NEXTLTDT) VALUES ('008', '28861845' ,'0','Y','0001-01-01','0001-01-01','A','N','010708','0001-01-01','0001-01-01','900001',CURRENT TIMESTAMP,'N','0001-01-01','2019-01-30','0001-01-01','0001-01-01')",
						new Object[] {});

	}

	/**
	 * J-107-0357_05097_B1002 Web e-Loan授信系統配合工業區及產業園區建廠優惠貸款專案，額度簽報新增「專案種類」與相關報表
	 */
	@NonTransactional
	@Override
	public void doLmsBatch0017() {
		this.getJdbc()
				.update("update mis.elf412 set ELF412_NCKDFLAG='1',ELF412_NCKDDATE = '2018-12-03',ELF412_NCKDMEMO='香港行擔任管理行' where elf412_custid in ('VGZ0084795','WSZ0032615') and elf412_branch = '070'",
						new Object[] {});

	}

	@Override
	public List<Map<String, Object>> getBankLaw33_3(String custId, String dupNo) {

		custId = Util.addSpaceWithValue(custId, 10);
		/*
		 * SELECT RCUSTID||DUPNO1 CUSTID,TRIM(CNAME) CUSTNAME,CASE WHEN
		 * CTYPE='1' THEN APPT ELSE PROF END RELATION, CASE WHEN CTYPE='1' THEN
		 * (CTYPE||APPT) WHEN CTYPE='2' THEN '25' WHEN CTYPE='3' THEN '35' ELSE
		 * '95' END SN, CTYPE FROM MIS.ELCREPER WHERE CUSTID||DUPNO = ? AND
		 * (CUSTID||DUPNO<>RCUSTID||DUPNO1) UNION SELECT DISTINCT
		 * P.CUSTID||P.DUPNO CUSTID,TRIM(C.CNAME) CUSTNAME,'本人' RELATION,'01'
		 * SN,'' AS CTYPE FROM MIS.ELCREPER P LEFT JOIN MIS.CUSTDATA C ON
		 * P.CUSTID=C.CUSTID AND P.DUPNO=C.DUPNO WHERE P.CUSTID||P.DUPNO = ?
		 * UNION SELECT DISTINCT CASE WHEN MATEID='' THEN LNF085_SPOUSE_ID ELSE
		 * MATEID||'0' END CUSTID,CASE WHEN MATEID='' THEN LNF085_SPOUSE_NAME
		 * ELSE MATENM END CUSTNAME,'配偶' RELATION,'02' SN,'' AS CTYPE FROM
		 * MIS.ELCREPER P LEFT JOIN MIS.CUSTDATA C ON P.CUSTID=C.CUSTID AND
		 * P.DUPNO=C.DUPNO LEFT JOIN LN.LNF085 ON
		 * LNF085_CUST_ID=P.CUSTID||P.DUPNO WHERE P.CUSTID||P.DUPNO = ? AND
		 * (MATEID<>'' OR LNF085_SPOUSE_ID<>'') ORDER BY SN,CUSTID,RELATION
		 */
		String idNo = custId + dupNo;
		return this.getJdbc().queryForList("ELCREPER.getBankLaw33_3",
				new Object[] { idNo, custId, dupNo, custId, dupNo });

	}

	@Override
	public List<Map<String, Object>> getELF402_ECONOMIC_Y(String custId,
			String dupNo) {
		return this.getJdbc().queryForListWithMax("ELCREPER.getECONOMIC_Y",
				new Object[] { custId, dupNo });
	}

	/**
	 * J-108-0039_05097_B1001 Web e-Loan
	 * 國內企金授信系統簽報、動審AML頁籤將借戶之「具控制權人」納入應查詢比對黑名單之對象。
	 */
	@Override
	public List<Map<String, Object>> findCMFLUNB1ByCustId(String custId,
			String dupNo) {
		return this.getJdbc().queryForListWithMax("CMFLUNB1.selByCustId",
				new Object[] { custId, dupNo });
	}

	@Override
	public List<Map<String, Object>> findSynBankList(String FINKIND,
			String FINCODE) {
		return this.getJdbc().queryForList("MISSynBank.selBrank3",
				new String[] { FINKIND, FINCODE });
	}

	/**
	 * J-108-0116 企金處共同行銷 塞 MIS.SYNBANK 資料
	 */
	@NonTransactional
	@Override
	public void doLmsBatch0021() {
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1A','700','','**********','','system','2018-02-07','兆豐證券股份有限公司','忠孝東路２段９５號３樓','','','兆豐證券股份有限公司')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1A','700','a00','02-********','','system','2014-03-12','兆豐證券股份有限公司忠孝分公司','忠孝東路４段５６３號３樓、５６５號４樓','','S700a00','兆豐證券股份有限公司')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1A','700','b00','03-2807759','','system','2014-03-12','兆豐證券股份有限公司中壢分公司','中山路１４２號８樓、１４６號８樓','','S700b00','兆豐證券股份有限公司')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1A','700','c00','**********','','system','2011-03-21','兆豐證券民生分公司','民生東路三段１２８號３樓之２','','S700c00','兆豐證券股份有限公司')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1A','700','d00','04-7289966','','system','2014-03-12','兆豐證券股份有限公司彰化分公司','和平路５７號６樓','','S700d00','兆豐證券股份有限公司')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1A','700','f00','04-7772900','','system','2014-03-12','兆豐證券股份有限公司鹿港分公司','彰鹿路八段５９號','','S700f00','兆豐證券股份有限公司')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1A','700','g00','02-********','','system','2014-03-12','兆豐證券股份有限公司南門分公司','羅斯福路２段９號２樓（部分）','','S700g00','兆豐證券股份有限公司')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1A','700','h00','07-3958858','','system','2014-03-12','兆豐證券股份有限公司三民分公司','大順二路４１號２樓','','S700h00','兆豐證券股份有限公司')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1A','700','i00','02-********','','system','2014-03-12','兆豐證券股份有限公司松德分公司','忠孝東路５段５１０號３樓、３樓之１、３樓之２、３樓之１２及３樓之１３','','S700i00','兆豐證券股份有限公司')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1A','700','j00','02-********','','system','2014-03-12','兆豐證券股份有限公司大安分公司','信義路３段１８２號３、４樓','','S700j00','兆豐證券股份有限公司')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1A','700','k00','04-8373898','','system','2014-03-12','兆豐證券股份有限公司員林分公司','中山路１段７５３號３樓','','S700k00','兆豐證券股份有限公司')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1A','700','p00','02-********','','system','2014-03-12','兆豐證券股份有限公司復興分公司','復興北路９９號８樓之１','','S700p00','兆豐證券股份有限公司')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1A','700','q00','02-********','','system','2014-03-12','兆豐證券股份有限公司內湖分公司','成功路４段１６６號５樓','','S700q00','兆豐證券股份有限公司')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1A','700','r00','**********','','system','2011-03-21','兆豐證券寶成分公司','台中港路三段７８之２號３樓','','S700r00','兆豐證券股份有限公司')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1A','700','s00','07-6235988','','system','2014-03-12','兆豐證券股份有限公司岡山分公司','中山北路２８號３樓（部份）','','S700s00','兆豐證券股份有限公司')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1A','700','w00','02-********','','system','2014-03-12','兆豐證券股份有限公司新莊分公司','思源路４２７號２樓','','S700w00','兆豐證券股份有限公司')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1A','700','B00','02-********','','system','2014-03-12','兆豐證券股份有限公司板橋分公司','文化路１段２６８號２樓之１','','S700B00','兆豐證券股份有限公司')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1A','700','C00','05-5338989','','system','2014-03-12','兆豐證券股份有限公司來福分公司','民生路１５６號８樓','','S700C00','兆豐證券股份有限公司')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1A','700','D00','05-5867999','','system','2014-03-12','兆豐證券股份有限公司西螺分公司','中山路１２７號３樓','','S700D00','兆豐證券股份有限公司')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1A','700','E00','05-5968899','','system','2014-03-12','兆豐證券股份有限公司斗南分公司','中正路１３１號３樓','','S700E00','兆豐證券股份有限公司')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1A','700','F00','05-6313388','','system','2014-03-12','兆豐證券股份有限公司虎尾分公司','公安路１３３號４樓','','S700F00','兆豐證券股份有限公司')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1A','700','G00','06-5728388','','system','2014-03-12','兆豐證券股份有限公司麻豆分公司','興中路１２７號３樓','','S700G00','兆豐證券股份有限公司')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1A','700','H00','**********','','system','2011-03-21','兆豐證券東門分公司','金山南路二段３３號２樓','','S700H00','兆豐證券股份有限公司')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1A','700','I00','*********','','system','2011-03-21','兆豐證券北高雄分公司','中正四路２３５號２樓之１','','S700I00','兆豐證券股份有限公司')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1A','700','J00','04-********','','system','2014-03-12','兆豐證券股份有限公司公益分公司','館前路５５號１樓','','S700J00','兆豐證券股份有限公司')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1A','700','K00','06-6357111','','system','2014-03-12','兆豐證券股份有限公司新營分公司','中營里中山路１１６號','','S700K00','兆豐證券股份有限公司')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1A','700','L00','02-********','','system','2014-07-22','兆豐證券股份有限公司天母分公司','中山北路六段１２６號３樓','','S700L00','兆豐證券股份有限公司')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1A','700','M00','03-3475188','','system','2014-03-12','兆豐證券股份有限公司桃園分公司','成功路２段２號２樓','','S700M00','兆豐證券股份有限公司')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1A','700','N00','02-********','','system','2014-03-12','兆豐證券股份有限公司埔墘分公司','三民路１段２１６號２樓','','S700N00','兆豐證券股份有限公司')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1A','700','P00','02-********','','system','2014-03-12','兆豐證券股份有限公司南京分公司','南京東路３段６５號３樓','','S700P00','兆豐證券股份有限公司')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1A','700','R00','07-8063000','','system','2014-03-12','兆豐證券股份有限公司小港分公司','漢民路３８１號２樓','','S700R00','兆豐證券股份有限公司')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1A','700','S00','02-********','','system','2014-03-12','兆豐證券股份有限公司大同分公司','民生西路２８６號３樓','','S700S00','兆豐證券股份有限公司')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1A','700','U00','03-3776899','','system','2014-03-12','兆豐證券股份有限公司桃鶯分公司','桃鶯路３４３號３、４樓','','S700U00','兆豐證券股份有限公司')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1A','700','V00','03-5256199','','system','2014-03-12','兆豐證券股份有限公司新竹分公司','中正路１２９號３樓','','S700V00','兆豐證券股份有限公司')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1A','700','W00','02-********','','system','2014-03-12','兆豐證券股份有限公司城中分公司','重慶南路１段２號２樓（部分）','','S700W00','兆豐證券股份有限公司')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1A','700','X00','02-********','','system','2014-03-12','兆豐證券股份有限公司永和分公司','保生路１號６樓之１','','S700X00','兆豐證券股份有限公司')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1A','700','Y00','02-********','','system','2014-03-12','兆豐證券股份有限公司世貿分公司','信義路４段４５８號６樓','','S700Y00','兆豐證券股份有限公司')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1A','700','Z00','07-3315200','','system','2014-03-12','兆豐證券股份有限公司高雄分公司','新光路３６號１樓','','S700Z00','兆豐證券股份有限公司')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1A','700','100','05-2230230','','system','2014-03-12','兆豐證券股份有限公司嘉義分公司','文化路２５９號３樓及４樓部份','','S700100','兆豐證券股份有限公司')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1A','700','300','04-********','','system','2014-03-12','兆豐證券股份有限公司台中分公司','五權西路一段２５７號７樓部分','','S700300','兆豐證券股份有限公司')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1A','700','400','07-2256588','','system','2014-03-12','兆豐證券股份有限公司東高雄分公司','五福二路８２號２樓','','S700400','兆豐證券股份有限公司')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1A','700','500','04-********','','system','2014-03-12','兆豐證券股份有限公司台中港分公司','沙田路１０７號２樓','','S700500','兆豐證券股份有限公司')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1A','700','600','06-2230398','','system','2014-03-12','兆豐證券股份有限公司台南分公司','忠義路２段１４號４、５樓','','S700600','兆豐證券股份有限公司')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1A','700','700','03-5543223','','system','2014-03-12','兆豐證券股份有限公司竹北分公司','三民路１８、２０、２２、２４號１樓及１８、２４號２樓','','S700700','兆豐證券股份有限公司')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1A','700','800','02-********','','system','2014-03-12','兆豐證券股份有限公司三重分公司','正義北路１９５號３樓','','S700800','兆豐證券股份有限公司')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1A','700','900','**********','','system','2011-03-21','兆豐證券景美分公司','景中街１號３樓','','S700900','兆豐證券股份有限公司')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1B','800','001','02-2381-2727','','system','2019-06-14','兆豐產險總公司','','','','兆豐產險')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1B','800','002','02-2571-5558','','system','2019-06-14','兆豐產險城東分公司','','','','兆豐產險')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1B','800','003','03-955-0546','','system','2019-06-14','兆豐產險羅東通訊處','','','','兆豐產險')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1B','800','004','02-2785-6936','','system','2019-06-14','兆豐產險信義分公司','','','','兆豐產險')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1B','800','005','02-2425-8531','','system','2019-06-14','兆豐產險基隆通訊處','','','','兆豐產險')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1B','800','006','02-2250-0790','','system','2019-06-14','兆豐產險台北分公司','','','','兆豐產險')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1B','800','007','02-2915-1788','','system','2019-06-14','兆豐產險新店通訊處','','','','兆豐產險')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1B','800','008','02-2986-0505','','system','2019-06-14','兆豐產險三重分公司','','','','兆豐產險')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1B','800','009','082-325-329','','system','2019-06-14','兆豐產險金門通訊處','','','','兆豐產險')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1B','800','010','02-2998-8789','','system','2019-06-14','兆豐產險新莊通訊處','','','','兆豐產險')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1B','800','011','03-316-3022','','system','2019-06-14','兆豐產險桃園分公司','','','','兆豐產險')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1B','800','012','03-495-3425','','system','2019-06-14','兆豐產險中壢通訊處','','','','兆豐產險')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1B','800','013','03-531-6666','','system','2019-06-14','兆豐產險新竹分公司','','','','兆豐產險')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1B','800','014','037-368-738','','system','2019-06-14','兆豐產險苗栗通訊處','','','','兆豐產險')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1B','800','015','04-2223-5004','','system','2019-06-14','兆豐產險台中分公司','','','','兆豐產險')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1B','800','016','04-2531-5633','','system','2019-06-14','兆豐產險豐原通訊處','','','','兆豐產險')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1B','800','017','04-2663-3511','','system','2019-06-14','兆豐產險沙鹿通訊處','','','','兆豐產險')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1B','800','018','04-7625888','','system','2019-06-14','兆豐產險彰化分公司','','','','兆豐產險')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1B','800','019','049-239-1325','','system','2019-06-14','兆豐產險草屯通訊處','','','','兆豐產險')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1B','800','020','06-235-2346','','system','2019-06-14','兆豐產險台南分公司','','','','兆豐產險')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1B','800','021','05-537-3535','','system','2019-06-14','兆豐產險斗六通訊處','','','','兆豐產險')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1B','800','022','05-276-8811','','system','2019-06-14','兆豐產險嘉義通訊處','','','','兆豐產險')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1B','800','023','06-202-9111','','system','2019-06-14','兆豐產險永康通訊處','','','','兆豐產險')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1B','800','024','07-251-9090','','system','2019-06-14','兆豐產險高雄分公司','','','','兆豐產險')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1B','800','025','08-736-4813','','system','2019-06-14','兆豐產險屏東通訊處','','','','兆豐產險')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1B','800','026','07-623-4608','','system','2019-06-14','兆豐產險岡山通訊處','','','','兆豐產險')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1B','800','027','08-835-3456','','system','2019-06-14','兆豐產險東港通訊處','','','','兆豐產險')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1B','800','028','03-833-4703','','system','2019-06-14','兆豐產險花蓮分公司','','','','兆豐產險')",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO MIS.SYNBANK values ('1B','800','029','089-328-947','','system','2019-06-14','兆豐產險台東通訊處','','','','兆豐產險')",
						new Object[] {});
	}

	/**
	 * J-108-0178_05097_B1001 Web
	 * e-Loan企金授信系統簽報書增列符合公司法第206條第三項「與本行董事具有控制從屬關係之公司」之董監事名單。
	 */
	@Override
	public List<Map<String, Object>> findELREMAINByElf902RcustId(String custId,
			String dupNo) {
		return this.getJdbc().queryForListWithMax(
				"ELREMAIN.selByElf902RcustId", new Object[] { custId, dupNo });
	}

	/**
	 * 1.修正三多分行異常通報-已完成 2.配合國外部解除能元科技股份有限公司(統一編號********)城中分行2016-03-03異常通報。
	 */
	@Override
	public void doLmsBatch0022() {
		int i = this
				.getJdbc()
				.update("update ln.lnfe0854 set LNFE0854_CLOSEFG = 'Y',LNFE0854_CLOSEDT='2019-10-30', LNFE0854_CLOSEPNO='108007LMS00651', LNFE0854_CLOSEUID ='64a89fa6e5e14b53acc4537cdf90dd4a' where lnfe0854_custid = '********' and LNFE0854_UNID = 'f648c27aff5e43028b96c4a1d48db91d'",
						new Object[] {});
		// this.getJdbc().update("update mis.elf412 set ELF412_RCKDLINE = 'A',ELF412_MDFLAG = '',ELF412_PROCESS = '',ELF412_MDDT = null where elf412_custid = '22147470' and elf412_branch = '038'",
		// new Object[] {});
	}

	@Override
	public List<Map<String, Object>> findCLS180R23_RatePlan_20() {
		return this.getJdbc().queryForListWithMax("MIS.CLS180R23_RatePlan_20",
				new Object[] {});
	}

	/**
	 * J-108-0166 企業社會責任貸放情形統計表 LMS180R47
	 */
	@Override
	public Map<String, Object> findLnunidList(String bgn, String end) {
		// 因迄日需含當天故設定時間為23:59:59
		SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
		SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd 23:59:59");

		return this.getJdbc().queryForMap(
				"rpt.LMS180R47_lnunid",
				new Object[] { sdf1.format(CapDate.getDate(bgn, "yyyy-MM-dd")),
						sdf2.format(CapDate.getDate(end, "yyyy-MM-dd")) });
	}

	@Override
	public List<Map<String, Object>> findTPCConsumerCreditDetail(
			Set<String> targetSet) {
//		StringBuffer sb = new StringBuffer();
		String targetParams = Util.genSqlParam(targetSet.toArray(new String[0]));
//		for (String key : targetSet) {
//			sb.append(sb.length() > 0 ? "," : "");
//			sb.append("'");
//			sb.append(key);
//			sb.append("'");
//		}

		return this.getJdbc().queryForAllListByCustParam(
				"MIS.queryTPCConsumerCreditDetail",
				new String[] { targetParams }, targetSet.toArray(new String[0]));
	}

	/**
	 * J-107-0342_05097_B1003 Web e-Loan授信系統新增覆審考核相關報表
	 * 
	 * @param brno
	 * @param custId
	 * @param dupNo
	 * @param ctlType
	 * @param baseDate
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findElf494ColsestReChkRpt(String brno,
			String custId, String dupNo, String ctlType, String baseDate) {
		return this.getJdbc().queryForListWithMax(
				"MIS.ELF494.findClosestReChkRpt",
				new Object[] { brno, custId, dupNo, ctlType, baseDate });
	}

	/**
	 * J-108-0288_05097_B1001 Web e-Loan授信系統新增合併關係企業額度彙總表
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findMisRelatedCompany(String custId,
			String dupNo) {

		return this.getJdbc().queryForListWithMax(
				"MIS.findRelatedCompany",
				new Object[] { custId, dupNo, custId, dupNo, custId, dupNo,
						custId, dupNo });
	}

	/**
	 * J-108-0288_05097_B1001 Web e-Loan授信系統新增合併關係企業額度彙總表
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	@Override
	public Map<String, Object> findLnf197SumFactAmtByCustId(String custId,
			String dupNo) {

		String fullCustKey = Util.getLeftStr(custId + "          ", 10) + dupNo;
		return this.getJdbc().queryForMap("MIS.LNF197.sumFactamtByCustId",
				new String[] { fullCustKey });
	}

	/**
	 * J-108-0225_05097_B1001 Web e-Loan企金授信額度明細表新增設質予本行之總股數欄位與檢核
	 * 
	 * 查詢股票資料
	 * 
	 * @param stkNo
	 * @return
	 * @throws CapException
	 */
	@Override
	public Map<String, Object> findStkDataByStkNo(String stkNo)
			throws CapException {
		return this.getJdbc().queryForMap("rpt.stkdata.getBystkno",
				new String[] { stkNo });
	}

	/**
	 * J-108-0225_05097_B1001 Web e-Loan企金授信額度明細表新增設質予本行之總股數欄位與檢核
	 * 
	 * 查詢設質總股數
	 * 
	 * @param stkNo
	 * @return
	 * @throws CapException
	 */
	@Override
	public Map<String, Object> findCOLL0307ByStkNoToSet(String stkNo)
			throws CapException {
		return this.getJdbc().queryForMap("rpt.COLL0307.getBystknoToSet",
				new String[] { stkNo });
	}

	/**
	 * J-109-0050_05097_B1001
	 * 修改國金部簽報書2019國金行(兆)授字第00122號借戶ID由INZ0002372換成INZ0000120
	 * 
	 */
	@NonTransactional
	@Override
	public void doLmsBatch0027() {
		this.getJdbc()
				.update("update MIS.ELCSECNT set COMID = 'INZ0000120',COMDUPNO = '0' where COMID = 'INZ0002372' AND COMDUPNO = '0' AND CNTRNO in ('025410600148','025410800099')",
						new Object[] {});

		this.getJdbc()
				.update("update MIS.ELLNSEEK set CUSTID = 'INZ0000120',DUPNO = '0' where CUSTID = 'INZ0002372' AND DUPNO = '0' AND CNTRNO in ('025410600148','025410800099')",
						new Object[] {});

		this.getJdbc()
				.update("update MIS.ELF447 set ELF447_CUSTID = 'INZ0000120',ELF447_DUPNO = '0' where ELF447_CUSTID = 'INZ0002372' AND ELF447_DUPNO = '0' AND ELF447_CONTRACT in ('025410600148','025410800099')",
						new Object[] {});

		this.getJdbc()
				.update("update MIS.ELF447N set ELF447N_CUSTID = 'INZ0000120',ELF447N_DUPNO = '0' where ELF447N_CUSTID = 'INZ0002372' AND ELF447N_DUPNO = '0' AND ELF447N_CONTRACT in ('025410600148','025410800099')",
						new Object[] {});

		this.getJdbc()
				.update("update MIS.ELF506 set ELF506_CUST_ID = 'INZ00001200' where ELF506_CNTRNO in ('025410600148','025410800099')",
						new Object[] {});

		this.getJdbc()
				.update("update MIS.IQUOTAPP set CUSTID = 'INZ0000120',DUPNO = '0' where CUSTID = 'INZ0002372' AND DUPNO = '0' AND QUOTANO in ('025410600148','025410800099')",
						new Object[] {});

		this.getJdbc()
				.update("delete from MIS.IQUOTGUR where CUSTID = 'INZ0000120' AND DUPNO = '0' AND QUOTANO in ('025410600148','025410800099')",
						new Object[] {});

		this.getJdbc()
				.update("update MIS.IQUOTJON set CUSTID = 'INZ0000120',DUPNO = '0' where CUSTID = 'INZ0002372' AND DUPNO = '0' AND QUOTANO in ('025410600148','025410800099')",
						new Object[] {});

		this.getJdbc()
				.update("update MIS.QUOTAINF set CUSTID = 'INZ0000120',DUPNO = '0' where CUSTID = 'INZ0002372' AND DUPNO = '0' AND CNTRNO in ('025410600148','025410800099')",
						new Object[] {});

		this.getJdbc()
				.update("update MIS.QUOTAPPR set CUSTID = 'INZ0000120',DUPNO = '0' where CUSTID = 'INZ0002372' AND DUPNO = '0' AND CNTRNO in ('025410600148','025410800099')",
						new Object[] {});

		this.getJdbc()
				.update("update MIS.QUOTSUB  set CUSTID = 'INZ0000120',DUPNO = '0' where CUSTID = 'INZ0002372' AND DUPNO = '0' AND CNTRNO in ('025410600148','025410800099')",
						new Object[] {});

		this.getJdbc()
				.update("update MIS.ELF503 set ELF503_CUSTID = 'INZ0000120',ELF503_DUPNO = '0' where ELF503_CUSTID = 'INZ0002372' AND ELF503_DUPNO = '0' AND ELF503_CNTRNO in ('025410600148','025410800099')",
						new Object[] {});

		this.getJdbc()
				.update("update MIS.ELLNGTEE set CUSTID = 'INZ0000120',DUPNO = '0' where CUSTID = 'INZ0002372' AND DUPNO = '0' AND CNTRNO in ('025410600148','025410800099')",
						new Object[] {});

		this.getJdbc()
				.update("update MIS.ELLNGTEE set LNGEID = 'INZ0000120',DUPNO1 = '0' where LNGEID = 'INZ0002372' AND DUPNO1 = '0' AND LNGEFLAG = 'C' AND CNTRNO in ('025410600148','025410800099')",
						new Object[] {});

		this.getJdbc()
				.update("delete from MIS.ELLNGTEE where CUSTID = 'INZ0000120' AND DUPNO = '0' AND LNGEFLAG = 'G' AND CNTRNO in ('025410600148','025410800099')",
						new Object[] {});

		this.getJdbc()
				.update("update LN.LNF164 set LNF164_CUST_ID='INZ00001200' where LNF164_CUST_ID = 'INZ00023720' AND LNF164_CONTRACT in ('025410600148','025410800099')",
						new Object[] {});

	}

	/**
	 * J-109-0077_05097_B1001 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
	 * 
	 * @param cntrNo
	 * @return
	 */
	@Override
	public Map<String, Object> findElf515MomByCntr(String cntrNo) {

		return this.getJdbc().queryForMap("MIS.ELF515.findMomByCntrNo",
				new String[] { cntrNo, cntrNo });
	}

	@Override
	public List<Map<String, Object>> getAppropriationDataForLaborReliefLoanSummaryReport() {
		return this.getJdbc().queryForList(
				"getAppropriationDataForLaborReliefLoanSummaryReport",
				new Object[] {});
	}

	@Override
	public List<Map<String, Object>> getAppropriationDateForLaborReliefLoanDetailReport(
			String applyDate, String custId) {
		return this.getJdbc().queryForListWithMax(
				"getAppropriationDateForLaborReliefLoanDetailReport",
				new Object[] { applyDate, custId });
	}

	@Override
	public List<Map<String, Object>> getELF505UnFinishData() {
		// String sql = "SELECT * FROM\n" + "(\n" +
		// "\tSELECT ROW_NUMBER() OVER (PARTITION BY T1.ELF505_BANKKEYNO ORDER "
		// +
		// "BY T1.ELF447N_ENDDATE DESC ) AS SEQ,T1.*\n" + "\tFROM\n" + "\t(\n" +
		// "\tSELECT A.*,B.LCNAME,C.BRBKNO,E" +
		// ".GRTNO,F.ELF447N_ENDDATE,G.ELF447_EMP_NO,H.TEL\n" +
		// "\tFROM MIS.ELF505 A\n" + "\tLEFT JOIN MIS.CUSTDATA B " +
		// "ON A.ELF505_IDNO = B.CUSTID AND A.ELF505_DUPNO = B.DUPNO\n" +
		// "\tLEFT JOIN MIS.SYNBANK C ON C.BRNNO = " +
		// "SUBSTR(A.ELF505_BANKKEYNO,1,3) AND C.FINCODE = '017'\n" +
		// "\tLEFT JOIN MIS.COLLCNTR D ON D.CNTRNO = A" +
		// ".ELF505_BANKKEYNO\n" +
		// "\tLEFT JOIN MIS.COLL0503 E ON E.TYPCD = D.TYPCD AND E.BRANCH = D.BRANCH AND E"
		// +
		// ".CUSTID = D.CUSTID AND E.DUPNO = D.DUPNO AND E.COLLNO = D.COLLNO \n"
		// + "\tLEFT JOIN MIS.ELF447N F ON F" +
		// ".ELF447N_CONTRACT = A.ELF505_BANKKEYNO\n" +
		// "\tLEFT JOIN MIS.ELF447 G ON G.ELF447_UNID = F" +
		// ".ELF447N_UNID AND  G.ELF447_CHKBRANCH = F.ELF447N_BRANCH AND ELF447_ROLE = '1' AND ELF447_SYSTYPE = "
		// +
		// "'CLS'\n" +
		// "\tLEFT JOIN MIS.ICBCBR H ON H.BRNO = SUBSTR(A.ELF505_BANKKEYNO,1,3)\n"
		// + "\tWHERE " +
		// "ELF505_SRLNO = '' AND ELF505_SRLNOSTATUS != '5' " +
		// //"AND E.GRTNO != '' AND E.GRTNO IS NOT NULL\n" +
		// "\t)" +
		// " AS T1\n" + ") AS T2\n" + "WHERE SEQ = 1";
		return this.getJdbc().queryForListWithMax("GET_ELF505UnFinishData",
				new Object[] {});
	}

	@Override
	public List<Map<String, Object>> getELF505UnFinishDataV2() {

		return this.getJdbc().queryForListWithMax("GET_ELF505UnFinishDatav2",
				new Object[] {});
	}

	@Override
	public void updateELF505(String custId, String grntPaper, String srlNo,
			String srlNoStatus, String ap_dt, String up_dt, String msg) {
		String sql = "ELF505.UPDATE_STATUS";
		this.getJdbc().update(
				sql,
				new Object[] { grntPaper, srlNo, srlNoStatus, ap_dt, up_dt,
						msg, custId });
	}

	@Override
	public void updateELF505withVer(String custId, String grntPaper,
			String srlNo, String srlNoStatus, String ap_dt, String up_dt,
			String msg, int verNo) {
		String sql = "ELF505.UPDATE_STATUS_WITH_VER";
		this.getJdbc().update(
				sql,
				new Object[] { grntPaper, srlNo, srlNoStatus, ap_dt, up_dt,
						msg, custId, String.valueOf(verNo) });
	}

	@Override
	public Map<String, Object> getELF505(String custId, int verNo) {
		String sql = "ELF505.GETBYID";
		return this.getJdbc().queryForMap(sql,
				new Object[] { custId, String.valueOf(verNo) });
	}

	@Override
	public List<Map<String, Object>> findLnf020(String cntrNo) {
		return this.getJdbc().queryForListWithMax("LN.LNF020ByContract",
				new Object[] { cntrNo });
	}

	@Override
	public List<Map<String, Object>> getContractAndLoanNo(String custId,
			String dupNo) {
		return this.getJdbc().queryForListWithMax(
				"getContractAndLoanNo",
				new Object[] { Util.addSpaceWithValue(custId, 10) + dupNo,
						Util.addSpaceWithValue(custId, 10) + dupNo,
						Util.addSpaceWithValue(custId, 10) + dupNo });
		// ,Util.addSpaceWithValue(custId, 10) + dupNo});
	}

	@Override
	public boolean chkFilterData(String custId, String dupNo, String cntrNo,
			String loanNo) {
		boolean chk = false;
		StringBuffer sb = new StringBuffer();
		List<String> w1List = new ArrayList<String>();
		w1List.add(Util.addSpaceWithValue(custId, 10) + dupNo);
		w1List.add(cntrNo);
		if (Util.isNotEmpty(loanNo)) {
			sb.append(" AND ( L150.LNF150_LOAN_NO=? OR E602.ELF602_LOAN_NO=? )");
			w1List.add(loanNo);
			w1List.add(loanNo);
		}

		List<Map<String, Object>> rowData = this.getJdbc()
				.queryForAllListByCustParam(
						"LNF020.chkContractAndLnf150LoanNo",
						new String[] { sb.toString() }, w1List.toArray());

		if (rowData.size() > 0) {
			int cnt = (Integer) rowData.get(0).get("CNT");
			if (cnt > 0) {
				chk = true;
			}
		} else {

		}
		//
		return chk;
	}

	@Override
	public boolean chkFilterData1(String custId, String dupNo, String cntrNo) {
		boolean chk = false;
		List<String> w1List = new ArrayList<String>();
		w1List.add(Util.addSpaceWithValue(custId, 10) + dupNo);
		w1List.add(cntrNo);

		List<Map<String, Object>> rowData = this.getJdbc().queryForListWithMax(
				"LNF020.chkContract",
				new Object[] { Util.addSpaceWithValue(custId, 10) + dupNo,
						cntrNo });

		if (rowData.size() > 0) {
			int cnt = (Integer) rowData.get(0).get("CNT");
			if (cnt > 0) {
				chk = true;
			}
		} else {

		}

		return chk;
	}
	
	@Override
	public boolean chkFilterData2(String cntrNo, String loanNo) {
		boolean chk = false;
		List<String> w1List = new ArrayList<String>();
		w1List.add(cntrNo);
		w1List.add(loanNo);

		List<Map<String, Object>> rowData = this.getJdbc().queryForListWithMax(
				"ELF602.chkLoanNo", new Object[] { cntrNo, loanNo });

		if (rowData.size() > 0) {
			int cnt = (Integer) rowData.get(0).get("CNT");
			if (cnt > 0) {
				chk = true;
			}
		}

		if (!chk) {
			List<Map<String, Object>> rowData2 = this.getJdbc()
					.queryForListWithMax("LNF150.chkLoanNo",
							new Object[] { cntrNo, loanNo });

			if (rowData2.size() > 0) {
				int cnt = (Integer) rowData2.get(0).get("CNT");
				if (cnt > 0) {
					chk = true;
				}
			}
		}

		return chk;
	}
	
	@Override
	public ELF601 getElf601ByUnid(String unid) {
		Map<String, Object> rowData = this.getJdbc().queryForMap(
				"ELF601.getElf601ByUnid", new Object[] { unid });
		if (rowData == null) {
			return null;
		} else {
			ELF601 model = new ELF601();
			DataParse.map2Bean(rowData, model);
			return model;
		}
	}
	
	@Override
	public List<ELF602> getESGDataMaxFoDate(String cntrno) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForList(
				"ELF602.getESGDataMaxFoDate", new Object[] { cntrno },0, Integer.MAX_VALUE);
		if (rowData == null) {
			return null;
		} else {			
			List<ELF602> list = new ArrayList<ELF602>();
			for (Map<String, Object> row : rowData) {
				ELF602 mode = new ELF602();
				DataParse.map2Bean(row, mode);
				list.add(mode);
			}
			return list;
		}
	}

	@Override
	public Map<String, Object> getElf603ByKey(String from602SUid,
			String from602SApptime, String cntrno, BigDecimal from602SSeqno) {
		Map<String, Object> rowData = this.getJdbc().queryForMap(
				"ELF603.getElf603ByKey",
				new Object[] { from602SUid, from602SApptime, cntrno,
						from602SSeqno });
		return rowData;
	}

	// J-110-0363 By ID
	@Override
	public List<ELF601> getElf601OnlyById(String custId, String dupNo,
			String brNo) {
		StringBuffer sb = new StringBuffer();
		List<String> w1List = new ArrayList<String>();
		sb.append(" ELF601_CUSTID=? ");
		w1List.add(custId);

		sb.append(" AND ELF601_DUPNO=? ");
		w1List.add(dupNo);

		sb.append(" AND ELF601_BR_NO=? ");
		w1List.add(brNo);

		sb.append(" AND ELF601_CNTRNO='' AND ELF601_LOAN_NO='' ");

		List<Map<String, Object>> rowData = this.getJdbc()
				.queryForListByCustParam("select",
						new Object[] { "ELF601", sb.toString() },
						w1List.toArray(), 0, Integer.MAX_VALUE,
						new EloanColumnMapRowMapper());

		List<ELF601> list = new ArrayList<ELF601>();
		for (Map<String, Object> row : rowData) {
			ELF601 model = new ELF601();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}

	@Override
	public List<ELF601> getElf601ByIdCntrNoLoanNo(String custId, String dupNo,
			String cntrNo, String loanNo, String brNo) {
		StringBuffer sb = new StringBuffer();
		List<String> w1List = new ArrayList<String>();
		sb.append(" ELF601_CUSTID=? ");
		w1List.add(custId);

		sb.append(" AND ELF601_DUPNO=? ");
		w1List.add(dupNo);

		sb.append(" AND ELF601_BR_NO=? ");
		w1List.add(brNo);

		if (cntrNo != null) {
			sb.append(" AND ELF601_CNTRNO=? ");
			w1List.add(cntrNo);
		}
		if (loanNo != null) {
			sb.append(" AND ELF601_LOAN_NO=? ");
			w1List.add(loanNo);
		}

		List<Map<String, Object>> rowData = this.getJdbc()
				.queryForListByCustParam("select",
						new Object[] { "ELF601", sb.toString() },
						w1List.toArray(), 0, Integer.MAX_VALUE,
						new EloanColumnMapRowMapper());

		List<ELF601> list = new ArrayList<ELF601>();
		for (Map<String, Object> row : rowData) {
			ELF601 model = new ELF601();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}

	@Override
	public List<ELF601> getElf601ByCntrNoLoanNo(String cntrNo, String loanNo,
			String brNo) {
		StringBuffer sb = new StringBuffer();
		List<String> w1List = new ArrayList<String>();
		sb.append(" ELF601_CNTRNO=? ");
		w1List.add(cntrNo);

		// 20200619 金襄理說 只選到額度層 => 只能撈 有額度沒帳號的
		// if(Util.isNotEmpty(loanNo)){
		sb.append(" AND ELF601_LOAN_NO=? ");
		w1List.add(loanNo);
		// }

		if (Util.isNotEmpty(brNo)) {
			sb.append(sb.length() > 0 ? " AND " : "");
			sb.append(" ELF601_BR_NO=? ");
			w1List.add(brNo);
		}

		List<Map<String, Object>> rowData = this.getJdbc()
				.queryForListByCustParam("select",
						new Object[] { "ELF601", sb.toString() },
						w1List.toArray(), 0, Integer.MAX_VALUE,
						new EloanColumnMapRowMapper());

		List<ELF601> list = new ArrayList<ELF601>();
		for (Map<String, Object> row : rowData) {
			ELF601 model = new ELF601();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}

	@Override
	public List<ELF601> getElf601OnlyIdByFilter(String custId, String dupNo,
			String status, String brNo) {
		StringBuffer sb = new StringBuffer();
		List<String> w1List = new ArrayList<String>();

		if (Util.isNotEmpty(custId)) {
			sb.append(" ELF601_CUSTID=? ");
			w1List.add(custId);
		}
		if (Util.isNotEmpty(dupNo)) {
			sb.append(sb.length() > 0 ? " AND " : "");
			sb.append(" ELF601_DUPNO=? ");
			w1List.add(dupNo);
		}
		if (Util.isNotEmpty(brNo)) {
			sb.append(sb.length() > 0 ? " AND " : "");
			sb.append(" ELF601_BR_NO=? ");
			w1List.add(brNo);
		}
		sb.append(sb.length() > 0 ? " AND " : "");
		sb.append(" ELF601_CNTRNO='' AND ELF601_LOAN_NO='' ");

		if (Util.isNotEmpty(status)) {
			if (Util.equals(status, "A")) {
				// 全部
			} else if (Util.equals(status, "N")) {
				// 未解除
				sb.append(sb.length() > 0 ? " AND " : "");
				sb.append(" ELF601_STATUS <> 'C' ");
			} else {
				// C-已解除
				sb.append(sb.length() > 0 ? " AND " : "");
				sb.append(" ELF601_STATUS=? ");
				w1List.add(status);
			}
		}

		List<Map<String, Object>> rowData = this.getJdbc()
				.queryForListByCustParam("select",
						new Object[] { "ELF601", sb.toString() },
						w1List.toArray(), 0, Integer.MAX_VALUE,
						new EloanColumnMapRowMapper());

		List<ELF601> list = new ArrayList<ELF601>();
		for (Map<String, Object> row : rowData) {
			ELF601 model = new ELF601();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}

	@Override
	public List<ELF601> getElf601ByFilter(String custId, String dupNo,
			String cntrNo, String loanNo, String status, String brNo) {
		StringBuffer sb = new StringBuffer();
		List<String> w1List = new ArrayList<String>();

		if (Util.isNotEmpty(custId)) {
			sb.append(" ELF601_CUSTID=? ");
			w1List.add(custId);
		}
		if (Util.isNotEmpty(dupNo)) {
			sb.append(sb.length() > 0 ? " AND " : "");
			sb.append(" ELF601_DUPNO=? ");
			w1List.add(dupNo);
		}
		if (Util.isNotEmpty(brNo)) {
			sb.append(sb.length() > 0 ? " AND " : "");
			sb.append(" ELF601_BR_NO=? ");
			w1List.add(brNo);
		}
		if (Util.isNotEmpty(cntrNo)) {
			sb.append(sb.length() > 0 ? " AND " : "");
			sb.append(" ELF601_CNTRNO=? ");
			w1List.add(cntrNo);
		}
		if (Util.isNotEmpty(loanNo)) {
			sb.append(sb.length() > 0 ? " AND " : "");
			sb.append(" ELF601_LOAN_NO=? ");
			w1List.add(loanNo);
		}
		if (Util.isNotEmpty(status)) {
			if (Util.equals(status, "A")) {
				// 全部
			} else if (Util.equals(status, "N")) {
				// 未解除
				sb.append(sb.length() > 0 ? " AND " : "");
				sb.append(" ELF601_STATUS <> 'C' ");
			} else {
				// C-已解除
				sb.append(sb.length() > 0 ? " AND " : "");
				sb.append(" ELF601_STATUS=? ");
				w1List.add(status);
			}
		}

		List<Map<String, Object>> rowData = this.getJdbc()
				.queryForListByCustParam("select",
						new Object[] { "ELF601", sb.toString() },
						w1List.toArray(), 0, Integer.MAX_VALUE,
						new EloanColumnMapRowMapper());

		List<ELF601> list = new ArrayList<ELF601>();
		for (Map<String, Object> row : rowData) {
			ELF601 model = new ELF601();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}

	@Override
	public ELF602 getElf602ByUnid(String unid) {
		Map<String, Object> rowData = this.getJdbc().queryForMap(
				"ELF602.getElf602ByUnid", new Object[] { unid });
		if (rowData == null) {
			return null;
		} else {
			ELF602 model = new ELF602();
			DataParse.map2Bean(rowData, model);
			return model;
		}
	}

	@Override
	public List<ELF601> getElf601ByUnidLike(String unid) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForListWithMax(
				"ELF601.getElf601ByUnidLike", new String[] { unid });

		List<ELF601> list = new ArrayList<ELF601>();
		for (Map<String, Object> row : rowData) {
			ELF601 model = new ELF601();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}

	@Override
	public List<ELF602> getElf602ByDatasrcLike(String datasrc) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForListWithMax(
				"ELF602.getElf602ByDatasrcLike", new String[] { datasrc });
		List<ELF602> list = new ArrayList<ELF602>();
		for (Map<String, Object> row : rowData) {
			ELF602 model = new ELF602();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}

	// J-110-0363 By ID
	@Override
	public List<ELF602> getElf602OnlyById(String custId, String dupNo,
			boolean undone, String brNo) {
		StringBuffer sb = new StringBuffer();
		List<String> w1List = new ArrayList<String>();
		sb.append(" ELf602_CUSTID=? ");
		w1List.add(custId);

		sb.append(" AND ELf602_DUPNO=? ");
		w1List.add(dupNo);

		sb.append(" AND ELF602_BR_NO=? ");
		w1List.add(brNo);

		sb.append(" AND ELF602_CNTRNO='' AND ELF602_LOAN_NO='' ");

		if (undone) {
			sb.append(" AND ELF602_STATUS IN ('1','2') ");
		}

		List<Map<String, Object>> rowData = this.getJdbc()
				.queryForListByCustParam("select",
						new Object[] { "ELF602", sb.toString() },
						w1List.toArray(), 0, Integer.MAX_VALUE,
						new EloanColumnMapRowMapper());

		List<ELF602> list = new ArrayList<ELF602>();
		for (Map<String, Object> row : rowData) {
			ELF602 model = new ELF602();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}

	@Override
	public List<ELF602> getElf602ByIdCntrNoLoanNo(String custId, String dupNo,
			String cntrNo, String loanNo, boolean undone, String brNo) {
		StringBuffer sb = new StringBuffer();
		List<String> w1List = new ArrayList<String>();
		sb.append(" ELf602_CUSTID=? ");
		w1List.add(custId);

		sb.append(" AND ELf602_DUPNO=? ");
		w1List.add(dupNo);

		sb.append(" AND ELF602_BR_NO=? ");
		w1List.add(brNo);

		if (cntrNo != null) {
			sb.append(" AND ELF602_CNTRNO=? ");
			w1List.add(cntrNo);
		}
		if (loanNo != null) {
			sb.append(" AND ELF602_LOAN_NO=? ");
			w1List.add(loanNo);
		}

		if (undone) {
			sb.append(" AND ELF602_STATUS IN ('1','2') ");
		}

		List<Map<String, Object>> rowData = this.getJdbc()
				.queryForListByCustParam("select",
						new Object[] { "ELF602", sb.toString() },
						w1List.toArray(), 0, Integer.MAX_VALUE,
						new EloanColumnMapRowMapper());

		List<ELF602> list = new ArrayList<ELF602>();
		for (Map<String, Object> row : rowData) {
			ELF602 model = new ELF602();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}

	@Override
	public List<ELF602> getElf602ByCntrNoLoanNo(String cntrNo, String loanNo,
			boolean undone, String brNo) {
		StringBuffer sb = new StringBuffer();
		List<String> w1List = new ArrayList<String>();
		sb.append(" ELF602_CNTRNO=? ");
		w1List.add(cntrNo);

		// 20200619 金襄理說 只選到額度層 => 只能撈 有額度沒帳號的
		// if(Util.isNotEmpty(loanNo)){
		sb.append(" AND ELF602_LOAN_NO=? ");
		w1List.add(loanNo);
		// }

		if (Util.isNotEmpty(brNo)) {
			sb.append(sb.length() > 0 ? " AND " : "");
			sb.append(" ELF602_BR_NO=? ");
			w1List.add(brNo);
		}

		if (undone) {
			sb.append(" AND ELF602_STATUS IN ('1','2') ");
		}

		List<Map<String, Object>> rowData = this.getJdbc()
				.queryForListByCustParam("select",
						new Object[] { "ELF602", sb.toString() },
						w1List.toArray(), 0, Integer.MAX_VALUE,
						new EloanColumnMapRowMapper());

		List<ELF602> list = new ArrayList<ELF602>();
		for (Map<String, Object> row : rowData) {
			ELF602 model = new ELF602();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}

	@Override
	public List<ELF602> getElf602ByDatasrc(String elf601Unid) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForListWithMax(
				"ELF602.getElf602ByDatasrc", new String[] { elf601Unid });
		List<ELF602> list = new ArrayList<ELF602>();
		for (Map<String, Object> row : rowData) {
			ELF602 model = new ELF602();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}

	@Override
	public List<ELF602> getElf602OnlyIdByFilter(String custId, String dupNo,
			String startDate, String endDate, String[] handlingStatus,
			String brNo) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

		StringBuffer sb = new StringBuffer();
		List<String> w1List = new ArrayList<String>();

		if (Util.isNotEmpty(custId)) {
			sb.append(" ELF602_CUSTID=? ");
			w1List.add(custId);
		}
		if (Util.isNotEmpty(dupNo)) {
			sb.append(sb.length() > 0 ? " AND " : "");
			sb.append(" ELF602_DUPNO=? ");
			w1List.add(dupNo);
		}
		if (Util.isNotEmpty(brNo)) {
			sb.append(sb.length() > 0 ? " AND " : "");
			sb.append(" ELF602_BR_NO=? ");
			w1List.add(brNo);
		}
		sb.append(sb.length() > 0 ? " AND " : "");
		sb.append(" ELF602_CNTRNO='' AND ELF602_LOAN_NO='' ");

		if (Util.isNotEmpty(startDate) && Util.isNotEmpty(endDate)) {
			sb.append(" AND ELF602_FO_DATE BETWEEN ? AND ? ");
			w1List.add(sdf.format(CapDate.getDate(startDate, "yyyy-MM-dd")));
			w1List.add(sdf.format(CapDate.getDate(endDate, "yyyy-MM-dd")));
		}

		StringBuffer sb2 = new StringBuffer();
		for (String status : handlingStatus) {
			if (Util.isEmpty(status)) {
				continue;
			}
			sb2.append(sb2.length() > 0 ? ", " : "");
			sb2.append("?");
			w1List.add(status);
		}
		if (sb2.length() > 0) {
			sb.append(" AND ELF602_STATUS IN ( ").append(sb2).append(" )");
		}

		List<Map<String, Object>> rowData = this.getJdbc()
				.queryForListByCustParam("select",
						new Object[] { "ELF602", sb.toString() },
						w1List.toArray(), 0, Integer.MAX_VALUE,
						new EloanColumnMapRowMapper());

		List<ELF602> list = new ArrayList<ELF602>();
		for (Map<String, Object> row : rowData) {
			ELF602 model = new ELF602();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}

	@Override
	public List<ELF602> getElf602ByFilter(String custId, String dupNo,
			String cntrNo, String loanNo, String startDate, String endDate,
			String[] handlingStatus, String brNo) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

		StringBuffer sb = new StringBuffer();
		List<String> w1List = new ArrayList<String>();

		if (Util.isNotEmpty(custId)) {
			sb.append(" ELF602_CUSTID=? ");
			w1List.add(custId);
		}
		if (Util.isNotEmpty(dupNo)) {
			sb.append(sb.length() > 0 ? " AND " : "");
			sb.append(" ELF602_DUPNO=? ");
			w1List.add(dupNo);
		}
		if (Util.isNotEmpty(brNo)) {
			sb.append(sb.length() > 0 ? " AND " : "");
			sb.append(" ELF602_BR_NO=? ");
			w1List.add(brNo);
		}
		if (Util.isNotEmpty(cntrNo)) {
			sb.append(sb.length() > 0 ? " AND " : "");
			sb.append(" ELF602_CNTRNO=? ");
			w1List.add(cntrNo);
		}
		if (Util.isNotEmpty(loanNo)) {
			sb.append(sb.length() > 0 ? " AND " : "");
			sb.append(" ELF602_LOAN_NO=? ");
			w1List.add(loanNo);
		}
		if (Util.isNotEmpty(startDate) && Util.isNotEmpty(endDate)) {
			sb.append(" AND ELF602_FO_DATE BETWEEN ? AND ? ");
			w1List.add(sdf.format(CapDate.getDate(startDate, "yyyy-MM-dd")));
			w1List.add(sdf.format(CapDate.getDate(endDate, "yyyy-MM-dd")));
		}

		StringBuffer sb2 = new StringBuffer();
		for (String status : handlingStatus) {
			if (Util.isEmpty(status)) {
				continue;
			}
			sb2.append(sb2.length() > 0 ? ", " : "");
			sb2.append("?");
			w1List.add(status);
		}
		if (sb2.length() > 0) {
			sb.append(" AND ELF602_STATUS IN ( ").append(sb2).append(" )");
		}

		// String sql = sqlp.getValue("ELF602.getElf602ByFilter",
		// "ELF602.getElf602ByFilter");
		// sql = MessageFormat.format(sql, new Object[] { sb.toString() });
		//
		// List<Map<String, Object>> rowData =
		// this.getJdbc().queryAllForList(sql, w1List.toArray());

		List<Map<String, Object>> rowData = this.getJdbc()
				.queryForListByCustParam("select",
						new Object[] { "ELF602", sb.toString() },
						w1List.toArray(), 0, Integer.MAX_VALUE,
						new EloanColumnMapRowMapper());

		List<ELF602> list = new ArrayList<ELF602>();
		for (Map<String, Object> row : rowData) {
			ELF602 model = new ELF602();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}

	@Override
	public void deleteELF601(String unid) {
		// DELETE FROM MIS.ELF601 WHERE ELF601_UNID = ?
		this.getJdbc().update("ELF601.delete", new String[] { unid });
	}

	@Override
	public void deleteELF602(String unid) {
		// DELETE FROM MIS.ELF602 WHERE ELF602_UNID = ?
		this.getJdbc().update("ELF602.delete", new String[] { unid });
	}

	@Override
	public void insertELF601(ELF601 elf601) {
		this.getJdbc()
				.update("ELF601.insert",
						new Object[] { elf601.getElf601_unid(),
								elf601.getElf601_cntrno(),
								elf601.getElf601_loan_no(),
								elf601.getElf601_loan_kind(),
								elf601.getElf601_fo_kind(),
								elf601.getElf601_fo_content(),
								elf601.getElf601_fo_way(),
								elf601.getElf601_fo_cycle(),
								elf601.getElf601_fo_beg_date(),
								elf601.getElf601_fo_end_date(),
								elf601.getElf601_fo_next_date(),
								elf601.getElf601_staff(),
								elf601.getElf601_fo_staffNo(),
								elf601.getElf601_ao_staffNo(),
								elf601.getElf601_status(),
								elf601.getElf601_cre_date(),
								elf601.getElf601_cre_teller(),
								elf601.getElf601_cre_supvno(),
								elf601.getElf601_upd_date(),
								elf601.getElf601_upd_teller(),
								elf601.getElf601_upd_supvno(),
								elf601.getElf601_full_content(),
								elf601.getElf601_custid(),
								elf601.getElf601_dupno(),
								elf601.getElf601_br_no(),
								elf601.getElf601_case_mark(),
								elf601.getElf601_suid(),
								elf601.getElf601_sapptime(),
								elf601.getElf601_sseqno() });
	}

	@Override
	public void insertELF602(ELF602 elf602) {
		this.getJdbc().update(
				"ELF602.insert",
				new Object[] { elf602.getElf602_unid(),
						elf602.getElf602_cntrno(), elf602.getElf602_loan_no(),
						elf602.getElf602_loan_kind(),
						elf602.getElf602_fo_kind(),
						elf602.getElf602_fo_content(),
						elf602.getElf602_staff(), elf602.getElf602_fo_date(),
						elf602.getElf602_chkdate(),
						elf602.getElf602_conform_fg(),
						elf602.getElf602_fo_memo(), elf602.getElf602_status(),
						elf602.getElf602_datasrc(),
						elf602.getElf602_unusual_fg(),
						elf602.getElf602_unusualdesc(),
						elf602.getElf602_isnotional(),
						elf602.getElf602_isaml(), elf602.getElf602_upd_date(),
						elf602.getElf602_upd_teller(),
						elf602.getElf602_upd_supvno(),
						elf602.getElf602_full_content(),
						elf602.getElf602_fieldMainId(),
						elf602.getElf602_fileDesc(), elf602.getElf602_custid(),
						elf602.getElf602_dupno(), elf602.getElf602_br_no(),
						elf602.getElf602_case_mark(),
                        elf602.getElf602_suid(),
                        elf602.getElf602_sapptime(),
                        elf602.getElf602_sseqno(),
                        elf602.getElf602_sesgsunre()
				});
	}

    @Override
    public void insertELF602(List<ELF602> elf602List) {
		for(ELF602 newelf602 : elf602List) {
			insertELF602(newelf602);
		}
    }

    @Override
	public void updateELF601Status(String unid, String status) {
		this.getJdbc().update("ELF601.updateELF601Status",
				new Object[] { status, unid });
	}

	@Override
	public void updateELF602StatusAndMemo(String unid, String status,
			String memo) {
		this.getJdbc().update("ELF602.updateELF602StatusAndMemo",
				new Object[] { status, memo, unid });
	}

	@Override
	public List<Map<String, Object>> getLandOrBuildLoanCntrNoDataForMonthlyReport(
			String branchNo) {

		return !CapString.isEmpty(branchNo) ? this
				.getJdbc()
				.queryForListWithMax(
						"CLS180R50.getLandOrBuildLoanCntrNoDataForMonthlyReportByBranchNo",
						new Object[] { branchNo })
				: this.getJdbc()
						.queryForListWithMax(
								"CLS180R50.getLandOrBuildLoanCntrNoDataForMonthlyReport",
								new Object[] {});
	}

	@Override
	public List<Map<String, Object>> findCLS1220R07_3_1(String ownBrId,
			String createTimeSince) {
		return this.getJdbc().queryForListWithMax(
				"findCLS1220R07_3_1",
				new Object[] { ownBrId + "%",
						StringUtils.substring(createTimeSince, 0, 10) });
	}

	@Override
	public List<Map<String, Object>> findCLS1220R07_3_2(String ownBrId,
			String createTimeSince) {
		return this.getJdbc().queryForListWithMax("findCLS1220R07_3_2",
				new Object[] { ownBrId + "%", createTimeSince });
	}

	@Override
	public List<Map<String, Object>> findCLS1220R07_3_3(String ownBrId,
			String createTimeSince) {
		return this.getJdbc().queryForListWithMax(
				"findCLS1220R07_3_3",
				new Object[] { ownBrId + "%",
						StringUtils.substring(createTimeSince, 0, 10) });
	}

	@Override
	public Map<String, Object> getCountryCrd(String country) {
		Date nowDate = CapDate.getCurrentTimestamp();
		String today = CapDate.formatDate(nowDate, "yyyy-MM-dd");
		String exday = CapDate.formatDate(CapDate.addMonth(nowDate, -1),
				"yyyy-MM-dd");
		return this.getJdbc().queryForMap("ELF679.queryCountryCrd",
				new Object[] { country, exday, today });
	}

	@Override
	public BigDecimal findLnf25cByType7(String crdType, String crdGrade) {
		BigDecimal result = BigDecimal.ZERO;
		if (Util.isEmpty(crdType) || Util.isEmpty(crdGrade)) {
			return result;
		} else {
			// UtilConstants.crdTypeN.SP
			if (Util.equals("NS", crdType)) {
				crdType = "S&P";
			} else if (Util.equals("NM", crdType)) {
				crdType = "Moodys";
			} else if (Util.equals("NF", crdType)) {
				crdType = "Fitch";
			} else if (Util.equals("NT", crdType)) {
				//J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等
				crdType = "FitchTW";
			} else if (Util.equals("NK", crdType)) {
				//J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等
				crdType = "KBRA";	
			} else {
				
				return result;
			}

			// S&P 標準普爾 Moodys 穆迪信評 Fitch 惠譽信評
			Map<String, Object> queryForMap = this.getJdbc().queryForMap(
					"LNF25C.queryValue1ByType7",
					new Object[] { crdType, crdGrade });

			if (queryForMap != null) {
				result = Util.parseBigDecimal(queryForMap
						.get("LNF25C_MAP_VALUE1"));
			}
		}

		return result;
	}

	/**
	 * J-108-0345 貸後管理 最新一筆維護資料之附件檔案
	 */
	@Override
	public void updateLatestELF602(String oid, String unid) {
		this.getJdbc()
				.update("ELF602.updateLatest", new Object[] { oid, unid });
	}

	/**
	 * J-108-0345 貸後管理 最新一筆維護資料之證明文件說明
	 */
	@Override
	public List<Map<String, Object>> queryElf602HadMaintainList() {
		return this.getJdbc().queryForListWithMax("ELF602.selHadMaintain",
				new Object[] {});
	}

	@Override
	public void updateLatestELF602FileDesc(String fileDesc, String unid) {
		this.getJdbc().update("ELF602.updateLatestFileDesc",
				new Object[] { fileDesc, unid });
	}

	@Override
	public List<Map<String, Object>> queryElf602List(ISearch pageSetting,
			String ownBrId, String fo_staffNo, String ao_staffNo) {
		// J-113-0150 配合分行，E-LOAN授信管理系統修改建檔維護-貸後管理追蹤檢核表維護-查詢未完成案件等。
		// 主機欄位如果首字可能有英文、數字，請先使用ascii(欄位名稱)排序、再以該欄位排序，避免排序亂序問題
		Map<String, Boolean> oriOrderMap = pageSetting.getOrderBy();
		Map<String, Boolean> newOrderMap = new LinkedHashMap<String, Boolean>();
		Map<String, String> replaceKeyMap = new LinkedHashMap<String, String>();
		replaceKeyMap.put("custId", "ascii(custId)");
		replaceKeyMap.put("cntrNo", "ascii(cntrNo)");
		replaceKeyMap.put("loanNo", "ascii(loanNo)");
		for (String key : oriOrderMap.keySet()) {
			if (replaceKeyMap.containsKey(key)) {
				newOrderMap.put(replaceKeyMap.get(key), oriOrderMap.get(key));
			}
			newOrderMap.put(key, oriOrderMap.get(key));
		}
		pageSetting.setOrderBy(newOrderMap);

		String orderByStr = Util.getOrderData(pageSetting.getOrderBy());
		List<Object> params = new ArrayList<Object>();
		params.add(ownBrId);

		StringBuffer sql = new StringBuffer();
		if (Util.isNotEmpty(fo_staffNo)) {
			if (sql.length() != 0) {
				sql.append(" OR ");
			}			
			sql.append(" RIGHT('000000'||ELF601_FO_STAFFNO,6) = ? ");
			params.add(fo_staffNo);
		}

		if (Util.isNotEmpty(ao_staffNo)) {
			if (sql.length() != 0) {
				sql.append(" OR ");
			}
			sql.append(" RIGHT('000000'||ELF601_AO_STAFFNO,6)  = ? ");
			params.add(ao_staffNo);
		}

		if (Util.isNotEmpty(fo_staffNo)) {
			if (sql.length() != 0) {
				sql.append(" OR ");
			}
			sql.append(" RIGHT('000000'||LNF020_FO_STAFFNO,6) = ? ");
			params.add(fo_staffNo);
		}

		if (Util.isNotEmpty(ao_staffNo)) {
			if (sql.length() != 0) {
				sql.append(" OR ");
			}
			sql.append(" RIGHT('000000'||LNF020_AO_STAFFNO,6) = ? ");
			params.add(ao_staffNo);
		}

		if (sql.length() == 0) {
			sql.append(" 1=1 ");
		}
		sql.insert(0, "(");
		sql.append(")");

		return this.getJdbc().queryForAllListByCustParam("queryElf602List",
				new Object[] { sql.toString(), orderByStr }, params.toArray());
	}

	@Override
	public String getMaxDateBetweenDrawdownEndDateAndCreditPeriodEndDateByLnf020(
			String contract) {
		List<Map<String, Object>> list = this
				.getJdbc()
				.queryForListWithMax(
						"getMaxDateBetweenDrawdownEndDateAndCreditPeriodEndDateByLnf020",
						new Object[] { contract });
		return list.isEmpty() ? null : (String) list.get(0).get("MAXDATE");
	}

	@Override
	public List<Map<String, Object>> getElf517hBetweenStartDateAndEndDate(
			String startDate, String endDate) {
		return this.getJdbc().queryForListWithMax(
				"getElf517hBetweenStartDateAndEndDate",
				new Object[] { startDate, endDate });
	}

	@Override
	public int job_J_109_0344_step1() {
		return this.getJdbc().update("job_J_109_0344_step1", null);
	}

	@Override
	public int job_J_109_0344_step2() {
		return this.getJdbc().update("job_J_109_0344_step2", null);
	}

	@Override
	public List<Map<String, Object>> crs_chose_J_109_0372_1st() {
		return this.getJdbc().queryForListWithMax("crs_chose_J_109_0372_1st",
				null);
	}

	@Override
	public List<Map<String, Object>> crs_chose_J_109_0372_byBatchNo(
			String LNF07A_KEY_1) {
		return this.getJdbc()
				.queryForListWithMax("crs_chose_J_109_0372_byBatchNo",
						new String[] { LNF07A_KEY_1 });
	}

	@Override
	public boolean chkHadReview(String custId, String dupNo) {
		String fullCustKey = Util.getLeftStr(custId + "          ", 10) + dupNo;
		List<Map<String, Object>> mapList = this.getJdbc().queryForListWithMax(
				"chkHadReview", new String[] { fullCustKey });
		if (mapList.isEmpty()) {
			return false;
		} else {
			return true;
		}
	}

	/**
	 * 更新主從債務人最佳信用品質保證人的國別註記
	 * 
	 * @return
	 */
	@Override
	public List<Map<String, Object>> getLnf020WithEllngtee() {
		return this.getJdbc().queryForListWithMax("MIS.selLnf020WithEllngtee",
				null);
	}

	/**
	 * 最新內部評等(配合國家風險更新主從債務人信用品質保證人優先註記)
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	@Override
	public Map<String, Object> getMowtbl1LastFrByCustIdForCountryRisk(
			String custId, String dupNo) {

		return this.getJdbc().queryForMap(
				"MOWTBL1.selLastFrByCustIdForCountryRisk",
				new Object[] { custId, dupNo });

	}

	/**
	 * 內部評等等級轉換外部評等分數
	 * 
	 * @param type
	 * @param fr
	 * @return
	 */
	@Override
	public Map<String, Object> getLnf25cMowScoreMapping(String type, String fr) {

		return this.getJdbc().queryForMap("LNF25C.getMowScoreMapping",
				new Object[] { type, fr });

	}

	/**
	 * 取得TYPE所有內部評等等級轉換外部評等分數
	 * 
	 * @return
	 */
	@Override
	public List<Map<String, Object>> getLnf25cAllMowScoreMappingByType(
			String type) {
		return this.getJdbc().queryForListWithMax(
				"LNF25C.getAllMowScoreMappingByType", new Object[] { type });
	}

	@Override
	public boolean decide_cls_hasHouseLoan(String custId, String dupNo) {
		return this
				.getJdbc()
				.queryForList("LNF154.cls_hasHouseLoan",
						new String[] { custId, dupNo }).size() > 0;
	}

	@Override
	public List<Map<String, Object>> lnf154_cls_all_loan(String custId,
			String dupNo) {
		return this.getJdbc().queryForListWithMax("LNF154.cls_all_loan",
				new String[] { custId, dupNo });
	}

	@Override
	public void batchUpdate_J_111_0022_LNACNTR_CLSA(List<Object[]> batchValues) {
		this.getJdbc().batchUpdate("run_J-111-0022_LNACNTR_CLSA",
				new int[] { Types.DECIMAL, Types.CHAR, Types.CHAR },
				batchValues);
	}

	@Override
	public void batchUpdate_J_111_0022_LNACNTR_CLSB(List<Object[]> batchValues) {
		this.getJdbc().batchUpdate("run_J-111-0022_LNACNTR_CLSB",
				new int[] { Types.DECIMAL, Types.DECIMAL, Types.CHAR },
				batchValues);
	}

	// @Override
	// public List<Map<String, Object>>
	// getValidGroupLoanBuildCaseMasterDataByApprovedGrpCntrno(String
	// grpCntrnoStr) {
	// return this.getJdbc().queryForListByCustParam(
	// "PTEAMAPP.getValidGroupLoanBuildCaseMasterDataByApprovedGrpCntrno", new
	// Object[]{grpCntrnoStr}, new Object[]{});
	// }

	/**
	 * J-110-0000_05097_B1001 配合2021/02專案金檢，產生擔保品檔案
	 */
	@Override
	public List<Map<String, Object>> doLmsBatch0037_01(String bgnMonth,
			String endMonth) {

		return this.getJdbc().queryForListWithMax("doLmsBatch0037.01",
				new Object[] { bgnMonth, endMonth });
	}

	/**
	 * J-110-0000_05097_B1001 配合2021/02專案金檢，產生擔保品檔案
	 * 
	 * @param cntrNo
	 * @return
	 */
	@Override
	public List<Map<String, Object>> doLmsBatch0037_02(String cntrNo,
			String dataYm) {

		return this.getJdbc().queryForListWithMax(
				"doLmsBatch0037.02." + dataYm, new Object[] { cntrNo });
	}

	/**
	 * J-110-0000_05097_B1001 配合2021/02專案金檢，產生擔保品檔案
	 * 
	 * @param cntrNo
	 * @return
	 */
	@Override
	public List<Map<String, Object>> doLmsBatch0037_03(String cntrNo,
			String dataYm) {

		return this.getJdbc().queryForListWithMax(
				"doLmsBatch0037.03." + dataYm, new Object[] { cntrNo });
	}

	/**
	 * J-110-0000_05097_B1001 配合2021/02專案金檢，產生擔保品檔案
	 * 
	 * @param cntrNo
	 * @return
	 */
	@Override
	public List<Map<String, Object>> doLmsBatch0037_04(String cntrNo,
			String dataYm) {

		return this.getJdbc().queryForListWithMax(
				"doLmsBatch0037.04." + dataYm, new Object[] { cntrNo });
	}

	/**
	 * J-110-0000_05097_B1001 配合2021/02專案金檢，產生擔保品檔案
	 * 
	 * @param cntrNo
	 * @return
	 */
	@Override
	public List<Map<String, Object>> doLmsBatch0037_05(String cntrNo,
			String dataYm) {

		return this.getJdbc().queryForListWithMax(
				"doLmsBatch0037.05." + dataYm, new Object[] { cntrNo });
	}

	/**
	 * J-110-0000_05097_B1001 配合2021/02專案金檢，產生擔保品檔案
	 * 
	 * @param upCntrNo
	 * @param upCity
	 * @param upTimeVal
	 * @param upAddr
	 * @param upCount
	 * @param upIsZu
	 */
	@Override
	public void insertDoLmsBatch0037(String dbName, String upCntrNo,
			String upCity, BigDecimal upTimeVal, String upAddr, int upCount,
			String upIsZu, String dataYm) {

		if (Util.equals(dbName, "ELFLL")) {
			// 擔保品 - 土地 - 活 MIS.ELFLL
			this.getJdbc().update(
					"doLmsBatch0037.insertELFLL",
					new Object[] { upCntrNo, upCity, upTimeVal, upAddr,
							upCount, upIsZu, dataYm });
		} else if (Util.equals(dbName, "ELFLD")) {
			// 擔保品 - 土地 - 死 MIS.ELFLD
			this.getJdbc().update(
					"doLmsBatch0037.insertELFLD",
					new Object[] { upCntrNo, upCity, upTimeVal, upAddr,
							upCount, upIsZu, dataYm });
		} else if (Util.equals(dbName, "ELFBL")) {
			// 擔保品 - 建物 - 活 MIS.ELFBL
			this.getJdbc().update(
					"doLmsBatch0037.insertELFBL",
					new Object[] { upCntrNo, upCity, upTimeVal, upAddr,
							upCount, upIsZu, dataYm });
		} else if (Util.equals(dbName, "ELFBD")) {
			// 擔保品 - 建物 - 死 MIS.ELFBD
			this.getJdbc().update(
					"doLmsBatch0037.insertELFBD",
					new Object[] { upCntrNo, upCity, upTimeVal, upAddr,
							upCount, upIsZu, dataYm });
		} else if (Util.equals(dbName, "ELFAA")) {
			// 擔保品 - 最終整理檔 MIS.ELFAA
			this.getJdbc().update(
					"doLmsBatch0037.insertELFAA",
					new Object[] { upCntrNo, upCity, upTimeVal, upAddr,
							upCount, upIsZu, dataYm });
		}

	}

	/**
	 * LMS180R63 境內法人於國際金融業務分行辦理外幣授信業務報表
	 * 
	 * J-110-0049_05097_B1001 Web e-Loan企金授信增加「境內法人於國際金融業務分行辦理外幣授信業務報表」
	 * 
	 * @return
	 */
	@Override
	public List<Map<String, Object>> queryLMS180R63Data(String bgnDate,
			String endDate) {

		return this.getJdbc().queryForListWithMax("rpt.LMS180R63",
				new String[] { bgnDate, endDate });

	}

	// @Override
	// public List<Map<String, Object>>
	// getValidGroupLoanBuildCaseMasterDataByApprovedGrpCntrno(String
	// grpCntrnoStr) {
	// return this.getJdbc().queryForListByCustParam(
	// "PTEAMAPP.getValidGroupLoanBuildCaseMasterDataByApprovedGrpCntrno", new
	// Object[]{grpCntrnoStr}, new Object[]{});
	// }

	@Override
	public Map<String, Object> findIndustryObjectCodeByBSTBL(String code) {
		return this.getJdbc().queryForMap("BSTBL.findByECOCD",
				new Object[] { code });
	}

	@Override
	public Map<String, Object> findIndustryObjectCodeByBSTBL_mark(String code,
			String mark) {
		return this.getJdbc().queryForMap("BSTBL.findByECOCD_MARK",
				new Object[] { code, mark });
	}

	/**
	 * 南京東路客戶移轉國外部
	 * 
	 * @param aLoanDate
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findLnf078T(String aLoanDate) {
		if (Util.equals(Util.trim(aLoanDate), "")) {
			return this.getJdbc().queryForListWithMax("LNF078T.selectAll",
					new Object[] {});
		} else {
			return this.getJdbc()
					.queryForListWithMax("LNF078T.selectAllByChangeDate",
							new Object[] { aLoanDate });
		}

	}

	@Override
	public Map<String, Object> findLnf020AndLnf030DataByCntrNo(String cntrNo) {
		return this.getJdbc().queryForMap("LN.LNF020.selLnf030DataByCntrNo",
				new Object[] { cntrNo });
	}

	@Override
	public List<Map<String, Object>> findSmallBussCDataByCustId(String custId) {

		return this.getJdbc().queryForListWithMax(
				"rpa.getSmallBussCDataByCustId", new String[] { custId });

	}

	/**
	 * CLS180R42特定金錢信託案件量統計報表，依額度序號取授信餘額
	 * 
	 * @return
	 */
	@Override
	public BigDecimal queryLNF022getLOANBAL(String contract) {
		Map<String, Object> data = this.getJdbc().queryForMap(
				"LNF022.getLOANBAL", new Object[] { contract });
		if (data != null && !data.isEmpty()) {
			BigDecimal loanBal = (BigDecimal) data.get("LOAN_BAL");
			return loanBal;
		}
		return BigDecimal.ZERO;
	}

	/**
	 * J-110-0272 該客戶所有額度下最新簽報的授信科目
	 */
	@Override
	public Map<String, Object> findQUOTSUBlatestLoanTPs(String custId,
			String dupNo, String date) {
		StringBuffer sb = new StringBuffer();
		List<Object> params = new ArrayList<Object>();
		params.add(custId);
		params.add(dupNo);

		if (Util.isNotEmpty(date)) { // 格式為 YYYY-MM-DD
			sb.append(" AND SDATE <= ? ");
			params.add(date);
		}

		String fullCustId = Util.addSpaceWithValue(custId, 10) + dupNo;
		params.add(fullCustId);
		List<Map<String, Object>> rowData = this.getJdbc()
				.queryForListByCustParam(
						"mis.quotsub.findLatestLoanTPsByCustId",
						new Object[] { sb.toString() },
						params.toArray(new Object[0]), 0,
						Integer.MAX_VALUE, new EloanColumnMapRowMapper());

		if (rowData.size() > 0) {
			return rowData.get(0);
		}
		return null;

		// return
		// this.getJdbc().queryForMap("mis.quotsub.findLatestLoanTPsByCustId",
		// new String[] { custId, dupNo });
	}

	/**
	 * CLS180R42特定金錢信託案件量統計報表，依額度序號查找是否有已完成覆核的企金動審表(ELF383)或消金動審表(ELF500)
	 * 
	 * @return
	 */
	@Override
	public int queryCLS180R42Data1(String contract) {
		Map<String, Object> data = this.getJdbc().queryForMap(
				"rpt.CLS180R42_01", new Object[] { contract, contract });
		int cnt = (Integer) data.get("CNT");
		return cnt;

	}

	/**
	 * CLS180R42特定金錢信託案件量統計報表，依額度序號取放款帳號、利率、首次動撥金額
	 * 
	 * @return
	 */
	@Override
	public List<Map<String, Object>> queryCLS180R42Data2(String contract) {
		return this.getJdbc().queryForListWithMax("rpt.CLS180R42_02",
				new String[] { contract });

	}

	/**
	 * J-110-0304_05097_B1001 Web e-Loan授信覆審配合RPA作業修改
	 * 
	 * @return
	 */
	@Override
	public List<Map<String, Object>> queryLnf09gByTxnCodeAndKey2(
			String txnCode, String bgnDate, String endDate) {
		return this.getJdbc().queryForListWithMax("LNF09G.selByTxnCodeAndKey2",
				new String[] { txnCode, bgnDate, endDate });

	}

	@Override
	public List<Map<String, Object>> findlnf025CoByCntrNoForLgd(
			List<String> cntrNos) {

		String cntrNoParams = Util.genSqlParam(cntrNos);
		List<Object> params = new ArrayList<Object>();
//		StringBuilder sb = new StringBuilder("'");
//		for (String cntrNo : cntrNos) {
//			sb.append(sb.length() > 1 ? "','" : "").append(cntrNo);
//		}
//		sb.append("'");
		params.addAll(cntrNos);
		params.addAll(cntrNos);
		// ~~~
		return this.getJdbc().queryForAllListByCustParam(
				"LNF025.selCoByCntrNoForLgd",
				new String[] { cntrNoParams }, params.toArray(new Object[0]));

	}

	// LGD
	@Override
	public List<Map<String, Object>> findlnf025ByCntrNoCo(String cntrNoCo) {
		return this.getJdbc().queryForListWithMax("LNF025.selByCntrNoCo",
				new String[] { cntrNoCo });
	}

	@Override
	public Map<String, Object> findLnf252ByCntrNo(String cntrNo) {
		return this.getJdbc().queryForMap("LNF252.selByCntrNoForLgd",
				new Object[] { cntrNo, cntrNo });
	}

	@Override
	public Map<String, String> selStockCompanyId(String stockNum) {
		String stkCMP = "";
		StringBuffer sb = new StringBuffer();

		if (Util.isNotEmpty(stockNum)) { // 格式為 YYYY-MM-DD
			sb.append(" AND STKNO = '").append(stockNum).append("'");
		}

		List<Map<String, Object>> listMap = this.getJdbc()
				.queryForListByCustParam("MISSTKDATA.selStock3",
						new Object[] { sb.toString() }, new Object[] {}, 0,
						Integer.MAX_VALUE, new EloanColumnMapRowMapper());

		Map<String, String> r = new LinkedHashMap<String, String>();
		if (!listMap.isEmpty()) {
			for (Map<String, Object> rstMap : listMap) {
				r.put(Util.trim(rstMap.get("STKNO")),
						Util.trim(rstMap.get("STKCMP")));
			}
		}

		return r;
	}

	@Override
	public Map<String, Object> findLnf447nByCntrNoAndStatusAndUuid(String cntrNo) {
		return this.getJdbc().queryForMap(
				"LNF447N.getLnf447nByCntrNoAndStatusAndUuid",
				new Object[] { cntrNo });
	}

	@Override
	public List<Map<String, Object>> getUnsoldHouseDataByUncancelledAndRemainingHouses(
			String toDate) {
		return this.getJdbc().queryForList(
				"ELF517.getUnsoldHouseDataByUncancelledAndRemainingHouses",
				new Object[] { toDate, toDate });
	}

	@Override
	public List<Map<String, Object>> getUnsoldHouseTrackReportDataFor918() {
		return this.getJdbc().queryForList(
				"ELF517.getUnsoldHouseTrackReportDataFor918", new Object[] {});
	}

	@Override
	public List<Map<String, Object>> getUnsoldHouseTrackReportDataForBranch(
			String branchCode) {
		return this.getJdbc().queryForList(
				"ELF517.getUnsoldHouseTrackReportDataForBranch",
				new Object[] { branchCode });
	}

	@Override
	public List<Map<String, Object>> queryList_for_cls_l120s06b_type2_orderByRate(
			String prodKind, String c900m01d_subjCode2, String lnf030_use_type,
			String endDateBeg, int rtn_cnt) {
		int fetch_cnt = rtn_cnt + 20; // 若聯貸子戶, 會抓到N個31的帳號 =>
										// 在回傳時，1個「額度序號」若有2個帳號，只回傳1筆
		List<Map<String, Object>> raw_list = this.getJdbc().queryForList(
				"queryList_for_cls_l120s06b_type2_orderByRate",
				new String[] { prodKind, c900m01d_subjCode2, lnf030_use_type,
						endDateBeg }, 0, fetch_cnt);
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		Set<String> cntrNo_set = new HashSet<String>();
		for (Map<String, Object> row_data : raw_list) {
			String cntrNo = Util.trim(MapUtils.getString(row_data,
					"LNF154_CONTRACT"));
			if (cntrNo_set.contains(cntrNo)) {
				continue;
			} else {
				cntrNo_set.add(cntrNo);
				list.add(row_data);
			}
			if (list.size() >= rtn_cnt) {
				break;
			}
		}

		return list;
	}

	@Override
	public String getLatestFirstLoanDateByLnf030Cntrno(String cntrNo) {

		Map<String, Object> map = this.getJdbc().queryForMap(
				"LN.LNF030.findLatestFirstLoanDateByLNF030_CONTRACT",
				new Object[] { cntrNo });

		if (map != null) {
			return String.valueOf(map.get("LNF030_LOAN_DATE"));
		}

		return null;
	}

	@Override
	public List<Map<String, Object>> getElf516UneditedDataAndCheckTimesLessThan3times(
			String checkTimeYYYYMM) {
		return this.getJdbc().queryForListWithMax(
				"MIS.ELF516.findUneditedDataAndCheckTimesLessThan3times",
				new Object[] { checkTimeYYYYMM });
	}

	@Override
	public void updateElf516CheckTimes(Integer checkTimes, String cntrNo,
			String custId, String dupNo, String yyymm, String status) {
		this.getJdbc()
				.update("MIS.ELF516_updateCheckTimes_checkTime",
						new Object[] { checkTimes, cntrNo, custId, dupNo,
								yyymm, status });
	}

	@Override
	public List<Map<String, Object>> getELF516UneditedDataAndCheckTimesLessThan3timesByYYYYMM(
			String yyyyMM, String checkTimeYYYYMM) {
		return this
				.getJdbc()
				.queryForListWithMax(
						"MIS.ELF516.findUneditedDataAndCheckTimesLessThan3timesByYYYYMM",
						new Object[] { yyyyMM, checkTimeYYYYMM });
	}

	@Override
	public List<Map<String, Object>> getELFDELYDEarliestOverdueDataByCustId_dupNo_Contract(
			String custId, String dupNo, String cntrNo) {
		return this.getJdbc().queryForList(
				"MIS.ELFDELYD.findEarliestOverdueDataByCustId_contract",
				new Object[] { custId + dupNo, cntrNo });
	}

	@Override
	public List<Map<String, Object>> getLmsPlantLoanTrackingData() {
		return this.getJdbc().queryForList("MIS.findLmsPlantLoanTrackingData",
				new Object[] {});
	}

	@Override
	public Map<String, Object> getLNF320ByNextBusinessDay(
			String lnf320NextDate_yyyy_MM_dd) {
		return this.getJdbc().queryForMap("LN.LNF320.getByLnf320NextDate",
				new String[] { lnf320NextDate_yyyy_MM_dd });
	}

	/**
	 * J-111-0423_05097_B1001 Web
	 * e-Loan企金授信就海外分行承做永續績效連結授信案(如附件)，於E-Loan「永續績效連結授信」相關註記
	 * 
	 */
	@Override
	public void updateEsgDataFromLms2105v01ServiceImpl(String cntrNo,
			String esgSustainLoan, String esgSustainLoanType,
			String esgSustainLoanUnReach) {
		this.getJdbc()
				.update("UPDATE MIS.QUOTAPPR SET ESGSFG = ?,ESGSTYPE = ?,ESGSUNRE=? WHERE CNTRNO =? ",
						new Object[] { esgSustainLoan, esgSustainLoanType,
								esgSustainLoanUnReach, cntrNo });

	}

	@Override
	public List<Map<String, Object>> getELF516EditedDataLastSpecifiedMonth(
			String yyyyMM) {
		return this.getJdbc().queryForList(
				"MIS.ELF516.findEditedDataLastSpecifiedMonth",
				new Object[] { yyyyMM });
	}

	@Override
	public List<Map<String, Object>> getUnconEmptyLandDataByActStDateAndUncancelled() {
		return this.getJdbc().queryForList(
				"ELF600.getUnconEmptyLandDataByActStDateAndUncancelled",
				new Object[] {});
	}

	/**
	 * J-111-0551 在途授信額度
	 */
	@Override
	public List<Map<String, Object>> selElAmt(String custId, String dupNo) {
		return this.getJdbc().queryForListWithMax("MIS.ELF447N.selElAmt",
				new Object[] { custId, dupNo });
	}

	/**
	 * J-112-0342 新增產生企金授信簽報案件明細檔
	 */
	@Override
	public List<Map<String, Object>> listLMS180R75_mis(String bgnDate, String endDate) {
		return this.getJdbc().queryForListWithMax("MIS.ELF447N.selLmsCase",
				new Object[] { bgnDate, endDate });
	}

	@Override
	public Map<String, Object> getLNF022_loanBalSByid(String id) {
		return this.getJdbc().queryForMap("LNF022.LOAN_BAL_S_BYID",
				new String[] { id });
	}

	@Override
	public Map<String, Object> getLNF022_loanBalNByid(String id) {
		return this.getJdbc().queryForMap("LNF022.LOAN_BAL_N_BYID",
				new String[] { id });
	}

	/**
	 * J-111-0633_05097_B1001 Web e-Loan授信系統不動產暨72-2相關資訊註記維護之頁面，增列補鍵產品種類33、34之功能
	 * 
	 * @param cntrNo
	 * @param prodKind
	 * @param adcCaseNo
	 */
	@Override
	public void updateAdcInfo(String cntrNo, String prodKind, String adcCaseNo) {
		this.getJdbc().update("MIS.ELF447N.update_PROD_CLASS_ByCntrNo",
				new Object[] { prodKind, cntrNo });
		this.getJdbc().update("MIS.QUOTAPPR.update_PROD_KIND_ByCntrNo",
				new Object[] { prodKind, cntrNo });
		this.getJdbc().update("MIS.ELF506.update_PROD_KIND_ByCntrNo",
				new Object[] { prodKind, adcCaseNo, cntrNo });

	}

	@Override
	public List<Map<String, Object>> getIntroductionSrcIsNotNullData() {
		return this.getJdbc().queryForListWithMax(
				"getIntroductionSrcIsNotNullData", new String[] {});
	}

	@Override
	public void insertLNF13E(String contractNo, String megaEmpNo,
			String agntNo, String agntChain, String megaCode, String subUnitNo,
			String subEmpNo, String subEmpNm, String introSrc, String megaEmpBrn) {
		this.getJdbc().update(
				"LNF13E.insert",
				new Object[] { contractNo, megaEmpNo, agntNo, agntChain,
						megaCode, subUnitNo, subEmpNo, subEmpNm, introSrc,
						megaEmpBrn });
	}
	
	@Override
	public void deleteLNF13EByKey(String contractNo){
		this.getJdbc().update(
				"LNF13E.delete_by_key",
				new Object[] { contractNo });
	}

	@Override
	public BigDecimal getSingleBankManLimitAmount() {

		Map<String, Object> map = getJdbc().queryForMap(
				"LNF070.getSingleBankManLimitAmount", new String[] {});
		if (map == null) {
			return new BigDecimal("********");
		}

		return new BigDecimal(map.get("LNF070_TYPE").toString());
	}

	@Override
	public List<Map<String, Object>> findJCICSubmitByCntrNo(String CntrNo) {
		return getJdbc().queryForList("ELF632.findJCICSubmitByCntrNo",
				new Object[] { CntrNo });
	}

	@Override
	public Map<String, Object> getELF632LastestByCntrNo(String CntrNo) {
		List<Map<String, Object>> lists = this.findJCICSubmitByCntrNo(CntrNo);
		if (lists.size() > 0) {
			return this.findJCICSubmitByCntrNo(CntrNo).get(0);
		} else {
			return null;
		}
	}

	@Override
	public void addElf632(Timestamp ELF632_TXN_DATE, String ELF632_CONTRACT,
			String ELF632_BANK_CODE, String ELF632_LN_BR_NO,
			String ELF632_FACT_TYPE, String ELF632_CUST_ID_B,
			String ELF632_SWFT_B, String ELF632_FACT_AMT_B,
			String ELF632_DBR22_AMT_B, String ELF632_CUST_ID_A,
			String ELF632_SWFT_A, String ELF632_FACT_AMT_A,
			String ELF632_DBR22_AMT_A, String ELF632_WRITE_SYS, String ELF632_LOAN_NM_1, String ELF632_LOAN_NM_2) {
		this.getJdbc()
				.update("ELF632.addElf632",
						new Object[] { ELF632_TXN_DATE, ELF632_CONTRACT,
								ELF632_BANK_CODE, ELF632_LN_BR_NO,
								ELF632_FACT_TYPE, ELF632_CUST_ID_B,
								ELF632_SWFT_B, ELF632_FACT_AMT_B,
								ELF632_DBR22_AMT_B, ELF632_CUST_ID_A,
								ELF632_SWFT_A, ELF632_FACT_AMT_A,
								ELF632_DBR22_AMT_A, ELF632_WRITE_SYS, ELF632_LOAN_NM_1, ELF632_LOAN_NM_2 });
	}

	@Override
	public Map<String, Object> getLatestChargeClerkOfSigningBook(String cntrNo,
			String authBranchNo) {
		return this.getJdbc().queryForMap(
				"ELF447.getLatestChargeClerkOfSigningBook",
				new String[] { cntrNo, authBranchNo });
	}

	@Override
	public List<Map<String, Object>> findLNF020_NOTICE_TYPE(String cntrno) {
		return this.getJdbc().queryForList("LNF020.getLNF020_NOTICE_TYPE",
				new String[] { cntrno });
	}

	@Override
	public List<Map<String, Object>> getOverdueLoanCaseRecommendedByLandsman(
			String yyyyMM, String fromDate, String toDate) {
		return this.getJdbc().queryForListWithMax(
				"getOverdueLoanCaseRecommendedByLandsmanByYYYYMM",
				new String[] { yyyyMM, yyyyMM, yyyyMM, fromDate, toDate });
	}

	@Override
	public BigDecimal getCoupleBankManLimitAmount() {

		Map<String, Object> map = getJdbc().queryForMap(
				"LNF070.getCoupleBankManLimitAmount", new String[] {});
		if (map == null) {
			return new BigDecimal("********");
		}

		return new BigDecimal(map.get("LNF070_TYPE").toString());
	}

	@Override
	public List<Map<String, Object>> get_LNF242_Contractno(String custId,
			String dupNo, String actualtime) {
		String CUST_ID = custId + dupNo;
		if (actualtime != null) {
			return this.getJdbc().queryForList("LNF242.getByConst2",
					new Object[] { CUST_ID, actualtime });
		} else {
			return this.getJdbc().queryForList("LNF242.getByConst",
					new Object[] { CUST_ID });
		}
	}

	@Override
	public Map<String, Object> get_LNF320_nextBussdate(String lnf320_query_date) {
		return this.getJdbc().queryForMap("LNF320.getNextBussdate",
				new Object[] { lnf320_query_date });
	}

	@Override
	public List<Map<String, Object>> get_LNF242ByNxtpdate(String basedate,
			String nxtpdate) {
		return this.getJdbc().queryForList("LNF242.getByNxtpdate",
				new String[] { basedate, nxtpdate });
	}

	@Override
	public void updateLNF242(String idchg_fg, String ward_fg, String wealth_fg, String custId,
			String dupNo, String contracts) {
		String CUST_ID = custId + dupNo;
		this.getJdbc().update("LNF242.update",
				new String[] { idchg_fg, ward_fg, wealth_fg, CUST_ID, contracts });

	}

	@Override
	public List<Map<String, Object>> getMISAMF_byCustidBrno(String custId,
			String brNo, String toDay) {
		return this.getJdbc().queryForList("MISAMF.getMISAMF_byCustidBrno",
				new Object[] { custId, brNo, toDay, toDay });
	}

	@Override
	public void updateElf516UnfundedAndCanceledCase(String lnFlag,
			Integer checkTimes, String cntrNo, String custId, String dupNo,
			String yyymm, String status) {
		this.getJdbc().update(
				"MIS.ELF516_updateLnflag_checkTimes_checkTime",
				new Object[] { lnFlag, checkTimes, cntrNo, custId, dupNo,
						yyymm, status });
	}

	/**
	 * J-112-0267勞工紓困還款介接檔
	 * 
	 * @return
	 */
	@Override
	public List<Map<String, Object>> get_ELF630(String payday_start,
			String payday_end) {
		return this.getJdbc().queryForListWithMax("ELF630.getPayday",
				new String[] { payday_start, payday_end });
	}

	@Override
	public List<Map<String, Object>> get_TelNNo(String brnno) {
		return this.getJdbc().queryForList("SYNBANK.getTelNNo",
				new String[] { brnno });
	}

	@Override
	public void updateELF630userName(String BankUserName, String StaffTel1,
			String StaffTel2, String StaffTelExt, String orgSrlNo8,
			String orgSrlNo9, String PayDay) {
		this.getJdbc().update(
				"ELF630.updateuserName",
				new Object[] { BankUserName, StaffTel1, StaffTel2, StaffTelExt,
						orgSrlNo8, orgSrlNo9, PayDay });

	}

	@Override
	public void updateELF630(String dataStatus, String srlNo, String sysMsg,
			String remark, String receiveDay, String orgSrlNo8,
			String orgSrlNo9, String payDay) {
		this.getJdbc().update(
				"ELF630.update",
				new Object[] { dataStatus, srlNo, sysMsg, remark, receiveDay,
						orgSrlNo8, orgSrlNo9, payDay });

	}

	/**
	 * J-111-0554_05097_B1001 Web e-Loan授信修改授信覆審作業系統中之相關事宜
	 * 
	 * @param bgnDate
	 * @param endDate
	 * @return
	 */
	@Override
	public List<Map<String, Object>> get_ELF492_By_LrDate(String bgnDate,
			String endDate) {
		return this.getJdbc().queryForListWithMax("ELF492.selUnidBylrDate",
				new String[] { bgnDate, endDate });
	}

	/**
	 * J-111-0554_05097_B1001 Web e-Loan授信修改授信覆審作業系統中之相關事宜
	 * 
	 * @param unid
	 * @param apprId
	 */
	@Override
	public void updateELF942_ApprId_By_Unid(String unid, String apprId) {
		this.getJdbc().update("ELF492.updateApprIdByUnid",
				new Object[] { apprId, unid });

	}

	@Override
	public List<Map<String, Object>> creditLoanCheckLoanDate(String custId, String dupNo, String cntrNo) {
		return this.getJdbc().queryForList("MIS.LNF030.creditLoan.checkLoanDate",
				new Object[] { custId + dupNo, cntrNo });
	}

	@Override
	public List<Map<String, Object>> findRelatedPartyList(String custId, String dupNo) {
		return this.getJdbc().queryForListWithMax("MIS.findRelatedPartyList", new String[] { custId, dupNo, custId + dupNo, custId, dupNo, custId, dupNo });
	}
	
	@Override
	public Map<String, Object> findElf500_Elf383_Lnf087ByCntrNo(String cntrNo) {
		return this.getJdbc().queryForMap("ELF500.ELF383.LNF087.selectByCntrNo", new String[] { cntrNo, cntrNo, cntrNo });
	}
	
	@Override
	public void insertELF690_EjcicT70Inquiry(String custId, String dupNo, String queryReason, String branchNo, String staffNo) {

		this.getJdbc().update("ELF690.insert",
			new Object[] {
				custId, 
				dupNo, 
				queryReason, 
				branchNo, 
				staffNo
			});
	}
	
	@Override
	public Map<String, Object> findELF690ByCustIdAndDupNoAndBranchNo(String custId, String dupNo, String branchNo) {
		return this.getJdbc().queryForMap("ELF690.selectByCustId_dupNo_brn",new String[] { custId, dupNo, branchNo });
	}

	@Override
	public int findLNF277ByCntrno(String cntrNo) {
		return this.getJdbc().queryForInt("LNF277.selectCountBycntrNo",new String[] { cntrNo });
	}

    /**
     * J-113-0519 e-Loan授信覆審作業系統之覆審名單篩選條件修正
     *
     * @return
     */
    @Override
    public int updateRetrialDataJ1130519() {
        String sqlTmp = this.getSqlBySqlId("J-113-0519_07623_B1001");
		String sqlList[] = sqlTmp.split("\\n");
		int rows = 0;
		for(String sql: sqlList){
			sql = CapString.trimNull(sql);
			if(!CapString.isEmpty(sql)) {
				int row = this.getJdbc().updateBySQL(sql, null);
				rows += row;
			}
		}
        return rows;
    }

}
