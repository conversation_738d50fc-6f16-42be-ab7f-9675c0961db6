
package com.mega.eloan.lms.fms.flow;

import org.springframework.stereotype.Component;

import com.mega.eloan.common.flow.AbstractFlowHandler;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.model.L300M01A;

import tw.com.jcs.flow.FlowInstance;


/**
 * <pre>
 * 覆審考核表作業流程
 * </pre>
 * 
 * @since 2022
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Component
public class LMS8100Flow extends AbstractFlowHandler {
	
	@Transition(node = "編製中", value = "呈主管")
	public void send(FlowInstance instance) {

	}
	
	@Transition(node = "確認", value = "核准")
	public void accept(FlowInstance instance) {
		
	}
	
	@Transition(node = "確認", value = "退回")
	public void back(FlowInstance instance) {

	}
	
	@Override
	public Class<? extends Meta> getDomainClass() {
		return L300M01A.class;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Class getDocStatusEnumClass() {
		return CreditDocStatusEnum.class;
	}
}