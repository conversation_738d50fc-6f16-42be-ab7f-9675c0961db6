/* 
 * LMSS03APanel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.panels;

import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * 額度明細表(國內企金授權外)
 * </pre>
 * 
 * @since 2012/1/19
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/19,<PERSON>,new
 *          </ul>
 */
public class CLSS03APanel extends Panel {

	public CLSS03APanel(String id) {
		super(id);
//		add(new LMS1401S01Panel("lms1405s01_panel"));
//		add(new LMS1401S02Panel("lms1405s02_panel"));
	}

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

}
