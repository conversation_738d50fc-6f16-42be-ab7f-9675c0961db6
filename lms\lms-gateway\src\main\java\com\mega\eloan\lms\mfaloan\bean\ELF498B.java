package com.mega.eloan.lms.mfaloan.bean;

import java.sql.Timestamp;

import javax.persistence.Column;

import tw.com.iisi.cap.model.GenericBean;

/** 防杜代辦消金覆審承辦行員 **/
public class ELF498B extends GenericBean{

	private static final long serialVersionUID = 1L;

	/** 文件編號UNID */
	@Column(name = "ELF498B_UNID", length = 32, columnDefinition = "CHAR(32)", nullable=false,unique = true)
	private String elf498b_unid;
	
	/** 簽報書UNID */
	@Column(name = "ELF498B_RPTID", length = 32, columnDefinition = "CHAR(32)", nullable=false,unique = true)
	private String elf498b_rptid;
	
	/** 承辦行員類型{1引介人, 2經辦人員, 3覆核主管} */
	@Column(name = "ELF498B_REL_TYPE", length = 1, columnDefinition = "CHAR(1)", nullable=false,unique = true)
	private String elf498b_rel_type;
	
	/** 行員編號 */
	@Column(name = "ELF498B_STAFFNO", length = 6, columnDefinition = "CHAR(6)", nullable=false,unique = true)
	private String elf498b_staffno;
		
	/** 資料修改人 */
	@Column(name = "ELF498B_UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String elf498b_updater;
	
	/** 資料更新日 */
	@Column(name = "ELF498B_TMESTAMP", columnDefinition = "TIMESTAMP")
	private Timestamp elf498b_tmestamp;

	public String getElf498b_unid() {
		return elf498b_unid;
	}

	public void setElf498b_unid(String elf498b_unid) {
		this.elf498b_unid = elf498b_unid;
	}

	public String getElf498b_rptid() {
		return elf498b_rptid;
	}

	public void setElf498b_rptid(String elf498b_rptid) {
		this.elf498b_rptid = elf498b_rptid;
	}

	public String getElf498b_rel_type() {
		return elf498b_rel_type;
	}

	public void setElf498b_rel_type(String elf498b_rel_type) {
		this.elf498b_rel_type = elf498b_rel_type;
	}

	public String getElf498b_staffno() {
		return elf498b_staffno;
	}

	public void setElf498b_staffno(String elf498b_staffno) {
		this.elf498b_staffno = elf498b_staffno;
	}

	public String getElf498b_updater() {
		return elf498b_updater;
	}

	public void setElf498b_updater(String elf498b_updater) {
		this.elf498b_updater = elf498b_updater;
	}

	public Timestamp getElf498b_tmestamp() {
		return elf498b_tmestamp;
	}

	public void setElf498b_tmestamp(Timestamp elf498b_tmestamp) {
		this.elf498b_tmestamp = elf498b_tmestamp;
	}
}
