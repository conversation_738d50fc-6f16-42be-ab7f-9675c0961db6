package com.mega.eloan.lms.fms.handler.grid;

import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.fms.service.LMS9020Service;
import com.mega.eloan.lms.model.L902M01A;
import com.mega.eloan.lms.model.L902S01A;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapFormatException;
import tw.com.iisi.cap.formatter.IBeanFormatter;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 停權解除維護Grid Handler
 * </pre>
 * 
 * @since 2013/1/21
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/1/21,Miller,new
 *          </ul>
 */
@Scope("request")
@Controller("lms9020gridhandler")
public class LMS9020GridHandler extends AbstractGridHandler {

	@Resource
	LMS9020Service service9020;

	@Resource
	UserInfoService userservice;

	@Resource
	BranchService branchService;

	/**
	 * 查詢L120M01AGrid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL902m01a(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addOrderBy("caseDate");
		// 取得文件狀態代碼(交易代碼)

		Page<? extends GenericBean> page = service9020.findPage(L902M01A.class,
				pageSetting);
		List<L902M01A> l902m01as = (List<L902M01A>) page.getContent();
		for (L902M01A model : l902m01as) {

			model.setDocURL("/fms/lms9020m01");

		}

		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();

		formatter.put("isClosed", new IBeanFormatter() {

			@Override
			public String reformat(Object in) throws CapFormatException {

				Meta meta = (Meta) in;
				String isClosed = "";
				if (!Util.isEmpty(meta.getDeletedTime())) {
					isClosed = "Y";
				} else {
					isClosed = "";
				}
				return isClosed;

			}
		});

		formatter.put("updater", new IBeanFormatter() {

			@Override
			public String reformat(Object in) throws CapFormatException {

				Meta meta = (Meta) in;
				String updater = "";
				if (Util.notEquals(Util.trim(meta.getUpdater()), "")) {
					updater = !Util.isEmpty(userservice.getUserName(meta
							.getUpdater())) ? userservice.getUserName(meta
							.getUpdater()) : Util.trim(meta.getUpdater());

				} else {
					updater = !Util.isEmpty(userservice.getUserName(meta
							.getCreator())) ? userservice.getUserName(meta
							.getCreator()) : Util.trim(meta.getCreator());

				}
				return updater;

			}
		});

		formatter.put("updateTime", new IBeanFormatter() {

			@Override
			public Timestamp reformat(Object in) throws CapFormatException {

				Meta meta = (Meta) in;
				Timestamp updateTime = null;
				if (Util.notEquals(Util.trim(meta.getUpdater()), "")) {
					updateTime = meta.getUpdateTime();

				} else {
					updateTime = meta.getCreateTime();

				}
				return updateTime;

			}
		});

		return new CapGridResult(page.getContent(), page.getTotalRow(),
				formatter);

	}

	/**
	 * 查詢L120M01AGrid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL902s01a(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addOrderBy("caseDate");
		// 取得文件狀態代碼(交易代碼)

		// 建立主要Search 條件
		String mainId = Util.nullToSpace(params.getString("mainId"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);

		Page<? extends GenericBean> page = service9020.findPage(L902S01A.class,
				pageSetting);

		List<L902S01A> l902s01as = (List<L902S01A>) page.getContent();

		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();

		formatter.put("updater", new IBeanFormatter() {

			@Override
			public String reformat(Object in) throws CapFormatException {

				GenericBean bean = (GenericBean) in;
				String updater = "";
				try {
					if (Util.notEquals(Util.trim((String) bean.get("updater")),
							"")) {
						updater = !Util.isEmpty(userservice
								.getUserName((String) bean.get("updater"))) ? userservice
								.getUserName((String) bean.get("updater"))
								: Util.trim((String) bean.get("updater"));

					} else {

						updater = !Util.isEmpty(userservice
								.getUserName((String) bean.get("creator"))) ? userservice
								.getUserName((String) bean.get("creator"))
								: Util.trim((String) bean.get("creator"));

					}
				} catch (CapException e) {
					return null;
				}
				return updater;

			}
		});

		formatter.put("updateTime", new IBeanFormatter() {

			@Override
			public Timestamp reformat(Object in) throws CapFormatException {

				GenericBean bean = (GenericBean) in;
				Timestamp updateTime = null;
				try {
					if (Util.notEquals(Util.trim((String) bean.get("updater")),
							"")) {
						updateTime = (Timestamp) bean.get("updateTime");

					} else {
						updateTime = (Timestamp) bean.get("createTime");

					}
				} catch (CapException e) {
					return null;
				}

				return updateTime;

			}
		});

		return new CapGridResult(page.getContent(), page.getTotalRow(),
				formatter);

	}

	
}
