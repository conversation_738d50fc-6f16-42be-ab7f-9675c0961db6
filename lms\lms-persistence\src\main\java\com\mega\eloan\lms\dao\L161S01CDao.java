/* 
 * L161S01CDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L161S01C;

/** 額度動用資訊敘述說明檔 **/
public interface L161S01CDao extends IGenericDao<L161S01C> {

	L161S01C findByOid(String oid);
	
	List<L161S01C> findByMainId(String mainId);
	
	L161S01C findByUniqueKey(String mainId, String pid, String itemType);

	List<L161S01C> findByIndex01(String mainId, String pid, String itemType);

	List<L161S01C> findByIndex02(String mainId);

	List<L161S01C> findByIndex03(String mainId, String pid);

	List<L161S01C> findByIndex04(String mainId, String cntrNo);
	
	List<L161S01C> findByMainIdPid(String mainId, String pid);

	List<L161S01C> findByMainIdCntrno(String mainId, String cntrNo);
	
	L161S01C findByMainIdPidItemType(String mainId,String pid,String itemType);
	
	L161S01C findByMainIdCntrnoItemType(String mainId,String cntrNo,String itemType);
	
}