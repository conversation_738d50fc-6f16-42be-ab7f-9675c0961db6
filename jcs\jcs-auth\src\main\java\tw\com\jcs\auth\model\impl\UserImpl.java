package tw.com.jcs.auth.model.impl;

import java.util.Set;

import tw.com.jcs.auth.model.Department;
import tw.com.jcs.auth.model.User;

/**
 * <pre>
 * UserImpl
 * </pre>
 * 
 * @since 2022年12月13日
 * <AUTHOR> @version
 *          <ul>
 *          <li>2022年12月13日
 *          </ul>
 */
public class UserImpl implements User {

    public static final String LOGIN_USER = "loginUser";

    private static final long serialVersionUID = -1556406275439264526L;

    /**
     * 使用者id
     */
    String id;

    /**
     * 使用者名稱
     */
    String name;
    String position;

    /**
     * 部門
     */
    Department dept;

    /**
     * 角色
     */
    Set<String> roles;

    /**
     * 登入時間
     */
    String loginTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Department getDepartment() {
        return dept;
    }

    public void setBranch(Department dept) {
        this.dept = dept;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public Set<String> getRoles() {
        return roles;
    }

    public void setRoles(Set<String> roles) {
        this.roles = roles;
    }

    public String getLoginTime() {
        return loginTime;
    }

    public void setLoginTime(String loginTime) {
        this.loginTime = loginTime;
    }
}
