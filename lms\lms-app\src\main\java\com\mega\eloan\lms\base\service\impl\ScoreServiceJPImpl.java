/* 
 * ScoreServiceJPImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.service.impl;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.MathContext;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import javax.annotation.Resource;
import javax.script.ScriptEngineManager;

import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.OverSeaUtil;
import com.mega.eloan.lms.base.constants.Score;
import com.mega.eloan.lms.base.constants.ScoreJP;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.ScoreService;
import com.mega.eloan.lms.base.service.ScoreServiceJP;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.ejcic.service.EjcicService;
import com.mega.eloan.lms.eloandb.service.impl.AbstractEloandbJdbc;
import com.mega.eloan.lms.etch.service.EtchService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C121S01A;

import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapMath;
import tw.com.jcs.common.Arithmetic;
import tw.com.jcs.common.Util;
/**
 * <pre>
 * 評分 ServiceImpl
 * </pre>
 * 
 * @since 2012/10/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/10/30,Fantasy,new
 *          </ul>
 */
@Service("ScoreServiceJP")
public class ScoreServiceJPImpl extends AbstractEloandbJdbc implements
		ScoreServiceJP {

	
	private static final Logger logger = LoggerFactory.getLogger(ScoreServiceJPImpl.class);
	private static final String JSON_NULL = "";
	private HashMap<String, JSONObject> container_houseLoan = new LinkedHashMap<String, JSONObject>();
	private ScriptEngineManager scriptEngineManager = new ScriptEngineManager();

	private static final int OVERSEA_SCALE = 5;

	@Resource
	EjcicService ejcicService;

	@Resource
	EtchService etchService;

	@Resource
	DwdbBASEService dwdbBASEService;

	@Resource
	MisdbBASEService misdbBaseService;
	
	@Resource
	ScoreService scoreService;
	
	@Resource
	CodeTypeService codeTypeService;


	@Override
	public JSONObject scoreJP(String type, JSONObject data, String varVer, String mowType2) { //房貸
		JSONObject result = new JSONObject();
		//預設使用1.0模型，因1.0沒有區分房貸非房貸，所以其實[設定檔_House_JP]跟[設定檔_notHouse_JP]是一樣的 (只是強迫症想分一下，但他們都是指向同一之檔案)
		String fileLoc = ScoreJP.設定檔_House_JP;	
		
		if(mowType2.equals(OverSeaUtil.海外評等_房貸)){
			fileLoc = ScoreJP.設定檔_House_JP;	
			if(Util.equals(OverSeaUtil.V2_0_LOAN_JP, varVer)){
				fileLoc = ScoreJP.設定檔_House_JP_V2_0;				
			}
		}else{ //非房貸
			fileLoc = ScoreJP.設定檔_notHouse_JP;	
			if(Util.equals(OverSeaUtil.V2_0_LOAN_JP, varVer)){
				fileLoc = ScoreJP.設定檔_notHouse_JP_V2_0;				
			}
		}
		
		
		
		JSONObject items = null;
		/*
		 * 邏輯應該是
		 * <1>先有 base基本 → 再算出「初始」評等
		 * <2>依評等 → 對應 DR (若有人工調整評等，要以調整後的評等，去對應)
		 */
		if(Util.equals(type, ScoreJP.type.日本消金模型基本)){
			if(Util.equals(OverSeaUtil.V1_0_LOAN_JP, varVer)){
				items = scoreService.parse(ScoreJP.jpBase.class, fileLoc);		
			}else if(Util.equals(OverSeaUtil.V2_0_LOAN_JP, varVer)){
				items = scoreService.parse(ScoreJP.jpBase_V2_0.class, fileLoc);		
			}else{
				items = new JSONObject();
			}
		}else if(Util.equals(type, ScoreJP.type.日本消金模型評等)){
			items = scoreService.parse(ScoreJP.jpGrade.class, fileLoc);
		}else if(Util.equals(type, ScoreJP.type.日本消金模型違約機率)){
			if(Util.equals(OverSeaUtil.V2_0_LOAN_JP, varVer)){
				//2.0版無3年違約率
				items = scoreService.parse(ScoreJP.jpDR_V2_0.class, fileLoc);
			}else{
				items = scoreService.parse(ScoreJP.jpDR.class, fileLoc);
			}

		}
		Properties prop = getProperty(fileLoc);
		
		if (items != null) {		
			_debug("[scoreJP]input_param:"+data);
			if(Util.equals(OverSeaUtil.V1_0_LOAN_JP, varVer)){
				get_Score(items, result, data, prop);
			}else if(Util.equals(OverSeaUtil.V2_0_LOAN_JP, varVer)){
				get_Score_V2_0(items, result, data, prop);
			}	
		}
		if(Util.equals(type, ScoreJP.type.日本消金模型基本)){
			process_JP(data, result, prop, varVer, mowType2);//裡面 call 計算[評等、違約機率]			
		}
			
		return result;
	}
	
	private void process_JP(JSONObject data, JSONObject result, Properties prop, String varVer, String mowType2) {
		if (prop != null) {
			
			result.put(ScoreJP.column.評等建立日期, CapDate.getCurrentDate(DataParse.DateFormat));
			result.put(ScoreJP.column.模型版本, varVer);
						
			if(true){//評等
				JSONObject level = scoreJP(ScoreJP.type.日本消金模型評等, result, varVer, mowType2);
				String pRating = Util.trim(level.get(ScoreJP.jpGrade.等級));
				result.put(ScoreJP.column.初始評等, pRating);
				//================					
				Integer sumRiskPt = process_JP_sumRiskPt(data, varVer);
				Integer adj_pts = get_JP_adj_pts(sumRiskPt);
				Integer sRating = null;	
				if(Util.isNotEmpty(pRating) && Util.isInteger(pRating)){
					//依「累加風險點數」降等
					int raw_sRating = Util.parseInt(pRating) - adj_pts;		
					sRating = OverSeaUtil.rating_min1_max10(raw_sRating);
				}
				//================
				String j10_score_flag = Util.trim(data.get(ScoreJP.column.J10信用評分種類));	
				int j10_score = Util.parseInt(Util.trim(data.get(ScoreJP.column.J10信用評分)));
				Integer adj_j10 = 0;
				String sprtRating = "";
				if(Util.isNotEmpty(pRating) && Util.isInteger(pRating)){
					adj_j10 = get_JP_adj_j10(j10_score_flag, j10_score,  Util.parseInt(pRating) );
					
					if(adj_j10==0){ //不調整
						sprtRating =  Util.trim(sRating);
					}else if(adj_j10==99){
						sprtRating = OverSeaUtil.DF;
					}else{
						if(Util.equals(varVer, OverSeaUtil.V2_0_LOAN_JP)){ //2.0模型最低等為15等
							sprtRating = Util.trim(OverSeaUtil.rating_min1_max15(sRating - adj_j10));	
						}else{
							sprtRating = Util.trim(OverSeaUtil.rating_min1_max10(sRating - adj_j10));	
						}
												
					}
				}				
				
				if(true){
					result.put(ScoreJP.column.累加風險點數, Util.trim(sumRiskPt));
					result.put(ScoreJP.column.升降等_依風險點數, Util.trim(adj_pts));	
				}				
				result.put(ScoreJP.column.獨立評等, Util.trim(sRating));
				if(true){
					result.put(ScoreJP.column.升降等_聯徵, Util.trim(adj_j10));	
				}
				result.put(ScoreJP.column.支援評等, Util.trim(sprtRating));
				result.put(ScoreJP.column.調整評等, "");
				result.put(ScoreJP.column.原始最終評等, Util.trim(sprtRating));
				result.put(ScoreJP.column.最終評等, Util.trim(sprtRating));
	
				result.put(ScoreJP.column.評等調整日期, "");
				result.put(ScoreJP.column.完成最終評等日期, "");
				result.put(ScoreJP.column.註記不調整, "");
				result.put(ScoreJP.column.調整狀態, "");
				result.put(ScoreJP.column.調整註記, "");
				result.put(ScoreJP.column.調整理由, "");
			}
			if(true){//違約機率
				JSONObject jpDR = scoreJP(ScoreJP.type.日本消金模型違約機率, result, varVer, mowType2);
				setJPDR(jpDR, result);
			}
			// clear
			prop.clear();
			prop = null;
		}
	}
	
	
	@Override
	public void setJPDR(JSONObject jpDR, JSONObject target){
		String[] keyArr = {ScoreJP.jpDR.違約機率_預估3年期, ScoreJP.jpDR.違約機率_預估1年期};
		for(String key: keyArr){
			target.put(key, jpDR.get(key));	
		}
	}
	
	private Integer process_JP_sumRiskPt(JSONObject data, String varVer){
		String have = UtilConstants.haveNo.有;
		
		int sumRiskPt = 0;
		//和 UI 的順序相同，以方便比對 chkItem 對應的點數
		if(Util.equals(Util.trim(data.get(ScoreJP.column.負面資訊_01)), have)){
			sumRiskPt += 4;
		}
		if(Util.equals(Util.trim(data.get(ScoreJP.column.負面資訊_02)), have)){
			sumRiskPt += 4;
		}
		if(Util.equals(Util.trim(data.get(ScoreJP.column.負面資訊_09)), have)){
			sumRiskPt += 2;
		}
		if(Util.equals(Util.trim(data.get(ScoreJP.column.負面資訊_11)), have)){
			sumRiskPt += 2;
		}
		if(Util.equals(Util.trim(data.get(ScoreJP.column.負面資訊_12)), have)){
			sumRiskPt += 1;
		}
		if(Util.equals(Util.trim(data.get(ScoreJP.column.負面資訊_04)), have)){
			sumRiskPt += 2;
		}
		if(Util.equals(Util.trim(data.get(ScoreJP.column.負面資訊_05)), have)){
			sumRiskPt += 2;
		}
		if(Util.equals(Util.trim(data.get(ScoreJP.column.負面資訊_10)), have)){
			sumRiskPt += 2;
		}
		if(Util.equals(Util.trim(data.get(ScoreJP.column.負面資訊_07)), have)){
			sumRiskPt += 3;
		}
		if(Util.equals(Util.trim(data.get(ScoreJP.column.負面資訊_13)), have)){
			sumRiskPt += 1;
		}
		
		return sumRiskPt;
	}
	
	private BigDecimal jpItemStdVal(Properties prop, String key, BigDecimal factorVal){	
		BigDecimal devSampleMean = BigDecimal.ZERO;
		BigDecimal devSampleSTD = BigDecimal.ONE;
		BigDecimal A = BigDecimal.ZERO;
		BigDecimal B = BigDecimal.ZERO;
		if(Util.equals(key, ScoreJP.column.合計WeightedScore)){
			devSampleMean = CrsUtil.parseBigDecimal(prop.getProperty("stdCoreScoreDevSampleMean"));
			devSampleSTD = CrsUtil.parseBigDecimal(prop.getProperty("stdCoreScoreDevSampleSTD"));
			A = CrsUtil.parseBigDecimal(prop.getProperty("stdCoreScoreA"));
			B = CrsUtil.parseBigDecimal(prop.getProperty("stdCoreScoreB"));
		}else{
			A = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreA"));
			B = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreB"));
			
			if(Util.equals(key, ScoreJP.jpBase.年齡_M1)){
				devSampleMean = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreDevSampleMean_m1"));
				devSampleSTD = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreDevSampleSTD_m1"));				
			}else if(Util.equals(key, ScoreJP.jpBase.職業_M5)){
				devSampleMean = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreDevSampleMean_m5"));
				devSampleSTD = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreDevSampleSTD_m5"));				
			}else if(Util.equals(key, ScoreJP.jpBase.年資_M7)){
				devSampleMean = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreDevSampleMean_m7"));
				devSampleSTD = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreDevSampleSTD_m7"));				
			}else if(Util.equals(key, ScoreJP.jpBase.年收入_P2)){
				devSampleMean = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreDevSampleMean_p2"));
				devSampleSTD = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreDevSampleSTD_p2"));				
			}else if(Util.equals(key, ScoreJP.jpBase.契約年限_A5)){
				devSampleMean = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreDevSampleMean_a5"));
				devSampleSTD = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreDevSampleSTD_a5"));				
			}else if(Util.equals(key, ScoreJP.jpBase.擔保品地點及種類_Z1)){
				devSampleMean = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreDevSampleMean_z1"));
				devSampleSTD = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreDevSampleSTD_z1"));				
			}else if(Util.equals(key, ScoreJP.jpBase.市場環境及變現性_Z2)){
				devSampleMean = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreDevSampleMean_z2"));
				devSampleSTD = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreDevSampleSTD_z2"));					
			}	
		}		
		BigDecimal val = CapMath.normalization(factorVal, devSampleMean, devSampleSTD, B, A, MathContext.DECIMAL64);
		return Arithmetic.round(val, OVERSEA_SCALE);	
	}
	
	/**
	 * 無須降等	      0	<=points<	2
	 * 至少降1等	      2	<=points<	5
	 * 至少降2等	      5	<=points<	7
	 * 至少降3等                            points>=	7
	 */
	private int get_JP_adj_pts(Integer sumRiskPt){
		int adj = 0;
		if(sumRiskPt==null){
			
		}else if(sumRiskPt>=2 && sumRiskPt<5){
			adj = -1;
		}else if(sumRiskPt>=5 && sumRiskPt<7){
			adj = -2;
		}else if(sumRiskPt>=7){
			adj = -3;
		}
		return adj;
	}
	
	/**
	<p>
	(1)	模型初始評等第4等(含)以下，且J10評分高於780分者，自動升1等，並可與主觀評等更新升等權限流用，即
		<ul>
		<li>支援評等未升等時，主觀評等更新可有升2等權限
		<li>支援評等升1等時，主觀評等更新僅有升1等權限；
		</ul>
	</p>
	<p>	
	(2)	低於500分者或固定評分(參照J10個人信用評分各類理由對照表代碼為023~043)自動降1等；
	</p>
	<p>  
	(3)	無法評分且揭露理由為有信用不良紀錄，且目前無正常授信帳戶或有效信用卡正卡
	    <br>(參照J10個人信用評分各類理由對照表代碼為016~022)，降為違約等級DF；
	</p>
	<p>        
	(4)	其他無法評分者(參照J10個人信用評分各類理由對照表代碼為001~015)不做調整
	   <br>(因雖有信用不良紀錄，但目前仍有正常授信帳戶或有效信用卡正卡，其信用不良紀錄大多已於聯徵特殊負面資訊中考量)。
	</p>
	 */
	private int get_JP_adj_j10(String j10_score_flag, int j10_score, int pRating){
		if(Util.equals("A", j10_score_flag)){
			if(pRating>=4 && j10_score>780){
				return 1;		
			}else if(j10_score<500){
				return -1;	
			}
		}else if(Util.equals("D", j10_score_flag)){//固定評分
			return -1;
		}else if(Util.equals("C", j10_score_flag)){//代碼為016~022
			return 99;
		}
	
		return 0;	
	}
	
	
	private void _debug(String s){
		
	}
	private static Properties getProperty(String name) {
		Properties prop = new Properties();
		InputStream stream = Thread.currentThread().getContextClassLoader()
				.getResourceAsStream(name);
		try {
			prop.load(stream);
		} catch (IOException e) {
			logger.error("[Properties.load]",e);
		} finally {
			try {
				if (stream != null)
					stream.close();
			} catch (IOException e) {
				logger.error("[InputStream.close]",e);
			}
		}
		return prop;
	}
	@Override
	public String get_Version_JP(){ 
		CodeType activeDate_2_0 = codeTypeService.findByCodeTypeAndCodeValue("ActiveDate_JP", OverSeaUtil.V2_0_LOAN_JP, "zh_TW");
		String V2_0_JP_ACTIVEDATE = activeDate_2_0.getCodeDesc2();
		
		if(LMSUtil.cmpDate(new Date(), ">=", CapDate.parseDate(V2_0_JP_ACTIVEDATE))){
			//自2023-MM-DD後，改用模型 2.0 
			return OverSeaUtil.V2_0_LOAN_JP;
		}else {
			return OverSeaUtil.V1_0_LOAN_JP; 
		}
	}
	
	private void get_Score(JSONObject items, JSONObject result, JSONObject data, Properties prop){
		@SuppressWarnings("unchecked")
		Set<String> set = (Set<String>) items.keySet();
		BigDecimal scr_core = BigDecimal.ZERO;
		BigDecimal val_100 = new BigDecimal("100");
		for (String key : set) {
			JSONObject json = (JSONObject) items.get(key);
			Object determine_val = scoreService.determine(key, json, data);
			_debug("[scoreJP]---af["+key+"]"+determine_val);
			result.put(key, determine_val);
							
			String weightKeyName = "";
			String stdKeyName = "";
			if(Util.equals(key, ScoreJP.jpBase.年齡_M1)){
				weightKeyName = ScoreJP.column.加權M1;
				stdKeyName = ScoreJP.column.標準化M1;
			}else if(Util.equals(key, ScoreJP.jpBase.職業_M5)){
				weightKeyName = ScoreJP.column.加權M5;
				stdKeyName = ScoreJP.column.標準化M5;
			}else if(Util.equals(key, ScoreJP.jpBase.年資_M7)){
				weightKeyName = ScoreJP.column.加權M7;
				stdKeyName = ScoreJP.column.標準化M7;
			}else if(Util.equals(key, ScoreJP.jpBase.年收入_P2)){
				weightKeyName = ScoreJP.column.加權P2;
				stdKeyName = ScoreJP.column.標準化P2;
			}else if(Util.equals(key, ScoreJP.jpBase.契約年限_A5)){
				weightKeyName = ScoreJP.column.加權A5;
				stdKeyName = ScoreJP.column.標準化A5;
			}else if(Util.equals(key, ScoreJP.jpBase.擔保品地點及種類_Z1)){
				weightKeyName = ScoreJP.column.加權Z1;
				stdKeyName = ScoreJP.column.標準化Z1;
			}else if(Util.equals(key, ScoreJP.jpBase.市場環境及變現性_Z2)){
				weightKeyName = ScoreJP.column.加權Z2;
				stdKeyName = ScoreJP.column.標準化Z2;
			}else{
				
			}
			BigDecimal stdItemVal = jpItemStdVal(prop, key, CrsUtil.parseBigDecimal(determine_val));
			BigDecimal weightItemVal = CrsUtil.parseBigDecimal(prop.getProperty(weightKeyName));
			result.put(stdKeyName, stdItemVal);
			result.put(weightKeyName, weightItemVal);
			
			scr_core = scr_core.add(stdItemVal.multiply(weightItemVal).divide(val_100));
		}
		scr_core = Arithmetic.round(scr_core, OVERSEA_SCALE);
		
		result.put(ScoreJP.column.合計WeightedScore, scr_core);
		result.put(ScoreJP.column.核心模型分數, jpItemStdVal(prop, ScoreJP.column.合計WeightedScore, scr_core));
		
	}
	
	private void get_Score_V2_0(JSONObject items, JSONObject result, JSONObject data, Properties prop){
		@SuppressWarnings("unchecked")
		Set<String> set = (Set<String>) items.keySet();
		BigDecimal scr_core = BigDecimal.ZERO;
		BigDecimal val_100 = new BigDecimal("100");
		for (String key : set) {
			JSONObject json = (JSONObject) items.get(key);
			Object determine_val = scoreService.determine(key, json, data);
			_debug("[scoreJP]---af["+key+"]"+determine_val);
			result.put(key, determine_val);		
			String weightKeyName = "";
			String weightScrName = "";
			if(Util.equals(key, ScoreJP.jpBase_V2_0.年齡_M1)){
				weightKeyName = ScoreJP.column.加權M1;
				weightScrName = ScoreJP.column.權重分數M1;
			}else if(Util.equals(key, ScoreJP.jpBase_V2_0.職業_M5)){
				weightKeyName = ScoreJP.column.加權M5;
				weightScrName = ScoreJP.column.權重分數M5;
			}else if(Util.equals(key, ScoreJP.jpBase_V2_0.年資_M7)){
				weightKeyName = ScoreJP.column.加權M7;
				weightScrName = ScoreJP.column.權重分數M7;
			}else if(Util.equals(key, ScoreJP.jpBase_V2_0.年收入_P2)){
				weightKeyName = ScoreJP.column.加權P2;
				weightScrName = ScoreJP.column.權重分數P2;
			}else if(Util.equals(key, ScoreJP.jpBase_V2_0.契約年限_A5)){
				weightKeyName = ScoreJP.column.加權A5;
				weightScrName = ScoreJP.column.權重分數A5;
			}else if(Util.equals(key, ScoreJP.jpBase_V2_0.擔保品地點及種類_Z1)){
				weightKeyName = ScoreJP.column.加權Z1;
				weightScrName = ScoreJP.column.權重分數Z1;
			}else if(Util.equals(key, ScoreJP.jpBase_V2_0.市場環境及變現性_Z2)){
				weightKeyName = ScoreJP.column.加權Z2;
				weightScrName = ScoreJP.column.權重分數Z2;
			}else if(Util.equals(key, ScoreJP.jpBase_V2_0.教育程度)){ //2.0新增因子
				weightKeyName = ScoreJP.column.加權edu;
				weightScrName = ScoreJP.column.權重分數edu;
			}else if(Util.equals(key, ScoreJP.jpBase_V2_0.個人負債比率)){ //2.0新增因子
				weightKeyName = ScoreJP.column.加權drate;
				weightScrName = ScoreJP.column.權重分數drate;
				
			}else{
			}
			//2.0版本不用算標準化(STD)
			//weightItemVal = 權重
			BigDecimal weightItemVal = CrsUtil.parseBigDecimal(prop.getProperty(weightKeyName));
			result.put(weightKeyName, weightItemVal);
			
			BigDecimal scr_decimal = CrsUtil.parseBigDecimal(determine_val);
			//scr_weight = 分數*權重
			BigDecimal scr_weight = scr_decimal.multiply(weightItemVal).divide(val_100);
			result.put(weightScrName, scr_weight);
			scr_core = scr_core.add(scr_weight);
		}
		scr_core = Arithmetic.round(scr_core, OVERSEA_SCALE);
		result.put(ScoreJP.column.合計WeightedScore, scr_core); // Σ分數*權重
		result.put(ScoreJP.column.核心模型分數, null); //STD標準化分數，不記錄
		
		
		BigDecimal slope = Util.parseBigDecimal(prop.getProperty(Score.column.斜率));
		BigDecimal interCept = Util.parseBigDecimal(prop.getProperty(Score.column.截距));
		result.put(ScoreJP.column.截距, interCept);
		result.put(ScoreJP.column.斜率, slope);

		
		double pd = (1 / (1 + Math.exp( interCept.add(slope.multiply(scr_core)).doubleValue())));			
		result.put(ScoreJP.column.預測壞率, Util.Overflow(pd, 4));
		
	}
	
	public boolean getModelType_2_0(String loanTP, C121S01A c121s01a) {
		/*Step1.判斷要引入的模型為房貸 or 非房貸
		 * 若會計科目為[1310-0100_303一般中期放款、1410-0500_503一般長期放款、1350-0100_403一般中期擔保放款、
		 * 1450-1500_673長期房屋購置擔保放款、1450-1000_603一般長期擔保放款、1260-0200_204短期存摺存款擔保透支]
		 * 且，擔保品選擇[不動產]、不動產種類為[建物]、[土地與建物]時 >>>>> 引用[房貸]模型
		 * 其餘引用[非房貸模型]
		*/
		
		//loanTP=會計科目
		//根據會計科目與擔保品，判斷使用的模型應為1.0 or 2.0
		Map<String, String> modelTypeMap = codeTypeService.findByCodeType("modelType_JP_M");
		if(modelTypeMap.get(loanTP) != null 
				&& (Util.equals(Util.trim(c121s01a.getCmsType()), "1") 
						&& (Util.equals(Util.trim(c121s01a.getLocationType()), "2") || Util.equals(Util.trim(c121s01a.getLocationType()), "3")))){
			return true;
		}
		
		return false;
	}
	
	
}