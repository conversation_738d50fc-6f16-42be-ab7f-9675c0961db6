/* 
 *  LMS7830M01FormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.handler.form;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.enums.TypCdEnum;
import com.mega.eloan.common.exception.GWException;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.lms.service.LMS7830Service;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.auth.AuthType;

/**
 * <pre>
 * 簽報案件查詢
 * </pre>
 * 
 * @since 2011/12/12
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/12/12,REX,new
 *          </ul>
 */
@Scope("request")
@Controller("lms7830m01formhandler")
public class LMS7830M01FormHandler extends AbstractFormHandler {

	@Resource
	LMS7830Service lms7830Service;

	/**
	 * 查詢簽報案件
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryCustData(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String custId = params.getString(UtilConstants.Field.CUSTID);
		List<Map<String, Object>> custList = null;

		try {
			custList = lms7830Service.queryCustData(custId);
		} catch (GWException g) {
			logger.error(g.getMessage());
			HashMap<String, String> msg = new HashMap<String, String>();
			msg.put("dsName", "CustData");
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0010", msg), getClass());
		}
		if (!custList.isEmpty()) {
			// String name = "";
			Map<String, String> custMap = new TreeMap<String, String>();
			TypCdEnum type = TypCdEnum.getEnum(params.getString("type"));
			for (Map<String, Object> row : custList) {
				custMap.put((String) row.get("DUPNO"),
						(String) row.get("CNAME"));
			}
			result.set("size", custList.size());
			result.set("item", new CapAjaxFormResult(custMap));
			result.set("queryDate", CapDate.formatDate(
					CapDate.getCurrentTimestamp(),
					UtilConstants.DateFormat.YYYY_MM));
			result.set("custId", custId);
			result.set("typCd", type.name());
		} else {
			// EFD0036=INFO|查無資料!|
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0036"), getClass());
		}
		return result;

	}

}
