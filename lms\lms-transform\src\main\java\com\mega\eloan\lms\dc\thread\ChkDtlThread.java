package com.mega.eloan.lms.dc.thread;

import com.mega.eloan.lms.dc.action.ChkDtlRcdNumber;
import com.mega.eloan.lms.dc.conf.ConfigData;

public class ChkDtlThread extends Thread {

	private String viewname = "";
	private ConfigData config = null;

	public ChkDtlThread() {
		super();
	}

	/**
	 * Constructor
	 * 
	 * @param vn
	 *            String :目前讀取的viewListName
	 * @param pps
	 *            :Properties
	 */
	public ChkDtlThread(String vn) {
		this.viewname = vn;
	}

	public void run() {
		ChkDtlRcdNumber cdrn = new ChkDtlRcdNumber();
		cdrn.setConfigData(config);
		cdrn.doCheck(this.viewname);
	}

	/**
	 * set the config
	 * 
	 * @param config
	 *            the config to set
	 */
	public void setConfig(ConfigData config) {
		if (config != null) {
			this.config = config;
		}
	}
}
