byBranch= various preferential mortgage tables branches (BY)
byCounty= various preferential mortgage tables the (BY counties)
number= various preferential mortgage statistics

mortgage8ke= eight thousand billion preferential mortgage amount control table
mortgage3ke= three hundred billion off the mortgage the project financial institutions handle situations Tables 
mortgage2ke= In 2008 two hundred billion preferential mortgage amount control table
teenager=Relieved family preferential loan - purchase of residential

gov= government allocation of total degrees:
approved= the aggregate amount of the Bank has approved:
allocated= the Bank has approved grants total:
unallocate= the Bank has approved the aggregate amount of not funding:
preparation= the total amount of the preparation of the Bank:

export= output into:
word= Word file
excel= Excel file
tbTitle= number of preferential loans to control
all= preferential mortgage
addTitle= produce preferential loans Control Table
viewTitle= Access
toExcel= transferred out Excel
toWord= transferred out Word
sendMail= send letters
close= Close
base= (million)
nonBracketsBase= million
record=record
total= total
totAppMoney=the total admissibility amount 
totFavloan= total offer amount
confirmOutputExcel= whether the output of such data to Excel?
confirmOutputWord= whether the output of such data to Word?
confirmSendMail= whether to send a notification letter?
unit= Unit: Household; yuan
isSended= The record has sent letters
dataIncurrect= data errors

rptName= Report Name
rptTime= creation date
endDate= deadline

brno= Branch Name

area= the location of the subject matter
houses= number of households
sumup= Total
normal= general interest rate
favor= prime rate
newHouse= new home
oldHouse= existing housing
overage= balance
rmk= Remarks
favorAmt= preferential mortgage
accept= admissible
allocate= funding
the allocateHouses= funding households, <br/> the purchase of new homes and existing housing households
favorRate= preferential mortgage rate
amt=amount 

# Send the credit
subject= test, preferential mortgage statements