/* 
 *LNLNF070ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import tw.com.jcs.common.Util;

import com.mega.eloan.lms.mfaloan.service.LNLNF070Service;

/**
 * <pre>
 * LN.LNF070
 * </pre>
 * 
 * @since 2012/11/7
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/11/7,REX,new
 *          </ul>
 */
@Service
public class LNLNF070ServiceImpl extends AbstractMFAloanJdbc implements
		LNLNF070Service {

	@Override
	public LinkedHashMap<String, String> getCode() {
		LinkedHashMap<String, String> map = new LinkedHashMap<String, String>();
		List<Map<String, Object>> rowData = this.getJdbc().queryForList(
				"LNLNF070.getCode", new String[] {});
		for (Map<String, Object> data : rowData) {
			String key = Util.trim(data.get("LNF070_CODE"));
			String value = Util.trim(data.get("LNF070_NAME"));
			map.put(key, value);
		}
		return map;
	}

	@Override
	public LinkedHashMap<String, String> getPrRate(String curr) {
		String type = this.coverCurrForType(curr);

		// LNF070_TYPE,名詞代碼<br/>
		// LNF070_CODE,名詞類別<br/>
		// LNf070_NAME 名詞顯示名稱
		LinkedHashMap<String, String> map = new LinkedHashMap<String, String>();
		List<Map<String, Object>> rowData = this.getJdbc().queryForList(
				"LNLNF070.getPrRateForOneToLMS", new String[] { type });
		for (Map<String, Object> data : rowData) {
			String key = Util
					.getRightStr(Util.trim(data.get("LNF070_TYPE")), 3);
			String value = Util.trim(data.get("LNf070_NAME"));
			map.put(key, value);
		}

		return map;
	}

	@Override
	public HashMap<String, LinkedHashMap<String, String>> getPrRate() {
		String[] rateTypes = new String[] { "00", "01", "39", "99" };
		HashMap<String, LinkedHashMap<String, String>> totalMap = new HashMap<String, LinkedHashMap<String, String>>();
		// LNF070_TYPE,名詞代碼<br/>
		// LNF070_CODE,名詞類別<br/>
		// LNf070_NAME 名詞顯示名稱
//		StringBuffer temp = new StringBuffer(0);
//		for (String type : rateTypes) {
//			temp.append(temp.length() > 0 ? "," : "");
//			temp.append("'").append(type).append("'");
//		}
		String rateTypesParam = Util.genSqlParam(rateTypes);
		List<Map<String, Object>> rowData = this.getJdbc()
				.queryForListByCustParam("LNLNF070.getPrRate",
						new Object[] { rateTypesParam }, rateTypes);
		for (Map<String, Object> data : rowData) {
			String type = this.coverTypeForCurr(Util.getLeftStr(
					Util.trim(data.get("LNF070_TYPE")), 2));
			String key = Util
					.getRightStr(Util.trim(data.get("LNF070_TYPE")), 3);
			String value = Util.trim(data.get("LNf070_NAME"));
			if (totalMap.containsKey(type)) {
				totalMap.get(type).put(key, value);
			} else {
				LinkedHashMap<String, String> map = new LinkedHashMap<String, String>();
				map.put(key, value);
				totalMap.put(type, map);
			}
		}
		return totalMap;
	}

	/**
	 * 由代碼轉換為幣別
	 * 
	 * @param type
	 *            代碼
	 * @return 幣別
	 */
	private String coverTypeForCurr(String type) {
		String rateType = "";
		if ("00".equals(type)) {
			rateType = "TWD";
		} else if ("01".equals(type)) {
			rateType = "USD";
		} else if ("39".equals(type)) {
			rateType = "CNY";
		} else if ("99".equals(type)) {
			rateType = "Z";// 雜幣
		}
		return rateType;
	}

	/**
	 * 由幣別轉換為代碼
	 * 
	 * TWD = 00<br/>
	 * USD = "01"<br/>
	 * CNY = "39"<br/>
	 * Else<br/>
	 * rateType = "99"<br/>
	 * 
	 * @param curr
	 *            幣別
	 * @return
	 */
	private String coverCurrForType(String curr) {
		String rateType = "";
		if ("TWD".equals(curr)) {
			rateType = "00";
		} else if ("USD".equals(curr)) {
			rateType = "01";
		} else if ("CNY".equals(curr)) {
			rateType = "39";
		} else {
			rateType = "99";// 雜幣
		}
		return rateType;
	}

	/**
	 * 取得資金來源小類
	 * 
	 * 
	 * @param fFund
	 *            資金來源大類(代碼)
	 * @return map 資金來源小類map
	 */
	@Override
	public LinkedHashMap<String, String> getdFund(String fFund) {
		// LNF070_TYPE,名詞代碼<br/>
		// LNF070_CODE,名詞類別<br/>
		// LNf070_NAME 名詞顯示名稱
		LinkedHashMap<String, String> map = new LinkedHashMap<String, String>();
		List<Map<String, Object>> rowData = this.getJdbc().queryForList(
				"LNLNF070.getdFund", new String[] { fFund });
		for (Map<String, Object> data : rowData) {
			String key = Util.trim(data.get("LNF070_CODE"));
			String value = Util.trim(data.get("LNF070_NAME"));
			map.put(key, value);
		}
		return map;
	}

	@Override
	public HashMap<String, LinkedHashMap<String, String>> getdFundAll() {
		// LNF070_TYPE,名詞代碼<br/>
		// LNF070_CODE,名詞類別<br/>
		// LNf070_NAME 名詞顯示名稱

		HashMap<String, LinkedHashMap<String, String>> result = new HashMap<String, LinkedHashMap<String, String>>();
		// FFUND_CODE

		List<Map<String, Object>> rowData = this.getJdbc().queryForList(
				"LNLNF070.getdFundAll", new String[] {});

		for (Map<String, Object> data : rowData) {
			String fFundCode = Util.trim(data.get("FFUND_CODE"));
			LinkedHashMap<String, String> map = null;
			if (!result.containsKey(fFundCode)) {
				map = new LinkedHashMap<String, String>();
				result.put(fFundCode, map);
			}
			map = result.get(fFundCode);
			String key = Util
					.getRightStr(Util.trim(data.get("LNF070_CODE")), 3);
			String value = Util.trim(data.get("LNF070_NAME"));
			// 當後三碼為000則不需抓取
			if ("000".equals(Util.getRightStr(key, 3))) {
				continue;
			}
			map.put(key, value);
		}
		return result;
	}

	@Override
	public HashMap<String, String> getRateBy070() {
		HashMap<String, String> result = new HashMap<String, String>();
		List<Map<String, Object>> rowData = this.getJdbc().queryForList(
				"findLnF070.ByCode", new String[] {});

		for (Map<String, Object> data : rowData) {
			String code = Util.trim(data.get("CODE"));
			String name = Util.trim(data.get("NAME"));
			result.put(code, name);

		}
		return result;
	}
}
