 package com.mega.eloan.lms.cls.service.impl;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.service.FlowService;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.DocLogEnum;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.lms.base.common.MISRows;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.cls.service.CLS3701Service;
import com.mega.eloan.lms.dao.C126M01ADao;
import com.mega.eloan.lms.dao.C126M01BDao;
import com.mega.eloan.lms.dao.L140M01ADao;
import com.mega.eloan.lms.mfaloan.bean.ELF457;
import com.mega.eloan.lms.mfaloan.bean.ELF604;
import com.mega.eloan.lms.mfaloan.service.MisELF604Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C126M01A;
import com.mega.eloan.lms.model.C126M01B;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

@Service
public class CLS3701ServiceImpl extends AbstractCapService implements CLS3701Service {
	
	private Logger logger = LoggerFactory.getLogger(this.getClass());
	
	@Resource
	C126M01ADao c126m01aDao;
	
	@Resource
	C126M01BDao c126m01bDao;
	
	@Resource
	FlowService flowService;
	
	@Resource
	DocLogService docLogService;
	
	@Resource
	TempDataService tempDataService;
	
	@Resource
	MisdbBASEService misdbBASEService;
	
	@Resource
	MisELF604Service misELF604Service;
	
	@Resource
	L140M01ADao l140m01adao;
	
	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		if (clazz == C126M01A.class) {
			return c126m01aDao.findByMainId(mainId);
		} else if(clazz == C126M01B.class){
			return c126m01bDao.findByMainId(mainId);
		}
		return null;
	}
	
	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		if (clazz == C126M01A.class) {
			return (T)c126m01aDao.findByOid(oid);
		} else if(clazz == C126M01B.class){
			return (T)c126m01bDao.findByOid(oid);
		}
		return null;
	}

	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof C126M01A) {
					if (Util.isEmpty(((C126M01A) model).getOid())) {
						((C126M01A) model).setCreator(user.getUserId());
						((C126M01A) model).setCreateTime(CapDate
								.getCurrentTimestamp());
						c126m01aDao.save((C126M01A) model);

						docLogService.record(((C126M01A) model).getOid(),
								DocLogEnum.CREATE);
					} else {
						if (((C126M01A) model).getDocStatus().equals(
								CreditDocStatusEnum.海外_編製中.getCode())
								|| ((C126M01A) model).getDocStatus().equals(
								CreditDocStatusEnum.海外_待補件.getCode())) {
							// 當文件狀態為編製中時文件亂碼才變更
							((C126M01A) model).setUpdater(user.getUserId());
							((C126M01A) model).setUpdateTime(CapDate
									.getCurrentTimestamp());
							if (!"Y".equals(SimpleContextHolder
									.get(EloanConstants.TEMPSAVE_RUN))) {
								tempDataService.deleteByMainId(((C126M01A) model)
										.getMainId());
								docLogService.record(((C126M01A) model).getOid(),
										DocLogEnum.SAVE);
							}
						}else if(((C126M01A) model).getDocStatus().equals(
								CreditDocStatusEnum.先行動用_已覆核.getCode())){
							((C126M01A) model).setApprover(user.getUserId());
							((C126M01A) model).setApproveTime(CapDate.getCurrentTimestamp());
							docLogService.record(((C126M01A) model).getOid(),
									DocLogEnum.ACCEPT);
						}
						else if(((C126M01A) model).getDocStatus().equals(
								CreditDocStatusEnum.海外_已核准.getCode())){
							
							docLogService.record(((C126M01A) model).getOid(),
									DocLogEnum.COMPLETE);
						}
						c126m01aDao.save((C126M01A) model);
					}
					//回寫aloan
					if (!Util.isEmpty(((C126M01A) model).getRebate())) {
						BigDecimal rebate=((C126M01A) model).getRebate();
						String mainId=((C126M01A) model).getMainId();
						List<C126M01B> c126m01bList=c126m01bDao.findByMainId(mainId);
						if(c126m01bList!=null){
							for(C126M01B c126m01b:c126m01bList){
								L140M01A l140m01a=l140m01adao.findByMainId(c126m01b.getL140m01aMainId());
								if(l140m01a!=null){
									List<Map<String, Object>> datas = misELF604Service.getBycntrNo(l140m01a.getOwnBrId(),l140m01a.getCntrNo());
									if(datas!=null){
										for(Map<String, Object> rowData :datas){
											boolean isNew=false;
											String loan_no = Util.trim(MapUtils.getString(rowData, "LNF033_LOAN_NO"));
											
											ELF604 elf604 = misELF604Service.findByLoanNo(loan_no);
											if(elf604==null){
												isNew=true;
												elf604 = new ELF604();
												elf604.setElf604_loan_no(loan_no);
												elf604.setElf604_1st_date(new Date());
												elf604.setElf604_1st_amt(rebate);
											}
											elf604.setElf604_upd_date(new Date());
											elf604.setElf604_upd_amt(rebate);
											
											List<ELF604> list = new ArrayList<ELF604>();
											list.add(elf604);
											if(isNew){
												MISRows<ELF604> mis_elf604 = new MISRows<ELF604>(ELF604.class);
												mis_elf604.setValues(list);
												insertMisToServer(mis_elf604, "MIS");
											}
											else{
												MISRows<ELF604> mis_elf604 = new MISRows<ELF604>(ELF604.class);
												mis_elf604.setValues(list);
												updateMisToServer(mis_elf604, "MIS",list);
											}
										}
									}
						        }
							}
						}
					}
				}
			}
		}
	}

	@Override
	public void delete(GenericBean... entity) {
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof C126M01A) {
					c126m01aDao.delete((C126M01A) model);
				}
			}
		}
	}
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {

		return c126m01aDao.findPage(search);
	}
	
	
	@Override
	public boolean deleteC126m01as(String[] oids) {
		boolean flag = false;
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<C126M01A> c126m01as = new ArrayList<C126M01A>();
		for (int i = 0, size = oids.length; i < size; i++) {
			C126M01A c126m01a = (C126M01A) findModelByOid(C126M01A.class, oids[i]);
			// 設定刪除並非直接刪除 ，只是標記刪除時間
			c126m01a.setDeletedTime(CapDate.getCurrentTimestamp());
			c126m01a.setUpdater(user.getUserId());
			c126m01as.add(c126m01a);
			docLogService.record(c126m01a.getOid(), DocLogEnum.DELETE);
		}
		if (!c126m01as.isEmpty()) {
			c126m01aDao.save(c126m01as);
			flag = true;
		}
		return flag;
	}

	public List<C126M01A> findAgntNo(String ownBrId,String agntNo,String applyTS_beg,String applyTS_end,String docStatus) {
		ISearch search = c126m01aDao.createSearchTemplete();
		if (Util.isNotEmpty(ownBrId))
			search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", ownBrId);
		if (Util.isNotEmpty(agntNo))
			search.addSearchModeParameters(SearchMode.EQUALS, "agntNo", agntNo);
		if (Util.isNotEmpty(applyTS_beg) && Util.isNotEmpty(applyTS_end))
		{
			String[] reasonStr = { applyTS_beg, applyTS_end };
			search.addSearchModeParameters(SearchMode.BETWEEN, "approveTime", reasonStr);
		}
		if (Util.isNotEmpty(docStatus))
			search.addSearchModeParameters(SearchMode.EQUALS, "docStatus", docStatus);
		
		search.addSearchModeParameters(SearchMode.EQUALS, "isClosed", "N");
		
		return c126m01aDao.find(search);
	}

	@Override
	public void backApproveSave(C126M01A c126m01a) {
		if (c126m01a!=null) {
			docLogService.record(c126m01a.getOid(),
					DocLogEnum.BACK);
			
			c126m01aDao.save(c126m01a);
		}
	}
	
	@Override
	public boolean checkSpecialBank(String ownBrId) {
		boolean checkSpecialBank=false;
		if (Util.equals(Util.getLeftStr(ownBrId, 1), "9" )|| ownBrId.equals("015")) {
			checkSpecialBank=true;
		}
		
		return checkSpecialBank;
	}

	public List<C126M01A> findDuplicateAgntCase(String mainId,String custId,String dupNo,String agntNo,String agntChain,String contractNo,String ownBrId) {
		ISearch search = c126m01aDao.createSearchTemplete();
		if (Util.isNotEmpty(mainId))
			search.addSearchModeParameters(SearchMode.NOT_EQUALS, "mainId", mainId);
		if (Util.isNotEmpty(custId))
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (Util.isNotEmpty(dupNo))
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (Util.isNotEmpty(agntNo))
			search.addSearchModeParameters(SearchMode.EQUALS, "agntNo", agntNo);
		if (Util.isNotEmpty(agntChain))
			search.addSearchModeParameters(SearchMode.EQUALS, "agntChain", agntChain);
		if (Util.isNotEmpty(contractNo))
			search.addSearchModeParameters(SearchMode.EQUALS, "contractNo", contractNo);
		if (Util.isNotEmpty(ownBrId))
			search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", ownBrId);
		if(true){
			search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		}
		return c126m01aDao.find(search);
	}

	public void saveELF604(List<Map<String, Object>> datas){
		List<ELF604> insert_list = new ArrayList<ELF604>();
		List<ELF604> update_list = new ArrayList<ELF604>();
		MISRows<ELF604> insert_elf604 = new MISRows<ELF604>(ELF604.class);
		MISRows<ELF604> update_elf604 = new MISRows<ELF604>(ELF604.class);
		for(Map<String, Object> rowData :datas){
			boolean isNew=false;
			String loan_no = Util.trim(MapUtils.getString(rowData, "elf604_loan_no"));
			String cntrno = Util.trim(MapUtils.getString(rowData, "elf604_cntrno_no"));
			BigDecimal fst_amt= Util.parseBigDecimal( Util.trim(MapUtils.getString(rowData, "elf604_1st_amt")));
			BigDecimal upd_amt= Util.parseBigDecimal( Util.trim(MapUtils.getString(rowData, "elf604_upd_amt")));
			
			ELF604 mis_elf604 = misELF604Service.findByLoanNo(loan_no);
			if(mis_elf604==null){
				isNew=true;
				mis_elf604 = new ELF604();
				mis_elf604.setElf604_loan_no(loan_no);
				mis_elf604.setElf604_1st_date(new Date());
				mis_elf604.setElf604_1st_amt(fst_amt);
			}
			mis_elf604.setElf604_upd_date(new Date());
			mis_elf604.setElf604_upd_amt(upd_amt);
			if(isNew){
				insert_list.add(mis_elf604);
			}
			else{
				update_list.add(mis_elf604);
			}
			//資料同步寫回房仲引介案件
			List<L140M01A> l140m01alist=l140m01adao.findByCntrNo(cntrno);
			if(l140m01alist.size()>0){
				for(L140M01A l140m01a:l140m01alist){
					String l140m01aMainId=l140m01a.getMainId();
					C126M01B c126m01b=c126m01bDao.findByL140m01aMainId(l140m01aMainId);
					if(c126m01b!=null){
						List<C126M01A> c126m01aList=c126m01aDao.findByMainId(c126m01b.getMainId());
						if(c126m01aList.size()>0){
							for(C126M01A c126m01a:c126m01aList){
								if(c126m01a!=null){
									c126m01a.setRebate(upd_amt);
									docLogService.record(c126m01a.getOid(),DocLogEnum.SAVE);
								}
							}
						}
					}
				}
			}
		}
		if(insert_list.size()>0){
			MISRows<ELF604> mis_elf604 = new MISRows<ELF604>(ELF604.class);
			mis_elf604.setValues(insert_list);
			insertMisToServer(mis_elf604, "MIS");
		}
		if(update_list.size()>0){
//			for(ELF604 elf604:update_list){
//				misELF604Service.update(elf604);
//			}
			MISRows<ELF604> mis_elf604 = new MISRows<ELF604>(ELF604.class);
			mis_elf604.setValues(update_list);
			updateMisToServer(mis_elf604, "MIS",update_list);
		}
	}
	private <T> void insertMisToServer(MISRows<T> misRows, String schemaName) {
		if (Util.isNotEmpty(misRows.getKeyValues())) {
			misdbBASEService.insert(misRows.getMsgFmtParam(schemaName),
					misRows.getTypes(), misRows.getValues());
			logger.info("{}=======>{}", misRows.getTableNm(), "Insert");
		}
	}
	private <T> void updateMisToServer(MISRows<T> misRows, String schemaName, List<ELF604> update_list) {
		if (Util.isNotEmpty(misRows.getKeyValues())) {
			Object[] key=misRows.getKeyValues();
			int[] type1 = {java.sql.Types.DATE,java.sql.Types.CHAR};
			int[] type2 = {java.sql.Types.DECIMAL,java.sql.Types.CHAR};
			
			List<Object[]> list1 = new ArrayList<Object[]>();
			List<Object[]> list2 = new ArrayList<Object[]>();
			if(update_list.size()>0){
				for(ELF604 elf604:update_list){
					list1.add(new Object[]{new Date()				 ,elf604.getElf604_loan_no()});
					list2.add(new Object[]{elf604.getElf604_upd_amt(),elf604.getElf604_loan_no()});
				}
			}										
			misdbBASEService.update(new Object[] { "MIS.ELF604", "ELF604_UPD_DATE=?", "ELF604_LOAN_NO=? " }, type1, list1);
			misdbBASEService.update(new Object[] { "MIS.ELF604", "ELF604_UPD_AMT=?" , "ELF604_LOAN_NO=? " }, type2, list2);
			logger.info("{}=======>{}", misRows.getTableNm(), "update");
		}
	}
}
