package com.mega.eloan.lms.fms.handler.form;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.DocLogEnum;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.fms.pages.CLS3601M01Page;
import com.mega.eloan.lms.fms.pages.CLS3601M02Page;
import com.mega.eloan.lms.mfaloan.bean.ELF500;
import com.mega.eloan.lms.mfaloan.service.MisELF500Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C360M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;


@Scope("request")
@Controller("cls3601formhandler")
public class CLS3601FormHandler extends AbstractFormHandler {
	private static final int MAXLEN_C360M01A_CHGREASON = StrUtils.getEntityFileldLegth(C360M01A.class, "chgReason", 900);
	private static final int MAXLEN_C360M01A_AFTDSCR = StrUtils.getEntityFileldLegth(C360M01A.class, "aftDscr", 500);
	
	@Resource
	CLSService clsService;
		
	@Resource
	UserInfoService userInfoService;
	
	@Resource
	TempDataService tempDataService;
	
	@Resource
	DocCheckService docCheckService;
	
	@Resource
	DocLogService docLogService;
	
	@Resource
	UserInfoService userService;
	
	@Resource
	BranchService branchService;
	
	@Resource
	MisdbBASEService misdbBASEService;
	
	@Resource
	MisELF500Service misELF500Service;
	
	
	Properties prop_cls3601m01 = MessageBundleScriptCreator.getComponentResource(CLS3601M01Page.class);
	Properties prop_cls3601m02 = MessageBundleScriptCreator.getComponentResource(CLS3601M02Page.class);
	Properties prop_AbstractEloanPage = MessageBundleScriptCreator.getComponentResource(AbstractEloanPage.class);
	
	
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult newC360M01A(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		CapAjaxFormResult result = new CapAjaxFormResult();
		String itemType = params.getString("itemType");
		String cntrNo = params.getString("cntrNo");
//		String loanNo = params.getString("loanNo");
		
		C360M01A c360m01a = null;
		Map<String, String> itemType_map = clsService.get_codeTypeWithOrder("C360M01A_itemType");
		if(itemType_map.containsKey(itemType)){
			if(Util.equals("01", itemType)){
				if(Util.isEmpty(cntrNo)){
					throw new CapMessageException("cntrNo is empty", getClass());		
				}
				
				check_cntrNo_belong_brNo(cntrNo, user.getUnitNo());
				
				ELF500 elf500 = misELF500Service.findByCntrNo(cntrNo);
				if(elf500==null){
					throw new CapMessageException(prop_cls3601m01.getProperty("C360M01A.cntrNo")+"："+cntrNo+" 未存在於中心的消金額度介面檔", getClass());
				}else{
					Map<String, String> grpCntrNo_TPC_321_map = clsService.get_codeTypeWithOrder("grpCntrNo_TPC_321");
					if(!grpCntrNo_TPC_321_map.containsKey(elf500.getElf500_grp_cntrno())){
						throw new CapMessageException(prop_cls3601m01.getProperty("C360M01A.cntrNo")+"："+cntrNo+" 非台電整批消貸案件", getClass());			
					}
					
					String custId = elf500.getElf500_custid();
					String dupNo = elf500.getElf500_dupno();
					String custName = clsService.get0024_custName(custId, dupNo);
					c360m01a = newC360M01A_itemType01(user, itemType, custId, dupNo, custName, elf500);
				}
			}else if(Util.equals("02", itemType)){
				if(Util.isEmpty(cntrNo)){
					throw new CapMessageException("cntrNo is empty", getClass());		
				}
				
				check_cntrNo_belong_brNo(cntrNo, user.getUnitNo());
				
				ELF500 elf500 = misELF500Service.findByCntrNo(cntrNo);
				if(elf500==null){
					throw new CapMessageException(prop_cls3601m01.getProperty("C360M01A.cntrNo")+"："+cntrNo+" 未存在於中心的消金額度介面檔", getClass());
				}else{
					String elf500_company_id = Util.trim(elf500.getElf500_company_id());
					if(Util.isEmpty(elf500_company_id)){
						throw new CapMessageException(prop_cls3601m01.getProperty("C360M01A.cntrNo")+"："+cntrNo+" 非青創案件", getClass());			
					}
					
					
					String custId = elf500.getElf500_custid();
					String dupNo = elf500.getElf500_dupno();
					String custName = clsService.get0024_custName(custId, dupNo);
					c360m01a = newC360M01A_itemType02(user, itemType, custId, dupNo, custName, elf500);
				}
			}else{
				//...
			}
		}else{
			throw new CapMessageException("{"+itemType+"}unvalid", getClass());
		}

		if(c360m01a!=null){
			return defaultResult(params, c360m01a, result);
		}
		return result;
	}
	
	private void check_cntrNo_belong_brNo(String cntrNo, String brNo)
	throws CapMessageException{
		if(!cntrNo.startsWith(brNo)){
			throw new CapMessageException(MessageFormat.format(prop_cls3601m01.getProperty("msg.04"), brNo, cntrNo), getClass());
		}
	}
	
	private C360M01A newC360M01A_itemType01(MegaSSOUserDetails user , String itemType, String custId, String dupNo, String custName, ELF500 elf500){
		String befItem = Util.trim(elf500.getElf500_welfare_cmte());
		C360M01A c360m01a = new C360M01A();
		c360m01a.setMainId(IDGenerator.getUUID());
		c360m01a.setTypCd(UtilConstants.Casedoc.typCd.DBU);
		c360m01a.setCustId(custId);
		c360m01a.setDupNo(dupNo);
		c360m01a.setCustName(custName);
		c360m01a.setOwnBrId(user.getUnitNo());
		c360m01a.setDocStatus(FlowDocStatusEnum.編製中.getCode());
		c360m01a.setItemType(itemType);
		c360m01a.setCntrNo(elf500.getElf500_cntrno());                                       
		c360m01a.setLoanNo("");                                       
		c360m01a.setBefItem(befItem);                                      
		c360m01a.setAftItem("");                                      
		c360m01a.setChgReason("");                                    
		c360m01a.setBefDscr(build_itemDscr(c360m01a.getItemType(), c360m01a.getBefItem()));                                      
		c360m01a.setAftDscr(build_itemDscr(c360m01a.getItemType(), c360m01a.getAftItem()));       
		c360m01a.setCreator(user.getUserId()); 
		c360m01a.setCreateTime(CapDate.getCurrentTimestamp());
		//~~~~~~~~~~~~~~~~~
		clsService.save(c360m01a);
		return c360m01a;
	}
	
	private C360M01A newC360M01A_itemType02(MegaSSOUserDetails user , String itemType, String custId, String dupNo, String custName, ELF500 elf500){
		String befItem = itemKey_02(elf500.getElf500_company_id(), elf500.getElf500_company_nm(), elf500.getElf500_cmbull_key());
		C360M01A c360m01a = new C360M01A();
		c360m01a.setMainId(IDGenerator.getUUID());
		c360m01a.setTypCd(UtilConstants.Casedoc.typCd.DBU);
		c360m01a.setCustId(custId);
		c360m01a.setDupNo(dupNo);
		c360m01a.setCustName(custName);
		c360m01a.setOwnBrId(user.getUnitNo());
		c360m01a.setDocStatus(FlowDocStatusEnum.編製中.getCode());
		c360m01a.setItemType(itemType);
		c360m01a.setCntrNo(elf500.getElf500_cntrno());                                       
		c360m01a.setLoanNo("");                                       
		c360m01a.setBefItem(befItem);        
		if(true){
			c360m01a.setAftItem(befItem);        
		}                                      
		c360m01a.setChgReason("");                                    
		c360m01a.setBefDscr(build_itemDscr(c360m01a.getItemType(), c360m01a.getBefItem()));                                      
		c360m01a.setAftDscr(build_itemDscr(c360m01a.getItemType(), c360m01a.getAftItem()));       
		c360m01a.setCreator(user.getUserId()); 
		c360m01a.setCreateTime(CapDate.getCurrentTimestamp());
		//~~~~~~~~~~~~~~~~~
		clsService.save(c360m01a);
		return c360m01a;
	}
	
	
	@DomainAuth(AuthType.Modify)
	public IResult delC360M01A(PageParameters params)
			throws CapException {		
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C360M01A c360m01a = clsService.findC360M01A_oid(mainOid);
		if(c360m01a==null){
			throw new CapMessageException(mainOid + "not found", getClass());
		}else{
			clsService.daoDelete(c360m01a);
		}		
		return result;
	}
		
	private CapAjaxFormResult defaultResult(PageParameters params, C360M01A meta,
			CapAjaxFormResult result) throws CapException {		
		// required information
		result.set(EloanConstants.PAGE, Util.trim(params.getString(EloanConstants.PAGE)));
		result.set(EloanConstants.MAIN_OID, Util.trim(meta.getOid()));
		result.set(EloanConstants.MAIN_DOC_STATUS, Util.trim(meta.getDocStatus()));
		result.set(EloanConstants.MAIN_ID, Util.trim(meta.getMainId()));		
		return result;
	}
	
	@DomainAuth(value = AuthType.Query)
	public IResult query(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C360M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = clsService.findC360M01A_oid(mainOid);
			
			LMSUtil.addMetaToResult(result, meta, new String[]{"custId", "dupNo", "custName", 
					"ownBrId", "createTime", "updateTime", "itemType", "cntrNo", "loanNo", "befItem", "aftItem", "chgReason", "befDscr", "aftDscr"
				});
			result.set("creator", Util.trim(userInfoService.getUserName(meta.getCreator())));
			result.set("updater", Util.trim(userInfoService.getUserName(meta.getUpdater())));
			result.set("ownBrIdDesc", caseBrIdDesc(meta.getOwnBrId()));
			
			String itemType = Util.trim(meta.getItemType());
			if(Util.equals("01", itemType)){
			}else if(Util.equals("02", itemType)){
				String[] arr_bef = split_itemDesc(itemType, meta.getBefItem());
				String[] arr_aft = split_itemDesc(itemType, meta.getAftItem());
				result.set("befItem_company_id", Util.trim(arr_bef[0]));
				result.set("aftItem_company_id", Util.trim(arr_aft[0]));
				result.set("befItem_company_nm", Util.trim(arr_bef[1]));
				result.set("aftItem_company_nm", Util.trim(arr_aft[1]));
				result.set("befItem_cmbull_key", Util.trim(arr_bef[2]));
				result.set("aftItem_cmbull_key", Util.trim(arr_aft[2]));
				result.set("befItem_cmbull_keyDesc", get_ecoNm(Util.trim(arr_bef[2])));
				result.set("aftItem_cmbull_keyDesc", get_ecoNm(Util.trim(arr_aft[2])));
			}
			String docStatus = meta.getDocStatus();			
			if(Util.equals(FlowDocStatusEnum.編製中.getCode(), docStatus)){
				docStatus = prop_AbstractEloanPage.getProperty("docStatus.010");
			}else if(Util.equals(FlowDocStatusEnum.待覆核.getCode(), docStatus)){
				docStatus = prop_AbstractEloanPage.getProperty("docStatus.020");
			}else if(Util.equals(FlowDocStatusEnum.已核准.getCode(), docStatus)){
				docStatus = prop_AbstractEloanPage.getProperty("docStatus.030");
			}
			result.set("docStatus", docStatus);			
		}
		
		return defaultResult(params, meta, result);
	}	
	
	private String get_ecoNm(String ecoCd){
		if(Util.isEmpty(ecoCd)){
			return "";
		}
		Map<String, Object> map = misdbBASEService.findIndustryObjectCodeByBSTBL(ecoCd);
		if(MapUtils.isNotEmpty(map)){
			return Util.trim(map.get("ECONM"));
		}
		return "";	
	}
	
	private String caseBrIdDesc(String s){		
		String val = Util.trim(s);
		if(Util.isNotEmpty(val)){
			return branchService.getBranchName(val);
		}
		return "";
	}
	
	/**
	 * 儲存
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(AuthType.Modify)
	public IResult saveMain(PageParameters params)
			throws CapException {
		return _saveAction(params, "N");
	}
	
	private CapAjaxFormResult _saveAction(PageParameters params,String tempSave)
	throws CapException{
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, tempSave);
		boolean allowIncomplete = Util.equals("Y", params.getString("allowIncomplete"));
		//===
		String KEY = "saveOkFlag";
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set(KEY, false);
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String chgReason = Util.trim(params.getString("chgReason"));
		C360M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			try{
				meta = clsService.findC360M01A_oid(mainOid);
				
				String page = params.getString(EloanConstants.PAGE);
				if ("01".equals(page)) {
					String itemType = Util.trim(meta.getItemType());
					if(Util.equals("01", itemType)){
						CapBeanUtil.map2Bean(params, meta, new String[] {"aftItem"});	
					}else if(Util.equals("02", itemType)){
						String company_id = Util.trim(params.getString("aftItem_company_id"));
						String company_nm = Util.trim(params.getString("aftItem_company_nm"));
						String cmbull_key = Util.trim(params.getString("aftItem_cmbull_key"));
						meta.setAftItem(itemKey_02(company_id, company_nm, cmbull_key));
					}
					
					meta.setChgReason(Util.truncateString(chgReason, MAXLEN_C360M01A_CHGREASON));
					meta.setAftDscr(Util.truncateString(build_itemDscr(meta.getItemType(), meta.getAftItem()), MAXLEN_C360M01A_AFTDSCR) );
				}
				//---
				meta.setDeletedTime(null);
				meta.setUpdater(user.getUserId());
				meta.setUpdateTime(CapDate.getCurrentTimestamp());
				clsService.save(meta);				
				// ===
				if (Util.notEquals("Y", SimpleContextHolder.get(EloanConstants.TEMPSAVE_RUN))) {
					// 在tempSave<>Y,若有未填欄位,丟 CapMessageException, 讓
					// saveOkFlag==false
					String msg = checkIncompleteMsg(meta, chgReason);
					if (Util.isNotEmpty(msg)) {
						if (allowIncomplete) {
							result.set("IncompleteMsg", msg);
						} else {
							throw new CapMessageException(msg, getClass());
						}
					}
				}
				result.set(KEY, true);	
			}catch(Exception e){
				logger.error(StrUtils.getStackTrace(e));
				throw new CapException(e, getClass());
			}		
		}
		
		result.add(query(params));
		
		return result;
	}
	
	private String checkIncompleteMsg(C360M01A model, String chgReason){
		String itemType = Util.trim(model.getItemType());
		String befItem = Util.trim(model.getBefItem());
		String aftItem = Util.trim(model.getAftItem());
		if(Util.equals(befItem, aftItem)){
			//msg.05=資料未異動
			return MessageFormat.format(prop_cls3601m01.getProperty("msg.05"), befItem, aftItem);
		}
		
		List<String> list = new ArrayList<String>();
		//=====================
		//修改後資料 
		if(true){
			if(true){
				//可能有的需求, 是要把值給清空
				//所以只針對特定的 itemType 才限制其修改後資料不可空白
				String[] not_empty_itemType = {"01","02"};
				if(Util.isEmpty(aftItem) && CrsUtil.inCollection(itemType, not_empty_itemType)){
					list.add("請輸入"+prop_cls3601m01.getProperty("C360M01A.aftDscr"));	
				}
			}
			
			String unvalid_aftItem = MessageFormat.format(prop_cls3601m01.getProperty("msg.02"), aftItem);
			if(Util.equals("01", itemType)){
				if(Util.isNotEmpty(aftItem)){
					Map<String, String> map = clsService.get_codeTypeWithOrder("TPCWelfareCommitteeNo");
					if(!map.containsKey(aftItem)){
						list.add(unvalid_aftItem);
					}	
				}				
			}else if(Util.equals("02", itemType)){
				String[] arr = split_itemDesc(itemType, aftItem);
				if(arr.length==3){
					String company_id = Util.trim(arr[0]);
					if(Util.isEmpty(company_id)){
						list.add(prop_cls3601m02.getProperty("C360M01A.itemType02.company_id")+"不可空白");
					}
					//~~~~~~~~~~
					String company_nm = Util.trim(arr[1]);
					if(Util.isEmpty(company_nm)){
						list.add(prop_cls3601m02.getProperty("C360M01A.itemType02.company_nm")+"不可空白");
					}
					//~~~~~~~~~~
					String ecoCd = Util.trim(arr[2]);
					Map<String, Object> map = misdbBASEService.findIndustryObjectCodeByBSTBL_mark(ecoCd, "0");
					if(MapUtils.isEmpty(map)){
						list.add(ecoCd+"非合理的"+prop_cls3601m02.getProperty("C360M01A.itemType02.cmbull_key"));
					}	
				}
			}
		}
		//=====================
		//檢核  調整原因 
		if(true){
			if (Util.isEmpty(chgReason)) {
				list.add("請輸入"+prop_cls3601m01.getProperty("C360M01A.chgReason"));
			}else{
				if (Util.notEquals(model.getChgReason(), chgReason)) {
					list.add(prop_cls3601m01.getProperty("C360M01A.chgReason")+"長度超過限制");
				}				
			}
		}		
		return StringUtils.join(list, "<br/>"); 
	}
	
	private String build_itemDscr(String itemType, String key){
		if(Util.equals("01", itemType)){
			Map<String, String> map = clsService.get_codeTypeWithOrder("TPCWelfareCommitteeNo");
			if(map.containsKey(key)){
				return key+":"+LMSUtil.getDesc(map, key);	
			}
		}else if(Util.equals("02", itemType)){
			String[] arr = split_itemDesc(itemType, key);
			if(arr.length!=3){
				return key;	
			}else{
				String elf500_company_id = arr[0];
				String elf500_company_nm = arr[1];
				String elf500_cmbull_key = arr[2];
				
				return elf500_company_id+" "+elf500_company_nm
					+"("+elf500_cmbull_key+":"+get_ecoNm(elf500_cmbull_key)+")";	
			}
		}
		return key;
	}
	
	private String itemKey_02(String company_id, String company_nm, String cmbull_key){
		return StringUtils.join(new String[]{Util.trim(company_id), Util.trim(company_nm), Util.trim(cmbull_key)}, "^");
	}
	private String[] split_itemDesc(String itemType, String itemDesc){
		boolean empty_itemDesc = Util.isEmpty(Util.trim(itemDesc));
		if(Util.equals(itemType, "02")){
			//String[] arr = StringUtils.split(itemDesc, "^"); 
			/*
			    String[] arr = StringUtils.split("^^", '^'); => arr.length=0
			  	String[] arr = StringUtils.split("1^^", '^') => arr.length=1			 
			 */
			if(empty_itemDesc){
				return new String[]{"", "", ""};
			}else{
				String[] arr = StringUtils.splitPreserveAllTokens(itemDesc, "^");			
				return arr;
			}
		}
		
		return null;
	}
	
	private String check_still_befItem(C360M01A meta){		
		String cntrNo = Util.trim(meta.getCntrNo());
//		String loanNo = Util.trim(meta.getLoanNo());
		String itemType = Util.trim(meta.getItemType());
		String befItem = Util.trim(meta.getBefItem());
		if(Util.equals("01", itemType)){
			ELF500 elf500 = misELF500Service.findByCntrNo(cntrNo);
			if(elf500==null){
				return cntrNo+" not found";
			}else{
				String currentVal = Util.trim(elf500.getElf500_welfare_cmte());
				if(!Util.equals(befItem, currentVal)){
					return MessageFormat.format(prop_cls3601m01.getProperty("msg.01"), befItem, currentVal);
				}
			}
		}else if(Util.equals("02", itemType)){
			ELF500 elf500 = misELF500Service.findByCntrNo(cntrNo);
			if(elf500==null){
				return cntrNo+" not found";
			}else{
				String currentVal = itemKey_02(elf500.getElf500_company_id(), elf500.getElf500_company_nm(), elf500.getElf500_cmbull_key());
				if(!Util.equals(befItem, currentVal)){
					return prop_cls3601m01.getProperty("msg.06");//msg.06=來源資料已異動
				}
			}
		}		
		return "";
	}
	
	private void update_befItem_to_aftItem(C360M01A meta){		
		String cntrNo = Util.trim(meta.getCntrNo());
//		String loanNo = Util.trim(meta.getLoanNo());
		String itemType = Util.trim(meta.getItemType());
		String aftItem = Util.trim(meta.getAftItem());
		if(Util.equals("01", itemType)){
			int[] type = {java.sql.Types.CHAR, java.sql.Types.CHAR};
				
			List<Object[]> list = new ArrayList<Object[]>();						
			if(true){
			list.add(new Object[]{aftItem, cntrNo});
			}										
			
			misdbBASEService.update(new Object[] { "MIS.ELF500", "elf500_welfare_cmte=? " , "elf500_cntrNo=? " }, type, list);				
		}else if(Util.equals("02", itemType)){
			int[] type = {java.sql.Types.CHAR, java.sql.Types.CHAR, java.sql.Types.CHAR, java.sql.Types.CHAR};
				
			List<Object[]> list = new ArrayList<Object[]>();						
			if(true){
				String[] arr = split_itemDesc(itemType, aftItem);
				list.add(new Object[]{arr[0], arr[1], arr[2], cntrNo});
			}										
			
			misdbBASEService.update(new Object[] { "MIS.ELF500", "elf500_company_id=? , elf500_company_nm=?, elf500_cmbull_key=? " , "elf500_cntrNo=? " }, type, list);
			
		}		
	}
	
	@DomainAuth(AuthType.Modify + AuthType.Accept)
	public IResult flowAction(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");

		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String decisionExpr = Util.trim(params.getString("decisionExpr"));
		
		C360M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = clsService.findC360M01A_oid(mainOid);
			
			String errMsg = "";

			String docStatus = Util.trim(meta.getDocStatus());	
			
			String nextStatus = "";
			DocLogEnum _DocLogEnum = null;
			if(Util.equals(FlowDocStatusEnum.編製中.getCode(), docStatus)){
				if(Util.isEmpty(errMsg)){
					errMsg = check_still_befItem(meta);
				}
				
				nextStatus = FlowDocStatusEnum.待覆核.getCode();
				_DocLogEnum = DocLogEnum.FORWARD;
			}else if(Util.equals(FlowDocStatusEnum.待覆核.getCode(), docStatus)){
				//核定、退回
				if(Util.equals("核定", decisionExpr)){	
					if(Util.isEmpty(errMsg)){
						errMsg = check_still_befItem(meta);
					}
					//===============
					if(Util.isEmpty(errMsg)){
						if(Util.equals(user.getUserId(), meta.getUpdater())){
							errMsg = RespMsgHelper.getMessage("EFD0053");
						}else{
							nextStatus = FlowDocStatusEnum.已核准.getCode();
							_DocLogEnum = DocLogEnum.ACCEPT;	
						}
					}
					
				}else if(Util.equals("退回", decisionExpr)){
					nextStatus = FlowDocStatusEnum.編製中.getCode();
					_DocLogEnum = DocLogEnum.BACK;
				}
			}else if(Util.equals(FlowDocStatusEnum.已核准.getCode(), docStatus)){	

			}
			
						
			if(Util.isNotEmpty(errMsg)){				
				throw new CapMessageException(errMsg, getClass());
			}else{
				if(Util.isEmpty(nextStatus)){
					throw new CapMessageException("流程異常["+docStatus+"]", getClass());
				}	
			}
			if(true){				
				if(Util.equals(nextStatus, FlowDocStatusEnum.已核准.getCode())){
					update_befItem_to_aftItem(meta);
					//~~~~~~~~~~~~~~~~~
					meta.setApprover(user.getUserId());
					meta.setApproveTime(CapDate.getCurrentTimestamp());
				}else if(Util.equals(nextStatus, FlowDocStatusEnum.編製中.getCode())){
					meta.setApprover(null);
					meta.setApproveTime(null);
				}else if(Util.equals(nextStatus, FlowDocStatusEnum.待覆核.getCode())){
					meta.setApprover(null);
					meta.setApproveTime(null);
				}
				meta.setDocStatus(nextStatus);
				//用 daoSave 避免把 approver 寫到 updater
				clsService.daoSave(meta);
				
				if(_DocLogEnum!=null){
					docLogService.record(meta.getOid(), _DocLogEnum);	
				}				
			}			
			tempDataService.deleteByMainId(meta.getMainId());
			docCheckService.unlockDocByMainIdUser(meta.getMainId(), user.getUserId());
		}
		return defaultResult( params, meta, result);
	}
}
