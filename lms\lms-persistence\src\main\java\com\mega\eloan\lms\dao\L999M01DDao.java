/* 
 * L999M01DDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L999M01D;

/** 企金約據書項目描述檔 **/
public interface L999M01DDao extends IGenericDao<L999M01D> {

	L999M01D findByOid(String oid);

	List<L999M01D> findByMainId(String mainId);

	L999M01D findByUniqueKey(String mainId, String itemType);

	List<L999M01D> findByIndex01(String mainId, String itemType);
}