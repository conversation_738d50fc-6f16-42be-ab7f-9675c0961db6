/* 
 * L300S01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 覆審考評表明細檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L300S01A", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L300S01A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 
	 * 文件編號<p/>
	 * L300M01A.mainId
	 */
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 項目欄位名稱 **/
	@Size(max=30)
	@Column(name="ITEMNAME", length=30, columnDefinition="VARCHAR(30)")
	private String itemName;

	/** 
	 * 項目類別<p/>
	 * YN:是否CNT:數量
	 */
	@Size(max=3)
	@Column(name="ITEMTYPE", length=3, columnDefinition="VARCHAR(3)")
	private String itemType;

	/** 項目次數 **/
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="ITEMCNT", columnDefinition="DECIMAL(7,2)")
	private BigDecimal itemCnt;

	/** 項目分數 **/
	@Digits(integer=3, fraction=2, groups = Check.class)
	@Column(name="ITEMSCORE", columnDefinition="DECIMAL(5,2)")
	private BigDecimal itemScore;

	/** 
	 * 扣分<p/>
	 * itemAll = (itemScor * itemCnt)
	 */
	@Digits(integer=15, fraction=2, groups = Check.class)
	@Column(name="ITEMALL", columnDefinition="DECIMAL(17,2)")
	private BigDecimal itemAll;

	/** 項目說明 **/
	@Size(max=10240)
	@Column(name="ITEMDSCR", length=10240, columnDefinition="VARCHAR(10240)")
	private String itemDscr;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String Creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 
	 * 取得文件編號<p/>
	 * L300M01A.mainId
	 */
	public String getMainId() {
		return this.mainId;
	}
	/**
	 *  設定文件編號<p/>
	 *  L300M01A.mainId
	 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得項目欄位名稱 **/
	public String getItemName() {
		return this.itemName;
	}
	/** 設定項目欄位名稱 **/
	public void setItemName(String value) {
		this.itemName = value;
	}

	/** 
	 * 取得項目類別<p/>
	 * YN:是否CNT:數量
	 */
	public String getItemType() {
		return this.itemType;
	}
	/**
	 *  設定項目類別<p/>
	 *  YN:是否CNT:數量
	 **/
	public void setItemType(String value) {
		this.itemType = value;
	}

	/** 取得項目次數 **/
	public BigDecimal getItemCnt() {
		return this.itemCnt;
	}
	/** 設定項目次數 **/
	public void setItemCnt(BigDecimal value) {
		this.itemCnt = value;
	}

	/** 取得項目分數 **/
	public BigDecimal getItemScore() {
		return this.itemScore;
	}
	/** 設定項目分數 **/
	public void setItemScore(BigDecimal value) {
		this.itemScore = value;
	}

	/** 
	 * 取得扣分<p/>
	 * itemAll = (itemScor * itemCnt)
	 */
	public BigDecimal getItemAll() {
		return this.itemAll;
	}
	/**
	 *  設定扣分<p/>
	 *  itemAll = (itemScor * itemCnt)
	 **/
	public void setItemAll(BigDecimal value) {
		this.itemAll = value;
	}

	/** 取得項目說明 **/
	public String getItemDscr() {
		return this.itemDscr;
	}
	/** 設定項目說明 **/
	public void setItemDscr(String value) {
		this.itemDscr = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.Creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.Creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
