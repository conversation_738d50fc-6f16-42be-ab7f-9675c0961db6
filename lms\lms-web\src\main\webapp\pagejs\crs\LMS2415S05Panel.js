$(function(){
	// 預設放入responseJSON.mainId
	var lms2415s05gridMainId = $("#lms2415s05gridMainId");
	if (!lms2415s05gridMainId.val()) {
		lms2415s05gridMainId.val(responseJSON.mainId);
	}
	// 如未有GRID生成時，產生GRID
	if ($("#lms2415s05grid").is(':empty')) {
		$("#lms2415s05grid").iGrid({
			handler : 'lms2415gridhandler',
			height : 350,
			sortname : 'uploadTime',
			multiselect : true,
			postData : {
				mainId : lms2415s05gridMainId.val(),
				formAction : "queryDocFile"
			},
			colModel : [ {
				colHeader : i18n.lms2415s05['docfile.filename'],//"檔名"
				name : 'srcFileName',
				width : 100,
				formatter : 'click',
				onclick : openDocFile
			}, {
				colHeader : i18n.lms2415s05['docfile.uploadTime'],//"上傳時間"
				name : 'uploadTime',
				width : 165,
				sortable : true
			}, {
				colHeader : "oid",
				name : 'oid',
				hidden : true
			}],
			ondblClickRow : function(rowid){
				openDocFile(null, null, $("#lms2415s05grid").getRowData(rowid));
			}
		});
		$("#lms2415s05BtnSel").click(function(){
			var limitFileSize=5242880;//5*1024*1024
			var $gridview = $("#lms2415s05grid");
			var gridData = $gridview.getRowData();
			for (var d in gridData){
				limitFileSize -= (gridData[d].fileSize || 0);
			}
			MegaApi.uploadDialog({
				fieldId: "AttachFile",
				title: i18n && i18n.def.insertfile || "請選擇附加檔案",
				subTitle:i18n.def('insertfileSize',{'fileSize':(limitFileSize/1048576).toFixed(2)}),
				fileCheck: false,
				successMsg: false,
				height:140,
				limitSize:limitFileSize,
				data: {
					mainOid : responseJSON.oid,
					mainId : lms2415s05gridMainId.val()
				}
				}).done(function(){
					$gridview.trigger('reloadGrid'); 
			});
		});
		$("#lms2415s05BtnDel").click(function(){
			var $gridview = $("#lms2415s05grid");
			var id = $gridview.getGridParam('selarrrow');
			var content = [];
			for (var i = 0; i < id.length; i++) {
				if (id[i] != "") {
					var datas = $gridview.getRowData(id[i]);
					content.push(datas.oid);
				}
			}
			if (content.length == 0) {
				API.showErrorMessage(i18n.def.action_002);
			} else {
				API.flowConfirmAction({
					message: i18n.def['confirmDelete'],
					handler: "lmscommonformhandler",
					action: "deleteUploadFile",			       
					data: {
						oids: content			            
					}
					}).done(function(){
						API.showPopMessage(i18n.def['confirmDeleteSuccess']);
						$gridview.trigger("reloadGrid");
				});	
			}    	    
		});
	}
	
	function openDocFile(cellvalue, options, rowObject) {
		ilog.debug(rowObject);
		$.capFileDownload({
	        handler : "simplefiledwnhandler",
	        data : {
	            fileOid : rowObject.oid
	        }
		});
	};
	
});