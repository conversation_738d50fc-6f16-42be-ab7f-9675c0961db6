package com.mega.eloan.lms.mfaloan.bean;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import tw.com.iisi.cap.model.GenericBean;

/** 關係人基本資料 **/
public class OTS_RKAPPLICANTOVS extends GenericBean{

	private static final long serialVersionUID = 1L;

	/** 分行別 **/
	@Column(name="BR_CD", length=3, columnDefinition="CHAR(3)", nullable=false,unique = true)
	private String br_cd;

	/** NOTES文件編號 **/
	@Column(name="NOTEID", length=32, columnDefinition="CHAR(32)", nullable=false,unique = true)
	private String noteid;

	/** 
	 * 評等日期<p/>
	 * 最終評等日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="RATING_DATE", columnDefinition="DATE", nullable=false,unique = true)
	private Date rating_date;

	/** 評等文件編號 **/
	@Column(name="RATING_ID", length=32, columnDefinition="CHAR(32)", nullable=false,unique = true)
	private String rating_id;

	/** 客戶統一編號 **/
	@Column(name="CUSTID", length=10, columnDefinition="CHAR(10)", nullable=false,unique = true)
	private String custid;

	/** 重複序號 **/
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)", nullable=false,unique = true)
	private String dupno;

	/** 主借款人統一編號 **/
	@Column(name="CUST_KEY", length=10, columnDefinition="CHAR(10)", nullable=false,unique = true)
	private String cust_key;

	/** 
	 * 授信科目<p/>
	 * 3-4碼授信科目
	 */
	@Column(name="LOAN_CODE", length=4, columnDefinition="VARCHAR(4)", nullable=false,unique = true)
	private String loan_code;

	/** 
	 * 評等模型類別(c121m01a.mowType)
	 */
	@Column(name="MOWTYPE", length=1, columnDefinition="CHAR(1)", nullable=false,unique = true)
	private String mowtype;
	
	/** 
	 * 房貸/非房貸註記(l141m01c.modelType)
	 * N=非房貸、M=房貸
	 */
	@Column(name="MOWTYPE2", length=1, columnDefinition="CHAR(1)")
	private String mowtype2;
	
	/** 
	 * 採用模型註記
	 * 國別碼(l120m01a.ratingFlag)
	 */
	@Column(name="MOWTYPE_COUNTRY", length=2, columnDefinition="CHAR(2)", nullable=false,unique = true)
	private String mowtype_country;

	/** 模型版本-大版 **/
	@Column(name="MOWVER1", columnDefinition="DEC(5,0)", nullable=false,unique = true)
	private Integer mowver1;

	/** 模型版本-小版 **/
	@Column(name="MOWVER2", columnDefinition="DEC(5,0)", nullable=false,unique = true)
	private Integer mowver2;

	/** 
	 * 科目<p/>
	 * 8碼會計科目
	 */
	@Column(name="SUBJCODE", length=8, columnDefinition="VARCHAR(8)")
	private String subjcode;

	/** 相關身分 **/
	@Column(name="LNGEFLAG", length=1, columnDefinition="CHAR(1)")
	private String lngeflag;

	/** 文件狀態 **/
	@Column(name="DOCSTATUS", length=2, columnDefinition="CHAR(2)")
	private String docstatus;

	/** 出生年月日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="DOB", columnDefinition="DATE")
	private Date dob;

	/** 學歷 **/
	@Column(name="EDUCATION", columnDefinition="DEC(2,0)")
	private Integer education;

	/** 婚姻 **/
	@Column(name="MARRIAGE", columnDefinition="DEC(2,0)")
	private Integer marriage;

	/** 扶養子女數 **/
	@Column(name="CHILDREN", columnDefinition="DEC(2,0)")
	private Integer children;

	/** 工作年資 **/
	@Column(name="SENIORITY", columnDefinition="DEC(4,2)")
	private BigDecimal seniority;

	/** 職業 **/
	@Column(name="POS", length=4, columnDefinition="VARCHAR(4)")
	private String pos;

	/** 金額單位 **/
	@Column(name="AMTUNIT", columnDefinition="DEC(7,0)")
	private BigDecimal amtunit;

	/** 年薪 **/
	@Column(name="YPAY", columnDefinition="DEC(10,0)")
	private BigDecimal ypay;

	/** 年薪幣別 **/
	@Column(name="YPAY_SWFT", length=3, columnDefinition="CHAR(3)")
	private String ypay_swft;

	/** 年薪轉換匯率 **/
	@Column(name="YPAY_EX_RATE", columnDefinition="DEC(9,5)")
	private BigDecimal ypay_ex_rate;

	/** 其他收入 **/
	@Column(name="OMONEY", length=30, columnDefinition="VARCHAR(30)")
	private String omoney;

	/** 其他收入金額 **/
	@Column(name="OMONEY_AMT", columnDefinition="DEC(10,0)")
	private BigDecimal omoney_amt;

	/** 其他收入幣別 **/
	@Column(name="OMONEY_AMT_SWFT", length=3, columnDefinition="CHAR(3)")
	private String omoney_amt_swft;

	/** 其他收入轉換匯率 **/
	@Column(name="OMONEY_AMT_EX_RATE", columnDefinition="DEC(9,5)")
	private BigDecimal omoney_amt_ex_rate;

	/** 夫妻年收入(之前命名為 家庭所得) **/
	@Column(name="HINCOME", columnDefinition="DEC(10,0)")
	private BigDecimal hincome;

	/** 夫妻年收入幣別(之前命名為 家庭所得幣別) **/
	@Column(name="HINCOME_SWFT", length=3, columnDefinition="CHAR(3)")
	private String hincome_swft;

	/** 夫妻年收入轉換匯率(之前命名為 家庭所得轉換匯率) **/
	@Column(name="HINCOME_EX_RATE", columnDefinition="DEC(9,5)")
	private BigDecimal hincome_ex_rate;

	/** 本次新做案下不動產租金收入 **/
	@Column(name="RINCOME", columnDefinition="DEC(10,0)")
	private BigDecimal rincome;

	/** 本次新做案下不動產租金收入幣別 **/
	@Column(name="RINCOME_SWFT", length=3, columnDefinition="CHAR(3)")
	private String rincome_swft;

	/** 本次新做案下不動產租金收入轉換匯率 **/
	@Column(name="RINCOME_EX_RATE", columnDefinition="DEC(9,5)")
	private BigDecimal rincome_ex_rate;

	/** 個人負債比率 **/
	@Column(name="DRATE", columnDefinition="DEC(7,4)")
	private BigDecimal drate;

	/** 夫妻負債比率(之前命名為 家庭負債比率) **/
	@Column(name="YRATE", columnDefinition="DEC(7,4)")
	private BigDecimal yrate;

	/** 個人所得證明文件 **/
	@Column(name="CERTIFICATE", length=1, columnDefinition="CHAR(1)")
	private String certificate;

	/** 夫妻所得證明文件(之前命名為 家庭所得證明文件) **/
	@Column(name="HCERTIFICATE", length=1, columnDefinition="CHAR(1)")
	private String hcertificate;

	/** 使用信用卡循環信用或現金卡情形 **/
	@Column(name="DBCREDIT", length=1, columnDefinition="CHAR(1)")
	private String dbcredit;

	/** 是否於本行財富管理有定時定額扣款 **/
	@Column(name="ISPFUND", length=1, columnDefinition="CHAR(1)")
	private String ispfund;

	/** 與本行其他業務往來(財富管理業務如基金保險信用卡等) **/
	@Column(name="OBUSINESS", length=1, columnDefinition="CHAR(1)")
	private String obusiness;

	/** 與本行財富管理三個月平均總資產 **/
	@Column(name="INVMBAL", columnDefinition="DEC(10,0)")
	private BigDecimal invmbal;

	/** 與本行財富管理三個月平均總資產幣別 **/
	@Column(name="INVMBAL_SWFT", length=3, columnDefinition="CHAR(3)")
	private String invmbal_swft;

	/** 與本行財富管理三個月平均總資產轉換匯率 **/
	@Column(name="INVMBAL_EX_RATE", columnDefinition="DEC(9,5)")
	private BigDecimal invmbal_ex_rate;

	/** 與他行財富管理三個月平均總資產 **/
	@Column(name="INVOBAL", columnDefinition="DEC(10,0)")
	private BigDecimal invobal;

	/** 與他行財富管理三個月平均總資產幣別 **/
	@Column(name="INVOBAL_SWFT", length=3, columnDefinition="CHAR(3)")
	private String invobal_swft;

	/** 與他行財富管理三個月平均總資產轉換匯率 **/
	@Column(name="INVOBAL_EX_RATE", columnDefinition="DEC(9,5)")
	private BigDecimal invobal_ex_rate;

	/** 與金融機構存款往來情形(近六個月平均餘額) **/
	@Column(name="ODEP", columnDefinition="DEC(10,0)")
	private BigDecimal odep;

	/** 與金融機構存款往來情形(近六個月平均餘額)幣別 **/
	@Column(name="ODEP_SWFT", length=3, columnDefinition="CHAR(3)")
	private String odep_swft;

	/** 與金融機構存款往來情形(近六個月平均餘額)轉換匯率 **/
	@Column(name="ODEP_EX_RATE", columnDefinition="DEC(9,5)")
	private BigDecimal odep_ex_rate;

	/** 不動產狀況 **/
	@Column(name="CMSSTATUS", length=1, columnDefinition="CHAR(1)")
	private String cmsstatus;

	/** 上傳資料日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="DATA_SRC_DT", columnDefinition="DATE")
	private Date data_src_dt;

	/** 個人授信信用風險評等表 **/
	@Column(name="LOCAL_RISK_RATING", length=2, columnDefinition="CHAR(2)")
	private String local_risk_rating;
	
	/** 每月個人收入合計 **/
	@Column(name="MONTH_TOTAL_INCOME", columnDefinition="DEC(10,0)")
	private BigDecimal month_total_income;
	
	/** 每月個人收入幣別 **/
	@Column(name="MONTH_TOT_INCOME_SWFT", length=3, columnDefinition="CHAR(3)")
	private String month_tot_income_swft;
	
	/** 每月個人收入轉換匯率 **/
	@Column(name="MONTH_TOT_INCOME_EX_RATE", columnDefinition="DEC(9,5)")
	private BigDecimal month_tot_income_ex_rate;
	
	/** 每月個人支出合計 **/
	@Column(name="MONTH_TOTAL_EXPENSE", columnDefinition="DEC(10,0)")
	private BigDecimal month_total_expense;
	
	/** 每月個人支出幣別 **/
	@Column(name="MONTH_TOT_EXPENSE_SWFT", length=3, columnDefinition="CHAR(3)")
	private String month_tot_expense_swft;
	
	/** 每月個人支出轉換匯率 **/
	@Column(name="MONTH_TOT_EXPENSE_EX_RATE", columnDefinition="DEC(9,5)")
	private BigDecimal month_tot_expense_ex_rate;
	
	
	/** 取得分行別 **/
	public String getBr_cd() {
		return this.br_cd;
	}
	/** 設定分行別 **/
	public void setBr_cd(String value) {
		this.br_cd = value;
	}

	/** 取得NOTES文件編號 **/
	public String getNoteid() {
		return this.noteid;
	}
	/** 設定NOTES文件編號 **/
	public void setNoteid(String value) {
		this.noteid = value;
	}

	/** 
	 * 取得評等日期<p/>
	 * 最終評等日
	 */
	public Date getRating_date() {
		return this.rating_date;
	}
	/**
	 *  設定評等日期<p/>
	 *  最終評等日
	 **/
	public void setRating_date(Date value) {
		this.rating_date = value;
	}

	/** 取得評等文件編號 **/
	public String getRating_id() {
		return this.rating_id;
	}
	/** 設定評等文件編號 **/
	public void setRating_id(String value) {
		this.rating_id = value;
	}

	/** 取得客戶統一編號 **/
	public String getCustid() {
		return this.custid;
	}
	/** 設定客戶統一編號 **/
	public void setCustid(String value) {
		this.custid = value;
	}

	/** 取得重複序號 **/
	public String getDupno() {
		return this.dupno;
	}
	/** 設定重複序號 **/
	public void setDupno(String value) {
		this.dupno = value;
	}

	/** 取得主借款人統一編號 **/
	public String getCust_key() {
		return this.cust_key;
	}
	/** 設定主借款人統一編號 **/
	public void setCust_key(String value) {
		this.cust_key = value;
	}

	/** 
	 * 取得授信科目<p/>
	 * 3-4碼授信科目
	 */
	public String getLoan_code() {
		return this.loan_code;
	}
	/**
	 *  設定授信科目<p/>
	 *  3-4碼授信科目
	 **/
	public void setLoan_code(String value) {
		this.loan_code = value;
	}

	/** 
	 * 取得評等模型類別(c121m01a.mowType)
	 */
	public String getMowtype() {
		return this.mowtype;
	}
	/**
	 *  設定評等模型類別(c121m01a.mowType)
	 **/
	public void setMowtype(String value) {
		this.mowtype = value;
	}
	
	/** 
	 * 取得房貸/非房貸註記(l141m01c.modelType)
	 * N=非房貸、M=房貸
	 */
	public String getMowtype2() {
		return this.mowtype2;
	}
	/**
	 *  設定房貸/非房貸註記(l141m01c.modelType)
	 *  N=非房貸、M=房貸
	 **/
	public void setMowtype2(String value) {
		this.mowtype2 = value;
	}
	
	/** 
	 * 採用模型註記
	 * 國別碼(l120m01a.ratingFlag)
	 */
	public String getMowtype_country() {
		return this.mowtype_country;
	}
	/** 
	 * 採用模型註記
	 * 國別碼(l120m01a.ratingFlag)
	 */
	public void setMowtype_country(String value) {
		this.mowtype_country = value;
	}

	/** 取得模型版本-大版 **/
	public Integer getMowver1() {
		return this.mowver1;
	}
	/** 設定模型版本-大版 **/
	public void setMowver1(Integer value) {
		this.mowver1 = value;
	}

	/** 取得模型版本-小版 **/
	public Integer getMowver2() {
		return this.mowver2;
	}
	/** 設定模型版本-小版 **/
	public void setMowver2(Integer value) {
		this.mowver2 = value;
	}

	/** 
	 * 取得科目<p/>
	 * 8碼會計科目
	 */
	public String getSubjcode() {
		return this.subjcode;
	}
	/**
	 *  設定科目<p/>
	 *  8碼會計科目
	 **/
	public void setSubjcode(String value) {
		this.subjcode = value;
	}

	/** 取得相關身分 **/
	public String getLngeflag() {
		return this.lngeflag;
	}
	/** 設定相關身分 **/
	public void setLngeflag(String value) {
		this.lngeflag = value;
	}

	/** 取得文件狀態 **/
	public String getDocstatus() {
		return this.docstatus;
	}
	/** 設定文件狀態 **/
	public void setDocstatus(String value) {
		this.docstatus = value;
	}

	/** 取得出生年月日 **/
	public Date getDob() {
		return this.dob;
	}
	/** 設定出生年月日 **/
	public void setDob(Date value) {
		this.dob = value;
	}

	/** 取得學歷 **/
	public Integer getEducation() {
		return this.education;
	}
	/** 設定學歷 **/
	public void setEducation(Integer value) {
		this.education = value;
	}

	/** 取得婚姻 **/
	public Integer getMarriage() {
		return this.marriage;
	}
	/** 設定婚姻 **/
	public void setMarriage(Integer value) {
		this.marriage = value;
	}

	/** 取得扶養子女數 **/
	public Integer getChildren() {
		return this.children;
	}
	/** 設定扶養子女數 **/
	public void setChildren(Integer value) {
		this.children = value;
	}

	/** 取得工作年資 **/
	public BigDecimal getSeniority() {
		return this.seniority;
	}
	/** 設定工作年資 **/
	public void setSeniority(BigDecimal value) {
		this.seniority = value;
	}

	/** 取得職業 **/
	public String getPos() {
		return this.pos;
	}
	/** 設定職業 **/
	public void setPos(String value) {
		this.pos = value;
	}

	/** 取得金額單位 **/
	public BigDecimal getAmtunit() {
		return this.amtunit;
	}
	/** 設定金額單位 **/
	public void setAmtunit(BigDecimal value) {
		this.amtunit = value;
	}

	/** 取得年薪 **/
	public BigDecimal getYpay() {
		return this.ypay;
	}
	/** 設定年薪 **/
	public void setYpay(BigDecimal value) {
		this.ypay = value;
	}

	/** 取得年薪幣別 **/
	public String getYpay_swft() {
		return this.ypay_swft;
	}
	/** 設定年薪幣別 **/
	public void setYpay_swft(String value) {
		this.ypay_swft = value;
	}

	/** 取得年薪轉換匯率 **/
	public BigDecimal getYpay_ex_rate() {
		return this.ypay_ex_rate;
	}
	/** 設定年薪轉換匯率 **/
	public void setYpay_ex_rate(BigDecimal value) {
		this.ypay_ex_rate = value;
	}

	/** 取得其他收入 **/
	public String getOmoney() {
		return this.omoney;
	}
	/** 設定其他收入 **/
	public void setOmoney(String value) {
		this.omoney = value;
	}

	/** 取得其他收入金額 **/
	public BigDecimal getOmoney_amt() {
		return this.omoney_amt;
	}
	/** 設定其他收入金額 **/
	public void setOmoney_amt(BigDecimal value) {
		this.omoney_amt = value;
	}

	/** 取得其他收入幣別 **/
	public String getOmoney_amt_swft() {
		return this.omoney_amt_swft;
	}
	/** 設定其他收入幣別 **/
	public void setOmoney_amt_swft(String value) {
		this.omoney_amt_swft = value;
	}

	/** 取得其他收入轉換匯率 **/
	public BigDecimal getOmoney_amt_ex_rate() {
		return this.omoney_amt_ex_rate;
	}
	/** 設定其他收入轉換匯率 **/
	public void setOmoney_amt_ex_rate(BigDecimal value) {
		this.omoney_amt_ex_rate = value;
	}

	/** 取得夫妻年收入(之前命名為 家庭所得) **/
	public BigDecimal getHincome() {
		return this.hincome;
	}
	/** 設定夫妻年收入(之前命名為 家庭所得) **/
	public void setHincome(BigDecimal value) {
		this.hincome = value;
	}

	/** 取得夫妻年收入幣別(之前命名為 家庭所得幣別) **/
	public String getHincome_swft() {
		return this.hincome_swft;
	}
	/** 設定夫妻年收入幣別(之前命名為 家庭所得幣別) **/
	public void setHincome_swft(String value) {
		this.hincome_swft = value;
	}

	/** 取得夫妻年收入轉換匯率(之前命名為 家庭所得轉換匯率) **/
	public BigDecimal getHincome_ex_rate() {
		return this.hincome_ex_rate;
	}
	/** 設定夫妻年收入轉換匯率(之前命名為 家庭所得轉換匯率) **/
	public void setHincome_ex_rate(BigDecimal value) {
		this.hincome_ex_rate = value;
	}

	/** 取得本次新做案下不動產租金收入 **/
	public BigDecimal getRincome() {
		return this.rincome;
	}
	/** 設定本次新做案下不動產租金收入 **/
	public void setRincome(BigDecimal value) {
		this.rincome = value;
	}

	/** 取得本次新做案下不動產租金收入幣別 **/
	public String getRincome_swft() {
		return this.rincome_swft;
	}
	/** 設定本次新做案下不動產租金收入幣別 **/
	public void setRincome_swft(String value) {
		this.rincome_swft = value;
	}

	/** 取得本次新做案下不動產租金收入轉換匯率 **/
	public BigDecimal getRincome_ex_rate() {
		return this.rincome_ex_rate;
	}
	/** 設定本次新做案下不動產租金收入轉換匯率 **/
	public void setRincome_ex_rate(BigDecimal value) {
		this.rincome_ex_rate = value;
	}

	/** 取得個人負債比率 **/
	public BigDecimal getDrate() {
		return this.drate;
	}
	/** 設定個人負債比率 **/
	public void setDrate(BigDecimal value) {
		this.drate = value;
	}

	/** 取得夫妻負債比率(之前命名為 家庭負債比率) **/
	public BigDecimal getYrate() {
		return this.yrate;
	}
	/** 設定夫妻負債比率(之前命名為 家庭負債比率) **/
	public void setYrate(BigDecimal value) {
		this.yrate = value;
	}

	/** 取得個人所得證明文件 **/
	public String getCertificate() {
		return this.certificate;
	}
	/** 設定個人所得證明文件 **/
	public void setCertificate(String value) {
		this.certificate = value;
	}

	/** 取得夫妻所得證明文件(之前命名為 家庭所得證明文件) **/
	public String getHcertificate() {
		return this.hcertificate;
	}
	/** 設定夫妻所得證明文件(之前命名為 家庭所得證明文件) **/
	public void setHcertificate(String value) {
		this.hcertificate = value;
	}

	/** 取得使用信用卡循環信用或現金卡情形 **/
	public String getDbcredit() {
		return this.dbcredit;
	}
	/** 設定使用信用卡循環信用或現金卡情形 **/
	public void setDbcredit(String value) {
		this.dbcredit = value;
	}

	/** 取得是否於本行財富管理有定時定額扣款 **/
	public String getIspfund() {
		return this.ispfund;
	}
	/** 設定是否於本行財富管理有定時定額扣款 **/
	public void setIspfund(String value) {
		this.ispfund = value;
	}

	/** 取得與本行其他業務往來(財富管理業務如基金保險信用卡等) **/
	public String getObusiness() {
		return this.obusiness;
	}
	/** 設定與本行其他業務往來(財富管理業務如基金保險信用卡等) **/
	public void setObusiness(String value) {
		this.obusiness = value;
	}

	/** 取得與本行財富管理三個月平均總資產 **/
	public BigDecimal getInvmbal() {
		return this.invmbal;
	}
	/** 設定與本行財富管理三個月平均總資產 **/
	public void setInvmbal(BigDecimal value) {
		this.invmbal = value;
	}

	/** 取得與本行財富管理三個月平均總資產幣別 **/
	public String getInvmbal_swft() {
		return this.invmbal_swft;
	}
	/** 設定與本行財富管理三個月平均總資產幣別 **/
	public void setInvmbal_swft(String value) {
		this.invmbal_swft = value;
	}

	/** 取得與本行財富管理三個月平均總資產轉換匯率 **/
	public BigDecimal getInvmbal_ex_rate() {
		return this.invmbal_ex_rate;
	}
	/** 設定與本行財富管理三個月平均總資產轉換匯率 **/
	public void setInvmbal_ex_rate(BigDecimal value) {
		this.invmbal_ex_rate = value;
	}

	/** 取得與他行財富管理三個月平均總資產 **/
	public BigDecimal getInvobal() {
		return this.invobal;
	}
	/** 設定與他行財富管理三個月平均總資產 **/
	public void setInvobal(BigDecimal value) {
		this.invobal = value;
	}

	/** 取得與他行財富管理三個月平均總資產幣別 **/
	public String getInvobal_swft() {
		return this.invobal_swft;
	}
	/** 設定與他行財富管理三個月平均總資產幣別 **/
	public void setInvobal_swft(String value) {
		this.invobal_swft = value;
	}

	/** 取得與他行財富管理三個月平均總資產轉換匯率 **/
	public BigDecimal getInvobal_ex_rate() {
		return this.invobal_ex_rate;
	}
	/** 設定與他行財富管理三個月平均總資產轉換匯率 **/
	public void setInvobal_ex_rate(BigDecimal value) {
		this.invobal_ex_rate = value;
	}

	/** 取得與金融機構存款往來情形(近六個月平均餘額) **/
	public BigDecimal getOdep() {
		return this.odep;
	}
	/** 設定與金融機構存款往來情形(近六個月平均餘額) **/
	public void setOdep(BigDecimal value) {
		this.odep = value;
	}

	/** 取得與金融機構存款往來情形(近六個月平均餘額)幣別 **/
	public String getOdep_swft() {
		return this.odep_swft;
	}
	/** 設定與金融機構存款往來情形(近六個月平均餘額)幣別 **/
	public void setOdep_swft(String value) {
		this.odep_swft = value;
	}

	/** 取得與金融機構存款往來情形(近六個月平均餘額)轉換匯率 **/
	public BigDecimal getOdep_ex_rate() {
		return this.odep_ex_rate;
	}
	/** 設定與金融機構存款往來情形(近六個月平均餘額)轉換匯率 **/
	public void setOdep_ex_rate(BigDecimal value) {
		this.odep_ex_rate = value;
	}

	/** 取得不動產狀況 **/
	public String getCmsstatus() {
		return this.cmsstatus;
	}
	/** 設定不動產狀況 **/
	public void setCmsstatus(String value) {
		this.cmsstatus = value;
	}

	/** 取得上傳資料日期 **/
	public Date getData_src_dt() {
		return this.data_src_dt;
	}
	/** 設定上傳資料日期 **/
	public void setData_src_dt(Date value) {
		this.data_src_dt = value;
	}
	
	/** 取得個人授信信用風險評等表 **/
	public String getLocal_risk_rating() {
		return local_risk_rating;
	}
	/** 設定個人授信信用風險評等表 **/
	public void setLocal_risk_rating(String local_risk_rating) {
		this.local_risk_rating = local_risk_rating;
	}
	
	/** 取得每月個人收入合計 **/
	public BigDecimal getMonth_total_income() {
		return month_total_income;
	}
	/** 設定每月個人收入合計 **/
	public void setMonth_total_income(BigDecimal month_total_income) {
		this.month_total_income = month_total_income;
	}
	
	/** 取得每月個人收入幣別 **/	
	public String getMonth_tot_income_swft() {
		return month_tot_income_swft;
	}
	/** 設定每月個人收入幣別 **/
	public void setMonth_tot_income_swft(String month_tot_income_swft) {
		this.month_tot_income_swft = month_tot_income_swft;
	}

	/** 取得每月個人收入轉換匯率 **/
	public BigDecimal getMonth_tot_income_ex_rate() {
		return month_tot_income_ex_rate;
	}
	/** 設定每月個人收入轉換匯率 **/
	public void setMonth_tot_income_ex_rate(BigDecimal month_tot_income_ex_rate) {
		this.month_tot_income_ex_rate = month_tot_income_ex_rate;
	}
	
	/** 取得每月個人支出合計 **/
	public BigDecimal getMonth_total_expense() {
		return month_total_expense;
	}
	/** 設定每月個人支出合計 **/
	public void setMonth_total_expense(BigDecimal month_total_expense) {
		this.month_total_expense = month_total_expense;
	}
	
	/** 取得每月個人支出幣別 **/
	public String getMonth_tot_expense_swft() {
		return month_tot_expense_swft;
	}
	/** 設定每月個人支出幣別 **/
	public void setMonth_tot_expense_swft(String month_tot_expense_swft) {
		this.month_tot_expense_swft = month_tot_expense_swft;
	}
	
	/** 取得每月個人支出轉換匯率 **/
	public BigDecimal getMonth_tot_expense_ex_rate() {
		return month_tot_expense_ex_rate;
	}
	/** 設定每月個人支出轉換匯率 **/
	public void setMonth_tot_expense_ex_rate(BigDecimal month_tot_expense_ex_rate) {
		this.month_tot_expense_ex_rate = month_tot_expense_ex_rate;
	}
	
}
