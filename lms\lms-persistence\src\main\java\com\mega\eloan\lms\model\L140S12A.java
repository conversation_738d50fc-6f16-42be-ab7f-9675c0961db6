/* 
 * L140S12A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 其他續做條件追蹤分項 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L140S12A", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L140S12A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 
	 * 文件編號<p/>
	 * L120M01A.mainId
	 */
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 序號 **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="SEQNUM", columnDefinition="DECIMAL(3,0)")
	private Integer seqNum;

	/** 
	 * 額度序號<p/>
	 * L140M01A.cntrNo
	 */
	@Size(max=12)
	@Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)")
	private String cntrNo;

	/** 
	 * 簽報書文件編號<p/>
	 * L120M01A.mainId
	 */
	@Size(max=32)
	@Column(name="CASEMAINID", length=32, columnDefinition="CHAR(32)")
	private String caseMainId;
	
	/** 
	 * 類別<p/>
	 * 1.綠色授信註記<br/>
	 *  2.永續績效連結授信條件<br/>
	 *  3.其他ESG條件<br/>
	 *  9.其他
	 */
	@Size(max=20)
	@Column(name="ESGTYPE", length=20, columnDefinition="VARCHAR(20)")
	private String esgType;

	/** 
	 * ESG模板<p/>
	 * Ex: S1|S2|E1|G1  <br/>
	 *  以|隔開
	 */
	@Size(max=100)
	@Column(name="ESGMODEL", length=100, columnDefinition="VARCHAR(100)")
	private String esgModel;

	/** 
	 * 起始追蹤日<p/>
	 * 1.首次撥款<br/>
	 *  2.每次撥款<br/>
	 *  3.其他
	 */
	@Size(max=1)
	@Column(name="TRACECONDITION", length=1, columnDefinition="CHAR(1)")
	private String traceCondition;

	/** 
	 * 追蹤週期<p/>
	 * 1.一次<br/>
	 *  2.週期月<br/>
	 *  3.核准日後6個月內<br/>
	 *  4.核准日後6個月第一次，其後每12個月一次
	 */
	@Size(max=1)
	@Column(name="TRACEPROFILING", length=1, columnDefinition="CHAR(1)")
	private String traceProfiling;

	/** 
	 * 週期月<p/>
	 * 追蹤週期=2時才有
	 */
	@Digits(integer=2, fraction=0, groups = Check.class)
	@Column(name="TRACEPROFILINGMONTH", columnDefinition="DECIMAL(2,0)")
	private Integer traceProfilingMonth;

	/** 內容 **/
	@Size(max=1500)
	@Column(name="CONTENTTEXT", length=1000, columnDefinition="VARCHAR(1500)")
	private String contentText;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 
	 * 取得文件編號<p/>
	 * L140M01A.mainId
	 */
	public String getMainId() {
		return this.mainId;
	}
	/**
	 *  設定文件編號<p/>
	 *  L140M01A.mainId
	 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得序號 **/
	public Integer getSeqNum() {
		return this.seqNum;
	}
	/** 設定序號 **/
	public void setSeqNum(Integer value) {
		this.seqNum = value;
	}

	/** 
	 * 取得額度序號<p/>
	 * L140M01A.cntrNo
	 */
	public String getCntrNo() {
		return this.cntrNo;
	}
	/**
	 *  設定額度序號<p/>
	 *  L140M01A.cntrNo
	 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}
	
	/** 
	 * 取得簽報書文件編號<p/>
	 * L120M01A.mainId
	 */
	public String getCaseMainId() {
		return this.caseMainId;
	}
	/**
	 *  設定簽報書文件編號<p/>
	 *  L120M01A.mainId
	 **/
	public void setCaseMainId(String value) {
		this.caseMainId = value;
	}


	/** 
	 * 取得類別<p/>
	 * 11.綠色授信註記<br/>
	 *  12.永續績效連結授信條件<br/>
	 *  13.其他ESG條件<br/>
	 *  8.其他
	 */
	public String getEsgType() {
		return this.esgType;
	}
	/**
	 *  設定類別<p/>
	 *  1.綠色授信註記<br/>
	 *  2.永續績效連結授信條件<br/>
	 *  3.其他ESG條件<br/>
	 *  9.其他
	 **/
	public void setEsgType(String value) {
		this.esgType = value;
	}

	/** 
	 * 取得ESG模板<p/>
	 * Ex: S1|S2|E1|G1  <br/>
	 *  以|隔開
	 */
	public String getEsgModel() {
		return this.esgModel;
	}
	/**
	 *  設定ESG模板<p/>
	 *  Ex: S1|S2|E1|G1  <br/>
	 *  以|隔開
	 **/
	public void setEsgModel(String value) {
		this.esgModel = value;
	}

	/** 
	 * 取得起始追蹤日<p/>
	 * 1.首次撥款<br/>
	 *  2.每次撥款<br/>
	 *  3.其他
	 */
	public String getTraceCondition() {
		return this.traceCondition;
	}
	/**
	 *  設定起始追蹤日<p/>
	 *  1.首次撥款<br/>
	 *  2.每次撥款<br/>
	 *  3.其他
	 **/
	public void setTraceCondition(String value) {
		this.traceCondition = value;
	}

	/** 
	 * 取得追蹤週期<p/>
	 * 1.一次<br/>
	 *  2.週期月<br/>
	 *  3.核准日後6個月內<br/>
	 *  4.核准日後6個月第一次，其後每12個月一次
	 */
	public String getTraceProfiling() {
		return this.traceProfiling;
	}
	/**
	 *  設定追蹤週期<p/>
	 *  1.一次<br/>
	 *  2.週期月<br/>
	 *  3.核准日後6個月內<br/>
	 *  4.核准日後6個月第一次，其後每12個月一次
	 **/
	public void setTraceProfiling(String value) {
		this.traceProfiling = value;
	}

	/** 
	 * 取得週期月<p/>
	 * 追蹤週期=2時才有
	 */
	public Integer getTraceProfilingMonth() {
		return this.traceProfilingMonth;
	}
	/**
	 *  設定週期月<p/>
	 *  追蹤週期=2時才有
	 **/
	public void setTraceProfilingMonth(Integer value) {
		this.traceProfilingMonth = value;
	}

	/** 取得內容 **/
	public String getContentText() {
		return this.contentText;
	}
	/** 設定內容 **/
	public void setContentText(String value) {
		this.contentText = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
