---------------------------------------------------------
-- LMS.L140S02K 貸款額度評等表
---------------------------------------------------------
---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.L140S02K;
CREATE TABLE LMS.L140S02K (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)      not null,
	SEQ           DECIMAL(5,0)  not null,
	SCORE01       DECIMAL(3,0) ,
	SCORE02       DECIMAL(3,0) ,
	SCORE03       DECIMAL(3,0) ,
	SCORE04       DECIMAL(3,0) ,
	SCORE05       DECIMAL(3,0) ,
	SCORE06       DECIMAL(3,0) ,
	SCORE07       DECIMAL(3,0) ,
	LOANAMT       DECIMAL(15,0),
	DSCR          VARCHAR(384) ,
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_L140S02K PRIMARY KEY(OID)
) IN EL_DATA_4KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XL140S02K01;
CREATE UNIQUE INDEX LMS.XL140S02K01 ON LMS.L140S02K   (MAINID, SEQ);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.L140S02K IS '貸款額度評等表';
COMMENT ON LMS.L140S02K (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	SEQ           IS '序號', 
	SCORE01       IS '借戶從事本業或訓練經驗', 
	SCORE02       IS '營業場所自、租用', 
	SCORE03       IS '抵押貸款或信用貸款的保證成數', 
	SCORE04       IS '借戶或連保人資力', 
	SCORE05       IS '授信往來', 
	SCORE06       IS '借戶(含事業體)無擔保授信總餘額', 
	SCORE07       IS '借戶(含事業體)聯徵被查詢次數', 
	LOANAMT       IS '本案最高可額貸額度', 
	DSCR          IS '分行初審綜合意見', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
