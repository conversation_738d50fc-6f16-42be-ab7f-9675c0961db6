package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.C101S01VDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C101S01V;

/** 申請資料核對表檔 **/
@Repository
public class C101S01VDaoImpl extends LMSJpaDao<C101S01V, String>
	implements C101S01VDao {

	@Override
	public C101S01V findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C101S01V> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.setMaxResults(Integer.MAX_VALUE);
		List<C101S01V> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public List<C101S01V> findByMainIdandItemsName(String mainId,String CustID,String Dupno,String items_Name) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", CustID);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", Dupno);
		search.addSearchModeParameters(SearchMode.EQUALS, "items_Name", items_Name);
		search.setMaxResults(Integer.MAX_VALUE);
		List<C101S01V> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public List<C101S01V> findByMainIdandCustID(String mainId,String CustID,String Dupno) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", CustID);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", Dupno);
		search.setMaxResults(Integer.MAX_VALUE);
		List<C101S01V> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public List<C101S01V> findByUniqueKey(String mainId, String ownBrId,
			String custId, String dupNo) {
		ISearch search = createSearchTemplete();
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (ownBrId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
					ownBrId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			return createQuery(search).getResultList();
		}
		return null;
	}

	@Override
	public int deleteByOid(String oid) {
		Query query = entityManager.createNamedQuery("C101S01V.deleteOid");
		query.setParameter("OID", oid);
		return query.executeUpdate();
	}
}