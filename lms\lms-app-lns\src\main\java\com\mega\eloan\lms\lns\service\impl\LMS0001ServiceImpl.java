/* 
 * LMS0001ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lns.service.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.Page;

import com.mega.eloan.lms.dao.L000M01ADao;
import com.mega.eloan.lms.lns.service.LMS0001Service;
import com.mega.eloan.lms.model.L000M01A;

/**
 * <pre>
 * 近期已收
 * </pre>
 * 
 * @since 2012/1/13
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/13,REX,new
 *          </ul>
 */

@Service
public class LMS0001ServiceImpl implements LMS0001Service {
	@Resource
	L000M01ADao l000m01aDao;

	@Override
	public Page<L000M01A> findPage(ISearch search) {
		return l000m01aDao.findPage(search);
	}


}
