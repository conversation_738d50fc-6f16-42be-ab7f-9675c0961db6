<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:wicket="http://wicket.apache.org/">
<body>
	<wicket:panel>
		<script type="text/javascript" src="pagejs/las/LMS1905S01.js"></script>
		<!-- 			<div id="tab-01"> -->
			<fieldset>
				<legend>
					<b>基本資訊</b>
				</legend>
				<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
					<tbody>						
						<tr>
							<td width="20%" class="hd1">
								<wicket:message key="Lms_Auditsht.ownbrid">受檢單位</wicket:message>&nbsp;&nbsp;
							</td>
							<td width="30%" id="ownBrName" name="ownBrName" class="max"></td>
							<td width="20%" class="hd1">
								<wicket:message	key="Lms_Auditsht.checkBase">檢查基準日</wicket:message>&nbsp;&nbsp;
							</td>
							<td width="30%"  class="max" >
								<input type="text" class="date" id="checkBase" name="checkBase" />
							</td>
						</tr>
						
						<tr>
							<td class="hd1">
								<wicket:message key="Lms_Auditsht.status">文件狀態</wicket:message>&nbsp;&nbsp;
							</td>
							<td><b>
								<span class="color-red max" id="docStatusCN"name="docStatusCN" > </span>
							</b> <!--　經辦：翁小芳--></td>
							<td class="hd1">
								<span class="text-red">＊</span>
								<wicket:message key="Lms_Auditsht.checkDate">檢查日期</wicket:message>&nbsp;&nbsp;</td>
							<td>
								<input type="text" id="checkDate" name="checkDate" class="date required" />
							</td>
						</tr>
						<tr>
							<td class="hd1" valign="middle">
								<wicket:message key="Lms_Auditsht.checkMan">檢查人</wicket:message>&nbsp;&nbsp;</td>
							<td>
								<input type="text" id="checkMan" name="checkMan" />
							</td>
							<td class="hd1" valign="middle">
								<wicket:message	key="Lms_Auditsht.tNo">總編號</wicket:message>&nbsp;&nbsp;
							</td>
							<td>
								<input type="text" name="tNo" id="tNo" maxlength="13" />
							</td>
						</tr>
						
						<tr>
							<td class="hd1" id="td_leader">
								<wicket:message key="Lms_Auditsht.leader">領隊</wicket:message>&nbsp;&nbsp;
							</td>
							<td>
								<input type="text"  name="leader" id="leader" />
							</td>
							<td class="hd1">
								<wicket:message key="Lms_Auditsht.wpNo">W/P編號</wicket:message>&nbsp;&nbsp;
							</td>
							<td id="wpNo" name="wpNo" class="max"></td>
						</tr>
						
						<tr>
							<td class="hd1">
								<wicket:message key="Lms_Auditsht.mtDoc">有無授信審查小組會議紀錄</wicket:message>&nbsp;&nbsp;
							</td>
							<td>
								<input id="mtDoc" type="radio" name="mtDoc" value="Y" /><label for="mtDoc"><wicket:message key="have">有</wicket:message></label>
								<input id="mtDoc_2" type="radio" name="mtDoc" value="N" /><label for="mtDoc_2"><wicket:message key="nohave">無</wicket:message></label>

							</td>
						</tr>
					</tbody>
				</table>
			</fieldset>

			<p />
			<fieldset>
				<legend>
					<b>文件異動紀錄</b>
				</legend>
				<div class="funcContainer">
					<!-- 				<div wicket:id="_docLog"></div> -->
				</div>
				<table class="tb2" width="100%" border="0" cellspacing="0"
					cellpadding="0">
					<tbody>
						<tr>
							<td width="20%" class="hd1">
								<wicket:message key="Lms_Auditsht.creator">文件建立者</wicket:message>&nbsp;&nbsp;</td>
							<td width="30%">
								<span id="creator" name="creator" class="max" maxlength="6"></span>(<span id="createTime" name="createTime"></span>)
							</td>
							<td width="20%" class="hd1">
								<wicket:message key="Lms_Auditsht.updater">最後異動者</wicket:message>&nbsp;&nbsp;
							</td>
							<td width="30%">
								<span id="updater" name="updater" class="max" maxlength="6"></span>(<span id="updateTime" name="updateTime"></span>)
							</td>
						</tr>
						<!--
						<tr>
							<td class="hd1">
								<wicket:message key="Lms_Auditsht.sendfirst">分行首次送件</wicket:message>&nbsp;&nbsp;
							</td>
							<td>
								<span id="sendFirst" name="sendFirst" class="max" maxlength="6"></span>&nbsp;<span id="sendFirstTime" name="sendFirstTime" />
							</td>
							<td class="hd1"><wicket:message key="Lms_Auditsht.sendlast">分行最後送件</wicket:message>&nbsp;&nbsp;</td>
							<td><span id="sendLast" name="sendLast" class="max" maxlength="6"></span>&nbsp;<span id="sendLastTime" name="sendLastTime"   /></td>
						</tr>-->
						<tr>
							<td class="hd1">
								<wicket:message key="Lms_Auditsht.randomcode">報表亂碼</wicket:message>&nbsp;&nbsp;
							</td>
							<td id="randomCode" name="randomCode" class="max" maxlength="32">
								
							</td>
						</tr>
					</tbody>
				</table>
			</fieldset>

			<p />
	</wicket:panel>
</body>
</html>
