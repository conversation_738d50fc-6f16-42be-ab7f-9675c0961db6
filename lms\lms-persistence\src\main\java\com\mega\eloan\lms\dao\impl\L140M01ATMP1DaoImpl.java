package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.model.Page;

import com.mega.eloan.lms.dao.L140M01ATMP1Dao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01ATMP1;

/** 授信簽報書主檔 **/
@Repository
public class L140M01ATMP1DaoImpl extends LMSJpaDao<L140M01ATMP1, String>
		implements L140M01ATMP1Dao {

	@Override
	public List<L140M01ATMP1> findByUserId(String userId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "notesUp", userId);

		search.addOrderBy("caseDate", true);
		search.addOrderBy("ownBrId");
		search.addOrderBy("caseNo", true);
		search.addOrderBy("custId");
		search.addOrderBy("dupNo");

		search.setMaxResults(Integer.MAX_VALUE);

		List<L140M01ATMP1> list = createQuery(L140M01ATMP1.class, search)
				.getResultList();
		return list;
	}

	public int delByUserId(String userId) {
		Query query = entityManager
				.createNamedQuery("L140M01ATMP1.deleteByUserId");

		query.setParameter("NOTESUP", userId);
		return query.executeUpdate();
	}

	@Override
	public Page<L140M01ATMP1> findL140m01atmp1UserIdForSearch(ISearch search,
			String userId) {

		search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "notesUp", userId);

		search.setMaxResults(Integer.MAX_VALUE);

		search.addOrderBy("caseDate", true);
		search.addOrderBy("ownBrId");
		search.addOrderBy("caseNo", true);
		search.addOrderBy("custId");
		search.addOrderBy("cntrNo");

		return findPage(search);
	}

	@Override
	public int delByUid(String uid) {
		Query query = entityManager
				.createNamedQuery("L140M01ATMP1.deleteByUid");

		query.setParameter("UID", uid);
		return query.executeUpdate();
	}

	@Override
	public Page<L140M01ATMP1> findL140m01atmp1ByUidForSearch(ISearch search,
			String uid) {

		search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "uid", uid);

		search.setMaxResults(Integer.MAX_VALUE);

		search.addOrderBy("caseDate", true);
		search.addOrderBy("ownBrId");
		search.addOrderBy("caseNo", true);
		search.addOrderBy("custId");
		search.addOrderBy("cntrNo");

		return findPage(search);
	}

	@Override
	public List<L140M01ATMP1> findByUid(String uid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "uid", uid);

		search.addOrderBy("caseDate", true);
		search.addOrderBy("ownBrId");
		search.addOrderBy("caseNo", true);
		search.addOrderBy("custId");
		search.addOrderBy("dupNo");

		search.setMaxResults(Integer.MAX_VALUE);

		List<L140M01ATMP1> list = createQuery(L140M01ATMP1.class, search)
				.getResultList();
		return list;
	}

}