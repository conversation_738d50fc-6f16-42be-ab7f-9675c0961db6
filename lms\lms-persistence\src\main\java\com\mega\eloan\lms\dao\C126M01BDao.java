/* 
 * C126M01BDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C126M01B;

/** 額度明細表主檔 **/
public interface C126M01BDao extends IGenericDao<C126M01B> {

	C126M01B findByOid(String oid);
	
	List<C126M01B> findByMainId(String mainId);
	
	C126M01B findByL140m01aMainId(String l140m01a_mainId);
}