var initDfd = initDfd || $.Deferred();

$(function(){
	var tabForm = $("#tabForm");
	var btnPanel = $("#buttonPanel");
	var _handler = "lms2420m01formhandler";
	var regYYYYMM = /^\d{4}\-(0?[1-9]|1[0-2])$/;
	var regHHMM = /^([0-9]|0[0-9]|1[0-9]|2[0-3]):[0-5][0-9]$/;

	$.form.init({
		formHandler:_handler, 
		formAction:'query', 
		loadSuccess:function(json){
			if ($("#orderBox").length > 0){
		        $.ajax({
		            type: "POST",
		            handler: _handler,
		            async: false,//用「同步」的方式
		            data: {
		                formAction: "queryBranch"
		            }
		        }).done(
					function(obj){
		            	
		            	var col_cnt = 3;
		            	var elmArr = [];
		            	$.each(obj.itemOrder, function(idx, brNo) {
		            		var brName = obj.item[brNo];
		            		//依 itemOrder, 一個一個append, 把 clear 指為 false
		            		
		            		var tdcol = {};
		            		tdcol['_a'] = "<label style='letter-spacing:0px;cursor:pointer;font-weight:normal;'><input value='"+brNo+"' id='branchList' name='branchList' type='checkbox'>"+brNo +" "+ brName+"</label>";
		            		tdcol['_b'] = "<input id='branch"+brNo+"' type='text' name='branchDate' maxlength='7' size='7'>";
		            		elmArr.push(tdcol);            		
						});
		            	//===
		            	//補empty col
		            	var addcnt = (col_cnt - (elmArr.length % col_cnt)); 
		            	if(addcnt==col_cnt){
		            		addcnt = 0;
		            	}
		            	for(var i=0;i<addcnt;i++){
		            		var tdcol = {};
		            		tdcol['_a'] = "&nbsp;";
		            		tdcol['_b'] = "&nbsp;";
		            		elmArr.push(tdcol);  
		            	}
		            	
		            	var dyna = [];
		            	dyna.push("<table width='100%' border='1' cellspacing='0' cellpadding='0'>");
		            	dyna.push("<tr>");
		            	for(var i=0;i<col_cnt;i++){
		            		dyna.push("<td colspan='2' align='center'>分行資料年月(YYYY-MM)</td>");	
		            	}
		            	dyna.push("</tr>");
		            	dyna.push("<tr>");
		            	for(var i=0;i<elmArr.length;i++){
		            		dyna.push("<td>"+elmArr[i]['_a']+"</td>");
		            		dyna.push("<td>"+elmArr[i]['_b']+"</td>");
		            		if( (i+1) % col_cnt==0){
		            			dyna.push("</tr><tr>");		
		            		}
		            	}
		            	dyna.push("</tr>");
		            	dyna.push("</table>");
		            	$("#orderBox").append(dyna.join("\n"));
		            }
				);
		    }
			
			// 控制頁面 Read/Write
			if(!$("#buttonPanel").find("#btnSave").is("button")) {
				tabForm.lockDoc();				
			}			
			
			tabForm.injectData(json);
			//---
			$.each(json.exeParam, function(k, v) {
				$("input[name=branchList][value="+k+"]").attr("checked", "checked");
				$("#branch"+k).val(v);
	        });
			//---
			initDfd.resolve(json);	
	}});

	btnPanel.find("#btnSave").click(function(){
		if(!tabForm.valid()){
			return;
		}
		var sch_hh = $("#sch_hh").val();
		var sch_mm = $("#sch_mm").val();
		var sch_hh_mm = "";
		if( $.trim(sch_hh).length==2 && $.trim(sch_mm).length==2){
			sch_hh_mm = sch_hh+":"+sch_mm
			if (!sch_hh_mm.match(regHHMM)) {
	            return CommonAPI.showMessage(i18n.def["val.time"]);
	        }	
		}else{
			return CommonAPI.showMessage("時間長度為2碼");
		}		
		//---
		var exeParamArr = [];
        var owin = $("[name=branchList]:checked").map(function(){
            return DOMPurify.sanitize($(this).val());            
        }).toArray();        
        if (owin == 'undefined' || owin == null || owin == "") {
            return CommonAPI.showMessage(i18n.lms2420m01["lms2420m01.msg.chooseBank"]);
        }
        
        for (var o in owin) {
        	var desc = owin[o]+"分行";
            if (!$("#branch" + owin[o]).val().match(regYYYYMM)) {
                //val.date2=日期格式錯誤(YYYY-MM)
                return CommonAPI.showMessage(desc+i18n.def["val.date2"]);
            }
            if ($("#branch" + owin[o]).val() ? false : true) {
                return CommonAPI.showMessage(desc+i18n.lms2420m01["lms2420m01.msg.noDataDate"]);
            } else {
            	exeParamArr.push(owin[o]+"^"+ $("#branch" + owin[o]).val());
            }
        }
        if(exeParamArr.length==0){
        	return;
        }
        
        var opts = {};
        opts['exeParam'] = exeParamArr.join("|");
        opts['schTime'] = $("#sch_date").val()+" "+sch_hh_mm+":00";
		saveAction(opts).done(function(json){
			if(json.saveOkFlag){
				API.showMessage(i18n.def.saveSuccess);
			}
        });
	});	
	
	$("#btn_bringDate").click(function(){
		var defStr = $("#def_yyyyMM").val();
		if (!defStr.match(regYYYYMM)) {
            //val.date2=日期格式錯誤(YYYY-MM)
            return CommonAPI.showMessage(i18n.lms2420m01["label.def_yyyyMM"]+ +i18n.def["val.date2"]);
        }
		$("input[name=branchDate]").val(defStr);
	});	
	
	var saveAction = function(opts){		
		if(tabForm.valid()){			
			return $.ajax({
                type: "POST",
                handler: _handler,
                data:$.extend( {
                	formAction: "saveMain",
                    mainOid: responseJSON.mainOid
                    }, 
                    tabForm.serializeData(),
                    ( opts||{} )
                )
            }).done(
				function(json){
                	tabForm.injectData(json);
                	//更新 opener 的 Grid
                    CommonAPI.triggerOpener("gridview", "reloadGrid");
                }
			);
		}else{
			return $.Deferred();
		}
	}
	
});
