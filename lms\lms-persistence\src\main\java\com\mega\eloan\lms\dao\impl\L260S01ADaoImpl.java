/* 
 * L260S01ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L260S01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L260S01A;

/** 貸後管理理財商品檔 **/
@Repository
public class L260S01ADaoImpl extends LMSJpaDao<L260S01A, String>
	implements L260S01ADao {

	@Override
	public L260S01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}
	
	@Override
	public L260S01A findByUniqueKey(String mainId, String custId, String dupNo, String proType, String bankProCode, String accNo){
		ISearch search = createSearchTemplete();
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (proType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "proType", proType);
		if (bankProCode != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "bankProCode", bankProCode);
		if (accNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "accNo", accNo);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<L260S01A> findByMainId(String mainId, boolean notIncDel) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if(notIncDel){
			search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		}
		Map<String, Boolean> map = new LinkedHashMap<String, Boolean>();
		map.put("lstBuyDt", false);
		map.put("accNo", false);
		map.put("oid", false);
		search.setOrderBy(map);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L260S01A> list = createQuery(search).getResultList();
		return list;
	}
}