package com.mega.eloan.lms.lms.report.impl;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.inet.report.ReportException;
import com.lowagie.text.Document;
import com.lowagie.text.Image;
import com.lowagie.text.PageSize;
import com.lowagie.text.pdf.PdfWriter;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.dao.DocFileDao;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.dao.L161S01DDao;
import com.mega.eloan.lms.lms.report.LMS1015R00RptService;
import com.mega.eloan.lms.lms.report.LMS1015R01RptService;
import com.mega.eloan.lms.lms.report.LMS1015RptService;
import com.mega.eloan.lms.model.L161S01D;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.PdfTools;
import tw.com.jcs.common.report.ReportGenerator;

@Service("lms1016rptservice")
public class LMS1016RptServiceImpl implements FileDownloadService, LMS1015RptService {
	
	protected static final Logger LOGGER = LoggerFactory
			.getLogger(LMS1016RptServiceImpl.class);
	@Resource
	CLSService clsService;
	
	@Resource
	LMS1015R00RptService lms1015R00RptService;
	
	@Resource
	LMS1015R01RptService lms1015R01RptService;
	
	@Resource
	DocFileDao docFileDao;

	@Resource
	DocFileService docFileService;
	
	@Resource
	L161S01DDao l161s01dDao;
	
	private Properties prop = null;
	
	@Override
	public byte[] getContent(PageParameters params) throws CapException,
			FileNotFoundException, ReportException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
			baos = (ByteArrayOutputStream) this.generateReport(params);
			return baos.toByteArray();
		} finally {
			if (baos != null) {
				baos.close();
			}
		}
	}

	private OutputStream generateReport(PageParameters params) throws IOException, Exception {
		
		List<InputStream> pdfNameList = new LinkedList<InputStream>();
		OutputStream outputStream = null;
		InputStream inputStream = null;
		int subLine = 8;
		Locale locale = null;
		Properties propEloanPage = null;
		List<Boolean> vaPrintResults = new ArrayList<Boolean>();
		
		try {
			propEloanPage = MessageBundleScriptCreator
					.getComponentResource(AbstractEloanPage.class);
			// zh_TW: 正體中文
			// zh_CN: 簡體中文
			// en_US: 英文
			locale = LMSUtil.getLocale();
			//prop = MessageBundleScriptCreator.getComponentResource(LMS1016RptServiceImpl.class);
			String mainId = Util.nullToSpace(params
					.getString(EloanConstants.MAIN_ID));
			//合併 RPA附檔
			ArrayList<String> filelist = new ArrayList<String>();
			List<String> oids = new ArrayList<String>();
			List<L161S01D> l161s01ds = l161s01dDao.findByMainId(mainId);
			for (L161S01D l161s01d : l161s01ds) {
				if (!CapString.isEmpty(l161s01d.getDocfileoid())) {
					oids.add(l161s01d.getDocfileoid());
				}
			}
			
			ISearch search = docFileDao.createSearchTemplete();
			search.addSearchModeParameters(SearchMode.IN,
					EloanConstants.OID, oids.toArray(new String[0]));
//			search.addSearchModeParameters(SearchMode.IN, "fieldId",
//					new String[] { "rpa_lms" });
//			search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime" , null);
			search.addOrderBy("uploadSeq");
			List<DocFile> docFiles = docFileDao.find(search);
			byte[] bdata = null;
			
			for (DocFile docFile : docFiles) {
				File checkFile = new File(docFileService.getFilePath(docFile));
				if (!checkFile.exists()) {
					continue;
				}
				if (docFile.getSrcFileName().indexOf("jpg") > -1) {
					File img = docFileService.getRealFile(docFile);
					File tempPdf = new File(img.getParent() + "\\temp.pdf");
					Document document = new Document(PageSize.A4.rotate(), 10,
							10, 10, 10);
					PdfWriter.getInstance(document, new FileOutputStream(
							tempPdf));
					document.open();
					Image image = Image.getInstance(docFileService
							.getFilePath(docFile));
					// 壓縮圖片
					float heigth = image.getHeight();
					float width = image.getWidth();
					int percent=getPercent2(heigth, width);
					//设置图片居中显示
					image.setAlignment(Image.MIDDLE);
					//按百分比显示图片的比例
					image.scalePercent(percent);//表示是原来图像的比例
					
					document.add(image);
					document.close();

					bdata = FileUtils.readFileToByteArray(tempPdf);
					inputStream = new ByteArrayInputStream(bdata);
					pdfNameList.add(inputStream);
					filelist.add(docFile.getRealFileName());
					vaPrintResults.add(true);
					tempPdf.delete();
				} else if (docFile.getSrcFileName().indexOf("pdf") > -1) {
					bdata = FileUtils.readFileToByteArray(docFileService
							.getRealFile(docFile));
					inputStream = new ByteArrayInputStream(bdata);
					pdfNameList.add(inputStream);
					filelist.add(docFile.getRealFileName());
					vaPrintResults.add(true);
				}
			}
			
			// 合併利害關係人 HTML
			outputStream = this.genL161S01D_05(mainId, locale);
			if (outputStream != null) {
				inputStream = new ByteArrayInputStream(
						((ByteArrayOutputStream) outputStream)
								.toByteArray());
				// 2022.08.04 把inputStream搬進HTML if判斷裡，有HTML才要將inputStream併進PDF
				// 不然會出現[ERROR] PDF header signature not found. 的Exception
				if (inputStream != null) {
					pdfNameList.add(inputStream);
				}
			}
			
			if (pdfNameList.size() == 0) {
				throw new Exception("無查詢資料");
			}
			
			LOGGER.info("mergeReWritePagePdf=====>start");
			String savepath = null;
			if (pdfNameList.size() > 0) {
				outputStream = new ByteArrayOutputStream();
				PdfTools.mergeReWritePagePdf(pdfNameList, outputStream,
						propEloanPage.getProperty("PaginationText"), true,
						locale, subLine, vaPrintResults);
				// savepath = PdfTools.mergePdfFilesFiles(filelist, null);

				pdfNameList.clear();
			}
			LOGGER.info("mergeReWritePagePdf=====>final");
		} finally {

		}
		return outputStream;
	}
	
	/**
	 * 產生利害關係人 PDF
	 * @param mainId
	 * @param locale
	 * @return
	 */
	private OutputStream genL161S01D_05(String mainId, Locale locale) 
				throws FileNotFoundException, IOException, Exception {
		LOGGER.info("genL161S01D_05=====>start");
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();
		ReportGenerator generator = new ReportGenerator();
		OutputStream outputStream = null;
		String branchName = null;
		try {
			List<L161S01D> l161s01ds = l161s01dDao.findByIndex01(mainId,
					UtilConstants.RPA.TYPE.銀行法及金控法利害關係人查詢, 
					UtilConstants.RPA.STATUS.查詢完成);
			
			if (l161s01ds.size() == 0) {
				return null;
			}
			rptVariableMap.put("html", l161s01ds.get(0).getData());

			generator.setLang(locale);
			generator.setVariableData(rptVariableMap);
			generator.setRowsData(titleRows);
			generator.setReportFile("report/rpt/LMS1016HTML_"
					+ locale.toString() + ".rpt");
			outputStream = generator.generateReport();
			LOGGER.info("genL161S01D_05=====>finish");
		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
			if (titleRows != null) {
				titleRows.clear();
			}
		}

		return outputStream;
	}

	public int getPercent2(float h, float w) {
		int p = 0;
		float p2 = 0.0f;
		p2 = 700 / w * 100; //700寬度
		p = Math.round(p2);
		return p;
	}
}
