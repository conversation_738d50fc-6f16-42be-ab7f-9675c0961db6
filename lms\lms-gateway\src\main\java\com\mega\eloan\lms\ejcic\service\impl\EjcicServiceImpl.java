/* 
 * EjcicServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.ejcic.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;
import java.util.TreeSet;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.lms.ejcic.service.EjcicService;

/**
 * <pre>
 * 聯徵查詢ServerImpl
 * </pre>
 * 
 * @since 2012/10/23
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/10/23,Fantasy,new
 *          <li>2013/07/09,Fantasy,getBAM087Data
 *          MIS.BAM087.getData->MIS.BAM087.getData2
 *          </ul>
 */
@Service
public class EjcicServiceImpl extends AbstractEjcicJdbc implements EjcicService {

	public static final String DateFormat = "yyyy-MM-dd";

	public static final char[] DelayPayLoanChars = { 'A', 'B', '1', '2', '3',
			'4', '5', '6' };

	public static final char[] CashCardChars = { 'X' };

	/**
	 * 取得月份
	 * 
	 * @param date
	 * @param month
	 * @return
	 */
	private String getBeforeMonth(Date date, int month) {
		return getBeforeMonth(date, month, false);
	}

	/**
	 * 取得月份
	 * 
	 * @param value
	 * @param
	 * @return
	 */
	private String getBeforeMonth(Date date, int month, boolean lastDate) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.MONTH, -month);
		if (lastDate) {
			calendar.set(Calendar.DAY_OF_MONTH,
					calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
		} else {
			calendar.set(Calendar.DAY_OF_MONTH, 1);
		}
		return Util.trim(TWNDate.toTW(calendar.getTime())).replaceAll("/", "");
	}

	/**
	 * 取得月份
	 * 
	 * @param value
	 * @param
	 * @return
	 */
	private String getBeforeDay(Date date, int day) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.DATE, -day);
		return Util.trim(TWNDate.toTW(calendar.getTime())).replaceAll("/", "");
	}

	@Override
	public String get_cls_PRODID(String id) {
		String PRODID_P7 = "P7";
		String PRODID_P9 = "P9";
		Map<String, Object> map_P7 = this.getJdbc().queryForMap(
				"LOGFILE.getClsRecord", new String[] { id, PRODID_P7 });

		Map<String, Object> map_P9 = this.getJdbc().queryForMap(
				"LOGFILE.getClsRecord", new String[] { id, PRODID_P9 });
		
		Map<String, Object> map_P7P9 = this.getJdbc().queryForMap(
				"MISDATA.getClsRecord", new String[] { id });

		String p7_date = Util.trim(MapUtils.getString(map_P7, "QDATE"));
		String p9_date = Util.trim(MapUtils.getString(map_P9, "QDATE"));
		String p7_clittime = Util.trim(MapUtils.getString(map_P7, "CLITTIME"));
		String p9_clittime = Util.trim(MapUtils.getString(map_P9, "CLITTIME"));
		String p7p9 = Util.trim(MapUtils.getString(map_P7P9, "PRODID"));

		if (Util.isEmpty(p7_date)) {
			if (Util.isEmpty(p9_date)) {
				// 兩者都無，default 用 P7
			} else {
				return PRODID_P9;
			}
		} else {
			// P7 有資料
			if (Util.isEmpty(p9_date)) {
				// default 用 P7
			} else {
				// 互比
				if (Util.equals(p7_date, p9_date)) {
					// QDATE同一天
					return p7_clittime.compareTo(p9_clittime) > 0 ? PRODID_P7
							: PRODID_P9;
				} else {
					return p7_date.compareTo(p9_date) > 0 ? PRODID_P7
							: PRODID_P9;
				}
			}
		}
		if(Util.isNotEmpty(p7p9)){
			return p7p9;
		}

		return PRODID_P7;
	}

	@Override
	public List<Map<String, Object>> get_mis_datadate_records(String id,
			String prodId) {
		return this.getJdbc().queryForList("MIS.DATADATE.getData",
				new String[] { id, prodId });
	}

	@Override
	public Map<String, Object> getDate(String id, String prodId) {
		Map<String, Object> result = null;
		List<Map<String, Object>> list = this.getJdbc().queryForList(
				"MIS.DATADATE.getData", new String[] { id, prodId });
		if (list != null && !list.isEmpty()) {
			result = list.get(0);
			if (result != null) {
				String QDATE = Util.trim(result.get("QDATE"));
				if (Util.isNotEmpty(QDATE)) {
					Date d = Util.parseDate(QDATE);
					result.put("QDATE2AD", Util.toAD(Util.parseDate(d)));
					// 檢查查詢當月有無資料
					String lowDate = getBeforeMonth(d, 0); // 應該會傳回 當月1日
					String highDate = getBeforeMonth(d, 0, true); // 應該會傳回
																	// 當月的月底日
					List<Map<String, Object>> krm40List = _getKRM040Data(id,
							prodId, lowDate, highDate);
					int m = (krm40List == null || krm40List.isEmpty() ? 1 : 0);
					result.put("t1", getBeforeMonth(d, 1 + m));
					result.put("t6", getBeforeMonth(d, 6));
					result.put("t12", getBeforeMonth(d, 11 + m));
					result.put("highDate", getBeforeMonth(d, 0 + m, true));
					result.put("tGetQdate", getBeforeDay(d, 90)); // 例如N06因子,需要排除近三個月(90天)本行(銀行代碼017)之查詢記錄
				}
				String DATADATE = Util.trim(result.get("DATADATE"));
				if (Util.isNotEmpty(DATADATE)) {
					result.put("DATADATE2AD",
							Util.toAD(Util.parseDate(DATADATE)));
				}
			}
		}
		return result;
	}

	// @Override
	// public String getDataDate(String id) {
	// String result = null;
	// Map<String, Object> map = this.getJdbc().queryForMap(
	// "MIS.DATADATE.getDataDate", new String[] { id });
	// if (map != null){
	// result = Util.trim(map.get("datadate"));
	// }
	// return result;
	// }

	@Override
	public String getAAS003QDate(String id, String prodId) {
		String result = null;
		Map<String, Object> map = this.getJdbc().queryForMap(
				"MIS.AAS003.getQDate", new String[] { id, prodId });
		if (map != null) {
			result = Util.trim(map.get("QDATE"));
		}
		return result;
	}

	@Override
	public Map<String, Object> getD07_G_V_1_3(String id, String prodId,
			String qDate) {
		return this.getJdbc().queryForMap("MIS.BAM095.D07_G_V_1_3",
				new String[] { id, qDate, prodId });
	}

	@Override
	public Map<String, Object> getD07_G_V_2_0(String id, String prodId,
			String qDate) {
		return this.getJdbc().queryForMap("MIS.BAM095.D07_G_V_2_0",
				new String[] { id, qDate, prodId, prodId });
	}

	// @Override
	// public Map<String, Object> getAAS003Date(String id) {
	// return this.getJdbc().queryForMap("MIS.AAS003.getDate",
	// new String[] { id });
	// }

	private List<Map<String, Object>> _getKRM040Data(String id, String prodId,
			String lowDate, String highDate) {
		return this.getJdbc().queryForList("MIS.KRM040.getData",
				new String[] { id, prodId, lowDate, highDate });
	}

	@Override
	public Map<String, Object> getKRM040AvgRev_G_V_1_3(String id,
			String prodId, String qDate, String lowDate, String highDate) {
		return this.getJdbc().queryForMap("MIS.KRM040.getAvgRev_G_V_1_3",
				new String[] { id, prodId, qDate, lowDate, highDate });
		// return this.getJdbc().queryForMap("MIS.KRM040.getAvgRev2",
		// new String[] { id, lowDate, highDate });
	}

	@Override
	public List<Map<String, Object>> getN06_G_V_1_3(String id, String prodId,
			String qDate, String tGetQdate, String lowDate, String highDate) {
		return this.getJdbc().queryForList("MIS.STM022.getBankData2",
				new String[] { id, qDate, prodId, tGetQdate, tGetQdate });
		// return this.getJdbc().queryForList(
		// "MIS.STM022.getBankData",
		// new String[] { id, qDate, tGetQdate, tGetQdate, lowDate,
		// highDate });
	}

	@Override
	public Map<String, Object> getN06_Q_V_3_0(String id, String prodId,
			String qDate) {
		return this.getJdbc().queryForMap("MIS.STM022.N06_Q_V_3_0",
				new String[] { id, qDate, prodId, prodId });
	}

	@Override
	public List<Map<String, Object>> getSTM022_N18_data(String id,
			String prodId, String qDate) {
		return this.getJdbc().queryForListWithMax("MIS.STM022.get_N18_data",
				new String[] { id, qDate, prodId });
	}

	@Override
	public Map<String, Object> getN18_INQ12_BY30D(String id, String prodId,
			String qDate) {
		List<Map<String, Object>> data_list = getSTM022_N18_data(id, prodId,
				qDate);
		if (data_list.size() == 0) {
			// 當原始STM022(未區分inq_purpose_1)筆數=0筆時, 應回傳 0 或 Null？ Ans:Null
			return null;
		} else {
			// 當筆數>0筆時, inq_purpose_1 都不是 ('1','3','5','7'), 應回傳 0 或 Null？ Ans:0
			int un_match_cnt = 0;
			List<String> match_inq_list = new ArrayList<String>();
			for (Map<String, Object> row : data_list) {
				String INQ_PURPOSE_1 = Util.trim(MapUtils.getString(row,
						"INQ_PURPOSE_1"));
				String QUERY_DATE = Util.trim(MapUtils.getString(row,
						"QUERY_DATE"));
				// ========
				if (Util.equals(INQ_PURPOSE_1, "1")
						|| Util.equals(INQ_PURPOSE_1, "3")
						|| Util.equals(INQ_PURPOSE_1, "5")
						|| Util.equals(INQ_PURPOSE_1, "7")) {
					match_inq_list.add(QUERY_DATE);
				} else {
					un_match_cnt++;
				}
			}
			// =================
			Integer n18 = null;
			if (match_inq_list.size() == 0) {
				n18 = 0;
			} else {
				n18 = _convert_N18_INQ12_BY30D(match_inq_list);
			}
			Map<String, Object> r = new HashMap<String, Object>();
			r.put("N18", n18);
			return r;
		}
	}
	
	@Override
	public Map<String, Object> getN22_INQ12_BY30D(String id, String prodId,
			String qDate) {
		//撈STM022資料，可使用N18的SQL，因此不重寫
		List<Map<String, Object>> data_list = getSTM022_N18_data(id, prodId,
				qDate);
		if (data_list.size() == 0) {
			// 當原始STM022(未區分inq_purpose_1)筆數=0筆時, 應回傳 0 或 Null？ Ans:Null
			return null;
		} else {
			// 當筆數>0筆時, inq_purpose_1 都不是 ('1','3','5','7'), 應回傳 0 或 Null？ Ans:0
			int un_match_cnt = 0;
			List<String> match_inq_list = new ArrayList<String>();
			for (Map<String, Object> row : data_list) {
				String INQ_PURPOSE_1 = Util.trim(MapUtils.getString(row,
						"INQ_PURPOSE_1"));
				String QUERY_DATE = Util.trim(MapUtils.getString(row,
						"QUERY_DATE"));
				String QDATE = Util.trim(MapUtils.getString(row,
						"QDATE"));
				// ========
				//先把qdate格式=106/06/22(民國年/月/日) 轉 yyyy-MM-DD格式
				int QDATE_yyyy = 1911 + Integer.parseInt(StringUtils.substring(QDATE, 0, 3));
				String QDATE_MM = StringUtils.substring(QDATE, 4, 6);
				String QDATE_DD = StringUtils.substring(QDATE, 7, 9);
				String FORM_QDATE = Integer.toString(QDATE_yyyy)+"-"+QDATE_MM+"-"+"01";
				
				// QUERY_DATE格式=1060622(民國年月日) 轉  yyyy-MM-DD格式
				int QUERY_DATE_yyyy = 1911 + Integer.parseInt(StringUtils.substring(QUERY_DATE, 0, 3));
				String QUERY_DATE_MM = StringUtils.substring(QUERY_DATE, 3, 5);
				String QUERY_DATE_DD = StringUtils.substring(QUERY_DATE, 5, 7);
				String FORM_QUERY_DATE = Integer.toString(QUERY_DATE_yyyy)+"-"+QUERY_DATE_MM+"-"+"01";
				
				int month = CapDate.calculateMonths(FORM_QDATE, FORM_QUERY_DATE, "yyyy-MM-dd", "yyyy-MM-dd");
				
				String BANK_CODE = Util.trim(MapUtils.getString(row,"BANK_CODE"));
				String Bank = StringUtils.substring(BANK_CODE, 0, 3);
				
				if(Util.equals(Bank, "017") && month <= 3){
					//排除3個月內本行(017)資料
				}else{
					if (Util.equals(INQ_PURPOSE_1, "1")
							|| Util.equals(INQ_PURPOSE_1, "3")
							|| Util.equals(INQ_PURPOSE_1, "5")
							|| Util.equals(INQ_PURPOSE_1, "7")) {
						match_inq_list.add(QUERY_DATE);
					} else {
						un_match_cnt++;
					}
				}
			}
			// =================
			Integer n22 = null;
			if (match_inq_list.size() == 0) {
				n22 = 0;
			} else {
				n22 = _convert_N18_INQ12_BY30D(match_inq_list); //跟N18算法一樣，套用即可
			}
			Map<String, Object> r = new HashMap<String, Object>();
			r.put("N22", n22);
			return r;
		}
	}

	private Integer _convert_N18_INQ12_BY30D(List<String> list) {
		// ==================
		// QUERY_DATE格式=1060622(民國年月日)
		Set<String> workingSet = new HashSet<String>();
		for (String elm : list) {
			int yyyy = 1911 + Integer
					.parseInt(StringUtils.substring(elm, 0, 3));
			String mm = StringUtils.substring(elm, 3, 5);
			String dd = StringUtils.substring(elm, 5, 7);
			workingSet.add(String.valueOf(yyyy + "-" + mm + "-" + dd));
		}

		int size = list.size();
		Map<String, Set<String>> group = new LinkedHashMap<String, Set<String>>();
		for (int cnt_in_loop = 0; cnt_in_loop < size; cnt_in_loop++) {
			String base = _max_string_in_collection(workingSet);
			TreeSet<String> ts = _get_date_by_passCnt(base, 30);
			if (ts.size() > 0) {
				group.put(ts.first() + "~" + ts.last(),
						_elm_join(workingSet, ts));
				// =====
				workingSet = _elm_onlyLeft(workingSet, ts);
			}

			if (workingSet.size() == 0) {
				break;
			}
		}
		// ==================
		return group.size();
	}

	private String _max_string_in_collection(Collection<String> coll) {
		return new TreeSet<String>(coll).last();
	}

	private Set<String> _elm_join(Collection<String> a, Collection<String> b) {
		Set<String> r = new HashSet<String>();
		for (String elm : a) {
			if (b.contains(elm)) {
				r.add(elm);
			}
		}
		return r;
	}

	private Set<String> _elm_onlyLeft(Collection<String> a, Collection<String> b) {
		Set<String> r = new HashSet<String>();
		for (String elm : a) {
			if (b.contains(elm)) {
				// both
			} else {
				r.add(elm);
			}
		}
		return r;
	}

	private TreeSet<String> _get_date_by_passCnt(String base, int cnt) {
		TreeSet<String> r = new TreeSet<String>();
		r.add(base);
		Date baseDate = CapDate.parseDate(base);
		if (baseDate != null) {
			for (int i = 0; i < cnt; i++) {
				r.add(TWNDate.toAD(CapDate.shiftDays(baseDate, -1 * i)));
			}
		}
		return r;
	}

	@Override
	public List<Map<String, Object>> getSTM022Data_isQdata14(String id,
			String prodId, String qDate) {
		return this.getJdbc().queryForList("MIS.STM022.getData",
				new String[] { id, prodId, qDate });
	}

	@Override
	public Map<String, Object> getR01_G_1_3(String id, String prodId,
			String qDate) {
		// return this.getJdbc().queryForMap("MIS.KRM040.getRevolRate",
		// new String[] { id, qDate });
		return this.getJdbc().queryForMap("MIS.KRM040.getRevolRate2_G_1_3",
				new String[] { id, qDate, prodId });
	}

	@Override
	public Map<String, Object> getR01_Q_3_0(String id, String prodId,
			String qDate) {
		return this.getJdbc().queryForMap("MIS.KRM040.R01_Q_V_3_0",
				new String[] { id, prodId, qDate });
	}

	@Override
	public Map<String, Object> getKRM040RevolRatio_G_V_1_3(String id,
			String prodId, String qDate) {
		return this.getJdbc().queryForMap("MIS.KRM040.getRevolRatio2_G_V_1_3",
				new String[] { id, qDate, prodId });

		// String BILL_DATE = null;
		// if (Util.isNotEmpty(qDate)) {
		// String temp = qDate.replace("/", "");
		// if (temp.length() >= 5)
		// BILL_DATE = temp.substring(0, 5);
		// }
		// return this.getJdbc().queryForMap("MIS.KRM040.getRevolRatio",
		// new String[] { id, BILL_DATE });
	}

	@Override
	public Map<String, Object> getKRM040getRevolBalByDebtRate(String id,
			String prodId, String qDate) {
		return this.getJdbc().queryForMap("MIS.KRM040.getRevolBalByDebtRate",
				new String[] { id, qDate, prodId });
	}

	// @Override
	// public Map<String, Object> getKRM040SixPcodeAtimes(String id,
	// String lowDate, String highDate) {
	// return this.getJdbc().queryForMap("MIS.KRM040.getSix_PcodeAtimes2",
	// new String[] { id });
	// return this.getJdbc().queryForMap("MIS.KRM040.getSix_PcodeAtimes",
	// new String[] { id, lowDate, highDate });

	// }

	// @Override
	// public Map<String, Object> getKRM040NotPcodeAtimes(String id,
	// String lowDate, String highDate) {
	// return this.getJdbc().queryForMap("MIS.KRM040.getNot_PcodeAtimes",
	// new String[] { id, lowDate, highDate });
	// }

	// @Override
	// public Map<String, Object> getKRM040PcodeAtimes(String id, String
	// lowDate,
	// String highDate) {
	// return this.getJdbc().queryForMap("MIS.KRM040.getPcodeAtimes",
	// new String[] { id, lowDate, highDate });
	// }

	@Override
	public Map<String, Object> getP25_V_1_3(String id, String prodId,
			String qDate) {
		return this.getJdbc().queryForMap("MIS.KRM040.getP25_G_V_1_3",
				new String[] { id, qDate, prodId, id, qDate, prodId });
	}

	@Override
	public Map<String, Object> getP25_V_2_0(String id, String prodId,
			String qDate) {
		return this.getJdbc().queryForMap("MIS.KRM040.getP25_G_V_2_0",
				new String[] { id, qDate, prodId, prodId });
	}

	@Override
	public Map<String, Object> getKRM001StopCreditCard(String id,
			String prodId, String qDate) {
		return this.getJdbc().queryForMap("MIS.KRM001.getStopCreditCard",
				new String[] { id, prodId, qDate });
	}

	@Override
	public Map<String, Object> getBAM101CollectionLog(String id, String prodId,
			String qDate) {
		return this.getJdbc().queryForMap("MIS.BAM101.getCollectionLog",
				new String[] { id, prodId, qDate });
	}
	
	@Override
	public Map<String, Object> getBAM101PassDueAmt(String id, String prodId,
			String qDate) {
		return this.getJdbc().queryForMap("MIS.BAM101.getPassDueAmt",
				new String[] { id, prodId, qDate });
	}

	@Override
	public Map<String, Object> getVAM106Data(String id, String prodId,
			String qDate) {
		return this.getJdbc().queryForMap("MIS.VAM106.getData",
				new String[] { id, prodId, qDate });
	}

	@Override
	public Map<String, Object> getVAM107Data(String id, String prodId,
			String qDate) {
		return this.getJdbc().queryForMap("MIS.VAM107.getData",
				new String[] { id, prodId, qDate });
	}

	@Override
	public Map<String, Object> getVAM108Data(String id, String prodId,
			String qDate) {
		return this.getJdbc().queryForMap("MIS.VAM108.getData",
				new String[] { id, prodId, qDate });
	}

	@Override
	public Map<String, Object> getBAM087DelayPayLoan(String id, String prodId,
			String qDate) {
		// SQL效率太差,改由java處理
		// return this.getJdbc()
		// .queryForMap(
		// "MIS.BAM087.getDelayPayLoan",
		// new String[] { id, id, id, id, id, id, id, id, id, id,
		// id, id });

		Map<String, Object> result = new HashMap<String, Object>();
		int count = 0;
		List<Map<String, Object>> list = getBAM087DelayPayLoan2(id, prodId,
				qDate);
		for (Map<String, Object> map : list) {
			String PAY_CODE_12 = Util.trim(map.get("PAY_CODE_12"));
			if (Util.isNotEmpty(PAY_CODE_12)) {
				char[] chars = PAY_CODE_12.toCharArray();
				for (char c : chars) {
					if (ArrayUtils.indexOf(DelayPayLoanChars, c) != -1)
						count++;
				}
			}
		}
		result.put("Counts", count);

		return result;
	}

	private List<Map<String, Object>> getBAM087DelayPayLoan2(String id,
			String prodId, String qDate) {
		return this.getJdbc().queryForList("MIS.BAM087.getDelayPayLoan2",
				new String[] { id, prodId, qDate });
	}

	@Override
	public Map<String, Object> getKRM040CardPayCode2(String id, String prodId,
			String qDate, String lowDate, String highDate) {
		return this.getJdbc().queryForMap("MIS.KRM040.getCardPayCode2",
				new String[] { id, prodId, qDate, lowDate, highDate });
	}

	@Override
	public Map<String, Object> getKRM040CardPayCode3(String id, String prodId,
			String qDate, String lowDate, String highDate) {
		return this.getJdbc().queryForMap("MIS.KRM040.getCardPayCode3",
				new String[] { id, prodId, qDate, lowDate, highDate });
	}

	@Override
	public List<Map<String, Object>> getKRM040CardPayCode4(String id,
			String prodId, String qDate, String lowDate, String highDate) {
		return this.getJdbc().queryForList("MIS.KRM040.getCardPayCode4",
				new String[] { id, prodId, qDate, lowDate, highDate });
	}

	@Override
	public Map<String, Object> getKRM040CashAdvance(String id, String prodId,
			String qDate) {
		return this.getJdbc().queryForMap("MIS.KRM040.getCashAdvance",
				new String[] { id, prodId, qDate });
	}

	@Override
	public Map<String, Object> getBAM087CashCard(String id, String prodId,
			String qDate) {
		// SQL效率太差,改由java處理
		// return this.getJdbc()
		// .queryForMap(
		// "MIS.BAM087.getCashCard",
		// new String[] { id, id, id, id, id, id, id, id, id, id,
		// id, id });

		// id = "X101010107"; // test F222691414,
		Map<String, Object> result = null;
		List<Map<String, Object>> list = getBAM087CashCard2(id, prodId, qDate);
		for (Map<String, Object> map : list) {
			/*
			 * 問處內負責JCIC的同仁，欄位 PAY_CODE_12 的內容 0-無延遲 X-不需還款
			 */
			String PAY_CODE_12 = Util.trim(map.get("PAY_CODE_12"));
			if (Util.isNotEmpty(PAY_CODE_12)) {
				char[] chars = PAY_CODE_12.toCharArray();
				for (char c : chars) {
					if (ArrayUtils.indexOf(CashCardChars, c) == -1) {
						if (result == null)
							result = new HashMap<String, Object>();
						result.put("PCODE", "D");
					}
				}
			}
		}

		return result;
	}

	private List<Map<String, Object>> getBAM087CashCard2(String id,
			String prodId, String qDate) {
		return this.getJdbc().queryForList("MIS.BAM087.getCashCard2",
				new String[] { id, prodId, qDate });
	}

	@Override
	public List<Map<String, Object>> getBAM087Data(String id, String prodId,
			String qDate) {
		return this.getJdbc().queryForList("MIS.BAM087.getData",
				new String[] { id, qDate, prodId, id, qDate, prodId });
		// return this.getJdbc().queryForList("MIS.BAM087.getData2",
		// new String[] { id, qDate });
	}

	@Override
	public Map<String, Object> getBAM087CollectionInfo1(String id,
			String prodId, String qDate) {
		return this.getJdbc().queryForMap("MIS.BAM087.getCollectionInfo1",
				new String[] { id, prodId, qDate, id, prodId, qDate });
	}

	// @Override
	// public Map<String, Object> getBAM303CollectionInfo1(String id) {
	// return this.getJdbc().queryForMap("MIS.BAM303.getCollectionInfo1",
	// new String[] { id });
	// }

	// @Override
	// public Map<String, Object> getKRM001CreditData2(String id) {
	// return this.getJdbc().queryForMap("MIS.KRM001.getCreditData2",
	// new String[] { id });
	// }

	@Override
	public List<Map<String, Object>> getCPXQueryLogHtml(String id,
			String prodId, String qDate) {
		return this.getJdbc().queryForList("mis.CPXQueryLog.getHtml",
				new String[] { id, qDate, prodId });
	}

	@Override
	public String getPurposeCode(String id, String prodId, String qDate) {
		String result = "";
		Map<String, Object> map = this.getJdbc().queryForMap(
				"MIS.BAM095.PURPOSE_CODE", new String[] { id, qDate, prodId });
		if (map != null) {
			if (Util.parseInt(map.get("Counts")) > 0) {
				result = "1";
			}
		}

		return result;
	}

	@Override
	public Map<String, Object> getP69P19(String id, String prodId, String qdate) {
		return this.getJdbc().queryForMap("MIS.KRM040.getP69P19",
				new String[] { id, qdate, prodId });
	}

	@Override
	public Map<String, Object> getD63LnNosBank(String id, String prodId,
			String qDate) {
		return this.getJdbc().queryForMap("BAM095.D63LnNosBank",
				new String[] { id, qDate, prodId, prodId });
	}

	@Override
	public Map<String, Object> getA21Cc6RcUseMonth_Q_V_1_0(String id,
			String prodId, String qDate) {
		return this.getJdbc().queryForMap("KRM040.A21Cc6RcUseMonth_Q_V_1_0",
				new String[] { id, qDate, prodId, prodId });
	}

	@Override
	public Map<String, Object> getAllCc6RcUseBank_Q_V_1_0(String id,
			String prodId, String qDate) {
		return this.getJdbc().queryForMap("KRM040.AllCc6RcUseBank_Q_V_1_0",
				new String[] { id, qDate, prodId, prodId });
	}

	@Override
	public Map<String, Object> getD53Ln6TimesFlag(String id, String prodId,
			String qDate) {
		return this.getJdbc().queryForMap("BAM095.D53Ln6TimesFlag",
				new String[] { id, qDate, prodId, prodId });
	}

	@Override
	public Map<String, Object> getAllCc6RcUseBank_V2_0(String id,
			String prodId, String qDate) {
		return this.getJdbc().queryForMap("KRM040.a11_cc6_rc_use_bank_V2_0",
				new String[] { id, qDate, prodId, prodId });
	}

	@Override
	public Map<String, Object> getP68P19_G_2_0(String id, String prodId,
			String qDate) {
		return this.getJdbc().queryForMap("KRM040.p68_p19_G_V_2_0",
				new String[] { id, qDate, prodId, prodId, prodId });
	}

	@Override
	public Map<String, Object> getP68P19_Q_2_0(String id, String prodId,
			String qDate) {
		/*
		 * 計算 P19 時, 用到 WHEN PAY_STAT ='1' AND PAY_CODE ='N'
		 * 
		 * ref 聯徵 KRM040 的備註說明
		 * 
		 * 上期繳款之繳款狀況代號： 第一碼為繳款金額代號 第二碼為繳款時間代號 X:不須繳款 X:不須繳款 1:全額繳清 N：無遲延
		 * 2:繳足最低金額但未全額繳清 1:遲延未滿一個月 3:未繳足最低金額 2:遲延一個月至未滿二個月 4:全額未繳 3:遲
		 * 延二個月至未滿3個月 4:遲延三個月至未滿四個月 5:遲延四個月至未滿五個月 6:遲延五個月至未滿六個月 7:遲延六個月以上 ⇒ 組合出
		 * XX, 1N, 2N 的值
		 */
		return this.getJdbc().queryForMap("KRM040.p68_p19_Q_V_2_0",
				new String[] { id, qDate, prodId, prodId, prodId });
	}

	@Override
	public Map<String, Object> getC101M01A_JcicFlag(String id, String prodId,
			String qDate) {
		return this.getJdbc().queryForMap("get_C101M01A_JcicFlag",
				new String[] { id, qDate, prodId });
	}

	@Override
	public Map<String, Object> getKRM040_revol_cnt(String id, String prodId,
			String qDate) {
		return this.getJdbc().queryForMap("KRM040.get_revol_cnt",
				new String[] { id, qDate, prodId });
	}

	@Override
	public List<Map<String, Object>> getKRM040_data(String id, String prodId,
			String qdate) {
		TreeMap<String, List<Map<String, Object>>> qdate_map = new TreeMap<String, List<Map<String, Object>>>();
		for (Map<String, Object> map : this.getJdbc().queryForListWithMax(
				"MIS.KRM040.getData",
				new String[] { id, prodId, "0010101", "9991231" })) {
			String QDATE = Util.trim(MapUtils.getString(map, "QDATE"));
			String ISSUE = Util.trim(MapUtils.getString(map, "ISSUE"));
			if (Util.equals("TOT", ISSUE)) {
				continue;
			}

			if (!qdate_map.containsKey(QDATE)) {
				qdate_map.put(QDATE, new ArrayList<Map<String, Object>>());
			}
			qdate_map.get(QDATE).add(map);
		}

		List<Map<String, Object>> r = null;
		if (Util.isNotEmpty(qdate)) {
			r = qdate_map.get(qdate);
		} else {
			r = qdate_map.get(qdate_map.lastKey());
		}
		if (r != null) {
			return r;
		}
		return new ArrayList<Map<String, Object>>();
	}

	@Override
	public Map<String, Object> getZ03(String id, String prodId, String qDate) {
		return this.getJdbc().queryForMap("BAM306.get_Z03",
				new String[] { id, qDate, prodId, prodId });
	}

	@Override
	public List<Map<String, Object>> getClsRecordLOGFILE(String id,
			String dupNo, String prodId) {
		return this.getJdbc().queryForList("LOGFILE.getClsRecord",
				new String[] { id, prodId });
	}

	@Override
	public List<Map<String, Object>> getBAM095_data(String id, String prodId,
			String qdate) {
		return this.getJdbc().queryForListWithMax("MIS.BAM095.getData",
				new String[] { id, prodId, qdate });
	}

	@Override
	public List<Map<String, Object>> getBAM095_IS_KIND(String id, String prodId,
			String qdate) {
		return this.getJdbc().queryForListWithMax("MIS.BAM095.IS_KIND",
				new String[] { id, prodId, qdate });
	}

	@Override
	public List<Map<String, Object>> getKCS003_data_ordByQdateDesc(String id) {
		return this.getJdbc().queryForListWithMax(
				"MIS.KCS003.getData_ordByQdateDesc", new String[] { id });
	}

	@Override
	public Map<String, String> get_DAM001_DAM003_relateData(String custId) {
		return get_DAM001_DAM003_relateData_withOutputParam(custId, false);
	}

	@Override
	public Map<String, String> get_DAM001_DAM003_relateData_debug(String custId) {
		return get_DAM001_DAM003_relateData_withOutputParam(custId, true);
	}

	private Map<String, String> get_DAM001_DAM003_relateData_withOutputParam(
			String custId, boolean isDebug) {
		String DAI001_QDATE = "";
		String DAI001_DATA_DATE = "";
		String DAI001_PRODID = "";
		// ~~~
		String DAI002_QDATE = "";
		String DAI002_DATA_DATE = "";
		String DAI002_PRODID = "";

		String dam001_list_info = "";
		String dam003_list_info = "";
		// ~~~
		String eChkDDate = "";

		double rtNt = 0;
		double rtNm = 0;
		double rtYt = 0;
		double rtYm = 0;
		Date dt_reject_date = CapDate.parseDate(CapDate.ZERO_DATE);
		if (true) { // 大額退票 DAI001 , DAM001
			String txid_at_logFile = "QB36";
			Map<String, Object> map_givenTXId = this.getJdbc().queryForMap(
					"LOGFILE.get_latest_by_TXID",
					new String[] { custId, txid_at_logFile });
			if (MapUtils.isNotEmpty(map_givenTXId)) {
				String logfile_QDATE = Util.trim(MapUtils.getString(
						map_givenTXId, "QDATE"));
				String logfile_PRODID = Util.trim(MapUtils.getString(
						map_givenTXId, "PRODID"));
				Map<String, Object> map_DAI001 = this.getJdbc().queryForMap(
						"MIS.DAI001.get_data",
						new String[] { custId, logfile_PRODID, logfile_QDATE });
				if (MapUtils.isNotEmpty(map_DAI001)) {
					DAI001_QDATE = Util.trim(MapUtils.getString(map_DAI001,
							"QDATE")); // 109/05/16
					DAI001_DATA_DATE = Util.trim(MapUtils.getString(map_DAI001,
							"DATA_DATE")); // 100/01/02
					DAI001_PRODID = Util.trim(MapUtils.getString(map_DAI001,
							"PRODID")); // DAI001 裡的 PRODID ，可能有 P7,P9, ST
					List<Map<String, Object>> list = _get_B36_etch_detailData(
							custId, DAI001_PRODID, DAI001_QDATE);
					dam001_list_info = "[" + custId + "][" + DAI001_PRODID
							+ "][" + DAI001_QDATE + "]===> size=" + list.size();
					for (Map<String, Object> dam001 : list) {
						rtNm += MapUtils.getDoubleValue(dam001, "BOUNCE_AMT"); // DECIMAL
																				// 50萬元以下主體退票未清償註記總金額(千元)
						rtNt += MapUtils.getDoubleValue(dam001, "BOUNCE_CNT"); // DECIMAL
																				// 50萬元以下主體退票未清償註記總張數
						Date bounce = parse_latest_bounce(MapUtils.getString(
								dam001, "LATEST_BOUNCE")); // CHAR(7)
															// 50萬元以下主體最近一次退票日期
						dt_reject_date = choose_latestDate_excludeZeroDate(
								dt_reject_date, bounce);
						rtYm += MapUtils.getDoubleValue(dam001, "WRITEOFF_AMT"); // DECIMAL
																					// 50萬元以下主體退票已清償註記總金額(千元)
						rtYt += MapUtils.getDoubleValue(dam001, "WRITEOFF_CNT");// DECIMAL
																				// 50萬元以下主體退票已清償註記票總張數
					}
				}
			}
		}

		if (true) { // 小額退票 DAI002 , DAM003
			Map<String, Object> map_DAI002 = _getD10_latest(custId);
			if (MapUtils.isNotEmpty(map_DAI002)) {
				DAI002_QDATE = Util.trim(MapUtils
						.getString(map_DAI002, "QDATE")); // 109/05/16
				DAI002_DATA_DATE = Util.trim(MapUtils.getString(map_DAI002,
						"DATA_DATE")); // 1000213
				DAI002_PRODID = Util.trim(MapUtils.getString(map_DAI002,
						"PRODID")); // DAI002 裡的 PRODID ，目前只有 ST
				// ================================
				List<Map<String, Object>> list = _get_D10_detailData(custId,
						DAI002_PRODID, DAI002_QDATE);
				dam003_list_info = "[" + custId + "][" + DAI002_PRODID + "]["
						+ DAI002_QDATE + "]===> size=" + list.size();
				for (Map<String, Object> dam003 : list) {

					rtNm += MapUtils.getDoubleValue(dam003, "BOUNCE_AMT"); // DECIMAL
																			// 50萬元以下主體退票未清償註記總金額(千元)
					rtNt += MapUtils.getDoubleValue(dam003, "BOUNCE_CNT"); // DECIMAL
																			// 50萬元以下主體退票未清償註記總張數
					Date bounce = parse_latest_bounce(MapUtils.getString(
							dam003, "LATEST_BOUNCE")); // CHAR(7)
														// 50萬元以下主體最近一次退票日期
					dt_reject_date = choose_latestDate_excludeZeroDate(
							dt_reject_date, bounce);
					rtYm += MapUtils.getDoubleValue(dam003, "WRITEOFF_AMT"); // DECIMAL
																				// 50萬元以下主體退票已清償註記總金額(千元)
					rtYt += MapUtils.getDoubleValue(dam003, "WRITEOFF_CNT");// DECIMAL
																			// 50萬元以下主體退票已清償註記票總張數
					// EJF750_LATEST_WRITEOFF CHAR(7) 50萬元以下主體最近一次已清償註記註銷日期
				}
			}
		}

		if (Util.isNotEmpty(DAI001_DATA_DATE)
				&& Util.isNotEmpty(DAI002_DATA_DATE)) {
			Date date_dai001_dataData = TWNDate.valueOf(DAI001_DATA_DATE);
			Date date_dai002_dataData = TWNDate.valueOf(DAI002_DATA_DATE);
			if (date_dai001_dataData != null && date_dai002_dataData != null) {
				// 資料日期 eChkDDate : 以較早的一個為主
				eChkDDate = TWNDate.toAD(get_smallDate(date_dai001_dataData,
						date_dai002_dataData));
			}
		}
		/*
		 * e-Loan 需要自 ETCHDB 取得3項資料 (1) 退票未清償註記總張數 (2) 拒絕往來日期 (3) 退票已清償註記總張數
		 * 
		 * 
		 * ETCHDB 應該只用 {未清償、已清償} 與 {存款不足、簽章不符、擅自指定金融業者為本票之擔當付款人、本票提示期限經過前撤銷付款委託}
		 * 來劃分 (A) 以 POR_ 開頭，表示有 {清償註記} (B) 退票原因 + UED_DCC 存款不足退票張數 + UMS_BCC
		 * 發票人簽章不符退票張數 + SD_BCC 擅自指定金融業者為本票之擔當付款人退票張數 + CPE_BCC
		 * 本票提示期限經過前撤銷付款委託退票張數
		 * 
		 * key="MIS.MSG_001.getData" select ued_dcc, ums_bcc, sd_bcc, cpe_bcc ,
		 * reject_date , por_ued_bcc,por_ums_bcc,por_sd_bcc,por_cpe_bcc from
		 * MIS.MSG_001 where QID = ?
		 * ========================================================= 本行八項負面資訊
		 * chkItem1a 退票 : 8個欄位任1個的數值 > 0 chkItem1b 拒往 : reject_date
		 * ========================================================= 相關資料查詢 退票紀錄
		 * isQdata9 , 另寫入 C101S01J
		 * 
		 * c101s01j.setRtD(Util.parseDate(reject_date)); // 最近一次退票日期
		 * 
		 * c101s01j.setRtNt(totcnt); // 退票未清償註記總張數
		 * c101s01j.setRtNm(Util.parseBigDecimal(map.get("TOTAMT"))); //
		 * 退票未清償註記總金額
		 * 
		 * c101s01j.setRtYt(porcnt); // 退票已清償註記總張數
		 * c101s01j.setRtYm(Util.parseBigDecimal(map.get("PORAMT"))); //
		 * 退票已清償註記總金額
		 * 
		 * 拒往紀錄 isQdata10 , 另寫入 C101S01J
		 * 
		 * c101s01j.setRdD(...); // RdD vs RtD
		 * ========================================================= 勞工紓困簡易評分
		 * C101S01X notAllowD 退票 {未註銷} 達3張
		 */
		String TOTCNT = pretty_numStr(new BigDecimal(rtNt));
		String TOTAMT = pretty_numStr(new BigDecimal(rtNm));
		String reject_date = "";// etchdb 裡的 mis.msg_001 ,格式為 01030119
		if (isNull_or_ZeroDate(dt_reject_date)) {
			reject_date = "00000000";
		} else {
			reject_date = Util.getRightStr(
					"00000000" + TWNDate.valueOf(dt_reject_date).toTW(), 8);// 民國年,
																			// 前補0,
																			// 取8碼
		}

		String PORCNT = pretty_numStr(new BigDecimal(rtYt));
		String PORAMT = pretty_numStr(new BigDecimal(rtYm));
		if (isDebug) {
			Map<String, String> r = new HashMap<String, String>();
			r.put("DAI001_QDATE", DAI001_QDATE);
			r.put("DAI001_DATA_DATE", DAI001_DATA_DATE); // 100/01/02
			r.put("DAI001_PRODID", DAI001_PRODID);
			// ~~~
			r.put("DAI002_QDATE", DAI002_QDATE);
			r.put("DAI002_DATA_DATE", DAI002_DATA_DATE); // 1000213
			r.put("DAI002_PRODID", DAI002_PRODID);

			r.put("dam001_list_info", dam001_list_info);
			r.put("dam003_list_info", dam003_list_info);
			_inject_DAM001_DAM003_output(r,
					Util.trim(TWNDate.toAD(TWNDate.valueOf(DAI001_QDATE))),
					eChkDDate, TOTCNT, TOTAMT, reject_date, PORCNT, PORAMT);
			return r;
		} else {
			Map<String, String> r = new HashMap<String, String>();
			if (Util.isNotEmpty(DAI001_QDATE) && Util.isNotEmpty(DAI002_QDATE)
					&& Util.equals(DAI001_QDATE, DAI002_QDATE)
					&& Util.isNotEmpty(eChkDDate)) { // 限同一天查詢,
				r.put("DAI001_DATA_DATE", DAI001_DATA_DATE); // 100/01/02
				r.put("DAI002_DATA_DATE", DAI002_DATA_DATE); // 1000213
				_inject_DAM001_DAM003_output(r,
						Util.trim(TWNDate.toAD(TWNDate.valueOf(DAI001_QDATE))),
						eChkDDate, TOTCNT, TOTAMT, reject_date, PORCNT, PORAMT);
			}
			return r;
		}
	}

	private Date parse_latest_bounce(String s) {
		if (Util.equals("", s) || Util.equals("N", s)) {
			return null;
		}
		return TWNDate.valueOf(s);
	}

	private void _inject_DAM001_DAM003_output(Map<String, String> r,
			String eChkQDate, String eChkDDate, String TOTCNT, String TOTAMT,
			String reject_date, String PORCNT, String PORAMT) {
		r.put("eChkQDate", eChkQDate); // yyyy-MM-dd
		r.put("eChkDDate", eChkDDate); // yyyy-MM-dd
		r.put("TOTCNT", TOTCNT);
		r.put("TOTAMT", TOTAMT);
		r.put("reject_date", reject_date);
		r.put("PORCNT", PORCNT);
		r.put("PORAMT", PORAMT);
	}

	private String pretty_numStr(BigDecimal r) {
		if (r == null) {
			return "";
		} else if (r.compareTo(BigDecimal.ZERO) == 0) {
			/*
			 * 為了在處理 lnf916s_accu_int
			 */
			return "0";
		} else {
			return r.stripTrailingZeros().toPlainString();
		}
	}

	private Date choose_latestDate_excludeZeroDate(Date reject_date, Date bounce) {
		if (isNOT_null_and_NOTZeroDate(bounce)) {
			if (isNOT_null_and_NOTZeroDate(reject_date)) {
				if (cmpDate(bounce, ">", reject_date)) {
					return bounce;
				} else {
					return reject_date;
				}
			} else {
				return bounce;
			}
		} else {
			return reject_date;
		}
	}

	private Date get_smallDate(Date d1, Date d2) {
		if (cmpDate(d1, "<", d2)) {
			return d1;
		} else {
			return d2;
		}
	}

	private boolean isNOT_null_and_NOTZeroDate(Date d) {
		return !isNull_or_ZeroDate(d);
	}

	private boolean isNull_or_ZeroDate(Date d) {
		if (d == null) {
			return true;
		}
		if (Util.equals(CapDate.ZERO_DATE, TWNDate.toAD(d))) {
			return true;
		}
		return false;
	}

	private boolean cmpDate(Date d1, String sign, Date d2) {
		return cmp_use_flag(d1, sign, d2, "yyyy-MM-dd");
	}

	private static boolean cmp_use_flag(Date d1, String sign, Date d2,
			String fmt) {
		int a = Integer.parseInt(CapDate.formatDate(d1, fmt).replace("-", ""));
		int b = Integer.parseInt(CapDate.formatDate(d2, fmt).replace("-", ""));
		if (sign.equals("<")) {
			return (a < b);
		} else if (sign.equals("<=")) {
			return (a <= b);
		} else if (sign.equals("==")) {
			return (a == b);
		} else if (sign.equals(">=")) {
			return (a >= b);
		} else if (sign.equals(">")) {
			return (a > b);
		}
		// default 用 < 判斷
		return (a < b);
	}

	private Map<String, Object> _getD10_latest(String id) {
		return this.getJdbc().queryForMap("MIS.DAI002.get_latest",
				new String[] { id });
	}

	/**
	 * 取得DAM001 主體連帶退票摘要紀錄(含外幣資訊)
	 */
	@Override
	public List<Map<String, Object>> _get_B36_etch_detailData(String id,
			String prodid, String qdate) {
		return this.getJdbc().queryForListWithMax("MIS.DAM001.get_data",
				new String[] { id, prodid, qdate });
	}

	private List<Map<String, Object>> _get_D10_detailData(String id,
			String prodid, String qdate) {
		return this.getJdbc().queryForListWithMax("MIS.DAM003.get_data",
				new String[] { id, prodid, qdate });
	}

	@Override
	public Map<String, Object> getLatestQueryRecordOfCreditInfoById(String id) {
		return this.getJdbc().queryForMap(
				"MIS.BAI001.LOGFILE.getLatestQueryRecordOfCreditInfoById",
				new String[] { id });
	}

	@Override
	public Map<String, Object> getLatestQueryRecordOfCreditInfoByIdInP7P9(
			String id) {
		return this.getJdbc().queryForMap(
				"MIS.BAI001.getLatestQueryRecordOfCreditInfoByIdInP7P9",
				new String[] { id });
	}

	@Override
	public List<Map<String, Object>> getBAM095Data(String id, String prodId,
			String queryPersonId, String queryPersonBranchNo, String queryDate) {
		return this.getJdbc().queryForListWithMax(
				"MIS.BAM095.getBAM095Data",
				new String[] { id, prodId, queryDate, queryPersonId,
						queryPersonBranchNo });
	}

	@Override
	public List<Map<String, Object>> getBAM305Data(String id, String prodId,
			String queryPersonId, String queryPersonBranchNo, String queryDate) {
		return this.getJdbc().queryForListWithMax(
				"MIS.BAM305.getBAM305Data",
				new String[] { id, prodId, queryDate, queryPersonId,
						queryPersonBranchNo });
	}

	@Override
	public int getHousingLoanCountByBAM095(String id, String prodId,
			String queryPersonId, String queryPersonBranchNo, String queryDate) {
		Map<String, Object> map = this.getJdbc().queryForMap(
				"MIS.BAM095.getHousingLoanCountByBAM095",
				new String[] { id, prodId, queryPersonId, queryPersonBranchNo,
						queryDate });
		return (Integer) map.get("TOTAL_COUNT");
	}

	@Override
	public boolean isEjcicDataQueryTimesMoreThan2ByOtherBank(String id,
			String prodId, String date, int itemCode_P_threshold) {
		Map<String, Object> map = this.getJdbc().queryForMap(
				"MIS.STM022.getEjcicDataQueryTimesMoreThan2ByOtherBank",
				new String[] { id, prodId, date });
		int count = (Integer) map.get("TOTAL_COUNT");
		return count >= itemCode_P_threshold ? true : false;
	}

	@Override
	public Map<String, Object> getEarliestIssueDateOfUnsuspensionCreditCard(
			String id) {
		return this.getJdbc().queryForMap(
				"MIS.KRM046.getEarliestIssueDateOfUnsuspensionCreditCard",
				new String[] { id });
	}

	@Override
	public Map<String, Object> getKRM046Data_getNotStopMegaCard_Min_StartDate(
			String id) {
		return this.getJdbc().queryForMap(
				"MIS.KRM046.getKRM046Data_getNotStopMegaCard_Min_StartDate",
				new String[] { id });
	}

	@Override
	public Map<String, Object> getLabourInsuredCount(String custId,
			String queryBranch) {
		return this.getJdbc().queryForMap("MIS.RAM020.getLabourInsuredCount",
				new String[] { custId, custId });
	}

	@Override
	public Map<String, Object> getLabourInsuredAmountMoreThan23800Count(
			String custId, String queryBranch) {
		return this.getJdbc().queryForMap(
				"MIS.RAM020.getLabourInsuredAmountMoreThan23800Count",
				new String[] { custId, custId });
	}

	@Override
	public Map<String, Object> getStopCardDataOfCreditCard(String custId,
			String prodId, String queryDate) {
		return this.getJdbc().queryForMap(
				"MIS.KRS001.getStopCardDataOfCreditCard",
				new String[] { custId, prodId, queryDate });
	}

	@Override
	public Map<String, Object> getIdChangedRecord(String custId, String prodId,
			String queryDate) {
		return this.getJdbc().queryForMap("MIS.VAM020.getIdChangedRecord",
				new String[] { custId, prodId, queryDate });
	}

	@Override
	public Map<String, Object> getOtherSupplementaryNoteInfo(String custId,
			String prodId, String queryDate) {
		return this.getJdbc().queryForMap(
				"MIS.VAM108.getOtherSupplementaryNoteInfo",
				new String[] { custId, prodId, queryDate });
	}

	@Override
	public Map<String, Object> getCaseNotifiedRecord(String custId,
			String prodId, String queryDate) {
		return this.getJdbc().queryForMap("MIS.VAM201.getCaseNotifiedRecord",
				new String[] { custId, prodId, queryDate });
	}

	@Override
	public List<Map<String, Object>> getForcedStopCardDataByStopCode(
			String custId, String prodId, String queryDate) {
		return this.getJdbc().queryForListWithMax(
				"MIS.KRM046.getForcedStopCardDataByStopCode",
				new String[] { custId, prodId, queryDate });
	}

	@Override
	public Map<String, String> getPaymentSituationOfBillInLatestMonthByBankCode(
			String custId, String prodId, String queryDate) {
		List<Map<String, Object>> list = this.getJdbc().queryForListWithMax(
				"MIS.KRM040.getPaymentSituationOfBillInLatestMonthByBankCode",
				new String[] { custId, prodId, queryDate });
		Map<String, String> map = new HashMap<String, String>();
		for (Map<String, Object> m : list) {
			map.put(String.valueOf(m.get("ISSUE")),
					String.valueOf(m.get("STATUS")));
		}

		return map;
	}

	@Override
	public Map<String, Object> getAnnualIncomeIsLessThan50WanIn108Year(
			String custId) {
		return this.getJdbc().queryForMap(
				"MIS.RAS020.getAnnualIncomeIsLessThan50WanIn108Year",
				new String[] { custId });
	}

	@Override
	public Map<String, Object> getLatePaymentRecordOfCreditOver30Days(
			String custId, String queryDate) {
		return this.getJdbc().queryForMap(
				"MIS.BAS020.getLatePaymentRecordOfCreditOver30Days",
				new String[] { custId, queryDate });
	}

	@Override
	public Map<String, Object> getOverdueCollectionAndBadDebtData(
			String custId, String prodId, String queryDate) {
		return this.getJdbc().queryForMap(
				"MIS.BAM210.getOverdueCollectionAndBadDebtData",
				new String[] { custId, prodId, queryDate });
	}

	@Override
	public Map<String, Object> getAbnormalCreditRecord(String custId,
			String prodId, String queryDate) {
		return this.getJdbc().queryForMap("MIS.BAS001.getAbnormalCreditRecord",
				new String[] { custId, prodId, queryDate });
	}

	@Override
	public Map<String, Object> getVAM106DataExceptMainCodeD(String id,
			String prodId, String qDate) {
		return this.getJdbc().queryForMap(
				"MIS.VAM106.getAllDataExceptMainCodeD",
				new String[] { id, prodId, qDate });
	}

	@Override
	public Map<String, Object> getVAM107DataExceptMainCodeD(String id,
			String prodId, String qDate) {
		return this.getJdbc().queryForMap(
				"MIS.VAM107.getAllDataExceptMainCodeD",
				new String[] { id, prodId, qDate });
	}

	@Override
	public Map<String, Object> getBAM095CheckAccountCode2HaveS(String id,
			String prodId, String qDate) {
		return this.getJdbc().queryForMap("MIS.BAM095.checkAccountCode2HaveS",
				new String[] { id, prodId, qDate });
	}

	@Override
	public Map<String, Object> getBAM305CheckAccountCode2HaveS(String id,
			String prodId, String qDate) {
		return this.getJdbc().queryForMap("MIS.BAM305.checkAccountCode2HaveS",
				new String[] { id, prodId, qDate });
	}

	@Override
	public Map<String, Object> getBAM306CheckAccountCode2HaveS(String id,
			String prodId, String qDate) {
		return this.getJdbc().queryForMap("MIS.BAM306.checkAccountCode2HaveS",
				new String[] { id, prodId, qDate });
	}

	/**
	 * 查詢最近一次產品組合查詢紀錄且查詢理由是本人同意
	 */
	@Override
	public List<Map<String, Object>> getZAM003RQueryRecord(String custId,
			String bgnDate, String endDate, String queryItem) {
		return this.getJdbc().queryForListWithMax("MIS.ZAM003R.getQueryRecord",
				new String[] { custId, bgnDate, endDate, queryItem });
	}
	
	@Override
	public Map<String, Object> getBam095_BuyingRealEstateAndCollateralInSpecificTypeData(String id, String prodId, String qDate){
		return this.getJdbc().queryForMap("MIS.BAM095.getBuyingRealEstateAndCollateralInSpecificType",
				new String[] { id, prodId, qDate });
	}

	/**
	 * 查詢最近1期信用卡預借現金紀錄
	 */
	@Override
	public Map<String, Object> getKRM040CreditCardCashLent(String id, String qDate,String prodId) {
		return this.getJdbc().queryForMap("MIS.KRM040.getCreditCardCashLent",
				new String[] { id, qDate, prodId });
	}

	/**
	 * 查詢現金卡餘額
	 */
	@Override
	public Map<String, Object> getBAM087CashCardLoan(String id,String qDate,String prodId) {
		return this.getJdbc().queryForMap("MIS.BAM087.getBAM087CashCardLoan",
				new String[] { id, qDate, prodId });
	}

	/**
	 * 退票異常紀錄
	 */
	@Override
	public List<Map<String, Object>> getDAS001_data(String id, String prodId,
			String qdate) {
		return this.getJdbc().queryForListWithMax("MIS.DAS001.get_data",
				new String[] { id, prodId, qdate });
	}
	
	/**
	 * 拒往紀錄
	 */
	@Override
	public List<Map<String, Object>> getDAS002_data(String id, String prodId,
			String qdate) {
		return this.getJdbc().queryForListWithMax("MIS.DAS002.get_data",
				new String[] { id, prodId, qdate });
	}

	@Override
	public List<Map<String, Object>> getVAM106DataList(String id,
			String prodId, String qDate) {
		return this.getJdbc().queryForListWithMax("MIS.VAM106.getData",
				new String[] { id, prodId, qDate });
	}

	@Override
	public List<Map<String, Object>> getVAM107DataList(String id,
			String prodId, String qDate) {
		return this.getJdbc().queryForListWithMax("MIS.VAM107.getData",
				new String[] { id, prodId, qDate });
	}
	
	@Override
	public List<Map<String, Object>> getKRM046Data(String id) {
		return this.getJdbc().queryForListWithMax("MIS.KRM046.getData", new String[] { id });
	}
	
	@Override
	public List<Map<String, Object>> getEjcicS11Data(String reqId, String reqBranchNo, String id) {
		return this.getJdbc().queryForListWithMax("MIS.LOGFILE.getS11Data", new String[] { reqId, reqBranchNo, id });
	}
	
	@Override
	public Map<String, Object> getLatestLogFileByTxId(String custId, String txId) {
		return this.getJdbc().queryForMap("LOGFILE.get_latest_by_TXID", new String[] { custId, txId });
	}
	
	@Override
	public List<Map<String, Object>> getBam029NewApprovedQuotaByOtherBank(String id, String prodId, String qdate) {
		return this.getJdbc().queryForListWithMax("MIS.BAM029.getNewApprovedQuotaByOtherBank", new String[] { id, prodId, qdate });
	}
	
	@Override
	public Map<String, Object> getLatestLogFileByTxIdAndQdate(String custId, String txId, String qDate) {
		return this.getJdbc().queryForMap("LOGFILE.get_latest_by_TXID_QDate", new String[] { custId, txId, qDate });
	}
	
	@Override
	public Map<String, Object> getLatestLogFileByQKey1_txId_toJcic(String custId, String txId) {
		return this.getJdbc().queryForMap("LOGFILE.get_latest_by_qKey1_txId_toJcic", new String[] { custId, txId });
	}
	
	@Override
	public List<Map<String, Object>> getEjcicQueryRecoredByOtherBank(String custId, String txId, String qDate) {
		return this.getJdbc().queryForListWithMax("MIS.STM017.getEjcicQueryRecoredByOtherBank", new String[] { custId, txId, qDate });
	}
	
	@Override
	public List<Map<String, Object>> getNewQuotaOrRepaymentInfo(String custId, String txId, String qDate) {
		return this.getJdbc().queryForListWithMax("MIS.BAS006.getNewQuotaOrRepaymentInfo", new String[] { custId, txId, qDate });
	}
	
	@Override
	public Map<String, Object> getLatestLogFileByQkey1AndProdId(String custId, String prodId) {
		return this.getJdbc().queryForMap("LOGFILE.get_latest_by_qKey1_prodId", new String[] { custId, prodId });
	}
	
	@Override
	public Map<String, Object> getD42_G_3_0(String id, String prodId,String qDate) {
		return this.getJdbc().queryForMap("KRM046.d42_G_V_3_0",new String[] { id, qDate, prodId });
	}
	
	@Override
	public Map<String, Object> getN01_G_3_0(String id, String prodId,String qDate) {
		return this.getJdbc().queryForMap("STM022.n01_G_V_3_0",new String[] { id, qDate, prodId, prodId, prodId });
	}
	
	@Override
	public Map<String, Object> getR01_G_3_0(String id, String prodId,String qDate) {
		return this.getJdbc().queryForMap("MIS.KRM040.R01_G_V_3_0",new String[] { id, prodId, qDate });
	}
	
	@Override
	public Map<String, Object> getKRM040_MaxQdate(String id) {
		return this.getJdbc().queryForMap("MIS.KRM040.MAX_QDATE",new String[] { id });
	}
	
	@Override
	public Map<String, Object> getP01_Q_V_4_0(String id, String prodId,String qDate) {
		return this.getJdbc().queryForMap("MIS.BAM095.P01_Q_V_4_0",new String[] { id, prodId, qDate });
	}
	
	@Override
	public Map<String, Object> getR01_Q_V_4_0(String id, String prodId,String qDate) {
		return this.getJdbc().queryForMap("MIS.KRM040.R01_Q_V_4_0",new String[] { id, prodId, qDate });
	}
	
	@Override
	public Map<String, Object> getKCS003DataBy(String id, String prodId, String qDate) {
		return this.getJdbc().queryForMap("MIS.KCS003.getDataBy_Id_ProdId_Qdate", new String[] { id, prodId, qDate });
	}
	
	@Override
	public List<Map<String, Object>> getP7P9HtmlData(String custId, String prodId){
		// 在「查詢項目135」、「查詢項目128」、「查詢項目116」都有 AAS003
		String qDate = Util.trim(this.getAAS003QDate(custId, prodId));
		List<Map<String, Object>> list = this.getCPXQueryLogHtml(custId, prodId, qDate);
		return list;
	}
	
	@Override
	public Map<String, Object> getVAM106DataExceptMainCode29DF(String id, String prodId, String qDate) { 
		return this.getJdbc().queryForMap("MIS.VAM106.getByMainCodeNotIn_2_9_D_F", new String[] { id, prodId, qDate });
	}
	
	@Override
	public Map<String, Object> getVAM107DataExceptMainCode29DF(String id, String prodId, String qDate) { 
		return this.getJdbc().queryForMap("MIS.VAM107.getByMainCodeNotIn_2_9_D_F", new String[] { id, prodId, qDate });
	}
	
	@Override
	public Map<String, Object> getVAM108DataExceptMainCode29DF(String id, String prodId, String qDate) { 
		return this.getJdbc().queryForMap("MIS.VAM108.getByMainCodeNotIn_2_9_D_F", new String[] { id, prodId, qDate });
	}
	
	@Override
	public List<Map<String, Object>> getKRM040CardPayDelayCountsByBank(String id, String prodId,
			String qDate, String lowDate, String highDate) {
		return this.getJdbc().queryForListWithMax("MIS.KRM040.getCardPayDelayCountsByBank",
				new String[] { id, prodId, qDate, lowDate, highDate });
	}
	
	@Override
	public List<Map<String, Object>> getKRM040CardPayDelayDataByBank(String id, String prodId,
			String qDate, String lowDate, String highDate, String issue){
		return this.getJdbc().queryForListWithMax("MIS.KRM040.getCardPayDelayDataByBank",
				new String[] { id, prodId, qDate, lowDate, highDate, issue});
	}
	
	@Override
	public List<Map<String, Object>> getKRM040CardPayLatestDateByBank(String id, String prodId,	String qDate){
		return this.getJdbc().queryForListWithMax("MIS.KRM040.getCardPayLatestDateByBank",
				new String[] { id, prodId, qDate });
	}
	
	@Override
	public Map<String, Object> getKRM040CardPayLatestDataUseRevolByBank(String id, String prodId, String qDate, 
			String billDate,String issue){
		return this.getJdbc().queryForMap("MIS.KRM040.getCardPayLatestDataUseRevolByBank",
				new String[] { id, prodId, qDate, billDate, issue});
	}
	
	@Override
	public Map<String, Object> getBAM095AllBankAmtData(String id, String prodId, String qDate){
		return this.getJdbc().queryForMap("MIS.BAM095.getAllBankAmtData",
				new String[] { id, qDate, prodId, id, qDate, prodId});
	}
	
	@Override
	public Map<String, Object> getBAM095Bank017AmtData(String id, String prodId, String qDate){
		return this.getJdbc().queryForMap("MIS.BAM095.getBank017AmtData",
				new String[] { id, qDate, prodId, id, qDate, prodId});
	}
	
	@Override
	public List<Map<String, Object>> getBAM305LoanAmtPassDueAmtByBank(String id, String prodId,	String qDate){
		return this.getJdbc().queryForListWithMax("MIS.BAM305.getLoanAmtPassDueAmtByBank",
				new String[] { id, prodId, qDate });
	}
	
	@Override
	public List<Map<String, Object>> getBAM306LoanAmtPassDueAmtByBank(String id, String prodId,	String qDate){
		return this.getJdbc().queryForListWithMax("MIS.BAM306.getLoanAmtPassDueAmtByBank",
				new String[] { id, prodId, qDate });
	}
	
	@Override
	public Map<String, Object> getKRM040CardPayAbnormalCount(String id, String prodId, String qDate){
		return this.getJdbc().queryForMap("MIS.KRM040.getCardPayAbnormalCount",
				new String[] { id, prodId, qDate});
	}

	@Override
	public Map<String, Object> getBAM305PassDueAmtOverZeroCount(String id, String prodId, String qDate){
		return this.getJdbc().queryForMap("MIS.BAM305.getPassDueAmtOverZeroCount",
				new String[] { id, prodId, qDate});
	}
	
	@Override
	public Map<String, Object> findTAS500(String id, String queryItem, String qDate, String qBranch) {
		return getJdbc().queryForMap("MIS.TAS500.findTAS500", new String[] { id, queryItem, qDate, qBranch });
	}
	
	@Override
	public Map<String, Object> findBT3FILEByIdQDate(String txId, String id, String qDate, String qBranch) {
		return getJdbc().queryForMap("MIS.BT3FILE.findByTxIdIdQDate", new String[]{ txId, id, qDate, qBranch });
	}
	
	@Override
	public Map<String, Object> findTAS700ById(String id) {
		return getJdbc().queryForMap("MIS.TAS700.findTAS700ById", new String[] { id });
	}
	
	@Override
	public Map<String, Object> findBT2FILEByTxIdIdQDate(String txId, String id, String qDate) {
		return getJdbc().queryForMap("MIS.BT2FILE.findByTxIdIdQDate", new String[]{ txId, id, qDate });
	}

	@Override
	public List<Map<String, Object>> findEJF369() {
		return this.getJdbc().queryForListWithMax("MIS.EJF369.findAll", new String[] {});
	}
	
	@Override
	public String findEJF369VDEPTID(String brId) {
		Map<String, Object> resultMap = this.getJdbc().queryForMap("MIS.EJF369.findVDEPTID", new String[] { brId });
		String result = brId;
		if(resultMap != null){
			result = Util.trim(resultMap.get("EJF369_VDEPTID"));
		}
		return result;
	}

	@Override
	public List<Map<String, Object>> getKRM040getRevolBalByDebtRateWithoutSum(String id,
																			  String prodId, String qDate) {
		return this.getJdbc().queryForListWithMax("MIS.KRM040.getKRM040getRevolBalByDebtRateWithoutSum",
				new String[] { id, qDate, prodId });
	}
	
	@Override
	public Map<String, Object> getKrm040PermanentQuotaAndRevolvingCreditData(String id) {
		return this.getJdbc().queryForMap("MIS.KRM040.getPermanentQuotaAndRevolvingCreditData", new String[] { id });
	}
	
	@Override
	public Map<String, Object> getTAS700DataBy(String id, String prodId) {
		return this.getJdbc().queryForMap("MIS.TAS700.getDataBy_Id_ProdId", new String[] { id, prodId });
	}
	
	@Override
	public Map<String, Object> getBT2FileDataBy(String custId, String txId, String qDate, String qempCode, String qBranch) {
		return this.getJdbc().queryForMap("MIS.BT2FILE.getSuccessHtmlData", new String[] { custId, txId, qDate, qempCode, qBranch });
	}
}
