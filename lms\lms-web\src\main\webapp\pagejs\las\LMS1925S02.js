$(document).ready(function(){
	
	$("#tAddr,#statementAddrFrom,#statementAddrTo").change(function(){
		var value = $(this).val().toFull();
		$(this).val(value);
	});
	
	$("#reInclude").click(function(){
		$.ajax({
            handler: "lms1925m01formhandler",
            action: "doInclude",
            data: $.extend($("#tabForm").serializeData(),{
            	custId: $("#custId").val(),
            	dupNo: $("#dupNo").val()
            }),
            success: function(responseData){			
				grid.trigger("reloadGrid");
				grid2.trigger("reloadGrid");
			}
        });
	});
	
	var grid = $("#result").iGrid({
        height: 50,
        width: "100%",
		//localFirst:true,
        autowidth: true,
        handler: "lms1925gridhandler",
        action: "queryL192M01B",
        postData: {
        	custType: "1"
        },
        rownumbers :true,
        colModel:[{
        	colHeader: i18n.lms1925m01['lms1925s02.007'],// "身份證"
        	name: "custId",
            align: "center"
        },{
        	colHeader: i18n.lms1925m01['lms1925s02.008'],// "借款人姓名",
        	name: "custName",
            align: "center"
        },{
        	colHeader: i18n.lms1925m01['lms1925s02.009'],// "(職業)行業別",
        	name: "posi",
            align: "center"
        },{
			colHeader: i18n.lms1925m01['lms1925s03.005'],// "幣別(收入)",
        	name: "incomeCurr",
            align: "center"
		},{
        	colHeader: i18n.lms1925m01['lms1925s02.010'],// "收入",
        	name: "incomeAmt",
            align: "center"
        },{
        	name: "oid",
        	hidden: "true"
        },{
        	name: "mainId",
        	hidden: "true"
        }]
	});
	
	var grid2 = $("#result2").iGrid({
        height: 50,
        width: "100%",
        autowidth: true,
		//localFirst:true,
        handler: "lms1925gridhandler",
        action: "queryL192M01B",
        postData: {
        	custType: "2"
        },
        rownumbers :true,
        colModel:[{
        	colHeader: i18n.lms1925m01['lms1925s02.007'],// "身份證"
        	name: "custId",
            align: "center"
        },{
        	colHeader: i18n.lms1925m01['lms1925s02.008'],// "借款人姓名",
        	name: "custName",
            align: "center"
        },{
        	colHeader: i18n.lms1925m01['lms1925s02.009'],// "(職業)行業別",
        	name: "posi",
            align: "center"
        },{
			colHeader: i18n.lms1925m01['lms1925s03.005'],// "幣別(收入)",
        	name: "incomeCurr",
            align: "center"
		},{
        	colHeader: i18n.lms1925m01['lms1925s02.010'],// "收入",
        	name: "incomeAmt",
            align: "center"
        },{
        	name: "oid",
        	hidden: "true"
        },{
        	name: "mainId",
        	hidden: "true"
        }]
	});
	
	
});