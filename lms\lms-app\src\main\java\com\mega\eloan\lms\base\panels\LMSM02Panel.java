package com.mega.eloan.lms.base.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;

/**<pre>
 * 異常通報界面共用
 * </pre>
 * @since  2012/10/17
 * <AUTHOR>
 * @version <ul>
 *           <li>2012/10/17,<PERSON>,new
 *          </ul>
 */
public class LMSM02Panel extends Panel {

	/**
	 * 
	 */
	private static final long serialVersionUID = -4024257163623646201L;

	public LMSM02Panel(String id) {
		super(id);
//		add(new LMSM02APanel("lmsm02a_panel"));
//		add(new LMSM02APanel("lmsm02a_panel2"));
	}

	public LMSM02Panel(String id, boolean updatePanelName) {
		super(id, updatePanelName);
	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);
	}
}
