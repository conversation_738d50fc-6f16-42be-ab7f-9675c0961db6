$(function(){

    var grid = $("#gridview").iGrid({
        handler: 'cls1221gridhandler',
        height: 350,
        width: 785,
        autowidth: false,
        action: "queryView_C",
        postData: {
            docStatus: viewstatus
        },
        rowNum: 15,
        sortname: "applyStatus|applyTS",
        sortorder: "asc|desc",
        multiselect: false, //338034全部進件資料
    	colModel : [
    	  {name : 'oid', hidden : true}
    	 ,{name : 'mainId',hidden : true}
    	 ,{name : 'docStatus',hidden : true}
    	,{
			colHeader : i18n.cls1220m02['C122M01A.custId'], 
			width : 90, //設定寬度
			sortable : true, //是否允許排序
			name : 'custId', //身分證統編
			formatter: 'click',
			onclick : openDoc							
		},{
			colHeader : ' ',
			width : 8, //設定寬度
			name : 'dupNo',
			sortable : false					
		},{
			colHeader : i18n.cls1220m02['C122M01A.custName'],
			align : "left",
			width : 140, //設定寬度
			sortable : true, //是否允許排序
			name : 'custName' //col
		},{
			colHeader : i18n.cls1220m02['C122M01A.applyTS'],
			align : "left",
			width : 110, //設定寬度		
			name : 'applyTS'
		},{
			colHeader : i18n.cls1220m02['C122M01A.applyStatus'],
			align : "left",
			width : 90, //設定寬度
			name : 'applyStatus' 
		},{
			colHeader : i18n.cls1220m02['C122M01A.modifyDocStatus'],
			align : "left",
			width : 90, //設定寬度
			sortable : false,
			name : 'isClosed' //借 欄位 isClosed 放 docStatusCN
		},{
			colHeader : i18n.cls1220m02['C122M01A.updater'],
			align : "left",
			width : 110, //設定寬度
			sortable : false, //是否允許排序
			name : 'updater' //col.id	
		},{
			colHeader : i18n.cls1220m02['C122M01A.updateTime'],
			align : "left",
			width : 130, //設定寬度
			sortable : false, //是否允許排序
			name : 'updateTime'//col.id
		}]				
	});
   
    /**
     * 回傳 yyyy-MM-dd
     * @param {Object} n_month
     */
    function getBefore_N_MonthDate(n_month){
        var sysdate = CommonAPI.getToday().split("-");
        var tDate = new Date(sysdate[0], sysdate[1] - 1, sysdate[2]);
        tDate.setMonth(tDate.getMonth() - n_month);
        return tDate.getFullYear() + "-" + (tDate.getMonth() < 9 ? "0" : "") + (tDate.getMonth() + 1) + "-" + (tDate.getDate() < 10 ? "0" : "") + tDate.getDate();
    }
    
    function openDoc(cellvalue, options, rowObject){
    	$.form.submit({
			url : '../lms/cls1220m02/01',
			data : {
                mainOid: rowObject.oid,
                mainId: rowObject.mainId,
                mainDocStatus: rowObject.docStatus
            },
            target: rowObject.oid
		});
    };
	
    $("#buttonPanel").find('#btnView').click(function(){
		var selrow = grid.getGridParam('selrow');
        if (selrow) {
            openDoc('', '', grid.getRowData(selrow));
        }
        else {
            CommonAPI.showErrorMessage(i18n.def["grid.selrow"]);
        }	
    }).end().find("#btnFilter").click(function(){    	
    	var _id = "_div_cls1220v0x_c";
		var _form = _id+"_form";
		 	
		if ($("#"+_id).length == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");
			dyna.push("<form id='"+_form+"'>");
			dyna.push("	<table class='tb2' width='100%' >");
			dyna.push("	<tr>");
			dyna.push("	  <td class='hd2' width='30%' nowrap>"+i18n.cls1220m02['C122M01A.custId']+"</td>");
			dyna.push("	  <td><input type='text' id='custId' name='custId' maxlength='10' /></td>");
			dyna.push("	</tr>");
			dyna.push("	<tr>");			
			dyna.push("	  <td class='hd2' width='30%' nowrap>"+i18n.cls1220m02['C122M01A.applyTS']+"</td>");
			dyna.push("	  <td>");
			dyna.push("	   <input type='text' id='applyTS_beg' name='applyTS_beg' maxlength='10' class='date' />");
			dyna.push("	 ~ <input type='text' id='applyTS_end' name='applyTS_end' maxlength='10' class='date' />");
			dyna.push("	  </td>");
			dyna.push("	</tr>");
			dyna.push("	<tr>");
			dyna.push("	  <td class='hd2' width='30%' nowrap>"+i18n.cls1220m02['C122M01A.applyStatus']+"</td>");
			dyna.push("	  <td><select id='applyStatus' name='applyStatus'>");
			dyna.push("	     <option value=''></option>");
			dyna.push("	     <option value='0A0'>"+i18n.cls1220m02['C122M01A.applyStatus.0A0']+"</option>");
			dyna.push("	     <option value='0B0'>"+i18n.cls1220m02['C122M01A.applyStatus.0B0']+"</option>");
			dyna.push("	     <option value='Z01'>"+i18n.cls1220m02['C122M01A.applyStatus.Z01']+"</option>");
			dyna.push("	     <option value='Z03'>"+i18n.cls1220m02['C122M01A.applyStatus.Z03']+"</option>");
			dyna.push("	  </select></td>");
			dyna.push("	</tr>");
			
			dyna.push(" </table>");
			dyna.push("</form>");
			
			dyna.push("</div>");
			
		    $('body').append(dyna.join(""));		
		    
		    $("#"+_form).find(".date").filter(function(){
		        return !$(this).prop('readonly');
		    }).datepicker();
		    
		    if(true){
		    	//在 function pageInit(...) 中，會針對 欄位 custId addClass upText
		    	pageInit.call( $("#"+_id) );
		    }
		}
		//clear data
		$("#"+_form).reset();
		$("#"+_form).find("[name=applyTS_beg]").val(getBefore_N_MonthDate(3));
		$("#"+_form).find("[name=applyTS_end]").val(CommonAPI.getToday());
		
		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
	       title: i18n.def.query,
	       width: 450,
           height: 210,
           align: "center",
           valign: "bottom",
           modal: false,
           i18n: i18n.def,
           buttons: {
               "sure": function(){
            	   $.thickbox.close();
            	   //~~~~
            	   grid.jqGrid("setGridParam", {
	           	    	postData : $.extend({ docStatus: viewstatus}, $("#"+_form).serializeData() ),
	           			search: true			
	           	   }).trigger("reloadGrid");
               },
               "cancel": function(){
            	   $.thickbox.close();            	  
               }
           }
		});
	});
    
   
    var getLastDateOfTheMonth = function(){
        var tDate = new Date();
        tDate.setMonth(tDate.getMonth() + 1);
        tDate.setDate(1);
        tDate.setDate(tDate.getDate() - 1);
        return tDate.getFullYear() + "-" +
        (tDate.getMonth() < 9 ? "0" : "") +
        (tDate.getMonth() + 1) +
        "-" +
        (tDate.getDate() < 10 ? "0" : "") +
        tDate.getDate();
    }
});

