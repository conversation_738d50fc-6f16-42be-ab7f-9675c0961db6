/* 
 * L184M01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.util.Date;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import org.apache.commons.lang3.builder.ToStringExclude;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;

/** 企金戶新增/增額/逾放轉正名單檔 **/
@Entity
@Table(name="L184M01A", uniqueConstraints = @UniqueConstraint(columnNames = {"oid" }))
//		uniqueConstraints = @UniqueConstraint(columnNames = {"dataDate","branchId"}))
public class L184M01A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;
	
	/**
	 * JOIN條件 L184A01A．關聯檔
	 * 
	 */
	@ToStringExclude
	@OneToMany(mappedBy = "l184m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private Set<L184A01A> l184a01a;

	public Set<L184A01A> getL184a01a() {
		return l184a01a;
	}

	public void setL184a01a(Set<L184A01A> l184a01a) {
		this.l184a01a = l184a01a;
	}

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 
	 * uid<p/>
	 * 如同 notes unique id
	 */
	@Column(name="UID", length=32, columnDefinition="CHAR(32)")
	private String uid;

	/** 文件編號 **/
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * 區部別<p/>
	 * 0.無、1.DBU、4.OBU、5.海外(海外同業, 海外客戶)
	 */
	@Column(name="TYPCD", length=1, columnDefinition="CHAR(1)")
	private String typCd;

	/** 統一編號 **/
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 重覆序號 **/
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 客戶名稱 **/
	@Column(name="CUSTNAME", length=120, columnDefinition="VARCHAR(120)")
	private String custName;

	/** 
	 * 辦理單位類別<p/>
	 * 1.分行/自辦 2.區域營運中心 3.徵信處
	 */
	@Column(name="UNITTYPE", length=1, columnDefinition="CHAR(1)")
	private String unitType;

	/** 
	 * 編製單位代號<p/>
	 * 評等單位/編製單位
	 */
	@Column(name="OWNBRID", length=3, columnDefinition="CHAR(3)")
	private String ownBrId;

	/** 目前文件狀態 **/
	@Column(name="DOCSTATUS", length=3, columnDefinition="VARCHAR(3)")
	private String docStatus;

	/** 文件亂碼 **/
	@Column(name="RANDOMCODE", length=32, columnDefinition="CHAR(32)")
	private String randomCode;

	/** 文件URL **/
	@Column(name="DOCURL", length=40, columnDefinition="VARCHAR(40)")
	private String docURL;

	/** 
	 * 交易代碼<p/>
	 * 第1碼為系統碼
	 */
	@Column(name="TXCODE", length=6, columnDefinition="CHAR(6)")
	private String txCode;

	/** 建立人員號碼 **/
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Date updateTime;

	/** 
	 * 核准人員號碼<p/>
	 * reCheck
	 */
	@Column(name="APPROVER", length=6, columnDefinition="CHAR(6)")
	private String approver;

	/** 
	 * 核准日期<p/>
	 * sendDateTime
	 */
	@Column(name="APPROVETIME", columnDefinition="TIMESTAMP")
	private Date approveTime;

	/** 
	 * 是否結案<p/>
	 * Y: 已結案 N: 未結案<br/>
	 *  2011/08/10 新增：文件鎖定檢查時使用
	 */
	@Column(name="ISCLOSED", length=1, columnDefinition="CHAR(1)")
	private String isClosed;

	/** 
	 * 刪除註記<p/>
	 * 2011/11/08 新增：文件刪除時使用(非立即性刪除)
	 */
	@Column(name="DELETEDTIME", columnDefinition="TIMESTAMP")
	private Date deletedTime;

	/** 
	 * 資料期間<p/>
	 * YYYY-MM-01
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="DATADATE", columnDefinition="DATE")
	private Date dataDate;

	/** 分行別 **/
	@Column(name="BRANCHID", length=3, columnDefinition="CHAR(03)")
	private String branchId;

	/** 
	 * EXCEL檔檔案位置<p/>
	 * 101/03/22新增
	 */
	@Column(name="EXCELFILE", length=40, columnDefinition="VARCHAR(40)")
	private String excelFile;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 
	 * 取得uid<p/>
	 * 如同 notes unique id
	 */
	public String getUid() {
		return this.uid;
	}
	/**
	 *  設定uid<p/>
	 *  如同 notes unique id
	 **/
	public void setUid(String value) {
		this.uid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得區部別<p/>
	 * 0.無、1.DBU、4.OBU、5.海外(海外同業, 海外客戶)
	 */
	public String getTypCd() {
		return this.typCd;
	}
	/**
	 *  設定區部別<p/>
	 *  0.無、1.DBU、4.OBU、5.海外(海外同業, 海外客戶)
	 **/
	public void setTypCd(String value) {
		this.typCd = value;
	}

	/** 取得統一編號 **/
	public String getCustId() {
		return this.custId;
	}
	/** 設定統一編號 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得重覆序號 **/
	public String getDupNo() {
		return this.dupNo;
	}
	/** 設定重覆序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得客戶名稱 **/
	public String getCustName() {
		return this.custName;
	}
	/** 設定客戶名稱 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/** 
	 * 取得辦理單位類別<p/>
	 * 1.分行/自辦 2.區域營運中心 3.徵信處
	 */
	public String getUnitType() {
		return this.unitType;
	}
	/**
	 *  設定辦理單位類別<p/>
	 *  1.分行/自辦 2.區域營運中心 3.徵信處
	 **/
	public void setUnitType(String value) {
		this.unitType = value;
	}

	/** 
	 * 取得編製單位代號<p/>
	 * 評等單位/編製單位
	 */
	public String getOwnBrId() {
		return this.ownBrId;
	}
	/**
	 *  設定編製單位代號<p/>
	 *  評等單位/編製單位
	 **/
	public void setOwnBrId(String value) {
		this.ownBrId = value;
	}

	/** 取得目前文件狀態 **/
	public String getDocStatus() {
		return this.docStatus;
	}
	/** 設定目前文件狀態 **/
	public void setDocStatus(String value) {
		this.docStatus = value;
	}

	/** 取得文件亂碼 **/
	public String getRandomCode() {
		return this.randomCode;
	}
	/** 設定文件亂碼 **/
	public void setRandomCode(String value) {
		this.randomCode = value;
	}

	/** 取得文件URL **/
	public String getDocURL() {
		return this.docURL;
	}
	/** 設定文件URL **/
	public void setDocURL(String value) {
		this.docURL = value;
	}

	/** 
	 * 取得交易代碼<p/>
	 * 第1碼為系統碼
	 */
	public String getTxCode() {
		return this.txCode;
	}
	/**
	 *  設定交易代碼<p/>
	 *  第1碼為系統碼
	 **/
	public void setTxCode(String value) {
		this.txCode = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}

	/** 
	 * 取得核准人員號碼<p/>
	 * reCheck
	 */
	public String getApprover() {
		return this.approver;
	}
	/**
	 *  設定核准人員號碼<p/>
	 *  reCheck
	 **/
	public void setApprover(String value) {
		this.approver = value;
	}

	/** 
	 * 取得核准日期<p/>
	 * sendDateTime
	 */
	public Date getApproveTime() {
		return this.approveTime;
	}
	/**
	 *  設定核准日期<p/>
	 *  sendDateTime
	 **/
	public void setApproveTime(Date value) {
		this.approveTime = value;
	}

	/** 
	 * 取得是否結案<p/>
	 * Y: 已結案 N: 未結案<br/>
	 *  2011/08/10 新增：文件鎖定檢查時使用
	 */
	public String getIsClosed() {
		return this.isClosed;
	}
	/**
	 *  設定是否結案<p/>
	 *  Y: 已結案 N: 未結案<br/>
	 *  2011/08/10 新增：文件鎖定檢查時使用
	 **/
	public void setIsClosed(String value) {
		this.isClosed = value;
	}

	/** 
	 * 取得刪除註記<p/>
	 * 2011/11/08 新增：文件刪除時使用(非立即性刪除)
	 */
	public Date getDeletedTime() {
		return this.deletedTime;
	}
	/**
	 *  設定刪除註記<p/>
	 *  2011/11/08 新增：文件刪除時使用(非立即性刪除)
	 **/
	public void setDeletedTime(Date value) {
		this.deletedTime = value;
	}

	/** 
	 * 取得資料期間<p/>
	 * YYYY-MM-01
	 */
	public Date getDataDate() {
		return this.dataDate;
	}
	/**
	 *  設定資料期間<p/>
	 *  YYYY-MM-01
	 **/
	public void setDataDate(Date value) {
		this.dataDate = value;
	}

	/** 取得分行別 **/
	public String getBranchId() {
		return this.branchId;
	}
	/** 設定分行別 **/
	public void setBranchId(String value) {
		this.branchId = value;
	}

	/** 
	 * 取得EXCEL檔檔案位置<p/>
	 * 101/03/22新增
	 */
	public String getExcelFile() {
		return this.excelFile;
	}
	/**
	 *  設定EXCEL檔檔案位置<p/>
	 *  101/03/22新增
	 **/
	public void setExcelFile(String value) {
		this.excelFile = value;
	}
}
