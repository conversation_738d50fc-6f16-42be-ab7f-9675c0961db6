/* 
 * CLS9041M04GridHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */package com.mega.eloan.lms.fms.handler.grid;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.lms.fms.service.CLS9041M04Service;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 每月新增就學貸款明細
 * </pre>
 * 
 * @since 2012/11/05
 * <AUTHOR> Lo
 * @version <ul>
 *          <li>2012/11/01,Vector Lo,new
 *          </ul>
 */
@Scope("request")
@Controller("cls9041m04gridhandler")
public class CLS9041M04GridHandler extends AbstractGridHandler {

	@Resource
	CLS9041M04Service service;

	@Resource
	BranchService brService;

	/**
	 * show 每月新增就學貸款明細
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	public CapMapGridResult query(ISearch pageSetting, PageParameters params) throws CapException {
		Calendar now = Calendar.getInstance();
		int year = now.get(Calendar.YEAR), month = now.get(Calendar.MONTH) + 1;// 起始為0
		List<Map<String, Object>> data = service.findMap(year, month);

		for (int i = 0; i < data.size(); i++) {
			Map<String, Object> pivot = data.get(i);
			if (Util.isNotEmpty(pivot.get("branch"))) {
				pivot.put("brnoName",
						brService.getBranchName(pivot.get("branch").toString()));
			}
			// 顯示單位：千元
			Object amt = pivot.get("fact_amt"), loan = pivot.get("loan_val");
			if (Util.isNotEmpty(amt) && Util.isNotEmpty(loan)) {
				pivot.put("fact_amt", new BigDecimal(amt.toString())
						.divide(new BigDecimal(1000)));
				pivot.put("loan_val", new BigDecimal(loan.toString())
						.divide(new BigDecimal(1000)));
			}
			// 獲得學歷稱呼(博士or碩士)
			if (Util.isNotEmpty(pivot.get("educls"))) {

				char educls = pivot.get("educls").toString().charAt(0);
				switch (educls) {
				case '1':
				case '2':
				case '4':
					pivot.put("educls", "博士");
					break;
				case '3':
					pivot.put("educls", "碩士");
				}
			}

		}
		return new CapMapGridResult(data, data.size());
	}
}
