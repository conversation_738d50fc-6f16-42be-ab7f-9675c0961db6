/* 
 * L184A01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L184A01A;

/** 企金戶新增/增額/逾放轉正報表授權檔 **/
public interface L184A01ADao extends IGenericDao<L184A01A> {

	L184A01A findByOid(String oid);

	L184A01A findByMainId(String mainId);

	L184A01A findByUniqueKey(String mainId, String ownUnit, String authType,
			String authUnit);

	List<L184A01A> findByIndex01(String mainId, String ownUnit,
			String authType, String authUnit);
}