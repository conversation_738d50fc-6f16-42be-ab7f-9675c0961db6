/* 
 * MicroEntServiceImpl.java 
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.service.impl;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.constants.SysParamConstants;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.pages.LMSLgdCommomPage;
import com.mega.eloan.lms.base.service.CLSLgdService;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.LMSLgdService;
import com.mega.eloan.lms.base.service.ProdService;
import com.mega.eloan.lms.base.service.ProdService.ProdKindEnum;
import com.mega.eloan.lms.dao.L140S02ADao;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01C;
import com.mega.eloan.lms.model.L140S02A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * <pre>
 * J-110-0986_05097_B1001 於簽報書新增LGD欄位
 * </pre>
 * 
 * @since 2019/09
 * <AUTHOR>
 * @version <ul>
 *          <li>2019/09,009301,new
 *          </ul>
 */
@Service("CLSLgdService")
public class CLSLgdServiceImpl extends AbstractCapService implements
		CLSLgdService {
	
	@Resource
	ProdService prodService;
	
	@Resource
	CLSService clsService;
	
	@Resource
	SysParameterService sysParameterService;
	
	@Resource
	LMSLgdService lmsLgdService;
	
	@Resource
	CodeTypeService codeTypeService;
	
	@Resource
	EloandbBASEService eloandbBASEService;
	
	@Resource
	BranchService branchService;
	
	@Resource
	L140S02ADao l140s02aDao;
	
	protected final Logger logger = LoggerFactory.getLogger(getClass());

	static final String[] PurchaseOrRepairSubject = new String[]{"273", "473", "474", "673", "674"};

	static final Map<String, String> mortgageMore766LgdGroup = new HashMap<String, String>();
	static final Map<String, String> mortgageEq_Less766LgdGroup = new HashMap<String, String>();
	static final Map<String, String> creditInsuranceLgdGroup = new HashMap<String, String>();
	static final Map<String, String> noneMortgage_guaranteeLgdGroup = new HashMap<String, String>();
	static final Map<String, String> noneMortgage_noGuaranteeLgdGroup = new HashMap<String, String>();

	static final Map<String, String> lgdDescMap = new HashMap<String, String>();
	
	static{

		mortgageMore766LgdGroup.put("CLS", "D1");
		mortgageMore766LgdGroup.put("LMS", "O1");
		mortgageEq_Less766LgdGroup.put("CLS", "D2");
		mortgageEq_Less766LgdGroup.put("LMS", "O2");
		noneMortgage_noGuaranteeLgdGroup.put("CLS", "D3");
		noneMortgage_noGuaranteeLgdGroup.put("LMS", "O3");
		creditInsuranceLgdGroup.put("CLS", "D4");
		creditInsuranceLgdGroup.put("LMS", "O4");
		noneMortgage_guaranteeLgdGroup.put("CLS", "D5");
		noneMortgage_guaranteeLgdGroup.put("LMS", "O5");
	}

	@Override 
	public Map<String, Object> processByProKindAndSubject(String snoKind, String l120m01a_mainId, List<L140S02A> l140s02aList, BigDecimal ltv, String cntrno){

		boolean isMortgageLoan = this.isMortgageLoanModel(l140s02aList);
		
		boolean isAllGuaranteeSubject = this.isAllGuaranteeSubjectByProdKind(l140s02aList);
		
		BigDecimal lgd = this.computingLgd(snoKind, isMortgageLoan, isAllGuaranteeSubject, ltv, "CLS");
		String group = this.computingLgdGroup(snoKind, isMortgageLoan, isAllGuaranteeSubject, ltv, "CLS");
		
		logger.debug("Lgd計算結果 -> Cntrno:{}, snoKind:{}, isMortgageLoan:{}, ltv:{}, isAllGuaranteeSubject:{}, Lgd:{}, group:{}", 
						new Object[]{cntrno, snoKind, isMortgageLoan, ltv, isAllGuaranteeSubject, lgd, group});
		
		Map<String, Object> rtnMap = new HashMap<String, Object>();
		rtnMap.put("LGD", lgd);
		rtnMap.put("GROUP", group);
		
		return rtnMap;
	}
	
	@Override
	public Map<String, Object> processByPurchaseOrRepairSubject(String snoKind, L140M01A l140m01a, BigDecimal ltv, String landBuildYN, String clsOrLms){
		
		List<L140M01C> l140m01cList = this.clsService.findL140M01C(l140m01a);
		List<String> creditSubjectList = new ArrayList<String>();
		for(L140M01C l140m01c : l140m01cList){
			creditSubjectList.add(l140m01c.getLoanTP());
		}
		
		// 企金個人戶如為土建融, 不考慮會計科目, 皆為非房貸評等
		boolean isMortgageLoan = "Y".equals(landBuildYN) ? false : this.isAllPurchaseOrRepairSubject(l140m01cList);
		
		boolean isAllGuaranteeSubject = this.isAllGuaranteeSubjectByCreditSubject(creditSubjectList);
		
		BigDecimal lgd = this.computingLgd(snoKind, isMortgageLoan, isAllGuaranteeSubject, ltv, clsOrLms);
		String group = this.computingLgdGroup(snoKind, isMortgageLoan, isAllGuaranteeSubject, ltv, clsOrLms);
		
		logger.debug("Lgd計算結果 -> Cntrno:{}, snoKind:{}, isMortgageLoan:{}, ltv:{}, isAllGuaranteeSubject:{}, Lgd:{}, group:{}", 
				new Object[]{l140m01a.getCntrNo(), snoKind, isMortgageLoan, ltv, isAllGuaranteeSubject, lgd, group});
		
		Map<String, Object> rtnMap = new HashMap<String, Object>();
		rtnMap.put("LGD", lgd);
		rtnMap.put("GROUP", group);
		
		return rtnMap;
	}
	
	//判斷是否為房貸評等
	@Override
	public boolean isMortgageLoanModel(List<L140S02A> l140s02aList) {
		
		List<String> modelKindList = new ArrayList<String>();
		for (L140S02A l140s02a : l140s02aList) {
			modelKindList.add(l140s02a.getModelKind());
		}
		
		if(modelKindList.contains(UtilConstants.L140S02AModelKind.房貸)){
			return true;
		}

		return false;
	}
	
	@Override 
	public BigDecimal computingLgd(String snoKind, boolean isMortgageLoan, boolean isAllGuaranteeSubject, BigDecimal ltv, String type){
		
		if(isMortgageLoan){
			
			if(ltv == null){
				return null;
			}
			
			int compare = ltv.compareTo(new BigDecimal("76.6"));
			String lgd = compare > 0 ? this.getMortgageMoreThan766Lgd().get(type) : this.getMortgageEq_Less766Lgd().get(type);
			return new BigDecimal(lgd);
		}
		
		/* 
		 * 非房貸信用評等 
		 * 信保案件
 		 */
		if(UtilConstants.Cntrdoc.snoKind.信保.equals(snoKind)){
			return new BigDecimal(this.getCreditInsuranceLgd().get(type));
		}

		/* 
		 * 非信保案件
		 * 皆為擔保科目
 		 */
		if(isAllGuaranteeSubject){
			return new BigDecimal(this.getNoneMortgage_GuaranteeLgd().get(type));
		}
		
		/* 
		 * 非信保案件
		 * 非全為擔保科目
 		 */
		return new BigDecimal(this.getNoneMortgage_NoGuaranteeLgd().get(type));
	}
	
	private boolean isAllGuaranteeSubjectByProdKind(List<L140S02A> l140s02aList){
		
		for(L140S02A l140s02a : l140s02aList){
			
			if(!this.prodService.isGuarantee(l140s02a.getSubjCode())){
				return false;
			}
		}
		
		return true;
	}
	
	private boolean isAllGuaranteeSubjectByCreditSubject(List<String> creditSubjectList){
		
		for(String subject : creditSubjectList){
			
			if(Integer.valueOf(subject.substring(0, 1)) % 2 == 1){
				return false;
			}
		}
		
		return true;
	}
	
	@Override
	public boolean isAllPurchaseOrRepairSubject(List<L140M01C> l140m01cList){
		
 		for(L140M01C l140m01c : l140m01cList){
			
			if(!Arrays.asList(PurchaseOrRepairSubject).contains(l140m01c.getLoanTP())){
				return false;
			}
		}
		
		return true;
	}
	
	@Override
	public String computingLgdGroup(String snoKind, boolean isMortgageLoan, boolean isAllGuaranteeSubject, BigDecimal ltv, String type){
		
		if(isMortgageLoan){
			
			if(ltv == null){
				return null;
			}
			
			int compare = ltv.compareTo(new BigDecimal("76.6"));
			return compare > 0 ? mortgageMore766LgdGroup.get(type) : mortgageEq_Less766LgdGroup.get(type);
		}
		
		/* 
		 * 非房貸信用評等 
		 * 信保案件
 		 */
		if(UtilConstants.Cntrdoc.snoKind.信保.equals(snoKind)){
			return creditInsuranceLgdGroup.get(type);
		}

		/* 
		 * 非信保案件
		 * 皆為擔保科目
 		 */
		if(isAllGuaranteeSubject){
			return noneMortgage_guaranteeLgdGroup.get(type);
		}
		
		/* 
		 * 非信保案件
		 * 非全為擔保科目
 		 */
		return noneMortgage_noGuaranteeLgdGroup.get(type);
	}
	
	@Override
	public String getComparisonTableHtml(){
		
		Properties prop = MessageBundleScriptCreator.getComponentResource(LMSLgdCommomPage.class);
		Map<String, String> mortgageMore766Lgd = this.getMortgageMoreThan766Lgd();
		Map<String, String> mortgageEq_Less766Lgd = this.getMortgageEq_Less766Lgd();
		Map<String, String> noneMortgage_noGuaranteeLgd = this.getNoneMortgage_NoGuaranteeLgd();
		Map<String, String> creditInsuranceLgd = this.getCreditInsuranceLgd();
		Map<String, String> noneMortgage_guaranteeLgd = this.getNoneMortgage_GuaranteeLgd();
		
		StringBuffer html = new StringBuffer();
		html.append("<div style='margin-bottom:5px;'>");
		html.append("<table  width='100%' border='1' cellspacing='0' cellpadding='0'>");
		html.append("<tr align='center'>");
		html.append("<td></td><td>");
		html.append(prop.getProperty("lgdGroupDescription.html.D1.O1"));
		html.append("</td><td>");
		html.append(prop.getProperty("lgdGroupDescription.html.D2.O2"));
		html.append("</td><td>");
		html.append(prop.getProperty("lgdGroupDescription.html.D3.O3"));
		html.append("</td><td>");
		html.append(prop.getProperty("lgdGroupDescription.html.D4.O4"));
		html.append("</td><td>");
		html.append(prop.getProperty("lgdGroupDescription.html.D5.O5"));
		html.append("</td>");
		html.append("</tr>");
		html.append("<tr>");
		html.append("<td align='center'>").append(prop.getProperty("lgdGroupDescription.text.domestic")).append("</td>");
		html.append("<td align='right'>").append(mortgageMore766Lgd.get("CLS")).append("%</td>");
		html.append("<td align='right'>").append(mortgageEq_Less766Lgd.get("CLS")).append("%</td>");
		html.append("<td align='right'>").append(noneMortgage_noGuaranteeLgd.get("CLS")).append("%</td>");
		html.append("<td align='right'>").append(creditInsuranceLgd.get("CLS")).append("%</td>");
		html.append("<td align='right'>").append(noneMortgage_guaranteeLgd.get("CLS")).append("%</td>");
		html.append("</tr>");
		html.append("<tr>");
		html.append("<td align='center'>").append(prop.getProperty("lgdGroupDescription.text.overseas")).append("</td>");
		html.append("<td align='right'>").append(mortgageMore766Lgd.get("LMS")).append("%</td>");
		html.append("<td align='right'>").append(mortgageEq_Less766Lgd.get("LMS")).append("%</td>");
		html.append("<td align='right'>").append(noneMortgage_noGuaranteeLgd.get("LMS")).append("%</td>");
		html.append("<td align='right'>").append(creditInsuranceLgd.get("LMS")).append("%</td>");
		html.append("<td align='right'>").append(noneMortgage_guaranteeLgd.get("LMS")).append("%</td>");
		html.append("</tr>");
		html.append("</table>");
		html.append("</div>");
		return html.toString();
	}
	
	@Override
	public String getFullLgdDescription(BigDecimal lgd, String group){
		
		Properties prop = MessageBundleScriptCreator.getComponentResource(LMSLgdCommomPage.class);
		lgdDescMap.put("D1", prop.getProperty("lgdGroupDescription.text.D1"));
		lgdDescMap.put("O1", prop.getProperty("lgdGroupDescription.text.O1"));
		lgdDescMap.put("D2", prop.getProperty("lgdGroupDescription.text.D2"));
		lgdDescMap.put("O2", prop.getProperty("lgdGroupDescription.text.O2"));
		lgdDescMap.put("D3", prop.getProperty("lgdGroupDescription.text.D3"));
		lgdDescMap.put("O3", prop.getProperty("lgdGroupDescription.text.O3"));
		lgdDescMap.put("D4", prop.getProperty("lgdGroupDescription.text.D4"));
		lgdDescMap.put("O4", prop.getProperty("lgdGroupDescription.text.O4"));
		lgdDescMap.put("D5", prop.getProperty("lgdGroupDescription.text.D5"));
		lgdDescMap.put("O5", prop.getProperty("lgdGroupDescription.text.O5"));
		
		String desc = StringUtils.isBlank(group) ? "" : " (" + CLSLgdServiceImpl.lgdDescMap.get(group) + ")";
		String lgdStr = lgd == null ? "" : lgd + "%";
		return "LGD：" + lgdStr + desc;
	}
	
	@Override
	public boolean isShowClsLgd(L120M01A l120m01a, String busCode, BigDecimal expectLgd){
		
		boolean isOpenExpectLgd = "Y".equals(this.sysParameterService.getParamValue(SysParamConstants.IS_OPEN_EXPECT_LGD)) ? true : false;
		
		if(!isOpenExpectLgd){
			return false;
		}

		// 消金
		if (LMSUtil.isClsCase(l120m01a)) {
			return true;
		}
		
		// 海外消金
		if (LMSUtil.isOverSea_CLS(l120m01a)) {
			return true;
		}
		
		// 企金個人戶
		if (LMSUtil.isBusCode_060000_130300(busCode)) {
			return true;
		}
		
		return false;
	}
	
	@Override
	public void setClsLgdColumnInfo(CapAjaxFormResult result, L120M01A l120m01a, String busCode, BigDecimal expectLgd, String expectLgdGroup){
		
		boolean isShowClsLgdInfo = this.isShowClsLgd(l120m01a, busCode, expectLgd);
		
		if(isShowClsLgdInfo){
			result.set("clsLgdInfo", this.getComparisonTableHtml());
			result.set("expectLgdDesc", this.getFullLgdDescription(expectLgd, expectLgdGroup));
		}
		
		result.set("isShowClsLgdInfo", isShowClsLgdInfo ? "Y" : "N");
	}
	
	@Override
	public String checkApprovedPercentIsRequiredForClsCase(L120M01A l120m01a, List<L140M01A> l140m01a_list){
		
		boolean isCheckApprovedPercent = "Y".equals(this.sysParameterService.getParamValue("IS_CHECK_APPROVED_PERCENT")) ? true : false;
		if(!isCheckApprovedPercent){
			return "";
		}
		
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		String simplifyFlag = l120m01a.getSimplifyFlag();
		
		if (LMSUtil.isParentCase(l120m01a) 
				|| UtilConstants.unitType.營運中心.equals(Util.trim(user.getUnitType())) 
				|| UtilConstants.unitType.授管處.equals(Util.trim(user.getUnitType()))
				|| UtilConstants.Casedoc.SimplifyFlag.快速審核信貸.equals(simplifyFlag)
				){
			
			return "";
		}
		
		Properties prop = MessageBundleScriptCreator.getComponentResource(LMSLgdCommomPage.class);
		List<String> proKind03CntrnoList = new ArrayList<String>();
		List<String> ltvCntrnoList = new ArrayList<String>();
		
		String[] subject_403_603 = new String[]{"403", "603"};
		
		for (L140M01A l140m01a : l140m01a_list) {

			if (LMSUtil.isContainValue(Util.trim(l140m01a.getProPerty()), UtilConstants.Cntrdoc.Property.不變)) {

				List<L140S02A> l140s02aList = this.clsService.findL140S02A(l140m01a);
				String cntrNo = l140m01a.getCntrNo();
				
				for(L140S02A l140s02a : l140s02aList){
					
					String creditSubj = this.prodService.getSubject8to3(l140s02a.getSubjCode());
					if(ProdKindEnum.行家理財貸款_中長期_03.getCode().equals(l140s02a.getProdKind()) 
							&& Arrays.asList(subject_403_603).contains(creditSubj)
							&& StringUtils.isEmpty(l140m01a.getHeadItem7())){
						// 額度序號{0}為產品種類03，則[擔保品敘述]擔保品是否為房屋需填寫！
						proKind03CntrnoList.add(cntrNo);
						continue;
					}
					
					if(UtilConstants.L140S02AModelKind.房貸.equals(prodService.getMatchModelKind(l140s02a)) && l140m01a.getApprovedPercent() == null){
						ltvCntrnoList.add(cntrNo);
					}
				}
			}
		}
		
		String errorMsg = "";
		
		if(!proKind03CntrnoList.isEmpty()){
			// 額度序號{0}為產品種類03，額度明細表頁籤 → 擔保品頁籤 → 擔保品敘述 →「擔保品是否為房屋」欄位需填寫！
			errorMsg += MessageFormat.format(prop.getProperty("L140M01A.msg.error.prodKindIs03CollateralIsHouseRequired"), StringUtils.join(proKind03CntrnoList, "、"));
			errorMsg += "<br/>";
		}
		
		if(!ltvCntrnoList.isEmpty()){
			// 額度序號{0}為房貸評等，額度明細表頁籤 → 點選「編輯核貸成數」按鈕 → 應填寫目前不動產核貸成數！
			errorMsg += MessageFormat.format(prop.getProperty("L140M01A.msg.error.mortgageModelApprovedPercentIsRequired"), StringUtils.join(ltvCntrnoList, "、"));
		}
		
		return errorMsg;
	}
	
	private Map<String, String> getMortgageMoreThan766Lgd(){
		return this.codeTypeService.findByCodeType("LGD_mortgageMore766Lgd");
	}
	
	private Map<String, String> getMortgageEq_Less766Lgd(){
		return this.codeTypeService.findByCodeType("LGD_mortgageEq_Less766Lgd");
	}
	
	private Map<String, String> getNoneMortgage_NoGuaranteeLgd(){
		return this.codeTypeService.findByCodeType("LGD_noneMortgage_noGuaranteeLgd");
	}
	
	private Map<String, String> getCreditInsuranceLgd(){
		return this.codeTypeService.findByCodeType("LGD_creditInsuranceLgd");
	}
	
	private Map<String, String> getNoneMortgage_GuaranteeLgd(){
		return this.codeTypeService.findByCodeType("LGD_noneMortgage_guaranteeLgd");
	}
	
	@Override 
	public Map<String, Object> processOverseasClsCase_or_LmsPersonalCaseLgd(L140M01A l140m01a, String modelKind, String snoKind, BigDecimal ltv, String clsOrLms){

		boolean isMortgageLoan =  UtilConstants.L140S02AModelKind.房貸.equals(modelKind) ? true : false;
		
		List<L140M01C> l140m01cList = this.clsService.findL140M01C(l140m01a);
		List<String> creditSubjectList = new ArrayList<String>();
		for(L140M01C l140m01c : l140m01cList){
			creditSubjectList.add(l140m01c.getLoanTP());
		}
		
		boolean isAllGuaranteeSubject = this.isAllGuaranteeSubjectByCreditSubject(creditSubjectList);
		
		BigDecimal lgd = this.computingLgd(snoKind, isMortgageLoan, isAllGuaranteeSubject, ltv, clsOrLms);
		String group = this.computingLgdGroup(snoKind, isMortgageLoan, isAllGuaranteeSubject, ltv, clsOrLms);
		
		logger.debug("Lgd計算結果 -> Cntrno:{}, snoKind:{}, isMortgageLoan:{}, ltv:{}, isAllGuaranteeSubject:{}, Lgd:{}, group:{}", 
						new Object[]{l140m01a.getCntrNo(), snoKind, isMortgageLoan, ltv, isAllGuaranteeSubject, lgd, group});
		
		Map<String, Object> rtnMap = new HashMap<String, Object>();
		rtnMap.put("LGD", lgd);
		rtnMap.put("GROUP", group);
		
		return rtnMap;
	}
	
	@Override
	public void setL140m01aExpectModelKind(L140M01A l140m01a){
		
		if(UtilConstants.BrNoType.國內.equals(branchService.getBranch(l140m01a.getCntrNo().substring(0, 3)).getBrNoFlag())){
			
			Map<String, Object> map = this.eloandbBASEService.findOldestL140m01aByCntrNo(l140m01a.getCntrNo());
			if(map != null){
				List<L140S02A> l140s02aList = l140s02aDao.findByMainId(String.valueOf(map.get("MAINID")));
				boolean isMortgageLoan = this.isMortgageLoanModel(l140s02aList);
				l140m01a.setExpectModelKind(isMortgageLoan ? UtilConstants.L140S02AModelKind.房貸 : UtilConstants.L140S02AModelKind.非房貸);
			}
		}
	}
	
	@Override
	public String checkApprovedPercentIsRequiredForOverseasCls_or_LmsPersonalCase(String unitType, L120M01A l120m01a, List<L140M01A> l140m01aList){

		boolean isCheckApprovedPercent = "Y".equals(this.sysParameterService.getParamValue("IS_CHECK_APPROVED_PERCENT")) ? true : false;
		if(!isCheckApprovedPercent){
			return "";
		}
		
		if (UtilConstants.unitType.營運中心.equals(unitType) || UtilConstants.unitType.授管處.equals(unitType)) {
			return "";
		}
		
		Properties prop = MessageBundleScriptCreator.getComponentResource(LMSLgdCommomPage.class);
		List<String> ltvCntrnoList = new ArrayList<String>();
		for(L140M01A l140m01a : l140m01aList){
			
			if(CapString.isEmpty(l140m01a.getCntrNo())){
				continue;
			}
			
			String busCode = lmsLgdService.getCustBusCodeByL140m01a(l140m01a);// 行業對象別
			
			if (LMSUtil.isOverSea_CLS(l120m01a) || LMSUtil.isBusCode_060000_130300(busCode)) {
				
				boolean isDomesticCntrNo = UtilConstants.BrNoType.國內.equals(branchService.getBranch(l140m01a.getCntrNo().substring(0, 3)).getBrNoFlag()) ? true : false;
				
//				if(isDomesticCntrNo && CapString.isEmpty(l140m01a.getExpectModelKind())){
//					return prop.getProperty("L140M01a.msg.error.expectModelKindMustChoose");// 海外消金或企金個人戶案件需填寫[申請內容]LGD選用模型
//				}
				
				boolean isMortgageLoan = false;
				
				if(isDomesticCntrNo){
					
					isMortgageLoan = UtilConstants.L140S02AModelKind.房貸.equals(l140m01a.getExpectModelKind()) ? true : false;
				}
				else{
					isMortgageLoan = this.isAllPurchaseOrRepairSubject(this.clsService.findL140M01C(l140m01a));
				}
				
				if(isMortgageLoan && (l140m01a.getApprovedPercent() == null || l140m01a.getApprovedPercent().compareTo(BigDecimal.ZERO) <= 0)) {
					ltvCntrnoList.add(l140m01a.getCntrNo());
				}
			}
		}

		if(!ltvCntrnoList.isEmpty()){
			// 額度序號{0}為房貸評等，額度明細表頁籤 → 擔保品頁籤 → 擔保品內容頁籤 → (不動產) 核貸成數欄位需填寫！
			return MessageFormat.format(prop.getProperty("L140M01A.msg.error.mortgageModelApprovedPercentIsRequired"), StringUtils.join(ltvCntrnoList, "、"));
		}
		
		return "";
	}
	
}
