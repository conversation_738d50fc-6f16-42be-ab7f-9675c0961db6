/* 
 *CLS9021V00Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.fms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;

/**
 * <pre>
 * 優惠房貸額度維護-總額度維護作業
 * </pre>
 * 
 * @since 2012/11/01
 * <AUTHOR> Lo
 * @version <ul>
 *          <li>2012/11/01,Vector Lo,new
 *          </ul>
 */
@Controller
@RequestMapping(path = "/fms/cls9021v00")
public class CLS9021V00Page extends AbstractEloanInnerView {

	public CLS9021V00Page() {
		super();
	}

	@Override
	public void  execute(ModelMap model, PageParameters params) {
		// 加上Button
		//add(new SimpleButtonPanel("_buttonPanel", null, EloanButton));
		// UPGRADE: 後續沒有用到就可以刪掉
		// add(new Label("_buttonPanel"));

		renderJsI18N(CLS9021V00Page.class);

	}// ;

	public String[] getJavascriptPath() {

		return new String[] { "pagejs/fms/CLS9021V00Page.js" };
	}
}
