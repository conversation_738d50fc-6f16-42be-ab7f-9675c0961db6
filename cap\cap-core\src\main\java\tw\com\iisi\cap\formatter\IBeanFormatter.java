/* 
 * AbstractBeanFormatter.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package tw.com.iisi.cap.formatter;

/**
 * <pre>
 * reformat所傳入的Object為整個JavaBean
 * </pre>
 * 
 * @since 2011年8月8日
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2011年8月8日,iristu,new
 *          </ul>
 */
public interface IBeanFormatter extends IFormatter {

}
