/* 
 * L120S14B.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 本票資料檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L120S14B", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L120S14B extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 
	 * 文件編號<p/>
	 * 新產生時：getUUID()
	 */
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * 文件亂碼<p/>
	 * 每次儲存：getRandomCode()
	 */
	@Size(max=32)
	@Column(name="RANDOMCODE", length=32, columnDefinition="CHAR(32)")
	private String randomCode;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 刪除註記<p/>
	 * 文件刪除時使用(非立即性刪除)
	 */
	@Column(name="DELETEDTIME", columnDefinition="TIMESTAMP")
	private Timestamp deletedTime;

	/** 
	 * 支付日<p/>
	 * yyyy-MM-dd
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="PAYMENTDATE", columnDefinition="DATE")
	private Date paymentDate;

	/** 
	 * 支付金額<p/>
	 * 預設 現請額度
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="PAYMENTAMT", columnDefinition="DECIMAL(17,2)")
	private BigDecimal paymentAmt;

	/** 
	 * 付款分行<p/>
	 * 預設 分行ID
	 */
	@Size(max=3)
	@Column(name="PAYMENTBRID", length=3, columnDefinition="CHAR(3)")
	private String paymentBrId;

	/** 
	 * 付款分行地址<p/>
	 * 預設 分行地址
	 */
	@Size(max=192)
	@Column(name="PAYMENTADDR", length=192, columnDefinition="VARCHAR(192)")
	private String paymentAddr;

	/** 
	 * 發票日<p/>
	 * yyyy-MM-dd
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="DRAWINGDATE", columnDefinition="DATE")
	private Date drawingDate;

	/** 發票人 **/
	@Size(max=150)
	@Column(name="DRAWER_1", length=150, columnDefinition="VARCHAR(150)")
	private String drawer_1;

	/** 
	 * 發票人地址<p/>
	 * 預設 借款人地址
	 */
	@Size(max=192)
	@Column(name="DRAWERADDR_1", length=192, columnDefinition="VARCHAR(192)")
	private String drawerAddr_1;

	/** 發票人 **/
	@Size(max=150)
	@Column(name="DRAWER_2", length=150, columnDefinition="VARCHAR(150)")
	private String drawer_2;

	/** 發票人地址 **/
	@Size(max=192)
	@Column(name="DRAWERADDR_2", length=192, columnDefinition="VARCHAR(192)")
	private String drawerAddr_2;

	/** 發票人 **/
	@Size(max=150)
	@Column(name="DRAWER_3", length=150, columnDefinition="VARCHAR(150)")
	private String drawer_3;

	/** 發票人地址 **/
	@Size(max=192)
	@Column(name="DRAWERADDR_3", length=192, columnDefinition="VARCHAR(192)")
	private String drawerAddr_3;

	/** 發票人 **/
	@Size(max=150)
	@Column(name="DRAWER_4", length=150, columnDefinition="VARCHAR(150)")
	private String drawer_4;

	/** 發票人地址 **/
	@Size(max=192)
	@Column(name="DRAWERADDR_4", length=192, columnDefinition="VARCHAR(192)")
	private String drawerAddr_4;

	/** 發票人 **/
	@Size(max=150)
	@Column(name="DRAWER_5", length=150, columnDefinition="VARCHAR(150)")
	private String drawer_5;

	/** 發票人地址 **/
	@Size(max=192)
	@Column(name="DRAWERADDR_5", length=192, columnDefinition="VARCHAR(192)")
	private String drawerAddr_5;

	/** 發票人 **/
	@Size(max=150)
	@Column(name="DRAWER_6", length=150, columnDefinition="VARCHAR(150)")
	private String drawer_6;

	/** 發票人地址 **/
	@Size(max=192)
	@Column(name="DRAWERADDR_6", length=192, columnDefinition="VARCHAR(192)")
	private String drawerAddr_6;

	/** 發票人 **/
	@Size(max=150)
	@Column(name="DRAWER_7", length=150, columnDefinition="VARCHAR(150)")
	private String drawer_7;

	/** 發票人地址 **/
	@Size(max=192)
	@Column(name="DRAWERADDR_7", length=192, columnDefinition="VARCHAR(192)")
	private String drawerAddr_7;

	/** 發票人 **/
	@Size(max=150)
	@Column(name="DRAWER_8", length=150, columnDefinition="VARCHAR(150)")
	private String drawer_8;

	/** 發票人地址 **/
	@Size(max=192)
	@Column(name="DRAWERADDR_8", length=192, columnDefinition="VARCHAR(192)")
	private String drawerAddr_8;

	/** 發票人 **/
	@Size(max=150)
	@Column(name="DRAWER_9", length=150, columnDefinition="VARCHAR(150)")
	private String drawer_9;

	/** 發票人地址 **/
	@Size(max=192)
	@Column(name="DRAWERADDR_9", length=192, columnDefinition="VARCHAR(192)")
	private String drawerAddr_9;

    /** 額度序號 **/
    @Column(name = "CNTRNO", length = 12, columnDefinition = "CHAR(12)")
    private String cntrNo;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 
	 * 取得文件編號<p/>
	 * 新產生時：getUUID()
	 */
	public String getMainId() {
		return this.mainId;
	}
	/**
	 *  設定文件編號<p/>
	 *  新產生時：getUUID()
	 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得文件亂碼<p/>
	 * 每次儲存：getRandomCode()
	 */
	public String getRandomCode() {
		return this.randomCode;
	}
	/**
	 *  設定文件亂碼<p/>
	 *  每次儲存：getRandomCode()
	 **/
	public void setRandomCode(String value) {
		this.randomCode = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 
	 * 取得刪除註記<p/>
	 * 文件刪除時使用(非立即性刪除)
	 */
	public Timestamp getDeletedTime() {
		return this.deletedTime;
	}
	/**
	 *  設定刪除註記<p/>
	 *  文件刪除時使用(非立即性刪除)
	 **/
	public void setDeletedTime(Timestamp value) {
		this.deletedTime = value;
	}

	/** 
	 * 取得支付日<p/>
	 * yyyy-MM-dd
	 */
	public Date getPaymentDate() {
		return this.paymentDate;
	}
	/**
	 *  設定支付日<p/>
	 *  yyyy-MM-dd
	 **/
	public void setPaymentDate(Date value) {
		this.paymentDate = value;
	}

	/** 
	 * 取得支付金額<p/>
	 * 預設 現請額度
	 */
	public BigDecimal getPaymentAmt() {
		return this.paymentAmt;
	}
	/**
	 *  設定支付金額<p/>
	 *  預設 現請額度
	 **/
	public void setPaymentAmt(BigDecimal value) {
		this.paymentAmt = value;
	}

	/** 
	 * 取得付款分行<p/>
	 * 預設 分行ID
	 */
	public String getPaymentBrId() {
		return this.paymentBrId;
	}
	/**
	 *  設定付款分行<p/>
	 *  預設 分行ID
	 **/
	public void setPaymentBrId(String value) {
		this.paymentBrId = value;
	}

	/** 
	 * 取得付款分行地址<p/>
	 * 預設 分行地址
	 */
	public String getPaymentAddr() {
		return this.paymentAddr;
	}
	/**
	 *  設定付款分行地址<p/>
	 *  預設 分行地址
	 **/
	public void setPaymentAddr(String value) {
		this.paymentAddr = value;
	}

	/** 
	 * 取得發票日<p/>
	 * yyyy-MM-dd
	 */
	public Date getDrawingDate() {
		return this.drawingDate;
	}
	/**
	 *  設定發票日<p/>
	 *  yyyy-MM-dd
	 **/
	public void setDrawingDate(Date value) {
		this.drawingDate = value;
	}

	/** 取得發票人 **/
	public String getDrawer_1() {
		return this.drawer_1;
	}
	/** 設定發票人 **/
	public void setDrawer_1(String value) {
		this.drawer_1 = value;
	}

	/** 
	 * 取得發票人地址<p/>
	 * 預設 借款人地址
	 */
	public String getDrawerAddr_1() {
		return this.drawerAddr_1;
	}
	/**
	 *  設定發票人地址<p/>
	 *  預設 借款人地址
	 **/
	public void setDrawerAddr_1(String value) {
		this.drawerAddr_1 = value;
	}

	/** 取得發票人 **/
	public String getDrawer_2() {
		return this.drawer_2;
	}
	/** 設定發票人 **/
	public void setDrawer_2(String value) {
		this.drawer_2 = value;
	}

	/** 取得發票人地址 **/
	public String getDrawerAddr_2() {
		return this.drawerAddr_2;
	}
	/** 設定發票人地址 **/
	public void setDrawerAddr_2(String value) {
		this.drawerAddr_2 = value;
	}

	/** 取得發票人 **/
	public String getDrawer_3() {
		return this.drawer_3;
	}
	/** 設定發票人 **/
	public void setDrawer_3(String value) {
		this.drawer_3 = value;
	}

	/** 取得發票人地址 **/
	public String getDrawerAddr_3() {
		return this.drawerAddr_3;
	}
	/** 設定發票人地址 **/
	public void setDrawerAddr_3(String value) {
		this.drawerAddr_3 = value;
	}

	/** 取得發票人 **/
	public String getDrawer_4() {
		return this.drawer_4;
	}
	/** 設定發票人 **/
	public void setDrawer_4(String value) {
		this.drawer_4 = value;
	}

	/** 取得發票人地址 **/
	public String getDrawerAddr_4() {
		return this.drawerAddr_4;
	}
	/** 設定發票人地址 **/
	public void setDrawerAddr_4(String value) {
		this.drawerAddr_4 = value;
	}

	/** 取得發票人 **/
	public String getDrawer_5() {
		return this.drawer_5;
	}
	/** 設定發票人 **/
	public void setDrawer_5(String value) {
		this.drawer_5 = value;
	}

	/** 取得發票人地址 **/
	public String getDrawerAddr_5() {
		return this.drawerAddr_5;
	}
	/** 設定發票人地址 **/
	public void setDrawerAddr_5(String value) {
		this.drawerAddr_5 = value;
	}

	/** 取得發票人 **/
	public String getDrawer_6() {
		return this.drawer_6;
	}
	/** 設定發票人 **/
	public void setDrawer_6(String value) {
		this.drawer_6 = value;
	}

	/** 取得發票人地址 **/
	public String getDrawerAddr_6() {
		return this.drawerAddr_6;
	}
	/** 設定發票人地址 **/
	public void setDrawerAddr_6(String value) {
		this.drawerAddr_6 = value;
	}

	/** 取得發票人 **/
	public String getDrawer_7() {
		return this.drawer_7;
	}
	/** 設定發票人 **/
	public void setDrawer_7(String value) {
		this.drawer_7 = value;
	}

	/** 取得發票人地址 **/
	public String getDrawerAddr_7() {
		return this.drawerAddr_7;
	}
	/** 設定發票人地址 **/
	public void setDrawerAddr_7(String value) {
		this.drawerAddr_7 = value;
	}

	/** 取得發票人 **/
	public String getDrawer_8() {
		return this.drawer_8;
	}
	/** 設定發票人 **/
	public void setDrawer_8(String value) {
		this.drawer_8 = value;
	}

	/** 取得發票人地址 **/
	public String getDrawerAddr_8() {
		return this.drawerAddr_8;
	}
	/** 設定發票人地址 **/
	public void setDrawerAddr_8(String value) {
		this.drawerAddr_8 = value;
	}

	/** 取得發票人 **/
	public String getDrawer_9() {
		return this.drawer_9;
	}
	/** 設定發票人 **/
	public void setDrawer_9(String value) {
		this.drawer_9 = value;
	}

	/** 取得發票人地址 **/
	public String getDrawerAddr_9() {
		return this.drawerAddr_9;
	}
	/** 設定發票人地址 **/
	public void setDrawerAddr_9(String value) {
		this.drawerAddr_9 = value;
	}

    /** 取得額度序號 **/
    public String getCntrNo() {
        return this.cntrNo;
    }

    /** 設定額度序號 **/
    public void setCntrNo(String value) {
        if (value != null) {
            // 一率轉大寫額度序號
            value = value.toUpperCase();
        }
        this.cntrNo = value;
    }
}
