/* 
 * C122S01E.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 線上貸款申貸資料查詢紀錄 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "C122S01E", uniqueConstraints = @UniqueConstraint(columnNames = { "mainId" }))
public class C122S01E extends GenericBean implements IDataObject, IDocObject {
	/*
	 * J-110-0395 增加線上貸款申貸資料查詢紀錄
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 申貸項目{'H':'房貸增貸_網銀', 'C':'線上信貸_雲端櫃台(卡友信貸)', 'B':'勞工紓困',
	 * 'D':'勞工紓困非e-Loan線上進件' 'P':'線上信貸_PLOAN系統_主借人','Q':'線上信貸_PLOAN系統_從債務人',
	 * 'E':'線上房貸_PLOAN系統', 'F':'線上房貸_PLOAN系統_從債務人' }
	 **/
	@Column(name = "APPLYKIND", length = 1, columnDefinition = "CHAR(1)")
	private String applyKind;

	/** 使用者IP位置 **/
	@Size(max = 32)
	@Column(name = "USERIP", length = 15, columnDefinition = "VARCHAR(15)")
	private String userIp;

	/** 使用者員工編號 **/
	@Size(max = 32)
	@Column(name = "USERID", length = 6, columnDefinition = "VARCHAR(6)")
	private String userId;

	/** 查詢理由代號 
	 * C122S01E_queryReason
	 * 
	 * **/
	@Size(max = 32)
	@Column(name = "QUERYREASON", length = 2, columnDefinition = "VARCHAR(2)")
	private String queryReason;

	/** 查詢時間 **/
	@Column(name = "QUERYTIME", columnDefinition = "TIMESTAMP")
	private Timestamp queryTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	public String getApplyKind() {
		return applyKind;
	}

	public void setApplyKind(String applyKind) {
		this.applyKind = applyKind;
	}

	public String getUserIp() {
		return userIp;
	}

	public void setUserIp(String userIp) {
		this.userIp = userIp;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getQueryReason() {
		return queryReason;
	}

	public void setQueryReason(String queryReason) {
		this.queryReason = queryReason;
	}

	public Timestamp getQueryTime() {
		return queryTime;
	}

	public void setQueryTime(Timestamp queryTime) {
		this.queryTime = queryTime;
	}

}
