/*
 * L120S23A.java
 *
 * Copyright (c) 2011-2012 JC Software Services, Inc.
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 *
 * Licensed Materials - Property of JC Software Services, Inc.
 *
 * This software is confidential and proprietary information of
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;


import java.math.BigDecimal;
import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;


/** RWA風險性資產資料檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L120S23A", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L120S23A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/**
	 * 文件編號<p/>
	 * L120M01A.mainId
	 */
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/**
	 * 本案授信戶統編<p/>
	 * 資料來源：額度明細表主檔
	 */
	@Size(max=10)
	@Column(name="RWACUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String rwaCustId;

	/**
	 * 本案授信戶重複序號<p/>
	 * 資料來源：額度明細表主檔
	 */
	@Size(max=1)
	@Column(name="RWADUPNO", length=1, columnDefinition="CHAR(1)")
	private String rwaDupNo;

	/**
	 * 本案授信戶額度序號<p/>
	 * 資料來源：額度明細表主檔
	 */
	@Size(max=12)
	@Column(name="RWACNTRNO", length=12, columnDefinition="CHAR(12)")
	private String rwaCntrNo;

	/**
	 * 性質<p/>
	 * 資料來源：額度明細表主檔
	 */
	@Size(max=30)
	@Column(name="RWAPROPERTY", length=30, columnDefinition="VARCHAR(30)")
	private String rwaProPerty;

	/**
	 * 主要動用幣別<p/>
	 * 資料來源：額度明細表主檔<br/>
	 *  現請額度幣別
	 */
	@Size(max=3)
	@Column(name="RWAAPPLYCURR", length=3, columnDefinition="CHAR(3)")
	private String rwaApplyCurr;

	/**
	 * 新做/增額之額度<p/>
	 * 資料來源：額度明細表主檔單位：元<br/>
	 *  新做：currentApplyAmt現請額度增額：以”現請額度幣別”為主currentApplyAmt現請額度- LV2Amt前准批覆授信額度
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="RWAAPPLYAMT", columnDefinition="DECIMAL(17,2)")
	private BigDecimal rwaApplyAmt;

	/**
	 * 匯率<p/>
	 * 額度幣別兌台幣匯率
	 */
	@Digits(integer=12, fraction=8, groups = Check.class)
	@Column(name="TOTWDRATE", columnDefinition="DECIMAL(12,8)")
	private BigDecimal toTwdRate;

	/**
	 * 抵減後風險權數<p/>
	 * 資料來源：額度明細表主檔<br/>
	 *  單位：%
	 */
	@Digits(integer=15, fraction=2, groups = Check.class)
	@Column(name="RWARITEMD", columnDefinition="DECIMAL(15,2)")
	private BigDecimal rwaRItemD;

	/** 授信收益率 **/
	@Digits(integer=8, fraction=5, groups = Check.class)
	@Column(name="INCOMERATE", columnDefinition="DECIMAL(8,5)")
	private BigDecimal incomeRate;

	/** 授信天期對應之FTP **/
	@Digits(integer=8, fraction=5, groups = Check.class)
	@Column(name="FTPRATE", columnDefinition="DECIMAL(8,5)")
	private BigDecimal ftpRate;

	/**
	 * 利差<p/>
	 * 授信收益率-FTP
	 */
	@Digits(integer=8, fraction=5, groups = Check.class)
	@Column(name="RATESPREAD", columnDefinition="DECIMAL(8,5)")
	private BigDecimal rateSpread;

	/**
	 * 加權風險性資產RWA_原幣<p/>
	 * 額度*抵減後風險權數
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="RWA", columnDefinition="DECIMAL(17,2)")
	private BigDecimal rwa;

	/**
	 * 加權風險性資產RWA_新台幣<p/>
	 * 額度*抵減後風險權數*匯率
	 */
	@Digits(integer=20, fraction=5, groups = Check.class)
	@Column(name="RWATWD", columnDefinition="DECIMAL(20,5)")
	private BigDecimal rwaTwd;

	/**
	 * RWA授信報酬率<br/>(RORWA)<p/>
	 * 利差/抵減後風險權數<br/>
	 * (額度*利差)/額度*權數=利潤/風險性資產
	 */
	@Digits(integer=10, fraction=5, groups = Check.class)
	@Column(name="RORWA", columnDefinition="DECIMAL(10,5)")
	private BigDecimal rorwa;

	/** 新台幣RWA合計數 **/
	@Digits(integer=20, fraction=5, groups = Check.class)
	@Column(name="RWATWDTOT", columnDefinition="DECIMAL(20,5)")
	private BigDecimal rwaTwdTot;

	/**
	 * 新台幣加權RWA授信報酬率<p/>
	 * Weighted Rorwa
	 */
	@Digits(integer=10, fraction=5, groups = Check.class)
	@Column(name="WRORWATWD", columnDefinition="DECIMAL(10,5)")
	private BigDecimal wRorwaTwd;

	/**
	 * 檢視結果<p/>
	 * Y：需預約<br/>
	 *  N：不需<br/>
	 *  X：未完成計算
	 */
	@Size(max=1)
	@Column(name="RWARESULT", length=1, columnDefinition="VARCHAR(1)")
	private String rwaResult;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/**
	 * 取得文件編號<p/>
	 * L120M01A.mainId
	 */
	public String getMainId() {
		return this.mainId;
	}
	/**
	 *  設定文件編號<p/>
	 *  L120M01A.mainId
	 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得本案授信戶統編<p/>
	 * 資料來源：額度明細表主檔
	 */
	public String getRwaCustId() {
		return this.rwaCustId;
	}
	/**
	 *  設定本案授信戶統編<p/>
	 *  資料來源：額度明細表主檔
	 **/
	public void setRwaCustId(String value) {
		this.rwaCustId = value;
	}

	/**
	 * 取得本案授信戶重複序號<p/>
	 * 資料來源：額度明細表主檔
	 */
	public String getRwaDupNo() {
		return this.rwaDupNo;
	}
	/**
	 *  設定本案授信戶重複序號<p/>
	 *  資料來源：額度明細表主檔
	 **/
	public void setRwaDupNo(String value) {
		this.rwaDupNo = value;
	}

	/**
	 * 取得本案授信戶額度序號<p/>
	 * 資料來源：額度明細表主檔
	 */
	public String getRwaCntrNo() {
		return this.rwaCntrNo;
	}
	/**
	 *  設定本案授信戶額度序號<p/>
	 *  資料來源：額度明細表主檔
	 **/
	public void setRwaCntrNo(String value) {
		this.rwaCntrNo = value;
	}

	/**
	 * 取得性質<p/>
	 * 資料來源：額度明細表主檔
	 */
	public String getRwaProPerty() {
		return this.rwaProPerty;
	}
	/**
	 *  設定性質<p/>
	 *  資料來源：額度明細表主檔
	 **/
	public void setRwaProPerty(String value) {
		this.rwaProPerty = value;
	}

	/**
	 * 取得主要動用幣別<p/>
	 * 資料來源：額度明細表主檔<br/>
	 *  現請額度幣別
	 */
	public String getRwaApplyCurr() {
		return this.rwaApplyCurr;
	}
	/**
	 *  設定主要動用幣別<p/>
	 *  資料來源：額度明細表主檔<br/>
	 *  現請額度幣別
	 **/
	public void setRwaApplyCurr(String value) {
		this.rwaApplyCurr = value;
	}

	/**
	 * 取得新做/增額之額度<p/>
	 * 資料來源：額度明細表主檔單位：元<br/>
	 *  新做：currentApplyAmt現請額度增額：以”現請額度幣別”為主currentApplyAmt現請額度- LV2Amt前准批覆授信額度
	 */
	public BigDecimal getRwaApplyAmt() {
		return this.rwaApplyAmt;
	}
	/**
	 *  設定新做/增額之額度<p/>
	 *  資料來源：額度明細表主檔單位：元<br/>
	 *  新做：currentApplyAmt現請額度增額：以”現請額度幣別”為主currentApplyAmt現請額度- LV2Amt前准批覆授信額度
	 **/
	public void setRwaApplyAmt(BigDecimal value) {
		this.rwaApplyAmt = value;
	}

	/**
	 * 取得匯率<p/>
	 * 額度幣別兌台幣匯率
	 */
	public BigDecimal getToTwdRate() {
		return this.toTwdRate;
	}
	/**
	 *  設定匯率<p/>
	 *  額度幣別兌台幣匯率
	 **/
	public void setToTwdRate(BigDecimal value) {
		this.toTwdRate = value;
	}

	/**
	 * 取得抵減後風險權數<p/>
	 * 資料來源：額度明細表主檔<br/>
	 *  單位：%
	 */
	public BigDecimal getRwaRItemD() {
		return this.rwaRItemD;
	}
	/**
	 *  設定抵減後風險權數<p/>
	 *  資料來源：額度明細表主檔<br/>
	 *  單位：%
	 **/
	public void setRwaRItemD(BigDecimal value) {
		this.rwaRItemD = value;
	}

	/** 取得授信收益率 **/
	public BigDecimal getIncomeRate() {
		return this.incomeRate;
	}
	/** 設定授信收益率 **/
	public void setIncomeRate(BigDecimal value) {
		this.incomeRate = value;
	}

	/** 取得授信天期對應之FTP **/
	public BigDecimal getFtpRate() {
		return this.ftpRate;
	}
	/** 設定授信天期對應之FTP **/
	public void setFtpRate(BigDecimal value) {
		this.ftpRate = value;
	}

	/**
	 * 取得利差<p/>
	 * 授信收益率-FTP
	 */
	public BigDecimal getRateSpread() {
		return this.rateSpread;
	}
	/**
	 *  設定利差<p/>
	 *  授信收益率-FTP
	 **/
	public void setRateSpread(BigDecimal value) {
		this.rateSpread = value;
	}

	/**
	 * 取得加權風險性資產RWA_原幣<p/>
	 * 額度*抵減後風險權數
	 */
	public BigDecimal getRwa() {
		return this.rwa;
	}
	/**
	 *  設定加權風險性資產RWA_原幣<p/>
	 *  額度*抵減後風險權數
	 **/
	public void setRwa(BigDecimal value) {
		this.rwa = value;
	}

	/**
	 * 取得加權風險性資產RWA_新台幣<p/>
	 * 額度*抵減後風險權數*匯率
	 */
	public BigDecimal getRwaTwd() {
		return this.rwaTwd;
	}
	/**
	 *  設定加權風險性資產RWA_新台幣<p/>
	 *  額度*抵減後風險權數*匯率
	 **/
	public void setRwaTwd(BigDecimal value) {
		this.rwaTwd = value;
	}

	/**
	 * 取得RWA授信報酬率<br/>(RORWA)<p/>
	 * 利差/抵減後風險權數<br/>
	 * (額度*利差)/額度*權數=利潤/風險性資產
	 */
	public BigDecimal getRorwa() {
		return this.rorwa;
	}
	/**
	 *  設定RWA授信報酬率<br/>(RORWA)<p/>
	 *  利差/抵減後風險權數<br/>
	 *  (額度*利差)/額度*權數=利潤/風險性資產
	 **/
	public void setRorwa(BigDecimal value) {
		this.rorwa = value;
	}

	/** 取得新台幣RWA合計數 **/
	public BigDecimal getRwaTwdTot() {
		return this.rwaTwdTot;
	}
	/** 設定新台幣RWA合計數 **/
	public void setRwaTwdTot(BigDecimal value) {
		this.rwaTwdTot = value;
	}

	/**
	 * 取得新台幣加權RWA授信報酬率<p/>
	 * Weighted Rorwa
	 */
	public BigDecimal getWRorwaTwd() {
		return this.wRorwaTwd;
	}
	/**
	 *  設定新台幣加權RWA授信報酬率<p/>
	 *  Weighted Rorwa
	 **/
	public void setWRorwaTwd(BigDecimal value) {
		this.wRorwaTwd = value;
	}

	/**
	 * 取得檢視結果<p/>
	 * Y：需預約<br/>
	 *  N：不需<br/>
	 *  X：未完成計算
	 */
	public String getRwaResult() {
		return this.rwaResult;
	}
	/**
	 *  設定檢視結果<p/>
	 *  Y：需預約<br/>
	 *  N：不需<br/>
	 *  X：未完成計算
	 **/
	public void setRwaResult(String value) {
		this.rwaResult = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
