var initDfd = $.Deferred(), inits = {
    fhandle: "lms1505m01formhandler",
    ghandle: "lms1505gridhandler"
};

//UPGRADE：刪除序列化後產生的無用文章內容(in CKEditor)
const GARBAGE_RX = /figure\{display:block\}figure\.table table\{[^}]+}figure\.table td\{[^}]+}/i;

function cleanEditorData(html) {
    return html.replace(GARBAGE_RX, '').trimStart();
}

function clean(html=''){
    return html.replace(GARBAGE_RX, '').trimStart();
}

$(function(){
	var auth = (typeof(responseJSON)!="undefined" ? responseJSON.Auth : {}); //權限
    $.form.init({
        formHandler: inits.fhandle,
        formPostData: {
            formAction: "queryL150m01a"
        },
        loadSuccess: function(json){
			
			Object.keys(json).forEach(k=>{
			    if (typeof json[k] === 'string')
			        json[k] = clean(json[k]);
			});

			$('body').injectData(json);
			
            initDfd.resolve(json);
            
            if (auth.readOnly) {
                $("form").lockDoc();
            }
            
        }
    });
    
    var btn = $("#buttonPanel");
    btn.find("#btnSave").click(function(showMsg){
        btSave(true);
    }).end().find("#btnDelete").click(function(){
        //confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                $.ajax({
                    handler: inits.fhandle,
                    data: {
                        formAction: "delete",
                        mainOid: $("#oid").val()
                    }
                }).done(function(obj){
					    CommonAPI.triggerOpener("gridview", "reloadGrid");
				});
            }
        });
    }).end().find("#btnPrint").click(function(){
    
        if (auth.readOnly || _openerLockDoc == "1") {
            btnPrint();
        } else {
            //saveBeforePrint=執行列印將自動儲存資料，是否繼續此動作? 
            CommonAPI.confirmMessage(i18n.def["saveBeforePrint"], function(b){
                if (b) {
                    btSave(false, btnPrint);
                }
            });
        }
        
        
    }).end().find("#btnExit").click(function(){
        setCloseConfirm(false);
    });
    
    //列印動作
    function btnPrint(){
        $.form.submit({
            url: "../../simple/FileProcessingService",
            target: "_blank",
            data: {
                mainId: responseJSON.mainId,
                mainOid: $("#oid").val(),
                fileDownloadName: "lms505r01.pdf",
                serviceName: "lms1505r01rptservice"
            }
        });
    }
    
    
    //按鈕的儲存動作
    function btSave(showMsg, tofn){
		if (!$("#L150M01AForm").valid()) return;
		
			//同步並清理CKEditor欄位避免格式錯誤顯示
			$('.ck-editor__editable').each(function(){
			    const editor = $(this).data('ckeditor');
			    if (editor) {
			        let data = editor.getData();
			        data     = cleanEditorData(data);
			        editor.setData(data);
			        editor.updateSourceElement();
			    }
			});

			// 先把表單序列化為物件
			const formObj = $("#L150M01AForm").serializeData();
	
			// 把含 CKEditor 的欄位做過濾
			['content', 'memo', 'note'].forEach(field => {
			    if (formObj[field]) {
			        formObj[field] = cleanEditorData(formObj[field]);
			    }
			});
	
			const formJson = JSON.stringify(formObj);			

		    $.ajax({
		        handler: inits.fhandle,
		        localSave: true,
		        data: {
		            formAction: "saveL150m01a",
		            txCode: responseJSON.txCode,
		            page: responseJSON.page,
		            mainOid: $("#mainOid").val() || "",
		            showMsg: showMsg,
		            L150M01AForm: formJson
		        }
		    }).done(function(responseData){
		        if(!responseData || !responseData.mainOid){
		            return CommonAPI.showErrorMessage("儲存失敗，請確認表單資料是否完整");
		        }

				Object.keys(responseData).forEach(key => {
				    if (typeof responseData[key] === 'string') {
				        responseData[key] = responseData[key].replace(GARBAGE_RX, '').trimStart();
				    }
				});

		        $('body').injectData(responseData);
		        setRequiredSave(!$("#mainOid").val());

		        CommonAPI.triggerOpener("gridview", "reloadGrid");
		        if (!showMsg && typeof tofn === "function") {
		            tofn();
				   }
				}
			);
        }
    }
);

$.extend(window.tempSave, {
    handler : "lms1505m01formhandler",
    action  : "tempSave",
    beforeCheck: function(){ return $("#L150M01AForm").valid(); },
    sendData: function(){
        return $.extend({
            formAction    : "tempSave",
            txCode        : responseJSON.txCode,
            page          : responseJSON.page,
            mainOid       : $("#mainOid").val(),
            docUrl        : "/lms/lms1505m01",
            L150M01AForm  : JSON.stringify($("#L150M01AForm").serializeData())
        }, $("#L150M01AForm").serializeData());
    }
});

