package com.mega.eloan.lms.dc.action;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOCase;
import org.apache.commons.io.IOUtils;
import org.apache.commons.io.filefilter.FileFilterUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;

import com.mega.eloan.lms.dc.base.DCException;
import com.mega.eloan.lms.dc.util.Column;
import com.mega.eloan.lms.dc.util.TextDefine;
import com.mega.eloan.lms.dc.util.Util;

/**
 * <pre>
 * CreateLoadSQL : 產生SQL指令
 * </pre>
 * 
 * @since 2013/2/4
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/2/4,Bang,new
 *          <li>2013/03/07,UFO,取消local_var: dataBean，父類別取得
 *          </ul>
 */
public class CreateLoadSQL extends BaseAction {

	private String schema = "";
	private String logsDirPath = "";
	private String loadDB2DataPath = "";// load_db2 下data目錄所在位置路徑
	private String loadDB2ClobPath = "";// load_db2 下clob 目錄所在位置路徑
	private String dbType = "";
	private PrintWriter logsCs = null;// 輸出log

	/**
	 * 初始化必要資訊及執行產生SQL指令動作
	 * 
	 * @param dbType
	 *            String:連結的DB
	 * @param schema
	 *            String:連結的 schema
	 */
	public List<RowData> genSql(String dbType, String schema) {
		if (StringUtils.isBlank(schema)) {
			String errmsg = "讀取系統名稱錯誤,未指定要執行的系統名稱,請重新確認!";
			this.logger.error(errmsg);
			throw new DCException(errmsg);
		}
		this.dbType = dbType; // Ex:DELOANDB
		this.schema = schema; // Ex:LMS

		this.logger.info("正在初始化 CreateLoadSQL 必要資訊...");

		if (TextDefine.SCHEMA_LMS.equalsIgnoreCase(schema)) {
			// LMS輸出log
			this.logsDirPath = this.configData.getLmsLogsDirPath();// User當前工作目錄\log\logs\執行日期\LMS
			// User當前工作目錄\load_db2\執行日期\LMS\data
			this.loadDB2DataPath = this.configData.getLmsloadDB2DirPath()
					+ this.configData.getDataPath();
			// User當前工作目錄\load_db2\執行日期\LMS\clob
			this.loadDB2ClobPath = this.configData.getLmsloadDB2DirPath()
					+ this.configData.getClobPath();
		} else {
			// CLS輸出log
			this.logsDirPath = this.configData.getClsLogsDirPath();// User當前工作目錄\log\logs\執行日期\CLS
			// User當前工作目錄\load_db2\執行日期\CLS\data
			this.loadDB2DataPath = this.configData.getClsloadDB2DirPath()
					+ this.configData.getDataPath();
			// User當前工作目錄\load_db2\執行日期\CLS\clob
			this.loadDB2ClobPath = this.configData.getClsloadDB2DirPath()
					+ this.configData.getClobPath();
		}
		List<RowData> sqlList = null;
		try {
			final String LOG_ROOT = this.logsDirPath + File.separator
					+ "GEN_SQL";
			Util.checkDirExist(LOG_ROOT);

			String createSQLLogPath = LOG_ROOT + File.separator
					+ "createSQL.log";
			this.logsCs = new PrintWriter(new BufferedWriter(
					new OutputStreamWriter(new FileOutputStream(new File(
							createSQLLogPath)), TextDefine.ENCODING_MS950)),
					true);

			sqlList = this.run();
			this.logger.info(" CreateLoadSQL 執行結束...\n");
		} catch (Exception e) {
			this.logger.error("CreateLoadSQL 時產生錯誤! Exception: ", e);
			throw new DCException("CreateLoadSQL 時產生錯誤...", e);
		}
		return sqlList;
	}

	/**
	 * 主要執行程式
	 */
	public List<RowData> run() {
		this.logsCs.println(" CreateLoadSQL 起始時間 :" + Util.getNowTime());
		long tt1 = System.currentTimeMillis();
		List<RowData> dataList = new ArrayList<RowData>();
		try {
			Collection<File> files = getLoadDb2DirList(this.loadDB2DataPath);
			for (File file : files) {
				dataList = this.sqlGenerate(file, dataList);
			}
			BigDecimal bd = new BigDecimal(
					(System.currentTimeMillis() - tt1) / 1000 / 60);
			bd.setScale(0, BigDecimal.ROUND_HALF_UP);
			this.logsCs.println("\n CreateLoadSQL 結束時間 :" + Util.getNowTime()
					+ " ,TOTAL TIME :" + bd + " 分鐘");
		} catch (Exception e) {
			String errmsg = "執行CreateLoadSQL 之run步驟時產生錯誤!";
			this.logger.error(errmsg, e);
			this.logsCs.println(errmsg);
			e.printStackTrace(this.logsCs);
			throw new DCException(errmsg, e);
		} finally {
			IOUtils.closeQuietly(this.logsCs);
		}
		return dataList;
	}

	/**
	 * 取得load_db2資料夾下準備寫入DB,開頭為"系統名稱.TableName"的文字檔
	 * 
	 * @param loadDb2Path
	 * @return
	 */
	private Collection<File> getLoadDb2DirList(String loadDb2Path) {

		Collection<File> files = FileUtils.listFiles(new File(loadDb2Path),
		// 此處需直接寫死LMS
				FileFilterUtils.prefixFileFilter(TextDefine.SCHEMA_LMS,
						IOCase.INSENSITIVE), FileFilterUtils.suffixFileFilter(
						TextDefine.ATTACH_TXT, IOCase.INSENSITIVE));
		return files;
	}

	/**
	 * 組成SQL語法
	 * 
	 * @param file
	 *            String :當前讀取的文字檔
	 * @param dataList
	 *            List<String>:裝載已組成SQL語法之容器
	 * @return
	 */
	protected List<RowData> sqlGenerate(File file, List<RowData> dataList) {
		String table = file.getName();
		int firstIdx = table.indexOf(".");
		int lastIdx = table.lastIndexOf(".");
		String tableName = table.substring(firstIdx + 1, lastIdx);

		String rowCol = "";
		try {
			// 此處需寫死LMS
			ArrayList<Column> colList = ColumnGetDBInfoFactory.getInstance()
					.getInfo(this.dbType, TextDefine.SCHEMA_LMS, tableName);
			
			List<String> tmpList = FileUtils.readLines(file,
					TextDefine.ENCODING_MS950);
			for (String tmp : tmpList) {
				String[] data = Util.split(tmp, TextDefine.SYMBOL_SEMICOLON);
				StringBuffer sb = new StringBuffer();
				RowData rd = new RowData();
				sb.append("INSERT INTO ").append(table.substring(0, lastIdx))
						.append(" VALUES(");
				for (int i = 0; i < colList.size(); i++) {
					Column col = colList.get(i);
					rowCol = data[i];
					// 轉換SQL特殊字符 例:ＬＯＮＧＪＥＴ ＩＮＴ'Ｌ ＬＴ-->ＬＯＮＧＪＥＴ ＩＮＴ''Ｌ ＬＴ
					String tmpCol = StringEscapeUtils.escapeSql(rowCol);
					switch (col.getType()) {
					case CHARACTER:
						if (StringUtils.isBlank(rowCol)) {
							sb.append("''");
						} else {
							sb.append("'").append(tmpCol).append("'");
						}
						break;
					case VARCHAR:
						// 這裡不能用isBlank,否則L140M01I的Rid欄位值若為8個空白時會被誤判為空值
						if (StringUtils.isEmpty(rowCol)) {
							if ("LMS.BDOCFILE".equalsIgnoreCase(table
									.substring(0, lastIdx))
									&& "pid".equalsIgnoreCase(col.getName())) {
								sb.append("null");
							} else {
								sb.append("''");
							}
						} else {
							sb.append("'").append(tmpCol).append("'");
						}
						break;
					case DATE:
						if (StringUtils.isBlank(rowCol)) {
							sb.append("null");
						} else {
							sb.append("date('").append(tmpCol).append("')");
						}
						break;
					case TIMESTAMP:
						if (StringUtils.isBlank(rowCol)) {
							sb.append("null");
						} else {
							sb.append("timestamp('").append(tmpCol)
									.append("')");
						}
						break;
					case INTEGER:
						if (StringUtils.isBlank(rowCol)) {
							sb.append("null");
						} else {
							sb.append(tmpCol);
						}
						break;
					case DECIMAL:
						if (StringUtils.isBlank(rowCol)) {
							sb.append("null");
						} else {
							sb.append(tmpCol);
						}
						break;
					case CLOB:
						sb.append("?");

						rd.setClobTb(true);
						if (StringUtils.isNotBlank(rowCol)) {
							// 讀檔後將內容存入
							if (table.substring(0, lastIdx).equalsIgnoreCase(
									"LMS.L140M01B")
									|| table.substring(0, lastIdx)
											.equalsIgnoreCase("LMS.L140M01F")) {
								String str = FileUtils
										.readFileToString(
												new File(this.loadDB2ClobPath
														+ File.separator
														+ tmpCol),
												this.configData.isOnlineMode() ? TextDefine.ENCODING_UTF8
														: TextDefine.ENCODING_MS950);
								rd.setClobString(str.trim());
							} else {
								rd.setClobString(tmpCol);
							}

						}
						break;
					default:
						break;
					}
					if (i != colList.size() - 1) {
						sb.append(",");
					}
				}
				sb.append(")");
				rd.setSql(sb.toString());
				dataList.add(rd);
				this.logsCs.println(rd);
			}
			return dataList;
		} catch (Exception e) {
			String errmsg = "組成SQL指令時產生錯誤...dbType:" + this.dbType
					+ " ,schema :" + this.schema + " ,table :" + table
					+ " ,file :" + file.getName() + " 執行欄位值: " + rowCol;
			throw new DCException(errmsg, e);
		}
	}

}
