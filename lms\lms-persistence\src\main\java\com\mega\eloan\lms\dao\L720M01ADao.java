/* 
 * L720M01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L720M01A;

/** 使用者自訂表格範本檔 **/
public interface L720M01ADao extends IGenericDao<L720M01A> {

	L720M01A findByOid(String oid);
	
	List<L720M01A> findByMainId(String mainId);
	
	L720M01A findByUniqueKey(String patternNM);

	List<L720M01A> findByIndex01(String patternNM);
}