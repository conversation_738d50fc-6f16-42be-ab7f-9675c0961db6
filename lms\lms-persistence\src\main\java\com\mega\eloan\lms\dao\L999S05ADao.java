/* 
 * L999S05ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L999S05A;


/** 股東債權同意書檔 **/
public interface L999S05ADao extends IGenericDao<L999S05A> {

	L999S05A findByOid(String oid);

	List<L999S05A> findByMainId(String mainId);
	
	List<L999S05A> findByCustIdDupId(String custId,String DupNo);
}