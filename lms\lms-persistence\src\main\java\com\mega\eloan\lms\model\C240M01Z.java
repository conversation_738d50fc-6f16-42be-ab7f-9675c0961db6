/* 
 * C240M01Z.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 每月覆審資料更新記錄檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C240M01Z", uniqueConstraints = @UniqueConstraint(columnNames = {"dataDate","branchId"}))
public class C240M01Z extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;
	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 
	 * 資料日期<p/>
	 * DW_ELF490OVS.CYC_MN
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="DATADATE", columnDefinition="DATE")
	private Date dataDate;

	/** 
	 * 分行別<p/>
	 * DW_ELF490OVS.BR_NO
	 */
	@Column(name="BRANCHID", length=3, columnDefinition="CHAR(3)")
	private String branchId;

	/** 更新完成時間 **/
	@Column(name="FINISHTIME", columnDefinition="TIMESTAMP")
	private Date finishTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 
	 * 取得資料日期<p/>
	 * DW_ELF490OVS.CYC_MN
	 */
	public Date getDataDate() {
		return this.dataDate;
	}
	/**
	 *  設定資料日期<p/>
	 *  DW_ELF490OVS.CYC_MN
	 **/
	public void setDataDate(Date value) {
		this.dataDate = value;
	}

	/** 
	 * 取得分行別<p/>
	 * DW_ELF490OVS.BR_NO
	 */
	public String getBranchId() {
		return this.branchId;
	}
	/**
	 *  設定分行別<p/>
	 *  DW_ELF490OVS.BR_NO
	 **/
	public void setBranchId(String value) {
		this.branchId = value;
	}

	/** 取得更新完成時間 **/
	public Date getFinishTime() {
		return this.finishTime;
	}
	/** 設定更新完成時間 **/
	public void setFinishTime(Date value) {
		this.finishTime = value;
	}
	
	public String getMainId() {
		return null;
	}
}
