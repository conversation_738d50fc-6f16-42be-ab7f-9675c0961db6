package com.mega.eloan.lms.lns.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;

import com.mega.eloan.lms.base.panels.LMSS08APanel01;
import com.mega.eloan.lms.base.panels.LMSS08APanel02;
import com.mega.eloan.lms.base.panels.LMSS08CPanel;
import com.mega.eloan.lms.base.panels.LMSS08DPanel;
import com.mega.eloan.lms.base.panels.LMSS08EPanel;
import com.mega.eloan.lms.base.panels.LMSS08FPanel;
import com.mega.eloan.lms.base.panels.LMSS08HPanel;
import com.mega.eloan.lms.base.panels.LMSS08IPanel;
import com.mega.eloan.lms.base.panels.LMSS08JPanel;
import com.mega.eloan.lms.base.panels.LMSS08KPanel;
import com.mega.eloan.lms.base.panels.LMSS08MPanel;
import com.mega.eloan.lms.base.panels.LMSS08NPanel;
import com.mega.eloan.lms.base.panels.LMSS08OPanel;
import com.mega.eloan.lms.base.panels.LMSS08PPanel;

/**
 * <pre>
 * 相關文件(企金授權外)
 * </pre>
 * 
 * @since 2012/1/19
 * <AUTHOR> Lin
 * @version <ul>
 *          <li>2012/1/19,Miller Lin,new
 *          <li>2013/1/14,UFO,增加案件報告表Pannel
 *          </ul>
 */
public class LMSS08APanel extends Panel {

	/**
	 * 
	 */
	private static final long serialVersionUID = -4024257163623646201L;

	public LMSS08APanel(String id, boolean updatePanelName) {
		super(id, updatePanelName);

	}
	
	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);
		
		new LMSS08APanel01("lmss08a_panel").processPanelData(model, params);
		new LMSS08APanel02("lmss08b_panel").processPanelData(model, params);
		new LMSS08CPanel("lmss08c_panel").processPanelData(model, params);
		new LMSS08DPanel("lmss08d_panel").processPanelData(model, params);
		new LMSS08EPanel("lmss08e_panel").processPanelData(model, params);

		new LMSS08FPanel("lmss08f_panel").processPanelData(model, params);

		// J-110-0327_05097_B1001 Web e-Loan國內與海外授信簽報書新增額度檢視表
		new LMSS08HPanel("lmss08h_panel").processPanelData(model, params);

		// J-110-0386_05097_B1001 Web e-Loan國內與海外企金授信「相關文件」頁籤新增「授信額度主要敘做條件彙總表」
		new LMSS08IPanel("lmss08i_panel").processPanelData(model, params);

		// J-110-0368_11557_B1001 常董會/董事會提案稿所需之相關文件
		new LMSS08JPanel("lmss08j_panel").processPanelData(model, params);
		new LMSS08KPanel("lmss08k_panel").processPanelData(model, params);
		// J-112-0357 新增敘做條件異動比較表
		new LMSS08MPanel("lmss08m_panel").processPanelData(model, params);
		// J-112-0357 新增常董會討論事項提案檢核表
		new LMSS08NPanel("lmss08n_panel").processPanelData(model, params);
		// 授審會常董會合併列印
		new LMSS08OPanel("lmss08o_panel").processPanelData(model, params);
		new LMSS08PPanel("lmss08p_panel").processPanelData(model, params);
	}
}
