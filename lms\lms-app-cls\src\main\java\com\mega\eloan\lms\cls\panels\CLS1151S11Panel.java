/* 
 * CLS1151S11Panel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.panels;

import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * 個金額度明細表 - 總處核定
 * </pre>
 * 
 * @since 2013/06/10
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/06/10,REX,new
 *          </ul>
 */
public class CLS1151S11Panel extends Panel {

	private static final long serialVersionUID = 1L;

	/**
	 * @param id
	 */
	public CLS1151S11Panel(String id) {
		super(id);
	}
}
