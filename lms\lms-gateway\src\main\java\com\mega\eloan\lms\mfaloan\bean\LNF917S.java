package com.mega.eloan.lms.mfaloan.bean;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;

/** 地上權分戶利率調整通知檔 **/
public class LNF917S extends GenericBean {

	private static final long serialVersionUID = 1L;

	/** 資料產生日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "LNF917S_PROC_DATE", columnDefinition = "DATE", nullable=false,unique = true)
	private Date lnf917s_proc_date;

	/** 通知單FORM ID **/
	@Size(max = 8)
	@Column(name = "LNF917S_FORMID", length = 8, columnDefinition = "CHAR(8)", nullable=false,unique = true)
	private String lnf917s_formid;

	/** 放款帳號 **/
	@Size(max = 14)
	@Column(name = "LNF917S_LOAN_NO", length = 14, columnDefinition = "CHAR(14)", nullable=false,unique = true)
	private String lnf917s_loan_no;

	/** 戶別 **/
	@Size(max = 10)
	@Column(name = "LNF917S_LOAN_SEQ", length = 10, columnDefinition = "CHAR(10)")
	private String lnf917s_loan_seq;

	/** 客戶ID **/
	@Size(max = 11)
	@Column(name = "LNF917S_CUST_ID", length = 11, columnDefinition = "CHAR(11)")
	private String lnf917s_cust_id;

	/** 客戶姓名 **/
	@Size(max = 50)
	@Column(name = "LNF917S_CUST_NAME", length = 50, columnDefinition = "CHAR(50)")
	private String lnf917s_cust_name;

	/** 收件人姓名 **/
	@Size(max = 50)
	@Column(name = "LNF917S_MAIL_NAME", length = 50, columnDefinition = "CHAR(50)")
	private String lnf917s_mail_name;

	/** 通訊地址1 **/
	@Size(max = 82)
	@Column(name = "LNF917S_ADDR1", length = 82, columnDefinition = "CHAR(82)")
	private String lnf917s_addr1;

	/** 通訊地址2 **/
	@Size(max = 82)
	@Column(name = "LNF917S_ADDR2", length = 82, columnDefinition = "CHAR(82)")
	private String lnf917s_addr2;

	/** 利率調整日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "LNF917S_INT_DATE", columnDefinition = "DATE")
	private Date lnf917s_int_date;

	/** 利率代碼中文 **/
	@Size(max = 50)
	@Column(name = "LNF917S_INTCD_TXT", length = 50, columnDefinition = "CHAR(50)")
	private String lnf917s_intcd_txt;

	/** 調整前利率 **/
	@Column(name = "LNF917S_INT_RATE_O", columnDefinition = "DECIMAL(8,6)")
	private BigDecimal lnf917s_int_rate_o;

	/** 調整後利率 **/
	@Column(name = "LNF917S_INT_RATE_N", columnDefinition = "DECIMAL(8,6)")
	private BigDecimal lnf917s_int_rate_n;

	/** 建商名稱 **/
	@Size(max = 42)
	@Column(name = "LNF917S_GRP_NAME", length = 42, columnDefinition = "CHAR(42)")
	private String lnf917s_grp_name;

	/** 取得資料產生日期 **/
	public Date getLnf917s_proc_date() {
		return this.lnf917s_proc_date;
	}

	/** 設定資料產生日期 **/
	public void setLnf917s_proc_date(Date value) {
		this.lnf917s_proc_date = value;
	}

	/** 取得通知單FORM ID **/
	public String getLnf917s_formid() {
		return this.lnf917s_formid;
	}

	/** 設定通知單FORM ID **/
	public void setLnf917s_formid(String value) {
		this.lnf917s_formid = value;
	}

	/** 取得放款帳號 **/
	public String getLnf917s_loan_no() {
		return this.lnf917s_loan_no;
	}

	/** 設定放款帳號 **/
	public void setLnf917s_loan_no(String value) {
		this.lnf917s_loan_no = value;
	}

	/** 取得戶別 **/
	public String getLnf917s_loan_seq() {
		return this.lnf917s_loan_seq;
	}

	/** 設定戶別 **/
	public void setLnf917s_loan_seq(String value) {
		this.lnf917s_loan_seq = value;
	}

	/** 取得客戶ID **/
	public String getLnf917s_cust_id() {
		return this.lnf917s_cust_id;
	}

	/** 設定客戶ID **/
	public void setLnf917s_cust_id(String value) {
		this.lnf917s_cust_id = value;
	}

	/** 取得客戶姓名 **/
	public String getLnf917s_cust_name() {
		return this.lnf917s_cust_name;
	}

	/** 設定客戶姓名 **/
	public void setLnf917s_cust_name(String value) {
		this.lnf917s_cust_name = value;
	}

	/** 取得收件人姓名 **/
	public String getLnf917s_mail_name() {
		return this.lnf917s_mail_name;
	}

	/** 設定收件人姓名 **/
	public void setLnf917s_mail_name(String value) {
		this.lnf917s_mail_name = value;
	}

	/** 取得通訊地址1 **/
	public String getLnf917s_addr1() {
		return this.lnf917s_addr1;
	}

	/** 設定通訊地址1 **/
	public void setLnf917s_addr1(String value) {
		this.lnf917s_addr1 = value;
	}

	/** 取得通訊地址2 **/
	public String getLnf917s_addr2() {
		return this.lnf917s_addr2;
	}

	/** 設定通訊地址2 **/
	public void setLnf917s_addr2(String value) {
		this.lnf917s_addr2 = value;
	}

	/** 取得利率調整日 **/
	public Date getLnf917s_int_date() {
		return this.lnf917s_int_date;
	}

	/** 設定利率調整日 **/
	public void setLnf917s_int_date(Date value) {
		this.lnf917s_int_date = value;
	}

	/** 取得利率代碼中文 **/
	public String getLnf917s_intcd_txt() {
		return this.lnf917s_intcd_txt;
	}

	/** 設定利率代碼中文 **/
	public void setLnf917s_intcd_txt(String value) {
		this.lnf917s_intcd_txt = value;
	}

	/** 取得調整前利率 **/
	public BigDecimal getLnf917s_int_rate_o() {
		return this.lnf917s_int_rate_o;
	}

	/** 設定調整前利率 **/
	public void setLnf917s_int_rate_o(BigDecimal value) {
		this.lnf917s_int_rate_o = value;
	}

	/** 取得調整後利率 **/
	public BigDecimal getLnf917s_int_rate_n() {
		return this.lnf917s_int_rate_n;
	}

	/** 設定調整後利率 **/
	public void setLnf917s_int_rate_n(BigDecimal value) {
		this.lnf917s_int_rate_n = value;
	}

	/** 取得建商名稱 **/
	public String getLnf917s_grp_name() {
		return this.lnf917s_grp_name;
	}

	/** 設定建商名稱 **/
	public void setLnf917s_grp_name(String value) {
		this.lnf917s_grp_name = value;
	}
}
