var PanelAction07 = {
    isInit: false,
    /**
     *頁面初始化的動作
     * */
    initAction: function(){
        _M.initItem("07");
        this.initEvent();
        
    },
    /**
     *載入頁面後的動作
     * */
    afterAction: function(){
    },
    /**
     * 初始化事件
     */
    initEvent: function(){
        var $form = $("#CLS1151Form07");
        $form.find("[name=checkQNote][value=5]").click(function(){
            if (this.checked) {
                $form.find("[name=checkQNote][value!=5]").each(function(){
                    $(this).removeAttr("checked").attr("disabled", "disabled");
                });
            } else {
                $form.find("[name=checkQNote]").each(function(){
                    $(this).removeAttr("disabled");
                });
            }
        });
        $form.find("[name=checkQNote][value=4]").click(function(){
            if (this.checked) {
                $form.find("[name=checkQNote][value!=4]").each(function(){
                    $(this).removeAttr("checked").attr("disabled", "disabled");
                });
            } else {
                $form.find("[name=checkQNote]").each(function(){
                    $(this).removeAttr("disabled");
                });
            }
        });
        
        
        $form.find("[name=checkQNote][value=6]").click(function(){
            if (this.checked) {
                $form.find("[name=checkQNote][value!=6]").each(function(){
                    $(this).removeAttr("checked").attr("disabled", "disabled");
                });
                $form.find("#checkNote").show();
            } else {
                $form.find("[name=checkQNote]").each(function(){
                    $(this).removeAttr("disabled");
                });
                $form.find("#checkNote").hide();
				$form.find("#checkNote").val("");
            }
        });
    }
};

_M.pageInitAcion["07"] = PanelAction07;
