/*
 * IResult.java
 *
 * Copyright (c) 2009-2011 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
*/
package tw.com.iisi.cap.response;

import javax.servlet.ServletResponse;

import tw.com.iisi.cap.exception.CapException;

/**
 * IResult.<br>
 * 取得回應結果
 * 
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2010/11/23,iristu,new
 *          </ul>
 */
public interface IResult {

    /**
     * 取得回應結果
     * 
     * @return
     */
    String getResult();

    /**
     * 取得Log訊息
     */
    String getLogMessage();

    /**
     * 回應結果
     * 
     * @param response
     * @throws CapException
     */
    void respondResult(ServletResponse response) throws CapException;

    /**
     * 加入回應結果內容
     * 
     * @param result
     */
    void add(IResult result);

}
