/* 
 * L260M01BDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import com.mega.eloan.lms.model.C124M01B;
import tw.com.iisi.cap.dao.IGenericDao;

import java.util.List;

/** 勞工紓困信保整批貸款簽章欄檔 **/
public interface C124M01BDao extends IGenericDao<C124M01B> {

	C124M01B findByOid(String oid);
	
	List<C124M01B> findByMainId(String mainId);

	C124M01B findByUniqueKey(String mainId, String branchType, String branchId, String staffNo, String staffJob);

	List<C124M01B> findByIndex01(String mainId, String branchType, String branchId, String staffNo, String staffJob);
}