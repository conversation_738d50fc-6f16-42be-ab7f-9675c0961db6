package com.mega.eloan.lms.base.service;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.mega.eloan.lms.mfaloan.bean.DW_RKADJUST;
import com.mega.eloan.lms.mfaloan.bean.DW_RKAPPLICANT;
import com.mega.eloan.lms.mfaloan.bean.DW_RKAPPLICANT_N;
import com.mega.eloan.lms.mfaloan.bean.DW_RKCNTRNO;
import com.mega.eloan.lms.mfaloan.bean.DW_RKCREDIT;
import com.mega.eloan.lms.mfaloan.bean.DW_RKCREDIT_N;
import com.mega.eloan.lms.mfaloan.bean.DW_RKJCIC;
import com.mega.eloan.lms.mfaloan.bean.DW_RKPROJECT;
import com.mega.eloan.lms.mfaloan.bean.DW_RKSCORE;
import com.mega.eloan.lms.mfaloan.bean.DW_RKSCORE_N;
import com.mega.eloan.lms.mfaloan.bean.OTS_RKADJUSTOVS;
import com.mega.eloan.lms.mfaloan.bean.OTS_RKAPPLICANTOVS;
import com.mega.eloan.lms.mfaloan.bean.OTS_RKCNTRNOOVS;
import com.mega.eloan.lms.mfaloan.bean.OTS_RKCOLLOVS;
import com.mega.eloan.lms.mfaloan.bean.OTS_RKCREDITOVS;
import com.mega.eloan.lms.mfaloan.bean.OTS_RKJCICOVS;
import com.mega.eloan.lms.mfaloan.bean.OTS_RKPROJECTOVS;
import com.mega.eloan.lms.mfaloan.bean.OTS_RKSCOREOVS;
import com.mega.eloan.lms.model.C101S01G_N;
import com.mega.eloan.lms.model.C101S01Q_N;
import com.mega.eloan.lms.model.C101S01R_N;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.C120S01B;
import com.mega.eloan.lms.model.C120S01C;
import com.mega.eloan.lms.model.C120S01E;
import com.mega.eloan.lms.model.C120S01G;
import com.mega.eloan.lms.model.C120S01Q;
import com.mega.eloan.lms.model.C120S01R;
import com.mega.eloan.lms.model.C121M01A;
import com.mega.eloan.lms.model.C121S01A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01C;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.service.ICapService;

public interface CLSScoreUpDWService extends ICapService {
	
	public List<DW_RKSCORE> upDW_RKSCORE(List<DW_RKSCORE> data, L120M01A l120m01a,
			L140M01A l140m01a, String ACCT_KEY, String modelKind,
			String custPos, C120M01A c120m01a, C120S01G c120s01g,
			C120S01Q c120s01q, C120S01R c120s01r, String dDOCSTATUS);
	public List<DW_RKJCIC> upDW_RKJCIC(List<DW_RKJCIC> data, L120M01A l120m01a,
			L140M01A l140m01a, String ACCT_KEY, String modelKind,
			String lngeFlag, C120M01A c120m01a, C120S01G c120s01g,
			C120S01Q c120s01q, C120S01R c120s01r, String dDOCSTATUS);
	public List<DW_RKCREDIT> upDW_RKCREDIT(List<DW_RKCREDIT> data, L120M01A l120m01a,
			L140M01A l140m01a, String ACCT_KEY, String modelKind,
			String LNGEFLAG, C120M01A c120m01a, C120S01G c120s01g,
			String custId, String dupNo, String L140CustIdDupNo, String reason,
			String reasonOth, C120S01Q c120s01q, C120S01R c120s01r,
			String dDOCSTATUS, C120S01E c120s01e, boolean isShortPeriodCase);
	public List<DW_RKPROJECT> upDW_RKPROJECT(List<DW_RKPROJECT> data,
			L120M01A l120m01a, L140M01A l140m01a, String ACCT_KEY,
			String modelKind, String LNGEFLAG, C120M01A c120m01a,
			C120S01G c120s01g, C120S01Q c120s01q, C120S01R c120s01r,
			String dDOCSTATUS, String finalGrade);
	public List<DW_RKADJUST> upDW_RKADJUST(List<DW_RKADJUST> data, L120M01A l120m01a,
			L140M01A l140m01a, String ACCT_KEY, String modelKind,
			String LNGEFLAG, C120M01A c120m01a, C120S01G c120s01g,
			C120S01Q c120s01q, C120S01R c120s01r, String dDOCSTATUS);
	public List<DW_RKCNTRNO> upDW_RKCNTRNO(List<DW_RKCNTRNO> data, L120M01A l120m01a,
			L140M01A l140m01a, String ACCT_KEY, String modelKind,
			String LNGEFLAG, C120M01A c120m01a, C120S01G c120s01g,
			C120S01Q c120s01q, C120S01R c120s01r, String dDOCSTATUS,
			String loanCurr, BigDecimal loanAmt, String property,
			Integer lnYear, Integer lnMonth, String prodKind, String subjCode,
			String residential, String cntrNo);
	public List<DW_RKAPPLICANT> upDW_RKAPPLICANT(List<DW_RKAPPLICANT> data,
			L120M01A l120m01a, L140M01A l140m01a, String ACCT_KEY,
			String modelKind, String LNGEFLAG, C120M01A c120m01a,
			C120S01A c120s01a, C120S01B c120s01b, C120S01C c120s01c,
			C120S01G c120s01g, C120S01Q c120s01q, C120S01R c120s01r,
			String dDOCSTATUS);
	public List<DW_RKAPPLICANT> upDW_RKAPPLICANTforOBU(
			List<DW_RKAPPLICANT> data, L120M01A l120m01a, L140M01A l140m01a,
			C120M01A c120m01a, C120S01A c120s01a, C120S01B c120s01b,
			C120S01C c120s01c, C120S01E c120s01e);
	
	public List<DW_RKSCORE_N> upDW_RKSCORE_N(List<DW_RKSCORE_N> data,
			L120M01A l120m01a, L140M01A l140m01a, String ACCT_KEY,
			String modelKind, String LNGEFLAG, C120M01A c120m01a,
			C101S01G_N c101s01g_n, C101S01Q_N c101s01q_n, C101S01R_N c101s01r_n,
			String dDOCSTATUS);
	public List<DW_RKCREDIT_N> upDW_RKCREDIT_N(List<DW_RKCREDIT_N> data,
			L120M01A l120m01a, L140M01A l140m01a, String ACCT_KEY,
			String modelKind, String LNGEFLAG, C120M01A c120m01a,
			C101S01G_N c101s01g_n, String custId, String dupNo,
			String L140CustIdDupNo, String reason, String reasonOth,
			C101S01Q_N c101s01q_n, C101S01R_N c101s01r_n, String dDOCSTATUS,
			C120S01E c120s01e, boolean isShortPeriodCase);
	public List<DW_RKAPPLICANT_N> upDW_RKAPPLICANT_N(List<DW_RKAPPLICANT_N> data,
			L120M01A l120m01a, L140M01A l140m01a, String ACCT_KEY,
			String modelKind, String LNGEFLAG, C120M01A c120m01a,
			C120S01A c120s01a, C120S01B c120s01b, C120S01C c120s01c,
			C101S01G_N c101s01g_n, C101S01Q_N c101s01q_n, C101S01R_N c101s01r_n,
			String dDOCSTATUS);
	
}
