package com.mega.eloan.lms.crs.pages;

import com.iisigroup.cap.component.PageParameters;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;



import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.model.C242M01A;


@Controller
@RequestMapping("/crs/lms2420m01/{page}")
public class LMS2420M01Page extends AbstractEloanForm {

	@Autowired
	RetrialService retrialService;
	
	public LMS2420M01Page() {
		super();
	}
	
	@Override
	public void execute(ModelMap model, PageParameters params) throws Exception {
		super.execute(model, params);
		
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C242M01A meta = null; 
		if (Util.isNotEmpty(mainOid)) {			
			meta = retrialService.findC242M01A_oid(mainOid);
		}
		boolean show = false;
		if(meta!=null 
				&& Util.equals(meta.getDocStatus(), RetrialDocStatusEnum.預約單_未處理.getCode())
				&& meta.getExeTime()==null){
			show = true;
		}
		addAclLabel(model, new AclLabel("_btnSave", AuthType.Modify ,params, show));
		
		renderJsI18N(LMS2420M01Page.class);
	}
	
	@Override
	public Class<? extends Meta> getDomainClass() {
		return C242M01A.class;
	}
}
