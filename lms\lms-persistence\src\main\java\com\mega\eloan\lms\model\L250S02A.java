package com.mega.eloan.lms.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import tw.com.iisi.cap.model.GenericBean;

/**
 * 企金模擬動審檢核表第二類設定檔
 * 
 * <AUTHOR>
 * 
 */
@Entity
@Table(name = "L250S02A")
public class L250S02A extends GenericBean {
	private static final long serialVersionUID = 1L;
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	private Integer group;

	private Integer sub1Item;

	private Integer sub1Order;

	private String yes;

	private String no;

	private String na;

	private String sub1RejectVal;

	private String sub1Title;

	public String getOid() {
		return oid;
	}

	public void setOid(String oid) {
		this.oid = oid;
	}

	/**
	 * 大類項目
	 * 
	 * @return
	 */
	public Integer getGroup() {
		return group;
	}

	/**
	 * 大類項目
	 * 
	 * @param group
	 */
	public void setGroup(Integer group) {
		this.group = group;
	}

	/**
	 * 第二類項目
	 * 
	 * @return
	 */
	public Integer getSub1Item() {
		return sub1Item;
	}

	/**
	 * 第二類項目
	 * 
	 * @param sub1Item
	 */
	public void setSub1Item(Integer sub1Item) {
		this.sub1Item = sub1Item;
	}

	/**
	 * 第二類項目順序
	 * 
	 * @return
	 */
	public Integer getSub1Order() {
		return sub1Order;
	}

	/**
	 * 第二類項目順序
	 * 
	 * @param sub1Order
	 */
	public void setSub1Order(Integer sub1Order) {
		this.sub1Order = sub1Order;
	}

	/**
	 * 是否顯示 「是」
	 * 
	 * @return
	 */
	public String getYes() {
		return yes;
	}

	/**
	 * 是否顯示 「是」
	 * 
	 * @param yes
	 */
	public void setYes(String yes) {
		this.yes = yes;
	}

	/**
	 * 是否顯示 「否」
	 * 
	 * @return
	 */
	public String getNo() {
		return no;
	}

	/**
	 * 是否顯示 「否」
	 * 
	 * @param no
	 */
	public void setNo(String no) {
		this.no = no;
	}

	/**
	 * 是否顯示 「不適用」
	 * 
	 * @return
	 */
	public String getNa() {
		return na;
	}

	/**
	 * 是否顯示 「不適用」
	 * 
	 * @param na
	 */
	public void setNa(String na) {
		this.na = na;
	}

	/**
	 * 第二類項目名稱
	 * 
	 * @return
	 */
	public String getSub1Title() {
		return sub1Title;
	}

	/**
	 * 第二類項目名稱
	 * 
	 * @param sub1Title
	 */
	public void setSub1Title(String sub1Title) {
		this.sub1Title = sub1Title;
	}

	/**
	 * 勾選值不可為?
	 * 
	 * @param sub1RejectVal
	 */
	public void setSub1RejectVal(String sub1RejectVal) {
		this.sub1RejectVal = sub1RejectVal;
	}

	/**
	 * 勾選值不可為?
	 * 
	 * @return
	 */
	public String getSub1RejectVal() {
		return sub1RejectVal;
	}

}
