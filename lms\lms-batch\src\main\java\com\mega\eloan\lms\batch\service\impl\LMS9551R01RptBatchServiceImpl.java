/* 
 * ADEB001101ServiceImpl.java
 * 
 * Copyright (c) 2009-2012 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.batch.service.impl;

import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.codec.binary.Hex;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.gwclient.DWUCB1FTPClient;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.common.DebConfig;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.eloandb.service.EloandbFsBASEService;
import com.mega.eloan.lms.lns.service.LMS1201Service;
import com.mega.eloan.lms.mfaloan.service.MisELLNGTEEService;
import com.mega.eloan.lms.model.L201S99C;
import com.mega.eloan.lms.model.L201S99D;
import com.mega.eloan.lms.rpt.service.LMS9551R01RptService;

import tw.com.iisi.cap.annotation.NonTransactional;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapFormatException;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapMath;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.PropUtil;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 產生傳送卡務檔案 Service
 * </pre>
 * 
 * @since 2012/7/17
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/7/17,TammyChen,new
 *          <li>2012/11/29,Sunkist Wang,add interface for spring trasaction aop
 *          config
 *          <li>2012/11/30,Sunkist Wang,but!在LNSPServiceImpl.callSP() throw
 *          Exception 時將會導致這隻批次執行失敗，所以在還沒方案前改回來WebBatchService
 *          <li>2013/1/15,Tammy Chen,#1377 傳送卡務所有檔案內日期，均以YYYY/MM/DD格式顯示
 *          </ul>
 */
@Service("LMS9551R01RptBatchServiceImpl")
public class LMS9551R01RptBatchServiceImpl extends AbstractCapService implements
		WebBatchService {

	private Logger logger = LoggerFactory.getLogger(this.getClass());
	@Resource
	DebConfig debConfig;
	@Resource
	LMS9551R01RptService lms9551r01rptservice; // deb1010GService;

	@Resource
	DocFileService fileService;
	@Resource
	DWUCB1FTPClient ftpClient;
	@Resource
	EloandbBASEService eloandbBaseService;
	@Resource
	MisELLNGTEEService misEllngteeService;
	@Resource
	LMS1201Service service1201;
	@Resource
	EloandbFsBASEService eloandbFsBASEService;

	final String UTF8 = "UTF-8";
	final String ContentType = "application/octet-stream";
	final String success = "Y";
	final String fail = "N";
	final String TW_DATE_FORMAT_STR = "YYYMMDD";
	final String TW_DATE_FORMAT_STR2 = "YYY/MM/DD";
	final String DATE_FORMAT_STR = "yyyy-MM-dd";
	final String DATE_FORMAT_STR_2 = "yyyy/MM/dd";
	final String DATE_FORMAT_STR_3 = "yyyy.MM.dd";
	final String ADD_DATA = "addList";
	final String SYSTEM = "system";
	File fileDir = null;
	final String getBrNoCol = "ICBCBR_BRNO_BRNM";
	boolean isSuccess = true;

	String[] tableList = new String[] { "IDLMS001", "IDLMS002", "IDLMS003",
			"IDLMS004", "IDLMS005", "IDLMS006", "IDLMS007" };

	String[] excelA01 = new String[] { "SYSTYPE", "OID", "MAINID", "CASENO",
			"CUSTID", "DUPNO", "CUSTNAME", "ENDDATE", "TYPCD", "DOCTYPE",
			"DOCKIND", "DOCCODE", "CASEBRID", "CASEDATE", "CASELVL",
			"DOCSTATUS", "ITEMTYPE", "ITEMDSCR", "ITEMTITLE" };

	String[] excelA02 = new String[] { "SYSTYPE", "OID", "MAINID", "CASENO",
			"CUSTID", "DUPNO", "CUSTNAME", "ENDDATE", "TYPCD", "DOCTYPE",
			"DOCKIND", "DOCCODE", "CASEBRID", "CASEDATE", "CASELVL",
			"DOCSTATUS", "CNTRMAINID", "ITEMTYPE", "ITEMDSCR", "TOALOAN" };

	String[] excelA03 = new String[] { "SYSTYPE", "OID", "MAINID", "CASENO",
			"CUSTID", "DUPNO", "CUSTNAME", "ENDDATE", "TYPCD", "DOCTYPE",
			"DOCKIND", "DOCCODE", "CASEBRID", "CASEDATE", "CASELVL",
			"DOCSTATUS", "BCUSTID", "BDUPNO", "BCUSTNAME", "CUSTNO", "CMPNM",
			"RMK", "REJTREASON", "REJTCASEADJMEMO", "BLACKNAME", "REJTREASON1",
			"REJTCASEADJMEMO1", "POSDSCR", "CHAIRMAN", "GMANAGERDSCR",
			"GMANAGER", "STOCKHOLDER", "CMPADDR", "FACTORYADDR", "GROUPNAME",
			"BUSSITEM", "INVMDSCR", "ECONM", "ECONM07A", "BUSMEMO", "CCSMEMO",
			"PRIVATEEQUITYNAME", "BFPRIVATEEQUITYNAME", "BENEFICIARY",
			"SENIORMGR", "CTRLPEO", "PRODMKT", "NONEGRADE", "DATADSCR1",
			"DATADSCR2", "HASD1", "HASD2", "HASD3,", "ITEM1_D1", "ITEMID_D1",
			"ITEMDUPNO_D1", "ITEMNAME_D1", "ITEM2_D1", "ITEMMEMO_D1",
			"ITEM3_D1", "ITEM1_D2", "ITEMID_D2", "ITEMDUPNO_D2", "ITEMNAME_D2",
			"ITEM2_D2", "ITEMMEMO_D2", "ITEM1_D3", "ITEMID_D3", "ITEMDUPNO_D3",
			"ITEMNAME_D3", "ITEM2_D3", "ITEMMEMO_D3" };

	String[] excelA04 = new String[] { "SYSTYPE", "OID", "MAINID", "CASENO",
			"CUSTID", "DUPNO", "CUSTNAME", "ENDDATE", "TYPCD", "DOCTYPE",
			"DOCKIND", "DOCCODE", "CASEBRID", "CASEDATE", "CASELVL",
			"DOCSTATUS", "BCUSTID", "BDUPNO", "BCUSTNAME", "CNTRMAINID",
			"HEADITEM1", "GUTPERCENT", "GUTCUTDATE", "HEADITEM2", "SYNDIPFD",
			"HEADITEM3", "HEADITEM3TTL", "HEADITEM5", "MRATETYPE", "MRATE",
			"PROPERTY", "SNOKIND", "COMMSNO", "CNTRNO", "SBJPROPERTY",
			"UNITCASE2", "HEADITEM4", "ISDERIVATIVES", "CURRENTAPPLYCURR",
			"CURRENTAPPLYAMT", "REUSE", "OTHERCURR", "USEDEADLINE",
			"GUARANTORTYPE", "NOLOAN", "RESIDENTIAL", "LNTYPE", "IS722FLAG",
			"ISBUY", "UNSECUREFLAG", "BCASENO", "CESRJTREASON", "GIST",
			"LNSUBJECT", "PAYDEADLINE", "DERIVATIVES", "BLMEMO", "COUNTSAY",
			"DESP1", "CHECKNOTE", "GUARANTOR", "GUARANTORMEMO", "GUARANTOR1",
			"NOINSUREASONOTHER", "RMK", "MULTIAMTFLAG", "MULTIAMT",
			"MULTIASSUREAMT", "L140M01JSTR", "DERVMULTIAMT",
			"DERIVATIVESNUMDSCR", "FACILITYRATING", "PROJCLASS", "CUSTNO",
			"NTCODE", "POSTYPE", "POSDSCR", "CHAIRMANID", "CHAIRMANDUPNO",
			"CHAIRMAN", "GMANAGERDSCR", "GMANAGER", "RGTCURR", "RGTAMT",
			"RGTUNIT", "CPTLCURR", "CPTLAMT", "CPTLUNIT", "STOCKHOLDER",
			"CMPADDR", "FACTORYADDR", "GROUPNO", "GROUPNAME", "BUSSITEM",
			"INVMFLAG", "INVMDSCR", "BUSCODE", "ECONM", "BUSSKIND", "ECONM07A",
			"CUSTCLASS", "BUSMEMO", "CCSMEMO", "GROUPBADFLAG",
			"PRIVATEEQUITYFG", "PRIVATEEQUITYNO", "PRIVATEEQUITYNAME",
			"BENEFICIARY", "SENIORMGR", "CTRLPEO", "PRODMKT", "MBRLT",
			"MBRLTDSCR", "MHRLT44", "MHRLT45", "MHRLT44DSCR", "MHRLT45DSCR",
			"MBMHRLTDSCR", "FCTMBRLT", "FCTMHRLT", "FCTMHRLTDSCR", "MBRLT33",
			"MBRLTDSCR33", "CARLT206", "CARLT206DSCR", "LOCALRLT",
			"LOCALRLTDSCR", "ITEMDSCR_1402", "ITEMDSCR_1403", "ITEMDSCR_1404",
			"ITEMDSCR_1405", "MAINBIZID", "MAINBIZNAME", "MAINBIZCNTRNO" };

	String[] excelA05 = new String[] { "SYSTYPE", "OID", "MAINID", "CASENO",
			"CUSTID", "DUPNO", "CUSTNAME", "ENDDATE", "TYPCD", "DOCTYPE",
			"DOCKIND", "DOCCODE", "CASEBRID", "CASEDATE", "CASELVL",
			"DOCSTATUS", "CHAIRMAN", "PROCESS", "COLLSTAT", "AMT", "REMAIND",
			"BANKAMT", "BANKREMAIND", "GROUP", "SAMETOTAMT", "GRPNAME",
			"SAMEIDEA", "REPORTDSCR", "SEQDSCR_1", "SEQDSCR_2", "SEQDSCR_3",
			"SEQDSCR_4" };

	String[] excelA06 = new String[] { "SYSTYPE", "OID", "MAINID", "CASENO",
			"CUSTID", "DUPNO", "CUSTNAME", "ENDDATE", "TYPCD", "DOCTYPE",
			"DOCKIND", "DOCCODE", "CASEBRID", "CASEDATE", "CASELVL",
			"DOCSTATUS", "REASONDESC", "GIST", "ITEMOFBUSI", "RPTTITLE1",
			"RPTTITLE2", "RPTTITLEAREA1", "SIGNNO", "BACKREASON", "ITEMDSCR",
			"OPINION" };

	String[] excelA07 = new String[] { "SYSTYPE", "OID", "MAINID", "CASENO",
			"CUSTID", "DUPNO", "CUSTNAME", "ENDDATE", "TYPCD", "DOCTYPE",
			"DOCKIND", "DOCCODE", "CASEBRID", "CASEDATE", "CASELVL",
			"DOCSTATUS", "MEETINGTYPE", "GIST", "DISPWORD", "QUOTADESRC",
			"MEETINGNOTE" };

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.common.batch.service.WebBatchService#execute(net.sf.json
	 * .JSONObject)
	 */
	@Override
	@NonTransactional
	public JSONObject execute(JSONObject json) {
		JSONObject result = null;
		isSuccess = true;

		JSONObject request = json.getJSONObject("request");

		// int exeType = Util.equals(Util.trim(request.getString("exeType")),
		// "") ? 0
		// : Util.parseInt(Util.trim(request.getString("exeType"))); // exeType
		// // =>
		// // 99=整批執行(第一次)，
		// // 2=每天執行
		// if (exeType == 99 || exeType == 98) {
		// // 99.整批執行檔案並傳送FTP(第一次)
		// // 98.不產生檔案，只傳FTP
		// result = exeMode99(json, exeType);
		// } else {
		// result = exeModeDaily(json);
		// }

		// 每天執行與第一次執行整合到同一支*****************************

		// 99.整批執行檔案並傳送FTP(第一次)
		// 98.不產生檔案，只傳FTP
		// 01.每天產生檔案並傳送FTP
		result = exeMode(json);

		return result;
	}

	/**
	 * 整批第一次執行
	 * 
	 * @param json
	 * @return
	 */
	public JSONObject exeMode(JSONObject json) {
		JSONObject result = null;
		isSuccess = true;

		// 產生主檔

		JSONObject request = json.getJSONObject("request");

		int exeType = Util.equals(Util.trim(request.getString("exeType")), "") ? 0
				: Util.parseInt(Util.trim(request.getString("exeType"))); // exeType
																			// =>
																			// 99=整批執行(第一次)，
																			// 2=每天執行

		String dataStartDate = Util.trim(request.getString("dataStartDate"));
		String dataEndDate = Util.trim(request.getString("dataEndDate"));
		String exeMonth = Util.trim(request.getString("exeMonth")); // 每X個月產生一次檔案
																	// 6 :半年 12:
																	// 一年

		if (Util.equals(exeMonth, "")) {
			exeMonth = "12";
		}

		int intExeMonth = Util.parseInt(exeMonth);

		if (exeType == 99 || exeType == 98) {
			if (Util.equals(dataStartDate, "") || Util.equals(dataEndDate, "")) {
				result = JSONObject.fromObject(WebBatchCode.RC_ERROR);
				result.element(WebBatchCode.P_RESPONSE, this.getClass()
						.getName() + "執行失敗！==>" + "未輸入執行期間");
				logger.error("未輸入執行期間");
				return result;

			}
		} else {
			if (Util.equals(dataStartDate, "") || Util.equals(dataEndDate, "")) {
				final String DATE_FORMAT_STR = "yyyy-MM-dd";
				String currentDate = CapDate.getCurrentDate(DATE_FORMAT_STR);
				dataStartDate = CapDate.shiftDaysString(currentDate,
						DATE_FORMAT_STR, -1);
				dataEndDate = dataStartDate;

			}
		}

		String localFilePath = PropUtil.getProperty("docFile.dir")
				+ File.separator + PropUtil.getProperty(

				"systemId") + File.separator + "900" + File.separator
				+ "FULLSEARCH" + File.separator;

		File bkFolder = new File(localFilePath);

		try {
			if (!bkFolder.exists()) {
				FileUtils.forceMkdir(bkFolder);
			}

			// 將執行完成的檔案備份到bk
			// FileUtils.copyFile(inFile, new File(bkFolder.toString(), file));
			// FileUtils.forceDelete(inFile);
		} catch (IOException ex) {
			result = JSONObject.fromObject(WebBatchCode.RC_ERROR);
			result.element(WebBatchCode.P_RESPONSE, this.getClass().getName()
					+ "執行失敗！==>" + ex.getLocalizedMessage());
			logger.error(ex.getMessage(), ex);
			return result;
		}

		List<Map<String, String>> doMonthList = new ArrayList<Map<String, String>>();

		int i = 0;
		int colCount = 0;

		if (exeType == 99 || exeType == 98) {
			// 按月執行
			while (CapDate.addMonth(Util.parseDate(dataStartDate), i)
					.compareTo(Util.parseDate(dataEndDate)) <= 0) {
				colCount = colCount + 1;

				String colDate = TWNDate.toAD(CapDate.addMonth(
						Util.parseDate(dataStartDate), i));

				final String DATE_FORMAT_STR = "yyyy-MM-dd";

				String tDataStartDate = Util.getLeftStr(colDate, 7) + "-01";

				String xDataEndDate = TWNDate.toAD(CapDate.addMonth(
						Util.parseDate(dataStartDate), i + intExeMonth));

				String tDataEndDate = CapDate.shiftDaysString(xDataEndDate,
						DATE_FORMAT_STR, -1);

				Map<String, String> txMap = new HashMap<String, String>();

				txMap.put("dataStartDate", tDataStartDate);
				txMap.put("dataEndDate", tDataEndDate);

				doMonthList.add(txMap);

				i = i + intExeMonth;

			}
		} else {
			// 按日執行

			Map<String, String> txMap = new HashMap<String, String>();

			txMap.put("dataStartDate", dataStartDate);
			txMap.put("dataEndDate", dataEndDate);

			doMonthList.add(txMap);

		}

		ArrayList<String> filePath = new ArrayList<String>();
		ArrayList<String> fileName = new ArrayList<String>();

		for (Map<String, String> doMonthMap : doMonthList) {

			String tDataStartDate = MapUtils.getString(doMonthMap,
					"dataStartDate");

			String tDataEndDate = MapUtils.getString(doMonthMap, "dataEndDate");

			for (String tableName : tableList) {
				Map resultMap = null;
				String[] cols = null;
				String sqlName = "";
				if (Util.equals(tableName, "IDLMS001")) {
					// 1.取得簽報書敘述說明檔
					cols = excelA01;
					sqlName = "selL120m01d";
				} else if (Util.equals(tableName, "IDLMS002")) {
					// 2.取得額度明細表敘述說明檔
					cols = excelA02;
					sqlName = "selL140m01b";
				} else if (Util.equals(tableName, "IDLMS003")) {
					// 3.取得借款人基本資料
					cols = excelA03;
					sqlName = "selL120s01ab";
				} else if (Util.equals(tableName, "IDLMS004")) {
					// 4.取得借款人基本資料
					cols = excelA04;
					sqlName = "selL140m01a";
				} else if (Util.equals(tableName, "IDLMS005")) {
					// 5.取得借款人基本資料
					cols = excelA05;
					sqlName = "selL130m01a";
				} else if (Util.equals(tableName, "IDLMS006")) {
					// 6.取得簽報書主檔資料
					cols = excelA06;
					sqlName = "selL120m01a";
				} else if (Util.equals(tableName, "IDLMS007")) {
					// 7.取得會議決議檔資料
					cols = excelA07;
					sqlName = "selL120m01h";
				}

				resultMap = this.subCreateBatchExcel(tableName, sqlName,
						localFilePath, tDataStartDate, tDataEndDate, cols,
						filePath, fileName, exeType);

				if (resultMap != null && !resultMap.isEmpty()) {
					String tIsSuccess = MapUtils.getString(resultMap,
							"isSuccess");
					String errMsg = MapUtils.getString(resultMap, "errMsg");
					if (Util.notEquals(tIsSuccess, "Y")) {
						isSuccess = false;
						result = JSONObject.fromObject(WebBatchCode.RC_ERROR);
						result.element(WebBatchCode.P_RESPONSE, this.getClass()
								.getName() + "執行失敗！==>" + errMsg);
						logger.error(errMsg);
						return result;
					}

					// String fileNameStr = MapUtils.getString(resultMap,
					// "fileName");
					// if (Util.notEquals(fileNameStr, "")) {
					// String[] fielArray = fileNameStr.split("\\|");
					// for (String xFileNm : fielArray) {
					//
					// filePath.add(localFilePath);
					// fileName.add(xFileNm);
					//
					// }
					// }

				}

			}
		}

		try {
			if (filePath != null && !filePath.isEmpty()) {
				Map ftpResultMap = sendToFTP_Batch(filePath, fileName);

				String tIsSuccess = MapUtils.getString(ftpResultMap,
						"isSuccess");
				String errMsg = MapUtils.getString(ftpResultMap, "errMsg");

				if (Util.notEquals(tIsSuccess, "Y")) {
					isSuccess = false;
					result = JSONObject.fromObject(WebBatchCode.RC_ERROR);
					result.element(WebBatchCode.P_RESPONSE, this.getClass()
							.getName() + "執行失敗！==>" + errMsg);
					logger.error(errMsg);
					return result;
				}

			}

		} catch (Exception e) {
			isSuccess = false;
			e.printStackTrace();

			logger.error(e.getMessage());

			result = JSONObject.fromObject(WebBatchCode.RC_ERROR);
			result.element(WebBatchCode.P_RESPONSE, this.getClass().getName()
					+ "執行失敗！==>" + e.getMessage());

			return result;

		}

		if (isSuccess) {
			result = WebBatchCode.RC_SUCCESS;
			result.remove(WebBatchCode.P_RC_MSG);
		} else {
			result = WebBatchCode.RC_ERROR;
			result.element(WebBatchCode.P_RC_MSG, "產檔失敗");
		}

		return result;
	}

	public Map<String, String> subCreateBatchExcel(String tableName,
			String sqlName, String localFilePath, String tDataStartDate,
			String tDataEndDate, String[] cols, ArrayList<String> filePaths,
			ArrayList<String> fileNames, int exeType) {

		boolean isSuccess = true;
		String errMsg = "";

		Map<String, String> result = new HashMap<String, String>();
		List<String> outList = null;
		List<Map<String, Object>> elData = null;
		// StringBuffer fileNameList = new StringBuffer("");

		try {

			Map<String, IFormatter> reformat = null;

			// 檔案名稱(一年一個檔)
			String fileName = ""
					+ tableName
					+ "_"
					+ StringUtils.replace(CapDate.formatDate(
							Util.parseDate(tDataStartDate), DATE_FORMAT_STR_3),
							".", "");

			File headerTxtFile = new File(localFilePath, fileName + ".H");

			File txtFile = new File(localFilePath, fileName + ".D");

			logger.info(tDataStartDate + "~" + tDataEndDate);

			// exeType == 98 只傳FTP
			if (exeType != 98) {

				elData = eloandbFsBASEService.findDataListBySqlName(sqlName,
						tDataStartDate, tDataEndDate);

				outList = new ArrayList<String>();

				// write header
				StringBuilder headerSB = new StringBuilder();
				headerSB.append(
						StringUtils.replace(CapDate.formatDate(
								Util.parseDate(tDataStartDate),
								DATE_FORMAT_STR_3), ".", ""))
						.append(StringUtils.replace(
								CapDate.formatDate(
										Util.parseDate(tDataEndDate),
										DATE_FORMAT_STR_3), ".", ""))
						.append(tableName + ".D")
						.append(StringUtils.replace(
								CapDate.getCurrentDate(DATE_FORMAT_STR_3
										+ "HHMMSS"), ".", ""))
						.append(CapString.fillZeroHead(
								String.valueOf(elData.size()), 9));
				FileUtils.write(headerTxtFile, headerSB.toString(), UTF8);

				// 開始放查出資料
				if (elData != null && !CollectionUtils.isEmpty(elData)) {
					StringBuffer newValBuffer = null;
					for (Map<String, Object> data : elData) {

						newValBuffer = new StringBuffer("");
						for (int s = 0; s < cols.length; s++) {
							String col = cols[s];

							String val = MapUtils.getString(data, col);
							
							if(Util.equals(tableName, "IDLMS003")){
								if(Util.equals(col, "CUSTNO")){
									if(val.getBytes().length > 11){
										val = new String(val.getBytes(),0,11);
									}
									
								}
							}
							 
							if(Util.equals(tableName, "IDLMS007")){
								if(Util.equals(col, "DISPWORD")){
									if(val.getBytes().length > 768){
										val = new String(val.getBytes(),0,768);
									}
									 
								}
							}

							if (reformat != null && reformat.containsKey(col)) {
								IFormatter callback = reformat.get(col);
								if (callback instanceof BrFormatter) {
									val = callback.reformat(data);
								} else {
									val = callback.reformat(val);
								}
							}
							if (Util.equals(Util.trim(val), "")) {
								val = "";
							}

							val = val.replace("\n", "").replace("\r", "")
									.replace("\"", "'").replace(",", "，");

							if (Util.equals(newValBuffer.toString(), "")) {
								newValBuffer.append(val);
							} else {
								newValBuffer.append(",").append(val);
							}

						}

						String hexString = Hex.encodeHexString(newValBuffer
								.toString().getBytes(UTF8));

						hexString = hexString.replace("efbfbd", "");

						outList.add(new String(Hex.decodeHex(hexString
								.toCharArray())));
						newValBuffer = null;

					}

					elData.clear();
				}

				if (outList != null) {

					// fileNameList.append(
					// Util.equals(fileNameList.toString(), "") ? ""
					// : "|").append(fileName);

					filePaths.add(headerTxtFile.toString());
					fileNames.add(fileName + ".H");
					filePaths.add(txtFile.toString());
					fileNames.add(fileName + ".D");

					FileUtils.writeLines(txtFile, "UTF8", outList);

					outList.clear();
				}
			} else {

				// exeType == 98 只傳FTP
				filePaths.add(headerTxtFile.toString());
				fileNames.add(fileName + ".H");
				filePaths.add(txtFile.toString());
				fileNames.add(fileName + ".D");

				// fileNameList
				// .append(Util.equals(fileNameList.toString(), "") ? ""
				// : "|").append(fileName);
			}

		} catch (Exception e) {
			isSuccess = false;
			e.printStackTrace();
			errMsg = e.getMessage();
			logger.error(e.getMessage());
		}

		result.put("isSuccess", isSuccess ? "Y" : "N");
		result.put("errMsg", errMsg);
		// result.put(
		// "fileName",
		// Util.notEquals(fileNameList.toString(), "") ? fileNameList
		// .toString() : "");

		return result;

	}

	/**
	 * NOT USE XXXXXXXXXXXXXXXXXXXXX
	 * 
	 * @param json
	 * @return
	 */
	public JSONObject exeModeDaily(JSONObject json) {
		JSONObject result = null;
		isSuccess = true;
		Timestamp currentTime = CapDate.getCurrentTimestamp();

		// 產生主檔
		L201S99C meta = new L201S99C();
		String msgId = IDGenerator.getUUID();
		meta.setMainId(msgId);
		meta.setUid(msgId);
		meta.setQryDate(CapDate.getCurrentTimestamp());
		meta.setCreDate(CapDate.getCurrentTimestamp());

		JSONObject request = json.getJSONObject("request");

		String dataStartDate = Util.trim(request.getString("dataStartDate"));
		String dataEndDate = Util.trim(request.getString("dataEndDate"));
		int exeType = Util.equals(Util.trim(request.getString("exeType")), "") ? 0
				: Util.parseInt(Util.trim(request.getString("exeType"))); // exeType
																			// =>
																			// 99=整批執行(第一次)，
																			// 2=每天執行

		String exeMonth = Util.trim(request.getString("exeMonth")); // 每X個月產生一次檔案
		// 6 :半年 12:
		// 一年

		if (Util.equals(dataStartDate, "") || Util.equals(dataEndDate, "")) {
			final String DATE_FORMAT_STR = "yyyy-MM-dd";
			String currentDate = CapDate.getCurrentDate(DATE_FORMAT_STR);
			dataStartDate = CapDate.shiftDaysString(currentDate,
					DATE_FORMAT_STR, -1);
			dataEndDate = currentDate;

		}

		String localFilePath = PropUtil.getProperty("docFile.dir")
				+ File.separator + PropUtil.getProperty(

				"systemId") + File.separator + "900" + File.separator
				+ "FULLSEARCH" + File.separator;
		File bkFolder = new File(localFilePath);

		try {
			if (!bkFolder.exists()) {
				FileUtils.forceMkdir(bkFolder);
			}

			// 將執行完成的檔案備份到bk
			// FileUtils.copyFile(inFile, new File(bkFolder.toString(), file));
			// FileUtils.forceDelete(inFile);
		} catch (IOException ex) {
			result = JSONObject.fromObject(WebBatchCode.RC_ERROR);
			result.element(WebBatchCode.P_RESPONSE, this.getClass().getName()
					+ "執行失敗！==>" + ex.getLocalizedMessage());
			logger.error(ex.getMessage(), ex);
			return result;
		}

		// 1.取得簽報書敘述說明檔
		List<L201S99D> result01 = subCreateA01Excel(meta, dataStartDate,
				dataEndDate, localFilePath, exeType); //
		// countAll("A01", meta, result01); // 會寫入另一筆總計 --->可以拿來寫.H檔

		// 2.取得額度明細表敘述說明檔
		List<L201S99D> result02 = subCreateA02Excel(meta, dataStartDate,
				dataEndDate, localFilePath, exeType); //
		// countAll("A02", meta, result02); // 會寫入另一筆總計 --->可以拿來寫.H檔

		// 3.取得借款人基本資料
		List<L201S99D> result03 = subCreateA03Excel(meta, dataStartDate,
				dataEndDate, localFilePath, exeType); //
		// countAll("A03", meta, result03); // 會寫入另一筆總計 --->可以拿來寫.H檔

		// 4.取得借款人基本資料
		List<L201S99D> result04 = subCreateA04Excel(meta, dataStartDate,
				dataEndDate, localFilePath, exeType); //
		// countAll("A04", meta, result04); // 會寫入另一筆總計 --->可以拿來寫.H檔

		// 5.取得借款人基本資料
		List<L201S99D> result05 = subCreateA05Excel(meta, dataStartDate,
				dataEndDate, localFilePath, exeType); //
		// countAll("A05", meta, result05); // 會寫入另一筆總計 --->可以拿來寫.H檔

		// 6.取得簽報書主檔資料
		List<L201S99D> result06 = subCreateA06Excel(meta, dataStartDate,
				dataEndDate, localFilePath, exeType); //
		// countAll("A06", meta, result06); // 會寫入另一筆總計 --->可以拿來寫.H檔

		// 7.取得會議決議檔資料
		List<L201S99D> result07 = subCreateA07Excel(meta, dataStartDate,
				dataEndDate, localFilePath, exeType); //
		// countAll("A07", meta, result07); // 會寫入另一筆總計 --->可以拿來寫.H檔

		if (exeType != 99) {
			// 非99=整批執行(第一次)，則由系統自動FTP
			try {
				List<L201S99D> erResult = new ArrayList<L201S99D>();
				erResult.addAll(result01);
				erResult.addAll(result02);
				erResult.addAll(result03);
				erResult.addAll(result04);
				erResult.addAll(result05);
				erResult.addAll(result06);
				erResult.addAll(result07);

				meta.setL201s99ds(erResult);

				// 傳送檔案至卡務中心
				meta = sendToFTP(meta);

				lms9551r01rptservice.saveS99C(meta);
			} catch (Exception e) {
				isSuccess = false;
				e.printStackTrace();
				logger.error(e.getMessage());
			}
		}

		if (isSuccess) {
			result = WebBatchCode.RC_SUCCESS;
			result.remove(WebBatchCode.P_RC_MSG);
		} else {
			result = WebBatchCode.RC_ERROR;
			result.element(WebBatchCode.P_RC_MSG, "產檔失敗");
		}

		return result;
	}

	/**
	 * 傳送檔案至卡務中心
	 * 
	 * @param meta
	 *            D201S99C
	 * @return D201S99C
	 * @throws CapException
	 */
	public L201S99C sendToFTP(L201S99C meta) throws CapException {
		meta.setExeResult("Y");
		List<DocFile> files = fileService
				.findByIDAndPid(meta.getMainId(), null);
		ArrayList<String> filePath = new ArrayList<String>();
		ArrayList<String> fileName = new ArrayList<String>();
		if (!CollectionUtils.isEmpty(files)) {
			for (DocFile file : files) {
				if (!"Distributionlist".equals(file.getFieldId())) {
					filePath.add(fileService.getFilePath(file));
					fileName.add(file.getSrcFileName());
				}
			}
			try {

				// 後台管理->系統設定維護->LMS_ArCtrlCanShow
				// Map<String, Object> onlineDateMap = eloandbBaseService
				// .getSysParamData("UCCFTP_DEF_DIR");
				//
				// String serverDir =
				// Util.trim(onlineDateMap.get("PARAMVALUE"));

				ftpClient.send(meta.getMainId(),
						filePath.toArray(new String[] {}),
						ftpClient.getServerDir(), // dw/ftpdata/ftpucc1/ftpout/
						// debConfig.getUCCFTPUploadPath(), // 切換路徑至：/ftpout/
						fileName.toArray(new String[] {}), true, true, false);
			} catch (Exception e) {
				meta.setExeResult("N");
				e.printStackTrace();
			}
		}
		meta.setSender(SYSTEM);
		meta.setSendTime(CapDate.getCurrentTimestamp());

		return meta;
	}

	/**
	 * 傳送檔案至卡務中心
	 * 
	 * @param meta
	 *            D201S99C
	 * @return D201S99C
	 * @throws CapException
	 */
	public Map<String, String> sendToFTP_Batch(ArrayList<String> filePath,
			ArrayList<String> fileName) throws CapException {

		boolean isSuccess = true;
		String errMsg = "";
		// ArrayList<String> filePath = new ArrayList<String>();
		// ArrayList<String> fileName = new ArrayList<String>();
		Map<String, String> result = new HashMap<String, String>();

		try {

			// 後台管理->系統設定維護->LMS_ArCtrlCanShow
			// Map<String, Object> onlineDateMap = eloandbBaseService
			// .getSysParamData("UCCFTP_DEF_DIR");
			//
			// String serverDir = Util.trim(onlineDateMap.get("PARAMVALUE"));
			String msgId = IDGenerator.getUUID();
			ftpClient.send(msgId, filePath.toArray(new String[] {}),
					ftpClient.getServerDir(), // dw/ftpdata/ftpucc1/ftpout/
					// debConfig.getUCCFTPUploadPath(), // 切換路徑至：/ftpout/
					fileName.toArray(new String[] {}), true, true, false);
		} catch (Exception e) {
			isSuccess = false;
			e.printStackTrace();
			errMsg = e.getMessage();
			logger.error(e.getMessage());
		}

		result.put("isSuccess", isSuccess ? "Y" : "N");
		result.put("errMsg", errMsg);
		return result;
	}

	/**
	 * 取得簽報書敘述說明檔
	 * 
	 * @param queryDate
	 * @param meta
	 * @return L201S99D
	 */
	public List<L201S99D> subCreateA01Excel(L201S99C meta,
			String dataStartDate, String dataEndDate, String localFilePath,
			int exeType) {

		ArrayList<L201S99D> S99Ds = new ArrayList<L201S99D>();
		Map<String, IFormatter> format = null;

		List<Map<String, Object>> elData = eloandbFsBASEService.findL120m01d(
				dataStartDate, dataEndDate);

		S99Ds.add(createExcelA0X("A01", "IDLMS001", "簽報書敘述說明檔", excelA01,
				elData, meta, format, dataStartDate, dataEndDate, localFilePath));

		return S99Ds;
	}

	/**
	 * 取得額度明細表敘述說明檔
	 * 
	 * @param queryDate
	 * @param meta
	 * @return L201S99D
	 */
	public List<L201S99D> subCreateA02Excel(L201S99C meta,
			String dataStartDate, String dataEndDate, String localFilePath,
			int exeType) {

		ArrayList<L201S99D> S99Ds = new ArrayList<L201S99D>();
		Map<String, IFormatter> format = null;

		List<Map<String, Object>> elData = eloandbFsBASEService.findL140m01b(
				dataStartDate, dataEndDate);

		S99Ds.add(createExcelA0X("A02", "IDLMS002", "額度明細表敘述說明檔", excelA02,
				elData, meta, format, dataStartDate, dataEndDate, localFilePath));

		return S99Ds;
	}

	/**
	 * 取得借款人基本資料
	 * 
	 * @param queryDate
	 * @param meta
	 * @return L201S99D
	 */
	public List<L201S99D> subCreateA03Excel(L201S99C meta,
			String dataStartDate, String dataEndDate, String localFilePath,
			int exeType) {

		ArrayList<L201S99D> S99Ds = new ArrayList<L201S99D>();
		Map<String, IFormatter> format = null;

		List<Map<String, Object>> elData = eloandbFsBASEService.findL120s01ab(
				dataStartDate, dataEndDate);

		S99Ds.add(createExcelA0X("A03", "IDLMS003", "借款人基本資料", excelA03,
				elData, meta, format, dataStartDate, dataEndDate, localFilePath));

		return S99Ds;
	}

	/**
	 * 取得借款人基本資料
	 * 
	 * @param queryDate
	 * @param meta
	 * @return L201S99D
	 */
	public List<L201S99D> subCreateA04Excel(L201S99C meta,
			String dataStartDate, String dataEndDate, String localFilePath,
			int exeType) {

		ArrayList<L201S99D> S99Ds = new ArrayList<L201S99D>();
		Map<String, IFormatter> format = null;

		List<Map<String, Object>> elData = eloandbFsBASEService.findL140m01a(
				dataStartDate, dataEndDate);

		S99Ds.add(createExcelA0X("A04", "IDLMS004", "額度明細表資料", excelA04,
				elData, meta, format, dataStartDate, dataEndDate, localFilePath));

		return S99Ds;
	}

	/**
	 * 取得異常通報資料
	 * 
	 * @param queryDate
	 * @param meta
	 * @return L201S99D
	 */
	public List<L201S99D> subCreateA05Excel(L201S99C meta,
			String dataStartDate, String dataEndDate, String localFilePath,
			int exeType) {

		ArrayList<L201S99D> S99Ds = new ArrayList<L201S99D>();
		Map<String, IFormatter> format = null;

		List<Map<String, Object>> elData = eloandbFsBASEService.findL130m01a(
				dataStartDate, dataEndDate);

		S99Ds.add(createExcelA0X("A05", "IDLMS005", "異常通報資料", excelA05, elData,
				meta, format, dataStartDate, dataEndDate, localFilePath));

		return S99Ds;
	}

	/**
	 * 取得簽報書主檔資料
	 * 
	 * @param queryDate
	 * @param meta
	 * @return L201S99D
	 */
	public List<L201S99D> subCreateA06Excel(L201S99C meta,
			String dataStartDate, String dataEndDate, String localFilePath,
			int exeType) {

		ArrayList<L201S99D> S99Ds = new ArrayList<L201S99D>();
		Map<String, IFormatter> format = null;

		List<Map<String, Object>> elData = eloandbFsBASEService.findL120m01a(
				dataStartDate, dataEndDate);

		S99Ds.add(createExcelA0X("A06", "IDLMS006", "簽報書主檔資料", excelA06,
				elData, meta, format, dataStartDate, dataEndDate, localFilePath));

		return S99Ds;
	}

	/**
	 * 取得會議決議檔資料
	 * 
	 * @param queryDate
	 * @param meta
	 * @return L201S99D
	 */
	public List<L201S99D> subCreateA07Excel(L201S99C meta,
			String dataStartDate, String dataEndDate, String localFilePath,
			int exeType) {

		ArrayList<L201S99D> S99Ds = new ArrayList<L201S99D>();
		Map<String, IFormatter> format = null;

		List<Map<String, Object>> elData = eloandbFsBASEService.findL120m01h(
				dataStartDate, dataEndDate);

		S99Ds.add(createExcelA0X("A07", "IDLMS007", "會議決議檔資料", excelA07,
				elData, meta, format, dataStartDate, dataEndDate, localFilePath));

		return S99Ds;
	}

	public void countAll(String dataType, L201S99C meta, List<L201S99D> S99Ds) {
		if (!CollectionUtils.isEmpty(S99Ds)) {
			L201S99D count = new L201S99D(meta);
			BigDecimal dataCnt = BigDecimal.ZERO;
			try {
				for (L201S99D S99D : S99Ds) {
					if (S99D.getDataCnt() != null) {
						dataCnt = dataCnt.add(S99D.getDataCnt());
					}
				}
			} catch (Exception e) {
				e.printStackTrace();
				logger.error(e.getMessage());
			}
			count.setDataType(dataType);
			count.setDataCnt(dataCnt);
			S99Ds.add(count);
		}
	}

	class BrFormatter implements IFormatter {

		private static final long serialVersionUID = 1L;

		String custId = "";
		String applyDate = "";

		public BrFormatter(String custId, String applyDate) {
			this.custId = custId;
			this.applyDate = applyDate;
		}

		/*
		 * (non-Javadoc)
		 * 
		 * @see tw.com.iisi.cap.formatter.IFormatter#reformat(java.lang.Object)
		 */
		@SuppressWarnings("unchecked")
		public String reformat(Object in) throws CapFormatException {
			Map<String, Object> misData = (Map<String, Object>) in;
			StringBuffer sb = new StringBuffer();
			return sb.toString();
		}
	}

	class DateFormatter implements IFormatter {

		private static final long serialVersionUID = 1L;

		String fromFormat = TW_DATE_FORMAT_STR;
		String toFormat = DATE_FORMAT_STR_2;

		/*
		 * (non-Javadoc)
		 * 
		 * @see tw.com.iisi.cap.formatter.IFormatter#reformat(java.lang.Object)
		 */
		@SuppressWarnings("unchecked")
		public String reformat(Object in) throws CapFormatException {
			String val = (String) in;
			if (!CapString.isEmpty(val)) {
				if (CapDate.isMatchPattern(val, TW_DATE_FORMAT_STR2)) {
					fromFormat = TW_DATE_FORMAT_STR2;
				} else if (CapDate.isMatchPattern(val, DATE_FORMAT_STR)) {
					fromFormat = DATE_FORMAT_STR;
				}
				return CapDate.formatDateFromF1ToF2(val, fromFormat, toFormat);
			}
			return EloanConstants.EMPTY_STRING;
		}
	}

	class DateYMFormatter implements IFormatter {

		private static final long serialVersionUID = 1L;

		String fromFormat = TW_DATE_FORMAT_STR;
		String toFormat = "yyyy/MM";

		/*
		 * (non-Javadoc)
		 * 
		 * @see tw.com.iisi.cap.formatter.IFormatter#reformat(java.lang.Object)
		 */
		@SuppressWarnings("unchecked")
		public String reformat(Object in) throws CapFormatException {
			String val = (String) in;
			if (!CapString.isEmpty(val)) {
				return CapDate.formatDateFromF1ToF2(val + "01", fromFormat,
						toFormat);
			}
			return EloanConstants.EMPTY_STRING;
		}
	}

	class BigDecimalFormatter implements IFormatter {

		private static final long serialVersionUID = 1L;

		/*
		 * (non-Javadoc)
		 * 
		 * @see tw.com.iisi.cap.formatter.IFormatter#reformat(java.lang.Object)
		 */
		@SuppressWarnings("unchecked")
		public String reformat(Object in) throws CapFormatException {
			String val = (String) in;
			if (!CapString.isEmpty(val)) {
				BigDecimal dec = in instanceof BigDecimal ? (BigDecimal) in
						: CapMath.getBigDecimal((String) in);
				String re = dec.stripTrailingZeros().toPlainString();
				if ("0.00".equals(re)) {
					re = "0";
				}
				return re;
			}
			return EloanConstants.EMPTY_STRING;
		}
	}

	private DocFile getDocFile(String mainId, String dataType) {
		// 設定上傳檔案資訊
		DocFile docFile = new DocFile();
		docFile.setBranchId("900");
		docFile.setMainId(mainId);
		docFile.setCrYear(CapDate.getCurrentDate("yyyy"));
		docFile.setDeletedTime(null);
		docFile.setUploadTime(CapDate.getCurrentTimestamp());
		docFile.setFieldId("excel" + dataType);
		docFile.setSysId(fileService.getSysId());

		return docFile;
	}

	/**
	 * 產生檔案
	 * 
	 * @param dataType
	 *            資料別
	 * @param fileName
	 *            檔案名稱
	 * @param sheetNm
	 *            頁籤名稱
	 * @param cols
	 *            寫入欄位
	 * @param misData
	 *            寫入內容
	 * @param meta
	 *            主檔
	 * @param reformat
	 *            DataReformatter
	 * @return D201S99D
	 */
	public L201S99D createExcelA0X(String dataType, String fileName,
			String sheetNm, String[] cols, List<Map<String, Object>> misData,
			L201S99C meta, Map<String, IFormatter> reformat,
			String dataStartDate, String dataEndDate, String localFilePath) {

		// 組成匯出文字資料。
		StringBuffer fileString = new StringBuffer();
		// HEADER
		StringBuilder headerSB = new StringBuilder();

		// fileName = fileName + "_" + CapDate.getCurrentDate(DATE_FORMAT_STR_3)
		// + ".csv";

		fileName = ""
				+ fileName
				+ "_"
				+ StringUtils.replace(CapDate.formatDate(
						Util.parseDate(dataStartDate), DATE_FORMAT_STR_3), ".",
						"");

		File headerTxtFile = new File(localFilePath, fileName + ".H");

		File txtFile = new File(localFilePath, fileName + ".D");

		// 資料結果明細
		L201S99D subMeta = new L201S99D(meta);
		subMeta.setDataType(dataType);

		// 設定上傳檔案資訊
		DocFile docFileH = getDocFile(meta.getMainId(), dataType);
		DocFile docFileD = getDocFile(meta.getMainId(), dataType);

		try {
			// 先放筆數資料
			String cnt = CollectionUtils.isEmpty(misData) ? "0" : Integer
					.toString(misData.size());

			// write header

			headerSB.append(
					StringUtils.replace(CapDate.formatDate(
							Util.parseDate(dataStartDate), DATE_FORMAT_STR_3),
							".", ""))
					.append(StringUtils.replace(CapDate.formatDate(
							Util.parseDate(dataEndDate), DATE_FORMAT_STR_3),
							".", ""))
					.append(fileName + ".D")
					.append(StringUtils.replace(CapDate
							.getCurrentDate(DATE_FORMAT_STR_3 + "HHMMSS"), ".",
							""))
					.append(CapString.fillZeroHead(
							String.valueOf(misData.size()), 9));

			// 開始放查出資料
			if (!CollectionUtils.isEmpty(misData)) {
				for (Map<String, Object> data : misData) {

					for (int i = 0; i < cols.length; i++) {
						String col = cols[i];

						String val = MapUtils.getString(data, col);

						if (reformat != null && reformat.containsKey(col)) {
							IFormatter callback = reformat.get(col);
							if (callback instanceof BrFormatter) {
								val = callback.reformat(data);
							} else {
								val = callback.reformat(val);
							}
						}
						if (Util.equals(Util.trim(val), "")) {
							val = "";
						}

						val = val.replace("\n", "").replace("\"", "'")
								.replace(",", "，");
						fileString.append(val).append(",");
					}

					// if (Util.notEquals(rptOid, "")) {
					// rptOid = rptOid.substring(0, StringUtils.length(rptOid) -
					// 1);
					// }

					fileString.append("\r\n"); // <--資料量太多會造成out of memory

				}
			}

			byte[] byteArray = null;
			try {
				byteArray = fileString.toString().getBytes(UTF8);

			} catch (UnsupportedEncodingException e) {
				byteArray = fileString.toString().getBytes(); // <--資料量太多會造成out
																// of memory
			}

			// 儲存產出檔案
			docFileD.setData(byteArray);
			// docFile.setFileSize(FileUtils.sizeOf(relFile));
			docFileD.setFileDesc(txtFile.toString());
			docFileD.setSrcFileName(fileName + ".D");
			docFileD.setContentType(ContentType);
			fileService.save(docFileD);

			// 儲存產出檔案
			docFileH.setData(headerSB.toString().getBytes(UTF8));
			// docFile.setFileSize(FileUtils.sizeOf(relFile));
			docFileH.setFileDesc(txtFile.toString());
			docFileH.setSrcFileName(fileName + ".H");
			docFileH.setContentType(ContentType);
			fileService.save(docFileH);

			subMeta.setDataCnt(Integer.parseInt(cnt));
			subMeta.setSendFlag(success);

		} catch (Exception e) {
			isSuccess = false;
			subMeta.setSendFlag(fail);
			e.printStackTrace();
			logger.error(e.getMessage());
		}
		return subMeta;
	}

}
