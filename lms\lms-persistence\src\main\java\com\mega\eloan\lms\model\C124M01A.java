/* 
 * C124M01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 勞工紓困信保整批貸款申請書 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C124M01A", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId"}))
public class C124M01A extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 案件編號 **/
	@Size(max=50)
	@Column(name="APPROVENO", length=50, columnDefinition="VARCHAR (50)")
	private String approveNo;

	/** 
	 * 本行已對個資當事人進行「信保基金蒐集、處理及利用個人資料告知書」聲明<p/>
	 * 1：是
	 */
	@Digits(integer=1, fraction=0, groups = Check.class)
	@Column(name="AGREEMENT", columnDefinition="DECIMAL(1,0)")
	private Integer agreement;

	/** 
	 * 本行依移送信用保證之程序規定辦理，填寫申請書前已先行辦妥徵信<p/>
	 * 1：是
	 */
	@Digits(integer=1, fraction=0, groups = Check.class)
	@Column(name="ISCREDITCHECK", columnDefinition="DECIMAL(1,0)")
	private Integer isCreditCheck;

	/** 
	 * 申請項目<p/>
	 * 固定塞131
	 */
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="TYPE", columnDefinition="DECIMAL(3,0)")
	private Integer type;

	/** 
	 * 保證對象資格<p/>
	 * 1：是<br/>
	 *  2：否
	 */
	@Digits(integer=1, fraction=0, groups = Check.class)
	@Column(name="QUALICODE", columnDefinition="DECIMAL(1,0)")
	private Integer qualiCode;

	/** 
	 * 申請人之票、債信情形<p/>
	 * 1：是<br/>
	 *  2：否
	 */
	@Digits(integer=1, fraction=0, groups = Check.class)
	@Column(name="ISCREDITABNORMAL", columnDefinition="DECIMAL(1,0)")
	private Integer isCreditAbnormal;

	/** 借款人出生日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="BORROWERBIRTHDAY", columnDefinition="DATE")
	private Date borrowerBirthDay;

	/** 申貸受理日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="APPLYLOANDAY", columnDefinition="DATE")
	private Date applyLoanDay;

	/** 申請額度 **/
	@Digits(integer=14, fraction=0, groups = Check.class)
	@Column(name="APPLYLOAN", columnDefinition="DECIMAL(14,0)")
	private BigDecimal applyLoan;

	/** 聯絡電話_區碼 **/
	@Size(max=2)
	@Column(name="TEL1", length=2, columnDefinition="VARCHAR(2)")
	private String tel1;

	/** 聯絡電話_號碼 **/
	@Size(max=8)
	@Column(name="TEL2", length=8, columnDefinition="VARCHAR(8)")
	private String tel2;

	/** 申請人行動電話區碼 **/
	@Size(max=4)
	@Column(name="OWNERCELLPHONE1", length=4, columnDefinition="VARCHAR(4)")
	private String ownerCellphone1;

	/** 申請人行動電話號碼 **/
	@Size(max=6)
	@Column(name="OWNERCELLPHONE2", length=6, columnDefinition="VARCHAR(6)")
	private String ownerCellphone2;

	/** 
	 * 有無電子郵箱<p/>
	 * 1：有<br/>
	 *  2：無
	 */
	@Digits(integer=1, fraction=0, groups = Check.class)
	@Column(name="ISEMAIL", columnDefinition="DECIMAL(1,0)")
	private Integer isEmail;

	/** 個人電子郵箱 **/
	@Size(max=50)
	@Column(name="EMAIL", length=50, columnDefinition="VARCHAR(50)")
	private String email;

	/** 申請人_郵遞區號 **/
    @Size(max=5)
    @Column(name="ZIP", length=5, columnDefinition="VARCHAR(5)")
    private String zip;

	/** 申請人_縣市 **/
    @Size(max=2)
    @Column(name="CITY", length=2, columnDefinition="VARCHAR(2)")
    private String city;

	/** 申請人_鄉鎮市區 **/
    @Size(max=3)
    @Column(name="DIST", length=3, columnDefinition="VARCHAR(3)")
    private String dist;

	/** 
	 * 申請人_里村名<p/>
	 * 前端只能輸入50個字(不管全半型)
	 */
	@Size(max=150)
	@Column(name="VILLAGENAME", length=150, columnDefinition="VARCHAR(150)")
	private String villageName;

	/** 
	 * 申請人_里村<p/>
	 * 1,里<br/>
	 *  2,村
	 */
	@Digits(integer=1, fraction=0, groups = Check.class)
	@Column(name="VILLAGE", columnDefinition="DECIMAL(1,0)")
	private Integer village;

	/** 申請人_鄰 **/
	@Digits(integer=4, fraction=0, groups = Check.class)
	@Column(name="NEIGHBORHOOD", columnDefinition="DECIMAL(4,0)")
	private Integer neighborhood;

	/** 
	 * 申請人_路/街名<p/>
	 * 前端只能輸入20個字(不管全半型)
	 */
	@Size(max=60)
	@Column(name="ROADNAME", length=60, columnDefinition="VARCHAR(60)")
	private String roadName;

	/** 
	 * 申請人_路<p/>
	 * 1,(空白)<br/>
	 *  2,路<br/>
	 *  3,街
	 */
	@Digits(integer=1, fraction=0, groups = Check.class)
	@Column(name="ROAD", columnDefinition="DECIMAL(1,0)")
	private Integer road;

	/** 
	 * 申請人_段<p/>
	 * 前端只能輸入4個字(不管全半型)
	 */
	@Size(max=12)
	@Column(name="SEC", length=12, columnDefinition="VARCHAR(12)")
	private String sec;

	/** 
	 * 申請人_巷<p/>
	 * 前端只能輸入8個字(不管全半型)
	 */
	@Size(max=24)
	@Column(name="LANE", length=24, columnDefinition="VARCHAR(24)")
	private String lane;

	/** 
	 * 申請人_弄<p/>
	 * 前端只能輸入8個字(不管全半型)
	 */
	@Size(max=24)
	@Column(name="ALLEY", length=24, columnDefinition="VARCHAR(24)")
	private String alley;

	/** 
	 * 申請人_號<p/>
	 * 前端只能輸入4個字(不管全半型)
	 */
	@Size(max=12)
	@Column(name="NO1", length=12, columnDefinition="VARCHAR(12)")
	private String no1;

	/** 
	 * 申請人_之號<p/>
	 * 前端只能輸入4個字(不管全半型)
	 */
	@Size(max=12)
	@Column(name="NO2", length=12, columnDefinition="VARCHAR(12)")
	private String no2;

	/** 
	 * 申請人_樓<p/>
	 * 前端只能輸入3個字(不管全半型)
	 */
	@Size(max=9)
	@Column(name="FLOOR1", length=9, columnDefinition="VARCHAR(9)")
	private String floor1;

	/** 
	 * 申請人_之樓<p/>
	 * 前端只能輸入3個字(不管全半型)
	 */
	@Size(max=9)
	@Column(name="FLOOR2", length=9, columnDefinition="VARCHAR(9)")
	private String floor2;

	/** 
	 * 申請人_室<p/>
	 * 前端只能輸入3個字(不管全半型)
	 */
	@Size(max=9)
	@Column(name="ROOM", length=9, columnDefinition="VARCHAR(9)")
	private String room;

    /**申請書鍵入者號碼 */
    @Column(length = 6, columnDefinition = "CHAR(6)")
    private String importerNo;

	/** 
	 * 申請書鍵入者姓名<p/>
	 * 前端只能輸10(不管全半型)
	 */
	@Size(max=30)
	@Column(name="IMPORTERNAME", length=30, columnDefinition="VARCHAR(30)")
	private String importerName;

	/** 申請書鍵入者行動電話區碼 **/
	@Size(max=4)
	@Column(name="IMPORTERCELLPHONE1", length=4, columnDefinition="VARCHAR(4)")
	private String importerCellphone1;

	/** 申請書鍵入者行動電話號碼 **/
	@Size(max=6)
	@Column(name="IMPORTERCELLPHONE2", length=6, columnDefinition="VARCHAR(6)")
	private String importerCellphone2;

	/** 申請書鍵入者電話區碼 **/
	@Size(max=2)
	@Column(name="IMPORTERTEL1", length=2, columnDefinition="VARCHAR(2)")
	private String importerTel1;

	/** 申請書鍵入者電話號碼 **/
	@Size(max=8)
	@Column(name="IMPORTERTEL2", length=8, columnDefinition="VARCHAR(8)")
	private String importerTel2;

	/** 申請書鍵入者電話分機 **/
	@Size(max=5)
	@Column(name="IMPORTERTELEXT", length=5, columnDefinition="VARCHAR(5)")
	private String importerTelExt;

	/** 申請書鍵入者電子信箱 **/
	@Size(max=50)
	@Column(name="IMPORTEREMAIL", length=50, columnDefinition="VARCHAR (50)")
	private String importerEmail;

	/** 保證案號 **/
	@Digits(integer=10, fraction=0, groups = Check.class)
	@Column(name="GRNTPAPER", columnDefinition="DECIMAL(10,0)")
	private BigDecimal grntPaper;

	/** 回覆日 **/
	@Column(name="RECEIVEDAY", columnDefinition="TIMESTAMP")
	private Timestamp receiveDay;

	/** 回覆結果 **/
	@Size(max=3)
	@Column(name="DATASTATUS", length=3, columnDefinition="VARCHAR(3)")
	private String dataStatus;

    /** 檔案產生日 **/
    @Column(name="BATCHDATE", columnDefinition="TIMESTAMP")
    private Timestamp batchDate;

    /** 處理說明 **/
    @Size(max=600)
    @Column(name="DESCRIPTION", length=600, columnDefinition="VARCHAR(600)")
    private String description;

    /** 勞工紓困版本 **/
    @Digits(integer = 2, fraction = 0, groups = Check.class)
    @Column(name = "VERSION", columnDefinition = "DECIMAL(2,0)")
    private Integer version;

	/** 取得案件編號 **/
	public String getApproveNo() {
		return this.approveNo;
	}
	/** 設定案件編號 **/
	public void setApproveNo(String value) {
		this.approveNo = value;
	}

	/** 
	 * 取得本行已對個資當事人進行「信保基金蒐集、處理及利用個人資料告知書」聲明<p/>
	 * 1：是
	 */
	public Integer getAgreement() {
		return this.agreement;
	}
	/**
	 *  設定本行已對個資當事人進行「信保基金蒐集、處理及利用個人資料告知書」聲明<p/>
	 *  1：是
	 **/
	public void setAgreement(Integer value) {
		this.agreement = value;
	}

	/** 
	 * 取得本行依移送信用保證之程序規定辦理，填寫申請書前已先行辦妥徵信<p/>
	 * 1：是
	 */
	public Integer getIsCreditCheck() {
		return this.isCreditCheck;
	}
	/**
	 *  設定本行依移送信用保證之程序規定辦理，填寫申請書前已先行辦妥徵信<p/>
	 *  1：是
	 **/
	public void setIsCreditCheck(Integer value) {
		this.isCreditCheck = value;
	}

	/** 
	 * 取得申請項目<p/>
	 * 固定塞131
	 */
	public Integer getType() {
		return this.type;
	}
	/**
	 *  設定申請項目<p/>
	 *  固定塞131
	 **/
	public void setType(Integer value) {
		this.type = value;
	}

	/** 
	 * 取得保證對象資格<p/>
	 * 1：是<br/>
	 *  2：否
	 */
	public Integer getQualiCode() {
		return this.qualiCode;
	}
	/**
	 *  設定保證對象資格<p/>
	 *  1：是<br/>
	 *  2：否
	 **/
	public void setQualiCode(Integer value) {
		this.qualiCode = value;
	}

	/** 
	 * 取得申請人之票、債信情形<p/>
	 * 1：是<br/>
	 *  2：否
	 */
	public Integer getIsCreditAbnormal() {
		return this.isCreditAbnormal;
	}
	/**
	 *  設定申請人之票、債信情形<p/>
	 *  1：是<br/>
	 *  2：否
	 **/
	public void setIsCreditAbnormal(Integer value) {
		this.isCreditAbnormal = value;
	}

	/** 取得借款人出生日期 **/
	public Date getBorrowerBirthDay() {
		return this.borrowerBirthDay;
	}
	/** 設定借款人出生日期 **/
	public void setBorrowerBirthDay(Date value) {
		this.borrowerBirthDay = value;
	}

	/** 取得申貸受理日 **/
	public Date getApplyLoanDay() {
		return this.applyLoanDay;
	}
	/** 設定申貸受理日 **/
	public void setApplyLoanDay(Date value) {
		this.applyLoanDay = value;
	}

	/** 取得申請額度 **/
	public BigDecimal getApplyLoan() {
		return this.applyLoan;
	}
	/** 設定申請額度 **/
	public void setApplyLoan(BigDecimal value) {
		this.applyLoan = value;
	}

	/** 取得聯絡電話_區碼 **/
	public String getTel1() {
		return this.tel1;
	}
	/** 設定聯絡電話_區碼 **/
	public void setTel1(String value) {
		this.tel1 = value;
	}

	/** 取得聯絡電話_號碼 **/
	public String getTel2() {
		return this.tel2;
	}
	/** 設定聯絡電話_號碼 **/
	public void setTel2(String value) {
		this.tel2 = value;
	}

	/** 取得申請人行動電話區碼 **/
	public String getOwnerCellphone1() {
		return this.ownerCellphone1;
	}
	/** 設定申請人行動電話區碼 **/
	public void setOwnerCellphone1(String value) {
		this.ownerCellphone1 = value;
	}

	/** 取得申請人行動電話號碼 **/
	public String getOwnerCellphone2() {
		return this.ownerCellphone2;
	}
	/** 設定申請人行動電話號碼 **/
	public void setOwnerCellphone2(String value) {
		this.ownerCellphone2 = value;
	}

	/** 
	 * 取得有無電子郵箱<p/>
	 * 1：有<br/>
	 *  2：無
	 */
	public Integer getIsEmail() {
		return this.isEmail;
	}
	/**
	 *  設定有無電子郵箱<p/>
	 *  1：有<br/>
	 *  2：無
	 **/
	public void setIsEmail(Integer value) {
		this.isEmail = value;
	}

	/** 取得個人電子郵箱 **/
	public String getEmail() {
		return this.email;
	}
	/** 設定個人電子郵箱 **/
	public void setEmail(String value) {
		this.email = value;
	}

	/** 取得申請人_郵遞區號 **/
    public String getZip() {
        return this.zip;
    }
	/** 設定申請人_郵遞區號 **/
    public void setZip(String value) {
        this.zip = value;
    }

	/** 取得申請人_縣市 **/
    public String getCity() {
        return this.city;
    }
	/** 設定申請人_縣市 **/
    public void setCity(String value) {
        this.city = value;
    }

	/** 取得申請人_鄉鎮市區 **/
    public String getDist() {
        return this.dist;
    }
	/** 設定申請人_鄉鎮市區 **/
    public void setDist(String value) {
        this.dist = value;
    }

	/** 
	 * 取得申請人_里村名<p/>
	 * 前端只能輸入50個字(不管全半型)
	 */
	public String getVillageName() {
		return this.villageName;
	}
	/**
	 *  設定申請人_里村名<p/>
	 *  前端只能輸入50個字(不管全半型)
	 **/
	public void setVillageName(String value) {
		this.villageName = value;
	}

	/** 
	 * 取得申請人_里村<p/>
	 * 1,里<br/>
	 *  2,村
	 */
	public Integer getVillage() {
		return this.village;
	}
	/**
	 *  設定申請人_里村<p/>
	 *  1,里<br/>
	 *  2,村
	 **/
	public void setVillage(Integer value) {
		this.village = value;
	}

	/** 取得申請人_鄰 **/
	public Integer getNeighborhood() {
		return this.neighborhood;
	}
	/** 設定申請人_鄰 **/
	public void setNeighborhood(Integer value) {
		this.neighborhood = value;
	}

	/** 
	 * 取得申請人_路/街名<p/>
	 * 前端只能輸入20個字(不管全半型)
	 */
	public String getRoadName() {
		return this.roadName;
	}
	/**
	 *  設定申請人_路/街名<p/>
	 *  前端只能輸入20個字(不管全半型)
	 **/
	public void setRoadName(String value) {
		this.roadName = value;
	}

	/** 
	 * 取得申請人_路<p/>
	 * 1,(空白)<br/>
	 *  2,路<br/>
	 *  3,街
	 */
	public Integer getRoad() {
		return this.road;
	}
	/**
	 *  設定申請人_路<p/>
	 *  1,(空白)<br/>
	 *  2,路<br/>
	 *  3,街
	 **/
	public void setRoad(Integer value) {
		this.road = value;
	}

	/** 
	 * 取得申請人_段<p/>
	 * 前端只能輸入4個字(不管全半型)
	 */
	public String getSec() {
		return this.sec;
	}
	/**
	 *  設定申請人_段<p/>
	 *  前端只能輸入4個字(不管全半型)
	 **/
	public void setSec(String value) {
		this.sec = value;
	}

	/** 
	 * 取得申請人_巷<p/>
	 * 前端只能輸入8個字(不管全半型)
	 */
	public String getLane() {
		return this.lane;
	}
	/**
	 *  設定申請人_巷<p/>
	 *  前端只能輸入8個字(不管全半型)
	 **/
	public void setLane(String value) {
		this.lane = value;
	}

	/** 
	 * 取得申請人_弄<p/>
	 * 前端只能輸入8個字(不管全半型)
	 */
	public String getAlley() {
		return this.alley;
	}
	/**
	 *  設定申請人_弄<p/>
	 *  前端只能輸入8個字(不管全半型)
	 **/
	public void setAlley(String value) {
		this.alley = value;
	}

	/** 
	 * 取得申請人_號<p/>
	 * 前端只能輸入4個字(不管全半型)
	 */
	public String getNo1() {
		return this.no1;
	}
	/**
	 *  設定申請人_號<p/>
	 *  前端只能輸入4個字(不管全半型)
	 **/
	public void setNo1(String value) {
		this.no1 = value;
	}

	/** 
	 * 取得申請人_之號<p/>
	 * 前端只能輸入4個字(不管全半型)
	 */
	public String getNo2() {
		return this.no2;
	}
	/**
	 *  設定申請人_之號<p/>
	 *  前端只能輸入4個字(不管全半型)
	 **/
	public void setNo2(String value) {
		this.no2 = value;
	}

	/** 
	 * 取得申請人_樓<p/>
	 * 前端只能輸入3個字(不管全半型)
	 */
	public String getFloor1() {
		return this.floor1;
	}
	/**
	 *  設定申請人_樓<p/>
	 *  前端只能輸入3個字(不管全半型)
	 **/
	public void setFloor1(String value) {
		this.floor1 = value;
	}

	/** 
	 * 取得申請人_之樓<p/>
	 * 前端只能輸入3個字(不管全半型)
	 */
	public String getFloor2() {
		return this.floor2;
	}
	/**
	 *  設定申請人_之樓<p/>
	 *  前端只能輸入3個字(不管全半型)
	 **/
	public void setFloor2(String value) {
		this.floor2 = value;
	}

	/** 
	 * 取得申請人_室<p/>
	 * 前端只能輸入3個字(不管全半型)
	 */
	public String getRoom() {
		return this.room;
	}
	/**
	 *  設定申請人_室<p/>
	 *  前端只能輸入3個字(不管全半型)
	 **/
	public void setRoom(String value) {
		this.room = value;
	}

    /** 取得申請書鍵入者號碼 **/
    public String getImporterNo() {
        return this.importerNo;
    }
    /** 設定申請書鍵入者號碼 **/
    public void setImporterNo(String value) {
        this.importerNo = value;
    }

	/** 
	 * 取得申請書鍵入者姓名<p/>
	 * 前端只能輸10(不管全半型)
	 */
	public String getImporterName() {
		return this.importerName;
	}
	/**
	 *  設定申請書鍵入者姓名<p/>
	 *  前端只能輸10(不管全半型)
	 **/
	public void setImporterName(String value) {
		this.importerName = value;
	}

	/** 取得申請書鍵入者行動電話區碼 **/
	public String getImporterCellphone1() {
		return this.importerCellphone1;
	}
	/** 設定申請書鍵入者行動電話區碼 **/
	public void setImporterCellphone1(String value) {
		this.importerCellphone1 = value;
	}

	/** 取得申請書鍵入者行動電話號碼 **/
	public String getImporterCellphone2() {
		return this.importerCellphone2;
	}
	/** 設定申請書鍵入者行動電話號碼 **/
	public void setImporterCellphone2(String value) {
		this.importerCellphone2 = value;
	}

	/** 取得申請書鍵入者電話區碼 **/
	public String getImporterTel1() {
		return this.importerTel1;
	}
	/** 設定申請書鍵入者電話區碼 **/
	public void setImporterTel1(String value) {
		this.importerTel1 = value;
	}

	/** 取得申請書鍵入者電話號碼 **/
	public String getImporterTel2() {
		return this.importerTel2;
	}
	/** 設定申請書鍵入者電話號碼 **/
	public void setImporterTel2(String value) {
		this.importerTel2 = value;
	}

	/** 取得申請書鍵入者電話分機 **/
	public String getImporterTelExt() {
		return this.importerTelExt;
	}
	/** 設定申請書鍵入者電話分機 **/
	public void setImporterTelExt(String value) {
		this.importerTelExt = value;
	}

	/** 取得申請書鍵入者電子信箱 **/
	public String getImporterEmail() {
		return this.importerEmail;
	}
	/** 設定申請書鍵入者電子信箱 **/
	public void setImporterEmail(String value) {
		this.importerEmail = value;
	}

	/** 取得保證案號 **/
	public BigDecimal getGrntPaper() {
		return this.grntPaper;
	}
	/** 設定保證案號 **/
	public void setGrntPaper(BigDecimal value) {
		this.grntPaper = value;
	}

	/** 取得回覆日 **/
	public Timestamp getReceiveDay() {
		return this.receiveDay;
	}
	/** 設定回覆日 **/
	public void setReceiveDay(Timestamp value) {
		this.receiveDay = value;
	}

	/** 取得回覆結果 **/
	public String getDataStatus() {
		return this.dataStatus;
	}
	/** 設定回覆結果 **/
	public void setDataStatus(String value) {
		this.dataStatus = value;
	}

    /** 取得檔案產生日 **/
    public Timestamp getBatchDate() {
        return this.batchDate;
    }
    /** 設定檔案產生日 **/
    public void setBatchDate(Timestamp value) {
        this.batchDate = value;
    }

    /** 取得處理說明 **/
    public String getDescription() {
        return this.description;
    }
    /** 設定處理說明 **/
    public void setDescription(String value) {
        this.description = value;
    }

	/** 取得版本 **/
	public Integer getVersion() {
		return this.version;
	}

	/** 設定版本 **/
	public void setVersion(Integer value) {
		this.version = value;
	}
}
