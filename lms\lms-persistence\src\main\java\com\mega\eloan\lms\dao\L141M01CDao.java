/* 
 * L141M01CDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L141M01C;


/** 共同借款人檔 **/
public interface L141M01CDao extends IGenericDao<L141M01C> {

	L141M01C findByOid(String oid);

	List<L141M01C> findByMainId(String mainId);

	L141M01C findByUniqueKey(String mainId, String custId, String dupNo);

	List<L141M01C> findByIndex01(String mainId, String custId, String dupNo);

	List<L141M01C> findByCntrNo(String CntrNo);
	
	List<L141M01C> findByCustIdDupId(String custId,String DupNo);
}