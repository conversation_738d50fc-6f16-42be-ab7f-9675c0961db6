/* 
 * L140S06ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140S06A;

/** 條件變更資訊 **/
public interface L140S06ADao extends IGenericDao<L140S06A> {

	L140S06A findByOid(String oid);
	
	List<L140S06A> findByMainId(String mainId);

	List<L140S06A> findByIndex01(String mainId);
	
	/**
	 * 撈出該額度明細表中某項條例
	 * @param mainId
	 * @param intReg
	 * @return
	 */
	L140S06A findByIntReg(String mainId, String intReg);
}