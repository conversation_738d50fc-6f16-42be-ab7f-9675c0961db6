/* 
 * RetrialDocStatusEnum.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.flow.enums;

/**<pre>
 * 文件的狀態表(共用)
 * 若要取得各系統使用的文件狀態代碼，請使用其系統所定義之enum
 * 
 * EX: 編製中 010
 * 第一碼：系統碼
 *   0.共用 ,1.擔保品管理系統,2.徵信管理系統,3.企金授信管理系統,4.消金授信管理系統,5.逾放催收管理系統,6.資料建檔
 * 第二碼：類別碼(依據notes文件狀態)
 *   0.待收案件,1.編製中,2.待覆核,3.已核准,4.婉卻	,7.待補件或撤件
 * 第三碼：單位碼&自訂義
 *   0.預設狀態(保留關鍵字),<PERSON><PERSON>分行,H.授管處,C.總行,<PERSON><PERSON>區中心,O<PERSON>海外
 * 
 * </pre>
 * @since  2011/4/29
 * <AUTHOR> @version <ul>
 *           <li>2011/4/29,new
 *           <li>2011/8/12,override toString()以便Enum.toString()可取得code值
 *          </ul>
 */
public enum RetrialDocStatusEnum {

	// 覆審預約單
	預約單_未處理("1"),
	預約單_處理成功("3"),
	預約單_處理失敗("4"),
	
	//共用0
	待收案件("000"),
	編製中("010"),
	待覆核("020"),
	已核准("030"),
	婉卻("040"),
	已覆核未核定("050"),
	已覆核已核定("060"),
	待補件或撤件("070"),
	待受檢單位回覆("080"),
	編製完成("090"),
	已產生覆審名單報告檔("0A0"),
	
	//分行B
	分行_待收案件("00B"),
	分行_編製中("01B"),
	分行_待覆核("02B"),
	分行_已覆核未核定("03B"),
	分行_已覆核已核定("0AB"),
	
	分行_婉卻("04B"),
	分行_待補件或撤件("07B"),
	
	//授管處H
	授管處_待收案件("00H"),
	授管處_編製中("01H"),
	授管處_待覆核("02H"),
	授管處_已核准("03H"),
	授管處_婉卻("04H"),
	授管處_待補件或撤件("07H"),
	//總行C
	總行_待收案件("00C"),
	總行_編製中("01C"),
	總行_待覆核("02C"),
	總行_已核准("03C"),
	總行_婉卻("04C"),
	總行待待補件或撤件("07C"),
	//區中心A
	區中心_待收案件("00A"),
	區中心_編製中("01A"),
	區中心_待覆核("02A"),
	區中心_已核准("03A"),
	區中心_婉卻("04A"),
	區中心_待補件或撤件("07A"),
	
	//海外O
//	海外_待收案件("00O"),
//	海外_編製中("01O"),
//	海外_待覆核("02O"),
//	海外_已核准("03O"),
//	海外_婉卻("04O"),
//	海外_已覆核未核定("05O"),
//	海外_已覆核已核定("06O"),
//	海外_待補件或撤件("07O"),
//	海外_已產生覆審名單報告檔("0AO"),
		
	//改使用中文...(以下英文暫時保留..)
	/** 編製中 **/
	DOC_EDITING("010"),
	/** 待覆核 **/
	WAIT_APPROVE("020");

	private String code;
	RetrialDocStatusEnum(String code){
		this.code = code;
	}
	
	public String getCode() {
        return code;
    }
	
	public boolean isEquals(Object other){
		if (other instanceof String){
			return code.equals(other);
		}else{
			return super.equals(other);
		}
	}
	
	public static RetrialDocStatusEnum getEnum(String code){
		for (RetrialDocStatusEnum enums : RetrialDocStatusEnum.values()){
			if (enums.isEquals(code)){
				return enums;
			}
		}
		return null;
	}
	
	@Override
	public String toString() {
		return code;
	}
	
}
