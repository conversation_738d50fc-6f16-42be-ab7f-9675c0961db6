/* 
 * L260S01D.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 公司訪問紀錄表主檔  **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L260S01D", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId"}))
public class L260S01D extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** oid **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 
	 * 文件編號<p/>
	 * 存放L260M01D.oid
	 */
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 客戶名稱 **/
	@Size(max=500)
	@Column(name="VISITCOMPNAME", length=500, columnDefinition="VARCHAR(500)")
	private String visitCompName;

	/** 訪問方式 **/
	@Size(max=1)
	@Column(name="VISITWAY", length=1, columnDefinition="VARCHAR(1)")
	private String visitWay;

	/** 訪問時間 **/
	@Temporal(TemporalType.DATE)
	@Column(name="VISITDT", columnDefinition="DATE")
	private Date visitDt;

	/** 訪問地點 **/
	@Size(max=120)
	@Column(name="VISITPLACE", length=120, columnDefinition="VARCHAR(120)")
	private String visitPlace;

	/** 受訪人職稱 **/
	@Size(max=120)
	@Column(name="VISITORJOBTITLE", length=120, columnDefinition="VARCHAR(120)")
	private String visitorJobTitle;

	/** 受訪人姓名 **/
	@Size(max=120)
	@Column(name="VISITORNAME", length=120, columnDefinition="VARCHAR(120)")
	private String visitorName;

	/** 受訪人電話 **/
	@Size(max=20)
	@Column(name="VISITORPHONE", length=20, columnDefinition="VARCHAR(20)")
	private String visitorPhone;

	/** 單位主管 **/
	@Size(max=6)
	@Column(name="UNITMGR_S01D", length=6, columnDefinition="CHAR(6)")
	private String unitMgr_S01D;

	/** 帳戶管理員 **/
	@Size(max=6)
	@Column(name="ACCOUNTMGR_S01D", length=6, columnDefinition="CHAR(6)")
	private String accountMgr_S01D;

	/** 文件亂碼 **/
	@Size(max=32)
	@Column(name="RANDOMCODE_S01D", length=32, columnDefinition="CHAR(32)")
	private String randomCode_S01D;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 電子表單列印套版版本ID **/
	@Size(max=32)
	@Column(name="RPTID_S01D", length=32, columnDefinition="VARCHAR(32)")
	private String rptId_S01D;

	/** 
	 * 刪除註記<p/>
	 * 文件刪除時使用(非立即性刪除)
	 */
	@Column(name="DELETEDTIME", columnDefinition="TIMESTAMP")
	private Timestamp deletedTime;

	/** 取得oid **/
	public String getOid() {
		return this.oid;
	}
	/** 設定oid **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 
	 * 取得文件編號<p/>
	 * 存放L260M01D.oid
	 */
	public String getMainId() {
		return this.mainId;
	}
	/**
	 *  設定文件編號<p/>
	 *  存放L260M01D.oid
	 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得客戶名稱 **/
	public String getVisitCompName() {
		return this.visitCompName;
	}
	/** 設定客戶名稱 **/
	public void setVisitCompName(String value) {
		this.visitCompName = value;
	}

	/** 取得訪問方式 **/
	public String getVisitWay() {
		return this.visitWay;
	}
	/** 設定訪問方式 **/
	public void setVisitWay(String value) {
		this.visitWay = value;
	}

	/** 取得訪問時間 **/
	public Date getVisitDt() {
		return this.visitDt;
	}
	/** 設定訪問時間 **/
	public void setVisitDt(Date value) {
		this.visitDt = value;
	}

	/** 取得訪問地點 **/
	public String getVisitPlace() {
		return this.visitPlace;
	}
	/** 設定訪問地點 **/
	public void setVisitPlace(String value) {
		this.visitPlace = value;
	}

	/** 取得受訪人職稱 **/
	public String getVisitorJobTitle() {
		return this.visitorJobTitle;
	}
	/** 設定受訪人職稱 **/
	public void setVisitorJobTitle(String value) {
		this.visitorJobTitle = value;
	}

	/** 取得受訪人姓名 **/
	public String getVisitorName() {
		return this.visitorName;
	}
	/** 設定受訪人姓名 **/
	public void setVisitorName(String value) {
		this.visitorName = value;
	}

	/** 取得受訪人電話 **/
	public String getVisitorPhone() {
		return this.visitorPhone;
	}
	/** 設定受訪人電話 **/
	public void setVisitorPhone(String value) {
		this.visitorPhone = value;
	}

	/** 取得單位主管 **/
	public String getUnitMgr_S01D() {
		return this.unitMgr_S01D;
	}
	/** 設定單位主管 **/
	public void setUnitMgr_S01D(String value) {
		this.unitMgr_S01D = value;
	}

	/** 取得帳戶管理員 **/
	public String getAccountMgr_S01D() {
		return this.accountMgr_S01D;
	}
	/** 設定帳戶管理員 **/
	public void setAccountMgr_S01D(String value) {
		this.accountMgr_S01D = value;
	}

	/** 取得文件亂碼 **/
	public String getRandomCode_S01D() {
		return this.randomCode_S01D;
	}
	/** 設定文件亂碼 **/
	public void setRandomCode_S01D(String value) {
		this.randomCode_S01D = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 取得電子表單列印套版版本ID **/
	public String getRptId_S01D() {
		return this.rptId_S01D;
	}
	/** 設定電子表單列印套版版本ID **/
	public void setRptId_S01D(String value) {
		this.rptId_S01D = value;
	}

	/** 
	 * 取得刪除註記<p/>
	 * 文件刪除時使用(非立即性刪除)
	 */
	public Timestamp getDeletedTime() {
		return this.deletedTime;
	}
	/**
	 *  設定刪除註記<p/>
	 *  文件刪除時使用(非立即性刪除)
	 **/
	public void setDeletedTime(Timestamp value) {
		this.deletedTime = value;
	}
}
