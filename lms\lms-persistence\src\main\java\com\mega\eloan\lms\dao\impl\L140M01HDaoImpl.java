/* 
 * L140M01HDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao.impl;

import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L140M01HDao;
import com.mega.eloan.lms.model.L140M01H;

/** 額度費率明細檔 **/
@Repository
public class L140M01HDaoImpl extends LMSJpaDao<L140M01H, String>
	implements L140M01HDao {

	@Override
	public L140M01H findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}
	@Override
	public List<L140M01H> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L140M01H> list = createQuery(L140M01H.class,search).getResultList();
		return list;
	}
	@Override
	public L140M01H findByUniqueKey(String mainId,Integer rateSeq){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "rateSeq", rateSeq);
		return findUniqueOrNone(search);
	}
	@Override
	public List<L140M01H> findByMainIdAndRateSeq(String mainId, Integer rateSeq) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "rateSeq", rateSeq);
		List<L140M01H> list = createQuery(L140M01H.class,search).getResultList();
		return list;
	}
}