package com.mega.eloan.lms.lms.service;

/* 
 * LMS7840Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
import java.util.List;

import com.mega.eloan.lms.model.C900M01D;

/**
 * <pre>
 * 簽報案件查詢
 * </pre>
 * 
 * @since 2011/12/12
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/12/12,REX,new
 *          </ul>
 */
public interface LMS7840Service {

	public List<C900M01D> queryC900m01ds();

}