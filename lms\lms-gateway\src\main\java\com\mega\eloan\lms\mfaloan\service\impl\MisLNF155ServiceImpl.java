package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.mfaloan.service.MisLNF150Service;
import com.mega.eloan.lms.mfaloan.service.MisLNF155Service;

@Service
public class MisLNF155ServiceImpl extends AbstractMFAloanJdbc implements
		MisLNF155Service {

	@Override
	public List<Map<String, Object>> findSeasonData(String thisSeason, String lastSeason) {

		return getJdbc().queryForList("LNF155.findSeasonData",
				new String[] { thisSeason, thisSeason, lastSeason, lastSeason });

	}

}
