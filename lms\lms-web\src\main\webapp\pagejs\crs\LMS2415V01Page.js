$(function(){
	var gridShowBr = false;
	if( $("#gridShowBr") && $("#gridShowBr").length > 0  && $("#gridShowBr").val()==="Y"){
		gridShowBr = true;
	}
	var _m_chkbox = false;
	if($("#buttonPanel").find("#btnPrint").is("button")){
		_m_chkbox = true;
	}
	if($("#buttonPanel").find("#btnAllSend").is("button")){
		_m_chkbox = true;
	}
	
	var my_colModel = [];	
	my_colModel.push({ name: 'oid', hidden: true });
	my_colModel.push({ name: 'mainId', hidden: true });
	my_colModel.push({ name: 'dupNo', hidden: true });
	if(gridShowBr){
		my_colModel.push({
			colHeader: i18n.abstracteloan['doc.branchName'],//"分行名稱",
            name: 'ownBrId',
            width: 80
	    });
	}
	my_colModel.push({
        colHeader: i18n.lms2415v01["C241M01a.docStatus"],// 文件狀態
        align: "left",
        width: 100, // 設定寬度
        sortable: true, // 是否允許排序
        name: 'docStatus' // col.id
    });
	my_colModel.push({
        colHeader: i18n.lms2415v01["C241M01a.retrialDate"],// 覆審日期
        align: "center",
        width: 100, // 設定寬度
        sortable: true, // 是否允許排序
        name: 'retrialDate' // col.id
    });
	my_colModel.push({
        colHeader: i18n.lms2415v01["C241M01a.custId"], // 統一編號
        align: "left",
        width: 80, // 設定寬度
        sortable: true, // 是否允許排序
        formatter: 'click',
        onclick: BOM,
        name: 'custId' // col.id
    });
	my_colModel.push({
        colHeader: i18n.lms2415v01["C241M01a.dupNo"], // 重複序號
        align: "center",
        width: 60, // 設定寬度
        sortable: true, // 是否允許排序
        name: 'dupNo' // col.id
    });
	my_colModel.push({
        colHeader: i18n.lms2415v01["C241M01a.custName"], // 客戶名稱
        align: "left",
        width: 100, // 設定寬度
        sortable: true, // 是否允許排序
        name: 'custName' // col.id
    });
	my_colModel.push({
        colHeader: i18n.lms2415v01["C241M01a.condition"], // 覆審意見
        align: "left",
        width: 100, // 設定寬度
        sortable: false, // 是否允許排序
        name: 'condition' // col.id
    });
	my_colModel.push({
        colHeader: i18n.lms2415v01["C241M01a.branchComm"], // 洽辦情形
        align: "left",
        width: 100, // 設定寬度
        sortable: false, // 是否允許排序
        name: 'branchComm' // col.id
    });
	my_colModel.push({
        colHeader: i18n.lms2415v01["C241M01a.lastRetrialDate"],
        // // 上次覆審日期
        align: "center",
        width: 100, // 設定寬度
        sortable: true, // 是否允許排序
        name: 'lastRetrialDate' // col.id
    });
	//---
    var grid = $("#gridview").iGrid({
        rownumbers: true,
        handler: 'lms2415gridhandler',
        height: 350, // 設定高度
        sortname: 'retrialDate|ownBrId|custId', // 預設排序
        sortorder: "desc|asc|asc",
        multiselect: _m_chkbox,
        shrinkToFit: false,
        rowNum: 15,
        postData: {
            formAction: "queryC241m01a",
            docStatus: viewstatus
        },
        colModel: my_colModel,
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = $("#gridview").getRowData(rowid);
            BOM(null, null, data);
        }
    
    });
    
    
    $("#buttonPanel").find("#btnView").click(function(){
        var id = $("#gridview").getGridParam('selrow');
        if (!id) {
            // action_004=請先選擇需「調閱」之資料列
            return CommonAPI.showMessage(i18n.def["action_004"]);
        }
        var result = $("#gridview").getRowData(id);
        BOM(null, null, result);
        //openDoc(null, null, result);
    }).end().find("#btnFilter").click(function(){
        //此程式 再LMS2415FilterPanel.html
        FilterAction.openBox();
    }).end().find("#btnAllSend").click(function(){
    	var rowId_arr = $("#gridview").getGridParam('selarrrow');
      	var oid_arr = [];
      	for (var i = 0; i < rowId_arr.length; i++) {
      		 var data = $("#gridview").getRowData(rowId_arr[i]);
      		 oid_arr.push(data.oid);
        }

      	if(oid_arr.length==0){
      		API.showMessage(i18n.def.action_005);//請先選取一筆以上之資料列
      		return;
      	}

      	var _id = "_div_btnAllSend";
		var _form = _id+"_form";
		if ($("#"+_id).length == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");
			dyna.push("<form id='"+_form+"'>");
			dyna.push("		<p><label><input type='radio' name='decisionExpr' value='1' class='required' />核定</label></p>");
			dyna.push("</form>");
			
			dyna.push("</div>");
			
		     $('body').append(dyna.join(""));
		}
		//clear data
		$("#"+_form).reset();
		
		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
	        title: i18n.def["confirmApprove"],
	        width: 380, height: 180, align: "center", valign: "bottom", modal: false, i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if (!$("#"+_form).valid()) {
                        return;
                    }
                    $.thickbox.close();
                    
                    var val = $("#"+_form).find("[name='decisionExpr']:checked").val();
                    var opts = {};
                    if(val=="1"){
                    	var cnt = 0;
                      	var ok_cnt = 0;
                		var target = oid_arr.length;
                			
                		var theQueue = $({});
                		$.each(oid_arr,function(k, v_oid) {                                            			
                			theQueue.queue('myqueue', function(next) {
                				$.ajax({
                		            type: "POST",
                		            handler: "lms2411m01formhandler", action: "flowAction",
                		            data:{ 'mainOid': v_oid, 'mainDocStatus':'050','addMetaDesc':'Y',  'decisionExpr':'to_已覆核已核定'}                 
                				}).done(function(json){
                					ok_cnt++;
                		        }).always(function(){//用 complete(不論done, fail)
                		        	cnt++;
                  					if(cnt==target){  						
                  						$("#gridview").trigger("reloadGrid");
                  						if(cnt > ok_cnt){
                  							API.showMessage("請開啟個別的覆審報告表 ，執行「編製完成並上傳至覆審控制檔」");
                  						}
                      				}
                  					//---
                  					//把 next() 寫在 finish 的 callback 裡
                  					//才會 1個 url 抓到 response 後,再get下1個
                  					//不然會1次跳 N 個出來
                  					next();
                		        });				
                			});                                            			 
                		});
                		theQueue.dequeue('myqueue');
                    }
                   
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
	    });		
      	
    }).end().find("#btnPrint").click(function(){
    	var rowId_arr = $("#gridview").getGridParam('selarrrow');
      	var oid_arr = [];
      	for (var i = 0; i < rowId_arr.length; i++) {
      		 var data = $("#gridview").getRowData(rowId_arr[i]);
      		 oid_arr.push(data.oid+"^"+data.mainId);
        }

      	if(oid_arr.length==0){
      		API.showMessage(i18n.def.action_006);//請先選擇需「列印」之資料列
      		return;
      	}
      	printC241M01A(oid_arr.join("|"));
    }).end().find("#btnBatchApproved").click(function(){//受檢行-待覆核
    	proc_btnBatchApproved();
    }).end().find("#btnAdd").click(function(){//新增
    	thickBoxOpenADD();
//    	AddCustAction.open({
//    		handler: "lms2415m01formhandler",
//			action : 'produceNewC241M01A',
//			callback : function(response){
//				//~~~~~~
//				$.thickbox.close();
//				$("#gridview").trigger("reloadGrid");
//				BOM(null, null, response.C241M01AForm);
//            }
//        });
    }).end().find("#btnDelete").click(function(){
        var $gridview = $("#gridview");
        var ids = $gridview.getGridParam('selrow');
        
        if (!ids) {// TMMDeleteError=請先選擇需修改(刪除)之資料列
            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
        }
        var oids = [];
        for (var i in ids) {
            oids.push($gridview.getRowData(ids).oid);
        }
        // confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
            	$.ajax({
                    type: "POST",
                    handler: "lms2415m01formhandler",
                    data: {
                        formAction: "queryC240M01A",
                        oid: oids
                    }
                    }).done(function(json){
                    	if (json.c240m01aMainIds) {
                    		$("#nckdflagForm").reset();
                            $("#openbox3").thickbox({
                                //不覆審選項
                                title: i18n.lms2415v01['chooseNckdflag'],
                                width: 550,
                                height: 400,
                                align: "center",
                                valign: "bottom",
                                modal: false,
                                i18n: i18n.def,
                                open: function(){
                                	if ($("#NCKDFLAG")) {
                                        var obj = CommonAPI.loadCombos("lms2405m01_NckdFlag");
                                        var NCKDFLAG = $("#NCKDFLAG");
                                        NCKDFLAG.setItems({
                                            item: obj.lms2405m01_NckdFlag,
                                            format: "{value} {key}",
                                            size: 1
                                        });
                                    }
                                },
                                buttons: {
                                    "sure": function(){
                                        if (($("[name=NCKDFLAG]:checked").val() ? false : true)) {
                                            CommonAPI.showMessage(i18n.lms2415v01["chooseNckdflag"]);
                                            return;
                                        }
                                        $.ajax({
                                            type: "POST",
                                            handler: "lms2405m01formhandler",
                                            data: {
                                                formAction: "saveNoCTL",
                                                oid: oids,
                                                mainId: json.c240m01aMainIds,
                                                reason: $("[name=NCKDFLAG]:checked").parent().html()
                                            }
                                            }).done(function(json){
                                            	$("#gridview").trigger("reloadGrid");
                                        });
                                        $.ajax({
                                			handler: "lms2415m01formhandler",
                                			action: "deleteMarkC241M01A",
                                			data: {
                                				oids: oids
                                			}
                                			}).done(function(obj){
                                				$("#gridview").trigger("reloadGrid");
                                		});
                                        $.thickbox.close();
                                    },
                                    "cancel": function(){
                                        $.thickbox.close();
                                    }
                                }
                            });
                    	} else {
                    		$.ajax({
                    			handler: "lms2415m01formhandler",
                    			action: "deleteMarkC241M01A",
                    			data: {
                    				oids: oids
                    			}
                    			}).done(function(obj){
                    				$("#gridview").trigger("reloadGrid");
                    		});
                    }
                });
            }
        });
    });
    
    function thickBoxOpenADD(cellvalue, options, rowObject, showMsg){
        $("#tabForm").reset();
        var test2 = $("#lms170new").thickbox({ // '新增覆審報告表',
            title: i18n.lms2415v01['C241M01a.insertData'],
            width: 450,
            height: 60,
            align: 'center',
            valign: 'bottom',
            modal: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(cellvalue, options, rowObject, showMsg, oid, id){
                
                    // 驗證
                    if ($("#tabForm").find("#custId").val() == "" ||
                    		$("#tabForm").find("#duoNo").val() == "") {
                        // val.required=此為必填欄位.
                        return CommonAPI.showMessage(i18n.def["val.required"]);
                    }
                    $.ajax({
                    	handler: "lms2415m01formhandler",
            			action : 'produceNewC241M01A',
                        type: "POST",
                        dataType: "json",
                        data: $.extend($("#tabForm").serializeData(), {
                            mainOid: oid,
                            showMsg: false
                        })
                        }).done(function(responseData){
                        	$.thickbox.close();
                        	BOM(null, null, responseData.C241M01AForm);
                        	$("#gridview").trigger("reloadGrid");
                        }).fail(function(responseData){{}
                    });
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    };
    
    var flowAction = function(opts){
		return $.ajax({
            type: "POST",
            handler: 'lms2411m01formhandler', action: "flowAction",
            data:( opts||{} )                
            }).done(function(json){
        });
	}
    
    function gridSelectRowDataIdxArr(){
		return grid.getGridParam('selarrrow');		
	}
    
    function gridSelectOidArr(){
    	var rowId_arr = gridSelectRowDataIdxArr();
		var oid_arr = [];
   	 	for (var i = 0; i < rowId_arr.length; i++) {
			var data = grid.getRowData(rowId_arr[i]);
			oid_arr.push(data.oid);    			
        }
   	 	return oid_arr;
    }
    
    function proc_btnBatchApproved(){
		var oid_arr = gridSelectOidArr();
      	if(oid_arr.length==0){
      		API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
      		return;
      	}
		var _id = "_div_btnBatchApproved";
		var _form = _id+"_form";
		if ($("#"+_id).length == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");
			dyna.push("<form id='"+_form+"'>");
			dyna.push("		<p><label><input type='radio' name='decisionExpr' value='1' class='required' />"+i18n.lms2411m01["ui_lms2411.msg20"]+"</label></p>");
			dyna.push("		<p><label><input type='radio' name='decisionExpr' value='2' class='required' />"+i18n.lms2411m01["ui_lms2411.msg19"]+"</label></p>");
			dyna.push("		<p><label><input type='radio' name='decisionExpr' value='3' class='required' />"+i18n.lms2411m01["ui_lms2411.msg18"]+"</label></p>");
			dyna.push("</form>");
			
			dyna.push("</div>");
			
		     $('body').append(dyna.join(""));
		}
		//clear data
		$("#"+_form).reset();
		
		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
	        title: i18n.def["confirmApprove"],
	        width: 380, height: 180, align: "center", valign: "bottom", modal: false, i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if (!$("#"+_form).valid()) {
                        return;
                    }
                    var val = $("#"+_form).find("[name='decisionExpr']:checked").val();
                    var opts = {};
                    if(val=="1"){
                    	opts = {'decisionExpr':'to_已覆核未核定'};
                    }else if(val=="2"){
                    	opts = {'decisionExpr':'backto_編製中_分行端'};
                    }else if(val=="3"){
                    	opts = {'decisionExpr':'backto_編製中_覆審組'};
                    }
                    
                    if(true){
                    	var theQueue = $({});
                		var cnt = 0;
                		$.each(oid_arr,function(k, _oid) {                                            			
                			theQueue.queue('myqueue', function(next) {
                				                            	 
                				flowAction($.extend(opts, {'mainOid':_oid, 'mainDocStatus': viewstatus})
                				).always(function(){//用 complete(不論done, fail)
                		        	cnt++;
                  					
                		        	grid.trigger("reloadGrid");
                      				if(cnt==oid_arr.length){
                      					$.thickbox.close();
                      					API.showMessage(i18n.def.runSuccess);
                      				}
                  					//---
                  					next();
                		        });				
                			});                                            			 
                		});
                		theQueue.dequeue('myqueue');
                    }
                   
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
	    });
	}
	
});


/**
 * 同時印多份: 
 * oid_arr.push(data.oid+"^"+data.mainId);
 * rptOid: oid_arr.join("|")
 */
function printC241M01A(rptOid){
	var _id = "_div_printC241M01A";	
	var _form = _id+"_form";
	var rdoName = "rdo_printC241M01A";
	var option = {
		'X':'經副襄理',
		'Y':'正副營運長'
	};
	if ($("#"+_id).length == 0){
		var dyna = [];
		dyna.push("<div id='"+_id+"' style='display:none;' >");
		dyna.push("<form id='"+_form+"'>");
		$.each(option, function(k, v) { 
			dyna.push("   <p><label><input type='radio' name='"+rdoName+"' value='"+k+"' class='required cbox' />"+v+"</label></p>"); 
        });
		dyna.push("</form>");
		dyna.push("</div>");
	    $('body').append(dyna.join(""));
	}

	$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
       title: "",
       width: 250,
       height: 160,
       align: "center",
       valign: "bottom",
       modal: false,
       i18n: i18n.def,
       buttons: {
           "sure": function(){
        	   if (!$("#"+_form).valid()) {
                   return;
               }
               var L5DescFlag = $("#"+_form).find("[name='"+rdoName+"']:checked").val();
               
               $.form.submit({
                   url: "../simple/FileProcessingService",
                   target: "_blank",
                   data: {
                       'rptOid': rptOid,
                       'fileDownloadName': "lms2411r01.pdf",
                       'L5DescFlag' : L5DescFlag,
                       serviceName: "lms2411r01rptservice"            
                   }
               });
               $.thickbox.close();
           }
       }
	});
}

// ======================================================================================================
function BOM(cellvalue, options, rowObject){ // 連結XXXXM01.html視窗
	$.ajax({
	      type: "POST",
	      handler: "lms2405m01formhandler",
	      action: "overSeaProgram",
	      data: {}
	      }).done(function(json){
	    	if(json.overSeaProgram){
	    		
	$.form.submit({
        url: '../crs/LMS2415M01Page/01',
        data: {
            formAction: "queryL2415m01a",
            mainId: rowObject.mainId,
            mainDocStatus: viewstatus,
            mainOid: rowObject.oid, // mainOid 驗證文件狀態 ,權限按鈕顯現
            custId: rowObject.custId,
            dupNo: rowObject.dupNo
        
        },
        target: rowObject.oid
    });
	
	    	}else{
	    		var postData = {
            		'mainOid': rowObject.oid, 
            		'mainId': rowObject.mainId,
            		'mainDocStatus': viewstatus
            	}
            	$.form.submit({ url:'../crs/lms2411m01/04', data:postData, target:rowObject.oid});
	    	}      
    });
}

//=====================================================================================

