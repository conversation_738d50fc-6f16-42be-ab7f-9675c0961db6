package com.mega.eloan.lms.lms.report.impl;

import java.util.LinkedHashMap;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.lms.base.service.AbstractReportService;
import com.mega.eloan.lms.lms.service.LMS1205Service;
import com.mega.eloan.lms.model.L120S01A;
import com.mega.eloan.lms.model.L120S01D;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.ReportGenerator;

/**
 * 產生授信 有 無 不適用 銀行法,44,45所稱與本行有利害關係人PDF
 * 
 * <AUTHOR>
 * 
 */
@Service("lms1205r13rptservice")
public class LMS1205R13RptServiceImpl extends AbstractReportService {

	@Resource
	LMS1205Service service1205;

	@Resource
	BranchService branch;

	@Override
	public String getReportTemplateFileName() {
		// zh_TW: 正體中文
		// zh_CN: 簡體中文
		// en_US: 英文
		Locale locale = LocaleContextHolder.getLocale();
		if (locale == null)
			locale = Locale.getDefault();
		return "report/lms/LMS1205R13_" + locale.toString() + ".rpt";
	}

	/*
	 * (non-Javadoc) 設定需要傳入RPT參數
	 * 
	 * @see
	 * com.mega.eloan.lms.base.service.AbstractReportService#setReportData(com
	 * .mega.eloan.lms.base.report.ReportGenerator,
	 * org.apache.wicket.PageParameters)
	 */
	@Override
	public void setReportData(ReportGenerator reportTools, PageParameters params) {
		Properties prop = null;
		// zh_TW: 正體中文
		// zh_CN: 簡體中文
		// en_US: 英文
		Locale locale = null;
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String custId = params.getString("custId");
		String dupNo = params.getString("dupNo");
		String showDataType = params.getString("showDataType");
		L120S01A l120s01a = null;
		L120S01D l120s01d = null;
		try {
			prop = MessageBundleScriptCreator
					.getComponentResource(LMS1205R01RptServiceImpl.class);

			locale = LocaleContextHolder.getLocale();
			if (locale == null)
				locale = Locale.getDefault();

			// L120S01A．主要借款人主檔
			l120s01a = service1205.findL120s01aByUniqueKey(mainId, custId,
					dupNo);
			if (l120s01a == null) {
				l120s01a = new L120S01A();
			}
			l120s01d = service1205.findL120s01dByUniqueKey(mainId, custId,
					dupNo);
			if (l120s01d == null) {
				l120s01d = new L120S01D();
			}
			rptVariableMap.put("SHOWDATATYPE", showDataType);
			rptVariableMap = this.setL120S01AData(rptVariableMap, l120s01a,
					prop);

			rptVariableMap = this.setL120S01DData(rptVariableMap, l120s01d,
					prop);

			// this.generator.setLang(java.util.Locale.TAIWAN);
			reportTools.setLang(locale);
			reportTools.setVariableData(rptVariableMap);
			// reportTools.checkVariableExist("C:/test.txt", rptVariableMap);
			// this.reportTools.setTestMethod(true);
		}finally{
			
		}
	}

	/**
	 * 存放L120S01A資料
	 * 
	 * @param rptVariableMap
	 *            存放變數MAP
	 * @param l12001a
	 *            L120S01A資料
	 * @param prop
	 *            Properties
	 * @return Map<String, String> 存放變數MAP
	 */
	private Map<String, String> setL120S01AData(
			Map<String, String> rptVariableMap, L120S01A l120s01a,
			Properties prop) {
		rptVariableMap.put("L120S01A.CUSTID",
				Util.nullToSpace(l120s01a.getCustId()));
		rptVariableMap.put("L120S01A.DUPNO",
				Util.nullToSpace(l120s01a.getDupNo()));
		rptVariableMap.put("L120S01A.CUSTNAME",
				Util.nullToSpace(l120s01a.getCustName()));
		return rptVariableMap;
	}

	/**
	 * 存放L120S01D資料
	 * 
	 * @param rptVariableMap
	 *            存放變數MAP
	 * @param l12001d
	 *            L120S01D資料
	 * @param prop
	 *            Properties
	 * @return Map<String, String> 存放變數MAP
	 */
	private Map<String, String> setL120S01DData(
			Map<String, String> rptVariableMap, L120S01D l120s01d,
			Properties prop) {
		rptVariableMap.put("L120S01D.MBDATE",
				Util.nullToSpace(TWNDate.toAD(l120s01d.getMbDate())));
		rptVariableMap.put("L120S01D.MHDATE",
				Util.nullToSpace(TWNDate.toAD(l120s01d.getMhDate())));
		rptVariableMap.put("L120S01D.MHRLT44DSCR",
				Util.nullToSpace(l120s01d.getMhRlt44Dscr()));
		rptVariableMap.put("L120S01D.MHRLT45DSCR",
				Util.nullToSpace(l120s01d.getMhRlt45Dscr()));
		rptVariableMap.put("L120S01D.FCTMHRLTDSCR",
				Util.nullToSpace(l120s01d.getFctMhRltDscr()));
		rptVariableMap.put("L120S01D.MBRLTDSCR",
				Util.nullToSpace(l120s01d.getMbRltDscr()));

		rptVariableMap.put("L120S01D.MBRLT",
				Util.nullToSpace(prop.get("COMMON.CON" + l120s01d.getMbRlt())));
		rptVariableMap.put("L120S01D.MHRLT44", Util.nullToSpace(prop
				.get("COMMON.CON" + l120s01d.getMhRlt44())));
		rptVariableMap.put("L120S01D.MHRLT45", Util.nullToSpace(prop
				.get("COMMON.CON" + l120s01d.getMhRlt45())));

		return rptVariableMap;
	}
}
