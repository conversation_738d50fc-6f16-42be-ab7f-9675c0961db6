/* 
 * L820M01WDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L820M01WDao;
import com.mega.eloan.lms.model.L820M01W;

/** RPA發查明家事公告細檔 **/
@Repository
public class L820M01WDaoImpl extends LMSJpaDao<L820M01W, String>
	implements L820M01WDao {

	@Override
	public L820M01W findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L820M01W> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L820M01W> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public L820M01W findByUniqueKey(String mainId, String branchNo, String empNo, String dataCustomerNo){
		ISearch search = createSearchTemplete();
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (branchNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "branchNo", branchNo);
		if (empNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "empNo", empNo);
		if (dataCustomerNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dataCustomerNo", dataCustomerNo);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<L820M01W> findByIndex01(String mainId, String branchNo, String empNo, String dataCustomerNo){
		ISearch search = createSearchTemplete();
		List<L820M01W> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (branchNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "branchNo", branchNo);
		if (empNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "empNo", empNo);
		if (dataCustomerNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dataCustomerNo", dataCustomerNo);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L820M01W> findBy(String mainId, String custId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dataCustomerNo", custId);
		search.addOrderBy("queryTime", true);
		List<L820M01W> list = createQuery(search).getResultList();
		return list;
	}
}