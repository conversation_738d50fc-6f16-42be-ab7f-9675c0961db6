package com.mega.eloan.lms.dao.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.TreeSet;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;
import tw.com.jcs.common.Util;

import com.mega.eloan.lms.dao.C122M01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C122M01A;

@Repository
public class C122M01ADaoImpl extends LMSJpaDao<C122M01A, String> implements
		C122M01ADao {

	@Override
	public C122M01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public C122M01A findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C122M01A> find_0A0_C122M01A(String[] applyKind_arr){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		// 這裡抓不到 UtilConstants 內的值，所以寫
		search.addSearchModeParameters(SearchMode.IN, "applyStatus",
				new String[] { "0A0"}); //0A0:受理中(未收件)
		if(applyKind_arr!=null&&applyKind_arr.length>0){
			search.addSearchModeParameters(SearchMode.IN, "applyKind", applyKind_arr);
		}
		search.addSearchModeParameters(SearchMode.NOT_LIKE, "docStatus", "A%");
		search.setMaxResults(Integer.MAX_VALUE);
		List<C122M01A> list = createQuery(C122M01A.class, search)
				.getResultList();
		return list;
	}
	
	@Override
	public List<C122M01A> findInProgressC122M01A(String ownBrId, String[] applyKind_arr) {
		return _findInProgressC122M01A(ownBrId, applyKind_arr, null, null);
	}

	@Override
	public List<C122M01A> findInProgressC122M01A(String ownBrId, String[] applyKind_arr, String custId,
			String dupNo) {
		return _findInProgressC122M01A(ownBrId, applyKind_arr, Util.trim(custId),
				Util.trim(dupNo));
	}

	private List<C122M01A> _findInProgressC122M01A(String ownBrId, String[] applyKind_arr,
			String custId, String dupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", ownBrId);
		if (custId == null && dupNo == null) {
		} else {
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		}
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		// 這裡抓不到 UtilConstants 內的值，所以寫
		search.addSearchModeParameters(SearchMode.IN, "applyStatus",
				new String[] { "0A0", "0B0" });
		if(applyKind_arr!=null&&applyKind_arr.length>0){
			search.addSearchModeParameters(SearchMode.IN, "applyKind", applyKind_arr);
		}
		search.addSearchModeParameters(SearchMode.NOT_LIKE, "docStatus", "A%");
		search.setMaxResults(Integer.MAX_VALUE);
		if (true) {
			search.addOrderBy("applyStatus", false);
			search.addOrderBy("custId", false);
			search.addOrderBy("applyTS", true);
		}

		List<C122M01A> list = createQuery(C122M01A.class, search)
				.getResultList();
		return list;
	}

	@Override
	public List<C122M01A> findBy_brNo_custId_applyKind_orderByApplyTSDesc(String ownBrId, String custId, String applyKind){
		String[] applyKind_arr = new String[]{applyKind};
		return findBy_brNo_custId_applyKind_orderByApplyTSDesc(ownBrId, custId, applyKind_arr);
	}
	
	@Override
	public List<C122M01A> findBy_brNo_custId_applyKind_orderByApplyTSDesc(String ownBrId, String custId, String[] applyKind_arr){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", ownBrId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		search.addSearchModeParameters(SearchMode.IN, "applyKind", applyKind_arr);
		if (true) {
			search.addOrderBy("applyTS", true);
		}

		List<C122M01A> list = createQuery(C122M01A.class, search)
				.getResultList();
		return list;
	}
	
	@Override
	public List<C122M01A> findBy_brNo_custId_applyKind_orderByApplyTSDesc(String ownBrId, String custId, String applyKind, String createTimeSince){
		String[] applyKind_arr = new String[]{applyKind};
		return findBy_brNo_custId_applyKind_orderByApplyTSDesc(ownBrId, custId, applyKind_arr, createTimeSince);
	}
	
	@Override
	public List<C122M01A> findBy_brNo_custId_applyKind_orderByApplyTSDesc(String ownBrId, String custId, String[] applyKind_arr, String createTimeSince){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", ownBrId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		search.addSearchModeParameters(SearchMode.IN, "applyKind", applyKind_arr);
		search.addSearchModeParameters(SearchMode.GREATER_EQUALS, "createTime", Util.parseDate(createTimeSince));
		
		if (true) {
			search.addOrderBy("createTime", true);
			search.addOrderBy("applyTS", true);
		}

		List<C122M01A> list = createQuery(C122M01A.class, search)
				.getResultList();
		return list;
	}
	
	@Override
	public List<C122M01A> findBy_brNo_custId_applyKind_for_C160S01D_orderByApplyTSDesc(String ownBrId, String custId, String[] applyKind_arr, String createTimeSince, String exclude_statFlag){
		ISearch search = createSearchTemplete();
		if(Util.isNotEmpty(ownBrId)){
			search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", ownBrId);	
		}		
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		search.addSearchModeParameters(SearchMode.IN, "applyKind", applyKind_arr);
		search.addSearchModeParameters(SearchMode.GREATER_EQUALS, "createTime", createTimeSince);
		search.addSearchModeParameters(SearchMode.NOT_EQUALS, "statFlag", exclude_statFlag); 
		search.addSearchModeParameters(SearchMode.LIKE, "ploanPlan", "C0%"); //中鋼集團消貸的行銷方案 C001~C020
				
		if (true) {
			search.addOrderBy("createTime", true);
			search.addOrderBy("applyTS", true);
		}

		List<C122M01A> list = createQuery(C122M01A.class, search)
				.getResultList();
		return list;
	}
	
	@Override
	public List<C122M01A> findBy_custId_applyKind_orderByApplyTSDesc(String custId, String applyKind, String createTimeSince){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		search.addSearchModeParameters(SearchMode.EQUALS, "applyKind", applyKind);
		search.addSearchModeParameters(SearchMode.GREATER_EQUALS, "createTime", createTimeSince);
		if (true) {
			search.addOrderBy("applyTS", true);
		}

		List<C122M01A> list = createQuery(C122M01A.class, search)
				.getResultList();
		return list;
	}
	
	@Override
	public List<C122M01A> queryUnMatchReason(String custId, String dupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.LIKE, "docStatus", "A%");
		search.addSearchModeParameters(SearchMode.NOT_EQUALS, "docStatus", "A00"); //查不符原因，排除A00:通過
		search.addSearchModeParameters(SearchMode.IN, "applyKind", new String[] { "H", "C" });
		search.setMaxResults(Integer.MAX_VALUE);
		if (true) {
			search.addOrderBy("applyTS", true);
		}		
		
		List<C122M01A> list = createQuery(C122M01A.class, search)
				.getResultList();
		return list;
	}

	@Override
	public List<C122M01A> findBy_ploanCaseId(String ploanCaseId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "ploanCaseId", ploanCaseId);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",null);
		search.setMaxResults(Integer.MAX_VALUE);
		
		List<C122M01A> list = createQuery(C122M01A.class, search).getResultList();
		return list;
	}
	
	@Override
	public TreeSet<String> queryOwnBranchListC122M01A(String applyKind) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "applyKind", applyKind);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		search.addSearchModeParameters(SearchMode.NOT_LIKE, "docStatus", "A%");
		search.addSearchModeParameters(SearchMode.GREATER_THAN, "applyStatus", "");
		search.setMaxResults(Integer.MAX_VALUE);
		search.setDistinct(true);
		search.setDistinctColumn(new String[] { "ownBrId" });
		
		List<C122M01A> list = find(search);
		//==============
		TreeSet<String> brNoSet = new TreeSet<String>();
		for(C122M01A distinct_obj: list){
			brNoSet.add(Util.trim(distinct_obj.getOwnBrId()));
		}
		return brNoSet;
	}

	@Override
	public TreeSet<String> queryOwnBranchListC122M01AByApplyStatus(String applyKind, String[] applyStatus) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "applyKind", applyKind);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		search.addSearchModeParameters(SearchMode.NOT_LIKE, "docStatus", "A%");
		search.addSearchModeParameters(SearchMode.IN, "applyStatus", applyStatus);
		search.setMaxResults(Integer.MAX_VALUE);
		search.setDistinct(true);
		search.setDistinctColumn(new String[] { "ownBrId" });
		
		List<C122M01A> list = find(search);
		//==============
		TreeSet<String> brNoSet = new TreeSet<String>();
		for(C122M01A distinct_obj: list){
			brNoSet.add(Util.trim(distinct_obj.getOwnBrId()));
		}
		return brNoSet;
	}

	@Override
	public List<Object[]> getAllOnLineLoan(String applyKind) {
		Query query = entityManager
				.createNamedQuery("C122M01A.getAllOnLineLoan");
		query.setParameter(1, applyKind);
		List<Object[]> resultList = query.getResultList();
		return resultList;

	}

	@Override
	public List<Object[]> getOnLineLoanByBranch(String applyKind, String ownBrId) {
		Query query = entityManager
				.createNamedQuery("C122M01A.getOnLineLoanByBranch");
		query.setParameter(1, applyKind);
		query.setParameter(2, ownBrId);
		List<Object[]> resultList = query.getResultList();
		return resultList;
	}

	@Override
	public List<Object[]> getCloseCaseByBranchAndMonth(String applyKind, String ownBrId, String yyyy_mm) {
		Query query = entityManager
				.createNamedQuery("C122M01A.getCloseCaseByBrAndMonth");
		query.setParameter(1, applyKind);
		query.setParameter(2, ownBrId);
		query.setParameter(3, yyyy_mm);
		List<Object[]> resultList = query.getResultList();
		return resultList;

	}

	@Override
	public List<C122M01A> queryCreditLineByBranch(String applyKind, String ownBrId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "applyKind", applyKind);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		search.addSearchModeParameters(SearchMode.EQUALS, "applyStatus", "Z03");
		search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", ownBrId);
		search.setMaxResults(Integer.MAX_VALUE);
		search.addOrderBy("applyTS", true);
		List<C122M01A> list = find(search);
		return list;
	}

	@Override
	public C122M01A findPloanParentByApplyKind_ploanCaseId(String applyKind, String ploanCaseId) {
		//因 ploanCaseId 會對應到 主借人, 從債務人
		//這裡傳入的參數 applyKind , 應只能傳入{主借人}
		if(Util.equals("P", applyKind) || Util.equals("E", applyKind) || Util.equals("O", applyKind)){
			ISearch search = createSearchTemplete();
			search.addSearchModeParameters(SearchMode.EQUALS, "applyKind", applyKind);
			search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
			search.addSearchModeParameters(SearchMode.EQUALS, "ploanCaseId", ploanCaseId);
			return findUniqueOrNone(search);			
		}
		return null;
	}
	
	@Override
	public C122M01A findValidContractBy_brNo_custId_applyKind_orderByCreateTime_ApplyTSDesc(String ownBrId, String custId, String[] applyKind_arr, String createTimeSince){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", ownBrId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		search.addSearchModeParameters(SearchMode.IN, "applyKind", applyKind_arr);
		search.addSearchModeParameters(SearchMode.GREATER_EQUALS, "createTime", createTimeSince);
		if (true) {
			search.addOrderBy("createTime", true);
			search.addOrderBy("applyTS", true);
		}

		List<C122M01A> list = createQuery(C122M01A.class, search)
				.getResultList();
		
		List<C122M01A> c122m01as_x = new ArrayList<C122M01A>();
		List<C122M01A> c122m01as_notX = new ArrayList<C122M01A>();
		for(C122M01A c122m01a:list){
			if(Util.equals(c122m01a.getStatFlag(), "X")){
				c122m01as_x.add(c122m01a);
			}else{
				c122m01as_notX.add(c122m01a);
			}
		}				
		C122M01A c122m01a = null;
		if(c122m01as_notX.size()>0){
			c122m01a = c122m01as_notX.get(0);
		}else if(c122m01as_x.size()>0){
			c122m01a = c122m01as_x.get(0);	
		}
		return c122m01a;
	}

	@Override
	public List<C122M01A> findInProgressLastC122M01A(String ownBrId, String[] applyKind_arr, String custId,
												 String dupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", ownBrId);
		if (custId == null && dupNo == null) {
		} else {
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		}
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		// 這裡抓不到 UtilConstants 內的值，所以寫
		search.addSearchModeParameters(SearchMode.IN, "applyStatus",
				new String[] { "0A0", "0B0" });
		if(applyKind_arr!=null&&applyKind_arr.length>0){
			search.addSearchModeParameters(SearchMode.IN, "applyKind", applyKind_arr);
		}
		search.addSearchModeParameters(SearchMode.NOT_LIKE, "docStatus", "A%");
		search.setFirstResult(0).setMaxResults(1);
		if (true) {
			search.addOrderBy("applyStatus", false);
			search.addOrderBy("custId", false);
			search.addOrderBy("applyTS", true);
		}

		List<C122M01A> list = createQuery(C122M01A.class, search)
				.getResultList();
		return list;
	}
	
	@Override
	public List<C122M01A> findC122M01AList(ISearch search) {
		List<C122M01A> list = createQuery(C122M01A.class, search)
				.getResultList();
		return list;
	}
	
	//J-111-0226 配合青創貸款線上申請作業，額度明細表增加引進青創線上申請之欄位
	@Override
	public List<C122M01A> findyoungLoanListBy_brNo_custId_applyKind(String ownBrId, String custId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", ownBrId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		search.addSearchModeParameters(SearchMode.IS_NULL, "isClosed", null);
		
		search.addSearchModeParameters(SearchMode.IN, "applyKind", new String[] { "I", "J" });
		
		if (true) {
			search.addOrderBy("applyTS", true);
		}

		List<C122M01A> list = createQuery(C122M01A.class, search)
				.getResultList();
		
		return list;
	}
	
	
	// J-111-0602 歡喜信貸徵審流程程式修改
	@Override
	public List<C122M01A> findOnlineCasesRequiringInitialCheck() {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		search.addSearchModeParameters(SearchMode.IS_NULL, "isClosed", null);
		search.addSearchModeParameters(SearchMode.EQUALS, "isGroupLoan", "N");
		search.addSearchModeParameters(SearchMode.IS_NULL, "initChkFinishTime", "");
		
		search.addSearchModeParameters(SearchMode.EQUALS, "docStatus", "A99");
		search.addOrderBy("applyTS", true);

		return createQuery(C122M01A.class, search).getResultList();
	}
	
	@Override
	public List<C122M01A> findSmsSendingCaseForOnlineInitialCheckCase() {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		search.addSearchModeParameters(SearchMode.IS_NULL, "isClosed", null);
		search.addSearchModeParameters(SearchMode.EQUALS, "initCheckStatus", "N");
		search.addSearchModeParameters(SearchMode.IS_NULL, "smsSendingStatus", "");
		search.addSearchModeParameters(SearchMode.IS_NOT_NULL, "initChkFinishTime", "");
		return createQuery(C122M01A.class, search).getResultList();
	}

	@Override
	public List<C122M01A> findSmsSentCaseForOnlineInitialCheckCase() {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);// 刪除註記日期
		search.addSearchModeParameters(SearchMode.IS_NULL, "isClosed", null);// 文件尚未結案
		search.addSearchModeParameters(SearchMode.IS_NOT_NULL, "initChkFinishTime", "");// 歡喜信貸初審有完成檢核
		search.addSearchModeParameters(SearchMode.EQUALS, "initCheckStatus", "N");// 未通過歡喜信貸初審
		search.addSearchModeParameters(SearchMode.OR, 
				new SearchModeParameter(SearchMode.EQUALS, "smsSendingStatus", "U")
				,new SearchModeParameter(SearchMode.EQUALS, "smsSendingStatus", "D"));// 婉拒簡訊發送還未確定成功或失敗
		return createQuery(C122M01A.class, search).getResultList();
	}

	@Override
	public C122M01A findByPloanCaseNo(String ploanCaseNo){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "ploanCaseNo", ploanCaseNo);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		search.setMaxResults(Integer.MAX_VALUE);
		return findUniqueOrNone(search);
	}

    @Override
    public List<Object[]> findSMEAList(Date sDate, Date eDate) {
		Query query = entityManager
				.createNamedQuery("C122M01A.getSMEAList");
		SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
		SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd 23:59:59");
		query.setParameter(1, sdf1.format(sDate));
		query.setParameter(2, sdf2.format(eDate));

		return query.getResultList();
    }
}