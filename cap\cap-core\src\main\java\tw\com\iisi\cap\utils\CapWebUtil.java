/* 
 * CapWebStringUtil.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package tw.com.iisi.cap.utils;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;

/**
 * <pre>
 * CapWebStringUtil
 * 文件下載資訊
 * </pre>
 * 
 * @since 2011/12/9
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2011/12/9,iristu,new
 *          </ul>
 */
public class CapWebUtil {

    private static final Logger logger = LoggerFactory.getLogger(CapWebUtil.class);

    /**
     * 本地語系<br>
     * {@value #localeKey}
     */
    public static final String localeKey = "localeKey";

    /**
     * 下載檔名中文依IE及FireFox做區分
     * 
     * @param req
     *            HttpServletRequest
     * @param fileName
     *            前端要顯示的檔案名稱
     * @return String
     */
    public static String encodeFileName(HttpServletRequest req, String fileName) {
        try {
            fileName = URLDecoder.decode(fileName, "utf-8");
            String agent = req.getHeader("USER-AGENT");

            if (null != agent && -1 != agent.indexOf("MSIE")) {

                return URLEncoder.encode(fileName, "UTF8");

            } else if (null != agent && -1 != agent.indexOf("Mozilla")) {
                return "=?UTF-8?B?" + (new String(Base64.encodeBase64(fileName.getBytes("UTF-8")))) + "?=";
            } else {
                return fileName;
            }
        } catch (UnsupportedEncodingException e) {
            return fileName;
        }
    }

    /**
     * 取得文件Url
     * 
     * @param clazz
     *            類別名稱
     * @return
     */
    public static String getDocUrl(Class<?> clazz) {
        String reslut = clazz.getAnnotation(RequestMapping.class).value()[0];
        return reslut.replace("/{page}", "");
    }

    /**
     * 顯示傳遞之參數
     * 
     * @param params
     *            wicket.PageParameters
     */
    public static void showParams(PageParameters params) {
        logger.debug("++++++++++++++++++++++++++++++++++");
        for (String key : params.keySet()) {
            String value = params.getString(key);
            logger.debug("{}={}", key, value);
        }
        logger.debug("++++++++++++++++++++++++++++++++++");
    }

}
