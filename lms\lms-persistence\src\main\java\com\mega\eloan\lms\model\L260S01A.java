/* 
 * L260S01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;

import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 貸後管理理財商品檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L260S01A", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId","custId","dupNo","proType","bankProCode","accNo"}))
public class L260S01A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 統一編號 **/
	@Size(max=10)
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 重覆序號 **/
	@Size(max=1)
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 客戶名稱 **/
	@Size(max=150)
	@Column(name="CUSTNAME", length=150, columnDefinition="VARCHAR(150)")
	private String custName;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;
	
	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 刪除註記<p/>
	 * 文件刪除時使用(非立即性刪除)
	 */
	@Column(name="DELETEDTIME", columnDefinition="TIMESTAMP")
	private Timestamp deletedTime;

	/** 
	 * 商品類別<p/>
	 * FUND: 基金<br/>
	 *  BD: 債券<br/>
	 *  ETF: 指數股票型基金<br/>
	 *  STD: 優利投資<br/>
	 *  INS: 保險
	 */
	@Size(max=6)
	@Column(name="PROTYPE", length=6, columnDefinition="VARCHAR(6)")
	private String proType;

	/** 商品代號 **/
	@Size(max=20)
	@Column(name="BANKPROCODE", length=20, columnDefinition="VARCHAR(20)")
	private String bankProCode;

	/** 商品名稱 **/
	@Size(max=240)
	@Column(name="BANKPRONAME", length=240, columnDefinition="VARCHAR(240)")
	private String bankProName;

	/** 
	 * 憑證編號/定存帳號/受理編號<p/>
	 * FUND: 憑證編號<br/>
	 *  BD: 憑證編號<br/>
	 *  ETF: 商品代號<br/>
	 *  STD: 定存帳號<br/>
	 *  INS: 保險
	 */
	@Size(max=32)
	@Column(name="ACCNO", length=32, columnDefinition="VARCHAR(32)")
	private String accNo;

	/** 
	 * 最近一次申購扣帳日期<p/>
	 * YYYY-MM-DD
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="LSTBUYDT", columnDefinition="DATE")
	private Date lstBuyDt;

	/** 最近一次申購交易幣別 **/
	@Size(max=3)
	@Column(name="LSTBUYCURCD", length=3, columnDefinition="CHAR(3)")
	private String lstBuyCurCd;

	/** 最近一次申購扣帳金額(台幣) **/
	@Digits(integer=18, fraction=6, groups = Check.class)
	@Column(name="LSTBUYAMT", columnDefinition="DECIMAL(18,6)")
	private BigDecimal lstBuyAmt;

	/** 最近一次申購交易分行 **/
	@Size(max=3)
	@Column(name="LSTBUYBRCD", length=3, columnDefinition="CHAR(3)")
	private String lstBuyBrCd;

	/** 
	 * 最近一次贖回/到期入帳日期<p/>
	 * YYYY-MM-DD
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="LSTSELLDT", columnDefinition="DATE")
	private Date lstSellDt;

	/** 最近一次贖回/到期交易幣別 **/
	@Size(max=3)
	@Column(name="LSTSELLCURCD", length=3, columnDefinition="CHAR(3)")
	private String lstSellCurCd;

	/** 最近一次贖回/到期入帳金額(台幣) **/
	@Digits(integer=18, fraction=6, groups = Check.class)
	@Column(name="LSTSELLAMT", columnDefinition="DECIMAL(18,6)")
	private BigDecimal lstSellAmt;

	/** 最近一次贖回交易分行 **/
	@Size(max=3)
	@Column(name="LSTSELLBRCD", length=3, columnDefinition="CHAR(3)")
	private String lstSellBrCd;

	/** 最近一次贖回/到期交易類別 **/
	@Size(max=3)
	@Column(name="TRANTYPE", length=3, columnDefinition="VARCHAR(3)")
	private String tranType;

	/** 本金餘額 **/
	@Digits(integer=18, fraction=6, groups = Check.class)
	@Column(name="INVAMT", columnDefinition="DECIMAL(18,6)")
	private BigDecimal invAmt;

	/** 
	 * 資料日<p/>
	 * YYYY-MM-DD
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="DATADT", columnDefinition="DATE")
	private Date dataDt;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得統一編號 **/
	public String getCustId() {
		return this.custId;
	}
	/** 設定統一編號 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得重覆序號 **/
	public String getDupNo() {
		return this.dupNo;
	}
	/** 設定重覆序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得客戶名稱 **/
	public String getCustName() {
		return this.custName;
	}
	/** 設定客戶名稱 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}
	
	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 
	 * 取得刪除註記<p/>
	 * 文件刪除時使用(非立即性刪除)
	 */
	public Timestamp getDeletedTime() {
		return this.deletedTime;
	}
	/**
	 *  設定刪除註記<p/>
	 *  文件刪除時使用(非立即性刪除)
	 **/
	public void setDeletedTime(Timestamp value) {
		this.deletedTime = value;
	}

	/** 
	 * 取得商品類別<p/>
	 * FUND: 基金<br/>
	 *  BD: 債券<br/>
	 *  ETF: 指數股票型基金<br/>
	 *  STD: 優利投資<br/>
	 *  INS: 保險
	 */
	public String getProType() {
		return this.proType;
	}
	/**
	 *  設定商品類別<p/>
	 *  FUND: 基金<br/>
	 *  BD: 債券<br/>
	 *  ETF: 指數股票型基金<br/>
	 *  STD: 優利投資<br/>
	 *  INS: 保險
	 **/
	public void setProType(String value) {
		this.proType = value;
	}

	/** 取得商品代號 **/
	public String getBankProCode() {
		return this.bankProCode;
	}
	/** 設定商品代號 **/
	public void setBankProCode(String value) {
		this.bankProCode = value;
	}

	/** 取得商品名稱 **/
	public String getBankProName() {
		return this.bankProName;
	}
	/** 設定商品名稱 **/
	public void setBankProName(String value) {
		this.bankProName = value;
	}

	/** 
	 * 取得憑證編號/定存帳號/受理編號<p/>
	 * FUND: 憑證編號<br/>
	 *  BD: 憑證編號<br/>
	 *  ETF: 商品代號<br/>
	 *  STD: 定存帳號<br/>
	 *  INS: 保險
	 */
	public String getAccNo() {
		return this.accNo;
	}
	/**
	 *  設定憑證編號/定存帳號/受理編號<p/>
	 *  FUND: 憑證編號<br/>
	 *  BD: 憑證編號<br/>
	 *  ETF: 商品代號<br/>
	 *  STD: 定存帳號<br/>
	 *  INS: 保險
	 **/
	public void setAccNo(String value) {
		this.accNo = value;
	}

	/** 
	 * 取得最近一次申購扣帳日期<p/>
	 * YYYY-MM-DD
	 */
	public Date getLstBuyDt() {
		return this.lstBuyDt;
	}
	/**
	 *  設定最近一次申購扣帳日期<p/>
	 *  YYYY-MM-DD
	 **/
	public void setLstBuyDt(Date value) {
		this.lstBuyDt = value;
	}

	/** 取得最近一次申購交易幣別 **/
	public String getLstBuyCurCd() {
		return this.lstBuyCurCd;
	}
	/** 設定最近一次申購交易幣別 **/
	public void setLstBuyCurCd(String value) {
		this.lstBuyCurCd = value;
	}

	/** 取得最近一次申購扣帳金額(台幣) **/
	public BigDecimal getLstBuyAmt() {
		return this.lstBuyAmt;
	}
	/** 設定最近一次申購扣帳金額(台幣) **/
	public void setLstBuyAmt(BigDecimal value) {
		this.lstBuyAmt = value;
	}

	/** 取得最近一次申購交易分行 **/
	public String getLstBuyBrCd() {
		return this.lstBuyBrCd;
	}
	/** 設定最近一次申購交易分行 **/
	public void setLstBuyBrCd(String value) {
		this.lstBuyBrCd = value;
	}

	/** 
	 * 取得最近一次贖回/到期入帳日期<p/>
	 * YYYY-MM-DD
	 */
	public Date getLstSellDt() {
		return this.lstSellDt;
	}
	/**
	 *  設定最近一次贖回/到期入帳日期<p/>
	 *  YYYY-MM-DD
	 **/
	public void setLstSellDt(Date value) {
		this.lstSellDt = value;
	}

	/** 取得最近一次贖回/到期交易幣別 **/
	public String getLstSellCurCd() {
		return this.lstSellCurCd;
	}
	/** 設定最近一次贖回/到期交易幣別 **/
	public void setLstSellCurCd(String value) {
		this.lstSellCurCd = value;
	}

	/** 取得最近一次贖回/到期入帳金額(台幣) **/
	public BigDecimal getLstSellAmt() {
		return this.lstSellAmt;
	}
	/** 設定最近一次贖回/到期入帳金額(台幣) **/
	public void setLstSellAmt(BigDecimal value) {
		this.lstSellAmt = value;
	}

	/** 取得最近一次贖回交易分行 **/
	public String getLstSellBrCd() {
		return this.lstSellBrCd;
	}
	/** 設定最近一次贖回交易分行 **/
	public void setLstSellBrCd(String value) {
		this.lstSellBrCd = value;
	}

	/** 取得最近一次贖回/到期交易類別 **/
	public String getTranType() {
		return this.tranType;
	}
	/** 設定最近一次贖回/到期交易類別 **/
	public void setTranType(String value) {
		this.tranType = value;
	}

	/** 取得本金餘額 **/
	public BigDecimal getInvAmt() {
		return this.invAmt;
	}
	/** 設定本金餘額 **/
	public void setInvAmt(BigDecimal value) {
		this.invAmt = value;
	}

	/** 
	 * 取得資料日<p/>
	 * YYYY-MM-DD
	 */
	public Date getDataDt() {
		return this.dataDt;
	}
	/**
	 *  設定資料日<p/>
	 *  YYYY-MM-DD
	 **/
	public void setDataDt(Date value) {
		this.dataDt = value;
	}
}
