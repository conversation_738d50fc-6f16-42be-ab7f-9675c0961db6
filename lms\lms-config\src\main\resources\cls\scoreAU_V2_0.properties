#=====================================================
# \u8a55\u7b49\u53c3\u6578\u8a2d\u5b9a \u7248\u672c[varVer] 
#=====================================================
varVer=2.0
#Step 1.3 \u5c07 0-100 Score \u6a19\u6e96\u5316 (Mean=50, STD=25)\uff1a
stdItemScoreDevSampleMean_m1=61.333
stdItemScoreDevSampleSTD_m1=27.322
stdItemScoreDevSampleMean_m5=54.667
stdItemScoreDevSampleSTD_m5=19.384
stdItemScoreDevSampleMean_m7=24.000
stdItemScoreDevSampleSTD_m7=25.651
stdItemScoreDevSampleMean_d1=75.333
stdItemScoreDevSampleSTD_d1=22.396
stdItemScoreDevSampleMean_p3=44.444
stdItemScoreDevSampleSTD_p3=46.036
stdItemScoreDevSampleMean_a5=56.000
stdItemScoreDevSampleSTD_a5=19.009
stdItemScoreDevSampleMean_o1=44.778
stdItemScoreDevSampleSTD_o1=29.915
stdItemScoreDevSampleMean_z1=59.111
stdItemScoreDevSampleSTD_z1=18.460
stdItemScoreDevSampleMean_z2=72.833
stdItemScoreDevSampleSTD_z2=24.724
stdItemScoreA=25
stdItemScoreB=50	
#Step1.4 \u5c07\u6a19\u6e96\u5316\u5f8c\u7684\u5206\u6578\u4e58\u4ee5\u6307\u5b9a\u6b0a\u91cd
weight_m1=4
weight_m5=5
weight_m7=8
weight_d1=45
weight_p3=15
weight_a5=1
weight_o1=20
weight_z1=1		
weight_z2=1
#Step 1.6 \u5c07CORE Score\u6a19\u6e96\u5316
stdCoreScoreDevSampleMean=46.460
stdCoreScoreDevSampleSTD=11.240
stdCoreScoreA=25
stdCoreScoreB=50	
#===============================================================================
#\u8aaa\u660e\uff1a\u4ee5","\u70ba\u5206\u9694\u7b26\u865f[0]\u5206\u6578\u3001[1]\u8a55\u7b49\u516c\u5f0f(javascript)\u3001[3]\u70ba\u8a55\u7b49\u53c3\u6578,\u5206\u9694\u7b26\u865f\u70ba";"
#===============================================================================
#--------------------------------------------------------------
# \u65e5\u672c\u6d88\u91d1\u6a21\u578b(\u57fa\u672c)
#--------------------------------------------------------------

#M1_age	\u5e74\u9f61
auBase.scr_m1.01=20, {0} <= 30, item_m1
auBase.scr_m1.02=60, {0} > 30 && {0} <=45, item_m1
auBase.scr_m1.03=80, {0} > 45 && {0} <=55, item_m1
auBase.scr_m1.04=60, {0} > 55, item_m1
auBase.scr_m1.05=20, /{0}/.test("Null"), item_m1
#M5_occupation	\u8077\u696d\u5927\u985e
auBase.scr_m5.01=80, /{0}/.test("***********.***********.13"), item_m5
auBase.scr_m5.02=40, /{0}/.test("***********.14"), item_m5
auBase.scr_m5.03=0, /{0}/.test("Null"), item_m5
#M7_seniority	\u5e74\u8cc7(\u5e74)
auBase.scr_m7.01=30, {0} <= 15, item_m7
auBase.scr_m7.02=80, {0} > 15 , item_m7
auBase.scr_m7.03=0, /{0}/.test("Null"), item_m7
#D1_ICR	\u5229\u606f\u4fdd\u969c\u500d\u6578
auBase.scr_d1.01=10,  (/{0}/.test("0") && (  /{1}/.test("Null")  )), item_d1_na;item_d1_icr
auBase.scr_d1.02=10,  (/{0}/.test("0") && (            {1}<=0.9  )), item_d1_na;item_d1_icr
auBase.scr_d1.03=30, (/{0}/.test("0") && ({1}>0.9  && {1}<=1.45 )), item_d1_na;item_d1_icr
auBase.scr_d1.04=50, (/{0}/.test("0") && ({1}>1.45 && {1}<=2.85 )), item_d1_na;item_d1_icr
auBase.scr_d1.05=80,(/{0}/.test("0") && ({1}>2.85              )), item_d1_na;item_d1_icr
auBase.scr_d1.06=40,  /{0}/.test("1"),   item_d1_na
auBase.scr_d1.07=0,   /{0}/.test("2"),   item_d1_na
auBase.scr_d1.08=0,   /{0}/.test("Null"),item_d1_na
#P3 \u592b\u59bb\u5e74\u6536\u5165(\u6fb3\u5e63\u5143)
auBase.scr_p3.01=20, {0} <= 57000, item_p3
auBase.scr_p3.02=50, {0} > 57000 && {0} <=69000, item_p3
auBase.scr_p3.03=80, {0} > 69000, item_p3
auBase.scr_p3.04=0, /{0}/.test("Null"), item_p3
#A5_loan_period	\u5951\u7d04\u5e74\u9650(\u5e74)
auBase.scr_a5.01=100, {0} <= 1, item_a5
auBase.scr_a5.02=100, {0} > 1 && {0} <=7, item_a5
auBase.scr_a5.03=100, {0} > 7, item_a5
auBase.scr_a5.04=100, /{0}/.test("Null"), item_a5
#O1 VEDA Score
auBase.scr_o1.01=20, {0} <= 682, item_o1
auBase.scr_o1.02=70, {0} > 682 && {0} <=865, item_o1
auBase.scr_o1.03=100, {0} > 865, item_o1
auBase.scr_o1.04=20, /{0}/.test("Null"), item_o1
#Z1	\u64d4\u4fdd\u54c1\u7684\u5ea7\u843d\u5730\u9ede\u53ca\u7a2e\u985e
auBase.scr_z1.01=0, /{0}/.test("1"), item_z1
auBase.scr_z1.02=100, /{0}/.test("2"), item_z1
auBase.scr_z1.03=100, /{0}/.test("3"), item_z1
auBase.scr_z1.04=100, /{0}/.test("4"), item_z1
auBase.scr_z1.05=100, /{0}/.test("5"), item_z1
auBase.scr_z1.06=0, /{0}/.test("Null"), item_z1
#Z2	\u5e02\u5834\u74b0\u5883\u53ca\u8b8a\u73fe\u6027
auBase.scr_z2.01=0, /{0}/.test("1"), item_z2
auBase.scr_z2.02=100, /{0}/.test("2"), item_z2
auBase.scr_z2.03=100, /{0}/.test("3"), item_z2
auBase.scr_z2.04=100, /{0}/.test("4"), item_z2
auBase.scr_z2.05=0, /{0}/.test("Null"), item_z2
#--------------------------------------------------------------
# \u6d88\u91d1\u6a21\u578b(\u7b49\u7d1a)
#--------------------------------------------------------------
auGrade.level.01= 1, {0} > 100, std_core
auGrade.level.02= 2, {0} > 90 && {0} <= 100, std_core
auGrade.level.03= 3, {0} > 75 && {0} <= 90, std_core
auGrade.level.04= 4, {0} > 60 && {0} <= 75, std_core
auGrade.level.05= 5, {0} > 45 && {0} <= 60, std_core
auGrade.level.06= 6, {0} > 30 && {0} <= 45, std_core
auGrade.level.07= 7, {0} > 20 && {0} <= 30, std_core
auGrade.level.08= 8, {0} > 10 && {0} <= 20, std_core
auGrade.level.09= 9, {0} > 0 && {0} <= 10, std_core
auGrade.level.10=10, {0} <= 0, std_core
#auGrade.level.11=10, /{0}/.test("Null"), std_core
#--------------------------------------------------------------
# \u65e5\u672c\u6d88\u91d1\u6a21\u578b(\u9055\u7d04\u6a5f\u7387)
#--------------------------------------------------------------
auDR.dr_3yr.01=0.0009, /{0}/.test("1"), fRating
auDR.dr_3yr.02=0.0015, /{0}/.test("2"), fRating
auDR.dr_3yr.03=0.0026, /{0}/.test("3"), fRating
auDR.dr_3yr.04=0.0046, /{0}/.test("4"), fRating
auDR.dr_3yr.05=0.0087, /{0}/.test("5"), fRating
auDR.dr_3yr.06=0.0180, /{0}/.test("6"), fRating
auDR.dr_3yr.07=0.0366, /{0}/.test("7"), fRating
auDR.dr_3yr.08=0.0761, /{0}/.test("8"), fRating
auDR.dr_3yr.09=0.1363, /{0}/.test("9"), fRating
auDR.dr_3yr.10=0.2729, /{0}/.test("10"), fRating
auDR.dr_3yr.11=0.0000, /{0}/.test("Null"), fRating

auDR.dr_1yr.01=0.0003, /{0}/.test("1"), fRating
auDR.dr_1yr.02=0.0005, /{0}/.test("2"), fRating
auDR.dr_1yr.03=0.0009, /{0}/.test("3"), fRating
auDR.dr_1yr.04=0.0015, /{0}/.test("4"), fRating
auDR.dr_1yr.05=0.0029, /{0}/.test("5"), fRating
auDR.dr_1yr.06=0.0060, /{0}/.test("6"), fRating
auDR.dr_1yr.07=0.0124, /{0}/.test("7"), fRating
auDR.dr_1yr.08=0.0260, /{0}/.test("8"), fRating
auDR.dr_1yr.09=0.0477, /{0}/.test("9"), fRating
auDR.dr_1yr.10=0.1008, /{0}/.test("10"), fRating
auDR.dr_1yr.11=0.0000, /{0}/.test("Null"), fRating
