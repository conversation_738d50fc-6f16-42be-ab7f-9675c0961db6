#---------------------------------------------#
# javascript commom.js use
#---------------------------------------------#
comboSpace=--Please select--
yes=Yes
no=No
all=All
close=Close
cancel=Cancel
include=Import
noData=Data not found, please search again.
timeout=Server connection timed out, please try again later.
connectError=Server connection failed, please try again later or check if network connection is intact.
sessionTimeout=Your login session has expired\n Please log in from the employee intranet
loading="Connecting" to the remote system, please wait patiently
lastDBMonidyTime=Last Modifier Of Server Data
sure=Confirm
enter=Input
compID=Unified Business Number
megaID=MEGA ID
dupNo=Repeat Serial No.
name=Name
compName=Name
query=Inquiry
look=Retrieve
reQuery=Re-import
reModify=Re-modify
import=import
calculate=Calculations
del=Delete
print=Print
deleted=This document has been returned by the supervisor and marked for deletion; no further actions are allowed, please go back to the home screen for another query or new case.
newData=Add
newCustomer=New Customer
selectOption=Options
saveData=Save
accept=Accept
return=Return
saveSuccess=Saved Successfully
txnSuccess=Transaction Successful
runSuccess=Executed successfully
addSuccess=Added Successfully
saveDataClose=Exit And Save
CloseWithoutSave=Close without Saving
confirmSaveLeave=Are you sure to save and exit?
closewindows=The data you are editing will be lost if you click on "Close" or "Refresh"
count=Serial Number
action=Change Mode
actoin_001=Whether to perform this action?
action_002=Please select the row of data you wish to "Edit/Delete".
action_003=Are you sure you want to "Delete" this data?
action_004=Please select the row of data you wish to "Retrieve"
action_005=Please select at least one row of data
action_006=Please Select the row of data you wish to "Print"
confirmApply=Confirm to send for supervisor's approval?
confirmApply2=Confirm to send for supervisor's approval?
confirmApprove=Confirm Approve?
confirmApprove2=Confirm Submit?
confirmApprove3=Confirm Batch Verify?
confirmReturn=Confirm Return?
confirmReject=Confirm Reject?
confirmDelete=Confirm Delete?
confirmCopy=Confirm Copy?
confirmSend=Confirm Send?
confirmRun=Are you sure you perform this function?
confirmCk=Content Changed, Are you sure to saved?
confirmApplySuccess=Approved Successfully
confirmApplySuccess1=Approved Successfully
confirmApplySuccess2=Submit Successful
confirmReturnSuccess=Returned Successfully
confirmDeleteSuccess=Delete Successful
confirmCopySuccess=Copied Successfully
confirmApplySuccess3=Successfully submitted for supervisor's approval
confirmRejectSuccess=Rejected
confirmDeliverSuccess=Send Successful
confirmContinueRun=Data has been changed and not yet saved; are you sure to proceed?
confirmTitle=Prompt
id_reason=Query reason
grid_selector=Please Select Data
#J-106-0029-002  \u6d17\u9322\u9632\u5236-\u65b0\u589e\u6d17\u9322\u9632\u5236\u9801\u7c64
confirmBeforeDeleteAll=Execution will delete the existing data, whether to determine the implementation\uff1f
#J-107-0390_05097_B1001 \u5206\u884c\u6b0a\u9650\u4e4b\u6388\u4fe1\u6848\u4ef6\u82e5\u65bc\u8986\u6838\u5f8c\u6b32\u4fee\u6539,\u5f97\u6388\u6b0a\u4e3b\u7ba1\u5f97\u9000\u56de\u81f3\u7de8\u88fd\u4e2d
confirmBackApprove=After the case is returned, it must be reviewed again. It cannot be returned to the approved state directly. Are you sure to return the approved case?


#(\u96d9\u64ca\u6ed1\u9f20\u5de6\u9375\u5f15\u5165)
TMInsert=Are you sure to add this data?
TMModify=Confirm Approve & Send?
TMDelete=Are you sure to delete this data?
TMMDeleteError=Please select the row of data you wish to edit "delete".
TMNoChangeModify=No data has been changed, therefore no editing is required
fileSelect=Please select a file
fileSelError=Please select a file with the correct extension
#fileXlsError=\u8acb\u4f7f\u7528*.xls\u6a94
fileUploadError=Error while loading file, please upload again
fileUploading=File is being loaded, please wait patiently\u2026
fileUploadSuccess=File upload complete
attachfile=File Attachment
insertfile=Please Select Files To Attach
insertfileSize=Currently granted upload file size is$\{fileSize\}M
#id_dcTitle=(\u8acb\u96d9\u64ca\u6240\u9700\u4e4b\u9078\u9805)
#id_error=\u7d71\u4e00\u7de8\u865f\u6aa2\u6838\u932f\u8aa4
lastModifyBy=Edit Personnel
lastModifyRole=Edit Personnel Group
lastModifyName=Edit Personnel
lastModifyTime=Date Of Last Update
lastUpdater=Latest Personnel Change
lastUpdateTime=Time Of Last Change
actionType=Case Status
createTime=Date Created
tabchange=Processing page data\u2026
localtempResolve=Do you want to repeat the save function as it did not complete normally the last time you tried?
requireSave=Please save before continuing other functions, thank you.
saveBeforePrint=The data is saved automatically after executing the print function; are you sure to continue?
saveBeforeSend=The data is saved automatically after executing; are you sure you want to continue?
saveBeforeAction=Data will be automatically saved, $\{btnAction\}
lognView=Imput/Retrieve Approval Remarks
#---------------------------------------------#
# javascript commom.js use(validation)
#---------------------------------------------#
val.required=This is a required field.
#
val.remote=Please fix this field.
#Please fix this field.
val.email=E-Mail format error
#Please enter a valid email address.
val.url=URL format error.
#Please enter a valid URL.
val.date=Date input error (YYYYMMDD)
val.date2=Date Format error(YYYY-MM)
val.date3=Date Format error(YYYYMM)
val.date4=Date Format error(YYYY/MM
#Please enter a valid date(YYYYMMDD).
val.dateISO=Please enter a valid date (ISO).
#Please enter a valid date (ISO).
#val.dateD=Bitte geben Sie ein g\u00fcltiges Datum ein.
#Bitte geben Sie ein g\u00fcltiges Datum ein.
val.number=Please input a number.
#Please enter a valid number.
#val.numberDE=Bitte geben Sie eine Nummer ein.
#Bitte geben Sie eine Nummer ein.
val.digits=Please input a number (without positive/negative signs)
#Please enter only digits
val.creditcard=Please enter a valid credit card number.
#Please enter a valid credit card number.
val.equalTo=Please enter the same value again.
#Please enter the same value again.
val.accept=Please enter a value with a valid extension.
#Please enter a value with a valid extension.
val.maxlength=Up to {0} characters are allowed.
#$.validator.format("Please enter no more than {0} characters.
val.minlength=At least {0} characters are required
#Please enter at least {0} characters.
val.rangelength=Please input between {0} and {1} characters
#Please enter a value between {0} and {1} characters long.
val.range=Please enter a value between {0} and {1}.
#Please enter a value between {0} and {1}.
val.max=Please enter a value less than or equal to {0}.
#Please enter a value less than or equal to {0}.
val.min=Please enter a value greater than or equal to {0}.
#Please enter a value greater than or equal to {0}.
val.twid=ID card input error.
val.compNo=UBN input error.
val.foreign=Error in foreign individual's ID input
val.requiredLength=Please input {0} characters
val.checkID="ID card" or "UBN" input error.
val.tooLong=Text length is too long
#\u65e5\u671f
val.ineldate=Please input year/month
val.inelbranch=Please Select Branch
val.ip=Please input a valid IP
val.time=Please input a valid time between 00:00~23:59
val.noSignNumber=Please input a number (without positive/negative signs)
val.checkmaxlength=Can not exceed {0} characters
val.numeric=Please input a ${0}-digit integer
val.numericFraction=, followed by ${0} decimal places
val.phone=Tel No. format error
val.alphanum=You can only input English & numeric
val.obuText=only input English & numeric
val.numText=Please enter the numeric
val.enText=Please enter the alphabetic
val.halfword=You can only input half-word
#\u5171\u7528Grid i18n
grid.pgtext=Page {0} of {1}
grid.emptyrecords=No information found
grid.recordtext={0}~{1}/{2} records in total
grid.loadtext=Inquiry in progress, please wait
grid.refresh=Refresh
grid.up=Move Up
grid.down=Move Down
grid.selrow=Please select 1 record.
grid.check=Confirm Change
grid.showAllBranch=Branch List Inquiry
grid.branchNo=Branch code
grid.branchName=Branch name
grid.branchGroup=Name Of Credit Factory
grid.datePeriodCheck=Please input a starting date and an ending date for the timeframe
localSave.quotaExceededError=Please set your computer to allow more storage space.
grid.docName=File Name
grid.maxSelrow=${0} You can select up to ${1} record(s) only.
#\u6587\u4ef6\u7570\u52d5\u8a18\u9304
docLog.logNum=Record Serial No.
docLog.logTime=Record Date & Time
docLog.unitId=Executor's Unit
docLog.userId=Executor's ID
docLog.userName=Executor's Name
docLog.userPos=Executor's Job Role
docLog.actCode=Executed Item
docLog.title=Document Revision History
#\u6d41\u7a0b\u985e
flow.confirmSend=Confirm Send?
flow.sent=Send Successful
flow.exit=Confirm Exit?
flow.confirmSend2=Confirm to send for supervisor's approval?
flow.sent2=Submit Successful
flow.confirmReturn=Confirm Return?
flow.returned=Returned Successfully
flow.needCreateContract=Whether to automatically generate and review the online insurance contract?
#\u5f15\u5165\u5ba2\u6236ID
includeId.title=Customer Data Inquiry
includeId.subTitle=Unified Business Number
includeId.newCustName=(New Customer)
includeId.noData=Customer's profile contains no UBN
includeId.selData=Please select 1 record
#0024\u5efa\u6a94
creatCust.queryType1=By UBN Inquiry
creatCust.queryType2=By Customer English Name Inquiry(Company Account)
creatCust.error=Please input content for your inquiry
creatCust.custType=Customer Category
creatCust.custType1=Individual Account
creatCust.custType2=Company Account
creatCust.headNation=Company Registered Country Code(personal nationality)
creatCust.regNation=Location Country(personal birthplace)
creatCust.licenseType=Registration certificate Category
creatCust.licenseNO=Registration certificate No.
creatCust.birthday=Company's Establishment Date(Individual's Date of Birth)
creatCust.CNAME=Traditional Chinese Account Name(Double bytes)
creatCust.LNAME=Local Account Name(Double bytes)
creatCust.ENAME=English Account Name(single byte)
creatCust.custName=Account name
creatCust.ENAMEerror=English Account Name format error
creatCust.bstbl=DGBAS Industry type major categories
creatCust.bstb2=DGBAS Industry type
creatCust.bstb3=DGBAS Industry type sub categories
creatCust.bstb4=Industry type sub
creatCust.bstbError=Please select a Industry type
creatCust.localId=Overseas Branch Local AS400 encoded ID
creatCust1.060000=Personal
creatCust1.130300=Non-resident Foreign Nationals in Taiwan
creatCust.companyDate=The same customer ID Company account's Establishment Date
creatCust.reqID=Corporate representative ID
creatCust.swiftID=Peer Bank Code
creatCust.buscd=Industry type
creatCust.newUserDisabled=No Add User Authority
creatCust.failAndConfirmExit=New Customer Created error, Whether end the New Customer File creation process
creatCust.MEMO=Please select [Personal] from \u201cPDGBAS Industry type major categories(\u4e3b\u8a08\u8655\u884c\u696d\u5c0d\u8c61\u5225\u5927\u985e)\u201d for Individual Accounts of Oversea Branches.
#\u6a94\u6848\u4e0a\u50b3
uploadFile.button=Upload data to the host
uploadFile.uploadTime=Upload Time
uploadFile.srcFileName=File Name
uploadFile.srcFileDesc=File Description
#\u5831\u8868\u985e
printError=Error while generating report
printPrcess=Rendering of the report, please wait\u2026
openTckeditBoxmsg=Open {0} the input screen
backdoc.msg1=Are you sure to proceed Cancel Approval?
#\u756b\u9762\u8a0a\u606f
err.chooseBoss=Please select supervisor
common.L140M01M=\u592e\u884c\u8cfc\u4f4f/\u7a7a\u5730/\u8208\u5efa\u623f\u5c4b\u7d71\u8a08\u5831\u8868\u7528\u76f8\u95dc\u8cc7\u8a0a
common.L140M01Q=\u5927\u9678\u5730\u5340\u6388\u4fe1\u696d\u52d9\u63a7\u7ba1\u8a3b\u8a18
common.L140S05A=Item Of Change In Terms
common.001=\u6b04\u4f4d\u6aa2\u6838\u672a\u5b8c\u6210\uff0c\u8acb\u586b\u59a5\u5f8c\u518d\u9001\u51fa
common.002=\u8acb\u9078\u64c7\u6bcd\u884c\u55ae\u4f4d/\u6388\u6b0a\u4e3b\u7ba1
common.003=\u7d93\u8fa6
common.004=\u5e38\u7528\u4e3b\u7ba1
defaultFontSize=\u5b57\u578b\u5927\u5c0f\u5efa\u8b70\u4f7f\u752816
#\u9650\u5b9aCKEDIT\u4e00\u884c\u5b57\u6578\u6709\u591a\u9577\u7684\u5099\u8a3b
lms.ckeditRemark1=\u8a3b1:\u5efa\u8b70\u5b57\u578b16
lms.ckeditRemark2=\u8a3b2:|\u2190\u5b57\u578b16\u6642\u5efa\u8b70\u63db\u884c
lms.ckeditRemark3=\u8a3b1:|\u2190\u5efa\u8b70\u63db\u884c
queryByEnglish=\u4f9d\u5ba2\u6236\u82f1\u6587\u540d\u67e5\u8a62
