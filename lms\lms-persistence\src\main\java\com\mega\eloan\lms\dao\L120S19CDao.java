/* 
 * L120S19CDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S19C;

/** 無紙化簽報貸款費用檔 **/
public interface L120S19CDao extends IGenericDao<L120S19C> {

	L120S19C findByOid(String oid);
	
	List<L120S19C> findByMainId(String mainId);
	
	public List<L120S19C> findByMainIdFeeNoRole(String mainId, String feeNo, String role);
	
	public List<L120S19C> findByMainIdRole(String mainId, String role);
}