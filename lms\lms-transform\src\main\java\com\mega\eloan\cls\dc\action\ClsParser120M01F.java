/* 
 * ClsParser120M01F.java
 *
 * IBM Confidential
 * GBS Source Materials
 * 
 * Copyright (c) 2013 IBM Corp. 
 * All Rights Reserved.
 */
package com.mega.eloan.cls.dc.action;

import org.apache.commons.lang.StringUtils;
import org.w3c.dom.Document;

import com.mega.eloan.lms.dc.base.DCException;
import com.mega.eloan.lms.dc.bean.L120M01FBean;
import com.mega.eloan.lms.dc.util.Util;

/**
 * <pre>
 * ClsParser120M01F
 * </pre>
 * 
 * @since 2013/4/17
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/4/17,Vance,new
 *          </ul>
 */
public class ClsParser120M01F extends AbstractCLSCustParser {

	private String class_name = ClsParser120M01F.class.getSimpleName();

	interface BRANCHTYPE {
		String 分行 = "1";
		String 母行 = "2";
		String 營運中心 = "3";
		String 授管處 = "4";
	}

	interface STAFFJOB {
		String L1 = "L1";
		String L2 = "L2";
		String L3 = "L3";
		String L4 = "L4";
		String L5 = "L5";
		String L9 = "L9";

		String C1 = "C1";
		String C2 = "C2";
		String C4 = "C4";
		String C5 = "C5";
	}

	public static final String SPLIT = "、";

	/**
	 * @param pid
	 * @param doViewName
	 * @param formName
	 */
	public ClsParser120M01F(String pid, String doViewName, String formName) {
		super(pid, doViewName, formName);
	}

	/**
	 * 讀取,處理及轉換
	 * 
	 * @param dxlPath
	 *            String : .dxl檔存放路徑
	 * @param dxlName
	 *            :.dxl列表中的.dxl檔名
	 * @param strBrn
	 *            String:分行名稱
	 * @param domDoc
	 *            DOM Document:已轉為DOM Document的.dxl檔
	 */
	@SuppressWarnings("unused")
	protected void transferDXL(String dxlPath, String dxlName, String strBrn,
			Document domDoc, String dxlXml) {
		long t1 = System.currentTimeMillis();
		try {
			// 20130509 modified by Sandra配合個金審核書產生簽報書mainid規則調整
			// String mainId = getItemValue(domDoc, "UnidDocID");
			String mainId = this.xmlHandler.getItemValue(domDoc, "RPTID");
			if (StringUtils.isBlank(mainId)) {
				mainId = this.xmlHandler.getItemValue(domDoc, "UnidDocID");
			}

			String grant = getItemValue(domDoc, "grant");
			String branch_id = getItemValue(domDoc, "branch_id");
			String gMothBch = getItemValue(domDoc, "gMothBch");
			String AreaBch = getItemValue(domDoc, "AreaBch");

			// branchType = 1 ...............................................
			String[] job1 = { STAFFJOB.L1, STAFFJOB.L2, STAFFJOB.L3,
					STAFFJOB.L4, STAFFJOB.L5, STAFFJOB.L9 };
			String[] id1 = { "Appr_ID", "AO_id", "Boss_ID", "Re_CheckID",
					"Manager_ID", "UNIT_MANAGERID" };
			String[] name1 = { "Appraiser", "AO", "Boss", "Re_Check",
					"Manager", "UNIT_MANAGER" };
			parseStaff(domDoc, mainId, BRANCHTYPE.分行, branch_id, job1, id1,
					name1);

			// branchType = 2 ...............................................
			String[] job2 = { STAFFJOB.L4, STAFFJOB.L5, STAFFJOB.L9 };
			String[] id2 = { "MO_ReCheckID", "MO_ReCheckID_1",
					"MO_UNIT_MANAGERID" };
			String[] name2 = { "MO_ReCheck", "MO_ReCheck_1", "MO_UNIT_MANAGER" };
			parseStaff(domDoc, mainId, BRANCHTYPE.母行, gMothBch, job2, id2,
					name2);

			// branchType = 3 ...............................................
			String[] job3 = { STAFFJOB.L1, STAFFJOB.L4 };
			String[] id3 = { "ApprID_A", "ReCheckID_A" };
			String[] name3 = { "Appraiser_A", "ReCheck_A" };
			parseStaff(domDoc, mainId, BRANCHTYPE.營運中心, AreaBch, job3, id3,
					name3);

			// branchType = 4 ...............................................
			String[] job4 = { STAFFJOB.L9 };
			String[] id4 = { "UNIT_MANAGERID_S" };
			String[] name4 = { "UNIT_MANAGER_S" };
			parseStaff(domDoc, mainId, BRANCHTYPE.授管處, "918", job4, id4, name4);

			// 特別處理 ...................................................
			if ("3".equals(grant)) {
				String[] job5 = { STAFFJOB.L1, STAFFJOB.L4, STAFFJOB.L9 };
				String[] id5 = { "HQ_ApprID", "HQ_ReCheckID",
						"UNIT_MANAGERID_A" };
				String[] name5 = { "HQ_Appraiser", "HQ_ReCheck",
						"UNIT_MANAGER_A" };
				parseStaff(domDoc, mainId, BRANCHTYPE.營運中心, AreaBch, job5, id5,
						name5);
			} else if ("2".equals(grant) || "4".equals(grant)) {
				String[] job6 = { STAFFJOB.L1, STAFFJOB.L4 };
				String[] id6 = { "HQ_ApprID", "HQ_ReCheckID" };
				String[] name6 = { "HQ_Appraiser", "HQ_ReCheck" };
				parseStaff(domDoc, mainId, BRANCHTYPE.授管處, "918", job6, id6,
						name6);

				if ("4".equals(grant)) {
					String[] job7 = { STAFFJOB.L9 };
					String[] id7 = { "UNIT_MANAGERID_A" };
					String[] name7 = { "UNIT_MANAGER_A" };
					parseStaff(domDoc, mainId, BRANCHTYPE.營運中心, AreaBch, job6,
							id6, name6);
				}
			}

		} catch (Exception e) {
			String errmsg = "【" + strBrn + "】分行執行" + class_name
					+ " 之transferDXL時產生錯誤,dxl檔名:" + dxlName + ",dxlPath="
					+ dxlPath;
			throw new DCException(errmsg, e);
		} finally {
			if (DEBUG && logger.isDebugEnabled()) {
				logger.debug("@@@@@@@@ TOTAL_COST="
						+ (System.currentTimeMillis() - t1));
			}
		}
	}

	/**
	 * 解析簽章檔
	 * 
	 * @param domDoc
	 * @param mainId
	 * @param branchType
	 * @param branchId
	 * @param cesDate
	 * @param job
	 * @param idTag
	 * @param nameTag
	 * @throws Exception
	 */
	private void parseStaff(Document domDoc, String mainId, String branchType,
			String branchId, String[] job, String[] idTag, String[] nameTag)
			throws Exception {
		for (int i = 0; i < job.length; i++) {
			String id = this.getItemValue(domDoc, idTag[i]);
			String name = this.getItemValue(domDoc, nameTag[i]);

			// 已下幾個欄位會有多欄位值，需特別處理
			if ("Boss".toUpperCase().contains(nameTag[i].toUpperCase())) {
				String[] ids = Util.split(id, SPLIT);
				String[] names = Util.split(name, SPLIT);
				for (int j = 0; j < names.length; j++) {
					print(mainId, branchType, branchId, job[i],
							getData(ids, j), getData(names, j));
				}
			} else {
				print(mainId, branchType, branchId, job[i], id, name);
			}
		}
	}

	/**
	 * 產生文字檔
	 * 
	 * @param mainId
	 * @param branchType
	 * @param branchId
	 * @param staffJob
	 * @param staffNo
	 * @param staffName
	 */
	private void print(String mainId, String branchType, String branchId,
			String staffJob, String staffNo, String staffName) {
		L120M01FBean bean = new L120M01FBean();
		bean.setOid(Util.getOID());
		bean.setMainId(mainId);

		bean.setBranchType(branchType);
		bean.setBranchId(branchId);
		bean.setStaffJob(staffJob);
		bean.setStaffNo(staffNo);
		bean.setStaffName(staffName);

		// staffNo不是英數字則直接轉成空白
		if (!Util.isEnOrNum(bean.getStaffNo())) {
			bean.setStaffNo("");
		}

		// staffNo 或 staffName 只要有一個不是空白則轉入資料庫
		if (StringUtils.isNotBlank(bean.getStaffNo())
				|| StringUtils.isNotBlank(bean.getStaffName())) {
			this.txtWrite.println(bean.toString());
			this.parserTotal++;
		}
	}

	/**
	 * 取得資料
	 * 
	 * @param arys
	 * @param num
	 * @return
	 */
	private String getData(String[] arys, int num) {
		try {
			String value = arys[num];
			return value;
		} catch (Exception e) {
			return "";
		}
	}
}
