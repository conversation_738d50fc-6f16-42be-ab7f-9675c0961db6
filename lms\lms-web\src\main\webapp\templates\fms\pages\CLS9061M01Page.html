<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:wicket="http://wicket.apache.org/">
<body>
	<wicket:extend>
		<script type="text/javascript" src="pagejs/fms/CLS9061M01Page.js"></script>
        <div class="button-menu funcContainer" id="buttonPanel">
        	<!-- ===================================== -->
			<wicket:enclosure>
				<span wicket:id="_btnSave" />
				
				<button type="button" id="btnSave">
					<span class="ui-icon ui-icon-jcs-04" />
					<wicket:message key="button.save">儲存</wicket:message>
				</button>
				<button type="button" id="btnSend">
					<span class="ui-icon ui-icon-jcs-02" />
					<wicket:message key="button.send">呈主管覆核</wicket:message>
				</button>				
			</wicket:enclosure>
			<!-- ===================================== -->
			<wicket:enclosure>
				<span wicket:id="_btnWAIT_APPROVE" />
				
				<button type="button" id="btnAccept">
					<span class="ui-icon ui-icon-check" />
					<wicket:message key="button.check">覆核</wicket:message>
				</button>
			</wicket:enclosure>
			<!-- ===================================== -->
			<wicket:enclosure>
				<span wicket:id="_btnAPPROVED" />
				
				<button type="button" id="btnReturn">
					<!-- 退回編製中 -->
					<wicket:message key="button.return"/>「<wicket:message key="docStatus.010"/>」
				</button>	
			</wicket:enclosure>
        	
			
			<button type="button" id="btnExit" class="forview">
				<span class="ui-icon ui-icon-jcs-01"></span>
				<wicket:message key="button.exit">離開</wicket:message>
			</button>
		</div>	
			
		<div class="tit2 color-black">
            <table width="100%">
                <tr>
                    <td width="100%">
                    	<wicket:message key="doc.title">偽造證件或財力證明建檔作業</wicket:message>
					</td>
                </tr>
            </table>
        </div>
		
		<div style='padding:1em;'>
        <form id="tabForm">
        	<fieldset>
                <legend>
                    <b><wicket:message key="label.basicData">基本資料</wicket:message></b>
                </legend>
				<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
	                <tbody>
	                	<tr>
	                        <td width="30%" class="hd1" nowrap>
	                            <wicket:message key="C900M01E.custId">統一編號</wicket:message>&nbsp;&nbsp;								
	                        </td>
	                        <td>
	                            <span id="custId" />&nbsp;
								<span id="dupNo" />
	                        </td>
							<td width="30%" class="hd1" nowrap>
	                            <wicket:message key="doc.custName">姓名/名稱</wicket:message>&nbsp;&nbsp;
								<button type="button" id="btn_imp_custId">
										<wicket:message key="btn.impName">重引名稱</wicket:message>
								</button>
	                        </td>
	                        <td>
	                            <span id="custName" />&nbsp;
	                        </td>
	                    </tr>
						<tr>
	                        <td width="30%" class="hd1" nowrap>
	                            <wicket:message key="doc.docStatus">文件狀態</wicket:message>&nbsp;&nbsp;								
	                        </td>
	                        <td colspan='3'>
								<b><span id="docStatus" class="color-red" /></b>
	                        </td>
	                    </tr>
						<tr>
	                        <td width="30%" class="hd1" nowrap>
	                            <wicket:message key="C900M01E.comId">任職公司統編</wicket:message>&nbsp;&nbsp;
								
	                        </td>
	                        <td>
	                            <input type="text" id="comId" name="comId" class="" maxlength="10" size="10" />&nbsp;
	                        </td>
							<td width="30%" class="hd1" nowrap>
	                            <wicket:message key="C900M01E.comName">任職公司名稱</wicket:message>&nbsp;&nbsp;
								<button type="button" id="btn_imp_comId">
										<wicket:message key="btn.impName">重引名稱</wicket:message>
								</button>
	                        </td>
	                        <td>
	                            <input type="text" id="comName" name="comName" class="" maxlength="250" size="30" />&nbsp;
	                        </td>
	                    </tr>
						<tr>
	                        <td width="30%" class="hd1" nowrap>
	                            <wicket:message key="C900M01E.comTarget">服務單位地址</wicket:message>&nbsp;&nbsp;
	                        </td>
	                        <td colspan='3'>
	                            <input type="text" id="comCity" name="comCity" style="display:none;" />
								<input type="text" id="comZip" name="comZip" style="display:none;" />
								<input type="text" id="comAddr" name="comAddr" style="display:none;" />
								<a href="#" id="comTargetLink" class="readOnly" ><span id="comTarget" class="field comboSpace" /></a>
	                        </td>
	                    </tr>
						<tr>
	                        <td width="30%" class="hd1" nowrap>
	                        	<span class="text-red">＊</span>
	                            <wicket:message key="C900M01E.sourceNo">來源文號</wicket:message>&nbsp;&nbsp;
	                        </td>
	                        <td colspan='3'>
	                            <input type="text" id="sourceNo" name="sourceNo" maxlength="128" maxlengthC="42" size="100" class="required" />&nbsp;
	                        </td>
	                    </tr>
						<tr>
	                        <td width="30%" class="hd1" nowrap>
	                            <wicket:message key="C900M01E.dataSrc">資料來源</wicket:message>&nbsp;&nbsp;
	                        </td>
	                        <td>
	                            <label><input type="radio" id="dataSrc" name="dataSrc" value="0" class="required"><wicket:message key="C900M01E.dataSrc.0">卡務中心</wicket:message></label>
								<label><input type="radio" id="dataSrc" name="dataSrc" value="9" class="required"><wicket:message key="C900M01E.dataSrc.9">其他</wicket:message></label>
	                        </td>
							<td width="30%" class="hd1" nowrap>
	                            <wicket:message key="C900M01E.isClosed">是否解除</wicket:message>&nbsp;&nbsp;
	                        </td>
	                        <td>
	                        	<label><input type="radio" id="isClosed" name="isClosed" value="N" class="required"><wicket:message key="C900M01E.isClosed.N">未解除</wicket:message>(<wicket:message key="C900M01E.isClosed.N.desc"/>)</label>&nbsp;
	                            <label><input type="radio" id="isClosed" name="isClosed" value="Y" class="required"><wicket:message key="C900M01E.isClosed.Y">解除</wicket:message>(<wicket:message key="C900M01E.isClosed.Y.desc"/>)</label>							
	                        </td>
	                    </tr>
					</tbody>
	            </table>
			</fieldset>
			
			<fieldset>
                    <legend>
                        <b><wicket:message key="doc.docUpdateLog">文件異動紀錄</wicket:message></b>
                    </legend>
                    <div class="funcContainer">
                        <div wicket:id="_docLog">
                        </div>
                    </div>
                    <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tbody>
                            <tr>
                                <td width="20%" class="hd1">
                                    <wicket:message key="doc.creator">文件建立者</wicket:message>&nbsp;&nbsp;
                                </td>
                                <td width="30%">
                                    <span id="creator" name="creator"></span>
                                    (<span id="createTime" name="createTime"></span>)
                                </td>
                                <td width="20%" class="hd1">
                                    <wicket:message key="doc.lastUpdater">最後異動者</wicket:message>&nbsp;&nbsp;
                                </td>
                                <td width="30%">
                                    <span id="updater" name="updater"></span>
                                    (<span id="updateTime" name="updateTime"></span>)
                                </td>
                            </tr>                            
                        </tbody>
                    </table>
                </fieldset>
        </form>			
		</div>
			
		<!-- 地址 -->
		<div id="AddrThickBox" style="display:none;" >
			<div id="AddrDiv" >
				<form id="AddrForm" >
					<table class="tb2" width="100%">
						<tr>
							<td width="30%" class="hd2" align="right"><span class="text-red">＊</span><wicket:message key="label.city">縣市別</wicket:message>&nbsp;&nbsp;</td>
							<td ><select id="AddrCity" name="AddrCity" class="required" /></td>
						</tr>
						<tr>
							<td class="hd2" align="right"><span class="text-red">＊</span><wicket:message key="label.zip">鄉鎮市區</wicket:message>&nbsp;&nbsp;</td>
							<td><select id="AddrZip" name="AddrZip" class="required" /></td>
						</tr>
						<tr>
							<td class="hd2" align="right"><span class="text-red">＊</span><wicket:message key="label.addr">地址</wicket:message>&nbsp;&nbsp;</td>
							<td><input id="AddrAddr" name="AddrAddr" class="max required" maxlength="192" size="50" /></td>
						</tr>
					</table>
					<input style="display:none;" /><!-- Prevent click enter to do submit -->
				</form>
			</div>
		</div>
	</wicket:extend>
</body>
</html>
