/* 
 *  CLS1021GridHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.handler.grid;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.cls.pages.CLS1021M01Page;
import com.mega.eloan.lms.cls.service.CLS1021Service;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.mfaloan.service.MisLNF030Service;
import com.mega.eloan.lms.model.C102M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 購置房屋擔保放款風險權數檢核表
 * </pre>
 * 
 * @since 2013/01/07
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/01/07,GaryChang,new
 *          </ul>
 */
@Scope("request")
@Controller("cls1021gridhandler")
public class CLS1021GridHandler extends AbstractGridHandler {

	@Resource
	CLS1021Service cls1021Service;

	@Resource
	UserInfoService userInfoService;

	@Resource
	BranchService branchService;

	@Resource
	DocFileService docFileService;

	@Resource
	CodeTypeService codeTypeService;
	@Resource
	LMSService lmsService;
	@Resource
	MisCustdataService miscustdataservice;
	@Resource
	MisLNF030Service mislnf030service;

	@Resource
	CLSService clsService;
	
	/**
	 * 購置房屋擔保放款風險權數檢核表grid
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryC102m01a(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String docStatus = Util.nullToSpace(params
				.getString(EloanConstants.DOC_STATUS));

		String[] docStatusArray = docStatus
				.split(UtilConstants.Mark.SPILT_MARK);

		pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
				docStatusArray);// 取得文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"c102a01a.authUnit", user.getUnitNo());

		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		Page<? extends GenericBean> page = cls1021Service.findPage(
				C102M01A.class, pageSetting);

		List<C102M01A> c102m01alist = (List<C102M01A>) page.getContent();
		StringBuilder cntrNo = new StringBuilder("");
		for (C102M01A c102m01a : c102m01alist) {
			c102m01a.setCreator(userInfoService.getUserName(c102m01a
					.getCreator()));
			// 設定顯示名稱 使用者id+重複序號
			c102m01a.setCustId(c102m01a.getCustId() + " " + c102m01a.getDupNo());
			cntrNo.setLength(0);
		}
		return new CapGridResult(c102m01alist, page.getTotalRow());

	}

	/**
	 * 查詢購置房屋擔保放款風險權數檢核表外部的grid(已覆核)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryC102m01a3(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String[] docStatusArray = new String[] { CreditDocStatusEnum.海外_已核准
				.getCode() };
		String type = Util.nullToSpace(params.getString("type"));

		pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
				docStatusArray);// 取得文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"c102a01a.authUnit", user.getUnitNo());
		// 狀態1 是用客戶ID去查
		switch (Util.parseInt(type)) {
		case 1:
			String custId = Util.nullToSpace(params.getString("custId"));
			String dupNo = Util.nullToSpace(params.getString("dupNo"));
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId",
					custId);
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dupNo",
					dupNo);
			break;
		case 2:
			String cntrNo = Util.nullToSpace(params.getString("cntrNo"));
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "cntrNo",
					cntrNo);
			break;
		case 3:
			String approveTime = Util.nullToSpace(params
					.getString("approveTime"));
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"approveTime", CapDate.parseDate(approveTime));
			break;

		default:
			break;
		}

		Page<? extends GenericBean> page = cls1021Service.findPage(
				C102M01A.class, pageSetting);
		List<C102M01A> c102m01as = (List<C102M01A>) page.getContent();
		StringBuilder cntrNo = new StringBuilder("");
		for (C102M01A c102m01a : c102m01as) {
			// 設定顯示名稱 使用者id+重複序號
			c102m01a.setCustId(c102m01a.getCustId() + " " + c102m01a.getDupNo());
			cntrNo.setLength(0);
			// 確認全部動用是否有選
		}
		return new CapGridResult(c102m01as, page.getTotalRow());
	}

	/**
	 * 查詢顧客名稱
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryGetCustName(ISearch pageSetting,
			PageParameters params) throws CapException {
		Properties prop = null;
		prop = MessageBundleScriptCreator
				.getComponentResource(CLS1021M01Page.class);
		String custId = Util.nullToSpace(params.getString("custId"));
		String dupNo = Util.nullToSpace(params.getString("dupNo"));
		CapMapGridResult list = new CapMapGridResult();
		List<Map<String, Object>> rowData = new ArrayList<Map<String, Object>>();
		Map<String, Object> map = miscustdataservice.findCNameByIdDupNo(custId,
				dupNo);
		if (map != null) {
			rowData.add(map);
		}
		if (Util.isEmpty(map)) {
			throw new CapMessageException(prop.getProperty("C102M01A.error5"),
					getClass());
		}
		list.setRowData(rowData);
		return list;

	}

	/**
	 * 查詢放款帳號
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryGetaLoanAC(ISearch pageSetting,
			PageParameters params) throws CapException {
		Properties prop = null;
		prop = MessageBundleScriptCreator
				.getComponentResource(CLS1021M01Page.class);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String custId = Util.nullToSpace(params.getString("custId"));
		String dupNo = Util.nullToSpace(params.getString("dupNo"));
		String cntrNo = Util.nullToSpace(params.getString("cntrNo"));
		String branchId = Util.nullToSpace(user.getUnitNo());
		CapMapGridResult list = new CapMapGridResult();
		List<Map<String, Object>> rowData = null;
		if(clsService.is_function_on_codetype("J-109-0084")){
			rowData = mislnf030service.selaLoanAC_allow_403_603(custId,
					dupNo, branchId, cntrNo);
		}else{
			rowData = mislnf030service.selaLoanAC(custId,
					dupNo, branchId, cntrNo);	
		}
		
		if (rowData.size() == 0) {
			throw new CapMessageException(prop.getProperty("C102M01A.error6"),
					getClass());
		}
		list.setRowData(rowData);
		return list;
	}

}
