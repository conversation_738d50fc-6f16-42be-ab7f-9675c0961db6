package com.mega.eloan.lms.cls.service.impl;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONException;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.iisigroup.cap.component.PageParameters;
import com.iisigroup.cap.component.impl.CapMvcParameters;
import com.mega.eloan.common.aml.AmlStrategy;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.constants.SysParamConstants;
import com.mega.eloan.common.enums.WitcherFinApiEnum;
import com.mega.eloan.common.gwclient.Brmp004O;
import com.mega.eloan.common.gwclient.Brmp005O;
import com.mega.eloan.common.gwclient.IdentificationCheckGwReqMessage;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.model.ElAml;
import com.mega.eloan.common.model.ElAmlItem;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.service.AmlService;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.common.service.RPAService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.service.WitcherFinService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.ClsScoreUtil;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.ClsScoreConstants;
import com.mega.eloan.lms.base.constants.Score;
import com.mega.eloan.lms.base.constants.ScoreCardLoan;
import com.mega.eloan.lms.base.constants.ScoreNotHouseLoan;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.eloan.lms.base.service.AMLRelateService;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.RPAProcessService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.base.service.ScoreService;
import com.mega.eloan.lms.base.service.impl.RPAProcessServiceImpl;
import com.mega.eloan.lms.cls.common.ClsUtil;
import com.mega.eloan.lms.cls.constants.ClsConstants;
import com.mega.eloan.lms.cls.pages.CLS1131S01Page;
import com.mega.eloan.lms.cls.panels.CLS1131S01Panel;
import com.mega.eloan.lms.cls.report.CLS1131R03RptService;
import com.mega.eloan.lms.cls.service.CLS1131Service;
import com.mega.eloan.lms.cls.service.CLS1141Service;
import com.mega.eloan.lms.cls.service.CLS1220Service;
import com.mega.eloan.lms.dao.C101S01HDao;
import com.mega.eloan.lms.dao.C101S01SDao;
import com.mega.eloan.lms.dao.C101S01VDao;
import com.mega.eloan.lms.dao.C101S01XDao;
import com.mega.eloan.lms.dao.C101S01YDao;
import com.mega.eloan.lms.dao.C101S02ADao;
import com.mega.eloan.lms.dao.C101S02BDao;
import com.mega.eloan.lms.dao.C101S02SDao;
import com.mega.eloan.lms.dao.C120S01WDao;
import com.mega.eloan.lms.dao.C120S01YDao;
import com.mega.eloan.lms.dao.C120S02ADao;
import com.mega.eloan.lms.dao.C120S02SDao;
import com.mega.eloan.lms.dao.C122M01ADao;
import com.mega.eloan.lms.dao.C160S02ADao;
import com.mega.eloan.lms.dao.C900M01EDao;
import com.mega.eloan.lms.dao.EJCICCOMMONDao;
import com.mega.eloan.lms.dao.L120S01MDao;
import com.mega.eloan.lms.dao.L120S01NDao;
import com.mega.eloan.lms.dao.L120S01ODao;
import com.mega.eloan.lms.dao.L161S01DDao;
import com.mega.eloan.lms.eai.service.EAIService;
import com.mega.eloan.lms.ejcic.service.EjcicService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.enums.NcodeEnum;
import com.mega.eloan.lms.etch.service.EtchService;
import com.mega.eloan.lms.mfaloan.service.LNLNBadCRService;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.mfaloan.service.MisEJF305Service;
import com.mega.eloan.lms.mfaloan.service.MisELLNGTEEService;
import com.mega.eloan.lms.mfaloan.service.MisElCUS25Service;
import com.mega.eloan.lms.mfaloan.service.MisElf901Service;
import com.mega.eloan.lms.mfaloan.service.MisElf902Service;
import com.mega.eloan.lms.mfaloan.service.MisElremainService;
import com.mega.eloan.lms.mfaloan.service.MisGrpcmpService;
import com.mega.eloan.lms.mfaloan.service.MisLNF030Service;
import com.mega.eloan.lms.mfaloan.service.MisLNF150Service;
import com.mega.eloan.lms.mfaloan.service.MisLNF251Service;
import com.mega.eloan.lms.mfaloan.service.MisLnunIdService;
import com.mega.eloan.lms.mfaloan.service.MisStoredProcService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C101M01A;
import com.mega.eloan.lms.model.C101S01A;
import com.mega.eloan.lms.model.C101S01B;
import com.mega.eloan.lms.model.C101S01C;
import com.mega.eloan.lms.model.C101S01D;
import com.mega.eloan.lms.model.C101S01E;
import com.mega.eloan.lms.model.C101S01G;
import com.mega.eloan.lms.model.C101S01G_N;
import com.mega.eloan.lms.model.C101S01H;
import com.mega.eloan.lms.model.C101S01I;
import com.mega.eloan.lms.model.C101S01J;
import com.mega.eloan.lms.model.C101S01K;
import com.mega.eloan.lms.model.C101S01L;
import com.mega.eloan.lms.model.C101S01M;
import com.mega.eloan.lms.model.C101S01N;
import com.mega.eloan.lms.model.C101S01O;
import com.mega.eloan.lms.model.C101S01P;
import com.mega.eloan.lms.model.C101S01Q;
import com.mega.eloan.lms.model.C101S01Q_N;
import com.mega.eloan.lms.model.C101S01R;
import com.mega.eloan.lms.model.C101S01R_N;
import com.mega.eloan.lms.model.C101S01S;
import com.mega.eloan.lms.model.C101S01U;
import com.mega.eloan.lms.model.C101S01V;
import com.mega.eloan.lms.model.C101S01W;
import com.mega.eloan.lms.model.C101S01X;
import com.mega.eloan.lms.model.C101S01Y;
import com.mega.eloan.lms.model.C101S02B;
import com.mega.eloan.lms.model.C101S02C;
import com.mega.eloan.lms.model.C101S02S;
import com.mega.eloan.lms.model.C101S04W;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01B;
import com.mega.eloan.lms.model.C120S01Q;
import com.mega.eloan.lms.model.C120S01W;
import com.mega.eloan.lms.model.C120S01X;
import com.mega.eloan.lms.model.C120S01Y;
import com.mega.eloan.lms.model.C120S02C;
import com.mega.eloan.lms.model.C120S02S;
import com.mega.eloan.lms.model.C122M01A;
import com.mega.eloan.lms.model.C900M01E;
import com.mega.eloan.lms.model.C900M03A;
import com.mega.eloan.lms.model.C900S03A;
import com.mega.eloan.lms.model.C900S03B;
import com.mega.eloan.lms.model.EJCICCOMMON;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120S01M;
import com.mega.eloan.lms.model.L120S01N;
import com.mega.eloan.lms.model.L120S01O;
import com.mega.eloan.lms.model.L120S09A;
import com.mega.eloan.lms.model.L120S09B;
import com.mega.eloan.lms.model.L120S19A;
import com.mega.eloan.lms.model.L161S01D;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapFormatException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.formatter.NumericFormatter;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapMath;
import tw.com.iisi.cap.util.CapString;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.iisi.cap.utils.CapEntityUtil;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 個金徵信作業
 * </pre>
 * 
 * @since 2012/10/11
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/10/11,Fantasy,new
 *          <li>2013/06/17,Fantasy,deleteByJPQL(delList) 改為 delete(delList);
 *          <li>2013/06/18,Fantasy,throw save Exception
 *          <li>2013/07/09,Fantasy,調整歸戶查詢,先查他行餘額,再查本行餘額
 *          <li>2013/08/02,Rex,修改判斷最近一次退票日期為********也要呈現
 *          <li>2013/08/05,Rex,修改退票記錄判斷，當有退票張數表示有退票記錄
 *          </ul>
 */

@Service
@Qualifier("CLS1131Service")
public class CLS1131ServiceImpl extends CLS1130ServiceImpl implements
		CLS1131Service {

	private static final int MAXLEN_C101S01E_ENAME = StrUtils
			.getEntityFileldLegth(C101S01E.class, "eName", 120);

	private static final Logger logger = LoggerFactory
			.getLogger(CLS1131ServiceImpl.class);

	@Resource
	AMLRelateService amlRelateService;

	@Resource
	AmlService amlService;

	@Resource
	CodeTypeService codeTypeService;

	@Resource
	MisdbBASEService misBaseService;

	@Resource
	MisELLNGTEEService misEllngteeService;

	@Resource
	EjcicService ejcicService;

	@Resource
	EtchService etchService;

	@Resource
	ScoreService scoreService;

	@Resource
	MisLnunIdService misLnunIdService;

	@Resource
	MisLNF150Service misLNF150Service;

	@Resource
	MisStoredProcService misStoredProcService;

	@Resource
	MisElremainService misElremainService;

	@Resource
	MisCustdataService misCustdataService;

	@Resource
	EAIService eaiService;

	@Resource
	MisEJF305Service misEJF305Service;

	@Resource
	LMSService lmsService;

	@Resource
	CLSService clsService;

	@Resource
	L120S01MDao l120s01mDao;

	@Resource
	L120S01NDao l120s01nDao;

	@Resource
	L120S01ODao l120s01oDao;

	@Resource
	C900M01EDao c900m01eDao;

	@Resource
	C122M01ADao c122m01aDao;
	
	@Resource
	EJCICCOMMONDao ejciccommonDao;
	
	@Resource
	C101S01HDao c101s01hDao;

	@Resource
	C101S01VDao c101s01vDao;
	
	@Resource
	ICustomerService iCustomerService;

	@Resource
	MisGrpcmpService misGrpcmpService;

	@Resource
	CLS1220Service cls1220Service;

	@Resource
	RetrialService retrialService;

	@Resource
	MisElCUS25Service misElcus25Service;

	@Resource
	LNLNBadCRService lnLnBadCrService;

	@Resource
	C101S01SDao c101s01sDao;

	@Resource
	MisElf902Service misElf902Service;

	@Resource
	BranchService branchService;

	@Resource
	CLS1131R03RptService cls1131R03RptService;

	@Resource
	MisElf901Service misElf901Service;

	@Resource
	RPAService rpaservice;
	
	@Resource
	DocFileService docFileService;
	
	@Resource
	L161S01DDao l161s01dDao;
	
	@Resource
	SysParameterService sysparamService;
	
	@Resource
	C101S01XDao c101s01xDao;
	
	@Resource
	RPAProcessService rpaProcessService;
	
	@Resource
	CLS1141Service cls1141Service;
	
	@Resource
	EloandbBASEService eloandbBASEService;
	
	@Resource
	MisLNF030Service misLNF030Service;
	
	@Resource
	MisLNF251Service misLNF251Service;

	@Resource
	C101S01YDao c101s01yDao;
	
	@Resource
	C120S01YDao c120s01yDao;

	@Resource
	C120S01WDao c120s01wDao;

	@Resource
	C101S02ADao c101s02aDao;

	@Resource
	C120S02ADao c120s02aDao;
	
	@Resource
	C160S02ADao c160s02aDao;

	@Resource
	WitcherFinService witcherFinService;

	@Resource
	C101S02BDao c101s02bDao;
	
	@Resource
	C101S02SDao c101s02sDao;
	
	@Resource
	C120S02SDao c120s02sDao;

	@Override
	public void save(String mainId, String custId, String dupNo, String type,
			List<GenericBean> list) {
		if (Util.isNotEmpty(type)) {
			if (ClsConstants.C101S01E.主從債務人_不含本次資料.equals(type)) {
				save(mainId, custId, dupNo, list, C101S01K.class,
						C101S01O.class);
			} else if (ClsConstants.C101S01E.本行利害關係人.equals(type)) {
				List<C101S01L> delList = c101s01lDao.findByIndex01(mainId,
						custId, dupNo, ClsConstants.xType.銀行法, null, null);
				c101s01lDao.delete(delList);
				save(mainId, custId, dupNo, list, new Class[] {});
			} else if (ClsConstants.C101S01E.金控利害關係人44條.equals(type)) {
				List<C101S01L> delList = c101s01lDao.findByIndex01(mainId,
						custId, dupNo, ClsConstants.xType.金控法44條, null, null);
				c101s01lDao.delete(delList);
				save(mainId, custId, dupNo, list, new Class[] {});
			} else if (ClsConstants.C101S01E.金控利害關係人45條.equals(type)) {
				List<C101S01L> delList = c101s01lDao.findByIndex01(mainId,
						custId, dupNo, ClsConstants.xType.金控法45條, null, null);
				c101s01lDao.delete(delList);
				save(mainId, custId, dupNo, list, new Class[] {});
			} else if (ClsConstants.C101S01E.近一年內不含查詢當日非Z類被聯行查詢紀錄明細
					.equals(type)) {
				save(mainId, custId, dupNo, list, C101S01M.class);
			} else if (ClsConstants.C101S01E.歸戶_本行餘額為aLoan資料_他行餘額為聯徵資料
					.equals(type)) {
				save(mainId, custId, dupNo, list, C101S01N.class);
			} else if (ClsConstants.C101S01E.擔任負責人或董監事之企業是否於本行有授信額度達一億元以上
					.equals(type)) {
				save(mainId, custId, dupNo, list, C101S01P.class);
			} else if (ClsConstants.C101S01E.大數據風險報告.equals(type)) {

			} else {
				save(mainId, custId, dupNo, list, new Class[] {});
			}
		} else {
			save(mainId, custId, dupNo, list, C101S01K.class, C101S01O.class,
					C101S01L.class, C101S01M.class, C101S01N.class,
					C101S01P.class);
		}
	}

	@Override
	public void save(String mainId, String custId, String dupNo,
			List<GenericBean> list, Class<?>... del_classes) {

		// 刪除
		if (del_classes != null) {
			List<GenericBean> delList = new ArrayList<GenericBean>();
			for (Class<?> clazz : del_classes) {
				List<? extends GenericBean> beans = findListByRelationKey(
						clazz, mainId, custId, dupNo);

				if (clazz == C101S01P.class) {
					HashSet<String> savedComCustId = new HashSet<String>();
					for (GenericBean model : list) {
						if (model instanceof C101S01P) {
							savedComCustId.add(((C101S01P) model)
									.getComCustId());
						}
					}
					for (GenericBean model : beans) {
						if (model instanceof C101S01P) {
							String comCustId = ((C101S01P) model)
									.getComCustId();
							if (!savedComCustId.contains(comCustId)) {
								delList.add(model);
							}
						}
					}
					continue;
				}

				for (GenericBean model : beans) {
					delList.add(model);
				}
			}
			delete(delList);
			// deleteByJPQL(delList);
		}

		for (GenericBean model : list) {
			try {
				model.set(EloanConstants.MAIN_ID, mainId);
				if (model instanceof C101S01K) {
					// 排除主從債人檔C101S01K
				} else {
					model.set("custId", custId);
					model.set("dupNo", dupNo);
				}
				// save(model);
			} catch (CapException e) {
				logger.error("[model.set]", e);
			} finally {
				save(model); // throw save Exception
			}
		}

		// List<GenericBean> newList = new ArrayList<GenericBean>();
		// for (GenericBean model : list) {
		// try {
		// boolean flage = false;
		// for (Class<?> clazz : classes) {
		// if (model.getClass().getName().equals(clazz.getName())) {
		// GenericBean newModel = null;
		// try {
		// newModel = (GenericBean) Class.forName(
		// clazz.getName()).newInstance();
		// } catch (IllegalAccessException e) {
		// logger.error(e.getMessage());
		// } catch (InstantiationException e) {
		// logger.error(e.getMessage());
		// } catch (ClassNotFoundException e) {
		// logger.error(e.getMessage());
		// }
		// Util.beanCopy(model, newModel);
		// newModel.set("oid", null);
		// newModel.set(EloanConstants.MAIN_ID, mainId);
		// newModel.set("custId", custId);
		// newModel.set("dupNo", dupNo);
		// flage = true;
		//
		// newList.add(newModel);
		// }
		// }
		// if (!flage) {
		// model.set(EloanConstants.MAIN_ID, mainId);
		// model.set("custId", custId);
		// model.set("dupNo", dupNo);
		// newList.add(model);
		// }
		//
		// } catch (CapException e) {
		// logger.error(e.getMessage());
		// }
		// }
		//
		// // 儲存
		// save(newList);
	}

	@Override
	public C101M01A findC101M01A(String ownBrId, String custId, String dupNo) {
		return c101m01aDao.findByUniqueKey(null, ownBrId, custId, dupNo);
	}

	public String changCityR(String key) {
		// 桃園升格0024還未更新時處理方法
		if ("桃園縣".equals(key)) {
			key = Util.trim(key).replace("縣", "市");
		}
		return key;
	}

	public String changTownR(String key) {
		// 桃園升格0024還未更新時處理方法
		if (ArrayUtils.indexOf(ClsUtil.HVILLAGES, key) > -1) {
			key = Util.trim(key).replace("鄉", "區");
			key = Util.trim(key).replace("市", "區");
			key = Util.trim(key).replace("鎮", "區");
		}
		return key;
	}

	public String changLeeR(String key) {
		// 桃園升格0024還未更新時處理方法
		if (ArrayUtils.indexOf(ClsUtil.HTOWNS, key) > -1) {
			key = Util.trim(key).replace("村", "里");
		}
		return key;
	}

	@Override
	public Map<String, Object> findMisCustData(String custId, String dupNo) {
		Map<String, Object> result = null;
		List<Map<String, Object>> list = misBaseService
				.findCUSTDATA_selCustData(custId, dupNo);
		if (!list.isEmpty()) {
			result = list.get(0);
			// 申貸戶通訊地址
			List<Map<String, Object>> addrList = misBaseService
					.findELCUS21_selAddrr(custId, dupNo);
			for (Map<String, Object> map : addrList) {
				JSONObject json = parseZip(Util.trim(map.get("ADDRZIP")));
				map.put(ClsConstants.City.縣市代碼,
						Util.trim(json.get(ClsConstants.City.縣市代碼)));
				map.put(ClsConstants.City.郵地區號,
						Util.trim(json.get(ClsConstants.City.郵地區號)));

				// 桃園升格0024還未更新時處理方法
				if ("桃園縣".equals(Util.trim(MapUtils.getString(map, "CITYR")))) {
					map.put("CITYR", changCityR(Util.trim(MapUtils.getString(
							map, "CITYR"))));
					map.put("TOWNR", changTownR(Util.trim(MapUtils.getString(
							map, "TOWNR"))));
					map.put("LEER", changLeeR(Util.trim(MapUtils.getString(map,
							"LEER"))));
				}

			}
			result.put("addrs", addrList);
			// 服務單位電話
			result.put("tels",
					misBaseService.findELCUS28_selTelno(custId, dupNo));
			// 申貸戶行動電話
			result.put("phones",
					misBaseService.findELCUS23_selMpno(custId, dupNo));
			// 申貸戶電子郵件地址
			result.put("mails",
					misBaseService.findELCUS22_selMailaddr(custId, dupNo));
		}
		return result;
	}

	@Override
	public Map<String, Object> findMisMateData(String custId, String dupNo) {
		Map<String, Object> result = new HashMap<String, Object>();
		List<Map<String, Object>> list = misBaseService
				.findCUSTDATA_selMCustId(custId, dupNo);
		if (!list.isEmpty())
			result = list.get(0);

		return result;
	}

	@Override
	public JSONObject parseZip(String zip) {
		JSONObject result = new JSONObject();
		String tempZip = Util.trim(zip);
		if (Util.isNotEmpty(tempZip) && zip.length() == 5) {
			String fZip = tempZip.substring(0, 3);
			CodeType zipCode = codeTypeService.findByCodeTypeAndCodeValue(
					ClsConstants.codeType.鄉鎮區, fZip);
			if (zipCode != null) {
				CodeType cityCode = codeTypeService.findByTypeAndDesc2(
						ClsConstants.codeType.縣市別,
						Util.trim(zipCode.getCodeDesc3()));
				if (cityCode != null)
					result.put(ClsConstants.City.縣市代碼,
							Util.trim(cityCode.getCodeValue())); // 縣市代碼
			}
			result.put(ClsConstants.City.郵地區號, fZip); // 鄉鎮市區代碼
		}
		return result;
	}

	@Override
	public JSONObject queryData(List<GenericBean> dataList, C101S01E c101s01e,
			C101S01J c101s01j, PageParameters params)
			throws CapMessageException {
		JSONObject result = new JSONObject();

		String type = Util.trim(params.getString("type"));
		String custId = Util.trim(params.getString("custId"));
		String prodId = ejcicService.get_cls_PRODID(custId);

		if (Util.isEmpty(type)) {
			String[] types = { ClsConstants.C101S01E.婉卻紀錄,
					ClsConstants.C101S01E.本行利害關係人,
					ClsConstants.C101S01E.金控利害關係人44條,
					ClsConstants.C101S01E.金控利害關係人45條,
					ClsConstants.C101S01E.主從債務人_不含本次資料,
					ClsConstants.C101S01E.對同一自然人授信總餘額比率,
					ClsConstants.C101S01E.歸戶_本行餘額為aLoan資料_他行餘額為聯徵資料,
					ClsConstants.C101S01E.近一年內不含查詢當日非Z類被聯行查詢紀錄明細,
					ClsConstants.C101S01E.黑名單,
					ClsConstants.C101S01E.證券暨期貨違約交割紀錄,
					ClsConstants.C101S01E.退票紀錄, ClsConstants.C101S01E.拒絕往來紀錄,
					ClsConstants.C101S01E.主債務逾期_催收_呆帳紀錄,
					ClsConstants.C101S01E.信用卡強停紀錄,
					ClsConstants.C101S01E.擔任負責人或董監事之企業是否於本行有授信額度達一億元以上,
					ClsConstants.C101S01E.疑似偽造證件或財力證明,
					ClsConstants.C101S01E.授信信用風險管理_遵循檢核,
					ClsConstants.C101S01E.異常通報紀錄,
					ClsConstants.C101S01E.財管理財客戶等級,
					ClsConstants.C101S01E.大數據風險報告,
					ClsConstants.C101S01E.聯徵T70證券暨期貨違約交割記錄資訊,
					ClsConstants.C101S01E.聯徵B42從債務查詢_擔保品類別,
					ClsConstants.C101S01E.聯徵B42共同債務查詢_擔保品類別,
					ClsConstants.C101S01E.是否有近一年有二戶以上授信借貸結案紀錄,
					ClsConstants.C101S01E.是否有近三年有二戶以上授信借貸結案紀錄};

			for (String t : types) {
				params.put("type", t);
				JSONObject data = querySingleData(result, dataList, c101s01e,
						c101s01j, params, prodId);
				if (data != null) {
					result.putAll(data);
				}
			}

			// 是否為自然人 add by fantasy 2013/03/28
			boolean naturalFlag = LMSUtil.check2(custId); // 是否為自然人
			if (!naturalFlag) {
				String addTypes[] = { ClsConstants.C101S01E.身分證補換發紀錄,
						ClsConstants.C101S01E.成年監護制度查詢紀錄 };
				for (String t : addTypes) {
					params.put("type", t);
					JSONObject data = querySingleData(result, dataList,
							c101s01e, c101s01j, params, prodId);
					if (data != null) {
						result.putAll(data);
					}
				}
			}
		} else {
			if (ClsConstants.C101S01E.授信信用風險管理_遵循檢核.equals(type)) {
				// 再多查詢 銀行法、金控法 的 利害關係人
				String[] types = { ClsConstants.C101S01E.本行利害關係人,
						ClsConstants.C101S01E.金控利害關係人44條,
						ClsConstants.C101S01E.金控利害關係人45條,
						ClsConstants.C101S01E.授信信用風險管理_遵循檢核 };

				for (String t : types) {
					params.put("type", t);
					JSONObject data = querySingleData(result, dataList,
							c101s01e, c101s01j, params, prodId);
					if (data != null) {
						result.putAll(data);
					}
				}
			} else {
				JSONObject data = querySingleData(result, dataList, c101s01e,
						c101s01j, params, prodId);
				if (data != null) {
					result.putAll(data);
				}
			}
		}
		return result;
	}

	private JSONObject querySingleData(JSONObject resultMap,
			List<GenericBean> dataList, C101S01E c101s01e, C101S01J c101s01j,
			PageParameters params, String prodId) throws CapMessageException {

		JSONObject result = new JSONObject();

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String type = Util.trim(params.getString("type"));

		String ejcicQdate = Util.trim(params
				.getString(ClsConstants.C101S01E.聯徵查詢日期));
		String qDate = "";
		if (Util.isNotEmpty(ejcicQdate)) {
			qDate = Util.trim(TWNDate.valueOf(ejcicQdate).toTW('/'));
		}
		// ~~~~~~~~~~~~~~~~~~~~~~
		List<Map<String, String>> queryIdList = new ArrayList<Map<String, String>>();
		Map<String, String> mMap = new HashMap<String, String>();
		mMap.put("custId", custId);
		mMap.put("dupNo", dupNo);
		queryIdList.add(mMap);

		// =============================
		// 取得配偶統編 add by fantasy 2013/06/10
		try {
			JSONObject C101S01DJson = ClsUtil.getJson(params, C101S01D.class);
			if (C101S01DJson != null) {
				String mCustId = Util.trim(C101S01DJson.get("mCustId"));
				String mDupNo = Util.trim(C101S01DJson.get("mDupNo"));
				if (Util.isNotEmpty(mCustId) && !custId.equals(mCustId)) {
					Map<String, String> sMap = new HashMap<String, String>();
					sMap.put("custId", mCustId);
					sMap.put("dupNo", Util.isEmpty(mDupNo) ? "0" : mDupNo);
					sMap.put("mName", Util.trim(C101S01DJson.get("mName")));// 配偶姓名
					queryIdList.add(sMap);
				}
			}
		} catch (CapException e1) {
			//
		}

		if (queryIdList.size() == 1) {
			// 只有本人, 在C101S01D 未登錄配偶資料, 則再到 0024 查
			Map<String, Object> custId_0024 = misCustdataService.findByIdDupNo(
					custId, dupNo);
			if (custId_0024 != null) {
				String mateid_0024 = Util.trim(custId_0024.get("MATEID"));
				String mDupNo_0024 = "";
				if (Util.isNotEmpty(mateid_0024) && !custId.equals(mateid_0024)) {
					Map<String, String> sMap = new HashMap<String, String>();
					sMap.put("custId", mateid_0024);
					sMap.put("dupNo", Util.isEmpty(mDupNo_0024) ? "0"
							: mDupNo_0024);
					sMap.put("mName", Util.trim(custId_0024.get("MATENM")));// 配偶姓名
					queryIdList.add(sMap);
				}
			}
		}
		// =============================
		boolean naturalFlag = LMSUtil.check2(custId); // 是否為自然人

		// 用 haveData 來決定寫入 C101S01E 的值是 [有 or 無]
		String haveData_str = "F"; // 用 {T, F, }來區分
		// 婉卻紀錄
		if (ClsConstants.C101S01E.婉卻紀錄.equals(type)) {
			Map<String, Object> map = misLnunIdService.queryReject(custId,
					dupNo);
			if (map != null) {
				JSONObject C101M01AJson = new JSONObject();
				C101M01AJson.put("bfRejBranch", Util.trim(map.get("REGBR")));
				C101M01AJson.put("bfRejReason", Util.trim(map.get("REFUSEDS")));
				C101M01AJson.put("bfRejDate", Util.trim(map.get("REGDT")));
				C101M01AJson.put("bfRejCase", Util.trim(map.get("STATUSCD")));
				C101M01AJson.put("rejectCase", Util.trim(map.get("STATUSCD")));
				C101M01AJson.put("rejectMemo", "");

				result.put(ClsUtil.CLS1131_QUERY_SINGLE_C101M01A_1,
						C101M01AJson);

				haveData_str = "T";
			} else {
				// 如果找不到資料時要將原本的值清空。
				JSONObject C101M01AJson = new JSONObject();
				C101M01AJson.put("bfRejBranch", "");
				C101M01AJson.put("bfRejReason", "");
				C101M01AJson.put("bfRejDate", "");
				C101M01AJson.put("bfRejCase", "");
				C101M01AJson.put("rejectCase", "");
				C101M01AJson.put("rejectMemo", "");

				result.put(ClsUtil.CLS1131_QUERY_SINGLE_C101M01A_1,
						C101M01AJson);
			}
		}
		// 本行利害關係人
		else if (ClsConstants.C101S01E.本行利害關係人.equals(type)) {
			// 加入查詢配偶 add by fantasy 2013/06/10

			boolean useNewSQL_C101S01L = true;
			for (Map<String, String> idMap : queryIdList) {
				String qCustId = Util.trim(idMap.get("custId"));
				String qDupNo = Util.trim(idMap.get("dupNo"));
				// String mName = Util.trim(idMap.get("mName"));

				if (useNewSQL_C101S01L) {
					List<Map<String, Object>> list = misElremainService
							.selForCLS(qCustId, qDupNo);
					for (Map<String, Object> map : list) {
						dataList.add(createC101S01L_v2(map, qCustId, qDupNo));
					}
					if (!CollectionUtils.isEmpty(list)
							&& Util.equals(LMSUtil.getCustKey_len10custId(
									qCustId, qDupNo), LMSUtil
									.getCustKey_len10custId(custId, dupNo))) {
						haveData_str = "T";
					}

					if (CollectionUtils.isEmpty(list)) {
						C101S01L model = not_found_C101S01L(
								ClsConstants.xType.銀行法, qCustId, qDupNo);
						dataList.add(model);
					}
				} else {
					// 本行利害關係人
					List<Map<String, Object>> mainLIst = misElremainService
							.findElremainByReidAndDupno(qCustId, qDupNo);
					for (Map<String, Object> map : mainLIst) {
						dataList.add(createC101S01L(map,
								ClsConstants.relvl.本行利害關係人, qCustId, qDupNo));
					}
					// 本行有利害關係人
					List<Map<String, Object>> ecdList = misElremainService
							.findElresecdByCmpid(qCustId);
					for (Map<String, Object> map : ecdList) {
						dataList.add(createC101S01L(map,
								ClsConstants.relvl.本行有利害關係人, qCustId, qDupNo));
					}
					// 本行有利害關係企業
					List<Map<String, Object>> comList = misElremainService
							.findElrescomByCmpid(qCustId);
					for (Map<String, Object> map : comList) {
						dataList.add(createC101S01L(map,
								ClsConstants.relvl.本行有利害關係企業, qCustId, qDupNo));
					}
					// set result
					if (mainLIst.size() > 0 || ecdList.size() > 0
							|| comList.size() > 0) {
						haveData_str = "T";
					} else {
						if (true) {// Util.isNotEmpty(mName)){
							C101S01L model = not_found_C101S01L(
									ClsConstants.xType.銀行法, qCustId, qDupNo);

							dataList.add(model);
						}
					}
				}
			}
		}
		// 金控利害關係人44,45條
		else if (ClsConstants.C101S01E.金控利害關係人44條.equals(type)
				|| ClsConstants.C101S01E.金控利害關係人45條.equals(type)) {
			// 加入查詢配偶 add by fantasy 2013/06/10
			for (Map<String, String> idMap : queryIdList) {
				String qCustId = Util.trim(idMap.get("custId"));
				String qDupNo = Util.trim(idMap.get("dupNo"));
				// String mName = Util.trim(idMap.get("mName"));

				List<Map<String, Object>> list_with_qCustId = misElremainService
						.findElrex45ByRelidAndLawno(
								qCustId,
								ClsConstants.C101S01E.金控利害關係人44條.equals(type) ? ClsConstants.lawno.金控法44條
										: ClsConstants.lawno.金控法45條);
				for (Map<String, Object> map : list_with_qCustId) {
					C101S01L model = new C101S01L();
					model.setXType(ClsConstants.C101S01E.金控利害關係人44條
							.equals(type) ? ClsConstants.xType.金控法44條
							: ClsConstants.xType.金控法45條);
					model.setXCustId(Util.trim(map.get("REL_ID")));
					model.setXDupNo("");
					model.setXCustName(Util.trim(map.get("C_NAME")));
					model.setRelvl(ClsConstants.relvl.金控利害關係人);
					model.setMagaMemo(Util.trim(map.get("MAGA_MEMO")));
					model.setQCustId(qCustId);
					model.setQDupNo(qDupNo);
					dataList.add(model);
				}
				if (!CollectionUtils.isEmpty(list_with_qCustId)
						&& Util.equals(
								LMSUtil.getCustKey_len10custId(qCustId, qDupNo),
								LMSUtil.getCustKey_len10custId(custId, dupNo))) {
					haveData_str = "T";
				}

				if (CollectionUtils.isEmpty(list_with_qCustId)) {
					// J-106-0117-001 增加金控法45條利害關係人比對範圍-實質關係人(授信以外交易)
					if (ClsConstants.C101S01E.金控利害關係人45條.equals(type)) {
						Map<String, Object> map = misBaseService
								.findRealRltByCustId(Util.trim(qCustId),
										Util.trim(qDupNo));
						if (map != null && !map.isEmpty()) {
							// 有-實質關係人(授信以外交易)
							Properties pop = MessageBundleScriptCreator
									.getComponentResource(LMSCommomPage.class);

							C101S01L model = new C101S01L();
							model.setXType(ClsConstants.C101S01E.金控利害關係人44條
									.equals(type) ? ClsConstants.xType.金控法44條
									: ClsConstants.xType.金控法45條);
							model.setXCustId(qCustId);
							model.setXDupNo("");
							model.setXCustName("");

							// 只有本人, 在C101S01D 未登錄配偶資料, 則再到 0024 查
							Map<String, Object> custId_0024 = misCustdataService
									.findByIdDupNo(Util.trim(qCustId),
											Util.trim(qDupNo));
							if (custId_0024 != null) {
								String cname_0024 = Util.trim(custId_0024
										.get("CNAME"));
								if (Util.isNotEmpty(cname_0024)) {
									model.setXCustName(cname_0024);
								}
							}

							model.setRelvl(ClsConstants.relvl.金控利害關係人);
							// l120s01d.effectiveRel=實質關係人(授信以外交易)
							model.setMagaMemo(Util.trim(pop
									.getProperty("l120s01d.effectiveRel")));
							model.setQCustId(qCustId);
							model.setQDupNo(qDupNo);
							dataList.add(model);
							if (Util.equals(LMSUtil.getCustKey_len10custId(
									qCustId, qDupNo), LMSUtil
									.getCustKey_len10custId(custId, dupNo))) {
								haveData_str = "T";
							}
						} else {
							// 無-實質關係人(授信以外交易)
							if (true) {// Util.isNotEmpty(mName)){
								C101S01L model = new C101S01L();
								model.setXType(ClsConstants.C101S01E.金控利害關係人44條
										.equals(type) ? ClsConstants.xType.金控法44條
										: ClsConstants.xType.金控法45條);
								model.setXCustId(qCustId);
								model.setXDupNo(qDupNo);
								model.setXCustName("");// mName);
								model.setRelvl("0");// 0.無
								model.setMagaMemo("");
								model.setQCustId(qCustId);
								model.setQDupNo(qDupNo);
								dataList.add(model);
							}
						}
					} else {
						if (true) {// Util.isNotEmpty(mName)){
							C101S01L model = new C101S01L();
							model.setXType(ClsConstants.C101S01E.金控利害關係人44條
									.equals(type) ? ClsConstants.xType.金控法44條
									: ClsConstants.xType.金控法45條);
							model.setXCustId(qCustId);
							model.setXDupNo(qDupNo);
							model.setXCustName("");// mName);
							model.setRelvl("0");// 0.無
							model.setMagaMemo("");
							model.setQCustId(qCustId);
							model.setQDupNo(qDupNo);
							dataList.add(model);
						}
					}

				}
			}
		}
		// 主從債務人_不含本次資料
		else if (ClsConstants.C101S01E.主從債務人_不含本次資料.equals(type)) {
			List<Map<String, Object>> list = misEllngteeService
					.findMisellngteeSelSell(custId, dupNo);
			if (true) {
				Set<String> nexus = new HashSet<String>();
				for (Map<String, Object> map : list) {
					String CUSTID = Util.trim(map.get("CUSTID"));
					String DUPNO = Util.trim(map.get("DUPNO"));
					String LNGEID = Util.trim(map.get("LNGEID"));
					String DUPNO1 = Util.trim(map.get("DUPNO1"));
					String LNGERE = Util.trim(map.get("LNGERE"));

					// 排除重覆 add by fantasy 2013/06/10
					// String key = LNGEID + DUPNO1 + LNGERE;
					/*
					 * 以隱碼後的id : A278271041 為例 其查詢出的結果如下，應該以 custId+dupNo當 key
					 * CUSTID DUPNO CNTRNO LNGEFLAG LNGEID DUPNO1 LNGENM LNGERE
					 * ---------- ----- ------------ -------- ---------- ------
					 * ------ ------ HKZ0009157 0 019409600050 G A278271041 0
					 * 賴○○　 1X 01509779 0 019109500039 G A278271041 0 賴○○　 1X
					 */
					String key = LMSUtil.getCustKey_len10custId(CUSTID, DUPNO);
					if (!nexus.contains(key)) {
						C101S01K model = new C101S01K();
						model.setCustId(Util.trim(map.get("CUSTID")));
						model.setDupNo(Util.trim(map.get("DUPNO")));
						model.setLnGeId(LNGEID);
						model.setLnGeDupNo(DUPNO1);
						model.setLnGeFlag(Util.trim(map.get("LNGEFLAG")));
						model.setLnGeRe(LNGERE);
						dataList.add(model);

						nexus.add(key);
						haveData_str = "T";
					}
				}
			}

			Date date = new Date();
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(date);
			calendar.add(Calendar.DATE, -90);
			String cancelDate = TWNDate.toAD(calendar.getTime()); // N203617976
			Set<String> idSet = misEllngteeService.findByLgneIdSet(custId,
					dupNo, cancelDate);
			for (String id : idSet) {
				List<Map<String, Object>> idList = misEllngteeService
						.findByCustId2(id, cancelDate);
				for (Map<String, Object> map : idList) {
					C101S01O c101s01o = new C101S01O();
					c101s01o.setMainId(mainId);
					c101s01o.setCustId(custId);
					c101s01o.setDupNo(dupNo);
					c101s01o.setQueryDate(date);
					c101s01o.setCntrNo(Util.trim(map.get("CNTRNO"))); // 額度序號
					c101s01o.setLoanNo(Util.trim(map.get("LOANNO")));// 放款帳號
					c101s01o.setCancelFlag(Util.isEmpty(map.get("CANCELDATE")) ? UtilConstants.DEFAULT.否
							: UtilConstants.DEFAULT.是);// 銷戶註記
					c101s01o.setCancelDate(Util.parseDate(map.get("CNTRNO")));// 銷戶日期
					c101s01o.setCustId1(Util.trim(map.get("CUSTID")));// 主債務人統編
					c101s01o.setDupNo1(Util.trim(map.get("DUPNO")));// 主債務人重覆序號
					c101s01o.setCustNm1(Util.trim(map.get("CNAME")));// 主債務人統編名稱
					c101s01o.setCustId2(Util.trim(map.get("LNGEID")));// 從債務人統編
					c101s01o.setDupNo2(Util.trim(map.get("DUPNO1")));// 從債務人重覆序號
					c101s01o.setCustNm2(Util.trim(map.get("LNGENM")));// 從債務人統編名稱
					c101s01o.setLngeFlag(Util.trim(map.get("LNGEFLAG")));// 相關身份
					c101s01o.setLngere(Util.trim(map.get("LNGERE")));// 與主債務人關係
					dataList.add(c101s01o);
				}
			}
		}

		// 對同一自然人授信總餘額比率
		else if (ClsConstants.C101S01E.對同一自然人授信總餘額比率.equals(type)) {
			List<Map<String, Object>> list = misBaseService
					.findLnf022LoanbalByCustid(Util.addSpaceWithValue(custId,
							10) + dupNo);
			for (Map<String, Object> map : list) {
				c101s01j.setTBal(Util.parseToBigDecimal(map
						.get("LOAN_BAL_TOTAL"))); // 總餘額
				c101s01j.setNBal(Util.parseToBigDecimal(map.get("LOAN_BAL_N"))); // 無擔保餘額
				// 判斷是否有資料 add by fantasy 2013/03/08
				// if (Util.isNotEmpty(map.get("LOAN_BAL_N"))
				// || Util.isNotEmpty(map.get("LOAN_BAL_S")))
				// haveData = true;
			}
			Map<String, Object> netValueMap = misBaseService.getBankNetValue();
			if (netValueMap != null) {
				c101s01j.setNetValue(Util.parseToBigDecimal(netValueMap
						.get("REPURE")));
				if (Util.parseLong(c101s01j.getNetValue().toString()) > 0) {
					// 有無超過本行淨值百分之三
					if (c101s01j.getTBal() != null) {
						BigDecimal bd = c101s01j.getTBal().divide(
								c101s01j.getNetValue(), 4,
								BigDecimal.ROUND_HALF_UP);
						if (Util.parseDouble(bd.toString()) > 0.03) {
							c101s01j.setRatio2(UtilConstants.DEFAULT.是);
							haveData_str = "T";
						} else {
							c101s01j.setRatio2(UtilConstants.DEFAULT.否);
						}
					}
					// 有無超過本行淨值百分之一
					if (c101s01j.getTBal() != null) {
						BigDecimal bd = c101s01j.getNBal().divide(
								c101s01j.getNetValue(), 4,
								BigDecimal.ROUND_HALF_UP);
						if (Util.parseDouble(bd.toString()) > 0.01) {
							c101s01j.setRatio1(UtilConstants.DEFAULT.是);
							haveData_str = "T";
						} else {
							c101s01j.setRatio1(UtilConstants.DEFAULT.否);
						}
					}
				}
			}
		}
		// 歸戶_本行餘額為aLoan資料_他行餘額為聯徵資料
		else if (ClsConstants.C101S01E.歸戶_本行餘額為aLoan資料_他行餘額為聯徵資料.equals(type)) {
			// 他行餘額

			if (Util.isNotEmpty(qDate)) {
				List<Map<String, Object>> list = ejcicService.getBAM087Data(
						custId, prodId, qDate);
				for (Map<String, Object> map : list) {
					C101S01N model = new C101S01N();
					// String BANK_CODE = Util.trim(map.get("BANK_CODE"));
					// model.setBankNo(BANK_CODE.length() >= 3 ? BANK_CODE
					// .substring(0, 3) : "");
					model.setBankNo(Util.trim(map.get("BC")));
					model.setLoanNo("");
					// model.setLoanTP(Util.trim(map.get("ACCOUNT_CODE")));
					model.setLoanTP(Util.trim(map.get("ACCTCODE")));
					// model.setQuotaPrv(Util.parseBigDecimal(
					// map.get("CONTRACT_AMT1")).multiply(
					// new BigDecimal("1000")));
					model.setQuotaPrv(Util.parseBigDecimal(map.get("TOT_CONT"))
							.multiply(new BigDecimal("1000")));
					model.setQuotaDup("");
					// String ACCOUNT_CODE2 =
					// Util.trim(map.get("ACCOUNT_CODE2"));
					String ACCOUNT_CODE2 = Util.trim(map.get("ACCTCODE2"));
					// BigDecimal LOAN_AMT = Util.parseBigDecimal(
					// map.get("LOAN_AMT")).multiply(
					// new BigDecimal("1000"));
					BigDecimal LOAN_AMT = Util.parseBigDecimal(
							map.get("TOT_LOAN")).multiply(
							new BigDecimal("1000"));
					if ("S".equals(ACCOUNT_CODE2)) {
						model.setBalS(LOAN_AMT);
					} else {
						model.setBalN(LOAN_AMT);
					}
					model.setLoanBal(LOAN_AMT);
					dataList.add(model);

					haveData_str = "T";
				}
			}

			// 本行餘額
			Set<String> quotanoSet = new HashSet<String>();
			List<Map<String, Object>> list = misLNF150Service
					.gfnDB2GetELLNF(custId);
			for (Map<String, Object> map : list) {
				C101S01N model = new C101S01N();
				// model.setBankNo(UtilConstants.兆豐銀行代碼);
				model.setBankNo(Util.trim(map.get("BRNO")));
				String LOANNO = Util.trim(map.get("LOANNO"));
				model.setLoanNo(LOANNO);
				model.setLoanTP(Util.trim(map.get("LOANTP")));
				model.setQuotaPrv(Util.parseBigDecimal(map.get("QUOTAPRV")));
				String QUOTANO = Util.trim(map.get("QUOTANO"));
				if (quotanoSet.contains(QUOTANO)) {
					model.setQuotaDup("*");
				} else {
					quotanoSet.add(QUOTANO);
					model.setQuotaDup("");
				}
				model.setLoanBal(Util.parseBigDecimal(map.get("LOANBAL")));
				// 判斷有擔,無擔 LOANNO第5位,雙數(有擔) 單數(無擔)
				if (LOANNO.length() >= 5) {
					int n = Util.parseInt(LOANNO.substring(4, 5)) % 2;
					if (n == 0) {
						model.setBalS(Util.parseBigDecimal(map.get("LOANBAL")));
					} else {
						model.setBalN(Util.parseBigDecimal(map.get("LOANBAL")));
					}
				}
				dataList.add(model);

				haveData_str = "T";
			}
		}
		// 近一年內不含查詢當日非Z類被聯行查詢紀錄明細
		else if (ClsConstants.C101S01E.近一年內不含查詢當日非Z類被聯行查詢紀錄明細.equals(type)
				&& naturalFlag) {
			if (Util.isNotEmpty(qDate)) {
				List<Map<String, Object>> list = ejcicService
						.getSTM022Data_isQdata14(custId, prodId, qDate);
				for (Map<String, Object> map : list) {
					C101S01M model = new C101S01M();
					model.setQDate(Util.parseDate(map.get("QUERY_DATE")));
					model.setBankNo(Util.trim(map.get("BANK_COD")));
					model.setBankName(Util.trim(map.get("BANK_NAME")));
					model.setMemo(Util.trim(map.get("ITEM_LIST")));
					model.setInqCd(Util.trim(map.get("INQ_PURPOSE_1")));
					model.setInq(Util.trim(map.get("INQ_PURPOSE")));
					dataList.add(model);

					haveData_str = "T";
				}
			}
		}
		// 黑名單
		else if (ClsConstants.C101S01E.黑名單.equals(type)) {

			String eName = Util.trim(c101s01j.getEName());
			eName = Util.truncateString(eName, MAXLEN_C101S01E_ENAME);

			String amlUnitNo = user.getUnitNo();
			if (clsService.active_SAS_AML(amlUnitNo)) {
				String amlRefNo = Util.trim(c101s01j.getAmlRefNo());
				String amlRefOid = Util.trim(c101s01j.getAmlRefOid());
				// 原本 haveData 的 default值= '否' , 當為 AML查詢改為 'NA'
				haveData_str = "NA";

				Date c101s01j_blackRecQry = null;
				String c101s01j_ans1 = "";

				L120S09B l120s09b = clsService.findL120S09B_refNo_or_oid(
						amlRefNo, amlRefOid);
				L120S09A l120s09a = clsService.findL120S09A_cls1131(l120s09b);

				/*
				 * 目前, lockEdit 只有針對 E00(掃描中) 在未 lockEdit 的狀況下, 應該也要允許再去查詢狀態
				 * 
				 * 可能第1次查到的結果是001(Hit) , 並未lockEdit user 可能會有以下行動 (1)檢視輸入的資料,
				 * 看是否因為 keyin錯誤, 導致Hit. 若這樣, 可能 user會在e-loan更改資料後, 再送查詢
				 * (2)user遇到001(Hit)後, 在AML輸入［調查結果, 可否承做］, 然後在e-loan按查詢去抓該 refNo
				 * 最新狀態
				 */
				String errMsg = "";
				try {
					errMsg = _proc_isQdata7_AMLStrategy(amlUnitNo, l120s09b,
							l120s09a);
				} catch (CapException ce) {
					errMsg = ce.getMessage();
				}
				if (Util.isEmpty(errMsg)) {

					// 由 l120s09a 將查詢結果，寫入至 c101s01j
					c101s01j_blackRecQry = l120s09a.getQueryDateS();
					c101s01j_ans1 = Util.trim(l120s09a.getBlackListCode());
				} else {
					logger.error("querySingleData_sas[mainId="
							+ c101s01j.getMainId() + ", refNo=" + amlRefNo
							+ "]" + errMsg);
				}

				if (true) {
					c101s01j.setBlackRecQry(c101s01j_blackRecQry);
					c101s01j.setAns1(c101s01j_ans1);
				}
				// =================================
				if (l120s09b == null) {
					haveData_str = "NA";
				} else {
					if (clsService.is_aml_lockEdit_cls1131(l120s09b)) {
						// 若L120S09B.ncResult=E00 => 出現 NA(避免讓人誤會, 已取得結果)
						haveData_str = "NA";
					} else {
						String ans1 = Util.trim(c101s01j.getAns1());
						if (Util.isEmpty(ans1)) {
							haveData_str = "NA";
						} else {
							if (Util.equals(
									UtilConstants.Casedoc.L120s09aBlackListCode.是黑名單,
									ans1)
									|| Util.equals(
											UtilConstants.Casedoc.L120s09aBlackListCode.可能是黑名單,
											ans1)) {
								haveData_str = "T";
							} else {
								haveData_str = "F";
							}
						}
					}
				}
			} else {
				// 若無 eName, 抓 0024 的 eName 做為預設值
				boolean use_0024_data = false;
				if (Util.isEmpty(eName)) {
					List<Map<String, Object>> list = misCustdataService
							.findCustDataCname(custId, dupNo);
					for (Map<String, Object> map : list) {
						eName = Util.trim(map.get("ENAME"));
						eName = Util.truncateString(eName,
								MAXLEN_C101S01E_ENAME);
						use_0024_data = true;
						c101s01j.setEName(eName); // 黑名單查詢英文名稱
						result.put(ClsConstants.C101S01E.黑名單全型字英文名, eName);
					}
				}

				if (true) {
					logger.trace("querySingleData_old_type[" + type + "]eName["
							+ eName + "]use_0024_data[" + use_0024_data + "]");
				}

				if (Util.isEmpty(eName)) {
					haveData_str = "F"; // 舊黑名單邏輯, 只傳 eName 去掃, 若前端未輸入 => 固定回傳
										// '無'
				} else {
					// 原本的 iCustomerService.findBlackList(...)=== beg
					List<String> blackResult = new ArrayList<String>();
					try {
						blackResult = iCustomerService.findBlackList(
								user.getUnitNo(), eName, "");
					} catch (CapException ex) {
						logger.error("iCustomerService.findBlackList_"
								+ StrUtils.getStackTrace(ex));
					}
					if (blackResult.size() > 0) {
						String blackListCode = blackResult
								.get(ICustomerService.BlackList_ReturnCode);
						c101s01j.setBlackRecQry(CapDate.getCurrentTimestamp()); // 黑名單查詢日期
						c101s01j.setAns1(blackListCode);
						if (Util.equals(
								UtilConstants.Casedoc.L120s09aBlackListCode.是黑名單,
								blackListCode)
								|| Util.equals(
										UtilConstants.Casedoc.L120s09aBlackListCode.可能是黑名單,
										blackListCode)) {
							haveData_str = "T";
						}
					}

				}
			} // end-if ,原本的 iCustomerService.findBlackList(...)=== end

		}
		// 證券暨期貨違約交割紀錄
		else if (ClsConstants.C101S01E.證券暨期貨違約交割紀錄.equals(type) && naturalFlag) {
			
			haveData_str = this.processC101s01eIsQdata8(mainId, custId, dupNo);
			
//			try {
//				c101s01j.setQryDefault(CapDate.getCurrentTimestamp()); // 違約交割查詢日期
//				JSONArray jsonArray = eaiService
//						.findCURIQ01ById(custId, mainId);
//				if (jsonArray != null) {
//					int size = jsonArray.size();
//					c101s01j.setDefaultRec(UtilConstants.DEFAULT.否); // 違約交割紀錄
//					for (int i = 0; i < size; i++) {
//						c101s01j.setDefaultMemo(null); // 違約交割說明事項
//
//						haveData_str = "T";
//					}
//				}
//			} catch (CapMessageException e) {
//				/*
//				 * 查 EAI 拋出的 Exception, 可能是正常的 查無資料 也可能是 Read timed out 不能全部用
//				 * ERROR 來處理 tw.com.iisi.cap.exception.CapMessageException: Read
//				 * timed out at
//				 * com.mega.eloan.lms.eai.service.impl.EAIServiceImpl
//				 * .findCURIQ01ById(EAIServiceImpl.java:67) at
//				 * com.mega.eloan.lms
//				 * .eai.service.impl.EAIServiceImpl$$FastClassByCGLIB$$11626ad8
//				 * .invoke(<generated>) at
//				 * net.sf.cglib.proxy.MethodProxy.invoke(MethodProxy.java:191)
//				 * 
//				 * tw.com.iisi.cap.exception.CapMessageException: EAI 1000:查無資料
//				 * at com.mega.eloan.lms.eai.service.impl.EAIServiceImpl.
//				 * findCURIQ01ById(EAIServiceImpl.java:67) at
//				 * com.mega.eloan.lms.
//				 * eai.service.impl.EAIServiceImpl$$FastClassByCGLIB$$11626ad8
//				 * .invoke(<generated>) at
//				 * net.sf.cglib.proxy.MethodProxy.invoke(MethodProxy.java:149)
//				 */
//				if (Util.equals("EAI 1000:查無資料", e.getMessage())) {
//					logger.trace("證券暨期貨違約交割紀錄[custId=" + custId + "][mainId="
//							+ mainId + "]" + e.getMessage());
//				} else {
//					logger.error("type_isQdata8_", e);
//				}
//			}
		}
		else if (ClsConstants.C101S01E.聯徵T70證券暨期貨違約交割記錄資訊.equals(type) && naturalFlag) {
			
			haveData_str = null;
			
			if("1.0".equals(c101s01e.getVersion())){
				
				haveData_str = this.processC101s01eIsQdata8(mainId, custId, dupNo);
			}
			
			if("2.0".equals(c101s01e.getVersion())){
				
				C101S02S c101s02s = this.getC101s02sByUniqueKey(mainId, custId, dupNo);
				if(c101s02s != null && "A3".equals(c101s02s.getDataStatus())){
					
					c101s01j.setQryDefaultT70(c101s02s.getQueryTime());
					
					String negFlag = c101s02s.getNegFlag();
					if(StringUtils.isNotBlank(negFlag)){
						
						if("N0".equals(negFlag) || "N1".equals(negFlag)){
							c101s01j.setDefaultRecT70(UtilConstants.DEFAULT.否);
							haveData_str = "F";
						}
						else{
							c101s01j.setDefaultRecT70(UtilConstants.DEFAULT.是);
							haveData_str = "T";
						}
					}
					
					Object negAmtObj = c101s02s.getNegAmt();
					if(negAmtObj != null){
						c101s01j.setUnsettledBal(LMSUtil.nullToZeroBigDecimal(negAmtObj));
					}
				}
			}
			
		}
		else if (ClsConstants.C101S01E.聯徵B42從債務查詢_擔保品類別.equals(type) && naturalFlag) {
			// J-113-0227 配合房貸核貸成數新增檢核邏輯，個金徵信新增[B42從債務查詢－擔保品類別]
			// 第一階段僅新增欄位供經辦填寫(不檢核必填)，並沒有從聯徵收回資料
			// 點[相關資料查詢]的時候都先給預設值[NA]
			haveData_str = "";//先預設NA
			c101s01j.setQryCollateralB42(UtilConstants.haveNo.NA);//c101s01j 也先預設NA
			
		}
		else if (ClsConstants.C101S01E.聯徵B42共同債務查詢_擔保品類別.equals(type) && naturalFlag) {
			// J-113-0227 配合房貸核貸成數新增檢核邏輯，個金徵信新增[B42從債務查詢－擔保品類別]
			// 第一階段僅新增欄位供經辦填寫(不檢核必填)，並沒有從聯徵收回資料
			// 點[相關資料查詢]的時候都先給預設值[NA]
			haveData_str = "";//先預設NA
			c101s01j.setQryMutualDebtB42(UtilConstants.haveNo.NA);//c101s01j 也先預設NA
		}
		else if (ClsConstants.C101S01E.是否有近一年有二戶以上授信借貸結案紀錄.equals(type) && naturalFlag) {
			// J-113-0227 配合房貸核貸成數新增檢核邏輯，個金徵信新增[B42從債務查詢－擔保品類別]
			// 第一階段僅新增欄位供經辦填寫(不檢核必填)，並沒有從聯徵收回資料
			// 點[相關資料查詢]的時候都先給預設值[NA]
			haveData_str = "";//先預設NA
			c101s01j.setOver2DataPastY(UtilConstants.haveNo.NA);//c101s01j 也先預設NA
		}else if (ClsConstants.C101S01E.是否有近三年有二戶以上授信借貸結案紀錄.equals(type) && naturalFlag) {
			// J-113-0227 配合房貸核貸成數新增檢核邏輯，個金徵信新增[B42從債務查詢－擔保品類別]
			// 第一階段僅新增欄位供經辦填寫(不檢核必填)，並沒有從聯徵收回資料
			// 點[相關資料查詢]的時候都先給預設值[NA]
			haveData_str = "";//先預設NA
			c101s01j.setOver2DataPast3y(UtilConstants.haveNo.NA);//c101s01j 也先預設NA
		}
		// 退票紀錄
		else if (ClsConstants.C101S01E.退票紀錄.equals(type) && naturalFlag) {
			List<Map<String, Object>> list = etchService.getMSG001Info1(custId);
			for (Map<String, Object> map : list) {
				String reject_date = Util.trim(map.get("reject_date"));
				// 2013/08/02,Rex,修改判斷最近一次退票日期為********也要呈現
				if (!"********".equals(reject_date)) {
					c101s01j.setRtD(Util.parseDate(reject_date)); // 最近一次退票日期
				}
				int totcnt = Util.parseInt(map.get("TOTCNT"));
				c101s01j.setRtNt(totcnt); // 退票未清償註記總張數
				c101s01j.setRtNm(Util.parseBigDecimal(map.get("TOTAMT"))); // 退票未清償註記總金額
				int porcnt = Util.parseInt(map.get("PORCNT"));
				c101s01j.setRtYt(porcnt); // 退票已清償註記總張數
				c101s01j.setRtYm(Util.parseBigDecimal(map.get("PORAMT"))); // 退票已清償註記總金額
				// 2013/08/05,Rex,修改退票記錄判斷，當有退票張數表示有退票記錄
				if (totcnt != 0 || porcnt != 0) {
					haveData_str = "T";
				}

			}
			if(list.size()==0){//勞工紓困案件, 可能有 B36+D10
				Map<String, String> dam001_dam003_map = ejcicService.get_DAM001_DAM003_relateData(custId);
				
				String eChkDDate = Util.trim(MapUtils.getString(dam001_dam003_map, "eChkDDate"));
				String eChkQDate = Util.trim(MapUtils.getString(dam001_dam003_map, "eChkQDate"));			
				
				if(Util.isNotEmpty(eChkDDate) && Util.isNotEmpty(eChkQDate)){
					
					String reject_date = Util.trim(MapUtils.getString(dam001_dam003_map, "reject_date"));
					if (!"********".equals(reject_date)) {
						c101s01j.setRtD(Util.parseDate(reject_date)); // 最近一次退票日期
					}
					int totcnt = Util.parseInt(MapUtils.getString(dam001_dam003_map, "TOTCNT"));
					c101s01j.setRtNt(totcnt); // 退票未清償註記總張數
					c101s01j.setRtNm(Util.parseBigDecimal(MapUtils.getString(dam001_dam003_map, "TOTAMT"))); // 退票未清償註記總金額
					int porcnt = Util.parseInt(MapUtils.getString(dam001_dam003_map, "PORCNT"));
					c101s01j.setRtYt(porcnt); // 退票已清償註記總張數
					c101s01j.setRtYm(Util.parseBigDecimal(MapUtils.getString(dam001_dam003_map, "PORAMT"))); // 退票已清償註記總金額
					// 2013/08/05,Rex,修改退票記錄判斷，當有退票張數表示有退票記錄
					if (totcnt != 0 || porcnt != 0) {
						haveData_str = "T";
					}
				}
			
			}
			
			// result.put(ClsConstants.C101S01E.有無票信退補記錄,
			// haveData ? UtilConstants.haveNo.有 : UtilConstants.haveNo.無);
		}
		// 拒絕往來紀錄
		else if (ClsConstants.C101S01E.拒絕往來紀錄.equals(type) && naturalFlag) {
			Map<String, Object> map = etchService.getMSG001Data(custId);
			if (map != null) {
				String reject_date = Util.trim(map.get("reject_date"));// 拒絕往來日期
				if (!"********".equals(reject_date)) {
					c101s01j.setRdD("********".equals(reject_date) ? null
							: Util.parseDate(reject_date));
					haveData_str = "T";
				}
			}else{
				if(true){//勞工紓困案件, 可能有 B36+D10
					Map<String, String> dam001_dam003_map = ejcicService.get_DAM001_DAM003_relateData(custId);
					
					String eChkDDate = Util.trim(MapUtils.getString(dam001_dam003_map, "eChkDDate"));
					String eChkQDate = Util.trim(MapUtils.getString(dam001_dam003_map, "eChkQDate"));			
					
					if(Util.isNotEmpty(eChkDDate) && Util.isNotEmpty(eChkQDate)){
						
						String reject_date = Util.trim(MapUtils.getString(dam001_dam003_map, "reject_date"));
						if (!"********".equals(reject_date)) {
							c101s01j.setRdD("********".equals(reject_date) ? null: Util.parseDate(reject_date));
							haveData_str = "T";
						}
					}
				}
			}
		}
		// 主債務逾期_催收_呆帳紀錄
		else if (ClsConstants.C101S01E.主債務逾期_催收_呆帳紀錄.equals(type)
				&& naturalFlag) {
			if (Util.isNotEmpty(qDate)) {
				Map<String, Object> map = ejcicService
						.getBAM087CollectionInfo1(custId, prodId, qDate);
				if (map != null) {
					c101s01j.setBaAgm(Util.parseBigDecimal(map.get("TOT_CONT"))); // 訂約金額
					c101s01j.setBaAvm(Util.parseBigDecimal(map.get("TOT_LOAN"))); // 授信餘額
					c101s01j.setBaOdm(Util.parseBigDecimal(map.get("TOT_PASS"))); // 逾期金額

					String DirOvNote = "(註)查借款人、連保人 無 從債務逾期金額。";
					if (Util.parseLong(c101s01j.getBaAgm()) > 0
							|| Util.parseLong(c101s01j.getBaAvm()) > 0
							|| Util.parseLong(c101s01j.getBaOdm()) > 0) {
						DirOvNote = "(註)查借款人、連保人 有 從債務逾期金額。";
						haveData_str = "T";
					}
					c101s01j.setDirOvNote(DirOvNote); // 逾催呆說明
				}
				String r = "";
				if (Util.equals(haveData_str, "T")) {
					r = UtilConstants.haveNo.有;
				} else if (Util.equals(haveData_str, "F")) {
					r = UtilConstants.haveNo.無;
				} else {
					r = UtilConstants.haveNo.NA;
				}
				// 在 table 右上角(聯徵資料日期,聯徵查詢日期 上方)
				result.put(ClsConstants.C101S01E.有無聯徵逾催呆記錄, r);
			}
		}
		// 信用卡強停紀錄
		else if (ClsConstants.C101S01E.信用卡強停紀錄.equals(type) && naturalFlag) {
			if (Util.isNotEmpty(qDate)) {
				Map<String, Object> map = ejcicService.getKRM001StopCreditCard(
						custId, prodId, qDate);
				if (map != null) {
					c101s01j.setCstDt(Util.parseDate(map.get("STOPDATE"))); // 最近一次強制停用日期

					haveData_str = "T";
				}
			}
		}
		// 身分證補換發紀錄
		else if (ClsConstants.C101S01E.身分證補換發紀錄.equals(type) && naturalFlag) {

		}
		// 成年監護制度查詢紀錄
		else if (ClsConstants.C101S01E.成年監護制度查詢紀錄.equals(type) && naturalFlag) {

			// 擔任負責人或董監事之企業是否於本行有授信額度達一億元以上
		} else if (ClsConstants.C101S01E.擔任負責人或董監事之企業是否於本行有授信額度達一億元以上
				.equals(type) && naturalFlag) {

			List<Map<String, Object>> list = misEJF305Service
					.getByCustId_LNF022_AVL_FAMT_T(custId,
							CapMath.getBigDecimal("1********"));
			for (Map<String, Object> map : list) {
				String comCustId = Util.trim(map.get("LNF022_CUST_ID"));
				String comName = Util.trim(map.get("CNAME"));
				BigDecimal fact_amt = Util.parseToBigDecimal(map
						.get("FACT_AMT"));
				C101S01P o = c101s01pDao.findByUniqueKey(mainId, custId, dupNo,
						comCustId);

				C101S01P model = null;
				if (Util.isNotEmpty(o) && Util.isNotEmpty(o.getOid())) {
					model = o;
				} else {
					model = new C101S01P();
				}
				model.setComCustId(comCustId);
				model.setComName(comName);
				model.setFact_amt(fact_amt);
				dataList.add(model);
			}
			if (list.size() > 0) {
				haveData_str = "T";
			}
		} else if (ClsConstants.C101S01E.疑似偽造證件或財力證明.equals(type)
				&& naturalFlag) {
			C900M01E c900m01e = c900m01eDao.findActiveByCustId(custId);
			if (c900m01e != null) {
				c101s01j.setRComId(c900m01e.getComId());
				c101s01j.setRComName(c900m01e.getComName());
				c101s01j.setRComTarget(c900m01e.getComTarget());
				c101s01j.setRSourceNo(c900m01e.getSourceNo());
				c101s01j.setRDataSrc(c900m01e.getDataSrc());
				// ---
				haveData_str = "T";
			} else {
				c101s01j.setRComId(null);
				c101s01j.setRComName(null);
				c101s01j.setRComTarget(null);
				c101s01j.setRSourceNo(null);
				c101s01j.setRDataSrc(null);
			}

		} else if (ClsConstants.C101S01E.授信信用風險管理_遵循檢核.equals(type)) {
			Properties prop = MessageBundleScriptCreator
					.getComponentResource(CLS1131S01Panel.class);
			Map<String, String> _my_build_param = new HashMap<String, String>();
			Map<String, Object> custMap = iCustomerService.findByIdDupNo(
					custId, dupNo);
			if (true) {
				_my_build_param.putAll(mMap);

				List<Map<String, Object>> listGrpMap = misGrpcmpService
						.findGrpcmpSelGrpdtl(custId, dupNo);
				Map<String, Object> grpMap = null;
				if (CollectionUtils.isNotEmpty(listGrpMap)) {
					grpMap = listGrpMap.get(0);
				}
				// 參考 L120S01B 的定義
				String groupNo = Util.trim(MapUtils.getString(grpMap, "GRPID"));
				String groupName = Util.trim(MapUtils
						.getString(grpMap, "GRPNM"));

				// 參考 L120S01D 的定義
				// 優先抓 resultMap 的資料, 若無再抓 C101S01E 的資料
				String mbRlt = Util.trim(MapUtils.getString(resultMap,
						ClsConstants.C101S01E.本行利害關係人));
				String mhRlt44 = Util.trim(MapUtils.getString(resultMap,
						ClsConstants.C101S01E.金控利害關係人44條));
				String mhRlt45 = Util.trim(MapUtils.getString(resultMap,
						ClsConstants.C101S01E.金控利害關係人45條));
				String busCode = Util
						.trim(MapUtils.getString(custMap, "BUSCD"));
				String custName = Util.trim(MapUtils
						.getString(custMap, "CNAME"));

				_my_build_param.put("groupNo", groupNo);
				_my_build_param.put("groupName", groupName);
				_my_build_param.put("mbRlt", mbRlt);
				_my_build_param.put("mhRlt44", mhRlt44);
				_my_build_param.put("mhRlt45", mhRlt45);
				_my_build_param.put("busCode", busCode);
				_my_build_param.put("custName", custName);

				List<String> errParamList = new ArrayList<String>();
				{
					HashSet<String> validSet = new HashSet<String>();

					validSet.add(UtilConstants.haveNo.有);// 1
					validSet.add(UtilConstants.haveNo.無);// 2
					validSet.add(UtilConstants.haveNo.NA);// 3

					if (!validSet.contains(mbRlt)) {
						errParamList.add(prop.getProperty("C101S01E.isQdata2")
								+ ":" + mbRlt);
					}
					if (!validSet.contains(mhRlt44)) {
						errParamList.add(prop.getProperty("C101S01E.isQdata3")
								+ ":" + mhRlt44);
					}
					if (!validSet.contains(mhRlt45)) {
						errParamList.add(prop.getProperty("C101S01E.isQdata16")
								+ ":" + mhRlt45);
					}

					Map<String, Object> data_0024 = misCustdataService
							.findByIdDupNo(custId, dupNo);
					if (data_0024 != null) {
						String mateid_0024 = Util.trim(data_0024.get("MATEID"));
						if (Util.isNotEmpty(mateid_0024)
								&& custId.equals(mateid_0024)) {
							errParamList.add(prop
									.getProperty("message.error0024MateId"));
						}
					}
				}
				if (CollectionUtils.isNotEmpty(errParamList)) {
					throw new CapMessageException(StringUtils.join(
							errParamList, "、"), getClass());
				}
			}
			if (MapUtils.isEmpty(custMap)) {
				if (clsService.is_function_on_codetype("cls1131_idDup_in_0024")) {
					throw new CapMessageException("於0024，查無" + custId + "-"
							+ dupNo, getClass());
				} else {
					// Testing環境裡的 隱碼後ID, 有存在於 EJCICDB 的ID, 可能不存在0024
				}
			}
			try {
				if (MapUtils.isNotEmpty(custMap)) {
					PageParameters builtParams = new CapMvcParameters();
					_my_build_param.forEach((key, value) -> {
						builtParams.setParameter(key, value);
					});
					lmsService.importL120s01m(mainId, builtParams);
				}
			} catch (CapException e) {
				logger.error("lmsService.importL120s01m_"
						+ StrUtils.getStackTrace(e));

				throw new CapMessageException(
						(prop.getProperty("l120s01m.item26") + " ERROR"),
						getClass());
			}
		} else if (ClsConstants.C101S01E.異常通報紀錄.equals(type)) {
			JSONObject C101M01AJson_29 = new JSONObject();
			C101M01AJson_29.put("abnormalReadDate",
					Util.trim(TWNDate.toAD(CapDate.getCurrentTimestamp())));

			Map<String, Object> map = findLnfe0854LatestStatus(custId, dupNo);
			if (MapUtils.isEmpty(map)) {
				C101M01AJson_29.put("abnormalBrNo", "");
				C101M01AJson_29.put("abnormalDate", "");
				C101M01AJson_29.put("abnormalStatus", "");
				C101M01AJson_29.put("abnormalMainId", "");
			} else {
				C101M01AJson_29.put("abnormalBrNo",
						Util.trim(MapUtils.getString(map, "LNFE0854_MDBRNO")));
				C101M01AJson_29.put("abnormalDate", Util.trim(TWNDate
						.toAD(CapDate.parseDate(MapUtils.getString(map,
								"LNFE0854_SDATE")))));
				C101M01AJson_29.put("abnormalStatus",
						Util.equals(
								MapUtils.getString(map, "LNFE0854_CLOSEFG"),
								"Y") ? UtilConstants.Casedoc.abnormalStatus.已解除
								: UtilConstants.Casedoc.abnormalStatus.未解除);
				C101M01AJson_29.put("abnormalMainId",
						Util.trim(MapUtils.getString(map, "LNFE0854_UNID")));
				// ===========
				haveData_str = "T";
			}

			/*
			 * 企業戶也可能有異常通報,且引入消金簽報書 ～～～～～～～～～～～～～～ 但既有的企業戶不列印
			 * 
			 * 一般消金簽報書：不允許 主借人 是企業戶 團貸母戶簽報書：主借人是企業戶，但本來就限制，授權等級不可為「分行授權內」
			 */
			result.put(ClsUtil.CLS1131_QUERY_SINGLE_C101M01A_29,
					C101M01AJson_29);
		} else if (ClsConstants.C101S01E.財管理財客戶等級.equals(type)) {
			String wm_qDate = TWNDate.toAD(CapDate.getCurrentTimestamp());
			String wm_flag = "";
			String wm_gra_cd = "";
			String wm_gra_name = "";

			if (true) {
				C900M03A c900m03a = clsService
						.findC900M03A_fn_maxGenDate("IMWM0017.D");
				if (c900m03a != null) {
					C900S03A c900s03a = clsService.findC900S03A_byData(
							c900m03a.getFn(), c900m03a.getGenDate(),
							c900m03a.getGenTime(), custId, dupNo);
					if (c900s03a != null) {
						wm_flag = "A";
					}
				}
			}
			if (Util.isNotEmpty(wm_flag)) {
				C900M03A c900m03a = clsService
						.findC900M03A_fn_maxGenDate("IDWM0002.D");
				if (c900m03a != null) {
					C900S03B c900s03b = clsService.findC900S03B_byData(
							c900m03a.getFn(), c900m03a.getGenDate(),
							c900m03a.getGenTime(), custId, dupNo);
					if (c900s03b != null) {
						wm_gra_cd = Util.trim(c900s03b.getGra_cd());
						wm_gra_name = Util.trim(c900s03b.getGra_name());
					}
				}
			}
			// J-107-0304 績優理財客戶
			result.put("wm_qDate", wm_qDate);
			result.put("wm_flag", wm_flag);
			result.put("wm_gra_cd", wm_gra_cd);
			result.put("wm_gra_name", wm_gra_name);
		} else if (ClsConstants.C101S01E.大數據風險報告.equals(type)) {
			String c101s01aForm = Util.trim(params.getString("C101S01AForm"));
			JSONObject s01aFormJSON = JSONObject.fromObject(c101s01aForm);
			String mTel = CapString.trimNull(s01aFormJSON.optString("mTel"));
			// J-112-0310 暫停大數據風險報告查詢
			String witchFinOpenFlag = sysparamService.getParamValue("WITCHER_FIN_OPEN");
			if ("Y".equals(witchFinOpenFlag)) {
				queryWitcherFin(mainId, custId, dupNo, mTel, result);
			}
		}

		// set 個金相關查詢結果檔
		if (ArrayUtils.indexOf(ClsUtil.typeNA, type) != -1 && !naturalFlag) {
			// 公司戶, 固定把某些欄位填入NA
			result.put(type, UtilConstants.haveNo.NA);
		} else if (ClsConstants.C101S01E.授信信用風險管理_遵循檢核.equals(type)) {
			// not set
		} else if (ClsConstants.C101S01E.財管理財客戶等級.equals(type)) {
			// not set
		} else if (ClsConstants.C101S01E.大數據風險報告.equals(type)) {
			// not set
		} else {
			String r = "";
			if (Util.equals(haveData_str, "T")) {
				r = UtilConstants.haveNo.有;
			} else if (Util.equals(haveData_str, "F")) {
				r = UtilConstants.haveNo.無;
			} else {
				r = UtilConstants.haveNo.NA;
			}
			result.put(type, r);
		}

		// === 在 C101S01E 及 C101S01J, 有相同的欄位 isQdata12,isQdata15
		// === 但在
		try {
			Field f = ClsConstants.C101S01J.class.getField(type);
			if (f != null) {
				String name = Util.trim(f.get(null));
				if (Util.isNotEmpty(name)) {
					String r = "";
					if (Util.equals(haveData_str, "T")) {
						r = UtilConstants.DEFAULT.是;
					} else if (Util.equals(haveData_str, "F")) {
						r = UtilConstants.DEFAULT.否;
					} else {
						r = UtilConstants.DEFAULT.否;// 暫先比照 False 的結果回傳
					}
					c101s01j.set(name, r);
					// TODO 依 type 將值寫入 C101S01J 的欄位
					// logger.trace("[type="+type+"][c101s01j][name="+name+"][r="+r+"]");
					// [type=isQdata2][c101s01j][name=Qresult][r=Y|N] -------
					// 本行利害關係人
					// [type=isQdata3][c101s01j][name=XQResult][r=Y|N] ------
					// 金控利害關係人(44條)
					// [type=isQdata16][c101s01j][name=X45QResult][r=Y|N] ---
					// 金控利害關係人(45條)
					// [type=isQdata8][c101s01j][name=defaultRec][r=Y|N] ----
					// 證券暨期貨違約交割紀錄
					// [type=isQdata13][c101s01j][name=cstRd][r=Y|N] --------
					// 信用卡強停紀錄
				}
			}
		} catch (NoSuchFieldException nex) {
			// 像 isQdata1, isQdata6, isQdata5, isQdata4, isQdata14
			// isQdata9, isQdata10, isQdata11, isQdata17, isQdata18
			// isQL120S01M, isQdata29
			// 就會在 Field f = ClsConstants.C101S01J.class.getField(type); 這一行
			// 丟出 NoSuchFieldException
			logger.trace("type_to_C101S01J_NoSuchFieldException[type=" + type
					+ "]" + nex.getMessage());
		} catch (CapException cex) {
			// [type=isQdata7]field:blackRecQry 引數類型不符-----------------黑名單,
			// blackRecQry={Date,查詢日期}
			logger.trace("type_to_C101S01J_CapException[type=" + type + "]"
					+ cex.getMessage());
		} catch (Exception e) {
			logger.error("type_to_C101S01J_" + StrUtils.getStackTrace(e));
		}

		return result;
	}

	private String _proc_isQdata7_AMLStrategy(String amlUnitNo,
			L120S09B l120s09b, L120S09A l120s09a) throws CapException {
		if (l120s09b == null) {
			return "尚未送掃描_l120s09b";
		}

		if (l120s09a == null) {
			return "l120s09a==null";
		}

		AmlStrategy as = amlRelateService.getAmlStrategy("");
		boolean isSend = as.isSent(Util.trim(l120s09b.getUniqueKey()));
		if (!isSend) {
			// AML.error019=本案相關掃描對象編號與掃描批號尚未送防制洗錢及打擊資恐系統系統掃描，請先執行【傳送名單掃描】按鈕後再試。
			// throw new
			// CapMessageException(prop_LMSS20APanel.getProperty("AML.error019"),
			// getClass());
			return "尚未送掃描_isSent";
		}
		ElAml elaml = as.checkResult(Util.trim(l120s09b.getUniqueKey()));
		if (elaml == null) {
			// AML.error019=本案相關掃描對象編號與掃描批號尚未送防制洗錢及打擊資恐系統系統掃描，請先執行【傳送名單掃描】按鈕後再試。
			// throw new
			// CapMessageException(prop_LMSS20APanel.getProperty("AML.error019"),
			// getClass());
			return "尚未送掃描_ElAml";
		}
		// boolean isSend = true; ElAml elaml = new ElAml();

		String ERRORCODE = Util.trim(elaml.getErrorCode());
		String ERRPRMSG = Util.trim(elaml.getErrprMsg());
		if (Util.notEquals(ERRORCODE, "") && Util.notEquals(ERRORCODE, "0")) {
			// throw new CapMessageException("ERROR!! " + ERRORCODE + "：" +
			// ERRPRMSG, getClass());
			return ("ERROR!! " + ERRORCODE + "：" + ERRPRMSG);
		}

		String caseNcResult = Util.trim(elaml.getNcresult());
		String ncCaseId = Util.trim(elaml.getNcCaseId());
		if (Util.equals(caseNcResult, "") && isSend) {
			caseNcResult = UtilConstants.SasNcResult.掃描中;
		}
		if (true) {
			boolean chgModel = false;
			// ~~~~~~~~~~~~~~~~~~
			if (Util.equals(l120s09b.getNcResult(), caseNcResult)) {
				// 相同
			} else {
				// 狀態可能由 001(Hit)→012(Pending)→002(Passed)
				l120s09b.setNcResult(caseNcResult);
				chgModel = true;
			}
			// ~~~~~~~~~~~~~~~~~~
			if (Util.equals(l120s09b.getNcCaseId(), ncCaseId)) {
				// 相同
			} else {
				l120s09b.setNcCaseId(ncCaseId);
				chgModel = true;
			}
			// ~~~~~~~~~~~~~~~~~~
			if (chgModel) {
				clsService.save(l120s09b);
			}
		}

		if (Util.equals(caseNcResult, "")) {
			// AML.error013=黑名單尚未開始掃描，稍後再執行本作業。
			// throw new
			// CapMessageException(prop_LMSS20APanel.getProperty("AML.error013"),
			// getClass());
			return "黑名單尚未開始掃描_caseNcResult";
		} else if (Util.equals(caseNcResult,
				UtilConstants.SasNcResult.SAS錯誤LEVEL_1)) {
			// AML.error014=黑名單掃描發生錯誤請通知資訊處，錯誤代碼:SAS錯誤LEVEL_1。
			// throw new
			// CapMessageException(prop_LMSS20APanel.getProperty("AML.error014"),
			// getClass());
			return "黑名單SAS錯誤LEVEL_1";
		} else if (Util.equals(caseNcResult,
				UtilConstants.SasNcResult.SAS錯誤LEVEL_2)) {
			// AML.error015=黑名單掃描發生錯誤請通知資訊處，錯誤代碼:SAS錯誤LEVEL_2。
			// throw new
			// CapMessageException(prop_LMSS20APanel.getProperty("AML.error015"),
			// getClass());
			return "黑名單SAS錯誤LEVEL_2";
		}
		Map<String, ElAmlItem> checkSeq_ElAmlItem_map = new HashMap<String, ElAmlItem>();
		for (ElAmlItem elAmlItem : amlService.getElAmlItems(Util.trim(l120s09b
				.getUniqueKey()))) {
			String checkSeq = Util.trim(elAmlItem.getCheckseq());
			checkSeq_ElAmlItem_map.put(checkSeq, elAmlItem);
		}

		if (true) {
			String checkSeq = Util.trim(l120s09a.getCheckSeq());
			ElAmlItem elamlItem = checkSeq_ElAmlItem_map.get(checkSeq);
			if (elamlItem == null) {
				return "l120s09a.checkSeq[" + checkSeq + "]無法對應到 ElAmlItem";
			}
			boolean isOverSea = clsService.aml_isOverSea(
					l120s09b.getCaseBrId(), amlUnitNo);
			clsService.sync_ElAmlItem_to_L120S09A(isOverSea, l120s09b,
					elamlItem, l120s09a);
		}
		return "";
	}

	@Override
	public List<GenericBean> getHtml(String mainId, String custId,
			String dupNo, String prodId) {
		List<GenericBean> result = new ArrayList<GenericBean>();
		// 取得聯徵HTML

		if (true) {
			// 在「查詢項目135」、「查詢項目128」、「查詢項目116」都有 AAS003
			String qDate = Util.trim(ejcicService
					.getAAS003QDate(custId, prodId));
			List<Map<String, Object>> list = ejcicService.getCPXQueryLogHtml(
					custId, prodId, qDate);
			for (Map<String, Object> map : list) {
				C101S01H model = new C101S01H();
				model.setMainId(Util.trim(mainId));
				model.setCustId(Util.trim(custId));
				model.setDupNo(Util.trim(dupNo));
				model.setBanid(Util.trim(map.get("BANID")));
				model.setId(Util.trim(map.get("ID")));
				model.setProdid(Util.trim(map.get("PRODID")));
				model.setTxid(Util.trim(map.get("TXID")));
				model.setHtmlData(Util.trim(map.get("HTMLDATA")));
				model.setQDate(Util.trim(map.get("QDATE")));
				model.setQEmpCode(Util.trim(map.get("QEMPCODE")));
				model.setQEmpName(Util.trim(map.get("QEMPNAME")));
				model.setQBranch(Util.trim(map.get("QBRANCH")));
				result.add(model);
			}
		}

		// 取得票信HTML F220170430
		if (true) {
			String qDate = null;
			String[] txids = { "4111", "4114" };
			for (String txid : txids) {
				if (qDate == null) {
					qDate = etchService.getMSG001QDate(custId, txid);
					if (qDate != null) {
						List<Map<String, Object>> list = etchService
								.getHRESULT(custId, txid, qDate);
						for (Map<String, Object> map : list) {
							C101S01I model = new C101S01I();
							model.setMainId(Util.trim(mainId));
							model.setCustId(Util.trim(custId));
							model.setDupNo(Util.trim(dupNo));
							model.setSeqId(Util.trim(map.get("SEQ_ID")));
							model.setQDate(Util.trim(map.get("QDATE")));
							model.setTxid(Util.trim(map.get("TXID")));
							model.setSndrRef(Util.trim(map.get("SNDR_REF")));
							model.setHtmlResult(Util.trim(map
									.get("HTML_RESULT")));
							result.add(model);
						}
					}
				}
			}
		}

		return result;
	}

	/**
	 * 建立個金相關查詢利害關係人檔
	 * 
	 * @param data
	 * @param relvl
	 * @return
	 */
	private C101S01L createC101S01L(Map<String, Object> data, String relvl,
			String qCustId, String qDupNo) {
		C101S01L model = new C101S01L();
		model.setXType(ClsConstants.xType.銀行法);
		model.setXCustId(Util.trim(data.get("REID")));
		model.setXDupNo(Util.trim(data.get("DUPNO")));
		model.setXCustName(Util.trim(data.get("REIDNM")));
		model.setRelvl(relvl);
		model.setRectl(Util.trim(data.get("RECTL")));
		model.setRelcd(Util.trim(data.get("RELCD")));
		model.setMagaMemo(Util.trim(data.get("REMEMO")));
		model.setQCustId(qCustId);
		model.setQDupNo(qDupNo);
		return model;
	}

	/**
	 * 之前分3個SQL去查, 後來改成在1個SQL查
	 */
	private C101S01L createC101S01L_v2(Map<String, Object> data,
			String qCustId, String qDupNo) {
		C101S01L model = new C101S01L();
		model.setXType(ClsConstants.xType.銀行法);
		model.setXCustId(Util.trim(data.get("XID")));
		model.setXDupNo(Util.trim(data.get("XDUPNO")));
		model.setXCustName(Util.trim(data.get("REIDNM")));
		model.setRelvl(Util.trim(data.get("RELVL")));
		model.setRectl(Util.trim(data.get("RECTL")));
		model.setRelcd(Util.trim(data.get("RELCD")));
		model.setMagaMemo(Util.trim(data.get("REMEMO")));
		model.setQCustId(qCustId);
		model.setQDupNo(qDupNo);
		return model;
	}

	private C101S01L not_found_C101S01L(String xType, String qCustId,
			String qDupNo) {
		C101S01L model = new C101S01L();
		model.setXType(xType);
		model.setXCustId(qCustId);
		model.setXDupNo(qDupNo);
		model.setXCustName("");// mName);
		model.setRelvl("0");// 0.無
		model.setMagaMemo("");
		model.setQCustId(qCustId);
		model.setQDupNo(qDupNo);
		return model;
	}

	@Override
	public void deleteL120s01mno(String mainId, String custId, String dupNo) {
		if (true) {// M
			L120S01M l120s01m = findL120s01m(mainId, custId, dupNo);
			if (Util.isNotEmpty(l120s01m)) {
				l120s01mDao.delete(l120s01m);
			}
		}
		if (true) {// N
			List<L120S01N> listS01n = l120s01nDao.findByCustId(mainId, custId,
					dupNo);
			if (CollectionUtils.isNotEmpty(listS01n)) {
				l120s01nDao.delete(listS01n);
			}
		}
		if (true) {// O
			List<L120S01O> listS01o = l120s01oDao.findByCustId(mainId, custId,
					dupNo);
			if (CollectionUtils.isNotEmpty(listS01o)) {
				l120s01oDao.delete(listS01o);
			}
		}
	}

	@Override
	public L120S01M findL120s01m(String mainId, String custId, String dupNo) {
		return l120s01mDao.findByIndex01(mainId, custId, dupNo);
	}

	@Override
	public List<L120S01N> findL120s01nByCustId(String mainId, String custId,
			String dupNo) {
		return l120s01nDao.findByCustId(mainId, custId, dupNo);
	}

	@Override
	public List<L120S01O> findL120s01oByCustIdRelType(String mainId,
			String custId, String dupNo, String relType) {
		return l120s01oDao.findByCustIdRelType(mainId, custId, dupNo, relType);
	}

	@Override
	public boolean unChg_RatingFactor_G(GenericBean model, JSONObject json,
			String version_G) {
		if (Util.equals(ClsScoreUtil.V1_3_HOUSE_LOAN, version_G)) {
			return _unChg_RatingFactor(model, json,
					ClsConstants.ratingFactor_G_V1_3.class.getClasses());
		} else if (Util.equals(ClsScoreUtil.V2_0_HOUSE_LOAN, version_G)
				|| Util.equals(ClsScoreUtil.V2_1_HOUSE_LOAN, version_G)) {
			return _unChg_RatingFactor(model, json,
					ClsConstants.ratingFactor_G_V2_0.class.getClasses());
		} else if (Util.equals(ClsScoreUtil.V3_0_HOUSE_LOAN, version_G)) {
			return _unChg_RatingFactor(model, json,
					ClsConstants.ratingFactor_G_V3_0.class.getClasses());
		} else {
			return false;
		}
	}

	@Override
	public boolean unChg_RatingFactor_Q(GenericBean model, JSONObject json,
			String version_NotHouseLoan) {
		if (Util.equals(ClsScoreUtil.V1_0_NOT_HOUSE_LOAN, version_NotHouseLoan)) {
			return _unChg_RatingFactor(model, json,
					ClsConstants.ratingFactor_Q_V1_0.class.getClasses());
		} else if (Util.equals(ClsScoreUtil.V2_0_NOT_HOUSE_LOAN,
				version_NotHouseLoan)) {
			return _unChg_RatingFactor(model, json,
					ClsConstants.ratingFactor_Q_V2_0.class.getClasses());
		} else if (Util.equals(ClsScoreUtil.V2_1_NOT_HOUSE_LOAN,
				version_NotHouseLoan)) {
			return _unChg_RatingFactor(model, json,
					ClsConstants.ratingFactor_Q_V2_1.class.getClasses());
		} else if (Util.equals(ClsScoreUtil.V3_0_NOT_HOUSE_LOAN,version_NotHouseLoan) 
				|| Util.equals(ClsScoreUtil.V3_1_NOT_HOUSE_LOAN,version_NotHouseLoan)) {
			return _unChg_RatingFactor(model, json,
					ClsScoreConstants.ratingFactor_Q_V3_0.class.getClasses());
		} else if (Util.equals(ClsScoreUtil.V4_0_NOT_HOUSE_LOAN,
				version_NotHouseLoan)) {
			return _unChg_RatingFactor(model, json,
					ClsScoreConstants.ratingFactor_Q_V4_0.class.getClasses());
		} else {
			return false;
		}

	}

	@Override
	public boolean unChg_RatingFactor_R(GenericBean model, JSONObject json,
			String version_R) {
		if (Util.equals(ClsScoreUtil.V2_1_CARD_LOAN, version_R)) {
			return _unChg_RatingFactor(model, json,
					ClsConstants.ratingFactor_R_V2_1.class.getClasses());
		} else if (Util.equals(ClsScoreUtil.V3_0_CARD_LOAN, version_R) 
				|| Util.equals(ClsScoreUtil.V3_1_CARD_LOAN, version_R)) {
			return _unChg_RatingFactor(model, json,
					ClsScoreConstants.ratingFactor_R_V3_0.class.getClasses());
		}  else if (Util.equals(ClsScoreUtil.V4_0_CARD_LOAN, version_R)) {
			return _unChg_RatingFactor(model, json,
					ClsScoreConstants.ratingFactor_R_V4_0.class.getClasses());
		}else {
			return false;
		}
	}

	/**
	 * 會依參數 model 的 getClass(), 從參數Class＜?＞[class1, class2, ...]取得match class
	 * 所包含的 DeclaredFields
	 * 
	 * @param model
	 * @param json
	 * @param classes
	 * @return
	 */
	private boolean _unChg_RatingFactor(GenericBean model, JSONObject json,
			Class<?>[] classes) {
		boolean result = true;
		Class<?> modelClass = DataParse.getEntityClass(model);
		if (modelClass != null) {
			String modelName = Util.trim(modelClass.getSimpleName());

			for (Class<?> clazz : classes) {
				if (modelName.equals(clazz.getSimpleName()) && result) {
					Field[] fields = clazz.getDeclaredFields();
					if (fields != null) {
						for (Field f : fields) {
							if (result) {
								try {
									String fName = Util.trim(f.get(null));

									String A = Util.trim(model.get(fName))
											.replaceAll(",", "")
											.replaceAll("null", "");
									String B = Util.trim(json.get(fName))
											.replaceAll(",", "")
											.replaceAll("null", "");
									// 2013/07/11,Rex,當為數字要轉成數值比對不然會有0.00和0的差別
									if (Util.isNumeric(A)) {
										result = Util
												.parseBigDecimal(A)
												.compareTo(
														Util.parseBigDecimal(B)) == 0;
									} else {
										result = A.equals(B);
									}

									if (!result) {
										logger.info(
												"modelName==>{},fName ==>{},A==>{},B==>{}",
												new Object[] { modelName,
														fName, A, B, });
									}
								} catch (Exception e) {

								}
							}
						}
					}
				}
			}
		}
		return result;
	}

	@Override
	public CapAjaxFormResult loadScore_G(C101S01G model_g) throws CapException {
		JSONObject j = DataParse.toJSON(model_g, C101S01G.class, ",##0.####",
				true, true);
		CapAjaxFormResult formResult = new CapAjaxFormResult(j);

		if (Util.equals(ClsScoreUtil.V1_3_HOUSE_LOAN, model_g.getVarVer())) {
			String[] displayNull = { "yFamAmt", "marry", "child", "edu",
					"jobType1", Score.column.D07聯徵查詢月份前一月或二個月之無擔保授信餘額,
					"chkAmt02", Score.column.N06十二個月新業務申請查詢總家數, "avgRate01",
					"avgRate02", Score.column.P25近六個月信用卡繳款狀況出現全額繳清無延遲次數,
					Score.column.P69近十二個月信用卡繳款狀況出現不良紀錄或使用循環信用次數,
					Score.column.P19近十二個月信用卡繳款狀況出現全額繳清無延遲次數 };
			_empty_toStr_Null(formResult, displayNull);
			// 婚姻狀況
			CodeType marry = codeTypeService.findByCodeTypeAndCodeValue(
					"marry", Util.trim(model_g.getMarry()));
			if (marry != null)
				formResult.set("marryDisplay", marry.getCodeDesc());
			// 教育程度
			CodeType edu = codeTypeService.findByCodeTypeAndCodeValue(
					"cls1131m01_edu", Util.trim(model_g.getEdu()));
			if (edu != null)
				formResult.set("eduDisplay", edu.getCodeDesc());
			// 職業大類
			CodeType jobTitle = codeTypeService.findByCodeTypeAndCodeValue(
					"jobType", Util.trim(model_g.getJobType1()));
			if (jobTitle != null) {
				formResult.set("jobType1Display", jobTitle.getCodeDesc());
			}
		} else if (Util.equals(ClsScoreUtil.V2_0_HOUSE_LOAN,model_g.getVarVer()) 
				|| Util.equals(ClsScoreUtil.V2_1_HOUSE_LOAN,model_g.getVarVer())) {
			String[] displayNull = { Score.column.夫妻年收入, Score.column.年資,
					Score.column.D07_DIV_PINCOME因子,
					Score.column.D07聯徵查詢月份前一月或二個月之無擔保授信餘額, Score.column.個人年所得,
					Score.column.N18次數因子, Score.column.職稱,
					Score.column.P25近六個月信用卡繳款狀況出現全額繳清無延遲次數,
					Score.column.P68近六個月信用卡繳款狀況出現不良紀錄或使用循環信用次數,
					Score.column.P19近十二個月信用卡繳款狀況出現全額繳清無延遲次數, Score.column.Z03因子 };
			_empty_toStr_Null(formResult, displayNull);
		} else if (Util.equals(ClsScoreUtil.V3_0_HOUSE_LOAN,
				model_g.getVarVer())) {
			String[] displayNull = { Score.column.v3_N18因子, Score.column.v3_職稱因子,
					Score.column.v3_D42因子, Score.column.v3_Edu因子,
					Score.column.v3_P68因子, Score.column.v3_yRate因子,
					Score.column.v3_N01因子, Score.column.v3_P19因子,
					Score.column.v3_R01因子};
			_empty_toStr_Null(formResult, displayNull);
		}
		return formResult;
	}

	@Override
	public CapAjaxFormResult loadScore_Q(C101S01Q model_q) throws CapException {
		JSONObject j = DataParse.toJSON(model_q, C101S01Q.class, ",##0.####",
				true, true);
		CapAjaxFormResult formResult = new CapAjaxFormResult(j);
		if (Util.equals(ClsScoreUtil.V1_0_NOT_HOUSE_LOAN, model_q.getVarVer())) {
			// C101S01Q中,的評分變量
			String[] displayNull = { "yPay", "seniority", "education",
					"nochkItem01", "nochkItem02", "nochkItem03", "nochkItem04" };
			_empty_toStr_Null(formResult, displayNull);
			// 教育程度
			CodeType edu = codeTypeService.findByCodeTypeAndCodeValue(
					"cls1131m01_edu", Util.trim(model_q.getEducation()));
			if (edu != null) {
				formResult.set("eduDisplay", edu.getCodeDesc());
			}
		} else if (Util.equals(ClsScoreUtil.V2_0_NOT_HOUSE_LOAN,
				model_q.getVarVer())
				|| Util.equals(ClsScoreUtil.V2_1_NOT_HOUSE_LOAN,
						model_q.getVarVer())) {
			// C101S01Q中,的評分變量
			String[] displayNull = { ScoreNotHouseLoan.column.個人年所得,
					ScoreNotHouseLoan.column.年資,
					ScoreNotHouseLoan.column.聯徵查詢月份當時無擔保授信往來家數,
					ScoreNotHouseLoan.column.近6個月信用卡使用循環信用家數,
					ScoreNotHouseLoan.column.D07因子,
					ScoreNotHouseLoan.column.N06因子,
					ScoreNotHouseLoan.column.P68因子,
					ScoreNotHouseLoan.column.P19因子 };
			_empty_toStr_Null(formResult, displayNull);
		} else if (Util.equals(ClsScoreUtil.V3_0_NOT_HOUSE_LOAN,model_q.getVarVer()) 
				|| Util.equals(ClsScoreUtil.V3_1_NOT_HOUSE_LOAN,model_q.getVarVer())) {
			// C101S01Q中,的評分變量(3.0跟3.1是一樣的)
			String[] displayNull = ClsScoreUtil.get_input_factor("Q",
					ClsScoreUtil.V3_0_NOT_HOUSE_LOAN);
			_empty_toStr_Null(formResult, displayNull);
		} else if (Util.equals(ClsScoreUtil.V4_0_NOT_HOUSE_LOAN,
				model_q.getVarVer())) {
			String[] displayNull = { ScoreNotHouseLoan.column.nv4_學歷因子,
					ScoreNotHouseLoan.column.nv4_P01因子,
					ScoreNotHouseLoan.column.nv4_loanBalSByid因子,
					ScoreNotHouseLoan.column.nv4_dRate因子,
					ScoreNotHouseLoan.column.nv4_MaxR01因子};
			_empty_toStr_Null(formResult, displayNull);
		}
		return formResult;
	}

	/**
	 * 中鋼整批的因子, 只存在C120S01Q
	 */
	public CapAjaxFormResult loadScore_Q(C120S01Q model_q) throws CapException {
		JSONObject j = DataParse.toJSON(model_q, C120S01Q.class, ",##0.####",
				true, true);
		CapAjaxFormResult formResult = new CapAjaxFormResult(j);
		if (Util.equals(ClsScoreUtil.V1_0_NOT_HOUSE_LOAN, model_q.getVarVer())) {
			// C120S01Q中,的評分變量
			String[] displayNull = { "yPay", "seniority", "education",
					"nochkItem01", "nochkItem02", "nochkItem03", "nochkItem04" };
			_empty_toStr_Null(formResult, displayNull);
			// 教育程度
			CodeType edu = codeTypeService.findByCodeTypeAndCodeValue(
					"cls1131m01_edu", Util.trim(model_q.getEducation()));
			if (edu != null) {
				formResult.set("eduDisplay", edu.getCodeDesc());
			}
		} else if (Util.equals(ClsScoreUtil.V2_0_NOT_HOUSE_LOAN,
				model_q.getVarVer())
				|| Util.equals(ClsScoreUtil.V2_1_NOT_HOUSE_LOAN,
						model_q.getVarVer())) {
			// C101S01Q中,的評分變量
			String[] displayNull = { ScoreNotHouseLoan.column.個人年所得,
					ScoreNotHouseLoan.column.年資,
					ScoreNotHouseLoan.column.聯徵查詢月份當時無擔保授信往來家數,
					ScoreNotHouseLoan.column.近6個月信用卡使用循環信用家數,
					ScoreNotHouseLoan.column.D07因子,
					ScoreNotHouseLoan.column.N06因子,
					ScoreNotHouseLoan.column.P68因子,
					ScoreNotHouseLoan.column.P19因子 };
			_empty_toStr_Null(formResult, displayNull);
		} else if (Util.equals(ClsScoreUtil.V3_0_NOT_HOUSE_LOAN,model_q.getVarVer()) 
				|| Util.equals(ClsScoreUtil.V3_1_NOT_HOUSE_LOAN,model_q.getVarVer())) {
			// C101S01Q中,的評分變量
			String[] displayNull = ClsScoreUtil.get_input_factor("Q",
					ClsScoreUtil.V3_0_NOT_HOUSE_LOAN);
			_empty_toStr_Null(formResult, displayNull);
		}
		return formResult;
	}

	@Override
	public CapAjaxFormResult loadScore_R(C101S01R model_r) throws CapException {
		JSONObject j = DataParse.toJSON(model_r, C101S01R.class, ",##0.####",
				true, true);
		CapAjaxFormResult formResult = new CapAjaxFormResult(j);
		if (Util.equals(ClsScoreUtil.V2_1_CARD_LOAN, model_r.getVarVer())) {
			// C101S01R中,的評分變量
			String[] displayNull = { ScoreCardLoan.column.個人年所得,
					ScoreCardLoan.column.年資,
					ScoreCardLoan.column.聯徵查詢月份當時無擔保授信往來家數,
					ScoreCardLoan.column.近6個月信用卡使用循環信用家數,
					ScoreCardLoan.column.D07因子, ScoreCardLoan.column.N06因子,
					ScoreCardLoan.column.P68因子, ScoreCardLoan.column.P19因子 };
			_empty_toStr_Null(formResult, displayNull);
		} else if (Util.equals(ClsScoreUtil.V3_0_CARD_LOAN, model_r.getVarVer()) 
				|| Util.equals(ClsScoreUtil.V3_1_CARD_LOAN, model_r.getVarVer())) {
			String[] displayNull = ClsScoreUtil.get_input_factor("R",
					ClsScoreUtil.V3_0_CARD_LOAN);
			_empty_toStr_Null(formResult, displayNull);
		} else if (Util.equals(ClsScoreUtil.V4_0_CARD_LOAN,
				model_r.getVarVer())) {
			String[] displayNull = { ScoreCardLoan.column.nv4_學歷因子,
					ScoreCardLoan.column.nv4_P01因子,
					ScoreCardLoan.column.nv4_loanBalSByid因子,
					ScoreCardLoan.column.nv4_dRate因子,
					ScoreCardLoan.column.nv4_MaxR01因子};
			_empty_toStr_Null(formResult, displayNull);
		}
		return formResult;
	}

	//-----雙軌模型處理-----
	@Override
	public CapAjaxFormResult loadScore_GN(C101S01G_N model_gn) throws CapException {
		JSONObject j = DataParse.toJSON(model_gn, C101S01G_N.class, ",##0.####",
				true, true);
		//房貸雙軌固定跑3.0
		CapAjaxFormResult formResult = new CapAjaxFormResult(j);

		if (Util.equals(ClsScoreUtil.V3_0_HOUSE_LOAN,model_gn.getVarVer())) {
			String[] displayNull = { Score.column.v3_N18因子, Score.column.v3_職稱因子,
					Score.column.v3_D42因子, Score.column.v3_Edu因子,
					Score.column.v3_P68因子, Score.column.v3_yRate因子,
					Score.column.v3_N01因子, Score.column.v3_P19因子,
					Score.column.v3_R01因子};
			_empty_toStr_Null(formResult, displayNull);
		}
		return formResult;
	}
	
	public CapAjaxFormResult loadScore_QN(C101S01Q_N model_qn) throws CapException {
		JSONObject j = DataParse.toJSON(model_qn, C101S01Q_N.class, ",##0.####",
				true, true);
		//非房貸雙軌固定跑4.0
		CapAjaxFormResult formResult = new CapAjaxFormResult(j);
		if (Util.equals(ClsScoreUtil.V4_0_NOT_HOUSE_LOAN,
				model_qn.getVarVer())) {
			String[] displayNull = { ScoreNotHouseLoan.column.nv4_學歷因子,
					ScoreNotHouseLoan.column.nv4_P01因子,
					ScoreNotHouseLoan.column.nv4_loanBalSByid因子,
					ScoreNotHouseLoan.column.nv4_dRate因子,
					ScoreNotHouseLoan.column.nv4_MaxR01因子};
			_empty_toStr_Null(formResult, displayNull);
		}
		
		return formResult;
	}
	
	public CapAjaxFormResult loadScore_RN(C101S01R_N model_rn) throws CapException {
		JSONObject j = DataParse.toJSON(model_rn, C101S01R_N.class, ",##0.####",
				true, true);
		//專案信貸(非團體)雙軌固定跑4.0
		CapAjaxFormResult formResult = new CapAjaxFormResult(j);
		if (Util.equals(ClsScoreUtil.V4_0_CARD_LOAN,
				model_rn.getVarVer())) {
			String[] displayNull = { ScoreCardLoan.column.nv4_學歷因子,
					ScoreCardLoan.column.nv4_P01因子,
					ScoreCardLoan.column.nv4_loanBalSByid因子,
					ScoreCardLoan.column.nv4_dRate因子,
					ScoreCardLoan.column.nv4_MaxR01因子};
			_empty_toStr_Null(formResult, displayNull);
		}
		return formResult;
	}
	//-----雙軌模型處理-----
	
	/**
	 * 在 DataParse.toJSON(...)即使allowNull=true, 也只是傳回 {key:''}, 此功能傳回{key:Null}
	 * 
	 * @param formResult
	 * @param displayNull
	 */
	private void _empty_toStr_Null(CapAjaxFormResult formResult,
			String[] displayNull) {
		for (String key : displayNull) {
			if (Util.isEmpty(formResult.get(key)))
				formResult.set(key, "Null");
		}
	}

	@Override
	public void c122m01aStatusTo0B0(String ownBrId, String[] applyKind_arr,
			String custId, String dupNo, String empNo) {
		for (C122M01A c122m01a : c122m01aDao.findInProgressLastC122M01A(ownBrId,
				applyKind_arr, custId, dupNo)) {
			/*
			 * 1個人同時有3筆「受理中」的申貸 在引入後，應該3筆都變成審核中
			 * 2021-11-1 調整為最新一份申請書才將狀態調整為審核中
			 */
			if (Util.equals(c122m01a.getApplyStatus(),
					UtilConstants.C122_ApplyStatus.受理中)
					&& Util.equals("", c122m01a.getDocStatus())) {
				c122m01a.setApplyStatus(UtilConstants.C122_ApplyStatus.審核中);
				
				if(Util.equals(UtilConstants.C122_ApplyKind.P, c122m01a.getApplyKind())
						|| Util.equals(UtilConstants.C122_ApplyKind.Q, c122m01a.getApplyKind())
						|| Util.equals(UtilConstants.C122_ApplyKind.E, c122m01a.getApplyKind())
						|| Util.equals(UtilConstants.C122_ApplyKind.F, c122m01a.getApplyKind())){
					c122m01a.setStatFlag("1");
				}
				
				if (Util.isEmpty(Util.trim(c122m01a.getContactEmpNo()))) {
					c122m01a.setContactEmpNo(empNo);
				}
				// ---------
				cls1220Service.save(c122m01a);
			}
		}
	}

	@Override
	public String[] parseAddr(String fullStr) {
		String unparseStr = fullStr;
		String part1 = "";
		String part2 = "";
		String part3 = unparseStr;
		try {
			if (true) {
				Map<String, String> part1DescCodeMap = new HashMap<String, String>();
				Map<String, String> countiesMap = retrialService
						.get_common_codeTypeMap("counties");
				for (String countiesKey : countiesMap.keySet()) {
					String desc = countiesMap.get(countiesKey);
					part1DescCodeMap.put(desc, countiesKey);
					if (desc.indexOf("台") >= 0) {
						part1DescCodeMap.put(desc.replaceAll("台", "臺"),
								countiesKey);
					}
				}
				for (String desc : part1DescCodeMap.keySet()) {
					if (unparseStr.indexOf(desc) >= 0) {
						part1 = part1DescCodeMap.get(desc);
						unparseStr = StringUtils.substring(unparseStr,
								unparseStr.indexOf(desc) + desc.length());
						part3 = unparseStr;
						break;
					}
				}
			}

			if (Util.isNotEmpty(part1)) {
				Map<String, String> part2DescCodeMap = new HashMap<String, String>();
				Map<String, String> countiesMap = retrialService
						.get_common_codeTypeMap("counties" + part1);
				for (String countiesKey : countiesMap.keySet()) {
					String desc = countiesMap.get(countiesKey);
					part2DescCodeMap.put(desc, countiesKey);
					if (desc.indexOf("台") >= 0) {
						part2DescCodeMap.put(desc.replaceAll("台", "臺"),
								countiesKey);
					}
				}

				for (String desc : part2DescCodeMap.keySet()) {
					if (unparseStr.startsWith(desc)) {
						part2 = part2DescCodeMap.get(desc);
						part3 = StringUtils
								.substring(unparseStr, desc.length());
						break;
					}
				}
			}

		} catch (Exception e) {
			logger.error("parseAddr_" + StrUtils.getStackTrace(e));
		}
		return new String[] { part1, part2, part3 };
	}

	@Override
	public void injectAbnormalDocParam(CapAjaxFormResult result,
			L120M01A l120m01a, String injectKey) {
		CapAjaxFormResult form = new CapAjaxFormResult();
		if (true) {
			try {
				form = DataParse.toResult(l120m01a);
			} catch (CapException e) {
				logger.error("[injectAbnormalDocParam]"
						+ StrUtils.getStackTrace(e));
			}
		}
		form.set("mainOid", l120m01a.getOid());
		form.set("mainDocStatus", l120m01a.getDocStatus());
		// ~~~~~~
		result.set(injectKey, form);
	}

	/**
	 * 之前授管曾說要列出三年內的資料, 但可能很多 改成只抓最後一筆
	 */
	private Map<String, Object> findLnfe0854LatestStatus(String custId,
			String dupNo) {
		List<Map<String, Object>> list = misBaseService
				.findLnfe0854LastStatusByCustId(custId, dupNo);
		if (list != null && list.size() > 0) {
			return list.get(0);
		}
		return null;
	}

	public Map<String, Object> getCreditAnomalyData(String custId,
			String dupNo, String custName) throws CapFormatException {

		Map<String, Object> returnMap = new HashMap<String, Object>();

		List<Map<String, Object>> gridview1 = new ArrayList<Map<String, Object>>();// 本行往來戶負面信用資料查詢(發查聯徵負面信用資訊紀錄)
		List<Map<String, Object>> gridview2 = new ArrayList<Map<String, Object>>();// 拒往戶資料查詢(2007年1月以後票交所拒往紀錄)
		List<Map<String, Object>> gridview3 = new ArrayList<Map<String, Object>>();// 本行往來戶退票戶資料查詢(票交所近7年退票資料)

		// 查詢聯徵虛擬統編
		String ejcicVirtualCustId = misElcus25Service
				.getEjcicId_RpsVersion(custId);
		ejcicVirtualCustId = CapString.fillBlankTail(ejcicVirtualCustId.equals(custId) ? "" : ejcicVirtualCustId, 10);// 10位左靠補空白

		// J-104-0031 增加引進101年4月底前的票據退票及拒往資訊。
		Map<String, Object> refundAndRejectedMap = misStoredProcService
				.getRefundAndRejectedInfo(custId, dupNo, ejcicVirtualCustId);
		String dataStatus = this
				.processRefundAndRejectedAccountRecord(custId, custName,
						refundAndRejectedMap, returnMap, gridview2, gridview3);

		returnMap.put("gridview2", gridview2);
		returnMap.put("gridview3", gridview3);

		// 取得聯徵負面信用資訊紀錄
		List<Map<String, Object>> negativeCreditList = lnLnBadCrService
				.getNegativeCreditInfo(custId);
		
		String[] custIdArray = new String[]{custId};
		String[] dupNoArray = new String[]{dupNo};
		// 取得本行逾催呆資料
        List<Map<String, Object>> loanLossList = lnLnBadCrService.getOverdueAndBadDebtInfo(Arrays.asList(custIdArray), Arrays.asList(dupNoArray));
		
		dataStatus = this.processEjcicNegativeCreditRecord(negativeCreditList, loanLossList,
						custId, custName, gridview1) + dataStatus;
		
		returnMap.put("gridview1", gridview1);
		returnMap.put("dataStatus", dataStatus);

		return returnMap;
	}

	private String processRefundAndRejectedAccountRecord(String custId,
			String custName, Map<String, Object> refundAndRejectedMap,
			Map<String, Object> returnMap, List<Map<String, Object>> gridview2,
			List<Map<String, Object>> gridview3) throws CapFormatException {

		Map<String, Object> grid2Map = new HashMap<String, Object>();
		Map<String, Object> grid3Map = new HashMap<String, Object>();
		grid2Map.put("CUSTID", custId);
		grid2Map.put("CUST_NAME", custName);
		grid3Map.put("CUSTID", custId);
		grid3Map.put("CUST_NAME", custName);

		String grid2BadCreditRecord = "0";
		String grid3BadCreditRecord = "0";

		if (null == refundAndRejectedMap.get("ERROR")) {

			NumericFormatter nf = new NumericFormatter();

			if ("N".equals(refundAndRejectedMap.get("DNGER-YN"))) {
				grid2Map.put("DATA_FROM", "無拒往紀錄");// 資料來源出處
				grid2Map.put("FROM_FLAG", CapConstants.EMPTY_STRING);// 外部
				grid2Map.put("DNGERDATE", CapConstants.EMPTY_STRING);// 拒往發生日
				grid2Map.put("DNGER_D_DATE", CapConstants.EMPTY_STRING);// 撤銷拒往日
			} else {
				grid2Map.put("DATA_FROM", "票據交換所 ");// 資料來源出處
				grid2Map.put("FROM_FLAG", "外部");// 外部
				grid2Map.put("DNGERDATE", this.dateFormat(String
						.valueOf(refundAndRejectedMap.get("DNGER-DATE"))));// 拒往發生日
				grid2Map.put("DNGER_D_DATE", this.dateFormat(String
						.valueOf(refundAndRejectedMap.get("DNGER-D-DATE"))));// 撤銷拒往日
				grid2BadCreditRecord = "1";
			}

			if ("N".equals(refundAndRejectedMap.get("REJ-YN"))) {
				// ELRPS04Q12.006=無退票資料
				grid3Map.put("DATA_FROM", "無退票資料");
				grid3Map.put("FROM_FLAG", CapConstants.EMPTY_STRING);
				grid3Map.put("REJ_DATE", CapConstants.EMPTY_STRING);
				grid3Map.put("REJ_CNT", CapConstants.EMPTY_STRING);
				grid3Map.put("REJ_SWFT", CapConstants.EMPTY_STRING);
				grid3Map.put("REJ_AMT", CapConstants.EMPTY_STRING);
			} else {
				grid3Map.put("DATA_FROM", "票據交換所 ");// 資料來源出處
				grid3Map.put("FROM_FLAG", "外部");// ELRPS04Q12.002=外部
				grid3Map.put("REJ_DATE", this.dateFormat(String
						.valueOf(refundAndRejectedMap.get("REJ-DATE"))));// 最近退票日期
				grid3Map.put("REJ_CNT", nf.reformat(CapMath
						.getBigDecimal(String.valueOf(refundAndRejectedMap
								.get("REJ-CNT")))));// 總退票張數
				grid3Map.put("REJ_SWFT", refundAndRejectedMap.get("REJ-SWFT"));// 退票幣別
				grid3Map.put("REJ_AMT",
						nf.reformat(refundAndRejectedMap.get("REJ-AMT")));// 總退票金額
				grid3BadCreditRecord = "1";
			}
		}

		// 發生錯誤
		if (null != refundAndRejectedMap.get("ERROR")) {
			grid2Map.put("DATA_FROM", refundAndRejectedMap.get("ERROR"));
			grid2Map.put("FROM_FLAG", CapConstants.EMPTY_STRING);
			grid2Map.put("DNGERDATE", CapConstants.EMPTY_STRING);
			grid2Map.put("DNGER_D_DATE", CapConstants.EMPTY_STRING);

			grid3Map.put("DATA_FROM", refundAndRejectedMap.get("ERROR"));
			grid3Map.put("FROM_FLAG", CapConstants.EMPTY_STRING);
			grid3Map.put("REJ_DATE", CapConstants.EMPTY_STRING);
			grid3Map.put("REJ_CNT", CapConstants.EMPTY_STRING);
			grid3Map.put("REJ_SWFT", CapConstants.EMPTY_STRING);
			grid3Map.put("REJ_AMT", CapConstants.EMPTY_STRING);
			returnMap.put("ERRORMSG",
					String.valueOf(refundAndRejectedMap.get("ERROR")));
		}

		gridview2.add(grid2Map);
		gridview3.add(grid3Map);

		return grid2BadCreditRecord + grid3BadCreditRecord;
	}

	private String dateFormat(String date) {
		if ("********".equals(date)
				|| !CapDate.isMatchPattern(date, CapDate.DEFAULT_DATE_FORMAT)) {
			return "";
		}
		return CapDate.formatyyyyMMddToDateFormat(date, "yyyy-MM-dd");
	}

	private String processEjcicNegativeCreditRecord(
			List<Map<String, Object>> negativeCreditList, List<Map<String, Object>> loanLossList, 
			String custId, String custName, List<Map<String, Object>> gridview1)
			throws CapFormatException {

		Map<String, String> bankMap = codeTypeService
				.findByCodeType("FinancialCod01-LocalBank");
		NumericFormatter nf = new NumericFormatter();
		String grid1BadCreditRecord = "1";

		if (negativeCreditList.isEmpty() && loanLossList.isEmpty()) {
			Map<String, Object> grid1Map = new HashMap<String, Object>();
			grid1Map.put("CUSTID", custId);// 統一編號
			grid1Map.put("CUST_NAME", custName);// 客戶名稱
			grid1Map.put("DATA_FROM", "無負面信用資料");// 資料來源出處
			grid1Map.put("FROM_FLAG", CapConstants.EMPTY_STRING);// 來源代碼
			grid1Map.put("BANK_CODE", CapConstants.EMPTY_STRING);// 原因發生銀行
			grid1Map.put("CREATE_DT", CapConstants.EMPTY_STRING);// 建檔日
			grid1Map.put("DUE_DATE", CapConstants.EMPTY_STRING);// 資料揭露到期日
			grid1Map.put("BAD_CLASS", CapConstants.EMPTY_STRING);// 信用負面類型
			grid1Map.put("BAD_SWFT", CapConstants.EMPTY_STRING);// 幣別
			grid1Map.put("BAD_AMT", CapConstants.EMPTY_STRING);// 金額
			grid1Map.put("DECRIPTION", CapConstants.EMPTY_STRING);// 揭露信用負面說明
			gridview1.add(grid1Map);
			grid1BadCreditRecord = "0";
		}

		for (Map<String, Object> bean : negativeCreditList) {
			Map<String, Object> grid1Map = new HashMap<String, Object>();
			grid1Map.put("CUSTID", bean.get("LNBADCR_CUST_ID"));// 統一編號
			grid1Map.put("CUST_NAME", bean.get("LNBADCR_CUST_NAME"));// 客戶名稱
			grid1Map.put("DATA_FROM", bean.get("LNBADCR_DATA_FROM"));// 資料來源出處
			grid1Map.put("FROM_FLAG",
					"I".equals(bean.get("LNBADCR_FROM_FLAG")) ? "內部" : "外部");// 來源代碼
																				// I-
																				// 內部
																				// ,O-
																				// 外部
			// 原因發生銀行
			String bankCode = "";
			if (Util.isNotEmpty(bean.get("LNBADCR_BANK_CODE"))) {
				String tempBankNo = (String) bean.get("LNBADCR_BANK_CODE");
				bankCode = tempBankNo
						+ (bankMap.get(tempBankNo) != null ? "-"
								+ bankMap.get(tempBankNo)
								: CapConstants.EMPTY_STRING);
			}

			grid1Map.put("BANK_CODE", bankCode);

			grid1Map.put(
					"CREATE_DT",
					bean.get("LNBADCR_CREATE_DT") == null // 建檔日
					? CapConstants.EMPTY_STRING
							: CapDate.formatDate(
									new Date(((Timestamp) bean
											.get("LNBADCR_CREATE_DT"))
											.getTime()), "yyyy-MM-dd"));
			// 資料揭露到期日
			String dueDate = "";
			if (Util.isEmpty(bean.get("LNBADCR_DUE_DATE"))) {
				dueDate = "永久";
			} else {
				String tempDate = CapDate.formatDateFromF1ToF2(
						(String) bean.get("LNBADCR_DUE_DATE"), "yyyy-MM-dd",
						"yyyy-MM-dd");
				dueDate = tempDate == null ? dueDate : tempDate;
			}

			grid1Map.put("DUE_DATE", dueDate);
			grid1Map.put("BAD_CLASS", bean.get("LNBADCR_BAD_CLASS"));// 信用負面類型
			grid1Map.put("BAD_SWFT", bean.get("LNBADCR_BAD_SWFT"));// 幣別
			
			//當 select * from ln.lnbadcr where LNBADCR_PROG_NAME='LNPBW03' 
			//因卡處傳來的資料無金額，為免出現金額0 造成誤會，在 SQL 加工讓 LNBADCR_BAD_AMT 可以 return null 
			Object LNBADCR_BAD_AMT = bean.get("LNBADCR_BAD_AMT");
			grid1Map.put("BAD_AMT", Util.isEmpty(Util.trim(LNBADCR_BAD_AMT))?"":nf.reformat(LNBADCR_BAD_AMT));// 金額
			grid1Map.put("DECRIPTION", bean.get("LNBADCR_DECRIPTION"));// 揭露信用負面說明
			gridview1.add(grid1Map);
		}
		
		for (Map<String, Object> bean : loanLossList) {
			
            Map<String, Object> grid1Map = new HashMap<String, Object>();
            String id = CapString.trimNull(bean.get("LNF030_CUST_ID"));
            // ID
            grid1Map.put("CUSTID", id);
            String name = CapString.trimNull(bean.get("CNAME"));
            // 姓名
            grid1Map.put("CUST_NAME", name);
            // 資料來源出處
            grid1Map.put("DATA_FROM", "本行逾催呆名單");

            grid1Map.put("FROM_FLAG", "內部");

            // 原因發生銀行
            grid1Map.put("BANK_CODE", "兆豐國際商銀");

            // 建檔日
            grid1Map.put("CREATE_DT", bean.get("LNF030_OPEN_DATE") == null 
            				? CapConstants.EMPTY_STRING 
            				: CapDate.formatDate((Date)bean.get("LNF030_OPEN_DATE"), "yyyy-MM-dd"));

            // 資料揭露到期日
            grid1Map.put("DUE_DATE", "永久");

            // 信用負面類型
            grid1Map.put("BAD_CLASS", bean.get("LNF030_STATUS_NM"));
            // 幣別
            grid1Map.put("BAD_SWFT", bean.get("LNF030_SWFT"));
            // 金額
            String progName = CapString.trimNull(bean.get("LNF030_LOAN_BAL"));
            grid1Map.put("BAD_AMT", Util.isEmpty(progName) ? "" : nf.reformat(progName));

            // 揭露信用負面說明
            grid1Map.put("DECRIPTION", bean.get("LNF030_MEMO"));

            gridview1.add(grid1Map);
        }

		return grid1BadCreditRecord;
	}

	@Override
	public C101S01S modifyC101S01SForMixRecordData(String mainId,
			String custId, String dupNo, String dataType, String dataStatus,
			byte[] loanCreditDesFile) {

		List<C101S01S> c101s01sList = c101s01sDao.findByList(mainId, custId,
				dupNo, dataType);
		c101s01sDao.delete(c101s01sList);
		c101s01sDao.flush();

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		C101S01S c101s01s = new C101S01S();
		c101s01s.setMainId(mainId);
		c101s01s.setCustId(custId);
		c101s01s.setDupNo(dupNo);
		c101s01s.setDataType(dataType);
		c101s01s.setDataStatus(dataStatus);
		c101s01s.setReportFile(loanCreditDesFile);
		c101s01s.setFileSeq("1");
		Timestamp currentDateTime = CapDate.getCurrentTimestamp();
		c101s01s.setCreateTime(currentDateTime);
		c101s01s.setUpdater(user.getUserId());
		c101s01s.setUpdateTime(currentDateTime);
		c101s01s.setDataCreateTime(currentDateTime);
		// 20200604 新增reportFileType是JSON時，檔案類型設為J
		if (loanCreditDesFile != null && isJSON(loanCreditDesFile)) {
			c101s01s.setReportFileType("J");
		}
		c101s01sDao.save(c101s01s);
		return c101s01s;
	}

	private Object getMessageOfStakeholder(Object msg) {

		Properties abstractProperties = MessageBundleScriptCreator
				.getComponentResource(AbstractEloanPage.class);
		Properties cls1131Properties = MessageBundleScriptCreator
				.getComponentResource(CLS1131S01Page.class);

		if ("Y".equals(msg)) {
			return abstractProperties.getProperty("yes");
		} else if ("N".equals(msg)) {
			return abstractProperties.getProperty("no");
		} else if (CapString.trimNull(msg).contains("EAI 1000")) {
			// EAI 1000:查無資料
			return abstractProperties.getProperty("no");
		} else if (CapString.trimNull(msg).contains("ERROR")) {
			// HostDBMsg.ERR=主機資料庫連線異常，請連絡資訊處
			return cls1131Properties.getProperty("message.eai.hostError");
		} else {
			return msg;
		}
	}

	public String getDataStatusOfStakeholder(String msg) {

		if ("N".equals(msg) || CapString.trimNull(msg).contains("EAI 1000")) {
			return "0";
		}

		return "1";
	}

	private String getRelationshipNameByMegaId(String custName, String custId,
			String dupNo) {

		// 戶名查詢金控法44條
		String relNAME = custName;
		// 戶名為空，查詢0024ENAME
		if (CapString.isEmpty(relNAME)) {

			List<Map<String, Object>> custNamelsit = iCustomerService
					.findByIdBy0024(custId);
			if (custNamelsit != null) {

				for (Map<String, Object> cust : custNamelsit) {
					if (dupNo.equals(cust.get("DUPNO"))) {
						relNAME = CapString.trimNull(Util.isNotEmpty(cust
								.get("ENAME")) ? cust.get("ENAME") : "");
					}
				}
			}
		}

		return relNAME;
	}

	public Map<String, Object> getStakeholderData(String custId, String dupNo,
			String custName) throws CapMessageException {

		Map<String, Object> returnMap = new HashMap<String, Object>();

		JSONArray stakeholderData = eaiService
				.findStakeholderInfoByFinancialHoldingAct("", custId, dupNo);// 客戶是否為金控利害關係人
		String remain = String.valueOf(stakeholderData.get(0));
		String other = String.valueOf(stakeholderData.get(3));
		String name = custName;
		String law44 = String.valueOf(stakeholderData.get(1));
		String law45 = String.valueOf(stakeholderData.get(2));

		// MEGAID用戶名查詢金控法
		if (custId.matches("^[A-Z]{2}Z[\\d]{7}$")) {

			String relNAME = this.getRelationshipNameByMegaId(custName, custId,
					dupNo);
			// 取代畫面上的戶名
			name = relNAME;

			// 若戶名為空，顯示錯誤訊息
			law44 = "無中文及英文戶名";
			law45 = "無中文及英文戶名";

			if (Util.isNotEmpty(relNAME)) {

				String RelId = "";
				String compare = "";
				String check = "0";
				String nCode = NcodeEnum.法人2.getCode(); // 法人

				// 戶名查詢金控法44條
				JSONArray law44Data = eaiService.doIPSCO01(RelId, relNAME,
						compare, check, "44", nCode);
				law44 = "N";// message.001=查無資料
				if (law44Data != null && !law44Data.isEmpty()) {
					law44 = "Y";
				}

				// 戶名查詢金控法45條
				JSONArray law45Data = eaiService.doIPSCO01(RelId, relNAME,
						compare, check, "45", nCode);
				law45 = "N";// message.001=查無資料
				if (law45Data != null && !law45Data.isEmpty()) {
					law45 = "Y";
				}
			}
		}

		stakeholderData.set(1, law44);
		stakeholderData.set(2, law45);

		Properties properties = MessageBundleScriptCreator
				.getComponentResource(AbstractEloanPage.class);

		// J-108-0178 新增公司法董事控制從屬關係公司查詢
		String director = properties.getProperty("no");
		String directorStatus = "N";

		if (!custId.isEmpty()) {

			Map<String, Object> directorMap = misElf902Service
					.findCnameByRcustIdAndDupNo1(custId, dupNo);
			String mapKey = !directorMap.keySet().isEmpty() ? new ArrayList<String>(
					directorMap.keySet()).get(0) : "";

			String directorValue = StringUtils.removeEnd(
					CapString.trimNull(directorMap.get(mapKey)), "、");
			if (directorValue.length() > 0) {
				director = properties.getProperty("yes") + "(" + directorValue
						+ ")";
				directorStatus = "Y";
			}

			// save 查詢後記錄, 供報表列印
			List<Map<String, Object>> list = this.getMisElf901DataList(custId,
					custName, trans4Elf901_otherMsg(stakeholderData.get(0)),
					trans4Elf901_otherMsg(stakeholderData.get(1)),
					trans4Elf901_otherMsg(stakeholderData.get(2)), this
							.getClass().getName());
			misElf901Service.addElf901(list);
		}

		returnMap.put("custId", custId);
		returnMap.put("dupNo", dupNo);
		returnMap.put("name", name);
		returnMap.put("remain", this.getMessageOfStakeholder(remain));
		returnMap.put("law44", this.getMessageOfStakeholder(law44));
		returnMap.put("law45", this.getMessageOfStakeholder(law45));
		returnMap.put("other", this.getMessageOfStakeholder(other));
		returnMap.put("director", this.getMessageOfStakeholder(director));
		returnMap.put(
				"dataStatus",
				this.getDataStatusOfStakeholder(remain)
						+ this.getDataStatusOfStakeholder(law44)
						+ this.getDataStatusOfStakeholder(law45)
						+ this.getDataStatusOfStakeholder(other)
						+ this.getDataStatusOfStakeholder(directorStatus));
		return returnMap;
	}

	@Override
	public Map<String, Object> getRefusedData(String custId, String dupNo,
			String custName) throws CapFormatException {

		List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();
		Map<String, Object> rtnMap = new HashMap<String, Object>();

		// 查詢本行
		List<Map<String, Object>> megaList = misLnunIdService
				.findLnunIdByCustId(custId, dupNo);
		// 查詢金控
		List<Map<String, Object>> fhList = misLnunIdService
				.findRefusedRecordOfFinancialHolding(custId, dupNo);

		Map<String, CapAjaxFormResult> codeTypeMap = codeTypeService
				.findByCodeType(new String[] { "oth1000_rejectType",
						"oth1000_rejectReason", "oth1001_statusCd" });

		megaList = this.setRefusedRecordData(custId, dupNo, custName, 1,
				megaList, codeTypeMap);
		fhList = this.setRefusedRecordData(custId, dupNo, custName, 2, fhList,
				codeTypeMap);

		String dataStatus = (String) megaList.get(0).get("dataStatus");
		dataStatus += (String) fhList.get(0).get("dataStatus");

		dataList.addAll(megaList);
		dataList.addAll(fhList);

		rtnMap.put("refuseList", dataList);
		rtnMap.put("dataStatus", dataStatus);

		return rtnMap;
	}

	private List<Map<String, Object>> setRefusedRecordData(String custId,
			String dupNo, String custName, int type,
			List<Map<String, Object>> dataList,
			Map<String, CapAjaxFormResult> codeTypeMap) {

		List<Map<String, Object>> rtnList = new ArrayList<Map<String, Object>>();

		for (Map<String, Object> data : dataList) {

			Map<String, Object> map = new HashMap<String, Object>();
			// 統一編號
			map.put("custId", custId);
			// 重覆序號
			map.put("dupNo", dupNo);
			// 客戶名稱
			map.put("custName", custName);
			// 本行/金控
			map.put("type", type == 1 ? "本行" : "金控");

			Timestamp regDt = (Timestamp) MapUtils.getObject(data, "REGDT");
			// 登錄日期
			map.put("regDt",
					regDt == null ? "" : StringUtils.left(regDt.toString(), 10));
			String regBr = MapUtils.getString(data, "REGBR");
			// 婉卻分行
			map.put("regBr", branchService.getBranchName(regBr));
			String statusCd = MapUtils.getString(data, "STATUSCD");
			// 婉卻控管種類
			map.put("statusCd",
					CapString.trimNull(codeTypeMap.get("oth1001_statusCd").get(
							statusCd)));
			String clsCase = MapUtils.getString(data, "CLSCASE");
			// 本行婉卻業務別/金控卡貸婉卻
			map.put("clsCase",
					codeTypeMap.get("oth1000_rejectType").get(clsCase));
			String refuseCd = MapUtils.getString(data, "REFUSECD");
			// 婉卻原因/說明
			map.put("refuseCd",
					codeTypeMap.get("oth1000_rejectReason").containsKey(
							refuseCd) ? codeTypeMap.get("oth1000_rejectReason")
							.get(refuseCd) : refuseCd + " "
							+ MapUtils.getString(data, "REFUSEDS"));
			map.put("dataStatus", "1");

			rtnList.add(map);
		}

		if (dataList.isEmpty()) {
			Map<String, Object> map = new HashMap<String, Object>();
			map.put("custId", custId);
			map.put("dupNo", dupNo);
			map.put("custName", custName);
			map.put("type", type == 1 ? "本行" : "金控");
			map.put("regDt", "無婉卻記錄");
			map.put("dataStatus", "0");
			rtnList.add(map);
		}

		return rtnList;
	}

	public byte[] getReportOfCreditAnomalyData(Map<String, Object> map)
			throws FileNotFoundException, IOException, Exception {

		PageParameters params = new CapMvcParameters();
		params.put("gridview1", JSONArray.fromObject(map.get("gridview1")));
		params.put("gridview2", JSONArray.fromObject(map.get("gridview2")));
		params.put("gridview3", JSONArray.fromObject(map.get("gridview3")));

		if (Util.isNotEmpty(map.get("ERRORMSG"))) {
			params.put("ERRORMSG", String.valueOf(map.get("ERRORMSG")));
		}

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		params.put("queryMan", user.getUserId() + " " + user.getUserName());
		// lmsService.getUserName(user.getUserId())
		return cls1131R03RptService.getContent(params);
	}

	private String trans4Elf901_otherMsg(Object msg) {
		if (CapString.trimNull(msg).contains("EAI 1000")) {
			// EAI 1000:查無資料
			return "N";
		} else if (CapString.trimNull(msg).contains("ERROR")) {
			// HostDBMsg.ERR=主機資料庫連線異常，請連絡資訊處
			return null;
		} else {
			return CapString.trimNull(msg);
		}
	}

	/**
	 * NEW MisElf901 Data List
	 * 
	 * @return
	 */
	private List<Map<String, Object>> getMisElf901DataList(String custId,
			String custName, String bank, String law44, String law45,
			String querytx) {

		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		Map<String, Object> map = new HashMap<String, Object>();
		map.put("BRN", user.getUnitNo()); // 分行
		map.put("CUSTID", custId); // 客戶ID
		map.put("CUSTNAME", custName); // 客戶姓名
		map.put("BANK", bank); // 是否為銀行法利害關係人
		map.put("LAW44", law44); // 是否為金控法44條利害關係人
		map.put("LAW45", law45); // 是否為金控法45條利害關係人
		map.put("QUERYTX", querytx); // 查詢交易代碼
		map.put("LAST_TELLER", user.getUserId()); // 查詢人員
		map.put("LAST_TIME", CapDate.getCurrentTimestamp()); // 查詢時間
		list.add(map);

		return list;
	}

	// @Override
	// public List<Map<String, Object>> loadDataArchivalRecordData(String
	// mainId){
	//
	// List<C101S01S> c101s01sList = c101s01sDao.findByMainId(mainId);
	// List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
	//
	// for(C101S01S c101s01s : c101s01sList){
	// list.add(clsService.getReturnMapForMixRecordData(c101s01s.getDataStatus(),
	// c101s01s.getDataType(), c101s01s.getFileSeq(),
	// c101s01s.getCreateTime()));
	// }
	//
	// return list;
	// }

	@Override
	public Map<String, Object> getCURIQ01(String mainId, String custId)
			throws CapMessageException {
		Map<String, Object> map = new HashMap<String, Object>();

		String dataStatus = "0";
		List<Map<String, Object>> eaiCURIQ01_list = new ArrayList<Map<String, Object>>();
		// =========================================
		try {
			JSONArray jsonArray = eaiService.findCURIQ01ById(custId, mainId);
			if (jsonArray != null) {
				int size = jsonArray.size();
				if (size > 0) {
					dataStatus = "1"; // 表示有 證券違約交割資料
					for (int i = 0; i < size; i++) {
						Object elm = jsonArray.get(i);
						String NAME_CURJ = "ERROR";
						String RDATE_CURJ = "ERROR";
						String CDATE_CURJ = "ERROR";
						String ORDATE_CURJ = "ERROR";
						String OCDATE_CURJ = "ERROR";
						String SORT_CURJ = "ERROR";
						String LIDNO_CURJ = "ERROR";
						String FRDATE_CURJ = "ERROR";
						String FCDATE_CURJ = "ERROR";
						if (elm instanceof JSONObject) {
							JSONObject jsonElm = (JSONObject) elm;
							/*
							 * {"IDNO":"16507519","RDATE":"890915","CDATE":"940915"
							 * ,
							 * "ORDATE":"890915","OCDATE":"940915","SORT":"","NAME"
							 * :"桂祥投資股","LIDNO":"","FRDATE":"0","FCDATE":"0"}]
							 */
							logger.trace("eai_CURIQ01_[custId=" + custId
									+ "]---[" + i + "/" + size
									+ "] instanceof JSONObject[" + jsonElm
									+ "]");
							RDATE_CURJ = Util.trim(jsonElm.optString("RDATE"));
							CDATE_CURJ = Util.trim(jsonElm.optString("CDATE"));
							ORDATE_CURJ = Util
									.trim(jsonElm.optString("ORDATE"));
							OCDATE_CURJ = Util
									.trim(jsonElm.optString("OCDATE"));
							SORT_CURJ = Util.trim(jsonElm.optString("SORT"));
							NAME_CURJ = Util.trim(jsonElm.optString("NAME"));
							LIDNO_CURJ = Util.trim(jsonElm.optString("LIDNO"));
							FRDATE_CURJ = Util
									.trim(jsonElm.optString("FRDATE"));
							FCDATE_CURJ = Util
									.trim(jsonElm.optString("FCDATE"));
						} else {
							logger.info("[" + i + "/" + size
									+ "] ***unknown instance***");
						}

						eaiCURIQ01_list.add(build_CURIQ01_data(custId,
								NAME_CURJ, RDATE_CURJ, CDATE_CURJ, ORDATE_CURJ,
								OCDATE_CURJ, SORT_CURJ, LIDNO_CURJ,
								FRDATE_CURJ, FCDATE_CURJ));
					}
				}
			}
			// ===============================
			if (eaiCURIQ01_list.size() == 0) {
				eaiCURIQ01_list.add(build_CURIQ01_ok_data(custId));
			}
		} catch (CapMessageException e) {
			if (Util.equals("EAI 1000:查無資料", e.getMessage())) {
				logger.trace("證券暨期貨違約交割紀錄[custId=" + custId + "][mainId="
						+ mainId + "]" + e.getMessage());
				if (eaiCURIQ01_list.size() == 0) {
					eaiCURIQ01_list.add(build_CURIQ01_ok_data(custId));
				}
			} else {
				throw e;
			}
		} catch (Exception e) {
			throw new CapMessageException(e, getClass());
		}
		map.put("dataStatus", dataStatus);
		map.put("eaiCURIQ01_list", eaiCURIQ01_list);
		return map;
	}

	private Map<String, Object> build_CURIQ01_ok_data(String custId) {
		String findNoDataDesc = "查無資料";
		return build_CURIQ01_data(custId, findNoDataDesc, findNoDataDesc,
				findNoDataDesc, findNoDataDesc, findNoDataDesc, findNoDataDesc,
				findNoDataDesc, findNoDataDesc, findNoDataDesc);
	}

	private Map<String, Object> build_CURIQ01_data(String custId,
			String NAME_CURJ, String RDATE_CURJ, String CDATE_CURJ,
			String ORDATE_CURJ, String OCDATE_CURJ, String SORT_CURJ,
			String LIDNO_CURJ, String FRDATE_CURJ, String FCDATE_CURJ) {
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("IDNO_CURJ", custId);// 統一編號
		map.put("NAME_CURJ", NAME_CURJ);// 客戶名稱
		map.put("RDATE_CURJ", RDATE_CURJ);// 拒絕往來日期(TSE)
		map.put("CDATE_CURJ", CDATE_CURJ);// 解除拒絕往來日期(TSE)
		map.put("ORDATE_CURJ", ORDATE_CURJ);// 拒絕往來日期(OTC)
		map.put("OCDATE_CURJ", OCDATE_CURJ);// 解除拒絕往來日期(OTC)
		map.put("SORT_CURJ", SORT_CURJ);// 身份別
		map.put("LIDNO_CURJ", LIDNO_CURJ);// 負責人ID
		map.put("FRDATE_CURJ", FRDATE_CURJ);// 期貨拒絕往來日期
		map.put("FCDATE_CURJ", FCDATE_CURJ);// 期貨解除拒絕往來日期
		return map;
	}

	@Override
	public String sync_ej_data_to_C101S01E(C101M01A c101m01a) {
		String prodId = "P9";
		List<Map<String, Object>> datedate_list = ejcicService
				.get_mis_datadate_records(c101m01a.getCustId(), prodId);
		if (datedate_list.size() > 0) {
			Map<String, Object> map_datedate = datedate_list.get(0); // 已依時間去
																		// sort
																		// 過了
			String str_qDate = MapUtils.getString(map_datedate, "QDATE");
			TWNDate qDate = TWNDate.valueOf(str_qDate);
			// ==============
			C101S01E c101s01e = clsService.findC101S01E(c101m01a);
			if (c101s01e == null) {
				c101s01e = new C101S01E();
				c101s01e.setMainId(c101m01a.getMainId());
				c101s01e.setCustId(c101m01a.getCustId());
				c101s01e.setDupNo(c101m01a.getDupNo());
			}
			boolean sync_to_Z13 = false;
			if (c101s01e.getZ13_qTime() == null) {
				sync_to_Z13 = true; // 無HTML資料
			} else {
				if (qDate != null
						&& LMSUtil.cmpDate(c101s01e.getZ13_qTime(), "<", qDate)) {
					sync_to_Z13 = true; // Z13的查詢時間，在昨天以前
				}
			}
			if (sync_to_Z13 && qDate != null) {
				for (Map<String, Object> querylog : ejcicService
						.getCPXQueryLogHtml(c101m01a.getCustId(), prodId,
								MapUtils.getString(map_datedate, "QDATE"))) {
					if (Util.equals(MapUtils.getString(querylog, "TXID"),
							CrsUtil.EJ_TXID_HZ13)) {
						String htmlData = MapUtils.getString(querylog,
								"HTMLDATA");
						c101s01e.setZ13_html(htmlData);
						if (c101s01e.getOneBtnQ_qTime() != null
								&& LMSUtil.cmpDate(qDate, "==",
										c101s01e.getOneBtnQ_qTime())) {
							c101s01e.setZ13_qTime(c101s01e.getOneBtnQ_qTime());
						} else {
							c101s01e.setZ13_qTime(Timestamp.valueOf(qDate
									.toFullAD('-')));
						}
						clsService.daoSave(c101s01e);
						return "Y";
					}
				}
			}
		}
		return "N";
	}

	@Override
	public boolean isJSON(byte[] byteArr) {
		String jsonstr = new String(byteArr);
		try {
			JSONObject.fromObject(jsonstr);
		} catch (JSONException ex) {
			return false;
		}
		return true;
	}

	public List<C101S01V> findC101S01VByMainid(String mainId,String ownBrId,String CustID,String Dupno){
		return c101s01vDao.findByUniqueKey(mainId,null, CustID, Dupno);
	}

	@Override
	public String[] getPersonalIncomeDetailColumns() {
		return new String[] { "positionType", "mainIncomeType", "itemAvalue", "itemAvalueYear", "itemB1value1",
				"itemB1value2", "itemB1value3", "itemB1value4", "itemB1value5", "itemB1value6", "itemB1HolidayBonus",
				"itemB1YearEndBonus", "itemB1valueYear", "itemB2value", "itemB2valueYear", "itemB3ReportType",
				"itemB3value1", "itemB3value2", "itemB3value3", "itemB3value4", "itemB3value5", "itemB3value6",
				"itemB3InProfit", "itemB3Holding", "itemB3valueYear", "itemB4value1", "itemB4value2", "itemB4value3",
				"itemB4value4", "itemB4value5", "itemB4value6", "itemB4valueYear", "otherC1", "itemC1value1",
				"itemC1value2", "itemC1value3", "itemC1value4", "itemC1value5", "itemC1value6", "itemC1valueYear",
				"otherC2", "itemC2value1", "itemC2value2", "itemC2value3", "itemC2value4", "itemC2value5",
				"itemC2value6", "itemC2valueYear", "otherC3", "itemC3value1", "itemC3value2", "itemC3value3",
				"itemC3value4", "itemC3value5", "itemC3value6", "itemC3valueYear", "otherD4", "itemD4value1",
				"itemD4value2", "itemD4value3", "itemD4value4", "itemD4value5", "itemD4value6", "itemD4valueYear",
				"otherD5", "itemD5value1", "itemD5value2", "itemD5value3", "itemD5value4", "itemD5value5",
				"itemD5value6", "itemD5valueYear", "otherD6", "itemD6value1", "itemD6value2", "itemD6value3",
				"itemD6value4", "itemD6value5", "itemD6value6", "itemD6valueYear", "otherD7", "itemD7value1",
				"itemD7value2", "itemD7value3", "itemD7value4", "itemD7value5", "itemD7value6", "itemD7valueYear",
				"otherD8", "itemD8value1", "itemD8value2", "itemD8value3", "itemD8value4", "itemD8value5",
				"itemD8value6", "itemD8valueYear", "otherD9", "itemD9value1", "itemD9value2", "itemD9value3",
				"itemD9value4", "itemD9value5", "itemD9value6", "itemD9valueYear", "itemB4DisRate", "itemD9DisRate" };
	}

	@Override
	public void calPersonalIncomeDetail(C101S01B s01b) throws CapException {

		//負債比是否重新計算
		BigDecimal preYearIncome = s01b.getPayAmt();
		BigDecimal preOtherIncome = s01b.getOthAmt();
		
		BigDecimal yearIncome = BigDecimal.ZERO;
		BigDecimal otherIncome = BigDecimal.ZERO;

		//先將合計後年化收入清除，免得有殘值
		s01b.setItemAvalueYear(null);
		s01b.setItemB1valueYear(null);
		s01b.setItemB2valueYear(null);
		s01b.setItemB3valueYear(null);
		s01b.setItemB4valueYear(null);
		s01b.setItemC1valueYear(null);
		s01b.setItemC2valueYear(null);
		s01b.setItemC3valueYear(null);
		s01b.setItemD4valueYear(null);
		s01b.setItemD5valueYear(null);
		s01b.setItemD6valueYear(null);
		s01b.setItemD7valueYear(null);
		s01b.setItemD8valueYear(null);
		s01b.setItemD9valueYear(null);

		String mainIncomeType = s01b.getMainIncomeType();
		if ("A".equals(mainIncomeType)) {
			BigDecimal itemAvalue = s01b.getItemAvalue();
			s01b.setItemAvalueYear(itemAvalue);

			yearIncome = yearIncome.add(s01b.getItemAvalueYear());
		} else if ("B1".equals(mainIncomeType)) {

			String positionType = s01b.getPositionType();
			BigDecimal itemB1value1 = CapMath.bigDecimalIsNullToZero(s01b.getItemB1value1());
			BigDecimal itemB1value2 = CapMath.bigDecimalIsNullToZero(s01b.getItemB1value2());
			BigDecimal itemB1value3 = CapMath.bigDecimalIsNullToZero(s01b.getItemB1value3());
			BigDecimal itemB1HolidayBonus = CapMath.bigDecimalIsNullToZero(s01b.getItemB1HolidayBonus());
			BigDecimal itemB1YearEndBonus = CapMath.bigDecimalIsNullToZero(s01b.getItemB1YearEndBonus());

			//1.非業務職者(上方職位別勾選1、2者)：
			//僅顯示3個B1a欄位、三節及年收獎金供填列
			//公式：{(B1a加總後÷3)+(B1b加總÷12)}×12 = (all B1a)*4+B1b
			if ("1".equals(positionType) || "2".equals(positionType)) {
				BigDecimal value1 = itemB1value1.add(itemB1value2).add(itemB1value3).multiply(BigDecimal.valueOf(4));
				BigDecimal value2 = itemB1HolidayBonus.add(itemB1YearEndBonus);
				BigDecimal result = value1.add(value2);
				s01b.setItemB1valueYear(result.setScale(0, BigDecimal.ROUND_HALF_UP));

			} else {
				//2.業務職者(上方職位別勾選3者)：
				//顯示6個B1a欄位、三節及年收獎金供填列
				//{(B1a加總後÷6)+(B1b加總÷12)}×12" = (all B1a)*2+B1b
				BigDecimal itemB1value4 = CapMath.bigDecimalIsNullToZero(s01b.getItemB1value4());
				BigDecimal itemB1value5 = CapMath.bigDecimalIsNullToZero(s01b.getItemB1value5());
				BigDecimal itemB1value6 = CapMath.bigDecimalIsNullToZero(s01b.getItemB1value6());

				BigDecimal value1 = itemB1value1.add(itemB1value2)
						.add(itemB1value3)
						.add(itemB1value4)
						.add(itemB1value5)
						.add(itemB1value6)
						.multiply(BigDecimal.valueOf(2));
				BigDecimal value2 = itemB1HolidayBonus.add(itemB1YearEndBonus);
				BigDecimal result = value1.add(value2);
				s01b.setItemB1valueYear(result.setScale(0, BigDecimal.ROUND_HALF_UP));
			}

			yearIncome = yearIncome.add(s01b.getItemB1valueYear());
		} else if ("B2".equals(mainIncomeType)) {
			//=B2×12
			BigDecimal itemB2value = s01b.getItemB2value();
			BigDecimal result = itemB2value.multiply(BigDecimal.valueOf(12));
			s01b.setItemB2valueYear(result.setScale(0, BigDecimal.ROUND_HALF_UP));

			yearIncome = yearIncome.add(s01b.getItemB2valueYear());
		} else if ("B3".equals(mainIncomeType)) {

			//公式均為B3加總×淨利率％×持股%*2
			String itemB3ReportType = s01b.getItemB3ReportType();
			BigDecimal itemB3value1 = CapMath.bigDecimalIsNullToZero(s01b.getItemB3value1());
			BigDecimal itemB3value2 = CapMath.bigDecimalIsNullToZero(s01b.getItemB3value2());
			BigDecimal itemB3value3 = CapMath.bigDecimalIsNullToZero(s01b.getItemB3value3());

			BigDecimal itemB3InProfit = CapMath.bigDecimalIsNullToZero(s01b.getItemB3InProfit());
			BigDecimal itemB3Holding = CapMath.bigDecimalIsNullToZero(s01b.getItemB3Holding());

			BigDecimal result = itemB3value1.add(itemB3value2)
					.add(itemB3value3)
					.multiply(itemB3InProfit)
					.divide(BigDecimal.valueOf(100), MathContext.DECIMAL64)
					.multiply(itemB3Holding)
					.divide(BigDecimal.valueOf(100), MathContext.DECIMAL64)
					.multiply(BigDecimal.valueOf(2));
			s01b.setItemB3valueYear(result.setScale(0, BigDecimal.ROUND_HALF_UP));

			yearIncome = yearIncome.add(s01b.getItemB3valueYear());
		} else if ("B4".equals(mainIncomeType)) {

			//=B4加總×折算率÷6*12=B4加總×折算率*2
			BigDecimal itemB4value1 = CapMath.bigDecimalIsNullToZero(s01b.getItemB4value1());
			BigDecimal itemB4value2 = CapMath.bigDecimalIsNullToZero(s01b.getItemB4value2());
			BigDecimal itemB4value3 = CapMath.bigDecimalIsNullToZero(s01b.getItemB4value3());
			BigDecimal itemB4value4 = CapMath.bigDecimalIsNullToZero(s01b.getItemB4value4());
			BigDecimal itemB4value5 = CapMath.bigDecimalIsNullToZero(s01b.getItemB4value5());
			BigDecimal itemB4value6 = CapMath.bigDecimalIsNullToZero(s01b.getItemB4value6());
			BigDecimal itemB4DisRate = CapMath.bigDecimalIsNullToZero(s01b.getItemB4DisRate());
			BigDecimal result = itemB4value1.add(itemB4value2)
					.add(itemB4value3)
					.add(itemB4value4)
					.add(itemB4value5)
					.add(itemB4value6)
					.multiply(itemB4DisRate)
					.divide(BigDecimal.valueOf(100), MathContext.DECIMAL64)
					.multiply(BigDecimal.valueOf(2));
			s01b.setItemB4valueYear(result.setScale(0, BigDecimal.ROUND_HALF_UP));

			yearIncome = yearIncome.add(s01b.getItemB4valueYear());
		}

		String otherC1 = s01b.getOtherC1();
		if ("Y".equals(otherC1)) {

			//=C1加總×80%÷6×12 = C1加總×80%*2
			BigDecimal itemC1value1 = CapMath.bigDecimalIsNullToZero(s01b.getItemC1value1());
			BigDecimal itemC1value2 = CapMath.bigDecimalIsNullToZero(s01b.getItemC1value2());
			BigDecimal itemC1value3 = CapMath.bigDecimalIsNullToZero(s01b.getItemC1value3());
			BigDecimal itemC1value4 = CapMath.bigDecimalIsNullToZero(s01b.getItemC1value4());
			BigDecimal itemC1value5 = CapMath.bigDecimalIsNullToZero(s01b.getItemC1value5());
			BigDecimal itemC1value6 = CapMath.bigDecimalIsNullToZero(s01b.getItemC1value6());

			BigDecimal result = itemC1value1.add(itemC1value2)
					.add(itemC1value3)
					.add(itemC1value4)
					.add(itemC1value5)
					.add(itemC1value6)
					.multiply(BigDecimal.valueOf(80))
					.divide(BigDecimal.valueOf(100), MathContext.DECIMAL64)
					.multiply(BigDecimal.valueOf(2));
			s01b.setItemC1valueYear(result.setScale(0, BigDecimal.ROUND_HALF_UP));

			yearIncome = yearIncome.add(s01b.getItemC1valueYear());
		}
		String otherC2 = s01b.getOtherC2();

		if ("Y".equals(otherC2)) {

			//=C2加總×80%÷6×12 = C2加總×80%*2
			BigDecimal itemC2value1 = CapMath.bigDecimalIsNullToZero(s01b.getItemC2value1());
			BigDecimal itemC2value2 = CapMath.bigDecimalIsNullToZero(s01b.getItemC2value2());
			BigDecimal itemC2value3 = CapMath.bigDecimalIsNullToZero(s01b.getItemC2value3());
			BigDecimal itemC2value4 = CapMath.bigDecimalIsNullToZero(s01b.getItemC2value4());
			BigDecimal itemC2value5 = CapMath.bigDecimalIsNullToZero(s01b.getItemC2value5());
			BigDecimal itemC2value6 = CapMath.bigDecimalIsNullToZero(s01b.getItemC2value6());

			BigDecimal result = itemC2value1.add(itemC2value2)
					.add(itemC2value3)
					.add(itemC2value4)
					.add(itemC2value5)
					.add(itemC2value6)
					.multiply(BigDecimal.valueOf(80))
					.divide(BigDecimal.valueOf(100), MathContext.DECIMAL64)
					.multiply(BigDecimal.valueOf(2));
			s01b.setItemC2valueYear(result.setScale(0, BigDecimal.ROUND_HALF_UP));

			yearIncome = yearIncome.add(s01b.getItemC2valueYear());
		}

		String otherC3 = s01b.getOtherC3();

		if ("Y".equals(otherC3)) {

			//=C3加總÷6×12=C3加總*2
			BigDecimal itemC3value1 = CapMath.bigDecimalIsNullToZero(s01b.getItemC3value1());
			BigDecimal itemC3value2 = CapMath.bigDecimalIsNullToZero(s01b.getItemC3value2());
			BigDecimal itemC3value3 = CapMath.bigDecimalIsNullToZero(s01b.getItemC3value3());
			BigDecimal itemC3value4 = CapMath.bigDecimalIsNullToZero(s01b.getItemC3value4());
			BigDecimal itemC3value5 = CapMath.bigDecimalIsNullToZero(s01b.getItemC3value5());
			BigDecimal itemC3value6 = CapMath.bigDecimalIsNullToZero(s01b.getItemC3value6());

			BigDecimal result = itemC3value1.add(itemC3value2)
					.add(itemC3value3)
					.add(itemC3value4)
					.add(itemC3value5)
					.add(itemC3value6)
					.multiply(BigDecimal.valueOf(2));
			s01b.setItemC3valueYear(result.setScale(0, BigDecimal.ROUND_HALF_UP));

			yearIncome = yearIncome.add(s01b.getItemC3valueYear());
		}

		//"1.D4~D8 皆為6欄數值中取4欄平均後×10%
		//2.D4~D9 皆必須輸滿6個欄位，若6欄中有一欄為空格或數值為0，則年化後收入直接以0計算"

		String[] loops = new String[] { "D4", "D5", "D6", "D7", "D8", "D9" };

		for (String loop : loops) {
			String other = (String) s01b.get("other" + loop);
			if ("Y".equals(other)) {
				BigDecimal itemValue1 = CapMath.bigDecimalIsNullToZero((BigDecimal) s01b.get("item" + loop + "value1"));
				BigDecimal itemValue2 = CapMath.bigDecimalIsNullToZero((BigDecimal) s01b.get("item" + loop + "value2"));
				BigDecimal itemValue3 = CapMath.bigDecimalIsNullToZero((BigDecimal) s01b.get("item" + loop + "value3"));
				BigDecimal itemValue4 = CapMath.bigDecimalIsNullToZero((BigDecimal) s01b.get("item" + loop + "value4"));
				BigDecimal itemValue5 = CapMath.bigDecimalIsNullToZero((BigDecimal) s01b.get("item" + loop + "value5"));
				BigDecimal itemValue6 = CapMath.bigDecimalIsNullToZero((BigDecimal) s01b.get("item" + loop + "value6"));

				if (BigDecimal.ZERO.compareTo(itemValue1) == 0 || BigDecimal.ZERO.compareTo(
						itemValue2) == 0 || BigDecimal.ZERO.compareTo(itemValue3) == 0 || BigDecimal.ZERO.compareTo(
						itemValue4) == 0 || BigDecimal.ZERO.compareTo(itemValue5) == 0 || BigDecimal.ZERO.compareTo(
						itemValue6) == 0) {

					s01b.set("item" + loop + "valueYear", BigDecimal.ZERO);
					//s01b.setItemD4valueYear(BigDecimal.ZERO);
				} else {
					ArrayList<BigDecimal> arrays = new ArrayList<BigDecimal>();
					arrays.add(itemValue1);
					arrays.add(itemValue2);
					arrays.add(itemValue3);
					arrays.add(itemValue4);
					arrays.add(itemValue5);
					arrays.add(itemValue6);
					Collections.sort(arrays);

					BigDecimal value = BigDecimal.ZERO;
					BigDecimal result;

					if ("D9".equals(loop)) {

						// D9增加折算率讓user填入
						BigDecimal itemD9DisRate = CapMath.bigDecimalIsNullToZero(s01b.getItemD9DisRate());

						//=D9加總×折算率÷6×12
						for (int i = 0; i < arrays.size(); i++) {
							value = value.add(arrays.get(i));
						}
						result = value.multiply(itemD9DisRate)
								.divide(BigDecimal.valueOf(100), MathContext.DECIMAL64)
								.multiply(BigDecimal.valueOf(2));
					} else {
						//D4~D8 皆為6欄數值中取4欄平均後×10%
						//消金處說不用乘上12
						for (int i = 0; i < arrays.size(); i++) {
							if (i == 0 || i == arrays.size() - 1) {
								continue;
							}
							value = value.add(arrays.get(i));
						}
						result = value.divide(BigDecimal.valueOf(arrays.size() - 2), MathContext.DECIMAL64)
								.multiply(BigDecimal.valueOf(10))
								.divide(BigDecimal.valueOf(100), MathContext.DECIMAL64);
					}
					s01b.set("item" + loop + "valueYear", result.setScale(0, BigDecimal.ROUND_HALF_UP));
				}
				otherIncome = otherIncome.add((BigDecimal) s01b.get("item" + loop + "valueYear"));
			}
		}
		// 折合萬元
		yearIncome = yearIncome.divide(BigDecimal.valueOf(10000), 0, BigDecimal.ROUND_DOWN);
		otherIncome = otherIncome.divide(BigDecimal.valueOf(10000), 0, BigDecimal.ROUND_DOWN);
		
		s01b.setPayAmt(yearIncome);
		s01b.setOthAmt(otherIncome);
		
		C101S01C c101s01c = this.findModelByKey(C101S01C.class, s01b.getMainId(), s01b.getCustId(), s01b.getDupNo());

		if (c101s01c != null && s01b.getPayAmt() != null && s01b.getOthAmt() != null) {
			if (!s01b.getPayAmt().equals(preYearIncome) || !s01b.getOthAmt().equals(preOtherIncome)) {
				c101s01c.setReCalFlg("N");
			}
		}
		
		this.save(s01b, c101s01c);
	}

	private C101S01W getNewC101s01w(C101S01B s01b, String key, String value) {
		C101S01W w = new C101S01W();
		w.setMainId(s01b.getMainId());
		w.setCustId(s01b.getCustId());
		w.setDupNo(s01b.getDupNo());
		w.setKeyString(key);
		w.setValueString(value);
		return w;
	}

	@Override
	public void calPersonalIncomeDetailV1(C101S01B s01b, JSONObject jsObject) throws CapException {

		List<GenericBean> s01ws = new ArrayList<GenericBean>();

		Iterator keys = jsObject.keys();
		while (keys.hasNext()) {
			String key = (String) keys.next();
			String value = jsObject.optString(key);
			s01ws.add(this.getNewC101s01w(s01b, Util.trim(key), Util.trim(value)));
		}

		//負債比是否重新計算
		BigDecimal preYearIncome = s01b.getPayAmt();
		BigDecimal preOtherIncome = s01b.getOthAmt();

		BigDecimal yearIncome = BigDecimal.ZERO;
		BigDecimal otherIncome = BigDecimal.ZERO;

		String[] loopItems = new String[] { "A", "B", "C", "D", "E", "F", "G", "H" };

		for (String loopItem : loopItems) {
			String hasItem = Util.trim(jsObject.optString("hasItem" + loopItem));
			if ("A".equals(loopItem) && "Y".equals(hasItem)) {
				//所得清單/扣繳憑單
				BigDecimal itemAvalueYear;
				String itemAvalue = jsObject.optString("itemAvalue");
				itemAvalueYear = CapMath.getBigDecimal(itemAvalue).setScale(0, BigDecimal.ROUND_HALF_UP);
				s01ws.add(getNewC101s01w(s01b, "itemAvalueYear", itemAvalueYear.toString()));

				yearIncome = yearIncome.add(itemAvalueYear);
			} else if ("B".equals(loopItem) && "Y".equals(hasItem)) {
				//薪轉存摺/薪資單
				String salaryStructure = jsObject.optString("salaryStructure");
				String has1year = jsObject.optString("has1year");
				BigDecimal itemBvalueYear = BigDecimal.ZERO;

				//二、如固定薪，年資未滿一年者：
				//　　B：薪轉轉摺/薪資單，B1欄位改為3格
				//　　年化公式為：(B1加總÷3╳12)+B2
				//
				//三、如為業務職，年資未滿一年者：
				//　　B：薪轉轉摺/薪資單，B1欄位改為6格
				//　　年化公式為：(B1加總÷6╳12)+B2
				int base = 12;
				if (!"Y".equals(has1year)) {
					base = "1".equals(salaryStructure) ? 3 : 6;
				}
				for (int i = 1; i <= base; i++) {
					BigDecimal itemBvalue = CapMath.getBigDecimal(jsObject.optString("itemBvalue" + i));
					itemBvalueYear = itemBvalueYear.add(itemBvalue);
				}
				// 年化
				itemBvalueYear = itemBvalueYear.multiply(BigDecimal.valueOf(12 / base)).setScale(0, BigDecimal.ROUND_HALF_UP);

				BigDecimal itemBRegularBonus = CapMath.getBigDecimal(jsObject.optString("itemBRegularBonus"));
				itemBvalueYear = itemBvalueYear.add(itemBRegularBonus);
				s01ws.add(getNewC101s01w(s01b, "itemBvalueYear", itemBvalueYear.toString()));

				yearIncome = yearIncome.add(itemBvalueYear);
			} else if ("C".equals(loopItem) && "Y".equals(hasItem)) {
				//"營利事業所得申報書/銷售額與稅額申報書(401/403報表)"
				BigDecimal itemCvalueYear = BigDecimal.ZERO;

				//公式為C加總×淨利率％×持股%
				for (int i = 1; i <= 6; i++) {
					BigDecimal itemCvalue = CapMath.getBigDecimal(jsObject.optString("itemCvalue" + i));
					itemCvalueYear = itemCvalueYear.add(itemCvalue);
				}

				BigDecimal itemCInProfit = CapMath.getBigDecimal(jsObject.optString("itemCInProfit"));
				BigDecimal itemCHolding = CapMath.getBigDecimal(jsObject.optString("itemCHolding"));

				itemCvalueYear = itemCvalueYear
						.multiply(itemCInProfit)
						.divide(BigDecimal.valueOf(100), MathContext.DECIMAL64)
						.multiply(itemCHolding)
						.divide(BigDecimal.valueOf(100), MathContext.DECIMAL64).setScale(0, BigDecimal.ROUND_HALF_UP);

				s01ws.add(getNewC101s01w(s01b, "itemCvalueYear", itemCvalueYear.toString()));

				yearIncome = yearIncome.add(itemCvalueYear);
			} else if ("D".equals(loopItem) && "Y".equals(hasItem)) {
				//現金收入
				BigDecimal itemDvalueYear = BigDecimal.ZERO;

				for (int i = 1; i <= 12; i++) {
					BigDecimal itemDvalue = CapMath.getBigDecimal(jsObject.optString("itemDvalue" + i));
					itemDvalueYear = itemDvalueYear.add(itemDvalue);
				}
				BigDecimal itemDDisRate = CapMath.getBigDecimal(jsObject.optString("itemDDisRate"));

				//=D加總×折算率%
				itemDvalueYear = itemDvalueYear.multiply(itemDDisRate)
						.divide(BigDecimal.valueOf(100), MathContext.DECIMAL64).setScale(0, BigDecimal.ROUND_HALF_UP);

				s01ws.add(getNewC101s01w(s01b, "itemDvalueYear", itemDvalueYear.toString()));
				yearIncome = yearIncome.add(itemDvalueYear);
			} else if ("E".equals(loopItem) && "Y".equals(hasItem)) {
				//定存利息
				BigDecimal itemEvalueYear = BigDecimal.ZERO;
				//=E加總
				for (int i = 1; i <= 12; i++) {
					BigDecimal itemEvalue = CapMath.getBigDecimal(jsObject.optString("itemEvalue" + i));
					itemEvalueYear = itemEvalueYear.add(itemEvalue);
				}
				itemEvalueYear = itemEvalueYear.setScale(0, BigDecimal.ROUND_HALF_UP);
				s01ws.add(getNewC101s01w(s01b, "itemEvalueYear", itemEvalueYear.toString()));
				yearIncome = yearIncome.add(itemEvalueYear);
			} else if ("F".equals(loopItem) && "Y".equals(hasItem)) {
				//租金收入
				BigDecimal itemFvalueYear = BigDecimal.ZERO;
				//=F加總×80%
				for (int i = 1; i <= 12; i++) {
					BigDecimal itemFvalue = CapMath.getBigDecimal(jsObject.optString("itemFvalue" + i));
					itemFvalueYear = itemFvalueYear.add(itemFvalue);
				}
				itemFvalueYear = itemFvalueYear.multiply(BigDecimal.valueOf(80))
						.divide(BigDecimal.valueOf(100), MathContext.DECIMAL64).setScale(0, BigDecimal.ROUND_HALF_UP);
				s01ws.add(getNewC101s01w(s01b, "itemFvalueYear", itemFvalueYear.toString()));

				yearIncome = yearIncome.add(itemFvalueYear);
			} else if ("G".equals(loopItem) && "Y".equals(hasItem)) {
				//退休/退撫/退役收入
				BigDecimal itemGvalueYear = BigDecimal.ZERO;
				//=G加總
				for (int i = 1; i <= 12; i++) {
					BigDecimal itemGvalue = CapMath.getBigDecimal(jsObject.optString("itemGvalue" + i));
					itemGvalueYear = itemGvalueYear.add(itemGvalue);
				}
				itemGvalueYear = itemGvalueYear.setScale(0, BigDecimal.ROUND_HALF_UP);
				s01ws.add(getNewC101s01w(s01b, "itemGvalueYear", itemGvalueYear.toString()));

				yearIncome = yearIncome.add(itemGvalueYear);
			} else if ("H".equals(loopItem) && "Y".equals(hasItem)) {
				//其他
				BigDecimal itemHvalueYear = BigDecimal.ZERO;
				//=H加總×折算率%
				for (int i = 1; i <= 12; i++) {
					BigDecimal itemHvalue = CapMath.getBigDecimal(jsObject.optString("itemHvalue" + i));
					itemHvalueYear = itemHvalueYear.add(itemHvalue);
				}
				BigDecimal itemHDisRate = CapMath.getBigDecimal(jsObject.optString("itemHDisRate"));

				itemHvalueYear = itemHvalueYear
						.multiply(itemHDisRate)
						.divide(BigDecimal.valueOf(100), MathContext.DECIMAL64).setScale(0, BigDecimal.ROUND_HALF_UP);

				s01ws.add(getNewC101s01w(s01b, "itemHvalueYear", itemHvalueYear.toString()));

				otherIncome = otherIncome.add(itemHvalueYear);
			}
		}

		//pAllAmt 年收入不轉成萬元的，給計算負債比用
		s01ws.add(getNewC101s01w(s01b, "pAllAmt", yearIncome.add(otherIncome).toString()));
		s01ws.add(getNewC101s01w(s01b, "pYearIncome", yearIncome.toString()));
		s01ws.add(getNewC101s01w(s01b, "pOtherIncome", otherIncome.toString()));

		//這邊是存成萬元
		s01b.setPayAmt(yearIncome.divide(BigDecimal.valueOf(10000), 0, BigDecimal.ROUND_DOWN));
		s01b.setOthAmt(otherIncome.divide(BigDecimal.valueOf(10000), 0, BigDecimal.ROUND_DOWN));

		C101S01C c101s01c = this.findModelByKey(C101S01C.class, s01b.getMainId(), s01b.getCustId(), s01b.getDupNo());

		if (c101s01c != null && s01b.getPayAmt() != null && s01b.getOthAmt() != null) {
			if (!s01b.getPayAmt().equals(preYearIncome) || !s01b.getOthAmt().equals(preOtherIncome)) {
				c101s01c.setReCalFlg("N");
			}
		}

		List<GenericBean> c101s01ws = (List<GenericBean>) this.findListByMainId(C101S01W.class, s01b.getMainId());
		if (CollectionUtils.isNotEmpty(c101s01ws)) {
			this.delete(c101s01ws);
		}
		this.save(s01ws);
		this.save(s01b, c101s01c);
	}

	@Override
	public void calPersonalIncomeItem(C101S01W s01w) {
		String formulaType = Util.trim(s01w.getFormulaType());
		if ("1".equals(formulaType)) {
			BigDecimal value01 = CapMath.bigDecimalIsNullToZero(s01w.getValue01());
			s01w.setValueYear(value01);
		} else if ("2".equals(formulaType)) {
			//(AAA加總)÷3×12
			BigDecimal value01 = CapMath.bigDecimalIsNullToZero(s01w.getValue01());
			BigDecimal value02 = CapMath.bigDecimalIsNullToZero(s01w.getValue02());
			BigDecimal value03 = CapMath.bigDecimalIsNullToZero(s01w.getValue03());
			BigDecimal valueYear = value01.add(value02).add(value03).multiply(BigDecimal.valueOf(4));
			s01w.setValueYear(valueYear);
		} else if ("3".equals(formulaType)) {
			//A×12
			BigDecimal value01 = CapMath.bigDecimalIsNullToZero(s01w.getValue01());
			BigDecimal valueYear = value01.multiply(BigDecimal.valueOf(12));
			s01w.setValueYear(valueYear);
		} else if ("4".equals(formulaType)) {
			//(AAAAAA加總)÷6×12
			BigDecimal value01 = CapMath.bigDecimalIsNullToZero(s01w.getValue01());
			BigDecimal value02 = CapMath.bigDecimalIsNullToZero(s01w.getValue02());
			BigDecimal value03 = CapMath.bigDecimalIsNullToZero(s01w.getValue03());
			BigDecimal value04 = CapMath.bigDecimalIsNullToZero(s01w.getValue04());
			BigDecimal value05 = CapMath.bigDecimalIsNullToZero(s01w.getValue05());
			BigDecimal value06 = CapMath.bigDecimalIsNullToZero(s01w.getValue06());
			BigDecimal valueYear = value01.add(value02).add(value03).add(value04).add(value05).add(value06).multiply(BigDecimal.valueOf(2));
			s01w.setValueYear(valueYear);
		} else if ("5".equals(formulaType)) {
			//(AAA加總)÷6×B%×C%×12
			BigDecimal value01 = CapMath.bigDecimalIsNullToZero(s01w.getValue01());
			BigDecimal value02 = CapMath.bigDecimalIsNullToZero(s01w.getValue02());
			BigDecimal value03 = CapMath.bigDecimalIsNullToZero(s01w.getValue03());
			BigDecimal inProfit = CapMath.bigDecimalIsNullToZero(s01w.getInProfit());
			BigDecimal holding = CapMath.bigDecimalIsNullToZero(s01w.getHolding());
			BigDecimal valueYear = value01.add(value02).add(value03).multiply(BigDecimal.valueOf(2)).
					multiply(inProfit).multiply(holding).divide(BigDecimal.valueOf(10000), 0, RoundingMode.HALF_UP);
			s01w.setValueYear(valueYear);
		} else if ("6".equals(formulaType)) {
			//(AAAAAA加總)÷6×B%×C%×12
			BigDecimal value01 = CapMath.bigDecimalIsNullToZero(s01w.getValue01());
			BigDecimal value02 = CapMath.bigDecimalIsNullToZero(s01w.getValue02());
			BigDecimal value03 = CapMath.bigDecimalIsNullToZero(s01w.getValue03());
			BigDecimal value04 = CapMath.bigDecimalIsNullToZero(s01w.getValue04());
			BigDecimal value05 = CapMath.bigDecimalIsNullToZero(s01w.getValue05());
			BigDecimal value06 = CapMath.bigDecimalIsNullToZero(s01w.getValue06());
			BigDecimal inProfit = CapMath.bigDecimalIsNullToZero(s01w.getInProfit());
			BigDecimal holding = CapMath.bigDecimalIsNullToZero(s01w.getHolding());
			BigDecimal valueYear = value01.add(value02).add(value03).add(value04).add(value05).add(value06).multiply(BigDecimal.valueOf(2)).
					multiply(inProfit).multiply(holding).divide(BigDecimal.valueOf(10000), 0, RoundingMode.HALF_UP);
			s01w.setValueYear(valueYear);
		} else if ("7".equals(formulaType)) {
			//A×B%
			BigDecimal value01 = CapMath.bigDecimalIsNullToZero(s01w.getValue01());
			BigDecimal disRate = CapMath.bigDecimalIsNullToZero(s01w.getDisRate());
			BigDecimal valueYear = value01.multiply(disRate).divide(BigDecimal.valueOf(100), 0, RoundingMode.HALF_UP);
			s01w.setValueYear(valueYear);
		} else if ("8".equals(formulaType)) {
			//(AAA加總)÷3×12 +常態性獎金
			BigDecimal value01 = CapMath.bigDecimalIsNullToZero(s01w.getValue01());
			BigDecimal value02 = CapMath.bigDecimalIsNullToZero(s01w.getValue02());
			BigDecimal value03 = CapMath.bigDecimalIsNullToZero(s01w.getValue03());
			BigDecimal bonus = CapMath.bigDecimalIsNullToZero(s01w.getBonus());
			BigDecimal valueYear = value01.add(value02).add(value03).multiply(BigDecimal.valueOf(4)).add(bonus);
			s01w.setValueYear(valueYear);
		} else if ("9".equals(formulaType)) {
			//(AAAAAA加總)÷6×12 +常態性獎金
			BigDecimal value01 = CapMath.bigDecimalIsNullToZero(s01w.getValue01());
			BigDecimal value02 = CapMath.bigDecimalIsNullToZero(s01w.getValue02());
			BigDecimal value03 = CapMath.bigDecimalIsNullToZero(s01w.getValue03());
			BigDecimal value04 = CapMath.bigDecimalIsNullToZero(s01w.getValue04());
			BigDecimal value05 = CapMath.bigDecimalIsNullToZero(s01w.getValue05());
			BigDecimal value06 = CapMath.bigDecimalIsNullToZero(s01w.getValue06());
			BigDecimal bonus = CapMath.bigDecimalIsNullToZero(s01w.getBonus());
			BigDecimal valueYear = value01.add(value02).add(value03).add(value04).add(value05).add(value06).multiply(BigDecimal.valueOf(2)).add(bonus);
			s01w.setValueYear(valueYear);
		} else if ("10".equals(formulaType)) {
			//(AAAAAAAAAAAA加總)+常態性獎金
			BigDecimal value01 = CapMath.bigDecimalIsNullToZero(s01w.getValue01());
			BigDecimal value02 = CapMath.bigDecimalIsNullToZero(s01w.getValue02());
			BigDecimal value03 = CapMath.bigDecimalIsNullToZero(s01w.getValue03());
			BigDecimal value04 = CapMath.bigDecimalIsNullToZero(s01w.getValue04());
			BigDecimal value05 = CapMath.bigDecimalIsNullToZero(s01w.getValue05());
			BigDecimal value06 = CapMath.bigDecimalIsNullToZero(s01w.getValue06());
			BigDecimal value07 = CapMath.bigDecimalIsNullToZero(s01w.getValue07());
			BigDecimal value08 = CapMath.bigDecimalIsNullToZero(s01w.getValue08());
			BigDecimal value09 = CapMath.bigDecimalIsNullToZero(s01w.getValue09());
			BigDecimal value10 = CapMath.bigDecimalIsNullToZero(s01w.getValue10());
			BigDecimal value11 = CapMath.bigDecimalIsNullToZero(s01w.getValue11());
			BigDecimal value12 = CapMath.bigDecimalIsNullToZero(s01w.getValue12());
			BigDecimal bonus = CapMath.bigDecimalIsNullToZero(s01w.getBonus());
			BigDecimal valueYear = value01.add(value02).add(value03).add(value04).add(value05).add(value06).add(value07).add(value08).add(value09).add(value10).add(value11).add(value12).add(bonus);
			s01w.setValueYear(valueYear);
		} else if ("11".equals(formulaType)) {
			//(AAAAAA加總)×B%×C%
			BigDecimal value01 = CapMath.bigDecimalIsNullToZero(s01w.getValue01());
			BigDecimal value02 = CapMath.bigDecimalIsNullToZero(s01w.getValue02());
			BigDecimal value03 = CapMath.bigDecimalIsNullToZero(s01w.getValue03());
			BigDecimal value04 = CapMath.bigDecimalIsNullToZero(s01w.getValue04());
			BigDecimal value05 = CapMath.bigDecimalIsNullToZero(s01w.getValue05());
			BigDecimal value06 = CapMath.bigDecimalIsNullToZero(s01w.getValue06());
			BigDecimal inProfit = CapMath.bigDecimalIsNullToZero(s01w.getInProfit());
			BigDecimal holding = CapMath.bigDecimalIsNullToZero(s01w.getHolding());
			BigDecimal valueYear = value01.add(value02).add(value03).add(value04).add(value05).add(value06).
					multiply(inProfit).multiply(holding).divide(BigDecimal.valueOf(10000), 0, RoundingMode.HALF_UP);
			s01w.setValueYear(valueYear);
		} else if ("12".equals(formulaType)) {
			//(AAAAAAAAAAAA加總)×B%×C%
			BigDecimal value01 = CapMath.bigDecimalIsNullToZero(s01w.getValue01());
			BigDecimal value02 = CapMath.bigDecimalIsNullToZero(s01w.getValue02());
			BigDecimal value03 = CapMath.bigDecimalIsNullToZero(s01w.getValue03());
			BigDecimal value04 = CapMath.bigDecimalIsNullToZero(s01w.getValue04());
			BigDecimal value05 = CapMath.bigDecimalIsNullToZero(s01w.getValue05());
			BigDecimal value06 = CapMath.bigDecimalIsNullToZero(s01w.getValue06());
			BigDecimal value07 = CapMath.bigDecimalIsNullToZero(s01w.getValue07());
			BigDecimal value08 = CapMath.bigDecimalIsNullToZero(s01w.getValue08());
			BigDecimal value09 = CapMath.bigDecimalIsNullToZero(s01w.getValue09());
			BigDecimal value10 = CapMath.bigDecimalIsNullToZero(s01w.getValue10());
			BigDecimal value11 = CapMath.bigDecimalIsNullToZero(s01w.getValue11());
			BigDecimal value12 = CapMath.bigDecimalIsNullToZero(s01w.getValue12());
			BigDecimal inProfit = CapMath.bigDecimalIsNullToZero(s01w.getInProfit());
			BigDecimal holding = CapMath.bigDecimalIsNullToZero(s01w.getHolding());
			BigDecimal valueYear = value01.add(value02).add(value03).add(value04).add(value05).add(value06).add(value07).add(value08).add(value09).add(value10).add(value11).add(value12).
					multiply(inProfit).multiply(holding).divide(BigDecimal.valueOf(10000), 0, RoundingMode.HALF_UP);
			s01w.setValueYear(valueYear);
		} else if ("13".equals(formulaType)) {
			//(AAAAAAAAAAAA加總)
			BigDecimal value01 = CapMath.bigDecimalIsNullToZero(s01w.getValue01());
			BigDecimal value02 = CapMath.bigDecimalIsNullToZero(s01w.getValue02());
			BigDecimal value03 = CapMath.bigDecimalIsNullToZero(s01w.getValue03());
			BigDecimal value04 = CapMath.bigDecimalIsNullToZero(s01w.getValue04());
			BigDecimal value05 = CapMath.bigDecimalIsNullToZero(s01w.getValue05());
			BigDecimal value06 = CapMath.bigDecimalIsNullToZero(s01w.getValue06());
			BigDecimal value07 = CapMath.bigDecimalIsNullToZero(s01w.getValue07());
			BigDecimal value08 = CapMath.bigDecimalIsNullToZero(s01w.getValue08());
			BigDecimal value09 = CapMath.bigDecimalIsNullToZero(s01w.getValue09());
			BigDecimal value10 = CapMath.bigDecimalIsNullToZero(s01w.getValue10());
			BigDecimal value11 = CapMath.bigDecimalIsNullToZero(s01w.getValue11());
			BigDecimal value12 = CapMath.bigDecimalIsNullToZero(s01w.getValue12());
			BigDecimal valueYear = value01.add(value02).add(value03).add(value04).add(value05).add(value06).add(value07).add(value08).add(value09).add(value10).add(value11).add(value12);
			s01w.setValueYear(valueYear);
		}
		c101s01wDao.save(s01w);
	}

	@Override
	public void calPersonalIncomeDetailV2(C101S01B s01b) throws CapException {

		//負債比是否重新計算
		BigDecimal preYearIncome = s01b.getPayAmt();
		BigDecimal preOtherIncome = s01b.getOthAmt();

		String positionType = s01b.getPositionType();

		List<C101S01W> c101s01ws = c101s01wDao.findByList(s01b.getMainId(), s01b.getCustId(), s01b.getDupNo());
		boolean check = false;
		for (C101S01W c101s01w : c101s01ws) {
			String incomeItem = c101s01w.getIncomeItem();
			if ("3".equals(positionType)) {
				if ("A06".equals(incomeItem)) {
					check = true;
					c101s01wDao.delete(c101s01w);
				}
			} else {
				if ("A07".equals(incomeItem)) {
					check = true;
					c101s01wDao.delete(c101s01w);
				}
			}
		}
		if (check) {
			c101s01ws = c101s01wDao.findByList(s01b.getMainId(), s01b.getCustId(), s01b.getDupNo());
		}

		BigDecimal yearIncome = BigDecimal.ZERO;
		BigDecimal otherIncome = BigDecimal.ZERO;
		for (C101S01W c101s01w : c101s01ws) {
			BigDecimal valueYear = CapMath.bigDecimalIsNullToZero(c101s01w.getValueYear());
			String incomeType = c101s01w.getIncomeType();
			if ("A".equals(incomeType)) {
				yearIncome = yearIncome.add(valueYear);
			} else if ("B".equals(incomeType)) {
				otherIncome = otherIncome.add(valueYear);
			}
		}
		s01b.setPayAmt(yearIncome.divide(BigDecimal.valueOf(10000), 0, RoundingMode.DOWN));
		s01b.setOthAmt(otherIncome.divide(BigDecimal.valueOf(10000), 0, RoundingMode.DOWN));

		C101S01C c101s01c = this.findModelByKey(C101S01C.class, s01b.getMainId(), s01b.getCustId(), s01b.getDupNo());

		if (c101s01c != null && s01b.getPayAmt() != null && s01b.getOthAmt() != null) {
			if (!s01b.getPayAmt().equals(preYearIncome) || !s01b.getOthAmt().equals(preOtherIncome)) {
				c101s01c.setReCalFlg("N");
			}
		}
		this.save(s01b, c101s01c);

	}

	/**
	 * RPA地政士查詢
	 */
	@SuppressWarnings("unchecked")
	@Override
	public void queryRpaQueryLaaName(String mainId, String queryLaaName) throws CapMessageException {
		String token = "";
		Map<String, Object> objResult = new LinkedHashMap<String, Object>();
		C101M01A c101m01a = clsService.findC101M01A_mainId(mainId);
		
		//明細檔發查
		try {
			//Step 1 取得 token
			JSONObject resultJson = null;
			try {
				resultJson = rpaservice.getRPAAccessToken(objResult);
				token = resultJson != null ? resultJson.getString("result") : "";
				token = "Bearer " + token;
				if (CapString.isEmpty(token)) {
					throw new CapMessageException("getRPAAccessToken失敗，請稍後再試。", this.getClass());
				}
			} catch (Exception e){
				throw new CapMessageException("取得RPA Token失敗，請稍後再試。", this.getClass());
			}

			//Step 發查RPA
			executeRPAJobsForLaaName(c101m01a.getMainId(), queryLaaName, token);

		} catch (CapException e) {
			throw new CapMessageException(e.getMessage(), this.getClass());
		}
		
	}
	
	/**
	 * RPA查詢地政士
	 * @param mainId
	 * @param queryLaaName
	 * @param token
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	private String executeRPAJobsForLaaName(String mainId, String queryLaaName, String token)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String errorMsg = "", processKey = "";
		Map<String, Object> objResult = new LinkedHashMap<String, Object>();
		JSONObject resultJson = null;
		C101M01A c101m01a = clsService.findC101M01A_mainId(mainId);
		
		try {
			// STEP2 啟動JOB-RPA地政士查詢
			objResult = new LinkedHashMap<String, Object>();
			try {
				logger.info("啟動JOB-RPA地政士查詢 開始========================");


				L161S01D l161s01d = new L161S01D();
				l161s01d.setMainId(mainId);
				l161s01d.setType(UtilConstants.RPA.TYPE.地政士);
				l161s01d.setStatus(UtilConstants.RPA.STATUS.查詢中); // 查詢中
				l161s01d.setQueryTime(CapDate.getCurrentTimestamp());
				// 查詢條件一 地政士
				l161s01d.setRpaQueryReason1(queryLaaName);
				save(l161s01d);
				l161s01dDao.flush();

				// 設定要傳遞的參數 LinkedHashMap 才可以按順序取出
				objResult
						.put("responseURL",
								sysparamService
										.getParamValue(SysParamConstants.RPA_GW_RESPONSE_URL_LMS)); // 回傳位址
																									// SIT
																									// :
																									// "http://192.168.53.85:9081/lms-web/app/schedulerRPA"
				objResult.put("system", "eloan");
				objResult.put("branchNo", user.getUnitNo());
				objResult.put(
						"empNo",
						CapString.isNumeric(user.getUserId()) ? String.format(
								"%06d", Integer.valueOf(user.getUserId()))
								: user.getUserId());
				objResult.put("uniqueID", l161s01d.getMainId() + "|" + l161s01d.getRpaQueryReason1());
				// 查詢條件
				objResult.put("agentName", l161s01d.getRpaQueryReason1());
				objResult.put("custId", c101m01a.getCustId());
				
				logger.info("傳入參數==>[{}]", objResult.toString());

				resultJson = rpaservice.StartRPAJobForLMS(objResult, token,
						processKey, SysParamConstants.RPA_地政士查詢);

				logger.info("啟動JOB-RPA地政士查詢 結束========================");
			} catch (Exception e) {
				errorMsg = "RPA Job建立失敗，請稍後再試。";
				throw new CapMessageException("RPA Job建立失敗，請稍後再試。",
						this.getClass());
			}
		} catch (Exception e) {
			throw new CapMessageException(errorMsg, this.getClass());
		}

		return errorMsg;
	}

	@SuppressWarnings("unchecked")
	@Override
	public CapAjaxFormResult importRpaDetail(String mainId, String oid) {
		CapAjaxFormResult result = new CapAjaxFormResult();
		C101M01A c101m01a = clsService.findC101M01A_mainId(mainId);
		String custId = c101m01a.getCustId();
		String dupNo = c101m01a.getDupNo();
		C101S01E c101s01e = clsService.findC101S01E(c101m01a);
		L161S01D l161s01d = this.findModelByOid(L161S01D.class, oid);
		
		if (c101s01e == null || !l161s01d.getStatus().equals(UtilConstants.RPA.STATUS.查詢完成)){
			return result;
		}
		if (l161s01d.getType().equals(UtilConstants.RPA.TYPE.地政士)) {
			//匯入相關欄位data
			JSONObject req = JSONObject.fromObject(l161s01d.getData());
			
			JSONArray agentAry =  req.getJSONArray("agentInfo");
			
			result.set("size", agentAry.size());
			if (agentAry.size() == 1) {
				
				String agentName = agentAry.getJSONObject(0).optString("agentName", "");
				String agentCertYear = agentAry.getJSONObject(0).optString("agentCertYear", "");
				String agentCertWord = agentAry.getJSONObject(0).optString("agentCertWord", "");
				String agentCertNo = agentAry.getJSONObject(0).optString("agentCertNo", "");
				String landOffice = agentAry.getJSONObject(0).optString("landOffice", "");
				String license = agentAry.getJSONObject(0).optString("license", "");
				
				result.set("laaName", agentName);
				result.set("laaYear", agentCertYear);
				result.set("laaWord", agentCertWord);
				result.set("laaNo", agentCertNo);
				result.set("laaOffice", landOffice);

				if (c101s01e != null) {
//					c101s01e.setLaaName(agentName);
//					c101s01e.setLaaYear(agentCertYear);
//					c101s01e.setLaaWord(agentCertWord);
//					c101s01e.setLaaNo(agentCertNo);
//					c101s01e.setLaaOffice(landOffice);
//					this.save(c101s01e);
				}
				C101S01Y c101s01y = c101s01yDao.findLaaByList(mainId, custId, dupNo, agentCertYear, agentCertWord, agentCertNo);
				if(c101s01y==null){
					c101s01y = new C101S01Y();
					c101s01y.setMainId(mainId);
					c101s01y.setLaaName(agentName);
					c101s01y.setLaaYear(agentCertYear);
					c101s01y.setLaaWord(agentCertWord);
					c101s01y.setLaaNo(agentCertNo);
					c101s01y.setLaaOffice(landOffice);
				}else{
					c101s01y.setLaaName(agentName);
					c101s01y.setLaaYear(agentCertYear);
					c101s01y.setLaaWord(agentCertWord);
					c101s01y.setLaaNo(agentCertNo);
					c101s01y.setLaaOffice(landOffice);
				}
				c101s01y.setCustId(custId);
				c101s01y.setDupNo(dupNo);
				Map<String, Object> c900m01h = clsService
					.findActiveMajorC900M01HByCertNo2(agentCertYear, agentCertWord, agentCertNo);
				String laaCtlFlag = "0";
				String laaMatchRuleFlag = "";
				if (c900m01h != null) {
					laaCtlFlag = MapUtils.getString(c900m01h, "CTLFLAG");
				}
				//J-109-0251_10702_B1001 Web e-Loan 授信管理系統調整地政士懲戒紀錄選項判斷邏輯
				if(clsService.checkLaaMatchRuleFlag(c900m01h)){
					laaMatchRuleFlag = UtilConstants.DEFAULT.是;
				}
				c101s01y.setLaaCtlFlag(laaCtlFlag);
				c101s01y.setLaaMatchRuleFlag(laaMatchRuleFlag);
				c101s01y.setLaaQueryDate(new Date());
				this.save(c101s01y);
			} else {
				result.set("agentAry", agentAry.toString());
			}
		}
		return result;
	}

	@Override
	public CapAjaxFormResult importRpaDetailByAgent(String mainId, String oid,
			String laaName, String laaYear, String laaWord, String laaNo,
			String laaOffice) {
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		C101M01A c101m01a = clsService.findC101M01A_mainId(mainId);
		String custId = c101m01a.getCustId();
		String dupNo = c101m01a.getDupNo();
		
		L161S01D l161s01d = this.findModelByOid(L161S01D.class, oid);
		
		if (!l161s01d.getStatus().equals(UtilConstants.RPA.STATUS.查詢完成)){
			return result;
		}
		
		if (l161s01d.getType().equals(UtilConstants.RPA.TYPE.地政士)) {
			result.set("laaName", laaName);
			result.set("laaYear", laaYear);
			result.set("laaWord", laaWord);
			result.set("laaNo", laaNo);
			result.set("laaOffice", laaOffice);
			
			C101S01E c101s01e = clsService.findC101S01E(c101m01a);
			if (c101s01e != null) {
//				c101s01e.setLaaName(laaName);
//				c101s01e.setLaaYear(laaYear);
//				c101s01e.setLaaWord(laaWord);
//				c101s01e.setLaaNo(laaNo);
//				c101s01e.setLaaOffice(laaOffice);
				save(c101s01e);
			}
			C101S01Y c101s01y = c101s01yDao.findLaaByList(mainId, custId, dupNo, laaYear, laaWord, laaNo);
			if (c101s01y==null) {
				c101s01y = new C101S01Y();
				c101s01y.setLaaName(laaName);
				c101s01y.setLaaYear(laaYear);
				c101s01y.setLaaWord(laaWord);
				c101s01y.setLaaNo(laaNo);
				c101s01y.setLaaOffice(laaOffice);
				c101s01y.setMainId(mainId);
			} else {
				c101s01y.setLaaName(laaName);
				c101s01y.setLaaYear(laaYear);
				c101s01y.setLaaWord(laaWord);
				c101s01y.setLaaNo(laaNo);
				c101s01y.setLaaOffice(laaOffice);
			}
			c101s01y.setCustId(custId);
			c101s01y.setDupNo(dupNo);
			Map<String, Object> c900m01h = clsService
				.findActiveMajorC900M01HByCertNo2(laaYear, laaWord, laaNo);
			String laaCtlFlag = "0";
			String laaMatchRuleFlag = "";
			if (c900m01h != null) {
				laaCtlFlag = MapUtils.getString(c900m01h, "CTLFLAG");
			}
			//J-109-0251_10702_B1001 Web e-Loan 授信管理系統調整地政士懲戒紀錄選項判斷邏輯
			if(clsService.checkLaaMatchRuleFlag(c900m01h)){
				laaMatchRuleFlag = UtilConstants.DEFAULT.是;
			}
			c101s01y.setLaaCtlFlag(laaCtlFlag);
			c101s01y.setLaaMatchRuleFlag(laaMatchRuleFlag);
			c101s01y.setLaaQueryDate(new Date());
			this.save(c101s01y);
		}
		
		return result;
	}
	
	/**
	 * 受監護輔助宣告查詢
	 * 
	 * @param c101m01a_mainId
	 * @return
	 * @throws CapException
	 */
	@Override
	public String gotoRPAJobs_04(String c101m01a_mainId, String custId, String dupNo, String token, String functionCode) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String errorMsg = "", processKey = "";
		Map<String, Object> objResult = new LinkedHashMap<String, Object>();
		JSONObject resultJson = null;

		try {
			// STEP2 啟動JOB-受監護輔助宣告查詢
			objResult = new LinkedHashMap<String, Object>();
			try {
				logger.info("啟動JOB-受監護輔助宣告查詢 開始======================== Function Code: " + functionCode);

				L161S01D l161s01d = new L161S01D();
				l161s01d.setMainId(c101m01a_mainId);
				l161s01d.setType(UtilConstants.RPA.TYPE.受監護輔助宣告查詢);
				l161s01d.setStatus(UtilConstants.RPA.STATUS.查詢中); // 查詢中
				l161s01d.setQueryTime(CapDate.getCurrentTimestamp());

				l161s01d.setRpaQueryReason1(custId);// 查詢條件一
				l161s01d.setRpaQueryReason2(dupNo);
				l161s01d.setRpaQueryReason9(functionCode);
				this.l161s01dDao.save(l161s01d);
				l161s01dDao.flush();

				// 設定要傳遞的參數 LinkedHashMap 才可以按順序取出
				objResult.put("responseURL",
								sysparamService.getParamValue(SysParamConstants.RPA_GW_RESPONSE_URL_LMS)); 
								// 回傳位址
								// SIT
								// :
								// "http://192.168.53.85:9081/lms-web/app/schedulerRPA"
				objResult.put("system", "eloan");
				
				objResult.put("uniqueID",
								l161s01d.getMainId() + "|" + l161s01d.getRpaQueryReason1() + "|" + functionCode + "|" + dupNo);
				
				objResult.put("branchNo", user.getUnitNo());
				
				objResult.put("empNo", 
							CapString.isNumeric(user.getUserId()) ? String.format("%06d", Integer.valueOf(user.getUserId())) : user.getUserId());
				// 查詢條件
				objResult.put("data_CustomerNo", l161s01d.getRpaQueryReason1()); // 負責人ID

				logger.info("傳入參數==>[{}]", objResult.toString());

				resultJson = rpaservice.StartRPAJobForLMS(objResult, token,
						processKey, SysParamConstants.RPA_受監護輔助宣告);

				logger.info("啟動JOB-稅籍登記資料公示查詢 結束========================");
			} catch (Exception e) {
				errorMsg = "RPA Job建立失敗，請稍後再試。";
				throw new CapMessageException("RPA Job建立失敗，請稍後再試。",
						this.getClass());
			}
		} catch (Exception e) {
			throw new CapMessageException(errorMsg, this.getClass());
		}

		return errorMsg;
	}
	
	/**
	 * 建立JSON
	 * 
	 * @param req
	 * @return
	 * @throws Exception
	 */
	public byte[] generateJSONObjectForIdCardCheck(IdentificationCheckGwReqMessage req, Calendar currentDateTime) throws Exception {

		Map<String, String> rptVariableMap = null;
		JSONObject json = new JSONObject();

		try {
			String checkIdCardApply = req.getCheckIdCardApply();
			String id = req.getPersonId();
			String applyYYYMMDD = req.getApplyYYYMMDD();
			String queryDateTime = CapDate.convertDateToTaiwanYear(String.valueOf(currentDateTime.get(Calendar.YEAR))) + "年" 
									  + (currentDateTime.get(Calendar.MONTH)+1) + "月" 
									  + currentDateTime.get(Calendar.DAY_OF_MONTH) + "日" 
									  + currentDateTime.get(Calendar.HOUR_OF_DAY) + "時" 
									  + currentDateTime.get(Calendar.MINUTE) + "分" 
									  + currentDateTime.get(Calendar.SECOND) + "秒";
			
			String issuingDate = "已輸入";
			String issuingPlace = "已輸入";
			String applyCodeName = "已輸入";
			if(!"1".equals(checkIdCardApply)){
				issuingDate = "民國" + applyYYYMMDD.substring(0, 3) + "年" + applyYYYMMDD.substring(3, 5) + "月" + applyYYYMMDD.substring(5, 7) + "日";
				CodeType codeType = this.codeTypeService.findByTypeAndDesc2("C101S01A_idCard_siteId_for_API", req.getIssueSiteId());
				issuingPlace = codeType != null ? codeType.getCodeDesc() : "";
				applyCodeName = LMSUtil.getApplyCodeMap().get(req.getApplyCode());
			}
			
			String resultMsg = LMSUtil.getIdCardCheckReturnMessage(id, checkIdCardApply);
			
			rptVariableMap = new LinkedHashMap<String, String>();
			rptVariableMap.put("checkIdCardApply", checkIdCardApply);
			rptVariableMap.put("queryDateTime", queryDateTime);
			rptVariableMap.put("id", id);
			rptVariableMap.put("issuingDate", issuingDate);
			rptVariableMap.put("issuingPlace", issuingPlace);
			rptVariableMap.put("applyCodeName", applyCodeName);
			rptVariableMap.put("resultMsg", resultMsg);
			
			json.putAll(rptVariableMap);

		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
		}
		return json.toString().getBytes("utf-8");

	}
	
	@Override
	public void processIsApprovedFlagAndFlowFlag(boolean isApproved, C101S01X c101s01x){
		
		String flowFlag = null;
		String isApprovedFlag = null;
		
		if(!isApproved){//未通過
			isApprovedFlag = "N";
			flowFlag = "";
		}
		
		if(isApproved){//已核可
			
			int totalScore = c101s01x.getTotalScore();
			int E = c101s01x.getNotAllowE();
			boolean isFlowC = true;
			
			isApprovedFlag = "Y";
			
			if(totalScore < 60){
				isApprovedFlag = "";
				flowFlag = "A";//授權內
				isFlowC = false;
			}
			
			String isBadRecord = c101s01x.getIsOtherBadRecord() + c101s01x.getIsIdChangedMark() + c101s01x.getIsOtherAddMark() 
									+ c101s01x.getIsCaseNotifiedMark() + c101s01x.getIsBam210Data() + c101s01x.getIsBas001Record();
			
			if(isBadRecord.contains("Y")){
				flowFlag = "A";//授權內
				isFlowC = false;
			}
			
			if(E == 1){
				flowFlag = "B";//授權外
				isFlowC = false;
			}
			
			if(isFlowC){
				flowFlag = "C";//一般流程
			}
		}
		
		c101s01x.setIsApproved(isApprovedFlag);
		c101s01x.setFlowFlag(flowFlag);
	}
	
	@Override
	public boolean checkIsApprovedForLabourBailout(C101S01X c101s01x, boolean isManualReview){

		int totalScore = c101s01x.getTotalScore();
		int A = c101s01x.getNotAllowA();
		int B = c101s01x.getNotAllowB();
		int C = c101s01x.getNotAllowC();
		int D = c101s01x.getNotAllowD();
		int E = c101s01x.getNotAllowE();
		String isForcedStopRecord = c101s01x.getIsForcedStopRecord();
		String isPayInterestNormal = c101s01x.getIsEAttachedPass();
		String isOtherAssets = c101s01x.getIsOtherAssets();
		
		if((A+B+C+D) > 0){
			return false;
		}
		
		if("Y".equals(isForcedStopRecord) && "N".equals(isPayInterestNormal)){
			return false;
		}
		
		//2000年申請過勞工紓困資料
		String applyTs = this.sysparamService.getParamValue("APPLY_DATE_LABOUR_BAILOUT4");
		Map<String, Object> labourBailout2000Data = this.misLNF030Service.findLabourBailoutDataByCustIdAndLoanDate(c101s01x.getCustId(), c101s01x.getDupNo(), applyTs);
		
		if(E == 1 && ("N".equals(c101s01x.getIsPtaFlag()) || labourBailout2000Data == null)){//非薪轉戶or不為2000年勞工紓困戶
			return false;
		}
		
		if(totalScore < 60 && !"Y".equals(isOtherAssets)){
			return false;
		}
		
		if(isManualReview && totalScore < 60){
			return false;
		}
		
		return true;
	}
	
	@Override
	public void processEjcicAndEtchDataForC101S01X(String c101m01a_mainId, String custId, String dupNo, String queryBranch, C101S01X c101s01x) throws CapMessageException{
		
		String applyTs = this.sysparamService.getParamValue("APPLY_DATE_LABOUR_BAILOUT4");
		String mingoApplyTs = CapDate.formatDateFromF1ToF2(applyTs, "yyyy-MM-dd", "YYY/MM/DD");
		
		Map<String, Object> prodIdMap = this.ejcicService.getLatestQueryRecordOfCreditInfoByIdInP7P9(custId);
		String prodId = prodIdMap != null ? String.valueOf(prodIdMap.get("PRODID")) : "";
		String qDate = prodIdMap != null ? String.valueOf(prodIdMap.get("QDATE")) : "";
		int notAllowA = 1;
		int notAllowB = 2;
		int notAllowC = 2;
		int notAllowD = 2; //{0:無  ,  1:有 , 2:不適用}
		int notAllowE = 2;//債信異常
		String isDebtAgreement = null;
		String isForcedStopRecord = "N";
		String isOtherBadRecord = null;
		
		C101S01B c101s01b = this.c101s01bDao.findByUniqueKey(c101m01a_mainId, custId, dupNo);
		String isSalaryTransAccount = c101s01b.getPtaFlag();
		
		//108年個人各類所得總額超過50萬元者
		Map<String, Object> ras020Map = this.ejcicService.getAnnualIncomeIsLessThan50WanIn108Year(custId);
		if(ras020Map != null){
			String income50YnQdate = String.valueOf(ras020Map.get("QDATE"));
			income50YnQdate = CapDate.formatDateFromF1ToF2(income50YnQdate, "YYY/MM/DD", "yyyy-MM-dd");
			boolean isQdateMoreThanApplyTs = CapDate.parseDate(income50YnQdate).compareTo(CapDate.parseDate(applyTs)) >= 0;
			String income50Yn = String.valueOf(ras020Map.get("INCOME50_YN"));
			notAllowA = isQdateMoreThanApplyTs && "Y".equals(income50Yn) ? 0 : 1;
		}
		
		//近一年，有超過30天之貸款延遲還款紀錄(不含信用卡延遲紀錄)
		Map<String, Object> bas020Map = this.ejcicService.getLatePaymentRecordOfCreditOver30Days(custId, mingoApplyTs);
		//行內貸款延遲還款紀錄
		Map<String, Object> insideBankPaymentMap = this.misLNF030Service.findCreditDelayPaymentRecord(custId, dupNo);
		//取得最小預期日期
		Map<String, Object> overdueMap = this.misLNF251Service.findEarliestOverdueDate(custId, dupNo);
		boolean isOverDue = false;
		if(overdueMap != null && overdueMap.get("LNF251_OV_HAPEN_DT") != null){
			Date overdueDeadLine = (Date)overdueMap.get("LNF251_OV_HAPEN_DT");
			Date toDate = CapDate.parseDate(CapDate.getCurrentDate("yyyy-MM-dd"));
			overdueDeadLine = CapDate.addMonth(overdueDeadLine, 1);
			isOverDue = toDate.after(overdueDeadLine);
		}
		
		boolean condition1 = bas020Map != null && "Y".equals(String.valueOf(bas020Map.get("PAY_CODE12_YN")));
		boolean condition2 = insideBankPaymentMap != null;
		notAllowB = condition1 || condition2 || isOverDue ? 1 : 0;
		
		//拒絕往來紀錄 
		C101S01E c101s01e = this.c101s01eDao.findByUniqueKey(c101m01a_mainId, custId, dupNo);
		notAllowC = "1".equals(c101s01e.getIsQdata10()) ? 1 : 0;
		String isStakeholder = "1".equals(c101s01e.getIsQdata2()) ? "Y" : "N";
		
		//退票未註銷達3張
		Map<String, Object> etchLog = this.etchService.getLatestEtch4111QueryLog(custId);
		if(etchLog == null){
			boolean show_error = true;
			if(true){
				Map<String, String> dam001_dam003_map = ejcicService.get_DAM001_DAM003_relateData(custId);
				
				String eChkDDate = Util.trim(MapUtils.getString(dam001_dam003_map, "eChkDDate"));
				String eChkQDate = Util.trim(MapUtils.getString(dam001_dam003_map, "eChkQDate"));
				if(Util.isNotEmpty(eChkDDate) && Util.isNotEmpty(eChkQDate) ){
				    //雖無  票交所  查詢日期, 但有 B36+D10 的日期
					show_error = false;
					//~~~~~~~~~~~~~~~~~~~~~~~~~
					String totCnt = MapUtils.getString(dam001_dam003_map, "TOTCNT");
					if(Util.isNotEmpty(totCnt)){
						notAllowD = Util.parseInt(totCnt) > 2 ? 1 : 0;
					}
				}
			}
			if(show_error){
				throw new CapMessageException("票信etch  MIS.SUCC_KEY_MAP 查無資料",  this.getClass());
			}
		}else{
			//以 票交所 的資料，判斷   {notAllowD 退票未註銷達3張}
//			String seqId = String.valueOf(etchLog.get("SEQ_ID"));
			String queryDate = String.valueOf(etchLog.get("QDATE"));
			String txId = String.valueOf(etchLog.get("TXID"));
			Map<String, Object> etchData = this.etchService.getEtchInqueryData(txId, queryDate, custId);
			if(etchData == null){
				throw new CapMessageException("票信etch MIS.MSG_001 查無資料",  this.getClass());
			}
			int d1 = (Integer)etchData.get("UED_DCC");//存款不足退票張數
			int d2 = (Integer)etchData.get("UMS_BCC");//發票人簽章不符退票張數
			int d3 = (Integer)etchData.get("SD_BCC"); //擅自指定金融業者為本票之擔當付款人退票張數
			int d4 = (Integer)etchData.get("CPE_BCC");//本票提示期限經過前撤銷付款委託退票張數
			notAllowD = (d1 + d2 + d3 + d4) > 2 ? 1 : 0;
		}
		//聯徵之債務協商註記
		Map<String, Object> vam106Map = this.ejcicService.getVAM106DataExceptMainCodeD(custId, prodId, qDate);
		Map<String, Object> vam107Map = this.ejcicService.getVAM107DataExceptMainCodeD(custId, prodId, qDate);
		isDebtAgreement = vam106Map != null || vam107Map != null ? "Y" : "N";
		
		//聯徵之信用卡強制停卡註記
		String isPayInterestNormal = "Y";//信用卡繳息紀錄為1N、XX、空白
		Map<String, Object> stopCardMap = this.ejcicService.getStopCardDataOfCreditCard(custId, prodId, qDate);
		int forcedStopCount = stopCardMap != null && stopCardMap.get("DEAD_CNT") != null ? Integer.parseInt((String)stopCardMap.get("DEAD_CNT")) : 0;
		if(forcedStopCount > 0){
			isForcedStopRecord = "Y";
			
			//檢查KRM046, STOP_CODE(停用種類代號), 是否為3-強制停用
			List<Map<String, Object>> forcedStopCode3List = this.ejcicService.getForcedStopCardDataByStopCode(custId, prodId, qDate);
			
			if(forcedStopCode3List != null && !forcedStopCode3List.isEmpty()){
				
				List<String> bankCodeList = new ArrayList<String>();
				for(Map<String, Object> m : forcedStopCode3List){
					bankCodeList.add(String.valueOf(m.get("ISSUE")));
				}
				//檢查繳款狀況
				Map<String, String> paySituationMap = this.ejcicService.getPaymentSituationOfBillInLatestMonthByBankCode(custId, prodId, qDate);
				for(String bankCode : bankCodeList){
					String status = paySituationMap.get(bankCode);
					if(!"XX".equals(status) && !"1N".equals(status)){
						isPayInterestNormal = "N";
						break;
					}
				}
			}
		}
		
		notAllowE = "Y".equals(isDebtAgreement) || "Y".equals(isForcedStopRecord) ? 1 : 0;
		
		String isIdChangedRecord = this.ejcicService.getIdChangedRecord(custId, prodId, qDate) != null ? "Y" : "N";
		String isOtherAddMark = this.ejcicService.getOtherSupplementaryNoteInfo(custId, prodId, qDate) != null ? "Y" : "N";
		String isCaseNotifiedMark = this.ejcicService.getCaseNotifiedRecord(custId, prodId, qDate) != null ? "Y" : "N";
		String isBam210Data = this.ejcicService.getOverdueCollectionAndBadDebtData(custId, prodId, qDate) != null ? "Y" : "N";
		Map<String, Object> abCreditData = this.ejcicService.getAbnormalCreditRecord(custId, prodId, qDate);
		String isBas001Record = abCreditData != null && "Y".equals(abCreditData.get("IS_DELIN")) ? "Y" : "N";
		
		String isQdata1 = "1".equals(c101s01e.getIsQdata1()) ? "Y" : "N";//婉卻紀錄
		String isQdata29 = "1".equals(c101s01e.getIsQdata29()) ? "Y" : "N"; //異常通報紀錄
		String isQdata3 = "1".equals(c101s01e.getIsQdata3()) ? "Y" : "N";//金控利害關係人(44條)
		String isQdata16 = "1".equals(c101s01e.getIsQdata16()) ? "Y" : "N";//金控利害關係人(45條)  
		String isQdata18 = "1".equals(c101s01e.getIsQdata18()) ? "Y" : "N";//疑似偽造證件或財力證明
		String isQdata7 = "1".equals(c101s01e.getIsQdata7()) ? "Y" : "N";//黑名單
		String isQdata8 = "1".equals(c101s01e.getIsQdata8()) ? "Y" : "N";//證券暨期貨違約交割紀錄
		String idCardCheckNotPass = null;
		String isFANotPass = "N";
		
		//身分證補、換發紀錄
		C101S01S c101s01s = this.c101s01sDao.findByUniqueKey(c101m01a_mainId, custId, dupNo, ClsConstants.C101S01S_dataType.行內_身分證驗證, "1");
		if(c101s01s == null){
			throw new CapMessageException("身分證補、換發紀錄 查無資料",  this.getClass());
		}
		
		idCardCheckNotPass = "1".equals(c101s01s.getDataStatus()) ? "Y" : "N";
		
		//往來客戶信用異常資料
		String clientAbnormalData = "N";
		C101S01S c101s01sAbData = this.c101s01sDao.findByUniqueKey(c101m01a_mainId, custId, dupNo, ClsConstants.C101S01S_dataType.往來客戶信用異常資料, "1");
		if(c101s01sAbData != null){
			clientAbnormalData = c101s01sAbData.getDataStatus().contains("1") ? "Y" : "N";
		}
		
//		C101S04W c101s04w = this.rpaProcessService.getC101S04WBy(c101m01a_mainId, custId);
//		if(c101s04w == null || UtilConstants.RPA.STATUS.查詢中.equals(c101s04w.getStatus())){
//			isFANotPass = "Y";
//		}
//		else{
//			
//			if(UtilConstants.RPA.STATUS.查詢完成.equals(c101s04w.getStatus())){
//				isFANotPass = c101s04w.getMemo().contains("有案件") ? "Y" : "N";
//			}
//			else{
//				isFANotPass = "N";
//			}
//		}
		
		//聯徵任一筆貸款最近一期授信繳款狀況≧1
//		int latestMonthPayRecord = 0;
//		List<Map<String, Object>> loanDataList = this.ejcicService.getBAM095_data(custId, prodId, qDate);
//		for(Map<String, Object> m : loanDataList){
//			String payRecord = m.get("PAY_CODE_12") == null ? "" : (String)m.get("PAY_CODE_12");
//			if(!"".equals(payRecord.trim())){
//				String tempRecord = payRecord.substring(0, 1);
//				if(!"0".equals(tempRecord) && !"X".equals(tempRecord)){
//					latestMonthPayRecord = 1;
//					break;
//				}
//			}
//		}
		
		C101S01G c101s01g = this.c101s01gDao.findByUniqueKey(c101m01a_mainId, queryBranch, custId, dupNo);
		String negativeInformation = "";
		if(c101s01g != null){
			negativeInformation = c101s01g.getChkItem1() + c101s01g.getChkItem2() + c101s01g.getChkItem3() + c101s01g.getChkItem4() + 
									c101s01g.getChkItem5() + c101s01g.getChkItem6() + c101s01g.getChkItem7() + c101s01g.getChkItem8();
		}
		
		isOtherBadRecord = isQdata1 + isQdata29 + isStakeholder + isQdata3 + isQdata16 + isQdata18 + isQdata7 + isQdata8 
							+ idCardCheckNotPass + isFANotPass + clientAbnormalData +  (negativeInformation.contains("1") ? "Y" : "N");

		c101s01x.setNotAllowA(notAllowA);
		c101s01x.setNotAllowB(notAllowB);
		c101s01x.setNotAllowC(notAllowC);
		c101s01x.setNotAllowD(notAllowD);
		c101s01x.setNotAllowE(notAllowE);
		c101s01x.setIsDebtAgreement(isDebtAgreement);
		c101s01x.setIsForcedStopRecord(isForcedStopRecord);
		c101s01x.setIsEAttachedPass(isPayInterestNormal);
		c101s01x.setIsPtaFlag(isSalaryTransAccount);
		c101s01x.setIsStakeHolder(isStakeholder);
		c101s01x.setIsIdChangedMark(isIdChangedRecord);
		c101s01x.setIsOtherAddMark(isOtherAddMark);
		c101s01x.setIsCaseNotifiedMark(isCaseNotifiedMark);
		c101s01x.setIsBam210Data(isBam210Data);
		c101s01x.setIsBas001Record(isBas001Record);
		c101s01x.setIsOtherBadRecord(isOtherBadRecord.contains("Y") ? "Y" : "N");
	}
	
	@Override
	public void computeScoreForLabourBailout(String custId, String queryBranch, C101S01X c101s01x, int b1Score, int c1Score){
		Map<String, Object> insuredMap = this.ejcicService.getLabourInsuredCount(custId, queryBranch);
		Map<String, Object> amountMap = this.ejcicService.getLabourInsuredAmountMoreThan23800Count(custId, queryBranch);
		int insuredScore = (Integer)insuredMap.get("TOTAL_COUNT") * 10;
		insuredScore = insuredScore > 60 ? 60 : insuredScore;
		int more23800Score = (Integer)amountMap.get("TOTAL_COUNT") * 10;
		more23800Score = more23800Score > 30 ? 30 : more23800Score;
		int a1Score = insuredScore + more23800Score;
		a1Score = a1Score > 80 ? 80 : a1Score;
		int a1_b1Score = a1Score + b1Score;
		a1_b1Score = a1_b1Score > 80 ? 80 : a1_b1Score;
		int c1ScoreTmp = c1Score > 20 ? 20 : c1Score;
		int totalScore = a1_b1Score + c1ScoreTmp;
		
		c101s01x.setA1Score(a1Score);
		c101s01x.setB1Score(b1Score);
		c101s01x.setC1Score(c1Score);
		c101s01x.setTotalScore(totalScore);
	}
	
	@Override
	public void saveC101S01X(C101S01X c101s01x, String applyDate, String applyQuota){
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String userId = user.getUserId();
		
		if(c101s01x.getCreator() == null){
			c101s01x.setCreator(userId);
			c101s01x.setCreateTime(CapDate.getCurrentTimestamp());
		}
		
		c101s01x.setApplyDate(CapDate.parseDate(applyDate));
		c101s01x.setApplyQuota(StringUtils.isEmpty(applyQuota) ? null : Integer.parseInt(applyQuota));
		c101s01x.setUpdater(userId);
		c101s01x.setUpdateTime(CapDate.getCurrentTimestamp());
		this.c101s01xDao.save(c101s01x);
	}
	
	@Override
	public void saveC101S01X(C101S01X c101s01x){
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String userId = user.getUserId();
		
		if(c101s01x.getCreator() == null){
			c101s01x.setCreator(userId);
			c101s01x.setCreateTime(CapDate.getCurrentTimestamp());
		}

		c101s01x.setUpdater(userId);
		c101s01x.setUpdateTime(CapDate.getCurrentTimestamp());
		this.c101s01xDao.save(c101s01x);
	}
	
	@Override
	public C101S01X getC101S01X(String mainId, String custId, String dupNo){
		return this.c101s01xDao.findByUniqueKey(mainId, custId, dupNo);
	}

	@Override
	public C120S01X getC120S01X(String mainId, String custId, String dupNo){
		return this.c120s01xDao.findByUniqueKey(mainId, custId, dupNo);
	}
	
	@Override
	public void checkForLabourBailout4(String applyDate) throws CapMessageException{
		Date toDate = CapDate.parseDate(CapDate.getCurrentDate("yyyy-MM-dd"));
		Date applyDateTmp = CapDate.parseDate(applyDate);
		if(applyDateTmp.after(toDate)){
			throw new CapMessageException("申請日期 不可大於今天",  this.getClass());
		}
	}
	
	@Override
	public C101S01X getC101S01XObject(String mainId, String custId, String dupNo){
		
		C101S01X c101s01x = this.getC101S01X(mainId, custId, dupNo);
		if (c101s01x == null){
			c101s01x = new C101S01X();
			c101s01x.setMainId(mainId);
			c101s01x.setCustId(custId);
			c101s01x.setDupNo("0");
		}
		
		return c101s01x;
	}
	
	@Override
	public int findC122m01aAgreeQueryEJIpCount(String agreeQueryEJIp, String applyTs){
		Map<String, Object> map = this.eloandbBASEService.findC122m01aAgreeQueryEJIpCount(agreeQueryEJIp, applyTs);
		return (Integer)map.get("TOTAL_COUNT");
	}
	
	@Override
	public int findC122m01aAgreeQueryEJMtelCount(String agreeQueryEJMtel, String applyTs){
		Map<String, Object> map = this.eloandbBASEService.findC122m01aAgreeQueryEJMtelCount(agreeQueryEJMtel, applyTs);
		return (Integer)map.get("TOTAL_COUNT");
	}
	
	@Override
	public C101S01Y getC101S01Y(String oid){
		return this.c101s01yDao.findByOid(oid);
	}

	@Override
	public C120S01Y getC120S01Y(String oid){
		return this.c120s01yDao.findByOid(oid);
	}

	@Override
	public void saveC101S01Y(C101S01Y c101s01y) {
		c101s01yDao.save(c101s01y);
	}

	@Override
	public void saveC120S01Y(C120S01Y c120s01y) {
		c120s01yDao.save(c120s01y);		
	}
	
	@Override
	public Page<C101S01Y> getC101S01YPage(ISearch search) {
		return c101s01yDao.findPage(search);
	}

	@Override
	public Page<C101S01W> getC101S01WPage(ISearch search) {
		return c101s01wDao.findPage(search);
	}
	
	@Override
	public Page<C120S01Y> getC120S01YPage(ISearch search) {
		return c120s01yDao.findPage(search);
	}

	@Override
	public Page<C120S01W> getC120S01WPage(ISearch search) {
		return c120s01wDao.findPage(search);
	}
	
	@Override
	public void deleteC101S01Y(C101S01Y c101s01y) {
		c101s01yDao.delete(c101s01y);
	}
	
	@Override
	public void deleteC120S01Y(C120S01Y c120s01y) {
		c120s01yDao.delete(c120s01y);
	}
	
	@Override
	public List<C101S01Y> getC101S01YList(String mainId, String custId, String dupNo){
		return this.c101s01yDao.findByList(mainId, custId, dupNo);
	}

	@Override
	public List<C120S01Y> getC120S01YList(String mainId, String custId, String dupNo){
		return this.c120s01yDao.findByList(mainId, custId, dupNo);
	}
	
	@Override
	public C120S01Y getC120S01Y(String mainId, String custId, String dupNo, String LAAYEAR, String LAAWORD, String LAANO) {
		return this.c120s01yDao.findLaaByList(mainId, custId, dupNo, LAAYEAR, LAAWORD, LAANO);
	}
	
	@Override
	public C101S01Y getC101S01Y(String mainId, String custId, String dupNo, String LAAYEAR, String LAAWORD, String LAANO) {
		return this.c101s01yDao.findLaaByList(mainId, custId, dupNo, LAAYEAR, LAAWORD, LAANO);
	}

	@Override
	public Map<String, Object> getEjcicReusltRecord(String custId,
			String prodId, String qDate, String mainId, String dupNo) {
		Map<String, Object> map = new HashMap<String, Object>();
		List<CodeType> codeTypes = codeTypeService.findByCodeTypeList("C101S02A_ejcicItem");
		
		Map<String, Object> lnf320Map = this.misBaseService.getLNF320ByNextBusinessDay(CapDate.getCurrentDate("yyyy-MM-dd"));
		Date preBussinessDate = lnf320Map == null ? null : (Date)lnf320Map.get("LNF320_QUERY_DATE");
		
		for (CodeType codeType : codeTypes) {
			String item = codeType.getCodeValue();
			JSONObject json = new JSONObject();
			if ("BAM095_CONTRACT_AMT1".equals(item)) {
				// BAM095_CONTRACT_AMT1 全體行庫授信綜合額度金額
				// CONTRACT_AMT1=綜合額度金額 (千元)
				String sumCONTRACT_AMT1 = "0";
				for (Map<String, Object> row : ejcicService.getBAM095_data(custId,
						prodId, qDate)) {
					String CONTRACT_AMT1 = Util.trim(MapUtils.getString(row,
					"CONTRACT_AMT1"));
					if (!CapString.isEmpty(CONTRACT_AMT1)) {
						sumCONTRACT_AMT1 = CapMath.add(sumCONTRACT_AMT1, CONTRACT_AMT1);
					}
				}
				json.put("CONTRACT_AMT1", sumCONTRACT_AMT1);
			} else if ("DAS001_IS_BCHEK".equals(item)) {
				// DAS001_IS_BCHEK 大額退票異常紀錄
				// IS_BCHEK=有Y無N
				String IS_BCHEK = "";
				for (Map<String, Object> row : ejcicService.getDAS001_data(custId,
						prodId, qDate)) {
					IS_BCHEK = Util.trim(MapUtils.getString(row,
					"IS_BCHEK"));
				}
				json.put("IS_BCHEK", IS_BCHEK);
			} else if ("DAS002_IS_REJC".equals(item)) {
				// DAS002_IS_REJC 拒絕往來紀錄
				// IS_REJC=有Y無N
				String IS_REJC = "";
				for (Map<String, Object> row : ejcicService.getDAS002_data(custId,
						prodId, qDate)) {
					IS_REJC = Util.trim(MapUtils.getString(row,
					"IS_REJC"));
				}
				json.put("IS_REJC", IS_REJC);
			} else if ("STM022_count".equals(item)) {
				// STM022_count	被查詢次數
				String count = String.valueOf(ejcicService.getSTM022_N18_data(custId, prodId, qDate).size());
				json.put("count", count);
			} else if ("VAM106_count".equals(item)) {
				// VAM106_count	消債條例信用註記資訊筆數
				String count = String.valueOf(ejcicService.getVAM106DataList(custId, prodId, qDate).size());
				json.put("count", count);
			} else if ("VAM107_count".equals(item)) {
				// VAM107_count	銀行公會消金案件債務協商補充註記筆數
				String count = String.valueOf(ejcicService.getVAM107DataList(custId, prodId, qDate).size());
				json.put("count", count);
			} else if ("KRM040_code".equals(item)) {
				// KRM040_code 繳款狀況
				Set<String> codes = new HashSet<String>();
				for (Map<String, Object> row : ejcicService.getKRM040_data(custId,
						prodId, qDate)) {
					// PAY_STAT||PAY_CODE
					String code = Util.trim(MapUtils.getString(row, "PAY_STAT")) 
							+ Util.trim(MapUtils.getString(row, "PAY_CODE"));
					codes.add(code);
				}
				json.put("code", StringUtils.join(codes, ","));
			}
			else if("STM017_IS_QUERIED".equals(item)){
				//近2個營業日有無他行查詢聯徵紀錄
				List<Map<String, Object>> list = this.ejcicService.getEjcicQueryRecoredByOtherBank(custId, prodId, qDate);
				boolean isQueriedByOtherBank = false;
				for(Map<String, Object> m : list){
					String queryDate_yyyyMMdd = CapDate.formatDateFormatToyyyyMMdd(String.valueOf(m.get("QUERY_DATE")), "YYYMMDD");
					Date queryDate = CapDate.parseDate(queryDate_yyyyMMdd);
					if(preBussinessDate != null && queryDate.compareTo(preBussinessDate) >= 0){
						isQueriedByOtherBank = true;
					}
				}
				json.put("IS_QUERIED", isQueriedByOtherBank ? "Y" : "N");
			}
			else if("BAS006_IS_CREDIT_AD".equals(item)){
				//是否有新增額度/撥款清償提示資訊
				List<Map<String, Object>> list = this.ejcicService.getNewQuotaOrRepaymentInfo(custId, prodId, qDate);
				String isNewQuotaOrRepaymentInfo = "N";
				for(Map<String, Object> m : list){
					isNewQuotaOrRepaymentInfo = "Y".equals(m.get("CREDIT_AD_FLAG")) ? "Y" : isNewQuotaOrRepaymentInfo;
				}
				json.put("IS_CREDIT_AD", isNewQuotaOrRepaymentInfo);
			} else if("DA_IS_STAKEHOLDER".equals(item)){
				//是否有命中銀行法或金控法44條利害關係人
				List<C101S01S> c101s01sList = this.clsService.findC101S01S_byIdDupDataType(mainId, custId, dupNo, ClsConstants.C101S01S_dataType.客戶是否為利害關係人資料);
				String isStakeholders = "N";
				for(C101S01S c101s01s : c101s01sList){
					if(c101s01s.getDataStatus().replaceAll("0", "").length()>0){
						isStakeholders = "Y";
					}
				}
				json.put("IS_STAKEHOLDER", isStakeholders);
			} else if("FA_DATA_STATUS".equals(item)){
				
				C101S04W c101s04w = this.rpaProcessService.getC101S04WBy(mainId, custId);
				String dataStatus = "";
				if(c101s04w != null){
					dataStatus = this.rpaProcessService.getDataStatus(c101s04w.getStatus(), c101s04w.getReturnData());
				}
				
				String statusName = RPAProcessServiceImpl.statusNameMap.get(dataStatus);
				json.put("DATA_STATUS", statusName);
			}
			
			map.put(item, json.toString());
		}
		return map;
	}
	
	@Override
	public String checkIsQueryEjcicS11(String c101m01a_mainId, String custId, String dupNo){
		
		Map<String, Object> map = this.ejcicService.getLatestQueryRecordOfCreditInfoById(custId);
		if(map == null){
			logger.trace("checkIsQueryEjcicS11 [無聯徵查詢紀錄] custId:" + custId + "/ C101M01A_mainId:" + c101m01a_mainId);
			return "無聯徵查詢紀錄";
		}
			
		String prodId = String.valueOf(map.get("PRODID"));
		if(!"P9".equals(prodId)){
			logger.trace("checkIsQueryEjcicS11 [不為信貸一鍵查詢P9] custId:" + custId + "/ C101M01A_mainId:" + c101m01a_mainId);
			return "不為信貸一鍵查詢P9";
		}
		
		String queryPersonId = String.valueOf(map.get("REQID"));
		String queryPersonBranchNo = String.valueOf(map.get("QBRANCH"));
		String queryDate = String.valueOf(map.get("QDATE"));
		List<Map<String, Object>> list = this.ejcicService.getBAM095Data(custId, prodId, queryPersonId, queryPersonBranchNo, queryDate);
		
		if(this.ejcicService.getEjcicS11Data(queryPersonId, queryPersonBranchNo, custId).size() > 0){
			logger.debug("checkIsQueryEjcicS11 [已有聯徵S11查詢紀錄] custId:" + custId + "/ C101M01A_mainId:" + c101m01a_mainId);
			return "已有聯徵S11查詢紀錄";
		}
		
		/* J-113-0270 修改S11自動查詢邏輯-取消判斷有授信BAM095資料不查S11
		if(list.size() > 0){
			logger.debug("checkIsQueryEjcicS11 [有授信BAM095資料] custId:" + custId + "/ C101M01A_mainId:" + c101m01a_mainId);
			return "有授信BAM095資料";
		}
		*/
		
		C101S01A c101s01a = this.c101s01aDao.findByUniqueKey(c101m01a_mainId, custId, dupNo);
		Date date35yrsOld = CapDate.addYears(c101s01a.getBirthday(), 35);
		
		
		
		Date toDate = CapDate.parseDate(CapDate.getCurrentDate("yyyy-MM-dd"));
		if(toDate.compareTo(date35yrsOld) < 0){
			logger.trace("checkIsQueryEjcicS11 [小於35歲] custId:" + custId + "/ C101M01A_mainId:" + c101m01a_mainId);
			return "小於35歲";
		}
		
		
		
		list = this.ejcicService.getKRM046Data(custId);
		if(list.size() > 0){
			logger.debug("checkIsQueryEjcicS11 [有信用卡KRM046資料] custId:" + custId + "/ C101M01A_mainId:" + c101m01a_mainId);
			return "有信用卡KRM046資料";
		}

		return "";
	}

	@Override
	public void queryWitcherFin(String mainId, String custId, String dupNo, String mTel, JSONObject result) throws CapMessageException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		mTel = CapString.trimNull(mTel);
		// 判斷電話號碼
		boolean isRightNumFormatter = isNeedQueryWitcherFin(mTel);
		// 不查詢時清空已存在資料
		if (isRightNumFormatter == false) {
			C101S02B c101s02b = findModelByKey(C101S02B.class,
					mainId, custId, dupNo);
			if (c101s02b != null) {
				delete(c101s02b);
			}
		} else {
			JSONObject withcerFinJson = new JSONObject();
			try {
				withcerFinJson = witcherFinService.getWicherFinData(
						WitcherFinApiEnum.全部, mTel, mainId);
			} catch (Exception e) {
				throw new CapMessageException(e.getMessage(), getClass());
			}
			C101S02B s02b = c101s02bDao.findByUniqueKey(mainId, custId, dupNo);
			if (s02b == null) {
				s02b = new C101S02B();
				s02b.setMainId(mainId);
				s02b.setCustId(custId);
				s02b.setDupNo(dupNo);
				s02b.setWfCreator(user.getUserId());
				s02b.setWfCreateTime(CapDate.getCurrentTimestamp());
			}
			// 20220509,消金處呂明澤,若number_origin OR is_invalid_number為"not found"，則將顯是欄位無資料
			if ("not found".equals(withcerFinJson.optString("number_origin")) 
					|| "not found".equals(withcerFinJson.optString("is_invalid_number"))) {
				String[] c101S02B_HMLCols = new String[] { "in_fraud_db",
						"bad_history", "spam_num", "special_num",
						"using_frequency", "reachable", "last_activity_range",
						"has_social_activity", "has_traffic_pattern",
						"has_abnormal_pattern", "has_multiple_region",
						"whitelist_cluster", "phony_account_cluster",
						"agency_cluster", "special_loan_cluster",
						"pawnshop_cluster", "debt_collect_cluster",
						"spec_career_cluster", "installment_cluster" };
				for (String col : c101S02B_HMLCols) {
					withcerFinJson.put(col, 99);
				}
			}
			String[] s02bCols = CapEntityUtil.getColumnName(s02b);
			CapBeanUtil.map2Bean(withcerFinJson, s02b, s02bCols);
			// 因DB欄位名稱與JSON KEY不同，額外處理suspicious_laa_cluster
			Integer suspicious_laa_clusterI = withcerFinJson.optInt("suspicious_land_administration_agents_cluster");
			if (suspicious_laa_clusterI != null) {
				s02b.setSuspicious_laa_cluster(new BigDecimal(suspicious_laa_clusterI));
			}
			s02b.setMpnum(mTel);
			s02b.setWfUpdater(user.getUserId());
			s02b.setWfUpdateTime(CapDate.getCurrentTimestamp());
			c101s02bDao.save(s02b);
			if (result != null) {
				result.putAll(withcerFinJson);
			}
		}
	}

	@Override
	public boolean isNeedQueryWitcherFin(String mTel) throws CapMessageException {
		// 判斷電話號碼
		// 0.空白不發查
		// 1.09XXXXXXXX國內電話發查
		// 2.09??國內電話但不完整顯示提示訊息
		// 3.其他非空白不發查
		mTel = CapString.trimNull(mTel);
		boolean isRightNumFormatter = false;
		if (CapString.isEmpty(mTel)) {
			isRightNumFormatter = false;
		} else if (mTel.matches("(09)+[\\d]{8}")) {
			isRightNumFormatter = true;
		} else if (mTel.startsWith("09")) {
			throw new CapMessageException("行動電話號碼格式有誤", getClass());
		} else {
			isRightNumFormatter = false;
		}
		return isRightNumFormatter;
	}
	
	@Override
	public List<EJCICCOMMON> findEJCICCOMMONList(String custId, String prodId, String txId, String qdate) {
		return ejciccommonDao.findByJcicKey(custId, prodId, txId, qdate);
	}
	@Override
	public List<C101S01H> findC122S01HList(String mainId) {
		return c101s01hDao.findByMainId(mainId);
	}
	@Override
	public List<C101S01U> findC122S01UList(String mainId, String custId, String dupNo, String txId) {
//		return c101s01uDao.findByMainIdCustIdDupNoTxid(mainId, custId, dupNo, txId);
		return c101s01uDao.findByMainIdTxid(mainId, txId);
	}
	
	
	public void findC122S01ItoC101S01H_U(String mainId, String custId, String dupNo, String[] qdate_array){
		//找C122S01I有沒有對應的html資料(P7,J10) [PRODID+CUSTID+QDATE]
		//基本上要處理的資料 PRODID = [P7,J10]
		
		/*理論上若是正常藉由排成發查，P7及J10的QDATE應該會是一樣的
		 * 但考慮到，組合查詢P7 >> PRODID=P7 TXID=H128/H135。
		 * 			標準查詢J10 >> PRODID=ST TXID=HJ10
		 * 參數定義差異，已及QDATE有級低機率不一樣的情況，資料搬移作業將分別進行
		 */
		
		//設定參數，QDATE外面查好了，傳進來即可。
		String[] prodId_array = {"P7", "ST"};
		String[] txId_array = {"H135,H128","HJ10"};
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		for(int p=0;p<prodId_array.length;p++){
			String prodId = prodId_array[p];
			String txId = txId_array[p];
			String qdate = qdate_array[p];
			String[] arrTxIdSplit = txId.split(",");
			boolean deleteFlagP7 = true;
			boolean deleteFlagJ10 = true;
			for(int tx=0;tx<arrTxIdSplit.length;tx++){
				//P7會有兩種TXID，所以要做兩次
				List<EJCICCOMMON> ejciccommomList = this.findEJCICCOMMONList(custId,prodId,arrTxIdSplit[tx],qdate); 
				if(ejciccommomList != null && ejciccommomList.size()>0){ //有資料
					for(int i=0;i<ejciccommomList.size();i++){
						EJCICCOMMON ejciccommon = ejciccommomList.get(i);
						//開始資料搬移，將EJCICCOMMON copy到 C101S01H
						try {
							if(prodId.equals("P7")){
								//檢查C101S01H有沒有一樣的資料，有的化先刪除
								if(deleteFlagP7){
									List<C101S01H> c101s01hList = this.findC122S01HList(mainId);
									if (CollectionUtils.isNotEmpty(c101s01hList)) {//有資料，先行刪除
										c101s01hDao.delete(c101s01hList);
									}
								}
								C101S01H newC101S01H = new C101S01H();
								CapBeanUtil.copyBean(ejciccommon, newC101S01H, new String[] { "custId",
										"dupNo","banid","Id","prodid","txid","htmlData","qDate","qEmpCode",
										"qEmpName","qBranch","creator","createTime","updater","updateTime"});
								newC101S01H.setMainId(mainId);
								save(newC101S01H);
							}else{ //J10
								//檢查C101S01U有沒有一樣的資料，有的化先刪除
								if(deleteFlagJ10){
									List<C101S01U> c101s01uList = this.findC122S01UList(mainId, custId, dupNo, arrTxIdSplit[tx]);
									if (CollectionUtils.isNotEmpty(c101s01uList)) {//有資料，先行刪除
										c101s01uDao.delete(c101s01uList);
									}
								}
								C101S01U newC101S01U = new C101S01U();
								CapBeanUtil.copyBean(ejciccommon, newC101S01U, new String[] { "custId",
										"dupNo","txid","htmlData","qDate","creator","createTime","updater","updateTime"});
								newC101S01U.setMainId(mainId);
								newC101S01U.setSendTime(ejciccommon.getCreateTime()); //排成發查，傳送時間=產生資料時間
								save(newC101S01U);
							}
						} catch (CapException e) {
							// TODO Auto-generated catch block
							e.printStackTrace();
						}
					}
					deleteFlagP7 = false;
					deleteFlagJ10 = false;
				}
			}
		}
		//將資料轉至到C101S01H/C101S01U,(MAINID要修改為個金徵信用的MAINID)
	}

	
	@Override
	public CapAjaxFormResult mappingClsJob (String clsJobType1Val ,String clsJobType2Val,String clsJobTitleVal,String capital, boolean isNPO)throws CapMessageException{
		if(StringUtils.isEmpty(clsJobType1Val)){
			return createMappingCleanResult();
		}
		if(StringUtils.isEmpty(clsJobType2Val)){
			return createMappingCleanResult();
		}
		if(StringUtils.isEmpty(clsJobTitleVal)){
			return createMappingCleanResult();
		}
		if(StringUtils.isEmpty(capital)){
			return createMappingCleanResult();
		}
		
		//檢查jobtype1、jobtype2 是否為同一個類型
		if(clsJobType2Val.charAt(0) != clsJobType1Val.charAt(0)){
			return createMappingCleanResult();
		}
		
		
		
		String [] SPECIAL_JOB = {"0301","0302","0303","0304","0305","0501","0502","0601","0602","0603","0604","0605","0606","0701","0702","0703","0704","0705","0801","0802","0901","0902","0903","1301","1302","1501","1502","1503","1504","1506","1601"};
		String CLSJOBTYPE = "clsJobType";
		
		//init value 
		String oldJobType1Val = "";
		String oldJobType2Val = "";
		String oldJobTitleVal = "";
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		

		//mapping
		//檢查特殊職業
		for(String specialJob : SPECIAL_JOB){
			if(clsJobTitleVal.equals(specialJob)){
				CodeType clsJobTitle = codeTypeService.findByCodeTypeAndCodeValue(new String[] {"clsJobTitle"},clsJobTitleVal);
				oldJobType1Val = clsJobTitle.getCodeDesc2().split("_")[0];
				oldJobType2Val = clsJobTitle.getCodeDesc2().split("_")[1];
				oldJobTitleVal = clsJobTitle.getCodeDesc3();
				result = createMappingClsJobResult(oldJobType1Val,oldJobType2Val,oldJobTitleVal);
				return result;
			}
		}
		double capitalInt =  Util.parseDouble(capital);
		
		//是否非營利 & 小於500W & 負責人
		if(!isNPO && capitalInt<5000000 && clsJobTitleVal.equals("0201")){
			//貿易/自營 - 自營企業主 - 非上市負責人
			return createMappingClsJobResult("07","E","i");
		}
		
		
		//一般mapping 
		CodeType clsJobType = codeTypeService.findByCodeTypeAndCodeValue(new String[] {CLSJOBTYPE+clsJobType1Val},clsJobType2Val);
		oldJobType1Val = clsJobType.getCodeDesc2();
		oldJobType2Val = clsJobType.getCodeDesc3();
		CodeType clsJobTitle = codeTypeService.findByCodeTypeAndCodeValue(new String[] {"clsJobTitle"},clsJobTitleVal);
		//檢查是否為上市上櫃。
		oldJobTitleVal = (capitalInt >= 50000000)? clsJobTitle.getCodeDesc3() : clsJobTitle.getCodeDesc2();
		
		//檢查是否符合 B02-「電子業、電腦業、科技業」及「I01-電訊業、通訊業、網訊業」且 500W下 且 非營利單位。  需寫入資訊通訊業的其他
		if(!isNPO && capitalInt<5000000){
			if(clsJobType1Val.equals("B") && clsJobType2Val.equals("B02")){
				return createMappingClsJobResult("06","C",oldJobTitleVal);
			}
			if(clsJobType1Val.equals("I") && clsJobType2Val.equals("I01")){
				return createMappingClsJobResult("06","C",oldJobTitleVal);
			}
		}
		
		result = createMappingClsJobResult(oldJobType1Val,oldJobType2Val,oldJobTitleVal);
		
		return result;
	}
	
	private CapAjaxFormResult createMappingClsJobResult(String oldJobType1Val,String oldJobType2Val,String oldJobTitleVal){
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set("jobType1", oldJobType1Val);
		result.set("jobType2", oldJobType2Val);
		result.set("jobTitle", oldJobTitleVal);
		
		return result;
		
	}
	private CapAjaxFormResult createMappingCleanResult(){
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set("jobType1", "");
		result.set("jobType2", "");
		result.set("jobTitle", "");
		
		return result;
		
	}
	
	@Override
	public boolean isQueryEjcicT70Info(String custId, String dupNo, String branchNo, String mainId){
		
		//check 資料是否存在 是否需重新發查
		Map<String, Object> map = this.misBaseService.findELF690ByCustIdAndDupNoAndBranchNo(custId, dupNo, branchNo);
		if(map == null){
			return true;
		}
		
		if(map != null){
			
			Object ejTmeStamp = map.get("ELF690_EJ_TMESTAMP");
			if(ejTmeStamp == null){
				return false;
			}
			
			// 查詢成功超過 30天 需重新發查
			C101S02S c101s02s = this.clsService.findLatestC101S02S(custId, dupNo, branchNo);
			
			if(c101s02s == null){
				return true;
			}
			
			Date tempDate = c101s02s.getqSuccessDate();
			
			if(tempDate != null){
				
				Calendar qSuccessDate = Calendar.getInstance();
				qSuccessDate.setTime(tempDate);
				qSuccessDate.add(Calendar.DAY_OF_YEAR, 29);
				Calendar nowDate = Calendar.getInstance();
				nowDate.setTime(CapDate.parseDate(CapDate.formatDate(new Date(), "yyyy-MM-dd")));
				if(qSuccessDate.compareTo(nowDate) == -1){
					return true;
				}
			}

		}
		
		return false;
	}
	
	@Override
	public void deleteC101S02S(String mainId, String custId, String dupNo){
		
		C101S02S c101s02s = this.c101s02sDao.findByUniqueKey(mainId, custId, dupNo);
		if(c101s02s != null){
			this.c101s02sDao.delete(c101s02s);
		}
	}
	
	@Override
	public void saveC101S02S(String mainId, String custId, String dupNo, String branchNo){
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Timestamp currentTimestamp = CapDate.getCurrentTimestamp();
		C101S02S c101s02s = new C101S02S();
		c101s02s.setMainId(mainId);
		c101s02s.setCustId(custId);
		c101s02s.setDupNo(dupNo);
		c101s02s.setBranchNo(branchNo);
		c101s02s.setCreator(user.getUserId());
		c101s02s.setCreateTime(currentTimestamp);
		c101s02s.setUpdater(user.getUserId());
		c101s02s.setUpdateTime(currentTimestamp);
		this.c101s02sDao.save(c101s02s);
	}
	
	@Override
	public C101S02S getC101s02sByUniqueKey(String mainId, String custId, String dupNo){
		return this.c101s02sDao.findByUniqueKey(mainId, custId, dupNo);
	}
	
	@Override
	public C120S02S getC120s02sByUniqueKey(String mainId, String custId, String dupNo){
		return this.c120s02sDao.findByUniqueKey(mainId, custId, dupNo);
	}
	
	@Override
	public void setC101s01eVersion(String version, C101M01A c101m01a){
		C101S01E c101se01e = this.clsService.findC101S01E(c101m01a);
		c101se01e.setVersion(version);
	}
	
	private String processC101s01eIsQdata8(String mainId, String custId, String dupNo){

		C101S01S c101s01s = this.c101s01sDao.findByUniqueKey(mainId, custId, dupNo, ClsConstants.C101S01S_dataType.證券暨期貨違約交割紀錄, "1");
		
		if(c101s01s == null){
			return null;
		}
		
		String dataStatus = c101s01s.getDataStatus();
		if("1".equals(dataStatus)){
			return "T";
		}
		
		return "F";
	}
	
	@Override
	public Map<String,String> findTermGroup(String mainId,String custId,String dupNo){
		Map<String,String> termGroupResultMap = null;
		C101M01A c101m01a = clsService.findC101M01A_mainId(mainId);//個金徵信
		
		if(c101m01a!=null && Util.isNotEmpty(c101m01a)){
			termGroupResultMap = findTermGroupByC101m01a(c101m01a);
		} else {//如果C101M01A(個金徵信)找不到，就是簽報書引用的借款人
			C120M01A c120m01a = this.findModelByKey(C120M01A.class,
					mainId, custId, dupNo);
			if(c120m01a!=null && Util.isNotEmpty(c120m01a)){
				termGroupResultMap = findTermGroupByC120m01a(c120m01a);
			}
		}
		return termGroupResultMap;
	}
	
	private boolean needTermGroup(Timestamp mainUpdateTime){
		boolean isNeed = false;
		DateFormat S_FORMAT = new SimpleDateFormat(UtilConstants.DateFormat.YYYY_MM_DD);
		String chkDateStr = Util.trim(sysparamService.getParamValue("J-112-0467_CHKDATE"));
		Date chkDate = CapDate.getDate(
				(Util.isEmpty(chkDateStr) ? CapDate
						.getCurrentDate("yyyy-MM-dd") : chkDateStr),
				"yyyy-MM-dd");
		Date updateTime = (mainUpdateTime == null ? Util
				.parseDate(CapDate.getCurrentDate("yyyy-MM-dd")) :
					Util.parseDate(S_FORMAT.format(mainUpdateTime)) );
		
		if (updateTime.compareTo(chkDate) >= 0) {
			isNeed = true;
		}
		return isNeed;
	}
	
	private Map<String,String> findTermGroupByC101m01a(C101M01A c101m01a){
		Map<String,String> termGroupResultMap = null;
		String termGroup = "";
		String applyDBRType = "";
		String errorMsg = "";
		if(c101m01a!=null && Util.isNotEmpty(c101m01a)){
			C101S01B c101s01b = clsService.findC101S01B(c101m01a);
			if(c101s01b != null && Util.isNotEmpty(c101s01b) ){
				if(Util.isNotEmpty(c101s01b.getTermGroup())){
					termGroup = Util.trim(c101s01b.getTermGroup());
					applyDBRType = Util.trim(c101s01b.getApplyDBRType());
				}else{
					// 在C101S01B 沒有資料
					// 如果是功能上線後要去檢查需不需要補資料
					if(needTermGroup(c101m01a.getUpdateTime()) &&
							clsService.is_function_on_codetype("J-113-0285_oldCase")){
						// 去收決策之前給的結果(註：這邊開始是補之前的資料)
						L120S19A l120s19a = clsService
									.findL120S19A_byMainId_itemType_latest_itemVersion(c101m01a.getMainId(),
											ClsConstants.L120S19A_ItemTypeCode.BRMP_termGroupRule_output);
						// 有收到決策的結果就寫回 C101S01B
						if(l120s19a!=null){
							try{
								ObjectMapper objectMapper = new ObjectMapper();
								Brmp004O brmpOutput = 
									objectMapper.readValue(JSONObject.fromObject(l120s19a.getJsonData()).toString(), Brmp004O.class);
								if(brmpOutput!=null){
									if(Util.isNotEmpty(brmpOutput.getErrorMsg())){
										//有錯誤就不會有結果了
										errorMsg = Util.trim(brmpOutput.getErrorMsg());
									}else{
										termGroup = Util.trim(brmpOutput.getResult().getTermGroup());
										applyDBRType = Util.trim(brmpOutput.getResult().getApplyDBRType());
										c101s01b.setTermGroup(termGroup);
										c101s01b.setApplyDBRType(applyDBRType);
										this.save(c101s01b);
									}
								}
								
							}catch(Exception e){
								logger.error("[findTermGroupByC101m01a] error," +
										" c120m01a MAINID=[" + c101m01a.getMainId() + "]," +
										" c120m01a CUSTID=[" + c101m01a.getCustId() + "]," +
										" l120s19a MAINID=[" + l120s19a.getMainId() + "]"+
										StrUtils.getStackTrace(e));
							}
						}
					}
				}
			}
		}
		if(Util.isNotEmpty(termGroup) || 
				Util.isNotEmpty(errorMsg)){
			termGroupResultMap = new HashMap<String,String>();
			termGroupResultMap.put("haveResult", "Y");
			termGroupResultMap.put("termGroup", termGroup);
			termGroupResultMap.put("applyDBRType", applyDBRType);
			termGroupResultMap.put("errorMsg", errorMsg);
		}
		return termGroupResultMap;
	}
	
	private Map<String,String> findTermGroupByC120m01a(C120M01A c120m01a){
		Map<String,String> termGroupResultMap = null;
		String termGroup = "";
		String applyDBRType = "";
		String errorMsg = "";
		
		if(c120m01a!=null && Util.isNotEmpty(c120m01a)){
			C120S01B c120s01b = clsService.findC120S01B(c120m01a);
			if(c120s01b!=null && Util.isNotEmpty(c120s01b)){
				termGroup = Util.trim(c120s01b.getTermGroup());
				applyDBRType = Util.trim(c120s01b.getApplyDBRType());
				// 在 C120S01B 沒有資料 + 功能上線後的
				// 就去找決策之前查詢結果(註：這邊是補之前的資料)
				if(Util.isEmpty(termGroup) && 
						needTermGroup(c120m01a.getUpdateTime()) &&
						clsService.is_function_on_codetype("J-113-0285_oldCase")){
					// 1.先回頭找個金徵信的
					C101M01A c101m01a_original = 
						this.findC101M01A(c120m01a.getOwnBrId(), c120m01a.getCustId(), c120m01a.getDupNo());
					termGroupResultMap = findTermGroupByC101m01a(c101m01a_original);
					if(termGroupResultMap != null && Util.isNotEmpty(termGroupResultMap)){
						// 1-1.個金徵信有資料要寫回來簽報書這邊的借款人
						if(Util.equals("Y", Util.trim(termGroupResultMap.get("haveResult")))){
							if(Util.isEmpty(termGroupResultMap.get("errorMsg"))){
								termGroup = Util.trim(termGroupResultMap.get("termGroup"));
								applyDBRType = Util.trim(termGroupResultMap.get("applyDBRType"));
								c120s01b.setTermGroup(termGroup);
								c120s01b.setApplyDBRType(applyDBRType);
								this.save(c120s01b);
							}
						}
						
					}else{
						// 2.如果個金徵信沒有再用簽報書的mainId去找
						// (這是因為一開始功能設計少了把個金徵信結果帶到簽報書裡面，
						// 導致簽報書這邊會是另外查，這樣在途案件也可能會有結果，
						// 但是取結果時沒有區分借款人，若簽報書引入超過一個人的時候就會有誤抓的情況了)
						// 2-1.先用簽報書的mainId把清單撈出來(從新到舊排序)比對CUSTID是否一致
						List<L120S19A> l120s19as = clsService.findByMainIdItemTypeOrderBy(c120m01a.getMainId(),
								ClsConstants.L120S19A_ItemTypeCode.BRMP_termGroupRule_output, true);
						if(l120s19as!=null && l120s19as.size()>0){
							for( L120S19A l120s19a : l120s19as ){
								try{
									JSONObject jsons = JSONObject.fromObject(l120s19a.getJsonData());
									if(jsons != null){
										JSONObject inputFactor = JSONObject.fromObject(jsons.get("inputFactor"));
										if(inputFactor != null){
											String l120s19a_custId = Util.trim(inputFactor.get("custId"));
											// 2-2.有對應上CUSTID就寫回 簽報書這邊的借款人
											if(Util.equals(l120s19a_custId, c120m01a.getCustId())){
												ObjectMapper objectMapper = new ObjectMapper();
												Brmp004O brmpOutput = 
													objectMapper.readValue(JSONObject.fromObject(l120s19a.getJsonData()).toString(), Brmp004O.class);
												if(brmpOutput!=null){
													if(Util.isNotEmpty(brmpOutput.getErrorMsg())){
														//有錯誤就不會有結果了
														errorMsg = Util.trim(brmpOutput.getErrorMsg());
													}else{
														termGroup = Util.trim(brmpOutput.getResult().getTermGroup());
														applyDBRType = Util.trim(brmpOutput.getResult().getApplyDBRType());
														c120s01b.setTermGroup(termGroup);
														c120s01b.setApplyDBRType(applyDBRType);
														this.save(c120s01b);
													}
												}
												break;
											}
										}
									}
								}catch(Exception e){
									logger.error("[findTermGroupByC120m01a] = >"+StrUtils.getStackTrace(e));
									logger.error("[findTermGroupByC120m01a] error," +
											" c120m01a MAINID=[" + c120m01a.getMainId() + "]," +
											" c120m01a CUSTID=[" + c120m01a.getCustId() + "]," +
											" l120s19a MAINID=[" + l120s19a.getMainId() + "]"+
											StrUtils.getStackTrace(e));
								}
							}
						}
					}
				}
			}
		}
		if(Util.isNotEmpty(termGroup) ||
				Util.isNotEmpty(errorMsg)){
			termGroupResultMap = new HashMap<String,String>();
			termGroupResultMap.put("haveResult", "Y");
			termGroupResultMap.put("termGroup", termGroup);
			termGroupResultMap.put("applyDBRType", applyDBRType);
			termGroupResultMap.put("errorMsg", errorMsg);
		}
		
		return termGroupResultMap;
	}
	
	@Override
	public boolean isCloseFinHoldingDefaultDeliveryFunction() {
		
		String chkDateStr = Util.trim(lmsService.getSysParamDataValue("IS_CLOSE_STOCK_DEFAULT_FUN"));
		
		Date chkDate = CapDate.getDate((Util.isEmpty(chkDateStr) ? CapDate.getCurrentDate("yyyy-MM-dd") : chkDateStr), "yyyy-MM-dd");
		
		Date toDate = CapDate.parseDate(CapDate.getCurrentDate("yyyy-MM-dd"));

		if (toDate.compareTo(chkDate) >= 0) {
			return true;
		}

		return false;
	}
	
	@Override
	public C101S02S copyNewC101S02SAndC101S01U(String c101m01a_mainId, C101S02S c101s02s) throws CapException{
		
		Timestamp nowTS = CapDate.getCurrentTimestamp();
		C101S02S newC101S02S = new C101S02S();
		DataParse.copy(c101s02s, newC101S02S);
		newC101S02S.setMainId(c101m01a_mainId);
		newC101S02S.setCreateTime(nowTS);
		
		if("Y".equals(c101s02s.getRefreshStatus())){
			
			List<C101S01U> c101s01u_list = this.clsService.findC101S01U_txid(c101s02s.getMainId(), c101s02s.getCustId(), c101s02s.getDupNo(), CrsUtil.EJ_TXID_T70);
			if (c101s01u_list.size() > 0) {
				C101S01U c101s01u = c101s01u_list.get(0);
				C101S01U newC101s01u = new C101S01U();
				DataParse.copy(c101s01u, newC101s01u);
				newC101s01u.setMainId(c101m01a_mainId);
				newC101s01u.setCreateTime(nowTS);
				this.clsService.daoSave(newC101s01u);
			}
		}
		
		this.clsService.saveC101S02S(newC101S02S);
		return newC101S02S;
	}
	
	//取得雙軌模型分數 >> 雙軌啟用時間為2024-08-01~2024-12-31
	public CapAjaxFormResult loadScoreSDT(CapAjaxFormResult formResult, 
			String markModel, String mainId, String custId, String dupNo) throws CapException{
		
		C101S01G_N model_gn = null;
		C101S01Q_N model_qn = null;
		C101S01R_N model_rn = null;
		
		if (Util.equals(markModel, UtilConstants.L140S02AModelKind.房貸)) {
			model_gn = this.findModelByKey(C101S01G_N.class, mainId,
					custId, dupNo);
		} else if (Util.equals(markModel, UtilConstants.L140S02AModelKind.非房貸)) {
			model_qn = this.findModelByKey(C101S01Q_N.class, mainId,
					custId, dupNo);
		} else if (Util.equals(markModel, UtilConstants.L140S02AModelKind.卡友貸)) {
			model_rn = this.findModelByKey(C101S01R_N.class, mainId,
					custId, dupNo);
		}
		if (model_gn != null) {
			CapAjaxFormResult formResult_n = this.loadScore_GN(model_gn);
			formResult_n.putAll(ClsUtil.procMarkModel_G_N(model_gn));
			//J-111-0271 消金房貸3.0,計算[與最高分這差距]、[影響性]
			String varVer = Util.trim(model_gn.getVarVer());
			if(Util.equals(varVer, ClsScoreUtil.V3_0_HOUSE_LOAN)){
				//與最高分差距
				//Step1. 定義9個因子分數欄位
				String[] scoreArr = ClsScoreUtil.scoreArr_G_3_0;
				String[] HighGapArr = ClsScoreUtil.HighGapArr_G_3_0;
				String[] InfluenceArr = ClsScoreUtil.InfluenceArr_G_3_0;
				String[] HighScoreArr = ClsScoreUtil.HighScoreArr_G_3_0;
				String[] itemArr = ClsScoreUtil.itemArr_G_3_0;
				String[] elseInfoArr = ClsScoreUtil.elseInfoArr_G_3_0;
				
				this.newScoreModel_01(formResult_n,scoreArr,HighGapArr,InfluenceArr, HighScoreArr);
				//欄位轉置
				for (int i=0 ; i<scoreArr.length; i++) {
					formResult.set(scoreArr[i]+"_N", formResult_n.get(scoreArr[i])== null ? null : formResult_n.get(scoreArr[i]).toString());
				}
				for (int i=0 ; i<HighGapArr.length; i++) {
					formResult.set(HighGapArr[i]+"_N", formResult_n.get(HighGapArr[i])== null ? null : formResult_n.get(HighGapArr[i]).toString());
				}
				for (int i=0 ; i<InfluenceArr.length; i++) {
					formResult.set(InfluenceArr[i]+"_N", formResult_n.get(InfluenceArr[i])== null ? null : formResult_n.get(InfluenceArr[i]).toString());
				}
				for (int i=0 ; i<itemArr.length; i++) {
					formResult.set(itemArr[i]+"_N", formResult_n.get(itemArr[i])== null ? null : formResult_n.get(itemArr[i]).toString());
				}
				for (int i=0 ; i<elseInfoArr.length; i++) {
					formResult.set(elseInfoArr[i]+"_N", formResult_n.get(elseInfoArr[i])== null ? null : formResult_n.get(elseInfoArr[i]).toString());
				}
			}
		}
		
		if (model_qn != null) {
			CapAjaxFormResult formResult_n = this.loadScore_QN(model_qn);
			formResult_n.putAll(ClsUtil.procMarkModel_Q_N(model_qn));
			//J-111-0271 消金房貸3.0,計算[與最高分這差距]、[影響性]
			String varVer = Util.trim(model_qn.getVarVer());
			if(Util.equals(varVer, ClsScoreUtil.V4_0_NOT_HOUSE_LOAN)){
				String[] scoreArr = ClsScoreUtil.scoreArr_Q_4_0;
				String[] HighGapArr = ClsScoreUtil.HighGapArr_Q_4_0;
				String[] InfluenceArr = ClsScoreUtil.InfluenceArr_Q_4_0;
				String[] HighScoreArr = ClsScoreUtil.HighScoreArr_Q_4_0;
				String[] itemArr = ClsScoreUtil.itemArr_Q_4_0;
				String[] elseInfoArr = ClsScoreUtil.elseInfoArr_Q_4_0;
				
				this.newScoreModel_01(formResult_n,scoreArr,HighGapArr,InfluenceArr, HighScoreArr);
				
				//欄位轉置
				for (int i=0 ; i<scoreArr.length; i++) {
					formResult.set(scoreArr[i]+"_N", formResult_n.get(scoreArr[i])== null ? null : formResult_n.get(scoreArr[i]).toString());
				}
				for (int i=0 ; i<HighGapArr.length; i++) {
					formResult.set(HighGapArr[i]+"_N", formResult_n.get(HighGapArr[i])== null ? null : formResult_n.get(HighGapArr[i]).toString());
				}
				for (int i=0 ; i<InfluenceArr.length; i++) {
					formResult.set(InfluenceArr[i]+"_N", formResult_n.get(InfluenceArr[i])== null ? null : formResult_n.get(InfluenceArr[i]).toString());
				}
				for (int i=0 ; i<itemArr.length; i++) {
					formResult.set(itemArr[i]+"_N", formResult_n.get(itemArr[i])== null ? null : formResult_n.get(itemArr[i]).toString());
				}
				for (int i=0 ; i<elseInfoArr.length; i++) {
					formResult.set(elseInfoArr[i]+"_N", formResult_n.get(elseInfoArr[i])== null ? null : formResult_n.get(elseInfoArr[i]).toString());
				}
			}
		}
		
		if (model_rn != null) {
			CapAjaxFormResult formResult_n = this.loadScore_RN(model_rn);
			formResult_n.putAll(ClsUtil.procMarkModel_R_N(model_rn));
			//J-111-0271 消金房貸3.0,計算[與最高分這差距]、[影響性]
			String varVer = Util.trim(model_rn.getVarVer());
			if(Util.equals(varVer, ClsScoreUtil.V4_0_CARD_LOAN)){
				String[] scoreArr = ClsScoreUtil.scoreArr_R_4_0;
				String[] HighGapArr = ClsScoreUtil.HighGapArr_R_4_0;
				String[] InfluenceArr = ClsScoreUtil.InfluenceArr_R_4_0;
				String[] HighScoreArr = ClsScoreUtil.HighScoreArr_R_4_0;
				String[] itemArr = ClsScoreUtil.itemArr_R_4_0;
				String[] elseInfoArr = ClsScoreUtil.elseInfoArr_R_4_0;
				
				this.newScoreModel_01(formResult_n,scoreArr,HighGapArr,InfluenceArr, HighScoreArr);
				
				//欄位轉置
				for (int i=0 ; i<scoreArr.length; i++) {
					formResult.set(scoreArr[i]+"_N", formResult_n.get(scoreArr[i])== null ? null : formResult_n.get(scoreArr[i]).toString());
				}
				for (int i=0 ; i<HighGapArr.length; i++) {
					formResult.set(HighGapArr[i]+"_N", formResult_n.get(HighGapArr[i])== null ? null : formResult_n.get(HighGapArr[i]).toString());
				}
				for (int i=0 ; i<InfluenceArr.length; i++) {
					formResult.set(InfluenceArr[i]+"_N", formResult_n.get(InfluenceArr[i])== null ? null : formResult_n.get(InfluenceArr[i]).toString());
				}
				for (int i=0 ; i<itemArr.length; i++) {
					formResult.set(itemArr[i]+"_N", formResult_n.get(itemArr[i])== null ? null : formResult_n.get(itemArr[i]).toString());
				}
				for (int i=0 ; i<elseInfoArr.length; i++) {
					formResult.set(elseInfoArr[i]+"_N", formResult_n.get(elseInfoArr[i])== null ? null : formResult_n.get(elseInfoArr[i]).toString());
				}
			}
		}
		
		return formResult;
	}
	
	public void newScoreModel_01(CapAjaxFormResult formResult, String[] scoreArr, 
			String[] HighGapArr, String[] InfluenceArr, String[] HighScoreArr){
		//Step2. 取得5個因子中，分數最高的那一個(順便整一下參數，新版本小數點很多，去除多餘的0)
//		BigDecimal highGap = BigDecimal.ZERO;
//		for (int i=0 ; i<scoreArr.length; i++) {
//			String score_str = Util.trim(formResult.get(scoreArr[i]));
//				BigDecimal score = BigDecimal.ZERO;
//			if(Util.notEquals(score_str, "")){
//				score = new BigDecimal(score_str);
//			}
//			if(highGap.compareTo(score) < 0){
//				highGap = score;
//			};
//		}
		
		//Step3. 根據取得的最高分，開始計算[與最高分差距]，並同時加總與最高分差距，後續計算影響性
		BigDecimal ttlHighGap = BigDecimal.ZERO;
		for (int i=0 ; i<scoreArr.length; i++) {
			String highGap_s = HighScoreArr[i];
			BigDecimal highGap = new BigDecimal(highGap_s);
			String score_str = Util.trim(formResult.get(scoreArr[i]));
			BigDecimal score = BigDecimal.ZERO;
			if(Util.notEquals(score_str, "")){
				score = new BigDecimal(score_str).stripTrailingZeros();
			}
			formResult.set(scoreArr[i], score);
			BigDecimal highGapScore = (score.subtract(highGap)).setScale(6).stripTrailingZeros();
			if(highGapScore.compareTo(BigDecimal.ZERO) == 0){ //不知道為什麼，0.000000 沒辦法正常stripTrailingZeros 為 0
				highGapScore = new BigDecimal("0");
			}
			formResult.set(HighGapArr[i], highGapScore);
			ttlHighGap = ttlHighGap.add(highGapScore);
		}
		//影響性
		//Step4. [與最高分差距]及[與最高分差距總額]，後續計算影響性(%)
		for (int i=0 ; i<HighGapArr.length; i++) {
			String highgap_str = Util.trim(formResult.get(HighGapArr[i]));
			BigDecimal highgap = BigDecimal.ZERO;
			if(Util.notEquals(highgap_str, "")){
				highgap = new BigDecimal(highgap_str);
			}
			BigDecimal highGapInf = highgap.multiply(new BigDecimal("100")).divide(ttlHighGap,6).setScale(0,BigDecimal.ROUND_HALF_UP);
			formResult.set(InfluenceArr[i], highGapInf);
		}
		formResult.set("ttlHighGap", ttlHighGap);
	}
	@Override
	public C101S02C findC101S02C(String mainId) {
		return c101s02cDao.findC101S02C_idDup_latestOne(mainId);
	}

	@Override
	public C120S02C findC120S02C(String mainId) {
		return c120s02cDao.findC120S02C_idDup_latestOne(mainId);
	}

	@Override
	public Brmp005O getAutoCheck(Brmp005O brmp005o_obj) throws CapException {
		final Map<String, String> statusDesc = new HashMap<String, String>();
		statusDesc.put("A", "通過");
		statusDesc.put("Q", "人工審核");
		statusDesc.put("D", "不通過");
		final Map<String, String> megaDataDesc = new HashMap<String, String>();
		megaDataDesc.put("1", "是");
		megaDataDesc.put("0", "無資料");
		megaDataDesc.put("-1", "未查詢");
		megaDataDesc.put("null", "N.A");
		final Map<String, String> inputDesc = new HashMap<String, String>();
		inputDesc.put("1", "是");
		inputDesc.put("0", "否");
		inputDesc.put("null", "N.A");
		final Map<String, String> newStatusOrder = new HashMap<String, String>();
		newStatusOrder.put("A", "3");
		newStatusOrder.put("Q", "2");
		newStatusOrder.put("D", "1");
		final Map<String, String> hasDataDesc = new HashMap<String, String>();
		hasDataDesc.put("1", "有資料");
		hasDataDesc.put("0", "無資料");
		hasDataDesc.put("null", "N.A");
		final Map<String, String> matchDesc = new HashMap<String, String>();
		matchDesc.put("1", "不相符");
		matchDesc.put("0", "相符");
		matchDesc.put("null", "N.A");

		//消金處有定義排序順序
		List<Brmp005O.Brmp005O_result_policyObj> sort = brmp005o_obj.getResult().getPolicyResult();
		Collections.sort(sort, new Comparator<Brmp005O.Brmp005O_result_policyObj>() {
			@Override
			public int compare(Brmp005O.Brmp005O_result_policyObj o1, Brmp005O.Brmp005O_result_policyObj o2) {
				// 未符合>Missing>不適用>正常符合
				if (newStatusOrder.get(o1.getActionType()).compareTo(newStatusOrder.get(o2.getActionType())) == 0) {
					// 再依代碼排序
					return o1.getPolicyCode().compareTo(o2.getPolicyCode());
				} else {
					return newStatusOrder.get(o1.getActionType()).compareTo(newStatusOrder.get(o2.getActionType()));
				}
			}
		});

		for (Brmp005O.Brmp005O_result_policyObj obj:sort) {
			String showWord = obj.getShowWord();
//			String aTom = obj.getaTom();
			Pattern pattern = Pattern.compile("\\{(.*?)\\}");
			Matcher matcher = pattern.matcher(showWord);

			List<String> extractedTextList = new ArrayList<String>();

			// Extract text and add to list
			while (matcher.find()) {
				extractedTextList.add(matcher.group(1));
			}
			for (String param:extractedTextList) {
				if (Util.isNotEmpty(param)) {
					String[] parameter = StringUtils.split(param, ".");
					if (parameter.length>1) {
						String param_aTom = parameter[0];
						String param_key = parameter[1];

						JSONObject factorObj = JSONObject.fromObject(brmp005o_obj.getResult().getPolicyFactor());
						if (factorObj!=null) {
							JSONObject atom = factorObj.optJSONObject(param_aTom);
							Iterator<String> keys = atom.keys();
							while(keys.hasNext()) {
								String key = keys.next();
								if (!Util.isEmpty(key) && Util.equals(key,param_key)) {
									String value = atom.optString(key);
									if (Util.equals(key.toUpperCase(),"MEGANEGATIVECREDIT")
											|| Util.equals(key.toUpperCase(),"MEGAREJECTCUST")
											|| Util.equals(key.toUpperCase(),"MEGAREFUNDCUST")) {
										value = megaDataDesc.get(atom.optString(key));
									} else if (Util.equals(key.toUpperCase(),"BANKINGINTERESTEDPERSON")
											|| Util.equals(key.toUpperCase(),"FINANCIALCONTROLINTERESTEDPERSON")
											|| Util.equals(key.toUpperCase(),"DECLINERECORDFLAG")) {
										value = inputDesc.get(atom.optString(key));
									} else if (Util.equals(key.toUpperCase(),"HASBADNEW")) {
										value = hasDataDesc.get(atom.optString(key));
									}
									else if (Util.equals(key.toUpperCase(),"HASIDCARDREPLACE")) {
										value = matchDesc.get(atom.optString(key));
									}
									//判斷數字大於0的應該就是金額加上comma
									if (Util.parseInt(value) >0) {
										value = NumConverter.addComma(value);
									}
									showWord = showWord.replace("{"+ param_aTom+"."+key +"}",value);
									obj.setShowWord(showWord);
								}
							}
						}
					}
				}
			}

			obj.setActionType(statusDesc.get(obj.getActionType()));
		}
		return brmp005o_obj;
	}

	@Override
	public int getAge(Date birthday) {
		int age = 0;
		try{
			Calendar now = Calendar.getInstance();
			now.setTime(new Date());//當前時間
			
			Calendar birth = Calendar.getInstance();
			birth.setTime(birthday);
			
			if(birth.after(now)){//若生日在當前時間之後,回傳0
				return 0;
			} else {
				age = now.get(Calendar.YEAR) - birth.get(Calendar.YEAR);
				if (now.get(Calendar.MONTH) < birth.get(Calendar.MONTH)) {
					age--;
				} else if (now.get(Calendar.MONTH) == birth.get(Calendar.MONTH)
				&& now.get(Calendar.DAY_OF_MONTH) < birth.get(Calendar.DAY_OF_MONTH)) {
					age--;
				}
			}
		} catch (Exception e){
			return 0;
		}
		
		return age;
	}
}
