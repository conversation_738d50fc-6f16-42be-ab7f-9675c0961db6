/**
 * 個金約據書主明細js
 */
var initDfd = $.Deferred();
var initsM06 = {
	// handler name
    fhandle: "lms9990m06formhandler",
	ghandle: "lms1405gridhandler",
    
	// grid method
	gridQuery : "queryL140m01a",
	
	// add method
	addAction: "saveC999m01a",
	queryAction: "queryCustData"
};
$(function() {
	$.form.init({
        formHandler: initsM06.fhandle,
        formPostData:{//把form上貼上資料
        	formAction : initsM06.queryAction,
        	srcMainId : responseJSON.srcMainId,
        	txCode : responseJSON.txCode
        },
		loadSuccess:function(json){
			initDfd.resolve(json);
		}
    });//close form init
		
	var btn = $("#buttonPanel");
	btn.find("#btnAdd").click(function(){
		openAdd();
	}).end().find("#btnDelete").click(function(){
        var row = $("#gridViewBox").getGridParam('selrow');
        var list = "";
        var data = $("#gridViewBox").getRowData(row);
        list = data.oid;
        if (list == undefined) {
			// includeId.selData=請選擇一筆資料！！
            CommonAPI.showMessage(i18n.def["includeId.selData"]);
            return;
        }
        // confirmDelete=是否確定刪除?
		CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
		if (b) {					
		        $.ajax({
		            type: "POST",
		            handler: "lms9990m06formhandler",
		            data: {
		                formAction: "deleteC999m01a",
		                list: list
		            },
		        }).done(function(responseData){
					$("#gridViewBox").trigger("reloadGrid");// 更新Grid內容					
				});
			}				
		});		
	});
	
	var amountGridViewBox=$("#amountGridViewBox").iGrid({
		handler : initsM06.ghandle,
		height : 225,
		sortname : 'cntrNo',
		postData : {
			formAction : ""
		},
		multiselect : true,
		rowNum : 15,
		colModel : [  {
			colHeader :i18n.lms9990m06['L140M01A.custId'],// "借款人統編",
			name : 'custId',
			width : 120,
			align: "center",
			sortable : true
		}, {
			colHeader : i18n.lms9990m06['L140M01A.dupNo'],// "重複序號",
			name : 'dupNo',
			width : 60,
			align: "left",
			sortable : true
		}, {
			colHeader : i18n.lms9990m06['L140M01A.custName'],// "借款人名稱",
			name : 'custName',
			width : 140,
			align: "center",
			sortable : true
		}, {
			colHeader : i18n.lms9990m06['L140M01A.cntrNo'],// "額度序號",
			name : 'cntrNo',
			width : 90,
			align: "center",
			sortable : true
		}, {
			colHeader : i18n.lms9990m06['L140M01A.commSno'],// "共用額度序號",
			name : 'commSno',
			width : 90,
			align: "center",
			sortable : true
		}, {
			name : 'mainId',
			hidden : true
		}, {
			name : 'oid',
			hidden : true
		}]
	});	
});

/***檢核使用者所選約據書種類是否合法***/
function checkColumn(chkColId) {
	if($("#contractType").val() == ''){
		return CommonAPI.showErrorMessage(i18n.lms9990m06['C999M01AM06.message02']);
	}else{
		var chkColIdVal = $("#" + chkColId).val();
		if(chkColIdVal == ''){
			return CommonAPI.showErrorMessage(i18n.lms9990m06['C999M01AM06.message05']);
		}else if(chkColIdVal == "W31"){
			// 政策性留學貸款契約書
			$("#contractKind").val("A");
			return true;			
		}else if(chkColIdVal == "W01"){
			// 一般契約書
			$("#contractKind").val("A");
			return true;			
		}else{
			// 直接產生檔案供下載			
			var id= $("#amountGridViewBox").getGridParam('selarrrow');
			var oidFor140 = "";
			var count = 0;
			if(id.length <=0){
				// 請選擇資料
				return CommonAPI.showErrorMessage(i18n.def["grid_selector"]);
			}else if(id.length > 1){
				// 請選擇一筆資料!!
				return CommonAPI.showErrorMessage(i18n.def["includeId.selData"]);
			}
			for ( var i = 0; i < id.length; i++) {
				if(id[i]!=""){
					var datas = $("#amountGridViewBox").getRowData(id[i]);
					oidFor140 =  oidFor140 + datas.oid + "|";								
					count++;
				}
			}			
			if(oidFor140.length != 0){
				oidFor140 = oidFor140.substring(0,oidFor140.length-1);
			}
			addDoc(chkColIdVal);
/*
			$.capFileDownload({					     
				handler:"lmsdownloadformhandler",
	            data: {
					oidFor140 : oidFor140,
		            mainId: responseJSON.mainId,
		            contractType: $("#contractType").val(),
					contractKind : $("#contractKind").val(),
					chkColIdVal : chkColIdVal,
		            fileDownloadName: "LMS9990" + chkColIdVal + "C.doc",
		            serviceName: "lms9990doc02service"
	            }
	        });
*/
			return false;			
		}
	}	
	return true;
}

/***開啟新增視窗***/
function openAdd() {
	var addForm = $("#addForm"); 
	//初始化
	addForm.reset();
	addForm.find("select").val("");
	addForm.find(".initSelect").hide();
	
	filter1Grid({
		mainId : responseJSON.srcMainId,
		itemType : "1"
	 	});
	$("#amountBox").thickbox({	// 使用選取的內容進行彈窗
		title : i18n.lms9990m06['C999M01AM06.addTitle'],
	    width : 700,
	    height : 515,
	    modal : true,
	    valign:"bottom",
		align:"center",
		i18n:i18n.def,
	    buttons: {
	    	sure: function() {
	    		//J-112-0586_05097_B1002 依據簽會-2023-2192「Web eLoan-Checkmarx弱點改善會議」按季追蹤弱點修正進度
	    		if(checkColumn(DOMPurify.sanitize($('#chkColId').val()))){
		    		addDoc(null);
	    		}
			},            			    	
			cancel: function() {
				$.thickbox.close();
			}
		}
	});
}

/**
 * 新增約據書
 */
function addDoc(chkColIdVal) {
	var isFile = false;
	if(chkColIdVal){
		isFile = true;
	}
	var id= $("#amountGridViewBox").getGridParam('selarrrow');
	var mainIdFor140 = "";
	var count = 0;
	var l140CustId = "";
	var l140DupNo = "";
	var l140CustName = "";
	if(id.length <=0){
		// 請選擇資料
		return CommonAPI.showErrorMessage(i18n.def["grid_selector"]);
	}
	for ( var i = 0; i < id.length; i++) {
		if(id[i]!=""){
			var datas = $("#amountGridViewBox").getRowData(id[i]);
			mainIdFor140 =  mainIdFor140 + datas.mainId + "|";
			if(l140CustId == ""){
				l140CustId = datas.custId;
			}
			if(l140DupNo == ""){
				l140DupNo = datas.dupNo;
			}
			if(l140CustName == ""){
				l140CustName = datas.custName;
			}								
			count++;
		}
	}

	if($("#contractType").val() == 'B' && id.length > 1){
		// grid.maxSelrow=${0} 最多只可選擇${1}筆資料。
		return CommonAPI.showErrorMessage
		(i18n.def["grid.maxSelrow"].replace('${0}','').replace('${1}','1'));
	}else if(mainIdFor140.length != 0){
		mainIdFor140 = mainIdFor140.substring(0,mainIdFor140.length-1);
	}

	$.ajax({	//載入Form資料(客戶名稱)
        handler: initsM06.fhandle,
		action: initsM06.addAction,
		data : {
			srcMainId : responseJSON.srcMainId,
			contractType : $("#contractType").val(),
			contractKind : $("#contractKind").val(),
			mainIdFor140 : mainIdFor140,
			l140CustId : l140CustId,
			l140DupNo : l140DupNo,
			l140CustName : l140CustName,
			txCode : responseJSON.txCode,
			isFile : isFile,
			chkColIdVal : chkColIdVal
		},				
	}).done(function(json){
		$("#gridViewBox").trigger("reloadGrid");
		if(!isFile){
			var actionUrl;
			var $contractKind = $("#contractKind");
			var $contractType = $("#contractType");
			// 依照種類開啟個金約據書子畫面
			if($contractType.val() == 'A'){
				// 一般
				if ($contractKind.val() == 'A') {
					// 契約書
					actionUrl = '../lms9990m07/01';
				}				
			}else if($contractType.val() == 'B'){
				// 政策性留學貸款
				if ($contractKind.val() == 'A') {
					// 契約書
					actionUrl = '../lms9990m08/01';
				}
			}
			if($contractKind.val() == 'A'){
				$.form.submit({
					url : actionUrl,
					data : {
						mainId : json.mainId,
						mainOid : json.mainOid,
						contractType : json.contractType,
						mainDocStatus : json.mainDocStatus,
						srcMainId : json.srcMainId,
						txCode : responseJSON.txCode
					},
					target : "_blank"
				});				
			}				
		}		
	}).fail(function(responseData){
		// '額度明細表借款人須相同。'錯誤訊息不需顯示'新增失敗'訊息
		if(responseData.responseText.indexOf("EFD0015") == -1){
			return CommonAPI.showErrorMessage(i18n.lms9990m06['C999M01AM06.message01']);
		}					
	});
}

/**
 * grid 資料篩選
 */
function filter1Grid(sendData){
	 $("#amountGridViewBox").jqGrid("setGridParam", {
		 postData : $.extend({
				formAction: initsM06.gridQuery,
				mainId : responseJSON.srcMainId,
				itemType : "1"
		},sendData || {}), 
		search: true
	}).trigger("reloadGrid");	
}


function choiceKind(){
/*
	hideType();
	$('#contractKind').empty();
	var size = $('#contractKind_TEMP option').size();
	for(var i = 0 ; i < size ; i++){
		var text = $("#contractKind_TEMP option[index='" + i + "']").text();
		var val = $("#contractKind_TEMP option[index='" + i + "']").val();
		$('#contractKind').append("<option value='" + val + "'>" + text + "</option>");
	}
*/
	$("#addForm").find(".initSelect").show();
	hideType();
	if($('#contractType').val() == "A"){
		// 一般
		choiceType2();
		$('#type1').show();
		$('#type1a').show();
	}else if($('#contractType').val() == "B"){
		// 政策性留學貸款
		choiceType2();
		$('#type2').show();
		$('#type2H').show();
	}
}

function choiceType2(){
	// init select
	$('#type2').hide();
	$('#type2A').hide();
	$('#type2B').hide();
	$('#type2C').hide();
	$('#type2D').hide();
	//$('#type2E').hide();
	$('#type2F').hide();
	$('#type2G').hide();
	$('#type2H').hide();	
	
	var $chkColId = $('#chkColId');	
	var contractTypeVal = $('#contractType').find("option:selected").val();
	var contractKindVal = $('#contractKind').find("option:selected").val();
	if((contractTypeVal != undefined && contractTypeVal != null && contractTypeVal != '')){
		$('#type2').show();
		switch(contractTypeVal){
			case 'A':
				if(contractKindVal != undefined && contractKindVal != null && contractKindVal != ''){
					switch(contractKindVal){
						case 'A':
							$('#type2A').show();
							$chkColId.val("contractType2A");
							break;
						case 'B':
							$('#type2B').show();
							$chkColId.val("contractType2B");
							break;
						case 'C':
							$('#type2C').show();
							$chkColId.val("contractType2C");
							break;
						case 'D':
							$('#type2D').show();
							$chkColId.val("contractType2D");
							break;
						case 'E':
							$('#type2F').show();
							$chkColId.val("contractType2F");
							break;
						case 'F':
							$('#type2G').show();
							$chkColId.val("contractType2G");
							break;
						default:
					}					
				}
				break;
			case 'B':
				$('#type2H').show();
				$chkColId.val("contractType2H");
				break;
			default:
		}
	}
}

function hideType(){
	$('#type1').hide();
	$('#type1a').hide();
	$('#type2B').hide();
	$('#type2').hide();
	$('#type2A').hide();
	$('#type2B').hide();
	$('#type2C').hide();
	$('#type2D').hide();
	//$('#type2E').hide();
	$('#type2F').hide();
	$('#type2G').hide();
	$('#type2H').hide();
}



