/* 
 * C122S01B.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 線上增貸核貸批號明細 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C122S01B", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId","batchNo","cntrNoMainId","seq"}))
public class C122S01B extends GenericBean implements IDataObject, IDocObject {
	/*
	 J-108-0187 卡友信貸統計表
	 J-109-0232 線上貸款 
	 */
	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 批號版本 **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="BATCHNO", columnDefinition="DECIMAL(3,0)")
	private Integer batchNo;

	/** 額度ID **/
	@Size(max=32)
	@Column(name="CNTRNOMAINID", length=32, columnDefinition="CHAR(32)")
	private String cntrNoMainId;

	/** 
	 * 產品序號<p/>
	 * 同L140S02A.seq
	 */
	@Digits(integer=5, fraction=0, groups = Check.class)
	@Column(name="SEQ", columnDefinition="DECIMAL(5,0)")
	private Integer seq;

	/** 額度序號 **/
	@Size(max=12)
	@Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)")
	private String cntrNo;

	/** 
	 * 產品種類<p/>
	 * Ref L140S02A
	 */
	@Size(max=2)
	@Column(name="PRODKIND", length=2, columnDefinition="CHAR(2)")
	private String prodKind;

	/** 
	 * 動撥幣別<p/>
	 * Ref L140S02A
	 */
	@Size(max=3)
	@Column(name="LOANCURR", length=3, columnDefinition="CHAR(3)")
	private String loanCurr;

	/** 
	 * 動撥金額<p/>
	 * Ref L140S02A
	 */
	@Digits(integer=13, fraction=0, groups = Check.class)
	@Column(name="LOANAMT", columnDefinition="DECIMAL(13,0)")
	private BigDecimal loanAmt;

	/** 
	 * 利率<p/>
	 * Ref L140S02A
	 */
	@Size(max=4500)
	@Column(name="RATEDESC", length=4500, columnDefinition="VARCHAR(4500)")
	private String rateDesc;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得批號版本 **/
	public Integer getBatchNo() {
		return this.batchNo;
	}
	/** 設定批號版本 **/
	public void setBatchNo(Integer value) {
		this.batchNo = value;
	}

	/** 取得額度ID **/
	public String getCntrNoMainId() {
		return this.cntrNoMainId;
	}
	/** 設定額度ID **/
	public void setCntrNoMainId(String value) {
		this.cntrNoMainId = value;
	}

	/** 
	 * 取得產品序號<p/>
	 * 同L140S02A.seq
	 */
	public Integer getSeq() {
		return this.seq;
	}
	/**
	 *  設定產品序號<p/>
	 *  同L140S02A.seq
	 **/
	public void setSeq(Integer value) {
		this.seq = value;
	}

	/** 取得額度序號 **/
	public String getCntrNo() {
		return this.cntrNo;
	}
	/** 設定額度序號 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/** 
	 * 取得產品種類<p/>
	 * Ref L140S02A
	 */
	public String getProdKind() {
		return this.prodKind;
	}
	/**
	 *  設定產品種類<p/>
	 *  Ref L140S02A
	 **/
	public void setProdKind(String value) {
		this.prodKind = value;
	}

	/** 
	 * 取得動撥幣別<p/>
	 * Ref L140S02A
	 */
	public String getLoanCurr() {
		return this.loanCurr;
	}
	/**
	 *  設定動撥幣別<p/>
	 *  Ref L140S02A
	 **/
	public void setLoanCurr(String value) {
		this.loanCurr = value;
	}

	/** 
	 * 取得動撥金額<p/>
	 * Ref L140S02A
	 */
	public BigDecimal getLoanAmt() {
		return this.loanAmt;
	}
	/**
	 *  設定動撥金額<p/>
	 *  Ref L140S02A
	 **/
	public void setLoanAmt(BigDecimal value) {
		this.loanAmt = value;
	}

	/** 
	 * 取得利率<p/>
	 * Ref L140S02A
	 */
	public String getRateDesc() {
		return this.rateDesc;
	}
	/**
	 *  設定利率<p/>
	 *  Ref L140S02A
	 **/
	public void setRateDesc(String value) {
		this.rateDesc = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
	
	/**
	 * 顯示用欄位
	 */
	@Transient
	private String link;

	public void setLink(String value) {
		this.link = value;
	}

	public String getLink() {
		return link;
	}
}
