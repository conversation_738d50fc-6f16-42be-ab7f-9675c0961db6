package com.mega.eloan.lms.fms.handler.grid;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.formatter.CodeTypeFormatter;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dw.service.DwLnquotovService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.fms.service.LMS7500Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.L140MM3A;
import com.mega.eloan.lms.model.L140MM3C;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapFormatException;
import tw.com.iisi.cap.formatter.IBeanFormatter;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 都更危老註記維護作業
 * </pre>
 * 
 * @since 2014/08/28
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Scope("request")
@Controller("lms7500gridhandler")
public class LMS7500GridHandler extends AbstractGridHandler {

	@Resource
	LMS7500Service lms7500Service;
	
	@Resource
	CodeTypeService codeTypeService;
	
	@Resource
	DocFileService docFileService;
	
	@Resource
	EloandbBASEService eloandbService;
	
	@Resource
	DwLnquotovService dwLnquotovService;
	
	@Resource
	MisdbBASEService misdbBASEService;

	@Resource
	LMSService lmsService;

	/**
	 * 都更危老註記維護作業grid
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL140mm3a(ISearch pageSetting,
			PageParameters params) throws CapException {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String docStatus = Util.nullToSpace(params
				.getString(EloanConstants.DOC_STATUS));

		String[] docStatusArray = docStatus
				.split(UtilConstants.Mark.SPILT_MARK);

		pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
				docStatusArray);// 取得文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, 
				UtilConstants.Field.目前編製行, user.getUnitNo());

		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		Page<? extends GenericBean> page = lms7500Service.findPage(
				L140MM3A.class, pageSetting);

		List<L140MM3A> l140mm3alist = (List<L140MM3A>) page.getContent();

		return new CapGridResult(l140mm3alist, page.getTotalRow());

	}

	@SuppressWarnings("unchecked")
	public CapGridResult queryL140mm3c(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "flag",
				params.getString("flag"));

		Page<? extends GenericBean> page = lms7500Service.findPage(
				L140MM3C.class, pageSetting);

		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();

		formatter
				.put("estateType",
						new CodeTypeFormatter(
								codeTypeService,
								"estateType",
								com.mega.eloan.common.formatter.CodeTypeFormatter.ShowTypeEnum.ValSpaceDesc));

		Map<String, String> estateType = codeTypeService
				.findByCodeType("estateType");
		Map<String, String> estateSubType = codeTypeService
				.findByCodeType("estateSubType");

		class EstateTypeFormatter implements IBeanFormatter {
			private static final long serialVersionUID = 2501150363189246663L;
			Map<String, String> estateType;
			Map<String, String> estateSubType;

			public EstateTypeFormatter(Map<String, String> estateType,
					Map<String, String> estateSubType) {
				this.estateType = estateType;
				this.estateSubType = estateSubType;
			}

			@Override
			public String reformat(Object in) throws CapFormatException {
				L140MM3C l140mm3c = (L140MM3C) in;
				String estateType2 = l140mm3c.getEstateType();
				if (UtilConstants.L140M01T_estatType.都更危老.equals(estateType2)) {
					return estateType2 + " " + estateType.get(estateType2) + "-"
							+ Util.trim(estateSubType.get(l140mm3c.getEstateSubType()));
				} else {
					return estateType2 + " " + estateType.get(estateType2);
				}

			}

		}

		formatter.put("estateType", new EstateTypeFormatter(estateType,
				estateSubType));

		formatter.put("checkYN", new IFormatter() {

			@Override
			public String reformat(Object in) throws CapFormatException {
				String txt = (String) in;
				if ("Y".equals(txt)) {
					return "O";
				}
				return "X";
			}
		});

		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow(), formatter);

		return result;
	}
	
	public CapGridResult queryFile(ISearch pageSetting, PageParameters params) throws CapException {

		// 查這份文件的MinId
		String mainId = Util.nullToSpace(params.getString("mainId"));
		String fieldId = params.getString("fieldId");
		boolean needCngName = params.getBoolean("needCngName");
		boolean needBranch = params.getBoolean("needBranch");
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "fieldId",
				fieldId);
		Page<DocFile> page = docFileService.readToGrid(pageSetting);
		
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}
	
	
	/**
	 * 查詢都更危老註記維護作業grid(已覆核)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */	
	public CapMapGridResult queryGetCntrno(ISearch pageSetting,
			PageParameters params) throws CapException {
		String custId = Util.nullToSpace(params.getString("custId"));
		String dupNo = Util.nullToSpace(params.getString("dupNo"));

		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		List<String> cntrnoList = new ArrayList<String>();
		
		if(!custId.isEmpty() && !dupNo.isEmpty()){
			//簽報書的額度明細表額度序號
			List<Map<String, Object>> l140m01as = eloandbService.selDistinctCntrnoByCustidDupno(custId, dupNo);
			for (Map<String, Object> l140m01a : l140m01as) {
				Map<String, Object> row = new HashMap<String, Object>();
	
				String cntrNo = Util.trim(l140m01a.get("CNTRNO"));
				if(!cntrNo.isEmpty()){
					if(cntrnoList != null && cntrnoList.contains(cntrNo)){
						//排除重複
					} else {
						cntrnoList.add(cntrNo);	
						row.put("cntrNo", cntrNo);
						list.add(row);
					}
				}
			}
			
			// 海外查 DW_ASLNQUOT
			List<Map<String, Object>> dwLncntrovs = dwLnquotovService.selDistinctCntrnoByCustidDupno(custId, dupNo);
			for (Map<String, Object> lncntr : dwLncntrovs) {
				Map<String, Object> row = new HashMap<String, Object>();
	
				String cntrNo = Util.trim(lncntr.get("CNTRNO"));
				if(!cntrNo.isEmpty()){
					if(cntrnoList != null && cntrnoList.contains(cntrNo)){
						//排除重複
					} else {
						cntrnoList.add(cntrNo);	
						row.put("cntrNo", cntrNo);
						list.add(row);
					}
				}
			}
			
			custId = Util.addSpaceWithValue(custId, 10);
			//遠匯	LNF197		非遠匯	LNF020
			List<Map<String, Object>> lnfs = misdbBASEService.selDistinctCntrnoByCustidDupno(custId, dupNo);
			for (Map<String, Object> lnf : lnfs) {
				Map<String, Object> row = new HashMap<String, Object>();
	
				String cntrNo = Util.trim(lnf.get("CNTRNO"));
				if(!cntrNo.isEmpty()){
					if(cntrnoList != null && cntrnoList.contains(cntrNo)){
						//排除重複
					} else {
						cntrnoList.add(cntrNo);	
						row.put("cntrNo", cntrNo);
						list.add(row);
					}
				}
			}
			
			//排序
			Collections.sort(list, new Comparator<Map<String, Object>>() {
				public int compare(Map<String, Object> o1, Map<String, Object> o2) {
					String name1 = o1.get("cntrNo").toString(); 
					String name2 = o2.get("cntrNo").toString();
	                return name1.compareTo(name2);
				}
			});
		}
		
		return new CapMapGridResult(list, list.size());
	}

    @SuppressWarnings("unchecked")
    public CapMapGridResult queryAdcList(ISearch pageSetting, PageParameters params)
            throws CapException {
		String queryType = Util.trim(params.getString("queryType", ""));
		String queryCustId = Util.trim(params.getString("queryCustId", ""));
		String queryDupNo = Util.trim(params.getString("queryDupNo", ""));
		String queryCntrNo = Util.trim(params.getString("queryCntrNo", ""));
		List<Map<String, Object>> newList = new ArrayList<Map<String, Object>>();
		Map<String, Object> data = null;
		if(Util.isNotEmpty(queryType)) {
			HashSet<String> adcCaseNoSet = lmsService.getAdcCaseNoList(queryType, queryCustId, queryDupNo, queryCntrNo, "");
			for (String no : adcCaseNoSet) {
				data = new HashMap<String, Object>();
				data.put("adcCaseNo", no);
				newList.add(data);
			}
		}
		Page<Map<String, Object>> pages = LMSUtil.setPageMap(newList, pageSetting);
        return new CapMapGridResult(pages.getContent(), newList.size());
    }
    
    @DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public CapGridResult filterL140mm3aByEditCompletedCase(ISearch pageSetting, PageParameters params) throws CapException {
		
    	MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String cntrNo = Util.trim(params.getString("cntrNo"));
		String custId = Util.trim(params.getString("custId"));
		
		String docStatus = Util.nullToSpace(params.getString(EloanConstants.DOC_STATUS));

		String[] docStatusArray = docStatus.split(UtilConstants.Mark.SPILT_MARK);

		pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus", docStatusArray);// 取得文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, UtilConstants.Field.目前編製行, user.getUnitNo());
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, UtilConstants.Field.刪除時間, "");
		
		if(StringUtils.isNotBlank(cntrNo)){
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		}
		
		if(StringUtils.isNotBlank(custId)){
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, UtilConstants.Field.CUSTID, custId);
		}
		
		Page<? extends GenericBean> page = lms7500Service.findPage( L140MM3A.class, pageSetting);

		@SuppressWarnings("unchecked")
		List<L140MM3A> l140mm3alist = (List<L140MM3A>) page.getContent();

		return new CapGridResult(l140mm3alist, page.getTotalRow());
	}
	
}