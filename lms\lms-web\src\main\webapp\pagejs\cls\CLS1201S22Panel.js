$(document).ready(function(){
    ilog.debug("<EMAIL>");
    var obj = API.loadCombos(["cls1141_purpose", "cls1141_resource", "cls1141_outlook"]);
    $("[name=purposeTemp]").setItems({
        space: true,
        item: obj["cls1141_purpose"],
        format: "{key}",
        size: 5
    });
    
    $("[name=resourceTemp]").setItems({
        space: true,
        item: obj["cls1141_resource"],
        format: "{key}",
        size: 5
    });
	
	$("[name=custOutlook]").setItems({
        item: obj["cls1141_outlook"],
        format: "{key}",
        size: 40
    });
	
	$("[name=guarantorOutlook]").setItems({
        item: obj["cls1141_outlook"],
        format: "{key}",
        size: 40
    });
    
    initDfd.done(function(obj){
        //於文件第一次載入時設定
        setPurpose(obj.CLSForm.purpose);
        setResource(obj.CLSForm.resource);
		setCollStatusFull(obj.CLSForm.collStatusFull);
		setCustOutlook(obj.CLSForm.custOutlook);
		setGuarantorOutlook(obj.CLSForm.guarantorOutlook);
		setScrambleBus(obj.CLSForm.scrambleBus);
		setScrambleBus4(obj.CLSForm.scrambleBus4);
		setItemdscrRLimit(obj.CLSForm.itemdscrRLimit);
		initRiskEvaluate(obj.CLSForm.showRiskQA);
		
		//UI 控制
		$("[name=collStatus]").change(function(){
            //擔保情形
			var collStatus = $("input[name='collStatus']:checked").val();
			if (collStatus == "1") {
				$("#spanCollStatus_1 input").readOnly(false);
				$("#collStatusOth").attr("disabled", true);
				$("#collStatusOth").val("");
			} else if (collStatus == "2") {
				//無 
				$("#spanCollStatus_1 input").readOnly(true);
				$("#collStatusOth").attr("disabled", true);
				$("#spanCollStatus_1 input:text").val("");
				$("#spanCollStatus_1 input:checked").attr("checked", false);
				$("#collStatusOth").val("");
				//擔保品標的相關項目直接清空後隱藏
				$("#collTarget").val("");
				$("#printCollTarget").attr("checked",false);
				$("#collTargetCount").val("0");
				var collTargetDesc = $("#collTargetDesc");
				collTargetDesc.injectData({'collTargetDesc':''},false);
				$("#collTargetDiv").hide();
			} else if (collStatus == "3") {
				//其他
				$("#spanCollStatus_1 input").readOnly(true);
				$("#collStatusOth").attr("disabled", false);
				$("#spanCollStatus_1 input:text").val("");
				$("#spanCollStatus_1 input:checked").attr("checked", false);
				//擔保品標的相關項目直接清空後隱藏
				$("#collTarget").val("");
				$("#printCollTarget").attr("checked",false);
				$("#collTargetCount").val("0");
				var collTargetDesc = $("#collTargetDesc");
				collTargetDesc.injectData({'collTargetDesc':''},false);
				$("#collTargetDiv").hide();
			}
        });
		
		$("[name=collStatusFull]").change(function(){
			var collStatusFull=[];
			var j=0;
			for(var i = 0 ; i < $("[name=collStatusFull]").length ;i++){
				var $collStatusFull=$($("[name=collStatusFull]")[i]);
				$collStatusFull.attr("checked");
				if($collStatusFull.attr("checked")){
					collStatusFull[j]=$collStatusFull.val();
					j++;
				}
			}
			if(collStatusFull == "C"){
				$("#collTargetDiv").show();
			}else{
				$("#collTarget").val("");
				$("#printCollTarget").attr("checked",false);
				$("#collTargetCount").val("0");
				var collTargetDesc = $("#collTargetDesc");
				collTargetDesc.injectData({'collTargetDesc':''},false);
				$("#collTargetDiv").hide();
			}
		});
		$("#cls1201s22_btn_import_collRealEstate").click(function(){
			$.ajax({
	            handler: "cls1141m01formhandler",
	            type: "POST",
	            dataType: "json",
	            data: {
	                formAction: "cls1201s22_btn_import_collRealEstate",
	                mainId: responseJSON.mainId
	            },
	            success: function(json){
					$("#collTarget").val(json.target);
					$("#collTargetCount").val(json.targetCount);
					var collTargetDesc = $("#collTargetDesc");
					if(json.targetCount != undefined && json.targetCount > 0){
						collTargetDesc.injectData({'collTargetDesc':json.targetDesc},false);
					}else{
						collTargetDesc.injectData({'collTargetDesc':''},false);
					}
	            }
	        });
		});
		
		$("[name=scrambleBus]").change(function(){
            //擬爭攬業務
			//alert($(this).val());
			//alert($(this).attr("checked"));
			
			if ($(this).val() == "2") {
				if ($(this).attr("checked")) {
					$("#spanScrambleBus_2 input").readOnly(false);
				}
				else {
					$("#spanScrambleBus_2 input").readOnly(true);
					$("#spanScrambleBus_2 input:text").val("");
					$("#spanScrambleBus_2 input:checked").attr("checked", false);
				}
			}
			
			if ($(this).val() == "4") {
				if ($(this).attr("checked")) {
					$("#spanScrambleBus_4 input").readOnly(false);
				}
				else {
					$("#spanScrambleBus_4 input").readOnly(true);
					$("#spanScrambleBus_4 input:text").val("");
					$("#spanScrambleBus_4 input:checked").attr("checked", false);
				}
				$("[name=scrambleBus4]").change();
			}
			
			if ($(this).val() == "5") {
				if ($(this).attr("checked")) {
					$("#scrambleBusOth").attr("disabled", false);
				}
				else {
					$("#scrambleBusOth").attr("disabled", true);
					$("#scrambleBusOth").val("");
				}
			}
        });
		
		$("[name=scrambleBus4]").change(function(){
            //擬爭攬業務-財富管理
			if ($(this).val() == "D") {
				if ($(this).attr("checked")) {
					$("#scrambleBus4DOth").readOnly(false);
				}
				else {
					$("#scrambleBus4DOth").readOnly(true);
					$("#scrambleBus4DOth").val("");
				}
			}
        });
		
		//風險綜合評估-1.借款人或保證人之年齡加計借款期間後是否未超過75歲
		$("input[name=isRiskQ1]").change(function(){	
	    	var isRiskQ1 = $("input[name='isRiskQ1']:radio:checked").val();
	    	if (isRiskQ1 == "N") {
	    		$("#riskQ1Desc").attr('disabled', false);
	    	}else{
	    		$("#riskQ1Desc").attr('disabled', true);
	    		$("#riskQ1Desc").val("");
	    	}
		});
		//風險綜合評估-2.借款人近期是否無聯徵中心密集查詢紀錄
		$("input[name=isRiskQ2]").change(function(){	
	    	var isRiskQ2 = $("input[name='isRiskQ2']:radio:checked").val();
	    	if (isRiskQ2 == "N") {
	    		$("#riskQ2Desc").attr('disabled', false);
	    	}else{
	    		$("#riskQ2Desc").attr('disabled', true);
	    		$("#riskQ2Desc").val("");
	    	}
		});
		//風險綜合評估-3.借款人、保證人經查聯合徵信中心系統組合查詢及票交所，是否皆無授信異常及票據異常記錄。
		$("input[name=isRiskQ3]").change(function(){	
	    	var isRiskQ3 = $("input[name='isRiskQ3']:radio:checked").val();
	    	if (isRiskQ3 == "N") {
	    		$("#riskQ3Desc").attr('disabled', false);
	    	}else{
	    		$("#riskQ3Desc").attr('disabled', true);
	    		$("#riskQ3Desc").val("");
	    	}
		});
		//風險綜合評估-4.若為歡喜房貸案件，借款人、保證人之任一人是否皆符合無「專項貸款再加碼機制」樣態。
		$("input[name=isRiskQ4]").change(function(){	
	    	var isRiskQ4 = $("input[name='isRiskQ4']:radio:checked").val();
	    	if (isRiskQ4 == "N") {
	    		$("#riskQ4Desc").attr('disabled', false);
	    	}else{
	    		$("#riskQ4Desc").attr('disabled', true);
	    		$("#riskQ4Desc").val("");
	    	}
		});
		//風險綜合評估-5.借款人收入是否明確且具償債能力
		$("input[name=isRiskQ5]").change(function(){	
	    	var isRiskQ5 = $("input[name='isRiskQ5']:radio:checked").val();
	    	if (isRiskQ5 == "N") {
	    		$("#riskQ5Desc").attr('disabled', false);
	    	}else{
	    		$("#riskQ5Desc").attr('disabled', true);
	    		$("#riskQ5Desc").val("");
	    	}
		});
		//風險綜合評估-6.借款人負債比是否小於60%或負債比已逾60%案件已依規減成敘做?
		$("input[name=isRiskQ6]").change(function(){	
	    	var isRiskQ6 = $("input[name='isRiskQ6']:radio:checked").val();
	    	if (isRiskQ6 == "N") {
	    		$("#riskQ6Desc").attr('disabled', false);
	    	}else{
	    		$("#riskQ6Desc").attr('disabled', true);
	    		$("#riskQ6Desc").val("");
	    	}
		});
		//風險綜合評估-7.本案是否非跨區案件?
		$("input[name=isRiskQ7]").change(function(){	
	    	var isRiskQ7 = $("input[name='isRiskQ7']:radio:checked").val();
	    	if (isRiskQ7 == "N") {
	    		$("#riskQ7Desc").attr('disabled', false);
	    	}else{
	    		$("#riskQ7Desc").attr('disabled', true);
	    		$("#riskQ7Desc").val("");
	    	}
		});
		//風險綜合評估-8.若為購置不動產案件，本案是否已落實買賣契約書審查並符合本行相關規範。
		$("input[name=isRiskQ8]").change(function(){	
	    	var isRiskQ8 = $("input[name='isRiskQ8']:radio:checked").val();
	    	if (isRiskQ8 == "N") {
	    		$("#riskQ8Desc").attr('disabled', false);
	    	}else{
	    		$("#riskQ8Desc").attr('disabled', true);
	    		$("#riskQ8Desc").val("");
	    	}
		});
		//風險綜合評估-9.若為青年安心成家購屋優惠，借款人與其配偶及未成年子女是否均無自有住宅。
		$("input[name=isRiskQ9]").change(function(){	
	    	var isRiskQ9 = $("input[name='isRiskQ9']:radio:checked").val();
	    	if (isRiskQ9 == "N") {
	    		$("#riskQ9Desc").attr('disabled', false);
	    	}else{
	    		$("#riskQ9Desc").attr('disabled', true);
	    		$("#riskQ9Desc").val("");
	    	}
		});
		//風險綜合評估-10.借款人、保證人、共同借款人、擔保物提供人及其任職公司是否皆無負面新聞資訊。
		$("input[name=isRiskQ10]").change(function(){	
	    	var isRiskQ10 = $("input[name='isRiskQ10']:radio:checked").val();
	    	if (isRiskQ10 == "N") {
	    		$("#riskQ10Desc").attr('disabled', false);
	    	}else{
	    		$("#riskQ10Desc").attr('disabled', true);
	    		$("#riskQ10Desc").val("");
	    	}
		});
		
		$("[name=isqdata]").change(function(){
			//借款人明細利害關係人
            var isqdataValue = $("input[name='isqdata']:checked").val();
			if (isqdataValue == "N") {
				$("#isqdataDesc").attr('disabled', true);
				$("#isqdataDesc").val("");
			} else {
				$("#isqdataDesc").attr('disabled', false);
			}
        });
		
		$("[name=collStatus]").change();
		$("[name=scrambleBus]").change();
		$("[name=scrambleBus4]").change();
		$("input[name=isRiskQ1]").change();
		$("input[name=isRiskQ2]").change();
		$("input[name=isRiskQ3]").change();
		$("input[name=isRiskQ4]").change();
		$("input[name=isRiskQ5]").change();
		$("input[name=isRiskQ6]").change();
		$("input[name=isRiskQ7]").change();
		$("input[name=isRiskQ8]").change();
		$("input[name=isRiskQ9]").change();
		$("input[name=isRiskQ10]").change();
    });
    
    function setPurpose(value){
		if (value == undefined) {
			return;
		}
		$("[name=purposeTemp]").val(value.split("|"));
    }
    
    function setResource(value){
		if (value == undefined) {
			return;
		}
        $("[name=resourceTemp]").val(value.split("|"));
    }
	
	function setCollStatusFull(value){
		if (value == undefined) {
			return;
		}
        $("[name=collStatusFull]").val(value.split("|"));
        if(value == "C"){
        	$("#collTargetDiv").show();
        }else{
        	$("#collTargetDiv").hide();
        }
    }
	
	function setCustOutlook(value){
		if (value == undefined) {
			return;
		}
        $("[name=custOutlook]").val(value.split("|"));
    }
	
	function setGuarantorOutlook(value){
		if (value == undefined) {
			return;
		}
        $("[name=guarantorOutlook]").val(value.split("|"));
    }
	
	function setScrambleBus(value){
		if (value == undefined) {
			return;
		}
        $("[name=scrambleBus]").val(value.split("|"));
    }
	
	function setScrambleBus4(value){
		if (value == undefined) {
			return;
		}
        $("[name=scrambleBus4]").val(value.split("|"));
    }
	
	function setItemdscrRLimit(value){
		if (value == undefined) {
			return;
		}
		var l120m01d_itemdscrR_limit = $("#l120m01d_itemdscrR_limit");
		var itemdscrR_limitDesc = "字數限制為"+value+"字";
		l120m01d_itemdscrR_limit.injectData({'l120m01d_itemdscrR_limit':itemdscrR_limitDesc},false);
		$("#l120m01d_itemdscrR").attr("maxlengthC", value);
	}
	function initRiskEvaluate(value){
		if (value == "Y") {
			$("#riskEvaluate1").hide();
			$("#riskEvaluate2").show();
        }else{
        	$("#riskEvaluate1").show();
        	$("#riskEvaluate2").hide();
        }
	}
	
	//借款人明細
    var gridview_colModel = [{
        name: 'oid',
        hidden: true //是否隱藏
    }, {
        name: 'mainId',
        hidden: true //是否隱藏
    }, {
        name: 'custId', //身分證統編
        hidden: true //是否隱藏
    }, {
        name: 'dupNo', //身分證統編重複碼
        hidden: true //是否隱藏
    }, {
        colHeader: "序號",
        align: "center",
        width: 15, 
        sortable: true, 
        name: 'seqNo'
    }, {
        colHeader: i18n.clss02c["C120M01A.custId"], //身分證統編
        align: "left",
        width: 80, 
        sortable: true, 
        name: 'custId', 
        formatter: 'click',
        onclick: openDoc
    }, {
        colHeader: i18n.clss02c["C120M01A.custName"], //借款人姓名
        align: "left",
        width: 120, 
        sortable: true, 
        name: 'custName' 
    }, {
        colHeader: '利害關係人',
        align: "center",
        width: 50,
        sortable: false, 
        name: 'isqdata'             
    }, {
        colHeader: i18n.clss02c["C120M01A.rmk"], //備註
        align: "center",
        width: 80, 
        sortable: true, 
        name: 'rmk'
    }];
    
    $("#gridview").iGrid({
        handler: 'cls1131gridhandler',
        height: 150,
        action: "queryC120s01t",
        rowNum: 15,
        sortname: "seqNo",
        sortorder: 'asc',
        colModel: gridview_colModel,
        ondblClickRow: function(rowid){
            openDoc(null, null, $("#gridview").getRowData(rowid));
        }
    });
    
	function openDoc(cellvalue, options, rowObject){
		$("#C120S01TForm").reset();
		$("#c120s01tOid").val('');
		
		$.ajax({
			type : "POST",
			handler : "cls1141m01formhandler",
			data : {
				formAction : "queryC120S01T",
				oid : rowObject.oid
			},
			success:function(responseData){
				$("#c120s01tOid").val(responseData.oid);
				$("#C120S01TForm").injectData(responseData);				
				$("#c120s01t_custid").val(responseData.custId + " " + responseData.custName);
				$("[name=isqdata]").change();
				openOder();
			}
		});
	}
	
	function openOder(){
		var openBts;
		openBts = API.createJSON([{
			key: i18n.def['sure'],
			value: function(){
				if ($("#C120S01TForm").valid()) {
					
					$.ajax({
						type : "POST",
						handler : "cls1141m01formhandler",
						data : {
							formAction : "saveC120S01T",
							oid : $("#c120s01tOid").val(),
							C120S01TForm : JSON.stringify($("#C120S01TForm").serializeData())
						},
						success:function(responseData){
							$("#gridview").trigger("reloadGrid");
						}
					});
				}
			}
		}, {
			key: i18n.def['cancel'],
			value: function(){$.thickbox.close();}
		}]);
		
		$("#detail").thickbox({
			title : "借款人明細",
			width : 850,
			height : 500,
			align : "center",
			valign : "bottom",
			model : true,
			i18n : i18n.def,
			buttons : openBts
		});
	}

	//各項費用 
	var grid = $("#L140M01RGrid").iGrid({
        height: 90,
        width: 200,
        autowidth: true,
        handler: "cls1141gridhandler",
        action: "queryL140M01R",
        rownumbers: true,
        colModel: [{
            colHeader: i18n.cls1141s05['L140M01R.002'],//"費用代碼",
            name: "feeNo",
            align: "center"
        }, {
            colHeader: i18n.cls1141s05['L140M01R.015'],//"幣別",
            width: 20, // 設定寬度
            name: "feeSwft",
            align: "center"
        }, {
            colHeader: i18n.cls1141s05['L140M01R.004'],//"費用金額",
            name: "feeAmt",
            align: "right",
            formatter: 'currency',
            formatoptions: {
                decimalSeparator: ",",
                thousandsSeparator: ",",
                decimalPlaces: 0,
                defaultValue: ""
            }
        }, {
            colHeader: i18n.cls1141s05['L140M01R.005'],//"備註",
            name: "feeMemo",
            align: "center"
        }, {
            name: "oid",
            hidden: true
        }, {
            name: "mainId",
            hidden: true
        }]
    });
	
    $("#cls1201s22_btn_import").click(function(){
		//取得筆數
		var rows = $("#gridview").getGridParam('reccount');
		
		if (rows != 'undefined' && rows != 0) {
			CommonAPI.confirmMessage("重新引入會覆蓋資料，是否繼續？",function(b){
				if(b){
					$.ajax({
			            handler: 'cls1141m01formhandler',
			            type: "POST",
			            dataType: "json",
			            data: {
			                formAction: "cls1201s22_btn_import",
			                mainId: responseJSON.mainid
			            },
			            success: function(json){
							$("#gridview").trigger("reloadGrid");
			            }
			        });
				}
			});
		} else {
			$.ajax({
	            handler: 'cls1141m01formhandler',
	            type: "POST",
	            dataType: "json",
	            data: {
	                formAction: "cls1201s22_btn_import",
	                mainId: responseJSON.mainid
	            },
	            success: function(json){
					$("#gridview").trigger("reloadGrid");
	            }
	        });
		}
    });
	
	$("#cls1201s22_occupation_import").click(function(){
		$.ajax({
	            handler: 'cls1141m01formhandler',
	            type: "POST",
	            dataType: "json",
	            data: {
	                formAction: "cls1201s22_occupation_import",
	                oid : $("#c120s01tOid").val()
	            },
	            success: function(json){
					$("#occupation").val(json.occupation);
	            }
	        });
    });
	
	$("#cls1201s22_isqdata_import").click(function(){
		$.ajax({
	            handler: 'cls1141m01formhandler',
	            type: "POST",
	            dataType: "json",
	            data: {
	                formAction: "cls1201s22_isqdata_import",
	                oid : $("#c120s01tOid").val()
	            },
	            success: function(json){
					$("#tdIsqdata").injectData(json);
					$("[name=isqdata]").change();
	            }
	        });
    });
	
	$("#upSeqNoBox").click(function(){
		upDownBox(true);
    });
	
	$("#downSeqNoBox").click(function(){
		upDownBox(false);
    });
	
	//向上或向下移動
	function upDownBox(upDown){
		var rows = $("#gridview").getGridParam('selrow');
		var list = "";
		var data;
		if (rows != 'undefined' && rows != null && rows != 0) {
			data = $("#gridview").getRowData(rows);
			list = data.oid;
		}
		if (list == "") {
			CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
			return;
		}
		
		upOrDownF(data, upDown);
	}
	
	function upOrDownF(data, upDown){
		$.ajax({
			type : "POST",
			handler : "cls1141m01formhandler",
			data : {
				formAction : "changeC120S01TSeqNum",
				detailOid : data.oid,
				upOrDown : upDown
			},
			success:function(responseData){
				$("#gridview").trigger("reloadGrid");
			}
		});
	}
});

