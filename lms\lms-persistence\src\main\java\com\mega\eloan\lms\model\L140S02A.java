/* 
 * L140S02A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.lms.validation.group.Check;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/** 個金產品種類檔 **/
@NamedEntityGraph(name = "L140S02A-entity-graph", attributeNodes = { 
		@NamedAttributeNode("l140s02c"),
		@NamedAttributeNode("l140s02e"),
		@NamedAttributeNode("l140s02j"),
		@NamedAttributeNode("l140s02f")
		})
@Entity
@Table(name = "L140S02A", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "seq" }))
public class L140S02A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * JOIN條件 L140S02C． 分段利率主檔
	 * 
	 */
	@OneToOne(cascade = CascadeType.REMOVE, fetch = FetchType.LAZY)
	@JoinColumns({
			@JoinColumn(name = "MAINID", referencedColumnName = "MAINID", insertable = false, updatable = false),
			@JoinColumn(name = "SEQ", referencedColumnName = "SEQ", insertable = false, updatable = false) })
	private L140S02C l140s02c;

	/**
	 * 取得分段利率主檔
	 * 
	 * @return L140S02C
	 */
	public L140S02C getL140S02C() {
		return l140s02c;
	}

	public void setL140S02C(L140S02C l140s02c) {
		this.l140s02c = l140s02c;
	}

	/**
	 * JOIN條件 L140S02E． 償還方式檔
	 * 
	 * 
	 */
	@OneToOne(cascade = CascadeType.REMOVE, fetch = FetchType.LAZY)
	@JoinColumns({
			@JoinColumn(name = "MAINID", referencedColumnName = "MAINID", insertable = false, updatable = false),
			@JoinColumn(name = "SEQ", referencedColumnName = "SEQ", insertable = false, updatable = false) })
	private L140S02E l140s02e;

	/**
	 * 取得償還方式檔
	 * 
	 * @return L140S02E
	 */
	public L140S02E getL140S02E() {
		return l140s02e;
	}

	public void setL140S02E(L140S02E L140S02E) {
		this.l140s02e = L140S02E;
	}

	/**
	 * JOIN條件 L140S02J． 外勞貸款檔
	 * 
	 */
	@OneToOne(cascade = CascadeType.REMOVE, fetch = FetchType.LAZY)
	@JoinColumns({
			@JoinColumn(name = "MAINID", referencedColumnName = "MAINID", insertable = false, updatable = false),
			@JoinColumn(name = "SEQ", referencedColumnName = "SEQ", insertable = false, updatable = false) })
	private L140S02J l140s02j;

	/**
	 * 取得外勞貸款檔
	 * 
	 * @return L140S02J
	 */
	public L140S02J getL140S02J() {
		return l140s02j;
	}

	public void setL140S02J(L140S02J l140s02j) {
		this.l140s02j = l140s02j;
	}

	/**
	 * JOIN條件 L140S02F． 房屋貸款檔
	 * 
	 */
	@OneToOne(cascade = CascadeType.REMOVE, fetch = FetchType.LAZY)
	@JoinColumns({
			@JoinColumn(name = "MAINID", referencedColumnName = "MAINID", insertable = false, updatable = false),
			@JoinColumn(name = "SEQ", referencedColumnName = "SEQ", insertable = false, updatable = false) })
	private L140S02F l140s02f;

	/**
	 * 取得房屋貸款檔
	 * 
	 * @return L140S02F
	 */
	public L140S02F getL140S02F() {
		return l140s02f;
	}

	public void setL140S02F(L140S02F l140s02f) {
		this.l140s02f = l140s02f;
	}

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 序號 **/
	@Digits(integer = 5, fraction = 0, groups = Check.class)
	@Column(name = "SEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer seq;

	/** 流水號 **/
	@Digits(integer = 5, fraction = 0, groups = Check.class)
	@Column(name = "SECNO", columnDefinition = "DECIMAL(5,0)")
	private Integer secNo;

	/** 額度序號 **/
	@Size(max = 12)
	@Column(name = "CNTRNO", length = 12, columnDefinition = "CHAR(12)")
	private String cntrNo;

	/**
	 * 承做性質                  select * from com.bcodetype where codetype = 'L140S02A_property'
	 * <p/>
	 * 單選：<br/>
	 * 報價|13 ( 個金無此項目<br/>
	 * 新做|1<br/>
	 * 增額|5<br/>
	 * 紓困|10<br/>
	 * 協議清償|12<br/>
	 * 減額|6<br/>
	 * 變更條件|3<br/>
	 * 續約|2<br/>
	 * 提前續約|11<br/>
	 * 展期(不良授信案)|9<br/>
	 * 流用|4 ( 個金無此項目<br/>
	 * 取消|8<br/>
	 * 不變|7
	 */
	@Size(max = 2)
	@Column(name = "PROPERTY", length = 2, columnDefinition = "VARCHAR(2)")
	private String property;

	/**
	 * 放款帳號
	 * <p/>
	 * ※若為舊案引入時，會顯示「放款帳號」，並控管「承做性質」不得選取「新做」。
	 */
	@Size(max = 14)
	@Column(name = "LOANNO", length = 14, columnDefinition = "CHAR(14)")
	private String loanNo;

	/**
	 * 開戶原因                  select * from com.bcodetype where codetype = 'L140S02A_transCode' 
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * 0: 一般案<br/>
	 * 1: 催收轉正<br/>
	 * 2: 更正前次撥款<br/>
	 * 3: 繼承案件<br/>
	 * 4: 受贈土地之土地增值稅融資<br/>
	 * 5: 遺產稅融資<br/>
	 * 6: 納稅義務人為受贈人之贈與稅融資<br/>
	 * 7: 借款人擔保品被政府徵收而改列無擔保之放款<br/>
	 * B: 個人繳回土地徵收補償款之無擔保貸款
	 */
	@Size(max = 1)
	@Column(name = "TRANSCODE", length = 1, columnDefinition = "CHAR(1)")
	private String transCode;

	/**
	 * 承接之前放款帳號
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * 當開戶原因為<br/>
	 * 1: 催收轉正<br/>
	 * 2: 更正前次撥款<br/>
	 * 3: 繼承案件<br/>
	 * 才需填入
	 */
	@Size(max = 14)
	@Column(name = "ORGLOANNO", length = 14, columnDefinition = "CHAR(14)")
	private String orgLoanNo;

	/**
	 * 授信期間選項
	 * <p/>
	 * 單選：<br/>
	 * 1.YYYY-MM-DD~ YYYY-MM-DD<br/>
	 * 2.YY年MM月<br/>
	 * 3.自核准日起YY年MM月<br/>
	 * 4.自簽約日起YY年MM月<br/>
	 * 5.其他
	 */
	@Size(max = 1)
	@Column(name = "LNSELECT", length = 1, columnDefinition = "CHAR(1)")
	private String lnSelect;

	/**
	 * 授信期間-起始日期
	 * <p/>
	 * 1.YYYY-MM-DD~ YYYY-MM-DD
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "LNFROMDATE", columnDefinition = "DATE")
	private Date lnFromDate;

	/**
	 * 授信期間-截止日期
	 * <p/>
	 * 1.YYYY-MM-DD~ YYYY-MM-DD
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "LNENDDATE", columnDefinition = "DATE")
	private Date lnEndDate;

	/**
	 * 授信期間-年數
	 * <p/>
	 * lnSelect<br/>
	 * 2.YY年MM月<br/>
	 * 3.自核准日起YY年MM月<br/>
	 * 4.自簽約日起YY年MM月
	 */
	@Digits(integer = 2, fraction = 0, groups = Check.class)
	@Column(name = "LNYEAR", columnDefinition = "DECIMAL(2,0)")
	private Integer lnYear;

	/**
	 * 授信期間-月數
	 * <p/>
	 * lnSelect<br/>
	 * 2.YY年MM月<br/>
	 * 3.自核准日起YY年MM月<br/>
	 * 4.自簽約日起YY年MM月
	 */
	@Digits(integer = 2, fraction = 0, groups = Check.class)
	@Column(name = "LNMONTH", columnDefinition = "DECIMAL(2,0)")
	private Integer lnMonth;

	/**
	 * 授信期間-其他
	 * <p/>
	 * 5.其他
	 */
	@Size(max = 350)
	@Column(name = "LNOTHER", length = 350, columnDefinition = "VARCHAR(350)")
	private String lnOther;

	/** 產品種類     select * from lms.c900m01a ||  select * from ln.lnf110 **/
	@Size(max = 2)
	@Column(name = "PRODKIND", length = 2, columnDefinition = "CHAR(02)")
	private String prodKind;

	/**
	 * 購置
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * 23/24(921案件)<br/>
	 * 1|購置(第一段)<br/>
	 * 2|購置(第二段)<br/>
	 * codetype = L140S02A_for921Case
	 */
	@Size(max = 1)
	@Column(name = "FOR921CASE", length = 1, columnDefinition = "CHAR(01)")
	private String for921Case;

	/**
	 * 科目     select * from lms.c900m01d
	 * <p/>
	 * ※會計科目代碼
	 */
	@Size(max = 8)
	@Column(name = "SUBJCODE", length = 8, columnDefinition = "VARCHAR(8)")
	private String subjCode;

	/**
	 * 團貸案名稱
	 * <p/>
	 * 60個中文字
	 */
	@Size(max = 180)
	@Column(name = "CASENAME", length = 180, columnDefinition = "VARCHAR(180)")
	private String caseName;

	/**
	 * 建案名稱
	 * <p/>
	 * 60個中文字
	 */
	@Size(max = 180)
	@Column(name = "BUILDNAME", length = 180, columnDefinition = "VARCHAR(180)")
	private String buildName;

	/** 土地坐落區-縣市 **/
	@Size(max = 10)
	@Column(name = "LANDCITY", length = 10, columnDefinition = "VARCHAR(10)")
	private String landCity;

	/** 土地坐落區-區域 **/
	@Size(max = 3)
	@Column(name = "LANDAREA", length = 3, columnDefinition = "VARCHAR(3)")
	private String landArea;

	/** 土地坐落區-大段 **/
	@Size(max = 15)
	@Column(name = "LANDPART1", length = 15, columnDefinition = "VARCHAR(15)")
	private String landpart1;

	/** 土地坐落區-小段 **/
	@Size(max = 15)
	@Column(name = "LANDPART2", length = 15, columnDefinition = "VARCHAR(15)")
	private String landpart2;

	/** 最終評等－採用借保人ID **/
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 最終評等－採用借保人重複序號 **/
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/**
	 * 最終評等－評等
	 * <p/>
	 * ※如於簽報書「借款人基本資料」頁籤重新引進所採用之借保人資料，亦同步更新本筆所採用之最終評等。
	 */
	@Size(max = 2)
	@Column(name = "GRADE1", length = 2, columnDefinition = "CHAR(2)")
	private String grade1;

	/**
	 * 是否重簽契約
	 * <p/>
	 * ※「02 行家理財－短期」且為「續約」<br/>
	 * Y/N
	 */
	@Size(max = 1)
	@Column(name = "CHGCONTRACT", length = 1, columnDefinition = "CHAR(1)")
	private String chgContract;

	/**
	 * 前已續約次數 <br/>
	 * ※「02 行家理財－短期」且為「續約」時才出現，若換約則歸0重算<br/>
	 * ※換約、短放已續約1次(含)以上、短擔已續約6次(含)以上，則都要提醒「※請通知客戶換約」 <br/>
	 * ● 程式修改申請(094)658 , J-094-0186 , 擬同意增加{本次續約次數}之提醒文字功能 惟系統無需以{最多六次}為限制. <br/>
	 * 
	 * 異動歷程：  CLS1151S01Page_zh_TW.properties  <br/>
	 * ● L140S02A.chgConTimes=［svn /branches/PRODSource］ 472 → 485(現已續約 → 前已續約) 2013-06-17 <br/>
	 * ● L140M01P.Item25     =［svn /branches/PRODSource］ 自 439 開始, 文字就都維持這樣, 未變更 <br/>
	 * OP.ONBATCH.JCL(DBJEF513) 於 2013/03/18 上版 <br/>
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "CHGCONTIMES", columnDefinition = "DECIMAL(3,0)")
	private Integer chgConTimes;

	/**
	 * 動用方式                        select * from com.bcodetype where codetype = 'L140S02A_useType'
	 * <p/>
	 * 單選：<br/>
	 * 1.憑借支書<br/>
	 * 2.憑金融卡<br/>
	 * 3.憑支票
	 */
	@Size(max = 1)
	@Column(name = "USETYPE", length = 1, columnDefinition = "CHAR(1)")
	private String useType;

	/**
	 * 應計入DBR22倍規範額度－幣別
	 * <p/>
	 * 預設：新台幣TWD
	 */
	@Size(max = 3)
	@Column(name = "DBR22CURR", length = 3, columnDefinition = "CHAR(3)")
	private String DBR22Curr;

	/**
	 * 應計入DBR22倍規範額度－金額
	 * <p/>
	 * 單位：元
	 */
	@Digits(integer = 13, fraction = 0, groups = Check.class)
	@Column(name = "DBR22AMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal DBR22Amt;

	/**
	 * 資金來源                        select codeValue,codeDesc from com.bcodetype where codetype='L140S02A_fFund'
	 * <p/>
	 * 單選：<br/>
	 * 0本行自有資金<br/>
	 * 1中美基金<br/>
	 * 4郵匯局基金<br/>
	 * 5信保基金
	 */
	@Size(max = 1)
	@Column(name = "FFUND", length = 1, columnDefinition = "CHAR(1)")
	private String fFund;

	/**
	 * 資金來源小類
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * SELECT * FROM LN.LNF070 WHERE LNF070_LNF070_TYPE=’LNF030-FUND-TYPE’ AND
	 * SUBSTR(LNF070_CODE,1,1)='資金來源代碼'
	 */
	@Size(max = 4)
	@Column(name = "DFUND", length = 4, columnDefinition = "CHAR(4)")
	private String dFund;

	/**
	 * 動撥幣別
	 * <p/>
	 * 預設：新台幣TWD
	 */
	@Size(max = 3)
	@Column(name = "LOANCURR", length = 3, columnDefinition = "CHAR(3)")
	private String loanCurr;

	/**
	 * 動撥金額
	 * <p/>
	 * 單位：元
	 */
	@Digits(integer = 13, fraction = 0, groups = Check.class)
	@Column(name = "LOANAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal loanAmt;

	/**
	 * 開辦費
	 * <p/>
	 * 單位：元
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "AGENCYAMT", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal agencyAMT;

	/**
	 * 開辦費
	 * <p/>
	 * 單位：元
	 */
	public BigDecimal getAgencyAMT() {
		return agencyAMT;
	}

	/**
	 * 開辦費
	 * <p/>
	 * 單位：元
	 */
	public void setAgencyAMT(BigDecimal agencyAMT) {
		this.agencyAMT = agencyAMT;
	}

	/**
	 * 是否搭配長擔貸款
	 * <p/>
	 * Y/N
	 */
	@Size(max = 1)
	@Column(name = "CHKUSED", length = 1, columnDefinition = "CHAR(1)")
	private String chkUsed;

	/**
	 * 融資業務分類             select codeValue,codeDesc from com.bcodetype where codetype='L140S02A_lnPurpose' 
	 * <p/>
	 * 單選：<br/>
	 * 1-個人投資理財貸款<br/>
	 * 2-購置住宅貸款（其他）<br/>
	 * 3-房屋修繕貸款（其他）<br/>
	 * L-購置住宅貸款（非自用）<br/>
	 * M-購置住宅貸款（自用）<br/>
	 * N-房屋修繕貸款<br/>
	 * O-汽車貸款<br/>
	 * P-職工福利貸款<br/>
	 * S-創業貸款<br/>
	 * U-建築融資貸款<br/>
	 * V-代墊投標保證金貸款<br/>
	 * X-參與都市更新計畫貸款<br/>
	 * Y-企業員工認購股票（或可轉換公司債）貸款<br/>
	 * Z-其他個人金融貸款
	 */
	@Size(max = 1)
	@Column(name = "LNPURPOSE", length = 1, columnDefinition = "CHAR(1)")
	private String lnPurpose;

	/**
	 * 用途別                        select codeValue,codeDesc from com.bcodetype where codetype='L140S02A_lnPurs' 
	 * <p/>
	 * 單選：<br/>
	 * 1購置不動產<br/>
	 * 2購置動產<br/>
	 * 3企業投資<br/>
	 * 4週轉金
	 */
	@Size(max = 1)
	@Column(name = "LNPURS", length = 1, columnDefinition = "CHAR(1)")
	private String lnPurs;

	/**
	 * 是否屬興建住宅
	 * <p/>
	 * 單選：<br/>
	 * 1.是-興建住宅自用<br/>
	 * 2.是-興建住宅非自用<br/>
	 * N.否-非興建住宅<br/>
	 * => 影響 elf501_residential , lnf030_residential <br/>
	 * ==> LOAN.PROD.SOURCE(LNSR136) 會用 lnf030_residential 去決定 LNF154_LOAN_CLASS 是 '07-個人戶辦理土地融資及建融融資' 或 '14-土地貸款' <br/>
	 * ==> 參考 LLMLN273 , 欄位 LNF154_CLS_CLASS <br/>
	 * ====> 影響 JCIC報送 欄位  "建築貸款註記" 
	 */
	@Size(max = 1)
	@Column(name = "RESIDENTIAL", length = 1, columnDefinition = "CHAR(1)")
	private String residential;

	/**
	 * 引介行員代號(l140s02a, l140s02f皆有此欄位, select * from com.bcodetype where codetype='L140S02F_importId_housebroker' )
	 * <p/>
	 * ※2012--10-25 新增
	 */
	@Size(max = 6)
	@Column(name = "IMPORTID", length = 6, columnDefinition = "VARCHAR(6)")
	private String importId;

	/**
	 * 行銷分行
	 * <p/>
	 * ※2012--10-25 新增
	 */
	@Size(max = 3)
	@Column(name = "SELLBANK", length = 3, columnDefinition = "CHAR(3)")
	private String sellBank;

	/**
	 * 信保成數%
	 * <p/>
	 * 信保成數%<br/>
	 * ※當額度種類為20時要出現此選項
	 */
	@Digits(integer = 3, fraction = 2, groups = Check.class)
	@Column(name = "GUTPERCENT", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal gutPercent;

	/**
	 * 利率
	 */
	@Size(max = 4500)
	@Column(name = "RATEDESC", length = 4500, columnDefinition = "VARCHAR(4500)")
	private String rateDesc;

	/**
	 * 費率
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * 256個全型字產品種類為ZZ時顯示
	 */
	@Size(max = 768)
	@Column(name = "FREERATEDESC", length = 768, columnDefinition = "VARCHAR(768)")
	private String freeRateDesc;

	/**
	 * 償還方式
	 * <p/>
	 * 256個全型字<br/>
	 * ※房貸寬限期最長不超過三年，且應先預扣建物之寬限期折舊，辦理綜合理財房貸及雙週繳款方式不得選擇寬限期。<br/>
	 * ※消費性貸款(額外顯示固定文字)<br/>
	 * (借款人依約展延時，結欠本金得延長之借款期間依約償付) <br/>
	 * ■扣帳：消費者放款－其他個人消費性放款<br/>
	 * ■扣薪：消費者放款－機關團體職工福利貸款<br/>
	 * ※外勞貸款(額外輸入部份欄位)
	 */
	@Size(max = 1200)
	@Column(name = "PAYOFFWAY", length = 1200, columnDefinition = "VARCHAR(1200)")
	private String payoffWay;

	/**
	 * 期付金是否超過薪水1/3
	 * <p/>
	 * Y/N<br/>
	 * ※產品為07時才需顯示
	 */
	@Size(max = 1)
	@Column(name = "PERIODCHK", length = 1, columnDefinition = "CHAR(1)")
	private String periodChk;

	/**
	 * 期付金幣別
	 * <p/>
	 * TWD※產品為07時才需顯示
	 */
	@Size(max = 3)
	@Column(name = "PERIODCURR", length = 3, columnDefinition = "CHAR(3)")
	private String periodCurr;

	/**
	 * 期付金金額
	 * <p/>
	 * ※產品為07時才需顯示
	 */
	@Digits(integer = 13, fraction = 0, groups = Check.class)
	@Column(name = "PERIODAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal periodAmt;

	/** 期付金對照表起日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "PERIODSDATE", columnDefinition = "DATE")
	private Date periodSDate;

	/** 期付金對照表迄日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "PERIODEDATE", columnDefinition = "DATE")
	private Date periodEDate;

	/**
	 * 契約起日
	 * <p/>
	 * ※「02 行家理財－短期」且為「續約」
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "CHGCONSDATE", columnDefinition = "DATE")
	private Date chgConSDate;

	/**
	 * 輸入資料檢誤完成(Y/N)
	 * <p/>
	 */
	@Column(name = "CHKYN", length = 1, columnDefinition = "CHAR(1)")
	private String chkYN;
	
	/**
	 * 週轉金用途細項 {1:購買房貸壽險, 2:申購理財商品, 3:其他}	select * from com.bcodetype where codetype='L140S02A_workingFundPurpose'
	 */
	@Column(name = "WORKINGFUNDPURPOSE", length = 1, columnDefinition = "CHAR(1)")
	private String workingFundPurpose;

	/** 綠色支出類型	select * from com.bcodetype where codetype='L140S02A_esggtype' */
	@Column(name = "ESGGTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String esggtype; //J-111-0047 綠色授信

	/** 綠色授信註記(企金在額度層L140M01A.isEsgGreenLoan) */
	@Column(name = "ESGGNLOANFG", length = 1, columnDefinition = "CHAR(1)")
	private String esggnLoanFg; //J-111-0047 綠色授信
	
	/** 
	 * 對具業務貢獻或為爭攬其他業務於本行往來
	 *  Y/N
	 */
	@Column(name = "ISCONTRIBUTION", length = 1, columnDefinition = "CHAR(1)")
	private String isContribution; 
	 
	/** 
	 * 授權單位主管減碼(僅適用兆豐富二代)
	 *  Y/N
	 */
	@Column(name = "MANAGERREDUCE", length = 1, columnDefinition = "CHAR(1)")
	private String managerReduce; 
	
	/**
	 * 輸入資料檢誤完成(Y/N)
	 * <p/>
	 */
	public String getChkYN() {
		return chkYN;
	}

	/**
	 * 輸入資料檢誤完成(Y/N)
	 * <p/>
	 */
	public void setChkYN(String chkYN) {
		this.chkYN = chkYN;
	}

	/**
	 * 償還扣款方式
	 * <p/>
	 * 1.扣帳<br/>
	 * 2.扣薪<br/>
	 * codeType=L140S02A_chargebackWay
	 */
	@Column(name = "CHARGEBACKWAY", length = 1, columnDefinition = "CHAR(1)")
	private String chargebackWay;

	/**
	 * 取得償還扣款方式
	 * <p/>
	 * 1.扣帳<br/>
	 * 2.扣薪<br/>
	 * codeType=L140S02A_chargebackWay
	 */
	public String getChargebackWay() {
		return chargebackWay;
	}

	/**
	 * 設定償還扣款方式
	 * <p/>
	 * 1.扣帳<br/>
	 * 2.扣薪<br/>
	 * codeType=L140S02A_chargebackWay
	 */
	public void setChargebackWay(String chargebackWay) {
		this.chargebackWay = chargebackWay;
	}

	/**
	 * 承諾書
	 * <p/>
	 * 1|承諾書：由借款人之服務機構或其職工福利委員會出具，按月代扣薪津以繳付期付金。<br/>
	 * 2|委任書/授權書：由借款人出具，授權本行自借款人在本行活存(儲)帳中按月扣償本息。<br/>
	 * codeType=L140S02A_undertaking<br/>
	 * 多筆 1|2|...<br/>
	 */
	@Column(name = "UNDERTAKING", length = 5, columnDefinition = "VARCHAR(5)")
	private String undertaking;

	/**
	 * 承諾書
	 * <p/>
	 * 1|承諾書：由借款人之服務機構或其職工福利委員會出具，按月代扣薪津以繳付期付金。<br/>
	 * 2|委任書/授權書：由借款人出具，授權本行自借款人在本行活存(儲)帳中按月扣償本息。<br/>
	 * codeType=L140S02A_undertaking<br/>
	 * 多筆 1|2|...<br/>
	 */
	public String getUndertaking() {
		return undertaking;
	}

	/**
	 * 承諾書
	 * <p/>
	 * 1|承諾書：由借款人之服務機構或其職工福利委員會出具，按月代扣薪津以繳付期付金。<br/>
	 * 2|委任書/授權書：由借款人出具，授權本行自借款人在本行活存(儲)帳中按月扣償本息。<br/>
	 * codeType=L140S02A_undertaking<br/>
	 * 多筆 1|2|...<br/>
	 */
	public void setUndertaking(String undertaking) {
		this.undertaking = undertaking;
	}

	/**
	 * 契約迄日
	 * <p/>
	 * ※「02 行家理財－短期」且為「續約」
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "CHGCONEDATE", columnDefinition = "DATE")
	private Date chgConEDate;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/** 次要性質 **/
	@Column(name = "SUBPROPERTY", length = 30, columnDefinition = "VARCHAR(30)")
	private String subProperty;
	
	/** 選用模型
	 * 0.免辦 
	 * 1.房貸個人評等模型
	 * 2.非房貸個人評等模型 
	 * 3.卡友貸
	 **/
	@Size(max = 1)
	@Column(name = "MODELKIND", length = 1, columnDefinition = "CHAR(1)")
	private String modelKind;
	
	/** 排序條件 **/
	@Digits(integer = 5, fraction = 0)
	@Column(name = "UISEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer uiSeq;
	
	/**
	 * 原始放款帳號
	 * @return
	 */
	@Size(max = 14)
	@Column(name = "SRCLOANNO", length = 14, columnDefinition = "CHAR(14)")
	private String srcLoanNo;
	
	/** 原始產品種類 **/
	@Size(max = 2)
	@Column(name = "SRCPRODKIND", length = 2, columnDefinition = "CHAR(02)")
	private String srcProdKind;	
	
	/**
	 * 原始科目
	 * <p/>
	 * ※會計科目代碼
	 */
	@Size(max = 8)
	@Column(name = "SRCSUBJCODE", length = 8, columnDefinition = "VARCHAR(8)")
	private String srcSubjCode;
	
	/** 天然及重大災害種類{01:0206震災受災戶}  for產品{63,64,65} <br/>
	        select * from com.bcodetype where codeType in ('val_elf501_disas_type')      <br/>
	        select * from ln.lnf070 where LNF070_TYPE in ('LNF030_DISAS_TYPE')           <br/>
	 */
	@Size(max = 2)
	@Column(name = "DISASTYPE", length = 2, columnDefinition = "VARCHAR(2)")
	private String disasType;
	
	/** 原始天然及重大災害種類 **/
	@Size(max = 2)
	@Column(name = "SRCDISASTYPE", length = 2, columnDefinition = "VARCHAR(2)")
	private String srcDisasType;
	
	/**
	 * 以房養老每期進帳金額(單位：元)
	 */
	@Digits(integer = 13, fraction = 2)
	@Column(name = "RMRCTAMT", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal rmRctAmt;
	
	/**
	 * 以房養老每期利息上限金額(單位：元)
	 */
	@Digits(integer = 13, fraction = 2)
	@Column(name = "RMINTMAX", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal rmIntMax;
	
	/**
	 * 引介行員名稱
	 */
	@Size(max = 30)
	@Column(name = "IMPORTNAME", length = 30, columnDefinition = "VARCHAR(30)")
	private String importName;
	
	/** 房貸引介子公司代號{90001:兆豐金控, 90002:	兆豐產險, 90003:	兆豐證券, 90004:	兆豐投信
		, 90005:兆豐資產管理, 90006:兆豐人身保代, 90007:兆豐創投, 90008:兆豐票券} **/
	@Size(max = 5)
	@Column(name = "MEGACODE", length = 5, columnDefinition = "VARCHAR(5)")
	private String megaCode;
	
	/** 引介房仲收取回饋金 */
	@Size(max = 1)
	@Column(name = "AGNTFBFG", length = 1, columnDefinition = "CHAR(1)")
	private String agntFbfg;
	
	/** 前順位額度序號 */
	@Size(max = 12)
	@Column(name = "BEFSEQCNTRNO", length = 12, columnDefinition = "CHAR(12)")
	private String befSeqCntrNo;

	/** J-109-0135是否發送簡訊Y/N**/
	@Size(max = 1)
	@Column(name = "SENDSMS", length = 1, columnDefinition = "CHAR(1)")
	private String sendSMS;

	/** 客群選單為{優質A類、優質B類、普惠、小額}  select * from com.bcodetype where codetype in ('L140S02A_termGroup_brmp', 'L140S02A_termGroup')     */
	//已在 CLS1151M01FormHandler::chk_mix_prodKind(...) 去檢核，產品種類71不可與其它產品混合簽報
	@Column(name = "TERMGROUP", length = 10, columnDefinition = "VARCHAR(10)")
	private String termGroup;
	
	/** 客群子類別 欄位先開大     */
	@Column(name = "TERMGROUPSUB", length = 10, columnDefinition = "VARCHAR(10)")
	private String termGroupSub;
	
	/** 產品種類類型
	 *  1：房貸
	 *  2：信貸
	 *  3：其他     */
	@Size(max = 1)
	@Column(name = "PRODKINDTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String prodKindType;
	
	/**
	 * 社會責任授信註記
	 */
	@Column(name = "SOCIALLOANFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String socialLoanFlag;
	
	/**
	 * 社會責任專案類別
	 */
	@Column(name = "SOCIALKIND", length = 1, columnDefinition = "CHAR(1)")
	private String socialKind;
	
	/**
	 * 社會責任專案目標族群
	 */
	@Column(name = "SOCIALTA", length = 1, columnDefinition = "CHAR(1)")
	private String socialTa;
	
	/**
	 * 承作社會責任專案者
	 */
	@Column(name = "SOCIALRESP", length = 1, columnDefinition = "CHAR(1)")
	private String socialResp;
	
	
	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得序號 **/
	public Integer getSeq() {
		return this.seq;
	}

	/** 設定序號 **/
	public void setSeq(Integer value) {
		this.seq = value;
	}

	/** 取得流水號 **/
	public Integer getSecNo() {
		return this.secNo;
	}

	/** 設定流水號 **/
	public void setSecNo(Integer value) {
		this.secNo = value;
	}

	/** 取得額度序號 **/
	public String getCntrNo() {
		return this.cntrNo;
	}

	/** 設定額度序號 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/**
	 * 取得承做性質
	 * <p/>
	 * 單選：<br/>
	 * 報價|13 ( 個金無此項目<br/>
	 * 新做|1<br/>
	 * 增額|5<br/>
	 * 紓困|10<br/>
	 * 協議清償|12<br/>
	 * 減額|6<br/>
	 * 變更條件|3<br/>
	 * 續約|2<br/>
	 * 提前續約|11<br/>
	 * 展期(不良授信案)|9<br/>
	 * 流用|4 ( 個金無此項目<br/>
	 * 取消|8<br/>
	 * 不變|7
	 */
	public String getProperty() {
		return this.property;
	}

	/**
	 * 設定承做性質
	 * <p/>
	 * 單選：<br/>
	 * 報價|13 ( 個金無此項目<br/>
	 * 新做|1<br/>
	 * 增額|5<br/>
	 * 紓困|10<br/>
	 * 協議清償|12<br/>
	 * 減額|6<br/>
	 * 變更條件|3<br/>
	 * 續約|2<br/>
	 * 提前續約|11<br/>
	 * 展期(不良授信案)|9<br/>
	 * 流用|4 ( 個金無此項目<br/>
	 * 取消|8<br/>
	 * 不變|7
	 **/
	public void setProperty(String value) {
		this.property = value;
	}

	/**
	 * 取得放款帳號
	 * <p/>
	 * ※若為舊案引入時，會顯示「放款帳號」，並控管「承做性質」不得選取「新做」。
	 */
	public String getLoanNo() {
		return this.loanNo;
	}

	/**
	 * 設定放款帳號
	 * <p/>
	 * ※若為舊案引入時，會顯示「放款帳號」，並控管「承做性質」不得選取「新做」。
	 **/
	public void setLoanNo(String value) {
		this.loanNo = value;
	}

	/**
	 * 取得開戶原因 select * from com.bcodetype where codetype = 'L140S02A_transCode' 
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * 0: 一般案<br/>
	 * 1: 催收轉正<br/>
	 * 2: 更正前次撥款<br/>
	 * 3: 繼承案件<br/>
	 * 4: 受贈土地之土地增值稅融資<br/>
	 * 5: 遺產稅融資<br/>
	 * 6: 納稅義務人為受贈人之贈與稅融資<br/>
	 * 7: 借款人擔保品被政府徵收而改列無擔保之放款<br/>
	 * B: 個人繳回土地徵收補償款之無擔保貸款
	 */
	public String getTransCode() {
		return this.transCode;
	}

	/**
	 * 設定開戶原因 select * from com.bcodetype where codetype = 'L140S02A_transCode' 
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * 0: 一般案<br/>
	 * 1: 催收轉正<br/>
	 * 2: 更正前次撥款<br/>
	 * 3: 繼承案件<br/>
	 * 4: 受贈土地之土地增值稅融資<br/>
	 * 5: 遺產稅融資<br/>
	 * 6: 納稅義務人為受贈人之贈與稅融資<br/>
	 * 7: 借款人擔保品被政府徵收而改列無擔保之放款<br/>
	 * B: 個人繳回土地徵收補償款之無擔保貸款
	 **/
	public void setTransCode(String value) {
		this.transCode = value;
	}

	/**
	 * 取得承接之前放款帳號
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * 當開戶原因為<br/>
	 * 1: 催收轉正<br/>
	 * 2: 更正前次撥款<br/>
	 * 3: 繼承案件<br/>
	 * 才需填入
	 */
	public String getOrgLoanNo() {
		return this.orgLoanNo;
	}

	/**
	 * 設定承接之前放款帳號
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * 當開戶原因為<br/>
	 * 1: 催收轉正<br/>
	 * 2: 更正前次撥款<br/>
	 * 3: 繼承案件<br/>
	 * 才需填入
	 **/
	public void setOrgLoanNo(String value) {
		this.orgLoanNo = value;
	}

	/**
	 * 取得授信期間選項
	 * <p/>
	 * 單選：<br/>
	 * 1.YYYY-MM-DD~ YYYY-MM-DD<br/>
	 * 2.YY年MM月<br/>
	 * 3.自核准日起YY年MM月<br/>
	 * 4.自簽約日起YY年MM月<br/>
	 * 5.其他
	 */
	public String getLnSelect() {
		return this.lnSelect;
	}

	/**
	 * 設定授信期間選項
	 * <p/>
	 * 單選：<br/>
	 * 1.YYYY-MM-DD~ YYYY-MM-DD<br/>
	 * 2.YY年MM月<br/>
	 * 3.自核准日起YY年MM月<br/>
	 * 4.自簽約日起YY年MM月<br/>
	 * 5.其他
	 **/
	public void setLnSelect(String value) {
		this.lnSelect = value;
	}

	/**
	 * 取得授信期間-起始日期
	 * <p/>
	 * 1.YYYY-MM-DD~ YYYY-MM-DD
	 */
	public Date getLnFromDate() {
		return this.lnFromDate;
	}

	/**
	 * 設定授信期間-起始日期
	 * <p/>
	 * 1.YYYY-MM-DD~ YYYY-MM-DD
	 **/
	public void setLnFromDate(Date value) {
		this.lnFromDate = value;
	}

	/**
	 * 取得授信期間-截止日期
	 * <p/>
	 * 1.YYYY-MM-DD~ YYYY-MM-DD
	 */
	public Date getLnEndDate() {
		return this.lnEndDate;
	}

	/**
	 * 設定授信期間-截止日期
	 * <p/>
	 * 1.YYYY-MM-DD~ YYYY-MM-DD
	 **/
	public void setLnEndDate(Date value) {
		this.lnEndDate = value;
	}

	/**
	 * 取得授信期間-年數
	 * <p/>
	 * lnSelect<br/>
	 * 2.YY年MM月<br/>
	 * 3.自核准日起YY年MM月<br/>
	 * 4.自簽約日起YY年MM月
	 */
	public Integer getLnYear() {
		return this.lnYear;
	}

	/**
	 * 設定授信期間-年數
	 * <p/>
	 * lnSelect<br/>
	 * 2.YY年MM月<br/>
	 * 3.自核准日起YY年MM月<br/>
	 * 4.自簽約日起YY年MM月
	 **/
	public void setLnYear(Integer value) {
		this.lnYear = value;
	}

	/**
	 * 取得授信期間-月數
	 * <p/>
	 * lnSelect<br/>
	 * 2.YY年MM月<br/>
	 * 3.自核准日起YY年MM月<br/>
	 * 4.自簽約日起YY年MM月
	 */
	public Integer getLnMonth() {
		return this.lnMonth;
	}

	/**
	 * 設定授信期間-月數
	 * <p/>
	 * lnSelect<br/>
	 * 2.YY年MM月<br/>
	 * 3.自核准日起YY年MM月<br/>
	 * 4.自簽約日起YY年MM月
	 **/
	public void setLnMonth(Integer value) {
		this.lnMonth = value;
	}

	/**
	 * 取得授信期間-其他
	 * <p/>
	 * 5.其他
	 */
	public String getLnOther() {
		return this.lnOther;
	}

	/**
	 * 設定授信期間-其他
	 * <p/>
	 * 5.其他
	 **/
	public void setLnOther(String value) {
		this.lnOther = value;
	}

	/** 取得產品種類 **/
	public String getProdKind() {
		return this.prodKind;
	}

	/** 設定產品種類 **/
	public void setProdKind(String value) {
		this.prodKind = value;
	}

	/**
	 * 取得購置
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * 23/24(921案件)<br/>
	 * 1|購置(第一段)<br/>
	 * 2|購置(第二段)
	 */
	public String getFor921Case() {
		return this.for921Case;
	}

	/**
	 * 設定購置
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * 23/24(921案件)<br/>
	 * 1|購置(第一段)<br/>
	 * 2|購置(第二段)
	 **/
	public void setFor921Case(String value) {
		this.for921Case = value;
	}

	/**
	 * 取得科目
	 * <p/>
	 * ※會計科目代碼
	 */
	public String getSubjCode() {
		return this.subjCode;
	}

	/**
	 * 設定科目
	 * <p/>
	 * ※會計科目代碼
	 **/
	public void setSubjCode(String value) {
		this.subjCode = value;
	}

	/**
	 * 取得團貸案名稱
	 * <p/>
	 * 60個中文字
	 */
	public String getCaseName() {
		return this.caseName;
	}

	/**
	 * 設定團貸案名稱
	 * <p/>
	 * 60個中文字
	 **/
	public void setCaseName(String value) {
		this.caseName = value;
	}

	/**
	 * 取得建案名稱
	 * <p/>
	 * 60個中文字
	 */
	public String getBuildName() {
		return this.buildName;
	}

	/**
	 * 設定建案名稱
	 * <p/>
	 * 60個中文字
	 **/
	public void setBuildName(String value) {
		this.buildName = value;
	}

	/** 取得土地坐落區-縣市 **/
	public String getLandCity() {
		return this.landCity;
	}

	/** 設定土地坐落區-縣市 **/
	public void setLandCity(String value) {
		this.landCity = value;
	}

	/** 取得土地坐落區-區域 **/
	public String getLandArea() {
		return this.landArea;
	}

	/** 設定土地坐落區-區域 **/
	public void setLandArea(String value) {
		this.landArea = value;
	}

	/** 取得土地坐落區-大段 **/
	public String getLandpart1() {
		return this.landpart1;
	}

	/** 設定土地坐落區-大段 **/
	public void setLandpart1(String value) {
		this.landpart1 = value;
	}

	/** 取得土地坐落區-小段 **/
	public String getLandpart2() {
		return this.landpart2;
	}

	/** 設定土地坐落區-小段 **/
	public void setLandpart2(String value) {
		this.landpart2 = value;
	}

	/** 取得最終評等－採用借保人ID **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定最終評等－採用借保人ID **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得最終評等－採用借保人重複序號 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定最終評等－採用借保人重複序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/**
	 * 取得最終評等－評等
	 * <p/>
	 * ※如於簽報書「借款人基本資料」頁籤重新引進所採用之借保人資料，亦同步更新本筆所採用之最終評等。
	 */
	public String getGrade1() {
		return this.grade1;
	}

	/**
	 * 設定最終評等－評等
	 * <p/>
	 * ※如於簽報書「借款人基本資料」頁籤重新引進所採用之借保人資料，亦同步更新本筆所採用之最終評等。
	 **/
	public void setGrade1(String value) {
		this.grade1 = value;
	}

	/**
	 * 取得是否重簽契約
	 * <p/>
	 * ※「02 行家理財－短期」且為「續約」<br/>
	 * Y/N
	 */
	public String getChgContract() {
		return this.chgContract;
	}

	/**
	 * 設定是否重簽契約
	 * <p/>
	 * ※「02 行家理財－短期」且為「續約」<br/>
	 * Y/N
	 **/
	public void setChgContract(String value) {
		this.chgContract = value;
	}

	/** 取得 前已續約次數 */
	public Integer getChgConTimes() {
		return this.chgConTimes;
	}

	/** 設定 前已續約次數 */
	public void setChgConTimes(Integer value) {
		this.chgConTimes = value;
	}

	/**
	 * 取得動用方式
	 * <p/>
	 * 單選：<br/>
	 * 1.憑借支書<br/>
	 * 2.憑金融卡<br/>
	 * 3.憑支票
	 */
	public String getUseType() {
		return this.useType;
	}

	/**
	 * 設定動用方式
	 * <p/>
	 * 單選：<br/>
	 * 1.憑借支書<br/>
	 * 2.憑金融卡<br/>
	 * 3.憑支票
	 **/
	public void setUseType(String value) {
		this.useType = value;
	}

	/**
	 * 取得應計入DBR22倍規範額度－幣別
	 * <p/>
	 * 預設：新台幣TWD
	 */
	public String getDBR22Curr() {
		return this.DBR22Curr;
	}

	/**
	 * 設定應計入DBR22倍規範額度－幣別
	 * <p/>
	 * 預設：新台幣TWD
	 **/
	public void setDBR22Curr(String value) {
		this.DBR22Curr = value;
	}

	/**
	 * 取得應計入DBR22倍規範額度－金額
	 * <p/>
	 * 單位：元
	 */
	public BigDecimal getDBR22Amt() {
		return this.DBR22Amt;
	}

	/**
	 * 設定應計入DBR22倍規範額度－金額
	 * <p/>
	 * 單位：元
	 **/
	public void setDBR22Amt(BigDecimal value) {
		this.DBR22Amt = value;
	}

	/**
	 * 取得資金來源
	 * <p/>
	 * 單選：<br/>
	 * 0本行自有資金<br/>
	 * 1中美基金<br/>
	 * 4郵匯局基金<br/>
	 * 5信保基金
	 */
	public String getFFund() {
		return this.fFund;
	}

	/**
	 * 設定資金來源
	 * <p/>
	 * 單選：<br/>
	 * 0本行自有資金<br/>
	 * 1中美基金<br/>
	 * 4郵匯局基金<br/>
	 * 5信保基金
	 **/
	public void setFFund(String value) {
		this.fFund = value;
	}

	/**
	 * 取得資金來源小類
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * SELECT * FROM LN.LNF070 WHERE LNF070_LNF070_TYPE=’LNF030-FUND-TYPE’ AND
	 * SUBSTR(LNF070_CODE,1,1)='資金來源代碼'
	 */
	public String getDFund() {
		return this.dFund;
	}

	/**
	 * 設定資金來源小類
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * SELECT * FROM LN.LNF070 WHERE LNF070_LNF070_TYPE=’LNF030-FUND-TYPE’ AND
	 * SUBSTR(LNF070_CODE,1,1)='資金來源代碼'
	 **/
	public void setDFund(String value) {
		this.dFund = value;
	}

	/**
	 * 取得動撥幣別
	 * <p/>
	 * 預設：新台幣TWD
	 */
	public String getLoanCurr() {
		return this.loanCurr;
	}

	/**
	 * 設定動撥幣別
	 * <p/>
	 * 預設：新台幣TWD
	 **/
	public void setLoanCurr(String value) {
		this.loanCurr = value;
	}

	/**
	 * 取得動撥金額
	 * <p/>
	 * 單位：元
	 */
	public BigDecimal getLoanAmt() {
		return this.loanAmt;
	}

	/**
	 * 設定動撥金額
	 * <p/>
	 * 單位：元
	 **/
	public void setLoanAmt(BigDecimal value) {
		this.loanAmt = value;
	}

	/**
	 * 取得是否搭配長擔貸款
	 * <p/>
	 * Y/N
	 */
	public String getChkUsed() {
		return this.chkUsed;
	}

	/**
	 * 設定是否搭配長擔貸款
	 * <p/>
	 * Y/N
	 **/
	public void setChkUsed(String value) {
		this.chkUsed = value;
	}

	/**
	 * 取得融資業務分類
	 * <p/>
	 * 單選：<br/>
	 * 1-個人投資理財貸款<br/>
	 * 2-購置住宅貸款（其他）<br/>
	 * 3-房屋修繕貸款（其他）<br/>
	 * L-購置住宅貸款（非自用）<br/>
	 * M-購置住宅貸款（自用）<br/>
	 * N-房屋修繕貸款<br/>
	 * O-汽車貸款<br/>
	 * P-職工福利貸款<br/>
	 * S-創業貸款<br/>
	 * U-建築融資貸款<br/>
	 * V-代墊投標保證金貸款<br/>
	 * X-參與都市更新計畫貸款<br/>
	 * Y-企業員工認購股票（或可轉換公司債）貸款<br/>
	 * Z-其他個人金融貸款
	 */
	public String getLnPurpose() {
		return this.lnPurpose;
	}

	/**
	 * 設定融資業務分類
	 * <p/>
	 * 單選：<br/>
	 * 1-個人投資理財貸款<br/>
	 * 2-購置住宅貸款（其他）<br/>
	 * 3-房屋修繕貸款（其他）<br/>
	 * L-購置住宅貸款（非自用）<br/>
	 * M-購置住宅貸款（自用）<br/>
	 * N-房屋修繕貸款<br/>
	 * O-汽車貸款<br/>
	 * P-職工福利貸款<br/>
	 * S-創業貸款<br/>
	 * U-建築融資貸款<br/>
	 * V-代墊投標保證金貸款<br/>
	 * X-參與都市更新計畫貸款<br/>
	 * Y-企業員工認購股票（或可轉換公司債）貸款<br/>
	 * Z-其他個人金融貸款
	 **/
	public void setLnPurpose(String value) {
		this.lnPurpose = value;
	}

	/**
	 * 取得用途別
	 * <p/>
	 * 單選：<br/>
	 * 1購置不動產<br/>
	 * 2購置動產<br/>
	 * 3企業投資<br/>
	 * 4週轉金
	 */
	public String getLnPurs() {
		return this.lnPurs;
	}

	/**
	 * 設定用途別
	 * <p/>
	 * 單選：<br/>
	 * 1購置不動產<br/>
	 * 2購置動產<br/>
	 * 3企業投資<br/>
	 * 4週轉金
	 **/
	public void setLnPurs(String value) {
		this.lnPurs = value;
	}

	/**
	 * 取得是否屬興建住宅
	 * <p/>
	 * 單選：<br/>
	 * 1.是-興建住宅自用<br/>
	 * 2.是-興建住宅非自用<br/>
	 * N.否-非興建住宅<br/>
	 * => 影響 elf501_residential , lnf030_residential <br/>
	 * ==> LOAN.PROD.SOURCE(LNSR136) 會用 lnf030_residential 去決定 LNF15?_LOAN_CLASS 是 '07' 或 '14' <br/>
	 * ====> 影響 JCIC報送 欄位  "建築貸款註記" 
	 */
	public String getResidential() {
		return this.residential;
	}

	/**
	 * 設定是否屬興建住宅
	 * <p/>
	 * 單選：<br/>
	 * 1.是-興建住宅自用<br/>
	 * 2.是-興建住宅非自用<br/>
	 * N.否-非興建住宅<br/>
	 * => 影響 elf501_residential , lnf030_residential <br/>
	 * ==> LOAN.PROD.SOURCE(LNSR136) 會用 lnf030_residential 去決定 LNF15?_LOAN_CLASS 是 '07' 或 '14' <br/>
	 * ====> 影響 JCIC報送 欄位  "建築貸款註記" 
	 */
	public void setResidential(String value) {
		this.residential = value;
	}

	/**
	 * 取得引介行員代號(l140s02a, l140s02f皆有此欄位, select * from com.bcodetype where codetype='L140S02F_importId_housebroker' )
	 * <p/>
	 * ※2012--10-25 新增
	 */
	public String getImportId() {
		return this.importId;
	}

	/**
	 * 設定引介行員代號(l140s02a, l140s02f皆有此欄位, select * from com.bcodetype where codetype='L140S02F_importId_housebroker' )
	 * <p/>
	 * ※2012--10-25 新增
	 **/
	public void setImportId(String value) {
		this.importId = value;
	}

	/**
	 * 取得行銷分行
	 * <p/>
	 * ※2012--10-25 新增
	 */
	public String getSellBank() {
		return this.sellBank;
	}

	/**
	 * 設定行銷分行
	 * <p/>
	 * ※2012--10-25 新增
	 **/
	public void setSellBank(String value) {
		this.sellBank = value;
	}

	/**
	 * 取得信保成數%
	 * <p/>
	 * 信保成數%<br/>
	 * ※當額度種類為20時要出現此選項
	 */
	public BigDecimal getGutPercent() {
		return this.gutPercent;
	}

	/**
	 * 設定信保成數%
	 * <p/>
	 * 信保成數%<br/>
	 * ※當額度種類為20時要出現此選項
	 **/
	public void setGutPercent(BigDecimal value) {
		this.gutPercent = value;
	}

	/**
	 * 取得利率
	 */
	public String getRateDesc() {
		return this.rateDesc;
	}

	/**
	 * 設定利率
	 **/
	public void setRateDesc(String value) {
		this.rateDesc = value;
	}

	/**
	 * 取得費率
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * 256個全型字產品種類為ZZ時顯示
	 */
	public String getFreeRateDesc() {
		return this.freeRateDesc;
	}

	/**
	 * 設定費率
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * 256個全型字產品種類為ZZ時顯示
	 **/
	public void setFreeRateDesc(String value) {
		this.freeRateDesc = value;
	}

	/**
	 * 取得償還方式
	 * <p/>
	 * 256個全型字<br/>
	 * ※房貸寬限期最長不超過三年，且應先預扣建物之寬限期折舊，辦理綜合理財房貸及雙週繳款方式不得選擇寬限期。<br/>
	 * ※消費性貸款(額外顯示固定文字)<br/>
	 * (借款人依約展延時，結欠本金得延長之借款期間依約償付) <br/>
	 * ■扣帳：消費者放款－其他個人消費性放款<br/>
	 * ■扣薪：消費者放款－機關團體職工福利貸款<br/>
	 * ※外勞貸款(額外輸入部份欄位)
	 */
	public String getPayoffWay() {
		return this.payoffWay;
	}

	/**
	 * 設定償還方式
	 * <p/>
	 * 256個全型字<br/>
	 * ※房貸寬限期最長不超過三年，且應先預扣建物之寬限期折舊，辦理綜合理財房貸及雙週繳款方式不得選擇寬限期。<br/>
	 * ※消費性貸款(額外顯示固定文字)<br/>
	 * (借款人依約展延時，結欠本金得延長之借款期間依約償付) <br/>
	 * ■扣帳：消費者放款－其他個人消費性放款<br/>
	 * ■扣薪：消費者放款－機關團體職工福利貸款<br/>
	 * ※外勞貸款(額外輸入部份欄位)
	 **/
	public void setPayoffWay(String value) {
		this.payoffWay = value;
	}

	/**
	 * 取得期付金是否超過薪水1/3
	 * <p/>
	 * Y/N<br/>
	 * ※產品為07時才需顯示
	 */
	public String getPeriodChk() {
		return this.periodChk;
	}

	/**
	 * 設定期付金是否超過薪水1/3
	 * <p/>
	 * Y/N<br/>
	 * ※產品為07時才需顯示
	 **/
	public void setPeriodChk(String value) {
		this.periodChk = value;
	}

	/**
	 * 取得期付金幣別
	 * <p/>
	 * TWD※產品為07時才需顯示
	 */
	public String getPeriodCurr() {
		return this.periodCurr;
	}

	/**
	 * 設定期付金幣別
	 * <p/>
	 * TWD※產品為07時才需顯示
	 **/
	public void setPeriodCurr(String value) {
		this.periodCurr = value;
	}

	/**
	 * 取得期付金金額
	 * <p/>
	 * ※產品為07時才需顯示
	 */
	public BigDecimal getPeriodAmt() {
		return this.periodAmt;
	}

	/**
	 * 設定期付金金額
	 * <p/>
	 * ※產品為07時才需顯示
	 **/
	public void setPeriodAmt(BigDecimal value) {
		this.periodAmt = value;
	}

	/** 取得期付金對照表起日 **/
	public Date getPeriodSDate() {
		return this.periodSDate;
	}

	/** 設定期付金對照表起日 **/
	public void setPeriodSDate(Date value) {
		this.periodSDate = value;
	}

	/** 取得期付金對照表迄日 **/
	public Date getPeriodEDate() {
		return this.periodEDate;
	}

	/** 設定期付金對照表迄日 **/
	public void setPeriodEDate(Date value) {
		this.periodEDate = value;
	}

	/**
	 * 取得契約起日
	 * <p/>
	 * ※「02 行家理財－短期」且為「續約」
	 */
	public Date getChgConSDate() {
		return this.chgConSDate;
	}

	/**
	 * 設定契約起日
	 * <p/>
	 * ※「02 行家理財－短期」且為「續約」
	 **/
	public void setChgConSDate(Date value) {
		this.chgConSDate = value;
	}

	/**
	 * 取得契約迄日
	 * <p/>
	 * ※「02 行家理財－短期」且為「續約」
	 */
	public Date getChgConEDate() {
		return this.chgConEDate;
	}

	/**
	 * 設定契約迄日
	 * <p/>
	 * ※「02 行家理財－短期」且為「續約」
	 **/
	public void setChgConEDate(Date value) {
		this.chgConEDate = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/**
	 * 新增來源 <br/>
	 * ※2013-05-09新增欄位<br/>
	 * 0|舊案轉入產品<br/>
	 * 1|一般分行新增<br/>
	 * 2|授管處新增<br/>
	 * **/
	@Size(max = 1)
	@Column(name = "CREATSRC", length = 1, columnDefinition = "CHAR(1)")
	private String creatSrc;

	/**
	 * 新增來源 <br/>
	 * ※2013-05-09新增欄位<br/>
	 * 0|舊案轉入產品<br/>
	 * 1|一般分行新增<br/>
	 * 2|授管處新增<br/>
	 * **/
	public String getCreatSrc() {
		return creatSrc;
	}

	/**
	 * 新增來源 <br/>
	 * ※2013-05-09新增欄位<br/>
	 * 0|舊案轉入產品<br/>
	 * 1|一般分行新增<br/>
	 * 2|授管處新增<br/>
	 * **/
	public void setCreatSrc(String creatSrc) {
		this.creatSrc = creatSrc;
	}
	
	/** 取得次要性質 **/
	public String getSubProperty() {
		return subProperty;
	}
	
	/** 設定次要性質 **/	
	public void setSubProperty(String subProperty) {
		this.subProperty = subProperty;
	}
	
	/** 取得選用模型 **/
	public String getModelKind() {
		return modelKind;
	}
	
	/** 設定選用模型 **/	
	public void setModelKind(String val) {
		this.modelKind = val;
	}

	/**
	 * 設定原始放款帳號
	 * @return
	 */
	public void setSrcLoanNo(String val) {
		this.srcLoanNo = val;
	}

	/**
	 * 取得原始放款帳號
	 * @return
	 */
	public String getSrcLoanNo() {
		return srcLoanNo;
	}

	/** 設定原始產品種類 **/
	public void setSrcProdKind(String val) {
		this.srcProdKind = val;
	}

	/** 取得原始產品種類 **/
	public String getSrcProdKind() {
		return srcProdKind;
	}

	/**
	 * 設定原始科目
	 * <p/>
	 * ※會計科目代碼
	 */
	public void setSrcSubjCode(String val) {
		this.srcSubjCode = val;
	}

	/**
	 * 取得原始科目
	 * <p/>
	 * ※會計科目代碼
	 */
	public String getSrcSubjCode() {
		return srcSubjCode;
	}

	/** 取得序號 **/
	public Integer getUiSeq() {
		return this.uiSeq;
	}

	/** 設定序號 **/
	public void setUiSeq(Integer value) {
		this.uiSeq = value;
	}

	/** 取得天然及重大災害種類{01:0206震災受災戶} **/
	public String getDisasType() {
		return this.disasType;
	}
	/** 設定天然及重大災害種類{01:0206震災受災戶} **/
	public void setDisasType(String value) {
		this.disasType = value;
	}

	/** 取得原始天然及重大災害種類 **/
	public String getSrcDisasType() {
		return this.srcDisasType;
	}
	/** 設定天然及重大災害種類 **/
	public void setSrcDisasType(String value) {
		this.srcDisasType = value;
	}

	/** 取得以房養老每期進帳金額 **/
	public BigDecimal getRmRctAmt() {
		return this.rmRctAmt;
	}
	/** 設定以房養老每期進帳金額 **/
	public void setRmRctAmt(BigDecimal value) {
		this.rmRctAmt = value;
	}
	
	/** 取得以房養老每期利息上限金額 **/
	public BigDecimal getRmIntMax() {
		return this.rmIntMax;
	}
	/** 設定以房養老每期利息上限金額 **/
	public void setRmIntMax(BigDecimal value) {
		this.rmIntMax = value;
	}

	/** 取得引介行員名稱 **/
	public String getImportName() {
		return this.importName;
	}

	/** 設定引介行員名稱 **/
	public void setImportName(String value) {
		this.importName = value;
	}

	/** 取得房貸引介子公司代號{90001:兆豐金控, 90002:	兆豐產險, 90003:	兆豐證券, 90004:	兆豐投信
	, 90005:兆豐資產管理, 90006:兆豐人身保代, 90007:兆豐創投, 90008:兆豐票券} **/
	public String getMegaCode() {
		return megaCode;
	}
	/** 設定房貸引介子公司代號{90001:兆豐金控, 90002:	兆豐產險, 90003:	兆豐證券, 90004:	兆豐投信
	, 90005:兆豐資產管理, 90006:兆豐人身保代, 90007:兆豐創投, 90008:兆豐票券} **/
	public void setMegaCode(String megaCode) {
		this.megaCode = megaCode;
	}

	/** 取得引介房仲收取回饋金 */
	public String getAgntFbfg() {
		return agntFbfg;
	}
	/** 設定引介房仲收取回饋金 */
	public void setAgntFbfg(String agntFbfg) {
		this.agntFbfg = agntFbfg;
	}

	/** 取得前順位額度序號 */
	public String getBefSeqCntrNo() {
		return befSeqCntrNo;
	}
	/** 取得前順位額度序號 */
	public void setBefSeqCntrNo(String befSeqCntrNo) {
		this.befSeqCntrNo = befSeqCntrNo;
	}
	/** 是否發送簡訊Y/N**/
	public String getSendSMS() {
		return sendSMS;
	}
	/** 是否發送簡訊Y/N**/
	public void setSendSMS(String sendSMS) {
		this.sendSMS = sendSMS;
	}

	/** 取得客群選單 */
	public String getTermGroup() {
		return termGroup;
	}
	/** 設定客群選單 */
	public void setTermGroup(String termGroup) {
		this.termGroup = termGroup;
	}
	
	/**
	 * 週轉金用途細項 {1:購買房貸壽險, 2:申購理財商品, 3:其他}	select * from com.bcodetype where codetype='L140S02A_workingFundPurpose'
	 */
	public String getWorkingFundPurpose() {
		return workingFundPurpose;
	}
	/**
	 * 週轉金用途細項 {1:購買房貸壽險, 2:申購理財商品, 3:其他}	select * from com.bcodetype where codetype='L140S02A_workingFundPurpose'
	 */
	public void setWorkingFundPurpose(String workingFundPurpose) {
		this.workingFundPurpose = workingFundPurpose;
	}

	/** 取得綠色支出類型	*/
	public String getEsggtype() {
		return esggtype;
	}
	/** 設定綠色支出類型 */
	public void setEsggtype(String esggtype) {
		this.esggtype = esggtype;
	}
	
	/** 綠色授信註記(企金在額度層L140M01A.isEsgGreenLoan) */
	public String getEsggnLoanFg() {
		return esggnLoanFg;
	}
	/** 綠色授信註記(企金在額度層L140M01A.isEsgGreenLoan) */
	public void setEsggnLoanFg(String esggnLoanFg) {
		this.esggnLoanFg = esggnLoanFg;
	}
	
	/** 客群子類別 */
	public void setTermGroupSub(String termGroupSub) {
		this.termGroupSub = termGroupSub;
	}

	public String getTermGroupSub() {
		return termGroupSub;
	}

	/** 取得產品種類類型*/
	public String getProdKindType() {
		return prodKindType;
	}

	/** 設定產品種類類型 */
	public void setProdKindType(String prodKindType) {
		this.prodKindType = prodKindType;
	}

	/** 取得對具業務貢獻或為爭攬其他業務於本行往來*/
	public String getIsContribution() {
		return isContribution;
	}

	/** 設定對具業務貢獻或為爭攬其他業務於本行往來 */
	public void setIsContribution(String isContribution) {
		this.isContribution = isContribution;
	}

	/** 取得授權單位主管減碼(僅適用兆豐富二代)*/
	public String getManagerReduce() {
		return managerReduce;
	}

	/** 設定授權單位主管減碼(僅適用兆豐富二代) */
	public void setManagerReduce(String managerReduce) {
		this.managerReduce = managerReduce;
	}

	public String getSocialLoanFlag() {
		return socialLoanFlag;
	}

	public void setSocialLoanFlag(String socialLoanFlag) {
		this.socialLoanFlag = socialLoanFlag;
	}

	public String getSocialKind() {
		return socialKind;
	}

	public void setSocialKind(String socialKind) {
		this.socialKind = socialKind;
	}

	public String getSocialTa() {
		return socialTa;
	}

	public void setSocialTa(String socialTa) {
		this.socialTa = socialTa;
	}

	public String getSocialResp() {
		return socialResp;
	}

	public void setSocialResp(String socialResp) {
		this.socialResp = socialResp;
	}	
}
