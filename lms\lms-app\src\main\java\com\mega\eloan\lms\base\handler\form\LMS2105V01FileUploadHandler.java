package com.mega.eloan.lms.base.handler.form;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.multipart.MultipartFile;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.lms.base.service.LMS2105V01Service;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dc.util.TextDefine;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.mfaloan.bean.LNF07A;
import com.mega.eloan.lms.mfaloan.service.LNLNF07AService;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.model.BRTOBR01;
import com.mega.eloan.lms.model.L180R60A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import jxl.Cell;
import jxl.CellType;
import jxl.DateCell;
import jxl.Sheet;
import jxl.Workbook;
import jxl.read.biff.BiffException;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.handler.FileUploadHandler;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 評等對照 上傳
 * </pre>
 * 
 * @since 2016/4/12
 * <AUTHOR>
 * @version <ul>
 *          <li>2016/4/12,EL08034,new
 *          </ul>
 */
@Scope("request")
@Controller("lms2105v01fileuploadhandler")
public class LMS2105V01FileUploadHandler extends FileUploadHandler {

	@Autowired
	DocFileService fileService;

	@Resource
	LMSService lmsService;
	@Resource
	BranchService branchSrv;
	// @Resource
	// LMS1401Service lms1401Service;
	@Resource
	MisCustdataService misCustdataService;

	@Resource
	EloandbBASEService eloandbService;

	@Resource
	LNLNF07AService lnLnf07aService;

	@Resource
	LMS2105V01Service lms2105v01Service;

	@Override
	public IResult afterUploaded(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// Properties pop = MessageBundleScriptCreator
		// .getComponentResource(LMS1401S02Page.class);
		CapAjaxFormResult result = new CapAjaxFormResult();
		MultipartFile uFile = params.getFile(params.getString("fieldId"));

		String mainId = params.getString("tabFormMainId");
		String type = params.getString("type");

		boolean isGetImgDimension = params.getBoolean("getImgDimension");
		String sysId = params.getString("sysId", fileService.getSysId());

		// 設定上傳檔案資訊
		String fileName = uFile.getName();
		String fieldId = Util.trim(params.getString("fieldId"));

		logger.info("LMS2105V01FileUploadHandler afterUploaded Begin ============================================>");
		logger.info("sysId=" + fieldId + "，fileName=" + fileName + "，fieldId="
				+ fieldId + "");

		/*
		 * if (params.containsKey("fileSize")) { if (uFile.getSize() >
		 * params.getLong("fileSize", 1048576)) { //
		 * EFD0063=ERROR|上送的檔案已超過$\{fileSize\}M的限制大小，無法執行上傳動作。| Map<String,
		 * String> msg = new HashMap<String, String>(); msg.put("fileSize",
		 * CapMath.divide(params.getString("fileSize"), "1048576")); //
		 * 1M*1024*1024 MegaErrorResult result = new MegaErrorResult();
		 * result.putError( getComponent(), new
		 * CapMessageException(RespMsgHelper.getMessage( getComponent(),
		 * "EFD0063", msg), getClass())); return result; } }
		 */

		// 開始匯入EXCEL****************************************************************************
		Workbook workbook = null;
		String errMsg = "";
		InputStream is = null;
		String fileKey = "";
		boolean findError = false;

		int[] dimension = { -1, -1 };

		if (Util.equals(fieldId, "uploadFileAml")) {

			logger.info("uploadFileAml Begin ============================================>");

			try {

				is = uFile.getInputStream();
				workbook = Workbook.getWorkbook(is);
				Sheet sheet = workbook.getSheet(0);
				int totalCol = sheet.getColumns();
				if (totalCol == 0) {
					throw new CapMessageException("匯入之名單EXCEL格式錯誤。", getClass());
				}

				logger.info("totalCol:[" + totalCol + "]");

				if (totalCol == 0) {
					throw new CapMessageException("匯入之名單EXCEL格式錯誤。", getClass());
				}

				HashMap<String, String> importCustId = new HashMap<String, String>();
				int maxItemSeq = 0;

				List<BRTOBR01> brtobr01_list_insert = new ArrayList<BRTOBR01>();

				String key_exDate = "";

				String lnf07a_key_1 = "";
				String lnf07a_key_2 = ""; // key_exDate
				String lnf07a_explain = "";

				for (int row = 1; row < sheet.getRows(); row++) {
					int column = 0;

					String custId = "";
					String dupNo = "";
					String fullCustId = "";

					String taiwanId = ""; // 未使用
					String cntrNo_AS400 = ""; // 未使用
					String cntrNo_ELOAN = ""; // 未使用

					Date exDate = null;
					String fbranch = "";
					BigDecimal fcustId = null;
					BigDecimal fcustNo = null;
					String tbranch = "";
					BigDecimal tcustId = null;
					BigDecimal tcustNo = null;
					String cname = "";
					String chgCustId = "";
					String chgDupNo = "";

					// KEY1
					if (++column <= totalCol) {
						if (Util.equals(lnf07a_key_1, "")) {
							lnf07a_key_1 = StringUtils.upperCase(Util
									.trim(getContents(sheet.getCell(column - 1,
											row))));
						}

					}

					// EXPLAIN
					if (++column <= totalCol) {
						if (Util.equals(lnf07a_explain, "")) {
							lnf07a_explain = StringUtils.upperCase(Util
									.trim(getContents(sheet.getCell(column - 1,
											row))));
						}

					}

					// FROM BRNO
					if (++column <= totalCol) {
						fbranch = StringUtils.upperCase(Util
								.trim(getContents(sheet
										.getCell(column - 1, row))));
					}

					// CUST ID
					if (++column <= totalCol) {
						String tStr = Util.trim(getContents(sheet.getCell(
								column - 1, row)));
						fcustId = Util.equals(tStr, "") ? null : Util
								.parseBigDecimal(tStr);
					}

					// CUST NO
					if (++column <= totalCol) {
						String tStr = Util.trim(getContents(sheet.getCell(
								column - 1, row)));
						fcustNo = Util.equals(tStr, "") ? null : Util
								.parseBigDecimal(tStr);

					}

					// ICBC ID
					if (++column <= totalCol) {
						fullCustId = StringUtils.upperCase(Util
								.trim(getContents(sheet
										.getCell(column - 1, row))));

						// 處理沒有重複序號
						fullCustId = fullCustId + "           ";
						custId = Util.trim(Util.getLeftStr(fullCustId
								+ "          ", 10));
						dupNo = Util.trim(StringUtils.substring(fullCustId, 10,
								11));
						if (Util.equals(dupNo, "")) {
							dupNo = "0";
						}

					}

					// TAIWAN ID
					if (++column <= totalCol) {
						taiwanId = Util.toSemiCharString(Util
								.trim(getContents(sheet
										.getCell(column - 1, row))));
					}

					// CONTRACT NO
					if (++column <= totalCol) {
						// CONTRACT NO
						cntrNo_AS400 = Util.toSemiCharString(Util
								.trim(getContents(sheet
										.getCell(column - 1, row))));
					}

					// ELOAN LINE NO
					if (++column <= totalCol) {
						// ELOAN LINE NO
						cntrNo_ELOAN = Util.toSemiCharString(Util
								.trim(getContents(sheet
										.getCell(column - 1, row))));
					}

					// TO BRNO
					if (++column <= totalCol) {
						tbranch = StringUtils.upperCase(Util
								.trim(getContents(sheet
										.getCell(column - 1, row))));

					}

					// TO CUST ID
					if (++column <= totalCol) {
						String tStr = Util.trim(getContents(sheet.getCell(
								column - 1, row)));
						tcustId = Util.equals(tStr, "") ? null : Util
								.parseBigDecimal(tStr);

					}

					// TO CUST NO
					if (++column <= totalCol) {

						String tStr = Util.trim(getContents(sheet.getCell(
								column - 1, row)));
						tcustNo = Util.equals(tStr, "") ? null : Util
								.parseBigDecimal(tStr);

					}

					// EXDATE
					if (++column <= totalCol) {
						String tStr = Util.trim(getContents(sheet.getCell(
								column - 1, row)));
						exDate = Util.equals(tStr, "") ? null : Util
								.parseDate(Util.trim(getContents(sheet.getCell(
										column - 1, row))));
					}

					// CNAME
					if (++column <= totalCol) {
						cname = Util.toSemiCharString(Util
								.trim(getContents(sheet
										.getCell(column - 1, row))));
					}

					// CHGCUSTID
					if (++column <= totalCol) {
						String fullChgCustId = Util.toSemiCharString(Util
								.trim(getContents(sheet
										.getCell(column - 1, row))));

						// 處理沒有重複序號
						fullChgCustId = fullChgCustId + "           ";
						chgCustId = Util.trim(Util.getLeftStr(fullChgCustId
								+ "          ", 10));
						chgDupNo = Util.trim(StringUtils.substring(
								fullChgCustId, 10, 11));
						if (Util.equals(chgDupNo, "")) {
							chgDupNo = "0";
						}

					}

					// 檢核EXCEL內容**********************************************************************************
					StringBuffer custErrMsg = new StringBuffer("");

					if (Util.equals(Util.trim(lnf07a_key_1), "")) {
						custErrMsg.append("KEY不得空白");
						findError = true;
					}

					if (Util.equals(Util.trim(lnf07a_explain), "")) {
						custErrMsg.append("EXPLAIN不得空白");
						findError = true;
					}

					if (Util.equals(Util.trim(fullCustId), "")) {
						custErrMsg.append("MEGA ID不得空白");
						findError = true;
					}

					if (fcustId == null) {
						custErrMsg.append("移出CUST ID不得空白");
						findError = true;
					}

					if (fcustNo == null) {
						custErrMsg.append("移出CUST NO不得空白");
						findError = true;
					}

					if (tcustId == null) {
						custErrMsg.append("移入CUST ID不得空白");
						findError = true;
					}

					if (tcustNo == null) {
						custErrMsg.append("移入CUST NO不得空白");
						findError = true;
					}

					if (Util.equals(fbranch, "")) {
						custErrMsg.append("移出分行不得空白");
						findError = true;
					}

					if (Util.equals(tbranch, "")) {
						custErrMsg.append("移入分行不得空白");
						findError = true;
					}

					if (exDate == null) {
						custErrMsg.append("移入日期不得空白");
						findError = true;
					}

					String custKey = custId + "-" + dupNo;

					if (Util.notEquals(custErrMsg.toString(), "")) {
						if (importCustId.containsKey(custKey)) {
							importCustId.put(custKey, importCustId.get(custKey)
									+ "、" + custErrMsg.toString());
						} else {
							importCustId.put(custKey, custErrMsg.toString());
						}
					}

					// 儲存BRTOBR01**********************************************************************************

					if (Util.equals(custErrMsg.toString(), "")) {
						// 沒問題才要產生資料

						if (Util.equals(key_exDate, "")) {

							key_exDate = CapDate.formatDate(exDate,
									"yyyy-MM-dd");

						}

						BRTOBR01 brtobr01 = new BRTOBR01();

						brtobr01.setCustId(custId);
						brtobr01.setDupNo(dupNo);
						brtobr01.setExDate(exDate);
						brtobr01.setFbranch(fbranch);
						brtobr01.setFcustId(fcustId);
						brtobr01.setFcustNo(fcustNo);
						brtobr01.setTbranch(tbranch);
						brtobr01.setTcustId(tcustId);
						brtobr01.setTcustNo(tcustNo);
						brtobr01.setCname(cname);
						brtobr01.setChgCustId(chgCustId);
						brtobr01.setChgDupNo(chgDupNo);

						brtobr01_list_insert.add(brtobr01);// 將資料放入ArrayList的陣列中
					}
				}

				// 引進名單結束************************************************************************************************************************************

				logger.info("引進名單結束");
				if (findError) {

					logger.info("資料匯入失敗，名單檢核有錯誤");

					// L140M01a.message205=資料匯入失敗，錯誤如下:<br>{0}
					StringBuffer allError = new StringBuffer("");

					for (String errCustKey : importCustId.keySet()) {
						String errCustId = errCustKey.split("-")[0];
						String errDupNo = errCustKey.split("-")[1];
						String errCustMsg = importCustId.get(errCustKey);
						if (Util.notEquals(errCustMsg, "")) {
							allError.append(errCustId).append(errDupNo)
									.append("：").append(errCustMsg)
									.append("<br>");
						}

					}

					workbook.close();

					// throw new CapMessageException(MessageFormat.format(
					// pop.getProperty("L140M01a.message205"),
					// allError.toString()), getClass());

					throw new CapMessageException(MessageFormat.format(
							"資料匯入失敗，錯誤如下:<br>{0}", allError.toString()),
							getClass());

				} else {

					logger.info("資料匯入成功，開始上傳檔案");

					// 刪除BRTOBR01舊資料
					logger.info("更新BRTOBR01作業-刪除BRTOBR01舊資料");
					eloandbService.deleteBrToBr01ByExDate(key_exDate);
					// 寫入BRTOBR01
					logger.info("更新BRTOBR01作業-寫入BRTOBR01");
					for (BRTOBR01 brtobr01 : brtobr01_list_insert) {

						String custId = brtobr01.getCustId();
						String dupNo = brtobr01.getDupNo();

						String exDate = CapDate.formatDate(
								brtobr01.getExDate(), "yyyy-MM-dd");
						String fbranch = brtobr01.getFbranch();
						BigDecimal fcustId = brtobr01.getFcustId();
						BigDecimal fcustNo = brtobr01.getFcustNo();
						String tbranch = brtobr01.getTbranch();
						BigDecimal tcustId = brtobr01.getTcustId();
						BigDecimal tcustNo = brtobr01.getTcustNo();
						String cname = brtobr01.getCname();
						String chgCustId = brtobr01.getChgCustId();
						String chgDupNo = brtobr01.getChgDupNo();

						eloandbService.insertBrToBr01(custId, dupNo, exDate,
								fbranch, fcustId, fcustNo, tbranch, tcustId,
								tcustNo, cname, chgCustId, chgDupNo);
					}

					// 上傳名單到LNF07A*****************************************************************
					logger.info("更新LNF07A作業-取得ELOAN要轉檔資料...........");
					// 1.取得ELOAN要轉檔資料
					LinkedHashMap<String, String> cntrNoWithCustIdMap = new LinkedHashMap<String, String>();

					List rows = eloandbService
							.findBrToBr01ListByExDate(key_exDate);

					Iterator it = rows.iterator();

					lnf07a_key_2 = key_exDate; // 2019-01-01

					// 先刪除LNF07A舊資料
					logger.info("更新LNF07A作業-刪除LNF07A舊資料...........");

					List<LNF07A> lnf07a_list_del = new ArrayList<LNF07A>();
					LNF07A lnf07a_del = new LNF07A();
					lnf07a_del.setLnf07a_key_1(lnf07a_key_1);
					lnf07a_del.setLnf07a_key_2(lnf07a_key_2);
					lnf07a_list_del.add(lnf07a_del);
					if (lnf07a_list_del.size() > 0) {
						lnLnf07aService.delete_by_key1_key2(lnf07a_list_del);
					}

					logger.info("更新LNF07A作業-開始上傳LNF07A...........");

					// 開始上傳LNF07A
					int index = 0;
					List<LNF07A> lnf07a_list_insert = new ArrayList<LNF07A>();
					while (it.hasNext()) {
						Map dataMap = (Map) it.next();

						String CUSTID = Util.trim(MapUtils.getString(dataMap,
								"CUSTID"));
						String DUPNO = Util.trim(MapUtils.getString(dataMap,
								"DUPNO"));
						String EXDATE = Util.trim(MapUtils.getString(dataMap,
								"EXDATE"));

						String FBRANCH = Util.trim(MapUtils.getString(dataMap,
								"FBRANCH"));
						String FCUSTID = Util.trim(MapUtils.getString(dataMap,
								"FCUSTID"));
						String FCUSTNO = Util.trim(MapUtils.getString(dataMap,
								"FCUSTNO"));
						String TBRANCH = Util.trim(MapUtils.getString(dataMap,
								"TBRANCH"));
						String TCUSTID = Util.trim(MapUtils.getString(dataMap,
								"TCUSTID"));
						String TCUSTNO = Util.trim(MapUtils.getString(dataMap,
								"TCUSTNO"));
						String CNAME = Util.trim(MapUtils.getString(dataMap,
								"CNAME"));

						String CHGCUSTID = Util.trim(MapUtils.getString(
								dataMap, "CHGCUSTID"));
						String CHGDUPNO = Util.trim(MapUtils.getString(dataMap,
								"CHGDUPNO"));

						if (Util.equals(CUSTID, "") || Util.equals(DUPNO, "")) {
							continue;
						}

						index = index + 1;

						LNF07A lnf07a = new LNF07A();
						// ==========
						String lnf07a_key_3 = CUSTID;
						String lnf07a_key_4 = DUPNO;
						String lnf07a_key_5 = new Integer(index).toString(); // 讓相同ID可以不會DUPLICATE
						String lnf07a_content_1 = FBRANCH;
						String lnf07a_content_2 = TBRANCH;
						String lnf07a_content_3 = CHGCUSTID; // 保留-可放舊額度序號
						String lnf07a_content_4 = CHGDUPNO; // 保留-可放新額度序號
						String lnf07a_content_5 = ""; // 保留

						lnf07a.setLnf07a_key_1(lnf07a_key_1);
						lnf07a.setLnf07a_key_2(lnf07a_key_2);
						lnf07a.setLnf07a_key_3(lnf07a_key_3);
						lnf07a.setLnf07a_key_4(lnf07a_key_4);
						lnf07a.setLnf07a_key_5(lnf07a_key_5);
						lnf07a.setLnf07a_explain(lnf07a_explain);
						lnf07a.setLnf07a_content_1(lnf07a_content_1);
						lnf07a.setLnf07a_content_2(lnf07a_content_2);
						lnf07a.setLnf07a_content_3(lnf07a_content_3);
						lnf07a.setLnf07a_content_4(lnf07a_content_4);
						lnf07a.setLnf07a_content_5(lnf07a_content_5);
						// ==========
						lnf07a_list_insert.add(lnf07a);

					}

					if (lnf07a_list_insert.size() > 0) {
						lnLnf07aService.insert(lnf07a_list_insert);

					}

				}

				workbook.close();

			} catch (IOException e) {
				logger.error(e.getMessage(), e);
				throw new CapMessageException("file IO ERROR", getClass());
			} catch (BiffException be) {
				logger.error(be.getMessage(), be);
				throw new CapMessageException("file IO ERROR", getClass());
			} finally {
				if (is != null) {
					try {
						is.close();
						if (workbook != null) {
							workbook.close();
							workbook = null;
						}
					} catch (IOException e) {
						logger.debug("inputStream close Error", getClass());
					}
				}
			}

			if (Util.isNotEmpty(errMsg)) {
				throw new CapMessageException(errMsg, getClass());
			}

			return new CapAjaxFormResult().set("url", "file?id=" + fileKey)
					.set("fileKey", fileKey).set("imgWidth", dimension[0])
					.set("imgHeight", dimension[1]);
		} else if (Util.equals(fieldId, "uploadCapital")) {
			// uploadCapital
			try {

				is = uFile.getInputStream();
				workbook = Workbook.getWorkbook(is);
				Sheet sheet = workbook.getSheet(0);
				int totalCol = sheet.getColumns();
				if (totalCol == 0) {
					throw new CapMessageException("匯入之名單EXCEL格式錯誤。", getClass());
				}

				HashMap<String, String> importCustId = new HashMap<String, String>();
				int maxItemSeq = 0;

				List<BRTOBR01> brtobr01_list_insert = new ArrayList<BRTOBR01>();

				String key_exDate = "";
				String key_fBranch = "";
				String key_tBranch = "";

				String lnf07a_key_1 = "ELOAN-CAPITAL-01";
				String lnf07a_key_2 = CapDate.formatDate(
						CapDate.getCurrentTimestamp(), "yyyy-MM-dd"); // 2019-01-01
				String lnf07a_explain = "AI370實收資本額";

				// 先刪除LNF07A舊資料
				List<LNF07A> lnf07a_list_del = new ArrayList<LNF07A>();
				LNF07A lnf07a_del = new LNF07A();
				lnf07a_del.setLnf07a_key_1(lnf07a_key_1);
				lnf07a_del.setLnf07a_key_2(lnf07a_key_2);
				lnf07a_list_del.add(lnf07a_del);
				if (lnf07a_list_del.size() > 0) {
					lnLnf07aService.delete_by_key1_key2(lnf07a_list_del);
				}

				// 開始上傳LNF07A
				List<LNF07A> lnf07a_list_insert = new ArrayList<LNF07A>();

				int totalCount = sheet.getRows();

				for (int row = 1; row < sheet.getRows(); row++) {
					int column = 0;

					String custId = "";
					String custName = "";
					String dupNo = "";
					String fullCustId = "";
					String addr = "";

					String taiwanId = ""; // 未使用
					String cntrNo_AS400 = ""; // 未使用
					String cntrNo_ELOAN = ""; // 未使用

					String buildDate = null;
					String exDate = CapDate.getCurrentDate("yyyy-MM-dd");
					String fbranch = "";
					String capital = null;
					String sno = "";
					String cname = "";
					String mCustId = "";

					// 序號
					if (++column <= totalCol) {
						sno = StringUtils.upperCase(Util.trim(getContents(sheet
								.getCell(column - 1, row))));
					}

					// 統一編號
					if (++column <= totalCol) {
						custId = StringUtils.upperCase(Util
								.trim(getContents(sheet
										.getCell(column - 1, row))));
						if (Util.equals(custId, "NA")) {
							continue;
						} else {
							if (StringUtils.length(custId) < 8) {
								custId = Util.getRightStr("00000000" + custId,
										8);
							}

						}
					}

					// 營業地址
					if (++column <= totalCol) {
						addr = StringUtils.upperCase(Util
								.trim(getContents(sheet
										.getCell(column - 1, row))));
						if (Util.equals(addr, "NA")) {
							addr = "";
						}

					}

					// 總機構統一編號
					if (++column <= totalCol) {
						mCustId = StringUtils.upperCase(Util
								.trim(getContents(sheet
										.getCell(column - 1, row))));
						if (Util.equals(mCustId, "NA")) {
							mCustId = "";
						}
					}

					// 營業人名稱
					if (++column <= totalCol) {
						custName = StringUtils.upperCase(Util
								.trim(getContents(sheet
										.getCell(column - 1, row))));
						if (Util.equals(custName, "NA")) {
							custName = "";
						}

					}

					// 資本額
					if (++column <= totalCol) {

						capital = StringUtils.upperCase(Util
								.trim(getContents(sheet
										.getCell(column - 1, row))));
						if (Util.equals(capital, "NA")) {
							continue;
						}

					}

					// 設立日期
					if (++column <= totalCol) {

						String tStr = Util.trim(getContents(sheet.getCell(
								column - 1, row)));
						if (Util.equals(tStr, "NA")) {
							tStr = "";
						}
						if (Util.notEquals(tStr, "")) {

							if (StringUtils.length(tStr) < 7) {
								tStr = Util.getRightStr("00000000" + tStr, 7);
							}

							buildDate = TWNDate.valueOf(tStr).toAD('-');
						}

					}

					// 檢核EXCEL內容**********************************************************************************
					StringBuffer custErrMsg = new StringBuffer("");

					// 上傳名單到LNF07A*****************************************************************

					LNF07A lnf07a = new LNF07A();
					// ==========
					String lnf07a_key_3 = custId;
					String lnf07a_key_4 = dupNo;
					String lnf07a_key_5 = ""; // 保留-
					String lnf07a_content_1 = addr;
					String lnf07a_content_2 = custName;
					String lnf07a_content_3 = capital; // 保留-可放舊額度序號
					String lnf07a_content_4 = buildDate; // 保留-可放新額度序號
					String lnf07a_content_5 = exDate; // 保留-

					lnf07a.setLnf07a_key_1(lnf07a_key_1);
					lnf07a.setLnf07a_key_2(lnf07a_key_2);
					lnf07a.setLnf07a_key_3(lnf07a_key_3);
					lnf07a.setLnf07a_key_4(lnf07a_key_4);
					lnf07a.setLnf07a_key_5(lnf07a_key_5);
					lnf07a.setLnf07a_explain(lnf07a_explain);
					lnf07a.setLnf07a_content_1(lnf07a_content_1);
					lnf07a.setLnf07a_content_2(lnf07a_content_2);
					lnf07a.setLnf07a_content_3(lnf07a_content_3);
					lnf07a.setLnf07a_content_4(lnf07a_content_4);
					lnf07a.setLnf07a_content_5(lnf07a_content_5);
					// ==========
					lnf07a_list_insert.add(lnf07a);

				}

				if (lnf07a_list_insert.size() > 0) {
					lnLnf07aService.insert(lnf07a_list_insert);
				}

				workbook.close();

			} catch (IOException e) {
				logger.error(e.getMessage(), e);
				throw new CapMessageException("file IO ERROR", getClass());
			} catch (BiffException be) {
				logger.error(be.getMessage(), be);
				throw new CapMessageException("file IO ERROR", getClass());
			} finally {
				if (is != null) {
					try {
						is.close();
						if (workbook != null) {
							workbook.close();
							workbook = null;
						}
					} catch (IOException e) {
						logger.debug("inputStream close Error", getClass());
					}
				}
			}

			if (Util.isNotEmpty(errMsg)) {
				throw new CapMessageException(errMsg, getClass());
			}

			return new CapAjaxFormResult().set("url", "file?id=" + fileKey)
					.set("fileKey", fileKey).set("imgWidth", dimension[0])
					.set("imgHeight", dimension[1]);
		} else if (Util.equals(fieldId, "uploadTripleCoupon")) {
			// J-109-0519_05097_B1001 Web e-Loan產生央行C方案借款人，且兌付振興三倍券達888張之名單
			try {

				is = uFile.getInputStream();
				workbook = Workbook.getWorkbook(is);
				Sheet sheet = workbook.getSheet(0);
				int totalCol = sheet.getColumns();
				if (totalCol == 0) {
					throw new CapMessageException("匯入之名單EXCEL格式錯誤。", getClass());
				}

				List<L180R60A> l1580r60alist = lms2105v01Service
						.findL180r60aAll();

				if (l1580r60alist != null && !l1580r60alist.isEmpty()) {
					lms2105v01Service.deleteListL180r60a(l1580r60alist);
				}

				// 開始上傳LNF07A
				List<L180R60A> l180r60a_list_insert = new ArrayList<L180R60A>();

				int totalCount = sheet.getRows();

				for (int row = 0; row < sheet.getRows(); row++) {
					int column = 0;

					String caseBrId = "";
					String dataType = "";
					String custId = "";
					String custName = "";
					BigDecimal coupon200 = BigDecimal.ZERO;
					BigDecimal coupon500 = BigDecimal.ZERO;
					BigDecimal couponTot = BigDecimal.ZERO;
					BigDecimal couponAmt = BigDecimal.ZERO;

					// 分行代號
					if (++column <= totalCol) {
						caseBrId = StringUtils.upperCase(Util
								.trim(getContents(sheet
										.getCell(column - 1, row))));
					}

					// 分行名稱
					if (++column <= totalCol) {
						// 不寫入DB
					}

					// 本行/跨行
					if (++column <= totalCol) {
						dataType = StringUtils.upperCase(Util
								.trim(getContents(sheet
										.getCell(column - 1, row))));
						if (Util.equals(dataType, "本行")) {
							dataType = "1";
						} else {
							dataType = "2"; // 跨行
						}
					}

					// 統一編號
					if (++column <= totalCol) {
						custId = StringUtils.upperCase(Util
								.trim(getContents(sheet
										.getCell(column - 1, row))));
					}

					// 客戶名稱
					if (++column <= totalCol) {
						custName = Util.trim(getContents(sheet.getCell(
								column - 1, row)));
					}

					// 200元券(張)
					if (++column <= totalCol) {
						String tValue = NumConverter.delCommaString(Util
								.trim(getContents(sheet
										.getCell(column - 1, row))));

						coupon200 = Util.notEquals(tValue, "")
								&& Util.isNumeric(tValue) ? Util
								.parseBigDecimal(tValue) : BigDecimal.ZERO;

					}

					// 500元券(張)
					if (++column <= totalCol) {
						String tValue = NumConverter.delCommaString(Util
								.trim(getContents(sheet
										.getCell(column - 1, row))));

						coupon500 = Util.notEquals(tValue, "")
								&& Util.isNumeric(tValue) ? Util
								.parseBigDecimal(tValue) : BigDecimal.ZERO;

					}

					// 總張數
					if (++column <= totalCol) {
						String tValue = NumConverter.delCommaString(Util
								.trim(getContents(sheet
										.getCell(column - 1, row))));

						couponTot = Util.notEquals(tValue, "")
								&& Util.isNumeric(tValue) ? Util
								.parseBigDecimal(tValue) : BigDecimal.ZERO;

					}

					// 總金額
					if (++column <= totalCol) {
						String tValue = NumConverter.delCommaString(Util
								.trim(getContents(sheet
										.getCell(column - 1, row))));

						couponAmt = Util.notEquals(tValue, "")
								&& Util.isNumeric(tValue) ? Util
								.parseBigDecimal(tValue) : BigDecimal.ZERO;

					}

					// 檢核EXCEL內容**********************************************************************************
					StringBuffer custErrMsg = new StringBuffer("");

					// 上傳名單到LNF07A*****************************************************************

					L180R60A l180r60a = new L180R60A();

					l180r60a.setCaseBrId(caseBrId);
					l180r60a.setDataType(dataType);
					l180r60a.setCustId(custId);
					l180r60a.setCustName(custName);
					l180r60a.setCoupon200(coupon200);
					l180r60a.setCoupon500(coupon500);
					l180r60a.setCouponTot(couponTot);
					l180r60a.setCouponAmt(couponAmt);
					l180r60a.setCreator("J09519");
					l180r60a.setCreateTime(CapDate.getCurrentTimestamp());
					l180r60a.setUpdater("J09519");
					l180r60a.setUpdateTime(CapDate.getCurrentTimestamp());
					// ==========
					l180r60a_list_insert.add(l180r60a);

				}

				if (l180r60a_list_insert.size() > 0) {
					lms2105v01Service.saveListL180r60a(l180r60a_list_insert);
				}

				workbook.close();

			} catch (IOException e) {
				logger.error(e.getMessage(), e);
				throw new CapMessageException("file IO ERROR", getClass());
			} catch (BiffException be) {
				logger.error(be.getMessage(), be);
				throw new CapMessageException("file IO ERROR", getClass());
			} finally {
				if (is != null) {
					try {
						is.close();
						if (workbook != null) {
							workbook.close();
							workbook = null;
						}
					} catch (IOException e) {
						logger.debug("inputStream close Error", getClass());
					}
				}
			}

			if (Util.isNotEmpty(errMsg)) {
				throw new CapMessageException(errMsg, getClass());
			}

			return new CapAjaxFormResult().set("url", "file?id=" + fileKey)
					.set("fileKey", fileKey).set("imgWidth", dimension[0])
					.set("imgHeight", dimension[1]);
		} else if (Util.equals(fieldId, "uploadRetrialL224File")) {
			// J-110-0304_05097_B1003 Web e-Loan授信覆審配合RPA作業修改

			String errorMsg = "";
			result.set("errorMsg", errorMsg);

			try {
				errorMsg = lms2105v01Service.rpaUpdateLmsReCheckReport(params, uFile);
				result.set("errorMsg", errorMsg);
			} catch (Exception e) {
				logger.error(e.getMessage(), e);
				throw new CapMessageException("file IO ERROR", getClass());
			} finally {
				if (is != null) {
					try {
						is.close();
					} catch (IOException e) {
						logger.debug("inputStream close Error", getClass());
					}
				}
				result.set("errorMsg", errorMsg);
			}

			if (Util.isNotEmpty(errMsg)) {
				throw new CapMessageException(errMsg, getClass());
			}

			// return new CapAjaxFormResult().set("url", "file?id=" + fileKey)
			// .set("fileKey", fileKey).set("errorMsg", errorMsg);

			return result;

		} else if (Util.equals(fieldId, "uploadEsgFile")) {
			// J-111-0423_05097_B1001 Web
			// e-Loan企金授信就海外分行承做永續績效連結授信案(如附件)，於E-Loan「永續績效連結授信」相關註記
			try {

				is = uFile.getInputStream();
				workbook = Workbook.getWorkbook(is);
				Sheet sheet = workbook.getSheet(0);
				int totalCol = sheet.getColumns();
				if (totalCol == 0) {
					throw new CapMessageException("匯入之名單EXCEL格式錯誤。", getClass());
				}

				List<Map<String, String>> dataList = new ArrayList<Map<String, String>>();

				int totalCount = sheet.getRows();

				for (int row = 1; row < sheet.getRows(); row++) {
					int column = 0;

					String cntrNo = "";
					String esgSustainLoan = "";
					String esgSustainLoanType_E = "";
					String esgSustainLoanType_S = "";
					String esgSustainLoanType_G = "";
					String esgSustainLoanUnReach = "";

					// 額度序號
					if (++column <= totalCol) {
						cntrNo = StringUtils.upperCase(Util
								.trim(getContents(sheet
										.getCell(column - 1, row))));
					}

					// 永續績效連結授信類別 E
					if (++column <= totalCol) {
						esgSustainLoanType_E = StringUtils.upperCase(Util
								.trim(getContents(sheet
										.getCell(column - 1, row))));
					}

					// 永續績效連結授信類別 S
					if (++column <= totalCol) {
						esgSustainLoanType_S = StringUtils.upperCase(Util
								.trim(getContents(sheet
										.getCell(column - 1, row))));
					}

					// 永續績效連結授信類別 G
					if (++column <= totalCol) {
						esgSustainLoanType_G = StringUtils.upperCase(Util
								.trim(getContents(sheet
										.getCell(column - 1, row))));
					}

					// 未達成永續連結條件(Y/N)
					if (++column <= totalCol) {
						esgSustainLoanUnReach = StringUtils.upperCase(Util
								.trim(getContents(sheet
										.getCell(column - 1, row))));
					}

					// 檢核EXCEL內容**********************************************************************************
					StringBuffer custErrMsg = new StringBuffer("");

					// 上傳名單到LNF07A*****************************************************************

					if (Util.equals(Util.trim(cntrNo), "")) {
						break;
					}

					if (Util.notEquals(esgSustainLoanType_E, "")
							|| Util.notEquals(esgSustainLoanType_S, "")
							|| Util.notEquals(esgSustainLoanType_G, "")) {
						esgSustainLoan = "Y";
					}

					HashMap<String, String> rowData = new HashMap<String, String>();

					rowData.put("cntrNo", cntrNo);
					rowData.put("esgSustainLoan", esgSustainLoan);
					rowData.put("esgSustainLoanType_E", esgSustainLoanType_E);
					rowData.put("esgSustainLoanType_S", esgSustainLoanType_S);
					rowData.put("esgSustainLoanType_G", esgSustainLoanType_G);
					rowData.put("esgSustainLoanUnReach", esgSustainLoanUnReach);
					dataList.add(rowData);

				}

				if (dataList != null && !dataList.isEmpty()) {
					lms2105v01Service.processEsgFile(dataList);
				}

				workbook.close();

			} catch (IOException e) {
				logger.error(e.getMessage(), e);
				throw new CapMessageException("file IO ERROR", getClass());
			} catch (BiffException be) {
				logger.error(be.getMessage(), be);
				throw new CapMessageException("file IO ERROR", getClass());
			} finally {
				if (is != null) {
					try {
						is.close();
						if (workbook != null) {
							workbook.close();
							workbook = null;
						}
					} catch (IOException e) {
						logger.debug("inputStream close Error", getClass());
					}
				}
			}

			if (Util.isNotEmpty(errMsg)) {
				throw new CapMessageException(errMsg, getClass());
			}

			return new CapAjaxFormResult().set("url", "file?id=" + fileKey)
					.set("fileKey", fileKey).set("imgWidth", dimension[0])
					.set("imgHeight", dimension[1]);
		} else if (Util.equals(fieldId, "uploadBisFile")) {
			// J-111-0443_05097_B1001 Web e-Loan企金授信開發授信BIS評估表

			String errorMsg = "";
			String lmsBisSelfCapital = ""; // 自有資本(E46)
			String lmsBisMegaRwa = ""; // 風險性資產(E61)

			try {

				is = uFile.getInputStream();
				workbook = Workbook.getWorkbook(is);

				Sheet sheet = null;
				int totalCol = 0;

				if (true) {
					Sheet sheet0 = workbook.getSheet(0);
					int totalCol0 = sheet0.getColumns();
					String sheetName0 = sheet0.getName();
					logger.info("sheet 0 totalCol:[" + totalCol0 + "]");

					String sheet0ColumnF4 = "";
					if (totalCol0 >= 0) {
						if (StringUtils.contains(sheetName0, "仟元")) {
							sheet = sheet0;
							totalCol = totalCol0;
						}
					}
				}

				if (true) {
					Sheet sheet1 = workbook.getSheet(1);
					int totalCol1 = sheet1.getColumns();
					String sheetName1 = sheet1.getName();
					logger.info("sheet 1 totalCol:[" + totalCol1 + "]");

					String sheet1ColumnF4 = "";
					if (totalCol1 >= 0) {
						if (StringUtils.contains(sheetName1, "仟元")) {
							sheet = sheet1;
							totalCol = totalCol1;
						}
					}
				}

				if (totalCol == 0) {
					throw new CapMessageException("匯入之名單EXCEL格式錯誤。", getClass());
				}

				lmsBisSelfCapital = StringUtils.upperCase(Util
						.trim(getContents(sheet.getCell(4, 45)))); // 自有資本(E46)
				lmsBisMegaRwa = StringUtils.upperCase(Util
						.trim(getContents(sheet.getCell(4, 60)))); // 風險性資產(E61)

				workbook.close();

			} catch (IOException e) {
				logger.error(e.getMessage(), e);
				throw new CapMessageException("file IO ERROR", getClass());
			} catch (BiffException be) {
				logger.error(be.getMessage(), be);
				throw new CapMessageException("file IO ERROR", getClass());
			} finally {
				if (is != null) {
					try {
						is.close();
						if (workbook != null) {
							workbook.close();
							workbook = null;
						}
					} catch (IOException e) {
						logger.debug("inputStream close Error", getClass());
					}
				}
			}

			if (Util.isNotEmpty(errMsg)) {
				throw new CapMessageException(errMsg, getClass());
			}

			return new CapAjaxFormResult().set("url", "file?id=" + fileKey)
					.set("fileKey", fileKey).set("imgWidth", dimension[0])
					.set("imgHeight", dimension[1]).set("errorMsg", errorMsg)
					.set("lmsBisSelfCapital", lmsBisSelfCapital)
					.set("lmsBisMegaRwa", lmsBisMegaRwa);
		} else if (Util.equals(fieldId, "uploadBatGutFile")) {
			// J-112-0366_12473_B1001 Web e-Loan企金授信批次信用保證統計表總表
			try {
				is = uFile.getInputStream();
				workbook = Workbook.getWorkbook(is);
				Sheet sheet = workbook.getSheet(0);
				int totalCol = sheet.getColumns();
				if (totalCol == 0) {
					throw new CapMessageException("匯入之名單EXCEL格式錯誤。", getClass());
				}

				List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();
				String id = UUID
						.randomUUID()
						.toString()
						.toUpperCase()
						.replaceAll(TextDefine.SYMBOL_DASH,
								TextDefine.EMPTY_STRING);
				String ssoUnitNo = user.getSsoUnitNo();
				String userId = user.getUserId();
				for (int row = 1; row < sheet.getRows(); row++) {
					if (row >= 16) { // 資料從17行開始
						HashMap<String, Object> rowData = new HashMap<String, Object>();

						String colKey1 = "columnKey1";
						String colVal1 = Util.trim(getContents(sheet.getCell(
								3 - 1, row))); // 批號
						String colKey2 = "columnKey2";
						String colVal2 = Util.trim(getContents(sheet.getCell(
								15 - 1, row))); // 逾期融資
						String colKey3 = "columnKey3";
						String colVal3 = Util.trim(getContents(sheet.getCell(
								23 - 1, row))); // 調整後實際可代償金額計提
						String colKey4 = "columnKey4";
						String colVal4 = userId; // 上傳人員
						String colKey5 = "columnKey5";
						Timestamp colVal5 = CapDate.getCurrentTimestamp();

						rowData.put(colKey1, colVal1);
						rowData.put(colKey2, colVal2);
						rowData.put(colKey3, colVal3);
						rowData.put(colKey4, colVal4);
						rowData.put(colKey5, colVal5);

						if (!colVal1.isEmpty()) {
							dataList.add(rowData);
						}
					}
				}

				// 因會上傳空白檔案，不做空值判斷
				// if (dataList != null && !dataList.isEmpty()) {
				List<DocFile> ftpFiles = lms2105v01Service.processBatGutFile(
						dataList, id, ssoUnitNo);
				lms2105v01Service.sendToFTP(ftpFiles, id);
				// }

				workbook.close();

			} catch (IOException e) {
				logger.error(e.getMessage(), e);
				throw new CapMessageException("file IO ERROR", getClass());
			} catch (BiffException be) {
				logger.error(be.getMessage(), be);
				throw new CapMessageException("file IO ERROR", getClass());
			} finally {
				if (is != null) {
					try {
						is.close();
						if (workbook != null) {
							workbook.close();
							workbook = null;
						}
					} catch (IOException e) {
						logger.debug("inputStream close Error", getClass());
					}
				}
			}

			if (Util.isNotEmpty(errMsg)) {
				throw new CapMessageException(errMsg, getClass());
			}

			return new CapAjaxFormResult().set("url", "file?id=" + fileKey)
					.set("fileKey", fileKey).set("imgWidth", dimension[0])
					.set("imgHeight", dimension[1]);
		} else {
			// 沒處理的檔案
			return new CapAjaxFormResult().set("url", "file?id=" + fileKey)
					.set("fileKey", fileKey).set("imgWidth", dimension[0])
					.set("imgHeight", dimension[1]);
		}

	}

	private String getContents(Cell cell) {
		DateCell dCell = null;
		if (cell.getType() == CellType.DATE) {
			dCell = (DateCell) cell;
			// System.out.println("Value of Date Cell is: " + dCell.getDate());
			// ==> Value of Date Cell is: Thu Apr 22 02:00:00 CEST 2088
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
			// System.out.println(sdf.format(dCell.getDate()));
			// ==> 2088-04-22
			return sdf.format(dCell.getDate());
		}
		// possibly manage other types of cell in here if needed for your goals
		// read more:
		// http://www.quicklyjava.com/reading-excel-file-in-java-datatypes/#ixzz2fYIkHdZP
		return cell.getContents();
	}

	/**
	 * 將字串轉為BigDecimal格式
	 * 
	 * @param in
	 *            the input
	 * @return BigDecimal
	 */
	public String getBigDecimalString(String in) {

		char[] ca = in.toCharArray();
		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < ca.length; i++) {
			switch (ca[i]) {
			case '-':
			case '+':
			case '0':
			case '1':
			case '2':
			case '3':
			case '4':
			case '5':
			case '6':
			case '7':
			case '8':
			case '9':
			case '.':
				sb.append(ca[i]);
			}
		}
		return sb.toString();
	}
}
