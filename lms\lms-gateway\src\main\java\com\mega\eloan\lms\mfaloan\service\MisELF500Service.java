/* 
 *MisELF500Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.mfaloan.service;

import java.util.List;
import java.util.Map;

import com.mega.eloan.lms.mfaloan.bean.ELF500;

/**
 * <pre>
 * 消金額度介面檔 MIS.ELF500
 * </pre>
 * 
 * @since 2013/1/17
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/1/17,REX,new
 *          </ul>
 */
public interface MisELF500Service {

	/**
	 * 查詢額度相關資料
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @return
	 */
	public List<ELF500> findByCustId_tmeStamp_1year(String custId, String dupNo);

	/**
	 * 查詢額度相關資料
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @param cntrNo
	 *            額度序號
	 * @return
	 */
	public ELF500 findByCustIdAndCntrno_tmeStamp_1year(String custId, String dupNo,
			String cntrNo);
	
	public ELF500 findByCustIdAndCntrno1(String custId, String dupNo,
			String cntrNo);

	public ELF500 findByCntrNo(String cntrNo);
	
	public List<ELF500> findByCustIdALL(String custId, String dupNo);
	
	public List<Map<String, Object>> findNeedUpdateFincomeData(String eloanTime);
	
	public void updateFincomeByCntrNo(int fincome, String cntrNo);

}
