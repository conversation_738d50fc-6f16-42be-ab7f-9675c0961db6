package com.mega.eloan.lms.fms.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.model.L300M01A;
import com.mega.eloan.lms.model.L300M01B;
import com.mega.eloan.lms.model.L300M01C;
import com.mega.eloan.lms.model.L300S01A;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;

/**
 * <pre>
 * 覆審考核表作業
 * </pre>
 * 
 * @since 2022
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
public interface LMS8100Service extends AbstractService {
		
    @SuppressWarnings("rawtypes")
    public Page<? extends GenericBean> findPage(Class clazz, ISearch search);

    @SuppressWarnings("rawtypes")
    public <T extends GenericBean> T findModelByOid(Class clazz, String oid);

    @SuppressWarnings("rawtypes")
    public <T extends GenericBean> T findModelByMainId(Class clazz, String mainId);

    @SuppressWarnings("rawtypes")
    public List<? extends GenericBean> findListByMainId(Class clazz, String mainId);
    
    public void flowAction(String mainOid, L300M01A model, boolean setResult, 
    		boolean resultType, boolean upMis) throws Throwable;

    public Map<String, String> getItemTypeMap(String verStr);
    
    public Map<String, BigDecimal> getItemScoreMap(String verStr);

    public String buildPaFormHtml(L300M01A l300m01a, Properties prop);
    
    public List<L300M01A> findL300m01aList(String ownBrId, String branchId, String bgnDate, String endDate);
    
    public L300M01A findL300m01a(String ownBrId, String branchId, String bgnDate, String endDate);
    
	/**
	 * J-112-0461 授信覆審考核表-編製中-下拉選項統計由每半年改為每季統計
	 * 
	 * @param ownBrId
	 * @param branchId
	 * @param bgnDate
	 * @param endDate
	 * @return
	 */
	public L300M01A findL300m01aExist(String ownBrId, String branchId,
			String bgnDate, String endDate);
    
    public void saveL300m01aList(List<L300M01A> list, boolean isNew);
    
    public boolean deleteL300m01as(String[] oids);
    
	public L300M01B findL300m01b(String mainId, String branchType, String branchId,
			String staffNo, String staffJob);
	
    public void saveL300m01bList(List<L300M01B> list);
    
    public void deleteL300m01bs(List<L300M01B> l300m01bs);
    
    public List<L300S01A> findL300s01aList(String mainId);
    
    public L300S01A findL300s01a(String mainId, String itemType, String itemName);
    	
    public void saveL300s01aList(List<L300S01A> list);
    
    public void deleteL300s01as(List<L300S01A> list);
    
    public List<L300M01A> findL300m01aProduceList(String ownBrId, String docStatus, 
    		String bgnDate, String endDate, String produceFlag);
    
    public L300M01C findL300m01c(String ownBrId, String bgnDate, String endDate);
    
	/**
	 * J-112-0461 授信覆審考核表-編製中-下拉選項統計由每半年改為每季統計。 <br/>
	 * 查詢統計週期 <br/>
	 * 
	 * 每半年 HY <br/>
	 * 每季 S <br/>
	 * 
	 * @param dt
	 * @return
	 */
	public String findProduceInterval(String dt);
    
    public void setBackActLog(Meta meta);
}
