package com.mega.eloan.lms.lrs.report.impl;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;
import java.util.TreeMap;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import com.iisigroup.cap.component.PageParameters;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.PdfTools;
import tw.com.jcs.common.report.ReportGenerator;

import com.inet.report.ReportException;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.LrsUtil;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.dao.L180M01ADao;
import com.mega.eloan.lms.lrs.pages.LMS1810M01Page;
import com.mega.eloan.lms.lrs.report.LMS1800R03RptService;
import com.mega.eloan.lms.lrs.service.LMS1801Service;
import com.mega.eloan.lms.mfaloan.service.MisELF411Service;
import com.mega.eloan.lms.model.L170M01A;
import com.mega.eloan.lms.model.L180M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

@Service("lms1800r03rptservcie")
public class LMS1800R03RptServiceImpl implements FileDownloadService,
		LMS1800R03RptService {

	@Resource
	L180M01ADao l180m01aDao;

	@Resource
	LMS1801Service lms1801Service;

	@Resource
	RetrialService retrialService;

	@Resource
	BranchService branchService;

	@Resource
	MisELF411Service misELF411Service;
	@Resource
	CodeTypeService codeTypeService;
	
	SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

	@Override
	public byte[] getContent(PageParameters params) throws CapException,
			FileNotFoundException, ReportException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
			String mode = Util.trim(params.getString("mode"));
			if (Util.equals("A", mode)) {
				baos = (ByteArrayOutputStream) this.generatePdf(params);
			} else if (Util.equals("3", mode) || Util.equals("4", mode)) {
				baos = (ByteArrayOutputStream) this.generateXls(params);
			}
			if (baos == null) {
				return null;
			} else {
				return baos.toByteArray();
			}
		} finally {
			if (baos != null) {
				baos.close();
			}

		}
	}

	private ByteArrayOutputStream generateXls(PageParameters params)
			throws IOException, Exception {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mode = Util.trim(params.getString("mode"));
		String dataDate = Util.trim(params.getString("dataDate"));
		Date dataDateObj = sdf.parse(dataDate + "-01");
		String[] brNos = Util.trim(params.getString("brNos")).split("\\|");
		;
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		if (Util.equals("3", mode)) {
			ISearch search = l180m01aDao.createSearchTemplete();
			search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
					user.getUnitNo());
			search.addSearchModeParameters(SearchMode.EQUALS, "dataDate",
					dataDateObj);
			search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
					null);
			search.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
					RetrialDocStatusEnum.編製中.getCode());
			search.addOrderBy("branchId");
			List<L180M01A> list = l180m01aDao.find(search);

			lms1801Service.gfnGenCTLListExcel_GroupByBranch(outputStream,
					dataDate, list);
		} else if (Util.equals("4", mode)) {
			Date sysMonth_1st = CrsUtil.get_sysMonth_1st();
			String tELF411_DATADATE = LrsUtil
					.elf412_rocDateStr_from_Date(CapDate.addMonth(sysMonth_1st,
							-1));
			if (misELF411Service.isELF411Ready(tELF411_DATADATE)) {

				List<String> brNo_list = new ArrayList<String>();
				for (String brNo : brNos) {
					if (retrialService.existL180M01Z_sysMonth(brNo)) {
						brNo_list.add(brNo);
					}
				}
				lms1801Service.gfnGenerateCTL_FLMS180R13(outputStream,
						dataDate, brNo_list);
			} else {
				lms1801Service.genExcelWithMsg(outputStream,
						"中心覆審控制檔ELF411本月資料尚未完畢");
			}
		}
		if (outputStream != null) {
			outputStream.flush();
		}
		return outputStream;
	}

	private OutputStream generatePdf(PageParameters params) throws IOException,
			Exception {

		List<InputStream> list = new LinkedList<InputStream>();
		OutputStream outputStream = null;

		// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		String ctlType = params.getString("ctlType", LrsUtil.CTLTYPE_主辦覆審);

		int subLine = 9;// 此數值對應的(x,y).要查 PdfTools.並不一定是愈小,愈上面
		try {
			Locale locale = LMSUtil.getLocale();
			String[] oids = Util.trim(params.getString("oids")).split("\\|");
			if (oids != null && oids.length > 0) {
				boolean paginate = true;
				Properties propEloanPage = MessageBundleScriptCreator
						.getComponentResource(AbstractEloanPage.class);
				String paginationText = propEloanPage
						.getProperty("PaginationText");// 頁次：第 {0} 頁/共 {1} 頁
				for (String oid : oids) {
					L180M01A meta = retrialService.findL180M01A_oid(oid);
					if (meta == null) {
						continue;
					}

					// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
					OutputStream out_meta = genLMS1800R03(locale, meta, ctlType);
					List<InputStream> streamOfPDFFiles = new ArrayList<InputStream>();
					streamOfPDFFiles.add(new ByteArrayInputStream(
							((ByteArrayOutputStream) out_meta).toByteArray()));
					outputStream = new ByteArrayOutputStream();
					PdfTools.mergeReWritePagePdf(streamOfPDFFiles,
							outputStream, paginationText, paginate, locale,
							subLine);

					list.add(new ByteArrayInputStream(
							((ByteArrayOutputStream) outputStream)
									.toByteArray()));
				}
			}

			outputStream = new ByteArrayOutputStream();
			PdfTools.mergeReWritePagePdf(list, outputStream);
		} finally {

		}
		return outputStream;
	}

	// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
	private OutputStream genLMS1800R03(Locale locale, L180M01A meta,
			String ctlType) throws FileNotFoundException, IOException,
			Exception {
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();

		String rptFile = "LMS1800R03";

		ReportGenerator generator = new ReportGenerator("report/lrs/" + rptFile
				+ "_" + locale.toString() + ".rpt");
		OutputStream outputStream = null;

		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1810M01Page.class);

		try {
			// 分行名稱
			rptVariableMap.put("BRANCHNAME",
					Util.trim(branchService.getBranchName(meta.getBranchId()))
							+ "(" + meta.getBranchId() + ")");
			rptVariableMap.put("L180M01A.RETRIALDATE",
					TWNDate.toAD(meta.getDefaultCTLDate()));

			// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
			rptVariableMap.put("CTLTYPE",
					Util.equals(Util.trim(ctlType), "") ? LrsUtil.CTLTYPE_主辦覆審
							: Util.trim(ctlType));

			// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能

			// rptVariableMap
			// .put("CTLTYPE_DECR",
			// Util.equals(Util.trim(ctlType),
			// LrsUtil.CTLTYPE_自辦覆審) ? prop
			// .getProperty("L180M01B.ctlType_B") : prop
			// .getProperty("L180M01B.ctlType_A"));

			rptVariableMap.put("CTLTYPE_DECR", StringUtils.replace(Util
					.trim(codeTypeService.findByCodeTypeAndCodeValue(
							"lms1815m01_elfCtlType", Util.trim(ctlType))
							.getCodeDesc()), Util.trim(ctlType) + ".", ""));

			if (true) {
				// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
				// J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
				List<L170M01A> l170m01a_list = retrialService
						.getLrsRetrialSummaryList(meta, ctlType);
				rptVariableMap.put("L180M01A.REQUANTITY",
						Util.trim(l170m01a_list.size()));

				if (Util.equals(ctlType, LrsUtil.CTLTYPE_自辦覆審)) {
					rptVariableMap
							.put("CONTENT",
									getContentString(retrialService
											.getLrsRetrialSummary_ctlTypeB(l170m01a_list)));
				} else if (Util.equals(ctlType, LrsUtil.CTLTYPE_價金履約)) {
					// J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
					rptVariableMap
							.put("CONTENT",
									getContentString(retrialService
											.getLrsRetrialSummary_ctlTypeC(l170m01a_list)));
				} else {
					rptVariableMap.put("CONTENT",
							getContentString(retrialService
									.getLrsRetrialSummary(l170m01a_list, false)));
				}

			}

			generator.setLang(locale);
			generator.setVariableData(rptVariableMap);
			generator.setRowsData(titleRows);

			outputStream = generator.generateReport();
		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
		}
		return outputStream;
	}

	private String getContentString(TreeMap<String, List<String>> map) {
		boolean haveData = false;
		StringBuffer sb = new StringBuffer();
		sb.append("<table border='0'>");
		for (String keyStr : map.keySet()) {
			List<String> detailList = map.get(keyStr);
			if (CollectionUtils.isEmpty(detailList)) {
				continue;
			}
			haveData = true;
			int detailCnt = detailList.size();
			sb.append("<tr><td colspan='3'>");
			sb.append("第")
					.append(NumberUtils.toInt(keyStr.substring(0,
							CrsUtil.SUMMARY_PREFIX_LEN))).append("項")
					.append(keyStr.substring(CrsUtil.SUMMARY_PREFIX_LEN))
					.append("共" + detailCnt + "戶");
			sb.append("</td></tr>");
			for (int i = 0; i < detailCnt; i++) {
				sb.append("<tr>");
				sb.append("<td>").append("　").append("</td>");
				sb.append("<td>(").append(i + 1).append(")</td>");
				sb.append("<td>").append(detailList.get(i)).append("</td>");
				sb.append("</tr>");
			}

		}
		sb.append("</table>");

		return haveData ? sb.toString() : "本分行本次覆審無任何缺失。";
	}

}
