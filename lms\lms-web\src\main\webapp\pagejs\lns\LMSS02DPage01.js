/**
 *婉卻紀錄共用元件js 
 */
initDfd.done(function(){
    setCloseConfirm(true);	
});

/**
 *查詢 申貸戶 婉卻紀錄 
 */
function getReject(){
    $.ajax({
        handler: __handler,
        type: "POST",
        dataType: "json",
        data: {
            formAction: "getReject",
            mainId: responseJSON.mainId,
            custId: $("#L120S01aForm").find("#custId").html(),
            dupNo: $("#L120S01aForm").find("#dupNo").html()
        },
        success: function(json){
			$("#L120S01aForm").setData(json, false);
			var rejtCaseAdjMemo = $("#L120S01aForm").find("#rejtCaseAdjMemo").val();
			if(rejtCaseAdjMemo == undefined || rejtCaseAdjMemo == null || rejtCaseAdjMemo == ""){
				$("#L120S01aForm .hideMemo").hide();
			}else{
				$("#L120S01aForm .hideMemo").show();
			}
        }
    });	
}

/**
 *查詢 負責人 婉卻紀錄 
 */
function getReject1(){
	var val_chairmanId =  $("#L120S01aForm").find("#chairmanId").val();
    var val_chairmanDupNo  = $("#L120S01aForm").find("#chairmanDupNo").val();
	
	$.ajax({
        handler: __handler,
        type: "POST",
        dataType: "json",
        data: {
            formAction: "getReject1",
            mainId: responseJSON.mainId,
            custId: $("#L120S01aForm").find("#custId").val(),
            dupNo: $("#L120S01aForm").find("#dupNo").val(),
            chairmanId: val_chairmanId,
            chairmanDupNo: val_chairmanDupNo,
            chairmanVisible : $("#L120S01aForm").find("#chairmanId").is(':visible')
        },
        success: function(json){
        	if(json.isPassed){        		
        		$("#L120S01aForm").setData(json, false);
        		if(json.L120S01aForm.IsRejt1==""){
        			$("#L120S01aForm").find("[name=IsRejt1]").attr("checked", false);        			
        		}
    			var rejtCaseAdjMemo1 = $("#L120S01aForm").find("#rejtCaseAdjMemo1").val();
    			if(rejtCaseAdjMemo1 == undefined || rejtCaseAdjMemo1 == null || rejtCaseAdjMemo1 == ""){
    				$("#L120S01aForm .hideMemo1").hide();
    			}else{
    				$("#L120S01aForm .hideMemo1").show();
    			}    			
        	}
			
        }
    });	
	    	
}

/**
 *修改 申貸戶 婉卻控管種類ThickBox
 */
function editReject(){
	var rejtVal = $("[name='rejtCase']:radio:checked").val();
	var rejtCaseBefore = $("[name='rejtCaseBefore']:radio:checked").val();
	// 婉卻控管Radio設定
	$("[name='_rejtCase']").each(function(i){
		var $this = $(this);
		if(rejtVal == $this.val()){
			$this.attr("checked",true);
		}
	});
	// 婉卻控管變更前Radio設定
	$("[name='_rejtCaseBefore']").each(function(i){
		var $this = $(this);
		if(rejtCaseBefore == $this.val()){
			$this.attr("checked",true);
		}
	});
    var editReject = $("#editReject").thickbox({
        // 使用選取的內容進行彈窗
        title: i18n.lmscommom["other.msg127"],	//other.msg127=修改婉卻控管種類
        width: 500,
        height: 200,
        align: 'center',
        valign: 'bottom',
        modal: true,
        i18n: i18n.def,
        buttons: {
            "sure": function(showMsg){
                $.ajax({
                    type: "POST",
                    handler: __handler,
                    data: {
                        formAction: "editReject",
                        _rejtCase: $("[name='_rejtCase']:radio:checked").val(),
                        _rejtCaseBefore: rejtVal,
			            custId: $("#L120S01aForm").find("#custId").html(),
			            dupNo: $("#L120S01aForm").find("#dupNo").html()                        
                    },
                    success: function(responseData){
                        $("#L120S01aForm").setData(responseData.L120S01aForm,false);
						$(".showBefore").show();
						var rejtCaseAdjMemo = $("#L120S01aForm").find("#rejtCaseAdjMemo").val();
						if(rejtCaseAdjMemo == undefined || rejtCaseAdjMemo == null || rejtCaseAdjMemo == ""){
							$("#L120S01aForm").find(".hideMemo").hide();
						}else{
							$("#L120S01aForm").find(".hideMemo").show();
						}
                        $.thickbox.close();
						$.thickbox.close();
						CommonAPI.showMessage(responseData.NOTIFY_MESSAGE);
                    }
                });
            },
            "cancel": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

/**
 *修改 負責人 婉卻控管種類ThickBox
 */
function editReject1(){
	var rejtVal1 = $("[name='rejtCase1']:radio:checked").val();
	var rejtCaseBefore1 = $("[name='rejtCaseBefore1']:radio:checked").val();
	// 婉卻控管Radio設定
	$("[name='_rejtCase1']").each(function(i){
		var $this = $(this);
		if(rejtVal1 == $this.val()){
			$this.attr("checked",true);
		}
	});
	// 婉卻控管變更前Radio設定
	$("[name='_rejtCaseBefore1']").each(function(i){
		var $this = $(this);
		if(rejtCaseBefore1 == $this.val()){
			$this.attr("checked",true);
		}
	});
    var editReject1 = $("#editReject1").thickbox({
        // 使用選取的內容進行彈窗
        title: i18n.lmscommom["other.msg127"],	//other.msg127=修改婉卻控管種類
        width: 500,
        height: 200,
        align: 'center',
        valign: 'bottom',
        modal: true,
        i18n: i18n.def,
        buttons: {
            "sure": function(showMsg){
                $.ajax({
                    type: "POST",
                    handler: __handler,
                    data: {
                        formAction: "editReject1",
                        _rejtCase1: $("[name='_rejtCase1']:radio:checked").val(),
                        _rejtCaseBefore1: rejtVal1,
			            custId: $("#L120S01aForm").find("#custId").html(),
			            dupNo: $("#L120S01aForm").find("#dupNo").html()                        
                    },
                    success: function(responseData){
                        $("#L120S01aForm").setData(responseData.L120S01aForm,false);
						$(".showBefore1").show();
						var rejtCaseAdjMemo1 = $("#L120S01aForm").find("#rejtCaseAdjMemo1").val();
						if(rejtCaseAdjMemo1 == undefined || rejtCaseAdjMemo1 == null || rejtCaseAdjMemo1 == ""){
							$("#L120S01aForm").find(".hideMemo1").hide();
						}else{
							$("#L120S01aForm").find(".hideMemo1").show();
						}
                        $.thickbox.close();
						$.thickbox.close();
						CommonAPI.showMessage(responseData.NOTIFY_MESSAGE);
                    }
                });
            },
            "cancel": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

/**
 * J-105-0179-001 Web e-Loan企金授信建立「往來異常通報戶」紀錄查詢及於簽報書上顯示查詢結果功能
 *查詢 申貸戶 異常通報紀錄 
 */
function getAbnormal(){
    $.ajax({
        handler: __handler,
        type: "POST",
        dataType: "json",
        data: {
            formAction: "getAbnormal",
            mainId: responseJSON.mainId,
            custId: $("#L120S01aForm").find("#custId").html(),
            dupNo: $("#L120S01aForm").find("#dupNo").html()
        },
        success: function(json){
			$("#L120S01aForm").setData(json, false);
        }
    });	
}


/**
 *查詢 負責人 婉卻紀錄 
 */
function getAbnormal1(){
	var val_chairmanId =  $("#L120S01aForm").find("#chairmanId").val();
    var val_chairmanDupNo  = $("#L120S01aForm").find("#chairmanDupNo").val();
	
	$.ajax({
        handler: __handler,
        type: "POST",
        dataType: "json",
        data: {
            formAction: "getAbnormal1",
            mainId: responseJSON.mainId,
            custId: $("#L120S01aForm").find("#custId").val(),
            dupNo: $("#L120S01aForm").find("#dupNo").val(),
            chairmanId: val_chairmanId,
            chairmanDupNo: val_chairmanDupNo,
            chairmanVisible : $("#L120S01aForm").find("#chairmanId").is(':visible')
        },
        success: function(json){
        	if(json.isPassed){        		
        		$("#L120S01aForm").setData(json, false);
        		if(json.L120S01aForm.isAbnormal1==""){
        			$("#L120S01aForm").find("[name=isAbnormal1]").attr("checked", false);        			
        		}
    			
        	}
			
        }
    });	
	    	
}

/**
 * 查詢使用者點擊的資料
 *
 * @param abnormalKind
 * 空白.申貸戶
 * 2.負責人
 */
function openDocAbnormal(abnormalKind){
     
	var mainId =  $("#L120S01aForm").find("#abnormalMainId"+abnormalKind).val();
	
	if(!mainId){
		//return CommonAPI.showMessage(i18n.def["noData"]);
		
		return CommonAPI.showMessage(i18n.lmscommom["other.msg209"]);
	}
	
	if(abnormalKind =="1"){
		//負責人
		openCustId = $("#L120S01aForm").find("#custId").val();
		openDupNo = $("#L120S01aForm").find("#dupNo").val();
	}else{
		//申貸戶
		openCustId = $("#L120S01aForm").find("#abnormalChairmanId").val();
		openDupNo = $("#L120S01aForm").find("#abnormalChairmanDupNo").val();
		if(!openCustId){
			return CommonAPI.showMessage(i18n.def["noData"]);
		}
	}
	 
	$.ajax({
        handler: __handler,
        type: "POST",
        dataType: "json",
        data: {
            formAction: "openDocAbnormal",
            abnormalMainId: mainId,
            openCustId:openCustId,
            openDupNo:openDupNo
        },
        success: function(obj){	
			var typCd = "";
			if(obj.typCd =="5"){
				typCd="5";
			}else{
				typCd="1";
			}
			var pdfName = "LMS120"+typCd+"R27.pdf";
		    var rType = "R27"
		    
		    var content = ""; 
		    content = rType + "^" + obj.oid + "^" + openCustId + "^" + openDupNo + "^" ;
		    
		    $.form.submit({
		        url: "../../../app/simple/FileProcessingService",
		        target: "_blank",
		        data: {
		            mainId: mainId,
		            rptOid: content,
		            fileDownloadName: pdfName,
		            serviceName: "lms120"+typCd+"r01rptservice"
		        }
		    });
			
        }
    });	

}



/**
 *J-111-0535_05097_B1001 Web e-Loan企金授信配合「ESG綜效調查表 」建置，於簽報書增設相對應欄位
 *查詢申貸戶因不符ESG而暫緩承作紀錄
 */
function getEsgReject(){
    $.ajax({
        handler: __handler,
        type: "POST",
        dataType: "json",
        data: {
            formAction: "getEsgReject",
            mainId: responseJSON.mainId,
            custId: $("#L120S01aForm").find("#custId").html(),
            dupNo: $("#L120S01aForm").find("#dupNo").html()
        },
        success: function(json){
			$("#L120S01aForm").setData(json, false);
			if(json.L120S01aForm.isEsgRejt==""){
    			$("#L120S01aForm").find("[name=isEsgRejt]").attr("checked", false);        			
    		}
			
        }
    });	
}



