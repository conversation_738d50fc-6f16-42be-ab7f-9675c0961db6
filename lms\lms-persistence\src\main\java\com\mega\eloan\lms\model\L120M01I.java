/* 
 * L120M01I.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Lob;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 簽報書主檔資料檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L120M01I", uniqueConstraints = @UniqueConstraint(columnNames = { "mainId" }))
public class L120M01I extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 有無善進社會責任
	 * <p/>
	 * 104/06/01 J-104-138<br/>
	 * 簽報書赤道原則
	 */
	@Column(name = "HASDUTY", length = 1, columnDefinition = "CHAR(01)")
	private String hasDuty;

	/**
	 * 有無顯著之具體事實
	 * <p/>
	 * 104/06/01 J-104-138<br/>
	 * 簽報書赤道原則
	 */
	@Column(name = "HASDEEDS", length = 1, columnDefinition = "CHAR(01)")
	private String hasDeeds;

	/** 建立人員號碼 **/
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/** 未成年人授信註記(Y/N) **/
	@Column(name = "CLSMINOR", length = 1, columnDefinition = "VARCHAR(1)")
	private String clsMinor;

	/** 未成年人授信_徵提目的 **/
	@Lob
	@Column(name = "CLSMINORPURPOSE", columnDefinition = "CLOB")
	private String clsMinorPurpose;

	/** 未成年人授信_適法性說明 **/
	@Lob
	@Column(name = "CLSMINORLEGALITY", columnDefinition = "CLOB")
	private String clsMinorLegality;

	/** 單位授權主管職稱 **/
	@Column(name = "UNITAUTHJOBTITLE", length = 8, columnDefinition = "VARCHAR(8)")
	private String unitAuthJobTitle;

	/**
	 * 是否已評估客戶之合法性及誠信經營政策
	 */
	@Column(name = "HASLEGALITY", length = 1, columnDefinition = "CHAR(01)")
	private String hasLegality;

	/**
	 * 有無涉及不誠信
	 */
	@Column(name = "HASBADFAITH", length = 1, columnDefinition = "CHAR(01)")
	private String hasBadFaith;

	/**
	 * 版本
	 */
	@Column(name = "VER", length = 2, columnDefinition = "VARCHAR(2)")
	private String ver;

	/**
	 * 是否未善盡環境保護
	 */
	@Column(name = "HASD1", length = 1, columnDefinition = "VARCHAR(1)")
	private String hasD1;

	/**
	 * 是否未善盡企業社會責任
	 */
	@Column(name = "HASD2", length = 1, columnDefinition = "VARCHAR(1)")
	private String hasD2;

	/**
	 * 是否發生涉及侵害人權行為或事件
	 */
	@Column(name = "HASD3", length = 1, columnDefinition = "VARCHAR(1)")
	private String hasD3;

	/**
	 * 未善盡環境保護_細項1
	 */
	@Column(name = "ITEM1_D1", length = 5, columnDefinition = "VARCHAR(5)")
	private String item1_D1;

	/**
	 * 未善盡環境保護_細項1_統編
	 */
	@Column(name = "ITEMID_D1", length = 10, columnDefinition = "VARCHAR(10)")
	private String itemId_D1;

	/**
	 * 未善盡環境保護_細項1_統編重覆碼
	 */
	@Column(name = "ITEMDUPNO_D1", length = 1, columnDefinition = "VARCHAR(1)")
	private String itemDupNo_D1;

	/**
	 * 未善盡環境保護_細項1_名稱
	 */
	@Column(name = "ITEMNAME_D1", length = 120, columnDefinition = "VARCHAR(120)")
	private String itemName_D1;

	/**
	 * 未善盡環境保護_細項2
	 */
	@Column(name = "ITEM2_D1", length = 20, columnDefinition = "VARCHAR(20)")
	private String item2_D1;

	/**
	 * 未善盡環境保護_細項2_備註
	 */
	@Column(name = "ITEMMEMO_D1", length = 300, columnDefinition = "VARCHAR(300)")
	private String itemMemo_D1;

	/**
	 * 未善盡環境保護_細項3
	 */
	@Column(name = "ITEM3_D1", length = 1, columnDefinition = "VARCHAR(1)")
	private String item3_D1;

	/**
	 * 未善盡企業社會責任_細項1
	 */
	@Column(name = "ITEM1_D2", length = 5, columnDefinition = "VARCHAR(5)")
	private String item1_D2;

	/**
	 * 未善盡企業社會責任_細項1_統編
	 */
	@Column(name = "ITEMID_D2", length = 10, columnDefinition = "VARCHAR(10)")
	private String itemId_D2;

	/**
	 * 未善盡企業社會責任_細項1_統編重覆碼
	 */
	@Column(name = "ITEMDUPNO_D2", length = 1, columnDefinition = "VARCHAR(1)")
	private String itemDupNo_D2;

	/**
	 * 未善盡企業社會責任_細項1_名稱
	 */
	@Column(name = "ITEMNAME_D2", length = 120, columnDefinition = "VARCHAR(120)")
	private String itemName_D2;

	/**
	 * 未善盡企業社會責任_細項2
	 */
	@Column(name = "ITEM2_D2", length = 20, columnDefinition = "VARCHAR(20)")
	private String item2_D2;

	/**
	 * 未善盡企業社會責任_細項2_備註
	 */
	@Column(name = "ITEMMEMO_D2", length = 300, columnDefinition = "VARCHAR(300)")
	private String itemMemo_D2;

	/**
	 * 發生涉及侵害人權行為或事件_細項1
	 */
	@Column(name = "ITEM1_D3", length = 5, columnDefinition = "VARCHAR(5)")
	private String item1_D3;

	/**
	 * 發生涉及侵害人權行為或事件_細項1_統編
	 */
	@Column(name = "ITEMID_D3", length = 10, columnDefinition = "VARCHAR(10)")
	private String itemId_D3;

	/**
	 * 發生涉及侵害人權行為或事件_細項1_統編重覆碼
	 */
	@Column(name = "ITEMDUPNO_D3", length = 1, columnDefinition = "VARCHAR(1)")
	private String itemDupNo_D3;

	/**
	 * 發生涉及侵害人權行為或事件_細項1_名稱
	 */
	@Column(name = "ITEMNAME_D3", length = 120, columnDefinition = "VARCHAR(120)")
	private String itemName_D3;

	/**
	 * 發生涉及侵害人權行為或事件_細項2
	 */
	@Column(name = "ITEM2_D3", length = 20, columnDefinition = "VARCHAR(20)")
	private String item2_D3;

	/**
	 * 發生涉及侵害人權行為或事件_細項2_備註
	 */
	@Column(name = "ITEMMEMO_D3", length = 300, columnDefinition = "VARCHAR(300)")
	private String itemMemo_D3;

	/**
	 * 個人負債比率控管原則減成對象
	 * <p/>
	 * Y/N<br/>
	 * 109/03/02 J-108-0318 簡化授信簽報書
	 */
	@Size(max = 1)
	@Column(name = "RATECONTROLFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String rateControlFlag;

	/** 擔保情形 **/
	@Size(max = 1)
	@Column(name = "COLLSTATUS", length = 1, columnDefinition = "CHAR(1)")
	private String collStatus;

	/** 擔保情形-其他 **/
	@Size(max = 450)
	@Column(name = "COLLSTATUSOTH", length = 450, columnDefinition = "VARCHAR(450)")
	private String collStatusOth;

	/** 擔保情形-十足擔保 **/
	@Size(max = 10)
	@Column(name = "COLLSTATUSFULL", length = 10, columnDefinition = "VARCHAR(10)")
	private String collStatusFull;

	/** 擔保情形-十足擔保鑑估值 **/
	@Digits(integer = 13, fraction = 0)
	@Column(name = "APPAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal appAmt;

	/** 擔保情形-十足擔保放款值 **/
	@Digits(integer = 13, fraction = 0)
	@Column(name = "LOANAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal loanAmt;

	/** 擔保情形-十足擔保登陸用途 **/
	@Size(max = 120)
	@Column(name = "COLLSTATUSFULLDESC", length = 120, columnDefinition = "VARCHAR(120)")
	private String collStatusFullDesc;

	/** 擔保情形-十足擔保實際用途 **/
	@Size(max = 120)
	@Column(name = "COLLSTATUSFULLREAL", length = 120, columnDefinition = "VARCHAR(120)")
	private String collStatusFullReal;

	/**
	 * 擔保情形-十足擔保是否出租
	 * <p/>
	 * Y/N
	 */
	@Size(max = 1)
	@Column(name = "COLLSTATUSFULLRENT", length = 1, columnDefinition = "CHAR(1)")
	private String collStatusFullRent;

	/**
	 * 擔保情形-十足擔保是否轉貸案件
	 * <p/>
	 * Y/N
	 */
	@Size(max = 1)
	@Column(name = "COLLSTATUSFULLTRANLOAN", length = 1, columnDefinition = "CHAR(1)")
	private String collStatusFullTranLoan;

	/**
	 * LTV 貸放成數
	 * <p/>
	 * 自由輸入EX: 60.5%無擔保品，可填N/A 或不適用
	 */
	@Size(max = 9)
	@Column(name = "LTV", length = 9, columnDefinition = "VARCHAR(9)")
	private String ltv;

	/**
	 * 借款人未來展望
	 * <p/>
	 * codeType
	 */
	@Size(max = 1)
	@Column(name = "CUSTOUTLOOK", length = 1, columnDefinition = "CHAR(1)")
	private String custOutlook;

	/**
	 * 保證人未來展望
	 * <p/>
	 * codeType
	 */
	@Size(max = 1)
	@Column(name = "GUARANTOROUTLOOK", length = 1, columnDefinition = "CHAR(1)")
	private String guarantorOutlook;

	/**
	 * 擬爭攬業務
	 * <p/>
	 * 1房貸<br/>
	 * 2房貸壽險<br/>
	 * 3信用卡<br/>
	 * 4財富管理<br/>
	 * 5其他
	 */
	@Size(max = 10)
	@Column(name = "SCRAMBLEBUS", length = 10, columnDefinition = "VARCHAR(10)")
	private String scrambleBus;

	/** 擬爭攬業務-其他 **/
	@Size(max = 120)
	@Column(name = "SCRAMBLEBUSOTH", length = 120, columnDefinition = "VARCHAR(120)")
	private String scrambleBusOth;

	/** 擬爭攬業務-房貸壽險保額 **/
	@Digits(integer = 13, fraction = 0)
	@Column(name = "SCRAMBLEBUS2AMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal scrambleBus2Amt;

	/** 擬爭攬業務-房貸壽險保險期間(起) **/
	@Temporal(TemporalType.DATE)
	@Column(name = "SCRAMBLEBUS2INSBDATE", columnDefinition = "DATE")
	private Date scrambleBus2InsBdate;

	/** 擬爭攬業務-房貸壽險保險期間(迄) **/
	@Temporal(TemporalType.DATE)
	@Column(name = "SCRAMBLEBUS2INSEDATE", columnDefinition = "DATE")
	private Date scrambleBus2InsEdate;

	/** 擬爭攬業務-房貸壽險保費 **/
	@Digits(integer = 13, fraction = 0)
	@Column(name = "SCRAMBLEBUS2INSFEE", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal scrambleBus2InsFee;

	/**
	 * 擬爭攬業務-財富管理
	 * <p/>
	 * A基金<br/>
	 * B保險<br/>
	 * C債券<br/>
	 * D其他
	 */
	@Size(max = 10)
	@Column(name = "SCRAMBLEBUS4", length = 10, columnDefinition = "VARCHAR(10)")
	private String scrambleBus4;

	/** 擬爭攬業務-財富管理其他 **/
	@Size(max = 120)
	@Column(name = "SCRAMBLEBUS4DOTH", length = 120, columnDefinition = "VARCHAR(120)")
	private String scrambleBus4DOth;

	/** 擬爭攬業務-房貸壽險保險期間(起) **/
	@Column(name = "SCRAMBLEBUS2INSBSTR", length = 120, columnDefinition = "VARCHAR(21)")
	private String scrambleBus2InsBstr;

	/** 擬爭攬業務-房貸壽險保險期間(迄) **/
	@Column(name = "SCRAMBLEBUS2INSESTR", length = 21, columnDefinition = "VARCHAR(21)")
	private String scrambleBus2InsEstr;

	/** 紓困方案註記{放寬負面資訊} **/
	@Column(name = "BAILOUT_FLAG1", length = 1, columnDefinition = "VARCHAR(1)")
	private String bailout_flag1;

	/** 紓困方案註記{放寬評等} **/
	@Column(name = "BAILOUT_FLAG2", length = 1, columnDefinition = "VARCHAR(1)")
	private String bailout_flag2;

	/** 合理性分析表 免填列註記 **/
	@Size(max = 1)
	@Column(name = "PASSCHKS08AFG", length = 1, columnDefinition = "VARCHAR(1)")
	private String passChkS08aFg;

	/**
	 * (快速信貸)用途別 select * from com.bcodetype where
	 * codetype='L120M01I_creditLoanPurpose' (ref 'cls1141_purpose')
	 */
	@Column(name = "CREDITLOANPURPOSE", length = 1, columnDefinition = "VARCHAR(1)")
	private String creditLoanPurpose;

	/** 購買本行理財商品無徵提聲明書或有勸誘事宜註記{X:是, V:否, N:未購買, K:未填寫} **/
	@Size(max = 1)
	@Column(name = "INDUCEFLAG", length = 1, columnDefinition = "VARCHAR(1)")
	private String induceFlag;

	/**
	 * 可排除利害關係人授信限制原因(達新台幣1億元以上)
	 * <p/>
	 * 2021/11/25 J-110-0493<br/>
	 * 多筆|串 EX:1|2|3<br/>
	 * 1政府<br/>
	 * 2持股50%<br/>
	 * 3保兌<br/>
	 * 4其他
	 */
	@Size(max = 20)
	@Column(name = "EXCLUDERLTLIMRSN", length = 20, columnDefinition = "VARCHAR(20)")
	private String excludeRltLimRsn;

	/**
	 * 可排除利害關係人授信限制原因_其他描述
	 * <p/>
	 * 2021/11/25 J-110-0493<br/>
	 * 原因欄位為4是才會有值
	 */
	@Size(max = 150)
	@Column(name = "EXCLUDERLTLIMOTHER", length = 150, columnDefinition = "VARCHAR(150)")
	private String excludeRltLimOther;

	/**
	 * 利害關係人授信(達新台幣1億元以上)名單
	 * <p/>
	 * 2021/11/25 J-110-0493<br/>
	 * 放符合條件的借款人ID 姓名，給提示訊息時使用<br/>
	 * 超過三人時，以"xxx...等x人"紀錄
	 */
	@Size(max = 200)
	@Column(name = "RLTLIMLIST", length = 200, columnDefinition = "VARCHAR(200)")
	private String rltLimList;

	/**
	 * J-110-0358 海外授權外改版 簽報書列印版本
	 */
	@Column(name = "PRINTVER", length = 2, columnDefinition = "VARCHAR(2)")
	private String printVer;

	/**
	 * 合併關係企業授信額度疑有超逾營業單位授權
	 * <p/>
	 * J-111-0283<br/>
	 * 關係企業授信額度是否逾分行權限<br/>
	 * 逾權塞Y，其他時候為null
	 */
	@Size(max = 1)
	@Column(name = "OVERAUTHLOAN", length = 1, columnDefinition = "VARCHAR(1)")
	private String overAuthLoan;

	/**
	 * 合併關係企業出口押匯業務額度疑有超逾營業單位授權
	 * <p/>
	 * J-111-0283<br/>
	 * 關係企業出口押匯額度是否逾分行權限<br/>
	 * 逾權塞Y，其他時候為null
	 */
	@Size(max = 1)
	@Column(name = "OVERAUTHEXPERF", length = 1, columnDefinition = "VARCHAR(1)")
	private String overAuthExperf;

	/**
	 * 單獨劃分授信額度疑有超逾營業單位授權
	 * <p/>
	 * J-111-0488<br/>
	 * 單獨劃分授權額度是否逾分行權限<br/>
	 * 逾權塞Y，其他時候為null
	 */
	@Size(max = 1)
	@Column(name = "OVERAUTHALONELOAN", length = 1, columnDefinition = "VARCHAR(1)")
	private String overAuthAloneLoan;

	/**
	 * J-112-0254 消金授信-簡化授信簽報書 擔保品標的
	 */
	@Size(max = 300)
	@Column(name = "COLLTARGET", length = 300, columnDefinition = "VARCHAR(300)")
	private String collTarget;

	/**
	 * J-112-0254 消金授信-簡化授信簽報書 列印擔保品標的
	 */
	@Size(max = 1)
	@Column(name = "PRINTCOLLTARGET", length = 1, columnDefinition = "VARCHAR(1)")
	private String printCollTarget;

	/**
	 * J-112-0360 消金授信-簡化授信簽報書 擔保品標的筆數
	 */
	@Column(name = "COLLTARGETCOUNT", columnDefinition = "DECIMAL(3,0)")
	private BigDecimal collTargetCount;

	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 1.借款人或保證人之年齡加計借款期間後是否未超過75歲
	 * Y-是
	 * N-否
	 */
	@Size(max=1)
	@Column(name="ISRISKQ1", length=1, columnDefinition="VARCHAR(1)")
	private String isRiskQ1;
	
	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 1.借款人或保證人之年齡加計借款期間後是否未超過75歲-說明
	 */
	@Lob
	@Column(name="RISKQ1DESC", columnDefinition="CLOB")
	private String riskQ1Desc;
	
	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 2.借款人近期是否無聯徵中心密集查詢紀錄
	 * Y-是
	 * N-否
	 */
	@Size(max=1)
	@Column(name="ISRISKQ2", length=1, columnDefinition="VARCHAR(1)")
	private String isRiskQ2;
	
	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 2.借款人近期是否無聯徵中心密集查詢紀錄-說明
	 */
	@Lob
	@Column(name="RISKQ2DESC", columnDefinition="CLOB")
	private String riskQ2Desc;
	
	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 3.借款人、保證人經查聯合徵信中心系統組合查詢及票交所，是否皆無授信異常及票據異常記錄。
	 * Y-是
	 * N-否
	 */
	@Size(max=1)
	@Column(name="ISRISKQ3", length=1, columnDefinition="VARCHAR(1)")
	private String isRiskQ3;
	
	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 3.借款人、保證人經查聯合徵信中心系統組合查詢及票交所，是否皆無授信異常及票據異常記錄。-說明
	 */
	@Lob
	@Column(name="RISKQ3DESC", columnDefinition="CLOB")
	private String riskQ3Desc;
	
	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 4.若為歡喜房貸案件，借款人、保證人之任一人是否皆符合無「專項貸款再加碼機制」樣態。
	 * Y-是
	 * N-否
	 * O-不適用
	 */
	@Size(max=1)
	@Column(name="ISRISKQ4", length=1, columnDefinition="VARCHAR(1)")
	private String isRiskQ4;
	
	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 4.若為歡喜房貸案件，借款人、保證人之任一人是否皆符合無「專項貸款再加碼機制」樣態。-說明
	 */
	@Lob
	@Column(name="RISKQ4DESC", columnDefinition="CLOB")
	private String riskQ4Desc;
	
	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 5.借款人收入是否明確且具償債能力
	 * Y-是
	 * N-否
	 */
	@Size(max=1)
	@Column(name="ISRISKQ5", length=1, columnDefinition="VARCHAR(1)")
	private String isRiskQ5;
	
	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 5.借款人收入是否明確且具償債能力-說明
	 */
	@Lob
	@Column(name="RISKQ5DESC", columnDefinition="CLOB")
	private String riskQ5Desc;
	
	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 6.借款人負債比是否小於60%或負債比已逾60%案件已依規減成敘做?
	 * Y-是
	 * N-否
	 * O-不適用
	 */
	@Size(max=1)
	@Column(name="ISRISKQ6", length=1, columnDefinition="VARCHAR(1)")
	private String isRiskQ6;
	
	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 6.借款人負債比是否小於60%或負債比已逾60%案件已依規減成敘做?-說明
	 */
	@Lob
	@Column(name="RISKQ6DESC", columnDefinition="CLOB")
	private String riskQ6Desc;
	
	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 7.本案是否非跨區案件?
	 * Y-是
	 * N-否
	 */
	@Size(max=1)
	@Column(name="ISRISKQ7", length=1, columnDefinition="VARCHAR(1)")
	private String isRiskQ7;
	
	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 7.本案是否非跨區案件?-說明
	 */
	@Lob
	@Column(name="RISKQ7DESC", columnDefinition="CLOB")
	private String riskQ7Desc;
	
	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 8.若為購置不動產案件，本案是否已落實買賣契約書審查並符合本行相關規範。
	 * Y-是
	 * N-否
	 * O-不適用
	 */
	@Size(max=1)
	@Column(name="ISRISKQ8", length=1, columnDefinition="VARCHAR(1)")
	private String isRiskQ8;
	
	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 8.若為購置不動產案件，本案是否已落實買賣契約書審查並符合本行相關規範。-說明
	 */
	@Lob
	@Column(name="RISKQ8DESC", columnDefinition="CLOB")
	private String riskQ8Desc;
	
	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 9.若為青年安心成家購屋優惠，借款人與其配偶及未成年子女是否均無自有住宅。
	 * Y-是
	 * N-否
	 * O-不適用
	 */
	@Size(max=1)
	@Column(name="ISRISKQ9", length=1, columnDefinition="VARCHAR(1)")
	private String isRiskQ9;
	
	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 9.若為青年安心成家購屋優惠，借款人與其配偶及未成年子女是否均無自有住宅。-說明
	 */
	@Lob
	@Column(name="RISKQ9DESC", columnDefinition="CLOB")
	private String riskQ9Desc;
	
	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 10.借款人、保證人、共同借款人、擔保物提供人及其任職公司是否皆無負面新聞資訊。
	 * Y-是
	 * N-否
	 */
	@Size(max=1)
	@Column(name="ISRISKQ10", length=1, columnDefinition="VARCHAR(1)")
	private String isRiskQ10;

	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 10.借款人、保證人、共同借款人、擔保物提供人及其任職公司是否皆無負面新聞資訊。-說明
	 */
	@Lob
	@Column(name="RISKQ10DESC", columnDefinition="CLOB")
	private String riskQ10Desc;
	
	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 11.其他補充說明
	 */
	@Lob
	@Column(name="RISKQ11OTHERDESC", columnDefinition="CLOB")
	private String riskQ11OtherDesc;
	
	/**
	 * J-112-0508_05097_B1001 授審會及常董會提案稿合併列印UNID
	 */
	@Size(max = 32)
	@Column(name = "COMBINEPRINT_UNID", length = 32, columnDefinition = "VARCHAR(32)")
	private String combinePrint_unid;

	/** 授審會及常董會提案稿合併列印送件時間 **/
	@Column(name = "COMBINEPRINT_TIME", columnDefinition = "TIMESTAMP")
	private Timestamp combinePrint_time;

	/**
	 * J-112-0508_05097_B1001 授審會及常董會提案稿合併列印執行狀態
	 */
	@Column(name = "COMBINEPRINT_STATUS", length = 3, columnDefinition = "CHAR(3)")
	private String combinePrint_status;

	/**
	 * J-112-0508_05097_B1001 授審會及常董會提案稿合併列印錯誤訊息
	 */
	@Column(name = "COMBINEPRINT_ERRMSG", length = 300, columnDefinition = "VARCHAR(300)")
	private String combinePrint_errMsg;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得有無善進社會責任
	 * <p/>
	 * 104/06/01 J-104-138<br/>
	 * 簽報書赤道原則
	 */
	public String getHasDuty() {
		return this.hasDuty;
	}

	/**
	 * 設定有無善進社會責任
	 * <p/>
	 * 104/06/01 J-104-138<br/>
	 * 簽報書赤道原則
	 **/
	public void setHasDuty(String value) {
		this.hasDuty = value;
	}

	/**
	 * 取得有無顯著之具體事實
	 * <p/>
	 * 104/06/01 J-104-138<br/>
	 * 簽報書赤道原則
	 */
	public String getHasDeeds() {
		return this.hasDeeds;
	}

	/**
	 * 設定有無顯著之具體事實
	 * <p/>
	 * 104/06/01 J-104-138<br/>
	 * 簽報書赤道原則
	 **/
	public void setHasDeeds(String value) {
		this.hasDeeds = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 取得未成年人授信註記(Y/N) **/
	public String getClsMinor() {
		return clsMinor;
	}

	/** 設定未成年人授信註記(Y/N) **/
	public void setClsMinor(String clsMinor) {
		this.clsMinor = clsMinor;
	}

	/** 取得未成年人授信_徵提目的 **/
	public String getClsMinorPurpose() {
		return clsMinorPurpose;
	}

	/** 設定未成年人授信_徵提目的 **/
	public void setClsMinorPurpose(String clsMinorPurpose) {
		this.clsMinorPurpose = clsMinorPurpose;
	}

	/** 取得未成年人授信_適法性說明 **/
	public String getClsMinorLegality() {
		return clsMinorLegality;
	}

	/** 設定未成年人授信_適法性說明 **/
	public void setClsMinorLegality(String clsMinorLegality) {
		this.clsMinorLegality = clsMinorLegality;
	}

	/** 設定單位授權主管職稱 **/
	public void setUnitAuthJobTitle(String unitAuthJobTitle) {
		this.unitAuthJobTitle = unitAuthJobTitle;
	}

	/** 取得單位授權主管職稱 **/
	public String getUnitAuthJobTitle() {
		return unitAuthJobTitle;
	}

	/** 設定是否已評估客戶之合法性及誠信經營政策 **/
	public void setHasLegality(String hasLegality) {
		this.hasLegality = hasLegality;
	}

	/** 取得是否已評估客戶之合法性及誠信經營政策 **/
	public String getHasLegality() {
		return hasLegality;
	}

	/** 設定有無涉及不誠信 **/
	public void setHasBadFaith(String hasBadFaith) {
		this.hasBadFaith = hasBadFaith;
	}

	/** 取得有無涉及不誠信 **/
	public String getHasBadFaith() {
		return hasBadFaith;
	}

	/** 設定版本 **/
	public void setVer(String value) {
		this.ver = value;
	}

	/** 取得版本 **/
	public String getVer() {
		return ver;
	}

	/** 設定是否未善盡環境保護 **/
	public void setHasD1(String value) {
		this.hasD1 = value;
	}

	/** 取得是否未善盡環境保護 **/
	public String getHasD1() {
		return hasD1;
	}

	/** 設定是否未善盡企業社會責任 **/
	public void setHasD2(String value) {
		this.hasD2 = value;
	}

	/** 取得是否未善盡企業社會責任 **/
	public String getHasD2() {
		return hasD2;
	}

	/** 設定是否發生涉及侵害人權行為或事件 **/
	public void setHasD3(String value) {
		this.hasD3 = value;
	}

	/** 取得是否發生涉及侵害人權行為或事件 **/
	public String getHasD3() {
		return hasD3;
	}

	/** 設定未善盡環境保護_細項1 **/
	public void setItem1_D1(String value) {
		this.item1_D1 = value;
	}

	/** 取得未善盡環境保護_細項 1 **/
	public String getItem1_D1() {
		return item1_D1;
	}

	/** 設定未善盡環境保護_細項1_統編 **/
	public void setItemId_D1(String value) {
		this.itemId_D1 = value;
	}

	/** 取得未善盡環境保護_細項 1_統編 **/
	public String getItemId_D1() {
		return itemId_D1;
	}

	/** 設定未善盡環境保護_細項1_統編重覆碼 **/
	public void setItemDupNo_D1(String value) {
		this.itemDupNo_D1 = value;
	}

	/** 取得未善盡環境保護_細項 1_統編重覆碼 **/
	public String getItemDupNo_D1() {
		return itemDupNo_D1;
	}

	/** 設定未善盡環境保護_細項1_名稱 **/
	public void setItemName_D1(String value) {
		this.itemName_D1 = value;
	}

	/** 取得未善盡環境保護_細項 1_名稱 **/
	public String getItemName_D1() {
		return itemName_D1;
	}

	/** 設定未善盡環境保護_細項2 **/
	public void setItem2_D1(String value) {
		this.item2_D1 = value;
	}

	/** 取得未善盡環境保護_細項 2 **/
	public String getItem2_D1() {
		return item2_D1;
	}

	/** 設定未善盡環境保護_細項2_備註 **/
	public void setItemMemo_D1(String value) {
		this.itemMemo_D1 = value;
	}

	/** 取得未善盡環境保護_細項2_備註 **/
	public String getItemMemo_D1() {
		return itemMemo_D1;
	}

	/** 設定未善盡環境保護_細項3 **/
	public void setItem3_D1(String value) {
		this.item3_D1 = value;
	}

	/** 取得未善盡環境保護_細項3 **/
	public String getItem3_D1() {
		return item3_D1;
	}

	/** 設定未善盡企業社會責任_細項1 **/
	public void setItem1_D2(String value) {
		this.item1_D2 = value;
	}

	/** 取得未善盡企業社會責任_細項 1 **/
	public String getItem1_D2() {
		return item1_D2;
	}

	/** 設定未善盡企業社會責任_細項1_統編 **/
	public void setItemId_D2(String value) {
		this.itemId_D2 = value;
	}

	/** 取得未善盡企業社會責任_細項 1_統編 **/
	public String getItemId_D2() {
		return itemId_D2;
	}

	/** 設定未善盡企業社會責任_細項1_統編重覆碼 **/
	public void setItemDupNo_D2(String value) {
		this.itemDupNo_D2 = value;
	}

	/** 取得未善盡企業社會責任_細項 1_統編重覆碼 **/
	public String getItemDupNo_D2() {
		return itemDupNo_D2;
	}

	/** 設定未善盡企業社會責任_細項1_名稱 **/
	public void setItemName_D2(String value) {
		this.itemName_D2 = value;
	}

	/** 取得未善盡企業社會責任_細項 1_名稱 **/
	public String getItemName_D2() {
		return itemName_D2;
	}

	/** 設定未善盡企業社會責任_細項2 **/
	public void setItem2_D2(String value) {
		this.item2_D2 = value;
	}

	/** 取得未善盡企業社會責任_細項 2 **/
	public String getItem2_D2() {
		return item2_D2;
	}

	/** 設定未善盡企業社會責任_細項2_備註 **/
	public void setItemMemo_D2(String value) {
		this.itemMemo_D2 = value;
	}

	/** 取得未善盡企業社會責任_細項2_備註 **/
	public String getItemMemo_D2() {
		return itemMemo_D2;
	}

	/** 設定發生涉及侵害人權行為或事件_細項1 **/
	public void setItem1_D3(String value) {
		this.item1_D3 = value;
	}

	/** 取得發生涉及侵害人權行為或事件_細項 1 **/
	public String getItem1_D3() {
		return item1_D3;
	}

	/** 設定發生涉及侵害人權行為或事件_細項1_統編 **/
	public void setItemId_D3(String value) {
		this.itemId_D3 = value;
	}

	/** 取得發生涉及侵害人權行為或事件_細項 1_統編 **/
	public String getItemId_D3() {
		return itemId_D3;
	}

	/** 設定發生涉及侵害人權行為或事件_細項1_統編重覆碼 **/
	public void setItemDupNo_D3(String value) {
		this.itemDupNo_D3 = value;
	}

	/** 取得發生涉及侵害人權行為或事件_細項 1_統編重覆碼 **/
	public String getItemDupNo_D3() {
		return itemDupNo_D3;
	}

	/** 設定發生涉及侵害人權行為或事件_細項1_名稱 **/
	public void setItemName_D3(String value) {
		this.itemName_D3 = value;
	}

	/** 取得發生涉及侵害人權行為或事件_細項 1_名稱 **/
	public String getItemName_D3() {
		return itemName_D3;
	}

	/** 設定發生涉及侵害人權行為或事件_細項2 **/
	public void setItem2_D3(String value) {
		this.item2_D3 = value;
	}

	/** 取得發生涉及侵害人權行為或事件_細項 2 **/
	public String getItem2_D3() {
		return item2_D3;
	}

	/** 設定發生涉及侵害人權行為或事件_細項2_備註 **/
	public void setItemMemo_D3(String value) {
		this.itemMemo_D3 = value;
	}

	/** 取得發生涉及侵害人權行為或事件_細項2_備註 **/
	public String getItemMemo_D3() {
		return itemMemo_D3;
	}

	/**
	 * 取得個人負債比率控管原則減成對象
	 * <p/>
	 * Y/N<br/>
	 * 109/03/02 J-108-0318 簡化授信簽報書
	 */
	public String getRateControlFlag() {
		return this.rateControlFlag;
	}

	/**
	 * 設定個人負債比率控管原則減成對象
	 * <p/>
	 * Y/N<br/>
	 * 109/03/02 J-108-0318 簡化授信簽報書
	 **/
	public void setRateControlFlag(String value) {
		this.rateControlFlag = value;
	}

	/** 取得擔保情形 **/
	public String getCollStatus() {
		return this.collStatus;
	}

	/** 設定擔保情形 **/
	public void setCollStatus(String value) {
		this.collStatus = value;
	}

	/** 取得擔保情形-其他 **/
	public String getCollStatusOth() {
		return this.collStatusOth;
	}

	/** 設定擔保情形-其他 **/
	public void setCollStatusOth(String value) {
		this.collStatusOth = value;
	}

	/** 取得擔保情形-十足擔保 **/
	public String getCollStatusFull() {
		return this.collStatusFull;
	}

	/** 設定擔保情形-十足擔保 **/
	public void setCollStatusFull(String value) {
		this.collStatusFull = value;
	}

	/** 取得擔保情形-十足擔保鑑估值 **/
	public BigDecimal getAppAmt() {
		return this.appAmt;
	}

	/** 設定擔保情形-十足擔保鑑估值 **/
	public void setAppAmt(BigDecimal value) {
		this.appAmt = value;
	}

	/** 取得擔保情形-十足擔保放款值 **/
	public BigDecimal getLoanAmt() {
		return this.loanAmt;
	}

	/** 設定擔保情形-十足擔保放款值 **/
	public void setLoanAmt(BigDecimal value) {
		this.loanAmt = value;
	}

	/** 取得擔保情形-十足擔保登陸用途 **/
	public String getCollStatusFullDesc() {
		return this.collStatusFullDesc;
	}

	/** 設定擔保情形-十足擔保登陸用途 **/
	public void setCollStatusFullDesc(String value) {
		this.collStatusFullDesc = value;
	}

	/** 取得擔保情形-十足擔保實際用途 **/
	public String getCollStatusFullReal() {
		return this.collStatusFullReal;
	}

	/** 設定擔保情形-十足擔保實際用途 **/
	public void setCollStatusFullReal(String value) {
		this.collStatusFullReal = value;
	}

	/**
	 * 取得擔保情形-十足擔保是否出租
	 * <p/>
	 * Y/N
	 */
	public String getCollStatusFullRent() {
		return this.collStatusFullRent;
	}

	/**
	 * 設定擔保情形-十足擔保是否出租
	 * <p/>
	 * Y/N
	 **/
	public void setCollStatusFullRent(String value) {
		this.collStatusFullRent = value;
	}

	/**
	 * 取得擔保情形-十足擔保是否轉貸案件
	 * <p/>
	 * Y/N
	 */
	public String getCollStatusFullTranLoan() {
		return this.collStatusFullTranLoan;
	}

	/**
	 * 設定擔保情形-十足擔保是否轉貸案件
	 * <p/>
	 * Y/N
	 **/
	public void setCollStatusFullTranLoan(String value) {
		this.collStatusFullTranLoan = value;
	}

	/**
	 * 取得LTV 貸放成數
	 * <p/>
	 * 自由輸入EX: 60.5%無擔保品，可填N/A 或不適用
	 */
	public String getLtv() {
		return this.ltv;
	}

	/**
	 * 設定LTV 貸放成數
	 * <p/>
	 * 自由輸入EX: 60.5%無擔保品，可填N/A 或不適用
	 **/
	public void setLtv(String value) {
		this.ltv = value;
	}

	/**
	 * 取得借款人未來展望
	 * <p/>
	 * codeType
	 */
	public String getCustOutlook() {
		return this.custOutlook;
	}

	/**
	 * 設定借款人未來展望
	 * <p/>
	 * codeType
	 **/
	public void setCustOutlook(String value) {
		this.custOutlook = value;
	}

	/**
	 * 取得保證人未來展望
	 * <p/>
	 * codeType
	 */
	public String getGuarantorOutlook() {
		return this.guarantorOutlook;
	}

	/**
	 * 設定保證人未來展望
	 * <p/>
	 * codeType
	 **/
	public void setGuarantorOutlook(String value) {
		this.guarantorOutlook = value;
	}

	/**
	 * 取得擬爭攬業務
	 * <p/>
	 * 1房貸<br/>
	 * 2房貸壽險<br/>
	 * 3信用卡<br/>
	 * 4財富管理<br/>
	 * 5其他
	 */
	public String getScrambleBus() {
		return this.scrambleBus;
	}

	/**
	 * 設定擬爭攬業務
	 * <p/>
	 * 1房貸<br/>
	 * 2房貸壽險<br/>
	 * 3信用卡<br/>
	 * 4財富管理<br/>
	 * 5其他
	 **/
	public void setScrambleBus(String value) {
		this.scrambleBus = value;
	}

	/** 取得擬爭攬業務-其他 **/
	public String getScrambleBusOth() {
		return this.scrambleBusOth;
	}

	/** 設定擬爭攬業務-其他 **/
	public void setScrambleBusOth(String value) {
		this.scrambleBusOth = value;
	}

	/** 取得擬爭攬業務-房貸壽險保額 **/
	public BigDecimal getScrambleBus2Amt() {
		return this.scrambleBus2Amt;
	}

	/** 設定擬爭攬業務-房貸壽險保額 **/
	public void setScrambleBus2Amt(BigDecimal value) {
		this.scrambleBus2Amt = value;
	}

	/** 取得擬爭攬業務-房貸壽險保險期間(起) **/
	public Date getScrambleBus2InsBdate() {
		return this.scrambleBus2InsBdate;
	}

	/** 設定擬爭攬業務-房貸壽險保險期間(起) **/
	public void setScrambleBus2InsBdate(Date value) {
		this.scrambleBus2InsBdate = value;
	}

	/** 取得擬爭攬業務-房貸壽險保險期間(迄) **/
	public Date getScrambleBus2InsEdate() {
		return this.scrambleBus2InsEdate;
	}

	/** 設定擬爭攬業務-房貸壽險保險期間(迄) **/
	public void setScrambleBus2InsEdate(Date value) {
		this.scrambleBus2InsEdate = value;
	}

	/** 取得擬爭攬業務-房貸壽險保費 **/
	public BigDecimal getScrambleBus2InsFee() {
		return this.scrambleBus2InsFee;
	}

	/** 設定擬爭攬業務-房貸壽險保費 **/
	public void setScrambleBus2InsFee(BigDecimal value) {
		this.scrambleBus2InsFee = value;
	}

	/**
	 * 取得擬爭攬業務-財富管理
	 * <p/>
	 * A基金<br/>
	 * B保險<br/>
	 * C債券<br/>
	 * D其他
	 */
	public String getScrambleBus4() {
		return this.scrambleBus4;
	}

	/**
	 * 設定擬爭攬業務-財富管理
	 * <p/>
	 * A基金<br/>
	 * B保險<br/>
	 * C債券<br/>
	 * D其他
	 **/
	public void setScrambleBus4(String value) {
		this.scrambleBus4 = value;
	}

	/** 取得擬爭攬業務-財富管理其他 **/
	public String getScrambleBus4DOth() {
		return this.scrambleBus4DOth;
	}

	/** 設定擬爭攬業務-財富管理其他 **/
	public void setScrambleBus4DOth(String value) {
		this.scrambleBus4DOth = value;
	}

	public String getScrambleBus2InsBstr() {
		return scrambleBus2InsBstr;
	}

	public void setScrambleBus2InsBstr(String scrambleBus2InsBstr) {
		this.scrambleBus2InsBstr = scrambleBus2InsBstr;
	}

	public String getScrambleBus2InsEstr() {
		return scrambleBus2InsEstr;
	}

	public void setScrambleBus2InsEstr(String scrambleBus2InsEstr) {
		this.scrambleBus2InsEstr = scrambleBus2InsEstr;
	}

	public String getBailout_flag1() {
		return bailout_flag1;
	}

	public void setBailout_flag1(String bailout_flag1) {
		this.bailout_flag1 = bailout_flag1;
	}

	public String getBailout_flag2() {
		return bailout_flag2;
	}

	public void setBailout_flag2(String bailout_flag2) {
		this.bailout_flag2 = bailout_flag2;
	}

	/** 設定合理性分析表_免填列註記 **/
	public void setPassChkS08aFg(String value) {
		this.passChkS08aFg = value;
	}

	/** 取得合理性分析表_免填列註記 **/
	public String getPassChkS08aFg() {
		return passChkS08aFg;
	}

	public String getCreditLoanPurpose() {
		return creditLoanPurpose;
	}

	public void setCreditLoanPurpose(String creditLoanPurpose) {
		this.creditLoanPurpose = creditLoanPurpose;
	}

	public String getInduceFlag() {
		return induceFlag;
	}

	public void setInduceFlag(String induceFlag) {
		this.induceFlag = induceFlag;
	}

	/** 設定簽報書列印版本 **/
	public void setPrintVer(String value) {
		this.printVer = value;
	}

	/** 取得簽報書列印版本 **/
	public String getPrintVer() {
		return printVer;
	}

	/**
	 * 取得可排除利害關係人授信限制原因(達新台幣1億元以上)
	 * <p/>
	 * 2021/11/25 J-110-0493<br/>
	 * 多筆|串 EX:1|2|3<br/>
	 * 1政府<br/>
	 * 2持股50%<br/>
	 * 3保兌<br/>
	 * 4其他
	 */
	public String getExcludeRltLimRsn() {
		return this.excludeRltLimRsn;
	}

	/**
	 * 設定可排除利害關係人授信限制原因(達新台幣1億元以上)
	 * <p/>
	 * 2021/11/25 J-110-0493<br/>
	 * 多筆|串 EX:1|2|3<br/>
	 * 1政府<br/>
	 * 2持股50%<br/>
	 * 3保兌<br/>
	 * 4其他
	 **/
	public void setExcludeRltLimRsn(String value) {
		this.excludeRltLimRsn = value;
	}

	/**
	 * 取得可排除利害關係人授信限制原因_其他描述
	 * <p/>
	 * 2021/11/25 J-110-0493<br/>
	 * 原因欄位為4是才會有值
	 */
	public String getExcludeRltLimOther() {
		return this.excludeRltLimOther;
	}

	/**
	 * 設定可排除利害關係人授信限制原因_其他描述
	 * <p/>
	 * 2021/11/25 J-110-0493<br/>
	 * 原因欄位為4是才會有值
	 **/
	public void setExcludeRltLimOther(String value) {
		this.excludeRltLimOther = value;
	}

	/**
	 * 取得利害關係人授信(達新台幣1億元以上)名單
	 * <p/>
	 * 2021/11/25 J-110-0493<br/>
	 * 放符合條件的借款人ID 姓名，給提示訊息時使用<br/>
	 * 超過三人時，以"xxx...等x人"紀錄
	 */
	public String getRltLimList() {
		return this.rltLimList;
	}

	/**
	 * 設定利害關係人授信(達新台幣1億元以上)名單
	 * <p/>
	 * 2021/11/25 J-110-0493<br/>
	 * 放符合條件的借款人ID 姓名，給提示訊息時使用<br/>
	 * 超過三人時，以"xxx...等x人"紀錄
	 **/
	public void setRltLimList(String value) {
		this.rltLimList = value;
	}

	/**
	 * 取得合併關係企業授信額度疑有超逾營業單位授權
	 * <p/>
	 * J-111-0283<br/>
	 * 關係企業授信額度是否逾分行權限<br/>
	 * 逾權塞Y，其他時候為null
	 */
	public String getOverAuthLoan() {
		return this.overAuthLoan;
	}

	/**
	 * 設定合併關係企業授信額度疑有超逾營業單位授權
	 * <p/>
	 * J-111-0283<br/>
	 * 關係企業授信額度是否逾分行權限<br/>
	 * 逾權塞Y，其他時候為null
	 **/
	public void setOverAuthLoan(String value) {
		this.overAuthLoan = value;
	}

	/**
	 * 取得合併關係企業出口押匯業務額度疑有超逾營業單位授權
	 * <p/>
	 * J-111-0283<br/>
	 * 關係企業出口押匯額度是否逾分行權限<br/>
	 * 逾權塞Y，其他時候為null
	 */
	public String getOverAuthExperf() {
		return this.overAuthExperf;
	}

	/**
	 * 設定合併關係企業出口押匯業務額度疑有超逾營業單位授權
	 * <p/>
	 * J-111-0283<br/>
	 * 關係企業出口押匯額度是否逾分行權限<br/>
	 * 逾權塞Y，其他時候為null
	 **/
	public void setOverAuthExperf(String value) {
		this.overAuthExperf = value;
	}

	/**
	 * 取得單獨劃分授信額度疑有超逾營業單位授權
	 * <p/>
	 * J-111-0488<br/>
	 * 單獨劃分授權額度是否逾分行權限<br/>
	 * 逾權塞Y，其他時候為null
	 */
	public String getOverAuthAloneLoan() {
		return this.overAuthAloneLoan;
	}

	/**
	 * 設定單獨劃分授信額度疑有超逾營業單位授權
	 * <p/>
	 * J-111-0488<br/>
	 * 單獨劃分授權額度是否逾分行權限<br/>
	 * 逾權塞Y，其他時候為null
	 **/
	public void setOverAuthAloneLoan(String value) {
		this.overAuthAloneLoan = value;
	}

	/**
	 * J-112-0254 消金授信-簡化授信簽報書 取得擔保品標的
	 */
	public String getCollTarget() {
		return collTarget;
	}

	/**
	 * J-112-0254 消金授信-簡化授信簽報書 設定擔保品標的
	 */
	public void setCollTarget(String collTarget) {
		this.collTarget = collTarget;
	}

	/**
	 * J-112-0254 消金授信-簡化授信簽報書 取得列印擔保品標的
	 */
	public String getPrintCollTarget() {
		return printCollTarget;
	}

	/**
	 * J-112-0254 消金授信-簡化授信簽報書 設定列印擔保品標的
	 */
	public void setPrintCollTarget(String printCollTarget) {
		this.printCollTarget = printCollTarget;
	}

	/**
	 * J-112-0360 消金授信-簡化授信簽報書 取得擔保品標的筆數
	 */
	public BigDecimal getCollTargetCount() {
		return collTargetCount;
	}

	/**
	 * J-112-0360 消金授信-簡化授信簽報書 設定擔保品標的筆數
	 */
	public void setCollTargetCount(BigDecimal collTargetCount) {
		this.collTargetCount = collTargetCount;
	}

	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 取得借款人或保證人之年齡加計借款期間後是否未超過75歲
	 */
	public String getIsRiskQ1() {
		return isRiskQ1;
	}

	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 設定1.借款人或保證人之年齡加計借款期間後是否未超過75歲
	 */
	public void setIsRiskQ1(String isRiskQ1) {
		this.isRiskQ1 = isRiskQ1;
	}

	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 取得1.借款人或保證人之年齡加計借款期間後是否未超過75歲-說明
	 */
	public String getRiskQ1Desc() {
		return riskQ1Desc;
	}

	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 設定借款人或保證人之年齡加計借款期間後是否未超過75歲-說明
	 */
	public void setRiskQ1Desc(String riskQ1Desc) {
		this.riskQ1Desc = riskQ1Desc;
	}

	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 取得2.借款人近期是否無聯徵中心密集查詢紀錄
	 */
	public String getIsRiskQ2() {
		return isRiskQ2;
	}

	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 設定2.借款人近期是否無聯徵中心密集查詢紀錄
	 */
	public void setIsRiskQ2(String isRiskQ2) {
		this.isRiskQ2 = isRiskQ2;
	}

	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 取得2.借款人近期是否無聯徵中心密集查詢紀錄-說明
	 */
	public String getRiskQ2Desc() {
		return riskQ2Desc;
	}

	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 設定2.借款人近期是否無聯徵中心密集查詢紀錄-說明
	 */
	public void setRiskQ2Desc(String riskQ2Desc) {
		this.riskQ2Desc = riskQ2Desc;
	}

	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 取得3.借款人、保證人經查聯合徵信中心系統組合查詢及票交所，是否皆無授信異常及票據異常記錄。
	 */
	public String getIsRiskQ3() {
		return isRiskQ3;
	}

	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 設定3.借款人、保證人經查聯合徵信中心系統組合查詢及票交所，是否皆無授信異常及票據異常記錄。
	 */
	public void setIsRiskQ3(String isRiskQ3) {
		this.isRiskQ3 = isRiskQ3;
	}

	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 取得3.借款人、保證人經查聯合徵信中心系統組合查詢及票交所，是否皆無授信異常及票據異常記錄。-說明
	 */
	public String getRiskQ3Desc() {
		return riskQ3Desc;
	}

	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 設定3.借款人、保證人經查聯合徵信中心系統組合查詢及票交所，是否皆無授信異常及票據異常記錄。-說明
	 */
	public void setRiskQ3Desc(String riskQ3Desc) {
		this.riskQ3Desc = riskQ3Desc;
	}

	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 取得4.若為歡喜房貸案件，借款人、保證人之任一人是否皆符合無「專項貸款再加碼機制」樣態。
	 */
	public String getIsRiskQ4() {
		return isRiskQ4;
	}

	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 設定4.若為歡喜房貸案件，借款人、保證人之任一人是否皆符合無「專項貸款再加碼機制」樣態。
	 */
	public void setIsRiskQ4(String isRiskQ4) {
		this.isRiskQ4 = isRiskQ4;
	}

	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 取得4.若為歡喜房貸案件，借款人、保證人之任一人是否皆符合無「專項貸款再加碼機制」樣態。-說明
	 */
	public String getRiskQ4Desc() {
		return riskQ4Desc;
	}

	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 設定4.若為歡喜房貸案件，借款人、保證人之任一人是否皆符合無「專項貸款再加碼機制」樣態。-說明
	 */
	public void setRiskQ4Desc(String riskQ4Desc) {
		this.riskQ4Desc = riskQ4Desc;
	}

	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 取得5.借款人收入是否明確且具償債能力
	 */
	public String getIsRiskQ5() {
		return isRiskQ5;
	}

	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 設定5.借款人收入是否明確且具償債能力
	 */
	public void setIsRiskQ5(String isRiskQ5) {
		this.isRiskQ5 = isRiskQ5;
	}

	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 取得5.借款人收入是否明確且具償債能力-說明
	 */
	public String getRiskQ5Desc() {
		return riskQ5Desc;
	}

	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 設定5.借款人收入是否明確且具償債能力-說明
	 */
	public void setRiskQ5Desc(String riskQ5Desc) {
		this.riskQ5Desc = riskQ5Desc;
	}

	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 取得6.借款人負債比是否小於60%或負債比已逾60%案件已依規減成敘做?
	 */
	public String getIsRiskQ6() {
		return isRiskQ6;
	}

	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 設定6.借款人負債比是否小於60%或負債比已逾60%案件已依規減成敘做?
	 */
	public void setIsRiskQ6(String isRiskQ6) {
		this.isRiskQ6 = isRiskQ6;
	}

	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 取得6.借款人負債比是否小於60%或負債比已逾60%案件已依規減成敘做?-說明
	 */
	public String getRiskQ6Desc() {
		return riskQ6Desc;
	}

	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 設定6.借款人負債比是否小於60%或負債比已逾60%案件已依規減成敘做?-說明
	 */
	public void setRiskQ6Desc(String riskQ6Desc) {
		this.riskQ6Desc = riskQ6Desc;
	}

	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 取得7.本案是否非跨區案件?
	 */
	public String getIsRiskQ7() {
		return isRiskQ7;
	}

	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 設定7.本案是否非跨區案件?
	 */
	public void setIsRiskQ7(String isRiskQ7) {
		this.isRiskQ7 = isRiskQ7;
	}

	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 取得7.本案是否非跨區案件?-說明
	 */
	public String getRiskQ7Desc() {
		return riskQ7Desc;
	}

	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 設定7.本案是否非跨區案件?-說明
	 */
	public void setRiskQ7Desc(String riskQ7Desc) {
		this.riskQ7Desc = riskQ7Desc;
	}

	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 取得8.若為購置不動產案件，本案是否已落實買賣契約書審查並符合本行相關規範。
	 */
	public String getIsRiskQ8() {
		return isRiskQ8;
	}

	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 設定8.若為購置不動產案件，本案是否已落實買賣契約書審查並符合本行相關規範。
	 */
	public void setIsRiskQ8(String isRiskQ8) {
		this.isRiskQ8 = isRiskQ8;
	}

	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 取得8.若為購置不動產案件，本案是否已落實買賣契約書審查並符合本行相關規範。-說明
	 */
	public String getRiskQ8Desc() {
		return riskQ8Desc;
	}

	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 設定8.若為購置不動產案件，本案是否已落實買賣契約書審查並符合本行相關規範。-說明
	 */
	public void setRiskQ8Desc(String riskQ8Desc) {
		this.riskQ8Desc = riskQ8Desc;
	}

	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 取得9.若為青年安心成家購屋優惠，借款人與其配偶及未成年子女是否均無自有住宅。
	 */
	public String getIsRiskQ9() {
		return isRiskQ9;
	}

	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 設定9.若為青年安心成家購屋優惠，借款人與其配偶及未成年子女是否均無自有住宅。
	 */
	public void setIsRiskQ9(String isRiskQ9) {
		this.isRiskQ9 = isRiskQ9;
	}

	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 取得9.若為青年安心成家購屋優惠，借款人與其配偶及未成年子女是否均無自有住宅。-說明
	 */
	public String getRiskQ9Desc() {
		return riskQ9Desc;
	}

	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 設定9.若為青年安心成家購屋優惠，借款人與其配偶及未成年子女是否均無自有住宅。-說明
	 */
	public void setRiskQ9Desc(String riskQ9Desc) {
		this.riskQ9Desc = riskQ9Desc;
	}

	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 取得10.借款人、保證人、共同借款人、擔保物提供人及其任職公司是否皆無負面新聞資訊。
	 */
	public String getIsRiskQ10() {
		return isRiskQ10;
	}

	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 設定10.借款人、保證人、共同借款人、擔保物提供人及其任職公司是否皆無負面新聞資訊。
	 */
	public void setIsRiskQ10(String isRiskQ10) {
		this.isRiskQ10 = isRiskQ10;
	}
	
	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 取得10.借款人、保證人、共同借款人、擔保物提供人及其任職公司是否皆無負面新聞資訊。-說明
	 */
	public String getRiskQ10Desc() {
		return riskQ10Desc;
	}

	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 設定10.借款人、保證人、共同借款人、擔保物提供人及其任職公司是否皆無負面新聞資訊。-說明
	 */
	public void setRiskQ10Desc(String riskQ10Desc) {
		this.riskQ10Desc = riskQ10Desc;
	}

	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 取得11.其他補充說明
	 */
	public String getRiskQ11OtherDesc() {
		return riskQ11OtherDesc;
	}

	/**
	 * J-112-0480 消金簡化授信簽報書風險綜合評估改為勾選
	 * 設定11.其他補充說明
	 */
	public void setRiskQ11OtherDesc(String riskQ11OtherDesc) {
		this.riskQ11OtherDesc = riskQ11OtherDesc;
	}
	

	/**
	 * 設定授審會及常董會提案稿合併列印UNID
	 * 
	 * @param combinePrint_unid
	 */
	public void setCombinePrint_unid(String combinePrint_unid) {
		this.combinePrint_unid = combinePrint_unid;
	}

	/**
	 * 取得授審會及常董會提案稿合併列印UNID
	 * 
	 * @return
	 */
	public String getCombinePrint_unid() {
		return combinePrint_unid;
	}

	/**
	 * 設定授審會及常董會提案稿合併列印送件時間
	 * 
	 * @param combinePrint_time
	 */
	public void setCombinePrint_time(Timestamp combinePrint_time) {
		this.combinePrint_time = combinePrint_time;
	}

	/**
	 * 取得授審會及常董會提案稿合併列印送件時間
	 * 
	 * @return
	 */
	public Timestamp getCombinePrint_time() {
		return combinePrint_time;
	}

	/**
	 * 設定授審會及常董會提案稿合併列印執行狀態
	 * 
	 * @param combinePrint_time
	 */
	public void setCombinePrint_status(String combinePrint_status) {
		this.combinePrint_status = combinePrint_status;
	}

	/**
	 * 取得授審會及常董會提案稿合併列印執行狀態
	 * 
	 * @return
	 */
	public String getCombinePrint_status() {
		return combinePrint_status;
	}

	/**
	 * 設定授審會及常董會提案稿合併列印錯誤訊息
	 * 
	 * @param combinePrint_time
	 */
	public void setCombinePrint_errMsg(String combinePrint_errMsg) {
		this.combinePrint_errMsg = combinePrint_errMsg;
	}

	/**
	 * 取得授審會及常董會提案稿合併列印錯誤訊息
	 * 
	 * @return
	 */
	public String getCombinePrint_errMsg() {
		return combinePrint_errMsg;
	}

}
