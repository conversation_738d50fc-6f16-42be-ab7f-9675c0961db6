/* 
 * C900M01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;
import java.util.Map;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C900M01A;

/** 個金產品名稱檔 **/
public interface C900M01ADao extends IGenericDao<C900M01A> {

	C900M01A findByOid(String oid);

	List<C900M01A> findByMainId(String mainId);

	C900M01A findByUniqueKey(String prodKind);

	List<C900M01A> findByIndex01(String prodKind);

	/**
	 * 取得產品種類
	 * 
	 * @param isForShow
	 *            是否用於呈現
	 * @return
	 */
	List<C900M01A> getAll(boolean isForShow);
	
	/**
	 * J-113-0036 新增新產品資訊時，將產品種類下拉選單選項做細部分類
	 * 以貸款類型取得產品種類
	 */
	public List<C900M01A> getAllByLoanType(String loanType);
	
	/**
	 * @return 以prodKind對應prodNM1(+"-"+prodNM2)的Map
	 */
	Map<String,String> getProdMap();
}