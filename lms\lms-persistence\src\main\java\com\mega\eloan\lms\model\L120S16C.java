/* 
 * L120S16C.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 授信額度異動情形 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L120S16C", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "custId", "dupNo" }))
public class L120S16C extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 本案授信戶統編
	 * <p/>
	 * 資料來源：額度明細表主檔
	 */
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/**
	 * 本案授信戶重複序號
	 * <p/>
	 * 資料來源：額度明細表主檔
	 */
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/**
	 * 本案授信戶區部別
	 * <p/>
	 * 101/02/18新增<br/>
	 * 資料來源：額度明細表主檔
	 */
	@Size(max = 1)
	@Column(name = "TYPCD", length = 1, columnDefinition = "CHAR(1)")
	private String typCd;

	/**
	 * 本案授信戶
	 * <p/>
	 * 資料來源：額度明細表主檔
	 */
	@Size(max = 150)
	@Column(name = "CUSTNAME", length = 150, columnDefinition = "VARCHAR(150)")
	private String custName;

	/** 前准額度 **/
	@Size(max = 600)
	@Column(name = "LVTOTAMT", length = 600, columnDefinition = "VARCHAR(600)")
	private String lvTotAmt;

	/** 餘額 **/
	@Size(max = 600)
	@Column(name = "BLTOTAMT", length = 600, columnDefinition = "VARCHAR(600)")
	private String blTotAmt;

	/** 增加減少 **/
	@Size(max = 600)
	@Column(name = "INCAPPLYTOTAMT", length = 600, columnDefinition = "VARCHAR(600)")
	private String incApplyTotAmt;

	/** 額度增減說明 **/
	@Size(max = 600)
	@Column(name = "INCAPPLYMEMO", length = 600, columnDefinition = "VARCHAR(600)")
	private String incApplyMemo;

	/** 授信總額度 **/
	@Size(max = 600)
	@Column(name = "LOANTOTAMT", length = 600, columnDefinition = "VARCHAR(600)")
	private String loanTotAmt;

	/** 其中擔保 **/
	@Size(max = 600)
	@Column(name = "ASSURETOTAMT", length = 600, columnDefinition = "VARCHAR(600)")
	private String assureTotAmt;

	/** 列印順序 **/
	@Digits(integer = 5, fraction = 0, groups = Check.class)
	@Column(name = "PRINTSEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer printSeq;

	/**
	 * 輸入資料檢誤完成(Y/N)
	 * <p/>
	 * 100/12/05新增<br/>
	 * Y/N<br/>
	 * 預先檢核資料是否已登錄完整，供執行【呈主管覆核】時，可快速檢核資料是否完備用。
	 */
	@Size(max = 1)
	@Column(name = "CHKYN", length = 1, columnDefinition = "CHAR(1)")
	private String chkYN;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

    /** 備註 **/
    @Size(max = 900)
    @Column(name = "EXPMEMO", length = 900, columnDefinition = "VARCHAR(900)")
    private String expMemo;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得本案授信戶統編
	 * <p/>
	 * 資料來源：額度明細表主檔
	 */
	public String getCustId() {
		return this.custId;
	}

	/**
	 * 設定本案授信戶統編
	 * <p/>
	 * 資料來源：額度明細表主檔
	 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/**
	 * 取得本案授信戶重複序號
	 * <p/>
	 * 資料來源：額度明細表主檔
	 */
	public String getDupNo() {
		return this.dupNo;
	}

	/**
	 * 設定本案授信戶重複序號
	 * <p/>
	 * 資料來源：額度明細表主檔
	 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/**
	 * 取得本案授信戶區部別
	 * <p/>
	 * 101/02/18新增<br/>
	 * 資料來源：額度明細表主檔
	 */
	public String getTypCd() {
		return this.typCd;
	}

	/**
	 * 設定本案授信戶區部別
	 * <p/>
	 * 101/02/18新增<br/>
	 * 資料來源：額度明細表主檔
	 **/
	public void setTypCd(String value) {
		this.typCd = value;
	}

	/**
	 * 取得本案授信戶
	 * <p/>
	 * 資料來源：額度明細表主檔
	 */
	public String getCustName() {
		return this.custName;
	}

	/**
	 * 設定本案授信戶
	 * <p/>
	 * 資料來源：額度明細表主檔
	 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/** 取得前准額度 **/
	public String getLvTotAmt() {
		return this.lvTotAmt;
	}

	/** 設定前准額度 **/
	public void setLvTotAmt(String value) {
		this.lvTotAmt = value;
	}

	/** 取得餘額 **/
	public String getBlTotAmt() {
		return this.blTotAmt;
	}

	/** 設定餘額 **/
	public void setBlTotAmt(String value) {
		this.blTotAmt = value;
	}

	/** 取得增加減少 **/
	public String getIncApplyTotAmt() {
		return this.incApplyTotAmt;
	}

	/** 設定增加減少 **/
	public void setIncApplyTotAmt(String value) {
		this.incApplyTotAmt = value;
	}

	/** 取得額度增減說明 **/
	public String getIncApplyMemo() {
		return this.incApplyMemo;
	}

	/** 設定額度增減說明 **/
	public void setIncApplyMemo(String value) {
		this.incApplyMemo = value;
	}

	/** 取得授信總額度 **/
	public String getLoanTotAmt() {
		return this.loanTotAmt;
	}

	/** 設定授信總額度 **/
	public void setLoanTotAmt(String value) {
		this.loanTotAmt = value;
	}

	/** 取得其中擔保 **/
	public String getAssureTotAmt() {
		return this.assureTotAmt;
	}

	/** 設定其中擔保 **/
	public void setAssureTotAmt(String value) {
		this.assureTotAmt = value;
	}

	/** 取得列印順序 **/
	public Integer getPrintSeq() {
		return this.printSeq;
	}

	/** 設定列印順序 **/
	public void setPrintSeq(Integer value) {
		this.printSeq = value;
	}

	/**
	 * 取得輸入資料檢誤完成(Y/N)
	 * <p/>
	 * 100/12/05新增<br/>
	 * Y/N<br/>
	 * 預先檢核資料是否已登錄完整，供執行【呈主管覆核】時，可快速檢核資料是否完備用。
	 */
	public String getChkYN() {
		return this.chkYN;
	}

	/**
	 * 設定輸入資料檢誤完成(Y/N)
	 * <p/>
	 * 100/12/05新增<br/>
	 * Y/N<br/>
	 * 預先檢核資料是否已登錄完整，供執行【呈主管覆核】時，可快速檢核資料是否完備用。
	 **/
	public void setChkYN(String value) {
		this.chkYN = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

    /** 取得備註 **/
    public String getExpMemo() {
        return this.expMemo;
    }

    /** 設定備註 **/
    public void setExpMemo(String value) {
        this.expMemo = value;
    }
}
