<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:jdbc="http://www.springframework.org/schema/jdbc" xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
           http://www.springframework.org/schema/jdbc http://www.springframework.org/schema/jdbc/spring-jdbc-3.0.xsd
           http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd">

	<!-- <bean id="com-db" class="org.springframework.jdbc.datasource.SingleConnectionDataSource">
		<property name="driverClassName" value="${com.jdbc.driver}" /> <property
		name="url" value="${com.jdbc.url}" /> <property name="username" value="${com.jdbc.username}"
		/> <property name="password" value="${com.jdbc.password}" /> <property name="suppressClose"
		value="true" /> </bean> -->

	<!-- <bean id="lms-db" class="org.springframework.jdbc.datasource.SingleConnectionDataSource">
		<property name="driverClassName" value="${lms.jdbc.driver}" /> <property
		name="url" value="${lms.jdbc.url}" /> <property name="username" value="${lms.jdbc.username}"
		/> <property name="password" value="${lms.jdbc.password}" /> <property name="suppressClose"
		value="true" /> </bean> -->

	<!-- JNDI Datasource -->
	<bean id="com-db" class="org.springframework.jndi.JndiObjectFactoryBean">
		<property name="jndiName">
			<value>${com.jndiName}</value>
		</property>
	</bean>

	<bean id="lms-db" class="org.springframework.jndi.JndiObjectFactoryBean">
		<property name="jndiName">
			<value>${lms.jndiName}</value>
		</property>
	</bean>

	<bean id="lms4com-db" class="org.springframework.jndi.JndiObjectFactoryBean">
		<property name="jndiName">
			<value>${lms4com.jndiName}</value>
		</property>
	</bean>

</beans>
