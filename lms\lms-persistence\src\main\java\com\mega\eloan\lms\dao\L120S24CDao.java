/* 
 * L120S24CDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S24C;

/** 外部評等風險權數對照表 **/
public interface L120S24CDao extends IGenericDao<L120S24C> {

	L120S24C findByOid(String oid);
	
	/**
	 * 新增了版本日期，要帶版本日期去抓
	 * @param grade
	 * @param versionDate
	 * @return
	 */
	L120S24C findByGradeAndVersionDate(String grade, String versionDate);
	
	/**
	 * 抓某版本日期的全部資料
	 * @param versionDate
	 * @return
	 */
	List<L120S24C> findAll(String versionDate);
	
	/**
	 * 抓下一級的中央政府風險權數，要帶版本日期去抓
	 * @param CentralGov
	 * @return
	 */
	L120S24C findCentralGovNextRW(String centralGov, String versionDate);
}
