/* 
 * LMS1205S16Panel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.panels;

import com.mega.eloan.common.panels.Panel;

/**<pre>
 * 額度批覆表/批覆書
 * </pre>
 * @since  2011/11/4
 * <AUTHOR>
 * @version <ul>
 *           <li>2011/11/4,<PERSON>,new
 *          </ul>
 */
public class CLS1201S16Panel extends Panel {

	public CLS1201S16Panel(String id) {
		super(id);
//		add(new LMS7415S01Panel("_lms7415s01panel"));
	}

	/**/
	private static final long serialVersionUID = 1L;

}
