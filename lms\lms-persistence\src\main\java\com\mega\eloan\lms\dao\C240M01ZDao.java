/* 
 * C240M01ZDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C240M01Z;

/** 每月覆審資料更新記錄檔 **/
public interface C240M01ZDao extends IGenericDao<C240M01Z> {

	C240M01Z findByOid(String oid);
	
	List<C240M01Z> findByMainId(String mainId);
	
	C240M01Z findByUniqueKey(Date dataDate, String branchId);

	List<C240M01Z> findByIndex1Z(Date dataDate, String branchId);
}