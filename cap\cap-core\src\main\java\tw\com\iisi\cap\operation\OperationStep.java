/**
 * OperationStep.java
 *
 * Copyright (c) 2009 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
 */
package tw.com.iisi.cap.operation;

import java.util.Map;

import com.iisigroup.cap.component.PageParameters;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.handler.FormHandler;
import tw.com.iisi.cap.response.IResult;

/**
 * <p>
 * Operation執行步驟.
 * </p>
 * 
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2010/7/23,iristu,new
 *          </ul>
 */
public interface OperationStep {

    /**
     * {@value #NEXT}
     */
    static final String NEXT = "next";

    /**
     * {@value #ERROR}
     */
    static final String ERROR = "error";

    /**
     * {@value #RETURN}
     */
    static final String RETURN = "return";

    /**
     * 取得步驟名稱
     * 
     * @return
     */
    String getName();

    /**
     * 設置步驟名稱
     * 
     * @param name
     *            步驟名稱
     */
    void setName(String name);

    /**
     * 取得所有步驟
     * 
     * @return
     */
    Map<String, String> getRuleMap();

    /**
     * 設置所有步驟
     * 
     * @param ruleMap
     *            所有步驟
     */
    void setRuleMap(Map<String, String> ruleMap);

    /**
     * 執行
     * 
     * @param params
     *            Request參數
     * @param handler
     *            對應執行的handler
     * @param result
     *            回傳結果
     * @return
     * @throws CapException
     */
    String execute(PageParameters params, FormHandler handler, IResult result) throws CapException;

    /**
     * 例外處理
     * 
     * @param e
     *            例外
     * @return
     * @throws CapException
     */
    String handleException(Exception e) throws CapException;

}
