package com.mega.eloan.lms.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import com.mega.eloan.common.model.RelativeMeta;


/**
 * <pre>
 * C140S09B model.
 * </pre>
 * 
 * @since 2011/10/27
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/10/27,<PERSON>,new</li>
 *          </ul>
 */
@NamedEntityGraph(name = "C140S09B-entity-graph", attributeNodes = { @NamedAttributeNode("c140m01a") })
@Entity
@Table(name="C140S09B", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class C140S09B extends RelativeMeta implements Serializable {
	private static final long serialVersionUID = 1L;

    @Temporal( TemporalType.DATE)
	@Column(name="GRT_D")
	private Date grtD;

    //J-105-0080-001 Web e-Loan授信管理系統集團轄下公司名稱欄位放大為38個全形字
	@Column(name="GRT_NA1", length=120)
	private String grtNa1;

	@Column(name="GRT_NM1", precision=11, scale=2)
	private BigDecimal grtNm1;

	@Column(name="GRT_NT1", precision=12)
	private BigDecimal grtNt1;

	@Column(name="GRT_YM1", precision=11, scale=2)
	private BigDecimal grtYm1;

	@Column(name="GRT_YT1", precision=12)
	private BigDecimal grtYt1;

	//bi-directional many-to-one association to C140M01A
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumns({ @JoinColumn(name = "MAINID", referencedColumnName = "MAINID", nullable = false, insertable = false, updatable = false),
            @JoinColumn(name = "PID", referencedColumnName = "UID", nullable = false, insertable = false, updatable = false) })
	private C140M01A c140m01a;

	public Date getGrtD() {
		return this.grtD;
	}

	public void setGrtD(Date grtD) {
		this.grtD = grtD;
	}

	public String getGrtNa1() {
		return this.grtNa1;
	}

	public void setGrtNa1(String grtNa1) {
		this.grtNa1 = grtNa1;
	}

	public BigDecimal getGrtNm1() {
		return this.grtNm1;
	}

	public void setGrtNm1(BigDecimal grtNm1) {
		this.grtNm1 = grtNm1;
	}

	public BigDecimal getGrtNt1() {
		return this.grtNt1;
	}

	public void setGrtNt1(BigDecimal grtNt1) {
		this.grtNt1 = grtNt1;
	}

	public BigDecimal getGrtYm1() {
		return this.grtYm1;
	}

	public void setGrtYm1(BigDecimal grtYm1) {
		this.grtYm1 = grtYm1;
	}

	public BigDecimal getGrtYt1() {
		return this.grtYt1;
	}

	public void setGrtYt1(BigDecimal grtYt1) {
		this.grtYt1 = grtYt1;
	}

	public C140M01A getC140m01a() {
		return this.c140m01a;
	}

	public void setC140m01a(C140M01A c140m01a) {
		this.c140m01a = c140m01a;
	}
	
}