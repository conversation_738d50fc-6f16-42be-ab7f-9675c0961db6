<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<artifactId>lms</artifactId>
		<groupId>com.mega.eloan</groupId>
		<version>1.0.0-SNAPSHOT</version>
	</parent>
	<artifactId>lms-persistence</artifactId>
	<version>1.0.0-SNAPSHOT</version>
	<dependencies>
		<dependency>
			<groupId>com.mega.eloan</groupId>
			<artifactId>lms-config</artifactId>
			<version>${project.version}</version>
			<type>jar</type>
			<scope>compile</scope>
		</dependency>
	</dependencies>
	
	<build>
		<plugins>
			<plugin>
				<groupId>org.hibernate.orm.tooling</groupId>
				<artifactId>hibernate-enhance-maven-plugin</artifactId>
				<version>5.6.9.Final</version>
				<executions>
					<execution>
						<configuration>
							<enableLazyInitialization>true</enableLazyInitialization>
							<enableDirtyTracking>true</enableDirtyTracking>
						</configuration>
						<goals>
							<goal>enhance</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<!-- <plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>openjpa-maven-plugin</artifactId>
				<version>1.2</version>
				<configuration>
					<persistenceXmlFile>${project.basedir}/src/main/resources/META-INF/persistence-com.xml</persistenceXmlFile>
					<persistenceXmlFile>${project.basedir}/src/main/resources/META-INF/persistence-lms.xml</persistenceXmlFile>
					<includes>com/mega/eloan/lms/model/*.class</includes>
					<excludes>com/mega/eloan/lms/model/*_.class,com/mega/eloan/lms/model/*$*.class</excludes>
					<addDefaultConstructor>true</addDefaultConstructor>
					<enforcePropertyRestrictions>true</enforcePropertyRestrictions>

				</configuration>
				<executions>
					<execution>
						<id>enhancer</id>
						<phase>process-classes</phase>
						<goals>
							<goal>enhance</goal>
						</goals>
					</execution>
				</executions>

				<dependencies>
					<dependency>
						<groupId>org.apache.openjpa</groupId>
						<artifactId>openjpa</artifactId>
						<version>${jpa.version}</version>
					</dependency>
				</dependencies>

			</plugin>-->
		</plugins>
	</build>
</project>