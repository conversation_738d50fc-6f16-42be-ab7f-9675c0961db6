/* 
 * L300S01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L300S01A;

/** 覆審考評表明細檔 **/
public interface L300S01ADao extends IGenericDao<L300S01A> {

	L300S01A findByOid(String oid);
	
	List<L300S01A> findByMainId(String mainId);

	List<L300S01A> findByIndex01(String mainId, String itemType, String itemName);
}