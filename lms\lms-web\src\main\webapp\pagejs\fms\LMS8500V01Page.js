$(document).ready(function(){

    var grid = $("#gridview").iGrid({
        handler: 'lms8500gridhandler',
        height: 350,
        width: 785,
        autowidth: false,
        action: "queryL850m01a",
        postData: {
            docStatus: viewstatus
        },
        rowNum: 15,
        sortname: "createTime",
        sortorder: "desc",
        multiselect: true,
        colModel: [{
	        colHeader: i18n.lms8500v01["L850M01A.appCode"], // 申請類別
	        name: 'appCode',
	        align: "left", 
	        width: 100, 
	        sortable: true, 
	        formatter: 'click', 
	        onclick: openDoc
        }, {
            colHeader: i18n.lms8500v01['L850M01A.docStatus'],// "狀態",
            name: 'docStatusStr',
            width: 50,
            align: "center"
        }, {
            colHeader: i18n.lms8500v01['L850M01A.creator'],// "經辦",
            name: 'updaterName',
            width: 80,
            sortable: true,
            align: "center"
        }, {
        	colHeader: i18n.lms8500v01["L850M01A.createTime"], // 建立日期
        	name: 'createTime',
        	align: "left",
        	width: 80, // 設定寬度
        	sortable: true, // 是否允許排序
        	formatter: 'date',
        	formatoptions: {
        		srcformat: 'Y-m-d H:i:s',
        		newformat: 'Y-m-d H:i'
        	}
        }, {
            name: 'oid',
            hidden: true
        }, {
            name: 'mainId',
            hidden: true
        }, {
            name: 'docURL',
            hidden: true
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = $("#gridview").getRowData(rowid);
            openDoc(null, null, data);
        }
    });
	
    // 塞入新增和查詢的select選項
    $.ajax({
		type : "POST",
		handler : "lms8500m01formhandler",
		action : "getAppCodeBySsoUnitno",
		data : 
		{
			mainId : ''
		},				
		success:function(responseData){
			var item = JSON.parse(DOMPurify.sanitize(JSON.stringify(responseData)));
			$("#appCodeFilter").setItems({
				size : 1,
                item: item,
                format: "{key} "
			});
			
			$("#appCode").setItems({
				size : 1,
                item: item,
                format: "{key} "
			});
		}
	});	
    
    function openDoc(cellvalue, options, rowObject){
        $.form.submit({			
            url: '..' + DOMPurify.sanitize(rowObject.docURL) + '/01',
            data: {
                oid: DOMPurify.sanitize(rowObject.oid),
                mainId: DOMPurify.sanitize(rowObject.mainId),
                mainOid: rowObject.oid,// 這個一定要有，跟控制按鈕有關
                mainDocStatus: viewstatus
            },
            target: rowObject.oid
        });
    }
	
	
    $("#buttonPanel").find("#btnDelete").click(function(){
        var rows = $("#gridview").getGridParam('selarrrow');
        var data = [];
        
        if (rows == "") {// TMMDeleteError=請先選擇需修改(刪除)之資料列
            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
        }
        //confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                for (var i in rows) {
                    data.push($("#gridview").getRowData(rows[i]).oid);
                }
                
                $.ajax({
                    handler: "lms8500m01formhandler",
                    data: {
                        formAction: "deleteL850m01a",
                        oids: data
                    },
                    success: function(obj){
                    	grid.trigger("reloadGrid");
                    }
                });
            }
        });
    }).end().find("#btnFilter").click(function(){
    	$("#filterThickBox").thickbox({
    		title : '請選擇上傳資料類別',
    		width : 500,
    		height : 300,
    		modal : true,
    		align : 'center',
    		valign: 'bottom',
    		i18n: i18n.def,
    		open : function() {
    			$(this).find("#filterForm").reset();
    		},
    		buttons : {
    			'sure' : function(){
    				var appCode = $("#appCodeFilter").val();
    				grid.jqGrid("setGridParam", {
    					postData: {
    						'appCode': appCode
    					},
    					search: true
    				}).trigger("reloadGrid");
    				$.thickbox.close();
    			},
    			'close' : function(){
    				$.thickbox.close();
    			}
    		}
    	});
    }).end().find("#btnAdd").click(function(){
    	$("#addThickBox").thickbox({
			title : '請選擇上傳資料類別',
			width : 500,
			height : 300,
			modal : true,
			align : 'center',
			valign: 'bottom',
			i18n: i18n.def,
			open : function() {
				$(this).find("#addForm").reset();
			},
			buttons : {
				'sure' : function(){
					var appCode = $("#appCode").val();
					if ($.trim(appCode) == "") {
						// message.checkAppCode=請選擇申請類別
						return CommonAPI.showMessage(i18n.lms8500v01["message.checkAppCode"]);
					}
					// 新增一筆L850M01A
					$.ajax({
						handler: "lms8500m01formhandler",
						action : 'newL850m01a',
						data : {
							appCode: appCode
						},
						success: function(obj){
							$.thickbox.close();
							// reload grid
							grid.trigger("reloadGrid");
							openDoc(null, null, obj);
						}
					});
				},
				'close' : function(){
					$.thickbox.close();
				}
			}
		});
    });
});

