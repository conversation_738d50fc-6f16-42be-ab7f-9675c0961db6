package com.mega.eloan.lms.las.pages;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import java.io.OutputStream;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.lms.base.pages.AbstractPdfReportPage;
import com.mega.eloan.lms.base.pages.AbstractSimplePdfPage;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;

/**
 * 授信業務工作底稿基本內容列印
 * 
 * <AUTHOR>
 * 
 */
@Controller
@RequestMapping("/las/lms1925r02")
public class LMS1925R02Report extends AbstractPdfReportPage {

	public LMS1925R02Report() {
		super();
	}

	@Override
	public String getDownloadFileName() {
		return "lms1925r01.pdf";
	}

	@Override
	public String getFileDownloadServiceName() {
		return "lms1925r01rptservice";
	}
	
	//UPGRADE：或可參照LMS1200P01Page.java的方式return
	@Override
	protected String getViewName() {
		return null;
	}

}
