package com.mega.eloan.lms.base.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.LmsExcelUtil;
import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 異常通報產Excel
 * </pre>
 * 
 * @since 2012/12/3
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/3,Miller
 *          </ul>
 */
@Service("lmslgdxlsservice")
public class LMSLgdXLSServiceImpl implements FileDownloadService {

	@Resource
	LMSService lmsService;

	@Resource
	EloandbBASEService eloanDbBaseService;

	@Resource
	BranchService branch;

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(LMSLgdXLSServiceImpl.class);

	@Override
	public byte[] getContent(PageParameters params) throws CapException {
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Properties popComm = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);

		ByteArrayOutputStream baos = null;

		HSSFWorkbook book = null;
		HSSFSheet sheet1 = null;
		HSSFSheet sheet2 = null;
		HSSFSheet sheet3 = null;
		HSSFFont font12 = null;
		HSSFCellStyle format12Center = null;
		HSSFCellStyle formatFontLeft = null;
		
		try {
			baos = new ByteArrayOutputStream();
			book = new HSSFWorkbook();
			
			sheet1 = book.createSheet("額度共用");
			sheet2 = book.createSheet("EAD＆LGD");
			sheet3 = book.createSheet("擔保品");
			
			// 設定橫向列印
			sheet1.getPrintSetup().setLandscape(true);
			sheet2.getPrintSetup().setLandscape(true);
			sheet3.getPrintSetup().setLandscape(true);
			// 啟用縮放比例
			sheet1.setFitToPage(true);
			sheet2.setFitToPage(true);
			sheet3.setFitToPage(true);
			// 縮放比例頁寬為 1 頁
			sheet1.getPrintSetup().setFitWidth((short) 1);
			sheet2.getPrintSetup().setFitWidth((short) 1);
			sheet3.getPrintSetup().setFitWidth((short) 1);

			// 縮放比例頁高為 5000 頁（通常設成 0 表示不限制高度）
			sheet1.getPrintSetup().setFitHeight((short) 5000);
			sheet2.getPrintSetup().setFitHeight((short) 5000);
			sheet3.getPrintSetup().setFitHeight((short) 5000);
			
			// 建立字型（12pt、非粗體）
			font12 = book.createFont();
			font12.setFontName(popComm.getProperty("other.msg60"));
			font12.setFontHeightInPoints((short) 12);
			font12.setBold(false); // 或 font12.setBoldweight(HSSFFont.BOLDWEIGHT_NORMAL);
			// 建立 CellFormat：置中
			format12Center = LmsExcelUtil.setCellFormat(book, font12, HorizontalAlignment.CENTER);
			// 建立 CellFormat：靠左
			formatFontLeft = LmsExcelUtil.setCellFormat(book, font12, HorizontalAlignment.LEFT);
			
			// L120S21A*****************************************************************

			List<Map<String, Object>> list1 = null;
			LinkedHashMap<String, String> fieldNmMap1 = new LinkedHashMap<String, String>();

			list1 = eloanDbBaseService.findL120s21aByMainId(mainId);

			// 取得欄位名稱跟計算欄位數
			if (list1.size() > 0) {

				// 欄位名稱
				for (Map<String, Object> map : list1) {
					for (String fieldName : map.keySet()) {
						fieldNmMap1.put(fieldName, fieldName);
					}
					break;
				}
			}

			int y1 = 0;
			sheet1 = this.setLastTitleL120S21(sheet1, format12Center, popComm, fieldNmMap1, y1);

			for (Map<String, Object> map : list1) {

				y1++;
				book = this.setColumnDataL120S21(map, book, sheet1,	formatFontLeft, popComm, y1, fieldNmMap1);
			}

			// L120S21B*****************************************************************

			List<Map<String, Object>> list2 = null;
			LinkedHashMap<String, String> fieldNmMap2 = new LinkedHashMap<String, String>();

			list2 = eloanDbBaseService.findL120s21bByMainId(mainId);

			// 取得欄位名稱跟計算欄位數
			if (list2.size() > 0) {

				// 欄位名稱
				for (Map<String, Object> map : list2) {
					for (String fieldName : map.keySet()) {
						fieldNmMap2.put(fieldName, fieldName);
					}
					break;
				}
			}

			int y2 = 0;
			sheet2 = this.setLastTitleL120S21(sheet2, format12Center, popComm,
					fieldNmMap2, y2);

			for (Map<String, Object> map : list2) {

				y2++;
				book = this.setColumnDataL120S21(map, book, sheet2,	formatFontLeft, popComm, y2, fieldNmMap2);
			}

			// L120S21C*****************************************************************

			List<Map<String, Object>> list3 = null;
			LinkedHashMap<String, String> fieldNmMap3 = new LinkedHashMap<String, String>();

			list3 = eloanDbBaseService.findL120s21cByMainId(mainId);

			// 取得欄位名稱跟計算欄位數
			if (list3.size() > 0) {

				// 欄位名稱
				for (Map<String, Object> map : list3) {
					for (String fieldName : map.keySet()) {
						fieldNmMap3.put(fieldName, fieldName);
					}
					break;
				}
			}

			int y3 = 0;
			sheet3 = this.setLastTitleL120S21(sheet3, format12Center, popComm, fieldNmMap3, y3);

			for (Map<String, Object> map : list3) {

				y3++;
				book = this.setColumnDataL120S21(map, book, sheet3,	formatFontLeft, popComm, y3, fieldNmMap3);
			}
			
			book.write(baos);
			book.close();
			return baos.toByteArray();
		} catch (Exception ex) {
			LOGGER.error("[getContent] Exception!!", ex.getMessage());
		} finally {
			if (baos != null) {
				try {
					baos.close();
				} catch (IOException ex) {
					LOGGER.error("[getContent] Exception!!", ex.getMessage());
				}
			}
		}
		return null;
	}

	/**
	 * 設定SHEET的TITLE
	 * 
	 * @param sheet
	 * @param formatFont
	 * @param prop
	 * @return
	 */
	private HSSFSheet setLastTitleL120S21(HSSFSheet sheet,
			HSSFCellStyle formatFont, Properties prop,
			LinkedHashMap<String, String> fieldNmMap, int y) {

		int x = 0;
		HSSFRow row = sheet.createRow(y);
		for (String fieldName : fieldNmMap.keySet()) {
			sheet.setColumnWidth(x, 20 * 256); // POI 是以 1/256 字元寬度單位設定
			HSSFCell cell = row.createCell(x);
	        cell.setCellValue(fieldName); // 寫入欄位名稱
	        cell.setCellStyle(formatFont); // 套用格式
			x = x + 1;
		}

		return sheet;
	}

	/**
	 * 設定內容
	 * 
	 * @param Map<String, Object> map
	 * @param HSSFWorkbook book
	 * @param HSSFSheet sheet
	 * @param HSSFCellStyle formatFont
	 * @param Properties prop
	 * @param int y
	 * @param LinkedHashMap<String, String> fieldNmMap
	 * @return
	 */
	private HSSFWorkbook setColumnDataL120S21(Map<String, Object> map,
			HSSFWorkbook book, HSSFSheet sheet,	HSSFCellStyle formatFont,
			Properties prop, int y, LinkedHashMap<String, String> fieldNmMap) {
		
		int x = 0;
		HSSFRow row = sheet.createRow(y);
		for (String fieldName : fieldNmMap.keySet()) {
			String tmpVal = Util.trim(map.get(fieldName));
			HSSFCell cell = row.createCell(x);
	        cell.setCellValue(tmpVal); // 寫入欄位名稱
	        cell.setCellStyle(formatFont); // 套用格式
			x = x + 1;
		}

		return book;
	}

}
