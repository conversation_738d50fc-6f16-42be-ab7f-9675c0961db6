/* 
 * LMS1705Formhandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lrs.handler.form;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.TreeMap;

import javax.annotation.Resource;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.TypCdEnum;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.BstblService;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.service.UserInfoService.SignEnum;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.LrsUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.lrs.constants.lrsConstants;
import com.mega.eloan.lms.lrs.panels.LMS1705S01Panel;
import com.mega.eloan.lms.lrs.panels.LMS1705S04Panel;
import com.mega.eloan.lms.lrs.service.LMS170502Service;
import com.mega.eloan.lms.lrs.service.LMS1705Service;
import com.mega.eloan.lms.lrs.service.LMS1805Service;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.model.L170M01A;
import com.mega.eloan.lms.model.L170M01B;
import com.mega.eloan.lms.model.L170M01C;
import com.mega.eloan.lms.model.L170M01D;
import com.mega.eloan.lms.model.L170M01E;
import com.mega.eloan.lms.model.L170M01F;
import com.mega.eloan.lms.model.L170M01G;
import com.mega.eloan.lms.model.L170M01I;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapString;
import tw.com.iisi.cap.utils.CapWebUtil;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 覆審報告表
 * </pre>
 * 
 * @since 2011/9/7
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/9/7,jessica,new
 *          </ul>
 */

@Scope("request")
@Controller("lms1705m01formhandler")
public class LMS1705M01Formhandler extends AbstractFormHandler {

	@Resource
	BstblService bstblService;
	@Resource
	UserInfoService userInfoService;

	@Resource
	BranchService branch;

	@Resource
	LMS1705Service lms1705service;

	@Resource
	LMS170502Service lms170502Service;

	@Resource
	LMS1805Service service1805;

	@Resource
	DocCheckService docCheckService;

	@Resource
	CodeTypeService codetypeService;

	@Resource
	MisCustdataService lmsCustdataService;

	@Resource
	LMSService lmsService;

	@Resource
	RetrialService retrialService;

	@Resource
	EloandbBASEService eloandbBASEService;

	/**
	 * 回傳l170s01d預設值
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult returnL170s04Val(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String hascms = params.getString("HASCMS", "N");
		// 先判斷L170M01A是否有資料(授信資料合計)
		L170M01A l170m01a = lms1705service.findModelByMainId(L170M01A.class,
				mainId);
		if (l170m01a == null) {
			l170m01a = new L170M01A();
		}
		L170M01F l170m01f = lms1705service.findModelByMainId(L170M01F.class,
				mainId);
		if (l170m01f == null) {
			l170m01f = new L170M01F();
		}
		List<L170M01D> l170m01dlist = lms1705service
				.findL170m01dByMainId(mainId);
		int quotaResult = -1;
		StringBuffer msg = new StringBuffer();
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1705S04Panel.class);
		try {
			// "※覆審內容第【０４】項：借款人項下授信合計(循環:額度、不循環:餘額)共 "+Cstr(Format(totalAmt,"###,###,###,###,###,##0"))+"仟元，【逾】三千萬(含)，預設值為【是】"
			// "※覆審內容第【０４】項：借款人項下授信合計(循環:額度、不循環:餘額)共 "+Cstr(Format(totalAmt,"###,###,###,###,###,##0"))+"仟元，【未達】三千萬，預設值為【－】"
			BigDecimal totQuotaTemp = LMSUtil.toBigDecimal(l170m01a
					.getTotQuota());
			if (totQuotaTemp != null) {
				totQuotaTemp = totQuotaTemp.divide(new BigDecimal(1000));
				if (totQuotaTemp.compareTo(new BigDecimal(30000)) == 1) {
					// ※覆審內容第【０４】項：借款人項下授信合計(循環:額度、不循環:餘額)共Cstr(Format(totalAmt,"###,###,###,###,###,##0"))+"仟元，【逾】三千萬(含)，預設值為【是】"
					quotaResult = 1;

				} else {
					quotaResult = 0;
				}
			} else {
				// "※覆審內容第【０４】項：計算借款人授信合計錯誤【"+calcuMsg+"】，設值為【－】"
				quotaResult = 0;
			}
			if (quotaResult == 1) {
				msg.append(prop.getProperty("L170S04.MSG01"))
						.append(NumConverter.addComma(totQuotaTemp))
						.append(prop.getProperty("L170S04.MSG02"))
						.append("<BR/>");
			} else {
				msg.append(prop.getProperty("L170S04.MSG01"))
						.append(NumConverter.addComma(totQuotaTemp))
						.append(prop.getProperty("L170S04.MSG03"))
						.append("<BR/>");
			}
			// 前次覆審報告表
			// String doc = RetrialDocStatusEnum.已核准.getCode();
			// 塞預設值
			boolean ver = Util
					.isNotEmpty(Util.nullToSpace(l170m01a.getRptId())) ? true
					: false;
			List<L170M01D> list = new ArrayList<L170M01D>();
			for (L170M01D l170m01d : l170m01dlist) {
				if (ver) {

					// 有RPTID
					// J-108-0888_05097_B1001
					String itemType = l170m01d.getItemType();
					String itemNo = l170m01d.getItemNo();

					// if ("Z".equals(itemType) || "Y".equals(itemType)
					// || "X".equals(itemType)) {
					// // continue;
					//
					//
					// } else {
					String[] B = new String[] { "B020", "B021", "B022" };
					// J-108-0128_05097_B1001 Web e-Loan企金授信覆審系統修改覆審報告表內容。
					String[] K = new String[] { "B009", "B024", "B010", "B015",
							"B025", "B017", "B018" };

					// J-108-0128_05097_B1001 Web e-Loan企金授信覆審系統修改覆審報告表內容。
					String[] Y = new String[] { "A001", "A002", "A003", "B001",
							"B002", "B003", "B004", "B005", "B006", "B011",
							"B012", "B013", "B014", "B023", "C001", "C002" };
					
					// J-113-0204  新增及修正說明文句
					String[] N = new String[] { "A005" };

					// 第14、16項附表欄位X、Y類**********************************************
					String[] XY_Y = new String[] { "X110", "X111", "X112",
							"X210", "X211", "X212", "Y111", "Y112", "Y121",
							"Y122", "Y123", "Y124", "Y12A", "Y12B", "Y12C",
							"Y12D", "Y12E", "Y12F", "Y131", "Y132", "Y133",
							"Y134", "Y135" };
					String[] XY_K = new String[] { "X113", "X213" };

					String[] CMS_Y = { "" };
					String[] CMS_N = { "" };
					String[] CMS_K = { "" };
					// J-111-0405 更動覆審系統內以下15式覆審報告表之文字內容。
					if (LrsUtil.compareRptVersion(l170m01a.getRptId(), ">=",
							LrsUtil.V_O_202210)) {
						// Y210改Y21A
						if ("Y".equals(hascms)) {
							CMS_Y = new String[] { "Y21A", "Y211", "Y212",
									"Y213" };
						} else {
							CMS_N = new String[] { "Y21A" };
							CMS_K = new String[] { "Y211", "Y212", "Y213" };
						}
					} else {
						if ("Y".equals(hascms)) {
							CMS_Y = new String[] { "Y210", "Y211", "Y212",
									"Y213" };
						} else {
							CMS_N = new String[] { "Y210" };
							CMS_K = new String[] { "Y211", "Y212", "Y213" };
						}
					}

					// 土建融Z類(含B007、B008)
					String[] Z_Y = { "" };
					String[] Z_N = { "" };
					String[] Z_K = { "" };
					if (Util.equals(Util.trim(l170m01a.getRealRpFg()), "Y")) {
						// 土建融案
						Z_Y = new String[] { "B007", "B008", "Z100", "Z200",
								"Z300", "Z400" };
					} else {
						// 非土建融案
						Z_K = new String[] { "B007", "B008", "Z100", "Z200",
								"Z300", "Z400" };
					}

					if ("A004".equals(itemNo)) {
						if (quotaResult == 1) {
							l170m01d.setChkResult("Y");
						} else if (quotaResult == 0) {
							l170m01d.setChkResult("K");
						} else {
							l170m01d.setChkResult("K");
						}
					} else if ("B016".equals(itemNo)) {
						if (l170m01a.getLastRetrialDate() != null) { // K
							msg.append(prop.getProperty("L170S04.MSG06"));
							l170m01d.setChkResult("K");
						} else {
							if ("2".equals(l170m01f.getConFlag())) { // Y
								// 前次已完成之覆審報告【有】異常或應行改善事項，預設值為【有】"
								l170m01d.setChkResult("Y");
								msg.append(prop.getProperty("L170S04.MSG07"));
							} else { // N
								// 前次已完成之覆審報告【無】異常或應行改善事項，預設值為【－】"
								l170m01d.setChkResult("K");
								msg.append(prop.getProperty("L170S04.MSG06"));
							}
						}
					} else if ("B019".equals(itemNo)) {
						if ("Y".equals(hascms)) {
							l170m01d.setChkResult("N");
						} else {
							l170m01d.setChkResult("K");
						}
					} else if (Arrays.asList(B).contains(itemNo)) {
						if ("Y".equals(hascms)) {
							l170m01d.setChkResult("Y");
						} else {
							l170m01d.setChkResult("K");
						}
					} else if (Arrays.asList(K).contains(itemNo)) {
						l170m01d.setChkResult("K");
					} else if (Arrays.asList(Y).contains(itemNo)) {
						l170m01d.setChkResult("Y");
					} else if (Arrays.asList(N).contains(itemNo)) {
						l170m01d.setChkResult("N");
					} else if (Arrays.asList(XY_K).contains(itemNo)) {
						l170m01d.setChkResult("K");
					} else if (Arrays.asList(XY_Y).contains(itemNo)) {
						l170m01d.setChkResult("Y");
					} else if (Arrays.asList(CMS_Y).contains(itemNo)) {
						l170m01d.setChkResult("Y");
					} else if (Arrays.asList(CMS_N).contains(itemNo)) {
						l170m01d.setChkResult("N");
					} else if (Arrays.asList(CMS_K).contains(itemNo)) {
						l170m01d.setChkResult("K");
					} else if (Arrays.asList(Z_Y).contains(itemNo)) {
						l170m01d.setChkResult("Y");
					} else if (Arrays.asList(Z_K).contains(itemNo)) {
						l170m01d.setChkResult("K");
					} else {

						l170m01d.setChkResult("Y");

					}
					// }

				} else {

					// 無RPTID
					int seq = l170m01d.getItemSeq();

					switch (seq) {
					case 4:
						if (quotaResult == 1) {
							l170m01d.setChkResult("Y");
						} else if (quotaResult == 0) {
							l170m01d.setChkResult("K");
						} else {
							l170m01d.setChkResult("K");
						}
						break;
					case 5:
					case 12:
					case 13:
					case 14:
					case 19:
					case 21:
					case 22:
						l170m01d.setChkResult("K");
						break;
					case 20:
						if (l170m01a.getLastRetrialDate() != null) {
							// K
							msg.append(prop.getProperty("L170S04.MSG04"));
							l170m01d.setChkResult("K");
						} else {
							if ("2".equals(l170m01f.getConFlag())) {
								// Y
								// "※覆審內容第【２０】項：前次已完成之覆審報告【有】異常或應行改善事項，預設值為【有】"
								l170m01d.setChkResult("Y");
								msg.append(prop.getProperty("L170S04.MSG05"));
							} else {
								// N
								// "※覆審內容第【２０】項：前次已完成之覆審報告【無】異常或應行改善事項，預設值為【－】"
								l170m01d.setChkResult("K");
								msg.append(prop.getProperty("L170S04.MSG04"));
							}
						}
						break;
					case 23:
						if ("Y".equals(hascms)) {
							l170m01d.setChkResult("N");
						} else {
							l170m01d.setChkResult("K");
						}
						break;
					case 24:
					case 25:
					case 26:
						if ("Y".equals(hascms)) {
							l170m01d.setChkResult("Y");
						} else {
							l170m01d.setChkResult("K");
						}
						break;
					default:
						l170m01d.setChkResult("Y");
						break;
					}
				}
				list.add(l170m01d);
			}

			JSONArray ja = new JSONArray();
			for (L170M01D model : list) {
				JSONObject data = DataParse.toJSON(model);
				ja.add(data);
			}
			result.set("L170M01DArray", ja);
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, msg.toString());
		} catch (CapException e) {
			HashMap<String, String> param = new HashMap<String, String>();
			param.put("colName", "內部發生錯誤");
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0005", param), getClass());
		} finally {

		}

		return result;

	}

	/**
	 * 儲存前檢查必要欄位是否都已輸入
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult checkSendBoss(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String checkBranchComm = params.getString("checkBranchComm");
		L170M01A l170m01a = lms1705service.findModelByMainId(L170M01A.class,
				mainId);
		// 信用評等檔
		List<L170M01E> l170m01eList = lms1705service.findL170m01eByMainId(
				mainId, "T");
		List<L170M01D> l170m01dList = lms1705service
				.findL170m01dByMainId(mainId);
		L170M01F l170m01f = lms1705service.findModelByMainId(L170M01F.class,
				mainId);
		String projectNo = l170m01a.getProjectNo();
		String projectSeq = l170m01a.getProjectNo();
		Date retrialDate = l170m01a.getRetrialDate();
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1705S01Panel.class);
		HashMap<String, String> param = new HashMap<String, String>();
		result.set("check", true);
		if (projectNo == null && projectSeq == null) {
			// EFD3016=INFO|無覆審序號,不可移授檢單位登錄！|
			// throw new CapMessageException(RespMsgHelper.getMessage(parent,
			// "EFD3016", param), getClass());
			result.set("WARNCODE", RespMsgHelper.getMainMessage("EFD3016"));
			return result;
		}
		if (Util.isEmpty(retrialDate)) {
			result.set("check", false);
			// EFD0005=ERROR|$\{colName\}此欄位不可空白|(覆審日期)
			param.put("colName", pop.getProperty("L170M01a.retrialDate"));
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0005", param), getClass());
		}

		if (l170m01eList.size() > 0) {
			boolean checkL170M01EResult = false;
			for (L170M01E l170m01e : l170m01eList) {
				if (!checkL170M01EResult) {
					if ("NA".equals(Util.trim(l170m01e.getCrdType()))
							|| "G".equals(Util.trim(l170m01e.getCrdType()))) {
						checkL170M01EResult = true;
					} else if (Util.trim(l170m01e.getCrdType()).startsWith("N")) {

					} else if ("".equals(Util.trim(l170m01e.getCrdType()))) {

					} else {
						if ("".equals(Util.trim(l170m01e.getGrade()))) {
							result.set("check", false);
							// EFD0005=ERROR|$\{colName\}此欄位不可空白| (信用評等)
							param.put("colName",
									pop.getProperty("L170M01a.credit"));
							throw new CapMessageException(RespMsgHelper.getMessage("EFD0005", param), getClass());
						}
						checkL170M01EResult = true;
					}
				}
			}
			if (!checkL170M01EResult) {
				result.set("check", false);
				// EFD0005=ERROR|$\{colName\}此欄位不可空白|(信用評等)
				param.put("colName", pop.getProperty("L170M01a.credit"));
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0005", param), getClass());
			}
		} else {
			result.set("check", false);
			// EFD0005=ERROR|$\{colName\}此欄位不可空白|(信用評等)
			param.put("colName", pop.getProperty("L170M01A.check02"));
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0005", param), getClass());
		}

		// 前次信用評等資訊
		List<L170M01E> exL170m01eList = lms1705service.findL170m01eByMainId(
				mainId, "L");
		if (exL170m01eList.size() > 0) {
			boolean checkL170M01EResult = false;
			for (L170M01E exL170m01e : exL170m01eList) {
				if (!checkL170M01EResult) {
					if ("NA".equals(Util.trim(exL170m01e.getCrdType()))
							|| "G".equals(Util.trim(exL170m01e.getCrdType()))
							|| Util.equals(UtilConstants.Type.無資料_C,
									exL170m01e.getCrdType())
							|| Util.equals(UtilConstants.Type.無資料_M,
									exL170m01e.getCrdType())) {
						checkL170M01EResult = true;
					} else if (Util.trim(exL170m01e.getCrdType()).startsWith(
							"N")) {

					} else if ("".equals(Util.trim(exL170m01e.getCrdType()))) {

					} else {
						if ("".equals(Util.trim(exL170m01e.getGrade()))) {
							result.set("check", false);
							// EFD0005=ERROR|$\{colName\}此欄位不可空白| (信用評等)
							param.put("colName", pop.getProperty("L170M01a.ex")
									+ pop.getProperty("L170M01a.credit"));
							throw new CapMessageException(RespMsgHelper.getMessage("EFD0005", param), getClass());
						}
						checkL170M01EResult = true;
					}
				}
			}
			if (!checkL170M01EResult) {
				result.set("check", false);
				// EFD0005=ERROR|$\{colName\}此欄位不可空白|(信用評等)
				param.put(
						"colName",
						pop.getProperty("L170M01a.ex")
								+ pop.getProperty("L170M01a.credit"));
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0005", param), getClass());
			}
		} else {
			result.set("check", false);
			// EFD0005=ERROR|$\{colName\}此欄位不可空白|(信用評等)
			param.put("colName", pop.getProperty("L170M01A.check02"));
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0005", param), getClass());
		}

		String cPR = "";
		String num = "";
		if (Util.equals("", Util.nullToSpace(l170m01a.getRptId()))) {
			cPR = "B014";
			num = "19";
		} else {
			cPR = "B015";
			num = "18";
		}

		if (l170m01dList != null) {
			String chkResult = l170m01dList.get(18).getChkResult();
			// 前次覆審項目檢核 chkPreReview
			if (Util.equals(cPR, l170m01dList.get(18).getItemNo())) {
				if (chkResult == null || "".equals(chkResult)) {
					result.set("check", false);
					// ERROR|請選擇第19項是否有改善，並且輸入說明|
					param.put("number", num);
					throw new CapMessageException(RespMsgHelper.getMessage("EFD3008", param), getClass());
				} else {
					result.set("check", true);
				}
			}
		} else {
			// ERROR|請選擇第19項是否有改善，並且輸入說明|
			param.put("number", num);
			throw new CapMessageException(RespMsgHelper.getMessage("EFD3008", param), getClass());
		}

		if (Util.isEmpty(l170m01f) || Util.isEmpty(l170m01f.getRetialComm())) {
			result.set("check", false);
			// ERROR||
			throw new CapMessageException(RespMsgHelper.getMessage("EFD3028", param), getClass());
		}
		if (Util.isEmpty(l170m01f)
				|| Util.isEmpty(Util.trim(l170m01f.getConFlag()))) {
			result.set("check", false);
			// ERROR||
			throw new CapMessageException(RespMsgHelper.getMessage("EFD3029", param), getClass());
		}
		if ("Y".equals(checkBranchComm)) {
			if (Util.isEmpty(l170m01f)
					|| Util.isEmpty(Util.trim(l170m01f.getBranchComm()))) {
				result.set("check", false);
				// EFD0005=ERROR|$\{colName\}此欄位不可空白|(覆審日期)
				param.put("colName", pop.getProperty("L170M01a.error5"));
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0005", param), getClass());
			}
		}

		return result;
	}

	/**
	 * 儲存前檢查必要欄位是否都已輸入
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult checkSend(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		CapAjaxFormResult result = new CapAjaxFormResult();
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		String mainId = params.getString(EloanConstants.MAIN_ID);
		L170M01A l170m01a = lms1705service.findModelByMainId(L170M01A.class,
				mainId);

		String custId = l170m01a.getCustId();
		String dupNo = l170m01a.getDupNo();
		String crdType = Util.trim(params.getString("crdType", ""));
		String grade = Util.trim(params.getString("grade", ""));
		String crdTypeMow = Util.trim(params.getString("crdTypeMow", ""));
		String mow = Util.trim(params.getString("mow", ""));
		String excrdType = Util.trim(params.getString("excrdType", ""));
		String exgrade = Util.trim(params.getString("exgrade", ""));
		String excrdTypeMow = Util.trim(params.getString("excrdTypeMow", ""));
		String exmow = Util.trim(params.getString("exmow", ""));

		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1705S01Panel.class);

		HashMap<String, String> param = new HashMap<String, String>();
		switch (page) {

		case 1:
			String formL170m01a = params.getString("L170M01aForm");
			DataParse.toBean(formL170m01a, l170m01a);
			Date retrialDate = l170m01a.getRetrialDate();
			Date lastRetrialDate = l170m01a.getLastRetrialDate();
			if (retrialDate == null) {
				result.set("check", false);
				// EFD0005=ERROR|$\{colName\}此欄位不可空白|(覆審日期)
				param.put("colName", pop.getProperty("L170M01a.retrialDate"));
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0005", param), getClass());
			}

			if (retrialDate != null && lastRetrialDate != null) {
				if (Integer
						.parseInt(TWNDate.toAD(retrialDate).replace("-", "")) <= Integer
						.parseInt(TWNDate.toAD(lastRetrialDate)
								.replace("-", ""))) {
					result.set("check", false);
					result.set("checkDate", true);
					param.put("msg", pop.getProperty("L170M01a.error4"));
					throw new CapMessageException(RespMsgHelper.getMessage("EFD0025", param), getClass());
				}
			}
			if (lastRetrialDate != null) {
				if (Integer.parseInt(TWNDate.toAD(lastRetrialDate).replace("-",
						"")) > Integer.parseInt(TWNDate.toAD(new Date())
						.replace("-", ""))) {
					result.set("check", false);
					result.set("checkDate", true);
					param.put("msg", pop.getProperty("L170M01a.error3"));
					throw new CapMessageException(RespMsgHelper.getMessage("EFD0025", param), getClass());
				}
			}

			if (Util.isEmpty(crdType)) {
				result.set("check", false);
				// EFD0005=ERROR|$\{colName\}此欄位不可空白|(信用評等)
				param.put("colName", pop.getProperty("L170M01a.credit"));
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0005", param), getClass());
			} else if (!crdType.equals(UtilConstants.crdType.未評等)) {
				if (Util.isEmpty(grade)) {
					result.set("check", false);
					// EFD0005=ERROR|$\{colName\}此欄位不可空白| (信用評等)
					param.put("colName", pop.getProperty("L170M01a.creditGrid"));
					throw new CapMessageException(RespMsgHelper.getMessage("EFD0005", param), getClass());
				}
			} else {
				result.set("check", true);
			}

			if (Util.isEmpty(crdTypeMow)) {
				result.set("check", false);
				// EFD0005=ERROR|$\{colName\}此欄位不可空白|(信用評等)
				param.put("colName", pop.getProperty("L170M01a.gradeTyp2"));
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0005", param), getClass());
			} else if (!crdTypeMow.equals(UtilConstants.mowType.免辦)) {
				if (Util.isEmpty(mow)) {
					result.set("check", false);
					// EFD0005=ERROR|$\{colName\}此欄位不可空白|(信用評等)
					param.put("colName",
							pop.getProperty("L170M01a.gradeTyp2MOW"));
					throw new CapMessageException(RespMsgHelper.getMessage("EFD0005", param), getClass());
				}
			} else {
				result.set("check", true);
			}

			if (Util.isEmpty(excrdType)) {
				result.set("check", false);
				// EFD0005=ERROR|$\{colName\}此欄位不可空白|(信用評等)
				param.put(
						"colName",
						pop.getProperty("L170M01a.ex")
								+ pop.getProperty("L170M01a.credit"));
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0005", param), getClass());
			} else if (!excrdType.equals(UtilConstants.crdType.未評等)
					&& !excrdType.equals(UtilConstants.Type.無資料_C)) {
				if (Util.isEmpty(exgrade)) {
					result.set("check", false);
					// EFD0005=ERROR|$\{colName\}此欄位不可空白| (信用評等)
					param.put(
							"colName",
							pop.getProperty("L170M01a.ex")
									+ pop.getProperty("L170M01a.creditGrid"));
					throw new CapMessageException(RespMsgHelper.getMessage("EFD0005", param), getClass());
				}
			} else {
				result.set("check", true);
			}

			if (Util.isEmpty(excrdTypeMow)) {
				result.set("check", false);
				// EFD0005=ERROR|$\{colName\}此欄位不可空白|(信用評等)
				param.put(
						"colName",
						pop.getProperty("L170M01a.ex")
								+ pop.getProperty("L170M01a.gradeTyp2"));
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0005", param), getClass());
			} else if (!excrdTypeMow.equals(UtilConstants.mowType.免辦)
					&& !excrdTypeMow.equals(UtilConstants.Type.無資料_M)) {
				if (Util.isEmpty(exmow)) {
					result.set("check", false);
					// EFD0005=ERROR|$\{colName\}此欄位不可空白|(信用評等)
					param.put(
							"colName",
							pop.getProperty("L170M01a.ex")
									+ pop.getProperty("L170M01a.gradeTyp2MOW"));
					throw new CapMessageException(RespMsgHelper.getMessage("EFD0005", param), getClass());
				}
			} else {
				result.set("check", true);
			}

			if (retrialDate != null && !Util.isEmpty(grade)
					&& !Util.isEmpty(crdType)) {
				result.set("check", true);
			}
			break;
		case 4:

			String formL170m01d = params.getString("L170M01dForm");
			JSONObject jobjectD = JSONObject.fromObject(formL170m01d);
			JSONArray itemNo = jobjectD.getJSONArray("itemNo");
			String chkPreReview = jobjectD.getString("chkPreReview");

			if (Util.equals("", Util.nullToSpace(l170m01a.getRptId()))) {
				for (int i = 0; i < itemNo.size(); i++) {
					L170M01D l170m01d = lms1705service.findL170m01dByMainId(
							mainId, custId, dupNo,
							Util.trim(itemNo.getString(i)));

					if (i == 18) {
						// 前次覆審項目檢核 chkPreReview
						if ("B014".equals(l170m01d.getItemNo())) {
							if (chkPreReview == null || "".equals(chkPreReview)) {
								result.set("check", false);
								// ERROR|請選擇第19項是否有改善，並且輸入說明|
								throw new CapMessageException(RespMsgHelper.getMessage("EFD3008", param), getClass());

							} else {
								result.set("check", true);
							}

						}
					}
				}
			} else {
				List<L170M01D> l170m01dList = lms1705service
						.findL170m01dByMainId(mainId);
				if (Util.equals("N",
						SimpleContextHolder.get(EloanConstants.TEMPSAVE_RUN))) {
					String msg = lms170502Service.checkS04Data(l170m01dList);
					if (Util.isNotEmpty(msg)) {
						result.set("check", false);
						throw new CapMessageException(msg, getClass());
					}
				}
			}
			break;
		}

		return result;

	}

	/**
	 * TempSave 切換頁籤自動儲存
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult tempSave(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		result = lms170502Service.tempSave(params);
		return result;
	}

	/**
	 * 查詢主管人員清單
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@DomainAuth(AuthType.Modify)
	public IResult queryBoss(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 查詢所選銀行的甲級主管、乙級主管清單
		SignEnum[] signs = { SignEnum.首長, SignEnum.單位主管, SignEnum.甲級主管,
				SignEnum.乙級主管 };
		Map<String, String> bossList = userInfoService.findByBrnoAndSignId(
				user.getUnitNo(), signs);
		result.set("bossList", new CapAjaxFormResult(bossList));
		return result;
	}

	/**
	 * <pre>
	 * 查詢 授信案件覆審報告表
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 * </pre>
	 */

	@DomainAuth(value = AuthType.Query, CheckDocStatus = true)
	public IResult queryL170m01a(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		// MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		int page = Util.parseInt(params.getString("page"));
		L170M01A l170m01a = lms1705service.findModelByMainId(L170M01A.class,
				mainId);
		L170M01G l170m01gL1 = lms1705service.findL170m01gByBranchTypeStaffJob(
				mainId, lrsConstants.BRANCHTYPE.覆審單位,
				UtilConstants.STAFFJOB.分行單位主管L6);
		if (l170m01gL1 == null)
			l170m01gL1 = new L170M01G();
		L170M01G l170m01gL4 = lms1705service.findL170m01gByBranchTypeStaffJob(
				mainId, lrsConstants.BRANCHTYPE.受檢單位,
				UtilConstants.STAFFJOB.執行覆核主管L4);
		if (l170m01gL4 == null)
			l170m01gL4 = new L170M01G();
		if (l170m01a == null)
			l170m01a = new L170M01A();
		String kind = l170m01a.getTypCd();
		RetrialDocStatusEnum docStatusEnum = RetrialDocStatusEnum.getEnum(Util
				.trim(l170m01a.getDocStatus()));
		if (Util.isEmpty(l170m01a.getUpdater())) {
			l170m01a.setUpdateTime(null);
		}
		result = DataParse.toResult(l170m01a);
		String docStatus = docStatusEnum.name();
		// 信用評等
		String crdType = "";
		String grade = "";
		// 信用內部風險
		String crdTypeMow = "";
		String mow = "";
		Map<String, String> CRDTypeMap = null;
		StringBuffer str = new StringBuffer();
		StringBuffer hiddenData = new StringBuffer();
		// 前次
		String excrdType = "";
		String exgrade = "";
		String excrdTypeMow = "";
		String exmow = "";
		StringBuffer exstr = new StringBuffer();
		StringBuffer exhiddenData = new StringBuffer();

		result.set("branchNo", l170m01a.getOwnBrId());
		result.set("branchName", branch.getBranchName(l170m01a.getOwnBrId()));
		result.set("custId1", l170m01a.getCustId());
		result.set("dupNo1", l170m01a.getDupNo());
		result.set("custName1", l170m01a.getCustName());
		result.set("typCd",
				Util.nullToSpace(TypCdEnum.getEnum(kind).toString()));
		// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		result.set("ctlType", l170m01a.getCtlType());
		result.set("typCd1",
				Util.nullToSpace(TypCdEnum.getEnum(kind).toString()));
		result.set("docStatus", docStatus);
		// 經辦
		result.set("appraiser", lmsService.getUserName(l170m01a.getUpdater()));
		// 複審人員
		result.set("approver", lmsService.getUserName(l170m01a.getApprover()));
		result.set("creator", Util.nullToSpace(userInfoService
				.getUserName(l170m01a.getCreator())));
		result.set("updater", Util.nullToSpace(userInfoService
				.getUserName(l170m01a.getUpdater())));

		// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		result.set("ctlType", Util.trim(l170m01a.getCtlType()));

		switch (page) {
		case 1:
			List<L170M01E> l170m01elist = lms1705service.findL170m01eByMainId(
					mainId, "T");
			CRDTypeMap = codetypeService.findByCodeType("CRDType");
			// items = codetypeService.findByCodeType("lms1705s01_crdType2");
			for (L170M01E l170m01e : l170m01elist) {
				if (UtilConstants.crdType.DBU大型企業.equals(Util.trim(l170m01e
						.getCrdType()))
						|| UtilConstants.crdType.DBU中小型企業.equals(Util
								.trim(l170m01e.getCrdType()))
						|| UtilConstants.crdType.海外.equals(Util.trim(l170m01e
								.getCrdType()))
						|| UtilConstants.crdType.未評等.equals(Util.trim(l170m01e
								.getCrdType()))) {
					crdType = Util.trim(l170m01e.getCrdType());
					grade = Util.trim(l170m01e.getGrade());
				} else if (UtilConstants.crdTypeC.泰國GroupA.equals(Util
						.trim(l170m01e.getCrdType()))
						|| UtilConstants.crdTypeC.泰國GroupB.equals(Util
								.trim(l170m01e.getCrdType()))
						|| UtilConstants.crdTypeC.自訂.equals(Util.trim(l170m01e
								.getCrdType()))
						|| UtilConstants.crdTypeC.消金評等.equals(Util
								.trim(l170m01e.getCrdType()))
						|| UtilConstants.crdTypeN.Moody.equals(Util
								.trim(l170m01e.getCrdType()))
						|| UtilConstants.crdTypeN.中華信評.equals(Util
								.trim(l170m01e.getCrdType()))
						|| UtilConstants.crdTypeN.SP.equals(Util.trim(l170m01e
								.getCrdType()))
						|| UtilConstants.crdTypeN.Fitch.equals(Util
								.trim(l170m01e.getCrdType()))
						|| UtilConstants.crdTypeN.FitchTW.equals(Util
								.trim(l170m01e.getCrdType()))
						|| UtilConstants.crdTypeN.KBRA.equals(Util
								.trim(l170m01e.getCrdType()))) {
					// //J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等
					if (str.length() != 0) {
						str.append("<BR/>");
						hiddenData.append("|");
					}
					str.append(
							Util.nullToSpace(TWNDate.toAD(l170m01e
									.getCrdTYear())))
							.append(" ")
							.append(Util.nullToSpace(CRDTypeMap.get(Util
									.trim(l170m01e.getCrdType()))))
							.append(" ")
							.append(Util.nullToSpace(Util.trim(l170m01e
									.getGrade())));
					hiddenData
							.append(Util.nullToSpace(Util.trim(l170m01e
									.getCrdType())))
							.append("^")
							.append(Util.nullToSpace(Util.trim(l170m01e
									.getGrade())))
							.append("^")
							.append(Util.nullToSpace(l170m01e.getCntrNo()))
							.append("^")
							.append(Util.nullToSpace(TWNDate.toAD(l170m01e
									.getCrdTYear()))).append("^")
							.append(Util.nullToSpace(l170m01e.getFinYear()));

				} else {
					crdTypeMow = Util.trim(l170m01e.getCrdType());
					mow = Util.trim(l170m01e.getGrade());
				}
			}

			List<L170M01E> exL170m01elist = lms1705service
					.findL170m01eByMainId(mainId, "L");
			Map<String, String> CrdType = codetypeService
					.findByCodeType("lms1705s01_crdType");
			for (L170M01E exLl170m01e : exL170m01elist) {
				if (Util.isNotEmpty(Util.nullToSpace(CrdType.get(Util
						.trim(exLl170m01e.getCrdType()))))
						|| Util.equals(UtilConstants.Type.無資料_C,
								Util.trim(exLl170m01e.getCrdType()))) {
					excrdType = Util.trim(exLl170m01e.getCrdType());
					exgrade = Util.trim(exLl170m01e.getGrade());
				} else if (UtilConstants.crdTypeC.泰國GroupA.equals(Util
						.trim(exLl170m01e.getCrdType()))
						|| UtilConstants.crdTypeC.泰國GroupB.equals(Util
								.trim(exLl170m01e.getCrdType()))
						|| UtilConstants.crdTypeC.自訂.equals(Util
								.trim(exLl170m01e.getCrdType()))
						|| UtilConstants.crdTypeC.消金評等.equals(Util
								.trim(exLl170m01e.getCrdType()))
						|| UtilConstants.crdTypeN.Moody.equals(Util
								.trim(exLl170m01e.getCrdType()))
						|| UtilConstants.crdTypeN.中華信評.equals(Util
								.trim(exLl170m01e.getCrdType()))
						|| UtilConstants.crdTypeN.SP.equals(Util
								.trim(exLl170m01e.getCrdType()))
						|| UtilConstants.crdTypeN.Fitch.equals(Util
								.trim(exLl170m01e.getCrdType()))
						|| UtilConstants.crdTypeN.FitchTW.equals(Util
								.trim(exLl170m01e.getCrdType()))
						|| UtilConstants.crdTypeN.KBRA.equals(Util
								.trim(exLl170m01e.getCrdType()))) {

					// J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等
					if (exstr.length() != 0) {
						exstr.append("<BR/>");
						exhiddenData.append("|");
					}
					exstr.append(
							Util.nullToSpace(TWNDate.toAD(exLl170m01e
									.getCrdTYear())))
							.append(" ")
							.append(Util.nullToSpace(CRDTypeMap.get(Util
									.trim(exLl170m01e.getCrdType()))))
							.append(" ")
							.append(Util.nullToSpace(Util.trim(exLl170m01e
									.getGrade())));
					exhiddenData
							.append(Util.nullToSpace(Util.trim(exLl170m01e
									.getCrdType())))
							.append("^")
							.append(Util.nullToSpace(Util.trim(exLl170m01e
									.getGrade())))
							.append("^")
							.append(Util.nullToSpace(exLl170m01e.getCntrNo()))
							.append("^")
							.append(Util.nullToSpace(TWNDate.toAD(exLl170m01e
									.getCrdTYear()))).append("^")
							.append(Util.nullToSpace(exLl170m01e.getFinYear()));
				} else {
					excrdTypeMow = Util.trim(exLl170m01e.getCrdType());
					exmow = Util.trim(exLl170m01e.getGrade());
				}
			}
			// 信用評等
			result.set("crdType", Util.nullToSpace(crdType));
			result.set("grade", Util.nullToSpace(grade));
			// 信用內部風險
			result.set("crdTypeMow", Util.nullToSpace(crdTypeMow));
			result.set("mow", Util.nullToSpace(mow));
			// 外部評等信用風險
			result.set("crdTypeCN", str.toString());
			result.set("crdTypeHiddenCN", hiddenData.toString());
			result.set("staffJobL1", Util.nullToSpace(userInfoService
					.getUserName(Util.nullToSpace(l170m01gL1.getStaffNo()))));
			result.set("staffJobL4", Util.nullToSpace(userInfoService
					.getUserName(Util.nullToSpace(l170m01gL4.getStaffNo()))));
			// 前次
			result.set("excrdType", Util.nullToSpace(excrdType));
			result.set("exgrade", Util.nullToSpace(exgrade));
			result.set("excrdTypeMow", Util.nullToSpace(excrdTypeMow));
			result.set("exmow", Util.nullToSpace(exmow));
			result.set("excrdTypeCN", exstr.toString());
			result.set("excrdTypeHiddenCN", exhiddenData.toString());
			break;
		case 2:
			result.set("totBalCurr", Util.nullToSpace(l170m01a.getTotBalCurr()));
			if (l170m01a.getTotBal() != null) {
				result.set("totBal", NumConverter.addComma(
						LMSUtil.toBigDecimal(l170m01a.getTotBal()).divide(
								new BigDecimal(1000)), "#,##0.00"));
			} else {
				result.set("totBal", "0.00");
			}
			if (l170m01a.getTotQuota() != null) {
				result.set("totQuota", NumConverter.addComma(
						LMSUtil.toBigDecimal(l170m01a.getTotQuota()).divide(
								new BigDecimal(1000)), "#,##0.00"));
			} else {
				result.set("totQuota", "0.00");
			}
			break;
		case 3:
			L170M01C l170m01c = lms1705service.findModelByMainId(
					L170M01C.class, l170m01a.getMainId());
			if (l170m01c == null) {
				l170m01c = new L170M01C();
				l170m01c.setMainId(l170m01a.getMainId());
				l170m01c.setCustId(l170m01a.getCustId());
				l170m01c.setDupNo(l170m01a.getDupNo());
				// 預設值
				l170m01c.setRatioNo1(UtilConstants.ratioNo.負債比率);
				l170m01c.setRatioNo2(UtilConstants.ratioNo.流動比率);
				l170m01c.setRatioNo3(UtilConstants.ratioNo.速動比率);
				l170m01c.setRatioNo4(UtilConstants.ratioNo.固定長期適合率);
				lms1705service.save(l170m01c);
			}
			Map<String, String> unitMap = codetypeService
					.findByCodeType("lms1205s01_Unit");
			if (unitMap == null)
				unitMap = new LinkedHashMap<String, String>();
			JSONObject data = DataParse.toJSON(l170m01c, L170M01C.class,
					"#,##0.00");
			result = new CapAjaxFormResult(data);
			result.set("typCd1",
					Util.nullToSpace(TypCdEnum.getEnum(kind).toString()));
			result.set("l170m01c_curr", Util.trim(l170m01c.getCurr()));
			// result.set("l170m01c.unit",
			// Util.nullToSpace(unitMap.get(Util.trim(l170m01c.getUnit()))));
			result.set("l170m01c_unit", Util.trim(l170m01c.getUnit()));
			result.set("custName1", l170m01a.getCustName());
			result.set("custId1", l170m01a.getCustId());
			result.set("dupNo1", l170m01a.getDupNo());
			result.set("No1", l170m01c.getRatioNo1());
			result.set("No2", l170m01c.getRatioNo2());
			result.set("No3", l170m01c.getRatioNo3());
			result.set("No4", l170m01c.getRatioNo4());

			break;
		case 4:
			result.set("custName4", l170m01a.getCustName());
			result.set("custId4", l170m01a.getCustId());
			result.set("dupNo4", l170m01a.getDupNo());
			result.set("rptId", Util.nullToSpace(l170m01a.getRptId()));
			break;

		case 5:

			List<L170M01G> l170m01gList = lms1705service
					.findL170m01gByMainId(mainId);
			L170M01F l170m01f = lms1705service.findModelByMainId(
					L170M01F.class, mainId);
			if (l170m01f != null) {
				l170m01f.setBranchComm(Util.trim(l170m01f.getBranchComm()));
				result = DataParse.toResult(l170m01f);
			}
			for (L170M01G l170m01g : l170m01gList) {
				result.set(
						Util.nullToSpace(l170m01g.getBranchType()) + "staffJob"
								+ Util.nullToSpace(l170m01g.getStaffJob()),
						Util.nullToSpace(l170m01g.getStaffName()));
			}
			result.set("typCd1",
					Util.nullToSpace(TypCdEnum.getEnum(kind).toString()));
			result.set("updater", Util.nullToSpace(userInfoService
					.getUserName(l170m01a.getUpdater())));
			result.set("custName1", l170m01a.getCustName());
			result.set("custId1", l170m01a.getCustId());
			result.set("dupNo1", l170m01a.getDupNo());
			break;
		default:
		}
		result.set(EloanConstants.MAIN_OID,
				CapString.trimNull(l170m01a.getOid()));
		return result;
	};

	/**
	 * <pre>
	 * 查詢  一般/團貸覆審項目檔 (查詢覆審項目是否有變更)
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = true)
	public IResult queryL170m01d(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		result = lms170502Service.queryL170m01d(params);
		return result;
	};

	/**
	 * 查詢幣別select
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryMoney(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		// 幣別
		Map<String, String> moneyMap = codetypeService
				.findByCodeType("Common_Currcy");
		TreeMap moneyMapT = new TreeMap(moneyMap);
		CapAjaxFormResult money = new CapAjaxFormResult(moneyMapT);
		result.set("moneyItem", money);
		return result;
	}

	/**
	 * 搜尋分行
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 **/
	public IResult queryBranch(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		TreeMap<String, String> tm = retrialService.getBranch(user.getUnitNo());

		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set("item", new CapAjaxFormResult(tm));
		result.set("itemOrder", new ArrayList<String>(tm.keySet()));
		return result;
	}

	/**
	 * 更新覆審控制檔
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult updateElf412(PageParameters params)
			throws CapException {
		String mainId = params.getString(EloanConstants.MAIN_ID, "");
		L170M01A l170m01a = null;
		List<L170M01E> l170m01eList = null;
		CapAjaxFormResult result = new CapAjaxFormResult();
		HashMap<String, String> param = new HashMap<String, String>();
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1705S01Panel.class);
		try {
			l170m01a = lms1705service.findModelByMainId(L170M01A.class, mainId);
			if (l170m01a == null)
				l170m01a = new L170M01A();
			l170m01eList = lms1705service.findL170m01eByMainId(mainId, "T");
			if (Util.isEmpty(l170m01a.getRetrialDate())) {
				result.set("check", false);
				// EFD0005=ERROR|$\{colName\}此欄位不可空白|(覆審日期)
				param.put("colName", pop.getProperty("L170M01a.retrialDate"));
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0005", param), getClass());
			}

			if (l170m01eList.size() > 0) {
				boolean checkL170M01EResult = false;
				for (L170M01E l170m01e : l170m01eList) {
					if (!checkL170M01EResult) {
						if ("NA".equals(Util.trim(l170m01e.getCrdType()))
								|| "G".equals(Util.trim(l170m01e.getCrdType()))) {
							checkL170M01EResult = true;
						} else if (Util.trim(l170m01e.getCrdType()).startsWith(
								"N")) {

						} else if ("".equals(Util.trim(l170m01e.getCrdType()))) {

						} else {
							if ("".equals(Util.trim(l170m01e.getGrade()))) {
								result.set("check", false);
								// EFD0005=ERROR|$\{colName\}此欄位不可空白| (信用評等)
								param.put("colName",
										pop.getProperty("L170M01a.credit"));
								throw new CapMessageException(RespMsgHelper.getMessage("EFD0005", param), getClass());
							}
							checkL170M01EResult = true;
						}
					}
				}
				if (!checkL170M01EResult) {
					result.set("check", false);
					// EFD0005=ERROR|$\{colName\}此欄位不可空白|(信用評等)
					param.put("colName", pop.getProperty("L170M01a.credit"));
					throw new CapMessageException(RespMsgHelper.getMessage("EFD0005", param), getClass());
				}
			} else {
				result.set("check", false);
				// EFD0005=ERROR|$\{colName\}此欄位不可空白|(信用評等)
				param.put("colName", pop.getProperty("L170M01a.credit"));
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0005", param), getClass());
			}

			result = lms170502Service.updateElf412(params);
			// 由於從service無法使用this.getComponent() 所以從service存到result
			// 當回到formhandler再進行處理
			this.HandlerResultCode(result, "WARMCODE");
			this.HandlerResultCode(result, "SUCCESSCODE");
			this.HandlerErrorCode(result);
		} finally {

		}
		return result;
	}

	/**
	 * 引進基本資料(主要借款人,負責人,信用評等 queryCreditData,保證人,主要授信戶) (1)
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = true)
	public IResult queryCreditBase(PageParameters params)
			throws CapException {
		String lms412Error = "";
		String mapcharminError = "";
		String guarantorError = "";

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		L170M01A l170m01a = lms1705service.findModelByMainId(L170M01A.class,
				mainId);
		if (l170m01a == null)
			l170m01a = new L170M01A();
		String custId = l170m01a.getCustId();
		String dupNo = l170m01a.getDupNo();

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String brNo = user.getUnitNo();

		// 搜尋LMS.LMS412 引進主要借款人,重覆序號,客戶名稱
		Map<String, Object> dataMapBase = lms1705service
				.findMisByCustIdDupNoBranch(custId, dupNo, brNo);
		if (dataMapBase != null) {

			// 借款人統一編號(FROM LMS412)
			result.set("custId", (String) dataMapBase.get("custId"));
			// 客戶名稱(FROM CUSTDATA)
			List<Map<String, Object>> list = lmsCustdataService
					.findCustDataCname(custId, dupNo);
			Map<String, Object> custDataMap = null;
			if (!Util.isEmpty(list) && list.size() > 0) {
				custDataMap = list.get(0);
			}
			if (custDataMap != null) {
				if (!Util.isEmpty(custDataMap)) {
					result.set("custName", Util.trim(custDataMap.get("CNAME")));
				}
			}

			// 主要授信戶(FROM LMS412)
			// J-111-0326 海外覆審作業系統改良第一階段：
			// 4-2. 新增 符合授信額度標準
			// 原 mLoanPerson主要戶 改 mLoanPerson符合授信額度標準、mLoanPersonA主要戶
			result.set("mLoanPersonA",
					Util.nullToSpace(dataMapBase.get("maincust")));

			// 信用評等(FROM LMS412)
			String crdtype = Util.nullToSpace(dataMapBase.get("crdtype"));
			// 評等等級
			String crdttbl = Util.nullToSpace(Util.trim(dataMapBase
					.get("crdttbl")));

			// (信用風險)模型評等類別 (FROM LMS412)
			String mowtype = Util.nullToSpace(dataMapBase.get("mowtype"));
			// 內部評等
			String mowtbl1 = Util.nullToSpace(dataMapBase.get("mowtbl1"));
			if ("Z".equals(mowtype)) {
				mowtype = UtilConstants.mowType.免辦;
				mowtbl1 = "";
			}

			// LMS.L170M01E(DB=DBU大型企業,DL=DBU中小型企業,OU=0BU)
			// LMS.LMS412(B=DBU、大型企業、L=DBU中小型企業、O=OBU)

			// 信評表類別
			if (crdtype != null) {
				if ("O".equals(crdtype)) {
					crdtype = UtilConstants.crdType.海外;
				} else if ("B".equals(crdtype)) {
					crdtype = UtilConstants.crdType.DBU大型企業;
				} else if ("Z".equals(crdtype)) {
					crdtype = UtilConstants.crdType.未評等;
					crdttbl = "";
				}
			}
			result.set("crdType", crdtype);
			if (Util.isEmpty(crdtype)) {
				crdttbl = "";
			}
			result.set("grade", crdttbl);

			// 1:DBU大型企業,2:DBU中型企業,3:DBU中小型企業,4:DBU不動產有建案規劃,5:DBU專案融資
			// 6:DBU本國證券公司,8:DBU投資公司一般情況,9:DBU租賃公司,A:DBU一案建商,B:DBU非一案建商(擔保/土融)
			// C:DBU非一案建商(無擔),D:投資公司情況一,E:投資公司情況二
			result.set("mowtype", mowtype);
			result.set("mowtbl1", mowtbl1);

		} else {
			// EFD0037=INFO|覆審控制檔查無資料!|
			lms412Error = "N";

		}

		// 引進負責人
		Map<String, Object> dataMapcharmin = lms1705service
				.findCharminByCustIdDupNoBranch(custId, dupNo, brNo);
		if (dataMapcharmin != null && dataMapcharmin.size() > 0) {
			result.set("chairman",
					Util.nullToSpace(Util.trim(dataMapcharmin.get("sup1cnm"))));

			// System.out.println(dataMapcharmin.get("ecocd"));
			result.set("tradeType",
					Util.nullToSpace(dataMapcharmin.get("tradeType")));

			// J-111-0326 海外覆審作業系統改良第一階段： 10. 加行業別代碼
			result.set("busCd", Util.nullToSpace(dataMapcharmin.get("busCd")));
			result.set("bussKind",
					Util.nullToSpace(dataMapcharmin.get("bussKind")));
		} else {
			// EFD0038=INFO|查無負責人資料!|
			mapcharminError = "N";
		}

		String cname = null;

		Object[] temp = lms1705service.getGuarantor(custId, dupNo, mainId);
		String getResult = null;
		if (temp.length >= 2) {
			getResult = Util.trim(temp[0]);
			cname = Util.trim(temp[1]);
		}
		if ("X".equals(getResult)) {
			// 查保證人無資料!
			guarantorError = "N";
		} else if ("N".equals(getResult)) {
			// EFD3003=INFO|請先引進一般授信資料|
			guarantorError = "IN";
		} else {
			result.set("rltGuarantor", Util.trim(cname));
		}

		if ("N".equals(lms412Error) && "N".equals(guarantorError)
				&& "N".equals(mapcharminError)) {
			// 覆審控制檔,保證人,負責人 皆無資料
			result.set("error", "1");
		} else if ("N".equals(lms412Error) && "IN".equals(guarantorError)
				&& "N".equals(mapcharminError)) {
			// 覆審控制檔,保證人(引進一般授信資料),負責人 皆無資料
			result.set("error", "2");
		} else if ("N".equals(guarantorError) && "N".equals(mapcharminError)) {
			// 保證人,負責人 皆無資料
			result.set("error", "3");
		} else if ("IN".equals(guarantorError) && "N".equals(mapcharminError)) {
			// 保證人(引進一般授信資料),負責人 皆無資料
			result.set("error", "4");
		} else if ("N".equals(lms412Error) && "N".equals(mapcharminError)) {
			// 覆審控制檔,負責人 皆無資料
			result.set("error", "5");
		} else if ("N".equals(lms412Error) && "N".equals(guarantorError)) {
			// 覆審控制檔,保證人 皆無資料
			result.set("error", "6");
		} else if ("N".equals(lms412Error) && "IN".equals(guarantorError)) {
			// 覆審控制檔無資料,保證人(引進一般授信資料)
			result.set("error", "7");
		} else if ("N".equals(guarantorError)) {
			// 保證人無資料
			result.set("error", "8");
		} else if ("IN".equals(guarantorError)) {
			// 保證人(引進一般授信資料)
			result.set("error", "9");
		} else if ("N".equals(mapcharminError)) {
			// 負責人 無資料
			result.set("error", "10");
		} else if ("N".equals(lms412Error)) {
			// 覆審控制檔無資料
			result.set("error", "11");
		} else {
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0017"));
		}

		exCreditData(params, result); // 引前次評等資訊

		return result;

	}

	/**
	 * 重新引進負責人 (2)
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("rawtypes")
	public IResult queryCharmin(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		L170M01A l170m01a = lms1705service.findModelByMainId(L170M01A.class,
				mainId);
		String custId = l170m01a.getCustId();
		String dupNo = l170m01a.getDupNo();

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String brNo = user.getUnitNo();
		// (2).負責人
		Map dataMapcharmin = lms1705service.findCharminByCustIdDupNoBranch(
				custId, dupNo, brNo);
		if (dataMapcharmin != null && dataMapcharmin.size() > 0) {
			String chairman = Util.trim((String) dataMapcharmin.get("sup1cnm"));
			String tradeType = Util.trim((String) dataMapcharmin
					.get("tradeType"));
			// J-111-0326 海外覆審作業系統改良第一階段： 10. 加行業別代碼
			String busCd = Util.trim((String) dataMapcharmin.get("busCd"));
			String bussKind = Util.nullToSpace(dataMapcharmin.get("bussKind"));
			result.set("chairman", chairman);
			result.set("tradeType", tradeType);
			// J-111-0326 海外覆審作業系統改良第一階段： 10. 加行業別代碼
			result.set("busCd", busCd);
			result.set("bussKind", bussKind);
		} else {
			// EFD0036=INFO|查無資料!|
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0036"));

		}

		return result;
	}

	/**
	 * <pre>
	 * 引進信用評等資料(L170M01A) (3)
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 * </pre>
	 */
	@SuppressWarnings("unused")
	@DomainAuth(value = AuthType.Query, CheckDocStatus = true)
	public IResult queryCreditData(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		// [評等方式]:1.信用評等 2.信用風險內部評等(只有國內) 3.國際信評 4.當地信評 5.全部免辦
		String gradeType = params.getString("gradeType", "");
		String mainId = params.getString(EloanConstants.MAIN_ID);
		L170M01A l170m01a = lms1705service.findModelByMainId(L170M01A.class,
				mainId);
		String dupNo = l170m01a.getDupNo();
		String custId = l170m01a.getCustId();

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String OvUnitNo = user.getUnitNo();
		String crdType = "";
		String grade = "";
		String crdTypeMow = "";
		String mow = "";
		String crdtyearStr = "";
		int gradeTypeNumber = Integer.parseInt(gradeType);
		JSONArray ja = new JSONArray();
		List<L170M01E> l170m01eList = null;
		List<Map<String, Object>> elf338nList = null;
		List<Map<String, Object>> elfmow1YList = null;
		List<Map<String, Object>> elfmow1NList = null;
		Map<String, String> crdTypeMap = null;
		boolean page3Result = false;

		StringBuffer str = new StringBuffer();
		StringBuffer hiddenData = new StringBuffer();
		// 先刪除L170M01E裡有mainId的資料
		// service.deleteL170m01eList(mainId);
		switch (gradeTypeNumber) {
		// 信用評等
		case 1:
			// 客戶統一編號+重複序號+評等單位+評等表類別
			elf338nList = lms1705service.findElf338nByType(custId, dupNo,
					OvUnitNo, UtilConstants.crdType.DBU大型企業.toString(),
					UtilConstants.crdType.DBU中小型企業.toString(),
					UtilConstants.crdType.海外.toString(), "", "", "");
			for (Map<String, Object> map : elf338nList) {
				String tempCrdType = Util.nullToSpace(map
						.get("ELF338N_CRDTYPE"));
				String tempGrade = Util.nullToSpace(map.get("ELF338N_GRADE"));
				if (UtilConstants.crdType.DBU大型企業.equals(tempCrdType)
						|| UtilConstants.crdType.DBU中小型企業.equals(tempCrdType)
						|| UtilConstants.crdType.海外.equals(tempCrdType)) {
					crdType = Util.trim(tempCrdType);
					grade = Util.trim(tempGrade);
				}
			}
			if (!"".equals(crdType)) {
				if ("Z".equals(crdType)) {
					crdType = UtilConstants.crdType.未評等;
					grade = "";
				}
				result.set("crdType", Util.trim(crdType));
				result.set("grade", Util.trim(grade));
				// 信用風險內部評等:免辦8
				result.set("crdTypeMow", UtilConstants.mowType.免辦);
				result.set("mow", "");
			} else {
				// EFD0036=INFO|查無資料!|
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0036"));
			}
			break;
		// 信用風險內部評等
		case 2:

			elfmow1YList = lms1705service.findElfmow1ByType(custId, dupNo, "Y");
			elfmow1NList = lms1705service.findElfmow1ByType(custId, dupNo, "N");
			for (Map<String, Object> map : elfmow1YList) {
				crdTypeMow = Util.nullToSpace(Util.trim(map.get("MOWTYPE")));
				mow = Util.nullToSpace(Util.trim(map.get("FR")));
			}
			for (Map<String, Object> map : elfmow1NList) {
				crdTypeMow = Util.nullToSpace(Util.trim(map.get("MOWTYPE")));
				mow = Util.nullToSpace(Util.trim(map.get("FR")));
			}
			if (!"".equals(crdTypeMow)) {
				if ("Z".equals(crdTypeMow)) {
					crdTypeMow = UtilConstants.mowType.免辦;
					mow = "";
				}
				result.set("crdTypeMow", Util.trim(crdTypeMow));
				result.set("mow", mow);
				result.set("crdType", UtilConstants.crdType.未評等);
				result.set("grade", "");
			} else {
				// EFD0036=INFO|查無資料!|
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0036"));
			}
			break;
		case 3:
			page3Result = true;
		case 4:
			crdTypeMap = codetypeService.findByCodeType("CRDType");
			if (crdTypeMap == null)
				crdTypeMap = new LinkedHashMap<String, String>();
			if (!page3Result) {
				// 客戶統一編號+重複序號+評等單位+評等表類別
				// J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等
				elf338nList = lms1705service.findElf338nByType(custId, dupNo,
						OvUnitNo, UtilConstants.crdTypeC.泰國GroupA.toString(),
						UtilConstants.crdTypeC.泰國GroupB.toString(),
						UtilConstants.crdTypeC.自訂.toString(),
						UtilConstants.crdTypeC.消金評等.toString(), "", "");

			} else {
				// J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等
				String fitchTW = retrialService
						.isFcrdTypeEffect(UtilConstants.crdTypeN.FitchTW
								.toString()) ? UtilConstants.crdTypeN.FitchTW
						.toString() : "";
				String kbra = retrialService
						.isFcrdTypeEffect(UtilConstants.crdTypeN.KBRA
								.toString()) ? UtilConstants.crdTypeN.KBRA
						.toString() : "";
				elf338nList = lms1705service.findElf338nByType(custId, dupNo,
						OvUnitNo, UtilConstants.crdTypeN.Moody.toString(),
						UtilConstants.crdTypeN.SP.toString(),
						UtilConstants.crdTypeN.Fitch.toString(),
						UtilConstants.crdTypeN.中華信評.toString(), fitchTW, kbra);
			}
			for (Map<String, Object> map : elf338nList) {
				if (str.length() != 0) {
					str.append("<BR/>");
					hiddenData.append("|");
				}
				str.append(
						Util.nullToSpace(Util.trim(map.get("ELF338N_CRDTYEAR"))))
						.append(" ")
						.append(Util.nullToSpace(crdTypeMap.get(Util.trim(map
								.get("ELF338N_CRDTYPE")))))
						.append(" ")
						.append(Util.nullToSpace(Util.trim(map
								.get("ELF338N_GRADE"))));
				hiddenData
						.append(Util.nullToSpace(Util.trim(map
								.get("ELF338N_CRDTYPE"))))
						.append("^")
						.append(Util.nullToSpace(Util.trim(map
								.get("ELF338N_GRADE"))))
						.append("^")
						.append(Util.nullToSpace(Util.trim(map
								.get("ELF338N_CNTRNO"))))
						.append("^")
						.append(Util.nullToSpace(Util.trim(map
								.get("ELF338N_CRDTYEAR"))))
						.append("^")
						.append(Util.nullToSpace(Util.trim(map
								.get("ELF338N_FINYEAR"))));
			}

			// str.append("2012/02/01").append(" ")
			// .append(Util.nullToSpace(crdTypeMap.get("CA")))
			// .append(" E");
			// hiddenData.append(Util.nullToSpace(Util.trim("CA"))).append("^")
			// .append(Util.nullToSpace(Util.trim("E"))).append("^")
			// .append(Util.nullToSpace("00000000")).append("^")
			// .append("2012/02/01").append("^")
			// .append(Util.nullToSpace(""));
			// str.append("<BR/>").append("2012/02/01 ")
			// .append(Util.nullToSpace(crdTypeMap.get("CB")))
			// .append(" F");
			// hiddenData.append("|").append(Util.nullToSpace(Util.trim("CB")))
			// .append("^").append(Util.nullToSpace(Util.trim("F")))
			// .append("^").append(Util.nullToSpace("00000000"))
			// .append("^").append("2012/02/01").append("^")
			// .append(Util.nullToSpace(""));

			if (str.length() != 0) {
				result.set("crdTypeCN", str.toString());
				result.set("crdTypeHiddenCN", hiddenData.toString());
				result.set("NMSFC", "Y");
			} else {
				// EFD0036=INFO|查無資料!|
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0036"));
			}
			break;
		case 5:
			// 信用評等:未評等
			result.set("crdType", UtilConstants.crdType.未評等);
			result.set("grade", "");
			// 信用風險內部評等:免辦8
			result.set("crdTypeMow", UtilConstants.mowType.免辦);
			result.set("mow", "");
			break;
		default:
			// 信用評等:未評等
			result.set("crdType", UtilConstants.crdType.未評等);
			result.set("grade", "");
			// 信用風險內部評等:免辦8
			result.set("crdTypeMow", UtilConstants.mowType.免辦);
			result.set("mow", "");
			break;
		}

		// 都須重引前次評等資訊
		exCreditData(params, result);

		return result;
	};

	/**
	 * 引進主要授信戶 (4)
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("rawtypes")
	public IResult queryMaincust(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = params.getString(EloanConstants.MAIN_ID);
		L170M01A l170m01a = lms1705service.findModelByMainId(L170M01A.class,
				mainId);
		String dupNo = l170m01a.getDupNo();
		String custId = l170m01a.getCustId();

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String brNo = user.getUnitNo();

		// (4).主要授信戶
		Map dataMapMaincust = lms1705service.finLms412ByCustIdAndDupNo(custId,
				dupNo, brNo);
		if (!Util.isEmpty(dataMapMaincust)) {
			String maincust = (String) dataMapMaincust.get("maincust");
			if (!Util.isEmpty(maincust)) {
				// J-111-0326 海外覆審作業系統改良第一階段：
				// 4-2. 新增 符合授信額度標準
				// 原 mLoanPerson主要戶 改 mLoanPerson符合授信額度標準、mLoanPersonA主要戶
				result.set("mLoanPersonA",
						(String) dataMapMaincust.get("maincust"));
				// EFD0018=INFO|執行成功|
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0018"));
			} else {
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0036"));
			}

		} else {
			// EFD0036=INFO|查無資料!|
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0036"));
		}

		return result;
	}

	/**
	 * 重新引進保證人 (5) 保證人 rltGuarantor
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	public IResult queryRltGuarantor(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);

		L170M01A l170m01a = lms1705service.findModelByMainId(L170M01A.class,
				mainId);
		String dupNo = l170m01a.getDupNo();
		String custId = l170m01a.getCustId();
		String getResult = null;
		// 先判斷L170M01B是否有資料
		List<L170M01B> l170m01blis = lms1705service.findL170m01bList(mainId);
		String str = "";
		List<String> cntrNoList = new LinkedList<String>();
		for (L170M01B l170m01b : l170m01blis) {
			cntrNoList.add(Util.trim(l170m01b.getCntrNo()));
		}

		Object[] temp = lms1705service.getGuarantor(custId, dupNo, mainId);
		if (temp.length >= 2) {
			getResult = Util.trim(temp[0]);
			str = Util.trim(temp[1]);
		}
		if ("X".equals(getResult)) {
			// EFD0036=INFO|查無資料!|
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0036"));
		} else if ("N".equals(getResult)) {
			// EFD3003=INFO|請先引進一般授信資料|
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD3003"));
		} else {
			result.set("rltGuarantor", str);
		}
		return result;
	}

	/**
	 * <pre>
	 * 查詢一般授信資料(單筆)thickbox內容
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = true)
	public IResult queryCredit(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		// 查詢授信資料內容(L170M01B)
		String oid = params.getString(EloanConstants.MAIN_OID);
		L170M01B l170m01b = lms1705service.findModelByOid(L170M01B.class, oid);
		BigDecimal thsNumber = new BigDecimal(1000);
		if (l170m01b != null) {
			if (l170m01b.getBalAmt() != null) {
				l170m01b.setBalAmt(l170m01b.getBalAmt().divide(thsNumber)
						.setScale(2, BigDecimal.ROUND_HALF_UP));
			}
			if (l170m01b.getQuotaAmt() != null) {
				l170m01b.setQuotaAmt(l170m01b.getQuotaAmt().divide(thsNumber)
						.setScale(2, BigDecimal.ROUND_HALF_UP));
			}
			if (l170m01b.getEstAmt() != null) {
				l170m01b.setEstAmt(l170m01b.getEstAmt().divide(thsNumber)
						.setScale(2, BigDecimal.ROUND_HALF_UP));
			}
			if (l170m01b.getLoanAmt() != null) {
				l170m01b.setLoanAmt(l170m01b.getLoanAmt().divide(thsNumber)
						.setScale(2, BigDecimal.ROUND_HALF_UP));
			}
			result = DataParse.toResult(l170m01b);
			if (l170m01b.getLnDataDate() == null) {
				result.set("lnDataDateResult", "N");
			} else {
				result.set("lnDataDateResult", "Y");
			}
		}
		return result;
	};

	/**
	 * <pre>
	 * 引進最近三次財務及業務資料檔
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 * </pre>
	 */

	@DomainAuth(value = AuthType.Query, CheckDocStatus = true)
	public IResult queryFinance(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		L170M01A l170m01a = lms1705service.findModelByMainId(L170M01A.class,
				mainId);
		if (l170m01a == null)
			l170m01a = new L170M01A();
		String custId = l170m01a.getCustId();
		String dupNo = l170m01a.getDupNo();

		String[] mainIdYearlist = params.getStringArray("list");
		String[] ratioNolist = params.getStringArray("ratioNo");

		String[] yearList = new String[mainIdYearlist.length];
		String[] mainIdList = new String[mainIdYearlist.length];
		int i = 0;
		for (String temp : mainIdYearlist) {
			yearList[i] = temp.split("-")[1];
			i++;
		}
		i = 0;
		Arrays.sort(yearList);

		String tmp = "";
		for (String checkYear : yearList) {
			if (!"".equals(tmp)) {
				if (tmp.equals(checkYear)) {
					// 所選財報年度不可重複
					throw new CapMessageException(getMessage("lms1705.check3"),
							this.getClass());
				}
			}
			tmp = checkYear;
		}
		for (String temp : yearList) {
			for (String temp2 : mainIdYearlist) {
				if (temp.equals(temp2.split("-")[1])) {
					mainIdList[i] = temp2.split("-")[0];
					i++;
				}
			}
		}

		L170M01C l170m01c = (L170M01C) lms1705service.findModelByMainId(
				L170M01C.class, mainId);
		if (l170m01c == null) {
			l170m01c = lms170502Service.setC170M01CDefault(mainId, custId,
					dupNo,
					"".equals(Util.trim(l170m01a.getTotBalCurr())) ? branch
							.getBranch(Util.trim(l170m01a.getOwnBrId()))
							.getUseSWFT() : l170m01a.getTotBalCurr(), "", "",
					"", "");
		}
		l170m01c.setAmt11(null);
		l170m01c.setAmt12(null);
		l170m01c.setAmt13(null);
		l170m01c.setAmt21(null);
		l170m01c.setAmt22(null);
		l170m01c.setAmt23(null);
		l170m01c.setAmt31(null);
		l170m01c.setAmt32(null);
		l170m01c.setAmt33(null);

		// J-107-0045-001 Web e-Loan企金授信簽報書配合海外啟用IFRS徵信報告調整財報引進相關功能。
		l170m01c.setRatioNo1(null);
		l170m01c.setRatioNo2(null);
		l170m01c.setRatioNo3(null);
		l170m01c.setRatioNo4(null);

		l170m01c.setRate11(null);
		l170m01c.setRate12(null);
		l170m01c.setRate13(null);
		l170m01c.setRate14(null);
		l170m01c.setRate21(null);
		l170m01c.setRate22(null);
		l170m01c.setRate23(null);
		l170m01c.setRate24(null);
		l170m01c.setRate31(null);
		l170m01c.setRate32(null);
		l170m01c.setRate33(null);
		l170m01c.setRate34(null);
		l170m01c.setEndDate1(null);
		l170m01c.setEndDate2(null);
		l170m01c.setEndDate3(null);
		l170m01c.setFromDate1(null);
		l170m01c.setFromDate2(null);
		l170m01c.setFromDate3(null);
		l170m01c.setRateDate1(null);
		l170m01c.setRateDate2(null);
		l170m01c.setRateDate3(null);
		l170m01c.setRatioNo1(null);
		l170m01c.setRatioNo2(null);
		l170m01c.setRatioNo3(null);
		l170m01c.setRatioNo4(null);
		l170m01c.setUnit(null);
		l170m01c.setCurr(null);
		l170m01c = lms1705service.findF101s01ABymainId(mainId, mainIdList,
				custId, dupNo, l170m01c, l170m01a);
		l170m01c = lms1705service.findF101S01BBymainId(mainId, mainIdList,
				custId, dupNo, ratioNolist, l170m01c);
		lms1705service.save(l170m01c);
		// 從帳務資料給予mainId查詢L170M01C並引進三筆資料

		JSONObject data = DataParse
				.toJSON(l170m01c, L170M01C.class, "#,##0.00");
		result = new CapAjaxFormResult(data);

		result.set("No1", l170m01c.getRatioNo1());
		result.set("No2", l170m01c.getRatioNo2());
		result.set("No3", l170m01c.getRatioNo3());
		result.set("No4", l170m01c.getRatioNo4());
		result.set("l170m01c_oid", Util.trim(l170m01c.getOid()));
		// result.set("RateDate1",
		// l170m01c.getRateDate1());
		// result.set("fromDate1",
		// l170m01c.getFromDate1());
		// result.set("endDate1",
		// l170m01c.getEndDate1());
		// result.set("ratioNo1", l170m01c.getRatioNo1());
		// result.set("ratioNo2", l170m01c.getRatioNo2());
		// result.set("ratioNo3", l170m01c.getRatioNo3());
		// result.set("ratioNo4", l170m01c.getRatioNo4());
		// result.set("rate11",
		// String.valueOf(l170m01c.getRate11()));
		// result.set("rate12",
		// String.valueOf(l170m01c.getRate12()));
		// result.set("rate13",
		// String.valueOf(l170m01c.getRate13()));
		// result.set("rate14",
		// String.valueOf(l170m01c.getRate14()));
		// result.set("amt11",
		// String.valueOf(l170m01c.getAmt11()));
		// result.set("amt12",
		// String.valueOf(l170m01c.getAmt12()));
		// result.set("amt13",
		// String.valueOf(l170m01c.getAmt13()));
		// result.set("RateDate2",
		// l170m01c.getRateDate1());
		// result.set("fromDate2",
		// l170m01c.getFromDate1());
		// result.set("endDate2",
		// l170m01c.getEndDate1());
		// result.set("ratioNo1", l170m01c.getRatioNo1());
		// result.set("ratioNo2", l170m01c.getRatioNo2());
		// result.set("ratioNo3", l170m01c.getRatioNo3());
		// result.set("ratioNo4", l170m01c.getRatioNo4());
		// result.set("rate21",
		// String.valueOf(l170m01c.getRate21()));
		// result.set("rate22",
		// String.valueOf(l170m01c.getRate22()));
		// result.set("rate23",
		// String.valueOf(l170m01c.getRate23()));
		// result.set("rate24",
		// String.valueOf(l170m01c.getRate24()));
		// result.set("amt21",
		// String.valueOf(l170m01c.getAmt21()));
		// result.set("amt22",
		// String.valueOf(l170m01c.getAmt22()));
		// result.set("amt23",
		// String.valueOf(l170m01c.getAmt23()));
		// result.set("RateDate3",
		// l170m01c.getRateDate3());
		// result.set("fromDate3",
		// l170m01c.getFromDate3());
		// result.set("endDate3",
		// l170m01c.getEndDate3());
		// result.set("ratioNo1", l170m01c.getRatioNo1());
		// result.set("ratioNo2", l170m01c.getRatioNo2());
		// result.set("ratioNo3", l170m01c.getRatioNo3());
		// result.set("ratioNo4", l170m01c.getRatioNo4());
		// result.set("rate31",
		// String.valueOf(l170m01c.getRate31()));
		// result.set("rate32",
		// String.valueOf(l170m01c.getRate32()));
		// result.set("rate33",
		// String.valueOf(l170m01c.getRate33()));
		// result.set("rate34",
		// String.valueOf(l170m01c.getRate34()));
		// result.set("amt31",
		// String.valueOf(l170m01c.getAmt31()));
		// result.set("amt32",
		// String.valueOf(l170m01c.getAmt32()));
		// result.set("amt33",
		// String.valueOf(l170m01c.getAmt33()));
		return result;
	};

	/**
	 * <pre>
	 * 產生所有授信資料
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult addCredit(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		result = lms170502Service.addCredit(params);
		// 由於從service無法使用this.getComponent() 所以從service存到result
		// 當回到formhandler再進行處理
		this.HandlerResultCode(result, "WARMCODE");
		this.HandlerResultWarmMsg(result);
		return result;
	}

	/**
	 * <pre>
	 * 刪除所有授信資料
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult deleteCredit(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		result = lms170502Service.deleteCredit(params);
		this.HandlerResultCode(result, "SUCCESSCODE");
		this.HandlerResultWarmMsg(result);
		return result;
	}

	/**
	 * <pre>
	 * 刪除選取的授信資料(不包含外部引進的)
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult deleteChkCredit(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		result = lms170502Service.deleteChkCredit(params);
		this.HandlerResultCode(result, "SUCCESSCODE");
		this.HandlerResultWarmMsg(result);
		return result;
	}

	/**
	 * <pre>
	 * 取得登入分行名
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult queryOvUnitCName(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 取得登入所在分行名稱
		result.set("OvUnitCName", user.getUnitCName());
		return result;

	}

	/**
	 * <pre>
	 * 儲存受檢單位洽辦情形
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult save1705M5(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String form = params.getString("lms1705s05BOX");
		JSONObject json = JSONObject.fromObject(form);
		String strText = json.getString("lms1705s05BOXtext");
		String mainId = params.getString(EloanConstants.MAIN_ID);
		L170M01A l170m01a = lms1705service.findModelByMainId(L170M01A.class,
				mainId);
		L170M01F l170m01f = lms1705service.findModelByMainId(L170M01F.class,
				mainId);

		if (l170m01f == null) {
			l170m01f = new L170M01F();
			l170m01f.setMainId(l170m01a.getMainId());
			l170m01f.setCustId(l170m01a.getCustId());
			l170m01f.setDupNo(l170m01a.getDupNo());

		}
		l170m01f.setBranchComm(strText);
		DataParse.toBean(form, l170m01f);
		lms1705service.save(l170m01f);
		result = DataParse.toResult(l170m01f);
		if (params.getAsBoolean("showMsg", true)) {
			// EFD0017=INFO|儲存成功|
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0017"));
		}
		result.set(EloanConstants.MAIN_OID, l170m01a.getOid());
		return result;

	};

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult querylms1705s05BOX(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		L170M01F l170m01f = lms1705service.findModelByMainId(L170M01F.class,
				mainId);

		if (l170m01f != null) {
			result.set("branchComm", Util.trim(l170m01f.getBranchComm()));
		}
		return result;

	};

	/**
	 * <pre>
	 * 新增 覆審報告表
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult addL170m01a(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		try {
			result = lms170502Service.addL170m01a(params);
			this.HandlerErrorCode(result);
		} catch (Exception e) {
			this.HandlerErrorCode(result);
		}
		return result;

	};

	/**
	 * <pre>
	 * 儲存(單筆)一般授信資料
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(AuthType.Modify)
	public IResult saveL170m01b(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		result = lms170502Service.saveL170m01b(params);
		this.HandlerResultCode(result, "SUCCESSCODE");
		this.HandlerResultWarmMsg(result);
		return result;
	}

	/**
	 * <pre>
	 * 儲存(全部標籤內容)
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(AuthType.Modify)
	public IResult saveAll(PageParameters params)
			throws CapException {
		boolean showMsg = params.getBoolean("showMsg");
		CapAjaxFormResult result = new CapAjaxFormResult();
		result = lms170502Service.saveAll(params);
		if (showMsg) {
			this.HandlerResultCode(result, "SUCCESSCODE");
		} else {
			if (Util.isNotEmpty(Util.nullToSpace(result.get("IncompleteMsg")))) {
				throw new CapMessageException(result.get("IncompleteMsg")
						.toString(), getClass());
			}
		}
		return result;
	}// ;

	/**
	 * <pre>
	 * 刪除
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 * </pre>
	 */

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteListL170m01a(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] oids = params.getStringArray("oids");
		if (oids.length > 0) {
			if (lms1705service.deleteL170m01aList(oids)) {
				// EFD0019=INFO|刪除成功|
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0019"));
			}
		}

		return result;
	};

	/**
	 * <pre>
	 * 撈CMS擔保品的資料回來
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = true)
	public IResult reQueryL170m01bCMSData(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID, "");
		String cntrNo = params.getString("cntrNo", "");
		L170M01A l170m01a = lms1705service.findModelByMainId(L170M01A.class,
				mainId);
		if (l170m01a == null) {
			l170m01a = new L170M01A();
		}
		Map<String, Object> map = lms1705service.getCMSData(l170m01a, cntrNo);
		result.set("guaranteeName", Util.trim(map.get("guaranteeName")));
		result.set("loanCurr", Util.trim(map.get("loanCurr")));
		result.set("estCurr", Util.trim(map.get("estCurr")));
		result.set("loanAmt", NumConverter.addComma(LMSUtil
				.nullToZeroBigDecimal(map.get("loanAmt"))));
		result.set("estAmt", NumConverter.addComma(LMSUtil
				.nullToZeroBigDecimal(map.get("estAmt"))));
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0018"));
		result.set("insMemo", Util.trim(map.get("insMemo")));
		return result;
	};

	/**
	 * <pre>
	 * 呈主管覆核(呈主管 主管覆核 拆2個method)
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(AuthType.Modify + AuthType.Accept)
	public IResult flowAction(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapWebUtil.showParams(params);

		String oid = params.getString(EloanConstants.MAIN_OID, "");
		String mainId = params.getString(EloanConstants.MAIN_ID, "");
		String managerId = params.getString("managerId", "");
		String flowActionResult = params.getString("flowActionResult", "N");
		String flowSeq = params.getString("flowSeq", "");
		String checkFlowUpdaterResult = params.getString(
				"checkFlowUpdaterResult", "Y");

		HashMap<String, Object> data = new HashMap<String, Object>();
		data.put("mainId", mainId);
		data.put("flowActionResult", flowActionResult);
		data.put("managerId", managerId);
		data.put("oid", oid);
		data.put("mainOid", oid);
		if ("Y".equals(checkFlowUpdaterResult)) {
			L170M01A l170m01a = lms1705service.findModelByOid(L170M01A.class,
					oid);
			if ("Y".equals(flowActionResult)
					&& user.getUserId().equals(l170m01a.getUpdater())) {
				// EFD0053=WARN|覆核人員不可與「經辦人員或其它覆核人員」為同一人|
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0053"), getClass());
			}
		}

		if ("2".equals(flowSeq)) {
			data.put("result", "Y".equals(flowActionResult) ? "通過" : "退回");
		} else if ("4".equals(flowSeq)) {
			data.put("result", "Y".equals(flowActionResult) ? "核定" : "退回");
		}
		// if
		// (RetrialDocStatusEnum.編製中.getCode().equals(l170m01a.getDocStatus()))
		// {
		// }else if
		// (RetrialDocStatusEnum.待受檢單位回覆.getCode().equals(l170m01a.getDocStatus()))
		// {
		// }else if
		// (RetrialDocStatusEnum.編製完成.getCode().equals(l170m01a.getDocStatus()))
		// {
		// }else if
		// (RetrialDocStatusEnum.待覆核.getCode().equals(l170m01a.getDocStatus()))
		// {
		// }else if
		// (RetrialDocStatusEnum.已核准.getCode().equals(l170m01a.getDocStatus()))
		// {
		// }
		lms1705service.flowAction(oid, user, data);

		return result;
	}// ;

	/**
	 * 處理從service回傳回來的訊息
	 * 
	 * @param result
	 * @param code
	 */
	private void HandlerResultCode(CapAjaxFormResult result, String code) {
		if (result.containsKey(code)) {
			String msg = null;
			HashMap<String, String> param = new HashMap<String, String>();
			if (!"".equals(Util.nullToSpace(result.get("retrialDate")))) {
				param.put("retrialDate",
						Util.nullToSpace(result.get("retrialDate")));
				msg = RespMsgHelper.getMessage(Util.nullToSpace(result.get(code)), param);
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, msg);
			} else if (Util.isNotEmpty(Util.nullToSpace(result
					.get("IncompleteMsg")))) {
				msg = Util.nullToSpace(result.get(code));
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(msg));
				result.set(
						CapConstants.AJAX_NOTIFY_MESSAGE,
						result.get(CapConstants.AJAX_NOTIFY_MESSAGE)
								+ "<BR/><BR/>"
								+ Util.nullToSpace(result.get("IncompleteMsg")));
			} else {
				msg = Util.nullToSpace(result.get(code));
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(msg));
			}

		}
	}

	/**
	 * 處理從service回傳回來的訊息
	 * 
	 * @param result
	 * @param code
	 * @throws CapMessageException
	 */
	private void HandlerErrorCode(CapAjaxFormResult result)
			throws CapMessageException {
		if (result.containsKey("ERRORCODE")) {
			HashMap<String, String> param = new HashMap<String, String>();
			param.put("colName", Util.nullToSpace(result.get("colName")));
			param.put("msg", Util.nullToSpace(result.get("msg")));
			throw new CapMessageException(RespMsgHelper.getMessage(Util.nullToSpace(result.get("ERRORCODE")), param),
					getClass());
		}
	}

	/**
	 * 處理從service回傳回來的訊息
	 * 
	 * @param result
	 * @param code
	 */
	private void HandlerResultWarmMsg(CapAjaxFormResult result) {
		if (result.containsKey("WARMMSG")) {
			if (result.containsKey("SUCCESSCODE")) {
				result.set(
						CapConstants.AJAX_NOTIFY_MESSAGE,
						result.get(CapConstants.AJAX_NOTIFY_MESSAGE)
								+ "<BR/><BR/>"
								+ Util.nullToSpace(result.get("WARMMSG")));
			} else {
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
						Util.nullToSpace(result.get("WARMMSG")));
			}

		}
	}

	/**
	 * <pre>
	 * 引進前次信用評等資料
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 * </pre>
	 */
	@SuppressWarnings("unused")
	@DomainAuth(value = AuthType.Query, CheckDocStatus = true)
	public IResult exCreditData(PageParameters params,
			CapAjaxFormResult result) throws CapException {
		String mainId = params.getString(EloanConstants.MAIN_ID);
		L170M01A l170m01a = lms1705service.findModelByMainId(L170M01A.class,
				mainId);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String excrdType = "";
		String exgrade = "";
		String exmowtype = "";
		String exmowtbl1 = "";
		StringBuffer exstr = new StringBuffer();
		StringBuffer exhiddenData = new StringBuffer();
		Map<String, String> CRDTypeF = codetypeService
				.findByCodeType("CRDType");
		Map<String, String> CrdType = codetypeService
				.findByCodeType("lms1705s01_crdType");

		// 取得最新取得之前次資料
		String exMainid = eloandbBASEService.findL170M01A_exMainid(
				l170m01a.getMainId(), l170m01a.getCustId(),
				l170m01a.getDupNo(), l170m01a.getOwnBrId());
		if (Util.isNotEmpty(exMainid)) {
			List<L170M01E> exL170m01elist_L = lms1705service
					.findL170m01eByMainId(exMainid, "T");
			if (!exL170m01elist_L.isEmpty()) {
				for (L170M01E exLl170m01e_L : exL170m01elist_L) {
					if (Util.isNotEmpty(Util.nullToSpace(CrdType.get(Util
							.trim(exLl170m01e_L.getCrdType()))))
							|| Util.equals(UtilConstants.Type.無資料_C,
									Util.trim(exLl170m01e_L.getCrdType()))) {
						if (!"".equals(Util.nullToSpace(Util.trim(exLl170m01e_L
								.getCrdType())))) {
							excrdType = Util.trim(exLl170m01e_L.getCrdType());
							exgrade = Util.trim(exLl170m01e_L.getGrade());
						}
					} else if (UtilConstants.crdTypeC.泰國GroupA.equals(Util
							.trim(exLl170m01e_L.getCrdType()))
							|| UtilConstants.crdTypeC.泰國GroupB.equals(Util
									.trim(exLl170m01e_L.getCrdType()))
							|| UtilConstants.crdTypeC.自訂.equals(Util
									.trim(exLl170m01e_L.getCrdType()))
							|| UtilConstants.crdTypeC.消金評等.equals(Util
									.trim(exLl170m01e_L.getCrdType()))
							|| UtilConstants.crdTypeN.Moody.equals(Util
									.trim(exLl170m01e_L.getCrdType()))
							|| UtilConstants.crdTypeN.中華信評.equals(Util
									.trim(exLl170m01e_L.getCrdType()))
							|| UtilConstants.crdTypeN.SP.equals(Util
									.trim(exLl170m01e_L.getCrdType()))
							|| UtilConstants.crdTypeN.Fitch.equals(Util
									.trim(exLl170m01e_L.getCrdType()))
							|| UtilConstants.crdTypeN.FitchTW.equals(Util
									.trim(exLl170m01e_L.getCrdType()))
							|| UtilConstants.crdTypeN.KBRA.equals(Util
									.trim(exLl170m01e_L.getCrdType()))) {
						// J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等
						if (exstr.length() != 0) {
							exstr.append("<BR/>");
							exhiddenData.append("|");
						}
						exstr.append(
								Util.nullToSpace(TWNDate.toAD(exLl170m01e_L
										.getCrdTYear())))
								.append(" ")
								.append(Util.nullToSpace(CRDTypeF.get(Util
										.trim(exLl170m01e_L.getCrdType()))))
								.append(" ")
								.append(Util.nullToSpace(Util
										.trim(exLl170m01e_L.getGrade())));
						exhiddenData
								.append(Util.nullToSpace(Util
										.trim(exLl170m01e_L.getCrdType())))
								.append("^")
								.append(Util.nullToSpace(Util
										.trim(exLl170m01e_L.getGrade())))
								.append("^")
								.append(Util.nullToSpace(Util
										.trim(exLl170m01e_L.getCntrNo())))
								.append("^")
								.append(Util.nullToSpace(TWNDate
										.toAD(exLl170m01e_L.getCrdTYear())))
								.append("^")
								.append(Util.nullToSpace(Util
										.trim(exLl170m01e_L.getFinYear())));
					} else {
						if (!"".equals(Util.nullToSpace(Util.trim(exLl170m01e_L
								.getCrdType())))) {
							exmowtype = Util.trim(exLl170m01e_L.getCrdType());
							exmowtbl1 = Util.trim(exLl170m01e_L.getGrade());
						}
					}
				}
			}
		} else {
			excrdType = UtilConstants.Type.無資料_C;
			exgrade = "";
			exmowtype = UtilConstants.Type.無資料_M;
			exmowtbl1 = "";
		}

		result.set("excrdType", excrdType);
		result.set("exgrade", exgrade);
		result.set("exmowtype", exmowtype);
		result.set("exmowtbl1", exmowtbl1);
		result.set("excrdTypeCN", exstr.toString());
		result.set("excrdTypeHiddenCN", exhiddenData.toString());

		return result;
	}

	/**
	 * J-108-0260 海外覆審檢視表
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = true)
	public IResult queryL170m01i(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		L170M01A l170m01a = lms1705service.findModelByMainId(L170M01A.class,
				mainId);
		L170M01I l170m01i = lms1705service.findModelByMainId(L170M01I.class,
				mainId);
		if (l170m01i == null)
			l170m01i = new L170M01I();
		result = DataParse.toResult(l170m01i);

		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1705S04Panel.class);
		String reviewType = ""; // 取得該案複審內容版本
		reviewType = lms1705service.getReviewType(l170m01a);

		// 取得項目
		HashMap<String, String> chklist = new HashMap<String, String>();
		chklist = lms1705service.getChkList(reviewType);

		// 取得項目文字
		String[] spanContent = new String[6];
		spanContent = retrialService.getChkListSpan(reviewType, chklist, prop);
		result.set("first", spanContent[0]);
		result.set("second", spanContent[1]);
		result.set("third", spanContent[2]);
		result.set("fourth", spanContent[3]);
		result.set("fifth", spanContent[4]);

		return result;
	}

	@DomainAuth(AuthType.Modify)
	public IResult saveL170m01i(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		L170M01A l170m01a = lms1705service.findModelByMainId(L170M01A.class,
				mainId);
		result = lms170502Service.saveL170m01i(params, l170m01a);
		L170M01I l170m01i = lms1705service.findModelByMainId(L170M01I.class,
				mainId);
		String checkData = lms170502Service.chkL170m01i(l170m01i);

		if (checkData.length() == 0) {
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));
		} else {
			result.set("msg", checkData);
		}

		return result;
	}
}
