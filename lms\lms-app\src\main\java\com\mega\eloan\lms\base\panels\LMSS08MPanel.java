/* 
 * LMSS08MPanel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.panels;

import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * J-112-0357 相關文件-敘做條件異動比較表
 * </pre>
 * 
 * @since 2023/08
 * <AUTHOR>
 * @version <ul>
 *          <li>2023/08,009301,new
 *          </ul>
 */
public class LMSS08MPanel extends Panel {

	private static final long serialVersionUID = -4024257163623646201L;

	public LMSS08MPanel(String id) {
		super(id);
	}
	
}
