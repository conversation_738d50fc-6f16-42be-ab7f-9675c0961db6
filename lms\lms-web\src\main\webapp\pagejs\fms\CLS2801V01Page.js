$(function(){	
	var my_colModel = [{
        colHeader: "",name: 'oid', hidden: true			//ROWID  
    }, {
    	colHeader: "",name: 'mainId', hidden: true		//文件編號
    }, {
    	colHeader: "",name: 'seq', hidden: true			//序號
    }, {
        colHeader: i18n.cls2801v01["L140MM2A.custId"], //借款戶統一編號
        align: "left", width: 100, sortable: true, name: 'custId',
        formatter: 'click', onclick: openDoc
    }, {
        colHeader: i18n.cls2801v01["L140MM2A.custName"], //借款戶名稱
        align: "left", width: 100, sortable: true, name: 'custName'
    }, {
        colHeader: i18n.cls2801v01["L140MM2A.cntrNo"], //額度序號
        align: "left", width: 100, sortable: true, name: 'cntr<PERSON>o'
    }, {
        colHeader: i18n.cls2801v01["L140S02L.hold_no"], //毀損住宅所有權人戶籍號碼
        align: "left", width: 180, sortable: false, name: 'hold_no'
    }, {
        colHeader: i18n.cls2801v01["L140S02L.owner_id"], //毀損住宅所有權人統一編號
        align: "left", width: 180, sortable: false, name: 'owner_id' 
    }];
	
	var grid = $("#gridview").iGrid({
        handler: "cls2801gridhandler",
        height: 350,
        rowNum: 15,
        shrinkToFit: false,
        postData: {
            formAction: "queryMain",
            docStatus: viewstatus,
            ownBrId: userInfo.unitNo
        },
        colModel: my_colModel,
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = grid.getRowData(rowid);
            openDoc(null, null, data);
        }
    });
    
    function openDoc(cellvalue, options, rowObject){
    	var postData = {
    			'mainOid': rowObject.oid, 
    			'mainId': rowObject.mainId,
    			'mainSeq': rowObject.seq,
    			'mainDocStatus': viewstatus
    		}
    	
    	if (typeof noOpenDoc != 'undefined' && noOpenDoc == 'Y'){
    		postData['noOpenDoc'] = true;
    	};
    	
		$.form.submit({
			url : '../fms/cls2801m01/01',
			data : postData,
			target : '_blank'
		});
    }
    
    
    function chose_custId(){	
		var my_dfd = $.Deferred();
		AddCustAction.open({
	    		handler: 'cls2801formhandler',
				action : 'echo_custId',
				data : {
	            },
				callback : function(json){					
	            	// 關掉 AddCustAction 的 
	            	$.thickbox.close();					
					my_dfd.resolve( json );					
				}
			});
		return my_dfd.promise();
	}
    
    function chose_cntrNo(resultFrom_chose_custId){
		var my_dfd = $.Deferred();		
		GetCntrnoGrid.jqGrid("setGridParam", {
            postData: {
                'custId': resultFrom_chose_custId.custId
				,'dupNo': resultFrom_chose_custId.dupNo
            },
            search: true
        }).trigger("reloadGrid");
		
		$("#GetCntrnoThickBox").thickbox({
	       title: i18n.cls2801m01["doc.title02"], width: 400,height: 450,align: "center",valign: "bottom",
           modal: false, i18n: i18n.def,
		   buttons: {
                "sure": function(){
					 var data = GetCntrnoGrid.getSingleData();
                     if (data) {
						 $.thickbox.close();
                    	 var cntrNo = data.cntrNo;
						 var sDate = data.sDate;
        				 my_dfd.resolve($.extend(resultFrom_chose_custId, {'cntrNo':cntrNo, 'sDate':sDate} ));
                     }     	
                },
                "cancel": function(){
                	$.thickbox.close();
                }
            }	
		});	
		
		return my_dfd.promise();
	}
	
    
    var GetCntrnoGrid = $('#GetCntrnoGrid').iGrid({
        handler: 'cls2801gridhandler', //設定handler
        height: 400, //設定高度
        action: 'queryGetCntrno', //執行的Method
        postData: {
            
        },
        needPager: false,
        rownumbers: true,
        colModel: [{
            colHeader: i18n.cls2801m01["L140MM2A.cntrNo"], // 額度序號
            align: "center",
            width: 100, //設定寬度
            sortable: true, //是否允許排序
            name: 'cntrNo'
        }, {
        	name: 'sDate',
        	hidden: true
    	}],
        loadComplete: function () {
        	
        	if( GetCntrnoGrid.getGridParam("records")>0){
        		
        	}else{
				var custId = GetCntrnoGrid.getGridParam("postData")['custId'];
				if(custId && custId.length>1){
					$.thickbox.close();
					API.showErrorMessage(i18n.cls2801m01["doc.cntrNoError"]);	
				}            		
        	}
        }  
    });
    
    $("#buttonPanel").find("#btnAdd").click(function(){
    	chose_custId().done(function(resultFrom_chose_custId){
   	 		chose_cntrNo(resultFrom_chose_custId).done(function(resultFrom_chose_cntrNo){

				$.ajax({
                    handler: "cls2801formhandler",
                    action : 'newl140mm2a',
					data : {
						custId:resultFrom_chose_cntrNo.custId,
						dupNo:resultFrom_chose_cntrNo.dupNo,
						custName:resultFrom_chose_cntrNo.custName,
						cntrNo:resultFrom_chose_cntrNo.cntrNo,
						sDate:resultFrom_chose_cntrNo.sDate                
   	 				},
                    success: function(obj){                   	
	        			$.form.submit({
                    		url: '../fms/cls2801m01/01',
                    		data: {
                        		formAction: "query",
                        		oid: obj.oid,
                        		mainOid: obj.oid,
                        		mainDocStatus: viewstatus,
                        		txCode: txCode
                    		},
                    		target: obj.oid
               	 		});
					}
	    		});
	    	});
	    });
    }).end().find("#btnDelete").click(function(){
    	var rows = grid.getGridParam('selrow');
        var list = "";
        if (rows != 'undefined' && rows != null && rows != 0) {
            var data = grid.getRowData(rows);
            list = data.oid;
        }
        if (list == "") {
            CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
            return;
        }
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                $.ajax({
                    handler: "cls2801formhandler",
                    type: "POST",
                    dataType: "json",
                    data: {
                        formAction: "deleteMark",
                        list: list,
                        docStatus: viewstatus
                    },
                    success: function(obj){
                    	if(obj.saveOkFlag){
                    		API.showMessage(i18n.def.runSuccess);//執行成功
                    	}
                    	grid.trigger("reloadGrid");
                    }
                });
            }
        });	
    });
});
