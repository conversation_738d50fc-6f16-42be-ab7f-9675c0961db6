var pageAction = {
    handler: 'cls8011m01formhandler',
    isGrid3: function(){
    	return (viewstatus =='03O');
    },
    build: function(){
        pageAction.grid = $("#gridview").iGrid({
            // localFirst: true,
            handler: 'cls8011gridhandler',
            height: 400,
            rowNum: 15,
            rownumbers: true,
            multiselect: true,
            sortname: "caseNo|custId",
            sortorder: "asc|asc",
            postData: {
                docStatus: viewstatus,
                docType:2,
                formAction: "queryViewData"
            },
            colModel: [{
                name: 'oid',
                hidden: true
            }, {
                name: 'mainId',
                hidden: true
            }, {
                name: 'dupNo',
                hidden: true            
            }, {
                name: 'docStatus',
                hidden: true
            }, {
                colHeader: i18n.cls8011v01["C801M01A.caseStatus"], //戶況
                align: "left",
                width: 8, // 設定寬度
                sortable: true, // 是否允許排序
                name: 'caseStatus',
                formatter: function(cellvalue, options, rowObject){
                    if (cellvalue == "1") {
                        return i18n.cls8011v01["C801M01A.caseStatus.1"];
                    }else if (cellvalue == "2") {
                    	return i18n.cls8011v01["C801M01A.caseStatus.2"];
                    }else if (cellvalue == "3") {
                    	return i18n.cls8011v01["C801M01A.caseStatus.3"];
                    }else if (cellvalue == "4") {
                    	return i18n.cls8011v01["C801M01A.caseStatus.4"];
                    }else{
                    	return cellvalue;
                    }
                } 
            }, {
                colHeader: i18n.cls8011v01["C801M01A.caseNo"], // C801M01A.caseNo=檔案編號
                align: "center",
                width: 10, // 設定寬度
                sortable: true, // 是否允許排序
                name: 'caseNo'    	
            }, {
                colHeader: i18n.cls8011v01["C801M01A.custId"], // C801M01A.custId=統一編號
                align: "left",
                width: 15, // 設定寬度
                sortable: true, // 是否允許排序
                name: 'custId',
                formatter: 'click',
				onclick: pageAction.openDoc
            }, {
                colHeader: i18n.cls8011v01["C801M01A.custName"], // C801M01A.custName=客戶名稱
                align: "left",
                width: 15, // 設定寬度
                sortable: true, // 是否允許排序
                name: 'custName'
            }, {
                colHeader: i18n.cls8011v01["C801M01A.cntrNo"], // C801M01A.cntrNo=額度序號
                align: "left",
                width: 15, // 設定寬度
                sortable: true, // 是否允許排序
                name: 'cntrNo'
            }, {
                colHeader: i18n.cls8011v01["C801M01A.loanNo"], // C801M01A.loanNo=放款帳號
                align: "left",
                width: 20, // 設定寬度
                sortable: true, // 是否允許排序
                name: 'loanNo'
            }, {
                colHeader: i18n.cls8011v01["C801M01A.creatorname"], // C801M01A.creatorname=分行經辦
                align: "left",
                width: 10,
                sortable: true,
                name: 'caseAppr'
            }, {
                colHeader: i18n.cls8011v01["C801M01A.approveTime"], //覆核日期
                align: "left",
                width: 11, // 設定寬度
                sortable: true, // 是否允許排序
                name: 'approveTime',
                hidden: (!pageAction.isGrid3())
            } 
            ],
            ondblClickRow: function(rowid){
                var data = pageAction.grid.getRowData(rowid);
                pageAction.openDoc(null, null, data);
            }
        });
        
		//先依照SIT畫面呈現
        $("#buttonPanel").find("#btnAdd").click(function(){
				API.showMessage(i18n.def.runSuccess);
			
//			var _id = "addBox";
//    		var _form = "addForm";
//    		if ($("#"+_id).length== 0){
//    			var dyna = [];
//    			dyna.push("<div id='"+_id+"' style='display:none'>");
//    			dyna.push("<form id='"+_form+"' name='"+_form+"'>");
//    			
//    			dyna.push("<p>");
//    		    dyna.push("<label><b>"+"企/個金案件"+"：</b></label>");   'i18n.cls8011v01["C801M01A.caseStatus"]'
//    		    dyna.push("<select id='docType' name='docType'>");
//    		    dyna.push("<option value=''>"+i18n.cls8011v01["C801M01A.caseStatus.all"]+"</option>");
//    		    dyna.push("<option value='1'>"+"企金"+"</option>");
//    		    dyna.push("<option value='2'>"+"個金"+"</option>");
//    		    dyna.push("</select>");
//    		    dyna.push("</p>");
//    		    
//    		    dyna.push("<br>");
//
//    		    dyna.push("</form>");
//    			dyna.push("</div>");
//    			
//    		    $('body').append(dyna.join(""));
// 
//    		}
//    		
//		  
//    		$("#"+_id).thickbox({
//	            title: '',
//	            width: 300,
//	            height: 120,
//	            valign: "bottom",
//	            align: "center",
//	            i18n: i18n.def,
//	            buttons: {
//	                'sure': function(){
//				        $.ajax({
//				             handler: pageAction.handler,
//				             data: {
//				                 formAction: "newc801m01a",
//								 docType : $("#"+_id).find("#docType").val()
//				             }
//				             }).done(function(){
//				            	 $("#gridview").trigger("reloadGrid");
//				            	 API.showMessage(i18n.def.runSuccess);
//				        });
//						$.thickbox.close();
//	                },
//	                'cancel': function(){
//	                    $.thickbox.close();
//	                }
//	            }
//	        });

			
        	
        }).end().find("#btnDelete").click(function(){
        	var rowId_arr = pageAction.grid.getGridParam('selarrrow');
    		var oids = [];
       	 	for (var i = 0; i < rowId_arr.length; i++) {
    			var data = pageAction.grid.getRowData(rowId_arr[i]);
    			oids.push(data.oid);    			
            }
       	 	
       	 	if(oids.length==0){   	 		
    	 		API.showMessage(i18n.def.action_005);//請先選取一筆以上之資料列
    	 		return;
    	 	}      
	       	$.ajax({
	             handler: pageAction.handler,
	             data: {
	                 formAction: "deleteC801m01a",
	                 oids: oids
	             }
	             }).done(function(){
	            	 $("#gridview").trigger("reloadGrid");
	        });
        }).end().find("#btnFilter").click(function(){
        	
        	var _id = "filterBox";
    		var _form = "filterForm";
    		if ($("#"+_id).length == 0){
    			var dyna = [];
    			dyna.push("<div id='"+_id+"' style='display:none'>");
    			dyna.push("<form id='"+_form+"' name='"+_form+"'>");
    			dyna.push("<input type='hidden' id='docType' name='docType' value='2'");
//				dyna.push("<p>");
//    		    dyna.push("<label><b>"+"企/個金案件"+"：</b></label>");   'i18n.cls8011v01["C801M01A.caseStatus"]'
//    		    dyna.push("<select id='docType' name='docType'>");
//    		    dyna.push("<option value=''>"+i18n.cls8011v01["C801M01A.caseStatus.all"]+"</option>");
//    		    dyna.push("<option value='1'>"+"企金"+"</option>");
//    		    dyna.push("<option value='2'>"+"個金"+"</option>");
//    		    dyna.push("</select>");
//    		    dyna.push("</p>"); 
//    		    dyna.push("<br>");
	            dyna.push("<p>");
    		    dyna.push("<label><b>"+i18n.cls8011v01["C801M01A.caseStatus"]+"：</b></label>");
    		    dyna.push("<select id='caseStatus' name='caseStatus'>");
    		    dyna.push("<option value=''>"+i18n.cls8011v01["C801M01A.caseStatus.all"]+"</option>");
    		    dyna.push("<option value='1'>"+i18n.cls8011v01["C801M01A.caseStatus.1"]+"</option>");
    		    dyna.push("<option value='2'>"+i18n.cls8011v01["C801M01A.caseStatus.2"]+"</option>");
    		    dyna.push("<option value='3'>"+i18n.cls8011v01["C801M01A.caseStatus.3"]+"</option>");
    		    dyna.push("<option value='4'>"+i18n.cls8011v01["C801M01A.caseStatus.4"]+"</option>");
    		    dyna.push("</select>");
    		    dyna.push("</p>");
    		    
    		    dyna.push("<p class='forDocStatus03O'>");
    		    dyna.push("<label><b>"+i18n.cls8011v01["C801M01A.approveTime"]+"：</b></label>");
    		    dyna.push("<input type='text' id='flt_approveTime_beg' name='flt_approveTime_beg' class='date'>");
    		    dyna.push("~");
    		    dyna.push("<input type='text' id='flt_approveTime_end' name='flt_approveTime_end' class='date'>");
    		    dyna.push("</p>");
    		    
    		    dyna.push("<p class='forDocStatus03O'>");
    		    dyna.push("<label><b>"+i18n.cls8011v01["C801M01A.creatorname"]+"：</b></label>");
    		    dyna.push("<select id='flt_caseApprId' name='flt_caseApprId'></select>");
    		    dyna.push("</p>");
    		    
    		    dyna.push("<br>");
    		    
    			dyna.push("<table><label><b>請輸入欲查詢紀錄：</b></label>");    		      
    		    dyna.push("<tbody>");
    		    dyna.push("<tr><td><label><input type='radio' name='queryData' value='1' />主要借款人統編</label>&nbsp;&nbsp;&nbsp;&nbsp;<label><input type='radio' name='queryData' value='2'/>額度序號</label></td></tr>");
	    		dyna.push("<tr id='queryDataTr1' class='queryData'><td><input id='custId' name='custId' type='text' size='14' maxlength='10' class=''/><span class='text-red'>ex:A123456789</span></td></tr>");
  				dyna.push("<tr id='queryDataTr2' class='queryData'><td><input id='cntrNo' name='cntrNo' type='text'size='14' maxlength='12' class='' /></td></tr>");
    		    dyna.push("</tbody>");
    		    dyna.push("</table>");
    		        		    
    		    dyna.push("</form>");
    			dyna.push("</div>");
    			
    		    $('body').append(dyna.join(""));
    		    if(true){
    	    		//在 function pageInit(...) 中，會針對 欄位 custId addClass upText
         		    pageInit.call( $("#"+_id) );
    		    }
    		     $("#"+_id).find('input[name=queryData]').click(function(){
		            var $fm = $("#"+_form);
		            $fm.find('.queryData').hide();
		            $fm.find('.queryData input').val('');
		            if ($(this).val() == '1') {
		                $fm.find('#queryDataTr1').show();
		            } else {
		                $fm.find('#queryDataTr2').show();		                
		            }
		        })
		        $("#"+_form).find('input[name=queryData]').filter("[value='1']").trigger('click').attr("checked", true);
    		     
				$("#flt_approveTime_beg").datepicker();
				$("#flt_approveTime_end").datepicker();
				
				
    		    $.ajax({
    		    	 	handler: pageAction.handler,
    		    	 	async: true,
	                    data: {formAction: "queryEmp" }
	                    }).done(function(json){
	                    	$("#flt_caseApprId").setItems({ item: json.staff_list, space: true, format:'{value} : {key}' });
	                    
	            });
    		}

 		    if(viewstatus=='03O'){
 		    	$(".forDocStatus03O").show();
 		    }else{
 		    	$(".forDocStatus03O").find("input[type=text]").val('');
 		    	$(".forDocStatus03O").find("select").val('');
 		    	$(".forDocStatus03O").hide();
 		    }
		  
    		$("#"+_id).thickbox({
	            title: '',
	            width: 520,
	            height: 340,
	            valign: "bottom",
	            align: "center",
	            i18n: i18n.def,
	            buttons: {
	                'sure': function(){
	                    var $form = $("#"+_form);
	                    if ($form.valid()) {
	                        $("#gridview").jqGrid("setGridParam",{postData: $form.serializeData(), page: 1}).trigger("reloadGrid");
	                        $.thickbox.close();
	                    }
	                },
	                'cancel': function(){
	                    $.thickbox.close();
	                }
	            }
	        });
		  
        }).end().find("#btnFCheck").click(function(){
        	var rowId_arr = pageAction.grid.getGridParam('selarrrow');
    		var oids = [];
       	 	for (var i = 0; i < rowId_arr.length; i++) {
    			var data = pageAction.grid.getRowData(rowId_arr[i]);
    			oids.push(data.oid);    			
            }
       	 	
       	 	if(oids.length==0){   	 		
    	 		API.showMessage(i18n.def.action_005);//請先選取一筆以上之資料列
    	 		return;
    	 	}
	       	$.ajax({
	             handler: pageAction.handler,
	             data: {
	                 formAction: "to_finish",
	                 oids: oids
	             }
	             }).done(function(){
	            	 $("#gridview").trigger("reloadGrid");
	            	 API.showMessage(i18n.def.runSuccess);
	        });
        }).end().find("#btnPrint").click(function(){
        	var rowId_arr = pageAction.grid.getGridParam('selarrrow');
    		var oid_arr = [];
       	 	for (var i = 0; i < rowId_arr.length; i++) {
    			var data = pageAction.grid.getRowData(rowId_arr[i]);
    			oid_arr.push(data.oid+"^"+data.mainId);    			
            }
       	 	
       	 	if(oid_arr.length==0){   	 		
    	 		API.showMessage(i18n.def.action_005);//請先選取一筆以上之資料列
    	 		return;
    	 	}
       	 /*
       	  * 同時印多份: 
       	  * oid_arr.push(data.oid+"^"+data.mainId);
       	  * rptOid: oid_arr.join("|")
       	  */
	       	 $.form.submit({
	             url: "../simple/FileProcessingService",
	             target: "_blank",
	             data: {
	                 'rptOid': oid_arr.join("|"),
	                 'fileDownloadName': "cls8011r01.pdf",                       
	                 serviceName: "cls8011r01rptservice"            
	             }
	         });
        }).end().find("#btnCreateExl").click(function(){
        	var _id = "createExlBox";
    		var _form = "createExlForm";
    		if ($("#"+_id).length == 0){
    			var dyna = [];
    			dyna.push("<div id='"+_id+"' style='display:none'>");
    			dyna.push("<form id='"+_form+"' name='"+_form+"'>");
    			
    			dyna.push("<p>");
   					dyna.push("<label><input class='required' type='radio' value='1' name='ByCondOrSel'>依條件</label>");
   					dyna.push("<table id='xls_cond_area' style='display:none; margin-left:20px;'>");
   					dyna.push("<input type='hidden' id='docType' name='docType' value='2'");
//					dyna.push("<tr>");
//	   					dyna.push("<td>");
//							dyna.push("<span>"+"企/個金案件"+"：</span>");
//						dyna.push("</td>");
//						dyna.push("<td>");
//							dyna.push("<select id='docType' name='docType'>");
//			    		    dyna.push("<option value=''>"+i18n.cls8011v01["C801M01A.caseStatus.all"]+"</option>");
//			    		    dyna.push("<option value='1'>"+"企金"+"</option>");
//			    		    dyna.push("<option value='2'>"+"個金"+"</option>");
//			    		    dyna.push("</select>");
//						dyna.push("</td>");
//	    		    dyna.push("</tr>");
					dyna.push("<tr>");
	   					dyna.push("<td>");
	   					dyna.push("<span>"+i18n.cls8011v01["C801M01A.caseStatus"]+"：</span>");
	   					dyna.push("</td><td>");
							dyna.push("<select id='xls_caseStatus' name='xls_caseStatus'>");					
			    		    dyna.push("<option value=''>"+i18n.cls8011v01["C801M01A.caseStatus.all"]+"</option>");
			    		    dyna.push("<option value='1'>"+i18n.cls8011v01["C801M01A.caseStatus.1"]+"</option>");
			    		    dyna.push("<option value='2'>"+i18n.cls8011v01["C801M01A.caseStatus.2"]+"</option>");
			    		    dyna.push("<option value='3'>"+i18n.cls8011v01["C801M01A.caseStatus.3"]+"</option>");
			    		    dyna.push("<option value='4'>"+i18n.cls8011v01["C801M01A.caseStatus.4"]+"</option>");
			    		    dyna.push("</select>");
	    		    	dyna.push("</td>");
	    		    	dyna.push("</tr>");
						dyna.push("<tr>");
		   					dyna.push("<td>");
		   					dyna.push("<span>"+i18n.cls8011v01["C801M01A.approveTime"]+"：</span>");
		   					dyna.push("</td><td>");
			   					dyna.push("<input type='text' id='xls_approveTime_beg' name='xls_approveTime_beg' class='date'>");
			    		    	dyna.push("~");
			    		    	dyna.push("<input type='text' id='xls_approveTime_end' name='xls_approveTime_end' class='date'>");
		    		    	dyna.push("</td>");
		    		    	dyna.push("</tr>");
						dyna.push("<tr>");
		   					dyna.push("<td>");
		   					dyna.push("<span>"+i18n.cls8011v01["C801M01A.creatorname"]+"：</span>");
		   					dyna.push("</td><td>");
			   					dyna.push("<select id='xls_caseApprId' name='xls_caseApprId'></select>");
		    		    	dyna.push("</td>");	
	    		    dyna.push("</tr></table>");
    		    dyna.push("</p>");
    		    
	    		dyna.push("<p>");
	    				dyna.push("<label><input class='required' type='radio' value='2' name='ByCondOrSel'>依勾選資料</label>");
				dyna.push("</p>");
				    		    
    		    dyna.push("</form>");
    			dyna.push("</div>");
    			
    		    $('body').append(dyna.join(""));
				
				$("#xls_approveTime_beg").datepicker();
				$("#xls_approveTime_end").datepicker();
				
    		    if(true){
    	    		//在 function pageInit(...) 中，會針對 欄位 custId addClass upText
         		    pageInit.call( $("#"+_id) );
    		    }
    		    $.ajax({
		    	 	handler: pageAction.handler,
		    	 	async: true,
                    data: {formAction: "queryEmp" }
                    }).done(function(json){
                    	$("#xls_caseApprId").setItems({ item: json.staff_list, space: true, format:'{value} : {key}' });
    		    });
    		    //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ 
    		    $("#"+_id).find('input[name=ByCondOrSel]').click(function(){
		            var $fm = $("#"+_form);
		            if ($(this).val() == '1') {
		                $fm.find('#xls_cond_area').show();
		            } else {
		                $fm.find('#xls_cond_area').hide();		                
		            }
		        })
		        $("#"+_id).find("[name='ByCondOrSel'][value=1]").prop("checked", true).trigger('change').trigger('click');
    		}
    		
    		$("#"+_id).thickbox({
	            title: '',
	            width: 420,
	            height: 270,
	            valign: "bottom",
	            align: "center",
	            i18n: i18n.def,
	            buttons: {
	                'sure': function(){
	                    var $form = $("#"+_form);
	                    if ($form.valid()) {
	                    	
	                    	var opts = {}
	                    	//J-108-0361_10702_B1001 Web e-Loan 授信企金新增個人資料檔案清冊功能
	                    	//企金=1，個金=2
	                    	var docType = 2;
							opts['docType'] = docType;
							
	                    	var valByCondOrSel = $form.find("[name='ByCondOrSel']:checked").val();
         		            if (valByCondOrSel == '1') {
         		            	opts['caseStatus'] = $form.find("[name='xls_caseStatus']").val();
         		            	opts['docStatus'] = viewstatus;
         		            	opts['xls_approveTime_beg'] = $form.find("[name='xls_approveTime_beg']").val();
         		            	opts['xls_approveTime_end'] = $form.find("[name='xls_approveTime_end']").val();
     		            	    opts['xls_caseApprId'] = $form.find("[name='xls_caseApprId']").val();
         		            } else {
         		            	var oid_arr = [];
         		         
         		            	var rowId_arr = pageAction.grid.getGridParam('selarrrow');	         	
         		          	 	for (var i = 0; i < rowId_arr.length; i++) {
         		          	 		var data = pageAction.grid.getRowData(rowId_arr[i]);
         		          	 		oid_arr.push(data.oid);    			
         		          	 	}
         		          	 	
                               	if(oid_arr.length==0){
                    				return CommonAPI.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列	 
                    			}
                               	opts['oids'] = oid_arr.join("|");
         		            }
	                    	
                            //不能直接用 $.capFileDownload(...)，會強制 encode 把  | 轉成 %7C
	                    	var _param = {'_pa' : 'lmsdownloadformhandler'
	                    				, 'fileDownloadName' : 'list.xls'
	                    				, 'serviceName' : "cls8011r01rptservice"
	                    				, 'printFlag' : 'N'	
	                    	};
	                    	$.form.submit({ url: __ajaxHandler, target : "_blank", data : $.extend(_param,opts) });
	                        $.thickbox.close();
	                    }
	                },
	                'cancel': function(){
	                    $.thickbox.close();
	                }
	            }
	        });
    		
        }).end().find("#btnBackDoc").click(function(){
        	var rowId_arr = pageAction.grid.getGridParam('selarrrow');
    		var oids = [];
       	 	for (var i = 0; i < rowId_arr.length; i++) {
    			var data = pageAction.grid.getRowData(rowId_arr[i]);
    			oids.push(data.oid);    			
            }
       	 	
       	 	if(oids.length==0){   	 		
    	 		API.showMessage(i18n.def.action_005);//請先選取一筆以上之資料列
    	 		return;
    	 	}
	       	$.ajax({
	             handler: pageAction.handler,
	             data: {
	                 formAction: "to_edit",
	                 oids: oids
	             }
	             }).done(function(){
	            	 $("#gridview").trigger("reloadGrid");
	            	 API.showMessage(i18n.def.runSuccess);
	        });
       	 	
        });
        
    },
    /**
     * 開啟文件
     */
    openDoc: function(cellvalue, options, data){
        $.form.submit({
            url: '../cls/cls8011m01/01',
            data:{
  				mainOid: data.oid,
  				mainId: data.mainId,
                mainDocStatus: viewstatus
  			},
            target: data.oid
        });
    }    
}

$(function(){
    pageAction.build();    
});
