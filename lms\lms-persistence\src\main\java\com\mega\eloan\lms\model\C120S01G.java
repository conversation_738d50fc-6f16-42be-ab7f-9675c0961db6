/* 
 * C120S01G.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 個金信用評等表 **/
@NamedEntityGraph(name = "C120S01G-entity-graph", attributeNodes = { @NamedAttributeNode("c120m01a") })
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "C120S01G", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "ownBrId", "custId", "dupNo" }))
public class C120S01G extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 編製單位代號
	 * <p/>
	 * 單位代碼
	 */
	@Size(max = 3)
	@Column(name = "OWNBRID", length = 3, columnDefinition = "CHAR(3)")
	private String ownBrId;

	/** 身分證統編 **/
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/** 借款人姓名 **/
	@Size(max = 120)
	@Column(name = "CUSTNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String custName;

	/** 文件狀態 **/
	@Size(max = 3)
	@Column(name = "DOCSTATUS", length = 3, columnDefinition = "CHAR(3)")
	private String docStatus;

	/** 評等建立日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "GRDCDATE", columnDefinition = "DATE")
	private Date grdCDate;

	/** 評等調整日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "GRDTDATE", columnDefinition = "DATE")
	private Date grdTDate;

	/** 聯徵查詢日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "JCICQDATE", columnDefinition = "DATE")
	private Date jcicQDate;

	/** 票信查詢日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "ETCHQDATE", columnDefinition = "DATE")
	private Date etchQDate;

	/** 夫妻年收入（萬元） **/
	@Digits(integer = 9, fraction = 0, groups = Check.class)
	@Column(name = "YFAMAMT", columnDefinition = "DEC(9,0)")
	private BigDecimal yFamAmt;

	/** 夫妻年收入分數 **/
	@Digits(integer = 7, fraction = 4, groups = Check.class)
	@Column(name = "SCRNUM01", columnDefinition = "DEC(7,4)")
	private BigDecimal scrNum01;

	/** 婚姻 **/
	@Size(max = 1)
	@Column(name = "MARRY", length = 1, columnDefinition = "CHAR(1)")
	private String marry;

	/** 子女數 **/
	@Digits(integer = 2, fraction = 0, groups = Check.class)
	@Column(name = "CHILD", columnDefinition = "DECIMAL(2,0)")
	private Integer child;

	/** 學歷 **/
	@Size(max = 2)
	@Column(name = "EDU", length = 2, columnDefinition = "CHAR(2)")
	private String edu;

	/** 婚姻狀況及學歷分數 **/
	@Digits(integer = 7, fraction = 4, groups = Check.class)
	@Column(name = "SCRNUM02", columnDefinition = "DEC(7,4)")
	private BigDecimal scrNum02;

	/** 職業大類 **/
	@Size(max = 2)
	@Column(name = "JOBTYPE1", length = 2, columnDefinition = "CHAR(2)")
	private String jobType1;

	/** 職業大類分數 **/
	@Digits(integer = 7, fraction = 4, groups = Check.class)
	@Column(name = "SCRNUM03", columnDefinition = "DEC(7,4)")
	private BigDecimal scrNum03;

	/** 聯徵查詢月份前一月（或二個月）之無擔保授信餘額（千元） D07  **/
	@Digits(integer = 9, fraction = 0, groups = Check.class)
	@Column(name = "CHKAMT01", columnDefinition = "DEC(9,0)")
	private BigDecimal chkAmt01;

	/** 聯徵查詢月份前一月（或二個月）之無擔保授信餘額（千元） D07 分數 **/
	@Digits(integer = 7, fraction = 4, groups = Check.class)
	@Column(name = "SCRNUM04", columnDefinition = "DEC(7,4)")
	private BigDecimal scrNum04;

	/** 近六個月平均的月信用卡循環信用（元） **/
	@Digits(integer = 13, fraction = 4, groups = Check.class)
	@Column(name = "CHKAMT02", columnDefinition = "DEC(13,4)")
	private BigDecimal chkAmt02;

	/** 近六個月平均的月信用卡循環信用（元）分數 **/
	@Digits(integer = 7, fraction = 4, groups = Check.class)
	@Column(name = "SCRNUM05", columnDefinition = "DEC(7,4)")
	private BigDecimal scrNum05;

	/** 十二個月新業務申請查詢總家數 N06 **/
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "INQQTY", columnDefinition = "DEC(3,0)")
	private Integer inqQty;

	/** 十二個月新業務申請查詢總家數分數 **/
	@Digits(integer = 7, fraction = 4, groups = Check.class)
	@Column(name = "SCRNUM06", columnDefinition = "DEC(7,4)")
	private BigDecimal scrNum06;

	/** 近十二個月信用卡（每筆）循環信用平均使用率 **/
	@Digits(integer = 7, fraction = 4, groups = Check.class)
	@Column(name = "AVGRATE01", columnDefinition = "DEC(7,4)")
	private BigDecimal avgRate01;

	/** 近十二個月信用卡（每筆）循環信用平均使用率分數 **/
	@Digits(integer = 7, fraction = 4, groups = Check.class)
	@Column(name = "SCRNUM07", columnDefinition = "DEC(7,4)")
	private BigDecimal scrNum07;

	/** 聯徵查詢月份前一月之信用卡循環信用使用率 **/
	@Digits(integer = 7, fraction = 4, groups = Check.class)
	@Column(name = "AVGRATE02", columnDefinition = "DEC(7,4)")
	private BigDecimal avgRate02;

	/** 聯徵查詢月份前一月之信用卡循環信用使用率分數 **/
	@Digits(integer = 7, fraction = 4, groups = Check.class)
	@Column(name = "SCRNUM08", columnDefinition = "DEC(7,4)")
	private BigDecimal scrNum08;

	/** 近六個月信用卡繳款狀況出現全額繳清無延遲次數 P25 **/
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "CHKNUM1", columnDefinition = "DEC(3,0)")
	private BigDecimal chkNum1;

	/** 近六個月信用卡繳款狀況出現全額繳清無延遲次數分數 P25 **/
	@Digits(integer = 7, fraction = 4, groups = Check.class)
	@Column(name = "SCRNUM09", columnDefinition = "DEC(7,4)")
	private BigDecimal scrNum09;

	/** 近十二個月信用卡繳款狀況出現不良紀錄或使用循環信用次數 P69 **/
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "CHKNUM2", columnDefinition = "DEC(3,0)")
	private BigDecimal chkNum2;

	/** 近十二個月信用卡繳款狀況出現全額繳清無延遲次數 P19(房貸、非房貸判斷PAY_CODE的logic不同) **/
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "CHKNUM3", columnDefinition = "DEC(3,0)")
	private BigDecimal chkNum3;

	/**
	 * 近十二個月信用卡繳款狀況出現不良紀錄或使用循環信用次數 及 近十二個月信用卡繳款狀況出現全額繳清無延遲次數分數
	 **/
	@Digits(integer = 7, fraction = 4, groups = Check.class)
	@Column(name = "SCRNUM10", columnDefinition = "DEC(7,4)")
	private BigDecimal scrNum10;

	/** 組別 **/
	@Size(max = 20)
	@Column(name = "GRPNUM10", length = 20, columnDefinition = "VARCHAR(20)")
	private String grpNum10;

	/** 合計變量得分 **/
	@Digits(integer = 7, fraction = 4, groups = Check.class)
	@Column(name = "SCRNUM11", columnDefinition = "DEC(7,4)")
	private BigDecimal scrNum11;

	/**
	 * 基準底分=A + B ＊ 常數項
	 */
	@Digits(integer = 7, fraction = 4, groups = Check.class)
	@Column(name = "SCRNUM12", columnDefinition = "DEC(7,4)")
	private BigDecimal scrNum12;

	/**
	 * 初始評分=scrNum11 + scrNum12
	 */
	@Digits(integer = 8, fraction = 4, groups = Check.class)
	@Column(name = "SCRNUM13", columnDefinition = "DEC(8,4)")
	private BigDecimal scrNum13;

	/**
	 * 初始評等
	 * <p/>
	 * 初始評分對照的等級
	 */
	@Size(max = 2)
	@Column(name = "GRADE1", length = 2, columnDefinition = "VARCHAR(2)")
	private String grade1;

	/**
	 * 調整評等
	 * <p/>
	 * 人工調整
	 */
	@Size(max = 2)
	@Column(name = "GRADE2", length = 2, columnDefinition = "VARCHAR(2)")
	private String grade2;

	/** 最終評等 **/
	@Size(max = 2)
	@Column(name = "GRADE3", length = 2, columnDefinition = "VARCHAR(2)")
	private String grade3;

	/**
	 * 預測壞率
	 * <p/>
	 * 計算：小數點4 位，四捨五入<br/>
	 * (1/(1+Exp(scrNum13 – A/B)))
	 */
	@Digits(integer = 9, fraction = 4, groups = Check.class)
	@Column(name = "PD", columnDefinition = "DEC(9,4)")
	private BigDecimal pd;

	/**
	 * 有退票、拒往、信用卡強停或催收呆帳紀錄
	 * <p/>
	 * 票交所與聯徵特殊負面資訊
	 */
	@Size(max = 1)
	@Column(name = "CHKITEM1", length = 1, columnDefinition = "CHAR(1)")
	private String chkItem1;

	/** 退票 **/
	@Size(max = 1)
	@Column(name = "CHKITEM1A", length = 1, columnDefinition = "CHAR(1)")
	private String chkItem1a;

	/** 拒往 **/
	@Size(max = 1)
	@Column(name = "CHKITEM1B", length = 1, columnDefinition = "CHAR(1)")
	private String chkItem1b;

	/** 信用卡強停 **/
	@Size(max = 1)
	@Column(name = "CHKITEM1C", length = 1, columnDefinition = "CHAR(1)")
	private String chkItem1c;

	/** 催收呆帳 **/
	@Size(max = 1)
	@Column(name = "CHKITEM1D", length = 1, columnDefinition = "CHAR(1)")
	private String chkItem1d;
	
	/** 逾期放款 **/
	@Size(max = 1)
	@Column(name = "CHKITEM1E", length = 1, columnDefinition = "CHAR(1)")
	private String chkItem1e;

	/**
	 * 有消債條例信用註記、銀行公會債務協商註記或其他補充註記
	 * <p/>
	 * 票交所與聯徵特殊負面資訊
	 */
	@Size(max = 1)
	@Column(name = "CHKITEM2", length = 1, columnDefinition = "CHAR(1)")
	private String chkItem2;

	/** 消債條例信用註記 **/
	@Size(max = 1)
	@Column(name = "CHKITEM2A", length = 1, columnDefinition = "CHAR(1)")
	private String chkItem2a;

	/** 銀行公會債務協商註記 **/
	@Size(max = 1)
	@Column(name = "CHKITEM2B", length = 1, columnDefinition = "CHAR(1)")
	private String chkItem2b;

	/** 其他補充註記 **/
	@Size(max = 1)
	@Column(name = "CHKITEM2C", length = 1, columnDefinition = "CHAR(1)")
	private String chkItem2c;

	/**
	 * 近12個月授信帳戶出現延遲二次（含）以上
	 * <p/>
	 * 票交所與聯徵特殊負面資訊
	 */
	@Size(max = 1)
	@Column(name = "CHKITEM3", length = 1, columnDefinition = "CHAR(1)")
	private String chkItem3;

	/**
	 * 近12個月信用卡繳款狀況出現（循環信用有延遲）二次（含）以上
	 * <p/>
	 * 票交所與聯徵特殊負面資訊
	 */
	@Size(max = 1)
	@Column(name = "CHKITEM4", length = 1, columnDefinition = "CHAR(1)")
	private String chkItem4;

	/**
	 * 近12個月信用卡繳款狀況出現（未繳足最低金額）二次（含）以上
	 * <p/>
	 * 票交所與聯徵特殊負面資訊
	 */
	@Size(max = 1)
	@Column(name = "CHKITEM5", length = 1, columnDefinition = "CHAR(1)")
	private String chkItem5;

	/**
	 * 近12個月信用卡繳款狀況出現（全額逾期連續未繳）二次（含）以上
	 * <p/>
	 * 票交所與聯徵特殊負面資訊
	 */
	@Size(max = 1)
	@Column(name = "CHKITEM6", length = 1, columnDefinition = "CHAR(1)")
	private String chkItem6;

	/**
	 * 近12個月信用卡有預借現金餘額二次（含）以上
	 * <p/>
	 * 票交所與聯徵特殊負面資訊
	 */
	@Size(max = 1)
	@Column(name = "CHKITEM7", length = 1, columnDefinition = "CHAR(1)")
	private String chkItem7;

	/**
	 * 近12個月現金卡有動用紀錄
	 * <p/>
	 * 票交所與聯徵特殊負面資訊
	 */
	@Size(max = 1)
	@Column(name = "CHKITEM8", length = 1, columnDefinition = "CHAR(1)")
	private String chkItem8;

	/** 基準底分A **/
	@Digits(integer = 5, fraction = 0, groups = Check.class)
	@Column(name = "VARA", columnDefinition = "DEC(5,0)")
	private BigDecimal varA;

	/** 基準底分B **/
	@Digits(integer = 7, fraction = 4, groups = Check.class)
	@Column(name = "VARB", columnDefinition = "DEC(7,4)")
	private BigDecimal varB;

	/** 基準底分常數項 **/
	@Digits(integer = 9, fraction = 7, groups = Check.class)
	@Column(name = "VARC", columnDefinition = "DEC(9,7)")
	private BigDecimal varC;

	/** 基準底分版本 **/
	@Size(max = 10)
	@Column(name = "VARVER", length = 10, columnDefinition = "VARCHAR(10)")
	private String varVer;

	/** 報表亂碼 **/
	@Size(max = 32)
	@Column(name = "RANDOMCODE", length = 32, columnDefinition = "CHAR(32)")
	private String randomCode;

	/**
	 * 調整狀態
	 * <p/>
	 * 1.調升 2.調降 3.回復
	 */
	@Size(max = 1)
	@Column(name = "ADJUSTSTATUS", length = 1, columnDefinition = "CHAR(1)")
	private String adjustStatus;

	/**
	 * 調整註記(升等)
	 * <p/>
	 * 1.淨資產2.職業3.其它4.一般保證人資信佳
	 */
	@Size(max = 1)
	@Column(name = "ADJUSTFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String adjustFlag;

	/** 調整理由 **/
	@Size(max = 300)
	@Column(name = "ADJUSTREASON", length = 300, columnDefinition = "VARCHAR(300)")
	private String adjustReason;

	/** 未持有信用卡{Y.是}(值 和C101M01A.jcicFlg 第3碼 相反) */
	@Size(max = 1)
	@Column(name = "CARDFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String cardFlag;

	/**
	 * 用途別
	 * <p/>
	 * Add 2013/02/22
	 */
	@Size(max = 1)
	@Column(name = "PURPOSECODE", length = 1, columnDefinition = "CHAR(1)")
	private String purposeCode;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;
	
	/** 
	 * 是否引用<p/>
	 * Y.是
	 */
	@Size(max=1)
	@Column(name="QUOTE", length=1, columnDefinition="CHAR(1)")
	private String quote;

	/** 違約機率(預估3年期) **/
	@Digits(integer = 8, fraction = 5, groups = Check.class)
	@Column(name = "DR_3YR", columnDefinition = "DEC(8,5)")
	private BigDecimal dr_3YR;
	

	/** 違約機率(預估1年期) **/
	@Digits(integer = 8, fraction = 5, groups = Check.class)
	@Column(name = "DR_1YR", columnDefinition = "DEC(8,5)")
	private BigDecimal dr_1YR;

	/** 信用卡循環信用次數 **/
	@Digits(integer = 3, fraction = 0)
	@Column(name = "REVOL_CNT", columnDefinition = "DEC(3,0)")
	private BigDecimal revol_cnt;
	
	/** 職稱 **/
	@Size(max = 1)
	@Column(name = "JOBTITLE", length = 1, columnDefinition = "CHAR(1)")
	private String jobTitle;
	
	/** 年資 **/
	@Digits(integer = 2, fraction = 2)
	@Column(name = "SENIORITY", columnDefinition = "DECIMAL(4,2)")
	private BigDecimal seniority;
	
	/** 年資分數_G **/
	@Digits(integer=7, fraction=4)
	@Column(name="SCRSENIORITY_G", columnDefinition="DEC(7,4)")
	private BigDecimal scrSeniority_G;
	
	/** 個人年所得(年薪+其他收入)(萬元) **/
	@Digits(integer=13, fraction=0)
	@Column(name="PINCOME", columnDefinition="DECIMAL(13,0)")
	private BigDecimal pIncome;
	
	/** D07_DIV_PINCOME因子 **/
	@Digits(integer=19, fraction=4)
	@Column(name="ITEMD07_DIV_PINCOME", columnDefinition="DECIMAL(19,4)")
	private BigDecimal itemD07_DIV_PINCOME;
	
	/** D07_DIV_PINCOME分數 **/
	@Digits(integer=7, fraction=4)
	@Column(name="SCRD07_DIV_PINCOME", columnDefinition="DEC(7,4)")
	private BigDecimal scrD07_DIV_PINCOME;
	
	/** P68因子 **/
	@Digits(integer=3, fraction=0)
	@Column(name="ITEMP68", columnDefinition="DECIMAL(3,0)")
	private BigDecimal itemP68;
	
	/** P68_P19分數 **/
	@Digits(integer=7, fraction=4)
	@Column(name="SCRP68P19", columnDefinition="DEC(7,4)")
	private BigDecimal scrP68P19;
	
	/** N18次數因子 **/
	@Digits(integer=5, fraction=2)
	@Column(name="ITEMN18", columnDefinition="DECIMAL(5,2)")
	private BigDecimal itemN18;
	
	/** N22次數因子 **/
	@Digits(integer=5, fraction=2)
	@Column(name="ITEMN22", columnDefinition="DECIMAL(5,2)")
	private BigDecimal itemN22;
	
	/** N18_JobTitle分數 **/
	@Digits(integer=7, fraction=4)
	@Column(name="SCRN18", columnDefinition="DEC(7,4)")
	private BigDecimal scrN18;
	
	/** N22_JobTitle分數 **/
	@Digits(integer=7, fraction=4)
	@Column(name="SCRN22", columnDefinition="DEC(7,4)")
	private BigDecimal scrN22;
	
	/** Z03因子 **/
	@Digits(integer=17, fraction=2)
	@Column(name="ITEMZ03", columnDefinition="DECIMAL(17,2)")
	private BigDecimal itemZ03;
	
	/** Z03分數 **/
	@Digits(integer=7, fraction=4)
	@Column(name="SCRZ03", columnDefinition="DEC(7,4)")
	private BigDecimal scrZ03;
	
	/** 一般保證人統編 **/
	@Size(max = 10)
	@Column(name = "POSID", length = 10, columnDefinition = "CHAR(10)")
	private String posId;
	
	/** 一般保證人統編重複碼 **/
	@Size(max = 1)
	@Column(name = "POSDUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String posDupNo;
	
	/** 一般保證人姓名 **/
	@Size(max = 30)
	@Column(name = "POSNAME", length = 30, columnDefinition = "CHAR(30)")
	private String posName;

	/** 一般保證人關係類別  select * from com.bcodetype where codetype='Relation_type2' and locale='zh_TW' 
	 */
	@Size(max = 2)
	@Column(name = "POSKINDD", length = 2, columnDefinition = "CHAR(2)")
	private String posKindD;

	/** 擔保品類別
	 */
	@Size(max = 2)
	@Column(name = "ISKIND", length = 2, columnDefinition = "CHAR(2)")
	private String isKind;
	
	/** ----------消金房貸3.0 Start---------- **/
	/** 
	 * 近12個月新業務申請查詢總次數，計算方式-天數別(30天內算1次)分數<p/>
	 * 消金房貸3.0
	 */
	@Digits(integer=12, fraction=8, groups = Check.class)
	@Column(name="SCRN18ONLY", columnDefinition="DECIMAL(10,4)")
	private BigDecimal scrN18Only;

	/** 
	 * 夫妻負債比<p/>
	 * 消金房貸3.0
	 */
	@Digits(integer=7, fraction=4, groups = Check.class)
	@Column(name="YRATE", columnDefinition="DECIMAL(7,4)")
	private BigDecimal yRate;

	/** 
	 * 夫妻負債比分數<p/>
	 * 消金房貸3.0
	 */
	@Digits(integer=12, fraction=8, groups = Check.class)
	@Column(name="SCRYRATE", columnDefinition="DECIMAL(10,4)")
	private BigDecimal scrYRate;

	/** 
	 * 職稱分數<p/>
	 * 消金房貸3.0
	 */
	@Digits(integer=12, fraction=8, groups = Check.class)
	@Column(name="SCRJOBTITLE", columnDefinition="DECIMAL(10,4)")
	private BigDecimal scrJobTitle;

	/** 
	 * 當月有效信用卡主卡平均信用額度(仟元)<p/>
	 * 消金房貸3.0
	 */
	@Digits(integer=14, fraction=5, groups = Check.class)
	@Column(name="ITEMD42", columnDefinition="DECIMAL(14,5)")
	private BigDecimal itemD42;

	/** 
	 * 當月有效信用卡主卡平均信用額度(仟元)分數<p/>
	 * 消金房貸3.0
	 */
	@Digits(integer=12, fraction=8, groups = Check.class)
	@Column(name="SCRD42", columnDefinition="DECIMAL(10,4)")
	private BigDecimal scrD42;

	/** 
	 * 學歷分數<p/>
	 * 消金房貸3.0
	 */
	@Digits(integer=12, fraction=8, groups = Check.class)
	@Column(name="SCREDU", columnDefinition="DECIMAL(10,4)")
	private BigDecimal scrEdu;

	/** 
	 * 近6個月信用卡繳款狀況出現不良繳款紀錄或使用循環信用的次數分數<p/>
	 * 消金房貸3.0
	 */
	@Digits(integer=12, fraction=8, groups = Check.class)
	@Column(name="SCRP68", columnDefinition="DECIMAL(10,4)")
	private BigDecimal scrP68;

	/** 
	 * 近3個月非Z類申請查詢總次數(近三個月本行查詢不列入計算)<p/>
	 * 消金房貸3.0
	 */
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="ITEMN01", columnDefinition="DECIMAL(5,2)")
	private BigDecimal itemN01;

	/** 
	 * 近3個月非Z類申請查詢總次數(近三個月本行查詢不列入計算)分數<p/>
	 * 消金房貸3.0
	 */
	@Digits(integer=12, fraction=8, groups = Check.class)
	@Column(name="SCRN01", columnDefinition="DECIMAL(10,4)")
	private BigDecimal scrN01;

	/** 
	 * 近12個月信用卡繳款狀況出現全額繳清無延遲次數(不含無須繳款)分數<p/>
	 * 消金房貸3.0
	 */
	@Digits(integer=12, fraction=8, groups = Check.class)
	@Column(name="SCRP19", columnDefinition="DECIMAL(10,4)")
	private BigDecimal scrP19;
	
	/** 
	 * 截距<p/>
	 * 消金房貸3.0
	 */
	@Digits(integer=6, fraction=4, groups = Check.class)
	@Column(name="INTERCEPT", columnDefinition="DECIMAL(6,4)")
	private BigDecimal interCept;

	/** 
	 * 斜率<p/>
	 * 消金房貸3.0
	 */
	@Digits(integer=6, fraction=4, groups = Check.class)
	@Column(name="SLOPE", columnDefinition="DECIMAL(6,4)")
	private BigDecimal slope;
	
	
	/** ----------消金房貸3.0 End---------- **/
	
	
	
	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得編製單位代號
	 * <p/>
	 * 單位代碼
	 */
	public String getOwnBrId() {
		return this.ownBrId;
	}

	/**
	 * 設定編製單位代號
	 * <p/>
	 * 單位代碼
	 **/
	public void setOwnBrId(String value) {
		this.ownBrId = value;
	}

	/** 取得身分證統編 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定身分證統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得身分證統編重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定身分證統編重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得借款人姓名 **/
	public String getCustName() {
		return this.custName;
	}

	/** 設定借款人姓名 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/** 取得文件狀態 **/
	public String getDocStatus() {
		return this.docStatus;
	}

	/** 設定文件狀態 **/
	public void setDocStatus(String value) {
		this.docStatus = value;
	}

	/** 取得評等建立日期 **/
	public Date getGrdCDate() {
		return this.grdCDate;
	}

	/** 設定評等建立日期 **/
	public void setGrdCDate(Date value) {
		this.grdCDate = value;
	}

	/** 取得評等調整日期 **/
	public Date getGrdTDate() {
		return this.grdTDate;
	}

	/** 設定評等調整日期 **/
	public void setGrdTDate(Date value) {
		this.grdTDate = value;
	}

	/** 取得聯徵查詢日期 **/
	public Date getJcicQDate() {
		return this.jcicQDate;
	}

	/** 設定聯徵查詢日期 **/
	public void setJcicQDate(Date value) {
		this.jcicQDate = value;
	}

	/** 取得票信查詢日期 **/
	public Date getEtchQDate() {
		return this.etchQDate;
	}

	/** 設定票信查詢日期 **/
	public void setEtchQDate(Date value) {
		this.etchQDate = value;
	}

	/** 取得夫妻年收入（萬元） **/
	public BigDecimal getYFamAmt() {
		return this.yFamAmt;
	}

	/** 設定夫妻年收入（萬元） **/
	public void setYFamAmt(BigDecimal value) {
		this.yFamAmt = value;
	}

	/** 取得夫妻年收入分數 **/
	public BigDecimal getScrNum01() {
		return this.scrNum01;
	}

	/** 設定夫妻年收入分數 **/
	public void setScrNum01(BigDecimal value) {
		this.scrNum01 = value;
	}

	/** 取得婚姻 **/
	public String getMarry() {
		return this.marry;
	}

	/** 設定婚姻 **/
	public void setMarry(String value) {
		this.marry = value;
	}

	/** 取得子女數 **/
	public Integer getChild() {
		return this.child;
	}

	/** 設定子女數 **/
	public void setChild(Integer value) {
		this.child = value;
	}

	/** 取得學歷 **/
	public String getEdu() {
		return this.edu;
	}

	/** 設定學歷 **/
	public void setEdu(String value) {
		this.edu = value;
	}

	/** 取得婚姻狀況及學歷分數 **/
	public BigDecimal getScrNum02() {
		return this.scrNum02;
	}

	/** 設定婚姻狀況及學歷分數 **/
	public void setScrNum02(BigDecimal value) {
		this.scrNum02 = value;
	}

	/** 取得職業大類 **/
	public String getJobType1() {
		return this.jobType1;
	}

	/** 設定職業大類 **/
	public void setJobType1(String value) {
		this.jobType1 = value;
	}

	/** 取得職業大類分數 **/
	public BigDecimal getScrNum03() {
		return this.scrNum03;
	}

	/** 設定職業大類分數 **/
	public void setScrNum03(BigDecimal value) {
		this.scrNum03 = value;
	}

	/** 取得聯徵查詢月份前一月（或二個月）之無擔保授信餘額（千元） **/
	public BigDecimal getChkAmt01() {
		return this.chkAmt01;
	}

	/** 設定聯徵查詢月份前一月（或二個月）之無擔保授信餘額（千元） **/
	public void setChkAmt01(BigDecimal value) {
		this.chkAmt01 = value;
	}

	/** 取得聯徵查詢月份前一月（或二個月）之無擔保授信餘額（千元）分數 **/
	public BigDecimal getScrNum04() {
		return this.scrNum04;
	}

	/** 設定聯徵查詢月份前一月（或二個月）之無擔保授信餘額（千元）分數 **/
	public void setScrNum04(BigDecimal value) {
		this.scrNum04 = value;
	}

	/** 取得近六個月平均的月信用卡循環信用（元） **/
	public BigDecimal getChkAmt02() {
		return this.chkAmt02;
	}

	/** 設定近六個月平均的月信用卡循環信用（元） **/
	public void setChkAmt02(BigDecimal value) {
		this.chkAmt02 = value;
	}

	/** 取得近六個月平均的月信用卡循環信用（元）分數 **/
	public BigDecimal getScrNum05() {
		return this.scrNum05;
	}

	/** 設定近六個月平均的月信用卡循環信用（元）分數 **/
	public void setScrNum05(BigDecimal value) {
		this.scrNum05 = value;
	}

	/** 取得十二個月新業務申請查詢總家數 **/
	public Integer getInqQty() {
		return this.inqQty;
	}

	/** 設定十二個月新業務申請查詢總家數 **/
	public void setInqQty(Integer value) {
		this.inqQty = value;
	}

	/** 取得十二個月新業務申請查詢總家數分數 **/
	public BigDecimal getScrNum06() {
		return this.scrNum06;
	}

	/** 設定十二個月新業務申請查詢總家數分數 **/
	public void setScrNum06(BigDecimal value) {
		this.scrNum06 = value;
	}

	/** 取得近十二個月信用卡（每筆）循環信用平均使用率 **/
	public BigDecimal getAvgRate01() {
		return this.avgRate01;
	}

	/** 設定近十二個月信用卡（每筆）循環信用平均使用率 **/
	public void setAvgRate01(BigDecimal value) {
		this.avgRate01 = value;
	}

	/** 取得近十二個月信用卡（每筆）循環信用平均使用率分數 **/
	public BigDecimal getScrNum07() {
		return this.scrNum07;
	}

	/** 設定近十二個月信用卡（每筆）循環信用平均使用率分數 **/
	public void setScrNum07(BigDecimal value) {
		this.scrNum07 = value;
	}

	/** 取得聯徵查詢月份前一月之信用卡循環信用使用率 **/
	public BigDecimal getAvgRate02() {
		return this.avgRate02;
	}

	/** 設定聯徵查詢月份前一月之信用卡循環信用使用率 **/
	public void setAvgRate02(BigDecimal value) {
		this.avgRate02 = value;
	}

	/** 取得聯徵查詢月份前一月之信用卡循環信用使用率分數 **/
	public BigDecimal getScrNum08() {
		return this.scrNum08;
	}

	/** 設定聯徵查詢月份前一月之信用卡循環信用使用率分數 **/
	public void setScrNum08(BigDecimal value) {
		this.scrNum08 = value;
	}

	/** 取得近六個月信用卡繳款狀況出現全額繳清無延遲次數 **/
	public BigDecimal getChkNum1() {
		return this.chkNum1;
	}

	/** 設定近六個月信用卡繳款狀況出現全額繳清無延遲次數 **/
	public void setChkNum1(BigDecimal value) {
		this.chkNum1 = value;
	}

	/** 取得近六個月信用卡繳款狀況出現全額繳清無延遲次數分數 **/
	public BigDecimal getScrNum09() {
		return this.scrNum09;
	}

	/** 設定近六個月信用卡繳款狀況出現全額繳清無延遲次數分數 **/
	public void setScrNum09(BigDecimal value) {
		this.scrNum09 = value;
	}

	/** 取得近十二個月信用卡繳款狀況出現不良紀錄或使用循環信用次數 **/
	public BigDecimal getChkNum2() {
		return this.chkNum2;
	}

	/** 設定近十二個月信用卡繳款狀況出現不良紀錄或使用循環信用次數 **/
	public void setChkNum2(BigDecimal value) {
		this.chkNum2 = value;
	}

	/** 取得近十二個月信用卡繳款狀況出現全額繳清無延遲次數 **/
	public BigDecimal getChkNum3() {
		return this.chkNum3;
	}

	/** 設定近十二個月信用卡繳款狀況出現全額繳清無延遲次數 **/
	public void setChkNum3(BigDecimal value) {
		this.chkNum3 = value;
	}

	/**
	 * 取得近十二個月信用卡繳款狀況出現不良紀錄或使用循環信用次數 及 近十二個月信用卡繳款狀況出現全額繳清無延遲次數分數
	 **/
	public BigDecimal getScrNum10() {
		return this.scrNum10;
	}

	/**
	 * 設定近十二個月信用卡繳款狀況出現不良紀錄或使用循環信用次數 及 近十二個月信用卡繳款狀況出現全額繳清無延遲次數分數
	 **/
	public void setScrNum10(BigDecimal value) {
		this.scrNum10 = value;
	}

	/** 取得組別 **/
	public String getGrpNum10() {
		return this.grpNum10;
	}

	/** 設定組別 **/
	public void setGrpNum10(String value) {
		this.grpNum10 = value;
	}

	/** 取得合計變量得分 **/
	public BigDecimal getScrNum11() {
		return this.scrNum11;
	}

	/** 設定合計變量得分 **/
	public void setScrNum11(BigDecimal value) {
		this.scrNum11 = value;
	}

	/**
	 * 取得基準底分=A + B ＊ 常數項
	 */
	public BigDecimal getScrNum12() {
		return this.scrNum12;
	}

	/**
	 * 設定基準底分=A + B ＊ 常數項
	 **/
	public void setScrNum12(BigDecimal value) {
		this.scrNum12 = value;
	}

	/**
	 * 取得初始評分=scrNum11 + scrNum12
	 */
	public BigDecimal getScrNum13() {
		return this.scrNum13;
	}

	/**
	 * 設定初始評分=scrNum11 + scrNum12
	 **/
	public void setScrNum13(BigDecimal value) {
		this.scrNum13 = value;
	}

	/**
	 * 取得初始評等
	 * <p/>
	 * 初始評分對照的等級
	 */
	public String getGrade1() {
		return this.grade1;
	}

	/**
	 * 設定初始評等
	 * <p/>
	 * 初始評分對照的等級
	 **/
	public void setGrade1(String value) {
		this.grade1 = value;
	}

	/**
	 * 取得調整評等
	 * <p/>
	 * 人工調整
	 */
	public String getGrade2() {
		return this.grade2;
	}

	/**
	 * 設定調整評等
	 * <p/>
	 * 人工調整
	 **/
	public void setGrade2(String value) {
		this.grade2 = value;
	}

	/** 取得最終評等 **/
	public String getGrade3() {
		return this.grade3;
	}

	/** 設定最終評等 **/
	public void setGrade3(String value) {
		this.grade3 = value;
	}

	/**
	 * 取得預測壞率
	 * <p/>
	 * 計算：小數點4 位，四捨五入<br/>
	 * (1/(1+Exp(scrNum13 – A/B)))
	 */
	public BigDecimal getPd() {
		return this.pd;
	}

	/**
	 * 設定預測壞率
	 * <p/>
	 * 計算：小數點4 位，四捨五入<br/>
	 * (1/(1+Exp(scrNum13 – A/B)))
	 **/
	public void setPd(BigDecimal value) {
		this.pd = value;
	}

	/**
	 * 取得有退票、拒往、信用卡強停或催收呆帳紀錄
	 * <p/>
	 * 票交所與聯徵特殊負面資訊
	 */
	public String getChkItem1() {
		return this.chkItem1;
	}

	/**
	 * 設定有退票、拒往、信用卡強停或催收呆帳紀錄
	 * <p/>
	 * 票交所與聯徵特殊負面資訊
	 **/
	public void setChkItem1(String value) {
		this.chkItem1 = value;
	}

	/** 取得退票 **/
	public String getChkItem1a() {
		return this.chkItem1a;
	}

	/** 設定退票 **/
	public void setChkItem1a(String value) {
		this.chkItem1a = value;
	}

	/** 取得拒往 **/
	public String getChkItem1b() {
		return this.chkItem1b;
	}

	/** 設定拒往 **/
	public void setChkItem1b(String value) {
		this.chkItem1b = value;
	}

	/** 取得信用卡強停 **/
	public String getChkItem1c() {
		return this.chkItem1c;
	}

	/** 設定信用卡強停 **/
	public void setChkItem1c(String value) {
		this.chkItem1c = value;
	}

	/** 取得催收呆帳 **/
	public String getChkItem1d() {
		return this.chkItem1d;
	}

	/** 設定催收呆帳 **/
	public void setChkItem1d(String value) {
		this.chkItem1d = value;
	}
	
	/** 取得逾期放款 **/
	public String getChkItem1e() {
		return this.chkItem1e;
	}

	/** 設定逾期放款 **/
	public void setChkItem1e(String value) {
		this.chkItem1e = value;
	}

	/**
	 * 取得有消債條例信用註記、銀行公會債務協商註記或其他補充註記
	 * <p/>
	 * 票交所與聯徵特殊負面資訊
	 */
	public String getChkItem2() {
		return this.chkItem2;
	}

	/**
	 * 設定有消債條例信用註記、銀行公會債務協商註記或其他補充註記
	 * <p/>
	 * 票交所與聯徵特殊負面資訊
	 **/
	public void setChkItem2(String value) {
		this.chkItem2 = value;
	}

	/** 取得消債條例信用註記 **/
	public String getChkItem2a() {
		return this.chkItem2a;
	}

	/** 設定消債條例信用註記 **/
	public void setChkItem2a(String value) {
		this.chkItem2a = value;
	}

	/** 取得銀行公會債務協商註記 **/
	public String getChkItem2b() {
		return this.chkItem2b;
	}

	/** 設定銀行公會債務協商註記 **/
	public void setChkItem2b(String value) {
		this.chkItem2b = value;
	}

	/** 取得其他補充註記 **/
	public String getChkItem2c() {
		return this.chkItem2c;
	}

	/** 設定其他補充註記 **/
	public void setChkItem2c(String value) {
		this.chkItem2c = value;
	}

	/**
	 * 取得近12個月授信帳戶出現延遲二次（含）以上
	 * <p/>
	 * 票交所與聯徵特殊負面資訊
	 */
	public String getChkItem3() {
		return this.chkItem3;
	}

	/**
	 * 設定近12個月授信帳戶出現延遲二次（含）以上
	 * <p/>
	 * 票交所與聯徵特殊負面資訊
	 **/
	public void setChkItem3(String value) {
		this.chkItem3 = value;
	}

	/**
	 * 取得近12個月信用卡繳款狀況出現（循環信用有延遲）二次（含）以上
	 * <p/>
	 * 票交所與聯徵特殊負面資訊
	 */
	public String getChkItem4() {
		return this.chkItem4;
	}

	/**
	 * 設定近12個月信用卡繳款狀況出現（循環信用有延遲）二次（含）以上
	 * <p/>
	 * 票交所與聯徵特殊負面資訊
	 **/
	public void setChkItem4(String value) {
		this.chkItem4 = value;
	}

	/**
	 * 取得近12個月信用卡繳款狀況出現（未繳足最低金額）二次（含）以上
	 * <p/>
	 * 票交所與聯徵特殊負面資訊
	 */
	public String getChkItem5() {
		return this.chkItem5;
	}

	/**
	 * 設定近12個月信用卡繳款狀況出現（未繳足最低金額）二次（含）以上
	 * <p/>
	 * 票交所與聯徵特殊負面資訊
	 **/
	public void setChkItem5(String value) {
		this.chkItem5 = value;
	}

	/**
	 * 取得近12個月信用卡繳款狀況出現（全額逾期連續未繳）二次（含）以上
	 * <p/>
	 * 票交所與聯徵特殊負面資訊
	 */
	public String getChkItem6() {
		return this.chkItem6;
	}

	/**
	 * 設定近12個月信用卡繳款狀況出現（全額逾期連續未繳）二次（含）以上
	 * <p/>
	 * 票交所與聯徵特殊負面資訊
	 **/
	public void setChkItem6(String value) {
		this.chkItem6 = value;
	}

	/**
	 * 取得近12個月信用卡有預借現金餘額二次（含）以上
	 * <p/>
	 * 票交所與聯徵特殊負面資訊
	 */
	public String getChkItem7() {
		return this.chkItem7;
	}

	/**
	 * 設定近12個月信用卡有預借現金餘額二次（含）以上
	 * <p/>
	 * 票交所與聯徵特殊負面資訊
	 **/
	public void setChkItem7(String value) {
		this.chkItem7 = value;
	}

	/**
	 * 取得近12個月現金卡有動用紀錄
	 * <p/>
	 * 票交所與聯徵特殊負面資訊
	 */
	public String getChkItem8() {
		return this.chkItem8;
	}

	/**
	 * 設定近12個月現金卡有動用紀錄
	 * <p/>
	 * 票交所與聯徵特殊負面資訊
	 **/
	public void setChkItem8(String value) {
		this.chkItem8 = value;
	}

	/** 取得基準底分A **/
	public BigDecimal getVarA() {
		return this.varA;
	}

	/** 設定基準底分A **/
	public void setVarA(BigDecimal value) {
		this.varA = value;
	}

	/** 取得基準底分B **/
	public BigDecimal getVarB() {
		return this.varB;
	}

	/** 設定基準底分B **/
	public void setVarB(BigDecimal value) {
		this.varB = value;
	}

	/** 取得基準底分常數項 **/
	public BigDecimal getVarC() {
		return this.varC;
	}

	/** 設定基準底分常數項 **/
	public void setVarC(BigDecimal value) {
		this.varC = value;
	}

	/** 取得基準底分版本 **/
	public String getVarVer() {
		return this.varVer;
	}

	/** 設定基準底分版本 **/
	public void setVarVer(String value) {
		this.varVer = value;
	}

	/** 取得報表亂碼 **/
	public String getRandomCode() {
		return this.randomCode;
	}

	/** 設定報表亂碼 **/
	public void setRandomCode(String value) {
		this.randomCode = value;
	}

	/**
	 * 取得調整狀態
	 * <p/>
	 * 1.調升 2.調降 3.回復
	 */
	public String getAdjustStatus() {
		return this.adjustStatus;
	}

	/**
	 * 設定調整狀態
	 * <p/>
	 * 1.調升 2.調降 3.回復
	 **/
	public void setAdjustStatus(String value) {
		this.adjustStatus = value;
	}

	/**
	 * 取得調整註記(升等)
	 * <p/>
	 * 1.淨資產2.職業3.其它4.一般保證人資信佳
	 */
	public String getAdjustFlag() {
		return this.adjustFlag;
	}

	/**
	 * 設定調整註記(升等)
	 * <p/>
	 * 1.淨資產2.職業3.其它4.一般保證人資信佳
	 **/
	public void setAdjustFlag(String value) {
		this.adjustFlag = value;
	}

	/** 取得調整理由 **/
	public String getAdjustReason() {
		return this.adjustReason;
	}

	/** 設定調整理由 **/
	public void setAdjustReason(String value) {
		this.adjustReason = value;
	}

	/** 取得未持有信用卡{Y.是}(值 和C101M01A.jcicFlg 第3碼 相反) */
	public String getCardFlag() {
		return this.cardFlag;
	}

	/** 設定未持有信用卡{Y.是}(值 和C101M01A.jcicFlg 第3碼 相反) */
	public void setCardFlag(String value) {
		this.cardFlag = value;
	}

	/**
	 * 取得用途別
	 * <p/>
	 * Add 2013/02/22
	 */
	public String getPurposeCode() {
		return this.purposeCode;
	}

	/**
	 * 設定用途別
	 * <p/>
	 * Add 2013/02/22
	 **/
	public void setPurposeCode(String value) {
		this.purposeCode = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}


	/** 
	 * 取得是否引用<p/>
	 * Y.是
	 */
	public String getQuote() {
		return this.quote;
	}
	/**
	 *  設定是否引用<p/>
	 *  Y.是
	 **/
	public void setQuote(String value) {
		this.quote = value;
	}


	/**
	 * 取得違約機率(預估3年期)
	 */
	public BigDecimal getDr_3YR() {
		return this.dr_3YR;
	}

	/**
	 * 設定違約機率(預估3年期)
	 **/
	public void setDr_3YR(BigDecimal value) {
		this.dr_3YR = value;
	}
	
	/**
	 * 取得違約機率(預估1年期)
	 */
	public BigDecimal getDr_1YR() {
		return this.dr_1YR;
	}

	/**
	 * 設定違約機率(預估1年期)
	 **/
	public void setDr_1YR(BigDecimal value) {
		this.dr_1YR = value;
	}

	/** 取得信用卡循環信用次數 **/	
	public BigDecimal getRevol_cnt() {
		return revol_cnt;
	}
	/** 設定信用卡循環信用次數 **/
	public void setRevol_cnt(BigDecimal revol_cnt) {
		this.revol_cnt = revol_cnt;
	}
	
	/** 取得職稱 **/
	public String getJobTitle() {
		return this.jobTitle;
	}
	/** 設定職稱 **/
	public void setJobTitle(String value) {
		this.jobTitle = value;
	}

	/** 取得年資 **/
	public BigDecimal getSeniority() {
		return this.seniority;
	}
	/** 設定年資 **/
	public void setSeniority(BigDecimal value) {
		this.seniority = value;
	}
	
	/** 取得年資分數_G  **/
	public BigDecimal getScrSeniority_G() {
		return scrSeniority_G;
	}
	/** 設定年資分數_G  **/
	public void setScrSeniority_G(BigDecimal scrSeniority_G) {
		this.scrSeniority_G = scrSeniority_G;
	}

	/** 取得個人年所得(年薪+其他收入)(萬元) **/
	public BigDecimal getPIncome() {
		return pIncome;
	}
	/** 設定個人年所得(年薪+其他收入)(萬元) **/
	public void setPIncome(BigDecimal pIncome) {
		this.pIncome = pIncome;
	}

	/** 取得D07_DIV_PINCOME因子 **/
	public BigDecimal getItemD07_DIV_PINCOME() {
		return itemD07_DIV_PINCOME;
	}
    /** 設定D07_DIV_PINCOME因子 **/
	public void setItemD07_DIV_PINCOME(BigDecimal itemD07_DIV_PINCOME) {
		this.itemD07_DIV_PINCOME = itemD07_DIV_PINCOME;
	}

    /** 取得D07_DIV_PINCOME分數 **/
	public BigDecimal getScrD07_DIV_PINCOME() {
		return scrD07_DIV_PINCOME;
	}
    /** 設定D07_DIV_PINCOME分數 **/
	public void setScrD07_DIV_PINCOME(BigDecimal scrD07_DIV_PINCOME) {
		this.scrD07_DIV_PINCOME = scrD07_DIV_PINCOME;
	}
	
    /** 取得P68因子 **/
	public BigDecimal getItemP68() {
		return itemP68;
	}
	/** 設定P68因子 **/
	public void setItemP68(BigDecimal itemP68) {
		this.itemP68 = itemP68;
	}
	
    /** 取得P68_P19分數 **/
	public BigDecimal getScrP68P19() {
		return scrP68P19;
	}
	/** 設定P68_P19分數 **/
	public void setScrP68P19(BigDecimal scrP68P19) {
		this.scrP68P19 = scrP68P19;
	}
	
	/** 取得N18次數因子 **/
	public BigDecimal getItemN18() {
		return itemN18;
	}
	/** 設定N18次數因子 **/
	public void setItemN18(BigDecimal itemN18) {
		this.itemN18 = itemN18;
	}
	
	/** 取得N22次數因子 **/
	public BigDecimal getItemN22() {
		return itemN22;
	}
	/** 設定N22次數因子 **/
	public void setItemN22(BigDecimal itemN22) {
		this.itemN22 = itemN22;
	}

	/** 取得N18_JobTitle分數 **/
	public BigDecimal getScrN18() {
		return scrN18;
	}
	/** 設定N18_JobTitle分數 **/
	public void setScrN18(BigDecimal scrN18) {
		this.scrN18 = scrN18;
	}
	
	/** 取得N22_JobTitle分數 **/
	public BigDecimal getScrN22() {
		return scrN22;
	}
	/** 設定N22_JobTitle分數 **/
	public void setScrN22(BigDecimal scrN22) {
		this.scrN22 = scrN22;
	}

	/** 取得Z03因子 **/
	public BigDecimal getItemZ03() {
		return itemZ03;
	}
	/** 設定Z03因子 **/
	public void setItemZ03(BigDecimal itemZ03) {
		this.itemZ03 = itemZ03;
	}

	/** 取得Z03分數 **/
	public BigDecimal getScrZ03() {
		return scrZ03;
	}
	/** 設定Z03分數 **/
	public void setScrZ03(BigDecimal scrZ03) {
		this.scrZ03 = scrZ03;
	}

	/** 取得一般保證人統編 **/
	public String getPosId() {
		return posId;
	}
	/** 設定一般保證人統編 **/
	public void setPosId(String posId) {
		this.posId = posId;
	}

	/** 取得一般保證人統編重複碼 **/
	public String getPosDupNo() {
		return posDupNo;
	}
	/** 設定一般保證人統編重複碼 **/
	public void setPosDupNo(String posDupNo) {
		this.posDupNo = posDupNo;
	}

	/** 取得一般保證人姓名 **/
	public String getPosName() {
		return posName;
	}
	/** 設定一般保證人姓名 **/
	public void setPosName(String posName) {
		this.posName = posName;
	}

	/** 取得一般保證人關係類別 **/
	public String getPosKindD() {
		return posKindD;
	}
	/** 設定一般保證人關係類別 **/
	public void setPosKindD(String posKindD) {
		this.posKindD = posKindD;
	}

	/** 取得擔保品類別 **/
	public String getIsKind() {
		return isKind;
	}
	/** 設定擔保品類別 **/
	public void setIsKind(String isKind) {
		this.isKind = isKind;
	}

	/** ----------消金房貸3.0 Start---------- **/
	/** 
	 * 取得近12個月新業務申請查詢總次數，計算方式-天數別(30天內算1次)分數<p/>
	 * 消金房貸3.0
	 */
	public BigDecimal getScrN18Only() {
		return this.scrN18Only;
	}
	/**
	 *  設定近12個月新業務申請查詢總次數，計算方式-天數別(30天內算1次)分數<p/>
	 *  消金房貸3.0
	 **/
	public void setScrN18Only(BigDecimal value) {
		this.scrN18Only = value;
	}

	/** 
	 * 取得夫妻負債比<p/>
	 * 消金房貸3.0
	 */
	public BigDecimal getYRate() {
		return this.yRate;
	}
	/**
	 *  設定夫妻負債比<p/>
	 *  消金房貸3.0
	 **/
	public void setYRate(BigDecimal value) {
		this.yRate = value;
	}

	/** 
	 * 取得夫妻負債比分數<p/>
	 * 消金房貸3.0
	 */
	public BigDecimal getScrYRate() {
		return this.scrYRate;
	}
	/**
	 *  設定夫妻負債比分數<p/>
	 *  消金房貸3.0
	 **/
	public void setScrYRate(BigDecimal value) {
		this.scrYRate = value;
	}

	/** 
	 * 取得職稱分數<p/>
	 * 消金房貸3.0
	 */
	public BigDecimal getScrJobTitle() {
		return this.scrJobTitle;
	}
	/**
	 *  設定職稱分數<p/>
	 *  消金房貸3.0
	 **/
	public void setScrJobTitle(BigDecimal value) {
		this.scrJobTitle = value;
	}

	/** 
	 * 取得當月有效信用卡主卡平均信用額度(仟元)<p/>
	 * 消金房貸3.0
	 */
	public BigDecimal getItemD42() {
		return this.itemD42;
	}
	/**
	 *  設定當月有效信用卡主卡平均信用額度(仟元)<p/>
	 *  消金房貸3.0
	 **/
	public void setItemD42(BigDecimal value) {
		this.itemD42 = value;
	}

	/** 
	 * 取得當月有效信用卡主卡平均信用額度(仟元)分數<p/>
	 * 消金房貸3.0
	 */
	public BigDecimal getScrD42() {
		return this.scrD42;
	}
	/**
	 *  設定當月有效信用卡主卡平均信用額度(仟元)分數<p/>
	 *  消金房貸3.0
	 **/
	public void setScrD42(BigDecimal value) {
		this.scrD42 = value;
	}

	/** 
	 * 取得學歷分數<p/>
	 * 消金房貸3.0
	 */
	public BigDecimal getScrEdu() {
		return this.scrEdu;
	}
	/**
	 *  設定學歷分數<p/>
	 *  消金房貸3.0
	 **/
	public void setScrEdu(BigDecimal value) {
		this.scrEdu = value;
	}

	/** 
	 * 取得近6個月信用卡繳款狀況出現不良繳款紀錄或使用循環信用的次數分數<p/>
	 * 消金房貸3.0
	 */
	public BigDecimal getScrP68() {
		return this.scrP68;
	}
	/**
	 *  設定近6個月信用卡繳款狀況出現不良繳款紀錄或使用循環信用的次數分數<p/>
	 *  消金房貸3.0
	 **/
	public void setScrP68(BigDecimal value) {
		this.scrP68 = value;
	}

	/** 
	 * 取得近3個月非Z類申請查詢總次數(近三個月本行查詢不列入計算)<p/>
	 * 消金房貸3.0
	 */
	public BigDecimal getItemN01() {
		return this.itemN01;
	}
	/**
	 *  設定近3個月非Z類申請查詢總次數(近三個月本行查詢不列入計算)<p/>
	 *  消金房貸3.0
	 **/
	public void setItemN01(BigDecimal value) {
		this.itemN01 = value;
	}

	/** 
	 * 取得近3個月非Z類申請查詢總次數(近三個月本行查詢不列入計算)分數<p/>
	 * 消金房貸3.0
	 */
	public BigDecimal getScrN01() {
		return this.scrN01;
	}
	/**
	 *  設定近3個月非Z類申請查詢總次數(近三個月本行查詢不列入計算)分數<p/>
	 *  消金房貸3.0
	 **/
	public void setScrN01(BigDecimal value) {
		this.scrN01 = value;
	}

	/** 
	 * 取得近12個月信用卡繳款狀況出現全額繳清無延遲次數(不含無須繳款)分數<p/>
	 * 消金房貸3.0
	 */
	public BigDecimal getScrP19() {
		return this.scrP19;
	}
	/**
	 *  設定近12個月信用卡繳款狀況出現全額繳清無延遲次數(不含無須繳款)分數<p/>
	 *  消金房貸3.0
	 **/
	public void setScrP19(BigDecimal value) {
		this.scrP19 = value;
	}
	
	/** 
	 * 取得截距<p/> 
	 * 消金房貸3.0
	 */
	public BigDecimal getInterCept() {
		return this.interCept;
	}
	/**
	 *  設定截距<p/> 
	 *  消金房貸3.0
	 **/
	public void setInterCept(BigDecimal value) {
		this.interCept = value;
	}

	/** 
	 * 取得斜率<p/> 
	 * 消金房貸3.0
	 */
	public BigDecimal getSlope() {
		return this.slope;
	}
	/**
	 *  設定斜率<p/> 
	 *  消金房貸3.0
	 **/
	public void setSlope(BigDecimal value) {
		this.slope = value;
	}
	/** ----------消金房貸3.0 End---------- **/
	
	/**
	 * join
	 */
	@OneToOne(cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	@JoinColumns({
			@JoinColumn(name = "mainId", referencedColumnName = "mainId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "ownBrId", referencedColumnName = "ownBrId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "custId", referencedColumnName = "custId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "dupNo", referencedColumnName = "dupNo", nullable = false, insertable = false, updatable = false) })
	private C120M01A c120m01a;

	public void setC120m01a(C120M01A c120m01a) {
		this.c120m01a = c120m01a;
	}

	public C120M01A getC120m01a() {
		return c120m01a;
	}

	/**
	 * 顯示用欄位
	 */
	/** 票交所與聯徵特殊負面資訊 **/
	@Transient
	private String alertMsg;

	public String getAlertMsg() {
		return this.alertMsg;
	}

	public void setAlertMsg(String value) {
		this.alertMsg = value;
	}
}
