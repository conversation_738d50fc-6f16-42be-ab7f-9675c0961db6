/*
 * IReportService.java
 *
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.report;

import java.io.OutputStream;

import com.iisigroup.cap.component.PageParameters;

import tw.com.iisi.cap.exception.CapException;

/**
 * <p>
 * 報表處理的主要介面，主要是產生檔案及計算頁面兩種方法。
 * </p>
 * 
 * <AUTHOR> Wang
 * @version $Revision$
 * @version <ul>
 *          <li>2011/6/13,Sunkist,new
 *          </ul>
 */
public interface IReportService {

	/**
	 * 報表產生的共同進入方法，會在這裡與i-net鏈結，帶入reportParameter，以及產生ByteArray的報表檔案。
	 * 
	 * @return OutputStream
	 * @throws CapException
	 */
	OutputStream generateReport() throws CapException;

	/**
	 * 報表產生的共同進入方法，會在這裡與i-net鏈結，帶入reportParameter，以及產生ByteArray的報表檔案。
	 * 
	 * @param params
	 *            PageParameters
	 * @return OutputStream
	 * @throws CapException
	 */
	OutputStream generateReport(PageParameters params) throws CapException;

	/**
	 * 產生報表以取得報表頁數。
	 * 
	 * @return OutputStream
	 * @throws CapException
	 */
	int getReportPageCount() throws CapException;

	/**
	 * 產生報表以取得報表頁數。
	 * 
	 * @param params
	 *            PageParameters
	 * @return OutputStream
	 * @throws CapException
	 */
	int getReportPageCount(PageParameters params) throws CapException;
}
