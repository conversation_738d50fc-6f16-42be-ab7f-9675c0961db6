package com.mega.eloan.lms.base.service;

import java.util.List;

/**
 * <pre>
 * 單筆資料轉換
 * </pre>
 * 
 * @since 2013/5/11
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/5/11, 007625 ,new
 *          </ul>
 */
public interface CLSDataConvertService {

	/**
	 * 建立lock File
	 * 
	 * @return true is Free, false is not Free
	 */
	boolean doLockFile();
	
	/**
	 * 單筆轉檔
	 * @param nsfName
	 * @param viewList
	 * @param mainId
	 * @param ip
	 * @param schema
	 * @param dbType
	 * @param brNo
	 */
	String doDataConvert(String nsfName, List<String> viewList, String mainId,
			String ip, String schema, String dbType, String brNo, String... l120m01aMainId);
	
	/**
	 * 判斷單資料轉檔是否在執行中
	 * 
	 * @return true is Free, false is not Free
	 */
	boolean checkIsOK();

	boolean doUnLockFile();

}
