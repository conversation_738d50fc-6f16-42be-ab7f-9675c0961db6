/* 
 * L140M01E_AFDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140M01E_AF;

/** 動審後額度聯行攤貸比例檔 **/
public interface L140M01E_AFDao extends IGenericDao<L140M01E_AF> {

	L140M01E_AF findByOid(String oid);
	
	List<L140M01E_AF> findByOids(String[] oids);
	
	List<L140M01E_AF> findByMainId(String mainId);

	List<L140M01E_AF> findByIndex01(String mainId, String flag, String shareBrId);
	
	L140M01E_AF findByUniqueKey(String mainId, String shareBrId);

	List<L140M01E_AF> findByMainIdAndFlag(String mainId, String[] Flag);
	
	List<L140M01E_AF> findByCntrNo(String CntrNo);
	
	List<L140M01E_AF> findByCustIdDupId(String custId,String DupNo);
}