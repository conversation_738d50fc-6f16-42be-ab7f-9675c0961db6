package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.L901M01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L901M01A;

/** 動用審核表稽核項目 **/
@Repository
public class L901M01ADaoImpl extends LMSJpaDao<L901M01A, String> implements
		L901M01ADao {

	@Override
	public L901M01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L901M01A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L901M01A> list = createQuery(L901M01A.class, search).getResultList();
		return list;
	}

	@Override
	public List<L901M01A> findByItemTypeAndbranchId(String itemType,
			String branchId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "branchId", branchId);
		search.addSearchModeParameters(SearchMode.EQUALS, "itemType", itemType);
		List<L901M01A> list = createQuery(L901M01A.class, search).getResultList();
		return list;
	}

	@Override
	public List<L901M01A> findByItemTypeAndbranchId(String itemType,
			String branchId, String locale) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "branchId", branchId);
		search.addSearchModeParameters(SearchMode.EQUALS, "itemType", itemType);
		search.addSearchModeParameters(SearchMode.EQUALS, "locale", locale);
		List<L901M01A> list = createQuery(L901M01A.class, search).getResultList();
		return list;
	}
}