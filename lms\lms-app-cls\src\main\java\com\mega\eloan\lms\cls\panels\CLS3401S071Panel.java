
package com.mega.eloan.lms.cls.panels;

import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * 中鋼集團消貸線上契約(C340M01A_CtrType.Type_C)
 * </pre>
 * 
 * @since 2021/11/26
 * <AUTHOR>
 * @version <ul>
 *          <li>2021/11/26,EL08034,J-110-0373
 *          </ul>
 */
public class CLS3401S071Panel extends Panel {

	public CLS3401S071Panel(String id) {
		super(id);
		//add(new DocLogPanel("_docLog"));	
	}

	public CLS3401S071Panel(String id, boolean updatePanelName) {
		super(id, updatePanelName);
	}

	private static final long serialVersionUID = 1L;
}
