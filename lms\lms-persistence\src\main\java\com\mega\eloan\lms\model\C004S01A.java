/* 
 * C004S01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 政策性留學生貸款報送資料Q檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C004S01A", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class C004S01A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** mainId **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 分行代號 **/
	@Size(max=3)
	@Column(name="BRNO", length=3, columnDefinition="CHAR(3)")
	private String brno;

	/** 件數 **/
	@Digits(integer=5, fraction=0, groups = Check.class)
	@Column(name="NUM", columnDefinition="DECIMAL(5)")
	private Integer num;

	/** 貸款金額 **/
	@Digits(integer=11, fraction=0, groups = Check.class)
	@Column(name="AMT", columnDefinition="DECIMAL(11)")
	private BigDecimal amt;

	/** 積欠利息 **/
	@Digits(integer=11, fraction=0, groups = Check.class)
	@Column(name="OWEINTEREST", columnDefinition="DECIMAL(11)")
	private BigDecimal oweInterest;

	/** 逾期利息 **/
	@Digits(integer=11, fraction=0, groups = Check.class)
	@Column(name="DUEINTEREST", columnDefinition="DECIMAL(11)")
	private BigDecimal dueInterest;

	/** 訴訟費用 **/
	@Digits(integer=11, fraction=0, groups = Check.class)
	@Column(name="LITIGATE", columnDefinition="DECIMAL(11)")
	private BigDecimal litigate;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得mainId **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定mainId **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得分行代號 **/
	public String getBrno() {
		return this.brno;
	}
	/** 設定分行代號 **/
	public void setBrno(String value) {
		this.brno = value;
	}

	/** 取得件數 **/
	public Integer getNum() {
		return this.num;
	}
	/** 設定件數 **/
	public void setNum(Integer value) {
		this.num = value;
	}

	/** 取得貸款金額 **/
	public BigDecimal getAmt() {
		return this.amt;
	}
	/** 設定貸款金額 **/
	public void setAmt(BigDecimal value) {
		this.amt = value;
	}

	/** 取得積欠利息 **/
	public BigDecimal getOweInterest() {
		return this.oweInterest;
	}
	/** 設定積欠利息 **/
	public void setOweInterest(BigDecimal value) {
		this.oweInterest = value;
	}

	/** 取得逾期利息 **/
	public BigDecimal getDueInterest() {
		return this.dueInterest;
	}
	/** 設定逾期利息 **/
	public void setDueInterest(BigDecimal value) {
		this.dueInterest = value;
	}

	/** 取得訴訟費用 **/
	public BigDecimal getLitigate() {
		return this.litigate;
	}
	/** 設定訴訟費用 **/
	public void setLitigate(BigDecimal value) {
		this.litigate = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
