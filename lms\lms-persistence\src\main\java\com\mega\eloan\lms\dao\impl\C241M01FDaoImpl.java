package com.mega.eloan.lms.dao.impl;

import java.util.LinkedHashMap;
import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.C241M01FDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C241M01F;


/** 防杜代辦覆審_承辦行員 **/
@Repository
public class C241M01FDaoImpl extends LMSJpaDao<C241M01F, String> implements
		C241M01FDao {

	@Override
	public C241M01F findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C241M01F> findByMainId_inOrder(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.setMaxResults(Integer.MAX_VALUE);
		if(true){
			LinkedHashMap<String, Boolean> printSeqMap = new LinkedHashMap<String, Boolean>();
			printSeqMap.put("caseYear", false);
			printSeqMap.put("caseSeq", false);
			printSeqMap.put("rel_type", false);
			printSeqMap.put("seq", false);
			search.setOrderBy(printSeqMap);
		}
		return find(search);
	}
}