/* 
 * ColConfig.java
 * 
 * Copyright (c) 2009-2012 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.constants;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;

import net.lingala.zip4j.core.ZipFile;
import net.lingala.zip4j.exception.ZipException;
import net.lingala.zip4j.model.ZipParameters;
import net.lingala.zip4j.util.Zip4jConstants;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ZipUtils {
	private final static Logger logger = LoggerFactory.getLogger("ZipUtils");

	/**
	 * 使用給定密碼壓縮指定檔案或資料夾到指定位置. dest可傳最終壓縮檔案存放的絕對路徑,也可以傳存放目錄,也可以傳null或者"".
	 * 如果傳null或者""則將壓縮檔案存放在當前目錄,即跟原始檔同目錄,壓縮檔名取原始檔名,以.zip為字尾;
	 * 如果以路徑分隔符(File.separator)結尾,則視為目錄,壓縮檔名取原始檔名,以.zip為字尾,否則視為檔名.
	 * 
	 * @param src
	 *            要壓縮的檔案或資料夾路徑
	 * @param dest
	 *            壓縮檔案存放路徑
	 * @param isCreateDir
	 *            是否在壓縮檔案裡建立目錄,僅在壓縮檔案為目錄時有效. 如果為false,將直接壓縮目錄下檔案到壓縮檔案.
	 * @param passwd
	 *            壓縮使用的密碼
	 * @return 最終的壓縮檔案存放的絕對路徑,如果為null則說明壓縮失敗.
	 */
	public static String zip(String src, String dest, boolean isCreateDir,
			String passwd) {
		File srcFile = new File(src);
		dest = buildDestinationZipFilePath(srcFile, dest);// 獲取壓縮檔案的存放路徑
		ZipParameters zipParameters = new ZipParameters();
		zipParameters.setCompressionMethod(Zip4jConstants.COMP_DEFLATE);// 設定壓縮方法是deflate
		zipParameters.setCompressionLevel(Zip4jConstants.DEFLATE_LEVEL_NORMAL);// 設定壓縮級別
		if (passwd != null && passwd.trim().length() > 0) {
			// 如果密碼不為空，壓縮包進行加密,需要設定壓縮演算法
			zipParameters.setEncryptFiles(true);
			zipParameters
					.setEncryptionMethod(Zip4jConstants.ENC_METHOD_STANDARD);// 加密演算法設定為standard
			zipParameters.setPassword(passwd.toCharArray());
		}
		try {
			ZipFile zipFile = new ZipFile(dest);
			if (srcFile.isDirectory()) {
				if (!isCreateDir) {// 如果false，表示不按照目錄結構進行壓縮
					ArrayList<File> list = new ArrayList<File>();
					Collections.addAll(list, srcFile.listFiles());
					zipFile.addFiles(list, zipParameters);
				} else {
					// 按照目錄結構壓縮
					zipFile.addFolder(srcFile, zipParameters);
				}
			} else {
				zipFile.addFile(srcFile, zipParameters);
			}
		} catch (ZipException e) {
			logger.error("-----檔案壓縮失敗-----");
			logger.error(e.getMessage());
			dest = null;
		}
		return dest;

	}

	/**
	 * 構建壓縮檔案存放路徑,如果不存在將會建立 * 傳入的可能是檔名或者目錄,也可能不傳,此方法用以轉換最終壓縮檔案的存放路徑 *
	 * 
	 * @param srcFile
	 *            原始檔
	 * @param destParam
	 *            壓縮目標路徑 *
	 * @return 正確的壓縮檔案存放路徑
	 */
	private static String buildDestinationZipFilePath(File srcFile,
			String destParam) {
		if (destParam == null || destParam.trim() == "") {
			if (srcFile.isDirectory()) {
				destParam = srcFile.getParent() + File.separator
						+ srcFile.getName() + ".zip";
			} else {
				String fileName = srcFile.getName().substring(0,
						srcFile.getName().lastIndexOf("."));
				destParam = srcFile.getParent() + File.separator + fileName
						+ ".zip";
			}
		} else {
			createDestDirectoryIfNecessary(destParam); // 在指定路徑不存在的情況下將其創建出來
			if (destParam.endsWith(File.separator)) {
				String fileName = "";
				if (srcFile.isDirectory()) {
					fileName = srcFile.getName();
				} else {
					fileName = srcFile.getName().substring(0,
							srcFile.getName().lastIndexOf("."));
				}
				destParam += fileName + ".zip";
			}
		}
		return destParam;
	}

	/**
	 * 建立壓縮檔案存放目錄
	 * 
	 * @param destParam
	 */
	private static void createDestDirectoryIfNecessary(String destParam) {
		File file = null;
		if (destParam.endsWith(File.separator)) {
			file = new File(destParam);
		} else {
			file = new File(destParam.substring(0,
					destParam.lastIndexOf(File.separator)));
		}
		if (file.exists()) {
			file.mkdirs();
		}
	}
}