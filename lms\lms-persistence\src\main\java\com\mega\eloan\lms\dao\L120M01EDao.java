/* 
 * L120M01EDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120M01E;

/** 相關文件資料檔 **/
public interface L120M01EDao extends IGenericDao<L120M01E> {

	L120M01E findByOid(String oid);

	List<L120M01E> findByMainId(String mainId);

	List<L120M01E> findByMainIdAndDocType(String mainId, String docType);

	L120M01E findByUniqueKey(String mainId, String docType, String docURL,
			String docOid);

	List<L120M01E> findByIndex01(String mainId, String docType, String docURL,
			String docOid);

	List<L120M01E> findByCustIdDupId(String custId, String DupNo);

	List<L120M01E> findByOids(String[] oids);
	
	List<L120M01E> findByMainIdAndDocTypeDocCustIdDocDupNo(String mainId, String docType, String docCustId, String docDupNo);
}