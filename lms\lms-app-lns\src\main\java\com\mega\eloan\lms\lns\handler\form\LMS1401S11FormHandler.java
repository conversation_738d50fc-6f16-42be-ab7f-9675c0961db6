/*
 * lms1401s11formhandler.java
 *
 * Copyright (c) 2011-2012 JC Software Services, Inc.
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 *
 * Licensed Materials - Property of JC Software Services, Inc.
 *
 * This software is confidential and proprietary information of
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lns.handler.form;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.constants.SysParamConstants;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.base.common.BranchRate;
import com.mega.eloan.lms.base.common.Rate;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.LMSBisService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.lns.panels.LMS1401S03Panel;
import com.mega.eloan.lms.lns.panels.LMS1401S11Panel;
import com.mega.eloan.lms.lns.panels.LMSS07APanel;
import com.mega.eloan.lms.lns.service.LMS1201Service;
import com.mega.eloan.lms.lns.service.LMS1401Service;
import com.mega.eloan.lms.mfaloan.bean.LNF07A;
import com.mega.eloan.lms.mfaloan.service.LNLNF07AService;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120S23A;
import com.mega.eloan.lms.model.L120S24A;
import com.mega.eloan.lms.model.L120S25A;
import com.mega.eloan.lms.model.L120S25C;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.PropUtil;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 額度明細表 - BIS
 * </pre>
 * 
 * @since 2022/08/
 * <AUTHOR>
 * @version <ul>
 *          <li>2022/08/,009301,new
 *          </ul>
 */
@Scope("request")
@Controller("lms1401s11formhandler")
public class LMS1401S11FormHandler extends AbstractFormHandler {

	@Resource
	LMSService lmsService;

	@Resource
	LMS1201Service lms1201Service;

	@Resource
	LMS1401Service lms1401Service;

	@Resource
	LNLNF07AService lnLnf07aService;

	@Resource
	LMSBisService lmsBisService;

	@Resource
	private SysParameterService sysParameterService;

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult showPanelLms140s11(PageParameters params) {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		L120M01A l120m01a = lmsService
				.findModelByMainId(L120M01A.class, mainId);
		if (l120m01a == null) {
			l120m01a = new L120M01A();
		}
		result.set("showPanelLms140s11",
				lmsService.showPanelLms140s11(l120m01a) ? "Y" : "N");
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL120S25A(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.nullToSpace(params.getString(EloanConstants.OID));

		L120S25A l120s25a = lms1201Service.findL120s25aByOid(oid);
		if (l120s25a == null) {
			l120s25a = new L120S25A();
		}
		result = DataParse.toResult(l120s25a, DataParse.Delete, new String[] {
				EloanConstants.OID, EloanConstants.MAIN_ID, "creator",
				"createTime", "updater", "updateTime" });
		result.set("oidL120S25A", CapString.trimNull(l120s25a.getOid()));

		// J-111-0443_05097_B1005 Web e-Loan企金授信開發授信BIS評估表
		List<L120S25A> listL120s25a = null;

		listL120s25a = lms1201Service.findL120s25aByMainIdAndCustId(
				l120s25a.getMainId(), l120s25a.getBisCustId_s25a(),
				l120s25a.getBisDupNo_s25a());

		BigDecimal rItemD = (l120s25a.getBisRItemD() == null ? BigDecimal.ZERO
				: l120s25a.getBisRItemD()); // %
		if (rItemD.compareTo(BigDecimal.ZERO) == 0) {
			result.set("bisRorwa", "N.A.");
			result.set("bisRorwa_1", "N.A.");
			result.set("bisRaroc", "N.A.");
			result.set("bisRaroc_1", "N.A.");
			result.set("bisImpactNum", "N.A.");
		}

		if (!lmsBisService.showBisTotalNotNa(listL120s25a)) {

			result.set("bisTotRItemD", "N.A.");
			result.set("bisTotRwa", "N.A.");
			result.set("bisTotRorwa", "N.A.");
			result.set("bisTotRorwa_1", "N.A.");
			result.set("bisTotRaroc", "N.A.");
			result.set("bisTotRaroc_1", "N.A.");
			result.set("bisTotImpactNum", "N.A.");
			result.set("bisTotIncomeRate", "N.A.");
		}

		// J-112-0389_05097_B1001 Web e-Loan調整企金e-Loan之RORWA風險成本計算方式
		if (lmsBisService.compareBisVersion(l120s25a, "<",
				new BigDecimal("1.3"))) {
			result.set("isShowBisBadDebtExp", "Y");
		} else {
			result.set("isShowBisBadDebtExp", "N");
		}

		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult importL120S25A(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));

		List<L120S25A> l120s25aList = new ArrayList<L120S25A>();
		List<L140M01A> listL140m01a = lms1401Service
				.findL140m01aListByL120m01cMainId(mainId,
						UtilConstants.Cntrdoc.ItemType.額度明細表);
		if (listL140m01a != null && !listL140m01a.isEmpty()) {
			for (L140M01A l140m01a : listL140m01a) {
				if (Util.isNotEmpty(Util.nullToSpace(l140m01a.getCntrNo()))) {
					if (l140m01a != null) {
						if (lmsService.chkCntrNoNeedBis(l140m01a)) {
							L120S25A l120s25a = null;

							List<L120S25A> l120s25as = lms1201Service
									.findL120s25aByMainIdAndCntrNo(mainId,
											l140m01a.getCntrNo());
							if (l120s25as != null && !l120s25as.isEmpty()) {
								l120s25a = l120s25as.get(0);
							} else {
								l120s25a = new L120S25A();
							}

							this.l140m01aToL120s25a(mainId, l140m01a, l120s25a);
							l120s25aList.add(l120s25a);
						}

					}
				}
			}
		}

		if (l120s25aList.size() > 0) {

			List<L120S25A> l120s25asAll = lms1201Service
					.findL120s25aByMainId(mainId);
			if (l120s25asAll != null && !l120s25asAll.isEmpty()) {

				List<String> listOid = new ArrayList<String>();
				for (L120S25A model : l120s25asAll) {

					boolean needDel = true;
					for (L120S25A l120s25a : l120s25aList) {
						if (Util.equals(model.getBisCntrNo_s25a(),
								l120s25a.getBisCntrNo_s25a())) {
							needDel = false;
							break;
						}
					}

					if (needDel) {
						listOid.add(model.getOid());
					}

				}

				lms1201Service.deleteL120s25aList(listOid
						.toArray(new String[0]));
			}

			lms1201Service.saveL120s25aList(l120s25aList);
			// 計算加總欄位
			// this.calcTotColumn(mainId);
		}

		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult importL120S25A_OLD(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		String[] oids = params.getStringArray("oids");
		List<L120S25A> l120s25aList = new ArrayList<L120S25A>();
		if (oids.length > 0) {
			for (String oid : oids) {
				L140M01A l140m01a = lms1401Service.findModelByOid(
						L140M01A.class, oid);
				if (l140m01a != null) {
					L120S25A l120s25a = null;

					List<L120S25A> l120s25as = lms1201Service
							.findL120s25aByMainIdAndCntrNo(mainId,
									l140m01a.getCntrNo());
					if (l120s25as != null && !l120s25as.isEmpty()) {
						l120s25a = l120s25as.get(0);
					} else {
						l120s25a = new L120S25A();
					}

					this.l140m01aToL120s25a(mainId, l140m01a, l120s25a);
					l120s25aList.add(l120s25a);
				}
			}
		}
		if (l120s25aList.size() > 0) {
			lms1201Service.saveL120s25aList(l120s25aList);
			// 計算加總欄位
			// this.calcTotColumn(mainId);
		}

		return result;
	}

	public void l140m01aToL120s25a(String mainId, L140M01A l140m01a,
			L120S25A l120s25a) throws CapMessageException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		BranchRate branchRate = lmsService.getBranchRate(user.getUnitNo());
		if (l120s25a == null) {
			l120s25a = new L120S25A();
		}

		// J-112-0389_05097_B1001 Web e-Loan調整企金e-Loan之RORWA風險成本計算方式
		String LMS_BIS_VERSION_1 = Util.trim(lmsService
				.getSysParamDataValue("LMS_BIS_VERSION_1"));
		String LMS_BIS_VERSION_2 = Util.trim(lmsService
				.getSysParamDataValue("LMS_BIS_VERSION_2"));
		// 設定版本
		l120s25a.setBisVer1(Util.isEmpty(LMS_BIS_VERSION_1) ? 0 : Util
				.parseInt(LMS_BIS_VERSION_1));
		l120s25a.setBisVer2(Util.isEmpty(LMS_BIS_VERSION_2) ? 0 : Util
				.parseInt(LMS_BIS_VERSION_2));

		if (l140m01a != null) {
			l120s25a.setMainId(mainId);
			l120s25a.setBisCustId_s25a(Util.nullToSpace(l140m01a.getCustId()));
			l120s25a.setBisDupNo_s25a(Util.nullToSpace(l140m01a.getDupNo()));
			l120s25a.setBisCntrNo_s25a(Util.nullToSpace(l140m01a.getCntrNo()));
			l120s25a.setBisProPerty(Util.nullToSpace(l140m01a.getProPerty()));
			l120s25a.setBisApplyCurr(Util.nullToSpace(l140m01a
					.getCurrentApplyCurr()));
			l120s25a.setBisApplyAmt(l140m01a.getCurrentApplyAmt());

			l120s25a.setBisTwdRate(Util.equals(
					Util.nullToSpace(l120s25a.getBisApplyCurr()), "") ? BigDecimal.ONE
					: branchRate.toTWDRate(
							Util.nullToSpace(l120s25a.getBisApplyCurr()))
							.setScale(5, BigDecimal.ROUND_HALF_UP));

			// ===================企金處RWA=========================
			List<L120S23A> l120s23aList = lms1201Service
					.findL120s23aByMainIdCntrNo(l120s25a.getMainId(),
							l120s25a.getBisCntrNo_s25a());
			if (l120s23aList != null && !l120s23aList.isEmpty()
					&& l120s23aList.size() > 0) {
				L120S23A tmp = l120s23aList.get(0);
				if (tmp != null) {
					// J-111-0443_05097_B1005 Web e-Loan企金授信開發授信BIS評估表
					// RWA要有值才更新
					if (tmp.getIncomeRate() != null) {
						l120s25a.setBisIncomeRate(tmp.getIncomeRate()); // 授信收益率
					}
					if (tmp.getFtpRate() != null) {
						l120s25a.setBisFtpRate(tmp.getFtpRate()); // FTP
					}

				}
			}
			// ===================企金處RWA END=========================

			if (false) {
				// 改成引進L120S24A
				// ****表內/表外、信用轉換係數*************************************
				String[] sheetItemAndCcf = lmsService
						.getSheetItemAndCcf(l140m01a);
				if (sheetItemAndCcf.length > 1) {
					l120s25a.setBisSheetItem(sheetItemAndCcf[0]);
					l120s25a.setBisCcf(Util.parseBigDecimal(sheetItemAndCcf[1]));
				}
			}

			// J-112-0389_05097_B1001 Web e-Loan調整企金e-Loan之RORWA風險成本計算方式
			// 企業模型評等各等級擬制呆帳損失差異率(表訂)
			if (lmsBisService.compareBisVersion(l120s25a, "<", new BigDecimal(
					"1.3"))) {
				String bisBadDebtExp = lmsService.getLossDiffRate(l140m01a);
				l120s25a.setBisBadDebtExp(Util.isEmpty(bisBadDebtExp) ? null
						: new BigDecimal(bisBadDebtExp));
			} else {
				l120s25a.setBisBadDebtExp(null);
			}

			// 清除計算欄位
			this.initCalcColumn(l120s25a);

			// 預先引進資料，之後計算還是會重新引進
			String tErrMsg = this.importL120s24aData(l120s25a);
			if (Util.notEquals(tErrMsg, "")) {
				// L120S25A.errMsg01=額度序號『{0}』請先完成風險權數計算。
				throw new CapMessageException(tErrMsg, getClass());
			}

			l120s25a.setCreator(user.getUserId());
			l120s25a.setCreateTime(CapDate.getCurrentTimestamp());

			// J-112-0225_05097_B1001 Web
			// e-Loan企金授信調整BIS評估表FTP引用資料
			l120s25a.setBisCostRateType("02"); // FTP =>資金成本

		}

	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult deleteL120S25A(PageParameters params) throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		// String cntrNoCo_s21a = Util.nullToSpace(params
		// .getString("cntrNoCo_s21a"));

		// 取得list中所有資料組成的字串
		String listOid = params.getString("listOid");
		// 取得sign的資料
		String sign = Util.trim(params.getString("sign"));
		// 將已取得的字串轉換成一陣列，分割辨識為sign內容
		String[] oidArray = listOid.split(sign);

		if (oidArray.length > 0) {
			lms1201Service.deleteL120s25aList(oidArray);
		}

		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));

		return result;

	}

	/**
	 * 清除引進與計算欄位
	 * 
	 * @param l120s25a
	 */
	public void initCalcColumn(L120S25A l120s25a) {

		// J-110-0485_05097_B1009_B Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
		String LMS_BIS_VERSION_1 = Util.trim(lmsService
				.getSysParamDataValue("LMS_BIS_VERSION_1"));
		String LMS_BIS_VERSION_2 = Util.trim(lmsService
				.getSysParamDataValue("LMS_BIS_VERSION_2"));

		// J-112-0389_05097_B1001 Web
		// e-Loan調整企金e-Loan之RORWA風險成本計算方式
		// 要先設定版本，才能確定用最新版本來算
		// 設定版本
		l120s25a.setBisVer1(Util.isEmpty(LMS_BIS_VERSION_1) ? 0 : Util
				.parseInt(LMS_BIS_VERSION_1));
		l120s25a.setBisVer2(Util.isEmpty(LMS_BIS_VERSION_2) ? 0 : Util
				.parseInt(LMS_BIS_VERSION_2));

		// l120s25a.setBisVer1(null); // BIS評估表版本_大版
		// l120s25a.setBisVer2(null); // BIS評估表版本_小版

		l120s25a.setBisSheetItem(null); // 表內外
		l120s25a.setBisCcf(null); // 信用轉換係數

		l120s25a.setBisBankWorkCost(null); // 營運成本
		l120s25a.setBisExptLoss(null); // 預期損失率
		l120s25a.setBisCollAmt(null); // 合格擔保品抵減額
		l120s25a.setBisRskAmt1(null); // 風險抵減後暴險額

		l120s25a.setBisRiskCost(null); // 風險成本(預期損失)
		l120s25a.setBisRiskAdjReturn(null); // 風險調整後收益
		l120s25a.setBisRiskAdjReturn_1(null); // 風險調整後收益
		l120s25a.setBisRItemD(null); // 抵減後風險權數
		l120s25a.setBisRwa(null); // 風險抵減後風險性資產
		l120s25a.setBisRorwa(null); // RORWA
		l120s25a.setBisRorwa_1(null); // RORWA
		l120s25a.setBisRaroc(null); // RAROC
		l120s25a.setBisRaroc_1(null); // RAROC
		l120s25a.setBisImpactNum(null); // BIS影響數
		l120s25a.setBisCapitalReqYear(null); // 標準法資本要求年度
		l120s25a.setBisCapitalReq(null); // 標準法資本要求
		l120s25a.setBisSelfCapital(null); // 自有資本
		l120s25a.setBisMegaRwa(null); // 全行風險性資產

		// TOTAL********************************
		l120s25a.setBisTotRItemD(null); // 抵減後風險權數加權平均
		l120s25a.setBisTotRwa(null); // 風險抵減後風險性資產加總
		l120s25a.setBisTotRorwa(null); // RORWA加權平均
		l120s25a.setBisTotRorwa_1(null); // RORWA加權平均
		l120s25a.setBisTotRaroc(null); // RAROC加權平均
		l120s25a.setBisTotRaroc_1(null); // RAROC加權平均
		l120s25a.setBisTotImpactNum(null); // BIS影響數加總
		l120s25a.setBisTotIncomeRate(null); // 授信收益率加權平均

		// J-111-0443_05097_B1005 Web e-Loan企金授信開發授信BIS評估表
		l120s25a.setBisResult(null); // 檢視結果 ，抵減後風險權數為0代表這筆不用納入計算與合計，要顯示N.A.

	}

	public String importL120s24aData(L120S25A l120s25a) {
		String errMsg = "";

		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1401S11Panel.class);

		l120s25a.setBisApplyCurr(null);
		l120s25a.setBisApplyAmt(null);
		l120s25a.setBisTwdRate(null);
		l120s25a.setBisSheetItem(null);
		l120s25a.setBisSheetItem(null);

		l120s25a.setBisSheetItem(null);
		l120s25a.setBisCcf(null);
		l120s25a.setBisCollAmt(null); // 合計_合格擔保品抵減金額 totalDisCollAmt_s24a
		l120s25a.setBisRskAmt1(null); // 合格擔保品抵減後暴險額 calDisCollExposureAmt_s24a
		l120s25a.setBisRItemD(null); // 抵減後風險權數 calDeductRW_s24a
		l120s25a.setBisRwa(null); // 抵減後風險性資產 calDeductRWA_s24a

		String mainId = l120s25a.getMainId();
		String cntrNo = l120s25a.getBisCntrNo_s25a();

		L140M01A l140m01a = lmsService.findL140M01AByL120m01cMainIdAndcntrNo(
				mainId, cntrNo, UtilConstants.Cntrdoc.ItemType.額度明細表);
		if (l140m01a == null) {
			l140m01a = new L140M01A();
		}

		// ===================等思恩 L120S24A=========================

		List<L120S24A> l120s24aList = lms1201Service
				.findL120s24aByMainIdCntrNo(mainId, cntrNo);
		if (l120s24aList != null && !l120s24aList.isEmpty()
				&& l120s24aList.size() > 0) {
			L120S24A l120s24a = l120s24aList.get(0);
			if (l120s24a != null) {

				l120s25a.setBisApplyCurr(l120s24a.getCurrentApplyCurr_s24a());
				l120s25a.setBisApplyAmt(l120s24a.getCurrentApplyAmt_s24a());
				l120s25a.setBisTwdRate(l120s24a.getRateLoanToLoc_s24a());

				l120s25a.setBisSheetItem(Util.trim(l120s24a.getOnlyGuar_s24a()));
				l120s25a.setBisCcf(Util.parseBigDecimal(l120s24a.getCcf_s24a()));

				l120s25a.setBisCollAmt(l120s24a.getTotalDisCollAmt_TWD_s24a()); // 合計_合格擔保品抵減金額
				// totalDisCollAmt_s24a
				l120s25a.setBisRskAmt1(l120s24a
						.getCalDisCollExposureAmt_TWD_s24a()); // 合格擔保品抵減後暴險額
																// calDisCollExposureAmt_s24a
				l120s25a.setBisRItemD(l120s24a.getCalDeductRW_s24a()); // 抵減後風險權數
																		// calDeductRW_s24a
				l120s25a.setBisRwa(l120s24a.getCalDeductRWA_TWD_s24a()); // 抵減後風險性資產
																			// calDeductRWA_s24a

				// J-112-0542_05097_B1001 Web e-Loan企金授信調整BIS評估表保證案件額度之計算方式
				l120s25a.setOnlyGuar_s25a(l120s24a.getOnlyGuar_s24a());

			}
		} else {
			String currentApplyCurr = Util.trim(l140m01a.getCurrentApplyCurr());
			BigDecimal currentApplyAmt = l140m01a.getCurrentApplyAmt();
			// 簽報書主檔
			L120M01A l120m01a = lms1201Service.findL120m01aByMainId(mainId);
			// 計算折台幣用
			BranchRate branchRate = lmsService.getBranchRate(l120m01a
					.getCaseBrId());
			HashMap<String, Rate> misRateMap = branchRate.getMisRateMap();

			Rate rate = misRateMap.get(currentApplyCurr);

			l120s25a.setBisApplyCurr(currentApplyCurr);
			l120s25a.setBisApplyAmt(currentApplyAmt);
			l120s25a.setBisTwdRate(rate.getRate().stripTrailingZeros());

			l120s25a.setBisRwa(null);
			l120s25a.setBisRItemD(null);
			l120s25a.setBisCollAmt(null); // 合計_合格擔保品抵減金額
			l120s25a.setBisRskAmt1(null); // 合格擔保品抵減後暴險額
											// calDisCollExposureAmt_s24a
			l120s25a.setBisSheetItem(null); // 本額度僅有保證科目、應收信用狀款項科目
			l120s25a.setBisCcf(null);// 表外項目信用轉換係數(CCF)

			// J-112-0542_05097_B1001 Web e-Loan企金授信調整BIS評估表保證案件額度之計算方式
			l120s25a.setOnlyGuar_s25a(null);

			// 3.分行沒建風險權數 要從簽報書引抵減後風險權數來算
			// 現請額度*抵減後風險權數=抵減後風險性資產
			if (l140m01a != null) {
				BigDecimal rItemD = l140m01a.getRItemD();
				BigDecimal applyAmt = (l120s25a.getBisApplyAmt() == null ? BigDecimal.ZERO
						: l120s25a.getBisApplyAmt());
				BigDecimal twdRate = (l120s25a.getBisTwdRate() == null ? BigDecimal.ONE
						: l120s25a.getBisTwdRate());
				BigDecimal applyAmtTwd = applyAmt.multiply(twdRate);
				if (rItemD != null) {
					BigDecimal BisRwa = applyAmtTwd.multiply(
							rItemD.divide(new BigDecimal(100), 4,
									BigDecimal.ROUND_HALF_UP)).setScale(2,
							BigDecimal.ROUND_HALF_UP);
					l120s25a.setBisRwa(BisRwa);
					l120s25a.setBisRItemD(rItemD);
				}
			}

			String[] ccfInfo = lmsService.getSheetItemAndCcf(l140m01a);
			if (ccfInfo != null && ccfInfo.length >= 2) {
				if ("2".equals(ccfInfo[0])) {
					// 表外
					l120s25a.setBisSheetItem("Y"); // 本額度僅有保證科目、應收信用狀款項科目
					l120s25a.setBisCcf(Util.parseBigDecimal(new BigDecimal(
							ccfInfo[1])));// 表外項目信用轉換係數(CCF)

					// J-112-0542_05097_B1001 Web e-Loan企金授信調整BIS評估表保證案件額度之計算方式
					l120s25a.setOnlyGuar_s25a("Y");// 本額度僅有保證科目、應收信用狀款項科目

				} else {
					// 表內
					l120s25a.setBisSheetItem("N");

					// J-112-0542_05097_B1001 Web e-Loan企金授信調整BIS評估表保證案件額度之計算方式
					l120s25a.setOnlyGuar_s25a("N");

				}
			}

		}

		// ===================等思恩 L120S24A END=========================
		if (l120s25a.getBisRItemD() == null || l120s25a.getBisRwa() == null) {

			// L120S25A.errMsg01=額度序號『{0}』請先完成風險權數計算或額度明細表抵減後風險權數填列。
			String msg = MessageFormat.format(
					prop.getProperty("L120S25A.errMsg01"), cntrNo);

			errMsg = msg;
		}

		// J-112-0542_05097_B1001 Web e-Loan企金授信調整BIS評估表保證案件額度之計算方式
		String bisCostRateType = Util.equals(
				Util.trim(l120s25a.getBisCostRateType()), "") ? "02" : Util
				.trim(l120s25a.getBisCostRateType()); // FTP、資金成本
		if (Util.equals(bisCostRateType, "02")) {
			// 資金成本
			if (Util.equals(l120s25a.getOnlyGuar_s25a(), "Y")) {
				// A.本額度是否僅有保證科目
				// A=是，資金成本 欄位=0
				// A=否，資金成本 欄位不調整
				l120s25a.setBisFtpRate(BigDecimal.ZERO);
			}
		}

		return errMsg;
	}

	public String chkColumn(L120S25A l120s25a) {

		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1401S11Panel.class);

		// 欄位未輸入檢核BGN*********************************************************
		StringBuffer temp = new StringBuffer();

		// 統計未填欄位數
		int countItme = 1;

		BigDecimal bisIncomeRate = l120s25a.getBisIncomeRate(); // 授信收益率
		if (Util.isEmpty(bisIncomeRate)) {
			// errMsg07=「{0}」欄位不得空白！！
			// L120S25A.bisIncomeRate=授信收益率
			countItme = this.setHtmlBr(temp, countItme,
					pop.getProperty("L120S25A.bisIncomeRate"));
		}

		BigDecimal bisFtpRate = l120s25a.getBisFtpRate(); // FTP
		if (Util.isEmpty(bisFtpRate)) {
			// errMsg07=「{0}」欄位不得空白！！
			// J-112-0225_05097_B1001 Web e-Loan企金授信調整BIS評估表FTP引用資料
			// L120S25A.bisFtpRate=FTP
			// L120S25A.bisCapitalCost=資金成本
			countItme = this.setHtmlBr(temp, countItme,
					pop.getProperty("L120S25A.bisCapitalCost"));
		}

		// J-112-0389_05097_B1001 Web e-Loan調整企金e-Loan之RORWA風險成本計算方式
		if (lmsBisService.compareBisVersion(l120s25a, "<",
				new BigDecimal("1.3"))) {
			BigDecimal bisBadDebtExp = l120s25a.getBisBadDebtExp(); // 企業模型評等各等級擬制呆帳損失差異率(表訂)
			if (Util.isEmpty(bisBadDebtExp)) {
				// errMsg07=「{0}」欄位不得空白！！
				// L120S25A.bisBadDebtExp=企業模型評等各等級擬制呆帳損失差異率(表訂)
				countItme = this.setHtmlBr(temp, countItme,
						pop.getProperty("L120S25A.bisBadDebtExp"));
			}
		}

		String bisEstimatedType = l120s25a.getBisEstimatedType(); // 非授信收益預估對象
		if (Util.isEmpty(bisEstimatedType)) {
			// errMsg07=「{0}」欄位不得空白！！
			// L120S25A.bisEstimatedType=非授信收益預估對象
			countItme = this.setHtmlBr(temp, countItme,
					pop.getProperty("L120S25A.bisEstimatedType"));
		}

		// BigDecimal bisEstimatedReturn = l120s25a.getBisEstimatedReturn(); //
		// 預估非授信收益率
		// if (Util.isEmpty(bisEstimatedReturn)) {
		// // errMsg07=「{0}」欄位不得空白！！
		// // L120S25A.bisEstimatedReturn=預估非授信收益率
		// countItme = this.setHtmlBr(temp, countItme,
		// pop.getProperty("L120S25A.bisEstimatedReturn"));
		// }

		BigDecimal bisNoneLoanProfit = l120s25a.getBisNoneLoanProfit(); // 近一年非授信貢獻度
		BigDecimal bisNoneLoanProfit_1 = l120s25a.getBisNoneLoanProfit_1();
		if (Util.isEmpty(bisNoneLoanProfit)
				|| Util.isEmpty(bisNoneLoanProfit_1)) {
			// errMsg07=「{0}」欄位不得空白！！
			// L120S25A.bisNoneLoanProfit=近一年非授信貢獻度
			countItme = this.setHtmlBr(temp, countItme,
					pop.getProperty("L120S25A.bisNoneLoanProfit"));
		}

		BigDecimal bisLoanBal = l120s25a.getBisLoanBal(); // 近一年授信餘額
		BigDecimal bisLoanBal_1 = l120s25a.getBisLoanBal_1();
		if (Util.isEmpty(bisLoanBal) || Util.isEmpty(bisLoanBal_1)) {
			// errMsg07=「{0}」欄位不得空白！！
			// L120S25A.bisLoanBal=近一年授信餘額
			countItme = this.setHtmlBr(temp, countItme,
					pop.getProperty("L120S25A.bisLoanBal"));
		}

		BigDecimal bisFactAmtIncrease = l120s25a.getBisFactAmtIncrease(); // 新作增額額度
		BigDecimal bisFactAmtIncrease_1 = l120s25a.getBisFactAmtIncrease_1();
		if (Util.isEmpty(bisFactAmtIncrease)
				|| Util.isEmpty(bisFactAmtIncrease_1)) {
			// errMsg07=「{0}」欄位不得空白！！
			// L120S25A.bisFactAmtIncrease=新作增額額度
			countItme = this.setHtmlBr(temp, countItme,
					pop.getProperty("L120S25A.bisFactAmtIncrease"));
		}

		if (temp.length() > 0) {
			// L120S25A.errMsg02=尚有必填欄位未填
			temp.insert(0, pop.getProperty("L120S25A.errMsg02") + "<br/>");

		}

		String bisCostRateType = Util.trim(l120s25a.getBisCostRateType());
		if (Util.equals(bisCostRateType, "01")
				|| Util.equals(bisCostRateType, "")) {
			// J-112-0225_05097_B1001 Web e-Loan企金授信調整BIS評估表FTP引用資料
			temp.append(temp.length() > 0 ? "、" : "");
			// L120S25A.errMsg04=選單「FTP」請改選「資金成本」，並填入正確資金成本數值。
			temp.append("<br/>");
			String msg = pop.getProperty("L120S25A.errMsg04");
			temp.append(msg);
		}

		if (temp.length() > 0) {
			temp.insert(0, l120s25a.getBisCntrNo_s25a());
		}

		return temp.toString();
	}

	/**
	 * 設定 訊息換列 目前設定五筆 就換列
	 * 
	 * @param temp
	 *            暫存文字
	 * @param countItme
	 *            計算欄位數量
	 * @param showMessage
	 * @return countItme 目前的 計算數量
	 */
	public int setHtmlBr(StringBuffer temp, int countItme, String showMessage) {
		int maxLenth = 5;
		temp.append(temp.length() > 0 ? "、" : "");
		if (countItme > maxLenth) {
			temp.append("<br/>");
			countItme = 1;
		} else {
			countItme++;
		}
		temp.append(showMessage);
		return countItme;
	}

	public String calcColumn(L120S25A l120s25a) {
		String errMsg = "";
		if (l120s25a != null) {
			// J-112-0389_05097_B1001 Web e-Loan調整企金e-Loan之RORWA風險成本計算方式
			if (lmsBisService.compareBisVersion(l120s25a, "<", new BigDecimal(
					"1.3"))) {
				// 風險成本(預期損失)= 預期損失率 + 企業模型評等各等級擬制呆帳損失差異率(表訂)
				if (l120s25a.getBisExptLoss() == null
						&& l120s25a.getBisBadDebtExp() == null) {
					l120s25a.setBisRiskCost(null);
				} else {
					BigDecimal exptLoss = (l120s25a.getBisExptLoss() == null ? BigDecimal.ZERO
							: l120s25a.getBisExptLoss()); // %
					BigDecimal badDebtExp = (l120s25a.getBisBadDebtExp() == null ? BigDecimal.ZERO
							: l120s25a.getBisBadDebtExp()); // %
					l120s25a.setBisRiskCost(exptLoss.add(badDebtExp)); // %
				}
			} else {
				// 1.調整「風險成本(預期損失)」之名稱為「風險成本(全行平均)」
				// 2.風險成本(全行平均) =【預期損失率】。
				if (l120s25a.getBisExptLoss() == null) {
					l120s25a.setBisRiskCost(null);
				} else {
					BigDecimal exptLoss = (l120s25a.getBisExptLoss() == null ? BigDecimal.ZERO
							: l120s25a.getBisExptLoss()); // %
					l120s25a.setBisRiskCost(exptLoss); // %
				}
			}

			// 風險調整後收益 = 收益率% – FTP(資金成本)% – 營運成本%– 風險成本(預期損失)% + 預估非授信收益率%
			BigDecimal inCome = (l120s25a.getBisIncomeRate() == null ? BigDecimal.ZERO
					: l120s25a.getBisIncomeRate()); // %
			BigDecimal ftp = (l120s25a.getBisFtpRate() == null ? BigDecimal.ZERO
					: l120s25a.getBisFtpRate()); // %
			BigDecimal cost = (l120s25a.getBisBankWorkCost() == null ? BigDecimal.ZERO
					: l120s25a.getBisBankWorkCost()); // %
			BigDecimal risk = (l120s25a.getBisRiskCost() == null ? BigDecimal.ZERO
					: l120s25a.getBisRiskCost()); // %

			BigDecimal bisNoneLoanProfit = (l120s25a.getBisNoneLoanProfit() == null ? BigDecimal.ZERO
					: l120s25a.getBisNoneLoanProfit()); // A.近一年非授信貢獻度
			BigDecimal bisLoanBal = (l120s25a.getBisLoanBal() == null ? BigDecimal.ZERO
					: l120s25a.getBisLoanBal()); // B.近一年授信餘額
			BigDecimal bisFactAmtIncrease = (l120s25a.getBisFactAmtIncrease() == null ? BigDecimal.ZERO
					: l120s25a.getBisFactAmtIncrease()); // C.新作增額額度
			BigDecimal bisEstimatedReturn = BigDecimal.ZERO; // D.預估非授信收益率 %
			if ((bisLoanBal.add(bisFactAmtIncrease)).compareTo(BigDecimal.ZERO) > 0) {
				bisEstimatedReturn = bisNoneLoanProfit.divide(
						bisLoanBal.add(bisFactAmtIncrease), 4,
						BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)); // D=
																					// A
																					// /
																					// (B
																					// +
																					// C)
			}
			l120s25a.setBisEstimatedReturn(bisEstimatedReturn);

			BigDecimal bisNoneLoanProfit_1 = (l120s25a.getBisNoneLoanProfit_1() == null ? BigDecimal.ZERO
					: l120s25a.getBisNoneLoanProfit_1()); // A.近一年非授信貢獻度
			BigDecimal bisLoanBal_1 = (l120s25a.getBisLoanBal_1() == null ? BigDecimal.ZERO
					: l120s25a.getBisLoanBal_1()); // B.近一年授信餘額
			BigDecimal bisFactAmtIncrease_1 = (l120s25a
					.getBisFactAmtIncrease_1() == null ? BigDecimal.ZERO
					: l120s25a.getBisFactAmtIncrease_1()); // C.新作增額額度
			BigDecimal bisEstimatedReturn_1 = BigDecimal.ZERO; // D.預估非授信收益率 %
			if ((bisLoanBal_1.add(bisFactAmtIncrease_1))
					.compareTo(BigDecimal.ZERO) > 0) {
				bisEstimatedReturn_1 = bisNoneLoanProfit_1.divide(
						bisLoanBal_1.add(bisFactAmtIncrease_1), 4,
						BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
			}
			l120s25a.setBisEstimatedReturn_1(bisEstimatedReturn_1);

			// BigDecimal bisEstimatedReturn = (l120s25a.getBisEstimatedReturn()
			// == null ? BigDecimal.ZERO
			// : l120s25a.getBisEstimatedReturn()); // %

			l120s25a.setBisRiskAdjReturn((inCome.subtract(ftp).subtract(cost)
					.subtract(risk).add(bisEstimatedReturn)).setScale(5,
					BigDecimal.ROUND_HALF_UP));

			l120s25a.setBisRiskAdjReturn_1((inCome.subtract(ftp).subtract(cost)
					.subtract(risk).add(bisEstimatedReturn_1)).setScale(5,
					BigDecimal.ROUND_HALF_UP));

			// RORWA% = 風險調整後收益% / 抵減後風險權數(%) * 100
			BigDecimal adj = (l120s25a.getBisRiskAdjReturn() == null ? BigDecimal.ZERO
					: l120s25a.getBisRiskAdjReturn()); // %
			BigDecimal rItemD = (l120s25a.getBisRItemD() == null ? BigDecimal.ZERO
					: l120s25a.getBisRItemD()); // %
			BigDecimal adj_1 = (l120s25a.getBisRiskAdjReturn_1() == null ? BigDecimal.ZERO
					: l120s25a.getBisRiskAdjReturn_1()); // %

			// J-111-0443_05097_B1005 Web e-Loan企金授信開發授信BIS評估表
			// if (rItemD.compareTo(BigDecimal.ZERO) == 0) {
			// l120s25a.setBisResult("N");
			// } else {
			// l120s25a.setBisResult("Y");
			// }

			BigDecimal rorwa = BigDecimal.ZERO;
			if (adj.compareTo(BigDecimal.ZERO) != 0
					&& rItemD.compareTo(BigDecimal.ZERO) != 0) {
				rorwa = adj.multiply(new BigDecimal(100)).divide(rItemD, 5,
						BigDecimal.ROUND_HALF_UP); // %
			}
			l120s25a.setBisRorwa(rorwa.setScale(5, BigDecimal.ROUND_HALF_UP)); // %

			BigDecimal rorwa_1 = BigDecimal.ZERO;
			if (adj_1.compareTo(BigDecimal.ZERO) != 0
					&& rItemD.compareTo(BigDecimal.ZERO) != 0) {
				rorwa_1 = adj_1.multiply(new BigDecimal(100)).divide(rItemD, 5,
						BigDecimal.ROUND_HALF_UP); // %
			}
			l120s25a.setBisRorwa_1(rorwa_1
					.setScale(5, BigDecimal.ROUND_HALF_UP)); // %

			// RAROC% = RORWA% / 標準法資本要求% * 100

			// 標準法資本要求(標準法資本要求 12.5% 13.5% 14.0% 14.5%， 單位%)，要除以100
			BigDecimal req = (l120s25a.getBisCapitalReq() == null ? BigDecimal.ZERO
					: l120s25a.getBisCapitalReq()); // %

			BigDecimal raroc = BigDecimal.ZERO;
			if (req.compareTo(BigDecimal.ZERO) != 0) {
				raroc = rorwa.multiply(new BigDecimal(100)).divide(req, 5,
						BigDecimal.ROUND_HALF_UP);
			} else {
				l120s25a.setBisRaroc(null);
			}
			l120s25a.setBisRaroc(raroc.setScale(5, BigDecimal.ROUND_HALF_UP));

			BigDecimal raroc_1 = BigDecimal.ZERO;
			if (req.compareTo(BigDecimal.ZERO) != 0) {
				raroc_1 = rorwa_1.multiply(new BigDecimal(100)).divide(req, 5,
						BigDecimal.ROUND_HALF_UP);
			} else {
				l120s25a.setBisRaroc_1(null);
			}
			l120s25a.setBisRaroc_1(raroc_1
					.setScale(5, BigDecimal.ROUND_HALF_UP));

			// BIS影響數 =〔自有資本/(全行風險性資產+風險抵減後風險性資產)〕- (自有資本/全行風險性資產)

			BigDecimal self = (l120s25a.getBisSelfCapital() == null ? BigDecimal.ZERO
					: l120s25a.getBisSelfCapital());
			BigDecimal megaRwa = (l120s25a.getBisMegaRwa() == null ? BigDecimal.ZERO
					: l120s25a.getBisMegaRwa());
			BigDecimal rwa = (l120s25a.getBisRwa() == null ? BigDecimal.ZERO
					: l120s25a.getBisRwa());
			BigDecimal denominator = megaRwa.add(rwa);
			BigDecimal r1 = null;
			BigDecimal r2 = null;
			if (denominator.compareTo(BigDecimal.ZERO) != 0) {
				r1 = self.divide(denominator, 10, BigDecimal.ROUND_HALF_UP);
			}
			if (megaRwa.compareTo(BigDecimal.ZERO) != 0) {
				r2 = self.divide(megaRwa, 10, BigDecimal.ROUND_HALF_UP);
			}
			if (r1 != null && r2 != null) {
				l120s25a.setBisImpactNum(((r1.subtract(r2))
						.multiply(new BigDecimal(100))).setScale(5,
						BigDecimal.ROUND_HALF_UP)); // %
			}

		}

		return errMsg;
	}

	public void calcTotColumn(String mainId) {

		// J-112-0389_05097_B1001 Web e-Loan調整企金e-Loan之RORWA風險成本計算方式
		// J-110-0485_05097_B1009_B Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
		// String LMS_BIS_VERSION_1 = Util.trim(lmsService
		// .getSysParamDataValue("LMS_BIS_VERSION_1"));
		// String LMS_BIS_VERSION_2 = Util.trim(lmsService
		// .getSysParamDataValue("LMS_BIS_VERSION_2"));

		// BY ID歸戶
		Map<String, String> custIdMap = new HashMap<String, String>();

		List<L120S25A> l120s25aListAll = lms1201Service
				.findL120s25aByMainId(mainId);

		if (l120s25aListAll != null && !l120s25aListAll.isEmpty()) {
			for (L120S25A l120s25a : l120s25aListAll) {
				String custId = Util.trim(l120s25a.getBisCustId_s25a());
				String dupNo = Util.trim(l120s25a.getBisDupNo_s25a());
				String fullId = custId + "-" + dupNo;
				if (!custIdMap.containsKey(fullId)) {
					custIdMap.put(fullId, fullId);
				}
			}
		}

		// BY借款人
		for (String key : custIdMap.keySet()) {
			String custId = key.split("-")[0];
			String dupNo = key.split("-")[1];

			List<L120S25A> l120s25aList = lms1201Service
					.findL120s25aByMainIdAndCustId(mainId, custId, dupNo);

			if (l120s25aList != null && !l120s25aList.isEmpty()) {
				// 抵減後風險權數加權平均 = [加總(現請額度*抵減後風險權數)]/加總現請額度
				BigDecimal totRItemD = BigDecimal.ZERO;
				// 風險抵減後風險性資產 加總 = bisRwa 加總
				BigDecimal totRwa = BigDecimal.ZERO;
				// RORWA加權平均 = [加總(現請額度*RORWA)]/加總現請額度
				BigDecimal totRorwa = BigDecimal.ZERO;
				BigDecimal totRorwa_1 = BigDecimal.ZERO;
				// RAROC加權平均 = [加總(現請額度*RAROC)]/加總現請額度
				BigDecimal totRaroc = BigDecimal.ZERO;
				BigDecimal totRaroc_1 = BigDecimal.ZERO;
				// BIS影響數加總 = bisImpactNum 加總
				BigDecimal totImpactNum = BigDecimal.ZERO;
				// 授信收益率加權平均 = [加總(現請額度*授信收益率)]/加總現請額度
				BigDecimal totIncomeRate = BigDecimal.ZERO;

				// 現請額度新台幣加總
				BigDecimal totApplyAmtTwd = BigDecimal.ZERO;
				// 加總新台幣(現請額度*抵減後風險權數)
				BigDecimal totRItemDTwd = BigDecimal.ZERO;
				// 加總新台幣(現請額度*RORWA)
				BigDecimal totRorwaTwd = BigDecimal.ZERO;
				BigDecimal totRorwaTwd_1 = BigDecimal.ZERO;
				// 加總新台幣(現請額度*RAROC)
				BigDecimal totRarocTwd = BigDecimal.ZERO;
				BigDecimal totRarocTwd_1 = BigDecimal.ZERO;
				// 授信收益率加權平均
				BigDecimal totIncomeRateTwd = BigDecimal.ZERO;

				for (L120S25A l120s25a : l120s25aList) {
					if (l120s25a != null && Util.isNotEmpty(l120s25a)) {

						// J-111-0443_05097_B1005 Web e-Loan企金授信開發授信BIS評估表
						BigDecimal rItemD = (l120s25a.getBisRItemD() == null ? BigDecimal.ZERO
								: l120s25a.getBisRItemD()); // %
						if (rItemD.compareTo(BigDecimal.ZERO) == 0) {
							// 若抵減後風險權數不納入
							continue;
						}

						BigDecimal applyAmt = (l120s25a.getBisApplyAmt() == null ? BigDecimal.ZERO
								: l120s25a.getBisApplyAmt());
						BigDecimal twdRate = (l120s25a.getBisTwdRate() == null ? BigDecimal.ONE
								: l120s25a.getBisTwdRate());
						BigDecimal applyAmtTwd = applyAmt.multiply(twdRate);

						BigDecimal rorwa = (l120s25a.getBisRorwa() == null ? BigDecimal.ZERO
								: l120s25a.getBisRorwa()); // %
						BigDecimal raroc = (l120s25a.getBisRaroc() == null ? BigDecimal.ZERO
								: l120s25a.getBisRaroc()); // %
						BigDecimal rorwa_1 = (l120s25a.getBisRorwa_1() == null ? BigDecimal.ZERO
								: l120s25a.getBisRorwa_1()); // %
						BigDecimal raroc_1 = (l120s25a.getBisRaroc_1() == null ? BigDecimal.ZERO
								: l120s25a.getBisRaroc_1()); // %

						BigDecimal incomeRate = (l120s25a.getBisIncomeRate() == null ? BigDecimal.ZERO
								: l120s25a.getBisIncomeRate()); // %

						BigDecimal rwa = (l120s25a.getBisRwa() == null ? BigDecimal.ZERO
								: l120s25a.getBisRwa());
						BigDecimal impactNum = (l120s25a.getBisImpactNum() == null ? BigDecimal.ZERO
								: l120s25a.getBisImpactNum());// %

						totApplyAmtTwd = totApplyAmtTwd.add(applyAmtTwd);
						totRItemDTwd = applyAmtTwd.multiply(rItemD).add(
								totRItemDTwd);
						totRorwaTwd = applyAmtTwd.multiply(rorwa).add(
								totRorwaTwd);
						totRarocTwd = applyAmtTwd.multiply(raroc).add(
								totRarocTwd);
						totRorwaTwd_1 = applyAmtTwd.multiply(rorwa_1).add(
								totRorwaTwd_1);
						totRarocTwd_1 = applyAmtTwd.multiply(raroc_1).add(
								totRarocTwd_1);
						totIncomeRateTwd = applyAmtTwd.multiply(incomeRate)
								.add(totIncomeRateTwd);

						totRwa = totRwa.add(rwa);
						totImpactNum = totImpactNum.add(impactNum);
					}
				}

				if (totApplyAmtTwd.compareTo(BigDecimal.ZERO) != 0) {
					if (totRItemDTwd.compareTo(BigDecimal.ZERO) != 0) {
						totRItemD = totRItemDTwd.divide(totApplyAmtTwd, 2,
								BigDecimal.ROUND_HALF_UP);
					}
					if (totRorwaTwd.compareTo(BigDecimal.ZERO) != 0) {
						totRorwa = totRorwaTwd.divide(totApplyAmtTwd, 5,
								BigDecimal.ROUND_HALF_UP);
					}
					if (totRarocTwd.compareTo(BigDecimal.ZERO) != 0) {
						totRaroc = totRarocTwd.divide(totApplyAmtTwd, 5,
								BigDecimal.ROUND_HALF_UP);
					}
					if (totRorwaTwd_1.compareTo(BigDecimal.ZERO) != 0) {
						totRorwa_1 = totRorwaTwd_1.divide(totApplyAmtTwd, 5,
								BigDecimal.ROUND_HALF_UP);
					}
					if (totRarocTwd_1.compareTo(BigDecimal.ZERO) != 0) {
						totRaroc_1 = totRarocTwd_1.divide(totApplyAmtTwd, 5,
								BigDecimal.ROUND_HALF_UP);
					}

					if (totIncomeRateTwd.compareTo(BigDecimal.ZERO) != 0) {
						totIncomeRate = totIncomeRateTwd.divide(totApplyAmtTwd,
								5, BigDecimal.ROUND_HALF_UP);
					}
				}

				for (L120S25A l120s25a : l120s25aList) {
					if (l120s25a != null && Util.isNotEmpty(l120s25a)) {
						l120s25a.setBisTotRItemD(totRItemD.setScale(2,
								BigDecimal.ROUND_HALF_UP));
						l120s25a.setBisTotRwa(totRwa.setScale(2,
								BigDecimal.ROUND_HALF_UP));
						l120s25a.setBisTotRorwa(totRorwa.setScale(5,
								BigDecimal.ROUND_HALF_UP));
						l120s25a.setBisTotRaroc(totRaroc.setScale(5,
								BigDecimal.ROUND_HALF_UP));
						l120s25a.setBisTotRorwa_1(totRorwa_1.setScale(5,
								BigDecimal.ROUND_HALF_UP));
						l120s25a.setBisTotRaroc_1(totRaroc_1.setScale(5,
								BigDecimal.ROUND_HALF_UP));
						l120s25a.setBisTotImpactNum(totImpactNum.setScale(5,
								BigDecimal.ROUND_HALF_UP));

						l120s25a.setBisTotIncomeRate(totIncomeRate.setScale(5,
								BigDecimal.ROUND_HALF_UP)); // 授信收益率加權平均

						// J-112-0389_05097_B1001 Web
						// e-Loan調整企金e-Loan之RORWA風險成本計算方式
						// 設定版本
						// l120s25a.setBisVer1(Util.isEmpty(LMS_BIS_VERSION_1) ?
						// 0
						// : Util.parseInt(LMS_BIS_VERSION_1));
						// l120s25a.setBisVer2(Util.isEmpty(LMS_BIS_VERSION_2) ?
						// 0
						// : Util.parseInt(LMS_BIS_VERSION_2));

					}
				}
				lms1201Service.saveL120s25aList(l120s25aList);
			}
		}
	}
	
	/**
	 * J-113-0327 授信收益率及新作增額額度檢核
	 * 若首次儲存值為0時，出提示訊息
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult checkBeforeSaveL120S25A(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		Properties prop = MessageBundleScriptCreator.getComponentResource(LMS1401S11Panel.class);
		String oid = Util.trim(params.getString("oid"));
		BigDecimal bisIncomeRate = Util.parseBigDecimal(NumConverter
				.delCommaString(params.getString("bisIncomeRate"))); // 授信收益率
		BigDecimal bisFactAmtIncrease = Util.parseBigDecimal(NumConverter
				.delCommaString(params.getString("bisFactAmtIncrease"))); //新作增額額度_個體
		BigDecimal bisFactAmtIncrease_1 = Util.parseBigDecimal(NumConverter
				.delCommaString(params.getString("bisFactAmtIncrease_1"))); //新作增額額度_集團
		boolean chkBisIncomeRate = false;
		boolean chkBisFactAmtIncrease = false;
		StringBuffer chkMsg = new StringBuffer();
		if(Util.isNotEmpty(oid)){
			L120S25A l120s25a = lms1201Service.findL120s25aByOid(oid);
			if (l120s25a != null) {
				if(BigDecimal.ZERO.compareTo(bisIncomeRate) == 0 && l120s25a.getBisIncomeRate() == null){
					chkBisIncomeRate = true;
				}
				if(BigDecimal.ZERO.compareTo(bisFactAmtIncrease) == 0 && l120s25a.getBisFactAmtIncrease() == null){
					chkBisFactAmtIncrease = true;
				}
				if(BigDecimal.ZERO.compareTo(bisFactAmtIncrease_1) == 0 && l120s25a.getBisFactAmtIncrease_1() == null){
					chkBisFactAmtIncrease = true;
				}
			}
		}
		if(chkBisIncomeRate){
			chkMsg.append(prop.getProperty("L120S25A.chkMsg1") + "<br>");
		}
		if(chkBisFactAmtIncrease){
			chkMsg.append(prop.getProperty("L120S25A.chkMsg2") + "<br>");
		}
		
		if(Util.isNotEmpty(chkMsg.toString())){
			result.set("chkMsg", chkMsg.toString());
		}
		
		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL120S25A(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String oid = Util.trim(params.getString("oid"));
		String errMsg = "";

		result.set("bisEstimatedReturn", "");
		result.set("bisEstimatedReturn_1", "");
		
		if (Util.isNotEmpty(oid)) {
			L120S25A l120s25a = lms1201Service.findL120s25aByOid(oid);
			if (l120s25a != null) {
				String bisSheetItem = Util.nullToSpace(params
						.getString("bisSheetItem"));
				l120s25a.setBisSheetItem(bisSheetItem);
				String bisCcf = Util.nullToSpace(params.getString("bisCcf"));
				l120s25a.setBisCcf(Util.equals(bisSheetItem, "2") ? Util
						.parseBigDecimal(bisCcf) : null);
				BigDecimal bisIncomeRate = Util.parseBigDecimal(NumConverter
						.delCommaString(params.getString("bisIncomeRate")));
				l120s25a.setBisIncomeRate(bisIncomeRate);
				BigDecimal bisFtpRate = Util.parseBigDecimal(NumConverter
						.delCommaString(params.getString("bisFtpRate")));
				l120s25a.setBisFtpRate(bisFtpRate);
				String bisMemo = params.getString("bisMemo");
				l120s25a.setBisMemo(bisMemo);

				String bisEstimatedType = params.getString("bisEstimatedType");
				l120s25a.setBisEstimatedType(bisEstimatedType);

				String bisEstimatedDateYear = Util.nullToSpace(params
						.getString("bisEstimatedDateYear"));
				l120s25a.setBisEstimatedDateYear(bisEstimatedDateYear);

				String bisEstimatedDateMonth = Util.nullToSpace(params
						.getString("bisEstimatedDateMonth"));
				l120s25a.setBisEstimatedDateMonth(bisEstimatedDateMonth);

				BigDecimal bisEstimatedReturn = Util
						.parseBigDecimal(NumConverter.delCommaString(params
								.getString("bisEstimatedReturn")));
				l120s25a.setBisEstimatedReturn(bisEstimatedReturn);

				BigDecimal bisNoneLoanProfit = Util
						.parseBigDecimal(NumConverter.delCommaString(params
								.getString("bisNoneLoanProfit")));
				l120s25a.setBisNoneLoanProfit(bisNoneLoanProfit);

				BigDecimal bisLoanBal = Util.parseBigDecimal(NumConverter
						.delCommaString(params.getString("bisLoanBal")));
				l120s25a.setBisLoanBal(bisLoanBal);

				BigDecimal bisFactAmtIncrease = Util
						.parseBigDecimal(NumConverter.delCommaString(params
								.getString("bisFactAmtIncrease")));
				l120s25a.setBisFactAmtIncrease(bisFactAmtIncrease);

				BigDecimal bisEstimatedReturn_1 = Util
						.parseBigDecimal(NumConverter.delCommaString(params
								.getString("bisEstimatedReturn_1")));
				l120s25a.setBisEstimatedReturn_1(bisEstimatedReturn_1);

				BigDecimal bisNoneLoanProfit_1 = Util
						.parseBigDecimal(NumConverter.delCommaString(params
								.getString("bisNoneLoanProfit_1")));
				l120s25a.setBisNoneLoanProfit_1(bisNoneLoanProfit_1);

				BigDecimal bisLoanBal_1 = Util.parseBigDecimal(NumConverter
						.delCommaString(params.getString("bisLoanBal_1")));
				l120s25a.setBisLoanBal_1(bisLoanBal_1);

				BigDecimal bisFactAmtIncrease_1 = Util
						.parseBigDecimal(NumConverter.delCommaString(params
								.getString("bisFactAmtIncrease_1")));
				l120s25a.setBisFactAmtIncrease_1(bisFactAmtIncrease_1);

				// J-112-0225_05097_B1001 Web e-Loan企金授信調整BIS評估表FTP引用資料
				l120s25a.setBisCostRateType(Util.equals(
						Util.trim(params.getString("bisCostRateType")), "") ? "02"
						: Util.trim(params.getString("bisCostRateType"))); // FTP
				// =>資金成本

				// ===================等思恩 L120S24A
				// 可以更改嗎?=========================
				// BigDecimal bisCollAmt = Util.parseBigDecimal(NumConverter
				// .delCommaString(params.getString("bisCollAmt")));
				// l120s25a.setBisCollAmt(bisCollAmt);
				// BigDecimal bisRskAmt1 = Util.parseBigDecimal(NumConverter
				// .delCommaString(params.getString("bisRskAmt1")));
				// l120s25a.setBisRskAmt1(bisRskAmt1);
				// BigDecimal bisRItemD = Util.parseBigDecimal(NumConverter
				// .delCommaString(params.getString("bisRItemD")));
				// l120s25a.setBisRItemD(bisRItemD);
				// BigDecimal bisRwa = Util.parseBigDecimal(NumConverter
				// .delCommaString(params.getString("bisRwa")));
				// l120s25a.setBisRwa(bisRwa);
				// ===================等思恩 L120S24A END=========================

				// 計算相關欄位
				// this.calcColumn(l120s25a);

				// 清除計算欄位
				this.initCalcColumn(l120s25a);

				// 檢核欄位
				String tMsg = this.chkColumn(l120s25a);
				if (Util.notEquals(tMsg, "")) {
					throw new CapMessageException(tMsg, getClass());
				}

				// 小計
				this.calcL120s25aColumn(l120s25a);
				result.set("bisEstimatedReturn",
						l120s25a.getBisEstimatedReturn());
				result.set("bisEstimatedReturn_1",
						l120s25a.getBisEstimatedReturn_1());
				lms1201Service.save(l120s25a);

				// 計算加總欄位
				// this.calcTotColumn(l120s25a.getMainId());
			}
		}
		if (Util.isNotEmpty(errMsg)) {
			result.set("msg", errMsg);
		}
				
		return result;
	}

	public void calcL120s25aColumn(L120S25A l120s25a) {

		BigDecimal bisNoneLoanProfit = (l120s25a.getBisNoneLoanProfit() == null ? BigDecimal.ZERO
				: l120s25a.getBisNoneLoanProfit()); // A.近一年非授信貢獻度
		BigDecimal bisLoanBal = (l120s25a.getBisLoanBal() == null ? BigDecimal.ZERO
				: l120s25a.getBisLoanBal()); // B.近一年授信餘額
		BigDecimal bisFactAmtIncrease = (l120s25a.getBisFactAmtIncrease() == null ? BigDecimal.ZERO
				: l120s25a.getBisFactAmtIncrease()); // C.新作增額額度
		BigDecimal bisEstimatedReturn = BigDecimal.ZERO; // D.預估非授信收益率 %
		if ((bisLoanBal.add(bisFactAmtIncrease)).compareTo(BigDecimal.ZERO) > 0) {
			bisEstimatedReturn = bisNoneLoanProfit.divide(
					bisLoanBal.add(bisFactAmtIncrease), 4,
					BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)); // D=
																				// A
																				// /
																				// (B
																				// +
																				// C)
		}
		l120s25a.setBisEstimatedReturn(bisEstimatedReturn);

		BigDecimal bisNoneLoanProfit_1 = (l120s25a.getBisNoneLoanProfit_1() == null ? BigDecimal.ZERO
				: l120s25a.getBisNoneLoanProfit_1()); // A.近一年非授信貢獻度
		BigDecimal bisLoanBal_1 = (l120s25a.getBisLoanBal_1() == null ? BigDecimal.ZERO
				: l120s25a.getBisLoanBal_1()); // B.近一年授信餘額
		BigDecimal bisFactAmtIncrease_1 = (l120s25a.getBisFactAmtIncrease_1() == null ? BigDecimal.ZERO
				: l120s25a.getBisFactAmtIncrease_1()); // C.新作增額額度
		BigDecimal bisEstimatedReturn_1 = BigDecimal.ZERO; // D.預估非授信收益率 %
		if ((bisLoanBal_1.add(bisFactAmtIncrease_1)).compareTo(BigDecimal.ZERO) > 0) {
			bisEstimatedReturn_1 = bisNoneLoanProfit_1.divide(
					bisLoanBal_1.add(bisFactAmtIncrease_1), 4,
					BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
		}
		l120s25a.setBisEstimatedReturn_1(bisEstimatedReturn_1);
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult calcAllL120s25a(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));

		String errMsg = "";

		List<L120S25A> l120s25as = lms1201Service.findL120s25aByMainId(mainId);

		if (l120s25as == null || l120s25as.isEmpty()) {
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0036"), getClass());
		}

		// 一次性共用資料引進*************************************************

		// 營運成本
		String baseRate2 = "";
		List<LNF07A> list2 = lnLnf07aService
				.sel_by_key1_orderBykey3Desc("BANK WORK COST");
		if (list2.size() > 0) {
			LNF07A lnf07a2 = list2.get(0);
			// 9(2)V9(5) 整數兩位 小數5位
			// 007600 => 0.76
			baseRate2 = Util.trim(lnf07a2.getLnf07a_key_4());
			StringBuffer sb2 = new StringBuffer();
			sb2.append(NumberUtils.toInt(baseRate2.substring(0, 2)))
					.append(".").append(baseRate2.substring(2, 7));
			baseRate2 = sb2.toString();
		}

		if (Util.equals(baseRate2, "")) {
			throw new CapMessageException("無法引進「營運成本」", getClass());
		}

		// 預期損失率
		String baseRate3 = "";
		List<LNF07A> list3 = lnLnf07aService
				.sel_by_key1_orderBykey3Desc("LOST RATE");
		if (list3.size() > 0) {
			LNF07A lnf07a3 = list3.get(0);
			// 9(2)V9(5) 整數兩位 小數5位
			// 007600 => 0.76
			baseRate3 = Util.trim(lnf07a3.getLnf07a_key_4());
			StringBuffer sb3 = new StringBuffer();
			sb3.append(NumberUtils.toInt(baseRate3.substring(0, 2)))
					.append(".").append(baseRate3.substring(2, 7));
			baseRate3 = sb3.toString();
		}

		if (Util.equals(baseRate3, "")) {
			throw new CapMessageException("無法引進「預期損失率」", getClass());
		}

		List<L120S25C> l120s25cs = null;

		// 標準法資本要求 單位%，BIS評估表標準法資本要求
		BigDecimal bisCapitalReq = null;
		String bisCapitalReqYear = "";
		// 先抓指定日期
		l120s25cs = lms1201Service.findL120s25bByParamTypeDate(
				"LMS_BIS_CAPITAL_REQ", CapDate.getCurrentDate("yyyy")
						+ "-01-01");
		if (l120s25cs != null && !l120s25cs.isEmpty()) {
			String capitalReq = l120s25cs.get(0).getParamVal();
			bisCapitalReqYear = CapDate.getCurrentDate("yyyy");
			if (Util.isEmpty(capitalReq)) {
				bisCapitalReq = new BigDecimal(0);
			} else {
				bisCapitalReq = Util.parseBigDecimal(capitalReq);
			}
		} else {
			// 沒資料時改抓最新一筆
			l120s25cs = lms1201Service.findL120s25bByParamTypeDate(
					"LMS_BIS_CAPITAL_REQ", null);
			if (l120s25cs != null && !l120s25cs.isEmpty()) {
				String capitalReq = l120s25cs.get(0).getParamVal();
				bisCapitalReqYear = Util.getLeftStr(l120s25cs.get(0)
						.getParamDate(), 4);
				if (Util.isEmpty(capitalReq)) {
					bisCapitalReq = new BigDecimal(0);
				} else {
					bisCapitalReq = Util.parseBigDecimal(capitalReq);
				}
			}
		}

		if (bisCapitalReq == null) {
			throw new CapMessageException("無法引進「標準法資本要求」", getClass());
		}

		// 自有資本 單位元，會計處每月15日自結報表自有資本(E46)
		BigDecimal bisSelfCapital = null;

		// 先抓指定日期
		l120s25cs = lms1201Service.findL120s25bByParamTypeDate(
				"LMS_BIS_SELF_CAPITAL", CapDate.getCurrentDate("yyyy-MM")
						+ "-01");
		if (l120s25cs != null && !l120s25cs.isEmpty()) {
			String selfCapital = l120s25cs.get(0).getParamVal();
			if (Util.isEmpty(selfCapital)) {
				bisSelfCapital = new BigDecimal(0);
			} else {
				bisSelfCapital = Util.parseBigDecimal(selfCapital);
			}
		} else {
			// 沒資料時改抓最新一筆
			l120s25cs = lms1201Service.findL120s25bByParamTypeDate(
					"LMS_BIS_SELF_CAPITAL", null);
			if (l120s25cs != null && !l120s25cs.isEmpty()) {
				String selfCapital = l120s25cs.get(0).getParamVal();
				if (Util.isEmpty(selfCapital)) {
					bisSelfCapital = new BigDecimal(0);
				} else {
					bisSelfCapital = Util.parseBigDecimal(selfCapital);
				}
			}
		}

		if (bisSelfCapital == null) {
			throw new CapMessageException("無法引進「自有資本」", getClass());
		}

		// 全行風險性資產 單位元，會計處每月15日自結報表風險性資產(E61)
		BigDecimal bisMegaRwa = null;

		l120s25cs = lms1201Service.findL120s25bByParamTypeDate(
				"LMS_BIS_MEGARWA", CapDate.getCurrentDate("yyyy-MM") + "-01");
		if (l120s25cs != null && !l120s25cs.isEmpty()) {
			String megaRwa = l120s25cs.get(0).getParamVal();
			if (Util.isEmpty(megaRwa)) {
				bisMegaRwa = new BigDecimal(0);
			} else {
				bisMegaRwa = Util.parseBigDecimal(megaRwa);
			}
		} else {
			// 沒資料時改抓最新一筆
			l120s25cs = lms1201Service.findL120s25bByParamTypeDate(
					"LMS_BIS_MEGARWA", null);
			if (l120s25cs != null && !l120s25cs.isEmpty()) {
				String megaRwa = l120s25cs.get(0).getParamVal();
				if (Util.isEmpty(megaRwa)) {
					bisMegaRwa = new BigDecimal(0);
				} else {
					bisMegaRwa = Util.parseBigDecimal(megaRwa);
				}
			}
		}

		if (bisMegaRwa == null) {
			throw new CapMessageException("無法引進「全行風險性資產」", getClass());
		}

		// 先掃一遍欄位檢核******************************************************************
		StringBuffer chkMsg = new StringBuffer("");
		for (L120S25A l120s25a : l120s25as) {
			String tMsg = this.chkColumn(l120s25a);
			if (Util.notEquals(tMsg, "")) {
				chkMsg.append(
						(Util.notEquals(chkMsg.toString(), "") ? "<BR>" : ""))
						.append(tMsg);
			}
		}

		if (Util.notEquals(chkMsg.toString(), "")) {
			throw new CapMessageException(chkMsg.toString(), getClass());
		}

		// 先引思恩L120S24A
		// RW資料****************************************************************
		StringBuffer importMsg = new StringBuffer("");
		for (L120S25A l120s25a : l120s25as) {
			// 清除計算欄位
			this.initCalcColumn(l120s25a);

			// 引進風險權數相關資料L120S24A--思恩
			String tErrMsg = this.importL120s24aData(l120s25a);

			if (Util.notEquals(tErrMsg, "")) {
				// L120S25A.errMsg01=額度序號『{0}』請先完成風險權數計算。
				importMsg
						.append((Util.notEquals(importMsg.toString(), "") ? "<BR>"
								: "")).append(tErrMsg);
			}

			// BGN 共用一次性資料塞直***********************************
			// 營運成本
			l120s25a.setBisBankWorkCost(Util.isEmpty(baseRate2) ? null
					: new BigDecimal(baseRate2));
			// 預期損失率
			l120s25a.setBisExptLoss(Util.isEmpty(baseRate3) ? null
					: new BigDecimal(baseRate3));

			// 標準法資本要求年度
			l120s25a.setBisCapitalReqYear(bisCapitalReqYear);

			// 標準法資本要求
			l120s25a.setBisCapitalReq(Util.parseBigDecimal(bisCapitalReq));

			// 自有資本
			l120s25a.setBisSelfCapital(Util.parseBigDecimal(bisSelfCapital));

			// 全行風險性資產
			l120s25a.setBisMegaRwa(bisMegaRwa);

			// END 共用一次性資料塞直***********************************

			lms1201Service.save(l120s25a);
		}

		if (Util.notEquals(importMsg.toString(), "")) {
			throw new CapMessageException(importMsg.toString(), getClass());
		}

		// 計算明細與合計****************************************************************

		for (L120S25A l120s25a : l120s25as) {

			// 計算相關欄位
			String tCalcErrMsg = this.calcColumn(l120s25a);
			if (Util.notEquals(tCalcErrMsg, "")) {
				throw new CapMessageException(tCalcErrMsg, getClass());
			}

			lms1201Service.save(l120s25a);

		}

		// 計算加總欄位
		this.calcTotColumn(mainId);

		if (Util.isNotEmpty(errMsg)) {
			result.set("msg", errMsg);
		}

		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));

		return result;
	}

	// J-111-0443_05097_B1001 Web e-Loan企金授信開發授信BIS評估表
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveBisParamToL120s25c(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String bisParamForm = Util
				.nullToSpace(params.getString("bisParamForm"));

		JSONObject jsonbisParamForm = JSONObject.fromObject(bisParamForm);

		String lmsBisSelfCapitalDate = jsonbisParamForm
				.optString("lmsBisSelfCapitalDate");
		String lmsBisSelfCapital = jsonbisParamForm
				.optString("lmsBisSelfCapital");
		String lmsBisMegaRwa = jsonbisParamForm.optString("lmsBisMegaRwa");

		if (Util.isEmpty(lmsBisSelfCapitalDate)
				|| Util.isEmpty(lmsBisSelfCapital)
				|| Util.isEmpty(lmsBisMegaRwa)) {
			throw new CapMessageException("欄位不得空白", getClass());
		}

		// 單位元，會計處每月15日自結報表自有資本(E46)
		List<L120S25C> l120s25cs1 = lmsBisService
				.findL120s25cByParamTypeKeyDate("LMS_BIS_SELF_CAPITAL", null,
						lmsBisSelfCapitalDate);
		if (l120s25cs1 != null && !l120s25cs1.isEmpty()) {
			lmsBisService.deleteListL120s25c(l120s25cs1);
		}

		// 單位元，會計處每月15日自結報表風險性資產(E61)
		List<L120S25C> l120s25cs2 = lmsBisService
				.findL120s25cByParamTypeKeyDate("LMS_BIS_MEGARWA", null,
						lmsBisSelfCapitalDate);
		if (l120s25cs2 != null && !l120s25cs2.isEmpty()) {
			lmsBisService.deleteListL120s25c(l120s25cs2);
		}

		L120S25C l120s25c = null;
		// 單位元，會計處每月15日自結報表自有資本(E46)
		// INSERT INTO
		// lms.l120s25c(OID,PARAMTYPE,PARAMDATE,PARAMKEY,PARAMVAL,PARAMDSCR)
		// VALUES
		// (GET_OID(),'LMS_BIS_SELF_CAPITAL','2022-08-01','','309822545000','單位元，會計處每月15日自結報表自有資本(E46)');
		l120s25c = new L120S25C();
		l120s25c.setParamDate(lmsBisSelfCapitalDate);
		l120s25c.setParamDscr("單位元，會計處每月15日自結報表自有資本(E46)");
		l120s25c.setParamKey("");
		l120s25c.setParamType("LMS_BIS_SELF_CAPITAL");
		l120s25c.setParamVal((Util.parseBigDecimal(lmsBisSelfCapital)
				.multiply(new BigDecimal(1000))).toPlainString());
		l120s25c.setCreator(user.getUserId());
		l120s25c.setCreateTime(CapDate.getCurrentTimestamp());
		lmsBisService.save(l120s25c);

		// 單位元，會計處每月15日自結報表風險性資產(E61)
		// INSERT INTO
		// lms.l120s25c(OID,PARAMTYPE,PARAMDATE,PARAMKEY,PARAMVAL,PARAMDSCR)
		// VALUES
		// (GET_OID(),'LMS_BIS_MEGARWA','2022-08-01','','2316709025000','單位元，會計處每月15日自結報表風險性資產(E61)');
		l120s25c = new L120S25C();
		l120s25c.setParamDate(lmsBisSelfCapitalDate);
		l120s25c.setParamDscr("單位元，會計處每月15日自結報表風險性資產(E61)");
		l120s25c.setParamKey("");
		l120s25c.setParamType("LMS_BIS_MEGARWA");
		l120s25c.setParamVal((Util.parseBigDecimal(lmsBisMegaRwa)
				.multiply(new BigDecimal(1000))).toPlainString());
		l120s25c.setCreator(user.getUserId());
		l120s25c.setCreateTime(CapDate.getCurrentTimestamp());
		lmsBisService.save(l120s25c);

		// 單位元，會計處每月15日自結報表風險性資產(E61)

		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));

		return result;
	}

	/**
	 * 實績貢獻 J-110-0155 修改e-loan授信管理系統簽報書之「利率定價合理性分析表」為「新臺幣、美元利率定價合理性及收益率分析表」
	 */
	public IResult impBisEstimatedReturn(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1401S03Panel.class);

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String qOid = Util.trim(params.getString("qOid"));

		// J-111-0443_05097_B1006 Web e-Loan企金授信開發授信BIS評估表
		// 資料基準年月
		String queryDateE0 = params.getString("queryDateE0");
		String queryDateE1 = params.getString("queryDateE1");

		if (Util.isEmpty(queryDateE0) || Util.isEmpty(queryDateE1)) {
			// L120S08A.error10=資料查詢期間不得為空白！
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0015", prop.getProperty("L120S08A.error10")),
					getClass());
		}

		L120S25A l120s25a = lms1201Service.findL120s25aByOid(qOid);
		if (l120s25a == null) {
			l120s25a = new L120S25A();
		}

		// 1:借款人
		// 2:關係企業
		// 3.關係企業+借款人
		String kind = Util.trim(params.getString("kind"));
		String qCustId = Util.trim(l120s25a.getBisCustId_s25a());
		String qDupNo = Util.trim(l120s25a.getBisDupNo_s25a());

		L120M01A meta = lms1201Service.findL120m01aByMainId(mainId);

		BranchRate branchRate = lmsService.getBranchRate(meta.getCaseBrId());
		JSONObject jsonData = new JSONObject();

		StringBuilder strbE = new StringBuilder();
		// 轉換日期成YYYY-MM-01格式

		strbE.append(checkLength(queryDateE0)).append("-")
				.append(checkLength(queryDateE1)).append("-").append("01");
		String queryDateE = strbE.toString();

		if (Util.isEmpty(queryDateE)) {
			// L120S08A.error10=資料查詢期間不得為空白！
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0015", prop.getProperty("L120S08A.error10")),
					getClass());
		}

		// 檢查查詢起迄************************************
		JSONObject formJson = new JSONObject();
		StringBuilder sbMsg = new StringBuilder();
		String popMsg1 = UtilConstants.Mark.SPACE;
		String popMsg2 = UtilConstants.Mark.SPACE;
		String popMsg3 = "L1205S07.error7";
		StringBuilder sbDate = new StringBuilder();
		StringBuilder sbDbDate = new StringBuilder();

		// 查詢起日-往前推半年
		Date dQueryDate6S = CapDate.addMonth(Util.parseDate(queryDateE), -5);
		// 查詢起日-往前推一年
		Date dQueryDate12S = CapDate.addMonth(Util.parseDate(queryDateE), -11);
		// 查詢迄日
		Date dQueryDateE = Util.parseDate(queryDateE);

		String queryDateS0 = CapDate.formatDate(dQueryDate12S, "yyyy");
		String queryDateS1 = CapDate.formatDate(dQueryDate12S, "MM");

		int getKind = 0;
		getKind = lms1201Service.checkDate(
				CapDate.formatDate(dQueryDate12S, "yyyy-MM-dd"),
				CapDate.formatDate(dQueryDateE, "yyyy-MM-dd"), formJson);

		if (getKind == 0) {
			if (Util.equals(kind, "A")) {
				// A.個體+集團　　2023/01/18 風控處改個體(借款人)；集團(借款人+集團企業)　kind固定為A
				lmsBisService
						.impBisEstimatedReturn("1", mainId, queryDateE,
								jsonData, branchRate, qCustId, qDupNo, true);
				lmsBisService
						.impBisEstimatedReturn("3", mainId, queryDateE,
								jsonData, branchRate, qCustId, qDupNo, true);
			} else {
				lmsBisService.impBisEstimatedReturn(kind, mainId,
						queryDateE, jsonData, branchRate, qCustId, qDupNo,
						false);
			}
		} else if (getKind == 1) {
			sbMsg.setLength(0);
			sbDate.setLength(0);
			sbDbDate.setLength(0);
			sbDate.append(checkLength(queryDateS0)).append("/")
					.append(checkLength(queryDateS1));
			sbDbDate.append(
					getYMDofDate(Util.nullToSpace(formJson.get("MIN_CYC_MN")),
							"Y"))
					.append("/")
					.append(getYMDofDate(
							Util.nullToSpace(formJson.get("MIN_CYC_MN")), "M"));
			popMsg1 = "L1205S07.error1";
			popMsg2 = "L1205S07.error8";
			showError(sbMsg, popMsg1, popMsg2, popMsg3, sbDate, sbDbDate);
		} else if (getKind == 2) {
			sbMsg.setLength(0);
			sbDate.setLength(0);
			sbDbDate.setLength(0);
			sbDate.append(checkLength(queryDateS0)).append("/")
					.append(checkLength(queryDateS1));
			sbDbDate.append(
					getYMDofDate(Util.nullToSpace(formJson.get("MIN_CYC_MN")),
							"Y"))
					.append("/")
					.append(getYMDofDate(
							Util.nullToSpace(formJson.get("MIN_CYC_MN")), "M"));
			popMsg1 = "L1205S07.error1";
			popMsg2 = "L1205S07.error4";
			showError(sbMsg, popMsg1, popMsg2, popMsg3, sbDate, sbDbDate);
		} else if (getKind == 3) {
			sbMsg.setLength(0);
			sbDate.setLength(0);
			sbDbDate.setLength(0);
			sbDate.append(checkLength(queryDateS0)).append("/")
					.append(checkLength(queryDateS1));
			popMsg1 = "L1205S07.error1";
			popMsg2 = "L1205S07.error3";
			showError(sbMsg, popMsg1, popMsg2, popMsg3, sbDate, sbDbDate);
		} else if (getKind == 4) {
			sbMsg.setLength(0);
			sbDate.setLength(0);
			sbDbDate.setLength(0);
			sbDate.append(checkLength(queryDateE0)).append("/")
					.append(checkLength(queryDateE1));
			sbDbDate.append(
					getYMDofDate(Util.nullToSpace(formJson.get("MAX_CYC_MN")),
							"Y"))
					.append("/")
					.append(getYMDofDate(
							Util.nullToSpace(formJson.get("MAX_CYC_MN")), "M"));
			popMsg1 = "L1205S07.error2";
			popMsg2 = "L1205S07.error6";
			showError(sbMsg, popMsg1, popMsg2, popMsg3, sbDate, sbDbDate);
		} else if (getKind == 5) {
			sbMsg.setLength(0);
			sbDate.setLength(0);
			sbDbDate.setLength(0);
			sbDate.append(checkLength(queryDateE0)).append("/")
					.append(checkLength(queryDateE1));
			sbDbDate.append(
					getYMDofDate(Util.nullToSpace(formJson.get("MAX_CYC_MN")),
							"Y"))
					.append("/")
					.append(getYMDofDate(
							Util.nullToSpace(formJson.get("MAX_CYC_MN")), "M"));
			popMsg1 = "L1205S07.error2";
			popMsg2 = "L1205S07.error10";
			showError(sbMsg, popMsg1, popMsg2, popMsg3, sbDate, sbDbDate);
		} else {
			sbMsg.setLength(0);
			sbDate.setLength(0);
			sbDbDate.setLength(0);
			sbDate.append(checkLength(queryDateE0)).append("/")
					.append(checkLength(queryDateE1));
			popMsg1 = "L1205S07.error2";
			popMsg2 = "L1205S07.error5";
			showError(sbMsg, popMsg1, popMsg2, popMsg3, sbDate, sbDbDate);
		}
		// **************************************************

		result.set("bisNoneLoanProfit",
				jsonData.optString("bisNoneLoanProfit", "0"));
		result.set("bisLoanBal", jsonData.optString("bisLoanBal", "0"));
		result.set("bisFactAmtIncrease",
				jsonData.optString("bisFactAmtIncrease", "0"));

		BigDecimal bisNoneLoanProfit = Util.parseBigDecimal(jsonData.optString(
				"bisNoneLoanProfit", "0"));
		BigDecimal bisLoanBal = Util.parseBigDecimal(jsonData.optString(
				"bisLoanBal", "0"));
		BigDecimal bisFactAmtIncrease = Util.parseBigDecimal(jsonData
				.optString("bisFactAmtIncrease", "0"));

		BigDecimal bisEstimatedReturn = BigDecimal.ZERO;
		if ((bisLoanBal.add(bisFactAmtIncrease)).compareTo(BigDecimal.ZERO) > 0) {
			bisEstimatedReturn = bisNoneLoanProfit
					.divide(bisLoanBal.add(bisFactAmtIncrease), 6,
							BigDecimal.ROUND_HALF_UP)
					.multiply(new BigDecimal(100))
					.setScale(2, BigDecimal.ROUND_HALF_UP);
		}

		result.set("bisEstimatedReturn", bisEstimatedReturn.toPlainString());

		if (Util.equals(kind, "A")) {
			result.set("bisNoneLoanProfit_1",
					jsonData.optString("bisNoneLoanProfit_1", "0"));
			result.set("bisLoanBal_1", jsonData.optString("bisLoanBal_1", "0"));
			result.set("bisFactAmtIncrease_1",
					jsonData.optString("bisFactAmtIncrease_1", "0"));

			BigDecimal bisNoneLoanProfit_1 = Util.parseBigDecimal(jsonData
					.optString("bisNoneLoanProfit_1", "0"));
			BigDecimal bisLoanBal_1 = Util.parseBigDecimal(jsonData.optString(
					"bisLoanBal_1", "0"));
			BigDecimal bisFactAmtIncrease_1 = Util.parseBigDecimal(jsonData
					.optString("bisFactAmtIncrease_1", "0"));

			BigDecimal bisEstimatedReturn_1 = BigDecimal.ZERO;
			if ((bisLoanBal_1.add(bisFactAmtIncrease_1))
					.compareTo(BigDecimal.ZERO) > 0) {
				bisEstimatedReturn_1 = bisNoneLoanProfit_1
						.divide(bisLoanBal_1.add(bisFactAmtIncrease_1), 6,
								BigDecimal.ROUND_HALF_UP)
						.multiply(new BigDecimal(100))
						.setScale(2, BigDecimal.ROUND_HALF_UP);
			}

			result.set("bisEstimatedReturn_1",
					bisEstimatedReturn_1.toPlainString());
		} else {
			result.set("bisNoneLoanProfit_1", "0");
			result.set("bisLoanBal_1", "0");
			result.set("bisFactAmtIncrease_1", "0");
			result.set("bisEstimatedReturn_1", "0");
		}

		// 印出執行成功訊息!
		// J-112-0281_05097_B1001 Web
		// e-Loan簽報書BIS評估表調整海外分行存款貢獻度之年化處理及非授信貢獻度為負時應顯示之警語
		String negativeAttrbute = jsonData.optString("negativeAttrbute", "");
		if (Util.notEquals(negativeAttrbute, "")) {
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
					"檢核下列統編之非授信貢獻度為負，請確認是否已於AS400 SCR：2DR3進行Earning Rate之維護交易；如已執行，請確認交易是否正確："
							+ "<br>" + negativeAttrbute);
		} else {
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
					.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		}

		return result;
	}

	/**
	 * 實績貢獻 J-110-0155 修改e-loan授信管理系統簽報書之「利率定價合理性分析表」為「新臺幣、美元利率定價合理性及收益率分析表」
	 */
	public IResult calcBisEstimatedReturn(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1401S03Panel.class);

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String oid = Util.trim(params.getString("oid"));

		L120S25A l120s25a = lms1201Service.findL120s25aByOid(oid);
		if (l120s25a == null) {
			l120s25a = new L120S25A();
		}

		BigDecimal bisNoneLoanProfit = Util.parseBigDecimal(NumConverter
				.delCommaString(params.getString("bisNoneLoanProfit")));
		l120s25a.setBisNoneLoanProfit(bisNoneLoanProfit);

		BigDecimal bisLoanBal = Util.parseBigDecimal(NumConverter
				.delCommaString(params.getString("bisLoanBal")));
		l120s25a.setBisLoanBal(bisLoanBal);

		BigDecimal bisFactAmtIncrease = Util.parseBigDecimal(NumConverter
				.delCommaString(params.getString("bisFactAmtIncrease")));
		l120s25a.setBisFactAmtIncrease(bisFactAmtIncrease);

		BigDecimal bisEstimatedReturn = BigDecimal.ZERO;
		if ((bisLoanBal.add(bisFactAmtIncrease)).compareTo(BigDecimal.ZERO) > 0) {
			bisEstimatedReturn = bisNoneLoanProfit
					.divide(bisLoanBal.add(bisFactAmtIncrease), 6,
							BigDecimal.ROUND_HALF_UP)
					.multiply(new BigDecimal(100))
					.setScale(2, BigDecimal.ROUND_HALF_UP);
		}

		result.set("bisEstimatedReturn", bisEstimatedReturn.toPlainString());

		BigDecimal bisNoneLoanProfit_1 = Util.parseBigDecimal(NumConverter
				.delCommaString(params.getString("bisNoneLoanProfit_1")));
		l120s25a.setBisNoneLoanProfit_1(bisNoneLoanProfit_1);

		BigDecimal bisLoanBal_1 = Util.parseBigDecimal(NumConverter
				.delCommaString(params.getString("bisLoanBal_1")));
		l120s25a.setBisLoanBal_1(bisLoanBal_1);

		BigDecimal bisFactAmtIncrease_1 = Util.parseBigDecimal(NumConverter
				.delCommaString(params.getString("bisFactAmtIncrease_1")));
		l120s25a.setBisFactAmtIncrease_1(bisFactAmtIncrease_1);

		BigDecimal bisEstimatedReturn_1 = BigDecimal.ZERO;
		if ((bisLoanBal_1.add(bisFactAmtIncrease_1)).compareTo(BigDecimal.ZERO) > 0) {
			bisEstimatedReturn_1 = bisNoneLoanProfit_1
					.divide(bisLoanBal_1.add(bisFactAmtIncrease_1), 6,
							BigDecimal.ROUND_HALF_UP)
					.multiply(new BigDecimal(100))
					.setScale(2, BigDecimal.ROUND_HALF_UP);
		}

		result.set("bisEstimatedReturn_1", bisEstimatedReturn_1.toPlainString());

		// 印出執行成功訊息!
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));

		return result;
	}

	/**
	 * <pre>
	 * 處理月份長度為1碼時自動在前面加上0	Ex:1->01,3->03...etc
	 * @param str String
	 * @return String
	 * </pre>
	 */
	public String checkLength(String str) {
		if (Util.trim(str).length() == 1) {
			str = "0" + str;
		}
		return Util.trim(str);
	}

	/**
	 * <pre>
	 * 判斷是否輸入為空
	 * @param yyyy String
	 * @param mm String
	 * @return Date
	 * </pre>
	 */
	public Date checkSpace(String yyyy, String mm) {
		mm = (Util.isEmpty(mm)) ? UtilConstants.Mark.SPACE : Util
				.addZeroWithValue(mm, 2);
		if (UtilConstants.Mark.SPACE.equals(yyyy)
				|| UtilConstants.Mark.SPACE.equals(mm)) {
			return null;
		} else {
			StringBuilder strBuffer = new StringBuilder();
			strBuffer.append(yyyy).append("-").append(mm).append("-")
					.append("01");
			return Util.parseDate(strBuffer.toString());
		}
	}

	/**
	 * 顯示往來彙總日期範圍錯誤訊息
	 * 
	 * @param sbMsg
	 *            日期範圍錯誤訊息
	 * @param popMsg1
	 *            i18n property檔1
	 * @param popMsg2
	 *            i18n property檔2
	 * @param popMsg3
	 *            i18n property檔3
	 * @param sbDate
	 *            使用者輸入日期範圍
	 * @param sbDbDate
	 *            DB查詢日期範圍
	 * @throws CapException
	 */
	private void showError(StringBuilder sbMsg, String popMsg1, String popMsg2,
			String popMsg3, StringBuilder sbDate, StringBuilder sbDbDate) throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS07APanel.class);
		sbMsg.append(pop.getProperty(popMsg1)).append(sbDate.toString())
				.append(pop.getProperty(popMsg2)).append(sbDbDate.toString())
				.append("\t").append(pop.getProperty(popMsg3));

		String hostAddr = sysParameterService
				.getParamValue(SysParamConstants.MAIL_ADDRESS_HOST);
		boolean isTestEmail = true; // 是否為測試信件

		if (Util.equals(Util.trim(hostAddr), "")) {
			isTestEmail = "true".equals(PropUtil.getProperty("isTestEmail")) ? true
					: false; // 是否為測試信件
		} else {
			if (StringUtils.indexOf(hostAddr, "@notes.") >= 0) { // Production
				isTestEmail = false;
			} else {
				isTestEmail = true;
			}
		}

		if (isTestEmail) {
			// 測試環境不檔
			logger.error(UtilConstants.AJAX_RSP_MSG.注意, sbMsg.toString());
		} else {
			throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.注意, sbMsg.toString()),
					getClass());
		}

	}

	/**
	 * <pre>
	 * 將DB欄位分割送到相對應前端欄位2
	 * @param date String
	 * @param dateFormate String
	 * @return String
	 * </pre>
	 */
	private String getYMDofDate(String date, String dateFormate) {
		String dateArray[] = date.split("-");
		dateFormate = Util.trim(dateFormate).toUpperCase();
		if (dateArray.length >= 3) {
			if ("Y".equals(dateFormate)) {
				// Year
				return dateArray[0];
			} else if ("M".equals(dateFormate)) {
				// Month
				return dateArray[1];
			} else if ("D".equals(dateFormate)) {
				// Day
				return dateArray[2];
			}
		}
		return UtilConstants.Mark.SPACE;
	}

}
