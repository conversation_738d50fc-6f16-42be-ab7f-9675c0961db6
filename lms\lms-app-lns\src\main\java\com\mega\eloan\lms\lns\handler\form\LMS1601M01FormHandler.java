/* 
 *  LMS1601M01FormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lns.handler.form;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.TreeMap;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.DocAuthTypeEnum;
import com.mega.eloan.common.enums.TypCdEnum;
import com.mega.eloan.common.exception.GWException;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.service.UserInfoService.SignEnum;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.BranchRate;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.OverSeaUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.constants.UtilConstants.editDoc;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.panels.LMSS20APanel;
import com.mega.eloan.lms.base.service.AMLRelateService;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dw.service.DwLnquotovService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.lms.handler.form.LMS1605M01FormHandler;
import com.mega.eloan.lms.lms.pages.LMS2105M01Page;
import com.mega.eloan.lms.lms.service.LMS2105Service;
import com.mega.eloan.lms.lns.pages.LMS1401S02Page;
import com.mega.eloan.lms.lns.pages.LMS1601M01Page;
import com.mega.eloan.lms.lns.pages.LMSS07APage06;
import com.mega.eloan.lms.lns.panels.LMS1401S02Panel;
import com.mega.eloan.lms.lns.service.LMS1201Service;
import com.mega.eloan.lms.lns.service.LMS1401Service;
import com.mega.eloan.lms.lns.service.LMS1601Service;
import com.mega.eloan.lms.mfaloan.bean.ELF600;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.mfaloan.service.MisELF442Service;
import com.mega.eloan.lms.mfaloan.service.MisELF600Service;
import com.mega.eloan.lms.mfaloan.service.MisELLNGTEEService;
import com.mega.eloan.lms.mfaloan.service.MisElCUS25Service;
import com.mega.eloan.lms.mfaloan.service.MisElcrcoService;
import com.mega.eloan.lms.mfaloan.service.MisIcbcBrService;
import com.mega.eloan.lms.mfaloan.service.MisLNF022Service;
import com.mega.eloan.lms.mfaloan.service.MisMISLN20Service;
import com.mega.eloan.lms.mfaloan.service.MisQuotapprService;
import com.mega.eloan.lms.mfaloan.service.MisStoredProcService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01B;
import com.mega.eloan.lms.model.L120M01C;
import com.mega.eloan.lms.model.L120M01F;
import com.mega.eloan.lms.model.L120S01A;
import com.mega.eloan.lms.model.L120S01B;
import com.mega.eloan.lms.model.L120S01P;
import com.mega.eloan.lms.model.L120S01R;
import com.mega.eloan.lms.model.L120S09A;
import com.mega.eloan.lms.model.L120S09B;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01B;
import com.mega.eloan.lms.model.L140M01C;
import com.mega.eloan.lms.model.L140M01E;
import com.mega.eloan.lms.model.L140M01E_AF;
import com.mega.eloan.lms.model.L140M01I;
import com.mega.eloan.lms.model.L140M01J;
import com.mega.eloan.lms.model.L140M01M;
import com.mega.eloan.lms.model.L160M01A;
import com.mega.eloan.lms.model.L160M01B;
import com.mega.eloan.lms.model.L160M01C;
import com.mega.eloan.lms.model.L160M01D;
import com.mega.eloan.lms.model.L161S01A;
import com.mega.eloan.lms.model.L161S01B;
import com.mega.eloan.lms.model.L161S01C;
import com.mega.eloan.lms.model.L161S01D;
import com.mega.eloan.lms.model.L161S01E;
import com.mega.eloan.lms.model.L162S01A;
import com.mega.eloan.lms.model.L163S01A;
import com.mega.eloan.lms.model.L164S01A;
import com.mega.eloan.lms.model.L210A01A;
import com.mega.eloan.lms.model.L210M01A;
import com.mega.eloan.lms.model.L210S01A;
import com.mega.eloan.lms.model.L210S01B;
import com.mega.eloan.lms.model.L250M01A;
import com.mega.eloan.lms.model.L901M01A;
import com.mega.eloan.lms.model.VLUSEDOC01;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.iisi.cap.utils.CapWebUtil;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Arithmetic;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.core.FlowException;

/**
 * <pre>
 * 國內企金動用審核表
 * </pre>
 * 
 * @since 2012/11/20
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/11/20,REX,new
 *          <li>2013/07/03,Rex,同業聯貸的檢查條件改為UnitLoanCase
 *          才需檢查並登打，且需判斷選入的額度明細表是否有同業聯貸
 *          <li>2013/07/10,Rex,修改查詢黑名單問題
 *          </ul>
 */
@Scope("request")
@Controller("lms1601m01formhandler")
@DomainClass(L160M01A.class)
public class LMS1601M01FormHandler extends LMS1605M01FormHandler {

	@Resource
	MisStoredProcService misStoredProcService;

	@Resource
	LMS1601Service lms1601Service;
	@Resource
	LMS1401Service lms1401Service;
	@Resource
	MisCustdataService misCustdataService;

	@Resource
	LMSService lmsService;

	@Resource
	LMS1201Service lms1201Service;

	@Resource
	LMS2105Service lms2105Service;

	@Resource
	DocLogService docLogService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	BranchService branchService;

	@Resource
	MisdbBASEService misdbBASEService;
	@Resource
	EloandbBASEService eloandbBASEService;

	@Resource
	CodeTypeService codeTypeService;

	@Resource
	MisMISLN20Service misMISLN20Service;

	// J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	@Resource
	MisElCUS25Service misElcus25Service;

	@Resource
	MisElcrcoService misElcrcoService;

	@Resource
	AMLRelateService amlRelateService;

	@Resource
	ICustomerService iCustomerService;

	@Resource
	SysParameterService sysParameterService;

	@Resource
	MisELF442Service misELF442Service;
	
	@Resource
	MisELF600Service misELF600Service;

	@Resource
	MisQuotapprService misQuotapprService;

	@Resource
	MisLNF022Service misLNF022Service;

	@Resource
	MisELLNGTEEService misEllngteeService;

	@Resource
	MisIcbcBrService misIcbcBrService;

	@Resource
	CLSService clsService;
	
	@Resource
	DwLnquotovService dwLnquotovService;

	/**
	 * 新增動審表
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult newl160m01a(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		L160M01A l160m01a = new L160M01A();
		l160m01a.setDocStatus(CreditDocStatusEnum.海外_編製中.getCode());
		l160m01a.setOwnBrId(user.getUnitNo());
		l160m01a.setMainId(IDGenerator.getUUID());
		l160m01a.setApprId(user.getUserId());
		l160m01a.setTType("1");
		l160m01a.setUseSelect("1");
		String txCode = Util.trim(params
				.getString(EloanConstants.TRANSACTION_CODE));
		l160m01a.setTxCode(txCode);
		l160m01a.setDocURL(CapWebUtil.getDocUrl(LMS1601M01Page.class));
		l160m01a.setDeletedTime(CapDate.getCurrentTimestamp());
		lms1601Service.save(l160m01a);
		// 複製L901M01A審核項目
		List<L160M01C> l160m01cs = this.copyL901m01aToL160m01c(
				l160m01a.getMainId(), LocaleContextHolder.getLocale().toString());
		lms1601Service.saveL160m01cList(l160m01cs);
		CapAjaxFormResult result = new CapAjaxFormResult();
		return result.set(EloanConstants.OID, l160m01a.getOid());

	}

	/**
	 * 查詢共同行銷
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryJoinMarketing(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		L160M01A l160m01a = lms1601Service.getJoinMarketingSTR(mainId);
		result.set("joinMarketing", l160m01a.getJoinMarketing());
		result.set("joinMarketingDate", l160m01a.getJoinMarketingDate());
		return result;
	}

	/**
	 * 查詢黑名單
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryBlackInit(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		L160M01A l160m01a = lms1601Service.findL160M01AByMaindId(mainId);
		List<L160M01B> l160m01bs = (List<L160M01B>) lms1601Service
				.findListByMainId(L160M01B.class, mainId);

		ArrayList<String> mainIds = new ArrayList<String>();
		for (L160M01B l160m01b : l160m01bs) {
			mainIds.add(l160m01b.getReMainId());
		}

		List<L140M01A> l140m01as = lms1401Service
				.findL140m01aListByMainIdList(mainIds
						.toArray(new String[mainIds.size()]));
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1601M01Page.class);
		Boolean checkBlack = false;
		StringBuffer temp = new StringBuffer();
		Map<String, Object> map = null;
		HashMap<String, String> custIdMap = new HashMap<String, String>();
		String key = "";
		for (L140M01A l140m01a : l140m01as) {
			key = l140m01a.getCustId().toUpperCase()
					+ l140m01a.getDupNo().toUpperCase();
			// 判斷是否已經處理過這借款人資料
			if (custIdMap.containsKey(key)) {
				continue;
			}
			custIdMap.put(key, "");
			Map<String, Object> custData = misCustdataService
					.findAllByByCustIdAndDupNo(l140m01a.getCustId()
							.toUpperCase(), l140m01a.getDupNo().toUpperCase());
			if (custData == null || custData.isEmpty()) {
				// EFD3009=ERROR|$\{custId\}客戶中文檔0024 無此借款人資料 ！！|
				HashMap<String, String> param = new HashMap<String, String>();
				param.put("custId", l140m01a.getCustId().toUpperCase() + " "
						+ l140m01a.getDupNo().toUpperCase());
				throw new CapMessageException(RespMsgHelper.getMessage("EFD3009", param), getClass());
			}
			String eName = (String) custData.get("ENAME");
			String cName = (String) custData.get("CNAME");
			if (Util.isEmpty(Util.trim(eName))) {
				result.set("showBox", true);
				return result;
			} else {
				try {
					map = misStoredProcService.callLNSP0130(user.getUnitNo(),
							eName);

				} catch (GWException t1) {
					logger.error(t1.getMessage());
					throw t1;
				} catch (Exception e) {
					logger.error(
							"[callLNSP0130] misStoredProcService.findBlackPage EXCEPTION!!",
							e);
					HashMap<String, String> param = new HashMap<String, String>();
					param.put("dsName", "callLNSP0130");
					// EFD0010=ERROR|系統連接資料庫$\{dsName\}不成功，請洽資訊處|
					throw new CapMessageException(RespMsgHelper.getMessage("EFD0010", param), getClass());
				}
				if (map != null && !map.isEmpty()) {
					if ("YES".equals(Util.trim(map.get("SP_RETURN")))) {
						String outResult = Util.trim(map.get("SP_OUTPUT_AREA"));
						if (LMSUtil.checkSubStr(outResult, 1, 3)) {
							// 02:找到黑名單, 04:可能是黑名單
							String resultCode = outResult.substring(1, 3);
							if ("02".equals(resultCode)) {
								checkBlack = true;
								// L160M01A.message41=可能是黑名單
								String msg = pop
										.getProperty("L160M01A.message41");
								temp.append(temp.length() > 0 ? "\r" : "");
								temp.append(l140m01a.getCustId().toUpperCase())
										.append(" ");
								temp.append(eName);
								temp.append("OSAMA BIN LADEN");
								if (!Util.isEmpty(Util.trim(cName))) {
									temp.append("【").append(cName).append("】 ");
								}
								temp.append(" ").append(msg).append("。");
							} else if ("04".equals(resultCode)) {
								checkBlack = true;
								// L160M01A.message41=可能是黑名單
								String msg = pop
										.getProperty("L160M01A.message41");
								temp.append(temp.length() > 0 ? "\r" : "");
								temp.append(l140m01a.getCustId().toUpperCase())
										.append(" ");
								temp.append(eName);
								if (!Util.isEmpty(Util.trim(cName))) {
									temp.append("【").append(cName).append("】 ");
								}
								temp.append(" ").append(msg).append("。");
							} else {
								// L160M01A.message39=未列於黑名單
								String msg = pop
										.getProperty("L160M01A.message39");
								temp.append(temp.length() > 0 ? "\r" : "");
								temp.append(l140m01a.getCustId().toUpperCase());
								temp.append(" ");
								temp.append(eName);
								if (!Util.isEmpty(Util.trim(cName))) {
									temp.append("【").append(cName).append("】 ");
								}
								temp.append(" ").append(msg).append("。");
							}
						}
					} else {
						HashMap<String, String> msg = new HashMap<String, String>();
						msg.put("msg", Util.trim(map.get("SP_ERROR_MSG")));
						// 錯誤訊息
						throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, msg),
								getClass());
					}
				}
			}
		}

		if (checkBlack) {
			// L160M01A.message44=有可能是黑名單,請執行BTT-身份異常查詢及維護(0015)交易確認是否有列於黑名單中。
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
					RespMsgHelper.getMessage("EFD0015", pop.getProperty("L160M01A.message44")));
		}
		if (l160m01a != null) {
			l160m01a.setBlackListTxtOK(temp.toString());
			l160m01a.setBlackDataDate(new Date());
			lms1601Service.save(l160m01a);
		}
		result.set("blackListTxtOK", temp.toString());
		result.set("blackDataDate",
				CapDate.getCurrentDate(UtilConstants.DateFormat.YYYY_MM_DD));
		return result;

	}

	/**
	 * 查詢黑名單 手動輸入
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryBlackPage(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String name = Util.trim(params.getString("name")).toUpperCase();
		Map<String, Object> blackPage = null;
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		try {
			blackPage = misStoredProcService.callLNSP0130(user.getUnitNo(),
					name);
		} catch (GWException t1) {
			logger.error(t1.getMessage());
			throw t1;
		} catch (Exception e) {
			logger.error(
					"[queryBlackPage] lms1601Service.findBlackPage EXCEPTION!!",
					e);
			HashMap<String, String> param = new HashMap<String, String>();
			param.put("dsName", "MQ");
			// EFD0010=ERROR|系統連接資料庫$\{dsName\}不成功，請洽資訊處|
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0010", param), getClass());
		}
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1601M01Page.class);
		String msg = "";
		String mainId = params.getString(EloanConstants.MAIN_ID);
		L160M01A l160m01a = lms1601Service.findL160M01AByMaindId(mainId);
		String custName = "";
		String custId = "";
		if (l160m01a != null) {
			custId = l160m01a.getCustId() + " " + l160m01a.getDupNo();

			custName = "【" + l160m01a.getCustName() + "】";
		}
		if (blackPage != null && !blackPage.isEmpty()) {
			if ("YES".equals(Util.trim(blackPage.get("SP_RETURN")))) {
				String outResult = Util.trim(blackPage.get("SP_OUTPUT_AREA"));
				if (LMSUtil.checkSubStr(outResult, 1, 3)) {
					// 02:找到黑名單, 04:可能是黑名單
					String resultCode = outResult.substring(1, 3);
					if ("02".equals(resultCode)) {
						// L160M01A.message41=可能是黑名單
						msg = pop.getProperty("L160M01A.message41");

					} else if ("04".equals(resultCode)) {
						msg = pop.getProperty("L160M01A.message41");
					} else {
						// L160M01A.message39=未列於黑名單
						msg = pop.getProperty("L160M01A.message39");
					}
				}
			} else {
				HashMap<String, String> msg2 = new HashMap<String, String>();
				msg2.put("msg", Util.trim(blackPage.get("SP_ERROR_MSG")));
				// 錯誤訊息
				throw new CapMessageException(RespMsgHelper.getMessage(
						UtilConstants.AJAX_RSP_MSG.執行有誤, msg2), getClass());
			}
		}
		result.set("blackListTxtOK", custId + " " + name + " " + custName + " "
				+ msg);
		result.set("blackDataDate",
				CapDate.getCurrentDate(UtilConstants.DateFormat.YYYY_MM_DD));
		return result;

	}

	/**
	 * 儲存L160M01A 動用審核表主檔
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL160m01a(PageParameters params) throws CapException {
		// lmsService.uploadELLNSEEK(new L120M01A());
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", "N"));
		CapAjaxFormResult result = this.savebase(params);
		return result;
	}

	/**
	 * 基本儲存
	 * 
	 * @param params
	 *            PageParameters
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	private CapAjaxFormResult savebase(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		List<GenericBean> allBean = new ArrayList<GenericBean>();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		String oid = Util.trim(params.getString(EloanConstants.MAIN_OID));
		String formL160m01a = Util.trim(params.getString("L160M01AForm")); // 指定的form
		JSONObject jsonL160m01a = null;
		L160M01A l160m01a = null;
		Boolean showMsg = params.getAsBoolean("showMsg", false);
		if (Util.isEmpty(oid)) {

			l160m01a = new L160M01A();
			l160m01a.setDocStatus(CreditDocStatusEnum.海外_編製中.getCode());
			l160m01a.setTType("1");
			l160m01a.setOwnBrId(user.getUnitNo());
			l160m01a.setMainId(IDGenerator.getUUID());
			l160m01a.setApprId(user.getUserId());
			String txCode = Util.trim(params
					.getString(EloanConstants.TRANSACTION_CODE));
			l160m01a.setTxCode(txCode);
			l160m01a.setDocURL(CapWebUtil.getDocUrl(LMS1601M01Page.class));

			// 複製L901M01A審核項目
			List<L160M01C> l160m01cs = copyL901m01aToL160m01c(
					l160m01a.getMainId(), LocaleContextHolder.getLocale().toString());
			lms1601Service.saveL160m01cList(l160m01cs);
		} else {
			l160m01a = lms1601Service.findModelByOid(L160M01A.class, oid);
			l160m01a.setRandomCode(IDGenerator.getRandomCode());

			l160m01a.setApprId(user.getUserId());
		}
		String mainId = l160m01a.getMainId();
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1601M01Page.class);
		if (l160m01a.getDeletedTime() != null) {
			l160m01a.setDeletedTime(null);
		}
		String validate = null;
		// 組待辦事項
		StringBuilder waitingItem = new StringBuilder("");
		switch (page) {
		case 1:
			jsonL160m01a = JSONObject.fromObject(formL160m01a);
			DataParse.toBean(jsonL160m01a, l160m01a);
			validate = Util.validateColumnSize(l160m01a, pop, "L160M01A");
			if (validate != null) {
				Map<String, String> param = new HashMap<String, String>();
				param.put("colName", validate);
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
			}
			result.set("randomCode", l160m01a.getRandomCode());
			String apprId = Util.trim(l160m01a.getApprId());
			result.set("showApprId",
					apprId + " " + lmsService.getUserName(apprId));
			break;
		case 2:
			L163S01A l163s01a = lms1601Service.findL163m01aByMainId(mainId);
			List<L160M01C> l160m01cList = (List<L160M01C>) lms1601Service
					.findListByMainId(L160M01C.class, mainId);
			jsonL160m01a = JSONObject.fromObject(formL160m01a);
			DataParse.toBean(jsonL160m01a, l160m01a);
			// 全行項目
			String allResult = params.getString("allresult");
			JSONObject jsonAllresult = JSONObject.fromObject(allResult);

			// 自訂項目
			String selfresult = params.getString("selfresult");
			JSONObject jsonselfresult = JSONObject.fromObject(selfresult);
			// 先排完一般項目再排自訂項目
			int item4MaxSeq = 0;
			for (L160M01C l160m01c : l160m01cList) {
				int type = Util.parseInt(l160m01c.getItemType());
				switch (type) {
				case 4:
					Integer itemSeq = l160m01c.getItemSeq();
					item4MaxSeq = itemSeq;
					String value1 = (String) jsonAllresult.get(l160m01c
							.getOid());
					l160m01c.setItemCheck(value1);
					// 當回傳值為2 表示未收
					if (UtilConstants.Usedoc.checkItem.未收.equals(value1)) {
						waitingItem
								.append((waitingItem.length() > 0 ? "、" : ""));

						waitingItem.append(itemSeq);
						waitingItem.append(".");
						waitingItem.append(l160m01c.getItemContent());
					}
					break;
				}
			}

			for (L160M01C l160m01c : l160m01cList) {
				int type = Util.parseInt(l160m01c.getItemType());
				switch (type) {
				case 3:
					item4MaxSeq++;
					String seq = String.valueOf(l160m01c.getItemSeq());
					// 當傳回來的json裡面存在該seq的物件在執行儲存
					if (jsonselfresult.containsKey(seq)) {
						JSONObject selfObject = (JSONObject) jsonselfresult
								.get(seq);
						String selectValue = (String) selfObject.get("id");
						String drc = Util.trim(selfObject.get("drc"));
						l160m01c.setItemCheck(selectValue);
						l160m01c.setItemContent(drc);
						// 當回傳值為2 表示未收
						if (UtilConstants.Usedoc.checkItem.未收
								.equals(selectValue) && Util.isNotEmpty(drc)) {
							waitingItem.append((waitingItem.length() > 0 ? "、"
									: ""));
							waitingItem.append(item4MaxSeq);
							waitingItem.append(".");
							waitingItem.append(l160m01c.getItemContent());
						}
					}
					break;
				}
			}

			if (Util.isEmpty(l163s01a)) {
				l163s01a = new L163S01A();
				l163s01a.setMainId(mainId);
				l163s01a.setCreateTime(CapDate.getCurrentTimestamp());
				l163s01a.setCreator(user.getUserId());
			}

			// 如果有未收項目，新增第四頁籤的model ，若沒有未收清除預定補全日
			if (waitingItem.length() > 0) {
				l160m01a.setUseType("Y");

				// L160M01A.message25=未收到（未辦妥）
				String endMessg = pop.getProperty("L160M01A.message25");
				waitingItem.append(" ").append(endMessg);
				l163s01a.setWaitingItem(waitingItem.toString());
			} else {
				l163s01a.setWillFinishDate(null);
				l163s01a.setWaitingItem("");
				l160m01a.setUseType("N");
				l163s01a.setAppraiserId(null);
			}
			validate = Util.validateColumnSize(l160m01a, pop, "L160M01A");
			if (validate != null) {
				Map<String, String> param = new HashMap<String, String>();
				param.put("colName", validate);
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
			}
			validate = Util.validateColumnSize(l163s01a, pop, "L163M01A");
			if (validate != null) {
				Map<String, String> param = new HashMap<String, String>();
				param.put("colName", validate);
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
			}
			result.set("mark", waitingItem.length() > 0 ? true : false);
			allBean.add(l163s01a);
			for (L160M01C l160m01c : l160m01cList) {
				allBean.add(l160m01c);
			}

			String errorMsg = this.lms1601Service.check31ItemIsChecked(
					l160m01a.getMainId(), "4", 31);
			if (!"".equals(errorMsg)) {
				throw new CapMessageException(errorMsg, getClass());
			}

			break;
		case 3:
			/*
			 * String formL161m01a = params.getString("L161M01AForm");
			 * JSONObject jsonL161m01a = JSONObject.fromObject(formL161m01a); //
			 * 先儲存L161M01A 聯貸案參貸比率一覽表主檔 L161S01A l161m01a =
			 * lms1601Service.findL161m01aByMainId(l160m01a .getMainId());
			 * DataParse.toBean(jsonL161m01a, l161m01a); allBean.add(l161m01a);
			 * break;
			 */
			break;
		case 4:
			L163S01A l163m01aPage4 = lms1601Service
					.findL163m01aByMainId(l160m01a.getMainId());
			String formL163m01a = params.getString("L163M01AForm"); // 指定的form
			JSONObject jsonL163m01a = JSONObject.fromObject(formL163m01a);
			DataParse.toBean(jsonL163m01a, l163m01aPage4);

			// J-110-0547 為控管先行動用之授信案件，增加先行動用呈核及控制表預定補全日期之通知功能。
			// 01O編制中時才需要檢查
			if (Util.equals(l160m01a.getDocStatus(), CreditDocStatusEnum.海外_編製中)
					&& l163m01aPage4.getWillFinishDate() != null
					&& CapDate.getCurrentTimestamp().after(
							l163m01aPage4.getWillFinishDate())) {
				// L163M01A.willFinishDateError=預定補全日期需晚於今日日期
				throw new CapMessageException(
						pop.getProperty("L163M01A.willFinishDateError"),
						getClass());
			}

			result.set("appraiserId",
					lmsService.getUserName(l163m01aPage4.getAppraiserId()));

			if (Util.validateColumnSize(l163m01aPage4, pop, "L163M01A") != null) {
				Map<String, String> param = new HashMap<String, String>();
				param.put("colName",
						Util.validateColumnSize(l163m01aPage4, pop, "L163M01A"));

				throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
			}
			allBean.add(l163m01aPage4);
			break;

		}
		allBean.add(l160m01a);
		if (showMsg) {
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));
		}
		if (waitingItem.length() > 0
				&& (l160m01a.getL163S01A() == null || Util.isEmpty(l160m01a
						.getL163S01A().getWillFinishDate()))) {
			// L160M01A.message24 =
			// 尚有待辦事項，在『先行動用呈核及控制表』頁籤中之「預定補全日期」，請至『先行動用呈核及控制表』登錄
			result.set("showPage4msg", "Y");
		}
		// J-109-0150_10702_B1001 IVR頁籤由模擬動審移至動審表
		if (!"tempSave".equals(Util.trim(params.getString("formAction")))) {
			List<L160M01B> l160m01bs = (List<L160M01B>) lms1601Service
					.findListByMainId(L160M01B.class, l160m01a.getMainId());
			for (L160M01B l160m01b : l160m01bs) {
				String reMainId = l160m01b.getReMainId();
				L140M01A l140m01a = lms1601Service.findByMainId(reMainId);
				String custName = l140m01a.getCustName();
				String projClass = l140m01a.getProjClass();

				if (Util.isEmpty(l160m01b.getIVRFlag())) {
					if (Util.equals(projClass, LMSUtil.特定金錢信託受益權自行設質擔保授信)) {
						throw new CapMessageException(RespMsgHelper.getMessage("EFD0005", MessageFormat.format(
										pop.getProperty("cls3301v00.error.01"),
										new StringBuffer().append(
												l140m01a.getCustName())
												.toString(), new StringBuffer()
												.append(l140m01a.getCntrNo())
												.toString())), getClass());
					}
					// J-110-0283_10702_B1001 Web e-Loan
					// IVR動審表引入月份放寬，比對與前次額度明細表幣別不同
					// if(LMSUtil.isContainValue(Util.trim(l140m01a.getProPerty()),
					// UtilConstants.Cntrdoc.Property.變更條件)
					// ||
					// LMSUtil.isContainValue(Util.trim(l140m01a.getProPerty()),
					// UtilConstants.Cntrdoc.Property.增額)){
					// Map<String, Object> map = eloandbBASEService
					// .findLastTimeCaseByCutIdAndCntrNoOrdByUpdateTime(
					// l140m01a.getCustId(), l140m01a.getDupNo(),
					// Util.trim(l140m01a.getCntrNo()),
					// UtilConstants.Casedoc.DocType.企金,
					// Util.trim(l140m01a.getMainId()));
					// L140M01A l140m01a_old = null;
					// String oldL140M01AmainId = "";
					// if (map != null) {
					// oldL140M01AmainId = Util.trim(map.get("MAINID"));
					// l140m01a_old = lms1401Service
					// .findL140m01aByMainId(oldL140M01AmainId);
					// }
					// if(Util.isNotEmpty(l140m01a_old)){
					// if(!Util.equals(l140m01a.getCurrentApplyCurr(),l140m01a_old.getCurrentApplyCurr())){
					// throw new CapMessageException(RespMsgHelper.getMessage(
					// parent, "EFD0005",
					// MessageFormat.format(
					// pop.getProperty("cls3301v00.error.02"),
					// new
					// StringBuffer().append(l140m01a.getCustName()).toString(),
					// new
					// StringBuffer().append(l140m01a.getCntrNo()).toString())),
					// getClass());
					// }
					// }
					// }
				}
			}
		}
		result.set(EloanConstants.OID, CapString.trimNull(l160m01a.getOid()));
		result.set(EloanConstants.MAIN_OID,
				CapString.trimNull(l160m01a.getOid()));
		result.set(EloanConstants.MAIN_ID,
				CapString.trimNull(l160m01a.getMainId()));

		// for小規模 RPA查詢
		result.set("isSmallBuss", l160m01a.getIsSmallBuss());

		// J-109-0KKK_05097_B1001 簡化青年創業及啟動金貸款簽報書簽案流程
		// J-110-0CCC_05097_B1001 Web e-Loan新增國發基金協助新創事業紓困融資加碼方案微型企業簽報書格式
		result.set(
				"show_tabs_6",
				(lmsService.isRescueSmallBussOnlyCaseC(l160m01a)
						|| lmsService.isOnlyLnType61(l160m01a) || lmsService
						.isOnlyCaseType003(l160m01a)) ? "Y" : "N");

		lms1601Service.saveByGenericBeans(allBean);
		return result;
	};

	/**
	 * 當如果是新案要將L901M01A的項目寫入到L160M01C
	 * 
	 * @param mainId
	 *            動審表主檔mainId
	 * @return
	 * @throws CapException
	 */
	private List<L160M01C> copyL901m01aToL160m01c(String mainId, String local) throws CapException {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<L160M01C> l160m01cs = new ArrayList<L160M01C>();

		// 當如果是新案將全行項目跟該分行項目放入
		// 當是起新案的將目前的項目 918總行
		List<L901M01A> allItem = lms1601Service
				.findL901m01aByBranchIdAndItemType(
						UtilConstants.Maintain.L901M01ItemType.國內企金,
						UtilConstants.BankNo.授管處, local);

		for (L901M01A item : allItem) {
			L160M01C l160m01c = new L160M01C();
			l160m01c.setCreator(user.getUserId());
			l160m01c.setCreateTime(CapDate.getCurrentTimestamp());
			l160m01c.setItemSeq(item.getItemSeq());
			l160m01c.setMainId(mainId);
			l160m01c.setItemContent(item.getItemContent());
			l160m01c.setItemType(UtilConstants.Usedoc.itemType.國內企金);
			l160m01cs.add(l160m01c);
		}

		// 自訂項目
		for (int i = 1; i < 7; i++) {
			L160M01C l160m01c = new L160M01C();
			l160m01c.setCreator(user.getUserId());
			l160m01c.setCreateTime(CapDate.getCurrentTimestamp());
			l160m01c.setItemSeq(i);
			l160m01c.setMainId(mainId);
			l160m01c.setItemType(UtilConstants.Usedoc.itemType.自訂項目);
			l160m01cs.add(l160m01c);
		}

		return l160m01cs;

	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult tempSave(PageParameters params) throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", "Y"));
		CapAjaxFormResult result = this.savebase(params);
		return result;
	}

	/**
	 * 重新引進動審表稽核項目
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult reloadL160M01C(PageParameters params) throws CapException {
		String mainId = params.getString(EloanConstants.MAIN_ID);
		CapAjaxFormResult result = new CapAjaxFormResult();
		List<L160M01C> l160m01c_Olds = (List<L160M01C>) lms1601Service
				.findListByMainId(L160M01C.class, mainId);
		lms1601Service.deleteL160m01cs(l160m01c_Olds);
		List<L160M01C> l160m01cs = null;
		// 判斷純小規模
		L160M01A l160m01a = lms1601Service.findL160M01AByMaindId(mainId);
		if (l160m01a != null) {
			if (lmsService.isRescueSmallBussOnlyCaseC(l160m01a)) {
				l160m01cs = this.copyL901m01aToL160m01c_SmallBussOnlyCaseC(
						mainId, LocaleContextHolder.getLocale().toString());
				l160m01a.setIsSmallBuss("Y");
				l160m01a.setSmallBussChkListDate(Util.parseDate("2020-06-02"));
			} else if (lmsService.isRescueOnlyCaseF(l160m01a)) {
				// J-112-0148 疫後振興
				l160m01cs = this.copyL901m01aToL160m01c_OnlyCaseF(mainId,
						LocaleContextHolder.getLocale().toString());
				l160m01a.setIsSmallBuss("N");
				l160m01a.setSmallBussChkListDate(null);
			} else if (lmsService.isRescueOnlyCaseJ03HeadItem1(l160m01a)) {
				l160m01cs = this.copyL901m01aToL160m01c_OnlyCaseJ03HeadItem1(mainId,
						LocaleContextHolder.getLocale().toString());
				l160m01a.setIsSmallBuss("N");
				l160m01a.setSmallBussChkListDate(null);
			} else {
				l160m01cs = this.copyL901m01aToL160m01c(mainId, LocaleContextHolder.getLocale().toString());
				l160m01a.setIsSmallBuss("N");
				l160m01a.setSmallBussChkListDate(null);

				List<L160M01B> L160m01bs = (List<L160M01B>) this.lms1601Service
						.findListByMainId(L160M01B.class, mainId);
				this.lms1601Service.checkIsRemoveAllocateFundsCheckListItem(
						L160m01bs, l160m01cs);
			}
			if (l160m01cs != null) {
				lms1601Service.saveL160m01cList(l160m01cs);
			}

		}

		this.getL160M01C(result, l160m01cs);

		return result;
	}

	/**
	 * 是否要出現呈總行的按鈕
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryBranchData(PageParameters params) throws CapException {
		// select PARENTBRNO from COM.BELSBRN where BRNO=? with ur
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		IBranch ibranch = branchService.getBranch(user.getUnitNo());
		// 當目前登錄 分行有總行時才顯示 呈總行的按鈕
		if (Util.isEmpty(Util.trim(ibranch.getParentBrNo()))) {
			result.set("showSendBranch", false);
		} else {
			result.set("showSendBranch", true);
		}

		// 取得目前登錄分行國別
		result.set("country", ibranch.getCountryType());

		return result;
	}

	/**
	 * 查詢所選銀行底下的分行
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryBranch(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainBranch = Util.trim(params.getString("mainBranch"));
		Map<String, String> m = new TreeMap<String, String>();
		// 如果是兆豐銀行查詢的位置不一樣
		if (UtilConstants.兆豐銀行代碼.equals(mainBranch)) {
			// 抓需要的銀行代碼
			List<IBranch> bank = branchService.getAllBranch();
			for (IBranch b : bank) {
				String brName = Util.trim(b.getBrName());
				String brCode = b.getBrNo();
				m.put(brCode, brName);
			}
		} else {
			List<Map<String, Object>> rows = misdbBASEService
					.findMISSynBank(Util.trim(mainBranch));
			for (Map<String, Object> dataMap : rows) {
				String code = Util.trim(((String) dataMap.get("CODE")));
				String name = Util.trim(((String) dataMap.get("NAME")));
				m.put(code, name);
			}

		}
		result.set("brankList", new CapAjaxFormResult(m));
		return result;
	}

	/**
	 * 取得聯貸案已編碼國外銀行的清單
	 * 
	 * @param params
	 *            type 12-國外分行 99 -其他(由國外部徵信系統金融機構資料維護)
	 * 
	 * @return CapAjaxFormResult
	 * 
	 *         foreignBranch 國外銀行清單
	 * 
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryforeignBranch(PageParameters params) throws CapException {
		String type = Util.trim(params.getString("type"));
		CapAjaxFormResult result = new CapAjaxFormResult();
		Map<String, String> brank = new TreeMap<String, String>();
		if ("12".equals(type)) {
			List<Map<String, Object>> rows = this.misdbBASEService
					.findMISELFBKSNOBank();

			for (Map<String, Object> dataMap : rows) {
				String code = Util.trim(((String) dataMap.get("NUMBER")));
				String name = Util.trim(((String) dataMap.get("NAME")));
				brank.put(code, name);
			}
		} else if ("99".equals(type)) {
			List<Map<String, Object>> rows = this.misdbBASEService
					.findMISSynBankBy99();
			for (Map<String, Object> dataMap : rows) {
				String code = Util.trim(((String) dataMap.get("CODE")));
				String name = Util.trim(((String) dataMap.get("NAME")));
				brank.put(code, name);
			}
		}
		result.set("foreignBranch", new CapAjaxFormResult(brank));
		return result;
	}

	/**
	 * 查詢登錄 -先行動用呈核及控制表
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryLogeIN(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String oid = params.getString(EloanConstants.OID);
		L160M01A l160m01a = lms1601Service.findModelByOid(L160M01A.class, oid);
		CapAjaxFormResult result = DataParse.toResult(l160m01a);
		result = formatResultShow(result, l160m01a, 0);
		L163S01A l163s01a = l160m01a.getL163S01A();
		result.set("waitingItem", l163s01a.getWaitingItem());
		result.set("willFinishDate", l163s01a.getWillFinishDate());
		result.set("managerId", l163s01a.getManagerId());
		result.set("finishDate", l163s01a.getFinishDate());
		result.set("itemTrace", l163s01a.getItemTrace());
		SignEnum[] signs = { SignEnum.首長, SignEnum.單位主管, SignEnum.甲級主管 };
		Map<String, String> bossList = userInfoService.findByBrnoAndSignId(
				user.getUnitNo(), signs);
		result.set("bossList", new CapAjaxFormResult(bossList));
		return result;
	}

	/**
	 * 查詢L160M01A 動用審核表主檔
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL160m01a(PageParameters params)	throws CapException {
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);

		// J-109-0KKK_05097_B1001 簡化青年創業及啟動金貸款簽報書簽案流程
		result.set("show_tabs_6", "N");

		if (!Util.isEmpty(oid)) {
			L160M01A l160m01a = lms1601Service.findModelByOid(L160M01A.class,
					oid);
			result = formatResultShow(result, l160m01a, page);

			// J-109-0KKK_05097_B1001 簡化青年創業及啟動金貸款簽報書簽案流程
			// J-110-0CCC_05097_B1001 Web e-Loan新增國發基金協助新創事業紓困融資加碼方案微型企業簽報書格式

			// J-110-0540_05097_B1001 Web e-Loan企金授信配合調整E-loan系統動用審核表部分內容
			boolean showTabsForAll = false;
			String LMS_L160M01A_SHOW_TABS_6 = Util.trim(lmsService
					.getSysParamDataValue("LMS_L160M01A_SHOW_TABS_6"));
			if (Util.equals(LMS_L160M01A_SHOW_TABS_6, "Y")) {
				showTabsForAll = true;
			}

			result.set(
					"show_tabs_6",
					(lmsService.isRescueSmallBussOnlyCaseC(l160m01a)
							|| showTabsForAll
							|| lmsService.isOnlyLnType61(l160m01a) || lmsService
							.isOnlyCaseType003(l160m01a)) ? "Y" : "N");

		} else {

			// 開啟新案帶入起案的分行和目前文件狀態
			result.set(
					"docStatus",
					this.getMessage("docStatus."
							+ CreditDocStatusEnum.海外_編製中.getCode()));
			result.set("ownBrId", user.getUnitNo());
			result.set(
					"ownBrName",
					StrUtils.concat(" ",
							branchService.getBranchName(user.getUnitNo())));
		}

		return result;

	}

	/**
	 * 查詢L161S01B 聯貸案參貸比率一覽表明細檔
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL161s01b(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);

		L161S01B l161s01b = lms1601Service.findModelByOid(L161S01B.class, oid);
		if (!Util.isEmpty(l161s01b)) {
			result = DataParse.toResult(l161s01b);
		}
		return result;
	}

	/**
	 * 查詢L162M01A 主從債務人資料表檔
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL162m01a(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);
		L162S01A l162m01a = lms1601Service.findModelByOid(L162S01A.class, oid);
		if (!Util.isEmpty(l162m01a)) {
			result = DataParse.toResult(l162m01a);
			result.set("custIdSelect", l162m01a.getCustId().toUpperCase()
					+ l162m01a.getDupNo().toUpperCase());

			// J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
			L164S01A l164s01a = lms1601Service.findL164s01aByMainIdCustId(
					l162m01a.getMainId(), l162m01a.getRId(),
					l162m01a.getRDupNo());
			if (l164s01a != null) {
				// J-107-0070-001 Web e-Loan
				// 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
				// J-108-0039_05097_B1001 Web e-Loan
				// 國內企金授信系統簽報、動審AML頁籤將借戶之「具控制權人」納入應查詢比對黑名單之對象。
				// J-109-0209_05097_B1001 e-Loan國內企金動審表增加借戶性質註記等進扣帳對象檢核項目
				result.putAll(DataParse.toResult(l164s01a, DataParse.Need,
						new String[] { "chairmanId", "chairmanDupNo",
								"chairman", "beneficiary", "seniorMgr",
								"ctrlPeo", "isSole", "soleType", "hasRegis" }));

			}
			String banktype = "";
			//參數控制是否卡保證金額上限、當地客戶識別ID，海外分行才能填
			String controlflag = Util.trim(lmsService.getSysParamDataValue("RPS_GTE1000"));
			if(Util.equals(UtilConstants.DEFAULT.是, controlflag)){
				if(Util.isNotEmpty(l162m01a.getCntrNo())){
					if (UtilConstants.BrNoType.國外.equals(branchService.getBranch(l162m01a.getCntrNo().substring(0, 3))
							.getBrNoFlag())) {
						banktype = TypCdEnum.海外.getCode();
					}
				}
			}
			result.set("banktype", banktype);
		} else {

		}

		return result;
	}

	/**
	 * 引進額度明細表 L140M01A 動用的額度序號 並將額度序號儲存到L160M01B
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult queryL140m01a(PageParameters params)	throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String oid = params.getString(EloanConstants.OID);
		String type = params.getString("type");
		String dataSrc = params.getString("dataSrc");
		L160M01A l160m01a = null;
		if (Util.isEmpty(oid)) {
			l160m01a = new L160M01A();
			l160m01a.setDocStatus(CreditDocStatusEnum.海外_編製中.getCode());
			l160m01a.setOwnBrId(user.getUnitNo());
			l160m01a.setMainId(IDGenerator.getUUID());
			l160m01a.setApprId(user.getUserId());
			l160m01a.setTType("1");
			l160m01a.setUseSelect("1");
			String txCode = Util.trim(params
					.getString(EloanConstants.TRANSACTION_CODE));
			l160m01a.setTxCode(txCode);
			l160m01a.setDocURL(CapWebUtil.getDocUrl(LMS1601M01Page.class));

		} else {
			l160m01a = lms1601Service.findModelByOid(L160M01A.class, oid);
		}
		List<L160M01D> models = (List<L160M01D>) lms1601Service
				.findListByMainId(L160M01D.class, l160m01a.getMainId());
		if (!models.isEmpty()) {
			lms1601Service.deleteL160m01ds(models, true);
		}
		// 所選擇的案件簽報書mainId
		String caseMainId = params.getString("caseMainId");
		L120M01A l120m01a = lms1201Service.findL120m01aByMainId(caseMainId);
		if (Util.isEmpty(l120m01a)) {
			logger.debug("\n l120m01a is =====> null");
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤), getClass());
		}
		if (UtilConstants.Casedoc.DocKind.授權外.equals(l120m01a.getDocKind())) {
			List<L120M01F> l120m01fs = lms1201Service
					.findL120m01fByMainId(caseMainId);

			List<L160M01D> newL1160M01ds = new ArrayList<L160M01D>();
			for (L120M01F l120m01f : l120m01fs) {
				if (UtilConstants.BRANCHTYPE.授管處.equals(l120m01f
						.getBranchType())) {
					// 2013_04_08_更改總處覆核主管為授管處按下放行的人
					if (UtilConstants.STAFFJOB.經辦L1.equals(l120m01f
							.getStaffJob())
							|| UtilConstants.STAFFJOB.執行覆核主管L4.equals(l120m01f
									.getStaffJob())) {
						L160M01D l160m01d = new L160M01D();
						l160m01d.setMainId(l160m01a.getMainId());
						l160m01d.setCreateTime(CapDate.getCurrentTimestamp());
						l160m01d.setCreator(user.getUserId());
						if (UtilConstants.STAFFJOB.經辦L1.equals(l120m01f
								.getStaffJob())) {
							l160m01d.setStaffJob("L6");
						} else {
							l160m01d.setStaffJob("L7");
						}

						l160m01d.setStaffNo(l120m01f.getStaffNo());
						l160m01d.setUpdater(user.getUserId());
						l160m01d.setUpdateTime(CapDate.getCurrentTimestamp());
						newL1160M01ds.add(l160m01d);
					}

				}
			}
			if (!newL1160M01ds.isEmpty()) {
				lms1601Service.saveL160m01dList(newL1160M01ds);
			}

		}
		HashMap<String, String> custContry = lms1601Service
				.getCustCounty(l120m01a);

		l160m01a.setRandomCode(IDGenerator.getRandomCode());

		// 再重新引進時 之前引進的資料
		// J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制
		lms1601Service.deleteListReInclude(l160m01a.getMainId());

		// String useBrId = user.getUnitNo();
		String itemType = lmsService.checkL140M01AItemType(l120m01a);
		List<VLUSEDOC01> vusedoc01s = null;
		// List<L140M01A> l140m01as = null;
		// 如果是全部動用將該簽報書的所有額度序號放進L160M01B

		// I-110-0028_05097_B1002 Web e-Loan企金授信額度明細表配合進出口業務集中化修改小行可以敘作大行動審表
		List<String> brnoList = new ArrayList<String>();
		brnoList.add(user.getUnitNo());

		Map<String, Object> bankInfo = misIcbcBrService.getBankInfo(user
				.getUnitNo());
		if (bankInfo != null && !bankInfo.isEmpty()) {
			String IEX_BRM = Util.trim(MapUtils.getString(bankInfo, "IEX_BRM"));
			if (Util.notEquals(IEX_BRM, "")) {
				brnoList.add(IEX_BRM);
			}
		}

		String[] useBrId = brnoList.toArray(new String[brnoList.size()]);

		if ("all".equals(type)) {

			vusedoc01s = lms1601Service.getDoCntrNo(caseMainId, useBrId,
					itemType);

			if (vusedoc01s.isEmpty()) {
				logger.info("not find l140m01as");
				Properties pop = MessageBundleScriptCreator
						.getComponentResource(LMS1601M01Page.class);
				HashMap<String, String> param = new HashMap<String, String>();
				// L160M01A.message49=查無額度明細表
				param.put("msg", pop.getProperty("L160M01A.message49"));
				throw new CapMessageException(RespMsgHelper.getMessage(
						UtilConstants.AJAX_RSP_MSG.執行有誤, param), getClass());
			}
			l160m01a.setAllCanPay(UtilConstants.DEFAULT.是);

		} else {
			String[] selectCntrNo = params.getStringArray("selectCntrNo");
			vusedoc01s = lms1601Service.getDoCntrNo(caseMainId, selectCntrNo,
					useBrId, itemType);
			l160m01a.setAllCanPay(UtilConstants.DEFAULT.否);
		}
		// 檢查要動用的額度明細表是否再簽約未動用被註記為取消
		for (VLUSEDOC01 vusedoc01 : vusedoc01s) {
			String mainId = vusedoc01.getMainId();
			Map<String, Object> rowData = eloandbBASEService
					.findL230S01AMaxDATADATE(mainId,
							CreditDocStatusEnum.海外_已核准.getCode());

			if (rowData != null
					&& UtilConstants.NoUseCase.NuseMemo.不簽約註銷額度.equals(rowData
							.get("NUSEMEMO"))) {
				Date dataDate = (Date) rowData.get("DATADATE");
				String cntrNo = (String) rowData.get("CNTRNO");
				Properties pop = MessageBundleScriptCreator
						.getComponentResource(LMS1601M01Page.class);
				// L160M01A.message54=額度序號 {0} 已由未簽約/動用報送作業於
				// {1}註記為【不簽約-註銷額度】不得動用，請重新選擇額度明細表後再執行動用作業!!
				String errorMsg = MessageFormat.format(
						pop.getProperty("L160M01A.message54"), cntrNo,
						Util.trim(dataDate));
				throw new CapMessageException(RespMsgHelper.getMessage(
						UtilConstants.AJAX_RSP_MSG.執行有誤, errorMsg), getClass());
			}
		}

		// 將引進的案件簽報書資料複製到動審表
		l160m01a = copyL120m01aToL160m01a(l160m01a, l120m01a);
		String mainMainId = l160m01a.getMainId();
		// 存放動用額度序號
		List<L160M01B> newL160m01bs = new ArrayList<L160M01B>();

		// 避免重複的額度序號
		HashMap<String, String> cntrNoMap = new HashMap<String, String>();
		ArrayList<String> selectMainId = new ArrayList<String>();
		// 取得該額度明細表 對應 額度序號
		HashMap<String, String> mainIdmapingCntrno = new HashMap<String, String>();
		for (VLUSEDOC01 vusedoc01 : vusedoc01s) {
			String cntrNo = Util.trim(vusedoc01.getUseCntrNo());

			// 需為該分行額度序號才可動用
			// if (!user.getUnitNo().equals(cntrNo.substring(0, 3))) {

			// I-110-0028_05097_B1002 Web e-Loan企金授信額度明細表配合進出口業務集中化修改小行可以敘作大行動審表
			// 進出口小行可以幫大行作動審表
			if (!brnoList.contains(cntrNo.substring(0, 3))) {
				Properties pop = MessageBundleScriptCreator
						.getComponentResource(LMS1601M01Page.class);
				HashMap<String, String> param = new HashMap<String, String>();
				param.put("msg", pop.getProperty("L160M01A.message45"));
				// L160M01A.message45=不得動用額度序號非本行之額度！
				throw new CapMessageException(RespMsgHelper.getMessage(
						UtilConstants.AJAX_RSP_MSG.執行有誤, param), getClass());
			}

			// I-110-0028_05097_B1002 Web e-Loan企金授信額度明細表配合進出口業務集中化修改小行可以敘作大行動審表
			if (!user.getUnitNo().equals(cntrNo.substring(0, 3))) {
				// 進一步檢查只有進出口科目才能小行幫大行做動審表

				List<L140M01C> l140m01cs = lms1401Service
						.findL140m01cListByMainId(vusedoc01.getMainId());
				HashMap<String, String> itemMap = new HashMap<String, String>();
				for (L140M01C l140m01c : l140m01cs) {
					itemMap.put(l140m01c.getLoanTP(), "");
				}
				String checkItem18 = Util.trim(lmsService
						.getSysParamDataValue("LMS_CHECKITEM18_SUBJECT")); // "941,942,944,715,950,971";
				boolean hasCheckItem18 = false;
				String[] item18 = checkItem18.split(",");
				List<String> asList18 = Arrays.asList(item18);

				for (String key : itemMap.keySet()) {

					if (asList18.contains(Util.getLeftStr(key, 3))) {
						hasCheckItem18 = true;
					}

				}
				if (!hasCheckItem18) {
					Properties pop = MessageBundleScriptCreator
							.getComponentResource(LMS1601M01Page.class);
					HashMap<String, String> param = new HashMap<String, String>();
					param.put("msg", pop.getProperty("L160M01A.message45"));
					// L160M01A.message45=不得動用額度序號非本行之額度！
					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, param),
							getClass());
				}

			}

			if (cntrNoMap.containsKey(cntrNo)) {
				continue;
			}
			mainIdmapingCntrno.put(vusedoc01.getMainId(), cntrNo);
			cntrNoMap.put(cntrNo, "");
			selectMainId.add(vusedoc01.getMainId());
			L160M01B l160m01b = new L160M01B();
			l160m01b.setCntrNo(cntrNo);
			l160m01b.setMainId(mainMainId);
			l160m01b.setReMainId(vusedoc01.getMainId());
			l160m01b.setCreator(user.getUserId());
			l160m01b.setCreateTime(CapDate.getCurrentTimestamp());
			newL160m01bs.add(l160m01b);

		}
		List<L140M01A> l140m01as = lms1401Service
				.findL140m01aListByMainIdList(selectMainId
						.toArray(new String[selectMainId.size()]));
		List<L162S01A> newL162m01as = new ArrayList<L162S01A>();

		// 動審表引進時，動用期限要帶入第一筆額度明細表的動用期限
		Date useFromDate = null;
		Date useEndDate = null;
		// 2013/07/03,Rex,同業聯貸的檢查條件改為UnitLoanCase 才需檢查並登打，且需判斷選入的額度明細表是否有同業聯貸
		boolean haveUnitLoanCase = false;

		// J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
		List<L164S01A> newL164s01as = new ArrayList<L164S01A>();
		List<L120S01P> newL120s01ps = new ArrayList<L120S01P>();
		Map<String, String> processedCustId = new HashMap<String, String>();
		if (UtilConstants.Casedoc.DocType.企金.equals(l120m01a.getDocType())) {
			List<L120S01B> l120s01bs = (List<L120S01B>) amlRelateService
					.findListByMainId(L120S01B.class, l120m01a.getMainId());
			for (L120S01B l120s01b : l120s01bs) {
				if (l120s01b != null) {
					// 複製借款人基本資料 L120S01B L120S01A 到 L164S01A
					// J-109-0209_05097_B1001 e-Loan國內企金動審表增加借戶性質註記等進扣帳對象檢核項目
					newL164s01as.add(copyL120s01bToL164s01a(l120s01b,
							mainMainId, custContry));

					// 複製借款人實質受益人資料 L120S01P(簽報書MAINID)->L120S01P(動審表MAINID)
					newL120s01ps.addAll(copyL120s01pToNewL120s01p(l120s01b,
							mainMainId));
				}
			}
		} else {
			// 個金
		}

		for (L140M01A l140m01a : l140m01as) {

			String cntrNo = mainIdmapingCntrno.get(l140m01a.getMainId());

			// 複製連保人資料到主從債務人
			newL162m01as.addAll(this.copyL140m01IToL162m01a(l140m01a,
					mainMainId, custContry, cntrNo));

			if ("Y".equals(l160m01a.getUnitLoanCase())
					&& "Y".equals(Util.trim(l140m01a.getUnitCase2()))) {
				haveUnitLoanCase = true;
			}

			// 產生L161S01A 額度動用資訊明細 ***************

			L120M01B l120m01b = lms1401Service.findL120m01bByUniqueKey(l120m01a
					.getMainId());

			// 引進聯行攤貸比例
			Set<L140M01E> l140m01es = l140m01a.getL140m01e();

			if (l120m01b == null) {
				l120m01b = new L120M01B();
				l120m01b.setUnitCase("N");
				l120m01b.setUnitMega(l140m01es.isEmpty() ? "N" : "Y");
				l120m01b.setCoKind("0");
			}

			L161S01A l161s01a = lms1601Service.findL161m01aByMainIdCntrno(
					l160m01a.getMainId(), cntrNo);

			if (Util.isEmpty(l161s01a)) {
				l161s01a = new L161S01A();
				l161s01a.setCreator(user.getUserId());
				l161s01a.setCreateTime(l140m01a.getCreateTime());
				l161s01a.setMainId(l160m01a.getMainId());
				l161s01a.setUid(IDGenerator.getUUID());
			}

			l161s01a.setCustId(l140m01a.getCustId());
			l161s01a.setDupNo(l140m01a.getDupNo());
			l161s01a.setProperty(l140m01a.getProPerty());
			l161s01a.setCurrentApplyCurr(l140m01a.getCurrentApplyCurr());
			//G-113-0036 額度序號前三碼與簽案中額度序號前三碼不一致時，則帶入分配參帶金額
			//於處理L140M01E、L140M01E_AF時更新
			l161s01a.setCurrentApplyAmt(l140m01a.getCurrentApplyAmt());
			l161s01a.setCntrMainId(l140m01a.getMainId());
			l161s01a.setSnoKind(l140m01a.getSnoKind());
			l161s01a.setPrintSeq(l140m01a.getPrintSeq());
			l161s01a.setCntrNo(cntrNo);
			l161s01a.setUseSpecialReason("00");

			// J-105-0135-001 Web e-Loan國內企金授信系統動審表，開放可修改振興經濟非中小企業專案貸款註記與金額。
			// J-108-0098-001 企金處移除振興經濟非中小企相關欄位
			// l161s01a.setIsNonSMEProjLoan(l140m01a.getIsNonSMEProjLoan());
			// l161s01a.setNonSMEProjLoanAmt(l140m01a.getNonSMEProjLoanAmt());

			// J-106-0082-001 Web e-Loan國內企金授信系統，額度明細表新增中小企業創新發展專案貸款
			l161s01a.setInSmeFg(l140m01a.getInSmeFg());
			l161s01a.setInSmeToAmt(l140m01a.getInSmeToAmt());
			l161s01a.setInSmeCaAmt(l140m01a.getInSmeCaAmt());

			// Map<String, Object> quotapprMap = misdbBASEService
			// .findLastQuotapprOrderBySDate(l140m01a.getCustId(),
			// l140m01a.getDupNo(), l140m01a.getCntrNo());

			Map<String, Object> l161s01aMap = null;
			List<Map<String, Object>> l161s01aList = eloandbBASEService
					.findL161s01aLastRescueDataWithRescueItem(
							Util.trim(l161s01a.getCntrNo()),
							Util.trim(l161s01a.getCustId()),
							Util.trim(l161s01a.getDupNo()),
							Util.trim(l140m01a.getRescueItem()));

			if (l161s01aList != null && !l161s01aList.isEmpty()) {
				for (Map<String, Object> t161Map : l161s01aList) {
					l161s01aMap = t161Map;
					break;
				}
			}

			// J-109-0077_05097_B1001 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
			l161s01a.setIsRescue(l140m01a.getIsRescue());
			l161s01a.setRescueItem(l140m01a.getRescueItem());

			String rescueItem = Util.trim(l140m01a.getRescueItem());
			String rescueItemSub = Util.trim(l140m01a.getRescueItemSub());

			if (lmsService.isResueItemOldCase(rescueItem)) {
				// 預設從額度明細表來
				l161s01a.setRescueRate(l140m01a.getRescueRate());
			} else if (lmsService.isResueItemRescueRate(rescueItem,
					rescueItemSub)) {

				if (l161s01aMap == null || l161s01aMap.isEmpty()) {
					// QUOTAPPR 沒有值
					l161s01a.setRescueRate(null);
				} else {
					String RESCUERATE = Util.trim(MapUtils.getString(
							l161s01aMap, "RESCUERATE", ""));
					if (Util.equals(RESCUERATE, "")) {
						// QUOTAPPR 沒有值
						l161s01a.setRescueRate(null);
					} else {
						l161s01a.setRescueRate(Util.parseBigDecimal(RESCUERATE));
					}
				}

			}

			// J-109-0077_05097_B1003因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
			l161s01a.setIsExtendSixMon(l140m01a.getIsExtendSixMon());

			// J-109-0077_05097_B1008 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
			// 金爺調整20200414
			// 至忠，上午 11:34
			// 動審表合意展延日,減收利率, 幫我改成首次合意展延日及減收利率(再次展延與首次展延之減收利率應一致)
			// 至忠，上午 11:49
			// 動審表先讀取原本ELFXXX的資料, 若沒有在讀取簽報書的
			// 最近一次核准動審表 QUOTAPPR RESCUEIBDT
			// 取得該額度ELF383最新一筆
			// 動審表是首次，簽報書可能是非首次，所以優先以最近一次覆核的動審表為主

			if (l161s01aMap == null || l161s01aMap.isEmpty()) {
				// QUOTAPPR 沒有值，以簽報書為主
				l161s01a.setRescueIbDate(l140m01a.getRescueIbDate());
			} else {
				String RESCUEIBDATE = Util.trim(MapUtils.getString(l161s01aMap,
						"RESCUEIBDATE", ""));
				if (Util.equals(RESCUEIBDATE, "")) {
					// QUOTAPPR 沒有值，以簽報書為主
					l161s01a.setRescueIbDate(l140m01a.getRescueIbDate());
				} else {
					l161s01a.setRescueIbDate(Util.parseDate(RESCUEIBDATE));
				}
			}

			l161s01a.setRescueCurr(l140m01a.getRescueCurr());
			l161s01a.setRescueAmt(l140m01a.getRescueAmt());
			l161s01a.setIsCbRefin(l140m01a.getIsCbRefin());

			// J-109-0077_05097_B1005 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
			l161s01a.setRescueItemSub(l140m01a.getRescueItemSub());

			// J-109-0077_05097_B1006 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
			l161s01a.setRescueDate(l140m01a.getRescueDate());

			// J-109-0077_05097_B1008 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
			l161s01a.setHeadItem1(l140m01a.getHeadItem1());
			l161s01a.setGutPercent(l140m01a.getGutPercent());
			l161s01a.setGutCutDate(l140m01a.getGutCutDate());
			l161s01a.setCgfRate(l140m01a.getCgfRate());
			l161s01a.setCgfDate(l140m01a.getCgfDate());
			l161s01a.setIsGuaOldCase(l140m01a.getIsGuaOldCase());
			l161s01a.setByNewOld(l140m01a.getByNewOld());

			if (l161s01aMap == null || l161s01aMap.isEmpty()) {
				// QUOTAPPR 沒有值
				l161s01a.setRescueNo("");
				l161s01a.setEmpCount(null);
			} else {
				String RESCUENO = Util.trim(MapUtils.getString(l161s01aMap,
						"RESCUENO", ""));
				String EMPCOUNT = Util.trim(MapUtils.getString(l161s01aMap,
						"EMPCOUNT", ""));
				if (Util.equals(RESCUENO, "")) {
					// QUOTAPPR 沒有值
					l161s01a.setRescueNo("");
				} else {
					l161s01a.setRescueNo(RESCUENO);
				}

				if (Util.equals(EMPCOUNT, "")) {
					// QUOTAPPR 沒有值
					l161s01a.setEmpCount(null);
				} else {
					l161s01a.setEmpCount(Util.parseToBigDecimal(EMPCOUNT));
				}

			}

			// J-110-0288_05097_B1001 Web
			// e-Loan配合辦理「行政院國家發展基金協助新創事業紓困融資加碼方案」，修改額度明細表欄位

			l161s01a.setRescueNdfGutPercent(l140m01a.getRescueNdfGutPercent());

			// J-110-0465_05097_B1001 Web e-Loan國內企金授信動審表新增額度編號
			// 額度編號
			// if (!lmsService.isResueItemNeedNdfGutPercent(rescueItem)) {
			// J-112-0148_05097_B1002 Web
			// e-Loan企金授信新增經濟部協助中小型事業疫後振興專案貸款暨經濟部協助中小企業轉型發展專案貸款
			if (!lms1601Service.isResueItemRescueSn(rescueItem)) {
				l161s01a.setRescueSn("");
			} else {
				if (l161s01aMap == null || l161s01aMap.isEmpty()) {
					l161s01a.setRescueSn("");
				} else {
					String RESCUESN = Util.trim(MapUtils.getString(l161s01aMap,
							"RESCUESN", ""));
					if (Util.equals(RESCUESN, "")) {
						// RESCUESN 沒有值
						l161s01a.setRescueSn("");
					} else {
						l161s01a.setRescueSn(RESCUESN);
					}
				}
			}

			// J-110-0288_05097_B1002 Web e-Loan配合國發基金新創事業紓困加碼方案H01、A07專案修改
			if (!lmsService.isResueItemNeedIsTurnoverDecreased(rescueItem)) {
				l161s01a.setIsTurnoverDecreased("");
			} else {
				if (l161s01aMap == null || l161s01aMap.isEmpty()) {
					// QUOTAPPR 沒有值
					l161s01a.setIsTurnoverDecreased("");
				} else {
					String ISTURNOVERDECREASED = Util.trim(MapUtils.getString(
							l161s01aMap, "ISTURNOVERDECREASED", ""));

					if (Util.equals(ISTURNOVERDECREASED, "")) {
						// QUOTAPPR 沒有值
						l161s01a.setIsTurnoverDecreased("");
					} else {
						l161s01a.setIsTurnoverDecreased(ISTURNOVERDECREASED);
					}

				}

				// 如果還是空白，判斷是不是中小企業
				if (Util.equals(Util.trim(l161s01a.getIsTurnoverDecreased()),
						"")) {

					// 非中小企業或視同中小企業時，預設值塞X不適用
					JSONObject jsonData = new JSONObject();
					jsonData = lmsService.getCustBusCDAndClass(
							l161s01a.getCustId(), l161s01a.getDupNo());
					String custClass = Util.trim(jsonData.optString(
							"custClass", ""));
					if (Util.notEquals(custClass, "")) {
						if (Util.notEquals(custClass, "4")
								&& Util.notEquals(custClass, "8")) {
							// 4.民營中小企業、8.視同中小企業
							// 大型企業要塞X不適用
							l161s01a.setIsTurnoverDecreased("X");
						}
					}

				}

			}

			// J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制
			String[] needCol = new String[] { "isClearLand", "ctlType",
					"fstDate", "lstDate", "isChgStDate", "cstDate",
					"cstReason", "adoptFg", "isChgRate", "rateAdd", "custRoa",
					"relRoa", "roaBgnDate", "roaEndDate", "isLegal",
					"actStartDate" };

			// 清除其他欄位
			for (String col : needCol) {
				l161s01a.set(col, null);
			}

			boolean hasIsClearLand = false;
			L140M01M l140m01m = lmsService.findModelByMainId(L140M01M.class,
					l140m01a.getMainId());
			if (l140m01m != null) {
				// 引進額度明細表
				if (Util.notEquals(Util.trim(l140m01m.getIsClearLand()), "")) {
					hasIsClearLand = true;
					CapBeanUtil.copyBean(l140m01m, l161s01a, needCol);
					lms1601Service.copyRoaL120s04List(l140m01m, l161s01a);
				}
			}

			// J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制
			if (!hasIsClearLand) {
				// 簽報書沒有註記，改抓ELF600

				ELF600 elf600 = misELF600Service.findByContract(cntrNo);
				String isClearLand = lmsService.isClearLandEffective(elf600);
				l161s01a.setIsClearLand(isClearLand);
				if (Util.equals(isClearLand, "Y")) {
					l161s01a.setCtlType(Util.trim(elf600.getElf600_ctltype()));
					l161s01a.setFstDate(elf600.getElf600_fstdate());
					l161s01a.setLstDate(elf600.getElf600_lstdate());

				} else {
					l161s01a.setCtlType("");
					l161s01a.setFstDate(null);
					l161s01a.setLstDate(null);
				}
			}

			Map<String, String> caseMap = lmsService.getCaseType("2",
					l120m01a.getMainId(), l120m01b, l140m01a);

			String tmpUnitCase = caseMap.get("tmpUnitCase");
			String tmpMainBranch = caseMap.get("tmpMainBranch");
			String caseType = caseMap.get("caseType");

			l161s01a.setCaseType(caseType);

			l161s01a.setUnitCase(tmpUnitCase);

			if (Util.equals(tmpUnitCase, "Y")) {
				l161s01a.setUCMainBranch(Util.isEmpty(l120m01b) ? "N"
						: l120m01b.getUCMainBranch());
				l161s01a.setUCntBranch(tmpMainBranch);
				l161s01a.setUCMSBranch(Util.isEmpty(l120m01b) ? "N" : l120m01b
						.getUCMSBranch());
				l161s01a.setUHideName(Util.isEmpty(l120m01b) ? "N" : l120m01b
						.getUHideName());
				l161s01a.setUArea(Util.isEmpty(l120m01b) ? "N" : l120m01b
						.getUArea());
				l161s01a.setURP1(Util.isEmpty(l120m01b) ? "N" : l120m01b
						.getURP1());
				l161s01a.setURP2(Util.isEmpty(l120m01b) ? "N" : l120m01b
						.getURP2());
				l161s01a.setURP3(Util.isEmpty(l120m01b) ? "N" : l120m01b
						.getURP3());
			} else {
				l161s01a.setUCMainBranch("N");
				l161s01a.setUCntBranch("N");
				l161s01a.setUCMSBranch("N");
				l161s01a.setUHideName("N");
				l161s01a.setUArea("N");
				l161s01a.setURP1("N");
				l161s01a.setURP2("N");
				l161s01a.setURP3("N");
			}

			l161s01a.setUnitMega("N");
			if (Util.equals(UtilConstants.Usedoc.caseType.同業聯貸主辦含自行聯貸, caseType)
					|| Util.equals(UtilConstants.Usedoc.caseType.同業聯貸參貸含自行聯貸,
							caseType)
					|| Util.equals(UtilConstants.Usedoc.caseType.自行聯貸, caseType)) {
				l161s01a.setUnitMega("Y");

			}

			l161s01a.setCoKind(Util.isEmpty(l120m01b) ? "N" : l120m01b
					.getCoKind());
			l161s01a.setMCntrt(Util.isEmpty(l120m01b) ? "N" : l120m01b
					.getMCntrt());
			l161s01a.setSCntrt(Util.isEmpty(l120m01b) ? "N" : l120m01b
					.getSCntrt());
			l161s01a.setMScntrt(Util.isEmpty(l120m01b) ? "N" : l120m01b
					.getMScntrt());
			l161s01a.setMSAcc(Util.isEmpty(l120m01b) ? "N" : l120m01b
					.getMSAcc());

			l161s01a.setCaseYear(l120m01a.getCaseYear());
			l161s01a.setCaseBrId(Util.trim(l120m01a.getCaseBrId()));
			l161s01a.setCaseSeq(l120m01a.getCaseSeq());
			l161s01a.setCaseNo(Util.trim(l120m01a.getCaseNo()));
			l161s01a.setSignDate(l120m01a.getCaseDate());

			if (Util.equals(tmpUnitCase, "Y")) {
				// 有同業時才要引進案由
				l161s01a.setGist(l120m01a.getGist());
				l161s01a.setQuotaCurr(l140m01a.getCurrentApplyCurr());
			}

			// G-113-0036 動審後有新的攤貸設定則帶最新的攤貸比例
			// 引進聯行攤貸比例，如來自聯行額度明細(dataSrc)則預設把分配到的金額寫到L161S01A.currentApplyAmt(現請額度)
			Set<L140M01E_AF> l140m01e_afs = l140m01a.getL140m01e_af();
			if (l140m01e_afs.isEmpty()) {
				if (!l140m01es.isEmpty()) {
					for (L140M01E l140m01e : l140m01es) {
						L161S01B l161s01b = new L161S01B();
						l161s01b.setCreator(user.getUserId());
						l161s01b.setCreateTime(l140m01e.getCreateTime());
						l161s01b.setMainId(l160m01a.getMainId());
						l161s01b.setPid(l161s01a.getUid());
						l161s01b.setSeq(lms1601Service.findL161m01bMaxSeq(
								l160m01a.getMainId(), l161s01a.getUid()));
						l161s01b.setSlBankType("01");
						l161s01b.setSlBank(UtilConstants.兆豐銀行代碼);
						Map<String, Object> map = misdbBASEService.findSYNBANK("");
						l161s01b.setSlBankCN((String) map.get("BRNNAME"));
						l161s01b.setSlBranchCN(branchService.getBranchName(l140m01e
								.getShareBrId()));
						l161s01b.setSlBranch(l140m01e.getShareBrId());
						l161s01b.setSlCurr(l140m01a.getCurrentApplyCurr());
						l161s01b.setSlAmt(l140m01e.getShareAmt());
						//G-113-0036 如非額度管理行, 則現請額度預設帶被分配到的金額
						if (Util.equals(l140m01e.getShareNo(), l161s01a.getCntrNo())) {//140m01e/額度序號 = l161s01a額度序號
							l161s01a.setCurrentApplyAmt(this.checkL161s01aCurrentApplyAmt(l140m01a, l161s01a, l140m01e.getShareAmt()));
						}
						lms1601Service.save(l161s01b);
						//動審攤貸比例init
						L140M01E_AF l140m01e_af = new L140M01E_AF();
						CapBeanUtil.copyBean(l140m01e, l140m01e_af, CapBeanUtil.getFieldName(L140M01E.class, true));// 複製的語法
						l140m01e_af.setOid(null);
						l140m01e_af.setCreator(user.getUserId());
						l140m01e_af.setCreateTime(CapDate.getCurrentTimestamp());
						lms1401Service.save(l140m01e_af);
					}
				}
			} else{//動審後攤貸比例(如有新調整過的以新的AF為準
				for (L140M01E_AF l140m01e_af : l140m01e_afs) {
					L161S01B l161s01b = new L161S01B();
					l161s01b.setCreator(user.getUserId());
					l161s01b.setCreateTime(l140m01e_af.getCreateTime());
					l161s01b.setMainId(l160m01a.getMainId());
					l161s01b.setPid(l161s01a.getUid());
					l161s01b.setSeq(lms1601Service.findL161m01bMaxSeq(
							l160m01a.getMainId(), l161s01a.getUid()));
					l161s01b.setSlBankType("01");
					l161s01b.setSlBank(UtilConstants.兆豐銀行代碼);
					Map<String, Object> map = misdbBASEService.findSYNBANK("");
					l161s01b.setSlBankCN((String) map.get("BRNNAME"));
					l161s01b.setSlBranchCN(branchService.getBranchName(l140m01e_af
							.getShareBrId()));
					l161s01b.setSlBranch(l140m01e_af.getShareBrId());
					l161s01b.setSlCurr(l140m01a.getCurrentApplyCurr());
					l161s01b.setSlAmt(l140m01e_af.getShareAmt());
					//G-113-0036 如非額度管理行, 則現請額度預設帶被分配到的金額
					if (Util.equals(l140m01e_af.getShareNo(), l161s01a.getCntrNo())) {//140m01e/額度序號 = l161s01a額度序號
						l161s01a.setCurrentApplyAmt(this.checkL161s01aCurrentApplyAmt(l140m01a, l161s01a, l140m01e_af.getShareAmt()));
					}
					lms1601Service.save(l161s01b);
				}
			}

			// J-103-0202-005 Web e-Loan授信簽案衍生性金融商品遠匯與換匯科目，改以交易額度來簽案。
			l161s01a.setIsDerivatives(UtilConstants.DEFAULT.否);
			ArrayList<String> itemsAll = new ArrayList<String>();
			List<L140M01C> l140m01cs = lms1401Service
					.findL140m01cListByMainId(l140m01a.getMainId());

			if (l140m01cs != null && !l140m01cs.isEmpty()) {
				for (L140M01C l140m01c : l140m01cs) {
					itemsAll.add(l140m01c.getLoanTP());
				}

				Boolean hasDerivateSubjectFlag = false;
				hasDerivateSubjectFlag = lmsService.hasDerivateSubject(itemsAll
						.toArray(new String[itemsAll.size()]));
				if (hasDerivateSubjectFlag == true) {
					l161s01a.setIsDerivatives(UtilConstants.DEFAULT.是);
				}
			} else {
				// 找不到額度明細表
				Properties pop = MessageBundleScriptCreator
						.getComponentResource(LMS1601M01Page.class);
				throw new CapMessageException(
						pop.getProperty("L160M01A.message66"), getClass());
			}

			// J-103-0317-001 Web e-Loan企金簽報書上傳承諾事項與追蹤檢視日期

			L161S01C l161s01c = lms1601Service.findL161s01cByMainIdPidItemType(
					l161s01a.getMainId(), l161s01a.getUid(),
					UtilConstants.Cntrdoc.l140m01bItemType.其他敘做條件_動撥提示用語);

			if (Util.isEmpty(l161s01c)) {
				l161s01c = new L161S01C();
				l161s01c.setCreator(user.getUserId());
				l161s01c.setCreateTime(CapDate.getCurrentTimestamp());
				l161s01c.setMainId(l161s01a.getMainId());
				l161s01c.setCntrNo(l161s01a.getCntrNo());
				l161s01c.setPid(l161s01a.getUid());
				l161s01c.setItemType(UtilConstants.Cntrdoc.l140m01bItemType.其他敘做條件_動撥提示用語);
			}

			String LMS_ToAloan2CanShow = chkArCtrlCanShow();
			if (Util.equals(LMS_ToAloan2CanShow, "Y")) {
				L140M01B l140m01b = lms1401Service.findL140m01bUniqueKey(
						l140m01a.getMainId(),
						UtilConstants.Cntrdoc.l140m01bItemType.其他敘做條件_動撥提示用語);
				if (l140m01b != null) {
					l161s01c.setToALoan(l140m01b.getToALoan());
					l161s01c.setItemDscr(l140m01b.getItemDscr());
				} else {
					l161s01c.setToALoan("");
					l161s01c.setItemDscr("");
				}
			} else {
				l161s01c.setToALoan("");
				l161s01c.setItemDscr("");
			}

			// J-111-0506_05097_B1001 Web e-Loan企金授信動審表增加授信作業手續費之欄位
			l161s01a.setIsOperationFee(l140m01a.getIsOperationFee());

			// ***********************************************************

			l161s01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.已計算);

			if (Util.equals(tmpUnitCase, UtilConstants.DEFAULT.是)
					&& Util.equals(tmpMainBranch, UtilConstants.DEFAULT.是)) {
				l161s01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);
			}

			if (Util.equals(l161s01a.getIsDerivatives(),
					UtilConstants.DEFAULT.是)) {
				l161s01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);
			}

			// J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制
			if (Util.equals(l161s01a.getIsClearLand(), UtilConstants.DEFAULT.是)) {
				if (Util.equals(Util.trim(l161s01a.getIsChgStDate()), "")) {
					// 如果簽報書沒有空地貸款註記，是動審表引進時才引進ELF600，且是空地貸款控管註記，則必須由分行確認是否要變更預計動工日
					l161s01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);

				}

			}

			// J-109-0077_05097_B1003 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
			if (Util.equals(l161s01a.getIsRescue(), UtilConstants.DEFAULT.是)) {
				rescueItem = Util.trim(l161s01a.getRescueItem());
				if (Util.equals(rescueItem, "")) {
					l161s01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);
				}

				// J-109-0077_05097_B1006 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
				Date rescueDate = l161s01a.getRescueDate();
				if (rescueDate == null) {
					// L140M01a.rescueDate=受理日期
					l161s01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);
				}

				String isCbRefin = Util.trim(l161s01a.getIsCbRefin());
				if (Util.equals(isCbRefin, "")) {
					// L140M01a.isCbRefin=本案資金來源是否為央行轉融通
					l161s01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);
				}

				if (Util.equals(isCbRefin, "Y")) {
					// J-109-0811_05097_B1001
					// 配合「嚴重特殊傳染性肺炎防治及妤困振興特別條例」施行期間調整，動審表新增央行優惠利率融通期限
					// 動審表時才要輸入央行優惠利率融通期限
					if (Util.equals(Util.trim(l161s01a.getCbRefinDt()), "")) {
						l161s01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);
					}
				}

				rescueItemSub = Util.trim(l161s01a.getRescueItemSub());
				if (lmsService.needRescueItemSub(rescueItem)) {
					if (Util.equals(rescueItemSub, "")) {
						// L140M01a.rescueItemSub=合併申請紓困方案
						l161s01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);
					}
				}

				if (lmsService.isResueItemRescueRate(rescueItem, rescueItemSub)) {
					BigDecimal rescueRate = null;
					rescueRate = l161s01a.getRescueRate();
					if (rescueRate == null) {
						// L140M01a.rescueRate=減收利率
						l161s01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);
					}

					// J-109-0077_05097_B1017 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
					if (lmsService.isResueItemRescueRateNotZero(rescueItem,
							rescueItemSub)) {
						if (rescueRate != null
								&& BigDecimal.ZERO.compareTo(rescueRate) >= 0) {
							// L140M01a.rescueRate=減收利率
							l161s01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);
						}
					}

					// J-110-0288_05097_B1003 Web
					// e-Loan配合國發基金新創事業紓困加碼方案H01、A07專案修改
					// 國發基金新創事業紓困加碼方案H01、A07專案(補充)

					// J-109-0077_05097_B1017 因應政府嚴重特殊傳染性肺炎紓困方案實施需要,
					// 配合新增相關作業
					// 檢核紓困貸款類別利率不得超過

					if (rescueRate != null) {

						// 預設一定都不能超過於上限
						String chkRateLessError = lmsService
								.chkRescueItemRescueRateLess(rescueItem,
										rescueItemSub, rescueRate);
						if (Util.notEquals(chkRateLessError, "")) {
							l161s01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);
						}

						// J-110-0288_05097_B1003 Web
						// e-Loan配合國發基金新創事業紓困加碼方案H01、A07專案修改
						if (lmsService
								.isResueItemNeedIsTurnoverDecreased(rescueItem)
								&& !Util.isEmpty(l161s01a
										.getIsTurnoverDecreased())) {
							if (Util.equals(Util.trim(l161s01a
									.getIsTurnoverDecreased()), "Y")) {
								// 中小企業符合紓困4.0利息補貼
								// 【A07】X

								// 必須輸入欄位 P7 or 0

								// J-109-0077_05097_B1017 因應政府嚴重特殊傳染性肺炎紓困方案實施需要,
								// 配合新增相關作業
								// 檢核紓困貸款類別利率不得低於
								// 可以為0
								if (rescueRate != null
										&& rescueRate
												.compareTo(BigDecimal.ZERO) != 0) {
									String chkRateLessError2 = lmsService
											.chkRescueItemRescueRateHigh(
													rescueItem, rescueItemSub,
													rescueRate);
									if (Util.notEquals(chkRateLessError2, "")) {
										l161s01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);
									}
								}

							} else {
								// 中小企業不符合紓困4.0 利息補貼
								// 大型企業
								// 【A07】Y

								// 必須輸入欄位 P7

								// J-110-0288_05097_B1002 Web
								// e-Loan配合國發基金新創事業紓困加碼方案H01、A07專案修改
								// 檢核紓困貸款類別利率不得低於
								// 不可以為0
								if (rescueRate != null) {
									String chkRateLessError2 = lmsService
											.chkRescueItemRescueRateHigh(
													rescueItem, rescueItemSub,
													rescueRate);
									if (Util.notEquals(chkRateLessError2, "")) {
										l161s01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);
									}
								}

							}

						} else {
							// J-110-0288_05097_B1002 Web
							// e-Loan配合國發基金新創事業紓困加碼方案H01、A07專案修改
							// 檢核紓困貸款類別利率不得低於
							if (rescueRate != null) {
								String chkRateLessError2 = lmsService
										.chkRescueItemRescueRateHigh(
												rescueItem, rescueItemSub,
												rescueRate);
								if (Util.notEquals(chkRateLessError2, "")) {
									l161s01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);
								}
							}

						}
					}

				}

				// J-109-0077_05097_B1015 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
				if (lmsService.isResueItemNeedEmpCount(rescueItem,
						rescueItemSub)) {
					BigDecimal empCount = null;
					empCount = l161s01a.getEmpCount();
					if (empCount == null) {
						// L160M01A.empCount=現有員工人數
						l161s01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);
					} else {
						if (BigDecimal.ZERO.compareTo(empCount) > 0) {
							// L160M01A.empCount=現有員工人數
							l161s01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);
						}
					}
				}

				// J-109-0077_05097_B1018 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
				// A07 Y, Z 可以不輸入掛件文號
				// Y.符合經濟部補助要點適用對象 中小企業 不符合紓困4.0 利息補貼 A07 =>營收減少15%為N
				// Z.符合經濟部補助要點適用對象 大型企業 A07 =>營收減少15%為X
				if (lmsService.isResueItemNeedRescueNo(rescueItem,
						rescueItemSub)) {

					if (lmsService
							.isResueItemNeedIsTurnoverDecreased(rescueItem)
							&& !Util.isEmpty(l161s01a.getIsTurnoverDecreased())) {

						// 洪真逢 10429/MEGABANK/MEGA (企金業務處)
						// 2021/08/13 上午 11:16
						// 建霖 您好
						// "國發基金紓困貸款"原先選擇 A07, 僅通知您 X 狀況需輸入掛件文號
						// 經洽經理銀行 , 麻煩您更改 X,Y,Z 狀況, 皆需 輸入 掛件文號

						// if (Util.equals(
						// Util.trim(l161s01a.getIsTurnoverDecreased()),
						// "Y")) {
						// // 符合紓困4.0 利息補貼 A07一定要有掛件文號
						// String rescueNo = Util.trim(l161s01a.getRescueNo());
						// if (Util.equals(rescueNo, "")) {
						// // L160M01A.rescueNo=掛件文號
						// l161s01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);
						// }
						// } else {
						// // A07 Y, Z 可以不輸入掛件文號
						// // Y.符合經濟部補助要點適用對象 中小企業 不符合紓困4.0 利息補貼 A07
						// // =>營收減少15%為N
						// // Z.符合經濟部補助要點適用對象 大型企業 A07 =>營收減少15%為X
						// }

						String rescueNo = Util.trim(l161s01a.getRescueNo());
						if (!lms1401Service
								.isRescueItemCanEmptyRescueNo(rescueItem)) {
							if (Util.equals(rescueNo, "")) {
								// L160M01A.rescueNo=掛件文號
								l161s01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);
							}
						}

					} else {
						String rescueNo = Util.trim(l161s01a.getRescueNo());
						if (!lms1401Service
								.isRescueItemCanEmptyRescueNo(rescueItem)) {
							if (Util.equals(rescueNo, "")) {
								// L160M01A.rescueNo=掛件文號
								l161s01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);
							}
						}

					}

				}

				if (lmsService.isResueItemOldCase(rescueItem) || rescueItem.matches("J04|J05")) {
					// J-109-0077_05097_B1003 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業

					String isExtendSixMon = "";
					Date rescueIbDate = null;
					BigDecimal rescueAmt = null;
					String rescueCurr = "";

					isExtendSixMon = Util.trim(l161s01a.getIsExtendSixMon());
					if (Util.equals(isExtendSixMon, "")) {
						// L140M01a.isExtendSixMon=本案是否展延六個月
						l161s01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);
					}

					rescueIbDate = l161s01a.getRescueIbDate();
					if (rescueIbDate == null) {
						// L140M01a.rescueIbDate=合意展延日
						l161s01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);
					}

					rescueAmt = l161s01a.getRescueAmt();
					if (rescueAmt == null) {
						// L140M01a.rescueAmt=截至1090312前既有之本行貸款餘額
						l161s01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);
					}

					rescueCurr = Util.trim(l161s01a.getRescueCurr());
					if (Util.equals(rescueCurr, "")) {
						// L140M01a.rescueCurr=截至1090312前既有之本行貸款幣別
						l161s01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);
					}

				}

				if (Util.isEmpty(Util.trim(l161s01a.getHeadItem1()))) {
					// L140M01a.headItem1=本案是否移送信保基金保證
					l161s01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);
				}

				// J-110-0288_05097_B1001 Web
				// e-Loan配合辦理「行政院國家發展基金協助新創事業紓困融資加碼方案」，修改額度明細表欄位
				if (lmsService.isResueItemNeedNdfGutPercent(rescueItem)) {
					if (Util.isEmpty(l161s01a.getRescueNdfGutPercent())) {
						// L140M01a.rescueNdfGutPercent=國發基金加碼保證成數
						l161s01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);
					}
				}

				// J-112-0148_05097_B1002 Web
				// e-Loan企金授信新增經濟部協助中小型事業疫後振興專案貸款暨經濟部協助中小企業轉型發展專案貸款
				if (lms1601Service.isResueItemRescueSn(rescueItem)) {
					// J-110-0465_05097_B1001 Web e-Loan國內企金授信動審表新增額度編號
					if (Util.isEmpty(Util.trim(l161s01a.getRescueSn()))) {
						// L160M01A.rescueSn=額度編號
						l161s01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);
					}
				}

				// J-110-0288_05097_B1002 Web e-Loan配合國發基金新創事業紓困加碼方案H01、A07專案修改
				if (lmsService.isResueItemNeedIsTurnoverDecreased(rescueItem)) {
					if (Util.isEmpty(l161s01a.getIsTurnoverDecreased())) {
						// L160M01A.isTurnoverDecreased=是否符合110年5~12月營業額減少達15%
						l161s01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);
					}
				}

				// J-111-0214_05097_B1001 Web e-Loan國內企金動用審核表新增可適用新利率計算減免息相關功能
				if (lms1601Service.needRescueChgRateFg(rescueItem)) {

					String rescueChgRateFg = "";
					Date rescueChgRateSingDate = null;
					BigDecimal rescueChgRate = null;
					Date rescueChgRateEffectDate = null;

					rescueChgRateFg = Util.trim(l161s01a.getRescueChgRateFg());
					if (Util.equals(rescueChgRateFg, "")) {
						// L161S01A.rescueChgRateFg=客戶是否申請調整補貼利率
						l161s01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);
					}

					if (Util.equals(rescueChgRateFg, "Y")) {

						rescueChgRateSingDate = l161s01a
								.getRescueChgRateSingDate();
						if (rescueChgRateSingDate == null) {
							// L161S01A.rescueChgRateSingDate=簽約日(增補合約)
							l161s01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);
						}

						rescueChgRate = l161s01a.getRescueChgRate();
						if (rescueChgRate == null) {
							// L161S01A.rescueChgRate=調整後利率
							l161s01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);
						}

						// rescueChgRateEffectDate =
						// l161s01a.getRescueChgRateEffectDate();
						// if (rescueChgRateSingDate == null) {
						// // L161S01A.rescueChgRateEffectDate=客戶利率調升日
						// l161s01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);
						// }
					}

				}

			}

			// J-109-0371_05097_B1001 簡化青年創業及啟動金貸款簽報書簽案流程
			if (UtilConstants.DEFAULT.是.equals(l161s01a.getHeadItem1())) {
				// 檢核送保為Y要輸入保證成數
				// J-112-0366_12473_B1001 送保方式為批次送保時不檢核
				if (!"3".equals(Util.trim(l140m01a.getGutType())) && Util.isEmpty(l161s01a.getGutPercent())) {
					// L140M01a.gratio=保證成數
					l161s01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);
				}

				// 檢核送保為Y要輸入信保首次動用有效期限
				if (Util.isEmpty(l161s01a.getGutCutDate())) {
					// L140M01a.gutCutDate=信保首次動用有效期限
					l161s01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);
				}

				// J-107-0137_05097_B1001 Web
				// e-Loan企金授信額度明細表，新增「信保基金保證書發文日期」與「信保基金核准之保證手續費率」
				// 檢核送保為Y要輸入信保基金核准之保證手續費率
				if (Util.isEmpty(l161s01a.getCgfRate())) {
					// L140M01a.cgfRate=信保基金核准之保證手續費率
					l161s01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);
				}

				// 檢核送保為Y要輸入信保基金保證書發文日期
				if (Util.isEmpty(l161s01a.getCgfDate())) {
					// L140M01a.cgfDate=信保基金保證書發文日期
					l161s01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);
				}

				// 檢核送保為Y要輸入本案是否為信保續約案
				if (Util.isEmpty(l161s01a.getIsGuaOldCase())) {
					// L140M01a.isGuaOldCase=本案是否為信保續約案
					l161s01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);
				} else {
					if (Util.equals(l161s01a.getIsGuaOldCase(), "Y")) {
						// 檢核信保續約案為Y要輸入是否可借新還舊
						if (Util.isEmpty(l161s01a.getByNewOld())) {
							// L140M01a.byNewOld=是否可借新還舊
							l161s01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);
						}
					}
				}

			}

			// J-111-0506_05097_B1001 Web e-Loan企金授信動審表增加授信作業手續費之欄位
			if (Util.notEquals(Util.trim(l161s01a.getIsOperationFee()), "N")) {
				// 是否收取企金授信作業手續費不是N，代表沒填、或是Y的時候有收取金額 、幣別、最晚收取日還要填
				l161s01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);
			}

			lms1601Service.save(l161s01a, l161s01c);

			// ********************************************

			if (useFromDate == null) {
				if ("1".equals(l140m01a.getUseDeadline())) {
					String[] desp1 = Util.trim(l140m01a.getDesp1()).split("~");
					if (desp1.length == 2) {

						String date1 = Util.trim(desp1[0]);
						if (Util.isNotEmpty(date1)) {
							useFromDate = CapDate.getDate(date1,
									UtilConstants.DateFormat.YYYY_MM_DD);
						}

						String date2 = Util.trim(desp1[1]);
						if (Util.isNotEmpty(date2)) {
							useEndDate = CapDate.getDate(date2,
									UtilConstants.DateFormat.YYYY_MM_DD);
						}
					}

				} else if ("8".equals(l140m01a.getUseDeadline())) {
					// J-110-0320 為符營業單位陳報團貸實務作業，於ELOAN系統新增團貸動用期限選項
					// 自核准日起~YYYY-MM-DD
					String[] desp1 = Util.trim(l140m01a.getDesp1()).split("~");
					if (desp1.length == 2) {
						String date2 = Util.trim(desp1[1]);
						if (Util.isNotEmpty(date2)) {
							useFromDate = l120m01a.getEndDate();
							useEndDate = CapDate.getDate(date2,
									UtilConstants.DateFormat.YYYY_MM_DD);
						}
					}

				}
			}

			// 小規模與青創計收遲延利息加碼預設改為3%
			if (Util.equals(Util.trim(l140m01a.getIsRescue()), "Y")
					&& Util.equals(Util.trim(l140m01a.getIsCbRefin()), "Y")
					&& Util.equals(Util.trim(l140m01a.getIsSmallBuss()), "C")) {
				// dRateAdd
				l160m01a.setDRateAdd(Util.parseBigDecimal("3"));
			} else if (Util.equals(Util.trim(l140m01a.getLnType()), "61")) {
				l160m01a.setDRateAdd(Util.parseBigDecimal("3"));
			}

		}

		// 複製L901M01A審核項目
		List<L160M01C> l160m01cs = null;
		if (l160m01a != null) {
			List<L160M01C> l160m01c_Olds = (List<L160M01C>) lms1601Service
					.findListByMainId(L160M01C.class, l160m01a.getMainId());
			lms1601Service.deleteL160m01cs(l160m01c_Olds);
			if (lmsService.isRescueSmallBussOnlyCaseC(l160m01a)) {
				l160m01cs = this.copyL901m01aToL160m01c_SmallBussOnlyCaseC(
						l160m01a.getMainId(), LocaleContextHolder.getLocale().toString());
				l160m01a.setIsSmallBuss("Y");
				l160m01a.setSmallBussChkListDate(Util.parseDate("2020-06-02"));
			} else if (lmsService.isRescueOnlyCaseF(l160m01a)) {
				// J-112-0148 疫後振興
				l160m01cs = this.copyL901m01aToL160m01c_OnlyCaseF(
						l160m01a.getMainId(), LocaleContextHolder.getLocale().toString());
				l160m01a.setIsSmallBuss("N");
				l160m01a.setSmallBussChkListDate(null);
			} else if (lmsService.isRescueOnlyCaseJ03HeadItem1(l160m01a)) {
				l160m01cs = this.copyL901m01aToL160m01c_OnlyCaseJ03HeadItem1(
						l160m01a.getMainId(), LocaleContextHolder.getLocale().toString());
				l160m01a.setIsSmallBuss("N");
				l160m01a.setSmallBussChkListDate(null);
			} else {
				l160m01cs = this.copyL901m01aToL160m01c(l160m01a.getMainId(),
						LocaleContextHolder.getLocale().toString());
				l160m01a.setIsSmallBuss("N");
				l160m01a.setSmallBussChkListDate(null);

				this.lms1601Service.checkIsRemoveAllocateFundsCheckListItem(
						newL160m01bs, l160m01cs);

			}
			if (l160m01cs != null) {
				lms1601Service.saveL160m01cList(l160m01cs);
			}

		}

		l160m01a.setUseFromDate(useFromDate);
		l160m01a.setUseEndDate(useEndDate);
		l160m01a.setUnitLoanCase(haveUnitLoanCase ? UtilConstants.DEFAULT.是
				: UtilConstants.DEFAULT.否);
		l160m01a.setDataSrc(dataSrc);
		l160m01a.setSrcMainId(caseMainId);
		l160m01a.setNewVersion("01");
		if (l160m01a.getDeletedTime() != null) {
			l160m01a.setDeletedTime(null);
		}

		// J-109-0KKK_05097_B1001 簡化青年創業及啟動金貸款簽報書簽案流程
		l160m01a.setMiniFlag(Util.trim(l120m01a.getMiniFlag()));
		l160m01a.setCaseType(Util.trim(l120m01a.getCaseType()));

		lms1601Service.saveMain(newL160m01bs, newL162m01as, l160m01a);

		// J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
		lms1601Service.saveL164s01aList(newL164s01as);
		amlRelateService.saveL120s01pList(newL120s01ps);

		// J-111-0207 檢查信保案件負責人客戶基本資料是否已建配偶檔
		if (l160m01a != null) {
			checkSmeMateInfo(l160m01a.getMainId());
		}

		CapAjaxFormResult result = DataParse.toResult(l160m01a);
		result = formatResultShow(result, l160m01a, 1);

		return result;

	}

	/**
	 * 引進額度明細表 連保人 資料 L140M01I
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult includeL140m01I(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);
		String mainId = params.getString(EloanConstants.MAIN_ID);
		L160M01A l160m01a = lms1601Service.findModelByOid(L160M01A.class, oid);

		List<L162S01A> l162m01as = (List<L162S01A>) lms1601Service
				.findListByMainId(L162S01A.class, l160m01a.getMainId());

		// 當該mainId已經存在的主從債務人資料，要先刪除再引進新的。
		if (!l162m01as.isEmpty()) {
			lms1601Service.deleteL162m01as(l162m01as);
		}

		// J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
		List<L164S01A> l164s01as = (List<L164S01A>) lms1601Service
				.findListByMainId(L164S01A.class, l160m01a.getMainId());
		if (l164s01as != null && !l164s01as.isEmpty()) {
			lms1601Service.deleteL164s01as(l164s01as);
		}

		// J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
		List<L120S01P> l120s01ps = (List<L120S01P>) amlRelateService
				.findListByMainId(L120S01P.class, l160m01a.getMainId());
		if (l120s01ps != null && !l120s01ps.isEmpty()) {
			amlRelateService.deleteListL120s01p(l120s01ps);
		}

		Set<L160M01B> l160m01bs = l160m01a.getL160m01b();
		List<String> selectMainId = new ArrayList<String>();
		HashMap<String, String> mainIdmapingCntrno = new HashMap<String, String>();
		for (L160M01B l160m01b : l160m01bs) {
			selectMainId.add(l160m01b.getReMainId());
			mainIdmapingCntrno
					.put(l160m01b.getReMainId(), l160m01b.getCntrNo());
		}

		L120M01A l120m01a = lms1201Service.findL120m01aByMainId(l160m01a
				.getSrcMainId());
		HashMap<String, String> custCountry = lms1601Service
				.getCustCounty(l120m01a);
		List<L140M01A> l140m01as = lms1401Service
				.findL140m01aListByMainIdList(selectMainId
						.toArray(new String[selectMainId.size()]));

		List<L162S01A> newL162m01as = new ArrayList<L162S01A>();

		// J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
		List<L164S01A> newL164s01as = new ArrayList<L164S01A>();
		List<L120S01P> newL120s01ps = new ArrayList<L120S01P>();
		Map<String, String> processedCustId = new HashMap<String, String>();
		if (UtilConstants.Casedoc.DocType.企金.equals(l120m01a.getDocType())) {
			List<L120S01B> l120s01bs = (List<L120S01B>) amlRelateService
					.findListByMainId(L120S01B.class, l120m01a.getMainId());
			for (L120S01B l120s01b : l120s01bs) {
				if (l120s01b != null) {
					// 複製借款人基本資料 L120S01B L120S01A 到 L164S01A
					// J-109-0209_05097_B1001 e-Loan國內企金動審表增加借戶性質註記等進扣帳對象檢核項目
					newL164s01as.add(copyL120s01bToL164s01a(l120s01b, mainId,
							custCountry));

					// 複製借款人實質受益人資料 L120S01P(簽報書MAINID)->L120S01P(動審表MAINID)
					newL120s01ps.addAll(copyL120s01pToNewL120s01p(l120s01b,
							mainId));
				}
			}
		} else {
			// 個金
		}

		for (L140M01A l140m01a : l140m01as) {
			// 複製連保人資料到主從債務人
			String cntrNo = mainIdmapingCntrno.get(l140m01a.getMainId());
			newL162m01as.addAll(copyL140m01IToL162m01a(l140m01a, mainId,
					custCountry, cntrNo));
		}

		lms1601Service.saveL162m01aList(newL162m01as);

		// J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
		lms1601Service.saveL164s01aList(newL164s01as);
		amlRelateService.saveL120s01pList(newL120s01ps);
		return result;

	}

	/**
	 * 儲存L161M01B 聯貸案參貸比率一覽表明細檔
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL161m01b(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String slCurr = Util.trim(params.getString("slCurr"));
		String cntrNo = Util.trim(params.getString("cntrNo"));
		String uid = Util.trim(params.getString("uid"));

		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1601M01Page.class);

		// Long totalAmt = params.getLong("totalAmt");
		String formL161m01a = params.getString("L161M01AForm");
		String formL161m01b = params.getString("L161M01BForm");
		JSONObject jsonL161m01a = JSONObject.fromObject(formL161m01a);
		JSONObject jsonL161m01b = JSONObject.fromObject(formL161m01b);

		L160M01A l160m01a = lms1601Service.findL160M01AByMaindId(mainId);
		L161S01B l161s01b = lms1601Service.findModelByOid(L161S01B.class, oid);
		// 先儲存L161M01A 聯貸案參貸比率一覽表主檔
		L161S01A l161s01a = lms1601Service.findL161m01aByMainIdUid(mainId, uid);

		if (l161s01a == null) {
			throw new CapMessageException(
					prop.getProperty("L160M01A.message79"), getClass());
		}

		if (Util.isEmpty(l161s01b)) {
			l161s01b = new L161S01B();
			DataParse.toBean(jsonL161m01b, l161s01b);
			l161s01b.setSeq(lms1601Service.findL161m01bMaxSeq(
					l160m01a.getMainId(), l161s01a.getUid()));
			l161s01b.setCreator(user.getUserId());
			l161s01b.setCreateTime(CapDate.getCurrentTimestamp());
			l161s01b.setMainId(mainId);
		} else {
			DataParse.toBean(jsonL161m01b, l161s01b);
		}

		l161s01b.setSlCurr(slCurr);
		l161s01b.setPid(l161s01a.getUid());
		
		DataParse.toBean(jsonL161m01a, l161s01a);

		String validate = Util.validateColumnSize(l161s01b, prop, "L160M01A");
		if (validate != null) {
			Map<String, String> param = new HashMap<String, String>();
			param.put("colName", validate);

			throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
		}
		lms1601Service.save(l161s01a, l161s01b);
		return result;

	}

	/**
	 * 儲存L162M01A 主從債務人資料表檔
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL162m01a(PageParameters params) throws CapException {
		// J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
		Properties lmss20aPop = MessageBundleScriptCreator
				.getComponentResource(LMSS20APanel.class);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, Object> chkInsteadMap = amlRelateService
				.checkInstead(mainId);
		boolean instead = MapUtils.getBooleanValue(chkInsteadMap, "instead",
				false);
		String queryBrId = MapUtils.getString(chkInsteadMap, "queryBrId");
		String rKindD = Util.trim(params.getString("rKindD"));
		String formL162m01a = params.getString("L162M01AForm"); // 指定的form
		JSONObject jsonL162m01a = JSONObject.fromObject(formL162m01a);
		L162S01A l162s01a = lms1601Service.findModelByOid(L162S01A.class, oid);
		List<L162S01A> l162m01as = (List<L162S01A>) lms1601Service
				.findListByMainId(L162S01A.class, mainId);

		boolean needChkSeniorMgr = amlRelateService
				.needChkSeniorMgrBeforeSendBoss(queryBrId);

		boolean needChkCtrlPeo = amlRelateService
				.needChkCtrlPeoBeforeSendBoss(queryBrId);

		if (Util.isEmpty(l162s01a)) {
			l162s01a = new L162S01A();
			DataParse.toBean(jsonL162m01a, l162s01a);
			l162s01a.setCreator(user.getUserId());
			l162s01a.setCreateTime(CapDate.getCurrentTimestamp());
			l162s01a.setMainId(mainId);
			// 2 是自行新增的選項
			l162s01a.setDataSrc("2");
		} else {
			DataParse.toBean(jsonL162m01a, l162s01a);
		}

		l162s01a.setRKindD(rKindD);
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1601M01Page.class);
		// 驗證欄位長度
		String validate = Util.validateColumnSize(l162s01a, prop, "L162M01A");
		if (validate != null) {
			Map<String, String> param = new HashMap<String, String>();
			param.put("colName", validate);
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
		}

		// J-103-0299 Web e-Loan企金額度明細表保證人新增保證比例
		if (Util.notEquals(l162s01a.getRType(), "C")
				&& Util.notEquals(l162s01a.getRType(), "S")) {
			if (Util.isEmpty(l162s01a.getGuaPercent())) {
				throw new CapMessageException(MessageFormat.format(
						prop.getProperty("L162M01A.error01"), l162s01a.getRId()
								+ " " + Util.trim(l162s01a.getRName())),
						getClass());
			}

			// J-110-0040_05097_B1001 Web
			// e-Loan增加「本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)」註記

			String guarantorPriorityOn = Util.trim(lmsService
					.getSysParamDataValue("LMS_GUARANTOR_PRIORITY_ON"));
			if (Util.equals(guarantorPriorityOn, "Y")) {
				if (Util.isEmpty(l162s01a.getGuaNaExposure())) {
					// L162M01A.error03=「{0}」欄位「{0}」不得空白
					// L162M01A.guaNaExposure=本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)
					throw new CapMessageException(MessageFormat.format(
							prop.getProperty("L162M01A.error03"),
							l162s01a.getRId() + " "
									+ Util.trim(l162s01a.getRName()),
							prop.getProperty("L162M01A.guaNaExposure")),
							getClass());
				}
			}

		} else {
			l162s01a.setGuaPercent(null);
			// J-110-0040_05097_B1001 Web
			// e-Loan增加「本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)」註記
			l162s01a.setGuaNaExposure(null);
		}

		// 檢查是否已經有此債務人
		for (L162S01A l162m01aold : l162m01as) {
			// 不檢查自己
			if (l162m01aold.getOid().equals(oid)) {
				continue;
			}
			if (Util.trim(l162m01aold.getCustId()).toUpperCase()
					.equals(Util.trim(l162s01a.getCustId()).toUpperCase())
					&& Util.trim(l162m01aold.getDupNo())
							.toUpperCase()
							.equals(Util.trim(l162s01a.getDupNo())
									.toUpperCase())) {
				if (Util.trim(l162m01aold.getRId()).toUpperCase()
						.equals(Util.trim(l162s01a.getRId()).toUpperCase())
						&& Util.trim(l162m01aold.getRDupNo())
								.toUpperCase()
								.equals(Util.trim(l162s01a.getRDupNo())
										.toUpperCase())
						&& Util.trim(l162m01aold.getRType())
								.toUpperCase()
								.equals(Util.trim(l162s01a.getRType())
										.toUpperCase())) {
					if (Util.trim(l162m01aold.getCntrNo())
							.toUpperCase()
							.equals(Util.trim(l162s01a.getCntrNo())
									.toUpperCase())) {
						Map<String, String> param = new HashMap<String, String>();
						param.put("custId",
								Util.trim(l162s01a.getRId()).toUpperCase()
										+ " "
										+ Util.trim(l162s01a.getRDupNo())
												.toUpperCase());
						// EFD3010=ERROR|$\{custId\}此債務人已經存在 ！！|
						throw new CapMessageException(RespMsgHelper.getMessage("EFD3010", param), getClass());
					}

				}
			}
		}
		// 當主借人ID與從債務人ID相同，其相關身分應該為C 共同借款人
		if (Util.trim(l162s01a.getCustId()).toUpperCase()
				.equals(Util.trim(l162s01a.getRId()).toUpperCase())
				&& Util.trim(l162s01a.getDupNo()).toUpperCase()
						.equals(Util.trim(l162s01a.getRDupNo()).toUpperCase())) {
			if (!UtilConstants.lngeFlag.共同借款人.equals(l162s01a.getRType())) {
				Map<String, String> param = new HashMap<String, String>();
				Properties pop = MessageBundleScriptCreator
						.getComponentResource(LMS1601M01Page.class);
				param.put("msg", pop.getProperty("L160M01A.message48"));
				// L160M01A.message48=主債務人統編與從債務人統編相同者，其相關身份應為 C、共同借款人
				throw new CapMessageException(RespMsgHelper.getMessage(
						UtilConstants.AJAX_RSP_MSG.執行有誤, param), getClass());
			}
		}
		//G-113-0036 主從債務人檢核時 保證金額上限(Guarante Amount) 需<= L140M01A 現請額度
		if (Util.isNotEmpty(l162s01a)) {
			if (UtilConstants.BrNoType.國外.equals(branchService.getBranch(l162s01a.getCntrNo().substring(0, 3))
					.getBrNoFlag())) {
				String controlflag = Util.trim(lmsService.getSysParamDataValue("RPS_GTE1000"));
				if(Util.equals(UtilConstants.DEFAULT.是, controlflag)){
					BigDecimal checkamt = new BigDecimal(0);
					L161S01A l161s01a = lms1601Service.findL161m01aByMainIdCntrno(mainId, l162s01a.getCntrNo());
					if (l161s01a != null) {
						L140M01A l140m01a = lms1401Service.findL140m01aByMainId(l161s01a.getCntrMainId());
						if (l140m01a != null && Util.isNotEmpty(l140m01a.getCurrentApplyAmt())) {
							checkamt = l140m01a.getCurrentApplyAmt();
							BigDecimal grtamt = Util.isEmpty(l162s01a.getGrtAmt()) ? new BigDecimal(0) : l162s01a.getGrtAmt();
							if(grtamt.compareTo(checkamt) > 0){
								throw new CapMessageException(MessageFormat.format(
										prop.getProperty("L162M01A.error05"), l162s01a.getRId()
												+ " " + Util.trim(l162s01a.getRName())),
										getClass());
							}
						}
						
					}
				}
			}
		}

		// J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
		if (Util.equals(Util.trim(l162s01a.getRType()), "C")) {
			// 025分行動審表主從債務人資料表儲存ERROR
			L164S01A l164s01a = lms1601Service.findL164s01aByMainIdCustId(
					mainId, Util.trim(l162s01a.getRId()),
					Util.trim(l162s01a.getRDupNo()));
			if (l164s01a == null) {
				l164s01a = new L164S01A();
				l164s01a.setCreator(user.getUserId());
				l164s01a.setCreateTime(CapDate.getCurrentTimestamp());
				l164s01a.setMainId(mainId);
			}

			DataParse.toBean(jsonL162m01a, l164s01a);
			l164s01a.setCustId(l162s01a.getRId());
			l164s01a.setDupNo(l162s01a.getRDupNo());
			l164s01a.setCustPos(l162s01a.getRType());

			// 取得0024判斷行業對象別
			Map<String, Object> m0024 = iCustomerService.findByIdDupNo(
					l162s01a.getRId(), l162s01a.getRDupNo());
			String busCode = Util.trim(MapUtils.getString(m0024, "BUSCD"));

			// 企業戶才要負責人跟實質受益人
			if (Util.equals(busCode, "")) {
				// AML.error010=主從債務人「{0}」於0024無行業對象別資料。
				throw new CapMessageException(MessageFormat.format(
						lmss20aPop.getProperty("AML.error010"),
						l162s01a.getRId() + l162s01a.getRDupNo()
								+ l162s01a.getRName()), getClass());
			} else {

				if (!LMSUtil.isBusCode_060000_130300(busCode)) {

					String emptyFieldName = "";

					if (Util.equals(l164s01a.getBeneficiary(), "")) {
						// L120S09a.checkbox7=實質受益人
						emptyFieldName = emptyFieldName
								+ (Util.equals(emptyFieldName, "") ? "" : "、")
								+ lmss20aPop.getProperty("L120S09a.checkbox7");
					}

					// J-107-0070-001 Web e-Loan
					// 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
					if (needChkSeniorMgr) {
						if (Util.equals(l164s01a.getSeniorMgr(), "")) {
							// L120S09a.checkbox10=高階管理人員
							emptyFieldName = emptyFieldName
									+ (Util.equals(emptyFieldName, "") ? ""
											: "、")
									+ lmss20aPop
											.getProperty("L120S09a.checkbox10");
						}

					}

					// J-108-0039_05097_B1001 Web e-Loan
					// 國內企金授信系統簽報、動審AML頁籤將借戶之「具控制權人」納入應查詢比對黑名單之對象。
					if (needChkCtrlPeo) {
						if (Util.equals(l164s01a.getCtrlPeo(), "")) {
							// L120S09a.checkbox11=具控制權人
							emptyFieldName = emptyFieldName
									+ (Util.equals(emptyFieldName, "") ? ""
											: "、")
									+ lmss20aPop
											.getProperty("L120S09a.checkbox11");
						}

					}

					if (Util.equals(l164s01a.getChairman(), "")) {
						// L120S09a.checkbox3=借戶負責人
						emptyFieldName = emptyFieldName
								+ (Util.equals(emptyFieldName, "") ? "" : "、")
								+ lmss20aPop.getProperty("L120S09a.checkbox3");
					}

					if (Util.notEquals(emptyFieldName, "")) {
						// L120S09a.message07=「{0}」欄位不得為空白
						throw new CapMessageException(MessageFormat.format(
								lmss20aPop.getProperty("L120S09a.message07"),
								emptyFieldName), getClass());
					}
				}
			}

			// J-109-0209_05097_B1001 e-Loan國內企金動審表增加借戶性質註記等進扣帳對象檢核項目

			if (Util.equals(Util.trim(l162s01a.getRType()), "C")
					&& (l162s01a.getCustId().toUpperCase()
							.equals(l162s01a.getRId().toUpperCase()) && l162s01a
							.getDupNo().toUpperCase()
							.equals(l162s01a.getRDupNo().toUpperCase()))) {
				// 金爺說小規模的才檢核

				boolean needChk = false;
				L161S01A l161s01a = lms1601Service.findL161m01aByMainIdCntrno(
						mainId, l162s01a.getCntrNo());
				if (l161s01a != null) {
					L140M01A l140m01a = lms1401Service
							.findL140m01aByMainId(l161s01a.getCntrMainId());
					if (l140m01a != null) {
						if (Util.equals(Util.trim(l161s01a.getIsRescue()), "Y")) {
							String isCbRefin = Util.trim(l161s01a
									.getIsCbRefin());
							if (Util.equals(isCbRefin, "Y")) {
								// 額度明細表的央行轉融通辦理類別
								String isSmallBuss = Util.trim(l140m01a
										.getIsSmallBuss());
								if (Util.equals(isSmallBuss, "C")) {
									needChk = true;
								}
							}
						}
					}
				}

				if (needChk) {

					// 借款人本身，要檢核有無填列是否為我國獨資或合夥企業s
					String isSole = "";
					if (l164s01a != null) {
						isSole = Util.trim(l164s01a.getIsSole());

						if (Util.equals(isSole, "")) {
							Properties pop = MessageBundleScriptCreator
									.getComponentResource(LMS1601M01Page.class);

							// L120S09a.message07=「{0}」欄位不得為空白
							// L164S01A.isSole=是否為我國獨資或合夥企業
							throw new CapMessageException(MessageFormat.format(
									lmss20aPop
											.getProperty("L120S09a.message07"),
									pop.getProperty("L164S01A.isSole")),
									getClass());
						}
					}

					if (Util.equals(isSole, "Y")) {
						// 既然是否為我國獨資或合夥企業有填Y，就要檢核把其他欄位填完
						String soleType = Util.trim(l164s01a.getSoleType());
						String hasRegis = Util.trim(l164s01a.getHasRegis());
						if (Util.equals(soleType, "")) {
							Properties pop = MessageBundleScriptCreator
									.getComponentResource(LMS1601M01Page.class);

							// L120S09a.message07=「{0}」欄位不得為空白
							// L164S01A.soleType=企業類別
							throw new CapMessageException(MessageFormat.format(
									lmss20aPop
											.getProperty("L120S09a.message07"),
									pop.getProperty("L164S01A.soleType")),
									getClass());
						}

						if (Util.equals(hasRegis, "")) {
							Properties pop = MessageBundleScriptCreator
									.getComponentResource(LMS1601M01Page.class);

							// L120S09a.message07=「{0}」欄位不得為空白
							// L164S01A.hasRegis=是否取得商業登記
							throw new CapMessageException(MessageFormat.format(
									lmss20aPop
											.getProperty("L120S09a.message07"),
									pop.getProperty("L164S01A.hasRegis")),
									getClass());
						}

					}

				}

			}

			lms1601Service.save(l164s01a);
		}
		// J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
		lms1601Service.save(l162s01a);

		result.set("formOid", l162s01a.getOid());

		return result;

	}

	/**
	 * 儲存已覆核 登錄辦妥事項
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult saveLogeIn(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		L160M01A l160m01a = lms1601Service.findModelByOid(L160M01A.class, oid);
		String formL160m01a = params.getString("L160M01AForm"); // 指定的form
		JSONObject jsonL160m01a = JSONObject.fromObject(formL160m01a);
		L163S01A l163s01a = l160m01a.getL163S01A();
		DataParse.toBean(jsonL160m01a, l163s01a);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		l163s01a.setAppraiserId(user.getUserId());
		try {
			lms1601Service.flowActionGo(oid, l163s01a);
		} catch (Throwable e) {
			logger.error(
					"[saveLogeIn] lms1601Service.flowActionGo EXCEPTION!!", e);
			throw new CapMessageException(e.getMessage(), getClass());
		}

		return result;

	}

	/**
	 * 刪除L160M01A 動用審核表主檔
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteL160m01a(PageParameters params) throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] oids = params.getStringArray("oids");
		if (oids.length > 0) {

			// 檢查動審表有沒有被已核准退回過
			List<L160M01A> l160m01as = lms1601Service.findL160M01AByOids(oids);

			for (L160M01A l160m01a : l160m01as) {
				List<Map<String, Object>> list = eloandbBASEService
						.findDeletedMetaByMainId(l160m01a.getMainId());
				if (list != null && !list.isEmpty()) {

					StringBuffer msg = new StringBuffer("");
					msg.append("動審表 " + l160m01a.getCustId() + " "
							+ l160m01a.getCustName() + " 有已核准退回紀錄，本筆不得刪除!!<BR>");
					for (Map<String, Object> dataMap : list) {
						msg.append("前次覆核資訊：<BR>");
						msg.append("統編:")
								.append(Util.trim(dataMap.get("CUSTID")))
								.append("<BR>");
						msg.append("戶名:")
								.append(Util.trim(dataMap.get("CUSTNAME")))
								.append("<BR>");
						msg.append("建立人員:")
								.append(Util.trim(dataMap.get("CREATOR")))
								.append("<BR>");
						msg.append("覆核人員:")
								.append(Util.trim(dataMap.get("APPROVER")))
								.append("<BR>");
						msg.append("覆核日期:")
								.append(Util.trim(dataMap.get("APPROVETIME")))
								.append("<BR>");
						msg.append("退回日期:")
								.append(Util.trim(dataMap.get("DELETEDTIME")))
								.append("<BR>");
						break;
					}
					throw new CapMessageException(msg.toString(), getClass());
				}
			}

			if (lms1601Service.deleteL160m01as(oids)) {
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
						RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.刪除成功));
			}
		}

		return result;

	}

	/**
	 * 刪除上傳檔案
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteUploadFile(PageParameters params) throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] oids = params.getStringArray("oids");
		if (oids.length > 0) {
			lms1601Service.deleteUploadFile(oids);
		}

		return result;

	}

	/**
	 * 刪除 L161M01B 聯貸案參貸比率一覽表明細檔
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteL161M01B(PageParameters params) throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] oids = params.getStringArray("oids");
		if (oids.length > 0) {
			lms1601Service.deleteListByOid(L161S01B.class, oids);
		}

		return result;

	}

	/**
	 * 刪除 L162M01A 主從債務人資料表檔
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteL162M01A(PageParameters params) throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] oids = params.getStringArray("oids");
		if (oids.length > 0) {
			lms1601Service.deleteListByOid(L162S01A.class, oids);
		}

		return result;

	}

	/**
	 * 檢核是否符 (特殊資料修改流程用) 103.01.17 取消特殊資料修改流程，改以動審表額度動用資訊修改
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryDateFixBase(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String l140m01aOid = params.getString("l140m01aOid");
		String l160m01aOid = params.getString("l160m01aOid");

		L140M01A l140m01a = lms1401Service.findModelByOid(L140M01A.class,
				l140m01aOid);
		L160M01A l160m01a = lms1601Service.findModelByOid(L160M01A.class,
				l160m01aOid);
		L120M01A l120m01a = lms1201Service.findL120m01aByMainId(l160m01a
				.getSrcMainId());
		if (l140m01a == null) {
			throw new CapMessageException(RespMsgHelper.getMainMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤), getClass());
		}
		// if ("2".equals(l160m01a.getDataSrc())) {
		// Properties prop = MessageBundleScriptCreator
		// .getComponentResource(LMS1601M01Page.class);
		// // EFD0015=WARN|【注意】$\{noticeMsg\}|
		// // L160M01A.message52=額度序號來源為聯行額度明細表，不能執行資料修正
		// HashMap<String, String> msgMap = new HashMap<String, String>();
		// msgMap.put("noticeMsg", prop.getProperty("L160M01A.message52"));
		// throw new CapMessageException(RespMsgHelper.getMessage(parent,
		// "EFD0015", msgMap), getClass());
		// }

		L161S01A l161s01a = lms1601Service.findL161m01aByMainIdCntrno(
				l160m01a.getMainId(), l140m01a.getCntrNo());

		Set<L161S01B> l161s01b = l161s01a.getL161s01b();

		// 表示無同業聯貸和同行聯貸不得進行資料修正
		if (l140m01a.getL140m01e().isEmpty() && l161s01b.isEmpty()) {
			Properties prop = MessageBundleScriptCreator
					.getComponentResource(LMS1601M01Page.class);
			// EFD0015=WARN|【注意】$\{noticeMsg\}|
			// L160M01A.message42= 此案件並非聯貸案性質，無法修改參貸比率資料！
			HashMap<String, String> msgMap = new HashMap<String, String>();
			msgMap.put("noticeMsg", prop.getProperty("L160M01A.message42"));
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0015", msgMap), getClass());
		}

		L210M01A l210m01a = new L210M01A();

		// 複製的語法
		CapBeanUtil
				.copyBean(l160m01a, l210m01a, new String[] { "custId", "dupNo",
						"custName", "typCd", "caseNo", "caseDate", "ownBrId" });
		l210m01a.setMainId(IDGenerator.getUUID());
		l210m01a.setRefMainId(l160m01a.getMainId());
		l210m01a.setSrcMainId(l140m01a.getMainId());

		// 如果沒有聯行參貸比率就抓額度明細表上
		if (l161s01b.isEmpty()) {
			l210m01a.setLtAmt(l140m01a.getCurrentApplyAmt());
			l210m01a.setLtCurr(l140m01a.getCurrentApplyCurr());
			l210m01a.setCntrNo(l140m01a.getCntrNo());
		} else {
			l210m01a.setLtAmt(l161s01a.getQuotaAmt());
			l210m01a.setLtCurr(l161s01a.getQuotaCurr());
			l210m01a.setCntrNo(l161s01a.getCntrNo());
		}
		l210m01a.setCreateTime(CapDate.getCurrentTimestamp());
		l210m01a.setCreator(user.getUserId());
		l210m01a.setUpdateTime(CapDate.getCurrentTimestamp());
		l210m01a.setUpdater(user.getUserId());
		l210m01a.setDocURL(CapWebUtil.getDocUrl(LMS2105M01Page.class));
		l210m01a.setModifyDate(CapDate.getCurrentTimestamp());

		// 授權檔
		L210A01A l210a01a = new L210A01A();
		l210a01a.setAuthTime(CapDate.getCurrentTimestamp());
		l210a01a.setAuthType(DocAuthTypeEnum.MODIFY.getCode());
		l210a01a.setAuthUnit(user.getUnitNo());
		l210a01a.setMainId(l210m01a.getMainId());
		l210a01a.setOwner(user.getUserId());
		l210a01a.setOwnUnit(user.getUnitNo());
		Set<L210A01A> l210a01as = new HashSet<L210A01A>();
		l210a01as.add(l210a01a);
		l210m01a.setL210a01a(l210a01as);

		// 1同行聯貸2同業聯貸3 同業聯貸&同行聯貸
		if (!l140m01a.getL140m01e().isEmpty() && !l161s01b.isEmpty()) {
			l210m01a.setCaseType(UtilConstants.editDoc.caseType.同業聯貸和同行聯貸);
		} else if (!l140m01a.getL140m01e().isEmpty()) {
			l210m01a.setCaseType(UtilConstants.editDoc.caseType.同行聯貸);
		} else if (!l161s01b.isEmpty()) {
			l210m01a.setCaseType(UtilConstants.editDoc.caseType.同業聯貸);
		}
		// 要複製的欄位名稱
		String[] copyColumnForS01A = new String[] { "flag", "shareBrId",
				"shareRate1", "shareRate2", "shareAmt", "totalAmt", "shareNo",
				"shareFlag" };
		// 複製同行聯貸
		ArrayList<L210S01A> l210s01as = new ArrayList<L210S01A>();
		if (!l140m01a.getL140m01e().isEmpty()) {
			for (L140M01E l140m01e : l140m01a.getL140m01e()) {
				L210S01A l210s01a = new L210S01A();
				L210S01A l210s01a2 = new L210S01A();
				CapBeanUtil.copyBean(l140m01e, l210s01a, copyColumnForS01A);
				l210s01a.setMainId(l210m01a.getMainId());
				l210s01a.setCreateTime(CapDate.getCurrentTimestamp());
				l210s01a.setCreator(user.getUserId());
				l210s01a.setUpdater(user.getUserId());
				l210s01a.setUpdateTime(CapDate.getCurrentTimestamp());
				l210s01a.setChgFlag(editDoc.chanFlag.變動前);
				l210s01as.add(l210s01a);
				CapBeanUtil.copyBean(l140m01e, l210s01a2, copyColumnForS01A);
				l210s01a2.setMainId(l210m01a.getMainId());
				l210s01a2.setCreateTime(CapDate.getCurrentTimestamp());
				l210s01a2.setCreator(user.getUserId());
				l210s01a2.setUpdater(user.getUserId());
				l210s01a2.setUpdateTime(CapDate.getCurrentTimestamp());
				l210s01a2.setChgFlag(editDoc.chanFlag.變動後);
				l210s01as.add(l210s01a2);
			}

		}

		// 要複製的欄位名稱
		String[] copyColumnForS01B = new String[] { "slBankType", "slBank",
				"slBankCN", "slBranch", "slBranchCN", "slMaster", "slAccNo",
				"slCurr", "slAmt", "seq" };
		// 複製同業聯貸
		ArrayList<L210S01B> l210s01bs = new ArrayList<L210S01B>();
		if (!l161s01b.isEmpty()) {

			for (L161S01B l161m01b : l161s01b) {
				L210S01B l210s01b = new L210S01B();
				L210S01B l210s01b2 = new L210S01B();
				CapBeanUtil.copyBean(l161m01b, l210s01b, copyColumnForS01B);
				l210s01b.setMainId(l210m01a.getMainId());
				l210s01b.setCreateTime(CapDate.getCurrentTimestamp());
				l210s01b.setUpdateTime(CapDate.getCurrentTimestamp());
				l210s01b.setCreator(user.getUserId());
				l210s01b.setUpdater(user.getUserId());
				l210s01b.setChgFlag(editDoc.chanFlag.變動前);
				l210s01bs.add(l210s01b);
				CapBeanUtil.copyBean(l161m01b, l210s01b2, copyColumnForS01B);
				l210s01b2.setMainId(l210m01a.getMainId());
				l210s01b2.setCreateTime(CapDate.getCurrentTimestamp());
				l210s01b2.setUpdateTime(CapDate.getCurrentTimestamp());
				l210s01b2.setCreator(user.getUserId());
				l210s01b2.setUpdater(user.getUserId());
				l210s01b2.setChgFlag(editDoc.chanFlag.變動後);
				l210s01bs.add(l210s01b2);
			}
		}
		// 當修改分行與目前分行相同 才上這個註記
		if (user.getUnitNo().equals(l120m01a.getCaseBrId())) {
			l120m01a.setReEstFlag("Y");
		}

		lms2105Service
				.saveNewL210m01a(l210s01as, l210s01bs, l210m01a, l120m01a);
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		return result;

	}

	/**
	 * 將引進的案件簽報書資料複製到動審表
	 * 
	 * @param l160m01a
	 *            動審表主檔
	 * @param l120m01a
	 *            案件簽報書主檔
	 * @return 複製完成的動審表主檔
	 * @throws CapException
	 */
	private L160M01A copyL120m01aToL160m01a(L160M01A l160m01a, L120M01A l120m01a) throws CapException {

		l160m01a.setCaseBrId(Util.trim(l120m01a.getCaseBrId()));
		l160m01a.setCaseLvl(Util.trim(l120m01a.getCaseLvl()));
		l160m01a.setCaseDate(l120m01a.getCaseDate());
		l160m01a.setCaseNo(Util.trim(l120m01a.getCaseNo()));
		l160m01a.setCaseSeq((l120m01a.getCaseSeq()));
		l160m01a.setCaseYear(l120m01a.getCaseYear());
		l160m01a.setCustId(Util.trim(l120m01a.getCustId().toUpperCase()));
		l160m01a.setCustName(Util.trim(l120m01a.getCustName()));
		l160m01a.setDupNo(Util.trim(l120m01a.getDupNo()));
		l160m01a.setTypCd(Util.trim(l120m01a.getTypCd()));
		// L120M01B L120m01b = lms1401Service.findL120m01bByUniqueKey(l120m01a
		// .getMainId());
		// l160m01a.setUCMainBranch(Util.isEmpty(L120m01b) ? "N" : L120m01b
		// .getUCntBranch());
		// l160m01a.setUnitLoanCase(Util.isEmpty(L120m01b) ? "N" : L120m01b
		// .getUnitCase());
		// L161S01A l161s01a = lms1601Service.findL161m01aByMainId(l160m01a
		// .getMainId());
		// if (Util.isEmpty(l161s01a)) {
		// MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// l161s01a = new L161S01A();
		// l161s01a.setCreator(user.getUserId());
		// l161s01a.setCreateTime(CapDate.getCurrentTimestamp());
		// l161s01a.setMainId(l160m01a.getMainId());
		// }
		// l161s01a.setCaseBrId(Util.trim(l120m01a.getCaseBrId()));
		//
		// // 簽約日期是否等於簽案日期
		// l161s01a.setSignDate(l120m01a.getCaseDate());
		// l161s01a.setCaseNo(Util.trim(l120m01a.getCaseNo()));
		// l161s01a.setCaseSeq(l120m01a.getCaseSeq());
		// l161s01a.setCaseYear(l120m01a.getCaseYear());
		// l161s01a.setGist(l120m01a.getGist());
		// lms1601Service.save(l161s01a);
		return l160m01a;

	}

	/**
	 * 將引進的案件簽報書資料複製連保人資料到主從債務人
	 * 
	 * @param l140m01a
	 *            額度明細表主檔
	 * @param mainId
	 *            要加進去的mainId
	 * @param custContry
	 *            簽報書所有借款人的國別 <custId+dupNo, 國別>
	 * @return 複製完成的主從債務人資料表檔
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	private List<L162S01A> copyL140m01IToL162m01a(L140M01A l140m01a,
			String mainId, HashMap<String, String> custContry, String cntrNo)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<L140M01J> l140m01js = (List<L140M01J>) lms1401Service
				.findModelListByMainId(L140M01J.class, l140m01a.getMainId());
		List<L162S01A> newL162s01as = new ArrayList<L162S01A>();
		
		// 聯行額度明細表或攤貸行額度序號會與額度明細表CNTRNO不同，所以應該是用L140M01A 對應的 L160M01B 的額度序號
		// 例如047 動用 0C3的簽報書時，額度明細表0C3500810067，但047的額度應該是047410500007
		// 所以要在外面就決定好用哪個cntrNo
		// String cntrNo = Util.trim(l140m01a.getCntrNo());

		String key = "";
		Set<String> isCreatePerson = new HashSet<String>();
		String custId = l140m01a.getCustId().toUpperCase();
		String dupNo = l140m01a.getDupNo().toUpperCase();
		key = custId + dupNo + custId + dupNo;
		if (!isCreatePerson.contains(key)) {
			isCreatePerson.add(key);
			// 當沒有資料先把主借人資料塞入
			L162S01A l162s01a = new L162S01A();
			// 設定主債務人資料
			l162s01a.setMainId(mainId);
			l162s01a.setCntrNo(cntrNo);
			l162s01a.setCreator(user.getUserId());
			l162s01a.setCreateTime(CapDate.getCurrentTimestamp());
			l162s01a.setCustId(custId);
			l162s01a.setDupNo(dupNo);
			l162s01a.setRKindD("");
			l162s01a.setRKindM("");
			String custName = l140m01a.getCustName();
			l162s01a.setRName(custName);
			if (custContry.containsKey(custId + dupNo)) {
				l162s01a.setRCountry(custContry.get(custId + dupNo));
			}
			// 設定主債務人資料
			l162s01a.setRId(custId);
			l162s01a.setRDupNo(dupNo);
			l162s01a.setDataSrc("1");
			l162s01a.setRType(UtilConstants.lngeFlag.共同借款人);
			newL162s01as.add(l162s01a);
		}
		for (L140M01J l140m01j : l140m01js) {
			key = custId + dupNo + l140m01j.getCustId() + l140m01j.getDupNo();
			if (isCreatePerson.contains(key)) {
				continue;
			} else {
				isCreatePerson.add(key);
			}
			L162S01A l162s01a = new L162S01A();
			// 設定主債務人資料
			l162s01a.setMainId(mainId);
			l162s01a.setCntrNo(cntrNo);
			l162s01a.setCreator(user.getUserId());
			l162s01a.setCreateTime(CapDate.getCurrentTimestamp());
			l162s01a.setCustId(custId);
			l162s01a.setDupNo(dupNo);

			// 設定從債務人資料
			l162s01a.setRCountry(l140m01j.getNtCode());
			l162s01a.setRId(l140m01j.getCustId());
			l162s01a.setRDupNo(l140m01j.getDupNo());
			l162s01a.setRName(l140m01j.getCustName());
			l162s01a.setDataSrc("1");
			l162s01a.setRType(l140m01j.getCustPos());
			String custRlt = Util.trim(l140m01j.getCustRlt());
			String rKindM = "";
			if (Util.isNotEmpty(custRlt)) {
				int index = custRlt.indexOf("X");
				switch (index) {
				case 0:
					rKindM = "2";
					break;
				case 1:
					rKindM = "1";
					break;
				case -1:
					rKindM = "3";
					break;
				}
			}
			l162s01a.setRType(UtilConstants.lngeFlag.共同借款人);
			l162s01a.setRKindD(custRlt);
			l162s01a.setRKindM(rKindM);
			newL162s01as.add(l162s01a);
		}

		// J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
		// 當同時為連保人與擔保品提供人，則RTYPE 寫G就好， S 不需要再寫
		// 因為G 為連帶保證人，擔保品提供人兼連帶保證人
		List<L140M01I> l140m011TypeG = lms1401Service
				.findL140m01iListWithRType(l140m01a.getMainId(),
						UtilConstants.lngeFlag.連帶保證人);
		Set<String> guarantorGSet = new HashSet<String>();
		for (L140M01I l140m01i : l140m011TypeG) {
			key = custId + dupNo + l140m01i.getRId() + l140m01i.getRDupNo();
			guarantorGSet.add(key);
		}

		String guarantType = Util.trim(l140m01a.getGuarantorType());
		for (L140M01I l140m01i : l140m01a.getL140m01i()) {

			// J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
			if (Util.equals(l140m01i.getRType(), UtilConstants.lngeFlag.擔保品提供人)) {
				String chkSKey = custId + dupNo + l140m01i.getRId()
						+ l140m01i.getRDupNo();
				if (guarantorGSet.contains(chkSKey)) {
					// 因為G 為連帶保證人，擔保品提供人兼連帶保證人，所以S就不用再寫ㄧ筆了
					continue;
				}
			}

			// J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
			key = custId + dupNo + l140m01i.getRId() + l140m01i.getRDupNo()
					+ l140m01i.getRType();
			if (isCreatePerson.contains(key)) {
				continue;
			} else {
				isCreatePerson.add(key);
			}
			L162S01A l162m01a = new L162S01A();
			// 設定主債務人資料
			l162m01a.setMainId(mainId);
			l162m01a.setCntrNo(cntrNo);
			l162m01a.setCreator(user.getUserId());
			l162m01a.setCreateTime(CapDate.getCurrentTimestamp());
			l162m01a.setCustId(custId);
			l162m01a.setDupNo(dupNo);

			// 設定從債務人資料
			l162m01a.setRCountry(l140m01i.getRCountry());
			l162m01a.setRId(l140m01i.getRId());
			l162m01a.setRDupNo(l140m01i.getRDupNo());
			l162m01a.setRKindD(Util.trim(l140m01i.getRKindD()));
			l162m01a.setRKindM(Util.trim(l140m01i.getRKindM()));
			l162m01a.setRName(l140m01i.getRName());
			l162m01a.setDataSrc("1");

			// J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
			// 連帶保證人、擔保品提供人
			// if (Util.equals(l140m01i.getRType(),
			// UtilConstants.lngeFlag.連帶保證人)) {
			// l162m01a.setRType(UtilConstants.lngeFlag.連帶保證人);
			// } else {
			// l162m01a.setRType(Util.trim(l140m01i.getRType()));
			// }
			if (Util.equals(guarantType, "3")) {
				if (Util.equals(Util.trim(l140m01i.getGuarantorTypeItem()), "2")) { // 一般保證人
					l162m01a.setRType(UtilConstants.lngeFlag.ㄧ般保證人);
				} else { // 其餘的放連帶保證人
					l162m01a.setRType(UtilConstants.lngeFlag.連帶保證人);
				}
			} else {
				l162m01a.setRType(Util.trim(l140m01i.getRType()));
			}

			// J-103-0299 Web e-Loan企金額度明細表保證人新增保證比例
			if (Util.equals(l140m01i.getRType(), UtilConstants.lngeFlag.擔保品提供人)) {
				l162m01a.setGuaPercent(null);
				// J-110-0040_05097_B1001 Web
				// e-Loan增加「本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)」註記
				l162m01a.setGuaNaExposure(null);
			} else {
				if (Util.equals(l140m01a.getGuaPercentFg(), "Y")) {
					// 有按照比率
					// J-105-0100-001 Web
					// e-Loan授信管理系統企金案件額度明細表之自然人保證人保證比例欄位開放可自行輸入
					// if (Util.equals(l140m01i.getType(), "2")) {
					// 企業戶
					if (l140m01i.getGuaPercent() == null) {
						l162m01a.setGuaPercent(BigDecimal.valueOf(100));
					} else {
						l162m01a.setGuaPercent(l140m01i.getGuaPercent());
					}

					// } else {
					// l162m01a.setGuaPercent(BigDecimal.valueOf(100));
					// }
				} else {
					l162m01a.setGuaPercent(BigDecimal.valueOf(100));
				}

				// J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
				l162m01a.setPriority(l140m01i.getPriority());

				// J-110-0040_05097_B1001 Web
				// e-Loan增加「本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)」註記
				l162m01a.setGuaNaExposure(Util.trim(l140m01a.getGuaNaExposure()));

			}

			newL162s01as.add(l162m01a);
		}
		
		//G-113-0036 主從債務人
		//取得 MIS.ELLNGTEE(ELF401)，如額度明細引入之主從債務人 擔保限額Guarante Amount(GRTAMT)、當地客戶識別ID Local Id(LOCALID)未填，則帶MIS.ELLNGTEE為預設值
		lms1601Service.checkL162s01aLocalIdAndGrtAmt(custId, dupNo, cntrNo, newL162s01as);

		return newL162s01as;
	}

	/**
	 * 格式化顯示訊息
	 * 
	 * @param mainId
	 *            動審表主檔mainId
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	private CapAjaxFormResult formatResultShow(CapAjaxFormResult result,
			L160M01A l160m01a, Integer page) throws CapException {
		String mainId = l160m01a.getMainId();
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1601M01Page.class);
		String apprId = Util.trim(l160m01a.getApprId());
		switch (page) {
		case 1:
			result = DataParse.toResult(l160m01a);
			// 簽章欄
			if (!Util.isEmpty(l160m01a.getL160M01D())) {
				// 取得人員職稱 L1. 分行經辦 L3. 分行授信主管 L4. 分行覆核主管 L5. 經副襄理L6. 總行經辦
				// L7.總行主管
				StringBuilder bossId = new StringBuilder("");
				for (L160M01D l160m01d : l160m01a.getL160M01D()) {
					// 要加上人員代碼
					String type = Util.trim(l160m01d.getStaffJob());
					String userId = Util.trim(l160m01d.getStaffNo());
					String value = Util.trim(lmsService.getUserName(userId));
					String showName = userId + " " + value;
					if ("L1".equals(type)) {
						result.set("showApprId", showName);
					} else if ("L3".equals(type)) {
						bossId.append(bossId.length() > 0 ? "<br/>" : "");
						bossId.append(userId);
						bossId.append(" ");
						bossId.append(value);
					} else if ("L4".equals(type)) {
						result.set("reCheckId", showName);
					} else if ("L5".equals(type)) {
						result.set("managerId", showName);
					} else if ("L6".equals(type)) {
						result.set("mainApprId", showName);
					} else if ("L7".equals(type)) {
						result.set("mainReCheckId", showName);
					}
				}

				result.set("bossId", bossId.toString());
			}
			// result.set("showApprId",
			// lmsService.getUserName(l160m01a.getApprId()));
			result.set("ownBrName",
					" " + branchService.getBranchName(l160m01a.getOwnBrId()));

			StringBuilder cntrNo = new StringBuilder("");
			// 確認全部動用是否有選
			if (UtilConstants.DEFAULT.否.equals(l160m01a.getAllCanPay())) {
				List<L160M01B> l160m01bs = (List<L160M01B>) lms1601Service
						.findListByMainId(L160M01B.class, mainId);
				for (L160M01B l160m01b : l160m01bs) {
					cntrNo.append(cntrNo.length() > 0 ? "," : "");
					cntrNo.append(Util.trim(l160m01b.getCntrNo()));
				}

			} else if (UtilConstants.DEFAULT.是.equals(l160m01a.getAllCanPay())) {

				// 簽報書項下額度明細表全部動用
				cntrNo.append(pop.getProperty("L160M01A.message3"));
			}
			result.set("showApprId",
					apprId + " " + lmsService.getUserName(apprId));
			result.set("creator", lmsService.getUserName(l160m01a.getCreator()));
			result.set("updater", lmsService.getUserName(l160m01a.getUpdater()));
			result.set("docStatus",
					getMessage("docStatus." + l160m01a.getDocStatus()));
			result.set("cntrNo", cntrNo.toString());
			break;
		case 2:
			result = DataParse.toResult(l160m01a);
			List<L160M01C> allItem = (List<L160M01C>) lms1601Service
					.findListByMainId(L160M01C.class, mainId);
			this.getL160M01C(result, allItem);
			// J-111-0207 取得信保案件配偶檢核結果
			Map<String, String> checkResult = lms1601Service
					.findSmeMateInfoCheckResult(mainId);
			result.set("smeMateInfo", checkResult.get("smeMateInfo"));
			if (Util.isNotEmpty(checkResult.get("noMateBorrower"))) {
				result.set("noMateBorrower", checkResult.get("noMateBorrower"));
			}
			break;
		case 3:

			// 存放額度序號 map
			TreeMap<String, JSONArray> cntrNos = new TreeMap<String, JSONArray>();

			// 存放額度序號 map
			TreeMap<String, String> cntrNoMap = new TreeMap<String, String>();

			Set<L160M01B> l160m01bs = l160m01a.getL160m01b();
			ArrayList<String> mainIds = new ArrayList<String>();

			/**
			 * {mainId:[cntrno,...,..]
			 */
			// 先把額度序號跟 mainId關連起來
			for (L160M01B l160m01b : l160m01bs) {
				String tempMainId = l160m01b.getReMainId();
				mainIds.add(tempMainId);
				if (cntrNos.containsKey(tempMainId)) {
					JSONArray value = cntrNos.get(tempMainId);
					value.add(l160m01b.getCntrNo());
				} else {
					JSONArray value = new JSONArray();
					value.add(l160m01b.getCntrNo());
					cntrNos.put(tempMainId, value);
				}
				cntrNoMap.put(l160m01b.getCntrNo(), l160m01b.getCntrNo());
			}
			// 存放客戶統編 map
			TreeMap<String, JSONArray> cusIds = new TreeMap<String, JSONArray>();
			List<L140M01A> l140m01as = lms1401Service
					.findL140m01aListByMainIdList(mainIds
							.toArray(new String[] {}));
			for (L140M01A l140m01a : l140m01as) {
				String key = l140m01a.getCustId().toUpperCase()
						+ l140m01a.getDupNo().toUpperCase();
				// if (!cusIds.containsKey(key)) {
				// cusIds.put(key, cntrNos.get(l140m01a.getMainId()));
				// }
				// cusIds.put(key,
				// JSONArray.fromObject("[\"007110100105\",\"007110100106\"]"));
				if (!cusIds.containsKey(key)) {
					cusIds.put(key, cntrNos.get(l140m01a.getMainId()));
				} else {
					JSONArray jsArr = cusIds.get(key);
					jsArr.addAll(JSONArray.toCollection(cntrNos.get(l140m01a
							.getMainId())));
					cusIds.put(key, jsArr);

				}
			}

			CapAjaxFormResult custIdSelect = new CapAjaxFormResult(cusIds);
			CapAjaxFormResult cntSelect = new CapAjaxFormResult(cntrNoMap);
			result.set("custIdSelect", custIdSelect);
			result.set("cntSelect", cntSelect);

			// 針對舊案調閱顯示原聯貸案參貸比率一覽表用，新動審表因為顯示額度動用資訊GRID，所以不走這邊
			if (Util.notEquals(l160m01a.getDocStatus(),
					CreditDocStatusEnum.海外_編製中)
					&& Util.notEquals(l160m01a.getDocStatus(),
							CreditDocStatusEnum.海外_待覆核)) {

				if (Util.equals(Util.trim(l160m01a.getNewVersion()), "")
						|| Util.equals(Util.trim(l160m01a.getNewVersion()),
								"00")) {

					// 找到對應主債務人的額度序號
					// 舊動審表上線時會把MAINID的值塞到UID
					L161S01A l161m01a = lms1601Service.findL161m01aByMainIdUid(
							mainId, mainId);
					if (l161m01a != null) {
						result = DataParse.toResult(l161m01a);

						result.set("gistOld", Util.equals(l161m01a.getGist(),
								"") ? "TWD" : l161m01a.getGist());
						result.set(
								"quotaCurrOld",
								Util.equals(l161m01a.getQuotaCurr(), "") ? "TWD"
										: l161m01a.getQuotaCurr());
						result.set(
								"quotaAmtOld",
								Util.isEmpty(l161m01a.getQuotaAmt()) ? BigDecimal.ZERO
										: l161m01a.getQuotaAmt());
						result.set("signDateOld", Util.isEmpty(l161m01a
								.getSignDate()) ? null : l161m01a.getSignDate());
						// try{
						// result.set("signDateOld",
						// Util.isEmpty(l161m01a.getSignDate()) ? new
						// SimpleDateFormat("yyyy-MM-dd").parse("0001-01-01") :
						// l161m01a.getSignDate());
						// }catch(Exception e){
						// throw new CapMessageException(e.toString(),
						// getClass());
						// }

						result.set("cntrNoOld",
								Util.equals(l161m01a.getCntrNo(), "") ? ""
										: l161m01a.getCntrNo());
						result.set(
								"unitLoanCaseShowOld",
								("Y".equals(l160m01a.getUnitLoanCase()) ? pop
										.getProperty("L160M01A.yes") : pop
										.getProperty("L160M01A.no")));
						result.set(
								"uCMainBranchShowOld",
								("Y".equals(l160m01a.getUCMainBranch()) ? pop
										.getProperty("L160M01A.yes") : pop
										.getProperty("L160M01A.no")));

						result.set("unitLoanCaseOld", ("Y".equals(l160m01a
								.getUnitLoanCase()) ? "Y" : "N"));
						result.set("uCMainBranchOld", ("Y".equals(l160m01a
								.getUCMainBranch()) ? "Y" : "N"));
						result.set("caseNoOld", l160m01a.getCaseNo());
						result.set("uidOld", l161m01a.getUid());
						result.set("showVersion", "0"); // 顯示舊案模式(聯貸參貸比率一覽表)
					} else {
						result.set("showVersion", "0"); // 顯示新按模式(額度動用資訊GRID)
					}

				} else {
					result.set("showVersion", "1"); // 顯示新按模式(額度動用資訊GRID)
				}

			}
			break;
		case 4:
			L163S01A l163s01a = lms1601Service.findL163m01aByMainId(l160m01a
					.getMainId());
			result = DataParse.toResult(l163s01a);
			String appId = "";
			if (Util.isNotEmpty(l163s01a.getAppraiserId())) {
				appId = l163s01a.getAppraiserId() + " "
						+ lmsService.getUserName(l163s01a.getAppraiserId());
			} else {
				appId = apprId + " " + lmsService.getUserName(apprId);
			}

			result.set("appraiserId", Util.trim(appId));
			if (!Util.isEmpty(l163s01a.getBossId())) {
				result.set(
						"bossId",
						l163s01a.getBossId() + " "
								+ lmsService.getUserName(l163s01a.getBossId()));
			}
			if (!Util.isEmpty(l163s01a.getManagerId())) {
				// 當數字為0表示自行輸入
				if ("0".equals(l163s01a.getManagerId())) {
					result.set("managerId", Util.trim(l163s01a.getManagerNm()));
				} else {
					result.set("managerId", l163s01a.getManagerId() + " "
							+ lmsService.getUserName(l163s01a.getManagerId()));
				}
			}
			result.set("L163M01AForm", result);
			break;
		case 5:
			// J-106-0029-002 洗錢防制-新增洗錢防制頁籤
			// 洗錢防制
			// J-106-0238-001
			// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
			CapAjaxFormResult LMS1205S20Form01 = new CapAjaxFormResult();
			L120S09A l120s09a = amlRelateService
					.findL120s09aMaxQDateByMainId(mainId);
			if (l120s09a != null) {
				Date blackListQDate = l120s09a.getQueryDateS();
				if (blackListQDate != null) {
					LMS1205S20Form01.set("blackListQDate", CapDate
							.formatDate(blackListQDate,
									UtilConstants.DateFormat.YYYY_MM_DD));

				}

			}

			// J-106-0238-001
			// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
			L120S09B l120s09b = amlRelateService.findL120s09bByMainId(mainId);
			if (l120s09b != null) {
				LMS1205S20Form01.set("ncResult", l120s09b.getNcResult());
				LMS1205S20Form01.set("uniqueKey", l120s09b.getUniqueKey());
				LMS1205S20Form01.set("refNo", l120s09b.getRefNo());
				LMS1205S20Form01.set("ncCaseId", l120s09b.getNcCaseId());
				if (Util.isNotEmpty(Util.nullToSpace(l120s09b.getQueryDateS()))) {
					LMS1205S20Form01.set("blackListQDate", CapDate.formatDate(
							l120s09b.getQueryDateS(),
							UtilConstants.DateFormat.YYYY_MM_DD));
				}

			}

			result.set("LMS1205S20Form01", LMS1205S20Form01);

			break;
		}// close switch case
		result.set("docStatusVal", l160m01a.getDocStatus());
		result.set("useType", l160m01a.getUseType());
		if (!Util.isEmpty(l160m01a.getCustId())) {
			result.set("typCd", this.getMessage("typCd." + l160m01a.getTypCd()));
			result.set("showTypCd",
					this.getMessage("typCd." + l160m01a.getTypCd()));
			result.set("showCustId", StrUtils.concat(l160m01a.getCustId()
					.toUpperCase(), " ", l160m01a.getDupNo().toUpperCase(),
					" ", l160m01a.getCustName()));
		}

		result.set("randomCode", l160m01a.getRandomCode());
		result.set(EloanConstants.OID, CapString.trimNull(l160m01a.getOid()));
		result.set(EloanConstants.MAIN_OID,
				CapString.trimNull(l160m01a.getOid()));
		result.set(EloanConstants.MAIN_ID,
				CapString.trimNull(l160m01a.getMainId()));

		result.set("isSmallBuss", l160m01a.getIsSmallBuss());

		// J-109-0KKK_05097_B1001 簡化青年創業及啟動金貸款簽報書簽案流程
		// J-110-0CCC_05097_B1001 Web e-Loan新增國發基金協助新創事業紓困融資加碼方案微型企業簽報書格式
		result.set(
				"show_tabs_6",
				(lmsService.isRescueSmallBussOnlyCaseC(l160m01a)
						|| lmsService.isOnlyLnType61(l160m01a) || lmsService
						.isOnlyCaseType003(l160m01a)) ? "Y" : "N");

		return result;

	}

	/**
	 * 產生動審表稽核項目
	 * 
	 * @param result
	 * @param allItem
	 */
	@SuppressWarnings("unchecked")
	private void getL160M01C(CapAjaxFormResult result, List<L160M01C> allItem) {
		JSONObject jsonAllItem = null;
		JSONObject jsonVal = null;
		JSONArray jsonArrayAllItem = new JSONArray();
		JSONArray jsonArrayLocalItem = new JSONArray();
		JSONArray jsonArrayselfItem = new JSONArray();
		JSONArray jsonArrayAllVal = new JSONArray();
		for (L160M01C item : allItem) {
			jsonAllItem = new JSONObject();
			jsonVal = new JSONObject();
			jsonVal.put("id", item.getOid());
			jsonVal.put("val", item.getItemCheck());
			jsonAllItem.put("id", item.getOid());
			if (Util.isEmpty(Util.trim(item.getItemContent()))
					|| UtilConstants.Usedoc.itemType.自訂項目.equals(item
							.getItemType())) {
				jsonAllItem.put("drc", item.getItemContent());
			} else {
				jsonAllItem.put(
						"drc",
						item.getItemSeq()
								+ "."
								+ Util.trim(item.getItemContent()).replace(
										"\r", "<br/>"));
			}
			if (UtilConstants.Usedoc.itemType.全行共同項目.equals(item.getItemType())
					|| UtilConstants.Usedoc.itemType.國內企金.equals(item
							.getItemType())) {

				// 全行項目
				jsonArrayAllItem.add(jsonAllItem);
				jsonArrayAllVal.add(jsonVal);
			} else if (UtilConstants.Usedoc.itemType.當地特殊規定項目.equals(item
					.getItemType())) {

				// 本地分行項目
				jsonArrayAllVal.add(jsonVal);
				jsonArrayLocalItem.add(jsonAllItem);
			} else if (UtilConstants.Usedoc.itemType.自訂項目.equals(item
					.getItemType())) {
				jsonAllItem.put("id", item.getItemSeq());
				jsonVal.put("id", String.valueOf(item.getItemSeq()));
				// 自訂項目
				jsonArrayAllVal.add(jsonVal);
				jsonArrayselfItem.add(jsonAllItem);

			}

		}
		result.set("allItem", jsonArrayAllItem);
		result.set("localItem", jsonArrayLocalItem);
		result.set("elfItem", jsonArrayselfItem);
		result.set("value", jsonArrayAllVal);

	}

	/**
	 * 動審表重新上傳MIS
	 * 
	 * <pre>
	 * @param params PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@SuppressWarnings({ "unchecked" })
	@DomainAuth(value = AuthType.Accept, CheckDocStatus = false)
	public IResult reUpMIS(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		// 儲存and檢核
		String oid = params.getString(EloanConstants.MAIN_OID);
		L160M01A l160m01a = (L160M01A) lms1601Service.findModelByOid(
				L160M01A.class, oid);

		try {
			lms1601Service.upLoadMIS(l160m01a, false);

		} catch (FlowException t1) {
			logger.error(
					"[flowAction] lms1601Service.flowAction FlowException!!",
					t1);
			throw new CapMessageException(RespMsgHelper.getMessage(t1.getMessage()), getClass());
		} catch (Throwable t1) {
			logger.error("[flowAction] lms1601Service.flowAction EXCEPTION!!",
					t1);
			throw new CapMessageException(t1.getMessage(), getClass());
		}

		return result;
	}

	/**
	 * 呈主管覆核(呈主管 主管覆核 拆2個method)
	 * 
	 * <pre>
	 * @param params PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@SuppressWarnings({ "unchecked" })
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult flowAction(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		// 儲存and檢核
		String oid = params.getString(EloanConstants.MAIN_OID);
		L160M01A l160m01a = (L160M01A) lms1601Service.findModelByOid(
				L160M01A.class, oid);
		String[] formSelectBoss = params.getStringArray("selectBoss");

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		if (!Util.isEmpty(formSelectBoss)) {

			String manager = Util.trim(params.getString("manager"));
			List<L160M01D> models = (List<L160M01D>) lms1601Service
					.findListByMainId(L160M01D.class, l160m01a.getMainId());
			if (!models.isEmpty()) {
				lms1601Service.deleteL160m01ds(models, false);
			}
			List<L160M01D> l160m01ds = new ArrayList<L160M01D>();
			for (String people : formSelectBoss) {
				L160M01D l160m01d = new L160M01D();
				l160m01d.setCreator(user.getUserId());
				l160m01d.setCreateTime(CapDate.getCurrentTimestamp());
				l160m01d.setMainId(l160m01a.getMainId());

				// L1. 分行經辦 L3. 分行授信主管 L4. 分行覆核主管 L5. 經副襄理
				l160m01d.setStaffJob(UtilConstants.STAFFJOB.授信主管L3);
				l160m01d.setStaffNo(people);
				l160m01ds.add(l160m01d);
			}
			L160M01D managerL160m01d = new L160M01D();
			managerL160m01d.setCreator(user.getUserId());
			managerL160m01d.setCreateTime(CapDate.getCurrentTimestamp());
			managerL160m01d.setMainId(l160m01a.getMainId());
			managerL160m01d.setStaffJob(UtilConstants.STAFFJOB.單位授權主管L5);
			managerL160m01d.setStaffNo(manager);
			l160m01ds.add(managerL160m01d);
			L160M01D apprL160m01d = new L160M01D();
			apprL160m01d.setCreator(user.getUserId());
			apprL160m01d.setCreateTime(CapDate.getCurrentTimestamp());
			apprL160m01d.setMainId(l160m01a.getMainId());
			apprL160m01d.setStaffJob(UtilConstants.STAFFJOB.經辦L1);
			apprL160m01d.setStaffNo(user.getUserId());
			l160m01ds.add(apprL160m01d);
			lms1601Service.saveL160m01dList(l160m01ds);
		}
		Boolean upMis = false;
		// 如果有這個key值表示是輸入chekDate核准日期
		if (params.containsKey("checkDate")) {

			l160m01a.setApprover(user.getUserId());
			l160m01a.setApproveTime(CapDate.convertStringToTimestamp(params
					.getString("checkDate") + " 00:00:00"));
			l160m01a.setReCheckId(user.getUserId());
			// J-110-0547 為控管先行動用之授信案件，增加先行動用呈核及控制表預定補全日期之通知功能。
			// for批次使用，系統實際核准時間，讓晚上批次可以抓資料寫進ELF601
			l160m01a.setSysActApproveTime(CapDate.getCurrentTimestamp());
			upMis = true;
			L160M01D l160m01d = lms1601Service.findL160m01d(
					l160m01a.getMainId(), user.getUserId(),
					UtilConstants.STAFFJOB.執行覆核主管L4);
			if (l160m01d == null) {
				l160m01d = new L160M01D();
				l160m01d.setCreator(user.getUserId());
				l160m01d.setCreateTime(CapDate.getCurrentTimestamp());
				l160m01d.setMainId(l160m01a.getMainId());
				l160m01d.setStaffJob(UtilConstants.STAFFJOB.執行覆核主管L4);
				l160m01d.setStaffNo(user.getUserId());
			}
			lms1601Service.save(l160m01d);
			String cltype = this.getCltype(l160m01a.getCustId(),
					l160m01a.getDupNo());
			if (Util.isNotEmpty(cltype)) {
				result.set("otherMsg", cltype);
			}

			// J-110-0234_05097_B1002 Web
			// e-Loan國內企金簽報書小規模RPA修改**************************************
			String LMS_SMALLBUSS_C_RETRUN_STATUS = Util.trim(lmsService
					.getSysParamDataValue("LMS_SMALLBUSS_C_RETRUN_STATUS"));
			if (Util.equals(LMS_SMALLBUSS_C_RETRUN_STATUS, "Y")) {

				List<L140M01A> chkl140m01as = new ArrayList<L140M01A>();
				Set<L160M01B> l160m01bs = l160m01a.getL160m01b();
				if (l160m01bs != null && !l160m01bs.isEmpty()) {
					for (L160M01B l160m01b : l160m01bs) {
						L140M01A l140m01a = amlRelateService.findModelByMainId(
								L140M01A.class, l160m01b.getReMainId());
						if (l140m01a != null) {
							chkl140m01as.add(l140m01a);
						}
					}
				}

				if (chkl140m01as != null && !chkl140m01as.isEmpty()) {

					for (L140M01A templ140m01a : chkl140m01as) {
						// ID 把id補成10碼再作處理
						boolean notChkProperty = false;

						if (LMSUtil.isContainValue(
								Util.trim(templ140m01a.getProPerty()),
								UtilConstants.Cntrdoc.Property.不變)
								|| LMSUtil.isContainValue(
										Util.trim(templ140m01a.getProPerty()),
										UtilConstants.Cntrdoc.Property.取消)) {
							notChkProperty = true;
						}

						if (!notChkProperty) {

							boolean isSmallBussC = lmsService
									.isCntrDocSmallBussC(templ140m01a);

							if (isSmallBussC) {
								// 小規模額度才要寫回線上進件申貸狀態
								BigDecimal currentApplyAmt = templ140m01a
										.getCurrentApplyAmt() == null ? BigDecimal.ZERO
										: templ140m01a.getCurrentApplyAmt();
								if (currentApplyAmt.compareTo(BigDecimal.ZERO) > 0) {

									// 找到最新一筆線上進件資料並更新狀態
									Map<String, Object> cesC240m01aMap = eloandbBASEService
											.findCesC240m01aLastByOwnBrIdAndCustId(
													l160m01a.getOwnBrId(),
													templ140m01a.getCustId());

									if (cesC240m01aMap != null
											&& !cesC240m01aMap.isEmpty()) {
										// 有最新一筆線上進件

										String status4BrCes240 = "";
										String c240oid = Util.trim(MapUtils
												.getString(cesC240m01aMap,
														"OID"));

										status4BrCes240 = UtilConstants.c240m01aStatus4Br.動審表已核准;

										if (Util.notEquals(c240oid, "")) {

											String orgstatus4Br = Util
													.trim(MapUtils.getShort(
															cesC240m01aMap,
															"STATUS4BR"));
											if (Util.notEquals(
													orgstatus4Br,
													UtilConstants.c240m01aStatus4Br.動審表已核准)) {
												// 如果目前線上進件最新狀態為動審表已核准:B22，就不要再被簽報書蓋掉了

												// 更新CES.C240M01A STATUS4BR
												if (Util.notEquals(
														status4BrCes240, "")) {
													eloandbBASEService
															.updateC240m01aStatus4BrByOid(
																	status4BrCes240,
																	c240oid);
												}
											}

										}

									}// 有最新一筆線上進件 END

								}
							}
						}
					}
				}

			}

		}

		if (!Util.isEmpty(l160m01a)) {
			try {
				// 如果有這值表示非呈主管，要檢查覆核主管和文件最後更新者是否相同
				if (params.containsKey("flowAction")) {
					// 退回部檢查
					if (params.getBoolean("flowAction")) {
						L160M01D l160m01d = lms1601Service.findL160m01d(
								l160m01a.getMainId(), user.getUserId(),
								UtilConstants.STAFFJOB.經辦L1);
						if (l160m01d != null) {
							// EFD0053=WARN|覆核人員不可與「經辦人員或其它覆核人員」為同一人|
							throw new CapMessageException(RespMsgHelper.getMessage("EFD0053"), getClass());
						}

						// J-110-0547 為控管先行動用之授信案件，增加先行動用呈核及控制表預定補全日期之通知功能。
						if (l160m01a.getL163S01A() != null) {
							// L163S01A有可能存在，但沒有WaitingItem，所以要包在判斷是否有WaitingItem裡
							if (!Util.isEmpty(Util.trim(l160m01a.getL163S01A()
									.getWaitingItem()))) {
								// 主管核准時會檢查，預定補全日為今天以前(含今天)，代表他應該應該要收齊資料了，就不該是先行動用
								// 02O待覆核時才需要檢查，避免後面先行動用_待覆核時也走進來判斷
								if (Util.equals(l160m01a.getDocStatus(),
										CreditDocStatusEnum.海外_待覆核)
										&& l160m01a.getL163S01A()
												.getWillFinishDate() != null
										&& CapDate.getCurrentTimestamp().after(
												l160m01a.getL163S01A()
														.getWillFinishDate())) {
									// L163M01A.willFinishDateError=先行動用控制表之預定補全日期需晚於今日日期
									Properties pop = MessageBundleScriptCreator
											.getComponentResource(LMS1601M01Page.class);
									throw new CapMessageException(
											pop.getProperty("L163M01A.willFinishDateError"),
											getClass());
								}
							}
						}
					}
				}
				lms1601Service
						.flowAction(l160m01a.getOid(), l160m01a,
								params.containsKey("flowAction"),
								params.getAsBoolean("flowAction", false), upMis);
			} catch (FlowException t1) {
				logger.error(
						"[flowAction] lms1601Service.flowAction FlowException!!",
						t1);
				throw new CapMessageException(RespMsgHelper.getMessage(
						t1.getMessage()), getClass());
			} catch (Throwable t1) {
				logger.error(
						"[flowAction] lms1601Service.flowAction EXCEPTION!!",
						t1);
				throw new CapMessageException(t1.getMessage(), getClass());
			}
		}

		return result;
	}

	/**
	 * 檢核資料是否已經有正確的登錄
	 * 
	 * <pre>
	 * @param params PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult checkData(PageParameters params) throws CapException {
		Map<String, String> param = new HashMap<String, String>();
		// 儲存and檢核
		String oid = params.getString(EloanConstants.OID);
		L160M01A l160m01a = lms1601Service.findModelByOid(L160M01A.class, oid);

		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1601M01Page.class);
		if (Util.isEmpty(l160m01a)) {

			// L160M01A.error10=請先儲存
			param.put("msg", pop.getProperty("L160M01A.error10"));
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤, param), getClass());
		}
		
		String mainId = l160m01a.getMainId();
		List<L160M01C> l160m01cList = (List<L160M01C>) lms1601Service
				.findListByMainId(L160M01C.class, mainId);
		// 檢核是否引進額度明細表
		if (l160m01a.getL160m01b().isEmpty()) {

			// L160M01A.error3=請先引進額度明細表
			param.put("msg", pop.getProperty("L160M01A.error3"));
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤, param), getClass());
		}
		if (!Util.isEmpty(l160m01a.getGuFromDate())
				&& !Util.isEmpty(l160m01a.getGuEndDate())) {
			// EFD3026=ERROR|$\{colName\}起始日期不能大於結束日期|
			if (l160m01a.getGuFromDate().after(l160m01a.getGuEndDate())) {
				param.put("colName", pop.getProperty("L160M01A.guCurr") + " "
						+ pop.getProperty("L160M01A.guFromDate"));
				// L160M01A.guCurr"><!--連帶保證書最高本金
				// L160M01A.guFromDate"><!--期間-
				throw new CapMessageException(RespMsgHelper.getMessage("EFD3026", param), getClass());
			}
		}
		// 檢核動用期限是否登錄
		if (Util.isEmpty(l160m01a.getTType())) {

			// L160M01A.tType= 授信契約書
			param.put("colName", pop.getProperty("L160M01A.tType"));
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.欄位不得為空, param), getClass());
		} else if ("1".equals(l160m01a.getTType())) {
			param.put("colName", pop.getProperty("L160M01A.useDate"));
			switch (Util.parseInt(l160m01a.getUseSelect())) {
			case 1:
				if (Util.isEmpty(l160m01a.getUseFromDate())
						|| Util.isEmpty(l160m01a.getUseEndDate())) {
					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.欄位不得為空, param),
							getClass());
				}
				// EFD3026=ERROR|$\{colName\}起始日期不能大於結束日期|
				if (l160m01a.getUseFromDate().after(l160m01a.getUseEndDate())) {
					throw new CapMessageException(RespMsgHelper.getMessage("EFD3026", param), getClass());
				}
				break;
			case 2:
				if (Util.isEmpty(l160m01a.getUseMonth())) {
					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.欄位不得為空, param),
							getClass());
				}
				break;
			case 3:
				if (Util.isEmpty(l160m01a.getUseOther())) {
					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.欄位不得為空, param),
							getClass());
				}
				break;
			default:
				throw new CapMessageException(RespMsgHelper.getMessage(
						UtilConstants.AJAX_RSP_MSG.欄位不得為空, param), getClass());
			}
		} else if ("2".equals(l160m01a.getTType())) {
			param.put("colName", pop.getProperty("L160M01A.useDate"));
			switch (Util.parseInt(l160m01a.getUseSelect())) {
			case 1:
				if (Util.isEmpty(l160m01a.getUseFromDate())
						|| Util.isEmpty(l160m01a.getUseEndDate())) {
					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.欄位不得為空, param),
							getClass());
				}
				// EFD3026=ERROR|$\{colName\}起始日期不能大於結束日期|
				if (l160m01a.getUseFromDate().after(l160m01a.getUseEndDate())) {
					throw new CapMessageException(RespMsgHelper.getMessage("EFD3026", param), getClass());
				}
				break;
			case 2:
				if (Util.isEmpty(l160m01a.getUseMonth())) {
					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.欄位不得為空, param),
							getClass());
				}
				break;
			case 3:
				if (Util.isEmpty(l160m01a.getUseOther())) {
					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.欄位不得為空, param),
							getClass());
				}
				break;
			default:
				throw new CapMessageException(RespMsgHelper.getMessage(
						UtilConstants.AJAX_RSP_MSG.欄位不得為空, param), getClass());
			}
			param.put("colName", pop.getProperty("L160M01A.lnDate"));
			switch (Util.parseInt(l160m01a.getLnSelect())) {
			case 1:
				if (Util.isEmpty(l160m01a.getLnFromDate())
						|| Util.isEmpty(l160m01a.getLnEndDate())) {

					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.欄位不得為空, param),
							getClass());
				}
				// EFD3026=ERROR|$\{colName\}起始日期不能大於結束日期|
				if (l160m01a.getLnFromDate().after(l160m01a.getLnEndDate())) {
					throw new CapMessageException(RespMsgHelper.getMessage("EFD3026", param), getClass());
				}
				break;
			case 2:
				if (Util.isEmpty(l160m01a.getLnMonth())
						|| Util.isEmpty(l160m01a.getLnYear())) {

					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.欄位不得為空, param),
							getClass());
				}
				break;
			case 3:
				if (Util.isEmpty(Util.trim(l160m01a.getLnOther()))) {

					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.欄位不得為空, param),
							getClass());
				}
				break;
			default:
				throw new CapMessageException(RespMsgHelper.getMessage(
						UtilConstants.AJAX_RSP_MSG.欄位不得為空, param), getClass());
			}
		}
		// L160M01A.comm=審核意見
		if (Util.isEmpty(Util.trim(l160m01a.getComm()))) {
			param.put("colName", pop.getProperty("L160M01A.comm"));
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.欄位不得為空, param), getClass());
		}
		if (l160m01a.getL163S01A() != null) {
			// 檢查是否有待辦事項若有要將預定補全日填上日期
			if (!Util.isEmpty(Util
					.trim(l160m01a.getL163S01A().getWaitingItem()))) {
				if (Util.isEmpty(l160m01a.getL163S01A().getWillFinishDate())) {
					// L160M01A.willFinishDate=預定補全日期
					param.put("colName",
							pop.getProperty("L160M01A.willFinishDate"));
					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.欄位不得為空, param),
							getClass());
				}

				// J-110-0547 為控管先行動用之授信案件，增加先行動用呈核及控制表預定補全日期之通知功能。
				// 主管核准時會檢查，預定補全日為今天以前(含今天)，代表他應該應該要收齊資料了，就不該是先行動用
				// 01O編制中時才需要檢查
				// L163S01A有可能存在，但沒有WaitingItem，所以要包在判斷是否有WaitingItem裡
				if (Util.equals(l160m01a.getDocStatus(),
						CreditDocStatusEnum.海外_編製中)
						&& l160m01a.getL163S01A().getWillFinishDate() != null
						&& CapDate.getCurrentTimestamp().after(
								l160m01a.getL163S01A().getWillFinishDate())) {
					// L163M01A.willFinishDateError=先行動用控制表之預定補全日期需晚於今日日期
					throw new CapMessageException(
							pop.getProperty("L163M01A.willFinishDateError"),
							getClass());
				}
			}
		}

		// 當是否為管理行和是否有聯貸案為是檢核是否聯貸案參貸比率一覽表
		// 2013/07/03,Rex,同業聯貸的檢查條件改為UnitLoanCase 才需檢查並登打，且需判斷選入的額度明細表是否有同業聯貸
		// if ("Y".equals(l160m01a.getUnitLoanCase())
		// && "Y".equals(l160m01a.getUCMainBranch())) {

		if (Util.equals(Util.trim(l160m01a.getNewVersion()), "")
				|| Util.equals(Util.trim(l160m01a.getNewVersion()), "00")) {
			// 動審表請重新引進額度明細表
			throw new CapMessageException(
					pop.getProperty("L160M01A.message73"), getClass());
		}

		Set<L161S01A> l161s01as = l160m01a.getL161S01A();

		// J-105-0135-001 Web e-Loan國內企金授信系統動審表，開放可修改振興經濟非中小企業專案貸款註記與金額。
		StringBuffer nonSMEProjCntrNo = new StringBuffer("");

		if (l161s01as.isEmpty()) {
			// 動審表請重新引進額度明細表
			throw new CapMessageException(
					pop.getProperty("L160M01A.message73"), getClass());
		} else {

			StringBuffer errCntrNo1 = new StringBuffer("");
			StringBuffer errCntrNo2 = new StringBuffer(""); // 判斷進出口、遠匯續約案件動用期限種類必須為
															// 1
															// (YYY/MM/DD～YYY/MM/DD)

			//
			// 1. "715","942","944","961","962","941","9411","9441"
			// "進出口、遠匯續約案件動用期限種類必須為 1 (YYY/MM/DD～YYY/MM/DD)"
			String checkItem1 = "715,942,944,9441,961,9611,962,963,964,941,9411"; // 715,942,944,9441,961,9611,962,963,964,941,9411
			String[] item1 = checkItem1.split(",");
			List<String> asList1 = Arrays.asList(item1);

			// J-109-0077_05097_B1003 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
			HashMap<String, String> rescueItemMap = new HashMap<String, String>();
			HashMap<String, String> isSmallBussMap = new HashMap<String, String>();
			HashMap<String, String> alreadyChkQuotapprIsSmallBussMap = new HashMap<String, String>();

			for (L161S01A l161s01a : l161s01as) {
				if (Util.notEquals(l161s01a.getChkYN(), "Y")) {
					// 額度動用資訊上未完成檢核，不得呈主管覆核
					param.put("msg", pop.getProperty("L160M01A.message74"));
					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, param),
							getClass());
				}

				if (Util.equals(Util.trim(l161s01a.getIsRescue()), "Y")) {

					String rescueItem = Util.trim(l161s01a.getRescueItem());
					if (Util.notEquals(rescueItem, "")
							&& lmsService.isResueItemOldCase(rescueItem)) {
						String newRescueIbDate = l161s01a.getRescueIbDate() == null ? ""
								: CapDate.formatDate(
										l161s01a.getRescueIbDate(),
										"yyyy-MM-dd");
						if (rescueItemMap.containsKey(rescueItem)) {
							String oldRescueIbDate = Util.trim(rescueItemMap
									.get(rescueItem));
							if (Util.notEquals(oldRescueIbDate, "")
									&& Util.notEquals(newRescueIbDate, "")) {
								if (Util.notEquals(oldRescueIbDate,
										newRescueIbDate)) {
									// 動審表請重新引進額度明細表
									throw new CapMessageException(
											"額度動用資訊一覽表中，額度序號「"
													+ l161s01a.getCntrNo()
													+ "」紓困貸款類別「" + rescueItem
													+ "」之合意展延日「"
													+ newRescueIbDate
													+ "」與其他相同紓困貸款類別不同「"
													+ oldRescueIbDate + "」",
											getClass());
								}
							}
						} else {

							if (Util.notEquals(newRescueIbDate, "")) {
								rescueItemMap.put(rescueItem, newRescueIbDate);
							}

						}
					}

				}

				L140M01A l140m01a = lms1401Service
						.findL140m01aByMainId(l161s01a.getCntrMainId());

				if (l140m01a == null) {
					// 找不到額度明細表
					throw new CapMessageException(
							pop.getProperty("L160M01A.message66")
									+ l161s01a.getCntrNo(), getClass());
				}

				// J-110-0253_05097_B1001 Web
				// e-Loan企金授信配合經濟部紓困4.0，新增A04、A05、A06專案
				// e-Loan企金授信配合經濟部紓困4.0，新增A04、A05、A06專案
				String isRescueOn = Util.trim(l140m01a.getIsRescueOn());
				String rescueItemOn = Util.trim(l140m01a.getRescueItemOn());
				String cntrNoOn = Util.trim(l140m01a.getCntrNoOn());

				if (Util.equals(Util.trim(l161s01a.getIsRescue()), "Y")) {
					String rescueItem = Util.trim(l161s01a.getRescueItem());
					if (Util.notEquals(rescueItem, "")) {
						String newRescueIbDate = l161s01a.getRescueIbDate() == null ? ""
								: CapDate.formatDate(
										l161s01a.getRescueIbDate(),
										"yyyy-MM-dd");
						String LMS_RESCUEITEM_RESCUEIBDATE_CC = Util
								.trim(lmsService
										.getSysParamDataValue("LMS_RESCUEITEM_RESCUEIBDATE_CC"));
						if (Util.notEquals(LMS_RESCUEITEM_RESCUEIBDATE_CC, "")) {
							JSONObject jsonRescueIbDate = JSONObject
									.fromObject("{"
											+ LMS_RESCUEITEM_RESCUEIBDATE_CC
											+ "}");
							if (jsonRescueIbDate != null) {
								String rescueIbDateBase = Util
										.trim(jsonRescueIbDate.get(rescueItem));
								if (Util.isNotEmpty(rescueIbDateBase)) {
									if (Util.notEquals(newRescueIbDate, "")) {
										if (LMSUtil
												.cmpDate(
														Util.parseDate(newRescueIbDate),
														"<",
														Util.parseDate(rescueIbDateBase))) {
											String msg ="額度動用資訊一覽表中，額度序號「"
													+ l161s01a.getCntrNo()
													+ "」紓困貸款類別「"
													+ rescueItem
													+ "」之合意展延日「"
													+ newRescueIbDate
													+ "」不得早於「"
													+ rescueIbDateBase
													+ "」";
											if (rescueItem.matches("J04|J05")) {
												msg = "額度動用資訊一覽表中，額度序號「"
														+ l161s01a.getCntrNo()
														+ "」天然災害貸款類別「"
														+ rescueItem
														+ "」之合意減息日「"
														+ newRescueIbDate
														+ "」不得早於「"
														+ rescueIbDateBase
														+ "」";
											}
											throw new CapMessageException(
													msg, getClass());
										}
									}

								}
							}
						}
					}
				}

				if (Util.equals(Util.trim(l161s01a.getIsRescue()), "Y")
						&& Util.equals(isRescueOn, "Y")) {

					String rescueItem = Util.trim(l161s01a.getRescueItem());
					if (Util.notEquals(rescueItem, "")
							&& lmsService.isResueItemOldCase(rescueItem)) {
						String newRescueIbDate = l161s01a.getRescueIbDate() == null ? ""
								: CapDate.formatDate(
										l161s01a.getRescueIbDate(),
										"yyyy-MM-dd");
						String LMS_RESCUEITEM_RESCUEIBDATE_CK = Util
								.trim(lmsService
										.getSysParamDataValue("LMS_RESCUEITEM_RESCUEIBDATE_CK"));
						if (Util.notEquals(LMS_RESCUEITEM_RESCUEIBDATE_CK, "")) {
							JSONObject jsonRescueIbDate = JSONObject
									.fromObject("{"
											+ LMS_RESCUEITEM_RESCUEIBDATE_CK
											+ "}");
							if (jsonRescueIbDate != null) {
								String rescueIbDateBase = Util
										.trim(jsonRescueIbDate.get(rescueItem));
								if (Util.isNotEmpty(rescueIbDateBase)) {
									if (Util.notEquals(newRescueIbDate, "")) {
										if (LMSUtil
												.cmpDate(
														Util.parseDate(newRescueIbDate),
														"<",
														Util.parseDate(rescueIbDateBase))) {
											throw new CapMessageException(
													"額度動用資訊一覽表中，額度序號「"
															+ l161s01a.getCntrNo()
															+ "」紓困貸款類別「"
															+ rescueItem
															+ "」之合意展延日「"
															+ newRescueIbDate
															+ "」不得早於「"
															+ rescueIbDateBase
															+ "」", getClass());

										}
									}

								}
							}
						}

					}

					// J-111-0112_05097_B1002 Web e-Loan企金授信管理系統新增111年經濟部紓困方案
					// 有是否延續前一年度紓困專案利息補貼方案時檢核
					if ((lmsService.isResueItemSubSidy(rescueItem, "111") && (lmsService
							.isResueItemSubSidy(rescueItemOn, "109")))) {
						// J-111-0112_05097_B1002 Web
						// e-Loan企金授信管理系統新增111年經濟部紓困方案
						String errMsg = lms1201Service
								.chkRescueItemHasTwoCross(
										Util.trim(l140m01a.getCustId()),
										Util.trim(l140m01a.getDupNo()),
										Util.trim(l140m01a.getCntrNo()),
										rescueItem, rescueItemOn);
						if (Util.notEquals(errMsg, "")) {
							// "額度序號" + cntrNo + "有動審表已覆核之110年度紓困方案「" +
							// rescueItem110
							// + "」，欄位「是否延續前一年度紓困專案利息補貼方案」不得為109年度紓困方案「" +
							// rescueItemOn
							// + "」";
							throw new CapMessageException(errMsg, getClass());
						}
					}

				}

				// J-110-0253_05097_B1001 Web
				// e-Loan企金授信配合經濟部紓困4.0，新增A04、A05、A06專案
				if (Util.equals(Util.trim(l161s01a.getIsRescue()), "Y")) {
					// A04檢核，合意展延日要晚於A01
					String rescueItem = Util.trim(l161s01a.getRescueItem());

					// J-111-0112_05097_B1002 Web e-Loan企金授信管理系統新增111年經濟部紓困方案
					if (Util.notEquals(rescueItem, "")
							&& lmsService.isResueItemOldCase(rescueItem)
							&& (lmsService
									.isResueItemSubSidy(rescueItem, "110") || lmsService
									.isResueItemSubSidy(rescueItem, "111"))) {

						String newRescueIbDate = l161s01a.getRescueIbDate() == null ? ""
								: CapDate.formatDate(
										l161s01a.getRescueIbDate(),
										"yyyy-MM-dd");

						List<Map<String, Object>> l161s01aList = eloandbBASEService
								.findL161s01aLastRescueDataWithoutRescueItem(
										l161s01a.getCntrNo(),
										l161s01a.getCustId(),
										l161s01a.getDupNo());

						if (l161s01aList != null && !l161s01aList.isEmpty()) {
							for (Map<String, Object> l161s01aMap : l161s01aList) {
								String elf383RescueItem = Util.trim(MapUtils
										.getString(l161s01aMap, "RESCUEITEM",
												""));
								if (Util.notEquals(elf383RescueItem, "")
										&& lmsService
												.isResueItemOldCase(elf383RescueItem)
										&& (lmsService.isResueItemSubSidy(
												elf383RescueItem, "109") || lmsService
												.isResueItemSubSidy(
														elf383RescueItem, "110"))) {

									String RESCUEIBDATE_161 = Util
											.trim(MapUtils
													.getString(l161s01aMap,
															"RESCUEIBDATE"));

									if (Util.notEquals(RESCUEIBDATE_161, "")
											&& Util.notEquals(newRescueIbDate,
													"")) {
										if (LMSUtil
												.cmpDate(
														Util.parseDate(newRescueIbDate),
														"<",
														Util.parseDate(RESCUEIBDATE_161))) {
											throw new CapMessageException(
													"額度動用資訊一覽表中，額度動用資訊一覽表中，額度序號「"
															+ l161s01a.getCntrNo()
															+ "」紓困貸款類別「"
															+ rescueItem
															+ "」之合意展延日「"
															+ newRescueIbDate
															+ "」不得早於"
															+ (lmsService
																	.isResueItemSubSidy(
																			elf383RescueItem,
																			"109") ? "109"
																	: "110")
															+ "年紓困方案合意展延日「"
															+ RESCUEIBDATE_161
															+ "」", getClass());

										}
									}

								}

							}
						}

					}

				}

				// J-105-0135-001 Web e-Loan國內企金授信系統動審表，開放可修改振興經濟非中小企業專案貸款註記與金額。
				// J-108-0098-001 企金處移除振興經濟非中小企相關欄位
				// if (Util.equals(Util.trim(l161s01a.getIsNonSMEProjLoan()),
				// "Y")) {
				// nonSMEProjCntrNo.append(Util.equals(
				// nonSMEProjCntrNo.toString(), "") ? "" : "、");
				// nonSMEProjCntrNo.append(l161s01a.getCntrNo());
				// }

				// J-109-0077_05097_B1013 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
				// 檢核同一客戶，央行轉融通辦理類別 [A、B] 與[C]不能同時存在

				String needChkIsSmallBuss = Util.trim(lmsService
						.getSysParamDataValue("LMS_J1090077_CHK_ISSMALLBUSS"));

				if (Util.equals(needChkIsSmallBuss, "Y")) {

					if (Util.equals(Util.trim(l161s01a.getIsRescue()), "Y")) {
						String isCbRefin = Util.trim(l161s01a.getIsCbRefin());
						if (Util.equals(isCbRefin, "Y")) {
							// 額度明細表的央行轉融通辦理類別
							String isSmallBuss = Util.trim(l140m01a
									.getIsSmallBuss());
							if (Util.notEquals(isSmallBuss, "")) {

								// 檢查檢查同一借款人於此動審表中
								// 檢查動審表本身是否有互斥的央行轉融通辦理類別
								String xxKey = Util.trim(l161s01a.getCustId())
										+ "-" + Util.trim(l161s01a.getDupNo());

								if (isSmallBussMap.containsKey(xxKey)) {
									String oldIsSmallBuss = Util
											.trim(isSmallBussMap.get(xxKey));
									if (Util.notEquals(oldIsSmallBuss, "")) {
										if (Util.equals(isSmallBuss, "A")
												|| Util.equals(isSmallBuss, "B")) {
											if (Util.equals(oldIsSmallBuss, "C")) {
												// 動審表請重新引進額度明細表
												throw new CapMessageException(
														"額度動用資訊一覽表中，額度序號「"
																+ l161s01a.getCntrNo()
																+ "」央行轉融通辦理類別為「"
																+ isSmallBuss
																+ "」，不得同時承作其他央行轉融通辦理類別「"
																+ oldIsSmallBuss
																+ "」 ",
														getClass());
											}
										} else if (Util
												.equals(isSmallBuss, "C")) {
											if (Util.notEquals(oldIsSmallBuss,
													"C")) {
												// 動審表請重新引進額度明細表
												throw new CapMessageException(
														"額度動用資訊一覽表中，額度序號「"
																+ l161s01a.getCntrNo()
																+ "」央行轉融通辦理類別為「"
																+ isSmallBuss
																+ "」，不得同時承作其他央行轉融通辦理類別「"
																+ oldIsSmallBuss
																+ "」 ",
														getClass());
											}
										}
									}

								} else {
									isSmallBussMap.put(xxKey, isSmallBuss);
								}

								// 檢查同一借款人於MIS.QUOTAPPR其他已覆核的動審表有無互斥的央行轉融通辦理類別
								if (!alreadyChkQuotapprIsSmallBussMap
										.containsKey(xxKey)) {

									alreadyChkQuotapprIsSmallBussMap.put(xxKey,
											xxKey);

									StringBuffer errIsSamllBusCntrNo = new StringBuffer(
											"");
									String[] isSmallBusArr = null;
									if (Util.equals(isSmallBuss, "A")
											|| Util.equals(isSmallBuss, "B")) {
										// 找不同額度序號，有沒有C
										isSmallBusArr = new String[] { "C" };

									} else if (Util.equals(isSmallBuss, "C")) {
										// 找不同額度序號，有沒有AB
										isSmallBusArr = new String[] { "A", "B" };

									}

									if (isSmallBusArr != null && isSmallBusArr.length > 0) {
										List<Map<String, Object>> elf383List = misQuotapprService
												.findHasOtherIsSmallBus(Util
														.trim(l161s01a
																.getCustId()),
														Util.trim(l161s01a
																.getDupNo()),
														Util.trim(l161s01a
																.getCntrNo()),
														isSmallBusArr);

										if (elf383List != null
												&& !elf383List.isEmpty()) {
											for (Map<String, Object> elf383Map : elf383List) {

												String xxCntrNo = Util
														.trim(MapUtils
																.getString(
																		elf383Map,
																		"CNTRNO"));
												String xxIsSmallBus = Util
														.trim(MapUtils
																.getString(
																		elf383Map,
																		"ISSMALLBUS"));
												if (Util.notEquals(xxCntrNo, "")) {
													if (Util.equals(
															errIsSamllBusCntrNo
																	.toString(),
															"")) {
														errIsSamllBusCntrNo
																.append(xxCntrNo)
																.append("(")
																.append(xxIsSmallBus)
																.append(")");

													} else {
														errIsSamllBusCntrNo
																.append("、")
																.append(xxCntrNo)
																.append("(")
																.append(xxIsSmallBus)
																.append(")");
													}

												}

											}
										}

										if (Util.notEquals(
												errIsSamllBusCntrNo.toString(),
												"")) {
											throw new CapMessageException(
													"借款人於已核准動審案件有其他不得同時承作之「央行轉融通辦理類別」，內容："
															+ errIsSamllBusCntrNo
																	.toString(),
													getClass());

										}
									}
								}

							}
						}
					}
				}

				String needChkRescueSame = Util.trim(lmsService
						.getSysParamDataValue("LMS_J1090077_CHK_RESCUE_SAME"));

				if (Util.equals(needChkRescueSame, "Y")) {

					// 檢核該額度序號與最新一筆核准的動審表紓困貸款類別 是否與本次一致
					if (Util.equals(Util.trim(l161s01a.getIsRescue()), "Y")) {
						String ELF383_ISRESCUE = "";
						String ELF383_RESCUEITEM = "";
						Map<String, Object> quotapprMap = misdbBASEService
								.findLastQuotapprOrderBySDate(
										Util.trim(l161s01a.getCustId()),
										Util.trim(l161s01a.getDupNo()),
										Util.trim(l161s01a.getCntrNo()));

						if (quotapprMap != null && !quotapprMap.isEmpty()) {
							ELF383_ISRESCUE = Util.trim(MapUtils.getString(
									quotapprMap, "ISRESCUE", ""));
						}

						if (Util.equals(ELF383_ISRESCUE, "Y")) {

							String l161s01aRescueItem = "";
							List<Map<String, Object>> l161s01aList = eloandbBASEService
									.findL161s01aLastRescueDataWithoutRescueItem(
											Util.trim(l161s01a.getCntrNo()),
											Util.trim(l161s01a.getCustId()),
											Util.trim(l161s01a.getDupNo()));

							if (l161s01aList != null && !l161s01aList.isEmpty()) {
								for (Map<String, Object> l161s01aMap : l161s01aList) {
									l161s01aRescueItem = Util.trim(MapUtils
											.getString(l161s01aMap,
													"RESCUEITEM", ""));
									break;
								}
							}
							if (Util.isNotEmpty(l161s01aRescueItem)) {
								// 只能變更為
								String onlyCan = Util
										.trim(lmsService
												.getSysParamDataValue("LMS_RESCUEITEM_CHANGE_ONLYCAN"));
								if (Util.notEquals(onlyCan, "")) {
									JSONObject jsonOnlyCan = JSONObject
											.fromObject("{" + onlyCan + "}");
									if (jsonOnlyCan != null) {
										String onlyCanItem = Util
												.trim(jsonOnlyCan
														.get(l161s01aRescueItem));
										if (Util.isNotEmpty(onlyCanItem)) {
											boolean ocPass = false;
											String[] onlyCanArr = StringUtils
													.split(onlyCanItem, "|");
											for (String ocItem : onlyCanArr) {
												if (Util.equals(
														Util.trim(l161s01a
																.getRescueItem()),
														ocItem)) {
													ocPass = true;
													break;
												}
											}
											if (!ocPass) {

												// L140M01a.message270=最近一次已核准動審表紓困類別為{0}，本次簽案紓困代碼類別不得更改為{1}。
												Properties prop = MessageBundleScriptCreator
														.getComponentResource(LMS1401S02Panel.class);
												throw new CapMessageException(
														MessageFormat
																.format(prop
																		.getProperty("L140M01a.message270"),
																		l161s01aRescueItem,
																		Util.trim(l161s01a
																				.getRescueItem())),
														getClass());

											}
										}
									}
								}
							}
						}

						// Map<String, Object> quotapprMap = misdbBASEService
						// .findLastQuotapprOrderBySDate(
						// Util.trim(l161s01a.getCustId()),
						// Util.trim(l161s01a.getDupNo()),
						// Util.trim(l161s01a.getCntrNo()));
						//
						// if (quotapprMap != null && !quotapprMap.isEmpty()) {
						// String ELF383_ISRESCUE = Util.trim(MapUtils
						// .getString(quotapprMap, "ISRESCUE", ""));
						// if (Util.equals(ELF383_ISRESCUE, "Y")) {
						// // 比對
						//
						// String ELF383_RESCUEITEM = Util.trim(MapUtils
						// .getString(quotapprMap, "RESCUEITEM",
						// ""));
						//
						// if (Util.notEquals(ELF383_RESCUEITEM, "")
						// && Util.notEquals(Util.trim(l161s01a
						// .getRescueItem()), "")) {
						// if (Util.notEquals(ELF383_RESCUEITEM,
						// Util.trim(l161s01a.getRescueItem()))) {
						// throw new CapMessageException("額度序號"
						// + Util.trim(l161s01a
						// .getCntrNo())
						// + "紓困貸款類別「"
						// + Util.trim(l161s01a
						// .getRescueItem())
						// + "」，與最近一次已覆核動審表「"
						// + ELF383_RESCUEITEM + "」不同",
						// getClass());
						// }
						// }
						//
						// }
						//
						// }

					}

				}

				// J-109-0077_05097_B1015 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
				if (Util.equals(Util.trim(l161s01a.getIsRescue()), "Y")) {

					String rescueItem = Util.trim(l161s01a.getRescueItem());
					String rescueItemSub = Util.trim(l161s01a
							.getRescueItemSub());

					if (lmsService.isResueItemNeedEmpCount(rescueItem,
							rescueItemSub)) {
						BigDecimal empCount = null;
						empCount = l161s01a.getEmpCount();
						if (empCount == null) {
							throw new CapMessageException("額度序號"
									+ Util.trim(l161s01a.getCntrNo())
									+ "紓困貸款類別為「"
									+ Util.trim(l161s01a.getRescueItem())
									+ "」，欄位「現有員工人數」不得空白", getClass());
						} else {
							if (BigDecimal.ZERO.compareTo(empCount) > 0) {
								throw new CapMessageException("額度序號"
										+ Util.trim(l161s01a.getCntrNo())
										+ "紓困貸款類別為「"
										+ Util.trim(l161s01a.getRescueItem())
										+ "」，欄位「現有員工人數」必須大於或等於0", getClass());
							}
						}
					}

					// J-110-0288_05097_B1003 Web
					// e-Loan配合國發基金新創事業紓困加碼方案H01、A07專案修改
					// J-109-0077_05097_B1018 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業

					// A07 Y, Z 可以不輸入掛件文號
					// Y.符合經濟部補助要點適用對象 中小企業 不符合紓困4.0 利息補貼 A07 =>營收減少15%為N
					// Z.符合經濟部補助要點適用對象 大型企業 A07 =>營收減少15%為X
					if (lmsService.isResueItemNeedRescueNo(rescueItem,
							rescueItemSub)) {
						String rescueNo = Util.trim(l161s01a.getRescueNo());
						if (Util.equals(rescueNo, "")) {
							BigDecimal rescueRate = l161s01a.getRescueRate();
							if (lmsService
									.isResueItemNeedIsTurnoverDecreased(rescueItem)
									&& !Util.isEmpty(l161s01a
											.getIsTurnoverDecreased())) {

								// 洪真逢 10429/MEGABANK/MEGA (企金業務處)
								// 2021/08/13 上午 11:16
								// 建霖 您好
								// "國發基金紓困貸款"原先選擇 A07, 僅通知您 X 狀況需輸入掛件文號
								// 經洽經理銀行 , 麻煩您更改 X,Y,Z 狀況, 皆需 輸入 掛件文號

								// A07
								// if (Util.equals(Util.trim(l161s01a
								// .getIsTurnoverDecreased()), "Y")) {
								// // 符合紓困4.0 利息補貼 A07一定要有掛件文號
								//
								// // L160M01A.rescueNo=掛件文號
								// throw new CapMessageException("額度序號"
								// + Util.trim(l161s01a.getCntrNo())
								// + "紓困貸款類別為「"
								// + Util.trim(l161s01a
								// .getRescueItem())
								// + "」，欄位「掛件文號」不得空白", getClass());
								//
								// } else {
								// // A07 Y, Z 可以不輸入掛件文號
								// // Y.符合經濟部補助要點適用對象 中小企業 不符合紓困4.0
								// // 利息補貼 A07
								// // =>營收減少15%為N
								// // Z.符合經濟部補助要點適用對象 大型企業 A07
								// // =>營收減少15%為X
								// }

								// A07
								// L160M01A.rescueNo=掛件文號

								if (!lms1401Service
										.isRescueItemCanEmptyRescueNo(rescueItem)) {
									throw new CapMessageException("額度序號"
											+ Util.trim(l161s01a.getCntrNo())
											+ "紓困貸款類別為「"
											+ Util.trim(l161s01a
													.getRescueItem())
											+ "」，欄位「掛件文號」不得空白", getClass());
								}

							} else {
								// 非A07
								if (lmsService.isResueItemRescueRate(
										rescueItem, rescueItemSub)) {
									if (rescueRate != null
											&& rescueRate
													.compareTo(BigDecimal.ZERO) != 0) {
										// L160M01A.rescueNo=掛件文號

										if (!lms1401Service
												.isRescueItemCanEmptyRescueNo(rescueItem)) {
											throw new CapMessageException(
													"額度序號"
															+ Util.trim(l161s01a
																	.getCntrNo())
															+ "紓困貸款類別為「"
															+ Util.trim(l161s01a
																	.getRescueItem())
															+ "」，欄位「掛件文號」不得空白",
													getClass());
										}

									}
								} else {
									// L160M01A.rescueNo=掛件文號
									if (!lms1401Service
											.isRescueItemCanEmptyRescueNo(rescueItem)) {
										throw new CapMessageException("額度序號"
												+ Util.trim(l161s01a
														.getCntrNo())
												+ "紓困貸款類別為「"
												+ Util.trim(l161s01a
														.getRescueItem())
												+ "」，欄位「掛件文號」不得空白", getClass());
									}

								}

							}

						}
					}

					// J-112-0148_05097_B1002 Web
					// e-Loan企金授信新增經濟部協助中小型事業疫後振興專案貸款暨經濟部協助中小企業轉型發展專案貸款
					if (lms1601Service.isResueItemRescueSn(rescueItem)) {
						if (Util.isEmpty(Util.trim(l161s01a.getRescueSn()))) {
							throw new CapMessageException("額度序號"
									+ Util.trim(l161s01a.getCntrNo())
									+ "紓困貸款類別為「"
									+ Util.trim(l161s01a.getRescueItem())
									+ "」，欄位「額度編號」不得空白", getClass());
						}
					}
				}

				// J-111-0303_05097_B1001 Web e-Loan企金授信管理系統新增111年經濟部紓困方案
				// 檢核紓困代碼是否已經開放使用
				// if (Util.equals(Util.trim(l161s01a.getIsRescue()), "Y")) {
				// String errMsg = lms1201Service.chkRescueItemNotEffect(
				// Util.trim(l161s01a.getRescueItem()), "2");
				// if (Util.notEquals(errMsg, "")) {
				// throw new CapMessageException("額度序號"
				// + Util.trim(l161s01a.getCntrNo()) + errMsg,
				// getClass());
				// }
				// }

				ArrayList<String> itemsAll = new ArrayList<String>();
				List<L140M01C> l140m01cs = lms1401Service
						.findL140m01cListByMainId(l140m01a.getMainId());
				if (l140m01cs != null && !l140m01cs.isEmpty()) {
					for (L140M01C l140m01c : l140m01cs) {
						itemsAll.add(l140m01c.getLoanTP());
					}
				}

				// 判斷科目********************************************************************************
				// J-103-0202-005 Web e-Loan授信簽案衍生性金融商品遠匯與換匯科目，改以交易額度來簽案。
				if (Util.equals(l161s01a.getIsDerivatives(), "")) {

					Boolean hasDerivateSubjectFlag = false;

					if (l140m01cs != null && !l140m01cs.isEmpty()) {

						hasDerivateSubjectFlag = lmsService
								.hasDerivateSubject(itemsAll
										.toArray(new String[itemsAll.size()]));

						if (hasDerivateSubjectFlag == true) {
							errCntrNo1.append(Util.equals(
									errCntrNo1.toString(), "") ? "" : "、");
							errCntrNo1.append(l161s01a.getCntrNo());

						}

					} else {
						// 找不到額度明細表
						throw new CapMessageException(
								pop.getProperty("L160M01A.message66")
										+ l161s01a.getCntrNo(), getClass());
					}

				} else if (Util.equals(l161s01a.getIsDerivatives(),
						UtilConstants.DEFAULT.是)) {
					if (Util.equals(l161s01a.getDervApplyAmtType(), "")) {
						errCntrNo1
								.append(Util.equals(errCntrNo1.toString(), "") ? ""
										: "、");
						errCntrNo1.append(l161s01a.getCntrNo());

					}
				}

				// 1. "715","942","944","961","962","941","9411","9441"
				// "進出口、遠匯續約案件動用期限種類必須為 1 (YYY/MM/DD～YYY/MM/DD)"
				boolean hasCheckItem1 = false;
				for (String key : itemsAll) {
					if (asList1.contains(key)) {
						hasCheckItem1 = true;
					}
				}
				if (hasCheckItem1) {
					if (!"1".equals(l160m01a.getUseSelect())) {
						if (LMSUtil.isContainValue(
								Util.trim(l140m01a.getProPerty()),
								UtilConstants.Cntrdoc.Property.續約)) {
							errCntrNo2.append(Util.equals(
									errCntrNo2.toString(), "") ? "" : "、");
							errCntrNo2.append(l161s01a.getCntrNo());

						}
					}
				}

				HashMap<String, String> tempMap = new HashMap<String, String>();

				//J-112-0522 依金管會112年度專案檢查報告面請改善事項辦理，因營業單位漏未建檔報送金管會AI370聯合授信案基本資料(國內BTT-L556、海外AS400-3K30)，致本行每月報送報表產生報送錯誤，
				//擬將上述建檔機制移至E-LOAN-授信管理系統-動用審核表內，於動用審核表新增一頁籤(聯貸案基本資料)，營業單位於簽約動審時，須完成建檔作業。
				//相關報表 >> 額度動用資訊一覽表 >> 基本資訊 >> 額度控管種類為"30 - 聯貸"時，需檢核向下的攤貸額度序號都要存在於LNF277
				if(Util.equals(UtilConstants.Cntrdoc.snoKind.聯貸, l161s01a.getSnoKind())){
					String controlflag = Util.trim(lmsService.getSysParamDataValue("J-112-0522_CNTRNO_CHK"));
					if(Util.equals(UtilConstants.DEFAULT.是, controlflag)){
						Set<L140M01E_AF> l140m01e_afs = l140m01a.getL140m01e_af();
						if (l140m01e_afs != null && !l140m01e_afs.isEmpty()) {
							for (L140M01E_AF l140m01e_af : l140m01e_afs) {
								String cntrNo = l140m01e_af.getShareNo();
								if(Util.isNotEmpty(cntrNo)){
									int count277 = misdbBASEService.findLNF277ByCntrno(cntrNo);
									if (count277 == 0){
										throw new CapMessageException(
												MessageFormat.format(
														pop.getProperty("L140M01E_AF.message01"),
														new StringBuffer().append(l161s01a.getCntrNo()).toString()
														//new StringBuffer().append(cntrNo).toString()
														),
												this.getClass());
									}
								}
							}
						}
					}
				}
			}// for (L161S01A l161s01a : l161s01as)

			if (Util.notEquals(errCntrNo1.toString(), "")) {
				throw new CapMessageException(
						pop.getProperty("L160M01A.cntrInfo")
								+ errCntrNo1.toString()
								+ pop.getProperty("L160M01A.message75") + "：「"
								+ pop.getProperty("L160M01A.dervApplyAmtType")
								+ "」", getClass());
			}

			if (Util.notEquals(errCntrNo2.toString(), "")) {
				Properties pop140 = MessageBundleScriptCreator
						.getComponentResource(LMS1401S02Page.class);

				// L140M01a.message113=進出口、遠匯續約案件動用期限種類必須為1
				// (YYYY-MM-DD～YYYY-MM-DD)
				throw new CapMessageException(
						pop140.getProperty("L140M01a.message113") + "：「"
								+ errCntrNo2.toString() + "」", getClass());
			}

		}

		// 檢核是否登錄主從債務表
		List<L162S01A> l162m01as = (List<L162S01A>) lms1601Service
				.findListByMainId(L162S01A.class, mainId);
		if (l162m01as.isEmpty()) {
			// L160M01A.message28=「主從債務人資料表」，尚未登錄，不得呈主管覆核
			param.put("msg", pop.getProperty("L160M01A.message28"));
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤, param), getClass());
		}
		// 檢查主從人債務表是否必填欄位都有填寫
		HashMap<String, String> tempMap = new HashMap<String, String>();
		// 檢查債務人是否存在
		HashMap<String, String> getAllCustIdMap = new HashMap<String, String>();
		HashMap<String, String> cntrNoGuaNaExposureMap = new HashMap<String, String>();
		for (L162S01A l162s01a : l162m01as) {
			String cntrNo = Util.trim(l162s01a.getCntrNo());
			String showKey = StrUtils.concat(
					l162s01a.getCustId().toUpperCase(), " ", l162s01a
							.getDupNo().toUpperCase(), " ", l162s01a.getRId()
							.toUpperCase(), " ", l162s01a.getRDupNo()
							.toUpperCase());
			if (Util.isEmpty(cntrNo)) {
				// L160M01A.message63=「主從債務人資料表」額度序號不得為空!!
				param.put("msg", pop.getProperty("L160M01A.message63"));
				throw new CapMessageException(RespMsgHelper.getMessage(
						UtilConstants.AJAX_RSP_MSG.執行有誤, param), getClass());
			}
			getAllCustIdMap.put(l162s01a.getCustId() + l162s01a.getDupNo(), "");
			getAllCustIdMap.put(l162s01a.getRId() + l162s01a.getRDupNo(), "");
			if (Util.isEmpty(l162s01a.getRCountry())) {
				tempMap.put(showKey, l162s01a.getCntrNo().toUpperCase());
			}

			// 主債務人的檢查
			if (l162s01a.getCustId().toUpperCase()
					.equals(l162s01a.getRId().toUpperCase())
					&& l162s01a.getDupNo().toUpperCase()
							.equals(l162s01a.getRDupNo().toUpperCase())) {
			} else {
				if (Util.isEmpty(l162s01a.getRKindD())
						|| Util.isEmpty(l162s01a.getRKindM())) {
					tempMap.put(showKey, l162s01a.getCntrNo().toUpperCase());
				}

			}

			// J-109-0209_05097_B1001 e-Loan國內企金動審表增加借戶性質註記等進扣帳對象檢核項目
			// 主債務人的檢查
			if (l162s01a.getCustId().toUpperCase()
					.equals(l162s01a.getRId().toUpperCase())
					&& l162s01a.getDupNo().toUpperCase()
							.equals(l162s01a.getRDupNo().toUpperCase())) {

				boolean needChk = false;
				L161S01A l161s01a = lms1601Service.findL161m01aByMainIdCntrno(
						mainId, l162s01a.getCntrNo());
				if (l161s01a != null) {
					L140M01A l140m01a = lms1401Service
							.findL140m01aByMainId(l161s01a.getCntrMainId());
					if (l140m01a != null) {
						if (Util.equals(Util.trim(l161s01a.getIsRescue()), "Y")) {
							String isCbRefin = Util.trim(l161s01a
									.getIsCbRefin());
							if (Util.equals(isCbRefin, "Y")) {
								// 額度明細表的央行轉融通辦理類別
								String isSmallBuss = Util.trim(l140m01a
										.getIsSmallBuss());
								if (Util.equals(isSmallBuss, "C")) {
									needChk = true;
								}
							}
						}
					}
				}

				StringBuffer emptySoleData = new StringBuffer("");

				L164S01A l164s01a = lms1601Service.findL164s01aByMainIdCustId(
						mainId, Util.trim(l162s01a.getRId()),
						Util.trim(l162s01a.getRDupNo()));
				String isSole = "";
				if (needChk) {

					if (l164s01a == null) {

						// L164S01A.isSole=是否為我國獨資或合夥企業
						if (Util.equals(emptySoleData.toString(), "")) {
							emptySoleData.append(pop
									.getProperty("L164S01A.isSole"));
						} else {
							emptySoleData.append("、").append(
									pop.getProperty("L164S01A.isSole"));
						}

					} else {
						isSole = Util.trim(l164s01a.getIsSole());

						if (Util.equals(isSole, "")) {

							// L164S01A.isSole=是否為我國獨資或合夥企業
							if (Util.equals(emptySoleData.toString(), "")) {
								emptySoleData.append(pop
										.getProperty("L164S01A.isSole"));
							} else {
								emptySoleData.append("、").append(
										pop.getProperty("L164S01A.isSole"));
							}
						}
					}
				}

				// 既然有填Y，就要把剩下欄位填完
				if (Util.equals(isSole, "Y")) {
					String soleType = Util.trim(l164s01a.getSoleType());
					String hasRegis = Util.trim(l164s01a.getHasRegis());

					if (Util.equals(soleType, "")) {

						// L164S01A.soleType=企業類別
						if (Util.equals(emptySoleData.toString(), "")) {
							emptySoleData.append(pop
									.getProperty("L164S01A.soleType"));
						} else {
							emptySoleData.append("、").append(
									pop.getProperty("L164S01A.soleType"));
						}
					}

					if (Util.equals(hasRegis, "")) {
						// L164S01A.hasRegis=是否取得商業登記
						if (Util.equals(emptySoleData.toString(), "")) {
							emptySoleData.append(pop
									.getProperty("L164S01A.hasRegis"));
						} else {
							emptySoleData.append("、").append(
									pop.getProperty("L164S01A.hasRegis"));
						}
					}
				}

				if (Util.notEquals(emptySoleData.toString(), "")) {
					// J-109-0209_05097_B1001 e-Loan國內企金動審表增加借戶性質註記等進扣帳對象檢核項目
					// L160M01A.message108=「主從債務人資料表」，主借款人「{0}」額度序號「{1}」之資料不齊全「{2}」。
					String msg = MessageFormat.format(
							pop.getProperty("L160M01A.message108"),
							l162s01a.getCustId().toUpperCase() + ""
									+ Util.trim(l162s01a.getRName()), cntrNo,
							emptySoleData.toString());
					param.put("msg", msg);
					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, param),
							getClass());

				}

			}

			// J-110-0040_05097_B1001 Web
			// e-Loan增加「本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)」註記
			String guaNaExposure = Util.trim(l162s01a.getGuaNaExposure());
			String rType = Util.trim(l162s01a.getRType());
			if (Util.notEquals(rType, "C") && Util.notEquals(rType, "S")) {

				String guarantorPriorityOn = Util.trim(lmsService
						.getSysParamDataValue("LMS_GUARANTOR_PRIORITY_ON"));

				if (Util.equals(guarantorPriorityOn, "Y")) {

					// 檢查欄位不得空白
					if (Util.equals(guaNaExposure, "")) {
						throw new CapMessageException(MessageFormat.format(
								pop.getProperty("L162M01A.error03"),
								l162s01a.getRId() + " "
										+ Util.trim(l162s01a.getRName()) + " "
										+ Util.trim(l162s01a.getCntrNo()),
								pop.getProperty("L162M01A.guaNaExposure")),
								getClass());
					}

					// 檢查同一額度序號選的結果要一樣
					if (cntrNoGuaNaExposureMap.containsKey(Util.trim(l162s01a
							.getCntrNo()))) {
						if (Util.notEquals(
								guaNaExposure,
								MapUtils.getString(cntrNoGuaNaExposureMap,
										Util.trim(l162s01a.getCntrNo())))) {
							// L162M01A.error04=「{0}」欄位「{1}」同一額度序號下所有借款人選擇必須一致
							throw new CapMessageException(MessageFormat.format(
									pop.getProperty("L162M01A.error04"),
									l162s01a.getRId() + " "
											+ Util.trim(l162s01a.getRName())
											+ " "
											+ Util.trim(l162s01a.getCntrNo()),
									pop.getProperty("L162M01A.guaNaExposure")),
									getClass());
						}
					} else {
						cntrNoGuaNaExposureMap.put(
								Util.trim(l162s01a.getCntrNo()), guaNaExposure);
					}
				}

			}

		}

		// BGN J-106-0029-004
		// 洗錢防制-動審表新增洗錢防制頁籤************************************************
		// 檢查實質受益人與負責人

		// 主要借款人
		LinkedHashMap<String, String> cntrNoMainBorrowerMap = new LinkedHashMap<String, String>();

		// 主要借款人+共同借款人
		LinkedHashMap<String, String> allBorrowerIdMap = new LinkedHashMap<String, String>();
		Set<L160M01B> l160m01bs = l160m01a.getL160m01b();
		if (l160m01bs != null && !l160m01bs.isEmpty()) {
			for (L160M01B l160m01b : l160m01bs) {
				L140M01A l140m01a = amlRelateService.findModelByMainId(
						L140M01A.class, l160m01b.getReMainId());
				if (l140m01a != null) {
					String chkId = Util.trim(l140m01a.getCustId());
					String chkDupNo = Util.trim(l140m01a.getDupNo());
					if (Util.notEquals(chkId, "")
							&& Util.notEquals(chkDupNo, "")) {
						if (!cntrNoMainBorrowerMap.containsKey(l160m01b
								.getCntrNo())) {
							cntrNoMainBorrowerMap.put(l160m01b.getCntrNo(),
									chkId + "-" + chkDupNo);
						}

						if (!allBorrowerIdMap.containsKey(chkId + "-"
								+ chkDupNo)) {
							allBorrowerIdMap.put(chkId + "-" + chkDupNo,
									l140m01a.getCustName());
						}

					}
				}
			}
		}
		for (L162S01A l162s01a : l162m01as) {
			if (Util.equals(l162s01a.getRType(), UtilConstants.lngeFlag.共同借款人)) {
				String chkId = Util.trim(l162s01a.getRId());
				String chkDupNo = Util.trim(l162s01a.getRDupNo());
				if (Util.notEquals(chkId, "") && Util.notEquals(chkDupNo, "")) {
					if (!allBorrowerIdMap.containsKey(chkId + "-" + chkDupNo)) {
						allBorrowerIdMap.put(chkId + "-" + chkDupNo,
								l162s01a.getRName());
					}
				}
			}
		}

		if (cntrNoMainBorrowerMap != null && !cntrNoMainBorrowerMap.isEmpty()) {
			// 檢查本次動用之額度與主借款人是否未列於相關報表->主從債務人資料表中。
			String errMsg = amlRelateService.chkMainBorrowerInL162S01A(mainId,
					cntrNoMainBorrowerMap);
			if (Util.notEquals(errMsg, "")) {
				// AML.error009=本次動用之額度與主借款人{0}未列於相關報表->主從債務人資料表中。
				throw new CapMessageException(errMsg, getClass());
			}

		}

		if (allBorrowerIdMap != null && !allBorrowerIdMap.isEmpty()) {
			// 檢查實質受益人欄未有無輸入 + 有沒有完成身分確認
			String errMsg = amlRelateService.chkBeneficiaryIsOkForDrawDown(
					mainId, allBorrowerIdMap);
			if (Util.notEquals(errMsg, "")) {
				// AML.error008=借款人/共同借款人{0}於相關報表->主從債務人資料表中尚有洗錢防制所需欄位未完成輸入。
				throw new CapMessageException(errMsg, getClass());
			}

			// J-107-0070-001 Web e-Loan
			// 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
			// 檢查高階管理人員
			String errMsgSeniorMgr = amlRelateService
					.chkSeniorMgrIsOkForDrawDown(mainId, allBorrowerIdMap);
			if (Util.notEquals(errMsgSeniorMgr, "")) {
				// AML.error023=「{0}」借款人0024-23無「高階管理人員」資料
				// AML.error024=「{0}」借款人0024-23有「高階管理人員」資料，e-Loan借款人基本資料「高階管理人員」不得為「無」
				throw new CapMessageException(errMsgSeniorMgr, getClass());
			}

			// J-108-0039_05097_B1001 Web e-Loan
			// 國內企金授信系統簽報、動審AML頁籤將借戶之「具控制權人」納入應查詢比對黑名單之對象。
			// 檢查具控制權人
			String errMsgCtrlPeo = amlRelateService.chkCtrlPeoIsOkForDrawDown(
					mainId, allBorrowerIdMap);
			if (Util.notEquals(errMsgCtrlPeo, "")) {
				// AML.error028=「{0}」借款人0024-23無「具控制權人」資料
				// AML.error029=「{0}」借款人0024-23有「具控制權人」資料，e-Loan借款人基本資料「具控制權人」不得為「無」
				throw new CapMessageException(errMsgCtrlPeo, getClass());
			}

		}

		// J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
		// 檢查黑名單
		amlRelateService.chkBlackListFullExitForDrawDown(mainId, true);

		// J-106-0238-001
		// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
		// 檢核簽報書調查狀態是否已經完成可以送呈主管
		// lmsL120M01A.error036=簽報書AML/CFT頁籤之「黑名單案件調查結果」尚未完成，請先執行「取得黑名單查詢結果」按鈕。
		amlRelateService.chkNcResultFinishCanSendBoss(mainId);

		// J-106-0238-001
		// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
		// 檢核借款人是否為0024拒絕交易名單
		// 檢核0024有拒絕交易且額度明細表有新作時可否送呈
		// lmsL120M01A.error035=請注意! 下列借款人於0024客戶中文檔為拒絕交易狀態!
		amlRelateService.chk0024RejecjTrancCanNotSendBoss(mainId);

		// END J-106-0238-001
		
		// J-113-0082 配合法務部新規，於AML頁籤新增引入「受告誡處分」資訊
		// 若有借戶身分但沒有「受告誡處分」引入結果要擋
		// 會出現以下訊息
		// L120S09a.cmfwarnpResult.error=AML頁籤尚未執行告誡戶掃描
		amlRelateService.checkCmfwarnpNeed(mainId);
		// ********************************************************************************************************

		// END J-106-0029-004
		// 洗錢防制-動審表新增洗錢防制頁籤************************************************

		Map<String, Object> custData = null;
		// J-103-0299-001
		// Web e-Loan企金額度明細表保證人新增保證比例
		Map<String, String> custBusCd = new HashMap<String, String>();

		// 檢查債務人是否都有建檔
		StringBuffer temp0024 = new StringBuffer();
		for (String key : getAllCustIdMap.keySet()) {
			String custId = key.substring(0, key.length() - 1);
			String dupNo = key.substring(key.length() - 1, key.length());

			custData = misCustdataService.findAllByByCustIdAndDupNo(custId,
					dupNo);
			if (custData == null || custData.isEmpty()) {
				temp0024.append(temp0024.length() > 0 ? "" : "");
				temp0024.append(custId + " " + dupNo);
			} else {
				// J-103-0299-001
				// Web e-Loan企金額度明細表保證人新增保證比例
				custBusCd.put(Util.trim(custId) + Util.trim(dupNo), custData
						.get("BUSCD") != null ? custData.get("BUSCD")
						.toString() : "999999");
			}
		}
		if (temp0024.length() > 0) {
			// EFD3009=ERROR|$\{custId\}客戶中文檔0024 無此借款人資料 ！！|
			param.put("custId", temp0024.toString());
			throw new CapMessageException(RespMsgHelper.getMessage("EFD3009", param), getClass());
		}
		StringBuffer temp = new StringBuffer();
		if (!tempMap.isEmpty()) {
			for (String key : tempMap.keySet()) {
				temp.append("<br/>");
				temp.append(key);
				temp.append(" ");
				temp.append(tempMap.get(key));
			}
			// L160M01A.message51=「主從債務人資料表」以下借款人，資料不完整，不得呈主管覆核{0}
			param.put("msg", MessageFormat.format(
					pop.getProperty("L160M01A.message51"), temp.toString()));
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤, param), getClass());
		}

		// J-111-0220 高齡客戶關懷檢核表
		// 若主從債務人有超過65歲
		// 放在這裡代表主從債務人資料都齊全且都查過0024了
		String isCheck65On = sysParameterService
				.getParamValue("J_111_0220_CHECK_65");
		String canPassCaseType = sysParameterService
				.getParamValue("J_111_0220_CASETYPE");
		List<String> canPassCaseTypeList = Arrays.asList(canPassCaseType
				.split(","));
		if (Util.equals("Y", isCheck65On)) {
			// 來源簽報書
			L120M01A l120m01aForL120s01r = lms1201Service
					.findL120m01aByMainId(l160m01a.getSrcMainId());
			String miniFlag = Util.trim(l120m01aForL120s01r.getMiniFlag());
			String caseType = Util.trim(l120m01aForL120s01r.getCaseType());
			// 金襄理:除了"LIBOR退場變更利率條件簡易簽報"外, 其他都要檢核!!
			// 簡易簽報目前也要檢核，先挖洞怕之後要略過檢核
			// if (!lmsService.isLiborExitCase(l120m01aForL120s01r) &&
			// !(Util.equals("Y", Util.trim(miniFlag)) &&
			// canPassCaseTypeList.contains(caseType)) ){

			// J-111-0423_05097_B1001 Web
			// e-Loan企金授信就海外分行承做永續績效連結授信案(如附件)，於E-Loan「永續績效連結授信」相關註記
			// 2022.08.29 金襄理:來源簽報書如果是海外分行的，我們就pass高齡客戶的檢核
			if (!(lmsService.isLiborExitCase(l120m01aForL120s01r)
					|| lmsService.isEuroyenTiborExitCase(l120m01aForL120s01r))
					&& !(Util.equals("Y", Util.trim(miniFlag)) && canPassCaseTypeList
							.contains(caseType))
					&& !UtilConstants.Casedoc.typCd.海外.equals(Util
							.trim(l120m01aForL120s01r.getTypCd()))) {

				Properties popLmsS07APage06 = MessageBundleScriptCreator
						.getComponentResource(LMSS07APage06.class);
				StringBuilder noL120s01r = new StringBuilder();// 放沒有填寫關懷主檔的借款人
				StringBuilder rejL120s01r = new StringBuilder();// 有關懷主檔的借款人，但勾選了應予婉拒
				for (L162S01A l162s01a : l162m01as) {
					String rId = Util.trim(l162s01a.getRId());
					String rDupNo = Util.trim(l162s01a.getRDupNo());
					String rName = Util.trim(l162s01a.getRName());

					// 0024個人資料
					Map<String, Object> custInfo = misCustdataService
							.findAllByByCustIdAndDupNo(rId, rDupNo);
					// 1.先判斷是否為個人戶
					String busCode = Util.trim(MapUtils.getString(custInfo,
							"BUSCD"));
					if (LMSUtil.isBusCode_060000_130300(busCode)) {
						// 2.判斷是否>=65歲
						String birthDt = Util.trim(MapUtils.getString(custInfo,
								"BIRTHDT", ""));
						// 2022.09.07 金襄:沒有建生日就by pass不檢核
						if (Util.notEquals(birthDt, "")
								&& Util.notEquals(birthDt, "0001-01-01")) {
							Date birthDate = CapDate.getDate(birthDt,
									"yyyy-MM-dd");
							int age = OverSeaUtil.getAge(birthDate);
							String checkAgeSys = sysParameterService
									.getParamValue("J_111_0220_CHECK_65_AGE");
							int checkAge = Util.isEmpty(checkAgeSys) ? 65
									: new Integer(checkAgeSys);
							// 2023.05.22 授審處陳眉如:要含65歲
							if (age >= checkAge) {
								// 3.判斷是否有填寫關懷主檔
								L120S01R l120s01rBigger65 = lmsService
										.findL120s01rByMainIdAndCustId(
												l120m01aForL120s01r.getMainId(),
												rId, rDupNo);
								if (l120s01rBigger65 == null) {
									// 根本沒填寫高齡關懷主檔
									noL120s01r
											.append(noL120s01r.length() > 0 ? "、"
													: "");
									noL120s01r.append(rId + " " + rName);
								}

								// 2022.08.09 企金拿掉最下面經判斷評估...那一列
								// 只檢核是否有填寫，沒有評估後核准、婉拒的情況

								// }else{
								// 有填寫
								// if(Util.notEquals("0",
								// l120s01rBigger65.getResult())){
								// 但沒有選擇0可依徵審流程辦理，可能選了應予婉拒or沒選
								// rejL120s01r.append(rejL120s01r.length() > 0 ?
								// "、" : "");
								// rejL120s01r.append(rId + " " + rName);
								// }
							}
						}
					}

				}

				String messageError2 = "";
				String messageError3 = "";
				if (Util.isNotEmpty(noL120s01r)) {
					// L1205S07Page06.error2=借款人{0}尚未填寫高齡客戶關懷檢核表
					// L1205S07Page06.error4=請至簽報書 {0}
					// 綜合評估(簽報書-一般)、說明(簽報書-其他)頁籤之「高齡客戶關懷檢核表」頁籤進行維護
					messageError2 = MessageFormat.format(popLmsS07APage06
							.getProperty("L1205S07Page06.error2"), noL120s01r
							.toString())
							+ "<br/>"
							+ MessageFormat.format(popLmsS07APage06
									.getProperty("L1205S07Page06.error4"),
									l120m01aForL120s01r.getCaseNo());
				}
				if (Util.isNotEmpty(rejL120s01r)) {
					// L1205S07Page06.error3=借款人{0}於高齡客戶關懷檢核表中未勾選「可依徵審流程辦理」
					// L1205S07Page06.error4=請至簽報書 {0}
					// 綜合評估(簽報書-一般)、說明(簽報書-其他)頁籤之「高齡客戶關懷檢核表」頁籤進行維護
					messageError3 = MessageFormat.format(popLmsS07APage06
							.getProperty("L1205S07Page06.error3"), rejL120s01r
							.toString())
							+ "<br/>"
							+ MessageFormat.format(popLmsS07APage06
									.getProperty("L1205S07Page06.error4"),
									l120m01aForL120s01r.getCaseNo());
				}

				if (Util.isNotEmpty(messageError2)
						|| Util.isNotEmpty(messageError2)) {
					String messageBr = Util.isEmpty(messageError2) ? ""
							: "<br/><br/>";
					throw new CapMessageException(messageError2 + messageBr
							+ messageError3, getClass());
				}
			}
		}

		// J-103-0299-001
		// Web e-Loan企金額度明細表保證人新增保證比例
		// J-105-0100-001 Web e-Loan授信管理系統企金案件額度明細表之自然人保證人保證比例欄位開放可自行輸入
		// StringBuffer guaPercentErr = new StringBuffer("");
		// for (L162S01A l162s01a : l162m01as) {
		// String rType = l162s01a.getRType();
		// String rCustId = Util.trim(l162s01a.getRId());
		// String rDupNo = Util.trim(l162s01a.getRDupNo());
		// String rName = Util.trim(l162s01a.getRName());
		// String tKey = rCustId + rDupNo;
		// if (Util.notEquals(rType, UtilConstants.lngeFlag.共同借款人)
		// && Util.notEquals(rType, UtilConstants.lngeFlag.擔保品提供人)) {
		// if (!custBusCd.isEmpty()) {
		// if (custBusCd.containsKey(tKey)) {
		// String busCd = custBusCd.get(tKey) != null ? custBusCd
		// .get(tKey).toString() : "999999";
		// if (LMSUtil.isBusCode_060000_130300(busCd)) {
		// BigDecimal guaPercent = l162s01a.getGuaPercent();
		// if (!Util.isEmpty(guaPercent)) {
		// if (guaPercent.compareTo(BigDecimal
		// .valueOf(100)) != 0) {
		// guaPercentErr.append(Util.equals(
		// guaPercentErr.toString(), "") ? ""
		// : "、");
		// guaPercentErr.append(rCustId + "-" + rName
		// + "(" + l162s01a.getCntrNo() + ")");
		// }
		// }
		// }
		// }
		// }
		// }
		//
		// }
		// if (guaPercentErr.length() > 0) {
		// // L162M01A.error02=「{0}」非法人戶之保證人負?保證責任比率欄位必須為100
		// // 為了可以換行呈現 在最前面加上<br> tag
		// guaPercentErr.insert(0, "<br/>");
		// param.put(
		// "msg",
		// " "
		// + MessageFormat.format(
		// pop.getProperty("L162M01A.error02"),
		// guaPercentErr.toString()));
		// throw new CapMessageException(RespMsgHelper.getMessage(parent,
		// UtilConstants.AJAX_RSP_MSG.執行有誤, param), getClass());
		//
		// }

		/**
		 * J-101-0104
		 * 
		 * <pre>
		 * 檢查
		 * 動審表增加五、其他事項，下列查核項目應辦而未完成，不得呈主管覆核之檢核。
		 * 1.核准敘做文件；
		 * 3.額度本票；
		 * 10.身分證遺失檢查；
		 * 11.查詢司法院網站借款人及保證人是否為受監護或受輔助宣告資料	；
		 * 12.恐怖份子黑名單查詢紀錄；
		 * 13.銀行法及金控法利害關係人查詢紀錄(簽約時須再查一次)；
		 * 14.同一關係企業/集團企業建檔維護；
		 * 15.應收帳款承購無追索權—買方；銀行法及金控法利害關係人查詢紀錄；
		 * 17.核貸條件其他應辦事項之土地信託或地上權設定；
		 * 22.擔保品設押(質)。
		 * 
		 * </pre>
		 */

		boolean loseNonSMEProjAffidavit = false; // J-105-0135-001 Web
													// e-Loan國內企金授信系統動審表，開放可修改振興經濟非中小企業專案貸款註記與金額。
		StringBuffer reguiredItem = new StringBuffer();
		String b29B33InquiryLogMsg = "";

		for (L160M01C l160m01c : l160m01cList) {
			int seq = l160m01c.getItemSeq();
			if (UtilConstants.Usedoc.itemType.國內企金.equals(l160m01c
					.getItemType())) {
				if (this.isRequireItem(seq, l160m01a)
						&& UtilConstants.Usedoc.checkItem.未收.equals(l160m01c
								.getItemCheck())) {
					reguiredItem.append(reguiredItem.length() > 0 ? "<br/>"
							: "");
					reguiredItem.append(seq);
					reguiredItem.append(".");
					reguiredItem.append(l160m01c.getItemContent());
				}

				// J-105-0135-001 Web e-Loan國內企金授信系統動審表，開放可修改振興經濟非中小企業專案貸款註記與金額。
				// J-108-0098-001 企金處移除振興經濟非中小企相關欄位
				// if (seq == 27) {
				// if (!UtilConstants.Usedoc.checkItem.已收.equals(l160m01c
				// .getItemCheck())) {
				// if (Util.notEquals(nonSMEProjCntrNo.toString(), "")) {
				// loseNonSMEProjAffidavit = true;
				// }
				// }
				// }

			}

			if (seq == 31) {
				// 查無今日查詢聯徵中心B29及B33紀錄
				b29B33InquiryLogMsg = this.clsService
						.checkIsEjcicB29AndB33InquiryLogForLms(new ArrayList<L160M01B>(
								l160m01a.getL160m01b()));
			}
		}
		if (reguiredItem.length() > 0) {
			// L160M01A.message53=五、其他事項之下列項目未收到(未辦妥)，不得呈主管覆核:{0}
			// 為了可以換行呈現 在最前面加上<br> tag
			reguiredItem.insert(0, "<br/>");
			param.put(
					"msg",
					" "
							+ MessageFormat.format(
									pop.getProperty("L160M01A.message53"),
									reguiredItem.toString()));
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤, param), getClass());

		}

		if (!"".equals(b29B33InquiryLogMsg)) {
			throw new CapMessageException(
					RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, b29B33InquiryLogMsg), getClass());
		}

		// J-105-0135-001 Web e-Loan國內企金授信系統動審表，開放可修改振興經濟非中小企業專案貸款註記與金額。
		// J-108-0098-001 企金處移除振興經濟非中小企相關欄位
		// if (loseNonSMEProjAffidavit) {
		// //
		// L160M01A.message103=額度序號「{0}」屬振興經濟非中小企業專案貸款，其他事項第27項「振興經濟非中小企業專案貸款切結書」必須為「V」。
		// param.put(
		// "msg",
		// " "
		// + MessageFormat.format(
		// pop.getProperty("L160M01A.message103"),
		// nonSMEProjCntrNo.toString()));
		// throw new CapMessageException(RespMsgHelper.getMessage(parent,
		// UtilConstants.AJAX_RSP_MSG.執行有誤, param), getClass());
		// }

		// 先使用參數設定來決定是否要執行模擬動審，免得有其它問題
		String checkSimuTrial = sysParameterService.getParamValue("SIMU_TRIAL");
		String checkSimuTrialDate = sysParameterService
				.getParamValue("SIMU_TRIAL_DATE");

		if ("Y".equals(checkSimuTrial)) {
			L120M01A l120m01a = lms1201Service.findL120m01aByMainId(l160m01a
					.getSrcMainId());
			Date endDate = l120m01a.getEndDate();
			// 簽報書核準日期要大於等於2017-09-01，才檢核要做模擬動審
			if (endDate == null
					|| endDate.compareTo(CapDate.parseDate(Util
							.trim(checkSimuTrialDate))) >= 0) {
				for (L160M01B l160m01b : l160m01bs) {
					String cntrNo = l160m01b.getCntrNo();
					L140M01A l140m01a = lms1401Service
							.findL140m01aByMainId(l160m01b.getReMainId());
					String proPerty = l140m01a.getProPerty();
					if (UtilConstants.Cntrdoc.Property.不變.equals(proPerty)
							|| UtilConstants.Cntrdoc.Property.取消
									.equals(proPerty)) {
						continue;
					}

					// J-109-0019_05097_B1001 Web e-Loan企金授信出口押匯案免辦理模擬動審
					// 942-出口押匯
					boolean has942 = false;
					for (L140M01C l140m01c : l140m01a.getL140m01c()) {
						if (Util.equals(l140m01c.getLoanTP(), "942")) {
							has942 = true;
							break;
						}
					}
					if (has942) {
						continue;
					}

					// 判斷紓困案，免模擬動審
					boolean hasRescue = false;
					if (!l161s01as.isEmpty()) {
						for (L161S01A l161s01a : l161s01as) {
							if (Util.equals(Util.trim(l161s01a.getCntrNo()),
									cntrNo)) {
								if (Util.equals(
										Util.trim(l161s01a.getIsRescue()), "Y")) {
									hasRescue = true;
									break;
								}
							}
						}
					}
					if (hasRescue) {
						continue;
					}

					List<L250M01A> l250m01as = lmsService
							.getSimuTrialDataBeforePay(l160m01a.getSrcMainId(),
									cntrNo);
					if (l250m01as == null || l250m01as.isEmpty()) {
						throw new CapMessageException("額度序號"
								+ l140m01a.getCntrNo() + "，動審表尚未執行模擬動審",
								this.getClass());
					}
				}
			}
		}

		// J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
		// 檢核保證人順位是否有缺漏
		// 借款人為企業戶且保證人(一般/連帶)亦為企業戶時，需要填列保證人信用品質順序。

		String guarantorPriorityOn = Util.trim(lmsService
				.getSysParamDataValue("LMS_GUARANTOR_PRIORITY_ON"));
		if (Util.equals(guarantorPriorityOn, "Y")) {
			l161s01as = l160m01a.getL161S01A();
			if (!l161s01as.isEmpty()) {
				for (L161S01A l161s01a : l161s01as) {
					String cntrNo = Util.trim(l161s01a.getCntrNo());
					String mainCustId = Util.trim(l161s01a.getCustId());
					String mainDupNo = Util.trim(l161s01a.getDupNo());
					// 借款人是企業戶才要檢核

					// J-110-0007_05097_B1003 Web
					// e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
					// Map<String, Object> custDataMap = misCustdataService
					// .findAllByByCustIdAndDupNo(mainCustId, mainDupNo);
					//
					// if (custDataMap != null && !custDataMap.isEmpty()) {
					// // 保證人是個人戶就不要
					// if (Util.equals(custDataMap.get("BUSCD"), "130300")
					// || Util.equals(custDataMap.get("BUSCD"),
					// "060000")) {
					// continue;
					// }
					// }

					List<L162S01A> l162s01as = lms1601Service
							.findL162s01aByMainIdCntrno(mainId, cntrNo);
					// 取得必要順序的保證人
					// 再刪選需要的保證人(保證人企業戶且為)
					List<L162S01A> mustL162s01as = lms1601Service
							.findL162s01aNeedPriority(l162s01as, cntrNo);

					if (mustL162s01as != null && !mustL162s01as.isEmpty()) {

						// 檢查保證比例100%前是否都有信用品質順序
						BigDecimal totGuaPercent = BigDecimal.ZERO;
						int setCount = 0;
						for (L162S01A l162s01a : mustL162s01as) {

							if (l162s01a.getPriority() != null
									&& l162s01a.getPriority().compareTo(
											BigDecimal.ZERO) > 0) {

								BigDecimal guaPercent = l162s01a
										.getGuaPercent() == null ? BigDecimal.ZERO
										: l162s01a.getGuaPercent();
								totGuaPercent = totGuaPercent.add(guaPercent);
								setCount = setCount + 1;

							}

						}

						if (mustL162s01as.size() > setCount
								&& totGuaPercent.compareTo(Util
										.parseBigDecimal("100")) < 0) {
							// L140M01a.message262=保證人必需設定信用品質順序，直到該額度之保證人之負?保證責任比率合計達100%。
							Properties prop = MessageBundleScriptCreator
									.getComponentResource(LMS1401S02Panel.class);
							String msg = "「" + cntrNo + "」"
									+ prop.getProperty("L140M01a.message262");
							throw new CapMessageException(
									RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, msg), getClass());
						}
					}

				}
			}
		}

		// J-110-0209_05097_B1001 Web e-Loan國內企金簽報書額度明細表中增列「創兆貸」專案，並產生統計報表案
		// 檢核動審表新舊紓困案是否可以放行(ALOAN 7/20 計息才會OK)
		String LMS_RESCUEITEM_DRAW_ONLYCAN = Util.trim(lmsService
				.getSysParamDataValue("LMS_RESCUEITEM_DRAW_ONLYCAN"));
		if (Util.notEquals(LMS_RESCUEITEM_DRAW_ONLYCAN, "")) {

			for (L160M01B l160m01b : l160m01a.getL160m01b()) {

				String cntrNo = l160m01b.getCntrNo();

				L140M01A l140m01a = lms1401Service
						.findL140m01aByMainId(l160m01b.getReMainId());

				if (l140m01a != null) {

					L161S01A l161s01a = lms1601Service
							.findL161m01aByMainIdCntrno(l160m01a.getMainId(),
									cntrNo);

					// 這次的動審表額度動用資訊
					String isRescue = Util.trim(l161s01a.getIsRescue());
					String rescueItem = Util.trim(l161s01a.getRescueItem());

					// 簽報書額度明細表的前次是否為109年嚴重肺炎......................
					String isRescueOn = Util.trim(l140m01a.getIsRescueOn());
					String rescueItemOn = Util.trim(l140m01a.getRescueItemOn());
					String cntrNoOn = Util.trim(l140m01a.getCntrNoOn());

					if (Util.equals(isRescue, "Y")
							&& Util.equals(isRescueOn, "Y")) {

						// J-111-0112_05097_B1002 Web
						// e-Loan企金授信管理系統新增111年經濟部紓困方案
						// A04 A05 A06
						if ((lmsService.isResueItemSubSidy(rescueItem, "110")
								&& lmsService.isResueItemSubSidy(rescueItemOn,
										"109") || (lmsService
								.isResueItemSubSidy(rescueItem, "111") && (lmsService
								.isResueItemSubSidy(rescueItemOn, "109") || lmsService
								.isResueItemSubSidy(rescueItemOn, "110"))))) {
							// 只能變更為
							String onlyCan = LMS_RESCUEITEM_DRAW_ONLYCAN;

							if (Util.notEquals(onlyCan, "")) {
								JSONObject jsonOnlyCan = JSONObject
										.fromObject("{" + onlyCan + "}");
								if (jsonOnlyCan != null) {
									String onlyCanItem = Util.trim(jsonOnlyCan
											.get(rescueItemOn));
									if (Util.isNotEmpty(onlyCanItem)) {
										boolean ocPass = false;
										String[] onlyCanArr = StringUtils
												.split(onlyCanItem, "|");
										for (String ocItem : onlyCanArr) {
											if (Util.equals(rescueItem, ocItem)) {
												ocPass = true;
												break;
											}
										}
										if (!ocPass) {

											Map<String, String> lms140_rescueItem = codeTypeService
													.findByCodeType(
															"lms140_rescueItem",
															LMSUtil.getLocale()
																	.toString());

											String msg = "本次動用之額度序號「"
													+ cntrNo
													+ "」中心帳務系統尚未開放可敘作「"
													+ rescueItemOn
													+ "."
													+ lms140_rescueItem
															.get(rescueItemOn)
													+ "」轉 「 "
													+ rescueItem
													+ "."
													+ lms140_rescueItem
															.get(rescueItem)
													+ "」";

											throw new CapMessageException(
													RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, msg),
													getClass());
										}
									}
								}
							}
						}
					}
				}
			}

		}

		// J-110-0288_05097_B1001 Web
		// e-Loan配合辦理「行政院國家發展基金協助新創事業紓困融資加碼方案」，修改額度明細表欄位
		// 檢核借款人項下不得同時並存紓困代碼類別
		String LMS_RESCUEITEM_NOT_EXIST_SAME = Util.trim(lmsService
				.getSysParamDataValue("LMS_RESCUEITEM_NOT_EXIST_SAME"));
		if (Util.notEquals(LMS_RESCUEITEM_NOT_EXIST_SAME, "")) {
			for (L160M01B l160m01b : l160m01a.getL160m01b()) {
				String cntrNo = l160m01b.getCntrNo();

				L140M01A l140m01a = lms1401Service
						.findL140m01aByMainId(l160m01b.getReMainId());

				if (l140m01a != null) {

					L161S01A l161s01a = lms1601Service
							.findL161m01aByMainIdCntrno(l160m01a.getMainId(),
									cntrNo);

					// 這次的動審表額度動用資訊
					String isRescue = Util.trim(l161s01a.getIsRescue());
					String rescueItem = Util.trim(l161s01a.getRescueItem());

					if (Util.equals(isRescue, "Y")) {
						// LMS_RESCUEITEM_NOT_EXIST_SAME
						// "A07":"H01|XXX","H01":"A07|XXX" J-110-0258
						// 借款人項下不得同時並存紓困代碼類別
						String notSameExist = LMS_RESCUEITEM_NOT_EXIST_SAME;

						if (Util.notEquals(notSameExist, "")) {

							JSONObject jsonNotSame = JSONObject.fromObject("{"
									+ notSameExist + "}");
							if (jsonNotSame != null) {
								String notSameItem = Util.trim(jsonNotSame
										.get(rescueItem));
								if (Util.isNotEmpty(notSameItem)) {

									String[] notSameArr = StringUtils.split(
											notSameItem, "|");

									// 判斷同一動審表******************************************************************************
									List<Map<String, Object>> l161s01aRescueItemNotSameListR = eloandbBASEService
											.findL161s01aAllRescueDataWithRescueItemByCustIdAndMainId(
													Util.trim(l161s01a
															.getMainId()),
													l140m01a.getCustId(),
													l140m01a.getDupNo(),
													notSameArr);
									if (l161s01aRescueItemNotSameListR != null
											&& !l161s01aRescueItemNotSameListR
													.isEmpty()) {
										for (Map<String, Object> l161s01aRescueItemNotSameMapR : l161s01aRescueItemNotSameListR) {
											String RESCUEITEM_L161 = Util
													.trim(MapUtils
															.getString(
																	l161s01aRescueItemNotSameMapR,
																	"RESCUEITEM"));
											String CNTRNO_L161 = Util
													.trim(MapUtils
															.getString(
																	l161s01aRescueItemNotSameMapR,
																	"CNTRNO"));

											Map<String, String> lms140_rescueItem = codeTypeService
													.findByCodeType(
															"lms140_rescueItem",
															LMSUtil.getLocale()
																	.toString());

											String msg = "本次動用之額度序號「"
													+ cntrNo
													+ "」紓困代碼「"
													+ rescueItem
													+ "."
													+ lms140_rescueItem
															.get(rescueItem)
													+ "」不得與額度序號「"
													+ CNTRNO_L161
													+ "「 "
													+ RESCUEITEM_L161
													+ "."
													+ lms140_rescueItem
															.get(RESCUEITEM_L161)
													+ "」並存。";

											throw new CapMessageException(
													RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, msg),
													getClass());

										}
									}

									// 判斷已動審******************************************************************************
									List<Map<String, Object>> l161s01aRescueItemNotSameList = eloandbBASEService
											.findL161s01aAllRescueDataWithRescueItemByCustId(
													l140m01a.getCustId(),
													l140m01a.getDupNo(),
													notSameArr);

									if (l161s01aRescueItemNotSameList != null
											&& !l161s01aRescueItemNotSameList
													.isEmpty()) {
										for (Map<String, Object> l161s01aRescueItemNotSameMap : l161s01aRescueItemNotSameList) {
											String RESCUEITEM_L161 = Util
													.trim(MapUtils
															.getString(
																	l161s01aRescueItemNotSameMap,
																	"RESCUEITEM"));
											String CNTRNO_L161 = Util
													.trim(MapUtils
															.getString(
																	l161s01aRescueItemNotSameMap,
																	"CNTRNO"));

											Map<String, String> lms140_rescueItem = codeTypeService
													.findByCodeType(
															"lms140_rescueItem",
															LMSUtil.getLocale()
																	.toString());

											String msg = "本次動用之額度序號「"
													+ cntrNo
													+ "」紓困代碼「"
													+ rescueItem
													+ "."
													+ lms140_rescueItem
															.get(rescueItem)
													+ "」不得與已動審額度序號「"
													+ CNTRNO_L161
													+ "「 "
													+ RESCUEITEM_L161
													+ "."
													+ lms140_rescueItem
															.get(RESCUEITEM_L161)
													+ "」並存。";

											throw new CapMessageException(
													RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, msg),
													getClass());
										}
									}
								}
							}
						}
					}
				}
			}
		}

		// J-110-0209_05097_B1001 Web e-Loan國內企金簽報書額度明細表中增列「創兆貸」專案，並產生統計報表案
		// 檢核紓困掛件文號新舊案不能相同
		String LMS_RESCUEITEM_CHK_DOCNO_NSAME = Util.trim(lmsService
				.getSysParamDataValue("LMS_RESCUEITEM_CHK_DOCNO_NSAME"));
		if (Util.notEquals(LMS_RESCUEITEM_CHK_DOCNO_NSAME, "")) {

			for (L160M01B l160m01b : l160m01a.getL160m01b()) {

				String cntrNo = l160m01b.getCntrNo();

				L140M01A l140m01a = lms1401Service
						.findL140m01aByMainId(l160m01b.getReMainId());

				if (l140m01a != null) {

					L161S01A l161s01a = lms1601Service
							.findL161m01aByMainIdCntrno(l160m01a.getMainId(),
									cntrNo);

					// 這次的動審表額度動用資訊
					String isRescue = Util.trim(l161s01a.getIsRescue());
					String rescueItem = Util.trim(l161s01a.getRescueItem());
					String rescueNo = Util.trim(l161s01a.getRescueNo());

					// 簽報書額度明細表的前次是否為109年嚴重肺炎......................
					String isRescueOn = Util.trim(l140m01a.getIsRescueOn());
					String rescueItemOn = Util.trim(l140m01a.getRescueItemOn());
					String cntrNoOn = Util.trim(l140m01a.getCntrNoOn());

					if (Util.equals(isRescue, "Y")) {

						// A04 A05 A06
						if (lmsService.isResueItemSubSidy(rescueItem, "110")) {

							if (Util.notEquals(rescueNo, "")) {
								// 查 383 的紓困掛件文號不能跟109年度相同

								List<Map<String, Object>> l161s01aRescueNoList = eloandbBASEService
										.findL161s01aLastRescueDataWithRescueNo(
												l140m01a.getCustId(),
												l140m01a.getDupNo(), rescueNo);
								if (l161s01aRescueNoList != null
										&& !l161s01aRescueNoList.isEmpty()) {
									for (Map<String, Object> l161s01aRescueNoMap : l161s01aRescueNoList) {
										String RESCUEITEM_L161_RESCUENO = Util
												.trim(MapUtils.getString(
														l161s01aRescueNoMap,
														"RESCUEITEM"));
										String CNTRNO_L161_RESCUENO = Util
												.trim(MapUtils.getString(
														l161s01aRescueNoMap,
														"CNTRNO"));
										if (Util.notEquals(
												RESCUEITEM_L161_RESCUENO, "")) {
											if (lmsService.isResueItemSubSidy(
													RESCUEITEM_L161_RESCUENO,
													"109")) {
												Map<String, String> lms140_rescueItem = codeTypeService
														.findByCodeType(
																"lms140_rescueItem",
																LMSUtil.getLocale()
																		.toString());

												String msg = "本次動用之額度序號「"
														+ cntrNo
														+ "」紓困貸款類別「"
														+ rescueItem
														+ "."
														+ lms140_rescueItem
																.get(rescueItemOn)
														+ "」，案件編號「"
														+ rescueNo
														+ "」與前次已核准動用之額度「"
														+ CNTRNO_L161_RESCUENO
														+ "、 "
														+ RESCUEITEM_L161_RESCUENO
														+ "."
														+ lms140_rescueItem
																.get(RESCUEITEM_L161_RESCUENO)
														+ "」不得相同。";

												throw new CapMessageException(
														RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, msg),
														getClass());
											}
										}
									}
								}

							}
						}

						// J-111-0112_05097_B1002 Web
						// e-Loan企金授信管理系統新增111年經濟部紓困方案
						if (lmsService.isResueItemSubSidy(rescueItem, "111")) {

							if (Util.notEquals(rescueNo, "")) {
								// 查 383 的紓困掛件文號不能跟110年度相同

								List<Map<String, Object>> l161s01aRescueNoList = eloandbBASEService
										.findL161s01aLastRescueDataWithRescueNo(
												l140m01a.getCustId(),
												l140m01a.getDupNo(), rescueNo);
								if (l161s01aRescueNoList != null
										&& !l161s01aRescueNoList.isEmpty()) {
									for (Map<String, Object> l161s01aRescueNoMap : l161s01aRescueNoList) {
										String RESCUEITEM_L161_RESCUENO = Util
												.trim(MapUtils.getString(
														l161s01aRescueNoMap,
														"RESCUEITEM"));
										String CNTRNO_L161_RESCUENO = Util
												.trim(MapUtils.getString(
														l161s01aRescueNoMap,
														"CNTRNO"));
										if (Util.notEquals(
												RESCUEITEM_L161_RESCUENO, "")) {
											if (lmsService.isResueItemSubSidy(
													RESCUEITEM_L161_RESCUENO,
													"109")
													|| lmsService
															.isResueItemSubSidy(
																	RESCUEITEM_L161_RESCUENO,
																	"110")) {
												Map<String, String> lms140_rescueItem = codeTypeService
														.findByCodeType(
																"lms140_rescueItem",
																LMSUtil.getLocale()
																		.toString());

												String msg = "本次動用之額度序號「"
														+ cntrNo
														+ "」紓困貸款類別「"
														+ rescueItem
														+ "."
														+ lms140_rescueItem
																.get(rescueItemOn)
														+ "」，案件編號「"
														+ rescueNo
														+ "」與前次已核准動用之額度「"
														+ CNTRNO_L161_RESCUENO
														+ "、 "
														+ RESCUEITEM_L161_RESCUENO
														+ "."
														+ lms140_rescueItem
																.get(RESCUEITEM_L161_RESCUENO)
														+ "」不得相同。";

												throw new CapMessageException(
														RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, msg),
														getClass());
											}
										}
									}
								}

							}
						}

					}
				}
			}

		}

		// J-111-0112_05097_B1001 Web e-Loan企金授信管理系統新增紓困代碼檢核
		for (L160M01B l160m01b : l160m01a.getL160m01b()) {

			String cntrNo = l160m01b.getCntrNo();

			L161S01A l161s01a = lms1601Service.findL161m01aByMainIdCntrno(
					l160m01a.getMainId(), cntrNo);

			L140M01A l140m01a = lms1401Service.findL140m01aByMainId(l160m01b
					.getReMainId());

			if (l161s01a == null) {
				continue;
			}

			String isRescue = Util.trim(l161s01a.getIsRescue());
			String rescueItem = Util.trim(l161s01a.getRescueItem());

			if (LMSUtil.isContainValue(Util.trim(l140m01a.getProPerty()),
					UtilConstants.Cntrdoc.Property.新做)) {

				if (l140m01a != null) {
					// 這次的動審表額度動用資訊

					if (Util.equals(isRescue, "Y")) {

						String rescueItemExpiredMsg = lmsService
								.isRescueItemExpired(rescueItem,
										CapDate.getCurrentDate("yyyy-MM-dd"),
										"2");

						if (Util.notEquals(rescueItemExpiredMsg, "")) {
							throw new CapMessageException(
									RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, rescueItemExpiredMsg),
									getClass());
						}
					}
				}

			}

			if (Util.equals(isRescue, "Y")) {
				String errMsg = lms1201Service.chkRescueItemNotEffect(
						rescueItem, "2");
				if (Util.notEquals(errMsg, "")) {
					throw new CapMessageException("額度序號" + cntrNo + errMsg,
							getClass());
				}
			}

		}

		// J-111-0406_05097_B1001 Web
		// e-Loan企金授信12305479(興富發建設股份有限公司)限制不得動撥及選擇上述所列二授信業務暨其相對應之會計科目。
		for (L160M01B l160m01b : l160m01a.getL160m01b()) {

			L140M01A l140m01a = lms1401Service.findL140m01aByMainId(l160m01b
					.getReMainId());

			if (l140m01a != null) {
				String noLoanMsg = lmsService.chkNoLoanId(l140m01a, "2");
				if (Util.notEquals(noLoanMsg, "")) {
					throw new CapMessageException(noLoanMsg, getClass());
				}
			}
		}

		// J-110-0540_05097_B1001 Web e-Loan企金授信配合調整E-loan系統動用審核表部分內容
		// 動審表主檔授信期限選擇3.詳額度動用資訊一覽表
		String tTypeErr = this.chkAndClearL161s01aTtypeData(l160m01a);
		if (Util.notEquals(Util.trim(tTypeErr), "")) {
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤, tTypeErr), getClass());
		}

		// J-110-0540_05097_B1001 Web e-Loan企金授信配合調整E-loan系統動用審核表部分內容
		// 3.連動擔保品管理系統，控管在額度本票未覆核的狀態，無法放行動審表。
		// J-112-0148 疫後振興 本票項目檢查
		String flag = "00";
		if (Util.equals(Util.trim(l160m01a.getIsSmallBuss()), "Y")
				|| lmsService.isRescueOnlyCaseF(l160m01a) || lmsService.isRescueOnlyCaseJ03HeadItem1(l160m01a)) {
			flag = "01";
		}
		String LMS_L160M01C_CHKITEM_P_NOTE = Util.trim(lmsService
				.getSysParamDataValue("LMS_L160M01C_CHKITEM_P_NOTE_" + flag)); // 3.額度本票

		if (Util.notEquals(LMS_L160M01C_CHKITEM_P_NOTE, "")
				&& Util.notEquals(LMS_L160M01C_CHKITEM_P_NOTE, "XX")) {
			for (L160M01C l160m01c : l160m01cList) {
				int seq = l160m01c.getItemSeq();
				if (UtilConstants.Usedoc.itemType.國內企金.equals(l160m01c
						.getItemType())) {
					if (Util.equals(LMS_L160M01C_CHKITEM_P_NOTE, Util.trim(seq))
							&& UtilConstants.Usedoc.checkItem.已收
									.equals(l160m01c.getItemCheck())) {
						// 3.連動擔保品管理系統，控管在額度本票未覆核的狀態，無法放行動審表。
						StringBuffer noPromissNoteCntrNo = new StringBuffer();
						for (L160M01B l160m01b : l160m01bs) {
							String cntrNo = l160m01b.getCntrNo();
							L140M01A l140m01a = lms1401Service
									.findL140m01aByMainId(l160m01b
											.getReMainId());
							if (l140m01a != null) {

								// 不變取消不檢查
								String proPerty = Util.trim(l140m01a
										.getProPerty());
								if (LMSUtil.isContainValue(proPerty,
										UtilConstants.Cntrdoc.Property.取消)
										|| LMSUtil
												.isContainValue(
														proPerty,
														UtilConstants.Cntrdoc.Property.不變)) {
									continue;
								}

								List<Map<String, Object>> cms06Map = eloandbBASEService
										.findCmsCollType06ByCntrNo(cntrNo);

								if (cms06Map == null || cms06Map.isEmpty()) {
									// 沒有已設定額度本票擔保品

									// J-110-0540_05097_B1002 Web
									// e-Loan企金授信配合調整E-loan系統動用審核表部分內容
									// 企金處何坤霖:要再多檢查額度明細表額度本票欄位如果是空白或是無，就不檢查
									if (Util.equals(l140m01a.getCheckNoteRef(),
											"1")) {
										// 詳前頁
										// 則要找前一筆非詳前頁之額度本票
										L120M01C l120m01c = l140m01a
												.getL120m01c();
										if (l120m01c != null) {

											List<L140M01A> l140m01as = lms1401Service
													.findL140m01aListByL120m01cMainId(
															l120m01c.getMainId(),
															l120m01c.getItemType(),
															l140m01a.getDocStatus());
											String checkNote = "";
											String preCntrNo = "";
											if (l140m01as != null
													&& !l140m01as.isEmpty()) {
												for (L140M01A tl140m01a : l140m01as) {
													if (Util.notEquals(
															Util.trim(l140m01a
																	.getCntrNo()),
															Util.trim(tl140m01a
																	.getCntrNo()))) {
														// 跟本筆額度序號不同，且非詳前頁，要記錄本票欄位內容
														if (Util.notEquals(
																tl140m01a
																		.getCheckNoteRef(),
																"1")) {
															checkNote = Util
																	.trim(tl140m01a
																			.getCheckNote());
															preCntrNo = Util
																	.trim(tl140m01a
																			.getCntrNo());
														}
													} else {
														// 相同一筆，離開
														break;
													}
												}
											}

											if (Util.isNotEmpty(checkNote)) {

												if (Util.isEmpty(checkNote)
														|| Util.equals(
																Util.trim(checkNote),
																"無")) {
													// 不是詳前頁，且本票欄位是空白或是無，就不檢查
												} else {
													// 不是詳前頁，且本票欄位有值且不是無，要檢查
													noPromissNoteCntrNo
															.append(noPromissNoteCntrNo
																	.length() > 0 ? "、"
																	: "");
													noPromissNoteCntrNo
															.append(cntrNo
																	+ "(詳前頁:"
																	+ preCntrNo
																	+ ")");
												}

											}
										}
									} else {
										// 不是詳前頁
										if (Util.isEmpty(l140m01a
												.getCheckNote())
												|| Util.equals(
														Util.trim(l140m01a
																.getCheckNote()),
														"無")) {
											// 不是詳前頁，且本票欄位是空白或是無，就不檢查
										} else {
											// 不是詳前頁，且本票欄位有值且不是無，要檢查
											noPromissNoteCntrNo
													.append(noPromissNoteCntrNo
															.length() > 0 ? "、"
															: "");
											noPromissNoteCntrNo.append(cntrNo);
										}
									}

								}
							} else {
								// 找不到額度明細表
								// throw new CapMessageException(
								// pop.getProperty("L160M01A.message66"),
								// getClass());
							}
						}

						if (noPromissNoteCntrNo.length() > 0) {
							// L160M01A.message113=其他事項有勾選本票相關，下列額度序號必須於擔保品系統之額度本票類別完成已覆核登記:{0}
							noPromissNoteCntrNo.insert(0, "<br/>");
							param.put(
									"msg",
									" "
											+ MessageFormat.format(
													pop.getProperty("L160M01A.message113"),
													noPromissNoteCntrNo
															.toString()));
							throw new CapMessageException(
									RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, param), getClass());
						}

						break;
					}
				}
			}
		}

		
		// J-112-0196_08035_B1001  動審表送呈檢查是否有已覆核登記之保證擔保品
		String j1120194_is_online = Util.trim(lmsService.getSysParamDataValue("J-112-0194_is_online"));
		
		if ("Y".equals(j1120194_is_online)){
			StringBuilder errMsg = new StringBuilder();
			for (L160M01B l160m01b : l160m01a.getL160m01b()) {
				L140M01A l140m01a = lms1401Service.findL140m01aByMainId(l160m01b.getReMainId());

				if (l140m01a != null && Util.equals(Util.trim(l140m01a.getHeadItem1()),"Y")) {//J-112-0366_12473_B1001  因應批次保證調整判斷邏輯
					List<Map<String, Object>> cms05Map = eloandbBASEService.findCmsCollType05ByCntrNo(l140m01a.getCntrNo());
					if (cms05Map == null || cms05Map.isEmpty()) {
						errMsg.append(l140m01a.getCntrNo()).append(" ");
					}
				}
			}
			if (Util.isNotEmpty(errMsg)) {
				errMsg.append(pop.getProperty("L160M01A.message118"));
				throw new CapMessageException(errMsg.toString(), getClass());
			}
		}
		//是否檢核行業別
		String classCode9_controlFlag = Util.trim(lmsService.getSysParamDataValue("J-113-0166_controlFlag"));
		if(Util.equals(UtilConstants.DEFAULT.是, classCode9_controlFlag)){
			//本次動用資訊，檢查所有額度借款人
			for (L160M01B l160m01b : l160m01a.getL160m01b()) {
				L140M01A l140m01a = lms1401Service.findL140m01aByMainId(l160m01b.getReMainId());
				//來源簽報書額度動用的借款人
				L120S01B l120s01b = lms1201Service.findL120s01bByUniqueKey(l160m01a.getSrcMainId(), l140m01a.getCustId(), l140m01a.getDupNo());
				//檢核 MIS.CUSTDATA 與 
				Map<String, Object> custInfo = misCustdataService.findAllByByCustIdAndDupNo(l140m01a.getCustId(), l140m01a.getDupNo());
				if(l120s01b != null && custInfo != null){
					StringBuilder errMsg = new StringBuilder();
					//0024行業別與簽案借款人行業別不同
					if(Util.notEquals(Util.trim(l120s01b.getBusCode()), Util.trim(MapUtils.getString(custInfo, "BUSCD", "")))){
						//J-113-0166_11850_B1001 依客戶取得是否有 ELF442_CLASS_CODE=9(預約額度 09: 行業對象別申請確認暨修改)，如有資料則跳警示
						List<Map<String, Object>> elf442List = misELF442Service.findELF442ByCustIdByClassCode(l140m01a.getCustId(), l140m01a.getDupNo(), "09");
						if(elf442List == null || elf442List.isEmpty()){//沒申請
							errMsg.append(pop.getProperty("L160M01A.message119"));
							throw new CapMessageException(errMsg.toString(), getClass());
						}else{//有申請，需確認申請的行業別是否相同, 不同還是要擋住
							if(elf442List != null && !elf442List.isEmpty()){
								for (Map<String, Object> elf442Map : elf442List) {
									//ELF442_CLASS_CODE =09時   為申請調整的行業對象別
									String elf442Site4No= Util.trim(MapUtils.getString(elf442Map, "ELF442_SITE4NO", ""));
									if(Util.notEquals(elf442Site4No, Util.trim(MapUtils.getString(custInfo, "BUSCD", "")))){
										errMsg.append(pop.getProperty("L160M01A.message119"));
										throw new CapMessageException(errMsg.toString(), getClass());
									}
								}
							}
						}
					}
				}
			}
		}
		CapAjaxFormResult result = new CapAjaxFormResult();

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 查詢所選銀行的甲級主管、乙級主管清單
		SignEnum[] signs = { SignEnum.首長, SignEnum.單位主管, SignEnum.甲級主管,
				SignEnum.乙級主管 };
		Map<String, String> bossList = userInfoService.findByBrnoAndSignId(
				user.getUnitNo(), signs);
		result.set("bossList", new CapAjaxFormResult(bossList));
		return result;
	}

	/**
	 * 檢查是否為必填項目
	 * 
	 * @param seq
	 *            檢查序號
	 * @return 是 |否
	 */
	private Boolean isRequireItem(int seq, L160M01A l160m01a) {
		/**
		 * J-101-0104
		 * 
		 * <pre>
		 * 檢查
		 * 動審表增加五、其他事項，下列查核項目應辦而未完成，不得呈主管覆核之檢核。
		 * 1.核准敘做文件；
		 * 3.額度本票；
		 * 10.身分證遺失檢查；
		 * 11.查詢司法院網站借款人及保證人是否為受監護或受輔助宣告資料	；
		 * 12.恐怖份子黑名單查詢紀錄；
		 * 13.銀行法及金控法利害關係人查詢紀錄(簽約時須再查一次)；
		 * 14.同一關係企業/集團企業建檔維護；
		 * 15.應收帳款承購無追索權—買方；銀行法及金控法利害關係人查詢紀錄；
		 * 17.核貸條件其他應辦事項之土地信託或地上權設定；
		 * 22.擔保品設押(質)。
		 * 
		 * </pre>
		 */

		// for (Integer theSeq : UtilConstants.Usedoc.動審表必填項目) {
		// if (theSeq == seq) {
		// return true;
		// }
		// }

		// COM_J1070152_160M01C_CHK_TW
		// 1,3,10,11,12,13,14,16,21
		// 國內-動審表檢查五、其他事項，下列查核項目應辦而未完成，不得呈主管覆核之檢核
		String chkItems = "";
		if (l160m01a != null
				&& Util.equals(Util.trim(l160m01a.getIsSmallBuss()), "Y")) {
			chkItems = Util.trim(lmsService
					.getSysParamDataValue("LMS_SMALLBUSS_CHKLIST_CHK"));
		} else {
			chkItems = Util.trim(lmsService
					.getSysParamDataValue("COM_J1070152_160M01C_CHK_TW"));
		}

		if (Util.notEquals(chkItems, "")) {
			for (String xx : chkItems.split(",")) {
				if (Util.equals(xx, Util.trim(seq))) {
					return true;
				}
			}
		}

		return false;
	}

	/**
	 * 壓力測試用
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult TETSMIS(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);
		L160M01A l160m01a = lms1601Service.findModelByOid(L160M01A.class, oid);
		lms1601Service.upLoadMIS(l160m01a, true);
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		return result;

	}

	/**
	 * 顯示0024客戶資訊檔中該企業為何種企業
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	private String getCltype(String custId, String dupNo) {
		/**
		 * <pre>
		 * 		   If Not(doc.DupCode(0) >= "A" And doc.DupCode(0) <= "Z") Then
		 *            Dim scale As String
		 *            If gfnDB2GetCompScale(doc.borrower_id(0)) = "1" Then            
		 *                    scale = "大型企業"
		 *            Else
		 *                    scale = "中小型企業"
		 *            End If
		 *            Msgbox doc.borrower(0) + "【為" + scale +"】，若資料有誤，逕自0024客戶資訊檔更正！", 64,db.title
		 *    End If
		 *    Print "起始時間: " & FROMDTIME & "   終止時間:" & Now()
		 *    Msgbox "執行成功，此文件已移至「已覆核」",64,db.title
		 *    Call uidoc.Close
		 * </pre>
		 **/

		String result = "";
		if (Util.isNumeric(dupNo)) {
			String clType = misCustdataService.findCltypeById(custId, dupNo);
			if (Util.isNotEmpty(clType)) {
				// L160M01A.message64={0}【為{1}】，若資料有誤，逕自0024客戶資訊檔更正！
				Properties pop = MessageBundleScriptCreator
						.getComponentResource(LMS1601M01Page.class);
				String temp = "";
				String custKey = custId + " " + dupNo;
				if ("1".equals(clType)) {
					// L160M01A.Cltype1=大型企業
					temp = pop.getProperty("L160M01A.Cltype1");
				} else {
					// L160M01A.Cltype2=中小型企業
					temp = pop.getProperty("L160M01A.Cltype2");
				}
				result = MessageFormat.format(
						pop.getProperty("L160M01A.message64"), custKey, temp);
			}
		}
		return result;
	}

	/**
	 * 取得所有需查詢黑名單的客戶ID
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryCust(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		JSONObject json = new JSONObject();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		// L160M01A l160m01a = lms1601Service.findL160M01AByMaindId(mainId);
		List<L160M01B> l160m01bs = (List<L160M01B>) lms1601Service
				.findListByMainId(L160M01B.class, mainId);

		ArrayList<String> mainIds = new ArrayList<String>();
		for (L160M01B l160m01b : l160m01bs) {
			mainIds.add(l160m01b.getReMainId());
		}

		List<L140M01A> l140m01as = lms1401Service
				.findL140m01aListByMainIdList(mainIds
						.toArray(new String[mainIds.size()]));
		for (L140M01A l140m01a : l140m01as) {
			String custId = Util.trim(l140m01a.getCustId());
			String dupNo = Util.trim(l140m01a.getDupNo());
			String custName = Util.trim(l140m01a.getCustName());
			String key = custId + dupNo;
			if (!json.containsKey(key) && Util.isNotEmpty(key)) {
				JSONObject value = new JSONObject();
				value.put("custId", custId);
				value.put("dupNo", dupNo);
				value.put("custName", custName);
				json.put(key, value);
			}
		}

		result.set("cust", new CapAjaxFormResult(json));
		return result;

	}

	/**
	 * 查詢L161S01A 聯貸案參貸比率一覽表主檔
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL161s01a(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);

		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1401S02Panel.class);

		Properties prop2 = MessageBundleScriptCreator
				.getComponentResource(LMS1601M01Page.class);

		L161S01A l161s01a = lms1601Service.findModelByOid(L161S01A.class, oid);

		// J-110-0540_05097_B1001 Web e-Loan企金授信配合調整E-loan系統動用審核表部分內容
		L160M01A l160m01a = null;
		if (l161s01a != null) {
			l160m01a = lms1601Service.findL160M01AByMaindId(l161s01a
					.getMainId());
		} else {
			l160m01a = new L160M01A();
		}

		L140M01A l140m01a = null;
		if (!Util.isEmpty(l161s01a)) {
			// J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制
			String mainId140 = l161s01a.getCntrMainId();
			l140m01a = lms1401Service.findL140m01aByMainId(mainId140);
			if (l140m01a == null) {
				// 找不到額度明細表
				throw new CapMessageException(
						prop2.getProperty("L160M01A.message66"), getClass());
			}

			if (Util.equals(l161s01a.getIsDerivatives(), "")) {
				Boolean hasDerivateSubjectFlag = false;

				ArrayList<String> itemsAll = new ArrayList<String>();
				List<L140M01C> l140m01cs = lms1401Service
						.findL140m01cListByMainId(l140m01a.getMainId());

				if (l140m01cs != null && !l140m01cs.isEmpty()) {
					for (L140M01C l140m01c : l140m01cs) {
						itemsAll.add(l140m01c.getLoanTP());
					}

					hasDerivateSubjectFlag = lmsService
							.hasDerivateSubject(itemsAll
									.toArray(new String[itemsAll.size()]));

					if (hasDerivateSubjectFlag == true) {
						l161s01a.setIsDerivatives("Y");
					}
				} else {
					// 找不到額度明細表
					throw new CapMessageException(
							prop2.getProperty("L160M01A.message66"), getClass());
				}

			}

		}
		if (!Util.isEmpty(l161s01a)) {
			result = DataParse.toResult(l161s01a);
		}

		// 判斷是否要顯示動撥提醒資訊頁簽
		String LMS_ToAloan2CanShow = chkArCtrlCanShow();
		if (Util.equals(LMS_ToAloan2CanShow, "Y")) {
			result.set("toAloan2CanShow", "Y");
		} else {
			result.set("toAloan2CanShow", "N");
		}

		L161S01C l161s01c = lms1601Service.findL161s01cByMainIdPidItemType(
				l161s01a.getMainId(), l161s01a.getUid(),
				UtilConstants.Cntrdoc.l140m01bItemType.其他敘做條件_動撥提示用語);

		if (l161s01c != null) {
			result.set("toALoan1", l161s01c.getToALoan());
			result.set("toALoan2", l161s01c.getItemDscr());
		} else {
			result.set("toALoan1", "");
			result.set("toALoan2", "");
		}

		// J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制
		if (l140m01a != null) {
			result.set("custId", l140m01a.getCustId());
			result.set("dupNo", l140m01a.getDupNo());
			result.set("custName", l140m01a.getCustName());
		}

		result.set("proPertyShowMsg",
				Util.equals(Util.trim(l161s01a.getProperty()), "") ? ""
						: LMSUtil.getProPerty(l161s01a.getProperty(), prop));

		// J-110-0540_05097_B1001 Web e-Loan企金授信配合調整E-loan系統動用審核表部分內容
		result.set("l160m01a_tType", Util.trim(l160m01a.getTType()));
		
		// J-112-0366_12473_B1001
		result.set("l140m01a_gutType", Util.trim(l140m01a.getGutType()));

		return result;
	}

	/**
	 * 儲存L161M01B 聯貸案參貸比率一覽表明細檔
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL161s01a(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String cntrNo = Util.trim(params.getString("cntrNo"));
		String uid = Util.trim(params.getString("uid"));
		String formL161m01a = params.getString("L161M01AForm");

		JSONObject jsonL161m01a = JSONObject.fromObject(formL161m01a);
		L160M01A l160m01a = lms1601Service.findL160M01AByMaindId(mainId);
		L161S01A l161s01a = lms1601Service.findL161m01aByMainIdUid(mainId, uid);

		if (Util.isEmpty(l161s01a)) {
			l161s01a = new L161S01A();
			DataParse.toBean(jsonL161m01a, l161s01a);
			l161s01a.setCreator(user.getUserId());
			l161s01a.setCreateTime(CapDate.getCurrentTimestamp());
			l161s01a.setMainId(mainId);
		} else {
			DataParse.toBean(jsonL161m01a, l161s01a);
		}

		// J-108-0098-001 企金處移除振興經濟非中小企相關欄位
		l161s01a.setIsNonSMEProjLoan("");
		l161s01a.setNonSMEProjLoanAmt(null);

		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1601M01Page.class);
		String validate = Util.validateColumnSize(l161s01a, prop, "L160M01A");
		if (validate != null) {
			Map<String, String> param = new HashMap<String, String>();
			param.put("colName", validate);

			throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
		}

		// J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制
		String tCntrNo = cntrNo;
		if (Util.notEquals(tCntrNo, "")) {
			ELF600 elf600 = misELF600Service.findByContract(tCntrNo);
			String isClearLand = lmsService.isClearLandEffective(elf600);

			if (Util.equals(isClearLand, "Y")) {
				if (CrsUtil.isNOT_null_and_NOTZeroDate(elf600
						.getElf600_fstdate())) {
					if (elf600.getElf600_fstdate().compareTo(
							l161s01a.getFstDate()) != 0) {
						throw new CapMessageException(
								"撥貸逾一年以上未動工興建之空地貸款控管中「初次核定預計動工日」"
										+ CapDate.formatDate(
												l161s01a.getFstDate(),
												"yyyy-MM-dd")
										+ "與ELF600控制檔"
										+ CapDate.formatDate(
												elf600.getElf600_fstdate(),
												"yyyy-MM-dd") + "不一致",
								getClass());
					}
				}

				if (CrsUtil.isNOT_null_and_NOTZeroDate(elf600
						.getElf600_lstdate())) {
					if (elf600.getElf600_lstdate().compareTo(
							l161s01a.getLstDate()) != 0) {
						throw new CapMessageException(
								"撥貸逾一年以上未動工興建之空地貸款控管中「最新核定(動審)預計動工日」"
										+ CapDate.formatDate(
												l161s01a.getLstDate(),
												"yyyy-MM-dd")
										+ "與ELF600控制檔"
										+ CapDate.formatDate(
												elf600.getElf600_lstdate(),
												"yyyy-MM-dd") + "不一致",
								getClass());
					}
				}

			}

		}

		int countUnitMega = 0;
		int countUnitBank = 0;
		if (Util.equals(jsonL161m01a.optString("unitCase", ""), "Y")
				|| Util.equals(jsonL161m01a.optString("unitMega", ""), "Y")) {

			Set<L161S01B> l161s01bs = l161s01a.getL161s01b();

			if (l161s01bs != null && !l161s01bs.isEmpty()) {
				for (L161S01B l161s01b : l161s01bs) {

					if (Util.equals(l161s01b.getSlBank(), UtilConstants.兆豐銀行代碼)) {
						// hasUnitMega = true;
						countUnitMega = countUnitMega + 1;
					} else {
						// hasUnitBank = true;
						countUnitBank = countUnitBank + 1;
					}

				}
			}

		}
		if (countUnitBank > 0) {
			l161s01a.setCoBank("Y");
		} else {
			l161s01a.setCoBank("N");
		}

		if (countUnitMega > 0) {
			l161s01a.setCoBranch("Y");
		} else {
			l161s01a.setCoBranch("N");
		}

		if (Util.equals(l161s01a.getIsDerivatives(), "")) {

			L140M01A l140m01a = lms1401Service.findL140m01aByMainId(l161s01a
					.getCntrMainId());

			if (l140m01a == null) {
				// 找不到額度明細表
				throw new CapMessageException(
						prop.getProperty("L160M01A.message66")
								+ l161s01a.getCntrNo(), getClass());
			}

			Boolean hasDerivateSubjectFlag = false;

			ArrayList<String> itemsAll = new ArrayList<String>();
			List<L140M01C> l140m01cs = lms1401Service
					.findL140m01cListByMainId(l140m01a.getMainId());

			if (l140m01cs != null && !l140m01cs.isEmpty()) {
				for (L140M01C l140m01c : l140m01cs) {
					itemsAll.add(l140m01c.getLoanTP());
				}

				hasDerivateSubjectFlag = lmsService.hasDerivateSubject(itemsAll
						.toArray(new String[itemsAll.size()]));

				if (hasDerivateSubjectFlag == true) {
					l161s01a.setIsDerivatives(UtilConstants.DEFAULT.是);
				} else {
					l161s01a.setIsDerivatives(UtilConstants.DEFAULT.否);
				}
			} else {
				// 找不到額度明細表
				throw new CapMessageException(
						prop.getProperty("L160M01A.message66")
								+ l161s01a.getCntrNo(), getClass());
			}

		}

		// J-103-0317-001 Web e-Loan企金簽報書上傳承諾事項與追蹤檢視日期
		// 1.動撥提示用語
		// for「9動撥提示用語」注意事項
		// 最多只能輸入３０個全型字！

		L161S01C l161s01c = lms1601Service.findL161s01cByMainIdPidItemType(
				mainId, uid,
				UtilConstants.Cntrdoc.l140m01bItemType.其他敘做條件_動撥提示用語);

		if (Util.isEmpty(l161s01c)) {
			l161s01c = new L161S01C();
			DataParse.toBean(jsonL161m01a, l161s01c);
			l161s01c.setCreator(user.getUserId());
			l161s01c.setCreateTime(CapDate.getCurrentTimestamp());
			l161s01c.setMainId(mainId);
			l161s01c.setCntrNo(l161s01a.getCntrNo());
			l161s01c.setPid(l161s01a.getUid());
			l161s01c.setItemType(UtilConstants.Cntrdoc.l140m01bItemType.其他敘做條件_動撥提示用語);
		} else {
			DataParse.toBean(jsonL161m01a, l161s01c);
		}

		String toALoan1 = Util.toFullCharString(jsonL161m01a
				.getString("toALoan1"));
		if (this.calTheWordByte(toALoan1, 90)) {
			Map<String, String> param = new HashMap<String, String>();
			// L140M01b.toALoan=動撥提醒事項
			// L140M01b.toALoan1=注意事項
			param.put("colName", "動撥提醒事項" + "注意事項" + "(30)");
			// param.put("colName", prop.getProperty("L140M01b.toALoan")
			// + prop.getProperty("L140M01b.toALoan1") + "(30)");
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
		}
		l161s01c.setToALoan(toALoan1);
		// for「9動撥提示用語」承諾事項
		// L140M01b.toALoan=動撥提醒事項
		l161s01c.setToALoan(toALoan1);

		// L140M01b.toALoan2=承諾事項
		String toALoan2 = Util.toFullCharString(jsonL161m01a
				.getString("toALoan2"));
		if (calTheWordByte(toALoan2, 1170)) {
			Map<String, String> param = new HashMap<String, String>();
			// L140S02Tab.11=總處核定
			param.put("colName", "動撥提醒事項" + "承諾事項" + "(390)");
			// param.put("colName", prop.getProperty("L140M01b.toALoan")
			// + prop.getProperty("L140M01b.toALoan2") + "(390)");
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
		}
		// 最多只能輸入３９０個全型字
		l161s01c.setItemDscr(toALoan2);

		String suggestMsg = this.getSuggestData(prop, l161s01a);
		String errorMsg = this.isCheckData(prop, l161s01a, l160m01a, l161s01c, result);

		if (Util.equals(Util.trim(errorMsg), "")) {
			l161s01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.已計算);
		} else {
			l161s01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);
		}

		result.set("suggestMsg", suggestMsg);
		result.set("errorMsg", errorMsg);
		lms1601Service.save(l161s01a, l161s01c);
		return result;

	}

	/**
	 * 設定 訊息換列 目前設定五筆 就換列
	 * 
	 * @param temp
	 *            暫存文字
	 * @param countItme
	 *            計算欄位數量
	 * @param showMessage
	 * @return countItme 目前的 計算數量
	 */
	public int setHtmlBr(StringBuffer temp, int countItme, String showMessage) {
		int maxLenth = 5;
		temp.append(temp.length() > 0 ? "、" : "");
		if (countItme > maxLenth) {
			temp.append("<br/>");
			countItme = 1;
		} else {
			countItme++;
		}
		temp.append(showMessage);
		return countItme;
	}

	public String getSuggestData(Properties prop, L161S01A l161s01a) throws CapException {
		StringBuffer temp = new StringBuffer("");
		// 取得建議資訊

		L140M01A l140m01a = lms1401Service.findL140m01aByMainId(l161s01a
				.getCntrMainId());

		if (l140m01a == null) {
			// 找不到額度明細表
			throw new CapMessageException(
					prop.getProperty("L160M01A.message66")
							+ l161s01a.getCntrNo(), getClass());
		}

		// String tmpUnitCase = null;
		// String tmpMainBranch = null;
		String caseType = null;

		// String cntrNo = l161s01a.getCntrNo();

		// 檢核案件性質、額度控管種類
		if (!UtilConstants.Cntrdoc.Property.不變.equals(l161s01a.getProperty())) {

			Map<String, String> caseMap = lmsService.getCaseType("2", l161s01a,
					l140m01a);

			// tmpUnitCase = caseMap.get("tmpUnitCase");
			// tmpMainBranch = caseMap.get("tmpMainBranch");
			caseType = caseMap.get("caseType");

			// 多判斷動審表聯行攤貸比率有無017兆豐是主辦行*************
			String hasMainFlag = "N";
			String slBankType = "";
			String slBank = "";
			String slMaster = "";
			// 放017兆豐銀行的l161s01b
			Map<String, L161S01B> checkList = new HashMap<String, L161S01B>();
			Set<L161S01B> l161s01bs = l161s01a.getL161s01b();

			if (l161s01bs != null && !l161s01bs.isEmpty()) {
				for (L161S01B l161s01b : l161s01bs) {

					slBankType = l161s01b.getSlBankType();
					slBank = l161s01b.getSlBank();
					slMaster = l161s01b.getSlMaster();
					if (Util.equals(slBankType, "01")
							&& Util.equals(slBank, UtilConstants.兆豐銀行代碼)) {
						if (Util.equals(slMaster, "Y")) {
							hasMainFlag = "Y";
						}
						checkList.put(l161s01b.getSlBranch(), l161s01b);
					}

				}
			}
			//G-113-0036聯貸比率資訊(L161S01B)自行攤貸 <> 額度明細表聯行攤貸比例(L140M01E_AF)
			//1. 017兆豐銀行  家數、金額不一致時跳提醒
			//2. 攤貸行現請額度金額>分攤金額時，跳提醒
			Set<L140M01E_AF> l140m01e_afs = l140m01a.getL140m01e_af();
			BigDecimal branchShareAmt = l140m01a.getCurrentApplyAmt();
			if(l140m01e_afs != null && !l140m01e_afs.isEmpty()){
				if(l140m01e_afs.size() != checkList.size()){
					if(Util.equals(l161s01a.getUnitMega(), "Y")){
						temp.append(new StringBuffer(prop.getProperty("L140M01e.message08")));
						temp.append("<br/>");
					}
				}else{
					for (L140M01E_AF l140m01e_af : l140m01e_afs) {
						//攤貸行現請額度金額>分攤金額時，跳提醒
						if(Util.equals(l161s01a.getCntrNo().substring(0, 3), l140m01e_af.getShareNo().substring(0, 3))){
							if (Util.notEquals(l161s01a.getCntrNo().substring(0, 3), l140m01a.getCntrNo().substring(0, 3))) {//非額度管理行
								branchShareAmt = l140m01e_af.getShareAmt();
							}
						}
						if(Util.equals(l161s01a.getUnitMega(), "Y")){
							if(checkList.get(l140m01e_af.getShareBrId()) == null){
								//分行不一樣 跳提醒
								temp.append(new StringBuffer(prop.getProperty("L140M01e.message08")));
								temp.append("<br/>");
								break;
							}else{
								//同分行檢核參貸金額金額，不一致跳提醒
								if(l140m01e_af.getShareAmt().compareTo(checkList.get(l140m01e_af.getShareBrId()).getSlAmt()) != 0){
									temp.append(new StringBuffer(prop.getProperty("L140M01e.message08")));
									temp.append("<br/>");
									break;
								}
							}
						}
					}
				}
			}
			//非額度管理行(攤貸行動審)如動審現請額度 > 攤貸金額，跳提醒訊息 
			if (l161s01a.getCurrentApplyAmt().compareTo(branchShareAmt) > 0) {
				temp.append(new StringBuffer(prop.getProperty("L140M01e.message09")));
				temp.append("<br/>");
			}

			if (Util.equals(hasMainFlag, "Y")) {
				if (Util.equals(caseType, UtilConstants.Usedoc.caseType.同業聯貸參貸)) {
					// 案件性質建議為
					caseType = UtilConstants.Usedoc.caseType.同業聯貸主辦;
				} else if (Util.equals(caseType,
						UtilConstants.Usedoc.caseType.同業聯貸參貸含自行聯貸)) {
					caseType = UtilConstants.Usedoc.caseType.同業聯貸主辦含自行聯貸;
				}
			}

			// *************

			// 1
			if (Util.notEquals(caseType, l161s01a.getCaseType())) {
				CodeType caseTypeCT = codeTypeService
						.findByCodeTypeAndCodeValue("lms1605m01_caseType",
								Util.trim(caseType));

				// 案件性質建議為
				temp.append(new StringBuffer(prop
						.getProperty("L160M01A.message67")).append(caseType)
						.append(".").append(caseTypeCT.getCodeDesc()));
				temp.append("<br/>");

			}

			// 2 lms1405m01_snoKind
			StringBuffer snoKindStr = new StringBuffer("");
			if (Util.notEquals(Util.trim(l161s01a.getSnoKind()), "")) {
				CodeType snoKind = codeTypeService.findByCodeTypeAndCodeValue(
						"lms1405m01_snoKind", Util.trim(l161s01a.getSnoKind()));
				snoKindStr.append(Util.trim(l161s01a.getSnoKind())).append(" ")
						.append(snoKind.getCodeDesc());
			} else {
				snoKindStr.append(Util.trim(l140m01a.getSnoKind()));
			}

			StringBuffer suggestSnoKind = null;
			suggestSnoKind = new StringBuffer("");

			Map<String, String> caseMapSuggest = lmsService.getCaseType("1",
					l161s01a, l140m01a);

			String caseTypeSuggest = caseMapSuggest.get("caseType");

			Map<String, String> snoKindCodeTypeMap = codeTypeService
					.findByCodeType("lms1405m01_snoKind");

			switch (Util.parseInt(caseTypeSuggest)) {
			case 1:
			case 2:
			case 4:
			case 5:
				if (!UtilConstants.Cntrdoc.snoKind.聯貸.equals(l161s01a
						.getSnoKind())) {
					// 建議為 『30.聯貸』|『62.聯貸』
					suggestSnoKind.append("30.")
							.append(snoKindCodeTypeMap.get("30")).append("、");
					suggestSnoKind.append("62.").append(
							snoKindCodeTypeMap.get("62"));
				}
				break;
			case 3:
				if (!UtilConstants.Cntrdoc.snoKind.一般.equals(l161s01a
						.getSnoKind())) {
					// 建議為 『10.一般』
					suggestSnoKind.append("10.").append(
							snoKindCodeTypeMap.get("10"));
				}
				break;
			case 7:
				if (!UtilConstants.Cntrdoc.snoKind.合作母.equals(l161s01a
						.getSnoKind())) {
					// 建議為 『40.合作母』|
					suggestSnoKind.append("40.").append(
							snoKindCodeTypeMap.get("40"));
				}
				break;

			case 8:
				if (!UtilConstants.Cntrdoc.snoKind.合作子.equals(l161s01a
						.getSnoKind())) {
					// 建議為 『41.合作子』|
					suggestSnoKind.append("40.").append(
							snoKindCodeTypeMap.get("41"));
				}
				break;
			case 9:
				if (!UtilConstants.Cntrdoc.snoKind.應收帳款買方.equals(l161s01a
						.getSnoKind())
						&& !UtilConstants.Cntrdoc.snoKind.應收帳款賣方一般戶
								.equals(l161s01a.getSnoKind())) {
					// 『60.應收帳款及供應鏈融資買方』『61.應收帳款及供應鏈融資賣方一般戶』|
					suggestSnoKind.append("60.")
							.append(snoKindCodeTypeMap.get("60")).append("、");
					suggestSnoKind.append("61.").append(
							snoKindCodeTypeMap.get("61"));
				}
				break;
			case 10:
				if (!UtilConstants.Cntrdoc.snoKind.應收帳款賣方聯貸母戶.equals(l161s01a
						.getSnoKind())) {
					// 『62.應收帳款及供應鏈融資賣方聯貸母戶』|
					suggestSnoKind.append("62.").append(
							snoKindCodeTypeMap.get("62"));
				}
				break;
			default:

				break;

			}

			if (Util.notEquals(Util.trim(suggestSnoKind.toString()), "")) {

				// 額度控管種類 建議
				temp.append(new StringBuffer(prop
						.getProperty("L140M01a.snoKind"))
						.append(prop.getProperty("other.suggest")).append("【")
						.append(suggestSnoKind.toString()).append("】")
						.toString());
				temp.append("<br/>");

			}

		}

		// J-109-0077_05097_B1001 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
		String isRescue = Util.trim(l161s01a.getIsRescue());
		String rescueItem = Util.trim(l161s01a.getRescueItem());
		String rescueItemSub = Util.trim(l161s01a.getRescueItemSub());

		// J-109-0077_05097_B1015 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
		if (Util.equals(isRescue, "Y")) {

			if (lmsService.isResueItemNeedEmpCount(rescueItem, rescueItemSub)) {
				BigDecimal empCount = null;
				empCount = l161s01a.getEmpCount();
				if (empCount == null) {
					// 顯示錯誤訊息，非建議訊息
				} else {
					if (BigDecimal.ZERO.compareTo(empCount) == 0) {
						// L160M01A.empCount=現有員工人數
						temp.append(new StringBuffer("欄位「"
								+ prop.getProperty("L160M01A.empCount") + "」")
								.append("為0，請再次確認是否正確!").toString());
						temp.append("<br/>");
					}
				}
			}
		}

		// J-109-0811_05097_B1001 配合「嚴重特殊傳染性肺炎防治及妤困振興特別條例」施行期間調整，動審表新增央行優惠利率融通期限
		// 提醒訊息
		String isCbRefin = Util.trim(l161s01a.getIsCbRefin());
		if (Util.equals(isRescue, "Y") && Util.equals(isCbRefin, "Y")) {
			String cbRefinDt = Util.trim(l161s01a.getCbRefinDt());

			// cbRefinDt = 01 或空白為2020-03-27，不用檢查
			if (Util.notEquals(cbRefinDt, "")
					&& Util.notEquals(cbRefinDt, "01")
					&& Util.notEquals(Util.trim(l161s01a.getCntrNo()), "")) {

				String chkCbRefinDt = Util.trim(sysParameterService
						.getParamValue("LMS_RESCUE_CBREFINDT_" + cbRefinDt));

				if (Util.notEquals(chkCbRefinDt, "")
						&& Util.notEquals(chkCbRefinDt, "0001-01-01")) {

					String cbRefinDt_ElonErrMsg = "";
					String cbRefinDt_AlonErrMsg = "";

					// 1.檢查該案簽報書的核准日期
					L120M01C l120m01c = l140m01a.getL120m01c();

					L120M01A l120m01a = lms1201Service
							.findL120m01aByMainId(l120m01c.getMainId());
					if (l120m01a != null) {
						String ENDDATE = l120m01a.getEndDate() == null ? ""
								: CapDate.formatDate(l120m01a.getEndDate(),
										"yyyy-MM-dd");

						if (Util.notEquals(ENDDATE, "")
								&& Util.notEquals(ENDDATE, "0001-01-01")) {

							// 2020-08-10之後核准的案子，央行優惠利率融通期限才可以是02(2021-06-30)
							// 2020-08-10 > 2020-07-01
							if (LMSUtil.cmpDate(Util.parseDate(chkCbRefinDt),
									">", Util.parseDate(ENDDATE))) {
								cbRefinDt_ElonErrMsg = "，惟簽報書核准日期" + ENDDATE
										+ "小於" + chkCbRefinDt;
							}
						}
					}

					// 2.檢查ALOAN授信起日
					Map<String, Object> lnf022Map = misLNF022Service
							.findByCntrNoFetchOne(Util.trim(l161s01a
									.getCntrNo()));

					if (lnf022Map != null && !lnf022Map.isEmpty()) {
						// a-Loan額度已建檔
						String LNF022_DURING_BG = Util.trim(MapUtils.getString(
								lnf022Map, "LNF022_DURING_BG", ""));

						if (Util.notEquals(LNF022_DURING_BG, "")
								&& Util.notEquals(LNF022_DURING_BG,
										"0001-01-01")) {

							// 2020-08-10之後核准的案子，央行優惠利率融通期限才可以是02(2021-06-30)
							// 2020-08-10 > 2020-07-01
							if (LMSUtil.cmpDate(Util.parseDate(chkCbRefinDt),
									">", Util.parseDate(LNF022_DURING_BG))) {
								cbRefinDt_AlonErrMsg = (Util.equals(
										cbRefinDt_ElonErrMsg, "") ? "，惟" : "，且")
										+ "此額度已於a-Loan建檔，授信起日"
										+ LNF022_DURING_BG
										+ "小於"
										+ chkCbRefinDt;

							}
						}

					}

					if (Util.notEquals(cbRefinDt_AlonErrMsg, "")
							|| Util.notEquals(cbRefinDt_ElonErrMsg, "")) {
						// J-109-0811_05097_B1001
						// 配合「嚴重特殊傳染性肺炎防治及妤困振興特別條例」施行期間調整，動審表新增央行優惠利率融通期限
						Map<String, String> lms140_cbRefinDt = codeTypeService
								.findByCodeType("lms140_cbRefinDt", LMSUtil
										.getLocale().toString());

						// LMS_RESCUE_CBREFINDT_MSG=洽企金處詹景安襄理(#2689)
						String chkCbRefinDtMsg = Util.trim(sysParameterService
								.getParamValue("LMS_RESCUE_CBREFINDT_MSG"));

						temp.append(new StringBuffer("欄位「"
								+ prop.getProperty("L140M01a.cbRefinDt")
								+ "」為"
								+ lms140_cbRefinDt.get(Util.trim(l161s01a
										.getCbRefinDt())))

								.append(cbRefinDt_ElonErrMsg)
								.append(cbRefinDt_AlonErrMsg)
								.append("，恐與央行規定不符，請先" + chkCbRefinDtMsg
										+ "確認是否適用央行方案!").toString());
						temp.append("<br/>");
					}

				}
			}
		}

		if (UtilConstants.DEFAULT.是.equals(Util.trim(l140m01a.getIsOperationFee()))) {
			boolean showOpFeeMsg = false;
			String operationFeeWay = Util.trim(l140m01a.getOperationFeeWay());
			BigDecimal operationFeeAmt = l140m01a.getOperationFeeAmt() == null ?
					BigDecimal.ZERO : l140m01a.getOperationFeeAmt();
			String applyCurr = Util.trim(l161s01a.getCurrentApplyCurr());
			BigDecimal applyAmt = l161s01a.getCurrentApplyAmt() == null ?
					BigDecimal.ZERO : l161s01a.getCurrentApplyAmt();
			String opFeeCurr = Util.trim(l161s01a.getOperationFeeCurr());
			BigDecimal opFeeAmt = l161s01a.getOperationFeeAmt() == null ?
					BigDecimal.ZERO : l161s01a.getOperationFeeAmt();
			BigDecimal tempAmt = null;
			if (Util.equals(operationFeeWay, "1")) {
				if (Util.notEquals(applyCurr, "TWD") || Util.notEquals(opFeeCurr, "TWD")) {
					showOpFeeMsg = true;
				} else {
					tempAmt = applyAmt.multiply(BigDecimal.valueOf(0.0005)).setScale(0, BigDecimal.ROUND_HALF_UP);
					if (tempAmt.compareTo(new BigDecimal("1500")) >= 0) {
						if (opFeeAmt.compareTo(tempAmt) == 0) {

						} else {
							showOpFeeMsg = true;
						}
					} else {
						if (opFeeAmt.compareTo(new BigDecimal("1500")) == 0) {

						} else {
							showOpFeeMsg = true;
						}
					}
				}
			} else if (Util.equals(operationFeeWay, "2")) {
				if (Util.notEquals(opFeeCurr, "TWD")) {
					showOpFeeMsg = true;
				} else {
					if (operationFeeAmt.compareTo(opFeeAmt) == 0) {

					} else {
						showOpFeeMsg = true;
					}
				}
			}
			if (showOpFeeMsg) {
				if (temp.length() > 0) {
					temp.append("<br/>");
				}
				temp.append(prop.getProperty("L160M01A.message121"));
				temp.append("<br/>");
			}
		}

		if (temp.length() > 0) {
			// 尚有必填欄位未填
			temp.insert(0, prop.getProperty("L160M01A.message77") + "：<br/>");
		}

		return temp.toString();

	}

	private String isCheckData(Properties prop, L161S01A l161s01a,
			L160M01A l160m01a, L161S01C l161s01c, CapAjaxFormResult result) throws CapException {

		StringBuffer temp = new StringBuffer("");
		// 統計未填欄位數
		int countItme = 1;

		L140M01A l140m01a = lms1401Service.findL140m01aByMainId(l161s01a
				.getCntrMainId());

		if (l140m01a == null) {
			// 找不到額度明細表
			throw new CapMessageException(
					prop.getProperty("L160M01A.message66")
							+ l161s01a.getCntrNo(), getClass());
		}

		// 檢核欄位有沒有填入
		if (Util.equals(l161s01a.getUnitCase(), "Y")
				&& Util.equals(l161s01a.getUCntBranch(), "Y")) {
			// 幣別
			if (Util.isEmpty(Util.trim(l161s01a.getQuotaCurr()))) {
				countItme = this.setHtmlBr(temp, countItme,
						prop.getProperty("L160M01A.curr"));
			}
			// 簽約日期
			if (Util.isEmpty(Util.trim(l161s01a.getSignDate()))) {
				countItme = this.setHtmlBr(temp, countItme,
						prop.getProperty("L160M01A.signDate"));
			}
			// 總額度
			if (Util.isEmpty(Util.trim(l161s01a.getQuotaAmt()))) {
				countItme = this.setHtmlBr(temp, countItme,
						prop.getProperty("L160M01A.allMoney"));
			}

		} else {
			// 加強檢核案件性質為 1 2 時總額度一定要KEY
			if (Util.equals(l161s01a.getCaseType(),
					UtilConstants.Usedoc.caseType.同業聯貸主辦)
					|| Util.equals(l161s01a.getCaseType(),
							UtilConstants.Usedoc.caseType.同業聯貸主辦含自行聯貸)) {
				// 幣別
				if (Util.isEmpty(Util.trim(l161s01a.getQuotaCurr()))) {
					countItme = this.setHtmlBr(temp, countItme,
							prop.getProperty("L160M01A.curr"));
				}
				// 簽約日期
				if (Util.isEmpty(Util.trim(l161s01a.getSignDate()))) {
					countItme = this.setHtmlBr(temp, countItme,
							prop.getProperty("L160M01A.signDate"));
				}
				// 總額度
				if (Util.isEmpty(Util.trim(l161s01a.getQuotaAmt()))) {
					countItme = this.setHtmlBr(temp, countItme,
							prop.getProperty("L160M01A.allMoney"));
				}
			}
		}

		if (Util.equals(l161s01a.getIsDerivatives(), UtilConstants.DEFAULT.是)) {
			if (Util.isEmpty(Util.trim(l161s01a.getDervApplyAmtType()))) {
				countItme = this.setHtmlBr(temp, countItme,
						prop.getProperty("L160M01A.dervApplyAmtType"));
			}
		}

		// J-105-0135-001 Web e-Loan國內企金授信系統動審表，開放可修改振興經濟非中小企業專案貸款註記與金額。
		// J-108-0098-001 企金處移除振興經濟非中小企相關欄位
		// if (Util.equals(Util.trim(l161s01a.getIsNonSMEProjLoan()), "")) {
		// countItme = this.setHtmlBr(temp, countItme,
		// prop.getProperty("L140M01a.isNonSMEProjLoan"));
		// }
		// if (Util.equals(Util.trim(l161s01a.getIsNonSMEProjLoan()), "Y")) {
		// if (l161s01a.getNonSMEProjLoanAmt() == null) {
		// countItme = this.setHtmlBr(temp, countItme,
		// prop.getProperty("L140M01a.nonSMEProjLoanAmt"));
		// }
		// }

		// J-106-0082-001 Web e-Loan國內企金授信系統，額度明細表新增中小企業創新發展專案貸款
		if (Util.equals(Util.trim(l161s01a.getInSmeFg()), "")) {
			countItme = this.setHtmlBr(temp, countItme,
					prop.getProperty("L140M01a.inSmeFg"));
		}
		if (Util.equals(Util.trim(l161s01a.getInSmeFg()), "Y")) {
			if (l161s01a.getInSmeToAmt() == null) {
				countItme = this.setHtmlBr(temp, countItme,
						prop.getProperty("L140M01a.inSmeToAmt"));
			}
			if (l161s01a.getInSmeCaAmt() == null) {
				countItme = this.setHtmlBr(temp, countItme,
						prop.getProperty("L140M01a.inSmeCaAmt"));
			}
		}

		// J-109-0077_05097_B1001 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
		String isRescue = Util.trim(l161s01a.getIsRescue());
		if (Util.equals(isRescue, "")) {
			// L140M01a.isRescue=本案是否屬因應嚴重特殊傳染性肺炎影響事業資金紓困
			countItme = this.setHtmlBr(temp, countItme,
					prop.getProperty("L140M01a.isRescue"));
		}

		// J-109-0077_05097_B1003 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
		String rescueItem = "";
		String isCbRefin = "";
		BigDecimal rescueRate = null;
		String isExtendSixMon = "";
		Date rescueIbDate = null;
		BigDecimal rescueAmt = null;
		String rescueCurr = "";
		// J-109-0077_05097_B1005 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
		String rescueItemSub = "";
		// J-109-0077_05097_B1006 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
		Date rescueDate = null;
		String cbRefinDt = "";

		if (Util.equals(isRescue, "Y")) {
			rescueItem = Util.trim(l161s01a.getRescueItem());
			if (Util.equals(rescueItem, "")) {
				// L140M01a.rescueItem=紓困貸款類別
				countItme = this.setHtmlBr(temp, countItme,
						prop.getProperty("L140M01a.rescueItem"));
			}

			rescueDate = l161s01a.getRescueDate();
			if (rescueDate == null) {
				// L140M01a.rescueDate=受理日期
				countItme = this.setHtmlBr(temp, countItme,
						prop.getProperty("L140M01a.rescueDate"));
			}

			// J-109-0077_05097_B1003 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
			// J-112-0148 疫後振興不檢查
			if (!lmsService.isResueItemCaseF(rescueItem) && !lmsService.isResueItemCaseJ(rescueItem)
					&& !"K01".equals(rescueItem) && !lmsService.isResueItemCaseL(rescueItem)) {
				isCbRefin = Util.trim(l161s01a.getIsCbRefin());
				if (Util.equals(isCbRefin, "")) {
					// L140M01a.isCbRefin=本案資金來源是否為央行轉融通
					countItme = this.setHtmlBr(temp, countItme,
							prop.getProperty("L140M01a.isCbRefin"));
				}
			}

			// J-109-0811_05097_B1001
			// 配合「嚴重特殊傳染性肺炎防治及妤困振興特別條例」施行期間調整，動審表新增央行優惠利率融通期限
			if (Util.equals(isCbRefin, "Y")) {
				cbRefinDt = Util.trim(l161s01a.getCbRefinDt());
				if (Util.equals(cbRefinDt, "")) {
					// L140M01a.cbRefinDt=央行優惠利率融通期限
					countItme = this.setHtmlBr(temp, countItme,
							prop.getProperty("L140M01a.cbRefinDt"));
				}
			}

			// J-109-0077_05097_B1005 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
			rescueItemSub = Util.trim(l161s01a.getRescueItemSub());
			if (lmsService.needRescueItemSub(rescueItem)) {
				if (Util.equals(rescueItemSub, "")) {
					// L140M01a.rescueItemSub=合併申請紓困方案
					countItme = this.setHtmlBr(temp, countItme,
							prop.getProperty("L140M01a.rescueItemSub"));
				}
			}

			if (lmsService.isResueItemRescueRate(rescueItem, rescueItemSub)) {
				rescueRate = l161s01a.getRescueRate();
				if (rescueRate == null) {
					// L140M01a.rescueRate=減收利率
					countItme = this.setHtmlBr(temp, countItme,
							prop.getProperty("L140M01a.rescueRate"));
				}
			}

			// J-109-0077_05097_B1015 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
			if (lmsService.isResueItemNeedEmpCount(rescueItem, rescueItemSub)) {
				BigDecimal empCount = null;
				empCount = l161s01a.getEmpCount();
				if (empCount == null) {
					// L160M01A.empCount=現有員工人數
					countItme = this.setHtmlBr(temp, countItme,
							prop.getProperty("L160M01A.empCount"));
				} else {
					if (BigDecimal.ZERO.compareTo(empCount) > 0) {
						// L160M01A.empCount=現有員工人數
						countItme = this.setHtmlBr(temp, countItme,
								prop.getProperty("L160M01A.empCount"));
					}
				}
			}

			// J-110-0288_05097_B1003 Web e-Loan配合國發基金新創事業紓困加碼方案H01、A07專案修改
			// J-109-0077_05097_B1018 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業

			// J-110-0288_05097_B1003 Web
			// e-Loan配合國發基金新創事業紓困加碼方案H01、A07專案修改
			// J-109-0077_05097_B1018 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業

			// A07 Y, Z 可以不輸入掛件文號
			// Y.符合經濟部補助要點適用對象 中小企業 不符合紓困4.0 利息補貼 A07 =>營收減少15%為N
			// Z.符合經濟部補助要點適用對象 大型企業 A07 =>營收減少15%為X
			if (lmsService.isResueItemNeedRescueNo(rescueItem, rescueItemSub)) {
				String rescueNo = Util.trim(l161s01a.getRescueNo());
				if (Util.equals(rescueNo, "")) {
					if (lmsService
							.isResueItemNeedIsTurnoverDecreased(rescueItem)
							&& !Util.isEmpty(l161s01a.getIsTurnoverDecreased())) {

						// 洪真逢 10429/MEGABANK/MEGA (企金業務處)
						// 2021/08/13 上午 11:16
						// 建霖 您好
						// "國發基金紓困貸款"原先選擇 A07, 僅通知您 X 狀況需輸入掛件文號
						// 經洽經理銀行 , 麻煩您更改 X,Y,Z 狀況, 皆需 輸入 掛件文號

						// A07
						// if (Util.equals(
						// Util.trim(l161s01a.getIsTurnoverDecreased()),
						// "Y")) {
						// // 符合紓困4.0 利息補貼 A07一定要有掛件文號
						//
						// // L160M01A.rescueNo=掛件文號
						// countItme = this.setHtmlBr(temp, countItme,
						// prop.getProperty("L160M01A.rescueNo"));
						//
						// } else {
						// // A07 Y, Z 可以不輸入掛件文號
						// // Y.符合經濟部補助要點適用對象 中小企業 不符合紓困4.0
						// // 利息補貼 A07
						// // =>營收減少15%為N
						// // Z.符合經濟部補助要點適用對象 大型企業 A07
						// // =>營收減少15%為X
						// }

						if (!lms1401Service
								.isRescueItemCanEmptyRescueNo(rescueItem)) {
							countItme = this.setHtmlBr(temp, countItme,
									prop.getProperty("L160M01A.rescueNo"));
						}

					} else {
						// 非A07
						if (lmsService.isResueItemRescueRate(rescueItem,
								rescueItemSub)) {
							if (rescueRate != null
									&& rescueRate.compareTo(BigDecimal.ZERO) != 0) {

								if (!lms1401Service
										.isRescueItemCanEmptyRescueNo(rescueItem)) {
									// L160M01A.rescueNo=掛件文號
									countItme = this
											.setHtmlBr(
													temp,
													countItme,
													prop.getProperty("L160M01A.rescueNo"));
								}

							}
						} else {
							// L160M01A.rescueNo=掛件文號
							if (!lms1401Service
									.isRescueItemCanEmptyRescueNo(rescueItem)) {
								countItme = this.setHtmlBr(temp, countItme,
										prop.getProperty("L160M01A.rescueNo"));
							}

						}

					}

				}
			}

			if (lmsService.isResueItemOldCase(rescueItem) || rescueItem.matches("J04|J05")) {

				isExtendSixMon = Util.trim(l161s01a.getIsExtendSixMon());
				if (Util.equals(isExtendSixMon, "")) {
					// L140M01a.isExtendSixMon=本案是否展延六個月
					countItme = this.setHtmlBr(temp, countItme,
							prop.getProperty("L140M01a.isExtendSixMon"));
				}

				rescueIbDate = l161s01a.getRescueIbDate();
				if (rescueIbDate == null) {
					// L140M01a.rescueIbDate=合意展延日
					countItme = this.setHtmlBr(temp, countItme,
							prop.getProperty("L140M01a.rescueIbDate"));
				}

				rescueAmt = l161s01a.getRescueAmt();
				if (rescueAmt == null) {
					// L140M01a.rescueAmt=截至1090312前既有之本行貸款餘額
					// L140M01a.rescueAmt_A04=截至110.06.03前既有之本行貸款餘額
					String rescueAmtStr = prop
							.getProperty("L140M01a.rescueAmt");
					rescueAmtStr = rescueItem.matches("A04|A08|J04|J05") ?
							prop.getProperty("L140M01a.rescueAmt_" + rescueItem)
							: rescueAmtStr;
					countItme = this.setHtmlBr(temp, countItme, rescueAmtStr);
				}

				rescueCurr = Util.trim(l161s01a.getRescueCurr());
				if (Util.equals(rescueCurr, "")) {
					// L140M01a.rescueCurr=截至1090312前既有之本行貸款幣別
					countItme = this.setHtmlBr(temp, countItme,
							prop.getProperty("L140M01a.rescueCurr"));
				} else {
					if (rescueItem.matches("J04|J05")) {
						if (!"TWD".equals(rescueCurr)) {
							countItme = this.setHtmlBr(temp, countItme,
									prop.getProperty("L140M01a.rescueAmt_" + rescueItem) + " 幣別需為TWD");
						}
					}
				}

			}

			// J-110-0288_05097_B1001 Web
			// e-Loan配合辦理「行政院國家發展基金協助新創事業紓困融資加碼方案」，修改額度明細表欄位
			if (lmsService.isResueItemNeedNdfGutPercent(rescueItem)) {
				if (Util.isEmpty(l161s01a.getRescueNdfGutPercent())) {
					// L140M01a.rescueNdfGutPercent=國發基金加碼保證成數
					countItme = this.setHtmlBr(temp, countItme,
							prop.getProperty("L140M01a.rescueNdfGutPercent"));
				}
			}

			// J-112-0148_05097_B1002 Web
			// e-Loan企金授信新增經濟部協助中小型事業疫後振興專案貸款暨經濟部協助中小企業轉型發展專案貸款
			if (lms1601Service.isResueItemRescueSn(rescueItem)) {
				if (Util.isEmpty(l161s01a.getRescueSn())) {
					// L160M01A.rescueSn=額度編號
					countItme = this.setHtmlBr(temp, countItme,
							prop.getProperty("L160M01A.rescueSn"));
				}
			}

			// J-110-0288_05097_B1002 Web
			// e-Loan配合辦理「行政院國家發展基金協助新創事業紓困融資加碼方案」，修改額度明細表欄位
			if (lmsService.isResueItemNeedIsTurnoverDecreased(rescueItem)) {
				if (Util.isEmpty(l161s01a.getIsTurnoverDecreased())) {
					// L160M01A.isTurnoverDecreased=是否符合110年5~12月營業額減少達15%
					countItme = this.setHtmlBr(temp, countItme,
							prop.getProperty("L160M01A.isTurnoverDecreased"));
				}
			}

			// J-111-0214_05097_B1001 Web e-Loan國內企金動用審核表新增可適用新利率計算減免息相關功能
			if (lms1601Service.needRescueChgRateFg(rescueItem)) {

				String rescueChgRateFg = "";
				Date rescueChgRateSingDate = null;
				BigDecimal rescueChgRate = null;
				Date rescueChgRateEffectDate = null;

				rescueChgRateFg = Util.trim(l161s01a.getRescueChgRateFg());
				if (Util.equals(rescueChgRateFg, "")) {
					// L161S01A.rescueChgRateFg=客戶是否申請調整補貼利率
					countItme = this.setHtmlBr(temp, countItme,
							prop.getProperty("L161S01A.rescueChgRateFg"));
				}

				if (Util.equals(rescueChgRateFg, "Y")) {

					rescueChgRateSingDate = l161s01a.getRescueChgRateSingDate();
					if (rescueChgRateSingDate == null) {
						// L161S01A.rescueChgRateSingDate=簽約日(增補合約)
						countItme = this.setHtmlBr(temp, countItme, prop
								.getProperty("L161S01A.rescueChgRateSingDate"));
					}

					rescueChgRate = l161s01a.getRescueChgRate();
					if (rescueChgRate == null) {
						// L161S01A.rescueChgRate=調整後利率
						countItme = this.setHtmlBr(temp, countItme,
								prop.getProperty("L161S01A.rescueChgRate"));
					}

					// rescueChgRateEffectDate =
					// l161s01a.getRescueChgRateEffectDate();
					// if (rescueChgRateSingDate == null) {
					// // L161S01A.rescueChgRateEffectDate=客戶利率調升日
					// l161s01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);
					// }

				}

			}

			// J-111-0112_05097_B1001 Web e-Loan企金授信管理系統新增紓困代碼檢核
			if (LMSUtil.isContainValue(Util.trim(l140m01a.getProPerty()),
					UtilConstants.Cntrdoc.Property.新做)) {

				if (l140m01a != null) {
					// 這次的動審表額度動用資訊
					if (Util.equals(isRescue, "Y")) {
						String rescueItemExpiredMsg = lmsService
								.isRescueItemExpired(rescueItem,
										CapDate.getCurrentDate("yyyy-MM-dd"),
										"2");

						if (Util.notEquals(rescueItemExpiredMsg, "")) {

							countItme = this.setHtmlBr(temp, countItme,
									rescueItemExpiredMsg);

						}
					}
				}

			}

		}

		// J-109-0077_05097_B1008 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
		// 檢核本額度有無送保 Y/N（有/無）
		if (Util.isEmpty(Util.trim(l161s01a.getHeadItem1()))) {
			countItme = this.setHtmlBr(temp, countItme,
					prop.getProperty("L140M01a.headItem1"));
		}

		if (UtilConstants.DEFAULT.是.equals(l161s01a.getHeadItem1())) {
			// 檢核送保為Y要輸入保證成數
			// J-112-0366_12473_B1001 送保方式為批次保證時不檢核
			if (Util.isEmpty(l161s01a.getGutPercent()) && !"3".equals(Util.trim(l140m01a.getGutType()))) {
				countItme = this.setHtmlBr(temp, countItme,
						prop.getProperty("L140M01a.gratio"));
			}

			// 檢核送保為Y要輸入信保首次動用有效期限
			if (Util.isEmpty(l161s01a.getGutCutDate())) {
				countItme = this.setHtmlBr(temp, countItme,
						prop.getProperty("L140M01a.gutCutDate"));
			}

			// J-107-0137_05097_B1001 Web
			// e-Loan企金授信額度明細表，新增「信保基金保證書發文日期」與「信保基金核准之保證手續費率」
			// 檢核送保為Y要輸入信保基金核准之保證手續費率
			if (Util.isEmpty(l161s01a.getCgfRate())) {
				countItme = this.setHtmlBr(temp, countItme,
						prop.getProperty("L140M01a.cgfRate"));
			}

			// 檢核送保為Y要輸入信保基金保證書發文日期
			if (Util.isEmpty(l161s01a.getCgfDate())) {
				countItme = this.setHtmlBr(temp, countItme,
						prop.getProperty("L140M01a.cgfDate"));
			}

			// 檢核送保為Y要輸入本案是否為信保續約案
			if (Util.isEmpty(l161s01a.getIsGuaOldCase())) {
				countItme = this.setHtmlBr(temp, countItme,
						prop.getProperty("L140M01a.isGuaOldCase"));
			} else {
				if (Util.equals(l161s01a.getIsGuaOldCase(), "Y")) {
					// 檢核信保續約案為Y要輸入是否可借新還舊
					if (Util.isEmpty(l161s01a.getByNewOld())) {
						countItme = this.setHtmlBr(temp, countItme,
								prop.getProperty("L140M01a.byNewOld"));
					}
				}
			}

		}

		// J-110-0540_05097_B1001 Web e-Loan企金授信配合調整E-loan系統動用審核表部分內容
		if (Util.equals(Util.trim(l160m01a.getTType()), "3")) {
			// 動審表主檔授信期限選擇3.詳額度動用資訊一覽表

			// 檢核動用期限是否登錄
			if (Util.isEmpty(l161s01a.getTType_s01a())) {

				// L160M01A.tType= 授信契約書
				// L160M01A.guFromDate=期間
				countItme = this.setHtmlBr(
						temp,
						countItme,
						prop.getProperty("L160M01A.tType")
								+ prop.getProperty("L160M01A.guFromDate"));

			} else if ("1".equals(l161s01a.getTType_s01a())) {

				switch (Util.parseInt(l161s01a.getUseSelect_s01a())) {
				case 1:
					if (Util.isEmpty(l161s01a.getUseFromDate_s01a())
							|| Util.isEmpty(l161s01a.getUseEndDate_s01a())) {
						// L160M01A.useDate=動用期限
						countItme = this.setHtmlBr(temp, countItme,
								prop.getProperty("L160M01A.useDate"));

					}
					break;
				case 2:
					if (Util.isEmpty(l161s01a.getUseMonth_s01a())) {
						// L160M01A.useDate=動用期限
						countItme = this.setHtmlBr(temp, countItme,
								prop.getProperty("L160M01A.useDate"));

					}
					break;
				case 3:
					if (Util.isEmpty(l161s01a.getUseOther_s01a())) {
						// L160M01A.useDate=動用期限
						countItme = this.setHtmlBr(temp, countItme,
								prop.getProperty("L160M01A.useDate"));
					}
					break;
				default:
					// L160M01A.useDate=動用期限
					countItme = this.setHtmlBr(temp, countItme,
							prop.getProperty("L160M01A.useDate"));
				}
			} else if ("2".equals(l161s01a.getTType_s01a())) {

				switch (Util.parseInt(l161s01a.getUseSelect_s01a())) {
				case 1:
					if (Util.isEmpty(l161s01a.getUseFromDate_s01a())
							|| Util.isEmpty(l161s01a.getUseEndDate_s01a())) {
						// L160M01A.useDate=動用期限
						countItme = this.setHtmlBr(temp, countItme,
								prop.getProperty("L160M01A.useDate"));
					}

					break;
				case 2:
					if (Util.isEmpty(l161s01a.getUseMonth_s01a())) {
						// L160M01A.useDate=動用期限
						countItme = this.setHtmlBr(temp, countItme,
								prop.getProperty("L160M01A.useDate"));
						;
					}
					break;
				case 3:
					if (Util.isEmpty(l161s01a.getUseOther_s01a())) {
						// L160M01A.useDate=動用期限
						countItme = this.setHtmlBr(temp, countItme,
								prop.getProperty("L160M01A.useDate"));
					}
					break;
				default:
					// L160M01A.useDate=動用期限
					countItme = this.setHtmlBr(temp, countItme,
							prop.getProperty("L160M01A.useDate"));
				}

				switch (Util.parseInt(l161s01a.getLnSelect_s01a())) {
				case 1:
					if (Util.isEmpty(l161s01a.getLnFromDate_s01a())
							|| Util.isEmpty(l161s01a.getLnEndDate_s01a())) {
						// L160M01A.lnDate=授信期限
						countItme = this.setHtmlBr(temp, countItme,
								prop.getProperty("L160M01A.lnDate"));

					}

					break;
				case 2:
					if (Util.isEmpty(l161s01a.getLnMonth_s01a())
							|| Util.isEmpty(l161s01a.getLnYear_s01a())) {
						// L160M01A.lnDate=授信期限
						countItme = this.setHtmlBr(temp, countItme,
								prop.getProperty("L160M01A.lnDate"));
					}
					break;
				case 3:
					if (Util.isEmpty(Util.trim(l161s01a.getLnOther_s01a()))) {
						// L160M01A.lnDate=授信期限
						countItme = this.setHtmlBr(temp, countItme,
								prop.getProperty("L160M01A.lnDate"));
					}
					break;
				default:
					// L160M01A.lnDate=授信期限
					countItme = this.setHtmlBr(temp, countItme,
							prop.getProperty("L160M01A.lnDate"));
				}
			}
		}

		// J-111-0506_05097_B1001 Web e-Loan企金授信動審表增加授信作業手續費之欄位
		if (Util.isEmpty(Util.trim(l161s01a.getIsOperationFee()))) {
			// L160M01A.isOperationFee=是否收取授信作業手續費
			countItme = this.setHtmlBr(temp, countItme,
					prop.getProperty("L160M01A.isOperationFee"));
		}

		// J-111-0506_05097_B1001 Web e-Loan企金授信動審表增加授信作業手續費之欄位
		if (UtilConstants.DEFAULT.是.equals(l161s01a.getIsOperationFee())) {
			// 檢核是否收取授信作業手續費為Y要輸入收取幣別 金額 最晚收取日
			if (Util.isEmpty(l161s01a.getOperationFeeCurr())) {
				// L160M01A.operationFeeCurr=收取幣別
				countItme = this.setHtmlBr(temp, countItme,
						prop.getProperty("L160M01A.operationFeeCurr"));
			}

			if (Util.isEmpty(l161s01a.getOperationFeeAmt())) {
				// L160M01A.operationFeeAmt=收取金額
				countItme = this.setHtmlBr(temp, countItme,
						prop.getProperty("L160M01A.operationFeeAmt"));
			}

			if (l161s01a.getOperationFeeDueDate() == null) {
				// L160M01A.operationFeeDueDate=最晚收取日
				countItme = this.setHtmlBr(temp, countItme,
						prop.getProperty("L160M01A.operationFeeDueDate"));
			}
		}

		if (temp.length() > 0) {
			// 尚有必填欄位未填
			temp.insert(0, prop.getProperty("L160M01A.message75") + "<br/>");
		}

		// ----------以下檢核邏輯-------------

		// 檢核現請額度不得超過額度明細表
		if (l161s01a.getCurrentApplyAmt().compareTo(
				l140m01a.getCurrentApplyAmt()) > 0) {
			temp.append("<br/>");
			// 動審表現請額度金額{0}不得大於額度明細表{1}
			temp.append(MessageFormat.format(
					prop.getProperty("L160M01A.message68"),
					new StringBuffer().append(l161s01a.getCurrentApplyAmt()),
					new StringBuffer().append(l140m01a.getCurrentApplyAmt()))
					.toString());
		}

		// 有同業且為帳務管理行，1.檢核一定要同業參貸比率 2.檢核總額度與明細是否一致

		// boolean hasUnitMega = false;
		// boolean hasUnitBank = false;

		int countUnitMega = 0;
		int countUnitBank = 0;

		String hasKeyL161S01B = "N";
		BigDecimal countAll = BigDecimal.ZERO;
		BigDecimal countMega = BigDecimal.ZERO;
		String hasMainFlag = "N";
		String slBankType = "";
		String slBank = "";
		String slMaster = "";

		Set<L161S01B> l161s01bs = l161s01a.getL161s01b();

		if (l161s01bs != null && !l161s01bs.isEmpty()) {
			for (L161S01B l161s01b : l161s01bs) {

				BigDecimal tSlAmt = l161s01b.getSlAmt() == null ? BigDecimal.ZERO
						: l161s01b.getSlAmt();

				if (Util.equals(l161s01b.getSlBank(), UtilConstants.兆豐銀行代碼)) {
					// hasUnitMega = true;
					countUnitMega = countUnitMega + 1;
				} else {
					// hasUnitBank = true;
					countUnitBank = countUnitBank + 1;
				}

				if (hasKeyL161S01B.equals("N")) {
					hasKeyL161S01B = "Y";
				}

				countAll = countAll.add(tSlAmt);

				if (Util.equals(l161s01b.getSlBank(), UtilConstants.兆豐銀行代碼)) {
					countMega = countMega.add(tSlAmt);
				}

				slBankType = l161s01b.getSlBankType();
				slBank = l161s01b.getSlBank();
				slMaster = l161s01b.getSlMaster();
				if (Util.equals(slBankType, "01")
						&& Util.equals(slBank, UtilConstants.兆豐銀行代碼)) {
					if (Util.equals(slMaster, "Y")) {
						hasMainFlag = "Y";
					}
				}

			}
		}

		if (Util.equals(hasMainFlag, "Y")) {
			if (Util.equals(l161s01a.getCaseType(),
					UtilConstants.Usedoc.caseType.同業聯貸參貸)
					|| Util.equals(l161s01a.getCaseType(),
							UtilConstants.Usedoc.caseType.同業聯貸參貸含自行聯貸)) {
				temp.append("<br/>");
				// L160M01A.message91=聯貸案參貸比率一覽表兆豐分行有勾選為共同主辦，基本資訊頁籤-「案件性質」欄位則必須為主辦
				temp.append(prop.getProperty("L160M01A.message91"));

			}
		}

		if (Util.equals(l161s01a.getUnitCase(), "Y")
				|| Util.equals(l161s01a.getCaseType(),
						UtilConstants.Usedoc.caseType.同業聯貸主辦)
				|| Util.equals(l161s01a.getCaseType(),
						UtilConstants.Usedoc.caseType.同業聯貸主辦含自行聯貸)) {
			// 檢查輸入的總額度與聯行攤貸總額金額是否一樣
			BigDecimal tQuotaAmt = l161s01a.getQuotaAmt() == null ? BigDecimal.ZERO
					: l161s01a.getQuotaAmt();
			if (tQuotaAmt.compareTo(BigDecimal.ZERO) != 0) {
				// String caseType = l161s01a.getCaseType();
				// if (!(Util.equals(caseType,
				// UtilConstants.Usedoc.caseType.同業聯貸主辦) || Util.equals(
				// caseType, UtilConstants.Usedoc.caseType.同業聯貸主辦含自行聯貸))) {
				if (Util.notEquals(l161s01a.getQuotaCurr(),
						l161s01a.getCurrentApplyCurr())) {
					temp.append("<br/>");
					// 同業聯貸總額度幣別必須與額度明細表現請額度幣別一致
					temp.append(prop.getProperty("L160M01A.message85"));
				}

				// }
			}
		}

		if ((Util.equals(l161s01a.getUnitCase(), "Y") && Util.equals(
				l161s01a.getUCntBranch(), "Y"))
				|| Util.equals(l161s01a.getCaseType(),
						UtilConstants.Usedoc.caseType.同業聯貸主辦)
				|| Util.equals(l161s01a.getCaseType(),
						UtilConstants.Usedoc.caseType.同業聯貸主辦含自行聯貸)) {
			if (countUnitBank == 0) {
				// 【本案是否有同業聯貸案額度】及【本行是否為額度管理行】皆為是，則聯貸案參貸比率一覽表中必須要有同業參貸資料
				temp.append("<br/>");
				temp.append(prop.getProperty("L160M01A.message69"));
			}
		}

		if (Util.equals(l161s01a.getUnitMega(), "Y")) {
			if (countUnitMega <= 1) {
				// 【本案是否有聯行攤貸額度】為是，則聯貸案參貸比率一覽表中必須要有兩筆(含)以上分行的攤貸資料
				temp.append("<br/>");
				temp.append(prop.getProperty("L160M01A.message70"));
			}

		}

		if (Util.equals(l161s01a.getUnitCase(), "N")
				&& Util.equals(l161s01a.getUnitMega(), "Y")) {
			if (countUnitBank > 0) {
				// 「本案是否有同業聯貸案額度」為否，則聯貸案參貸比率一覽表中不得有同業參貸資料
				temp.append("<br/>");
				temp.append(prop.getProperty("L160M01A.message86"));
			}
		}

		if (Util.equals(l161s01a.getUnitCase(), "Y")
				|| Util.equals(l161s01a.getCaseType(),
						UtilConstants.Usedoc.caseType.同業聯貸主辦)
				|| Util.equals(l161s01a.getCaseType(),
						UtilConstants.Usedoc.caseType.同業聯貸主辦含自行聯貸)) {
			// 檢查輸入的總額度與聯行攤貸總額金額是否一樣
			BigDecimal tQuotaAmt = l161s01a.getQuotaAmt() == null ? BigDecimal.ZERO
					: l161s01a.getQuotaAmt();
			if (countUnitBank > 0 || tQuotaAmt.compareTo(BigDecimal.ZERO) != 0) {
				if (countAll.compareTo(tQuotaAmt) != 0) {
					// L160M01A.message30=「聯貸案參貸比率一覽表」，所有參貸金額加總後({0})與總額度({1})不相等。
					temp.append("<br/>");
					temp.append(MessageFormat.format(
							prop.getProperty("L160M01A.message71"),
							new StringBuffer().append(countAll).toString(),
							new StringBuffer().append(tQuotaAmt).toString()));
				}
			}

		}

		// 有自行聯貸時，檢核017分行的比率金額要等於動審表之現請額度
		if (Util.equals(l161s01a.getUnitMega(), "Y")
				|| Util.equals(l161s01a.getCaseType(),
						UtilConstants.Usedoc.caseType.同業聯貸參貸含自行聯貸)
				|| Util.equals(l161s01a.getCaseType(),
						UtilConstants.Usedoc.caseType.同業聯貸主辦含自行聯貸)
				|| Util.equals(l161s01a.getCaseType(),
						UtilConstants.Usedoc.caseType.自行聯貸)) {
			boolean chgRate = false; // 當現請額度幣別與同業參貸總額度幣別不一致時，以同業參貸總額度幣別為準，此時

			if (Util.equals(l161s01a.getUnitCase(), "Y")) {
				BigDecimal tQuotaAmt = l161s01a.getQuotaAmt() == null ? BigDecimal.ZERO
						: l161s01a.getQuotaAmt();

				if (tQuotaAmt.compareTo(BigDecimal.ZERO) != 0) {
					if (Util.notEquals(l161s01a.getQuotaCurr(),
							l161s01a.getCurrentApplyCurr())) {
						chgRate = true;
					}
				}
			}

			BigDecimal tCurrentApplyAmt = l161s01a.getCurrentApplyAmt() == null ? BigDecimal.ZERO
					: l161s01a.getCurrentApplyAmt();

			if (chgRate == true) {
				BranchRate branchRate = lmsService.getBranchRate(l160m01a
						.getCaseBrId());
				BigDecimal chgRateCurrentApplyAmt = branchRate.toOtherAmt(
						l161s01a.getCurrentApplyCurr(),
						l161s01a.getQuotaCurr(), tCurrentApplyAmt).setScale(0,
						BigDecimal.ROUND_UP);
				if (countMega.compareTo(chgRateCurrentApplyAmt) > 0) {
					// L160M01A.message84=「聯貸案參貸比率一覽表」，本行聯行攤貸金額加總後({0})({1})不得大於動審表現請額度換算後({2})({3})
					temp.append("<br/>");
					temp.append(MessageFormat.format(
							prop.getProperty("L160M01A.message84"),
							l161s01a.getQuotaCurr(),
							new StringBuffer().append(countMega),
							l161s01a.getQuotaCurr(),
							new StringBuffer().append(chgRateCurrentApplyAmt))
							.toString());
				}
			} else {
				if (Util.equals(l161s01a.getCntrNo().substring(0, 3), l140m01a.getCntrNo().substring(0, 3))) {//額度管理行
					if (countMega.compareTo(tCurrentApplyAmt) != 0) {
						// 「聯貸案參貸比率一覽表」，本行聯貸金額加總後({0})必須等於動審表之現請額度({1})。
						temp.append("<br/>");
						temp.append(MessageFormat.format(
								prop.getProperty("L160M01A.message72"),
								new StringBuffer().append(countMega),
								new StringBuffer().append(l140m01a
										.getCurrentApplyAmt())).toString());
					}
				}else{//非額度管理行 <=
					if (tCurrentApplyAmt.compareTo(countMega) > 0) {
						// 「聯貸案參貸比率一覽表」，動審表之現請額度({0})必須小於等於本行聯貸金額加總後({1})。
						temp.append("<br/>");
						temp.append(MessageFormat.format(
								prop.getProperty("L160M01A.message120"),
								new StringBuffer().append(countMega),
								new StringBuffer().append(tCurrentApplyAmt),
								new StringBuffer().append(countMega))).toString();//原本放的是額度明細表現請額度, 改成動審表
					}
				}
			}
		}

		// 國內授信才檢查020 存在此額度序號其額度種類要和020相同，海外不檢核
		String snoKind = Util.trim(l161s01a.getSnoKind());
		if (Util.isNotEmpty(snoKind)) {
			String factType = misMISLN20Service.findFactType(
					l161s01a.getCustId(), l161s01a.getDupNo(),
					l161s01a.getCntrNo());
			if ("51".equals(factType)) {
				factType = UtilConstants.Cntrdoc.snoKind.一般;
			} else if ("50".equals(factType)) {
				factType = UtilConstants.Cntrdoc.snoKind.信保;
			}
			// 2013-06-14,Rex, edit 修 改當額度控管種類不存在 於020 才需檢查020
			if (Util.isNotEmpty(factType) && !snoKind.equals(factType)) {
				if (Util.equals(Util.trim(l140m01a.getIsEfin()), "Y")) {
					if (Util.equals(factType.subSequence(0, 1), "4")
							&& Util.equals(snoKind.subSequence(0, 1), "6")) {
						// J-104-0284-001 額度明細表檢核供應鏈融資賣放限週轉科目
						// 供應鏈融資允許ALOAN 是 40/41 但 ELOAN是 60/61/62
						// OK
					} else {
						Map<String, String> codeMap_tmp = codeTypeService
								.findByCodeType("lms1405m01_snoKind");
						// L140M01a.message90=額度明細表之額度控管種類『{0}』與a-Loan『{1}』不同!!
						String msg = MessageFormat.format(
								prop.getProperty("L160M01A.message87"),
								codeMap_tmp.get(snoKind),
								codeMap_tmp.get(factType));

						temp.append("<br/>");
						temp.append(msg);
					}
				} else {
					Map<String, String> codeMap_tmp = codeTypeService
							.findByCodeType("lms1405m01_snoKind");
					// L140M01a.message90=額度明細表之額度控管種類『{0}』與a-Loan『{1}』不同!!
					String msg = MessageFormat
							.format(prop.getProperty("L160M01A.message87"),
									codeMap_tmp.get(snoKind),
									codeMap_tmp.get(factType));

					temp.append("<br/>");
					temp.append(msg);
				}

			}
		}

		// 額度明細表為供應鏈融資，則動審表額度控管種類必須為6開頭
		if (Util.equals(Util.trim(l140m01a.getIsEfin()), "Y")
				&& (Util.equals(
						Util.trim(l140m01a.getSnoKind()).subSequence(0, 1), "4") || Util
						.equals(Util.trim(l140m01a.getSnoKind()).subSequence(0,
								1), "6"))) {

			if (!Util.equals(Util.trim(snoKind).substring(0, 1), "6")) {
				// L140M01a.message165=額度明細表->申請內容->額度控管種類第一碼必須為6!!
				// Properties cntrProp = MessageBundleScriptCreator
				// .getComponentResource(LMS1401S02Page.class);
				// L160M01A.message101=額度明細表->申請內容->額度控管種類->欄位「是否為供應鏈融資」為是，動審表->基本資訊->額度控管種類->第一碼必須為6(EX:60、61、62)
				temp.append("<br/>");
				temp.append(prop.getProperty("L160M01A.message101"));
			}

		}

		// J-103-0202-005 Web e-Loan授信簽案衍生性金融商品遠匯與換匯科目，改以交易額度來簽案。
		if (Util.equals(l161s01a.getIsDerivatives(), UtilConstants.DEFAULT.是)) {
			if (Util.equals(l161s01a.getSnoKind(),
					UtilConstants.Cntrdoc.snoKind.聯貸)
					|| Util.equals(l161s01a.getSnoKind(),
							UtilConstants.Cntrdoc.snoKind.應收帳款賣方聯貸母戶)) {

				ArrayList<String> itemsAll = new ArrayList<String>();
				List<L140M01C> l140m01cs = lms1401Service
						.findL140m01cListByMainId(l140m01a.getMainId());

				if (l140m01cs != null && !l140m01cs.isEmpty()) {
					for (L140M01C l140m01c : l140m01cs) {
						itemsAll.add(l140m01c.getLoanTP());
					}

					if (lmsService.hasFxSubject(itemsAll
							.toArray(new String[itemsAll.size()]))) {
						temp.append("<br/>");
						// L160M01A.message90=遠匯、換匯科目額度控管種類不得為聯貸
						temp.append(prop.getProperty("L160M01A.message90")
								+ "－" + l161s01a.getCntrNo());
					}

				} else {
					temp.append("<br/>");
					// 找不到額度明細表
					temp.append(prop.getProperty("L160M01A.message66")
							+ l161s01a.getCntrNo());

				}

			}
		}

		// J-103-0317-001 Web e-Loan企金簽報書上傳承諾事項與追蹤檢視日期

		if (Util.notEquals(l161s01c.getItemDscr(), "")) {
			if (Util.equals(l161s01a.getReViewDateKind(), "")) {
				temp.append("<br/>");
				// L160M01A.message92=動撥提醒資訊-承諾事項-下次檢視日期選項不得為空白
				temp.append(prop.getProperty("L160M01A.message92"));// prop.getProperty("L160M01A.message66")+
																	// l161s01a.getCntrNo());
			}

			if (Util.equals(l161s01a.getReViewDateKind(), "01")) {
				Date reviewDate = l161s01a.getReViewDate();
				if (Util.isEmpty(reviewDate)) {
					temp.append("<br/>");
					// L160M01A.message93=動撥提醒資訊-承諾事項-下次檢視日期不得為空白
					temp.append(prop.getProperty("L160M01A.message93"));// prop.getProperty("L160M01A.message66")+
																		// l161s01a.getCntrNo());
				}

				if (Util.equals(l161s01a.getReViewChgKind(), "")) {
					temp.append("<br/>");
					// L160M01A.message94=動撥提醒資訊-承諾事項-檢視週期選項不得為空白
					temp.append(prop.getProperty("L160M01A.message94"));// prop.getProperty("L160M01A.message66")+
																		// l161s01a.getCntrNo());
				}

				if (Util.equals(l161s01a.getReViewChgKind(), "01")) {
					if (Util.equals(l161s01a.getReViewChg1(), "")) {
						temp.append("<br/>");
						// L160M01A.message95=動撥提醒資訊-承諾事項-檢視週期-月 欄位不得空白
						temp.append(prop.getProperty("L160M01A.message95"));// prop.getProperty("L160M01A.message66")+
																			// l161s01a.getCntrNo());
					}
				}

			}

		} else {
			if (Util.equals(l161s01a.getReViewDateKind(), "01")) {
				Date reviewDate = l161s01a.getReViewDate();
				if (Util.isEmpty(reviewDate)) {
					temp.append("<br/>");
					// L160M01A.message93=動撥提醒資訊-承諾事項-下次檢視日期不得為空白
					temp.append(prop.getProperty("L160M01A.message93"));// prop.getProperty("L160M01A.message66")+
																		// l161s01a.getCntrNo());
				}

				if (Util.equals(l161s01a.getReViewChgKind(), "")) {
					temp.append("<br/>");
					// L160M01A.message94=動撥提醒資訊-承諾事項-檢視週期選項不得為空白
					temp.append(prop.getProperty("L160M01A.message94"));// prop.getProperty("L160M01A.message66")+
																		// l161s01a.getCntrNo());
				}

				if (Util.equals(l161s01a.getReViewChgKind(), "01")) {
					if (Util.equals(l161s01a.getReViewChg1(), "")) {
						temp.append("<br/>");
						// L160M01A.message95=動撥提醒資訊-承諾事項-檢視週期-月 欄位不得空白
						temp.append(prop.getProperty("L160M01A.message95"));// prop.getProperty("L160M01A.message66")+
																			// l161s01a.getCntrNo());
					}
				}

			}
		}

		// J-105-0135-001 Web e-Loan國內企金授信系統動審表，開放可修改振興經濟非中小企業專案貸款註記與金額。
		// J-108-0098-001 企金處移除振興經濟非中小企相關欄位
		// if (Util.equals(Util.trim(l161s01a.getIsNonSMEProjLoan()), "Y")) {
		// if (l161s01a.getNonSMEProjLoanAmt() != null) {
		// // 檢核現請額度不得超過額度明細表
		// if (l161s01a.getCurrentApplyAmt().compareTo(
		// l161s01a.getNonSMEProjLoanAmt()) < 0) {
		// temp.append("<br/>");
		// // L160M01A.message102=基本資訊頁籤之欄位「屬振興經濟非中小企業專案貸款金額」不得大於現請額度。
		// temp.append(prop.getProperty("L160M01A.message102"));
		//
		// }
		// }
		// }

		// J-106-0082-001 Web e-Loan國內企金授信系統，額度明細表新增中小企業創新發展專案貸款
		if (Util.equals(Util.trim(l161s01a.getInSmeFg()), "Y")) {
			if (l161s01a.getCurrentApplyAmt() != null) {
				if (l161s01a.getInSmeToAmt() != null
						&& l161s01a.getInSmeCaAmt() != null) {
					if (l161s01a.getCurrentApplyAmt().compareTo(
							(l161s01a.getInSmeToAmt().add(l161s01a
									.getInSmeCaAmt()))) < 0) {
						temp.append("<br/>");
						// L160M01A.message104=基本資訊頁籤之欄位中小企業創新發展專案貸款「週轉性支出金額+資本性支出金額」不得大於「現請額度」。
						temp.append(prop.getProperty("L160M01A.message104"));

					}

					// 現請額度大於0且為中小企業創新發展專案貸款，則週轉性支出金額+資本性支出金額不能為0
					if (l161s01a.getCurrentApplyAmt()
							.compareTo(BigDecimal.ZERO) > 0
							&& (l161s01a.getInSmeToAmt().add(l161s01a
									.getInSmeCaAmt()))
									.compareTo(BigDecimal.ZERO) <= 0) {
						temp.append("<br/>");
						// L160M01A.message106=額度明細表->申請內容->中小企業創新發展專案貸款「週轉性支出金額+資本性支出金額」不得為0。
						temp.append(prop.getProperty("L160M01A.message106"));
					}
				}
			}
		}

		// J-106-0082-001 Web e-Loan國內企金授信系統，額度明細表新增中小企業創新發展專案貸款
		// 判斷與額度明細表是否一致
		if (Util.notEquals(Util.trim(l140m01a.getInSmeFg()), "")) {
			if (Util.notEquals(Util.trim(l140m01a.getInSmeFg()),
					Util.trim(l161s01a.getInSmeFg()))) {
				temp.append("<br/>");
				// L160M01A.message105=基本資訊頁籤之欄位「本案是否為中小企業創新發展專案貸款」與額度明細表不同。
				temp.append(prop.getProperty("L160M01A.message105"));
			}
		}

		// J-109-0077_05097_B1001 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
		// 若額度明細表有，則動審表要與額度明細表一致，否則要退回簽報書修改
		String isRescue_l140 = Util.trim(l140m01a.getIsRescue());
		if (Util.notEquals(isRescue_l140, "")) {

			if (lmsService.isNeedChkIsRescueSameL140m01a(rescueItem)) {
				// 額度明細表有值
				if (Util.notEquals(isRescue_l140, isRescue)) {
					temp.append("<br/>");
					temp.append("欄位「本案是否屬因應嚴重特殊傳染性肺炎影響事業資金紓困」內容(" + isRescue
							+ ")與額度明細表" + isRescue_l140 + ")不一致");
				}

				if (Util.equals(isRescue_l140, "Y")) {

					if (lmsService.isNeedChkResueItemSameL140m01a(rescueItem)) {
						// 額度明細表為是，要多判斷紓困貸款種類
						rescueItem = Util.trim(l161s01a.getRescueItem());
						String rescueItem_l140 = Util.trim(l140m01a
								.getRescueItem());
						if (Util.notEquals(rescueItem_l140, "")) {
							if (Util.notEquals(rescueItem_l140, rescueItem)) {
								temp.append("<br/>");
								temp.append("欄位「紓困貸款類別」內容(" + rescueItem
										+ ")與額度明細表(" + rescueItem_l140 + ")不一致");
							}
						}
					}

				}
			}
		}

		// J-109-0077_05097_B1003 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
		if (lmsService.isResueItemOldCase(rescueItem)) {
			// 舊貸(已辦理貸款)資訊
			if (Util.notEquals(isExtendSixMon, "Y")) {
				temp.append("<br/>");
				temp.append("紓困貸款類別「"
						+ rescueItem
						+ "」屬政府舊貸(已辦理貸款)紓困方案，欄位「是否展延到期日(原則短期半年/中長期寬限1年)」必須為「是」。");
			}

		}
		// J-109-0077_05097_B1003 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
		if (Util.equals(isCbRefin, "Y")) {
			if (Util.notEquals(l161s01a.getCurrentApplyCurr(), "TWD")) {
				temp.append("<br/>");
				temp.append("本案資金來源為央行轉融通資金專案,現請額度幣別僅能敘作新台幣。");
			}
		}
		// J-109-0077_05097_B1001 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
		// 若額度明細表有，則動審表要與額度明細表一致，否則要退回簽報書修改
		String isCbRefin_l140 = Util.trim(l140m01a.getIsCbRefin());
		if (Util.notEquals(isCbRefin, "") && Util.notEquals(isCbRefin_l140, "")) {
			// 額度明細表有值
			if (Util.notEquals(isCbRefin_l140, isCbRefin)) {
				if (lmsService.isNeedChkIsCbRefinSameL140m01a(rescueItem)) {
					temp.append("<br/>");
					temp.append("欄位「本案資金來源是否為央行轉融通資金專案」內容(" + isCbRefin
							+ ")與額度明細表(" + isCbRefin_l140 + ")不一致");
				}

			}
		}

		// J-109-0077_05097_B1001 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
		// 若額度明細表有，則動審表要與額度明細表一致，否則要退回簽報書修改
		String headItem1 = Util.trim(l161s01a.getHeadItem1());
		String headItem1_l140 = Util.trim(l140m01a.getHeadItem1());
		if (Util.notEquals(headItem1, "") && Util.notEquals(headItem1_l140, "")) {
			// 額度明細表有值
			if (Util.notEquals(headItem1_l140, headItem1)) {
				if (lmsService.isNeedChkHeadItem1SameL140m01a(rescueItem)) {
					temp.append("<br/>");
					temp.append("欄位「本案是否移送信保基金保證」內容(" + headItem1 + ")與額度明細表("
							+ headItem1_l140 + ")不一致");
				}

			}
		}

		// J-109-0077_05097_B1017 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
		if (lmsService.isResueItemRescueRate(rescueItem, rescueItemSub)) {
			rescueRate = l161s01a.getRescueRate();
			if (lmsService.isResueItemRescueRateNotZero(rescueItem,
					rescueItemSub)) {
				if (rescueRate != null
						&& BigDecimal.ZERO.compareTo(rescueRate) >= 0) {
					// L140M01a.rescueRate=減收利率
					temp.append("<br/>");
					temp.append("紓困案之減收利率/補貼利率應大於0");
				}

			}

			// J-110-0288_05097_B1003 Web e-Loan配合國發基金新創事業紓困加碼方案H01、A07專案修改
			// 國發基金新創事業紓困加碼方案H01、A07專案(補充)

			// J-109-0077_05097_B1017 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
			// 檢核紓困貸款類別利率不得超過
			// 預設一定都不能超過於上限
			if (rescueRate != null) {
				String chkRateLessError = lmsService
						.chkRescueItemRescueRateLess(rescueItem, rescueItemSub,
								rescueRate);
				if (Util.notEquals(chkRateLessError, "")) {
					temp.append("<br/>");
					temp.append(chkRateLessError);
				}
			}

			// J-110-0288_05097_B1003 Web e-Loan配合國發基金新創事業紓困加碼方案H01、A07專案修改
			if (lmsService.isResueItemNeedIsTurnoverDecreased(rescueItem)
					&& !Util.isEmpty(l161s01a.getIsTurnoverDecreased())) {
				if (Util.equals(Util.trim(l161s01a.getIsTurnoverDecreased()),
						"Y")) {
					// 中小企業符合紓困4.0利息補貼
					// 【A07】X

					// 必須輸入欄位 P7 or 0

					// J-109-0077_05097_B1017 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
					// 檢核紓困貸款類別利率不得低於
					if (rescueRate != null
							&& rescueRate.compareTo(BigDecimal.ZERO) != 0) {
						String chkRateLessError2 = lmsService
								.chkRescueItemRescueRateHigh(rescueItem,
										rescueItemSub, rescueRate);
						if (Util.notEquals(chkRateLessError2, "")) {
							temp.append("<br/>");
							temp.append(chkRateLessError2);
						}
					}

				} else if (Util.equals(
						Util.trim(l161s01a.getIsTurnoverDecreased()), "N")
						|| Util.equals(
								Util.trim(l161s01a.getIsTurnoverDecreased()),
								"X")) {
					// 中小企業不符合紓困4.0 利息補貼
					// 大型企業
					// 【A07】Y

					// 必須輸入欄位 P7

					// J-110-0288_05097_B1002 Web
					// e-Loan配合國發基金新創事業紓困加碼方案H01、A07專案修改
					// 檢核紓困貸款類別利率不得低於
					if (rescueRate != null) {
						String chkRateLessError2 = lmsService
								.chkRescueItemRescueRateHigh(rescueItem,
										rescueItemSub, rescueRate);
						if (Util.notEquals(chkRateLessError2, "")) {
							temp.append("<br/>");
							temp.append(chkRateLessError2);
						}
					}

				}

			} else {
				// J-110-0288_05097_B1002 Web e-Loan配合國發基金新創事業紓困加碼方案H01、A07專案修改
				// 檢核紓困貸款類別利率不得低於
				if (rescueRate != null) {
					String chkRateLessError2 = lmsService
							.chkRescueItemRescueRateHigh(rescueItem,
									rescueItemSub, rescueRate);
					if (Util.notEquals(chkRateLessError2, "")) {
						temp.append("<br/>");
						temp.append(chkRateLessError2);
					}
				}

			}

		}

		if (Util.equals(isRescue, "Y")) {
			if (lmsService.isResueItemNeedRescueNo(rescueItem, rescueItemSub)) {
				String rescueNo = Util.trim(l161s01a.getRescueNo());
				if (Util.notEquals(rescueNo, "")) {

					// 琬雅，上午 10:52
					// 分行是說要不要開放檢核,補貼利率0,不檢核掛件文號
					if (lmsService.isResueItemRescueRate(rescueItem,
							rescueItemSub)) {
						if (rescueRate != null
								&& rescueRate.compareTo(BigDecimal.ZERO) != 0) {
							// 2021/06/22 何忻蓓，下午 05:44 分行說他有跟台企確認今年編號開頭都是4耶
							// 行員編號 005231 提問單位 034-永和分行
							if (!CapString.checkRegularMatch(rescueNo,
									"^[a-zA-Z4]{1}[a-zA-Z0-9]+$")) {
								temp.append("<br/>");
								// L160M01A.message112=掛件文號格式錯誤 ，僅能輸入 英文字母
								// 與數字，且第一碼必須為英文。
								temp.append(prop
										.getProperty("L160M01A.message112"));
							}
						}
					} else {
						if (!CapString.checkRegularMatch(rescueNo,
								"^[a-zA-Z4]{1}[a-zA-Z0-9]+$")) {
							temp.append("<br/>");
							// L160M01A.message112=掛件文號格式錯誤 ，僅能輸入 英文字母
							// 與數字，且第一碼必須為英文。
							temp.append(prop.getProperty("L160M01A.message112"));
						}
					}

				}
			}

			// J-110-0465_05097_B1001 Web e-Loan國內企金授信動審表新增額度編號
			// J-112-0148_05097_B1002 Web
			// e-Loan企金授信新增經濟部協助中小型事業疫後振興專案貸款暨經濟部協助中小企業轉型發展專案貸款
			if (lms1601Service.isResueItemRescueSn(rescueItem)) {
				String rescueSn = Util.trim(l161s01a.getRescueSn());
				if (Util.notEquals(rescueSn, "")) {

					if (lmsService.isResueItemNeedNdfGutPercent(rescueItem)) {

						// A07
						// NBP-0148
						if (!CapString.checkRegularMatch(rescueSn,
								"^[a-zA-Z4]{3}-[0-9]{4}+$")) {
							temp.append("<br/>");
							temp.append("額度編號格式錯誤，正確格式為「3碼英文字」+「-」+「4碼數字」，如NBP-0001");
						}
					} else {
						// NBP-0148
						// GGGGGGGGGGGGGGGGGGGGGGG
						/*
						 * if (!CapString.checkRegularMatch(rescueSn,
						 * "^[a-zA-Z4]{3}-[0-9]{4}+$")) { temp.append("<br/>");
						 * temp
						 * .append("額度編號格式錯誤，正確格式為「3碼英文字」+「-」+「4碼數字」，如NBP-0001"
						 * ); }
						 */
					}

				}
			}

		}

		// e-Loan企金授信配合經濟部紓困4.0，新增A04、A05、A06專案
		String isRescueOn = Util.trim(l140m01a.getIsRescueOn());
		String rescueItemOn = Util.trim(l140m01a.getRescueItemOn());
		String cntrNoOn = Util.trim(l140m01a.getCntrNoOn());

		if (Util.equals(isRescue, "Y")) {
			// 有移轉的才要檢查
			if (Util.notEquals(rescueItem, "")) {
				String newRescueIbDate = l161s01a.getRescueIbDate() == null ? ""
						: CapDate.formatDate(l161s01a.getRescueIbDate(),
								"yyyy-MM-dd");
				String LMS_RESCUEITEM_RESCUEIBDATE_CC = Util
						.trim(lmsService
								.getSysParamDataValue("LMS_RESCUEITEM_RESCUEIBDATE_CC"));
				if (Util.notEquals(LMS_RESCUEITEM_RESCUEIBDATE_CC, "")) {
					JSONObject jsonRescueIbDate = JSONObject.fromObject("{"
							+ LMS_RESCUEITEM_RESCUEIBDATE_CC + "}");
					if (jsonRescueIbDate != null) {
						String rescueIbDateBase = Util.trim(jsonRescueIbDate
								.get(rescueItem));
						if (Util.isNotEmpty(rescueIbDateBase)) {
							if (Util.notEquals(newRescueIbDate, "")) {
								if (LMSUtil.cmpDate(
										Util.parseDate(newRescueIbDate), "<",
										Util.parseDate(rescueIbDateBase))) {

									temp.append("<br/>");
									// L160M01A.message112=掛件文號格式錯誤 ，僅能輸入 英文字母
									// 與數字，且第一碼必須為英文。
									if (rescueItem.matches("J04|J05")) {
										temp.append("天然災害貸款類別「" + rescueItem
												+ "」之合意減息日「" + newRescueIbDate
												+ "」不得早於「" + rescueIbDateBase + "」");
									} else {
										temp.append("紓困貸款類別「" + rescueItem
												+ "」之合意展延日「" + newRescueIbDate
												+ "」不得早於「" + rescueIbDateBase + "」");
									}

								}
							}

						}
					}
				}

			}

		}

		if (Util.equals(isRescue, "Y") && Util.equals(isRescueOn, "Y")) {
			// 有移轉的才要檢查
			if (Util.notEquals(rescueItem, "")
					&& lmsService.isResueItemOldCase(rescueItem)) {
				String newRescueIbDate = l161s01a.getRescueIbDate() == null ? ""
						: CapDate.formatDate(l161s01a.getRescueIbDate(),
								"yyyy-MM-dd");
				String LMS_RESCUEITEM_RESCUEIBDATE_CK = Util
						.trim(lmsService
								.getSysParamDataValue("LMS_RESCUEITEM_RESCUEIBDATE_CK"));
				if (Util.notEquals(LMS_RESCUEITEM_RESCUEIBDATE_CK, "")) {
					JSONObject jsonRescueIbDate = JSONObject.fromObject("{"
							+ LMS_RESCUEITEM_RESCUEIBDATE_CK + "}");
					if (jsonRescueIbDate != null) {
						String rescueIbDateBase = Util.trim(jsonRescueIbDate
								.get(rescueItem));
						if (Util.isNotEmpty(rescueIbDateBase)) {
							if (Util.notEquals(newRescueIbDate, "")) {
								if (LMSUtil.cmpDate(
										Util.parseDate(newRescueIbDate), "<",
										Util.parseDate(rescueIbDateBase))) {

									temp.append("<br/>");
									// L160M01A.message112=掛件文號格式錯誤 ，僅能輸入 英文字母
									// 與數字，且第一碼必須為英文。
									temp.append("紓困貸款類別「" + rescueItem
											+ "」之合意展延日「" + newRescueIbDate
											+ "」不得早於「" + rescueIbDateBase + "」");

								}
							}

						}
					}
				}

			}

		}

		// J-110-0288_05097_B1001 Web
		// e-Loan配合辦理「行政院國家發展基金協助新創事業紓困融資加碼方案」，修改額度明細表欄位
		// 聖介，7/20上午 10:59
		// 企金處說只紓困不補貼時，合意日就視為簽約日，經討論後建議合意日不得超過2031/12/31(10年內紓困應該結束了吧)
		if (Util.equals(isRescue, "Y")) {
			// 有移轉的才要檢查
			if (Util.notEquals(rescueItem, "")
					&& lmsService.isResueItemOldCase(rescueItem)) {
				String newRescueIbDate = l161s01a.getRescueIbDate() == null ? ""
						: CapDate.formatDate(l161s01a.getRescueIbDate(),
								"yyyy-MM-dd");
				String LMS_RESCUEITEM_RESCUEIBDATE_DL = Util
						.trim(lmsService
								.getSysParamDataValue("LMS_RESCUEITEM_RESCUEIBDATE_DL"));
				if (Util.notEquals(LMS_RESCUEITEM_RESCUEIBDATE_DL, "")) {
					JSONObject jsonRescueIbDate = JSONObject.fromObject("{"
							+ LMS_RESCUEITEM_RESCUEIBDATE_DL + "}");
					if (jsonRescueIbDate != null) {
						String rescueIbDateBase = Util.trim(jsonRescueIbDate
								.get(rescueItem));
						if (Util.isNotEmpty(rescueIbDateBase)) {
							if (Util.notEquals(newRescueIbDate, "")) {
								if (LMSUtil.cmpDate(
										Util.parseDate(newRescueIbDate), ">",
										Util.parseDate(rescueIbDateBase))) {

									temp.append("<br/>");
									// L160M01A.message112=掛件文號格式錯誤 ，僅能輸入 英文字母
									// 與數字，且第一碼必須為英文。
									temp.append("紓困貸款類別「" + rescueItem
											+ "」之合意展延日「" + newRescueIbDate
											+ "」不得晚於「" + rescueIbDateBase + "」");

								}
							}

						}
					}
				}

			}

		}

		// J-110-0253_05097_B1001 Web e-Loan企金授信配合經濟部紓困4.0，新增A04、A05、A06專案
		if (Util.equals(isRescue, "Y")) {
			// A04檢核，合意展延日要晚於A01
			if (Util.notEquals(rescueItem, "")
					&& lmsService.isResueItemOldCase(rescueItem)
					&& lmsService.isResueItemSubSidy(rescueItem, "110")) {

				String newRescueIbDate = l161s01a.getRescueIbDate() == null ? ""
						: CapDate.formatDate(l161s01a.getRescueIbDate(),
								"yyyy-MM-dd");

				List<Map<String, Object>> l161s01aList = eloandbBASEService
						.findL161s01aLastRescueDataWithoutRescueItem(
								l161s01a.getCntrNo(), l161s01a.getCustId(),
								l161s01a.getDupNo());

				if (l161s01aList != null && !l161s01aList.isEmpty()) {
					for (Map<String, Object> l161s01aMap : l161s01aList) {
						String elf383RescueItem = Util.trim(MapUtils.getString(
								l161s01aMap, "RESCUEITEM", ""));
						if (Util.notEquals(elf383RescueItem, "")
								&& lmsService
										.isResueItemOldCase(elf383RescueItem)
								&& lmsService.isResueItemSubSidy(
										elf383RescueItem, "109")) {

							String RESCUEIBDATE_161 = Util.trim(MapUtils
									.getString(l161s01aMap, "RESCUEIBDATE"));

							if (Util.notEquals(RESCUEIBDATE_161, "")
									&& Util.notEquals(newRescueIbDate, "")) {
								if (LMSUtil.cmpDate(
										Util.parseDate(newRescueIbDate), "<",
										Util.parseDate(RESCUEIBDATE_161))) {

									temp.append("<br/>");
									// L160M01A.message112=掛件文號格式錯誤 ，僅能輸入 英文字母
									// 與數字，且第一碼必須為英文。
									temp.append("紓困貸款類別「" + rescueItem
											+ "」之合意展延日「" + newRescueIbDate
											+ "」不得早於109年紓困方案合意展延日「"
											+ RESCUEIBDATE_161 + "」");
									break;

								}
							}

						}
					}
				}

			}

			// J-111-0112_05097_B1002 Web e-Loan企金授信管理系統新增111年經濟部紓困方案
			if (Util.notEquals(rescueItem, "")
					&& lmsService.isResueItemOldCase(rescueItem)
					&& lmsService.isResueItemSubSidy(rescueItem, "111")) {

				String newRescueIbDate = l161s01a.getRescueIbDate() == null ? ""
						: CapDate.formatDate(l161s01a.getRescueIbDate(),
								"yyyy-MM-dd");

				List<Map<String, Object>> l161s01aList = eloandbBASEService
						.findL161s01aLastRescueDataWithoutRescueItem(
								l161s01a.getCntrNo(), l161s01a.getCustId(),
								l161s01a.getDupNo());

				if (l161s01aList != null && !l161s01aList.isEmpty()) {
					for (Map<String, Object> l161s01aMap : l161s01aList) {
						String elf383RescueItem = Util.trim(MapUtils.getString(
								l161s01aMap, "RESCUEITEM", ""));
						if (Util.notEquals(elf383RescueItem, "")
								&& lmsService
										.isResueItemOldCase(elf383RescueItem)
								&& (lmsService.isResueItemSubSidy(
										elf383RescueItem, "109") || lmsService
										.isResueItemSubSidy(elf383RescueItem,
												"110"))) {

							String RESCUEIBDATE_161 = Util.trim(MapUtils
									.getString(l161s01aMap, "RESCUEIBDATE"));

							if (Util.notEquals(RESCUEIBDATE_161, "")
									&& Util.notEquals(newRescueIbDate, "")) {
								if (LMSUtil.cmpDate(
										Util.parseDate(newRescueIbDate), "<",
										Util.parseDate(RESCUEIBDATE_161))) {

									temp.append("<br/>");
									// L160M01A.message112=掛件文號格式錯誤 ，僅能輸入 英文字母
									// 與數字，且第一碼必須為英文。
									temp.append("紓困貸款類別「" + rescueItem
											+ "」之合意展延日「" + newRescueIbDate
											+ "」不得早於109/110年紓困方案合意展延日「"
											+ RESCUEIBDATE_161 + "」");
									break;

								}
							}

						}
					}
				}

			}

		}

		// J-110-0288_05097_B1003 Web e-Loan配合國發基金新創事業紓困加碼方案H01、A07專案修改
		if (Util.equals(isRescue, "Y")) {
			if (lmsService.isResueItemNeedIsTurnoverDecreased(rescueItem)) {
				if (Util.equals(Util.trim(l161s01a.getIsTurnoverDecreased()),
						"Y")) {

					String LMS_LMS_RESCUE_ISTURNOVER_CHK = Util
							.trim(lmsService
									.getSysParamDataValue("LMS_LMS_RESCUE_ISTURNOVER_CHK"));
					if (Util.equals(LMS_LMS_RESCUE_ISTURNOVER_CHK, "Y")) {

						// 要中小企業或視同中小企業才可以選Y

						JSONObject jsonData = new JSONObject();
						jsonData = lmsService.getCustBusCDAndClass(
								l161s01a.getCustId(), l161s01a.getDupNo());
						String custClass = Util.trim(jsonData.optString(
								"custClass", ""));
						if (Util.notEquals(custClass, "")) {
							if (Util.notEquals(custClass, "4")
									&& Util.notEquals(custClass, "8")) {

								// 4.民營中小企業、8.視同中小企業
								temp.append("<br/>");
								// L160M01A.message112=掛件文號格式錯誤 ，僅能輸入 英文字母
								// 與數字，且第一碼必須為英文。
								temp.append("本案紓困貸款類別為「"
										+ rescueItem
										+ "」且借戶非中小企業，欄位「"
										+ Util.trim(prop
												.getProperty("L160M01A.isTurnoverDecreased_"
														+ rescueItem))
										+ "」必須為「不適用」");
							}
						}
					}

				}
			}
		}

		// J-111-0214_05097_B1001 Web e-Loan國內企金動用審核表新增可適用新利率計算減免息相關功能
		if (Util.equals(isRescue, "Y")) {

			if (lms1601Service.needRescueChgRateFg(rescueItem)) {

				String rescueChgRateFg = "";
				Date rescueChgRateSingDate = null;
				BigDecimal rescueChgRate = null;
				Date rescueChgRateEffectDate = null;

				rescueChgRateFg = Util.trim(l161s01a.getRescueChgRateFg());

				if (Util.equals(rescueChgRateFg, "Y")) {

					rescueChgRate = l161s01a.getRescueChgRate();
					if (rescueChgRate != null) {
						String chkRescueChgRateError = lmsService
								.chkRescueItemRescueRateHigh(rescueItem,
										rescueItemSub, rescueChgRate);
						if (Util.notEquals(chkRescueChgRateError, "")) {
							temp.append("<br/>");
							temp.append(chkRescueChgRateError);
						}

						String chkRescueChgRateError2 = lmsService
								.chkRescueItemRescueRateLess(rescueItem,
										rescueItemSub, rescueChgRate);
						if (Util.notEquals(chkRescueChgRateError2, "")) {
							temp.append("<br/>");
							temp.append(chkRescueChgRateError2);
						}

					}

				}

			}

		}

		// J-112-0148_05097_B1001 Web
		// e-Loan企金授信新增經濟部協助中小型事業疫後振興專案貸款暨經濟部協助中小企業轉型發展專案貸款
	    // J-112-0366_12473_B1001 送保方式為批次保證時不做檢核
		if (Util.equals(isRescue, "Y") && !"3".equals(Util.trim(l140m01a.getGutType()))) {
			String chkSmeRateForRescueItemErr = Util.trim(lms1401Service
					.chkSmeRateForRescueItem(l140m01a.getCustId(),
							l140m01a.getDupNo(), l140m01a.getCntrNo(),
							l161s01a.getIsRescue(), l161s01a.getRescueItem(),
							l161s01a.getHeadItem1(), l161s01a.getGutPercent()));
			if (Util.notEquals(chkSmeRateForRescueItemErr, "")) {
				// 額度序號「" + cntrNo + "」"+ "紓困代碼類別「" + rescueItem + "."+ lms140_rescueItem.get(rescueItem)+ "」之信保保證成數為「" + NumConverter.addComma(gutPercent) + "」，正確應該要介於「~」。<BR>";
				temp.append("<br/>");
				temp.append(chkSmeRateForRescueItemErr);
			}
		}

		// J-111-0506_05097_B1001 Web e-Loan企金授信動審表增加授信作業手續費之欄位
		if (UtilConstants.DEFAULT.是.equals(l161s01a.getIsOperationFee())) {

			if (l161s01a.getOperationFeeAmt() != null
					&& l161s01a.getOperationFeeAmt().compareTo(BigDecimal.ZERO) <= 0) {
				// L160M01A.message116=欄位「{0}」必須大於0。
				// L160M01A.operationFeeAmt=收取金額
				temp.append("<br/>");
				temp.append(MessageFormat.format(
						prop.getProperty("L160M01A.message116"),
						prop.getProperty("L160M01A.operationFeeAmt")));
			}
		}

		// J-111-0506_05097_B1001 Web e-Loan企金授信動審表增加授信作業手續費之欄位
		String LMS_CHK_OPFEE_SAME_L140M01A = Util.trim(lmsService
				.getSysParamDataValue("LMS_CHK_OPFEE_SAME_L140M01A"));
		if (Util.equals(LMS_CHK_OPFEE_SAME_L140M01A, "Y")) {
			if (Util.notEquals(Util.trim(l140m01a.getIsOperationFee()), "")
					&& Util.notEquals(Util.trim(l161s01a.getIsOperationFee()),
							"")) {
				if (Util.notEquals(Util.trim(l161s01a.getIsOperationFee()),
						Util.trim(l140m01a.getIsOperationFee()))) {
					// L160M01A.message117=欄位「{0}」必須與額度明細表相同。
					// L160M01A.isOperationFee=是否收取授信作業手續費
					temp.append("<br/>");
					temp.append(MessageFormat.format(
							prop.getProperty("L160M01A.message117"),
							prop.getProperty("L160M01A.isOperationFee")));
				}
			}
		}
		//G-113-0036 額度明細表聯貸攤貸比例金額檢核
		// 暫存合計總額
		BigDecimal count = BigDecimal.ZERO;
		// 分子加總
		BigDecimal shareRateCount = BigDecimal.ZERO;
		// 暫存分母
		BigDecimal shareRate = BigDecimal.ZERO;
		// 檢查現請額度 總金額是否與攤貸金額相等
		BigDecimal nowCurrAMT = l140m01a.getCurrentApplyAmt();
		// 是否有餘額剩下
		BigDecimal tempAmt = BigDecimal.ZERO;
		Set<L140M01E_AF> l140m01e_afs = l140m01a.getL140m01e_af();
		if (l140m01e_afs != null && !l140m01e_afs.isEmpty()) {
			for (L140M01E_AF l140m01e_af : l140m01e_afs) {
				count = count.add(Util.parseBigDecimal(l140m01e_af.getShareAmt()));
				// 以比例計算的時候在要判斷
				if (UtilConstants.Cntrdoc.shareType.以比例計算.equals(l140m01e_af.getShareFlag())) {
					shareRateCount = shareRateCount.add(l140m01e_af.getShareRate1());
					shareRate = l140m01e_af.getShareRate2();
				}
			}
			if (l140m01e_afs.size() > 0) {
				if (shareRateCount.compareTo(shareRate) != 0) {
					temp.append(temp.length() > 0 ? "、" : "");
					// L140M01e.message04=限額條件下聯行攤貸分子加總不等於分母
					temp.append("<br/>");
					temp.append(prop.getProperty("L140M01e.message04"));
				}

			}
			// 檢核分子是否攤貸完
			if (shareRateCount.compareTo(shareRate) == 0) {
				// 當加總額小於現請額度
				if (count.compareTo(nowCurrAMT) == -1) {
					tempAmt = nowCurrAMT.subtract(count);
					temp.append("<br/>");
					temp.append(MessageFormat.format(
							prop.getProperty("L140M01e.message06"),
							tempAmt));
				}
				// 最後剩餘金額
				result.set("l140m01eAmt", NumConverter.addComma(tempAmt));
				
			}
		}

		if (temp.length() > 0) {
			// 以下訊息為必須更正之錯誤
			temp.insert(0, prop.getProperty("L160M01A.message78") + "：<br/>");
		}

		return temp.toString();

	}

	/**
	 * 複製L161M01B 聯貸案參貸比率明細檔
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult copyL161s01b(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1601M01Page.class);
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String copyFromUid = Util.trim(params.getString("copyFromUid"));
		String copyToUid = Util.trim(params.getString("copyToUid"));

		if (Util.equals(copyFromUid, copyToUid)) {
			// 不得複製同筆額度動用資訊參貸比率
			throw new CapMessageException(
					prop.getProperty("L160M01A.message82"), getClass());
		}

		L161S01A l161s01aFrom = lms1601Service.findL161m01aByMainIdUid(mainId,
				copyFromUid);

		Set<L161S01B> l161s01bsFrom = null;

		if (l161s01aFrom != null) {
			l161s01bsFrom = l161s01aFrom.getL161s01b();
		}

		if (l161s01bsFrom == null) {
			// 選取之額度動用資訊項下無同業參貸比率可複製
			throw new CapMessageException(
					prop.getProperty("L160M01A.message81"), getClass());
		}

		L161S01A l161s01aTo = lms1601Service.findL161m01aByMainIdUid(mainId,
				copyToUid);

		boolean hasUnitBank = false;
		for (L161S01B l161s01b : l161s01bsFrom) {
			if (Util.notEquals(l161s01b.getSlBank(), UtilConstants.兆豐銀行代碼)) {
				hasUnitBank = true;
				break;
			}
		}

		if (hasUnitBank == false) {
			// 選取之額度動用資訊項下無同業參貸比率可複製
			throw new CapMessageException(
					prop.getProperty("L160M01A.message81"), getClass());
		}

		// 先刪除
		Set<L161S01B> l161s01bsTo = l161s01aTo.getL161s01b();
		for (L161S01B l161s01b : l161s01bsTo) {
			if (Util.notEquals(l161s01b.getSlBank(), UtilConstants.兆豐銀行代碼)) {
				lms1601Service.delete(l161s01b);
			}
		}

		// 再複製
		for (L161S01B l161s01b : l161s01bsFrom) {
			if (Util.notEquals(l161s01b.getSlBank(), UtilConstants.兆豐銀行代碼)) {
				L161S01B newL161s01b = new L161S01B();

				newL161s01b.setCreator(user.getUserId());
				newL161s01b.setCreateTime(CapDate.getCurrentTimestamp());
				newL161s01b.setMainId(l161s01aTo.getMainId());
				newL161s01b.setPid(l161s01aTo.getUid());
				newL161s01b.setSeq(lms1601Service.findL161m01bMaxSeq(
						l161s01aTo.getMainId(), l161s01aTo.getUid()));

				newL161s01b.setSlBankType(l161s01b.getSlBankType());
				newL161s01b.setSlBank(l161s01b.getSlBank());
				newL161s01b.setSlBankCN(l161s01b.getSlBankCN());
				newL161s01b.setSlBranch(l161s01b.getSlBranch());
				newL161s01b.setSlBranchCN(l161s01b.getSlBranchCN());

				newL161s01b.setSlMaster(l161s01b.getSlMaster());
				newL161s01b.setSlAccNo(l161s01b.getSlAccNo());

				newL161s01b.setSlCurr(l161s01b.getSlCurr());
				newL161s01b.setSlAmt(l161s01b.getSlAmt());

				lms1601Service.save(newL161s01b);
			}
		}

		return result;

	}

	/**
	 * 複製L161M01B 聯貸案參貸比率明細檔
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult applyGist(PageParameters params)	throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1601M01Page.class);
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String cntrNo = Util.trim(params.getString("cntrNo"));
		String uid = Util.trim(params.getString("uid"));

		L161S01A l161s01a = lms1601Service.findL161m01aByMainIdUid(mainId, uid);

		if (l161s01a == null) {
			// 無法取得額度動用資訊L161S01A
			throw new CapMessageException(
					prop.getProperty("L160M01A.message79"), getClass());
		}

		String mainId140 = l161s01a.getCntrMainId();

		L140M01A l140m01a = lms1401Service.findL140m01aByMainId(mainId140);
		if (l140m01a == null) {
			// 找不到額度明細表
			throw new CapMessageException(
					prop.getProperty("L160M01A.message66"), getClass());
		}

		L120M01C l120m01c = l140m01a.getL120m01c();

		L120M01A l120m01a = lms1201Service.findL120m01aByMainId(l120m01c
				.getMainId());
		if (l120m01a == null) {
			// 無法取得額度動用資訊L161S01A對應的簽報書
			throw new CapMessageException(
					prop.getProperty("L160M01A.message83"), getClass());
		}

		result.set("gist", l120m01a.getGist());
		return result;

	}

	/**
	 * 先確認整批勾選列印的額度動用資訊是否都有聯貸參貸比率
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult chkAllPrintHasL161S01B(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1601M01Page.class);

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));

		String rptOid = Util.nullToSpace(params.getString("uids"));
		String[] dataSplit = rptOid.split(",");

		boolean printOk = false;

		for (String uid : dataSplit) {
			// L160M01B．動審表額度序號資料
			List<L161S01B> l161s01bList = null;

			L161S01A l161s01a = lms1601Service.findL161m01aByMainIdUid(mainId,
					uid);
			l161s01bList = l161s01a.getL161s01bs();

			if (l161s01bList != null && !l161s01bList.isEmpty()) {
				if (Util.equals(l161s01a.getUnitMega(), "Y")
						|| Util.equals(l161s01a.getUnitCase(), "Y")) {
					printOk = true;
					break;
				}

			}
		}

		if (printOk == false) {
			throw new CapMessageException(
					prop.getProperty("L160M01A.message88"), getClass());
		}
		return result;

	}

	/**
	 * 計算長度
	 * 
	 * @param value
	 *            值
	 * @param length
	 *            DB長度
	 * @return
	 */
	public Boolean calTheWordByte(Object value, int length) {
		int count = 0;
		if (value != null) {
			byte[] bytes = null;
			try {
				bytes = String.valueOf(value).getBytes("UTF-8");
			} catch (UnsupportedEncodingException e) {
				logger.error(e.getMessage());
			}
			count = bytes.length;
		}

		if (count > length) {
			return true;
		}
		return false;
	}

	/**
	 * 引進額度明細表動撥提醒事項 J-103-0317-001 Web e-Loan企金簽報書上傳承諾事項與追蹤檢視日期
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult applyToAloan(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1601M01Page.class);
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String cntrNo = Util.trim(params.getString("cntrNo"));
		String uid = Util.trim(params.getString("uid"));

		L161S01A l161s01a = lms1601Service.findL161m01aByMainIdUid(mainId, uid);

		if (l161s01a == null) {
			// 無法取得額度動用資訊L161S01A
			throw new CapMessageException(
					prop.getProperty("L160M01A.message79"), getClass());
		}

		String mainId140 = l161s01a.getCntrMainId();

		L140M01A l140m01a = lms1401Service.findL140m01aByMainId(mainId140);
		if (l140m01a == null) {
			// 找不到額度明細表
			throw new CapMessageException(
					prop.getProperty("L160M01A.message66"), getClass());
		}

		String toAloan = "";
		String itemDscr = "";
		L140M01B l140m01b = lms1401Service.findL140m01bUniqueKey(
				l140m01a.getMainId(),
				UtilConstants.Cntrdoc.l140m01bItemType.其他敘做條件_動撥提示用語);
		if (l140m01b != null) {
			toAloan = l140m01b.getToALoan();
			itemDscr = l140m01b.getItemDscr();
		} else {
			toAloan = "";
			itemDscr = "";
		}

		result.set("toAloan", toAloan);
		result.set("itemDscr", itemDscr);
		return result;

	}

	/**
	 * 檢核資料是否已經有正確的登錄
	 * 
	 * <pre>
	 * @param params PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult checkToAloan2(PageParameters params)	throws CapException {
		Map<String, String> param = new HashMap<String, String>();
		// 儲存and檢核
		String oid = params.getString(EloanConstants.OID);
		L160M01A l160m01a = lms1601Service.findModelByOid(L160M01A.class, oid);

		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1601M01Page.class);
		if (Util.isEmpty(l160m01a)) {

			// L160M01A.error10=請先儲存
			param.put("msg", pop.getProperty("L160M01A.error10"));
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤, param), getClass());
		}

		Set<L161S01A> l161s01as = l160m01a.getL161S01A();

		// J-103-0317-001 Web e-Loan企金簽報書上傳承諾事項與追蹤檢視日期

		StringBuffer checkToAloan = new StringBuffer("");

		if (l161s01as.isEmpty()) {
			// 動審表請重新引進額度明細表
			throw new CapMessageException(
					pop.getProperty("L160M01A.message73"), getClass());
		} else {
			// J-103-0202-005 Web e-Loan授信簽案衍生性金融商品遠匯與換匯科目，改以交易額度來簽案。
			StringBuffer hasToAloan2Cntrno = new StringBuffer("");

			for (L161S01A l161s01a : l161s01as) {

				L161S01C l161s01c = lms1601Service
						.findL161s01cByMainIdPidItemType(
								l161s01a.getMainId(),
								l161s01a.getUid(),
								UtilConstants.Cntrdoc.l140m01bItemType.其他敘做條件_動撥提示用語);

				if (l161s01c != null) {
					if (Util.notEquals(l161s01c.getItemDscr(), "")) {
						if (Util.equals(
								Util.trim(l161s01a.getReViewDateKind()), "")) {
							hasToAloan2Cntrno.append(Util.equals(
									hasToAloan2Cntrno.toString(), "") ? ""
									: "、");
							hasToAloan2Cntrno.append(l161s01a.getCntrNo());
						}
					}
				}

			}
			if (Util.notEquals(hasToAloan2Cntrno.toString(), "")) {
				// 下列額度於動審表-相關報表-額度動用資訊-動撥提醒資訊有承諾事項，但未設定是否需追蹤檢視，是否繼續?<br/>
				checkToAloan.append(pop.getProperty("L160M01A.message96"));
				checkToAloan.append("「");
				checkToAloan.append(hasToAloan2Cntrno.toString());
				checkToAloan.append("」");
				checkToAloan.append("<br/>");
				checkToAloan.append("<br/>");
				// 若承諾事項需持續追蹤檢視，請設定下列項目:<br/>
				checkToAloan.append(pop.getProperty("L160M01A.message97"));
				// 1.可於動審表-相關報表-額度動用資訊-動撥提醒資訊 設定承諾事項下次檢視日期與週期。<br/>
				checkToAloan.append(pop.getProperty("L160M01A.message98"));
				// 2.亦可於a-Loan L521 企業授信戶承諾事項維護交易設定。<br/>
				checkToAloan.append(pop.getProperty("L160M01A.message99"));
				// 3.完成上述設定後，請於報表查詢系統查詢LLDLN229(每日)LLMLN229(每月)國內各分行企業戶承諾事項相關報表以定期檢視追蹤。
				checkToAloan.append(pop.getProperty("L160M01A.message100"));
			}

		}

		CapAjaxFormResult result = new CapAjaxFormResult();

		result.set("checkToAloan2", checkToAloan.toString());

		return result;
	}

	/**
	 * 由動審表中所對應的額度序號引入個人清冊
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult getC801M01AByL140M01A(PageParameters params)	throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");

		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID)); // CLS1161M01.mainId

		List<Map<String, Object>> list = eloandbBASEService
				.findCntrnoByL161S01A(mainId);

		String[] cntrNos = new String[list.size()];

		for (int i = 0; i < list.size(); i++) {
			cntrNos[i] = Util.trim(list.get(i).get("CNTRNO"));
		}

		lms1601Service.reNewC801M01A(cntrNos, mainId);

		return result;
	}

	/**
	 * 檢查是否開放動撥提醒資訊頁籤 上線時要將COMMON 的 LMS_ToAloan2CanShow 設成Y LMS_ArCtrlCanShow
	 * 
	 * @param params
	 *            <pre>
	 *            oids : 要刪除的L140M01O.oid陣列<br/>
	 *          mainId : 額度明細表文件編號
	 * </pre>
	 * @param
	 * @return IResult
	 * @throws CapException
	 */
	public String chkArCtrlCanShow() throws CapException {
		String canShow = "N";
		CapAjaxFormResult result = new CapAjaxFormResult();

		// 後台管理->系統設定維護->LMS_ArCtrlCanShow
		Map<String, Object> onlineDateMap = eloandbBASEService
				.getSysParamData("LMS_ToAloan2CanShow");

		String onlineDate = Util.trim(onlineDateMap.get("PARAMVALUE"));

		if (Util.equals(onlineDate, "Y")) {
			canShow = "Y";
		}

		return canShow;
	}

	/**
	 * 將引進的案件簽報書資料複製連保人資料到主從債務人
	 * 
	 * @param l140m01a
	 *            額度明細表主檔
	 * @param mainId
	 *            要加進去的mainId
	 * @param custContry
	 *            簽報書所有借款人的國別 <custId+dupNo, 國別>
	 * @return 複製完成的主從債務人資料表檔
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	private L164S01A copyL120s01bToL164s01a(L120S01B l120s01b, String mainId,
			HashMap<String, String> custContry) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		L164S01A l164s01a = null;
		L120S01A l120s01a = l120s01b.getL120s01a();

		l164s01a = lms1601Service.findL164s01aByMainIdCustId(mainId,
				l120s01b.getCustId(), l120s01b.getDupNo());
		if (l164s01a == null) {
			l164s01a = new L164S01A();
			l164s01a.setCreator(user.getUserId());
			l164s01a.setCreateTime(CapDate.getCurrentTimestamp());
			l164s01a.setMainId(mainId);
		}

		l164s01a.setCustId(Util.trim(l120s01b.getCustId()));
		l164s01a.setDupNo(Util.trim(l120s01b.getDupNo()));
		l164s01a.setCustPos(Util.trim(l120s01a.getCustPos()));
		l164s01a.setChairmanId(Util.trim(l120s01b.getChairmanId()));
		l164s01a.setChairmanDupNo(Util.trim(l120s01b.getChairmanDupNo()));
		l164s01a.setChairman(Util.toSemiCharString(Util.trim(l120s01b
				.getChairman())));
		l164s01a.setBeneficiary(Util.trim(l120s01b.getBeneficiary()));
		// J-107-0070-001 Web e-Loan
		// 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
		l164s01a.setSeniorMgr((Util.trim(l120s01b.getSeniorMgr())));
		// J-108-0039_05097_B1001 Web e-Loan
		// 國內企金授信系統簽報、動審AML頁籤將借戶之「具控制權人」納入應查詢比對黑名單之對象。
		l164s01a.setCtrlPeo((Util.trim(l120s01b.getCtrlPeo())));

		// J-109-0209_05097_B1001 e-Loan國內企金動審表增加借戶性質註記等進扣帳對象檢核項目
		String isSole = "";
		String soleType = "";
		String hasRegis = "";

		// 如果有最新的QUOTAPPR就引進，沒有就抓0024判斷
		Map<String, Object> quotapprMap = misQuotapprService
				.findLastIsSoleByCustId(Util.trim(l120s01b.getCustId()),
						Util.trim(l120s01b.getDupNo()));
		if (quotapprMap == null || quotapprMap.isEmpty()) {
			// 沒資料抓國別跟0024

			// Map<String, Object> custData = (Map<String, Object>)
			// misCustdataService
			// .findCharCdByIdDupNo(Util.trim(l120s01b.getCustId()),
			// Util.trim(l120s01b.getDupNo()));
			// if (custData != null && !custData.isEmpty()) {
			// String charCd = Util.trim((String) custData.get("CHARCD"));
			// String tCountry = "";
			// if (custContry.containsKey(Util.trim(l120s01b.getCustId())
			// + Util.trim(l120s01b.getDupNo()))) {
			// tCountry = Util.trim(custContry.get(Util.trim(l120s01b
			// .getCustId()) + Util.trim(l120s01b.getDupNo())));
			// }
			// if (Util.equals(charCd, "20") && Util.equals(tCountry, "TW")) {
			// // 我國非法人組織，要填獨資或合夥
			// isSole = "Y";
			// } else {
			// isSole = "N";
			// }
			// }

			String tCountry = "";
			if (custContry.containsKey(Util.trim(l120s01b.getCustId())
					+ Util.trim(l120s01b.getDupNo()))) {
				tCountry = Util.trim(custContry.get(Util.trim(l120s01b
						.getCustId()) + Util.trim(l120s01b.getDupNo())));
			}
			if (Util.notEquals(tCountry, "TW")) {
				isSole = "N";
			} else {
				isSole = "";
			}

		} else {
			// 有資料直接用
			isSole = Util.trim(MapUtils.getString(quotapprMap, "ISSOLE", ""));
			soleType = Util.trim(MapUtils
					.getString(quotapprMap, "SOLETYPE", ""));
			hasRegis = Util.trim(MapUtils
					.getString(quotapprMap, "HASREGIS", ""));
		}

		l164s01a.setIsSole(isSole);
		l164s01a.setSoleType(soleType);
		l164s01a.setHasRegis(hasRegis);

		return l164s01a;
	}

	@SuppressWarnings("unchecked")
	private List<L120S01P> copyL120s01pToNewL120s01p(L120S01B l120s01b,	String mainId) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		List<L120S01P> newL120s01ps = new ArrayList<L120S01P>();

		List<L120S01P> listL120s01p = amlRelateService
				.findL120s01pByMainIdAndCustIdWithRType(l120s01b.getMainId(),
						l120s01b.getCustId(), l120s01b.getDupNo(),
						UtilConstants.Casedoc.L120s09aBlackListCtlTarget.實質受益人);
		if (listL120s01p != null && !listL120s01p.isEmpty()) {
			for (L120S01P oldL120s01p : listL120s01p) {

				L120S01P newL120s01p = new L120S01P();
				CapBeanUtil.copyBean(oldL120s01p, newL120s01p,
						CapBeanUtil.getFieldName(L120S01P.class, true));// 複製的語法
				newL120s01p.setOid(null);
				newL120s01p.setMainId(mainId);
				newL120s01p.setCreator(user.getUserId());
				newL120s01p.setCreateTime(CapDate.getCurrentTimestamp());
				newL120s01ps.add(newL120s01p);
			}
		}

		// J-107-0070-001 Web e-Loan 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
		List<L120S01P> listL120s01p_smgr = amlRelateService
				.findL120s01pByMainIdAndCustIdWithRType(l120s01b.getMainId(),
						l120s01b.getCustId(), l120s01b.getDupNo(),
						UtilConstants.Casedoc.L120s09aBlackListCtlTarget.高階管理人員);
		if (listL120s01p_smgr != null && !listL120s01p_smgr.isEmpty()) {
			for (L120S01P oldL120s01p : listL120s01p_smgr) {

				L120S01P newL120s01p = new L120S01P();
				CapBeanUtil.copyBean(oldL120s01p, newL120s01p,
						CapBeanUtil.getFieldName(L120S01P.class, true));// 複製的語法
				newL120s01p.setOid(null);
				newL120s01p.setMainId(mainId);
				newL120s01p.setCreator(user.getUserId());
				newL120s01p.setCreateTime(CapDate.getCurrentTimestamp());
				newL120s01ps.add(newL120s01p);
			}
		}

		// J-108-0039_05097_B1001 Web e-Loan
		// 國內企金授信系統簽報、動審AML頁籤將借戶之「具控制權人」納入應查詢比對黑名單之對象。
		List<L120S01P> listL120s01p_ctrlPeo = amlRelateService
				.findL120s01pByMainIdAndCustIdWithRType(l120s01b.getMainId(),
						l120s01b.getCustId(), l120s01b.getDupNo(),
						UtilConstants.Casedoc.L120s09aBlackListCtlTarget.具控制權人);
		if (listL120s01p_ctrlPeo != null && !listL120s01p_ctrlPeo.isEmpty()) {
			for (L120S01P oldL120s01p : listL120s01p_ctrlPeo) {

				L120S01P newL120s01p = new L120S01P();
				CapBeanUtil.copyBean(oldL120s01p, newL120s01p,
						CapBeanUtil.getFieldName(L120S01P.class, true));// 複製的語法
				newL120s01p.setOid(null);
				newL120s01p.setMainId(mainId);
				newL120s01p.setCreator(user.getUserId());
				newL120s01p.setCreateTime(CapDate.getCurrentTimestamp());
				newL120s01ps.add(newL120s01p);
			}
		}

		return newL120s01ps;
	}

	/**
	 * J-108-0145_05097_B1001 Web e-Loan 國內外企金授信私募基金案件調整實質受益人控管
	 * 
	 * <pre>
	 * @param params PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult getWarnMsg(PageParameters params) throws CapException {
		Map<String, String> param = new HashMap<String, String>();
		StringBuffer warnMsg = new StringBuffer("");

		// 儲存and檢核
		String oid = params.getString(EloanConstants.OID);
		L160M01A l160m01a = lms1601Service.findModelByOid(L160M01A.class, oid);

		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1601M01Page.class);
		if (Util.isEmpty(l160m01a)) {

			// L160M01A.error10=請先儲存
			param.put("msg", pop.getProperty("L160M01A.error10"));
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤, param), getClass());
		}

		// #J-108-0145_05097_B1001 Web e-Loan 國內外企金授信私募基金案件調整實質受益人控管

		L120M01A l120m01a = lms1201Service.findL120m01aByMainId(l160m01a
				.getSrcMainId());
		if (l120m01a != null) {
			String canPassAMLWarnMsg = amlRelateService
					.getPassAmlRelativeAndRiskLvlChkWarnMsg("2",
							l120m01a.getMainId());
			// L120S09a.message27=本案為實質受益人等延後辦理辨識案件，請衡酌加註相關控管條件(如：「首次動撥前務必確實完成實質受益人等辨識相關作業」)
			if (Util.notEquals(canPassAMLWarnMsg, "")) {
				warnMsg.append("").append("●").append(canPassAMLWarnMsg);
			}
		}

		// I-107-0260_05097_B1001 Web e-Loan企金授信系統增加提示訊息【該客戶屬於巴拿馬文件名單】
		if (UtilConstants.Casedoc.DocType.企金.equals(l120m01a.getDocType())) {
			if (!(UtilConstants.Casedoc.DocCode.陳復陳述案.equals(Util.trim(l120m01a
					.getDocCode())) || UtilConstants.Casedoc.DocCode.異常通報
					.equals(Util.trim(l120m01a.getDocCode())))) {

				if (amlRelateService.needShowPanaMsg(Util.trim(l160m01a
						.getOwnBrId()))) {

					// 借戶才要，因為目前只有檢核借戶才要引風險等級，代表只有借戶才有完整的0024-23
					// 主要借款人
					LinkedHashMap<String, String> allBorrowerIdMap = new LinkedHashMap<String, String>();
					Set<L160M01B> l160m01bs = l160m01a.getL160m01b();
					if (l160m01bs != null && !l160m01bs.isEmpty()) {
						for (L160M01B l160m01b : l160m01bs) {
							L140M01A l140m01a = amlRelateService
									.findModelByMainId(L140M01A.class,
											l160m01b.getReMainId());
							if (l140m01a != null) {
								String chkId = Util.trim(l140m01a.getCustId());
								String chkDupNo = Util
										.trim(l140m01a.getDupNo());
								if (Util.notEquals(chkId, "")
										&& Util.notEquals(chkDupNo, "")) {

									if (!allBorrowerIdMap.containsKey(chkId
											+ "-" + chkDupNo)) {
										allBorrowerIdMap.put(chkId + "-"
												+ chkDupNo,
												l140m01a.getCustName());
									}

								}
							}
						}
					}

					// 主要借款人
					// 檢核是否登錄主從債務表
					// List<L162S01A> l162m01as = (List<L162S01A>)
					// lms1601Service
					// .findListByMainId(L162S01A.class,
					// l160m01a.getMainId());
					// if (l162m01as.isEmpty()) {
					// // L160M01A.message28=「主從債務人資料表」，尚未登錄，不得呈主管覆核
					// param.put("msg", pop.getProperty("L160M01A.message28"));
					// throw new CapMessageException(
					// RespMsgHelper.getMessage(parent,
					// UtilConstants.AJAX_RSP_MSG.執行有誤, param),
					// getClass());
					// }

					// for (L162S01A l162s01a : l162m01as) {
					// if (Util.equals(l162s01a.getRType(),
					// UtilConstants.lngeFlag.共同借款人)) {
					// String chkId = Util.trim(l162s01a.getRId());
					// String chkDupNo = Util.trim(l162s01a.getRDupNo());
					// if (Util.notEquals(chkId, "")
					// && Util.notEquals(chkDupNo, "")) {
					// if (!allBorrowerIdMap.containsKey(chkId + "-"
					// + chkDupNo)) {
					// allBorrowerIdMap.put(
					// chkId + "-" + chkDupNo,
					// l162s01a.getRName());
					// }
					// }
					// }
					// }

					if (allBorrowerIdMap != null && !allBorrowerIdMap.isEmpty()) {

						Properties popLmss20a = MessageBundleScriptCreator
								.getComponentResource(LMSS20APanel.class);

						String panaCust = amlRelateService
								.chkCustListInPana(allBorrowerIdMap);

						if (Util.notEquals(panaCust.toString(), "")) {
							// L120S09a.lnsp0130_3=下列借款人屬於巴拿馬文件名單

							String tMarkS = "<SPAN style='BORDER-TOP-STYLE: solid; BORDER-TOP-COLOR: red; BORDER-BOTTOM-STYLE: solid; BORDER-LEFT-COLOR: red; BORDER-BOTTOM-COLOR: red; BORDER-RIGHT-STYLE: solid; BORDER-LEFT-STYLE: solid; BORDER-RIGHT-COLOR: red'><FONT color=red>";
							String tMarkE = "</FONT></SPAN>";

							warnMsg.append("<br/><br/>")
									.append("●")
									.append(popLmss20a
											.getProperty("L120S09a.lnsp0130_3")
											+ ":").append(tMarkS)
									.append(panaCust.toString()).append(tMarkE);

						} else {
							// L120S09a.lnsp0130_4=借款人皆無列屬於巴拿馬文件名單
							warnMsg.append("<br/><br/>")
									.append("●")
									.append(popLmss20a
											.getProperty("L120S09a.lnsp0130_4"));
						}
					}
				}
			}
		}

		// J-109-0209_05097_B1001 e-Loan國內企金動審表增加借戶性質註記等進扣帳對象檢核項目
		LinkedHashMap<String, String> aLoanIdMap = new LinkedHashMap<String, String>();
		Set<L160M01B> l160m01bs = l160m01a.getL160m01b();
		StringBuffer showLoanTarget = new StringBuffer("");
		if (l160m01bs != null && !l160m01bs.isEmpty()) {
			for (L160M01B l160m01b : l160m01bs) {
				L140M01A l140m01a = amlRelateService.findModelByMainId(
						L140M01A.class, l160m01b.getReMainId());
				if (l140m01a != null) {
					String tCustId = Util.trim(l140m01a.getCustId());
					String tDupNo = Util.trim(l140m01a.getDupNo());
					if (Util.notEquals(tCustId, "")
							&& Util.notEquals(tDupNo, "")) {
						if (!aLoanIdMap.containsKey(tCustId + "-" + tDupNo)) {

							// J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
							L164S01A l164s01a = lms1601Service
									.findL164s01aByMainIdCustId(
											l160m01a.getMainId(), tCustId,
											tDupNo);
							if (l164s01a != null) {
								String isSole = Util.trim(l164s01a.getIsSole());
								String soleType = Util.trim(l164s01a
										.getSoleType());
								String hasRegis = Util.trim(l164s01a
										.getHasRegis());
								String loanTarget = "";

								if (Util.equals(isSole, "Y")
										&& Util.equals(hasRegis, "N")) {

									loanTarget = l140m01a.getCustId() + " "
											+ l140m01a.getCustName() + "與"
											+ l164s01a.getChairmanId() + " "
											+ l164s01a.getChairman();

									// L160M01A.message111=借款人「{0}」額度撥/還款之對應存款進/扣帳對象限為「{1}」之帳號。
									String tMsg = MessageFormat
											.format(pop
													.getProperty("L160M01A.message111"),
													l140m01a.getCustId()
															+ l140m01a
																	.getCustName(),
													loanTarget);

									if (Util.equals(showLoanTarget.toString(),
											"")) {
										showLoanTarget.append(tMsg);
									} else {
										showLoanTarget.append("<BR>").append(
												tMsg);
									}

								} else {
									loanTarget = "";
								}

								aLoanIdMap.put(tCustId + "-" + tDupNo,
										l140m01a.getCustName());

							}

						}

					}
				}
			}
		}

		if (Util.notEquals(showLoanTarget.toString(), "")) {
			// L160M01A.message110=除下列借款人外，額度撥/還款之對應存款進/扣帳對象限為借戶本身之帳號。
			warnMsg.append("<br/><br/>")
					.append("●")
					.append(pop.getProperty("L160M01A.message110") + "<BR>"
							+ showLoanTarget.toString());

		} else {
			// L160M01A.message109=本案額度撥/還款之對應存款進/扣帳對象限為借戶本身之帳號。
			warnMsg.append("<br/><br/>").append("●")
					.append(pop.getProperty("L160M01A.message109"));
		}

		// J-109-0077_05097_B1021 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
		Set<L161S01A> l161s01as = l160m01a.getL161S01A();

		if (l161s01as.isEmpty()) {
			// 動審表請重新引進額度明細表
			throw new CapMessageException(
					pop.getProperty("L160M01A.message73"), getClass());
		}

		StringBuffer notSameIsRescue = new StringBuffer("");
		for (L161S01A l161s01a : l161s01as) {

			Map<String, Object> quotapprMap = misdbBASEService
					.findLastQuotapprOrderBySDate(
							Util.trim(l161s01a.getCustId()),
							Util.trim(l161s01a.getDupNo()),
							Util.trim(l161s01a.getCntrNo()));

			if (Util.notEquals(Util.trim(l161s01a.getIsRescue()), "Y")) {
				// 本案是否屬因應嚴重特殊傳染性肺炎影響事業資金紓困本次為否，但前次是是，則顯示提醒訊息
				if (quotapprMap != null && !quotapprMap.isEmpty()) {
					String ELF383_ISRESCUE = Util.trim(MapUtils.getString(
							quotapprMap, "ISRESCUE", ""));
					if (Util.equals(ELF383_ISRESCUE, "Y")) {

						notSameIsRescue
								.append((Util.equals(
										notSameIsRescue.toString(), "") ? ""
										: "、")).append(
										Util.trim(l161s01a.getCntrNo()));

					}

				}

			}

		}

		if (Util.notEquals(notSameIsRescue.toString(), "")) {
			warnMsg.append("<br/><br/>")
					.append("●")
					.append("額度序號" + notSameIsRescue.toString()
							+ "「本案是否屬因應嚴重特殊傳染性肺炎影響事業資金紓困」，與最近一次已覆核動審表不同");
		}

		// 琬雅，上午 11:13
		// 是不是可以在動審表呈主管覆核時,增加提醒若是紓困有補貼利息,請當天須做L505-39減息金額上限建檔
		//
		// 聖介，上午 11:27
		// (ELF383-RESCUEITEM = 'A01' OR 'A02' OR 'A03' OR
		// 'B01' OR 'B02' OR 'C02' OR
		// 'C03' OR 'E01' OR 'E02' OR
		// 'E03')
		// A01,A02,A03,B01,B02,C02,C03,E01,E02,E03

		StringBuffer hasRescueItemNeedL505_39 = new StringBuffer("");
		for (L161S01A l161s01a : l161s01as) {
			String isRescue = Util.trim(l161s01a.getIsRescue());
			if (Util.equals(isRescue, "Y")) {
				String rescueItem = Util.trim(l161s01a.getRescueItem());
				String rescueItemSub = Util.trim(l161s01a.getRescueItemSub());
				if (lmsService.isResueItemNeedL505_39(rescueItem)) {

					BigDecimal rescueRate = l161s01a.getRescueRate();
					if (lmsService.isResueItemRescueRate(rescueItem,
							rescueItemSub)) {
						// 需要輸入補貼利率，判斷補貼利率是不是0，大於0才檢核
						if (rescueRate != null
								&& rescueRate.compareTo(BigDecimal.ZERO) != 0) {
							// 補貼利率若為0，代表不補貼，則不用顯示訊息
							hasRescueItemNeedL505_39
									.append((Util.equals(
											hasRescueItemNeedL505_39.toString(),
											"") ? "" : "、")).append(
											Util.trim(l161s01a.getCntrNo()));
						}
					} else {
						hasRescueItemNeedL505_39
								.append((Util.equals(
										hasRescueItemNeedL505_39.toString(), "") ? ""
										: "、")).append(
										Util.trim(l161s01a.getCntrNo()));
					}

				}
			}
		}

		if (Util.notEquals(hasRescueItemNeedL505_39.toString(), "")) {
			warnMsg.append("<br/><br/>")
					.append("●")
					.append("額度序號" + hasRescueItemNeedL505_39.toString()
							+ "若是紓困有補貼利息,須當天做L505-39減息金額上限建檔");
		}

		// J-113-0082 配合法務部新規，於AML頁籤新增引入「受告誡處分」資訊
		// 提示有受告誡處分
		String checkCmfwarnpResult = 
			amlRelateService.checkCmfwarnpResult(l160m01a.getMainId());
		if(Util.isNotEmpty(checkCmfwarnpResult)){
			warnMsg.append("<br/><br/>")
			.append("●")
			.append(checkCmfwarnpResult.toString());
		}

		// *****************************************************************************************

		CapAjaxFormResult result = new CapAjaxFormResult();

		result.set("warnMsg", warnMsg.toString());

		return result;
	}

	/**
	 * 引進額度明細表動撥提醒事項 J-103-0317-001 Web e-Loan企金簽報書上傳承諾事項與追蹤檢視日期
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult queryInitData(PageParameters params)	throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1601M01Page.class);
		CapAjaxFormResult result = new CapAjaxFormResult();

		String oldRescueCaseItem = "";
		String rescueRateItem = "";
		String rescueRateItemSub = "";
		// J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
		String guarantorPriorityOn = "";
		// J-111-0214_05097_B1001 Web e-Loan國內企金動用審核表新增可適用新利率計算減免息相關功能
		String rescueChgRateItem = "";

		// J-112-0148_05097_B1002 Web
		// e-Loan企金授信新增經濟部協助中小型事業疫後振興專案貸款暨經濟部協助中小企業轉型發展專案貸款
		String rescueNoItem = "";
		String empCountItem = "";
		String rescueSnItem = "";
		String rescueNoName = "掛件文號";

		oldRescueCaseItem = Util.trim(lmsService
				.getSysParamDataValue("LMS_RESCUEITEM_OLD_CASE"));
		rescueRateItem = Util.trim(lmsService
				.getSysParamDataValue("LMS_RESCUEITEM_RESCUERATE"));
		rescueRateItemSub = Util.trim(lmsService
				.getSysParamDataValue("LMS_RESCUEITEMSUB_RESCUERATE"));

		// J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
		guarantorPriorityOn = Util.trim(lmsService
				.getSysParamDataValue("LMS_GUARANTOR_PRIORITY_ON"));

		// J-111-0214_05097_B1001 Web e-Loan國內企金動用審核表新增可適用新利率計算減免息相關功能
		rescueChgRateItem = Util.trim(lmsService
				.getSysParamDataValue("LMS_RESCUEITEM_CHG_RATE"));

		// J-112-0148_05097_B1002 Web
		// e-Loan企金授信新增經濟部協助中小型事業疫後振興專案貸款暨經濟部協助中小企業轉型發展專案貸款
		rescueNoItem = Util.trim(lmsService
				.getSysParamDataValue("LMS_RESCUEITEM_RESCUENO"));
		empCountItem = Util.trim(lmsService
				.getSysParamDataValue("LMS_RESCUEITEM_EMPCOUNT"));
		rescueSnItem = Util.trim(lmsService
				.getSysParamDataValue("LMS_RESCUEITEM_RESCUESN"));

		result.set("rescueRateItem", rescueRateItem);
		result.set("oldRescueCaseItem", oldRescueCaseItem);
		result.set("rescueRateItemSub", rescueRateItemSub);
		result.set("guarantorPriorityOn", guarantorPriorityOn);
		// J-111-0214_05097_B1001 Web e-Loan國內企金動用審核表新增可適用新利率計算減免息相關功能
		result.set("rescueChgRateItem", rescueChgRateItem);

		// J-112-0148_05097_B1002 Web
		// e-Loan企金授信新增經濟部協助中小型事業疫後振興專案貸款暨經濟部協助中小企業轉型發展專案貸款
		result.set("rescueNoItem", rescueNoItem);
		result.set("empCountItem", empCountItem);
		result.set("rescueSnItem", rescueSnItem);

		return result;

	}

	/**
	 * 當如果是新案要將L901M01A的項目寫入到L160M01C
	 * 
	 * @param mainId
	 *            動審表主檔mainId
	 * @return
	 * @throws CapException
	 */
	private List<L160M01C> copyL901m01aToL160m01c_SmallBussOnlyCaseC(
			String mainId, String local) throws CapException {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<L160M01C> l160m01cs = new ArrayList<L160M01C>();

		List<CodeType> codeTypeList = codeTypeService.findByCodeTypeList(
				"lmsl901m01a_checkList", "zh_TW");

		for (CodeType item : codeTypeList) {
			L160M01C l160m01c = new L160M01C();
			l160m01c.setCreator(user.getUserId());
			l160m01c.setCreateTime(CapDate.getCurrentTimestamp());
			l160m01c.setItemSeq(item.getCodeOrder());
			l160m01c.setMainId(mainId);
			l160m01c.setItemContent(item.getCodeDesc());
			l160m01c.setItemType(UtilConstants.Usedoc.itemType.國內企金);
			l160m01cs.add(l160m01c);
		}

		// 自訂項目
		for (int i = 1; i < 7; i++) {
			L160M01C l160m01c = new L160M01C();
			l160m01c.setCreator(user.getUserId());
			l160m01c.setCreateTime(CapDate.getCurrentTimestamp());
			l160m01c.setItemSeq(i);
			l160m01c.setMainId(mainId);
			l160m01c.setItemType(UtilConstants.Usedoc.itemType.自訂項目);
			l160m01cs.add(l160m01c);
		}

		return l160m01cs;

	}

	private List<L160M01C> copyL901m01aToL160m01c_OnlyCaseF(String mainId, String local) throws CapException {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<L160M01C> l160m01cs = new ArrayList<L160M01C>();

		List<CodeType> codeTypeList = codeTypeService.findByCodeTypeList(
				"lmsl901m01a_checkListCaseF", "zh_TW");

		for (CodeType item : codeTypeList) {
			L160M01C l160m01c = new L160M01C();
			l160m01c.setCreator(user.getUserId());
			l160m01c.setCreateTime(CapDate.getCurrentTimestamp());
			l160m01c.setItemSeq(item.getCodeOrder());
			l160m01c.setMainId(mainId);
			l160m01c.setItemContent(item.getCodeDesc());
			l160m01c.setItemType(UtilConstants.Usedoc.itemType.國內企金);
			l160m01cs.add(l160m01c);
		}

		// 自訂項目
		for (int i = 1; i < 7; i++) {
			L160M01C l160m01c = new L160M01C();
			l160m01c.setCreator(user.getUserId());
			l160m01c.setCreateTime(CapDate.getCurrentTimestamp());
			l160m01c.setItemSeq(i);
			l160m01c.setMainId(mainId);
			l160m01c.setItemType(UtilConstants.Usedoc.itemType.自訂項目);
			l160m01cs.add(l160m01c);
		}

		return l160m01cs;
	}

	private List<L160M01C> copyL901m01aToL160m01c_OnlyCaseJ03HeadItem1(String mainId, String local) throws CapException {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<L160M01C> l160m01cs = new ArrayList<L160M01C>();

		List<CodeType> codeTypeList = codeTypeService.findByCodeTypeList(
				"lmsl901m01a_checkListCaseJ03Head", "zh_TW");

		for (CodeType item : codeTypeList) {
			L160M01C l160m01c = new L160M01C();
			l160m01c.setCreator(user.getUserId());
			l160m01c.setCreateTime(CapDate.getCurrentTimestamp());
			l160m01c.setItemSeq(item.getCodeOrder());
			l160m01c.setMainId(mainId);
			l160m01c.setItemContent(item.getCodeDesc());
			l160m01c.setItemType(UtilConstants.Usedoc.itemType.國內企金);
			l160m01cs.add(l160m01c);
		}

		// 自訂項目
		for (int i = 1; i < 7; i++) {
			L160M01C l160m01c = new L160M01C();
			l160m01c.setCreator(user.getUserId());
			l160m01c.setCreateTime(CapDate.getCurrentTimestamp());
			l160m01c.setItemSeq(i);
			l160m01c.setMainId(mainId);
			l160m01c.setItemType(UtilConstants.Usedoc.itemType.自訂項目);
			l160m01cs.add(l160m01c);
		}

		return l160m01cs;
	}

	/**
	 * 檢查RPA資料
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings({ "unused", "unchecked" })
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult checkRpaData(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		Map<String, String> param = new HashMap<String, String>();

		// 檢核
		String mainId = params.getString(EloanConstants.MAIN_ID);

		// 檢核每一筆查詢名單 L161S01E
		List<L161S01E> l161s01es = (List<L161S01E>) lms1601Service
				.findListByMainId(L161S01E.class, mainId);
		for (L161S01E l161s01e : l161s01es) {
			if (l161s01e.getChkYN().equals(UtilConstants.DEFAULT.否)) {
				throw new CapMessageException("資料檢誤尚未完成", getClass());
			}
		}

		// 檢查是否有在查詢中的項目，提示
		Boolean ckeckSearching = false;
		List<L161S01D> l161s01ds = (List<L161S01D>) lms1601Service
				.findListByMainId(L161S01D.class, mainId);

		for (L161S01D l161s01d : l161s01ds) {
			if (l161s01d.getStatus().equals(UtilConstants.RPA.STATUS.查詢中)) {
				ckeckSearching = true;
			}
		}

		if (ckeckSearching) {
			result.set("confirmMsg", "已有查詢中項目，是否重新查詢！");
		} else {
			result.set("confirmMsg", "");
		}

		return result;
	}

	/**
	 * 重新引進查詢名單
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unused")
	@DomainAuth(value = AuthType.Modify)
	public IResult importQueryList(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString("oid"));

		lms1601Service.importQueryList(oid);

		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, "引進成功");

		return result;
	}

	/**
	 * RPA一鍵發查
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unused")
	@DomainAuth(value = AuthType.Modify)
	public IResult queryRpaQuery(PageParameters params)	throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString("oid"));

		lms1601Service.queryRpaQuery(oid);

		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, "發查成功");

		return result;
	}

	/**
	 * delete L161S01E
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unused")
	@DomainAuth(value = AuthType.Modify)
	public IResult deleteL161S01E(PageParameters params) throws CapException {

		// J-110-0540_05097_B1001 Web e-Loan企金授信配合調整E-loan系統動用審核表部分內容
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));

		// 取得list中所有資料組成的字串
		String listOid = params.getString("listOid");
		// 取得sign的資料
		String sign = Util.trim(params.getString("sign"));
		// 將已取得的字串轉換成一陣列，分割辨識為sign內容
		String[] oidArray = listOid.split(sign);

		lms1601Service.deleteListByOid(L161S01E.class, oidArray);

		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));

		return result;
	}

	/**
	 * RPA重新查詢
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unused")
	@DomainAuth(value = AuthType.Modify)
	public IResult queryRpaRetry(PageParameters params)	throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString("oid"));

		lms1601Service.queryRpaRetry(oid);

		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, "發查成功");

		return result;
	}

	/**
	 * RPA匯入
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify)
	public IResult importRpaDetail(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString("mainId"));
		String oid = params.getString("oid", "");

		lms1601Service.importRpaDetail(mainId, oid);
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, "引入成功");

		return result;
	}

	/**
	 * 動審表查詢名單明細檔
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL161s01e(PageParameters params)	throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS20APanel.class);
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);
		L161S01E l161s01e = lms1601Service.findModelByOid(L161S01E.class, oid);
		if (l161s01e == null) {
			// 查無資料
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.查無資料), getClass());
		}
		CapAjaxFormResult myForm2Result = DataParse.toResult(l161s01e);
		String[] strs = l161s01e.getCustRelation().split(",");
		List<String> list = new ArrayList<String>();
		for (int i = 0; i < strs.length; i++) {
			list.add(strs[i]);
		}

		myForm2Result.set("createBY2",
				UtilConstants.Casedoc.L120s04aCreateBY.系統產生.equals(l161s01e
						.getCreateBY()) ? pop.getProperty("L120S09a.createBY1")
						: pop.getProperty("L120S09a.createBY2"));

		myForm2Result.set("custRelation", list);

		String[] strTypes = l161s01e.getType().split(",");
		List<String> listType = new ArrayList<String>();
		for (int i = 0; i < strTypes.length; i++) {
			listType.add(strTypes[i]);
		}
		myForm2Result.set("type", listType);

		result.set("L160S01EForm01", myForm2Result);
		return result;
	}

	/**
	 * save 動審表查詢名單明細檔
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL161s01e(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS20APanel.class);

		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String formL160S01E = params.getString("L160S01EForm01");
		JSONObject json = JSONObject.fromObject(formL160S01E);
		String custId = Util.trim(json.optString("custId", ""));
		String dupNo = Util.trim(json.optString("dupNo", ""));
		String custName = Util.trim(json.optString("custName", ""));
		String idDateYear = Util.trim(json.optString("idDateYear", ""));
		String idDateMonth = Util.trim(json.optString("idDateMonth", ""));
		String idDateDay = Util.trim(json.optString("idDateDay", ""));
		String idSite = Util.trim(json.optString("idSite", ""));
		String idChangeType = Util.trim(json.optString("idChangeType", ""));

		String custRelation = Util.trim(params.getString("list"));
		String type = Util.trim(params.getString("listType"));

		if (Util.equals(custRelation, "")) {
			// pop.getProperty("L120S09a.createBY1")
			// 「與本案關係」
			// L120S09a.message07=「{0}」欄位不得為空白
			throw new CapMessageException(MessageFormat.format(
					pop.getProperty("L120S09a.message07"),
					pop.getProperty("L120S09a.custRelation")), getClass());

		}

		if (Util.equals(type, "")) {
			// pop.getProperty("L120S09a.createBY1")
			// 「查詢項目」
			// L120S09a.message07=「{0}」欄位不得為空白
			throw new CapMessageException(MessageFormat.format(
					pop.getProperty("L120S09a.message07"), "查詢項目"), getClass());

		}

		String[] strs = amlRelateService.getSortCustRelation(custRelation
				.split(","));
		// 對陣列進行排序
		if (strs.length > 0) {
			// Arrays.sort(strs);
			StringBuilder sb = new StringBuilder();
			sb.setLength(0);
			for (String str : strs) {
				sb.append((sb.length() > 0) ? "," : UtilConstants.Mark.SPACE)
						.append(str);
			}
			custRelation = sb.toString();
		}

		String[] strsType = type.split(",");
		// 對陣列進行排序
		if (strsType.length > 0) {
			StringBuilder sb = new StringBuilder();
			sb.setLength(0);
			for (String str : strsType) {
				sb.append((sb.length() > 0) ? "," : UtilConstants.Mark.SPACE)
						.append(str);
			}
			type = sb.toString();
		}

		if (Util.equals(custId, "")) {
			// 「本案關係人統編」欄位不得為空白
			throw new CapMessageException(MessageFormat.format(
					pop.getProperty("L120S09a.message07"),
					pop.getProperty("L120S09a.custId")), getClass());
		}
		if (Util.equals(dupNo, "")) {
			// 「重覆序號」欄位不得為空白
			throw new CapMessageException(MessageFormat.format(
					pop.getProperty("L120S09a.message07"),
					pop.getProperty("L120S09a.dupNo")), getClass());
		}

		// 檢核是否輸入身分證補換資訊
		boolean hasSUP1 = false; // 有查身分證

		for (int i = 0; i < strsType.length; i++) {
			if (Util.equals(strsType[i], UtilConstants.RPA.TYPE.身份證換補查詢)) {
				hasSUP1 = true;
			}
		}

		if (hasSUP1) {
			if (Util.equals(idDateYear, "") || Util.equals(idDateMonth, "")
					|| Util.equals(idDateDay, "") || Util.equals(idSite, "")
					|| Util.equals(idChangeType, "")) {
				// 「身分證換補資訊」欄位不得為空白
				throw new CapMessageException(MessageFormat.format(
						pop.getProperty("L120S09a.message07"), "身分證換補資訊"),
						getClass());
			}
		}

		// 檢查有沒有重複ID
		List<L161S01E> tl161s01es = null;
		if (Util.notEquals(custId, "") && Util.notEquals(dupNo, "")) {
			tl161s01es = lms1601Service.findListL161S01EByCustId(mainId,
					custId, dupNo);
		}

		L161S01E l161s01e = null;
		if (Util.notEquals(oid, "")) {
			l161s01e = lms1601Service.findModelByOid(L161S01E.class, oid);
		}

		if (l161s01e != null) {
			// 有資料(更新儲存)
			DataParse.toBean(formL160S01E, l161s01e);
		} else {
			// 如無資料則新建立
			l161s01e = new L161S01E();
			DataParse.toBean(formL160S01E, l161s01e);
			// 設定一些初始化內容
			l161s01e.setMainId(mainId);
			l161s01e.setCreateTime(CapDate.getCurrentTimestamp());
			l161s01e.setCreator(user.getUserId());
			l161s01e.setCreateBY(UtilConstants.Casedoc.L120s04aCreateBY.人工產生);
		}

		if (tl161s01es != null && !tl161s01es.isEmpty()) {
			for (L161S01E tl161s01e : tl161s01es) {
				if (Util.notEquals(tl161s01e.getOid(), l161s01e.getOid())) {

					// L120S09a.message08=「{0}」統編或戶名已存在
					throw new CapMessageException(MessageFormat.format(
							pop.getProperty("L120S09a.message08"), custId
									+ dupNo + " " + custName), getClass());

				}
			}

		}

		if (custRelation != null) {
			l161s01e.setCustRelation(custRelation);
		} else {
			l161s01e.setCustRelation(UtilConstants.Mark.SPACE);
		}

		if (type != null) {
			l161s01e.setType(type);
		} else {
			l161s01e.setType(UtilConstants.Mark.SPACE);
		}

		// 戶名轉半形
		l161s01e.setCustName(Util.toSemiCharString(Util.trim(l161s01e
				.getCustName())));

		// 進行檢核的判斷寫在此
		l161s01e.setChkYN(UtilConstants.DEFAULT.是);
		l161s01e.setMemo("");

		lms1601Service.save(l161s01e);

		if (params.getAsBoolean("showMsg", true)) {
			// 印出儲存成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));
		}

		result.set("newOid", l161s01e.getOid());
		return result;
	}// ;
		// J-109-0150_10702_B1001 Web e-Loan IVR頁籤由模擬動審移至動審表

	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult addIVRList(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		List<String> datas = Arrays.asList(params.getStringArray("rows"));
		if (!Util.isEmpty(datas) && datas.size() > 0) {
			lms1601Service.saveIVRFlag(
					params.getString(EloanConstants.MAIN_OID), datas);
		}
		return result;
	}

	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteIVRList(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String oid = params.getString(EloanConstants.MAIN_OID);
		String datas = params.getString("record_FileName");
		String deleteCustId = params.getString("deleteCustId");

		lms1601Service.deleteIVRFlag(oid, deleteCustId, datas);

		return result;
	}

	/**
	 * 檢查主從債務人都有在0024建檔，否則沒辦法判斷是不是法人
	 * 
	 * 
	 * J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult chkAllGuarantorHas0024(PageParameters params) throws CapException {

		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1601M01Page.class);
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String mainCntrNo = Util.trim(params.getString("cntrNo"));
		L160M01A l160m01a = lms1601Service.findL160M01AByMaindId(mainId);

		Map<String, String> param = new HashMap<String, String>();

		// 檢核是否登錄主從債務表
		List<L162S01A> l162m01as = lms1601Service.findL162s01aByMainIdCntrno(
				mainId, mainCntrNo);

		// 檢查主從人債務表是否必填欄位都有填寫
		HashMap<String, String> tempMap = new HashMap<String, String>();
		// 檢查債務人是否存在
		HashMap<String, String> getAllCustIdMap = new HashMap<String, String>();
		for (L162S01A l162s01a : l162m01as) {
			String cntrNo = Util.trim(l162s01a.getCntrNo());
			String showKey = StrUtils.concat(
					l162s01a.getCustId().toUpperCase(), " ", l162s01a
							.getDupNo().toUpperCase(), " ", l162s01a.getRId()
							.toUpperCase(), " ", l162s01a.getRDupNo()
							.toUpperCase());
			if (Util.isEmpty(cntrNo)) {
				// L160M01A.message63=「主從債務人資料表」額度序號不得為空!!
				param.put("msg", pop.getProperty("L160M01A.message63"));
				throw new CapMessageException(RespMsgHelper.getMessage(
						UtilConstants.AJAX_RSP_MSG.執行有誤, param), getClass());
			}
			getAllCustIdMap.put(l162s01a.getCustId() + l162s01a.getDupNo(), "");
			getAllCustIdMap.put(l162s01a.getRId() + l162s01a.getRDupNo(), "");

			// 主債務人的檢查
			if (l162s01a.getCustId().toUpperCase()
					.equals(l162s01a.getRId().toUpperCase())
					&& l162s01a.getDupNo().toUpperCase()
							.equals(l162s01a.getRDupNo().toUpperCase())) {
			} else {
				if (Util.isEmpty(l162s01a.getRKindD())
						|| Util.isEmpty(l162s01a.getRKindM())) {
					tempMap.put(showKey, l162s01a.getCntrNo().toUpperCase());
				}

			}

		}

		Map<String, Object> custData = null;
		// J-103-0299-001
		// Web e-Loan企金額度明細表保證人新增保證比例
		Map<String, String> custBusCd = new HashMap<String, String>();

		// 檢查債務人是否都有建檔
		StringBuffer temp0024 = new StringBuffer();
		for (String key : getAllCustIdMap.keySet()) {
			String custId = key.substring(0, key.length() - 1);
			String dupNo = key.substring(key.length() - 1, key.length());

			custData = misCustdataService.findAllByByCustIdAndDupNo(custId,
					dupNo);
			if (custData == null || custData.isEmpty()) {
				temp0024.append(temp0024.length() > 0 ? "" : "");
				temp0024.append(custId + " " + dupNo);
			} else {
				// J-103-0299-001
				// Web e-Loan企金額度明細表保證人新增保證比例
				custBusCd.put(Util.trim(custId) + Util.trim(dupNo), custData
						.get("BUSCD") != null ? custData.get("BUSCD")
						.toString() : "999999");
			}
		}
		if (temp0024.length() > 0) {
			// EFD3009=ERROR|$\{custId\}客戶中文檔0024 無此借款人資料 ！！|
			param.put("custId", temp0024.toString());
			throw new CapMessageException(RespMsgHelper.getMessage("EFD3009", param), getClass());
		}

		StringBuffer temp = new StringBuffer("");
		if (!tempMap.isEmpty()) {
			for (String key : tempMap.keySet()) {
				temp.append("<br/>");
				temp.append(key);
				temp.append(" ");
				temp.append(tempMap.get(key));
			}
			// L162M01A.message1=「主從債務人資料表」以下借款人，資料不完整，不得執行本功能{0}
			param.put("msg", MessageFormat.format(
					pop.getProperty("L162M01A.message1"), temp.toString()));
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤, param), getClass());
		}

		return result;
	}

	/**
	 * 儲存連保人信用品質順序設定
	 * 
	 * 
	 * J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult savePriority(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = params.getString(EloanConstants.MAIN_ID);
		String cntrNo = Util.trim(params.getString("cntrNo"));

		L160M01A l160m01a = lms1601Service.findL160M01AByMaindId(mainId);

		String data = params.getString("data");
		JSONObject jsonData = JSONObject.fromObject(data);

		List<L162S01A> l162s01as = lms1601Service.findL162s01aByMainIdCntrno(
				mainId, cntrNo);

		// 取得必要順序的保證人
		// 再刪選需要的保證人(保證人企業戶且為)
		List<L162S01A> mustL162s01as = lms1601Service.findL162s01aNeedPriority(
				l162s01as, cntrNo);

		if (mustL162s01as != null && !mustL162s01as.isEmpty()) {

			// 檢查保證比例100%前是否都有信用品質順序
			BigDecimal totGuaPercent = BigDecimal.ZERO;
			int setCount = 0;
			for (L162S01A l162s01a : mustL162s01as) {
				String oid = l162s01a.getOid();
				if (jsonData.has(oid)) {

					if (Util.notEquals(Util.trim(jsonData.getString(oid)), "")) {
						BigDecimal guaPercent = l162s01a.getGuaPercent() == null ? BigDecimal.ZERO
								: l162s01a.getGuaPercent();
						totGuaPercent = totGuaPercent.add(guaPercent);
						setCount = setCount + 1;
					}

				}

			}

			if (mustL162s01as.size() > setCount
					&& totGuaPercent.compareTo(Util.parseBigDecimal("100")) < 0) {
				// L140M01a.message262=保證人必需設定信用品質順序，直到該額度之保證人之負担保證責任比率合計達100%。
				Properties prop = MessageBundleScriptCreator
						.getComponentResource(LMS1401S02Panel.class);
				String msg = prop.getProperty("L140M01a.message262");
				throw new CapMessageException(RespMsgHelper.getMessage(
						UtilConstants.AJAX_RSP_MSG.執行有誤, msg), getClass());
			}
		}

		// 檢查信用品質順序是否有跳號
		int i = 0;
		int maxSeq = 0;
		Iterator<String> keys = jsonData.keys();
		while (keys.hasNext()) {
			String oid = keys.next();
			String id = jsonData.getString(oid);

			if (Util.notEquals(Util.trim(jsonData.getString(oid)), "")) {
				i = i + 1;

				int thisSeq = Util.parseInt(Util.trim(jsonData.getString(oid)));
				if (thisSeq == 0) {
					// L162M01A.message3=保證人信用品質順序必須從1開始依序編排，不得跳號。
					Properties prop = MessageBundleScriptCreator
							.getComponentResource(LMS1601M01Page.class);
					String msg = prop.getProperty("L162M01A.message3");
					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, msg),
							getClass());
				}

				if (thisSeq >= maxSeq) {
					maxSeq = Util.parseInt(Util.trim(jsonData.getString(oid)));
				}

			}

		}

		if (i != maxSeq) {
			// L162M01A.message3=保證人信用品質順序必須從1開始依序編排，不得跳號。
			Properties prop = MessageBundleScriptCreator
					.getComponentResource(LMS1601M01Page.class);
			String msg = prop.getProperty("L162M01A.message3");
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤, msg), getClass());
		}

		for (L162S01A l162s01a : l162s01as) {
			String oid = l162s01a.getOid();
			if (jsonData.has(oid)) {

				if (Util.equals(Util.trim(jsonData.getString(oid)), "")) {
					l162s01a.setPriority(null);

				} else {
					l162s01a.setPriority(Util.parseBigDecimal(Util
							.trim(jsonData.getString(oid))));
				}
				lms1601Service.save(l162s01a);
			}
		}

		return result;// 傳回執行這個動作的AjAX
	}

	/**
	 * 取得保證人信評順序
	 * 
	 * 
	 * J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult getGuarantorCreditPriority(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = params.getString(EloanConstants.MAIN_ID);

		String data = params.getString("data");

		Map<String, String> map = lmsService.getGuarantorPriority("L160M01A",
				data);

		// JSONObject jsonData = JSONObject.fromObject(data);
		// Map<String, String> map = new LinkedHashMap<String, String>();
		// List<L140M01I> l140m01is = lms1401Service.findL140m01iListWithRType(
		// tabFormMainId, UtilConstants.lngeFlag.連帶保證人);
		// int i = 0;
		//
		// Iterator<String> keys = jsonData.keys();
		//
		// while (keys.hasNext()) {
		// String oid = keys.next();
		// String id = jsonData.getString(oid);
		// i = i + 1;
		//
		// map.put(Util.trim(Util.parseBigDecimal(id)),
		// Util.trim(i));
		// }

		result.putAll(map);
		return result;// 傳回執行這個動作的AjAX
	}

	/**
	 * 引進建檔建檔系統主從債務人順序
	 * 
	 * 
	 * J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult applyEllngteePriority(PageParameters params)	throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1601M01Page.class);
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String l162s01aOid = params.getString("l162s01aOid"); // 只會有一筆
		String data = params.getString("data");
		L160M01A l160m01a = lms1601Service.findL160M01AByMaindId(mainId);
		L162S01A l162s01a = lms1601Service.findModelByOid(L162S01A.class,
				l162s01aOid);

		if (l162s01a == null) {
			l162s01a = new L162S01A();
		}

		String custId = Util.trim(l162s01a.getCustId());
		String dupNo = Util.trim(l162s01a.getDupNo());
		String cntrNo = Util.trim(l162s01a.getCntrNo());

		if (Util.isEmpty(cntrNo)) {
			Map<String, String> param = new HashMap<String, String>();
			// L160M01A.message63=「主從債務人資料表」額度序號不得為空!!
			param.put("msg", prop.getProperty("L160M01A.message63"));
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤, param), getClass());
		}

		List<Map<String, Object>> ellngteeDatas = misEllngteeService
				.getAllByCustIdCntrNo(custId, dupNo, cntrNo);

		if (ellngteeDatas == null || ellngteeDatas.isEmpty()) {
			// EFD0036=INFO|查無資料!|
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0036"), getClass());
		}

		Map<String, String> ellngteeMap = new LinkedHashMap<String, String>();
		boolean hasPriority = false;
		for (Map<String, Object> ellngteeData : ellngteeDatas) {

			String lngeFlag = (String) (ellngteeData.get("LNGEFLAG") == null ? ""
					: ellngteeData.get("LNGEFLAG"));

			// C: 共同借款人
			// D: 共同發票人　
			// E: 票據債務人（指金融交易之擔保背書）
			// G: 連帶保證人，擔保品提供人兼連帶保證人
			// L: 連帶借款人，連帶債務人，擔保品提供人兼連帶債務人
			// S: 擔保品提供人
			// N: ㄧ般保證人

			if (Util.equals(lngeFlag, UtilConstants.lngeFlag.連帶保證人)
					|| Util.equals(lngeFlag, UtilConstants.lngeFlag.ㄧ般保證人)) {
				String priority = ((ellngteeData.get("PRIORITY") == null || Util
						.equals(Util.trim(ellngteeData.get("PRIORITY")), "0")) ? " "
						: Util.trim(ellngteeData.get("PRIORITY")));

				if (Util.notEquals(Util.trim(priority), "")
						&& Util.notEquals(Util.trim(priority), "0")) {

					// 990以上是透過批次設定的，分行應該要重新自己設定一次
					// Step1->991.保證人國別都相同或只有一個保證人
					// Step2->992.裡面有非100的，則以最大的當代表............????????.....要不要再調整
					// Step3->993.保證人RATING最高
					// Step4->994.任一保證人國別與額度最終風險國別相同
					// Step5->995.任一保證人國別與借款人相同
					// Step6->996.保證人國家RATING最高
					// Step6->997.無法判斷(非以上STEP)
					if (Util.parseBigDecimal(Util.trim(priority)).compareTo(
							Util.parseBigDecimal("990")) <= 0) {
						hasPriority = true;
					}

				}

				String lngeId = (String) (ellngteeData.get("LNGEID") == null ? ""
						: Util.trim(ellngteeData.get("LNGEID")));
				String dupNo1 = (String) (ellngteeData.get("DUPNO1") == null ? ""
						: Util.trim(ellngteeData.get("DUPNO1")));

				String key = lngeId + "-" + dupNo1;
				ellngteeMap.put(key, priority);
			}

		}

		if (!hasPriority) {
			// ELLNGTEE 資料都沒有一筆有設定PRIORITY
			// EFD0036=INFO|查無資料!|
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0036"), getClass());
		}

		JSONObject jsonData = JSONObject.fromObject(data);
		Map<String, String> map = new LinkedHashMap<String, String>();

		List<L162S01A> l162s01as = lms1601Service.findL162s01aByMainIdCntrno(
				mainId, cntrNo);

		Iterator<String> keys = jsonData.keys();

		while (keys.hasNext()) {
			// 設定初始值
			String oid = keys.next();
			String id = jsonData.getString(oid);
			map.put(Util.trim(Util.parseBigDecimal(id)), " ");
		}

		for (L162S01A tl162s01a : l162s01as) {
			String oid = tl162s01a.getOid();
			if (jsonData.has(oid)) {
				String id = jsonData.getString(oid);
				String rId = Util.trim(tl162s01a.getRId());
				String rDupNo = Util.trim(tl162s01a.getRDupNo());
				String key = rId + "-" + rDupNo;
				if (ellngteeMap.containsKey(key)) {

					map.put(id, MapUtils.getString(ellngteeMap, key));
				}

			}
		}

		result.putAll(map);
		return result;// 傳回執行這個動作的AjAX
	}

	/**
	 * 整批引進最新資料
	 * 
	 * J-110-0040_05097_B1001 Web e-Loan增加「本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)」註記
	 * 
	 * @param params
	 *            PageParameters
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult entireApplyNew(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1601M01Page.class);
		boolean needSave = false;
		String[] oids = params.getStringArray("oids");
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String allCheackedVal = params.getString("allCheackedVal");
		L160M01A l160m01a = lms1601Service.findL160M01AByMaindId(mainId);

		List<L162S01A> l162s01as = lms1601Service.findL162S01AByOids(oids);

		String guaNaExposure = params.getString("guaNaExposure");

		String toDayStr = CapDate.formatDate(new Date(),
				UtilConstants.DateFormat.YYYY_MM_DD);
		String toDayStr1 = toDayStr.replace("-", "");

		// 上個月
		String newDateStr = CapDate.formatyyyyMMddToDateFormat(
				CapDate.addMonth(toDayStr1, -1),
				UtilConstants.DateFormat.YYYY_MM);

		// 上個月底最後一天
		String preMonLastDate = Util
				.toAD(CapDate.shiftDays(CapDate.addMonth(
						CapDate.parseDate(newDateStr + "-01"), 1), -1));

		// 上個月第一天
		String preMonFirstDate = Util.getLeftStr(preMonLastDate, 7) + "-01";

		HashMap<String, String> doneCntrNoMapA01 = new HashMap<String, String>();

		// 如果文件包含非編製中的不能刪除
		for (L162S01A l162s01a : l162s01as) {

			String cntrNo = Util.trim(l162s01a.getCntrNo());
			String custId = Util.trim(l162s01a.getCustId());
			String dupNo = Util.trim(l162s01a.getDupNo());

			String[] entireApplyCheckList = Util.trim(allCheackedVal).split(
					UtilConstants.Mark.SPILT_MARK);
			for (String checkItem : entireApplyCheckList) {
				if (Util.equals(checkItem, "A01")) {
					// 本行國家暴險是否以保證人國別為計算基準(取代最終風險國別

					if (Util.notEquals(cntrNo, "")) {

						// BY 額度整批設定
						if (!doneCntrNoMapA01.containsKey(cntrNo)) {

							List<L162S01A> tl162s01as = lms1601Service
									.findL162s01aByMainIdCntrno(mainId, cntrNo);
							for (L162S01A tl162s01a : tl162s01as) {
								String rType = Util.trim(tl162s01a.getRType());
								if (Util.notEquals(rType, "C")
										&& Util.notEquals(rType, "S")) {
									tl162s01a.setGuaNaExposure(guaNaExposure);
									lms1601Service.save(tl162s01a);
								}
							}

							doneCntrNoMapA01.put(cntrNo, cntrNo);
						}

					}

				}

			}

		}
		CapAjaxFormResult result = new CapAjaxFormResult();

		return result;
	}

	/**
	 * J-110-0540_05097_B1001 Web e-Loan企金授信配合調整E-loan系統動用審核表部分內容
	 * 
	 * @param l160m01a
	 * @return
	 */
	String chkAndClearL161s01aTtypeData(L160M01A l160m01a) {

		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1601M01Page.class);

		// J-110-0540_05097_B1001 Web e-Loan企金授信配合調整E-loan系統動用審核表部分內容
		// 動審表主檔授信期限選擇3.詳額度動用資訊一覽表
		StringBuffer temp2 = new StringBuffer("");
		Set<L161S01A> l161s01as = l160m01a.getL161S01A();
		if (Util.equals(Util.trim(l160m01a.getTType()), "3")) {
			// 3的時候要檢查

			// 統計未填欄位數
			int countItme = 1;

			if (!l161s01as.isEmpty()) {
				for (L161S01A l161s01a : l161s01as) {
					StringBuffer tTypeErr = new StringBuffer("");
					String cntrNo = Util.trim(l161s01a.getCntrNo());

					// 檢核動用期限是否登錄
					if (Util.isEmpty(Util.trim(l161s01a.getTType_s01a()))) {

						// L160M01A.tType= 授信契約書
						// L160M01A.guFromDate=期間
						countItme = this
								.setHtmlBr(
										tTypeErr,
										countItme,
										pop.getProperty("L160M01A.tType")
												+ pop.getProperty("L160M01A.guFromDate"));

					} else if ("1".equals(l161s01a.getTType_s01a())) {

						switch (Util.parseInt(l161s01a.getUseSelect_s01a())) {
						case 1:
							if (Util.isEmpty(l161s01a.getUseFromDate_s01a())
									|| Util.isEmpty(l161s01a
											.getUseEndDate_s01a())) {
								// L160M01A.useDate=動用期限
								countItme = this.setHtmlBr(tTypeErr, countItme,
										pop.getProperty("L160M01A.useDate"));

							}
							break;
						case 2:
							if (Util.isEmpty(l161s01a.getUseMonth_s01a())) {
								// L160M01A.useDate=動用期限
								countItme = this.setHtmlBr(tTypeErr, countItme,
										pop.getProperty("L160M01A.useDate"));

							}
							break;
						case 3:
							if (Util.isEmpty(l161s01a.getUseOther_s01a())) {
								// L160M01A.useDate=動用期限
								countItme = this.setHtmlBr(tTypeErr, countItme,
										pop.getProperty("L160M01A.useDate"));
							}
							break;
						default:
							// L160M01A.useDate=動用期限
							countItme = this.setHtmlBr(tTypeErr, countItme,
									pop.getProperty("L160M01A.useDate"));
						}
					} else if ("2".equals(l161s01a.getTType_s01a())) {

						switch (Util.parseInt(l161s01a.getUseSelect_s01a())) {
						case 1:
							if (Util.isEmpty(l161s01a.getUseFromDate_s01a())
									|| Util.isEmpty(l161s01a
											.getUseEndDate_s01a())) {
								// L160M01A.useDate=動用期限
								countItme = this.setHtmlBr(tTypeErr, countItme,
										pop.getProperty("L160M01A.useDate"));
							}

							break;
						case 2:
							if (Util.isEmpty(l161s01a.getUseMonth_s01a())) {
								// L160M01A.useDate=動用期限
								countItme = this.setHtmlBr(tTypeErr, countItme,
										pop.getProperty("L160M01A.useDate"));
								;
							}
							break;
						case 3:
							if (Util.isEmpty(l161s01a.getUseOther_s01a())) {
								// L160M01A.useDate=動用期限
								countItme = this.setHtmlBr(tTypeErr, countItme,
										pop.getProperty("L160M01A.useDate"));
							}
							break;
						default:
							// L160M01A.useDate=動用期限
							countItme = this.setHtmlBr(tTypeErr, countItme,
									pop.getProperty("L160M01A.useDate"));
						}

						switch (Util.parseInt(l161s01a.getLnSelect_s01a())) {
						case 1:
							if (Util.isEmpty(l161s01a.getLnFromDate_s01a())
									|| Util.isEmpty(l161s01a
											.getLnEndDate_s01a())) {
								// L160M01A.lnDate=授信期限
								countItme = this.setHtmlBr(tTypeErr, countItme,
										pop.getProperty("L160M01A.lnDate"));

							}

							break;
						case 2:
							if (Util.isEmpty(l161s01a.getLnMonth_s01a())
									|| Util.isEmpty(l161s01a.getLnYear_s01a())) {
								// L160M01A.lnDate=授信期限
								countItme = this.setHtmlBr(tTypeErr, countItme,
										pop.getProperty("L160M01A.lnDate"));
							}
							break;
						case 3:
							if (Util.isEmpty(Util.trim(l161s01a
									.getLnOther_s01a()))) {
								// L160M01A.lnDate=授信期限
								countItme = this.setHtmlBr(tTypeErr, countItme,
										pop.getProperty("L160M01A.lnDate"));
							}
							break;
						default:
							// L160M01A.lnDate=授信期限
							countItme = this.setHtmlBr(tTypeErr, countItme,
									pop.getProperty("L160M01A.lnDate"));
						}
					}

					if (tTypeErr.length() > 0) {
						tTypeErr.insert(0, cntrNo + ":");
						temp2.append(
								Util.equals(temp2.toString(), "") ? ""
										: "<br/>").append(tTypeErr);
						l161s01a.setChkYN("N");
						lms1601Service.save(l161s01a);
					}

				}
			}

			if (temp2.length() > 0) {
				// 尚有必填欄位未填
				// L160M01A.title17=額度動用資訊一覽表
				temp2.insert(
						0,
						pop.getProperty("L160M01A.title17")
								+ pop.getProperty("L160M01A.message75")
								+ "<br/>");

			}

		} else {
			// 非3的時候要清除

			// J-110-0540_05097_B1001 Web e-Loan企金授信配合調整E-loan系統動用審核表部分內容
			// 清除額度動用一覽表的授信契約期間
			if (!l161s01as.isEmpty()) {
				for (L161S01A l161s01a : l161s01as) {
					this.clearL161s01aTtypeData(l161s01a);
					lms1601Service.save(l161s01a);
				}
			}

		}

		return temp2.toString();
	}

	/**
	 * J-110-0540_05097_B1001 Web e-Loan企金授信配合調整E-loan系統動用審核表部分內容
	 * 
	 * @param l160m01a
	 * @return
	 */
	void clearL161s01aTtypeData(L161S01A l161s01a) {

		/**
		 * 設定授信契約書期別
		 * <p/>
		 * 1短期2中長期
		 **/
		l161s01a.setTType_s01a("");

		/**
		 * 設定動用期間選項
		 * <p/>
		 * 100/10/06新增<br/>
		 * YYYY-MM-DD~ YYYY-MM-DD<br/>
		 * 自首動日起MM個月<br/>
		 * 其他
		 **/
		l161s01a.setUseSelect_s01a("");

		/**
		 * 設定動用期間-起始日期
		 * <p/>
		 * 1.YYYY-MM-DD~ YYYY-MM-DD
		 **/
		l161s01a.setUseFromDate_s01a(null);

		/**
		 * 設定動用期間-截止日期
		 * <p/>
		 * 1.YYYY-MM-DD~ YYYY-MM-DD
		 **/
		l161s01a.setUseEndDate_s01a(null);

		/**
		 * 設定動用期間-月數
		 * <p/>
		 * 2.自首動日起MM個月
		 **/
		l161s01a.setUseMonth_s01a(null);

		/**
		 * 設定動用期間-其他
		 * <p/>
		 * 3.其他
		 **/
		l161s01a.setUseOther_s01a("");

		/**
		 * 設定授信期間選項
		 * <p/>
		 * 100/10/06新增<br/>
		 * YYYY-MM-DD~ YYYY-MM-DD<br/>
		 * 自首動日起MM個月<br/>
		 * 其他
		 **/
		l161s01a.setLnSelect_s01a("");

		/**
		 * 設定授信期間-起始日期
		 * <p/>
		 * 1.YYYY-MM-DD~ YYYY-MM-DD
		 **/
		l161s01a.setLnFromDate_s01a(null);

		/**
		 * 設定授信期間-截止日期
		 * <p/>
		 * 1.YYYY-MM-DD~ YYYY-MM-DD
		 **/
		l161s01a.setLnEndDate_s01a(null);

		/**
		 * 設定授信期間-年數
		 * <p/>
		 * 100/10/11新增<br/>
		 * 2.自首動日起YY年
		 **/
		l161s01a.setLnYear_s01a(null);

		/**
		 * 設定授信期間-月數
		 * <p/>
		 * 2.自首動日起MM個月
		 **/
		l161s01a.setLnMonth_s01a(null);

		/**
		 * 設定授信期間-其他
		 * <p/>
		 * 3.其他
		 **/
		l161s01a.setLnOther_s01a("");

		return;
	}

	/**
	 * 試算新利率起息日
	 * 
	 * J-111-0214_05097_B1001 Web e-Loan國內企金動用審核表新增可適用新利率計算減免息相關功能
	 * 
	 * @param params
	 *            PageParameters
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult cacuRescueChgRateEffectDate(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String mainId = params.getString(EloanConstants.MAIN_ID);

		L160M01A l160m01a = lms1601Service.findL160M01AByMaindId(mainId);

		CapAjaxFormResult result = new CapAjaxFormResult();

		String formL161m01a = params.getString("L161M01AForm");

		JSONObject jsonL161m01a = JSONObject.fromObject(formL161m01a);

		// L161S01A l161s01a = new L161S01A();
		// DataParse.toBean(jsonL161m01a, l161s01a);

		String rescueChgRateSingDate = jsonL161m01a.optString(
				"rescueChgRateSingDate", "");

		Map<String, String> returnMap = lms1601Service
				.cacuRescueChgRateEffectDate(rescueChgRateSingDate,
						rescueChgRateSingDate);
		String isOk = MapUtils.getString(returnMap, "isOk");
		String errorMsg = MapUtils.getString(returnMap, "errorMsg");
		String rescueChgRateEffectDate = MapUtils.getString(returnMap,
				"rescueChgRateEffectDate");
		String aloanCacuRateDate = MapUtils.getString(returnMap,
				"aloanCacuRateDate");

		if (Util.equals(isOk, "Y")) {
			result.set("isOk", "Y");
			result.set("rescueChgRateEffectDate", rescueChgRateEffectDate);
			result.set("aloanCacuRateDate", aloanCacuRateDate);
		} else {
			result.set("isOk", "N");
			result.set("errorMsg", errorMsg);
		}

		return result;
	}

	/**
	 * J-111-0207 國內企金，動用檢核表增加一項目:「信保案件負責人客戶基本資料檔是否已建有配偶資料」
	 * 
	 * 重新引入檢核結果
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult reloadSmeMateInfo(PageParameters params)	throws CapException {
		String mainId = params.getString(EloanConstants.MAIN_ID);
		CapAjaxFormResult result = new CapAjaxFormResult();

		checkSmeMateInfo(mainId);

		Map<String, String> checkResult = lms1601Service
				.findSmeMateInfoCheckResult(mainId);
		result.set("smeMateInfo", checkResult.get("smeMateInfo"));
		if (Util.isNotEmpty(checkResult.get("noMateBorrower"))) {
			result.set("noMateBorrower", checkResult.get("noMateBorrower"));
		}
		return result;
	}

	/**
	 * J-111-0207 國內企金，動用檢核表增加一項目:「信保案件負責人客戶基本資料檔是否已建有配偶資料」
	 * 
	 * 檢核信保案件負責人是否已建有配偶資料後寫入DB
	 * 
	 * @param mainId
	 *            (L160M01A mainId)
	 */
	private void checkSmeMateInfo(String mainId) {
		L160M01A l160m01a = lms1601Service.findL160M01AByMaindId(mainId);

		if (l160m01a == null) {
			return;
		}
		Set<L161S01A> l161s01as = l160m01a.getL161S01A();

		Map<String, String> custIdMap = new HashMap<String, String>();// 該動審表所引用之全部額度明細為信保的借款人Map
		Map<String, String> principalMap = new HashMap<String, String>();// 信保借款人對應的負責人Map
		String key = "";

		if (Util.isEmpty(l161s01as)) {
			return;
		}

		for (L161S01A l161s01a : l161s01as) {
			L140M01A l140m01a = lms1401Service.findL140m01aByMainId(Util
					.trim(l161s01a.getCntrMainId()));

			if (l140m01a == null) {// 找不到額度明細表
				continue;
			}
			key = Util.trim(l140m01a.getCustId()).toUpperCase()
					+ Util.trim(l140m01a.getDupNo()).toUpperCase();

			if (Util.isEmpty(key)) {
				continue;
			}
			// 取得額度明細表信保案件類別 1:中小信保 2:海外信保 3:國家融資保證機制 非信保案件為空值("")
			String smeKind = lms1401Service.getSmeKind(l140m01a);
			String chkResult = "";
			String principal = "";

			// 若為信保額度則放入map
			if (Util.isNotEmpty(smeKind)) {// smeKind不為空值則為信保案件

				if (custIdMap.containsKey(key)) {// 有檢查過的就不檢查，直接取得結果
					chkResult = custIdMap.get(key);
					principal = principalMap.get(key);
				} else {
					// 用借款人去撈0024負責人
					Map<String, Object> principalData = misElcus25Service
							.getMiselcus25_Principal(
									Util.trim(l140m01a.getCustId()),
									Util.trim(l140m01a.getDupNo()));

					if (principalData == null || Util.isEmpty(principalData)) {
						// 找不到借款人
						// (主政說0024不可能沒有建檔該企業資料，如果真的沒有就不檢查，但因為是逐筆寫入檢查結果，所以這邊還是壓2)
						chkResult = "2";// 2:無(未建配偶檔)
					} else {
						// 取借款人的負責人資料
						String tCustId = Util
								.trim(principalData.get("TCUSTID"));
						String tDupNo = Util.trim(principalData.get("TDUPNO"));

						// 該借款人沒有建負責人資料檔案
						// (主政說沒有建檔負責人資料的不檢查，但因為是逐筆寫入檢查結果，所以這邊還是壓2)
						if (Util.isEmpty(tCustId)) {
							chkResult = "2";// 2:無(未建配偶檔)
						} else {
							principal = tCustId + tDupNo;
							// 有負責人要用該負責人去0024撈配偶資料
							Map<String, Object> principalMateData = misCustdataService
									.findPrincipalMate(tCustId, tDupNo);
							if (principalMateData == null
									|| Util.isEmpty(principalMateData)) {
								// 該負責人沒有建配偶檔 ->NG
								chkResult = "2";// 2:無(未建配偶檔-不管負責人的婚姻狀態)
							} else {
								// OK
								chkResult = "1";// 1:有(有建配偶檔)
							}
						}
					}
					custIdMap.put(key, chkResult);// 紀錄借款人及結果
					principalMap.put(key, principal);// 紀錄這次檢查的負責人(主政表示若沒建配偶檔要顯示公司負責人，而不是借款人(企業))
				}
			} else {
				chkResult = "0"; // 0:NA(非信保案件不需檢查)
			}

			l161s01a.setSmeMateInfo(chkResult);
			l161s01a.setSmeChkMatePrincipal(principal);
			lms1601Service.save(l161s01a);
		}
	}

	/**
	 * 掛件文號改成動態顯示名稱
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult reloadRescueItemData(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String rescueItem = params.getString("rescueItem");

		String rescueNoName = lms1601Service.reloadRescueItemData(rescueItem);

		result.set("rescueNo_Name", rescueNoName);

		return result;
	}
	
	/**
	 * 查詢分行為海外還是國內
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryShareBrIdType(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString("cntrMainId"));
		String shareBrId = params.getString("shareBrId");

		if (UtilConstants.BrNoType.國外.equals(branchService.getBranch(shareBrId)
				.getBrNoFlag())) {
			result.set("type", TypCdEnum.海外.getCode());
		} else {
			result.set("type", "");
		}
		// 檢查目前分行是否已經登錄
		L140M01E_AF l140m01e_af = lms1401Service.findL140m01e_afByUniqueKey(mainId, shareBrId);
		result.set("have", l140m01e_af != null ? true : false);
		return result;
	}
	
	/**
	 * 檢查原案額度序號(從額度明細複製過來，這僅用到 justSave=3聯行攤貸比例, 相關判斷先拿掉)
	 * 
	 * @param params
	 *            <pre>
	 *            {String}cntrNo 額度序號
	 *            {String}oid額度明細表文件編號
	 * </pre>
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult checkCntrno(PageParameters params) throws CapException {

		String cntrNo = params.getString("cntrNo", "");
		String snoKind = params.getString("snoKind", "");
		String cntrMainId = params.getString("cntrMainId", "");
		L140M01A l140m01a = lms1401Service.findL140m01aByMainId(cntrMainId);
		if (Util.isEmpty(cntrNo)) {
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤), getClass());
		}

		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set("ownBrName",
				branchService.getBranchName(cntrNo.substring(0, 3)));

		Properties prop = MessageBundleScriptCreator.getComponentResource(LMS1601M01Page.class);
		String custId = l140m01a.getCustId();
		String dupNo = l140m01a.getDupNo();

		// 2012_05_22_ 建霖說看已核准簽報書的額度明細表額度序號與攤貸行(l140m01e)的
		List<L140M01A> l140m01as = lms1401Service.findL140m01aBycntrNo(cntrNo,
				custId, dupNo);
		int countE = eloandbBASEService.findL140M01EByCustIdAndDupNoAndCntrno(
				custId, dupNo, cntrNo);
		
		//J-112-0522 依金管會112年度專案檢查報告面請改善事項辦理，因營業單位漏未建檔報送金管會AI370聯合授信案基本資料(國內BTT-L556、海外AS400-3K30)，致本行每月報送報表產生報送錯誤，擬將上述建檔機制移至E-LOAN-授信管理系統-動用審核表內，於動用審核表新增一頁籤(聯貸案基本資料)，營業單位於簽約動審時，須完成建檔作業。
		//舊案額度序號檢核多看LN.LNF277有存在也可以建
		int count277 = 0;
		String controlflag = Util.trim(lmsService.getSysParamDataValue("J-112-0522_CNTRNO_CHK"));
		if(Util.equals(UtilConstants.DEFAULT.是, controlflag)){
			if (l140m01as.isEmpty() && countE == 0) {
				count277 = misdbBASEService.findLNF277ByCntrno(cntrNo);
			}
		}

		// 是否檢查額度種類
		// 在額度明細表查詢不到時在到帳務系統內查詢
		if (l140m01as.isEmpty() && countE == 0 && count277 ==0) {
			int count = 0;
			// 海外查 DW_ASLNQUOT
			if (TypCdEnum.海外.getCode().equals(cntrNo.substring(3, 4))) {
				count = dwLnquotovService.findByCntrNoAndCustIdAndDupNo(cntrNo,
						custId, dupNo);
			} else {
				custId = Util.addSpaceWithValue(custId, 10);
				// X為 遠匯
				if ("X".equals(cntrNo.substring(7, 8))) {
					count = misdbBASEService.findLNF197BycntrNoAndCustId(
							cntrNo, custId, dupNo);
				} else {
					count = misdbBASEService.findMISLN20BycntrNoAndCustId(
							cntrNo, custId, dupNo);
				}
			}

			if (count == 0) {
				// L140M01a.message69=此舊額度序號：{0} 並未存在於帳務系統, 請確認後再輸入!
				String msg = MessageFormat.format(
						prop.getProperty("L140M01a.message69"), cntrNo);
				throw new CapMessageException(RespMsgHelper.getMessage(
						UtilConstants.AJAX_RSP_MSG.執行有誤, msg), getClass());
			}
		}

		// 非海外且不是遠匯才需要檢查額度種類
		if (!TypCdEnum.海外.getCode().equals(cntrNo.substring(3, 4))
				&& !"X".equals(cntrNo.substring(7, 8))) {
			if (Util.isEmpty(snoKind)) {
				Map<String, String> param = new HashMap<String, String>();
				// L140M01a.snoKind=額度控管種類
				param.put("colName", prop.getProperty("L140M01a.snoKind"));
				throw new CapMessageException(RespMsgHelper.getMessage(
						UtilConstants.AJAX_RSP_MSG.欄位不得為空, param), getClass());
			}
			String factType = misMISLN20Service.findFactType(custId, dupNo, cntrNo);
			if (!Util.isEmpty(factType)) {
				if ("51".equals(factType)) {
					factType = UtilConstants.Cntrdoc.snoKind.一般;
				} else if ("50".equals(factType)) {
					factType = UtilConstants.Cntrdoc.snoKind.信保;
				}
				// 2013-06-14,Rex, edit 修改當額度控管種類不存在 於020 才需檢查020
				if (Util.isNotEmpty(factType) && !snoKind.equals(factType)) {
					if (Util.equals(Util.trim(l140m01a.getIsEfin()), "Y")) {
						if (Util.equals(factType.subSequence(0, 1), "4") && Util.equals(snoKind.subSequence(0, 1), "6")) {
							// J-104-0284-001 額度明細表檢核供應鏈融資賣放限週轉科目
							// 供應鏈融資允許ALOAN 是 40/41 但 ELOAN是 60/61/62
							// OK
						} else {
							Map<String, String> codeMap = codeTypeService.findByCodeType("lms1405m01_snoKind");
							// L140M01a.message90=額度明細表之額度控管種類『{0}』與a-Loan『{1}』不同!!
							String msg = MessageFormat
									.format(prop.getProperty("L140M01a.message90"),
											codeMap.get(snoKind), codeMap.get(factType));
							throw new CapMessageException(msg, getClass());
						}
					} else {
						Map<String, String> codeMap = codeTypeService
								.findByCodeType("lms1405m01_snoKind");
						// L140M01a.message90=額度明細表之額度控管種類『{0}』與a-Loan『{1}』不同!!
						String msg = MessageFormat.format(
								prop.getProperty("L140M01a.message90"),
								codeMap.get(snoKind), codeMap.get(factType));
						throw new CapMessageException(msg, getClass());
					}

				} else {
					// lms1401Service.save(l140m01a);
				}

			} else {
				// lms1401Service.save(l140m01a);
			}

		}

		return result;// 傳回執行這個動作的AjAX
	}

	public String getCntrno(L140M01A l140m01a, String type, String numberType, String selectBrNo, String originalCntrNo) throws CapException {
		// 用來放額度序號
		Map<String, Object> cntrNo = null;
		String returnCntrNo = null;
		// 如果是海外的就是選擇起新號或者輸入舊號
		if (TypCdEnum.海外.getCode().equals(type)) {
			if ("1".equals(numberType)) {
				// 是否為遠匯 ，是 =X 、否=0
				String classCd = "0";
				if (LMSUtil.isNeedX(l140m01a)) {
					classCd = "X";
				}
				String ownBrName = branchService.getBranchName(selectBrNo);
				if (ownBrName == null) {
					// EFD0037=WARN|找不到該分行代號之分行名稱，請洽資訊室!!|
					throw new CapMessageException(RespMsgHelper.getMessage("EFD0037"), getClass());
				}
				cntrNo = lms1401Service.queryLnsp0050(
						selectBrNo.toUpperCase(), type, classCd);
				if (!"YES".equals(cntrNo.get("chkFlag"))) {
					HashMap<String, String> msg = new HashMap<String, String>();
					msg.put("msg", Util.trim(cntrNo.get("errMsg")));
					// 錯誤訊息
					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, msg),
							getClass());
				}
				returnCntrNo = (String) cntrNo.get("cntrNo");
			} else {
				returnCntrNo = originalCntrNo;
			}
		}
		return returnCntrNo;
	}
	
	/**
	 * G-113-0036 各筆額度主辦行才能新增聯行額度攤貸資訊
	 * @param l161s01b_slbank
	 * @param l161s01b_slbranch
	 * @param l161s01a_cntrno
	 * @param l140m01a_cntrno
	 * @return checkflag  true=可、flase=不可新刪
	 * @throws CapException
	 */
	public boolean checkCanModL140M01E_AF(String L161S01A_cntrno, String l140m01a_cntrno) throws CapException {
		boolean checkflag = true;
		//非額度管理行是否能新增/刪除本行攤貸資訊
		String controlflag = Util.trim(lmsService.getSysParamDataValue("G-113-0036-controlFlag"));
		if(Util.equals(UtilConstants.DEFAULT.否, controlflag)){
			if(Util.notEquals(L161S01A_cntrno.substring(0, 3), l140m01a_cntrno.substring(0, 3))){
				//(額度序號前三碼與簽案額度序號相比)
				checkflag = false;
			}
		}
		return checkflag;
	}
	
	public boolean checkCanDelL140M01E_AF(String[] oids, String l140m01a_mainid, String L161S01A_cntrno) throws CapException {
		boolean checkflag = true;
		//非額度管理行是否能新增/刪除本行攤貸資訊
		//G-113-0036 各筆額度主辦行才能新增/刪除聯行額度攤貸資訊(額度序號前三碼與簽案額度序號相比)
		L140M01A l140m01a = lms1401Service.findL140m01aByMainId(l140m01a_mainid);
		checkflag = this.checkCanModL140M01E_AF(L161S01A_cntrno, l140m01a.getCntrNo());

		return checkflag;
	}
	
	/**
	 * 查詢 攤貸比例 並告訴使用者目前剩餘可攤貸金額，和刪除以攤貸分行
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL140m01e_af(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		CapAjaxFormResult brankList = null;
		String mainId = params.getString("cntrMainId");
		List<L140M01E_AF> modelEs = (List<L140M01E_AF>) lms1601Service
				.findListByMainId(L140M01E_AF.class, mainId);
		//簽案額度明細
		L140M01A l140m01a = lms1401Service.findL140m01aByMainId(mainId);
		BigDecimal ShareRate1Count = BigDecimal.ZERO;
		BigDecimal ShareAmtCount = BigDecimal.ZERO;
		Map<String, String> m = new TreeMap<String, String>();

		List<IBranch> bank = branchService.getAllBranch();
		for (IBranch b : bank) {
			String brName = Util.trim(b.getBrName());
			String brCode = b.getBrNo();
			m.put(brCode, brName);
		}

		String oid = params.getString(EloanConstants.OID);
		L140M01E_AF l140m01e_af = null;
		if (!Util.isEmpty(oid)) {
			l140m01e_af = lms1601Service.findModelByOid(L140M01E_AF.class, oid);
			CapAjaxFormResult formData = DataParse.toResult(l140m01e_af,
					DataParse.Delete, new String[] { EloanConstants.MAIN_ID,
							EloanConstants.OID });
			result.set("formData", formData);

		}
		if (!modelEs.isEmpty()) {// 看目前筆數是否為空
			BigDecimal ShareRate2 = modelEs.get(0).getShareRate2();
			BigDecimal TotalAmt = modelEs.get(0).getTotalAmt();
			result.set("role", String.valueOf(ShareRate2));// 取得目前分母
			String shareFlag = "";
			for (L140M01E_AF f : modelEs) {
				if (UtilConstants.Cntrdoc.shareType.以比例計算.equals(f
						.getShareFlag())) {
					ShareRate1Count = ShareRate1Count.add(f.getShareRate1());
				}

				ShareAmtCount = ShareAmtCount.add(f.getShareAmt());
				if (m.containsKey(f.getShareBrId())) {
					if (!Util.isEmpty(oid)
							&& f.getShareBrId().equals(f.getShareBrId())) {
						continue;
					}
					m.remove(f.getShareBrId());
				}
				shareFlag = f.getShareFlag();
			}
			if (UtilConstants.Cntrdoc.shareType.以比例計算.equals(shareFlag)) {
				ShareRate2 = ShareRate2.subtract(ShareRate1Count);
			}

			TotalAmt = TotalAmt.subtract(ShareAmtCount);
			result.set("tips", String.valueOf(ShareRate2));// 增加目前分子剩下提示
			result.set("tips2", String.valueOf(TotalAmt));// 增加目前可攤貸餘額提示
			result.set("shareFlag", shareFlag);
		} else {
			result.set("role", "0");
		}
		brankList = new CapAjaxFormResult(m);
		result.set("item", brankList);
		result.set("totalAmt",
				NumConverter.addComma(l140m01a.getCurrentApplyAmt()));
		return result;

	}
	
	/**
	 * 儲存額度明細表攤貸比率
	 * 
	 * @param params
	 *            <pre>
	 * {
	 *  L140M01E_AFForm :form的資料,
	 *  cntrMainId:額度明細表mainId
	 *  numberType: 給號的種類 1.給新號 2.舊號
	 *  type:額度序號的種類 1.DBU,4.OBU,5.海外
	 *  classCD:是否為遠匯
	 *  selectBrNo:分行號碼
	 *  originalCntrNo:原始額度序號
	 *  }
	 * </pre>
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL140m01e_af(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String mainId = params.getString("cntrMainId", "");
		String cntrno = params.getString("cntrNo", "");
		
		String formL140m01e_af = params.getString("L140M01E_AFForm"); // 指定的form
		JSONObject jsonL140m01e_af = JSONObject.fromObject(formL140m01e_af);
		String shareBrId = jsonL140m01e_af.getString("shareBrId");
		L140M01E_AF l140m01e_af = lms1401Service.findL140m01e_afByUniqueKey(mainId,
				shareBrId);
		L140M01A l140m01a = lms1401Service.findL140m01aByMainId(mainId);
		// 計算分母
		BigDecimal shareRate1Count = BigDecimal.ZERO;

		List<L140M01E_AF> l140m01e_afList = (List<L140M01E_AF>) lms1601Service
				.findListByMainId(L140M01E_AF.class, mainId);
		// 用來放額度序號
		Map<String, Object> cntrNo = null;
		for (L140M01E_AF modele : l140m01e_afList) {
			// 如果已存在這間分行不將金額納入計算，以免會重複計算到
			if (shareBrId.equals(modele.getShareBrId())) {
				continue;
			}
			if (UtilConstants.Cntrdoc.shareType.以比例計算.equals(modele
					.getShareFlag())) {
				shareRate1Count = shareRate1Count.add(modele.getShareRate1());
			}
		}
		if (l140m01e_af == null) {
			// 判斷額度序號給號
			String type = params.getString("type");
			l140m01e_af = new L140M01E_AF();
			l140m01e_af.setMainId(mainId);
			DataParse.toBean(jsonL140m01e_af, l140m01e_af);
			String numberType = params.getString("numberType");
			String originalCntrNo = params.getString("originalCntrNo");
			// 2012-04-10_當該分行已有自己的額度序號，就不需重新起號
			String cntrNoTemp = Util.trim(l140m01a.getCntrNo());
			// 如果是海外的就是選擇起新號或者輸入舊號
			if (TypCdEnum.海外.getCode().equals(type)) {
				if ("1".equals(numberType)) {
					String selectBrNo = params.getString("selectBrNo");
					// 是否為遠匯 ，是 =X 、否=0
					String classCd = "0";
					if (LMSUtil.isNeedX(l140m01a)) {
						classCd = "X";
					}
					String ownBrName = branchService.getBranchName(selectBrNo);
					if (ownBrName == null) {
						// EFD0037=WARN|找不到該分行代號之分行名稱，請洽資訊室!!|
						throw new CapMessageException(RespMsgHelper.getMessage("EFD0037"), getClass());
					}
					cntrNo = lms1401Service.queryLnsp0050(
							selectBrNo.toUpperCase(), type, classCd);
					if (!"YES".equals(cntrNo.get("chkFlag"))) {
						HashMap<String, String> msg = new HashMap<String, String>();
						msg.put("msg", Util.trim(cntrNo.get("errMsg")));
						// 錯誤訊息
						throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, msg),
								getClass());
					}
					cntrNoTemp = (String) cntrNo.get("cntrNo");
				} else {
					cntrNoTemp = originalCntrNo;
				}
			}
			l140m01e_af.setShareNo(cntrNoTemp);
			// 再存的時候把是國內國外的flag 加進去
			l140m01e_af.setFlag(branchService.getBranch(shareBrId).getBrNoFlag());
			l140m01e_af.setCreateTime(CapDate.getCurrentTimestamp());
			l140m01e_af.setCreator(user.getUserId());
			Long shareAmt = Util.parseLong(NumConverter
					.delCommaString(jsonL140m01e_af.getString("shareAmt")));
			l140m01e_af.setShareAmt(new BigDecimal(shareAmt));
		} else {
			DataParse.toBean(jsonL140m01e_af, l140m01e_af);
		}
		if (!this.checkCanModL140M01E_AF(cntrno, l140m01a.getCntrNo())) {
			// G-113-0036 各筆額度主辦行才能新增聯行額度攤貸資訊
			// 非主辦行是否能新刪本行攤貸資訊
			Properties prop = MessageBundleScriptCreator
					.getComponentResource(LMS1601M01Page.class);
			String msg = prop.getProperty("L140M01e.message07");
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤, msg), getClass());
		}
		
		// 當如果是以比例計算就判斷比例
		if (UtilConstants.Cntrdoc.shareType.以比例計算.equals(l140m01e_af.getShareFlag())) {
			// 檢核分子總和是否大於分母
			if (shareRate1Count.add(l140m01e_af.getShareRate1()).compareTo(
					l140m01e_af.getShareRate2()) == 1) {
				Properties prop = MessageBundleScriptCreator
						.getComponentResource(LMS1601M01Page.class);
				// L140M01e.lmterror 分子總和大於分母無法儲存
				String msg = prop.getProperty("L140M01e.lmterror");
				throw new CapMessageException(RespMsgHelper.getMessage(
						UtilConstants.AJAX_RSP_MSG.執行有誤, msg), getClass());
			}
		} else {
			l140m01e_af.setShareRate1(null);
			l140m01e_af.setShareRate2(null);
		}
		// 專前端的現請額度
		BigDecimal totalAmt = new BigDecimal(NumConverter.delComma(jsonL140m01e_af.getString("totalAmt")));
		l140m01e_af.setTotalAmt(totalAmt);
		lms1401Service.save(l140m01e_af);
		CapAjaxFormResult result = new CapAjaxFormResult();
		return result;// 傳回執行這個動作的AjAX
	}
	
	/**
	 * 刪除(多筆)額度明細表攤貸比率
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteL140m01e_af(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] Idlist = params.getStringArray("Idlist");
		String mainId = params.getString("cntrMainId", "");
		String cntrno = params.getString("cntrNo", "");
		if (Idlist.length > 0) {
			boolean checkflag = this.checkCanDelL140M01E_AF(Idlist, mainId, cntrno);
			if(checkflag){//可刪
				lms1401Service.deleteL140m01e_afList(Idlist);
			} else {
				// G-113-0036 各筆額度主辦行才能新增聯行額度攤貸資訊
				// 非主辦行是否能新刪本行攤貸資訊
				Properties prop = MessageBundleScriptCreator
						.getComponentResource(LMS1601M01Page.class);
				String msg = prop.getProperty("L140M01e.message07");
				throw new CapMessageException(RespMsgHelper.getMessage(
						UtilConstants.AJAX_RSP_MSG.執行有誤, msg), getClass());
			}
		}
		

		return result;
	}
	
	/**
	 * 查詢變更分母
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult queryChangesShareRate(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString("cntrMainId", "");
		HashMap<String, String> msg = new HashMap<String, String>();
		List<L140M01E_AF> L140M01E_AFs = (List<L140M01E_AF>) lms1401Service
				.findModelListByMainId(L140M01E_AF.class, mainId);
		if (L140M01E_AFs.isEmpty()) {
			// L140M01e.message01=請先登錄聯行攤貸比例
			Properties prop = MessageBundleScriptCreator.getComponentResource(LMS1601M01Page.class);
			msg.put("msg", Util.trim((String) prop.get("L140M01e.message01")));
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤, msg), getClass());
		}
		if (UtilConstants.Cntrdoc.shareType.以金額計算.equals(L140M01E_AFs.get(0)
				.getShareFlag())) {
			// L140M01e.message02=以比例計算時才可使用此功能
			Properties prop = MessageBundleScriptCreator.getComponentResource(LMS1601M01Page.class);
			msg.put("msg", Util.trim((String) prop.get("L140M01e.message02")));
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤, msg), getClass());
		}
		return result;
	}
	
	/**
	 * 儲存變更分母
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveChangesShareRate(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString("cntrMainId", "");
		String cntrno = params.getString("cntrNo", "");
		String shareRate = NumConverter.delCommaString(params.getString("shareRate", ""));
		List<L140M01E_AF> l140m01e_afs = (List<L140M01E_AF>) lms1401Service
				.findModelListByMainId(L140M01E_AF.class, mainId);
		//取得額度明細
		L140M01A l140m01a = lms1401Service.findL140m01aByMainId(mainId);
		if (!this.checkCanModL140M01E_AF(cntrno, l140m01a.getCntrNo())) {
			// G-113-0036 各筆額度主辦行才能新增聯行額度攤貸資訊
			// 非主辦行是否能新刪本行攤貸資訊
			Properties prop = MessageBundleScriptCreator
					.getComponentResource(LMS1601M01Page.class);
			String msg = prop.getProperty("L140M01e.message07");
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤, msg), getClass());
		}
		
		// 原始分母
		BigDecimal orgRate = l140m01e_afs.get(0).getShareRate2();
		// 新分母
		BigDecimal newRate = new BigDecimal(shareRate);
		// 當新分母與舊分母不同 才做更新
		if (orgRate.compareTo(newRate) != 0) {
			for (L140M01E_AF l140m01e_af : l140m01e_afs) {
				l140m01e_af.setShareRate2(newRate);
				// 重算攤貸額度金額 攤貸總額 * 分子 /　分母
				BigDecimal shareAmt = Arithmetic.div_floor(l140m01e_af
						.getTotalAmt().multiply(l140m01e_af.getShareRate1()),
						l140m01e_af.getShareRate2(), 0);
				l140m01e_af.setShareAmt(shareAmt);
			}
			lms1401Service.savelistL140M01E_AF(l140m01e_afs);
		}
		return result;
	}
	
	/**
	 * 儲存動審攤貸剩餘餘額
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL140m01e_afAmt(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);
		String amt = NumConverter.delCommaString(params.getString("amt"));
		L140M01E_AF l140m01e_af = lms1401Service.findModelByOid(L140M01E_AF.class, oid);

		if (l140m01e_af != null) {
			l140m01e_af.setShareAmt(Util.parseBigDecimal(l140m01e_af.getShareAmt()).add(new BigDecimal(amt)));

			lms1401Service.save(l140m01e_af);
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		}

		return result;// 傳回執行這個動作的AjAX
	}
	
	/**
	 * 檢查攤貸比率是否攤貸完畢並將尚未攤貸餘額加到該額度明細表上的分行， 若沒有則跳出視窗選擇加在哪一筆上
	 * 
	 * @param l140m01a
	 *            額度明細表主檔
	 * @return BigDecimal 回傳剩餘金額
	 */
	public BigDecimal checkL140m01e_af(L140M01A l140m01a) {
		Set<L140M01E_AF> l140m01e_afs = l140m01a.getL140m01e_af();
		BigDecimal nowCurrAMT = l140m01a.getCurrentApplyAmt();
		if (nowCurrAMT == null) {
			nowCurrAMT = BigDecimal.ZERO;
		}
		// 是否有餘額剩下
		BigDecimal tempAmt = BigDecimal.ZERO;
		if (l140m01e_afs != null && !l140m01e_afs.isEmpty()) {
			// 加總分子
			BigDecimal shareRate1Count = BigDecimal.ZERO;
			// 分母
			BigDecimal shareRate2temp = BigDecimal.ZERO;
			// 計算總金額
			BigDecimal amtCount = BigDecimal.ZERO;

			for (L140M01E_AF l140m01e_af : l140m01e_afs) {
				if (UtilConstants.Cntrdoc.shareType.以比例計算.equals(l140m01e_af.getShareFlag())) {
					shareRate1Count = shareRate1Count.add(l140m01e_af.getShareRate1());
					shareRate2temp = l140m01e_af.getShareRate2();
				}
				amtCount = amtCount.add(Util.parseBigDecimal(l140m01e_af.getShareAmt()));
			}

			// 檢核分子是否攤貸完
			if (shareRate1Count.compareTo(shareRate2temp) == 0) {
				// 當加總額小於現請額度
				if (amtCount.compareTo(nowCurrAMT) == -1) {
					tempAmt = nowCurrAMT.subtract(amtCount);
				}
			}
		}
		return tempAmt;
	}
	
	/**
	 * 判斷動審l161s01a 現請額度
	 * @param l140m01a 額度明細
	 * @param l161s01a
	 * @param ShareAmt
	 * @return
	 */
	public BigDecimal checkL161s01aCurrentApplyAmt(L140M01A l140m01a, L161S01A l161s01a, BigDecimal ShareAmt) {
		BigDecimal currentApplyAmt = l140m01a.getCurrentApplyAmt();
		// 非主辦行, 放額度明細表現請額度
		if (Util.notEquals(l161s01a.getCntrNo().substring(0, 3), l140m01a.getCntrNo().substring(0, 3))) {
			currentApplyAmt = ShareAmt;
		}
		return currentApplyAmt;
	}
}
