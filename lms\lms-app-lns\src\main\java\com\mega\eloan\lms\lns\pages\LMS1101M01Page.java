/* * LMS1105M01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lns.pages;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import tw.com.jcs.auth.AuthType;
import tw.com.jcs.auth.CodeItemService;
import tw.com.jcs.common.Util;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.eloan.lms.base.panels.LMS7301M01Panel;
import com.mega.eloan.lms.base.panels.LMSM01Panel;
import com.mega.eloan.lms.base.panels.LMSM03Panel;
import com.mega.eloan.lms.base.panels.LMSM04Panel;
import com.mega.eloan.lms.base.panels.LMSM06Panel;
import com.mega.eloan.lms.base.panels.LMSS08COMPanel;
import com.mega.eloan.lms.base.panels.LMSS20APanel;
import com.mega.eloan.lms.base.panels.LMSS21APanel;
import com.mega.eloan.lms.base.panels.LMSS23APanel;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.lms.panels.LMS1405S02Panel;
import com.mega.eloan.lms.lms.panels.LMSS10Panel;
import com.mega.eloan.lms.lns.panels.LMS1201S05Panel;
import com.mega.eloan.lms.lns.panels.LMS1201S11Panel;
import com.mega.eloan.lms.lns.panels.LMS1201S13Panel;
import com.mega.eloan.lms.lns.panels.LMS1201S15Panel;
import com.mega.eloan.lms.lns.panels.LMS1201S18Panel;
import com.mega.eloan.lms.lns.panels.LMS1201S28Panel;
import com.mega.eloan.lms.lns.panels.LMS1301S05Panel;
import com.mega.eloan.lms.lns.panels.LMS1401S02Panel;
import com.mega.eloan.lms.lns.panels.LMS1401S02Panel04;
import com.mega.eloan.lms.lns.panels.LMS1401S03Panel;
import com.mega.eloan.lms.lns.panels.LMS1401S04Panel;
import com.mega.eloan.lms.lns.panels.LMS1401S05Panel;
import com.mega.eloan.lms.lns.panels.LMS1401S06Panel;
import com.mega.eloan.lms.lns.panels.LMS1401S07Panel;
import com.mega.eloan.lms.lns.panels.LMS1401S08Panel;
import com.mega.eloan.lms.lns.panels.LMS1401S09Panel;
import com.mega.eloan.lms.lns.panels.LMS1401S10Panel;
import com.mega.eloan.lms.lns.panels.LMS1401S11Panel;
import com.mega.eloan.lms.lns.panels.LMSS01APanel;
import com.mega.eloan.lms.lns.panels.LMSS02BPanel;
import com.mega.eloan.lms.lns.panels.LMSS03APanel;
import com.mega.eloan.lms.lns.panels.LMSS04APanel;
import com.mega.eloan.lms.lns.panels.LMSS05APanel;
import com.mega.eloan.lms.lns.panels.LMSS07APanel;
import com.mega.eloan.lms.lns.panels.LMSS08APanel;
import com.mega.eloan.lms.lns.panels.LMSS09APanel;
import com.mega.eloan.lms.lns.panels.LMSS10APanel;
import com.mega.eloan.lms.lns.service.LMS1201Service;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * <pre>
 * 簽報書明細頁籤與按鈕控制(企金授權內)
 * </pre>
 * 
 * @since 2011/5/3
 * <AUTHOR> Lin
 * @version <ul>
 *          <li>2011/5/3,Miller Lin,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms1101m01/{page}")
public class LMS1101M01Page extends AbstractEloanForm {

	@Autowired
	CodeItemService cis;
	@Autowired
	LMS1201Service service1201;
	@Autowired
	LMSService lmsService;

	final String TAB_CTX = "_tabCtx";
	final String TAB_SIGN = "tab-";

	@Override
	public void execute(ModelMap model, PageParameters params) {
		// 依權限設定button
		// 案件考核表分頁
		new LMS7301M01Panel("lmss7305_panel").processPanelData(model, params);
		// 常用主管名單共用元件
		new LMSM03Panel("lmsm03_panel").processPanelData(model, params);
		new LMSM04Panel("lmsm04_panel").processPanelData(model, params);
		// 可排除利害關係人授信限制原因共用元件_獨立的thickbox
		new LMSM06Panel("lmsm06_panel_for_thickbox").processPanelData(model, params);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 如果存在海外聯貸案文件狀態則新增此狀態的按鈕
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L120M01A l120M01A = service1201.findL120m01aByOid(mainOid);
		String docStatus = l120M01A.getDocStatus();
		if (LMSUtil.isSpecialBranch(Util.trim(l120M01A.getCaseBrId()))) {
			// 控制呈授管處/營運中心時隱藏預設按鈕
			if (docStatus.indexOf("C") != -1 || docStatus.indexOf("H") != -1) {
				if (!user.getUnitNo().equals(UtilConstants.BankNo.授管處)
						&& !user.getUnitNo()
								.equals(UtilConstants.BankNo.中區營運中心)
						&& !user.getUnitNo().equals(
								UtilConstants.BankNo.中部區域授信中心)
						&& !user.getUnitNo().equals(
								UtilConstants.BankNo.北一區營運中心)
						&& !user.getUnitNo().equals(
								UtilConstants.BankNo.北二區營運中心)
						&& !user.getUnitNo()
								.equals(UtilConstants.BankNo.南區營運中心)
						&& !user.getUnitNo().equals(
								UtilConstants.BankNo.南部區域授信中心)
						&& !user.getUnitNo().equals(
								UtilConstants.BankNo.桃竹苗區營運中心)) {
					hdefaultBtn(model, params);
				} else {
					defaultBtn(model, params);
				}
			} else {
				defaultBtn(model, params);
			}
		} else if (user.getUnitNo().equals(Util.trim(l120M01A.getOwnBrId()))) {
			defaultBtn(model, params);
		} else {
			if ("A".equals(user.getUnitType())
					&& "3".equals(Util.trim(l120M01A.getAuthLvl()))) {
				defaultBtn(model, params);
			} else {
				hdefaultBtn(model, params);
			}
		}
		addAclLabel(model, new AclLabel("_btnDOC_SEA_SIGNING", AuthType.Modify, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_SEA_SIGNING2", AuthType.Accept, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_SEA_SIGNING3", AuthType.Query, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_SEA_CWAITGO", AuthType.Modify, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_SEA_CWAITGO2", AuthType.Accept, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_SEA_CWAITGO3", AuthType.Query, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_SEA_ALREADYSIGN", AuthType.Modify, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_SEA_ALREADYSIGN2", AuthType.Accept, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_SEA_ALREADYSIGN3", AuthType.Query, params, false));

		boolean show = (CreditDocStatusEnum.海外_已核准.getCode().equals(
				l120M01A.getDocStatus()) && lmsService.checkNeedContract(l120M01A
				.getMainId()));

		// J-109-0371_05097_B1002 簡化青年創業及啟動金貸款簽報書簽案流程
		boolean show_lnType61 = (CreditDocStatusEnum.海外_已核准.getCode().equals(
				l120M01A.getDocStatus()) && lmsService
				.checkNeedContract_lnType61(l120M01A));

		// J-109-0371_05097_B1002 簡化青年創業及啟動金貸款簽報書簽案流程
		addAclLabel(model, new AclLabel("_btn_SHOWCONTRACT", AuthType.Query, params,	(show || show_lnType61)));

		renderJsI18N(LMS1101M01Page.class);
		renderJsI18N(LMS1201M01Page.class, new String[] { "l120m01a.error24" });
		renderJsI18N(LMSM01Panel.class);
		renderJsI18N(LMSCommomPage.class);

		renderRespMsgJsI18N(UtilConstants.AJAX_RSP_MSG.ERR_MSG); // 多render一個msgi18n
		// renderJsI18N(LMS1205V01Page.class);
		// System.out.println("123");
		// tabs
		int page = Util.parseInt(params.getString("page"));
		String tabID = TAB_SIGN + Util.addZeroWithValue(page, 2); // 指定ID
		Panel panel = getPanel(page);
		panel.processPanelData(model, params);
		model.addAttribute("tabIdx", tabID);
	}// ;

	private void defaultBtn(ModelMap model, PageParameters params) {
		// 海外
		// add(new FlowButton("_FlowButton", "LMS1205M01Flow", params,
		// getDomainClass(), CreditDocStatusEnum.class, false));
		addAclLabel_23(model, "_btnDOC_EDITING", params, CreditDocStatusEnum.海外_編製中);
		
		addAclLabel_23(model, "_btnWAIT_APPROVE", params, CreditDocStatusEnum.海外_待覆核, CreditDocStatusEnum.國內簡易行待覆核);
		
		addAclLabel_23(model, "_btnWORK_LOOKING", params, CreditDocStatusEnum.海外_呈授管處, CreditDocStatusEnum.海外_呈營運中心, CreditDocStatusEnum.海外_呈總行);
		
		addAclLabel_23(model, "_btnSEND_WAITLOGIN", params, CreditDocStatusEnum.海外_提會待登錄, CreditDocStatusEnum.海外_總行提會待登錄);
		
		addAclLabel_23(model, "_btnSEND_WAITAPPROVE", params, CreditDocStatusEnum.海外_提會待覆核, CreditDocStatusEnum.海外_總行提會待覆核);
		
		addAclLabel_23(model, "_btnWAIT_GETORBACK", params, CreditDocStatusEnum.海外_待補件, CreditDocStatusEnum.海外_待撤件);
		
		addAclLabel_23(model, "_btnALREADY_OK", params, CreditDocStatusEnum.海外_已核准);
		
		addAclLabel_23(model, "_btnALREADY_REJECT", params, CreditDocStatusEnum.海外_婉卻);
		
		addAclLabel_23(model, "_btnSAY_CASE", params, CreditDocStatusEnum.海外_陳復案_陳述案);
		
		// 授管處
		addAclLabel_23(model, "_btnDOC_HEDITING", params, CreditDocStatusEnum.授管處_審查中);
		
		addAclLabel_23(model, "_btnDOC_HALREADY", params, CreditDocStatusEnum.授管處_已會簽);
		
		addAclLabel_ab(model, "_btnDOC_HSEND1", params, CreditDocStatusEnum.授管處_提授審會);
		
		addAclLabel_ab(model, "_btnDOC_HSEND2", params, CreditDocStatusEnum.授管處_提催收會);
		
		addAclLabel_ab(model, "_btnDOC_HSEND3", params, CreditDocStatusEnum.授管處_提常董會);

		addAclLabel_23(model, "_btnDOC_HWAITCHECK", params, CreditDocStatusEnum.授管處_待放行, CreditDocStatusEnum.授管處_待核定);

		addAclLabel_23(model, "_btnDOC_HWAITCHANGE", params, CreditDocStatusEnum.授管處_待更正, CreditDocStatusEnum.授管處_待分行撤件);

		addAclLabel_23(model, "_btnDOC_HALREADYOK", params, CreditDocStatusEnum.授管處_已核准);
		
		addAclLabel_23(model, "_btnDOC_HALREADYREJECT", params, CreditDocStatusEnum.授管處_已婉卻, CreditDocStatusEnum.授管處_待陳復);

		// add(new AclLabel("_btnDOC_HNOLOOK", params, getDomainClass(),
		// AuthType.Modify, CreditDocStatusEnum.授管處_免批覆案件));

		// 營運中心
		addAclLabel_23(model, "_btnDOC_CEDITING", params, CreditDocStatusEnum.營運中心_審查中);
		
		addAclLabel_23(model, "_btnDOC_CWAITCHECK", params, CreditDocStatusEnum.營運中心_待放行, CreditDocStatusEnum.營運中心_待核定);
		
		addAclLabel_23(model, "_btnDOC_CTOADMIN", params, CreditDocStatusEnum.營運中心_呈總處);
		
		addAclLabel_23(model, "_btnDOC_CWAITCHANGE", params, CreditDocStatusEnum.營運中心_待更正, CreditDocStatusEnum.營運中心_待分行撤件);
		
		addAclLabel_23(model, "_btnDOC_CALREADYOK", params, CreditDocStatusEnum.營運中心_已核准);
		
		addAclLabel_23(model, "_btnDOC_CALREADYREJECT", params, CreditDocStatusEnum.營運中心_已婉卻, CreditDocStatusEnum.營運中心_待陳復);
		
		addAclLabel_23(model, "_btnDOC_CALLSEND", params, CreditDocStatusEnum.營運中心_所有提會案件);
		
		addAclLabel_23(model, "_btnDOC_CSAY_CASE", params, CreditDocStatusEnum.營運中心_陳復案_陳述案);
	}

	private void hdefaultBtn(ModelMap model, PageParameters params) {
		// 海外
		// add(new AclLabel("_btnDOC_SEA_ALREADYSIGN3",AuthType.Query, params,
		// false));
		addAclLabel_23boolean(model, "_btnDOC_EDITING", params);
		
		addAclLabel_23boolean(model, "_btnWAIT_APPROVE", params);
		
		addAclLabel_23boolean(model, "_btnWORK_LOOKING", params);
		
		addAclLabel_23boolean(model, "_btnSEND_WAITLOGIN", params);
		
		addAclLabel_23boolean(model, "_btnSEND_WAITAPPROVE", params);
		
		addAclLabel_23boolean(model, "_btnWAIT_GETORBACK", params);
		
		addAclLabel_23boolean(model, "_btnALREADY_OK", params);
		
		addAclLabel_23boolean(model, "_btnALREADY_REJECT", params);
		
		addAclLabel_23boolean(model, "_btnSAY_CASE", params);
		
		addAclLabel_23boolean(model, "_btnDOC_HEDITING", params);

		// 授管處
		addAclLabel_23boolean(model, "_btnDOC_HEDITING", params);
		
		addAclLabel_23boolean(model, "_btnDOC_HALREADY", params);

		addAclLabel_abboolean(model, "_btnDOC_HSEND1", params);
		
		addAclLabel_abboolean(model, "_btnDOC_HSEND2", params);
		
		addAclLabel_abboolean(model, "_btnDOC_HSEND3", params);
		
		addAclLabel_23boolean(model, "_btnDOC_HWAITCHECK", params);
		
		addAclLabel_23boolean(model, "_btnDOC_HWAITCHANGE", params);
		
		addAclLabel_23boolean(model, "_btnDOC_HALREADYOK", params);
		
		addAclLabel_23boolean(model, "_btnDOC_HALREADYREJECT", params);
		// add(new AclLabel("_btnDOC_HNOLOOK", params, getDomainClass(),
		// AuthType.Modify, CreditDocStatusEnum.授管處_免批覆案件));

		// 營運中心

		addAclLabel_23boolean(model, "_btnDOC_CEDITING", params);
		
		addAclLabel_23boolean(model, "btnDOC_CWAITCHECK", params);
		
		addAclLabel_23boolean(model, "_btnDOC_CTOADMIN", params);
		
		addAclLabel_23boolean(model, "_btnDOC_CWAITCHANGE", params);
		
		addAclLabel_23boolean(model, "_btnDOC_CALREADYOK", params);
		
		addAclLabel_23boolean(model, "_btnDOC_CALREADYREJECT", params);
		
		addAclLabel_23boolean(model, "_btnDOC_CALLSEND", params);
		
		addAclLabel_23boolean(model, "_btnDOC_CSAY_CASE", params);
	}

	// 頁籤
	@SuppressWarnings("unused")
	public Panel getPanel(int index) {
		Panel panel = null;
		Map<String, String> msgs = null;
		switch (index) {
		case 1:
			// panel = new LMS120S01Panel(TAB_CTX);
			panel = new LMSS01APanel(TAB_CTX, true);
			break;
		case 2:
			renderJsI18N(LMSS02BPanel.class);
			panel = new LMSS02BPanel(TAB_CTX, true);
			break;
		case 3:
			msgs = lmsService.getAllDervPeriod();
			renderJsI18NWithMsgName("dervPeriodCodeType", msgs);
			renderJsI18N(LMS1401S02Panel.class);
			renderJsI18N(LMS1401S02Panel04.class);
			// J-105-0264-001 WEB-ELOAN授信案件簽報書增訂「新臺幣、美元利率定價合理性分析表」。
			renderJsI18N(LMS1401S03Panel.class);
			// J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊
			renderJsI18N(LMS1401S04Panel.class);
			renderJsI18N(LMS1401S05Panel.class);
			// J-108-0288_05097_B1001 Web e-Loan授信系統新增合併關係企業額度彙總表
			renderJsI18N(LMS1401S06Panel.class);
			// J-109-0370_05097_B1002 配合企金一般簽報書格式修訂，新增、調整簽報作業相關內容
			renderJsI18N(LMS1401S07Panel.class);
			// J-110-0382 Web e-Loan國內與海外企金授信新增「BIS信用風險標準法/內評法」相關欄位
			renderJsI18N(LMS1401S08Panel.class);
			// J-111-0397 RWA
			renderJsI18N(LMS1401S09Panel.class);
			// J-111-XXXXXXXXXX
			renderJsI18N(LMS1401S10Panel.class);
			// BIS 沒用到該頁籤還是要加i18n，因為會load js，讀不到i18n會讓部分js失效
			renderJsI18N(LMS1401S11Panel.class);
			panel = new LMSS03APanel(TAB_CTX, true);
			break;
		case 4:
			renderJsI18N(LMSS04APanel.class);
			panel = new LMSS04APanel(TAB_CTX, true);
			break;
		case 5:
			renderJsI18N(LMS1201S05Panel.class);
			panel = new LMSS05APanel(TAB_CTX, true);
			break;
		case 7:
			// renderJsI18N(AbstractEloanPage.class, new String[]{"EFD0002"});
			renderJsI18N(LMS1301S05Panel.class);
			renderJsI18N(LMSS07APanel.class);
			renderJsI18N(LMS1405S02Panel.class);
			panel = new LMSS07APanel(TAB_CTX, true);
			break;
		case 8:
			renderJsI18N(LMS1201V01Page.class);
			renderJsI18N(LMSS08APanel.class);
			renderJsI18N(LMSS08COMPanel.class);
			renderJsI18N(AbstractEloanPage.class,
					new String[] { "docStatus.230" });
			renderRespMsgJsI18N(new String[] { "EFD2058", "EFD2059", "EFD2060" }); // 多render一個msgi18n
			panel = new LMSS08APanel(TAB_CTX, true);
			break;
		case 9:
			renderJsI18N(LMSS09APanel.class);
			panel = new LMSS09APanel(TAB_CTX, true);
			break;
		case 10:
			renderJsI18N(LMSS10APanel.class);
			panel = new LMSS10APanel(TAB_CTX, true);
			break;
		case 11:
			msgs = lmsService.getAllDervPeriod();
			renderJsI18NWithMsgName("dervPeriodCodeType", msgs);
			renderJsI18N(LMS1401S02Panel.class);
			renderJsI18N(LMS1401S02Panel04.class);
			panel = new LMS1201S11Panel(TAB_CTX, true);
			break;
		case 13:
			// J-111-0488_05097_B1001 Web e-Loan企金授信簽報系統增加強分行經理權限對於合併關係企業彙總額度之管控
			// renderJsI18N(LMSS10APanel.class);
			renderJsI18N(LMSS10Panel.class);
			panel = new LMS1201S13Panel(TAB_CTX, true);
			break;
		// case 12:
		// panel = new LMS1205S12Panel(TAB_CTX);
		// break;
		// case 13:
		// panel = new LMS1205S13Panel(TAB_CTX);
		// break;
		// case 14:
		// panel = new LMS1205S14Panel(TAB_CTX);
		// break;
		case 15:
			renderJsI18N(LMSS10Panel.class);
			panel = new LMS1201S15Panel(TAB_CTX, true);
			break;
		// case 16:
		// renderJsI18N(LMS1405S02Panel.class);
		// panel = new LMS1205S16Panel(TAB_CTX);
		// break;
		// case 17:
		// panel = new LMS1205S17Panel(TAB_CTX);
		// break;
		case 18:
			panel = new LMS1201S18Panel(TAB_CTX, true);
			break;
		case 20:
			// J-106-0029-001 新增洗錢防制頁籤
			renderJsI18N(LMSS20APanel.class);
			panel = new LMSS20APanel(TAB_CTX, true);
			break;
		case 21:
			// J-106-0085-001 Web e-Loan企金授信新增主要還款來源國等相關欄位
			renderJsI18N(LMSS21APanel.class);
			panel = new LMSS21APanel(TAB_CTX, true);
			break;
		case 23:
			// J-108-0243 微型企業
			renderJsI18N(LMSS23APanel.class);
			panel = new LMSS23APanel(TAB_CTX, true);
			break;
		case 28:
			// RPA資料查詢
			renderJsI18N(LMS1601M01Page.class);
			panel = new LMS1201S28Panel(TAB_CTX, true);
			break;
		default:
			panel = new LMSS01APanel(TAB_CTX, true);
			break;
		}
		if (panel == null) {
			panel = new Panel(TAB_CTX);
		}

		return panel;
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return L120M01A.class;
	}
	
	
	private void addAclLabel_23(ModelMap model, String id, PageParameters params, Enum... fdse) {
		addAclLabel(model, new AclLabel(id, params, getDomainClass(), AuthType.Modify, fdse));
		addAclLabel(model, new AclLabel(id + "2", params, getDomainClass(), AuthType.Accept, fdse));
		addAclLabel(model, new AclLabel(id + "3", params, getDomainClass(), AuthType.Query, fdse));
	}
	
	private void addAclLabel_ab(ModelMap model, String id, PageParameters params, Enum... fdse) {
		addAclLabel(model, new AclLabel(id, params, getDomainClass(), AuthType.Modify, fdse));
		addAclLabel(model, new AclLabel(id + "a", params, getDomainClass(), AuthType.Accept, fdse));
		addAclLabel(model, new AclLabel(id + "b", params, getDomainClass(), AuthType.Query, fdse));		
	}
	
	private void addAclLabel_23boolean(ModelMap model, String id, PageParameters params) {
		addAclLabel(model, new AclLabel(id, params, getDomainClass(), AuthType.Modify, false));
		addAclLabel(model, new AclLabel(id + "2", params, getDomainClass(), AuthType.Accept, false));
		addAclLabel(model, new AclLabel(id + "3", params, getDomainClass(), AuthType.Query, false));		
	}
	
	private void addAclLabel_abboolean(ModelMap model, String id, PageParameters params) {
		addAclLabel(model, new AclLabel(id, params, getDomainClass(), AuthType.Modify, false));
		addAclLabel(model, new AclLabel(id + "a", params, getDomainClass(), AuthType.Accept, false));
		addAclLabel(model, new AclLabel(id + "b", params, getDomainClass(), AuthType.Query, false));		
	}

}// ~
