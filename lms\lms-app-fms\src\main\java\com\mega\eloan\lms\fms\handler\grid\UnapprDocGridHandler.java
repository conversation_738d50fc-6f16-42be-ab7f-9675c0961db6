/* 
 *  UnapprDocGridHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.handler.grid;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.formatter.BranchNameFormatter;
import com.mega.eloan.common.formatter.BranchNameFormatter.ShowTypeEnum;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CLSDocStatusEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.flow.enums.LasDocStatusEnum;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.fms.service.UnapprDocService;
import com.mega.eloan.lms.model.C102M01A;
import com.mega.eloan.lms.model.C124M01A;
import com.mega.eloan.lms.model.C160M01A;
import com.mega.eloan.lms.model.C241M01A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L141M01A;
import com.mega.eloan.lms.model.L160M01A;
import com.mega.eloan.lms.model.L170M01A;
import com.mega.eloan.lms.model.L180M01A;
import com.mega.eloan.lms.model.L192M01A;
import com.mega.eloan.lms.model.L210M01A;
import com.mega.eloan.lms.model.L230M01A;
import com.mega.eloan.lms.model.L712M01A;
import com.mega.eloan.lms.model.L785M01A;
import com.mega.eloan.lms.model.L918M01A;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 取消覆核
 * </pre>
 * 
 * @since 2012/6/26
 * <AUTHOR>
 * @version <ul>
 *          2012/6/26,REX,new
 *          </ul>
 */
@Scope("request")
@Controller("unapprdocgridhandler")
public class UnapprDocGridHandler extends AbstractGridHandler {

	@Resource
	UserInfoService userInfoService;

	@Resource
	BranchService branchService;
	@Resource
	UnapprDocService unapprDocService;

	/**
	 * 查詢已核准和婉卻
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */

	@SuppressWarnings({ "unchecked", "rawtypes" })
	public CapGridResult queryForBack(ISearch pageSetting,
			PageParameters params) throws CapException {
		String ownBrId = Util.trim(params.getString("ownBrId", ""));
		String selectCase = Util.trim(params.getString("selectCase", ""));
		String randomCode = Util.trim(params.getString("randomCode", ""));
		if (Util.isNotEmpty(randomCode)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"randomCode", randomCode);
		}
		if (Util.isNotEmpty(ownBrId)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
					ownBrId);
		}

		String file_oid = Util.trim(params.getString("file_oid", ""));
		if (Util.isNotEmpty(file_oid)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					EloanConstants.OID, file_oid);
		}

		// L120M01A 案件簽報書
		// L141M01A 聯行額度明細表
		// L160M01A 動用審核表
		// L210M01A 修改資料特殊流程
		// L230M01A 簽約未動用
		// L170M01A 企金覆審報告表
		// L180M01A 企金覆審名單
		// C241M01A 個金覆審報告表
		// L192M01A 稽核工作底稿

		Class className = null;
		ArrayList<String> docstaus = new ArrayList<String>();
		if (Util.isNotEmpty(selectCase)) {
			if (UtilConstants.CaseDefName.案件簽報書.equals(selectCase)) {
				className = L120M01A.class;
				docstaus.add(CreditDocStatusEnum.海外_已核准.getCode());
				docstaus.add(CreditDocStatusEnum.海外_婉卻.getCode());
				docstaus.add(CreditDocStatusEnum.海外_待撤件.getCode());
				docstaus.add(CreditDocStatusEnum.授管處_待陳復.getCode());
			} else if (UtilConstants.CaseDefName.聯行額度明細表.equals(selectCase)) {
				className = L141M01A.class;
				docstaus.add(CreditDocStatusEnum.海外_已核准.getCode());
				docstaus.add(CreditDocStatusEnum.海外_婉卻.getCode());
			} else if (UtilConstants.CaseDefName.動用審核表.equals(selectCase)) {
				className = L160M01A.class;
				docstaus.add(CreditDocStatusEnum.海外_已核准.getCode());
				docstaus.add(CreditDocStatusEnum.海外_婉卻.getCode());
				docstaus.add(CreditDocStatusEnum.先行動用_已覆核.getCode());
			} else if (UtilConstants.CaseDefName.修改資料特殊流程.equals(selectCase)) {
				className = L210M01A.class;
				docstaus.add(CreditDocStatusEnum.海外_已核准.getCode());
				docstaus.add(CreditDocStatusEnum.海外_婉卻.getCode());
			} else if (UtilConstants.CaseDefName.簽約未動用.equals(selectCase)) {
				className = L230M01A.class;
				docstaus.add(CreditDocStatusEnum.海外_已核准.getCode());
				docstaus.add(CreditDocStatusEnum.海外_婉卻.getCode());
			} else if (UtilConstants.CaseDefName.企金覆審報告表.equals(selectCase)) {
				className = L170M01A.class;
				docstaus.add(RetrialDocStatusEnum.已核准.getCode());
			} else if (UtilConstants.CaseDefName.企金覆審名單.equals(selectCase)) {
				className = L180M01A.class;
				docstaus.add(RetrialDocStatusEnum.已核准.getCode());
				docstaus.add(RetrialDocStatusEnum.已產生覆審名單報告檔.getCode());
			} else if (UtilConstants.CaseDefName.個金覆審報告表.equals(selectCase)) {
				className = C241M01A.class;
				docstaus.add(RetrialDocStatusEnum.已覆核未核定.getCode());
			} else if (UtilConstants.CaseDefName.稽核工作底稿.equals(selectCase)) {
				className = L192M01A.class;
				docstaus.add(LasDocStatusEnum.分行_已核准.getCode());
				docstaus.add(LasDocStatusEnum.稽核室_已核准.getCode());
			} else if (UtilConstants.CaseDefName.個金動用審核表.equals(selectCase)) {
				className = C160M01A.class;
				docstaus.add(CLSDocStatusEnum.已核准.getCode());
				docstaus.add(CLSDocStatusEnum.先行動用_已覆核.getCode());

			} else if (UtilConstants.CaseDefName.購置房屋擔保放款風險權數檢核表
					.equals(selectCase)) {
				className = C102M01A.class;
				docstaus.add(CreditDocStatusEnum.海外_已核准.getCode());
				docstaus.add(CreditDocStatusEnum.海外_婉卻.getCode());
			} else if (UtilConstants.CaseDefName.停權解除維護.equals(selectCase)) {
				className = L918M01A.class;
				docstaus.add(CreditDocStatusEnum.授管處_停權已覆核.getCode());
			} else if (UtilConstants.CaseDefName.大陸地區控管維護.equals(selectCase)) {
				className = L712M01A.class;
				docstaus.add(CreditDocStatusEnum.授管處_停權已覆核.getCode());
			} else if (UtilConstants.CaseDefName.全行額度明細表查詢.equals(selectCase)) {
				className = L785M01A.class;
				docstaus.add(CreditDocStatusEnum.授管處_停權已覆核.getCode());
			} else if (UtilConstants.CaseDefName.中小信保整批申請.equals(selectCase)) {
				className = C124M01A.class;
				docstaus.add(CreditDocStatusEnum.海外_已核准.getCode());
				docstaus.add(CreditDocStatusEnum.海外_已核准.getCode());
			} else {
				className = L120M01A.class;
			}
		}

		String start = Util.nullToSpace(params.getString("tempStartDate"));
		String end = Util.nullToSpace(params.getString("tempEndDate"));
		if (!Util.isEmpty(start) && !Util.isEmpty(end)) {
			Date startDate = Util.parseDate(start);
			Date endDate = Util.parseDate(end);
			Object[] date = { startDate, endDate };
			pageSetting.addSearchModeParameters(SearchMode.BETWEEN,
					"approveTime", date);
		}
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
				docstaus.toArray(new String[docstaus.size()]));
		Page<? extends GenericBean> page = unapprDocService.findPage(className,
				pageSetting);
		List<Meta> metas = (List<Meta>) page.getContent();
		for (Meta model : metas) {
			model.setTypCd(getMessage("typCd." + model.getTypCd()));
			// 設定顯示名稱 使用者id+重複序號+名稱
			model.setCustId(StrUtils.concat(Util.trim(model.getCustId()), " ",
					Util.trim(model.getDupNo())));
		}
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("ownBrId", new BranchNameFormatter(branchService,
				ShowTypeEnum.IDSpaceName));
		result.setDataReformatter(dataReformatter);
		return result;
	}
}
