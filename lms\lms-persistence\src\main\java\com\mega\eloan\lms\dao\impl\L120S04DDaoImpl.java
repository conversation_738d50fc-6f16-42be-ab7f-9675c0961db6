/* 
 * L120S04DDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L120S04DDao;
import com.mega.eloan.lms.model.L120S04D;

/** 各項往來資料檔 **/
@Repository
public class L120S04DDaoImpl extends LMSJpaDao<L120S04D, String>
	implements L120S04DDao {

	@Override
	public L120S04D findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120S04D> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L120S04D> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public L120S04D findByUniqueKey(String mainId, String keyCustId, String keyDupNo){
		ISearch search = createSearchTemplete();
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (keyCustId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "keyCustId", keyCustId);
		if (keyDupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "keyDupNo", keyDupNo);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<L120S04D> findByIndex01(String mainId, String keyCustId, String keyDupNo){
		ISearch search = createSearchTemplete();
		List<L120S04D> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (keyCustId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "keyCustId", keyCustId);
		if (keyDupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "keyDupNo", keyDupNo);
		search.setMaxResults(Integer.MAX_VALUE);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
}