var AccountGrid_height = 240;

initDfd.done(function(json){
	var AccountGrid = $('#AccountGrid').iGrid({
        handler: "cls3401gridhandler", // 設定handler
        height: AccountGrid_height, // 設定高度
        needPager: false,      
        shrinkToFit: false,
        postData: {
            formAction: "accountQuery"
        	, accountQuery_idDupList: (json.accountQuery_idDupList||'')
        },
        colModel: [{ colHeader: i18n.def.megaID, align: "center", width: 100, sortable: false, name: 'DP_custId'}
        , { colHeader: i18n.def.dupNo, align: "center", width: 80, sortable: false, name: 'DP_dupNo'}
        , { colHeader: i18n.def.compName, align: "center", width: 150, sortable: false, name: 'DP_custName'}
        , { colHeader: i18n.cls3401m01["label.actNo"], align: "center", width: 120, sortable: false, name: 'Account'}
        ],
        ondblClickRow: function(rowid){
            
        }
    });
	
	function chose_act_no(elm_id) {
		$('#AccountThickBox').thickbox({
	        title: i18n.cls3401m01["label.actNo"],
	        width: 550,
	        height: (AccountGrid_height + 150),
	        align: 'center',
	        valign: 'bottom',
	        buttons: {
	            'sure': function(){
	                var data = AccountGrid.getSingleData();
	                if (data) {
	                	$("#"+elm_id).val(data.Account);
	                    $.thickbox.close();
	                }
	            },
	            'cancel': function(){
	                $.thickbox.close();
	            }
	        }
	    });
	}
	
	$('#delivActno_imp').click(function(){
		chose_act_no("eloan_pa_deliv_A_t2");
    });

	$('#repayActno_imp').click(function(){
		chose_act_no("eloan_pa_repay_actNo");
    });
});

