/* 
 * PQUOTNF.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.mfaloan.bean;


import java.util.Date;
import javax.persistence.*;
import tw.com.iisi.cap.model.GenericBean;

/** 消金提前還款違約金免收條件 **/
public class PQUOTNF extends GenericBean{

	private static final long serialVersionUID = 1L;

	/** 
	 * 分行別<p/>
	 * 公司統一編號
	 */
	@Column(name="BRNO", length=3, columnDefinition="CHAR(3)",unique = true)
	private String brno;

	/** 借款人統編 **/
	@Column(name="CUSTID", length=10, columnDefinition="CHAR(10)",unique = true)
	private String custid;

	/** 重複序號 **/
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(01)",unique = true)
	private String dupno;

	/** 額度序號 **/
	@Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)",unique = true)
	private String cntrno;

	/** 產品種類 **/
	@Column(name="PRODC", length=2, columnDefinition="CHAR(2)")
	private String prodc;

	/** 
	 * 免收條件<p/>
	 * 總共9碼<br/>
	 *  若在審核書有勾選擇對應第n碼為1，沒有勾選擇為””<br/>
	 *  第1碼：借款人出售擔保品<br/>
	 *  第2碼：借款人同意本行於其結清貸款後三個月內不給抵押權塗銷同意書者,惟還款時須向戒護徵提切結書,或於簽訂借款契約書內明定之<br/>
	 *  第9碼：其他
	 */
	@Column(name="NFEECON", length=12, columnDefinition="CHAR (12)")
	private String nfeecon;

	/** 其他內容(會在BTT的 L02A 出現提示訊息) **/
	@Column(name="MEMO1", length=40, columnDefinition="CHAR (40)")
	private String memo1;

	/** 備用欄位1 **/
	@Column(name="RSFLD1", length=10, columnDefinition="CHAR (10)")
	private String rsfld1;

	/** 備用欄位2 **/
	@Column(name="RSFLD2", length=10, columnDefinition="CHAR(10)")
	private String rsfld2;

	/** 資料修改人(行員代號) **/
	@Column(name="UPDATER", length=40, columnDefinition="CHAR (40)")
	private String Updater;

	/** 資料修改日期 **/
	@Column(name="TMESTAMP", columnDefinition="TIMESTAMP")
	private Date tmestamp;
	
	/** 帳號或產品序號 **/
	@Column(name="LOANNO", length=14, columnDefinition="CHAR (14)")
	private String loanNo;

	
	
	
	/** 
	 * 取得分行別<p/>
	 * 公司統一編號
	 */
	public String getBrno() {
		return this.brno;
	}
	/**
	 *  設定分行別<p/>
	 *  公司統一編號
	 **/
	public void setBrno(String value) {
		this.brno = value;
	}

	/** 取得借款人統編 **/
	public String getCustid() {
		return this.custid;
	}
	/** 設定借款人統編 **/
	public void setCustid(String value) {
		this.custid = value;
	}

	/** 取得重複序號 **/
	public String getDupno() {
		return this.dupno;
	}
	/** 設定重複序號 **/
	public void setDupno(String value) {
		this.dupno = value;
	}

	/** 取得額度序號 **/
	public String getCntrno() {
		return this.cntrno;
	}
	/** 設定額度序號 **/
	public void setCntrno(String value) {
		this.cntrno = value;
	}

	/** 取得產品種類 **/
	public String getProdc() {
		return this.prodc;
	}
	/** 設定產品種類 **/
	public void setProdc(String value) {
		this.prodc = value;
	}

	/** 
	 * 取得免收條件<p/>
	 * 總共9碼<br/>
	 *  若在審核書有勾選擇對應第n碼為1，沒有勾選擇為””<br/>
	 *  第1碼：借款人出售擔保品<br/>
	 *  第2碼：借款人同意本行於其結清貸款後三個月內不給抵押權塗銷同意書者,惟還款時須向戒護徵提切結書,或於簽訂借款契約書內明定之<br/>
	 *  第9碼：其他
	 */
	public String getNfeecon() {
		return this.nfeecon;
	}
	/**
	 *  設定免收條件<p/>
	 *  總共9碼<br/>
	 *  若在審核書有勾選擇對應第n碼為1，沒有勾選擇為””<br/>
	 *  第1碼：借款人出售擔保品<br/>
	 *  第2碼：借款人同意本行於其結清貸款後三個月內不給抵押權塗銷同意書者,惟還款時須向戒護徵提切結書,或於簽訂借款契約書內明定之<br/>
	 *  第9碼：其他
	 **/
	public void setNfeecon(String value) {
		this.nfeecon = value;
	}

	/** 取得其他內容(會在BTT的 L02A 出現提示訊息) **/
	public String getMemo1() {
		return this.memo1;
	}
	/** 設定其他內容(會在BTT的 L02A 出現提示訊息) **/
	public void setMemo1(String value) {
		this.memo1 = value;
	}

	/** 取得備用欄位1 **/
	public String getRsfld1() {
		return this.rsfld1;
	}
	/** 設定備用欄位1 **/
	public void setRsfld1(String value) {
		this.rsfld1 = value;
	}

	/** 取得備用欄位2 **/
	public String getRsfld2() {
		return this.rsfld2;
	}
	/** 設定備用欄位2 **/
	public void setRsfld2(String value) {
		this.rsfld2 = value;
	}

	/** 取得資料修改人(行員代號) **/
	public String getUpdater() {
		return this.Updater;
	}
	/** 設定資料修改人(行員代號) **/
	public void setUpdater(String value) {
		this.Updater = value;
	}

	/** 取得資料修改日期 **/
	public Date getTmestamp() {
		return this.tmestamp;
	}
	/** 設定資料修改日期 **/
	public void setTmestamp(Date value) {
		this.tmestamp = value;
	}
	
	/** 取得帳號或產品序號 **/
	public void setLoanNo(String loanNo) {
		this.loanNo = loanNo;
	}
	/** 設定帳號或產品序號 **/
	public String getLoanNo() {
		return loanNo;
	}
	



}
