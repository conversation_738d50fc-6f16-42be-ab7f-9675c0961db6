package com.mega.eloan.lms.mfaloan.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <pre>
 * 公司董監事資料
 * </pre>
 * 
 * @since 2013/8/20
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/8/20,EL08034,new
 *          </ul>
 */
public interface MisEJF305Service {

	List<Map<String, Object>> getByCustId_LNF022_AVL_FAMT_T(String custId, BigDecimal famt);

	List<Map<String, Object>> getByCustId(String custId);

}
