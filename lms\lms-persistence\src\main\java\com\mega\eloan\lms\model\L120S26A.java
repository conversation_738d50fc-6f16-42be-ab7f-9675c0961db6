/* 
 * L120S26A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 企金T70明細檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L120S26A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class L120S26A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 統一編號 **/
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 重覆序號 **/
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/** 中文戶名 **/
	@Size(max = 120)
	@Column(name = "CUSTNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String custName;

	/**
	 * T70資料回覆日期
	 * <p/>
	 * YYYY/MM/DD
	 */
	@Size(max = 10)
	@Column(name = "T70DATE", length = 10, columnDefinition = "VARCHAR(10)")
	private String T70Date;

	/** T70查詢狀態 **/
	@Size(max = 10)
	@Column(name = "T70STATUS", length = 10, columnDefinition = "VARCHAR(10)")
	private String T70Status;

	/** T70負面紀錄 **/
	@Size(max = 10)
	@Column(name = "T70NEGFLAG", length = 10, columnDefinition = "VARCHAR(10)")
	private String T70NegFlag;

	/** T70未清償總餘額 **/
	@Size(max = 20)
	@Column(name = "T70AMT", length = 20, columnDefinition = "VARCHAR(20)")
	private String T70Amt;

	/**
	 * 實體檔案oid
	 * <p/>
	 * docfile的OID
	 */
	@Size(max = 32)
	@Column(name = "DOCFILEOID", length = 32, columnDefinition = "CHAR(32)")
	private String Docfileoid;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得統一編號 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定統一編號 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得重覆序號 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定重覆序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得中文戶名 **/
	public String getCustName() {
		return this.custName;
	}

	/** 設定中文戶名 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/**
	 * 取得T70資料回覆日期
	 * <p/>
	 * YYY/MM/DD
	 */
	public String getT70Date() {
		return this.T70Date;
	}

	/**
	 * 設定T70資料回覆日期
	 * <p/>
	 * YYY/MM/DD
	 **/
	public void setT70Date(String value) {
		this.T70Date = value;
	}

	/** 取得T70查詢狀態 **/
	public String getT70Status() {
		return this.T70Status;
	}

	/** 設定T70查詢狀態 **/
	public void setT70Status(String value) {
		this.T70Status = value;
	}

	/** 取得T70負面紀錄 **/
	public String getT70NegFlag() {
		return this.T70NegFlag;
	}

	/** 設定T70負面紀錄 **/
	public void setT70NegFlag(String value) {
		this.T70NegFlag = value;
	}

	/** 取得T70未清償總餘額 **/
	public String getT70Amt() {
		return this.T70Amt;
	}

	/** 設定T70未清償總餘額 **/
	public void setT70Amt(String value) {
		this.T70Amt = value;
	}

	/**
	 * 取得實體檔案oid
	 * <p/>
	 * docfile的OID
	 */
	public String getDocfileoid() {
		return this.Docfileoid;
	}

	/**
	 * 設定實體檔案oid
	 * <p/>
	 * docfile的OID
	 **/
	public void setDocfileoid(String value) {
		this.Docfileoid = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
