package com.mega.eloan.lms.fms.handler.form;

import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.multipart.MultipartFile;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.lms.fms.service.LMS8500Service;
import com.mega.eloan.lms.model.L850M01C;
import com.mega.sso.service.BranchService;

import jxl.Cell;
import jxl.CellType;
import jxl.DateCell;
import jxl.Sheet;
import jxl.Workbook;
import jxl.read.biff.BiffException;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.handler.FileUploadHandler;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 資料上傳作業專用的上傳handler，複製LMS2105V01FileUploadHandler來的
 * </pre>
 * 
 * @since 2016/4/12
 * <AUTHOR>
 * @version <ul>
 *          <li>2016/4/12,EL08034,new
 *          </ul>
 */
@Scope("request")
@Controller("lms8500m01fileuploadhandler")
public class LMS8500M01FileUploadHandler extends FileUploadHandler {

	@Autowired
	DocFileService fileService;

	@Resource
	LMS8500Service lms8500Service;

	@Resource
	BranchService branchSrv;

	@Override
	public IResult afterUploaded(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MultipartFile uFile = params.getFile(params.getString("fieldId"));

		String mainId = params.getString("mainId");

		// 設定上傳檔案資訊
		String fieldId = params.getString("fieldId");
		String fileName = uFile.getOriginalFilename();


		logger.info("LMS8500M01FileUploadHandler afterUploaded Begin ============================================>");
		logger.info("sysId=" + fieldId + "，fileName=" + fileName + "，fieldId="
				+ fieldId + "");

		// 各交易自行處理uFile檔案，這邊就不寫共用了，自己看想要抓excel裡面什麼資料來用
		// 可能指抓某幾欄的資料，也有可能是跑迴圈每筆都要進去
		// 甚至之後可能可以讀非EXCEL格式??
		if (Util.equals(fieldId, "uploadBisFile")) {
			return uploadBisFile(uFile);
		} else if (Util.equals(fieldId, "uploadCodeTypeFile")) {
			return uploadCodeTypeFile(mainId, uFile);
		} else {
			// 沒處理的檔案
			throw new CapMessageException("匯入method設定錯誤。", getClass());
		}

	}

	/**
	 * for LMS8500M01V00101
	 * 
	 * 引進BIS自有資本與風險性資產
	 * 
	 * @param uFile
	 * @return
	 * @throws CapMessageException
	 */
	private IResult uploadBisFile(MultipartFile uFile) throws CapMessageException {
		// 開始匯入EXCEL****************************************************************************
		Workbook workbook = null;
		String errMsg = "";
		InputStream is = null;
		String fileKey = "";
		boolean findError = false;

		int[] dimension = { -1, -1 };
		// J-111-0443_05097_B1001 Web e-Loan企金授信開發授信BIS評估表

		String errorMsg = "";
		String lmsBisSelfCapital = ""; // 自有資本(E46)
		String lmsBisMegaRwa = ""; // 風險性資產(E61)

		try {

			is = uFile.getInputStream();
			workbook = Workbook.getWorkbook(is);

			Sheet sheet = null;
			int totalCol = 0;

			if (true) {
				Sheet sheet0 = workbook.getSheet(0);
				int totalCol0 = sheet0.getColumns();
				String sheetName0 = sheet0.getName();
				logger.info("sheet 0 totalCol:[" + totalCol0 + "]");

				String sheet0ColumnF4 = "";
				if (totalCol0 >= 0) {
					if (StringUtils.contains(sheetName0, "仟元")) {
						sheet = sheet0;
						totalCol = totalCol0;
					}
				}
			}

			if (true) {
				Sheet sheet1 = workbook.getSheet(1);
				int totalCol1 = sheet1.getColumns();
				String sheetName1 = sheet1.getName();
				logger.info("sheet 1 totalCol:[" + totalCol1 + "]");

				String sheet1ColumnF4 = "";
				if (totalCol1 >= 0) {
					if (StringUtils.contains(sheetName1, "仟元")) {
						sheet = sheet1;
						totalCol = totalCol1;
					}
				}
			}

			if (totalCol == 0) {
				throw new CapMessageException("匯入之名單EXCEL格式錯誤。", getClass());
			}

			lmsBisSelfCapital = StringUtils.upperCase(Util
					.trim(getContents(sheet.getCell(4, 45)))); // 自有資本(E46)
			lmsBisMegaRwa = StringUtils.upperCase(Util.trim(getContents(sheet
					.getCell(4, 60)))); // 風險性資產(E61)

			workbook.close();

		} catch (IOException e) {
			logger.error(e.getMessage(), e);
			throw new CapMessageException("file IO ERROR", getClass());
		} catch (IndexOutOfBoundsException e) {
			logger.error(e.getMessage(), e);
			throw new CapMessageException("匯入之名單EXCEL格式錯誤。", getClass());
		} catch (BiffException be) {
			logger.error(be.getMessage(), be);
			throw new CapMessageException("file IO ERROR", getClass());
		} finally {
			if (is != null) {
				try {
					is.close();
					if (workbook != null) {
						workbook.close();
						workbook = null;
					}
				} catch (IOException e) {
					logger.debug("inputStream close Error", getClass());
				}
			}
		}

		if (Util.isNotEmpty(errMsg)) {
			throw new CapMessageException(errMsg, getClass());
		}

		return new CapAjaxFormResult().set("url", "file?id=" + fileKey)
				.set("fileKey", fileKey).set("imgWidth", dimension[0])
				.set("imgHeight", dimension[1]).set("errorMsg", errorMsg)
				.set("lmsBisSelfCapital", lmsBisSelfCapital)
				.set("lmsBisMegaRwa", lmsBisMegaRwa);

	}

	/**
	 * for LMS8500M01V09001
	 * 
	 * 引進CODETYPE共用參數
	 * 
	 * @param uFile
	 * @return
	 * @throws CapMessageException
	 */
	private IResult uploadCodeTypeFile(String mainId, MultipartFile uFile)
			throws CapMessageException {
		// 開始匯入EXCEL****************************************************************************
		Workbook workbook = null;
		InputStream is = null;
		String fileKey = "";
		boolean findError = false;
		int[] dimension = { -1, -1 };

		// 要匯入的codeType資料，裝在L850M01C裡面
		List<L850M01C> l850m01cList = new ArrayList<L850M01C>();

		try {

			is = uFile.getInputStream();
			workbook = Workbook.getWorkbook(is);

			Sheet sheet = workbook.getSheet(0);
			int totalCol = 0;
			if (sheet != null) {
				totalCol = sheet.getColumns();
			}
			logger.info("sheet 0 totalCol:[" + totalCol + "]");

			if (sheet == null || totalCol == 0) {
				throw new CapMessageException("匯入之名單EXCEL格式錯誤。", getClass());
			}

			for (int i = 1; i < sheet.getRows(); i++) {
				// 從第一條資料出發，第0條是title不處理
				// 每一條row就是一筆L850M01C
				// Cell[] row = sheet.getRow(i);
				L850M01C l850m01c = new L850M01C();

				// 只抓1~7個欄位，第0欄不使用
				for (int j = 1; j < 8; j++) {
					// 1:CODETYPE
					// 2:CODEVALUE
					// 3:CODEDESC
					// 4:LOCALE
					// 5:CODEDESC2
					// 6:CODEDESC3
					// 7:CODEORDER

					// getCell(int column, int row)
					// i是row行數，j是column欄位數
					String column = Util.trim(getContents(sheet.getCell(j, i)));
					try {
						l850m01c.set("detail" + String.valueOf(j), column);
					} catch (CapException e) {
						throw new CapMessageException("寫入L850M01C失敗",
								getClass());
					}
				}

				// 僅針對不可為空的三個欄位做檢查
				if (Util.isEmpty(l850m01c.getDetail1())) {
					// 1:CODETYPE
					throw new CapMessageException("匯入失敗，第" + (i + 1)
							+ "行資料，CODETYPE欄位不得為空", getClass());
				}
				if (Util.isEmpty(l850m01c.getDetail2())) {
					// 2:CODEVALUE
					throw new CapMessageException("匯入失敗，第" + (i + 1)
							+ "行資料，CODEVALUE欄位不得為空", getClass());
				}
				String locale = Util.trim(l850m01c.getDetail4());
				if (Util.isEmpty(locale)) {
					// 4:LOCALE
					throw new CapMessageException("匯入失敗，第" + (i + 1)
							+ "行資料，LOCALE欄位不得為空", getClass());
				}
				if (Util.notEquals(locale, "zh_TW")
						&& Util.notEquals(locale, "zh_CN")
						&& Util.notEquals(locale, "en")) {
					// 4:LOCALE
					throw new CapMessageException("匯入失敗，第" + (i + 1)
							+ "行資料，LOCALE欄位只可以為zh_TW、zh_CN、en", getClass());
				}
				l850m01cList.add(l850m01c);
			}
			workbook.close();

		} catch (IOException e) {
			logger.error(e.getMessage(), e);
			throw new CapMessageException("file IO ERROR", getClass());
		} catch (BiffException be) {
			logger.error(be.getMessage(), be);
			throw new CapMessageException("file IO ERROR", getClass());
		} finally {
			if (is != null) {
				try {
					is.close();
					if (workbook != null) {
						workbook.close();
						workbook = null;
					}
				} catch (IOException e) {
					logger.debug("inputStream close Error", getClass());
				}
			}
		}

		// 處理完excel放入L850M01C之後，還要檢查這次要匯入的資料是否都是不存在的資料
		StringBuilder errMsg = lms8500Service
				.checkCodeTypeCanInsert(l850m01cList);
		if (Util.isNotEmpty(errMsg)) {
			String error = "匯入失敗，因以下資料已存在於資料庫中，無法匯入<BR/>" + errMsg.toString();
			throw new CapMessageException(error, getClass());
		}

		// 如果到這裡都沒問題，就可以去真的寫入L850M01C了(先刪除後寫入)
		// 要call service才有transaction
		lms8500Service.deleteL850m01c(mainId);
		lms8500Service.saveL850m01c(mainId, l850m01cList);

		// 會跑到這邊就是沒出錯了
		CapAjaxFormResult result = new CapAjaxFormResult();
		return result;

	}

	private String getContents(Cell cell) {
		DateCell dCell = null;
		if (cell.getType() == CellType.DATE) {
			dCell = (DateCell) cell;
			// System.out.println("Value of Date Cell is: " + dCell.getDate());
			// ==> Value of Date Cell is: Thu Apr 22 02:00:00 CEST 2088
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
			// System.out.println(sdf.format(dCell.getDate()));
			// ==> 2088-04-22
			return sdf.format(dCell.getDate());
		}
		// possibly manage other types of cell in here if needed for your goals
		// read more:
		// http://www.quicklyjava.com/reading-excel-file-in-java-datatypes/#ixzz2fYIkHdZP
		return cell.getContents();
	}

	/**
	 * 將字串轉為BigDecimal格式
	 * 
	 * @param in
	 *            the input
	 * @return BigDecimal
	 */
	public String getBigDecimalString(String in) {

		char[] ca = in.toCharArray();
		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < ca.length; i++) {
			switch (ca[i]) {
			case '-':
			case '+':
			case '0':
			case '1':
			case '2':
			case '3':
			case '4':
			case '5':
			case '6':
			case '7':
			case '8':
			case '9':
			case '.':
				sb.append(ca[i]);
			}
		}
		return sb.toString();
	}

}
