/* 
 * L120S16A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Set;

import javax.persistence.*;
import javax.validation.constraints.*;

import org.apache.commons.lang3.builder.ToStringExclude;

import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 主要申請敘作內容主檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L120S16A", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "custId", "dupNo", "cntrNo" }))
public class L120S16A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * JOIN敘述說明檔L120S16B
	 * 
	 */
	@ToStringExclude
	@OneToMany(mappedBy = "l120s16a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private Set<L120S16B> l120s16b;

	public void setL120s16b(Set<L120S16B> l120s16b) {
		this.l120s16b = l120s16b;
	}

	public Set<L120S16B> getL120s16b() {
		return l120s16b;
	}

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 本案授信戶統編
	 * <p/>
	 * 資料來源：額度明細表主檔
	 */
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/**
	 * 本案授信戶重複序號
	 * <p/>
	 * 資料來源：額度明細表主檔
	 */
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/**
	 * 本案授信戶額度序號
	 * <p/>
	 * 資料來源：額度明細表主檔
	 */
	@Size(max = 12)
	@Column(name = "CNTRNO", length = 12, columnDefinition = "CHAR(12)")
	private String cntrNo;

	/**
	 * 本案授信戶區部別
	 * <p/>
	 * 101/02/18新增<br/>
	 * 資料來源：額度明細表主檔
	 */
	@Size(max = 1)
	@Column(name = "TYPCD", length = 1, columnDefinition = "CHAR(1)")
	private String typCd;

	/**
	 * 本案授信戶
	 * <p/>
	 * 資料來源：額度明細表主檔
	 */
	@Size(max = 150)
	@Column(name = "CUSTNAME", length = 150, columnDefinition = "VARCHAR(150)")
	private String custName;

	/**
	 * 性質
	 * <p/>
	 * 資料來源：額度明細表主檔<br/>
	 * 101/07/16調整欄位長度<br/>
	 * VARCHAR(150)(VARCHAR(30)
	 */
	@Size(max = 30)
	@Column(name = "PROPERTY", length = 30, columnDefinition = "VARCHAR(30)")
	private String property;

	/**
	 * 額度
	 * <p/>
	 * 資料來源：額度明細表主檔
	 */
	@Size(max = 600)
	@Column(name = "CURRENTAPPLY", length = 600, columnDefinition = "VARCHAR(600)")
	private String currentApply;

	/**
	 * 授信科目
	 * <p/>
	 * 資料來源：額度明細表主檔<br/>
	 * 101/07/16調整欄位長度<br/>
	 * VARCHAR(200)(VARCHAR(300)<br/>
	 * 102/05/16調整欄位長度<br/>
	 * VARCHAR(300)(VARCHAR(1536)
	 */
	@Size(max = 1536)
	@Column(name = "LNSUBJECT", length = 1536, columnDefinition = "VARCHAR(1536)")
	private String lnSubject;

	/**
	 * 期限
	 * <p/>
	 * 資料來源：額度明細表主檔<br/>
	 * 101/07/16調整欄位長度<br/>
	 * VARCHAR(100)(VARCHAR(200)<br/>
	 * 102/05/16調整欄位長度<br/>
	 * VARCHAR(200)(VARCHAR(600)
	 */
	@Size(max = 600)
	@Column(name = "PAYDEADLINE", length = 600, columnDefinition = "VARCHAR(600)")
	private String payDeadline;

	/**
	 * 連保人
	 * <p/>
	 * 資料來源：額度明細表主檔<br/>
	 * 102/05/16調整欄位長度<br/>
	 * VARCHAR(384)(VARCHAR(1800)
	 */
	@Size(max = 1800)
	@Column(name = "GUARANTOR", length = 1800, columnDefinition = "VARCHAR(1800)")
	private String guarantor;

	/**
	 * 擔保品
	 * <p/>
	 * 資料來源：額度明細表主檔<br/>
	 * 102/05/16調整欄位長度<br/>
	 * VARCHAR(384)(VARCHAR(1800)
	 */
	@Size(max = 1800)
	@Column(name = "COLLATERAL", length = 1800, columnDefinition = "VARCHAR(1800)")
	private String collateral;

	/** 列印順序 **/
	@Digits(integer = 5, fraction = 0, groups = Check.class)
	@Column(name = "PRINTSEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer printSeq;

	/**
	 * 輸入資料檢誤完成(Y/N)
	 * <p/>
	 * 100/12/05新增<br/>
	 * Y/N<br/>
	 * 預先檢核資料是否已登錄完整，供執行【呈主管覆核】時，可快速檢核資料是否完備用。
	 */
	@Size(max = 1)
	@Column(name = "CHKYN", length = 1, columnDefinition = "CHAR(1)")
	private String chkYN;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得本案授信戶統編
	 * <p/>
	 * 資料來源：額度明細表主檔
	 */
	public String getCustId() {
		return this.custId;
	}

	/**
	 * 設定本案授信戶統編
	 * <p/>
	 * 資料來源：額度明細表主檔
	 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/**
	 * 取得本案授信戶重複序號
	 * <p/>
	 * 資料來源：額度明細表主檔
	 */
	public String getDupNo() {
		return this.dupNo;
	}

	/**
	 * 設定本案授信戶重複序號
	 * <p/>
	 * 資料來源：額度明細表主檔
	 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/**
	 * 取得本案授信戶額度序號
	 * <p/>
	 * 資料來源：額度明細表主檔
	 */
	public String getCntrNo() {
		return this.cntrNo;
	}

	/**
	 * 設定本案授信戶額度序號
	 * <p/>
	 * 資料來源：額度明細表主檔
	 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/**
	 * 取得本案授信戶區部別
	 * <p/>
	 * 101/02/18新增<br/>
	 * 資料來源：額度明細表主檔
	 */
	public String getTypCd() {
		return this.typCd;
	}

	/**
	 * 設定本案授信戶區部別
	 * <p/>
	 * 101/02/18新增<br/>
	 * 資料來源：額度明細表主檔
	 **/
	public void setTypCd(String value) {
		this.typCd = value;
	}

	/**
	 * 取得本案授信戶
	 * <p/>
	 * 資料來源：額度明細表主檔
	 */
	public String getCustName() {
		return this.custName;
	}

	/**
	 * 設定本案授信戶
	 * <p/>
	 * 資料來源：額度明細表主檔
	 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/**
	 * 取得性質
	 * <p/>
	 * 資料來源：額度明細表主檔<br/>
	 * 101/07/16調整欄位長度<br/>
	 * VARCHAR(150)(VARCHAR(30)
	 */
	public String getProperty() {
		return this.property;
	}

	/**
	 * 設定性質
	 * <p/>
	 * 資料來源：額度明細表主檔<br/>
	 * 101/07/16調整欄位長度<br/>
	 * VARCHAR(150)(VARCHAR(30)
	 **/
	public void setProperty(String value) {
		this.property = value;
	}

	/**
	 * 取得額度
	 * <p/>
	 * 資料來源：額度明細表主檔
	 */
	public String getCurrentApply() {
		return this.currentApply;
	}

	/**
	 * 設定額度
	 * <p/>
	 * 資料來源：額度明細表主檔
	 **/
	public void setCurrentApply(String value) {
		this.currentApply = value;
	}

	/**
	 * 取得授信科目
	 * <p/>
	 * 資料來源：額度明細表主檔<br/>
	 * 101/07/16調整欄位長度<br/>
	 * VARCHAR(200)(VARCHAR(300)<br/>
	 * 102/05/16調整欄位長度<br/>
	 * VARCHAR(300)(VARCHAR(1536)
	 */
	public String getLnSubject() {
		return this.lnSubject;
	}

	/**
	 * 設定授信科目
	 * <p/>
	 * 資料來源：額度明細表主檔<br/>
	 * 101/07/16調整欄位長度<br/>
	 * VARCHAR(200)(VARCHAR(300)<br/>
	 * 102/05/16調整欄位長度<br/>
	 * VARCHAR(300)(VARCHAR(1536)
	 **/
	public void setLnSubject(String value) {
		this.lnSubject = value;
	}

	/**
	 * 取得期限
	 * <p/>
	 * 資料來源：額度明細表主檔<br/>
	 * 101/07/16調整欄位長度<br/>
	 * VARCHAR(100)(VARCHAR(200)<br/>
	 * 102/05/16調整欄位長度<br/>
	 * VARCHAR(200)(VARCHAR(600)
	 */
	public String getPayDeadline() {
		return this.payDeadline;
	}

	/**
	 * 設定期限
	 * <p/>
	 * 資料來源：額度明細表主檔<br/>
	 * 101/07/16調整欄位長度<br/>
	 * VARCHAR(100)(VARCHAR(200)<br/>
	 * 102/05/16調整欄位長度<br/>
	 * VARCHAR(200)(VARCHAR(600)
	 **/
	public void setPayDeadline(String value) {
		this.payDeadline = value;
	}

	/**
	 * 取得連保人
	 * <p/>
	 * 資料來源：額度明細表主檔<br/>
	 * 102/05/16調整欄位長度<br/>
	 * VARCHAR(384)(VARCHAR(1800)
	 */
	public String getGuarantor() {
		return this.guarantor;
	}

	/**
	 * 設定連保人
	 * <p/>
	 * 資料來源：額度明細表主檔<br/>
	 * 102/05/16調整欄位長度<br/>
	 * VARCHAR(384)(VARCHAR(1800)
	 **/
	public void setGuarantor(String value) {
		this.guarantor = value;
	}

	/** 取得列印順序 **/
	public Integer getPrintSeq() {
		return this.printSeq;
	}

	/** 設定列印順序 **/
	public void setPrintSeq(Integer value) {
		this.printSeq = value;
	}

	/**
	 * 取得輸入資料檢誤完成(Y/N)
	 * <p/>
	 * 100/12/05新增<br/>
	 * Y/N<br/>
	 * 預先檢核資料是否已登錄完整，供執行【呈主管覆核】時，可快速檢核資料是否完備用。
	 */
	public String getChkYN() {
		return this.chkYN;
	}

	/**
	 * 設定輸入資料檢誤完成(Y/N)
	 * <p/>
	 * 100/12/05新增<br/>
	 * Y/N<br/>
	 * 預先檢核資料是否已登錄完整，供執行【呈主管覆核】時，可快速檢核資料是否完備用。
	 **/
	public void setChkYN(String value) {
		this.chkYN = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	public void setCollateral(String collateral) {
		this.collateral = collateral;
	}

	public String getCollateral() {
		return collateral;
	}

}
