/* 
 * DEB2010R03GridHandler.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.handler.grid;

import java.util.Calendar;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.formatter.UserNameFormatter;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.service.LMSGridService;
import com.mega.eloan.lms.model.L201S99A;
import com.mega.eloan.lms.model.L201S99B;
import com.mega.eloan.lms.rpt.pages.LMS9550R01Page;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapFormatException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.formatter.ADDateFormatter;
import tw.com.iisi.cap.formatter.ADDateTimeFormatter;
import tw.com.iisi.cap.formatter.IBeanFormatter;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;

/**
 * <pre>
 * 主債協商之Grid handler。
 * 使用此Grid Handler 可能帶入文件狀態如下：
 * <ol>
 * <li>傳送卡務檔案作業</li>
 * </ol>
 * </pre>
 * 
 * @since 2012/7/16
 * <AUTHOR>
 * @version <ul>
 *          <li>new
 *          </ul>
 */
@Scope("request")
@Controller("lms9550v01gridhandler")
public class LMS9550V01GridHandler extends AbstractGridHandler {

	@Resource
	LMSGridService service;
	@Resource
	DocFileService docFileService;
	@Resource
	UserInfoService userInfoSrv;

	/**
	 * 查詢 Grid 資料。
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryView(ISearch pageSetting, PageParameters params) throws CapException {

		if (!CapString.isEmpty(params.getString("dateS", null))
				&& !CapString.isEmpty(params.getString("dateE", null))) {
			Calendar cal = Calendar.getInstance();
			Calendar sDate = (Calendar) cal.clone();
			Calendar eDate = (Calendar) cal.clone();
			Map<String, String> msgParam = new HashMap<String, String>();
			try {
				sDate.setTime(CapDate.getDate(params.getString("dateS"),
						"yyyy-MM-dd"));
				eDate.setTime(CapDate.getDate(params.getString("dateE"),
						"yyyy-MM-dd"));
			} catch (Exception e) {
				msgParam.put("colName", MessageBundleScriptCreator
						.getComponentResource(LMS9550R01Page.class)
						.getProperty("pop.02"));
				throw new CapMessageException(getMessage("EFD0001", msgParam),
						getClass());
			}
			if (sDate.getTimeInMillis() < eDate.getTimeInMillis()) {
				eDate.add(Calendar.DAY_OF_MONTH, 1);
				pageSetting.addSearchModeParameters(SearchMode.BETWEEN,
						"creDate", new Object[] { sDate, eDate });
			} else {
				sDate.add(Calendar.DAY_OF_MONTH, 1);
				pageSetting.addSearchModeParameters(SearchMode.BETWEEN,
						"creDate", new Object[] { eDate, sDate });
			}
		}
		// 取得資料
		Page<L201S99A> page = service.findPage(L201S99A.class, pageSetting);

		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		formatter.put("qryDate", new ADDateFormatter());
		formatter.put("creDate", new ADDateTimeFormatter(
				"yyyy-MM-dd hh:mm:ss a"));
		formatter.put("dataCntA01", new dataCntFormatter("A01"));
//		formatter.put("dataCntZ93", new dataCntFormatter("Z93"));
//		formatter.put("dataCntZ96", new dataCntFormatter("Z96"));
//		formatter.put("dataCnt98F", new dataCntFormatter("98F"));
//		formatter.put("dataCnt98E", new dataCntFormatter("98E"));
//		formatter.put("dataCntZ78", new dataCntFormatter("Z78"));
//		formatter.put("dataCntZ63", new dataCntFormatter("Z63"));
		formatter.put("exeMsg", new exeMsg());

		// 第三個參數為formatting
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow(), formatter);

		return result;
	}

	/**
	 * 查詢文件 Grid 資料。
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryDocView(ISearch pageSetting,
			PageParameters params) throws CapException {

		String mainId = CapString.trimNull(params.getString("mainId"));
		String fieldId = CapString.trimNull(params.getString("fieldId"));

		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "sysId", "DEB");
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "fieldId",
				fieldId);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				null);

		// 取得資料
		Page<DocFile> page = docFileService.readToGrid(pageSetting);

		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow(), null);

		return result;
	}

	/**
	 * dataCntFormatter formatter
	 */
	class dataCntFormatter implements IBeanFormatter {
		private static final long serialVersionUID = 1L;
		String dataType;

		public dataCntFormatter(String dataType) {
			this.dataType = dataType;
		}

		/*
		 * (non-Javadoc)
		 * 
		 * @see tw.com.iisi.cap.formatter.IFormatter#reformat(java.lang.Object)
		 */
		@SuppressWarnings("unchecked")
		public String reformat(Object in) throws CapFormatException {
			L201S99A meta = (L201S99A) in;
			if (meta != null) {
				for (L201S99B detail : meta.getL201s99bs()) {
					if (dataType.equals(detail.getDataType())
							&& detail.getDataCnt() != null) {
						return detail.getDataCnt().toPlainString();
					}
				}
			}
			return EloanConstants.EMPTY_STRING;
		}
	}

	/**
	 * exeMsg formatter
	 */
	class exeMsg implements IBeanFormatter {
		private static final long serialVersionUID = 1L;
		UserNameFormatter unFormatter = new UserNameFormatter(userInfoSrv);
		Properties properties = MessageBundleScriptCreator
				.getComponentResource(LMS9550R01Page.class);

		/*
		 * (non-Javadoc)
		 * 
		 * @see tw.com.iisi.cap.formatter.IFormatter#reformat(java.lang.Object)
		 */
		@SuppressWarnings("unchecked")
		public String reformat(Object in) throws CapFormatException {
			L201S99A meta = (L201S99A) in;
			if (meta != null
					&& (meta.getSendTime() != null || !CapString.isEmpty(meta
							.getSender()))) {
				return StrUtils.concat(CapString.trimNull(CapDate.convertTimestampToString(
						meta.getSendTime(), "yyyy-MM-dd hh:mm:ss a")),
						CapConstants.SPACE, CapString.trimNull(unFormatter
								.reformat(meta.getSender())), ":", CapString
								.trimNull(properties.get("exeResult."
										+ meta.getExeResult())));
			}
			return EloanConstants.EMPTY_STRING;
		}
	}
}
