$(function(){

    var grid = $("#gridview").iGrid({
        handler: 'cls1221gridhandler',
        height: 350,
        width: 800,
        autowidth: false,
        action: "queryView_P",
        postData: {
            docStatus: viewstatus
        },
        rowNum: 15,
        sortname: "createTime",
        sortorder: "desc",
        multiselect: false, 
    	colModel : [
    	  {name : 'oid', hidden : true}
    	 ,{name : 'mainId',hidden : true}
    	 ,{name : 'docStatus',hidden : true}
    	 ,{name : 'ownBrId',hidden : true}
    	,{
			colHeader : i18n.cls1220m04['C122M01A.custId'], 
			width : 90,
			sortable : true,
			name : 'custId',
			formatter: 'click',
			onclick : openDoc		
    	},{
			colHeader : i18n.cls1220m04['C122M01A.custName'],
			align : "left",
			width : 90,
			sortable : true,
			name : 'custName'
    	},{
			colHeader : i18n.cls1220m04['C122M01A.applyTS'],
			align : "left",
			width : 100,		
			name : 'applyTS'
    	},{
			colHeader : i18n.cls1220m04['C122M01A.statFlag'],
			align : "left",
			width : 60,
			name : 'statFlag' 
		},{
			colHeader : i18n.cls1220m04['label.notifyMemo'],
			align : "left",
			width : 140,		
			name : 'notifyMemo'
		},{
			colHeader : i18n.cls1220m04['C122M01A.ploanCaseNo'],
			align : "left",
			width : 100,		
			name : 'ploanCaseId'
		},{
			colHeader : i18n.cls1220m04['C122M01A.updater'],
			align : "left",
			width : 60,  
			name : 'updater'
		},{
			colHeader : i18n.cls1220m04['C122M01A.ploanPlan'],
			align : "left",
			width : 40,  
			name : 'ploanPlan'
		},{
			colHeader : i18n.cls1220m04['label.loanBrNo'], //label.loanBrNo=目前貸款往來分行
			align : "left", width : 60, sortable : false,  
			name : 'loanBrNo' 
		},{
			colHeader : i18n.cls1220m04['label.payrollTransfersBrNo'], //label.payrollTransfersBrNo=目前薪轉往來分行
			align : "left", width : 60, sortable : false,  
			name : 'payrollTransfersBrNo'
//		},{
//			colHeader : i18n.cls1220m04['C122M01A.updateTime'],
//			align : "left",
//			width : 130,
//			name : 'updateTime'
		}]				
	});
   
    
    function openDoc(cellvalue, options, rowObject){
    	$.form.submit({
			url : '../lms/cls1220m04/01',
			data : {
                mainOid: rowObject.oid,
                mainId: rowObject.mainId,
                mainDocStatus: rowObject.docStatus
            },
            target: rowObject.oid
		});
    };
	
    $("#buttonPanel").find('#btnView').click(function(){
		var selrow = grid.getGridParam('selrow');
        if (selrow) {
            openDoc('', '', grid.getRowData(selrow));
        }
        else {
            CommonAPI.showErrorMessage(i18n.def["grid.selrow"]);
        }	    
    }).end().find("#btnQueryCustLoanRecord").click(function(){//查詢客戶申貸記錄
    	var _id = "div_applyKindP_History";
    	var _form = "div_applyKindP_History_form";
		var grid_id = "grid_applyKindP_History";
		
		//clear data
		$("#"+_form).reset();
		var my_post_data = get_param_grid_history('N');
		
		if($("#"+grid_id+".ui-jqgrid-btable").length >0){
			$("#"+grid_id).jqGrid("setGridParam", {
				postData : my_post_data,
				search : true
			}).trigger("reloadGrid");	        		
		}else{
			$("#"+grid_id).iGrid({
				handler : 'cls1221gridhandler',
				height : 160,
				divWidth: 0,
				postData : my_post_data,			
				colModel : [ {
		            colHeader: i18n.cls1220m04['C122M01A.custId'],
		            align: "left", width: 60, sortable:true, name: 'custId'
				}, {
		            colHeader: i18n.cls1220m04['C122M01A.custName'],
		            align: "left", width: 120, sortable: false, name: 'custName'
				}, {
		            colHeader: i18n.cls1220m04['C122M01A.applyTS'],
		            align: "left", width: 70, sortable: false, name: 'applyTS'
				}, {
		        	colHeader: i18n.cls1220m04['grid.ownBrId'],
		            align: "left", width: 100, sortable: false, name: 'ownBrId'
		       } ]
			});
		}    
		if(true){
			$("#"+_id+" #div_applyKindP_History_label_custId").val(i18n.cls1220m04['C122M01A.custId']);
		}		
		
		$("#"+_id).thickbox({
	        title: '',
	        width: 650, height: 400, align: 'center', valign: 'bottom', modal: false,
	        buttons: {
	            "close": function(){
	               $.thickbox.close();
	            }
	        }
	    });
    }).end().find("#btnCaseToChange").click(function(){
    	var grid_chose_data = grid.getSingleData();
		if (grid_chose_data){
			CommonAPI.showAllBranch({
		         btnAction: function(a, rtn){
		        	 MegaApi.confirmMessage("是否將"+grid_chose_data.custId+" "+grid_chose_data.custName 
		        			 +" 由 "+grid_chose_data.ownBrId+" 改分派至 "+rtn.brNo +" ？", function(action){
							if (action){
								$.thickbox.close();
				            	
								changeOwnBrId_memo().done(function(json_changeOwnBrId_memo){
					            	$.ajax({handler : 'cls1220m04formhandler',
										action : 'changeOwnBrId_then_mail',
										data : $.extend(json_changeOwnBrId_memo, {'mainOid':grid_chose_data.oid, 'newBrNo':rtn.brNo} )
					            	}).done(function(json){
										grid.trigger("reloadGrid");
									});
								});
							}
					}); 
		        	
	            }
	        });
        }else{
//        	CommonAPI.showMessage(i18n.def['grid.selrow']);
        }
    }).end().find("#btnFilter").click(function(){    	
    	var _id = "_div_cls1220v08_b";
		var _form = _id+"_form";
		 	
		if ($("#"+_id).length == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");
			dyna.push("<form id='"+_form+"'>");
			dyna.push("	<table class='tb2' width='100%' >");
			dyna.push("	<tr>");
			dyna.push("	  <td class='hd2' width='30%' nowrap>"+i18n.cls1220m04['C122M01A.custId']+"</td>");
			dyna.push("	  <td><input type='text' id='custId' name='custId' maxlength='10' size='12' /></td>");
			dyna.push("	</tr>");			
			dyna.push("	<tr>");
			dyna.push("	  <td class='hd2' width='30%' nowrap>"+i18n.cls1220m04['C122M01A.ploanCaseNo']+"</td>");
			dyna.push("	  <td><input type='text' id='ploanCaseId' name='ploanCaseId' maxlength='30' size='30' /></td>");
			dyna.push("	</tr>");			
			dyna.push("	<tr>");			
			dyna.push("	  <td class='hd2' width='30%' nowrap>"+i18n.cls1220m04['C122M01A.applyTS']+"</td>");
			dyna.push("	  <td>");
			dyna.push("	   <input type='text' id='applyTS_beg' name='applyTS_beg' maxlength='10' class='date' />");
			dyna.push("	 ~ <input type='text' id='applyTS_end' name='applyTS_end' maxlength='10' class='date' />");
			dyna.push("	  </td>");
			dyna.push("	</tr>");
			dyna.push("	<tr>");
			dyna.push("	  <td class='hd2' width='30%' nowrap>"+i18n.cls1220m04['C122M01A.statFlag']+"</td>");
			dyna.push("	  <td><select id='statFlag' name='statFlag'> ");
			dyna.push("	  <option value=''>"+i18n.def.comboSpace+"</option>");
			dyna.push("	  <option value='0'>"+i18n.cls1220m04['C122M01A.statFlag.applyKindP.0']+"</option>");
			dyna.push("	  <option value='1'>"+i18n.cls1220m04['C122M01A.statFlag.applyKindP.1']+"</option>");
			dyna.push("	  <option value='2'>"+i18n.cls1220m04['C122M01A.statFlag.applyKindP.2']+"</option>");
			dyna.push("	  <option value='5'>"+i18n.cls1220m04['C122M01A.statFlag.applyKindP.5']+"</option>");
			dyna.push("	  <option value='6'>"+i18n.cls1220m04['C122M01A.statFlag.applyKindP.6']+"</option>");
			dyna.push("	  <option value='A'>"+i18n.cls1220m04['C122M01A.statFlag.applyKindP.A']+"</option>");
			dyna.push("	  <option value='D'>"+i18n.cls1220m04['C122M01A.statFlag.applyKindP.D']+"</option>");
			dyna.push("	  <option value='E'>"+i18n.cls1220m04['C122M01A.statFlag.applyKindP.E']+"</option>");
			dyna.push("	  </select>");
			dyna.push("	  </td>");
			dyna.push("	</tr>");		
			dyna.push("	<tr>");
			dyna.push("	  <td class='hd2' width='30%' nowrap>"+i18n.cls1220m04['C122M01A.ploanPlan']+"</td>");
			dyna.push("	  <td><select space='true' combokey='ploan_plan' id='ploanPlan' name='ploanPlan'/> ");
			dyna.push("	  </td>");
			dyna.push("	</tr>");
			
			dyna.push(" </table>");
			dyna.push("</form>");
			
			dyna.push("</div>");
			
		    $('body').append(dyna.join(""));		
		    
		    $("#"+_form).find(".date").filter(function(){
		        return !$(this).prop('readonly');
		    }).datepicker();
		    
		    if(true){
		    	//在 function pageInit(...) 中，會針對 欄位 custId addClass upText
		    	pageInit.call( $("#"+_id) );
		    }
		}
		//clear data
		$("#"+_form).reset();
		
		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
	       title: i18n.def.query,
	       width: 480,
           height: 280,
           align: "center",
           valign: "bottom",
           modal: false,
           i18n: i18n.def,
           buttons: {
               "sure": function(){
            	   $.thickbox.close();
            	   //~~~~
            	   grid.jqGrid("setGridParam", {
	           	    	postData : $.extend({ docStatus: viewstatus}, $("#"+_form).serializeData() ),
	           			search: true			
	           	   }).trigger("reloadGrid");
               },
               "cancel": function(){
            	   $.thickbox.close();            	  
               }
           }
		});
    }).end().find("#btnCreateCSCExcel").click(function(){//產生中鋼消貸Excel
    	var _id = "div_create_CSC_excel";
    	var _form = "div_create_CSC_excel_form";
		
		//clear data
		$("#"+_form).reset();
		$("#"+_id).thickbox({
	        title: '',
	        width: 450, height: 200, align: 'center', valign: 'bottom', modal: false,
	        buttons: {
	        	'sure' : function(){
					$.form.submit({
                     	url: __ajaxHandler,
                  		target : "_blank",
                  		data : {
                  			_pa : 'lmsdownloadformhandler',
                  			'csc_ploan_plan': $("#csc_ploan_plan").val(),
                  			'csc_applyTS_beg': $("#csc_applyTS_beg").val(),
                  			'csc_applyTS_end': $("#csc_applyTS_end").val(),
                            'fileDownloadName' : 'data_.xls',
                  			'serviceName' : "cls1220r08rptservcie"
                  		}
                  	 });
                  	 $.thickbox.close();
				},
	            "close": function(){
	               $.thickbox.close();
	            }
	        }
	    });
	});

    $("#filter_historyBtn").click(function(){	
		
		var grid_id= "grid_applyKindP_History";
		$("#"+grid_id).jqGrid("setGridParam", {
			postData : get_param_grid_history('Y'),
			search : true
		}).trigger("reloadGrid");			
	
	});
    
    // 002港都分行中鋼EXCEL放下拉選單選項
    if (userInfo.unitNo == '002') {
    	$.ajax({
    		handler: "cls1220m04formhandler",
    		action: "getPloanPlanList"
    	}).done(function(json){
			$("#csc_ploan_plan").setOptions(json, true);
		});
    }
    
    function get_param_grid_history(flag){
    	var _form = "div_applyKindP_History_form";
    	return {
			'formAction' : "query_applyKindP_History",
			'custId': $("#"+_form).find("[name=search_custId]").val(),
			'flag':flag
		};
    }
    
    function changeOwnBrId_memo(){
    	var my_dfd = $.Deferred();      	
    	$("#div_changeOwnBrId_memo").thickbox({ // 使用選取的內容進行彈窗
 	       width: 650,
           height: 280,
           align: "center",
           valign: "bottom",
           modal: false,
           i18n: i18n.def,
           buttons: {
               "sure": function(){
            	   $frm = $("#div_changeOwnBrId_memo_form");
                   if ($frm.valid()) {
                	   $.thickbox.close();
                       my_dfd.resolve( $frm.serializeData() ); 	   
                   }  	
               }
           }
 		});
    	return my_dfd.promise();
    }
    
});

