$(function(){	
	var my_colModel = [{
        colHeader: "",name: 'oid', hidden: true
    }, {
    	colHeader: "",name: 'mainId', hidden: true
    }, {
        colHeader: i18n.cls2701v01["C900M01I.ownBrId"],
        align: "left", width: 80, sortable: true, name: 'ownBrId'
    }, {
        colHeader: i18n.cls2701v01["C900M01I.orgCustId"],
        align: "left", width: 80, sortable: true, name: 'orgCustId'
    }, {
        colHeader: ' ',
        align: "left", width: 20, sortable: true, name: 'orgDupNo'
//    }, {
//        colHeader: i18n.cls2701v01["C900M01I.orgCustName"],
//        align: "left", width: 110, sortable: true, name: 'orgCustName'
    }, {
        colHeader: i18n.cls2701v01["C900M01I.custId"],
        align: "left", width: 80, sortable: true, name: 'custId'
    }, {
        colHeader: ' ',
        align: "left", width: 20, sortable: true, name: 'dupNo'
    }, {
        colHeader: i18n.cls2701v01["C900M01I.custName"],
        align: "left", width: 110, sortable: true, name: 'custName'
    }, {
        colHeader: i18n.cls2701v01["C900M01I.docDate"],
        align: "left", width: 80, sortable: true, name: 'docDate'
    }, {
        colHeader: i18n.cls2701v01["C900M01I.docNo"],
        align: "left", width: 180, sortable: true, name: 'docNo'
    }, {
    	colHeader: i18n.cls2701v01["C900M01I.updater"], //異動人員
        align: "left", width: 80, sortable: true, name: 'updater'
    }, {
    	colHeader: i18n.cls2701v01["C900M01I.updateTime"], //異動日期
        align: "left", width: 120, sortable: true, name: 'updateTime'
    }];
		
	var grid = $("#gridview").iGrid({
        handler: "cls2701gridhandler",
        height: 350,
        rowNum: 15,
        shrinkToFit: false,
        sortname: 'docDate',
		sortorder: 'desc',
        postData: {
            formAction: "queryC900M01I",
            docStatus: viewstatus
        },
        colModel: my_colModel,
        ondblClickRow: function(rowid){ 
        }
    });
    
    
    $("#buttonPanel").find("#btnAdd").click(function(){
    	var $div = $("#div_insertC900M01I");
    	if(true){
    		$div.find("#label_orgCustIdDup").val(i18n.cls2701v01['C900M01I.orgCustId']);
        	$div.find("#label_custIdDup").val(i18n.cls2701v01['C900M01I.custId']);
        	$div.find("#label_docDate").val(i18n.cls2701v01['C900M01I.docDate']);
        	$div.find("#label_docNo").val(i18n.cls2701v01['C900M01I.docNo']);	
    	}
    	var $form = $("#C900M01IForm");
    	if(true){
    		$form.injectData({
    			  'orgDupNo':'0'
    			, 'dupNo':'0'
    			, 'docNo':'金徵(業)字第號'
    		});
    	}
    	$div.thickbox({
	        title: "",
	        width: 540,
	        height: 260,
	        modal: true,
	        i18n: i18n.def,
	        buttons: {
	            "sure": function(){				
					
                    if (!$form.valid()) {
                        return false
                    }
							
	                CommonAPI.confirmMessage("是否確定要執行", function(b){
							
			            if (b) {
			                $.ajax({
					            type: "POST",
					            handler: "cls2701formhandler",
					            data: $.extend({
					                formAction: "insertC900M01I"									
					            }, $form.serializeData() ),
					            success: function(json){
					            	$.thickbox.close();	
					            	grid.trigger("reloadGrid");
					            }
					        });		 
			            }else{
			            	$.thickbox.close();	
			            }
			        });
					
	            },
	            "close": function(){
	                $.thickbox.close();
	            }
	        }
	    });
    }).end().find("#btnDelete").click(function(){
    	var rows = grid.getGridParam('selrow');
        var oid = "";
        if (rows != 'undefined' && rows != null && rows != 0) {
            var data = grid.getRowData(rows);
            oid = data.oid;
        }
        if (oid == "") {
            CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
            return;
        }
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                $.ajax({
                    handler: "cls2701formhandler",
                    type: "POST",
                    dataType: "json",
                    data: {
                        'formAction': 'deleteC900M01I',
                        'oid': oid
                    },
                    success: function(obj){
                    	API.showMessage(i18n.def.runSuccess);//執行成功
                    	grid.trigger("reloadGrid");
                    }
                });
            }
        });	
    });
});
