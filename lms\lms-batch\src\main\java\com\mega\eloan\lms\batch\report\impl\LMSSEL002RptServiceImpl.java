package com.mega.eloan.lms.batch.report.impl;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.inet.report.Engine;
import com.inet.report.ReportException;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.report.AbstractIISIReportService;
import com.mega.eloan.lms.batch.report.LMSSEL002RptService;
import com.mega.eloan.lms.dao.C122S01EDao;
import com.mega.eloan.lms.model.C122S01E;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;

/**
 * eloan線上貸款申貸資料查詢紀錄報表 日報表
 * SLMS-00147
 * 
 * <AUTHOR>
 * 
 */
@Service("lmssel002rptservice")
public class LMSSEL002RptServiceImpl extends AbstractIISIReportService
		implements LMSSEL002RptService {

	@Resource
	C122S01EDao c122s01eDao;
	@Resource
	CodeTypeService codeTypeService;
	@Resource
	UserInfoService userInfoService;

	@Override
	public ReportData getReportParameter(PageParameters params, ReportData reportData,
			Engine engine) {

		try {
			String reportDate = params.get("reportDate").toString();
			Calendar start = Calendar.getInstance();
			Calendar end = Calendar.getInstance();
			// 沒帶年月日參數，使用系統昨日日期
			if (!CapString.isEmpty(reportDate)) {
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
				Date date = sdf.parse(reportDate);
				Calendar cal = Calendar.getInstance();
				cal.setTime(date);
			} else {
				Timestamp nowTime = CapDate.getCurrentTimestamp();
				start.setTime(nowTime);
				end.setTime(nowTime);
				start.add(Calendar.DATE, -1);
				end.add(Calendar.DATE, -1);
			}
			start.set(Calendar.HOUR_OF_DAY, 0);
			start.set(Calendar.MINUTE, 0);
			start.set(Calendar.SECOND, 0);
			start.set(Calendar.MILLISECOND, 0);
			end.set(Calendar.HOUR_OF_DAY, 23);
			end.set(Calendar.MINUTE, 59);
			end.set(Calendar.SECOND, 59);
			end.set(Calendar.MILLISECOND, 999);
			Timestamp startTime = new Timestamp(start.getTimeInMillis());
			Timestamp endTime = new Timestamp(end.getTimeInMillis());
			List<C122S01E> list = c122s01eDao.findByMainIdUserIdIp(null, null,
					null, startTime, endTime);
			Map<String, String> codeMap = codeTypeService
					.findByCodeType("C122S01E_queryReason");
			List<List<String>> details = new ArrayList<List<String>>();
			for (C122S01E c122s01e : list) {
				// 系統名稱
				String sys = "eloan";
				// 業務項目
				String applyKind = getApplyKindDESC(c122s01e.getApplyKind());
				// 查詢時間
				String queryTime = CapDate.convertTimestampToString(
						c122s01e.getQueryTime(),
						UtilConstants.DateFormat.YYYY_MM_DD_HH_MM_SS);
				// 使用者及IP
				String userId = c122s01e.getUserId();
				String userName = userInfoService.getUserName(userId);
				String userIp = c122s01e.getUserIp();
				// 查詢理由代碼
				String queryReason = CapString.trimNull(codeMap.get(c122s01e
						.getQueryReason()));
				List<String> detail = new ArrayList<String>();
				detail.add(sys);
				detail.add(applyKind);
				detail.add(queryTime);
				detail.add(userId + " " + userName + " " + userIp);
				detail.add(queryReason);
				details.add(detail);
			}
			reportData.addDetail(details);
			reportData.setField("printDate",
					CapDate.getCurrentDate("yyyy-MM-dd"));
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

		return reportData;

	}
	
	private String getApplyKindDESC(String applyKind) {
		if (UtilConstants.C122_ApplyKind.H.equals(applyKind)) {
			return "房貸增貸";
		} else if (UtilConstants.C122_ApplyKind.C.equals(applyKind)) {
			return "線上信貸";
		} else if (UtilConstants.C122_ApplyKind.B.equals(applyKind)) {
			return "勞工紓困";
		} else if (UtilConstants.C122_ApplyKind.D.equals(applyKind)) {
			return "勞工紓困";
		} else if (UtilConstants.C122_ApplyKind.P.equals(applyKind)) {
			return "線上信貸";
		} else if (UtilConstants.C122_ApplyKind.Q.equals(applyKind)) {
			return "線上信貸";
		} else if (UtilConstants.C122_ApplyKind.E.equals(applyKind)) {
			return "線上房貸";
		} else if (UtilConstants.C122_ApplyKind.F.equals(applyKind)) {
			return "線上房貸";
		}
		return "";
	}

	@Override
	public String getReportDefinition() {
		return "report/lms/LLSEL002";
	}

	@Override
	public byte[] getContent(PageParameters params) throws CapException,
			FileNotFoundException, ReportException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
			baos = (ByteArrayOutputStream) generateReport(params);
			return baos.toByteArray();
		} finally {
			if (baos != null) {
				baos.close();
			}
		}
	}

}
