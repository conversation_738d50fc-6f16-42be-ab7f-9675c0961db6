package com.mega.eloan.lms.eloandb.service.impl;

import javax.annotation.Resource;
import javax.sql.DataSource;

import tw.com.iisi.cap.context.CapParameter;

import com.mega.eloan.common.exception.GWException;
import com.mega.eloan.common.jdbc.EloanJdbcTemplate;

public class AbstractEloandbFsJdbc {
	private EloanJdbcTemplate jdbc;

	@Resource(name = "eLoanFsSqlStatement")
	CapParameter lmsSQL;

	@Resource(name = "lms-db")
	public void setDataSource(DataSource dataSource) {
		jdbc = new EloanJdbcTemplate(dataSource, GWException.GWTYPE_MFALOAN);
		jdbc.setSqlProvider(lmsSQL);
		jdbc.setCauseClass(this.getClass());
	}

	/**
	 * get the the jdbc
	 * 
	 * @return the jdbc
	 */
	public EloanJdbcTemplate getJdbc() {
		return jdbc;
	}
}
