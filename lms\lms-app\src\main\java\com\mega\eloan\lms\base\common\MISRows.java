package com.mega.eloan.lms.base.common;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import net.sf.cglib.beans.BeanMap;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

public class MISRows<T> extends BeanRows<T> {
	private static Logger logger = LoggerFactory.getLogger(MISRows.class);
	
	public static String NULL_DATE_STR = "0001-01-01";
	private static HashSet<String> COLUMN_DW_RKSCORE = new HashSet<String>();
	static{
		COLUMN_DW_RKSCORE.add("CC12_REVOL_BAL");
		COLUMN_DW_RKSCORE.add("CC12_REVOL_LIMIT");
		COLUMN_DW_RKSCORE.add("R01_CC12_MAX_REVOL_RATE");
		COLUMN_DW_RKSCORE.add("R01_CC12_MAX_REVOL_RATE_SCORE");
		COLUMN_DW_RKSCORE.add("CC_REVOL_BAL");
		COLUMN_DW_RKSCORE.add("CC_REVOL_PERMIT_LIMIT");
		COLUMN_DW_RKSCORE.add("R10_REVOL_RATE");
		COLUMN_DW_RKSCORE.add("R10_REVOL_RATE_SCORE");
		COLUMN_DW_RKSCORE.add("D07_LN_NOS_TAMT");
		COLUMN_DW_RKSCORE.add("D07_LN_NOS_TAMT_SCORE");
		COLUMN_DW_RKSCORE.add("D12_CC12_MAX_AVGLIMIT");
		COLUMN_DW_RKSCORE.add("D12_CC12_MAX_AVGLIMIT_SCORE");
		COLUMN_DW_RKSCORE.add("D15_CC6_AVG_RC");
		COLUMN_DW_RKSCORE.add("D15_CC6_AVG_RC_SCORE");
		COLUMN_DW_RKSCORE.add("P19_CC12_PCODE_A_TIMES");
		COLUMN_DW_RKSCORE.add("P19_CC12_PCODE_A_TIMES_SCORE");
		COLUMN_DW_RKSCORE.add("MARRIAGE");
		COLUMN_DW_RKSCORE.add("CHILDREN");
		COLUMN_DW_RKSCORE.add("MARRIAGE_SCORE");
		COLUMN_DW_RKSCORE.add("OCCUPATION_1");
		COLUMN_DW_RKSCORE.add("OCCUPATION_1_SCORE");
		COLUMN_DW_RKSCORE.add("COLL_3");
		COLUMN_DW_RKSCORE.add("COLL_3_SCORE");
		COLUMN_DW_RKSCORE.add("EDUCATION");
		COLUMN_DW_RKSCORE.add("EDUCATION_SCORE");
		COLUMN_DW_RKSCORE.add("N11_INQ3_LN_BANK");
		COLUMN_DW_RKSCORE.add("N11_INQ3_LN_BANK_SCORE");
		COLUMN_DW_RKSCORE.add("N06_INQ12_NAPP_BANK");
		COLUMN_DW_RKSCORE.add("N06_INQ12_NAPP_BANK_SCORE");
		COLUMN_DW_RKSCORE.add("H01_CC_MAX_MONTH");
		COLUMN_DW_RKSCORE.add("H01_CC_MAX_MONTH_SCORE");
		COLUMN_DW_RKSCORE.add("D33_LN6_AVG_NOS_AMT");
		COLUMN_DW_RKSCORE.add("C01_LN6_TAMT_ADDRATIO");
		COLUMN_DW_RKSCORE.add("D01_LN_TAMT");
		COLUMN_DW_RKSCORE.add("LN6_6TH_TAMT");
		COLUMN_DW_RKSCORE.add("D33_C01_SCORE");
		COLUMN_DW_RKSCORE.add("RATING_DATE");
		COLUMN_DW_RKSCORE.add("DOCSTATUS");
		COLUMN_DW_RKSCORE.add("FINAL_RATING_DATE");
		COLUMN_DW_RKSCORE.add("R01_CC12_REVOL_RATE");
		COLUMN_DW_RKSCORE.add("R01_CC12_REVOL_RATE_SCORE");
		COLUMN_DW_RKSCORE.add("HINCOME_REG");
		COLUMN_DW_RKSCORE.add("HINCOME_REG_SCORE");
		COLUMN_DW_RKSCORE.add("P69_CC12_DELAY_RC_TIMES");
		COLUMN_DW_RKSCORE.add("P25_CC6_PCODE_A_TIMES");
		COLUMN_DW_RKSCORE.add("P25_CC6_PCODE_A_TIMES_SCORE");
		COLUMN_DW_RKSCORE.add("MARR_EDU_SCORE");
		COLUMN_DW_RKSCORE.add("P69_P19_SCORE");
		COLUMN_DW_RKSCORE.add("YPAY");
		COLUMN_DW_RKSCORE.add("YPAY_SCORE");
		COLUMN_DW_RKSCORE.add("SENIORITY");
		COLUMN_DW_RKSCORE.add("SENIORITY_SCORE");
		COLUMN_DW_RKSCORE.add("EDUCATION_N_SCORE");
		COLUMN_DW_RKSCORE.add("D63_LN_NOS_BANK");
		COLUMN_DW_RKSCORE.add("D63_SCORE");
		COLUMN_DW_RKSCORE.add("A21_CC6_RC_USE_MONTH");
		COLUMN_DW_RKSCORE.add("A21_SCORE");
		COLUMN_DW_RKSCORE.add("A11_CC6_RC_USE_BANK");
		COLUMN_DW_RKSCORE.add("A11_SCORE");
		COLUMN_DW_RKSCORE.add("D53_LN_6_TIMES_FLAG");
		COLUMN_DW_RKSCORE.add("D53_SCORE");
		COLUMN_DW_RKSCORE.add("PINCOME");
		COLUMN_DW_RKSCORE.add("PINCOME_SCORE");
		COLUMN_DW_RKSCORE.add("P68_CC6_DELAY_RC_TIMES");
		COLUMN_DW_RKSCORE.add("P68_P19_SCORE");
		COLUMN_DW_RKSCORE.add("N18_INQ12_BY30D");
		COLUMN_DW_RKSCORE.add("N18_SCORE");
		COLUMN_DW_RKSCORE.add("D07_DIV_PINCOME");
		COLUMN_DW_RKSCORE.add("D07_DIV_PINCOME_SCORE");
		COLUMN_DW_RKSCORE.add("Z03_ACC_DEBT_ENTERPRISE");
		COLUMN_DW_RKSCORE.add("Z03_SCORE");
		COLUMN_DW_RKSCORE.add("DRATE_SCORE");
		COLUMN_DW_RKSCORE.add("DESIGNATION_SCORE");
		COLUMN_DW_RKSCORE.add("D42_SCORE");
		COLUMN_DW_RKSCORE.add("P68_SCORE");
		COLUMN_DW_RKSCORE.add("YRATE_SCORE");
		COLUMN_DW_RKSCORE.add("N01_SCORE");
		COLUMN_DW_RKSCORE.add("DESIGNATION");
		COLUMN_DW_RKSCORE.add("D42_AVG_LIMIT");
		COLUMN_DW_RKSCORE.add("N01_INQ3_TOTAL");
		//消金非房貸4.0
		COLUMN_DW_RKSCORE.add("P1_O_COUNTS");
		COLUMN_DW_RKSCORE.add("P1_SCORE");
		COLUMN_DW_RKSCORE.add("LOAN_BAL_S_BYID");
		COLUMN_DW_RKSCORE.add("LOAN_BAL_S_BYIDSCORE");
		//消金房貸2.1
		COLUMN_DW_RKSCORE.add("N22_INQ12_BY30D");
		COLUMN_DW_RKSCORE.add("N22_SCORE");
		
	}
	
	private static HashSet<String> COLUMN_DW_RKJCIC = new HashSet<String>();
	static{
		COLUMN_DW_RKJCIC.add("LN12_PAY_DELAY_TIMES");
		COLUMN_DW_RKJCIC.add("CC12_REVOL_PAY_DELAY_TIMES");
		COLUMN_DW_RKJCIC.add("CC12_MINPAY_DELAY_TIMES");	
		COLUMN_DW_RKJCIC.add("CC12_TOTPAY_DELAY_TIMES");
		COLUMN_DW_RKJCIC.add("CC12_CASH_ADV_TIMES");
		COLUMN_DW_RKJCIC.add("LN12_CASH_TIMES");
	}
	
	private static HashSet<String> COLUMN_DW_RKAPPLICANT = new HashSet<String>();
	static{
		COLUMN_DW_RKAPPLICANT.add("EDUCATION");
		COLUMN_DW_RKAPPLICANT.add("MARRIAGE");
		COLUMN_DW_RKAPPLICANT.add("CHILDREN");
		COLUMN_DW_RKAPPLICANT.add("HINCOME");
		COLUMN_DW_RKAPPLICANT.add("DRATE");
		COLUMN_DW_RKAPPLICANT.add("YRATE");
		COLUMN_DW_RKAPPLICANT.add("FRATE"); 
		COLUMN_DW_RKAPPLICANT.add("INVMBAL");      
		COLUMN_DW_RKAPPLICANT.add("INVOBAL");
		COLUMN_DW_RKAPPLICANT.add("ODEP");
		COLUMN_DW_RKAPPLICANT.add("PRIMARY_CARD");
		COLUMN_DW_RKAPPLICANT.add("ADDITIONAL_CARD");      
		COLUMN_DW_RKAPPLICANT.add("BUSINESS_OR_P_CARD");
		COLUMN_DW_RKAPPLICANT.add("TOTAL_CAPITAL");
		COLUMN_DW_RKAPPLICANT.add("PAIDUP_CAPITAL");
		COLUMN_DW_RKAPPLICANT.add("FINCOME");
	}
	private static HashSet<String> COLUMN_DW_RKCREDIT = new HashSet<String>();
	static{
		COLUMN_DW_RKCREDIT.add("SPR");
		COLUMN_DW_RKCREDIT.add("J10_SCORE");
		COLUMN_DW_RKCREDIT.add("JR_AUTODG");
		COLUMN_DW_RKCREDIT.add("BASE_A");
		COLUMN_DW_RKCREDIT.add("BASE_B");
		COLUMN_DW_RKCREDIT.add("BASE_S");
		COLUMN_DW_RKCREDIT.add("BASE_SCORE");
		COLUMN_DW_RKCREDIT.add("SLOPE");
		COLUMN_DW_RKCREDIT.add("INTERCEPT");
		COLUMN_DW_RKCREDIT.add("PRODID");
	}
	//----------------------
	private static HashSet<String> COLUMN_OTS_RKCREDITOVS = new HashSet<String>();
	static{
		COLUMN_OTS_RKCREDITOVS.add("J10_SCORE");
		COLUMN_OTS_RKCREDITOVS.add("SPR");
		COLUMN_OTS_RKCREDITOVS.add("JSPTS");
		COLUMN_OTS_RKCREDITOVS.add("GWS_DG");
		COLUMN_OTS_RKCREDITOVS.add("SWS_DG");
		COLUMN_OTS_RKCREDITOVS.add("OI_UG");
		COLUMN_OTS_RKCREDITOVS.add("JR_AUTODG");
		COLUMN_OTS_RKCREDITOVS.add("SWS_RATING_CAP");
		COLUMN_OTS_RKCREDITOVS.add("UI_DG");
		//日本模型2.0
		COLUMN_OTS_RKCREDITOVS.add("MOWTYPE2");
		COLUMN_OTS_RKCREDITOVS.add("SLOPE");
		COLUMN_OTS_RKCREDITOVS.add("INTERCEPT");
		COLUMN_OTS_RKCREDITOVS.add("DR");
		COLUMN_OTS_RKCREDITOVS.add("ADJ_RATING");
		COLUMN_OTS_RKCREDITOVS.add("CORE_STDSCORE");
		COLUMN_OTS_RKCREDITOVS.add("PREDICT_BAD_RATE");
		COLUMN_OTS_RKCREDITOVS.add("MOWTYPE_COUNTRY");
	}
	
	private static HashSet<String> COLUMN_OTS_RKSCOREOVS = new HashSet<String>();
	static{
		COLUMN_OTS_RKSCOREOVS.add("M1_AGE");
		COLUMN_OTS_RKSCOREOVS.add("M5_OCCUPATION");
		COLUMN_OTS_RKSCOREOVS.add("M7_SENIORITY");
		COLUMN_OTS_RKSCOREOVS.add("D1_ICR");
		COLUMN_OTS_RKSCOREOVS.add("P2_PINCOME");
		COLUMN_OTS_RKSCOREOVS.add("P3_HINCOME");
		COLUMN_OTS_RKSCOREOVS.add("A5_LOAN_PERIOD");
		COLUMN_OTS_RKSCOREOVS.add("O1_VEDASCORE");
		COLUMN_OTS_RKSCOREOVS.add("Z1");
		COLUMN_OTS_RKSCOREOVS.add("Z2");
		COLUMN_OTS_RKSCOREOVS.add("YPAY_EX_RATE");
		COLUMN_OTS_RKSCOREOVS.add("OMONEY_AMT_EX_RATE");
		COLUMN_OTS_RKSCOREOVS.add("HINCOME_EX_RATE");
		COLUMN_OTS_RKSCOREOVS.add("M1_SCORE");
		COLUMN_OTS_RKSCOREOVS.add("M5_SCORE");
		COLUMN_OTS_RKSCOREOVS.add("M7_SCORE");
		COLUMN_OTS_RKSCOREOVS.add("D1_SCORE");
		COLUMN_OTS_RKSCOREOVS.add("P2_SCORE");
		COLUMN_OTS_RKSCOREOVS.add("P3_SCORE");
		COLUMN_OTS_RKSCOREOVS.add("A5_SCORE");
		COLUMN_OTS_RKSCOREOVS.add("O1_SCORE");
		COLUMN_OTS_RKSCOREOVS.add("Z1_SCORE");
		COLUMN_OTS_RKSCOREOVS.add("Z2_SCORE");
		COLUMN_OTS_RKSCOREOVS.add("M1_STD_SCORE");
		COLUMN_OTS_RKSCOREOVS.add("M5_STD_SCORE");
		COLUMN_OTS_RKSCOREOVS.add("M7_STD_SCORE");
		COLUMN_OTS_RKSCOREOVS.add("D1_STD_SCORE");
		COLUMN_OTS_RKSCOREOVS.add("P2_STD_SCORE");
		COLUMN_OTS_RKSCOREOVS.add("P3_STD_SCORE");
		COLUMN_OTS_RKSCOREOVS.add("A5_STD_SCORE");
		COLUMN_OTS_RKSCOREOVS.add("O1_STD_SCORE");
		COLUMN_OTS_RKSCOREOVS.add("Z1_STD_SCORE");
		COLUMN_OTS_RKSCOREOVS.add("Z2_STD_SCORE");
		COLUMN_OTS_RKSCOREOVS.add("CORE_STDSCORE");		
		COLUMN_OTS_RKSCOREOVS.add("P3_TH_TOTINCOME");
		COLUMN_OTS_RKSCOREOVS.add("P4_TH_DRATE");
		COLUMN_OTS_RKSCOREOVS.add("Z3_TH_SECURITY_RATIO");
		COLUMN_OTS_RKSCOREOVS.add("P3_TH_SCORE");
		COLUMN_OTS_RKSCOREOVS.add("P4_TH_SCORE");
		COLUMN_OTS_RKSCOREOVS.add("Z3_TH_SCORE");
		COLUMN_OTS_RKSCOREOVS.add("P3_TH_STD_SCORE");
		COLUMN_OTS_RKSCOREOVS.add("P4_STD_SCORE");
		COLUMN_OTS_RKSCOREOVS.add("Z3_TH_STD_SCORE");
		//日本模型2.0
		COLUMN_OTS_RKSCOREOVS.add("EDU_ITEM");
		COLUMN_OTS_RKSCOREOVS.add("EDU_SCORE");
		COLUMN_OTS_RKSCOREOVS.add("EDU_WEIGHT_SCORE");
		COLUMN_OTS_RKSCOREOVS.add("DRATE_WEIGHT_SCORE");
		COLUMN_OTS_RKSCOREOVS.add("M1_WEIGHT_SCORE");
		COLUMN_OTS_RKSCOREOVS.add("M7_WEIGHT_SCORE");
		COLUMN_OTS_RKSCOREOVS.add("A5_WEIGHT_SCORE");
		COLUMN_OTS_RKSCOREOVS.add("M5_WEIGHT_SCORE");
		COLUMN_OTS_RKSCOREOVS.add("P2_WEIGHT_SCORE");
		COLUMN_OTS_RKSCOREOVS.add("Z1_WEIGHT_SCORE");
		COLUMN_OTS_RKSCOREOVS.add("Z2_WEIGHT_SCORE");
		COLUMN_OTS_RKSCOREOVS.add("MOWTYPE2");
		COLUMN_OTS_RKSCOREOVS.add("MOWTYPE_COUNTRY");
		//其他地區模型
		COLUMN_OTS_RKSCOREOVS.add("P3_WEIGHT_SCORE");
	}
	
	private static HashSet<String> COLUMN_OTS_RKJCICOVS = new HashSet<String>();
	static{
		COLUMN_OTS_RKJCICOVS.add("CASH_AMT");
		COLUMN_OTS_RKJCICOVS.add("NOS_AMT");
		COLUMN_OTS_RKJCICOVS.add("SL_AMT");
		COLUMN_OTS_RKJCICOVS.add("CC12_REVOL_PAY_DELAY_TIMES");
		COLUMN_OTS_RKJCICOVS.add("CC12_MINPAY_DELAY_TIMES");
		COLUMN_OTS_RKJCICOVS.add("CC12_TOTPAY_DELAY_TIMES");
		COLUMN_OTS_RKJCICOVS.add("CC12_CASH_ADV_TIMES");
		COLUMN_OTS_RKJCICOVS.add("INQ3_NAPP_BANK");
		COLUMN_OTS_RKJCICOVS.add("J10_SCORE");
		COLUMN_OTS_RKJCICOVS.add("VEDA_CREDIT_ENQUIRIES");
		COLUMN_OTS_RKJCICOVS.add("VEDA_CREDIT_FILE_AGE");
		COLUMN_OTS_RKJCICOVS.add("NCB_OVER_90_DAYS");
		COLUMN_OTS_RKJCICOVS.add("NCB_ENQ_RECENT_6M");
		//日本模型2.0
		COLUMN_OTS_RKJCICOVS.add("MOWTYPE2");
		COLUMN_OTS_RKJCICOVS.add("MOWTYPE_COUNTRY");
	}
	
	private static HashSet<String> COLUMN_OTS_RKAPPLICANTOVS = new HashSet<String>();
	static{
		COLUMN_OTS_RKAPPLICANTOVS.add("EDUCATION");
		COLUMN_OTS_RKAPPLICANTOVS.add("MARRIAGE");
		COLUMN_OTS_RKAPPLICANTOVS.add("CHILDREN");
		COLUMN_OTS_RKAPPLICANTOVS.add("YPAY"); 
		COLUMN_OTS_RKAPPLICANTOVS.add("YPAY_EX_RATE");
		COLUMN_OTS_RKAPPLICANTOVS.add("OMONEY_AMT");
		COLUMN_OTS_RKAPPLICANTOVS.add("OMONEY_AMT_EX_RATE");
		COLUMN_OTS_RKAPPLICANTOVS.add("HINCOME");
		COLUMN_OTS_RKAPPLICANTOVS.add("HINCOME_EX_RATE");
		COLUMN_OTS_RKAPPLICANTOVS.add("RINCOME");
		COLUMN_OTS_RKAPPLICANTOVS.add("RINCOME_EX_RATE");
		COLUMN_OTS_RKAPPLICANTOVS.add("DRATE");
		COLUMN_OTS_RKAPPLICANTOVS.add("YRATE");
		COLUMN_OTS_RKAPPLICANTOVS.add("INVMBAL");
		COLUMN_OTS_RKAPPLICANTOVS.add("INVMBAL_EX_RATE");
		COLUMN_OTS_RKAPPLICANTOVS.add("INVOBAL");
		COLUMN_OTS_RKAPPLICANTOVS.add("INVOBAL_EX_RATE");
		COLUMN_OTS_RKAPPLICANTOVS.add("ODEP");
		COLUMN_OTS_RKAPPLICANTOVS.add("ODEP_EX_RATE");
		COLUMN_OTS_RKAPPLICANTOVS.add("MONTH_TOTAL_INCOME");
		COLUMN_OTS_RKAPPLICANTOVS.add("MONTH_TOT_INCOME_EX_RATE");
		COLUMN_OTS_RKAPPLICANTOVS.add("MONTH_TOTAL_EXPENSE");
		COLUMN_OTS_RKAPPLICANTOVS.add("MONTH_TOT_EXPENSE_EX_RATE");
		//日本模型2.0
		COLUMN_OTS_RKAPPLICANTOVS.add("MOWTYPE2");
		COLUMN_OTS_RKAPPLICANTOVS.add("MOWTYPE_COUNTRY");
	}
	
	private static HashSet<String> COLUMN_OTS_RKCOLLOVS = new HashSet<String>();
	static{
		COLUMN_OTS_RKCOLLOVS.add("SECURITY_RATE");
		//日本模型2.0
		COLUMN_OTS_RKCOLLOVS.add("MOWTYPE2");
		COLUMN_OTS_RKCOLLOVS.add("MOWTYPE_COUNTRY");
		
	}

	private static HashSet<String> COLUMN_DW_ESG = new HashSet<String>();
	static{
		COLUMN_DW_ESG.add("ELF606_ESGSCORE");
		COLUMN_DW_ESG.add("ELF606_ESGLEVEL");
		COLUMN_DW_ESG.add("ELF606_ISSLEVEL1");
		COLUMN_DW_ESG.add("ELF606_ISSLEVEL2");
		COLUMN_DW_ESG.add("ELF606_SPSCORE");
		COLUMN_DW_ESG.add("ELF606_MOODY");
	}
	//----------------------
	public MISRows(Class<T> clazz) {
		super(clazz);
	}

	@Override
	protected Object getValue(RowColumn col, Object obj) {
		Object _obj = obj;
		switch (col.getType()) {
		case CMSTypes.DECIMAL:
			if (_obj == null) {				
				if(("DW_RKSCORE".equals(StringUtils.upperCase(getTableNm())) || "DW_RKSCORE_N".equals(StringUtils.upperCase(getTableNm()))) 
						&& COLUMN_DW_RKSCORE.contains(StringUtils.upperCase(col.getName()))){
				}else if("DW_RKJCIC".equals(StringUtils.upperCase(getTableNm()))
						&& COLUMN_DW_RKJCIC.contains(StringUtils.upperCase(col.getName()))){
				}else if(("DW_RKAPPLICANT".equals(StringUtils.upperCase(getTableNm())) || "DW_RKAPPLICANT_N".equals(StringUtils.upperCase(getTableNm())))
						&& COLUMN_DW_RKAPPLICANT.contains(StringUtils.upperCase(col.getName()))){
				}else if(("DW_RKCREDIT".equals(StringUtils.upperCase(getTableNm())) || "DW_RKCREDIT_N".equals(StringUtils.upperCase(getTableNm())))
						&& COLUMN_DW_RKCREDIT.contains(StringUtils.upperCase(col.getName()))){
				}else if("OTS_RKCREDITOVS".equals(StringUtils.upperCase(getTableNm()))
						&& COLUMN_OTS_RKCREDITOVS.contains(StringUtils.upperCase(col.getName()))){
				}else if("OTS_RKSCOREOVS".equals(StringUtils.upperCase(getTableNm()))
						&& COLUMN_OTS_RKSCOREOVS.contains(StringUtils.upperCase(col.getName()))){
				}else if("OTS_RKJCICOVS".equals(StringUtils.upperCase(getTableNm()))
						&& COLUMN_OTS_RKJCICOVS.contains(StringUtils.upperCase(col.getName()))){
				}else if("OTS_RKAPPLICANTOVS".equals(StringUtils.upperCase(getTableNm()))
						&& COLUMN_OTS_RKAPPLICANTOVS.contains(StringUtils.upperCase(col.getName()))){
				}else if("OTS_RKCOLLOVS".equals(StringUtils.upperCase(getTableNm()))
						&& COLUMN_OTS_RKCOLLOVS.contains(StringUtils.upperCase(col.getName()))){
				}else if("ELF606".equals(StringUtils.upperCase(getTableNm()))
						&& COLUMN_DW_ESG.contains(StringUtils.upperCase(col.getName()))){
				}else if("ELF500".equals(StringUtils.upperCase(getTableNm()))
						&& "ELF500_OLD_QUOTA".contains(StringUtils.upperCase(col.getName()))){// #J-111-0534：隨此欄位於畫面上被棄用，值不需被轉換為0
				}else{
					_obj = 0;	
				}
			}
			break;
		case CMSTypes.DATE:
			// nothing to do....
			if (this.isAS400) {
				if (_obj == null) {
					_obj = MISRows.NULL_DATE_STR;
				}
			}
			break;
		case CMSTypes.DATETYPE1:
			if (_obj != null) {
				_obj = Util.addZeroWithValue(
						TWNDate.valueOf((Date) _obj).toAD('-'), 10);
			} else {
				_obj = "";
			}
			break;
		case CMSTypes.DATETYPE2:
			if (_obj != null) {
				_obj = TWNDate.valueOf((Date) _obj).toAD();
			} else {
				_obj = "";
			}
			break;
		case CMSTypes.DATETYPE3:
			// if (_obj != null) {
			// TWNDate d = TWNDate.valueOf((Date) _obj);
			//
			// // 上傳mis如日期大於最大2910/12/31 (999/12/31) 一律上傳 2910/12/31
			// if (d.compareTo(CMSUtil.MAX_TWNDate) > 0) {
			// d = CMSUtil.MAX_TWNDate;
			// }
			// _obj = d.toTW();
			//
			// // 日期如果小於 1911-01-01 則轉空白 (因為西元扣掉1911後會變成負的)
			// if (d.compareTo(CMSUtil.MIN_TWNDate) < 0) {
			// _obj = "";
			// }
			//
			// } else {
			// _obj = "";
			// }
			break;
		case CMSTypes.DATETYPE4:
			if (_obj != null) {
				_obj = TWNDate.valueOf((Date) _obj).toAD('/');
			} else {
				_obj = "";
			}
			break;
		case CMSTypes.DATETYPE5:
			if (this.isAS400) {
				// as400專用欄位...故加上此檢核
				if (_obj != null) {
					_obj = TWNDate.valueOf((Date) _obj).getAS400Timestamp();
				} else {
					_obj = "";
				}
			} else {
				if (_obj != null) {
					_obj = TWNDate.toFullAD((Date) _obj);
				}
			}
			break;
		case CMSTypes.DATETYPE6:
			if (_obj != null) {
				_obj = TWNDate.valueOf((Date) _obj).toAD('-');
			} else {
				_obj = MISRows.NULL_DATE_STR;
			}
			break;
		case CMSTypes.TIMESTAMP:
			if (this.isAS400) {
				// as400專用欄位...故加上此檢核
				if (_obj != null) {
					_obj = TWNDate.valueOf((Date) _obj).getAS400Timestamp();
				} else {
					_obj = "";
				}
			}
			break;
		default:
			if (_obj == null) {
				_obj = "";
			} else {
				if("ELF501".equals(StringUtils.upperCase(getTableNm()))
						&& "ELF501_TAXADDR".equals(StringUtils.upperCase(col.getName()))){
					//...
				}else if(("OTS_RKADJUSTOVS".equals(StringUtils.upperCase(getTableNm()))|| "DW_RKADJUST".equals(StringUtils.upperCase(getTableNm())) ) && 
						("UPGRADE_REASON_TEXT".equals(StringUtils.upperCase(col.getName())) || "DOWNGRADE_REASON_TEXT".equals(StringUtils.upperCase(col.getName())))){
					//...
				}else{
					_obj = Util.trimSizeInOS390((String) _obj, col.getLength());
				}
			}

			break;
		}
		//------
		//比照 EloanJdbcTemplate :: convertToSQLCommand
		if(_obj==null){			
		}else if(_obj instanceof String) {
		}else if(_obj instanceof Number || _obj instanceof BigDecimal) {
		}else if(_obj instanceof java.sql.Timestamp) {
		}else if(_obj instanceof java.sql.Time) {
		}else if(_obj instanceof java.sql.Date) {
		}else if(_obj instanceof java.util.Date){
			if(col.getType()==java.sql.Types.DATE){
				if (_obj instanceof java.sql.Date) {
					//not convert
				}else {
					_obj = new java.sql.Date(((java.util.Date) _obj).getTime());
				}
			}else{
				logger.info("????????????["+getTableNm()+"."+col.getName()+"][type=="+col.getType()+"]java.util.Date【"+_obj.getClass().getName()+"】"+_obj);	
			}
		}else{
			logger.error("????????????["+getTableNm()+"."+col.getName()+"][type=="+col.getType()+"]【"+_obj.getClass().getName()+"】"+_obj);
			if(_obj instanceof java.util.Calendar){
				logger.info("???????????? ---> java.util.Calendar");	
			}			
		}
		//------
		return _obj;
	}
	
	private int _matchIdx(String colName){
		int matchIdx = -1;
		for (int i = 0; i < cols.size(); i++) {
			if(StringUtils.upperCase(cols.get(i).getName()).equals(StringUtils.upperCase(colName))){
				matchIdx =i;
				break;
			}
		}
		return matchIdx;
	}
	public List<Object> getValuesWithAssignColumn(String colName){
		int matchIdx = _matchIdx(colName);
		List<Object> r = new ArrayList<Object>();
		if(matchIdx>=0){			
			for(Object[] entity: getValues()){
				r.add(entity[matchIdx]);	
			}
		}
		return r;
	}
	
	public List<Object[]> getValuesWithAssignColumns(String[] colNameArr){
		
		List<Object[]> r = new ArrayList<Object[]>();
		int length = colNameArr.length;
		if(length>0){
			Object[] r_row = new Object[length];
			for(int i=0;i<length;i++){
				r_row[i] = "[init]";
			}
			for(int i=0;i<length;i++){
				int matchIdx = _matchIdx(colNameArr[i]);
				if(matchIdx>=0){			
					for(Object[] entity: getValues()){
						r_row[i] = entity[matchIdx];	
					}
				}
			}
			r.add(r_row);
		}
		return r;
	}
	
	/**
	 * 判斷「欄位」對應不到
	 * ● RowColumn 抓 field 的[第1碼小寫][第2碼之後]
	 * ● BeanMap 抓 getter/setter 的[第1碼小寫][第2碼之後]
	 * [X] pIncome, getPIncome, setPIncome
	 * [O] pincome, getPincome, setPincome
	 */
	public String debug_setValues(List<T> list){
		for (T entry : list) {
			BeanMap bm = Util.getBeanMap(entry);
			if(true){				
				Set<String> colSet = new HashSet<String>();
				Set<String> bmSet = new HashSet<String>(); 
				for (int i = 0; i < cols.size(); i++) {
					RowColumn rowColumn = cols.get(i);
					colSet.add(rowColumn.getName());
				}
				for(Object k: bm.keySet()){
					bmSet.add(k.toString());
				}
				return ("left:"+LMSUtil.elm_onlyLeft(colSet, bmSet)
						+"right:"+LMSUtil.elm_onlyRight(colSet, bmSet)
						+"both:"+LMSUtil.elm_join(colSet, bmSet));
			}
		}
		return "";
	}
}
