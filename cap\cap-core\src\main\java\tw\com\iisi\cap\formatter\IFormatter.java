/**
 * IFormatter.java
 *
 * Copyright (c) 2009 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
*/
package tw.com.iisi.cap.formatter;

import java.io.Serializable;

import tw.com.iisi.cap.exception.CapFormatException;

/**
 * <p>
 * 格式化欄位值
 * </p>
 * 
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2010/1/8,iristu,new
 *          </ul>
 */
public interface IFormatter extends Serializable {

    /**
     * 格式化傳入的值
     * 
     * @param <T>
     * 
     * @param in
     *            the input
     * @return String
     * @throws CapFormatException
     */
    public <T> T reformat(Object in) throws CapFormatException;

}
