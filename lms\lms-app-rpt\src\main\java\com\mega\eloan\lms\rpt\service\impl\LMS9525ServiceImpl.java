/* 
 *  LMS9515ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.AbstractCapService;

import com.mega.eloan.common.dao.DocFileDao;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.lms.dao.L784A01ADao;
import com.mega.eloan.lms.dao.L784M01ADao;
import com.mega.eloan.lms.dao.L784S01ADao;
import com.mega.eloan.lms.dao.L784S07ADao;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.eloandb.service.Dw_elf411ovsService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.eloandb.service.Lms412Service;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.mfaloan.service.MisELF447Service;
import com.mega.eloan.lms.mfaloan.service.MisElcsecntService;
import com.mega.eloan.lms.mfaloan.service.MisEllnseekservice;
import com.mega.eloan.lms.mfaloan.service.MisLMS422Service;
import com.mega.eloan.lms.mfaloan.service.MisLNFE0851Service;
import com.mega.eloan.lms.model.L784M01A;
import com.mega.eloan.lms.model.L784S01A;
import com.mega.eloan.lms.model.L784S07A;
import com.mega.eloan.lms.rpt.service.LMS9525Service;
import com.mega.sso.service.BranchService;

@Service
public class LMS9525ServiceImpl extends AbstractCapService implements
		LMS9525Service {

	@Resource
	DocFileDao docFileDao;

	@Autowired
	DocFileService fileService;

	@Resource
	BranchService branchService;

	@Resource
	EloandbBASEService eloandbBaseService;

	@Resource
	TempDataService tempDataService;

	@Resource
	CodeTypeService codetypeService;

	@Resource
	Dw_elf411ovsService dwElf411ovsService;

	@Resource
	Lms412Service lms412Service;

	@Resource
	DwdbBASEService dwdbService;

	@Resource
	MisLMS422Service misLms422Service;

	@Resource
	MisCustdataService misCustdataService;

	@Resource
	MisLNFE0851Service misLnfe0851Service;

	@Resource
	MisElcsecntService misElcsecntService;

	@Resource
	MisEllnseekservice misEllnseekService;

	@Resource
	MisELF447Service misElf447Service;

	@Resource
	L784A01ADao l784a01aDao;

	@Resource
	L784M01ADao l784m01aDao;

	@Resource
	L784S07ADao l784s07aDao;

	@Resource
	L784S01ADao l784s01aDao;

	@Override
	public void save(GenericBean... entity) {
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L784S07A) {
					l784s07aDao.save((L784S07A) model);

				} else if (model instanceof L784S01A) {
					l784s01aDao.save((L784S01A) model);
				}
			}
		}
	}

	@Override
	public void delete(GenericBean... entity) {

	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == L784S07A.class) {
			return l784s07aDao.findPage(search);
		} else if (clazz == L784M01A.class) {
			return l784m01aDao.findPage(search);
		} else if (clazz == L784S01A.class) {
			return l784s01aDao.findPage(search);
		}
		return null;
	}

	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		if (clazz == L784S07A.class) {
			return (T) l784s07aDao.find(oid);
		} else if (clazz == L784S01A.class) {
			return (T) l784s01aDao.find(oid);
		}
		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		return null;
	}

}
