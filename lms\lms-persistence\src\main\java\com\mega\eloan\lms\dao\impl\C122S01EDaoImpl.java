/* 
 * C122S01EDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.sql.Timestamp;
import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.C122S01EDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C122S01E;

/** 線上貸款申貸資料查詢紀錄 **/
@Repository
public class C122S01EDaoImpl extends LMSJpaDao<C122S01E, String> implements
		C122S01EDao {

	@Override
	public C122S01E findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C122S01E> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<C122S01E> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<C122S01E> findByMainIdUserIdIp(String mainId, String userId,
			String userIp, Timestamp startTime, Timestamp endTime) {
		ISearch search = createSearchTemplete();
		if (mainId != null) {
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		}
		if (userIp != null) {
			search.addSearchModeParameters(SearchMode.EQUALS, "userIp", userIp);
		}
		if (userId != null) {
			search.addSearchModeParameters(SearchMode.EQUALS, "userId", userId);
		}
		search.addSearchModeParameters(SearchMode.GREATER_EQUALS, "queryTime",
				startTime);
		search.addSearchModeParameters(SearchMode.LESS_EQUALS, "queryTime",
				endTime);
		search.addOrderBy("applyKind");
		search.addOrderBy("queryTime");
		List<C122S01E> list = createQuery(search).getResultList();
		return list;
	}

}