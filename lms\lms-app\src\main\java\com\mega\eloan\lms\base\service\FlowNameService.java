/* 
 * FlowNameService.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.service;

/**
 * <pre>
 * flow name 英文名和中文轉換
 * </pre>
 * 
 * @since 2011/11/15
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/11/15,REX,new
 *          </ul>
 */
public interface FlowNameService {

	void init();

	// 退補件單位 backUnit
	public enum FlowbackUnitEnum {

		營運中心_退回分行("A"), 授管_退回分行("S"), 授管_退回營運("C");

		private String code;

		FlowbackUnitEnum(String code) {
			this.code = code;
		}

		public String getCode() {
			return code;
		}

	}

	// 分行撤件註-returnFromBH
	public enum FlowReturnEnum {

		撤件("1"), 陳復("2");

		private String code;

		FlowReturnEnum(String code) {
			this.code = code;
		}

		public String getCode() {
			return code;
		}

	}

	String getKey(String key);

}
