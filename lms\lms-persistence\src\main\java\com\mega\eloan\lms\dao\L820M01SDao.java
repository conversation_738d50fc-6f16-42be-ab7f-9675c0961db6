/* 
 * L820M01SDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L820M01S;

/** 以房養老綜合資訊檔 **/
public interface L820M01SDao extends IGenericDao<L820M01S> {

	L820M01S findByOid(String oid);
	
	List<L820M01S> findByMainId(String mainId);
	
	public List<L820M01S> findByList(String mainId, String custId, String dupNo, String dataType);
	
	L820M01S findByUniqueKey(String mainId, String custId, String dupNo, String dataType, String fileSeq);

	List<L820M01S> findByIndex01(String mainId, String custId, String dupNo, String dataType, String fileSeq);
	
	public List<L820M01S> findByIdDupDataType(String mainId, String custId, String dupNo, String dataType);
}