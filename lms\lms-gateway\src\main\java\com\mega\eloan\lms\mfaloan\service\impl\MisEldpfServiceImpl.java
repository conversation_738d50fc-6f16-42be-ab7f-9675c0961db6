/* 
 * MisEldpfService.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.mfaloan.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Calendar;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import tw.com.iisi.cap.util.CapMath;
import tw.com.iisi.cap.util.CapString;

import com.mega.eloan.lms.mfaloan.service.MisEldpfService;

/**
 * <pre>
 * MIS存款檔
 * </pre>
 * 
 * @since 2011/9/23
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/9/23,rodeschen,new
 *          </ul>
 */
@Service
public class MisEldpfServiceImpl extends AbstractMFAloanJdbc implements
		MisEldpfService {

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.ces.mfaloan.service.MisEldpfService#findABy(java.lang.
	 * String)
	 */
	@Override
	public BigDecimal findAVGByCustId(String custId) {
		Map<String, Object> result = getJdbc().queryForMap(
				"ELDPF.findAvgByCustId",
				new String[] { CapString.fillBlankTail(custId, 10) });
		if (result.get("AVG6M") != null) {
			return CapMath.getBigDecimal(result.get("AVG6M").toString());
		}
		return null;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.ces.mfaloan.service.MisEldpfService#findCurrentYearAvgByCustId
	 * (java.lang.String)
	 */
	@Override
	public Map<String, Object> findCurrentYearAvgByCustId(String custId) {
		Map<String, Object> result = getJdbc().queryForMap(
				"ELDPF.findCurrentYearAvgByCustId", new String[] { custId });

		int month = Calendar.getInstance().get(Calendar.MONTH);
		BigDecimal b = CapMath.getBigDecimal("0");
		String[] calMonth = new String[] { "NB01", "NB02", "NB03", "NB04",
				"NB05", "NB06", "NB07", "NB08", "NB09", "NB10", "NB11" };
		for (int i = 0; i < month; i++) {
			Object o = result.get(calMonth[i]);
			if (o != null) {
				b = b.add(CapMath.getBigDecimal(o.toString()));
			}
		}
		result.put("N", month);
		if (month == 0) {
			result.put("LM", 0);
		} else {
			result.put("LM",
					b.divide(new BigDecimal(month), 2, RoundingMode.HALF_UP));
		}
		return result;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.mega.eloan.ces.mfaloan.service.MisEldpfService#
	 * findCurrentYearAvgByCustId2(java.lang.String)
	 */
	@Override
	public Map<String, Object> findCurrentYearAvgByCustId2(String custId) {
		Map<String, Object> result = getJdbc().queryForMap(
				"ELDPF.findCurrentYearAvgByCustId2", new String[] { custId });

		int month = Calendar.getInstance().get(Calendar.MONTH);
		BigDecimal b = CapMath.getBigDecimal("0");
		String[] calMonth = new String[] { "NB01", "NB02", "NB03", "NB04",
				"NB05", "NB06", "NB07", "NB08", "NB09", "NB10", "NB11" };
		for (int i = 0; i < month; i++) {
			Object o = result.get(calMonth[i]);
			if (o != null) {
				b = b.add(CapMath.getBigDecimal(o.toString()));
			}
		}
		result.put("N", month);
		if (month == 0) {
			result.put("LM", 0);
		} else {
			result.put("LM",
					b.divide(new BigDecimal(month), 2, RoundingMode.HALF_UP));
		}
		return result;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.ces.mfaloan.service.MisEldpfService#findByCustId(java.lang.
	 * String)
	 */
	@Override
	public List<Map<String, Object>> findByCustId(String custId) {

		String[][] APCDCode = new String[][] {// 與現行存款種類對應
		{ "01", "01" }, { "06", "01" }, { "09", "01" }, { "02", "02" },
				{ "07", "02" }, { "08", "02" }, { "10", "02" }, { "12", "02" },
				{ "13", "02" }, { "14", "02" }, { "60", "02" }, { "61", "02" },
				{ "62", "02" }, { "63", "02" }, { "64", "02" }, { "65", "02" },
				{ "20", "03" }, { "21", "03" }, { "22", "03" }, { "27", "03" },
				{ "05", "08" }, { "15", "08" }, { "16", "08" }, { "17", "08" },
				{ "18", "08" }, { "53", "08" }, { "57", "08" }, { "30", "05" },
				{ "38", "05" }, { "42", "05" }, { "31", "05" }, { "39", "05" },
				{ "45", "05" }, { "46", "05" }, { "47", "05" }, { "48", "05" },
				{ "11", "09" }, { "19", "09" }, { "33", "10" }, { "43", "10" },
				{ "34", "11" }, { "44", "11" }, { "35", "12" }, { "36", "13" } };

		List<Map<String, Object>> result = getJdbc().queryForList(
				"MISDPF.findByCustId",
				new String[] { CapString.fillBlankTail(custId, 10) });

		if (!CollectionUtils.isEmpty(result)) {
			HashMap<String, String> hm = new HashMap<String, String>();
			for (String[] mapping : APCDCode) {
				hm.put(mapping[0], mapping[1]);
			}

			for (Map<String, Object> data : result) {
				if (hm.containsKey(data.get("DP_AP_CODE"))) {
					data.put("depType", hm.get(data.get("DP_AP_CODE")));
				} else {
					data.put("depType", "");
				}
			}
		}

		return result;
	}
}
