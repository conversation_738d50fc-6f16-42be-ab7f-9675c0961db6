/* 
 * MisELF501ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import tw.com.jcs.common.Util;

import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.mfaloan.bean.ELF501;
import com.mega.eloan.lms.mfaloan.service.MisELF501Service;

/**
 * <pre>
 * 消金額度介面檔 MIS.ELF501
 * </pre>
 * 
 * @since 2013/1/17
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/1/17,REX,new
 *          </ul>
 */
@Service
public class MisELF501ServiceImpl extends AbstractMFAloanJdbc implements
		MisELF501Service {

	@Override
	public List<ELF501> findByKey(String custId, String dupNo, String cntrNo) {

		List<Map<String, Object>> rowData = this.getJdbc().queryForList(
				"MIS.ELF501_findByKey", new Object[] { custId, dupNo, cntrNo });

		List<ELF501> list = new ArrayList<ELF501>();
		for (Map<String, Object> row : rowData) {
			ELF501 model = new ELF501();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}

	@Override
	public ELF501 findByUniqueKey1(String custId, String dupNo, String cntrNo,
			Integer seq) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForList(
				"MIS.ELF501_findByUniqueKey1",
				new Object[] { custId, dupNo, cntrNo, seq });
		if (rowData.size() > 0) {
			Map<String, Object> row = rowData.get(0);
			ELF501 model = new ELF501();
			DataParse.map2Bean(row, model);
			return model;
		} else {
			return null;
		}
	}

	@Override
	public ELF501 findByUniqueKey2(String custId, String dupNo, String cntrNo,
			String LoanNo) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForList(
				"MIS.ELF501_findByUniqueKey2",
				new Object[] { custId, dupNo, cntrNo, LoanNo });
		if (rowData.size() > 0) {
			Map<String, Object> row = rowData.get(0);
			ELF501 model = new ELF501();
			DataParse.map2Bean(row, model);
			return model;
		} else {
			return null;
		}
	}

	@Override
	public Map<String, Object> CheckProdKind58(String company_id) {
		return this.getJdbc().queryForMap("ElF500.CheckProdKind58",
				new Object[] { company_id });
	}

	@Override
	public Map<String, Object> checkProdKind60(String custId, String dupNo) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForList(
				"ElF501.CheckProdKind60",new Object[] { custId, dupNo });

		Map<String, Object> r = new HashMap<String, Object>(); 
		for (Map<String, Object> row : rowData) {
			r.put(Util.trim(row.get("ELF501_CNTRNO")), row.get("TOTAL"));
		}
		return r;
	}
}
