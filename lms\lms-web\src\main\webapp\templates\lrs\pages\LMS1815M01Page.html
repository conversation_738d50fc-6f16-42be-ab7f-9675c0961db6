<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
	<body>
		<th:block th:fragment="innerPageBody">
		<script type="text/javascript">
				loadScript('pagejs/lrs/LMS1815M01Page');
		</script>
		<div class="button-menu funcContainer" id="buttonPanel">
			<th:block th:if="${_btnDOC_EDITING_visible}">
				<button type="button" id="btnSearch" style="display:none;">
					<span class="ui-icon ui-icon-circle-zoomout" ></span>
					<th:block th:text="#{'button.search'}">查詢借款人</th:block>
				</button>
				<button type="button" id="btnSave">
					<span class="ui-icon ui-icon-jcs-04" ></span>
					<th:block th:text="#{'button.save'}">儲存</th:block>
				</button>
				<button type="button" id="btnSend">
					<span class="ui-icon ui-icon-jcs-02" ></span>
					<th:block th:text="#{'button.send'}">呈主管覆核</th:block>
				</button>
			</th:block>
			<th:block th:if="${_btnWAIT_APPROVE_visible}">
				<button type="button" id="btnAccept">
					<span class="ui-icon ui-icon-check" ></span>
					<th:block th:text="#{'button.accept'}">覆核</th:block>
				</button>
				<!-- 退回  -->
				<!-- <button id="btnReturn">
					<span class="ui-icon ui-icon-closethick" />
					<th:block th:text="#{'button.return'}">退回</th:block>
				</button> -->
			</th:block>
			<button type="button" id="btnCauculate">
				<span class="ui-icon ui-icon-jcs-12" ></span>
				<th:block th:text="#{'button.cauculate'}">試算下次覆審日</th:block>
			</button>
			<button type="button" id="btnState">
				<span class="ui-icon ui-icon-jcs-08" ></span>
				<th:block th:text="#{'button.state'}">查詢客戶最新戶況</th:block>
			</button>
			<button type="button" id="btnExit" class="forview">
				<span class="ui-icon ui-icon-jcs-01"></span>
				<th:block th:text="#{'button.exit'}">離開</th:block>
			</button>
		</div>
		<div class="tit2 color-black">
			<th:block th:text="#{'doc.title'}"></th:block><b>
			(<span class="color-red" id="titInfo"></span>)<span class="color-blue" id="custInfo"></span></b>
		</div>
		<div class="tabs doc-tabs">
			<ul>
				<li>
					<a href="#tab-01" goto="01">
					<b><th:block th:text="#{'doc.docinfo'}">文件資訊</th:block></b></a>
				</li>
				<li>
					<a href="#tab-02" goto="02">
					<b><th:block th:text="#{'doc.tit02'}">案件資訊</th:block></b></a>
				</li>
			</ul>
			<div class="tabCtx-warp">
			<div th:id="${tabID}" th:insert="~{${panelName} :: ${panelFragmentName}}"></div>
			</div>
		</div>
		<div id="Search" style="display: none">
			<form id="custInfoForm">
				<table width="100%" border="0" cellspacing="0" cellpadding="0">
					<tr>
						<td>
							<b><th:block th:text="#{'enterBranch'}">請輸入分行代碼</th:block>&nbsp;:&nbsp;</b>
							<input id="branch" class="upWord required" type="text" size="10" maxlength=3 />
						</td>
					</tr>
					<tr>
						<td>
							<b><th:block th:text="#{'enterCustId'}">請輸入借款人統一編號</th:block>&nbsp;:&nbsp;</b>
							<input id="custid" class="upWord required" type="text" size="10" maxlength=10 />
						</td>
					</tr>
					<tr>
						<td>
							<b><th:block th:text="#{'enterDupNo'}">含重覆序號</th:block>&nbsp;:&nbsp;</b>
							<input id="dupNo" class="digits required" type="text" size="1" maxlength=1 />
						</td>
					</tr>
				</table>
			</form>
		</div>
		<div id="sendBox" style="display:none;">
			<table width="100%">
				<tr>
					<td>
						<label><input type="radio" name="send" value="1" /><th:block th:text="#{'accept'}">核准</th:block></label><br/>
						<label><input type="radio" name="send" value="2" /><th:block th:text="#{'return'}">退回</th:block></label>
					</td>
				</tr>
			</table>
		</div>
		<input id='mainOid' name='mainOid' type="hidden"/>
	</th:block>
</body>
</html>
