/* 
 * LMS1415M01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.pages;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.lms.panels.LMS1405S02Panel;
import com.mega.eloan.lms.lms.panels.LMS1405S02Panel01;
import com.mega.eloan.lms.lms.panels.LMS1405S02Panel05;
import com.mega.eloan.lms.lms.panels.LMS1415S01Panel;
import com.mega.eloan.lms.lms.panels.LMS1415S02Panel;
import com.mega.eloan.lms.lms.panels.LMS1415S03Panel;
import com.mega.eloan.lms.lms.service.LMS1205Service;
import com.mega.eloan.lms.lms.service.LMS1415Service;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L141M01A;

import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * [海外]聯行額度明細表
 * </pre>
 * 
 * @since 2011/12/1
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/12/1,REX,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms1415m01/{page}")
public class LMS1415M01Page extends AbstractEloanForm {
	@Autowired
	LMSService lmsService;

	@Autowired
	LMS1205Service service1205;

	@Autowired
	LMS1415Service lms1415Service;

	final String TAB_CTX = "_tabCtx";
	final String TAB_SIGN = "tab-";

	@Override
	public void execute(ModelMap model, PageParameters params) {
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L141M01A l141m01a = lms1415Service.findModelByOid(L141M01A.class,
				mainOid);
		L120M01A l120m01a = null;
		if (l141m01a != null) {
			l120m01a = service1205.findL120m01aByMainId(l141m01a.getSrcMainId());
		}

		// 依權限設定button
		addAclLabel(model,
				new AclLabel("_btnDOC_EDITING", params, getDomainClass(), AuthType.Modify, CreditDocStatusEnum.海外_編製中));
		addAclLabel(model, new AclLabel("_btnWAIT_APPROVE", params, getDomainClass(), AuthType.Accept,
				CreditDocStatusEnum.海外_待覆核));
		renderJsI18N(LMS1415M01Page.class);

		// tabs
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		String tabID = TAB_SIGN + Util.addZeroWithValue(page, 2); // 指定ID
		Panel panel = getPanel(page, l120m01a, model);
		panel.processPanelData(model, params);
		model.addAttribute("tabID", tabID);
		renderJsI18N(LMS1205M01Page.class, "print");
	}// ;

	// 頁籤
	public Panel getPanel(int index, L120M01A l120m01a, ModelMap model) {
		Panel panel = null;
		Map<String, String> msgs = null;
		switch (index) {
		case 1:
			panel = new LMS1415S01Panel(TAB_CTX, true);
			break;
		case 2:
			msgs = lmsService.getAllDervPeriod();
			renderJsI18NWithMsgName("dervPeriodCodeType", msgs);
			msgs = lmsService.getCodeType("lms120_noFactCountry");
			renderJsI18NWithMsgName("lms120_noFactCountry", msgs);
			msgs = lmsService.getCodeType("lms120_freezeFactCountry");
			renderJsI18NWithMsgName("lms120_freezeFactCountry", msgs);
			// G-111-0168_05097_B1001 新增海外分(子)行「綠色授信」及「永續績效連結授信」等註記
			msgs = lmsService.getCodeType("lms140_esgGreenSpendType");
			renderJsI18NWithMsgName("lms140_esgGreenSpendType", msgs);
			// G-111-0168_05097_B1001 新增海外分(子)行「綠色授信」及「永續績效連結授信」等註記
			msgs = lmsService.getCodeType("lms140_esgSustainLoanType");
			renderJsI18NWithMsgName("lms140_esgSustainLoanType", msgs);
			// G-113-0145 授信新做額度於eloan簽報核准後，自動傳送AS400執行3X02，以利央行RDT報表傳送。
			msgs = lmsService.getCodeType("lms_140_loanAndContType");
			renderJsI18NWithMsgName("lms_140_loanAndContType", msgs);
			//J-113-0377 海外分(子)行企金授信新增社會責任授信
			msgs = lmsService.getCodeType("lms140_socialKind");
			renderJsI18NWithMsgName("lms140_socialKind", msgs);
			msgs = lmsService.getCodeType("lms140_socialTa");
			renderJsI18NWithMsgName("lms140_socialTa", msgs);
			msgs = lmsService.getCodeType("lms140_socialResp");
			renderJsI18NWithMsgName("lms140_socialResp", msgs);
			panel = new LMS1415S02Panel(TAB_CTX, true, l120m01a);
			renderJsI18N(LMS1405S02Panel.class);
			renderJsI18N(LMS1405S02Panel01.class);
			renderJsI18N(LMS1405S02Panel05.class);
			break;
		case 3:
			panel = new LMS1415S03Panel(TAB_CTX, true);
			break;
		default:
			panel = new LMS1415S01Panel(TAB_CTX, true);
			break;
		}
		return panel;
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return L141M01A.class;
	}
}
