package com.mega.eloan.lms.fms.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.lms.dao.C126M01ADao;
import com.mega.eloan.lms.fms.service.CLS2901Service;
import com.mega.eloan.lms.model.C126M01A;

import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapString;

@Service
public class CLS2901ServiceImpl extends AbstractCapService implements
		CLS2901Service {
	
	private static final Logger logger = LoggerFactory.getLogger(CLS2901ServiceImpl.class);
	
	@Resource
	C126M01ADao c126m01aDao;

	@Override
	public List<Map<String, Object>> getRealEstateAgentInfo(String custId, String dupNo) {

		List<C126M01A> list = c126m01aDao.findByCustIdAndDupNo(custId, dupNo);

		List<Map<String, Object>> rtnList = new ArrayList<Map<String, Object>>();
		
		for(C126M01A entity : list){
			
			Map<String, Object> m = new HashMap<String, Object>();
			String licenseYear = CapString.trimNull(entity.getLicenseYear());
			String licenseWord = CapString.trimNull(entity.getLicenseWord());
			String licenseNumber = CapString.trimNull(entity.getLicenseNumber());
			String hou = !"".equals(licenseNumber) ? "號" : "";
			
			m.put("AGNTNO", entity.getAgntNo());
			m.put("AGNTNAME", entity.getAgntName());
			m.put("LICENSEYEAR", licenseYear);
			m.put("LICENSEWORD", licenseWord);
			m.put("LICENSENUMBER", licenseNumber);
			m.put("AGENTCERTNO", licenseYear + licenseWord + licenseNumber + hou);
			rtnList.add(m);
		}
		
		return rtnList;
	}
	
}
