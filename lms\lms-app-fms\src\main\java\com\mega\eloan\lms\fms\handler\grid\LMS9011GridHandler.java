/* 
 * LMS9011GridHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.handler.grid;

import java.util.List;
import java.util.Properties;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.fms.pages.LMS9011V00Page;
import com.mega.eloan.lms.fms.service.LMS9015Service;
import com.mega.eloan.lms.model.L901M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;

@Scope("request")
@Controller("lms9011gridhandler")
public class LMS9011GridHandler extends AbstractGridHandler {

	@Resource
	LMS9015Service lms9015Service;

	/**
	 * 查詢Grid 資料(國內企金)
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	public CapGridResult queryL901m01a(ISearch pageSetting,
			PageParameters params) throws CapException {
		// Properties
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS9011V00Page.class);

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "branchId",
				user.getUnitNo());
		String itemType = "";
		// 1.全行共同項目(總行維護) 、 2.當地特殊規定項目(海外各分行自行維護)、3.國內企金(總行維護)、國內個金(總行維護)
		if (UtilConstants.BankNo.授管處.equals(user.getUnitNo())) {
			itemType = UtilConstants.Maintain.L901M01ItemType.國內企金;
		} else {
			itemType = UtilConstants.Maintain.L901M01ItemType.當地特殊規定項目;
		}
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "itemType",
				itemType);
		Page page = lms9015Service.findPage(L901M01A.class, pageSetting);
		List<L901M01A> list = page.getContent();
		for (L901M01A model : list) {
			String local = model.getLocale();
			if ("zh_TW".equals(local)) {
				model.setLocale(pop.getProperty("L901M01a.zh_TW"));
			} else if ("zh_CN".equals(local)) {
				model.setLocale(pop.getProperty("L901M01a.zh_CN"));
			} else {
				model.setLocale(pop.getProperty("L901M01a.en_US"));
			}

		}

		return new CapGridResult(list, page.getTotalRow());

	}

	/**
	 * 查詢Grid 資料(國內個金)
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	public CapGridResult queryL901m02a(ISearch pageSetting,
			PageParameters params) throws CapException {
		// Properties
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS9011V00Page.class);

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "branchId",
				user.getUnitNo());
		String itemType = "";
		// 1.全行共同項目(總行維護) 、 2.當地特殊規定項目(海外各分行自行維護)、3.國內企金(總行維護)、國內個金(總行維護)
		if (UtilConstants.BankNo.授管處.equals(user.getUnitNo())) {
			itemType = UtilConstants.Usedoc.itemType.國內個金;
		} else {
			itemType = UtilConstants.Usedoc.itemType.當地特殊規定項目;
		}
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "itemType",
				itemType);
		Page page = lms9015Service.findPage(L901M01A.class, pageSetting);
		List<L901M01A> list = page.getContent();
		for (L901M01A model : list) {
			String local = model.getLocale();
			if ("zh_TW".equals(local)) {
				model.setLocale(pop.getProperty("L901M01a.zh_TW"));
			} else if ("zh_CN".equals(local)) {
				model.setLocale(pop.getProperty("L901M01a.zh_CN"));
			} else {
				model.setLocale(pop.getProperty("L901M01a.en_US"));
			}

		}

		return new CapGridResult(list, page.getTotalRow());

	}
}
