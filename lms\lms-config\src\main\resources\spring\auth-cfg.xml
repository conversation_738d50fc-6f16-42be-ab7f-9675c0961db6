<?xml version="1.0" encoding="UTF-8"?>
<member>
	<system>L</system> <!--權限系統代碼 C.擔保品 I.徵信 L.企金授信 S.消金授信 O.逾放催收 D.資料建檔--> 
	<table>
		<department>BELSBRN</department>
		<user>BELSUSR</user>
		<role>B<PERSON><PERSON>LE</role>
		<user-role>BELSUSRR</user-role>
		<role-auth>BELSRLF</role-auth>
		<code>B<PERSON><PERSON>GM</code>
		<doc>BELSRDA</doc>
	</table>
	<query>
		<user>
			select ID, NAME, POSITION AS POS, BRANCH AS DEPT
   			from ${table.user}
   			where DATASTU='3'
   			order by ID
		</user>
		<branch>
   			select * from ${table.department}
   			order by BRNO,UNITTYPE
		</branch>
		<department>
   			select UNITTYPE AS TYPE, BRNO AS DEPT, BRNAME AS NAME
   			from ${table.department}
   			order by TYPE, BRNO
		</department>
<!-- 		<user-role> -->
<!-- 			select ID, ROLCODE AS ROLE -->
<!-- 			from ${table.user-role} -->
<!-- 			where DATASTU='3' and TYPE='${system}' -->
<!-- 			  and current date between WORKSDT and WORKEDT -->
<!-- 			  and current date not between STOPSDT and STOPEDT -->
<!-- 		</user-role> -->
		<user-role>
			select 
				rur.ID AS ID, 
				rur.ROLCODE AS ROLE,
				rle.UNIT AS UNIT
			from ${table.user-role} rur
			  inner join ${table.role} rle
			  on rle.DATASTU='3' and rur.ROLCODE=rle.ROLCODE
			where rur.DATASTU='3' and rur.TYPE='${system}'
			  and current date between rur.WORKSDT and rur.WORKEDT
			  and current date not between rur.STOPSDT and rur.STOPEDT
		</user-role>
		<role-auth>
			select
			  rle.ROLCODE AS ROLE,
			  rle.UNIT AS UNIT,
			  rlf.PGMCODE AS AUTHCODE,
			  rlf.PGMAUTH AS AUTH,
			  rlf.PGMDEPT AS AUTHDEPT			  
			from ${table.role} rle
			  inner join ${table.role-auth} rlf
			  on rlf.DATASTU='3' and rlf.ROLCODE=rle.ROLCODE
			where rle.DATASTU='3' and rle.TYPE='${system}' and rle.STU='0'
			order by rle.ROLCODE, rlf.PGMCODE
		</role-auth>
		<code>
			select * from (
				select
				  TYPE AS STEP,
				  PGMCODE AS CODE,
				  SEQ,
				  PGMTYP AS PARENT,
				  PGMNAME AS NAME,
				  PGMPATH AS PATH,
				  PGMDESC AS DESC,
				  DOCID
				from ${table.code}
				where SYSTYP='${system}'
			) S
			order by STEP, PARENT, SEQ
		</code>
		<!-- add by fantasy 2012/02/29 -->
		<role>
			select ROLCODE
   			from ${table.role}
   			where DATASTU='3' and STU='0'
   			order by ROLCODE
		</role>
		<!-- add by fantasy 2012/04/13 -->
		<doc>
			select * from ${table.doc}
   			where SYSTYP='${system}'
			order by ROLCODE
		</doc>
	</query>
</member>