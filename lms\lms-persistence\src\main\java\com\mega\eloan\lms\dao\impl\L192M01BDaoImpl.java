package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.L192M01BDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L192M01B;
import com.mega.eloan.lms.model.L192M01A;


@Repository
public class L192M01BDaoImpl extends LMSJpaDao<L192M01B, String> implements
		L192M01BDao {

	@Override
	public int deleteByMeta(L192M01A meta) {
		Query query = entityManager
				.createNamedQuery("l192m01b.deleteByMainId");
		query.setParameter("MAINID", meta.getMainId());
		return query.executeUpdate();
	}
	@Override
	public List<L192M01B> findByCustIdDupId(String custId,String DupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", DupNo);
		List<L192M01B> list = createQuery(L192M01B.class,search).getResultList();
		return list;
	}
	
	@Override
	public List<L192M01B> getL192M01Bs(String mainId, String mainCustId,
			String mainDupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS,
				"mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "mainCustId", mainCustId);
		search.addSearchModeParameters(SearchMode.EQUALS, "mainDupNo",
				mainDupNo);

		search.addOrderBy("mainCustId", true);
		return find(search);
	}
}
