/* 
 * L120S01MDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S01C;
import com.mega.eloan.lms.model.L120S01M;

/** 信用風險管理遵循 **/
public interface L120S01MDao extends IGenericDao<L120S01M> {

	L120S01M findByOid(String oid);

	List<L120S01M> findByMainId(String mainId);

	L120S01M findByUniqueKey(String mainId, String custId, String dupNo);

	L120S01M findByIndex01(String mainId, String custId, String dupNo);

	public List<L120S01M> findByCustId(String mainId, String custId,
			String dupNo);
	
	int deleteByKey(String mainId, String custId, String dupNo);
}