/* 
 * L902M01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 私募基金代碼主檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L902M01A", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L902M01A extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * 私募基金代碼<p/>
	 * NOT NULL
	 */
	@Column(name="PENO", length=4, columnDefinition="CHAR(4)")
	private String peNo;

	/** 私募基金名稱 **/
	@Column(name="PENAME", length=120, columnDefinition="VARCHAR(120)")
	private String peName;

	 

	/** 
	 * 取得私募基金代碼<p/>
	 * NOT NULL
	 */
	public String getPeNo() {
		return this.peNo;
	}
	/**
	 *  設定私募基金代碼<p/>
	 *  NOT NULL
	 **/
	public void setPeNo(String value) {
		this.peNo = value;
	}

	/** 取得私募基金名稱 **/
	public String getPeName() {
		return this.peName;
	}
	/** 設定私募基金名稱 **/
	public void setPeName(String value) {
		this.peName = value;
	}

	
}
