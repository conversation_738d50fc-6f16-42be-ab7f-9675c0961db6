package com.mega.eloan.lms.base.common;

import java.math.BigDecimal;

import tw.com.jcs.common.Util;

public class ClsTermGBCreditLoan implements IClsTerm {

	private BigDecimal income;
	private String s01r_grade ;
	private String reduct_fg ;
	private String pConBegEnd_fg ;
	
	public ClsTermGBCreditLoan(BigDecimal income, String s01r_grade, String reduct_fg, String pConBegEnd_fg){
		this.income = income;
		this.s01r_grade = s01r_grade;
		this.reduct_fg = reduct_fg;
		this.pConBegEnd_fg = pConBegEnd_fg;
	}

	@Override
	public String infoStr(BigDecimal base_rate) {		
		return "[GB]"+"income="+income+ "{IR("+LMSUtil.pretty_numStr(base_rate)+") + " +rate_from_income()+" = "+LMSUtil.pretty_numStr(base_rate.add(rate_from_income()))+"%}"
		+" , grade=" + s01r_grade +"{"+ rate_from_grade()+"}"
		+" , reduct=" + reduct_fg +"{"+ rate_from_reductFg()+"}"
		+" => {final: "+ LMSUtil.pretty_numStr(base_rate.add(get_IR_plus_rate()))+"%}" 
		+" , pConBegEnd=" + pConBegEnd_fg +"{feeNo01: $ "+ get_feeNo_01_feeAmt()+"}"
		;
	}
	
	@Override
	public BigDecimal get_IR_plus_rate() {	
		return rate_from_income().add(rate_from_grade()).add(rate_from_reductFg());
	}

	@Override
	public BigDecimal get_feeNo_01_feeAmt() {
		if(Util.equals("Y", pConBegEnd_fg)){
			return BigDecimal.valueOf(5000);	
		}
		return BigDecimal.valueOf(9000);
	}

	private BigDecimal rate_from_income(){
		if(income.compareTo(BigDecimal.valueOf(120))>=0){
			return BigDecimal.valueOf(1.64);
		}else if(income.compareTo(BigDecimal.valueOf(80))>=0){
			return BigDecimal.valueOf(2.04);
		}else if(income.compareTo(BigDecimal.valueOf(50))>=0){
			return BigDecimal.valueOf(3.54);
		}else{
			return BigDecimal.valueOf(4.04);
		}
	}
	
	private BigDecimal rate_from_grade(){
		if(CrsUtil.inCollection(s01r_grade, new String[]{"1", "2", "3", "4", "5"})){
			return BigDecimal.ZERO;
		}
		return BigDecimal.valueOf(1);
	}
	
	private BigDecimal rate_from_reductFg(){
		if(Util.equals("Y", reduct_fg)){
			return BigDecimal.valueOf(-0.25);
		}
		return BigDecimal.ZERO;
	}

}
