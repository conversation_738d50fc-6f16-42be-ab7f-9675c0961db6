/* 
 * AMLRelateServiceImpl.java 
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.service.impl;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.aml.AmlStrategy;
import com.mega.eloan.common.aml.AmlStrategyFactory;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.common.utils.CustomerIdCheckUtil;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.eloan.lms.base.panels.LMSS20APanel;
import com.mega.eloan.lms.base.service.AMLRelateService;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.NumberService;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dao.L120M01CDao;
import com.mega.eloan.lms.dao.L120M01EDao;
import com.mega.eloan.lms.dao.L120S01ADao;
import com.mega.eloan.lms.dao.L120S01BDao;
import com.mega.eloan.lms.dao.L120S01PDao;
import com.mega.eloan.lms.dao.L120S04ADao;
import com.mega.eloan.lms.dao.L120S09ADao;
import com.mega.eloan.lms.dao.L120S09BDao;
import com.mega.eloan.lms.dao.L120S09CDao;
import com.mega.eloan.lms.dao.L120S26ADao;
import com.mega.eloan.lms.dao.L140M01ADao;
import com.mega.eloan.lms.dao.L140M01SDao;
import com.mega.eloan.lms.dao.L160A01ADao;
import com.mega.eloan.lms.dao.L160M01ADao;
import com.mega.eloan.lms.dao.L160M01BDao;
import com.mega.eloan.lms.dao.L160M01CDao;
import com.mega.eloan.lms.dao.L160M01DDao;
import com.mega.eloan.lms.dao.L161S01ADao;
import com.mega.eloan.lms.dao.L161S01BDao;
import com.mega.eloan.lms.dao.L161S01CDao;
import com.mega.eloan.lms.dao.L162S01ADao;
import com.mega.eloan.lms.dao.L163S01ADao;
import com.mega.eloan.lms.dao.L164S01ADao;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.ejcic.service.EjcicService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.mfaloan.service.MisElCUS25Service;
import com.mega.eloan.lms.mfaloan.service.MisElcrcoService;
import com.mega.eloan.lms.mfaloan.service.MisStoredProcService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01C;
import com.mega.eloan.lms.model.L120M01E;
import com.mega.eloan.lms.model.L120S01A;
import com.mega.eloan.lms.model.L120S01B;
import com.mega.eloan.lms.model.L120S01P;
import com.mega.eloan.lms.model.L120S04A;
import com.mega.eloan.lms.model.L120S09A;
import com.mega.eloan.lms.model.L120S09B;
import com.mega.eloan.lms.model.L120S09C;
import com.mega.eloan.lms.model.L120S26A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01C;
import com.mega.eloan.lms.model.L140M01I;
import com.mega.eloan.lms.model.L140M01J;
import com.mega.eloan.lms.model.L140M01S;
import com.mega.eloan.lms.model.L160M01A;
import com.mega.eloan.lms.model.L160M01B;
import com.mega.eloan.lms.model.L160M01C;
import com.mega.eloan.lms.model.L160M01D;
import com.mega.eloan.lms.model.L161S01A;
import com.mega.eloan.lms.model.L161S01B;
import com.mega.eloan.lms.model.L161S01C;
import com.mega.eloan.lms.model.L162S01A;
import com.mega.eloan.lms.model.L163S01A;
import com.mega.eloan.lms.model.L164S01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapMath;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * BY 專案共用Service
 * </pre>
 * 
 * @since 2012/1/13
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/13,REX,new
 *          <li>2012/12/26,REX,checkELF447NCLASS_增加遠匯判斷
 *          <li>2013/04/22,UFO,增加L000M01A設ngflag值
 *          <li>2013/06/24 Rex,edit,修改國內個金案件判斷的來源不一樣
 *          <li>2013/06/28,Rex,明澤說個金Elcsecnt直皆上加總結果
 *          <li>2013/07/03,Rex,聯行額度明細表不變、取消、婉卻的案件也不需要傳送
 *          <li>2013/07/05,Rex,當為個金團貸案件則不上傳婉卻檔
 *          <li>2013/07/05,Fantasy,個人負債比率和家庭負債比率 Interger改為BigDecimal
 *          <li>2013/07/16,Rex,明澤說是否為本行貸款不列印
 *          <li>2013/08/05,Rex,#646排除已經刪除的額度明細表不傳到聯行額度明細表
 *          <li>2013/08/06,Rex,649當isUSE為空要帶預設值
 *          </ul>
 */
@Service("AMLRelateService")
public class AMLRelateServiceImpl extends AbstractCapService implements
		AMLRelateService {

	protected final Logger logger = LoggerFactory.getLogger(getClass());

	@Resource
	ICustomerService iCustomerService;

	@Resource
	LMSService lmsService;
	@Resource
	CLSService clsService;
	@Resource
	BranchService branchSrv;
	@Resource
	MisdbBASEService misDbService;

	@Resource
	L120M01ADao l120m01aDao;

	@Resource
	L120M01CDao l120m01cDao;

	@Resource
	L140M01ADao l140m01aDao;

	@Resource
	L160M01ADao l160m01aDao;

	@Resource
	L120S09ADao l120s09adao;

	@Resource
	L120S04ADao l120s04aDao;

	@Resource
	L120S01PDao l120s01pdao;

	@Resource
	L160A01ADao l160A01aDao;

	@Resource
	L160M01BDao l160m01bDao;

	@Resource
	L160M01CDao l160m01cDao;

	@Resource
	L160M01DDao l160m01dDao;

	@Resource
	L161S01ADao l161m01aDao;

	@Resource
	L161S01BDao l161m01bDao;

	@Resource
	L161S01CDao l161s01cDao;

	@Resource
	L162S01ADao l162s01aDao;

	@Resource
	L163S01ADao l163s01aDao;

	@Resource
	L164S01ADao l164s01aDao;

	@Resource
	L120S01BDao l120s01bDao;

	@Resource
	L120M01EDao l120m01eDao;

	@Resource
	MisElcrcoService misElcrcoService;

	@Resource
	DwdbBASEService dwdbService;

	@Resource
	EloandbBASEService eloanDbBaseService;

	@Resource
	MisElCUS25Service elcus25Srv;

	@Resource
	MisCustdataService misCustdataService;

	@Resource
	ICustomerService customerSrv;

	@Resource
	MisElCUS25Service misElcus25Service;

	@Resource
	CodeTypeService codetypeservice;

	// J-106-0238-001
	// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
	@Resource
	L120S09BDao l120s09bdao;

	// #J-107-0226_11557_B1001 疑似洗錢或資恐交易態樣檢核表-授信
	@Resource
	L120S09CDao l120s09cdao;

	@Resource
	NumberService number;
	@Resource
	BranchService branchService;

	@Resource
	L120S01ADao l120s01aDao;

	@Resource
	AmlStrategyFactory amlstrategyfactory;

	// J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊
	@Resource
	L140M01SDao l140m01sDao;

	@Resource
	MisStoredProcService misStoredProcService;

	@Resource
	EjcicService ejcicService;

	@Resource
	L120S26ADao l120S26aDao;

	@Resource
	DocFileService docFileService;

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public <T extends GenericBean> T findModelByMainId(Class clazz,
			String mainId) {
		if (clazz == L140M01A.class) {
			return (T) l140m01aDao.findByMainId(mainId);
		} else if (clazz == L120M01A.class) {
			return (T) l120m01aDao.findByMainId(mainId);
		} else if (clazz == L160M01A.class) {
			// J-106-0029-002 洗錢防制-新增洗錢防制頁籤
			return (T) l160m01aDao.findByMainId(mainId);
		}
		return null;
	}

	// J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	@Override
	public L120S09A findL120s09aByOid(String oid) {
		return l120s09adao.findByOid(oid);
	}

	// J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	@Override
	public List<L120S09A> findL120s09asByOids(String[] oids) {
		return l120s09adao.findL120S09AListByOids(oids);
	}

	// J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	@Override
	public List<L120S09A> findL120s09aByMainId(String mainId) {
		return l120s09adao.findByMainId(mainId);
	}

	// J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	@Override
	public void saveL120s09aList(List<L120S09A> list) {
		if (!list.isEmpty()) {
			l120s09adao.save(list);

		}
	}

	// J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	@Override
	public void deleteListL120s09a(List<L120S09A> list) {
		List<String> listOid = new ArrayList<String>();
		for (L120S09A model : list) {
			listOid.add(model.getOid());
		}
		l120s09adao.delete(list);

	}

	// J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	@Override
	public List<L120S09A> findListL120s09aByCustId(String mainId,
			String custId, String dupNo) {
		return l120s09adao.findByMainIdAndCustIdDupNo(mainId, custId, dupNo);
	}

	// J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	@Override
	public List<L120S09A> findListL120s09aByCustName(String mainId,
			String custName) {
		return l120s09adao.findByMainIdAndCustName(mainId, custName);
	}

	// J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	@Override
	public L120S09A findL120s09aMaxQDateByMainId(String mainId) {
		return l120s09adao.findMaxQDateByMainId(mainId);
	}

	// J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	@Override
	public List<L120S09A> findL120s09aByMainIdWithOrder(String mainId) {
		return l120s09adao.findByMainIdWithOrder(mainId);
	}

	// J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	@Override
	public List<L140M01A> findL140m01aListByL120m01cMainId(String mainId,
			String caseType) {
		return l140m01aDao.findL140m01aListByL120m01cMainId(mainId, caseType,
				null);
	}

	// J-106-0029-003 洗錢防制-新增實質受益人
	@Override
	public L120S01P findL120s01pByOid(String oid) {
		return l120s01pdao.findByOid(oid);
	}

	// J-106-0029-003 洗錢防制-新增實質受益人
	@Override
	public L120S01P findL120s01pByRNameWithRType(String mainId, String custId,
			String dupNo, String rType, String rName) {
		return l120s01pdao.findByRNameWithRType(mainId, custId, dupNo, rType,
				rName);
	}

	// J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
	// @Override
	// public L140M01I findL140m01iByUniqueKey(String mainId, String type,
	// String rId, String rDupNo) {
	// return l140m01iDao.findByUniqueKeyWithRType(mainId, type, rId, rDupNo,
	// UtilConstants.lngeFlag.連帶保證人);
	// }

	// J-106-0029-003 洗錢防制-新增實質受益人
	@Override
	public boolean deleteListL120s01p(String[] oids) {
		boolean flag = false;
		List<L120S01P> l120s01ps = l120s01pdao.findL120S01PListByOids(oids);

		if (!l120s01ps.isEmpty()) {
			l120s01pdao.delete(l120s01ps);
			flag = true;
		}
		return flag;
	}

	// J-106-0029-003 洗錢防制-新增實質受益人
	@Override
	public L120S01P findL120s01pMaxSeqNumByMainIdAndCustIdWithRType(
			String mainId, String custId, String dupNo, String rType) {
		return l120s01pdao.findMaxSeqNumByMainIdAndCustIdWithRType(mainId,
				custId, dupNo, rType);
	}

	// J-106-0029-003 洗錢防制-新增實質受益人
	@Override
	public List<L120S01P> findL120s01pByMainIdAndCustIdWithRType(String mainId,
			String custId, String dupNo, String rType) {
		return l120s01pdao.findByMainIdAndCustIdWithRType(mainId, custId,
				dupNo, rType);
	}

	// J-106-0029-003 洗錢防制-新增實質受益人
	@Override
	public List<L120S01P> findL120s01pByMainIdAndCustIdWithoutRType(
			String mainId, String custId, String dupNo) {
		return l120s01pdao.findByMainIdAndCustIdWithoutRType(mainId, custId,
				dupNo);
	}

	// J-106-0029-003 洗錢防制-新增實質受益人
	@Override
	public List<L120S01P> findL120s01pByMainIdWithRType(String mainId,
			String rType) {
		return l120s01pdao.findByMainIdWithRType(mainId, rType);
	}

	// J-106-0029-003 洗錢防制-新增實質受益人
	@Override
	public L120S01B findL120s01bByUniqueKey(String mainId, String custId,
			String dupNo) {
		// 透過獨特Key取得資料
		return l120s01bDao.findByUniqueKey(mainId, custId, dupNo);
	}

	// J-106-0029-003 洗錢防制-新增實質受益人
	@Override
	public List<L120S01P> findL120s01pByMainIdAndCustIdWithRTypeOrderForBuildStr(
			String mainId, String custId, String dupNo, String rType) {
		return l120s01pdao.findByMainIdAndCustIdWithRTypeForBuildStr(mainId,
				custId, dupNo, rType);
	}

	// J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	@Override
	public void saveL120s01pList(List<L120S01P> list) {
		if (!list.isEmpty()) {
			l120s01pdao.save(list);
		}
	}

	// J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	@Override
	public void deleteListL120s01p(List<L120S01P> list) {
		List<String> listOid = new ArrayList<String>();
		for (L120S01P model : list) {
			listOid.add(model.getOid());
		}
		l120s01pdao.delete(list);

	}

	// J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
	@Override
	public L164S01A findL164s01aByUniqueKey(String mainId, String custId,
			String dupNo) {
		// 透過獨特Key取得資料
		return l164s01aDao.findByUniqueKey(mainId, custId, dupNo);
	}

	// J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		if (clazz == L160M01A.class) {
			return l160m01aDao.findByMainIds(mainId);
		} else if (clazz == L160M01B.class) {
			return l160m01bDao.findByMainId(mainId);
		} else if (clazz == L160M01C.class) {
			return l160m01cDao.findByMainId(mainId);
		} else if (clazz == L160M01D.class) {
			return l160m01dDao.findByMainId(mainId);
		} else if (clazz == L161S01A.class) {
			return l161m01aDao.findByMainId(mainId);
		} else if (clazz == L161S01B.class) {
			return l161m01bDao.findByMainId(mainId);
		} else if (clazz == L161S01C.class) {
			return l161s01cDao.findByMainId(mainId);
		} else if (clazz == L162S01A.class) {
			return l162s01aDao.findByMainId(mainId);
		} else if (clazz == L163S01A.class) {
			return l163s01aDao.findByMainId(mainId);
		} else if (clazz == L164S01A.class) {
			return l164s01aDao.findByMainId(mainId);
		} else if (clazz == L120S01B.class) {
			return l120s01bDao.findByMainId(mainId);
		} else if (clazz == L120S04A.class) {
			return l120s04aDao.findByMainId(mainId);
		} else if (clazz == L120S09A.class) {
			return l120s09adao.findByMainId(mainId);
		} else if (clazz == L120S01P.class) {
			return l120s01pdao.findByMainIdWithoutRType(mainId);
		}
		return null;
	}

	/**
	 * J-106-0029-002 洗錢防制-新增洗錢防制頁籤 送呈主管時檢核黑名單是否有漏掉的
	 */
	@Override
	public void chkBlackListFullExitForRptDoc(String mainId, boolean chkScanDone)
			throws CapException {

		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS20APanel.class);

		Map<String, Object> chkInsteadMap = this.checkInstead(mainId);
		boolean instead = MapUtils.getBooleanValue(chkInsteadMap, "instead",
				false);
		String queryBrId = MapUtils.getString(chkInsteadMap, "queryBrId");
		boolean isNeedChkScanDone = this.needChkAmlOkBeforeSendBoss(queryBrId);

		L120M01A l120m01a = this.findModelByMainId(L120M01A.class, mainId);
		if (l120m01a != null) {
			if (UtilConstants.Casedoc.DocType.企金.equals(l120m01a.getDocType())) {
				// J-109-0370 相關評估改版
				// 第一階段 只檢查有無主借款人資料
				List<L120S04A> listL120s04a = lmsService
						.findL120s04aByMainIdKeyCustIdDupNo(mainId,
								Util.trim(l120m01a.getCustId()),
								Util.trim(l120m01a.getDupNo()));
				// List<L120S04A> listL120s04a = (List<L120S04A>) this
				// .findListByMainId(L120S04A.class, mainId);

				// J-110-0CCC_05097_B1001 Web
				// e-Loan新增國發基金協助新創事業紓困融資加碼方案微型企業簽報書格式
				if (!lmsService.hidePanelbyCaseType(l120m01a)
						&& !lmsService.hidePanelbyCaseType_lnType61(l120m01a)
						&& !lmsService.hidePanelbyCaseType_003(l120m01a)
						&& !lmsService.hidePanelbyCaseType_004(l120m01a)
						&& !lmsService.hidePanelbyCaseTypeF(l120m01a)) {
					if (listL120s04a == null || listL120s04a.isEmpty()) {
						// AML.error003=尚未引進關係戶於本行各項業務往來彙總
						throw new CapMessageException(
								pop.getProperty("AML.error003"), getClass());
					}
				}

			}
		}

		List<L120S09A> l120s09as = this.findL120s09aByMainId(mainId);
		if (l120s09as != null && !l120s09as.isEmpty()) {
			StringBuffer noEngNameCust = new StringBuffer("");

			// J-107-0059-001 Web e-Loan 授信簽報書與動審表之AML頁籤及列印檢核表時，增加引進風險等級
			StringBuffer noLuvRiskLevel = new StringBuffer("");
			boolean needChkCustRiskLvl = this
					.needChkAmlCustRiskLevel(queryBrId);

			IBranch branch = branchSrv.getBranch(queryBrId);
			String tCallSas = Util.trim(branch.getCallSas());

			// J-111-0278_05097_B1001 Web
			// e-Loan紐約分行企金限制eloan簽報書中，AMLCFT頁籤中，Name欄位僅能儲存英文、數字、標點符號，且會拒絕非英文數字之字型
			boolean isCustNameOnlyEng = this.isAmlCustNameOnlyEng(queryBrId);
			StringBuffer custNameOnlyEng = new StringBuffer("");

			for (L120S09A l120s09a : l120s09as) {

				// chkScanDone = TRUE 送呈的時候才要真的檢核
				if (chkScanDone && isNeedChkScanDone
						&& Util.notEquals(tCallSas, "S")) {
					if (Util.equals(Util.trim(l120s09a.getBlackListCode()), "")) {
						// AML.error029=洗錢防制頁籤有未完成黑名單掃描之名單
						throw new CapMessageException(
								pop.getProperty("AML.error002"), getClass());
					}
				}

				// J-106-0238-001
				// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
				boolean needEngName = false; // 需要英文戶名 (借款人或共同借款人)
				needEngName = (instead ? this.isL120s09ANeedEngName(
						Util.trim(l120s09a.getCustRelation()), queryBrId)
						: this.isL120s09ANeedEngName(
								Util.trim(l120s09a.getCustRelation()), ""));

				if (needEngName) {
					if (Util.equals(Util.trim(l120s09a.getCustEName()), "")) {
						if (Util.notEquals(Util.trim(noEngNameCust.toString()),
								"")) {
							noEngNameCust.append("、")
									.append(Util.trim(l120s09a.getCustId()))
									.append("  ")
									.append(Util.trim(l120s09a.getCustName()));
						} else {
							noEngNameCust
									.append(Util.trim(l120s09a.getCustId()))
									.append("  ")
									.append(Util.trim(l120s09a.getCustName()));
						}
					}

				}

				// J-107-0059-001 Web e-Loan 授信簽報書與動審表之AML頁籤及列印檢核表時，增加引進風險等級
				if (chkScanDone && needChkCustRiskLvl) {
					if (LMSUtil
							.isContainValue(
									Util.trim(l120s09a.getCustRelation()),
									UtilConstants.Casedoc.L120s09aBlackListCtlTarget.借戶,
									",")) {
						if (Util.equals(Util.trim(l120s09a.getLuvRiskLevel()),
								"")) {

							// J-108-0145_05097_B1001 Web e-Loan
							// 國內外企金授信私募基金案件調整實質受益人控管
							if (Util.notEquals(this
									.canPassAmlRelativeAndRiskLvlChk(l120s09a,
											Util.trim(l120s09a.getCustId()),
											Util.trim(l120s09a.getDupNo())),
									"Y")) {

								if (Util.notEquals(
										Util.trim(noLuvRiskLevel.toString()),
										"")) {
									noLuvRiskLevel
											.append("、")
											.append(Util.trim(l120s09a
													.getCustId()))
											.append("  ")
											.append(Util.trim(l120s09a
													.getCustName()));
								} else {
									noLuvRiskLevel
											.append(Util.trim(l120s09a
													.getCustId()))
											.append("  ")
											.append(Util.trim(l120s09a
													.getCustName()));
								}

							}

						}
					}
				}

				// J-111-0278_05097_B1001 Web
				// e-Loan紐約分行企金限制eloan簽報書中，AMLCFT頁籤中，Name欄位僅能儲存英文、數字、標點符號，且會拒絕非英文數字之字型
				if (isCustNameOnlyEng) {

					String semiCustName = Util.toSemiCharString(String
							.valueOf(Util.trim(l120s09a.getCustName())));

					if (Util.notEquals(semiCustName, "")) {

						if (this.hasFullCharInStringText(semiCustName)) {
							if (Util.notEquals(
									Util.trim(custNameOnlyEng.toString()), "")) {
								custNameOnlyEng
										.append("、")
										.append(Util.trim(l120s09a.getCustId()))
										.append("  ")
										.append(Util.trim(l120s09a
												.getCustName()));
							} else {
								custNameOnlyEng
										.append(Util.trim(l120s09a.getCustId()))
										.append("  ")
										.append(Util.trim(l120s09a
												.getCustName()));
							}
						}

					}

				}

			}

			if (Util.notEquals(Util.trim(noEngNameCust.toString()), "")) {
				// AML.error018=欲掃描之名單「{0}」必須要有英文戶名。
				throw new CapMessageException(MessageFormat.format(
						pop.getProperty("AML.error018"),
						Util.trim(noEngNameCust.toString())), getClass());
			}

			// J-107-0059-001 Web e-Loan 授信簽報書與動審表之AML頁籤及列印檢核表時，增加引進風險等級

			if (chkScanDone && needChkCustRiskLvl) {
				if (Util.notEquals(Util.trim(noLuvRiskLevel.toString()), "")) {
					// AML.error020=本案借款人「{0}」於AML/CFT頁籤中，名單內之借戶風險等級不得為空白。

					throw new CapMessageException(MessageFormat.format(
							pop.getProperty("AML.error020"),
							Util.trim(noLuvRiskLevel.toString())), getClass());

				}
			}

			// J-111-0278_05097_B1001 Web
			// e-Loan紐約分行企金限制eloan簽報書中，AMLCFT頁籤中，Name欄位僅能儲存英文、數字、標點符號，且會拒絕非英文數字之字型
			if (Util.notEquals(Util.trim(custNameOnlyEng.toString()), "")) {
				// AML.error030=欲掃描之名單「{0}」本案戶名必須為英文、數字或標點符號。
				throw new CapMessageException(MessageFormat.format(
						pop.getProperty("AML.error030"),
						Util.trim(custNameOnlyEng.toString())), getClass());
			}

		}

		Map<String, String> amlLostMap = this
				.chkBlackListFullExistForRptDoc(mainId);
		StringBuffer amlLostStrBuff = new StringBuffer("");
		if (amlLostMap != null && !amlLostMap.isEmpty()) {
			for (String keySet : amlLostMap.keySet()) {
				String tKey = Util.trim(keySet);
				String tValue = Util.trim(amlLostMap.get(tKey));
				if (Util.equals(tKey, tValue)) {
					amlLostStrBuff.append(tKey).append("<BR>");
					;
				} else {
					amlLostStrBuff.append(tKey).append(" ").append(tValue)
							.append("<BR>");
				}

			}
		}
		if (Util.notEquals(amlLostStrBuff, "")) {
			// AML.error028=下列名單於洗錢防制頁籤無對應的黑名單掃描結果<BR>{0}
			throw new CapMessageException(
					MessageFormat.format(pop.getProperty("AML.error001"),
							amlLostStrBuff.toString()), getClass());
		}

		// J-107-0059-001 Web e-Loan 授信簽報書與動審表之AML頁籤及列印檢核表時，增加引進風險等級
		// 檢核0024-23往來業務是否有勾選正確

		// 放款 => 02 授信業務
		// 進口 => 03 進出口業務 (科目941,944)
		// 出口 => 03 進出口業務 (科目942,715,950)
		// 應收帳款 => 02 授信業務
		// 供應鏈融資=> 02 授信業務
		// 遠匯 => 01 存匯業務 / 0 3 進出口業務 (科目961,962,963,964)
		// 風險參與 => 03 進出口業務 (科目971)

		// 國內分行才要檢核
		if (chkScanDone && isNeedChkScanDone) {
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			String unitNo = (instead ? queryBrId : user.getUnitNo());
			boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchSrv
					.getBranch(unitNo).getBrNoFlag());
			if (!isOverSea) {

				// 簽報書檢核所有額度明細表(排除取消)
				List<L140M01A> l140m01as = this
						.findL140m01aListByL120m01cMainId(mainId,
								UtilConstants.Cntrdoc.ItemType.額度明細表);
				String err002423BussTypeMsg = this.chkBusinessTypeIn002423(
						mainId, l140m01as, "1");

				if (Util.notEquals(err002423BussTypeMsg, "")) {
					// AML.error021=本案借款人檢核0024-23往來項目資料錯誤如下：<BR>{0}
					throw new CapMessageException(MessageFormat.format(
							pop.getProperty("AML.error021"),
							err002423BussTypeMsg), getClass());
				}
			}
		}

	}

	/**
	 * J-106-0029-002 洗錢防制-新增洗錢防制頁籤 送呈主管時檢核黑名單是否有漏掉的
	 */
	public Map<String, String> chkBlackListFullExistForRptDoc(String mainId) {

		L120M01A l120m01a = this.findModelByMainId(L120M01A.class, mainId);

		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS20APanel.class);

		Locale locale = LMSUtil.getLocale();

		Map<String, String> custRelationMap = null;
		custRelationMap = codetypeservice.findByCodeType("BlackListRelation",
				locale.toString());

		Map<String, String> resultMap = new HashMap<String, String>();
		Map<String, String> oldAmlListIdMap = new HashMap<String, String>();
		Map<String, String> oldAmlListNameMap = new HashMap<String, String>();

		Map<String, Object> chkInsteadMap = this.checkInstead(mainId);
		boolean instead = MapUtils.getBooleanValue(chkInsteadMap, "instead",
				false);
		String queryBrId = MapUtils.getString(chkInsteadMap, "queryBrId");

		List<L120S09A> l120s09as = this.findL120s09aByMainId(mainId);
		if (l120s09as != null && !l120s09as.isEmpty()) {
			for (L120S09A l120s09a : l120s09as) {
				String tCustId = Util.trim(l120s09a.getCustId());
				String tDupNo = Util.trim(l120s09a.getDupNo());
				String tCustName = Util.toSemiCharString(String.valueOf(Util
						.trim(l120s09a.getCustName())));
				String tCustEName = Util.toSemiCharString(Util.trim(l120s09a
						.getCustEName()));
				String fullKey = tCustId + tDupNo;
				if (Util.notEquals(fullKey, "")) {
					if (!oldAmlListIdMap.containsKey(fullKey)) {
						oldAmlListIdMap.put(fullKey, tCustEName);
					}
				}

				if (Util.notEquals(tCustName, "")) {
					if (!oldAmlListNameMap.containsKey(tCustName)) {
						oldAmlListNameMap.put(tCustName, tCustEName);
					}
				}

			}

		}

		// 借款人+共借人(要掃其負責人與實質受益人)
		Map<String, String> cntrAllCustIdMap = new HashMap<String, String>();
		String chkId = "";
		String chkName = "";

		List<L140M01A> l140m01as = this.findL140m01aListByL120m01cMainId(
				mainId, UtilConstants.Cntrdoc.ItemType.額度明細表);

		Set<String> distinctL140id = new HashSet<String>();
		for (L140M01A l140m01a : l140m01as) {

			// 額度明細表借款人
			String bId = Util.trim(l140m01a.getCustId());
			String bNo = Util.trim(l140m01a.getDupNo());
			String bName = Util.toSemiCharString(Util.trim(l140m01a
					.getCustName()));

			distinctL140id.add(bId + "|" + bNo);

			if (!cntrAllCustIdMap.containsKey(bId + bNo)) {
				cntrAllCustIdMap.put(bId + bNo, bName);
			}

			chkId = bId + bNo;
			chkName = bName;
			if (Util.notEquals(chkId, "")) {
				if (!oldAmlListIdMap.containsKey(chkId)) {
					if (!resultMap.containsKey(chkId)) {
						// L120S09a.checkbox1=借戶
						resultMap
								.put(chkId,
										chkName
												+ "("
												+ Util.trim(custRelationMap
														.get(UtilConstants.Casedoc.L120s09aBlackListCtlTarget.借戶)) // pop.getProperty("L120S09a.checkbox1")
												+ ")");
					}
				}
			}

			// 額度明細表共同借款人
			Set<L140M01J> l140m01js = l140m01a.getL140m01j();
			for (L140M01J l140m01j : l140m01js) {
				String cId = Util.trim(l140m01j.getCustId());
				String cNo = Util.trim(l140m01j.getDupNo());
				String cName = Util.toSemiCharString(Util.trim(l140m01j
						.getCustName()));

				if (!cntrAllCustIdMap.containsKey(cId + cNo)) {
					cntrAllCustIdMap.put(cId + cNo, cName);
				}

				chkId = cId + cNo;
				chkName = cName;
				if (Util.notEquals(chkId, "")) {
					if (!oldAmlListIdMap.containsKey(chkId)) {
						if (!resultMap.containsKey(chkId)) {
							// L120S09a.checkbox2=共同借款人
							resultMap
									.put(chkId,
											chkName
													+ "("
													+ Util.trim(custRelationMap
															.get(UtilConstants.Casedoc.L120s09aBlackListCtlTarget.共同借款人)) // pop.getProperty("L120S09a.checkbox2")
													+ ")");
						}
					}
				}
			}

			// 連帶保證人&物上保證人
			Set<L140M01I> l140m01is = l140m01a.getL140m01i();
			for (L140M01I l140m01i : l140m01is) {
				if (Util.equals(l140m01i.getRType(),
						UtilConstants.lngeFlag.連帶保證人)
						|| Util.equals(l140m01i.getRType(),
								UtilConstants.lngeFlag.擔保品提供人)) {
					String gId = Util.trim(l140m01i.getRId());
					String gNo = Util.trim(l140m01i.getRDupNo());
					String gName = Util.toSemiCharString(Util.trim(l140m01i
							.getRName()));
					chkId = gId + gNo;
					chkName = gName;
					if (Util.notEquals(chkId, "")) {
						if (!oldAmlListIdMap.containsKey(chkId)) {
							if (!resultMap.containsKey(chkId)) {
								// resultMap.put(chkId, chkName);

								if (Util.equals(l140m01i.getRType(),
										UtilConstants.lngeFlag.連帶保證人)) {
									// L120S09a.checkbox4=連保人
									resultMap
											.put(chkId,
													chkName
															+ "("
															+ Util.trim(custRelationMap
																	.get(UtilConstants.Casedoc.L120s09aBlackListCtlTarget.連保人)) // pop.getProperty("L120S09a.checkbox4")
															+ ")");
								}
								if (Util.equals(l140m01i.getRType(),
										UtilConstants.lngeFlag.擔保品提供人)) {
									// L120S09a.checkbox5=擔保品提供人
									resultMap
											.put(chkId,
													chkName
															+ "("
															+ Util.trim(custRelationMap
																	.get(UtilConstants.Casedoc.L120s09aBlackListCtlTarget.擔保品提供人)) // pop.getProperty("L120S09a.checkbox5")
															+ ")");
								}

							}
						}
					}
				}
			}

			// J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊
			// 應收帳款無追索權買方
			List<L140M01S> listL140m01s = this.findL140m01sByMainIdType(
					l140m01a.getMainId(),
					UtilConstants.L140m01sType.本案應收帳款買方額度資訊);
			if (listL140m01s != null && !listL140m01s.isEmpty()) {
				for (L140M01S l140m01s : listL140m01s) {
					if (l140m01s.getItemSeq() != 99999) {
						String sId = l140m01s.getCustId();
						String sNo = l140m01s.getDupNo();
						String sName = Util.toSemiCharString(Util.trim(l140m01s
								.getCustName()));

						chkId = sId + sNo;
						chkName = sName;

						if (Util.notEquals(chkId, "")) {
							if (!oldAmlListIdMap.containsKey(chkId)) {
								if (!resultMap.containsKey(chkId)) {
									// 應收帳款買方無追索
									resultMap
											.put(chkId,
													chkName
															+ "("
															+ Util.trim(custRelationMap
																	.get(UtilConstants.Casedoc.L120s09aBlackListCtlTarget.應收帳款買方無追索))
															// pop.getProperty("L120S09a.checkbox2")
															+ ")");
								}
							}
						}

					}

				}
			}

		}

		String caseBrId = l120m01a.getCaseBrId();
		boolean isObs = branchService.isOBSBranch(caseBrId);

		for (String idNo : distinctL140id) {
			String custId = idNo.split("\\|")[0];
			String dupNo = idNo.split("\\|")[1];

			// 再查關係企業
			List<?> rows5 = misElcrcoService.findElcrecomByIdDupnoForBlackList(isObs, custId, dupNo);
			if (rows5 != null && !rows5.isEmpty()) {
				Iterator<?> it5 = rows5.iterator();
				while (it5.hasNext()) {
					Map<?, ?> dataMap5 = (Map<?, ?>) it5.next();

					String s04aId = Util.trim(String.valueOf(dataMap5.get("BAN")));
					String s04aNo = Util.trim(String.valueOf(dataMap5.get("DUPNO")));
					String s04aName = Util.toSemiCharString(Util.trim(String.valueOf(dataMap5.get("CNAME"))));
					String s04aEName = Util.toSemiCharString(Util.trim(String.valueOf(dataMap5.get("ENAME"))));
					if (Util.notEquals(s04aId, "")) {
						chkId = s04aId + s04aNo;
						chkName = s04aName;
						if (Util.notEquals(chkId, "")) {
							if (!oldAmlListIdMap.containsKey(chkId)) {
								if (!resultMap.containsKey(chkId)) {
									// L120S09a.checkbox6=關係企業
									resultMap.put(chkId,chkName + "(" + Util.trim(custRelationMap.get(UtilConstants.Casedoc.L120s09aBlackListCtlTarget.關係企業)) + ")");
								}
							}
						}

					}
				}
			}
		}

		// 關係企業
//		List<L120S04A> listL120s04a = (List<L120S04A>) this.findListByMainId(
//				L120S04A.class, mainId);
//		if (listL120s04a != null && !listL120s04a.isEmpty()) {
//			for (L120S04A l120s04a : listL120s04a) {
//				String custRelation = l120s04a.getCustRelation();
//
//				String[] item = custRelation.split(",");
//				List<String> asList = Arrays.asList(item);
//				if (asList.contains("6")) {
//					String s04aId = Util.trim(l120s04a.getCustId());
//					String s04aNo = Util.trim(l120s04a.getDupNo());
//					String s04aName = Util.toSemiCharString(Util.trim(l120s04a
//							.getCustName()));
//					if (Util.notEquals(s04aId, "")) {
//						chkId = s04aId + s04aNo;
//						chkName = s04aName;
//						if (Util.notEquals(chkId, "")) {
//							if (!oldAmlListIdMap.containsKey(chkId)) {
//								if (!resultMap.containsKey(chkId)) {
//									// L120S09a.checkbox6=關係企業
//									resultMap
//											.put(chkId,
//													chkName
//															+ "("
//															+ Util.trim(custRelationMap
//																	.get(UtilConstants.Casedoc.L120s09aBlackListCtlTarget.關係企業)) // pop.getProperty("L120S09a.checkbox6")
//															+ ")");
//								}
//							}
//						}
//					}
//
//				}
//
//			}
//
//		}

		// 可能沒有ID***********************************
		// 負責人+實質受益人
		List<L120S01B> l120s01bs = (List<L120S01B>) this.findListByMainId(
				L120S01B.class, mainId);
		if (l120s01bs != null && !l120s01bs.isEmpty()) {
			for (L120S01B l120s01b : l120s01bs) {

				String s01bCustId = Util.trim(l120s01b.getCustId());
				String s01bDupNo = Util.trim(l120s01b.getDupNo());
				if (cntrAllCustIdMap.containsKey(s01bCustId + s01bDupNo)) {
					// 借款人或共借人才要

					// 3.負責人
					String chairmanId = Util.trim(l120s01b.getChairmanId());
					String chairmanDupNo = Util.trim(l120s01b
							.getChairmanDupNo());
					String chairman = Util.toSemiCharString(Util.trim(l120s01b
							.getChairman()));
					chkId = chairmanId + chairmanDupNo;
					chkName = chairman;

					if (Util.notEquals(chkId, "")
							|| Util.notEquals(chkName, "")) {
						if (Util.notEquals(chkId, "")) {
							// 有ID就用ID查
							if (!oldAmlListIdMap.containsKey(chkId)) {
								if (!resultMap.containsKey(chkId)) {
									// L120S09a.checkbox3=借戶負責人
									resultMap
											.put(chkId,
													chkName
															+ "("
															+ Util.trim(custRelationMap
																	.get(UtilConstants.Casedoc.L120s09aBlackListCtlTarget.負責人)) // pop.getProperty("L120S09a.checkbox3")
															+ ")");
								}
							}
						} else {
							// 沒有ID但有戶名
							if (!oldAmlListNameMap.containsKey(chkName)) {
								if (!resultMap.containsKey(chkName)) {
									// L120S09a.checkbox3=借戶負責人
									resultMap
											.put(chkName,
													""
															+ "("
															+ Util.trim(custRelationMap
																	.get(UtilConstants.Casedoc.L120s09aBlackListCtlTarget.負責人)) // pop.getProperty("L120S09a.checkbox7")
															+ ")");
								}
							}

						}
					}

					// 7.實質受益人 L120S01P
					List<L120S01P> listL120s01p = this
							.findL120s01pByMainIdAndCustIdWithRType(
									mainId,
									s01bCustId,
									s01bDupNo,
									UtilConstants.Casedoc.L120s09aBlackListCtlTarget.實質受益人);
					if (listL120s01p != null && !listL120s01p.isEmpty()) {
						for (L120S01P l120s01p : listL120s01p) {
							String rId = Util.trim(l120s01p.getRId());
							String rDupNo = Util.trim(l120s01p.getRDupNo());
							String rName = Util.toSemiCharString(Util
									.trim(l120s01p.getRName()));
							chkId = rId + rDupNo;
							chkName = rName;
							if (Util.notEquals(chkId, "")
									|| Util.notEquals(chkName, "")) {
								if (Util.notEquals(chkId, "")) {
									if (!oldAmlListIdMap.containsKey(chkId)) {
										if (!resultMap.containsKey(chkId)) {
											// L120S09a.checkbox7=實質受益人
											resultMap
													.put(chkId,
															chkName
																	+ "("
																	+ Util.trim(custRelationMap
																			.get(UtilConstants.Casedoc.L120s09aBlackListCtlTarget.實質受益人)) // pop.getProperty("L120S09a.checkbox7")
																	+ ")");
										}
									}
								} else {
									if (!oldAmlListNameMap.containsKey(chkName)) {
										if (!resultMap.containsKey(chkName)) {
											// L120S09a.checkbox7=實質受益人
											resultMap
													.put(chkName,
															""
																	+ "("
																	+ Util.trim(custRelationMap
																			.get(UtilConstants.Casedoc.L120s09aBlackListCtlTarget.實質受益人)) // pop.getProperty("L120S09a.checkbox7")
																	+ ")");
										}
									}
								}
							}

						}

					}

					// J-107-0070-001 Web e-Loan
					// 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
					// 10.高階管理人員 L120S01P

					if (this.needChkCtrlPeoBeforeSendBoss(queryBrId)) {

						List<L120S01P> listL120s01p_smgr = this
								.findL120s01pByMainIdAndCustIdWithRType(
										mainId,
										s01bCustId,
										s01bDupNo,
										UtilConstants.Casedoc.L120s09aBlackListCtlTarget.高階管理人員);
						if (listL120s01p_smgr != null
								&& !listL120s01p_smgr.isEmpty()) {
							for (L120S01P l120s01p : listL120s01p_smgr) {
								String rId = Util.trim(l120s01p.getRId());
								String rDupNo = Util.trim(l120s01p.getRDupNo());
								String rName = Util.toSemiCharString(Util
										.trim(l120s01p.getRName()));
								chkId = rId + rDupNo;
								chkName = rName;
								if (Util.notEquals(chkId, "")
										|| Util.notEquals(chkName, "")) {
									if (Util.notEquals(chkId, "")) {
										if (!oldAmlListIdMap.containsKey(chkId)) {
											if (!resultMap.containsKey(chkId)) {
												// L120S09a.checkbox10=高階管理人員
												resultMap
														.put(chkId,
																chkName
																		+ "("
																		+ Util.trim(custRelationMap
																				.get(UtilConstants.Casedoc.L120s09aBlackListCtlTarget.高階管理人員)) // pop.getProperty("L120S09a.checkbox7")
																		+ ")");
											}
										}
									} else {
										if (!oldAmlListNameMap
												.containsKey(chkName)) {
											if (!resultMap.containsKey(chkName)) {
												// L120S09a.checkbox10=高階管理人員
												resultMap
														.put(chkName,
																""
																		+ "("
																		+ Util.trim(custRelationMap
																				.get(UtilConstants.Casedoc.L120s09aBlackListCtlTarget.高階管理人員)) // pop.getProperty("L120S09a.checkbox7")
																		+ ")");
											}
										}
									}
								}

							}

						}
					}

					// J-108-0039_05097_B1001 Web e-Loan
					// 國內企金授信系統簽報、動審AML頁籤將借戶之「具控制權人」納入應查詢比對黑名單之對象。
					// 11.具控制權人 L120S01P
					if (this.needChkCtrlPeoBeforeSendBoss(queryBrId)) {

						List<L120S01P> listL120s01p_smgr = this
								.findL120s01pByMainIdAndCustIdWithRType(
										mainId,
										s01bCustId,
										s01bDupNo,
										UtilConstants.Casedoc.L120s09aBlackListCtlTarget.具控制權人);
						if (listL120s01p_smgr != null
								&& !listL120s01p_smgr.isEmpty()) {
							for (L120S01P l120s01p : listL120s01p_smgr) {
								String rId = Util.trim(l120s01p.getRId());
								String rDupNo = Util.trim(l120s01p.getRDupNo());
								String rName = Util.toSemiCharString(Util
										.trim(l120s01p.getRName()));
								chkId = rId + rDupNo;
								chkName = rName;
								if (Util.notEquals(chkId, "")
										|| Util.notEquals(chkName, "")) {
									if (Util.notEquals(chkId, "")) {
										if (!oldAmlListIdMap.containsKey(chkId)) {
											if (!resultMap.containsKey(chkId)) {
												// L120S09a.checkbox11=具控制權人
												resultMap
														.put(chkId,
																chkName
																		+ "("
																		+ Util.trim(custRelationMap
																				.get(UtilConstants.Casedoc.L120s09aBlackListCtlTarget.具控制權人)) // pop.getProperty("L120S09a.checkbox11")
																		+ ")");
											}
										}
									} else {
										if (!oldAmlListNameMap
												.containsKey(chkName)) {
											if (!resultMap.containsKey(chkName)) {
												// L120S09a.checkbox11=具控制權人
												resultMap
														.put(chkName,
																""
																		+ "("
																		+ Util.trim(custRelationMap
																				.get(UtilConstants.Casedoc.L120s09aBlackListCtlTarget.具控制權人)) // pop.getProperty("L120S09a.checkbox11")
																		+ ")");
											}
										}
									}
								}

							}

						}
					}

				}

			}
		}

		return resultMap;
	}

	/**
	 * J-106-0029-003 洗錢防制-新增實質受益人
	 * 
	 * @param mainId
	 * @throws CapException
	 */
	@Override
	public String chkBeneficiaryIsOkForRptDoc(String mainId,
			LinkedHashMap<String, String> custIdMapAml) throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS20APanel.class);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		StringBuffer errMsg = new StringBuffer("");
		StringBuffer errNoConfirm = new StringBuffer("");
		StringBuffer errNoL120s01p = new StringBuffer("");
		String dataFrom = "0024";
		Map<String, Object> chkInsteadMap = this.checkInstead(mainId);
		boolean instead = MapUtils.getBooleanValue(chkInsteadMap, "instead",
				false);
		String queryBrId = MapUtils.getString(chkInsteadMap, "queryBrId");
		String unitNo = (instead ? queryBrId : user.getUnitNo());

		boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchSrv
				.getBranch(unitNo).getBrNoFlag());

		// 借款人-行業對象別
		LinkedHashMap<String, String> idAndBusCodeMap = new LinkedHashMap<String, String>();

		// 檢核借款人基本資料實質受益人欄位不得空白
		StringBuffer beneficiaryLostBuf = new StringBuffer("");
		List<L120S01B> l120s01bs = (List<L120S01B>) this.findListByMainId(
				L120S01B.class, mainId);

		if (l120s01bs != null) {
			for (L120S01B l120s01b : l120s01bs) {
				String s01bCustId = Util.trim(l120s01b.getCustId());
				String s01bDupNo = Util.trim(l120s01b.getDupNo());
				String busCode = Util.trim(l120s01b.getBusCode());

				// 簽報書檢核時，行業對象別從簽報書借款基本資料來
				// 動審表檢核時，行業對象別從0024來(因為借款人、共借人可能與簽報書不同)
				if (!idAndBusCodeMap.containsKey(s01bCustId + s01bDupNo)) {
					idAndBusCodeMap.put(s01bCustId + s01bDupNo, busCode);
				}

				if (Util.notEquals(busCode, "")
						&& !LMSUtil.isBusCode_060000_130300(busCode)) {
					// 企業戶才要檢核實質受益人不得空白
					if (Util.equals(l120s01b.getBeneficiary(), "")) {
						beneficiaryLostBuf.append(Util.equals(
								beneficiaryLostBuf.toString(), "") ? "" : "、");
						beneficiaryLostBuf.append(s01bCustId + s01bDupNo);
					}
				}

			}
		}
		if (Util.notEquals(beneficiaryLostBuf.toString(), "")) {
			// AML.error004=「{0}」借款人基本資料實質受益人欄位不得空白
			errMsg.append(Util.equals(errMsg, "") ? "" : "<BR>");
			errMsg.append(MessageFormat.format(pop.getProperty("AML.error004"),
					beneficiaryLostBuf.toString()));

		}

		if (this.needChkBeneficiaryBeforeSendBoss(unitNo)) {

			// 檢核實質受益人
			// 1.國內檢核0024是否已確認
			// 2.海外檢核DW 是否有實質受益人
			for (String fullCustId : custIdMapAml.keySet()) {
				String[] fullKey = fullCustId.split("-");
				String custId = fullKey[0];
				String dupNo = fullKey[1];
				String custName = custIdMapAml.get(fullCustId);

				String busCode = "";
				if (idAndBusCodeMap.containsKey(custId + dupNo)) {
					busCode = MapUtils.getString(idAndBusCodeMap, custId
							+ dupNo, "");
					if (LMSUtil.isBusCode_060000_130300(busCode)
							|| Util.equals(busCode, "")) {
						// 沒有行業對象別就先不檢核
						continue;
					}
				} else {
					continue;
				}

				if (!isOverSea) {
					// 國內
					dataFrom = "0024";
					Map<String, Object> cmfData = misDbService
							.selCMFLUNBNByCustId(custId, dupNo);

					if (cmfData == null || cmfData.isEmpty()) {
						errNoConfirm.append(Util.equals(errNoConfirm, "") ? ""
								: "、");
						errNoConfirm.append(custId + dupNo + " " + custName);
					} else {

						String BUSCD = Util.trim(MapUtils.getString(cmfData,
								"BUSCD"));
						String LUV_BEN_CONFIRM = Util.trim(MapUtils.getString(
								cmfData, "LUV_BEN_CONFIRM"));

						if (Util.notEquals(BUSCD, "")
								&& !LMSUtil.isBusCode_060000_130300(BUSCD)) {
							// 企業戶
							if (Util.notEquals(LUV_BEN_CONFIRM, "Y")
									&& Util.notEquals(LUV_BEN_CONFIRM, "N")) {
								errNoConfirm.append(Util.equals(errNoConfirm,
										"") ? "" : "、");
								errNoConfirm.append(custId + dupNo + " "
										+ custName);
							}

							if (Util.equals(LUV_BEN_CONFIRM, "Y")) {
								// 需確認 時，檢核有沒有L120S01P

								List<L120S01P> listL120s01p = this
										.findL120s01pByMainIdAndCustIdWithRType(
												mainId,
												custId,
												dupNo,
												UtilConstants.Casedoc.L120s09aBlackListCtlTarget.實質受益人);
								if (listL120s01p == null
										|| listL120s01p.isEmpty()) {
									errNoL120s01p.append(Util.equals(
											errNoL120s01p.toString(), "") ? ""
											: "、");
									errNoL120s01p.append(custId + dupNo);
								}

							}
						}
					}
				} else {
					// 海外
					dataFrom = "AS400";

					// busCode 從前面idAndBusCodeMap 來
					if (Util.notEquals(busCode, "")
							&& !LMSUtil.isBusCode_060000_130300(busCode)) {
						// 企業戶
						// Map<String, Object> csOvs = dwdbService
						// .findDW_OTS_CSOVS_By_CustId_And_BrNo(unitNo,
						// custId, dupNo);

						// 判斷AS400 最終實際受益人得為排除免徵提相關資料之註記
						/*
						 * 因目前大陸地區分行的客戶大多為台商企業，且部分具控制之實質受益人為公開發行公司，
						 * 蘇州分行依本行防制洗錢及打擊資助恐怖主義須知第五條
						 * -確認客戶身分措施提出新增此欄位之申請。2017/2/20
						 */

						boolean needChkEffective = this.foreignBranchCheckExcl(
								unitNo, custId, dupNo);

						if (needChkEffective) {
							List<Map<String, Object>> cmfDataList = dwdbService
									.findDW_OTS_EFFECTIVE_OVS_By_CustId_And_BrNo(
											unitNo, custId, dupNo);

							if (cmfDataList != null && !cmfDataList.isEmpty()) {
								// AS400有實際受益人時，檢核e-Loan有沒有L120S01P
								List<L120S01P> listL120s01p = this
										.findL120s01pByMainIdAndCustIdWithRType(
												mainId,
												custId,
												dupNo,
												UtilConstants.Casedoc.L120s09aBlackListCtlTarget.實質受益人);
								if (listL120s01p == null
										|| listL120s01p.isEmpty()) {
									errNoL120s01p.append(Util.equals(
											errNoL120s01p.toString(), "") ? ""
											: "、");
									errNoL120s01p.append(custId + dupNo);
								}
							} else {
								errNoConfirm.append(Util.equals(errNoConfirm,
										"") ? "" : "、");
								errNoConfirm.append(custId + dupNo + " "
										+ custName);
							}
						}

					}

				}
			}
		}

		if (Util.notEquals(errNoConfirm.toString(), "")) {

			// 後台管理->系統設定維護->LMS_J1060029002_ON Y 是否要檢核實質受益人
			// AML.error006=借款人「{0}」於{1}尚未完成法人戶實際受益人身份確認
			errMsg.append(Util.equals(errMsg, "") ? "" : "<BR>");
			errMsg.append(MessageFormat.format(pop.getProperty("AML.error006"),
					errNoConfirm.toString(), dataFrom));

		}

		if (Util.notEquals(errNoL120s01p.toString(), "")) {

			// 後台管理->系統設定維護->LMS_J1060029002_ON Y 是否要檢核實質受益人
			// isOverSea=false(國內分行)=>AML.error007=借款人「{0}」於{1}法人戶實際受益人身份為「需確認」，e-Loan借款人基本資料之「實質受益人」欄位不得為「無」。
			// isOverSea=true
			// (海外分行)=>AML.error012=借款人「{0}」於{1}有實際受益人，e-Loan「實質受益人」欄位不得為「無」。
			errMsg.append(Util.equals(errMsg, "") ? "" : "<BR>");
			errMsg.append(MessageFormat.format(
					(isOverSea ? pop.getProperty("AML.error012") : pop
							.getProperty("AML.error007")), errNoL120s01p
							.toString(), dataFrom));

		}

		return errMsg.toString();

	}

	/**
	 * 組出實質受益人資訊
	 * 
	 * @param l140m01a
	 * @return 實質受益人資訊
	 */
	@Override
	public String contactBeneficiary(String mainId, String custId,
			String dupNo, String rType) {
		int guaIndex = 0;
		StringBuilder temp = new StringBuilder("");
		List<L120S01P> l120s01ps = this
				.findL120s01pByMainIdAndCustIdWithRTypeOrderForBuildStr(mainId,
						custId, dupNo, rType);
		if (l120s01ps != null && !l120s01ps.isEmpty()) {

			for (L120S01P l120s01p : l120s01ps) {

				temp.append(temp.length() > 0 ? UtilConstants.Mark.MARKDAN : "");
				temp.append(guaIndex);
				temp.append(".");
				temp.append(l120s01p.getRName());

			}
		}

		return temp.toString();
	}

	/**
	 * J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤 (動審表)
	 * 
	 * @param mainId
	 * @throws CapException
	 */
	@Override
	public String chkBeneficiaryIsOkForDrawDown(String mainId,
			LinkedHashMap<String, String> allBorrowerIdMap) throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS20APanel.class);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		StringBuffer errBorrowerAMLEmptyField = new StringBuffer("");

		StringBuffer errMsg = new StringBuffer("");
		StringBuffer errNoConfirm = new StringBuffer("");
		StringBuffer errNoL120s01p = new StringBuffer("");
		StringBuffer errNo0024 = new StringBuffer("");
		String dataFrom = "0024";

		// 借款人-行業對象別
		LinkedHashMap<String, String> idAndBusCodeMap = new LinkedHashMap<String, String>();

		Map<String, Object> chkInsteadMap = this.checkInstead(mainId);
		boolean instead = MapUtils.getBooleanValue(chkInsteadMap, "instead",
				false);
		String queryBrId = MapUtils.getString(chkInsteadMap, "queryBrId");
		String unitNo = (instead ? queryBrId : user.getUnitNo());
		boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchSrv
				.getBranch(unitNo).getBrNoFlag());

		// 檢核借款人基本資料實質受益人欄位不得空白
		// 取得0024判斷行業對象別
		if (allBorrowerIdMap != null && !allBorrowerIdMap.isEmpty()) {
			for (String chkKey : allBorrowerIdMap.keySet()) {
				String[] chkKeyArr = chkKey.split("-");
				String chkId = chkKeyArr[0];
				String chkDupNo = chkKeyArr[1];
				String chkName = Util.trim(allBorrowerIdMap.get(chkKey));

				// 取得0024判斷行業對象別
				Map<String, Object> m0024 = iCustomerService.findByIdDupNo(
						chkId, chkDupNo);
				String busCode = (m0024 != null && !m0024.isEmpty()) ? Util
						.trim(MapUtils.getString(m0024, "BUSCD")) : "";

				// 簽報書檢核時，行業對象別從簽報書借款基本資料來
				// 動審表檢核時，行業對象別從0024來(因為借款人、共借人可能與簽報書不同)
				if (!idAndBusCodeMap.containsKey(chkId + chkDupNo)) {
					idAndBusCodeMap.put(chkId + chkDupNo, busCode);
				}

				// 企業戶才要檢核負責人跟實質受益人
				if (Util.equals(busCode, "")) {
					errNo0024.append(Util.equals(errNo0024.toString(), "") ? ""
							: "、");
					errNo0024.append(chkId + chkDupNo + " " + chkName);
				} else {
					if (Util.notEquals(busCode, "")
							&& !LMSUtil.isBusCode_060000_130300(busCode)) {
						// 企業戶
						L164S01A l164s01a = this.findL164s01aByUniqueKey(
								mainId, chkId, chkDupNo);
						if (l164s01a != null) {
							if (Util.equals(l164s01a.getChairman(), "")
									|| Util.equals(l164s01a.getBeneficiary(),
											"")) {

								errBorrowerAMLEmptyField
										.append(Util.equals(
												errBorrowerAMLEmptyField
														.toString(), "") ? ""
												: "、");
								errBorrowerAMLEmptyField.append(chkId
										+ chkDupNo + " " + chkName);
							}
						} else {
							errBorrowerAMLEmptyField
									.append(Util.equals(
											errBorrowerAMLEmptyField.toString(),
											"") ? "" : "、");
							errBorrowerAMLEmptyField.append(chkId + chkDupNo
									+ " " + chkName);
						}
					}
				}

			}
		}

		if (Util.notEquals(errNo0024.toString(), "")) {
			if (this.needChk0024Exist(queryBrId)) {
				// AML.error010=主從債務人「{0}」於0024無行業對象別資料。
				errMsg.append(Util.equals(errMsg, "") ? "" : "<BR>");
				errMsg.append(MessageFormat.format(pop
						.getProperty("AML.error010"), errNo0024.toString()
						.toString()));
			}

		}

		if (Util.notEquals(errBorrowerAMLEmptyField.toString(), "")) {
			// AML.error008=借款人/共同借款人{0}於相關報表->主從債務人資料表中尚有洗錢防制所需欄位未完成輸入。
			errMsg.append(Util.equals(errMsg, "") ? "" : "<BR>");
			errMsg.append(MessageFormat.format(pop.getProperty("AML.error008"),
					errBorrowerAMLEmptyField.toString()));
		}

		if (this.needChkBeneficiaryBeforeSendBoss(unitNo)) {
			// 檢核實質受益人
			// 1.國內檢核0024是否已確認
			// 2.海外檢核DW 是否有實質受益人(讀參數，目前新加坡跟澳洲地區不檢核)
			for (String fullCustId : allBorrowerIdMap.keySet()) {
				String[] fullKey = fullCustId.split("-");
				String custId = fullKey[0];
				String dupNo = fullKey[1];
				String custName = allBorrowerIdMap.get(fullCustId);
				String busCode = "";
				if (idAndBusCodeMap.containsKey(custId + dupNo)) {
					busCode = MapUtils.getString(idAndBusCodeMap, custId
							+ dupNo, "");
					if (LMSUtil.isBusCode_060000_130300(busCode)
							|| Util.equals(busCode, "")) {
						// 沒有行業對象別就先不檢核
						// 先出錯誤訊息errNo0024--AML.error010=主從債務人「{0}」於0024無行業對象別資料。
						continue;
					}
				} else {
					continue;
				}

				if (!isOverSea) {
					// 國內
					dataFrom = "0024";
					Map<String, Object> cmfData = misDbService
							.selCMFLUNBNByCustId(custId, dupNo);

					if (cmfData == null || cmfData.isEmpty()) {
						errNoConfirm.append(Util.equals(errNoConfirm, "") ? ""
								: "、");
						errNoConfirm.append(custId + dupNo + " " + custName);
					} else {

						String BUSCD = Util.trim(MapUtils.getString(cmfData,
								"BUSCD"));
						String LUV_BEN_CONFIRM = Util.trim(MapUtils.getString(
								cmfData, "LUV_BEN_CONFIRM"));

						if (Util.notEquals(BUSCD, "")
								&& !LMSUtil.isBusCode_060000_130300(BUSCD)) {
							if (Util.notEquals(LUV_BEN_CONFIRM, "Y")
									&& Util.notEquals(LUV_BEN_CONFIRM, "N")) {
								errNoConfirm.append(Util.equals(errNoConfirm,
										"") ? "" : "、");
								errNoConfirm.append(custId + dupNo + " "
										+ custName);
							}

							if (Util.equals(LUV_BEN_CONFIRM, "Y")) {
								// 需確認 時，檢核有沒有L120S01P

								List<L120S01P> listL120s01p = this
										.findL120s01pByMainIdAndCustIdWithRType(
												mainId,
												custId,
												dupNo,
												UtilConstants.Casedoc.L120s09aBlackListCtlTarget.實質受益人);
								if (listL120s01p == null
										|| listL120s01p.isEmpty()) {
									errNoL120s01p.append(Util.equals(
											errNoL120s01p.toString(), "") ? ""
											: "、");
									errNoL120s01p.append(custId + dupNo);
								}

							}
						}
					}
				} else {
					// 海外
					dataFrom = "AS400";

					// busCode 從前面idAndBusCodeMap 來
					if (Util.notEquals(busCode, "")
							&& !LMSUtil.isBusCode_060000_130300(busCode)) {
						// 企業戶
						// Map<String, Object> csOvs = dwdbService
						// .findDW_OTS_CSOVS_By_CustId_And_BrNo(unitNo,
						// custId, dupNo);

						// 判斷AS400 最終實際受益人得為排除免徵提相關資料之註記
						/*
						 * 因目前大陸地區分行的客戶大多為台商企業，且部分具控制之實質受益人為公開發行公司，
						 * 蘇州分行依本行防制洗錢及打擊資助恐怖主義須知第五條
						 * -確認客戶身分措施提出新增此欄位之申請。2017/2/20
						 */

						boolean needChkEffective = this.foreignBranchCheckExcl(
								unitNo, custId, dupNo);
						if (needChkEffective) {
							List<Map<String, Object>> cmfDataList = dwdbService
									.findDW_OTS_EFFECTIVE_OVS_By_CustId_And_BrNo(
											unitNo, custId, dupNo);

							if (cmfDataList != null && !cmfDataList.isEmpty()) {
								// AS400有實際受益人時，檢核e-Loan有沒有L120S01P
								List<L120S01P> listL120s01p = this
										.findL120s01pByMainIdAndCustIdWithRType(
												mainId,
												custId,
												dupNo,
												UtilConstants.Casedoc.L120s09aBlackListCtlTarget.實質受益人);
								if (listL120s01p == null
										|| listL120s01p.isEmpty()) {
									errNoL120s01p.append(Util.equals(
											errNoL120s01p.toString(), "") ? ""
											: "、");
									errNoL120s01p.append(custId + dupNo);
								}
							} else {
								errNoConfirm.append(Util.equals(errNoConfirm,
										"") ? "" : "、");
								errNoConfirm.append(custId + dupNo + " "
										+ custName);
							}
						}

					}
				}
			}
		}

		if (Util.notEquals(errNoConfirm.toString(), "")) {

			// AML.error006=借款人「{0}」於{1}尚未完成法人戶實際受益人身份確認
			errMsg.append(Util.equals(errMsg, "") ? "" : "<BR>");
			errMsg.append(MessageFormat.format(pop.getProperty("AML.error006"),
					errNoConfirm.toString(), dataFrom));

		}

		if (Util.notEquals(errNoL120s01p.toString(), "")) {

			// isOverSea=false(國內分行)=>AML.error007=借款人「{0}」於{1}法人戶實際受益人身份為「需確認」，e-Loan借款人基本資料之「實質受益人」欄位不得為「無」。
			// isOverSea=true
			// (海外分行)=>AML.error012=借款人「{0}」於{1}有實際受益人，e-Loan「實質受益人」欄位不得為「無」。
			errMsg.append(Util.equals(errMsg, "") ? "" : "<BR>");
			errMsg.append(MessageFormat.format(
					(isOverSea ? pop.getProperty("AML.error012") : pop
							.getProperty("AML.error007")), errNoL120s01p
							.toString(), dataFrom));

		}

		return errMsg.toString();

	}

	/**
	 * J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤送呈主管時檢核黑名單是否有漏掉的 (動審表用)
	 */
	@Override
	public void chkBlackListFullExitForDrawDown(String mainId,
			boolean chkScanDone) throws CapException {

		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS20APanel.class);

		Map<String, Object> chkInsteadMap = this.checkInstead(mainId);
		boolean instead = MapUtils.getBooleanValue(chkInsteadMap, "instead",
				false);
		String queryBrId = MapUtils.getString(chkInsteadMap, "queryBrId");
		boolean isNeedChkScanDone = this.needChkAmlOkBeforeSendBoss(queryBrId);
		boolean needChkCustRiskLvl = this.needChkAmlCustRiskLevel(queryBrId);

		List<L120S09A> l120s09as = this.findL120s09aByMainId(mainId);

		IBranch branch = branchSrv.getBranch(queryBrId);
		String tCallSas = Util.trim(branch.getCallSas());

		// J-111-0278_05097_B1001 Web
		// e-Loan紐約分行企金限制eloan簽報書中，AMLCFT頁籤中，Name欄位僅能儲存英文、數字、標點符號，且會拒絕非英文數字之字型
		boolean isCustNameOnlyEng = this.isAmlCustNameOnlyEng(queryBrId);
		StringBuffer custNameOnlyEng = new StringBuffer("");

		if (l120s09as != null && !l120s09as.isEmpty()) {
			StringBuffer noEngNameCust = new StringBuffer("");
			// J-107-0059-001 Web e-Loan 授信簽報書與動審表之AML頁籤及列印檢核表時，增加引進風險等級
			StringBuffer noLuvRiskLevel = new StringBuffer("");
			for (L120S09A l120s09a : l120s09as) {

				// chkScanDone = TRUE 送呈的時候才要真的檢核
				if (chkScanDone && isNeedChkScanDone
						&& Util.notEquals(tCallSas, "S")) {
					if (Util.equals(Util.trim(l120s09a.getBlackListCode()), "")) {
						// AML.error029=洗錢防制頁籤有未完成黑名單掃描之名單
						throw new CapMessageException(
								pop.getProperty("AML.error002"), getClass());
					}
				}

				// J-106-0238-001
				// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
				boolean needEngName = false; // 需要英文戶名 (借款人或共同借款人)
				needEngName = (instead ? this.isL120s09ANeedEngName(
						Util.trim(l120s09a.getCustRelation()), queryBrId)
						: this.isL120s09ANeedEngName(
								Util.trim(l120s09a.getCustRelation()), ""));
				if (needEngName) {
					if (Util.equals(Util.trim(l120s09a.getCustEName()), "")) {
						if (Util.notEquals(Util.trim(noEngNameCust.toString()),
								"")) {
							noEngNameCust.append("、")
									.append(Util.trim(l120s09a.getCustId()))
									.append("  ")
									.append(Util.trim(l120s09a.getCustName()));
						} else {
							noEngNameCust
									.append(Util.trim(l120s09a.getCustId()))
									.append("  ")
									.append(Util.trim(l120s09a.getCustName()));
						}
					}

				}

				// J-107-0059-001 Web e-Loan 授信簽報書與動審表之AML頁籤及列印檢核表時，增加引進風險等級
				if (chkScanDone && needChkCustRiskLvl) {
					if (LMSUtil
							.isContainValue(
									Util.trim(l120s09a.getCustRelation()),
									UtilConstants.Casedoc.L120s09aBlackListCtlTarget.借戶,
									",")) {
						if (Util.equals(Util.trim(l120s09a.getLuvRiskLevel()),
								"")) {
							if (Util.notEquals(
									Util.trim(noLuvRiskLevel.toString()), "")) {
								noLuvRiskLevel
										.append("、")
										.append(Util.trim(l120s09a.getCustId()))
										.append("  ")
										.append(Util.trim(l120s09a
												.getCustName()));
							} else {
								noLuvRiskLevel
										.append(Util.trim(l120s09a.getCustId()))
										.append("  ")
										.append(Util.trim(l120s09a
												.getCustName()));
							}
						}
					}
				}

				// J-111-0278_05097_B1001 Web
				// e-Loan紐約分行企金限制eloan簽報書中，AMLCFT頁籤中，Name欄位僅能儲存英文、數字、標點符號，且會拒絕非英文數字之字型
				if (isCustNameOnlyEng) {

					String semiCustName = Util.toSemiCharString(String
							.valueOf(Util.trim(l120s09a.getCustName())));

					if (Util.notEquals(semiCustName, "")) {

						if (this.hasFullCharInStringText(semiCustName)) {
							if (Util.notEquals(
									Util.trim(custNameOnlyEng.toString()), "")) {
								custNameOnlyEng
										.append("、")
										.append(Util.trim(l120s09a.getCustId()))
										.append("  ")
										.append(Util.trim(l120s09a
												.getCustName()));
							} else {
								custNameOnlyEng
										.append(Util.trim(l120s09a.getCustId()))
										.append("  ")
										.append(Util.trim(l120s09a
												.getCustName()));
							}
						}

					}

				}

			}

			if (Util.notEquals(Util.trim(noEngNameCust.toString()), "")) {
				// AML.error018=欲掃描之名單「{0}」必須要有英文戶名。
				throw new CapMessageException(MessageFormat.format(
						pop.getProperty("AML.error018"),
						Util.trim(noEngNameCust.toString())), getClass());
			}

			// J-107-0059-001 Web e-Loan 授信簽報書與動審表之AML頁籤及列印檢核表時，增加引進風險等級
			if (chkScanDone && needChkCustRiskLvl) {
				if (Util.notEquals(Util.trim(noLuvRiskLevel.toString()), "")) {
					// AML.error020=本案借款人「{0}」於AML/CFT頁籤中，名單內之借戶風險等級不得為空白。
					throw new CapMessageException(MessageFormat.format(
							pop.getProperty("AML.error020"),
							Util.trim(noLuvRiskLevel.toString())), getClass());
				}
			}

			// J-111-0278_05097_B1001 Web
			// e-Loan紐約分行企金限制eloan簽報書中，AMLCFT頁籤中，Name欄位僅能儲存英文、數字、標點符號，且會拒絕非英文數字之字型
			if (Util.notEquals(Util.trim(custNameOnlyEng.toString()), "")) {
				// AML.error030=欲掃描之名單「{0}」本案戶名必須為英文、數字或標點符號。
				throw new CapMessageException(MessageFormat.format(
						pop.getProperty("AML.error030"),
						Util.trim(custNameOnlyEng.toString())), getClass());
			}
		}

		Map<String, String> amlLostMap = this
				.chkBlackListFullExistForDrawDown(mainId);
		StringBuffer amlLostStrBuff = new StringBuffer("");
		if (amlLostMap != null && !amlLostMap.isEmpty()) {
			for (String keySet : amlLostMap.keySet()) {
				String tKey = Util.trim(keySet);
				String tValue = Util.trim(amlLostMap.get(tKey));
				if (Util.equals(tKey, tValue)) {
					amlLostStrBuff.append(tKey).append("<BR>");
					;
				} else {
					amlLostStrBuff.append(tKey).append(" ").append(tValue)
							.append("<BR>");
				}

			}
		}
		if (Util.notEquals(amlLostStrBuff, "")) {
			// AML.error028=下列名單於洗錢防制頁籤無對應的黑名單掃描結果<BR>{0}
			throw new CapMessageException(
					MessageFormat.format(pop.getProperty("AML.error001"),
							amlLostStrBuff.toString()), getClass());
		}

		// J-107-0059-001 Web e-Loan 授信簽報書與動審表之AML頁籤及列印檢核表時，增加引進風險等級
		// 檢核0024-23往來業務是否有勾選正確

		// 放款 => 02 授信業務
		// 進口 => 03 進出口業務 (科目941,944)
		// 出口 => 03 進出口業務 (科目942,715,950)
		// 應收帳款 => 02 授信業務
		// 供應鏈融資=> 02 授信業務
		// 遠匯 => 01 存匯業務 / 0 3 進出口業務 (科目961,962,963,964)
		// 風險參與 => 03 進出口業務 (科目971)

		// 國內分行才要檢核
		if (chkScanDone && isNeedChkScanDone) {
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			String unitNo = (instead ? queryBrId : user.getUnitNo());
			boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchSrv
					.getBranch(unitNo).getBrNoFlag());
			if (!isOverSea) {

				// 動審表主要資料來源為MIS TABLE
				L160M01A l160m01a = this.findModelByMainId(L160M01A.class,
						mainId);

				List<L140M01A> l140m01as = new ArrayList<L140M01A>();

				// 動審表檢核要動用的額度明細表(排除不變、取消)
				Set<L160M01B> l160m01bs = l160m01a.getL160m01b();
				if (l160m01bs != null && !l160m01bs.isEmpty()) {
					for (L160M01B l160m01b : l160m01bs) {
						L140M01A l140m01a = this.findModelByMainId(
								L140M01A.class, l160m01b.getReMainId());
						if (l140m01a != null) {
							l140m01as.add(l140m01a);
						}
					}
				}

				String err002423BussTypeMsg = this.chkBusinessTypeIn002423(
						mainId, l140m01as, "2");

				if (Util.notEquals(err002423BussTypeMsg, "")) {
					// AML.error021=本案借款人檢核0024-23往來項目資料錯誤如下：<BR>{0}
					throw new CapMessageException(MessageFormat.format(
							pop.getProperty("AML.error021"),
							err002423BussTypeMsg), getClass());
				}
			}
		}

	}

	/**
	 * J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤 送呈主管時檢核黑名單是否有漏掉的
	 */
	public Map<String, String> chkBlackListFullExistForDrawDown(String mainId) {

		// 動審表主要資料來源為MIS TABLE
		L160M01A l160m01a = this.findModelByMainId(L160M01A.class, mainId);

		String ownBrId = l160m01a.getOwnBrId();
		boolean isObs = branchService.isOBSBranch(ownBrId);

		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS20APanel.class);

		Locale locale = LMSUtil.getLocale();

		Map<String, String> custRelationMap = null;
		custRelationMap = codetypeservice.findByCodeType("BlackListRelation",
				locale.toString());

		Map<String, Object> chkInsteadMap = this.checkInstead(mainId);
		boolean instead = MapUtils.getBooleanValue(chkInsteadMap, "instead",
				false);
		String queryBrId = MapUtils.getString(chkInsteadMap, "queryBrId");

		Map<String, String> resultMap = new HashMap<String, String>();
		Map<String, String> oldAmlListIdMap = new HashMap<String, String>();
		Map<String, String> oldAmlListNameMap = new HashMap<String, String>();
		List<L120S09A> l120s09as = this.findL120s09aByMainId(mainId);
		if (l120s09as != null && !l120s09as.isEmpty()) {
			for (L120S09A l120s09a : l120s09as) {
				String tCustId = Util.trim(l120s09a.getCustId());
				String tDupNo = Util.trim(l120s09a.getDupNo());
				String tCustName = Util.toSemiCharString(String.valueOf(Util
						.trim(l120s09a.getCustName())));
				String tCustEName = Util.toSemiCharString(Util.trim(l120s09a
						.getCustEName()));
				String fullKey = tCustId + tDupNo;
				if (Util.notEquals(fullKey, "")) {
					if (!oldAmlListIdMap.containsKey(fullKey)) {
						oldAmlListIdMap.put(fullKey, tCustEName);
					}
				}

				if (Util.notEquals(tCustName, "")) {
					if (!oldAmlListNameMap.containsKey(tCustName)) {
						oldAmlListNameMap.put(tCustName, tCustEName);
					}
				}

			}

		}

		String chkId = "";
		String chkName = "";
		Map<String, String> cntrCustIdMap = new HashMap<String, String>();
		Map<String, String> cntrAllCustIdMap = new HashMap<String, String>();
		List<L162S01A> listL162s01a = (List<L162S01A>) this.findListByMainId(
				L162S01A.class, mainId);

		for (L162S01A l162s01a : listL162s01a) {

			if (Util.equals(l162s01a.getRType(), UtilConstants.lngeFlag.共同借款人)) {

				// 借款人
				String bId = l162s01a.getRId();
				String bNo = l162s01a.getRDupNo();
				String bName = Util.toSemiCharString(Util.trim(l162s01a
						.getRName()));

				if (!cntrCustIdMap.containsKey(bId + bNo)) {

					cntrCustIdMap.put(bId + bNo, bName);

					// 借戶/共同借款人
					String borrKind = "";
					String borrKindDesc = "";
					if (Util.equals(bId, l162s01a.getCustId())
							&& Util.equals(bNo, l162s01a.getDupNo())) {
						// L120S09a.checkbox1=借戶
						// borrKind = pop.getProperty("L120S09a.checkbox1");
						borrKind = UtilConstants.Casedoc.L120s09aBlackListCtlTarget.借戶;
					} else {
						// L120S09a.checkbox2=共同借款人
						// borrKind = pop.getProperty("L120S09a.checkbox2");
						borrKind = UtilConstants.Casedoc.L120s09aBlackListCtlTarget.共同借款人;
					}

					borrKindDesc = Util.trim(custRelationMap.get(borrKind));

					chkId = bId + bNo;
					chkName = bName;
					if (Util.notEquals(chkId, "")) {
						if (!oldAmlListIdMap.containsKey(chkId)) {
							if (!resultMap.containsKey(chkId)) {

								resultMap.put(chkId, chkName + "(" + borrKindDesc
										+ ")");
							}
						}
					}

					// 負責人
					L164S01A l164s01a = this.findL164s01aByUniqueKey(mainId,
							bId, bNo);
					if (l164s01a != null) {
						String chairmanId = Util.trim(l164s01a.getChairmanId());
						String chairmanDupNo = Util.trim(l164s01a
								.getChairmanDupNo());
						String chairman = Util.toSemiCharString(Util
								.trim(l164s01a.getChairman()));
						chkId = chairmanId + chairmanDupNo;
						chkName = chairman;
						if (Util.notEquals(chkId, "")
								|| Util.notEquals(chkName, "")) {

							if (Util.notEquals(chkId, "")) {
								if (!oldAmlListIdMap.containsKey(chkId)) {
									if (!resultMap.containsKey(chkId)) {
										// L120S09a.checkbox3=借戶負責人
										resultMap
												.put(chkId,
														chkName
																+ "("
																+ Util.trim(custRelationMap
																		.get(UtilConstants.Casedoc.L120s09aBlackListCtlTarget.負責人)) // pop.getProperty("L120S09a.checkbox3")
																+ ")");
									}
								}
							} else {
								if (!oldAmlListNameMap.containsKey(chkName)) {
									if (!resultMap.containsKey(chkName)) {
										// L120S09a.checkbox3=借戶負責人
										resultMap
												.put(chkName,
														""
																+ "("
																+ Util.trim(custRelationMap
																		.get(UtilConstants.Casedoc.L120s09aBlackListCtlTarget.負責人)) // pop.getProperty("L120S09a.checkbox3")
																+ ")");
									}
								}
							}
						}
					}

					// 實質受益人
					List<L120S01P> listL120s01p = this
							.findL120s01pByMainIdAndCustIdWithRType(
									mainId,
									bId,
									bNo,
									UtilConstants.Casedoc.L120s09aBlackListCtlTarget.實質受益人);
					if (listL120s01p != null && !listL120s01p.isEmpty()) {
						for (L120S01P l120s01p : listL120s01p) {
							String rId = Util.trim(l120s01p.getRId());
							String rDupNo = Util.trim(l120s01p.getRDupNo());
							String rName = Util.toSemiCharString(Util
									.trim(l120s01p.getRName()));
							chkId = rId + rDupNo;
							chkName = rName;
							if (Util.notEquals(chkId, "")
									|| Util.notEquals(chkName, "")) {

								if (Util.notEquals(chkId, "")) {
									if (!oldAmlListIdMap.containsKey(chkId)) {
										if (!resultMap.containsKey(chkId)) {
											// L120S09a.checkbox7=實質受益人
											resultMap
													.put(chkId,
															chkName
																	+ "("
																	+ Util.trim(custRelationMap
																			.get(UtilConstants.Casedoc.L120s09aBlackListCtlTarget.實質受益人)) // pop.getProperty("L120S09a.checkbox7")
																	+ ")");
										}
									}
								} else {
									if (!oldAmlListNameMap.containsKey(chkName)) {
										if (!resultMap.containsKey(chkName)) {
											// L120S09a.checkbox7=實質受益人
											resultMap
													.put(chkName,
															""
																	+ "("
																	+ Util.trim(custRelationMap
																			.get(UtilConstants.Casedoc.L120s09aBlackListCtlTarget.實質受益人)) // pop.getProperty("L120S09a.checkbox7")
																	+ ")");
										}
									}
								}
							}
						}

					}

					// J-107-0070-001 Web e-Loan
					// 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
					// 高階管理人員
					if (this.needChkCtrlPeoBeforeSendBoss(queryBrId)) {

						List<L120S01P> listL120s01p_smgr = this
								.findL120s01pByMainIdAndCustIdWithRType(
										mainId,
										bId,
										bNo,
										UtilConstants.Casedoc.L120s09aBlackListCtlTarget.高階管理人員);
						if (listL120s01p_smgr != null
								&& !listL120s01p_smgr.isEmpty()) {
							for (L120S01P l120s01p : listL120s01p_smgr) {
								String rId = Util.trim(l120s01p.getRId());
								String rDupNo = Util.trim(l120s01p.getRDupNo());
								String rName = Util.toSemiCharString(Util
										.trim(l120s01p.getRName()));
								chkId = rId + rDupNo;
								chkName = rName;
								if (Util.notEquals(chkId, "")
										|| Util.notEquals(chkName, "")) {

									if (Util.notEquals(chkId, "")) {
										if (!oldAmlListIdMap.containsKey(chkId)) {
											if (!resultMap.containsKey(chkId)) {
												// L120S09a.checkbox10=高階管理人員
												resultMap
														.put(chkId,
																chkName
																		+ "("
																		+ Util.trim(custRelationMap
																				.get(UtilConstants.Casedoc.L120s09aBlackListCtlTarget.高階管理人員)) // pop.getProperty("L120S09a.checkbox7")
																		+ ")");
											}
										}
									} else {
										if (!oldAmlListNameMap
												.containsKey(chkName)) {
											if (!resultMap.containsKey(chkName)) {
												// L120S09a.checkbox10=高階管理人員
												resultMap
														.put(chkName,
																""
																		+ "("
																		+ Util.trim(custRelationMap
																				.get(UtilConstants.Casedoc.L120s09aBlackListCtlTarget.高階管理人員)) // pop.getProperty("L120S09a.checkbox7")
																		+ ")");
											}
										}
									}
								}
							}

						}
					}

					// J-108-0039_05097_B1001 Web e-Loan
					// 國內企金授信系統簽報、動審AML頁籤將借戶之「具控制權人」納入應查詢比對黑名單之對象。
					// 具控制權人
					if (this.needChkCtrlPeoBeforeSendBoss(queryBrId)) {

						List<L120S01P> listL120s01p_smgr = this
								.findL120s01pByMainIdAndCustIdWithRType(
										mainId,
										bId,
										bNo,
										UtilConstants.Casedoc.L120s09aBlackListCtlTarget.具控制權人);
						if (listL120s01p_smgr != null
								&& !listL120s01p_smgr.isEmpty()) {
							for (L120S01P l120s01p : listL120s01p_smgr) {
								String rId = Util.trim(l120s01p.getRId());
								String rDupNo = Util.trim(l120s01p.getRDupNo());
								String rName = Util.toSemiCharString(Util
										.trim(l120s01p.getRName()));
								chkId = rId + rDupNo;
								chkName = rName;
								if (Util.notEquals(chkId, "")
										|| Util.notEquals(chkName, "")) {

									if (Util.notEquals(chkId, "")) {
										if (!oldAmlListIdMap.containsKey(chkId)) {
											if (!resultMap.containsKey(chkId)) {
												// L120S09a.checkbox11=具控制權人
												resultMap
														.put(chkId,
																chkName
																		+ "("
																		+ Util.trim(custRelationMap
																				.get(UtilConstants.Casedoc.L120s09aBlackListCtlTarget.具控制權人)) // pop.getProperty("L120S09a.checkbox11")
																		+ ")");
											}
										}
									} else {
										if (!oldAmlListNameMap
												.containsKey(chkName)) {
											if (!resultMap.containsKey(chkName)) {
												// L120S09a.checkbox11=具控制權人
												resultMap
														.put(chkName,
																""
																		+ "("
																		+ Util.trim(custRelationMap
																				.get(UtilConstants.Casedoc.L120s09aBlackListCtlTarget.具控制權人)) // pop.getProperty("L120S09a.checkbox11")
																		+ ")");
											}
										}
									}
								}
							}

						}
					}

					if (Util.equals(borrKind,
							UtilConstants.Casedoc.L120s09aBlackListCtlTarget.借戶)) {

						// 關係企業
						// 再查關係企業
						List<?> rows5 = misElcrcoService.findElcrecomByIdDupnoForBlackList(isObs, bId, bNo);
						if (rows5 != null && !rows5.isEmpty()) {
							Iterator<?> it5 = rows5.iterator();
							while (it5.hasNext()) {
								Map<?, ?> dataMap5 = (Map<?, ?>) it5.next();

								String s04aId = Util.trim(String
										.valueOf(dataMap5.get("BAN")));
								String s04aNo = Util.trim(String
										.valueOf(dataMap5.get("DUPNO")));
								String s04aName = Util.toSemiCharString(Util
										.trim(String.valueOf(dataMap5
												.get("CNAME"))));
								String s04aEName = Util.toSemiCharString(Util
										.trim(String.valueOf(dataMap5
												.get("ENAME"))));
								if (Util.notEquals(s04aId, "")) {
									chkId = s04aId + s04aNo;
									chkName = s04aName;
									if (Util.notEquals(chkId, "")) {
										if (!oldAmlListIdMap.containsKey(chkId)) {
											if (!resultMap.containsKey(chkId)) {
												// L120S09a.checkbox6=關係企業
												resultMap
														.put(chkId,
																chkName
																		+ "("
																		+ Util.trim(custRelationMap
																				.get(UtilConstants.Casedoc.L120s09aBlackListCtlTarget.關係企業)) // pop.getProperty("L120S09a.checkbox6")
																		+ ")");
											}
										}
									}
								}

							}
						}
					}
				}
			}

			if (Util.equals(l162s01a.getRType(), UtilConstants.lngeFlag.連帶保證人)) {
				String gId = l162s01a.getRId();
				String gNo = l162s01a.getRDupNo();
				String gName = Util.toSemiCharString(Util.trim(l162s01a
						.getRName()));
				chkId = gId + gNo;
				chkName = gName;
				if (Util.notEquals(chkId, "")) {
					if (!oldAmlListIdMap.containsKey(chkId)) {
						if (!resultMap.containsKey(chkId)) {
							// L120S09a.checkbox4=連保人
							resultMap
									.put(chkId,
											chkName
													+ "("
													+ Util.trim(custRelationMap
															.get(UtilConstants.Casedoc.L120s09aBlackListCtlTarget.連保人)) // pop.getProperty("L120S09a.checkbox4")
													+ ")");
						}
					}
				}
			}

			if (Util.equals(l162s01a.getRType(), UtilConstants.lngeFlag.ㄧ般保證人)) {
				String gId = l162s01a.getRId();
				String gNo = l162s01a.getRDupNo();
				String gName = Util.toSemiCharString(Util.trim(l162s01a
						.getRName()));
				chkId = gId + gNo;
				chkName = gName;
				if (Util.notEquals(chkId, "")) {
					if (!oldAmlListIdMap.containsKey(chkId)) {
						if (!resultMap.containsKey(chkId)) {
							// L120S09a.checkbox8=一般保證人
							resultMap
									.put(chkId,
											chkName
													+ "("
													+ Util.trim(custRelationMap
															.get(UtilConstants.Casedoc.L120s09aBlackListCtlTarget.一般保證人)) // pop.getProperty("L120S09a.checkbox8")
													+ ")");
						}
					}
				}
			}

			if (Util.equals(l162s01a.getRType(), UtilConstants.lngeFlag.擔保品提供人)
					|| Util.equals(l162s01a.getRType(),
							UtilConstants.lngeFlag.連帶借款人)) {
				String gId = l162s01a.getRId();
				String gNo = l162s01a.getRDupNo();
				String gName = Util.toSemiCharString(Util.trim(l162s01a
						.getRName()));
				chkId = gId + gNo;
				chkName = gName;
				if (Util.notEquals(chkId, "")) {
					if (!oldAmlListIdMap.containsKey(chkId)) {
						if (!resultMap.containsKey(chkId)) {
							// L120S09a.checkbox5=擔保品提供人
							resultMap
									.put(chkId,
											chkName
													+ "("
													+ Util.trim(custRelationMap
															.get(UtilConstants.Casedoc.L120s09aBlackListCtlTarget.擔保品提供人)) // pop.getProperty("L120S09a.checkbox5")
													+ ")");
						}
					}
				}
			}

		}

		// 補強檢核************************************************************************************

		if (l160m01a != null) {
			Set<L160M01B> l160m01bs = l160m01a.getL160m01b();
			if (l160m01bs != null && !l160m01bs.isEmpty()) {
				for (L160M01B l160m01b : l160m01bs) {
					L140M01A l140m01a = this.findModelByMainId(L140M01A.class,
							l160m01b.getReMainId());
					if (l140m01a != null) {
						String mId = l140m01a.getCustId();
						String mNo = l140m01a.getDupNo();
						String mName = Util.toSemiCharString(Util.trim(l140m01a
								.getCustName()));
						chkId = mId + mNo;
						chkName = mName;
						if (Util.notEquals(chkId, "")) {
							if (!oldAmlListIdMap.containsKey(chkId)) {
								if (!resultMap.containsKey(chkId)) {
									// L120S09a.checkbox1=借戶
									resultMap
											.put(chkId,
													chkName
															+ "("
															+ Util.trim(custRelationMap
																	.get(UtilConstants.Casedoc.L120s09aBlackListCtlTarget.借戶))// pop.getProperty("L120S09a.checkbox1")
															+ ")");
								}
							}
						}

						// J-107-0164_05097_B1001 Web
						// e-Loan國內企金授信額度明細表新增應收帳款簽案資訊
						// 應收帳款無追索權買方
						List<L140M01S> listL140m01s = this
								.findL140m01sByMainIdType(l140m01a.getMainId(),
										UtilConstants.L140m01sType.本案應收帳款買方額度資訊);
						if (listL140m01s != null && !listL140m01s.isEmpty()) {
							for (L140M01S l140m01s : listL140m01s) {
								if (l140m01s.getItemSeq() != 99999) {
									String sId = l140m01s.getCustId();
									String sNo = l140m01s.getDupNo();
									String sName = Util.toSemiCharString(Util
											.trim(l140m01s.getCustName()));

									chkId = sId + sNo;
									chkName = sName;

									if (Util.notEquals(chkId, "")) {
										if (!oldAmlListIdMap.containsKey(chkId)) {
											if (!resultMap.containsKey(chkId)) {
												// 應收帳款買方無追索
												resultMap
														.put(chkId,
																chkName
																		+ "("
																		+ Util.trim(custRelationMap
																				.get(UtilConstants.Casedoc.L120s09aBlackListCtlTarget.應收帳款買方無追索))
																		// pop.getProperty("L120S09a.checkbox2")
																		+ ")");
											}
										}
									}

								}

							}
						}

					}
				}
			}
		}

		return resultMap;
	}

	/**
	 * J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤 (動審表)
	 * 
	 * @param mainId
	 * @throws CapException
	 */
	@Override
	public String chkMainBorrowerInL162S01A(String mainId,
			LinkedHashMap<String, String> cntrNoMainBorrowerMap)
			throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS20APanel.class);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		StringBuffer errBorrowerAMLEmptyField = new StringBuffer("");

		StringBuffer errMsg = new StringBuffer("");
		StringBuffer errNoConfirm = new StringBuffer("");
		StringBuffer errNoL120s01p = new StringBuffer("");
		String dataFrom = "0024";
		Map<String, Object> chkInsteadMap = this.checkInstead(mainId);
		boolean instead = MapUtils.getBooleanValue(chkInsteadMap, "instead",
				false);
		String queryBrId = MapUtils.getString(chkInsteadMap, "queryBrId");
		String unitNo = (instead ? queryBrId : user.getUnitNo());
		boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchSrv
				.getBranch(unitNo).getBrNoFlag());

		if (cntrNoMainBorrowerMap != null && !cntrNoMainBorrowerMap.isEmpty()) {
			for (String cntrNo : cntrNoMainBorrowerMap.keySet()) {

				String chkValue = Util.trim(cntrNoMainBorrowerMap.get(cntrNo));
				if (Util.notEquals(chkValue, "")) {

					String[] chkKeyArr = chkValue.split("-");
					String chkId = chkKeyArr[0];
					String chkDupNo = chkKeyArr[1];

					L162S01A l162s01a = l162s01aDao.findByUniqueKey(mainId,
							chkId, chkDupNo, cntrNo, chkId, chkDupNo);

					if (l162s01a == null) {

						errBorrowerAMLEmptyField.append(Util.equals(
								errBorrowerAMLEmptyField.toString(), "") ? ""
								: "、");
						errBorrowerAMLEmptyField.append(cntrNo + "(ID:" + chkId
								+ chkDupNo + ")");
					}
				}
			}
		}
		if (Util.notEquals(errBorrowerAMLEmptyField, "")) {
			// AML.error009=本次動用之額度與主借款人{0}未列於相關報表->主從債務人資料表中。
			errMsg.append(Util.equals(errMsg, "") ? "" : "<BR>");
			errMsg.append(MessageFormat.format(pop.getProperty("AML.error009"),
					errBorrowerAMLEmptyField.toString()));
		}

		return errMsg.toString();

	}

	// J-106-0029-003 洗錢防制-新增實質受益人
	@Override
	public List<L120S01P> findL120s01pByMainIdWithoutRType(String mainId) {
		// 透過MainId取得多筆資料
		return l120s01pdao.findByMainIdWithoutRType(mainId);
	}

	@Override
	public List<L120M01E> findL120m01eByMainId(String mainId) {
		return l120m01eDao.findByMainId(mainId);
	}

	/**
	 * J-106-0029-002/003/004 洗錢防制-新增洗錢防制頁籤 新增實質受益人 動審表新增洗錢防制頁籤
	 * 判斷登入分行是否需檢核無實質受益人不得送呈主管
	 * 
	 * @return
	 */
	@Override
	public boolean needChkBeneficiaryBeforeSendBoss(String QueryBrId) {
		boolean isNeed = true;

		// 後台管理->系統設定維護->LMS_J1060029_SEND_BOSS_CHK_OFF SG,AU 取消檢核無實質受益人不得送呈主管

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String countryType = Util.trim(branchSrv.getBranch(QueryBrId)
				.getCountryType());

		String notChkCountry = Util.trim(lmsService
				.getSysParamDataValue("COM_J1060029_SEND_BOSS_CHK_OFF"));

		if (Util.notEquals(notChkCountry, "")
				&& Util.notEquals(countryType, "")) {
			for (String xx : notChkCountry.split(",")) {
				if (Util.equals(xx, countryType)) {
					isNeed = false;
					break;
				}

				if (Util.notEquals(countryType, "TW") && Util.equals(xx, "OV")) {
					// OV代表所有海外分行都不擋
					isNeed = false;
					break;
				}
			}
		}

		return isNeed;
	}

	/**
	 * 判斷關係戶是否需要統編 J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	 * 
	 * @param custRelationStr
	 * @return
	 */
	@Override
	public boolean isCustRelationNeedCustId(String custRelationStr) {
		boolean isNeed = true;

		// J-107-0070-001 Web e-Loan 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
		// 授管處趙晉毅 黃啟煒 取消關係企業檢核需要ID(有不用於資料建檔建關係企業)
		// J-108-0039_05097_B1001 Web e-Loan
		// 國內企金授信系統簽報、動審AML頁籤將借戶之「具控制權人」納入應查詢比對黑名單之對象。
		if (Util.notEquals(custRelationStr, "")
				&& (Util.equals(custRelationStr,
						UtilConstants.Casedoc.L120s09aBlackListCtlTarget.實質受益人)
						|| Util.equals(
								custRelationStr,
								UtilConstants.Casedoc.L120s09aBlackListCtlTarget.關係企業)
						|| Util.equals(
								custRelationStr,
								UtilConstants.Casedoc.L120s09aBlackListCtlTarget.高階管理人員)
						|| Util.equals(
								custRelationStr,
								UtilConstants.Casedoc.L120s09aBlackListCtlTarget.具控制權人) || Util
						.equals(custRelationStr,
								UtilConstants.Casedoc.L120s09aBlackListCtlTarget.負責人))) {
			// 負責人 實質受益人 高階管理人員 具控制權人 可以不用輸入ID
			isNeed = false;
		}

		return isNeed;
	}

	/**
	 * J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @param custName
	 * @param relation
	 */
	@Override
	public void reSetL120S09A(String mainId, String custId, String dupNo,
			String custName, String newRelation, String custEName,
			String country) {

		List<L120S09A> tl120s09as = null;
		custName = Util.toSemiCharString(Util.trim(custName));
		custEName = Util.toSemiCharString(Util.trim(custEName));

		if (Util.equals(Util.trim(custId), "")
				&& Util.equals(Util.trim(dupNo), "")
				&& Util.equals(Util.trim(custName), "")) {
			return;
		}

		if (Util.notEquals(custId, "") && Util.notEquals(dupNo, "")) {
			// BY ID
			tl120s09as = this.findListL120s09aByCustId(mainId, custId, dupNo);

			// 沒有ID再用戶名查有問題，因為73251209 測試環境很多相同戶名
			// if (tl120s09as == null || tl120s09as.isEmpty()) {
			// // BY CUSTNAME
			// if (Util.notEquals(custName, "")) {
			// tl120s09as = amlRelateService.findListL120s09aByCustName(
			// mainId, custName);
			// }
			//
			// }
		} else {
			// BY CUSTNAME
			if (Util.notEquals(custName, "")) {
				tl120s09as = this.findListL120s09aByCustName(mainId, custName);
			} else {
				tl120s09as = null;
			}

		}

		if (tl120s09as != null && !tl120s09as.isEmpty()) {
			for (L120S09A l120s09a : tl120s09as) {
				String custRelation = l120s09a.getCustRelation();

				String[] item = custRelation.split(",");
				List<String> asList = Arrays.asList(item);
				// List<String> a = new ArrayList<String>();

				String[] newRelationItem = newRelation.split(",");
				for (int n = newRelationItem.length - 1; n >= 0; n = n - 1) {
					String relation = newRelationItem[n];

					if (asList.contains(relation)) {
						continue;
					} else {

						if (Util.equals(custRelation, "")) {
							custRelation = relation;
						} else {
							custRelation = custRelation + "," + relation;
						}
					}

					String[] newItem = custRelation.split(",");

					int i, j;
					String tmp;
					for (i = newItem.length - 1; i >= 0; i = i - 1) {
						for (j = 0; j < i; j = j + 1) {
							// if (newItem[j] > newItem[i])// 換（"小於"是由大到小）
							if (Util.parseInt((String) newItem[j]) > Util
									.parseInt((String) newItem[i]))// 換（"小於"是由大到小）
							{
								tmp = newItem[j];
								newItem[j] = newItem[i];
								newItem[i] = tmp;
							}
						}
					}

					StringBuffer itemBuf = new StringBuffer("");
					for (String tItem : newItem) {
						itemBuf.append(Util.equals(itemBuf, "") ? "" : ",");
						itemBuf.append(tItem);
					}

					if (Util.notEquals(custEName, "")) {
						if (Util.equals(Util.trim(l120s09a.getCustEName()), "")) {
							l120s09a.setCustEName(custEName);
						}
					}

					l120s09a.setCustRelation(itemBuf.toString());

					if (Util.equals(l120s09a.getCustId(), "")
							&& Util.equals(l120s09a.getDupNo(), "")) {
						if (Util.notEquals(custId, "")
								&& Util.notEquals(dupNo, "")) {
							l120s09a.setCustId(Util.trim(custId));
							l120s09a.setDupNo(Util.trim(dupNo));
						}
					}

					if (Util.notEquals(Util.trim(country), "")) {
						l120s09a.setCountry(Util.trim(country));
					}

					lmsService.save(l120s09a);

				}

			}
		} else {
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			L120S09A l120s09a = new L120S09A();
			l120s09a.setMainId(mainId);
			l120s09a.setCustId(custId);
			l120s09a.setDupNo(dupNo);
			l120s09a.setCustName(custName); // 已經轉半形
			l120s09a.setCustEName(custEName); // 已經轉半形
			l120s09a.setCustRelation(newRelation);
			l120s09a.setCountry(Util.trim(country));
			l120s09a.setChkYN(UtilConstants.DEFAULT.否);
			l120s09a.setBlackListCode("");
			l120s09a.setMemo("");
			l120s09a.setCreateTime(CapDate.getCurrentTimestamp());
			l120s09a.setCreator(user.getUserId());
			l120s09a.setCreateBY(UtilConstants.Casedoc.L120s04aCreateBY.系統產生);

			// J-106-0238-001
			// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
			l120s09a.setCheckSeq("");
			l120s09a.setCm1AmlStatus("");
			lmsService.save(l120s09a);
		}

	}

	/**
	 * J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	 */
	@Override
	public void getLMSAndCesDocAMLCustNameMap(L120M01A l120m01a,
			Map<String, String> lmsAndCesAmlListIdCNameMap,
			Map<String, String> lmsAndCesAmlListIdENameMap,
			Map<String, String> lmsAndCesAmlListNameMap) {

		// 1.先抓簽報書AML名稱
		if (Util.equals(l120m01a.getDocStatus(), "05O")
				|| Util.equals(l120m01a.getDocStatus(), "06O")) {

			List<L120S09A> l120s09as = this.findL120s09aByMainId(l120m01a
					.getMainId());

			if (l120s09as != null && !l120s09as.isEmpty()) {
				for (L120S09A l120s09a : l120s09as) {
					String tCustId = Util.trim(l120s09a.getCustId());
					String tDupNo = Util.trim(l120s09a.getDupNo());
					String tCustName = Util.toSemiCharString(String
							.valueOf(Util.trim(l120s09a.getCustName())));
					String tCustEName = Util.toSemiCharString(Util
							.trim(l120s09a.getCustEName()));
					String fullKey = tCustId + tDupNo;
					if (Util.notEquals(fullKey, "")) {
						if (!lmsAndCesAmlListIdCNameMap.containsKey(fullKey)) {
							lmsAndCesAmlListIdCNameMap.put(fullKey, tCustName);
						}

						if (!lmsAndCesAmlListIdENameMap.containsKey(fullKey)) {
							lmsAndCesAmlListIdENameMap.put(fullKey, tCustEName);
						}
					}

					if (Util.notEquals(tCustName, "")) {
						if (!lmsAndCesAmlListNameMap.containsKey(tCustName)) {
							lmsAndCesAmlListNameMap.put(tCustName, tCustEName);
						}
					}

				}

			}
		}

		// 2.再抓CES資信簡表AML名稱
		List<L120M01E> listL120m01e = this.findL120m01eByMainId(l120m01a
				.getMainId());
		List<String> ces120MainIds = new ArrayList<String>();
		for (L120M01E l120m01e : listL120m01e) {
			if (UtilConstants.Casedoc.L120m01eDocType.徵信報告書.equals(Util
					.trim(l120m01e.getDocType()))) {
				if (Util.notEquals(Util.trim(l120m01e.getDocOid()), "")) {
					String c140MainId = Util.trim(l120m01e.getDocOid());
					String c120MainId = "";

					List<Map<String, Object>> temp140List = eloanDbBaseService
							.C140M01A_selCustname2(c140MainId);
					for (Map<String, Object> map : temp140List) {
						Map<String, Object> data = new HashMap<String, Object>();
						c120MainId = Util.trim(map.get("C120M01A_MAINID"));
						break;
					}

					if (Util.notEquals(c120MainId, "")) {
						if (!ces120MainIds.contains(c120MainId)) {
							ces120MainIds.add(c120MainId);
						}
					}
				}
			} else if (UtilConstants.Casedoc.L120m01eDocType.資信簡表.equals(Util
					.trim(l120m01e.getDocType()))) {

				String c120MainId = Util.trim(l120m01e.getDocOid());
				if (!ces120MainIds.contains(c120MainId)) {
					ces120MainIds.add(c120MainId);
				}
			}
		}

//		StringBuilder cesMainIds = new StringBuilder();
//		if (ces120MainIds != null && !ces120MainIds.isEmpty()) {
//			for (String cesMainId : ces120MainIds) {
//				cesMainIds.append(cesMainIds.length() > 0 ? "," : "");
//				cesMainIds.append("'");
//				cesMainIds.append(cesMainId);
//				cesMainIds.append("'");
//			}
//		}

		if (CollectionUtils.isNotEmpty(ces120MainIds)) {
			List<Map<String, Object>> ces120ListById = eloanDbBaseService
					.findC120S01D_selectGroupByCustId(ces120MainIds);
			for (Map<String, Object> idMap : ces120ListById) {
				String tCustId = Util.trim(MapUtils.getString(idMap, "CUSTID",
						""));
				String tDupNo = Util.trim(MapUtils
						.getString(idMap, "DUPNO", ""));
				String tCustName = Util.toSemiCharString(Util.trim(MapUtils
						.getString(idMap, "CUSTNAME", "")));
				String tCustEName = Util.toSemiCharString(Util.trim(MapUtils
						.getString(idMap, "CUSTENAME", "")));
				String fullKey = tCustId + tDupNo;
				if (Util.notEquals(fullKey, "")) {
					if (!lmsAndCesAmlListIdCNameMap.containsKey(fullKey)) {
						lmsAndCesAmlListIdCNameMap.put(fullKey, tCustName);
					}

					if (!lmsAndCesAmlListIdENameMap.containsKey(fullKey)) {
						lmsAndCesAmlListIdENameMap.put(fullKey, tCustEName);
					}

				}

			}

			List<Map<String, Object>> ces120ListByName = eloanDbBaseService
					.findC120S01D_selectGroupByCustName(ces120MainIds);
			for (Map<String, Object> nameMap : ces120ListByName) {
				String tCustId = Util.trim(MapUtils.getString(nameMap,
						"CUSTID", ""));
				String tDupNo = Util.trim(MapUtils.getString(nameMap, "DUPNO",
						""));
				String tCustName = Util.toSemiCharString(Util.trim(MapUtils
						.getString(nameMap, "CUSTNAME", "")));
				String tCustEName = Util.toSemiCharString(Util.trim(MapUtils
						.getString(nameMap, "CUSTENAME", "")));
				String fullKey = tCustName;
				if (Util.notEquals(fullKey, "")) {
					if (!lmsAndCesAmlListNameMap.containsKey(fullKey)) {
						lmsAndCesAmlListNameMap.put(fullKey, tCustEName);
					}
				}
			}

		}
	}

	/**
	 * J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	 * 
	 * @param mode
	 *            1: ALL NAME(中+英文戶名) 2: ONLY ENAME(僅英文戶名)
	 * @param mainId
	 *            動審表或簽報書MAINID，串L120S01P用
	 * @param l120m01a
	 * @param custRelation
	 * @param custId
	 * @param dupNo
	 * @param custName
	 * @param custEName
	 * @return
	 * @throws CapException
	 */
	@Override
	public Map<String, String> queryL120s09aNewCustNameForAML(String mode,
			String mainId, L120M01A l120m01a, String custRelation,
			String custId, String dupNo, String custName, String custEName)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS20APanel.class);
		Map<String, String> result = new HashMap<String, String>();

		String caseRptMainId = "";
		String mainCustId = "";
		String mainDupNo = "";

		mainCustId = l120m01a.getCustId();
		mainDupNo = l120m01a.getDupNo();
		caseRptMainId = l120m01a.getMainId();

		String newCustName = "";
		String newCustEName = "";

		String[] strs = custRelation.split(",");

		// 檢核是否輸入統編
		boolean needCustId = false;
		boolean hasSUP1 = false; // 有負責人
		boolean hasRelateCorp = false; // 有關係企業
		boolean hasRealMan = false; // 有實質受益人
		// J-107-0070-001 Web e-Loan 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
		boolean hasSeniorMgr = false; // 有高階管理人員
		// J-108-0039_05097_B1001 Web e-Loan
		// 國內企金授信系統簽報、動審AML頁籤將借戶之「具控制權人」納入應查詢比對黑名單之對象。
		boolean hasCtrlPeo = false; // 有高階管理人員
		List<String> list = new ArrayList<String>();
		for (int i = 0; i < strs.length; i++) {
			if (Util.notEquals(strs[i], "")
					&& this.isCustRelationNeedCustId(strs[i])) {
				// 負責人實質受益人可以不用輸入ID
				needCustId = true;
			}

			if (Util.equals(strs[i],
					UtilConstants.Casedoc.L120s09aBlackListCtlTarget.負責人)) {
				// 負責人
				hasSUP1 = true;
			}

			if (Util.equals(strs[i],
					UtilConstants.Casedoc.L120s09aBlackListCtlTarget.關係企業)) {
				// 關係企業
				hasRelateCorp = true;
			}

			if (Util.equals(strs[i],
					UtilConstants.Casedoc.L120s09aBlackListCtlTarget.實質受益人)) {
				// 實質受益人
				hasRealMan = true;
			}

			// J-107-0070-001 Web e-Loan 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
			if (Util.equals(strs[i],
					UtilConstants.Casedoc.L120s09aBlackListCtlTarget.高階管理人員)) {
				// 實質受益人
				hasSeniorMgr = true;
			}

			// J-107-0070-001 Web e-Loan 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
			if (Util.equals(strs[i],
					UtilConstants.Casedoc.L120s09aBlackListCtlTarget.具控制權人)) {
				// 實質受益人
				hasCtrlPeo = true;
			}
		}

		/*
		 * mode 1: ALL NAME(CNAME+ENAME) (判斷newCustName為空時才查資料) 2: ONLY ENAME
		 * (判斷newCustEName為空時才查資料)
		 */

		/***
		 * 本來有以下這段，但是授管處蕭鳳慈 反映問題，所以拿掉改到最後再做， 因為負責人0024 ELCUS25 中文戶名SUP1CNM
		 * 放英文會被截掉，導致黑名單查詢結果有問題 例如: SUP1CNM=ＺＡＩＮＡＬ　ＡＢＩ ; SUP1ENM=ZAINAL ABIDIN
		 * PUTIH
		 */
		// MODE = 1 ALL ENAME 模式，最後判斷--如果中文戶名都是英數字，則塞到英文戶名
		// MODE = 2 ONLY ENAME 模式，一開始就判斷--如果中文戶名都是英數字，則塞到英文戶名
		// if (Util.equals(Util.equals(mode, "2") ? newCustEName : newCustName,
		// "")) {
		// if (Util.notEquals(custName, "")) {
		// if (Util.equals(mode, "2")) {
		// // MODE = 2 ONLY ENAME
		// String xCustName = Util.toSemiCharString(Util
		// .trim(custName));
		// if (xCustName.length() == xCustName.getBytes().length) {
		// newCustEName = xCustName;
		// }
		// }
		// }
		// }

		if (Util.notEquals(custId, "") && Util.notEquals(dupNo, "")) {
			// 1.查0024
			if (Util.equals(
					Util.equals(mode, "2") ? newCustEName : newCustName, "")) {
				Map<String, Object> cust = null;
				if (Util.equals(mode, "2")) {
					cust = misCustdataService.findAllByByCustIdAndDupNo(custId,
							dupNo);

				} else {
					cust = customerSrv.findByIdDupNo(custId, dupNo);

				}
				if (cust != null && !cust.isEmpty()) {
					String cname = (String) cust.get("CNAME");
					cname = Util.toSemiCharString(cname);
					String ename = (String) cust.get("ENAME");
					String finalName = CustomerIdCheckUtil.getName(custId,
							cname, ename);

					newCustName = finalName;
					newCustEName = ename;
				}

			}

			// 2.查elcus25負責人英文欄位
			// select SUP1ID,SUP1DUPNO, SUP1CNM,SUP1ENM from mis.ELCUS25 fetch
			// first 1 row only

			if (Util.equals(
					Util.equals(mode, "2") ? newCustEName : newCustName, "")) {
				if (hasSUP1) {
					// 再查負責人
					List<L120S01B> l120s01bs = (List<L120S01B>) this
							.findListByMainId(L120S01B.class, caseRptMainId);
					// List<L120S01B> listToSave = new
					// ArrayList<L120S01B>();
					if (l120s01bs != null && !l120s01bs.isEmpty()) {
						for (L120S01B l120s01b : l120s01bs) {

							String s01CustId = l120s01b.getCustId();
							String s01DupNo = l120s01b.getDupNo();

							List<Map<String, Object>> listMap = misElcus25Service
									.findMiselcus25(s01CustId, s01DupNo);

							// 負責人
							String aLoanChairManID = "";
							String aLoanChairmanDupNo = "";
							String aLoanChairManName = "";
							String aLoanChairManEName = "";

							for (Map<String, Object> perMap : listMap) {
								aLoanChairManID = Util.trim(perMap
										.get("SUP1ID"));
								aLoanChairmanDupNo = Util.trim(perMap
										.get("SUP1DUPNO"));
								aLoanChairManName = Util.toSemiCharString(Util
										.trim(perMap.get("SUP1CNM")));
								aLoanChairManEName = Util.trim(perMap
										.get("SUP1ENM"));

								if (Util.notEquals(aLoanChairManID, "")
										&& Util.notEquals(aLoanChairmanDupNo,
												"")) {
									if (Util.equals(custId, aLoanChairManID)
											&& Util.equals(dupNo,
													aLoanChairmanDupNo)) {
										newCustName = aLoanChairManName;
										newCustEName = aLoanChairManEName;
									}
								} else {
									if (Util.notEquals(aLoanChairManName, "")) {
										if (Util.equals(custName,
												aLoanChairManName)) {
											newCustName = aLoanChairManName;
											newCustEName = aLoanChairManEName;
										}
									} else if (Util.notEquals(
											aLoanChairManEName, "")) {
										if (Util.equals(custName,
												aLoanChairManEName)) {
											newCustName = aLoanChairManEName;
											newCustEName = aLoanChairManEName;
										}
									}
								}
							}

						}

					}
				}
			}

			// 3.查關係企業英文戶名欄位
			if (Util.equals(
					Util.equals(mode, "2") ? newCustEName : newCustName, "")) {
				if (hasRelateCorp) {
					// 再查關係企業
					List<?> rows5 = misElcrcoService
							.findElcrecomByCustIdAndRCustId(mainCustId,
									mainDupNo, custId, dupNo);
					if (rows5 != null && !rows5.isEmpty()) {
						Iterator<?> it5 = rows5.iterator();
						while (it5.hasNext()) {
							Map<?, ?> dataMap5 = (Map<?, ?>) it5.next();
							newCustName = Util
									.toSemiCharString(Util.trim(String
											.valueOf(dataMap5.get("CNAME"))));
							newCustEName = Util
									.toSemiCharString(Util.trim(String
											.valueOf(dataMap5.get("ENAME"))));

							if (Util.equals(newCustEName, "")) {
								if (Util.notEquals(newCustName, "")) {
									// 如果中文戶名都是英數字，那就直接塞到英文戶名欄位
									if (newCustName.length() == newCustName
											.getBytes().length) {
										newCustEName = newCustName;
									}

								}
							}

						}
					}
				}
			}

		}

		// 查實質受益人
		// 以查詢L120S01P為主，不再查MIS
		if (Util.equals(Util.equals(mode, "2") ? newCustEName : newCustName, "")) {
			if (hasRealMan) {

				List<L120S01P> l120s01ps = this.findL120s01pByMainIdWithRType(
						mainId,
						UtilConstants.Casedoc.L120s09aBlackListCtlTarget.實質受益人);

				for (L120S01P l120s01p : l120s01ps) {

					String rid = l120s01p.getRId();
					String rDupNo = l120s01p.getRDupNo();
					String rName = l120s01p.getRName();
					String rEName = l120s01p.getREName();

					if (Util.notEquals(rid, "") && Util.notEquals(rDupNo, "")) {
						if (Util.equals(custId, rid)
								&& Util.equals(dupNo, rDupNo)) {
							newCustName = rName;
							newCustEName = rEName;
						}
					} else {
						if (Util.notEquals(rName, "")) {
							if (Util.equals(custName, rName)) {
								newCustName = rName;
								newCustEName = rEName;
							}
						} else if (Util.notEquals(rEName, "")) {
							if (Util.equals(custName, rEName)) {
								newCustName = rEName;
								newCustEName = rEName;
							}
						}
					}

				}

				// 再查實質受益人
				// boolean isOverSea =
				// UtilConstants.BrNoType.國外.equals(branchSrv
				// .getBranch(user.getUnitNo()).getBrNoFlag());
				//
				// List<L120S01B> l120s01bs = (List<L120S01B>) amlRelateService
				// .findListByMainId(L120S01B.class, caseRptMainId);
				//
				// if (l120s01bs != null && !l120s01bs.isEmpty()) {
				// for (L120S01B l120s01b : l120s01bs) {
				//
				// String s01CustId = l120s01b.getCustId();
				// String s01DupNo = l120s01b.getDupNo();
				//
				// if (!isOverSea) {
				// // 國內
				// Map<String, Object> cmfData = misDbService
				// .selCMFLUNBNByCustId(s01CustId, s01DupNo);
				//
				// String BUSCD = Util.trim(MapUtils.getString(
				// cmfData, "BUSCD"));
				// String LUV_BEN_CONFIRM = Util.trim(MapUtils
				// .getString(cmfData, "LUV_BEN_CONFIRM"));
				//
				// if (Util.equals(LUV_BEN_CONFIRM, "Y")) {
				//
				// int count = 0;
				// for (int i = 0; i < 4; i++) {
				// String LUB_BEN_ID = Util.trim(
				// MapUtils.getString(cmfData,
				// "LUB_BEN" + i + "_ID"))
				// .toUpperCase();
				// String LUB_BEN_CNAME = Util
				// .toSemiCharString(Util.trim(MapUtils
				// .getString(cmfData,
				// "LUB_BEN" + i
				// + "_CNAME")));
				// String LUB_BEN_ENAME = Util.trim(MapUtils
				// .getString(cmfData, "LUB_BEN" + i
				// + "_ENAME"));
				// String LUB_BEN_BIRTH_DATE = Util
				// .trim(MapUtils.getString(cmfData,
				// "LUB_BEN" + i
				// + "_BIRTH_DATE"));
				// String LUB_BEN_NATION = Util.trim(MapUtils
				// .getString(cmfData, "LUB_BEN" + i
				// + "_NATION"));
				// String LUB_BEN_PEPS = Util.trim(MapUtils
				// .getString(cmfData, "LUB_BEN" + i
				// + "_PEPS"));
				//
				// if (Util.notEquals(LUB_BEN_ID, "")) {
				// if (Util.equals(custId, LUB_BEN_ID)) {
				// newCustName = LUB_BEN_CNAME;
				// newCustEName = LUB_BEN_ENAME;
				// }
				// } else {
				// if (Util.notEquals(LUB_BEN_CNAME, "")) {
				// if (Util.equals(custName,
				// LUB_BEN_CNAME)) {
				// newCustName = LUB_BEN_CNAME;
				// newCustEName = LUB_BEN_ENAME;
				// }
				// } else if (Util.notEquals(
				// LUB_BEN_ENAME, "")) {
				// if (Util.equals(custName,
				// LUB_BEN_ENAME)) {
				// newCustName = LUB_BEN_ENAME;
				// newCustEName = LUB_BEN_ENAME;
				// }
				// }
				// }
				//
				// }
				//
				// }
				//
				// } else {
				// // 海外
				// // DWOTS 海外實質受益人
				// }
				// }
				// }
			}
		}

		// J-107-0070-001 Web e-Loan 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
		// 高階管理人員
		// 以查詢L120S01P為主，不再查MIS
		if (Util.equals(Util.equals(mode, "2") ? newCustEName : newCustName, "")) {
			if (hasSeniorMgr) {

				List<L120S01P> l120s01ps = this
						.findL120s01pByMainIdWithRType(
								mainId,
								UtilConstants.Casedoc.L120s09aBlackListCtlTarget.高階管理人員);

				for (L120S01P l120s01p : l120s01ps) {

					String rid = l120s01p.getRId();
					String rDupNo = l120s01p.getRDupNo();
					String rName = l120s01p.getRName();
					String rEName = l120s01p.getREName();

					if (Util.notEquals(rid, "") && Util.notEquals(rDupNo, "")) {
						if (Util.equals(custId, rid)
								&& Util.equals(dupNo, rDupNo)) {
							newCustName = rName;
							newCustEName = rEName;
						}
					} else {
						if (Util.notEquals(rName, "")) {
							if (Util.equals(custName, rName)) {
								newCustName = rName;
								newCustEName = rEName;
							}
						} else if (Util.notEquals(rEName, "")) {
							if (Util.equals(custName, rEName)) {
								newCustName = rEName;
								newCustEName = rEName;
							}
						}
					}

				}
			}
		}

		// J-108-0039_05097_B1001 Web e-Loan
		// 國內企金授信系統簽報、動審AML頁籤將借戶之「具控制權人」納入應查詢比對黑名單之對象。
		// 具控制權人
		// 以查詢L120S01P為主，不再查MIS
		if (Util.equals(Util.equals(mode, "2") ? newCustEName : newCustName, "")) {
			if (hasCtrlPeo) {

				List<L120S01P> l120s01ps = this.findL120s01pByMainIdWithRType(
						mainId,
						UtilConstants.Casedoc.L120s09aBlackListCtlTarget.具控制權人);

				for (L120S01P l120s01p : l120s01ps) {

					String rid = l120s01p.getRId();
					String rDupNo = l120s01p.getRDupNo();
					String rName = l120s01p.getRName();
					String rEName = l120s01p.getREName();

					if (Util.notEquals(rid, "") && Util.notEquals(rDupNo, "")) {
						if (Util.equals(custId, rid)
								&& Util.equals(dupNo, rDupNo)) {
							newCustName = rName;
							newCustEName = rEName;
						}
					} else {
						if (Util.notEquals(rName, "")) {
							if (Util.equals(custName, rName)) {
								newCustName = rName;
								newCustEName = rEName;
							}
						} else if (Util.notEquals(rEName, "")) {
							if (Util.equals(custName, rEName)) {
								newCustName = rEName;
								newCustEName = rEName;
							}
						}
					}

				}
			}
		}

		// 以上查MIS
		// 相關DB都沒有最新資料時****************************************************************************************

		// 最後英文戶名沒資料時，如果中文戶名都是英數字，則塞到英文戶名
		// MODE = 1 ALL ENAME 模式，最後判斷--如果中文戶名都是英數字，則塞到英文戶名
		// MODE = 2 ONLY ENAME 模式，一開始就判斷--如果中文戶名都是英數字，則塞到英文戶名
		if (Util.equals(Util.equals(mode, "2") ? newCustEName : newCustName, "")) {
			if (Util.notEquals(custName, "")) {
				// if (Util.equals(mode, "1")) {
				// MODE = 1 ALL ENAME
				String xCustName = Util.toSemiCharString(Util.trim(custName));
				if (xCustName.length() == xCustName.getBytes().length) {
					newCustEName = xCustName;
				}
				// }
			}
		}

		// 查詢戶名結束*************************************************************************************************************************

		if (Util.equals(mode, "2")) {
			// MODE = 2 ONLY ENAME
			// 因為整批引進一定會有custName，所以沿用帶入的custName，不更新
			result.put("newCustName",
					Util.toSemiCharString(Util.trim(custName)));
		} else {
			// MODE = 1 ALL ENAME
			result.put("newCustName",
					Util.toSemiCharString(Util.trim(newCustName)));
		}

		result.put("newCustEName",
				Util.toSemiCharString(Util.trim(newCustEName)));

		return result;
	}

	/**
	 * 從動審表取得簽報書 J-106-0029-003 洗錢防制-新增實質受益人
	 * 
	 * @param l160m01a
	 * @return
	 */
	@Override
	public L120M01A findL120m01aByL160m01a(L160M01A l160m01a) {
		L120M01A l120m01a = null;

		Map<String, String> cntrCustIdMap = new HashMap<String, String>();
		Set<L160M01B> l160m01bs = l160m01a.getL160m01b(); // 這次動用的額度
		if (l160m01bs != null && !l160m01bs.isEmpty()) {
			for (L160M01B l160m01b : l160m01bs) {
				L140M01A l140m01a = this.findModelByMainId(L140M01A.class,
						l160m01b.getReMainId());
				if (l140m01a != null) {

					L120M01C l120m01c = l140m01a.getL120m01c();
					l120m01a = this.findModelByMainId(L120M01A.class,
							l120m01c.getMainId());
					if (l120m01a != null) {
						break;
					}

				}
			}
		}
		return l120m01a;
	}

	// J-106-0238-001
	// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
	@Override
	public L120S09B findL120s09bByMainId(String mainId) {
		return l120s09bdao.findByMainId(mainId);
	}

	// J-106-0238-001
	// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
	/**
	 * 設定案號
	 * 
	 * @param l120m01a
	 *            簽報書主檔
	 */
	@Override
	public L120S09B initL120s09b(GenericBean model, String queryBrId)
			throws CapMessageException {

		// 產生掃描主檔
		L120S09B l120s09b = null;
		String mainId = "";
		String className = "";
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		if (model != null) {
			if (model instanceof L160M01A) {

				L160M01A l160m01a = (L160M01A) model;
				mainId = l160m01a.getMainId();
				className = L160M01A.class.getSimpleName();

				// 產生掃描主檔
				l120s09b = this.findL120s09bByMainId(mainId);
				if (l120s09b == null) {
					l120s09b = new L120S09B();
					l120s09b.setMainId(mainId);
					l120s09b.setCreateTime(CapDate.getCurrentTimestamp());
					l120s09b.setCreator(user.getUserId());
					l120s09b.setClassName(className);
				}

				if (Util.isEmpty(Util.trim(l120s09b.getCaseSeq()))
						&& Util.isEmpty(Util.trim(l120s09b.getCaseNo()))) {
					l120s09b.setCaseBrId(l160m01a.getOwnBrId());
					l120s09b.setCaseYear(l160m01a.getCaseYear());
					l120s09b.setCaseSeq(Integer.parseInt(number
							.getNumberWithMax(L160M01A.class, user.getUnitNo(),
									null, 99999)));

					StringBuilder caseNum = new StringBuilder();
					IBranch ibranch = branchService.getBranch(user.getUnitNo());
					caseNum.append(
							Util.toFullCharString(l120s09b.getCaseYear()
									.toString()))
							.append(Util.trim(ibranch.getNameABBR()))
							.append("動")
							.append(UtilConstants.Field.授字第)
							.append(Util.toFullCharString(Util
									.addZeroWithValue(
											Util.trim(l120s09b.getCaseSeq()), 5)))
							.append(UtilConstants.Field.號);
					l120s09b.setCaseNo(caseNum.toString());
					l120s09b.setCaseDate(l160m01a.getCaseDate());
					l120s09b.setRefNo(this.getRefNo(l120s09b, "1"));

				}

			} else if (model instanceof L120M01A) {
				L120M01A l120m01a = (L120M01A) model;
				mainId = l120m01a.getMainId();
				className = L120M01A.class.getSimpleName();

				l120s09b = this.findL120s09bByMainId(mainId);
				if (l120s09b == null) {
					l120s09b = new L120S09B();
					l120s09b.setMainId(mainId);
					l120s09b.setCreateTime(CapDate.getCurrentTimestamp());
					l120s09b.setCreator(user.getUserId());
					l120s09b.setClassName(className);
				}

				l120s09b.setRefNo(LMSUtil.getUploadCaseNo((L120M01A) model));
				l120s09b.setCaseBrId(l120m01a.getCaseBrId());
				l120s09b.setCaseYear(l120m01a.getCaseYear());
				l120s09b.setCaseSeq(l120m01a.getCaseSeq());
				l120s09b.setCaseNo(l120m01a.getCaseNo());
				l120s09b.setCaseDate(l120m01a.getCaseDate());
			}

		}

		if (l120s09b != null) {
			l120s09b.setNcResult(""); // 每次重新查詢都要設為未掃描
										// UtilConstants.SasNcResult.未掃描
			l120s09b.setUniqueKey(IDGenerator.getUUID()); // 每次重新查詢都要重編

			// P-108-0046_05097_B1001 Web e-Loan配合Oracle系統建置，修改AML/CFT相關功能。
			String qBrNo = l120s09b.getCaseBrId();
			if (Util.notEquals(queryBrId, "")) {
				qBrNo = queryBrId;
			}

			if (this.isOracle(qBrNo)) {
				l120s09b.setRefNo(StringUtils.substringBefore(
						Util.trim(l120s09b.getRefNo()), "-")
						+ "-" + Util.getRightStr(l120s09b.getUniqueKey(), 6));
			}

			l120s09b.setNcCaseId("");
		}

		return l120s09b;

	}

	/**
	 * 取得簽報書上傳案號-> 民國年 + 分行別+{LMS企金、CLS個金}+末五碼流水號
	 * 
	 * @param l120m01a
	 *            簽報書
	 * @param showSchema
	 *            是否顯示Schema
	 * @return 案號 民國年 + 分行別+{LMS企金、CLS個金}+末五碼流水號
	 */
	@Override
	public String getRefNo(L120S09B l120s09b, String docType) {
		String schema = "";
		if (Util.equals(docType, "1")) {
			schema = "DLMS";
		} else {
			schema = "DCLS";
		}

		return StrUtils.concat(l120s09b.getCaseYear() - 1911,
				l120s09b.getCaseBrId(), schema,
				Util.addZeroWithValue(Util.trim(l120s09b.getCaseSeq()), 5));
	}

	// J-106-0238-001
	// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
	@Override
	public List<L120S09A> findL120s09aByMainIdWithOrder1(String mainId) {
		return l120s09adao.findByMainIdWithOrder1(mainId);
	}

	/**
	 * J-106-0238-001
	 * 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單
	 * /黑國家/政治敏感人物交易具體檢核機制」
	 * 
	 * @return
	 */
	@Override
	public Map<String, String> checkAmlNewFuncMode(String QueryBrId) {

		// 要顯示舊按鈕還是新按鈕
		String callNewFunc = "N";
		String callSasTW = "N"; // 國內專用，用來切換呼叫主機還是直接CALL SAS
		HashMap<String, String> returnMap = new HashMap<String, String>();

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String unitNo = Util.equals(Util.trim(QueryBrId), "") ? user
				.getUnitNo() : Util.trim(QueryBrId);

		// String newFuncCountry = Util.trim(lmsService
		// .getSysParamDataValue("COM_J1060238_AML_CALL_NEW_FUNC"));
		//
		// String countryType = Util.trim(branchSrv.getBranch(user.getUnitNo())
		// .getCountryType());
		//
		// if (Util.notEquals(newFuncCountry, "")
		// && Util.notEquals(countryType, "")) {
		// for (String xx : newFuncCountry.split(",")) {
		// if (Util.equals(xx, countryType)) {
		// callNewFunc = "Y";
		// break;
		// }
		//
		// if (Util.notEquals(countryType, "TW") && Util.equals(xx, "OV")) {
		// // OV代表所有海外分行都不擋
		// callNewFunc = "Y";
		// break;
		// }
		// }
		// }

		IBranch branch = branchSrv.getBranch(unitNo);

		/*
		 * tCallSas：Y=是-間接連(0015-10/SWALLOW)， A=是-直接連(MQ)，N或空白=否
		 */
		// String tCallSas = Util.trim(branch.getCallSas());
		//
		// if (Util.notEquals(tCallSas, "N") && Util.notEquals(tCallSas, "")) {
		// callNewFunc = "Y";
		// } else {
		// callNewFunc = "N";
		// }
		//
		// if (Util.equals(tCallSas, "A")) {
		// callSasTW = "Y";
		// } else {
		// callSasTW = "N";
		// }

		String tCallSas = Util.trim(branch.getCallSas());
		if (Util.equals(tCallSas, "") || Util.equals(tCallSas, "N")
				|| Util.equals(tCallSas, "B")) {
			callNewFunc = "N";
		} else {
			callNewFunc = "Y";
		}

		if (Util.equals(tCallSas, "A")) {
			callSasTW = "Y";
		} else {
			callSasTW = "N";
		}

		returnMap.put("callNewFunc", callNewFunc);
		returnMap.put("callSasTW", callSasTW);

		return returnMap;
	}

	public boolean isNcResultRemarkShow(String mainId, boolean isCls) {
		boolean isNcResultRemarkShow = false;// 預設不顯示

		String callNewFunc = "N";// 預設為不callNewFunc

		if (isCls) {
			// for 國內消金判斷方式
			L120M01A l120m01a = this.findModelByMainId(L120M01A.class, mainId);
			if (l120m01a != null) {
				boolean active_SAS_AML = clsService.active_SAS_AML(l120m01a);
				if (active_SAS_AML) {
					callNewFunc = "Y";
				}
			} else {
				// 消金動審表使用按鈕呼叫到會走這邊
				return false;
			}
		} else {
			// for 國內企金 + 海外企消金 判斷方式
			Map<String, String> chkMap = this.checkAmlNewFuncMode("");
			callNewFunc = MapUtils.getString(chkMap, "callNewFunc", "N");
		}

		// 1.先判斷callNewFunc是否為Y
		// 海外授信系統有一些分行ex:0A7東京分行，他們的AML沒有黑名單資料查詢的機制
		// →若無掃描機制，就不用產生這兩欄位。
		if (Util.equals(callNewFunc, "Y")) {

			L120S09B l120s09b = this.findL120s09bByMainId(mainId);
			// 2.有可能還沒查過黑名單，l120s09b會沒有任何一筆
			if (l120s09b == null) {
				return false;
			}

			// 3.檢查簽案日期是否有超過程式上線日期
			Date caseDate = CapDate.getCurrentTimestamp();
			if (Util.equals("L160M01A", l120s09b.getClassName())) {
				// L160M01A l160m01a = this.findModelByMainId(L160M01A.class,
				// mainId);
				// caseDate = l160m01a.getCaseDate();
				// 2022.05.16與授審處確認動審表先不需要
				// 企金動審表
				return false;
			} else if (Util.equals("C160M01A", l120s09b.getClassName())) {
				// 2022.05.16與授審處確認動審表先不需要
				// 個金動審表
				return false;
			} else {
				// L120M01A
				L120M01A l120m01a = this.findModelByMainId(L120M01A.class,
						mainId);
				caseDate = l120m01a.getCaseDate();
			}

			boolean CaseDateChk = is_function_on_codetype(
					"J-111-0141_AML_CASEDATE", caseDate);

			if (CaseDateChk) {
				// 4.調查結果符合101,102,001,002,003
				String needShowResult = Util.trim(lmsService
						.getSysParamDataValue("COM_J1110141_AML_NCRESULTR"));
				List<String> needShowResultList = Arrays.asList(needShowResult
						.split(","));
				// 4-1.先看這份簽報書AML調查結果是否符合
				String ncResult = Util.trim(l120s09b.getNcResult());
				if (needShowResultList.contains(ncResult)) {
					isNcResultRemarkShow = true;
				}

				// 4-2.再看這份簽報書AML明細中的from徵信調查結果是否符合(國內企金才會有值)
				// 因為有的名單徵信送過再授信就不送
				// 所以得多去看from徵信調查結果
				if (!isNcResultRemarkShow) {
					List<L120S09A> l120s09as = this
							.findL120s09aByMainIdWithShowOrder(mainId);
					if (l120s09as != null && !l120s09as.isEmpty()) {
						for (L120S09A l120s09a : l120s09as) {
							// L120S09A存放的from徵信調查結果
							String cesNcResult = Util.trim(l120s09a
									.getCesNcResult());
							if (needShowResultList.contains(cesNcResult)) {
								isNcResultRemarkShow = true;// from徵信調查結果有一筆符合就算中
								break;
							}
						}
					}
				}
			}

		}
		return isNcResultRemarkShow;
	}

	public boolean isHighRiskRemarkShow(String mainId, boolean isCls) {
		boolean isHighRiskRemarkShow = false;// 預設不顯示

		String callNewFunc = "N";// 預設為不callNewFunc

		if (isCls) {
			// for 國內消金判斷方式
			L120M01A l120m01a = this.findModelByMainId(L120M01A.class, mainId);
			if (l120m01a != null) {
				boolean active_SAS_AML = clsService.active_SAS_AML(l120m01a);
				if (active_SAS_AML) {
					callNewFunc = "Y";
				}
			} else {
				// 消金動審表使用按鈕呼叫到會走這邊
				return false;
			}
		} else {
			// for 國內企金 + 海外企消金 判斷方式
			Map<String, String> chkMap = this.checkAmlNewFuncMode("");
			callNewFunc = MapUtils.getString(chkMap, "callNewFunc", "N");
		}

		// 1.先判斷callNewFunc是否為Y
		// 海外授信系統有一些分行ex:0A7東京分行，他們的AML沒有黑名單資料查詢的機制
		// →若無掃描機制，就不用產生這兩欄位。
		if (Util.equals(callNewFunc, "Y")) {

			L120S09B l120s09b = this.findL120s09bByMainId(mainId);
			// 2.有可能還沒查過黑名單，l120s09b會沒有任何一筆
			if (l120s09b == null) {
				return false;
			}

			// 3.檢查簽案日期是否有超過程式上線日期
			Date caseDate = CapDate.getCurrentTimestamp();
			if (Util.equals("L160M01A", l120s09b.getClassName())) {
				// L160M01A l160m01a = this.findModelByMainId(L160M01A.class,
				// mainId);
				// caseDate = l160m01a.getCaseDate();
				// 2022.05.16與授審處確認動審表先不需要
				return false;
			} else {
				// L120M01A
				L120M01A l120m01a = this.findModelByMainId(L120M01A.class,
						mainId);
				caseDate = l120m01a.getCaseDate();
			}

			boolean CaseDateChk = is_function_on_codetype(
					"J-111-0141_AML_CASEDATE", caseDate);

			if (CaseDateChk) {
				// 4.L120S09A黑名單明細全部拿出來檢查是否有高風險
				List<L120S09A> l120s09as = this
						.findL120s09aByMainIdWithShowOrder(mainId);
				if (l120s09as != null && !l120s09as.isEmpty()) {
					for (L120S09A l120s09a : l120s09as) {
						String luvRiskLevel = Util.trim(l120s09a
								.getLuvRiskLevel());
						if ("H".equals(luvRiskLevel)) {
							isHighRiskRemarkShow = true;// 有一筆高風險就算中
							break;
						}
					}
				}
			}

		}

		return isHighRiskRemarkShow;
	}

	/**
	 * J-106-0238-001
	 * 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單
	 * /黑國家/政治敏感人物交易具體檢核機制」
	 * 
	 * @return
	 */
	@Override
	public AmlStrategy getAmlStrategy(String QueryBrId) {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// String unitNo = user.getUnitNo();
		String unitNo = Util.equals(Util.trim(QueryBrId), "") ? user
				.getUnitNo() : Util.trim(QueryBrId);
		IBranch branch = branchSrv.getBranch(unitNo);
		String tCallSas = Util.trim(branch.getCallSas());

		String strategyName = "";

		// AS400 MQ(舊) N
		// a-Loan STORE PROCEDURE(舊) N -> B
		// amlMegaMqStrategy (新)國內SAS透過mq A
		// amlMegaOvsMqStrategy (新)海外SWALLOW Y -> S
		// amlMegaNYAMqStrategy (新)紐約prime透過mq ->P
		// amlMainframeStrategy 0015-10(新) Y

		if (Util.equals(tCallSas, "") || Util.equals(tCallSas, "N")
				|| Util.equals(tCallSas, "B")) {
			strategyName = "--";
		} else if (Util.equals(tCallSas, "A")) {
			strategyName = "amlMegaMqStrategy"; // (新)國內SAS透過mq
		} else if (Util.equals(tCallSas, "S")) {
			strategyName = "amlMegaOvsMqStrategy"; // (新)海外SWALLOW Y -> S
		} else if (Util.equals(tCallSas, "P") || Util.equals(tCallSas, "C")) {
			// J-110-00A2_05097_B1001 Web e-Loan配合紐行Oracle系統建置，修改AML相關功能。
			// (新)紐約prime透過mq ->P
			// (新)紐約ORACLE透過mq ->C
			strategyName = "amlMegaNYAMqStrategy";
		} else if (Util.equals(tCallSas, "Y")) {
			strategyName = "amlMainframeStrategy"; // (新)0015-10 Y
		} else if (Util.equals(tCallSas, "O")) {
			// P-108-0046_05097_B1001 Web e-Loan配合Oracle系統建置，修改AML相關功能。
			// 未來會有新的amlMainframeStrategyXX
			strategyName = "amlMainframeStrategy"; // (新)a-Loan Oracle
		} else {
			strategyName = "amlMainframeStrategy"; // (新)0015-10 Y
		}

		AmlStrategy as = null;

		// TODO FOR
		// TEST amlMegaOvsMqStrategy
		// *******************************************************************
		// strategyName = "amlMegaOvsMqStrategy";

		as = amlstrategyfactory.getStrategy(strategyName);

		return as;
	}

	/**
	 * 判斷簽報書AML check_result/NC_Result 結果
	 * 
	 * @param ncResutl
	 * @return
	 */
	@Override
	public Map<String, String> getCaseReportAmlStatus(String ncResutl,
			Map<String, String> queryMap) {
		HashMap<String, String> returnMap = null;

		String caseFinish = "N"; // COM_J1060238_AML_CASEFINISH_Y
									// 案件沒中或有中且已經調查完成，已經可以送呈主管
		String lockEdit = "N"; // COM_J1060238_AML_LOCKEDIT_Y
								// 案件丟出去掃描但是還沒有任何一個結果時，要鎖住前端畫面，不要讓USER再重送掃描，只顯示取得掃描結果按鈕
		String hasHitFlag = "N"; // COM_J1060238_AML_HASHITFLAG_Y checkResult
									// 掃描的名單有疑似命中或命中黑名單的狀態，要放到L120S09A的MEMO欄位
		String blackListCode = ""; // COM_J1060238_AML_BLACKCODE_00
									// COM_J1060238_AML_BLACKCODE_02
									// COM_J1060238_AML_BLACKCODE_04 checkResult
									// 轉換ELOAN黑名單代碼

		if (queryMap == null || queryMap.isEmpty()) {
			queryMap = new HashMap<String, String>();
			queryMap.put("lockEdit", lockEdit);
			queryMap.put("caseFinish", caseFinish);
			queryMap.put("hasHitFlag", hasHitFlag);
			queryMap.put("blackListCode", blackListCode);
		}

		returnMap = (HashMap) queryMap;

		String ncResult = Util.trim(ncResutl);

		// **********************************************************************************************************
		// caseFinish COM_J1060238_AML_CASEFINISH_Y 案件沒中或有中且已經調查完成，已經可以送呈主管
		if (queryMap.containsKey("caseFinish")) {
			String sysCaseFinish = Util.trim(lmsService
					.getSysParamDataValue("COM_J1060238_AML_CASEFINISH_Y"));
			if (Util.notEquals(sysCaseFinish, "")) {
				for (String xx : sysCaseFinish.split(",")) {
					String txx = Util.equals(Util.trim(xx), "NULL") ? "" : xx;
					if (Util.equals(txx, ncResult)) {
						caseFinish = "Y";
						break;
					}
				}
			}
		}

		// **********************************************************************************************************
		// lockEdit COM_J1060238_AML_LOCKEDIT_Y
		// 案件丟出去掃描但是還沒有任何一個結果時，要鎖住前端畫面，不要讓USER再重送掃描，只顯示取得掃描結果按鈕
		if (queryMap.containsKey("lockEdit")) {
			String sysLockEdit = Util.trim(lmsService
					.getSysParamDataValue("COM_J1060238_AML_LOCKEDIT_Y"));
			if (Util.notEquals(sysLockEdit, "")) {
				for (String xx : sysLockEdit.split(",")) {
					String txx = Util.equals(Util.trim(xx), "NULL") ? "" : xx;
					if (Util.equals(txx, ncResult)) {
						lockEdit = "Y";
						break;
					}
				}
			}
		}

		// **********************************************************************************************************
		// hasHitFlag COM_J1060238_AML_HASHITFLAG_Y
		// 掃描的名單有疑似命中或命中黑名單的狀態，要放到L120S09A的MEMO欄位
		if (queryMap.containsKey("hasHitFlag")) {
			String sysHasHitFlag = Util.trim(lmsService
					.getSysParamDataValue("COM_J1060238_AML_HASHITFLAG_Y"));
			if (Util.notEquals(sysHasHitFlag, "")) {
				for (String xx : sysHasHitFlag.split(",")) {
					String txx = Util.equals(Util.trim(xx), "NULL") ? "" : xx;
					if (Util.equals(txx, ncResult)) {
						hasHitFlag = "Y";
						break;
					}
				}
			}
		}

		// **********************************************************************************************************
		// 轉換ELOAN黑名單代碼
		// blackListCode COM_J1060238_AML_BLACKCODE_00
		// COM_J1060238_AML_BLACKCODE_02 COM_J1060238_AML_BLACKCODE_04
		if (queryMap.containsKey("blackListCode")) {
			if (Util.notEquals(ncResult, "")) {
				// blackListCode
				// 掃描的名單有疑似命中或命中黑名單的狀態，要放到L120S09A的MEMO欄位
				if (Util.equals(blackListCode, "")) {
					String sysBlackListCode00 = Util
							.trim(lmsService
									.getSysParamDataValue("COM_J1060238_AML_BLACKCODE_00"));
					if (Util.notEquals(sysBlackListCode00, "")) {
						for (String xx : sysBlackListCode00.split(",")) {
							String txx = Util.equals(Util.trim(xx), "NULL") ? ""
									: xx;
							if (Util.equals(txx, ncResult)) {
								blackListCode = UtilConstants.Casedoc.L120s09aBlackListCode.未列於黑名單;
								break;
							}
						}
					}
				}

				if (Util.equals(blackListCode, "")) {
					String sysBlackListCode02 = Util
							.trim(lmsService
									.getSysParamDataValue("COM_J1060238_AML_BLACKCODE_02"));
					if (Util.notEquals(sysBlackListCode02, "")) {
						for (String xx : sysBlackListCode02.split(",")) {
							String txx = Util.equals(Util.trim(xx), "NULL") ? ""
									: xx;
							if (Util.equals(txx, ncResult)) {
								blackListCode = UtilConstants.Casedoc.L120s09aBlackListCode.是黑名單;
								break;
							}
						}
					}
				}

				if (Util.equals(blackListCode, "")) {
					String sysBlackListCode04 = Util
							.trim(lmsService
									.getSysParamDataValue("COM_J1060238_AML_BLACKCODE_04"));
					if (Util.notEquals(sysBlackListCode04, "")) {
						for (String xx : sysBlackListCode04.split(",")) {
							String txx = Util.equals(Util.trim(xx), "NULL") ? ""
									: xx;
							if (Util.equals(txx, ncResult)) {
								blackListCode = UtilConstants.Casedoc.L120s09aBlackListCode.可能是黑名單;
								break;
							}
						}
					}
				}
			}
		}

		// **********************************************************************************************************

		/*
		 * 
		 * if (Util.equals(ncResult, UtilConstants.SasNcResult.未命中疑似名單)) {
		 * caseFinish = "Y"; blackListCode =
		 * UtilConstants.Casedoc.L120s09aBlackListCode.未列於黑名單; } else if
		 * (Util.equals(ncResult, UtilConstants.SasNcResult.命中疑似名單)) {
		 * hasHitFlag = "Y"; blackListCode =
		 * UtilConstants.Casedoc.L120s09aBlackListCode.可能是黑名單; } else if
		 * (Util.equals(ncResult, UtilConstants.SasNcResult.經調查覆核後可交易)) {
		 * caseFinish = "Y"; hasHitFlag = "Y"; blackListCode =
		 * UtilConstants.Casedoc.L120s09aBlackListCode.是黑名單; } else if
		 * (Util.equals(ncResult, UtilConstants.SasNcResult.經調查覆核後不可交易)) {
		 * caseFinish = "Y"; hasHitFlag = "Y"; blackListCode =
		 * UtilConstants.Casedoc.L120s09aBlackListCode.是黑名單; } else if (Util
		 * .equals(ncResult, UtilConstants.SasNcResult.台伊清算NonBlock)) {
		 * caseFinish = "Y"; } else if (Util.equals(ncResult,
		 * UtilConstants.SasNcResult.未掃描)) { caseFinish = "Y"; } else if
		 * (Util.equals(ncResult, UtilConstants.SasNcResult.經調查覆核後取消交易)) {
		 * caseFinish = "Y"; hasHitFlag = "Y"; blackListCode =
		 * UtilConstants.Casedoc.L120s09aBlackListCode.是黑名單; } else if
		 * (Util.equals(ncResult, UtilConstants.SasNcResult.案件調查中)) { hasHitFlag
		 * = "Y"; blackListCode =
		 * UtilConstants.Casedoc.L120s09aBlackListCode.可能是黑名單; } else if
		 * (Util.equals(ncResult, UtilConstants.SasNcResult.經調查確認命中)) {
		 * caseFinish = "Y"; hasHitFlag = "Y"; blackListCode =
		 * UtilConstants.Casedoc.L120s09aBlackListCode.是黑名單; } else if
		 * (Util.equals(ncResult, UtilConstants.SasNcResult.經調查確認誤中)) {
		 * caseFinish = "Y"; } else if (Util .equals(ncResult,
		 * UtilConstants.SasNcResult.SAS錯誤LEVEL_1)) {
		 * 
		 * } else if (Util .equals(ncResult,
		 * UtilConstants.SasNcResult.SAS錯誤LEVEL_2)) {
		 * 
		 * } else if (Util.equals(ncResult, "")) { lockEdit = "Y"; } else {
		 * 
		 * }
		 */

		returnMap.put("lockEdit", lockEdit);
		returnMap.put("caseFinish", caseFinish);
		returnMap.put("hasHitFlag", hasHitFlag);
		returnMap.put("blackListCode", blackListCode);
		return returnMap;
	}

	/**
	 * 判斷簽報書AML/CFT頁籤是否要顯是0024 AML STATUS 與提示訊息
	 * 
	 * @param ncResutl
	 * @return
	 */
	@Override
	public String getIsShow0024AmlStatus(String QueryBrId) {
		HashMap<String, String> returnMap = null;

		String show0024AmlStatus = "N"; // COM_J1060238_AML_0024AML_Y

		// **********************************************************************************************************
		// show0024AmlStatus COM_J1060238_AML_HASHITFLAG_Y
		// 掃描的名單有疑似命中或命中黑名單的狀態，要放到L120S09A的MEMO欄位

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String unitNo = user.getUnitNo();

		String countryType = Util.trim(branchSrv.getBranch(QueryBrId)
				.getCountryType());

		String newFuncCountry = Util.trim(lmsService
				.getSysParamDataValue("COM_J1060238_AML_SHOW_0024AML"));

		if (Util.notEquals(newFuncCountry, "")
				&& Util.notEquals(countryType, "")) {
			for (String xx : newFuncCountry.split(",")) {
				if (Util.equals(xx, countryType)) {
					show0024AmlStatus = "Y";
					break;
				}

				if (Util.notEquals(countryType, "TW") && Util.equals(xx, "OV")) {
					// OV代表所有海外分行都不擋
					show0024AmlStatus = "Y";
					break;
				}
			}
		}

		return show0024AmlStatus;
	}

	// J-106-0238-001
	// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
	// 檢核借款人是否為0024拒絕交易名單
	/**
	 * 檢核借款人的0024拒絕交易狀態
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@Override
	public HashMap<String, String> chkCustIn0024Reject(String mainId)
			throws CapException {

		HashMap<String, String> rejectCustId = new HashMap<String, String>();

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Map<String, Object> chkInsteadMap = this.checkInstead(mainId);
		boolean instead = MapUtils.getBooleanValue(chkInsteadMap, "instead",
				false);
		String queryBrId = MapUtils.getString(chkInsteadMap, "queryBrId");
		String unitNo = (instead ? queryBrId : user.getUnitNo());

		// 判斷簽報書AML/CFT頁籤是否要顯是0024 AML STATUS 與提示訊息
		String show0024AmlStatus = this.getIsShow0024AmlStatus(unitNo);
		if (Util.notEquals(show0024AmlStatus, "Y")) {
			return rejectCustId;
		}

		List<L120S01A> l120s01as = this.findL120s01aByMainId(mainId);
		if (l120s01as != null && !l120s01as.isEmpty()) {
			for (L120S01A l120s01a : l120s01as) {
				String custId = l120s01a.getCustId();
				String dupNo = l120s01a.getDupNo();

				List<L140M01A> l140m01as = this
						.findL140m01aListByL120m01cMainId(mainId,
								UtilConstants.Cntrdoc.ItemType.額度明細表);

				// 有額度明細表的借款人才檢查
				if (l140m01as != null && !l140m01as.isEmpty()) {

					String CM1_AML_STATUS = this.getCustIn0024AmlStatus(custId,
							dupNo);
					if (Util.equals(CM1_AML_STATUS, "3")) {
						rejectCustId.put(custId + dupNo,
								Util.trim(l120s01a.getCustName()));
					}

				}

			}
		}

		return rejectCustId;
	}

	/**
	 * J-106-0238-001
	 * 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單
	 * /黑國家/政治敏感人物交易具體檢核機制」 取得借款人的0024拒絕交易狀態
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	@Override
	public String getCustIn0024AmlStatus(String custId, String dupNo) {

		// I-108-0027_05097_B1001 Web
		// e-Loan國內授信系統配合0024客戶中文檔取消AML-STATUS禁止交易之限制，若為制裁名單將採關戶或凍結
		// AML/CFT 拒絕往來交易 調整
		// 原本AML判斷拒絕交易，是抓MIS.CMFCUS1 的CM1_AML_STATUS欄位 = '3'，現在要改成讀取
		// MIS.CMFDNGER ，當 CMDNG_REASON_CODE = '3' 且 CMDNG_TF_FLAG = 'Y' 為拒絕交易

		String CMDNG_REASON_CODE = "";
		String CMDNG_TF_FLAG = "";
		String CM1_AML_STATUS = "";
		Map<String, Object> cmf = misCustdataService.findCMFDNGERByIdAndDup(
				custId, dupNo);
		if (cmf != null) {
			CMDNG_REASON_CODE = Util.trim(MapUtils.getString(cmf,
					"CMDNG_REASON_CODE", ""));
			CMDNG_TF_FLAG = Util.trim(MapUtils.getString(cmf, "CMDNG_TF_FLAG",
					""));

			if (Util.equals(CMDNG_REASON_CODE, "3")
					&& Util.equals(CMDNG_TF_FLAG, "Y")) {
				CM1_AML_STATUS = "3";
			}

		}

		return CM1_AML_STATUS;
	}

	@Override
	public List<L120S01A> findL120s01aByMainId(String mainId) {
		// 透過MainId取得多筆資料
		return l120s01aDao.findByMainId(mainId);
	}

	/**
	 * 檢核簽報書調查狀態是否已經完成可以送呈主管 J-106-0238-001
	 * 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單
	 * /黑國家/政治敏感人物交易具體檢核機制」 取得借款人的0024拒絕交易狀態
	 */
	@Override
	public void chkNcResultFinishCanSendBoss(String mainId) throws CapException {
		// J-106-0238-001
		// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
		// 檢核簽報書黑名單掃描是否有調查完成才可以送呈主管
		// Map<String, String> chkMap = this.checkAmlNewFuncMode();
		Map<String, String> chkMap = new HashMap<String, String>();

		Map<String, Object> chkInsteadMap = this.checkInstead(mainId);
		boolean instead = MapUtils.getBooleanValue(chkInsteadMap, "instead",
				false);
		String queryBrId = MapUtils.getString(chkInsteadMap, "queryBrId");
		if (instead) {
			chkMap = this.checkAmlNewFuncMode(queryBrId);
		} else {
			chkMap = this.checkAmlNewFuncMode("");
		}
		if (!this.needChkAmlOkBeforeSendBoss(queryBrId)) {
			// 送呈主管時取消檢核AML是否完成
			return;
		}

		String callNewFunc = MapUtils.getString(chkMap, "callNewFunc", "N");
		if (Util.equals(callNewFunc, "Y")) {
			L120S09B l120s09b = this.findL120s09bByMainId(mainId);
			if (l120s09b != null) {
				// 新模式且有L120S09B的才要檢核(代表是開放舊案後才執行黑名單檢核)

				String ncResult = Util.trim(l120s09b.getNcResult());

				Map<String, String> queryMap = new HashMap<String, String>();
				queryMap.put("caseFinish", "");

				Map<String, String> statusMap = this.getCaseReportAmlStatus(
						ncResult, queryMap);

				String caseFinish = MapUtils.getString(statusMap, "caseFinish",
						"N");

				if (Util.notEquals(caseFinish, "Y")) {
					Properties propComm = MessageBundleScriptCreator
							.getComponentResource(LMSCommomPage.class);
					// lmsL120M01A.error036=簽報書AML/CFT頁籤之「黑名單案件調查結果」尚未完成，請先執行「取得黑名單查詢結果」按鈕。
					throw new CapMessageException(
							propComm.getProperty("lmsL120M01A.error036"),
							getClass());
				}

			}
		}

	}

	@Override
	public void chkNcResultMarkAndHighRiskMarkCanSendBoss(String mainId)
			throws CapException {
		// J-111-0141 針對國內企金、消金及海外授信簽案系統之AML頁籤，增加「調查結果說明」欄位
		Map<String, Object> chkInsteadMap = this.checkInstead(mainId);
		String queryBrId = MapUtils.getString(chkInsteadMap, "queryBrId");
		if (!this.needChkAmlOkBeforeSendBoss(queryBrId)) {
			// 送呈主管時取消檢核AML是否完成
			return;
		}

		boolean needSendCheck = is_function_on_codetype("J-111-0141_AML_SENDCHECK");
		if (!needSendCheck) {
			// 送呈主管時判斷檢核欄位是否有填寫，若codetype為N則不檢查該兩個欄位
			// 算是預留flag可不擋傳送
			return;
		}

		// 判斷"制裁/管制名單掃描調查結果說明"、"高風險調查結果說明"欄位 如果這兩個欄位是否該顯示
		boolean isNcResultRemarkShow = this.isNcResultRemarkShow(mainId, false);
		boolean isHighRiskRemarkShow = this.isHighRiskRemarkShow(mainId, false);

		L120S09B l120s09b = this.findL120s09bByMainId(mainId);

		// 如為true需顯示就該要輸入值，但卻沒有值的話要擋傳送
		String message = "";
		if (l120s09b != null) {
			String ncResultRemark = Util.trim(l120s09b.getNcResultRemark());

			if (isNcResultRemarkShow && Util.isEmpty(ncResultRemark)) {
				Properties propComm = MessageBundleScriptCreator
						.getComponentResource(LMSCommomPage.class);
				// lmsL120M01A.error072=AML頁籤制裁/管制名單掃描調查結果說明欄位未填
				message = propComm.getProperty("lmsL120M01A.error072");
			}

			String highRiskRemark = Util.trim(l120s09b.getHighRiskRemark());

			if (isHighRiskRemarkShow && Util.isEmpty(highRiskRemark)) {
				Properties propComm = MessageBundleScriptCreator
						.getComponentResource(LMSCommomPage.class);
				// lmsL120M01A.error073=AML頁籤高風險調查結果說明欄位未填
				if (Util.isEmpty(message)) {
					message = propComm.getProperty("lmsL120M01A.error073");
				} else {
					message = message + "<BR/>"
							+ propComm.getProperty("lmsL120M01A.error073");
				}
			}
		}

		if (Util.isNotEmpty(message)) {
			throw new CapMessageException(message, getClass());
		}

	}

	@Override
	public String chkNcResultMarkAndHighRiskMarkForRejtMsg(String mainId,
			boolean isCls) {
		// J-111-0141 針對國內企金、消金及海外授信簽案系統之AML頁籤，增加「調查結果說明」欄位
		Map<String, Object> chkInsteadMap = this.checkInstead(mainId);
		String queryBrId = MapUtils.getString(chkInsteadMap, "queryBrId");
		if (!this.needChkAmlOkBeforeSendBoss(queryBrId)) {
			// 送呈主管時取消檢核AML是否完成
			return "";// 回傳空字串代表沒事
		}

		boolean needRejtMsg = is_function_on_codetype("J-111-0141_AML_REJTMSG");
		if (!needRejtMsg) {
			// 1.國內消金傳送檢核
			// 2.海外企消金送呈提示
			// 時判斷檢核欄位是否有填寫，若codetype為N則不檢查該兩個欄位
			// 算是預留flag可不擋傳送
			return "";// 回傳空字串代表沒事
		}

		// 判斷"制裁/管制名單掃描調查結果說明"、"高風險調查結果說明"欄位 如果這兩個欄位是否該顯示
		boolean isNcResultRemarkShow = this.isNcResultRemarkShow(mainId, isCls);
		boolean isHighRiskRemarkShow = this.isHighRiskRemarkShow(mainId, isCls);

		L120S09B l120s09b = this.findL120s09bByMainId(mainId);

		// 如為true需顯示就該要輸入值，但卻沒有值的話要擋傳送
		String message = "";
		if (l120s09b != null) {
			String ncResultRemark = Util.trim(l120s09b.getNcResultRemark());

			if (isNcResultRemarkShow && Util.isEmpty(ncResultRemark)) {
				Properties propComm = MessageBundleScriptCreator
						.getComponentResource(LMSCommomPage.class);
				// lmsL120M01A.error072=AML頁籤制裁/管制名單掃描調查結果說明欄位未填
				message = propComm.getProperty("lmsL120M01A.error072");
			}

			String highRiskRemark = Util.trim(l120s09b.getHighRiskRemark());

			if (isHighRiskRemarkShow && Util.isEmpty(highRiskRemark)) {
				Properties propComm = MessageBundleScriptCreator
						.getComponentResource(LMSCommomPage.class);
				// lmsL120M01A.error073=AML頁籤高風險調查結果說明欄位未填
				if (Util.isEmpty(message)) {
					message = propComm.getProperty("lmsL120M01A.error073");
				} else {
					message = message + "<BR/>"
							+ propComm.getProperty("lmsL120M01A.error073");
				}
			}
		}

		return message;
	}

	/**
	 * 檢核0024有拒絕交易且額度明細表有新作時可否送呈 J-106-0238-001
	 * 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單
	 * /黑國家/政治敏感人物交易具體檢核機制」 取得借款人的0024拒絕交易狀態
	 */
	@Override
	public void chk0024RejecjTrancCanNotSendBoss(String mainId)
			throws CapException {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Map<String, Object> chkInsteadMap = this.checkInstead(mainId);
		boolean instead = MapUtils.getBooleanValue(chkInsteadMap, "instead",
				false);
		String queryBrId = MapUtils.getString(chkInsteadMap, "queryBrId");
		String unitNo = (instead ? queryBrId : user.getUnitNo());

		// 判斷簽報書AML/CFT頁籤是否要顯是0024 AML STATUS 與提示訊息
		String show0024AmlStatus = this.getIsShow0024AmlStatus(unitNo);
		if (Util.notEquals(show0024AmlStatus, "Y")) {
			return;
		}

		// 檢核0024有拒絕交易且額度明細表有新作時可否送呈
		// Y:0024有拒絕交易且有新案可以送呈=不用檢核
		// N:0024有拒絕交易且有新案不得送呈
		String rejectCanSendBoss = Util.trim(lmsService
				.getSysParamDataValue("COM_J1060238_AML_REJ_SEND_BOSS"));

		// 不得送呈
		if (Util.equals(rejectCanSendBoss, "N")) {

			// Map<String, String> chkMap = this.checkAmlNewFuncMode();
			Map<String, String> chkMap = new HashMap<String, String>();
			if (instead) {
				chkMap = this.checkAmlNewFuncMode(queryBrId);
			} else {
				chkMap = this.checkAmlNewFuncMode("");
			}

			String callNewFunc = MapUtils.getString(chkMap, "callNewFunc", "N");
			if (Util.equals(callNewFunc, "Y")) {

				L120S09B l120s09b = this.findL120s09bByMainId(mainId);

				if (l120s09b != null) {
					StringBuffer rejectCustStr = new StringBuffer("");

					HashMap<String, String> rejectCust = this
							.chkCustIn0024Reject(mainId);
					if (!rejectCust.isEmpty() && rejectCust != null) {
						// 借款人0024有拒絕交易情況
						List<L140M01A> l140m01as = null;

						if (Util.equals(l120s09b.getClassName(), "L160M01A")) {
							ArrayList<String> mainIds = new ArrayList<String>();
							// 動審表主要資料來源為MIS TABLE
							L160M01A l160m01a = this.findModelByMainId(
									L160M01A.class, mainId);
							Set<L160M01B> l160m01bs = l160m01a.getL160m01b();
							for (L160M01B l160m01b : l160m01bs) {
								mainIds.add(l160m01b.getReMainId());
							}

							l140m01as = this
									.findL140m01aListByMainIdList(mainIds
											.toArray(new String[mainIds.size()]));
						} else {
							// L120M01A
							l140m01as = this.findL140m01aListByL120m01cMainId(
									mainId,
									UtilConstants.Cntrdoc.ItemType.額度明細表);
						}

						String key = "";
						for (L140M01A l140m01a : l140m01as) {
							// 只有新作才擋
							boolean isProPerty1 = LMSUtil.isContainValue(
									Util.trim(l140m01a.getProPerty()),
									UtilConstants.Cntrdoc.Property.新做);

							key = l140m01a.getCustId() + l140m01a.getDupNo();

							if (isProPerty1) {
								if (rejectCust.containsKey(key)) {
									// 拒絕交易客戶本次動用有新增之額度明細表
									if (Util.equals(rejectCustStr.toString(),
											"")) {
										rejectCustStr.append(key).append(
												rejectCust.get(key));
									} else {
										rejectCustStr.append("、").append(key)
												.append(rejectCust.get(key));
									}

								}
							}

						}

					}

					if (Util.notEquals(rejectCustStr.toString(), "")) {
						// lmsL120M01A.error035=請注意! 下列借款人於0024客戶中文檔為拒絕交易狀態!
						Properties propComm = MessageBundleScriptCreator
								.getComponentResource(LMSCommomPage.class);

						throw new CapMessageException(
								propComm.getProperty("lmsL120M01A.error035")
										+ "：" + rejectCustStr.toString(),
								getClass());
					}
				}

			}
		}

	}

	@Override
	public List<L140M01A> findL140m01aListByMainIdList(String[] mainId) {
		return l140m01aDao.findL140m01aListByMainIdList(mainId);
	}

	/**
	 * 檢核L120S09A是否需要英文戶名
	 */
	@Override
	public boolean isL120s09ANeedEngName(String custRelation, String QueryBrId) {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String unitNo = Util.equals(Util.trim(QueryBrId), "") ? user
				.getUnitNo() : Util.trim(QueryBrId);

		boolean needEngName = false;

		String callNewFunc = "N";

		Map<String, String> chkMap = this.checkAmlNewFuncMode("");

		callNewFunc = MapUtils.getString(chkMap, "callNewFunc", "N");

		// J-106-0238-001
		// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
		if (Util.equals(callNewFunc, "Y")) {
			String countryType = Util.trim(branchSrv.getBranch(unitNo)
					.getCountryType());
			if (Util.equals(countryType, "US")) {
				// 美國地區掃描PRIME 只收英文戶名，中文會有亂碼，一定會中古巴
				needEngName = true;
			}
		}

		String[] strs = custRelation.split(",");
		for (int i = 0; i < strs.length; i++) {
			if (Util.equals(strs[i],
					UtilConstants.Casedoc.L120s09aBlackListCtlTarget.借戶)
					|| Util.equals(
							strs[i],
							UtilConstants.Casedoc.L120s09aBlackListCtlTarget.共同借款人)) {
				// 需要英文戶名 (借款人或共同借款人)
				needEngName = true;
				break;
			}
		}

		return needEngName;
	}

	/**
	 * J-106-0238-003 判斷登入分行是否需檢核AML不OK時不得送呈主管
	 * 
	 * @return
	 */
	@Override
	public boolean needChkAmlOkBeforeSendBoss(String QueryBrId) {
		boolean isNeed = true;

		// 後台管理->系統設定維護->COM_J1060238_AML_CHK_OFF SG,AU 送呈主管時取消檢核AML是否完成

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String countryType = Util.trim(branchSrv.getBranch(QueryBrId)
				.getCountryType());

		// 送呈主管時取消檢核AML是否完成
		String notChkCountry = Util.trim(lmsService
				.getSysParamDataValue("COM_J1060238_AML_CHK_OFF"));

		if (Util.notEquals(notChkCountry, "")
				&& Util.notEquals(countryType, "")) {
			for (String xx : notChkCountry.split(",")) {
				if (Util.equals(xx, countryType)) {
					isNeed = false;
					break;
				}

				if (Util.notEquals(countryType, "TW") && Util.equals(xx, "OV")) {
					// OV代表所有海外分行都不擋
					isNeed = false;
					break;
				}
			}
		}

		return isNeed;
	}

	/**
	 * J-107-0059-001 Web e-Loan 授信簽報書與動審表之AML頁籤及列印檢核表時，增加引進風險等級
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	@Override
	public String getCustLuvRiskLevel(String brNo, String custId, String dupNo) {
		String luvRiskLevel = "";

		boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchSrv
				.getBranch(brNo).getBrNoFlag());

		if (isOverSea) {
			// 海外抓DW OTS_CSOVS的DETAIL_FIN_RISK
			Map<String, Object> cmf = dwdbService
					.findDW_OTS_CSOVS_By_CustId_And_BrNo(brNo, custId, dupNo);
			if (cmf != null) {
				luvRiskLevel = Util.trim(MapUtils.getString(cmf,
						"DETAIL_FIN_RISK", ""));
			}

		} else {
			// 國內抓MIS.CMFLUNVA 的LUV_RISK_LEVEL
			Map<String, Object> cmf = misDbService.findCMFLUNVA_byUk(custId,
					dupNo);
			if (cmf != null) {
				luvRiskLevel = Util.trim(MapUtils.getString(cmf,
						"LUV_RISK_LEVEL", ""));
			}
		}

		return luvRiskLevel;
	}

	/**
	 * J-107-0059-001 Web e-Loan 授信簽報書與動審表之AML頁籤及列印檢核表時，增加引進風險等級
	 * 
	 * @return
	 */
	@Override
	public boolean needChkAmlCustRiskLevel(String QueryBrId) {
		boolean isNeed = true;

		// 後台管理->系統設定維護->COM_J1060238_AML_CHK_OFF SG,AU 送呈主管時取消檢核AML是否完成

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String countryType = Util.trim(branchSrv.getBranch(QueryBrId)
				.getCountryType());

		// 送呈主管時取消檢核AML是否完成
		String notChkCountry = Util.trim(lmsService
				.getSysParamDataValue("COM_J1070059_AML_RISK_LVL_OFF"));

		if (Util.notEquals(notChkCountry, "")
				&& Util.notEquals(countryType, "")) {
			for (String xx : notChkCountry.split(",")) {
				if (Util.equals(xx, countryType)) {
					isNeed = false;
					break;
				}

				if (Util.notEquals(countryType, "TW") && Util.equals(xx, "OV")) {
					// OV代表所有海外分行都不擋
					isNeed = false;
					break;
				}
			}
		}

		return isNeed;
	}

	/**
	 * J-107-0059-001 Web e-Loan 授信簽報書與動審表之AML頁籤及列印檢核表時，增加引進風險等級 0024-23 往來業務檢核
	 * 注意:以下分類不含"SUMIT" 放款 => 02 授信業務 進口 => 03 進出口業務 (科目941,944) 出口 => 03 進出口業務
	 * (科目942,715,950) 應收帳款 => 02 授信業務 供應鏈融資=> 02 授信業務 遠匯 => 01 存匯業務 / 0 3 進出口業務
	 * (科目961,962,963,964) 風險參與 => 03 進出口業務 (科目971) 無法判斷者歸 ' 02 授信業務'
	 * 
	 * 
	 * type : 1=簽報書 2=動審表
	 * 
	 * @throws CapException
	 */
	public String chkBusinessTypeIn002423(String mainId,
			List<L140M01A> l140m01as, String type) throws CapException {
		String errMsg = "";
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS20APanel.class);

		Map<String, String> cntrAllCustIdNameMap = new HashMap<String, String>();

		// I-110-0329_11557_B1001 針對既有自然人客戶增加KYC系統異常機制，KYC異常時可BYPASS
		boolean check0024_23_call_SCMLUINQ = is_function_on_codetype("call_SCMLUINQ_FOR_LUV_DEPT");
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);

		// 1.先取得借款人0024-23資料
		// 2.取得借款人額度明細表內所有科目
		Map<String, Map<String, Object>> g002423BussTypeMap = new HashMap<String, Map<String, Object>>();
		Map<String, Map<String, String>> cntrDocLoanTpMap = new HashMap<String, Map<String, String>>();
		for (L140M01A l140m01a : l140m01as) {

			boolean notChkProperty = false;

			// J-108-0145_05097_B1001 Web e-Loan 國內外企金授信私募基金案件調整實質受益人控管
			if (Util.equals(type, "1")) {
				// 簽報書才可以PASS AML實際受益人檢核
				if (Util.equals(
						this.canPassAmlRelativeAndRiskLvlChk(l140m01a,
								Util.trim(l140m01a.getCustId()),
								Util.trim(l140m01a.getDupNo())), "Y")) {
					continue;
				}
			}

			if (LMSUtil.isContainValue(Util.trim(l140m01a.getProPerty()),
					UtilConstants.Cntrdoc.Property.不變)
					|| LMSUtil.isContainValue(
							Util.trim(l140m01a.getProPerty()),
							UtilConstants.Cntrdoc.Property.取消)) {
				notChkProperty = true;
			}

			if (!notChkProperty) {
				String bId = Util.trim(l140m01a.getCustId());
				String bNo = Util.trim(l140m01a.getDupNo());
				String bName = Util.toSemiCharString(Util.trim(l140m01a
						.getCustName()));

				// 1.先取得借款人0024-23資料
				if (!cntrAllCustIdNameMap.containsKey(bId + bNo)) {
					cntrAllCustIdNameMap.put(bId + bNo, bName);

					// 先取得借款人0024-23資料
					Map<String, Object> cmf;
					// I-110-0329_11557_B1001 針對既有自然人客戶增加KYC系統異常機制，KYC異常時可BYPASS
					if (check0024_23_call_SCMLUINQ) {
						cmf = this.getAllCMPMLUNQListByCustId(bId, bNo);
					} else {
						cmf = misDbService.findCMFLUNVA_byUk(bId, bNo);
					}
					if (cmf != null) {
						g002423BussTypeMap.put(bId + bNo, cmf);
					} else {
						// 沒資料，錯誤
						g002423BussTypeMap.put(bId + bNo, null);
					}

				}

				// 2.取得借款人額度明細表內所有科目
				Set<L140M01C> l140m01cs = l140m01a.getL140m01c();
				if (l140m01cs != null) {
					for (L140M01C l140m01c : l140m01cs) {
						String item = Util.truncateString(l140m01c.getLoanTP(),
								3);
						if (cntrDocLoanTpMap.containsKey(bId + bNo)) {
							Map<String, String> loanTpMap = cntrDocLoanTpMap
									.get(bId + bNo);
							loanTpMap.put(item, item);
							cntrDocLoanTpMap.put(bId + bNo, loanTpMap);
						} else {
							Map<String, String> loanTpMap = new HashMap<String, String>();
							loanTpMap.put(item, item);
							cntrDocLoanTpMap.put(bId + bNo, loanTpMap);
						}
					}
				}

			}

		}

		// 進出口業務 941,944,942,715,950,971
		String[] forType03 = Util
				.trim(lmsService
						.getSysParamDataValue("COM_J1070059_AML_BUSSTYPE_03"))
				.split(",");

		// 衍生性金融商品 Z09,Z10,Z11
		String[] forType07 = Util
				.trim(lmsService
						.getSysParamDataValue("COM_J1070059_AML_BUSSTYPE_07"))
				.split(",");

		// 存匯業務/進出口業務 961,962,963,964
		String[] forType1OR3 = Util
				.trim(lmsService
						.getSysParamDataValue("COM_J1070059_AML_BUSSTYPE_1OR3"))
				.split(",");

		// 不檢核的科目 XXX
		String[] forTypeEx = Util
				.trim(lmsService
						.getSysParamDataValue("COM_J1070059_AML_BUSSTYPE_EX"))
				.split(",");

		List<String> asList03 = Arrays.asList(forType03);
		List<String> asList01_OR_03 = Arrays.asList(forType1OR3);
		List<String> asList07 = Arrays.asList(forType07);
		List<String> asListEx = Arrays.asList(forTypeEx);

		// 3.判斷正確性 g002423BussTypeMap VS cntrDocLoanTpMap

		// 放錯誤的ID與情形
		Map<String, Map<String, String>> errorCustIdBussTypeMap = new HashMap<String, Map<String, String>>();

		if (!asListEx.contains("ALL")) {

			// 掃描每個借款人所有要檢核的科目 cntrDocLoanTpMap ID & 科目
			for (String key : cntrDocLoanTpMap.keySet()) {

				// 該ID下所有科目
				Map<String, String> custLoanTpMap = cntrDocLoanTpMap.get(key);

				if (custLoanTpMap != null && !custLoanTpMap.isEmpty()) {

					// 該ID 0024-23資料
					Map<String, Object> t002423Map = g002423BussTypeMap
							.get(key);

					if (t002423Map == null || t002423Map.isEmpty()) {
						// 該ID沒有0024-23 ERROR
						if (errorCustIdBussTypeMap.containsKey(key)) {
							Map<String, String> errMap = errorCustIdBussTypeMap
									.get(key);
							errMap.put("NO_0024_23", "NO_0024_23");
							errorCustIdBussTypeMap.put(key, errMap);
						} else {
							Map<String, String> errMap = new HashMap<String, String>();
							errMap.put("NO_0024_23", "NO_0024_23");
							errorCustIdBussTypeMap.put(key, errMap);
						}

					} else {
						// 檢核每一個科目
						for (String loanTp : custLoanTpMap.keySet()) {

							// 該科目是否非屬授信業務
							boolean notLoan = false;

							// 屬於不檢核之科目，直接SKIP
							if (asListEx.contains(loanTp)
									|| asListEx.contains("ALL")) {
								continue;
							}

							// 進出口業務
							if (asList03.contains(loanTp)) {
								notLoan = true;
								// 看0024-23 有沒有勾3
								String LUV_DEPT_3 = Util.trim(t002423Map
										.get("LUV_DEPT_3"));
								if (Util.equals(LUV_DEPT_3, "")) {

									// I-110-0329_11557_B1001
									// 針對既有自然人客戶增加KYC系統異常機制，KYC異常時可BYPASS
									// 有啟用call SP才去撈這段訊息
									// clsL120M01A.error080=查詢統編{0}，回應碼:{1}-{2}
									String msgFromSP = check0024_23_call_SCMLUINQ ? "<BR/>"
											+ Util.trim(MapUtils.getString(
													t002423Map,
													"LUV_DEPT_3_MSG")) : "";

									// 0024-23 沒有勾進出口
									if (errorCustIdBussTypeMap.containsKey(key)) {
										Map<String, String> errMap = errorCustIdBussTypeMap
												.get(key);
										errMap.put("03", msgFromSP);
										errorCustIdBussTypeMap.put(key, errMap);
									} else {
										Map<String, String> errMap = new HashMap<String, String>();
										errMap.put("03", msgFromSP);
										errorCustIdBussTypeMap.put(key, errMap);
									}

								}
								continue;
							}

							// 存匯業務/進出口業務
							if (asList01_OR_03.contains(loanTp)) {
								notLoan = true;
								// 看0024-23 有沒有勾1或3
								String LUV_DEPT_1 = Util.trim(t002423Map
										.get("LUV_DEPT_1"));
								String LUV_DEPT_3 = Util.trim(t002423Map
										.get("LUV_DEPT_3"));
								if (Util.equals(LUV_DEPT_1, "")
										&& Util.equals(LUV_DEPT_3, "")) {

									// I-110-0329_11557_B1001
									// 針對既有自然人客戶增加KYC系統異常機制，KYC異常時可BYPASS
									// 有啟用call SP才去撈這段訊息
									// clsL120M01A.error080=查詢統編{0}，回應碼:{1}-{2}
									String msgFromSP = check0024_23_call_SCMLUINQ ? "<BR/>"
											+ Util.trim(MapUtils.getString(
													t002423Map,
													"LUV_DEPT_1_MSG")) : "";

									// 0024-23 沒有存匯業務/進出口業務
									if (errorCustIdBussTypeMap.containsKey(key)) {
										Map<String, String> errMap = errorCustIdBussTypeMap
												.get(key);
										errMap.put("01OR03", msgFromSP);
										errorCustIdBussTypeMap.put(key, errMap);
									} else {
										Map<String, String> errMap = new HashMap<String, String>();
										errMap.put("01OR03", msgFromSP);
										errorCustIdBussTypeMap.put(key, errMap);
									}

								}
								continue;
							}

							// 衍生性金融商品
							if (asList07.contains(loanTp)) {
								notLoan = true;
								// 看0024-23 有沒有勾7
								String LUV_DEPT_7 = Util.trim(t002423Map
										.get("LUV_DEPT_7"));
								if (Util.equals(LUV_DEPT_7, "")) {

									// I-110-0329_11557_B1001
									// 針對既有自然人客戶增加KYC系統異常機制，KYC異常時可BYPASS
									// 有啟用call SP才去撈這段訊息
									// clsL120M01A.error080=查詢統編{0}，回應碼:{1}-{2}
									String msgFromSP = check0024_23_call_SCMLUINQ ? "<BR/>"
											+ Util.trim(MapUtils.getString(
													t002423Map,
													"LUV_DEPT_7_MSG")) : "";

									// 0024-23 沒有勾衍生性金融商品
									if (errorCustIdBussTypeMap.containsKey(key)) {
										Map<String, String> errMap = errorCustIdBussTypeMap
												.get(key);
										errMap.put("07", msgFromSP);
										errorCustIdBussTypeMap.put(key, errMap);
									} else {
										Map<String, String> errMap = new HashMap<String, String>();
										errMap.put("07", msgFromSP);
										errorCustIdBussTypeMap.put(key, errMap);
									}

								}
								continue;
							}

							// 其餘歸授信
							if (!notLoan) {
								// 看0024-23 有沒有勾2
								String LUV_DEPT_2 = Util.trim(t002423Map
										.get("LUV_DEPT_2"));
								if (Util.equals(LUV_DEPT_2, "")) {

									// I-110-0329_11557_B1001
									// 針對既有自然人客戶增加KYC系統異常機制，KYC異常時可BYPASS
									// 有啟用call SP才去撈這段訊息
									// clsL120M01A.error080=查詢統編{0}，回應碼:{1}-{2}
									String msgFromSP = check0024_23_call_SCMLUINQ ? "<BR/>"
											+ Util.trim(MapUtils.getString(
													t002423Map,
													"LUV_DEPT_2_MSG")) : "";

									// 0024-23 沒有勾授信
									if (errorCustIdBussTypeMap.containsKey(key)) {
										Map<String, String> errMap = errorCustIdBussTypeMap
												.get(key);
										errMap.put("02", msgFromSP);
										errorCustIdBussTypeMap.put(key, errMap);
									} else {
										Map<String, String> errMap = new HashMap<String, String>();
										errMap.put("02", msgFromSP);
										errorCustIdBussTypeMap.put(key, errMap);
									}

								}
								continue;
							}

						}
					}
				}

			}
		}
		// 4.組成ERROR字串
		if (errorCustIdBussTypeMap != null && !errorCustIdBussTypeMap.isEmpty()) {
			StringBuffer errBuff = new StringBuffer("");
			for (String errKey : errorCustIdBussTypeMap.keySet()) {
				Map<String, String> errMap = errorCustIdBussTypeMap.get(errKey);
				if (errMap != null && !errMap.isEmpty()) {
					errBuff.append(errKey)
							.append(" ")
							.append(Util.trim(cntrAllCustIdNameMap.get(errKey)))
							.append(":");
					for (String tKey : errMap.keySet()) {
						if (Util.equals(tKey, "NO_0024_23")) {
							// AML.error021_002423_NO_0024_23=無0024-23資料;
							errBuff.append(pop.getProperty(
									"AML.error021_002423_NO_0024_23", ""));
						} else if (Util.equals(tKey, "03")) {
							// AML.error021_002423_03=未勾選03-進出口業務;
							errBuff.append(pop
									.getProperty("AML.error021_002423_03"));
							errBuff.append(MapUtils.getString(errMap, tKey, ""));
						} else if (Util.equals(tKey, "07")) {
							// AML.error021_002423_07=未勾選07-衍生性金融商品業務;
							errBuff.append(pop
									.getProperty("AML.error021_002423_07"));
							errBuff.append(MapUtils.getString(errMap, tKey, ""));
						} else if (Util.equals(tKey, "01OR03")) {
							// AML.error021_002423_01OR03=未勾選01-存匯業務或03-進出口業務;
							errBuff.append(pop
									.getProperty("AML.error021_002423_01OR03"));
							errBuff.append(MapUtils.getString(errMap, tKey, ""));
						} else if (Util.equals(tKey, "02")) {
							// AML.error021_002423_02=未勾選02-授信業務;
							errBuff.append(pop
									.getProperty("AML.error021_002423_02"));
							errBuff.append(MapUtils.getString(errMap, tKey, ""));
						}
					}
					errBuff.append("<BR>");
				}

			}
			errMsg = Util.trim(errBuff.toString());
		}

		return errMsg;
	}

	private boolean is_function_on_codetype(String paramStr) {
		return is_function_on_codetype(paramStr, null);
	}

	private boolean is_function_on_codetype(String paramStr, Date raw_cmpDate) {
		Date cmpDate = (raw_cmpDate == null) ? CapDate.getCurrentTimestamp()
				: raw_cmpDate;
		Map<String, String> map = get_codeTypeWithOrder("LMS_FUNC_ON_FLAG",
				"zh_TW");
		if (map.containsKey(paramStr)) {
			String val = Util.trim(map.get(paramStr));
			if (Util.equals("N", val)) {
				return false;
			} else {
				Date onDate = TWNDate.valueOf(val);
				if (onDate == null || LMSUtil.cmpDate(cmpDate, ">=", onDate)) {
					return true;
				} else {
					return false;
				}
			}
		}
		return true; // default 生效
	}

	private Map<String, String> get_codeTypeWithOrder(String codeType,
			String locale) {
		Map<String, String> r = new LinkedHashMap<String, String>();
		if (Util.isNotEmpty(codeType)) {
			List<CodeType> list = codetypeservice.findByCodeTypeList(codeType,
					locale);
			if (CollectionUtils.isNotEmpty(list)) {
				for (CodeType ct : list) {
					r.put(ct.getCodeValue(), ct.getCodeDesc());
				}
			}
		}
		return r;
	}

	/**
	 * J-107-0070-001 Web e-Loan 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
	 * 
	 * @param mainId
	 * @throws CapException
	 */
	@Override
	public String chkSeniorMgrIsOkForRptDoc(String mainId,
			LinkedHashMap<String, String> custIdMapAml) throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS20APanel.class);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		StringBuffer errMsg = new StringBuffer("");
		StringBuffer errNoConfirm = new StringBuffer("");
		StringBuffer errNoL120s01p = new StringBuffer("");
		String dataFrom = "0024";
		Map<String, Object> chkInsteadMap = this.checkInstead(mainId);
		boolean instead = MapUtils.getBooleanValue(chkInsteadMap, "instead",
				false);
		String queryBrId = MapUtils.getString(chkInsteadMap, "queryBrId");
		String unitNo = (instead ? queryBrId : user.getUnitNo());
		boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchSrv
				.getBranch(unitNo).getBrNoFlag());

		if (isOverSea) {
			return errMsg.toString();
		}

		// 後台管理->系統設定維護->COM_J1070070_AML_SMGR_CHK_OFF OV
		// 取消檢核無高階管理人員不得送呈主管，OV代表所有海外分行不檢核
		if (!this.needChkSeniorMgrBeforeSendBoss(unitNo)) {
			return errMsg.toString();
		}

		Date queryDateS = null;
		L120S09B l120s09b = this.findL120s09bByMainId(mainId);
		if (l120s09b == null) {
			// new Function
			List<L120S09A> l120s09as = this
					.findL120s09aByMainIdWithOrder1(mainId);
			if (l120s09as != null && !l120s09as.isEmpty()) {
				for (L120S09A l120s09a : l120s09as) {
					queryDateS = l120s09a.getQueryDateS();
					break;
				}
			}
		} else {
			// old Function
			queryDateS = l120s09b.getQueryDateS();
		}

		if (queryDateS == null) {
			queryDateS = CapDate.getCurrentTimestamp();
		}

		// 為了避免上線後，在途文件已經掃描過了卻因為沒有高階管理人員還要在掃描一次造成民怨，僅針對AML/CFT
		// 查詢日期在高階管理人員生效日之後(2018/06/01)才檢核高階管理人員是否空白與0024是否一致
		if (!this.isSeniorMgrEffective(queryDateS)) {
			// 後台管理->系統設定維護->COM_J1070070_AML_SMGR_START_DT 2018-06-01
			// AML/CFT查詢日期再生效日前，不檢核有無高階管理人員
			return errMsg.toString();
		}

		// 借款人-行業對象別
		LinkedHashMap<String, String> idAndBusCodeMap = new LinkedHashMap<String, String>();

		// 檢核借款人基本資料高階管理人員欄位不得空白
		StringBuffer beneficiaryLostBuf = new StringBuffer("");
		List<L120S01B> l120s01bs = (List<L120S01B>) this.findListByMainId(
				L120S01B.class, mainId);

		if (l120s01bs != null) {
			for (L120S01B l120s01b : l120s01bs) {
				String s01bCustId = Util.trim(l120s01b.getCustId());
				String s01bDupNo = Util.trim(l120s01b.getDupNo());
				String busCode = Util.trim(l120s01b.getBusCode());

				// 簽報書檢核時，行業對象別從簽報書借款基本資料來
				// 動審表檢核時，行業對象別從0024來(因為借款人、共借人可能與簽報書不同)
				if (!idAndBusCodeMap.containsKey(s01bCustId + s01bDupNo)) {
					idAndBusCodeMap.put(s01bCustId + s01bDupNo, busCode);
				}

				if (Util.notEquals(busCode, "")
						&& !LMSUtil.isBusCode_060000_130300(busCode)) {
					// 企業戶才要檢核高階管理人員不得空白
					if (Util.equals(l120s01b.getSeniorMgr(), "")) {
						beneficiaryLostBuf.append(Util.equals(
								beneficiaryLostBuf.toString(), "") ? "" : "、");
						beneficiaryLostBuf.append(s01bCustId + s01bDupNo);
					}
				}

			}
		}
		if (Util.notEquals(beneficiaryLostBuf.toString(), "")) {
			// AML.error022=「{0}」借款人基本資料高階管理人員欄位不得空白
			errMsg.append(Util.equals(errMsg, "") ? "" : "<BR>");
			errMsg.append(MessageFormat.format(pop.getProperty("AML.error022"),
					beneficiaryLostBuf.toString()));

		}

		// 檢核0024是否有高階管理人員
		for (String fullCustId : custIdMapAml.keySet()) {
			String[] fullKey = fullCustId.split("-");
			String custId = fullKey[0];
			String dupNo = fullKey[1];
			String custName = custIdMapAml.get(fullCustId);

			String busCode = "";
			if (idAndBusCodeMap.containsKey(custId + dupNo)) {
				busCode = MapUtils.getString(idAndBusCodeMap, custId + dupNo,
						"");
				if (LMSUtil.isBusCode_060000_130300(busCode)
						|| Util.equals(busCode, "")) {
					// 沒有行業對象別就先不檢核
					continue;
				}
			} else {
				continue;
			}

			if (!isOverSea) {
				// 國內
				dataFrom = "0024";

				List<Map<String, Object>> cmfDataList = misDbService
						.findCMFLUNSRByCustId(custId, dupNo);

				if (cmfDataList == null || cmfDataList.isEmpty()) {
					errNoConfirm.append(Util.equals(errNoConfirm, "") ? ""
							: "、");
					errNoConfirm.append(custId + dupNo + " " + custName);
				} else {

					int count = 0;
					for (Map<String, Object> cmfData : cmfDataList) {
						String BUSCD = Util.trim(MapUtils.getString(cmfData,
								"BUSCD"));
						String CMSR_SR_PV_ID = Util.trim(MapUtils.getString(
								cmfData, "CMSR_SR_PV_ID"));
						String CMSR_SR_PV_CNAME = Util.trim(MapUtils.getString(
								cmfData, "CMSR_SR_PV_CNAME"));
						String CMSR_SR_PV_ENAME = Util.trim(MapUtils.getString(
								cmfData, "CMSR_SR_PV_ENAME"));
						String CMSR_SR_PV_BD = Util.trim(MapUtils.getString(
								cmfData, "CMSR_SR_PV_BD"));
						String CMSR_SR_PV_NC = Util.trim(MapUtils.getString(
								cmfData, "CMSR_SR_PV_NC"));

						if (Util.notEquals(CMSR_SR_PV_ID, "")
								|| Util.notEquals(CMSR_SR_PV_CNAME, "")
								|| Util.notEquals(CMSR_SR_PV_ENAME, "")) {
							count = count + 1;
							// 知道至少有一筆就好
							break;
						}
					}
					// 需確認 時，檢核有沒有L120S01P
					if (count > 0) {
						List<L120S01P> listL120s01p = this
								.findL120s01pByMainIdAndCustIdWithRType(
										mainId,
										custId,
										dupNo,
										UtilConstants.Casedoc.L120s09aBlackListCtlTarget.高階管理人員);
						if (listL120s01p == null || listL120s01p.isEmpty()) {
							errNoL120s01p.append(Util.equals(
									errNoL120s01p.toString(), "") ? "" : "、");
							errNoL120s01p.append(custId + dupNo);
						}
					} else {
						// 一筆都沒有
						if (this.needChk0024MustHaveSeniorMgrBeforeSendBoss(unitNo)) {
							errNoConfirm
									.append(Util.equals(errNoConfirm, "") ? ""
											: "、");
							errNoConfirm
									.append(custId + dupNo + " " + custName);
						}

					}

				}
			} else {
				// 海外
				dataFrom = "AS400";

				// busCode 從前面idAndBusCodeMap 來
				if (Util.notEquals(busCode, "")
						&& !LMSUtil.isBusCode_060000_130300(busCode)) {

				}

			}
		}

		if (Util.notEquals(errNoConfirm.toString(), "")) {

			// AML.error023=「{0}」借款人0024-23無「高階管理人員」資料
			errMsg.append(Util.equals(errMsg, "") ? "" : "<BR>");
			errMsg.append(MessageFormat.format(pop.getProperty("AML.error023"),
					errNoConfirm.toString(), dataFrom));

		}

		if (Util.notEquals(errNoL120s01p.toString(), "")) {
			// AML.error024=「{0}」借款人0024-23有「高階管理人員」資料，e-Loan借款人基本資料「高階管理人員」不得為「無」
			errMsg.append(Util.equals(errMsg, "") ? "" : "<BR>");
			errMsg.append(MessageFormat.format(pop.getProperty("AML.error024"),
					errNoConfirm.toString(), dataFrom));

		}

		return errMsg.toString();

	}

	/**
	 * J-107-0070-001 Web e-Loan 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
	 * 
	 * @param mainId
	 * @throws CapException
	 */
	@Override
	public String chkSeniorMgrIsOkForDrawDown(String mainId,
			LinkedHashMap<String, String> allBorrowerIdMap) throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS20APanel.class);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		StringBuffer errBorrowerAMLEmptyField = new StringBuffer("");

		StringBuffer errMsg = new StringBuffer("");
		StringBuffer errNoConfirm = new StringBuffer("");
		StringBuffer errNoL120s01p = new StringBuffer("");
		StringBuffer errNo0024 = new StringBuffer("");
		String dataFrom = "0024";
		Map<String, Object> chkInsteadMap = this.checkInstead(mainId);
		boolean instead = MapUtils.getBooleanValue(chkInsteadMap, "instead",
				false);
		String queryBrId = MapUtils.getString(chkInsteadMap, "queryBrId");
		String unitNo = (instead ? queryBrId : user.getUnitNo());

		boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchSrv
				.getBranch(unitNo).getBrNoFlag());

		if (isOverSea) {
			return errMsg.toString();
		}

		// 後台管理->系統設定維護->COM_J1070070_AML_SMGR_CHK_OFF OV
		// 取消檢核無高階管理人員不得送呈主管，OV代表所有海外分行不檢核
		if (!this.needChkSeniorMgrBeforeSendBoss(unitNo)) {
			return errMsg.toString();
		}

		Date queryDateS = null;
		L120S09B l120s09b = this.findL120s09bByMainId(mainId);
		if (l120s09b == null) {
			// new Function
			List<L120S09A> l120s09as = this
					.findL120s09aByMainIdWithOrder1(mainId);
			if (l120s09as != null && !l120s09as.isEmpty()) {
				for (L120S09A l120s09a : l120s09as) {
					queryDateS = l120s09a.getQueryDateS();
					break;
				}
			}
		} else {
			// old Function
			queryDateS = l120s09b.getQueryDateS();
		}

		if (queryDateS == null) {
			queryDateS = CapDate.getCurrentTimestamp();
		}

		// 為了避免上線後，在途文件已經掃描過了卻因為沒有高階管理人員還要在掃描一次造成民怨，僅針對AML/CFT
		// 查詢日期在高階管理人員生效日之後(2018/06/01)才檢核高階管理人員是否空白與0024是否一致
		if (!this.isSeniorMgrEffective(queryDateS)) {
			// 後台管理->系統設定維護->COM_J1070070_AML_SMGR_START_DT 2018-06-01
			// AML/CFT查詢日期再生效日前，不檢核有無高階管理人員
			return errMsg.toString();
		}

		// 借款人-行業對象別
		LinkedHashMap<String, String> idAndBusCodeMap = new LinkedHashMap<String, String>();

		// 檢核借款人基本資料高階管理人員欄位不得空白
		// 取得0024判斷行業對象別
		if (allBorrowerIdMap != null && !allBorrowerIdMap.isEmpty()) {
			for (String chkKey : allBorrowerIdMap.keySet()) {
				String[] chkKeyArr = chkKey.split("-");
				String chkId = chkKeyArr[0];
				String chkDupNo = chkKeyArr[1];
				String chkName = Util.trim(allBorrowerIdMap.get(chkKey));

				// 取得0024判斷行業對象別
				Map<String, Object> m0024 = iCustomerService.findByIdDupNo(
						chkId, chkDupNo);
				String busCode = (m0024 != null && !m0024.isEmpty()) ? Util
						.trim(MapUtils.getString(m0024, "BUSCD")) : "";

				// 簽報書檢核時，行業對象別從簽報書借款基本資料來
				// 動審表檢核時，行業對象別從0024來(因為借款人、共借人可能與簽報書不同)
				if (!idAndBusCodeMap.containsKey(chkId + chkDupNo)) {
					idAndBusCodeMap.put(chkId + chkDupNo, busCode);
				}

				// 企業戶才要檢核高階管理人員
				if (Util.equals(busCode, "")) {
					errNo0024.append(Util.equals(errNo0024.toString(), "") ? ""
							: "、");
					errNo0024.append(chkId + chkDupNo + " " + chkName);
				} else {
					if (Util.notEquals(busCode, "")
							&& !LMSUtil.isBusCode_060000_130300(busCode)) {
						// 企業戶
						L164S01A l164s01a = this.findL164s01aByUniqueKey(
								mainId, chkId, chkDupNo);
						if (l164s01a != null) {
							if (Util.equals(l164s01a.getSeniorMgr(), "")) {

								errBorrowerAMLEmptyField
										.append(Util.equals(
												errBorrowerAMLEmptyField
														.toString(), "") ? ""
												: "、");
								errBorrowerAMLEmptyField.append(chkId
										+ chkDupNo + " " + chkName);
							}
						} else {
							errBorrowerAMLEmptyField
									.append(Util.equals(
											errBorrowerAMLEmptyField.toString(),
											"") ? "" : "、");
							errBorrowerAMLEmptyField.append(chkId + chkDupNo
									+ " " + chkName);
						}
					}
				}

			}
		}

		if (Util.notEquals(errNo0024.toString(), "")) {
			if (this.needChk0024Exist(queryBrId)) {
				// AML.error010=主從債務人「{0}」於0024無行業對象別資料。
				errMsg.append(Util.equals(errMsg, "") ? "" : "<BR>");
				errMsg.append(MessageFormat.format(pop
						.getProperty("AML.error010"), errNo0024.toString()
						.toString()));
			}

		}

		if (Util.notEquals(errBorrowerAMLEmptyField.toString(), "")) {
			// AML.error008=借款人/共同借款人{0}於相關報表->主從債務人資料表中尚有洗錢防制所需欄位未完成輸入。
			errMsg.append(Util.equals(errMsg, "") ? "" : "<BR>");
			errMsg.append(MessageFormat.format(pop.getProperty("AML.error008"),
					errBorrowerAMLEmptyField.toString()));
		}

		// 檢核高階管理人員
		for (String fullCustId : allBorrowerIdMap.keySet()) {
			String[] fullKey = fullCustId.split("-");
			String custId = fullKey[0];
			String dupNo = fullKey[1];
			String custName = allBorrowerIdMap.get(fullCustId);
			String busCode = "";
			if (idAndBusCodeMap.containsKey(custId + dupNo)) {
				busCode = MapUtils.getString(idAndBusCodeMap, custId + dupNo,
						"");
				if (LMSUtil.isBusCode_060000_130300(busCode)
						|| Util.equals(busCode, "")) {
					// 沒有行業對象別就先不檢核
					// 先出錯誤訊息errNo0024--AML.error010=主從債務人「{0}」於0024無行業對象別資料。
					continue;
				}
			} else {
				continue;
			}

			if (!isOverSea) {
				// 國內
				dataFrom = "0024";
				List<Map<String, Object>> cmfDataList = misDbService
						.findCMFLUNSRByCustId(custId, dupNo);

				if (cmfDataList == null || cmfDataList.isEmpty()) {
					errNoConfirm.append(Util.equals(errNoConfirm, "") ? ""
							: "、");
					errNoConfirm.append(custId + dupNo + " " + custName);
				} else {

					int count = 0;
					for (Map<String, Object> cmfData : cmfDataList) {
						String BUSCD = Util.trim(MapUtils.getString(cmfData,
								"BUSCD"));
						String CMSR_SR_PV_ID = Util.trim(MapUtils.getString(
								cmfData, "CMSR_SR_PV_ID"));
						String CMSR_SR_PV_CNAME = Util.trim(MapUtils.getString(
								cmfData, "CMSR_SR_PV_CNAME"));
						String CMSR_SR_PV_ENAME = Util.trim(MapUtils.getString(
								cmfData, "CMSR_SR_PV_ENAME"));
						String CMSR_SR_PV_BD = Util.trim(MapUtils.getString(
								cmfData, "CMSR_SR_PV_BD"));
						String CMSR_SR_PV_NC = Util.trim(MapUtils.getString(
								cmfData, "CMSR_SR_PV_NC"));

						if (Util.notEquals(CMSR_SR_PV_ID, "")
								|| Util.notEquals(CMSR_SR_PV_CNAME, "")
								|| Util.notEquals(CMSR_SR_PV_ENAME, "")) {
							// 知道至少有一筆就好
							count = count + 1;
							break;
						}
					}
					// 需確認 時，檢核有沒有L120S01P
					if (count > 0) {
						List<L120S01P> listL120s01p = this
								.findL120s01pByMainIdAndCustIdWithRType(
										mainId,
										custId,
										dupNo,
										UtilConstants.Casedoc.L120s09aBlackListCtlTarget.高階管理人員);
						if (listL120s01p == null || listL120s01p.isEmpty()) {
							errNoL120s01p.append(Util.equals(
									errNoL120s01p.toString(), "") ? "" : "、");
							errNoL120s01p.append(custId + dupNo);
						}
					} else {
						// 一筆都沒有
						if (this.needChk0024MustHaveSeniorMgrBeforeSendBoss(unitNo)) {
							errNoConfirm
									.append(Util.equals(errNoConfirm, "") ? ""
											: "、");
							errNoConfirm
									.append(custId + dupNo + " " + custName);
						}

					}

				}
			} else {
				// 海外--不檢核
				dataFrom = "AS400";

			}
		}

		if (Util.notEquals(errNoConfirm.toString(), "")) {

			// AML.error023=「{0}」借款人0024-23無「高階管理人員」資料
			errMsg.append(Util.equals(errMsg, "") ? "" : "<BR>");
			errMsg.append(MessageFormat.format(pop.getProperty("AML.error023"),
					errNoConfirm.toString(), dataFrom));

		}

		if (Util.notEquals(errNoL120s01p.toString(), "")) {
			// AML.error024=「{0}」借款人0024-23有「高階管理人員」資料，e-Loan借款人基本資料「高階管理人員」不得為「無」
			errMsg.append(Util.equals(errMsg, "") ? "" : "<BR>");
			errMsg.append(MessageFormat.format(pop.getProperty("AML.error024"),
					errNoConfirm.toString(), dataFrom));

		}

		return errMsg.toString();

	}

	/**
	 * J-107-0070-001 Web e-Loan 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
	 * 判斷登入分行是否需檢核無高階管理人員不得送呈主管
	 * 
	 * @return
	 */
	@Override
	public boolean needChkSeniorMgrBeforeSendBoss(String QueryBrId) {
		boolean isNeed = true;

		// 後台管理->系統設定維護->COM_J1070070_AML_SMGR_CHK_OFF SG,AU 取消檢核無實質受益人不得送呈主管

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String countryType = Util.trim(branchSrv.getBranch(QueryBrId)
				.getCountryType());

		String notChkCountry = Util.trim(lmsService
				.getSysParamDataValue("COM_J1070070_AML_SMGR_CHK_OFF"));

		if (Util.notEquals(notChkCountry, "")
				&& Util.notEquals(countryType, "")) {
			for (String xx : notChkCountry.split(",")) {
				if (Util.equals(xx, countryType)) {
					isNeed = false;
					break;
				}

				if (Util.notEquals(countryType, "TW") && Util.equals(xx, "OV")) {
					// OV代表所有海外分行都不擋
					isNeed = false;
					break;
				}
			}
		}

		return isNeed;
	}

	/**
	 * J-107-0070-001 Web e-Loan 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
	 * 判斷登入分行是否需檢核無高階管理人員不得送呈主管
	 * 
	 * @return
	 */
	@Override
	public String[] getSortCustRelation(String[] strs) {

		// 對陣列進行排序
		// 轉成數字後再SORT
		List<String> asList = Arrays.asList(strs);
		List<Integer> newList = new ArrayList();

		for (String str : asList) {
			if (Util.notEquals(str, "")) {
				newList.add(new BigDecimal(str).intValue());
			}
		}

		int index = 0;
		int[] newIntArr = new int[newList.toArray().length];
		for (Integer xInt : newList) {
			newIntArr[index] = xInt;
			index++;
		}

		// 對陣列進行排序
		Arrays.sort(newIntArr);

		// 回復原來的陣列
		index = 0;
		String[] rtnList = new String[newList.toArray().length];
		for (Integer intX : newIntArr) {

			String newStr = new BigDecimal(intX).toPlainString();
			rtnList[index] = newStr;
			index++;

		}

		return rtnList;
	}

	/**
	 * J-107-0070-001 Web e-Loan 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
	 * 判斷登入分行是否需檢核無高階管理人員不得送呈主管
	 * 
	 * 
	 * queryDateS : L120S09B 的查詢日期
	 * 
	 * @return
	 */
	@Override
	public boolean isSeniorMgrEffective(Date queryDateS) {
		boolean isStart = false;

		// 後台管理->系統設定維護->COM_J1070070_AML_SMGR_START_DT 2018-06-01
		// AML/CFT查詢日期再生效日前，不檢核有無高階管理人員
		String startDt = Util.trim(lmsService
				.getSysParamDataValue("COM_J1070070_AML_SMGR_START_DT"));

		if (Util.notEquals(startDt, "")) {

			if (queryDateS == null) {
				queryDateS = CapDate.getCurrentTimestamp();
			}

			if (LMSUtil.cmpDate(queryDateS, ">=", CapDate.parseDate(startDt))) {
				isStart = true;
			}
		}

		return isStart;
	}

	/**
	 * J-107-0070-001 Web e-Loan 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
	 * 判斷送呈時不用檢核是否0024至少要有一筆高階管理人員，OV代表所有海外分行不檢核
	 * 
	 * @return
	 */
	@Override
	public boolean needChk0024MustHaveSeniorMgrBeforeSendBoss(String QueryBrId) {
		boolean isNeed = true;

		// 後台管理->系統設定維護->COM_J1070070_AML_SMGR_NOT_NEED Y
		// 送呈時不用檢核是否0024至少要有一筆高階管理人員，OV代表所有海外分行不檢核

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String countryType = Util.trim(branchSrv.getBranch(QueryBrId)
				.getCountryType());

		String notChkCountry = Util.trim(lmsService
				.getSysParamDataValue("COM_J1070070_AML_SMGR_NOT_NEED"));

		if (Util.notEquals(notChkCountry, "")
				&& Util.notEquals(countryType, "")) {
			for (String xx : notChkCountry.split(",")) {
				if (Util.equals(xx, countryType)) {
					isNeed = false;
					break;
				}

				if (Util.notEquals(countryType, "TW") && Util.equals(xx, "OV")) {
					// OV代表所有海外分行都不擋
					isNeed = false;
					break;
				}
			}
		}

		return isNeed;
	}

	/**
	 * J-107-0070-001 Web e-Loan 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
	 * 
	 * @param mainId
	 * @return
	 */
	private int getCustRelationShowOrderNum(String custRelation) {
		int orderNum = 0;
		/*
		 * static final String 借戶 = "1"; static final String 共同借款人 = "2"; static
		 * final String 負責人 = "3"; static final String 連保人 = "4"; static final
		 * String 擔保品提供人 = "5"; static final String 關係企業 = "6"; static final
		 * String 實質受益人 = "7"; static final String 一般保證人 = "8"; static final
		 * String 應收帳款買方無追索 = "9"; static final String 高階管理人員 = "10";
		 */
		if (Util.equals(custRelation,
				UtilConstants.Casedoc.L120s09aBlackListCtlTarget.借戶)) {
			orderNum = 1;
		} else if (Util.equals(custRelation,
				UtilConstants.Casedoc.L120s09aBlackListCtlTarget.共同借款人)) {
			orderNum = 2;
		} else if (Util.equals(custRelation,
				UtilConstants.Casedoc.L120s09aBlackListCtlTarget.負責人)) {
			orderNum = 3;
		} else if (Util.equals(custRelation,
				UtilConstants.Casedoc.L120s09aBlackListCtlTarget.實質受益人)) {
			orderNum = 4;
		} else if (Util.equals(custRelation,
				UtilConstants.Casedoc.L120s09aBlackListCtlTarget.高階管理人員)) {
			orderNum = 5;
		} else if (Util.equals(custRelation,
				UtilConstants.Casedoc.L120s09aBlackListCtlTarget.具控制權人)) {
			// J-108-0039_05097_B1001 Web e-Loan
			// 國內企金授信系統簽報、動審AML頁籤將借戶之「具控制權人」納入應查詢比對黑名單之對象。
			orderNum = 6;
		} else if (Util.equals(custRelation,
				UtilConstants.Casedoc.L120s09aBlackListCtlTarget.連保人)) {
			orderNum = 7;
		} else if (Util.equals(custRelation,
				UtilConstants.Casedoc.L120s09aBlackListCtlTarget.擔保品提供人)) {
			orderNum = 8;
		} else if (Util.equals(custRelation,
				UtilConstants.Casedoc.L120s09aBlackListCtlTarget.關係企業)) {
			orderNum = 9;
		} else if (Util.equals(custRelation,
				UtilConstants.Casedoc.L120s09aBlackListCtlTarget.一般保證人)) {
			orderNum = 10;
		} else if (Util.equals(custRelation,
				UtilConstants.Casedoc.L120s09aBlackListCtlTarget.應收帳款買方無追索)) {
			orderNum = 11;

		} else {
			orderNum = 99;
		}

		return orderNum;
	}

	/**
	 * J-107-0070-001 Web e-Loan 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
	 * 
	 * @param mainId
	 * @return
	 */
	@Override
	public List<L120S09A> findL120s09aByMainIdWithShowOrder(String mainId) {

		List<L120S09A> l120s09as = this.findL120s09aByMainIdWithOrder1(mainId);
		if (l120s09as == null || l120s09as.isEmpty()) {
			return l120s09as;
		}

		/*
		 * search.addOrderBy("custRelation"); search.addOrderBy("custId");
		 * search.addOrderBy("dupNo"); search.addOrderBy("custEName");
		 * search.addOrderBy("custName");
		 */
		List<L120S09A> list = new ArrayList<L120S09A>(l120s09as);

		Collections.sort(list, new Comparator<L120S09A>() {

			@Override
			public int compare(L120S09A object1, L120S09A object2) {
				// TODO Auto-generated method stub
				int cr = 0;
				String[] resStr1 = Util.trim(object1.getCustRelation()).split(
						",");
				resStr1 = getSortCustRelation(resStr1);

				String[] resStr2 = Util.trim(object2.getCustRelation()).split(
						",");
				resStr2 = getSortCustRelation(resStr2);

				String res1 = Util
						.getRightStr(
								"000"
										+ getCustRelationShowOrderNum((resStr1 != null && resStr1.length > 0) ? resStr1[0]
												: ""), 3);
				String res2 = Util
						.getRightStr(
								"000"
										+ getCustRelationShowOrderNum((resStr2 != null && resStr2.length > 0) ? resStr2[0]
												: ""), 3);

				String custId1 = Util.getLeftStr(Util.trim(object1.getCustId())
						+ "          ", 10);
				String custId2 = Util.getLeftStr(Util.trim(object2.getCustId())
						+ "          ", 10);

				String dupNo1 = Util.getLeftStr(Util.trim(object1.getDupNo())
						+ "0", 1);
				String dupNo2 = Util.getLeftStr(Util.trim(object2.getDupNo())
						+ "0", 1);

				String comparStr1 = res1 + custId1 + dupNo1;
				String comparStr2 = res2 + custId2 + dupNo2;

				int a = comparStr2.compareTo(comparStr1);

				// 回傳值: -1 前者比後者小, 0 前者與後者相同, 1 前者比後者大
				if (a != 0) {
					cr = (a > 0) ? -1 : 5;
				}

				return cr;
			}
		});

		return list;
	}

	/**
	 * J-107-0176 代發機制 - 判斷是否為代發
	 * 
	 * @param mainId
	 * @return
	 */
	@Override
	public Map<String, Object> checkInstead(String mainId) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		HashMap<String, Object> returnMap = new HashMap<String, Object>();
		boolean instead = false; // default 否
		String queryBrId = user.getUnitNo(); // default 使用者分行
		L120S09B l120s09b = this.findL120s09bByMainId(mainId);
		if (l120s09b != null
				&& Util.isNotEmpty(Util.nullToSpace(l120s09b.getQueryBrId()))) {
			instead = true;
			queryBrId = l120s09b.getQueryBrId();
		}
		returnMap.put("instead", instead); // 是否為代發
		returnMap.put("queryBrId", queryBrId); // 代發分行

		return returnMap;
	}

	@Override
	public List<L140M01S> findL140m01sByMainIdType(String mainId, String type) {
		return l140m01sDao.findByMainIdType(mainId, type);
	}

	/**
	 * 判斷關係戶是否需要國別 J-107-0248_05097_B1001 Web e-Loan企金授信管理系統AML/CFT增加國別管制名單掃描功能
	 * 
	 * @param custRelationStr
	 * @return
	 */
	@Override
	public boolean isCustRelationNeedCountry(String custRelationStr) {
		boolean isNeed = false;

		if (Util.notEquals(custRelationStr, "")
				&& (Util.equals(custRelationStr,
						UtilConstants.Casedoc.L120s09aBlackListCtlTarget.借戶)
						|| Util.equals(
								custRelationStr,
								UtilConstants.Casedoc.L120s09aBlackListCtlTarget.共同借款人)
						|| Util.equals(
								custRelationStr,
								UtilConstants.Casedoc.L120s09aBlackListCtlTarget.一般保證人)
						|| Util.equals(
								custRelationStr,
								UtilConstants.Casedoc.L120s09aBlackListCtlTarget.連保人) || Util
						.equals(custRelationStr,
								UtilConstants.Casedoc.L120s09aBlackListCtlTarget.擔保品提供人))) {

			isNeed = true;
		}

		return isNeed;
	}

	/**
	 * J-107-0248_05097_B1001 Web e-Loan企金授信管理系統AML/CFT增加國別管制名單掃描功能
	 */
	@Override
	public void chkBlackListDataOk(String mainId) throws CapException {

		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS20APanel.class);

		Map<String, Object> chkInsteadMap = this.checkInstead(mainId);
		boolean instead = MapUtils.getBooleanValue(chkInsteadMap, "instead",
				false);
		String queryBrId = MapUtils.getString(chkInsteadMap, "queryBrId");
		boolean isNeedChkScanDone = this.needChkAmlOkBeforeSendBoss(queryBrId);

		List<L120S09A> l120s09as = this.findL120s09aByMainId(mainId);
		if (l120s09as != null && !l120s09as.isEmpty()) {
			StringBuffer noCountryCust = new StringBuffer("");

			for (L120S09A l120s09a : l120s09as) {
				// J-107-0248_05097_B1001 Web e-Loan企金授信管理系統AML/CFT增加國別管制名單掃描功能
				boolean needCountry = false;
				String xCustRelation = l120s09a.getCustRelation();
				String xCustId = l120s09a.getCustId();
				String xDupNo = l120s09a.getDupNo();
				String xCustName = l120s09a.getCustName();
				String xCustEName = l120s09a.getCustEName();
				String xCountry = Util.trim(l120s09a.getCountry());

				String[] strs = this.getSortCustRelation(Util.trim(
						xCustRelation).split(","));

				for (int i = 0; i < strs.length; i++) {

					// J-107-0248_05097_B1001 Web
					// e-Loan企金授信管理系統AML/CFT增加國別管制名單掃描功能
					if (Util.notEquals(strs[i], "")
							&& this.isCustRelationNeedCountry(strs[i])) {
						// 借款人(含共同借款人)及保證人(含一般保證、連帶保證、擔保品提供人)國別
						needCountry = true;
					}

				}

				// J-107-0248_05097_B1001 Web e-Loan企金授信管理系統AML/CFT增加國別管制名單掃描功能
				if (needCountry) {
					if (Util.equals(xCountry, "")) {
						if (Util.notEquals(Util.trim(noCountryCust.toString()),
								"")) {
							noCountryCust.append("、")
									.append(Util.trim(l120s09a.getCustId()))
									.append("  ")
									.append(Util.trim(l120s09a.getCustName()));
						} else {
							noCountryCust
									.append(Util.trim(l120s09a.getCustId()))
									.append("  ")
									.append(Util.trim(l120s09a.getCustName()));
						}
					}
				}

			}

			if (Util.notEquals(Util.trim(noCountryCust.toString()), "")) {
				// AML.error026=欲掃描之名單「{0}」必須要有「{0}」資料。
				// L120S09a.country=國別
				throw new CapMessageException(MessageFormat.format(
						pop.getProperty("AML.error026"),
						Util.trim(noCountryCust.toString()),
						pop.getProperty("L120S09a.country")), getClass());
			}

		}

	}

	/**
	 * J-108-0039_05097_B1001 Web e-Loan
	 * 國內企金授信系統簽報、動審AML頁籤將借戶之「具控制權人」納入應查詢比對黑名單之對象。
	 * 
	 * @param mainId
	 * @throws CapException
	 */
	@Override
	public String chkCtrlPeoIsOkForRptDoc(String mainId,
			LinkedHashMap<String, String> custIdMapAml) throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS20APanel.class);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		StringBuffer errMsg = new StringBuffer("");
		StringBuffer errNoConfirm = new StringBuffer("");
		StringBuffer errNoL120s01p = new StringBuffer("");
		String dataFrom = "0024";
		Map<String, Object> chkInsteadMap = this.checkInstead(mainId);
		boolean instead = MapUtils.getBooleanValue(chkInsteadMap, "instead",
				false);
		String queryBrId = MapUtils.getString(chkInsteadMap, "queryBrId");
		String unitNo = (instead ? queryBrId : user.getUnitNo());
		boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchSrv
				.getBranch(unitNo).getBrNoFlag());

		if (isOverSea) {
			return errMsg.toString();
		}

		// 後台管理->系統設定維護->COM_AML_CTRLPEO_CHK_OFF
		// 取消檢核無具控制權人不得送呈主管，OV代表所有海外分行不檢核
		if (!this.needChkCtrlPeoBeforeSendBoss(unitNo)) {
			return errMsg.toString();
		}

		Date queryDateS = null;
		L120S09B l120s09b = this.findL120s09bByMainId(mainId);
		if (l120s09b == null) {
			// new Function
			List<L120S09A> l120s09as = this
					.findL120s09aByMainIdWithOrder1(mainId);
			if (l120s09as != null && !l120s09as.isEmpty()) {
				for (L120S09A l120s09a : l120s09as) {
					queryDateS = l120s09a.getQueryDateS();
					break;
				}
			}
		} else {
			// old Function
			queryDateS = l120s09b.getQueryDateS();
		}

		if (queryDateS == null) {
			queryDateS = CapDate.getCurrentTimestamp();
		}

		// 為了避免上線後，在途文件已經掃描過了卻因為沒有具控制權人還要在掃描一次造成民怨，僅針對AML/CFT
		// 查詢日期在具控制權人生效日之後(2019/05/01)才檢核具控制權人是否空白與0024是否一致
		if (!this.isCtrlPeoEffective(queryDateS)) {
			// 後台管理->系統設定維護->COM_AML_CTRLPEO_START_DT 2019/05/01
			// AML/CFT查詢日期再生效日前，不檢核有無具控制權人
			return errMsg.toString();
		}

		// 借款人-行業對象別
		LinkedHashMap<String, String> idAndBusCodeMap = new LinkedHashMap<String, String>();

		// 檢核借款人基本資料具控制權人欄位不得空白
		StringBuffer beneficiaryLostBuf = new StringBuffer("");
		List<L120S01B> l120s01bs = (List<L120S01B>) this.findListByMainId(
				L120S01B.class, mainId);

		if (l120s01bs != null) {
			for (L120S01B l120s01b : l120s01bs) {
				String s01bCustId = Util.trim(l120s01b.getCustId());
				String s01bDupNo = Util.trim(l120s01b.getDupNo());
				String busCode = Util.trim(l120s01b.getBusCode());

				// 簽報書檢核時，行業對象別從簽報書借款基本資料來
				// 動審表檢核時，行業對象別從0024來(因為借款人、共借人可能與簽報書不同)
				if (!idAndBusCodeMap.containsKey(s01bCustId + s01bDupNo)) {
					idAndBusCodeMap.put(s01bCustId + s01bDupNo, busCode);
				}

				if (Util.notEquals(busCode, "")
						&& !LMSUtil.isBusCode_060000_130300(busCode)) {
					// 企業戶才要檢核具控制權人不得空白
					if (Util.equals(l120s01b.getCtrlPeo(), "")) {
						beneficiaryLostBuf.append(Util.equals(
								beneficiaryLostBuf.toString(), "") ? "" : "、");
						beneficiaryLostBuf.append(s01bCustId + s01bDupNo);
					}
				}

			}
		}
		if (Util.notEquals(beneficiaryLostBuf.toString(), "")) {
			// AML.error027=「{0}」借款人基本資料「具控制權人」欄位不得空白
			errMsg.append(Util.equals(errMsg, "") ? "" : "<BR>");
			errMsg.append(MessageFormat.format(pop.getProperty("AML.error027"),
					beneficiaryLostBuf.toString()));

		}

		// 檢核0024是否有具控制權人
		for (String fullCustId : custIdMapAml.keySet()) {
			String[] fullKey = fullCustId.split("-");
			String custId = fullKey[0];
			String dupNo = fullKey[1];
			String custName = custIdMapAml.get(fullCustId);

			String busCode = "";
			if (idAndBusCodeMap.containsKey(custId + dupNo)) {
				busCode = MapUtils.getString(idAndBusCodeMap, custId + dupNo,
						"");
				if (LMSUtil.isBusCode_060000_130300(busCode)
						|| Util.equals(busCode, "")) {
					// 沒有行業對象別就先不檢核
					continue;
				}
			} else {
				continue;
			}

			// J-108-0039_05097_B1001 TODO XXXXXXXXXXXXXXXXX
			if (!isOverSea) {
				// 國內
				dataFrom = "0024";

				List<Map<String, Object>> cmfDataList = misDbService
						.findCMFLUNB1ByCustId(custId, dupNo);
				if (cmfDataList == null || cmfDataList.isEmpty()) {
					errNoConfirm.append(Util.equals(errNoConfirm, "") ? ""
							: "、");
					errNoConfirm.append(custId + dupNo + " " + custName);
				} else {

					int count = 0;
					for (Map<String, Object> cmfData : cmfDataList) {
						String BUSCD = Util.trim(MapUtils.getString(cmfData,
								"BUSCD"));
						String CMSR_SR_PV_ID = Util.trim(MapUtils.getString(
								cmfData, "CMB1_CNTRL_PV_ID"));
						String CMSR_SR_PV_CNAME = Util.trim(MapUtils.getString(
								cmfData, "CMB1_CNTRL_PV_CNAME"));
						String CMSR_SR_PV_ENAME = Util.trim(MapUtils.getString(
								cmfData, "CMB1_CNTRL_PV_ENAME"));
						String CMSR_SR_PV_BD = Util.trim(MapUtils.getString(
								cmfData, "CMB1_CNTRL_PV_BD"));
						String CMSR_SR_PV_NC = Util.trim(MapUtils.getString(
								cmfData, "CMB1_CNTRL_PV_NC"));

						if (Util.notEquals(CMSR_SR_PV_ID, "")
								|| Util.notEquals(CMSR_SR_PV_CNAME, "")
								|| Util.notEquals(CMSR_SR_PV_ENAME, "")) {
							count = count + 1;
							// 知道至少有一筆就好
							break;
						}
					}
					// 需確認 時，檢核有沒有L120S01P
					if (count > 0) {
						List<L120S01P> listL120s01p = this
								.findL120s01pByMainIdAndCustIdWithRType(
										mainId,
										custId,
										dupNo,
										UtilConstants.Casedoc.L120s09aBlackListCtlTarget.具控制權人);
						if (listL120s01p == null || listL120s01p.isEmpty()) {
							errNoL120s01p.append(Util.equals(
									errNoL120s01p.toString(), "") ? "" : "、");
							errNoL120s01p.append(custId + dupNo);
						}
					} else {
						// 一筆都沒有
						if (this.needChk0024MustHaveCtrlPeoBeforeSendBoss(unitNo)) {
							errNoConfirm
									.append(Util.equals(errNoConfirm, "") ? ""
											: "、");
							errNoConfirm
									.append(custId + dupNo + " " + custName);
						}

					}

				}
			} else {
				// 海外
				dataFrom = "AS400";

				// busCode 從前面idAndBusCodeMap 來
				if (Util.notEquals(busCode, "")
						&& !LMSUtil.isBusCode_060000_130300(busCode)) {

				}

			}
		}

		if (Util.notEquals(errNoConfirm.toString(), "")) {

			// AML.error028=「{0}」借款人0024-23無「具控制權人」資料
			errMsg.append(Util.equals(errMsg, "") ? "" : "<BR>");
			errMsg.append(MessageFormat.format(pop.getProperty("AML.error028"),
					errNoConfirm.toString(), dataFrom));

		}

		if (Util.notEquals(errNoL120s01p.toString(), "")) {
			// AML.error029=「{0}」借款人0024-23有「具控制權人」資料，e-Loan借款人基本資料「具控制權人」不得為「無」
			errMsg.append(Util.equals(errMsg, "") ? "" : "<BR>");
			errMsg.append(MessageFormat.format(pop.getProperty("AML.error029"),
					errNoConfirm.toString(), dataFrom));

		}

		return errMsg.toString();

	}

	/**
	 * J-108-0039_05097_B1001 Web e-Loan
	 * 國內企金授信系統簽報、動審AML頁籤將借戶之「具控制權人」納入應查詢比對黑名單之對象。
	 * 
	 * @param mainId
	 * @throws CapException
	 */
	@Override
	public String chkCtrlPeoIsOkForDrawDown(String mainId,
			LinkedHashMap<String, String> allBorrowerIdMap) throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS20APanel.class);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		StringBuffer errBorrowerAMLEmptyField = new StringBuffer("");

		StringBuffer errMsg = new StringBuffer("");
		StringBuffer errNoConfirm = new StringBuffer("");
		StringBuffer errNoL120s01p = new StringBuffer("");
		StringBuffer errNo0024 = new StringBuffer("");
		String dataFrom = "0024";
		Map<String, Object> chkInsteadMap = this.checkInstead(mainId);
		boolean instead = MapUtils.getBooleanValue(chkInsteadMap, "instead",
				false);
		String queryBrId = MapUtils.getString(chkInsteadMap, "queryBrId");
		String unitNo = (instead ? queryBrId : user.getUnitNo());

		boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchSrv
				.getBranch(unitNo).getBrNoFlag());

		if (isOverSea) {
			return errMsg.toString();
		}

		// 後台管理->系統設定維護->>COM_AML_CTRLPEO_CHK_OFF
		// 取消檢核無具控制權人不得送呈主管，OV代表所有海外分行不檢核
		if (!this.needChkCtrlPeoBeforeSendBoss(unitNo)) {
			return errMsg.toString();
		}

		Date queryDateS = null;
		L120S09B l120s09b = this.findL120s09bByMainId(mainId);
		if (l120s09b == null) {
			// new Function
			List<L120S09A> l120s09as = this
					.findL120s09aByMainIdWithOrder1(mainId);
			if (l120s09as != null && !l120s09as.isEmpty()) {
				for (L120S09A l120s09a : l120s09as) {
					queryDateS = l120s09a.getQueryDateS();
					break;
				}
			}
		} else {
			// old Function
			queryDateS = l120s09b.getQueryDateS();
		}

		if (queryDateS == null) {
			queryDateS = CapDate.getCurrentTimestamp();
		}

		// 為了避免上線後，在途文件已經掃描過了卻因為沒有具控制權人還要在掃描一次造成民怨，僅針對AML/CFT
		// 查詢日期在具控制權人生效日之後(2019/05/01)才檢核具控制權人是否空白與0024是否一致
		if (!this.isCtrlPeoEffective(queryDateS)) {
			// 後台管理->系統設定維護->COM_J1070070_AML_SMGR_START_DT 2018-06-01
			// AML/CFT查詢日期再生效日前，不檢核有無具控制權人
			return errMsg.toString();
		}

		// 借款人-行業對象別
		LinkedHashMap<String, String> idAndBusCodeMap = new LinkedHashMap<String, String>();

		// 檢核借款人基本資料具控制權人欄位不得空白
		// 取得0024判斷行業對象別
		if (allBorrowerIdMap != null && !allBorrowerIdMap.isEmpty()) {
			for (String chkKey : allBorrowerIdMap.keySet()) {
				String[] chkKeyArr = chkKey.split("-");
				String chkId = chkKeyArr[0];
				String chkDupNo = chkKeyArr[1];
				String chkName = Util.trim(allBorrowerIdMap.get(chkKey));

				// 取得0024判斷行業對象別
				Map<String, Object> m0024 = iCustomerService.findByIdDupNo(
						chkId, chkDupNo);
				String busCode = (m0024 != null && !m0024.isEmpty()) ? Util
						.trim(MapUtils.getString(m0024, "BUSCD")) : "";

				// 簽報書檢核時，行業對象別從簽報書借款基本資料來
				// 動審表檢核時，行業對象別從0024來(因為借款人、共借人可能與簽報書不同)
				if (!idAndBusCodeMap.containsKey(chkId + chkDupNo)) {
					idAndBusCodeMap.put(chkId + chkDupNo, busCode);
				}

				// 企業戶才要檢核具控制權人
				if (Util.equals(busCode, "")) {
					errNo0024.append(Util.equals(errNo0024.toString(), "") ? ""
							: "、");
					errNo0024.append(chkId + chkDupNo + " " + chkName);
				} else {
					if (Util.notEquals(busCode, "")
							&& !LMSUtil.isBusCode_060000_130300(busCode)) {
						// 企業戶
						L164S01A l164s01a = this.findL164s01aByUniqueKey(
								mainId, chkId, chkDupNo);
						if (l164s01a != null) {
							if (Util.equals(l164s01a.getCtrlPeo(), "")) {

								errBorrowerAMLEmptyField
										.append(Util.equals(
												errBorrowerAMLEmptyField
														.toString(), "") ? ""
												: "、");
								errBorrowerAMLEmptyField.append(chkId
										+ chkDupNo + " " + chkName);
							}
						} else {
							errBorrowerAMLEmptyField
									.append(Util.equals(
											errBorrowerAMLEmptyField.toString(),
											"") ? "" : "、");
							errBorrowerAMLEmptyField.append(chkId + chkDupNo
									+ " " + chkName);
						}
					}
				}

			}
		}

		if (Util.notEquals(errNo0024.toString(), "")) {
			if (this.needChk0024Exist(queryBrId)) {
				// AML.error010=主從債務人「{0}」於0024無行業對象別資料。
				errMsg.append(Util.equals(errMsg, "") ? "" : "<BR>");
				errMsg.append(MessageFormat.format(pop
						.getProperty("AML.error010"), errNo0024.toString()
						.toString()));
			}

		}

		if (Util.notEquals(errBorrowerAMLEmptyField.toString(), "")) {
			// AML.error008=借款人/共同借款人{0}於相關報表->主從債務人資料表中尚有洗錢防制所需欄位未完成輸入。
			errMsg.append(Util.equals(errMsg, "") ? "" : "<BR>");
			errMsg.append(MessageFormat.format(pop.getProperty("AML.error008"),
					errBorrowerAMLEmptyField.toString()));
		}

		// 檢核具控制權人
		for (String fullCustId : allBorrowerIdMap.keySet()) {
			String[] fullKey = fullCustId.split("-");
			String custId = fullKey[0];
			String dupNo = fullKey[1];
			String custName = allBorrowerIdMap.get(fullCustId);
			String busCode = "";
			if (idAndBusCodeMap.containsKey(custId + dupNo)) {
				busCode = MapUtils.getString(idAndBusCodeMap, custId + dupNo,
						"");
				if (LMSUtil.isBusCode_060000_130300(busCode)
						|| Util.equals(busCode, "")) {
					// 沒有行業對象別就先不檢核
					// 先出錯誤訊息errNo0024--AML.error010=主從債務人「{0}」於0024無行業對象別資料。
					continue;
				}
			} else {
				continue;
			}

			// J-108-0039_05097_B1001 TODO XXXXXXXXXXXXXXXXX
			if (!isOverSea) {
				// 國內
				dataFrom = "0024";
				List<Map<String, Object>> cmfDataList = misDbService
						.findCMFLUNB1ByCustId(custId, dupNo);

				if (cmfDataList == null || cmfDataList.isEmpty()) {
					errNoConfirm.append(Util.equals(errNoConfirm, "") ? ""
							: "、");
					errNoConfirm.append(custId + dupNo + " " + custName);
				} else {

					int count = 0;
					for (Map<String, Object> cmfData : cmfDataList) {
						String BUSCD = Util.trim(MapUtils.getString(cmfData,
								"BUSCD"));
						String CMSR_SR_PV_ID = Util.trim(MapUtils.getString(
								cmfData, "CMB1_CNTRL_PV_ID"));
						String CMSR_SR_PV_CNAME = Util.trim(MapUtils.getString(
								cmfData, "CMB1_CNTRL_PV_CNAME"));
						String CMSR_SR_PV_ENAME = Util.trim(MapUtils.getString(
								cmfData, "CMB1_CNTRL_PV_ENAME"));
						String CMSR_SR_PV_BD = Util.trim(MapUtils.getString(
								cmfData, "CMB1_CNTRL_PV_BD"));
						String CMSR_SR_PV_NC = Util.trim(MapUtils.getString(
								cmfData, "CMB1_CNTRL_PV_NC"));

						if (Util.notEquals(CMSR_SR_PV_ID, "")
								|| Util.notEquals(CMSR_SR_PV_CNAME, "")
								|| Util.notEquals(CMSR_SR_PV_ENAME, "")) {
							// 知道至少有一筆就好
							count = count + 1;
							break;
						}
					}
					// 需確認 時，檢核有沒有L120S01P
					if (count > 0) {
						List<L120S01P> listL120s01p = this
								.findL120s01pByMainIdAndCustIdWithRType(
										mainId,
										custId,
										dupNo,
										UtilConstants.Casedoc.L120s09aBlackListCtlTarget.具控制權人);
						if (listL120s01p == null || listL120s01p.isEmpty()) {
							errNoL120s01p.append(Util.equals(
									errNoL120s01p.toString(), "") ? "" : "、");
							errNoL120s01p.append(custId + dupNo);
						}
					} else {
						// 一筆都沒有
						if (this.needChk0024MustHaveCtrlPeoBeforeSendBoss(unitNo)) {
							errNoConfirm
									.append(Util.equals(errNoConfirm, "") ? ""
											: "、");
							errNoConfirm
									.append(custId + dupNo + " " + custName);
						}

					}

				}
			} else {
				// 海外--不檢核
				dataFrom = "AS400";

			}
		}

		if (Util.notEquals(errNoConfirm.toString(), "")) {

			// AML.error028=「{0}」借款人0024-23無「具控制權人」資料
			errMsg.append(Util.equals(errMsg, "") ? "" : "<BR>");
			errMsg.append(MessageFormat.format(pop.getProperty("AML.error028"),
					errNoConfirm.toString(), dataFrom));

		}

		if (Util.notEquals(errNoL120s01p.toString(), "")) {
			// AML.error029=「{0}」借款人0024-23有「具控制權人」資料，e-Loan借款人基本資料「具控制權人」不得為「無」
			errMsg.append(Util.equals(errMsg, "") ? "" : "<BR>");
			errMsg.append(MessageFormat.format(pop.getProperty("AML.error029"),
					errNoConfirm.toString(), dataFrom));

		}

		return errMsg.toString();

	}

	/**
	 * J-108-0039_05097_B1001 Web e-Loan
	 * 國內企金授信系統簽報、動審AML頁籤將借戶之「具控制權人」納入應查詢比對黑名單之對象。 判斷登入分行是否需檢核無具控制權人不得送呈主管
	 * 
	 * @return
	 */
	@Override
	public boolean needChkCtrlPeoBeforeSendBoss(String QueryBrId) {
		boolean isNeed = true;

		// 後台管理->系統設定維護->COM_AML_CTRLPEO_CHK_OFF 取消檢核無實質受益人不得送呈主管

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String countryType = Util.trim(branchSrv.getBranch(QueryBrId)
				.getCountryType());

		String notChkCountry = Util.trim(lmsService
				.getSysParamDataValue("COM_AML_CTRLPEO_CHK_OFF"));

		if (Util.notEquals(notChkCountry, "")
				&& Util.notEquals(countryType, "")) {
			for (String xx : notChkCountry.split(",")) {
				if (Util.equals(xx, countryType)) {
					isNeed = false;
					break;
				}

				if (Util.notEquals(countryType, "TW") && Util.equals(xx, "OV")) {
					// OV代表所有海外分行都不擋
					isNeed = false;
					break;
				}
			}
		}

		return isNeed;
	}

	/**
	 * J-108-0039_05097_B1001 Web e-Loan
	 * 國內企金授信系統簽報、動審AML頁籤將借戶之「具控制權人」納入應查詢比對黑名單之對象。 判斷登入分行是否需檢核無具控制權人不得送呈主管
	 * 
	 * 
	 * queryDateS : L120S09B 的查詢日期
	 * 
	 * @return
	 */
	@Override
	public boolean isCtrlPeoEffective(Date queryDateS) {
		boolean isStart = false;

		// 後台管理->系統設定維護->COM_AML_CTRLPEO_START_DT 2018-06-01
		// AML/CFT查詢日期再生效日前，不檢核有無具控制權人
		String startDt = Util.trim(lmsService
				.getSysParamDataValue("COM_AML_CTRLPEO_START_DT"));

		if (Util.notEquals(startDt, "")) {

			if (queryDateS == null) {
				queryDateS = CapDate.getCurrentTimestamp();
			}

			if (LMSUtil.cmpDate(queryDateS, ">=", CapDate.parseDate(startDt))) {
				isStart = true;
			}
		}

		return isStart;
	}

	/**
	 * J-108-0039_05097_B1001 Web e-Loan
	 * 國內企金授信系統簽報、動審AML頁籤將借戶之「具控制權人」納入應查詢比對黑名單之對象。
	 * 判斷送呈時不用檢核是否0024至少要有一筆具控制權人，OV代表所有海外分行不檢核
	 * 
	 * @return
	 */
	@Override
	public boolean needChk0024MustHaveCtrlPeoBeforeSendBoss(String QueryBrId) {
		boolean isNeed = true;

		// 後台管理->系統設定維護->COM_J1070070_COM_AML_CTRLPEO_NOT_NEED Y
		// 送呈時不用檢核是否0024至少要有一筆具控制權人，OV代表所有海外分行不檢核

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String countryType = Util.trim(branchSrv.getBranch(QueryBrId)
				.getCountryType());

		String notChkCountry = Util.trim(lmsService
				.getSysParamDataValue("COM_AML_CTRLPEO_NOT_NEED"));

		if (Util.notEquals(notChkCountry, "")
				&& Util.notEquals(countryType, "")) {
			for (String xx : notChkCountry.split(",")) {
				if (Util.equals(xx, countryType)) {
					isNeed = false;
					break;
				}

				if (Util.notEquals(countryType, "TW") && Util.equals(xx, "OV")) {
					// OV代表所有海外分行都不擋
					isNeed = false;
					break;
				}
			}
		}

		return isNeed;
	}

	/**
	 * J-108-0145_05097_B1001 Web e-Loan 國內外企金授信私募基金案件調整實質受益人控管
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	@Override
	public String canPassAmlRelativeAndRiskLvlChk(GenericBean mainEntity,
			String custId, String dupNo) {
		String canPass = "N";
		L120S01B l120s01b = null;
		if (mainEntity instanceof L140M01A) {
			// 額度明細表
			L140M01A l140m01a = ((L140M01A) mainEntity);
			L120M01C l120m01c = l140m01a.getL120m01c();
			l120s01b = l120s01bDao.findByUniqueKey(l120m01c.getMainId(),
					custId, dupNo);

		} else if (mainEntity instanceof L120M01A) {
			// 動審表 -聯貸案參貸比率一覽表主檔
			L120M01A l120m01a = ((L120M01A) mainEntity);
			l120s01b = l120s01bDao.findByUniqueKey(l120m01a.getMainId(),
					custId, dupNo);
		} else if (mainEntity instanceof L120S09A) {
			// 動審表 -聯貸案參貸比率一覽表主檔
			L120S09A l120s09a = ((L120S09A) mainEntity);
			l120s01b = l120s01bDao.findByUniqueKey(l120s09a.getMainId(),
					custId, dupNo);
		} else {
			return canPass;
		}

		if (l120s01b == null) {
			return canPass;
		}

		// 'Y','本案已確實完成實質受益人等之辨識'
		// 'N','本案為實質受益人等延後辦理辨識案件'
		if (Util.equals(Util.trim(l120s01b.getBeneficiaryChk()), "N")) {
			canPass = "Y";
		}

		return canPass;
	}

	/**
	 * #J-108-0145_05097_B1001 Web e-Loan 國內外企金授信私募基金案件調整實質受益人控管
	 * 
	 * @param type
	 *            1:簽報書 2:動審表
	 * @param mainId
	 * @return
	 * @throws CapException
	 */
	@Override
	public String getPassAmlRelativeAndRiskLvlChkWarnMsg(String type,
			String mainId) throws CapException {

		String warnMsg = "";

		List<L140M01A> listL140m01a = this.findL140m01aListByL120m01cMainId(
				mainId, UtilConstants.Cntrdoc.ItemType.額度明細表);

		// 檢查實質受益人欄未有無輸入 + 有沒有完成身分確認
		// 主要借款人+共同借款人
		LinkedHashMap<String, String> allBorrowerIdMap = new LinkedHashMap<String, String>();
		boolean shoWarnMsg = false;
		for (L140M01A l140m01a : listL140m01a) {
			boolean isProPerty8 = LMSUtil.isContainValue(
					Util.trim(l140m01a.getProPerty()),
					UtilConstants.Cntrdoc.Property.取消);

			// 除不變外取消，其他借款人要檢查0024-23有沒有完成實質受益人身分確認
			if (!isProPerty8) {
				String custId = Util.trim(l140m01a.getCustId());
				String dupNo = Util.trim(l140m01a.getDupNo());
				String custName = Util.trim(l140m01a.getCustName());
				String fullCustId = custId + "-" + dupNo;

				// 已經檢查過的就不要在檢查了
				if (!allBorrowerIdMap.containsKey(fullCustId)) {
					// J-108-0145_05097_B1001 Web e-Loan
					// 國內外企金授信私募基金案件調整實質受益人控管
					if (Util.equals(this.canPassAmlRelativeAndRiskLvlChk(
							l140m01a, custId, dupNo), "Y")) {
						shoWarnMsg = true;
						break;
					} else {
						if (!allBorrowerIdMap.containsKey(fullCustId)) {
							allBorrowerIdMap.put(fullCustId, custName);
						}
					}
				}

				// 額度明細表共同借款人
				Set<L140M01J> l140m01js = l140m01a.getL140m01j();
				for (L140M01J l140m01j : l140m01js) {
					String cCustId = Util.trim(l140m01j.getCustId());
					String cDupNo = Util.trim(l140m01j.getDupNo());
					String cCustName = Util.trim(l140m01j.getCustName());
					String cFullCustId = cCustId + "-" + cDupNo;

					// 已經檢查過的就不要在檢查了
					if (!allBorrowerIdMap.containsKey(cFullCustId)) {
						if (Util.equals(this.canPassAmlRelativeAndRiskLvlChk(
								l140m01a, cCustId, cDupNo), "Y")) {
							shoWarnMsg = true;
							break;
						} else {
							if (!allBorrowerIdMap.containsKey(cFullCustId)) {
								allBorrowerIdMap.put(cFullCustId, cCustName);
							}
						}
					}

				}
			}
		}

		if (shoWarnMsg) {

			Properties pop = MessageBundleScriptCreator
					.getComponentResource(LMSS20APanel.class);

			if (Util.equals(type, "1")) {
				// 簽報書
				// L120S09a.message27=本案為實質受益人等延後辦理辨識案件，請衡酌加註相關控管條件(如：「首次動撥前務必確實完成實質受益人等辨識相關作業」)
				warnMsg = pop.getProperty("L120S09a.message27");
			} else {
				// 動審表
				// L120S09a.message28=本案為實質受益人等延後辦理辨識案件，務必確認已確實完成辨識等相關作業
				warnMsg = pop.getProperty("L120S09a.message28");
			}
		}

		return warnMsg;
	}

	/**
	 * I-107-0260_05097_B1001 Web e-Loan企金授信系統增加提示訊息【該客戶屬於巴拿馬文件名單】
	 * 
	 * @param custIdMapAml
	 * @return
	 * @throws CapException
	 */
	@Override
	public String chkCustListInPana(LinkedHashMap<String, String> custIdMapAml)
			throws CapException {

		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS20APanel.class);

		StringBuffer panaCust = new StringBuffer("");

		for (String fullCustId : custIdMapAml.keySet()) {
			String[] fullKey = fullCustId.split("-");
			String tCustId = fullKey[0];
			String tDupNo = fullKey[1];
			String tCustName = custIdMapAml.get(fullCustId);

			Map<String, Object> sp_result = misStoredProcService.callCMPEPUPD(
					tCustId, tDupNo);

			Map<String, String> errorMap = new HashMap<String, String>();
			StringBuilder errMSG = new StringBuilder();

			logger.debug("out Params:{}",
					Arrays.toString(sp_result.entrySet().toArray()));
			if (sp_result.containsKey("SP_RETURN")) {
				String spReturn = (String) sp_result.get("SP_RETURN");
				if ("YES".equals(spReturn)) {
					String outResult = Util.trim(sp_result
							.get("SP_OUTPUT_AREA"));
					List<String> list = Arrays.asList(outResult.substring(1,
							outResult.length() - 1).split("\\},\\{"));

					String CMPMUPEP_RETURN_CODE = Util.trim(list.get(0));
					// 錯誤訊息
					String CMPMUPEP_ERROR_MSG = Util.trim(list.get(1));
					// PEPS 註記
					String CMPMUPEP_PEPS_FLAG = Util.trim(list.get(2));
					// 重大負面新聞註記
					String CMPMUPEP_ADVRS_FLAG = Util.trim(list.get(3));
					// 024 PANAMA 名單註記
					String CMPMUPEP_PANA_FLAG = Util.trim(list.get(4));
					// 0024 第三方付款名單註記
					String CMPMUPEP_THIRD_FLAG = Util.trim(list.get(5));

					// CMPMUPEP_RETURN_CODE:
					// '00':NORMAL
					// '01':FUNCTION-ERROR
					// '02':PARM-ERROR
					// '99':ERROR
					if (Util.equals(CMPMUPEP_RETURN_CODE, "00")) {
						if (Util.equals(CMPMUPEP_PANA_FLAG, "Y")) {

							panaCust.append(
									Util.equals(Util.trim(panaCust.toString()),
											"") ? "" : ",").append(
									tCustId + tDupNo + " " + tCustName);

						}
					} else {
						// L120S09a.lnsp0130_1=錯誤代碼
						errMSG.append(
								Util.equals(Util.trim(errMSG.toString()), "") ? ""
										: ",")
								.append(pop.getProperty("L120S09a.lnsp0130_1"))
								.append("「" + CMPMUPEP_RETURN_CODE + "」:"
										+ CMPMUPEP_ERROR_MSG);
					}

				} else {
					// SP_RETURN 非 YES
					errMSG.append(
							Util.equals(Util.trim(errMSG.toString()), "") ? ""
									: ",").append(
							Util.trim(sp_result.get("SP_ERROR_MSG")));

				}
			} else {

				// L120S09a.lnsp0130_2=LNSP0130
				// CMPEPUPD未回傳SP_RETURN結果
				String spErrorMsg = pop.getProperty("L120S09a.lnsp0130_2");

				errMSG.append(
						Util.equals(Util.trim(errMSG.toString()), "") ? ""
								: ",").append(spErrorMsg);

			}

			// 發生錯誤就停止執行
			if (Util.notEquals(errMSG.toString(), "")) {
				errorMap.put("ERROR_MSG", errMSG.toString());
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0072"// 查詢中心黑名單失敗
						, errorMap), getClass());
			}

		}

		return panaCust.toString();
	}

	/**
	 * I-107-0260_05097_B1001 Web e-Loan企金授信系統增加提示訊息【該客戶屬於巴拿馬文件名單】
	 * 
	 * @return
	 */
	@Override
	public boolean needShowPanaMsg(String QueryBrId) {
		boolean isNeed = true;

		// 後台管理->系統設定維護->COM_I1070260_AML_SHOW_PANA_OFF 取消顯示該客戶屬於巴拿馬文件名單

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String countryType = Util.trim(branchSrv.getBranch(QueryBrId)
				.getCountryType());

		String notChkCountry = Util.trim(lmsService
				.getSysParamDataValue("COM_I1070260_AML_SHOW_PANA_OFF"));

		if (Util.notEquals(notChkCountry, "")
				&& Util.notEquals(countryType, "")) {
			for (String xx : notChkCountry.split(",")) {
				if (Util.equals(xx, countryType)) {
					isNeed = false;
					break;
				}

				if (Util.notEquals(countryType, "TW") && Util.equals(xx, "OV")) {
					// OV代表所有海外分行都不擋
					isNeed = false;
					break;
				}
			}
		}

		return isNeed;
	}

	/**
	 * I-110-0329_11557_B1001 針對既有自然人客戶增加KYC系統異常機制，KYC異常時可BYPASS
	 * 撈出會用到的01,02,03,07業務 特別處理RETURN-CODE=03 客戶無於 0024-23 建檔時回傳null
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 * @throws CapException
	 */
	public Map<String, Object> getAllCMPMLUNQListByCustId(String custId,
			String dupNo) throws CapException {
		Map<String, Object> allMap = new HashMap<String, Object>();
		Map<String, Object> map01 = this.getSCMLUINQListByCustId(custId, dupNo,
				"1");
		if (map01 != null
				&& Util.notEquals(MapUtils.getString(map01, "LUV_DEPT_1_CODE"),
						"03")) {
			allMap.putAll(map01);
		} else {
			return null;// 代表無資料
		}
		Map<String, Object> map02 = this.getSCMLUINQListByCustId(custId, dupNo,
				"2");
		if (map02 != null
				&& Util.notEquals(MapUtils.getString(map02, "LUV_DEPT_2_CODE"),
						"03")) {
			allMap.putAll(map02);
		} else {
			return null;// 代表無資料
		}
		Map<String, Object> map03 = this.getSCMLUINQListByCustId(custId, dupNo,
				"3");
		if (map03 != null
				&& Util.notEquals(MapUtils.getString(map03, "LUV_DEPT_3_CODE"),
						"03")) {
			allMap.putAll(map03);
		} else {
			return null;// 代表無資料
		}
		Map<String, Object> map07 = this.getSCMLUINQListByCustId(custId, dupNo,
				"7");
		if (map07 != null
				&& Util.notEquals(MapUtils.getString(map07, "LUV_DEPT_7_CODE"),
						"03")) {
			allMap.putAll(map07);
		} else {
			return null;// 代表無資料
		}

		// 回傳查完01,02,03,07業務的map
		return allMap;
	}

	/**
	 * I-110-0329_11557_B1001 針對既有自然人客戶增加KYC系統異常機制，KYC異常時可BYPASS
	 * 
	 * @param custId
	 * @param dupNo
	 * @param dept
	 *            查詢客戶之業務往來項目 若為ALL則撈出會用到的01,02,03,07 1 存匯業務 2 授信業務 3 進出口業務 4
	 *            票債券業務 5 信託業務 6 財富管理業務 7 衍生性金融商品業務 8 電子金融業務 9 信用卡 ( 已停用 ) 10 其他
	 * @return
	 * @throws CapException
	 */
	@Override
	public Map<String, Object> getSCMLUINQListByCustId(String custId,
			String dupNo, String dept) throws CapException {

		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);

		Locale locale = LMSUtil.getLocale();
		Map<String, String> codeSolutionMap = null;// 處理辦法，四組提供不同狀況下的解法，顯示在畫面上告知使用者
		codeSolutionMap = codetypeservice.findByCodeType(
				"SCMLUINQ_RETURN_CODE_SOLUTION", locale.toString());

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		logger.info("CALL LN.LNSP0130 SCMLUINQ custId={" + custId + "},dupNo={"
				+ dupNo + "},dept={" + dept + "}");
		Map<String, Object> sp_result = misStoredProcService.callSCMLUINQ(
				custId, dupNo, dept, user.getUnitNo(), user.getUserId());

		StringBuilder errMSG = new StringBuilder();// 查詢SP直接執行失敗的訊息

		Map<String, Object> deptMap = new HashMap<String, Object>();// 存各項業務是否有業務往來

		logger.info("CALL LN.LNSP0130 SCMLUINQ out Params:{}",
				Arrays.toString(sp_result.entrySet().toArray()));
		if (sp_result.containsKey("SP_RETURN")) {
			String spReturn = (String) sp_result.get("SP_RETURN");
			if ("YES".equals(spReturn)) {
				String outResult = Util.trim(sp_result.get("SP_OUTPUT_AREA"));
				List<String> list = Arrays.asList(outResult.substring(1,
						outResult.length() - 1).split("\\},\\{"));

				// 回應碼
				String CMPMLUNQ_RETURN_CODE = Util.trim(list.get(0));
				// 客戶風險等級
				String CMPMLUNQ_RISK_LEVEL = Util.trim(list.get(1));
				// 錯誤訊息
				String CMPMLUNQ_ERROR_MSG = Util.trim(list.get(2));

				// CMPMUPEP_RETURN_CODE:
				// '00':NORMAL
				// '01':FUNCTION-ERROR
				// '02':PARM-ERROR
				// '03':客戶無於 0024-23 建檔
				// '04':查詢之業務往來項目無勾選
				// '05':法人戶無辨識實際受益人
				// '06':審查期限逾期或未完成審查客戶
				// '07':FUNC = 03 為全新客戶
				// '99':ERROR
				if (Util.equals(CMPMLUNQ_RETURN_CODE, "00")) {
					// 00 成功 則註記為V
					deptMap.put("LUV_DEPT_" + dept, "V");// 仿原來查DB的註記
				} else {
					// 非00 則為失敗，註記為空
					deptMap.put("LUV_DEPT_" + dept, "");// 仿原來查DB的註記
					deptMap.put("LUV_DEPT_" + dept + "_CODE",
							CMPMLUNQ_RETURN_CODE);

					// 錯誤訊息統一在底層這支組裝完
					// clsL120M01A.error080=查詢統編{0}，回應碼:{1}-{2}
					String custReturnMsg = MessageFormat.format(
							pop.getProperty("clsL120M01A.error080"), custId,
							Util.trim(CMPMLUNQ_RETURN_CODE),
							Util.trim(CMPMLUNQ_ERROR_MSG));

					if (codeSolutionMap != null
							&& codeSolutionMap.get(CMPMLUNQ_RETURN_CODE) != null) {
						// 加上四組告知的解決辦法
						custReturnMsg = custReturnMsg
								+ "<BR>"
								+ Util.trim(codeSolutionMap
										.get(CMPMLUNQ_RETURN_CODE));
					}
					deptMap.put("LUV_DEPT_" + dept + "_MSG", custReturnMsg);
				}

			} else {
				// SP_RETURN 非 YES，但有執行成功
				errMSG.append(
						Util.equals(Util.trim(errMSG.toString()), "") ? ""
								: ",").append(
						Util.trim(sp_result.get("SP_ERROR_MSG")));

			}
		} else {
			// CMPEPUPD未回傳SP_RETURN結果
			// clsL120M01A.error079=LNSP0130 SCMLUINQ未回傳SP_RETURN結果
			String spErrorMsg = pop.getProperty("clsL120M01A.error079");

			errMSG.append(
					Util.equals(Util.trim(errMSG.toString()), "") ? "" : ",")
					.append(spErrorMsg);
		}

		// 發生錯誤就停止執行
		if (Util.notEquals(errMSG.toString(), "")) {
			// clsL120M01A.error078=查詢業務往來失敗
			throw new CapMessageException(
					pop.getProperty("clsL120M01A.error078") + ":"
							+ errMSG.toString(), getClass());
		}

		return deptMap;
	}

	/**
	 * P-108-0046_05097_B1001 Web e-Loan配合Oracle系統建置，修改AML相關功能。
	 * 
	 * @return
	 */
	@Override
	public boolean isOracle(String queryBrId) {

		IBranch branch = branchService.getBranch(queryBrId);
		String tCallSas = Util.trim(branch.getCallSas());
		String countryType = Util.trim(branch.getCountryType());
		// J-110-00A2_05097_B1001 Web e-Loan配合紐行Oracle系統建置，修改AML相關功能。
		// C.(新)紐約ORACLE透過MQ
		if ("O".equalsIgnoreCase(tCallSas)
				|| ("S".equalsIgnoreCase(tCallSas) && !"US"
						.equalsIgnoreCase(countryType))
				|| "C".equalsIgnoreCase(tCallSas)) {
			return true;
		} else {
			return false;
		}

	}

	/**
	 * P-108-0046_05097_B1001 Web e-Loan配合Oracle系統建置，修改AML相關功能。
	 * 
	 * @return
	 */
	@Override
	public boolean isOracle(L120M01A l120m01a) {

		String unitNo = l120m01a.getCaseBrId();

		Map<String, Object> chkInsteadMap = this.checkInstead(l120m01a
				.getMainId());
		boolean instead = MapUtils.getBooleanValue(chkInsteadMap, "instead",
				false);
		String queryBrId = MapUtils.getString(chkInsteadMap, "queryBrId");
		unitNo = (instead ? queryBrId : unitNo);

		IBranch branch = branchService.getBranch(unitNo);
		String tCallSas = Util.trim(branch.getCallSas());
		String countryType = Util.trim(branch.getCountryType());
		// J-110-00A2_05097_B1001 Web e-Loan配合紐行Oracle系統建置，修改AML相關功能。
		if ("O".equalsIgnoreCase(tCallSas)
				|| ("S".equalsIgnoreCase(tCallSas) && !"US"
						.equalsIgnoreCase(countryType))
				|| "C".equalsIgnoreCase(tCallSas)) {
			return true;
		} else {
			return false;
		}

	}

	/**
	 * 海外分行檢查需不需實質受益人 J-109-0067_05097_B1001 Web e-Loan
	 * 授信對於無需辨識實質受益人之法人主體，簽報書及額度明細表需出現無實質受益人
	 * 
	 * @param c120m01a
	 * @param needCheck
	 * @return
	 */
	@Override
	public boolean foreignBranchCheckExcl(String unitNo, String custId,
			String dupNo) {
		boolean needCheck = true;
		// 企業戶
		Map<String, Object> csOvs = dwdbService
				.findDW_OTS_CSOVS_By_CustId_And_BrNo(unitNo, custId, dupNo);
		if (csOvs != null) {

			String F1330_EXCL_DOC_TYPE = Util.trim(MapUtils.getString(csOvs,
					"F1330_EXCL_DOC_TYPE", ""));

			if (Util.equals(F1330_EXCL_DOC_TYPE, "")) {
				needCheck = true;
			} else {

				needCheck = false;

				// J-109-0067 新加坡分行及泰國分行為N時,必需有實質受益人,其它國家不為空值時即不需實質受益人
				String need_excl_n = Util.trim(lmsService
						.getSysParamDataValue("COM_J1090067_AML_NEED_EXCL_N"));

				IBranch branch = branchSrv.getBranch(MegaSSOSecurityContext
						.getUnitNo());
				String countryType = branch.getCountryType();
				if (Util.notEquals(need_excl_n, "")
						&& Util.notEquals(countryType, "")) {
					for (String xx : need_excl_n.split(",")) {
						if (Util.equals(xx, countryType)
								&& "N".equalsIgnoreCase(CapString
										.trimNull(csOvs
												.get("F1330_EXCL_DOC_TYPE")))) {
							needCheck = true;
							break;
						}
					}
				}
			}

		}
		return needCheck;
	}

	/**
	 * 海外分行檢查需不需實質受益人 J-109-0067_05097_B1001 Web e-Loan
	 * 授信對於無需辨識實質受益人之法人主體，簽報書及額度明細表需出現無實質受益人
	 * 
	 * @param c120m01a
	 * @param needCheck
	 * @return
	 */
	@Override
	public boolean needChk0024Exist(String QueryBrId) {
		boolean isNeed = true;

		// 後台管理->系統設定維護->COM_NOT_CHK_0024_EXIST XX 取消檢核0024有無資料

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String countryType = Util.trim(branchSrv.getBranch(QueryBrId)
				.getCountryType());

		String notChkCountry = Util.trim(lmsService
				.getSysParamDataValue("COM_AML_NOT_CHK_0024_EXIST"));

		if (Util.notEquals(notChkCountry, "")
				&& Util.notEquals(countryType, "")) {
			for (String xx : notChkCountry.split(",")) {
				if (Util.equals(xx, countryType)) {
					isNeed = false;
					break;
				}

				if (Util.notEquals(countryType, "TW") && Util.equals(xx, "OV")) {
					// OV代表所有海外分行都不擋
					isNeed = false;
					break;
				}
			}
		}

		return isNeed;
	}

	/**
	 * 海外分行檢查需不需實質受益人 J-109-0067_05097_B1001 Web e-Loan
	 * 授信對於無需辨識實質受益人之法人主體，簽報書及額度明細表需出現無實質受益人
	 * 
	 * @param c120m01a
	 * @param needCheck
	 * @return
	 */
	@Override
	public boolean needShowNotApplicableWhenNoEffective(String QueryBrId) {
		boolean isNeed = false;

		// 後台管理->系統設定維護->COM_AML_NO_EFFECTIVE_SHOW_NA SG J-109-0067
		// 無實際受益人時改為顯示不適用(預設為顯示無)

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String countryType = Util.trim(branchSrv.getBranch(QueryBrId)
				.getCountryType());

		String needShowCountry = Util.trim(lmsService
				.getSysParamDataValue("COM_AML_NO_EFFECTIVE_SHOW_NA"));

		if (Util.notEquals(needShowCountry, "")
				&& Util.notEquals(countryType, "")) {
			for (String xx : needShowCountry.split(",")) {
				if (Util.equals(xx, countryType)) {
					isNeed = true;
					break;
				}

				if (Util.notEquals(countryType, "TW") && Util.equals(xx, "OV")) {
					// OV代表所有海外分行都要顯示不適用
					isNeed = true;
					break;
				}

			}
		}

		return isNeed;
	}

	/**
	 * P-108-0046_05097_B1003 Web e-Loan授信系統傳送婉卻紀錄給ORACLE CIF
	 * 
	 * 核准時呼叫，故不能用user.getUnitNo();，因為可能是918覆核，所以要改用caseBrId();
	 * 
	 * @param l120m01a
	 * @return
	 */
	@Override
	public boolean needSendReject(L120M01A l120m01a) {
		boolean isNeed = false;

		// 後台管理->系統設定維護->COM_AML_SEND_REJECT TW P-108-0046_05097_B1003 Web
		// e-Loan授信系統傳送婉卻紀錄給ORACLE CIF

		String unitNo = l120m01a.getCaseBrId();
		String queryBrId = l120m01a.getCaseBrId();
		boolean instead = false;

		L120S09B l120s09b = this.findL120s09bByMainId(l120m01a.getMainId());
		if (l120s09b != null
				&& Util.isNotEmpty(Util.nullToSpace(l120s09b.getQueryBrId()))) {
			instead = true;
			queryBrId = l120s09b.getQueryBrId();
		}

		unitNo = (instead ? queryBrId : unitNo);

		String countryType = Util.trim(branchSrv.getBranch(unitNo)
				.getCountryType());

		String needShowCountry = Util.trim(lmsService
				.getSysParamDataValue("COM_AML_SEND_REJECT"));

		boolean isOracle = this.isOracle(l120m01a);

		if (Util.notEquals(needShowCountry, "")
				&& Util.notEquals(countryType, "")) {
			for (String xx : needShowCountry.split(",")) {
				if (Util.equals(xx, countryType)) {
					isNeed = true;
					break;
				}

				if (Util.notEquals(countryType, "TW") && Util.equals(xx, "OV")) {
					// OV代表所有海外分行都要傳送
					isNeed = true;
					break;
				}

			}
		}

		if (isNeed) {
			// 屬於要傳送之國家，但不是使用ORACLE系統，要改成不傳送
			// 此段暫時不使用
			if (!isOracle) {
				// isNeed=false;
			}
		}

		return isNeed;
	}

	// J-107-0226_11557_B1001 疑似洗錢或資恐交易態樣檢核表-授信
	@Override
	public L120S09C findL120s09cByOid(String oid) {
		return l120s09cdao.findByOid(oid);
	}

	// J-107-0226_11557_B1001 疑似洗錢或資恐交易態樣檢核表-授信
	@Override
	public List<L120S09C> findL120s09cByMainId(String mainId) {
		return l120s09cdao.findByMainId(mainId);
	}

	// J-107-0226_11557_B1001 疑似洗錢或資恐交易態樣檢核表-授信
	@Override
	public void deleteL120s09cByOid(String[] oids) {
		if (oids != null) {
			for (String oid : oids) {
				L120S09C l120S09c = l120s09cdao.find(oid);
				l120s09cdao.delete(l120S09c);
			}
		}
	}

	// J-107-0226_11557_B1001 疑似洗錢或資恐交易態樣檢核表-授信
	@Override
	public Map<String, String> queryL120s09cInit(String mainId,
			String[] chooseCustArr, String versionDate) {
		Map<String, String> resMap = new HashMap<String, String>();

		// 借款人姓名、統編
		StringBuilder sb1 = new StringBuilder();
		StringBuilder sb2 = new StringBuilder();

		String caseType = "";// 檢視時點
		String caseNo = "";// 案號
		String caseDate = "";// 簽案日期
		StringBuilder displayBusCdSb = new StringBuilder();// 行業別

		// 將前端傳來選擇的"借款人"裝到一個list中，讓後續在準備借款人資料時只拿前端有選的借款人
		List<String> chooseCustList = new ArrayList<String>();
		for (String custJson : chooseCustArr) {
			chooseCustList.add(custJson);
		}

		// 依據是簽報書or動審表，抓不同的欄位來源丟回新增畫面的預設資料
		L120M01A l120m01a = this.findModelByMainId(L120M01A.class, mainId);

		if (l120m01a != null) {
			// 簽報書
			caseType = "0";// 檢視時點

			// 借款人姓名、統編
			List<L120S01A> l120s01aList = this.findL120s01aByMainId(mainId);
			for (L120S01A model : l120s01aList) {
				String custId = model.getCustId();
				String dupNo = model.getDupNo();

				// 是前端有選擇的借款人統編才放資料
				if (chooseCustList.contains(custId + "^" + dupNo)) {
					sb1.append((sb1.length() > 0) ? "，" : "").append(
							Util.trim(model.getCustName()));
					sb2.append((sb2.length() > 0) ? "，" : "").append(
							Util.trim(custId + " " + dupNo));

					// 2024.08.16  10807版本才會用得到，先註解掉了
					// L120S01B．借款人 企金基本資料檔
					// 撈出借款人行業別
					// L120S01B l120s01b = this.findL120s01bByUniqueKey(mainId,
					// custId, dupNo);
					// if (l120s01b != null) {
					// String busCode = l120s01b.getBusCode();// 行業對象別
					// String ecoNm = l120s01b.getEcoNm();// 行業對免別名稱
					// String bussKind = l120s01b.getBussKind();// 次產業別
					// String ecoNm07A = l120s01b.getEcoNm07A();// 資產業別名稱
					// String displayBusCd = getDisplayBusCd(busCode, ecoNm,
					// bussKind, ecoNm07A);// 行業別
					// displayBusCdSb.append(
					// (displayBusCdSb.length() > 0) ? "，" : "")
					// .append(Util.trim(displayBusCd));
					// }
				}

			}

			caseNo = Util.nullToSpace(l120m01a.getCaseNo());// 案號
			caseDate = Util.nullToSpace(TWNDate.toAD(l120m01a.getCaseDate()));// 簽案日期

		} else {
			// 動審表
			L160M01A l160m01a = this.findModelByMainId(L160M01A.class, mainId);
			if (l160m01a != null) {
				caseType = "1";// 檢視時點

				// 借款人姓名、統編
				List<Map<String, Object>> custNameList = eloanDbBaseService
						.findL140M01AInfoByMainId(l160m01a.getMainId());

				// 過濾重複的借款人
				Map<String, Map<String, Object>> distMap = new HashMap<String, Map<String, Object>>();
				for (Map<String, Object> custMap : custNameList) {
					String custId = Util.trim(custMap.get("CUSTID"));
					String dupNo = Util.trim(custMap.get("DUPNO"));
					if (distMap.get(custId + custMap) != null) {
						// 重複的人不放入map
					} else {
						distMap.put(custId + dupNo, custMap);
					}
				}

				for (Map.Entry<String, Map<String, Object>> entry : distMap
						.entrySet()) {
					Map<String, Object> map = entry.getValue();
					String custName = Util.trim(map.get("CUSTNAME"));
					String custId = Util.trim(map.get("CUSTID"));
					String dupNo = Util.trim(map.get("DUPNO"));
					String m14MainId = Util.trim(map.get("M14MAINID"));

					// 是前端有選擇的借款人統編才放資料
					if (chooseCustList.contains(custId + "^" + dupNo)) {
						sb1.append((sb1.length() > 0) ? "，" : "").append(
								Util.trim(custName));
						sb2.append((sb2.length() > 0) ? "，" : "").append(
								Util.trim(custId + " " + dupNo));

						// 2024.08.16  10807版本才會用得到，先註解掉了
						// L120S01B．借款人 企金基本資料檔
						// 撈出借款人行業別
						// L120M01C l120m01c = l120m01cDao
						// .findoneByRefMainId(m14MainId);
						// if (l120m01c != null) {
						// // 怕串不出資料，留空白給user輸
						// String l120m01cMainId = l120m01c.getMainId();
						// L120S01B l120s01b = this.findL120s01bByUniqueKey(
						// l120m01cMainId, custId, dupNo);
						// String busCode = l120s01b.getBusCode();// 行業對象別
						// String ecoNm = l120s01b.getEcoNm();// 行業對免別名稱
						// String bussKind = l120s01b.getBussKind();// 次產業別
						// String ecoNm07A = l120s01b.getEcoNm07A();// 資產業別名稱
						// String displayBusCd = getDisplayBusCd(busCode,
						// ecoNm, bussKind, ecoNm07A);// 行業別
						// displayBusCdSb.append(
						// (displayBusCdSb.length() > 0) ? "，" : "")
						// .append(Util.trim(displayBusCd));
						// }
					}
				}

				caseNo = Util.nullToSpace(l160m01a.getCaseNo());// 案號
				caseDate = Util.nullToSpace(TWNDate.toAD(CapDate
						.getCurrentTimestamp()));// 今天日期
			}
		}

		resMap.put("state_custName", sb1.toString());// 客戶姓名
		resMap.put("state_custId", sb2.toString());// 客戶統編
		resMap.put("state_caseNo", caseNo);

		// 針對不同版本若有欄位不給預設值，在這裡做設定
		if ("11311".equals(versionDate)) {
			// 11311的版本幾乎與11311版本雷同
			// 檢核日期->預設放今天
			resMap.put("state_caseDate", CapDate.getCurrentDate("yyyy-MM-dd"));
		}if ("11307".equals(versionDate)) {
			// 11307的版本幾乎與11107版本雷同，多了一個檢視時點，但目前也不幫他選
			// 檢核日期->預設放今天
			resMap.put("state_caseDate", CapDate.getCurrentDate("yyyy-MM-dd"));
		} else if ("11107".equals(versionDate)) {
			// 11107的版本不幫他選檢視時點&沒有行業別欄位了
			// 沒有交易日期了，而是檢核日期->預設放今天
			resMap.put("state_caseDate", CapDate.getCurrentDate("yyyy-MM-dd"));
		} else {
			// 10807的版本才需要這兩個欄位
			resMap.put("state_caseDate", caseDate);
			resMap.put("state_caseType", caseType);
			resMap.put("state_ecoNm", displayBusCdSb.toString());
		}
		return resMap;
	}

	/**
	 * 行業別組字
	 * 
	 * @param bussCode
	 * @param ecoNm
	 * @param bussKind
	 * @param ecoNm07A
	 * @return
	 */
	private String getDisplayBusCd(String bussCode, String ecoNm,
			String bussKind, String ecoNm07A) {

		String displayBusCd1 = Util.trim(bussCode);
		String displayBusCd2 = Util.trim(ecoNm);

		if (Util.notEquals(bussKind, "")) {

			displayBusCd1 = displayBusCd1 + "-" + Util.trim(bussKind);
			displayBusCd2 = displayBusCd2 + "-" + Util.trim(ecoNm07A);
		}

		if (Util.notEquals(displayBusCd2, "")) {
			return displayBusCd1 + "：" + displayBusCd2;
		} else {
			return displayBusCd1;
		}

	}

	/**
	 * I-111-0089 調整授信審查處子系統e-loan，除徵信系統外，其餘排除掃瞄PEPs名單。
	 * <p/>
	 * 取得簽報書AML List
	 */
	@Override
	public Map<String, String[]> getAmlListPepsMap(GenericBean model) {
		Map<String, String[]> pepsMap = new HashMap<String, String[]>();

		if (model != null) {
			if (model instanceof L160M01A) { // 動審表 檢查 簽報書
				L160M01A l160m01a = (L160M01A) model;
				if (l160m01a != null) {
					L120M01A tempL120m01a = this
							.findL120m01aByL160m01a(l160m01a);

					if (tempL120m01a != null) {
						if (UtilConstants.Casedoc.DocType.企金
								.equals(tempL120m01a.getDocType())) {
							List<L120S09A> l120s09as = this
									.findL120s09aByMainId(tempL120m01a
											.getMainId());
							L120S09B l120s09b = this
									.findL120s09bByMainId(tempL120m01a
											.getMainId());
							String tNcResult = "";
							String tNcResultRemark = "";
							if (l120s09b != null) {
								tNcResult = Util.trim(l120s09b.getNcResult());
								tNcResultRemark = Util.trim(l120s09b
										.getNcResultRemark());
							}
							String[] ncData = { "LMS", tNcResult,
									tNcResultRemark };
							if (l120s09as != null && !l120s09as.isEmpty()) {
								for (L120S09A l120s09a : l120s09as) {
									String tCustId = Util.trim(l120s09a
											.getCustId());
									String tDupNo = Util.trim(l120s09a
											.getDupNo());
									String fullKey = tCustId + tDupNo;
									if (Util.notEquals(fullKey, "")) {
										if (!pepsMap.containsKey(fullKey)) {
											pepsMap.put(fullKey, ncData);
										}
									}
								}
							}
						}
					}
				}
			} else if (model instanceof L120M01A) { // 簽報書 檢查 徵信報告或資信簡表
				L120M01A l120m01a = (L120M01A) model;
				if (l120m01a != null) {
					if (UtilConstants.Casedoc.DocType.企金.equals(l120m01a
							.getDocType())) {
						List<String> ces120MainIds = this
								.getCesMainIdList(l120m01a);

//						StringBuilder cesMainIds = new StringBuilder();
//						if (ces120MainIds != null && !ces120MainIds.isEmpty()) {
//							for (String cesMainId : ces120MainIds) {
//								cesMainIds.append(cesMainIds.length() > 0 ? ","
//										: "");
//								cesMainIds.append("'");
//								cesMainIds.append(cesMainId);
//								cesMainIds.append("'");
//							}
//						}

						if (CollectionUtils.isNotEmpty(ces120MainIds)) {
							List<Map<String, Object>> ces120ListById = eloanDbBaseService
									.findC120S01D_selectGroupByCustId(ces120MainIds);
							for (Map<String, Object> idMap : ces120ListById) {
								String tCustId = Util.trim(MapUtils.getString(
										idMap, "CUSTID", ""));
								String tDupNo = Util.trim(MapUtils.getString(
										idMap, "DUPNO", ""));
								String tSn = Util.trim(MapUtils.getString(
										idMap, "SN", ""));
								String tNcResult = Util.trim(MapUtils
										.getString(idMap, "NCRESULT", ""));
								String tNcResultRemark = Util
										.trim(MapUtils.getString(idMap,
												"NCRESULTREMARK", ""));
								String tFullKey = tCustId + tDupNo;
								if (Util.notEquals(tFullKey, "")) {
									if (!pepsMap.containsKey(tFullKey)) {
										String[] ncData = { "CES", tNcResult,
												tNcResultRemark, tSn };
										pepsMap.put(tFullKey, ncData);
									}
								}
							}

							List<Map<String, Object>> ces120ListByName = eloanDbBaseService
									.findC120S01D_selectGroupByCustName(ces120MainIds);
							for (Map<String, Object> nameMap : ces120ListByName) {
								String tCustId = Util.trim(MapUtils.getString(
										nameMap, "CUSTID", ""));
								String tDupNo = Util.trim(MapUtils.getString(
										nameMap, "DUPNO", ""));
								String tSn = Util.trim(MapUtils.getString(
										nameMap, "SN", ""));
								String tNcResult = Util.trim(MapUtils
										.getString(nameMap, "NCRESULT", ""));
								String tNcResultRemark = Util.trim(MapUtils
										.getString(nameMap, "NCRESULTREMARK",
												""));
								String tFullKey = tCustId + tDupNo;
								if (Util.notEquals(tFullKey, "")) {
									if (!pepsMap.containsKey(tFullKey)) {
										String[] ncData = { "CES", tNcResult,
												tNcResultRemark, tSn };
										pepsMap.put(tFullKey, ncData);
									}
								}
							}
						}
					}
				}
			}
		}

		return pepsMap;
	}

	/**
	 * I-111-0089 調整授信審查處子系統e-loan，除徵信系統外，其餘排除掃瞄PEPs名單。
	 * <p/>
	 * 參照 getLMSAndCesDocAMLCustNameMap 內的撈取方法 取得簽報書內徵信文件MainId List
	 */
	@Override
	public List<String> getCesMainIdList(L120M01A l120m01a) {
		List<String> ces120MainIds = new ArrayList<String>();
		if (l120m01a != null) {
			List<L120M01E> listL120m01e = this.findL120m01eByMainId(l120m01a
					.getMainId());
			for (L120M01E l120m01e : listL120m01e) {
				if (UtilConstants.Casedoc.L120m01eDocType.徵信報告書.equals(Util
						.trim(l120m01e.getDocType()))) {
					if (Util.notEquals(Util.trim(l120m01e.getDocOid()), "")) {
						String c140MainId = Util.trim(l120m01e.getDocOid());
						String c120MainId = "";

						List<Map<String, Object>> temp140List = eloanDbBaseService
								.C140M01A_selCustname2(c140MainId);
						for (Map<String, Object> map : temp140List) {
							c120MainId = Util.trim(map.get("C120M01A_MAINID"));
							break;
						}

						if (Util.notEquals(c120MainId, "")) {
							if (!ces120MainIds.contains(c120MainId)) {
								ces120MainIds.add(c120MainId);
							}
						}
					}
				} else if (UtilConstants.Casedoc.L120m01eDocType.資信簡表
						.equals(Util.trim(l120m01e.getDocType()))) {

					String c120MainId = Util.trim(l120m01e.getDocOid());
					if (!ces120MainIds.contains(c120MainId)) {
						ces120MainIds.add(c120MainId);
					}
				}
			}
		}
		return ces120MainIds;
	}

	/**
	 * J-111-0278_05097_B1001 Web
	 * e-Loan紐約分行企金限制eloan簽報書中，AMLCFT頁籤中，Name欄位僅能儲存英文、數字、標點符號，且會拒絕非英文數字之字型
	 * 
	 * AML本案戶名只能是英數字或標點符號
	 * 
	 * @param caseBrId
	 * @return
	 */
	@Override
	public boolean isAmlCustNameOnlyEng(String caseBrId) {
		boolean onlyEng = false;

		IBranch ibranch = branchService.getBranch(caseBrId);

		String countryType = "";
		if (ibranch != null) {
			countryType = ibranch.getCountryType();
			if (Util.notEquals(countryType, "TW")) {
				countryType = "OV";
			}
		}

		String COM_AML_SEND_ONLY_ENGNAME = Util.trim(lmsService
				.getSysParamDataValue("COM_AML_CUSTNAME_ONLY_ENG"));

		if (Util.notEquals(COM_AML_SEND_ONLY_ENGNAME, "")) {
			for (String xx : COM_AML_SEND_ONLY_ENGNAME.split(",")) {
				if (Util.equals(xx, caseBrId) || Util.equals(countryType, xx)) {
					onlyEng = true;
					break;
				}
			}
		}

		return onlyEng;
	}

	/**
	 * J-111-0278_05097_B1001 Web
	 * e-Loan紐約分行企金限制eloan簽報書中，AMLCFT頁籤中，Name欄位僅能儲存英文、數字、標點符號，且會拒絕非英文數字之字型
	 * 
	 * 判斷內容是否有非英文、數字、標點符號
	 * 
	 * @param text
	 * @return
	 */
	@Override
	public boolean hasFullCharInStringText(String semiText) {
		boolean hasFull = false;

		try {
			for (int i = 0; i < semiText.length(); i++) {

				String text = semiText.substring(i, i + 1);
				String strHex = Integer.toHexString((int) text.charAt(0));

				if (strHex.length() == 4) {
					hasFull = true;
					break;
				}
			}

		} catch (Exception e) {

		}

		return hasFull;
	}

	/**
	 * AML傳送掃描時只送英文戶名
	 * 
	 * @param caseBrId
	 * @return
	 */
	@Override
	public boolean sendAmlOnlyEngName(String caseBrId) {
		boolean onlyEng = false;

		IBranch ibranch = branchService.getBranch(caseBrId);

		String countryType = "";
		if (ibranch != null) {
			countryType = ibranch.getCountryType();
			if (Util.notEquals(countryType, "TW")) {
				countryType = "OV";
			}
		}

		String COM_AML_SEND_ONLY_ENGNAME = Util.trim(lmsService
				.getSysParamDataValue("COM_AML_SEND_ONLY_ENGNAME"));

		if (Util.notEquals(COM_AML_SEND_ONLY_ENGNAME, "")) {
			for (String xx : COM_AML_SEND_ONLY_ENGNAME.split(",")) {
				if (Util.equals(xx, caseBrId) || Util.equals(countryType, xx)) {
					onlyEng = true;
					break;
				}
			}
		}

		return onlyEng;
	}

	/**
	 * J-112-0534 取得引入之T70資訊
	 * 
	 * @param mainId
	 * @param custId
	 * @return
	 */
	@Override
	public L120S26A findL120s26aByMainIdCustId(String mainId, String custId) {
		return l120S26aDao.findByMainIdAndCustId(mainId, custId);
	}

	/**
	 * 列印T70功能僅限有負面紀錄的時候才可列印(列印時印出該案件全部的T70清單)
	 * 
	 * @param mainId
	 * @param l120s09aList
	 * @param negCase
	 * @return
	 */
	@Override
	public List<L120S26A> findL120s26aListForRpt(String mainId,
			List<L120S09A> l120s09aList, boolean negCase) {
		List<String> custList = new ArrayList<String>();
		List<L120S26A> list = new ArrayList<L120S26A>();
		for (L120S09A l120s09a : l120s09aList) {
			custList.add(l120s09a.getCustId());
		}
		if (custList.size() > 0) {
			list = l120S26aDao.findByMainIdAndCustList(mainId,
					custList.toArray(new String[custList.size()]), negCase);
		}
		return list;
	}

	/**
	 * 確認是否至少有引入一筆主借款人之徵信報告或資信簡表
	 * 
	 * @param l120m01a
	 * @return
	 * @throws CapException
	 */
	@Override
	public void checkL120m01eForT70(L120M01A l120m01a) throws CapException {
		boolean result = false;
		List<L120M01E> listL120m01e = this.findL120m01eByMainId(l120m01a
				.getMainId());
		for (L120M01E l120m01e : listL120m01e) {
			if ((UtilConstants.Casedoc.L120m01eDocType.徵信報告書.equals(l120m01e
					.getDocType()) || UtilConstants.Casedoc.L120m01eDocType.資信簡表
					.equals(l120m01e.getDocType()))) {
				if (Util.trim(l120m01e.getDocCustId()).equals(
						l120m01a.getCustId())
						&& Util.trim(l120m01e.getDocDupNo()).equals(
								l120m01a.getDupNo())) {
					// 有一筆主借款人之徵信報告或資信簡表
					result = true;
				}
			}
		}

		if (!result
				&& !(UtilConstants.DEFAULT.是.equals(Util.trim(l120m01a
						.getCesCase())))
				&& !UtilConstants.Casedoc.DocCode.異常通報.equals(Util
						.trim(l120m01a.getDocCode()))) {
			throw new CapMessageException("簽報書至少要有一筆主借款人之徵信報告或資信簡表", getClass());
		}
	}

	/**
	 * 撈取L120s09a清單後引入T70資料
	 * 
	 * @param l120m01a
	 * @return
	 */
	@Override
	public StringBuilder importL120s09aT70Data(L120M01A l120m01a) {
		String mainId = l120m01a.getMainId();
		StringBuilder over2MonthSb = new StringBuilder();
		List<String> cesMainIdList = this.getCesMainIdList(l120m01a);

		// 取得L120S09A清單
		List<L120S09A> l120s09as = this.findL120s09aByMainId(mainId);

		if (l120s09as != null && !l120s09as.isEmpty()) {
			for (L120S09A l120s09a : l120s09as) {
				// 借戶 or 連保人 才需要T70
				if (LMSUtil.isContainValue(
						Util.trim(l120s09a.getCustRelation()),
						UtilConstants.Casedoc.L120s09aBlackListCtlTarget.借戶,
						",")
						|| LMSUtil
								.isContainValue(
										Util.trim(l120s09a.getCustRelation()),
										UtilConstants.Casedoc.L120s09aBlackListCtlTarget.連保人,
										",")) {
					String custId = Util.trim(l120s09a.getCustId());
					String dupNo = Util.trim(l120s09a.getDupNo());
					String custName = Util.trim(l120s09a.getCustName());
					if (Util.isNotEmpty(custId)) {
						// 引進T70 金融機構介接查調證券商授信業務負面信用資料
						String isOver2Month = this.importT70(mainId, custId,
								dupNo, custName, cesMainIdList);
						if (Util.equals("Y", isOver2Month)) {
							if (Util.isNotEmpty(over2MonthSb)) {
								over2MonthSb.append("、");
							}
							over2MonthSb.append(custId);
						}
					}
				}
			}
		}
		return over2MonthSb;
	}

	/**
	 * 從聯徵及徵信系統引入T70
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @param custName
	 * @param cesMainIdList
	 * @return
	 */
	@Override
	public String importT70(String mainId, String custId, String dupNo,
			String custName, List<String> cesMainIdList) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Date theDateBefore2Month = getBeforeDay(
				Util.parseDate(CapDate.getCurrentDate("yyyy-MM-dd")), 60);
		String over2Month = "N";

		// 引進T70 金融機構介接查調證券商授信業務負面信用資料
		Map<String, Object> tas700Data = ejcicService.findTAS700ById(custId);
		L120S26A l120s26a = l120S26aDao.findByMainIdAndCustId(mainId, custId);
		if (tas700Data != null) {// EJCIC有資料就用EJCIC的
			String qDate = (String) tas700Data.get("QDATE"); // 查詢資料日期
			String r_Status = (String) tas700Data.get("R_STATUS"); // 查詢狀態
			String negFlag = (String) tas700Data.get("NEG_FLAG"); // 負面紀錄
			String negAmt = CapString.trimNull(tas700Data.get("NEG_AMT")); // 未清償總餘額
			if (Util.isNotEmpty(negAmt)) {
				negAmt = negAmt.replaceAll(",", "");
				negAmt = String.valueOf(CapMath.getBigDecimal(negAmt)
						.intValue());
			}
			if (Util.isNotEmpty(Util.trim(qDate))) {
				Date t70Date = Util.parseDate(qDate);
				if (l120s26a == null || Util.isEmpty(l120s26a)) {
					l120s26a = this.createL120S26a(mainId, custId, dupNo,
							custName, user.getUserId());
				}
				// 更新原則：
				// 1.本來沒資料
				// 2.本來就有資料，且從EJCIC抓到的資料比較新就更新
				if (Util.isEmpty(l120s26a.getT70Date())
						|| (Util.isNotEmpty(l120s26a.getT70Date()) && t70Date
								.compareTo(CapDate.getDate(
										l120s26a.getT70Date(), "yyyy-MM-dd")) > 0)) {
					Map<String, Object> htmlData = ejcicService
							.findBT2FILEByTxIdIdQDate("HT70", custId, qDate);
					if (htmlData != null) {
						//
						try {
							String qRpt = Util.trim(MapUtils.getObject(
									htmlData, "QRPT"));
							byte[] qRptData = qRpt.getBytes("big5");

							String docFileOid = this.setDocfileData(mainId,
									user.getUnitNo(), qRptData,
									l120s26a.getDocfileoid());
							l120s26a.setDocfileoid(docFileOid);
						} catch (Exception e) {
							logger.error("write T70 file fail custId={}",
									custId);
						}
					}

					l120s26a.setT70Date(CapDate.formatDate(t70Date,
							UtilConstants.DateFormat.YYYY_MM_DD));// qDate
					l120s26a.setT70Status(r_Status);
					l120s26a.setT70NegFlag(negFlag);
					l120s26a.setT70Amt(negAmt);
					lmsService.save(l120s26a);
					// 收回的資料要檢查是否超過2個月，若超過要出提示訊息(但EJCIC資料不會保留超過2個月)
					if (theDateBefore2Month.compareTo(t70Date) > 0) {
						over2Month = "Y";
					}
				}
			}

		} else if (cesMainIdList != null && cesMainIdList.size() > 0) {
			// 沒有就去徵信報告撈
			List<Map<String, Object>> c140m01List = eloanDbBaseService
					.findC140M01A_selectT70Result(custId, dupNo, cesMainIdList);
			if (c140m01List != null && c140m01List.size() > 0) {
				Map<String, Object> t70ResultMap = c140m01List.get(0);
				String t70Result = Util.trim(t70ResultMap.get("JSONOB"));
				String T70NegFlag = "";
				String T70Date_s = "";
				String T70Status = "";
				String T70Amt = "";
				JSONObject jsons = JSONObject.fromObject(t70Result);
				if (!(jsons == null || Util.equals("null", Util.trim(jsons)))) {
					T70NegFlag = Util.trim(jsons.getString("T70NegFlag"));// 負面紀錄
					T70Date_s = Util.trim(jsons.getString("T70Date"));// 資料回覆日期
					T70Status = Util.trim(jsons.getString("T70Status"));// 查詢狀態
					T70Amt = Util.trim(jsons.getString("T70Amt"));// 未清償總餘額
					// 徵信沒資料就不處理
					if (Util.isNotEmpty(T70Date_s)
							&& Util.isNotEmpty(T70NegFlag)
							&& Util.isNotEmpty(T70Status)
							&& Util.isNotEmpty(T70Amt)) {
						if (l120s26a == null || Util.isEmpty(l120s26a)) {
							l120s26a = this.createL120S26a(mainId, custId,
									dupNo, custName, user.getUserId());
						}
						Date t70Date = Util.parseDate(T70Date_s);
						// 更新原則：
						// 1.本來沒資料
						// 2.本來就有資料，徵信的資料比較新就更新
						if (Util.isEmpty(l120s26a.getT70Date())
								|| (Util.isNotEmpty(l120s26a.getT70Date()) && t70Date
										.compareTo(CapDate.getDate(
												l120s26a.getT70Date(),
												"yyyy-MM-dd")) > 0)) {
							l120s26a.setT70Amt(T70Amt);
							l120s26a.setT70Date(CapDate.formatDate(t70Date,
									UtilConstants.DateFormat.YYYY_MM_DD));
							l120s26a.setT70NegFlag(T70NegFlag);
							l120s26a.setT70Status(T70Status);

							// 取得徵信資料的檔案
							String c140MainId = Util.trim(t70ResultMap
									.get("MAINID"));
							Map<String, Object> c140DocFileOid = eloanDbBaseService
									.findC140M01A_selectT70Html(c140MainId,
											custId);
							if (c140DocFileOid != null) {
								String cesT70FileOid = Util.trim(c140DocFileOid
										.get("DOCOID"));
								if (Util.isNotEmpty(cesT70FileOid)) {
									DocFile cesT70File = docFileService
											.findByOidAndSysId(cesT70FileOid,
													"CES");
									if (cesT70File != null
											&& Util.isNotEmpty(cesT70File
													.getData())) {
										String docFileOid = this
												.setDocfileData(
														mainId,
														user.getUnitNo(),
														cesT70File.getData(),
														l120s26a.getDocfileoid());
										l120s26a.setDocfileoid(docFileOid);
									}
								}
							}
							lmsService.save(l120s26a);
							// 從徵信收回的資料要檢查是否超過2個月，若超過要出提示訊息
							if (theDateBefore2Month.compareTo(t70Date) > 0) {
								over2Month = "Y";
							}
						}
					}
				}
			}
		}
		return over2Month;
	}

	private Date getBeforeDay(Date date, int day) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.DATE, -day);
		return calendar.getTime();
	}

	private L120S26A createL120S26a(String mainId, String custId, String dupNo,
			String custName, String creator) {
		L120S26A l120s26a = new L120S26A();
		l120s26a.setMainId(mainId);
		l120s26a.setCustId(custId);
		l120s26a.setDupNo(dupNo);
		l120s26a.setCustName(custName);
		l120s26a.setCreator(creator);
		l120s26a.setCreateTime(CapDate.getCurrentTimestamp());
		return l120s26a;
	}

	private String setDocfileData(String mainId, String branchId,
			byte[] qRptData, String oldDocfileoid) {
		DocFile docFile = new DocFile();
		docFile.setBranchId(branchId);
		docFile.setContentType("text/html");
		docFile.setMainId(mainId);
		docFile.setPid(null);
		docFile.setCrYear(CapDate.getCurrentDate("yyyy"));
		docFile.setFieldId("T70");
		docFile.setDeletedTime(null);
		docFile.setSrcFileName("T70.html");
		docFile.setUploadTime(CapDate.getCurrentTimestamp());
		docFile.setSysId("LMS");
		docFile.setFileSize(qRptData.length);
		docFile.setData(qRptData);
		docFile.setFileDesc("T70");
		String docFileOid = docFileService.save(docFile);

		if (Util.isNotEmpty(oldDocfileoid)) {
			// 刪除舊檔
			docFileService.delete(oldDocfileoid);
			DocFile oldDocFile = docFileService.findByOidAndSysId(
					oldDocfileoid, "LMS");
			if (Util.isNotEmpty(oldDocFile)) {
				FileUtils.deleteQuietly(docFileService.getRealFile(oldDocFile));
			}
		}
		return docFileOid;
	}

	/**
	 * 撈取L120s09a清單後從ODS引入「受告誡處分」資訊
	 * 
	 * @param mainId
	 * @return
	 */
	@Override
	public void importL120s09aWarnData(String mainId) {
		if (is_function_on_codetype("J-113-0082_Cmfwarnp")) {
			// 取得L120S09A清單
			List<L120S09A> l120s09as = this.findL120s09aByMainId(mainId);

			if (l120s09as != null && !l120s09as.isEmpty()) {
				for (L120S09A l120s09a : l120s09as) {
					// 借戶 才需要引入「受告誡處分」資訊
					if (LMSUtil
							.isContainValue(
									Util.trim(l120s09a.getCustRelation()),
									UtilConstants.Casedoc.L120s09aBlackListCtlTarget.借戶,
									",")) {
						String custId = Util.trim(l120s09a.getCustId());
						if (Util.isNotEmpty(custId)) {
							String cmfwarnp_status = "";
							String cmfwarnpQueryResultInfo = "";
							// 引進「受告誡處分」資訊
							Map<String, Object> warnData = lmsService
									.queryOdsCmfwarnp(custId);
							if (warnData != null && Util.isNotEmpty(warnData)) {
								cmfwarnp_status = (String) warnData
										.get("status"); // 受告誡處分結果：1.有 2.無 3.不適用
								cmfwarnpQueryResultInfo = (String) warnData
										.get("msg");
							}
							l120s09a.setCmfwarnpResult(cmfwarnp_status);
							l120s09a.setCmfwarnpQueryResultInfo(cmfwarnpQueryResultInfo);
							l120s09a.setCmfwarnpQueryTime(CapDate
									.getCurrentTimestamp());
						}
					} else {
						// 如果不是借戶就清空為null
						l120s09a.setCmfwarnpResult(null);
						l120s09a.setCmfwarnpQueryResultInfo(null);
						l120s09a.setCmfwarnpQueryTime(null);
					}
					lmsService.save(l120s09a);
				}
			}
		}

	}

	/**
	 * 需要引入受告誡處分
	 * 
	 * @param mainId
	 * @return
	 * @throws CapException
	 */
	@Override
	public void checkCmfwarnpNeed(String mainId) throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS20APanel.class);
		String checkResult = "";
		if (is_function_on_codetype("J-113-0082_checkCmfwarnpNeed")) {
			// 取得L120S09A清單
			List<L120S09A> l120s09as = this.findL120s09aByMainId(mainId);

			if (l120s09as != null && !l120s09as.isEmpty()) {
				for (L120S09A l120s09a : l120s09as) {
					// 借戶 需要引入「受告誡處分」資訊
					if (LMSUtil
							.isContainValue(
									Util.trim(l120s09a.getCustRelation()),
									UtilConstants.Casedoc.L120s09aBlackListCtlTarget.借戶,
									",")) {
						if (Util.isEmpty(l120s09a.getCmfwarnpResult())
								|| Util.equals(
										UtilConstants.Casedoc.L120s09aCmfwarnpResultCode.不適用,
										l120s09a.getCmfwarnpResult())) {
							// 只要有借戶沒引入或是引入失敗就要出通知
							checkResult = pop
									.getProperty("L120S09a.cmfwarnpResult.error");
							break;
						}
					}
				}
			}
		}
		if (Util.isNotEmpty(checkResult)) {
			throw new CapMessageException(checkResult, getClass());
		}
	}

	/**
	 * 需要引入受告誡處分
	 * 
	 * @param mainId
	 * @param isCls
	 * @return
	 */
	@Override
	public String checkCmfwarnpNeedMsg(String mainId) {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS20APanel.class);
		String checkResult = "";
		if (is_function_on_codetype("J-113-0082_checkCmfwarnpNeed")) {
			// 取得L120S09A清單
			List<L120S09A> l120s09as = this.findL120s09aByMainId(mainId);

			if (l120s09as != null && !l120s09as.isEmpty()) {
				for (L120S09A l120s09a : l120s09as) {
					// 借戶 需要引入「受告誡處分」資訊
					if (LMSUtil
							.isContainValue(
									Util.trim(l120s09a.getCustRelation()),
									UtilConstants.Casedoc.L120s09aBlackListCtlTarget.借戶,
									",")) {
						if (Util.isEmpty(l120s09a.getCmfwarnpResult())
								|| Util.equals(
										UtilConstants.Casedoc.L120s09aCmfwarnpResultCode.不適用,
										l120s09a.getCmfwarnpResult())) {
							// 只要有借戶沒引入或是引入失敗就要出通知
							checkResult = pop
									.getProperty("L120S09a.cmfwarnpResult.error");
							break;
						}
					}
				}
			}
		}
		return checkResult;
	}

	/**
	 * 取得受告誡處分訊息
	 * 
	 * @param mainId
	 * @return
	 */
	@Override
	public String checkCmfwarnpResult(String mainId) {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS20APanel.class);
		StringBuilder cmfwarnpResultMsg = new StringBuilder();
		if (is_function_on_codetype("J-113-0082_Cmfwarnp")) {
			// 取得L120S09A清單
			List<L120S09A> l120s09as = this.findL120s09aByMainId(mainId);

			if (l120s09as != null && !l120s09as.isEmpty()) {
				StringBuilder cmfwarnpResults_1 = new StringBuilder(); // 查詢結果=1(有)
				StringBuilder cmfwarnpResults_3 = new StringBuilder(); // 查詢結果=3(不適用)
				for (L120S09A l120s09a : l120s09as) {
					if (UtilConstants.Casedoc.L120s09aCmfwarnpResultCode.有
							.equals(Util.trim(l120s09a.getCmfwarnpResult()))) {
						if (Util.isNotEmpty(cmfwarnpResults_1)) {// 有超過1筆
							cmfwarnpResults_1.append("</br>");
						}
						cmfwarnpResults_1
								.append(MessageFormat.format(
										pop.getProperty("L120S09a.cmfwarnpResult.Msg2"),
										Util.trim(l120s09a.getCustId()), Util
												.trim(l120s09a.getCustName()),
										Util.trim(l120s09a
												.getCmfwarnpQueryResultInfo())));
					}
					if (UtilConstants.Casedoc.L120s09aCmfwarnpResultCode.不適用
							.equals(Util.trim(l120s09a.getCmfwarnpResult()))) {
						if (Util.isNotEmpty(cmfwarnpResults_3)) {// 有超過1筆
							cmfwarnpResults_3.append("</br>");

						}
						cmfwarnpResults_3
								.append(MessageFormat.format(
										pop.getProperty("L120S09a.cmfwarnpResult.Msg4"),
										Util.trim(l120s09a.getCustId()),
										Util.trim(l120s09a.getCustName()),
										(l120s09a.getCmfwarnpQueryTime() == null ? ""
												: CapDate.formatDate(
														l120s09a.getCmfwarnpQueryTime(),
														UtilConstants.DateFormat.YYYY_MM_DD))));
					}
				}
				if (Util.isNotEmpty(cmfwarnpResults_1)) {
					cmfwarnpResultMsg
							.append(pop.getProperty("L120S09a.cmfwarnpResult.Msg1"
									+ (this.cmfwarnpOverSea(mainId) ? "OverSea"
											: ""))).append("</br>")
							.append(cmfwarnpResults_1.toString());
				}
				if (Util.isNotEmpty(cmfwarnpResults_3)) {
					if (Util.isNotEmpty(cmfwarnpResultMsg)) {
						cmfwarnpResultMsg.append("</br>");
					}
					cmfwarnpResultMsg
							.append(pop
									.getProperty("L120S09a.cmfwarnpResult.Msg3"))
							.append("</br>")
							.append(cmfwarnpResults_3.toString());
				}
			}
		}

		return cmfwarnpResultMsg.toString();
	}

	/**
	 * 受告誡處分海外案件
	 * 
	 * @param mainId
	 * @return
	 */
	@Override
	public boolean cmfwarnpOverSea(String mainId) {
		Boolean isOverSea = false;
		L120M01A l120m01a = this.findModelByMainId(L120M01A.class, mainId);
		if (l120m01a != null) {
			if (Util.equals("5", l120m01a.getTypCd())) {
				isOverSea = true;
			}
		} else {
			L160M01A l160m01a = this.findModelByMainId(L160M01A.class, mainId);
			if (l160m01a != null) {
				if (Util.equals("5", l160m01a.getTypCd())) {
					isOverSea = true;
				}
			}
		}
		return isOverSea;
	}
}
