/* 
 * LNF164.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.mfaloan.bean;

import java.math.BigDecimal;
import java.sql.Timestamp;

import javax.persistence.*;

import tw.com.iisi.cap.model.GenericBean;


/** 額度明細表利率條件<br/>
* 企金簽報書，也有上傳LNF164, 參考 e-loan 的L140M01G  
*/
public class LNF164 extends GenericBean {

	private static final long serialVersionUID = 1L;

	/** 
	 * 分行別<p/>
	 * NOT NULL
	 */
	@Column(name="LNF164_BR_NO", length=3, columnDefinition="CHAR(3)",unique = true)
	private String lnf164_br_no;

	/** 
	 * 額度序號<p/>
	 * NOT NULL
	 */
	@Column(name="LNF164_CONTRACT", length=12, columnDefinition="CHAR(12)",unique = true)
	private String lnf164_contract;

	/** 客戶編號 **/
	@Column(name="LNF164_CUST_ID", length=11, columnDefinition="CHAR(11)")
	private String lnf164_cust_id;

	/** 
	 * 契約種類<p/>
	 * 企金短期<br/>
	 *  企金中長期<br/>
	 *  消金
	 */
	@Column(name="LNF164_KIND", length=1, columnDefinition="CHAR(01)")
	private String lnf164_kind;

	/** 放款幣別 **/
	@Column(name="LNF164_SWFT", length=3, columnDefinition="CHAR(3)")
	private String lnf164_swft;

	/** 
	 * 放款科目<p/>
	 * T
	 */
	@Column(name="LNF164_LNAP_CODE", length=3, columnDefinition="CHAR(3)")
	private String lnf164_lnap_code;

	/** 
	 * 收息方式<p/>
	 * 按月計息<br/>
	 *  每三個月收息<br/>
	 *  每半年收息<br/>
	 *  按年收息<br/>
	 *  本息併付<br/>
	 *  預收利息<br/>
	 *  7. 期付金
	 */
	@Column(name="LNF164_INTRT_TYPE", length=1, columnDefinition="CHAR(1)")
	private String lnf164_intrt_type;

	/** 
	 * 利率條件是否字述<p/>
	 * Y/N
	 */
	@Column(name="LNF164_INT_KIND", length=1, columnDefinition="CHAR(1)")
	private String lnf164_int_kind;

	/** 加碼基礎 **/
	@Column(name="LNF164_INT_BASE", length=100, columnDefinition="CHAR(100)")
	private String lnf164_int_base;

	/** 加減碼 **/
	@Column(name="LNF164_INT_SPREAD", columnDefinition="DEC(7,5)")
	private BigDecimal lnf164_int_spread;

	/** 
	 * 利率方式<p/>
	 * 固定<br/>
	 *  機動<br/>
	 *  定期浮動
	 */
	@Column(name="LNF164_INT_TYPE", length=1, columnDefinition="CHAR(1)")
	private String lnf164_int_type;

	/** 
	 * 利率變動方式<p/>
	 * W 每週<br/>
	 *  M 每月<br/>
	 *  S 每季<br/>
	 *  H 每半年<br/>
	 *  N 每九個月<br/>
	 *  Y 每年
	 */
	@Column(name="LNF164_INTCHG_TYPE", length=1, columnDefinition="CHAR(1)")
	private String lnf164_intchg_type;

	
	/** 寫入時間 **/
	@Column(name = "LNF164_TIMESTAMP", columnDefinition = "TIMESTAMP")
	private Timestamp lnf164_timestamp;
	/** 
	 * 利率條件中文敘述<p/>
	 * 無論採字述與否皆提供
	 */
	@Column(name="LNF164_INT_MEMO", length=200, columnDefinition="CHAR(200)")
	private String lnf164_int_memo;

	
	@Column(name="LNF164_INTCHG_CYCL", length=1, columnDefinition="CHAR(1)")
	private String lnf164_intchg_cycl;
	

	/** 
	 * 取得分行別<p/>
	 * NOT NULL
	 */
	public String getLnf164_br_no() {
		return this.lnf164_br_no;
	}
	/**
	 *  設定分行別<p/>
	 *  NOT NULL
	 **/
	public void setLnf164_br_no(String value) {
		this.lnf164_br_no = value;
	}

	/** 
	 * 取得額度序號<p/>
	 * NOT NULL
	 */
	public String getLnf164_contract() {
		return this.lnf164_contract;
	}
	/**
	 *  設定額度序號<p/>
	 *  NOT NULL
	 **/
	public void setLnf164_contract(String value) {
		this.lnf164_contract = value;
	}

	/** 取得客戶編號 **/
	public String getLnf164_cust_id() {
		return this.lnf164_cust_id;
	}
	/** 設定客戶編號 **/
	public void setLnf164_cust_id(String value) {
		this.lnf164_cust_id = value;
	}

	/** 
	 * 取得契約種類<p/>
	 * 企金短期<br/>
	 *  企金中長期<br/>
	 *  消金
	 */
	public String getLnf164_kind() {
		return this.lnf164_kind;
	}
	/**
	 *  設定契約種類<p/>
	 *  企金短期<br/>
	 *  企金中長期<br/>
	 *  消金
	 **/
	public void setLnf164_kind(String value) {
		this.lnf164_kind = value;
	}

	/** 取得放款幣別 **/
	public String getLnf164_swft() {
		return this.lnf164_swft;
	}
	/** 設定放款幣別 **/
	public void setLnf164_swft(String value) {
		this.lnf164_swft = value;
	}

	/** 
	 * 取得放款科目<p/>
	 * T
	 */
	public String getLnf164_lnap_code() {
		return this.lnf164_lnap_code;
	}
	/**
	 *  設定放款科目<p/>
	 *  T
	 **/
	public void setLnf164_lnap_code(String value) {
		this.lnf164_lnap_code = value;
	}

	/** 
	 * 取得收息方式<p/>
	 * 按月計息<br/>
	 *  每三個月收息<br/>
	 *  每半年收息<br/>
	 *  按年收息<br/>
	 *  本息併付<br/>
	 *  預收利息<br/>
	 *  7. 期付金
	 */
	public String getLnf164_intrt_type() {
		return this.lnf164_intrt_type;
	}
	/**
	 *  設定收息方式<p/>
	 *  按月計息<br/>
	 *  每三個月收息<br/>
	 *  每半年收息<br/>
	 *  按年收息<br/>
	 *  本息併付<br/>
	 *  預收利息<br/>
	 *  7. 期付金
	 **/
	public void setLnf164_intrt_type(String value) {
		this.lnf164_intrt_type = value;
	}

	/** 
	 * 取得利率條件是否字述<p/>
	 * Y/N
	 */
	public String getLnf164_int_kind() {
		return this.lnf164_int_kind;
	}
	/**
	 *  設定利率條件是否字述<p/>
	 *  Y/N
	 **/
	public void setLnf164_int_kind(String value) {
		this.lnf164_int_kind = value;
	}

	/** 取得加碼基礎 **/
	public String getLnf164_int_base() {
		return this.lnf164_int_base;
	}
	/** 設定加碼基礎 **/
	public void setLnf164_int_base(String value) {
		this.lnf164_int_base = value;
	}

	/** 取得加減碼 **/
	public BigDecimal getLnf164_int_spread() {
		return this.lnf164_int_spread;
	}
	/** 設定加減碼 **/
	public void setLnf164_int_spread(BigDecimal value) {
		this.lnf164_int_spread = value;
	}

	/** 
	 * 取得利率方式<p/>
	 * 固定<br/>
	 *  機動<br/>
	 *  定期浮動
	 */
	public String getLnf164_int_type() {
		return this.lnf164_int_type;
	}
	/**
	 *  設定利率方式<p/>
	 *  固定<br/>
	 *  機動<br/>
	 *  定期浮動
	 **/
	public void setLnf164_int_type(String value) {
		this.lnf164_int_type = value;
	}

	/** 
	 * 取得利率變動方式<p/>
	 * W 每週<br/>
	 *  M 每月<br/>
	 *  S 每季<br/>
	 *  H 每半年<br/>
	 *  N 每九個月<br/>
	 *  Y 每年
	 */
	public String getLnf164_intchg_type() {
		return this.lnf164_intchg_type;
	}
	/**
	 *  設定利率變動方式<p/>
	 *  W 每週<br/>
	 *  M 每月<br/>
	 *  S 每季<br/>
	 *  H 每半年<br/>
	 *  N 每九個月<br/>
	 *  Y 每年
	 **/
	public void setLnf164_intchg_type(String value) {
		this.lnf164_intchg_type = value;
	}

	/** 
	 * 取得利率條件中文敘述<p/>
	 * 無論採字述與否皆提供
	 */
	public String getLnf164_int_memo() {
		return this.lnf164_int_memo;
	}
	/**
	 *  設定利率條件中文敘述<p/>
	 *  無論採字述與否皆提供
	 **/
	public void setLnf164_int_memo(String value) {
		this.lnf164_int_memo = value;
	}
	
	/** 取得寫入時間 **/
	public Timestamp getLnf164_timestamp() {
		return this.lnf164_timestamp;
	}

	/** 設定寫入時間 **/
	public void setLnf164_timestamp(Timestamp value) {
		this.lnf164_timestamp = value;
	}

	public String getLnf164_intchg_cycl() {
		return this.lnf164_intchg_cycl;
	}

	public void setLnf164_intchg_cycl(String value) {
		this.lnf164_intchg_cycl = value;
	}
}
