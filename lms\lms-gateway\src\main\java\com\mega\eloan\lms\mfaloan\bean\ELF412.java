package com.mega.eloan.lms.mfaloan.bean;

import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import tw.com.iisi.cap.model.GenericBean;

/** 覆審控制檔 **/
public class ELF412 extends GenericBean {
	/**
	 * 分行代號
	 */
	@Column(name = "ELF412_BRANCH", length = 3, columnDefinition = "CHAR(3)", nullable = false, unique = true)
	private String elf412_branch;

	/**
	 * 借款人統一編號
	 */
	@Column(name = "ELF412_CUSTID", length = 10, columnDefinition = "CHAR(10)", nullable = false, unique = true)
	private String elf412_custId;

	/**
	 * 重複序號
	 */
	@Column(name = "ELF412_DUPNO", length = 1, columnDefinition = "CHAR(1)", nullable = false, unique = true)
	private String elf412_dupNo;

	/**
	 * 主要授信戶 <br/>
	 * 1.新戶增額由ELF411 <br/>
	 * 2.舊案由STORE PROCEDURE
	 */
	@Column(name = "ELF412_MAINCUST", length = 1, columnDefinition = "CHAR(1)")
	private String elf412_mainCust;

	/**
	 * 資信評等類別 <br/>
	 * B=DBU、大型企業、L=DBU中小型企業、O=OBU
	 */
	@Column(name = "ELF412_CRDTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String elf412_crdType;

	/**
	 * 資信評等 <br/>
	 * MIS.CRDTTBL
	 */
	@Column(name = "ELF412_CRDTTBL", length = 2, columnDefinition = "CHAR(2)")
	private String elf412_crdtTbl;

	/**
	 * 信用模型評等類別 <br/>
	 * 1:DBU大型企業 2:DBU中型企業 3:DBU中小型企業 4:DBU不動產有建案規劃 5:DBU專案融資 6:DBU本國證券公司
	 * 8:DBU投資公司一般情況 9:DBU租賃公司 A:DBU一案建商 B:DBU非一案建商(擔保/土融) C:DBU非一案建商(無擔)
	 * D:投資公司情況一 E:投資公司情況二 F:境外船舶與航空器 G:境外貿易型控股型 H:國金部實體營運企業 I:國金部租賃業
	 * J:信託、基金及其他金融工具 K:澳洲不動產租售業
	 * 
	 * <br/>
	 * 參考 select substr(CODEVALUE, 2, 3) as mowType,CODEDESC from com.bcodetype
	 * where codetype='CRDType' and CODEVALUE like 'M%' and LOCALE='zh_TW' <br/>
	 * 參考 UtilConstants.mowType
	 */
	@Column(name = "ELF412_MOWTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String elf412_mowType;

	/**
	 * 信用模型評等 <br/>
	 * MIS.MOWTBL1
	 */
	@Column(name = "ELF412_MOWTBL1", length = 2, columnDefinition = "CHAR(2)")
	private String elf412_mowTbl1;

	/**
	 * 上上次覆審日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF412_LLRDATE", columnDefinition = "DATE")
	private Date elf412_llrDate;

	/**
	 * 上次覆審日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF412_LRDATE", columnDefinition = "DATE")
	private Date elf412_lrDate;

	/**
	 * 異常週期 <br/>
	 * A.一年覆審一次。 <br/>
	 * B.半年覆審一次(主要授信戶並符合信評條件)。 <br/>
	 * C.新戶/增額戶。 <br/>
	 * D.異常戶已三個月覆審過- 爾後半年覆審一次。 <br/>
	 * E.首次通報之異常戶。（必需在首次通報日後3月內覆審） <br/>
	 * F.會計師出具保留意見已三個月覆審過- 爾後半年覆審一次。 <br/>
	 * G.首次通報有會計師出具保留意見之異常戶。（必需在首次通報日後3月內覆審） <br/>
	 * H.主管機關指定覆審案件。
	 */
	@Column(name = "ELF412_RCKDLINE", length = 2, columnDefinition = "CHAR(2)")
	private String elf412_rckdLine;

	/**
	 * 原始週期 <br/>
	 * 如果沒有異常通報、增額、主管機關指定時之週期，只會為A或B
	 */
	@Column(name = "ELF412_OCKDLINE", length = 2, columnDefinition = "CHAR(2)")
	private String elf412_ockdLine;

	/**
	 * 戶況 <br/>
	 * 0.無餘額 <br/>
	 * 1.正常 <br/>
	 * 2.逾期 <br/>
	 * 3.催收 <br/>
	 * 4.呆帳(該戶項下最嚴重的代碼)
	 */
	@Column(name = "ELF412_CSTATE", length = 1, columnDefinition = "CHAR(1)")
	private String elf412_cState;

	/**
	 * 異常通報代碼 <br/>
	 * 參考 SELECT LNF07A_EXPLAIN,LNF07A_KEY_2,LNF07A_CONTENT_1 FROM LN.LNF07A
	 * WHERE LNF07A_KEY_1 = 'E-LOAN-EX-MDFLAG' ORDER BY INTEGER(LNF07A_KEY_2)
	 */
	@Column(name = "ELF412_MDFLAG", length = 2, columnDefinition = "CHAR(2)")
	private String elf412_mdFlag;

	/**
	 * 異常通報日期 <br/>
	 * LN.LNFE0851
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF412_MDDT", columnDefinition = "DATE")
	private Date elf412_mdDt;

	/**
	 * 異常通報情形 <br/>
	 * 約100個中文字(LN.LNFE0851)
	 */
	@Column(name = "ELF412_PROCESS", length = 202, columnDefinition = "CHAR(202)")
	private String elf412_process;

	/**
	 * 新作/增額 <br/>
	 * N.新做/ C.增貸 <br/>
	 * 來自 ELF411_NEWADD {N:新作 , C:增額, R:逾放轉正} <br/>
	 * 參考 UtilConstants.NEWADD
	 */
	@Column(name = "ELF412_NEWADD", length = 1, columnDefinition = "CHAR(1)")
	private String elf412_newAdd;

	/**
	 * 新作增額資料日期 <br/>
	 * ELF411_DATAYM 之資料
	 */
	@Column(name = "ELF412_NEWDATE", length = 6, columnDefinition = "CHAR(6)")
	private String elf412_newDate;

	/**
	 * 不覆審代碼 <br/>
	 * 1.本行或同業主辦之聯貸案件，非擔任管理行。 <br/>
	 * 2.十成定存。 <br/>
	 * 3.純進出押戶。 <br/>
	 * 4.對政府或政府所屬機關、學校之授信案件。 <br/>
	 * 5.拆放同業或對同業之融通。 <br/>
	 * 6.已列報為逾期放款或轉列催收款項之案件。 <br/>
	 * 7.銷戶 <br/>
	 * 8.本次暫不覆審 <br/>
	 * 9.已專案核准免辦理覆審之房屋仲介價金履約保證案件。
	 * 10.企業戶之外勞保證中長期授信案件，除於新作後辦理一次覆審外，免再辦理覆審，但嗣後如有增額、
	 * 減額、變更條件或續約時，仍應依本要點第五條規定辦理一次覆審
	 */
	@Column(name = "ELF412_NCKDFLAG", length = 2, columnDefinition = "CHAR(2)")
	private String elf412_nckdFlag;

	/**
	 * 不覆審日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF412_NCKDDATE", columnDefinition = "DATE")
	private Date elf412_nckdDate;

	/**
	 * 不覆審備註
	 */
	@Column(name = "ELF412_NCKDMEMO", length = 202, columnDefinition = "CHAR(202)")
	private String elf412_nckdMemo;

	/**
	 * 銷戶日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF412_CANCELDT", columnDefinition = "DATE")
	private Date elf412_cancelDt;

	/**
	 * DBUOBU 是否有共管
	 */
	@Column(name = "ELF412_DBUOBU", length = 1, columnDefinition = "CHAR(1)")
	private String elf412_dbuObu;

	/**
	 * DBU共管客戶統編
	 */
	@Column(name = "ELF412_DBUCOID", length = 202, columnDefinition = "CHAR(202)")
	private String elf412_dbuCoid;

	/**
	 * OBU共管客戶統編
	 */
	@Column(name = "ELF412_OBUCOID", length = 202, columnDefinition = "CHAR(202)")
	private String elf412_obuCoid;

	/**
	 * 人工維護日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF412_UPDDATE", columnDefinition = "DATE")
	private Date elf412_upddate;

	/**
	 * 人工調整ID
	 */
	@Column(name = "ELF412_UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String elf412_updater;

	/**
	 * 其他備註
	 */
	@Column(name = "ELF412_MEMO", length = 202, columnDefinition = "CHAR(202)")
	private String elf412_memo;

	/**
	 * 資料更新日
	 */
	@Column(name = "ELF412_TMESTAMP", columnDefinition = "TIMESTAMP")
	private Timestamp elf412_tmestamp;

	/**
	 * 人工調整週期 <br/>
	 * 目前資料['Y', 'N', '']
	 */
	@Column(name = "ELF412_UCKDLINE", length = 2, columnDefinition = "CHAR(2)")
	private String elf412_uckdLine;

	/**
	 * 人工調整週期有效日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF412_UCKDDT", columnDefinition = "DATE")
	private Date elf412_uckdDt;

	/**
	 * 資料日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF412_DATADT", columnDefinition = "DATE")
	private Date elf412_dataDt;

	/**
	 * 最新一次下次恢復覆審日 <br/>
	 * 不覆審代碼註記為不覆審時，可設定下次恢復覆審日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF412_NEXTNWDT", columnDefinition = "DATE")
	private Date elf412_nextNwDt;

	/**
	 * 上次設定之下次恢復覆審日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF412_NEXTLTDT", columnDefinition = "DATE")
	private Date elf412_nextLtDt;

	/**
	 * 外部評等類別 <br/>
	 * 標準普爾 | 1 <br/>
	 * 穆迪信評 | 2 <br/>
	 * 惠譽信評 | 3 <br/>
	 * 中華信評 | 4
	 */
	@Column(name = "ELF412_FCRDTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String elf412_fcrdType;

	/**
	 * 外部評等地區別 <br/>
	 * 國際 | 1 <br/>
	 * 本國 | 2
	 */
	@Column(name = "ELF412_FCRDAREA", length = 1, columnDefinition = "CHAR(1)")
	private String elf412_fcrdArea;

	/**
	 * 外部評等期間別 <br/>
	 * 長期 | 1 <br/>
	 * 短期 | 2
	 */
	@Column(name = "ELF412_FCRDPRED", length = 1, columnDefinition = "CHAR(1)")
	private String elf412_fcrdPred;

	/**
	 * 外部評等等級
	 */
	@Column(name = "ELF412_FCRDGRAD", length = 30, columnDefinition = "CHAR(30)")
	private String elf412_fcrdGrad;

	/**
	 * 是否為實地覆審 J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
	 */
	@Column(name = "ELF412_REALCKFG", length = 1, columnDefinition = "CHAR(1)")
	private String elf412_realCkFg;

	/**
	 * 最近一次實地覆審時間 J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
	 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF412_REALDT", columnDefinition = "DATE")
	private Date elf412_realDt;

	/**
	 * 是否為實地覆審 BK (20170701土建融實地覆審上線時將20161101前覆審辦法聯貸案主辦實地要覆審欄位備份)
	 * J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
	 */
	@Column(name = "ELF412_REALCKFG_BK", length = 1, columnDefinition = "CHAR(1)")
	private String elf412_realCkFg_bk;

	/**
	 * 最近一次實地覆審時間 BK (20170701土建融實地覆審上線時將20161101前覆審辦法聯貸案主辦實地要覆審欄位備份)
	 * J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
	 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF412_REALDT_BK", columnDefinition = "DATE")
	private Date elf412_realDt_bk;

	/**
	 * J-108-0078_05097_B1001
	 * 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。
	 * 
	 * 
	 * 首次往來之新貸戶
	 */
	@Column(name = "ELF412_ISALLNEW", length = 1, columnDefinition = "CHAR(1)")
	private String elf412_isAllNew;

    /**
     * 純紓困戶
     */
    @Column(name = "ELF412_ISRESCUE", length = 1, columnDefinition = "CHAR(1)")
    private String elf412_isRescue;

	/** 信保擔保註記
	 * 2020/04 設定為8成 **/
	@Column(name = "ELF412_GUARFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String elf412_guarFlag;

	/** 新作紓困註記 **/
	@Column(name = "ELF412_NEWRESCUE", length = 1, columnDefinition = "CHAR(1)")
	private String elf412_newRescue;

    /** 新作紓困資料年月 **/
    @Column(name="ELF412_NEWRESCUEYM", length=6, columnDefinition="CHAR(6)")
    private String elf412_newRescueYM;

    /** 抽樣類別 **/
    @Column(name = "ELF412_RANDOMTYPE", length = 1, columnDefinition = "CHAR(1)")
    private String elf412_randomType;

	public String getElf412_branch() {
		return elf412_branch;
	}

	public void setElf412_branch(String elf412_branch) {
		this.elf412_branch = elf412_branch;
	}

	public String getElf412_custId() {
		return elf412_custId;
	}

	public void setElf412_custId(String elf412_custId) {
		this.elf412_custId = elf412_custId;
	}

	public String getElf412_dupNo() {
		return elf412_dupNo;
	}

	public void setElf412_dupNo(String elf412_dupNo) {
		this.elf412_dupNo = elf412_dupNo;
	}

	public String getElf412_mainCust() {
		return elf412_mainCust;
	}

	public void setElf412_mainCust(String elf412_mainCust) {
		this.elf412_mainCust = elf412_mainCust;
	}

	public String getElf412_crdType() {
		return elf412_crdType;
	}

	public void setElf412_crdType(String elf412_crdType) {
		this.elf412_crdType = elf412_crdType;
	}

	public String getElf412_crdtTbl() {
		return elf412_crdtTbl;
	}

	public void setElf412_crdtTbl(String elf412_crdtTbl) {
		this.elf412_crdtTbl = elf412_crdtTbl;
	}

	public String getElf412_mowType() {
		return elf412_mowType;
	}

	public void setElf412_mowType(String elf412_mowType) {
		this.elf412_mowType = elf412_mowType;
	}

	public String getElf412_mowTbl1() {
		return elf412_mowTbl1;
	}

	public void setElf412_mowTbl1(String elf412_mowTbl1) {
		this.elf412_mowTbl1 = elf412_mowTbl1;
	}

	public Date getElf412_llrDate() {
		return elf412_llrDate;
	}

	public void setElf412_llrDate(Date elf412_llrDate) {
		this.elf412_llrDate = elf412_llrDate;
	}

	public Date getElf412_lrDate() {
		return elf412_lrDate;
	}

	public void setElf412_lrDate(Date elf412_lrDate) {
		this.elf412_lrDate = elf412_lrDate;
	}

	public String getElf412_rckdLine() {
		return elf412_rckdLine;
	}

	public void setElf412_rckdLine(String elf412_rckdLine) {
		this.elf412_rckdLine = elf412_rckdLine;
	}

	public String getElf412_ockdLine() {
		return elf412_ockdLine;
	}

	public void setElf412_ockdLine(String elf412_ockdLine) {
		this.elf412_ockdLine = elf412_ockdLine;
	}

	public String getElf412_cState() {
		return elf412_cState;
	}

	public void setElf412_cState(String elf412_cState) {
		this.elf412_cState = elf412_cState;
	}

	public String getElf412_mdFlag() {
		return elf412_mdFlag;
	}

	public void setElf412_mdFlag(String elf412_mdFlag) {
		this.elf412_mdFlag = elf412_mdFlag;
	}

	public Date getElf412_mdDt() {
		return elf412_mdDt;
	}

	public void setElf412_mdDt(Date elf412_mdDt) {
		this.elf412_mdDt = elf412_mdDt;
	}

	public String getElf412_process() {
		return elf412_process;
	}

	public void setElf412_process(String elf412_process) {
		this.elf412_process = elf412_process;
	}

	public String getElf412_newAdd() {
		return elf412_newAdd;
	}

	public void setElf412_newAdd(String elf412_newAdd) {
		this.elf412_newAdd = elf412_newAdd;
	}

	public String getElf412_newDate() {
		return elf412_newDate;
	}

	public void setElf412_newDate(String elf412_newDate) {
		this.elf412_newDate = elf412_newDate;
	}

	public String getElf412_nckdFlag() {
		return elf412_nckdFlag;
	}

	public void setElf412_nckdFlag(String elf412_nckdFlag) {
		this.elf412_nckdFlag = elf412_nckdFlag;
	}

	public Date getElf412_nckdDate() {
		return elf412_nckdDate;
	}

	public void setElf412_nckdDate(Date elf412_nckdDate) {
		this.elf412_nckdDate = elf412_nckdDate;
	}

	public String getElf412_nckdMemo() {
		return elf412_nckdMemo;
	}

	public void setElf412_nckdMemo(String elf412_nckdMemo) {
		this.elf412_nckdMemo = elf412_nckdMemo;
	}

	public Date getElf412_cancelDt() {
		return elf412_cancelDt;
	}

	public void setElf412_cancelDt(Date elf412_cancelDt) {
		this.elf412_cancelDt = elf412_cancelDt;
	}

	public String getElf412_dbuObu() {
		return elf412_dbuObu;
	}

	public void setElf412_dbuObu(String elf412_dbuObu) {
		this.elf412_dbuObu = elf412_dbuObu;
	}

	public String getElf412_dbuCoid() {
		return elf412_dbuCoid;
	}

	public void setElf412_dbuCoid(String elf412_dbuCoid) {
		this.elf412_dbuCoid = elf412_dbuCoid;
	}

	public String getElf412_obuCoid() {
		return elf412_obuCoid;
	}

	public void setElf412_obuCoid(String elf412_obuCoid) {
		this.elf412_obuCoid = elf412_obuCoid;
	}

	public Date getElf412_upddate() {
		return elf412_upddate;
	}

	public void setElf412_upddate(Date elf412_upddate) {
		this.elf412_upddate = elf412_upddate;
	}

	public String getElf412_updater() {
		return elf412_updater;
	}

	public void setElf412_updater(String elf412_updater) {
		this.elf412_updater = elf412_updater;
	}

	public String getElf412_memo() {
		return elf412_memo;
	}

	public void setElf412_memo(String elf412_memo) {
		this.elf412_memo = elf412_memo;
	}

	public Timestamp getElf412_tmestamp() {
		return elf412_tmestamp;
	}

	public void setElf412_tmestamp(Timestamp elf412_tmestamp) {
		this.elf412_tmestamp = elf412_tmestamp;
	}

	public String getElf412_uckdLine() {
		return elf412_uckdLine;
	}

	public void setElf412_uckdLine(String elf412_uckdLine) {
		this.elf412_uckdLine = elf412_uckdLine;
	}

	public Date getElf412_uckdDt() {
		return elf412_uckdDt;
	}

	public void setElf412_uckdDt(Date elf412_uckdDt) {
		this.elf412_uckdDt = elf412_uckdDt;
	}

	public Date getElf412_dataDt() {
		return elf412_dataDt;
	}

	public void setElf412_dataDt(Date elf412_dataDt) {
		this.elf412_dataDt = elf412_dataDt;
	}

	public Date getElf412_nextNwDt() {
		return elf412_nextNwDt;
	}

	public void setElf412_nextNwDt(Date elf412_nextNwDt) {
		this.elf412_nextNwDt = elf412_nextNwDt;
	}

	public Date getElf412_nextLtDt() {
		return elf412_nextLtDt;
	}

	public void setElf412_nextLtDt(Date elf412_nextLtDt) {
		this.elf412_nextLtDt = elf412_nextLtDt;
	}

	public String getElf412_fcrdType() {
		return elf412_fcrdType;
	}

	public void setElf412_fcrdType(String elf412_fcrdType) {
		this.elf412_fcrdType = elf412_fcrdType;
	}

	public String getElf412_fcrdArea() {
		return elf412_fcrdArea;
	}

	public void setElf412_fcrdArea(String elf412_fcrdArea) {
		this.elf412_fcrdArea = elf412_fcrdArea;
	}

	public String getElf412_fcrdPred() {
		return elf412_fcrdPred;
	}

	public void setElf412_fcrdPred(String elf412_fcrdPred) {
		this.elf412_fcrdPred = elf412_fcrdPred;
	}

	public String getElf412_fcrdGrad() {
		return elf412_fcrdGrad;
	}

	public void setElf412_fcrdGrad(String elf412_fcrdGrad) {
		this.elf412_fcrdGrad = elf412_fcrdGrad;
	}

	/** 設定是否為實地覆審 **/
	public void setElf412_realCkFg(String elf412_realCkFg) {
		this.elf412_realCkFg = elf412_realCkFg;
	}

	/** 取得是否為實地覆審 **/
	public String getElf412_realCkFg() {
		return elf412_realCkFg;
	}

	/** 設定最近一次實地覆審時間 **/
	public void setElf412_realDt(Date elf412_realDt) {
		this.elf412_realDt = elf412_realDt;
	}

	/** 取得最近一次實地覆審時間 **/
	public Date getElf412_realDt() {
		return elf412_realDt;
	}

	/** 設定是否為實地覆審 (備份用) **/
	public void setElf412_realCkFg_bk(String elf412_realCkFg_bk) {
		this.elf412_realCkFg_bk = elf412_realCkFg_bk;
	}

	/** 取得是否為實地覆審 (備份用) **/
	public String getElf412_realCkFg_bk() {
		return elf412_realCkFg_bk;
	}

	/** 設定最近一次實地覆審時間 (備份用) **/
	public void setElf412_realDt_bk(Date elf412_realDt_bk) {
		this.elf412_realDt_bk = elf412_realDt_bk;
	}

	/** 取得最近一次實地覆審時間 (備份用) **/
	public Date getElf412_realDt_bk() {
		return elf412_realDt_bk;
	}

	/**
	 * 設定首次往來之新貸戶
	 * 
	 * @param elf412_isAllNew
	 */
	public void setElf412_isAllNew(String elf412_isAllNew) {
		this.elf412_isAllNew = elf412_isAllNew;
	}

	/**
	 * 取得首次往來之新貸戶
	 * 
	 * @return
	 */
	public String getElf412_isAllNew() {
		return elf412_isAllNew;
	}

    /**
     * 設定純紓困戶
     */
    public void setElf412_isRescue(String elf412_isRescue) {
        this.elf412_isRescue = elf412_isRescue;
    }

    /**
     * 取得純紓困戶
     */
    public String getElf412_isRescue() {
        return elf412_isRescue;
    }

	/**
	 * 設定信保擔保註記(2020/04 設定為8成)
	 */
	public String getElf412_guarFlag() {
		return elf412_guarFlag;
	}

	/**
	 * 取得信保擔保註記(2020/04 設定為8成)
	 */
	public void setElf412_guarFlag(String elf412_guarFlag) {
		this.elf412_guarFlag = elf412_guarFlag;
	}

	/**
	 * 設定新作紓困註記
	 */
	public String getElf412_newRescue() {
		return elf412_newRescue;
	}

	/**
	 * 取得新作紓困註記
	 */
	public void setElf412_newRescue(String elf412_newRescue) {
		this.elf412_newRescue = elf412_newRescue;
	}

    /**
     * 設定新作紓困資料年月
     */
    public void setElf412_newRescueYM(String elf412_newRescueYM) {
        this.elf412_newRescueYM = elf412_newRescueYM;
    }

    /**
     * 取得新作紓困資料年月
     */
    public String getElf412_newRescueYM() {
        return elf412_newRescueYM;
    }

    /**
     * 設定抽樣類別
     */
    public String getElf412_randomType() {
        return elf412_randomType;
    }

    /**
     * 取得抽樣類別
     */
    public void setElf412_randomType(String elf412_randomType) {
        this.elf412_randomType = elf412_randomType;
    }
}
