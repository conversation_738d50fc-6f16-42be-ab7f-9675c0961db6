package com.mega.eloan.lms.fms.handler.form;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.service.UserInfoService.SignEnum;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.fms.pages.LMS7700M01Page;
import com.mega.eloan.lms.fms.service.LMS7700Service;
import com.mega.eloan.lms.model.L140MM5A;
import com.mega.eloan.lms.model.L140MM5B;
import com.mega.eloan.lms.model.L140MM5C;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.core.FlowException;

/**
 * <pre>
 * 電子文件維護作業
 * </pre>
 * 
 * @since 2019
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Scope("request")
@Controller("lms7700m01formhandler")
@DomainClass(L140MM5A.class)
public class LMS7700M01FormHandler extends AbstractFormHandler {
	private static final Logger logger = LoggerFactory.getLogger(LMS7700M01FormHandler.class);
	
	@Resource
	LMS7700Service lms7700Service;
	
	@Resource
	BranchService branchService;
	
	@Resource
	LMSService lmsService;
	
	@Resource
	UserInfoService userInfoService;
	
	@Resource
	EloandbBASEService eloandbService;
	
	Properties pop = MessageBundleScriptCreator.getComponentResource(LMS7700M01Page.class);
	MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
	
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult newl140mm5a(PageParameters params)
			throws CapException {
		String mainId = "";
		L140MM5A l140mm5a = new L140MM5A();
		l140mm5a.setDocStatus(CreditDocStatusEnum.海外_編製中.getCode());
		l140mm5a.setOwnBrId(user.getUnitNo());

		mainId = IDGenerator.getUUID();

		l140mm5a.setMainId(mainId);
		String txCode = Util.trim(params
				.getString(EloanConstants.TRANSACTION_CODE));
		l140mm5a.setTxCode(txCode);
		// UPGRADE: 待確認，URL是否正確
		l140mm5a.setDocURL(params.getString("docUrl"));
		l140mm5a.setDeletedTime(CapDate.getCurrentTimestamp());
		l140mm5a.setDataYM(params.getString("dataYM"));
		l140mm5a.setCnt(0);	
		
		lms7700Service.save(l140mm5a);

		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set(EloanConstants.OID, l140mm5a.getOid());
		result.set(EloanConstants.MAIN_ID, l140mm5a.getMainId());
		return result;
	}
	
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryLMS7700M01(PageParameters params)
			throws CapException {
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, String> dataMap = new HashMap<String, String>();

		List<L140MM5C> data = lms7700Service.findL140mm5csByMainId(mainId);
		if(data == null || data.isEmpty()){
			if (!Util.isEmpty(oid)) {
				L140MM5A l140mm5a = lms7700Service.findModelByOid(L140MM5A.class, oid);
				//第一次啟案自動塞入前案資訊
				result = formatResultShow(result, l140mm5a, page);
			} else {
				// 開啟新案帶入起案的分行和目前文件狀態
				result.set(
						"docStatus",
						this.getMessage("docStatus."
								+ CreditDocStatusEnum.海外_編製中.getCode()));
				result.set("ownBrId", user.getUnitNo());
				result.set(
						"ownBrName",
						StrUtils.concat(" ",
								branchService.getBranchName(user.getUnitNo())));
				result.set("docStatusVal", CreditDocStatusEnum.海外_編製中.getCode());
			}
		} else {
			if (!Util.isEmpty(oid)) {
				L140MM5A l140mm5a = lms7700Service.findModelByOid(L140MM5A.class, oid);
				dataMap.put("mainId", l140mm5a.getMainId());				
				result.putAll(dataMap);
				result = formatResultShow(result, l140mm5a, page);
			}
		}
		
		return result;
	}
	
	/**
	 * 格式化顯示訊息
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	private CapAjaxFormResult formatResultShow(CapAjaxFormResult result,
			L140MM5A l140mm5a, Integer page) throws CapException {
		String mainId = l140mm5a.getMainId();
		
		switch (page) {
		case 1:
			result = DataParse.toResult(l140mm5a);
			List<L140MM5B> l140mm5bList = (List<L140MM5B>) lms7700Service
					.findListByMainId(L140MM5B.class, mainId);
			if (!Util.isEmpty(l140mm5bList)) {
				// 取得人員職稱 L1. 分行經辦 L3. 分行授信主管 L4. 分行覆核主管 L5. 經副襄理L6. 總行經辦
				// L7.總行主管
				StringBuilder bossId = new StringBuilder("");
				for (L140MM5B l140mm5b : l140mm5bList) {
					// 要加上人員代碼
					String type = Util.trim(l140mm5b.getStaffJob());
					String userId = Util.trim(l140mm5b.getStaffNo());
					String value = Util.trim(lmsService.getUserName(userId));
					if ("L1".equals(type)) {
						result.set("showApprId", userId + " " + value);
					} else if ("L3".equals(type)) {
						bossId.append(bossId.length() > 0 ? "<br/>" : "");
						bossId.append(userId);
						bossId.append(" ");
						bossId.append(value);
					} else if ("L4".equals(type)) {
						result.set("reCheckId", userId + " " + value);
					} else if ("L5".equals(type)) {
						result.set("managerId", userId + " " + value);
					} else if ("L6".equals(type)) {
						result.set("mainApprId", userId + " " + value);
					} else if ("L7".equals(type)) {
						result.set("mainReCheckId", userId + " " + value);
					}
				}
				result.set("bossId", bossId.toString());
			}
			result.set("ownBrName",
					" " + branchService.getBranchName(l140mm5a.getOwnBrId()));
			result.set("creator", lmsService.getUserName(l140mm5a.getCreator()));
			result.set("updater", lmsService.getUserName(l140mm5a.getUpdater()));
			result.set("docStatus",
					getMessage("docStatus." + l140mm5a.getDocStatus()));
			break;
		}// close switch case

		result.set("docStatusVal", l140mm5a.getDocStatus());
		result.set(EloanConstants.OID, CapString.trimNull(l140mm5a.getOid()));
		result.set(EloanConstants.MAIN_OID,	CapString.trimNull(l140mm5a.getOid()));
		result.set(EloanConstants.MAIN_ID, CapString.trimNull(l140mm5a.getMainId()));

		return result;
	}
	
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL140mm5a(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, params.getString("tempSave", "N"));
		CapAjaxFormResult result = new CapAjaxFormResult();
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		String oid = Util.trim(params.getString(EloanConstants.OID));

		String form = Util.trim(params.getString("mainPanel"));
		JSONObject jsonData = null;

		L140MM5A l140mm5a = null;
		Boolean showMsg = params.getAsBoolean("showMsg", false);
		String showMsg1 = "";
		if (Util.isNotEmpty(oid)) {
			l140mm5a = lms7700Service.findModelByOid(L140MM5A.class, oid);
			l140mm5a.setRandomCode(IDGenerator.getRandomCode());
		}

		l140mm5a.setDeletedTime(null);

		String validate = null;
		switch (page) {
		case 1:
			jsonData = JSONObject.fromObject(form);
			DataParse.toBean(jsonData, l140mm5a);
			validate = Util.validateColumnSize(l140mm5a, pop, "L140MM5A");
			if (validate != null) {
				Map<String, String> param = new HashMap<String, String>();
				param.put("colName", validate);
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
			}

			lms7700Service.save(l140mm5a);
			result.set("randomCode", l140mm5a.getRandomCode());
			break;
		}

		showMsg1 = this.checkSaveData(l140mm5a, "sendBoss");
		
		if (Util.isEmpty(showMsg1)) {
			if (showMsg) {
				showMsg1 = RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功);
			}
		} else {
			if (showMsg) {

			}else{
				throw new CapMessageException(showMsg1,	getClass());
			}
		}
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, showMsg1);

		result.set(EloanConstants.OID, CapString.trimNull(l140mm5a.getOid()));
		result.set(EloanConstants.MAIN_OID,	CapString.trimNull(l140mm5a.getOid()));
		result.set(EloanConstants.MAIN_ID, CapString.trimNull(l140mm5a.getMainId()));
		return result;
	}
	
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult maintainL140mm5c(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String isDelete = Util.trim(params.getString("isDelete",""));
		String reason = Util.trim(params.getString("reason",""));
		String[] oids = params.getStringArray("oids");
		if (oids.length > 0) {
			List<L140MM5C> l140mm5cs = new ArrayList<L140MM5C>();
			for (int i = 0, size = oids.length; i < size; i++) {
				L140MM5C l140mm5c = lms7700Service.findModelByOid(
						L140MM5C.class, oids[i]);
				l140mm5c.setIsDelete(isDelete);
				l140mm5c.setReason(reason);
				l140mm5c.setUpdater(user.getUserId());
				l140mm5c.setUpdateTime(CapDate.getCurrentTimestamp());
				l140mm5cs.add(l140mm5c);
			}
			if (!l140mm5cs.isEmpty()) {
				lms7700Service.saveL140MM5Cs(l140mm5cs);
			}
		}
		return result;
	}
	
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteL140mm5c(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] oids = params.getStringArray("oids");
		String oid = Util.trim(params.getString(EloanConstants.OID));
		if (oids.length > 0) {
			if (lms7700Service.deleteL140mm5cs(oids)) {
				this.updL140mm5aCnt(oid);
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
						.getMainMessage(UtilConstants.AJAX_RSP_MSG.刪除成功));
			}
		}
		return result;
	}
	
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult addL140mm5c(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String oid = Util.trim(params.getString(EloanConstants.OID));
		String custId = Util.trim(params.getString("custId",""));
		String dupNo = Util.trim(params.getString("dupNo",""));
		String custName = Util.trim(params.getString("custName",""));
		String sysList = Util.trim(params.getString("sys",""));
		String closeDate = Util.trim(params.getString("closeDate",""));
		
		String CES = "";
		String CMS = "";
		String COL = "";
		String LMS = "";
		String RPS = "";
		
		for(String propStr : StringUtils.split(Util.trim(sysList), "|")){
			String sName = Util.trim(propStr);
			if(Util.equals("CES", sName)){
				CES = "Y";
			} else if(Util.equals("CMS", sName)){
				CMS = "Y";
			} else if(Util.equals("COL", sName)){
				COL = "Y";
			} else if(Util.equals("LMS", sName)){
				LMS = "Y";
			} else if(Util.equals("RPS", sName)){
				RPS = "Y";
			}  
		}
		
		try {
			if(Util.isNotEmpty(closeDate)){
				closeDate = getDateFormat(closeDate);
				if(closeDate == null){
					throw new CapMessageException(pop.getProperty("L140MM5A.closeDate") + 
							pop.getProperty("notMatchFormat"),	getClass());
				}
			}
		} catch (Exception e){
			throw new CapMessageException(pop.getProperty("L140MM5A.closeDate") + 
					pop.getProperty("notMatchFormat"),	getClass());
		}
		
		L140MM5C l140mm5c = new L140MM5C();
		l140mm5c.setMainId(mainId);
		l140mm5c.setOwnBrId(user.getUnitNo());
		l140mm5c.setCustId(custId);
		l140mm5c.setDupNo(dupNo);
		l140mm5c.setCustName(custName);
		l140mm5c.setIsDelete("Y");
		l140mm5c.setReason("");
		l140mm5c.setDataFrom("2");	//1:系統 2:人工
		l140mm5c.setCloseDate(Util.isNotEmpty(closeDate) ? 
				CapDate.parseDate(closeDate) : null);
		l140mm5c.setCreator(user.getUserId());
		l140mm5c.setCreateTime(CapDate.getCurrentTimestamp());
		l140mm5c.setUpdater(user.getUserId());
		l140mm5c.setUpdateTime(CapDate.getCurrentTimestamp());
		
		l140mm5c.setCes(CES);
		l140mm5c.setCms(CMS);
		l140mm5c.setCol(COL);
		l140mm5c.setLms(LMS);
		l140mm5c.setRps(RPS);
		lms7700Service.save(l140mm5c);
		this.updL140mm5aCnt(oid);
		
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));
		return result;
	}
	
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public void updL140mm5aCnt(String oid) throws CapException {
		lms7700Service.updL140mm5aCnt(oid);
	}
	
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult checkData(PageParameters params)
			throws CapException {
		// 儲存and檢核
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 查詢所選銀行的甲級主管、乙級主管清單
		SignEnum[] signs = { SignEnum.首長, SignEnum.單位主管, SignEnum.甲級主管,
				SignEnum.乙級主管 };
		Map<String, String> bossList = userInfoService.findByBrnoAndSignId(
				user.getUnitNo(), signs);
		result.set("bossList", new CapAjaxFormResult(bossList));
		return result;

	}
	
	 /*** 呈主管覆核(呈主管 主管覆核 拆2個method) */
	@SuppressWarnings({ "unchecked" })
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult flowAction(PageParameters params)
			throws CapException {
		// 儲存and檢核
		String oid = params.getString(EloanConstants.MAIN_OID);
		L140MM5A l140mm5a = (L140MM5A) lms7700Service.findModelByOid(L140MM5A.class, oid);
		String[] formSelectBoss = params.getStringArray("selectBoss");

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		if (!Util.isEmpty(formSelectBoss)) {

			String manager = Util.trim(params.getString("manager"));
			List<L140MM5B> models = (List<L140MM5B>) lms7700Service
					.findListByMainId(L140MM5B.class, l140mm5a.getMainId());
			if (!models.isEmpty()) {
				lms7700Service.deleteL140mm5bs(models, false);
			}
			List<L140MM5B> l140mm5bs = new ArrayList<L140MM5B>();
			for (String people : formSelectBoss) {
				L140MM5B l140mm5b = new L140MM5B();
				l140mm5b.setCreator(user.getUserId());
				l140mm5b.setCreateTime(CapDate.getCurrentTimestamp());
				l140mm5b.setMainId(l140mm5a.getMainId());
				l140mm5b.setBranchType(user.getUnitType());
				l140mm5b.setBranchId(user.getUnitNo());
				// L1. 分行經辦 L3. 分行授信主管 L4. 分行覆核主管 L5. 經副襄理
				l140mm5b.setStaffJob(UtilConstants.STAFFJOB.授信主管L3);
				l140mm5b.setStaffNo(people);
				l140mm5bs.add(l140mm5b);
			}
			L140MM5B managerL140mm5b = new L140MM5B();
			managerL140mm5b.setCreator(user.getUserId());
			managerL140mm5b.setCreateTime(CapDate.getCurrentTimestamp());
			managerL140mm5b.setMainId(l140mm5a.getMainId());
			managerL140mm5b.setStaffJob(UtilConstants.STAFFJOB.單位授權主管L5);
			managerL140mm5b.setStaffNo(manager);
			managerL140mm5b.setBranchType(user.getUnitType());
			managerL140mm5b.setBranchId(user.getUnitNo());
			l140mm5bs.add(managerL140mm5b);
			L140MM5B apprL140mm5b = new L140MM5B();
			apprL140mm5b.setCreator(user.getUserId());
			apprL140mm5b.setCreateTime(CapDate.getCurrentTimestamp());
			apprL140mm5b.setMainId(l140mm5a.getMainId());
			apprL140mm5b.setStaffJob(UtilConstants.STAFFJOB.經辦L1);
			apprL140mm5b.setStaffNo(user.getUserId());
			apprL140mm5b.setBranchType(user.getUnitType());
			apprL140mm5b.setBranchId(user.getUnitNo());
			l140mm5bs.add(apprL140mm5b);
			lms7700Service.saveL140mm5bList(l140mm5bs);
		}
		Boolean upCom = false;
		L140MM5B l140mm5bL4 = new L140MM5B();
		// 如果有這個key值表示是輸入chekDate核准日期
		if (params.containsKey("checkDate")) {
			l140mm5a.setApprover(user.getUserId());
			l140mm5a.setApproveTime(CapDate.getCurrentTimestamp());
			upCom = true;
			L140MM5B l140mm5b = lms7700Service.findL140mm5b(
					l140mm5a.getMainId(), user.getUnitType(), user.getUnitNo(),
					user.getUserId(), UtilConstants.STAFFJOB.執行覆核主管L4);
			if (l140mm5b == null) {
				l140mm5b = new L140MM5B();
				l140mm5b.setCreator(user.getUserId());
				l140mm5b.setCreateTime(CapDate.getCurrentTimestamp());
				l140mm5b.setMainId(l140mm5a.getMainId());
				l140mm5b.setStaffJob(UtilConstants.STAFFJOB.執行覆核主管L4);
				l140mm5b.setStaffNo(user.getUserId());
				l140mm5b.setBranchType(user.getUnitType());
				l140mm5b.setBranchId(user.getUnitNo());
			}
			l140mm5bL4 = l140mm5b;
		}

		if (!Util.isEmpty(l140mm5a)) {
			try {
				// 如果有這值表示非呈主管，要檢查覆核主管和文件最後更新者是否相同
				if (params.containsKey("flowAction")) {
					// 退回部檢查
					if (params.getBoolean("flowAction")) {
						L140MM5B l140mm5b = lms7700Service.findL140mm5b(
								l140mm5a.getMainId(), user.getUnitType(),
								user.getUnitNo(), user.getUserId(),
								UtilConstants.STAFFJOB.經辦L1);

						if (l140mm5b != null) {
							// EFD0053=WARN|覆核人員不可與「經辦人員或其它覆核人員」為同一人|
							throw new CapMessageException(RespMsgHelper.getMessage("EFD0053"), getClass());
						} else {
							lms7700Service.save(l140mm5bL4);
							upCom = true;
						}
					}
				}
				lms7700Service.flowAction(l140mm5a.getOid(), l140mm5a,
						params.containsKey("flowAction"),
						params.getAsBoolean("flowAction", false), upCom);
			} catch (FlowException t1) {
				logger.error(
						"[flowAction] lms7700Service.flowAction FlowException!!",
						t1);
				throw new CapMessageException(RespMsgHelper.getMessage(t1.getMessage()), getClass());
			} catch (Throwable t1) {
				logger.error(
						"[flowAction]  lms7700Service.flowAction EXCEPTION!!",
						t1);
				throw new CapMessageException(t1.getMessage(), getClass());
			}
		}

		return new CapAjaxFormResult();
	}

	public String checkSaveData(L140MM5A l140mm5a, String flowaAtion)
			throws CapException {
		String msg = "";
		
		List<L140MM5C> l140mm5cs = lms7700Service.findL140mm5csByMainId(l140mm5a.getMainId());
		
		if(Util.equals("sendBoss", flowaAtion)){
			if(l140mm5cs.isEmpty() || l140mm5cs == null){
				
			} else {
				for(L140MM5C l140mm5c : l140mm5cs){
					if(Util.notEquals("Y", l140mm5c.getIsDelete()) && 
							Util.notEquals("N", l140mm5c.getIsDelete())){
						//message02=尚有名單未註記是否刪除
						msg = pop.getProperty("message02");
					}
				}
			}
		}
		
		return msg;
	}
	
	public IResult echo_custId(PageParameters params)
			throws CapException {
		// 儲存and檢核
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		result.set("custId", Util.trim(params.getString("custId")));
		result.set("dupNo", Util.trim(params.getString("dupNo")));
		result.set("custName", Util.trim(params.getString("custName")));
		return result;
	}
	
	public IResult queryElData(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String brId = user.getUnitNo();
		
		List<Map<String, Object>> elList = new ArrayList<Map<String, Object>>();
		elList = eloandbService.doLmsBatch0019(custId, dupNo, brId);
		StringBuilder sysList = new StringBuilder("");
		if(elList != null && Util.isNotEmpty(elList) && elList.size()>0){
			for (Map<String, Object> elMap : elList) {
				String sys = elMap.get("SYS_SCHEMA").toString();
				if(Util.notEquals("0", elMap.get("CNT"))){
					if(Util.notEquals("CMH", sys) && Util.notEquals("CMS", sys)){
						if(sysList == null || sysList.toString().equals("")){
							sysList.append(sys);
						} else {
							sysList.append("|").append(sys);
						}
					} else {	// CMH 算 CMS 底下
						if(sysList == null || sysList.toString().equals("")){
							sysList.append("CMS");
						} else {
							if(sysList.indexOf("CMS")!=-1){
								// 已經有CMS
							} else {
								sysList.append("|").append("CMS");
							}
						}
					}
				}
			}
		}
		result.set("sysList", sysList.toString());
		return result;
	}
	
	public String getDateFormat(String DATE){
		SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd"); 
		String dateFormat=DATE;
		if(Util.isNotEmpty(DATE)){
			try {
				Date df = sdf.parse(DATE);
				dateFormat = CapDate.formatDate(df, "yyyy-MM-dd");
			} catch (ParseException e) {
//				logger.error(e.getMessage(), e);
				logger.error(StrUtils.getStackTrace(e));
			} 
		}
		return dateFormat;
	}
}
