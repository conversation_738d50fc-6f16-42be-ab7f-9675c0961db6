#panel
doc.title01=Personal Application Credit Rating Model
doc.title02=Thailand
doc.title03=Asia Areas
#J-112-0242\u8abf\u6574
#title.clsRating=According to Article 3 of \u201cOperation Directions for Personal Banking Credit Rating\u201d of Our bank, \u201dprincipal borrower, joint guarantor and co-borrower of a case shall all be given a credit rating, one of which shall be selected as credit rating for the case.\u201d
title.clsRating=According to Article 3 of \u201cApplication Rules for Credit Rating of Personal Banking Loan Business\u201d of Our bank, \u201dprincipal borrower, joint guarantor and co-borrower of a case shall all be given a credit rating, one of which shall be selected as credit rating for the case.\u201d
tab.01=Document Info.
tab.02=Case Related Parties
tab.03=Case Related Parties\u2019 Total
tab.04=Collateral Info.
tab.05=NCB Credit Report Info.
tab.06=Override
tab.07=Credit Grade

label.caseBorrower=Related Party 
label.custId=MEGA ID
label.custName=Name
label.custPos=Related Identity
label.noticeItem=Note

print.rptName=Report Name
printItem1=Basic Information
printItem2=Rating Information
#panel-1
tab01.lnPeriod=Loan Tenor
tab01.lnYear=year(s)
tab01.lnMonth=month(s)
tab01.lnYearMonth={0}year{1}month
tab01.days=days
tab01.repaymentSch=Repayment Schedule
tab01.repaymentSchFmt=Loan tenor is equal to tenor(Repayment Schedule) of Credit Facility Report
tab01.repaymentSchDays.invalid=Repayment Schedule should not longer than Loan Tenor
#panel-2
tab02.btnImport=Import
tab02.btnSetCustPosCustRlt=Change Related Identity  
tab02.desc01=\u203bPlease update rating and import Credit Facility Report again if there is any change in the Basic Information Sheet.
tab02.desc02=\u203bM.Borrower, C.Co-borrower, G.Joint guarantor\u3002

#panel-3
tab03.annualIncome=Annual Income
tab03.sumDiv=Total income of the borrower and joint guarantor.
tab03.sumDiv.relevant=Related Parties
tab03.sumDiv.annualIncome=Total Annual Income

#panel-4
#\u70ba\u4e86\u8b93\u984d\u5ea6\u660e\u7d30\u8868\u4e5f\u5448\u73fe\u300c\u64d4\u4fdd\u54c1\u300d\uff0c\u628a property \u4e0b\u653e\u5230 panel \u5c64

#panel-5
tab05.desc01=Rating summary and suggested Standalone Rating
tab05.desc02=NCB Credit Report information is considered in the first model overlay. The credit assessment staff should input a relevant\u2019s accurate information into Basic Information -> Relevant Information based on NCB Credit Report and the system will automatically decide if Warning Signals are triggered or not. Warning Signals include General Warning Signals and Specific Warning Signals. Once <u>General Warning Signals(GWS)</u> are triggered, the related party is considered to have a higher probability of default(PD). The Cumulative Risk Points reflect the severity of GWS and first downgrading criterion is applied with the total of the Cumulative Risk Points. If the <u>Specific Warning Signal(SWS)</u> is triggered, the revelant is strongly inclined to default imminently. Once  SWS is triggered, an upper limit will be set to the credit grade (i.e. rating cap.) Also, should the Principal Borrower does not offer NCB credit report consent form (or does not offer NCB Credit Report), his/her Standalone Rating shall be set to: Preliminary Rating downgraded by 2 levels, or Grade Level 9, whichever is worse. Downgrading criteria are as follows.
tab05.desc03=Downgrade Logic
#tab05.desc04=None
#tab05.desc05=Downgrade 1
#tab05.desc06=Downgrade 2
#tab05.desc07=Downgrade 3
tab05.desc08=Note\uff1aIf a related party\u2019s NCB Credit Report is not available, General Warning Signal and Special Warning Signal triggers are marked N.A. If he/she is principal borrower, Standalone Rating(SR) is set as: Preliminary Rating downgraded by 2 levels or Grade Level 9, whichever is worse. If he/she is not principal borrower, there is no adjustment of rating here thus Standalone Rating is the same as Preliminary Rating.
tab05.desc09=No.	
#tab05.desc10=\u9805\u6b21\u5167\u5bb9	
tab05.desc11=Trigger or not
tab05.desc12=Points
tab05.desc13=Accumulated points
tab05.desc14=Downgraded automatically (if applicable) considering NCB Credit Report 
#tab05.desc15=General Warning Signals Description
#tab05.desc16=Special Warning Signals Description
#tab05.desc17=Other Information
#tab05.desc18=Upgrade Logic
#tab05.desc19=Upgrade 1
#tab05.desc20=Trigger other information
tab05.descG=General Warning Signals(GWS)
tab05.descG.logicA=None
tab05.descG.logicB=1 level
tab05.descG.logicC=2 levels
tab05.descS=Special Warning Signal(SWS)
tab05.descS.logic=Rating Cap\uff1aGrade Level 9
tab05.descO=Whether the Principal Borrower does not offer NCB Credit Report consent form (or does not offer NCB Credit Report)
tab05.descO.logic=Preliminary Rating downgraded by 2 levels or Grade Level 9, whichever is worse.
tab05.subItem=Item
tab05.mortgage=Mortgage
tab05.non-mortgage=non-Mortgage

#panel-6
tab06.desc01=Before determining the final rating\uff0e\uff0e\uff0e\uff0e\uff0e\uff0e please think over if following factors are taken into consideration.
tab06.desc02=Summary of rating output
tab06.desc03=Whether Standalone Rating(SR), which has included NCB Credit Report information, is consistent with your professional judgement?<br/>&nbsp;
tab06.desc04=Note: if you select Yes, override is not applied and further explanation is not required.
tab06.desc05=Override
tab06.desc06=Upgrade
tab06.desc07=Downgrade
tab06.desc08=level(s)
tab06.desc09=Override for upgrade is limited to a maximum of 2 levels. Please clearly explain the reason of override, so that the rating results and effectiveness are traceable and complied with the regulations of Financial Supervisory Commission, Republic of China.
tab06.desc10=\u203bNote: If an override is applied, please select a main reason listed below and describe in detail.
tab06.desc11=Upgrade reason\u3010Net Asset\u3011
tab06.desc12=(Please describe precisely with specific figures, Such as yearly(monthly) income, deposit amount, rental income, other assets, or debit ratio, etc.)
tab06.desc13=Upgrade reason\u3010Occupation\u3011
tab06.desc14=(Please describe clearly, Such as job title, employer company size, occupational categories, seniority, directorship, job prospect, etc)
tab06.desc15=Upgrade reason\u3010Others\u3011
tab06.desc16=(Such as relationship with banks, general comments on customers, or other reasons not belonging to the first two)
tab06.desc17=The rating is selected from borrower or co-borrower with one of these grades. Thus, borrower or co-borrower with well credit cannot be the reason for upgrade.
tab06.desc17.v2=As per Article 3 of \u201cOperation Directions for Personal Banking Credit Rating\u201dof Our bank, Borrower, Joint Guarantor or Co-borrower (if available) shall be graded seperately and one of these grades will be selected as credit rating of the Personal Banking Case Report. Moreover, to avoid circumstances such as the Borrower\u2019s rating is upgraded due to well credit worthiness of the Joint Guarantor/Co-borrower therefore the Borrower\u2019s final rating becomes even better than Joint Guarantor/Co-borrower\u2019s final rating, please note that it is forbidden to upgrade a related party\u2019s credit rating with the reason of well credit of the Borrower, Joint Guarantor, or Co-borrower other than himself/herself.
tab06.desc18=The rating can\u2019t be upgraded for the reason of \u201cwell credit of guarantee\u201d, \u201cwith insurance projet\u201d, \u201clocation\u201d or \u201dguarantee rate of the collateral.\u201d

#panel-7
tab07.note.sRating=After NCB Credit Report Info.
tab07.note.fRating=After Override
tab07.note.hasNcb=NCB Credit Report Record
tab07.note.hasNcb.Y=Exist
tab07.note.hasNcb.N=None
tab07.note.chkItemInfoNcb=NCB Credit Report Info.
tab07.note.chkItemInfoNcb.O.Y=The Principal Borrower does not offer NCB Credit Report consent form/NCB Credit Report
tab07.note.chkItemInfoNcb.noWarnMsg=Warning Signals(GWS & SWS): None.
tab07.note.overrideReason=Override Reason
tab07.mortgage=Mortgage
tab07.non-mortgage=non-Mortgage

#msg(extend)
l120m01a.error24=$\{colName\}Borrower can not leave blank!
l120s01a.custid=Unified Business Number
l120s01a.custname=Name
l120s01a.custrlt=Relationship With The Principal Borrower
l120s01a.custpos=Related Identity
l120s01a.other16=UBN input Category Description: <br/>Natural persons-<br/>(1)&nbsp;Taiwan ID-&nbsp;\u24d0Taiwan ID No.(Ex:A123456789)<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\u24d1Ministry of Interior UBN(Ex: GC00367938)<br/>(2) Non Taiwan ID-AD date of birth + The first two characters in the English name(Ex:20120202DU)<br/><br/>Non-Natural persons -<br/>(1)&nbsp;Taiwan ID-&nbsp;\u24d0Number issued by Ministry of Economic Affairs/Revenue Service Office (Ex:********)<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\u24d1In the Bank Coding Mega&nbsp;Id(Ex:AUZ0034022)<br/>(2)&nbsp;Non Taiwan ID-Query by English company before take a number
l120s02.other13=Relationship Category
l120s02.alert26=UBN, Name Select Alternative input or import
l120s02.alert27=Please input UBN or Name!
#msg
msg.001={0} not finish Final Rating
msg.002=Override upgrade is maximum two level.
msg.003=
msg.004=Incomplete\uff1a{0}
msg.005=if Supported Rating is DEFAULT, can not Upgrade
msg.006=[Please switch between related parties by using the dropdown list menu]
msg.007={0} after {1} should between Grade 1~10
#fmt
fmt.upgrade=Upgrade {0} Level
fmt.downgrade=downgraded {0} level(s) 
fmt.notchange=downgraded (None) level(s)
fmt.sws.Y=Rating Cap: 9  
fmt.sws.N=Rating Cap: None
fmt.o.Y=Check Result: Preliminary Rating downgraded by 2 levels or Grade Level 9, whichever is worse: Grade {0}
fmt.o.N=Check Result: No adjustment.
#========================
#C121M01A
C121M01A.custId=Principal Borrower\u2019s Mega ID(UBN) 
C121M01A.custName=Principal Borrower
C121M01A.ratingDate=Rating Date
C121M01A.caseNo=Rating Doc. No.
C121M01A.varVer=Rating Version

#C121M01D
C121M01D.pRating=Preliminary Rating
C121M01D.sRating=Standalone Rating
C121M01D.fRating=Final Rating
C121M01D.chkItemTHG1=Has account(s) with a Maximum DPD in 30 days
C121M01D.chkItemTHG2=Has account(s) with a Maximum DPD in 31-90 days
C121M01D.chkItemTHS1=Has account(s) 90 days past due
C121M01D.chkItemTHO1=Is Principal Borrower?
C121M01D.chkItemTHO2=NCB Credit Report Available?
C121M01D.chkItemTHO3=The related party is the Principal borrower and meets the circumstance listed above
C121M01D.adjustFlag.1=Net Asset
C121M01D.adjustFlag.2=Occupation
C121M01D.adjustFlag.3=Others

#C121S01A
C121S01A.cmsType=Type of Collateral
C121S01A.location=Location
C121S01A.region=\u5ea7\u843d\u5730\u5340
C121S01A.houseAge=Age of House
C121S01A.houseArea=Construction Area
C121S01A.securityRate=Security Ratio
#\u5831\u8868\u5448\u73fe\u6642\uff0c\u4e2d\u6587\u4e0d\u7528\u52a0\u7a7a\u767d\uff0c\u4f46\u82f1\u6587\u9700\u8981
report.snrDesc_year={0} Years
report.snrDesc_year_month={0} Years {1} Months