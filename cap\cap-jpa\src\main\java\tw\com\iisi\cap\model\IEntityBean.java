/**
 * IEntityBean.java
 *
 * Copyright (c) 2009 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
*/
package tw.com.iisi.cap.model;

/**
 * <pre>
 * IEntityBean.
 * 實體設置
 *  
 * $Date: 2010-08-03 17:38:52 +0800 (星期二, 03 八月 2010) $
 * $Author: iris $
 * $Revision: 26 $
 * $HeadURL: svn://***********/MICB_ISDOC/cap/cap-core/src/main/java/tw/com/iisi/cap/model/IEntityBean.java $
 * </pre>
 *
 * <AUTHOR>
 * @version $Revision: 26 $
 * @version
 *          <ul>
 *          <li>2010/7/30,iristu,new
 *          </ul>
 */
public interface IEntityBean extends IDataObject {

    /**
     * 取得實體名
     * 
     * @return
     */
    public String getEntityName();

    /**
     * 設置實體名
     * 
     * @param entityName
     *            實體名
     * @return
     */
    public IEntityBean setEntityName(String entityName);

}
