/* 
 * L186M01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L186M01A;

/** 覆審抽樣資料記錄檔 **/
public interface L186M01ADao extends IGenericDao<L186M01A> {

	L186M01A findByOid(String oid);
	
	L186M01A findByUniqueKey(Date dataDate, String branchId, String custId, String dupNo, String randomType);

	List<L186M01A> findByBrAndDateAndType(Date dataDate, String branchId, String randomType);
}