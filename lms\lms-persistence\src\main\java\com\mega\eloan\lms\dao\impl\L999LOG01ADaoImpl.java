/* 
 * L999LOG01ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L999LOG01ADao;
import com.mega.eloan.lms.model.L999LOG01A;

/** 額度進階查詢記錄檔 **/
@Repository
public class L999LOG01ADaoImpl extends LMSJpaDao<L999LOG01A, String> implements
		L999LOG01ADao {

	@Override
	public L999LOG01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public L999LOG01A findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L999LOG01A> findByCreatorAndItemType(String creator,
			String itemType) {
		ISearch search = createSearchTemplete();
		List<L999LOG01A> list = null;
		if (creator != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "creator",
					creator);
		if (itemType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "itemType",
					itemType);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public L999LOG01A findLatestByCreatorAndItemType(String creator,
			String itemType) {
		ISearch search = createSearchTemplete();
		List<L999LOG01A> list = null;
		if (creator != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "creator",
					creator);
		if (itemType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "itemType",
					itemType);
		search.addOrderBy("createTime", true);
		search.setMaxResults(1);
		return findUniqueOrNone(search);
	}
}