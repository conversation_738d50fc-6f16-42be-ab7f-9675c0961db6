package com.mega.eloan.lms.crs.panels;

import com.mega.eloan.common.panels.Panel;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.DocLogPanel;

public class LMS2401S01Panel extends Panel {
	
	private static final long serialVersionUID = 1L;

	public LMS2401S01Panel(String id) {
		super(id);
	}
	
	public LMS2401S01Panel(String id, boolean updatePanelName) {
		super(id, updatePanelName);
	}
	
	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);
		new DocLogPanel("_docLog").processPanelData(model, getPageParameters());
	}
}
