/* 
 * LMS1205Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.ctr.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.model.C999A01A;
import com.mega.eloan.lms.model.C999M01A;
import com.mega.eloan.lms.model.C999M01B;
import com.mega.eloan.lms.model.C999M01C;
import com.mega.eloan.lms.model.C999M01D;
import com.mega.eloan.lms.model.C999S01A;
import com.mega.eloan.lms.model.C999S01B;
import com.mega.eloan.lms.model.C999S02A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L140S01A;
import com.mega.eloan.lms.model.L140S02A;
import com.mega.eloan.lms.model.L140S02B;
import com.mega.eloan.lms.model.L140S02C;
import com.mega.eloan.lms.model.L140S02D;
import com.mega.eloan.lms.model.L140S02E;
import com.mega.eloan.lms.model.L140S02I;
import com.mega.eloan.lms.model.L140S02K;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.model.Page;

/**
 * <pre>
 * 個金約據書 Service
 * </pre>
 * 
 * @since 2012/8/15
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/8/15,Miller,new
 *          </ul>
 */
public interface LMS9990Service2 extends AbstractService {

	/**
	 * 取得未輸入重複序號的統編列表
	 * 
	 * @param custId
	 *            custId
	 * @param search
	 *            search
	 * @return Page<Map<String, Object>>
	 * @throws CapException
	 *             CapException
	 */
	Page<Map<String, Object>> listDupNoToCustId(String custId, ISearch search)
			throws CapException;

	/**
	 * 利用MAINID找到個金約據書授權檔群組
	 * 
	 * @param mainId
	 * @return
	 */
	List<C999A01A> findC999a01aByMainId(String mainId);

	/**
	 * 利用OID找到個金約據書主檔
	 * 
	 * @param oid
	 * @return
	 */
	C999M01A findC999m01aByOid(String oid);

	/**
	 * 利用MAINID找到個金約據書主檔
	 * 
	 * @param mainId
	 *            mainId
	 * @return C999M01A C999M01A
	 */
	C999M01A findC999m01aByMainId(String mainId);

	/**
	 * 利用獨特KEY找到個金約據書立約人檔
	 * 
	 * @param mainId
	 *            mainId
	 * @param custId
	 *            custId
	 * @param dupNo
	 *            dupNo
	 * @param type
	 *            type
	 * @return C999M01A C999M01A
	 */
	C999M01B findC999m01bByUniqueKey(String mainId, String custId,
			String dupNo, String type);

	/**
	 * 利用MAINID找到個金約據書立約人檔
	 * 
	 * @param mainId
	 *            mainId
	 * @return List<C999M01B> List<C999M01B>
	 */
	List<C999M01B> findC999m01bByMainId(String mainId);

	/**
	 * 利用MAINID找到個金約據書連保人(保證人)檔
	 * 
	 * @param mainId
	 *            mainId
	 * @return List<C999M01C> List<C999M01C>
	 */
	List<C999M01C> findC999m01cByMainId(String mainId);

	/**
	 * 儲存個金約據書連保人(保證人)檔群組
	 * 
	 * @param list
	 */
	void saveListC999m01c(List<C999M01C> list);

	/**
	 * 利用獨特KEY找到個金約據書項目描述檔
	 * 
	 * @param mainId
	 *            mainId
	 * @param itemType
	 *            itemType
	 * @return C999M01D C999M01D
	 */
	C999M01D findC999m01dByUniqueKey(String mainId, String itemType);

	/**
	 * 利用MAINID找到個金約據書項目描述檔
	 * 
	 * @param mainId
	 *            mainId
	 * @return List<C999M01D> List<C999M01D>
	 */
	List<C999M01D> findC999m01dByMainId(String mainId);

	/**
	 * 利用OID找到個金約據書產品種類檔
	 * 
	 * @param oid
	 *            oid
	 * @return C999S01A C999S01A
	 */
	C999S01A findC999s01aByOid(String oid);

	/**
	 * 利用MAINID找到個金約據書產品種類檔
	 * 
	 * @param mainId
	 *            mainId
	 * @return List<C999S01A> List<C999S01A>
	 */
	List<C999S01A> findC999s01aByMainId(String mainId);

	/**
	 * 儲存個金約據書產品種類檔(群組)
	 * 
	 * @param list
	 *            個金約據書產品種類檔(群組)
	 */
	void saveListC999s01a(List<C999S01A> list);

	/**
	 * 利用OID找到個金約據書契約內容檔
	 * 
	 * @param oid
	 *            oid
	 * @return C999S01B C999S01B
	 */
	C999S01B findC999s01bByOid(String oid);

	/**
	 * 利用MAINID找到個金約據書契約內容檔
	 * 
	 * @param mainId
	 *            mainId
	 * @return List<C999S01B> List<C999S01B>
	 */
	List<C999S01B> findC999s01bByMainId(String mainId);

	/**
	 * 儲存個金約據書契約內容檔(群組)
	 * 
	 * @param list
	 *            個金約據書契約內容檔(群組)
	 */
	void saveListC999s01b(List<C999S01B> list);

	/**
	 * 儲存個金約據書契約內容檔(群組)及個金約據書主檔
	 * 
	 * @param list
	 *            個金約據書契約內容檔(群組)及個金約據書主檔
	 */
	void saveMetaListC999s01b(List<C999S01B> list, C999M01A meta);

	/**
	 * 儲存個金約據書契約內容檔(群組)、產品種類檔及個金約據書主檔
	 * @param list
	 * @param c999s01a
	 * @param meta
	 */
	void saveMetaListC999s01ab(List<C999S01B> list, C999S01A c999s01a, C999M01A meta);
	
	/**
	 * 利用獨特Key找到個金約據書契約內容檔
	 * 
	 * @param mainId
	 *            mainId
	 * @param pid
	 *            pid
	 * @param type
	 *            type
	 * @return C999S01B
	 */
	C999S01B findC999s01bByUniqueKey(String mainId, String pid, String type);

	/**
	 * 利用OID找到個金約據書增補條款內容檔
	 * 
	 * @param oid
	 * @return C999S02A C999S02A
	 */
	C999S02A findC999s02aByOid(String oid);

	/**
	 * 利用MAINID找到個金約據書增補條款內容檔
	 * 
	 * @param mainId
	 *            mainId
	 * @return List<C999S02A> List<C999S02A>
	 */
	List<C999S02A> findC999s02aByMainId(String mainId);

	/**
	 * 依照立約人種類及相關參數取得立約人名稱
	 * 
	 * @param l140CustName
	 *            使用者所選額度明細表借款人名稱
	 * @param type
	 *            立約人種類
	 * @param args
	 *            立約人種類為乙方時(type=1)必填參數: 全行/分行
	 * @return 立約人名稱
	 */
	String getCustName(String l140CustName, char type, String args);

	/**
	 * 取得以分行名義分行代碼
	 * 
	 * @return 以分行名義分行代碼
	 */
	String getBrNo();

	/**
	 * 取得以分行名義分行名稱
	 * 
	 * @return 以分行名義分行名稱
	 */
	String getBrName();

	/**
	 * 依照使用者所選額度明細表文件編號引進所有個金約據書產品種類檔(新增約據書專用)
	 * 
	 * @param mainIdsFor140
	 *            使用者所選額度明細表文件
	 * @param c999MainId
	 *            個金約據書主檔文件編號
	 * @return 設定好的所有個金約據書產品種類檔
	 * @throws CapMessageException
	 */
	List<C999S01A> impotAllC999s01a(String[] mainIdsFor140, String c999MainId,
			PageParameters params) throws CapMessageException;

	/**
	 * 依照使用者所選額度明細表文件編號引進所有個金約據書連保人(保證人)檔(新增約據書專用)
	 * 
	 * @param mainIdsFor140
	 *            使用者所選額度明細表文件
	 * @param c999MainId
	 *            個金約據書主檔文件編號
	 * @return 設定好的所有個金約據書連保人(保證人)檔
	 */
	List<C999M01C> importAllC999m01c(String[] mainIdsFor140, String c999MainId);

	/**
	 * 修改項次成使用者指定項次
	 * 
	 * @param oidArray
	 *            oidArray
	 * @param seqArray
	 *            seqArray
	 * @param parent
	 *            parent
	 * @return List<C999S01A> List<C999S01A>
	 * @throws CapException
	 */
	List<C999S01A> modifySeqs(String[] oidArray, String[] seqArray) throws CapException;

	/**
	 * 刪除上傳檔案
	 * 
	 * @param oids
	 *            文件編號
	 */
	void deleteUploadFile(String[] oids);

	/**
	 * 利用OID找到個金借保人檔
	 * 
	 * @param oid
	 *            oid
	 * @return L140S01A L140S01A
	 */
	L140S01A findL140s01aByOid(String oid);

	/**
	 * 利用MAINID找到個金借保人檔
	 * 
	 * @param mainId
	 *            mainId
	 * @return L140S01A L140S01A
	 */
	List<L140S01A> findL140s01aByMainId(String mainId);

	/**
	 * 利用OID找到個金產品種類檔
	 * 
	 * @param oid
	 *            oid
	 * @return L140S02A L140S02A
	 */
	L140S02A findL140s02aByOid(String oid);

	/**
	 * 利用MAINID找到個金產品種類檔
	 * 
	 * @param mainId
	 *            mainId
	 * @return List<L140S02A> List<L140S02A>
	 */
	List<L140S02A> findL140s02aByMainId(String mainId);

	/**
	 * 利用獨特KEY找到個金產品種類檔
	 * 
	 * @param mainId
	 *            mainId
	 * @param seq
	 *            seq
	 * @return L140S02A L140S02A
	 */
	L140S02A findl140s02aByUniqueKey(String mainId, Integer seq);

	/**
	 * 利用OID找到流用(長擔)額度序號檔
	 * 
	 * @param oid
	 *            oid
	 * @return L140S02B L140S02B
	 */
	L140S02B findL140s02bByOid(String oid);

	/**
	 * 利用MAINID找到流用(長擔)額度序號檔
	 * 
	 * @param mainId
	 *            mainId
	 * @return List<L140S02B> List<L140S02B>
	 */
	List<L140S02B> findL140s02bByMainId(String mainId);

	/**
	 * 利用獨特KEY找到流用(長擔)額度序號檔
	 * 
	 * @param mainId
	 *            mainId
	 * @param seq
	 *            seq
	 * @param cntrNo
	 *            cntrNo
	 * @return L140S02B L140S02B
	 */
	L140S02B findl140s02bByUniqueKey(String mainId, Integer seq, String cntrNo);

	/**
	 * 利用OID找到國內個金分段利率主檔
	 * 
	 * @param oid
	 *            oid
	 * @return L140S02C L140S02C
	 */
	L140S02C findL140s02cByOid(String oid);

	/**
	 * 利用MAINID找到國內個金分段利率主檔
	 * 
	 * @param mainId
	 *            mainId
	 * @return List<L140S02C> List<L140S02C>
	 */
	List<L140S02C> findL140s02cByMainId(String mainId);

	/**
	 * 利用獨特KEY找到國內個金分段利率主檔
	 * 
	 * @param mainId
	 *            mainId
	 * @param seq
	 *            seq
	 * @return L140S02C L140S02C
	 */
	L140S02C findl140s02cByUniqueKey(String mainId, Integer seq);

	/**
	 * 利用OID找到國內個金分段利率明細檔
	 * 
	 * @param oid
	 *            oid
	 * @return L140S02D L140S02D
	 */
	L140S02D findL140s02dByOid(String oid);

	/**
	 * 利用MAINID找到國內個金分段利率明細檔
	 * 
	 * @param mainId
	 *            mainId
	 * @return List<L140S02D> List<L140S02D>
	 */
	List<L140S02D> findL140s02dByMainId(String mainId);

	/**
	 * 利用獨特KEY找到國內個金分段利率明細檔
	 * 
	 * @param mainId
	 *            mainId
	 * @param seq
	 *            seq
	 * @param phase
	 *            phase
	 * @return L140S02D L140S02D
	 */
	L140S02D findl140s02dByUniqueKey(String mainId, Integer seq, Integer phase);

	/**
	 * 利用OID找到償還方式檔
	 * 
	 * @param oid
	 *            oid
	 * @return L140S02E L140S02E
	 */
	L140S02E findL140s02eByOid(String oid);

	/**
	 * 利用MAINID找到償還方式檔
	 * 
	 * @param mainId
	 *            mainId
	 * @return List<L140S02E> List<L140S02E>
	 */
	List<L140S02E> findL140s02eByMainId(String mainId);

	/**
	 * 利用獨特KEY找到償還方式檔
	 * 
	 * @param mainId
	 *            mainId
	 * @param seq
	 *            seq
	 * @return L140S02E L140S02E
	 */
	L140S02E findl140s02eByUniqueKey(String mainId, Integer seq);

	/**
	 * 利用OID找到留學貸款檔
	 * 
	 * @param oid
	 *            oid
	 * @return L140S02I L140S02I
	 */
	L140S02I findL140s02iByOid(String oid);

	/**
	 * 利用MAINID找到留學貸款檔
	 * 
	 * @param mainId
	 *            mainId
	 * @return List<L140S02I> List<L140S02I>
	 */
	List<L140S02I> findL140s02iByMainId(String mainId);

	/**
	 * 利用獨特KEY找到留學貸款檔
	 * 
	 * @param mainId
	 *            mainId
	 * @param seq
	 *            seq
	 * @param stdCustId
	 *            stdCustId
	 * @param stdDupNo
	 *            stdDupNo
	 * @return L140S02I L140S02I
	 */
	L140S02I findl140s02iByUniqueKey(String mainId, Integer seq,
			String stdCustId, String stdDupNo);

	/**
	 * 利用OID找到貸款額度評等表檔
	 * 
	 * @param oid
	 *            oid
	 * @return L140S02K L140S02K
	 */
	L140S02K findL140s02kByOid(String oid);

	/**
	 * 利用MAINID找到貸款額度評等表檔
	 * 
	 * @param mainId
	 *            mainId
	 * @return List<L140S02K> List<L140S02K>
	 */
	List<L140S02K> findL140s02kByMainId(String mainId);

	/**
	 * 利用獨特KEY找到貸款額度評等表檔
	 * 
	 * @param mainId
	 *            mainId
	 * @param seq
	 *            seq
	 * @return L140S02K L140S02K
	 */
	L140S02K findl140s02kByUniqueKey(String mainId, Integer seq);

	/**
	 * 新增個金約據書主檔
	 * 
	 * @param meta
	 *            簽報書主檔
	 * @param contractType
	 *            contractType
	 * @param contractType2
	 *            contractType2
	 * @param contractKind
	 *            contractKind
	 * @param l140CustId
	 *            l140CustId
	 * @param l140DupNo
	 *            l140DupNo
	 * @param l140CustName
	 *            l140CustName
	 * @param txCode
	 *            txCode
	 * @return C999M01A C999M01A
	 */
	C999M01A addC999M01A(L120M01A meta, String contractType,
			String contractType2, String contractKind, String l140CustId,
			String l140DupNo, String l140CustName, String txCode);

	/**
	 * 新增個金約據書立約檔
	 * 
	 * @param l140CustId
	 *            l140CustId
	 * @param l140DupNo
	 *            l140DupNo
	 * @param l140CustName
	 *            l140CustName
	 * @param type
	 *            type
	 * @param c999MainId
	 *            c999MainId
	 * @return C999M01B C999M01B
	 */
	C999M01B addC999m01b(String l140CustId, String l140DupNo,
			String l140CustName, String type, String c999MainId);

	/**
	 * 新增個金約據書連保人(保證人)檔
	 * 
	 * @param custId
	 *            custId
	 * @param dupNo
	 *            dupNo
	 * @param custName
	 *            custName
	 * @param custPos
	 *            custPos
	 * @param c999MainId
	 *            c999MainId
	 * @return C999M01C C999M01C
	 */
	C999M01C addC999m01c(String custId, String dupNo, String custName,
			String custPos, String c999MainId);

	/**
	 * 新增個金約據書產品種類檔
	 * 
	 * @param c999MainId
	 *            c999MainId
	 * @param itemNo
	 *            itemNo
	 * @param cntrNo
	 *            cntrNo
	 * @param prodKind
	 *            prodKind
	 * @param subjCode
	 *            subjCode
	 * @param loanCurr
	 *            loanCurr
	 * @param loanAmt
	 *            loanAmt
	 * @return C999S01A C999S01A
	 */
	C999S01A addC999s01a(String c999MainId, Integer itemNo, String cntrNo,
			String prodKind, String subjCode, String loanCurr,
			BigDecimal loanAmt);

	/**
	 * 新增個金約據書契約內容檔
	 * 
	 * @param c999MainId
	 *            c999MainId
	 * @param String
	 *            pid
	 * @param itemType
	 *            itemType
	 * @param itemDscr
	 *            itemDscr
	 * @return C999S01B C999S01B
	 */
	C999S01B addC999s01b(String c999MainId, String pid, String itemType,
			String itemDscr);

	/**
	 * 依照類型取得組合好的契約內容
	 * 
	 * @param mainId
	 *            mainId
	 * @param type
	 *            type
	 * @param getSubTitle
	 *            getSubTitle
	 * @param isWord
	 *            isWord
	 * @return String str
	 * @throws CapMessageException
	 */
	String comBineC999s01b(String mainId, String type, boolean getSubTitle,
			boolean isWord) throws CapMessageException;

	// /**
	// * 將前端欄位轉換成JsonData並設定到後端JsonData欄位
	// * @param c999s01a 產品種類檔
	// * @param s18Panel 前端契約金額欄位值
	// * @param s19Panel 前端借款用途欄位值
	// * @param s20Panel 前端動用方式欄位值
	// * @param s21Panel 前端撥款方式欄位值
	// * @param s22Panel 前端償還辦法欄位值
	// * @param s23Panel 前端利息計付欄位值
	// * @return 設定好的契約內容檔群組
	// */
	// List<C999S01B> setJsonData(C999S01A c999s01a, JSONObject s18Panel,
	// JSONObject s19Panel, JSONObject s20Panel, JSONObject s21Panel, JSONObject
	// s22Panel,
	// JSONObject s23Panel);

	/**
	 * 將前端欄位轉換成JsonData並設定到後端JsonData欄位
	 * 
	 * @param c999s01a
	 * @param allJson
	 *            所有頁籤Json物件
	 * @return 設定好的契約內容檔群組
	 */
	List<C999S01B> setJsonData(C999S01A c999s01a, JSONObject allJson);

	/**
	 * 儲存個金約據書(包含相關Model)
	 * 
	 * @param c999m01a
	 * @param c999m01b1
	 * @param c999m01b2
	 * @param listC999m01c
	 * @param listC999s01a
	 */
	void saveC9990All(C999M01A c999m01a, C999M01B c999m01b1,
			C999M01B c999m01b2, List<C999M01C> listC999m01c,
			List<C999S01A> listC999s01a);

	/**
	 * 取得JsonData 字串內容或Key名稱(其他部份)
	 * 
	 * @param json
	 *            JSONObject
	 * @param type
	 *            類型
	 * @param getKey
	 *            只取得Key名稱
	 * @return getKey = true : 傳回Key名稱 getKey = false : 傳回JsonObject 數值
	 */
	String getJDataByType(JSONObject json, String type, boolean getKey);

	/**
	 * 取得子標題
	 * 
	 * @param json
	 *            JSONObject
	 * @param type
	 *            類型
	 * @param isSingle
	 *            是否為單一產品種類
	 * @return String
	 */
	String getSubTitleByType(JSONArray jsons, String type, boolean isSingle);

	/**
	 * 暫時的項次名次對應表
	 * 
	 * @param val
	 * @return
	 */
	String getKeyName(String val);

	/**
	 * 刪除個金約據書相關資料
	 * @param model
	 * @param list1
	 * @param list2
	 * @param list3
	 * @param list4
	 * @param list5
	 * @param list6
	 * @param list7
	 */
	void delRelC999(C999M01A model, List<C999A01A> list1, List<C999M01B> list2,
			List<C999M01C> list3, List<C999M01D> list4, List<C999S01A> list5,
			List<C999S01B> list6, List<C999S02A> list7);
}