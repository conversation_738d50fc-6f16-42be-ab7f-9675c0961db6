<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
           http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd">

	<context:annotation-config />
	<!-- @Repository -->
	<context:component-scan base-package="com.mega.eloan.lms.dao.impl" />

	<!-- COMMON DAO BEGIN -->

	<!-- 使用LMS4COM的EM -->
	<bean id="auditLogDaoImpl" class="com.mega.eloan.common.dao.impl.AuditLogDaoImpl">
		<property name="entityManager" ref="lms4comEntityManager" />
	</bean>

	<!-- 使用各子系統的EM -->
	<bean id="commonMetaDaoImpl" class="com.mega.eloan.common.dao.impl.CommonMetaDaoImpl">
		<property name="entityManager" ref="lmsEntityManager" />
	</bean>
	<bean id="docLogDaoImpl" class="com.mega.eloan.common.dao.impl.DocLogDaoImpl">
		<property name="entityManager" ref="lmsEntityManager" />
	</bean>
	<bean id="docOpenerDaoImpl" class="com.mega.eloan.common.dao.impl.DocOpenerDaoImpl">
		<property name="entityManager" ref="lmsEntityManager" />
	</bean>
	<bean id="docFileDaoImpl" class="com.mega.eloan.common.dao.impl.DocFileDaoImpl">
		<property name="entityManager" ref="lmsEntityManager" />
	</bean>
	<bean id="tempDataDaoImpl" class="com.mega.eloan.common.dao.impl.TempDataDaoImpl">
		<property name="entityManager" ref="lmsEntityManager" />
	</bean>
	
	<bean id="schMainDaoImpl" class="com.mega.eloan.common.batch.dao.impl.SchMainDaoImpl">
		<property name="entityManager" ref="comEntityManager" />
	</bean>

	<bean id="schJobDaoImpl" class="com.mega.eloan.common.batch.dao.impl.SchJobDaoImpl">
		<property name="entityManager" ref="comEntityManager" />
	</bean>

	<bean id="schRelDaoImpl" class="com.mega.eloan.common.batch.dao.impl.SchRelDaoImpl">
		<property name="entityManager" ref="comEntityManager" />
	</bean>

	<bean id="schLogDaoImpl" class="com.mega.eloan.common.batch.dao.impl.SchLogDaoImpl">
		<property name="entityManager" ref="comEntityManager" />
	</bean>

	<bean id="schQueueDaoImpl" class="com.mega.eloan.common.batch.dao.impl.SchQueueDaoImpl">
		<property name="entityManager" ref="comEntityManager" />
	</bean>
	<!-- COMMON DAO END -->
</beans>