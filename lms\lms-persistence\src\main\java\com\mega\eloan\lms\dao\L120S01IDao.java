/* 
 * L120S01IDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S01I;

/** 個金服務單位檔 **/
public interface L120S01IDao extends IGenericDao<L120S01I> {

	L120S01I findByOid(String oid);
	
	List<L120S01I> findByMainId(String mainId);
	
	L120S01I findByUniqueKey(String mainId, String custId, String dupNo);

	List<L120S01I> findByIndex01(String mainId, String custId, String dupNo);
	
	List<L120S01I> findByCustIdDupId(String custId,String DupNo);
}