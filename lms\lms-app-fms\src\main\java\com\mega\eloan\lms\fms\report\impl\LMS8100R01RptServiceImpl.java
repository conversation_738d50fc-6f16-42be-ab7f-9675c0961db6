package com.mega.eloan.lms.fms.report.impl;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.inet.report.ReportException;
import com.mega.eloan.common.dao.DocFileDao;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.AbstractReportService;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.fms.pages.LMS8100M01Page;
import com.mega.eloan.lms.fms.service.LMS8100Service;
import com.mega.eloan.lms.model.L300M01A;
import com.mega.eloan.lms.model.L300M01B;
import com.mega.eloan.lms.model.L300M01C;
import com.mega.eloan.lms.model.L300S01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapMath;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.PdfTools;
import tw.com.jcs.common.report.ReportGenerator;
import tw.com.jcs.common.report.SubReportParam;

@Service("lms8100r01rptservice")
public class LMS8100R01RptServiceImpl extends AbstractReportService implements FileDownloadService {

	@Resource
	DocFileDao docFileDao;
	
	@Resource
	DocFileService docFileService;
	
	@Resource
	BranchService branchService;
	
	@Resource
	UserInfoService userInfoService;
	
	@Resource
	EloandbBASEService eloanDbBaseService;
	
	@Resource
	LMS8100Service lms8100Service;

	@Resource
	RetrialService retrialService;
	
	protected static final Logger LOGGER = LoggerFactory
			.getLogger(LMS8100R01RptServiceImpl.class);

	@Override
	public byte[] getContent(PageParameters params) throws CapException,
			FileNotFoundException, ReportException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
			baos = (ByteArrayOutputStream) this.generateReport(params);
			return baos.toByteArray();
		} finally {
			if (baos != null) {
				baos.close();
			}
		}
	}

	public OutputStream generateReport(PageParameters params) throws Exception{
		LOGGER.info("into setReportData");
		OutputStream outputStream = null;
		Properties prop = MessageBundleScriptCreator.getComponentResource(LMS8100M01Page.class);
		Locale locale = null;
		locale = LocaleContextHolder.getLocale();
		if (locale == null)
			locale = Locale.getDefault();
		String type = Util.trim(params.getString("type"));

		if(Util.equals(type, "R01")){
			outputStream = this.genLMS8100R01(params, prop, locale);
		} else if(Util.equals(type, "R02")){
			outputStream = this.genLMS8100R02(params, prop, locale);
        } else {

		}

		return outputStream;
	}

	public OutputStream genLMS8100R01(PageParameters params, Properties prop, Locale locale)
			throws FileNotFoundException, IOException, Exception {
		OutputStream outputStream = null;
		Map<InputStream, Integer> pdfNameMap = new LinkedHashMap<InputStream, Integer>();
		List<InputStream> list = new LinkedList<InputStream>();
		int subLine = 3;
		
		Properties propEloanPage = MessageBundleScriptCreator.getComponentResource(
				AbstractEloanPage.class);
		try {
			String[] oids = params.getStringArray("oids");
//			String mainOid = Util.trim(params.getString(EloanConstants.MAIN_OID));
//			String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
			
			if (oids.length > 0) {
				for (String mainOid : oids) {
					outputStream = this.genLMS8100R01Detail(params, prop, locale, mainOid);
					pdfNameMap.put(
							new ByteArrayInputStream(
									((ByteArrayOutputStream) outputStream)
											.toByteArray()), subLine);
				}
			}
			
			if (pdfNameMap != null && pdfNameMap.size() > 0) {
				outputStream = new ByteArrayOutputStream();
				PdfTools.mergeReWritePagePdf(pdfNameMap, outputStream,
						propEloanPage.getProperty("PaginationText"), true,
						locale, subLine);
				list.add(new ByteArrayInputStream(
						((ByteArrayOutputStream) outputStream)
								.toByteArray()));
			}
			
			outputStream = new ByteArrayOutputStream();
			PdfTools.mergeReWritePagePdf(list, outputStream);
		} finally {
			if (pdfNameMap != null) {
				pdfNameMap.clear();
			}
		}
		
		return outputStream;
	}
	
	@SuppressWarnings("unchecked")
	public OutputStream genLMS8100R01Detail(PageParameters params, Properties prop, Locale locale, String oid)
			throws FileNotFoundException, IOException, Exception {
		ReportGenerator generator = new ReportGenerator(
				"report/fms/LMS8100R01_" + locale.toString() + ".rpt");
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		List<Map<String, String>> mainRows = new LinkedList<Map<String, String>>();
		OutputStream outputStream = null;
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		DecimalFormat df = new DecimalFormat("#####0.###");

		Properties propM01 = MessageBundleScriptCreator
				.getComponentResource(LMS8100M01Page.class);

		try {
			L300M01A l300m01a = lms8100Service.findModelByOid(L300M01A.class, oid);
			if (l300m01a == null) {
				l300m01a = new L300M01A();
			}
			
			String mainId = Util.nullToSpace(l300m01a.getMainId());
			
			String staffL1 = "";
			String staffL3 = "";
			String staffL5 = "";
			String staffL6 = "";
			List<L300M01B> l3000m01blist = (List<L300M01B>)lms8100Service.findListByMainId(L300M01B.class, mainId);
			if (!Util.isEmpty(l3000m01blist) && Util.notEquals(l300m01a.getDocStatus(), CreditDocStatusEnum.海外_編製中)) {
				for (L300M01B l300m01b : l3000m01blist) {
					String type = Util.trim(l300m01b.getStaffJob());
					String userId = Util.trim(l300m01b.getStaffNo());
					String userName = Util.nullToSpace(userInfoService.getUserName(userId));
					if(Util.equals(type, UtilConstants.STAFFJOB.經辦L1)) {
						staffL1 = userId + " " + userName;
					} else if (Util.equals(type, UtilConstants.STAFFJOB.授信主管L3)) {
						staffL3 = userId + " " + userName;
					} else if (Util.equals(type, UtilConstants.STAFFJOB.單位授權主管L5)) {
						staffL5 = userId + " " + userName;
					} else if (Util.equals(type, UtilConstants.STAFFJOB.分行單位主管L6)) {
						staffL6 = userId + " " + userName;
					}
				}
			} else {
				staffL1 = Util.trim(l300m01a.getUpdater()) + " " + Util.trim(userInfoService.getUserName(l300m01a.getUpdater()));
			}
			
			
			// J-112-0461
			// 授信貸後管理缺失考評表之說明一、考評期間：「分為上半年、下半年，各六個月為一期。」更改為「每三個月為一期，按季考評一年分四期。」
			String produceInterval = lms8100Service.findProduceInterval(CapDate
					.formatDate(l300m01a.getCreateTime(),
							UtilConstants.DateFormat.YYYY_MM_DD));
			String memo1 = "";
			if (produceInterval.equals("S")) {
				memo1 = "一、考評期間：每三個月為一期，按季考評一年分四期。";
			} else {
				memo1 = "一、考評期間：分為上半年、下半年，各六個月為一期。";
			}
			rptVariableMap.put("memo1", memo1);
			
			rptVariableMap.put("afterCalc", Util.equals(l300m01a.getProduceFlag(), "Y") ? "true" : "false");
			String ownBrNo = Util.isEmpty(Util.nullToSpace(l300m01a.getBranchId())) ? user.getUnitNo() : l300m01a.getBranchId(); 
			String ownBr = branchService.getBranchName(ownBrNo);
			rptVariableMap.put("ownBr", ownBr);
			rptVariableMap.put("ownBrNo", ownBrNo);
			rptVariableMap.put("year", StringUtils.substring(CapDate.formatDate(l300m01a.getBgnDate(), "yyyy-MM-dd"), 0, 4));
			rptVariableMap.put("L300M01B.staffL1", staffL1);
			rptVariableMap.put("L300M01B.staffL3", staffL3);
			rptVariableMap.put("L300M01B.staffL5", staffL5);
			rptVariableMap.put("L300M01B.staffL6", staffL6);
			rptVariableMap.put("L300M01A.assDate", CapDate.formatDate(l300m01a.getAssDate(), "yyyy-MM-dd"));
			rptVariableMap.put("L300M01A.subTotal", df.format(Util.parseBigDecimal(l300m01a.getSubTotal())) + "分");
			rptVariableMap.put("L300M01A.rsNum", Util.nullToSpace(l300m01a.getRsNum()));
			rptVariableMap.put("L300M01A.avgScore", df.format(Util.parseBigDecimal(
					l300m01a.getAvgScore()).setScale(3, BigDecimal.ROUND_HALF_UP)));
			rptVariableMap.put("L300M01A.realScore", df.format(Util.parseBigDecimal(
					l300m01a.getRealScore()).setScale(3, BigDecimal.ROUND_HALF_UP)) + "分");
			rptVariableMap.put("L300M01A.totalScore", df.format(Util.parseBigDecimal(
					l300m01a.getTotalScore()).setScale(3, BigDecimal.ROUND_HALF_UP)) + "分");

			// 各項目種類別 YN or CNT
			Map<String, String> typeMap = lms8100Service.getItemTypeMap(
					Util.nullToSpace(l300m01a.getPaVer()));
			// 先放上初始的扣分標準
			Map<String, BigDecimal> scoreMap = lms8100Service.getItemScoreMap(
					Util.nullToSpace(l300m01a.getPaVer()));
			for (String itemName : scoreMap.keySet()) {
				rptVariableMap.put(itemName + "_itemCnt", "0");
				
				BigDecimal itemScore = scoreMap.get(itemName);
				rptVariableMap.put(itemName + "_itemScore", df.format(itemScore == null ? 
						BigDecimal.ZERO : Util.parseBigDecimal(itemScore)));
				
				rptVariableMap.put(itemName + "_itemAll", "0");
				rptVariableMap.put(itemName + "_itemDscr", "");
			}
			rptVariableMap.put("plusItem_itemAll", "");
			rptVariableMap.put("plusItem_itemDscr", "");
			rptVariableMap.put("minusItem_itemAll", "");
			rptVariableMap.put("minusItem_itemDscr", "");

			List<L300S01A> l300s01aList = lms8100Service.findL300s01aList(l300m01a.getMainId());
			if (l300s01aList != null && !l300s01aList.isEmpty()) {
				for (L300S01A l300s01a : l300s01aList) {
					String itemName = Util.nullToSpace(l300s01a.getItemName());

					rptVariableMap.put(itemName + "_itemCnt", df.format(
							l300s01a.getItemCnt() == null ? BigDecimal.ZERO :
								Util.parseBigDecimal(l300s01a.getItemCnt())));
					rptVariableMap.put(itemName + "_itemScore", df.format(
							l300s01a.getItemScore() == null ? BigDecimal.ZERO :
								Util.parseBigDecimal(l300s01a.getItemScore())) + "分");
					rptVariableMap.put(itemName + "_itemAll", df.format(
							l300s01a.getItemAll() == null ? BigDecimal.ZERO :
								Util.parseBigDecimal(l300s01a.getItemAll())) + "分");
					rptVariableMap.put(itemName + "_itemDscr", l300s01a.getItemDscr());
				}
				// 考評項目動態組
				String paVer = Util.nullToSpace(l300m01a.getPaVer());
				String propVerStr = (Util.isNotEmpty(paVer) ? ("_" + paVer) : "");

				for (String item : scoreMap.keySet()) {	// 為了排序
					for (L300S01A l300s01a : l300s01aList) {
						String paItemName = Util.nullToSpace(l300s01a.getItemName());
						if (Util.notEquals(item, paItemName)) {
							continue;
						}

						String itemType = typeMap.get(item);
						String inputStr = "";
						if (Util.equals(itemType, "YN")) {
							inputStr = "戶";
						} else if (Util.equals(itemType, "CNT")) {
							inputStr = "項";
						} else {
						}

						Map<String, String> mainMap = Util.setColumnMap();
						mainMap.put("ReportBean.column01", propM01.getProperty("L300S01A." + paItemName + propVerStr));
						mainMap.put("ReportBean.column02", df.format(l300s01a.getItemCnt() == null ?
								BigDecimal.ZERO : Util.parseBigDecimal(l300s01a.getItemCnt())) + inputStr);
						mainMap.put("ReportBean.column03", df.format(l300s01a.getItemScore() == null ?
								BigDecimal.ZERO : Util.parseBigDecimal(l300s01a.getItemScore())) + "分");
						mainMap.put("ReportBean.column04", df.format(l300s01a.getItemAll() == null ?
								BigDecimal.ZERO : Util.parseBigDecimal(l300s01a.getItemAll())) + "分");
						mainMap.put("ReportBean.column05", l300s01a.getItemDscr());
						mainRows.add(mainMap);
					}
				}
			}

			SubReportParam subReportParam = new SubReportParam();
			Map<String, String> map = new HashMap<String, String>();
			subReportParam.setData(0, map, mainRows);
			generator.setSubReportParam(subReportParam);
			//generator.setRowsData(mainRows);
			generator.setVariableData(rptVariableMap);
			generator.setLang(locale);

			outputStream = generator.generateReport();
			
		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
		}
		
		return outputStream;
	}

	public OutputStream genLMS8100R02(PageParameters params, Properties prop, Locale locale)
			throws Exception {
		ReportGenerator generator = new ReportGenerator(
				"report/fms/LMS8100R02_" + locale.toString() + ".rpt");
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		OutputStream outputStream = null;
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		DecimalFormat df = new DecimalFormat("#####0.###");

		try {
			String bgnDate = Util.trim(params.getString("bgnDate"));
			String endDate = Util.trim(params.getString("endDate"));
			String yyyy = StringUtils.substring(bgnDate, 0, 4);
			String mm = StringUtils.substring(bgnDate, 5, 7);
			String brName = branchService.getBranchName(user.getUnitNo());

			// 非營運中心單位931覆審
			Set<String> SpecialBranchList = new HashSet<String>();
			String[] branch_K = retrialService.getRetrailSpecialBranch();
			for (String brnoK : branch_K) {
				String newBranch = Util.trim(retrialService
						.getRetrailNewBranch(brnoK));
				if (Util.notEquals(brnoK, user.getUnitNo())
						&& Util.equals(newBranch, user.getUnitNo())) {
					SpecialBranchList.add(brnoK);
				}
			}
			String[] SpecialBranchArray =  SpecialBranchList.toArray(new String[SpecialBranchList.size()]);

			List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();
			List<Map<String, Object>> list = eloanDbBaseService.getRankingBoard(
					user.getUnitNo(), bgnDate, endDate, SpecialBranchArray);
			if (list != null && !list.isEmpty()) {
				
				// J-112-0461 授信覆審考核表-編製中-下拉選項統計由每半年改為每季統計。
				int days = CapDate.calculateDays(Util.parseDate(endDate),
						Util.parseDate(bgnDate));
				if (days > 27 && days < 93) {

					String str = "";
					if (Util.equals(mm, "01")) {
						str = "第一季(1~3月)";
					} else if (Util.equals(mm, "04")) {
						str = "第二季(4~6月)";
					} else if (Util.equals(mm, "07")) {
						str = "第三季(7~9月)";
					} else if (Util.equals(mm, "10")) {
						str = "第四季(10~12月)";
					}

					rptVariableMap.put("title", yyyy + "年" + str + brName);

				} else {
					rptVariableMap.put("title",
							yyyy + "年" + (Util.equals(mm, "01") ? "上" : "下")
									+ "半年度" + brName);
				}
				
				
				rptVariableMap.put("ownBr", brName);

				BigDecimal rsNum = BigDecimal.ZERO;
				for (Map<String, Object> row : list) {
					String BRN = Util.trim(row.get("BRN"));			 				// 分行
					String RANK = Util.trim(row.get("RANK")); 						// 排名
					String TOTALSCORE = Util.trim(row.get("TOTALSCORE")); 			// 總分
					String RSNUMs = Util.trim(row.get("RSNUM")); 					// 覆審件數
					rsNum = rsNum.add(Util.parseBigDecimal(RSNUMs));
					
					Map<String, String> map = Util.setColumnMap();
					map.put("ReportBean.column01", BRN);
					map.put("ReportBean.column02", Util.isEmpty(RANK) ? "無資料" : RANK);
					map.put("ReportBean.column03", df.format(Util.parseBigDecimal(TOTALSCORE)));

					titleRows.add(map);
				}
				rptVariableMap.put("rsNum", CapMath.bigDecimalToString(rsNum));
				if (titleRows == null || titleRows.size() == 0) {
					Map<String, String> map = Util.setColumnMap();
					map.put("ReportBean.column01", "查無資料");
					titleRows.add(map);
				}
				
				generator.setLang(locale);
				generator.setVariableData(rptVariableMap);
				generator.setRowsData(titleRows);

				outputStream = generator.generateReport();
				
				// 排名表檔
				String fileMainId = "";
				L300M01C l300m01c = lms8100Service.findL300m01c(user.getUnitNo(), bgnDate, endDate);
				if(l300m01c == null){
					fileMainId = IDGenerator.getUUID();
					l300m01c = new L300M01C();
					l300m01c.setOwnBrId(user.getUnitNo());
					l300m01c.setMainId(fileMainId);
					l300m01c.setCreator(user.getUserId());
					l300m01c.setCreateTime(CapDate.getCurrentTimestamp());
					l300m01c.setBgnDate(CapDate.getDate(bgnDate, "yyyy-MM-dd"));
					l300m01c.setEndDate(CapDate.getDate(endDate, "yyyy-MM-dd"));
				} else {
					fileMainId = l300m01c.getMainId();
				}
				l300m01c.setCreatDate(Util.parseDate(CapDate.getCurrentDate("yyyy-MM-dd")));
				
				// 產生附件
				// String fileMainId = IDGenerator.getRandomCode();
				String fieldId = "paRankingBoard";
				String fileName = fieldId + yyyy + mm + "_" + user.getUnitNo() + "_" 
					+ CapDate.getCurrentDate("yyyy-MM-dd");
				
				ByteArrayOutputStream baos = null;
				baos = (ByteArrayOutputStream) outputStream;

				DocFile file = new DocFile();
				file.setMainId(fileMainId);
				file.setData(baos != null ? baos.toByteArray() : null);
				file.setFileDesc("覆審排名表" + user.getUserId());
				file.setCrYear(CapDate.getCurrentDate("yyyy"));
				file.setFieldId(fieldId);
				file.setSrcFileName(fileName + ".pdf");
				file.setUploadTime(CapDate.getCurrentTimestamp());
				file.setBranchId(user.getUnitNo());
				file.setContentType("application/pdf");
				file.setSysId("FMS");
				docFileService.save(file);
				file = docFileDao.find(file);	// 為了取得oid
				
				// 附件寫入排名表檔
				l300m01c.setReportFile(file.getOid());
				lms8100Service.save(l300m01c);
			} else {
				throw new CapMessageException("產生LMS8100R02_PDF失敗！", getClass());
			}
		} finally {
		}

		return outputStream;
	}

	@Override
	public String getReportTemplateFileName() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public void setReportData(ReportGenerator rptGenerator,
			PageParameters params) throws CapException, ParseException {
		// TODO Auto-generated method stub		
	}
}
