package com.mega.eloan.lms.batch.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.exception.GWException;
import com.mega.eloan.common.gwclient.GWLogger;
import com.mega.eloan.common.gwclient.GWType;
import com.mega.eloan.common.service.GWLogService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.lms.base.common.ClsUtility;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.cls.constants.ClsConstants;
import com.mega.eloan.lms.dao.C122M01ADao;
import com.mega.eloan.lms.model.C122M01A;

import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * RPA 回傳勞工紓困貸款案件狀態
 * </pre>
 * <p>
 * LOCAL Test URL example ：
 * http://localhost/lms-web/app/schedulerRPA
 * <p>
 * Post Request : {"serviceId":"getLaborCaseStatus",
 * "vaildIP":"N",
 * "request":{"responseCode":"R01","custId":"A123456789","brNo":"007","rpsUserId":"78001"}}
 * <p>
 * SIT http://*************:9081/lms-web/app/schedulerRPA
 *
 * <AUTHOR>
 * @version <ul>
 * <li>2021/6/2,EL07623,new
 * </ul>
 * @since 2021/6/2
 */
@Service("getLaborCaseStatus")
public class RPAGetLaborCaseStatusServiceImpl extends AbstractCapService implements WebBatchService {

	private static Logger logger = LoggerFactory.getLogger(RPAGetLaborCaseStatusServiceImpl.class);

	@Resource
	CLSService clsService;
	@Resource
	C122M01ADao c122m01aDao;
	
	@Resource
	SysParameterService sysParameterService;

	@Resource
	GWLogService gwLogService;

	@Value("${systemId}")
	private String sysId;

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.mega.eloan.common.batch.service.WebBatchService#execute(net.sf.json
	 * .JSONObject)
	 */
	@Override
	public JSONObject execute(JSONObject json) {
		JSONObject mag;
		logger.info("getLaborCaseStatus 啟動========================");
		logger.info("傳入參數==>[{}]", json.toString());
		GWLogger gwlogger = new GWLogger(GWType.GWTYPE_RPA, gwLogService, sysParameterService);
		
		JSONObject req = json.getJSONObject("request");
		String errorMsg = "";
		String custId = req.optString("custId", "");
		String brNo = req.optString("brNo", "");
		
		gwlogger.logBegin(sysId, custId, "getLaborBailoutStatus", req.toString(), System.currentTimeMillis());
		
		List<C122M01A> c122m01aList = c122m01aDao.findBy_brNo_custId_applyKind_orderByApplyTSDesc(brNo, custId
				, new String[]{UtilConstants.C122_ApplyKind.B, UtilConstants.C122_ApplyKind.D}, ClsUtility.get_labor_bailout_2021_since_ts());
		if (!c122m01aList.isEmpty()) {
			C122M01A c122m01a = c122m01aList.get(0);
			if(Util.equals(ClsConstants.C122M01A_StatFlag.已作廢, c122m01a.getStatFlag())){
				//經辦已作廢
			}else{
				String responseCode = req.getString("responseCode");
				logger.info("custId:{},", new Object[] { custId, responseCode });
				if (UtilConstants.C122_ApplyStatus.RPA評分未達60分.equalsIgnoreCase(responseCode)) {
					//C122M01A.statFlag.applyKindB.E=RPA評分未達60分
					c122m01a.setStatFlag("E");
				} else if (UtilConstants.C122_ApplyStatus.RPA送信保中.equalsIgnoreCase(responseCode)) {
					//C122M01A.statFlag.applyKindB.F=RPA送信保中
					c122m01a.setStatFlag("F");
				} else if (UtilConstants.C122_ApplyStatus.RPA簽報書待主管放行.equalsIgnoreCase(responseCode)) {
					// C122M01A.statFlag.applyKindB.G=RPA簽報書待主管放行
					c122m01a.setStatFlag("G");
				} else if (UtilConstants.C122_ApplyStatus.RPA簽報書需分行介入.equalsIgnoreCase(responseCode)) {
					//C122M01A.statFlag.applyKindB.H=RPA簽報書需分行介入
					c122m01a.setStatFlag("H");
				} else if (UtilConstants.C122_ApplyStatus.RPA動審表待主管放行.equalsIgnoreCase(responseCode)) {
					//C122M01A.statFlag.applyKindB.I=RPA動審表待主管放行
					c122m01a.setStatFlag("I");
				} else if (UtilConstants.C122_ApplyStatus.RPA動審表需分行介入.equalsIgnoreCase(responseCode)) {
					//C122M01A.statFlag.applyKindB.J=RPA動審表需分行介入
					c122m01a.setStatFlag("J");
				} else if (UtilConstants.C122_ApplyStatus.RPA執行異常.equalsIgnoreCase(responseCode)) {
					//C122M01A.statFlag.applyKindB.K=RPA執行異常
					c122m01a.setStatFlag("K");
				} else if (UtilConstants.C122_ApplyStatus.RPA契約書待主管放行.equalsIgnoreCase(responseCode)) {
					//C122M01A.statFlag.applyKindB.L=RPA契約書待主管放行
					c122m01a.setStatFlag("L");
				} else if (UtilConstants.C122_ApplyStatus.擔保品待主管放行.equalsIgnoreCase(responseCode)) {
					//C122M01A.statFlag.applyKindB.M=擔保品待主管放行
					c122m01a.setStatFlag("M");
				} else if (UtilConstants.C122_ApplyStatus.RPA待人工評分.equalsIgnoreCase(responseCode)) {
					//C122M01A.statFlag.applyKindB.N=RPA待人工評分
					c122m01a.setStatFlag("N");
				}else if (UtilConstants.C122_ApplyStatus.RPA契約書需分行介入.equalsIgnoreCase(responseCode)) {
					//C122M01A.statFlag.applyKindB.O=RPA契約書需分行介入
					c122m01a.setStatFlag("O");
				}else if (UtilConstants.C122_ApplyStatus.RPA洗防高風險客群負面新聞需分行介入.equalsIgnoreCase(responseCode)) {
					//C122M01A.statFlag.applyKindB.P=RPA洗防高風險客群需負面新聞分行介入
					c122m01a.setStatFlag("P");
				}else if (UtilConstants.C122_ApplyStatus.擔保品已覆核.equalsIgnoreCase(responseCode)) {
					//C122M01A.statFlag.applyKindB.P=RPA洗防高風險客群需負面新聞分行介入
					c122m01a.setStatFlag("S");
				}
				c122m01a.setApplyStatus(responseCode);
				c122m01a.setUpdateTime(CapDate.getCurrentTimestamp());
				clsService.save(c122m01a);
			}
		} else {
			errorMsg = "查無此客戶勞工紓困案件:" + custId + "!";
		}
		logger.info("getLaborCaseStatus 結束========================");
		GWException gwException = null;
		if (!CapString.isEmpty(errorMsg)) {
			logger.info(errorMsg);
			gwException = new GWException(errorMsg, getClass(), GWException.GWTYPE_RPA);
		}
		mag = JSONObject.fromObject("{\"rc\": 0, \"rcmsg\": \"SUCCESS\", \"message\":\"執行成功\"}");
		
		gwlogger.logEnd(mag.toString(), gwException, "0");
		return mag;
		
	}

}
