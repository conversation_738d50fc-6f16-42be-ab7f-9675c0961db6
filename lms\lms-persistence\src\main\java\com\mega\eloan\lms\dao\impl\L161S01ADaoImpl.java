/* 
 *  L161S01ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L161S01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L161S01A;

/** 聯貸案參貸比率一覽表主檔 **/
@Repository
public class L161S01ADaoImpl extends LMSJpaDao<L161S01A, String> implements
		L161S01ADao {

	@Override
	public L161S01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L161S01A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L161S01A> list = createQuery(L161S01A.class, search)
				.getResultList();
		return list;
	}

	@Override
	public L161S01A findByUniqueKey(String mainId, String cntrNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L161S01A> findByIndex01(String mainId) {
		ISearch search = createSearchTemplete();
		List<L161S01A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(L161S01A.class, search).getResultList();
		}
		return list;
	}

	@Override
	public List<L161S01A> findByCntrNo(String CntrNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", CntrNo);
		search.addOrderBy("cntrNo");
		List<L161S01A> list = createQuery(L161S01A.class, search)
				.getResultList();

		return list;
	}

	@Override
	public L161S01A findByMainIdUid(String mainId, String uid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "uid", uid);
		return findUniqueOrNone(search);
	}

	@Override
	public L161S01A findByMainIdCntrno(String mainId, String cntrNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		return findUniqueOrNone(search);
	}

	/**
	 * J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制
	 * 
	 * @param mainId
	 * @return
	 */
	@Override
	public List<L161S01A> findByMainIdWithOrder(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addOrderBy("printSeq");
		search.addOrderBy("custId");
		search.addOrderBy("cntrNo");
		search.addOrderBy("oid");

		List<L161S01A> list = createQuery(L161S01A.class, search)
				.getResultList();

		return list;

	}

}