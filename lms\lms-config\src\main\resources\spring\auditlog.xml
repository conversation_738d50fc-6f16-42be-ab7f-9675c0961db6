<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:util="http://www.springframework.org/schema/util" xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:tx="http://www.springframework.org/schema/tx" xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
    	   http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-3.0.xsd
           http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.0.xsd
           http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd
           http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd">

	<!-- AOP Config -->
	<aop:config proxy-target-class="true">
		<aop:pointcut
			expression="execution(*
		tw.com.iisi.cap.plugin.AjaxHandlerPlugin.execute(..)) and args(data)"
			id="pcAuditLog4Handler" />

		<aop:aspect id="AuditLog4HandlerAspect" ref="AuditLog4HandlerAdvice">
			<aop:around pointcut-ref="pcAuditLog4Handler" method="logAroundAjaxHandlerExecute" />

			<aop:after-returning pointcut-ref="pcAuditLog4Handler"
				method="logAfterAjaxHandlerExecute" returning="reVal" />
			<aop:after-throwing pointcut-ref="pcAuditLog4Handler"
				method="logAfterAjaxHandlerThrowingException" throwing="exception" />
		</aop:aspect>
	</aop:config>


	<bean id="AuditLog4HandlerAdvice" class="com.mega.eloan.common.aop.AuditLog4HandlerAdvice">
		<property name="sysId" value="${systemId}" />
	</bean>


</beans>