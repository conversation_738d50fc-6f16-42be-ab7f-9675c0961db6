/* 
 * C160M01BDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.C160M01BDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C160M01B;
import com.mega.eloan.lms.model.L160M01B;

/** 額度明明細檔 **/
@Repository
public class C160M01BDaoImpl extends LMSJpaDao<C160M01B, String>
	implements C160M01BDao {

	@Override
	public C160M01B findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C160M01B> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addOrderBy("createTime");
		List<C160M01B> list = createQuery(search).getResultList();
		return list;
	}
	@Override
	public C160M01B findByMainIdRefMainId(String mainId,String refmainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "refmainId", refmainId);
		return findUniqueOrNone(search);
	}
	
	@Override
	public List<C160M01B> findByCntrNo(String CntrNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", CntrNo);
		search.addOrderBy("cntrNo");
		search.setMaxResults(Integer.MAX_VALUE);
		List<C160M01B> list = createQuery(C160M01B.class,search).getResultList();
		
		return list;
	}
	
	@Override
	public C160M01B findByUniqueKey(String oid){
		ISearch search = createSearchTemplete();
		if (oid != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<C160M01B> findByIndex01(String oid){
		ISearch search = createSearchTemplete();
		List<C160M01B> list = null;
		if (oid != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public C160M01B findByMainidCntrno(String Mainid, String Cntrno) {
		ISearch search = createSearchTemplete();
		if (Mainid != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", Mainid);
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", Cntrno);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<C160M01B> findByRefMainId(String refmainId){
		ISearch search = createSearchTemplete();
		
		List<C160M01B> list = null;
		search.addSearchModeParameters(SearchMode.EQUALS, "refmainId", refmainId);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
	
	@Override
	public C160M01B findByMainIdUid(String mainId, String uid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "uid", uid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C160M01B> findLastByRefMainId(String refmainId) {
		ISearch search = createSearchTemplete();
		List<C160M01B> list = null;
		search.addSearchModeParameters(SearchMode.EQUALS, "refmainId", refmainId);
		search.addOrderBy("updateTime", true);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
}