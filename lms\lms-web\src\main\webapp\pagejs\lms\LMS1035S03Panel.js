var initDfd = initDfd || $.Deferred();

initDfd.done(function(json){
	if(json.c120m01a_list){
		var dyna = []
						
		$.each(json.c120m01a_list.key, function(idx, jsonItem) {
			dyna.push("<table border='1' width='95%' class='tb2'>");
			dyna.push("<tr>");
			dyna.push("<td class='hd2' colspan='2'>"+jsonItem.c120_custPos+"</td>");
			dyna.push("<td colspan='2'>"+jsonItem.c120_custId+"-"+jsonItem.c120_dupNo+" "+jsonItem.c120_custName+"</td>");
			dyna.push("<td class='hd2'>"+i18n.abstractoverseacls['L120S01J.dRate']+"</td>");
			dyna.push("<td>"+jsonItem.c120_item_p4+"%"+"</td>");
			dyna.push("</tr>");
			dyna.push("<tr>");
			dyna.push("<td class='hd2'>"+i18n.abstractoverseacls['l120s02.index92']+"</td>");
			dyna.push("<td class=''>"+_build_curr_amt_str(jsonItem.c120_raw_payCurr , jsonItem.c120_raw_payAmt )+"</td>");
			dyna.push("<td class='hd2'>"+i18n.abstractoverseacls['L120S01J.oIncome']+"</td>");
			dyna.push("<td class=''>"+_build_curr_amt_str(jsonItem.c120_raw_otherCurr , jsonItem.c120_raw_otherAmt )+"</td>");
			dyna.push("<td class='hd2'>"+i18n.lms1035m01['tab03.annualIncome']+"</td>");
			dyna.push("<td class=''>"+_build_curr_amt_str(json.localCurr, jsonItem.c120_raw_p3_idv)+"</td>");
			dyna.push("</tr>");
			dyna.push("</table>");
			dyna.push("<br/>");
		});	
		
		if(true){			
			dyna.push("<b>"+i18n.lms1035m01['tab03.sumDiv']+"</b>");//借款人與連帶保證人之合計資料
			dyna.push("<table border='1' width='95%' class='tb2'>");			
			dyna.push("<tr style='vertical-align:top;' >");
			dyna.push("<td class='hd2' nowrap>"+i18n.lms1035m01['tab03.sumDiv.relevant']+"</td>");			
			dyna.push("<td class='' style='padding:8px 16px;'>");
			if(true){
				dyna.push("<table border='0'  >");				
				
				$.each(json.c120m01a_list.key, function(idx, jsonItem) {
					dyna.push("<tr>");
					dyna.push("<td class='noborder' nowrap>"+jsonItem.c120_custId+"-"+jsonItem.c120_dupNo+"</td>");
					dyna.push("<td class='noborder' >"+jsonItem.c120_custName+"</td>");
					dyna.push("<td class='noborder' >"+jsonItem.c120_custPos+"</td>");
					dyna.push("</tr>");
				});	
				dyna.push("</table>");
			}
			dyna.push("</td>");
			dyna.push("</tr>");
			//---------
			dyna.push("<tr>");
			dyna.push("<td class='hd2'>"+i18n.lms1035m01['tab03.sumDiv.annualIncome']+"</td>");
			dyna.push("<td class='' style='padding:8px 16px;'>");
			if(true){
				dyna.push("<table border='0'  >");
				dyna.push("<tr>");
				dyna.push("<td class='noborder' >");
				dyna.push( _build_curr_amt_str(json.localCurr, json.item_p3));
				dyna.push("</td>");
				dyna.push("</tr>");
				dyna.push("</table>");
			}			
			dyna.push("</td>");
			dyna.push("</tr>");

			
			dyna.push("</table>");
			dyna.push("<br/>");
		}
		$("#div03").html(dyna.join(""));		
	}
});

function _build_curr_amt_str(curr, amt){	
	var str =
		"<div style='float:left; width:14px;'>"+curr+"</div>"+
		"<div style='float:left; width:120px; text-align:right; '>"+amt+"</div>";
	
	return str;
}