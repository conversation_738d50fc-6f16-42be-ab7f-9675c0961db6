/* 
 * C900M01D.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 會計科子目名稱檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "C900M01D", uniqueConstraints = @UniqueConstraint(columnNames = { "subjCode" }))
public class C900M01D extends GenericBean implements IDataObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)")
	private String oid;

	/** 會計科子細目 **/
	@Size(max = 8)
	@Column(name = "SUBJCODE", length = 8, columnDefinition = "VARCHAR(8)", nullable = false)
	private String subjCode;

	/** 授信科目 **/
	@Size(max = 4)
	@Column(name = "SUBJCODE2", length = 4, columnDefinition = "VARCHAR(4)")
	private String subjCode2;

	/** 會計科子名稱 **/
	@Size(max = 128)
	@Column(name = "SUBJNM", length = 128, columnDefinition = "VARCHAR(128)")
	private String subjNm;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得會計科子細目 **/
	public String getSubjCode() {
		return this.subjCode;
	}

	/** 設定會計科子細目 **/
	public void setSubjCode(String value) {
		this.subjCode = value;
	}

	/** 取得授信科目 **/
	public String getSubjCode2() {
		return this.subjCode2;
	}

	/** 設定授信科目 **/
	public void setSubjCode2(String value) {
		this.subjCode2 = value;
	}

	/** 取得會計科子名稱 **/
	public String getSubjNm() {
		return this.subjNm;
	}

	/** 設定會計科子名稱 **/
	public void setSubjNm(String value) {
		this.subjNm = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
