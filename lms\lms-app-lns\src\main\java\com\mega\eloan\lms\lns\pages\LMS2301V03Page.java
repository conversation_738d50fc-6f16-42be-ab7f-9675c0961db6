/* 
 * LMS2301V03Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.lns.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;

/**
 * <pre>
 * 簽約未動用授信案件送作業 --已核准
 * </pre>
 * 
 * @since 2011/12/12
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/12/12,REX,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms2301v03")
public class LMS2301V03Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		setGridViewStatus(CreditDocStatusEnum.海外_已核准);
		// 加上Button
		addToButtonPanel(model, LmsButtonEnum.View,LmsButtonEnum.Filter);
		renderJsI18N(LMS2301V01Page.class);
		renderJsI18N(LMS2301V03Page.class);
		model.addAttribute("loadScript", "loadScript('pagejs/lns/LMS2301V03Page');");
	}

}
