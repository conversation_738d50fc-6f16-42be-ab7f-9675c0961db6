/* 
 * L140S02H.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 代償轉貸借新還舊明細檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L140S02H", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "seq", "bankNo", "branchNo", "subACNo" }))
public class L140S02H extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 序號 **/
	@Digits(integer = 5, fraction = 0, groups = Check.class)
	@Column(name = "SEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer seq;

	/** 轉出之原金融機構代碼 **/
	@Size(max = 3)
	@Column(name = "BANKNO", length = 3, columnDefinition = "CHAR(3)")
	private String bankNo;

	/**
	 * 分行代號 ※2013-01-09_修改為VARCHAR(7) (Notes FCLS106M01 OriBranch)
	 **/
	@Size(max = 7)
	@Column(name = "BRANCHNO", length = 7, columnDefinition = "VARCHAR(7)")
	private String branchNo;

	/**
	 * 代償本行帳號
	 * <p/>
	 * ※轉出之原金融機構代碼為017
	 */
	@Size(max = 14)
	@Column(name = "SUBACNO", length = 14, columnDefinition = "VARCHAR(14)")
	private String subACNo;

	/** 行庫名稱 **/
	@Size(max = 60)
	@Column(name = "BANKNAME", length = 60, columnDefinition = "VARCHAR(60)")
	private String bankName;

	/**
	 * 分行名稱 (Notes FCLS106M01 Ori_bank)
	 **/
	@Size(max = 60)
	@Column(name = "BRANCHNAME", length = 60, columnDefinition = "VARCHAR(60)")
	private String branchName;

	/** 代償金額 **/
	@Digits(integer = 13, fraction = 0)
	@Column(name = "SUBAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal subAmt;

	/** 原貸放日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "OLNAPPDATE", columnDefinition = "DATE")
	private Date oLNAppDate;

	/** 原貸款到期日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "OLNENDDATE", columnDefinition = "DATE")
	private Date oLNEndDate;

	/** 剩餘貸款年限(年) **/
	@Digits(integer = 2, fraction = 0)
	@Column(name = "OLNREMYEAR", columnDefinition = "DECIMAL(2,0)")
	private Integer oLNRemYear;

	/** 設定抵押房屋稅籍編號 **/
	@Size(max = 60)
	@Column(name = "TAXNO", length = 60, columnDefinition = "VARCHAR(60)")
	private String taxNo;

	/**
	 * 代償同業房貸原因
	 * <p/>
	 * 單選：<br/>
	 * 1.買賣<br/>
	 * 2.利率<br/>
	 * 3.額度<br/>
	 * 4.其它
	 */
	@Size(max = 1)
	@Column(name = "SUBREASON", length = 1, columnDefinition = "CHAR(1)")
	private String subReason;

	/**
	 * 代償同業房貸原因(其他)
	 * <p/>
	 * 10個全型字
	 */
	@Size(max = 30)
	@Column(name = "SUBREAOTH", length = 30, columnDefinition = "VARCHAR(30)")
	private String subReaOth;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/** 代償產品別 **/
	@Size(max = 30)
	@Column(name = "REPAYMENTPRODUCTTYPE", length = 30, columnDefinition = "CHAR(30)")
	private String repaymentProductType;

	/** 代償產品
	 * * <p/>
	 * 	 * 單選：<br/>
	 * 	 * 1.信用貸款<br/>
	 * 	 * 2.現金卡<br/>
	 * 	 * 3.信用卡<br/>
	 * 	 */
	@Size(max = 1)
	@Column(name = "REPAYMENTPRODUCT", length = 1, columnDefinition = "CHAR(1)")
	private String repaymentProduct;

	/** 帳號 */
	@Size(max = 15)
	@Column(name = "ACCNO", length = 15, columnDefinition = "VARCHAR(15)")
	private String accNo;

	/** 戶名 */
	@Size(max = 120)
	@Column(name = "CUSTNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String custName;

	/** 聯徵原始額度 **/
	@Digits(integer = 13, fraction = 0)
	@Column(name = "ORIGINALAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal originalAmt;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得序號 **/
	public Integer getSeq() {
		return this.seq;
	}

	/** 設定序號 **/
	public void setSeq(Integer value) {
		this.seq = value;
	}

	/** 取得轉出之原金融機構代碼 **/
	public String getBankNo() {
		return this.bankNo;
	}

	/** 設定轉出之原金融機構代碼 **/
	public void setBankNo(String value) {
		this.bankNo = value;
	}

	/** 取得分行代號 **/
	public String getBranchNo() {
		return this.branchNo;
	}

	/** 設定分行代號 **/
	public void setBranchNo(String value) {
		this.branchNo = value;
	}

	/**
	 * 取得代償本行帳號
	 * <p/>
	 * ※轉出之原金融機構代碼為017
	 */
	public String getSubACNo() {
		return this.subACNo;
	}

	/**
	 * 設定代償本行帳號
	 * <p/>
	 * ※轉出之原金融機構代碼為017
	 **/
	public void setSubACNo(String value) {
		this.subACNo = value;
	}

	/** 取得行庫名稱 **/
	public String getBankName() {
		return this.bankName;
	}

	/** 設定行庫名稱 **/
	public void setBankName(String value) {
		this.bankName = value;
	}

	/** 取得分行名稱 **/
	public String getBranchName() {
		return this.branchName;
	}

	/** 設定分行名稱 **/
	public void setBranchName(String value) {
		this.branchName = value;
	}

	/** 取得代償金額 **/
	public BigDecimal getSubAmt() {
		return this.subAmt;
	}

	/** 設定代償金額 **/
	public void setSubAmt(BigDecimal value) {
		this.subAmt = value;
	}

	/** 取得原貸放日期 **/
	public Date getOLNAppDate() {
		return this.oLNAppDate;
	}

	/** 設定原貸放日期 **/
	public void setOLNAppDate(Date value) {
		this.oLNAppDate = value;
	}

	/** 取得原貸款到期日 **/
	public Date getOLNEndDate() {
		return this.oLNEndDate;
	}

	/** 設定原貸款到期日 **/
	public void setOLNEndDate(Date value) {
		this.oLNEndDate = value;
	}

	/** 取得剩餘貸款年限(年) **/
	public Integer getOLNRemYear() {
		return this.oLNRemYear;
	}

	/** 設定剩餘貸款年限(年) **/
	public void setOLNRemYear(Integer value) {
		this.oLNRemYear = value;
	}

	/** 取得設定抵押房屋稅籍編號 **/
	public String getTaxNo() {
		return this.taxNo;
	}

	/** 設定設定抵押房屋稅籍編號 **/
	public void setTaxNo(String value) {
		this.taxNo = value;
	}

	/**
	 * 取得代償同業房貸原因
	 * <p/>
	 * 單選：<br/>
	 * 1.買賣<br/>
	 * 2.利率<br/>
	 * 3.額度<br/>
	 * 4.其它
	 */
	public String getSubReason() {
		return this.subReason;
	}

	/**
	 * 設定代償同業房貸原因
	 * <p/>
	 * 單選：<br/>
	 * 1.買賣<br/>
	 * 2.利率<br/>
	 * 3.額度<br/>
	 * 4.其它
	 **/
	public void setSubReason(String value) {
		this.subReason = value;
	}

	/**
	 * 取得代償同業房貸原因(其他)
	 * <p/>
	 * 10個全型字
	 */
	public String getSubReaOth() {
		return this.subReaOth;
	}

	/**
	 * 設定代償同業房貸原因(其他)
	 * <p/>
	 * 10個全型字
	 **/
	public void setSubReaOth(String value) {
		this.subReaOth = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	public String getRepaymentProductType() {
		return repaymentProductType;
	}

	public void setRepaymentProductType(String repaymentProductType) {
		this.repaymentProductType = repaymentProductType;
	}

	public String getRepaymentProduct() {
		return repaymentProduct;
	}

	public void setRepaymentProduct(String repaymentProduct) {
		this.repaymentProduct = repaymentProduct;
	}

	public String getAccNo() {
		return accNo;
	}

	public void setAccNo(String accNo) {
		this.accNo = accNo;
	}

	public String getCustName() {
		return custName;
	}

	public void setCustName(String custName) {
		this.custName = custName;
	}

	public BigDecimal getOriginalAmt() {
		return originalAmt;
	}

	public void setOriginalAmt(BigDecimal originalAmt) {
		this.originalAmt = originalAmt;
	}
}
