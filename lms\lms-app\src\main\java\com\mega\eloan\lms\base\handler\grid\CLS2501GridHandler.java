/* 
 *  CLS250MGridHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.handler.grid;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;


import com.iisigroup.cap.component.PageParameters;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.pages.LMS140MM01Page;
import com.mega.eloan.lms.base.service.CLS2501Service;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.mfaloan.bean.ELF516;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.mfaloan.service.MisELF516Service;
import com.mega.eloan.lms.mfaloan.service.MisLNF030Service;
import com.mega.eloan.lms.mfaloan.service.MisMISLN20Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C250M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 可疑代辦案件註記作業
 */
@Scope("request")
@Controller("cls2501gridhandler")
public class CLS2501GridHandler extends AbstractGridHandler {

	@Resource
	CLS2501Service cls2501Service;
	@Resource
	UserInfoService userInfoService;
	@Resource
	BranchService branchService;
	@Resource
	DocFileService docFileService;
	@Resource
	CodeTypeService codeTypeService;
	@Resource
	LMSService lmsService;
	@Resource
	MisCustdataService miscustdataservice;
	@Resource
	MisLNF030Service mislnf030service;
	@Resource
	MisMISLN20Service misMISln20Service;
	@Resource
	MisdbBASEService misDbService;
	@Resource
	MisELF516Service misELF516Service;

	/**
	 * 可疑代辦案件註記作業grid
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryC250M01A(ISearch pageSetting,
			PageParameters params) throws CapException {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String docStatus = Util.nullToSpace(params
				.getString(EloanConstants.DOC_STATUS));

		String[] docStatusArray = docStatus
				.split(UtilConstants.Mark.SPILT_MARK);

		pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
				docStatusArray);// 取得文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"c250a01a.authUnit", user.getUnitNo());

		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		
		if(true){ //篩選
			String custId = Util.trim(params.getString("search_custId"));
			if (Util.isNotEmpty(custId)) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
			}	
			//~~~~~~
			String ctrType = Util.trim(params.getString("search_cntrNo"));
			if (Util.isNotEmpty(ctrType)) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", ctrType);
			}			
		}
		
		Page<? extends GenericBean> page = cls2501Service.findPage(
				C250M01A.class, pageSetting);

		List<C250M01A> c250m01alist = (List<C250M01A>) page.getContent();
		StringBuilder cntrNo = new StringBuilder("");
		for (C250M01A c250m01a : c250m01alist) {
			c250m01a.setCreator(userInfoService.getUserName(c250m01a.getCreator()));
			// 設定顯示名稱 使用者id+重複序號
			c250m01a.setCustId(c250m01a.getCustId() + " " + c250m01a.getDupNo());
			cntrNo.setLength(0);
		}
		return new CapGridResult(c250m01alist, page.getTotalRow());

	}

	/**
	 * 查詢可疑代辦案件註記作業grid(已覆核)
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryC250M01A3(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String[] docStatusArray = new String[] { CreditDocStatusEnum.海外_已核准
				.getCode() };
		String type = Util.nullToSpace(params.getString("type"));

		pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus", docStatusArray);// 取得文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "c250a01a.authUnit", user.getUnitNo());
		// 狀態1 是用客戶ID去查
		switch (Util.parseInt(type)) {
		case 1:
			String custId = Util.nullToSpace(params.getString("custId"));
			String dupNo = Util.nullToSpace(params.getString("dupNo"));
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
			break;
		case 2:
			String cntrNo = Util.nullToSpace(params.getString("cntrNo"));
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
			break;
		case 3:
			String approveTime = Util.nullToSpace(params.getString("approveTime"));
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "approveTime", CapDate.parseDate(approveTime));
			break;

		default:
			break;
		}

		Page<? extends GenericBean> page = cls2501Service.findPage(
				C250M01A.class, pageSetting);
		List<C250M01A> c250m01as = (List<C250M01A>) page.getContent();
		StringBuilder cntrNo = new StringBuilder("");
		for (C250M01A c250m01a : c250m01as) {
			// 設定顯示名稱 使用者id+重複序號
			// l140m01m.setCustId(l140m01m.getCustId() + " " +
			// l140m01m.getDupNo());
			cntrNo.setLength(0);
			// 確認全部動用是否有選
		}
		return new CapGridResult(c250m01as, page.getTotalRow());
	}

	/**
	 * 從MIS.ELF516查詢額度序號
	 */

	public CapMapGridResult queryGetCntrno(ISearch pageSetting,
			PageParameters params) throws CapException {
		Properties prop = null;
		prop = MessageBundleScriptCreator.getComponentResource(LMS140MM01Page.class);
		String custId = Util.nullToSpace(params.getString("custId"));
		String dupNo = Util.nullToSpace(params.getString("dupNo"));

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String branch = Util.trim(user.getUnitNo());

		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		List<ELF516> elf516Data = null;

		if (custId.length() == 10) {
			elf516Data = misELF516Service.findByCustId(custId, dupNo);
			for (ELF516 elf516 : elf516Data) {
				Map<String, Object> row = new HashMap<String, Object>();

				String tBrn = Util.trim(elf516.getCntrno()).substring(0, 3);
				if (!tBrn.equals(branch)) {
					// 避免重覆加入一筆空值
					continue;
				}
				String loanNo = "";
				String cntrNo = Util.trim(elf516.getCntrno());
				List<Map<String, Object>> lnf030s = mislnf030service.selaLoanNoByCntrno(cntrNo);

				if (!lnf030s.isEmpty()) {
					for (Map<String, Object> lnf030 : lnf030s) {
						if ("".equals(loanNo)) {
							loanNo = Util.trim(lnf030.get("LNF030_LOAN_NO"));
						} else {
							loanNo = loanNo + "、" + Util.trim(lnf030.get("LNF030_LOAN_NO"));
						}						
					}
				}
				
				row.put("cntrNo", cntrNo);
				row.put("loanNo", loanNo);
				row.put("status", elf516.getStatus()); // EX: A.增額、N.新作
				row.put("statusDes", elf516.getStatus()); // EX: A.增額、N.新作
				row.put("lnflag", elf516.getLnflag()); // 疑似代辦案件訊息
				row.put("lnflagDes", elf516.getLnflag());
				row.put("yyyymm", elf516.getYyyymm()); // 資料年月
				row.put("othermemo", elf516.getOthermemo()); // 取得其他可疑情形
				row.put("branchComm", elf516.getBranchComm()); // 取得其他可疑情形
				list.add(row);
			}
		} 
		return new CapMapGridResult(list, list.size());
	}
	
	/**
	 * 從MIS.ELF516查詢額度序號
	 * @throws IOException 
	 * @throws JsonMappingException 
	 * @throws JsonParseException 
	 */
	public CapMapGridResult queryOverduePaymentRecord(ISearch pageSetting, PageParameters params) throws CapException, JsonParseException, JsonMappingException, IOException {

		String oid = params.getString(EloanConstants.MAIN_OID);
		List<Map<String, String>> list = this.cls2501Service.getOverduePaymentRecordByC250M01A(oid);
		
		List<Map<String, Object>> rtnList = new ArrayList<Map<String, Object>>();
		
		for(Map<String, String> m : list){
			Map<String, Object> row = new HashMap<String, Object>();
			row.put("account", m.get("LOAN_NO"));
			row.put("accountBranch", m.get("BR_NO"));
			row.put("contractNo", m.get("CONTRACT"));
			rtnList.add(row);
		}
				
		return new CapMapGridResult(rtnList, rtnList.size());
	}
}
