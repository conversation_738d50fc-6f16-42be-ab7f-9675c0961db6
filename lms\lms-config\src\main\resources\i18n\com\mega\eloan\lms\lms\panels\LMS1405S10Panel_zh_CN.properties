L120S24A.title=\u98a8\u96aa\u6b0a\u6578
button.L120S24A.import=\u5f15\u9032\u984d\u5ea6\u660e\u7d30\u8868
button.L120S24A.write=\u5beb\u56de\u984d\u5ea6\u660e\u7d30\u8868
button.L120S24A.writeConfirm=\u662f\u5426\u8981\u5beb\u56de\u984d\u5ea6\u660e\u7d30\u8868\u98a8\u96aa\u6b0a\u6578\u6b04\u4f4d?
button.L120S24A.writeNonSelect=\u8acb\u5148\u9078\u64c7\u9700\u5beb\u56de\u4e4b\u8cc7\u6599\u5217
button.L120S24A.writeSuccess=\u5beb\u56de\u6210\u529f
button.L120S24A.printPage=\u5217\u5370\u6b64\u9801
button.L120S24A.delete=\u522a\u9664
button.L120S24A.printL120S24A=\u5217\u5370\u8a66\u7b97\u660e\u7d30

button.L120S24A.rpt.total=\u5408\u3000\u3000\u8a08

#\u932f\u8aa4\u8a0a\u606f
#LMS1401S10Panel
L120S24A.message.gen=\u7522\u751f\u6210\u529f
L120S24A.message.genNoCntrNo=\u6709\u984d\u5ea6\u660e\u7d30\u5c1a\u672a\u53d6\u865f\uff0c\u8acb\u5148\u53d6\u865f\u5f8c\u91cd\u65b0\u7522\u751f\u984d\u5ea6\u8cc7\u8a0a
L120S24A.message.genColumnIncomplete=\u984d\u5ea6\u660e\u7d30{0}\u5c1a\u672a\u8f38\u5165{1}\uff0c\u8acb\u5148\u8f38\u5165\u5f8c\u91cd\u65b0\u7522\u751f\u984d\u5ea6\u8cc7\u8a0a
L120S24A.message.genNoNtCode=\u501f\u6b3e\u4eba{0}\u5c1a\u672a\u8f38\u5165\u570b\u5225\uff0c\u8acb\u5148\u8f38\u5165\u5f8c\u91cd\u65b0\u7522\u751f\u984d\u5ea6\u8cc7\u8a0a
L120S24A.message.changeColumn1=\u984d\u5ea6\u660e\u7d30{0}\u4e2d {1} \u6b04\u4f4d\u5df2\u7570\u52d5
L120S24A.message.changeColumn2=\u8acb\u91cd\u65b0\u8a08\u7b97\u98a8\u96aa\u6b0a\u6578
L120S24A.gen.currentApplyCurr=\u73fe\u8acb\u984d\u5ea6-\u5e63\u5225
L120S24A.gen.currentApplyAmt=\u73fe\u8acb\u984d\u5ea6-\u91d1\u984d
L120S24A.gen.crdGrade=\u5916\u90e8\u4fe1\u8a55\u8a55\u7b49
L120S24A.gen.ntCode=\u570b\u5225
L120S24A.message.changeVersion1=\u98a8\u96aa\u6b0a\u6578\u7248\u672c\u5df2\u66f4\u65b0\uff0c\u8acb\u91cd\u65b0\u8a08\u7b97\u98a8\u96aa\u6b0a\u6578<BR/>\u5408\u683c\u91d1\u878d\u64d4\u4fdd\u54c1\u4e4b\u64d4\u4fdd\u54c1\u8a55\u7b49\u9810\u8a2d\u70baBB+ ~ BB-\uff0c\u8acb\u78ba\u8a8d\u662f\u5426\u9700\u8981\u8abf\u6574
L120S24A.gen.specialFinRiskType_s24a=\u7279\u6b8a\u878d\u8cc7\u985e\u5225
L120S24A.message.L120S24A.LTVFormatError=LTV \u6b04\u4f4d\u8acb\u8f38\u5165\u6578\u5b57
L120S24A.message.L120S24A.oppFormatError=\u5c1a\u672a\u5f15\u5165 \u4ea4\u6613\u5c0d\u624b\u7121\u64d4\u4fdd\u66b4\u96aa\u98a8\u96aa\u6b0a\u6578
L120S24A.message.L120S24D.findError=\u7121\u6cd5\u53d6\u5f97LTV\u98a8\u96aa\u6b0a\u6578\u8a08\u7b97\u5c0d\u7167\u8868
L120S24A.message.L120S24A.GutClassOtherDesc.empty=\u4fdd\u8b49\u985e\u578b\u70ba\u5176\u4ed6\uff0c\u8acb\u586b\u5beb\u4fdd\u8b49\u985e\u5225_\u5176\u4ed6\u8aaa\u660e\u5f8c\u518d\u9ede\u9078\u5132\u5b58
L120S24A.message.L120S24A.GutClassOtherDesc.empty2=\u4fdd\u8b49\u985e\u578b\u70ba\u5176\u4ed6\uff0c\u8acb\u586b\u5beb\u4fdd\u8b49\u985e\u5225_\u5176\u4ed6\u8aaa\u660e\u5f8c\u518d\u9ede\u9078\u8a08\u7b97
L120S24A.message.L120S24A.differentBowrrowerClass=\u8207\u984d\u5ea6\u5e8f\u865f{0}\u70ba\u540c\u4e00\u501f\u6b3e\u4eba\uff0c\u4f46\u9078\u64c7\u7684\u501f\u6b3e\u4eba\u985e\u5225\u4e0d\u4e00\u81f4\uff0c\u8acb\u78ba\u8a8d\u501f\u6b3e\u4eba\u985e\u5225\u5f8c\u518d\u9ede\u9078\u8a08\u7b97<BR/>\u5982\u9700\u8abf\u6574\u5176\u4ed6\u984d\u5ea6\u5e8f\u865f\u4e4b\u501f\u6b3e\u4eba\u985e\u5225\uff0c\u8acb\u5148\u5132\u5b58\u672c\u7b46\u8cc7\u6599
L120S24A.message.L120S24A.currentApplyAmtCanNotZero=\u73fe\u8acb\u984d\u5ea6-\u91d1\u984d\u4e0d\u53ef\u70ba0\uff0c\u8acb\u4fee\u6539\u91d1\u984d\u5f8c\u518d\u9ede\u9078\u8a08\u7b97
L120S24A.LTVRW_s24a=LTV\u6cd5\u98a8\u96aa\u6b0a\u6578
L120S24A.gutPercent_s24a=\u4fdd\u8b49\u6210\u6578
L120S24A.message.numberTooBig={0}\u6b04\u4f4d\u53ea\u53ef\u8f38\u5165{1}~{2}
L120S24A.message.numberNeedMore={0}\u9700>={1}%
L120S24A.cal.success=\u8a08\u7b97\u6210\u529f

#LMS1401S10PageVXXXXX\u5167\u7684\u52d5\u4f5c
L120S24A.error1=\u7121\u6cd5\u53d6\u5f97DW\u98a8\u96aa\u8a55\u5206\u8cc7\u6599
L120S24A.error2=\u5c1a\u672a\u8a2d\u5b9a\u501f\u6b3e\u4eba\u570b\u7c4d

L120S24A.CrdType0=\u7121
L120S24A.CrdTypeNM=Moody's
L120S24A.CrdTypeNS=S&P
L120S24A.CrdTypeNF=Fitch
L120S24A.CrdTypeNC=\u4e2d\u83ef\u4fe1\u8a55
L120S24A.CrdTypeNT=Fitch\u53f0\u7063

#grid column
#L120S24A.grid.custId_s24a=\u7d71\u4e00\u7de8\u865f
#L120S24A.grid.dupNo_s24a=\u91cd\u8907\u5e8f\u865f
#L120S24A.grid.custName_s24a=\u5ba2\u6236\u59d3\u540d
L120S24A.grid.unitThousand=\u55ae\u4f4d:\u4edf\u5143
L120S24A.grid.cntrNo_s24a=\u984d\u5ea6\u5e8f\u865f
L120S24A.grid.currentApplyCurr_s24a=\u5e63\u5225
L120S24A.grid.currentApplyAmt_s24a=\u73fe\u8acb\u984d\u5ea6<BR/>(A)
L120S24A.grid.ccf_s24a=CC<BR/>F
L120S24A.grid.ccfAmt_s24a=\u7d14\u8868\u5916\u4e4b<BR/>\u4fe1\u7528\u76f8\u7576<BR/>\u984d<BR/>(B)=(A)x<BR/>CCF
L120S24A.grid.specialFinRiskType_s24a=\u7279<BR/>\u6b8a<BR/>\u878d<BR/>\u8cc7
L120S24A.grid.hasEstate_s24a=LT<BR/>V<BR/>\u6cd5
L120S24A.grid.isCBControl_s24a=\u592e<BR/>\u884c<BR/>\u7ba1<BR/>\u5236
L120S24A.grid.beforeDeductRW_s24a=\u62b5\u6e1b<BR/>\u524d\u98a8<BR/>\u96aa\u6b0a<BR/>\u6578
#\u5408\u683c\u64d4\u4fdd\u54c1\u50f9\u503c\u5df2\u6539\u70ba\u62b5\u6e1b
#L120S24A.grid.totalCollAmt_s24a=\u5408\u683c<BR/>\u64d4\u4fdd\u54c1<BR/>\u50f9\u503c(C)
L120S24A.grid.totalDisCollAmt_s24a=\u5408\u683c<BR/>\u64d4\u4fdd\u54c1<BR/>\u62b5\u6e1b<BR/>\u91d1\u984d(C)
L120S24A.grid.calDisCollExposureAmt_s24a=\u5408\u683c\u64d4\u4fdd<BR/>\u54c1\u62b5\u6e1b\u5f8c<BR/>\u66b4\u96aa\u984d<BR/>(D)=Max[<BR/>(A)-(C),0]<BR/> \u6216Max[<BR/>(B)-(C),0]
L120S24A.grid.hasGutClass_s24a=\u4fe1<BR/>\u4fdd<BR/>\u6a5f<BR/>\u69cb
L120S24A.grid.calHasGutDeptRWA_s24a=\u4fe1\u4fdd\u90e8<BR/>\u4f4d\u98a8\u96aa<BR/>\u6027\u8cc7\u7522<BR/>(E)
L120S24A.grid.calNoGutRWA_s24a=\u975e\u4fe1\u4fdd<BR/>\u90e8\u4f4d\u98a8<BR/>\u96aa\u6027\u8cc7<BR/>\u7522(F)
L120S24A.grid.calDeductRWA_s24a=\u62b5\u6e1b\u5f8c\u98a8<BR/>\u96aa\u6027\u8cc7\u7522<BR/>(G)=(E)+<BR/>(F)
L120S24A.grid.calDeductRW_s24a=\u62b5\u6e1b\u5f8c\u98a8<BR/>\u96aa\u6b0a\u6578<BR/>=(G)/(A)

L120S24A.grid.bowrrowerClass.no=No
L120S24A.grid.bowrrowerClass.id=ID
L120S24A.grid.bowrrowerClass.name=\u6236\u540d

LMS1401S10Form01.saveData=\u5132\u5b58
LMS1401S10Form01.close=\u96e2\u958b
LMS1401S10Form01.explain.title=\u8aaa\u660e

#\u7d66js\u4f7f\u7528\uff0c\u6c92\u653e\u5728PageVXXXX.properties\u88e1
L120S24A.head1=\u984d\u5ea6\u5e8f\u865f
L120S24A.head2=\u98a8\u96aa\u6b0a\u6578\u8a66\u7b97
L120S24A.CCFTitle=\u8868\u5916\u9805\u76ee\u4fe1\u7528\u8f49\u63db\u4fc2\u6578 \u8aaa\u660e
L120S24A.bowrrowerClass=\u501f\u6b3e\u4eba\u985e\u5225 \u8aaa\u660e
L120S24A.specialFinRiskType=\u7279\u6b8a\u878d\u8cc7\u985e\u5225 \u8aaa\u660e
L120S24B.quaCollDetail=\u5408\u683c\u91d1\u878d\u64d4\u4fdd\u54c1
L120S24B.quaColl_s24b=\u5408\u683c\u91d1\u878d\u64d4\u4fdd\u54c1
L120S24B.collCurr_s24b=\u64d4\u4fdd\u54c1\u5e63\u5225
L120S24B.collAmt_s24b=\u64d4\u4fdd\u54c1\u50f9\u503c
L120S24B.currSym_s24b=\u5e63\u5225\u5c0d\u7a31<BR/>(\u64d4\u4fdd\u54c1\u5e63\u5225\u8207\u984d\u5ea6\u76f8\u540c)
L120S24B.disCollAmt_s24b=\u6298\u6263\u5f8c\u64d4\u4fdd\u54c1\u50f9\u503c
L120S24B.disCollAmt_TWD_s24b=\u6298\u6263\u5f8c\u64d4\u4fdd\u54c1\u50f9\u503c(TWD)
