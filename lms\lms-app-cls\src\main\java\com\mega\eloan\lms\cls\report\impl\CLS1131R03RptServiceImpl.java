package com.mega.eloan.lms.cls.report.impl;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.cls.report.CLS1131R03RptService;

import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.report.ReportGenerator;
import tw.com.jcs.common.report.SubReportParam;

/**
 * <pre>
 * 借保人徵信資料
 * </pre>
 * 
 * @since 2012/11/12
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/11/12,Fantasy,new
 *          <li>2013/06/15,調整主借款人和配偶之利害關係人顯示排序問題
 *          <li>2013/07/11,Rex,修改顯示評等
 *          <li>2013/07/17,Rex,修改判斷非兆豐行庫改用判斷其帳戶為14碼的排除加總
 *          </ul>
 */
@Service("cls1131r03rptservice")
public class CLS1131R03RptServiceImpl implements FileDownloadService,
		CLS1131R03RptService {

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(CLS1131R03RptServiceImpl.class);

	/*
	 * (non-Javadoc) 呈現在頁面用的
	 * 
	 * @see
	 * com.mega.eloan.lms.base.service.FileDownloadService#getContent(org.apache
	 * .wicket.PageParameters)
	 */
	@Override
	public byte[] getContent(PageParameters params)
			throws FileNotFoundException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
//			baos = (ByteArrayOutputStream) this.generateReport(params);
//			return baos.toByteArray();
			// J-109-0148 改為產生JSONObject
			return this.generateJSONObject(params);
		} finally {
			if (baos != null) {
				baos.close();
			}

		}
	}

	/**
	 * 建立PDF
	 * 
	 * @param params
	 *            params
	 * @return OutputStream OutputStream
	 * @throws Exception
	 * @throws IOException
	 * @throws FileNotFoundException
	 */
	@SuppressWarnings("unchecked")
	public OutputStream generateReport(PageParameters params)
			throws FileNotFoundException, IOException, Exception {

		List<Map<String, String>> rows1 = new ArrayList<Map<String, String>>();
		List<Map<String, String>> rows2 = new ArrayList<Map<String, String>>();
		List<Map<String, String>> rows3 = new ArrayList<Map<String, String>>();

		OutputStream outputStream = null;
		Locale locale = null;
		Map<String, String> rptVariableMap = null;

		try {
			locale = LMSUtil.getLocale();
			rptVariableMap = new LinkedHashMap<String, String>();
			ReportGenerator generator = new ReportGenerator(
					"report/cls/CLS1131R03_" + locale.toString() + ".rpt");

			List<Map<String, Object>> gridview1 = (List<Map<String, Object>>) params
					.get("gridview1");
			List<Map<String, Object>> gridview2 = (List<Map<String, Object>>) params
					.get("gridview2");
			List<Map<String, Object>> gridview3 = (List<Map<String, Object>>) params
					.get("gridview3");
			// 本行往來戶負面信用資料查詢(發查聯徵負面信用資訊紀錄)
			for (Map<String, Object> m : gridview1) {
				Map<String, String> map = new HashMap<String, String>();
				map.put("CommonBean1.field01", (String) m.get("CUSTID"));
				map.put("CommonBean1.field02", (String) m.get("CUST_NAME"));
				map.put("CommonBean1.field03", (String) m.get("DATA_FROM"));
				map.put("CommonBean1.field04", (String) m.get("FROM_FLAG"));
				map.put("CommonBean1.field05", (String) m.get("BANK_CODE"));
				map.put("CommonBean1.field06", (String) m.get("CREATE_DT"));
				map.put("CommonBean1.field07", (String) m.get("DUE_DATE"));
				map.put("CommonBean1.field08", (String) m.get("BAD_CLASS"));
				map.put("CommonBean1.field09", (String) m.get("BAD_SWFT"));
				map.put("CommonBean1.field10", (String) m.get("BAD_AMT"));
				map.put("CommonBean1.field11", (String) m.get("DECRIPTION"));
				rows1.add(map);
			}
			// 拒往戶資料查詢(2007年1月以後票交所拒往紀錄)
			for (Map<String, Object> m : gridview2) {
				Map<String, String> map = new HashMap<String, String>();
				map.put("CommonBean1.field01", (String) m.get("CUSTID"));
				map.put("CommonBean1.field02", (String) m.get("CUST_NAME"));
				map.put("CommonBean1.field03", (String) m.get("DATA_FROM"));
				map.put("CommonBean1.field04", (String) m.get("FROM_FLAG"));
				map.put("CommonBean1.field05", (String) m.get("DNGERDATE"));
				map.put("CommonBean1.field06", (String) m.get("DNGER_D_DATE"));
				rows2.add(map);
			}
			// 本行往來戶退票戶資料查詢(票交所近7年退票資料)
			for (Map<String, Object> m : gridview3) {
				Map<String, String> map = new HashMap<String, String>();
				map.put("CommonBean1.field01", (String) m.get("CUSTID"));
				map.put("CommonBean1.field02", (String) m.get("CUST_NAME"));
				map.put("CommonBean1.field03", (String) m.get("DATA_FROM"));
				map.put("CommonBean1.field04", (String) m.get("FROM_FLAG"));
				map.put("CommonBean1.field05", (String) m.get("REJ_DATE"));
				map.put("CommonBean1.field06", (String) m.get("REJ_CNT"));
				map.put("CommonBean1.field07", (String) m.get("REJ_SWFT"));
				map.put("CommonBean1.field08", (String) m.get("REJ_AMT"));
				rows3.add(map);
			}

			rptVariableMap.put("totalRecords", String.valueOf(rows1.size()));
			rptVariableMap.put("queryMan", params.getString("queryMan"));
			generator.setVariableData(rptVariableMap);

			// 子報表設定
			SubReportParam subReportParam = new SubReportParam();
			subReportParam.setData(0, rptVariableMap, rows1);
			subReportParam.setData(1, rptVariableMap, rows2);
			subReportParam.setData(2, rptVariableMap, rows3);
			generator.setSubReportParam(subReportParam);

			LOGGER.info("into generateReport");
			outputStream = generator.generateReport();
			LOGGER.info("exit generateReport");

		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
		}

		return outputStream;
	}

	/**
	 * 建立JSONObject
	 * 
	 * @param params
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public byte[] generateJSONObject(PageParameters params) throws Exception {

		List<Map<String, String>> rows1 = new ArrayList<Map<String, String>>();
		List<Map<String, String>> rows2 = new ArrayList<Map<String, String>>();
		List<Map<String, String>> rows3 = new ArrayList<Map<String, String>>();

		Map<String, String> rptVariableMap = null;
		JSONObject json = new JSONObject();

		try {
			rptVariableMap = new LinkedHashMap<String, String>();

			List<Map<String, Object>> gridview1 = (List<Map<String, Object>>) params
					.get("gridview1");
			List<Map<String, Object>> gridview2 = (List<Map<String, Object>>) params
					.get("gridview2");
			List<Map<String, Object>> gridview3 = (List<Map<String, Object>>) params
					.get("gridview3");
			// 本行往來戶負面信用資料查詢(發查聯徵負面信用資訊紀錄)
			for (Map<String, Object> m : gridview1) {
				Map<String, String> map = new HashMap<String, String>();
				map.put("td0", (String) m.get("CUSTID"));
				map.put("td1", (String) m.get("CUST_NAME"));
				map.put("td2", (String) m.get("DATA_FROM"));
				map.put("td3", (String) m.get("FROM_FLAG"));
				map.put("td4", (String) m.get("BANK_CODE"));
				map.put("td5", (String) m.get("CREATE_DT"));
				map.put("td6", (String) m.get("DUE_DATE"));
				map.put("td7", (String) m.get("BAD_CLASS"));
				map.put("td8", (String) m.get("BAD_SWFT"));
				map.put("td9", (String) m.get("BAD_AMT"));
				map.put("td10", (String) m.get("DECRIPTION"));
				rows1.add(map);
			}
			// 拒往戶資料查詢(2007年1月以後票交所拒往紀錄)
			for (Map<String, Object> m : gridview2) {
				Map<String, String> map = new HashMap<String, String>();
				map.put("td0", (String) m.get("CUSTID"));
				map.put("td1", (String) m.get("CUST_NAME"));
				map.put("td2", (String) m.get("DATA_FROM"));
				map.put("td3", (String) m.get("FROM_FLAG"));
				map.put("td4", (String) m.get("DNGERDATE"));
				map.put("td5", (String) m.get("DNGER_D_DATE"));
				rows2.add(map);
			}
			// 本行往來戶退票戶資料查詢(票交所近7年退票資料)
			for (Map<String, Object> m : gridview3) {
				Map<String, String> map = new HashMap<String, String>();
				map.put("td0", (String) m.get("CUSTID"));
				map.put("td1", (String) m.get("CUST_NAME"));
				map.put("td2", (String) m.get("DATA_FROM"));
				map.put("td3", (String) m.get("FROM_FLAG"));
				map.put("td4", (String) m.get("REJ_DATE"));
				map.put("td5", (String) m.get("REJ_CNT"));
				map.put("td6", (String) m.get("REJ_SWFT"));
				map.put("td7", (String) m.get("REJ_AMT"));
				rows3.add(map);
			}

			rptVariableMap.put("totalRecords", String.valueOf(rows1.size()));
			rptVariableMap.put("queryMan", params.getString("queryMan"));
			json.put("date",
					CapDate.getCurrentDate(UtilConstants.DateFormat.YYYY_MM_DD));
			json.put("rows1", rows1);
			json.put("rows2", rows2);
			json.put("rows3", rows3);
			json.putAll(rptVariableMap);

		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
		}

		return json.toString().getBytes("utf-8");
	}
}
