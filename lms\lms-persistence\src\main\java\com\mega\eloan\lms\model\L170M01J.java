/* 
 * L170M01J.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/** 覆審考評表明細檔 **/
@NamedEntityGraph(name = "L170M01J-entity-graph", attributeNodes = { @NamedAttributeNode("l170m01a") })
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L170M01J", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L170M01J extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * JOIN條件 L170M01A 關聯
	 *
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "MAINID", referencedColumnName = "MAINID", insertable = false, updatable = false)
	private L170M01A l170m01a;

	public void setL170m01a(L170M01A l170m01a) {
		this.l170m01a = l170m01a;
	}

	public L170M01A getL170m01a() {
		return l170m01a;
	}

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 
	 * 文件編號<p/>
	 * L170M01A.mainId
	 */
	@Size(max=32)
	@Column(length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 項目欄位名稱 **/
	@Size(max=30)
	@Column(name="ITEMNAME", length=30, columnDefinition="VARCHAR(30)")
	private String itemName;

	/** 
	 * 項目類別<p/>
	 * YN:是否CNT:數量
	 */
	@Size(max=3)
	@Column(name="ITEMTYPE", length=3, columnDefinition="VARCHAR(3)")
	private String itemType;

	/** 項目說明 **/
	@Size(max=4096)
	@Column(name="DSCR", length=4096, columnDefinition="VARCHAR(4096)")
	private String dscr;

	/** 項目- YN **/
	@Size(max=1)
	@Column(name="ITEMYN", length=1, columnDefinition="VARCHAR(1)")
	private String itemYn;

	/** 項目- CNT **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="ITEMCNT", columnDefinition="DECIMAL(3,0)")
	private Integer itemCnt;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String Creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 
	 * 取得文件編號<p/>
	 * L170M01A.mainId
	 */
	public String getMainId() {
		return this.mainId;
	}
	/**
	 *  設定文件編號<p/>
	 *  L170M01A.mainId
	 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得項目欄位名稱 **/
	public String getItemName() {
		return this.itemName;
	}
	/** 設定項目欄位名稱 **/
	public void setItemName(String value) {
		this.itemName = value;
	}

	/** 
	 * 取得項目類別<p/>
	 * YN:是否CNT:數量
	 */
	public String getItemType() {
		return this.itemType;
	}
	/**
	 *  設定項目類別<p/>
	 *  YN:是否CNT:數量
	 **/
	public void setItemType(String value) {
		this.itemType = value;
	}

	/** 取得項目說明 **/
	public String getDscr() {
		return this.dscr;
	}
	/** 設定項目說明 **/
	public void setDscr(String value) {
		this.dscr = value;
	}

	/** 取得項目- YN **/
	public String getItemYn() {
		return this.itemYn;
	}
	/** 設定項目- YN **/
	public void setItemYn(String value) {
		this.itemYn = value;
	}

	/** 取得項目- CNT **/
	public Integer getItemCnt() {
		return this.itemCnt;
	}
	/** 設定項目- CNT **/
	public void setItemCnt(Integer value) {
		this.itemCnt = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.Creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.Creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
