/* 
 * L180R54ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.Date;
import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L180R54ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L180R54A;

/** 企金兆元振興融資方案月檔 **/
@Repository
public class L180R54ADaoImpl extends LMSJpaDao<L180R54A, String> implements
		L180R54ADao {

	@Override
	public L180R54A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L180R54A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L180R54A> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L180R54A> findByIndex01(String mainId) {
		ISearch search = createSearchTemplete();
		List<L180R54A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L180R54A> findByIndex02(String isRevive, Date endDate) {
		ISearch search = createSearchTemplete();
		List<L180R54A> list = null;
		if (isRevive != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "isRevive",
					isRevive);
		if (endDate != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "endDate",
					endDate);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L180R54A> findByIndex03(String isRevive, String cltType,
			Date endDate) {
		ISearch search = createSearchTemplete();
		List<L180R54A> list = null;
		if (isRevive != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "isRevive",
					isRevive);
		if (cltType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "cltType",
					cltType);
		if (endDate != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "endDate",
					endDate);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L180R54A> findByIndex04(String cntrMainId) {
		ISearch search = createSearchTemplete();
		List<L180R54A> list = null;
		if (cntrMainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "cntrMainId",
					cntrMainId);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	public L180R54A findByMainIdAndCntrMainId(String mainId, String cntrMainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrMainId",
				cntrMainId);
		return findUniqueOrNone(search);
	}
}