i18n.def['cancelSelect'] = "清除重填";
var buttons = {
    "saveData":function(){
        saveData();
    },
    "cancelSelect": function(){
        resetPersonalIncomeDetailForm();
    },
    "print": function(){
        saveData().done(function(){
            $.form.submit({
                url: webroot + '/app/simple/FileProcessingService',
                target: "_blank",
                data: $.extend(CLS1131S01.data, {
                    fileDownloadName: 'cls1131r08.pdf',
                    serviceName: 'cls1131r08rptservice',
                    isC120M01A: CLS1131S01.isC120M01A
                })
            });
        })
    },
    "close": function(){
        $.thickbox.close();
    }
}

if(CLS1131S01.readOnly || CLS1131S01.isC120M01A){
    delete buttons["saveData"];
    delete buttons["cancelSelect"];
}

function resetPersonalIncomeDetailForm(){

    var pidForm = $("#personalIncomeDetailFormV1");
    pidForm.reset();
    pidForm.find(".personalIncomeDetail").hide();
    pidForm.find("input").filter(":not(.readonly,.editable)").attr("disabled", true).attr("readOnly", true).css("background-color", "#E0E0E0");
    pidForm.find('.readonly').readOnly();
}

var saveData = function(){
    var deferred = $.Deferred();

    if(CLS1131S01.readOnly || CLS1131S01.isC120M01A){
        deferred.resolve();
    } else {
        var pidForm = $("#personalIncomeDetailFormV1");
        var checkSize = $("input[name^='hasItem']:checked").size();

        if(pidForm.valid() && checkSize > 0){
             var salaryStructure = $("input[name='salaryStructure']:checked").val();
             var positionType = $("input[name='positionType']:checked").val();
             if(salaryStructure == "1"){
                if(positionType != "1" && positionType != "2"){
                    API.showErrorMessage("固定薪只可選擇職位別為「非藍領 或 藍領」");
                    return false;
                }
             } else if (salaryStructure == "2"){
                if(positionType != "3"){
                    API.showErrorMessage("業務職只可選擇職位別為「業務職(底薪+獎金者)」");
                    return false;
                }
             }

             $.ajax({
                handler: CLS1131S01.handler,
                action: 'savePersonalIncomeDetailV1',
                data: $.extend({
                    custId:CLS1131S01.data.custId,
                    dupNo:CLS1131S01.data.dupNo,
                    mainId:CLS1131S01.data.mainId

                },pidForm.serializeData()),
                success: function(json){
                    pidForm.injectData(json);
                    API.showPopMessage(i18n.def.saveSuccess);
                    deferred.resolve();
                }
            });
        } else {
            if(checkSize == 0){
                API.showErrorMessage("A~H(複選)選項至少需填列一項");
            }
            $("input.data-error").eq(0).focus();
        }
    }

    return deferred.promise();
}

function resetPersonalIncomeDetailView(){
    var pidForm = $("#personalIncomeDetailFormV1");
    resetPersonalIncomeDetailForm();
    pidForm.find("input[name='salaryStructure']").click(function(){
       var value = $(this).val();
       if(value == "2") {
           $("input[name='positionType']").filter("[value='3']").attr("checked", true);
       }
    })
    pidForm.find("input[name='salaryStructure'],input[name='has1year'],input[name='positionType']").click(function(){

        //一、無論何種薪資結構，只要年資滿一年均按本表計算邏輯。
        // 二、如固定薪，年資未滿一年者：
        // 　　B：薪轉轉摺/薪資單，B1欄位改為3格
        // 三、如為業務職，年資未滿一年者：
        // 　　B：薪轉轉摺/薪資單，B1欄位改為6格
//        alert($("input[name='salaryStructure']").is(":checked"));
//        alert($("input[name='has1year']").is(":checked"));
        if($("input[name='salaryStructure']").is(":checked") && $("input[name='has1year']").is(":checked") && $("input[name='positionType']").is(":checked")){

            var salaryStructure = $("input[name='salaryStructure']:checked").val();
            var has1year = $("input[name='has1year']:checked").val();

            pidForm.find(".personalIncomeDetail").show();


            $("#itemBCase1,#itemBCase2,#itemBCase3").hide();
            if(has1year == "Y"){
                $("#itemBCase1,#itemBCase2,#itemBCase3").show();
            } else {
                if(salaryStructure == "1"){
                    $("#itemBCase1").show();
                } else if(salaryStructure == "2") {
                    $("#itemBCase1,#itemBCase2").show();
                }
            }
        }

    })

    $(["A","B","C","D","E","F","G","H"]).each(function(i, val){
        var input = val;
        pidForm.find("input[name='hasItem" + input + "']").click(function(){

            pidForm.find(".incomeType" + input + " input").filter(":not(.readonly,.editable)").attr("disabled", true).attr("readOnly", true).css("background-color", "#E0E0E0");

            if($(this).is(":checked")){
                pidForm.find(".incomeType" + input + " input").filter(":not(.readonly,.editable)").attr("disabled", false).attr("readOnly", false).css("background-color", "white");
            } else {}
        });
        pidForm.find("input[name='incomeType" + input + "']").triggerHandler("click")
    })

}
resetPersonalIncomeDetailView();


$.ajax({
    handler: CLS1131S01.handler,
    action: 'getPersonalIncomeDetailWithVersion',
    data:  {
        custId:CLS1131S01.data.custId,
        dupNo:CLS1131S01.data.dupNo,
        mainId:CLS1131S01.data.mainId
    },
    success: function(response){
        var pidForm = $("#personalIncomeDetailFormV1");
        $("#personalIncomeDetailFormV1").injectData(response);
        if($("input[name='salaryStructure']").is(":checked") && $("input[name='has1year']").is(":checked") && $("input[name='positionType']").is(":checked")){
            pidForm.find("input[name='salaryStructure'],input[name='has1year']").triggerHandler("click")
        }
        if(needReset){
            resetPersonalIncomeDetailForm();
        }
        $("#personalIncomeDetailViewVer").thickbox({
            open:function(){
                if(CLS1131S01.readOnly || CLS1131S01.isC120M01A){
                    pidForm.lockDoc()
                }
            },
            width:950,
            height:600,
            buttons:buttons
        })
    }
});

$("#incomeNote1").click(function(){
    $("#incomeNote1Desc").thickbox({
        width:640,
        height:480,
        modal:false
    });
});

function build_respDW_OTS_TRPAYLG(json){
	var dyna = [];
	
	if(json && json.list_hasData=="Y"){
		dyna.push("<div class=''>"+json.begDate+"~"+json.endDate+" 共有 "+json.list_data.arr_detail.length+" 筆資料</div>");
	}
	if(json && json.list_hasData=="N"){
		dyna.push("<div class='text-red'>"+json.begDate+"~"+json.endDate+" 查無資料</div>");
	}	
	//==========
	dyna.push("<div style='overflow:auto' >");
	dyna.push("<table border='1' width='100%' class='tb2 '>");
	dyna.push("<tr class='hd2 ' >");
	dyna.push("<td>交易分行&nbsp;</td>");
	dyna.push("<td style='width:85px;'>交易日期&nbsp;</td>");
	dyna.push("<td>代發薪資公司統編&nbsp;</td>");
	dyna.push("<td style='width:200px;'>代發公司名稱&nbsp;</td>");
	dyna.push("<td>交易機號&nbsp;</td>");
	dyna.push("<td>入薪帳號&nbsp;</td>");
	dyna.push("<td style='width:100px;'>金額&nbsp;</td>");
	dyna.push("</tr>");						
	if(json && json.list_hasData=="Y"){
		$.each( json.list_data.arr_detail, function(idx, item){
			dyna.push(item.is_even=="N"?"<tr>":"<tr style='background-color:lavender;'>");
			dyna.push("<td>"+item.BR_CD+"&nbsp;</td>");	
			dyna.push("<td>"+item.TX_ACCT_DT+"&nbsp;</td>");
			dyna.push("<td>"+item.TAX_NO+"&nbsp;</td>");	
			dyna.push("<td>"+item.TAX_NM+"&nbsp;</td>");
			dyna.push("<td>"+item.TRM_ID+"&nbsp;</td>");	
			dyna.push("<td>"+item.ACCT_KEY+"&nbsp;</td>");
			dyna.push("<td style='text-align:right;'>"+item.TX_AMT+"&nbsp;</td>");
			dyna.push("</tr>");
		});
	}else{
		dyna.push("<tr>");
		dyna.push("<td>&nbsp;</td>");
		dyna.push("<td>&nbsp;</td>");
		dyna.push("<td>&nbsp;</td>");
		dyna.push("<td>&nbsp;</td>");
		dyna.push("<td>&nbsp;</td>");;
		dyna.push("<td>&nbsp;</td>");;
		dyna.push("<td>&nbsp;</td>");
		dyna.push("</tr>");
	}
	dyna.push("</table>");
	//==========
	dyna.push("&nbsp;");
	//==========
	dyna.push("<table border='1' style='width:440px;' class='tb2 '>");
	dyna.push("<tr class='hd2 ' >");
	dyna.push("<td style='width:120px;'>代發薪資公司統編&nbsp;</td>");	
	dyna.push("<td style='width:200px;'>代發公司名稱&nbsp;</td>");
	dyna.push("<td style='width:120px;'>查詢期間加總金額&nbsp;</td>");
	dyna.push("</tr>");						
	if(json && json.list_hasData=="Y"){
		$.each( json.list_data.arr_summary, function(idx, item){
			dyna.push("<tr>");
			dyna.push("<td>"+item.TAX_NO+"&nbsp;</td>");	
			dyna.push("<td>"+item.TAX_NM+"&nbsp;</td>");
			dyna.push("<td style='text-align:right;'>"+item.TX_AMT+"&nbsp;</td>");
			dyna.push("</tr>");	
		});
	}else{
		dyna.push("<tr>");
		dyna.push("<td>&nbsp;</td>");
		dyna.push("<td>&nbsp;</td>");
		dyna.push("<td>&nbsp;</td>");
		dyna.push("</tr>");
	}
	dyna.push("</table>");
	//==========
	dyna.push("</div>");
	$("#respDW_OTS_TRPAYLG").html(dyna.join("\n"));
}

$("#btnThickBoxDW_OTS_TRPAYLG").click(function(){
	var itemBvalue_cnt = $("#personalIncomeDetailFormV1").find("[id^=itemBvalue][id!=itemBvalueYear]:visible").length; // 在 CLS1131FormHandler.java 查找  jsObject.put("itemBvalue" + i
	$.ajax({
	    handler: CLS1131S01.handler,
	    action: 'defaultParam_OTS_TRPAYLG',
	    data: {
	        custId:CLS1131S01.data.custId,
	        dupNo:CLS1131S01.data.dupNo,
	        mainId:CLS1131S01.data.mainId,
	        itemBvalue_cnt: itemBvalue_cnt
	    },
	    success: function(json){
	    	$("#formDW_OTS_TRPAYLG").injectData({'trpaylg_id': CLS1131S01.data.custId, 'trpaylg_name':CLS1131S01.data.custName, 'trpaylg_begDate':json.begDate, 'trpaylg_endDate':json.endDate});
	    	build_respDW_OTS_TRPAYLG();
	    	
	    	var ptaFlag_val = $("input[name=ptaFlag]:checked").val();
	    	var ptaFlag_desc = "";
	    	if(ptaFlag_val=="Y"){
	    		ptaFlag_desc = "是";
	    	}else if(ptaFlag_val=="N"){
	    		ptaFlag_desc = "否";
	    	}
	        $("#divQueryDW_OTS_TRPAYLG").thickbox({
	        	title: CLS1131S01.data.custId+' '+CLS1131S01.data.custName+" "+("(薪轉戶註記："+ptaFlag_desc+"，資料日期："+ ($("#ptaDataDt").val()||'')+")"),
	        	width:850,
	            height:480,
	            modal:false
	        });
	    }
	});	
});


$("#btnQueryDW_OTS_TRPAYLG").click(function(){
	if( $("#formDW_OTS_TRPAYLG").valid() ){
		MegaApi.confirmMessage("是否以「個資查詢理由：08-其他作業前之客戶資料查詢」查詢客戶薪轉記錄？"+"<br/>"
				+"查詢記錄將會留存，並顯示在 LLDCMILG 個資查詢交易記錄報表 ", function(r){
            if (r) {
        		$.ajax({
        		    handler: CLS1131S01.handler,
        		    action: 'log_OTS_TRPAYLG',
        		    data:  $.extend({
        		        custId:CLS1131S01.data.custId,
        		        dupNo:CLS1131S01.data.dupNo,
        		        mainId:CLS1131S01.data.mainId,
        		        inqcode:'08'
        		    }, $("#formDW_OTS_TRPAYLG").serializeData() ),
        		    success: function(log_response){
        		    	$.ajax({
        				    handler: CLS1131S01.handler,
        				    action: 'query_OTS_TRPAYLG',
        				    data:  $.extend({
        				        custId:CLS1131S01.data.custId,
        				        dupNo:CLS1131S01.data.dupNo,
        				        mainId:CLS1131S01.data.mainId
        				    }, $("#formDW_OTS_TRPAYLG").serializeData() ),
        				    success: function(response){
        				    	build_respDW_OTS_TRPAYLG(response);
        				    }
        				});
        		    }
        		});
                
            }
        });
			
	}
});