/* 
 * LMSRPTDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.Date;
import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.LMSRPTDao;
import com.mega.eloan.lms.model.LMSRPT;

/** 授信批次報表歷史檔 **/
@Repository
public class LMSRPTDaoImpl extends LMSJpaDao<LMSRPT, String> implements
		LMSRPTDao {

	@Override
	public LMSRPT findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<LMSRPT> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		List<LMSRPT> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		search.setMaxResults(Integer.MAX_VALUE);
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<LMSRPT> findByIndex01(String branch, Date endDate,
			String rptNo, String nowRpt) {
		ISearch search = createSearchTemplete();
		List<LMSRPT> list = null;
		if (branch != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "branch", branch);
		if (endDate != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "endDate",
					endDate);
		if (rptNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "rptNo", rptNo);
		if (nowRpt != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "nowRpt", nowRpt);
		search.setMaxResults(Integer.MAX_VALUE);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<LMSRPT> findByIndex02(String branch, Date bgnDate,
			Date endDate, String rptNo, String nowRpt) {
		ISearch search = createSearchTemplete();
		List<LMSRPT> list = null;
		if (branch != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "branch", branch);
		if (bgnDate != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "bgnDate",
					bgnDate);
		if (endDate != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "endDate",
					endDate);
		if (rptNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "rptNo", rptNo);
		if (nowRpt != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "nowRpt", nowRpt);
		search.setMaxResults(Integer.MAX_VALUE);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public LMSRPT findByIndex03(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.setMaxResults(Integer.MAX_VALUE);
//		LMSRPT lmsRpt = findUniqueOrNone(search);
//		if(lmsRpt == null){
//			lmsRpt = new LMSRPT();
//		}
//		return lmsRpt;
		return findUniqueOrNone(search);
	}

	@Override
	public List<LMSRPT> findByIndex04(String branch, String rptNo, Date dataDate,String nowRpt) {
		ISearch search = createSearchTemplete();
		List<LMSRPT> list = null;
		if (branch != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "branch", branch);
		if (rptNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "rptNo", rptNo);
		if (dataDate != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dataDate",
					dataDate);
		if (nowRpt != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "nowRpt", nowRpt);
		search.setMaxResults(Integer.MAX_VALUE);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}
}