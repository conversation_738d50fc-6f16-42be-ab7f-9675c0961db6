package com.mega.eloan.lms.crs.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;

import tw.com.jcs.common.Util;

import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.model.C241M01A;

public class LMS2411S04PanelS extends Panel {
	
	private C241M01A meta;
	
	private static final long serialVersionUID = 1L;

	public LMS2411S04PanelS(String id, C241M01A meta) {
		super(id);
	}
	
	public LMS2411S04PanelS(String id, C241M01A meta, boolean updatePanelName) {
		super(id, updatePanelName);
	}
	
	@Override
	public void execute(ModelMap model, PageParameters params) throws Exception {
		if(true){
			model.addAttribute("show_default_btn", Util.equals(RetrialDocStatusEnum.區中心_編製中.getCode(), meta.getDocStatus()));
		}
	}
}
