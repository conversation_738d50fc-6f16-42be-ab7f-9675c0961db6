/* 
 * L120S01J.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.NotNull;

import org.apache.bval.constraints.NotEmpty;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 個金償債能力檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name="L120S01J", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId","custId","dupNo"}))
public class L120S01J extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 身分證統編 **/
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 
	 * 其他收入<p/>
	 * 100/12/08備註<br/>
	 *  薪資收入|1<br/>
	 *  營利收入|2<br/>
	 *  投資收入|3<br/>
	 *  租金收入|4<br/>
	 *  利息收入|5<br/>
	 *  ※海外才需填寫
	 */
	@Column(name="OINCOME", length=1, columnDefinition="VARCHAR(1)")
	private String oIncome;

	/** 
	 * 其他收入金額(幣別)<p/>
	 * 100/12/08備註<br/>
	 *  ※海外才需填寫
	 */
	@Column(name="OMONEYCURR", length=3, columnDefinition="VARCHAR(3)")
	private String oMoneyCurr;

	/** 
	 * 其他收入金額(金額)<p/>
	 * 100/12/08備註<br/>
	 *  ※海外才需填寫
	 */
	@Column(name="OMONEYAMT", columnDefinition="DECIMAL(13,0)")
	private Long oMoneyAmt;

	/** 家庭所得(幣別)
(夫妻年收入) **/
	@Column(name="YFAMCURR", length=3, columnDefinition="VARCHAR(3)")
	@NotNull(message="{required.message}", groups=Check.class)
	@NotEmpty(message="{required.message}", groups=Check.class)
	private String yFamCurr;

	/** 家庭所得(金額)
(夫妻年收入) **/
	@Column(name="YFAMAMT", columnDefinition="DECIMAL(13,0)")
	@NotNull(message="{required.message}", groups=Check.class)
	@NotEmpty(message="{required.message}", groups=Check.class)
	private Long yFamAmt;

	/** 
	 * 家庭所得證明文件<p/>
	 * 個人綜合所得申報資料|1<br/>
	 *  扣繳憑單|2<br/>
	 *  薪資轉帳存摺|3<br/>
	 *  勞保薪資|4<br/>
	 *  租賃契約|5<br/>
	 *  其他收入證明|6
	 */
	@Column(name="YINCOMECERT", length=1, columnDefinition="VARCHAR(1)")
	@NotNull(message="{required.message}", groups=Check.class)
	@NotEmpty(message="{required.message}", groups=Check.class)
	private String yIncomeCert;

	/** 個人負債比率 **/
	@Column(name="DRATE", columnDefinition="DECIMAL(3,0)")
	@NotNull(message="{required.message}", groups=Check.class)
	@NotEmpty(message="{required.message}", groups=Check.class)
	private Integer dRate;

	/** 家庭負債比率 **/
	@Column(name="YRATE", columnDefinition="DECIMAL(3,0)")
	@NotNull(message="{required.message}", groups=Check.class)
	@NotEmpty(message="{required.message}", groups=Check.class)
	private Integer yRate;

	/** 
	 * 使用信用卡循環信用或現金卡情形<p/>
	 * (複選) A或B或AB<br/>
	 *  最近一年有使用兩家(含)以上銀行之信用卡循環信用紀錄| A<br/>
	 *  最近一年有使用兩家(含)以上銀行之現金卡紀錄| B
	 */
	@Column(name="CREDIT", length=2, columnDefinition="VARCHAR(2)")
	private String credit;

	/** 
	 * 是否於本行財富管理有定時定額扣款<p/>
	 * 是 | Y<br/>
	 *  否 | N
	 */
	@Column(name="ISPERIODFUND", length=1, columnDefinition="VARCHAR(1)")
	@NotNull(message="{required.message}", groups=Check.class)
	@NotEmpty(message="{required.message}", groups=Check.class)
	private String isPeriodFund;

	/** 
	 * 與本行其他業務往來(財富管理業務如基金保險信用卡等)<p/>
	 * 有 | Y<br/>
	 *  無 | N
	 */
	@Column(name="BUSI", length=1, columnDefinition="VARCHAR(1)")
	@NotNull(message="{required.message}", groups=Check.class)
	@NotEmpty(message="{required.message}", groups=Check.class)
	private String busi;

	/** 與本行財富管理三個月平均總資產(幣別) **/
	@Column(name="INVMBALCURR", length=3, columnDefinition="VARCHAR(3)")
	@NotNull(message="{required.message}", groups=Check.class)
	@NotEmpty(message="{required.message}", groups=Check.class)
	private String invMBalCurr;

	/** 與本行財富管理三個月平均總資產(金額) **/
	@Column(name="INVMBALAMT", columnDefinition="DECIMAL(13,0)")
	@NotNull(message="{required.message}", groups=Check.class)
	@NotEmpty(message="{required.message}", groups=Check.class)
	private Long invMBalAmt;

	/** 與他行財富管理三個月平均總資產(幣別) **/
	@Column(name="INVOBALCURR", length=3, columnDefinition="VARCHAR(3)")
	@NotNull(message="{required.message}", groups=Check.class)
	@NotEmpty(message="{required.message}", groups=Check.class)
	private String invOBalCurr;

	/** 與他行財富管理三個月平均總資產(金額) **/
	@Column(name="INVOBALAMT", columnDefinition="DECIMAL(13,0)")
	@NotNull(message="{required.message}", groups=Check.class)
	@NotEmpty(message="{required.message}", groups=Check.class)
	private Long invOBalAmt;

	/** 與金融機構存款往來情形(近六個月平均餘額)(幣別) **/
	@Column(name="BRANCURR", length=3, columnDefinition="VARCHAR(3)")
	@NotNull(message="{required.message}", groups=Check.class)
	@NotEmpty(message="{required.message}", groups=Check.class)
	private String branCurr;

	/** 與金融機構存款往來情形(近六個月平均餘額)(金額) **/
	@Column(name="BRANAMT", columnDefinition="DECIMAL(13,0)")
	@NotNull(message="{required.message}", groups=Check.class)
	@NotEmpty(message="{required.message}", groups=Check.class)
	private Long branAmt;

	/** 建立人員號碼 **/
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Date updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得身分證統編 **/
	public String getCustId() {
		return this.custId;
	}
	/** 設定身分證統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得身分證統編重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}
	/** 設定身分證統編重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 
	 * 取得其他收入<p/>
	 * 100/12/08備註<br/>
	 *  薪資收入|1<br/>
	 *  營利收入|2<br/>
	 *  投資收入|3<br/>
	 *  租金收入|4<br/>
	 *  利息收入|5<br/>
	 *  ※海外才需填寫
	 */
	public String getOIncome() {
		return this.oIncome;
	}
	/**
	 *  設定其他收入<p/>
	 *  100/12/08備註<br/>
	 *  薪資收入|1<br/>
	 *  營利收入|2<br/>
	 *  投資收入|3<br/>
	 *  租金收入|4<br/>
	 *  利息收入|5<br/>
	 *  ※海外才需填寫
	 **/
	public void setOIncome(String value) {
		this.oIncome = value;
	}

	/** 
	 * 取得其他收入金額(幣別)<p/>
	 * 100/12/08備註<br/>
	 *  ※海外才需填寫
	 */
	public String getOMoneyCurr() {
		return this.oMoneyCurr;
	}
	/**
	 *  設定其他收入金額(幣別)<p/>
	 *  100/12/08備註<br/>
	 *  ※海外才需填寫
	 **/
	public void setOMoneyCurr(String value) {
		this.oMoneyCurr = value;
	}

	/** 
	 * 取得其他收入金額(金額)<p/>
	 * 100/12/08備註<br/>
	 *  ※海外才需填寫
	 */
	public Long getOMoneyAmt() {
		return this.oMoneyAmt;
	}
	/**
	 *  設定其他收入金額(金額)<p/>
	 *  100/12/08備註<br/>
	 *  ※海外才需填寫
	 **/
	public void setOMoneyAmt(Long value) {
		this.oMoneyAmt = value;
	}

	/** 取得家庭所得(幣別)
(夫妻年收入) **/
	public String getYFamCurr() {
		return this.yFamCurr;
	}
	/** 設定家庭所得(幣別)
(夫妻年收入) **/
	public void setYFamCurr(String value) {
		this.yFamCurr = value;
	}

	/** 取得家庭所得(金額)
(夫妻年收入) **/
	public Long getYFamAmt() {
		return this.yFamAmt;
	}
	/** 設定家庭所得(金額)
(夫妻年收入) **/
	public void setYFamAmt(Long value) {
		this.yFamAmt = value;
	}

	/** 
	 * 取得家庭所得證明文件<p/>
	 * 個人綜合所得申報資料|1<br/>
	 *  扣繳憑單|2<br/>
	 *  薪資轉帳存摺|3<br/>
	 *  勞保薪資|4<br/>
	 *  租賃契約|5<br/>
	 *  其他收入證明|6
	 */
	public String getYIncomeCert() {
		return this.yIncomeCert;
	}
	/**
	 *  設定家庭所得證明文件<p/>
	 *  個人綜合所得申報資料|1<br/>
	 *  扣繳憑單|2<br/>
	 *  薪資轉帳存摺|3<br/>
	 *  勞保薪資|4<br/>
	 *  租賃契約|5<br/>
	 *  其他收入證明|6
	 **/
	public void setYIncomeCert(String value) {
		this.yIncomeCert = value;
	}

	/** 取得個人負債比率 **/
	public Integer getDRate() {
		return this.dRate;
	}
	/** 設定個人負債比率 **/
	public void setDRate(Integer value) {
		this.dRate = value;
	}

	/** 取得家庭負債比率 **/
	public Integer getYRate() {
		return this.yRate;
	}
	/** 設定家庭負債比率 **/
	public void setYRate(Integer value) {
		this.yRate = value;
	}

	/** 
	 * 取得使用信用卡循環信用或現金卡情形<p/>
	 * (複選) A或B或AB<br/>
	 *  最近一年有使用兩家(含)以上銀行之信用卡循環信用紀錄| A<br/>
	 *  最近一年有使用兩家(含)以上銀行之現金卡紀錄| B
	 */
	public String getCredit() {
		return this.credit;
	}
	/**
	 *  設定使用信用卡循環信用或現金卡情形<p/>
	 *  (複選) A或B或AB<br/>
	 *  最近一年有使用兩家(含)以上銀行之信用卡循環信用紀錄| A<br/>
	 *  最近一年有使用兩家(含)以上銀行之現金卡紀錄| B
	 **/
	public void setCredit(String value) {
		this.credit = value;
	}

	/** 
	 * 取得是否於本行財富管理有定時定額扣款<p/>
	 * 是 | Y<br/>
	 *  否 | N
	 */
	public String getIsPeriodFund() {
		return this.isPeriodFund;
	}
	/**
	 *  設定是否於本行財富管理有定時定額扣款<p/>
	 *  是 | Y<br/>
	 *  否 | N
	 **/
	public void setIsPeriodFund(String value) {
		this.isPeriodFund = value;
	}

	/** 
	 * 取得與本行其他業務往來(財富管理業務如基金保險信用卡等)<p/>
	 * 有 | Y<br/>
	 *  無 | N
	 */
	public String getBusi() {
		return this.busi;
	}
	/**
	 *  設定與本行其他業務往來(財富管理業務如基金保險信用卡等)<p/>
	 *  有 | Y<br/>
	 *  無 | N
	 **/
	public void setBusi(String value) {
		this.busi = value;
	}

	/** 取得與本行財富管理三個月平均總資產(幣別) **/
	public String getInvMBalCurr() {
		return this.invMBalCurr;
	}
	/** 設定與本行財富管理三個月平均總資產(幣別) **/
	public void setInvMBalCurr(String value) {
		this.invMBalCurr = value;
	}

	/** 取得與本行財富管理三個月平均總資產(金額) **/
	public Long getInvMBalAmt() {
		return this.invMBalAmt;
	}
	/** 設定與本行財富管理三個月平均總資產(金額) **/
	public void setInvMBalAmt(Long value) {
		this.invMBalAmt = value;
	}

	/** 取得與他行財富管理三個月平均總資產(幣別) **/
	public String getInvOBalCurr() {
		return this.invOBalCurr;
	}
	/** 設定與他行財富管理三個月平均總資產(幣別) **/
	public void setInvOBalCurr(String value) {
		this.invOBalCurr = value;
	}

	/** 取得與他行財富管理三個月平均總資產(金額) **/
	public Long getInvOBalAmt() {
		return this.invOBalAmt;
	}
	/** 設定與他行財富管理三個月平均總資產(金額) **/
	public void setInvOBalAmt(Long value) {
		this.invOBalAmt = value;
	}

	/** 取得與金融機構存款往來情形(近六個月平均餘額)(幣別) **/
	public String getBranCurr() {
		return this.branCurr;
	}
	/** 設定與金融機構存款往來情形(近六個月平均餘額)(幣別) **/
	public void setBranCurr(String value) {
		this.branCurr = value;
	}

	/** 取得與金融機構存款往來情形(近六個月平均餘額)(金額) **/
	public Long getBranAmt() {
		return this.branAmt;
	}
	/** 設定與金融機構存款往來情形(近六個月平均餘額)(金額) **/
	public void setBranAmt(Long value) {
		this.branAmt = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}
}
