package com.mega.eloan.lms.lrs.report.impl;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import javax.annotation.Resource;

import jxl.Workbook;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;

import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.ReportGenerator;

import com.inet.report.Engine;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.LrsUtil;
import com.mega.eloan.lms.base.common.RO412;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.lrs.pages.LMS1800M01Page;
import com.mega.eloan.lms.lrs.report.LMS1800R01RptService;
import com.mega.eloan.lms.lrs.service.LMS1810Service;
import com.mega.eloan.lms.model.L180M01A;
import com.mega.eloan.lms.model.L180M01B;
import com.mega.eloan.lms.model.L180M01D;
import com.mega.sso.service.BranchService;

@Service("lms1800r01rptservice")
public class LMS1800R01RptServiceImpl implements LMS1800R01RptService {

	@Resource
	LMS1810Service lms1810Service;

	@Resource
	BranchService branchService;

	@Autowired
	DocFileService fileService;

	@Resource
	RetrialService retrialService;

	@Override
	public void genExcel(L180M01A meta) throws Exception {

		Properties lms1800m01page = MessageBundleScriptCreator
				.getComponentResource(LMS1800M01Page.class);

		Map<String, String> mowDesc = retrialService.get_lrs_MowType_1();
		Map<String, String> newAddDesc = retrialService.get_lrs_NewAdd();

		String fileId = "lms1800";
		String listName = "listExcel";
		boolean printNckdFlag8 = false;
		if (RetrialDocStatusEnum.編製中.getCode().equals(meta.getDocStatus())) {
			listName = "listSearch";
			printNckdFlag8 = true;
		} else if (RetrialDocStatusEnum.待覆核.getCode().equals(
				meta.getDocStatus())) {
			listName = "listWait";
			printNckdFlag8 = true;
		}

		List<DocFile> docFiles = fileService.findByIDAndName(meta.getMainId(),
				fileId, lms1800m01page.getProperty(listName) + ".xls");

		if (docFiles != null && !docFiles.isEmpty()) {
			for (DocFile file : docFiles) {
				fileService.clean(file.getOid());
			}
		}

		DocFile docFile = new DocFile();
		docFile.setBranchId(meta.getOwnBrId());
		docFile.setContentType("application/msexcel");
		docFile.setMainId(meta.getMainId());
		docFile.setCrYear(CapDate.getCurrentDate("yyyy"));
		docFile.setFieldId(fileId);
		docFile.setSrcFileName(lms1800m01page.getProperty(listName) + ".xls");
		docFile.setUploadTime(CapDate.getCurrentTimestamp());
		docFile.setSysId(fileService.getSysId());
		docFile.setData(new byte[] {});
		docFile.setDeletedTime(null);
		fileService.save(docFile, false);

		ReportGenerator rptGenerator = new ReportGenerator(Engine.EXPORT_XLS,
				"report/lrs/LMS1800R01_" + LMSUtil.getLocale() + ".rpt");

		Properties properties = new Properties();
		// 設定excel 的cell顯示方式，相關參數可參考 report-url-parameters
		// http://www.inetsoftware.de/documentation/clear-reports/online-help/features/report-url-parameters
		properties.setProperty("celldistribution", "linebreak");
		rptGenerator.setProperties(properties);

		List<Map<String, String>> list = new ArrayList<Map<String, String>>();
		if (meta != null) {

			List<L180M01B> l180m01bs = retrialService
					.findL180M01BDefaultOrder(meta.getMainId());

			int count = 1;
			for (L180M01B l180m01b : l180m01bs) {

				String newNckdflag = CapString.trimNull(l180m01b
						.getNewNCkdFlag());

				if ("8".equals(newNckdflag) && !printNckdFlag8) {
					continue;
				}

				Map<String, String> values = reNewHashMapParams();
				StringBuffer sb = new StringBuffer();
				values.put("ReportBean.column01", String.valueOf(count));
				values.put("ReportBean.column02", l180m01b.getCustId());

				// 處理信評
				String crdtTbl = CapString.trimNull(l180m01b.getElfCrdType());
				String mowTbl1 = CapString.trimNull(l180m01b.getElfMowType());

				if (!"".equals(crdtTbl)) {
					sb.append(lms1800m01page.getProperty("createExcel.title15")
							+ ":" + l180m01b.getElfCrdTTbl());
				}
				if (!"".equals(mowTbl1)) {
					if (sb.length() > 0) {
						sb.append(EloanConstants.LINE_BREAK);
					}

					sb.append(mowDesc.get(mowTbl1) + ":"
							+ l180m01b.getElfMowTbl1());
				}

				values.put("ReportBean.column03", sb.toString());
				values.put("ReportBean.column04", l180m01b.getElfMainCust());
				values.put("ReportBean.column05", l180m01b.getElfCName());
				values.put("ReportBean.column06", CapDate.formatDate(
						l180m01b.getElfLRDate(), "yyyy-MM-dd"));

				sb = new StringBuffer();
				Set<L180M01D> l180m01ds = l180m01b.getL180m01ds();
				if (l180m01ds != null && !l180m01ds.isEmpty()) {
					for (L180M01D l180m01d : l180m01ds) {
						if (sb.length() > 0) {
							sb.append(EloanConstants.LINE_BREAK);
						}
						String id = l180m01d.getDbuObuId();
						String name = l180m01d.getDbuObuName();
						sb.append(id + " " + name);
					}
				}

				values.put("ReportBean.column07", sb.toString());
				values.put("ReportBean.column08", l180m01b.getElfRCkdLine());
				values.put("ReportBean.column09", l180m01b.getElfNCkdMemo());

				sb = new StringBuffer();

				String newAdd = CapString.trimNull(l180m01b.getElfNewAdd());

				if (!"".equals(newAdd)) {
					String date = l180m01b.getElfNewDate();

					sb.append(date + " " + newAddDesc.get(newAdd));
				}

				String mdFlag = CapString.trimNull(l180m01b.getElfMDFlag());

				if (!"".equals(mdFlag) && !"0".equals(mdFlag)) {
					if (sb.length() > 0) {
						sb.append(EloanConstants.LINE_BREAK);
					}
					Date mdDt = l180m01b.getElfMDDt();
					sb.append(
							mdDt != null ? CapDate.formatDate(mdDt,
									"yyyy-MM-dd") : "").append(
							lms1800m01page.getProperty("createExcel.dialog01"));
				}

				String newNckdMemo = CapString.trimNull(l180m01b
						.getNewNCkdMemo());
				String nckdMemo = CapString.trimNull(l180m01b.getElfNCkdMemo());
				if (!"".equals(newNckdMemo)) {
					if (sb.length() > 0) {
						sb.append(EloanConstants.LINE_BREAK);
					}
					sb.append(newNckdMemo);
				} else {
					if (!"".equals(nckdMemo)
							&& RetrialDocStatusEnum.編製中.getCode().equals(
									meta.getDocStatus())) {
						if (sb.length() > 0) {
							sb.append(EloanConstants.LINE_BREAK);
						}
						sb.append(nckdMemo);
					}
				}

				String memo = CapString.trimNull(l180m01b.getElfMemo());
				if (!"".equals(memo)) {
					if (sb.length() > 0) {
						sb.append(EloanConstants.LINE_BREAK);
					}
					sb.append(memo);
				}

				values.put("ReportBean.column10", sb.toString());

				if ("8".equals(newNckdflag)
						&& l180m01b.getNewNextNwDt() != null) {
					if (RetrialDocStatusEnum.編製中.getCode().equals(
							meta.getDocStatus())
							|| RetrialDocStatusEnum.待覆核.getCode().equals(
									meta.getDocStatus())) {

						// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
						Date dueDate = null;
						if (Util.equals(Util.trim(l180m01b.getCtlType()),
								LrsUtil.CTLTYPE_自辦覆審)) {
							dueDate = retrialService
									.gfnCTL_Caculate_DueDate("3", meta
											.getDataDate(), null, new RO412(
											meta.getBranchId(), l180m01b), null);
						} else if (Util.equals(
								Util.trim(l180m01b.getCtlType()),
								LrsUtil.CTLTYPE_價金履約)) {
							dueDate = retrialService.gfnCTL_Caculate_DueDate(
									"3", meta.getDataDate(), null, null,
									new RO412(meta.getBranchId(), l180m01b));
						} else {
							// LrsUtil.CTLTYPE_主辦覆審 = "A"
							dueDate = retrialService.gfnCTL_Caculate_DueDate(
									"3", meta.getDataDate(),
									new RO412(meta.getBranchId(), l180m01b),
									null, null);
						}

						values.put("ReportBean.column11",
								CapDate.formatDate(dueDate, "yyyy-MM-dd"));

					}
				}

				count++;
				list.add(values);
			}

			Map<String, String> prompts = new HashMap<String, String>();

			if (RetrialDocStatusEnum.編製中.getCode().equals(meta.getDocStatus())
					|| RetrialDocStatusEnum.待覆核.getCode().equals(
							meta.getDocStatus())) {

				prompts.put("ctlDateString",
						lms1800m01page.getProperty("createExcel.dialog02"));
			} else {
				prompts.put("ctlDateString",
						lms1800m01page.getProperty("createExcel.dialog03"));
			}

			prompts.put("branchId", meta.getBranchId());
			prompts.put("datadate",
					CapDate.formatDate(meta.getDataDate(), "yyyy-MM"));
			prompts.put("branchName",
					branchService.getBranchName(meta.getBranchId()));
			prompts.put(
					"status",
					lms1800m01page.getProperty("createExcel.status"
							+ meta.getDocStatus()));

			String note = "";
			if (RetrialDocStatusEnum.編製中.getCode().equals(meta.getDocStatus())
					|| RetrialDocStatusEnum.待覆核.getCode().equals(
							meta.getDocStatus())) {
				note = lms1800m01page.getProperty("L180M01A.generateDate")
						+ ":"
						+ CapDate.formatDate(meta.getGenerateDate(),
								"yyyy-MM-dd") + "/"
						+ lms1800m01page.getProperty("randomCode") + ":"
						+ meta.getRandomCode();
			} else {
				note = lms1800m01page.getProperty("randomCode") + ":"
						+ meta.getRandomCode();
			}
			prompts.put("note", note);
			prompts.put("defaultCTLDate",
					CapDate.formatDate(meta.getDefaultCTLDate(), "yyyy-MM-dd"));

			File folder = new File(LMSUtil.getUploadFilePath(meta.getOwnBrId(),
					meta.getMainId(), fileId));
			FileUtils.forceMkdir(folder);
			rptGenerator.setRowsData(list);
			rptGenerator.setVariableData(prompts);

			OutputStream outputStream = rptGenerator.generateReport();

			InputStream excelInputStream = new ByteArrayInputStream(
					((ByteArrayOutputStream) outputStream).toByteArray());

			WritableWorkbook writableWorkbook = Workbook.createWorkbook(
					new File(folder, docFile.getOid() + ".xls"),
					Workbook.getWorkbook(excelInputStream));

			// 設定列印title 固定列數
			WritableSheet sheet = writableWorkbook.getSheet(0);
			sheet.getSettings().setPrintTitlesRow(0, 2);
			writableWorkbook.write();
			writableWorkbook.close();

			// OutputStream outputStreamToFile = new DataOutputStream(
			// new FileOutputStream(new File(folder, docFile.getOid()
			// + ".xls")));
			//
			// outputStreamToFile.write(((ByteArrayOutputStream) outputStream)
			// .toByteArray());
			// outputStreamToFile.flush();
			// outputStreamToFile.close();

		}

	}

	private Map<String, String> reNewHashMapParams() {
		Map<String, String> values = new HashMap<String, String>();
		for (int i = 1; i <= 60; i++) {
			values.put("ReportBean.column" + String.format("%02d", i), "");
		}
		return values;
	}
}
