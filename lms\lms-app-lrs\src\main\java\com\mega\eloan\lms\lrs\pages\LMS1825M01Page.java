/* 
 * LMS1825M01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lrs.pages;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.ui.ModelMap;
import com.iisigroup.cap.component.PageParameters;

import tw.com.jcs.auth.AuthType;

import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.model.L182M01A;

/**<pre>
 * 覆審控制檔
 * </pre>
 * @since  2011/9/27
 * <AUTHOR>
 * @version <ul>
 *           <li>2011/9/27,irene,new
 *          </ul>
 */
@Controller
@RequestMapping("/lrs/lms1825m01")
public class LMS1825M01Page extends AbstractEloanForm {

	public LMS1825M01Page() {
		super();
	}

	@Override
	public void execute(ModelMap model, PageParameters params) throws Exception {
		super.execute(model, params);
		
		addAclLabel(model, new AclLabel("_btnDOC_EDITING", params, getDomainClass(),
				AuthType.Modify, RetrialDocStatusEnum.預約單_未處理));
		addAclLabel(model, new AclLabel("_btnAll", params, getDomainClass(),
				AuthType.Modify, RetrialDocStatusEnum.預約單_未處理));
		addAclLabel(model, new AclLabel("_btnAllclean", params, getDomainClass(),
				AuthType.Modify, RetrialDocStatusEnum.預約單_未處理));
		
		renderJsI18N(LMS1825M01Page.class);
		model.addAttribute("loadScript","loadScript('pagejs/lrs/LMS1825M01Page');");
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return L182M01A.class;
	}

}
