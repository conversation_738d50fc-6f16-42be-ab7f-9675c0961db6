/* 
 * MisEJF327ServiceImpl.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.ejcic.service.impl;

import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.ejcic.service.MisEJF327Service;

/**
 * <pre>
 * MIS.PROFILE>>MIS.EJV32701>>MIS.EJF327代號對照表
 * </pre>
 * @since 2012/03/23
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/03/23,TimChiang,new
 *          </ul>
 */
@Service
public class MisEJF327ServiceImpl extends AbstractEjcicJdbc implements MisEJF327Service {

	@Override
	public Map<String, Object> findCreditCardDisbKindCNm( String codeNo) {	
		return getJdbc().queryForMap("PROFILE.findCreditCardDisbKindCNm", new String[]{codeNo});
	}

}
