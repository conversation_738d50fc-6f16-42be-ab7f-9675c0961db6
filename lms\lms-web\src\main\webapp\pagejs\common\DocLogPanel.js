pageJsInit(function() {
	$(function() {
		var grid = $("#docLogGrid").iGrid({
			localFirst : true,
			handler : 'docloggridhandler',
			height : 270,
			rownumbers : true,
			postData : {
				formAction : "query"
			},
			colModel: [{
				colHeader: i18n.def['docLog.logTime'], // "記錄日期時間",
				name: 'logTime',
				align: "center",
				width: 80,
				sortable: true
			}, {
				colHeader: i18n.def['docLog.unitId'], // "執行人所屬單位",
				name: 'unitId',
				width: 70
			}, {
				colHeader: i18n.def['docLog.userId'], // "執行人員代號",
				name: 'userId',
				align: "center",
				width: 50
			}, {
				colHeader: i18n.def['docLog.actCode'], // "執行項目",
				name: 'actCode',
				align: "center",
				width: 30
			}]
		});
		
		var reload = true;
		
		$("#readDocLog").click(function() {
			if (reload) {
				grid.trigger("reloadGrid");
			}
			$("#docLogView").thickbox({ // 使用選取的內容進行彈窗
				title: i18n.def['docLog.title'],
				width: 600,
				height: 400,
				modal: false,
				valign: 'bottom',
				align: 'center',
				buttons: (function() {
					var btn = {};
					btn[i18n.def['close']] = function() {
						$.thickbox.close();
					}
					return btn;
				})()
			});
		});
	});
});