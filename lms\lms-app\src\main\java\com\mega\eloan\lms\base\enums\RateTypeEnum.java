package com.mega.eloan.lms.base.enums;

/**
 * <pre>
 * 當為本位幣 轉  其他幣別時  M 為除、D 為乘
 *   其他幣別 轉  本位幣      M 為乘、D 為除
 * </pre>
 * 
 * @since 2012/5/9
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/5/9,<PERSON>,new
 *          </ul>
 */
public enum RateTypeEnum {

	M("M"), D("D");

	private String code;

	RateTypeEnum(String code) {
		this.code = code;
	}

	public String getCode() {
		return code;
	}

	public boolean isEquals(Object other) {
		if (other instanceof String) {
			return code.equals(other);
		} else {
			return super.equals(other);
		}
	}

	public static RateTypeEnum getEnum(String code) {
		for (RateTypeEnum enums : RateTypeEnum.values()) {
			if (enums.isEquals(code)) {
				return enums;
			}
		}
		return null;
	}
}
