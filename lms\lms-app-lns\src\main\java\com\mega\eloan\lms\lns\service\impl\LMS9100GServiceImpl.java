/* 
 * LMS9100GServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lns.service.impl;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.OutputStream;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.lms.lms.service.LMS9100GService;
import com.mega.eloan.lms.lns.report.LMS1201R01RptService;

/**
 * <pre>
 * 近期已收
 * </pre>
 * 
 * @since 2012/1/13
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/13,REX,new
 *          </ul>
 */

@Service
public class LMS9100GServiceImpl implements LMS9100GService {

	@Resource
	LMS1201R01RptService lms1201r01RptService;

	/**
	 * 提供base可以呼叫lns、cls的列印程式
	 */
	@Override
	public OutputStream generateReport(PageParameters params)
			throws FileNotFoundException, IOException, Exception {
		return lms1201r01RptService.generateReport(params);
	}

}
