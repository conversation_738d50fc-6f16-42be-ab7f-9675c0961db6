/* 
 * L140M01O_0307.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 擔保品資料股票明細檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L140M01O_0307", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L140M01O_0307 extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 序號 **/
	@Digits(integer=5, fraction=0, groups = Check.class)
	@Column(name="SEQNO", columnDefinition="DECIMAL(5,0)")
	private Integer seqNo;

	/** 股票代號 **/
	@Size(max=6)
	@Column(name="STKNO", length=6, columnDefinition="CHAR(06)")
	private String stkNo;

	/** 股票名稱 **/
	@Size(max=30)
	@Column(name="STKNM", length=30, columnDefinition="CHAR(30)")
	private String stkNm;

	/** 
	 * 發行總股數<p/>
	 * (單位股)
	 */
	@Digits(integer=14, fraction=0, groups = Check.class)
	@Column(name="STKNUM", columnDefinition="DECIMAL(14,0)")
	private BigDecimal stkNum;

	/** 
	 * 設質總股數<p/>
	 * (單位股)
	 */
	@Digits(integer=14, fraction=0, groups = Check.class)
	@Column(name="QNTY", columnDefinition="DECIMAL(14,0)")
	private BigDecimal qnty;

	/** 
	 * 加計本次增減設質總股數<p/>
	 * (單位股)
	 */
	@Digits(integer=14, fraction=0, groups = Check.class)
	@Column(name="CHANGEQNTY", columnDefinition="DECIMAL(14,0)")
	private BigDecimal changeQnty;

	/** 設質比率 **/
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="SETRATIO", columnDefinition="DECIMAL(5,2)")
	private BigDecimal setRatio;

	/** 
	 * 控管方式<p/>
	 * 徵提之股票加計本次後累計設質予本行之總股數佔其發行股數之百分比為<br/>
	 *  本案為國際聯貸案或徵提之股票註冊地在國外，且無法取得總發行股數者
	 */
	@Size(max=1)
	@Column(name="CTRLKIND", length=1, columnDefinition="CHAR(1)")
	private String ctrlKind;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得序號 **/
	public Integer getSeqNo() {
		return this.seqNo;
	}
	/** 設定序號 **/
	public void setSeqNo(Integer value) {
		this.seqNo = value;
	}

	/** 取得股票代號 **/
	public String getStkNo() {
		return this.stkNo;
	}
	/** 設定股票代號 **/
	public void setStkNo(String value) {
		this.stkNo = value;
	}

	/** 取得股票名稱 **/
	public String getStkNm() {
		return this.stkNm;
	}
	/** 設定股票名稱 **/
	public void setStkNm(String value) {
		this.stkNm = value;
	}

	/** 
	 * 取得發行總股數<p/>
	 * (單位股)
	 */
	public BigDecimal getStkNum() {
		return this.stkNum;
	}
	/**
	 *  設定發行總股數<p/>
	 *  (單位股)
	 **/
	public void setStkNum(BigDecimal value) {
		this.stkNum = value;
	}

	/** 
	 * 取得設質總股數<p/>
	 * (單位股)
	 */
	public BigDecimal getQnty() {
		return this.qnty;
	}
	/**
	 *  設定設質總股數<p/>
	 *  (單位股)
	 **/
	public void setQnty(BigDecimal value) {
		this.qnty = value;
	}

	/** 
	 * 取得加計本次增減設質總股數<p/>
	 * (單位股)
	 */
	public BigDecimal getChangeQnty() {
		return this.changeQnty;
	}
	/**
	 *  設定加計本次增減設質總股數<p/>
	 *  (單位股)
	 **/
	public void setChangeQnty(BigDecimal value) {
		this.changeQnty = value;
	}

	/** 取得設質比率 **/
	public BigDecimal getSetRatio() {
		return this.setRatio;
	}
	/** 設定設質比率 **/
	public void setSetRatio(BigDecimal value) {
		this.setRatio = value;
	}

	/** 
	 * 取得控管方式<p/>
	 * 徵提之股票加計本次後累計設質予本行之總股數佔其發行股數之百分比為<br/>
	 *  本案為國際聯貸案或徵提之股票註冊地在國外，且無法取得總發行股數者
	 */
	public String getCtrlKind() {
		return this.ctrlKind;
	}
	/**
	 *  設定控管方式<p/>
	 *  徵提之股票加計本次後累計設質予本行之總股數佔其發行股數之百分比為<br/>
	 *  本案為國際聯貸案或徵提之股票註冊地在國外，且無法取得總發行股數者
	 **/
	public void setCtrlKind(String value) {
		this.ctrlKind = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
