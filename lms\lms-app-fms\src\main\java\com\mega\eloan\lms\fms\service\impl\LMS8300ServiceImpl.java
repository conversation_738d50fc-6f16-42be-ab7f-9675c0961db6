package com.mega.eloan.lms.fms.service.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.dao.L830M01ADao;
import com.mega.eloan.lms.dao.L830M01BDao;
import com.mega.eloan.lms.fms.pages.LMS8300V01Page;
import com.mega.eloan.lms.fms.service.LMS8300Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.mfaloan.service.impl.LNLNF013ServiceImpl;
import com.mega.eloan.lms.model.L830M01A;
import com.mega.eloan.lms.model.L830M01B;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.service.FlowService;

/**
 * <pre>
 * AO帳戶管理員維護作業
 * </pre>
 * 
 * @since 2022/12/01
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Service
public class LMS8300ServiceImpl extends AbstractCapService implements
		LMS8300Service {
	private static final Logger logger = LoggerFactory
			.getLogger(LMS8300ServiceImpl.class);

	
	@Resource
	FlowService flowService;

	@Resource
	TempDataService tempDataService;

	@Resource
	DocLogService docLogService;

	@Resource
	MisdbBASEService misdbBASEService;

	@Resource
	BranchService branchService;
	
	@Resource
	LNLNF013ServiceImpl lnLNF013ServiceImpl;
	
	@Resource
	L830M01ADao l830m01aDao;
	
	@Resource
	L830M01BDao l830m01bDao;
	
	@Override
	public List<L830M01A> findL830m01aByBrno(String brNo, String creator) {
		return l830m01aDao.findByBrno(brNo,creator);
	}
	

	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L830M01A) {
					l830m01aDao.save((L830M01A) model);
				}else if(model instanceof L830M01B){
					l830m01bDao.save((L830M01B) model);
				}
			}
		}
	}

	
	@Override
	public void delete(GenericBean... entity) {
		// TODO Auto-generated method stub
	}
	
	@Override
	public void deleteL830m01aList(List<L830M01A> l830m01aList) {
		l830m01aDao.delete(l830m01aList);
	}
	
	public void deleteL830m01bList(List<L830M01B> l830m01bList) {
		l830m01bDao.delete(l830m01bList);
	}
	
	
	
	public L830M01A findByOid(String oid){
		return l830m01aDao.findByOid(oid);
	}


	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		// TODO Auto-generated method stub
		if (clazz == L830M01B.class) {
			return l830m01bDao.findPage(search);
		} 
		return null;
	}


	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		// TODO Auto-generated method stub
		return null;
	}


	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		// TODO Auto-generated method stub
		if (clazz == L830M01B.class) {
			return l830m01bDao.findByMainId(mainId);
		}else if(clazz == L830M01A.class){
			return l830m01aDao.findByMainId(mainId);
		}
		
		return null;
	}

	
	
	@Override
	public Map<String, String> get_maintainTypeDescMap(){
		Map<String, String> m = new HashMap<String, String>();
		Properties prop_lms8300v01 = MessageBundleScriptCreator.getComponentResource(LMS8300V01Page.class);
		m.put("S", Util.trim(prop_lms8300v01.get("L830M01B.SingleMaintain")));
		m.put("B", Util.trim(prop_lms8300v01.get("L830M01B.BatchMaintain")));
		
		return m;
	}
	
	
	@Override
	public int gfnUpdateLNF013(L830M01A meta, String brNo, String userId) {
		// UPDATE LNF013資料
		String dateNow = CapDate.formatDate(new Date(), "yyyy-MM-dd");
		String LNF013_BR_NO = meta.getOwnBrId();
		String LNF013_STAFF_NO = meta.getAOId(); // 6碼
		String LNF013_CUST_ID = meta.getALcustId();
		String LNF013_UPD_DATE = dateNow;
		String LNF013_UPD_TELLER = meta.getCreator();
		String LNF013_UPD_SUPVNO = userId;
		String LNF013_BUS_PER_FG = UtilConstants.Casedoc.DocType.個金;

		int updateData = lnLNF013ServiceImpl.updateByBrnoAndCustId(LNF013_UPD_DATE, LNF013_UPD_TELLER, LNF013_UPD_SUPVNO, LNF013_STAFF_NO,
				LNF013_BR_NO, LNF013_CUST_ID, LNF013_BUS_PER_FG );
		
		return updateData;
	}
	

}
