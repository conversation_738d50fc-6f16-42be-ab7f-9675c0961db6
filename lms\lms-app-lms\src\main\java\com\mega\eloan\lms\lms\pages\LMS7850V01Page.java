package com.mega.eloan.lms.lms.pages;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.html.EloanPageFragment;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;

import tw.com.jcs.auth.AuthType;

/**
 * <pre>
 * 停權解除維護(編製中)
 * </pre>
 * 
 * @since 2013/1/21
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/1/21,<PERSON>,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms7850v01")
public class LMS7850V01Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		// 設定文件狀態(交易代碼)
		setGridViewStatus(CreditDocStatusEnum.授管處_停權編製中);
		// 加上Button
		List<EloanPageFragment> buttons = new ArrayList<EloanPageFragment>();
		// 只有經辦出現的按鈕
		if (this.getAuth(AuthType.Modify)) {
			buttons.add(LmsButtonEnum.Add);
		}
		buttons.add(LmsButtonEnum.View);
		// 只有經辦出現的按鈕
		if (this.getAuth(AuthType.Modify)) {
			buttons.add(LmsButtonEnum.Delete);
		}

		addToButtonPanel(model, buttons);
		renderJsI18N(LMS7850V01Page.class);
		model.addAttribute("loadScript", "loadScript('pagejs/lms/LMS7850V01Page');");
	}// ;

}
