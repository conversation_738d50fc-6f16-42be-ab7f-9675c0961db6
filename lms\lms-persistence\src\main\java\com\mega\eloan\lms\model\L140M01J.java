/* 
 * L140M01J.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/** 共同借款人檔 **/
@NamedEntityGraph(name = "L140M01J-entity-graph", attributeNodes = { @NamedAttributeNode("l140m01a") })
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L140M01J", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "custId", "dupNo" }))
public class L140M01J extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 身分證統編 **/
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/** 借款人姓名 **/
	@Size(max = 120)
	@Column(name = "CUSTNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String custName;

	/**
	 * 企/個金案件
	 * <p/>
	 * 1企金<br/>
	 * 2個金
	 */
	@Size(max = 1)
	@Column(name = "DOCTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String docType;

	/**
	 * 客戶型態 (區部別)
	 * <p/>
	 * 0.無、1.DBU、2.OBU、5.海外(海外同業, 海外客戶)
	 */
	@Size(max = 1)
	@Column(name = "TYPCD", length = 1, columnDefinition = "CHAR(1)")
	private String typCd;

	/**
	 * 客戶編號
	 * <p/>
	 * 非必要輸入
	 */
	@Size(max = 7)
	@Column(name = "CUSTNO", length = 7, columnDefinition = "VARCHAR(7)")
	private String custNo;

	/**
	 * 主要借款人註記
	 * <p/>
	 * Y/N
	 */
	@Size(max = 1)
	@Column(name = "KEYMAN", length = 1, columnDefinition = "CHAR(1)")
	private String keyMan;

	/**
	 * 國別註冊地
	 * <p/>
	 * 1.企金：L120S01B.ntCode<br/>
	 * 2個金：L120S01H.ntCode
	 */
	@Size(max = 2)
	@Column(name = "NTCODE", length = 2, columnDefinition = "VARCHAR(2)")
	private String ntCode;

	/**
	 * 與主要借款人關係
	 * <p/>
	 * 本人、配偶…<br/>
	 * 企業關係人：1X, 2X, …<br/>
	 * 親屬關係：X0, XA, …<br/>
	 * 其他綜合關係：10, 1A, …<br/>
	 * (詳註一)
	 */
	@Size(max = 2)
	@Column(name = "CUSTRLT", length = 2, columnDefinition = "CHAR(2)")
	private String custRlt;

	/**
	 * 相關身份
	 * <p/>
	 * C.共同借款人<br/>
	 * D.共同發票人(企金)<br/>
	 * G.連帶保證人(個金)<br/>
	 * N.ㄧ般保證人(個金)<br/>
	 * S.擔保品提供人(個金)<br/>
	 * ※非主要借款人時才需填列
	 */
	@Size(max = 1)
	@Column(name = "CUSTPOS", length = 1, columnDefinition = "CHAR(1)")
	private String custPos;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * JOIN條件 關聯檔
	 * 
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "MAINID", referencedColumnName = "MAINID", insertable = false, updatable = false)
	private L140M01A l140m01a;

	public L140M01A getL140m01a() {
		return l140m01a;
	}

	public void setL140m01a(L140M01A l140m01a) {
		this.l140m01a = l140m01a;
	}
	
	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得身分證統編 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定身分證統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得身分證統編重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定身分證統編重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得借款人姓名 **/
	public String getCustName() {
		return this.custName;
	}

	/** 設定借款人姓名 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/**
	 * 取得企/個金案件
	 * <p/>
	 * 1企金<br/>
	 * 2個金
	 */
	public String getDocType() {
		return this.docType;
	}

	/**
	 * 設定企/個金案件
	 * <p/>
	 * 1企金<br/>
	 * 2個金
	 **/
	public void setDocType(String value) {
		this.docType = value;
	}

	/**
	 * 取得客戶型態 (區部別)
	 * <p/>
	 * 0.無、1.DBU、2.OBU、5.海外(海外同業, 海外客戶)
	 */
	public String getTypCd() {
		return this.typCd;
	}

	/**
	 * 設定客戶型態 (區部別)
	 * <p/>
	 * 0.無、1.DBU、2.OBU、5.海外(海外同業, 海外客戶)
	 **/
	public void setTypCd(String value) {
		this.typCd = value;
	}

	/**
	 * 取得客戶編號
	 * <p/>
	 * 非必要輸入
	 */
	public String getCustNo() {
		return this.custNo;
	}

	/**
	 * 設定客戶編號
	 * <p/>
	 * 非必要輸入
	 **/
	public void setCustNo(String value) {
		this.custNo = value;
	}

	/**
	 * 取得主要借款人註記
	 * <p/>
	 * Y/N
	 */
	public String getKeyMan() {
		return this.keyMan;
	}

	/**
	 * 設定主要借款人註記
	 * <p/>
	 * Y/N
	 **/
	public void setKeyMan(String value) {
		this.keyMan = value;
	}

	/**
	 * 取得國別註冊地
	 * <p/>
	 * 1.企金：L120S01B.ntCode<br/>
	 * 2個金：L120S01H.ntCode
	 */
	public String getNtCode() {
		return this.ntCode;
	}

	/**
	 * 設定國別註冊地
	 * <p/>
	 * 1.企金：L120S01B.ntCode<br/>
	 * 2個金：L120S01H.ntCode
	 **/
	public void setNtCode(String value) {
		this.ntCode = value;
	}

	/**
	 * 取得與主要借款人關係
	 * <p/>
	 * 本人、配偶…<br/>
	 * 企業關係人：1X, 2X, …<br/>
	 * 親屬關係：X0, XA, …<br/>
	 * 其他綜合關係：10, 1A, …<br/>
	 * (詳註一)
	 */
	public String getCustRlt() {
		return this.custRlt;
	}

	/**
	 * 設定與主要借款人關係
	 * <p/>
	 * 本人、配偶…<br/>
	 * 企業關係人：1X, 2X, …<br/>
	 * 親屬關係：X0, XA, …<br/>
	 * 其他綜合關係：10, 1A, …<br/>
	 * (詳註一)
	 **/
	public void setCustRlt(String value) {
		this.custRlt = value;
	}

	/**
	 * 取得相關身份
	 * <p/>
	 * C.共同借款人<br/>
	 * D.共同發票人(企金)<br/>
	 * G.連帶保證人(個金)<br/>
	 * N.ㄧ般保證人(個金)<br/>
	 * S.擔保品提供人(個金)<br/>
	 * ※非主要借款人時才需填列
	 */
	public String getCustPos() {
		return this.custPos;
	}

	/**
	 * 設定相關身份
	 * <p/>
	 * C.共同借款人<br/>
	 * D.共同發票人(企金)<br/>
	 * G.連帶保證人(個金)<br/>
	 * N.ㄧ般保證人(個金)<br/>
	 * S.擔保品提供人(個金)<br/>
	 * ※非主要借款人時才需填列
	 **/
	public void setCustPos(String value) {
		this.custPos = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
