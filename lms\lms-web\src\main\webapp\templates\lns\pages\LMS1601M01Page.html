<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="innerPageBody">
        	<script type="text/javascript">
	            loadScript('pagejs/lns/LMS1601M01Page');
	        </script>
        	<!--<script type="text/javascript" src="pagejs/lns/LMS1601M01Page.js?r=20220302"></script> -->
            <div class="button-menu funcContainer" id="buttonPanel">
                <!--編製中 -->
                <th:block th:if="${_btnDOC_EDITING_visible}">
                    <button id="btnSave">
                        <span class="ui-icon ui-icon-jcs-04"></span>
                        <th:block th:text="#{'button.save'}">儲存</th:block>
                    </button>
                    <button id="btnSend">
                        <span class="ui-icon ui-icon-jcs-02"></span>
                        <th:block th:text="#{'button.send'}">呈主管覆核</th:block>
                    </button>
                </th:block>
                <!--待覆核 -->
                <th:block th:if="${_btnWAIT_APPROVE_visible}">
                    <button id="btnCheck">
                        <span class="ui-icon ui-icon-jcs-106"></span>
                        <th:block th:text="#{'button.check'}">覆核</th:block>
                    </button>
                </th:block>
                <!--已覆核 -->
                <th:block th:if="${_btnDOC_REUPMIS_visible}">
                    <button id="btnReUpMIS">
                        <span class="ui-icon ui-icon-jcs-106"></span>
                        <th:block th:text="#{'button.reUpMIS'}">重新上傳MIS</th:block>
                    </button>
                </th:block>

                <button id="btnPrint" class="forview">
                    <span class="ui-icon ui-icon-jcs-03"></span>
                    <th:block th:text="#{'button.print'}">列印</th:block>
                </button>
                <button id="btnExit" class="forview">
                    <span class="ui-icon ui-icon-jcs-01"></span>
                    <th:block th:text="#{'button.exit'}">離開</th:block>
                </button>

            </div>
            <div class="tit2 color-black">
                <th:block th:text="#{'L160M01A.title01'}">動用審核表</th:block>：
                (<span id="showTypCd" class="text-red"></span>)<span id="showCustId" class="color-blue"></span>
            </div>
            <div class="tabs doc-tabs">
                <ul>
                    <li id="tabs_1">
                        <a href="#tab-01" goto="01">
                            <b><th:block th:text="#{'doc.docinfo'}">文件資訊</th:block></b>
                        </a>
                    </li>
                    <li>
                        <a href="#tab-02" goto="02">
                            <b><th:block th:text="#{'L160M01A.title01'}">動用審核表</th:block></b>
                        </a>
                    </li>
                    <li>
                        <a href="#tab-03" goto="03">
                            <b><th:block th:text="#{'L160M01A.title02'}">相關報表</th:block></b>
                        </a>
                    </li>
                    <li id="tabs_4" style="display:none">
                        <a href="#tab-04" goto="04">
                            <b><th:block th:text="#{'L160M01A.title03'}">先行動用呈核及控制表</th:block></b>
                        </a>
                    </li>
                    <li>
                        <a href="#tab-05" goto="05"><b>AML/CFT</b></a>
                    </li>
                    <li id="tabs_6">
                        <a href="#tab-06" goto="06">
                            <b><th:block th:text="#{'L160M01A.title06'}">RPA資料查詢</th:block></b>
                        </a>
                    </li>
                    <th:block th:if="${show_ivr_panel_visible}">
                        <li>
                            <a href="#tab-07" goto="07">
                                <b><th:block th:text="#{'L160M01A.title04'}">IVR電話語音檔</th:block></b>
                            </a>
                        </li>
                    </th:block>
                </ul>
                <div class="tabCtx-warp">
                    <div th:id="${tabID}" th:insert="~{${panelName} :: ${panelFragmentName}}"></div>
                </div>
            </div>
            <div id="openCheckBox" style="display:none">
                <div>
                    <span id="check1" style="display:none">
                        <label>
                            <input name="checkRadio" type="radio" value="3">
                            <th:block th:text="#{'L160M01A.bt12'}">核定動撥</th:block>
                        </label>
                        <br/>
                        <label>
                            <input name="checkRadio" type="radio" value="1">
                            <th:block th:text="#{'L160M01A.bt11'}">退回經辦修改</th:block>
                        </label>
                    </span>
                    <span id="check2" style="display:none">
                        <label>
                            <input name="checkRadio" type="radio" value="4">
                            <th:block th:text="#{'L160M01A.bt13'}">先行動用辦妥確認</th:block>
                        </label>
                        <br/>
                        <label>
                            <input name="checkRadio" type="radio" value="2">
                            <th:block th:text="#{'L160M01A.bt11'}">退回經辦修改</th:block>
                        </label>
                    </span>
                </div>
            </div>
            <div id="openChecDatekBox" style="display:none">
                <div>
                    <input id="forCheckDate" type="text" size="10" maxlength="10" class="date">
                </div>
            </div>
            <div id="selectBossBox" style="display:none;">
                <form id="selectBossForm">
                    <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td class="hd1" width="60%">
                                <th:block th:text="#{'L160M01A.selectBoss'}">授信主管人數</th:block>&nbsp;&nbsp;
                            </td>
                            <td width="40%">
                                <select id="numPerson" name="numPerson">
                                    <option value="1">1</option>
                                    <option value="2">2</option>
                                    <option value="3">3</option>
                                    <option value="4">4</option>
                                    <option value="5">5</option>
                                    <option value="6">6</option>
                                    <option value="7">7</option>
                                    <option value="8">8</option>
                                    <option value="9">9</option>
                                    <option value="10">10</option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <td class="hd1">
                                <th:block th:text="#{'L160M01A.bossId'}">授信主管</th:block>&nbsp;&nbsp;
                            </td>
                            <td>
                                <div>
                                    <th:block th:text="#{'L163M01A.no'}">第</th:block>1
                                    <th:block th:text="#{'L163M01A.site'}">位</th:block>
                                    <th:block th:text="#{'L160M01A.bossId'}">授信主管</th:block>&nbsp;&nbsp;
                                    <select id="mainBoss" name="boss1" class="boss"/>
                                    <span id="newBossSpan"></span>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="hd1">
                                <th:block th:text="#{'L160M01A.managerId'}">經副襄理</th:block>&nbsp;&nbsp;
                            </td>
                            <td>
                                <select id="manager" name="manager" class="boss"/>
                            </td>
                        </tr>
                    </table>
                </form>
            </div>
        </th:block>
    </body>
</html>
