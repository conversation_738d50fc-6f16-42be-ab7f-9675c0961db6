/* 
 * C140S07ADaoImpl.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.C140S07ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C140M07A;
import com.mega.eloan.lms.model.C140S07A;

/**
 * <pre>
 * 徵信調查報告書第柒章 - 財務分析(子)
 * </pre>
 * 
 * @since 2011/10/05
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/10/05,TimChiang, new
 *          </ul>
 */
@Repository
public class C140S07ADaoImpl extends LMSJpaDao<C140S07A, String> implements
		C140S07ADao {

	@Override
	public int deleteByMeta(C140M07A meta) {
		if (meta == null)
			return 0;
		Query query = entityManager
				.createNamedQuery("ces140s07a.deleteByMainIdAndPid");
		query.setParameter("mainId", meta.getMainId());
		query.setParameter("pid", meta.getUid());
		return query.executeUpdate();
	}

	@Override
	public List<C140S07A> findByMainIdAndTab(String mainId, String tab,
			String subtab) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "c140m07a.mainId",
				mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "c140m07a.tab", tab);
		if (subtab != null) {
			search.addSearchModeParameters(SearchMode.EQUALS,
					"c140m07a.subtab", subtab);
		}
//		search.addOrderBy(C140S07A_.eDate.getName(), true);
		return find(search);
	}//;
	
	public List<C140S07A> findBySubDocAndTab(String mainId, String pid, String tab,
			String subtab){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "c140m07a.mainId",
				mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "c140m07a.tab", tab);
		if (subtab != null) {
			search.addSearchModeParameters(SearchMode.EQUALS,
					"c140m07a.subtab", subtab);
		}
		search.addSearchModeParameters(SearchMode.EQUALS, "c140m07a.pid",
				pid);
//		search.addOrderBy(C140S07A_.eDate.getName(), true);
		return find(search);
	}//;
}// ;
