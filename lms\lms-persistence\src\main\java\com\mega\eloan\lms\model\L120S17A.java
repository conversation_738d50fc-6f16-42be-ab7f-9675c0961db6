/* 
 * L120S17A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 授權外案件徵授信進度時程檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L120S17A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class L120S17A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 徵信報告編號 **/
	@Size(max = 32)
	@Column(name = "CESMAINID", length = 32, columnDefinition = "CHAR(32)")
	private String cesMainId;

	/**
	 * 徵信類型
	 * <p/>
	 * 1.徵信報告 2.資信簡表
	 */
	@Size(max = 1)
	@Column(name = "CESTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String cesType;

	/** 徵信辦理單位類別 **/
	@Size(max = 1)
	@Column(name = "CESUNITTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String cesUnitType;

	/** 徵信編製單位代號 **/
	@Size(max = 3)
	@Column(name = "CESOWNBRID", length = 3, columnDefinition = "CHAR(3)")
	private String cesOwnBrId;

	/** 訪談日期(A) **/
	@Temporal(TemporalType.DATE)
	@Column(name = "DATEA", columnDefinition = "Date")
	private Date dateA;

	/** 訪談人員 **/
	@Size(max = 6)
	@Column(name = "ROLEA", length = 6, columnDefinition = "CHAR(6)")
	private String roleA;

	/** 徵信資料備妥日(B) **/
	@Temporal(TemporalType.DATE)
	@Column(name = "DATEB", columnDefinition = "Date")
	private Date dateB;

	/** 徵信單位經辦 **/
	@Size(max = 6)
	@Column(name = "ROLEB", length = 6, columnDefinition = "CHAR(6)")
	private String roleB;

	/** 徵信報告完成日(C) **/
	@Temporal(TemporalType.DATE)
	@Column(name = "DATEC", columnDefinition = "Date")
	private Date dateC;

	/** 徵信單位覆核主管 **/
	@Size(max = 6)
	@Column(name = "ROLEC", length = 6, columnDefinition = "CHAR(6)")
	private String roleC;

	/** 授管中心收件日（分行簽報內容最終定稿送呈日）(D) **/
	@Temporal(TemporalType.DATE)
	@Column(name = "DATED", columnDefinition = "Date")
	private Date dateD;

	/** 授管中心審查經辦 **/
	@Size(max = 6)
	@Column(name = "ROLED", length = 6, columnDefinition = "CHAR(6)")
	private String roleD;

	/** 授審處收件日（授管中心審查完成日/分行簽報內容最終定稿送呈日）(E) **/
	@Temporal(TemporalType.DATE)
	@Column(name = "DATEE", columnDefinition = "Date")
	private Date dateE;

	/** 提會日期(授審會)(F) **/
	@Temporal(TemporalType.DATE)
	@Column(name = "DATEF1", columnDefinition = "Date")
	private Date dateF1;

	/** 提會日期(常董會)(F) **/
	@Temporal(TemporalType.DATE)
	@Column(name = "DATEF2", columnDefinition = "Date")
	private Date dateF2;

	/** 提會日期(催收會)(F) **/
	@Temporal(TemporalType.DATE)
	@Column(name = "DATEF3", columnDefinition = "Date")
	private Date dateF3;
	
	/** 提會日期(審計委員會)(F) **/
	@Temporal(TemporalType.DATE)
	@Column(name = "DATEF4", columnDefinition = "Date")
	private Date dateF4;

	/** 最終核決層級核定日(G) **/
	@Temporal(TemporalType.DATE)
	@Column(name = "DATEG", columnDefinition = "Date")
	private Date dateG;

	/** 授審處審查經辦 **/
	@Size(max = 6)
	@Column(name = "ROLEG", length = 6, columnDefinition = "CHAR(6)")
	private String roleG;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/** 撤件日(G) **/
	@Temporal(TemporalType.DATE)
	@Column(name = "CASECANCELDATE", columnDefinition = "Date")
	private Date caseCancelDate;

	/** 海外總行收件日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "DATEH", columnDefinition = "Date")
	private Date dateH;

	/** 海外總行審查經辦 **/
	@Size(max = 6)
	@Column(name = "ROLEH", length = 6, columnDefinition = "CHAR(6)")
	private String roleH;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得徵信報告編號 **/
	public String getCesMainId() {
		return this.cesMainId;
	}

	/** 設定徵信報告編號 **/
	public void setCesMainId(String value) {
		this.cesMainId = value;
	}

	/**
	 * 取得徵信類型
	 * <p/>
	 * 1.徵信報告 2.資信簡表
	 */
	public String getCesType() {
		return this.cesType;
	}

	/**
	 * 設定徵信類型
	 * <p/>
	 * 1.徵信報告 2.資信簡表
	 **/
	public void setCesType(String value) {
		this.cesType = value;
	}

	/** 取得徵信辦理單位類別 **/
	public String getCesUnitType() {
		return this.cesUnitType;
	}

	/** 設定徵信辦理單位類別 **/
	public void setCesUnitType(String value) {
		this.cesUnitType = value;
	}

	/** 取得徵信編製單位代號 **/
	public String getCesOwnBrId() {
		return this.cesOwnBrId;
	}

	/** 設定徵信編製單位代號 **/
	public void setCesOwnBrId(String value) {
		this.cesOwnBrId = value;
	}

	/** 取得訪談日期(A) **/
	public Date getDateA() {
		return this.dateA;
	}

	/** 設定訪談日期(A) **/
	public void setDateA(Date value) {
		this.dateA = value;
	}

	/** 取得訪談人員 **/
	public String getRoleA() {
		return this.roleA;
	}

	/** 設定訪談人員 **/
	public void setRoleA(String value) {
		this.roleA = value;
	}

	/** 取得徵信資料備妥日(B) **/
	public Date getDateB() {
		return this.dateB;
	}

	/** 設定徵信資料備妥日(B) **/
	public void setDateB(Date value) {
		this.dateB = value;
	}

	/** 取得徵信單位經辦 **/
	public String getRoleB() {
		return this.roleB;
	}

	/** 設定徵信單位經辦 **/
	public void setRoleB(String value) {
		this.roleB = value;
	}

	/** 取得徵信報告完成日(C) **/
	public Date getDateC() {
		return this.dateC;
	}

	/** 設定徵信報告完成日(C) **/
	public void setDateC(Date value) {
		this.dateC = value;
	}

	/** 取得徵信單位覆核主管 **/
	public String getRoleC() {
		return this.roleC;
	}

	/** 設定徵信單位覆核主管 **/
	public void setRoleC(String value) {
		this.roleC = value;
	}

	/** 取得授管中心收件日（分行簽報內容最終定稿送呈日）(D) **/
	public Date getDateD() {
		return this.dateD;
	}

	/** 設定授管中心收件日（分行簽報內容最終定稿送呈日）(D) **/
	public void setDateD(Date value) {
		this.dateD = value;
	}

	/** 取得授管中心審查經辦 **/
	public String getRoleD() {
		return this.roleD;
	}

	/** 設定授管中心審查經辦 **/
	public void setRoleD(String value) {
		this.roleD = value;
	}

	/** 取得授審處收件日（授管中心審查完成日/分行簽報內容最終定稿送呈日）(E) **/
	public Date getDateE() {
		return this.dateE;
	}

	/** 設定授審處收件日（授管中心審查完成日/分行簽報內容最終定稿送呈日）(E) **/
	public void setDateE(Date value) {
		this.dateE = value;
	}

	/** 取得提會日期(授審會)(F) **/
	public Date getDateF1() {
		return this.dateF1;
	}

	/** 設定提會日期(授審會)(F) **/
	public void setDateF1(Date value) {
		this.dateF1 = value;
	}

	/** 取得提會日期(常董會)(F) **/
	public Date getDateF2() {
		return this.dateF2;
	}

	/** 設定提會日期(常董會)(F) **/
	public void setDateF2(Date value) {
		this.dateF2 = value;
	}

	/** 取得提會日期(催收會)(F) **/
	public Date getDateF3() {
		return this.dateF3;
	}

	/** 設定提會日期(催收會)(F) **/
	public void setDateF3(Date value) {
		this.dateF3 = value;
	}
	
	/** 取得提會日期(審計委員會)(F) **/
	public Date getDateF4() {
		return this.dateF4;
	}

	/** 設定提會日期(審計委員會)(F) **/
	public void setDateF4(Date value) {
		this.dateF4 = value;
	}

	/** 取得最終核決層級核定日(G) **/
	public Date getDateG() {
		return this.dateG;
	}

	/** 設定最終核決層級核定日(G) **/
	public void setDateG(Date value) {
		this.dateG = value;
	}

	/** 取得授審處審查經辦 **/
	public String getRoleG() {
		return this.roleG;
	}

	/** 設定授審處審查經辦 **/
	public void setRoleG(String value) {
		this.roleG = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	public void setCaseCancelDate(Date caseCancelDate) {
		this.caseCancelDate = caseCancelDate;
	}

	public Date getCaseCancelDate() {
		return caseCancelDate;
	}

	/**
	 * 設定海外總行收件日
	 * 
	 * @param roleH
	 */
	public void setDateH(Date dateH) {
		this.dateH = dateH;
	}

	/**
	 * 取得海外總行收件日
	 * 
	 * @return
	 */
	public Date getDateH() {
		return dateH;
	}

	/**
	 * 設定海外總行審查經辦
	 * 
	 * @param roleH
	 */
	public void setRoleH(String roleH) {
		this.roleH = roleH;
	}

	/**
	 * 取得海外總行審查經辦
	 * 
	 * @return
	 */
	public String getRoleH() {
		return roleH;
	}
}
