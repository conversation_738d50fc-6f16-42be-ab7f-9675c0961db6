/* 
 *  MicroEntFormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.handler.form;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;


import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.LMSCombinePrintService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.ejcic.service.EjcicService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01I;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

@Scope("request")
@Controller("lmscombineprintformhandler")
public class LMSCombinePrintFormHandler extends AbstractFormHandler {

	@Resource
	LMSCombinePrintService lmsCombinePrintService;

	@Resource
	EjcicService ejcicService;

	@Resource
	EloandbBASEService eloandbBASEService;

	@Resource
	LMSService lmsService;

	@Resource
	DocFileService docFileService;

	/**
	 * 勾選(授審會)(常董會)
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult choiceFile(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String type = Util.trim(params.getString("type"));
		List<String> selectItemList = new ArrayList<String>();

		if (Util.equals(type, "")) {
			type = "1";
		}

		// 1.勾選(授審會)
		// 2.勾選(常董會_個案)
		// 3.勾選(常董會_彙總)
		String LMS_RPA_COMBINE_SNED_ITEM = Util.trim(lmsService
				.getSysParamDataValue("LMS_RPA_COMBINE_SNED_ITEM_" + type));

		if (Util.notEquals(LMS_RPA_COMBINE_SNED_ITEM, "")) {
			String[] itemArr = LMS_RPA_COMBINE_SNED_ITEM
					.split(UtilConstants.Mark.SPILT_MARK);
			for (String xx : itemArr) {
				selectItemList.add(xx);
			}
		}

		result.set("selectItemList", selectItemList);
		return result;
	}

	/**
	 * 修改檔案資訊
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveFileInfo(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = params.getString(EloanConstants.MAIN_ID);
		String fieldId = params.getString("fieldId");
		String data = params.getString("data");
		JSONObject jsonData = JSONObject.fromObject(data);

		lmsCombinePrintService.saveFileInfo(mainId, fieldId, jsonData);

		return result;// 傳回執行這個動作的AjAX
	}

	/**
	 * 合併列印
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult sendCreatDoc(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));

		L120M01A l120m01a = lmsService
				.findModelByMainId(L120M01A.class, mainId);
		L120M01I l120m01i = null;
		if (l120m01a != null) {
			l120m01i = l120m01a.getL120m01i();
			if (l120m01i != null) {
				// 傳送RPA
				lmsCombinePrintService.sendCreatDoc(params);
			}
		}

		result.set("combinePrint_unid", "");
		result.set("combinePrint_time", "");
		l120m01i = l120m01a.getL120m01i();
		if (l120m01i != null) {
			result.set("combinePrint_unid",
					Util.trim(l120m01i.getCombinePrint_unid()));
			result.set("combinePrint_time",
					l120m01i.getCombinePrint_time() == null ? "" : l120m01i
							.getCombinePrint_time().toString());
		}

		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		return result;
	}

	/**
	 * 清除
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult clearRpaCombine(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));

		lmsCombinePrintService.clearRpaCombine(mainId);

		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		return result;
	}

	/**
	 * 初始化資料
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult initData(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();

		return result;
	}

	/**
	 * 更新顯示合併列印時間與UNID
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult updateCombinePrintInfo(PageParameters params) throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		L120M01A l120m01a = lmsService
				.findModelByMainId(L120M01A.class, mainId);
		L120M01I l120m01i = null;

		result.set("combinePrint_unid", "");
		result.set("combinePrint_time", "");
		result.set("combinePrint_status", "");
		result.set("combinePrint_errMsg", "");

		if (l120m01a != null) {
			l120m01i = l120m01a.getL120m01i();
			if (l120m01i != null) {
				result.set("combinePrint_unid",
						Util.trim(l120m01i.getCombinePrint_unid()));
				result.set("combinePrint_time",
						l120m01i.getCombinePrint_time() == null ? "" : l120m01i
								.getCombinePrint_time().toString());
				result.set("combinePrint_status",
						Util.trim(l120m01i.getCombinePrint_status()));
				result.set("combinePrint_errMsg",
						Util.trim(l120m01i.getCombinePrint_errMsg()));
			}

		}

		// 是否要顯示 授審會/常董會提案稿
		String LMS_SHOW_LMSS08O = Util.trim(lmsService
				.getSysParamDataValue("LMS_SHOW_LMSS08O"));

		String showLmss08o = "N";
		if (Util.equals(LMS_SHOW_LMSS08O, "Y")) {
			if (UtilConstants.Casedoc.DocType.企金.equals(l120m01a.getDocType())
					&& UtilConstants.Casedoc.DocKind.授權外.equals(l120m01a
							.getDocKind())) {
				if (UtilConstants.Casedoc.DocCode.陳復陳述案.equals(l120m01a
						.getDocCode())
						|| UtilConstants.Casedoc.DocCode.異常通報.equals(l120m01a
								.getDocCode())) {
					showLmss08o = "N";
				} else {
					showLmss08o = "Y";
				}
			}
		}

		result.set("showLmss08o", showLmss08o);

		return result;
	}

	/**
	 * 確認合併列印是否可以執行(超過三分鐘)
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult checkCanSend(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));

		L120M01A l120m01a = lmsService
				.findModelByMainId(L120M01A.class, mainId);
		L120M01I l120m01i = null;
		if (l120m01a != null) {
			l120m01i = l120m01a.getL120m01i();
			if (l120m01i != null) {
				String combinePrint_status = Util.trim(l120m01i
						.getCombinePrint_status());
				if (Util.equals(combinePrint_status,
						UtilConstants.RPA.STATUS.查詢中)) {
					// Timestamp 要另外NEW，要不然會影響到l120m01i.getCombinePrint_time()
					Timestamp combinePrint_time = new Timestamp(l120m01i
							.getCombinePrint_time().getTime());

					long m = 3; // 預設三分鐘

					String LMS_RPA_COMBINE_SEND_RETRY = Util
							.trim(lmsService
									.getSysParamDataValue("LMS_RPA_COMBINE_SEND_RETRY"));

					if (Util.notEquals(LMS_RPA_COMBINE_SEND_RETRY, "")) {
						m = Util.parseLong(LMS_RPA_COMBINE_SEND_RETRY);
					}

					// String x1 = l120m01i.getCombinePrint_time().toString();

					combinePrint_time.setTime(combinePrint_time.getTime()
							+ TimeUnit.MINUTES.toMillis(m));

					// String x2 = l120m01i.getCombinePrint_time().toString();

					if (combinePrint_time.after(CapDate.getCurrentTimestamp())) {
						throw new CapMessageException("前次送出列印時間為「"
								+ l120m01i.getCombinePrint_time()
								+ "」，請於3分鐘後再送出", getClass());
					}
				}

			}
		}

		// 檢查檔案有沒有被異動過
		if (l120m01a != null) {
			String LMS_RPA_COMBINE_COPY_FILE = Util.trim(lmsService
					.getSysParamDataValue("LMS_RPA_COMBINE_COPY_FILE"));

			if (Util.notEquals(LMS_RPA_COMBINE_COPY_FILE, "")) {
				JSONObject json = JSONObject.fromObject("{"
						+ LMS_RPA_COMBINE_COPY_FILE + "}");

				// 處理附加檔案複製
				List<DocFile> listFile = docFileService.findByIDAndPid(mainId,
						null);
				StringBuffer errorFileHasChange = new StringBuffer("");
				if (!listFile.isEmpty()) {
					for (DocFile file : listFile) {
						// String fid = file.getOid();

						String fileId = file.getFieldId();

						if (json.containsKey(fileId)) {

							if (Util.notEquals(Util.trim(file.getFlag()), "S")) {
								String fileInfo = json.optString(fileId);

								String[] fileInfoArr = fileInfo
										.split(UtilConstants.Mark.SPILT_MARK);
								String fileItemNo = fileInfoArr[0];
								String fileName = fileInfoArr[1];
								errorFileHasChange
										.append((Util.equals(
												errorFileHasChange, "") ? ""
												: "、")).append(
												fileItemNo + "." + fileName);
							}

						}

					}
				}

				if (Util.notEquals(errorFileHasChange.toString(), "")) {
					throw new CapMessageException("本次列印的檔案，來源檔「"
							+ errorFileHasChange.toString()
							+ "」已被異動過，請重新執行「匯入」按鈕", getClass());
				}
			}

		}

		return result;
	}

}
