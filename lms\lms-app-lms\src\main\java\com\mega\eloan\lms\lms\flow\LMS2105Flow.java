/* 
 *  LMS2105Flow.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.flow;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.mega.eloan.common.flow.AbstractFlowHandler;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dao.L120M01BDao;
import com.mega.eloan.lms.dao.L140M01ADao;
import com.mega.eloan.lms.dao.L160M01ADao;
import com.mega.eloan.lms.dao.L210M01ADao;
import com.mega.eloan.lms.dao.L210S01ADao;
import com.mega.eloan.lms.dao.L210S01BDao;
import com.mega.eloan.lms.mfaloan.service.MisQuotapprService;
import com.mega.eloan.lms.mfaloan.service.MisQuotunioService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01B;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L160M01A;
import com.mega.eloan.lms.model.L160M01B;
import com.mega.eloan.lms.model.L161S01A;
import com.mega.eloan.lms.model.L161S01B;
import com.mega.eloan.lms.model.L210M01A;
import com.mega.eloan.lms.model.L210S01A;
import com.mega.eloan.lms.model.L210S01B;
import com.mega.eloan.lms.obsdb.service.ObsdbELF385Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;

/**
 * <pre>
 * 修改資料特殊流程
 * </pre>
 * 
 * @since 2011/01/11
 * <AUTHOR>
 * @version <ul>
 *          2011/01/11,REX,new
 *          </ul>
 */
@Component
public class LMS2105Flow extends AbstractFlowHandler {

	@Resource
	L210M01ADao l210m01aDao;

	@Resource
	L160M01ADao l160m01aDao;

	@Resource
	L120M01ADao l120m01aDao;
	@Resource
	L120M01BDao l120m01bDao;
	@Resource
	L210S01ADao l210s01aDao;

	@Resource
	L210S01BDao l210s01bDao;
	@Resource
	L140M01ADao l140m01aDao;

	@Resource
	MisQuotunioService misQuotunioService;

	@Resource
	ObsdbELF385Service obsdbELF385Service;

	@Resource
	BranchService branchService;

	@Resource
	MisdbBASEService misdbBASEService;

	@Resource
	MisQuotapprService misQuotapprService;

	// node 決策點的name value 線的name
	@Transition(node = "確認", value = "to核定")
	public void check(FlowInstance instance) throws CapMessageException {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L210M01A meta = l210m01aDao.findByOid(instanceId);
		L120M01A l120m01a = null;
		L120M01B l120m01b = null;

		if (meta != null) {

			L160M01A l160m01a = l160m01aDao.findByMainId(meta.getRefMainId());
			if (l160m01a != null) {
				l120m01a = l120m01aDao.findByMainId(l160m01a.getSrcMainId());
				if (l120m01a != null) {
					l120m01a.setReEstFlag(null);
					l120m01aDao.save(l120m01a);
				}
				l120m01b = l120m01bDao.findByUniqueKey(l160m01a.getSrcMainId());
			}

			// 資料修改人
			String updater = MegaSSOSecurityContext.getUserId();
			String mainId = meta.getMainId();
			String cntrNo = meta.getCntrNo();
			List<L210S01A> l210s01as = l210s01aDao.findByMainIdAndChgFlag(
					mainId, UtilConstants.editDoc.chanFlag.變動後);
			List<Object[]> quotunioList = new ArrayList<Object[]>();
			List<Object[]> as400List = new ArrayList<Object[]>();
			boolean upAS400 = false;
			String BRNID = meta.getOwnBrId();
			if (UtilConstants.BrNoType.國外.equals(branchService.getBranch(BRNID)
					.getBrNoFlag())) {
				upAS400 = true;
			}
			if (!l210s01as.isEmpty()) {

				StringBuffer branchCode = new StringBuffer();
				for (L210S01A l210s01a : l210s01as) {
					IBranch branchData = branchService.getBranch(l210s01a
							.getShareBrId());
					// 當為海外分行其前三碼為999
					if (UtilConstants.BrNoType.國外.equals(branchData
							.getBrNoFlag())) {
						branchCode.append("999").append(
								misdbBASEService.findICBCBRByBrId(l210s01a
										.getShareBrId()));
					} else {
						branchCode.append("017")
								.append(l210s01a.getShareBrId());
					}

					// 2012_07_19_MIS 只有主辦行 可以上,簽報書簽案行 與額度序號前三碼相同時 才可以上傳
					if (BRNID.equals(cntrNo.substring(0, 3))) {
						misQuotunioService.delByCntrNo(cntrNo,
								UtilConstants.Usedoc.unioType.自行聯貸);
						quotunioList
								.add(new Object[] {
										UtilConstants.Usedoc.unioType.自行聯貸,
										cntrNo,
										branchCode.toString(),
										BRNID.equals(l210s01a.getShareBrId()) ? UtilConstants.DEFAULT.是
												: "", l210s01a.getShareAmt(),
										updater });
					}

					if (upAS400) {
						obsdbELF385Service.delByCntrNo(BRNID, cntrNo,
								UtilConstants.Usedoc.unioType.自行聯貸);
						as400List
								.add(new Object[] {
										UtilConstants.Usedoc.unioType.自行聯貸,
										cntrNo,
										branchCode.toString(),
										BRNID.equals(l210s01a.getShareBrId()) ? UtilConstants.DEFAULT.是
												: "", l210s01a.getShareAmt(),
										updater, CapDate.getCurrentTimestamp() });
					}

					branchCode.setLength(0);
				}
			}

			List<L210S01B> l210s01bs = l210s01bDao.findByMainIdAndChgFlag(
					mainId, UtilConstants.editDoc.chanFlag.變動後);
			BigDecimal l210s01bTotalAmt = BigDecimal.ZERO;
			if (!l210s01bs.isEmpty()) {

				String branchCode = "";
				for (L210S01B l210s01b : l210s01bs) {
					l210s01bTotalAmt = l210s01bTotalAmt.add(Util
							.parseBigDecimal(l210s01b.getSlAmt()));
					branchCode = "";
					if ("017".equals(l210s01b.getSlBank())) {
						IBranch branchData = branchService.getBranch(l210s01b
								.getSlBranch());
						// 當為海外分行其前三碼為999
						if (UtilConstants.BrNoType.國外.equals(branchData
								.getBrNoFlag())) {

							branchCode = StrUtils.concat("999",
									misdbBASEService.findICBCBRByBrId(l210s01b
											.getSlBranch()));

						} else {
							branchCode = StrUtils.concat("017",
									l210s01b.getSlBranch());

						}
					} else {
						if (Util.isNotEmpty(Util.trim(l210s01b.getSlBranch()))) {
							branchCode = StrUtils.concat(l210s01b.getSlBank(),
									l210s01b.getSlBranch().substring(3, 6));
						} else {
							branchCode = l210s01b.getSlBank();
						}

					}

					// 12 -國外銀行
					if ("12".equals(l210s01b.getSlBankType())
							|| "99".equals(l210s01b.getSlBankType())) {
						branchCode = l210s01b.getSlBank();

					}

					// 2012_07_19_MIS 只有主辦行 可以上,簽報書簽案行 與額度序號前三碼相同時 才可以上傳
					if (BRNID.equals(cntrNo.substring(0, 3))) {
						// 先刪除該額度序號的檔案
						misQuotunioService.delByCntrNo(cntrNo,
								UtilConstants.Usedoc.unioType.同業聯貸);
						quotunioList.add(new Object[] {
								UtilConstants.Usedoc.unioType.同業聯貸, cntrNo,
								branchCode, l210s01b.getSlMaster(),
								l210s01b.getSlAmt(), updater });
					}

					if (upAS400) {
						obsdbELF385Service.delByCntrNo(BRNID, cntrNo,
								UtilConstants.Usedoc.unioType.同業聯貸);
						as400List.add(new Object[] {
								UtilConstants.Usedoc.unioType.同業聯貸, cntrNo,
								branchCode, l210s01b.getSlMaster(),
								l210s01b.getSlAmt(), updater,
								CapDate.getCurrentTimestamp() });
					}

				}
			}

			if (!quotunioList.isEmpty()) {
				misQuotunioService.insert(quotunioList);
			}
			if (!as400List.isEmpty()) {
				obsdbELF385Service.insert(BRNID,
						LMSUtil.covertAs400Time(as400List));
			}

			String custId = meta.getCustId();
			String dupNo = meta.getDupNo();
			List<Map<String, Object>> findBy2105 = misQuotapprService
					.findBy2105(custId, dupNo, meta.getCntrNo());

			String sDate = "";
			if (Util.isEmpty(meta.getApproveTime())) {
				sDate = CapDate.formatDate(new Date(),
						UtilConstants.DateFormat.YYYY_MM_DD);
			} else {
				sDate = CapDate.formatDate(meta.getApproveTime(),
						UtilConstants.DateFormat.YYYY_MM_DD);
			}

			// 如果動審表有同業聯貸，則特殊流程的LTAMT為總額度
			// 反之則為額度明細表的現請額度
			BigDecimal ltAmt = meta.getLtAmt();

			if (UtilConstants.editDoc.caseType.同業聯貸.equals(meta.getCaseType())) {
				ltAmt = l210s01bTotalAmt;
			}

			BigDecimal curAmt = ltAmt;

			// 簽報書案號-> 民國年 + 分行別+LMS+末五碼流水號
			String documentNo = "";
			if (l120m01a != null) {
				documentNo = LMSUtil.getUploadCaseNo(l120m01a);
			}

			String caseType = "";
			String hideunion = "";
			String UArea = "";
			String unionRole = "";
			String permitType = "";
			String riskArea = "";
			String setDate = "";

			String unitLoanCase = Util.trim(l160m01a.getUnitLoanCase());
			String uCMainBranch = Util.trim(l160m01a.getUCMainBranch());

			if (l120m01a != null && l120m01b != null && l160m01a != null) {

				//L161S01A l161s01a = l160m01a.getL161S01A();
                
				L161S01A l161s01a=null;
				
				if (Util.equals(unitLoanCase, "Y")) {

					String selCntrNo = l161s01a.getCntrNo();
					for (L160M01B l160m01b : l160m01a.getL160m01b()) {
						String tCntrNo = l160m01b.getCntrNo();
						if (Util.notEquals(tCntrNo, "")) {
							String reMainId = l160m01b.getReMainId();
							if (Util.equals(selCntrNo, tCntrNo)) {
								L140M01A l140m01a = l140m01aDao
										.findByMainId(reMainId);
								String unitCase2 = l140m01a.getUnitCase2();

								// 報核方式***********************************************

								if (UtilConstants.BankNo.資訊處
										.equals(MegaSSOSecurityContext
												.getUnitNo())
										|| UtilConstants.BankNo.國金部
												.equals(MegaSSOSecurityContext
														.getUnitNo())) {
									if (l120m01b != null) {
										permitType = StrUtils.concat(
												Util.trim(l120m01b.getURP1()),
												Util.trim(l120m01b.getURP2()),
												Util.trim(l120m01b.getURP3()));
										if ("AAA".equals(permitType)) {
											permitType = "1";
										} else if ("AAB".equals(permitType)) {
											permitType = "2";
										} else if ("ABA".equals(permitType)) {
											permitType = "3";
										} else if ("ABB".equals(permitType)) {
											permitType = "4";
										} else if ("BAA".equals(permitType)) {
											permitType = "5";
										} else if ("BAB".equals(permitType)) {
											permitType = "6";
										} else if ("BBA".equals(permitType)) {
											permitType = "7";
										} else if ("BBB".equals(permitType)) {
											permitType = "8";
										} else {
											permitType = "";
										}

									}

								}

								if (l120m01b != null) {
									hideunion = Util.trim(l120m01b
											.getUHideName());
									UArea = Util.trim(l120m01b.getUArea());
									if (UtilConstants.DEFAULT.是.equals(l120m01b
											.getUCMainBranch())) {
										unionRole = "Y";
									}
									if (UtilConstants.DEFAULT.是.equals(l120m01b
											.getUCntBranch())) {
										unionRole += "L";
									}
									if (UtilConstants.DEFAULT.是.equals(l120m01b
											.getUCMSBranch())) {
										unionRole += "C";
									}
								}
								riskArea = Util.trim(l140m01a.getRiskArea());
								setDate = CapDate.formatDate(
										l120m01a.getEndDate(),
										UtilConstants.DateFormat.YYYY_MM_DD);
								if (Util.isEmpty(setDate)) {
									setDate = CapDate
											.formatDate(
													l120m01a.getApproveTime(),
													UtilConstants.DateFormat.YYYY_MM_DD);
								}

								// ***************************************************
								// 本案是否有同業聯貸案額度
								String tmpUnitCase = "";
								String tmpMainBranch = "";
								if (l120m01b != null
										&& UtilConstants.DEFAULT.是
												.equals(l120m01b.getUnitCase())) {
									tmpMainBranch = l120m01b.getUCMainBranch();
									if (Util.isEmpty(l140m01a.getUnitCase2())) {
										tmpUnitCase = UtilConstants.DEFAULT.否;
									} else {
										tmpUnitCase = l140m01a.getUnitCase2();

									}

								} else {
									tmpUnitCase = UtilConstants.DEFAULT.否;
									tmpMainBranch = UtilConstants.DEFAULT.否;
								}

								/** ---判斷案件性質------ */
								if (UtilConstants.DEFAULT.是.equals(tmpUnitCase)) {
									if (UtilConstants.DEFAULT.是
											.equals(tmpMainBranch)) {
										if (l140m01a.getL140m01e().isEmpty()) {
											caseType = UtilConstants.Usedoc.caseType.同業聯貸主辦;
										} else {
											caseType = UtilConstants.Usedoc.caseType.同業聯貸主辦含自行聯貸;
										}
									} else {
										if (l140m01a.getL140m01e().isEmpty()) {
											caseType = UtilConstants.Usedoc.caseType.同業聯貸參貸;
										} else {
											caseType = UtilConstants.Usedoc.caseType.同業聯貸參貸含自行聯貸;
										}
									}

								} else {// 非同業聯貸
									if (l140m01a.getL140m01e().isEmpty()) {
										caseType = UtilConstants.Usedoc.caseType.一般貸款;
									} else {

										caseType = UtilConstants.Usedoc.caseType.自行聯貸;
									}
								}

								// ***************************************************

								break;
							}
						}

					}

					// 加強檢核動審表同業參貸比例有沒有勾兆豐為主辦行，如果有且簽報書勾非主辦行，則改上傳QUOTAPPR時由參貸改為主辦

					Set<L161S01B> l161s01bs = l161s01a.getL161s01b();
					String hasMainFlag = "N";
					String slBankType = "";
					String slBank = "";
					String slMaster = "";
					for (L161S01B l161s01b : l161s01bs) {// 取出這個mainID底下的最大Seq
						slBankType = l161s01b.getSlBankType();
						slBank = l161s01b.getSlBank();
						slMaster = l161s01b.getSlMaster();
						if (Util.equals(slBankType, "01")
								&& Util.equals(slBank, "017")) {
							if (Util.equals(slMaster, "Y")) {
								hasMainFlag = "Y";
								break;
							}
						}
					}
					if (Util.equals(hasMainFlag, "Y")) {
						if (Util.equals(caseType,
								UtilConstants.Usedoc.caseType.同業聯貸參貸)) {
							caseType = UtilConstants.Usedoc.caseType.同業聯貸主辦;
						} else if (Util.equals(caseType,
								UtilConstants.Usedoc.caseType.同業聯貸參貸含自行聯貸)) {
							caseType = UtilConstants.Usedoc.caseType.同業聯貸主辦含自行聯貸;
						}
					}
				}// if (Util.equals(unitLoanCase, "Y")) {
			}

			if (findBy2105 == null || findBy2105.isEmpty()) {

				// 同一天做動審表引進ALOAN後再做特殊流程1.QUOTAPPR custId,
				// dupNo,cntrno,sdate有一筆且ONLNTIME有值(NOT NULL)
				// 暫無解，目前由分行調整輸入之核准日期
				// 同一天做兩次特殊流程1.QUOTAPPR custId,
				// dupNo,cntrno,sdate有一筆且ONLNTIME有值(NOT NULL)
				List<Map<String, Object>> findBy2105OnlyU = misQuotapprService
						.findBy2105OnlyU(custId, dupNo, meta.getCntrNo(), sDate);

				if (findBy2105OnlyU != null && !findBy2105OnlyU.isEmpty()) {
					// 若custId, dupNo,cntrno,sdate 有重複的要先刪除
					misQuotapprService.delBy2105OnlyU(custId, dupNo,
							meta.getCntrNo(), sDate);
				}

				List<Map<String, Object>> findBy2105IsExist = misQuotapprService
						.findBy2105IsExist(custId, dupNo, meta.getCntrNo(),
								sDate);

				if (!(findBy2105IsExist == null || findBy2105IsExist.isEmpty())) {
					throw new CapMessageException(RespMsgHelper.getMessage("EFD3048")
							+ "<br> ERROR DATE:" + sDate, getClass());
				}

				// caseType,permitType,hideunion,setDate,UArea,unionRole,riskArea
				misQuotapprService.insertBy2105(custId, dupNo, cntrNo, sDate,
						ltAmt, updater, curAmt, documentNo, caseType,
						permitType, hideunion, setDate, UArea, unionRole,
						riskArea);
			} else {
				misQuotapprService.updateByUniqueKey(ltAmt, updater, curAmt,
						caseType, custId, dupNo, cntrNo);
			}

		}
	}

	@Transition(node = "確認", value = "to已婉卻")
	public void reject(FlowInstance instance) {
	}

	@Transition(node = "海外_編製中")
	public void test2(FlowInstance instance) {
	}

	@Transition(node = "海外_待覆核")
	public void test3(FlowInstance instance) {
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return L210M01A.class;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Class getDocStatusEnumClass() {
		return CreditDocStatusEnum.class;
	}
}