/*
 * IUser.java
 *
 * Copyright (c) 2009-2011 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
 */
package tw.com.iisi.cap.security.model;

import java.io.Serializable;
import java.util.Map;

/**
 * <p>
 * 使用者資料.
 * </p>
 * 
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2010/7/26,iristu,new
 *          </ul>
 */
public interface IUser extends Serializable {

    /**
     * 取得使用者ID
     * 
     * @return
     */
    String getUserId();

    /**
     * 取得使用者名稱
     * 
     * @return
     */
    String getUserName();

    /**
     * 取得 getUnitNo
     * 
     * @return
     */
    String getUnitNo();

    /**
     * 取得角色
     * 
     * @return
     */
    Map<String, String> getRoles();

}
