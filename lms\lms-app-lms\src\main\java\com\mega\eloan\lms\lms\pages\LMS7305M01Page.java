package com.mega.eloan.lms.lms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;

/**
 * <pre>
 * 案件報案考核表-分頁
 * </pre>
 * 
 * @since 2012/3/22
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/3/22,<PERSON>,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms7305m01/{page}")
public class LMS7305M01Page extends AbstractEloanForm {

	@Override
	public void afterExecute(ModelMap model, PageParameters parameters) {
		super.afterExecute(parameters);
		// UPGRADE: 前端須配合改Thymeleaf的樣式
		// remove("_headerPanel");
		model.addAttribute("showHeader", false); // 不顯示 _headerPanel

	}

	private static final long serialVersionUID = 1L;

	@Override
	public Class<? extends Meta> getDomainClass() {
		return null;
	}
}
