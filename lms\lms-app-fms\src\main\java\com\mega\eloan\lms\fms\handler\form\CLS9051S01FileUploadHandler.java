/* 
 * CLS9051S01FileUploadHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.handler.form;

import java.io.File;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.RelatedAccountService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;

import jxl.CellType;
import jxl.DateCell;
import jxl.SheetSettings;
import jxl.Workbook;
import jxl.format.Alignment;
import jxl.format.PageOrientation;
import jxl.format.PaperSize;
import jxl.write.Label;
import jxl.write.WritableCellFormat;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

@Scope("request")
@Controller("cls9051s01fileuploadhandler")
public class CLS9051S01FileUploadHandler extends AbstractFormHandler {

	@Resource
	DocFileService dfs;

	@Resource
	MisdbBASEService misdbBASEService;
	
	@Resource
	RelatedAccountService relatedAccountService;

	public IResult genMainId(PageParameters params)
	throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set("mainId", IDGenerator.getUUID());
		return result;
	}
	/**
	 * 匯入EXCEL作業
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult importExcelStep(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String excelId = Util.trim(params.getString("excelId"));
		DocFile docFile = dfs.read(excelId);
		if (docFile != null) {

			WritableWorkbook workbook = null;
			Label label = null;
			String filePath = dfs.getFilePath(docFile);
			try {
				//List<GenericBean> list = new ArrayList<GenericBean>();

				workbook = Workbook.createWorkbook(new File(filePath),
						Workbook.getWorkbook(new File(filePath)));
				WritableSheet sheet = workbook.getSheet(0);

				List<Map<String, Object>> loanNoDataList = new LinkedList<Map<String, Object>>();
				String loanNos = new String();

				String DataYYmm = sheet.getCell(0, 1).getContents();
				if(sheet.getCell(0, 1).getType()==CellType.DATE){
					DateCell dateCell = (DateCell)sheet.getCell(0, 1);
					DataYYmm = new SimpleDateFormat(UtilConstants.DateFormat.YYYY_MM).format(dateCell.getDate());	
				}
				
				docFile.setFileDesc(DataYYmm);
				dfs.save(docFile);
				
				List<String> loanNo_list = new ArrayList<String>();
				for (int y = 3; y <= 9999; y++) {
					String str = Util.trim(sheet.getCell(5, y).getContents());
					if(Util.isEmpty(str)){
						break;
					}
					loanNo_list.add(str);
				}
				if(loanNo_list.size()==0){
					loanNo_list.add("");
				}
//				loanNos = StringUtils.join(loanNo_list, ",");
				
				loanNoDataList = misdbBASEService.findLNF155_findByLoanNo(
						loanNo_list.toArray(new String[0]), DataYYmm);
				{//產生結果的 worksheet
					sheet = workbook.createSheet("Result", 1);
					SheetSettings setting = sheet.getSettings();
					setting.setPaperSize(PaperSize.A4);
					setting.setFitWidth(1);
					setting.setOrientation(PageOrientation.PORTRAIT);
					sheet.setRowView(0, 500);
										
					label = new Label(0, 0, "LNF155_LOAN_NO");
					sheet.addCell(label);
					
					label = new Label(1, 0, "LNF155_LOAN_BAL_TW");
					sheet.addCell(label);
					
					label = new Label(2, 0, "LNF155_SITE1NO");
					sheet.addCell(label);
					
					label = new Label(3, 0, "LNF155_SITE2NO");
					sheet.addCell(label);
					
					label = new Label(4, 0, "ELF443_CITYNM");
					sheet.addCell(label);
				}
				if (loanNoDataList.size() > 0) {
					int y = 1;
					WritableCellFormat formatRight = new WritableCellFormat();
					formatRight.setAlignment(Alignment.RIGHT);
					for (Map<String, Object> map : loanNoDataList) {
						String loanNo = MapUtils.getString(map, "LNF155_LOAN_NO");
						BigDecimal loanBal = (BigDecimal)MapUtils.getObject(map, "LNF155_LOAN_BAL_TW");
						String site1No = MapUtils.getString(map, "LNF155_SITE1NO");
						BigDecimal site2No = (BigDecimal)MapUtils.getObject(map, "LNF155_SITE2NO");
						String cityNm = MapUtils.getString(map, "ELF443_CITYNM");
						
						
						label = new Label(0, y, loanNo);
						sheet.addCell(label);
						
						label = new Label(1, y, loanBal.toString(), formatRight);
						sheet.addCell(label);
						
						label = new Label(2, y, site1No);
						sheet.addCell(label);
						
						label = new Label(3, y, site2No.toString(), formatRight);
						sheet.addCell(label);
						
						label = new Label(4, y, cityNm);
						sheet.addCell(label);
						
						y++;
					}
				}

				workbook.write();
				workbook.close();

			} catch (Exception e) {
				logger.error("parseExcel EXCEPTION!!", e);
				throw new CapMessageException(e, getClass());
			} finally {
				try {
					if (workbook != null) {
						workbook.write();
						workbook.close();
						workbook = null;
						
						//dfs.clean(excelId);
					}
				} catch (Exception e) {
					logger.debug("Workbook close EXCEPTION!", getClass());
				}
			}
		} else {
			throw new CapMessageException("請先上傳EXCEL", getClass());
		}
		
		return result;
	}// ;
	
	/**
	 * 刪除上傳檔案
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult deleteUploadFile(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String fid = params.getString("fileOid");
		if (relatedAccountService.delfile(fid)) {
			// EFD0019=INFO|刪除成功|
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
					.getMainMessage(UtilConstants.AJAX_RSP_MSG.刪除成功));
		}
		return result;
	}

}
