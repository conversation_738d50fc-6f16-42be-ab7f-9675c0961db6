package com.mega.eloan.lms.cls.pages;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CLSDocStatusEnum;
import com.mega.eloan.lms.cls.panels.CLS1220S01CPanel;
import com.mega.eloan.lms.cls.panels.CLS1220S02Panel;
import com.mega.eloan.lms.cls.panels.CLS1220S03Panel;
import com.mega.eloan.lms.cls.panels.CLS1220S04Panel;
import com.mega.eloan.lms.cls.panels.CLS1220S05Panel;
import com.mega.eloan.lms.cls.service.CLS1220Service;
import com.mega.eloan.lms.model.C122M01A;

import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 線上信貸
 * </pre>
 * 
 * @since 2019/6/6
 * <AUTHOR>
 * @version <ul>
 *          <li>2019/6/6,EL08034,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/cls1220m02/{page}")
public class CLS1220M02Page extends AbstractEloanForm {
	
	@Autowired
	CLS1220Service service;
	
	final String TAB_CTX = "_tabCtx";
	final String TAB_SIGN = "tab-";

	@Override
	public void execute(ModelMap model, PageParameters params) {
		addAclLabel(model, new AclLabel("_btnWAIT_APPROVE_visible", params,
				getDomainClass(), AuthType.Accept, CLSDocStatusEnum.待覆核));
		renderJsI18N(CLS1220M02Page.class);
		renderJsI18N(CLS1220V01Page.class);
		model.addAttribute("loadScript", "loadScript('pagejs/cls/CLS1220M02Page');");
		
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C122M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = service.getC122M01A_byOid(mainOid);
		}
		//----------------
		boolean show_btnToEdit = false;
		if(meta!=null){
			if(Util.equals(meta.getDocStatus(), CLSDocStatusEnum.編製中.getCode())
			 || Util.equals(meta.getDocStatus(), CLSDocStatusEnum.待覆核.getCode())){
				 //show_btnToEdit still false
			}else{
				if(Util.equals(meta.getApplyStatus(), UtilConstants.C122_ApplyStatus.受理中)
					|| Util.equals(meta.getApplyStatus(), UtilConstants.C122_ApplyStatus.審核中)){
					show_btnToEdit = true;
				}else if(Util.equals(meta.getApplyStatus(), UtilConstants.C122_ApplyStatus.已核准)){
					//若要多次改額度,利率
					show_btnToEdit = true;
				}else{
					 //show_btnToEdit still false[已結案時]					
				}
			}
		} 
		boolean show_btnSAVE = (meta != null && Util.equals(meta.getDocStatus(),
				CLSDocStatusEnum.編製中.getCode()));
		model.addAttribute("_btnToEdit_visible", show_btnToEdit);
		model.addAttribute("_btnSAVE_visible", show_btnSAVE);
		model.addAttribute("_btnDOC_EDITING_visible", show_btnSAVE);

		// tabs
		int page = Util.parseInt(params.getString("page"));
		String tabID = TAB_SIGN + Util.addZeroWithValue(page, 2); // 指定ID
		Panel panel = getPanel(page, meta);
		panel.processPanelData(model, params);
		model.addAttribute("tabIdx", tabID);
		
		renderJsI18N(CLS1220M02Page.class);
	}
	
	// 頁籤
	public Panel getPanel(int index, C122M01A meta) {
		Panel panel = null;
		
		switch (index) {
		case 1:
			boolean showSelect_c122s01a_batchNo = true;
			int maxBatchNo = service.maxBatchNoInC122S01A(meta.getMainId());
			if(maxBatchNo>1){
				showSelect_c122s01a_batchNo = true;
			}else if(maxBatchNo==1){
				Map<String, String> m = service.getC122S01A_batchNo(meta.getMainId(), Util.equals(meta.getDocStatus(), CLSDocStatusEnum.編製中.getCode()));
				showSelect_c122s01a_batchNo = (m.size()>1);
			}else{
				showSelect_c122s01a_batchNo = false;
			}
			panel = new CLS1220S01CPanel(TAB_CTX, true, meta,
					showSelect_c122s01a_batchNo);
			break;		
		case 2:
			panel = new CLS1220S02Panel(TAB_CTX, true);
			break;
		case 3:
			panel = new CLS1220S03Panel(TAB_CTX, true);
			break;
		case 4:
			panel = new CLS1220S04Panel(TAB_CTX, true);
			break;
		case 5:
			panel = new CLS1220S05Panel(TAB_CTX, true);
			break;
		}
		return panel;
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return C122M01A.class;
	}
}
