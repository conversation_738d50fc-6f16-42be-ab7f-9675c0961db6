/* 
 * L140S09BDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L140S09BDao;
import com.mega.eloan.lms.model.L140S09B;

/** 其他敘作條件細項資訊檔 **/
@Repository
public class L140S09BDaoImpl extends LMSJpaDao<L140S09B, String>
	implements L140S09BDao {

	@Override
	public L140S09B findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L140S09B> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addOrderBy("seqNum", false);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L140S09B> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public L140S09B findMaxSeqNumByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addOrderBy("seqNum", true);
		return findUniqueOrNone(search);
	}
}