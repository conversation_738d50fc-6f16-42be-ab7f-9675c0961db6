package com.mega.eloan.lms.cls.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;

import tw.com.jcs.common.Util;

/**
 * <pre>
 * AML/CFT
 * </pre>
 * 
 * @since 2017/3/27
 * <AUTHOR>
 * @version <ul>
 *          <li>2017/3/27, EL08034, new
 *          </ul>
 */
public class CLS1201S20Panel extends Panel {

	private String active_SAS_AML;

	public CLS1201S20Panel(String id) {
		super(id);
	}

	public CLS1201S20Panel(String id, boolean updatePanelName,
			String active_SAS_AML) {
		super(id, updatePanelName);
		this.active_SAS_AML = active_SAS_AML;
	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);
		
		boolean HS_importBlackList_s_lock = false;
		boolean HS_importBlackList_s_enb = false;
		boolean HS_queryBlackList_s = false;
		//~~~~~~
		boolean HS_sendAmlList_s_lock = false;
		boolean HS_sendAmlList_s_enb = false;
		boolean HS_checkAmlResult_s_lock = false;
		boolean HS_checkAmlResult_s_enb = false;
		//~~~~~~
		boolean HS_add_rm_l120s09a_s_lock = false;
		boolean HS_add_rm_l120s09a_s_enb = false;
		boolean HS_sas_columns = false;
		//=====================================
		if(Util.equals("0", active_SAS_AML)){ //原邏輯
			HS_importBlackList_s_enb = true;
			HS_queryBlackList_s = true;
			HS_add_rm_l120s09a_s_enb = true;
		}else if(Util.equals("1", active_SAS_AML)){ //SAS_AML且未LOCKEDIT（未開始掃描/已結束）
			HS_importBlackList_s_enb = true;
			HS_sendAmlList_s_enb = true;
			/*
			目前, lockEdit 只有針對 E00(掃描中)
			在未 lockEdit 的狀況下, 應該也要允許再去查詢狀態
			
			可能第1次查到的結果是001(Hit) , 並未lockEdit
			user 可能會有以下行動
			(1)檢視輸入的資料, 看是否因為 keyin錯誤, 導致Hit. 若這樣, 可能 user會在e-loan更改資料後, 再送查詢
			(2)user遇到001(Hit)後, 在AML輸入［調查結果, 可否承做］, 然後在e-loan按查詢去抓該 refNo 最新狀態
			*/
			HS_checkAmlResult_s_enb = true;
			HS_add_rm_l120s09a_s_enb = true;
			HS_sas_columns = true;
		}else if(Util.equals("2", active_SAS_AML)){	//SAS_AML且LOCKEDIT（掃描中）
			HS_importBlackList_s_lock = true;
			HS_sendAmlList_s_lock = true;
			HS_checkAmlResult_s_enb = true;
			HS_add_rm_l120s09a_s_lock = true;
			HS_sas_columns = true;
		}
		model.addAttribute("HS_importBlackList_s_lock",
				HS_importBlackList_s_lock);
		model.addAttribute("HS_importBlackList_s_enb",
				HS_importBlackList_s_enb);
		model.addAttribute("HS_queryBlackList_s", HS_queryBlackList_s);
		model.addAttribute("HS_sendAmlList_s_lock", HS_sendAmlList_s_lock);
		model.addAttribute("HS_sendAmlList_s_enb", HS_sendAmlList_s_enb);
		model.addAttribute("HS_checkAmlResult_s_lock",
				HS_checkAmlResult_s_lock);
		model.addAttribute("HS_checkAmlResult_s_enb", HS_checkAmlResult_s_enb);
		model.addAttribute("HS_add_rm_l120s09a_s_lock",
				HS_add_rm_l120s09a_s_lock);
		model.addAttribute("HS_add_rm_l120s09a_s_enb",
				HS_add_rm_l120s09a_s_enb);
		
		model.addAttribute("HS_sas_column01", HS_sas_columns);
		model.addAttribute("HS_sas_column02", HS_sas_columns);
		model.addAttribute("HS_sas_column03", HS_sas_columns);
		model.addAttribute("grid_column_sas_show", HS_sas_columns);
		model.addAttribute("grid_column_sas_hide", !HS_sas_columns);
		//=========
//		String format = "yyyyMMddhhmmss";
//		add(new Label("jsVer", CapDate.convertTimestampToString(CapDate.getCurrentTimestamp(), format)));
	}

	/**/
	private static final long serialVersionUID = 1L;

}
