package com.mega.eloan.lms.base.common;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.kordamp.json.JSONObject;

import com.mega.eloan.lms.base.constants.ClsScoreConstants;
import com.mega.eloan.lms.base.constants.ScoreCardLoan;
import com.mega.eloan.lms.base.constants.ScoreNotHouseLoan;
import com.mega.eloan.lms.model.C120S01Q;
import com.mega.eloan.lms.model.C120S01R;

import tw.com.jcs.common.Util;

public class ClsScoreUtil {
	public static final String PRODID_KEY = "prodId";
	public static final String[] UPLOAD_DW_HOUSING_MODEL_VERSION = new String[] {ClsScoreUtil.V2_0_HOUSE_LOAN, ClsScoreUtil.V2_1_HOUSE_LOAN};
	
	//房貸申請信用評等 在99.1.1上線 , 程修(098)1508
	
	/*
	 * 在 CLS1141M01Page.js 的 function check_clsRatingModel(){...} =>
	 * cls1141m01formhandler :: check_clsRatingModel 會判斷 varVersion 是否過期，而去 lock
	 * 簽報書頁面(重引latest version 後才解除)
	 */
	public static final String V1_3_HOUSE_LOAN = "1.3";
	public static final String V2_0_HOUSE_LOAN = "2.0"; // J-106-0187 
	public static final String V2_1_HOUSE_LOAN = "2.1"; // J-106-0187 
	public static final String V3_0_HOUSE_LOAN = "3.0"; // J-111-0271(棄用)
	//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	// 非房貸
	public static final String V1_0_NOT_HOUSE_LOAN = "1.0";
	public static final String V1_9_NOT_HOUSE_LOAN = "1.9"; // J-108-0041
	public static final String V2_0_NOT_HOUSE_LOAN = "2.0"; // J-103-0335
	public static final String V2_1_NOT_HOUSE_LOAN = "2.1"; // J-108-0105	
	public static final String V3_0_NOT_HOUSE_LOAN = "3.0"; // J-108-0266
	public static final String V3_1_NOT_HOUSE_LOAN = "3.1"; // J-112-0192
	public static final String V4_0_NOT_HOUSE_LOAN = "4.0"; // J-111-0373(棄用)
	//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	// 卡友貸
	public static final String V2_1_CARD_LOAN = "2.1";
	public static final String V3_0_CARD_LOAN = "3.0";
	public static final String V3_1_CARD_LOAN = "3.1"; // J-112-0192
	public static final String V4_0_CARD_LOAN = "4.0"; // J-111-0373(棄用)
	
	//模型代號
	public static final String ModelType_房貸 = "G";
	public static final String ModelType_非房貸 = "Q";
	public static final String ModelType_專案信貸 = "R";
	
	//房貸模型3.0-評分表參數配置
	public static final String[] scoreArr_G_3_0 = new String[] { "scrN22", "scrJobTitle", "scrD42", "scrEdu", "scrP68", "scrYRate", "scrN01", "scrP19", "scrNum07"};
	public static final String[] HighGapArr_G_3_0 = new String[] { "N22HighGap", "JobTitleHighGap", "D42HighGap", "EduHighGap", "P68HighGap", "yRateHighGap", "N01HighGap", "P19HighGap", "AvgHighGap"};
	public static final String[] InfluenceArr_G_3_0 = new String[] { "N22Inf", "JobTitleInf", "D42Inf", "EduInf", "P68Inf", "yRateInf", "N01Inf", "P19Inf", "AvgInf"};
	public static final String[] HighScoreArr_G_3_0 = new String[] { "15.41", "12.34", "13.61", "15.38", "9.71", "13.26", "12.48", "11.33", "10.7"};
	public static final String[] itemArr_G_3_0 = new String[] { "yRate","jobTitle","itemD42","itemN22","itemN01","edu","itemP68","chkNum3","avgRate01"};
	public static final String[] elseInfoArr_G_3_0 = new String[] { "grdCDate","grdTDate","grade2Status","jcicQDate","etchQDate","scrNum11","grade1","scrNum13","ttlHighGap","pd","varVer"};
	
	//非房貸模型4.0-評分表參數配置
	public static final String[] scoreArr_Q_4_0 = new String[] { "screducation", "scrP01", "noscrItemDrate", "scrLoanBalSByid", "scrMaxR01"};
	public static final String[] HighGapArr_Q_4_0 = new String[] { "EduHighGap", "P01HighGap", "dRateHighGap", "loanBalSByidHighGap", "MaxR01HighGap"};
	public static final String[] InfluenceArr_Q_4_0 = new String[] { "EduInf", "P01Inf", "dRateInf", "loanBalSByidInf", "MaxR01Inf"};
	public static final String[] HighScoreArr_Q_4_0 = new String[] { "25.44", "23.49", "24.74", "24.77", "20.66" };
	public static final String[] itemArr_Q_4_0 = new String[] { "education","itemP01","nochkItemDrate","loanBalSByid","itemMaxR01"};
	public static final String[] elseInfoArr_Q_4_0 = new String[] { "grdCDate","grdTDate","grade2Status","jcicQDate","etchQDate","scrNum11","grade1","scrNum13","ttlHighGap","pd","varVer"};
	
	//專案信貸(非團體)模型4.0-評分表參數配置
	public static final String[] scoreArr_R_4_0 = new String[] { "screducation", "scrP01", "noscrItemDrate", "scrLoanBalSByid", "scrMaxR01"};
	public static final String[] HighGapArr_R_4_0 = new String[] { "EduHighGap", "P01HighGap", "dRateHighGap", "loanBalSByidHighGap", "MaxR01HighGap"};
	public static final String[] InfluenceArr_R_4_0 = new String[] { "EduInf", "P01Inf", "dRateInf", "loanBalSByidInf", "MaxR01Inf"};
	public static final String[] HighScoreArr_R_4_0 = new String[] { "25.44", "23.49", "24.74", "24.77", "20.66" };
	public static final String[] itemArr_R_4_0 = new String[] { "education","itemP01","nochkItemDrate","loanBalSByid","itemMaxR01"};
	public static final String[] elseInfoArr_R_4_0 = new String[] { "grdCDate","grdTDate","grade2Status","jcicQDate","etchQDate","scrNum11","grade1","scrNum13","ttlHighGap","pd","varVer"};
	
		
	public static String upDW_column_C_FLAG(C120S01Q c120s01q){
		String varVer = c120s01q.getVarVer();
		if(Util.equals("", varVer)
			|| Util.equals(ClsScoreUtil.V1_0_NOT_HOUSE_LOAN, varVer)
			|| Util.equals(ClsScoreUtil.V1_9_NOT_HOUSE_LOAN, varVer)
			|| Util.equals(ClsScoreUtil.V2_0_NOT_HOUSE_LOAN, varVer) ){
			return "";		
		}
		
		/*
		非房貸模型2.1 , 增加卡友貸
		為與 卡友貸 區分, 原版的非房貸模型上傳 N
		*/ 
		return "N";
	}
	
	public static String upDW_column_C_FLAG(String varVer){

		if(Util.equals("", varVer)
			|| Util.equals(ClsScoreUtil.V1_0_NOT_HOUSE_LOAN, varVer)
			|| Util.equals(ClsScoreUtil.V1_9_NOT_HOUSE_LOAN, varVer)
			|| Util.equals(ClsScoreUtil.V2_0_NOT_HOUSE_LOAN, varVer) ){
			return "";		
		}
		
		/*
		非房貸模型2.1 , 增加卡友貸
		為與 卡友貸 區分, 原版的非房貸模型上傳 N
		*/ 
		return "N";
	}
	
	public static String upDW_column_MowType(C120S01R c120s01r){
		//回傳的 mowType == N
		return "N";
	}
	
	public static BigDecimal get_BigDecimal(JSONObject formObj, String key){
		return CrsUtil.parseBigDecimal(MapUtils.getString(formObj, key));
	}
	
	public static BigDecimal get_pIncome_from_uiC101S01BForm(JSONObject uiC101S01BForm){
		BigDecimal pIncome = get_BigDecimal(uiC101S01BForm, "payAmt").add( 
				get_BigDecimal(uiC101S01BForm, "othAmt"));
		return pIncome;
	}
	
	public static BigDecimal get_dRate_from_uiC101S01CForm(JSONObject uiC101S01CForm){
		Object val = MapUtils.getObject(uiC101S01CForm, ClsScoreConstants.ratingFactor_Q_V3_0.C101S01C.個人負債比率);
		if(val==null){
			return null;
		}
		return CrsUtil.parseBigDecimal(val);
	}
	
	public static String get_edu_from_uiC101S01AForm(JSONObject uiC101S01AForm){
		String val = MapUtils.getString(uiC101S01AForm, ClsScoreConstants.ratingFactor_Q_V4_0.C101S01A.非房貸學歷);
		if(val==null){
			return null;
		}
		return val;
	}
	/**
	 * key:c101s01Q 的欄位名
	 * val:c101s01b 的欄位名
	 * @return
	 */
	public static void set_c101s01q_factor_V2_0(JSONObject result, JSONObject uiC101S01BForm){
		result.put(ScoreNotHouseLoan.column.個人年所得, get_pIncome_from_uiC101S01BForm(uiC101S01BForm));
		result.put(ScoreNotHouseLoan.column.年資, uiC101S01BForm.get(ClsScoreConstants.ratingFactor_Q_V2_0.C101S01B.年資));
	}
	
	public static void set_c101s01q_factor_V2_1(JSONObject result, JSONObject uiC101S01BForm){
		result.put(ScoreNotHouseLoan.column.個人年所得, get_pIncome_from_uiC101S01BForm(uiC101S01BForm));
		result.put(ScoreNotHouseLoan.column.年資, uiC101S01BForm.get(ClsScoreConstants.ratingFactor_Q_V2_1.C101S01B.年資));
	}	
	
	public static void set_c101s01r_factor_V2_1(JSONObject result, JSONObject uiC101S01BForm){
		result.put(ScoreCardLoan.column.個人年所得, get_pIncome_from_uiC101S01BForm(uiC101S01BForm));
		result.put(ScoreCardLoan.column.年資, uiC101S01BForm.get(ClsScoreConstants.ratingFactor_R_V2_1.C101S01B.年資));
	}
	
	public static void set_c101s01q_factor_V3_0_c101s01b(JSONObject result, JSONObject uiC101S01BForm){
		result.put(ScoreNotHouseLoan.column.個人年所得, get_pIncome_from_uiC101S01BForm(uiC101S01BForm));
		result.put(ScoreNotHouseLoan.column.職稱, Util.trim(uiC101S01BForm.optString(ClsScoreConstants.ratingFactor_Q_V3_0.C101S01B.職稱, "")));
	}
	public static void set_c101s01q_factor_V3_0_c101s01c(JSONObject result, JSONObject uiC101S01CForm){
		BigDecimal val = get_dRate_from_uiC101S01CForm(uiC101S01CForm);
		result.put(ScoreNotHouseLoan.column.個人負債比率, val==null?"":val);
	}
	//-----消金非房貸4.0-----
	public static void set_c101s01q_factor_V4_0_c101s01a(JSONObject result, JSONObject uiC101S01AForm){
		String edu = Util.trim(uiC101S01AForm.optString(ClsScoreConstants.ratingFactor_Q_V4_0.C101S01A.非房貸學歷, ""));
		result.put(ScoreNotHouseLoan.column.nv4_學歷因子, edu==null?"":edu);
	}
	public static void set_c101s01q_factor_V4_0_c101s01c(JSONObject result, JSONObject uiC101S01CForm){
		BigDecimal val = get_dRate_from_uiC101S01CForm(uiC101S01CForm);
		result.put(ScoreNotHouseLoan.column.個人負債比率, val==null?"":val);
	}
	//-----消金非房貸4.0-----
	
	public static void set_c101s01r_factor_V3_0_c101s01b(JSONObject result, JSONObject uiC101S01BForm){
		result.put(ScoreCardLoan.column.個人年所得, get_pIncome_from_uiC101S01BForm(uiC101S01BForm));
	}
	public static void set_c101s01r_factor_V3_0_c101s01c(JSONObject result, JSONObject uiC101S01CForm){
		BigDecimal val = get_dRate_from_uiC101S01CForm(uiC101S01CForm);
		result.put(ScoreCardLoan.column.個人負債比率, val==null?"":val);
	}
	
	//-----消金專案信貸(非團體)4.0-----
	public static void set_c101s01r_factor_V4_0_c101s01a(JSONObject result, JSONObject uiC101S01AForm){
		String edu = Util.trim(uiC101S01AForm.optString(ClsScoreConstants.ratingFactor_Q_V4_0.C101S01A.非房貸學歷, ""));
		result.put(ScoreCardLoan.column.卡友貸學歷, edu==null?"":edu);
	}
	public static void set_c101s01r_factor_V4_0_c101s01c(JSONObject result, JSONObject uiC101S01CForm){
		BigDecimal val = get_dRate_from_uiC101S01CForm(uiC101S01CForm);
		result.put(ScoreCardLoan.column.個人負債比率, val==null?"":val);
	}
	//-----消金專案信貸(非團體)4.0-----
	
	
	public static String[] get_input_factor(String  tbl, String varVer) {
		if(Util.equals("G", tbl)){
			
		}else if(Util.equals("Q", tbl)){
			if (Util.equals(V3_0_NOT_HOUSE_LOAN, varVer) || Util.equals(V3_1_NOT_HOUSE_LOAN, varVer)) {
				return new String[]{ ScoreNotHouseLoan.column.個人年所得
						, ScoreNotHouseLoan.column.個人負債比率
						, ScoreNotHouseLoan.column.D07因子
						, ScoreNotHouseLoan.column.N06因子 , ScoreNotHouseLoan.column.職稱
						, ScoreNotHouseLoan.column.P25因子
						, ScoreNotHouseLoan.column.P69因子
						, ScoreNotHouseLoan.column.P19因子
						, ScoreNotHouseLoan.column.R01因子 };
			}else if(Util.equals(V4_0_NOT_HOUSE_LOAN, varVer)){
				return new String[]{ ScoreNotHouseLoan.column.nv4_dRate因子
						, ScoreNotHouseLoan.column.nv4_loanBalSByid分數
						, ScoreNotHouseLoan.column.nv4_MaxR01分數
						, ScoreNotHouseLoan.column.nv4_P01分數
						, ScoreNotHouseLoan.column.nv4_學歷分數};
			}
		}else if(Util.equals("R", tbl)){
			if (Util.equals(V3_0_CARD_LOAN, varVer) || Util.equals(V3_1_CARD_LOAN, varVer)) {
				return new String[]{ ScoreCardLoan.column.個人年所得
						, ScoreCardLoan.column.個人負債比率
						, ScoreCardLoan.column.D07因子
						, ScoreCardLoan.column.N06因子 , ScoreCardLoan.column.職稱
						, ScoreCardLoan.column.P25因子
						, ScoreCardLoan.column.P69因子
						, ScoreCardLoan.column.P19因子
						, ScoreCardLoan.column.R01因子 };
			}else if(Util.equals(V4_0_CARD_LOAN, varVer)){
				return new String[]{ ScoreNotHouseLoan.column.nv4_dRate因子
						, ScoreNotHouseLoan.column.nv4_loanBalSByid分數
						, ScoreNotHouseLoan.column.nv4_MaxR01分數
						, ScoreNotHouseLoan.column.nv4_P01分數
						, ScoreNotHouseLoan.column.nv4_學歷分數};
			}
		}
		return new String[]{ "" };		
	}
	
	public static Map<String, String[]> get_Subject_Prefix_Map(){
		Map<String, String[]> subjectPrefixMap = new HashMap<String, String[]>();
		subjectPrefixMap.put("02", new String[]{"1", "2"});
		subjectPrefixMap.put("03", new String[]{"3", "4", "5", "6"});
		subjectPrefixMap.put("68", new String[]{"3", "4"});
		return subjectPrefixMap;
	}
	
	public static Map<String, String[]> get_Sbject_ErrMsg_Map(){
		Map<String, String[]> subjectErrMsgMap = new HashMap<String, String[]>();
		subjectErrMsgMap.put("02", new String[]{"短期"});
		subjectErrMsgMap.put("03", new String[]{"中期", "長期"});
		subjectErrMsgMap.put("68", new String[]{"中期"});
		return subjectErrMsgMap;
	}
	
}