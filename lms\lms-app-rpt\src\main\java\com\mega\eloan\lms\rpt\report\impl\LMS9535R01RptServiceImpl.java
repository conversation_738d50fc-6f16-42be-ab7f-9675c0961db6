package com.mega.eloan.lms.rpt.report.impl;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.TreeMap;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataAccessResourceFailureException;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.inet.report.ReportException;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.CodeTypeEnum;
import com.mega.eloan.common.formatter.CodeTypeFormatter;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.base.common.JSONUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.constants.UtilConstants.Casedoc;
import com.mega.eloan.lms.base.pages.LMSS08APage;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dao.L120S01BDao;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.lms.pages.LMS1205S05Page06a;
import com.mega.eloan.lms.lms.service.LMS1205Service;
import com.mega.eloan.lms.lms.service.LMS1405Service;
import com.mega.eloan.lms.lms.service.LMS1415Service;
import com.mega.eloan.lms.lns.service.LMS1201Service;
import com.mega.eloan.lms.mfaloan.service.MisGrpcmpService;
import com.mega.eloan.lms.model.C140JSON;
import com.mega.eloan.lms.model.C140M01A;
import com.mega.eloan.lms.model.C140M04A;
import com.mega.eloan.lms.model.C140S04A;
import com.mega.eloan.lms.model.C140S04B;
import com.mega.eloan.lms.model.C140S04C;
import com.mega.eloan.lms.model.C140S07A;
import com.mega.eloan.lms.model.C140S09A;
import com.mega.eloan.lms.model.C140SDSC;
import com.mega.eloan.lms.model.C140SFFF;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01D;
import com.mega.eloan.lms.model.L120S01A;
import com.mega.eloan.lms.model.L120S01B;
import com.mega.eloan.lms.model.L120S01C;
import com.mega.eloan.lms.model.L120S04A;
import com.mega.eloan.lms.model.L120S04B;
import com.mega.eloan.lms.model.L120S04C;
import com.mega.eloan.lms.model.L120S04E;
import com.mega.eloan.lms.model.L120S05A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.formatter.NumericFormatter;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapMath;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.PdfTools;
import tw.com.jcs.common.report.ReportGenerator;

/**
 * <pre>
 * 管理報表關係戶往來彙總產報表
 * </pre>
 * 
 * @since 2013/1/2
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/1/2,Miller,new
 *          </ul>
 */
@Service("lms9535r01rptservice")
public class LMS9535R01RptServiceImpl implements FileDownloadService {

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(LMS9535R01RptServiceImpl.class);
	@Resource
	LMS1205Service service1205;
	@Resource
	LMS1415Service service1415;
	@Resource
	LMS1405Service service1405;
	@Resource
	UserInfoService userInfoService;
	@Resource
	EloandbBASEService eloandbBaseService;

	@Resource
	BranchService branch;

	@Resource
	CodeTypeService codetypeservice;

	@Resource
	LMSService lmsService;

	@Resource
	LMS1201Service service1201;

	@Resource
	DwdbBASEService dwdbService;

	@Resource
	L120S01BDao l120s01bDao;

	@Resource
	MisGrpcmpService misGrpcmpService;

	// 以下為徵信沿用部份
	/** 總頁數欄位名稱 */
	static final String TotalPageNum = "_totalPage";
	/** C140M01A欄位名稱 */
	static final String C140M01A = "C140M01A";
	static final String C120M01A = "C120M01A";
	static final String M131M01A = "M131M01A";
	/** 目錄欄位名稱 */
	static final String ReportChapterList = "_reportChapterList";
	/** 目前頁次 */
	static final String PageNum = "PageNum";

	static final String PageText = "_pageText";

	static final String printMainOid = "printMainOid";

	static final int Ch3_Rd1_MaxNum = 10;
	static final int Ch9_Rd3_MaxNum = 15;
	static final int Ch9_Rd4_MaxNum = 20;
	static final int Ch9_Rd5_MaxNum = 5;

	static final String space = "";
	static final String spacebox = "□";
	static final String nspacebox = "■";

	/**
	 * 建立PDF
	 * 
	 * @param params
	 *            params
	 * @return OutputStream OutputStream
	 * @throws Exception
	 * @throws IOException
	 * @throws FileNotFoundException
	 */
	@SuppressWarnings("unused")
	public OutputStream generateReport(PageParameters params)
			throws FileNotFoundException, IOException, Exception {
		Properties prop = null;
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String rptOid = Util.nullToSpace(params.getString("rptOid"));

		Map<InputStream, Integer> pdfNameMap = new LinkedHashMap<InputStream, Integer>();
		// 額度明細表獨立頁碼
		Map<InputStream, Integer> pdfNameMap2 = new LinkedHashMap<InputStream, Integer>();
		// 額度披覆表獨立頁碼
		Map<InputStream, Integer> pdfNameMap3 = new LinkedHashMap<InputStream, Integer>();
		// 獨立頁碼
		Map<InputStream, Integer> pdfNameMap4 = new LinkedHashMap<InputStream, Integer>();
		// 獨立頁碼
		Map<InputStream, Integer> pdfNameMap5 = new LinkedHashMap<InputStream, Integer>();
		List<InputStream> list = new LinkedList<InputStream>();
		String[] dataSplit = rptOid.split("\\|");
		OutputStream outputStream = null;
		Locale locale = null;
		String type = null;
		String oid = null;
		String cntrNo = null;
		String custId = null;
		String dupNo = null;
		String refMainId = null;
		String inType = null;
		String inCustId = null;
		String inDupNo = null;
		int subLine = 8;
		Properties propEloanPage = null;
		boolean setTop = true;
		Map<String, String> l140m01aCustIdData = new LinkedHashMap<String, String>();
		try {
			propEloanPage = MessageBundleScriptCreator
					.getComponentResource(AbstractEloanPage.class);
			locale = LMSUtil.getLocale();
			// locale = java.util.Locale.US;
			// mainId = "";

			prop = MessageBundleScriptCreator
					.getComponentResource(LMS9535R01RptServiceImpl.class);
			int nowCount = 0;
			boolean nowResult = false;
			for (String temp : dataSplit) {
				outputStream = null;
				type = null;
				oid = null;
				cntrNo = null;
				custId = null;
				dupNo = null;
				String[] tempSplits = temp.split("\\^");
				if (tempSplits.length < 1) {
					type = "";
				} else {
					type = tempSplits[0];
				}
				if (tempSplits.length < 2) {
					oid = "";
				} else {
					oid = tempSplits[1];
				}
				if (tempSplits.length < 3) {
					custId = "";
				} else {
					custId = tempSplits[2];
				}
				if (tempSplits.length < 4) {
					dupNo = "";
				} else {
					dupNo = tempSplits[3];
				}
				if (tempSplits.length < 5) {
					cntrNo = "";
				} else {
					cntrNo = tempSplits[4];
				}
				if (tempSplits.length < 6) {
					refMainId = "";
				} else {
					refMainId = tempSplits[5];
				}

				if ("R14".equals(type)) {
					outputStream = this.genLMS1205R14(params, mainId, locale,
							prop);
					subLine = 7;
					pdfNameMap4.put(
							new ByteArrayInputStream(
									((ByteArrayOutputStream) outputStream)
											.toByteArray()), subLine);
				} else if ("R24".equals(type)) {
					outputStream = this.genLMS1205R24(params, mainId, locale,
							"report/lns/LMS1201R24_" + locale.toString()
									+ ".rpt", prop);
					// 列印頁次位置
					subLine = 1;
					pdfNameMap4.put(
							new ByteArrayInputStream(
									((ByteArrayOutputStream) outputStream)
											.toByteArray()), subLine);
				} else if ("RORWA".equals(type)) { //J-113-0183 新增RORWA計算
					outputStream = this.genLMS1205R48_RORWA(params, locale,
							prop);
					subLine = 8;
					pdfNameMap5.put(
							new ByteArrayInputStream(
									((ByteArrayOutputStream) outputStream)
											.toByteArray()), subLine);
				}
				nowCount++;
			}
			if (pdfNameMap != null && pdfNameMap.size() > 0) {
				outputStream = new ByteArrayOutputStream();
				PdfTools.mergeReWritePagePdf(pdfNameMap, outputStream,
						propEloanPage.getProperty("PaginationText"), true,
						locale, subLine);
				list.add(new ByteArrayInputStream(
						((ByteArrayOutputStream) outputStream).toByteArray()));
			}
			if (pdfNameMap2 != null && pdfNameMap2.size() > 0) {
				outputStream = new ByteArrayOutputStream();
				PdfTools.mergeReWritePagePdf(pdfNameMap2, outputStream,
						propEloanPage.getProperty("PaginationText"), true,
						locale, subLine);
				list.add(new ByteArrayInputStream(
						((ByteArrayOutputStream) outputStream).toByteArray()));
			}
			if (pdfNameMap3 != null && pdfNameMap3.size() > 0) {
				outputStream = new ByteArrayOutputStream();
				PdfTools.mergeReWritePagePdf(pdfNameMap3, outputStream,
						propEloanPage.getProperty("PaginationText"), true,
						locale, subLine);
				list.add(new ByteArrayInputStream(
						((ByteArrayOutputStream) outputStream).toByteArray()));
			}
			if (pdfNameMap4 != null && pdfNameMap4.size() > 0) {
				for (InputStream inputStream : pdfNameMap4.keySet()) {
					Map<InputStream, Integer> tempMap = new LinkedHashMap<InputStream, Integer>();
					tempMap.put(inputStream, pdfNameMap4.get(inputStream));
					outputStream = new ByteArrayOutputStream();
					PdfTools.mergeReWritePagePdf(tempMap, outputStream,
							propEloanPage.getProperty("PaginationText"), true,
							locale, subLine);
					list.add(new ByteArrayInputStream(
							((ByteArrayOutputStream) outputStream)
									.toByteArray()));
				}

			}
			if (pdfNameMap5 != null && pdfNameMap5.size() > 0) {
				for (InputStream inputStream : pdfNameMap5.keySet()) {
					Map<InputStream, Integer> tempMap = new LinkedHashMap<InputStream, Integer>();
					tempMap.put(inputStream, pdfNameMap5.get(inputStream));
					outputStream = new ByteArrayOutputStream();
					PdfTools.mergeReWritePagePdf(pdfNameMap5,
							outputStream,
							propEloanPage.getProperty("PaginationText"), true,
							locale, subLine, false);
					list.add(new ByteArrayInputStream(
							((ByteArrayOutputStream) outputStream)
									.toByteArray()));
				}

			}
			outputStream = new ByteArrayOutputStream();
			PdfTools.mergeReWritePagePdf(list, outputStream);
		} finally {
			if (list != null) {
				list.clear();
			}
			if (pdfNameMap != null) {
				pdfNameMap.clear();
			}
			if (pdfNameMap2 != null) {
				pdfNameMap2.clear();
			}
			if (pdfNameMap3 != null) {
				pdfNameMap3.clear();
			}
			if (pdfNameMap4 != null) {
				pdfNameMap4.clear();
			}
		}
		return outputStream;
	}

	/*
	 * (non-Javadoc) 呈現在頁面用的
	 * 
	 * @see
	 * com.mega.eloan.lms.base.service.FileDownloadService#getContent(
	 */
	@Override
	public byte[] getContent(PageParameters params)
			throws FileNotFoundException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
			baos = (ByteArrayOutputStream) this.generateReport(params);
			return baos.toByteArray();
		} finally {
			if (baos != null) {
				baos.close();
			}

		}
	}

	/**
	 * 產生LMS1205R14的PDF
	 * 
	 * @param mainId
	 *            mainId
	 * @param locale
	 *            語系
	 * @param prop
	 *            prop
	 * @return OutputStream OutputStream
	 * @throws Exception
	 * @throws IOException
	 * @throws FileNotFoundException
	 */
	public OutputStream genLMS1205R14(PageParameters params, String mainId,
			Locale locale, Properties prop) throws FileNotFoundException,
			IOException, Exception {
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String custName = Util.trim(params.getString("custName"));
		String chairmanId = Util.trim(params.getString("chairmanId"));
		String chairmanDupNo = Util.trim(params.getString("chairmanDupNo"));
		String chairman = Util.trim(params.getString("chairman"));
		String groupNo = Util.trim(params.getString("groupNo"));
		String groupName = Util.trim(params.getString("groupName"));
		
		// J-112-0307
		// 於eloan貸後管理追蹤檢核表維護功能中，新增一鍵生成公司訪問紀錄表及借戶、負責人、所屬企業集團等關係戶於本行各項業務往來彙總表。
		String fromPostLoan = Util.trim(params.getString("fromPostLoan"));
		String unitMgrName_S01D = Util.trim(params.getString("unitMgrName_S01D"));
		String accountMgrName_S01D = Util.trim(params.getString("accountMgrName_S01D"));

		// J-104-0240-001 Web e-Loan授信簽報書與額度明細表增加列示借款戶所屬集團企業代號、名稱與註記。
		String groupBadFlag = Util.trim(params.getString("groupBadFlag"));
		String groupBadFlagText = Util.trim(params
				.getString("groupBadFlagText"));

		List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		ReportGenerator generator = new ReportGenerator(
				"report/lns/LMS1201R14_" + locale.toString() + ".rpt");
		OutputStream outputStream = null;

		List<L120S04A> l120s04aList = null;
		L120M01A l120m01a = null;
		List<L120S01B> l120s01bList = null;
		L120S05A l120s05a = null;
		// List<L120S04B> l120s04bList = null;
		Map<String, String> yesNoMap = null;
		Map<String, String> caseLvlMap = null;
		Map<String, String> typCdMap = null;
		try {
			yesNoMap = codetypeservice.findByCodeType("Common_YesNo",
					locale.toString());
			caseLvlMap = codetypeservice.findByCodeType("lms1205m01_caseLvl",
					locale.toString());
			typCdMap = codetypeservice.findByCodeType("TypCd",
					locale.toString());
			if (caseLvlMap == null)
				caseLvlMap = new LinkedHashMap<String, String>();
			if (typCdMap == null)
				typCdMap = new LinkedHashMap<String, String>();
			if (yesNoMap == null) {
				yesNoMap = new LinkedHashMap<String, String>();
			}
			l120s04aList = service1205.findL120s04aByMainIdPrtFlag(mainId, "1");

			if (l120s04aList != null && !l120s04aList.isEmpty()) {

				Collections.sort(l120s04aList, new Comparator<L120S04A>() {

					@Override
					public int compare(L120S04A object1, L120S04A object2) {
						// TODO Auto-generated method stub
						int cr = 0;
						String[] resStr1 = Util.trim(object1.getCustRelation())
								.split(",");
						Arrays.sort(resStr1);
						String[] resStr2 = Util.trim(object2.getCustRelation())
								.split(",");
						Arrays.sort(resStr2);

						int a = resStr2[0].compareTo(resStr1[0]);

						String prtFlag1 = object1.getPrtFlag();
						String prtFlag2 = object2.getPrtFlag();
						int prtFlag = prtFlag2.compareTo(prtFlag1);

						if (prtFlag != 0) {
							cr = (prtFlag > 0) ? -1 : 5;
						} else if (a != 0) {
							cr = (a > 0) ? -2 : 4;
						} else {
							long b = (object2.getProfit() == null ? 0 : object2
									.getProfit())
									- (object1.getProfit() == null ? 0
											: object1.getProfit());
							if (b != 0) {
								cr = (b > 0) ? 3 : -3;
							} else {
								int c = object2.getCustId().compareTo(
										object1.getCustId());
								if (c != 0) {
									cr = (c > 0) ? -4 : 2;
								} else {
									// String oid1 = object1.getOid();
									// String oid2 = object2.getOid();
									// int oidFlag = oid2.compareTo(oid2);
									// if(oidFlag != 0){
									// cr = (oidFlag > 0)? -5:1;
									// }
								}
							}
						}

						return cr;
					}
				});
			}

			// l120s06bList = service1205.findL120s04bByMainId(mainId);
			l120m01a = service1205.findL120m01aByMainId(mainId);
			if (l120m01a == null) {
				l120m01a = new L120M01A();
			}
			l120s01bList = service1205.findL120s01bByMainId(mainId);
			l120s05a = service1205.findL120s05aByMainId(mainId);

			titleRows = this.setL120S04AListData(l120s04aList, titleRows, prop,
					yesNoMap);

			rptVariableMap = this.setL120S04AData(rptVariableMap, l120s04aList);

			rptVariableMap = this.setL120M01AData(rptVariableMap, l120m01a,
					typCdMap, caseLvlMap, prop);
			rptVariableMap = this.setL120S01B2Data(rptVariableMap,
					l120s01bList, l120m01a);

			if (UtilConstants.Casedoc.DocKind.授權外.equals(Util.trim(l120m01a
					.getDocKind()))
					&& UtilConstants.Casedoc.DocCode.一般.equals(Util
							.trim(l120m01a.getDocCode()))) {
				if (Util.isEmpty(rptVariableMap.get("L120S01B.GROUPNO"))) {
					rptVariableMap
							.put("L120S01B.GROUPNO", ("N.A.".equals(Util
									.nullToSpace(l120s05a.getGrpNo())) ? "N.A."
									: Util.nullToSpace(l120s05a.getGrpNo())));
				}
				if (Util.isEmpty(rptVariableMap.get("L120S01B.GROUPNAME"))) {
					rptVariableMap
							.put("L120S01B.GROUPNAME",
									Util.nullToSpace(l120s05a.getGrpNo())
											+ ("N.A.".equals(Util
													.nullToSpace(l120s05a
															.getGrpName())) ? "N.A."
													: Util.nullToSpace(l120s05a
															.getGrpName())));
				}
			}

			rptVariableMap.put("L120M01A.CUSTNAME", custName);
			rptVariableMap.put("L120M01A.CUSTID", custId);
			rptVariableMap.put("L120M01A.DUPNO", dupNo);
			rptVariableMap.put("L120S01B.CHAIRMAN", chairman);
			rptVariableMap.put("L120S01B.CHAIRMANID", chairmanId);
			rptVariableMap.put("L120S01B.CHAIRMANDUPNO", chairmanDupNo);
			// rptVariableMap.put("L120S01B.GROUPNAME", groupName);
			rptVariableMap.put("L120S01B.GROUPNO", groupNo);

			// J-104-0240-001 Web e-Loan授信簽報書與額度明細表增加列示借款戶所屬集團企業代號、名稱與註記。
			if (Util.notEquals(groupBadFlag, "0")
					&& Util.notEquals(groupBadFlag, "")) {
				rptVariableMap.put("L120S01B.GROUPNAME", groupName + "("
						+ groupBadFlagText + ")");
			} else {
				rptVariableMap.put("L120S01B.GROUPNAME", groupName);
			}

			// J-112-0307
			// 於eloan貸後管理追蹤檢核表維護功能中，新增一鍵生成公司訪問紀錄表及借戶、負責人、所屬企業集團等關係戶於本行各項業務往來彙總表。
			if(Util.equals(fromPostLoan,"Y")){
				rptVariableMap.put("L260M01D.FROMPOSTLOAN", fromPostLoan);
				rptVariableMap.put("UNITMGR_S01D", unitMgrName_S01D);
				rptVariableMap.put("ACCOUNTMGR_S01D", accountMgrName_S01D);
			}
			
			// generator.setLang(java.util.Locale.TAIWAN);
			generator.setLang(locale);
			generator.setVariableData(rptVariableMap);
			generator.setRowsData(titleRows);
			// generator.setReportFile("report/lms/LMS1205R01_" +
			// locale.toString() + ".rpt");

			// generator.setTestMethod(true);
			// generator.checkVariableExist("C:/test.txt", rptVariableMap);
			outputStream = generator.generateReport();
		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
			if (caseLvlMap != null) {
				caseLvlMap.clear();
			}
			if (typCdMap != null) {
				typCdMap.clear();
			}
		}

		return outputStream;
	}
	
	/**
	 * J-113-0183  產生LMS1205R48_RORWA的PDF
	 * 
	 * @param locale
	 *            語系
	 * @param prop
	 *            prop
	 * @return OutputStream OutputStream
	 * @throws Exception
	 * @throws IOException
	 * @throws FileNotFoundException
	 */
	public OutputStream genLMS1205R48_RORWA(PageParameters params, Locale locale, Properties prop) 
			throws FileNotFoundException, IOException, Exception {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String branchNo = user.getUnitNo();
		String BranchName = branch.getBranchName(Util.nullToSpace(user.getUnitNo()));
		
		String printDate = CapDate.formatDate(CapDate.getCurrentTimestamp(), UtilConstants.DateFormat.YYYY_MM_DD);
		String custId = Util.trim(params.getString("custId"));
		String custName = Util.trim(params.getString("custName"));
		String branch = branchNo + BranchName;
		String bisApplyCurr = Util.trim(params.getString("bisApplyCurr"));
		String newApplyAmt = Util.trim(params.getString("newApplyAmt"));
		String bisIncomeRate = Util.trim(params.getString("bisIncomeRate"));
		String bisFtpRate = Util.trim(params.getString("bisFtpRate"));
		String bisBankWorkCost = Util.trim(params.getString("bisBankWorkCost"));
		String bisRiskCost = Util.trim(params.getString("bisRiskCost"));
		String bisEstimatedReturn = Util.trim(params.getString("bisEstimatedReturn"));
		String bisEstimatedReturn_1 = Util.trim(params.getString("bisEstimatedReturn_1"));
		String bisRiskAdjReturn = Util.trim(params.getString("bisRiskAdjReturn"));
		String bisRiskAdjReturn_1 = Util.trim(params.getString("bisRiskAdjReturn_1"));
		String bisRItemD_standard = Util.trim(params.getString("bisRItemD_standard"));
		String bisRItemD_IRB = Util.trim(params.getString("bisRItemD_IRB"));
		String bisRorwa_standard = Util.trim(params.getString("bisRorwa_standard"));
		String bisRorwa_1_standard = Util.trim(params.getString("bisRorwa_1_standard"));
		String bisRorwa_IRB = Util.trim(params.getString("bisRorwa_IRB"));
		String bisRorwa_1_IRB = Util.trim(params.getString("bisRorwa_1_IRB"));
		
		

		List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		ReportGenerator generator = new ReportGenerator(
				"report/lms/LMS1205R48_RORWA_" + locale.toString() + ".rpt");
		OutputStream outputStream = null;
		
		if(!newApplyAmt.isEmpty()){
			//仟元轉成億元
			BigDecimal newApplyAmtVal = new BigDecimal(newApplyAmt).divide(new BigDecimal(100000));
			newApplyAmt = newApplyAmtVal.stripTrailingZeros().toPlainString();
		}
		rptVariableMap.put("RORWA.printDate", printDate);
		rptVariableMap.put("RORWA.custId", custId);
		rptVariableMap.put("RORWA.custName", custName);
		rptVariableMap.put("RORWA.branch", branch);
		rptVariableMap.put("RORWA.bisApplyCurr", bisApplyCurr);
		rptVariableMap.put("RORWA.newApplyAmt", newApplyAmt);
		rptVariableMap.put("RORWA.bisIncomeRate", bisIncomeRate);
		rptVariableMap.put("RORWA.bisFtpRate", bisFtpRate);
		rptVariableMap.put("RORWA.bisBankWorkCost", bisBankWorkCost);
		rptVariableMap.put("RORWA.bisRiskCost", bisRiskCost);
		rptVariableMap.put("RORWA.bisEstimatedReturn", bisEstimatedReturn);
		rptVariableMap.put("RORWA.bisEstimatedReturn_1", bisEstimatedReturn_1);
		rptVariableMap.put("RORWA.bisRiskAdjReturn", bisRiskAdjReturn);
		rptVariableMap.put("RORWA.bisRiskAdjReturn_1", bisRiskAdjReturn_1);
		rptVariableMap.put("RORWA.bisRItemD_standard", bisRItemD_standard);
		rptVariableMap.put("RORWA.bisRorwa_standard", bisRorwa_standard);
		rptVariableMap.put("RORWA.bisRorwa_1_standard", bisRorwa_1_standard);
		rptVariableMap.put("RORWA.bisRItemD_IRB", bisRItemD_IRB);
		rptVariableMap.put("RORWA.bisRorwa_IRB", bisRorwa_IRB);
		rptVariableMap.put("RORWA.bisRorwa_1_IRB", bisRorwa_1_IRB);
		try {
			generator.setLang(locale);
			generator.setVariableData(rptVariableMap);
			generator.setRowsData(titleRows);
			outputStream = generator.generateReport();
		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
		}

		return outputStream;
	}

	/**
	 * 產生LMS1205R24的PDF
	 * 
	 * @param mainId
	 * @param locale
	 * @param path
	 * @param prop
	 * @return
	 * @throws FileNotFoundException
	 * @throws ReportException
	 * @throws IOException
	 * @throws Exception
	 */
	public OutputStream genLMS1205R24(PageParameters params, String mainId,
			Locale locale, String path, Properties prop)
			throws FileNotFoundException, ReportException, IOException,
			Exception {
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String custName = Util.trim(params.getString("custName"));
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();
		ReportGenerator generator = new ReportGenerator(path);
		OutputStream outputStream = null;
		List<L120S04B> listL120s04b = service1205.findL120s04bByMainId(mainId);
		List<L120S04C> listL120s04c = service1205.findL120s04cByMainId(mainId);
		// J-112-0078 配合企金處，修改「借戶暨關係戶與本行往來實績彙總表」中，增列各業務別利潤貢獻度欄位等。
		List<L120S04E> listL120s04e = service1201.findL120s04eByMainId(mainId);
		// L120M01A meta = service1205.findL120m01aByMainId(mainId);
		StringBuilder sbcustAll = new StringBuilder();
		// 借款人統編及重覆序號
		sbcustAll.append(custId).append(Util.trim(dupNo));
		// 借款人與本行往來實績彙總表list
		List<L120S04C> listKind1 = new ArrayList<L120S04C>();
		// 借款人與本行往來實績彙總表資料年月(起)list
		List<String> dateKind1 = new ArrayList<String>();
		// 借款人暨關係戶與本行往來實績彙總表list
		List<L120S04C> listKind2 = new ArrayList<L120S04C>();
		// 借款人暨關係戶與本行往來實績彙總表資料年月(起)list
		List<String> dateKind2 = new ArrayList<String>();
		// J-112-0078 配合企金處，修改「借戶暨關係戶與本行往來實績彙總表」中，增列各業務別利潤貢獻度欄位等。
		List<L120S04E> listL120s04e1 = new ArrayList<L120S04E>();
		List<String> s04eDate1 = new ArrayList<String>();

		if (!listL120s04c.isEmpty()) {
			for (L120S04C l120s04c : listL120s04c) {
				if ("1".equals(Util.trim(l120s04c.getDocKind()))) {
					// 借款人與本行往來實績彙總表
					listKind1.add(l120s04c);
					String docDate = Util.trim(l120s04c.getDocDate());
					if (Util.isNotEmpty(docDate)) {
						dateKind1.add(docDate);
					}
				} else if ("2".equals(Util.trim(l120s04c.getDocKind()))) {
					// 借款人暨關係戶與本行往來實績彙總表
					listKind2.add(l120s04c);
					String docDate = Util.trim(l120s04c.getDocDate());
					if (docDate != null) {
						dateKind2.add(docDate);
					}
				}
			}
		}
		if (!listL120s04e.isEmpty()) {
			for (L120S04E l120s04e : listL120s04e) {
				// 跟 LMS1201R01RptServiceImpl.java 不同，因為撈資料的時候沒篩docKind
				if (Util.notEquals(Util.nullToSpace(l120s04e.getDocKind()), "1")) {
					continue;
				}
				listL120s04e1.add(l120s04e);
				String docDate = Util.trim(l120s04e.getDocDate());
				if (Util.isNotEmpty(docDate)) {
					s04eDate1.add(docDate);
				}
			}
		}
		// 進行排序
		Collections.sort(dateKind1);
		Collections.sort(dateKind2);
		Collections.sort(s04eDate1);
		L120S04B l120s04b = null;
		if (!listL120s04b.isEmpty()) {
			for (L120S04B model : listL120s04b) {
				l120s04b = model;
				break;
			}
		}
		Map<String, String> typCdMap = null;
		Map<String, String> caseLvlMap = null;
		try {
			// 初始化Map
			rptVariableMap = this.initL120S04BData(rptVariableMap);
			// 關係戶於本行往來實績彙總表主檔
			rptVariableMap.putAll(this
					.setL120S04BData(rptVariableMap, l120s04b));

			// 關係戶於本行往來實績彙總表明細檔
			rptVariableMap.putAll(setL120S04CData(rptVariableMap, listKind1,
					dateKind1, "1"));
			rptVariableMap.putAll(setL120S04CData(rptVariableMap, listKind2,
					dateKind2, "2"));

			rptVariableMap.putAll(setL120S04EData(rptVariableMap, listL120s04e,
					s04eDate1, "1"));

			rptVariableMap.put("L120M01A.CUSTID", sbcustAll.toString());
			rptVariableMap.put("L120M01A.CUSTNAME", custName);

			// J-107-0087-001 Web
			// e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。
			boolean newGrpGrade = lmsService.isNewGrpGrade(
					Util.trim(l120s04b.getGrpYear()), true);
			String defultNoGrade = lmsService.getGrpNoGrade(newGrpGrade);

			if (newGrpGrade) {
				// J-107-0007-001 Web
				// e-Loan國內、海外授信簽報書第八章新增相同集團企業評等等級之新臺幣及美元放款利率資訊
				rptVariableMap.putAll(genGrpRateDataHtml_2017(l120s04b, custId,
						dupNo, rptVariableMap));
			} else {
				// J-107-0007-001 Web
				// e-Loan國內、海外授信簽報書第八章新增相同集團企業評等等級之新臺幣及美元放款利率資訊
				rptVariableMap.putAll(genGrpRateDataHtml(l120s04b, custId,
						dupNo, rptVariableMap));
			}

			generator.setLang(locale);
			generator.setVariableData(rptVariableMap);
			generator.setRowsData(titleRows);
			// generator.setReportFile("report/lms/LMS1205R01_" +
			// locale.toString() + ".rpt");
			// generator.setTestMethod(true);
			// generator.checkVariableExist("C:/test.txt", rptVariableMap);
			outputStream = generator.generateReport();
		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
			if (titleRows != null) {
				titleRows.clear();
			}
			if (caseLvlMap != null) {
				caseLvlMap.clear();
			}
			if (typCdMap != null) {
				typCdMap.clear();
			}
		}

		return outputStream;
	}

	/**
	 * 初始化R24 rptVariableMap
	 * 
	 * @param rptVariableMap
	 * @param l120s04b
	 * @param typCdMap
	 * @param caseLvlMap
	 * @param prop
	 * @return
	 */
	private Map<String, String> initL120S04BData(
			Map<String, String> rptVariableMap) {
		String[] mapKeys = new String[] { "L120S04B.GRPNO", "L120S04B.GRPNAME",
				"L120S04B.GRPYEAR", "L120S04B.GRPGRRD",
				"L120S04B.MAINGRPDATES", "L120S04B.MAINGRPDATEE",
				"L120S04B.MAINGRPAVGRATE", "L120S04B.DEPOSITDATES",
				"L120S04B.DEPOSITDATEE", "L120S04B.MEGAAVGAMT",
				"L120S04B.DEMANDAMT", "L120S04B.DEMANDAVGRATE",
				"L120S04C.DOCDATES1", "L120S04C.DOCDATES2",
				"L120S04C.DOCDATES3", "L120S04C.DOCDATES4",
				"L120S04C.DOCDATES5", "L120S04C.DOCDATES6",
				"L120S04C.AVGDEPOSITAMT1", "L120S04C.AVGDEPOSITAMT2",
				"L120S04C.AVGDEPOSITAMT3", "L120S04C.AVGDEPOSITAMT4",
				"L120S04C.AVGDEPOSITAMT5", "L120S04C.AVGDEPOSITAMT6",
				"L120S04C.AVGLOANAMT1", "L120S04C.AVGLOANAMT2",
				"L120S04C.AVGLOANAMT3", "L120S04C.AVGLOANAMT4",
				"L120S04C.AVGLOANAMT5", "L120S04C.AVGLOANAMT6",
				"L120S04C.RCVBUYAVGAMT1", "L120S04C.RCVBUYAVGAMT2",
				"L120S04C.RCVBUYAVGAMT3", "L120S04C.RCVBUYAVGAMT4",
				"L120S04C.RCVBUYAVGAMT5", "L120S04C.RCVBUYAVGAMT6",
				"L120S04C.RCVSELLAVGAMT1", "L120S04C.RCVSELLAVGAMT2",
				"L120S04C.RCVSELLAVGAMT3", "L120S04C.RCVSELLAVGAMT4",
				"L120S04C.RCVSELLAVGAMT5", "L120S04C.RCVSELLAVGAMT6",
				"L120S04C.EXPORTAMT1", "L120S04C.EXPORTAMT2",
				"L120S04C.EXPORTAMT3", "L120S04C.EXPORTAMT4",
				"L120S04C.EXPORTAMT5", "L120S04C.EXPORTAMT6",
				"L120S04C.IMPORTAMT1", "L120S04C.IMPORTAMT2",
				"L120S04C.IMPORTAMT3", "L120S04C.IMPORTAMT4",
				"L120S04C.IMPORTAMT5", "L120S04C.IMPORTAMT6",
				"L120S04C.PROFITAMT1", "L120S04C.PROFITAMT2",
				"L120S04C.PROFITAMT3", "L120S04C.PROFITAMT4",
				"L120S04C.PROFITAMT5", "L120S04C.PROFITAMT6",
				"L120S04C.PROFITRATE1", "L120S04C.PROFITRATE2",
				"L120S04C.PROFITRATE3", "L120S04C.PROFITRATE4",
				"L120S04C.PROFITRATE5", "L120S04C.PROFITRATE6",
				"L120S04B.MAINGRPDATEMS", "L120S04B.MAINGRPDATEME",
				"L120S04B.MAINGRPAVGRATEM", "L120S04B.MAINGRPDATERS",
				"L120S04B.MAINGRPDATERE", "L120S04B.MAINGRPAVGRATER",
				"L120S04B.SALARYACCT", "L120S04B.SALARYCARDACCT",
				"L120S04B.SALARYCARDDATES", "L120S04B.SALARYCARDDATEE",
				"L120S04B.SALARYCARDPCAMT" };
		for (String mapKey : mapKeys) {
			rptVariableMap.put(mapKey, "");
		}
		return rptVariableMap;
	}

	/**
	 * 設定L120S04B Model
	 * 
	 * @param rptVariableMap
	 * @param l120s04b
	 * @param typCdMap
	 * @param caseLvlMap
	 * @param prop
	 * @return
	 */
	private Map<String, String> setL120S04BData(
			Map<String, String> rptVariableMap, L120S04B l120s04b) {
		if (l120s04b == null) {
			l120s04b = new L120S04B();
		}
		rptVariableMap.put("L120S04B.GRPNO",
				Util.nullToSpace(l120s04b.getGrpNo()));
		rptVariableMap.put("L120S04B.GRPYEAR",
				Util.nullToSpace(l120s04b.getGrpYear()));
		rptVariableMap.put("L120S04B.GRPGRRD",
				Util.nullToSpace(l120s04b.getGrpGrrd()));
		rptVariableMap.put("L120S04B.MAINGRPDATES",
				Util.trim(l120s04b.getMainGrpDateS()));
		rptVariableMap.put("L120S04B.MAINGRPDATEE",
				Util.trim(l120s04b.getMainGrpDateE()));
		// rptVariableMap.put("L120S04B.MAINGRPAVGRATE",
		// Util.nullToSpace(l120s04b.getMainGrpAvgRate()));

		rptVariableMap.put(
				"L120S04B.MAINGRPAVGRATE",
				(Util.isEmpty(l120s04b.getMainGrpAvgRate())) ? "N.A." : Util
						.nullToSpace(l120s04b.getMainGrpAvgRate()));

		rptVariableMap.put("L120S04B.DEPOSITDATES",
				Util.trim(l120s04b.getDepositDateS()));
		rptVariableMap.put("L120S04B.DEPOSITDATEE",
				Util.trim(l120s04b.getDepositDateE()));
		rptVariableMap.put("L120S04B.MEGAAVGAMT",
				NumConverter.addComma(l120s04b.getMegaAvgAmt()));
		rptVariableMap.put("L120S04B.DEMANDAMT",
				NumConverter.addComma(l120s04b.getDemandAmt()));
		rptVariableMap.put(
				"L120S04B.DEMANDAVGRATE",
				(Util.isEmpty(l120s04b.getDemandAvgRate())) ? "N.A." : Util
						.nullToSpace(l120s04b.getDemandAvgRate()));

		rptVariableMap.put("L120S04B.MAINGRPDATEMS",
				Util.trim(l120s04b.getMainGrpDateMS()));
		rptVariableMap.put("L120S04B.MAINGRPDATEME",
				Util.trim(l120s04b.getMainGrpDateME()));
		// rptVariableMap.put("L120S04B.MAINGRPAVGRATEM",
		// Util.nullToSpace(l120s04b.getMainGrpAvgRateM()));

		rptVariableMap.put(
				"L120S04B.MAINGRPAVGRATEM",
				(Util.isEmpty(l120s04b.getMainGrpAvgRateM())) ? "N.A." : Util
						.nullToSpace(l120s04b.getMainGrpAvgRateM()));
		rptVariableMap.put("L120S04B.MAINGRPDATERS",
				Util.trim(l120s04b.getMainGrpDateRS()));
		rptVariableMap.put("L120S04B.MAINGRPDATERE",
				Util.trim(l120s04b.getMainGrpDateRE()));
		rptVariableMap.put(
				"L120S04B.MAINGRPAVGRATER",
				(Util.isEmpty(l120s04b.getMainGrpAvgRateR())) ? "N.A." : Util
						.nullToSpace(l120s04b.getMainGrpAvgRateR()));

		// J-107-0087-001 Web
		// e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。
		rptVariableMap.put("L120S4B.SHOWGROUP", "");
		String grpGrrd = Util.nullToSpace(l120s04b.getGrpGrrd());
		String grpYear = Util.nullToSpace(l120s04b.getGrpYear());

		boolean newGrpGrade = lmsService.isNewGrpGrade(grpYear, true);
		String defultNoGrade = lmsService.getGrpNoGrade(newGrpGrade);

		if (newGrpGrade) {
			if (Util.notEquals(Util.nullToSpace(l120s04b.getGrpNo()), "")
					&& Util.notEquals(grpGrrd, "")
					&& Util.notEquals(grpGrrd, defultNoGrade)) {
				// 主要集團，要顯示
				rptVariableMap.put("L120S4B.SHOWGROUP", "Y");
			}
		} else {
			if (Util.notEquals(Util.nullToSpace(l120s04b.getGrpNo()), "")
					&& Util.notEquals(grpGrrd, "")
					&& Util.notEquals(grpGrrd, defultNoGrade)
					&& Util.notEquals(grpGrrd, "7")) {
				// 主要集團，要顯示
				rptVariableMap.put("L120S4B.SHOWGROUP", "Y");
			}
		}

		// J-111-0052 修改借戶暨關係戶與本行往來實績彙總表
		DecimalFormat df = new DecimalFormat(
				"###,###,###,###,###,###,###,##0.##");
		DecimalFormat dfRate = new DecimalFormat("###,###,##0.########");
		rptVariableMap.put("L120S4B.SHOWHINS", "N");
		if (lmsService.isHinsCust(Util.addSpaceWithValue(
				Util.trim(l120s04b.getKeyCustId()), 10)
				+ Util.trim(l120s04b.getKeyDupNo()))) {
			rptVariableMap.put("L120S4B.SHOWHINS", "Y");
			rptVariableMap.put("L120S4B.HINSDATE",
					Util.nullToSpace(l120s04b.getHinsDate()));
			rptVariableMap.put(
					"L120S4B.HINSAMT",
					(l120s04b.getHinsAmt() == null ? "" : df.format(l120s04b
							.getHinsAmt())));
			rptVariableMap.put(
					"L120S4B.HINSRATE",
					(l120s04b.getHinsRate() == null ? "" : dfRate
							.format(l120s04b.getHinsRate())));
			rptVariableMap.put(
					"L120S4B.HINSAVGAMT",
					(l120s04b.getHinsAvgAmt() == null ? "" : df.format(l120s04b
							.getHinsAvgAmt())));
			rptVariableMap.put(
					"L120S4B.HINSAVGRATE",
					(l120s04b.getHinsAvgRate() == null ? "" : dfRate
							.format(l120s04b.getHinsAvgRate())));
		}

		// M-112-0140 「借戶暨關係戶與本行往來實績彙總表」下方各業務別利潤貢獻度，另以附註方式說明薪轉戶持有信用卡之貢獻度。
		rptVariableMap.put("L120S04B.SHOWSALARYINFO", "N");
		if (l120s04b.getSalaryAcct() != null
				|| l120s04b.getSalaryCardAcct() != null
				|| l120s04b.getSalaryCardDateS() != null
				|| l120s04b.getSalaryCardDateE() != null
				|| l120s04b.getSalaryCardPcAmt() != null) {
			rptVariableMap.put("L120S04B.SHOWSALARYINFO", "Y");
			rptVariableMap.put(
					"L120S04B.SALARYACCT",
					(l120s04b.getSalaryAcct() == null ? "" : NumConverter
							.addComma(l120s04b.getSalaryAcct())));
			rptVariableMap.put(
					"L120S04B.SALARYCARDACCT",
					(l120s04b.getSalaryCardAcct() == null ? "" : NumConverter
							.addComma(l120s04b.getSalaryCardAcct())));
			rptVariableMap.put("L120S04B.SALARYCARDDATES",
					Util.nullToSpace(l120s04b.getSalaryCardDateS()));
			rptVariableMap.put("L120S04B.SALARYCARDDATEE",
					Util.nullToSpace(l120s04b.getSalaryCardDateE()));
			rptVariableMap.put(
					"L120S04B.SALARYCARDPCAMT",
					(l120s04b.getSalaryCardPcAmt() == null ? "" : df
							.format(l120s04b.getSalaryCardPcAmt())));
		}

		return rptVariableMap;
	}

	/**
	 * 設定L120S04C listModel
	 * 
	 * @param rptVariableMap
	 * @param listKind
	 * @param dateKind
	 * @param type
	 * @return
	 * @throws CapException
	 */
	private Map<String, String> setL120S04CData(
			Map<String, String> rptVariableMap, List<L120S04C> listKind,
			List<String> dateKind, String type) throws CapException {
		String[] l120S04cCol = new String[] { "docDate", "docDateE",
				"avgDepositAmt", "avgLoanAmt", "rcvBuyAvgAmt", "rcvSellAvgAmt",
				"exportAmt", "importAmt", "profitAmt", "profitSalaryAmt",
				"profitTrustFdtaAmt", "profitRate" };
		int index = 0;
		if (!dateKind.isEmpty() && !listKind.isEmpty()) {
			if ("1".equals(type)) {
				// 借款人與本行往來實績彙總表
				index = 1;
			} else if ("2".equals(type)) {
				// 借款人暨關係戶與本行往來實績彙總表
				index = 4;
			}
			for (String docDate : dateKind) {
				for (L120S04C l120s04c : listKind) {
					JSONObject json = DataParse.toJSON(l120s04c);
					if (docDate.equals(Util.trim(l120s04c.getDocDate()))) {
						for (String l120s04cCol : l120S04cCol) {
							rptVariableMap
									.put("L120S04C."
											+ (("docDate").equals(l120s04cCol) ? (l120s04cCol
													.toUpperCase() + "S")
													: l120s04cCol.toUpperCase())
											+ index,
											getProfitRate(l120s04cCol, index,
													json));
						}
						index++;
						break;
					}
				}
			}
		}
		return rptVariableMap;
	}

	/**
	 * 取得報酬率
	 * 
	 * @param l120s04cCol
	 *            欄位key
	 * @param index
	 *            欄位指標
	 * @param json
	 *            Json物件
	 * @return 報酬率
	 */
	private String getProfitRate(String l120s04cCol, int index, JSONObject json) {
		if ("profitRate".equals(l120s04cCol)) {
			// 若A-B+C = 0，則報酬率分母為零傳回N.A.
			if (LMSUtil
					.toBigDecimal(
							NumConverter.delCommaString(Util.trim(json
									.get("avgLoanAmt"))))
					.subtract(
							LMSUtil.toBigDecimal(NumConverter
									.delCommaString(Util.trim(json
											.get("rcvBuyAvgAmt")))))
					.add(LMSUtil.toBigDecimal(NumConverter.delCommaString(Util
							.trim(json.get("rcvSellAvgAmt")))))
					.compareTo(BigDecimal.ZERO) == 0) {
				return (index == 3 || index == 6 ? "＊N.A." : "N.A.");
			} else {
				return (index == 3 || index == 6 ? "＊"
						+ Util.trim(json.get(l120s04cCol)) : Util.trim(json
						.get(l120s04cCol)));
			}
		} else {
			if (Util.equals("profitAmt", l120s04cCol)) {

				return Util.trim(json.get(l120s04cCol)
						+ (json.get("profitSalaryAmt") == null ? "" : "\r("
								+ json.get("profitSalaryAmt") + ")")
						+ (json.get("profitTrustFdtaAmt") == null ? "" : "\r("
								+ json.get("profitTrustFdtaAmt") + ")"));
			} else {
				return Util.trim(json.get(l120s04cCol));
			}
		}
	}

	private Map<String, String> setL120S04EData(
			Map<String, String> rptVariableMap, List<L120S04E> listKind,
			List<String> dateKind, String type) throws CapException {
		DecimalFormat df = new DecimalFormat("#####0");
		// J-112-0147 配合企金處，「借戶暨關係戶與本行往來實績彙總表」下方各業務別利潤貢獻度欄位，增列「其他(信託等)」。
		String[] rptCols = new String[] { "docDate", "loanPcAmt", "depFxPcAmt",
				"wmPcAmt", "dervPcAmt", "salaryPcAmt", "cardPcAmt",
				"otherPcAmt", "totalPcAmt" };

		// init
		int dateSize = 2;
		if (dateKind != null && !dateKind.isEmpty()) {
			dateSize = dateKind.size();
		}
		for (int i = 0; i < dateSize; i++) {
			for (String col : rptCols) {
				col = StringUtils.upperCase(col);
				rptVariableMap
						.put("L120S04E." + col + "_" + i + "_" + type, "");
			}
		}

		boolean hasL120s04e = false;
		int index = 0;
		if (!dateKind.isEmpty() && !listKind.isEmpty()) {
			for (String docDate : dateKind) {
				for (L120S04E l120s04e : listKind) {
					JSONObject json = DataParse.toJSON(l120s04e);
					if (docDate.equals(Util.trim(l120s04e.getDocDate()))
							&& Util.equals(
									Util.nullToSpace(l120s04e.getDocKind()),
									type)) {
						hasL120s04e = true;
						BigDecimal totalPcAmt = (l120s04e.getTotalPcAmt() == null ? BigDecimal.ZERO
								: l120s04e.getTotalPcAmt());
						for (String rptCol : rptCols) {
							String rptColU = StringUtils.upperCase(rptCol);
							String rptLabel = ("L120S04E." + rptColU + "_"
									+ (dateSize - index - 1) + "_" + type);

							if (Util.equals(rptColU, "DOCDATE")) {
								rptVariableMap
										.put(rptLabel,
												(json.get(rptCol) == null ? ""
														: Util.nullToSpace(json
																.get(rptCol))));
							} else {
								BigDecimal tempAmt = LMSUtil
										.toBigDecimal(NumConverter
												.delCommaString(Util.trim(json
														.get(rptCol))));
								tempAmt = (tempAmt == null ? BigDecimal.ZERO
										: tempAmt);
								BigDecimal tempRate = null;
								if (totalPcAmt.compareTo(BigDecimal.ZERO) == 0) {
									// 分母(合計)為0 不會有占比
								} else if (tempAmt.compareTo(BigDecimal.ZERO) == 0) {
									// 分子為0 不顯示占比
								} else {
									tempRate = tempAmt.divide(totalPcAmt, 2,
											BigDecimal.ROUND_HALF_UP).multiply(
											BigDecimal.valueOf(100));
								}

								rptVariableMap
										.put(rptLabel,
												(json.get(rptCol) == null ? ""
														: (Util.nullToSpace(json
																.get(rptCol)) + (tempRate == null ? ""
																: ("\r("
																		+ df.format(tempRate) + "%)")))));
							}
						}
						index++;
						break;
					}
				}
			}
		}
		rptVariableMap.put("hasL120s04e", (hasL120s04e ? "Y" : "N"));
		return rptVariableMap;
	}

	@SuppressWarnings("unchecked")
	public OutputStream genCES1405RA1(String mainId, Locale locale,
			Properties prop) throws FileNotFoundException, IOException,
			Exception {
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		ReportGenerator generator = new ReportGenerator(
				"report/lms/CES1405RA1_" + locale.toString() + ".rpt");
		OutputStream outputStream = null;

		C140M01A meta = null;
		C140JSON c140json = null;
		C140SFFF c140sfff = null;
		try {
			meta = SimpleContextHolder.get(C140M01A);
			if (meta == null) {
				meta = service1205.getC140M01AByMainId(mainId);
			}
			rptVariableMap.put("toM5", meta.getToM5());
			rptVariableMap.put("typem5", meta.getTypem5());
			// 一、資金來源與運用
			// meta.getC140jsons();
			c140json = service1205.getC140JSONByMainPidTab(meta.getUid(),
					meta.getMainId(), "A1", "1");
			rptVariableMap.put("cr1_1", space);
			rptVariableMap.put("cr1_2", space);
			rptVariableMap.put("cr1_3", space);
			rptVariableMap.put("cr2_1", space);
			rptVariableMap.put("cr2_2", space);
			rptVariableMap.put("cr2_3", space);
			rptVariableMap.put("cr3_1", space);
			rptVariableMap.put("cr3_2", space);
			rptVariableMap.put("cr3_3", space);
			rptVariableMap.put("cr4_1", space);
			rptVariableMap.put("cr4_2", space);
			rptVariableMap.put("cr4_3", space);
			rptVariableMap.put("cr5_1", space);
			rptVariableMap.put("cr5_2", space);
			rptVariableMap.put("cr5_3", space);
			rptVariableMap.put("cr_tot", space);
			rptVariableMap.put("crp", space);
			rptVariableMap.put("cu1_1", space);
			rptVariableMap.put("cu2_1", space);
			rptVariableMap.put("cu3_1", space);
			rptVariableMap.put("cu4_1", space);
			rptVariableMap.put("cu5_1", space);
			rptVariableMap.put("cu1_2", space);
			rptVariableMap.put("cu2_2", space);
			rptVariableMap.put("cu3_2", space);
			rptVariableMap.put("cu4_2", space);
			rptVariableMap.put("cu5_2", space);
			rptVariableMap.put("cu1_3", space);
			rptVariableMap.put("cu2_3", space);
			rptVariableMap.put("cu3_3", space);
			rptVariableMap.put("cu4_3", space);
			rptVariableMap.put("cu5_3", space);
			rptVariableMap.put("cu_tot", space);
			rptVariableMap.put("cup", space);
			if (c140json != null
					&& !CapString.checkRegularMatch(c140json.getFlag(), "^D$")) {
				JSONObject data = JSONObject.fromObject(c140json.getJsonOb());
				if (data == null) {
					data = new JSONObject();
				}
				rptVariableMap.putAll(JSONUtil.parseJsonToStringMap(data));
			}
			rptVariableMap.put("cup", "100.00");
			rptVariableMap.put("crp", "100.00");
			rptVariableMap.put("cp10_cont2", space);
			// 二、重要假設條件
			c140sfff = service1205.getC140SFFF(meta.getMainId(), meta.getUid(),
					"cp10_cont2", "A1");
			if (c140sfff != null && c140json.getFlag() == null) {
				rptVariableMap.put(c140sfff.getFieldid(), c140sfff.getFfbody());
			}
			rptVariableMap.put("randomCode", meta.getRandomCode());
			rptVariableMap.put("TotPageNum", space);
			rptVariableMap.put("PageNum", space);
			rptVariableMap.put("chp10_name", space);
			generator.setLang(locale);
			rptVariableMap.put("pcType", space);
			if (Util.isEmpty(rptVariableMap.get("curramtUnitFU"))) {
				rptVariableMap.put("curramtUnitFU", space);
			}
			generator.setVariableData(rptVariableMap);
			// generator.checkVariableExist("C:/test.txt", rptVariableMap);
			outputStream = generator.generateReport();
		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
		}
		return outputStream;
	}

	public OutputStream genCES1405RA2(String mainId, Locale locale,
			Properties prop) throws FileNotFoundException, IOException,
			Exception {
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		ReportGenerator generator = new ReportGenerator(
				"report/lms/CES1405RA2_" + locale.toString() + ".rpt");
		OutputStream outputStream = null;

		C140M01A meta = null;
		List<C140SFFF> c140sfffs = null;
		try {
			meta = SimpleContextHolder.get(C140M01A);
			if (meta == null) {
				meta = service1205.getC140M01AByMainId(mainId);
			}
			rptVariableMap.put("toM5", meta.getToM5());
			rptVariableMap.put("typem5", meta.getTypem5());

			c140sfffs = service1205.getC140SFFF(mainId, meta.getUid(), "A1");
			rptVariableMap.put("cp10_cont1", space);
			rptVariableMap.put("cp10_cont2", space);
			if (c140sfffs != null) {
				for (C140SFFF sfff : c140sfffs) {
					if (sfff.getFlag() != null) {
						continue;
					}
					String fieldid = sfff.getFieldid();
					if (CapString
							.checkRegularMatch(fieldid, "^cp10_cont[1|2]$")) {
						if ("cp10_cont1".equals(fieldid)) {
							rptVariableMap.put(fieldid, sfff.getFfbody());
						} else if ("cp10_cont2".equals(fieldid)) {
							rptVariableMap.put(fieldid, sfff.getFfbody());
						}
					}
				}
			}
			rptVariableMap.put("randomCode", meta.getRandomCode());
			rptVariableMap.put("TotPageNum", space);
			rptVariableMap.put("PageNum", space);
			rptVariableMap.put("chp10_name", space);
			generator.setLang(locale);
			generator.setVariableData(rptVariableMap);
			// generator.checkVariableExist("C:/test.txt", rptVariableMap);
			outputStream = generator.generateReport();
		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
		}
		return outputStream;
	}

	@SuppressWarnings({ "unchecked", "rawtypes" })
	public OutputStream genCES1405RA4(String mainId, Locale locale,
			Properties prop, boolean setTop) throws FileNotFoundException,
			IOException, Exception {
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		ReportGenerator generator = new ReportGenerator(
				"report/lms/CES1405RA4_" + locale.toString() + ".rpt");
		OutputStream outputStream = null;

		C140M01A meta = null;
		List<C140S07A> c140s07aList = null;
		C140SFFF c140sfff = null;
		try {
			meta = SimpleContextHolder.get(C140M01A);
			if (meta == null) {
				meta = service1205.getC140M01AByMainId(mainId);
			}

			rptVariableMap.put("year_11", space);
			rptVariableMap.put("year_12", space);
			rptVariableMap.put("year_13", space);
			rptVariableMap.put("year_14", space);
			rptVariableMap.put("year_15", space);
			rptVariableMap.put("year_16", space);
			rptVariableMap.put("year_17", space);
			rptVariableMap.put("year_18", space);
			rptVariableMap.put("year_19", space);
			rptVariableMap.put("year_110", space);
			rptVariableMap.put("year_111", space);
			rptVariableMap.put("year_112", space);
			rptVariableMap.put("year_113", space);
			rptVariableMap.put("year_114", space);
			rptVariableMap.put("year_115", space);
			rptVariableMap.put("a10000000_11", space);
			rptVariableMap.put("a10000000_12", space);
			rptVariableMap.put("a10000000_13", space);
			rptVariableMap.put("a10000000_14", space);
			rptVariableMap.put("a10000000_15", space);
			rptVariableMap.put("a10000000_16", space);
			rptVariableMap.put("a10000000_17", space);
			rptVariableMap.put("a10000000_18", space);
			rptVariableMap.put("a10000000_19", space);
			rptVariableMap.put("a10000000_110", space);
			rptVariableMap.put("a10000000_111", space);
			rptVariableMap.put("a10000000_112", space);
			rptVariableMap.put("a10000000_113", space);
			rptVariableMap.put("a10000000_114", space);
			rptVariableMap.put("a10000000_115", space);
			rptVariableMap.put("a20000000_11", space);
			rptVariableMap.put("a20000000_12", space);
			rptVariableMap.put("a20000000_13", space);
			rptVariableMap.put("a20000000_14", space);
			rptVariableMap.put("a20000000_15", space);
			rptVariableMap.put("a20000000_16", space);
			rptVariableMap.put("a20000000_17", space);
			rptVariableMap.put("a20000000_18", space);
			rptVariableMap.put("a20000000_19", space);
			rptVariableMap.put("a20000000_110", space);
			rptVariableMap.put("a20000000_111", space);
			rptVariableMap.put("a20000000_112", space);
			rptVariableMap.put("a20000000_113", space);
			rptVariableMap.put("a20000000_114", space);
			rptVariableMap.put("a20000000_115", space);
			rptVariableMap.put("a30000000_11", space);
			rptVariableMap.put("a30000000_12", space);
			rptVariableMap.put("a30000000_13", space);
			rptVariableMap.put("a30000000_14", space);
			rptVariableMap.put("a30000000_15", space);
			rptVariableMap.put("a30000000_16", space);
			rptVariableMap.put("a30000000_17", space);
			rptVariableMap.put("a30000000_18", space);
			rptVariableMap.put("a30000000_19", space);
			rptVariableMap.put("a30000000_110", space);
			rptVariableMap.put("a30000000_111", space);
			rptVariableMap.put("a30000000_112", space);
			rptVariableMap.put("a30000000_113", space);
			rptVariableMap.put("a30000000_114", space);
			rptVariableMap.put("a30000000_115", space);
			rptVariableMap.put("a50000000_11", space);
			rptVariableMap.put("a50000000_12", space);
			rptVariableMap.put("a50000000_13", space);
			rptVariableMap.put("a50000000_14", space);
			rptVariableMap.put("a50000000_15", space);
			rptVariableMap.put("a50000000_16", space);
			rptVariableMap.put("a50000000_17", space);
			rptVariableMap.put("a50000000_18", space);
			rptVariableMap.put("a50000000_19", space);
			rptVariableMap.put("a50000000_110", space);
			rptVariableMap.put("a50000000_111", space);
			rptVariableMap.put("a50000000_112", space);
			rptVariableMap.put("a50000000_113", space);
			rptVariableMap.put("a50000000_114", space);
			rptVariableMap.put("a50000000_115", space);
			rptVariableMap.put("r30_11", space);
			rptVariableMap.put("r30_12", space);
			rptVariableMap.put("r30_13", space);
			rptVariableMap.put("r30_14", space);
			rptVariableMap.put("r30_15", space);
			rptVariableMap.put("r30_16", space);
			rptVariableMap.put("r30_17", space);
			rptVariableMap.put("r30_18", space);
			rptVariableMap.put("r30_19", space);
			rptVariableMap.put("r30_110", space);
			rptVariableMap.put("r30_111", space);
			rptVariableMap.put("r30_112", space);
			rptVariableMap.put("r30_113", space);
			rptVariableMap.put("r30_114", space);
			rptVariableMap.put("r30_115", space);
			rptVariableMap.put("a57000000_11", space);
			rptVariableMap.put("a57000000_12", space);
			rptVariableMap.put("a57000000_13", space);
			rptVariableMap.put("a57000000_14", space);
			rptVariableMap.put("a57000000_15", space);
			rptVariableMap.put("a57000000_16", space);
			rptVariableMap.put("a57000000_17", space);
			rptVariableMap.put("a57000000_18", space);
			rptVariableMap.put("a57000000_19", space);
			rptVariableMap.put("a57000000_110", space);
			rptVariableMap.put("a57000000_111", space);
			rptVariableMap.put("a57000000_112", space);
			rptVariableMap.put("a57000000_113", space);
			rptVariableMap.put("a57000000_114", space);
			rptVariableMap.put("a57000000_115", space);
			rptVariableMap.put("r32_11", space);
			rptVariableMap.put("r32_12", space);
			rptVariableMap.put("r32_13", space);
			rptVariableMap.put("r32_14", space);
			rptVariableMap.put("r32_15", space);
			rptVariableMap.put("r32_16", space);
			rptVariableMap.put("r32_17", space);
			rptVariableMap.put("r32_18", space);
			rptVariableMap.put("r32_19", space);
			rptVariableMap.put("r32_110", space);
			rptVariableMap.put("r32_111", space);
			rptVariableMap.put("r32_112", space);
			rptVariableMap.put("r32_113", space);
			rptVariableMap.put("r32_114", space);
			rptVariableMap.put("r32_115", space);
			rptVariableMap.put("a70000000_11", space);
			rptVariableMap.put("a70000000_12", space);
			rptVariableMap.put("a70000000_13", space);
			rptVariableMap.put("a70000000_14", space);
			rptVariableMap.put("a70000000_15", space);
			rptVariableMap.put("a70000000_16", space);
			rptVariableMap.put("a70000000_17", space);
			rptVariableMap.put("a70000000_18", space);
			rptVariableMap.put("a70000000_19", space);
			rptVariableMap.put("a70000000_110", space);
			rptVariableMap.put("a70000000_111", space);
			rptVariableMap.put("a70000000_112", space);
			rptVariableMap.put("a70000000_113", space);
			rptVariableMap.put("a70000000_114", space);
			rptVariableMap.put("a70000000_115", space);
			rptVariableMap.put("r33_11", space);
			rptVariableMap.put("r11_11", space);
			rptVariableMap.put("r12_11", space);
			rptVariableMap.put("r14_11", space);
			rptVariableMap.put("r20_11", space);
			rptVariableMap.put("r15_11", space);
			rptVariableMap.put("r33_12", space);
			rptVariableMap.put("r33_13", space);
			rptVariableMap.put("r33_14", space);
			rptVariableMap.put("r33_15", space);
			rptVariableMap.put("r33_16", space);
			rptVariableMap.put("r33_17", space);
			rptVariableMap.put("r33_18", space);
			rptVariableMap.put("r33_19", space);
			rptVariableMap.put("r33_110", space);
			rptVariableMap.put("r33_111", space);
			rptVariableMap.put("r33_112", space);
			rptVariableMap.put("r33_113", space);
			rptVariableMap.put("r33_114", space);
			rptVariableMap.put("r33_115", space);
			rptVariableMap.put("r11_12", space);
			rptVariableMap.put("r11_13", space);
			rptVariableMap.put("r11_14", space);
			rptVariableMap.put("r11_15", space);
			rptVariableMap.put("r11_16", space);
			rptVariableMap.put("r11_17", space);
			rptVariableMap.put("r11_18", space);
			rptVariableMap.put("r11_19", space);
			rptVariableMap.put("r11_110", space);
			rptVariableMap.put("r11_111", space);
			rptVariableMap.put("r11_112", space);
			rptVariableMap.put("r11_113", space);
			rptVariableMap.put("r11_114", space);
			rptVariableMap.put("r11_115", space);
			rptVariableMap.put("r12_12", space);
			rptVariableMap.put("r12_13", space);
			rptVariableMap.put("r12_14", space);
			rptVariableMap.put("r12_15", space);
			rptVariableMap.put("r12_16", space);
			rptVariableMap.put("r12_17", space);
			rptVariableMap.put("r12_18", space);
			rptVariableMap.put("r12_19", space);
			rptVariableMap.put("r12_110", space);
			rptVariableMap.put("r12_111", space);
			rptVariableMap.put("r12_112", space);
			rptVariableMap.put("r12_113", space);
			rptVariableMap.put("r12_114", space);
			rptVariableMap.put("r12_115", space);
			rptVariableMap.put("r14_12", space);
			rptVariableMap.put("r14_13", space);
			rptVariableMap.put("r14_14", space);
			rptVariableMap.put("r14_15", space);
			rptVariableMap.put("r14_16", space);
			rptVariableMap.put("r14_17", space);
			rptVariableMap.put("r14_18", space);
			rptVariableMap.put("r14_19", space);
			rptVariableMap.put("r14_110", space);
			rptVariableMap.put("r14_111", space);
			rptVariableMap.put("r14_112", space);
			rptVariableMap.put("r14_113", space);
			rptVariableMap.put("r14_114", space);
			rptVariableMap.put("r14_115", space);
			rptVariableMap.put("r20_12", space);
			rptVariableMap.put("r20_13", space);
			rptVariableMap.put("r20_14", space);
			rptVariableMap.put("r20_15", space);
			rptVariableMap.put("r20_16", space);
			rptVariableMap.put("r20_17", space);
			rptVariableMap.put("r20_18", space);
			rptVariableMap.put("r20_19", space);
			rptVariableMap.put("r20_110", space);
			rptVariableMap.put("r20_111", space);
			rptVariableMap.put("r20_112", space);
			rptVariableMap.put("r20_113", space);
			rptVariableMap.put("r20_114", space);
			rptVariableMap.put("r20_115", space);
			rptVariableMap.put("r15_12", space);
			rptVariableMap.put("r15_13", space);
			rptVariableMap.put("r15_14", space);
			rptVariableMap.put("r15_15", space);
			rptVariableMap.put("r15_16", space);
			rptVariableMap.put("r15_17", space);
			rptVariableMap.put("r15_18", space);
			rptVariableMap.put("r15_19", space);
			rptVariableMap.put("r15_110", space);
			rptVariableMap.put("r15_111", space);
			rptVariableMap.put("r15_112", space);
			rptVariableMap.put("r15_113", space);
			rptVariableMap.put("r15_114", space);
			rptVariableMap.put("r15_115", space);

			c140s07aList = service1205.getS07aByMetaAndTab(meta, "A1", "13");
			if (!CollectionUtils.isEmpty(c140s07aList)) {
				Iterator i$ = c140s07aList.iterator();
				do {
					if (!i$.hasNext())
						break;
					C140S07A c140s07a = (C140S07A) i$.next();
					JSONObject data = JSONObject.fromObject(c140s07a
							.getJsonData());
					String sn = String.valueOf(c140s07a.getSn().setScale(0));
					Set keySet = data.keySet();
					String keyList[] = (String[]) keySet
							.toArray(new String[keySet.size()]);
					String arr$[] = keyList;
					int len$ = arr$.length;
					for (int j$ = 0; j$ < len$; j$++) {
						String key = arr$[j$];
						rptVariableMap.put((new StringBuilder()).append(key)
								.append("_1").append(sn).toString(),
								data.getString(key));
					}

					rptVariableMap.put((new StringBuilder()).append("year_1")
							.append(sn).toString(), c140s07a.getYear());
					if ("1".equals(sn)) {
						rptVariableMap.put("curr_11", c140s07a.getCurr());
						rptVariableMap.put("amtUnit_11", CapMath
								.bigDecimalToString(c140s07a.getAmtUnit()));
					}
				} while (true);
			}

			// 基本案財務預估分析
			c140sfff = service1205.getC140SFFF(meta.getMainId(), meta.getUid(),
					"bfp_note1_1", "A1");
			if (c140sfff != null && c140sfff.getFlag() == null) {
				rptVariableMap.put(c140sfff.getFieldid(), c140sfff.getFfbody());
			} else {
				rptVariableMap.put("bfp_note1_1", space);
			}

			rptVariableMap.put("randomCode", meta.getRandomCode());
			rptVariableMap.put("yearCountOfData",
					String.valueOf(c140s07aList.size()));
			rptVariableMap.put("TotPageNum", space);
			rptVariableMap.put("PageNum", space);
			// rptVariableMap.put("chp10_name", space);
			generator.setLang(locale);
			generator.setVariableData(rptVariableMap);
			// generator.checkVariableExist("C:/test.txt", rptVariableMap);
			outputStream = generator.generateReport(setTop);
		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
		}
		return outputStream;
	}

	@SuppressWarnings("unchecked")
	public OutputStream genCES1405RA6(String mainId, Locale locale,
			Properties prop) throws FileNotFoundException, IOException,
			Exception {
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		ReportGenerator generator = new ReportGenerator(
				"report/lms/CES1405RA6_" + locale.toString() + ".rpt");
		OutputStream outputStream = null;

		C140M01A meta = null;
		List<C140S07A> c140s07aList = null;
		try {
			meta = SimpleContextHolder.get(C140M01A);
			if (meta == null) {
				meta = service1205.getC140M01AByMainId(mainId);
			}

			c140s07aList = service1205.getS07aByMetaAndTab(meta, "A1", "14");

			rptVariableMap.put("year_21", space);
			rptVariableMap.put("year_22", space);
			rptVariableMap.put("year_23", space);
			rptVariableMap.put("year_24", space);
			rptVariableMap.put("year_25", space);
			rptVariableMap.put("year_26", space);
			rptVariableMap.put("year_27", space);
			rptVariableMap.put("case1_21", space);
			rptVariableMap.put("r15_21", space);
			rptVariableMap.put("san1_21", space);
			rptVariableMap.put("san2_21", space);
			rptVariableMap.put("san3_21", space);
			rptVariableMap.put("r15_22", space);
			rptVariableMap.put("year_28", space);
			rptVariableMap.put("year_29", space);
			rptVariableMap.put("year_210", space);
			rptVariableMap.put("year_211", space);
			rptVariableMap.put("year_212", space);
			rptVariableMap.put("year_213", space);
			rptVariableMap.put("year_214", space);
			rptVariableMap.put("year_215", space);
			rptVariableMap.put("r15_23", space);
			rptVariableMap.put("r15_24", space);
			rptVariableMap.put("r15_25", space);
			rptVariableMap.put("r15_26", space);
			rptVariableMap.put("r15_27", space);
			rptVariableMap.put("r15_28", space);
			rptVariableMap.put("r15_29", space);
			rptVariableMap.put("r15_210", space);
			rptVariableMap.put("r15_211", space);
			rptVariableMap.put("r15_212", space);
			rptVariableMap.put("r15_213", space);
			rptVariableMap.put("r15_214", space);
			rptVariableMap.put("r15_215", space);
			rptVariableMap.put("san1_22", space);
			rptVariableMap.put("san1_23", space);
			rptVariableMap.put("san1_24", space);
			rptVariableMap.put("san1_25", space);
			rptVariableMap.put("san1_26", space);
			rptVariableMap.put("san1_27", space);
			rptVariableMap.put("san1_28", space);
			rptVariableMap.put("san1_29", space);
			rptVariableMap.put("san1_210", space);
			rptVariableMap.put("san1_211", space);
			rptVariableMap.put("san1_212", space);
			rptVariableMap.put("san1_213", space);
			rptVariableMap.put("san1_214", space);
			rptVariableMap.put("san1_215", space);
			rptVariableMap.put("san2_22", space);
			rptVariableMap.put("san2_23", space);
			rptVariableMap.put("san2_24", space);
			rptVariableMap.put("san2_25", space);
			rptVariableMap.put("san2_26", space);
			rptVariableMap.put("san2_27", space);
			rptVariableMap.put("san2_28", space);
			rptVariableMap.put("san2_29", space);
			rptVariableMap.put("san2_210", space);
			rptVariableMap.put("san2_211", space);
			rptVariableMap.put("san2_212", space);
			rptVariableMap.put("san2_213", space);
			rptVariableMap.put("san2_214", space);
			rptVariableMap.put("san2_215", space);
			rptVariableMap.put("san3_22", space);
			rptVariableMap.put("san3_23", space);
			rptVariableMap.put("san3_24", space);
			rptVariableMap.put("san3_25", space);
			rptVariableMap.put("san3_26", space);
			rptVariableMap.put("san3_27", space);
			rptVariableMap.put("san3_28", space);
			rptVariableMap.put("san3_29", space);
			rptVariableMap.put("san3_210", space);
			rptVariableMap.put("san3_211", space);
			rptVariableMap.put("san3_212", space);
			rptVariableMap.put("san3_213", space);
			rptVariableMap.put("san3_214", space);
			rptVariableMap.put("san3_215", space);
			rptVariableMap.put("case2_21", space);
			rptVariableMap.put("case3_21", space);

			if (!CollectionUtils.isEmpty(c140s07aList)) {
				for (C140S07A c140s07a : c140s07aList) {
					JSONObject data = JSONObject.fromObject(c140s07a
							.getJsonData());
					String sn = String.valueOf(c140s07a.getSn().setScale(0));
					Set<String> keySet = data.keySet();
					String[] keyList = keySet
							.toArray(new String[keySet.size()]);
					for (String key : keyList) {
						rptVariableMap
								.put(key + "_2" + sn, data.getString(key));
					}
					rptVariableMap.put("year_2" + sn, c140s07a.getYear());
				}
			}

			rptVariableMap.put("san_note", space);

			// 分析
			C140SDSC c140sdsc = service1205.getC140SDSCByMainPidTab(
					meta.getUid(), meta.getMainId(), "A1", "");
			if (c140sdsc != null && c140sdsc.getFlag() == null) {
				rptVariableMap.put(c140sdsc.getFieldId(), c140sdsc.getVal());
			}

			rptVariableMap.put("any1_#", space);
			rptVariableMap.put("amtUnit", space);
			rptVariableMap.put("curr", space);
			rptVariableMap.put("randomCode", meta.getRandomCode());
			rptVariableMap.put("TotPageNum", space);
			rptVariableMap.put("PageNum", space);
			rptVariableMap.put("yearCountOfData",
					String.valueOf(c140s07aList.size()));
			// rptVariableMap.put("chp10_name", space);
			generator.setLang(locale);
			generator.setVariableData(rptVariableMap);
			// generator.checkVariableExist("C:/test.txt", rptVariableMap);
			outputStream = generator.generateReport();
		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
		}
		return outputStream;
	}

	public OutputStream genLMS1205R16(String mainId, Locale locale,
			Properties prop) throws FileNotFoundException, IOException,
			Exception {
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		ReportGenerator generator = new ReportGenerator(
				"report/lms/LMS1205R16_" + locale.toString() + ".rpt");
		OutputStream outputStream = null;
		// L120M01A
		L120M01A l120m01a = null;
		L120M01D l120m01d = null;
		try {
			l120m01a = service1205.findL120m01aByMainId(mainId);
			if (l120m01a == null)
				l120m01a = new L120M01A();
			l120m01d = service1205.findL120m01dByUniqueKey(mainId, "4");
			if (l120m01d == null)
				l120m01d = new L120M01D();

			rptVariableMap.put("DOCTYPE", "");
			// 文件亂碼
			rptVariableMap.put("L120M01A.RANDOMCODE",
					Util.trim(l120m01a.getRandomCode()));
			// 綜合評估敘做理由
			rptVariableMap.put("L120M01D.ITEMDSCR4",
					Util.trim(l120m01d.getItemDscr()));
			// generator.setLang(java.util.Locale.TAIWAN);
			generator.setLang(locale);
			generator.setVariableData(rptVariableMap);
			// generator.setReportFile("report/lms/LMS1205R05_" +
			// locale.toString() + ".rpt");
			// generator.setTestMethod(true);
			// generator.checkVariableExist("C:/test.txt", rptVariableMap);
			outputStream = generator.generateReport();

		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
		}
		return outputStream;
	}

	@SuppressWarnings("unused")
	public OutputStream genCES1405R41(String mainId, Locale locale,
			Properties prop) throws FileNotFoundException, IOException,
			Exception {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS08APage.class);
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		List<Map<String, String>> list = new LinkedList<Map<String, String>>();
		ReportGenerator generator = new ReportGenerator(
				"report/lms/CES1405R41_" + locale.toString() + ".rpt");
		OutputStream outputStream = null;
		C140M01A meta = null;
		CodeTypeFormatter titleFmr;
		CodeTypeFormatter eduFmr;
		CodeTypeFormatter landLevel;
		CodeTypeFormatter landUse1;
		CodeTypeFormatter buUse;
		CodeTypeFormatter buStru;
		CodeTypeFormatter yesNo2;
		try {

			meta = SimpleContextHolder.get(C140M01A);
			if (meta == null) {
				meta = service1205.getC140M01AByMainId(mainId);
			}
			if (meta != null) {
				rptVariableMap.put("randomCode", meta.getRandomCode());
				List<C140M04A> c140m04as = service1205
						.getC140M04AByMainId(mainId);
				List<C140JSON> c140jsons = new ArrayList<C140JSON>();
				// List<C140JSON> c140jsons41;
				List<C140S04A> c140s04as;
				List<C140S04B> c140s04bs;
				List<C140S04C> c140s04cs;
				if (!CollectionUtils.isEmpty(c140m04as)) {
					titleFmr = new CodeTypeFormatter(codetypeservice, "Title2");
					eduFmr = new CodeTypeFormatter(codetypeservice, "Education");
					landLevel = new CodeTypeFormatter(codetypeservice,
							"LandLevel");
					landUse1 = new CodeTypeFormatter(codetypeservice,
							"LandUse1");
					buUse = new CodeTypeFormatter(codetypeservice, "BuUse");
					buStru = new CodeTypeFormatter(codetypeservice, "BuStru");
					yesNo2 = new CodeTypeFormatter(codetypeservice,
							"Common_YesNo2");
					int i = 0;
					List<List<String>> m04toList = new ArrayList<List<String>>();
					try {
						for (C140M04A m04a : c140m04as) {
							if (m04a.getL120m01e() != null) {
								if ("4".equals(m04a.getL120m01e().getDocType())) {
									if (++i > 5)
										break;
									C140JSON jsn1 = null, jsn5 = null, jsn7 = null, jsn8 = null, jsn9 = null, jsn10 = null;
									if (m04a.getC140jsons() != null) {
										c140jsons.addAll(m04a.getC140jsons());
										for (C140JSON jsn : m04a.getC140jsons()) {
											int key = Integer.parseInt(jsn
													.getSubtab());
											switch (key) {
											case 1:
												jsn1 = jsn;
												break;
											case 5:
												jsn5 = jsn;
												break;
											case 6:
												jsn7 = jsn;
												break;
											case 7:
												jsn8 = jsn;
												break;
											case 8:
												jsn9 = jsn;
												break;
											case 9:
												jsn10 = jsn;
												break;
											default:
												break;
											}
										}
									}
									List<String> tmp = new LinkedList<String>();
									JSONObject jsob = JSONObject
											.fromObject(jsn1.getJsonOb());
									if (!jsob.isEmpty()) {
										if ("Y".equals(jsob.get("pcCountry")))
											tmp.add(pop
													.getProperty("option.001"));
										else
											tmp.add(pop
													.getProperty("option.002"));
										tmp.add(m04a.getPcName());
										if ("1".equals(m04a.getPcSex()))
											tmp.add(pop.getProperty("pcSex.1"));
										else
											tmp.add(pop.getProperty("pcSex.2"));
										tmp.add(m04a.getPcId());
										tmp.add((String) jsob.get("pcBirth"));
										tmp.add(titleFmr.reformat(m04a
												.getPcTitle()));
										tmp.add(eduFmr.reformat(jsob
												.get("pcEdu")));
										tmp.add((String) jsob.get("pcExp"));
										tmp.add((String) jsob.get("pcSchool"));
										tmp.add((String) jsob.get("pcFaddr"));
										tmp.add((String) jsob.get("pcCaddr"));
										tmp.add((String) jsob.get("pcTel"));
										tmp.add((String) jsob.get("pcwName"));
										tmp.add((String) jsob.get("pcwId"));
									} else {
										tmp.add(space);
										tmp.add(space);
										tmp.add(space);
										tmp.add(space);
										tmp.add(space);
										tmp.add(space);
										tmp.add(space);
										tmp.add(space);
										tmp.add(space);
										tmp.add(space);
										tmp.add(space);
										tmp.add(space);
										tmp.add(space);
										tmp.add(space);
									}
									// 經營事業
									c140s04as = service1205.getC140S04A(mainId,
											m04a.getUid());
									int count = 0;
									for (C140S04A s4a : c140s04as) {
										if (++count > 5)
											break;
										tmp.add(s4a.getInvBusna1());
										tmp.add(s4a.getInvKind1());
										tmp.add(s4a.getInvCap11());
										tmp.add(s4a.getInvUnit11() == null ? ""
												: (new NumericFormatter()).reformat(s4a
														.getInvUnit11()));
										tmp.add(s4a.getInvUnit21() == null ? ""
												: (new NumericFormatter()).reformat(s4a
														.getInvUnit21()));
									}
									for (int k = 0; k < ((5 - c140s04as.size()) * 5); k++)
										tmp.add("");
									// 本人土地
									c140s04bs = service1205.getC140S04B(mainId,
											m04a.getUid());
									count = 0;
									for (C140S04B s4b : c140s04bs) {
										if (++count > 3)
											break;
										tmp.add(s4b.getLandAddr());
										tmp.add(s4b.getLandNum());
										tmp.add((new StringBuilder())
												.append(landUse1.reformat(s4b
														.getLandUse1()))
												.append("/")
												.append((new CodeTypeFormatter(
														codetypeservice,
														(new StringBuilder())
																.append("LandUse2")
																.append(s4b
																		.getLandUse1())
																.toString()))
														.reformat(s4b
																.getLandUse2()))
												.toString());
										tmp.add(landLevel.reformat(s4b
												.getLandLevel()));
										tmp.add(bigDecimal2String(s4b
												.getLandRateC()));
										tmp.add(bigDecimal2String(s4b
												.getLandRateD()));
										tmp.add(bigDecimal2String(s4b
												.getLandBp()));
										tmp.add(bigDecimal2String(s4b
												.getLandAp()));
										tmp.add(s4b.getLandMp());
										tmp.add(s4b.getLandMm() == null ? ""
												: (new NumericFormatter())
														.reformat(s4b
																.getLandMm()));
									}
									for (int k = 0; k < ((3 - c140s04bs.size()) * 10); k++)
										tmp.add("");
									// 本人建物
									c140s04cs = service1205.getC140S04C(mainId,
											m04a.getUid());
									count = 0;
									for (C140S04C s4c : c140s04cs) {
										if (++count > 3)
											break;
										tmp.add(s4c.getBuAddr());
										tmp.add(s4c.getBuNum());
										tmp.add(bigDecimal2String(s4c.getBuP()));
										tmp.add(buStru.reformat(s4c.getBuStru()));
										tmp.add(buUse.reformat(s4c.getBuUse()));
										tmp.add(s4c.getBuMp());
										tmp.add((new NumericFormatter())
												.reformat(s4c.getBuMm()));
									}
									for (int k = 0; k < ((3 - c140s04cs.size()) * 7); k++)
										tmp.add("");
									// 經濟狀況
									if (jsn5 == null)
										jsn5 = new C140JSON();
									String[] js5Cols = new String[] { "currDE",
											"amtUnitDE", "depName1",
											"depType1", "depAmnt1", "depNote1",
											"depName2", "depType2", "depAmnt2",
											"depNote2", "depName3", "depType3",
											"depAmnt3", "depNote3", "borName1",
											"borAmnt1", "borName2", "borAmnt2",
											"borName3", "borAmnt3", "borNote1",
											"dep_Tot_amnt", "bor_Tot_amnt",
											"peoLoanDate", "stkNa1",
											"stkAmnt1", "stkNote1", "stkNa2",
											"stkAmnt2", "stkNote2", "stkNa3",
											"stkAmnt3", "stkNote3", "proPeo1",
											"proAmnt1", "proPeo2", "proAmnt2",
											"proPeo3", "proAmnt3", "proNote1",
											"depOthNa1", "depOthNote1",
											"depOthNa2", "depOthNote2",
											"depOthNa3", "depOthNote3",
											"borOthNa1", "borOthNote1",
											"borOthNa2", "borOthNote2",
											"borOthNa3", "borOthNote3" };
									jsob = JSONObject.fromObject(jsn5
											.getJsonOb());
									CodeTypeFormatter currcyFmt = new CodeTypeFormatter(
											codetypeservice, CodeTypeEnum.幣別代碼);
									if (!jsob.isEmpty()) {
										for (String s : js5Cols) {
											if (jsob.containsKey(s)) {
												if ("amtUnitDE".equals(s)) {
													tmp.add(CapString
															.trimNull(jsob
																	.get(s)));
													continue;
												}
												if (s.indexOf("depType") != -1) {
													tmp.add((new CodeTypeFormatter(
															codetypeservice,
															"depType"))
															.reformat(jsob
																	.get(s)));
													continue;
												}
												if (s.indexOf("amt") != -1
														|| s.indexOf("amnt") != -1
														|| s.indexOf("Amnt") != -1
														&& !"".equals(jsob
																.get(s)))
													tmp.add((new NumericFormatter())
															.reformat(jsob
																	.get(s)));
												else
													tmp.add(CapString
															.trimNull(jsob
																	.get(s)));
											} else {
												tmp.add("");
											}
										}
									} else {
										for (String s : js5Cols) {
											tmp.add("");
										}
									}
									// 於本行主債務、共同債務及保證債務情形
									if (jsn10 == null)
										jsn10 = new C140JSON();
									String[] js10Cols = new String[] { "brNo",
											"mBalance", "mAccountState",
											"jDebtorBalance", "jDebtorState",
											"jDebtorName", "gDebtBalance",
											"gDebtState", "gDebtName" };
									jsob = JSONObject.fromObject(jsn10
											.getJsonOb());
									// 金額單位
									if (!jsob.isEmpty()) {
										tmp.add(CapString.trimNull(jsob
												.get("Curr")));
										tmp.add(CapString.trimNull(jsob
												.get("amtUnit")));
									} else {
										tmp.add("");
										tmp.add("");
									}

									for (int c = 1; c <= 4; c++) {
										for (String s : js10Cols) {
											s = s + c;
											if (!jsob.isEmpty()) {
												tmp.add(CapString.trimNull(jsob
														.get(s)));
											} else {
												tmp.add("");
											}
										}
									}

									if (!jsob.isEmpty()) {
										// 合計
										tmp.add("".equals(jsob
												.get("mBalanceTatal")) ? ""
												: (new NumericFormatter()).reformat(jsob
														.get("mBalanceTatal")));
										tmp.add("".equals(jsob
												.get("jDebtorBalanceTatal")) ? ""
												: (new NumericFormatter()).reformat(jsob
														.get("jDebtorBalanceTatal")));
										tmp.add("".equals(jsob
												.get("gDebtBalanceTatal")) ? ""
												: (new NumericFormatter()).reformat(jsob
														.get("gDebtBalanceTatal")));
										// 資料查詢日期
										tmp.add(CapString.trimNull(jsob
												.get("debtCheckDate")));
									} else {
										// 合計
										tmp.add("");
										tmp.add("");
										tmp.add("");
										// 資料查詢日期
										tmp.add("");
									}

									// 個人收支
									if (jsn7 == null)
										jsn7 = new C140JSON();
									String[] js7Cols = new String[] { "mycurr",
											"myamtUnit", "myYear", "myIcom",
											"myOcom" };
									String arr$[];
									int len$;
									jsob = JSONObject.fromObject(jsn7
											.getJsonOb());
									arr$ = js7Cols;
									len$ = arr$.length;
									for (int i$ = 0; i$ < len$; i$++) {
										String s = arr$[i$];
										if (jsob.containsKey(s)) {
											if ("amtUnit".equals(s)) {
												if (jsob.get(s) instanceof JSONArray)
													tmp.add(CapString
															.trimNull(jsob
																	.getJSONArray(
																			s)
																	.get(0)));
												else
													tmp.add(CapString
															.trimNull(jsob
																	.get(s)));
												continue;
											}
											if ("myYear".equals(s))
												tmp.add(CapString.trimNull(jsob
														.get(s)));
											else
												tmp.add(CapString.trimNull(jsob
														.get(s)));
										} else {
											tmp.add("");
										}
									}
									// if (!jsob.isEmpty()) {
									// for (String s : js7Cols) {
									// if (jsob.containsKey(s)) {
									// if (s.indexOf("curr") != -1) {
									// tmp.add(currcyFmt
									// .reformat(CapString
									// .trimNull(jsob
									// .get(s))));
									// //
									// reportData.setField("CommonBean6.field01",
									// // ct!=null?ct.getCodeDesc():"");
									// }
									// if ("amtUnit".equals(s)) {
									// if (jsob.get(s) instanceof JSONArray) {
									// tmp.add(CapString
									// .trimNull(((JSONArray) jsob
									// .getJSONArray(s))
									// .get(0)));
									// //
									// reportData.setField("CommonBean6.field02",
									// //
									// CapString.trimNull(((JSONArray)jsob.getJSONArray(s)).get(0)));
									// } else {
									// tmp.add(CapString.trimNull(jsob
									// .get(s)));
									// //
									// reportData.setField("CommonBean6.field02",
									// // CapString.trimNull(jsob.get(s)));
									// }
									// }
									// if ("myYear".equals(s)) {
									// tmp.add(CapString.trimNull(jsob
									// .get(s)));
									// //
									// reportData.setField("CommonBean6.field03",
									// // CapString.trimNull(jsob.get(s)));
									// } else
									// tmp.add(CapString.trimNull(jsob
									// .get(s)));
									// } else {
									// tmp.add("");
									// }
									//
									// }
									// } else {
									// for (@SuppressWarnings("unused")
									// String s : js7Cols) {
									// tmp.add("");
									// }
									// }

									// 信用情形及商場風評
									if (jsn8 == null)
										jsn8 = new C140JSON();
									String[] js8Cols = new String[] { "crdD3",
											"crdLyear", "refTick2",
											"qryDefault", "defaultRec",
											"creStopuse", "creStoptype",
											"creStopdate", "creStoprea",
											"creDelaycount", "isUsecashrevol",
											"creDelaymon", "isUsecredrevol" };
									jsob = JSONObject.fromObject(jsn8
											.getJsonOb());
									if (!jsob.isEmpty()) {
										for (String s : js8Cols) {
											if (jsob.containsKey(s)) {
												if ("crdLyear".equals(s)) {
													tmp.add((new CodeTypeFormatter(
															codetypeservice,
															"LatestYear"))
															.reformat(jsob
																	.get(s)));
													continue;
												}
												if ("refTick2".equals(s)) {
													tmp.add(yesNo2
															.reformat(jsob
																	.get(s)));
													continue;
												}
												if ("creStopuse".equals(s)) {
													tmp.add("1".equals(CapString
															.trimNull(jsob
																	.get(s))) ? "是"
															: "2".equals(CapString
																	.trimNull(jsob
																			.get(s))) ? "否"
																	: "");
													continue;
												}
												if ("isUsecashrevol".equals(s)) {
													tmp.add("1".equals(CapString
															.trimNull(jsob
																	.get(s))) ? "1"
															: "0".equals(CapString
																	.trimNull(jsob
																			.get(s))) ? "2"
																	: "");
													continue;
												}
												if ("isUsecredrevol".equals(s)) {
													tmp.add("1".equals(CapString
															.trimNull(jsob
																	.get(s))) ? "1"
															: "0".equals(CapString
																	.trimNull(jsob
																			.get(s))) ? "2"
																	: "");
													continue;
												}
												if ("defaultRec".equals(s))
													tmp.add(yesNo2
															.reformat(jsob
																	.get(s)));
												else
													tmp.add(CapString
															.trimNull(jsob
																	.get(s)));
											} else {
												tmp.add("");
											}

										}
									} else {
										for (String s : js8Cols) {
											tmp.add("");
										}
									}
									// 資料來源
									if (jsn9 == null)
										jsn9 = new C140JSON();
									jsob = JSONObject.fromObject(jsn9
											.getJsonOb());
									if (!jsob.isEmpty()) {
										tmp.add(CapString.trimNull(jsob
												.get("dataSrc")));
									} else {
										tmp.add("");
									}
									// 將一筆列印資料放到List
									m04toList.add(tmp);
								}
							}
						}
						for (List<String> lists : m04toList) {
							// 主Grid的筆數
							if (!lists.isEmpty()) {
								list = setCes1405r41Data(list, lists);
							}
						}
					} catch (Exception e) {
						LOGGER.error(e.getMessage(), e);
					}
				}
				List<C140SDSC> c140sdscs = service1205.getC140SDSCByMainPidTab(
						meta.getUid(), meta.getMainId(), "41");
				if (c140sdscs != null && !c140sdscs.isEmpty()) {
					for (C140SDSC c140sdsc : c140sdscs) {
						rptVariableMap.put(c140sdsc.getFieldId(),
								c140sdsc.getVal());
					}
				} else {
					rptVariableMap.put("ib", space);
					rptVariableMap.put("ro1note", space);
					rptVariableMap.put("ro2note", space);
					rptVariableMap.put("ro3note", space);
					rptVariableMap.put("ro4note", space);
				}
			}
			generator.setLang(locale);
			rptVariableMap.put("pcType", space);
			generator.setVariableData(rptVariableMap);
			generator.setRowsData(list);
			// generator.checkVariableExist("C:/test.txt", rptVariableMap);
			outputStream = generator.generateReport();

		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
		}

		return outputStream;
	}

	private List<Map<String, String>> setCes1405r41Data(
			List<Map<String, String>> list, List<String> slist) {
		String[] columns = { "CommonBean1.field01", "CommonBean1.field02",
				"CommonBean1.field03", "CommonBean1.field04",
				"CommonBean1.field05", "CommonBean1.field06",
				"CommonBean1.field07", "CommonBean1.field08",
				"CommonBean1.field09", "CommonBean1.field10",
				"CommonBean1.field11", "CommonBean1.field12",
				"CommonBean1.field13", "CommonBean1.field14",
				"CommonBean2.field01", "CommonBean2.field02",
				"CommonBean2.field03", "CommonBean2.field04",
				"CommonBean2.field05", "CommonBean2.field06",
				"CommonBean2.field07", "CommonBean2.field08",
				"CommonBean2.field09", "CommonBean2.field10",
				"CommonBean2.field11", "CommonBean2.field12",
				"CommonBean2.field13", "CommonBean2.field14",
				"CommonBean2.field15", "CommonBean2.field16",
				"CommonBean2.field17", "CommonBean2.field18",
				"CommonBean2.field19", "CommonBean2.field20",
				"CommonBean2.field21", "CommonBean2.field22",
				"CommonBean2.field23", "CommonBean2.field24",
				"CommonBean2.field25", "CommonBean3.field01",
				"CommonBean3.field02", "CommonBean3.field03",
				"CommonBean3.field04", "CommonBean3.field05",
				"CommonBean3.field06", "CommonBean3.field07",
				"CommonBean3.field08", "CommonBean3.field09",
				"CommonBean3.field10", "CommonBean3.field11",
				"CommonBean3.field12", "CommonBean3.field13",
				"CommonBean3.field14", "CommonBean3.field15",
				"CommonBean3.field16", "CommonBean3.field17",
				"CommonBean3.field18", "CommonBean3.field19",
				"CommonBean3.field20", "CommonBean3.field21",
				"CommonBean3.field22", "CommonBean3.field23",
				"CommonBean3.field24", "CommonBean3.field25",
				"CommonBean3.field26", "CommonBean3.field27",
				"CommonBean3.field28", "CommonBean3.field29",
				"CommonBean3.field30", "CommonBean4.field01",
				"CommonBean4.field02", "CommonBean4.field03",
				"CommonBean4.field04", "CommonBean4.field05",
				"CommonBean4.field06", "CommonBean4.field07",
				"CommonBean4.field08", "CommonBean4.field09",
				"CommonBean4.field10", "CommonBean4.field11",
				"CommonBean4.field12", "CommonBean4.field13",
				"CommonBean4.field14", "CommonBean4.field15",
				"CommonBean4.field16", "CommonBean4.field17",
				"CommonBean4.field18", "CommonBean4.field19",
				"CommonBean4.field20", "CommonBean4.field21",
				"CommonBean5.field01", "CommonBean5.field02",
				"CommonBean5.field03", "CommonBean5.field04",
				"CommonBean5.field05", "CommonBean5.field06",
				"CommonBean5.field07", "CommonBean5.field08",
				"CommonBean5.field09", "CommonBean5.field10",
				"CommonBean5.field11", "CommonBean5.field12",
				"CommonBean5.field13", "CommonBean5.field14",
				"CommonBean5.field15", "CommonBean5.field16",
				"CommonBean5.field17", "CommonBean5.field18",
				"CommonBean5.field19", "CommonBean5.field20",
				"CommonBean5.field21", "CommonBean5.field22",
				"CommonBean5.field23", "CommonBean5.field24",
				"CommonBean5.field25", "CommonBean5.field26",
				"CommonBean5.field27", "CommonBean5.field28",
				"CommonBean5.field29", "CommonBean5.field30",
				"CommonBean5.field31", "CommonBean5.field32",
				"CommonBean5.field33", "CommonBean5.field34",
				"CommonBean5.field35", "CommonBean5.field36",
				"CommonBean5.field37", "CommonBean5.field38",
				"CommonBean5.field39", "CommonBean5.field40",
				"CommonBean5.field41", "CommonBean5.field42",
				"CommonBean5.field43", "CommonBean5.field44",
				"CommonBean5.field45", "CommonBean5.field46",
				"CommonBean5.field47", "CommonBean5.field48",
				"CommonBean5.field49", "CommonBean5.field50",
				"CommonBean5.field51", "CommonBean5.field52",
				"CommonBean8.field01", "CommonBean8.field02",
				"CommonBean8.field03", "CommonBean8.field04",
				"CommonBean8.field05", "CommonBean8.field06",
				"CommonBean8.field07", "CommonBean8.field08",
				"CommonBean8.field09", "CommonBean8.field10",
				"CommonBean8.field11", "CommonBean8.field12",
				"CommonBean8.field13", "CommonBean8.field14",
				"CommonBean8.field15", "CommonBean8.field16",
				"CommonBean8.field17", "CommonBean8.field18",
				"CommonBean8.field19", "CommonBean8.field20",
				"CommonBean8.field21", "CommonBean8.field22",
				"CommonBean8.field23", "CommonBean8.field24",
				"CommonBean8.field25", "CommonBean8.field26",
				"CommonBean8.field27", "CommonBean8.field28",
				"CommonBean8.field29", "CommonBean8.field30",
				"CommonBean8.field31", "CommonBean8.field32",
				"CommonBean8.field33", "CommonBean8.field34",
				"CommonBean8.field35", "CommonBean8.field36",
				"CommonBean8.field37", "CommonBean8.field38",
				"CommonBean8.field39", "CommonBean8.field40",
				"CommonBean8.field41", "CommonBean8.field42",
				"CommonBean6.field01", "CommonBean6.field02",
				"CommonBean6.field03", "CommonBean6.field04",
				"CommonBean6.field05", "CommonBean6.field06",
				"CommonBean6.field07", "CommonBean6.field08",
				"CommonBean6.field09", "CommonBean6.field10",
				"CommonBean6.field11", "CommonBean6.field12",
				"CommonBean6.field13", "CommonBean6.field14",
				"CommonBean6.field15", "CommonBean6.field16",
				"CommonBean6.field17", "CommonBean6.field18",
				"CommonBean6.field19" };
		Map<String, String> map = new TreeMap<String, String>();
		for (int i = 0; i < slist.size(); i++) {
			String s = slist.get(i);
			map.put(columns[i], s);
		}
		if (slist.size() == 0) {
			map = new TreeMap<String, String>();
			list.add(map);
		} else {
			list.add(map);
		}
		return list;
	}

	private String bigDecimal2String(Object oo) {
		if (oo instanceof BigDecimal) {
			return CapMath.bigDecimalToString((BigDecimal) oo);
		}
		return CapConstants.EMPTY_STRING;
	}

	public OutputStream genCES1405R91(PageParameters params, String mainId,
			Locale locale, Properties prop) throws FileNotFoundException,
			IOException, Exception {
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		List<Map<String, String>> list = new LinkedList<Map<String, String>>();
		ReportGenerator generator = new ReportGenerator(
				"report/lms/CES1405R91_" + locale.toString() + ".rpt");
		OutputStream outputStream = null;
		C140M01A meta = null;
		try {
			meta = SimpleContextHolder.get(C140M01A);
			if (meta == null) {
				meta = service1205.getC140M01AByMainId(mainId);
			}
			if (meta != null) {
				rptVariableMap.put("randomCode", meta.getRandomCode());
				rptVariableMap.put("To_m4", meta.getToM4());
				rptVariableMap.put("typem4", meta.getTypem4());

				NumericFormatter nf = new NumericFormatter();
				try {
					C140JSON c140json = service1205.getC140JSONByMainPidTab(
							meta.getUid(), meta.getMainId(), "91", "1");
					if (c140json != null && c140json.getFlag() == null) {
						JSONObject data = JSONObject.fromObject(c140json
								.getJsonOb());

						rptVariableMap.put("GroupCompanyID1",
								data.getString("GroupCompanyID1"));
						rptVariableMap.put("GroupCompanyName1",
								data.getString("GroupCompanyName1"));
						rptVariableMap.put("curr5", data.getString("curr5"));
						rptVariableMap.put("unit", data.getString("unit"));
					}

					List<C140S09A> c140s09as = meta.getC140s09as();
					List<List<String>> detail = new ArrayList<List<String>>();
					int i = 0;

					ArrayList<String> tot1_1 = new ArrayList<String>();
					ArrayList<String> tot1_2 = new ArrayList<String>();
					ArrayList<String> tot1_3 = new ArrayList<String>();
					ArrayList<String> tot1_4 = new ArrayList<String>();
					ArrayList<String> tot4_3 = new ArrayList<String>();
					ArrayList<String> tot4_4 = new ArrayList<String>();

					for (C140S09A c140s09a : c140s09as) {
						List<String> detail1 = new ArrayList<String>();

						String GMbkAmt = c140s09a.getGMbkAmt() != null ? c140s09a
								.getGMbkAmt().toPlainString() : "";
						String GObuAmt = c140s09a.getGObuAmt() != null ? c140s09a
								.getGObuAmt().toPlainString() : "";
						String GOvsAmt = c140s09a.getGOvsAmt() != null ? c140s09a
								.getGOvsAmt().toPlainString() : "";
						String GMbkBal = c140s09a.getGMbkBal() != null ? c140s09a
								.getGMbkBal().toPlainString() : "";
						String GObuBal = c140s09a.getGObuBal() != null ? c140s09a
								.getGObuBal().toPlainString() : "";
						String GOvsBal = c140s09a.getGOvsBal() != null ? c140s09a
								.getGOvsBal().toPlainString() : "";

						tot1_1.add(CapMath.bigDecimalToString(c140s09a
								.getGAbkAmt()));
						tot1_2.add(CapMath.bigDecimalToString(c140s09a
								.getGAbkBal()));
						tot1_3.add(CapMath
								.add(new String[] { GMbkAmt, GObuAmt }));
						tot1_4.add(CapMath
								.add(new String[] { GMbkBal, GObuBal }));
						tot4_3.add(GOvsAmt);
						tot4_4.add(GOvsBal);

						if (++i > Ch9_Rd3_MaxNum) {
							continue;
						}

						detail1.add(c140s09a.getGNa());
						detail1.add(c140s09a.getGHolder());
						detail1.add(nf.reformat(c140s09a.getGAbkAmt()));
						detail1.add(nf.reformat(c140s09a.getGAbkBal()));
						detail1.add(nf.reformat(CapMath.add(new String[] {
								GMbkAmt, GObuAmt, GOvsAmt })));
						detail1.add(nf.reformat(CapMath.add(new String[] {
								GMbkBal, GObuBal, GOvsBal })));

						detail.add(detail1);
					}

					for (List<String> lists : detail) {
						// 主Grid的筆數
						if (!lists.isEmpty()) {
							list = setCes1405r91Data(list, lists);
						}
					}

					rptVariableMap.put("tot1_1", nf.reformat(CapMath.add(tot1_1
							.toArray(new String[] {}))));
					rptVariableMap.put("tot1_2", nf.reformat(CapMath.add(tot1_2
							.toArray(new String[] {}))));
					rptVariableMap.put("tot1_3", nf.reformat(CapMath.add(tot1_3
							.toArray(new String[] {}))));
					rptVariableMap.put("tot1_4", nf.reformat(CapMath.add(tot1_4
							.toArray(new String[] {}))));
					rptVariableMap.put(
							"tot2_1",
							"0".equals(rptVariableMap.get("tot1_1")) ? "0"
									: CapMath.multiply(CapMath.divide(
											(String) rptVariableMap
													.get("tot1_3"),
											(String) rptVariableMap
													.get("tot1_1"), 4), "100",
											2));
					rptVariableMap.put(
							"tot2_2",
							"0".equals(rptVariableMap.get("tot1_2")) ? "0"
									: CapMath.multiply(CapMath.divide(
											(String) rptVariableMap
													.get("tot1_4"),
											(String) rptVariableMap
													.get("tot1_2"), 4), "100",
											2));
					rptVariableMap.put("tot4_1", "N.A.");
					rptVariableMap.put("tot4_2", "N.A.");
					rptVariableMap.put("tot4_3", nf.reformat(CapMath.add(tot4_3
							.toArray(new String[] {}))));
					rptVariableMap.put("tot4_4", nf.reformat(CapMath.add(tot4_4
							.toArray(new String[] {}))));

				} catch (Exception e) {
					if (LOGGER.isErrorEnabled()) {
						LOGGER.error(e.getMessage(), e);
					}
				}

				String[] rSDSC = new String[] { "gcom_note1", "gcom_SrcDate" };
				for (String field : rSDSC) {
					C140SDSC c140sdsc = service1205.getC140SDSCByMainPidTab(
							meta.getUid(), meta.getMainId(), "91", field);
					if (c140sdsc != null) {
						rptVariableMap.put(c140sdsc.getFieldId(),
								c140sdsc.getVal());
					}
				}
			}
			generator.setLang(locale);
			rptVariableMap.put("pcType", space);
			generator.setVariableData(rptVariableMap);
			generator.setRowsData(list);
			// generator.checkVariableExist("C:/test.txt", rptVariableMap);
			outputStream = generator.generateReport();
		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
		}

		return outputStream;
	}

	private List<Map<String, String>> setCes1405r91Data(
			List<Map<String, String>> list, List<String> slist) {
		String[] columns = { "CommonBean1.field01", "CommonBean1.field02",
				"CommonBean1.field03", "CommonBean1.field04",
				"CommonBean1.field05", "CommonBean1.field06" };
		Map<String, String> map = new TreeMap<String, String>();
		for (int i = 0; i < slist.size(); i++) {
			String s = slist.get(i);
			map.put(columns[i], s);
		}
		if (slist.size() == 0) {
			map = new TreeMap<String, String>();
			list.add(map);
		} else {
			list.add(map);
		}
		return list;
	}

	/**
	 * 塞入變數MAP資料使用(L120M01A)
	 * 
	 * @param rptVariableMap
	 *            存放變數MAP
	 * @param l120m01a
	 *            L120M01A資料
	 * @return Map<String,String> rptVariableMap
	 */
	private Map<String, String> setL120S04AData(
			Map<String, String> rptVariableMap, List<L120S04A> l120s04aList) {

		rptVariableMap.put("L120S04A.QUERYDATES", "");
		rptVariableMap.put("L120S04A.QUERYDATEE", "");
		rptVariableMap.put("L120S04A.MEMO", "");
		StringBuilder strMemo = new StringBuilder();
		strMemo.setLength(0);
		for (L120S04A l120s04a : l120s04aList) {
			if (l120s04a.getQueryDateS() != null) {
				rptVariableMap.put("L120S04A.QUERYDATES",
						this.getYMDate(l120s04a.getQueryDateS()));
			}
			if (l120s04a.getQueryDateE() != null) {
				rptVariableMap.put("L120S04A.QUERYDATEE",
						this.getYMDate(l120s04a.getQueryDateE()));
			}
			// if (Util.isNotEmpty(l120s04a.getMemo())) {
			// // 開始進行合併
			// strMemo.append((strMemo.length() > 0) ? "<BR/>" : "").append(
			// Util.trim(l120s04a.getMemo()));
			// }
		}
		// if (strMemo.length() > 0) {
		// // 將合併後的說明設值進去
		// rptVariableMap.put("L120S04A.MEMO", strMemo.toString());
		// }
		return rptVariableMap;
	}

	/**
	 * 塞入變數MAP資料使用(L120S01B)
	 * 
	 * @param rptVariableMap
	 *            存放變數MAP
	 * @param l120s01b
	 *            L120S01B資料
	 * @return Map<String,String> rptVariableMap
	 */
	private Map<String, String> setL120S01B2Data(
			Map<String, String> rptVariableMap, List<L120S01B> l120s01bList,
			L120M01A l120m01a) {
		rptVariableMap.put("L120S01B.CHAIRMAN", "");
		rptVariableMap.put("L120S01B.GROUPNO", "");
		rptVariableMap.put("L120S01B.GROUPNAME", "");
		rptVariableMap.put("L120S01B.CHAIRMANID", "");
		rptVariableMap.put("L120S01B.CHAIRMANDUPNO", "");
		for (L120S01B l120s01b : l120s01bList) {
			if (Util.nullToSpace(l120m01a.getCustId()).equals(
					l120s01b.getCustId())
					&& Util.nullToSpace(l120m01a.getDupNo()).equals(
							l120s01b.getDupNo())) {
				rptVariableMap.put("L120S01B.CHAIRMAN",
						Util.nullToSpace(l120s01b.getChairman()));
				rptVariableMap
						.put("L120S01B.GROUPNO", ("N.A.".equals(Util
								.nullToSpace(l120s01b.getGroupNo())) ? "N.A."
								: Util.nullToSpace(l120s01b.getGroupNo())));
				rptVariableMap.put(
						"L120S01B.GROUPNAME",
						Util.nullToSpace(l120s01b.getGroupNo())
								+ ("N.A.".equals(Util.nullToSpace(l120s01b
										.getGroupName())) ? "N.A." : Util
										.nullToSpace(l120s01b.getGroupName())));
				rptVariableMap.put("L120S01B.CHAIRMANID",
						Util.nullToSpace(l120s01b.getChairmanId()));
				rptVariableMap.put("L120S01B.CHAIRMANDUPNO",
						Util.nullToSpace(l120s01b.getChairmanDupNo()));
			}
		}
		return rptVariableMap;
	}

	/**
	 * 塞入變數MAP資料使用(L120M01A)
	 * 
	 * @param rptVariableMap
	 *            存放變數MAP
	 * @param l120m01a
	 *            L120M01A資料
	 * @param typCdMap
	 *            typCdMap
	 * @param caseLvlMap
	 *            caseLvlMap
	 * @param prop
	 *            prop
	 * @return rptVariableMap 存放變數MAP
	 */
	private Map<String, String> setL120M01AData(
			Map<String, String> rptVariableMap, L120M01A l120m01a,
			Map<String, String> typCdMap, Map<String, String> caseLvlMap,
			Properties prop) {
		if (l120m01a == null) {
			l120m01a = new L120M01A();
		}
		rptVariableMap.put("L120M01A.CASENO",
				Util.nullToSpace(l120m01a.getCaseNo()));
		rptVariableMap.put("L120M01A.TYPCD",
				Util.nullToSpace(typCdMap.get(l120m01a.getTypCd())));
		rptVariableMap.put("L120M01A.CASEBRID",
				Util.nullToSpace(l120m01a.getCaseBrId()));
		rptVariableMap.put("L120M01A.CASEDATE",
				Util.nullToSpace(TWNDate.toAD(l120m01a.getCaseDate())));
		rptVariableMap.put("L120M01A.GIST",
				Util.nullToSpace(l120m01a.getGist()));
		rptVariableMap.put("L120M01A.ITEMOFBUSI",
				Util.nullToSpace(l120m01a.getItemOfBusi()));
		rptVariableMap.put("L120M01A.CESCUSTID",
				Util.nullToSpace(l120m01a.getCesCustId()));
		rptVariableMap.put("L120M01A.CESDUPNO",
				Util.nullToSpace(l120m01a.getCesDupNo()));
		rptVariableMap.put("L120M01A.CUSTID",
				Util.nullToSpace(l120m01a.getCustId()));
		rptVariableMap.put("L120M01A.DUPNO",
				Util.nullToSpace(l120m01a.getDupNo()));
		rptVariableMap.put("L120M01A.CUSTNAME",
				Util.nullToSpace(l120m01a.getCustName()));
		rptVariableMap.put("L120M01A.RANDOMCODE",
				Util.nullToSpace(l120m01a.getRandomCode()));
		rptVariableMap.put("L120M01A.PURPOSE",
				this.getPurpose(Util.nullToSpace(l120m01a.getPurpose()), prop));
		rptVariableMap.put("L120M01A.PURPOSEOTH",
				Util.nullToSpace(l120m01a.getPurposeOth()));
		rptVariableMap.put("L120M01A.RESOURCE", this.getResource(
				Util.nullToSpace(l120m01a.getResource()), prop));
		rptVariableMap.put("L120M01A.RESOURCEOTH",
				Util.nullToSpace(l120m01a.getResourceOth()));
		rptVariableMap.put("L120M01A.LONGCASEFLAG",
				Util.nullToSpace(l120m01a.getLongCaseFlag()));
		rptVariableMap.put(
				"L120M01A.CASELVL",
				!"".equals(Util.nullToSpace(l120m01a.getCaseLvl())
						.replace("　", "").replace(" ", "").trim()) ? "("
						+ Util.nullToSpace(caseLvlMap.get(Util
								.nullToSpace(l120m01a.getCaseLvl())
								.replace("　", " ").trim())) + ")" : "");
		if ("2".equals(Util.nullToSpace(l120m01a.getDocCode()))) {
			rptVariableMap.put("L120M01A.DOCCODENAME",
					Util.nullToSpace(prop.getProperty("L120M01A.DOCCODE2")));
		} else if ("3".equals(Util.nullToSpace(l120m01a.getDocCode()))) {
			rptVariableMap.put("L120M01A.DOCCODENAME",
					Util.nullToSpace(prop.getProperty("L120M01A.DOCCODE3")));
		} else {
			rptVariableMap.put("L120M01A.DOCCODENAME", "");
		}
		rptVariableMap.put("L120M01A.APPROVETIME",
				Util.nullToSpace(TWNDate.toAD(l120m01a.getApproveTime())));
		rptVariableMap.put("L120M01A.RPTTITLEAREA1",
				Util.nullToSpace(l120m01a.getRptTitleArea1()));
		rptVariableMap
				.put("L120M01A.RPTTITLE1", this.formatRptTitle1(Util
						.nullToSpace(l120m01a.getRptTitle1())));
		rptVariableMap.put("L120M01A.RPTTITLE2",
				Util.nullToSpace(l120m01a.getRptTitle2()));
		rptVariableMap.put("L120M01A.CUSTID",
				Util.nullToSpace(l120m01a.getCustId()));
		rptVariableMap.put("L120M01A.DUPNO",
				Util.nullToSpace(l120m01a.getDupNo()));
		rptVariableMap.put("L120M01A.CUSTNAME",
				Util.nullToSpace(l120m01a.getCustName()));
		rptVariableMap.put("L120M01A.CASEBRID",
				Util.nullToSpace(l120m01a.getCaseBrId()));
		rptVariableMap.put("L120M01A.CASENO",
				Util.nullToSpace(l120m01a.getCaseNo()));
		return rptVariableMap;
	}

	/**
	 * 塞入變數MAP資料使用(L120S01C)
	 * 
	 * @param rptVariableMap
	 *            存放變數MAP
	 * @param l120s01a
	 *            L120S01A的資料
	 * @param l120s01cList
	 *            LIST<L120S01C>的資料
	 * @param crdTypeMap
	 *            bcodetype的crdType
	 * @return Map<String,String> rptVariableMap
	 */
	@SuppressWarnings("unused")
	private Map<String, String> setL120S01CData(
			Map<String, String> rptVariableMap, L120S01A l120s01a,
			List<L120S01C> l120s01cList, Map<String, String> crdTypeMap,
			Properties prop) {
		StringBuffer str1 = new StringBuffer();
		StringBuffer str2 = new StringBuffer();
		StringBuffer str3 = new StringBuffer();
		StringBuffer str4 = new StringBuffer();
		// 免辦
		boolean noResult = false;
		boolean naResult = false;
		StringBuffer tempGrade = new StringBuffer();
		for (L120S01C l120s01c : l120s01cList) {
			if (Util.nullToSpace(l120s01a.getCustId()).equals(
					Util.nullToSpace(l120s01c.getCustId()))
					&& Util.nullToSpace(l120s01a.getDupNo()).equals(
							Util.nullToSpace(l120s01c.getDupNo()))) {
				String crdType = Util.trim(l120s01c.getCrdType());
				String grade = Util.trim(l120s01c.getGrade());
				tempGrade.setLength(0);
				if ("NA".equals(crdType)) {
					naResult = true;
					// str.append(prop.getProperty("L120S01C.CRDTITLE01"))
					// .append(prop.getProperty("L120S05A.GRPGRRDN"))
					// .append("、");
				} else if ("DB".equals(crdType) || "DL".equals(crdType)
						|| "OU".equals(crdType)) {
					if (str3.length() != 0) {
						str3.append("、");
					}
					str3.append(prop.getProperty("L120S01C.CRDTITLE01"))
							.append(grade)
							.append("【")
							.append(prop.getProperty("L120S01C.CRDTITLE02"))
							.append(Util.nullToSpace(TWNDate.toAD(l120s01c
									.getCrdTYear())))
							.append(" ")
							.append(prop.getProperty("L120S01C.CRDTITLE03"))
							.append(" ")
							.append(l120s01c.getCrdTBR())
							.append(" ")
							.append(Util.nullToSpace(branch.getBranchName(Util
									.nullToSpace(l120s01c.getCrdTBR()))))
							.append("】");
				} else if ("NO".equals(crdType)) {
					noResult = true;
					// str.append(prop.getProperty("L120S01C.CRDTITLE04"))
					// .append(prop.getProperty("L120S01C.NOCRD01"))
					// .append("、");
				} else if ("M".equals(Util.getLeftStr(crdType, 1))) {

					if (Util.isNumeric(grade)) {
						tempGrade.append(grade)
								.append(prop.getProperty("tempGrade"))
								.append(" ");
					}

					// 取得MOW等級之說明
					tempGrade.append(lmsService.getMowGradeName(prop, crdType,
							grade));

					if (str2.length() != 0) {
						str2.append("、");
					}
					str2.append(Util.nullToSpace(crdTypeMap.get(crdType)))
							.append(" : ")
							.append(tempGrade.toString())
							.append("【")
							.append(prop.getProperty("L120S01C.CRDTITLE02"))
							.append(Util.nullToSpace(TWNDate.toAD(l120s01c
									.getCrdTYear())))
							.append(" ")
							.append(prop.getProperty("L120S01C.CRDTITLE03"))
							.append(" ")
							.append(l120s01c.getCrdTBR())
							.append(" ")
							.append(Util.nullToSpace(branch.getBranchName(Util
									.nullToSpace(l120s01c.getCrdTBR()))))
							.append("】");
				} else if (Casedoc.CrdType.MOODY.equals(crdType)
						|| Casedoc.CrdType.SAndP.equals(crdType)
						|| Casedoc.CrdType.Fitch.equals(crdType)
						|| Casedoc.CrdType.FitchTW.equals(crdType)
						|| Casedoc.CrdType.KBRA.equals(crdType)) {
					// J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等
					if (str1.length() != 0) {
						str1.append("、");
					}
					str1.append(grade)
							.append("【")
							.append(prop.getProperty("L120S01C.CRDTITLE02"))
							.append(Util.nullToSpace(TWNDate.toAD(l120s01c
									.getCrdTYear())))
							.append(" ")
							.append(prop.getProperty("L120S01C.CRDTITLE03"))
							.append(Util.nullToSpace(crdTypeMap.get(l120s01c
									.getCrdType()))).append("】");
				} else if (crdType.startsWith("C")
						&& Util.notEquals(crdType, "CS")) {
					if (str4.length() != 0) {
						str4.append("、");
					}
					str4.append(Util.nullToSpace(crdTypeMap.get(crdType)))
							.append(" : ")
							.append(grade)
							.append("【")
							.append(prop.getProperty("L120S01C.CRDTITLE02"))
							.append(Util.nullToSpace(TWNDate.toAD(l120s01c
									.getCrdTYear())))
							.append(" ")
							.append(prop.getProperty("L120S01C.CRDTITLE03"))
							.append(" ")
							.append(l120s01c.getCrdTBR())
							.append(" ")
							.append(Util.nullToSpace(branch.getBranchName(Util
									.nullToSpace(l120s01c.getCrdTBR()))))
							.append("】");
				}
			}
		}

		/*
		 * 狀況1:MX+NA 狀況2:DX+NO 狀況3:NA+NO 狀況4:空 最後在加外部NM,NS,NP
		 */
		// 外部評等一定要串
		boolean result = false;
		rptVariableMap.put("L120S01C.CRD", "");
		StringBuffer total = new StringBuffer();
		// L120S01C.CRDTITLE04=模型評等 :
		if (str2.length() > 0) {

			// MXXX+外部
			// rptVariableMap.put("L120S01C.CRD",str2.toString());
			total.append(prop.getProperty("L120S01C.CRDTITLE04") + " " + str2);
			result = true;
		}
		// L120S01C.CRDTITLE01=信用評等 :
		if (str3.length() > 0) {
			// DXXX+外部
			total.append(total.length() > 0 ? "\r" : "");
			total.append(str3.toString());
			// rptVariableMap.put("L120S01C.CRD",str3.toString() + " " +
			// prop.getProperty("L120S01C.CRDTITLE04"));
			result = true;
		}

		// L120S01C.CRDTITLE05=外部評等 :
		if (str1.length() > 0) {
			total.append(total.length() > 0 ? "\r" : "");
			total.append(prop.getProperty("L120S01C.CRDTITLE05")
					+ str1.toString());
		}
		if (total.length() == 0) {
			// rptVariableMap.put("L120S01C.CRD",prop.getProperty("L120S01C.NOCRD01"));
			total.append(prop.getProperty("L120S01C.NOCRD01"));
			result = true;
		}
		// rptVariableMap.put("L120S01C.CRD",(!result ? "" :
		// (rptVariableMap.get("L120S01C.CRD") + "\n"))+crdtitle05 +
		// str1.toString());
		rptVariableMap.put("L120S01C.CRD", total.toString());
		return rptVariableMap;
	}

	/**
	 * 塞入多值欄位LIST資料使用(L120S01E)
	 * 
	 * @param l120s04aList
	 *            List<L120S04A>資料資料
	 * @param titleRows
	 *            titleRows
	 * @param prop
	 *            prop
	 * @param yesNoMap
	 *            yesNoMap
	 * @return Map<String,String> rptVariableMap
	 */
	private List<Map<String, String>> setL120S04AListData(
			List<L120S04A> l120s04aList, List<Map<String, String>> titleRows,
			Properties prop, Map<String, String> yesNoMap) {
		Map<String, String> map = null;
		int count = 0;
		boolean result = false;
		StringBuffer str = new StringBuffer();
		int countStr = 0;
		for (L120S04A l120s04a : l120s04aList) {
			if (countStr == 5) {
				countStr = 0;
				str.setLength(0);
			}
			countStr++;
			int number = count % 5;
			if (number == 0) {
				result = true;
				map = Util.setColumnMap();
				// if(count == 0){
				// map.put("ReportBean.column" + String.format("%02d", 171),
				// "N");
				// }else{
				// map.put("ReportBean.column" + String.format("%02d", 171),
				// "Y");
				// }
			}
			map.put("ReportBean.column"
					+ String.format("%02d", 1 + 34 * number),
					l120s04a.getCustName());
			map.put("ReportBean.column"
					+ String.format("%02d", 2 + 34 * number),
					Util.nullToSpace(l120s04a.getCustRelation())
							.replace(",", "/")
							.replace("|", "/")
							.replace("1",
									prop.getProperty("L120S04A.CUSTRELATION1"))
							.replace("2",
									prop.getProperty("L120S04A.CUSTRELATION2"))
							.replace("3", "")
							.replace("4", "")
							.replace("5",
									prop.getProperty("L120S04A.CUSTRELATION5"))
							.replace("6",
									prop.getProperty("L120S04A.CUSTRELATION6")));
			map.put("ReportBean.column"
					+ String.format("%02d", 3 + 34 * number),
					NumConverter.addComma(l120s04a.getDepTime()));
			map.put("ReportBean.column"
					+ String.format("%02d", 4 + 34 * number),
					NumConverter.addComma(l120s04a.getDepFixed()));
			map.put("ReportBean.column"
					+ String.format("%02d", 5 + 34 * number),
					NumConverter.addComma(l120s04a.getLoanQuota()));

			// M-104-0172-001 二維表收信新增AR買方額度餘額
			map.put("ReportBean.column" + String.format("%02d", 175 + number),
					NumConverter.addComma(l120s04a.getRcvBuyFactAmt()));

			map.put("ReportBean.column"
					+ String.format("%02d", 6 + 34 * number),
					NumConverter.addComma(l120s04a.getLoanAvgBal()));

			// M-104-0172-001 二維表收信新增AR買方額度餘額
			map.put("ReportBean.column" + String.format("%02d", 180 + number),
					NumConverter.addComma(l120s04a.getRcvBuyAvgBal()));

			map.put("ReportBean.column"
					+ String.format("%02d", 7 + 34 * number),
					NumConverter.addComma(l120s04a.getLoanAvgRate()));
			map.put("ReportBean.column"
					+ String.format("%02d", 8 + 34 * number),
					NumConverter.addComma(l120s04a.getExchgImpRec()) + "/"
							+ "<br>"
							+ NumConverter.addComma(l120s04a.getExchgImpAmt()));
			map.put("ReportBean.column"
					+ String.format("%02d", 9 + 34 * number),
					NumConverter.addComma(l120s04a.getExchgExpRec()) + "/"
							+ "<br>"
							+ NumConverter.addComma(l120s04a.getExchgExpAmt()));
			map.put("ReportBean.column"
					+ String.format("%02d", 10 + 34 * number),
					NumConverter.addComma(l120s04a.getExchgOutRec()) + "/"
							+ "<br>"
							+ NumConverter.addComma(l120s04a.getExchgOutAmt()));
			map.put("ReportBean.column"
					+ String.format("%02d", 11 + 34 * number),
					NumConverter.addComma(l120s04a.getExchgInRec()) + "/"
							+ "<br>"
							+ NumConverter.addComma(l120s04a.getExchgInAmt()));
			map.put("ReportBean.column"
					+ String.format("%02d", 12 + 34 * number),
					NumConverter.addComma(l120s04a.getDerOption()));
			map.put("ReportBean.column"
					+ String.format("%02d", 13 + 34 * number),
					NumConverter.addComma(l120s04a.getDerRateExchg()));
			map.put("ReportBean.column"
					+ String.format("%02d", 14 + 34 * number),
					NumConverter.addComma(l120s04a.getDerCCS()));
			map.put("ReportBean.column"
					+ String.format("%02d", 15 + 34 * number),
					NumConverter.addComma(l120s04a.getDerSWAP()));
			map.put("ReportBean.column"
					+ String.format("%02d", 16 + 34 * number),
					NumConverter.addComma(l120s04a.getTrustBond()));
			map.put("ReportBean.column"
					+ String.format("%02d", 17 + 34 * number),
					NumConverter.addComma(l120s04a.getTrustFund()));
			map.put("ReportBean.column"
					+ String.format("%02d", 18 + 34 * number),
					NumConverter.addComma(l120s04a.getTrustSetAcct()));
			map.put("ReportBean.column"
					+ String.format("%02d", 19 + 34 * number),
					NumConverter.addComma(l120s04a.getTrustOther()));
			map.put("ReportBean.column"
					+ String.format("%02d", 20 + 34 * number),
					NumConverter.addComma(l120s04a.getWealthTrust()));
			map.put("ReportBean.column"
					+ String.format("%02d", 21 + 34 * number),
					NumConverter.addComma(l120s04a.getWealthInsCom()));
			map.put("ReportBean.column"
					+ String.format("%02d", 22 + 34 * number),
					NumConverter.addComma(l120s04a.getWealthInvest()));
			map.put("ReportBean.column"
					+ String.format("%02d", 23 + 34 * number),
					NumConverter.addComma(l120s04a.getSalaryRec()));
			map.put("ReportBean.column"
					+ String.format("%02d", 24 + 34 * number),
					NumConverter.addComma(l120s04a.getSalaryFixed()));
			map.put("ReportBean.column"
					+ String.format("%02d", 25 + 34 * number),
					NumConverter.addComma(l120s04a.getSalaryMortgage())
							+ "/"
							+ NumConverter.addComma(l120s04a
									.getSalaryConsumption()));
			map.put("ReportBean.column"
					+ String.format("%02d", 26 + 34 * number),
					NumConverter.addComma(l120s04a.getSalaryCard()));
			map.put("ReportBean.column"
					+ String.format("%02d", 27 + 34 * number),
					NumConverter.addComma(l120s04a.getSalaryNetwork()));
			map.put("ReportBean.column"
					+ String.format("%02d", 28 + 34 * number),
					NumConverter.addComma(l120s04a.getCardCommercial()));
			map.put("ReportBean.column" + String.format("%02d", 170 + number),
					NumConverter.addComma(l120s04a.getCardNoneCommercial()));

			map.put("ReportBean.column"
					+ String.format("%02d", 29 + 34 * number),
					Util.nullToSpace(yesNoMap.get(l120s04a.getCardCoBranded())));
			map.put("ReportBean.column"
					+ String.format("%02d", 30 + 34 * number),
					NumConverter.addComma(l120s04a.getGEBTWDRec()));
			map.put("ReportBean.column"
					+ String.format("%02d", 31 + 34 * number),
					NumConverter.addComma(l120s04a.getGEBOTHRec()));
			map.put("ReportBean.column"
					+ String.format("%02d", 32 + 34 * number),
					NumConverter.addComma(l120s04a.getGEBLCRec()));
			map.put("ReportBean.column"
					+ String.format("%02d", 33 + 34 * number),
					NumConverter.addComma(l120s04a.getProfit())
							+ (l120s04a.getProfitSalary() == null ? "" : "\r"
									+ "("
									+ NumConverter.addComma(l120s04a
											.getProfitSalary()) + ")")
							+ (l120s04a.getProfitTrustFdta() == null ? ""
									: "\r"
											+ "("
											+ NumConverter.addComma(l120s04a
													.getProfitTrustFdta())
											+ ")"));
			// str.append(Util.nullToSpace(l120s04a.getMemo())).append("<BR/>");
			// map.put("ReportBean.column" + String.format("%02d", 34),
			// str.toString());

			if (Util.notEquals(Util.trim(l120s04a.getMemo()), "")) {
				str.append(Util.nullToSpace(l120s04a.getMemo()))
						.append("<BR/>");
			}

			if (countStr == 5 || (count + 1) == l120s04aList.size()) {
				map.put("ReportBean.column" + String.format("%02d", 34),
						str.toString());
			}

			// + 34 * number
			if (count != 0 && number == 4) {
				titleRows.add(map);
				result = false;
			}
			count++;
		}
		if (result) {
			titleRows.add(map);
		}
		if (count == 0) {
			map = Util.setColumnMap();
			titleRows.add(map);
		}
		return titleRows;
	}

	/**
	 * 取得借款用途
	 * 
	 * @param purpose
	 *            借款用途
	 * @return 借款用途
	 */
	private String getPurpose(String purpose, Properties prop) {
		StringBuffer str = new StringBuffer();
		if (purpose.indexOf("1") == -1) {
			str.append("□");
		} else {
			str.append("■");
		}
		str.append(prop.getProperty("L120M01A.PURPOSE1"));
		if (purpose.indexOf("2") == -1) {
			str.append("□");
		} else {
			str.append("■");
		}
		str.append(prop.getProperty("L120M01A.PURPOSE2"));
		if (purpose.indexOf("3") == -1) {
			str.append("□");
		} else {
			str.append("■");
		}
		str.append(prop.getProperty("L120M01A.PURPOSE3"));

		return str.toString();
	}

	/**
	 * 取得還款財源
	 * 
	 * @param resource
	 *            還款財源
	 * @return 還款財源
	 */
	private String getResource(String resource, Properties prop) {
		StringBuffer str = new StringBuffer();
		if (resource.indexOf("1") == -1) {
			str.append("□");
		} else {
			str.append("■");
		}
		str.append(prop.getProperty("L120M01A.RESOURCE1"));
		if (resource.indexOf("2") == -1) {
			str.append("□");
		} else {
			str.append("■");
		}
		str.append(prop.getProperty("L120M01A.RESOURCE2"));
		if (resource.indexOf("3") == -1) {
			str.append("□");
		} else {
			str.append("■");
		}
		str.append(prop.getProperty("L120M01A.RESOURCE3"));

		return str.toString();
	}

	/**
	 * 只取XXXX-XX顯示
	 * 
	 * @param date
	 *            日期
	 * @return String date
	 */
	private String getYMDate(Date date) {
		StringBuffer str = new StringBuffer();
		if (date != null) {
			str.append(TWNDate.toAD(date).subSequence(0, 4)).append("-")
					.append(TWNDate.toAD(date).substring(5, 7));
		}
		return str.toString();
	}

	// J-107-0007-001 Web e-Loan國內、海外授信簽報書第八章新增相同集團企業評等等級之新臺幣及美元放款利率資訊
	private Map<String, String> genGrpRateDataHtml(L120S04B l120s04b,
			String custId, String dupNo, Map<String, String> rptVariableMap)
			throws IOException, Exception {

		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1205S05Page06a.class);

		StringBuffer htmlBuf = new StringBuffer("");

		NumericFormatter df = new NumericFormatter("#0.0000");
		rptVariableMap.put("GRPRATEDATAHTML", "");
		rptVariableMap.put("GRPRATEDATATITLE", "");

		List<Map<String, Object>> rows3 = new ArrayList<Map<String, Object>>();

		try {
			rows3 = this.misGrpcmpService.findGrpcmpSelGrpdtl(custId, dupNo);
		} catch (DataAccessResourceFailureException e) {
			return rptVariableMap;
		}

		String grpGrrd = Util.trim(l120s04b.getGrpGrrd());
		Integer GRPYY = l120s04b.getGrpYear() == null ? Util.parseInt(CapDate
				.getCurrentDate("yyyy")) : l120s04b.getGrpYear();

		String queryGrpYy = new BigDecimal(Util.parseInt(GRPYY) - 1911)
				.toPlainString();
		String groupBadFlag = "";

		// 設定隸屬集團代碼與名稱 map3.get("BADFLAG")
		for (Map<String, Object> map3 : rows3) {
			groupBadFlag = Util.trim(Util.trim(MapUtils.getString(map3,
					"BADFLAG", "0")));
			if (Util.equals(groupBadFlag, "")) {
				groupBadFlag = "0";
			}
			break;
		}

		String grpNo = l120s04b.getGrpNo();
		Date cycMn = CapDate.getCurrentTimestamp();
		String grpGrade = l120s04b.getGrpGrrd();
		String badFg = groupBadFlag;
		String grpYear = Util.nullToSpace(l120s04b.getGrpYear());

		// 沒有集團就不要印
		// 財務危機集團不印
		// 問題集團
		if (!lmsService.isShowGrpRateData(grpNo, cycMn, grpGrade, badFg,
				grpYear)) {
			return rptVariableMap;
		}

		List<Map<String, Object>> grpRateList = dwdbService
				.findDW_DW_ROCGRPRT_By_BadFg_With_MaxCycMn(groupBadFlag);

		if (grpRateList == null || grpRateList.isEmpty()) {
			return rptVariableMap;
		}

		boolean isFirstLine = true;
		String rptTitleCycMn = "";
		for (Map<String, Object> grpRateMap : grpRateList) {

			if (isFirstLine) {

				htmlBuf.append("<html><head><meta http-equiv='Content-Type' content='text/html; charset=utf-8'><style>table, th {border-collapse: collapse;border-style: solid;border-width: 1px;}td {border-collapse: collapse;border-style: solid;border-width: 1px;padding-top: 2px;padding-right: 2px;padding-bottom: 2px;padding-left: 2px;}</style></head>");
				htmlBuf.append("<body>");

				if (Util.equals(groupBadFlag, "0")) {
					// l120s05a.grpRatePrintTitle1=評等第1~6級集團企業之新臺幣及美元放款利率資訊<br>(不含列管「應予注意」之集團企業)
					rptTitleCycMn = "<div style='text-align:center'>"
							+ prop.getProperty("l120s05a.grpRatePrintTitle1")
							+ "</div>";
				} else {
					// l120s05a.grpRatePrintTitle2=評等第1~6級列管為「應予注意」集團企業之新臺幣及美元放款利率資訊
					rptTitleCycMn = "<div style='text-align:center'>"
							+ prop.getProperty("l120s05a.grpRatePrintTitle2")
							+ "</div>";
				}

				htmlBuf.append("<div width='98%'>");
				// l120s05a.prnCycMn=資料基準日
				htmlBuf.append("<div style='text-align:right'>("
						+ prop.getProperty("l120s05a.prnCycMn")
						+ "："
						+ (Util.equals(
								MapUtils.getString(grpRateMap, "CYC_MN", ""),
								"") ? "" : CapDate.formatDate(CapDate
								.shiftDays(Util.parseDate(MapUtils.getString(
										grpRateMap, "CYC_MN")), -1),
								"yyyy-MM-dd")) + ")</div>");

				htmlBuf.append("<table width='100%'>");
				htmlBuf.append("   <tr style='text-align:center' >");

				// l120s05a.grpYy=集團企<br>業評等<br>
				// l120s05a.grpYyUnit=年度
				htmlBuf.append("      <td rowspan='3'>"
						+ prop.getProperty("l120s05a.grpYy") + "("
						+ l120s04b.getGrpYear()
						+ prop.getProperty("l120s05a.grpYyUnit") + ")</td>");
				htmlBuf.append("      <td rowspan='3' class='hd1'>");

				// l120s05a.cnt=集團<br>企業<br>家數
				htmlBuf.append(prop.getProperty("l120s05a.cnt"));
				htmlBuf.append("      </td>");
				htmlBuf.append("      <td colspan='6' class='hd1'>");
				// l120s05a.baseAvgRt=基準日加權<br>平均放款利率(%)
				htmlBuf.append(prop.getProperty("l120s05a.baseAvgRt"));
				htmlBuf.append("      </td>");
				htmlBuf.append("      <td colspan='4'  class='hd1'>");
				// l120s05a.baseMaxRt=基準日集團企業中<br>最高放款利率(%)
				htmlBuf.append(prop.getProperty("l120s05a.baseMaxRt"));
				htmlBuf.append("      </td>");
				htmlBuf.append("      <td colspan='4' class='hd1'>");
				// l120s05a.baseMinRt=基準日集團企業中<br>最低放款利率(%)
				htmlBuf.append(prop.getProperty("l120s05a.baseMinRt"));
				htmlBuf.append("      </td>");
				htmlBuf.append("   </tr>");
				htmlBuf.append("   <tr style='text-align:center ;' >");
				htmlBuf.append("      <td class='hd1' colspan='2'>");

				// l120s05a.avgRt=總平均<br>放款利率
				htmlBuf.append(prop.getProperty("l120s05a.avgRt"));
				htmlBuf.append("      </td>");
				htmlBuf.append("      <td class='hd1' colspan='2'>");
				// l120s05a.avgSRt=平均擔保<br>放款利率
				htmlBuf.append(prop.getProperty("l120s05a.avgSRt"));
				htmlBuf.append("      </td>");
				htmlBuf.append("      <td class='hd1' colspan='2'>");
				// l120s05a.avgNRt=平均無擔保<br>放款利率
				htmlBuf.append(prop.getProperty("l120s05a.avgNRt"));
				htmlBuf.append("      </td>");
				htmlBuf.append("      <td class='hd1' colspan='2'>");
				// l120s05a.SRt=擔保<br>放款利率
				htmlBuf.append(prop.getProperty("l120s05a.SRt"));
				htmlBuf.append("      </td>");
				htmlBuf.append("      <td class='hd1' colspan='2'>");
				// l120s05a.NRt=無擔保<br>放款利率
				htmlBuf.append(prop.getProperty("l120s05a.NRt"));
				htmlBuf.append("      </td>");
				htmlBuf.append("      <td class='hd1' colspan='2'>");
				// l120s05a.SRt=擔保<br>放款利率
				htmlBuf.append(prop.getProperty("l120s05a.SRt"));
				htmlBuf.append("      </td>");
				htmlBuf.append("      <td class='hd1' colspan='2'>");
				// l120s05a.NRt=無擔保<br>放款利率
				htmlBuf.append(prop.getProperty("l120s05a.NRt"));
				htmlBuf.append("      </td>");
				htmlBuf.append("   </tr>");
				htmlBuf.append("   <tr style='text-align:center ;' >");
				htmlBuf.append("      <td>");
				// l120s05a.twd=新臺幣
				htmlBuf.append(prop.getProperty("l120s05a.twd"));
				htmlBuf.append("      </td>");
				htmlBuf.append("      <td>");
				// l120s05a.usd=美金
				htmlBuf.append(prop.getProperty("l120s05a.usd"));
				htmlBuf.append("      </td>");
				htmlBuf.append("      <td>");
				// l120s05a.twd=新臺幣
				htmlBuf.append(prop.getProperty("l120s05a.twd"));
				htmlBuf.append("      </td>");
				htmlBuf.append("      <td>");
				htmlBuf.append("美金");
				htmlBuf.append("      </td>");
				htmlBuf.append("      <td>");
				// l120s05a.twd=新臺幣
				htmlBuf.append(prop.getProperty("l120s05a.twd"));
				htmlBuf.append("      </td>");
				htmlBuf.append("      <td>");
				// l120s05a.usd=美金
				htmlBuf.append(prop.getProperty("l120s05a.usd"));
				htmlBuf.append("      </td>");
				htmlBuf.append("      <td>");
				// l120s05a.twd=新臺幣
				htmlBuf.append(prop.getProperty("l120s05a.twd"));
				htmlBuf.append("      </td>");
				htmlBuf.append("      <td>");
				// l120s05a.usd=美金
				htmlBuf.append(prop.getProperty("l120s05a.usd"));
				htmlBuf.append("      </td>");
				htmlBuf.append("      <td>");
				// l120s05a.twd=新臺幣
				htmlBuf.append(prop.getProperty("l120s05a.twd"));
				htmlBuf.append("      </td>");
				htmlBuf.append("      <td>");
				// l120s05a.usd=美金
				htmlBuf.append(prop.getProperty("l120s05a.usd"));
				htmlBuf.append("      </td>");
				htmlBuf.append("      <td>");
				// l120s05a.twd=新臺幣
				htmlBuf.append(prop.getProperty("l120s05a.twd"));
				htmlBuf.append("      </td>");
				htmlBuf.append("      <td>");
				// l120s05a.usd=美金
				htmlBuf.append(prop.getProperty("l120s05a.usd"));
				htmlBuf.append("      </td>");
				htmlBuf.append("      <td>");
				// l120s05a.twd=新臺幣
				htmlBuf.append(prop.getProperty("l120s05a.twd"));
				htmlBuf.append("      </td>");
				htmlBuf.append("      <td>");
				// l120s05a.usd=美金
				htmlBuf.append(prop.getProperty("l120s05a.usd"));
				htmlBuf.append("      </td>");
				htmlBuf.append("   </tr>");

			}

			htmlBuf.append("   <tr >");
			htmlBuf.append("      <td style='text-align:center ;' >");

			// l120s05a.theFirst=第
			// l120s05a.grpGrade=級
			// l120s05a.prnMemo1=註1
			htmlBuf.append(prop.getProperty("l120s05a.theFirst")
					+ MapUtils.getString(grpRateMap, "GRPGRADE", "")
					+ prop.getProperty("l120s05a.grpGrade")
					+ (Util.equals(Util.trim(MapUtils.getString(grpRateMap,
							"GRPGRADE", "")), "6") ? "("
							+ prop.getProperty("l120s05a.prnMemo1") + ")" : "")
					+ "<br>");

			htmlBuf.append("      </td>");
			htmlBuf.append("      <td style='text-align:right ;' >");
			htmlBuf.append(MapUtils.getString(grpRateMap, "CNT", ""));
			htmlBuf.append("      </td>");

			htmlBuf.append("	  <td style='text-align:right ;' >");
			htmlBuf.append(Util.equals(
					MapUtils.getString(grpRateMap, "AVG_TWD_RT", ""), "") ? ""
					: df.reformat(new BigDecimal(MapUtils.getString(grpRateMap,
							"AVG_TWD_RT", null))));
			htmlBuf.append("      </td>");

			htmlBuf.append("	  <td style='text-align:right ;' >");
			htmlBuf.append(Util.equals(
					MapUtils.getString(grpRateMap, "AVG_USD_RT", ""), "") ? ""
					: df.reformat(new BigDecimal(MapUtils.getString(grpRateMap,
							"AVG_USD_RT", null))));
			htmlBuf.append("      </td>");

			htmlBuf.append("	  <td style='text-align:right ;' >");
			htmlBuf.append(Util.equals(
					MapUtils.getString(grpRateMap, "AVG_TWD_S_RT", ""), "") ? ""
					: df.reformat(new BigDecimal(MapUtils.getString(grpRateMap,
							"AVG_TWD_S_RT", null))));
			htmlBuf.append("      </td>");

			htmlBuf.append("	  <td style='text-align:right ;' >");
			htmlBuf.append(Util.equals(
					MapUtils.getString(grpRateMap, "AVG_USD_S_RT", ""), "") ? ""
					: df.reformat(new BigDecimal(MapUtils.getString(grpRateMap,
							"AVG_USD_S_RT", null))));
			htmlBuf.append("      </td>");

			htmlBuf.append("	  <td style='text-align:right ;' >");
			htmlBuf.append(Util.equals(
					MapUtils.getString(grpRateMap, "AVG_TWD_N_RT", ""), "") ? ""
					: df.reformat(new BigDecimal(MapUtils.getString(grpRateMap,
							"AVG_TWD_N_RT", null))));
			htmlBuf.append("      </td>");

			htmlBuf.append("	  <td style='text-align:right ;' >");
			htmlBuf.append(Util.equals(
					MapUtils.getString(grpRateMap, "AVG_USD_N_RT", ""), "") ? ""
					: df.reformat(new BigDecimal(MapUtils.getString(grpRateMap,
							"AVG_USD_N_RT", null))));
			htmlBuf.append("      </td>");

			htmlBuf.append("	  <td style='text-align:right ;' >");
			htmlBuf.append(Util.equals(
					MapUtils.getString(grpRateMap, "MAX_TWD_S_RT", ""), "") ? ""
					: df.reformat(new BigDecimal(MapUtils.getString(grpRateMap,
							"MAX_TWD_S_RT", null))));
			htmlBuf.append("      </td>");

			htmlBuf.append("	  <td style='text-align:right ;' >");
			htmlBuf.append(Util.equals(
					MapUtils.getString(grpRateMap, "MAX_USD_S_RT", ""), "") ? ""
					: df.reformat(new BigDecimal(MapUtils.getString(grpRateMap,
							"MAX_USD_S_RT", null))));
			htmlBuf.append("      </td>");

			htmlBuf.append("	  <td style='text-align:right ;' >");
			htmlBuf.append(Util.equals(
					MapUtils.getString(grpRateMap, "MAX_TWD_N_RT", ""), "") ? ""
					: df.reformat(new BigDecimal(MapUtils.getString(grpRateMap,
							"MAX_TWD_N_RT", null))));
			htmlBuf.append("      </td>");

			htmlBuf.append("	  <td style='text-align:right ;' >");
			htmlBuf.append(Util.equals(
					MapUtils.getString(grpRateMap, "MAX_USD_N_RT", ""), "") ? ""
					: df.reformat(new BigDecimal(MapUtils.getString(grpRateMap,
							"MAX_USD_N_RT", null))));
			htmlBuf.append("      </td>");

			htmlBuf.append("	  <td style='text-align:right ;' >");
			htmlBuf.append(Util.equals(
					MapUtils.getString(grpRateMap, "MIN_TWD_S_RT", ""), "") ? ""
					: df.reformat(new BigDecimal(MapUtils.getString(grpRateMap,
							"MIN_TWD_S_RT", null))));
			htmlBuf.append("      </td>");

			htmlBuf.append("	  <td style='text-align:right ;' >");
			htmlBuf.append(Util.equals(
					MapUtils.getString(grpRateMap, "MIN_USD_S_RT", ""), "") ? ""
					: df.reformat(new BigDecimal(MapUtils.getString(grpRateMap,
							"MIN_USD_S_RT", null))));
			htmlBuf.append("      </td>");

			htmlBuf.append("	  <td style='text-align:right ;' >");
			htmlBuf.append(Util.equals(
					MapUtils.getString(grpRateMap, "MIN_TWD_N_RT", ""), "") ? ""
					: df.reformat(new BigDecimal(MapUtils.getString(grpRateMap,
							"MIN_TWD_N_RT", null))));
			htmlBuf.append("      </td>");

			htmlBuf.append("	  <td style='text-align:right ;' >");
			htmlBuf.append(Util.equals(
					MapUtils.getString(grpRateMap, "MIN_USD_N_RT", ""), "") ? ""
					: df.reformat(new BigDecimal(MapUtils.getString(grpRateMap,
							"MIN_USD_N_RT", null))));
			htmlBuf.append("      </td>");

			htmlBuf.append("   </tr>");

			if (isFirstLine) {
				isFirstLine = false;
			}
		}

		htmlBuf.append("</table></div>");
		htmlBuf.append("</body></html>");

		rptVariableMap.put("GRPRATEDATAHTML", htmlBuf.toString());

		rptVariableMap.put("GRPRATEDATATITLE", rptTitleCycMn);

		// l120s05a.grpGradeMemo=第6級為新戶及未評等之集團企業。
		rptVariableMap.put("GRP_GRADE_MEMO1",
				prop.getProperty("l120s05a.grpGradeMemo"));

		return rptVariableMap;
	}

	// J-107-0007-001 Web e-Loan國內、海外授信簽報書第八章新增相同集團企業評等等級之新臺幣及美元放款利率資訊
	private Map<String, String> genGrpRateDataHtml_2017(L120S04B l120s04b,
			String custId, String dupNo, Map<String, String> rptVariableMap)
			throws IOException, Exception {

		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1205S05Page06a.class);

		StringBuffer htmlBuf = new StringBuffer("");

		NumericFormatter df = new NumericFormatter("#0.0000");
		rptVariableMap.put("GRPRATEDATAHTML", "");
		rptVariableMap.put("GRPRATEDATATITLE", "");

		List<Map<String, Object>> rows3 = new ArrayList<Map<String, Object>>();

		try {
			rows3 = this.misGrpcmpService.findGrpcmpSelGrpdtl(custId, dupNo);
		} catch (DataAccessResourceFailureException e) {
			return rptVariableMap;
		}

		String grpGrrd = Util.trim(l120s04b.getGrpGrrd());
		Integer GRPYY = l120s04b.getGrpYear() == null ? Util.parseInt(CapDate
				.getCurrentDate("yyyy")) : l120s04b.getGrpYear();

		String queryGrpYy = new BigDecimal(Util.parseInt(GRPYY) - 1911)
				.toPlainString();
		String groupBadFlag = "";

		// 設定隸屬集團代碼與名稱 map3.get("BADFLAG")
		for (Map<String, Object> map3 : rows3) {
			groupBadFlag = Util.trim(Util.trim(MapUtils.getString(map3,
					"BADFLAG", "0")));
			if (Util.equals(groupBadFlag, "")) {
				groupBadFlag = "0";
			}
			break;
		}

		String grpNo = l120s04b.getGrpNo();
		Date cycMn = CapDate.getCurrentTimestamp();
		String grpGrade = l120s04b.getGrpGrrd();
		String badFg = groupBadFlag;
		String grpYear = Util.nullToSpace(l120s04b.getGrpYear());

		// 沒有集團就不要印
		// 財務危機集團不印
		// 問題集團
		if (!lmsService.isShowGrpRateData(grpNo, cycMn, grpGrade, badFg,
				grpYear)) {
			return rptVariableMap;
		}

		List<Map<String, Object>> grpRateList = dwdbService
				.findDW_DW_ROCGRPRT_By_BadFg_With_MaxCycMn(groupBadFlag);

		if (grpRateList == null || grpRateList.isEmpty()) {
			return rptVariableMap;
		}

		boolean isFirstLine = true;
		String rptTitleCycMn = "";
		for (Map<String, Object> grpRateMap : grpRateList) {

			if (isFirstLine) {

				htmlBuf.append("<html><head><meta http-equiv='Content-Type' content='text/html; charset=utf-8'><style>table, th {border-collapse: collapse;border-style: solid;border-width: 1px;}td {border-collapse: collapse;border-style: solid;border-width: 1px;padding-top: 2px;padding-right: 2px;padding-bottom: 2px;padding-left: 2px;}</style></head>");
				htmlBuf.append("<body>");

				// J-107-0318_05097_B1001 Web e-Loan企金授信配合集團企業評等增加應予觀察註記相關程式修改
				if (Util.equals(groupBadFlag, "0")) {
					// l120s05a.grpRatePrintTitle1_2017=評等第0~7級集團企業之新臺幣及美元放款利率資訊<br>(不含列管「應予觀察」、「應予注意」及「財務危機」之集團企業)
					rptTitleCycMn = "<div style='text-align:center'>"
							+ prop.getProperty("l120s05a.grpRatePrintTitle1_2017")
							+ "</div>";
				} else if (Util.equals(groupBadFlag, "1")) {
					// l120s05a.grpRatePrintTitle2=評等第0~7級列管為「應予注意」集團企業之新臺幣及美元放款利率資訊
					rptTitleCycMn = "<div style='text-align:center'>"
							+ prop.getProperty("l120s05a.grpRatePrintTitle2_2017")
							+ "</div>";
				} else if (Util.equals(groupBadFlag, "3")) {
					// l120s05a.grpRatePrintTitle3_2017=評等第0~7級列管為「應予觀察」集團企業之新臺幣及美元放款利率資訊
					rptTitleCycMn = "<div style='text-align:center'>"
							+ prop.getProperty("l120s05a.grpRatePrintTitle3_2017")
							+ "</div>";
				} else {
					// l120s05a.grpRatePrintTitle2=評等第0~7級列管為「應予注意」集團企業之新臺幣及美元放款利率資訊
					rptTitleCycMn = "<div style='text-align:center'>"
							+ prop.getProperty("l120s05a.grpRatePrintTitle1_2017")
							+ "</div>";
				}

				htmlBuf.append("<div width='98%'>");
				// l120s05a.prnCycMn=資料基準日
				htmlBuf.append("<div style='text-align:right'>("
						+ prop.getProperty("l120s05a.prnCycMn")
						+ "："
						+ (Util.equals(
								MapUtils.getString(grpRateMap, "CYC_MN", ""),
								"") ? ""
								: CapDate.formatDate(CapDate.shiftDays(CapDate
										.addMonth(
												Util.parseDate(MapUtils
														.getString(grpRateMap,
																"CYC_MN")), 1),
										-1), "yyyy-MM-dd")) + ")</div>");

				htmlBuf.append("<table width='100%'>");
				htmlBuf.append("   <tr style='text-align:center' >");

				// l120s05a.grpYy=集團企<br>業評等<br>
				// l120s05a.grpYyUnit=年度
				htmlBuf.append("      <td rowspan='3'>"
						+ prop.getProperty("l120s05a.grpYy") + "("
						+ l120s04b.getGrpYear()
						+ prop.getProperty("l120s05a.grpYyUnit") + ")</td>");
				htmlBuf.append("      <td rowspan='3' class='hd1'>");

				// l120s05a.cnt=集團<br>企業<br>家數
				htmlBuf.append(prop.getProperty("l120s05a.cnt"));
				htmlBuf.append("      </td>");
				htmlBuf.append("      <td colspan='6' class='hd1'>");
				// l120s05a.baseAvgRt=基準日加權<br>平均放款利率(%)
				htmlBuf.append(prop.getProperty("l120s05a.baseAvgRt"));
				htmlBuf.append("      </td>");
				htmlBuf.append("      <td colspan='4'  class='hd1'>");
				// l120s05a.baseMaxRt=基準日集團企業中<br>最高放款利率(%)
				htmlBuf.append(prop.getProperty("l120s05a.baseMaxRt"));
				htmlBuf.append("      </td>");
				htmlBuf.append("      <td colspan='4' class='hd1'>");
				// l120s05a.baseMinRt=基準日集團企業中<br>最低放款利率(%)
				htmlBuf.append(prop.getProperty("l120s05a.baseMinRt"));
				htmlBuf.append("      </td>");
				htmlBuf.append("   </tr>");
				htmlBuf.append("   <tr style='text-align:center ;' >");
				htmlBuf.append("      <td class='hd1' colspan='2'>");

				// l120s05a.avgRt=總平均<br>放款利率
				htmlBuf.append(prop.getProperty("l120s05a.avgRt"));
				htmlBuf.append("      </td>");
				htmlBuf.append("      <td class='hd1' colspan='2'>");
				// l120s05a.avgSRt=平均擔保<br>放款利率
				htmlBuf.append(prop.getProperty("l120s05a.avgSRt"));
				htmlBuf.append("      </td>");
				htmlBuf.append("      <td class='hd1' colspan='2'>");
				// l120s05a.avgNRt=平均無擔保<br>放款利率
				htmlBuf.append(prop.getProperty("l120s05a.avgNRt"));
				htmlBuf.append("      </td>");
				htmlBuf.append("      <td class='hd1' colspan='2'>");
				// l120s05a.SRt=擔保<br>放款利率
				htmlBuf.append(prop.getProperty("l120s05a.SRt"));
				htmlBuf.append("      </td>");
				htmlBuf.append("      <td class='hd1' colspan='2'>");
				// l120s05a.NRt=無擔保<br>放款利率
				htmlBuf.append(prop.getProperty("l120s05a.NRt"));
				htmlBuf.append("      </td>");
				htmlBuf.append("      <td class='hd1' colspan='2'>");
				// l120s05a.SRt=擔保<br>放款利率
				htmlBuf.append(prop.getProperty("l120s05a.SRt"));
				htmlBuf.append("      </td>");
				htmlBuf.append("      <td class='hd1' colspan='2'>");
				// l120s05a.NRt=無擔保<br>放款利率
				htmlBuf.append(prop.getProperty("l120s05a.NRt"));
				htmlBuf.append("      </td>");
				htmlBuf.append("   </tr>");
				htmlBuf.append("   <tr style='text-align:center ;' >");
				htmlBuf.append("      <td>");
				// l120s05a.twd=新臺幣
				htmlBuf.append(prop.getProperty("l120s05a.twd"));
				htmlBuf.append("      </td>");
				htmlBuf.append("      <td>");
				// l120s05a.usd=美金
				htmlBuf.append(prop.getProperty("l120s05a.usd"));
				htmlBuf.append("      </td>");
				htmlBuf.append("      <td>");
				// l120s05a.twd=新臺幣
				htmlBuf.append(prop.getProperty("l120s05a.twd"));
				htmlBuf.append("      </td>");
				htmlBuf.append("      <td>");
				htmlBuf.append("美金");
				htmlBuf.append("      </td>");
				htmlBuf.append("      <td>");
				// l120s05a.twd=新臺幣
				htmlBuf.append(prop.getProperty("l120s05a.twd"));
				htmlBuf.append("      </td>");
				htmlBuf.append("      <td>");
				// l120s05a.usd=美金
				htmlBuf.append(prop.getProperty("l120s05a.usd"));
				htmlBuf.append("      </td>");
				htmlBuf.append("      <td>");
				// l120s05a.twd=新臺幣
				htmlBuf.append(prop.getProperty("l120s05a.twd"));
				htmlBuf.append("      </td>");
				htmlBuf.append("      <td>");
				// l120s05a.usd=美金
				htmlBuf.append(prop.getProperty("l120s05a.usd"));
				htmlBuf.append("      </td>");
				htmlBuf.append("      <td>");
				// l120s05a.twd=新臺幣
				htmlBuf.append(prop.getProperty("l120s05a.twd"));
				htmlBuf.append("      </td>");
				htmlBuf.append("      <td>");
				// l120s05a.usd=美金
				htmlBuf.append(prop.getProperty("l120s05a.usd"));
				htmlBuf.append("      </td>");
				htmlBuf.append("      <td>");
				// l120s05a.twd=新臺幣
				htmlBuf.append(prop.getProperty("l120s05a.twd"));
				htmlBuf.append("      </td>");
				htmlBuf.append("      <td>");
				// l120s05a.usd=美金
				htmlBuf.append(prop.getProperty("l120s05a.usd"));
				htmlBuf.append("      </td>");
				htmlBuf.append("      <td>");
				// l120s05a.twd=新臺幣
				htmlBuf.append(prop.getProperty("l120s05a.twd"));
				htmlBuf.append("      </td>");
				htmlBuf.append("      <td>");
				// l120s05a.usd=美金
				htmlBuf.append(prop.getProperty("l120s05a.usd"));
				htmlBuf.append("      </td>");
				htmlBuf.append("   </tr>");

			}

			htmlBuf.append("   <tr >");
			htmlBuf.append("      <td style='text-align:center ;' >");

			// l120s05a.theFirst=第
			// l120s05a.grpGrade=級
			// l120s05a.prnMemo1=註1
			htmlBuf.append(prop.getProperty("l120s05a.theFirst")
					+ MapUtils.getString(grpRateMap, "GRPGRADE", "")
					+ prop.getProperty("l120s05a.grpGrade") + "<br>");

			htmlBuf.append("      </td>");
			htmlBuf.append("      <td style='text-align:right ;' >");
			htmlBuf.append(MapUtils.getString(grpRateMap, "CNT", ""));
			htmlBuf.append("      </td>");

			htmlBuf.append("	  <td style='text-align:right ;' >");
			htmlBuf.append(Util.equals(
					MapUtils.getString(grpRateMap, "AVG_TWD_RT", ""), "") ? ""
					: df.reformat(new BigDecimal(MapUtils.getString(grpRateMap,
							"AVG_TWD_RT", null))));
			htmlBuf.append("      </td>");

			htmlBuf.append("	  <td style='text-align:right ;' >");
			htmlBuf.append(Util.equals(
					MapUtils.getString(grpRateMap, "AVG_USD_RT", ""), "") ? ""
					: df.reformat(new BigDecimal(MapUtils.getString(grpRateMap,
							"AVG_USD_RT", null))));
			htmlBuf.append("      </td>");

			htmlBuf.append("	  <td style='text-align:right ;' >");
			htmlBuf.append(Util.equals(
					MapUtils.getString(grpRateMap, "AVG_TWD_S_RT", ""), "") ? ""
					: df.reformat(new BigDecimal(MapUtils.getString(grpRateMap,
							"AVG_TWD_S_RT", null))));
			htmlBuf.append("      </td>");

			htmlBuf.append("	  <td style='text-align:right ;' >");
			htmlBuf.append(Util.equals(
					MapUtils.getString(grpRateMap, "AVG_USD_S_RT", ""), "") ? ""
					: df.reformat(new BigDecimal(MapUtils.getString(grpRateMap,
							"AVG_USD_S_RT", null))));
			htmlBuf.append("      </td>");

			htmlBuf.append("	  <td style='text-align:right ;' >");
			htmlBuf.append(Util.equals(
					MapUtils.getString(grpRateMap, "AVG_TWD_N_RT", ""), "") ? ""
					: df.reformat(new BigDecimal(MapUtils.getString(grpRateMap,
							"AVG_TWD_N_RT", null))));
			htmlBuf.append("      </td>");

			htmlBuf.append("	  <td style='text-align:right ;' >");
			htmlBuf.append(Util.equals(
					MapUtils.getString(grpRateMap, "AVG_USD_N_RT", ""), "") ? ""
					: df.reformat(new BigDecimal(MapUtils.getString(grpRateMap,
							"AVG_USD_N_RT", null))));
			htmlBuf.append("      </td>");

			htmlBuf.append("	  <td style='text-align:right ;' >");
			htmlBuf.append(Util.equals(
					MapUtils.getString(grpRateMap, "MAX_TWD_S_RT", ""), "") ? ""
					: df.reformat(new BigDecimal(MapUtils.getString(grpRateMap,
							"MAX_TWD_S_RT", null))));
			htmlBuf.append("      </td>");

			htmlBuf.append("	  <td style='text-align:right ;' >");
			htmlBuf.append(Util.equals(
					MapUtils.getString(grpRateMap, "MAX_USD_S_RT", ""), "") ? ""
					: df.reformat(new BigDecimal(MapUtils.getString(grpRateMap,
							"MAX_USD_S_RT", null))));
			htmlBuf.append("      </td>");

			htmlBuf.append("	  <td style='text-align:right ;' >");
			htmlBuf.append(Util.equals(
					MapUtils.getString(grpRateMap, "MAX_TWD_N_RT", ""), "") ? ""
					: df.reformat(new BigDecimal(MapUtils.getString(grpRateMap,
							"MAX_TWD_N_RT", null))));
			htmlBuf.append("      </td>");

			htmlBuf.append("	  <td style='text-align:right ;' >");
			htmlBuf.append(Util.equals(
					MapUtils.getString(grpRateMap, "MAX_USD_N_RT", ""), "") ? ""
					: df.reformat(new BigDecimal(MapUtils.getString(grpRateMap,
							"MAX_USD_N_RT", null))));
			htmlBuf.append("      </td>");

			htmlBuf.append("	  <td style='text-align:right ;' >");
			htmlBuf.append(Util.equals(
					MapUtils.getString(grpRateMap, "MIN_TWD_S_RT", ""), "") ? ""
					: df.reformat(new BigDecimal(MapUtils.getString(grpRateMap,
							"MIN_TWD_S_RT", null))));
			htmlBuf.append("      </td>");

			htmlBuf.append("	  <td style='text-align:right ;' >");
			htmlBuf.append(Util.equals(
					MapUtils.getString(grpRateMap, "MIN_USD_S_RT", ""), "") ? ""
					: df.reformat(new BigDecimal(MapUtils.getString(grpRateMap,
							"MIN_USD_S_RT", null))));
			htmlBuf.append("      </td>");

			htmlBuf.append("	  <td style='text-align:right ;' >");
			htmlBuf.append(Util.equals(
					MapUtils.getString(grpRateMap, "MIN_TWD_N_RT", ""), "") ? ""
					: df.reformat(new BigDecimal(MapUtils.getString(grpRateMap,
							"MIN_TWD_N_RT", null))));
			htmlBuf.append("      </td>");

			htmlBuf.append("	  <td style='text-align:right ;' >");
			htmlBuf.append(Util.equals(
					MapUtils.getString(grpRateMap, "MIN_USD_N_RT", ""), "") ? ""
					: df.reformat(new BigDecimal(MapUtils.getString(grpRateMap,
							"MIN_USD_N_RT", null))));
			htmlBuf.append("      </td>");

			htmlBuf.append("   </tr>");

			if (isFirstLine) {
				isFirstLine = false;
			}
		}

		htmlBuf.append("</table></div>");
		htmlBuf.append("</body></html>");

		rptVariableMap.put("GRPRATEDATAHTML", htmlBuf.toString());

		rptVariableMap.put("GRPRATEDATATITLE", rptTitleCycMn);

		// l120s05a.grpGradeMemo_2017=集團規模與級別說明：第1級(大A)，第2級(大B、中A)，第3級(大C、中B、小A)，第4級(大D、中C、小B)，第5級(大E、中D、小C)，第6級(大F、中E、小D)，第7級(大G、中F、小E)，第7級(新戶及未評等之集團企業)。
		rptVariableMap.put("GRP_GRADE_MEMO1",
				prop.getProperty("l120s05a.grpGradeMemo_2017"));

		return rptVariableMap;
	}

	/**
	 * J-109-0322_05097_B1001 Web e-Loan企消金授信簽報登錄決議輸入授審會會期, 於列印決議內容時將會期資訊改以西元年呈現
	 * 
	 * @param rptTitle1
	 *            授審會會期
	 * @return
	 */
	private String formatRptTitle1(String rptTitle1) {

		rptTitle1 = Util.trim(rptTitle1);

		if (Util.equals(rptTitle1, "")) {
			return rptTitle1;
		}

		String rtnRptTitle = rptTitle1;

		int yearIndex = rptTitle1.indexOf("年");

		if (yearIndex == 3) {
			String printAdYear = Util.trim(lmsService
					.getSysParamDataValue("LMS_RPTTITLE1_PRINT_AD_YEAR"));
			if (Util.equals(printAdYear, "Y")) {
				rtnRptTitle = (Util.parseInt(Util.getLeftStr(rptTitle1, 3)) + 1911)
						+ Util.getRightStr(rptTitle1, rptTitle1.length() - 3);
			}
		}

		return rtnRptTitle;
	}
}
