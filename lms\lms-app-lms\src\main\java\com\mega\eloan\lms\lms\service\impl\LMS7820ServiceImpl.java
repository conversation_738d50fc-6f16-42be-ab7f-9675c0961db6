/* 
 * LMS7820ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.mega.eloan.common.enums.DocLogEnum;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.lms.dao.L782A01ADao;
import com.mega.eloan.lms.dao.L782M01ADao;
import com.mega.eloan.lms.lms.service.LMS7820Service;
import com.mega.eloan.lms.model.L160M01B;
import com.mega.eloan.lms.model.L782M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;

/**
 * <pre>
 * 特殊登錄案件紀錄表
 * </pre>
 * 
 * @since 2011/12/8
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/12/8,REX,new
 *          </ul>
 */
@Service
public class LMS7820ServiceImpl extends AbstractCapService implements
		LMS7820Service {

	@Resource
	L782A01ADao l782a01aDao;

	@Resource
	L782M01ADao l782m01aDao;
	@Resource
	DocLogService docLogService;

	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		if (clazz == L782M01A.class) {
			return l782m01aDao.findByMainId(mainId);
		}

		return null;
	}

	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L782M01A) {
					((L782M01A) model).setUpdater(user.getUserId());
					((L782M01A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l782m01aDao.save((L782M01A) model);
				}
			}
		}
	}

	@Override
	public void delete(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L782M01A) {
					((L782M01A) model).setDeletedTime(CapDate
							.getCurrentTimestamp());
					((L782M01A) model).setUpdater(user.getUserId());
					docLogService.record(((L782M01A) model).getOid(),
							DocLogEnum.DELETE);
					l782m01aDao.save((L782M01A) model);
				}
			}
		}
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == L782M01A.class) {
			return l782m01aDao.findPage(search);
		}
		return null;
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		if (clazz == L782M01A.class) {
			return (T) l782m01aDao.findByOid(oid);
		} else if (clazz == L160M01B.class) {
			return (T) l782a01aDao.findByOid(oid);
		}
		return null;
	}

	@Override
	public List<L782M01A> findByAll(String ownBrId,String releaseDateS,String releaseDateE) {
		return l782m01aDao.findByAll(ownBrId,releaseDateS,releaseDateE);
	}

}
