/* 
 * C160S01FDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C160S01F;

/** 代償轉貸借新還舊明細檔 **/
public interface C160S01FDao extends IGenericDao<C160S01F> {

	C160S01F findByOid(String oid);

	List<C160S01F> findByMainId(String mainId);

	C160S01F findByUniqueKey(String mainId, Integer seq, String bankNo,
			String branchNo, String subACNo, String refmainId);

	List<C160S01F> findByIndex01(String mainId, Integer seq, String bankNo,
			String branchNo, String subACNo, String refmainId);

	List<C160S01F> findByMainIdSeqRefMainid(String mainId, Integer seq,
			String refmainId);

	java.math.BigDecimal getSubAmtSum(String mainId, Integer seq,
			String refmainId);

}