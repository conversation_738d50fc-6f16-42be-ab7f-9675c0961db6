/*
 * SimpleOperation.java
 *
 * Copyright (c) 2009-2011 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
 */
package tw.com.iisi.cap.operation.simple;

import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.iisigroup.cap.component.PageParameters;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.handler.FormHandler;
import tw.com.iisi.cap.operation.Operation;
import tw.com.iisi.cap.operation.OperationStep;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapString;

/**
 * <p>
 * SimpleOperation.
 * </p>
 * 
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2010/7/22,iristu,new
 *          </ul>
 */
public class SimpleOperation implements Operation {

    private static Logger logger = LoggerFactory.getLogger(SimpleOperation.class);

    /**
     * Operation name
     */
    String operName;

    /**
     * 所有步驟
     */
    Map<String, OperationStep> ruleMap;

    /**
     * 回傳結果
     */
    IResult result;

    /**
     * 執行Operation
     */
    public IResult execute(PageParameters params, FormHandler handler) throws CapException {
        OperationStep step = getStartStep();
        IResult formResult = result;
        long startOperation = System.currentTimeMillis();
        try {
            SimpleContextHolder.resetInfoStore();
            while (step != null) {
                String result = null;
                try {
                    long startStep = System.currentTimeMillis();
                    result = step.execute(params, handler, formResult);
                    logger.debug(step.getName() + " cost : " + (System.currentTimeMillis() - startStep));
                } catch (CapException e) {
                    result = step.handleException(e);
                    throw e;
                }
                if (!CapString.isEmpty(result)) {
                    if (OperationStep.NEXT.equals(result)) {
                        step = getNextStep(step.getName());
                    } else if (OperationStep.RETURN.equals(result) || OperationStep.ERROR.equals(result)) {
                        step = null;
                    } else {
                        step = getStep(step.getRuleMap().get(result));
                    }
                }
            }
            return formResult;
        } catch (CapException ce) {
            throw ce;
        } catch (Exception e) {
            throw new CapException(e, getClass());
        } finally {
            SimpleContextHolder.resetInfoStore();
            logger.debug("Operation cost : " + (System.currentTimeMillis() - startOperation));
        }
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.iisi.cap.operation.Operation#setName(java.lang.String)
     */
    @Override
    public void setName(String name) {
        this.operName = name;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.iisi.cap.operation.Operation#getName()
     */
    @Override
    public String getName() {
        return operName;
    }

    /**
     * 取得開始位置
     */
    public OperationStep getStartStep() {
        if (ruleMap != null && !ruleMap.isEmpty()) {
            Set<Entry<String, OperationStep>> steps = ruleMap.entrySet();
            for (Entry<String, OperationStep> entry : steps) {
                OperationStep step = entry.getValue();
                step.setName(entry.getKey());
                return step;
            }
        }
        return null;
    }

    /**
     * 取得下一步
     * 
     * @param currentStepName
     */
    public OperationStep getNextStep(String currentStepName) {
        if (ruleMap != null && !ruleMap.isEmpty()) {
            Set<Entry<String, OperationStep>> steps = ruleMap.entrySet();
            boolean b = false;
            for (Entry<String, OperationStep> entry : steps) {
                if (b) {
                    OperationStep step = entry.getValue();
                    step.setName(entry.getKey());
                    return step;
                } else if (currentStepName.equals(entry.getKey())) {
                    b = true;
                }
            }
        }
        return null;
    }

    /**
     * 取得步驟
     * 
     * @param stepName
     * @return
     */
    public OperationStep getStep(String stepName) {
        if (ruleMap != null && ruleMap.containsKey(stepName)) {
            OperationStep step = ruleMap.get(stepName);
            step.setName(stepName);
            return step;
        }
        return null;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.iisi.cap.operation.Operation#getRuleMap()
     */
    @Override
    public Map<String, OperationStep> getRuleMap() {
        return this.ruleMap;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.iisi.cap.operation.Operation#setRuleMap(java.util.Map)
     */
    @Override
    public void setRuleMap(final Map<String, OperationStep> ruleMap) {
        this.ruleMap = ruleMap;
    }

    /**
     * 設置回傳結果
     * 
     * @param result
     *            回傳結果
     */
    public void setResult(IResult result) {
        this.result = result;
    }

}
