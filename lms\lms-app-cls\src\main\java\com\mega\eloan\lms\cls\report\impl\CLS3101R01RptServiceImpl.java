package com.mega.eloan.lms.cls.report.impl;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.service.AbstractReportService;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.cls.pages.CLS3101M01Page;
import com.mega.eloan.lms.cls.report.CLS3101R01RptService;
import com.mega.eloan.lms.model.C310M01A;
import com.mega.eloan.lms.model.C310M01E;
import com.mega.eloan.lms.model.C900S02E;
import com.mega.eloan.lms.model.C900S02F;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.ReportGenerator;


@Service("cls3101r01rptservice")
public class CLS3101R01RptServiceImpl extends AbstractReportService implements CLS3101R01RptService  {

	@Resource
	BranchService branchService;
	
	@Resource
	LMSService lmsService;
	
	@Resource
	CLSService clsService;
	
	@Override
	public String getReportTemplateFileName() {
		Locale locale = LocaleContextHolder.getLocale();
		if (locale == null)
			locale = Locale.getDefault();
		// 測試用
		return "report/cls/CLS3101R01_" + locale.toString() + ".rpt";
	}

	@Override
	public void setReportData(ReportGenerator rptGenerator,
			PageParameters params) throws CapException, ParseException {
		Properties prop = MessageBundleScriptCreator.getComponentResource(CLS3101M01Page.class);
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		String mainOid = Util.trim(params.getString(EloanConstants.MAIN_OID));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		C310M01A c310m01a = null;
		
		String branchName = null;
		// zh_TW: 正體中文
		// zh_CN: 簡體中文
		// en_US: 英文
		Locale locale = null;
		try {
			locale = LocaleContextHolder.getLocale();
			if (locale == null)
				locale = Locale.getDefault();
			c310m01a = clsService.findC310M01A_oid(mainOid);
			
			String apprid = "";
			String recheckid = "";
			String bossid = "";
			String managerid = "";
			List<C310M01E> c310m01elist = clsService.findC310M01E(mainId);

				for (C310M01E c310m01e : c310m01elist) {
					// 要加上人員代碼
					String type = Util.trim(c310m01e.getStaffJob());
					String userId = Util.trim(c310m01e.getStaffNo());
					String value = Util.trim(lmsService.getUserName(userId));
					if ("L1".equals(type)) {
						apprid = userId + " " + value;
					} else if ("L3".equals(type)) {
						bossid = bossid + userId + " " + value + "<br/>";
					} else if ("L4".equals(type)) {
						recheckid = userId + " " + value;
					} else if ("L5".equals(type)) {
						managerid = userId + " " + value;
					}
				}
				
			branchName = Util.nullToSpace(branchService.getBranchName(Util.nullToSpace(c310m01a.getOwnBrId())));
			String idDup = Util.nullToSpace(c310m01a.getCustId()) + "-" + Util.nullToSpace(c310m01a.getDupNo());
			String custname = Util.nullToSpace(c310m01a.getCustName());
			

			C900S02E c900s02e = clsService.findC900S02E_mainId(c310m01a.getRefMainId());
			String text = "";
			List<String> c900s02f_data = new ArrayList<String>();
			if(c900s02e!=null){
				text  = c900s02e.getText();
				
				for(C900S02F c900s02f : clsService.findC900S02F_mainId_order(c900s02e.getMainId())){
					c900s02f_data.add(CrsUtil.build_C900S02F_info(c900s02f));
				}
			}
			
			String chk_result = c310m01a.getChk_result();
			if(Util.equals("Y", chk_result)){
				chk_result = prop.getProperty("C310M01A.chk_result.Y");
			}else if(Util.equals("N", chk_result)){
				chk_result = prop.getProperty("C310M01A.chk_result.N");
			}
			
			rptVariableMap.put("BRANCHNAME", branchName);
			rptVariableMap.put("C310M01A.Mainid", Util.trim(c310m01a.getMainId()));
			rptVariableMap.put("C310M01A.CUSTID", idDup);			
			rptVariableMap.put("C310M01A.CUSTNAME", custname);			
			rptVariableMap.put("C310M01A.CYC_MN", StringUtils.substring(TWNDate.toAD(c900s02e.getCyc_mn()), 0, 7));
			rptVariableMap.put("C310M01A.TEXT", text);
			rptVariableMap.put("C900S02F_DATA", StringUtils.join(c900s02f_data, " <br/>"));
			rptVariableMap.put("C310M01A.CHK_RESULT", chk_result);
			rptVariableMap.put("C310M01A.CHK_MEMO", Util.trim(c310m01a.getChk_memo()));
			
			rptVariableMap.put("C310M01E.APPRID", apprid);
			rptVariableMap.put("C310M01E.RECHECKID", recheckid);
			rptVariableMap.put("C310M01E.BOSSID", bossid);
			rptVariableMap.put("C310M01E.MANAGERID", managerid);
			
			
			// this.generator.setLang(java.util.Locale.TAIWAN);
			rptGenerator.setLang(locale);
			rptGenerator.setVariableData(rptVariableMap);
		} finally {

		}
		
	}

}
