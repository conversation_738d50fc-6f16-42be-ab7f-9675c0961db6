package com.mega.eloan.lms.dc.base;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.w3c.dom.Document;

public class OccursKeyChecker implements IKeyChecker {
	private static boolean DEBUG = false;
	private Logger logger = LoggerFactory.getLogger(OccursKeyChecker.class);

	private String[] keyAry;
	private String[] valuesAry;
	private boolean useFlag = false;
	private XMLHandler xmlHandler;

	private OccursHander occursHander;

	public OccursKeyChecker(OccursHander occursHander) {
		this.occursHander = occursHander;
	}

	public void init(XMLHandler xmlHandler, String key, String value) {
		this.xmlHandler = xmlHandler;
		if (StringUtils.isNotBlank(key) && !";".equals(key)) {
			useFlag = true;

			this.keyAry = StringUtils.split(key, ';');
			this.valuesAry = StringUtils.split(value, ';');

			if (this.keyAry.length != this.valuesAry.length) {
				throw new DCException("OccursKeys(" + key + ") & OccursValues("
						+ value + ") 之個數不符，請確認之！");
			}
		}
	}

	public boolean isUseChecker() {
		return this.useFlag;
	}

	@Override
	public boolean check(Document domDoc, Object param) throws Exception {
		long t1 = System.currentTimeMillis();

		int occurs = ((Integer) param).intValue();
		boolean result = false;

		try {
			for (int i = 0; i < this.keyAry.length; i++) {
				String fldValue = this.xmlHandler.getItemValue(domDoc,
						this.occursHander.chkOccurs(this.keyAry[i], occurs));
				// 2013-04-01 Modify by Bang :修正OccursKeys為多值時,未正確判斷 notSpace
				if (StringUtils.isBlank(fldValue)) {
					if ("notSpace".equalsIgnoreCase(this.valuesAry[i])) {
						result = false;
						break;
					}
				} else {
					String[] chkValue = StringUtils.split(this.valuesAry[i],
							'-');
					for (String chk : chkValue) {
						if (fldValue.equals(chk)) {
							result = true;
							break;
						} else if ("notSpace".equalsIgnoreCase(chk)
								&& fldValue.length() > 0) {
							result = true;
							break;
						}
					}
				}

				// 設設為AND，其中一個失敗，回傳失敗
				if (!result) {
					result = false;
					break;
				}
			}

			return result;
		} finally {
			if (logger.isDebugEnabled() && DEBUG) {
				this.logger
						.info("##### check() occurs=" + occurs + ",result="
								+ result + " COST="
								+ (System.currentTimeMillis() - t1));
			}
		}

	}

}
