/* 
 * C122S01HDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.C122S01HDao;
import com.mega.eloan.lms.model.C122S01H;

/** 進件狀態明細檔 **/
@Repository
public class C122S01HDaoImpl extends LMSJpaDao<C122S01H, String>
	implements C122S01HDao {

	@Override
	public C122S01H findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C122S01H> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<C122S01H> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public C122S01H findByUniqueKey(String mainId, String flowId){
		ISearch search = createSearchTemplete();
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (flowId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "flowId", flowId);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<C122S01H> findByIndex01(String mainId, String flowId){
		ISearch search = createSearchTemplete();
		List<C122S01H> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (flowId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "flowId", flowId);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<C122S01H> findByRefMainId(String refMainId, String flowId){
		ISearch search = createSearchTemplete();
		List<C122S01H> list = null;
		if (refMainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "remainId", refMainId);
		if (flowId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "flowId", flowId);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
}