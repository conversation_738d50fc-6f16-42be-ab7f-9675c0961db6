---------------------------------------------------------
-- LMS.C900M01F 個金授信科目限額檔
---------------------------------------------------------

---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.C900M01F;
CREATE TABLE LMS.C900M01F (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)      not null,
	LMTTY<PERSON><PERSON>       CHAR(1)       not null,
	LMTSEQ        DECIMAL(5,0)  not null,	
	SUBJCODE	  VARCHAR(8)	,
	SUBJECT       VARCHAR(300) ,
	LMTCURR       CHAR(3)      ,
	LMTAMT        DECIMAL(13,0),
	SUBJSEQ       DECIMAL(3,0),
	SUBJDSCR      VARCHAR(200) ,
	LMTDAYS       DECIMAL(5,0) ,
	LMTOTHER      CHAR(01)     ,
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_C900M01F PRIMARY KEY(OID)
) IN EL_DATA_4KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XC900M01F01;
CREATE UNIQUE INDEX LMS.XC900M01F01 ON LMS.C900M01F   (MAINID, LMTTYPE, LMTSEQ);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.C900M01F IS '個金授信科目限額檔';
COMMENT ON LMS.C900M01F (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	LMTTYPE       IS '限額類型', 
	LMTSEQ        IS '序號', 
	SUBJCODE	  IS '會計科子細目'
	SUBJECT       IS '授信科目(組合)', 
	LMTCURR       IS '限額－幣別', 
	LMTAMT        IS '限額－金額', 
	SUBJSEQ       IS '科目順序', 
	SUBJDSCR      IS '科目補充說明', 
	LMTDAYS       IS '清償期限－天數', 
	LMTOTHER      IS '清償期限－詳產品資訊', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
