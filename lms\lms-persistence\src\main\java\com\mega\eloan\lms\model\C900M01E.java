/* 
 * C900M01E.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 疑似利用偽造證件或財力證明用以申辦信用卡或貸款警示訊息檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C900M01E", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId"}))
public class C900M01E extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 任職公司之統一編號 **/
	@Size(max=10)
	@Column(name="COMID", length=10, columnDefinition="VARCHAR(10)")
	private String comId;

	/** 任職公司之名稱 **/
	@Size(max=250)
	@Column(name="COMNAME", length=250, columnDefinition="VARCHAR(250)")
	private String comName;

	/** 
	 * 服務單位地址(縣市)<p/>
	 * 比照C101S01B.EX:台北市
	 */
	@Size(max=2)
	@Column(name="COMCITY", length=2, columnDefinition="VARCHAR(2)")
	private String comCity;

	/** 
	 * 服務單位地址(鄉鎮市區)<p/>
	 * 比照C101S01B.EX:大安區
	 */
	@Size(max=5)
	@Column(name="COMZIP", length=5, columnDefinition="VARCHAR(5)")
	private String comZip;

	/** 
	 * 服務單位地址<p/>
	 * 比照C101S01B. EX:信義路3段
	 */
	@Size(max=192)
	@Column(name="COMADDR", length=192, columnDefinition="VARCHAR(192)")
	private String comAddr;

	/** 
	 * 服務單位地址(標的)<p/>
	 * 比照C101S01B.EX: 台北市大安區信義路3段
	 */
	@Size(max=300)
	@Column(name="COMTARGET", length=300, columnDefinition="VARCHAR(300)")
	private String comTarget;

	/** 來源文號 **/
	@Size(max=128)
	@Column(name="SOURCENO", length=128, columnDefinition="VARCHAR(128)")
	private String sourceNo;

	/** 
	 * 資料來源<p/>
	 * 0:卡務中心. 9:其他
	 */
	@Size(max=1)
	@Column(name="DATASRC", length=1, columnDefinition="CHAR(1)")
	private String dataSrc;

	/** 取得任職公司之統一編號 **/
	public String getComId() {
		return this.comId;
	}
	/** 設定任職公司之統一編號 **/
	public void setComId(String value) {
		this.comId = value;
	}

	/** 取得任職公司之名稱 **/
	public String getComName() {
		return this.comName;
	}
	/** 設定任職公司之名稱 **/
	public void setComName(String value) {
		this.comName = value;
	}

	/** 
	 * 取得服務單位地址(縣市)<p/>
	 * 比照C101S01B.EX:台北市
	 */
	public String getComCity() {
		return this.comCity;
	}
	/**
	 *  設定服務單位地址(縣市)<p/>
	 *  比照C101S01B.EX:台北市
	 **/
	public void setComCity(String value) {
		this.comCity = value;
	}

	/** 
	 * 取得服務單位地址(鄉鎮市區)<p/>
	 * 比照C101S01B.EX:大安區
	 */
	public String getComZip() {
		return this.comZip;
	}
	/**
	 *  設定服務單位地址(鄉鎮市區)<p/>
	 *  比照C101S01B.EX:大安區
	 **/
	public void setComZip(String value) {
		this.comZip = value;
	}

	/** 
	 * 取得服務單位地址<p/>
	 * 比照C101S01B. EX:信義路3段
	 */
	public String getComAddr() {
		return this.comAddr;
	}
	/**
	 *  設定服務單位地址<p/>
	 *  比照C101S01B. EX:信義路3段
	 **/
	public void setComAddr(String value) {
		this.comAddr = value;
	}

	/** 
	 * 取得服務單位地址(標的)<p/>
	 * 比照C101S01B.EX: 台北市大安區信義路3段
	 */
	public String getComTarget() {
		return this.comTarget;
	}
	/**
	 *  設定服務單位地址(標的)<p/>
	 *  比照C101S01B.EX: 台北市大安區信義路3段
	 **/
	public void setComTarget(String value) {
		this.comTarget = value;
	}

	/** 取得來源文號 **/
	public String getSourceNo() {
		return this.sourceNo;
	}
	/** 設定來源文號 **/
	public void setSourceNo(String value) {
		this.sourceNo = value;
	}

	/** 
	 * 取得資料來源<p/>
	 * 0:卡務中心. 9:其他
	 */
	public String getDataSrc() {
		return this.dataSrc;
	}
	/**
	 *  設定資料來源<p/>
	 *  0:卡務中心. 9:其他
	 **/
	public void setDataSrc(String value) {
		this.dataSrc = value;
	}
}
