/* 
 * LMS9515M01Formhandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.handler.form;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.rpt.service.LMS9511Service;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 管理報表 - 國內
 * </pre>
 * 
 * @since 2013/01/03
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/01/03,Ice,new
 *          <li>2013/01/10,Vector,加入個金報表
 *          </ul>
 */

@Scope("request")
@Controller("lms9521m01formhandler")
public class LMS9521M01Formhandler extends AbstractFormHandler {

	@Resource
	CodeTypeService codetypeService;// com.bcodetype

	@Resource
	BranchService branchService;

	@Resource
	DocFileService fileService;

	@Resource
	LMS9511Service lms9511Service;

	@Resource
	LMSService lmsService;

	@Resource
	EloandbBASEService eloandbBASEService;
	
	/**
	 * 搜尋分行
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 **/
	public IResult queryBranch(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String areaBranchId = Util.trim(params.getString("areaBranchId"));
		Map<String, String> m = new TreeMap<String, String>();
		List<IBranch> bankList1 = branchService.getBranchOfGroup(areaBranchId);
		for (IBranch b : bankList1) {
			String brName = Util.trim(b.getBrName());
			String brCode = b.getBrNo();
			m.put(brCode, brName);
		}

		CapAjaxFormResult bankList2 = new CapAjaxFormResult(m);
		result.set("item", bankList2);
		return result;
	}

	/**
	 * 搜尋 報表類型
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 **/
	public IResult queryReportType(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		Date dataStartDate = LMSUtil.getExMonthFirstDay(-1);
		Date dataEndDate = LMSUtil.getExMonthLastDay(-1);
		result.set("DATASTARTDATE1", TWNDate.toAD(dataStartDate));
		result.set("DATASTARTDATE2", TWNDate.toAD(dataStartDate)
				.substring(0, 7));
		result.set("DATAENDDATE1", TWNDate.toAD(dataEndDate));
		result.set("DATAENDDATE2", TWNDate.toAD(dataEndDate).substring(0, 7));
		result.set("LASTYEAR", (Integer.parseInt(CapDate.getCurrentDate("yyyy")) - 1));
		Calendar cal = Calendar.getInstance();
		cal = Calendar.getInstance();
		cal.add(Calendar.DATE, -1 * 7);  
		cal.set(Calendar.DAY_OF_WEEK,Calendar.SATURDAY);
		result.set("DATAENDDATE3", TWNDate.toAD(cal.getTime()));
		dataStartDate = LMSUtil.getExMonthFirstDay(1);
		dataEndDate = LMSUtil.getExMonthLastDay(1);
		result.set("NEXTDATASTARTDATE1", TWNDate.toAD(dataStartDate));
		result.set("NEXTDATAENDDATE1", TWNDate.toAD(dataEndDate));
		dataStartDate = LMSUtil.getExMonthFirstDay(0);
		dataEndDate = LMSUtil.getExMonthLastDay(0);
		result.set("THISDATASTARTDATE1", TWNDate.toAD(dataStartDate));
		result.set("THISDATAENDDATE1", TWNDate.toAD(dataEndDate));
		dataStartDate = LMSUtil.getExMonthFirstDay(-4);
		result.set("DATASTARTDATE5", TWNDate.toAD(dataStartDate).substring(0, 7));
		
		// TreeMap排序
		// lms9511v01_docType1 管理報表種類-企金
		CapAjaxFormResult type = codetypeService
				.findByCodeTypeWithOrderBy("lms9511v01_docType1");
		if (type == null) {
			type = new CapAjaxFormResult();
		}
		result.set("item", type);
		// Vector done
		// lms9511v01_docType2 管理報表種類-個金
		CapAjaxFormResult clsType = codetypeService
				.findByCodeTypeWithOrderBy("lms9511v01_docType2");
		if (clsType == null) {
			clsType = new CapAjaxFormResult();
		}
		result.set("clsItem", clsType);
		
		CapAjaxFormResult prod = lms9511Service.getProduct();
		if (prod == null) {
			prod = new CapAjaxFormResult();
		}
		result.set("prodItem", prod);
		
		return result;
	}
}
