
#==================================================
# \u8986\u5be9-\u7ba1\u7406\u5831\u8868 
#==================================================
L185M01a.ownBrId=Unit
L185M01a.createTime=Date Generated
L185M01a.dataDate=Data Date
L185M01a.sDate=Data Date(starting)
L185M01a.eDate=Data Date(ending)
L185M01a.conut=Data Count
L185M01a.acctCount=Total No. Of Accounts
L185M01a.uLoanYCount=No. Of Syndication Loans
L185M01a.uLoanNCount=No. Of Non-syndication Loans
L185M01a.delayCount=Overdue Count

L185M01a.type1=Pastdue Review List
L185M01a.type2=This corporate account does not appear on the credit review list
L185M01a.type3=Checklist for credit review 6 months after drawdown
L185M01a.type4=Credit Review Checklist

L185M01a.Type1Content01=method gold householdsyet to be rehear credit details
L185M01a.Type1Content02=the year until query not at the deadline for a review of corporate banking username singlebaseline date is
L185M01a.Type1Content03=Query Date\uff1a

L185M01a.Type1Title01=borrower name
L185M01a.Type1Title02=borrower ID
L185M01a.Type1Title03=Credit Limit Serial Number
L185M01a.Type1Title04=Credit Category
L185M01a.Type1Title05=Balance
L185M01a.Type1Title06=outstanding balance
L185M01a.Type1Title07=Credit period
L185M01a.Type1Title08=Is the major cases of borrowers
L185M01a.Type1Title09=Credit Grade
L185M01a.Type1Title10=Last review date
L185M01a.Type1Title11=The last review date
L185M01a.Type1Title12=Remarks
		
		
L185M01a.Type1End01=Remarks:
L185M01a.Type1End02=A.Review Once A Year
L185M01a.Type1End03=B. Review Once Every Six Months
L185M01a.Type1End04=C.New households / increase in households.
L185M01a.Type1End05=D.Abnormal households have three months to review - and later six months review.
L185M01a.Type1End06=E.Account with issue - 1st report. (A review must be completed with 3 months after the initial report)
L185M01a.Type1End07=F.Accountant issued a qualified opinion has three months to review - and later six months review.
L185M01a.Type1End08=G.Account with auditor's qualified opinion - 1st report. (A review must be completed with 3 months after the initial report)
L185M01a.Type1End09=H.Authority-specified Review Cases
#==================================================
# \u8986\u5be9-\u7ba1\u7406\u5831\u8868 thickbox
#==================================================
L185M01a.type=Report Name

title1=Data Date
title2=Generate a list of corporate accounts which did not complete credit reviews within the specified deadline
title3=Generate list of corporate accounts that are not subjected to credit review
title4=Generate New Customer/Limit Increase Overdue Checklist
title5=Generate Recent Credit Applications Checklist

search1=Please input the credit review year/month of the [Sent Credit Review Worksheet] you wish to import
search2=Please input the starting and ending year/month for New/Limit Increase case query
search3=Data Date
search4=Date Range For List Generation
search5=Please Select or enter data date

noInfo=No data selected
newInfo=Latest Data
chooseDate=Select Date
chooseBank=Select Branch
chooseBank2=Please select a branch list

L1855v01.startDate=Start Date
L1855v01.endDate=End Date
L1855v01.error=The Former Data Year/Month can not greater than the latter Data Year/Month

type2xls.content01=Corporate banking username single
type2xls.content02=Does not appear in the total at the end to rehear the operating system of corporate gold username single
type2xls.content03=Branch
type2xls.content04=Data Date
type2xls.content05=Syndicated loan farmers
type2xls.content06=Note:The table to do comparison of all \u300ce-Loan corporate customers review list \u300d and \u300cOBS AS/400 Account Data\u300d, display has not yet appeared in the "e-Loan Corp. Reveiw" list.
type2xls.content07=Of corporate customers list
type2xls.content08=Branch Code
type2xls.content09=Customer's unified business number
type2xls.content10=Account Name
type2xls.content11=Credit Limit Serial Number
type2xls.content12=Currency
type2xls.content13=Credit Limit
type2xls.content14=Credit period
type2xls.content15=Remarks


type3xls.content01=Review Checklist handle the amount of loan within six months after
type3xls.content02=Enterprises families new to do or increase the amount of credit cases within six months after the amount of loan, apply for cover trial and overdue checklist
type3xls.content03=Query Date
type3xls.content04=Information on starting and ending period
type3xls.content05=Branch Code
type3xls.content06=Branch
type3xls.content07=UBN
type3xls.content08=Account Name
type3xls.content09=Credit Limit Serial No.
type3xls.content10=Currency
type3xls.content11=Credit Limit
type3xls.content12=Credit period (or the Drawdown  of period)
type3xls.content13=New or increased the amount of years
type3xls.content14=Review for the first time year / month / day
type3xls.content15=Late Note
type3xls.content16=Note
type3xls.content17=(Not yet review)

type4xls.content01=Recent credit review details and overdue checklist
type4xls.content02=Apply for corporate customers credit review details and overdue check table
type4xls.content03=Branch Code\uff1a
type4xls.content04=Query Date\uff1a
type4xls.content05=Abnormal Credit Reporting
type4xls.content06=New
type4xls.content07=Limit Increase
type4xls.content08=Customer's unified business number
type4xls.content09=Account Name
type4xls.content10=The credit rating of the date of the last rehear / household / rehear cycle
type4xls.content11=Mainly households
type4xls.content12=Credit Grade
type4xls.content13=Review Cycles
type4xls.content14=Last Review Date
type4xls.content15=Last review date
type4xls.content16=The DBU / OBU shared quota
type4xls.content17=Late Note
type4xls.content18=Remarks


crdTypeDefault=Old Ratings
