package com.mega.eloan.lms.lms.service.impl;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.base.common.BranchRate;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.OverSeaUtil;
import com.mega.eloan.lms.base.constants.ScoreAU;
import com.mega.eloan.lms.base.constants.ScoreTH;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.pages.AbstractOverSeaCLSPage;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.ScoreServiceTH;
import com.mega.eloan.lms.dao.C120M01ADao;
import com.mega.eloan.lms.dao.C120S01ADao;
import com.mega.eloan.lms.dao.C120S01BDao;
import com.mega.eloan.lms.dao.C120S01CDao;
import com.mega.eloan.lms.dao.C120S01DDao;
import com.mega.eloan.lms.dao.C120S01EDao;
import com.mega.eloan.lms.dao.C121M01ADao;
import com.mega.eloan.lms.dao.C121M01DDao;
import com.mega.eloan.lms.dao.C121M01HDao;
import com.mega.eloan.lms.dao.C121S01ADao;
import com.mega.eloan.lms.dao.L120S01MDao;
import com.mega.eloan.lms.dao.L120S01NDao;
import com.mega.eloan.lms.dao.L120S01ODao;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.lms.pages.LMS1035M01Page;
import com.mega.eloan.lms.lms.service.LMS1035Service;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.C120S01B;
import com.mega.eloan.lms.model.C120S01C;
import com.mega.eloan.lms.model.C120S01E;
import com.mega.eloan.lms.model.C121M01A;
import com.mega.eloan.lms.model.C121M01D;
import com.mega.eloan.lms.model.C121M01H;
import com.mega.eloan.lms.model.C121S01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Arithmetic;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;


@Service("LMS1035Service")
public class LMS1035ServiceImpl extends AbstractCapService implements
		LMS1035Service {

	private static Logger logger = LoggerFactory
			.getLogger(LMS1035ServiceImpl.class);
	@Resource
	CLSService clsService;
	
	@Resource
	LMSService lmsService;
	
	@Resource
	DocLogService docLogService;
	
	@Resource
	TempDataService tempDataService;
	
	@Resource
	DocFileService docFileService;

	@Resource
	C120M01ADao c120m01aDao;
	
	@Resource
	C120S01ADao c120s01aDao;
	
	@Resource
	C120S01BDao c120s01bDao;

	@Resource
	C120S01CDao c120s01cDao;

	@Resource
	C120S01DDao c120s01dDao;
	
	@Resource
	C120S01EDao c120s01eDao;
	
	@Resource
	C121M01ADao c121m01aDao;
	
	@Resource
	C121S01ADao c121s01aDao;
	
	@Resource
	C121M01DDao c121m01dDao;
	
	@Resource
	C121M01HDao c121m01hDao;
	
	@Resource
	ScoreServiceTH scoreServiceTH;
	
	@Resource
	L120S01MDao l120s01mDao;
	
	@Resource
	L120S01NDao l120s01nDao;

	@Resource
	L120S01ODao l120s01oDao;

	@Resource
	DwdbBASEService dwdbBASEService;
	
	@Resource
	EloandbBASEService eloandbBASEService;
	
	@Override
	public String checkIncompleteMsg(C121M01A meta, List<String> adjReasonCnt, LinkedHashMap<String, String> adjReasonCfmMap){
		C121S01A c121s01a = clsService.findC121S01A(meta);	
		Properties prop_lms1035m01 = MessageBundleScriptCreator.getComponentResource(LMS1035M01Page.class);
		Properties prop_abstractOverSeaCLS = MessageBundleScriptCreator.getComponentResource(AbstractOverSeaCLSPage.class);
		List<C120M01A> c120m01a_list = clsService.findC120M01A_ByC121M01A_orderBy_keymanCustposCustid(meta);
		List<C120M01A> c120m01a_shouldRating_list = clsService.filter_shouldRating(c120m01a_list);
				
		if(true){			
			List<String> panel1 = new ArrayList<String>();//文件資訊
			if(true){
				List<String> overRange = new ArrayList<String>();
				if (true) {
					if(meta.getLnYear()!=null && (meta.getLnYear()<0)){
						overRange.add(prop_lms1035m01.getProperty("tab01.lnPeriod")+"-"+prop_lms1035m01.getProperty("tab01.lnYear"));
					}
					if(meta.getLnMonth()!=null && (meta.getLnMonth()<0||meta.getLnMonth()>=12)){
						overRange.add(prop_lms1035m01.getProperty("tab01.lnPeriod")+"-"+prop_lms1035m01.getProperty("tab01.lnMonth"));
					}
					OverSeaUtil.add_empty_to_list(panel1, meta.getLnYear(), prop_lms1035m01.getProperty("tab01.lnPeriod")+"-"+prop_lms1035m01.getProperty("tab01.lnYear"));
					OverSeaUtil.add_empty_to_list(panel1, meta.getLnMonth(), prop_lms1035m01.getProperty("tab01.lnPeriod")+"-"+prop_lms1035m01.getProperty("tab01.lnMonth"));
					
					if(Util.equals("2", meta.getRepaymentSchFmt())){
						OverSeaUtil.add_empty_to_list(panel1, meta.getRepaymentSchDays(), prop_lms1035m01.getProperty("tab01.repaymentSch"));
					}
					if(OverSeaUtil.valid_RepaymentSchDays_LoanTenor(meta)==false){
						panel1.add(prop_lms1035m01.getProperty("tab01.repaymentSchDays.invalid"));
					}
				}
				
				for(String colDesc :overRange){
					HashMap<String, String> msg = new HashMap<String, String>();
					msg.put("colName", colDesc);
					panel1.add(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.輸入位數超過, msg));
				}									
			}
			List<String> panel2 = new ArrayList<String>();//本案關係人基本資料
			if(true){
				int hasKeyMan = 0;
				for(C120M01A c120m01a: c120m01a_list){
					if(Util.equals("Y", c120m01a.getKeyMan())){
						hasKeyMan++;
					}
				}
				if(hasKeyMan==1){
					for(C120M01A c120m01a: c120m01a_list){
						if(Util.notEquals("Y", c120m01a.getO_chkYN())){
							panel2.add(MessageFormat.format(prop_lms1035m01.getProperty("msg.004")
									, c120m01a.getCustId()+"-"+c120m01a.getDupNo()) );
							continue;
						}
						
						if( Util.equals("Y", c120m01a.getKeyMan())){
							
						}else{
							//非主借人，要有 與主要借款人關係
							if(Util.isEmpty(Util.trim(c120m01a.getO_custRlt()))){
								panel2.add(MessageFormat.format(prop_lms1035m01.getProperty("msg.004")
										, c120m01a.getCustId()+"-"+c120m01a.getDupNo()+" "+prop_lms1035m01.getProperty("l120s01a.custrlt")) );
								continue;
							}
							
							if(Util.isEmpty(Util.trim(c120m01a.getCustPos()))){
								panel2.add(MessageFormat.format(prop_lms1035m01.getProperty("msg.004")
										, c120m01a.getCustId()+"-"+c120m01a.getDupNo()+" "+prop_lms1035m01.getProperty("l120s01a.custpos")) );
								continue;
							}
						}
					}	
				}else{
					panel2.add(MessageFormat.format(prop_lms1035m01.getProperty("msg.004"), prop_lms1035m01.getProperty("C121M01A.custId")) );
				}				
			}
			
			List<String> panel3 = new ArrayList<String>();//本案關係人合計資料
			
			List<String> panel4 = new ArrayList<String>();//擔保品資料
			if(true){

				if(c121s01a==null || Util.isEmpty(Util.trim(c121s01a.getCmsType()))){
					OverSeaUtil.add_empty_to_list(panel4, "", prop_lms1035m01, "C121S01A.cmsType");
				}
				if(c121s01a!=null){
				OverSeaUtil.add_empty_to_list(panel4, c121s01a.getSecurityRate(), prop_lms1035m01, "C121S01A.securityRate");
				
					if(Util.equals("1", c121s01a.getCmsType())){
						OverSeaUtil.add_empty_to_list(panel4, c121s01a.getLocation(), prop_lms1035m01, "C121S01A.location");
						OverSeaUtil.add_empty_to_list(panel4, c121s01a.getHouseAge(), prop_lms1035m01, "C121S01A.houseAge");
						OverSeaUtil.add_empty_to_list(panel4, c121s01a.getHouseArea(), prop_lms1035m01, "C121S01A.houseArea");						
					}
				}
			}
			
			List<String> panel5 = new ArrayList<String>();//NCB Credit Report資訊
			if(true){
				
			}
			
			List<String> panel6 = new ArrayList<String>();//主觀評等更新
			if(true){
				LinkedHashMap<String, String> adjReasonMap = new LinkedHashMap<String, String>();
				LinkedHashMap<String, String> adjReasonErrMap = new LinkedHashMap<String, String>();
								
				for(C120M01A c120m01a: c120m01a_shouldRating_list){
					//泰國2.0模型不得調整評等
					//**********要測一下!!!!! 順便看一下澳洲有沒有改錯
//					if(true){
					C121M01D c121m01d = clsService.findC121M01D_byC120M01A(c120m01a);
					if(LMSUtil.get_TH_BRNO_SET().contains(c120m01a.getOwnBrId())){ //分行屬於泰國地區.才有NCB Repoert
						if(c121m01d==null || Util.isEmpty(Util.trim(c121m01d.getNoAdj()))){
							panel6.add(MessageFormat.format(prop_lms1035m01.getProperty("msg.004")
									, c120m01a.getCustId()+"-"+c120m01a.getDupNo()+" "+prop_lms1035m01.getProperty("tab06.desc03")) );
						}
						if(c121m01d==null){
							continue;
						}
						if(Util.equals(c121m01d.getNoAdj(),"2")){
							if(Util.isEmpty(Util.trim(c121m01d.getAdjustStatus()))){
								panel6.add(MessageFormat.format(prop_lms1035m01.getProperty("msg.004")
										, c120m01a.getCustId()+"-"+c120m01a.getDupNo()+" "+prop_abstractOverSeaCLS.getProperty("message.adjustStatus")) );
							}	
							if(Util.equals(c121m01d.getSRating(), c121m01d.getFRating())){
								panel6.add(MessageFormat.format(prop_lms1035m01.getProperty("msg.004")
										, c120m01a.getCustId()+"-"+c120m01a.getDupNo()+" "+prop_abstractOverSeaCLS.getProperty("message.adjustLevel")) );
							}
							if(Util.equals(c121m01d.getAdjustStatus(),"1") && Util.isEmpty(Util.trim(c121m01d.getAdjustFlag()))){
								panel6.add(MessageFormat.format(prop_lms1035m01.getProperty("msg.004")
										, c120m01a.getCustId()+"-"+c120m01a.getDupNo()+" "+prop_abstractOverSeaCLS.getProperty("message.adjustFlag")) );
							}
							if(Util.isEmpty(Util.trim(c121m01d.getAdjustReason()))){
								panel6.add(MessageFormat.format(prop_lms1035m01.getProperty("msg.004")
										, c120m01a.getCustId()+"-"+c120m01a.getDupNo()+" "+prop_abstractOverSeaCLS.getProperty("message.adjustReason")) );
							}
						}
					}
//					}
					//(TH)切換 tab 頁面，可能使不完整的 data 存進DB
					String adjustReason = Util.trim(c121m01d.getAdjustReason());
					if(Util.isNotEmpty(adjustReason)){
						adjReasonMap.put(c120m01a.getCustId()+"-"+c120m01a.getDupNo()+" "+c120m01a.getCustName()
								, adjustReason);						
					}
				}
				
				if(true){
					adjReasonCnt.add(String.valueOf(adjReasonMap.size()));
					
					clsService.validate_adjustReason(adjReasonMap, adjReasonErrMap, adjReasonCfmMap);
					
					if(adjReasonErrMap.size()>0){
						for(String idDupName : adjReasonErrMap.keySet()){
							panel6.add(idDupName+" "+adjReasonErrMap.get(idDupName));
						}
					}					
				}
			}			
			
			List<String> panel7 = new ArrayList<String>();//評等等級
			if(true){
				for(C120M01A c120m01a: c120m01a_shouldRating_list){
					C121M01D c121m01d = clsService.findC121M01D_byC120M01A(c120m01a);
					if(c121m01d==null||c121m01d.getFRating()==null||CrsUtil.isNull_or_ZeroDate(c121m01d.getRatingDate())){
						panel7.add(MessageFormat.format(prop_lms1035m01.getProperty("msg.001"), c120m01a.getCustId()+"-"+c120m01a.getDupNo()) );	
					}
				}
			}
			
			List<String> r = new ArrayList<String>();
			add_errMsg(r, prop_lms1035m01.getProperty("tab.01"), panel1);
			add_errMsg(r, prop_lms1035m01.getProperty("tab.02"), panel2);
			add_errMsg(r, prop_lms1035m01.getProperty("tab.03"), panel3);
			add_errMsg(r, prop_lms1035m01.getProperty("tab.04"), panel4);
			add_errMsg(r, prop_lms1035m01.getProperty("tab.05"), panel5);
			add_errMsg(r, prop_lms1035m01.getProperty("tab.06"), panel6);
			
			String msg = StringUtils.join(r, "<br>");
			if(Util.isNotEmpty(msg)){
				logger.trace("checkIncompleteMsg:"+msg);
				return msg;
			}	
		}
		//已在 should_calc_C121_score(...) 裡，有檢核 varVer 不一致的狀況
		return "";
	}
	
	private void add_errMsg(List<String> errMsg, String panelTitle, List<String> panelErrMsg){
		if(panelErrMsg.size()>0){
			errMsg.add("【"+panelTitle+"】");
			for(String m : panelErrMsg){
				errMsg.add("&nbsp;&nbsp;&nbsp;&nbsp;"+m);	
			}
		}
	}
	

	@Override	
	public void delRatingDocCust(C121M01A c121m01a, C120M01A c120m01a){
		C121M01D c121m01d = clsService.findC121M01D_byC120M01A(c120m01a);
		if(c121m01d!=null){
			c121m01dDao.delete(c121m01d);	
		}
		//亞洲模型2.0新增C121M01H有就一起刪 
		C121M01H c121m01h = clsService.findC121M01H_byC120M01A(c120m01a);
		if(c121m01h!=null){
			c121m01hDao.delete(c121m01h);	
		}
		
		//-------------
		clsService.delC120Relate(c120m01a);
	}
	
	private Map<String, BigDecimal> _item_p3_map(BranchRate branchRate
			, List<Map<String, Object>> dw_fxrth_list
			, List<C120M01A> c120m01a_list, String[] posArr){
		Map<String, BigDecimal> r = new HashMap<String, BigDecimal>();
		
		for(C120M01A c120m01a : c120m01a_list){
			String idDup = LMSUtil.getCustKey_len10custId(c120m01a.getCustId(), c120m01a.getDupNo());
			BigDecimal p3_idv = BigDecimal.ZERO;
			if(true){
				boolean choose = false;
				if(Util.equals("Y", c120m01a.getKeyMan())){
					choose = true;
				}else {
					choose = CrsUtil.inCollection(c120m01a.getCustPos(), posArr);					
				}	
				if(choose){
					C120S01B c120s01b = clsService.findC120S01B(c120m01a);
					C120S01C c120s01c = clsService.findC120S01C(c120m01a);
					if(c120s01b!=null){
						//填入 年薪
						String curr = Util.trim(c120s01b.getPayCurr());
						BigDecimal amt = c120s01b.getPayAmt();
						
						p3_idv = p3_idv.add(proc_p3_idv(branchRate, dw_fxrth_list, curr, amt));
					}
					if(c120s01c!=null){
						//填入 其它收入
						String curr = Util.trim(c120s01c.getOMoneyCurr());
						BigDecimal amt = c120s01c.getOMoneyAmt();
						
						p3_idv = p3_idv.add(proc_p3_idv(branchRate, dw_fxrth_list, curr, amt));
					}			
				}
			}
			r.put(idDup, p3_idv);			
		}		
		return r;
	}
	
	/**
	 * 取到 整數位
	 * @param branchRate
	 * @param dw_fxrth_list
	 * @param curr
	 * @param amt
	 * @return
	 */
	private BigDecimal proc_p3_idv(BranchRate branchRate , List<Map<String, Object>> dw_fxrth_list
			, String curr, BigDecimal amt){
		if(Util.isEmpty(curr) || amt==null || OverSeaUtil.eqBigDecimal(amt, BigDecimal.ZERO)){
			return BigDecimal.ZERO;
		}
		
		BigDecimal exRate = _proc_exRate(curr, branchRate, dw_fxrth_list);
		return Arithmetic.div(amt.multiply(exRate ), BigDecimal.ONE, 0);
	}
	
	@Override
	public void calc_C121_score(C121M01A meta)
	throws CapException{
		List<C120M01A> c120m01a_list = clsService.findC120M01A_ByC121M01A_orderBy_keymanCustposCustid(meta);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		if(CollectionUtils.isEmpty(c120m01a_list)){
			return;
		}
		List<C121M01D> c121m01d_list = new ArrayList<C121M01D>();
		
		if(true){
			BranchRate branchRate = lmsService.getBranchRate(meta.getCaseBrId());
			List<Map<String, Object>> dw_fxrth_list = dwdbBASEService.findDW_FXRTH_LatestRate();
			String varVer = scoreServiceTH.get_Version_TH();
			
			Map<String, BigDecimal> p3_idv_map_MG = _item_p3_map(branchRate, dw_fxrth_list, c120m01a_list
					, new String[]{UtilConstants.lngeFlag.連帶保證人});
			Map<String, BigDecimal> p3_idv_map_MCG = _item_p3_map(branchRate, dw_fxrth_list, c120m01a_list
					, new String[]{UtilConstants.lngeFlag.共同借款人, UtilConstants.lngeFlag.連帶保證人});
			
			Set<String> idDup11Set = OverSeaUtil.fetch_idDup11(c120m01a_list);
			Map<String, C121M01D> idDup_C121M01D_map = clsService.findIdDup_C121M01D(meta.getMainId(), idDup11Set);
			for(C120M01A c120m01a : c120m01a_list ){
				String custId = c120m01a.getCustId();
				String dupNo = c120m01a.getDupNo();
				String brNo = c120m01a.getOwnBrId();

				if(clsService.custPosHasRating(c120m01a)){
					C120S01A c120s01a = clsService.findC120S01A(c120m01a);
					C120S01B c120s01b = clsService.findC120S01B(c120m01a);
					C120S01C c120s01c = clsService.findC120S01C(c120m01a);
					C120S01E c120s01e = clsService.findC120S01E(c120m01a);
					C121S01A c121s01a = clsService.findC121S01A(meta);
					C121M01D c121m01d = idDup_C121M01D_map.get(LMSUtil.getCustKey_len10custId(custId, dupNo));
					//~~~~~~
					JSONObject fetch_score_src = fetch_score_src(branchRate,dw_fxrth_list,meta, 
							c121s01a, c120m01a, c120s01a, c120s01b, c120s01c, c120s01e
							, p3_idv_map_MG, p3_idv_map_MCG, varVer);
					
					if(Util.equals(OverSeaUtil.C121M01A_MOW_TYPE_泰國, meta.getMowType())){
						
						if(c121m01d==null){
							c121m01d = new C121M01D();
							OverSeaUtil.copyC121M01D(c121m01d, meta, custId, dupNo);
						}
						//重製數值(C121M01D)
						this.initC121m01d(c121m01d);
						
						JSONObject score_TH = new JSONObject();							
						score_TH.putAll(fetch_score_src);
						score_TH.putAll(scoreServiceTH.scoreTH(ScoreTH.type.泰國消金模型基本, fetch_score_src, varVer, OverSeaUtil.海外評等_房貸, brNo));
						DataParse.toBean(score_TH, c121m01d);
						//------
						c121m01d_list.add(c121m01d);
					}					
				}				
			}
		}
		
		if(c121m01d_list.size()>0){
			//XXX 重算分數時，不應該用 tempSave
			SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
			//~~~~~~
			List<GenericBean> saved_list = new ArrayList<GenericBean>();
			Timestamp nowTS = CapDate.getCurrentTimestamp();
			String creator = user.getUserId();
			for(C121M01D c121m01d: c121m01d_list){
				c121m01d.setCreator(creator);
				c121m01d.setCreateTime(nowTS);
				c121m01d.setUpdater(null);
				c121m01d.setUpdateTime(null);
				//===
				saved_list.add(c121m01d);
			}
			if(true){
				//以 C121M01D 的評等日期、版本為主
				C121M01D c121m01d = c121m01d_list.get(0);
				String varVer = c121m01d.getVarVer();
				
				meta.setRatingDate(null);
				meta.setVarVer(varVer);
				//------
				saved_list.add(meta);
			}
			clsService.daoSave(saved_list);
		}
	}
	
	@Override
	public void calc_C121_score_V2_0(C121M01A meta)
	throws CapException{
		List<C120M01A> c120m01a_list = clsService.findC120M01A_ByC121M01A_orderBy_keymanCustposCustid(meta);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		if(CollectionUtils.isEmpty(c120m01a_list)){
			return;
		}
		List<GenericBean> saved_list = new ArrayList<GenericBean>();
		
		if(true){
			BranchRate branchRate = lmsService.getBranchRate(meta.getCaseBrId());
			List<Map<String, Object>> dw_fxrth_list = dwdbBASEService.findDW_FXRTH_LatestRate();
			String varVer = scoreServiceTH.get_Version_TH();
			Timestamp nowTS = CapDate.getCurrentTimestamp();
			String creator = user.getUserId();
			
			Map<String, BigDecimal> p3_idv_map_MG = _item_p3_map(branchRate, dw_fxrth_list, c120m01a_list
					, new String[]{UtilConstants.lngeFlag.連帶保證人});
			Map<String, BigDecimal> p3_idv_map_MCG = _item_p3_map(branchRate, dw_fxrth_list, c120m01a_list
					, new String[]{UtilConstants.lngeFlag.共同借款人, UtilConstants.lngeFlag.連帶保證人});
			
			for(C120M01A c120m01a : c120m01a_list ){
				String custId = c120m01a.getCustId();
				String dupNo = c120m01a.getDupNo();
				String brNo = c120m01a.getOwnBrId();

				if(clsService.custPosHasRating(c120m01a)){
					C120S01A c120s01a = clsService.findC120S01A(c120m01a);
					C120S01B c120s01b = clsService.findC120S01B(c120m01a);
					C120S01C c120s01c = clsService.findC120S01C(c120m01a);
					C120S01E c120s01e = clsService.findC120S01E(c120m01a);
					C121S01A c121s01a = clsService.findC121S01A(meta);
					
					//1.0版本無區分房貸非房貸，2.0開始拆分C121M01D=房貸、C121M01H=非房貸
					C121M01D c121m01d = clsService.findC121M01D_byC120M01A(c120m01a);
//					C121M01D c121m01d = idDup_C121M01D_map.get(LMSUtil.getCustKey_len10custId(custId, dupNo));
					C121M01H c121m01h = clsService.findC121M01H_byC120M01A(c120m01a);
					
					//~~~~~~
					JSONObject fetch_score_src = fetch_score_src(branchRate,dw_fxrth_list,meta, 
							c121s01a, c120m01a, c120s01a, c120s01b, c120s01c, c120s01e
							, p3_idv_map_MG, p3_idv_map_MCG, varVer);
					
					if(Util.equals(OverSeaUtil.C121M01A_MOW_TYPE_泰國, meta.getMowType())){
						if(c121m01d==null){
							c121m01d = new C121M01D();
							OverSeaUtil.copyC121M01D(c121m01d, meta, custId, dupNo);
						}
						//重製數值(C121M01D)
						this.initC121m01d(c121m01d);
						
						JSONObject score_TH = new JSONObject();							
						score_TH.putAll(fetch_score_src);
						score_TH.putAll(scoreServiceTH.scoreTH(ScoreTH.type.泰國消金模型基本, fetch_score_src, varVer, OverSeaUtil.海外評等_房貸, brNo));
						DataParse.toBean(score_TH, c121m01d);
						c121m01d.setCreator(creator);
						c121m01d.setCreateTime(nowTS);
						c121m01d.setUpdater(creator);
						c121m01d.setUpdateTime(nowTS);
						c121m01d.setRatingDate(nowTS);
						//2.0不可做評等調整，故noAdj[註記不需調整]，直接寫[1]=是
						c121m01d.setNoAdj("1");
						
						//------
						saved_list.add(c121m01d);
						
						//處理非房貸部份
						if(c121m01h==null){
							c121m01h = new C121M01H();
							OverSeaUtil.copyC121M01H(c121m01h, meta, custId, dupNo);
						}
						//重製數值(C121M01G)					
						JSONObject score_AU_NotHouse = new JSONObject();							
						score_AU_NotHouse.putAll(fetch_score_src);
						score_AU_NotHouse.putAll(scoreServiceTH.scoreTH(ScoreTH.type.泰國消金模型基本, fetch_score_src, varVer, OverSeaUtil.海外評等_非房貸, brNo));
						DataParse.toBean(score_AU_NotHouse, c121m01h);
						c121m01h.setCreator(creator);
						c121m01h.setCreateTime(nowTS);
						c121m01h.setUpdater(creator);
						c121m01h.setUpdateTime(nowTS);
						c121m01h.setRatingDate(nowTS);
						//2.0不可做評等調整，故noAdj[註記不需調整]，直接寫[1]=是
						c121m01h.setNoAdj("1");
						//------
						saved_list.add(c121m01h);
					}					
				}				
			}
			meta.setRatingDate(nowTS);
			meta.setVarVer(varVer);
			//避免評等升版時，缺乏國別資料
			if(Util.isEmpty(Util.trim(meta.getMowTypeCountry()))){
				meta.setMowTypeCountry(OverSeaUtil.getMowTypeCountry(meta.getCaseBrId()));
			}
			//------
			saved_list.add(meta);
		}
		clsService.daoSave(saved_list);
		
	}
	
	private JSONObject fetch_score_src(BranchRate branchRate, List<Map<String, Object>> dw_fxrth_list
			, C121M01A meta, C121S01A c121s01a
			, C120M01A c120m01a
			, C120S01A c120s01a, C120S01B c120s01b
			, C120S01C c120s01c, C120S01E c120s01e
			, Map<String, BigDecimal> p3_idv_map_MG, Map<String, BigDecimal> p3_idv_map_MCG, String varVer)
	throws CapException{
		JSONObject fetch_score_src = new JSONObject();
		
		JSONObject json_c120s01b = DataParse.toJSON(c120s01b);
		JSONObject json_c120s01c = DataParse.toJSON(c120s01c);
		JSONObject json_c120s01e = DataParse.toJSON(c120s01e);
		JSONObject json_c121s01a = DataParse.toJSON(c121s01a);

		fetch_score_src.put(ScoreTH.column.NCB查詢日期, json_c120s01e.get("ncbQDate") );
				
		String chkItemTHG1 = _get_chkItemTHG1(c120s01e);
		String chkItemTHG2 = _get_chkItemTHG2(c120s01e);
		String chkItemTHS1 = _get_chkItemTHS1(c120s01e);
		String chkItemTHO1 = _get_chkItemTHO1(c120m01a);
		String chkItemTHO2 = _get_chkItemTHO2(c120s01e);
		String chkItemTHO3 = _get_chkItemTHO3(chkItemTHO1, chkItemTHO2);
		
		fetch_score_src.put(ScoreTH.column.TH一般警訊1, chkItemTHG1);
		fetch_score_src.put(ScoreTH.column.TH一般警訊2, chkItemTHG2);
		fetch_score_src.put(ScoreTH.column.TH特殊警訊1, chkItemTHS1);
		fetch_score_src.put(ScoreTH.column.TH其他資訊1, chkItemTHO1);
		fetch_score_src.put(ScoreTH.column.TH其他資訊2, chkItemTHO2);
		fetch_score_src.put(ScoreTH.column.TH其他資訊3, chkItemTHO3);
		
		String raw_payCurr = Util.trim(c120s01b.getPayCurr());
		BigDecimal raw_payAmt = c120s01b.getPayAmt();
		BigDecimal exRate_pay = _proc_exRate(raw_payCurr, branchRate, dw_fxrth_list);
		
		String raw_otherCurr = Util.trim(c120s01c.getOMoneyCurr());
		BigDecimal raw_otherAmt = c120s01c.getOMoneyAmt();
		BigDecimal exRate_oth = _proc_exRate(raw_otherCurr, branchRate, dw_fxrth_list);
				
		String raw_hincomeCurr = Util.trim(c120s01c.getYFamCurr());		
		BigDecimal exRate_hincome = _proc_exRate(raw_hincomeCurr, branchRate, dw_fxrth_list);
				
		String raw_invMBalCurr = Util.trim(c120s01c.getInvMBalCurr());
		BigDecimal exRate_invMBal = _proc_exRate(raw_invMBalCurr, branchRate, dw_fxrth_list);
		
		String raw_invOBalCurr = Util.trim(c120s01c.getInvOBalCurr());
		BigDecimal exRate_invOBal = _proc_exRate(raw_invOBalCurr, branchRate, dw_fxrth_list);
		
		String raw_branAmtCurr = Util.trim(c120s01c.getBranCurr());
		BigDecimal exRate_branAmt = _proc_exRate(raw_branAmtCurr, branchRate, dw_fxrth_list);
		
		String raw_totMmInCurr = Util.trim(c120s01c.getTotMmInCurr());
		BigDecimal exRate_totMmIn = _proc_exRate(raw_totMmInCurr, branchRate, dw_fxrth_list);
		
		String raw_totMmExpCurr = Util.trim(c120s01c.getTotMmExpCurr());
		BigDecimal exRate_totMmExp = _proc_exRate(raw_totMmExpCurr, branchRate, dw_fxrth_list);
		
		String item_m1 = "";
		Date raw_m1 = null;
		BigDecimal p3 = BigDecimal.ZERO;
		BigDecimal raw_p3_idv = p3_idv_map_MCG.get(LMSUtil.getCustKey_len10custId(c120m01a.getCustId(), c120m01a.getDupNo()));
		String raw_p3_pos = _get_raw_p3_pos(c120m01a);
		Integer raw_a5 = null;
		String item_a5 = ""; 
		String p3_curr = Util.trim(c120s01c.getYFamCurr());
		
		if(true){		
			if(true){
				//P3意義(借款人及連保人之年收入合計)
				for(String idDup : p3_idv_map_MG.keySet()){
					p3 = p3.add( p3_idv_map_MG.get(idDup) );
				}
				p3 = Arithmetic.div(p3, BigDecimal.ONE, 0);
			}
			
			if(true){
				raw_a5 = OverSeaUtil.get_raw_a5(meta);
				
				if(raw_a5!=null){					
					int a5_scale = 2;//小數點後2位
					item_a5 = String.valueOf(Arithmetic.div(new BigDecimal(raw_a5), new BigDecimal(12), a5_scale) );	
				}				
			}
		}
				
		fetch_score_src.put(ScoreTH.column.因子M5_職業, json_c120s01b.get("jobType1"));
		fetch_score_src.put(ScoreTH.column.因子M7_年資, json_c120s01b.get("seniority"));
		
		if(true){			
			fetch_score_src.put(ScoreTH.column.年薪幣別, raw_payCurr);
			if(raw_payAmt==null){
				fetch_score_src.put(ScoreTH.column.年薪金額, "");
			}else{
				fetch_score_src.put(ScoreTH.column.年薪金額, raw_payAmt);	
			}			
			fetch_score_src.put(ScoreTH.column.轉換匯率_年薪, Util.trim(exRate_pay));
			
			fetch_score_src.put(ScoreTH.column.其它收入幣別, raw_otherCurr);
			if(raw_otherAmt==null){
				fetch_score_src.put(ScoreTH.column.其它收入金額, "");
			}else{
				fetch_score_src.put(ScoreTH.column.其它收入金額, raw_otherAmt);	
			}			
			fetch_score_src.put(ScoreTH.column.轉換匯率_其他收入, Util.trim(exRate_oth));
			
			fetch_score_src.put(ScoreTH.column.家庭所得幣別, raw_hincomeCurr);
			fetch_score_src.put(ScoreTH.column.轉換匯率_家庭所得, Util.trim(exRate_hincome));
			
			fetch_score_src.put(ScoreTH.column.財富管理_本行幣別 , raw_invMBalCurr);
			fetch_score_src.put(ScoreTH.column.轉換匯率_財富管理本行, Util.trim(exRate_invMBal));
			
			fetch_score_src.put(ScoreTH.column.財富管理_它行幣別 , raw_invOBalCurr);
			fetch_score_src.put(ScoreTH.column.轉換匯率_財富管理它行, Util.trim(exRate_invOBal));
			
			fetch_score_src.put(ScoreTH.column.金融機構存款往來情形幣別 , raw_branAmtCurr);
			fetch_score_src.put(ScoreTH.column.轉換匯率_金融機構存款往來情形, Util.trim(exRate_branAmt));
			
			fetch_score_src.put(ScoreTH.column.每月個人收入幣別 , raw_totMmInCurr);
			fetch_score_src.put(ScoreTH.column.轉換匯率_每月個人收入, Util.trim(exRate_totMmIn));
			
			fetch_score_src.put(ScoreTH.column.每月個人支出幣別 , raw_totMmExpCurr);
			fetch_score_src.put(ScoreTH.column.轉換匯率_每月個人支出, Util.trim(exRate_totMmExp));
			
			fetch_score_src.put(ScoreTH.column.因子P3_夫妻年收入_幣別, Util.trim(p3_curr));
			fetch_score_src.put(ScoreTH.column.因子P3_借款人及連保人之年收入合計, Util.trim(p3));
			fetch_score_src.put(ScoreTH.column.個人年收入P3, raw_p3_idv);
			fetch_score_src.put(ScoreTH.column.身份別P3, raw_p3_pos);
		}
		
		fetch_score_src.put(ScoreTH.column.因子P4_個人負債比率, json_c120s01c.get("dRate"));
		
		if(true){
			fetch_score_src.put(ScoreTH.column.月份數A5, Util.trim(raw_a5));
			fetch_score_src.put(ScoreTH.column.因子A5_契約年限, item_a5 );
		}
		
		fetch_score_src.put(ScoreTH.column.因子Z1, json_c121s01a.get("factor1") );
		fetch_score_src.put(ScoreTH.column.因子Z2, json_c121s01a.get("factor2") );
		
		if(Util.equals(varVer, OverSeaUtil.V2_0_LOAN_TH)){ //2.0模型，無Z3因子，新增M1、EDU因子
			if(true){
				raw_m1 = OverSeaUtil.get_raw_m1(c120s01a);
				item_m1 = Util.trim(OverSeaUtil.getAge(raw_m1));
				fetch_score_src.put(ScoreTH.column.出生日M1, Util.trim(TWNDate.toAD(raw_m1)));
				fetch_score_src.put(ScoreTH.column.因子M1, item_m1);
				fetch_score_src.put(ScoreAU.column.因子edu, c120s01a.getEdu());
			}
		}else{
			if(true){						
				BigDecimal val = _get_item_z3(c121s01a);			
				if(val==null){
					fetch_score_src.put(ScoreTH.column.因子Z3_擔保率, "");	
				}else{
					fetch_score_src.put(ScoreTH.column.因子Z3_擔保率, val);
				}			
			}
		}
		
		return fetch_score_src;
	}
	
	/**
	 * 取得的匯率可能是 3.3783783784
	 * 但在 eloan 只存到小數點後5位
	 * 
	 * 若直接用 branchRate.toLocalAmt(curr, amt)
	 * 可能會和 amt*匯率 的值，有一些差異 
	 */
//	private BigDecimal _proc_exRate(String inputCurr, BranchRate branchRate){
//		if(Util.isEmpty(Util.trim(inputCurr))){
//			return null;
//		}
//		int _scale = 5;//小數點後5位
//		return Arithmetic.div(branchRate.toLocalRate(inputCurr), BigDecimal.ONE, _scale);		
//	}
	
	private BigDecimal _proc_exRate(String inputCurr, BranchRate branchRate, List<Map<String, Object>> dw_fxrth_list){
		int _scale = 5;//小數點後5位
		return clsService.proc_exRate(inputCurr, branchRate.getMCurr(), _scale, dw_fxrth_list);
	}
	
	private String _get_raw_p3_pos(C120M01A c120m01a){
		if(Util.equals("Y", c120m01a.getKeyMan())){
			return OverSeaUtil.M;
		}else{
			String custPos = Util.trim(c120m01a.getCustPos());
			if(Util.isNotEmpty(custPos)){
				return custPos;
			}
		}
		return "-";
	}
	
	/**
	 * 在擔保品資料檔放 100.14, 但在評等檔放 1.0014
	 * @param c121s01a
	 * @return
	 */
	private BigDecimal _get_item_z3(C121S01A c121s01a){
		if(c121s01a!=null && c121s01a.getSecurityRate()!=null){
			int z3_scale = 5;
			return Arithmetic.div(c121s01a.getSecurityRate(), new BigDecimal(100), z3_scale) ;
		}
		return null;
	}
	
	private String _get_chkItemTHG1(C120S01E c120s01e){		
		return _get_chkItemTH_maxdpd(c120s01e, "1");
	}
	private String _get_chkItemTHG2(C120S01E c120s01e){
		return _get_chkItemTH_maxdpd(c120s01e, "2");
	}
	private String _get_chkItemTHS1(C120S01E c120s01e){		
		return _get_chkItemTH_maxdpd(c120s01e, "3");
	}
	//---
	private String _get_chkItemTH_maxdpd(C120S01E c120s01e, String val){
		if(Util.equals(UtilConstants.haveNo.有, c120s01e.getNcbRecord())){
			if(Util.equals(UtilConstants.haveNo.有, c120s01e.getNcbMaximumDpdFlag())){
				if(Util.equals(c120s01e.getNcbMaximumDpd(), val)){
					return UtilConstants.haveNo.有;
				}else{
					return UtilConstants.haveNo.無;	
				}
			}else if(Util.equals(UtilConstants.haveNo.無, c120s01e.getNcbMaximumDpdFlag())){
				return UtilConstants.haveNo.無;			
			}
			return UtilConstants.haveNo.NA;
		}else{
			return UtilConstants.haveNo.NA;
		}
	}	
	
	private String _get_chkItemTHO1(C120M01A c120m01a){
		String custPos = Util.trim(c120m01a.getCustPos());
		if(Util.isEmpty(custPos) || Util.equals(custPos, "M")){
			return UtilConstants.haveNo.有;
		}else{
			return UtilConstants.haveNo.無;				
		}
	}
	
	private String _get_chkItemTHO2(C120S01E c120s01e){
		if(Util.equals(UtilConstants.haveNo.有, c120s01e.getNcbRecord())){
			return UtilConstants.haveNo.有;
		}else{
			return UtilConstants.haveNo.無;	
		}
	}
	
	/**
	 * 在 chkItemTHO1 判斷是否主借人, 在 chkItemTHO2 是否有NCB信用報告 
	 * @param chkItemTHO1
	 * @param chkItemTHO2
	 * @return
	 */
	private String _get_chkItemTHO3(String chkItemTHO1, String chkItemTHO2){		
		if(Util.equals(UtilConstants.haveNo.有, chkItemTHO1) 
				&& Util.equals(UtilConstants.haveNo.無, chkItemTHO2) ){
			
			return UtilConstants.haveNo.有;
		}else{
			return UtilConstants.haveNo.無;	
		}
	}
	
	private boolean isCommonFactorChg(C121M01A meta, Integer c121m01_grade_a5
			, Integer c121m01_grade_z1, Integer c121m01_grade_z2
			, BigDecimal c121m01_z3){
		
		if(Util.notEquals(Util.trim(c121m01_grade_a5), Util.trim(OverSeaUtil.get_raw_a5(meta)))){	
			return true;
		}
		C121S01A c121s01a = clsService.findC121S01A(meta);
		if(c121s01a==null){
			return true;
		}else{			
			if(Util.notEquals(Util.trim(c121m01_grade_z1), Util.trim(c121s01a.getFactor1()))){
				return true;
			}
			if(Util.notEquals(Util.trim(c121m01_grade_z2), Util.trim(c121s01a.getFactor2()))){
				return true;
			}
			if(!OverSeaUtil.eqBigDecimal(c121m01_z3, _get_item_z3(c121s01a))){
				return true;
			}
		}
		
		return false;
	}
	
	private boolean isCustFactorChg(C121M01A meta, C120M01A c120m01a, C121M01D c121m01d,
			C120S01B c120s01b, C120S01C c120s01c, C120S01E c120s01e){
		try{
			isCustFactorChg_str(meta, c120m01a, c121m01d,
					 c120s01b, c120s01c, c120s01e);
			return false;
		}catch(CapException r){
			debug("isCustFactorChg【"+r.getMessage()+"】");
			return true;
		}		
	}
	
	private void isCustFactorChg_str(C121M01A meta, C120M01A c120m01a, C121M01D c121m01d,
			C120S01B c120s01b, C120S01C c120s01c, C120S01E c120s01e)
	throws CapException{			
		C120S01A c120s01a = clsService.findC120S01A(c120m01a);
		//M5_occupation
		if(true){
			String exist_item_m5 = c121m01d.getItem_m5();
			String item_m5 = c120s01b.getJobType1();
			cmp_diff("m5", exist_item_m5, item_m5);
		}
		//M7_seniority
		if(true){
//			Integer exist_item_m7 = c121m01d.getItem_m7();
//			Integer m7 = c120s01b.getSeniority();
//			cmp_diff("m7", Util.trim(exist_item_m7), Util.trim(m7));
			if(!OverSeaUtil.eqBigDecimal(c121m01d.getItem_m7(), c120s01b.getSeniority())){
				throw new CapException("m7["+c121m01d.getItem_m7()+" , "+ c120s01b.getSeniority()+"]", getClass());
			}	
		}
		//P3_total_income
		if(true){
			//比較【主借人/連保人合計】 且比較【每一個人的年薪、其他收入】
			cmp_diff("raw_payCurr", c121m01d.getRaw_payCurr(), c120s01b.getPayCurr());
			cmp_diff("raw_otherCurr", c121m01d.getRaw_otherCurr(), c120s01c.getOMoneyCurr());
			cmp_diff("raw_hincomeCurr", c121m01d.getRaw_hincomeCurr(), c120s01c.getYFamCurr());			
			cmp_diff("raw_invMBalCurr", c121m01d.getRaw_invMBalCurr(), c120s01c.getInvMBalCurr());
			cmp_diff("raw_invOBalCurr", c121m01d.getRaw_invOBalCurr(), c120s01c.getInvOBalCurr());
			cmp_diff("raw_branAmtCurr", c121m01d.getRaw_branAmtCurr(), c120s01c.getBranCurr());
			cmp_diff("raw_totMmInCurr", c121m01d.getRaw_totMmInCurr(), c120s01c.getTotMmInCurr());
			cmp_diff("raw_totMmExpCurr", c121m01d.getRaw_totMmExpCurr(), c120s01c.getTotMmExpCurr());
			
			BigDecimal raw_payAmt = c120s01b.getPayAmt();
			BigDecimal raw_otherAmt = c120s01c.getOMoneyAmt();
				
			if(!OverSeaUtil.eqBigDecimal(c121m01d.getRaw_payAmt(), raw_payAmt)){
				throw new CapException("raw_payAmt["+c121m01d.getRaw_payAmt()+" , "+ raw_payAmt+"]", getClass());
			}
			if(!OverSeaUtil.eqBigDecimal(c121m01d.getRaw_otherAmt(), raw_otherAmt)){
				throw new CapException("raw_otherAmt["+c121m01d.getRaw_otherAmt()+" , "+ raw_otherAmt+"]", getClass());
			}	
		
			if(true){
				cmp_diff("raw_p3_pos", c121m01d.getRaw_p3_pos(), _get_raw_p3_pos(c120m01a));
			}
		}
		//P4_drate
		if(true){
			if(!OverSeaUtil.eqBigDecimal(c121m01d.getItem_p4(), c120s01c.getDRate())){
				throw new CapException("drate["+c121m01d.getItem_p4()+" , "+ c120s01c.getDRate()+"]", getClass());
			}
		}
		
		//模型2.0要再判斷[學歷]、[M1]
		String varVer = Util.trim(meta.getVarVer());
		if(Util.equals(varVer, OverSeaUtil.V2_0_LOAN_TH)){
			//M1_age
			if(true){
				Date exist_raw_m1 = c121m01d.getRaw_m1();
				Date raw_m1 = OverSeaUtil.get_raw_m1(c120s01a);
				
				if(!OverSeaUtil.eqDate(exist_raw_m1, raw_m1)){				
					throw new CapException("raw_m1["+exist_raw_m1+" , "+ raw_m1+"]", getClass());
				}
			}
		}
		
		// 負面資訊  異動，影響 NCB Credit Report 資訊[一般警訊、特殊警訊]
		cmp_diff("chkItemTHG1", c121m01d.getChkItemTHG1(), _get_chkItemTHG1(c120s01e));
		cmp_diff("chkItemTHG2", c121m01d.getChkItemTHG2(), _get_chkItemTHG2(c120s01e));
		cmp_diff("chkItemTHS1", c121m01d.getChkItemTHS1(), _get_chkItemTHS1(c120s01e));
		cmp_diff("chkItemTHO1", c121m01d.getChkItemTHO1(), _get_chkItemTHO1(c120m01a));
		cmp_diff("chkItemTHO2", c121m01d.getChkItemTHO2(), _get_chkItemTHO2(c120s01e));
		cmp_diff("chkItemTHO3", c121m01d.getChkItemTHO3(), _get_chkItemTHO3(_get_chkItemTHO1(c120m01a), _get_chkItemTHO2(c120s01e)));		
	}	
	
	private void cmp_diff(String desc, String a, String b)throws CapException{
		if(Util.notEquals(a, b)){
			throw new CapException(desc+"["+a+" , "+b+"]", getClass());
		}
	}
	
	@Override	
	public void del_noneRating_score(C121M01A meta){		
		List<C120M01A> noneRating_list = clsService.filter_noneRating(clsService.findC120M01A_ByC121M01A_orderBy_keymanCustposCustid(meta));
		
		List<C121M01D> delList_D = new ArrayList<C121M01D>();		
		List<C121M01H> delList_H = new ArrayList<C121M01H>();		
		for(C120M01A c120m01a : noneRating_list ){			
			C121M01D c121m01d = clsService.findC121M01D_byC120M01A(c120m01a);
			if(c121m01d!=null){
				delList_D.add(c121m01d);				
			}
			C121M01H c121m01h = clsService.findC121M01H_byC120M01A(c120m01a);
			if(c121m01h!=null){
				delList_H.add(c121m01h);				
			}
		}
		
		if(delList_D.size()>0){
			
			/*
			在 SimpleContextHolder 放 N，應該對 dao 沒有影響
			SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
			 */
			c121m01dDao.delete(delList_D);	
			//--------------
			tempDataService.deleteByMainId(meta.getMainId());	
		}
		if(delList_H.size()>0){
			c121m01hDao.delete(delList_H);	
		}
	}
	
	@Override	
	public boolean should_calc_C121_score(int page, C121M01A meta){
		List<C120M01A> shouldRating_list = new ArrayList<C120M01A>();
		if(true){
			List<C120M01A> src_list = clsService.findC120M01A_ByC121M01A_orderBy_keymanCustposCustid(meta);
			shouldRating_list = clsService.filter_shouldRating(src_list);			
		}
		Set<String> idDup11Set = OverSeaUtil.fetch_idDup11(shouldRating_list);
		String debugStr = "should_calc_C121_score==TRUE";
		//房貸非房貸的版號、因子都是一樣的，因此這邊可以統一規則
		String varVerNow = scoreServiceTH.get_Version_TH(); //目前適用之評等版本
		String meta_varVer = Util.trim(meta.getVarVer()); //資料本來的評等版本
		
		Map<String, C121M01D> idDup_C121M01D_map = clsService.findIdDup_C121M01D(meta.getMainId(), idDup11Set);
		Map<String, C121M01H> idDup_C121M01H_map = clsService.findIdDup_C121M01H(meta.getMainId(), idDup11Set);
		
		if(true){
			if(Util.notEquals(varVerNow, meta_varVer)){
				return true;
			}else{
				Set<String> varVerSet = new HashSet<String>();
				if(Util.isNotEmpty(Util.trim(meta.getVarVer()))){
					varVerSet.add(Util.trim(meta.getVarVer()));
				}			
				
				for(C120M01A c120m01a : shouldRating_list ){			
					C121M01D c121m01d = idDup_C121M01D_map.get(LMSUtil.getCustKey_len10custId(c120m01a.getCustId(), c120m01a.getDupNo()));
					if(c121m01d==null){
						debug(debugStr+"["+c120m01a.getCustId()+"-"+c120m01a.getDupNo()+"]lost c121m01d");
						//應有評等，而無資料
						return true;	
					}
					//當 varVer 有不一致時，也要重算
					String varVer_d = Util.trim(c121m01d.getVarVer());
					if(Util.isNotEmpty(varVer_d)){
						varVerSet.add(varVer_d);
					}
					//================
					if(Util.equals(varVerNow, OverSeaUtil.V2_0_LOAN_TH)){ //泰國模型2.0
						C121M01H c121m01h = idDup_C121M01H_map.get(LMSUtil.getCustKey_len10custId(c120m01a.getCustId(), c120m01a.getDupNo()));
						if(c121m01h==null){
							debug(debugStr+"["+c120m01a.getCustId()+"-"+c120m01a.getDupNo()+"]lost c121m01h");
							//應有評等，而無資料
							return true;	
						}else{
							String varVer_h = Util.trim(c121m01d.getVarVer());
							if(Util.isNotEmpty(varVer_h)){
								varVerSet.add(varVer_h);
							}
						}
					}
				}	
				
				if(varVerSet.size()>1){
					debug(debugStr+"["+varVerSet+"]varVerSet.size()>1");
					return true;
				}
			}
		}
			
		if(page==1 || page==4){			
			if(shouldRating_list.size()>0){
				//各 custId 共用的因子
				C120M01A c120m01a = shouldRating_list.get(0);
				C121M01D c121m01d = idDup_C121M01D_map.get(LMSUtil.getCustKey_len10custId(c120m01a.getCustId(), c120m01a.getDupNo()));
				
				if(isCommonFactorChg(meta, c121m01d.getRaw_a5()
						, c121m01d.getItem_z1(), c121m01d.getItem_z2()
						, c121m01d.getItem_z3())){
					debug(debugStr+"[isCommonFactorChg=true]");
					return true;	
				}	
			}									
		}
		if(page==2){
			
			Map<String, C120S01B> idDup_C120S01B_map = clsService.findIdDup_C120S01B(meta.getMainId(), idDup11Set);
			Map<String, C120S01C> idDup_C120S01C_map = clsService.findIdDup_C120S01C(meta.getMainId(), idDup11Set);
			Map<String, C120S01E> idDup_C120S01E_map = clsService.findIdDup_C120S01E(meta.getMainId(), idDup11Set);
			
			for(C120M01A c120m01a: shouldRating_list){
				String idDup = LMSUtil.getCustKey_len10custId(c120m01a.getCustId(), c120m01a.getDupNo());
				C121M01D c121m01d = idDup_C121M01D_map.get(idDup);
				C120S01B c120s01b = idDup_C120S01B_map.get(idDup);
				C120S01C c120s01c = idDup_C120S01C_map.get(idDup);
				C120S01E c120s01e = idDup_C120S01E_map.get(idDup);
				
				if(isCustFactorChg(meta, c120m01a, c121m01d, c120s01b, c120s01c, c120s01e)){
					debug(debugStr+"["+c120m01a.getCustId()+"-"+c120m01a.getDupNo()+"][isCustFactorChg=true]");
					return true;
				}				
			}
		}
		return false;
	}
	private void debug(String s){
		//logger.debug(s);
	}

	@Override	
	public Page<Map<String, Object>> queryPrint(String mainId, ISearch pageSetting) throws CapException{
		
		List<Map<String, Object>> beanList = new ArrayList<Map<String, Object>>();
		C121M01A c121m01a = clsService.findC121M01AByMainId(mainId);
		
		Properties prop = MessageBundleScriptCreator.getComponentResource(LMS1035M01Page.class);
		for(C120M01A c120m01a: clsService.findC120M01A_ByC121M01A_orderBy_keymanCustposCustid(c121m01a)){
			Map<String, Object> data = new HashMap<String, Object>();
			
			data.put("oid", Util.trim(c120m01a.getOid()));
			data.put("mainId", Util.trim(c120m01a.getMainId()));
			data.put("type", "1");
			data.put("desc1", prop.getProperty("printItem1"));//借款人基本資料報表
			data.put("desc2", Util.trim(c120m01a.getCustId())+"-"+Util.trim(c120m01a.getDupNo())+" "+Util.trim(c120m01a.getCustName()));
			beanList.add(data);
		}
		if(true){
			Map<String, Object> data = new HashMap<String, Object>();
			
			data.put("oid", Util.trim(c121m01a.getOid()));
			data.put("mainId", Util.trim(c121m01a.getMainId()));
			data.put("type", "2");
			/*
			 * JP: Personal Credit Rating Sheet
			 * AU: Individual Credit Scorecard
			 */
			data.put("desc1", prop.getProperty("printItem2"));
			data.put("desc2", Util.trim(c121m01a.getCaseNo()));
			beanList.add(data);
		}
		
		return LMSUtil.getMapGirdDataRow(beanList, pageSetting);	
	}
	
	private void initC121m01d(C121M01D c121m01d){ 
		//重製評等相關欄位(共用欄位不用)
		c121m01d.setPRating(null);
		c121m01d.setSRating(null);
		c121m01d.setSprtRating(null);
		c121m01d.setAdjRating(null);
		c121m01d.setFRating(null);
		c121m01d.setOrgFr(null);
		c121m01d.setNoAdj(null);
		c121m01d.setAdjustStatus(null);
		c121m01d.setAdjustFlag(null);
		c121m01d.setAdjustReason(null);
		c121m01d.setDr_1yr(null);
		c121m01d.setDr_3yr(null);
		c121m01d.setPd(null);
		c121m01d.setSlope(null);
		c121m01d.setInterCept(null);
		
		c121m01d.setWeight_scr_m5(null);
		c121m01d.setWeight_scr_m7(null);
		c121m01d.setWeight_scr_p3(null);
		c121m01d.setWeight_scr_p4(null);
		c121m01d.setWeight_scr_a5(null);
		c121m01d.setWeight_scr_z1(null);
		c121m01d.setWeight_scr_z2(null);
		c121m01d.setWeight_scr_edu(null);
		c121m01d.setWeight_scr_m1(null);
		
		c121m01d.setStd_m5(null);
		c121m01d.setStd_m7(null);
		c121m01d.setStd_p3(null);
		c121m01d.setStd_a5(null);
		c121m01d.setStd_z1(null);
		c121m01d.setStd_z2(null);
		c121m01d.setStd_z3(null);
		c121m01d.setStd_p4(null);
		c121m01d.setStd_core(null);
		
		c121m01d.setWeight_edu(null);
		c121m01d.setItem_edu(null);
		c121m01d.setScr_edu(null);
		c121m01d.setWeight_m1(null);
		c121m01d.setItem_m1(null);
		c121m01d.setScr_m1(null);
		c121m01d.setWeight_z3(null);
		c121m01d.setItem_z3(null);
		c121m01d.setScr_z3(null);
		
	}
	
	
}