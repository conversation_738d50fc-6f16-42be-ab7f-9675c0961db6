/* 
 * DwLnquotovService.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dw.service;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

@Service
public interface DWAslndavgovsService {

	/**
	 * 個金覆審更新-查詢是否循環
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	List<?> findAslndavgovsJoinLnquotov(String custId, String dupNo);

	/**
	 * 個金覆審更新-判斷是否為不動產
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	List<?> findAslndavgovsSelQuotano(String custId, String dupNo);

	/**
	 * 個金覆審更新-判斷是否大於500萬
	 * 
	 * @param branch
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	List<?> findLnquotovJoinAslndavgovs(String branch, String custId,
			String dupNo);

	/**
	 * 個金覆審更新-排除逾催呆戶
	 * 
	 * @param loanNo
	 * @return
	 */
	List<?> findAslndavgovsSelStatus(String loanNo);

	/**
	 * 個金產生覆審名單-搜尋帳務資料
	 * 
	 * @param custId
	 * @param dupNo
	 * @param contract
	 * @return
	 */
	List<?> findLnquotovSelFactAmt(String custId, String dupNo, String contract);

	/**
	 * 個金產生資料-搜尋放款餘額
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	List<?> findLnquotovSelLnbal(String custId, String dupNo);

	/**
	 * 個金產生名單-搜尋會計科目
	 * 
	 * @param loanNo
	 * @return
	 */
	List<?> findAslndavqovsSelActcd(String loanNo);

	Map<String, Object> findLnquotovSelRevovle(String custId, String dupNo);

	public List<Map<String, Object>> getByCustIdBrNo(String custId, String brCd);

	Map<String, Object> findSumFact(String custId, String dupNo);

	/**
	 * 取餘額+額度+戶況 判斷是否要覆審(個金)
	 * 
	 * @param custId
	 * @param dupNo
	 * @param branchId
	 * @return
	 */
	List<Map<String, Object>> findLNQUOTOVselFACT_AMT_T(String custId,
			String dupNo, String branchId);

	/**
	 * 搜尋戶況
	 * @param custId
	 * @param dupNo
	 * @param branch
	 * @return
	 */
	Map<String, Object> findCstate(String custId, String dupNo, String branch);
	
	/**
	 * 引進海外前一日授信明細
	 * <ol>資信簡表\ 授信往來情形\引入前一日授信明細(申貸戶授信往來情形明細(附表))
	 * <ol>引進前一日之授信額度及餘額資料
	 * @param custId String
	 * @param dupNo String
	 * @return List<Map<String, Object>>
	 */
	List<Map<String, Object>> findPreviousDayCreditDetailByCustId(String custId, String dupNo);	
}
