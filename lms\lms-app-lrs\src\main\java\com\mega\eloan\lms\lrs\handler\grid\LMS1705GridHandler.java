/* 
 * LMS1705GridHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lrs.handler.grid;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import com.iisigroup.cap.component.PageParameters;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.formatter.CodeTypeFormatter;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.dao.L170M01ADao;
import com.mega.eloan.lms.dao.L170M01BDao;
import com.mega.eloan.lms.dao.L170M01CDao;
import com.mega.eloan.lms.lrs.service.LMS1705Service;
import com.mega.eloan.lms.model.L170M01A;
import com.mega.eloan.lms.model.L170M01B;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

@Scope("request")
@Controller("lms1705gridhandler")
public class LMS1705GridHandler extends AbstractGridHandler {

	@Resource
	LMS1705Service service;

	@Resource
	L170M01ADao dao;

	@Resource
	L170M01BDao daob;

	@Resource
	L170M01CDao daoc;

	@Resource
	CodeTypeService codeTypeService;

	@Resource
	LMS1705Service service1705;

	@Resource
	UserInfoService userInfoService;

	/**
	 * 查詢Grid 資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	public CapMapGridResult queryL170m01a(ISearch pageSetting,
			PageParameters params) throws CapException {
		String docStatus = Util.nullToSpace(params.getString("docStatus"));
		String retrialDate1 = Util
				.nullToSpace(params.getString("retrialDate1"));
		String retrialDate2 = Util
				.nullToSpace(params.getString("retrialDate2"));
		String custId = Util.nullToSpace(params.getString("custId"));
		//J-108-0163_10702_B1001 配合授審處於企金及消金「授信覆審系統」增加「戶名」搜尋欄位
		String custName = Util.nullToSpace(params.getString("custName"));
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		Page<Map<String, Object>> page = service.findL170m01AJoin(docStatus,
				user.getUnitNo(), retrialDate1, retrialDate2, custId,custName,
				pageSetting);
		List<Map<String, Object>> list = page.getContent();
		for (Map<String, Object> map : list) {
			// if ("1".equals(map.get("nCkdFlag"))
			// || "3".equals(map.get("nCkdFlag"))) {
			// map.put("nCkdFlag","Y");
			// } else {
			// map.put("nCkdFlag","N");
			// }
			String projectNo = Util.nullToSpace(map.get("projectNo"));
			map.put("projectNo",
					Util.isEmpty(projectNo) ? "" : projectNo.substring(
							projectNo.length() - 8, projectNo.length() - 1));
			map.put("updater", Util.trim(userInfoService.getUserName(Util
					.trim(map.get("updater")))));

		}
		return new CapMapGridResult(list, page.getTotalRow());
	}

	/**
	 * 查詢Grid 資料(L170m01b)
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */

	@SuppressWarnings("unchecked")
	public CapGridResult queryL170m01b(ISearch pageSetting,
			PageParameters params) throws CapException {

		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));

		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);

		Page<? extends GenericBean> page = service.findPage(L170M01B.class,
				pageSetting);
		List<L170M01B> list = (List<L170M01B>) page.getContent();
		for (L170M01B l170m01b : list) {
			if (l170m01b.getLnDataDate() != null) {
				l170m01b.setCreator("");
			} else {
				l170m01b.setCreator("Y");
			}
			if (l170m01b.getQuotaAmt() != null) {
				l170m01b.setQuotaAmt(l170m01b.getQuotaAmt().divide(
						new BigDecimal(1000)));
			}
			if (l170m01b.getBalAmt() != null) {
				l170m01b.setBalAmt(l170m01b.getBalAmt().divide(
						new BigDecimal(1000)));
			}
		}
		// 加入格式化
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());

		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("newCase", new CodeTypeFormatter(codeTypeService,
				"lms1705s02_newCase")); // codeType格式化
		dataReformatter.put("loanTP", new CodeTypeFormatter(codeTypeService,
				"lms1405m01_SubItem")); // codeType格式化

		// dataReformatter.put("quotaCurr", new
		// CodeTypeFormatter(codeTypeService,
		// "Common_Currcy")); // codeType格式化
		// dataReformatter.put("balCurr", new CodeTypeFormatter(codeTypeService,
		// "Common_Currcy")); // codeType格式化

		result.setDataReformatter(dataReformatter);
		return result;

	}

	/**
	 * 財務及業務資料(L170m01b)
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	public CapMapGridResult queryCESF101(ISearch pageSetting,
			PageParameters params) throws CapException {

		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		L170M01A l170m01a = service.findModelByMainId(L170M01A.class, mainId);

		String brNo = Util.trim(l170m01a.getOwnBrId());

		String custId = Util.trim(l170m01a.getCustId());
		String dupNo = Util.trim(l170m01a.getDupNo());

		// J-107-0045-001 Web e-Loan企金授信簽報書配合海外啟用IFRS徵信報告調整財報引進相關功能。
		List<Map<String, Object>> list = service1705.getCesf101(brNo, custId,
				dupNo);
		return new CapMapGridResult(list, list.size());

	}

	/**
	 * 查詢需列印的grid
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryPrint(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.setDistinct(true);
		Page<Map<String, Object>> page = service1705.getBorrows(mainId,
				pageSetting);
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

}
