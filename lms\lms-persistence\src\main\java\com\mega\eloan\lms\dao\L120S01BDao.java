package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S01B;


/** 企金基本資料檔 **/
public interface L120S01BDao extends IGenericDao<L120S01B> {

	L120S01B findByOid(String oid);
	
	List<L120S01B> findByMainId(String mainId);
	
	L120S01B findByUniqueKey(String mainId,String custId,String dupNo);
	
	int delModel(String mainId);
	
	List<L120S01B> findByCustIdDupId(String custId,String DupNo);
	
	List<L120S01B> findAllNoNoteUp();

}