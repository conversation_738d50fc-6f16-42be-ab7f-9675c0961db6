$(document).ready(function(){

    var grid = $("#gridview").iGrid({
        handler: 'lms7800gridhandler',
        height: 350,
        width: 785,
        autowidth: false,
        action: "queryL140mm6a",
        postData: {
            docStatus: viewstatus
        },
        rowNum: 15,
        sortname: "createTime|custId",
        sortorder: "desc|desc",
        multiselect: true,
        colModel: [{
	        colHeader: i18n.lms7800v01["L140MM6A.custId"], //借款戶統一編號
	        align: "left", width: 100, sortable: true, name: 'custId',
	        formatter: 'click', onclick: openDoc
	    }, {
	        colHeader: i18n.lms7800v01["L140MM6A.custName"], //借款戶名稱
	        align: "left", width: 100, sortable: true, name: 'custName'
	    },{
            colHeader: i18n.lms7800v01['L140MM6A.cntrNo'],//"額度序號",
            name: 'cntrNo',
            width: 100,
            sortable: true
        }, {
            colHeader: i18n.lms7800v01['L140MM6A.creator'],//"分行經辦",
            name: 'updater',
            width: 80,
            sortable: true,
            align: "center"
        }, {
                colHeader: i18n.lms7800v01["L140MM6A.createTime"], // 建立日期
                align: "left",
                width: 80, // 設定寬度
                sortable: true, // 是否允許排序
                name: 'createTime',
                formatter: 'date',
                formatoptions: {
                    srcformat: 'Y-m-d H:i:s',
                    newformat: 'Y-m-d H:i'
                }
        }, {
            name: 'oid',
            hidden: true
        }, {
            name: 'mainId',
            hidden: true
        }, {
            name: 'docURL',
            hidden: true
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = $("#gridview").getRowData(rowid);
            openDoc(null, null, data);
        }
    });
	
    function openDoc(cellvalue, options, rowObject){
        $.form.submit({			
            url: '..' + rowObject.docURL + '/01',
            data: {
                formAction: "queryL140mm6a",
                oid: rowObject.oid,
                mainId: rowObject.mainId,
                mainOid: rowObject.oid,
                mainDocStatus: viewstatus,
                txCode: txCode,
				cntrNo: rowObject.cntrNo
            },
            target: rowObject.oid
        });
    }
	
	
    $("#buttonPanel").find("#btnDelete").click(function(){
        var rows = $("#gridview").getGridParam('selarrrow');
        var data = [];
        
        if (rows == "") {// TMMDeleteError=請先選擇需修改(刪除)之資料列
            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
        }
        //confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                for (var i in rows) {
                    data.push($("#gridview").getRowData(rows[i]).oid);
                }
                
                $.ajax({
                    handler: "lms7800m01formhandler",
                    data: {
                        formAction: "deleteL140mm6a",
                        oids: data
                    },
                    success: function(obj){
                        $("#gridview").trigger("reloadGrid");
                    }
                });
            }
        });
    }).end().find("#btnAdd").click(function(){
		$("#addThickBox").thickbox({
			title : '',
			width : 150,
			height : 100,
			modal : true,
			align : 'center',
			valign: 'bottom',
			i18n: i18n.def,
			open : function() {
				$(this).find("#addForm").reset();
			},
			buttons : {
				'sure' : function(){
					var cntrNo = $("#cntrNo").val();
					if ($.trim(cntrNo) == "") {
						return CommonAPI.showMessage(i18n.lms7800v01["checkInput"]+i18n.lms7800v01["L140MM6A.cntrNo"]);
					}
					if (!cntrNo.match(/\w{12}/)) {
			            //額度序號長度應為12碼，編碼原則:XXX(分行代號)+X(1:DBU,4:OBU,5:海外)+YYY(年度)+99999(流水號)
			            return CommonAPI.showMessage(i18n.lms7800v01["L140MM6A.chkCntrNo"]);
			        }
					//檢查是否在L180R46A裡
					$.ajax({
	                    handler: "lms7800m01formhandler",
	                    action : 'checkCntrNo',
						data : {
							cntrNo: cntrNo
	   	 				},
	                    success: function(obj){
		        			if(obj.count == 0){
								return CommonAPI.showMessage(i18n.lms7800v01["L140MM6A.message01"]);
							} else {
								$.ajax({
				                    handler: "lms7800m01formhandler",
				                    action : 'newL140mm6a',
									data : {
										cntrNo: cntrNo
				   	 				},
				                    success: function(obj){
					        			$.form.submit({
				                    		url: '../fms/lms7800m01/01',
				                    		data: {
				                        		formAction: "queryL140mm6a",
				                        		oid: obj.oid,
				                        		mainOid: obj.oid,
				                        		mainDocStatus: viewstatus,
				                        		txCode: txCode,
												cntrNo: cntrNo,
												mainId: obj.mainId
				                    		},
				                    		target: obj.oid
				               	 		});
										$.thickbox.close();
									}
					    		});
							}
						}
		    		});
				},
				'close' : function(){
					$.thickbox.close();
				}
			}
		});
    });
});

