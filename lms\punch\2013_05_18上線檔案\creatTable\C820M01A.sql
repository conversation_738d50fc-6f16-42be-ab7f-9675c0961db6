---------------------------------------------------------
-- LMS.C820M01A 收集批覆書控制表
---------------------------------------------------------

---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.C820M01A;
CREATE TABLE LMS.C820M01A (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)     ,
	GROUPID       CHAR(3)      ,
	ISSELECTED    CHAR(1)      ,
	<PERSON><PERSON><PERSON>          CHAR(3)      ,
	BRNAME        VARCHAR(63)  ,
	<PERSON><PERSON>AY<PERSON>        CHAR(7)      ,
	CREATOR       CHAR(6)      ,
	CREATE<PERSON><PERSON>    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_C820M01A PRIMARY KEY(OID)
) IN EL_DATA_4KTS
  INDEX IN EL_INDEX_4KTS;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XC820M01A01;
CREATE INDEX LMS.XC820M01A01 ON LMS.C820M01A   (MAINID, GROUPID, CREATOR);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.C820M01A IS '收集批覆書控制表';
COMMENT ON LMS.C820M01A (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	GROUPID       IS '區域中心代碼', 
	ISSELECTED    IS '是否被選取', 
	BRNO          IS '分行代碼', 
	BRNAME        IS '分行名稱', 
	DATAYM        IS '欲產生之年月', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
