package com.mega.eloan.lms.etch.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.etch.service.MisSuccKeyMapService;

@Service
public class MisSuccKeyMapServiceImpl extends AbstractETchJdbc
    implements MisSuccKeyMapService
{

    public MisSuccKeyMapServiceImpl()
    {
    }

	/**
	 * 取得客戶票信查詢紀錄
	 * 
	 * @param custId
	 *            統編
	 * @return Map<String, Object>
	 */
	@Override
	public Map<String, Object> find4111ById(String custId) {
		return getJdbc().queryForMap("SUCCKEYMAP.find4111ById",
				new String[] { custId });
	}// ;

	@Override
	public Map<String, Object> find4112ById(String custId) {
		return getJdbc().queryForMap("SUCCKEYMAP.find4112ById",
				new String[] { custId });
	}// ;    
    
    @SuppressWarnings("rawtypes")
	public List findLastQDateById(String custId)
    {
        return getJdbc().queryForList("SUCCKEYMAP.findLastQDateById", new String[] {
            custId
        });
    }
}