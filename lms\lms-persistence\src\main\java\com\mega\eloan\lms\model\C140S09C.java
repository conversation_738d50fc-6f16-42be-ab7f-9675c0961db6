package com.mega.eloan.lms.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;


/**
 * <pre>
 * C140S09C model.
 * </pre>
 * 
 * @since 2011/10/27
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/10/27,<PERSON>,new</li>
 *          </ul>
 */
@NamedEntityGraph(name = "C140S09C-entity-graph", attributeNodes = { @NamedAttributeNode("c140m01a") })
@Entity
@Table(name="C140S09C", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class C140S09C extends com.mega.eloan.common.model.RelativeMeta implements Serializable {
	private static final long serialVersionUID = 1L;

    @Temporal( TemporalType.DATE)
	@Column(name="GRD_D1")
	private Date grdD1;

    //J-105-0080-001 Web e-Loan授信管理系統集團轄下公司名稱欄位放大為38個全形字
	@Column(name="GRD_NA1", length=120)
	private String grdNa1;

	//bi-directional many-to-one association to C140M01A
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumns({ @JoinColumn(name = "MAINID", referencedColumnName = "MAINID", nullable = false, insertable = false, updatable = false),
        @JoinColumn(name = "PID", referencedColumnName = "UID", nullable = false, insertable = false, updatable = false) })
	private C140M01A c140m01a;

	public Date getGrdD1() {
		return this.grdD1;
	}

	public void setGrdD1(Date grdD1) {
		this.grdD1 = grdD1;
	}

	public String getGrdNa1() {
		return this.grdNa1;
	}

	public void setGrdNa1(String grdNa1) {
		this.grdNa1 = grdNa1;
	}

	public C140M01A getC140m01a() {
		return this.c140m01a;
	}

	public void setC140m01a(C140M01A c140m01a) {
		this.c140m01a = c140m01a;
	}
	
}