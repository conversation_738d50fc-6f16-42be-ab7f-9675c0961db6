package com.mega.eloan.lms.fms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;



/**
 * <pre>
 * 		覆審考核表作業 - 待覆核
 * </pre>
 * 
 * @since 2022
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Controller@RequestMapping(path = "/fms/lms8100v02")
public class LMS8100V02Page extends AbstractEloanInnerView {

	public LMS8100V02Page() {
		super();
	}

	@Override
	public void  execute(ModelMap model, PageParameters params) {
		setGridViewStatus(CreditDocStatusEnum.海外_待覆核);
		
		// 加上Button
		addToButtonPanel(model, LmsButtonEnum.View);
		renderJsI18N(LMS8100V01Page.class);
		renderJsI18N(AbstractEloanPage.class);
	}

	public String[] getJavascriptPath() {
		return new String[] { "pagejs/fms/LMS8100V01Page.js" };
	}
}
