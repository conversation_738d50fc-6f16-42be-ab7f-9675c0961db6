<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:util="http://www.springframework.org/schema/util" xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:tx="http://www.springframework.org/schema/tx" xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
    	   http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-3.0.xsd
           http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.0.xsd
           http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd
           http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd">


	<!-- EAI GW Client -->
	<bean id="eaiGwClient" class="com.mega.eloan.common.gwclient.EaiGwClient">
		<!-- GW URL -->
		<!--<constructor-arg index="0" type="java.lang.String"
			value="http://*************:9081/hostdbconn/app/" /> -->
		<!-- 系統代碼 -->
		<constructor-arg index="0" type="java.lang.String" value="${systemId}" />
		<!-- Timeout (sec) -->
		<constructor-arg index="1" type="int" value="61" />
	</bean>
	
	<bean id="emailClient" class="com.mega.eloan.common.gwclient.EmailClient" />

	<!-- OBSMQ GW Client -->
	<bean id="oBSMqGwClient" class="com.mega.eloan.common.gwclient.OBSMqGwClient">
		<!-- GW URL -->
		<!-- <constructor-arg index="0" type="java.lang.String"
			value="http://*************:9084/mega-gateway/app/" /> -->
		<!-- 系統代碼 -->
		<constructor-arg index="0" type="java.lang.String" value="${systemId}" />
		<!-- Timeout (sec) -->
		<constructor-arg index="1" type="int" value="30" />
	</bean>

	<bean id="OBSDBJdbcContext" class="com.mega.eloan.common.jdbc.OBSDBJdbcContext"
		init-method="init">
 		<property name="dbcsTables">
			<list>
				<value>ELF164</value>
				<value>ELF383</value>
				<value>ELF422</value>
				<value>ELF461</value>
				<value>ELF476</value>
			</list>
		</property>
	</bean>

	<bean id="etchGwClient" class="com.mega.eloan.common.gwclient.ETCHGwClient">
		<!-- Timeout (sec) -->
		<constructor-arg index="0" type="int" value="30" />
	</bean>
	
	<bean id="ejcicGwClient" class="com.mega.eloan.common.gwclient.EJCICGwClient">
		<!-- Timeout (sec) -->
		<constructor-arg index="0" type="int" value="30" />
	</bean>

	<bean id="helpDeskClient" class="com.mega.eloan.common.gwclient.HelpDeskClient">
		<constructor-arg index="0" type="java.lang.String" value="${systemId}" />
		<!-- Timeout (sec) -->
		<constructor-arg index="1" type="int" value="30" />
	</bean>

	<bean id="monitorClient" class="com.mega.eloan.common.gwclient.MonitorClient">
		<property name="timeout" value="30" /><!-- Timeout(sec) -->
		<property name="appName" value="${systemId}" />
	</bean>
	
	<bean id="rServiceClient" class="com.mega.eloan.common.gwclient.RServiceClient">
		<constructor-arg index="0" type="int" value="65" />
	</bean>

	<bean id="eloanBatchClient" class="com.mega.eloan.common.gwclient.EloanBatchClient">
		<!-- 系統代碼 -->
		<constructor-arg index="0" type="java.lang.String" value="${systemId}" />
		<constructor-arg index="1" type="int" value="65" />
	</bean>
	
	<bean id="eloanHttpGetClient" class="com.mega.eloan.common.gwclient.EloanHttpGetClient">
		<!-- 系統代碼 -->
		<constructor-arg index="0" type="java.lang.String" value="${systemId}" />
		<constructor-arg index="1" type="int" value="65" />
	</bean>
	
	<bean id="arFtpClient" class="com.mega.eloan.common.gwclient.ARFTPClient">
		<!-- 系統代碼 -->
		<constructor-arg index="0" type="java.lang.String" value="${systemId}" />

		<!-- Timeout (sec) -->
		<constructor-arg index="1" type="int" value="65" />
	</bean>
	
	<bean id="uccFtpClient" class="com.mega.eloan.common.gwclient.UCCFTPClient">
		<!-- 系統代碼 -->
		<constructor-arg index="0" type="java.lang.String" value="${systemId}" />

		<!-- Timeout (sec) -->
		<constructor-arg index="1" type="int" value="65" />
	</bean>
	
	<bean id="rpqsFtpClient" class="com.mega.eloan.common.gwclient.RPQSFTPClient">
		<constructor-arg index="0" type="java.lang.String"
			value="LMS" />			
		<constructor-arg index="1" type="int" value="65" />
	</bean>
	
	<bean id="dwUCB1FTPClient" class="com.mega.eloan.common.gwclient.DWUCB1FTPClient">
		<constructor-arg index="0" type="java.lang.String" value="${systemId}" />

		<constructor-arg index="1" type="int" value="65" />
	</bean>
	
	<bean id="dwUFX1FTPClient" class="com.mega.eloan.common.gwclient.DWUFX1FTPClient">
		<constructor-arg index="0" type="java.lang.String" value="${systemId}" />

		<constructor-arg index="1" type="int" value="65" />
	</bean>
	
	<bean id="auditFTPClient" class="com.mega.eloan.common.gwclient.AUDITFTPClient">
		<constructor-arg index="0" type="java.lang.String" value="${systemId}" />

		<constructor-arg index="1" type="int" value="65" />
	</bean>
		
	<bean id="IVRGwClient" class="com.mega.eloan.common.gwclient.IVRGwClient">
		<constructor-arg index="0" type="java.lang.String" value="${systemId}" />

		<constructor-arg index="1" type="int" value="65" />
	</bean>
	<bean id="smegFtpClient" class="com.mega.eloan.common.gwclient.SMEGFTPClient">
		<constructor-arg index="0" type="java.lang.String" value="${systemId}" />
		<constructor-arg index="1" type="int" value="65" />
	</bean>
	
	<bean id="PLOANGwClient" class="com.mega.eloan.common.gwclient.PLOANGwClient">
		<constructor-arg index="0" type="java.lang.String" value="${systemId}" />
		<constructor-arg index="1" type="int" value="65" />
	</bean>		
	<bean id="BrmpGwClient" class="com.mega.eloan.common.gwclient.BrmpGwClient">
		<constructor-arg index="0" type="java.lang.String" value="${systemId}" />
		<constructor-arg index="1" type="int" value="65" />
	</bean>
	<bean id="MoneyTrustLearnGwClient" class="com.mega.eloan.common.gwclient.MoneyTrustLearnGwClient">
		<constructor-arg index="0" type="java.lang.String" value="${systemId}" />
		<constructor-arg index="1" type="int" value="65" />
	</bean>
	<bean id="IdentificationCheckGwClient" class="com.mega.eloan.common.gwclient.IdentificationCheckGwClient">
		<constructor-arg index="0" type="java.lang.String" value="${systemId}" />
		<constructor-arg index="1" type="int" value="65" />
	</bean>

	<bean id="rpaFtpClient" class="com.mega.eloan.common.gwclient.RPAFTPClient">
		<!-- 系統代碼 -->
		<constructor-arg index="0" type="java.lang.String" value="${systemId}" />

		<!-- Timeout (sec) -->
		<constructor-arg index="1" type="int" value="65" />
	</bean>
	
	<bean id="witcherFinGwClient" class="com.mega.eloan.common.gwclient.WitcherFinGwClient">
		<constructor-arg index="0" type="java.lang.String"
			value="LMS" />			
	</bean>
	
	<bean id="mEGAImageGwClient" class="com.mega.eloan.common.gwclient.MEGAImageGwClient">
		<constructor-arg index="0" type="java.lang.String"
			value="LMS" />			
	</bean>
	<bean id="mEGAIMAGEFTPClient" class="com.mega.eloan.common.gwclient.MEGAIMAGEFTPClient">
		<constructor-arg index="0" type="java.lang.String"
			value="LMS" />			
		<constructor-arg index="1" type="int" value="65" />
	</bean>
	<!--
	<bean id="eloanHttpGetClient" class="com.mega.eloan.common.gwclient.EloanHttpGetClient">
	-->
		<!-- 系統代碼 -->
	<!--
		<constructor-arg index="0" type="java.lang.String" value="${systemId}" />
		<constructor-arg index="1" type="int" value="65" />
	</bean>
	-->
	
	<!-- 資訊處管制科自動傳送聯徵FTP設定 -->
	<bean id="gdbAutoSendFtpClient" class="com.mega.eloan.common.gwclient.GDBAutoSendFTPClient">
		<!-- 系統代碼 -->
		<constructor-arg index="0" type="java.lang.String" value="${systemId}" />

		<!-- Timeout (sec) -->
		<constructor-arg index="1" type="int" value="65" />
	</bean>
	
	<!-- 資訊處管制科自動接收聯徵FTP設定 -->
	<bean id="gdbAutoRecvFtpClient" class="com.mega.eloan.common.gwclient.GDBAutoRecvFTPClient">
		<!-- 系統代碼 -->
		<constructor-arg index="0" type="java.lang.String" value="${systemId}" />

		<!-- Timeout (sec) -->
		<constructor-arg index="1" type="int" value="65" />
	</bean>

	<bean id="WiseNewsGwClient" class="com.mega.eloan.common.gwclient.WiseNewsGwClient">
		<constructor-arg index="0" type="java.lang.String" value="${systemId}" />
		<constructor-arg index="1" type="int" value="65" />
	</bean>

    <bean id="ssoWebServiceClient" class="com.mega.eloan.common.gwclient.SSOWebServiceClient">
        <!-- 系統代碼 -->
        <constructor-arg index="0" type="java.lang.String" value="${systemId}" />
		<constructor-arg index="1" type="int" value="65" />
    </bean>
	
</beans>