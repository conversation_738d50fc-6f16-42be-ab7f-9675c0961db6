$(function(){	
	Panel07Gridview01();
	Panel07Gridview02();
});

function Panel07Gridview01(){
    $("#Panel07Gridview01").iGrid({
		handler: 'lms1205gridhandler',
		height: 120, //for 10 筆
		width : "100%",
        autowidth:true,
        sortname : 'custName',
		postData : {
			formAction : "queryL120s05d",
			mainId : responseJSON.mainid,
			rowNum:10
		},
		caption: "&nbsp;",
		hiddengrid : false,
		rowNum:10,
		rownumbers:true,
        colModel: [{
            colHeader: i18n.lms1205s05["l120s05.grid11"],//"【關係企業】明細<br />(戶名最多顯示長度24個全形字)"
            name: 'custName',
            width: 180,
            sortable: false,
			align:"left"
        }, {
            colHeader: i18n.lms1205s05["l120s05.grid12"],//"授信總額度(海外)"
            name: 'totAmtB',
            width: 100,
            sortable: false,
			formatter : function(data) {
				if(data == null){
					return "";
				}else{
					// 加入撇節符號
					return util.addComma(data);	
				}
			},			
			align:"right"
        }, {
            colHeader: i18n.lms1205s05["l120s05.grid13"],//"授信總額度(台灣)"
            name: 'totAmtA',
            width: 100,
            sortable: false,
			formatter : function(data) {
				if(data == null){
					return "";
				}else{
					// 加入撇節符號
					return util.addComma(data);	
				}
			},			
			align:"right"
        }, {
            colHeader: i18n.lms1205s05["l120s05.grid14"],//"無擔保授信額度(海外)"
            name: 'crdAmtB',
            width: 100,
            sortable: false,
			formatter : function(data) {
				if(data == null){
					return "";
				}else{
					// 加入撇節符號
					return util.addComma(data);	
				}
			},			
			align:"right"
        }, {
            colHeader: i18n.lms1205s05["l120s05.grid15"],//"無擔保授信額度(台灣)"
            name: 'crdAmtA',
            width: 100,
            sortable: false,
			formatter : function(data) {
				if(data == null){
					return "";
				}else{
					// 加入撇節符號
					return util.addComma(data);	
				}
			},			
			align:"right"
        }, {
            colHeader: i18n.lms1205s05["l120s05.grid16"],//"扣除一～四項<br />授信總額度"
            name: 'excAmt',
            width: 100,
            sortable: false,
			formatter : function(data) {
				if(data == null){
					return "";
				}else{
					// 加入撇節符號
					return util.addComma(data);	
				}
			},			
			align:"right"
        }, {
            colHeader: i18n.lms1205s05["l120s05.grid17"],//"扣除一～四項<br />無擔保授信額度"
            name: 'excrdAmt',
            width: 100,
            sortable: false,
			formatter : function(data) {
				if(data == null){
					return "";
				}else{
					// 加入撇節符號
					return util.addComma(data);	
				}
			},			
			align:"right"
        }, {
            name: 'oid',
            hidden: true
        }],
        	ondblClickRow: function(rowid){
        }
    });
}

function uPanel07Gridview01(){
	   $("#Panel07Gridview01").jqGrid("setGridParam", {
		postData : {
			formAction : "queryL120s05d",
			mainId : responseJSON.mainId,
			rowNum:10
		},
		search: true
	   }).trigger("reloadGrid");
}
	   
function Panel07Gridview02(){
    $("#Panel07Gridview02").iGrid({
		handler: 'lms1205gridhandler',
		height: 120, //for 10 筆
		sortname : 'custName',
		postData : {
			formAction : "queryL120s05d",
			mainId : responseJSON.mainid,
			rowNum:10
		},
		caption: "&nbsp;",
		hiddengrid : false,
		rowNum:10,
		rownumbers:true,
        colModel: [{
            colHeader: i18n.lms1205s05["l120s05.grid18"],//"【關係企業】明細<br />(戶名最多顯示長度24個全形字)"
            name: 'custName',
            width: 180,
            sortable: false,
			align:"left"
        }, {
            colHeader: i18n.lms1205s05["l120s05.grid19"],//"金融商品"
            name: 'gpComAmt',
            width: 60,
            sortable: false,
			formatter : function(data) {
				if(data == null){
					return "";
				}else{
					// 加入撇節符號
					return util.addComma(data);	
				}
			},			
			align:"right"
        }, {
            colHeader: i18n.lms1205s05["l120s05.grid20"],//"曝險總額"
            name: 'gpRiskAmt',
            width: 60,
            sortable: false,
			formatter : function(data) {
				if(data == null){
					return "";
				}else{
					// 加入撇節符號
					return util.addComma(data);	
				}
			},			
			align:"right"
        }, {
            name: 'oid',
            hidden: true
        }],
        	ondblClickRow: function(rowid){
        }
    });
}

function uPanel07Gridview02(){
	   $("#Panel07Gridview02").jqGrid("setGridParam", {
		postData : {
			formAction : "queryL120s05d",
			mainId : responseJSON.mainId,
			rowNum:10
		},
		search: true
	   }).trigger("reloadGrid");
}