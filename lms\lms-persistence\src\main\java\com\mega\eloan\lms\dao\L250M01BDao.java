package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L250M01B;

/** 模擬動審額度序號資料 **/
public interface L250M01BDao extends IGenericDao<L250M01B> {

	L250M01B findByOid(String oid);

	List<L250M01B> findByMainId(String mainId);

	L250M01B findByUniqueKey(String mainId, String cntrNo);

	List<L250M01B> findByIndex01(String mainId, String cntrNo);

	List<L250M01B> findByCntrNo(String CntrNo);

	public List<L250M01B> findByReMainId(String cntrMainId);
}