/* 
 * L140S01ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.L140S01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L140S01A;

/** 個金借保人檔 **/
@Repository
public class L140S01ADaoImpl extends LMSJpaDao<L140S01A, String> implements
		L140S01ADao {

	@Override
	public L140S01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L140S01A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L140S01A> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public L140S01A findByUniqueKey(String mainId, String custId, String dupNo, String custPos) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "custPos", custPos);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L140S01A> findByCustIdDupId(String custId, String DupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", DupNo);
		List<L140S01A> list = createQuery(L140S01A.class, search)
				.getResultList();
		return list;
	}

	@Override
	public List<L140S01A> findByOids(String[] oids) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.IN, "oid", oids);
		List<L140S01A> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L140S01A> findByMainIdCustPos(String mainId, String custPos) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custPos", custPos);
		List<L140S01A> list = createQuery(L140S01A.class, search)
				.getResultList();
		return list;
	}

	@Override
	public List<L140S01A> findByMainId_excludeS(String mainId){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.NOT_EQUALS, "custPos", "S");
		List<L140S01A> list = createQuery(search).getResultList();
		return list;
	}
}