/* 
 * LMS9530FileUploadHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.handler.file;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;

import javax.annotation.Resource;

import org.apache.commons.io.IOUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.multipart.MultipartFile;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.utils.MegaFileUtils;
import com.mega.eloan.lms.model.LPDFS01A;
import com.mega.eloan.lms.rpt.service.LMS9530Service;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.handler.FileUploadHandler;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.jcs.common.PropUtil;

/**
 * <pre>
 * 舊案轉檔
 * </pre>
 * 
 * @since 2011/11/26
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/11/26,Vector,new
 *          </ul>
 */
@Scope("request")
@Controller("lms9530fileuploadhandler")
public class LMS9530FileUploadHandler extends FileUploadHandler {
	// private final String filePathRoot =
	// "C:/Users/<USER>/Desktop/Vector/ExportPDF";

	@Resource
	LMS9530Service service;

	@Override
	public IResult afterUploaded(PageParameters params) throws CapException {
		// 2013-09-09,Rex,改檔案讀取路徑為設定檔 docFile.dir 若需更改目錄位置則需新增參數
		String filePathRoot = PropUtil.getProperty("docFile.dir");
		MultipartFile uFile = params.getFile(params.getString("fieldId"));
		String oid = params.getString(EloanConstants.OID);
		FileOutputStream stream = null;
		InputStream is = null;
		try {
			LPDFS01A record = service.findModelByOid(LPDFS01A.class, oid);
			/*
			 * String fileName = "/" +
			 * Util.trim(record.getLpdfm01a().getOwnBrId()) + "-" +
			 * Util.trim(record.getRandomCode()) + "_" +
			 * Util.trim(record.getRptType()) + ".pdf";
			 * record.setRptFile(fileName); service.save(record);
			 */
			String oldPath = record.getRptFile();
			String filePath = this.getValidPath(filePathRoot + oldPath);
			record.setRptFile(oldPath.substring(0, oldPath.lastIndexOf("/"))
					+ "/" + uFile.getName());
			record.setRptName(uFile.getName());
			service.save(record);
			// 設定上傳檔案處理物件
			is = uFile.getInputStream();
			// 儲存上傳檔案
			File file = new File(filePath);
			file.deleteOnExit();
			file = new File(this.getValidPath(filePathRoot + record.getRptFile()));
			stream = new FileOutputStream(file, false);
			stream.write(IOUtils.toByteArray(is));
			stream.close();

		} catch (IOException e) {
			logger.error(e.getMessage(), e);
			throw new CapMessageException("file IO ERROR", getClass());
		} finally {
			if (is != null) {
				try {
					is.close();
				} catch (IOException e) {
					logger.debug("inputStream close Error", getClass());
				}
			}
			if (stream != null) {
				try {
					stream.close();
				} catch (IOException e) {
					logger.debug("inputStream close Error", getClass());
				}
			}
		}

		return new CapAjaxFormResult();
	}

	private String getValidPath(String dir) {
		if (MegaFileUtils.getValidPath(dir) == null) {
			throw new IllegalArgumentException("ERROR:" + dir);
		}
		return dir;
	}

	@Override
	public String getOperationName() {
		return "fileUploadOperation";
	}

}
