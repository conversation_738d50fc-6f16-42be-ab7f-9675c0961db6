var initDfd = initDfd || $.Deferred();
initDfd.done(function(json){
	var initControl_lockDoc = json['initControl_lockDoc'];

	$("tr.tr_factGrade").click(function(){
		if(initControl_lockDoc) {
			
		}else{
			$(this).find('input:radio').attr('checked', 'checked');
		}
	});
	
	$("#cmsType").change(function(k, v){
		var cmsType = $(this).val();
		if(cmsType==""){
			$("tr.cmsType_1 :input").val('');//clear 包含的 text, select
			
			$("tr.cmsType_1").show();
		}else if(cmsType=="1"){
			
			$("tr.cmsType_1").show();
		}else{			
			$("tr.cmsType_1 :input").val('');//clear 包含的 text, select	
			
			$("tr.cmsType_1").hide();	
		}
		
		if(cmsType=="6"){//無擔保品
			var _inj = {};
			//因子1, 因子2的 1分都是無擔保品
			_inj['factor1'] = '1';
			_inj['factor2'] = '1';
			$("#tabForm").injectData(_inj);
		}			
	});
	$("#cmsType").trigger('change');
});