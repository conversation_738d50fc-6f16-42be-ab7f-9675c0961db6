package com.mega.eloan.lms.fms.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.fms.pages.LMS9101V00Page;
import com.mega.eloan.lms.mfaloan.service.MisELF386Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import jxl.Workbook;
import jxl.write.Label;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;

@Service("lms9101xlsservice")
public class LMS9101XLSServiceImpl implements FileDownloadService {
	@Resource
	MisELF386Service mis386Service;
	protected static final Logger LOGGER = LoggerFactory
			.getLogger(LMS9101XLSServiceImpl.class);

	@Override
	public byte[] getContent(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// Properties
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS9101V00Page.class);
		ByteArrayOutputStream baos = null;
		List<Map<String, Object>> list = null;
		// 除了授管處及資訊處可取得全部資料，其他分行依照各分行代號碼得資料
		if (UtilConstants.BankNo.授管處.equals(user.getUnitNo())
				|| UtilConstants.BankNo.資訊處.equals(user.getUnitNo())) {
			list = mis386Service.getELF386data("%");
		} else {
			list = mis386Service.getELF386data("%" + user.getUnitNo() + "%");
		}

		try {
			baos = new ByteArrayOutputStream();
			WritableWorkbook book = Workbook.createWorkbook(baos);
			WritableSheet sheet = book.createSheet("1", 0);
			String[] title = { pop.getProperty("L910M01a.xlsitemSeq"),
					pop.getProperty("L910M01a.xlsId"),
					pop.getProperty("L910M01a.xlsname"),
					pop.getProperty("L910M01a.xlsstartime"),
					pop.getProperty("L910M01a.xlsendtime"),
					pop.getProperty("L910M01a.xlsYpay"),
					pop.getProperty("L910M01a.xlsOmoney") };
			for (int j = 0; j < title.length; j++) {
				Label labelT1 = new Label(j, 0, title[j]);
				sheet.addCell(labelT1);
			}
			for (int i = 0; i < list.size(); i++) {
				String oId = String.valueOf(i + 1);
				Label labeli = new Label(0, i + 1, oId);
				sheet.addCell(labeli);
				if (list.get(i).get("CUSTID") != null) {
					Label label = new Label(1, i + 1, list.get(i).get("CUSTID")
							.toString());
					sheet.addCell(label);
				}
				if (list.get(i).get("CNAME") != null) {
					Label labelCNAME = new Label(2, i + 1, list.get(i)
							.get("CNAME").toString());
					sheet.addCell(labelCNAME);
				}
				if (list.get(i).get("OPENDATE") != null) {
					Label labelOPENDATE = new Label(3, i + 1, list.get(i)
							.get("OPENDATE").toString());
					sheet.addCell(labelOPENDATE);
				}
				if (list.get(i).get("LOANDATE") != null) {
					Label labelLOANDATE = new Label(4, i + 1, list.get(i)
							.get("LOANDATE").toString());
					sheet.addCell(labelLOANDATE);
				}
				if (list.get(i).get("YPAY") != null) {
					Label labelYPAY = new Label(5, i + 1, list.get(i)
							.get("YPAY").toString());
					sheet.addCell(labelYPAY);
				}
				if (list.get(i).get("OMONEY") != null) {
					Label labelOMONEY = new Label(6, i + 1, list.get(i)
							.get("OMONEY").toString());
					sheet.addCell(labelOMONEY);
				}
			}
			book.write();
			book.close();
			return baos.toByteArray();
		} catch (Exception ex) {
			LOGGER.error("[getContent] Exception!!", ex);
		} finally {
			if (baos != null) {
				try {
					baos.close();
				} catch (IOException ex) {
					LOGGER.error("[getContent] Exception!!", ex);
				}
			}
		}
		return null;
	}
}
