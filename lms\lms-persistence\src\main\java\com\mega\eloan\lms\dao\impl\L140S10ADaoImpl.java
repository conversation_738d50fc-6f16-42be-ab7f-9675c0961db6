/* 
 * L140S10ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.jcs.common.Util;

import com.mega.eloan.lms.dao.L140S10ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L140S10A;

/** 其他敘作條件資訊檔 **/
@Repository
public class L140S10ADaoImpl extends LMSJpaDao<L140S10A, String>
	implements L140S10ADao {

	@Override
	public L140S10A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L140S10A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addOrderBy("seq", false);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L140S10A> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public L140S10A findByMainAndBizCat(String mainId, String bizCat) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "bizCat", bizCat);
		search.setMaxResults(Integer.MAX_VALUE);
		return findUniqueOrNone(search);
	}
	
	@Override
	public List<L140S10A> findByMainAndSequence(String mainId, int[] seqArray) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.IN, "seq", seqArray);
		search.addOrderBy("seq", false);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L140S10A> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public L140S10A findMaxSequenceByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addOrderBy("seq", true);
		return findUniqueOrNone(search);
	}

	@Override
	public L140S10A findMaxSeqNumByMainId(String mainId, String bizCat, Integer bizCatId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if(Util.isNotEmpty(bizCat)) {
			search.addSearchModeParameters(SearchMode.EQUALS, "bizCat", bizCat);
			search.addSearchModeParameters(SearchMode.EQUALS, "bizCatId", bizCatId);
		}
		search.addOrderBy("seq", true);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L140S10A> findExist(String mainId, String loanTPs, String bizCat, String bizItem) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if(Util.isNotEmpty(Util.trim(loanTPs))) {
			search.addSearchModeParameters(SearchMode.EQUALS, "loanTPs", loanTPs);
		}
		search.addSearchModeParameters(SearchMode.EQUALS, "bizCat", bizCat);
		if(Util.isNotEmpty(Util.trim(bizItem))) {
			search.addSearchModeParameters(SearchMode.EQUALS, "bizItem", bizItem);
		}
		search.addOrderBy("seq", false);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L140S10A> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L140S10A> findByLoanTPs(String mainId, String loanTPs) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "loanTPs", loanTPs);
//		search.addOrderBy("bizCat", false);
		search.addOrderBy("seq", false);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L140S10A> list = createQuery(search).getResultList();
		return list;
	}


	@Override
	public List<L140S10A> findByBizCatAndId(String mainId, String bizCat, Integer bizCatId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "bizCat", bizCat);
		search.addSearchModeParameters(SearchMode.EQUALS, "bizCatId", bizCatId);
		search.addOrderBy("seq", false);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L140S10A> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public List<L140S10A> findByMainIdAndSeqNum(String mainId, int[] seqArray) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.IN, "seq", seqArray);
		search.addOrderBy("seq", true);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L140S10A> list = createQuery(search).getResultList();
		return list;
	}
}