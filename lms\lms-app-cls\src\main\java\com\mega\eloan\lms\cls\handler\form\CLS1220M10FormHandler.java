package com.mega.eloan.lms.cls.handler.form;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.TreeMap;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.BranchTypeEnum;
import com.mega.eloan.common.enums.MEGAImageApiEnum;
import com.mega.eloan.common.enums.TypCdEnum;
import com.mega.eloan.common.enums.UnitTypeEnum;
import com.mega.eloan.common.formatter.CodeTypeFormatter;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.MEGAImageService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.service.UserInfoService.SignEnum;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.NumberService;
import com.mega.eloan.lms.base.service.ProdService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.cls.constants.ClsConstants;
import com.mega.eloan.lms.cls.pages.CLS1220M04Page;
import com.mega.eloan.lms.cls.pages.CLS1220V10Page;
import com.mega.eloan.lms.cls.panels.CLS1220S06Panel;
import com.mega.eloan.lms.cls.service.CLS1131Service;
import com.mega.eloan.lms.cls.service.CLS1220Service;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C101M01A;
import com.mega.eloan.lms.model.C101S01A;
import com.mega.eloan.lms.model.C101S01B;
import com.mega.eloan.lms.model.C101S01C;
import com.mega.eloan.lms.model.C101S01D;
import com.mega.eloan.lms.model.C101S01E;
import com.mega.eloan.lms.model.C101S01F;
import com.mega.eloan.lms.model.C101S01G;
import com.mega.eloan.lms.model.C101S01Q;
import com.mega.eloan.lms.model.C101S02C;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.C120S01B;
import com.mega.eloan.lms.model.C120S01C;
import com.mega.eloan.lms.model.C120S01D;
import com.mega.eloan.lms.model.C122M01A;
import com.mega.eloan.lms.model.C122M01C;
import com.mega.eloan.lms.model.C122M01F;
import com.mega.eloan.lms.model.C122S01C;
import com.mega.eloan.lms.model.C122S01G;
import com.mega.eloan.lms.model.C122S01H;
import com.mega.eloan.lms.model.C122S01Y;
import com.mega.eloan.lms.model.C900M01M;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapFormatException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.iisi.cap.utils.CapEntityUtil;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 線上房貸
 * </pre>
 * 
 * @since 2020/6/23
 * <AUTHOR>
 * @version <ul>
 *          <li>2020/6/23,EL08034,new
 *          </ul>
 */
@Scope("request")
@Controller("cls1220m10formhandler")
@DomainClass(C122M01A.class)
public class CLS1220M10FormHandler extends AbstractFormHandler {

	@Resource
	CLS1220Service service;
	@Resource
	UserInfoService userInfoService;
	@Resource
	BranchService branchService;
	@Resource
	TempDataService tempDataService;
	@Resource
	DocCheckService docCheckService;
	@Resource
	DocLogService docLogService;
	@Resource
	CLSService clsService;
	@Resource
	MisdbBASEService misdbBASEService;
	@Resource
	CodeTypeService codeTypeService;
	@Resource
	NumberService number;
	@Resource
	CLS1131Service cls1131Service;
	@Resource
	CLS1220Service cls1220Service;
	@Resource
	DwdbBASEService dwdbService;
	@Resource
	CodeTypeService codetypeService;
	@Resource
	MEGAImageService mEGAImageService;
	@Resource
	DocFileService docFileService;

	@Resource
	RetrialService retrialService;
	
	Properties prop_cls1220m04 = MessageBundleScriptCreator
			.getComponentResource(CLS1220M04Page.class);
	Properties prop_cls1220v10 = MessageBundleScriptCreator
			.getComponentResource(CLS1220V10Page.class);
	Properties prop_cls1220s06 = MessageBundleScriptCreator
			.getComponentResource(CLS1220S06Panel.class);

	// Properties prop_cls1220v09 =
	// MessageBundleScriptCreator.getComponentResource(CLS1220V09Page.class);
	// Properties prop_abstractEloan =
	// MessageBundleScriptCreator.getComponentResource(AbstractEloanPage.class);

	private String build_addr_2(String _AddressCity // 地址縣市
			, String _AddressTown // 地址鄉鎮區域
			, String _AddressVillage // 地址村里
			, String _AddressStreet // 地址街道
			, String _AddressSection // 地址段
			, String _AddressLane // 地址巷
			, String _AddressAlley // 地址弄
			, String _AddressNo // 地址號
			, String _AddressFloor // 地址樓
			, String _AddressLastNo // 樓之X
			, String _AddressRoom // 地址室
	) {
		String s_AddressVillage = Util.trim(_AddressVillage);
		// String s_AddressNeighborhood = Util.trim(_AddressNeighborhood);
		String s_AddressStreet = Util.trim(_AddressStreet);
		String s_AddressSection = Util.trim(_AddressSection);
		String s_AddressLane = Util.trim(_AddressLane);
		String s_AddressAlley = Util.trim(_AddressAlley);
		String s_AddressNo = Util.trim(_AddressNo);
		String s_AddressFloor = Util.trim(_AddressFloor);
		String s_AddressLastNo = Util.trim(_AddressLastNo);
		String s_AddressRoom = Util.trim(_AddressRoom);

		return s_AddressVillage
				// +
				// s_AddressNeighborhood+(Util.isEmpty(s_AddressNeighborhood)?"":(s_AddressNeighborhood.endsWith("鄰")?"":"鄰"))
				+ s_AddressStreet + s_AddressSection
				+ (Util.isEmpty(s_AddressSection) ? "" : "段") + s_AddressLane
				+ (Util.isEmpty(s_AddressLane) ? "" : "巷") + s_AddressAlley
				+ (Util.isEmpty(s_AddressAlley) ? "" : "弄") + s_AddressNo
				+ (Util.isEmpty(s_AddressNo) ? "" : "號") + s_AddressFloor
				+ (Util.isEmpty(s_AddressFloor) ? "" : "樓")
				+ (Util.isEmpty(s_AddressLastNo) ? "" : "之") + s_AddressLastNo
				+ (Util.isEmpty(s_AddressRoom) ? "" : "(") + s_AddressRoom
				+ (Util.isEmpty(s_AddressRoom) ? "" : "室)");
	}

	public String collateralAddrInfo(PageParameters params) {

		String _AddressCityId = Util.trim(params.getString("estAddressCity")); // 縣市
		Map<String, String> City = codeTypeService.findByCodeType("counties",
				LocaleContextHolder.getLocale().toString());
		String _AddressCity = Util.trim(City.get(_AddressCityId));

		String _AddressTownId = Util.trim(params.getString("estAddressArea")); // 鄉鎮區域
		Map<String, String> Town = codeTypeService.findByCodeType("counties" + _AddressCityId,
				LocaleContextHolder.getLocale().toString());
		String _AddressTown = Util.trim(Town.get(_AddressTownId));

		String _AddressStreetId = Util.trim(params
				.getString("estAddressStreet")); // 街道
		Map<String, String> Street = codeTypeService.findByCodeType("cms1010_road",
				LocaleContextHolder.getLocale().toString());
		String _AddressStreet = Util.trim(Street.get(_AddressStreetId));

		String _AddressVillage = Util.trim(params
				.getString("estAddressVillage")); // 村里
		// String _AddressNeighborhood =
		// Util.trim(ploan002_loanInfo.getCollateralAddressNeighborhood()); //鄰
		String _AddressSection = Util.trim(params
				.getString("estAddressSection")); // 段
		String _AddressLane = Util.trim(params.getString("estAddressLane")); // 巷
		String _AddressAlley = Util.trim(params.getString("estAddressAlley")); // 弄
		String _AddressNo = Util.trim(params.getString("estAddressNo")); // 號
		String _AddressFloor = Util.trim(params.getString("estAddressFloor")); // 樓
		String _AddressLastNo = Util.trim(params.getString("estAddressLastNo")); // 樓之X
		String _AddressRoom = Util.trim(params.getString("estAddressRoom")); // 室

		return Util.trim(_AddressCity) + Util.trim(_AddressTown)
				+ build_addr_2(_AddressCity // 地址縣市
						, _AddressTown // 地址鄉鎮區域
						, _AddressVillage // 地址村里
						, _AddressStreet // 地址街道
						, _AddressSection // 地址段
						, _AddressLane // 地址巷
						, _AddressAlley // 地址弄
						, _AddressNo // 地址號
						, _AddressFloor // 地址樓
						, _AddressLastNo // 樓之X
						, _AddressRoom // 地址室
				);
	}

	// 這邊開始試新的!!!!!!------------------------------------------------------------------------------------------

	/**
	 * 引進本行客戶/往來企業
	 */
	public IResult importCustomerOrCompanyInfo(PageParameters params)
			throws CapException {
		// 儲存and檢核
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set("introCustId", Util.trim(params.getString("custId")));
		result.set("introDupNo", Util.trim(params.getString("dupNo")));
		result.set("introCustName", Util.trim(params.getString("custName")));
		return result;
	}

	/**
	 * 人工進件
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify)
	public IResult makeC122M01AByManual(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		this.createC122m01a(params);
		// result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
		// prop_cls1220m04.getProperty("Message.createDataSuccess"));

		String mainId = params.getString("mainId");
		C122M01A c122m01a = service.getC122M01A_byMainId(mainId);

		// 新增一筆建檔記錄
		clsService.addC122S01F(c122m01a, c122m01a.getDocStatus(),null,null);

		result.set("oid", c122m01a.getOid());
		result.set("mainId", c122m01a.getMainId());
		result.set("docStatus", c122m01a.getDocStatus());
		result.set("applyKind", c122m01a.getApplyKind());
		result.set("incomType", c122m01a.getIncomType());

		result.set("Success", true);
		result.set("Message",
				prop_cls1220m04.getProperty("Message.createDataSuccess"));

		return result;
	}

	private void createC122m01a(PageParameters params)
			throws NumberFormatException, CapMessageException { // 人工進件時，先初始化
		C122M01A c122m01a = new C122M01A();
		String c122m01a_mainId = IDGenerator.getUUID();
		c122m01a.setMainId(c122m01a_mainId);
		params.add("mainId", c122m01a_mainId);
		CapBeanUtil.map2Bean(params, c122m01a, new String[] { "custId",
				"dupNo", "custName", "applyAmt", "maturity", "maturityM",
				"applyKind" });
		String nowExtend = Util.trim(params.getString("nowExtend"));
		if (Util.equals(nowExtend, "Y")) {
			c122m01a.setNowExtend("Y");
			if(Util.isNotEmpty(Util.trim(params.getString("extYear")))){
				c122m01a.setExtYear(new BigDecimal(Util.trim(params.getString("extYear"))));
			}else{
				c122m01a.setExtYear(BigDecimal.ZERO);
			}
		} else {
			c122m01a.setNowExtend("N");
			c122m01a.setExtYear(BigDecimal.ZERO);
		}
		// 房貸案件，增貸項目要填
		String applyKind = Util.trim(params.getString("applyKind"));
		if (Util.equals(applyKind, "E")) { // 房貸案才要填
			c122m01a.setPloan002_purposeType(Util.trim(params
					.getString("purposeType")));
		}
		// 其他案件，增貸項目固定填入[3-其他]
		if (Util.equals(applyKind, "O")) {
			c122m01a.setPloan002_purposeType("3");
		}
		
		//房貸案件，是否為購屋
		String purposeType = Util.trim(params.getString("purposeType"));
		String purchaseHouse = "N";
		if(Util.equals(applyKind, "E") && Util.equals(purposeType, "1")){
			purchaseHouse = Util.trim(params.getString("purchaseHouse"));
		}
		if(Util.equals(applyKind, "O")){// O-其他案件，是否為購屋根據使用者所填
			purchaseHouse = Util.trim(params.getString("purchaseHouse"));
		}
		c122m01a.setPurchaseHouse(purchaseHouse);
		c122m01a.setCreator(params.getString("_userId"));
		c122m01a.setCreateTime(CapDate.getCurrentTimestamp());
		c122m01a.setOwnBrId(MegaSSOSecurityContext.getUnitNo());
		c122m01a.setUpdater(params.getString("_userId"));
		c122m01a.setUpdateTime(CapDate.getCurrentTimestamp());
		c122m01a.setApplyTS(CapDate.getCurrentTimestamp());
		c122m01a.setDocStatus(UtilConstants.C122_DocStatus.待派案); // 審核中(編制)
		c122m01a.setApplyStatus(UtilConstants.C122_ApplyStatus.受理中);
		c122m01a.setStatFlag("0"); // 這是甚麼我還有點不清楚
		c122m01a.setApplyCurr("TWD");
		c122m01a.setIncomType(UtilConstants.C122_IncomType.線下);
		// 產生案件編號 ----- 固定XX開頭+日期+流水號
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String Date = CapDate.getDateTimeFormat(CapDate.getCurrentTimestamp());
//		int SeqNo = Integer
//				.parseInt(number.getNumberWithMax(C122M01A.class, user
//						.getUnitNo(), CapDate.getDateTimeFormat(CapDate
//						.getCurrentTimestamp()), 99999));
		//因案件編號內容無分行資訊，編碼修改為不區分分行，分行參數帶空值""
		int SeqNo = Integer
		.parseInt(number.getNumberWithMax(C122M01A.class, "", CapDate.getDateTimeFormat(CapDate
				.getCurrentTimestamp()), 99999));
		StringBuffer SeqNoBuf = new StringBuffer();
		SeqNoBuf.append("OF");
		SeqNoBuf.append(CapDate.formatDateFormatToyyyyMMdd(Date, "yyyy-MM-dd"));
		SeqNoBuf.append(String.format("%06d", SeqNo));
		c122m01a.setPloanCaseNo(SeqNoBuf.toString());
		c122m01a.setPloanCaseId(SeqNoBuf.toString());
		// ~~~~~
		C120M01A c120m01a = new C120M01A();
		c120m01a.setMainId(c122m01a_mainId);
		c120m01a.setOwnBrId(c122m01a.getOwnBrId());
		c120m01a.setCustId(c122m01a.getCustId());
		c120m01a.setDupNo(c122m01a.getDupNo());
		// c120m01a.setStaffNo(Util.trim(ploan001.getBasicInfo().getEmpNo()));

		// ~~~~
		C122M01F c122m01f = new C122M01F();
		c122m01f.setMainId(c122m01a_mainId);
		c122m01f.setOwnBrId(c122m01a.getOwnBrId());
		c122m01f.setCustId(c122m01a.getCustId());
		c122m01f.setDupNo(c122m01a.getDupNo());
		c122m01f.setCustName(c122m01a.getCustName());
		//J-112-0006 起案選項地政士引介勾選為[是]，引介來源要寫入4：地政士引介
		if(Util.equals(Util.trim(params.getString("laaFlag")), "Y")){
			c122m01f.setIntroduceSrc("4");//地政士引介
		} else {
			c122m01f.setIntroduceSrc("N");
		}
		if (Util.equals(applyKind, "E") || Util.equals(applyKind, "O")) { // 房貸案才要填，新增[O:其他案件]也要填
			c122m01f.setEstFlag(Util.trim(params.getString("estFlag")));
		} else {
			c122m01f.setEstFlag("N");
		}

		// ~~~~~
		C120S01A c120s01a = new C120S01A();
		c120s01a.setMainId(c120m01a.getMainId());
		c120s01a.setCustId(c120m01a.getCustId());
		c120s01a.setDupNo(c120m01a.getDupNo());
		// ~~~~~
		C120S01B c120s01b = new C120S01B();
		c120s01b.setMainId(c120m01a.getMainId());
		c120s01b.setCustId(c120m01a.getCustId());
		c120s01b.setDupNo(c120m01a.getDupNo());
		// ~~~~~
		C120S01C c120s01c = new C120S01C();
		c120s01c.setMainId(c120m01a.getMainId());
		c120s01c.setCustId(c120m01a.getCustId());
		c120s01c.setDupNo(c120m01a.getDupNo());
		// ~~~~~
		C120S01D c120s01d = new C120S01D();
		c120s01d.setMainId(c120m01a.getMainId());
		c120s01d.setCustId(c120m01a.getCustId());
		c120s01d.setDupNo(c120m01a.getDupNo());

		if (true) {
			service.save(c122m01a);
			service.save(c122m01f);
			service.save(c120m01a);
			service.save(c120s01a);
			service.save(c120s01b);
			service.save(c120s01c);
			service.save(c120s01d);
		}

	}
	
	/**
	 * 線下進件從債務人新增
	 * @param params
	 * @throws NumberFormatException
	 * @throws CapMessageException
	 */
	private C122M01A createRelC122m01a(PageParameters params)
			throws NumberFormatException, CapMessageException { // 人工進件時，先初始化
		C122M01A c122m01a = new C122M01A();
		String c122m01a_mainId = IDGenerator.getUUID();
		c122m01a.setMainId(c122m01a_mainId);
		CapBeanUtil.map2Bean(params, c122m01a, new String[] { "custId",
				"dupNo", "custName", "applyAmt", "maturity", "maturityM",
				"applyKind", "ploanCasePos", "ploanCaseId" });
		String nowExtend = Util.trim(params.getString("nowExtend"));
		if (Util.equals(nowExtend, "Y")) {
			c122m01a.setNowExtend("Y");
			if(Util.isNotEmpty(Util.trim(params.getString("extYear")))){
				c122m01a.setExtYear(new BigDecimal(Util.trim(params.getString("extYear"))));
			}else{
				c122m01a.setExtYear(BigDecimal.ZERO);
			}
		} else {
			c122m01a.setNowExtend("N");
			c122m01a.setExtYear(BigDecimal.ZERO);
		}
		// 房貸案件，增貸項目要填
		String applyKind = Util.trim(params.getString("applyKind"));
		if (Util.equals(applyKind, UtilConstants.C122_ApplyKind.F)	// 房貸案才要填
				|| Util.equals(applyKind, UtilConstants.C122_ApplyKind.R)) { //新增[R:其他案件]也要填
			c122m01a.setPloan002_purposeType(Util.trim(params
					.getString("purposeType")));
		}
		//房貸案件，是否為購屋
		String purposeType = Util.trim(params.getString("purposeType"));
		String purchaseHouse = "N";
		if(Util.equals(applyKind, UtilConstants.C122_ApplyKind.F) && Util.equals(purposeType, "1")){
			purchaseHouse = Util.trim(params.getString("purchaseHouse"));
		}
		if(Util.equals(applyKind, UtilConstants.C122_ApplyKind.R)){// R:其他案件，是否為購屋根據使用者所填
			purchaseHouse = Util.trim(params.getString("purchaseHouse"));
		}
		c122m01a.setPurchaseHouse(purchaseHouse);
		c122m01a.setCreator(params.getString("_userId"));
		c122m01a.setCreateTime(CapDate.getCurrentTimestamp());
		c122m01a.setOwnBrId(MegaSSOSecurityContext.getUnitNo());
		c122m01a.setUpdater(params.getString("_userId"));
		c122m01a.setUpdateTime(CapDate.getCurrentTimestamp());
		c122m01a.setApplyTS(CapDate.getCurrentTimestamp());
		c122m01a.setDocStatus(UtilConstants.C122_DocStatus.待派案); // 審核中(編制)
		c122m01a.setApplyStatus(UtilConstants.C122_ApplyStatus.受理中);
		c122m01a.setStatFlag("0"); // 這是甚麼我還有點不清楚
		c122m01a.setApplyCurr("TWD");
		c122m01a.setIncomType(UtilConstants.C122_IncomType.線下);
		// 產生案件編號 ----- 固定XX開頭+日期+流水號
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String Date = CapDate.getDateTimeFormat(CapDate.getCurrentTimestamp());
		//int SeqNo = Integer
		//		.parseInt(number.getNumberWithMax(C122M01A.class, user
		//				.getUnitNo(), CapDate.getDateTimeFormat(CapDate
		//				.getCurrentTimestamp()), 99999));
		//因案件編號內容無分行資訊，編碼修改為不區分分行，分行參數帶空值""
		int SeqNo = Integer
		.parseInt(number.getNumberWithMax(C122M01A.class, "", CapDate.getDateTimeFormat(CapDate
				.getCurrentTimestamp()), 99999));
		StringBuffer SeqNoBuf = new StringBuffer();
		SeqNoBuf.append("OF");
		SeqNoBuf.append(CapDate.formatDateFormatToyyyyMMdd(Date, "yyyy-MM-dd"));
		SeqNoBuf.append(String.format("%06d", SeqNo));
		c122m01a.setPloanCaseNo(SeqNoBuf.toString());
		// ~~~~~
		C120M01A c120m01a = new C120M01A();
		c120m01a.setMainId(c122m01a_mainId);
		c120m01a.setOwnBrId(c122m01a.getOwnBrId());
		c120m01a.setCustId(c122m01a.getCustId());
		c120m01a.setDupNo(c122m01a.getDupNo());
		// c120m01a.setStaffNo(Util.trim(ploan001.getBasicInfo().getEmpNo()));
		
		// ~~~~
		C122M01F c122m01f = new C122M01F();
		c122m01f.setMainId(c122m01a_mainId);
		c122m01f.setOwnBrId(c122m01a.getOwnBrId());
		c122m01f.setCustId(c122m01a.getCustId());
		c122m01f.setDupNo(c122m01a.getDupNo());
		c122m01f.setCustName(c122m01a.getCustName());
		c122m01f.setIntroduceSrc("N");
		if (Util.equals(applyKind, UtilConstants.C122_ApplyKind.F)
				|| Util.equals(applyKind, UtilConstants.C122_ApplyKind.R)) { // 房貸案才要填，新增[R:其他案件]也要填
			c122m01f.setEstFlag(Util.trim(params.getString("estFlag")));
		} else {
			c122m01f.setEstFlag("N");
		}
		
		// ~~~~~
		C120S01A c120s01a = new C120S01A();
		c120s01a.setMainId(c120m01a.getMainId());
		c120s01a.setCustId(c120m01a.getCustId());
		c120s01a.setDupNo(c120m01a.getDupNo());
		// ~~~~~
		C120S01B c120s01b = new C120S01B();
		c120s01b.setMainId(c120m01a.getMainId());
		c120s01b.setCustId(c120m01a.getCustId());
		c120s01b.setDupNo(c120m01a.getDupNo());
		// ~~~~~
		C120S01C c120s01c = new C120S01C();
		c120s01c.setMainId(c120m01a.getMainId());
		c120s01c.setCustId(c120m01a.getCustId());
		c120s01c.setDupNo(c120m01a.getDupNo());
		// ~~~~~
		C120S01D c120s01d = new C120S01D();
		c120s01d.setMainId(c120m01a.getMainId());
		c120s01d.setCustId(c120m01a.getCustId());
		c120s01d.setDupNo(c120m01a.getDupNo());
		
		if (true) {
			service.save(c122m01a);
			service.save(c122m01f);
			service.save(c120m01a);
			service.save(c120s01a);
			service.save(c120s01b);
			service.save(c120s01c);
			service.save(c120s01d);
		}
		return c122m01a;
	}
	
	/**
	 * <pre>
	 * 線下進件從債務人維護
	 * </pre>
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult ploanRelateCaseManage(PageParameters params) throws CapException {
		CapAjaxFormResult r = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		int mType = params.getInt("mType");
		// 從債務人C122M01A.mainId
		String relMainId = params.getString("rel_mainId");
		String relCustId = params.getString("rel_custId");
		String relDupNo = params.getString("rel_dupNo");
		if (CapString.isEmpty(relDupNo)) {
			relDupNo = " ";
		}
		String relCustName = params.getString("rel_custName");
		String relPloanCasePos = params.getString("rel_ploanCasePosCode");
		C122M01A meta = service.getC122M01A_byMainId(mainId);
		C122M01A relC122m01a = service.getC122M01A_byMainId(relMainId);
		switch (mType) {
		// 新增
		case 1:
			// 查詢ID是否有重複建檔
			// 取得線上進件的連保人保證人擔保品提供人
			List<C122M01A> relateCaseC122M01As = service
					.findMetaApplyKind_relateCase(meta.getApplyKind(), meta.getPloanCaseId());
			for (C122M01A c122m01a : relateCaseC122M01As) {
				if (relCustId.equals(CapString.trimNull(c122m01a.getCustId()))) {
					// Message.sendCheck.14=借保人資料已存在
					throw new CapMessageException(prop_cls1220m04.getProperty("Message.sendCheck.14"), getClass());
				}
			}
			// P:線上信貸_PLOAN系統_主借人
			// Q:線上信貸_PLOAN系統_從債務人
			// E:線上房貸_PLOAN系統_主借人
			// F:線上房貸_PLOAN系統_從債務人
			// O:其他_主借人
			// R:其他_從債務人
			String relApplyKiind = "";
			if (UtilConstants.C122_ApplyKind.P.equals(meta.getApplyKind())) {
				relApplyKiind = UtilConstants.C122_ApplyKind.Q;
			} else if (UtilConstants.C122_ApplyKind.E.equals(meta.getApplyKind())) {
				relApplyKiind = UtilConstants.C122_ApplyKind.F;
			} else if (UtilConstants.C122_ApplyKind.O.equals(meta.getApplyKind())) {
				relApplyKiind = UtilConstants.C122_ApplyKind.R;
			}
			params.put("custId", relCustId);
			params.put("dupNo", relDupNo);
			params.put("custName", relCustName);
			params.put("ploanCasePos", relPloanCasePos);
			params.put("applyAmt", meta.getApplyAmt());
			params.put("maturity", meta.getMaturity());
			params.put("applyKind", relApplyKiind);
			params.put("nowExtend", meta.getNowExtend());
			params.put("extYear", meta.getExtYear());
			params.put("purposeType", meta.getPurpose());
			params.put("purchaseHouse", meta.getPurchaseHouse());
			params.put("_userId", user.getUserId());
			params.put("estFlag", meta.getC122m01f().getEstFlag());
			params.put("ploanCaseId", meta.getPloanCaseId());
			relC122m01a = createRelC122m01a(params);
			// 新增時同步新增進件狀態明細檔C122S01H
			String remainId = params.getString("rel_remainId");
			String flowId = params.getString("rel_flowId");
			params.put("remainId", remainId);
			params.put("flowId", flowId);
			// MAIDID改為新產製的C122M01A.MAINID
			params.put("mainId", relC122m01a.getMainId());
			newC122S01H_flowId01(params);
			break;
		// 修改
		case 2:
			relC122m01a.setPloanCasePos(relPloanCasePos);
			relC122m01a.setUpdater(user.getUserId());
			relC122m01a.setUpdateTime(CapDate.getCurrentTimestamp());
			clsService.daoSave(relC122m01a);
			break;
		// 刪除
		case 3:
			// 【eLoan連動刪除影像】API軟刪除該ID此案所有關係人文件，並且Eloan從債務人資料也刪除(軟刪除)
			String respString = "";
			List<Map<String, Object>> megaImageList = mEGAImageService.getMEGAImageListByStakeholderID(
					MEGAImageApiEnum.取得影像清單, mainId, meta.getPloanCaseId(), relC122m01a.getCustId());
			// 軟刪除文件數位化資料
			for (Map<String, Object> map : megaImageList) {
				String docId = CapString.trimNull(map.get("DocId"));
				JSONObject resp = mEGAImageService.eloanCloseImage(docId);
				if (MEGAImageService.連動刪除影像_code.處理失敗.equals(resp.optString("code"))) {
					respString += "docId:" + docId + resp.optString("message") + "<br/>";
				}
			}
			List<GenericBean> updateList = new ArrayList<GenericBean>();
			List<C900M01M> c900m01ms = clsService.findC900M01MByCaseNoId(meta.getPloanCaseId(), relC122m01a.getCustId());
			for (C900M01M c900m01m : c900m01ms) {
				String batId = c900m01m.getBatId();
				String caseNo = c900m01m.getCaseNo();
				String docFileOid = c900m01m.getDocFileOid();
				// 刪除本地資料
				docFileService.delete(docFileOid);
				// 刪除FTP文件數位化資料
				mEGAImageService.deleteEloanUploadImageDir(caseNo, batId, mainId);
				// 更新紀錄檔
				c900m01m.setDeletedTime(CapDate.getCurrentTimestamp());
				updateList.add(c900m01m);
			}
			clsService.save(updateList);
			if (!CapString.isEmpty(respString)) {
				throw new CapMessageException(respString, getClass());
			}
			relC122m01a.setUpdater(user.getUserId());
			relC122m01a.setDeletedTime(CapDate.getCurrentTimestamp());
			clsService.daoSave(relC122m01a);
			break;
		}
		return r;
	}

	/**
	 * 行員查詢-只查該分行經辦
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unused")
	@DomainAuth(value = AuthType.Modify + AuthType.Query)
	public IResult getMegaEmpInfo(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		Map<String, String> bossListAO = new HashMap<String, String>();
//		bossListAO = userInfoService.getBRUserName(MegaSSOSecurityContext
//				.getUnitNo());
		SignEnum[] signs = { SignEnum.首長, SignEnum.單位主管,SignEnum.甲級主管, SignEnum.乙級主管, SignEnum.經辦人員 };
		bossListAO = userInfoService.findByBrnoAndSignId(
				MegaSSOSecurityContext.getUnitNo(), signs);
		
		Map<String, String> M943KycUser_Map = codeTypeService.findByCodeType("c122m01a_943KycUser",
				LocaleContextHolder.getLocale().toString());
	
		// 2023.02.24  消金處表示先顯示消金KYC人員，再顯示分行人員
		Map<String, String> finalUserMap = new LinkedHashMap<String, String>();
		String need943flag = Util.trim(params.getString("need943flag"));//查詢引介人員時不需要943KYC user
		if( Util.isEmpty(need943flag)){//沒值: 需要,  有值: 不需要
			if(M943KycUser_Map != null){
				finalUserMap.putAll(M943KycUser_Map);
			}
		}
		if(bossListAO != null){
			finalUserMap.putAll(bossListAO);
		}
		//J-112-0006 於案件內撈[指派人員]，需判斷引介來源，若為房貸+地政士引介案件需取得前後案相同地政士之負責經辦後排除
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String isEvaMegaEmp = Util.trim(params.getString("isEvaMegaEmp"));
		List<String> removeList = null;
		if(Util.isNotEmpty(mainId) && Util.notEquals(isEvaMegaEmp, "Y")){
			removeList = cls1220Service.getSameMegaEmpForLaaCase(
						MegaSSOSecurityContext.getUnitNo(), mainId);
			if(removeList!=null && removeList.size()>0){
				for(String removeId : removeList){
					finalUserMap.remove(removeId);
				}
			}
		}
		
		result.set("bossListAO", new CapAjaxFormResult(finalUserMap));
		result.set("Success", true);

		return result;
	}

	/**
	 * 行員查詢
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unused")
	@DomainAuth(value = AuthType.Modify)
	public IResult importMegaEmpInfo(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString("mainOid"));
		String mainId = Util.trim(params.getString("mainId"));
		String MegaEmpId = Util.trim(params.getString("MegaEmpId"));

		String MegaEmpName = Util.trim(userInfoService.getUserName(MegaEmpId)); // 查不到會回傳員編
		if (Util.equals(MegaEmpName, MegaEmpId)) { // 找不到
			result.set("Success", "N");
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
					prop_cls1220m04.getProperty("Message.noData"));
		} else {
			result.set("Success", "Y");
			result.set("MegaEmpId", MegaEmpId);
			result.set("MegaEmpName", MegaEmpName);
			// result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
			// prop_cls1220m04.getProperty("Message.getDataSuccess"));
		}
		return result;
	}
	
	/**
	 * 查詢所有分行
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unused")
	@DomainAuth(value = AuthType.Modify + AuthType.Query)
	public IResult getAllOrgBrId(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		Map<String, String> childMap = new HashMap<String, String>();
		// 取得所有分行
		List<IBranch> allBranch = branchService.getAllBranch();
		if (!allBranch.isEmpty()) {
			for (IBranch childBran : allBranch) {
				childMap.put(Util.trim(childBran.getBrNo()),
						Util.trim(childBran.getBrName()));
			}
		}
		result.set("childMap", new CapAjaxFormResult(childMap));
		result.set("Success", true);

		return result;
	}

	/**
	 * 取得國內分行
	 * 
	 * @param params
	 * @return IResult
	 * @throws CapException
	 */
	@SuppressWarnings("unused")
	@DomainAuth(value = AuthType.Modify + AuthType.Query)
	public IResult getDomesticBrches(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		TreeMap<String, String> childMap = new TreeMap<String, String>();
		List<IBranch> allBranch = branchService.getBranchByCountryType("TW");// 取得國內單位(含總處)
		if (!allBranch.isEmpty()) {
			for (IBranch childBran : allBranch) {
				// unitType=B、J之單位仍包含總處，需以BrNoFlag過濾
				if ("1".equals(childBran.getBrNoFlag()) && ("B".equals(childBran.getUnitType()) || "J".equals(childBran.getUnitType()))) {
					childMap.put(Util.trim(childBran.getBrNo()), Util.trim(childBran.getBrName()));
				}
			}
		}
		result.set("br_addSpace", childMap.size() > 1 ? "Y" : "N");
		result.set("br_itemOrder", new ArrayList<String>(childMap.keySet()));
		result.set("br_item", new CapAjaxFormResult(childMap));
		result.set("Success", true);

		return result;
	}
	
	/**
	 * 檢查是否有0024建檔
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unused")
	@DomainAuth(value = AuthType.Modify)
	public IResult check0024Data(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString("mainOid"));
		String mainId = Util.trim(params.getString("mainId"));
		boolean success = true;
		C122M01A c122m01a = service.getC122M01A_byMainId(mainId);

		String dupNo = Util.trim(c122m01a.getDupNo());
		if (Util.isEmpty(dupNo)) {
			dupNo = "0";
		}
		Map<String, Object> m_busCd_ecoNm = misdbBASEService
				.findCustBussDataByIdAndDup(c122m01a.getCustId(), dupNo);
		String busCode = Util.trim(MapUtils.getString(m_busCd_ecoNm, "BUSCD"));
		// String ecoNm = Util.trim(MapUtils.getString(m_busCd_ecoNm, "ECONM"));
		if (Util.isEmpty(busCode)) {// 客戶資訊不存在，請先至0024系統建置
			String message = prop_cls1220m04
					.getProperty("label.applyUserContent")
					+ c122m01a.getCustId()
					+ " "
					+ c122m01a.getDupNo()
					+ " "
					+ prop_cls1220m04.getProperty("Message.sendCheck.08");
			result.set("Message", message);
			success = false;
		}
		result.set("Success", success);
		return result;
	}

	/**
	 * 派案
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unused")
	@DomainAuth(value = AuthType.Modify)
	public IResult sendToMegaEmp(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString("mainOid"));
		String mainId = Util.trim(params.getString("mainId"));
		String signMegaEmpNo = Util.trim(params.getString("SignMegaEmp"));
		String evaMegaEmpNo = Util.equals(Util.trim(params.getString("evaMegaEmpNo")),"null") ? "" : Util.trim(params.getString("evaMegaEmpNo"));
		String estUnitName = Util.trim(params.getString("estUnitName"));

		C122M01F c122m01f = service.findC122M01F(mainId);
		// String evaMegaEmpNo = c122m01f.getEvaMegaEmpNo();
		String estFlag = c122m01f.getEstFlag();

		String errorMessage = "";
		// 指定收件行行員 >> 確認簽案人及擔保品經辦不為同一人
		if (Util.equals(estFlag, "1")
				&& Util.equals(signMegaEmpNo, evaMegaEmpNo)) {
			errorMessage = prop_cls1220m04.getProperty("Message.error.01");
		}
		
		// 派案失敗
		if (Util.equals(errorMessage, "")) {
			// 儲存派案資料
			C122M01A c122m01a = service.getC122M01A_byMainId(mainId);
			String docStatus_now = Util.trim(c122m01a.getDocStatus());
			if(Util.equals("E02", docStatus_now) || docStatus_now.startsWith("I") ||
					docStatus_now.startsWith("G") || docStatus_now.startsWith("F") ||
					docStatus_now.startsWith("Z")){
				// J-113-0351 如果已結案就不更新DocStatus
			}else{
				c122m01a.setDocStatus(UtilConstants.C122_DocStatus.已派案);
			}
			c122m01a.setApprover(params.getString("_userId"));
			c122m01a.setApproveTime(CapDate.getCurrentTimestamp());

			c122m01f.setSignMegaEmpNo(signMegaEmpNo);
			c122m01f.setSignMegaEmpName(Util.trim(userInfoService
					.getUserName(signMegaEmpNo)));
			c122m01f.setEvaMegaEmpNo(evaMegaEmpNo);
			c122m01f.setEvaMegaEmpName(Util.trim(userInfoService
					.getUserName(evaMegaEmpNo)));
			c122m01f.setEstUnitName(estUnitName);

			// 第一次案件執行派案的時間，撈報表用
			if(Util.isEmpty(c122m01f.getFirstAssignEmpTime())){
				c122m01f.setFirstAssignEmpTime(CapDate.getCurrentTimestamp());
			}
			
			if (true) {
				service.save(c122m01a);
				service.save(c122m01f);
			}
			clsService.addC122S01F(c122m01a,c122m01a.getDocStatus(),null,null);
			// result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
			// prop_cls1220m04.getProperty("Message.sendSuccess"));
			
			// J-112-0006 房貸+地政士引介案件會於撈取派案人員時排除前後房貸案相同地政士之負責經辦，在派案時再檢查一次後更新FLAG(只針對房貸案)
			cls1220Service.updateIsSameMegaEmpLaaCaseFlag(MegaSSOSecurityContext.getUnitNo(), mainId);
			
			result.set("Message",
					prop_cls1220m04.getProperty("Message.sendSuccess"));
			result.set("Success", true);
		} else {
			// result.set(CapConstants.AJAX_NOTIFY_MESSAGE, errorMessage);
			result.set("Message", errorMessage);
			result.set("Success", false);
		}
		return result;
	}

	/**
	 * 派案資料確認
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify)
	public IResult checkSendData(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String errorMessage = "";
		String mainId = Util.trim(params.getString("mainId"));
		C122M01A c122m01a = service.getC122M01A_byMainId(mainId);
		C122M01F c122m01f = service.findC122M01F(mainId);
		// 案件為房貸才要檢查
		String applyKind = Util.trim(c122m01a.getApplyKind());
		if (Util.equals(applyKind, "E") || Util.equals(applyKind, "F")
				|| Util.equals(applyKind, "O")) {//新增O:其他案
			//C122M01F c122m01f = service.findC122M01F(mainId);
			if (c122m01f == null) { // 進件資料未建立
				// 房貸案件請於派案前填妥[進件資料-有無擔保品]
				errorMessage = prop_cls1220m04
						.getProperty("Message.sendCheck.02");
			} else { // 有進件資料，要確認有沒有填寫
				String estFlag = Util.trim(c122m01f.getEstFlag());
				if (Util.equals(estFlag, "")) {
					// 房貸案件請於派案前填妥[進件資料-有無擔保品]
						errorMessage = prop_cls1220m04
							.getProperty("Message.sendCheck.02");
				} else if (Util.equals(estFlag, "N")) {
					// 房貸案件[進件資料-有無擔保品]不得選[無]
					if(Util.notEquals(applyKind, "O")){//排除其他案，其他案可以選[無]
						errorMessage = prop_cls1220m04
							.getProperty("Message.sendCheck.03");
					}
				}
				// J-112-0006 配合新增地政士等相關修改項目，派案前新增檢核引介來源
				if(Util.isEmpty(c122m01f.getIntroduceSrc())){
					if(Util.isNotEmpty(errorMessage)){
						//[進件資料]-[引介來源]尚未填寫
						errorMessage = errorMessage + "<br/>"
							+ prop_cls1220m04.getProperty("Message.dataCheck.12");
					}
				}else{
					// 若為[地政士引介]要檢查是否有輸入地政士
					String checkLaaMsg = service.checkIntroduceSrcLaa(c122m01a.getMainId());
					if(Util.isNotEmpty(checkLaaMsg)){
						if(Util.isNotEmpty(errorMessage)){
							errorMessage = errorMessage + "<br/>";
						}
						errorMessage = errorMessage + checkLaaMsg;
					}
				}
			}
		}
		// 青創線上進件 無相關進件資訊，直接幫忙補一筆
		if(c122m01f == null && Util.equals(c122m01a.getIncomType(), "2") && 
				(Util.equals(applyKind, UtilConstants.C122_ApplyKind.I)
						|| Util.equals(applyKind, UtilConstants.C122_ApplyKind.J))){
			c122m01f = new C122M01F();
			c122m01f.setMainId(c122m01a.getMainId());
			c122m01f.setCustId(c122m01a.getCustId());
			c122m01f.setDupNo(c122m01a.getDupNo());
			c122m01f.setCustName(c122m01a.getCustName());
			c122m01f.setOwnBrId(c122m01a.getOwnBrId());
			c122m01f.setIntroduceSrc("N");//引介來源為[N:自來件]
			c122m01f.setEstFlag("N");//有無擔保品為[N:無]
			service.save(c122m01f);
		}
	
		// 兩個FLAG要檢查
		if (Util.equals(errorMessage, "")) {
			result.set("Success", true);
		} else {
			result.set("Success", false);
			result.set("Message", errorMessage);
		}
		return result;
	}

	/**
	 * 派案資料確認
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult getEstFlag(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String errorMessage = "";
		String mainId = Util.trim(params.getString("mainId"));
		C122M01A c122m01a = service.getC122M01A_byMainId(mainId);
		C122M01F c122m01f = service.findC122M01F(mainId);
		String checkAccess = "";
		
		// J-113-0101 針對房貸案件增加派案(改派)權限為[甲級]以上
		if(!service.checkAssignCaseAccessForHouseLoan(mainId)){
			checkAccess = prop_cls1220v10.getProperty("Message.ChangeHouseLoanSignEmp");
		}
		result.set("checkAccess", checkAccess);
		
		if (c122m01f != null) {
			result.set("estFlag", c122m01f.getEstFlag());
			result.set("chgSignMegaEmp", c122m01f.getSignMegaEmpNo());
			result.set("chgEvaMegaEmp", c122m01f.getEvaMegaEmpNo());
			result.set("chgEstUnitName", c122m01f.getEstUnitName());
		}
		return result;
	}

	/**
	 * 產生個金徵信資料前之資料檢查
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify)
	public IResult checkDataToMakeCust(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		List<String> errorMessageList = new ArrayList<String>();
		String mainId = Util.trim(params.getString("mainId"));
		C122M01A c122m01a = service.getC122M01A_byMainId(mainId);
		C122M01F c122m01f = service.findC122M01F(mainId);

		if (c122m01f == null) { // 進件資料未建立
			// 請填妥[進件資料]
			errorMessageList.add(prop_cls1220m04
					.getProperty("Message.sendCheck.01"));
		} else { // 有進件資料，要確認有沒有填寫
					// 房貸案件，若擔保品為[擔保品座落位置]，要填地址
			String applyKind = Util.trim(c122m01a.getApplyKind());
			if (Util.equals(applyKind, "E") || Util.equals(applyKind, "F")
					 || Util.equals(applyKind, "O") || Util.equals(applyKind, "R") ) {// 增加[其他案件](O、R)
				String estFlag = Util.trim(c122m01f.getEstFlag());
				if (Util.equals(estFlag, "4")) {
					// 檢查地址有沒有填，已縣市鄉鎮為標的
					String estAddressCity = Util.trim(c122m01f
							.getEstAddressCity());
					String estAddressArea = Util.trim(c122m01f
							.getEstAddressArea());
					if (Util.equals(estAddressCity, "")
							|| Util.equals(estAddressArea, "")) {
						// 請填妥擔保品座落地址
						errorMessageList.add(prop_cls1220m04
								.getProperty("Message.sendCheck.04"));
					}
				}
			}
			
			// 檢查[引介來源]
			String introduceSrc = Util.trim(c122m01f.getIntroduceSrc());
			if (Util.equals(introduceSrc, "")) {
				// 請填妥[引介來源]
				errorMessageList.add(prop_cls1220m04
						.getProperty("Message.sendCheck.05"));
			} else {
				//J-112-0006 房貸、其他案 檢查引介來源若為[地政士引介]需填寫至少一筆地政士
				//但(房貸案/其他案 點選[徵信]的時候就會檢查了，理論上不會進下面這段；信貸案在[徵信]及下面這段都不檢核)
				if(Util.equals(applyKind, "E") || Util.equals(applyKind, "F")
						|| Util.equals(applyKind, "O") || Util.equals(applyKind, "R")){
					String checkLaaMsg = service.checkIntroduceSrcLaa(c122m01a.getMainId());
					if(Util.isNotEmpty(checkLaaMsg)){
						errorMessageList.add(checkLaaMsg);
					}
				}
			}
			
		}

		/*J-112-0006 線下案件取消顯示[基本資料]及[服務單位] 取消相關檢核
		// 人工進件案件，確認[基本資料]、[服務單位]資料填寫完整
		if (Util.equals(Util.trim(c122m01a.getIncomType()), "1")) { // 線下才要檢查
			// 基本資料
			C120S01A c120s01a = service.findC120S01A(c122m01a.getMainId(),
					c122m01a.getCustId(), c122m01a.getDupNo());
			Map<String, Object> s01aMap = CapBeanUtil.bean2Map(c120s01a,
					new String[] { "birthday", "edu", "fCity", "fZip", "fAddr",
							"fTarget", "coCity", "coZip", "coAddr", "coTarget",
							"houseStatus", "mTel"});

			for (Map.Entry<String, Object> entry : s01aMap.entrySet()) {
				if (Util.equals(Util.trim(entry.getValue()), "")) {
					errorMessageList.add(prop_cls1220m04
							.getProperty("Message.dataCheck.01"));
					break;
				}
				// System.out.println(entry.getKey() + ": " + entry.getValue());
			}

			// 服務單位
			C120S01B c120s01b = service.findC120S01B(c122m01a.getMainId(),
					c122m01a.getCustId(), c122m01a.getDupNo());
			Map<String, Object> s01bMap = CapBeanUtil.bean2Map(c120s01b,
					new String[] { "jobType1", "jobType2", "comName",
							"seniority", "comTel", "payAmt" });
			for (Map.Entry<String, Object> entry : s01bMap.entrySet()) {
				if (Util.equals(Util.trim(entry.getValue()), "")) {
					errorMessageList.add(prop_cls1220m04
							.getProperty("Message.dataCheck.02"));
					break;
				}
				// System.out.println(entry.getKey() + ": " + entry.getValue());
			}
		}*/

		// 兩個FLAG要檢查
		if (errorMessageList.size() > 0) {
			String errorMessage = "";
			for (int i = 0; i < errorMessageList.size(); i++) {
				errorMessage = errorMessage + errorMessageList.get(i) + "<br>";
			}
			result.set("Success", false);
			result.set("Message", errorMessage);

		} else {
			result.set("Success", true);
		}
		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult changeOwnBrId_then_mail(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String newBrNo = Util.trim(params.getString("newBrNo"));
		String memo = Util.trim(params.getString("memo"));
		C122M01A meta = service.getC122M01A_byOid(mainOid);
		if (meta == null) {
			throw new CapMessageException("[" + mainOid + "]not found",
					getClass());
		}
		if (Util.isEmpty(memo)) {
			throw new CapMessageException(
					prop_cls1220v10.getProperty("changeOwnBrId_memo") + "不可空白",
					getClass());
		}
		if (Util.equals(meta.getOwnBrId(), newBrNo)) {
			throw new CapMessageException("未更新分行別", getClass());
		}
		if (Util.equals(meta.getStatFlag(), "2")) {
			// C122M01A.statFlag=申貸案件狀態
			// C122M01A.statFlag.applyKindP.2=已核貸
			throw new CapMessageException(
					"不可改派("
							+ prop_cls1220m04.getProperty("C122M01A.statFlag")
							+ "："
							+ prop_cls1220m04.getProperty("C122M01A.statFlag.applyKindE.2")
							+ ")", getClass());
		}
		if (LMSUtil.cmpDate(CapDate.addMonth(meta.getApplyTS(), 1), "<",
				CapDate.getCurrentTimestamp())) {
			throw new CapMessageException("不可改派("
					+ prop_cls1220m04.getProperty("C122M01A.applyTS") + "："
					+ TWNDate.toAD(meta.getApplyTS()) + ")", getClass());
		}

		C122M01C c122m01c = service.changeOwnBrId(meta, newBrNo, memo);
		if (c122m01c != null) {
			service.changeOwnBrId_notifyT1(meta.getPloanCaseId(),
					meta.getApplyKind(), c122m01c, meta.getCustId(),
					meta.getCustName());
		}
		
		//改派分行
		//先移除C122S01H的徵信資料綁定
		List<C122S01H> c122s01hList = service.findC122S01H(meta.getMainId(),UtilConstants.C122s01h_flowId.借保人資料);
		if (c122s01hList != null && c122s01hList.size() > 0) {
			for(int i=0;i<c122s01hList.size();i++){
				C122S01H s01y = c122s01hList.get(i);
				if (s01y != null) {
					cls1220Service.deleteC122S01H(s01y);
				}
			}
		}
		//變更案件流程狀態(META於service.changeOwnBrId會被處理，先刷新一下)
		meta = service.getC122M01A_byOid(mainOid);
		meta.setDocStatus(UtilConstants.C122_DocStatus.待派案);
		String c122s01fMemo = "移轉分行(";
		c122s01fMemo = c122s01fMemo + Util.trim(c122m01c.getBrNoBef()) + Util.trim(branchService.getBranchName(Util.trim(c122m01c.getBrNoBef())));
		c122s01fMemo = c122s01fMemo + " --> ";
		c122s01fMemo = c122s01fMemo + Util.trim(c122m01c.getBrNoAft()) + branchService.getBranchName(Util.trim(c122m01c.getBrNoAft()));
		c122s01fMemo = c122s01fMemo + ")";
		clsService.addC122S01F(meta,UtilConstants.C122s01f_otherFlag.轉移分行,c122s01fMemo,c122m01c.getOid());
		// 因為移轉分行需要重新派案，把簽案行員的欄位清掉
		C122M01F c122m01f = service.findC122M01F(meta.getMainId());
		if(c122m01f!=null){
			c122m01f.setSignMegaEmpNo("");
			c122m01f.setSignMegaEmpName("");
			service.save(c122m01f);
		}
		
		service.save(meta);
		
		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult C122M01A_checkDocStatus(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C122M01A meta = service.getC122M01A_byOid(mainOid);
		
		boolean success = true;
		String message = "";
		
		if (Util.equals(Util.trim(meta.getIncomType()), "1")) { // 人工進件案件不可改派
			success = false;
			message = prop_cls1220v10.getProperty("Message.changeBchid.In");
		}
		//房貸案件才可以改派分行，信貸只能由943或900執行
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String ssoUnitNo = user.getSsoUnitNo();
		if(Util.equals(Util.trim(meta.getApplyKind()), UtilConstants.C122_ApplyKind.P) && Util.notEquals(ssoUnitNo, "943") && Util.notEquals(ssoUnitNo, "900")){
			success = false;
			message = message + "<br/>";
			message = message + prop_cls1220v10.getProperty("Message.changeBchid.ApplyKindP");
		}
		
		result.set("Success", success);
		result.set("Message", message);
		
		return result;
	}

	/**
	 * 檢查該客戶是否於C101M01A已存在相關徵信資料
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unused")
	@DomainAuth(value = AuthType.Modify)
	public IResult checkCustDateExist(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString("mainOid"));
		String mainId = Util.trim(params.getString("mainId"));
		C122M01A c122m01a = service.getC122M01A_byMainId(mainId);
		String dupNo = null;
		String incomType = Util.trim(c122m01a.getIncomType());
		if (Util.equals(incomType, "1")) { // 線下進件
			dupNo = c122m01a.getDupNo();
		}
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<C101M01A> c101m01aList = cls1220Service.findC101M01A(
				user.getUnitNo(), c122m01a.getCustId(), dupNo);
		boolean DataExist = true;
		if (c101m01aList != null && c101m01aList.size() > 0) {// 有對應資料
		} else {// new新資料
			DataExist = false;
		}
		result.set("DataExist", DataExist);
		return result;
	}

	/**
	 * 更新借保人資料
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult UpdateCust(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		// List<String> errorMessageList = new ArrayList<String>();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String oid = Util.trim(params.getString("mainOid"));
		String mainId = Util.trim(params.getString("mainId"));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		C122M01A c122m01a = service.getC122M01A_byMainId(mainId);
		C122S01C c122s01c = null;
		boolean isYLOnlineCase=false;//是否為青創線上進件
		if( (Util.equals(UtilConstants.C122_ApplyKind.I, c122m01a.getApplyKind())
				|| Util.equals(UtilConstants.C122_ApplyKind.J, c122m01a.getApplyKind()))
				&& Util.equals("2",c122m01a.getIncomType())){//青創線上進件
			isYLOnlineCase=true;
			c122s01c = service.getC122S01C(mainId);
		}

		// 根據畫面條件找到C101M01A的對應資料
		C101M01A c101m01a = cls1131Service.findC101M01A(user.getUnitNo(),
				custId, dupNo);
		// 更新欄位，更新欄位參照個金徵信作業中，同步PLOAN的欄位
		C101S01A c101s01a = clsService.findC101S01A(c101m01a);
		C120S01A c120s01a = service.findC120S01A(c122m01a.getMainId(),
				c122m01a.getCustId(), c122m01a.getDupNo());
		if(isYLOnlineCase){//青創線上進件要從青創的欄位去複製
			if(c122s01c!=null && c101s01a != null){
				CopyC122s01cToC101s01a(c122s01c,c101s01a);
			}
			
		}else{
			if(c120s01a!=null && c101s01a!=null ){
				CapBeanUtil.copyBean(c120s01a, c101s01a, new String[] { "birthday",
				"edu", "fCity", "fZip", "fAddr", "fTarget", "coCity", "coZip",
				"coAddr", "coTarget", "houseStatus", "mTel", "email" }); // 線上貸款的{住宅電話}可能是
																			// 租屋處
																			// 電話，不應寫入
																			// 0024
			}																// 的戶籍電話
		}

		C101S01B c101s01b = clsService.findC101S01B(c101m01a);
		C120S01B c120s01b = service.findC120S01B(c122m01a.getMainId(),
				c122m01a.getCustId(), c122m01a.getDupNo());
		if(isYLOnlineCase){//青創線上進件要從青創的欄位去複製
			if(c122s01c!=null && c101s01b != null){
				CopyC122s01cToC101s01b(c122s01c,c101s01b);
			}
		}else{
			if(c120s01b!=null && c101s01b!=null ){
				CapBeanUtil.copyBean(c120s01b, c101s01b, new String[] { "jobType1",
				"jobType2", "comName", "seniority", "juId", "comTel",
				"comCity", "comZip", "comAddr", "comTarget", "payAmt" });
			}
		}

		clsService.daoSave(c101s01a);
		clsService.daoSave(c101s01b);

		result.set("Success", true);
		return result;
	}
	
	private void CopyC122s01cToC101s01a(C122S01C c122s01c, C101S01A c101s01a)throws CapException {
		CodeTypeFormatter cityFormat = new CodeTypeFormatter(
				codetypeService, "counties", CodeTypeFormatter.ShowTypeEnum.Desc);

		try {
			String eLevel = "";//學歷(青創-教育程度)
			if (Util.isNotEmpty(c122s01c.getElevel())){
				int eLevelInt=Integer.parseInt(c122s01c.getElevel());
				switch (eLevelInt){//轉換為[基本資料]頁籤的代碼
					case 1://國中/小
						eLevel= "02";//國中(一律轉為國中)
						break;
					case 2://高中職
						eLevel= "03";
						break;
					case 3://專科
						eLevel= "04";
						break;
					case 4://大學
						eLevel= "05";
						break;
					case 5://碩士
						eLevel= "06";
						break;
					case 6://博士
						eLevel= "07";
						break;
				}
			}
			StringBuilder addr1 = new StringBuilder();//戶籍地址-全部文字
			String citya = cityFormat.reformat(CapString.trimNull(c122s01c.getCitya()));
			String cityAreaa = new CodeTypeFormatter(codetypeService,
					"counties" + CapString.trimNull(c122s01c.getCitya()),
					CodeTypeFormatter.ShowTypeEnum.Desc).reformat(CapString.trimNull(c122s01c
					.getCityareaa()));
			addr1.append(citya).append(cityAreaa).append(CapString.trimNull(c122s01c.getAddress()));
		
			StringBuilder addr2 = new StringBuilder();//通訊地址
			String cityb = cityFormat.reformat(CapString.trimNull(c122s01c.getCityb()));
			String cityAreab = new CodeTypeFormatter(codetypeService,
					"counties" + CapString.trimNull(c122s01c.getCityb()),
					CodeTypeFormatter.ShowTypeEnum.Desc).reformat(CapString.trimNull(c122s01c
					.getCityareab()));
			addr2.append(cityb).append(cityAreab).append(CapString.trimNull(c122s01c.getAddress2()));
		
			c101s01a.setBirthday(c122s01c.getBday());//生日
			c101s01a.setEdu(eLevel);//學歷
			c101s01a.setMTel(CapString.trimNull(c122s01c.getTel()));//行動電話(青創-連絡電話)
			c101s01a.setEmail(CapString.trimNull(c122s01c.getEmail()));//email
			c101s01a.setFCity(CapString.trimNull(c122s01c.getCitya()));//戶籍地址-縣市
			c101s01a.setFZip(CapString.trimNull(c122s01c.getCityareaa()));//戶籍地址-區市鄉鎮
			c101s01a.setFAddr(CapString.trimNull(c122s01c.getAddress()));//戶籍地址-地址
			c101s01a.setFTarget(addr1.toString());//戶籍地址-全部文字
			c101s01a.setCoCity(CapString.trimNull(c122s01c.getCityb()));//通訊地址-縣市
			c101s01a.setCoZip(CapString.trimNull(c122s01c.getCityareab()));//通訊地址-區市鄉鎮
			c101s01a.setCoAddr(CapString.trimNull(c122s01c.getAddress2()));//通訊地址-地址
			c101s01a.setCoTarget(addr2.toString());//通訊地址-全部文字
		} catch (CapFormatException e) {
			logger.error(StrUtils.getStackTrace(e));
			throw new CapException(e, getClass());
		}
	}
	
	private void CopyC122s01cToC101s01b(C122S01C c122s01c, C101S01B c101s01b) throws CapException {
		CodeTypeFormatter cityFormat = new CodeTypeFormatter(
				codetypeService, "counties", CodeTypeFormatter.ShowTypeEnum.Desc);
		try{
			StringBuilder busAddr= new StringBuilder();//營業地址
			String city = cityFormat.reformat(CapString.trimNull(c122s01c.getCity()));
			String cityArea = new CodeTypeFormatter(codetypeService,
					"counties" + CapString.trimNull(c122s01c.getCity()),
					CodeTypeFormatter.ShowTypeEnum.Desc).reformat(CapString.trimNull(c122s01c
					.getCityarea()));
			busAddr.append(city).append(cityArea).append(CapString.trimNull(c122s01c.getN_address()));
			c101s01b.setJuId(CapString.trimNull(c122s01c.getN_cnumber()));
			c101s01b.setComName(CapString.trimNull(c122s01c.getCname()));
			c101s01b.setComCity(CapString.trimNull(c122s01c.getCity()));
			c101s01b.setComZip(CapString.trimNull(c122s01c.getCityarea()));
			c101s01b.setComAddr(CapString.trimNull(c122s01c.getN_address()));
			c101s01b.setComTarget(busAddr.toString());//營業地址-全部文字
			c101s01b.setComTel(CapString.trimNull(c122s01c.getN_tel()));
			
		}catch (CapFormatException e) {
			logger.error(StrUtils.getStackTrace(e));
			throw new CapException(e, getClass());
		}
	}

	/**
	 * 新增借保人資料
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult AddNewCust(PageParameters params)
			throws CapException {
		String oid = Util.trim(params.getString("mainOid"));
		String mainId = Util.trim(params.getString("mainId"));
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		C122M01A c122m01a = service.getC122M01A_byMainId(mainId);
		String dupNo = "0";
		if (Util.equals(c122m01a.getIncomType(), "1")) { // 線下進件
			dupNo = c122m01a.getDupNo();
		}

		// 產生個金資料檔
		createNewCust(user, c122m01a.getCustId(), dupNo, c122m01a.getCustName());
		result.set("mainOid", c122m01a.getOid());
		result.set("mainId", c122m01a.getMainId());
		result.set("custId", c122m01a.getCustId());
		result.set("dupNo", dupNo);
		result.set("Success", true);

		return result;
	}

	/**
	 * 變更案件狀態 >> 系統徵信
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult updateDocStatus_B00(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString("mainOid"));
		String mainId = Util.trim(params.getString("mainId"));

		C122M01A c122m01a = service.getC122M01A_byMainId(mainId);

		if (c122m01a != null) {
			c122m01a.setDocStatus(UtilConstants.C122_DocStatus.系統徵信);
			service.save(c122m01a);
			result.set("Success", true);
			result.set("Message",
					prop_cls1220m04.getProperty("Message.sendCheck.11"));
		} else {
			result.set("Success", false);
			result.set("Message",
					prop_cls1220m04.getProperty("Message.sendCheck.10"));
		}

		return result;
	}

	/**
	 * 變更案件狀態 >> 補件通知
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult updateDocStatus_A02(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString("mainOid"));
		String mainId = Util.trim(params.getString("mainId"));
		C122M01A c122m01a = service.getC122M01A_byMainId(mainId);
		if (c122m01a != null) {
			c122m01a.setDocStatus(UtilConstants.C122_DocStatus.補件通知);
			c122m01a.setStatFlag(ClsConstants.C122M01A_StatFlag.待補件);
			service.save(c122m01a);
			clsService.addC122S01F(c122m01a,c122m01a.getDocStatus(),null,null);
			result.set("Success", true);
			result.set("docStatus", UtilConstants.C122_DocStatus.補件通知);
			result.set("Message",
					prop_cls1220m04.getProperty("Message.sendCheck.12"));

		} else {
			result.set("Success", false);
			result.set("Message",
					prop_cls1220m04.getProperty("Message.sendCheck.10"));
		}

		return result;
	}

	private void createNewCust(MegaSSOUserDetails user, String custId,
			String dupNo, String custName) {
		boolean naturalFlag = LMSUtil.check2(custId); // 是否為自然人
		JSONObject addJson = new JSONObject();
		addJson.put(EloanConstants.MAIN_ID, IDGenerator.getUUID());
		addJson.put("ownBrId", user.getUnitNo());
		addJson.put("custId", custId);
		addJson.put("dupNo",
				Util.isEmpty(dupNo) ? UtilConstants.Mark.ZEROISNODATA : dupNo);
		addJson.put("custName", custName);
		// default value
		addJson.put("markModel", UtilConstants.L140S02AModelKind.免辦);// 先填入default,讓經辦選擇[房貸、非房貸、免辦]
		addJson.put("mateFlag", ClsConstants.MateFlag.不登錄配偶資料);
		addJson.put("chkFlag", ClsConstants.Default.是否填列);
		addJson.put("isFromOld", UtilConstants.DEFAULT.否);
		addJson.put("randomCode", IDGenerator.getUUID());
		if (naturalFlag) {
			addJson.put("typCd", TypCdEnum.DBU.getCode());
			addJson.put("ntCode", ClsConstants.Default.國別);
			addJson.put("payCurr", ClsConstants.Default.幣別);
			addJson.put("othCurr", ClsConstants.Default.幣別);
			addJson.put("oMoneyCurr", ClsConstants.Default.幣別);
			addJson.put("yFamCurr", ClsConstants.Default.幣別);
			addJson.put("invMBalCurr", ClsConstants.Default.幣別);
			addJson.put("invOBalCurr", ClsConstants.Default.幣別);
			addJson.put("branCurr", ClsConstants.Default.幣別);
			addJson.put("fincomeCurr", ClsConstants.Default.幣別);
			addJson.put("mPayCurr", ClsConstants.Default.幣別);
			addJson.put("naturalFlag", UtilConstants.DEFAULT.是);
			addJson.put("importFlag", UtilConstants.DEFAULT.否);
		} else {
			addJson.put("typCd", this.checkTypCd(custId));
			addJson.put("naturalFlag", UtilConstants.DEFAULT.否);
			addJson.put("importFlag", UtilConstants.DEFAULT.是);
		}

		List<GenericBean> list = new ArrayList<GenericBean>();

		Class<?>[] clazzs = { C101M01A.class, C101S01A.class, C101S01B.class,
				C101S01C.class, C101S01D.class, C101S01E.class, C101S01F.class,
				C101S01G.class, C101S01Q.class , C101S02C.class };
		for (Class<?> clazz : clazzs) {
			if (LMSUtil.disableC101S01F_C120S01F() && clazz == C101S01F.class) {
				continue;
			}
			GenericBean model = cls1131Service.findModelByOid(clazz,
					UtilConstants.Mark.SPACE, true);
			DataParse.toBean(addJson, model);
			if (clazz == C101S01A.class) {
				// Map<String, Object> latestData =
				// iCustomerService.findByIdDupNo(custId, dupNo); ==> 此 method
				// 未回傳 生日
				Map<String, Object> latestData = cls1131Service
						.findMisCustData(custId, dupNo);
				if (MapUtils.isNotEmpty(latestData)) {
					Object birthDt_obj = MapUtils.getObject(latestData,
							"BIRTHDT");
					if (birthDt_obj instanceof Date) {
						Date birthDt = (Date) birthDt_obj;
						if (birthDt != null) {
							((C101S01A) model).setBirthday(birthDt);
						}
					}
				}
			}
			list.add(model);
		}
		// 儲存
		cls1131Service.save(list);
		// 重新查詢
		// c101m01a = cls1131Service.findC101M01A(user.getUnitNo(), custId,
		// dupNo);
	}

	/**
	 * 變更案件狀態 >> 不承作
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult CaseClosed(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString("mainOid"));
		String mainId = Util.trim(params.getString("mainId"));

		String CaseClosedType = Util.trim(params.getString("CaseClosedType"));
		C122M01A c122m01a = service.getC122M01A_byMainId(mainId);

		if (c122m01a != null) {
			//檢核，當項目為[拒絕]時，C122S01G至少要有一筆拒絕理由
			boolean checkC122s01g = true;
			if(Util.equals(CaseClosedType, "G")){
				List<C122S01G> c122s01gList = service.findC122S01G(mainId,
						UtilConstants.C122_DocStatus.不承作);
				if (c122s01gList != null && c122s01gList.size() > 0) {
				}else{
					checkC122s01g = false;
				}
			}
			if(checkC122s01g){
				String ploanCaseId = Util.trim(c122m01a.getPloanCaseId());
				String ploanCaseNo = Util.trim(c122m01a.getPloanCaseNo());
				String incomType = Util.trim(c122m01a.getIncomType());
				
				//取消-先處理取消項目先關作業
				if (Util.equals(CaseClosedType, "I")) { // 取消
					String CaseClosedItemI = Util.trim(params
							.getString("CaseClosedItemI"));
					Map<String, String> CaseClosedItemI_Map = codeTypeService.findByCodeType("c122m01a_docstatus_I",
							LocaleContextHolder.getLocale().toString());
					String CodeDesc = CaseClosedItemI_Map.get(CaseClosedItemI);
					C122S01G c122s01g = new C122S01G();
					c122s01g = createC122s01g(mainId,
							UtilConstants.C122_DocStatus.取消, CaseClosedItemI,
							CodeDesc, params.getString("_userId"));
					service.saveC122S01G(c122s01g);
				}
				//開始處理c122m01a的狀態
				if(Util.equals(incomType, UtilConstants.C122_IncomType.線下)){ //線下進件,無須回傳PLOAN (目前線下作業無從債務人，先這樣)
					if (Util.equals(CaseClosedType, "I")) { // 取消
						c122m01a.setDocStatus(UtilConstants.C122_DocStatus.取消);
						c122m01a.setStatFlag(ClsConstants.C122M01A_StatFlag.客戶撤件);
					} else if (Util.equals(CaseClosedType, "G")) { // 拒絕
						c122m01a.setDocStatus(UtilConstants.C122_DocStatus.不承作);
						c122m01a.setStatFlag(ClsConstants.C122M01A_StatFlag.票債信不良); // 統一放這個，回傳PLONA僅會回傳[不承做]
					} else if (Util.equals(CaseClosedType, "E")) { // 結案
						c122m01a.setDocStatus(UtilConstants.C122_DocStatus.動用已覆核);
						c122m01a.setStatFlag(ClsConstants.C122M01A_StatFlag.動審表已覆核);
					}
					// J-112-0329 配合消金處，E-LOAN個金授信管理系統清整歡喜信貸案件明細表相關資料欄位導入DW
					c122m01a.setIs_uploaded_dw_hpcl("N");// 押N，每日批次上傳非已結案資料至DW時，可被撈出並上傳
					service.save(c122m01a);
					clsService.addC122S01F(c122m01a,c122m01a.getDocStatus(),null,null);
				}else{
					MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
					String updater = user.getUserId();
					if(Util.equals(ploanCaseNo, ploanCaseId)){
						//作廢 主借人 (底下的從債務人，也會一併結案)
//						service.ploan_discard_loanCase(updater, ploanCaseId);
						for(C122M01A model_c122m01a : service.findC122M01A_by_ploanCaseId(ploanCaseId)){
							if (Util.equals(CaseClosedType, "I")) { // 取消
								model_c122m01a.setDocStatus(UtilConstants.C122_DocStatus.取消);
								model_c122m01a.setStatFlag(ClsConstants.C122M01A_StatFlag.客戶撤件);
							} else if (Util.equals(CaseClosedType, "G")) { // 拒絕
								model_c122m01a.setDocStatus(UtilConstants.C122_DocStatus.不承作);
								model_c122m01a.setStatFlag(ClsConstants.C122M01A_StatFlag.票債信不良); // 統一放這個，回傳PLONA僅會回傳[不承做]
							} else if (Util.equals(CaseClosedType, "E")) { // 結案
								model_c122m01a.setDocStatus(UtilConstants.C122_DocStatus.動用已覆核);
								model_c122m01a.setStatFlag(ClsConstants.C122M01A_StatFlag.動審表已覆核);
							}
							// J-112-0329 配合消金處，E-LOAN個金授信管理系統清整歡喜信貸案件明細表相關資料欄位導入DW
							c122m01a.setIs_uploaded_dw_hpcl("N");// 押N，每日批次上傳非已結案資料至DW時，可被撈出並上傳
							service.save(model_c122m01a);
							clsService.addC122S01F(model_c122m01a,model_c122m01a.getDocStatus(),null,null);//增加作廢流程紀錄
						}	
					}else{
						//無從債務人
//						service.ploan_discard_loanCase(updater, ploanCaseNo);
						if (Util.equals(CaseClosedType, "I")) { // 取消
							c122m01a.setDocStatus(UtilConstants.C122_DocStatus.取消);
							c122m01a.setStatFlag(ClsConstants.C122M01A_StatFlag.客戶撤件);
						} else if (Util.equals(CaseClosedType, "G")) { // 拒絕
							c122m01a.setDocStatus(UtilConstants.C122_DocStatus.不承作);
							c122m01a.setStatFlag(ClsConstants.C122M01A_StatFlag.票債信不良); // 統一放這個，回傳PLONA僅會回傳[不承做]
						} else if (Util.equals(CaseClosedType, "E")) { // 結案
							c122m01a.setDocStatus(UtilConstants.C122_DocStatus.動用已覆核);
							c122m01a.setStatFlag(ClsConstants.C122M01A_StatFlag.動審表已覆核);
						}
						// J-112-0329 配合消金處，E-LOAN個金授信管理系統清整歡喜信貸案件明細表相關資料欄位導入DW
						c122m01a.setIs_uploaded_dw_hpcl("N");// 押N，每日批次上傳非已結案資料至DW時，可被撈出並上傳
						service.save(c122m01a);
						clsService.addC122S01F(c122m01a,c122m01a.getDocStatus(),null,null);//增加作廢流程紀錄
					}
				}
				// 若選擇的項目是[取消]、[結案]，要把已選的拒絕選項刪除
				if (Util.equals(CaseClosedType, "E")
						|| Util.equals(CaseClosedType, "I")) {
					List<C122S01G> c122s01gList = service.findC122S01G(mainId,
							UtilConstants.C122_DocStatus.不承作);
					if (c122s01gList != null && c122s01gList.size() > 0) {
						for (int i = 0; i < c122s01gList.size(); i++) {
							C122S01G c122s01g = c122s01gList.get(i);
							cls1220Service.deleteC122S01G(c122s01g);
						}
					}
				}
				result.set("Success", true);
				result.set("Message",
						prop_cls1220m04.getProperty("Message.sendCheck.13"));
				
			}else{
				result.set("Success", false);
				result.set("Message",
						prop_cls1220m04.getProperty("Message.dataCheck.03"));
			}
		} else {
			result.set("Success", false);
			result.set("Message",
					prop_cls1220m04.getProperty("Message.sendCheck.10"));
		}

		return result;
	}

	/**
	 * 不承作 >> 新增不承做理由
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult addCaseClosedItemG(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString("mainOid"));
		String mainId = Util.trim(params.getString("mainId"));
		String CaseClosedItemG = Util.trim(params.getString("CaseClosedItemG"));
		boolean pass = true;

		// 檢核項目不要重複
		List<C122S01G> c122s01gList = service.findC122S01G(mainId,
				UtilConstants.C122_DocStatus.不承作);
		if (c122s01gList != null && c122s01gList.size() > 0) {
			for (int i = 0; i < c122s01gList.size(); i++) {
				C122S01G c122s01g = c122s01gList.get(i);
				String codeValue = Util.trim(c122s01g.getCodeValue());
				if (Util.equals(codeValue, CaseClosedItemG)) {
					// 已新增過一樣的項目
					pass = false;
					break;
				}
			}
		}

		if (pass) {
			Map<String, String> CaseClosedItemG_Map = codeTypeService.findByCodeType("c122m01a_docstatus_G",
					LocaleContextHolder.getLocale().toString());
			String CodeDesc = CaseClosedItemG_Map.get(CaseClosedItemG);
			if (Util.equals(CaseClosedItemG, "G99")) { // 其他
				CodeDesc = Util.trim(params.getString("otherReason"));
			}
			C122S01G c122s01g = new C122S01G();
			c122s01g = createC122s01g(mainId, UtilConstants.C122_DocStatus.不承作,
					CaseClosedItemG, CodeDesc, params.getString("_userId"));
			service.saveC122S01G(c122s01g);

			// c122s01g.setMainId(mainId);
			// c122s01g.setUid(mainId);
			// c122s01g.setDocStatus(UtilConstants.C122_DocStatus.不承作);
			// c122s01g.setCodeValue(CaseClosedItemG);
			// c122s01g.setCodeDesc(CodeDesc);
			// c122s01g.setCreator(params.getString("_userId"));
			// c122s01g.setCreateTime(CapDate.getCurrentTimestamp());

			result.set("Success", true);
		} else {
			result.set("Success", false);
			result.set("Message", prop_cls1220m04.getProperty("Message.addG00"));
		}

		return result;
	}

	/**
	 * 不承作 >> 刪除不承做理由(單筆_OID)
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult deleteCaseClosedItemG(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString("oid"));

		C122S01G s01y = this.cls1220Service.getC122S01G(oid);
		if (s01y != null) {
			cls1220Service.deleteC122S01G(s01y);
		}

		return result;
	}

	/**
	 * 不承作 >> 刪除不承做理由(多筆_MAINID)
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult deleteCaseClosedItemG_fromMainid(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString("mainId"));
		String CaseClosedType = Util.trim(params.getString("CaseClosedType"));

		// 若選擇的項目是[取消]、[結案]，要把已選的拒絕選項刪除
		if (Util.equals(CaseClosedType, "E")
				|| Util.equals(CaseClosedType, "I")) {
			List<C122S01G> c122s01gList = service.findC122S01G(mainId,
					UtilConstants.C122_DocStatus.不承作);
			if (c122s01gList != null && c122s01gList.size() > 0) {
				for (int i = 0; i < c122s01gList.size(); i++) {
					C122S01G c122s01g = c122s01gList.get(i);
					cls1220Service.deleteC122S01G(c122s01g);
				}
			}
		}
		result.set("Success", true);
		return result;
	}

	@DomainAuth(AuthType.Modify)
	public IResult newC122S01H_flowId01(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString("mainId"));
		String flowId = Util.trim(params.getString("flowId"));
		boolean checkOnly = params.getBoolean("checkOnly");
		C122M01A c122m01a = service.getC122M01A_byMainId(mainId);
		// 檢查是否已存在選擇的徵信資料
		List<C122S01H> c122s01hList = service.findC122S01H(mainId,flowId);
		boolean rpaError = false;

		if (c122s01hList.size() > 0) { // 資料已存在不可新增
			result.set("Success", false);
			String msg = prop_cls1220m04.getProperty("Message.sendCheck.14");
			if (c122s01hList.get(0).getFlowId().equals(UtilConstants.C122s01h_flowId.借保人資料)) {
				msg = prop_cls1220m04.getProperty("Message.sendCheck.14");

			} else if (c122s01hList.get(0).getFlowId().equals(UtilConstants.C122s01h_flowId.案件簽報書)){
				msg= prop_cls1220m04.getProperty("Message.sendCheck.16");
				throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, msg),
						getClass());
			}
			result.set("Message",msg);
		} else {
			if (c122m01a != null ) {
				if (!checkOnly) {
					String docStatus = flowId.equals(UtilConstants.C122s01h_flowId.借保人資料) ? UtilConstants.C122_DocStatus.徵信照會 :UtilConstants.C122_DocStatus.簽案作業;
					if (!c122m01a.getDocStatus().equals(docStatus)) {
						c122m01a.setDocStatus(docStatus);
						service.save(c122m01a);
					}
					C122S01H c122s01h = new C122S01H();
					CapBeanUtil.map2Bean(params, c122s01h, new String[] { "mainId",
							"custId", "dupNo", "custName", "flowId", "remainId" });
					c122s01h.setFlowdocStatus(docStatus);
					c122s01h.setCreator(params.getString("_userId"));
					c122s01h.setCreateTime(CapDate.getCurrentTimestamp());
					cls1220Service.save(c122s01h);
					//J-112-0006 發動徵信時若為地政士引介案件要協助以現有地政士名單發動RPA
					if(Util.isNotEmpty(c122m01a.getC122m01f()) &&
							Util.equals(c122m01a.getC122m01f().getIntroduceSrc(),"4")){
						List<C122S01Y> laaList= cls1220Service.findC122S01YbyMainId(mainId);//本案的地政士清單
						if(Util.isNotEmpty(laaList) && laaList.size() > 0){
							String mainId_for_RPA = c122s01h.getRemainId();
							for(C122S01Y c122s01y : laaList){
								String queryLaaName = Util.trim(c122s01y.getLaaName());
								try{
									cls1131Service.queryRpaQueryLaaName(mainId_for_RPA, queryLaaName);
								} catch (CapException e) {
									if(!rpaError){
										rpaError = true;
									}
								}
							}
						}
					}

					//J-113-0199 web eLoan行員自動過件
					boolean bankManFlag = misdbBASEService
							.isBankMan_on_the_job(c122m01a.getCustId()) || clsService.is_function_on_codetype("autoCheck_bankManFlag");
					String refMainId = Util.trim(params.getString("remainId"));
					String custId = Util.trim(params.getString("custId"));
					String dupNo = Util.trim(params.getString("dupNo"));
					if (bankManFlag && Util.equals(UtilConstants.C122_ApplyKind.P, c122m01a.getApplyKind())) {
						MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
						C101M01A c101m01a = cls1131Service.findC101M01A(user.getUnitNo(),
								custId, dupNo);
						if (c101m01a!=null) {
							if (Util.isEmpty(c101m01a.getMarkModel()) ||
									Util.equals(c101m01a.getMarkModel(),UtilConstants.L140S02AModelKind.免辦)) {
								c101m01a.setMarkModel("2");
							} else {
								if (!c101m01a.getMarkModel().contains("2")) {
									c101m01a.setMarkModel("2|"+c101m01a.getMarkModel());
								}
							}
							c101m01a.setProdKindFlag(UtilConstants.DEFAULT.是);
							cls1220Service.save(c101m01a);

							C101S02C c101s02c = cls1131Service.findC101S02C(c101m01a.getMainId());
							if (c101s02c==null) {
								c101s02c = new C101S02C();
								c101s02c.setMainId(c101m01a.getMainId());
								c101s02c.setCustId(c101m01a.getCustId());
								c101s02c.setDupNo(c101m01a.getDupNo());
								c101s02c.setCreateTime(CapDate.getCurrentTimestamp());
								c101s02c.setCreator(user.getUserId());
							}
							c101s02c.setLoanAmt(c122m01a.getApplyAmt());
							c101s02c.setLnYear(Util.parseInt(pretty_numStr(Util.trim(c122m01a.getMaturity()))));
							c101s02c.setLnMonth(Util.parseInt(pretty_numStr(Util.trim(c122m01a.getMaturityM()))));
							c101s02c.setProdKind(ProdService.ProdKindEnum.一般消貸含團體消貸_07.getCode());
							c101s02c.setInstallmentPay(null);
							c101s02c.setUpdateTime(CapDate.getCurrentTimestamp());
							c101s02c.setUpdater(user.getUserId());
							cls1220Service.save(c101s02c);
						}
					}

					result.set("docStatus", docStatus);
					result.set("Success", true);
					result.set("Message",
							prop_cls1220m04.getProperty("Message.sendCheck.13"));
					if(rpaError){
						result.set("rpaError", prop_cls1220m04.getProperty("Message.sendCheck.15"));
					}
				}else{
					result.set("Success", true);
				}
			} else {
				result.set("Success", false);
				result.set("Message",
						prop_cls1220m04.getProperty("Message.sendCheck.10"));
			}
		}
		
		return result;
	}

	/**
	 * 刪除已選擇的徵信資料
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult deleteDocStatusGrid(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString("oids"));

		C122S01H s01y = this.cls1220Service.getC122S01H(oid);
		if (s01y != null) {
			List<C122S01H> c122s01hs = cls1220Service.findC122S01H(s01y.getMainId(),null);
			if (s01y.getFlowId().equals(UtilConstants.C122s01h_flowId.借保人資料) && c122s01hs.size() > 1) {
				result.set("Success", false);
				result.set("Message",
						prop_cls1220m04.getProperty("Message.sendCheck.16"));
			}
			else{
				result.set("Success", true);
				cls1220Service.deleteC122S01H(s01y);
			}
		}
		return result;
	}

	/**
	 * 查詢
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult getDwDataByIdNo(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString("mainId"));
		String juId = Util.trim(params.getString("juId"));
		// C122M01A c122m01a = service.getC122M01A_byMainId(mainId);

		Map<String, Object> custOrgType = dwdbService
				.findOTS_DW_BGMOPENByIdNo(juId);
		if (custOrgType != null && !custOrgType.isEmpty()) {
			String addr = Util.trim(MapUtils
					.getString(custOrgType, "BUS_ADDRS")); // 地址
			result.set("comTarget", addr);
			// 分解地址
			String cityCN = addr.replaceAll("^(.+)([縣市])(.+)([區鎮鄉市])(.+)$",
					"$1$2");
			cityCN = cityCN.replace("臺", "台");
			CodeType cityCodeType = codeTypeService.findByTypeAndDesc(
					"counties", cityCN);
			result.set("AddrCity",
					cityCodeType != null ? cityCodeType.getCodeValue() : "");
			String countyCN = addr.replaceAll("^(.+)([縣市])(.+)([區鎮鄉市])(.+)$",
					"$3$4");
			CodeType countyCodeType = codeTypeService.findByTypeAndDesc(
					"counties" + cityCodeType.getCodeValue(), countyCN);
			result.set("AddrZip",
					countyCodeType != null ? countyCodeType.getCodeValue() : "");
			result.set("AddrAddr",
					addr.replaceAll("^(.+)([縣市])(.+)([區鎮鄉市])(.+)$", "$5"));

			String entity = Util.trim(MapUtils.getString(custOrgType,
					"ENTITY_NAME")); // 服務單位名稱
			result.set("comName", entity);// 服務單位名稱
			result.set("Success", true);

		} else {
			result.set("Success", false);
		}

		return result;
	}
	

	/**
		 * 變更簽案人員
		 * 
		 * @param params
		 * @return
		 * @throws CapException
		 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult changeSignEmp(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString("mainId"));
		String chgSignMegaEmp = Util.trim(params.getString("chgSignMegaEmp"));
		String chgEvaMegaEmpNo = Util.equals(Util.trim(params.getString("chgEvaMegaEmpNo")),"null") ? "" : Util.trim(params.getString("chgEvaMegaEmpNo"));
		String chgEstUnitName = Util.trim(params.getString("chgEstUnitName"));
		
		//檢核
		C122M01A c122m01a = service.getC122M01A_byMainId(mainId);

		if(c122m01a != null){
			C122M01F c122m01f = service.findC122M01F(mainId);
			if(c122m01f == null){//,直接補一筆
				c122m01f = new C122M01F();
				c122m01f.setMainId(c122m01a.getMainId());
				c122m01f.setOwnBrId(c122m01a.getOwnBrId());
				c122m01f.setCustId(c122m01a.getCustId());
				c122m01f.setDupNo(c122m01a.getDupNo());
				c122m01f.setCustName(c122m01a.getCustName());
				c122m01f.setIntroduceSrc("N");
//				if(Util.equals(applyKind, "E")){ //房貸案才要填
//					c122m01f.setEstFlag(Util.trim(params.getString("estFlag")));
//				}else{
					c122m01f.setEstFlag("N");
//				}	
			}
			boolean isSameMegaEmpLaaCase = false;//是否為重複派案
			//地政士引介之房貸案件要檢查是否重複派案
			if(Util.equals(c122m01f.getIntroduceSrc(), "4")
					&& (Util.equals(c122m01a.getApplyKind(), UtilConstants.C122_ApplyKind.E) ||
							Util.equals(c122m01a.getApplyKind(), UtilConstants.C122_ApplyKind.F)) ){
				List<String> sameLaaMegaEmpList = cls1220Service.getSameMegaEmpForLaaCase(MegaSSOSecurityContext.getUnitNo(),mainId);
				if(Util.isNotEmpty(sameLaaMegaEmpList) && sameLaaMegaEmpList.size()>0){
					for(String megaEmp : sameLaaMegaEmpList){
						if(Util.equals(megaEmp, chgSignMegaEmp)){
							isSameMegaEmpLaaCase = true;//新指派人選為重複派案
							break;
						}
					}
				}
			} 
			// 指定收件行行員 >> 確認簽案人及擔保品經辦不為同一人
			// 地政士引介案件不得相同地政士連續派案予相同經辦
			if ((Util.equals(Util.trim(c122m01f.getEstFlag()), "1")
					&& Util.equals(chgSignMegaEmp, chgEvaMegaEmpNo)) || isSameMegaEmpLaaCase ) {
				String errorMessage = "";
				if(Util.equals(Util.trim(c122m01f.getEstFlag()), "1")
						&& Util.equals(chgSignMegaEmp, chgEvaMegaEmpNo)){
					errorMessage = prop_cls1220m04.getProperty("Message.error.01");
				}
				if(isSameMegaEmpLaaCase){
					if(Util.isNotEmpty(errorMessage)){
						errorMessage = errorMessage + "<br>";
					}
					errorMessage = errorMessage + prop_cls1220m04.getProperty("Message.error.02");//相同地政士不得為連續派案予相同簽案行員
				}
				result.set("Success", false);
				result.set("Message", errorMessage);
			}else{
				//姑且先只記錄簽案行員
				String memo = "變更簽案行員(";
				memo = memo + Util.trim(c122m01f.getSignMegaEmpNo()) + Util.trim(c122m01f.getSignMegaEmpName());
				memo = memo + " --> ";
				memo = memo + chgSignMegaEmp + Util.trim(userInfoService.getUserName(chgSignMegaEmp));
				memo = memo + ")";
				
				c122m01f.setSignMegaEmpNo(chgSignMegaEmp);
				c122m01f.setSignMegaEmpName(Util.trim(userInfoService.getUserName(chgSignMegaEmp)));
				c122m01f.setEvaMegaEmpNo(chgEvaMegaEmpNo);
				c122m01f.setEvaMegaEmpName(Util.trim(userInfoService.getUserName(chgEvaMegaEmpNo)));
				c122m01f.setEstUnitName(chgEstUnitName);
				
				//新增一個改派行員的流程 >> 掛Z01
				clsService.addC122S01F(c122m01a, UtilConstants.C122s01f_otherFlag.改派簽案行員,memo,null);	
				result.set("Success", true);
			}
		}
		
		return result;
	}
	
	/**
	 * 狀態恢復
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult caseReturn(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString("mainId"));
		C122M01A c122m01a = service.getC122M01A_byMainId(mainId);
		if(c122m01a != null){
			//清除案件附加狀態
			List<C122S01G> c122s01gList = service.findC122S01GbyMainid(mainId);
			if (c122s01gList != null && c122s01gList.size() > 0) {
				for (int i = 0; i < c122s01gList.size(); i++) {
					C122S01G c122s01g = c122s01gList.get(i);
					cls1220Service.deleteC122S01G(c122s01g);
				}
			}
			
			String ploanCaseId = Util.trim(c122m01a.getPloanCaseId());
			String ploanCaseNo = Util.trim(c122m01a.getPloanCaseNo());
			String incomType = Util.trim(c122m01a.getIncomType());
			if(Util.equals(incomType, UtilConstants.C122_IncomType.線下)){ //線下進件,無須回傳PLOAN (目前線下作業無從債務人，先這樣)
				//回復c122m01a狀態為[待派案]
				c122m01a.setDocStatus(UtilConstants.C122_DocStatus.待派案);
				service.save(c122m01a);
				//建立一筆回復紀錄
				clsService.addC122S01F(c122m01a, UtilConstants.C122s01f_otherFlag.狀態回復,null,null);	
			}else{
				if(Util.equals(ploanCaseNo, ploanCaseId)){
					for(C122M01A model_c122m01a : service.findC122M01A_by_ploanCaseId(ploanCaseId)){
						model_c122m01a.setDocStatus(UtilConstants.C122_DocStatus.待派案);
						service.save(model_c122m01a);
						clsService.addC122S01F(model_c122m01a,model_c122m01a.getDocStatus(),null,null);//增加作廢流程紀錄
					}
				}else{
					//回復c122m01a狀態為[待派案]
					c122m01a.setDocStatus(UtilConstants.C122_DocStatus.待派案);
					service.save(c122m01a);
					//建立一筆回復紀錄
					clsService.addC122S01F(c122m01a, UtilConstants.C122s01f_otherFlag.狀態回復,null,null);	
				}
			}
		}
		result.set("Success", true);
		return result;
	}
	
	/**
	 * 變更原始申貸分行
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult chgOrgBrId(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString("mainId"));
		String chgOrgBrId = Util.trim(params.getString("chgOrgBrId"));
		C122M01A c122m01a = service.getC122M01A_byMainId(mainId);
		if(c122m01a != null){
			String c122s01fMemo = "變更原始申貸分行(";
			c122s01fMemo = c122s01fMemo + Util.trim(c122m01a.getOrgBrId()) + Util.trim(branchService.getBranchName(Util.trim(c122m01a.getOrgBrId())));
			c122s01fMemo = c122s01fMemo + " --> ";
			c122s01fMemo = c122s01fMemo + chgOrgBrId + branchService.getBranchName(chgOrgBrId);
			c122s01fMemo = c122s01fMemo + ")";
			
			//一併更改從債務人
			service.changeOrgBrId(c122m01a, chgOrgBrId);
			
			//建立一筆回復紀錄
			clsService.addC122S01F(c122m01a, UtilConstants.C122s01f_otherFlag.變更原始分行, c122s01fMemo, null);	
		}
		result.set("Success", true);
		result.set("docStatus", c122m01a.getDocStatus());
		return result;
	}
	

	private C122S01G createC122s01g(String mainId, String docStatus,
			String codeValue, String codeDesc, String userId) {
		C122S01G c122s01g = new C122S01G();
		c122s01g.setMainId(mainId);
		c122s01g.setUid(mainId);
		c122s01g.setDocStatus(docStatus);
		c122s01g.setCodeValue(codeValue);
		c122s01g.setCodeDesc(codeDesc);
		c122s01g.setCreator(userId);
		c122s01g.setCreateTime(CapDate.getCurrentTimestamp());

		return c122s01g;

	}
	
	/**
	 * 儲存該分行可被一鍵分案的人員
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveAssignCaseEmps(PageParameters params)
		throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String ownBrId = MegaSSOSecurityContext.getUnitNo();
		// String[] userIds = params.getStringArray("userIds");
		String assignRows = params.getString("assignRows");
		JSONArray assignArray = JSONArray.fromObject(assignRows);
		service.saveAssignCaseEmps(ownBrId, assignArray);
		return result;
	}
	
	/**
	 * 確認是否有待分案案件
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult checkHaveNeedAssignCase(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String ownBrId = MegaSSOSecurityContext.getUnitNo();
		
		String[] assignEmpNos = params.getStringArray("assignEmpNos");
		Map<String, String> resMap = service.checkHaveNeedAssignCase(ownBrId, assignEmpNos);
		
		result.set("needAssignCount", resMap.get("needAssignCount"));// 查出有幾筆待分案數量
		result.set("lastC122m01aOid", resMap.get("lastC122m01aOid"));// 紀錄最後一筆Oid
		return result;
	}
	
	/**
	 * 執行一鍵分案
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult oneButtonAssignCase(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String ownBrId = MegaSSOSecurityContext.getUnitNo();
		String assignCaseRows = params.getString("assignCaseRows");
		JSONArray assignCaseArray = JSONArray.fromObject(assignCaseRows);
		Map<String, Object> assignResultMap = service.oneButtonAssignCase(ownBrId, assignCaseArray);
		result.set("havePassCase", (Boolean) assignResultMap.get("havePassCase"));// 分案訊息
		result.set("message", (String)assignResultMap.get("message"));// 分案訊息
		
		return result;
	}
	
	/**
	 * 產生excel檔名
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult makeExcelFileNmae(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
//		String mainId = Util.trim(params.getString("mainId"));
		String ownBrId = MegaSSOSecurityContext.getUnitNo();
		String applyTS_beg_date = Util.trim(params.getString("applyTS_beg"));
		String applyTS_end_date = Util.trim(params.getString("applyTS_end"));
		String applyKind = Util.trim(params.getString("applyKind"));
		String purchaseHouse = Util.trim(params.getString("purchaseHouse"));
		Map<String, String> cacheMap = new HashMap<String, String>();
		String tableName = "派案登記表";
		if(Util.equals(applyKind, UtilConstants.C122_ApplyKind.E) && Util.equals(purchaseHouse, "Y")){ 
			//[房貸]且為[購屋]表單TITLE變更為[購屋新做房貸案件派案登記表]
			tableName = "購屋新做房貸案件派案登記表";
		}
		String title = ownBrId + getBrName(cacheMap, ownBrId)+ tableName + "("+applyTS_beg_date + "~"+applyTS_end_date+").xls";
		
		result.set("Success", true);
		result.set("fileNmae", title);
		return result;
	}
	private String getBrName(Map<String, String> cacheMap, String brNo) {
		if (!cacheMap.containsKey(brNo)) {
			IBranch obj = branchService.getBranch(brNo);
			if (obj != null) {
				cacheMap.put(brNo, Util.trim(obj.getBrName()));
			}
		}
		return Util.trim(cacheMap.get(brNo));
	}
	
	/**
	 * 判斷借款人區部別
	 * 
	 * @param custId
	 *            客戶統編
	 * @return DBU | OBU
	 */
	private String checkTypCd(String custId) {
		boolean isObuId = LMSUtil.isObuId(custId); // 是否為自然人
		String typCd = "";
		if (isObuId) {
			typCd = TypCdEnum.OBU.getCode();
		} else {
			typCd = TypCdEnum.DBU.getCode();
		}
		return typCd;
	}

	/**
	 * 判斷登入者是否僅有EL電銷權限
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult check_only_expermission(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String docStatus = Util.trim(params.getString("mainDocStatus"));
		
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		boolean masterRoles = user.getRoles().containsKey("EL02"); 
		String ssoUnitNo = user.getSsoUnitNo();
		boolean is943master = false;
		if(masterRoles && Util.equals(ssoUnitNo, "943")){
			is943master = true;
		}
		
		boolean isItemA = false;
		if(docStatus.startsWith("A")){
			isItemA = true;
		}
		boolean isItemB = false;
		if(docStatus.startsWith("B")){
			isItemB = true;
		}
		
		result.set("only_ex_permission", user.isEXAuth());// 是否僅有電銷權限
		result.set("is943master", is943master);// 是否為943主管
		result.set("isItemA", isItemA);// 案件狀態為A大類
		result.set("isItemB", isItemB);// 案件狀態為B大類
		result.set("masterRoles", masterRoles);// 是否為主管
		
		
		return result;

	}
	
	/**
	 * 房貸、其他案發動徵信前檢核相關欄位
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult checkDataBeforeCredit(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String alertMsg = "";
		
		String mainId = Util.trim(params.getString("mainId"));
		//檢查引介來源，若為[地政士引介]要檢查是否有輸入地政士
		alertMsg = service.checkIntroduceSrcLaa(mainId);

		if(Util.isNotEmpty(alertMsg)){
			result.set("alertMsg", alertMsg);
		}
		
		return result;
	}
	
	/**
	 * 發動徵信前檢核青創欄位
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult checkC122s01c(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();		
		
		String mainId = Util.trim(params.getString("mainId"));
		String applyKind = Util.trim(params.getString("applyKind"));
		String alertMsg = "";
		C122S01C c122s01c = service.getC122S01C(mainId);
		if(c122s01c == null){
			throw new CapMessageException("尚未填寫青年創業及啟動金貸款創業貸款申請表", getClass());
		}
		
		if(c122s01c.getN_cdate()!=null){
			Calendar fiveYears = Calendar.getInstance();
			fiveYears.add(Calendar.YEAR, -5);
			Calendar reqDate = Calendar.getInstance();
			reqDate.setTime(c122s01c.getN_cdate());
			if(fiveYears.after(reqDate)){//設立日期不得大於五年!
				alertMsg=alertMsg+ prop_cls1220m04.getProperty("Message.dataCheck.04") + "<br/>";
			}
		}
		if(c122s01c.getBday()!=null){
			Calendar Yearsold20 = Calendar.getInstance();
			Yearsold20.add(Calendar.YEAR, -18);  //J-111-0626 配合民法修正調整申辦年齡控管為18歲
			Calendar Yearsold45 = Calendar.getInstance();
			Yearsold45.add(Calendar.YEAR, -45);
			Calendar reqDate = Calendar.getInstance();
			reqDate.setTime(c122s01c.getBday());
			if(reqDate.after(Yearsold20) || reqDate.before(Yearsold45)){//Message.dataCheck.05 = 借款人年齡需介於20~45歲!
				//J-111-0626 配合民法修正調整申辦年齡控管為18歲  Message.dataCheck.05_N = 借款人年齡需介於20~45歲!
				alertMsg=alertMsg+ prop_cls1220m04.getProperty("Message.dataCheck.05_N") + "<br/>";
			}
		}
		if (Util.equals(UtilConstants.C122_ApplyKind.I, applyKind)) {//一百萬以下-申貸金額不得大於100萬
            if ((Util.isNotEmpty(c122s01c.getA3_1()) &&
            		c122s01c.getA3_1().compareTo(new BigDecimal(1000000)) == 1) || //準備金及開辦費用
            	(Util.isNotEmpty(c122s01c.getA3_4()) &&
	                c122s01c.getA3_4().compareTo(new BigDecimal(1000000)) == 1)	|| //週轉性支出
	            (Util.isNotEmpty(c122s01c.getA3_9()) &&
	                c122s01c.getA3_9().compareTo(new BigDecimal(1000000)) == 1) ) {//資本性支出
            	alertMsg=alertMsg+ prop_cls1220m04.getProperty("Message.dataCheck.10") + "<br/>";//申貸金額不得大於100萬
            }
        }
		if (Util.equals(UtilConstants.C122_ApplyKind.J, applyKind)) {//一百萬以上
            if (Util.isNotEmpty(c122s01c.getA3_1()) &&
            		c122s01c.getA3_1().compareTo(new BigDecimal(2000000)) == 1) {//準備金及開辦費用不得大於200萬
            	alertMsg=alertMsg+ prop_cls1220m04.getProperty("Message.dataCheck.06") + "<br/>";
            }
            if (Util.isNotEmpty(c122s01c.getA3_4()) &&
            		c122s01c.getA3_4().compareTo(new BigDecimal(4000000)) == 1) {//週轉性支出不得大於400萬
            	alertMsg=alertMsg+ prop_cls1220m04.getProperty("Message.dataCheck.07") + "<br/>";
            }
            if (Util.isNotEmpty(c122s01c.getA3_9()) &&
            		c122s01c.getA3_9().compareTo(new BigDecimal(12000000)) == 1) {//資本性支出不得大於1200萬
            	alertMsg=alertMsg+ prop_cls1220m04.getProperty("Message.dataCheck.08");
            }
        }
		if(!Util.equals(alertMsg, "")){
			result.set("alertMsg", alertMsg);
		}
		return result;		
	}
	
	@DomainAuth(AuthType.Modify)
	public IResult checkLaa(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String laaName = Util.trim(params.getString("laaName"));
		
		//暫不檢核是否輸入重複地政士
		
		return result;
	}

	@DomainAuth(AuthType.Modify)
	public IResult saveC122S01Y(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String sOid = Util.trim(params.getString("sOid"));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String laaName = Util.trim(params.getString("laaName"));
		
		C122M01A c122m01a = cls1220Service.getC122M01A_byMainId(mainId);
		String custId = c122m01a.getCustId();
		String dupNo = c122m01a.getDupNo();
		
		C122S01Y c122s01y = cls1220Service.findC122S01Y(sOid);
		if (c122s01y == null) {
			c122s01y = new C122S01Y();
			c122s01y.setMainId(mainId);
			c122s01y.setCustId(custId);
			c122s01y.setDupNo(dupNo);
		}
		c122s01y.setLaaName(laaName);
		cls1220Service.save(c122s01y);
		
		//避免新增地政士後未更新引介來源，先強制更新引介來源(因為地政士跟引介來源存的地方不一樣)
		if(Util.isNotEmpty(c122m01a.getC122m01f())
				&& Util.notEquals(c122m01a.getC122m01f().getIntroduceSrc(), "4")){
			C122M01F c122m01f=c122m01a.getC122m01f();
			c122m01f.setIntroduceSrc("4");
			cls1220Service.save(c122m01f);
		}
		//更新地政士名單時需要檢查並更新重複派案註記
		cls1220Service.updateIsSameMegaEmpLaaCaseFlag(MegaSSOSecurityContext.getUnitNo(), mainId);
		
		return result;
	}	

	@DomainAuth(AuthType.Modify)
	public IResult getC122S01Y(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String sOid = Util.trim(params.getString("sOid"));
		
		C122S01Y c122s01y = cls1220Service.findC122S01Y(sOid);
		result.putAll(new CapAjaxFormResult(c122s01y.toJSONObject(
				CapEntityUtil.getColumnName(c122s01y), null)));
		
		return result;
	}
	
	@DomainAuth(AuthType.Modify)
	public IResult deleteC122S01Y(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String sOid = Util.trim(params.getString("sOid"));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		C122S01Y c122s01y = cls1220Service.findC122S01Y(sOid);

		cls1220Service.delete(c122s01y);
		//更新地政士名單時需要檢查並更新重複派案註記(會針對房貸案去更新)
		cls1220Service.updateIsSameMegaEmpLaaCaseFlag(MegaSSOSecurityContext.getUnitNo(), mainId);
		
		return result;
	}
	
	@DomainAuth(AuthType.Modify)
	public IResult cleanC122S01Y(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		List<C122S01Y> c122s01yList = cls1220Service.findC122S01YbyMainId(mainId);
		
		for(C122S01Y c122s01y :c122s01yList){
			cls1220Service.delete(c122s01y);
		}
		
		return result;
	}
	
	/**
	 * 新增分行至'歡喜信貸自動派案維護-被指派分行清單檔'中
	 * 
	 * @param params
	 * @return void
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	@DomainAuth(AuthType.Modify)
	public void updateAssigneeBrchList(PageParameters params)
			throws CapException {
		ObjectMapper objectMapper = new ObjectMapper();
		String gridRowDataJSONArray = Util.trim(params.getString("gridRowDataJSONArray"));
		try {
			List<Map<String, Object>> gridRowDataList = objectMapper.readValue(gridRowDataJSONArray, List.class);
			service.updateAssigneeBrchList(gridRowDataList);// 分行重複檢核於前端已執行
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	/**
	 * 檢查人工進件資料是否已填妥
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify)
	public IResult checkC122M01AByManualData(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		List<String> errorItem = new ArrayList<String>();
		StringBuilder msg = new StringBuilder();
		
		String custId=Util.trim(params.getString("custId"));
		String dupNo=Util.trim(params.getString("dupNo"));
		String applyAmt=Util.trim(params.getString("applyAmt"));
		String year=Util.trim(params.getString("year"));
		String estFlag=Util.trim(params.getString("estFlag"));
		String ByType=Util.trim(params.getString("ByType"));
		String extYear=Util.trim(params.getString("extYear"));
		String nowExtend=Util.trim(params.getString("nowExtend"));
		
		if(Util.isEmpty(custId) || Util.isEmpty(dupNo)){
			errorItem.add(prop_cls1220v10.getProperty("C122M01A.custId"));//身分證統編
		}
		
		if(Util.isEmpty(applyAmt)){
			errorItem.add(prop_cls1220v10.getProperty("C122M01A.Input.Amt"));//申請金額
		}
		
		if(Util.isEmpty(year)){
			errorItem.add(prop_cls1220v10.getProperty("C122M01A.Input.Year"));//借款年限
		}
		
		if(Util.equals("Y", nowExtend) && Util.isEmpty(extYear)){
			errorItem.add(prop_cls1220v10.getProperty("C122M01A.Input.NowExtend"));//寬限期
		}
		
		if(Util.isEmpty(estFlag) && 
				(Util.equals(ByType, UtilConstants.C122_ApplyKind.E) ||
						Util.equals(ByType, UtilConstants.C122_ApplyKind.O)) ){
			errorItem.add(prop_cls1220v10.getProperty("C122M01A.estFlag"));//有無擔保品
		}
		
		for(String item : errorItem){
			if(Util.isNotEmpty(msg)){
				msg.append("、");
			}
			msg.append(item);
		}

		result.set("msg", msg.toString());

		return result;
	}
	
	/**
	 * 根據使用者取得分行清單
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public IResult queryBrList(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		UnitTypeEnum unitType = UnitTypeEnum.convertToUnitType(branchService.getBranch(user.getUnitNo()));
		List<IBranch> ibranchs = null;
		TreeMap<String, String> brMap = new TreeMap<String, String>();
		if (!unitType.isEquals(UnitTypeEnum.分行)) {
			//ibranchs = branchService.getAllBranch();//branchService.getBranchOfGroup(user.getUnitNo());
			ibranchs = branchService.getBranchByUnitType(BranchTypeEnum.一般分行.getCode(), BranchTypeEnum.簡易分行.getCode(),
						BranchTypeEnum.海外分行.getCode(), BranchTypeEnum.國金部.getCode());
			for (IBranch branch : ibranchs) {
				brMap.put(Util.trim(branch.getBrNo()),
						Util.trim(branch.getBrName()));
			}
		} else {
			brMap = retrialService.getBranch(user.getUnitNo());
		}

		CapAjaxFormResult bankList = new CapAjaxFormResult(brMap);
		result.set("userBrid", user.getUnitNo());
		result.set("brList", bankList);
		result.set("brListOrder", new ArrayList<String>(brMap.keySet()));
		return result;
	}
	
	/**
	 * 產生 引介案件進度查詢excel檔名
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult make_case_progress_excelfilename(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String applyTS_beg = Util.trim(params.getString("applyTS_beg"));
		String applyTS_end = Util.trim(params.getString("applyTS_end"));
		String brIdFilter = Util.trim(params.getString("brIdFilter"));

		Map<String, String> cacheMap = new HashMap<String, String>();
		String tableName = "引介案件進度";
		String title = brIdFilter + getBrName(cacheMap, brIdFilter)+ tableName + "(" + applyTS_beg + "~" + applyTS_end+").xls";
		
		result.set("Success", true);
		result.set("fileNmae", title);
		return result;
	}
	private String pretty_numStr(String r){
		int idx = r.lastIndexOf(".");
		if(idx>0){
			String bf = r.substring(0, idx);
			String point = r.substring(idx, idx+1);
			String af = r.substring(idx+1);
			for(int i=0;i<10;i++){
				if(af.endsWith("0")){
					af = af.substring(0, af.length()-1);
				}
			}
			return bf+(Util.isNotEmpty(af)?(point+af):"");
		}else{
			return r;
		}

	}
}
