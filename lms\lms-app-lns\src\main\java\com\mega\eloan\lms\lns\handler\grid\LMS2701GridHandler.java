/* 
 *  lms2701GridHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lns.handler.grid;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.formatter.CodeTypeFormatter;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.lns.service.LMS2701Service;
import com.mega.eloan.lms.model.L270M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.jcs.common.Util;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

/**
 * <pre>
 * 企金信保
 * 信保基金送保查詢
 * </pre>
 *
 * @since 2021/6/1
 * <AUTHOR>
 * @version <ul>
 *          <li>2021/6/1,009301,new
 *          </ul>
 */
@Scope("request")
@Controller("lms2701gridhandler")
public class LMS2701GridHandler extends AbstractGridHandler {

	@Resource
	LMS2701Service lms2701Service;

	@Resource
	CodeTypeService codeTypeService;

	@SuppressWarnings("unchecked")
	public CapGridResult queryView(ISearch pageSetting, PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		Boolean init = params.getAsBoolean("init", false);
		if(init){	// 第一次load畫面先不查
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId", "");
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dupNo", "");
		} else {
			String custId = Util.trim(params.getString("custId"));
			String dupNo = Util.trim(params.getString("dupNo"));
			if (Util.isNotEmpty(custId) && Util.isNotEmpty(dupNo)) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
			} else {
				throw new CapMessageException(UtilConstants.AJAX_RSP_MSG.查無資料, getClass());
			}
		}

		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", user.getUnitNo());

		Page<? extends GenericBean> page = lms2701Service.findPage(L270M01A.class, pageSetting);

		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();

		formatter.put("dataStatus", new CodeTypeFormatter(codeTypeService,
				"cls3501_dataStatus", CodeTypeFormatter.ShowTypeEnum.Desc));

		return new CapGridResult(page.getContent(), page.getTotalRow(), formatter);
	}
}
