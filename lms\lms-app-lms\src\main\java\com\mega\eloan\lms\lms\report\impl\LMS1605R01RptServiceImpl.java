package com.mega.eloan.lms.lms.report.impl;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.sql.Timestamp;
import java.text.DecimalFormat;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.tools.ant.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.inet.report.ReportException;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.eloan.lms.base.panels.LMSS20APanel;
import com.mega.eloan.lms.base.service.AMLRelateService;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dao.L160M01CDao;
import com.mega.eloan.lms.dao.L161S01BDao;
import com.mega.eloan.lms.dao.L162S01ADao;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.lms.pages.LMS1605M01Page;
import com.mega.eloan.lms.lms.service.LMS1205Service;
import com.mega.eloan.lms.lms.service.LMS1405Service;
import com.mega.eloan.lms.lms.service.LMS1605Service;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01C;
import com.mega.eloan.lms.model.L120S09A;
import com.mega.eloan.lms.model.L120S09B;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L160M01A;
import com.mega.eloan.lms.model.L160M01B;
import com.mega.eloan.lms.model.L160M01C;
import com.mega.eloan.lms.model.L160M01D;
import com.mega.eloan.lms.model.L163S01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.PdfTools;
import tw.com.jcs.common.report.ReportGenerator;

/**
 * 產生動審表PDF
 * 
 * <AUTHOR>
 * 
 */
@Service("lms1605r01rptservice")
public class LMS1605R01RptServiceImpl implements FileDownloadService {

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(LMS1605R01RptServiceImpl.class);
	@Resource
	EloandbBASEService eloanDbService;
	@Resource
	UserInfoService userInfoService;
	@Resource
	LMS1605Service service1605;

	@Resource
	BranchService branch;
	@Resource
	CodeTypeService codeTypeService;

	@Resource
	MisCustdataService misCustdataService;
	@Resource
	L162S01ADao l162s01aDao;

	@Resource
	L160M01CDao l160m01cDao;
	@Resource
	L161S01BDao l161s01bDao;

	@Resource
	LMS1205Service service1205;

	@Resource
	CodeTypeService codetypeservice;

	@Resource
	LMS1405Service lms1405Service;

	@Resource
	LMSService lmsService;
	@Resource
	AMLRelateService amlRelateService;

	private static ThreadLocal<DecimalFormat> dfMoney = new ThreadLocal<DecimalFormat>();
	private static ThreadLocal<DecimalFormat> dfRate = new ThreadLocal<DecimalFormat>();

	// @Override
	// public String getReportTemplateFileName() {
	// LOGGER.info("into getReportTemplateFileName");
	// // zh_TW: 正體中文
	// // zh_CN: 簡體中文
	// // en_US: 英文
	// Locale locale = (Session.get() != null) ? Session.get().getLocale()
	// : LocaleContextHolder.getLocale();
	// if (locale == null)
	// locale = Locale.getDefault();
	// // 測試用
	// // return
	// //
	// "C:/work/src.mega/WebELoan/lms/lms-config/src/main/resources/report/lms/LMS1605R01_zh_CN.rpt";
	// return "report/lms/LMS1605R01_" + locale.toString() + ".rpt";
	// }

	/**
	 * 建立PDF
	 * 
	 * @param params
	 *            params
	 * @return OutputStream OutputStream
	 * @throws Exception
	 */
	public OutputStream generateReport(PageParameters params) throws Exception {
		String mainId = params.getString("mainId");
		String type = params.getString("type", "R01");
		Map<InputStream, Integer> pdfNameMap = new LinkedHashMap<InputStream, Integer>();
		// J-106-0029-002 洗錢防制-新增洗錢防制頁籤
		// 洗錢防制檢核表
		Map<InputStream, Integer> pdfNameMap5 = new LinkedHashMap<InputStream, Integer>();

		List<InputStream> list = new LinkedList<InputStream>();
		OutputStream outputStream = null;
		Locale locale = null;
		Properties propEloanPage = null;
		int subLine = 1;
		Properties rptProperties = null;

		try {

			locale = LMSUtil.getLocale();
			rptProperties = MessageBundleScriptCreator
					.getComponentResource(LMS1605R01RptServiceImpl.class);
			propEloanPage = MessageBundleScriptCreator
					.getComponentResource(AbstractEloanPage.class);
			if ("R01".equals(type)) {
				outputStream = this
						.genLMS1605R01(mainId, locale, rptProperties);
			}

			pdfNameMap.put(new ByteArrayInputStream(
					((ByteArrayOutputStream) outputStream).toByteArray()),
					subLine);

			if ("R01".equals(type)) {
				List<L120S09A> listL120s09a = amlRelateService
						.findL120s09aByMainIdWithOrder(mainId);
				if (listL120s09a != null && !listL120s09a.isEmpty()) {
					// 加印洗錢防制檢核表
					outputStream = this.genLMS1205R33(mainId, locale,
							"report/lms/LMS1205R33_" + locale.toString()
									+ ".rpt", rptProperties);
					// 列印頁次位置
					subLine = 8;
					pdfNameMap5.put(
							new ByteArrayInputStream(
									((ByteArrayOutputStream) outputStream)
											.toByteArray()), subLine);
				}

			}

			if (pdfNameMap != null && pdfNameMap.size() > 0) {
				outputStream = new ByteArrayOutputStream();
				PdfTools.mergeReWritePagePdf(pdfNameMap, outputStream,
						propEloanPage.getProperty("PaginationText"), true,
						locale, subLine);
				list.add(new ByteArrayInputStream(
						((ByteArrayOutputStream) outputStream).toByteArray()));
			}
			if (pdfNameMap5 != null && pdfNameMap5.size() > 0) {
				outputStream = new ByteArrayOutputStream();
				PdfTools.mergeReWritePagePdf(pdfNameMap5, outputStream,
						propEloanPage.getProperty("PaginationText"), true,
						locale, subLine);
				list.add(new ByteArrayInputStream(
						((ByteArrayOutputStream) outputStream).toByteArray()));
			}

			outputStream = new ByteArrayOutputStream();
			PdfTools.mergeReWritePagePdf(list, outputStream);
		} finally {
			if (list != null) {
				list.clear();
			}
			if (pdfNameMap != null) {
				pdfNameMap.clear();
			}
			// J-106-0029-002 洗錢防制-新增洗錢防制頁籤
			if (pdfNameMap5 != null) {
				pdfNameMap5.clear();
			}
		}
		return outputStream;
	}

	/**
	 * 塞入變數MAP資料使用(L160M01A)
	 * 
	 * @param rptVariableMap
	 *            存放變數MAP
	 * @param l120m01a
	 *            L120M01A資料
	 * @return Map<String,String> rptVariableMap
	 */
	private Map<String, String> setL160M01ACustNameData(
			Map<String, String> rptVariableMap, List<Map<String, Object>> list) {
		StringBuffer str = new StringBuffer();
		HashMap<String, String> temp = new HashMap<String, String>();
		String custName = "";
		for (Map<String, Object> map : list) {
			custName = Util.trim(map.get("CUSTNAME"));
			if (temp.containsKey(custName)) {
				continue;
			} else {
				temp.put(custName, "");
			}
			str.append(str.length() > 0 ? "、" : "");
			str.append(custName);

		}
		rptVariableMap.put("L160M01A.CUSTNAME", str.toString());
		return rptVariableMap;
	}

	/**
	 * 塞入變數MAP資料使用(L160M01A)
	 * 
	 * @param rptVariableMap
	 *            存放變數MAP
	 * @param l120m01a
	 *            L120M01A資料
	 * @return Map<String,String> rptVariableMap
	 */
	private Map<String, String> setL160M01AData(
			Map<String, String> rptVariableMap, L160M01A l160m01a,
			Properties prop, Properties prop1605) {
		rptVariableMap.put("L160M01A.USETYPE",
				Util.nullToSpace(l160m01a.getUseType()));
		rptVariableMap.put("L160M01A.RANDOMCODE",
				Util.nullToSpace(l160m01a.getRandomCode()));
		rptVariableMap.put("L160M01A.CASENO",
				Util.nullToSpace(l160m01a.getCaseNo()));
		// rptVariableMap.put("L160M01A.CUSTNAME",
		// Util.nullToSpace(l160m01a.getCustName()));

		rptVariableMap.put(
				"L160M01A.TTYPE",
				""
						+ prop1605.getProperty("L160M01A.tType"
								+ l160m01a.getTType()) + "");

		rptVariableMap.put(
				"L160M01A.GUDATE",
				l160m01a.getGuFromDate() != null
						|| l160m01a.getGuEndDate() != null ? this
						.getDate(l160m01a.getGuFromDate())
						+ "~"
						+ this.getDate(l160m01a.getGuEndDate()) : "");// prop.getProperty("L160M01A.GUFORMDATEDEAUFUL")

		rptVariableMap.put("L160M01A.GUFORMDATE",
				this.getDate(l160m01a.getGuFromDate()));
		rptVariableMap.put("L160M01A.GUENDDATE",
				this.getDate(l160m01a.getGuEndDate()));
		rptVariableMap.put("L160M01A.GUCURR",
				Util.nullToSpace(l160m01a.getGuCurr()) + " ");
		rptVariableMap.put("L160M01A.GUAMT",
				NumConverter.addComma(l160m01a.getGuAmt()));
		rptVariableMap.put(
				"L160M01A.SIGNDATE",
				l160m01a.getSignDate() != null ? this.getDate(l160m01a
						.getSignDate()) : ""); // prop.getProperty("L160M01A.SIGNDATEDEAUFUL")

		String useSelect = l160m01a.getUseSelect();
		String useString = "";
		if (!Util.isEmpty(useSelect)) {
			// 動用期限判斷
			switch (Integer.valueOf(useSelect)) {
			case 1:
				useString = this.getDate(l160m01a.getUseFromDate()) + " ~ "
						+ this.getDate(l160m01a.getUseEndDate());
				break;
			case 2:

				// L160M01A.useMonth1=自首動日起
				// L160M01A.useMonth2=個月
				useString = prop1605.getProperty("L160M01A.useMonth1") + " "
						+ Util.trim(l160m01a.getUseMonth()) + " "
						+ prop1605.getProperty("L160M01A.useMonth2");
				break;
			case 3:
				useString = l160m01a.getUseOther();
				break;
			}
		}
		rptVariableMap.put("L160M01A.USEFORMDATE", useString);

		String lnString = "";
		String lnSelect = l160m01a.getLnSelect();
		// 授信期限判斷，當取得授信契約書期別 為中長期
		if (!Util.isEmpty(lnSelect) && "2".equals(l160m01a.getTType())) {
			switch (Integer.valueOf(lnSelect)) {
			case 1:
				lnString = this.getDate(l160m01a.getLnFromDate()) + " ~ "
						+ this.getDate(l160m01a.getLnEndDate());
				break;
			case 2:
				// L160M01A.useMonth1=自首動日起
				// L160M01A.lnYear=年
				// L160M01A.useMonth2=個月
				lnString = prop1605.getProperty("L160M01A.useMonth1") + " "
						+ Util.trim(l160m01a.getLnYear()) + " "
						+ prop1605.getProperty("L160M01A.lnYear") + " "
						+ Util.trim(l160m01a.getLnMonth()) + " "
						+ prop1605.getProperty("L160M01A.useMonth2");
				break;
			case 3:
				lnString = l160m01a.getLnOther();
				break;
			}
		}
		rptVariableMap.put("L160M01A.LNDATE", lnString);
		rptVariableMap.put("L160M01A.LNFORMDATE",
				this.getDate(l160m01a.getLnFromDate()));
		rptVariableMap.put("L160M01A.LNENDDATE",
				this.getDate(l160m01a.getLnEndDate()));

		// J-106-0238-001
		// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
		rptVariableMap.put("L160M01A.BLACKLISTTXTOK", "");
		rptVariableMap.put("L160M01A.BLACKDATADATE", "");
		if (Util.notEquals(Util.trim(l160m01a.getBlackListTxtOK()), "")) {
			rptVariableMap.put("L160M01A.BLACKLISTTXTOK",
					Util.nullToSpace(l160m01a.getBlackListTxtOK()));
			rptVariableMap.put("L160M01A.BLACKDATADATE",
					this.getDate(l160m01a.getBlackDataDate()));
		} else {

			List<L120S09A> listL120s09a = amlRelateService
					.findL120s09aByMainId(l160m01a.getMainId());

			// P-108-0046_05097_B1003 Web e-Loan授信系統傳送婉卻紀錄給ORACLE CIF
			// ORACLE
			boolean notPrintDetailForOracle = false;
			L120S09B l120s09b = amlRelateService.findL120s09bByMainId(l160m01a
					.getMainId());
			if (amlRelateService.isOracle(l160m01a.getOwnBrId())) {
				if (l120s09b != null) {
					if (Util.notEquals(Util.trim(l120s09b.getNcResult()), "")) {
						notPrintDetailForOracle = true;
					}
				}
			}

			// P-108-0046_05097_B1003 Web e-Loan授信系統傳送婉卻紀錄給ORACLE CIF
			StringBuilder temp = new StringBuilder("");
			String blackDataDate = "";
			if (notPrintDetailForOracle) {
				Locale locale = LMSUtil.getLocale();
				Map<String, String> sasNcResultMap = codetypeservice
						.findByCodeType("SAS_NC_Result", locale.toString());
				String queryDateS = l120s09b.getQueryDateS() == null ? ""
						: CapDate.formatDate(l120s09b.getQueryDateS(),
								"yyyy-MM-dd");
				String ncResult = Util.equals(
						Util.trim(l120s09b.getNcResult()), "") ? ""
						: sasNcResultMap.get(Util.trim(l120s09b.getNcResult()));

				blackDataDate = queryDateS;
				temp.append(ncResult);

			} else {
				// 有AML/CFT檢核表
				if (listL120s09a != null && !listL120s09a.isEmpty()) {
					Set<L160M01B> l160m01bs = l160m01a.getL160m01b();
					Map<String, String> queryCustMap = new HashMap<String, String>();
					ArrayList<String> mainIds = new ArrayList<String>();
					for (L160M01B l160m01b : l160m01bs) {
						mainIds.add(l160m01b.getReMainId());
					}

					List<L140M01A> l140m01as = lms1405Service
							.findL140m01aListByMainIdList(mainIds
									.toArray(new String[mainIds.size()]));
					for (L140M01A l140m01a : l140m01as) {
						String custId = Util.trim(l140m01a.getCustId());
						String dupNo = Util.trim(l140m01a.getDupNo());
						String custName = Util.trim(l140m01a.getCustName());
						String key = custId + "-" + dupNo;
						queryCustMap.put(key, custName);
					}

					for (String key : queryCustMap.keySet()) {
						String[] custKey = key.split("-");
						String tCustId = custKey[0];
						String tDupNo = custKey[1];
						// 新案
						listL120s09a = amlRelateService
								.findListL120s09aByCustId(l160m01a.getMainId(),
										tCustId, tDupNo);

						if (listL120s09a != null && !listL120s09a.isEmpty()) {
							for (L120S09A l120s09a : listL120s09a) {

								// Properties prop1605 =
								// MessageBundleScriptCreator
								// .getComponentResource(LMS1605M01Page.class);

								blackDataDate = Util.trim(TWNDate.toAD(l120s09a
										.getQueryDateS()));

								String custId = l120s09a.getCustId()
										.toUpperCase();
								String dupNo = l120s09a.getDupNo();
								String eName = l120s09a.getCustEName();
								String cName = Util
										.trim(l120s09a.getCustName());

								if (Util.notEquals(l120s09a.getBlackListCode(),
										"")) {
									String msg = "";
									if (Util.equals(
											l120s09a.getBlackListCode(),
											UtilConstants.Casedoc.L120s09aBlackListCode.未列於黑名單)) {
										// L160M01A.message39=未列於黑名單
										msg = prop1605
												.getProperty("L160M01A.message39");

									} else if (Util
											.equals(l120s09a.getBlackListCode(),
													UtilConstants.Casedoc.L120s09aBlackListCode.是黑名單)) {
										// L160M01A.message107=是黑名單
										msg = prop1605
												.getProperty("L160M01A.message107");

									} else if (Util
											.equals(l120s09a.getBlackListCode(),
													UtilConstants.Casedoc.L120s09aBlackListCode.可能是黑名單)) {
										// L160M01A.message41=可能是黑名單
										msg = prop1605
												.getProperty("L160M01A.message41");

									}

									temp.append(temp.length() > 0 ? "\r" : "");
									temp.append(custId).append(" ")
											.append(dupNo).append(" ")
											.append(cName);
									if (Util.isNotEmpty(eName)) {
										temp.append("【").append(eName)
												.append("】 ");
									}

									temp.append(" ").append(msg);
								}

								break;
							}
						}

					}
				}

			}
			if (Util.notEquals(Util.trim(temp.toString()), "")) {
				// 資料查詢日期：
				rptVariableMap.put("L160M01A.BLACKDATADATE", blackDataDate);
				rptVariableMap.put("L160M01A.BLACKLISTTXTOK",
						Util.nullToSpace(temp.toString()));
			}

		}

		rptVariableMap.put("L160M01A.COMM",
				Util.nullToSpace(l160m01a.getComm()));
		rptVariableMap.put("L160M01A.SIGN",
				Util.nullToSpace(l160m01a.getSign()));

		if (!Util.isEmpty(l160m01a.getL160M01D())
				&& !l160m01a.getL160M01D().isEmpty()) {
			String appid = "";
			String recheckid = "";
			String managerid = "";
			String mainAppid = "";
			String mainRecheckid = "";
			// 取得人員職稱 L1. 分行經辦 L3. 分行授信主管 L4. 分行覆核主管 L5. 經副襄理
			StringBuilder bossId = new StringBuilder("");
			for (L160M01D l160m01d : l160m01a.getL160M01D()) {
				// 要加上人員代碼
				String type = Util.trim(l160m01d.getStaffJob());
				String userId = Util.trim(l160m01d.getStaffNo());
				String value = Util.trim(this.getUserName(userId));
				if ("L1".equals(type)) {
					appid = value;
				} else if ("L3".equals(type)) {
					bossId.append(bossId.length() > 0 ? "<br/>" : "");
					bossId.append(value);
				} else if ("L4".equals(type)) {
					recheckid = value;

				} else if ("L5".equals(type)) {
					managerid = value;

				} else if ("L6".equals(type)) {
					mainAppid = value;

				} else if ("L7".equals(type)) {
					mainRecheckid = value;

				}
			}
			if (Util.isEmpty(recheckid)) {
				recheckid = this.getUserName(l160m01a.getReCheckId());
			}
			rptVariableMap.put("L160M01A.APPRID", appid);
			rptVariableMap.put("L160M01A.BOSSID", bossId.toString());
			rptVariableMap.put("L160M01A.RECHECKID", recheckid);
			rptVariableMap.put("L160M01A.MANAGERID", managerid);
			// 總行經辦
			rptVariableMap.put("L160M01A.MAINAPPID", mainAppid);
			// 總行覆核
			rptVariableMap.put("L160M01A.MAINRECHECKID", mainRecheckid);
		} else {
			rptVariableMap.put("L160M01A.APPRID",
					this.getUserName(l160m01a.getApprId()));
			rptVariableMap.put("L160M01A.BOSSID", "");
			rptVariableMap.put("L160M01A.RECHECKID", "");
			rptVariableMap.put("L160M01A.MANAGERID", "");
			// 總行經辦
			rptVariableMap.put("L160M01A.MAINAPPID", "");
			// 總行覆核
			rptVariableMap.put("L160M01A.MAINRECHECKID", "");

		}
		rptVariableMap.put("L160M01A.CASEDATE",
				this.getDate(l160m01a.getCaseDate()));
		// 共同行銷
		rptVariableMap.put("L160M01A.JOINMARKETINGDATE",
				this.getDate(l160m01a.getJoinMarketingDate()));
		rptVariableMap.put("L160M01A.JOINMARKETING",
				Util.trim(l160m01a.getJoinMarketing()));

		// J-105-0079-001 Web e-Loan授信管理系統修改柬埔寨地區分行動審表。
		rptVariableMap
				.put("L160M01A.noEdit1", Util.trim(l160m01a.getNoEdit1()));
		rptVariableMap
				.put("L160M01A.noEdit2", Util.trim(l160m01a.getNoEdit2()));
		rptVariableMap
				.put("L160M01A.noEdit3", Util.trim(l160m01a.getNoEdit3()));
		rptVariableMap
				.put("L160M01A.noEdit4", Util.trim(l160m01a.getNoEdit4()));

		// 重新編排標題項目號碼
		int titleCount = 8;
		int[] title = new int[titleCount];

		for (int i = 0; i < titleCount; i++) {
			title[i] = i + 1;
		}

		if (Util.equals(Util.trim(l160m01a.getNoEdit1()), "1")) {
			for (int i = 2; i <= titleCount; i++) {
				title[i - 1] = title[i - 1] - 1;
			}
		}

		if (Util.equals(Util.trim(l160m01a.getNoEdit2()), "1")) {
			for (int i = 3; i <= titleCount; i++) {
				title[i - 1] = title[i - 1] - 1;
			}
		}

		Locale locale = LocaleContextHolder.getLocale();
		if (locale == null)
			locale = Locale.getDefault();

		for (int i = 1; i <= titleCount; i++) {
			rptVariableMap.put(
					"title" + i,
					Util.equals(locale, "en") ? Util.trim(title[i - 1])
							: NumConverter.toChineseNumber(Util
									.trim(title[i - 1])));
		}

		return rptVariableMap;

	}

	/**
	 * 塞入變數MAP資料使用(L160M01A)
	 * 
	 * @param rptVariableMap
	 *            存放變數MAP
	 * @param l120m01a
	 *            L120M01A資料
	 * @return Map<String,String> rptVariableMap
	 */
	private Map<String, String> setL163S01AData(
			Map<String, String> rptVariableMap, L163S01A l163s01a) {
		rptVariableMap.put("L163S01A.WAITINGITEM",
				Util.nullToSpace(l163s01a.getWaitingItem()));
		rptVariableMap.put("L163S01A.TWILLFINISHDATE",
				this.getDate(l163s01a.getWillFinishDate()));
		rptVariableMap.put("L163S01A.FINISHDATE",
				this.getDate(l163s01a.getFinishDate()));
		rptVariableMap.put("L163S01A.ITEMTRACE",
				Util.nullToSpace(l163s01a.getItemTrace()));
		// managerid為0時表示自行輸入
		String managerid = "";
		if ("0".equals(l163s01a.getManagerId())) {
			managerid = l163s01a.getManagerNm();
		} else {
			managerid = this.getUserName(Util.nullToSpace(l163s01a
					.getManagerId()));
		}
		rptVariableMap.put("L163S01A.MANAGERID", managerid);
		rptVariableMap.put("L163S01A.BOSSID",
				this.getUserName(Util.nullToSpace(l163s01a.getBossId())));
		rptVariableMap.put("L163S01A.APPRAISERID",
				this.getUserName(Util.nullToSpace(l163s01a.getAppraiserId())));
		return rptVariableMap;
	}

	/**
	 * 取得日期(XXXX-XX-XX)
	 * 
	 * @param date
	 *            日期
	 * @return 日期
	 */
	private String getDate(Date date) {
		String str = null;
		if (date == null) {
			str = "";
		} else {
			str = TWNDate.toAD(date);
		}
		return str;
	}

	/**
	 * 設定L160M01B資料
	 * 
	 * @param rptVariableMap
	 *            javabeanMap
	 * @param list
	 *            L160M01B List
	 * @return rptVariableMap javabeanMap
	 */
	private Map<String, String> setL160M01BData(
			Map<String, String> rptVariableMap, List<L160M01B> list) {
		int count = 1;
		StringBuffer str = new StringBuffer();
		for (L160M01B l160m01b : list) {
			str.append(Util.nullToSpace(l160m01b.getCntrNo()).replace("　", " ")
					.trim());
			if (count != list.size()) {
				str.append("、");
			}
			count++;
		}
		rptVariableMap.put("L160M01B.CNTRNO", str.toString());
		return rptVariableMap;
	}

	/**
	 * 設定L160M01C資料
	 * 
	 * @param titleRows
	 *            多值MAP
	 * @param list
	 *            L160M01C List
	 * @return titleRows 多值MAP
	 */
	private List<Map<String, String>> setL160M01CDataList(
			List<Map<String, String>> titleRows, List<L160M01C> list,
			Properties prop) {
		// F代表第一次重覆 前面資料都要先印出來 之後才印重複資料(Y) 重複資料印完後才印後面的資料(N)
		Map<String, String> mapInTitleRows = null;
		mapInTitleRows = Util.setColumnMap();
		mapInTitleRows.put("ReportBean.column07", "F");
		titleRows.add(mapInTitleRows);
		int count = 1;
		boolean result = true;
		for (L160M01C l160m01c : list) {
			if (!Util.isEmpty(Util.trim(l160m01c.getItemContent()))) {

				// J-111-0028_05097_B1001 Web
				// e-Loan海外企金授信動用審核表其它事項增加「土建融案件維護表」檢核項目
				String itemField1Fg1 = Util.trim(service1605
						.isL160m01cHasInputItemField1(l160m01c));
				String itemContent = l160m01c.getItemContent();
				if (Util.notEquals(itemField1Fg1, "")) {
					itemContent = StringUtils.replace(
							l160m01c.getItemContent(), itemField1Fg1,
							l160m01c.getItemField1());
				}

				if (count % 3 == 1) {
					result = false;
					mapInTitleRows = Util.setColumnMap();
					mapInTitleRows.put("ReportBean.column07", "Y");
					mapInTitleRows.put("ReportBean.column01",
							this.getItemCheck(l160m01c.getItemCheck(), prop));
					// J-111-0028_05097_B1001 Web
					// e-Loan海外企金授信動用審核表其它事項增加「土建融案件維護表」檢核項目
					mapInTitleRows.put("ReportBean.column02", count + "."
							+ itemContent);
				} else if (count % 3 == 2) {
					result = false;
					mapInTitleRows.put("ReportBean.column03",
							this.getItemCheck(l160m01c.getItemCheck(), prop));
					// J-111-0028_05097_B1001 Web
					// e-Loan海外企金授信動用審核表其它事項增加「土建融案件維護表」檢核項目
					mapInTitleRows.put("ReportBean.column04", count + "."
							+ itemContent);
				} else if (count % 3 == 0) {
					result = true;
					mapInTitleRows.put("ReportBean.column05",
							this.getItemCheck(l160m01c.getItemCheck(), prop));
					// J-111-0028_05097_B1001 Web
					// e-Loan海外企金授信動用審核表其它事項增加「土建融案件維護表」檢核項目
					mapInTitleRows.put("ReportBean.column06", count + "."
							+ itemContent);
					titleRows.add(mapInTitleRows);
				}
				count++;
			}
		}
		if (!result) {
			titleRows.add(mapInTitleRows);
		}
		if (titleRows.size() == 1) {
			mapInTitleRows = Util.setColumnMap();
			mapInTitleRows.put("ReportBean.column07", "Y");
			titleRows.add(mapInTitleRows);
		}
		mapInTitleRows = Util.setColumnMap();
		mapInTitleRows.put("ReportBean.column07", "N");
		titleRows.add(mapInTitleRows);
		return titleRows;
	}

	/**
	 * 顯示需要呈現ITEMCHECK的資料
	 * 
	 * @param itemCheck
	 *            itemCheck
	 * @return 呈現到報表文字
	 */
	private String getItemCheck(String itemCheck, Properties prop) {
		String str = null;
		if ("0".equals(itemCheck)) {
			str = prop.getProperty("L160M01C.ITEMCHECK0");
		} else if ("1".equals(itemCheck)) {
			str = "V";
		} else if ("2".equals(itemCheck)) {
			str = "X";
		} else {
			str = "";
		}
		return str;
	}

	/**
	 * 取得使用者姓名
	 * 
	 * @param userId
	 *            員編
	 * @return 姓名
	 */
	private String getUserName(String userId) {
		if (Util.isEmpty(userId)) {
			return "";
		}
		String result = userInfoService.getUserName(userId);
		if (Util.isEmpty(result)) {
			return userId;
		} else {
			return result;
		}
	}

	/**
	 * 產生LMS1601R01的PDF
	 * 
	 * @param mainId
	 *            mainId
	 * @param lang
	 *            語系
	 * @return outputstream outputstream
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public OutputStream genLMS1605R01(String mainId, Locale locale,
			Properties rptProperties) throws Exception {
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();

		ReportGenerator generator = new ReportGenerator(
				"report/lms/LMS1605R01_" + locale.toString() + ".rpt");
		OutputStream outputStream = null;
		// reportTools.setTestMethod(true);
		LOGGER.info("into setReportData");
		Properties prop = null;
		prop = MessageBundleScriptCreator
				.getComponentResource(LMS1205R01RptServiceImpl.class);
		Properties prop1605 = MessageBundleScriptCreator
				.getComponentResource(LMS1605M01Page.class);

		// String mainOid = params.getString(EloanConstants.MAIN_OID);
		// String mainId = params.getString(EloanConstants.MAIN_ID);
		// L160M01A．動用審核表主檔
		L160M01A l160m01a = null;
		// L160M01B．動審表額度序號資料
		List<L160M01B> l160m01bList = null;
		// L160M01C．動審表查核項目資料
		List<L160M01C> l160m01cList = null;

		L163S01A l163s01a = null;
		String branchName = null;
		// zh_TW: 正體中文
		// zh_CN: 簡體中文
		// en_US: 英文
		List<Map<String, Object>> list = null;
		try {
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

			locale = LocaleContextHolder.getLocale();
			if (locale == null)
				locale = Locale.getDefault();
			l160m01a = service1605.findL160M01AByMaindId(mainId);
			l163s01a = service1605.findL163m01aByMainId(mainId);
			if (l163s01a == null)
				l163s01a = new L163S01A();
			if (l160m01a == null)
				l160m01a = new L160M01A();

			// branchName = Util.nullToSpace(branch.getBranchName(Util
			// .nullToSpace(l160m01a.getCaseBrId())));

			branchName = Util.nullToSpace(branch.getBranchName(Util
					.nullToSpace(l160m01a.getOwnBrId())));

			l160m01bList = (List<L160M01B>) service1605.findListByMainId(
					L160M01B.class, l160m01a.getMainId());
			l160m01cList = (List<L160M01C>) service1605.findListByMainId(
					L160M01C.class, l160m01a.getMainId());
			list = eloanDbService.findL140M01AByMainId(l160m01a.getMainId());

			String logoPath = lmsService.getLogoShowPath(
					UtilConstants.RPTPicType.兆豐LOGO, "00",
					l160m01a.getOwnBrId());
			rptVariableMap.put("LOGOSHOW", logoPath);

			// 分行名稱
			rptVariableMap.put("BRANCHNAME", branchName);
			rptVariableMap = this.setL160M01AData(rptVariableMap, l160m01a,
					prop, prop1605);

			if (UtilConstants.DEFAULT.是.equals(l160m01a.getAllCanPay())) {
				// L160M01A.allUse=全部動用
				rptVariableMap.put("L160M01B.CNTRNO",
						prop.getProperty("L160M01A.allUse"));
			} else {
				rptVariableMap = this.setL160M01BData(rptVariableMap,
						l160m01bList);
			}
			rptVariableMap = this.setL163S01AData(rptVariableMap, l163s01a);
			rptVariableMap = this.setL160M01ACustNameData(rptVariableMap, list);
			titleRows = this.setL160M01CDataList(titleRows, l160m01cList, prop);
			generator.setLang(locale);
			generator.setVariableData(rptVariableMap);
			generator.setRowsData(titleRows);
			outputStream = generator.generateReport();

		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
		}
		return outputStream;

	}

	/**
	 * 塞入變數MAP資料使用(L160M01A)
	 * 
	 * @param rptVariableMap
	 *            存放變數MAP
	 * @param l120m01a
	 *            L120M01A資料
	 * @return Map<String,String> rptVariableMap
	 */
	private Map<String, String> setL160M01ACustNameData(
			Map<String, String> rptVariableMap, List<Map<String, Object>> list,
			L160M01A l160m01a) {
		StringBuffer str = new StringBuffer();
		HashMap<String, String> temp = new HashMap<String, String>();
		String custName = "";
		String mainCustName = "";
		for (Map<String, Object> map : list) {
			custName = Util.trim(map.get("CUSTNAME"));
			if (temp.containsKey(custName)) {
				continue;
			} else {
				temp.put(custName, "");
			}
			if (custName.equals(Util.trim(l160m01a.getCustName()))) {
				mainCustName = custName;
			} else {
				str.append(str.length() > 0 ? "、" : "");
				str.append(custName);
			}
		}

		if (Util.equals(mainCustName, "")) {
			// 有可能比對不到，因為裡面有無法辨識的亂碼字元
			rptVariableMap.put(
					"L160M01A.CUSTNAME",
					mainCustName + (str.length() > 0 ? "" : "")
							+ str.toString());
		} else {
			rptVariableMap.put(
					"L160M01A.CUSTNAME",
					mainCustName + (str.length() > 0 ? "、" : "")
							+ str.toString());
		}

		return rptVariableMap;
	}

	@Override
	public byte[] getContent(PageParameters params) throws CapException,
			FileNotFoundException, ReportException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
			baos = (ByteArrayOutputStream) this.generateReport(params);
			return baos.toByteArray();
		} finally {
			if (baos != null) {
				baos.close();
			}

		}
	}

	/**
	 * 產生LMS1205R33的PDF 洗錢防制檢核表 J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	 * 
	 * @param mainId
	 *            mainId
	 * @param lang
	 *            語系
	 * @param path
	 *            rpt路徑
	 * @return outputstream outputstream
	 * @throws Exception
	 * @throws IOException
	 * @throws FileNotFoundException
	 * @throws ReportException
	 *             reportException
	 */
	public OutputStream genLMS1205R33(String mainId, Locale locale,
			String path, Properties prop) throws FileNotFoundException,
			ReportException, IOException, Exception {

		Properties prop1601 = MessageBundleScriptCreator
				.getComponentResource(LMS1605M01Page.class);

		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();
		ReportGenerator generator = new ReportGenerator(path);
		OutputStream outputStream = null;

		String branchName = null;

		L120M01A l120m01a = null;
		L160M01A l160m01a = null;

		// L120S09A．洗錢防制明細檔
		List<L120S09A> listL120s09a = null;

		Map<String, String> caseLvlMap = null;
		Map<String, String> typCdMap = null;
		Map<String, String> blackListCodeMap = null;
		Map<String, String> custRelationMap = null;

		// J-106-0238-001
		// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
		Map<String, String> sasNcResultMap = null;

		try {

			l160m01a = service1605.findL160M01AByMaindId(mainId);

			l120m01a = this.findL120m01aByL160m01a(l160m01a);
			if (l120m01a == null)
				l120m01a = new L120M01A();

			listL120s09a = amlRelateService
					.findL120s09aByMainIdWithOrder(mainId);

			branchName = Util.nullToSpace(branch.getBranchName(Util
					.nullToSpace(l160m01a.getOwnBrId())));

			caseLvlMap = codetypeservice.findByCodeType("lms1205m01_caseLvl",
					locale.toString());
			typCdMap = codetypeservice.findByCodeType("TypCd",
					locale.toString());
			blackListCodeMap = codetypeservice.findByCodeType("BlackListCode",
					locale.toString());
			custRelationMap = codetypeservice.findByCodeType(
					"BlackListRelation", locale.toString());

			// J-106-0238-001
			// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
			sasNcResultMap = codetypeservice.findByCodeType("SAS_NC_Result",
					locale.toString());

			if (caseLvlMap == null)
				caseLvlMap = new LinkedHashMap<String, String>();
			if (typCdMap == null)
				typCdMap = new LinkedHashMap<String, String>();
			if (blackListCodeMap == null)
				blackListCodeMap = new LinkedHashMap<String, String>();
			if (custRelationMap == null)
				custRelationMap = new LinkedHashMap<String, String>();
			// J-106-0238-001
			// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
			if (sasNcResultMap == null)
				sasNcResultMap = new LinkedHashMap<String, String>();

			// 分行名稱
			rptVariableMap.put("BRANCHNAME", branchName);

			// L160M01B．動審表額度序號資料
			List<L160M01B> l160m01bList = (List<L160M01B>) service1605
					.findListByMainId(L160M01B.class, l160m01a.getMainId());
			if (UtilConstants.DEFAULT.是.equals(l160m01a.getAllCanPay())) {
				// L160M01A.message3=簽報書項下額度明細表全部動用
				rptVariableMap.put("L160M01B.CNTRNO",
						prop1601.getProperty("L160M01A.message3"));
			} else {
				rptVariableMap = this.setL160M01BData(rptVariableMap,
						l160m01bList);
			}

			// L160M01A.CUSTNAME
			List<Map<String, Object>> custNameList = null;
			custNameList = eloanDbService.findL140M01AByMainId(l160m01a
					.getMainId());
			rptVariableMap = this.setL160M01ACustNameData(rptVariableMap,
					custNameList, l160m01a);

			// 黑名單查詢日期
			L120S09A l120s09a = amlRelateService
					.findL120s09aMaxQDateByMainId(mainId);
			Date blackListQDate = l120s09a.getQueryDateS();
			rptVariableMap.put("BLACKLISTQDATE", CapDate.formatDate(
					blackListQDate, UtilConstants.DateFormat.YYYY_MM_DD));

			// J-106-0238-001
			// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
			rptVariableMap.put("L120S09B.NCRESULT", "");
			rptVariableMap.put("L120S09B.REFNO", "");
			rptVariableMap.put("L120S09B.UNIQUEKEY", "");
			rptVariableMap.put("L120S09B.NCCASEID", "");
			//UPGRADE
			rptVariableMap.put("L120S09B.ISNCRESULTREMARKSHOW", "");
			rptVariableMap.put("L120S09B.ISHIGHRISKERMARKSHOW", "");
			rptVariableMap.put("L120S09B.NCRESULTREMARK", "");
			rptVariableMap.put("L120S09B.HIGHRISKERMARK", "");
			
			L120S09B l120s09b = amlRelateService.findL120s09bByMainId(mainId);

			// P-108-0046_05097_B1003 Web e-Loan授信系統傳送婉卻紀錄給ORACLE CIF
			String ncResultStr = "";

			if (l120s09b != null) {
				ncResultStr = Util
						.equals(Util.trim(l120s09b.getNcResult()), "") ? ""
						: sasNcResultMap.get(Util.trim(l120s09b.getNcResult()));

				rptVariableMap.put("L120S09B.NCRESULT",
						sasNcResultMap.get(Util.trim(l120s09b.getNcResult())));
				rptVariableMap.put("L120S09B.REFNO",
						Util.trim(l120s09b.getRefNo()));
				rptVariableMap.put("L120S09B.UNIQUEKEY",
						Util.trim(l120s09b.getUniqueKey()));
				rptVariableMap.put("L120S09B.NCCASEID",
						Util.trim(l120s09b.getNcCaseId()));

				// P-108-0046_05097_B1003 Web e-Loan授信系統傳送婉卻紀錄給ORACLE CIF
				ncResultStr = Util
						.equals(Util.trim(l120s09b.getNcResult()), "") ? ""
						: sasNcResultMap.get(Util.trim(l120s09b.getNcResult()));
			}

			rptVariableMap = this.setL120M01AData(rptVariableMap, l120m01a,
					typCdMap, caseLvlMap, prop);

			titleRows = this.setL120S09aData(titleRows, listL120s09a,
					blackListCodeMap, custRelationMap, prop, rptVariableMap,
					l120m01a, l120s09b, ncResultStr);

			// generator.setLang(java.util.Locale.TAIWAN);
			generator.setLang(locale);
			generator.setVariableData(rptVariableMap);
			generator.setRowsData(titleRows);
			// generator.setTestMethod(true);
			// generator.checkVariableExist("C:/test.txt", rptVariableMap);
			outputStream = generator.generateReport();
		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
			if (titleRows != null) {
				titleRows.clear();
			}
			if (caseLvlMap != null) {
				caseLvlMap.clear();
			}
			if (typCdMap != null) {
				typCdMap.clear();
			}
			if (blackListCodeMap != null) {
				blackListCodeMap.clear();
			}
			if (custRelationMap != null) {
				custRelationMap.clear();
			}
			// J-106-0238-001
			// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
			if (sasNcResultMap != null) {
				sasNcResultMap.clear();
			}

		}
		return outputStream;
	}

	/**
	 * 塞入L120S09A．洗錢防制明細檔
	 * 
	 * @param titleRows
	 *            多筆值資料 list代表每一列 map代表每一欄
	 * @param listL120s09a
	 *            List<L120S09A>資料
	 * @param blackListCodeMap
	 *            jobTitleMap
	 * @param custRelationMap
	 *            jobType1Map
	 * @param prop
	 *            prop
	 * @return List<Map<String, String>> list
	 */
	private List<Map<String, String>> setL120S09aData(
			List<Map<String, String>> titleRows, List<L120S09A> listL120s09a,
			Map<String, String> blackListCodeMap,
			Map<String, String> custRelationMap, Properties prop,
			Map<String, String> rptVariableMap, L120M01A l120m01a,
			L120S09B l120s09b, String ncResultStr) {

		// P-108-0046_05097_B1003 Web e-Loan授信系統傳送婉卻紀錄給ORACLE CIF

		int totCount = listL120s09a.size();
		int count = 0;
		// J-106-0057-001 Web e-Loan授信管理系統新增「應收帳款承購無追索權-買方黑名單查詢」功能
		String isFactoring_Without_Recourse_Buyer_Status = "3"; // 預設為不適用
		Timestamp chkTime = null;

		// P-108-0046_05097_B1003 Web e-Loan授信系統傳送婉卻紀錄給ORACLE CIF
		// ORACLE
		boolean notPrintDetailForOracle = false;
		if (amlRelateService.isOracle(l120m01a)) {
			if (l120s09b != null) {
				if (Util.notEquals(Util.trim(l120s09b.getNcResult()), "")) {
					notPrintDetailForOracle = true;
				}
			}
		}
		// J-113-0082 配合法務部新規，於AML頁籤新增引入「受告誡處分」資訊 
		StringBuilder cmfwarnpResults_1 = new StringBuilder(); //查詢結果=1(有)
		StringBuilder cmfwarnpResults_3 = new StringBuilder(); //查詢結果=3(不適用)
		Properties lmss20a_pop = MessageBundleScriptCreator
							.getComponentResource(LMSS20APanel.class);

		for (L120S09A l120s09a : listL120s09a) {
			count = count + 1;

			if (chkTime == null) {
				chkTime = l120s09a.getUpdateTime();
				if (chkTime == null) {
					chkTime = l120s09a.getCreateTime();
				}
			}

			String custRelation = l120s09a.getCustRelation();

			// P-108-0046_05097_B1003 Web e-Loan授信系統傳送婉卻紀錄給ORACLE CIF
			titleRows = this.setL120S09aDetailData(titleRows, l120s09a,
					blackListCodeMap, custRelationMap, count, totCount, prop,
					notPrintDetailForOracle, ncResultStr);

			if (Util.notEquals(isFactoring_Without_Recourse_Buyer_Status, "1")) {

				String blackListCode = Util.trim(l120s09a.getBlackListCode());
				String[] prorertyArray = custRelation.split(",");
				for (String data : prorertyArray) {
					if (UtilConstants.Casedoc.L120s09aBlackListCtlTarget.應收帳款買方無追索
							.equals(data)) {
						if (Util.equals(
								blackListCode,
								UtilConstants.Casedoc.L120s09aBlackListCode.是黑名單)) {
							isFactoring_Without_Recourse_Buyer_Status = "1"; // 有

						} else if (Util
								.equals(blackListCode,
										UtilConstants.Casedoc.L120s09aBlackListCode.可能是黑名單)) {
							isFactoring_Without_Recourse_Buyer_Status = "1"; // 有

						} else if (Util
								.equals(blackListCode,
										UtilConstants.Casedoc.L120s09aBlackListCode.未列於黑名單)) {
							isFactoring_Without_Recourse_Buyer_Status = "2"; // 無
						}
						break;
					}
				}
			}

			// J-113-0082 配合法務部新規，於AML頁籤新增引入「受告誡處分」資訊 
			if(UtilConstants.Casedoc.L120s09aCmfwarnpResultCode.有
					.equals(Util.trim(l120s09a.getCmfwarnpResult()))){
				if(Util.isNotEmpty(cmfwarnpResults_1)){//有超過1筆
					cmfwarnpResults_1.append("</br>");	
				}
				cmfwarnpResults_1.append(MessageFormat.format(
						lmss20a_pop.getProperty("L120S09a.cmfwarnpResult.Msg2"),
						Util.trim(l120s09a.getCustId()),
						Util.trim(l120s09a.getCustName()),
						Util.trim(l120s09a.getCmfwarnpQueryResultInfo())));
			}
			if(UtilConstants.Casedoc.L120s09aCmfwarnpResultCode.不適用
					.equals(Util.trim(l120s09a.getCmfwarnpResult()))){
				if(Util.isNotEmpty(cmfwarnpResults_3)){//有超過1筆
					cmfwarnpResults_3.append("</br>");	
				}
				cmfwarnpResults_3.append(MessageFormat.format(
						lmss20a_pop.getProperty("L120S09a.cmfwarnpResult.Msg4"),
						Util.trim(l120s09a.getCustId()),
						Util.trim(l120s09a.getCustName()),
						(l120s09a.getCmfwarnpQueryTime() == null ? "" : 
							CapDate.formatDate(l120s09a.getCmfwarnpQueryTime(),UtilConstants.DateFormat.YYYY_MM_DD) )));
				
			}
		}

		// J-106-0057-001 Web e-Loan授信管理系統新增「應收帳款承購無追索權-買方黑名單查詢」功能
		// FACTORINGWITHOUTRECOURSEBUYER
		// 1=有 2=無 3=不適用
		if (chkTime != null) {

			String printFactoringTimeStr = Util.trim(lmsService
					.getSysParamDataValue("LMS_J1060057001_ON"));

			Timestamp startTime = CapDate.getCurrentTimestamp();

			if (Util.notEquals(printFactoringTimeStr, "")) {
				startTime = CapDate
						.convertStringToTimestamp(printFactoringTimeStr);
			}

			if (!chkTime.before(startTime)) {
				rptVariableMap
						.put("FACTORINGWITHOUTRECOURSEBUYER",
								Util.nullToSpace(this.showYNPic6(
										Util.nullToSpace(isFactoring_Without_Recourse_Buyer_Status),
										prop)));// 黑名單查詢結果
			} else {
				rptVariableMap.put("FACTORINGWITHOUTRECOURSEBUYER", "");// 黑名單查詢結果
			}
		} else {
			rptVariableMap.put("FACTORINGWITHOUTRECOURSEBUYER", "");// 黑名單查詢結果
		}
		
		// J-113-0082 配合法務部新規，於AML頁籤新增引入「受告誡處分」資訊
		StringBuilder cmfwarnpResultDesc = new StringBuilder();
		if (Util.isNotEmpty(cmfwarnpResults_1)) {
			cmfwarnpResultDesc.append(lmss20a_pop.getProperty("L120S09a.cmfwarnpResult.Msg1OverSea"))
				.append("</br>").append(cmfwarnpResults_1.toString());
		}
		if (Util.isNotEmpty(cmfwarnpResults_3)) {
			if(Util.isNotEmpty(cmfwarnpResultDesc)){
				cmfwarnpResultDesc.append("</br>");
			}
			cmfwarnpResultDesc.append(lmss20a_pop.getProperty("L120S09a.cmfwarnpResult.Msg3"))
				.append("</br>").append(cmfwarnpResults_3.toString());
		}
		rptVariableMap.put("L120S09A.CmfwarnpResultDesc", cmfwarnpResultDesc.toString());// 受告誡處分查詢結果

		return titleRows;
	}

	private List<Map<String, String>> setL120S09aDetailData(
			List<Map<String, String>> titleRows, L120S09A l120s09a,
			Map<String, String> blackListCodeMap,
			Map<String, String> custRelationMap, int count, int totCount,
			Properties prop, boolean notPrintDetailForOracle, String ncResultStr) {

		// J-107-0059-001 Web e-Loan 授信簽報書與動審表之AML頁籤及列印檢核表時，增加引進風險等級
		Properties propLmss20a = MessageBundleScriptCreator
				.getComponentResource(LMSS20APanel.class);

		Map<String, String> map = Util.setColumnMap();

		map.put("ReportBean.column01",
				Util.nullToSpace(l120s09a.getCustId() + l120s09a.getDupNo()));// ID
		map.put("ReportBean.column02", Util.nullToSpace(l120s09a.getCustName()));// 戶名

		map.put("ReportBean.column03",
				Util.nullToSpace(l120s09a.getCustEName()));// 英文戶名

		StringBuilder sb = new StringBuilder();
		String[] newItem = Util.trim(l120s09a.getCustRelation()).split(",");
		// 對陣列進行排序

		int i, j;
		String tmp;
		for (i = newItem.length - 1; i >= 0; i = i - 1) {
			for (j = 0; j < i; j = j + 1) {
				// if (newItem[j] > newItem[i])// 換（"小於"是由大到小）
				if (Util.parseInt((String) newItem[j]) > Util
						.parseInt((String) newItem[i]))// 換（"小於"是由大到小）
				{
					tmp = newItem[j];
					newItem[j] = newItem[i];
					newItem[i] = tmp;
				}
			}
		}

		for (String s : newItem) {
			if (sb.length() > 0)
				sb.append("、");
			sb.append(Util.trim(Util.trim(custRelationMap.get(s))));
		}
		map.put("ReportBean.column04", Util.nullToSpace(sb.toString()));// 相關身分

		// P-108-0046_05097_B1003 Web e-Loan授信系統傳送婉卻紀錄給ORACLE CIF
		if (notPrintDetailForOracle) {
			map.put("ReportBean.column05", ncResultStr);// 黑名單查詢結果
		} else {
			map.put("ReportBean.column05", Util.nullToSpace(this.showYNPicAml(
					Util.nullToSpace(l120s09a.getBlackListCode()), prop)));// 黑名單查詢結果
		}

		// J-107-0059-001 Web e-Loan 授信簽報書與動審表之AML頁籤及列印檢核表時，增加引進風險等級
		// 風險等級
		if (Util.notEquals(Util.trim(l120s09a.getLuvRiskLevel()), "")) {
			map.put("ReportBean.column06",
					propLmss20a.getProperty("L120S09a.riskLvl_"
							+ Util.trim(l120s09a.getLuvRiskLevel())));
		} else {
			map.put("ReportBean.column06", "");
		}

		// e-Loan(企金、消金)徵信及授信管理系統，請增加可輸入借款人(含共同借款人)及保證人(含一般保證、連帶保證、擔保品提供人)國別之欄位；另徵信、簽報及動審AML/CFT頁籤，請將"國別"資料一併納入執行掃描制裁、管制名單(黑名單)。
		// 國別
		if (Util.notEquals(Util.trim(l120s09a.getCountry()), "")) {
			map.put("ReportBean.column07", Util.trim(l120s09a.getCountry()));
		} else {
			map.put("ReportBean.column07", "");
		}

		// 控制標題列
		if (count == 1) {
			map.put("ReportBean.column15", "AML01");
		} else {
			map.put("ReportBean.column15", "");
		}

		// 控制結尾列
		if (count == totCount) {
			map.put("ReportBean.column16", "AML99");
		} else {
			map.put("ReportBean.column16", "");
		}

		titleRows.add(map);
		return titleRows;
	}

	/**
	 * 取得欄位文字□是□可能是□不是
	 * 
	 * @param type
	 *            00=不是02=是 04=可能是
	 * @param prop
	 *            prop
	 * @return string
	 */
	private String showYNPicAml(String type, Properties prop) {
		StringBuffer str = new StringBuffer();
		if ("02".equals(type)) {
			str.append("■");
		} else {
			str.append("□");
		}
		str.append(prop.getProperty("AML.CON02"));
		if ("04".equals(type)) {
			str.append("■");
		} else {
			str.append("□");
		}
		str.append(prop.getProperty("AML.CON04"));
		if ("00".equals(type)) {
			str.append("■");
		} else {
			str.append("□");
		}
		str.append(prop.getProperty("AML.CON00"));
		return str.toString();
	}

	/**
	 * 塞入變數MAP資料使用(L120M01A)
	 * 
	 * @param rptVariableMap
	 *            存放變數MAP
	 * @param l120m01a
	 *            L120M01A資料
	 * @param typCdMap
	 *            typCdMap
	 * @param caseLvlMap
	 *            caseLvlMap
	 * @param prop
	 *            prop
	 * @return rptVariableMap 存放變數MAP
	 */
	private Map<String, String> setL120M01AData(
			Map<String, String> rptVariableMap, L120M01A l120m01a,
			Map<String, String> typCdMap, Map<String, String> caseLvlMap,
			Properties prop) {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);
		if (l120m01a == null) {
			l120m01a = new L120M01A();
		}
		rptVariableMap.put("L120M01A.CASENO",
				Util.nullToSpace(l120m01a.getCaseNo()));
		rptVariableMap.put("L120M01A.TYPCD",
				Util.nullToSpace(typCdMap.get(l120m01a.getTypCd())));
		rptVariableMap.put("L120M01A.CASEBRID",
				Util.nullToSpace(l120m01a.getCaseBrId()));
		rptVariableMap.put("L120M01A.CASEDATE",
				Util.nullToSpace(TWNDate.toAD(l120m01a.getCaseDate())));
		rptVariableMap.put("L120M01A.GIST",
				Util.nullToSpace(l120m01a.getGist()));
		rptVariableMap.put("L120M01A.ITEMOFBUSI",
				Util.nullToSpace(l120m01a.getItemOfBusi()));
		rptVariableMap.put("L120M01A.CESCUSTID",
				Util.nullToSpace(l120m01a.getCesCustId()));
		rptVariableMap.put("L120M01A.CESDUPNO",
				Util.nullToSpace(l120m01a.getCesDupNo()));
		rptVariableMap.put("L120M01A.CUSTID",
				Util.nullToSpace(l120m01a.getCustId()));
		rptVariableMap.put("L120M01A.DUPNO",
				Util.nullToSpace(l120m01a.getDupNo()));
		rptVariableMap.put("L120M01A.CUSTNAME",
				Util.nullToSpace(l120m01a.getCustName()));
		rptVariableMap.put("L120M01A.RANDOMCODE",
				Util.nullToSpace(l120m01a.getRandomCode()));
		rptVariableMap.put("L120M01A.PURPOSE",
				this.getPurpose(Util.nullToSpace(l120m01a.getPurpose()), prop));
		rptVariableMap.put("L120M01A.PURPOSEOTH",
				Util.nullToSpace(l120m01a.getPurposeOth()));
		rptVariableMap.put("L120M01A.RESOURCE", this.getResource(
				Util.nullToSpace(l120m01a.getResource()), prop));
		rptVariableMap.put("L120M01A.RESOURCEOTH",
				Util.nullToSpace(l120m01a.getResourceOth()));
		rptVariableMap.put("L120M01A.LONGCASEFLAG",
				Util.nullToSpace(l120m01a.getLongCaseFlag()));
		// other.msg187=詳授信期間財務預估及產業概況表
		// other.msg188=詳其他
		rptVariableMap.put(
				"L120M01A.LONGCASEDSCR",
				"1".equals(Util.nullToSpace(l120m01a.getLongCaseDscr())) ? pop
						.getProperty("other.msg187") : "2".equals(Util
						.nullToSpace(l120m01a.getLongCaseDscr())) ? pop
						.getProperty("other.msg188") : "");
		rptVariableMap.put(
				"L120M01A.CASELVL",
				!"".equals(Util.nullToSpace(l120m01a.getCaseLvl())
						.replace("　", "").replace(" ", "").trim()) ? "("
						+ Util.nullToSpace(caseLvlMap.get(Util
								.nullToSpace(l120m01a.getCaseLvl())
								.replace("　", " ").trim())) + ")" : "");
		if ("2".equals(Util.nullToSpace(l120m01a.getDocCode()))) {
			rptVariableMap.put("L120M01A.DOCCODENAME",
					Util.nullToSpace(prop.getProperty("L120M01A.DOCCODE2")));
		} else if ("3".equals(Util.nullToSpace(l120m01a.getDocCode()))) {
			rptVariableMap.put("L120M01A.DOCCODENAME",
					Util.nullToSpace(prop.getProperty("L120M01A.DOCCODE3")));
		} else if ("4".equals(Util.nullToSpace(l120m01a.getDocCode()))) {
			rptVariableMap.put("L120M01A.DOCCODENAME",
					Util.nullToSpace(prop.getProperty("L120M01A.DOCCODE4")));
		} else {
			rptVariableMap.put("L120M01A.DOCCODENAME", "");
		}
		rptVariableMap.put("L120M01A.APPROVETIME",
				Util.nullToSpace(TWNDate.toAD(l120m01a.getEndDate())));
		rptVariableMap.put("L120M01A.RPTTITLEAREA1",
				Util.nullToSpace(l120m01a.getRptTitleArea1()));
		rptVariableMap
				.put("L120M01A.RPTTITLE1", this.formatRptTitle1(Util
						.nullToSpace(l120m01a.getRptTitle1())));
		rptVariableMap.put("L120M01A.RPTTITLE2",
				Util.nullToSpace(l120m01a.getRptTitle2()));
		rptVariableMap.put("L120M01A.CUSTID",
				Util.nullToSpace(l120m01a.getCustId()));
		rptVariableMap.put("L120M01A.DUPNO",
				Util.nullToSpace(l120m01a.getDupNo()));
		rptVariableMap.put("L120M01A.CUSTNAME",
				Util.nullToSpace(l120m01a.getCustName()));
		rptVariableMap.put("L120M01A.CASEBRID",
				Util.nullToSpace(l120m01a.getCaseBrId()));
		rptVariableMap.put("L120M01A.CASENO",
				Util.nullToSpace(l120m01a.getCaseNo()));
		rptVariableMap.put("L120M01A.AREACHK",
				Util.nullToSpace(l120m01a.getAreaChk()));
		rptVariableMap
				.put("L120M01A.AUTHLVL", Util.trim(l120m01a.getAuthLvl()));

		// 國內屬營運中心制分行標題名稱
		rptVariableMap.put("AREATITLE",
				Util.nullToSpace(queryAreaTitle(l120m01a)));
		return rptVariableMap;
	}

	/**
	 * 取得國內屬營運中心制分行的標題名稱
	 * 
	 * @param l120m01a
	 *            簽報書主檔
	 * @return
	 * @throws CapException
	 */
	private String queryAreaTitle(L120M01A l120m01a) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		IBranch tBranch = branch.getBranch((l120m01a != null) ? Util
				.trim(l120m01a.getCaseBrId()) : user.getUnitNo());
		String docKind = Util.trim(l120m01a.getDocKind());
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);
		if (tBranch != null) {
			String brnGroup = Util.trim(tBranch.getBrnGroup());
			if (UtilConstants.BankNo.中部區域授信中心.equals(brnGroup)
					|| UtilConstants.BankNo.北一區營運中心.equals(brnGroup)
					|| UtilConstants.BankNo.南部區域授信中心.equals(brnGroup)
					|| UtilConstants.BankNo.北二區營運中心.equals(brnGroup)
					|| UtilConstants.BankNo.桃竹苗區營運中心.equals(brnGroup)
					|| UtilConstants.BankNo.中區營運中心.equals(brnGroup)
					|| UtilConstants.BankNo.南區營運中心.equals(brnGroup)) {
				if (UtilConstants.Casedoc.DocKind.授權外.equals(docKind)) {
					/*
					 * 因為海外分行不屬於營運中心制，所以提醒第四階段，國內屬營運中心制分行時TITLE顯示會有差異
					 * 國內營運中心制分行，分行授權外案件會顯示營運中心授權外案件簽報書
					 */
					// other.msg131=營運中心授權外
					return pop.getProperty("other.msg131");
				}
			}
		}
		return null;
	}

	/**
	 * 取得借款用途
	 * 
	 * @param purpose
	 *            借款用途
	 * @return 借款用途
	 */
	private String getPurpose(String purpose, Properties prop) {
		StringBuffer str = new StringBuffer();
		if (purpose.indexOf("1") >= 0) {
			str.append(prop.getProperty("L120M01A.PURPOSE1"));
			str.append("，");
		}

		if (purpose.indexOf("2") >= 0) {
			str.append(prop.getProperty("L120M01A.PURPOSE2"));
			str.append("，");
		}

		if (purpose.indexOf("3") >= 0) {
			str.append(prop.getProperty("L120M01A.PURPOSE3"));
			str.append("，");
		}

		if (str.length() > 0) {
			str.deleteCharAt(str.length() - 1);
		}

		return str.toString();
	}

	/**
	 * 取得還款財源
	 * 
	 * @param resource
	 *            還款財源
	 * @return 還款財源
	 */
	private String getResource(String resource, Properties prop) {
		StringBuffer str = new StringBuffer();
		if (resource.indexOf("1") >= 0) {
			str.append(prop.getProperty("L120M01A.RESOURCE1"));
			str.append("，");
		}

		if (resource.indexOf("2") >= 0) {
			str.append(prop.getProperty("L120M01A.RESOURCE2"));
			str.append("，");
		}

		if (resource.indexOf("3") >= 0) {
			str.append(prop.getProperty("L120M01A.RESOURCE3"));
			str.append("，");
		}

		if (str.length() > 0) {
			str.deleteCharAt(str.length() - 1);
		}

		return str.toString();
	}

	/**
	 * 從動審表取得簽報書 J-106-0029-003 洗錢防制-新增實質受益人
	 * 
	 * @param l160m01a
	 * @return
	 */
	public L120M01A findL120m01aByL160m01a(L160M01A l160m01a) {
		L120M01A l120m01a = null;

		Map<String, String> cntrCustIdMap = new HashMap<String, String>();
		Set<L160M01B> l160m01bs = l160m01a.getL160m01b(); // 這次動用的額度
		if (l160m01bs != null && !l160m01bs.isEmpty()) {
			for (L160M01B l160m01b : l160m01bs) {
				L140M01A l140m01a = lms1405Service
						.findL140m01aByMainId(l160m01b.getReMainId());
				if (l140m01a != null) {

					L120M01C l120m01c = l140m01a.getL120m01c();
					l120m01a = service1205.findL120m01aByMainId(l120m01c
							.getMainId());
					if (l120m01a != null) {
						break;
					}

				}
			}
		}
		return l120m01a;
	}

	/**
	 * 取得欄位文字□有□無□不適用
	 * 
	 * @param type
	 *            1=有 2=無 3=不適用
	 * @param prop
	 *            prop
	 * @return string
	 */
	private String showYNPic6(String type, Properties prop) {
		StringBuffer str = new StringBuffer();
		if ("1".equals(type)) {
			str.append("■");
		} else {
			str.append("□");
		}
		str.append(prop.getProperty("COMMON.CON1"));
		if ("2".equals(type)) {
			str.append("■");
		} else {
			str.append("□");
		}
		str.append(prop.getProperty("COMMON.CON2"));
		if ("3".equals(type)) {
			str.append("■");
		} else {
			str.append("□");
		}
		// AML.NONFACTORING=非應收帳款承購業務
		str.append(prop.getProperty("COMMON.CON3") + "("
				+ (prop.getProperty("AML.NONFACTORING")) + ")");
		return str.toString();
	}

	/**
	 * J-109-0322_05097_B1001 Web e-Loan企消金授信簽報登錄決議輸入授審會會期, 於列印決議內容時將會期資訊改以西元年呈現
	 * 
	 * @param rptTitle1
	 *            授審會會期
	 * @return
	 */
	private String formatRptTitle1(String rptTitle1) {

		rptTitle1 = Util.trim(rptTitle1);

		if (Util.equals(rptTitle1, "")) {
			return rptTitle1;
		}

		String rtnRptTitle = rptTitle1;

		int yearIndex = rptTitle1.indexOf("年");

		if (yearIndex == 3) {
			String printAdYear = Util.trim(lmsService
					.getSysParamDataValue("LMS_RPTTITLE1_PRINT_AD_YEAR"));
			if (Util.equals(printAdYear, "Y")) {
				rtnRptTitle = (Util.parseInt(Util.getLeftStr(rptTitle1, 3)) + 1911)
						+ Util.getRightStr(rptTitle1, rptTitle1.length() - 3);
			}
		}

		return rtnRptTitle;
	}

}
