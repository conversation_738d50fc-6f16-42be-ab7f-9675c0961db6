/* 
 * L120S08A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;

import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 利率定價核理性分析表主檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L120S08A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class L120S08A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 幣別
	 * <p/>
	 * NOT NULL
	 */
	@Size(max = 3)
	@Column(name = "CURR", length = 3, columnDefinition = "CHAR(3)")
	private String curr;

	/** 序號 **/
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "SEQNO", columnDefinition = "DECIMAL(3,0)")
	private BigDecimal seqNo;

	/** 來源額度序號 **/
	@Size(max = 12)
	@Column(name = "CNTRNO", length = 12, columnDefinition = "CHAR(12)")
	private String cntrNo;

	/** 基礎放款利率 **/
	@Column(name = "BASERATE", columnDefinition = "DECIMAL(7,5)")
	private BigDecimal baseRate;

	/** 利率條件 **/
	@Size(max = 4096)
	@Column(name = "RATEDSCR", length = 4096, columnDefinition = "VARCHAR(4096)")
	private String rateDscr;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/** 借款人統編 **/
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "CHAR(10)")
	private String custId;

	/** 重覆序號 **/
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/** 列印組別 **/
	@Column(name = "PRINTGROUP", columnDefinition = "DECIMAL(3,0)")
	private BigDecimal printGroup;

	/** 版本日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "VERSIONDATE", columnDefinition = "DATE")
	private Date versionDate;

	/** 列印順序 **/
	@Column(name = "PRINTSEQ", columnDefinition = "INTEGER")
	private Integer printSeq;

	/** 備註 **/
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(name = "MEMO", columnDefinition = "CLOB")
	private String memo;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得幣別
	 * <p/>
	 * NOT NULL
	 */
	public String getCurr() {
		return this.curr;
	}

	/**
	 * 設定幣別
	 * <p/>
	 * NOT NULL
	 **/
	public void setCurr(String value) {
		this.curr = value;
	}

	/** 取得序號 **/
	public BigDecimal getSeqNo() {
		return this.seqNo;
	}

	/** 設定序號 **/
	public void setSeqNo(BigDecimal value) {
		this.seqNo = value;
	}

	/** 取得來源額度序號 **/
	public String getCntrNo() {
		return this.cntrNo;
	}

	/** 設定來源額度序號 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/** 取得利率條件 **/
	public String getRateDscr() {
		return this.rateDscr;
	}

	/** 設定利率條件 **/
	public void setRateDscr(String value) {
		this.rateDscr = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 設定基礎放款利率 **/
	public void setBaseRate(BigDecimal baseRate) {
		this.baseRate = baseRate;
	}

	/** 設定基礎放款利率 **/
	public BigDecimal getBaseRate() {
		return baseRate;
	}

	/** 設定借款人統編 **/
	public void setCustId(String custId) {
		this.custId = custId;
	}

	/** 取得借款人統編 **/
	public String getCustId() {
		return custId;
	}

	/** 設定重覆序號 **/
	public void setDupNo(String dupNo) {
		this.dupNo = dupNo;
	}

	/** 取得重覆序號 **/
	public String getDupNo() {
		return dupNo;
	}

	/** 設定列印組別 **/
	public void setPrintGroup(BigDecimal printGroup) {
		this.printGroup = printGroup;
	}

	/** 取得列印組別 **/
	public BigDecimal getPrintGroup() {
		return printGroup;
	}

	/** 設定版本日期 **/
	public void setVersionDate(Date versionDate) {
		this.versionDate = versionDate;
	}

	/** 取得版本日期 **/
	public Date getVersionDate() {
		return versionDate;
	}

	/** 設定列印順序 **/
	public void setPrintSeq(Integer printSeq) {
		this.printSeq = printSeq;
	}

	/** 取得列印順序 **/
	public Integer getPrintSeq() {
		return printSeq;
	}

	/** 設定補充說明 **/
	public void setMemo(String memo) {
		this.memo = memo;
	}

	/** 取得補充說明 **/
	public String getMemo() {
		return memo;
	}
}
