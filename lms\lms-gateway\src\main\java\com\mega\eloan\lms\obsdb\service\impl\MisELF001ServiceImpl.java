package com.mega.eloan.lms.obsdb.service.impl;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.exception.GWException;
import com.mega.eloan.common.jdbc.AbstractOBSDBJdbcFactory;
import com.mega.eloan.lms.mfaloan.service.MisRatetblService;
import com.mega.eloan.lms.obsdb.service.MisELF001Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;

@Service
public class MisELF001ServiceImpl extends AbstractOBSDBJdbcFactory implements
		MisELF001Service {
	@Resource
	BranchService branchService;
	@Resource
	MisRatetblService misRateService;

	public MisELF001ServiceImpl() {
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public List queryAvgAmtTById(String custId, String dupNo) {
		String APCDCode[][] = { { "20000000", "03" }, { "20400000", "03" },
				{ "20430000", "05" }, { "20800000", "03" },
				{ "20810100", "03" }, { "20810200", "03" },
				{ "21220000", "08" }, { "21240101", "03" },
				{ "21240102", "05" }, { "21240201", "03" },
				{ "21240202", "05" }, { "21260000", "08" },
				{ "21600000", "05" }, { "21620000", "05" },
				{ "22000000", "06" }, { "22010000", "06" },
				{ "22020000", "13" }, { "22040000", "10" },
				{ "22050000", "11" }, { "22090000", "09" },
				{ "22160000", "12" }, { "22250000", "02" },
				{ "22420300", "03" }, { "27770000", "14" } };
		List rtnMap = new ArrayList();
		String brnId = MegaSSOSecurityContext.getUnitNo();
		try {
			rtnMap = getJdbc(brnId).queryForList(
					"ELF001.queryAvgAmtTById",
					new String[] { brnId,
							CapString.trimNull(custId).concat(dupNo) });
		} catch (GWException e) {
			return null;
		}
		if (!CollectionUtils.isEmpty(rtnMap)) {
			HashMap hm = new HashMap();
			String arr$[][] = APCDCode;
			int len$ = arr$.length;
			for (int i$ = 0; i$ < len$; i$++) {
				String mapping[] = arr$[i$];
				hm.put(mapping[0], mapping[1]);
			}

			for (Iterator i$ = rtnMap.iterator(); i$.hasNext();) {
				Map data = (Map) i$.next();
				String APCD = (String) data.get("APCD");
				if ("21200000".equals(APCD)) {
					if ("TWD".equals(data.get("ELF001_CURR")))
						data.put("depType", "01");
					else
						data.put("depType", "08");
				} else {
					if (hm.containsKey(APCD))
						data.put("depType", hm.get(data.get("APCD")));
					else
						data.put("depType", "");
				}
			}

		}
		return rtnMap;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Map findELF003ProfitContributeById(String custId, String dupNo,
			String branch) {
		Map rtnMap = new HashMap();
		String args[] = new String[1];
		args[0] = (new StringBuilder())
				.append(CapString.fillString(custId, 10, false, ' '))
				.append("0".equals(dupNo) ? "" : dupNo).toString();
		rtnMap = getJdbc(branch).queryForMap(
				"ELF003.queryProfitContributeById", args);
		if (!CollectionUtils.isEmpty(rtnMap))
			try {
				String key;
				for (Iterator keys = rtnMap.keySet().iterator(); keys.hasNext(); logger
						.debug((new StringBuilder())
								.append("AS400**queryProfitContributeById.........keyid => \"")
								.append(key).append(" \":::vaule => ")
								.append(rtnMap.get(key)).toString()))
					key = (String) keys.next();

			} catch (Exception e) {
			}
		return rtnMap;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Map findELF003ProfitContributeByIdDate(String custId, String dupNo,
			String branch, String startDate, String endDate) {
		Map<String, Object> rtnMap = new HashMap<String, Object>();
		String[] args = new String[3];
		// 20120704, CP, dupNo 為 0 時改代入半形空白。
		if ("0".equals(dupNo)) {
			dupNo = " ";
		}
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
		args[0] = CapString.fillString(custId, 10, false, ' ') + dupNo;
		args[1] = startDate;
		args[2] = endDate;

		List<Map<String, Object>> list = getJdbc(branch).queryForList(
				"ELF003.queryProfitContributeByIdDate", args);
		IBranch ibranch = branchService.getBranch(branch);
		// J-111-0443_05097_B1002 Web e-Loan企金授信開發授信BIS評估表
		Map<String, String> ELF003_YYMM_Map = new HashMap<String, String>();

		// J-112-0281_05097_B1001 Web
		// e-Loan簽報書BIS評估表調整海外分行存款貢獻度之年化處理及非授信貢獻度為負時應顯示之警語
		boolean hasNegative = false;

		if (!list.isEmpty()) {
			BigDecimal exchangeRate = null, tmpContribute = BigDecimal.ZERO;
			Calendar date = Calendar.getInstance();
			SimpleDateFormat defaultDateFormat = new SimpleDateFormat(
					"yyyy-MM-dd");
			for (Map<String, Object> m : list) {
				String curr = MapUtils.getString(m, "ELF003_LOC_CURR");

				// J-111-0443_05097_B1002 Web e-Loan企金授信開發授信BIS評估表
				String ELF003_YYMM = MapUtils.getString(m, "ELF003_YYMM");
				if (!ELF003_YYMM_Map.containsKey(ELF003_YYMM)) {
					ELF003_YYMM_Map.put(ELF003_YYMM, ELF003_YYMM);
				}

				try {
					date.setTime((Date) sdf.parse(MapUtils.getString(m,
							"ELF003_YYMM")));
				} catch (ParseException e) {
					logger.error(e.getMessage(), e);
				}
				date.set(Calendar.MONTH, date.getActualMaximum(Calendar.MONTH));

				if (!CapString.isEmpty(curr)) {
					if ("TWD".equals(curr)) {
						exchangeRate = BigDecimal.ONE;
					} else {
						// 不帶日期，改為抓最新的
						// defaultDateFormat.format(date.getTime())
						exchangeRate = misRateService.getCur1ToCur2Rate("",
								ibranch, curr, "TWD");
					}
				} else {
					exchangeRate = BigDecimal.ONE;
				}
				BigDecimal tmpBD = new BigDecimal(MapUtils.getString(m,
						"ELF003_AMT", "0"));

				// J-112-0281_05097_B1001 Web
				// e-Loan簽報書BIS評估表調整海外分行存款貢獻度之年化處理及非授信貢獻度為負時應顯示之警語
				if (tmpBD.compareTo(BigDecimal.ZERO) < 0) {
					hasNegative = true;
				} else {
					hasNegative = false;
				}

				tmpContribute = tmpContribute.add(tmpBD.multiply(exchangeRate));
			}
			rtnMap.put("ELF003_AMT", tmpContribute);
			rtnMap.put("ELF003_LOC_CURR", "TWD");
			// J-111-0443_05097_B1002 Web e-Loan企金授信開發授信BIS評估表
			rtnMap.put("ELF003_YYMM_COUNT", ELF003_YYMM_Map == null
					|| ELF003_YYMM_Map.isEmpty() ? BigDecimal.ZERO
					: ELF003_YYMM_Map.size());

			// J-112-0281_05097_B1001 Web
			// e-Loan簽報書BIS評估表調整海外分行存款貢獻度之年化處理及非授信貢獻度為負時應顯示之警語
			// [星期一 上午 09:58] 任立群(風險控管處,高級辦事員)
			// 1.最後一個月是負的要跳警語
			rtnMap.put("ELF003_LAST_HAS_NEGATIVE", hasNegative ? "Y" : "N");

		}
		if (!CollectionUtils.isEmpty(rtnMap)) {
			try { // for test
				Iterator<String> keys = rtnMap.keySet().iterator();
				while (keys.hasNext()) {
					String key = keys.next();
					logger.debug("AS400**queryProfitContributeById........."
							+ "keyid => \"" + key + " \":::vaule => "
							+ rtnMap.get(key));
				}
			} catch (Exception e) {
			}
		}
		return rtnMap;
	}

	private static final Logger logger = LoggerFactory
			.getLogger("com/mega/eloan/lms/mfaloan/service/MisELF001Service");

}