
package com.mega.eloan.lms.cls.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.common.ContractDocUtil;

/**
 * <pre>
 * 消金契約書
 * </pre>
 * 
 * @since 2020/08/07
 * <AUTHOR>
 * @version <ul>
 *          <li>2020/08/07,EL08034,new
 *          </ul>
 */
public class CLS3401S33Panel extends Panel {

	public CLS3401S33Panel(String id) {
		super(id);
	}

	public CLS3401S33Panel(String id, boolean updatePanelName) {
		super(id, updatePanelName);
	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);

		// UPGRADE: 前端須配合改Thymeleaf的樣式
//		Label _NumberLabel = new Label("S03_Chinese_Number", ContractDocUtil.list_Chinese_Number());
//		_NumberLabel.setEscapeModelStrings(false);
//		add(_NumberLabel);
		model.addAttribute("S03_Chinese_Number", ContractDocUtil.list_Chinese_Number());
	}

	/**/
	private static final long serialVersionUID = 1L;
}
