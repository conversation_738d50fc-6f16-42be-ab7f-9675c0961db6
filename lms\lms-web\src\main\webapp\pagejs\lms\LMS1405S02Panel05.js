if (!window.CMSAction) {
    window.CMSAction = {
        grid: null,
        srcGrid: null,
        stockGrid :null,   //J-108-0225_05097_B1001
        formId: "#queryCMSDataForm",
        _initgrid: function(){
            this.grid = $("#gridviewCollateral").iGrid({
                handler: inits.ghandle,
                action: "queryL140m01o",
                height: 200,
                rownumbers: true,
                autowidth: true,
                multiselect: true,
                colModel: [{
                    colHeader: i18n.lms1405s02['l1405s02c01.015'],//擔保品分行
                    name: 'branch',
                    align: "left",
                    width: 110,
                    sortable: true,
                    formatter: 'click',
                    onclick: CMSAction.openCMSBox
                }, {
                    colHeader: i18n.lms1405s02['l1405s02c01.016'],//擔保品類別
                    name: 'collTyp1',
                    align: "left",
                    width: 110,
                    sortable: true
                }, {
                    colHeader: i18n.lms1405s02['L140S02Tab.5_01'],//擔保品內容
                    name: 'cmsDesc',
                    width: 160,
                    sortable: false
                }, {
                    colHeader: i18n.lms1405s02['l1405s02c01.022'],//核貸成數
                    name: 'payPercent',
                    width: 60,
                    align: "right",
                    sortable: true,
                    formatter: 'currency',
                    formatoptions: {
                        thousandsSeparator: ",",
                        decimalPlaces: 0,
                        suffix: "%"
                    }
                }, {
                    name: 'oid',
                    hidden: true
                }],
                ondblClickRow: function(rowid){
                    var data = CMSAction.grid.getRowData(rowid);
                    CMSAction.openCMSBox(null, null, data);
                }
            });
            
            this.srcGrid = $("#gridviewSrcColl").iGrid({
                handler: inits.ghandle,
                height: 200,
                action: "queryCMS",
                rownumbers: true,
                autowidth: true,
                multiselect: true,
                colModel: [{
                    colHeader: i18n.lms1405s02['l1405s02c01.018'],//鑑估日期
                    name: 'estDate',
                    align: "left",
                    width: 80,
                    sortable: true
                }, {
                    colHeader: i18n.lms1405s02['L140M01a.custName'],//借款人名稱
                    name: 'custName',
                    align: "left",
                    width: 160,
                    sortable: true
                }, {
                    colHeader: i18n.lms1405s02['l1405s02c01.016'],//擔保品類別
                    name: 'collTyp1',
                    width: 100,
                    sortable: true
                }, {
                    colHeader: i18n.lms1405s02['l1405s02c01.016'],//擔保品小類
                    name: 'collTyp2',
                    width: 100,
                    sortable: true
                }, {
                    colHeader: i18n.lms1405s02['L140M01a.docStatus'],//文件狀態
                    name: 'docStatus',
                    width: 90,
                    sortable: true
                }, {
                    colHeader: "&nbsp",//幣別
                    name: 'currCd',
                    align: "center",
                    width: 50,
                    sortable: true
                }, {
                    colHeader: i18n.lms1405s02['l1405s02c01.017'],//放款值
                    name: 'loanAmt',
                    align: "right",
                    width: 120,
                    sortable: true,
                    formatter: 'currency',
                    formatoptions: {
                        thousandsSeparator: ",",
                        decimalPlaces: 0
                    }
                }, {
                    colHeader: i18n.lms1405s02['l1405s02c01.025'],//估價人員
                    name: 'appraiserName',
                    align: "left",
                    width: 80,
                    sortable: false
                }, {
                    name: 'appraiser',
                    hidden: true
                }, {
                    name: 'oid',
                    hidden: true
                }, {
                    name: 'mainId',
                    hidden: true
                }]
            });
            
            //J-108-0225_05097_B1001 Web e-Loan企金授信額度明細表新增設質予本行之總股數欄位與檢核
            this.stockGrid = $("#gridviewCollateralStock").iGrid({
                handler: inits.ghandle,
                action: "queryL140m01o_0307",
                postData: {
                    tabFormMainId: $("#tabFormMainId").val(),
                    mainId: $("#mainId").val()
                },
                sortname: 'ctrlKind|setRatio|stkNo',
                sortorder: 'asc|desc|asc',
                height: 200,
                rownumbers: true,
                autowidth: true,
                multiselect: true,
                colModel: [{
                    colHeader: i18n.lms1405s02['L140M01o.stkNo'],//股票代號
                    name: 'stkNo',
                    align: "left",
                    width: 30,
                    sortable: true,
                    formatter: 'click',
                    onclick: CMSAction.openEditStockBox
                }, {
                    colHeader: i18n.lms1405s02['L140M01o.stkNm'],//股票名稱
                    name: 'stkNm',
                    align: "left",
                    width: 70,
                    sortable: true
               
                }, {
                    colHeader: i18n.lms1405s02['L140M01o.qnty'],//設質總股數	
                    name: 'qnty',
                    width: 60,
                    align: "right",
                    sortable: true,
                    formatter: 'currency',
                    formatoptions: {
                        thousandsSeparator: ",",
                        decimalPlaces: 0
                    }
                }, {
                    colHeader: i18n.lms1405s02['L140M01o.changeQnty'],//加計本次增減設質總股數	
                    name: 'changeQnty',
                    width: 60,
                    align: "right",
                    sortable: true,
                    formatter: 'currency',
                    formatoptions: {
                        thousandsSeparator: ",",
                        decimalPlaces: 0
                    }
                }, {
                    colHeader: i18n.lms1405s02['L140M01o.stkNum'],//發行總股數
                    name: 'stkNum',
                    width: 60,
                    align: "right",
                    sortable: true,
                    formatter: function(data){
                    	if (data == 'undefined' || data == null) {
                            return "N.A.";
                        }else {
                        	 return util.addComma(data);
                        }
                        
                    },		
                    formatoptions: {
                        thousandsSeparator: ",",
                        decimalPlaces: 0
                    }
                }, {
                    colHeader: i18n.lms1405s02['L140M01o.setRatio'],//設質比率	
                    name: 'setRatio',
                    width: 40,
                    align: "right",
                    sortable: true,
                    formatter: function(data){
                    	if (data == 'undefined' || data == null) {
                            return "N.A.";
                        }
                        else {
                            return data+"%";
                        }
                    },		
                    formatoptions: {
                        thousandsSeparator: ",",
                        decimalPlaces: 2,
                        suffix: "%"
                    }
                }, {
                    name: 'oid',
                    hidden: true
                }, {
                    name: 'ctrlKind',
                    hidden: true    
                }],
                ondblClickRow: function(rowid){
                    var data = CMSAction.stockGrid.getRowData(rowid);
                    CMSAction.openEditStockBox(null, null, data);
                }
            });
        },
        init: function(inits){
            CMSAction._initgrid();
            /**
             登錄分行代號
             */
            $("#selectCMSBranchBt").click(function(){
                API.showAllBranch({
                    btnAction: function(a, b){
                        $("#selectFilterCMDBrno").val(b.brNo);
                        $.thickbox.close();
                    }
                });
            });
            /**
             * 查詢引進擔保品
             */
            $("#queryCMSData").click(function(){
                CMSAction.queryCMSData();
            });
            /**
             * 引進擔保品
             */
            $("#inculdeCMS").click(function(){
                CMSAction.inculdeCMS();
            });
            /**
             * 刪除擔保品
             */
            $("#deleteCMS").click(function(){
                CMSAction.deleteCMS();
            });
            /**
             * 重新引進文字
             */
            $("#reloadCMS").click(function(){
                CMSAction.reloadCMS();
            });
            /**
             * 引入評等質化內容
             */
            $("#impCMSFromRatingDoc").click(function(){
                CMSAction.impCMSFromRatingDoc();
            });
            
          //J-108-0225_05097_B1001 Web e-Loan企金授信額度明細表新增設質予本行之總股數欄位與檢核
            //引進前項擔保品建檔資料
            $("#inculdeCMSStock").click(function(){
            	CMSAction.inculdeCMSStock();
            });
            
            //J-108-0225_05097_B1001 Web e-Loan企金授信額度明細表新增設質予本行之總股數欄位與檢核
            //新增股票資料
            $("#addCMSStock").click(function(){
            	CMSAction.addCMSStock();
            });
            
            //J-108-0225_05097_B1001 Web e-Loan企金授信額度明細表新增設質予本行之總股數欄位與檢核
            //刪除股票資料
            $("#deleteCMSStock").click(function(){
            	CMSAction.deleteCMSStock();
            });
            
            //J-108-0225_05097_B1001 Web e-Loan企金授信額度明細表新增設質予本行之總股數欄位與檢核
            //引進股票設質比率
            $("#btnFindStgaStkNo").click(function(){
            	CMSAction.findStgaStkNo();
            });
            
            //J-108-0225_05097_B1001 Web e-Loan企金授信額度明細表新增設質予本行之總股數欄位與檢核
            //計算設質比率
            $("#btnCaculateSetRatio").click(function(){
            	CMSAction.caculateSetRatio();
            });
            
            /**
             * 更新grid
             */
            $("#lms140Tab05").click(function(){
                //組字串
            	//J-112-0586_05097_B1002 依據簽會-2023-2192「Web eLoan-Checkmarx弱點改善會議」按季追蹤弱點修正進度
                var headItem1 = $("input[name=headItem1]:checked").val(), gutPercent = DOMPurify.sanitize($("#gutPercent").val());
                if ("Y" == headItem1 && gutPercent != "0") {
                    $("#toALoan3").html(i18n.lms1405s02["L140M01a.message67"] + gutPercent + "%");
                } else {
                    $("#toALoan3").html("");
                }
                CMSAction.reloadGrid({
                    tabFormMainId: $("#tabFormMainId").val()
                }, CMSAction.grid);
                CMSAction.reloadGrid({}, CMSAction.srcGrid);
                
                //J-108-0225_05097_B1001 Web e-Loan企金授信額度明細表新增設質予本行之總股數欄位與檢核
                CMSAction._reloadStockGrid({}, CMSAction.stockGrid);
                
            });
            
            
            //J-108-0225_05097_B1001 Web e-Loan企金授信額度明細表新增設質予本行之總股數欄位與檢核
        	$("input[name='ctrlKind']" ).change(function(){
        		var $form = $("#formFindStgaData");
        		var ctrlKind= $form.find('input:checkbox:checked[name="ctrlKind"]').val();
        		if(ctrlKind=="2"){
        			$form.find(".showFindStgaData").find(":input").not(':button, :submit, :reset, :hidden').val("");
        			$form.find(".showFindStgaData").hide();
        			
        		}else{
        			$form.find(".showFindStgaData").show();
        			
        		}
        	}).trigger("change");
            
            
        },
        /**
         * 設定描述值
         * @param {Object} drc 描述
         */
        setDrc: function(drc){
            $("#itemDscr3").val(drc);
        },
        /**
         *依查詢條件重新查詢grid
         */
        queryCMSData: function(){
            var $form = $(CMSAction.formId);
            if (!$form.valid()) {
                return false;
            }
            CMSAction.reloadGrid($form.serializeData(), CMSAction.srcGrid);
        },
        /**
         * 設定初始值
         */
        setDefault: function(){
            var $CMSBoxDiv = $("#CMSBoxDiv");
            var $form = $("#L140M01AForm1");
            $CMSBoxDiv.find("#selectFilterCMDBrno").val($form.find("#ownBrId").val());
            $CMSBoxDiv.find("#cmsCustId").val($form.find("#custId").html());
            $CMSBoxDiv.find("#cmsDupNo").val($form.find("#dupNo").html());
            $CMSBoxDiv.find("#cmsType").val("01");
            
            
        },
        /**
         *引進擔保品
         */
        inculdeCMS: function(){
            CMSAction.reloadGrid({}, CMSAction.srcGrid);
            CMSAction.setDefault();
            $("#CMSBox").thickbox({
                title: i18n.lms1405s02["btn.cms"],
                width: 900,
                height: 500,
                modal: true,
                align: "center",
                valign: "bottom",
                readOnly: _openerLockDoc == "1" || inits.toreadOnly,
                i18n: i18n.def,
                open: function(){
                    $("#CMSBoxDiv").wrap("<form id='queryCMSDataForm'></form>");
                },
                close: function(){
                    $("#CMSBoxDiv").unwrap();
                },
                buttons: {
                    "sure": function(){
                        var gridIDs = CMSAction.srcGrid.getGridParam('selarrrow');
                        if (gridIDs == "") {
                            //action_005=請先選取一筆以上之資料列
                            API.showMessage(i18n.def["action_005"]);
                            return false;
                        }
                        var gridsOid = [];
                        for (var i = 0, total = gridIDs.length; i < total; i++) {
                            gridsOid[i] = CMSAction.srcGrid.getRowData(gridIDs[i]).oid;
							
							//J-109-0298 檢核引入之估價人員，如若有與授信經辦為同一人時，由系統發訊息警示。
							var appraiser = CMSAction.srcGrid.getRowData(gridIDs[i]).appraiser;
							if (appraiser == userInfo.userId) {
								//C100M01.Error1=估價人員不可和授信經辦為同一人!!
	                        	API.showErrorMessage(i18n.lms1405s02["C100M01.Error1"]);
	                        	return false;
							}
                        }
                        $.ajax({
                            handler: inits.fhandle,
                            action: "inculdeL140M01O",
                            data: {
                                tabFormMainId: $("#tabFormMainId").val(),
                                oids: gridsOid
                            },
                            success: function(obj){
                                CMSAction.setDrc(obj.drc);
                                CMSAction.reloadGrid({}, CMSAction.grid);
                                $.thickbox.close();
                            }
                        });
                        
                    },
                    "cancel": function(){
                        $.thickbox.close();
                    }
                }
            });
        },
        deleteCMS: function(){
            var gridIDs = CMSAction.grid.getGridParam('selarrrow');
            if (gridIDs == "") {
                //TMMDeleteError=請先選擇需修改(刪除)之資料列
                API.showMessage(i18n.def["TMMDeleteError"]);
                return false;
            }
            
            //confirmDelete=是否確定刪除?
            CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
                if (b) {
                    var gridsOid = [];
                    for (var i = 0, total = gridIDs.length; i < total; i++) {
                        gridsOid[i] = CMSAction.grid.getRowData(gridIDs[i]).oid;
                    }
                    $.ajax({
                        handler: inits.fhandle,
                        action: "deleteL140M01O",
                        data: {
                            tabFormMainId: $("#tabFormMainId").val(),
                            oids: gridsOid
                        },
                        success: function(obj){
                            CMSAction.setDrc(obj.drc);
                            CMSAction.reloadGrid({}, CMSAction.grid);
                        }
                    });
                }
            });
        },
        /**
         * 重新引進
         */
        reloadCMS: function(){
            $.ajax({
                handler: inits.fhandle,
                action: "reloadCMSDesc",
                data: {
                    tabFormMainId: $("#tabFormMainId").val()
                },
                success: function(obj){
                    CMSAction.setDrc(obj.drc);
                }
            });
        },
        /**
         * 重新整理Grid
         * @param {Object} data 設定資料
         * @param {Object} gridObject
         */
        reloadGrid: function(data, gridObject){
            gridObject.jqGrid("setGridParam", {
            	sortname: gridObject != CMSAction.grid  ? "collTyp1,createTime" : "collTyp1,seqNo,createTime",
                sortorder: gridObject != CMSAction.grid ? "asc,asc" : "asc,asc,asc",
                postData: $.extend({
                    selectFilterCMDBrno: "",
                    cmsCustId: "",
                    cmsDupNo: "",
                    cmsType: ""
                }, data || {}),
                page: 1,
                search: true
            }).trigger("reloadGrid");
        },
        /**
         * 引入評等質化內容
         */
        impCMSFromRatingDoc: function(){
        	var $formQualitativeFactor = $("#QualitativeFactorForm");
        	$formQualitativeFactor.reset();
        	
        	$.ajax({
                handler: inits.fhandle,
                action: "impCMSFromRatingDoc",
                data: {
                    tabFormMainId: $("#tabFormMainId").val()
                },
                success: function(obj){
                	$formQualitativeFactor.injectData(obj);
                }
            });
        },
        
        /**
         * 開啟修改鑑估值
         * @param {Object} cellvalue
         * @param {Object} type
         * @param {Object} data
         */
        openCMSBox: function(cellvalue, type, data){
            var $form = $("#L140M01OForm");
            $form.reset();
            $.ajax({
                handler: inits.fhandle,
                action: "queryL140M01O",
                data: {
                    tabFormMainId: $("#tabFormMainId").val(),
                    oid: data.oid
                },
                success: function(obj){
                    $form.injectData(obj);
                    $("#L140M01OBox").thickbox({
                        title: i18n.lms1405s02["btn.cms"],
                        width: 450,
                        height: 230,
                        modal: true,
                        align: "center",
                        valign: "bottom",
                        readOnly: _openerLockDoc == "1" || inits.toreadOnly,
                        i18n: i18n.def,
                        buttons: {
                            "sure": function(){
                                if ($form.valid()) {
                                    $.ajax({
                                        handler: inits.fhandle,
                                        action: "saveL140M01O",
                                        formId: "L140M01OForm",
                                        data: {
                                            tabFormMainId: $("#tabFormMainId").val(),
                                            oid: data.oid,
                                            payPercent: $form.find("#L140M01O_payPercent").val()
                                        },
                                        success: function(obj){
                                            CMSAction.setDrc(obj.drc);
                                            CMSAction.reloadGrid({}, CMSAction.grid);
                                            $.thickbox.close();
                                        }
                                    });
                                }
                            },
                            "cancel": function(){
                                $.thickbox.close();
                            }
                        }
                    });

					var isPayPercentReadonly = obj.L140M01O_collTyp1 == '01' ? true : false;
					$("#L140M01O_payPercent").prop("readonly", isPayPercentReadonly);
                }
            });
        },
        //J-108-0225_05097_B1001 Web e-Loan企金授信額度明細表新增設質予本行之總股數欄位與檢核
        openEditStockBox: function(cellvalue, options, data){
            var $form = $("#formFindStgaData");
            $form.reset();
    		
            var buttons = {
                "saveData": function(){
                    if ($form.valid()) {
                        var ctrlKind = $form.find('input:checkbox:checked[name="ctrlKind"]').val();
                        
                        if ($.trim(ctrlKind) == "" && $form.find('#stkNum').val() == "") {
                            // L140M01o.ctrlKind=控管方式
                            return CommonAPI.showErrorMessage(i18n.lms1405s02["L140M01a.error18"]+ "「"+ i18n.lms1405s02["L140M01o.stkNum"] +"」");
                        }
                        
                        if (ctrlKind != "2" && $form.find('#setRatio').val() == "") {
                            // L140M01o.setRatio=設質比率
                            return CommonAPI.showErrorMessage(i18n.lms1405s02["L140M01a.error18"]+ "「"+ i18n.lms1405s02["L140M01o.setRatio"] +"」");
                        }
                        
                        $.ajax({
                            handler: "lms1405m01formhandler",
                            action: "saveL140M01O_0307",
                            data: {
                            	tabFormMainId: $('#tabFormMainId').val(),//$("#mainId").val(),
                                oid: data.oid,
                                formFindStgaData: JSON.stringify($("#formFindStgaData").serializeData())
                            },
                            success: function(obj){
                            	
                            	CMSAction._reloadStockGrid({}, CMSAction.stockGrid);
                            	$form.find("#setRatio").val(obj.formFindStgaData.setRatio);
                                
                                //saveSuccess=儲存成功
                                CommonAPI.confirmMessage(i18n.def["saveSuccess"], function(b){
                                    if (b) {
                                        $.thickbox.close();
                                    }
                                });
                            }
                        });
                    }
                },
                "close": function(){
                    $.thickbox.close();
                }
            }
    		
            $.ajax({
                handler: "lms1405m01formhandler",
                action: "queryL140M01O_0307detail",
                data: {
                    tOid: data.oid,
                    mainId: responseJSON.mainId
                },
                success: function(obj){
                     
                    $form.injectData(obj);
  
                    $form.find('input:checkbox[name="ctrlKind"]').trigger("change");
                    
                    $("#divFindStgaData").thickbox({
                    	title: i18n.lms1405s02["title.23"], //設質比率查詢
            	        width: 750,
                        height: 400,
                        modal: true,
                        readOnly: _openerLockDoc == "1" || inits.toreadOnly,
                        i18n: i18n.def,
                        buttons: buttons
                    });
                }
            });
        },
        addCMSStock: function(){
        	var $form = $("#formFindStgaData");
            $form.reset();
    		
            var buttons = {
                "saveData": function(){
                    if ($form.valid()) {
                         var ctrlKind = $form.find('input:checkbox:checked[name="ctrlKind"]').val();
                        
                        if ($.trim(ctrlKind) == "" && $form.find('#stkNum').val() == "") {
                            // L140M01o.ctrlKind=控管方式
                            return CommonAPI.showErrorMessage(i18n.lms1405s02["L140M01a.error18"]+ "「"+ i18n.lms1405s02["L140M01o.stkNum"] +"」");
                        }
                        
                        if (ctrlKind != "2" && $form.find('#setRatio').val() == "") {
                            // L140M01o.setRatio=設質比率
                            return CommonAPI.showErrorMessage(i18n.lms1405s02["L140M01a.error18"]+ "「"+ i18n.lms1405s02["L140M01o.setRatio"] +"」");
                        }
                        
                        $.ajax({
                            handler: "lms1405m01formhandler",
                            action: "saveL140M01O_0307",
                            data: {
                            	tabFormMainId: $('#tabFormMainId').val(),//$("#mainId").val(),
                                oid: "",
                                formFindStgaData: JSON.stringify($("#formFindStgaData").serializeData())
                            },
                            success: function(obj){
                            	CMSAction._reloadStockGrid({}, CMSAction.stockGrid);
                            	$form.find("#setRatio").val(obj.formFindStgaData.setRatio);
                                //saveSuccess=儲存成功
                                CommonAPI.confirmMessage(i18n.def["saveSuccess"], function(b){
                                    if (b) {
                                        $.thickbox.close();
                                    }
                                });
                            }
                        });
                    }
                },
                "close": function(){
                    $.thickbox.close();
                }
            }
    		
            $("#divFindStgaData").thickbox({
            	title: i18n.lms1405s02["title.23"], //設質比率查詢
    	        width: 750,
                height: 400,
                modal: true,
                readOnly: _openerLockDoc == "1" || inits.toreadOnly,
                i18n: i18n.def,
                buttons: buttons
            });
            
            
        },
        deleteCMSStock: function(){
        	
        	var gridIDs = CMSAction.stockGrid.getGridParam('selarrrow');
            if (gridIDs == "") {
                //TMMDeleteError=請先選擇需修改(刪除)之資料列
                API.showMessage(i18n.def["TMMDeleteError"]);
                return false;
            }
            
            //confirmDelete=是否確定刪除?
            CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
                if (b) {
                    var gridsOid = [];
                    
                    for (var i = 0, total = gridIDs.length; i < total; i++) {
                        gridsOid[i] = CMSAction.stockGrid.getRowData(gridIDs[i]).oid;
                    }
                    $.ajax({
                        handler: inits.fhandle,
                        action: "deleteL140m01o_0307",
                        data: {
                            tabFormMainId: $("#tabFormMainId").val(),
                            oids: gridsOid
                        },
                        success: function(obj){
                        	CMSAction._reloadStockGrid({}, CMSAction.stockGrid);
                        }
                    });
                }
            });
 
        },
    	_reloadStockGrid:function(){	
    		CMSAction.stockGrid.jqGrid("setGridParam", {//重新設定grid需要查到的資料    	            
                postData: {
                    tabFormMainId: $("#tabFormMainId").val(),
                    mainId: $("#mainId").val()
                }
            }).trigger("reloadGrid");	        
    	},
        /**
         * J-108-0225_05097_B1001 Web e-Loan企金授信額度明細表新增設質予本行之總股數欄位與檢核
         * 
         * 查詢股票資料
         */
        findStgaStkNo: function(){
        	var $form = $("#formFindStgaData");
        	var stkNo = $.trim($form.find("#stkNo").val());
        	
        	$.ajax({
                handler: inits.fhandle,
                action: "findStgaStkNo",
                data: {
                	tabFormMainId: $('#tabFormMainId').val(),//$("#mainId").val(),
                	formFindStgaData: JSON.stringify($("#formFindStgaData").serializeData())
                },
                success: function(data){
                	$form.reset();
                	$form.injectData(data.formFindStgaData);
					if(data.formFindStgaData.ctrlKind == ""){
						$("[name=ctrlKind][value=2]"). attr("checked" , false );
					}else {
    					$("[name=ctrlKind][value="+data.formFindStgaData.ctrlKind+"]"). attr("checked" , true );	
					} 
					$form.find('input:checkbox[name="ctrlKind"]').trigger("change");
					
    				if(data.dataExist  != "Y" ){
    					//noData=查無資料，請重新查詢。
    					return CommonAPI.showErrorMessage(i18n.def["noData"]);
    				}
                    
                }
            }); 
            
        },
        /**
         * J-108-0225_05097_B1001 Web e-Loan企金授信額度明細表新增設質予本行之總股數欄位與檢核
         * 
         * 計算設質比率
         */
        caculateSetRatio: function(){
        	var stkNo = $.trim($("#formFindStgaData").find("#stkNo").val()); 
        	$.ajax({
                handler: inits.fhandle,
                action: "caculateSetRatio",
                data: {
                	tabFormMainId: $('#tabFormMainId').val(),//$("#mainId").val(),
                	formFindStgaData: JSON.stringify($("#formFindStgaData").serializeData())
                },
                success: function(data){
                	$("#formFindStgaData").find("#setRatio").val(data.setRatio); 
                }
            }); 
        },
        /**
         * J-108-0225_05097_B1001 Web e-Loan企金授信額度明細表新增設質予本行之總股數欄位與檢核
         * 
         * 引進前項擔保品建檔資料
         */
        inculdeCMSStock: function(){ 
        	var cmsCount = CMSAction.grid.jqGrid('getGridParam', 'records');
        	if (cmsCount <= 0) {
        		//grid.emptyrecords=查無資料
        		return CommonAPI.showErrorMessage(i18n.def["grid.emptyrecords"]);
        	}
        	
        	
        	var count = CMSAction.stockGrid.jqGrid('getGridParam', 'records');
        	if (count > 0) {
        		//L140M01a.message245=執行引進時會先刪除所有資料，是否確定執行？
        		CommonAPI.confirmMessage(i18n.lms1405s02["L140M01a.message245"], function(b){
                    if (b) {
                    	CMSAction.applyCMSStockData();
                    }
                });
        	}else{
        		CMSAction.applyCMSStockData();
        	}
            
        },
        /**
         * J-108-0225_05097_B1001 Web e-Loan企金授信額度明細表新增設質予本行之總股數欄位與檢核
         * 
         * 引進前項擔保品建檔資料
         */
        applyCMSStockData: function(){
       	 
        	$.ajax({
                handler: inits.fhandle,
                action: "applyCMSStockData",
                data: {
                	tabFormMainId: $('#tabFormMainId').val()
                },
                success: function(data){
                	 
                	CMSAction._reloadStockGrid({}, CMSAction.stockGrid);
                }
            }); 
            
        }
    };
}
initAll.done(function(inits){
    CMSAction.init(inits);
});
