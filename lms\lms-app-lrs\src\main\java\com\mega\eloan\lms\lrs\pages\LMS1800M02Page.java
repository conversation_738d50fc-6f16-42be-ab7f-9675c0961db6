package com.mega.eloan.lms.lrs.pages;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import tw.com.jcs.common.Util;
import org.springframework.ui.ModelMap;
import com.iisigroup.cap.component.PageParameters;

import tw.com.jcs.auth.AuthType;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LrsUtil;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.model.L180M01A;
import com.mega.eloan.lms.model.L180M01B;

@Controller
@RequestMapping("/lrs/lms1800m02/{page}")
public class LMS1800M02Page extends AbstractEloanForm {
	
	@Autowired
	RetrialService retrialService;
	
	public LMS1800M02Page() {
		super();
	}

	@Override
	public void execute(ModelMap model, PageParameters params) throws Exception {
		super.execute(model, params);
		// 依權限設定button
		addAclLabel(model, new AclLabel("_btnDOC_EDITING", params, getDomainClass(),
				AuthType.Modify, RetrialDocStatusEnum.編製中));
		
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L180M01B data = retrialService.findL180M01B_oid(mainOid);
		if(data==null){
			data = new L180M01B();
		}
		L180M01A meta = retrialService.findL180M01A(data);
		if(meta==null){
			meta = new L180M01A();
		}
		
		boolean isCompiling = Util.equals(RetrialDocStatusEnum.編製中.getCode(), meta.getDocStatus());
		model.addAttribute("show_projectNo", !isCompiling );
		model.addAttribute("hide_projectNo", isCompiling );
		
		if(Util.isEmpty(Util.trim(data.getNewNCkdFlag()))){
			model.addAttribute("show_newNCkd_8", false);
			model.addAttribute("show_newNCkd_N8", false);
			
		}else{
			boolean show_newNCkd_8 = Util.equals(LrsUtil.NCKD_8_本次暫不覆審, data.getNewNCkdFlag())
				&& (Util.equals("SYS", data.getCreateBY()));
			
			model.addAttribute("show_newNCkd_8", show_newNCkd_8);
			model.addAttribute("show_newNCkd_N8", !show_newNCkd_8);	
		}
		model.addAttribute("show_newLRDate", CrsUtil.isNOT_null_and_NOTZeroDate(data.getNewLRDate()));
		
		renderJsI18N(LMS1800M01Page.class);
		renderJsI18N(LMS1800M02Page.class);
	}
	
	@Override
	public Class<? extends Meta> getDomainClass() {
		return L180M01A.class;
	}
	
	@Override
	protected String getContentPageName() {
		return "lrs/pages/LMS1800M02Page";
	}
}
