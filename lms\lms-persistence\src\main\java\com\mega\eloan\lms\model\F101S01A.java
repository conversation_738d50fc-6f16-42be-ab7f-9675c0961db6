package com.mega.eloan.lms.model;

import java.io.Serializable;
import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.enums.FssQuickFlagEnum;
import com.mega.eloan.lms.enums.FssTabEnum;

/**
 * <pre>
 * 財務三表:資產 損益 現流 - The persistent class for the F101S01A database table.
 * </pre>
 * 
 * @since 2011/7/26
 * <AUTHOR> Wang
 * @version <ul>
 *          <li>2011/7/26,Sunkist Wang,new</li>
 *          <li>2011/7/26,Sunkist Wang,update directional</li>
 *          <li>2011/7/29,Sunkist Wang,update rename pidF101M04A</li>
 *          </ul>
 */
@NamedEntityGraph(name = "F101S01A-entity-graph", attributeNodes = { @NamedAttributeNode("f101m01a") })
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "F101S01A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class F101S01A extends com.mega.eloan.common.model.RelativeMeta
		implements Serializable, tw.com.iisi.cap.model.IDataObject, IDocObject {
	private static final long serialVersionUID = 1L;

	@Column(precision = 15)
	private BigDecimal amt;

	@Column(length = 32)
	private String pidF101M04A;

	@Column(length = 1)
	private String quickflag;

	@Column(precision = 11, scale = 2)
	private BigDecimal ratio;

	@Column(length = 60)
	private String subName;

	@Column(length = 8)
	private String subNo;

	@Column(length = 1)
	private String tab;

	// bi-directional many-to-one association to F101M01A
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumns({
			@JoinColumn(name = "MAINID", referencedColumnName = "MAINID", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "PID", referencedColumnName = "UID", nullable = false, insertable = false, updatable = false)
			})
	private F101M01A f101m01a;

	public F101S01A() {
	}

	public BigDecimal getAmt() {
		return this.amt;
	}

	public void setAmt(BigDecimal amt) {
		this.amt = amt;
	}

	public String getPidF101M04A() {
		return pidF101M04A;
	}

	public void setPidF101M04A(String pidF101M04A) {
		this.pidF101M04A = pidF101M04A;
	}

	public String getQuickflag() {
		return this.quickflag;
	}

	public void setQuickflag(String quickflag) {
		this.quickflag = quickflag;
	}

	public void setQuickflag(FssQuickFlagEnum quickflag) {
		this.quickflag = quickflag.getCode();
	}

	public BigDecimal getRatio() {
		return this.ratio;
	}

	public void setRatio(BigDecimal ratio) {
		this.ratio = ratio;
	}

	public String getSubName() {
		return this.subName;
	}

	public void setSubName(String subName) {
		this.subName = subName;
	}

	public String getSubNo() {
		return this.subNo;
	}

	public void setSubNo(String subNo) {
		this.subNo = subNo;
	}

	public String getTab() {
		return this.tab;
	}

	public void setTab(String tab) {
		this.tab = tab;
	}

	public void setTab(FssTabEnum fssTabEnum) {
		this.tab = fssTabEnum.getCode();
	}

	public F101M01A getF101m01a() {
		return f101m01a;
	}

	public void setF101m01a(F101M01A f101m01a) {
		this.f101m01a = f101m01a;
	}

	/* (non-Javadoc)
	 * @see java.lang.Object#equals(java.lang.Object)
	 */
	@Override
	public boolean equals(Object o) {
		
		if (!(o instanceof F101S01A)) {
			return false;
		}
		
		if (o == this) {
			return true;
		}
		
		F101S01A otherObj = (F101S01A) o;
		
		return new EqualsBuilder()
			.append(this.getMainId(), otherObj.getMainId())
			.append(this.getTab(), otherObj.getTab())
			.append(StringUtils.trimToEmpty(this.getSubNo()), StringUtils.trimToEmpty(otherObj.getSubNo()))
			.isEquals();
	}

	/* (non-Javadoc)
	 * @see java.lang.Object#hashCode()
	 */
	@Override
	public int hashCode() {
		return new HashCodeBuilder()
			.append(this.getMainId())
			.append(this.getTab())
			.append(this.getSubNo()).toHashCode();
	}	
}