package com.mega.eloan.lms.lns.handler.form;

import java.io.IOException;
import java.io.InputStream;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.multipart.MultipartFile;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.response.MegaErrorResult;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.utils.CustomerIdCheckUtil;
import com.mega.eloan.lms.base.common.BranchRate;
import com.mega.eloan.lms.base.common.Rate;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.model.L140M01S;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import jxl.Cell;
import jxl.CellType;
import jxl.DateCell;
import jxl.Sheet;
import jxl.Workbook;
import jxl.read.biff.BiffException;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.handler.FileUploadHandler;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapMath;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 評等對照 上傳
 * </pre>
 * 
 * @since 2016/4/12
 * <AUTHOR>
 * @version <ul>
 *          <li>2016/4/12,EL08034,new
 *          </ul>
 */
@Scope("request")
@Controller("lms1401fileuploadhandler")
public class LMS1401FileUploadHandler extends FileUploadHandler {

	@Autowired
	DocFileService fileService;

	@Resource
	LMSService lmsService;
	@Resource
	BranchService branchSrv;
	// @Resource
	// LMS1401Service lms1401Service;
	@Resource
	MisCustdataService misCustdataService;

	@Override
	public IResult afterUploaded(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// Properties pop = MessageBundleScriptCreator
		// .getComponentResource(LMS1401S02Page.class);

		MultipartFile uFile = params.getFile(params.getString("fieldId"));

		String mainId = params.getString("tabFormMainId");
		String type = params.getString("type");

		boolean isGetImgDimension = params.getBoolean("getImgDimension");
		String sysId = params.getString("sysId", fileService.getSysId());

		// 設定上傳檔案資訊
		String fileName = uFile.getName();
		String fieldId = Util.trim(params.getString("fieldId"));

		if (params.containsKey("fileSize")) {
			if (uFile.getSize() > params.getLong("fileSize", 1048576)) {
				// EFD0063=ERROR|上送的檔案已超過$\{fileSize\}M的限制大小，無法執行上傳動作。|
				Map<String, String> msg = new HashMap<String, String>();
				msg.put("fileSize",
						CapMath.divide(params.getString("fileSize"), "1048576")); // 1M*1024*1024
				MegaErrorResult result = new MegaErrorResult();
				result.putError(params, new CapMessageException(RespMsgHelper.getMessage("EFD0063", msg), getClass()));
				return result;
			}
		}

		// 開始匯入EXCEL****************************************************************************

		// 一律以國內MIS TABLE為主
		BranchRate branchRate = lmsService.getBranchRate("005");
		HashMap<String, Rate> rateMap = branchRate.getMisRateMap();

		Workbook workbook = null;
		String errMsg = "";
		InputStream is = null;
		String fileKey = "";
		boolean findError = false;

		int[] dimension = { -1, -1 };
		try {

			is = uFile.getInputStream();
			workbook = Workbook.getWorkbook(is);
			Sheet sheet = workbook.getSheet(0);
			int totalCol = sheet.getColumns();
			if (totalCol == 0) {
				// L120S09a.message14=匯入之黑名單EXCEL格式錯誤。
				throw new CapMessageException("匯入之黑名單EXCEL格式錯誤。", getClass());
			}

			List<L140M01S> newl140m01ss = new ArrayList<L140M01S>();
			HashMap<String, String> importCustId = new HashMap<String, String>();
			int maxItemSeq = 0;
			for (int row = 1; row < sheet.getRows(); row++) {
				int column = 0;

				String custId = "";
				String dupNo = "";
				String custName = "";
				String curr = "";
				String factAmt = "";
				String custId2 = "";
				String dupNo2 = "";
				String ratio = "";

				String memo1 = "";
				String memo2 = "";
				String chkYN = "";
				String country = "";

				if (++column <= totalCol) {
					custId = StringUtils.upperCase(Util.trim(getContents(sheet
							.getCell(column - 1, row))));
				}

				if (++column <= totalCol) {
					dupNo = Util.trim(getContents(sheet
							.getCell(column - 1, row)));
					if (Util.equals(dupNo, "")) {
						dupNo = "0";
					}
				}

				if (++column <= totalCol) {
					custName = Util.toSemiCharString(Util
							.trim(getContents(sheet.getCell(column - 1, row))));
				}

				if (++column <= totalCol) {
					curr = Util.toSemiCharString(Util.trim(getContents(sheet
							.getCell(column - 1, row))));
				}

				if (++column <= totalCol) {
					factAmt = Util.toSemiCharString(Util.trim(getContents(sheet
							.getCell(column - 1, row))));
				}

				if (++column <= totalCol) {
					custId2 = Util.toSemiCharString(Util.trim(getContents(sheet
							.getCell(column - 1, row))));
				}

				if (++column <= totalCol) {
					dupNo2 = Util.toSemiCharString(Util.trim(getContents(sheet
							.getCell(column - 1, row))));
				}

				if (++column <= totalCol) {
					ratio = Util.toSemiCharString(Util.trim(getContents(sheet
							.getCell(column - 1, row))));
				}

				if (++column <= totalCol) {
					memo1 = Util.toSemiCharString(Util.trim(getContents(sheet
							.getCell(column - 1, row))));
				}

				if (++column <= totalCol) {
					memo2 = Util.toSemiCharString(Util.trim(getContents(sheet
							.getCell(column - 1, row))));
				}

				// J-107-0248_05097_B1001 Web e-Loan企金授信管理系統AML/CFT增加國別管制名單掃描功能
				if (++column <= totalCol) {
					country = Util.toSemiCharString(Util.trim(getContents(sheet
							.getCell(column - 1, row))));
				}

				// 檢核EXCEL內容**********************************************************************************
				StringBuffer custErrMsg = new StringBuffer("");

				String custKey = custId + "-" + dupNo;
				if (importCustId.containsKey(custKey)) {
					// L140M01a.message195=「{0}」統編已存在

					custErrMsg.append("統編資料重複");
					custErrMsg.append(";");

				} else {
					importCustId.put(custKey, "");
				}

				// L140M01a.message197=「{0}」欄位內容錯誤。
				// custErrMsg.append(MessageFormat.format(
				// pop.getProperty("L140M01a.message197"),
				// pop.getProperty("L140M01S.custId")));

				if (Util.notEquals(custId, "")) {
					if (StringUtils.length(custId) > 10) {
						custErrMsg.append("買方統一編號長度錯誤");
						custErrMsg.append(";");

					}
				} else {
					custErrMsg.append("買方統一編號欄位空白");
					custErrMsg.append(";");
				}

				// 重覆序號
				if (Util.notEquals(dupNo, "")) {
					if (StringUtils.length(dupNo) != 1) {
						custErrMsg.append("買方重覆序號長度錯誤");
						custErrMsg.append(";");
					}
				}

				Map<String, Object> cust = null;
				if (Util.equals(custErrMsg.toString(), "")) {
					// ID沒問題才要產生資料
					// 0024是否存在

					cust = misCustdataService.findAllByByCustIdAndDupNo(custId,
							dupNo);
					if (cust == null || cust.isEmpty()) {
						custErrMsg.append("0024不存在");
						custErrMsg.append(";");
					}
				}

				// 幣別
				if (Util.notEquals(curr, "")) {
					if (StringUtils.length(curr) != 3) {
						custErrMsg.append("額度幣別長度錯誤");
						custErrMsg.append(";");
					} else {
						if (Util.notEquals(curr, "TWD")
								&& !rateMap.containsKey(curr)) {
							custErrMsg.append("匯率檔無" + curr + "幣別資料");
							custErrMsg.append(";");
						}

					}
				} else {
					custErrMsg.append("額度幣別欄位空白");
					custErrMsg.append(";");
				}

				// 額度金額
				if (Util.equals(factAmt, "")) {
					custErrMsg.append("額度金額欄位空白");
					custErrMsg.append(";");
				}

				if (!Util.isNumeric(getBigDecimalString(factAmt))) {
					custErrMsg.append("額度金額必須為數字");
					custErrMsg.append(";");
				}

				// 預支成數
				if (Util.equals(ratio, "")) {
					custErrMsg.append("預支成數欄位空白");
					custErrMsg.append(";");
				}
				if (!Util.isNumeric(getBigDecimalString(ratio))) {
					custErrMsg.append("預支成數必須為數字");
					custErrMsg.append(";");
				}

				// J-107-0248_05097_B1001 Web e-Loan企金授信管理系統AML/CFT增加國別管制名單掃描功能
				// 國別
				if (Util.notEquals(country, "")) {
					if (StringUtils.length(country) != 2) {
						custErrMsg.append("國別長度錯誤");
						custErrMsg.append(";");
					}
				}

				if (Util.notEquals(custErrMsg.toString(), "")) {
					findError = true;
					importCustId.put(custKey, custErrMsg.toString());
				}

				// 儲存L140M01S**********************************************************************************

				if (Util.equals(custErrMsg.toString(), "")) {
					// 沒問題才要產生資料

					L140M01S lms140m01s = null;
					lms140m01s = new L140M01S();

					lms140m01s.setMainId(mainId);
					lms140m01s.setType(type);
					maxItemSeq = maxItemSeq + 1;
					lms140m01s.setItemSeq(maxItemSeq);

					lms140m01s.setCustId(custId);
					lms140m01s.setDupNo(dupNo);

					if (cust != null && !cust.isEmpty()) {
						String cname = (String) MapUtils.getString(cust,
								"CNAME");
						cname = Util.toSemiCharString(cname);
						String ename = (String) MapUtils.getString(cust,
								"ENAME");
						String finalName = CustomerIdCheckUtil.getName(custId,
								cname, ename);
						lms140m01s
								.setCustName(Util.toSemiCharString(finalName));
					} else {
						lms140m01s.setCustName(Util.toSemiCharString(custName));
					}

					lms140m01s.setCurr(curr);
					lms140m01s.setFactAmt(CapMath.getBigDecimal(factAmt));
					lms140m01s.setCustId2(custId2);
					lms140m01s.setDupNo2(dupNo2);
					lms140m01s.setRatio(CapMath.getBigDecimal(ratio));
					lms140m01s.setRefRate("");
					lms140m01s.setMemo1(memo1);
					lms140m01s.setMemo2(memo2);
					lms140m01s.setChkYN("Y");
					lms140m01s.setCreateTime(CapDate.getCurrentTimestamp());
					lms140m01s.setCreator(user.getUserId());
					// J-107-0248_05097_B1001 Web
					// e-Loan企金授信管理系統AML/CFT增加國別管制名單掃描功能
					lms140m01s.setCountry(country);
					newl140m01ss.add(lms140m01s);// 將修改過的資料放入ArrayList的陣列中
				}
			}

			// 引進名單結束************************************************************************************************************************************

			if (findError) {
				// L140M01a.message205=資料匯入失敗，錯誤如下:<br>{0}
				StringBuffer allError = new StringBuffer("");

				for (String errCustKey : importCustId.keySet()) {
					String errCustId = errCustKey.split("-")[0];
					String errDupNo = errCustKey.split("-")[1];
					String errCustMsg = importCustId.get(errCustKey);
					if (Util.notEquals(errCustMsg, "")) {
						allError.append(errCustId).append(errDupNo).append("：")
								.append(errCustMsg).append("<br>");
					}

				}

				workbook.close();

				// throw new CapMessageException(MessageFormat.format(
				// pop.getProperty("L140M01a.message205"),
				// allError.toString()), getClass());

				throw new CapMessageException(MessageFormat.format(
						"資料匯入失敗，錯誤如下:<br>{0}", allError.toString()), getClass());

			} else {

				lmsService.deleteL140m01sAll(mainId, type);

				if (newl140m01ss != null && !newl140m01ss.isEmpty()) {
					lmsService.saveL140m01sList(newl140m01ss);
				}
			}

			workbook.close();

		} catch (IOException e) {
			logger.error(e.getMessage(), e);
			throw new CapMessageException("file IO ERROR", getClass());
		} catch (BiffException be) {
			logger.error(be.getMessage(), be);
			throw new CapMessageException("file IO ERROR", getClass());
		} finally {
			if (is != null) {
				try {
					is.close();
					if (workbook != null) {
						workbook.close();
						workbook = null;
					}
				} catch (IOException e) {
					logger.debug("inputStream close Error", getClass());
				}
			}

		}

		if (Util.isNotEmpty(errMsg)) {
			throw new CapMessageException(errMsg, getClass());
		}

		return new CapAjaxFormResult().set("url", "file?id=" + fileKey)
				.set("fileKey", fileKey).set("imgWidth", dimension[0])
				.set("imgHeight", dimension[1]);

	}

	private String getContents(Cell cell) {
		DateCell dCell = null;
		if (cell.getType() == CellType.DATE) {
			dCell = (DateCell) cell;
			// System.out.println("Value of Date Cell is: " + dCell.getDate());
			// ==> Value of Date Cell is: Thu Apr 22 02:00:00 CEST 2088
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
			// System.out.println(sdf.format(dCell.getDate()));
			// ==> 2088-04-22
			return sdf.format(dCell.getDate());
		}
		// possibly manage other types of cell in here if needed for your goals
		// read more:
		// http://www.quicklyjava.com/reading-excel-file-in-java-datatypes/#ixzz2fYIkHdZP
		return cell.getContents();
	}

	/**
	 * 將字串轉為BigDecimal格式
	 * 
	 * @param in
	 *            the input
	 * @return BigDecimal
	 */
	public String getBigDecimalString(String in) {

		char[] ca = in.toCharArray();
		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < ca.length; i++) {
			switch (ca[i]) {
			case '-':
			case '+':
			case '0':
			case '1':
			case '2':
			case '3':
			case '4':
			case '5':
			case '6':
			case '7':
			case '8':
			case '9':
			case '.':
				sb.append(ca[i]);
			}
		}
		return sb.toString();
	}
}
