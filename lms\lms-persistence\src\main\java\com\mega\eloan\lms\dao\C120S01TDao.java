/* 
 * C120S01TDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C120S01T;

/** 個金簡化簽報書借款人主檔 **/
public interface C120S01TDao extends IGenericDao<C120S01T> {

	C120S01T findByOid(String oid);
	
	List<C120S01T> findByMainId(String mainId);
	
	List<C120S01T> findByMainIdOrderBySeqNo(String mainId);
	
	C120S01T findByUniqueKey(String mainId, String ownBrId, String custId, String dupNo);

	List<C120S01T> findByIndex01(String mainId, String ownBrId, String custId, String dupNo);

	List<C120S01T> findByIndex02(String mainId, String ownBrId, String custId, String dupNo, Integer seqNo);

	void deleteByMainId(String mainid);
	
	public List<C120S01T> findByList(String mainId, String custId, String dupNo);
}