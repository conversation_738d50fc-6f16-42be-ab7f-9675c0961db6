/* 
 *LMS7820R01RptServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.report.impl;

import java.text.DecimalFormat;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.AbstractReportService;
import com.mega.eloan.lms.lms.service.LMS7820Service;
import com.mega.eloan.lms.model.L782M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.ReportGenerator;

/**
 * <pre>
 * 特殊案件登記彙總表
 * </pre>
 * 
 * @since 2012/2/9
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/2/9,REX,new
 *          </ul>
 */
@Service("lms7820r01rptservice")
public class LMS7820R01RptServiceImpl extends AbstractReportService {

	@Resource
	LMS7820Service lms7820Service;

	@Resource
	BranchService branchService;

	@Resource
	CodeTypeService codeTypeService;

	private static ThreadLocal<DecimalFormat> dfMoney = new ThreadLocal<DecimalFormat>();
	private static ThreadLocal<DecimalFormat> dfRate = new ThreadLocal<DecimalFormat>();

	@Override
	public String getReportTemplateFileName() {
		Locale locale = null;
		// zh_TW: 正體中文
		// zh_CN: 簡體中文
		// en_US: 英文
		locale = LocaleContextHolder.getLocale();
		if (locale == null)
			locale = Locale.getDefault();
		return "report/lms/LMS7820R01_" + locale.toString() + ".rpt";
	}

	/*
	 * (non-Javadoc) 設定需要傳入RPT參數
	 * 
	 * @see
	 * com.mega.eloan.lms.base.service.AbstractReportService#setReportData(com
	 * .mega.eloan.lms.base.report.ReportGenerator,
	 * org.apache.wicket.PageParameters)
	 */
	@Override
	public void setReportData(ReportGenerator reportTools, PageParameters params) {

		List<L782M01A> l782m01a = null;
		Locale locale = null;
		
		String releaseDateS = params.getString("releaseDateS", "");
		String releaseDateE = params.getString("releaseDateE", "");
		
		try {

			locale = LocaleContextHolder.getLocale();
			if (locale == null) {
				locale = Locale.getDefault();
			}
			dfMoney.set(new DecimalFormat("#,###,###,###,##0"));
			dfRate.set(new DecimalFormat("#,###,###,###,##0.00"));

			String[] codeType = { UtilConstants.CodeTypeItem.授信科目 };
			Map<String, CapAjaxFormResult> codeMap = codeTypeService
					.findByCodeType(codeType, locale.toString());
			Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
			List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			l782m01a = lms7820Service.findByAll(user.getUnitNo(),releaseDateS,releaseDateE);
			rptVariableMap.put("QueryDate",
					Util.getDate(CapDate.getCurrentTimestamp()));
			titleRows = this.setL782M01ADataList(titleRows, locale, l782m01a,
					codeMap);
			reportTools.setLang(locale);
			reportTools.setVariableData(rptVariableMap);
			reportTools.setRowsData(titleRows);
			// 　local用
			// reportTools.setTestMethod(true);
		}finally{
			
		}
	}

	/**
	 * 設定L782M01A資料
	 * 
	 * @param titleRows
	 *            多值MAP
	 * @param list
	 *            L161S01B List
	 * @return titleRows 多值MAP
	 */
	private List<Map<String, String>> setL782M01ADataList(
			List<Map<String, String>> titleRows, Locale locale,
			List<L782M01A> list, Map<String, CapAjaxFormResult> codeMap) {
		// F代表第一次重覆 前面資料都要先印出來 之後才印重複資料(Y) 重複資料印完後才印後面的資料(N)
		Map<String, String> mapInTitleRows = null;
		StringBuffer temp = new StringBuffer();
		for (L782M01A l782m01a : list) {
			mapInTitleRows = Util.setColumnMap();
			mapInTitleRows.put("ReportBean.column02",
					Util.getDate(l782m01a.getDispatchDate()));
			mapInTitleRows.put("ReportBean.column03", LMSUtil.concat(temp,
					l782m01a.getCaseBrId(), "",
					branchService.getBranchName(l782m01a.getCaseBrId())));

			mapInTitleRows.put(
					"ReportBean.column04",
					(String) codeMap.get(UtilConstants.CodeTypeItem.授信科目).get(
							l782m01a.getLoanTP()));
			mapInTitleRows.put("ReportBean.column05",
					Util.nullToSpace(l782m01a.getInteRate()));
			mapInTitleRows.put(
					"ReportBean.column06",
					LMSUtil.concat(temp, l782m01a.getCustId(), " ",
							l782m01a.getDupNo()));
			mapInTitleRows.put("ReportBean.column07", l782m01a.getCustName());
			mapInTitleRows.put("ReportBean.column08", l782m01a.getDisp1());
			titleRows.add(mapInTitleRows);
		}
		return titleRows;
	}

}
