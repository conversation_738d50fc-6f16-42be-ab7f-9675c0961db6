
package com.mega.eloan.lms.cls.pages;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.html.EloanPageFragment;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.sso.context.MegaSSOSecurityContext;

import tw.com.jcs.auth.AuthService;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 消金契約書- 編製中
 * </pre>
 */
@Controller
@RequestMapping("/cls/cls3401v01")
public class CLS3401V01Page extends AbstractEloanInnerView {

	@Autowired
	CLSService clsService;

	@Autowired
	AuthService au;

	@Override
	public void execute(ModelMap model, PageParameters params) {
		setGridViewStatus(CreditDocStatusEnum.海外_編製中);

		//========================
		Set<String> eloanRoles = MegaSSOSecurityContext.getEloanRoles();
		String pgmDept = MegaSSOSecurityContext.getPGMDept();
		int transactionCode = Util.parseInt(params
				.getString(EloanConstants.TRANSACTION_CODE));
		
//		boolean _Query = au.auth(pgmDept, eloanRoles, transactionCode,
//				AuthType.Query);
		boolean _Accept = au.auth(pgmDept, eloanRoles, transactionCode,
				AuthType.Accept);
		boolean _Modify = au.auth(pgmDept, eloanRoles, transactionCode,
				AuthType.Modify);
		
		// 加上Button
		List<EloanPageFragment> list = new ArrayList<EloanPageFragment>();
		// 主管跟經辦都會出現的按鈕
		list.add(LmsButtonEnum.View);
		if(_Accept||_Modify){
			list.add(LmsButtonEnum.Add);
			list.add(LmsButtonEnum.Delete);
		}
		list.add(LmsButtonEnum.Filter);
//		btns.add(CreditButtonEnum.PrintNote);
		
		// 只有主管出現的按鈕
		if (this.getAuth(AuthType.Accept)) {
			
		}
		// 只有經辦出現的按鈕
		if (this.getAuth(AuthType.Modify)) {
			
		}
		addToButtonPanel(model, list);

		model.addAttribute("ctrTypeB_area",
				clsService.is_function_on_codetype("ctrTypeB_on"));

		model.addAttribute("ctrTypeL_area",
				clsService.is_function_on_codetype("ctrTypeL_on"));
		
		model.addAttribute("ctrTypeB_area_visible",true);
		model.addAttribute("ctrTypeL_area_visible",true);
		
		
		
		renderJsI18N(CLS3401V01Page.class);
		renderJsI18N(CLS3401M01Page.class);

		// UPGRADE: 待確認JavaScript有無正確讀取
		model.addAttribute("loadScript",
				"loadScript('pagejs/cls/CLS3401V01Page');");
	}

}
