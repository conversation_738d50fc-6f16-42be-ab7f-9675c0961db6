/* 
 * C160S02A.java
 */

package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import org.apache.bval.constraints.NotEmpty;

import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.lms.validation.group.Check2;

/** 中鋼整批評等檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "C160M02A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class C160M02A extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * 案件號碼-年度
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	@Digits(integer = 4, fraction = 0, groups = Check.class)
	@Column(name = "CASEYEAR", columnDefinition = "DECIMAL(4,0)")
	private Integer caseYear;

	/**
	 * 案件號碼-流水號
	 * <p/>
	 * 100/09/27調整<br/>
	 * 資料來源：案件簽報書
	 */
	@Digits(integer = 5, fraction = 0, groups = Check.class)
	@Column(name = "CASESEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer caseSeq;

	/**
	 * 案件號碼
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	@NotNull(message = "{required.message}", groups = Check2.class)
	@NotEmpty(message = "{required.message}", groups = Check2.class)
	@Size(max = 62)
	@Column(name = "CASENO", length = 62, columnDefinition = "VARCHAR(62)")
	private String caseNo;

	/**
	 * 簽案日期
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "CASEDATE", columnDefinition = "DATE")
	private Date caseDate;

	/**
	 * 批號
	 * <p/>
	 * caseType =2<br/>
	 * caseType =3
	 */
	@Size(max = 10)
	@Column(name = "PACKNO", length = 10, columnDefinition = "VARCHAR(10)")
	private String packNo;

	/**
	 * Excel ID
	 * <p/>
	 * Excel file id
	 */
	@Size(max = 32)
	@Column(name = "EXCELID", length = 32, columnDefinition = "CHAR(32)")
	private String excelId;

	/** 完成筆數 **/
	@Digits(integer = 4, fraction = 0, groups = Check.class)
	@Column(name = "FINCOUNT", columnDefinition = "DECIMAL(4,0)")
	private Integer finCount;

	/** 總筆數 **/
	@Digits(integer = 4, fraction = 0, groups = Check.class)
	@Column(name = "TOTCOUNT", columnDefinition = "DECIMAL(4,0)")
	private Integer totCount;

	/** 經辦 **/
	@Size(max = 6)
	@Column(name = "APPRID", length = 6, columnDefinition = "CHAR(6)")
	private String apprId;
	
	/**匯入EXCEL批次執行狀態
	 * 01-執行中
	 * 02-計算信評成功
	 * 03-計算信評失敗
	 * 04-更新外部系統查詢結果成功
	 * 05-更新外部系統查詢結果失敗
	 * **/
	@Size(max = 2)
	@Column(name = "IMPORTEXCELSTATUS", length = 2, columnDefinition = "VARCHAR(2)")
	private String importExcelStatus;

	public Integer getCaseYear() {
		return caseYear;
	}

	public void setCaseYear(Integer caseYear) {
		this.caseYear = caseYear;
	}

	public Integer getCaseSeq() {
		return caseSeq;
	}

	public void setCaseSeq(Integer caseSeq) {
		this.caseSeq = caseSeq;
	}

	public String getCaseNo() {
		return caseNo;
	}

	public void setCaseNo(String caseNo) {
		this.caseNo = caseNo;
	}

	public Date getCaseDate() {
		return caseDate;
	}

	public void setCaseDate(Date caseDate) {
		this.caseDate = caseDate;
	}

	public String getPackNo() {
		return packNo;
	}

	public void setPackNo(String packNo) {
		this.packNo = packNo;
	}

	public Integer getFinCount() {
		return finCount;
	}

	public void setFinCount(Integer finCount) {
		this.finCount = finCount;
	}

	public Integer getTotCount() {
		return totCount;
	}

	public void setTotCount(Integer totCount) {
		this.totCount = totCount;
	}

	public String getApprId() {
		return apprId;
	}

	public void setApprId(String apprId) {
		this.apprId = apprId;
	}

	public String getExcelId() {
		return excelId;
	}

	public void setExcelId(String excelId) {
		this.excelId = excelId;
	}

	public String getImportExcelStatus() {
		return importExcelStatus;
	}

	public void setImportExcelStatus(String importExcelStatus) {
		this.importExcelStatus = importExcelStatus;
	}

}
