/* 
 *MisELF501Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.mfaloan.service;

import java.util.List;
import java.util.Map;

import com.mega.eloan.lms.mfaloan.bean.ELF501;

/**
 * <pre>
 * 消金額度介面檔 MIS.ELF501
 * </pre>
 * 
 * @since 2013/1/17
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/1/17,REX,new
 *          </ul>
 */
public interface MisELF501Service {

	/**
	 * 查詢額度相關資料
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @param cntrNo
	 *            額度序號
	 * @return
	 */
	public List<ELF501> findByKey(String custId, String dupNo, String cntrNo);

	public ELF501 findByUniqueKey1(String custId, String dupNo, String cntrNo,
			Integer seq);

	public ELF501 findByUniqueKey2(String custId, String dupNo, String cntrNo,
			String LoanNo);

	/**
	 * 
	 * 檢查產品58
	 * 
	 * @param company_id
	 *            同一事業體資訊
	 * @return
	 */
	public Map<String, Object> CheckProdKind58(String company_id);

	/**
	 * 檢查產品 60
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @return
	 */
	public Map<String, Object> checkProdKind60(String custId, String dupNo);
	
}
