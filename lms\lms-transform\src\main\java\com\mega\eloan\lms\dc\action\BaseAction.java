package com.mega.eloan.lms.dc.action;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.mega.eloan.lms.dc.conf.ConfigData;
import com.mega.eloan.lms.dc.conf.MainConfig;

public class BaseAction {
	protected Logger logger = LoggerFactory.getLogger(BaseAction.class);

	protected ConfigData configData = null;

	public BaseAction() {
		this.configData = MainConfig.getInstance().getConfig();
	}

	/**
	 * 設定DataBean
	 * 
	 * @param bean
	 *            DataBean
	 */
	public void setConfigData(ConfigData config) {
		if (config != null) {
			this.configData = config;
		}
	}

	/**
	 * 取得Config DataBean
	 * 
	 * @return Config DataBean
	 */
	public ConfigData getConfigData() {
		return this.configData;
	}

}
