/* 
 * C101S01HDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.C101S01HDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C101S01H;


/** 個金徵信訊息紀錄表 **/
@Repository
public class C101S01HDaoImpl extends LMSJpaDao<C101S01H, String>
	implements C101S01HDao {

	@Override
	public C101S01H findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C101S01H> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<C101S01H> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public C101S01H findByUniqueKey(String mainId, String custId, String dupNo){
		ISearch search = createSearchTemplete();
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<C101S01H> findByIndex01(String mainId, String custId, String dupNo){
		ISearch search = createSearchTemplete();
		List<C101S01H> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
	@Override
	public List<C101S01H> findByCustIdDupId(String custId,String DupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", DupNo);
		List<C101S01H> list = createQuery(C101S01H.class,search).getResultList();
		return list;
	}
	
	@Override
	public int deleteByOid(String oid) {
		Query query = entityManager.createNamedQuery("C101S01H.deleteOid");
		query.setParameter("OID", oid);
		return query.executeUpdate();
	}
	
	@Override
	public C101S01H findBy(String custId, String dupNo, String txid, String prodid, String qBranch, String qDate, String qEmpCode){
		ISearch search = createSearchTemplete();
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (txid != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "txid", txid);
		if (prodid != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "prodid", prodid);
		if (qBranch != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "qBranch", qBranch);
		if (qDate != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "qDate", qDate);
		if (qEmpCode != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "qEmpCode", qEmpCode);
		
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			return findUniqueOrNone(search);
		}
		
		return null;
	}
}