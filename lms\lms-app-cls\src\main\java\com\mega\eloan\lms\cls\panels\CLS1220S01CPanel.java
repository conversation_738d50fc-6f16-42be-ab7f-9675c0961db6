/* 
 * CMS1301S01Panel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.DocLogPanel;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.flow.enums.CLSDocStatusEnum;
import com.mega.eloan.lms.cls.pages.CLS1220M02Page;
import com.mega.eloan.lms.cls.pages.CLS1220M03Page;
import com.mega.eloan.lms.cls.pages.CLS1220V01Page;
import com.mega.eloan.lms.model.C122M01A;

import tw.com.jcs.common.Util;

/**
 * <pre>
 * 線上信貸
 * </pre>
 * 
 * @since 2019/6/6
 * <AUTHOR>
 * @version <ul>
 *          <li>2019/6/6,EL08034,new
 *          </ul>
 */
public class CLS1220S01CPanel extends Panel {

	private static final long serialVersionUID = 1L;

	private C122M01A meta;

	private boolean boolVal;

	/**
	 * @param id
	 */
	public CLS1220S01CPanel(String id) {
		super(id);
	}

	/**
	 * @param id
	 * @param updatePanelName
	 * @param meta
	 * @param boolVal
	 */
	public CLS1220S01CPanel(String id, boolean updatePanelName, C122M01A meta,
			boolean boolVal) {
		super(id, updatePanelName);
		this.meta = meta;
		this.boolVal = boolVal;
	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);
		renderJsI18N(CLS1220M02Page.class);
		renderJsI18N(CLS1220M03Page.class);
		renderJsI18N(CLS1220V01Page.class);

		new DocLogPanel("_docLog").processPanelData(model, params);

		boolean edit_n_applyStatus = (meta != null && (Util
				.equals(meta.getDocStatus(), CLSDocStatusEnum.編製中.getCode())
				|| Util.equals(meta.getDocStatus(),
						CLSDocStatusEnum.待覆核.getCode())));
		model.addAttribute("area_show_n_applyStatus_visible", edit_n_applyStatus);
		model.addAttribute("area_hide_n_applyStatus_visible", !edit_n_applyStatus);

		model.addAttribute("hs_c122s01a_batchNo_1_visible", boolVal);
		model.addAttribute("hs_c122s01a_batchNo_2_visible", !boolVal);
	}

}
