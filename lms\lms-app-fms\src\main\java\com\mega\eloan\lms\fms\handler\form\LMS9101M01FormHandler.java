/* 
 * LMS9101M01FormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.handler.form;

import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.fms.pages.LMS9101V00Page;
import com.mega.eloan.lms.mfaloan.service.MisELF386Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.PropUtil;
/**
 * <pre>
 * 補借款人所得空白資料
 * </pre>
 * 
 * @since 2012/11/02
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/11/02,gary,new
 *          </ul>
 */
@Scope("request")
@Controller("lms9101m01formhandler")
public class LMS9101M01FormHandler extends AbstractFormHandler {

	@Resource
	MisELF386Service mis386Service;
	@Resource
	DocFileService docfileservice;

	@Resource
	ICustomerService iCustomerService;
	
	/**
	 * 更新借款人所得空白資料(單筆)
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult querysingleUpdate(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String custId = params.getString("custId");
		String tDup = params.getString("tDup");
		String yPay = params.getString("yPay");
		String oMoney = params.getString("oMoney");
		String JobClass = params.getString("JobClass");
		
		boolean check_id_dup = true;
		if(LMSUtil.check2(custId)){
			if(LMSUtil.checkCustId_foreignerPersonalTIN(custId)){
				
			}else{
				check_id_dup = false;
			}
		}else{
			//not valid format
		} 
		
		if( check_id_dup ){
			Map<String, Object> latestData = iCustomerService.findByIdDupNo(custId, tDup);
			if(MapUtils.isEmpty(latestData)){
				throw new CapMessageException(custId+"-"+tDup+"請先在0024建檔", getClass());
			}
		}
		
		mis386Service.updateSingle(custId, tDup, yPay, oMoney, JobClass, user
				.getUserId().toString());
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));
		return result;
	}// ;

	/**
	 * 更新借款人所得空白資料(整批)
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryupdate2(PageParameters params)
			throws CapException {
		Properties pop = MessageBundleScriptCreator
		.getComponentResource(LMS9101V00Page.class);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString("mainId");
		String fieldId = params.getString("fieldId");
		String fileName = params.getString("name");
		String filesrc = new String();
		if (fileName.lastIndexOf("\\") != -1) {
			fileName = fileName.substring(fileName.lastIndexOf("\\") + 1,
					fileName.length());
		}
		List<DocFile> dupFiles = docfileservice.findByIDAndName(mainId,
				fieldId, fileName);
		filesrc = PropUtil.getProperty("docFile.dir") + "/"
				+ dupFiles.get(0).getRealFileName();
		String[] info = {pop.getProperty("L910M01a.xlsinsertsucces"),
				pop.getProperty("L910M01a.xlsupdatesucces"),
				pop.getProperty("L910M01a.xlsYpayzeroerror"),
				pop.getProperty("L910M01a.xlsYpaylongerror") };
		mis386Service.updateExcel(filesrc, user.getUserId().toString(),info);
		result.set("filesrc", filesrc);
		return result;
	}// ;

	/**
	 * 取得mainId
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getmianId(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mianId = IDGenerator.getRandomCode().toString();
		result.set("mainId", mianId);
		return result;
	}// ;

}
