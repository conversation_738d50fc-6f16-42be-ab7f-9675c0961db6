/* 
 * C101S01PDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C101S01P;

/** 個金相關查詢所擔任負責人或董監事之企業是否於本行有授信額度達一億元以上名單 **/
public interface C101S01PDao extends IGenericDao<C101S01P> {

	C101S01P findByOid(String oid);

	List<C101S01P> findByMainId(String mainId);

	C101S01P findByUniqueKey(String mainId, String custId, String dupNo, String comCustId);
	
	List<C101S01P> findByIndex01(String mainId, String custId, String dupNo, String comCustId);
	
	List<C101S01P> findByCustIdDupId(String custId,String dupNo);
	
	int deleteByOid(String oid);
}