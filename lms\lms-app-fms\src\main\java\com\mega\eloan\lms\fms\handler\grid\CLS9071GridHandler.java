package com.mega.eloan.lms.fms.handler.grid;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.fms.pages.CLS9071V01Page;
import com.mega.eloan.lms.fms.service.CLS9071Service;
import com.mega.eloan.lms.mfaloan.bean.PTEAMAPP;
import com.mega.eloan.lms.mfaloan.service.MisPTEAMAPPService;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

@Scope("request")
@Controller("cls9071gridhandler")
public class CLS9071GridHandler extends AbstractGridHandler {

	@Resource
	CLS9071Service service;

	@Resource
	BranchService branchService;
	
	@Resource
	UserInfoService userInfoService;

	@Resource
	MisPTEAMAPPService misPTEAMAPPService;
	
	Properties prop = MessageBundleScriptCreator
			.getComponentResource(CLS9071V01Page.class);

	/**
	 * 查詢Grid 資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	public CapMapGridResult queryGrpCntrNo(ISearch pageSetting,
			PageParameters params) throws CapException {
		
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String brNo = user.getUnitNo();
		
		String idDup = Util.trim(params.getString("idDup"));
		//=======
		if(Util.isNotEmpty(idDup) && CrsUtil.grp_detail_allow_companyIdDup(brNo).contains(idDup)){
			String custId = Util.trim(StringUtils.substring(idDup, 0, 10));
			String dupNo = Util.trim(StringUtils.substring(idDup, 10, 11));
			
			/*
			 * 參考 cls1151gridhandler :: genL140M01A_pteamappGrid
			 */ 
			List<PTEAMAPP> pteamapp_list = misPTEAMAPPService.getDataBykey(
					custId, dupNo, CapDate.ZERO_DATE);
			
			for (PTEAMAPP pteamapp : pteamapp_list) {
				Map<String, Object> row = new HashMap<String, Object>();

				LMSUtil.meta_to_map(row, pteamapp,
						new String[] { "issuebrno", "year", "projectnm",
								"buildname", "grpcntrno", "subcompnm",
								"efffrom", "effend", "overamt", "amtappno" });
				String issuebrno = Util.trim(pteamapp.getIssuebrno());
				row.put("issuebrno_name",
						issuebrno
								+ Util.trim(branchService
										.getBranchName(issuebrno)));
				// ---
				list.add(row);
			}			
		}
		
		Page<Map<String, Object>> page = LMSUtil.getMapGirdDataRow(list,
				pageSetting);
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}	
	
}
