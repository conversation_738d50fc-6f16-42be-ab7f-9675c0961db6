/*_
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.lns.handler.form;

import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.response.MegaErrorResult;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.handler.FileUploadHandler;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapMath;
import tw.com.iisi.cap.util.CapString;

/**
 * <pre>
 * 檔案上傳
 * </pre>
 * 
 * @since 2013/2/4
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/02/04,new,處理totPage使用者自行輸入頁次
 *          </ul>
 */
@Scope("request")
@Controller("lms1201fileuploadhandler")
public class LMS1201FileUploadHandler extends FileUploadHandler {
	private static final int LEN_SRC_FILE_NAME = StrUtils.getEntityFileldLegth(
			DocFile.class, "srcFileName", 180);
	private static final int LEN_CONTENT_TYPE = StrUtils.getEntityFileldLegth(
			DocFile.class, "contentType", 100);
	private static final int LEN_FILE_DESC = StrUtils.getEntityFileldLegth(
			DocFile.class, "fileDesc", 90);
	private static final String undefined = "undefined";

	@Autowired
	DocFileService fileService;

	@Override
	public IResult afterUploaded(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		MultipartFile uFile = params.getFile(params.getString("fieldId"));
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String oid = params.getString(EloanConstants.OID);
		String uid = params.getString(EloanConstants.MAIN_UID);
		String fieldId = params.getString("fieldId");
		String fileDesc = this.getFileDesc(params);
		String totPage = params.getString("totPage");

		boolean isGetImgDimension = params.getBoolean("getImgDimension");

		if (params.containsKey("fileSize")) {
			long tolSize = fileService.getTolFileSize(mainId, fieldId);
			tolSize = tolSize + uFile.getSize();
			if (tolSize > params.getLong("fileSize", 1048576)) {
				// EFD0063=ERROR|上送的檔案已超過$\{fileSize\}M的限制大小，無法執行上傳動作。|
				Map<String, String> msg = new HashMap<String, String>();
				msg.put("fileSize",
						CapMath.divide(params.getString("fileSize"), "1048576")); // 1M*1024*1024
				MegaErrorResult result = new MegaErrorResult();
				result.putError(params, new CapMessageException(RespMsgHelper.getMessage("EFD0063", msg), getClass()));
				return result;
			}
		}
		logger.debug(uFile.getContentType());
		String sysId = params.getString("sysId", fileService.getSysId());
		// 設定上傳檔案資訊
		DocFile docFile = new DocFile();
		docFile.setBranchId(user.getUnitNo());
		docFile.setContentType(uFile.getContentType());
		docFile.setMainId(undefined.equals(mainId) ? oid : mainId);
		docFile.setPid(CapString.isEmpty(uid) ? null : uid);
		docFile.setCrYear(CapDate.getCurrentDate("yyyy"));
		docFile.setFieldId(fieldId);
		docFile.setDeletedTime(null);
		if(!CapString.isEmpty(totPage)&&CapString.isNumeric(totPage))
			docFile.setTotPages(Integer.parseInt(totPage));
		// fix ie
		String fileName = uFile.getName();
		if (fileName.lastIndexOf("\\") != -1) {
			fileName = fileName.substring(fileName.lastIndexOf("\\") + 1,
					fileName.length());
		}
		docFile.setSrcFileName(fileName);
		docFile.setUploadTime(CapDate.getCurrentTimestamp());
		docFile.setSysId(sysId);
		docFile.setFileSize(uFile.getSize());
		docFile.setFileDesc(fileDesc);

		if (params.containsKey("deleteDup") && params.getAsBoolean("deleteDup")) {
			List<DocFile> dupFiles = fileService.findByIDAndName(mainId,
					fieldId, fileName);
			if (!CollectionUtils.isEmpty(dupFiles)) {
				for (DocFile dupFile : dupFiles) {
					fileService.delete(dupFile.getOid());
				}
			}
		}

		this.checkParams(docFile);

		InputStream is = null;
		String fileKey = "";
		int[] dimension = { -1, -1 };
		try {
			// 設定上傳檔案處理物件
			is = uFile.getInputStream();
			docFile.setData(IOUtils.toByteArray(is));
			// 儲存上傳檔案
			fileKey = fileService.save(docFile);

			// 若是圖檔取得其尺寸
			if (isGetImgDimension) {
				dimension = fileService.getImageDimension(docFile);
			}
		} catch (IOException e) {
			logger.error(e.getMessage(), e);
			throw new CapMessageException("file IO ERROR", getClass());
		} finally {
			if (is != null) {
				try {
					is.close();
				} catch (IOException e) {
					logger.debug("inputStream close Error", getClass());
				}
			}

		}

		return new CapAjaxFormResult().set("url", "file?id=" + fileKey)
				.set("fileKey", fileKey).set("imgWidth", dimension[0])
				.set("imgHeight", dimension[1]);
	}

	private void checkParams(DocFile docFile) throws CapMessageException {
		StringBuffer fields = new StringBuffer();
		if (docFile.getSrcFileName().getBytes().length > LEN_SRC_FILE_NAME) {
			fields.append("FileName ");
		}

		if (docFile.getContentType().getBytes().length > LEN_CONTENT_TYPE) {
			fields.append("ContentType ");
		}

		if (docFile.getFileDesc().getBytes().length > LEN_FILE_DESC) {
			fields.append("Description ");
		}

		if (fields.length() > 0) {
			Map<String, String> msgMap = new HashMap<String, String>();
			msgMap.put("colName", fields.toString());
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", msgMap), getClass());
		}

	}

	/**
	 * 取得檔案說明
	 * 
	 * @param params
	 *            {@link org.apache.wicket.PageParameters}
	 * @return 檔案說明
	 */
	private String getFileDesc(PageParameters params) {
		String fileDesc = params.getString("fileDesc");
		// if (fileDesc != null) {
		// try {
		// fileDesc = new String(fileDesc.getBytes("ISO8859-1"), "UTF-8");
		// } catch (UnsupportedEncodingException e) {
		// logger.error(e.getMessage());
		// }
		// }
		return StringUtils.trimToEmpty(fileDesc);

	}

	@Override
	public String getOperationName() {
		return "fileUploadOperation";
	}

}
