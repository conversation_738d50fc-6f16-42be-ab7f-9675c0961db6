package com.mega.eloan.lms.fms.report.impl;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.inet.report.ReportException;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.fms.report.CLS9071R02RptService;
import com.mega.eloan.lms.fms.service.CLS9071Service;

import tw.com.iisi.cap.exception.CapException;
import tw.com.jcs.common.Util;

@Service("cls9071r02rptservice")
public class CLS9071R02RptServiceImpl implements
	FileDownloadService, CLS9071R02RptService {

	@Resource
	CLS9071Service cls9071Service;
	
	@Override
	public byte[] getContent(PageParameters params) throws CapException,
			FileNotFoundException, ReportException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
			boolean includeOnTheWay = Util.equals(params.getString("onTheWay"), "Y");
			String brNo = Util.trim(params.getString("brNo"));
			String custId = Util.trim(params.getString("custId"));
			String grpCntrNo = Util.trim(params.getString("grpCntrNo"));
			
			String rptNo = Util.trim(params.getString("rptNo"));
			String p_dataYM1 = Util.trim(params.getString("p_dataYM1"));
			String c_dataYM1 = Util.trim(params.getString("c_dataYM1"));
			String c_dataYM2 = Util.trim(params.getString("c_dataYM2"));
			
			if(true){
				baos = (ByteArrayOutputStream) this.generateXls(includeOnTheWay, brNo, custId, grpCntrNo, rptNo,  p_dataYM1, c_dataYM1, c_dataYM2);	
			}
			if(baos==null){
				return null;
			}else{
				return baos.toByteArray();	
			}			
		} finally {
			if (baos != null) {
				baos.close();
			}

		}
	}
	
	private ByteArrayOutputStream generateXls(boolean includeOnTheWay, String brNo, String custId, String grpCntrNo, 
			String rptNo, String p_dataYM1 , String c_dataYM1 , String c_dataYM2) throws IOException, Exception {	
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		if(true){
			cls9071Service.genExcel_J_107_0046(outputStream, includeOnTheWay, brNo, custId, grpCntrNo, rptNo,  p_dataYM1, c_dataYM1, c_dataYM2 ,false);	
		}		
		if(outputStream!=null){
			outputStream.flush();	
		}		
		return outputStream;
	}
	
}
