#==================================================
# \u8986\u5be9\u5831\u544a\u8868-\u5916Grid
#==================================================
L170M01a.retrialDate=\u8986\u5ba1\u65e5\u671f
L170M01a.projectNo=\u8986\u5ba1\u6848\u53f7
L170M01a.custId=\u7edf\u4e00\u7f16\u53f7
L170M01a.custName=\u4e3b\u8981\u501f\u6b3e\u4eba
L170M01a.mLoanPerson=\u7b26\u5408\u6388\u4fe1\u984d\u5ea6\u6a19\u6e96
L170M01a.mLoanPersonA=\u4e3b\u8981\u6388\u4fe1\u6237
L170M01a.lastRetrialDate=\u4e0a\u6b21\u8986\u5ba1\u65e5\u671f
L170M01a.conFlag=\u514d\u8986\u5ba1\u6ce8\u8bb0
L170M01a.approver=\u8986\u5ba1\u4eba\u5458
L170M01a.complete=\u5b8c\u6210
#==================================================
# \u8986\u5be9\u5831\u544a\u8868-thickbox
#==================================================
L170M01a.insertData=\u65b0\u589e\u8986\u5ba1\u62a5\u544a\u8868
L170M01a.error1=\u5c1a\u672a\u9009\u53d6\u6570\u636e
L170M01a.error2=\u5c1a\u672a\u586b\u5beb\u8cc7\u6599
L170M01a.fileter=\u7be9\u9078\u689d\u4ef6
