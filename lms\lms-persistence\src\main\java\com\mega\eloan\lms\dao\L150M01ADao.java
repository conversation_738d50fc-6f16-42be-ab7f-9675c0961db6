/* 
 * L150M01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L150M01A;

/** 小放會會議紀錄檔 **/
public interface L150M01ADao extends IGenericDao<L150M01A> {
	/**
	 * 找出oid的 資料
	 * 
	 * @param oid
	 *            文件編號
	 * @return L150M01A
	 */
	L150M01A findByOid(String oid);

	/**
	 * 找出oids的 資料
	 * 
	 * @param oids
	 *            文件編號陣列
	 * @return List<L150M01A>
	 */
	List<L150M01A> findByOids(String oids[]);

	/**
	 * @param mainId
	 * @return
	 */
	L150M01A findByMainId(String mainId);
}