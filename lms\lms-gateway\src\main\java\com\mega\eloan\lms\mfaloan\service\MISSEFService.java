package com.mega.eloan.lms.mfaloan.service;

import java.util.Map;

/**
 * <pre>
 * MIS.MISSEF
 * </pre>
 * 
 * @since 2012/11/14
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/11/14,REX,new
 *          </ul>
 */
public interface MISSEFService {

	/**
	 *共同行銷取得所有欄位
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @return <pre>
	 * 
	 * SE_CUST_ID
	 * SE_CUST_DUP
	 * SE_BAS_DATA
	 * SE_ACC_DATA
	 * SE_ACC_DATA_BISC
	 * SE_ACC_DATA_CKI
	 * SE_ACC_DATA_CHBF
	 * SE_ACC_DATA_IA 
	 * SE_ACC_DATA_IIT
	 * SE_ACC_DATA_AMC
	 * SE_ACC_DATA_CI
	 * </pre>
	 */
	public Map<String, Object> getAll(String custId, String dupNo);
}
