package com.mega.eloan.lms.mfaloan.bean;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Digits;

import org.apache.wicket.markup.html.form.Check;

import tw.com.iisi.cap.model.GenericBean;

/** 覆審資料檔 **/
public class ELF490 extends GenericBean{

	private static final long serialVersionUID = 1L;
	/**
	 * 資料日期
	 */
	@Column(name = "ELF490_DATA_YM", length = 6, columnDefinition = "CHAR(6)")
	private String elf490_data_ym;
	
	/**
	 * 分行別
	 */
	@Column(name = "ELF490_BR_NO", length = 3, columnDefinition = "CHAR(3)")
	private String elf490_br_no;
	
	/**
	 * 客戶ID
	 */
	@Column(name = "ELF490_CUST_ID", length = 10, columnDefinition = "CHAR(10)")
	private String elf490_cust_id;

	/**
	 * 重覆碼
	 */
	@Column(name = "ELF490_DUP_NO", length = 1, columnDefinition = "CHAR(1)")
	private String elf490_dup_no;
	
	/**
	 * 額度金額
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "ELF490_FACT_AMT_T", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal elf490_fact_amt_t;
	
	/**
	 * 餘額
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "ELF490_LOAN_BAL_T", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal elf490_loan_bal_t;
	
	/**
	 * 最新額度動用起日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF490_MAX_BEG_DT", columnDefinition = "DATE")
	private Date elf490_max_beg_dt;
	
	/**
	 * 舊案類別
	 */
	@Column(name = "ELF490_RULE_NO", length = 255, columnDefinition = "CHAR(255)")
	private String elf490_rule_no;

	/**
	 * 新案類別
	 */
	@Column(name = "ELF490_RULE_NO_NEW", length = 255, columnDefinition = "CHAR(255)")
	private String elf490_rule_no_new;
	
	/**
	 * 相對月份註記
	 */
	@Column(name = "ELF490_RELATIVE_MM", length = 1, columnDefinition = "CHAR(1)")
	private String elf490_relative_mm;
	
	/**
	 * 全行歸戶不動產授信金額
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "ELF490_ESTATE_AMT", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal elf490_estate_amt;

	/** 貸放條件註記  <br/>
	 * {貸款成數逾八成且寬限期逾二年且信用評等經人工調升二(含)等以上者} 
	 */
	@Column(name = "ELF490_HOUSE_FG", length = 1, columnDefinition = "CHAR(1)")
	private String elf490_house_fg;
	
	/** 房貸壽險註記 */
	@Column(name = "ELF490_RMBINS_FG", length = 1, columnDefinition = "CHAR(1)")
	private String elf490_rmbins_fg;
	
	/** 房貸無擔保科目註記  <br/>
	 * {房貸搭配無擔保科目} 可能(A)在1額度裡包含2個帳號   (B)有擔1個額度，無擔1個額度
	 */
	@Column(name = "ELF490_HS_CRE_FG", length = 1, columnDefinition = "CHAR(1)")
	private String elf490_hs_cre_fg;
	
	/** 符合地區金額判斷註記 */
	@Column(name = "ELF490_AREA_FG", length = 1, columnDefinition = "CHAR(1)")
	private String elf490_area_fg;
	
	
	public String getElf490_data_ym() {
		return elf490_data_ym;
	}

	public void setElf490_data_ym(String elf490_data_ym) {
		this.elf490_data_ym = elf490_data_ym;
	}

	public String getElf490_br_no() {
		return elf490_br_no;
	}

	public void setElf490_br_no(String elf490_br_no) {
		this.elf490_br_no = elf490_br_no;
	}

	public String getElf490_cust_id() {
		return elf490_cust_id;
	}

	public void setElf490_cust_id(String elf490_cust_id) {
		this.elf490_cust_id = elf490_cust_id;
	}

	public String getElf490_dup_no() {
		return elf490_dup_no;
	}

	public void setElf490_dup_no(String elf490_dup_no) {
		this.elf490_dup_no = elf490_dup_no;
	}

	public BigDecimal getElf490_fact_amt_t() {
		return elf490_fact_amt_t;
	}

	public void setElf490_fact_amt_t(BigDecimal elf490_fact_amt_t) {
		this.elf490_fact_amt_t = elf490_fact_amt_t;
	}

	public BigDecimal getElf490_loan_bal_t() {
		return elf490_loan_bal_t;
	}

	public void setElf490_loan_bal_t(BigDecimal elf490_loan_bal_t) {
		this.elf490_loan_bal_t = elf490_loan_bal_t;
	}

	public Date getElf490_max_beg_dt() {
		return elf490_max_beg_dt;
	}

	public void setElf490_max_beg_dt(Date elf490_max_beg_dt) {
		this.elf490_max_beg_dt = elf490_max_beg_dt;
	}

	public String getElf490_rule_no() {
		return elf490_rule_no;
	}

	public void setElf490_rule_no(String elf490_rule_no) {
		this.elf490_rule_no = elf490_rule_no;
	}

	public String getElf490_rule_no_new() {
		return elf490_rule_no_new;
	}

	public void setElf490_rule_no_new(String elf490_rule_no_new) {
		this.elf490_rule_no_new = elf490_rule_no_new;
	}

	public String getElf490_relative_mm() {
		return elf490_relative_mm;
	}

	public void setElf490_relative_mm(String elf490_relative_mm) {
		this.elf490_relative_mm = elf490_relative_mm;
	}

	public BigDecimal getElf490_estate_amt() {
		return elf490_estate_amt;
	}

	public void setElf490_estate_amt(BigDecimal elf490_estate_amt) {
		this.elf490_estate_amt = elf490_estate_amt;
	}

	public String getElf490_house_fg() {
		return elf490_house_fg;
	}

	public void setElf490_house_fg(String elf490_house_fg) {
		this.elf490_house_fg = elf490_house_fg;
	}

	public String getElf490_rmbins_fg() {
		return elf490_rmbins_fg;
	}

	public void setElf490_rmbins_fg(String elf490_rmbins_fg) {
		this.elf490_rmbins_fg = elf490_rmbins_fg;
	}

	public String getElf490_hs_cre_fg() {
		return elf490_hs_cre_fg;
	}

	public void setElf490_hs_cre_fg(String elf490_hs_cre_fg) {
		this.elf490_hs_cre_fg = elf490_hs_cre_fg;
	}

	public String getElf490_area_fg() {
		return elf490_area_fg;
	}

	public void setElf490_area_fg(String elf490_area_fg) {
		this.elf490_area_fg = elf490_area_fg;
	}	

}
