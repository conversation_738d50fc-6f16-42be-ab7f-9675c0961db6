package com.mega.eloan.lms.batch.service.impl;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.mfaloan.service.MisELF506Service;
import com.mega.eloan.lms.obsdb.service.ObsdbELF506Service;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.annotation.NonTransactional;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.jcs.common.Util;

/**
 * J-113-0059 補上傳約定融資額度註記到ELF506
 */
@Service("lmsbatch0014serviceimpl")
public class LmsBatch0014ServiceImpl extends AbstractCapService implements
		WebBatchService {

	private Logger logger = LoggerFactory.getLogger(this.getClass());

	@Resource
	EloandbBASEService eloandbService;

	@Resource
	LMSService lmsService;

	@Resource
	MisELF506Service misELF506Service;

	@Resource
	ObsdbELF506Service obsdbELF506Service;

	@Resource
	BranchService branchService;

	@Override
	@NonTransactional
	public JSONObject execute(JSONObject json) {
		// @NonTransactional
		JSONObject result = new JSONObject();
		JSONObject request = json.getJSONObject("request");

		String errMsg = this.doLmsBatch0001(request);
		if (Util.notEquals(errMsg, "")) {
			result = WebBatchCode.RC_ERROR;
			result.element(WebBatchCode.P_RESPONSE,
					"LmsBatch0014ServiceImpl-doLmsBatch0001執行失敗！==>" + errMsg);
			return result;
		} else {
			result = WebBatchCode.RC_SUCCESS;
			result.element(WebBatchCode.P_RESPONSE,
					"LmsBatch00014ServiceImpl執行成功！");
		}

		return result;
	}

	// 取得ELF506需要更新約定融資額度註記之額度序號清單後，以該額度序號取得最新一筆核准之額度明細表內的約定融資額度註記寫回ELF506
	@NonTransactional
	public String doLmsBatch0001(JSONObject request) {

		StringBuffer errMsg = new StringBuffer("");
		String dataModifyDate = Util.trim(request.getString("dataModifyDate"));
		int batch_cnt = Util.parseInt(request.optString("batch_cnt"));

		if (batch_cnt == 0) {
			batch_cnt = 1000;
		}

		if (Util.isEmpty(dataModifyDate)) {
			dataModifyDate = "2024-04-01 00:00:00";// 預設補更新日期為2024/03/31(含)以前的資料
		} else {
			dataModifyDate = dataModifyDate + " 00:00:00";// 補指定更新日期(不含)以前的資料
		}

		List<Map<String, Object>> elf506List = misELF506Service
				.getExceptIsEmpty(dataModifyDate);// dataStartDate, dataEndDate,

		if (elf506List != null && elf506List.size() > 0) {
			int runCount = 1;
			int errorCount = 0;
			for (Map<String, Object> elf506Map : elf506List) {
				String elf506_cntrNo = Util.trim(MapUtils.getObject(elf506Map,
						"ELF506_CNTRNO"));
				logger.info("[LmsBatch0014ServiceImpl-ELF506_CNTRNO => "
						+ elf506_cntrNo + " ; runCount => " + runCount);
				try {
					// 以額度序號取得最新一筆核准之額度明細表內的約定融資額度註記
					Map<String, Object> l140m01aExceptMap = eloandbService
							.findLatestExceptFlagByCntrNo(elf506_cntrNo);

					if (l140m01aExceptMap != null
							&& Util.isNotEmpty(l140m01aExceptMap)) {
						String exceptFlag = Util.nullToSpace(MapUtils
								.getString(l140m01aExceptMap, "EXCEPTFLAG")); // 約定額度融資註記
						if (Util.equals(exceptFlag, "_")) {// _ 為N.A.，上傳時改為""
							exceptFlag = "";
						}
						String exceptFlagQAisY = Util
								.nullToSpace(MapUtils.getString(
										l140m01aExceptMap, "EXCEPTFLAGQAISY"));// 約定融資額度註記問答項目選擇[是]的項目
						String exceptFlagQAPlus = Util.nullToSpace(MapUtils
								.getString(l140m01aExceptMap,
										"EXCEPTFLAGQAPLUS"));// 約定融資額度註記資本計提延伸問答

						logger.info("[LmsBatch0014ServiceImpl-更新MIS.ELF506約定融資額度註記相關欄位]，額度序號 => "
								+ elf506_cntrNo
								+ "，約定額度融資註記="
								+ exceptFlag
								+ "，約定額度融資註記問答項目選擇[是]的項目 => "
								+ exceptFlagQAisY
								+ "，約定融資額度註記資本計提延伸問答 => " + exceptFlagQAPlus);
						// 更新 ELF506 約定融資額度註記
						misELF506Service.updateExcpetByCntrNo(exceptFlag,
								exceptFlagQAisY, exceptFlagQAPlus,
								elf506_cntrNo);

						String brnId = Util.getLeftStr(elf506_cntrNo, 3);

						if (UtilConstants.BrNoType.國外.equals(branchService
								.getBranch(brnId).getBrNoFlag())) {// 海外的
							// 更新AS400
							// 要特別處理Y的項目
							// 海外要特別處理exceptFlagQAisY
							String exceptFlagQAisY_as400 = exceptFlagQAisY;
							if (Util.isNotEmpty(exceptFlagQAisY_as400)
									&& exceptFlagQAisY_as400.length() == 1) {
								exceptFlagQAisY_as400 = "0"
										+ exceptFlagQAisY_as400;
							}
							logger.info("[LmsBatch0014ServiceImpl-更新AS400.ELF506約定融資額度註記相關欄位]，額度序號 => "
									+ elf506_cntrNo
									+ "，brnId=> "
									+ brnId
									+ "，約定額度融資註記 => "
									+ exceptFlag
									+ "，約定額度融資註記問答項目選擇[是]的項目 => "
									+ exceptFlagQAisY_as400
									+ "，約定融資額度註記資本計提延伸問答 => "
									+ exceptFlagQAPlus);
							// 更新 ELF506 約定融資額度註記
							obsdbELF506Service.updateExcpetByCntrNo(brnId,
									exceptFlag, exceptFlagQAisY_as400,
									exceptFlagQAPlus, elf506_cntrNo);
						}
					}
				} catch (Exception e) {
					errorCount++;
					logger.error("[LmsBatch0014ServiceImpl] update exceptFlag Exception ====>"
							+ "額度序號 =>> " + elf506_cntrNo);
					if (Util.isEmpty(errMsg)) {
						errMsg.append(
								"[LmsBatch0014ServiceImpl]-更新下列額度序號約定額度融資註記失敗 =>")
								.append(elf506_cntrNo);

					} else {
						errMsg.append("、").append(elf506_cntrNo);
					}
					if (errorCount == 30) {
						// 如果已經更新30筆失敗的話就先中斷批次
						break;
					}
				}
				if (runCount == batch_cnt) {
					break;
				}
				runCount++;
			}
		}
		return errMsg.toString();
	}

}
