/* 
 * L230S01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.lms.validation.group.Check;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/** 簽約未動用額度資訊檔 **/
@NamedEntityGraph(name = "L230S01A-entity-graph", attributeNodes = { @NamedAttributeNode("l230m01a") })
@Entity
@Table(name = "L230S01A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class L230S01A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * JOIN條件 關聯黨
	 * 
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "MAINID", referencedColumnName = "MAINID", insertable = false, updatable = false, nullable = false)
	private L230M01A l230m01a;

	public L230M01A getL230m01a() {
		return l230m01a;
	}

	public void setL230m01a(L230M01A l230m01a) {
		this.l230m01a = l230m01a;
	}

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** mainId **/
	@Size(max = 32)
	@Column(length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 統一編號
	 * <p/>
	 * 資料來源：額度明細表
	 */
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/**
	 * 重覆序號
	 * <p/>
	 * 資料來源：額度明細表
	 */
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/**
	 * 客戶名稱
	 * <p/>
	 * 資料來源：額度明細表
	 */
	@Size(max = 120)
	@Column(name = "CUSTNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String custName;

	/**
	 * 編製單位代號
	 * <p/>
	 * 評等單位/編製單位
	 */
	@Size(max = 3)
	@Column(name = "OWNBRID", length = 3, columnDefinition = "CHAR(3)")
	private String ownBrId;

	/**
	 * 目前文件狀態
	 * <p/>
	 * 資料來源：額度明細表
	 */
	@Size(max = 3)
	@Column(name = "DOCSTATUS", length = 3, columnDefinition = "VARCHAR(3)")
	private String docStatus;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Date updateTime;

	/**
	 * 案件號碼-分行
	 * <p/>
	 * 資料來源：額度明細表
	 */
	@Size(max = 3)
	@Column(name = "CASEBRID", length = 3, columnDefinition = "CHAR(3)")
	private String caseBrId;

	/**
	 * 案件號碼
	 * <p/>
	 * 資料來源：額度明細表
	 */
	@Size(max = 62)
	@Column(name = "CASENO", length = 62, columnDefinition = "VARCHAR(62)")
	private String caseNo;

	/**
	 * 簽案日期
	 * <p/>
	 * 資料來源：額度明細表
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "CASEDATE", columnDefinition = "DATE")
	private Date caseDate;

	/**
	 * 原案額度明細表MainId
	 * <p/>
	 * 資料來源：額度明細表
	 */
	@Size(max = 32)
	@Column(name = "SRCMAINID", length = 32, columnDefinition = "VARCHAR(32)")
	private String srcMainId;

	/**
	 * 額度序號
	 * <p/>
	 * 資料來源：額度明細表
	 */
	@Size(max = 12)
	@Column(name = "CNTRNO", length = 12, columnDefinition = "CHAR(12)")
	private String cntrNo;

	/**
	 * 授信科目
	 * <p/>
	 * 資料來源：額度明細表
	 */
	@Size(max = 1536)
	@Column(name = "LNSUBJECT", length = 1536, columnDefinition = "VARCHAR(1536)")
	private String lnSubject;

	/**
	 * 性質
	 * <p/>
	 * 資料來源：額度明細表新做|1<br/>
	 * <br/>
	 * 續約|2<br/>
	 * <br/>
	 * 變更條件|3<br/>
	 * <br/>
	 * 流用|4<br/>
	 * <br/>
	 * 增額|5<br/>
	 * <br/>
	 * 減額|6<br/>
	 * <br/>
	 * 不變|7<br/>
	 * <br/>
	 * 取消|8<br/>
	 * <br/>
	 * 展期|9<br/>
	 * <br/>
	 * 紓困|10<br/>
	 * <br/>
	 * 提前續約者|11<br/>
	 * <br/>
	 * 協議清償|12<br/>
	 * <br/>
	 * 報價|13
	 */
	@Size(max = 30)
	@Column(name = "PROPERTY", length = 30, columnDefinition = "VARCHAR(30)")
	private String proPerty;

	/**
	 * 現請額度－幣別
	 * <p/>
	 * 資料來源：額度明細表
	 */
	@Size(max = 3)
	@Column(name = "CURRENTAPPLYCURR", length = 3, columnDefinition = "CHAR(3)")
	private String currentApplyCurr;

	/**
	 * 現請額度－金額
	 * <p/>
	 * 資料來源：額度明細表
	 */
	@Digits(integer = 15, fraction = 2, groups = Check.class)
	@Column(name = "CURRENTAPPLYAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal currentApplyAmt;

	/**
	 * 狀態註記(新案才要維護)
	 * <p/>
	 * 已簽約| 3<br/>
	 * 不簽約-註銷額度 | D<br/>
	 * 未簽約| Z<br/>
	 * ※2012-10-24授管處新增<br/>
	 * 已動用| 3
	 */
	@Size(max = 1)
	@Column(name = "NUSEMEMO", length = 1, columnDefinition = "CHAR(1)")
	private String nuseMemo;

	/** 已簽約日期/已動用日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "SIGNDATE", columnDefinition = "DATE")
	private Date signDate;

	/**
	 * 不簽約原因
	 * <p/>
	 * ※2012-10-24授管處新增<br/>
	 * (可複選)<br/>
	 * 01 額度<br/>
	 * 02 利率<br/>
	 * 03 擔保品<br/>
	 * 04 徵提(連帶)保證人<br/>
	 * 05 動用限制<br/>
	 * 06 還款期間、還款條件<br/>
	 * 07 維持存款平均餘額條件<br/>
	 * 08 提供定存設質條件<br/>
	 * 09 限制用途條件<br/>
	 * 10 借款戶本身因素，主動婉卻訂約。<br/>
	 * 99 借款戶或連保人因素，本行主動婉卻訂約。<br/>
	 * 01|02|........<br/>
	 * 當勾選99時需輸入原因
	 */
	@Size(max = 40)
	@Column(name = "REASON", length = 40, columnDefinition = "VARCHAR(40)")
	private String reason;

	/**
	 * 不簽約原因說明
	 * <p/>
	 * ※2012-10-24授管處新增<br/>
	 * <br/>
	 * 需上傳447<br/>
	 * 100中文字
	 */
	@Size(max = 300)
	@Column(name = "REASONDRC", length = 300, columnDefinition = "VARCHAR(300)")
	private String reasonDrc;

	/**
	 * 核准日
	 * <p/>
	 * 於核准時填入
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "DATADATE", columnDefinition = "DATE")
	private Date dataDate;

	/**
	 * NOTES 轉檔版本
	 */
	@Column(name = "NOTESUP", columnDefinition = "VARCHAR(6)")
	private String notesUp;

	/**
	 * 本案實際簽約額度低於核准額度
	 * 
	 */
	@Size(max = 1)
	@Column(name = "ISSIGNAMTLOWERAPPLYAMT", length = 1, columnDefinition = "CHAR(1)")
	private String isSignAmtLowerApplyAmt;

	/**
	 * 實際簽約額度有擔保
	 * 
	 */
	@Digits(integer = 15, fraction = 2, groups = Check.class)
	@Column(name = "SIGNAMT_S", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal signAmt_S;

	/**
	 * 實際簽約額度無擔保
	 * 
	 */
	@Digits(integer = 15, fraction = 2, groups = Check.class)
	@Column(name = "SIGNAMT_N", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal signAmt_N;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得mainId **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定mainId **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得統一編號
	 * <p/>
	 * 資料來源：額度明細表
	 */
	public String getCustId() {
		return this.custId;
	}

	/**
	 * 設定統一編號
	 * <p/>
	 * 資料來源：額度明細表
	 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/**
	 * 取得重覆序號
	 * <p/>
	 * 資料來源：額度明細表
	 */
	public String getDupNo() {
		return this.dupNo;
	}

	/**
	 * 設定重覆序號
	 * <p/>
	 * 資料來源：額度明細表
	 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/**
	 * 取得客戶名稱
	 * <p/>
	 * 資料來源：額度明細表
	 */
	public String getCustName() {
		return this.custName;
	}

	/**
	 * 設定客戶名稱
	 * <p/>
	 * 資料來源：額度明細表
	 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/**
	 * 取得編製單位代號
	 * <p/>
	 * 評等單位/編製單位
	 */
	public String getOwnBrId() {
		return this.ownBrId;
	}

	/**
	 * 設定編製單位代號
	 * <p/>
	 * 評等單位/編製單位
	 **/
	public void setOwnBrId(String value) {
		this.ownBrId = value;
	}

	/**
	 * 取得目前文件狀態
	 * <p/>
	 * 資料來源：額度明細表
	 */
	public String getDocStatus() {
		return this.docStatus;
	}

	/**
	 * 設定目前文件狀態
	 * <p/>
	 * 資料來源：額度明細表
	 **/
	public void setDocStatus(String value) {
		this.docStatus = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}

	/**
	 * 取得案件號碼-分行
	 * <p/>
	 * 資料來源：額度明細表
	 */
	public String getCaseBrId() {
		return this.caseBrId;
	}

	/**
	 * 設定案件號碼-分行
	 * <p/>
	 * 資料來源：額度明細表
	 **/
	public void setCaseBrId(String value) {
		this.caseBrId = value;
	}

	/**
	 * 取得案件號碼
	 * <p/>
	 * 資料來源：額度明細表
	 */
	public String getCaseNo() {
		return this.caseNo;
	}

	/**
	 * 設定案件號碼
	 * <p/>
	 * 資料來源：額度明細表
	 **/
	public void setCaseNo(String value) {
		this.caseNo = value;
	}

	/**
	 * 取得簽案日期
	 * <p/>
	 * 資料來源：額度明細表
	 */
	public Date getCaseDate() {
		return this.caseDate;
	}

	/**
	 * 設定簽案日期
	 * <p/>
	 * 資料來源：額度明細表
	 **/
	public void setCaseDate(Date value) {
		this.caseDate = value;
	}

	/**
	 * 取得原案額度明細表MainId
	 * <p/>
	 * 資料來源：額度明細表
	 */
	public String getSrcMainId() {
		return this.srcMainId;
	}

	/**
	 * 設定原案額度明細表MainId
	 * <p/>
	 * 資料來源：額度明細表
	 **/
	public void setSrcMainId(String value) {
		this.srcMainId = value;
	}

	/**
	 * 取得額度序號
	 * <p/>
	 * 資料來源：額度明細表
	 */
	public String getCntrNo() {
		return this.cntrNo;
	}

	/**
	 * 設定額度序號
	 * <p/>
	 * 資料來源：額度明細表
	 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/**
	 * 取得授信科目
	 * <p/>
	 * 資料來源：額度明細表
	 */
	public String getLnSubject() {
		return this.lnSubject;
	}

	/**
	 * 設定授信科目
	 * <p/>
	 * 資料來源：額度明細表
	 **/
	public void setLnSubject(String value) {
		this.lnSubject = value;
	}

	/**
	 * 取得性質
	 * <p/>
	 * 資料來源：額度明細表新做|1<br/>
	 * <br/>
	 * 續約|2<br/>
	 * <br/>
	 * 變更條件|3<br/>
	 * <br/>
	 * 流用|4<br/>
	 * <br/>
	 * 增額|5<br/>
	 * <br/>
	 * 減額|6<br/>
	 * <br/>
	 * 不變|7<br/>
	 * <br/>
	 * 取消|8<br/>
	 * <br/>
	 * 展期|9<br/>
	 * <br/>
	 * 紓困|10<br/>
	 * <br/>
	 * 提前續約者|11<br/>
	 * <br/>
	 * 協議清償|12<br/>
	 * <br/>
	 * 報價|13
	 */
	public String getProPerty() {
		return this.proPerty;
	}

	/**
	 * 設定性質
	 * <p/>
	 * 資料來源：額度明細表新做|1<br/>
	 * <br/>
	 * 續約|2<br/>
	 * <br/>
	 * 變更條件|3<br/>
	 * <br/>
	 * 流用|4<br/>
	 * <br/>
	 * 增額|5<br/>
	 * <br/>
	 * 減額|6<br/>
	 * <br/>
	 * 不變|7<br/>
	 * <br/>
	 * 取消|8<br/>
	 * <br/>
	 * 展期|9<br/>
	 * <br/>
	 * 紓困|10<br/>
	 * <br/>
	 * 提前續約者|11<br/>
	 * <br/>
	 * 協議清償|12<br/>
	 * <br/>
	 * 報價|13
	 **/
	public void setProPerty(String value) {
		this.proPerty = value;
	}

	/**
	 * 取得現請額度－幣別
	 * <p/>
	 * 資料來源：額度明細表
	 */
	public String getCurrentApplyCurr() {
		return this.currentApplyCurr;
	}

	/**
	 * 設定現請額度－幣別
	 * <p/>
	 * 資料來源：額度明細表
	 **/
	public void setCurrentApplyCurr(String value) {
		this.currentApplyCurr = value;
	}

	/**
	 * 取得現請額度－金額
	 * <p/>
	 * 資料來源：額度明細表
	 */
	public BigDecimal getCurrentApplyAmt() {
		return this.currentApplyAmt;
	}

	/**
	 * 設定現請額度－金額
	 * <p/>
	 * 資料來源：額度明細表
	 **/
	public void setCurrentApplyAmt(BigDecimal value) {
		this.currentApplyAmt = value;
	}

	/**
	 * 取得狀態註記(新案才要維護)
	 * <p/>
	 * 已簽約| 3<br/>
	 * 不簽約-註銷額度 | D<br/>
	 * 未簽約| Z<br/>
	 * ※2012-10-24授管處新增<br/>
	 * 已動用| 3
	 */
	public String getNuseMemo() {
		return this.nuseMemo;
	}

	/**
	 * 設定狀態註記(新案才要維護)
	 * <p/>
	 * 已簽約| 3<br/>
	 * 不簽約-註銷額度 | D<br/>
	 * 未簽約| Z<br/>
	 * ※2012-10-24授管處新增<br/>
	 * 已動用| 3
	 **/
	public void setNuseMemo(String value) {
		this.nuseMemo = value;
	}

	/** 取得已簽約日期/已動用日期 **/
	public Date getSignDate() {
		return this.signDate;
	}

	/** 設定已簽約日期/已動用日期 **/
	public void setSignDate(Date value) {
		this.signDate = value;
	}

	/**
	 * 取得不簽約原因
	 * <p/>
	 * ※2012-10-24授管處新增<br/>
	 * (可複選)<br/>
	 * 01 額度<br/>
	 * 02 利率<br/>
	 * 03 擔保品<br/>
	 * 04 徵提(連帶)保證人<br/>
	 * 05 動用限制<br/>
	 * 06 還款期間、還款條件<br/>
	 * 07 維持存款平均餘額條件<br/>
	 * 08 提供定存設質條件<br/>
	 * 09 限制用途條件<br/>
	 * 10 借款戶本身因素，主動婉卻訂約。<br/>
	 * 99 借款戶或連保人因素，本行主動婉卻訂約。<br/>
	 * 01|02|........<br/>
	 * 當勾選99時需輸入原因
	 */
	public String getReason() {
		return this.reason;
	}

	/**
	 * 設定不簽約原因
	 * <p/>
	 * ※2012-10-24授管處新增<br/>
	 * (可複選)<br/>
	 * 01 額度<br/>
	 * 02 利率<br/>
	 * 03 擔保品<br/>
	 * 04 徵提(連帶)保證人<br/>
	 * 05 動用限制<br/>
	 * 06 還款期間、還款條件<br/>
	 * 07 維持存款平均餘額條件<br/>
	 * 08 提供定存設質條件<br/>
	 * 09 限制用途條件<br/>
	 * 10 借款戶本身因素，主動婉卻訂約。<br/>
	 * 99 借款戶或連保人因素，本行主動婉卻訂約。<br/>
	 * 01|02|........<br/>
	 * 當勾選99時需輸入原因
	 **/
	public void setReason(String value) {
		this.reason = value;
	}

	/**
	 * 取得不簽約原因說明
	 * <p/>
	 * ※2012-10-24授管處新增<br/>
	 * <br/>
	 * 需上傳447<br/>
	 * 100中文字
	 */
	public String getReasonDrc() {
		return this.reasonDrc;
	}

	/**
	 * 設定不簽約原因說明
	 * <p/>
	 * ※2012-10-24授管處新增<br/>
	 * <br/>
	 * 需上傳447<br/>
	 * 100中文字
	 **/
	public void setReasonDrc(String value) {
		this.reasonDrc = value;
	}

	/**
	 * 取得核准日
	 * <p/>
	 * 於核准時填入
	 */
	public Date getDataDate() {
		return this.dataDate;
	}

	/**
	 * 設定核准日
	 * <p/>
	 * 於核准時填入
	 **/
	public void setDataDate(Date value) {
		this.dataDate = value;
	}

	public void setNotesUp(String notesUp) {
		this.notesUp = notesUp;
	}

	public String getNotesUp() {
		return notesUp;
	}

	/**
	 * 設定本案實際簽約額度低於核准額度
	 **/
	public void setIsSignAmtLowerApplyAmt(String isSignAmtLowerApplyAmt) {
		this.isSignAmtLowerApplyAmt = isSignAmtLowerApplyAmt;
	}

	/**
	 * 取得本案實際簽約額度低於核准額度
	 **/
	public String getIsSignAmtLowerApplyAmt() {
		return isSignAmtLowerApplyAmt;
	}

	/**
	 * 設定實際簽約額度有擔保
	 **/
	public void setSignAmt_S(BigDecimal signAmt_S) {
		this.signAmt_S = signAmt_S;
	}

	/**
	 * 取得實際簽約額度有擔保
	 **/
	public BigDecimal getSignAmt_S() {
		return signAmt_S;
	}

	/**
	 * 設定實際簽約額度無擔保
	 **/
	public void setSignAmt_N(BigDecimal signAmt_N) {
		this.signAmt_N = signAmt_N;
	}

	/**
	 * 取得實際簽約額度無擔保
	 **/
	public BigDecimal getSignAmt_N() {
		return signAmt_N;
	}
}
