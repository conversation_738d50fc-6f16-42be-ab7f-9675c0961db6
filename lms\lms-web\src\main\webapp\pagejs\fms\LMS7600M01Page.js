
var initDfd = $.Deferred(), inits = {
    fhandle: "lms7600m01formhandler",
    ghandle: "lms7600gridhandler"
};

//select source
var useCd_s = CommonAPI.loadCombos(["LandUse1"]);
var useType_s = CommonAPI.loadCombos(["LandUse21","LandUse22"]);
var landType_s = CommonAPI.loadCombos(["cms1010_useKind1","cms1010_useKind2"]);
var landKind_s = CommonAPI.loadCombos(["cms1010_landkind"]);
var ctlType_s = CommonAPI.loadCombos(["lms7600_ctlType"]);
var elFlag_s = CommonAPI.loadCombos(["lms7600_elFlag"]);
var adoptFg_s = CommonAPI.loadCombos(["lms7600_adoptFg"]);
var cstReason_s = CommonAPI.loadCombos(["lms7600_cstReason"]);

var Action = {
	_isLoad: false,
	_initItem: function(){
	    $("[name='useCd']").setItems({
	        item: useCd_s.LandUse1,
	        format: "{key}"
	    });
	    $("[name='landKind']").setItems({
	        item: landKind_s.cms1010_landkind,
	        format: "{key}"
	    });
	    $("[name='ctlType']").setItems({
	        item: ctlType_s.lms7600_ctlType,
	        format: "{key}"
	    });
	    $("[name='elFlag']").setItems({
	        item: elFlag_s.lms7600_elFlag,
	        format: "{key}"
	    });
	    $("#adoptFg").setItems({
	    	item: adoptFg_s.lms7600_adoptFg,
	    	format: "{value} - {key}"
	    });
	    $("#adoptFgOn").setItems({
	    	item: adoptFg_s.lms7600_adoptFg,
	    	format: "{value} - {key}"
	    });
	    $("[name='cstReason']").setItems({
	        item: cstReason_s.lms7600_cstReason,
	        format: "{key}"
	    });    
	},
	_initEvent: function(){
		$("#useCd").change(function(k, v){			
            var value = $(this).val();
			
            if (value == "1") {
                $("#useType").setItems({
                    item: useType_s.LandUse21,
                    format: "{key}"
                });
                $("#landType").setItems({
                    item: landType_s.cms1010_useKind1,
                    format: "{key}"
                });
            } else if(value == "2") {
                $("#useType").setItems({
                    item: useType_s.LandUse22,
                    format: "{key}"
                });
                $("#landType").setItems({
                    item: landType_s.cms1010_useKind2,
                    format: "{key}"
                });
			}
        });
		$("#itemSpan_adoptFg").change(function(){
			if($("[name='adoptFg'][value='4']").is(":checked")){
				$("[name='adoptFg']").filter("[value !='4']").attr("checked", false).attr("disabled", true);
				$("#rateAdd").val('0');
				$("#custRoa").val('0');
				$("#relRoa").val('0');
			} else {
				$("[name='adoptFg']").attr("disabled", false);
				$("[name='adoptFg']").filter("[value='4']").attr("checked", false);
			} 
		});
		$("#rateAdd").blur(function () {
		    if ($.trim($(this).val()).length == 0) {
		        $(this).val("0");
		    }
		});
		$("#custRoa").blur(function () {
		    if ($.trim($(this).val()).length == 0) {
		        $(this).val("0");
		    }
		});
		$("#relRoa").blur(function () {
		    if ($.trim($(this).val()).length == 0) {
		        $(this).val("0");
		    }
		});
	},
	_initForm: function(){
		$.form.init({
			formHandler:"lms7600m01formhandler", 
			formAction:'queryL140mm4a',	//'query',
			loadSuccess:function(json){
				$('body').injectData(json);
				
				var listOn = json.adoptFgOn.split("|");
				for (var i = 0; i < listOn.length; i++) {
					var adoptFgOn = listOn[i];
					$("[name='adoptFgOn'][value=" + adoptFgOn + "]").attr("checked", true);
				}
				
				var list = json.adoptFg.split("|");
				for (var i = 0; i < list.length; i++) {
					var adoptFg = list[i];
					$("[name='adoptFg'][value=" + adoptFg + "]").attr("checked", true);
				}
				
				if($("[name='adoptFg'][value='4']").is(":checked")){
					$("[name='adoptFg']").filter("[value !='4']").attr("checked", false).attr("disabled", true);
				} else {
					$("[name='adoptFg']").attr("disabled", false);
					$("[name='adoptFg']").filter("[value='4']").attr("checked", false);
				}
				UI(json);
				Action._initEvent();
			}
		});
		$("#before").find('select, input').each(function(){
			$(this).attr("disabled", true);
		});
	},
	_init: function(){
        if (!this._isLoad) {
            this._initItem();
            this._initForm();
            this._isLoad = true;            
        } else {
            this._reloadGrid();
        }
    },
    getSelectItem: function(){
        var data = [];
        $("#itemSpan_adoptFg").find("[name='adoptFg']:checked").each(function(v, k){
            data.push($(k).val());
        });
        return data.join("|");
    }
}

// 驗證readOnly狀態
function checkReadonly(){
    var auth = (responseJSON ? responseJSON.Auth : {}); // 權限
    if (auth.readOnly || responseJSON.mainDocStatus != "01O") {
        return true;
    }
    return false;
}

function UI(json){
	if(json.useCdOn== "1") {
        $("#useTypeOn").setItems({
            item: useType_s.LandUse21,
            format: "{key}"
        });
        $("#landTypeOn").setItems({
            item: landType_s.cms1010_useKind1,
            format: "{key}"
        });
    } else if(json.useCdOn == "2") {
        $("#useTypeOn").setItems({
            item: useType_s.LandUse22,
            format: "{key}"
        });
        $("#landTypeOn").setItems({
            item: landType_s.cms1010_useKind2,
            format: "{key}"
        });
	}
	$("#useTypeOn").val(json.useTypeOn);
	$("#landTypeOn").val(json.landTypeOn);
	$("[name='isLegalOn'][value='" + json.isLegalOn + "']:radio").attr("checked", "checked");
	
	if(json.useCd== "1") {
        $("#useType").setItems({
            item: useType_s.LandUse21,
            format: "{key}"
        });
        $("#landType").setItems({
            item: landType_s.cms1010_useKind1,
            format: "{key}"
        });
    } else if(json.useCd == "2") {
        $("#useType").setItems({
            item: useType_s.LandUse22,
            format: "{key}"
        });
        $("#landType").setItems({
            item: landType_s.cms1010_useKind2,
            format: "{key}"
        });
	}
	$("#useType").val(json.useType);
	$("#landType").val(json.landType);
	$("[name='idleLand'][value='" + json.idleLand + "']:radio").attr("checked", "checked");
	$("[name='isLegal'][value='" + json.isLegal + "']:radio").attr("checked", "checked");		
}

function checkAdoptFg(){
	var cnt=0;
	$("#itemSpan_adoptFg").find("[name=adoptFg]:checked").each(function(v, k){
		cnt++;
    });
	if(cnt>0){
		return false;
	} else {
		return true;
	}
}

function checkIsLegal(){

	if ($.trim($("#rateAdd").val()).length == 0) {
		$("#rateAdd").val("0");
    }
	
	if ($.trim($("#custRoa").val()).length == 0) {
		$("#custRoa").val("0");
    }
	
	if ($.trim($("#relRoa").val()).length == 0) {
		$("#relRoa").val("0");
    }
	
//	if (checkAdoptFg()) {
//		return CommonAPI.showErrorMessage(i18n.lms7600m01['L140MM4A.adoptFg'] +
//                i18n.lms7600m01['cantEmpty']);
//    }

	$.ajax({
        handler: inits.fhandle,
        action: "checkIsLegal",
        data: {
        	mainId: $("#mainId").val(),
        	ctlType:$("#ctlType").val(),
        	fstDate:$("#fstDate").val(),
        	lstDate:$("#lstDate").val(),
        	cstDate:$("#cstDate").val(),
        	adoptFg:Action.getSelectItem,
        	rateAdd:$("#rateAdd").val(),
        	custRoa:$("#custRoa").val(),
        	relRoa:$("#relRoa").val()
        },
        success: function(obj){
        	if(obj.isLegal==""){
        		$("[name='isLegal']:radio").attr("checked", false);
        	} else {
        		$("[name='isLegal'][value='" + obj.isLegal + "']:radio").attr("checked", "checked");
        	}
        }
	})
}

$(document).ready(function(){
	var tabForm = $("#mainPanel");
	Action._init();
	$("#check1").show();
	
    if (checkReadonly()) {
        $(".readOnlyhide").hide();
        $("form").lockDoc();
		_openerLockDoc="1";
    }
    
    $("#applyOnLine").click(function(){
       //初始化    		   
	   $.ajax({
            async: false,
            handler: inits.fhandle,
            data: {
                formAction: "queryOnLine",
                mainId: $("#mainId").val(),
                cntrNo: $("#cntrNo").val(),
				oid: responseJSON.oid
            },
            success: function(json){
				Action._initForm();
            }
        });
    });
    
    $("#applyIsLegal").click(function(){
    	checkIsLegal();
     });
	
	// 呈主管覆核 選授信主管人數
    $("#numPerson").change(function(){
        $('#bossItem').empty();
        var value = $(this).val();
        if (value) {
            var html = '';
            for (var i = 1; i <= value; i++) {
                var name = 'boss' + i;
                html += i + '. '
                // || '授信主管'
                html += '<select id="' + name + '" name="boss"' +
                '" class="required" CommonManager="kind:2;type:2" />';
                html += '<br/>';
            }
            $('#bossItem').append(html).find('select').each(function(){
                $(this).setItems({
                    item: item,
                    format: "{value} {key}"
                });
            });
        }     
    });
	
	var btn = $("#buttonPanel");
    btn.find("#btnSave").click(function(showMsg){
        saveData(true);
    }).end().find("#btnSend").click(function(){
        saveData(false, sendBoss);	
    }).end().find("#btnCheck").click(function(){
        openCheck();
    }).end().find("#btnPrint").click(function(){
        if (checkReadonly()) {
            printAction();
        }
        else {
            // saveBeforePrint=執行列印將自動儲存資料，是否繼續此動作?
            CommonAPI.confirmMessage(i18n.def["saveBeforePrint"], function(b){
                if (b) {
                    saveData(false, printAction);
                }
            });
        }
    });
	
	// 儲存的動作
    function saveData(showMsg, tofn){
		
		// 為檢查UI的值是否皆無異常
		if ($("#mainPanel").valid() == false) {
			return;
		}

		FormAction.open = true;
		$.ajax({
			handler: inits.fhandle,
			data: $.extend($("#after").serializeData(),{// 把資料轉成json
				formAction: "saveL140mm4a",
				oid: responseJSON.oid,
				page: responseJSON.page,
				txCode: responseJSON.txCode,
				showMsg: showMsg,
				adoptFg:Action.getSelectItem
			}),
			success: function(obj){
				if (responseJSON.page == "01") {
					$('body').injectData(obj);
				}
				
				Action._initForm();
				
				CommonAPI.triggerOpener("gridview", "reloadGrid");
				if ($("#mainOid").val()) {
					setRequiredSave(false);
				}
				else {
					setRequiredSave(true);
				}
				
				// 執行列印
				if (!showMsg && tofn) {
					tofn();
				}
			}
		});
    }
	
	var item;
	// 呈主管 - 編製中
    function sendBoss(){
        $.ajax({
            handler: inits.fhandle,
            action: "checkData",
            data: {},
            success: function(json){
                $('#managerItem').empty();
                $('#bossItem').empty();
                item = json.bossList;
                var bhtml = '1. <select id="boss1" name="boss" class="required" CommonManager="kind:2;type:2"/>';
                $('#bossItem').append(bhtml).find('select').each(function(){
                    $(this).setItems({
                        item: item,
                        format: "{value} {key}"
                    });
                });
                var html = '<select id="manager" name="manager" class="required" CommonManager="kind:2;type:2" />';
                $('#managerItem').append(html).find('select').each(function(){
                    $(this).setItems({
                        item: item,
                        format: "{value} {key}"
                    });
                });
                
                // L140MM1B.message27=是否呈主管覆核？
                CommonAPI.confirmMessage(i18n.lms7600m01["L140MM4B.message01"], function(b){
                    if (b) {
                        $("#selectBossBox").thickbox({
                            // L140MM1B.bt14=覆核
                            title: i18n.lms7600m01['approve'],
                            width: 500,
                            height: 300,
                            modal: true,
                            readOnly: false,
                            valign: "bottom",
                            align: "center",
                            i18n: i18n.def,
                            buttons: {
                                "sure": function(){
                                
                                    var selectBoss = $("select[name^=boss]").map(function(){
                                        return $(this).val();
                                    }).toArray();
                                    
                                    for (var i in selectBoss) {
                                        if (selectBoss[i] == "") {
                                            // 請選擇授信主管
                                            return CommonAPI.showErrorMessage(i18n.lms7600m01['checkSelect'] +
                                            i18n.lms7600m01['L140MM4B.bossId']);
                                        }
                                    }
                                    if ($("#manager").val() == "") {
                                        // 請選擇經副襄理
                                        return CommonAPI.showErrorMessage(i18n.lms7600m01['checkSelect'] +
                                        i18n.lms7600m01['L140MM4B.managerId']);
                                    }
                                    // 驗證是否有重複的主管
                                    if (checkArrayRepeat(selectBoss)) {
                                        // 主管人員名單重複請重新選擇
                                        return CommonAPI.showErrorMessage(i18n.lms7600m01['L140MM4B.message02']);
                                    }
                                    
                                    flowAction({
                                        page: responseJSON.page,
                                        saveData: true,
                                        selectBoss: selectBoss,
                                        manager: $("#manager").val()
                                    });
                                    $.thickbox.close();
                                    
                                },
                                
                                "cancel": function(){
                                    $.thickbox.close();
                                }
                            }
                        });
                    }
                });
            }
        });
    }
	
	// 待覆核 - 覆核
    function openCheck(){
        $("#openCheckBox").thickbox({ // 使用選取的內容進行彈窗
            title: i18n.lms7600m01['approve'],
            width: 100,
            height: 100,
            modal: true,
            readOnly: false,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var val = $("[name=checkRadio]:checked").val();
                    if (!val) {
                        return CommonAPI.showMessage(i18n.lms7600m01['checkSelect']);
                    }
                    $.thickbox.close();
                    switch (val) {
                        case "1":
                            // 一般退回到編製中01O
                            // 該案件是否退回經辦修改？要退回請按【確定】，不退回請按【取消】
                            CommonAPI.confirmMessage(i18n.lms7600m01['L140MM4B.message03'], function(b){
                                if (b) {
                                    flowAction({
                                        flowAction: false
                                    });
                                }
                            }); 
                            break;
                        case "3":
                            // 該案件是否確定執行核定作業
                            CommonAPI.confirmMessage(i18n.lms7600m01['L140MM4B.message04'], function(b){
                                if (b) {
				                    flowAction({
				                        flowAction: true,
				                        checkDate: CommonAPI.getToday()//forCheckDate
				                    });
                                }
                            });
                            break;
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
	
	function flowAction(sendData){
        $.ajax({
            handler: inits.fhandle,
            data: $.extend({
                formAction: "flowAction",
                mainOid: $("#mainOid").val()
            }, (sendData || {})),
            success: function(){
                CommonAPI.triggerOpener("gridview", "reloadGrid");
				window.close();
            }
        });
    }

	// 列印動作
    function printAction(){
        $.form.submit({
            url: "../../simple/FileProcessingService",
            target: "_blank",
            data: {
                mainId: responseJSON.mainId,
                mainOid: responseJSON.oid,
                fileDownloadName: "lms7600r01.pdf",
                serviceName: "lms7600r01rptservice"
            }
        });
    }
	
	// 檢查陣列內容是否重複
    function checkArrayRepeat(arrVal){
        var newArray = [];
        for (var i = arrVal.length; i--;) {
            var val = arrVal[i];
            if ($.inArray(val, newArray) == -1) {
                newArray.push(val);
            }
            else {
                return true;
            }
        }
        return false;
    }
});