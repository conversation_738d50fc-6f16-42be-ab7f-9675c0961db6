package com.mega.eloan.cls.dc.util;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang.CharUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.mega.eloan.lms.dc.util.TextDefine;
import com.mega.eloan.lms.dc.util.Util;

/**
 * <pre>
 * ClsDXLUtil
 * </pre>
 * 
 * @since 2013/2/5
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/2/5,Bang,new
 *          </ul>
 */
public class ClsDXLUtil {
	private static Logger logger = LoggerFactory.getLogger(ClsDXLUtil.class);
	// 目前文件狀態
	private static Map<String, String> docStatus_Map = new HashMap<String, String>();
	// 連保人/保證人
	//private static Map<String, String> guarantorType_Map = new HashMap<String, String>();
	// 轉換中文數字為數值
	private static Map<String, String> numC2E_Map = new HashMap<String, String>();
	// 現行額度-是否循環使用
	private static Map<String, String> reUse_Map = new HashMap<String, String>();

	// -------------------------------------------------------------------------------------
	// CLS新增
	// -------------------------------------------------------------------------------------
	// 現住房屋
	private static Map<String, String> house_Map = new HashMap<String, String>();
	// 行業別
	private static Map<String, String> jobKind_Map = new HashMap<String, String>();
	// 利率變動方式
	private static Map<String, String> rateChgWay2_Map = new HashMap<String, String>();
	// 出國地區
	private static Map<String, String> stdCountry_Map = new HashMap<String, String>();
	// 學歷
	private static Map<String, String> edu_Map = new HashMap<String, String>();
	// 婚姻狀況
	private static Map<String, String> marry_Map = new HashMap<String, String>();
	// 個金服務單位:其他收入項目
	private static Map<String, String> othType_Map = new HashMap<String, String>();
	// 個金償債能力:其他收入項目
	private static Map<String, String> oIncome_Map = new HashMap<String, String>();

	// 目前文件狀態FCLS102M05/FCLS102M06專用
	private static Map<String, String> docStatus102_Map = new HashMap<String, String>();
	
	public static void init() {
		StringBuffer sbCode = new StringBuffer();
		StringBuffer sbMap = new StringBuffer();
		// 目前文件狀態
		sbCode.setLength(0);
		sbMap.setLength(0);
		sbCode.append("1;2;3;4;9");
		sbMap.append("010;020;030;040;060");
		docStatus_Map = initMap(sbCode.toString(), sbMap.toString());
		sbCode.append("1;2;3;4;5;7;9");
		sbMap.append("010;020;030;040;L1C;07O;060");
		docStatus102_Map = initMap(sbCode.toString(), sbMap.toString());
		// 連保人/保證人
/*		sbCode.setLength(0);
		sbMap.setLength(0);
		sbCode.append("G;I");// 01-18先將傳入值trim(),無論空白或空白字串均轉為1
		sbMap.append("1;2");
		guarantorType_Map = initMap(sbCode.toString(), sbMap.toString());*/
		// 轉換中文數字為數值
		sbCode.setLength(0);
		sbMap.setLength(0);
		sbCode.append("一;二;三;四;五;六;七;八;九");
		sbMap.append("1;2;3;4;5;6;7;8;9");
		numC2E_Map = initMap(sbCode.toString(), sbMap.toString());
		// 現行額度-是否循環使用
		sbCode.setLength(0);
		sbMap.setLength(0);
		sbCode.append("N;Y;非循環;循環");
		sbMap.append("1;2;1;2");
		reUse_Map = initMap(sbCode.toString(), sbMap.toString());

		// -------------------------------------------------------------------------------------
		// CLS新增
		// -------------------------------------------------------------------------------------
		// 現住房屋
		sbCode.setLength(0);
		sbMap.setLength(0);
		sbCode.append("自有，無貸款;自有，有貸款;配偶所有;父母親所有;宿舍;租賃");
		sbMap.append("1;2;3;4;5;6");
		house_Map = initMap(sbCode.toString(), sbMap.toString());
		// 行業別
		sbCode.setLength(0);
		sbMap.setLength(0);
		sbCode.append("公;軍;製造;營造;金融;資訊;保險;個人服務;其他");
		sbMap.append("01;02;03;04;05;06;07;08;99");
		jobKind_Map = initMap(sbCode.toString(), sbMap.toString());
		// 利率變動方式
		sbCode.setLength(0);
		sbMap.setLength(0);
		sbCode.append("月;三個月;半年;九個月;年");
		sbMap.append("01;03;06;09;12");
		rateChgWay2_Map = initMap(sbCode.toString(), sbMap.toString());
		// 出國地區
		sbCode.setLength(0);
		sbMap.setLength(0);
		sbCode.append("1;2;3;4;5;6;7;8");
		sbMap.append("US;CA;GB;FR;DE;AU;NZ;JP");
		stdCountry_Map = initMap(sbCode.toString(), sbMap.toString());
		// 學歷
		sbCode.setLength(0);
		sbMap.setLength(0);
		sbCode.append("國中以下;國中;高中職;專科;大專;大學;碩士;博士;其他");
		sbMap.append("01;02;03;04;04;05;06;07;01");
		edu_Map = initMap(sbCode.toString(), sbMap.toString());
		// 婚姻狀況
		sbCode.setLength(0);
		sbMap.setLength(0);
		sbCode.append("未婚;已婚;離婚;歿");
		sbMap.append("1;2;4;5");
		marry_Map = initMap(sbCode.toString(), sbMap.toString());
		// 個金服務單位:其他收入項目
		sbCode.setLength(0);
		sbMap.setLength(0);
		sbCode.append("薪資收入;利息收入;營利收入;租賃收入;租金收入;權利金收入;自力耕作、漁、牧、林、礦收入;執行業務收入;著作人、稿費、版稅、鐘點費等;財產交易收入;競技、競賽及機會中獎獎金;退職收入;其他所得");
		sbMap.append("01;02;03;04;04;05;06;07;08;09;10;11;12");
		othType_Map = initMap(sbCode.toString(), sbMap.toString());
		// 個金償債能力:其他收入項目
		sbCode.setLength(0);
		sbMap.setLength(0);
		sbCode.append("薪資收入;營利收入;投資收入;租金收入;利息收入");
		sbMap.append("1;2;3;4;5");
		oIncome_Map = initMap(sbCode.toString(), sbMap.toString());

	}

	/**
	 * 轉換字串為Map
	 * 
	 * @param s1
	 *            String: 預計的key值
	 * @param s2
	 *            String: 預計的value值
	 * @return tmpMap HashMap
	 */
	private static Map<String, String> initMap(String s1, String s2) {
		Map<String, String> tmpMap = new HashMap<String, String>();
		String[] map1 = s1.split(";"); // Key
		String[] map2 = s2.split(";"); // value
		for (int i = 0; i < map1.length; i++) {
			tmpMap.put(map1[i], map2[i]);
		}
		return tmpMap;
	}

	/**
	 * 依<itemType>之<itemValue>值取得Map對應的轉換值
	 * 
	 * @param itemType
	 *            String: db2Xml中的type屬性欄位
	 * @param itemValue
	 *            String: 從.dxl中對應到db2XML後取得的值
	 * @return str String :相對應的值
	 */
	public static String codeConvert(String itemType, String itemValue)
			throws Exception {
		if ("docStatus".equalsIgnoreCase(itemType)) { // 目前文件狀態
			return getCodeMap(docStatus_Map, itemValue, TextDefine.EMPTY_STRING);
		}
		// 01-18請先將傳入值trim()再轉換,無論空白或空白字串均轉為1
/*		else if ("guarantorType".equalsIgnoreCase(itemType.trim())) { // 連保人/保證人
			return getCodeMapForSpace(guarantorType_Map, itemValue, "1");
		}*/
		// 01-18 不符合者請回傳原值
		else if ("numC2E".equalsIgnoreCase(itemType)) { // 轉換中文數字為數值
			return getCodeMapForDefault(numC2E_Map, itemValue, itemValue);
		}
		// 01-18 若以上皆非，請回傳原傳入值
		else if ("reUse".equalsIgnoreCase(itemType)) { // 現行額度-是否循環使用
			//20130503 modified by Sandra 明澤口述，若無值則統一回傳1，代表N
			return getCodeMapForSpace(reUse_Map, itemValue, "1");
		}

		// -------------------------------------------------------------------------------------
		// CLS新增
		// -------------------------------------------------------------------------------------
		else if ("house".equalsIgnoreCase(itemType)) { // 現住房屋
			return getCodeMapForSpace(house_Map, itemValue,
					TextDefine.EMPTY_STRING);
		}
		// 不符合者回傳99
		else if ("jobkind".equalsIgnoreCase(itemType)) { // 行業別
			return getCodeMapForDefault(jobKind_Map, itemValue, "99");
		}
		// 不符合者回傳原值
		else if ("rateChgWay2".equalsIgnoreCase(itemType)) { // 利率變動方式
			return getCodeMapForSpace(rateChgWay2_Map, itemValue, itemValue);
		}
		// 不符合者回傳原值
		else if ("stdCountry".equalsIgnoreCase(itemType)) { // 出國地區
			return getCodeMapForSpace(stdCountry_Map, itemValue, itemValue);
		}

		else if ("edu".equalsIgnoreCase(itemType)) { // 學歷
			return getCodeMapForDefault(edu_Map, itemValue, "01");
		}
		// 不符合者回傳原值
		else if ("marry".equalsIgnoreCase(itemType)) { // 婚姻狀況
			return getCodeMapForSpace(marry_Map, itemValue, itemValue);
		}
		// 不符合者回傳原值
		else if ("oIncome".equalsIgnoreCase(itemType)) { // 個金償債能力:其他收入項目
			return getCodeMapForSpace(oIncome_Map, itemValue, itemValue);
		}
		//FCLS102M05/FCLS102M06的文件狀態狀態
		else if ("docStatus102".equalsIgnoreCase(itemType)) { // 目前文件狀態
			return getCodeMap(docStatus102_Map, itemValue, TextDefine.EMPTY_STRING);
		}
		//L140M01M city_id areaId 第一碼S->E   /   L->B   /   R->D
		else if("cityId".equalsIgnoreCase(itemType) || "areaId".equalsIgnoreCase(itemType)) { // L140M01M的cityid
			if(StringUtils.isBlank(itemValue)){
				return itemValue;
			}else{
				itemValue = itemValue.replaceFirst("S", "E");
				itemValue = itemValue.replaceFirst("L", "B");
				itemValue = itemValue.replaceFirst("R", "D");
			}
			if("cityId".equalsIgnoreCase(itemType) ){
				return itemValue.substring(0,1);
			}else if("areaId".equalsIgnoreCase(itemType)){
				return itemValue;
			}
			
		}


		return itemValue;
	}

	/**
	 * 依Map及key值(itemValue無空白key)取得對應之value值 若值為其他資料回傳"ERR_"+原傳入值
	 * 
	 * @param codeMap
	 *            HashMap<String, String>要取值的Map
	 * @param itemValue
	 *            String : key值
	 * @param defValue
	 *            String : 預設值
	 * @return str String :相對應的值
	 */
	private static String getCodeMap(Map<String, String> codeMap,
			String itemValue, String defValue) throws Exception {
		String str = codeMap.get(itemValue);
		if (StringUtils.isBlank(str)) {
			// return defValue;
			// 01-18其他資料請回傳"ERR_"+原傳入值
			return "ERR_" + itemValue;
		}
		return str;
	}

	/**
	 * 依Map及key值(itemValue有空白key)取得對應之value值,若值為空白者回傳default值 無對應值則回傳原值
	 * 
	 * @param codeMap
	 *            HashMap<String, String>要取值的Map
	 * @param itemValue
	 *            String : key值
	 * @param defValue
	 *            String : 預設值
	 * @return str String :相對應的值
	 */
	private static String getCodeMapForSpace(Map<String, String> codeMap,
			String itemValue, String defValue) throws Exception {
		if (null != itemValue && itemValue.trim().length() == 0) {// 值為空白者
			return defValue;
		}
		String str = codeMap.get(itemValue);
		if (StringUtils.isBlank(str)) {// 無對應值
			return itemValue;
		}
		return str;
	}

	/**
	 * 依Map及key值(itemValue無空白key)取得對應之value值,若無對應值則回傳default值
	 * 
	 * @param codeMap
	 *            HashMap<String, String>要取值的Map
	 * @param itemValue
	 *            String : key值
	 * @param defValue
	 *            String : 預設值
	 * @return str String :相對應的值
	 */
	private static String getCodeMapForDefault(Map<String, String> codeMap,
			String itemValue, String defValue) throws Exception {
		String str = codeMap.get(itemValue);
		if (StringUtils.isBlank(str)) {
			return defValue;
		}
		return str;
	}

	/**
	 * 以固定格式(YYY-MM-DD)或DB(YYY/MM/DD)或DB(YYYMMDD)將民國年月日轉為西元年月日
	 * 
	 * @param rocDate
	 *            String :即將被轉換日期
	 * @return result String :轉換過後的日期
	 * @Example 1.097/02/29<br>
	 *          2.87/5/18<br>
	 *          3.100/11/22 17:40:22 2013-01-17
	 *          決議若有錯先丟回原值,至columnTruncate時若檢核有錯則寫出另一份檔案
	 */
	public static final String parseRocDate(String rocDate) throws Exception {
		try {
			String tmpRocDate = rocDate;
			boolean regFlag = true;// 判斷日期是否含有"/"或"-"
			boolean timeFlag = true;// 判斷日期是否含有":"
			boolean errFlag = false;// 判斷錯誤的日期格式
			if (StringUtils.isEmpty(rocDate)) {
				return "";
			}
			// 目前已知格式有"."-->1997.1.2,"()"-->059/08/20(067/07)
			errFlag = tmpRocDate.indexOf(".") > -1
					|| tmpRocDate.indexOf("(") > -1;
			if (errFlag) {
				return "ERR_" + rocDate;
			}
			regFlag = tmpRocDate.indexOf("/") > -1
					|| tmpRocDate.indexOf("-") > -1;
			timeFlag = tmpRocDate.indexOf(":") > -1;
			if (!regFlag && timeFlag) {
				int spaceD = tmpRocDate.indexOf(TextDefine.SPACE);
				int year = Integer
						.parseInt(tmpRocDate.substring(0, spaceD - 4)) + 1911;
				int month = Integer.parseInt(tmpRocDate.substring((spaceD - 4),
						spaceD - 2));
				int day = Integer.parseInt(tmpRocDate.substring((spaceD - 2),
						spaceD));

				if (!isValidDate(year, month, day)) {
					// return "";
					return "ERR_" + rocDate;
				} else {
					return String.valueOf(year) + "-"
							+ String.format("%02d", month) + "-"
							+ String.format("%02d", day)
							+ tmpRocDate.substring(spaceD);
				}
			}
			// YYYMMDD,YYYYMMDD
			else if (tmpRocDate.length() <= 8 && !regFlag) {
				int year = 0;
				if (tmpRocDate.length() > 4 && tmpRocDate.length() <= 7) {
					year = Integer.parseInt(tmpRocDate.substring(0,
							tmpRocDate.length() - 4)) + 1911;
				} else if (tmpRocDate.length() == 8) {
					year = Integer.parseInt(tmpRocDate.substring(0,
							tmpRocDate.length() - 4));
				} else {// 不合法的日期
						// return "";
					return "ERR_" + rocDate;
				}
				int month = Integer.parseInt(tmpRocDate.substring(
						tmpRocDate.length() - 4, tmpRocDate.length() - 2));
				int day = Integer.parseInt(tmpRocDate.substring(tmpRocDate
						.length() - 2));

				// 確認日期正確性
				if (!isValidDate(year, month, day)) {
					// return "";
					return "ERR_" + rocDate;
				} else {
					return String.valueOf(year) + "-"
							+ String.format("%02d", month) + "-"
							+ String.format("%02d", day);
				}
			} else {
				tmpRocDate = tmpRocDate.replaceAll("/", "-");
				String[] strCDate = tmpRocDate.split("-");
				// 確保為年-月-日 型態
				if (strCDate.length < 3) {
					return "ERR_" + rocDate;
				}
				int year = 0;
				if (strCDate[0].length() == 4) { // 西元年
					year = Integer.parseInt(strCDate[0]);
				} else if (strCDate[0].length() < 4) { // 民國年
					year = Integer.parseInt(strCDate[0]) + 1911;
				} else { // 不正確的日期
							// return "";
					return "ERR_" + rocDate;
				}
				int month = Integer.parseInt(strCDate[1]);
				int day = 0;
				// 日期會有兩種格式1.年/月/日 2:年/月/日(空一格)時:分:秒 要各別處理
				int time = strCDate[2].indexOf(":");
				int space = strCDate[2].indexOf(TextDefine.SPACE);
				if (time > 0) {
					// 取strCDate[2] 到" "之間的值,否則萬一day不足2位數會出錯
					day = Integer.parseInt(strCDate[2].substring(0, space));
				} else {
					day = Integer.parseInt(strCDate[2]);
				}
				// 確認日期正確性
				if (!isValidDate(year, month, day)) {
					// return "";
					return "ERR_" + rocDate;
				}
				// 把不足的位數補齊,否則matcher時會比對不到
				String tmpYear = String.format("%04d", year);
				String tmpMonth = String.format("%02d",
						Integer.parseInt(strCDate[1]));
				String tmpday = String.format("%02d", day);
				// 重新再組一次
				tmpRocDate = tmpYear + "-" + tmpMonth + "-" + tmpday;

				// 判斷是否為西元年
				if (strCDate[0].length() == 4) {
					if (time > 0) {
						return tmpRocDate + strCDate[2].substring(space);
					} else {
						return tmpRocDate;
					}
				}

				final Pattern ROC_DATE_PATTERN = Pattern
						.compile("^(\\d{1,5})\\D(\\d{2})\\D(\\d{2})$");
				Matcher matcher = null;
				if (time > 0) {
					matcher = ROC_DATE_PATTERN.matcher(tmpRocDate
							.split(TextDefine.SPACE)[0]);
				} else {
					matcher = ROC_DATE_PATTERN.matcher(tmpRocDate);
				}

				if (matcher.matches()) {
					String rocYear = matcher.group(1);
					String rocMonth = matcher.group(2);
					String rocDay = matcher.group(3);
					if (time > 0) {
						return rocYear + "-" + rocMonth + "-" + rocDay
								+ strCDate[2].substring(space);
					} else {
						return rocYear + "-" + rocMonth + "-" + rocDay;
					}
				}
			}
			// 01-18格式有誤無法正常轉換者，請回傳"ERR_"+原傳入值
			return "ERR_" + rocDate;
		} catch (Exception e) {
			System.out
					.println("ClsDXLUtil.java:parseRocDate【"+rocDate+"】 將民國年轉為合法的日期字串時出現錯誤:"
							+ e);
			return "ERR_" + rocDate;
		}
	}

	/**
	 * 檢核 日期數值正確性
	 * 
	 * @param year
	 *            int:西元年
	 * @param month
	 *            int: 月
	 * @param day
	 *            int: 日
	 * @return boolean
	 */
	private static final boolean isValidDate(int year, int month, int day) {
		if (month < 1 || month > 12) {
			return false;
		}
		if (day < 1 || day > 31) {
			return false;
		}
		if ((month == 4 || month == 6 || month == 9 || month == 11)
				&& (day == 31)) {
			return false;
		}
		if (month == 2) {
			boolean leap = (year % 4 == 0 && (year % 100 != 0 || year % 400 == 0));
			if (day > 29 || (day == 29 && !leap)) {
				return false;
			}
		}
		return true;
	}

	/**
	 * 授權等級
	 * 
	 * @param itemValue
	 *            :被轉換的字串
	 * @return String 轉換結果
	 * @TODO if (1 - 分行授權內->1)、(3 - 營運中心授權內->3) (2 - 分行授權外、4- 營運中心授權外->2)
	 *       如果CaseLevel_No等於9時都轉為1 else return itemValue
	 *       簽報書的grant規則
	 */
	public static final String getAuthLvl(String itemValue) {
		//20130422 Sandra依明澤、Rex的mail調整規則
				if(StringUtils.isBlank(itemValue)
						||itemValue.equalsIgnoreCase("9")){
					return "1";
				}else if(itemValue.equalsIgnoreCase("B")){
					return "3";
				}else{
					return "5";
				}
		}

	/**
	 * 針對簽報書114的授權等級置換
	 * 
	 * @param itemValue
	 *            :被轉換的字串
	 * @return String 轉換結果
	 * @TODO 簽報書的grant規則：當grant=3，則回傳3，其他為5
	 *       
	 */
	public static final String getAuthLvl114(String itemValue) {
		if (StringUtils.isNotBlank(itemValue)) {
				if(itemValue.equalsIgnoreCase("1")){
					return "1";
				}else if(itemValue.equalsIgnoreCase("3")){
					return "3";
				}else{
					return "5";
				}
		}
		return itemValue;
	}

	/**
	 * 針對簽報書dockind置換
	 * 
	 * @param itemValue
	 *            :被轉換的字串
	 * @return String 轉換結果
	 * @TODO 若grant=3，dockind=1；else dockind =2 ,
	 * 然後merge時，114要回頭更新dockind
	 *       
	 */
	public static final String getDockind4(String itemValue) {
		if (StringUtils.isNotBlank(itemValue)) {
				if(itemValue.equalsIgnoreCase("1") || itemValue.equalsIgnoreCase("3")){
					return "1";
				}else{
					return "2";
				}
		}
		return itemValue;
	}
	/**
	 * 針對簽報書dockind置換
	 * 
	 * @param itemValue
	 *            :被轉換的字串
	 * @return String 轉換結果
	 * @TODO 
	 * 其他審核書CaseLevel_No='9'   或'B'  的    dockind = 1 ；else dockind =2 ,
	 *       
	 */
	public static final String getDockind(String itemValue) {
				if(StringUtils.isBlank(itemValue)
						||itemValue.equalsIgnoreCase("9")
						||itemValue.equalsIgnoreCase("B")){
					return "1";
				}else{
					return "2";
				}
	}
	
	/**
	 * 將英數字轉成全形
	 * 
	 * @param itemValue
	 *            String
	 * @return 轉成全形後的結果
	 */
	public static String getFullChar(String itemValue) {
		if (StringUtils.isNotBlank(itemValue)) {
			String outStr = "";
			char[] chars = itemValue.toCharArray();
			int tranTemp = 0;
			for (int i = 0; i < chars.length; i++) {
				// 中文不處理
				// 作法一 超過這個應該都是中文字了…
				// if((int)chars[i] < '\200'){
				// 作法二:isAsciiAlphanumeric判斷是否為英數字
				if (CharUtils.isAsciiAlphanumeric(chars[i])) {
					tranTemp = (int) chars[i];
					if (tranTemp != 45) { // ASCII碼:45 是減號 -
						tranTemp += 65248; // 此數字是 Unicode編碼轉為十進位 和 ASCII碼的 差
					}
					outStr += (char) tranTemp;
				} else {
					outStr += (char) chars[i];
				}
			}
			return outStr;
		} else {
			return itemValue;
		}
	}

	/**
	 * 依傳入值參數num決定回傳的字串須在左方增加幾位零
	 * 
	 * @param itemValue
	 *            String
	 * @param num
	 *            int
	 * @return String
	 * @Example 如傳入值為(3456,2)則回傳003456
	 */
	public static String getFillLeftZero(String itemValue, int num) {
		if (StringUtils.isNotBlank(itemValue)) {
			for (int i = 0; i < num; i++) {
				StringBuffer sb = new StringBuffer();
				sb.append("0").append(itemValue);
				itemValue = sb.toString();
			}
		}
		return itemValue;
	}

	/**
	 * 若傳入值為"未評等"，則回傳"NA"；若為"免辦"，則回傳 "NO"；
	 * 其他傳入值若為"等"字或"級"字結尾，請去除"等"或"級"後回傳；若以上皆非，請回傳"Error"+原值
	 * 
	 * @param itemValue
	 *            String
	 * @return String
	 */
	public static String getGrade(String itemValue) {
		if (StringUtils.isNotBlank(itemValue)) {
			String value = itemValue.trim();
			if ("未評等".equals(value)) {
				return "NA";
			} else if ("免辦".equals(value)) {
				return "NO";
			} else if ("等".equals(value.substring(value.length() - 1))
					|| "級".equals(value.substring(value.length() - 1))) {
				return value.substring(0, value.length() - 1);
			} else {
				return "Error" + itemValue;
			}
		}
		return itemValue;
	}

	/**
	 * 依傳入參數num決定傳回的號碼
	 * 
	 * @param itemValue
	 *            String
	 * @param num
	 *            :號碼分類
	 * @return 若無值則回傳空字串
	 * @TODO 適用於身分證號、統一編號、護照號碼 [0]取第一碼到倒數第二碼 [1]取最後一碼
	 */
	public static String getIdn11(String itemValue, int num) {
		if (StringUtils.isNotBlank(itemValue)) {
			switch (num) {
			case 0:
				return itemValue.substring(0, itemValue.length() - 1);
			case 1:
				return itemValue.substring(itemValue.length() - 1);
			}
		}
		return "";
	}

	/**
	 * 將數字中的千分號去除
	 * 
	 * @param itemValue
	 *            String
	 * @return String 2013-01-09 新增去除"元"字
	 */
	public static String getMoney(String itemValue) {
		if (StringUtils.isNotBlank(itemValue)) {
			int idx = itemValue.indexOf("元");
			if (idx > -1) {
				itemValue = itemValue.substring(0, idx);
			}
			return itemValue.trim().replace(",", TextDefine.EMPTY_STRING);
		} else {
			return itemValue;
		}
	}

	/*
	 * 095營業部(兆)授字第183號 [0]年度：取前三碼轉為西元年
	 * [1]分行別：去掉前三碼年度，取(兆)前段字串，如例：營業部,無符合的分行代碼，則回傳"ERROR"+字串
	 * [2]流水號：取"授字第"至"號"之前數值:183 依傳入參數num決定回傳第幾個值 96年以前，年度+分行+"授字第"+流水號+"號"
	 * 96年以後，年度+分行+"(兆)授字第"+流水號+"號"
	 */
	/**
	 * 取得案件號碼相關資料:年度 or 分行 or 流水號
	 * 
	 * @param itemValue
	 *            String
	 * @param num
	 *            int:itemNum
	 * @return
	 * @Example 095營業部(兆)授字第183號 or 098營業部授字第203號
	 */
	public static String getProjectNo(String itemValue, int num) {
		if (StringUtils.isBlank(itemValue)) {
			return itemValue;
		}
		// 年度
		if (num == 0) {
			String tmpValue = itemValue.substring(0, 3);
			if (Util.isNumeric(tmpValue)) {
				return Integer
						.toString((Integer.valueOf(tmpValue.trim()) + 1911));
			} else {
				return itemValue;
			}
		} else if (num == 1) { // 分行
			// 以"("是否存在來判別,年度有可能會不準
			int idx = itemValue.indexOf("(");
			if (idx >= 0) {
				return itemValue.substring(3, idx);
			} else {
				int idx1 = itemValue.indexOf("授");
				return itemValue.substring(3, idx1);
			}
		} else if (num == 2) { // 流水號
			int idx = itemValue.indexOf("第");
			if (idx >= 0) {
				return itemValue.substring(idx + 1, itemValue.length() - 1);
			}
		}
		return itemValue;
	}

	/**
	 * 取得利率基礎-敘做利率最小(大)值
	 * 
	 * @param itemValue
	 * @param num
	 * @return
	 * @Example 傳入值可能有以下兩種：2.845 或1.581～1.691
	 */
	public static String getReRate(String itemValue, int num) {
		if (StringUtils.isBlank(itemValue)) {
			return itemValue;
		}
		int idx = itemValue.indexOf("～");
		if (num == 0) {
			// 若字串有"～",num[0]為前值Ex:1.581
			// 若字串無"～",=原傳入值Ex:2.845
			if (idx >= 0) {
				return itemValue.substring(0, idx).trim();
			} else {
				return itemValue;
			}
		} else if (num == 1) {
			// 若字串有"～",num[1]為後值Ex:1.691
			// 若字串無"～",=原傳入值Ex:2.845
			if (idx >= 0) {
				return itemValue.substring(idx + 1, itemValue.length()).trim();
			} else {
				return itemValue;
			}
		}
		return itemValue;
	}

	/**
	 * 依傳入參數num決定傳回字串的前幾碼
	 * 
	 * @param itemValue
	 *            String
	 * @param num
	 *            :需切割的字數
	 * @return 若無值則回傳itemValue
	 */
	public static String getSubstr(String itemValue, int num) {
		try {
			if (StringUtils.isNotBlank(itemValue)) {
				return itemValue.substring(0, num);
			} else {
				return itemValue;
			}
		} catch (Exception e) {
			System.out
					.println("ClsDXLUtil.java:searchId 傳入【"+itemValue+"】參數"+num+"決定傳回字串的前幾碼時出現錯誤:"
							+ e);
			return "ERR_" + itemValue;
		}
	}

	/**
	 * 從dxl中找尋姓名與ID的對照資料
	 * 
	 * @param dxlSB
	 *            String: 已轉成字串的dxl檔
	 * @param strName
	 *            員工姓名
	 * @return rtstr 員工編號 :回查DXL取回員工編號，並於左邊補零至6碼
	 */
	public static String searchId(String dxlSB, String empName) {
		try {
			String rtstr = "";
			if (empName == null || empName.length() == 0) {
				return rtstr;
			}
			String tag = "CN=" + empName;
			int idx1 = dxlSB.indexOf(tag);
			if (idx1 > 0) {
				String endTag = "/OU=";
				int idx2 = dxlSB.indexOf(endTag, idx1);
				rtstr = dxlSB.substring(idx1 + tag.length(), idx2).trim();
				rtstr = String.format("%06d", Integer.valueOf(rtstr));
			} else {
				return "ERR_" + empName;
			}
			return rtstr;
		} catch (Exception e) {
			logger.error("ClsDXLUtil.java:searchId 取回員工編號【"+empName+"】時出現錯誤:" + e);
			return "ERR_" + empName;
		}
	}

	/**
	 * 截取員工編號
	 * 
	 * @param itemValue
	 *            String
	 * @return
	 */
	public static String getEmpID(String itemValue) {
		try {
			if (itemValue == null || itemValue.length() == 0) {
				return "";
			}
			String str = itemValue.trim();
			String[] s = str.split(TextDefine.SPACE);
			if (s.length >= 2) {
				String sId = s[s.length - 1];
				if (sId.length() == 5) {
					return sId;
				}
			}
			return "";
		} catch (Exception e) {
			logger.error("ClsDXLUtil.java:getEmpID 截取員工編號【"+itemValue+"】時出現錯誤:" + e);
			e.printStackTrace();
			return "ERR_" + itemValue;
		}
	}

	/**
	 * 截取員工姓名
	 * 
	 * @param itemValue
	 *            String
	 * @return
	 */
	public static final String getEmpName(String itemValue) {
		if (itemValue == null || itemValue.length() == 0) {
			return "";
		}
		String str = itemValue.trim();
		int idx = str.indexOf("(");
		if (idx >= 0) {
			return str.substring(0, idx).trim();
		}
		String[] s = str.split(" ");
		return s[0].trim();
	}

	/**
	 * 截取日期時間並將之轉換成西元年格式
	 * 
	 * @param input
	 * @return
	 */
	public static final String getDateTime(String input) throws Exception {
		if (input == null || input.length() == 0) {
			return "";
		}
		int idx1 = input.indexOf("(");
		int idx2 = input.indexOf(")");
		String s;
		if (idx1 > 0 && idx2 > 0 && idx2 > idx1) {
			s = input.substring(idx1 + 1, idx2);
		} else {
			return "";
		}
		String[] ss = s.split(TextDefine.SPACE);
		String strDate = parseRocDate(ss[0]);

		String strTime = ss[1].replaceAll(":", ".") + ".000000";
		return strDate + "-" + strTime; // timestamp
	} // end of getDateTime



	/**
	 * 將民國年月轉為西元年月
	 * 
	 * @param itemValue
	 *            String
	 * @return String
	 * @Example 092/04 :若無法轉換，請回傳"ERR_"+原傳入值
	 */
	public static String getYyyymm(String itemValue) throws Exception {
		try {
			if (StringUtils.isNotBlank(itemValue)) {
				itemValue = itemValue.replaceAll("/", "-").trim();
				String[] strDate = itemValue.split(TextDefine.SYMBOL_DASH);
				if (strDate.length == 2) {
					if (strDate[0].length() == 4) {// 西元年
						return itemValue;
					} else if (strDate[0].length() <= 3) {// 民國年
						//20130422 Sandra 原傳入格式為(y)yy/mm，傳回格式為yyyymm
						return String
						.valueOf((Integer.parseInt(strDate[0]) + 1911)+ strDate[1]);
						/*return String
								.valueOf((Integer.parseInt(strDate[0]) + 1911)
										+ TextDefine.SYMBOL_DASH + strDate[1]);*/
					} else {
						return "ERR_" + itemValue;
					}
				} else {// 不應該有日期
					return "ERR_" + itemValue;
				}
			}
		} catch (Exception e) {
			logger.error("ClsDXLUtil.java:getYyyymm【"+itemValue+"】 將民國年月轉為西元年月時出現錯誤:"
					+ e);
			e.printStackTrace();
			return "ERR_" + itemValue;
		}
		return itemValue;
	}

	/**
	 * 字串代碼:是.否.有.無.0.1轉成Y,N
	 * 
	 * @param itemValue
	 *            :被轉換的字串
	 * @return 轉換結果
	 */
	public static final String getYesNo(String itemValue) {
		if ("是".equals(itemValue) || "有".equals(itemValue)
				|| "0".equals(itemValue)) {
			return "Y";
		}
		if ("否".equals(itemValue) || "無".equals(itemValue)
				|| "1".equals(itemValue)) {
			return "N";
		}
		return itemValue;
	}

	/**
	 * 字串代碼轉換:1 & 是 轉成Y; 2 & 否 轉成N
	 * 
	 * @param itemValue
	 *            :被轉換的字串
	 * @return 轉換結果
	 */
	public static final String getYesNo2(String itemValue) {
		if ("是".equals(itemValue) || "1".equals(itemValue)) {
			return "Y";
		}
		if ("否".equals(itemValue) || "2".equals(itemValue)) {
			return "N";
		}
		return itemValue;
	}

	/**
	 * 字串代碼轉換:Y.有.是-->轉1, N.無.否-->轉2, N.A-->轉3
	 * 
	 * @param itemValue
	 *            :被轉換的字串
	 * @return 轉換結果
	 */
	public static final String getYn2Num(String itemValue) {
		if ("Y".equalsIgnoreCase(itemValue) || "有".equals(itemValue)
				|| "是".equals(itemValue)) {
			return "1";
		} else if ("N".equalsIgnoreCase(itemValue) || "無".equals(itemValue)
				|| "否".equals(itemValue)) {
			return "2";
		} else if ("N.A".equals(itemValue)) {
			return "3";
		}
		return itemValue;
	}

	/**
	 * 傳入值trim()後，長度大於零則為Y，否則為N
	 * 
	 * @param itemValue
	 * @return String
	 */
	public static String getYn1(String itemValue) {
		String value = itemValue.trim();
		if (value.length() > 0) {
			return "Y";
		} else {
			return "N";
		}
	}

	/**
	 * 字串代碼轉換 M男、F女
	 * 
	 * @param itemValue
	 *            :被轉換的字串
	 * @return 轉換結果
	 */
	public static final String getSexy(String itemValue) {
		if ("男".equals(itemValue)) {
			return "M";
		}
		if ("女".equals(itemValue)) {
			return "F";
		}
		return itemValue;
	}

	/**
	 * 代碼轉換:傳入值若不為空則給Y，否則為""
	 * 
	 * @param itemValue
	 * @return
	 */
	public static String getChgOther(String itemValue) {
		if (StringUtils.isBlank(itemValue)) {
			itemValue = "";
		} else {
			itemValue = "Y";
		}
		return itemValue;
	}

	/**
	 * 代碼轉換:若itemValue有值，則為B；無值，則為A
	 * 
	 * @param itemValue
	 * @return
	 */
	public static String getMateFlag(String itemValue) {
		if (StringUtils.isBlank(itemValue)) {
			itemValue = "A";
		} else {
			itemValue = "B";
		}
		return itemValue;
	}

	/**
	 * 代碼轉換:若值為"加"，則寫入P； 若值為"減"，則寫入M
	 * 
	 * @param itemValue
	 * @return
	 */
	public static String getPmFlag(String itemValue) {
		if ("加".equals(itemValue)) {
			return "P";
		} else if ("減".equals(itemValue)) {
			return "M";
		}
		return itemValue;
	}

	/**
	 * 是否為本行貸款 代碼轉換:值切左三碼若為017則放Y，其他為N
	 * 
	 * @param itemValue
	 * @return
	 */
	public static String getChgALoan(String itemValue) {
		if (StringUtils.isBlank(itemValue) || itemValue.length() < 3) {
			return "N";
		}
		String value = itemValue.substring(0, 3);
		if ("017".equals(value)) {
			return "Y";
		} else {
			return "N";
		}
	}

	/**
	 * 是否收取手續費 代碼轉換:值為"收取手續費",定義為"Y";值為"不取手續費",定義為"N"
	 * 
	 * @param itemValue
	 * @return
	 */
	public static String getChargeFlag(String itemValue) {
		if ("收取手續費".equals(itemValue)) {
			return "Y";
		} else if ("不取手續費".equals(itemValue)) {
			return "N";
		} else {
			return itemValue;
		}
	}

	/**
	 * 土地及建物擬/已設定/已投保 代碼轉換:若值為"擬",則寫入"1"； 若值為"已"，則寫入"2"
	 * 
	 * @param itemValue
	 * @return
	 */
	public static String getSetIns(String itemValue) {
		if ("擬".equals(itemValue)) {
			return "1";
		} else if ("已".equals(itemValue)) {
			return "2";
		}  else if ("應".equals(itemValue)) {//20130412 Sandra新增
			return "3";
		}else {
			return itemValue;
		}
	}

	/**
	 * 承諾存款代碼轉換:雇主 :1;仲介公司:2
	 * 
	 * @param itemValue
	 *            :被轉換的字串
	 * @return 轉換結果
	 */
	public static final String getAgency(String itemValue) {
		if ("雇主".equals(itemValue)) {
			return "1";
		} else if ("仲介公司".equals(itemValue)) {
			return "2";
		} else {
			return itemValue;
		}
	}

	/**
	 * (C120S01B,C101S01B)其他收入項目代碼轉換:
	 * 
	 * @param itemValue
	 *            :被轉換的字串
	 * @return 轉換結果
	 */
	public static final String getOthType(String itemValue) {
		try {
			StringBuffer sbValue = new StringBuffer();
			String tmp = itemValue;
			String[] spValue = tmp.split(";");
			for (int a = 0; a < spValue.length; a++) {
				String type = spValue[a];
				String tmpValue = getCodeMapForDefault(othType_Map, type, "12");
				sbValue.append(tmpValue).append("|");
			}
			return sbValue.toString().trim();
		} catch (Exception e) {
			logger.error("ClsDXLUtil.java:getOthType 【"+itemValue+"】其他收入項目代碼轉換時出現錯誤:"
					+ e);
			return "ERR_" + itemValue;
		}
	}

	/**
	 * 自然人 代碼轉換
	 * 
	 * @param bid
	 *            String:自然人Notes欄位之值
	 *            
	 * @return
	 * 
	 */
	public static String getNaturalFlag(String bid) {
		try {
			//2013-04-16明澤口頭告知，*開頭為自然人；#開頭為非自然人(公司戶)
			if(bid.startsWith("*")){
				return "Y";
			}
			if(bid.startsWith("#")){
				return "N";
			}
			char[] Chars = bid.toCharArray();
			Boolean engflag = true;
			char[] engChars = bid.substring(0, 3).toCharArray();
			for (int i = 0; i < engChars.length; i++) {
				if (!Util.isEnglish(engChars[i])) {
					engflag = false;
					break;
				}
			}
			// (台灣法人)若borrower_id為全數字8碼：N
			if (bid.length() == 8) {
				Boolean num = true;
				char[] chars = bid.toCharArray();
				for (int i = 0; i < chars.length; i++) {
					if (!CharUtils.isAsciiNumeric(chars[i])) {
						num = false;
						break;
					}
				}
				if (num) {
					return "N";
				}
			}
			// (OBU戶)若borrower_id為英文字母3碼+數字7碼：N
			else if (engflag) {
				Boolean numflag = true;
				char[] numChars = bid.substring(3).toCharArray();
				if (numChars.length != 7) {
					return bid;
				}
				for (int i = 0; i < numChars.length; i++) {
					if (!CharUtils.isAsciiNumeric(numChars[i])) {
						numflag = false;
						break;
					}
				}
				if (numflag) {
					return "N";
				} else {
					return bid;
				}
			}
			// (外籍人士在台申請內政部統一證號)若borrower_id為英文2碼+數字8碼：Y
			else if (Util.isEnglish(Chars[0]) && Util.isEnglish(Chars[1])) {
				Boolean numflag = true;
				char[] numChars = bid.substring(2).toCharArray();
				if (numChars.length != 8) {
					return bid;
				}
				for (int i = 0; i < numChars.length; i++) {
					if (!CharUtils.isAsciiNumeric(numChars[i])) {
						numflag = false;
						break;
					}
				}
				if (numflag) {
					return "Y";
				} else {
					return bid;
				}
			}
			// (台灣人)若borrower_id為英文字母1碼+數字9碼：Y
			else if (Util.isEnglish(Chars[0])) {
				Boolean numflag = true;
				char[] numChars = bid.substring(1).toCharArray();
				if (numChars.length != 9) {
					return bid;
				}
				for (int i = 0; i < numChars.length; i++) {
					if (!CharUtils.isAsciiNumeric(numChars[i])) {
						numflag = false;
						break;
					}
				}
				if (numflag) {
					return "Y";
				} else {
					return bid;
				}
			}
			// (內政部還沒有發統一證號時的編號規則)若borrower_id為8個數字+二個英文：Y
			else if (Util.isEnglish(Chars[Chars.length - 2])
					&& Util.isEnglish(Chars[Chars.length - 1])) {
				Boolean num = true;
				char[] numChars = bid.substring(0, Chars.length - 2)
						.toCharArray();
				if (numChars.length != 8) {
					return bid;
				}
				for (int i = 0; i < numChars.length; i++) {
					if (!CharUtils.isAsciiNumeric(numChars[i])) {
						num = false;
						break;
					}
				}
				if (num) {
					return "Y";
				}
			} else {
				return bid;
			}
			return bid;
		} catch (Exception e) {
			logger.error("ClsDXLUtil.java:getNaturalFlag 【"+bid+"】自然人代碼轉換時出現錯誤:"
					+ e);
			return "ERR_" + bid;
		}
	}

}
