/* 
 * L161S01D.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** RPA自動發查明細檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L161S01D", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class L161S01D extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID NOT NULL
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/**
	 * mainId
	 * <p/>
	 * NOT NULL
	 */
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 類別
	 * <p/>
	 * 01公司登記事項卡(商工資料)<br/>
	 * 02稅籍登記資料公示查詢<br/>
	 * 03身份證遺失檢查<br/>
	 * 04受監護輔助宣告(家事公告)<br/>
	 * 05銀行法及金控法利害關係人查詢
	 */
	@Size(max = 2)
	@Column(name = "TYPE", length = 2, columnDefinition = "CHAR(2)")
	private String type;

	/**
	 * 處理狀態
	 * <p/>
	 * A01:查詢中<br/>
	 * A02:查詢完成<br/>
	 * A03:查詢失敗
	 */
	@Size(max = 3)
	@Column(name = "STATUS", length = 3, columnDefinition = "CHAR(3)")
	private String status;

	/** 查詢時間 **/
	@Column(name = "QUERYTIME", columnDefinition = "TIMESTAMP")
	private Timestamp queryTime;

	/** 備註 **/
	@Size(max = 300)
	@Column(name = "MEMO", length = 300, columnDefinition = "VARCHAR(300)")
	private String memo;

	/** 回傳結果原因 **/
	@Size(max = 150)
	@Column(name = "REASON", length = 150, columnDefinition = "VARCHAR(150)")
	private String reason;

	/**
	 * 回傳結果內容
	 * <p/>
	 * Json格式
	 */
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(name = "DATA", columnDefinition = "CLOB")
	private String data;

	/**
	 * 回傳附檔1
	 * <p/>
	 * Ref BdocFile.oid
	 */
	@Size(max = 32)
	@Column(name = "DOCFILEOID", length = 32, columnDefinition = "CHAR(32)")
	private String docfileoid;

	/**
	 * 回傳附檔2
	 * <p/>
	 * Ref BdocFile.oid暫時沒用到
	 */
	@Size(max = 32)
	@Column(name = "DOCFILEOID2", length = 32, columnDefinition = "CHAR(32)")
	private String docfileoid2;

	/** 文件建立者 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(06)")
	private String creator;

	/** 文件建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 資料修改人(行編) **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(06)")
	private String updater;

	/** 資料修改日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 查詢理由1
	 * <p/>
	 * Type=01:企業統編<br/>
	 * Type=02:企業統編<br/>
	 * Type=03:負責人統編<br/>
	 * Type=04:負責人統編
	 */
	@Size(max = 30)
	@Column(name = "RPAQUERYREASON1", length = 30, columnDefinition = "VARCHAR(30)")
	private String rpaQueryReason1;

	/**
	 * 查詢理由2
	 * <p/>
	 * Type=03:發證民國年
	 */
	@Size(max = 30)
	@Column(name = "RPAQUERYREASON2", length = 30, columnDefinition = "VARCHAR(30)")
	private String rpaQueryReason2;

	/**
	 * 查詢理由3
	 * <p/>
	 * Type=03:月
	 */
	@Size(max = 30)
	@Column(name = "RPAQUERYREASON3", length = 30, columnDefinition = "VARCHAR(30)")
	private String rpaQueryReason3;

	/**
	 * 查詢理由4
	 * <p/>
	 * Type=03:日
	 */
	@Size(max = 30)
	@Column(name = "RPAQUERYREASON4", length = 30, columnDefinition = "VARCHAR(30)")
	private String rpaQueryReason4;

	/**
	 * 查詢理由5
	 * <p/>
	 * Type=03:發證地點
	 */
	@Size(max = 30)
	@Column(name = "RPAQUERYREASON5", length = 30, columnDefinition = "VARCHAR(30)")
	private String rpaQueryReason5;

	/**
	 * 查詢理由6
	 * <p/>
	 * Type=03:領補換別
	 */
	@Size(max = 30)
	@Column(name = "RPAQUERYREASON6", length = 30, columnDefinition = "VARCHAR(30)")
	private String rpaQueryReason6;

	/**
	 * 查詢理由7
	 * <p/>
	 * (保留用)
	 */
	@Size(max = 30)
	@Column(name = "RPAQUERYREASON7", length = 30, columnDefinition = "VARCHAR(30)")
	private String rpaQueryReason7;

	/**
	 * 查詢理由8
	 * <p/>
	 * (保留用)
	 */
	@Size(max = 30)
	@Column(name = "RPAQUERYREASON8", length = 30, columnDefinition = "VARCHAR(30)")
	private String rpaQueryReason8;

	/**
	 * 查詢理由9
	 * <p/>
	 * (保留用)
	 */
	@Size(max = 30)
	@Column(name = "RPAQUERYREASON9", length = 30, columnDefinition = "VARCHAR(30)")
	private String rpaQueryReason9;

	/**
	 * 回傳結果內容1
	 * <p/>
	 * (保留用)
	 */
	@Size(max = 100)
	@Column(name = "RPARETURNDATA1", length = 100, columnDefinition = "VARCHAR(100)")
	private String rpaReturnData1;

	/**
	 * 回傳結果內容2
	 * <p/>
	 * (保留用)
	 */
	@Size(max = 100)
	@Column(name = "RPARETURNDATA2", length = 100, columnDefinition = "VARCHAR(100)")
	private String rpaReturnData2;

	/**
	 * 回傳結果內容3
	 * <p/>
	 * (保留用)
	 */
	@Size(max = 100)
	@Column(name = "RPARETURNDATA3", length = 100, columnDefinition = "VARCHAR(100)")
	private String rpaReturnData3;

	/**
	 * 回傳結果內容4
	 * <p/>
	 * (保留用)
	 */
	@Size(max = 100)
	@Column(name = "RPARETURNDATA4", length = 100, columnDefinition = "VARCHAR(100)")
	private String rpaReturnData4;

	/**
	 * 回傳結果內容5
	 * <p/>
	 * (保留用)
	 */
	@Size(max = 100)
	@Column(name = "RPARETURNDATA5", length = 100, columnDefinition = "VARCHAR(100)")
	private String rpaReturnData5;

	/**
	 * 回傳結果內容6
	 * <p/>
	 * (保留用)
	 */
	@Size(max = 100)
	@Column(name = "RPARETURNDATA6", length = 100, columnDefinition = "VARCHAR(100)")
	private String rpaReturnData6;

	/**
	 * 回傳結果內容7
	 * <p/>
	 * (保留用)
	 */
	@Size(max = 100)
	@Column(name = "RPARETURNDATA7", length = 100, columnDefinition = "VARCHAR(100)")
	private String rpaReturnData7;

	/**
	 * 回傳結果內容8
	 * <p/>
	 * (保留用)
	 */
	@Size(max = 100)
	@Column(name = "RPARETURNDATA8", length = 100, columnDefinition = "VARCHAR(100)")
	private String rpaReturnData8;

	/**
	 * 回傳結果內容9
	 * <p/>
	 * (保留用)
	 */
	@Size(max = 100)
	@Column(name = "RPARETURNDATA9", length = 100, columnDefinition = "VARCHAR(100)")
	private String rpaReturnData9;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID NOT NULL
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID NOT NULL
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/**
	 * 取得mainId
	 * <p/>
	 * NOT NULL
	 */
	public String getMainId() {
		return this.mainId;
	}

	/**
	 * 設定mainId
	 * <p/>
	 * NOT NULL
	 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得類別
	 * <p/>
	 * 01公司登記事項卡(商工資料)<br/>
	 * 02稅籍登記資料公示查詢<br/>
	 * 03身份證遺失檢查<br/>
	 * 04受監護輔助宣告(家事公告)<br/>
	 * 05銀行法及金控法利害關係人查詢 06地政士
	 */
	public String getType() {
		return this.type;
	}

	/**
	 * 設定類別
	 * <p/>
	 * 01公司登記事項卡(商工資料)<br/>
	 * 02稅籍登記資料公示查詢<br/>
	 * 03身份證遺失檢查<br/>
	 * 04受監護輔助宣告(家事公告)<br/>
	 * 05銀行法及金控法利害關係人查詢
	 **/
	public void setType(String value) {
		this.type = value;
	}

	/**
	 * 取得處理狀態
	 * <p/>
	 * A01:查詢中<br/>
	 * A02:查詢完成<br/>
	 * A03:查詢失敗
	 */
	public String getStatus() {
		return this.status;
	}

	/**
	 * 設定處理狀態
	 * <p/>
	 * A01:查詢中<br/>
	 * A02:查詢完成<br/>
	 * A03:查詢失敗
	 **/
	public void setStatus(String value) {
		this.status = value;
	}

	/** 取得查詢時間 **/
	public Timestamp getQueryTime() {
		return this.queryTime;
	}

	/** 設定查詢時間 **/
	public void setQueryTime(Timestamp value) {
		this.queryTime = value;
	}

	/** 取得備註 **/
	public String getMemo() {
		return this.memo;
	}

	/** 設定備註 **/
	public void setMemo(String value) {
		this.memo = value;
	}

	/** 取得回傳結果原因 **/
	public String getReason() {
		return this.reason;
	}

	/** 設定回傳結果原因 **/
	public void setReason(String value) {
		this.reason = value;
	}

	/**
	 * 取得回傳結果內容
	 * <p/>
	 * Json格式
	 */
	public String getData() {
		return this.data;
	}

	/**
	 * 設定回傳結果內容
	 * <p/>
	 * Json格式
	 **/
	public void setData(String value) {
		this.data = value;
	}

	/**
	 * 取得回傳附檔1
	 * <p/>
	 * Ref BdocFile.oid
	 */
	public String getDocfileoid() {
		return this.docfileoid;
	}

	/**
	 * 設定回傳附檔1
	 * <p/>
	 * Ref BdocFile.oid
	 **/
	public void setDocfileoid(String value) {
		this.docfileoid = value;
	}

	/**
	 * 取得回傳附檔2
	 * <p/>
	 * Ref BdocFile.oid暫時沒用到
	 */
	public String getDocfileoid2() {
		return this.docfileoid2;
	}

	/**
	 * 設定回傳附檔2
	 * <p/>
	 * Ref BdocFile.oid暫時沒用到
	 **/
	public void setDocfileoid2(String value) {
		this.docfileoid2 = value;
	}

	/** 取得文件建立者 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定文件建立者 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得文件建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定文件建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得資料修改人(行編) **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定資料修改人(行編) **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得資料修改日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定資料修改日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/**
	 * 取得查詢理由1
	 * <p/>
	 * Type=01:企業統編<br/>
	 * Type=02:企業統編<br/>
	 * Type=03:負責人統編<br/>
	 * Type=04:負責人統編
	 */
	public String getRpaQueryReason1() {
		return this.rpaQueryReason1;
	}

	/**
	 * 設定查詢理由1
	 * <p/>
	 * Type=01:企業統編<br/>
	 * Type=02:企業統編<br/>
	 * Type=03:負責人統編<br/>
	 * Type=04:負責人統編
	 **/
	public void setRpaQueryReason1(String value) {
		this.rpaQueryReason1 = value;
	}

	/**
	 * 取得查詢理由2
	 * <p/>
	 * Type=03:發證民國年
	 */
	public String getRpaQueryReason2() {
		return this.rpaQueryReason2;
	}

	/**
	 * 設定查詢理由2
	 * <p/>
	 * Type=03:發證民國年
	 **/
	public void setRpaQueryReason2(String value) {
		this.rpaQueryReason2 = value;
	}

	/**
	 * 取得查詢理由3
	 * <p/>
	 * Type=03:月
	 */
	public String getRpaQueryReason3() {
		return this.rpaQueryReason3;
	}

	/**
	 * 設定查詢理由3
	 * <p/>
	 * Type=03:月
	 **/
	public void setRpaQueryReason3(String value) {
		this.rpaQueryReason3 = value;
	}

	/**
	 * 取得查詢理由4
	 * <p/>
	 * Type=03:日
	 */
	public String getRpaQueryReason4() {
		return this.rpaQueryReason4;
	}

	/**
	 * 設定查詢理由4
	 * <p/>
	 * Type=03:日
	 **/
	public void setRpaQueryReason4(String value) {
		this.rpaQueryReason4 = value;
	}

	/**
	 * 取得查詢理由5
	 * <p/>
	 * Type=03:發證地點
	 */
	public String getRpaQueryReason5() {
		return this.rpaQueryReason5;
	}

	/**
	 * 設定查詢理由5
	 * <p/>
	 * Type=03:發證地點
	 **/
	public void setRpaQueryReason5(String value) {
		this.rpaQueryReason5 = value;
	}

	/**
	 * 取得查詢理由6
	 * <p/>
	 * Type=03:領補換別
	 */
	public String getRpaQueryReason6() {
		return this.rpaQueryReason6;
	}

	/**
	 * 設定查詢理由6
	 * <p/>
	 * Type=03:領補換別
	 **/
	public void setRpaQueryReason6(String value) {
		this.rpaQueryReason6 = value;
	}

	/**
	 * 取得查詢理由7
	 * <p/>
	 * (保留用)
	 */
	public String getRpaQueryReason7() {
		return this.rpaQueryReason7;
	}

	/**
	 * 設定查詢理由7
	 * <p/>
	 * (保留用)
	 **/
	public void setRpaQueryReason7(String value) {
		this.rpaQueryReason7 = value;
	}

	/**
	 * 取得查詢理由8
	 * <p/>
	 * (保留用)
	 */
	public String getRpaQueryReason8() {
		return this.rpaQueryReason8;
	}

	/**
	 * 設定查詢理由8
	 * <p/>
	 * (保留用)
	 **/
	public void setRpaQueryReason8(String value) {
		this.rpaQueryReason8 = value;
	}

	/**
	 * 取得查詢理由9
	 * <p/>
	 * (保留用)
	 */
	public String getRpaQueryReason9() {
		return this.rpaQueryReason9;
	}

	/**
	 * 設定查詢理由9
	 * <p/>
	 * (保留用)
	 **/
	public void setRpaQueryReason9(String value) {
		this.rpaQueryReason9 = value;
	}

	/** status顯示用欄位 */
	@Transient
	private String statusDesc;

	public void setStatusDesc(String value) {
		this.statusDesc = value;
	}

	public String getStatusDesc() {
		return statusDesc;
	}

	public void setRpaReturnData1(String rpaReturnData1) {
		this.rpaReturnData1 = rpaReturnData1;
	}

	public String getRpaReturnData1() {
		return rpaReturnData1;
	}

	public void setRpaReturnData2(String rpaReturnData2) {
		this.rpaReturnData2 = rpaReturnData2;
	}

	public String getRpaReturnData2() {
		return rpaReturnData2;
	}

	public void setRpaReturnData3(String rpaReturnData3) {
		this.rpaReturnData3 = rpaReturnData3;
	}

	public String getRpaReturnData3() {
		return rpaReturnData3;
	}

	public void setRpaReturnData4(String rpaReturnData4) {
		this.rpaReturnData4 = rpaReturnData4;
	}

	public String getRpaReturnData4() {
		return rpaReturnData4;
	}

	public void setRpaReturnData5(String rpaReturnData5) {
		this.rpaReturnData5 = rpaReturnData5;
	}

	public String getRpaReturnData5() {
		return rpaReturnData5;
	}

	public void setRpaReturnData6(String rpaReturnData6) {
		this.rpaReturnData6 = rpaReturnData6;
	}

	public String getRpaReturnData6() {
		return rpaReturnData6;
	}

	public void setRpaReturnData7(String rpaReturnData7) {
		this.rpaReturnData7 = rpaReturnData7;
	}

	public String getRpaReturnData7() {
		return rpaReturnData7;
	}

	public void setRpaReturnData8(String rpaReturnData8) {
		this.rpaReturnData8 = rpaReturnData8;
	}

	public String getRpaReturnData8() {
		return rpaReturnData8;
	}

	public void setRpaReturnData9(String rpaReturnData9) {
		this.rpaReturnData9 = rpaReturnData9;
	}

	public String getRpaReturnData9() {
		return rpaReturnData9;
	}
}
