#=====================================================
# \u8a55\u7b49\u53c3\u6578\u8a2d\u5b9a \u7248\u672c[varVer] 
#=====================================================
varVer=1.0
#Step 1.3 \u5c07 0-100 Score \u6a19\u6e96\u5316 (Mean=50, STD=25)\uff1a
stdItemScoreDevSampleMean_m1=49.333
stdItemScoreDevSampleSTD_m1=29.653
stdItemScoreDevSampleMean_m5=55.467
stdItemScoreDevSampleSTD_m5=19.611
stdItemScoreDevSampleMean_m7=32.533
stdItemScoreDevSampleSTD_m7=30.979
stdItemScoreDevSampleMean_p2=62.267
stdItemScoreDevSampleSTD_p2=26.639
stdItemScoreDevSampleMean_a5=70.667
stdItemScoreDevSampleSTD_a5=23.327
stdItemScoreDevSampleMean_z1=67.733
stdItemScoreDevSampleSTD_z1=19.971
stdItemScoreDevSampleMean_z2=74.000
stdItemScoreDevSampleSTD_z2=19.983
stdItemScoreA=25
stdItemScoreB=50	
#Step1.4 \u5c07\u6a19\u6e96\u5316\u5f8c\u7684\u5206\u6578\u4e58\u4ee5\u6307\u5b9a\u6b0a\u91cd
weight_m1=20
weight_m5=10
weight_m7=15
weight_p2=25
weight_a5=10
weight_z1=10		
weight_z2=10
#Step 1.6 \u5c07CORE Score\u6a19\u6e96\u5316
stdCoreScoreDevSampleMean=50
stdCoreScoreDevSampleSTD=11.875 
stdCoreScoreA=25
stdCoreScoreB=50	
#===============================================================================
#\u8aaa\u660e\uff1a\u4ee5","\u70ba\u5206\u9694\u7b26\u865f[0]\u5206\u6578\u3001[1]\u8a55\u7b49\u516c\u5f0f(javascript)\u3001[3]\u70ba\u8a55\u7b49\u53c3\u6578,\u5206\u9694\u7b26\u865f\u70ba";"
#===============================================================================
#--------------------------------------------------------------
# \u65e5\u672c\u6d88\u91d1\u6a21\u578b(\u57fa\u672c)
#--------------------------------------------------------------

#M1_age	\u5e74\u9f61
jpBase.scr_m1.01=20, {0} <= 45, item_m1
jpBase.scr_m1.02=60, {0} > 45 && {0} <=55, item_m1
jpBase.scr_m1.03=100, {0} > 55, item_m1
jpBase.scr_m1.04=20, /{0}/.test("Null"), item_m1
#M5_occupation	\u8077\u696d\u5927\u985e
jpBase.scr_m5.01=80, /{0}/.test("***********.***********.13"), item_m5
jpBase.scr_m5.02=40, /{0}/.test("***********.14"), item_m5
jpBase.scr_m5.03=0, /{0}/.test("Null"), item_m5
#M7_seniority	\u5e74\u8cc7(\u5e74)
jpBase.scr_m7.01=40, {0} <= 7, item_m7
jpBase.scr_m7.02=60, {0} > 7 && {0} <=20, item_m7
jpBase.scr_m7.03=100, {0} > 20, item_m7
jpBase.scr_m7.04=0, /{0}/.test("Null"), item_m7
#P2_pincome	\u500b\u4eba\u5e74\u6536\u5165(\u65e5\u5e63\u4edf\u5143)
jpBase.scr_p2.01=20, {0} <= 2500, item_p2
jpBase.scr_p2.02=50, {0} > 2500 && {0} <=7500, item_p2
jpBase.scr_p2.03=80, {0} > 7500 && {0} <=15000, item_p2
jpBase.scr_p2.04=100, {0} > 15000, item_p2
jpBase.scr_p2.05=0, /{0}/.test("Null"), item_p2
#A5_loan_period	\u5951\u7d04\u5e74\u9650(\u5e74)
jpBase.scr_a5.01=40, {0} <= 10, item_a5
jpBase.scr_a5.02=60, {0} > 10 && {0} <=15, item_a5
jpBase.scr_a5.03=100, {0} > 15, item_a5
jpBase.scr_a5.04=20, /{0}/.test("Null"), item_a5
#Z1	\u64d4\u4fdd\u54c1\u7684\u5ea7\u843d\u5730\u9ede\u53ca\u7a2e\u985e
jpBase.scr_z1.01=0, /{0}/.test("1"), item_z1
jpBase.scr_z1.02=20, /{0}/.test("2"), item_z1
jpBase.scr_z1.03=50, /{0}/.test("3"), item_z1
jpBase.scr_z1.04=70, /{0}/.test("4"), item_z1
jpBase.scr_z1.05=80, /{0}/.test("5"), item_z1
jpBase.scr_z1.06=100, /{0}/.test("6"), item_z1
jpBase.scr_z1.07=0, /{0}/.test("Null"), item_z1
#Z2	\u5e02\u5834\u74b0\u5883\u53ca\u8b8a\u73fe\u6027
jpBase.scr_z2.01=0, /{0}/.test("1"), item_z2
jpBase.scr_z2.02=40, /{0}/.test("2"), item_z2
jpBase.scr_z2.03=75, /{0}/.test("3"), item_z2
jpBase.scr_z2.04=100, /{0}/.test("4"), item_z2
jpBase.scr_z2.05=0, /{0}/.test("Null"), item_z2
#--------------------------------------------------------------
# \u65e5\u672c\u6d88\u91d1\u6a21\u578b(\u7b49\u7d1a)
#--------------------------------------------------------------
jpGrade.level.01= 1, {0} > 77, std_core
jpGrade.level.02= 2, {0} > 63 && {0} <= 77, std_core
jpGrade.level.03= 3, {0} > 48 && {0} <= 63, std_core
jpGrade.level.04= 4, {0} > 35 && {0} <= 48, std_core
jpGrade.level.05= 5, {0} > 20 && {0} <= 35, std_core
jpGrade.level.06= 6, {0} > 10 && {0} <= 20, std_core
jpGrade.level.07= 7, {0} > -5 && {0} <= 10, std_core
jpGrade.level.08= 8, {0} > -20 && {0} <= -5, std_core
jpGrade.level.09= 9, {0} > -35 && {0} <= -20, std_core
jpGrade.level.10=10, {0} <= -35, std_core
#jpGrade.level.11=10, /{0}/.test("Null"), std_core
#--------------------------------------------------------------
# \u65e5\u672c\u6d88\u91d1\u6a21\u578b(\u9055\u7d04\u6a5f\u7387)
#--------------------------------------------------------------
jpDR.dr_3yr.01=0.0009, /{0}/.test("1"), fRating
jpDR.dr_3yr.02=0.0015, /{0}/.test("2"), fRating
jpDR.dr_3yr.03=0.0026, /{0}/.test("3"), fRating
jpDR.dr_3yr.04=0.0046, /{0}/.test("4"), fRating
jpDR.dr_3yr.05=0.0087, /{0}/.test("5"), fRating
jpDR.dr_3yr.06=0.0180, /{0}/.test("6"), fRating
jpDR.dr_3yr.07=0.0366, /{0}/.test("7"), fRating
jpDR.dr_3yr.08=0.0761, /{0}/.test("8"), fRating
jpDR.dr_3yr.09=0.1363, /{0}/.test("9"), fRating
jpDR.dr_3yr.10=0.2729, /{0}/.test("10"), fRating
jpDR.dr_3yr.11=0.0000, /{0}/.test("Null"), fRating
jpDR.dr_3yr.12=1, /{0}/.test("DF"), fRating

jpDR.dr_1yr.01=0.0003, /{0}/.test("1"), fRating
jpDR.dr_1yr.02=0.0005, /{0}/.test("2"), fRating
jpDR.dr_1yr.03=0.0009, /{0}/.test("3"), fRating
jpDR.dr_1yr.04=0.0015, /{0}/.test("4"), fRating
jpDR.dr_1yr.05=0.0029, /{0}/.test("5"), fRating
jpDR.dr_1yr.06=0.0060, /{0}/.test("6"), fRating
jpDR.dr_1yr.07=0.0124, /{0}/.test("7"), fRating
jpDR.dr_1yr.08=0.0260, /{0}/.test("8"), fRating
jpDR.dr_1yr.09=0.0477, /{0}/.test("9"), fRating
jpDR.dr_1yr.10=0.1008, /{0}/.test("10"), fRating
jpDR.dr_1yr.11=0.0000, /{0}/.test("Null"), fRating
jpDR.dr_1yr.12=1, /{0}/.test("DF"), fRating
