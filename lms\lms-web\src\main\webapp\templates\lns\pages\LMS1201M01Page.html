<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:th="http://www.thymeleaf.org">
<body>
	<th:block th:fragment="innerPageBody">
			<script type="text/javascript">
            	require(['pagejs/lns/LMSM01APage'], function() { loadScript('pagejs/lns/LMS1201M01Page'); });
            	loadScript('pagejs/lns/LMSM03Page');
            </script>
		<div class="button-menu funcContainer" id="buttonPanel">
			<!-- flow button -->
			<!--<span wicket:id="_FlowButton" />-->
			<!-- 特殊分行(國外部、國金部、金控總部分行、財務部、財富管理處)授權外編製中顯示按鈕  -->
		    <span id="hideSpecialBtn">		    	
				<button type="button" id="btnSLogIN">
			    	<span class="ui-icon ui-icon-tag"></span><th:block th:text="#{'button.login'}">登錄</th:block>
				</button>
		    	<span id="showAfterSignBtn" class="hide">
					<button type="button" id="btnSave">
						<span class="ui-icon ui-icon-jcs-04"></span>
						<th:block th:text="#{'button.save'}">儲存</th:block>
					</button>			    		
		    	</span>
		    </span>
			<!-- 特殊分行(國外部、國金部、金控總部分行、財務部、財富管理處)授權外已會簽(會簽後修改)顯示按鈕 -->
			<span id="hideSpecialBtn2">
			    <button type="button" id="btnLogeIN">
			    	<span class="ui-icon ui-icon-tag"></span><th:block th:text="#{'button.login'}">登錄</th:block>
				</button>
			    <button type="button" id="btnSendCase">
			    	<!--(108)第 3230 號-->
			    	<span class="ui-icon ui-icon-jcs-15"></span><th:block th:text="#{'button.sendcase'}">提會</th:block>
				</button>
				<button type="button" id="btnSend">
					<span class="ui-icon ui-icon-jcs-02"></span>
					<th:block th:text="#{'button.send'}">呈主管覆核</th:block>
				</button>
				<span id="sAfterSign">				
			    <button type="button" id="btnAfterSign">
			    	<span class="ui-icon ui-icon-jcs-101"></span>會簽/審核後修改
				</button>
				</span>				
				<span id="sAfterSign2" class="hide">				
			    <button type="button" id="btnAfterSign2">
			    	<span class="ui-icon ui-icon-jcs-101"></span>回上一步
				</button>
				</span>				
				<button type="button" id="btnOpenLmsCase">
					<span class="ui-icon ui-icon-jcs-07"></span>
						<th:block th:text="#{'button.OpenLmsCase'}">開啟授信報案考核表</th:block>
				</button>				
			</span>			
			
			<!--海外分行-->
			<!--海外分行 編製中-->
			<th:block th:if="${_btnDOC_EDITING_visible}">
				<button type="button" id="btnSave">
					<span class="ui-icon ui-icon-jcs-04"></span>
					<th:block th:text="#{'button.save'}">儲存</th:block>
				</button>
				<button type="button" id="btnSend">
					<span class="ui-icon ui-icon-jcs-02" ></span>
					<th:block th:text="#{'button.send'}">呈主管覆核</th:block>
				</button>
			</th:block>
			<th:block th:if="${_btnDOC_EDITING2_visible}">
			</th:block>
			<th:block th:if="${_btnDOC_EDITING3_visible}">
			</th:block>
			<!--海外分行 待覆核-->
			<th:block th:if="${_btnWAIT_APPROVE_visible}">
			</th:block>
			<th:block th:if="${_btnWAIT_APPROVE2_visible}">
				<button type="button" id="btnCheck" >
	        		<span class="ui-icon ui-icon-jcs-106"></span>
	        		<th:block th:text="#{'button.check'}">覆核</th:block>
	        	</button>			
	        </th:block>
			<th:block th:if="${_btnWAIT_APPROVE3}">
			</th:block>
			<!--海外分行 呈授管處/營運中心-->
			<th:block th:if="${_btnWORK_LOOKING_visible}">
			</th:block>
			<th:block th:if="${_btnWORK_LOOKING2_visible}">
			</th:block>
			<th:block th:if="${_btnWORK_LOOKING3_visible}">
			</th:block>
			<!--海外分行 提會待登錄案件-->
			<th:block th:if="${_btnSEND_WAITLOGIN_visible}">
				<button type="button" id="btnSendWaitLogin">
					<span class="ui-icon ui-icon-jcs-02"></span>
					<th:block th:text="#{'button.send2'}">呈主管審核</th:block>
				</button>	
	        </th:block>
			<th:block th:if="${_btnSEND_WAITLOGIN2_visible}">
	        </th:block>
			<th:block th:if="${_btnSEND_WAITLOGIN3_visible}">
				<button type="button" id="btnOpenLmsCase">
					<span class="ui-icon ui-icon-jcs-07"></span>
					<th:block th:text="#{'button.OpenLmsCase'}">開啟授信報案考核表</th:block>
				</button>	
	        </th:block>
			<!--海外分行 提會待覆核案件-->
			<th:block th:if="${_btnSEND_WAITAPPROVE_visible}">
	        </th:block>
			<th:block th:if="${_btnSEND_WAITAPPROVE2_visible}">
				<button type="button" id="btnCheckWaitApprove" >
	        		<span class="ui-icon ui-icon-jcs-106"></span>
	        		<th:block th:text="#{'button.check'}">覆核</th:block>
	        	</button>
	        </th:block>
			<th:block th:if="${_btnSEND_WAITAPPROVE3_visible}">
				<button type="button" id="btnOpenLmsCase">
					<span class="ui-icon ui-icon-jcs-07"></span>
					<th:block th:text="#{'button.OpenLmsCase'}">開啟授信報案考核表</th:block>
				</button>	
	        </th:block>
			<!--海外分行 待補件/撤件-->
			<th:block th:if="${_btnWAIT_GETORBACK_visible}">
				<button type="button" id="btnSave">
					<span class="ui-icon ui-icon-jcs-04"></span>
					<th:block th:text="#{'button.save'}">儲存</th:block>
				</button>
				<button type="button" id="btnSend">
					<span class="ui-icon ui-icon-jcs-02"></span>
					<th:block th:text="#{'button.send'}">呈主管覆核</th:block>
				</button>
			</th:block>
			<th:block th:if="${_btnWAIT_GETORBACK2_visible}">
			</th:block>
			<th:block th:if="${_btnWAIT_GETORBACK3_visible}">
			</th:block>
			<!--海外分行 已核准受理-->
			<th:block th:if="${_btnALREADY_OK_visible}">
	        </th:block>
			<th:block th:if="${_btnALREADY_OK2_visible}">
	        </th:block>
			<th:block th:if="${_btnALREADY_OK3_visible}">
				<button type="button" id="btnOpenLmsCase">
					<span class="ui-icon ui-icon-jcs-07"></span>
					<th:block th:text="#{'button.OpenLmsCase'}">開啟授信報案考核表</th:block>
				</button>		
	        </th:block>
			<!--海外分行 婉卻-->
			<th:block th:if="${_btnALREADY_REJECT_visible}">
	        </th:block>
			<th:block th:if="${_btnALREADY_REJECT2_visible}">
	        </th:block>
			<th:block th:if="${_btnALREADY_REJECT3_visible}">
				<button type="button" id="btnOpenLmsCase">
					<span class="ui-icon ui-icon-jcs-07"></span>
					<th:block th:text="#{'button.OpenLmsCase'}">開啟授信報案考核表</th:block>
				</button>		
	        </th:block>
			<!--海外分行 陳復案/陳述案-->
			<th:block th:if="${_btnSAY_CASE_visible}">
				<button type="button" id="btnSave">
					<span class="ui-icon ui-icon-jcs-04"></span>
					<th:block th:text="#{'button.save'}">儲存</th:block>
				</button>
				<button type="button" id="btnSend">
					<span class="ui-icon ui-icon-jcs-02"></span>
					<th:block th:text="#{'button.send'}">呈主管覆核</th:block>
				</button>
			</th:block>
			<th:block th:if="${_btnSAY_CASE2_visible}">
			</th:block>
			<th:block th:if="${_btnSAY_CASE3_visible}">
			</th:block>
			<!--營運中心-->
			<th:block th:if="${_btnDOC_CEDITING_visible}">
			    <button type="button" id="btnLogeIN">
			    	<span class="ui-icon ui-icon-tag"></span><th:block th:text="#{'button.login'}">登錄</th:block>
				</button>
			    <button type="button" id="btnBackUnit">
			    	<span class="ui-icon ui-icon-mail-closed"></span><th:block th:text="#{'button.backunit'}">退回分行更正</th:block>
				</button>		
				<button type="button" id="btnBackCase2">
			    	<span class="ui-icon ui-icon-mail-closed"></span><th:block th:text="#{'button.backcase2'}">撤件</th:block>
				</button>										
				<button type="button" id="btnSend">
					<span class="ui-icon ui-icon-jcs-02"></span>
					<th:block th:text="#{'button.send'}">呈主管覆核</th:block>
				</button>
			    <button type="button" id="btnOpenLmsCase">
			    	<span class="ui-icon ui-icon-jcs-07"></span><th:block th:text="#{'button.openlms'}">開啟授信報案考核表</th:block>
				</button>							
			</th:block>
			<th:block th:if="${_btnDOC_CEDITING2_visible}">
			</th:block>
			<th:block th:if="${_btnDOC_CEDITING3_visible}">
			    <button type="button" id="btnCaseToChange">
			    	<span class="ui-icon ui-icon-jcs-10"></span><th:block th:text="#{'button.casetochange'}">案件改分派</th:block>
				</button>								
			</th:block>
			
			<th:block th:if="${_btnDOC_CWAITCHECK_visible}">
			</th:block>
			<th:block th:if="${_btnDOC_CWAITCHECK2_visible}">
				<button type="button" id="btnCheckArea" >
	        		<span class="ui-icon ui-icon-jcs-106"></span>
	        		<th:block th:text="#{'button.check'}">覆核</th:block>
	        	</button>				
			</th:block>
			<th:block th:if="${_btnDOC_CWAITCHECK3_visible}">
			</th:block>
						
			<th:block th:if="${_btnDOC_CTOADMIN_visible}">
			    <button type="button" id="btnBackCase">
			    	<span class="ui-icon ui-icon-mail-closed"></span><th:block th:text="#{'button.backcase'}">撤件/陳復</th:block>
				</button>				
			</th:block>
			<th:block th:if="${_btnDOC_CTOADMIN2_visible}">
				<button type="button" id="btnCheck" >
	        		<span class="ui-icon ui-icon-jcs-106"></span>
	        		<th:block th:text="#{'button.check'}">覆核</th:block>
	        	</button>				
			</th:block>
			<th:block th:if="${_btnDOC_CTOADMIN3_visible}">
			</th:block>
			
			<th:block th:if="${_btnDOC_CWAITCHANGE_visible}">
				<button type="button" id="btnReBackUnit">
			    	<span class="ui-icon ui-icon-jcs-15"></span><th:block th:text="#{'button.rebackunit'}">重新傳回分行</th:block>
				</button>				
			</th:block>
			<th:block th:if="${_btnDOC_CWAITCHANGE2_visible}">
			</th:block>
			<th:block th:if="${_btnDOC_CWAITCHANGE3_visible}">
			</th:block>
			
			<th:block th:if="${_btnDOC_CALREADYOK_visible}">
				<button type="button" id="btnReBackUnit">
			    	<span class="ui-icon ui-icon-jcs-15"></span><th:block th:text="#{'button.rebackunit'}">重新傳回分行</th:block>
				</button>				
			</th:block>
			<th:block th:if="${_btnDOC_CALREADYOK2_visible}">
			</th:block>
			<th:block th:if="${_btnDOC_CALREADYOK3_visible}">
			    <button type="button" id="btnOpenLmsCase">
			    	<span class="ui-icon ui-icon-jcs-07"></span><th:block th:text="#{'button.openlms'}">開啟授信報案考核表</th:block>
				</button>				
			</th:block>
			
			<th:block th:if="${_btnDOC_CALREADYREJECT_visible}">
				<button type="button" id="btnSendCase">
			    	<span class="ui-icon ui-icon-jcs-15"></span><th:block th:text="#{'button.sendcase'}">提會</th:block>
				</button>
				<button type="button" id="btnSend3">
			    	<span class="ui-icon ui-icon-jcs-02"></span><th:block th:text="#{'button.send3'}">呈主管放行</th:block>
				</button>
				<button type="button" id="btnReBackUnit">
			    	<span class="ui-icon ui-icon-jcs-15"></span><th:block th:text="#{'button.rebackunit'}">重新傳回分行</th:block>
				</button>				
			</th:block>
			<th:block th:if="${_btnDOC_CALREADYREJECT2_visible}">
			</th:block>
			<th:block th:if="${_btnDOC_CALREADYREJECT3_visible}">
			</th:block>
			
			<th:block th:if="${_btnDOC_CALLSEND_visible}">
				<button type="button" id="btnSend3">
			    	<span class="ui-icon ui-icon-jcs-02"></span><th:block th:text="#{'button.send3'}">呈主管放行</th:block>
				</button>
				<button type="button" id="btnReBackUnit">
			    	<span class="ui-icon ui-icon-jcs-15"></span><th:block th:text="#{'button.rebackunit'}">重新傳回分行</th:block>
				</button>				
			</th:block>
			<th:block th:if="${_btnDOC_CALLSEND2_visible}">
			</th:block>
			<th:block th:if="${_btnDOC_CALLSEND3_visible}">
			</th:block>
			
			<th:block th:if="${_btnDOC_CSAY_CASE_visible}">
				<button type="button" id="btnSend3">
			    	<span class="ui-icon ui-icon-jcs-02"></span><th:block th:text="#{'button.send3'}">呈主管放行</th:block>
				</button>
				<button type="button" id="btnReBackUnit">
			    	<span class="ui-icon ui-icon-jcs-15"></span><th:block th:text="#{'button.rebackunit'}">重新傳回分行</th:block>
				</button>				
			</th:block>
			<th:block th:if="${_btnDOC_CSAY_CASE2_visible}">
			</th:block>
			<th:block th:if="${_btnDOC_CSAY_CASE3_visible}">
			</th:block>
			
			<th:block th:if="${_btnDOC_SEA_SIGNING_visible}">
				<button type="button" id="btnLogeIN">
			    	<span class="ui-icon ui-icon-tag"></span><th:block th:text="#{'button.login'}">登錄</th:block>
				</button>
				<button type="button" id="btnSend3">
			    	<span class="ui-icon ui-icon-jcs-02"></span><th:block th:text="#{'button.send3'}">呈主管放行</th:block>
				</button>				
			</th:block>
			<th:block th:if="${_btnDOC_SEA_SIGNING2_visible}">
			</th:block>
			<th:block th:if="${_btnDOC_SEA_SIGNING3_visible}">
			</th:block>
						
			<th:block th:if="${_btnDOC_SEA_CWAITGO_visible}">
			</th:block>
			<th:block th:if="${_btnDOC_SEA_CWAITGO2_visible}">
				<button type="button" id="btnCheckSea" >
	        		<span class="ui-icon ui-icon-jcs-106"></span>
	        		<th:block th:text="#{'button.check'}">覆核</th:block>
	        	</button>			
			</th:block>
			<th:block th:if="${_btnDOC_SEA_CWAITGO3_visible}">
			</th:block>
			
			<th:block th:if="${_btnDOC_SEA_ALREADYSIGN_visible}">
			</th:block>
			<th:block th:if="${_btnDOC_SEA_ALREADYSIGN2_visible}">
			</th:block>
			<th:block th:if="${_btnDOC_SEA_ALREADYSIGN3_visible}">
			</th:block>
												
			<!--授管處-->
			<th:block th:if="${_btnDOC_HEDITING0_visible}">
			    <button type="button" id="btnBackSea">
			    	<span class="ui-icon ui-icon-mail-closed"></span><th:block th:text="#{'button.backSea'}">退回會簽意見</th:block>
				</button>																					
			</th:block>			
			<th:block th:if="${_btnDOC_HEDITING_visible}">
			    <button type="button" id="btnLogeIN">
			    	<span class="ui-icon ui-icon-tag"></span><th:block th:text="#{'button.login'}">登錄</th:block>
				</button>
			    <button type="button" id="btnSendCase">
			    	<span class="ui-icon ui-icon-jcs-15"></span><th:block th:text="#{'button.sendcase'}">提會</th:block>
				</button>
			    <button type="button" id="btnBackCase">
			    	<span class="ui-icon ui-icon-mail-closed"></span><th:block th:text="#{'button.backcase'}">撤件/陳復</th:block>
				</button>
			    <button type="button" id="btnBackInHead">
			    	<span class="ui-icon ui-icon-mail-closed"></span><th:block th:text="#{'button.backInHead'}">退回更正</th:block>
				</button>
			    <button type="button" id="btnBackSea">
			    	<span class="ui-icon ui-icon-mail-closed"></span><th:block th:text="#{'button.backSea'}">退回會簽意見</th:block>
				</button>																	
				<button type="button" id="btnSend">
					<span class="ui-icon ui-icon-jcs-02"></span>
					<th:block th:text="#{'button.send3'}">呈主管放行</th:block>
				</button>				
			</th:block>
			<th:block th:if="${_btnDOC_HEDITING2_visible}">
			</th:block>
			<th:block th:if="${_btnDOC_HEDITING3_visible}">
			    <button type="button" id="btnOpenLmsCase">
			    	<span class="ui-icon ui-icon-jcs-07"></span><th:block th:text="#{'button.openlms'}">開啟授信報案考核表</th:block>
				</button>
			    <button type="button" id="btnCaseToChange">
			    	<span class="ui-icon ui-icon-jcs-10"></span><th:block th:text="#{'button.casetochange'}">案件改分派</th:block>
				</button>								
			</th:block>
			
			<th:block th:if="${_btnDOC_HALREADY_visible}">
			    <button type="button" id="btnLogeIN">
			    	<span class="ui-icon ui-icon-tag"></span><th:block th:text="#{'button.login'}">登錄</th:block>
				</button>
			    <button type="button" id="btnSendCase">
			    	<span class="ui-icon ui-icon-jcs-15"></span><th:block th:text="#{'button.sendcase'}">提會</th:block>
				</button>												
			    <button type="button" id="btnReBackUnit">
			    	<span class="ui-icon ui-icon-jcs-15"></span><th:block th:text="#{'button.rebackunit'}">重新傳回分行</th:block>
				</button>								
			</th:block>
			<th:block th:if="${_btnDOC_HALREADY2_visible}">
			</th:block>
			<th:block th:if="${_btnDOC_HALREADY3_visible}">
			    <button type="button" id="btnOpenLmsCase">
			    	<span class="ui-icon ui-icon-jcs-07"></span><th:block th:text="#{'button.openlms'}">開啟授信報案考核表</th:block>
				</button>								
			</th:block>
			
			<th:block th:if="${_btnDOC_HSEND1_visible}">
			    <button type="button" id="btnLogeIN">
			    	<span class="ui-icon ui-icon-tag"></span><th:block th:text="#{'button.login'}">登錄</th:block>
				</button>
			    <button type="button" id="btnSendCase">
			    	<span class="ui-icon ui-icon-jcs-15"></span><th:block th:text="#{'button.sendcase'}">提會</th:block>
				</button>
			    <button type="button" id="btnBackCase">
			    	<span class="ui-icon ui-icon-mail-closed"></span><th:block th:text="#{'button.backcase'}">撤件/陳復</th:block>
				</button>
			    <button type="button" id="btnBackUnit">
			    	<span class="ui-icon ui-icon-mail-closed"></span><th:block th:text="#{'button.backunit'}">退回分行更正</th:block>
				</button>												
				<button type="button" id="btnReBackUnit">
			    	<span class="ui-icon ui-icon-jcs-15"></span><th:block th:text="#{'button.rebackunit'}">重新傳回分行</th:block>
				</button>				
			</th:block>
			<th:block th:if="${_btnDOC_HSEND1a_visible}">
			</th:block>
			<th:block th:if="${_btnDOC_HSEND1b_visible}">
			    <button type="button" id="btnOpenLmsCase">
			    	<span class="ui-icon ui-icon-jcs-07"></span><th:block th:text="#{'button.openlms'}">開啟授信報案考核表</th:block>
				</button>
			    <button type="button" id="btnCaseToChange">
			    	<span class="ui-icon ui-icon-jcs-10"></span><th:block th:text="#{'button.casetochange'}">案件改分派</th:block>
				</button>								
			</th:block>
			
			<th:block th:if="${_btnDOC_HSEND2_visible}">
			    <button type="button" id="btnLogeIN">
			    	<span class="ui-icon ui-icon-tag"></span><th:block th:text="#{'button.login'}">登錄</th:block>
				</button>
			    <button type="button" id="btnSendCase">
			    	<span class="ui-icon ui-icon-jcs-15"></span><th:block th:text="#{'button.sendcase'}">提會</th:block>
				</button>
			    <button type="button" id="btnBackCase">
			    	<span class="ui-icon ui-icon-mail-closed"></span><th:block th:text="#{'button.backcase'}">撤件/陳復</th:block>
				</button>
			    <button type="button" id="btnBackUnit">
			    	<span class="ui-icon ui-icon-mail-closed"></span><th:block th:text="#{'button.backunit'}">退回分行更正</th:block>
				</button>
			    <button type="button" id="btnSend3">
			    	<span class="ui-icon ui-icon-jcs-02"></span><th:block th:text="#{'button.send3'}">呈主管放行</th:block>
				</button>																				
			</th:block>
			<th:block th:if="${_btnDOC_HSEND2a_visible}">
			</th:block>
			<th:block th:if="${_btnDOC_HSEND2b_visible}">
				<button type="button" id="btnOpenLmsCase">
			    	<span class="ui-icon ui-icon-jcs-07"></span><th:block th:text="#{'button.openlms'}">開啟授信報案考核表</th:block>
				</button>
			    <button type="button" id="btnCaseToChange">
			    	<span class="ui-icon ui-icon-jcs-10"></span><th:block th:text="#{'button.casetochange'}">案件改分派</th:block>
				</button>																				
			</th:block>
			
			<th:block th:if="${_btnDOC_HSEND3_visible}">
			    <button type="button" id="btnLogeIN">
			    	<span class="ui-icon ui-icon-tag"></span><th:block th:text="#{'button.login'}">登錄</th:block>
				</button>
			    <button type="button" id="btnSendCase">
			    	<span class="ui-icon ui-icon-jcs-15"></span><th:block th:text="#{'button.sendcase'}">提會</th:block>
				</button>
			    <button type="button" id="btnBackCase">
			    	<span class="ui-icon ui-icon-mail-closed"></span><th:block th:text="#{'button.backcase'}">撤件/陳復</th:block>
				</button>
			    <button type="button" id="btnBackUnit">
			    	<span class="ui-icon ui-icon-mail-closed"></span><th:block th:text="#{'button.backunit'}">退回分行更正</th:block>
				</button>
				<button type="button" id="btnSend">
					<span class="ui-icon ui-icon-jcs-02"></span>
					<th:block th:text="#{'button.send'}">呈主管覆核</th:block>
				</button>																				
			</th:block>
			<th:block th:if="${_btnDOC_HSEND3a_visible}">
			</th:block>
			<th:block th:if="${_btnDOC_HSEND3b_visible}">
				<button type="button" id="btnOpenLmsCase">
			    	<span class="ui-icon ui-icon-jcs-07"></span><th:block th:text="#{'button.openlms'}">開啟授信報案考核表</th:block>
				</button>
			    <button type="button" id="btnCaseToChange">
			    	<span class="ui-icon ui-icon-jcs-10"></span><th:block th:text="#{'button.casetochange'}">案件改分派</th:block>
				</button>																				
			</th:block>
			
			<th:block th:if="${_btnDOC_HWAITCHECK_visible}">
			    <button type="button" id="btnBackCase">
			    	<span class="ui-icon ui-icon-mail-closed"></span><th:block th:text="#{'button.backcase'}">撤件/陳復</th:block>
				</button>																
			</th:block>
			<th:block th:if="${_btnDOC_HWAITCHECK2_visible}">
				<button type="button" id="btnCheckHead" >
	        		<span class="ui-icon ui-icon-jcs-106"></span>
	        		<th:block th:text="#{'button.check'}">覆核</th:block>
	        	</button>																				
			</th:block>
			<th:block th:if="${_btnDOC_HWAITCHECK3_visible}">
				<button type="button" id="btnOpenLmsCase">
			    	<span class="ui-icon ui-icon-jcs-07"></span><th:block th:text="#{'button.openlms'}">開啟授信報案考核表</th:block>
				</button>																
			</th:block>
			
			<th:block th:if="${_btnDOC_HWAITCHANGE_visible}">
				<button type="button" id="btnReBackUnit">
			    	<span class="ui-icon ui-icon-jcs-15"></span><th:block th:text="#{'button.rebackunit'}">重新傳回分行</th:block>
				</button>																
			</th:block>
			<th:block th:if="${_btnDOC_HWAITCHANGE2_visible}">
			</th:block>
			<th:block th:if="${_btnDOC_HWAITCHANGE3_visible}">
				<button type="button" id="btnOpenLmsCase">
			    	<span class="ui-icon ui-icon-jcs-07"></span><th:block th:text="#{'button.openlms'}">開啟授信報案考核表</th:block>
				</button>																
			</th:block>
			
			<th:block th:if="${_btnDOC_HALREADYOK_visible}">
				<button type="button" id="btnReBackUnit">
			    	<span class="ui-icon ui-icon-jcs-15"></span><th:block th:text="#{'button.rebackunit'}">重新傳回分行</th:block>
				</button>																
			</th:block>
			<th:block th:if="${_btnDOC_HALREADYOK2_visible}">
			</th:block>
			<th:block th:if="${_btnDOC_HALREADYOK3_visible}">
				<button type="button" id="btnOpenLmsCase">
			    	<span class="ui-icon ui-icon-jcs-07"></span><th:block th:text="#{'button.openlms'}">開啟授信報案考核表</th:block>
				</button>																
			</th:block>
			
			<th:block th:if="${_btnDOC_HALREADYREJECT_visible}">
				<button type="button" id="backToHeadFirst">
			    	<span class="ui-icon ui-icon-jcs-210"></span><th:block th:text="#{'button.backToHeadFirst'}">退回審核中</th:block>
				</button>				
			</th:block>
			<th:block th:if="${_btnDOC_HALREADYREJECT2_visible}">
			</th:block>
			<th:block th:if="${_btnDOC_HALREADYREJECT3_visible}">
				<button type="button" id="btnOpenLmsCase">
			    	<span class="ui-icon ui-icon-jcs-07"></span><th:block th:text="#{'button.openlms'}">開啟授信報案考核表</th:block>
				</button>																
			</th:block>
				<button type="button" id="btnPrint" class="forview">
					<span class="ui-icon ui-icon-jcs-03"></span>
					<th:block th:text="#{'button.print'}">列印</th:block>
				</button>						
				<button type="button" id="btnExit" class="forview">
					<span class="ui-icon ui-icon-jcs-01"></span>
					<th:block th:text="#{'button.exit'}">離開</th:block>
				</button>
				<!--J-111-0551_05097_B1006 Web e-Loan授信之信用風險管理遵循檢核表及借款人暨關係戶與本行授信往來情形及利潤貢獻度納入在途案件之額度-->
				<button type="button" id="btnApproveUnestablshExl" class="forview">
					<span class="ui-icon ui-icon-jcs-110"></span>
					<th:block th:text="#{'button.approveUnestablshExl'}">查詢在途授信額度</th:block>
				</button>	
			<th:block th:if="${_btn_SHOWCONTRACT_visible}">
				<button type="button" id="btnPrintL120S14A" class="forview">
                    <span class="ui-icon ui-icon-jcs-01"></span>
                    <th:block th:text="#{'button.printContract'}">列印合約書等相關簽約文件</th:block>
                </button>
			</th:block>
		</div>
		<div class="tit2 color-black">
		<form id="showBorrowData">
		<b>
			<span id="title0a" name="title0a" style="display:none;"><th:block th:text="#{'l120m01a.title0a'}">企金</th:block></span><span id="title0b" name="title0b" style="display:none;"><th:block th:text="#{'l120m01a.title0b'}">個金</th:block></span><span id="title1301" name="title1301"></span><span id="title1" name="title1" style="display:none;"><th:block th:text="#{'l120m01a.title1'}">分行授權外案件簽報書(一般)</th:block></span><span id="title1a" name="title1a" style="display:none;"><th:block th:text="#{'l120m01a.title1a'}">分行授權外案件簽報書(其他)</th:block></span><span id="title1b" name="title1b" style="display:none;"><th:block th:text="#{'l120m01a.title1b'}">分行授權外案件簽報書(陳復(述)案)</th:block></span><span id="title1c" name="title1c" style="display:none;"><th:block th:text="#{'l120m01a.title1c'}">分行授權內案件簽報書(一般)</th:block></span><span id="title1d" name="title1d" style="display:none;"><th:block th:text="#{'l120m01a.title1d'}">分行授權內案件簽報書(其他)</th:block></span><span id="title1e" name="title1e" style="display:none;"><th:block th:text="#{'l120m01a.title1e'}">分行授權內案件簽報書(陳復(述)案)</th:block></span><span id="title1f" name="title1f" style="display:none;"><th:block th:text="#{'l120m01a.title1f'}">總行授權內案件簽報書(一般)</th:block></span><span id="title1g" name="title1g" style="display:none;"><th:block th:text="#{'l120m01a.title1g'}">總行授權內案件簽報書(其他)</th:block></span><span id="title1h" name="title1h" style="display:none;"><th:block th:text="#{'l120m01a.title1h'}">總行授權內案件簽報書(陳復(述)案)</th:block></span><span id="title1i" name="title1i" style="display:none;"><th:block th:text="#{'l120m01a.title1i'}">營運中心授權內案件簽報書(一般)</th:block></span><span id="title1j" name="title1j" style="display:none;"><th:block th:text="#{'l120m01a.title1j'}">營運中心授權內案件簽報書(其他)</th:block></span><span id="title1k" name="title1k" style="display:none;"><th:block th:text="#{'l120m01a.title1k'}">營運中心授權內案件簽報書(陳復(述)案)</th:block></span>：(<span class="text-red" id="typCd"></span>)<span class="color-blue" id="custId"></span>&nbsp;<span class="color-blue" id="dupNo"></span>&nbsp;<span class="color-blue" id="custName"></span>
			<span id="overAuthMessageOnTitle"></span>
			<span id="highestCaseLvlOnTitle" class="highestCaseLvlSpan"></span>
		</b>
			<!-- 逾越授權的計算內容 -->
			<div id="overAuthDetailDiv" style="display:none" >
				<p id="overAuthDetailSpan"></p>
			</div>
		</form>
		</div>
        <div id="printView" style="display:none;">
            <div id="printGrid" ></div>
        </div>
        <div id="printView2" style="display:none;">
            <div id="printGrid2" ></div>
        </div>
        <div id="printThickBox" style="display:none;">
		    <table width="100%" class="tb2">
		      <tbody>
		  	    <tr id="printUnitTypeA">
					<td style='border:none;padding:0px;margin:0px;'>
						<label><input name="printCondition" type="radio" value="A" checked="true"/><th:block th:text="#{'THICKBOX.RPTNAMEA'}"><!--簽報書及相關附表--></th:block></label>
					</td>
		        </tr>
		  	    <tr id="printUnitTypeB">
					<td style='border:none;padding:0px;margin:0px;'>
						<label><input name="printCondition" type="radio" value="B"/><th:block th:text="#{'THICKBOX.RPTNAMEB'}"><!--母行法人代表提案意見--></th:block></label>
					</td>
		        </tr>
		  	    <tr id="printUnitTypeC">
					<td style='border:none;padding:0px;margin:0px;'>
						<label><input name="printCondition" type="radio" value="C"/><th:block th:text="#{'THICKBOX.RPTNAMEC'}"><!--營運中心說明及意見--></th:block></label>
					</td>
		        </tr>
		  	    <tr id="printUnitTypeD">
					<td style='border:none;padding:0px;margin:0px;'>
						<label><input name="printCondition" type="radio" value="D"/><th:block th:text="#{'THICKBOX.RPTNAMED'}"><!--審查意見及補充說明/會簽--></th:block></label>
					</td>
		        </tr>
		  	    <tr id="printUnitTypeZ">
					<td style='border:none;padding:0px;margin:0px;'>
						<label><input name="printCondition" type="radio" value="Z"/><th:block th:text="#{'THICKBOX.RPTNAMEZ'}"><!--營運中心會議決議--></th:block></label>
					</td>
		        </tr>				
		  	    <tr id="printUnitTypeE">
					<td style='border:none;padding:0px;margin:0px;'>
						<label><input name="printCondition" type="radio" value="E"/><th:block th:text="#{'THICKBOX.RPTNAMEE'}"><!--授審會會議決議--></th:block></label>
					</td>
		        </tr>
		  	    <tr id="printUnitTypeF">
					<td style='border:none;padding:0px;margin:0px;'>
						<label><input name="printCondition" type="radio" value="F"/><th:block th:text="#{'THICKBOX.RPTNAMEF'}"><!--催收會會議決議--></th:block></label>
					</td>
		        </tr>
                <tr id="printUnitTypeF2">
                    <td style='border:none;padding:0px;margin:0px;'>
                        <label><input name="printCondition" type="radio" value="F2"/><th:block th:text="#{'THICKBOX.RPTNAMEF2'}"><!--常董會會議決議--></th:block></label>
                    </td>
                </tr>				
		  	    <tr id="printUnitTypeG">
					<td style='border:none;padding:0px;margin:0px;'>
						<label><input name="printCondition" type="radio" value="G"/><span id="spectialPrint" class="hide"><th:block th:text="#{'THICKBOX.RPTNAMEI'}"><!--授管處會簽意見--></th:block></span><span id="signPrint"><th:block th:text="#{'THICKBOX.RPTNAMEG'}"><!--海外聯貸案會簽意見--></th:block></span></label>
					</td>
		        </tr>
		  	    <tr id="printUnitTypeH">
					<td style='border:none;padding:0px;margin:0px;'>
						<label><input name="printCondition" type="radio" value="H"/><th:block th:text="#{'THICKBOX.RPTNAMEH'}"><!--批覆書--></th:block></label>
					</td>
		        </tr>
		  	    <tr id="printUnitTypeX">
					<td style='border:none;padding:0px;margin:0px;'>
						<label><input name="printCondition" type="radio" value="X"/><th:block th:text="#{'THICKBOX.RPTNAMEX'}"><!--資信簡表--></th:block></label>
					</td>
		        </tr>
				
				<!--J-109-0479_05097_B1001 Web e-Loan簽報書增加各別流程控管階段的時間點並提供列印案件階段進度及統計excel下載-->
				<tr id="printUnitTypeK">
					<td style='border:none;padding:0px;margin:0px;'>
						<label><input name="printCondition" type="radio" value="K"/><th:block th:text="#{'THICKBOX.RPTNAMEK'}">授權外案件徵授信進度時程表</th:block></label>
					</td>
		        </tr>						
		      </tbody>
		    </table>
        </div>
		<div id="tabs-lms" class="tabs doc-tabs">
			<ul>
				<li><a href="#tab-01" goto="01"><b><th:block th:text="#{'l120m01a.status'}">文件資訊</th:block></b>
				</a>
				</li>
				<li><a href="#tab-02" goto="02"><b><th:block th:text="#{'l120m01a.borrowdata'}">借款人基本資料</th:block></b>
				</a>
				</li>
				<li><a href="#tab-03" goto="03"><b><th:block th:text="#{'l120m01a.l140m01a'}">額度明細表</th:block></b>
				</a>
				</li>
				<li class="hide" id="book2"><a href="#tab-16" goto="16"><b><th:block th:text="#{'l120m01a.l140m01b1'}">母行法人提案意見</th:block></b>
				</a>
				</li>				
				<li id="book1"><a href="#tab-11" goto="11"><b><th:block th:text="#{'l120m01a.l140m01b'}">額度批覆表</th:block></b>
				</a>
				</li>
				<li><a href="#tab-04" goto="04"><b><th:block th:text="#{'l120m01a.casebecause'}">案由</th:block></b>
				</a>
				</li>
				<li><a href="#tab-05" goto="05"><b><th:block th:text="#{'l120m01a.say'}">說明</th:block></b>
				</a>
				</li>
				<li><a href="#tab-06" goto="06"><b><th:block th:text="#{'l120m01a.other'}">其他</th:block></b>
				</a>
				</li>
				<li><a href="#tab-07" goto="07"><b><th:block th:text="#{'l120m01a.lookall'}">綜合評估/往來彙總</th:block></b>
				</a>
				</li>
				<li><a href="#tab-08" goto="08"><b><th:block th:text="#{'l120m01a.relationshipdoc'}">相關文件</th:block></b>
				</a>
				</li>
				<li><a href="#tab-09" goto="09"><b><th:block th:text="#{'l120m01a.moresay'}">補充說明</th:block></b>
				</a>
				</li>
				<li id="book3"><a href="#tab-12" goto="12"><b><th:block th:text="#{'l120m01a.decide'}">會簽/會議決議</th:block></b>
				</a>
				</li>
				<li id="book4"><a href="#tab-13" goto="13"><b><th:block th:text="#{'l120m01a.admin'}">授管處意見</th:block></b>
				</a>
				</li>
				<li id="book5" class="hide"><a href="#tab-14" goto="14"><b><th:block th:text="#{'l120m01a.otherplace'}">國金部/營運中心會簽意見</th:block></b>
				</a>
				</li>
				<li id="book6"><a href="#tab-15" goto="15"><b><th:block th:text="#{'l120m01a.otherplace2'}">營運中心意見</th:block></b>
				</a>
				</li>
				<li><a href="#tab-10" goto="10"><b><th:block th:text="#{'l120m01a.addfile'}">附加檔案</th:block></b>
				</a>
				</li>
				<li id="book28"><a href="#tab-28" goto="28"><b>RPA資料查詢</b>
				</a>
				</li>
				<!--J-106-0029-001 新增洗錢防制頁籤-->
				<li><a href="#tab-20" goto="20"><b>AML/CFT</b>   <!--<th:block th:text="#{'l120m01a.addfile'}</th:block>ssage>-->
				</a>
				</li>
				<!--J-106-0085-001  Web e-Loan企金授信新增主要還款來源國等相關欄位-->
				<li><a href="#tab-21" goto="21"><b><th:block th:text="#{'l120m01a.paySourceCountry'}">還款來源國說明</th:block></b>    
				</a>
				</li>
				
				<!--
				KENNY_20201015_REMARK-->
				<!---->
				<li id="book24"><a href="#tab-24" goto="24"><b><th:block th:text="#{'l120m01a.countryRiskLimit'}">國家風險限額說明</th:block></b>    
				</a>
				</li>
				
				
				
				<li id="book7"><a href="#tab-17" goto="17"><b><th:block th:text="#{'l120m01a.addfile1'}">常董稿附加檔案</th:block></b>
				</a>
				</li>
				<li id="book8"><a href="#tab-18" goto="18"><b><th:block th:text="#{'l120m01a.adminsay'}">主管批示</th:block></b>
				</a>
				</li>				
			</ul>
			<div class="tabCtx-warp">
				<input type="text" class="hide" id="mainDocStatus" name="mainDocStatus" />
               	<input type="text" class="hide" id="mainOid" name="mainOid" />
				<input type="text" class="hide" id="mainId" name="mainId" />	
				<div th:id="${tabIdx}" th:insert="~{${panelName} :: ${panelFragmentName}}"></div>
			</div>
		</div>

		<div id="checkBoxSea" style="display:none" >
			<table>
		      <tr>
		    	<td>
		    		<label><input name="qButtonTypeSea" type="radio" value="8" checked="checked"/><th:block th:text="#{'l120m01a.queryButtonType8'}"><!-- 確認 --></th:block></label>
				</td>
			  </tr>				
		      <tr>
		      	<td>
		      		<label><input name="qButtonTypeSea" type="radio" value="1"/><th:block th:text="#{'l120m01a.queryButtonType1'}"><!--退回經辦修改 --></th:block></label>
				</td>
			  </tr>			  
		    </table>
		</div>
		
		<div id="checkBox" style="display:none" >
			<table>
			  <tr style="display:none">
		    	<td>
		    		<label><input name="queryButtonType" type="radio" value="8"/><th:block th:text="#{'l120m01a.queryButtonType8'}"><!--確認--></th:block></label>
				</td>
			  </tr>				
		      <tr style="display:none">
		    	<td>
		    		<label><input name="queryButtonType" type="radio" value="2"/><th:block th:text="#{'l120m01a.queryButtonType2'}"><!-- 呈授管處--></th:block><span id="sendSign" class="hide"><th:block th:text="#{'l120m01a.decide1'}"><!--</th:block>ssage></span><span id="sendCheck" class="hide"><th:block th:text="#{'l120m01a.decide4'}"><!-- 審查--></th:block></span><span id="sendSpec" class="hide"><th:block th:text="#{'l120m01a.queryButtonType10'}"><!-- 審核批覆--></th:block></span></label>
				</td>
			  </tr>
			  <tr style="display:none">
		    	<td>
		    		<label><input name="queryButtonType" type="radio" value="3"/><th:block th:text="#{'l120m01a.queryButtonType3'}"><!--呈營運中心 --></th:block></label>
				</td>
			  </tr >
			  <tr style="display:none">
		    	<td>
		    		<label><input name="queryButtonType" type="radio" value="4"/><th:block th:text="#{'l120m01a.queryButtonType4'}"><!--呈總行--></th:block></label>
				</td>
			  </tr>
			   <tr style="display:none">
		    	<td>
		    		<label><input name="queryButtonType" type="radio" value="5"/><span id="goSend"><th:block th:text="#{'l120m01a.queryButtonType5'}"><!--核定放行--></th:block></span><span id="goSign" class="hide"><th:block th:text="#{'l120m01a.decide5'}"><!-- 會簽內容放行--></th:block></span></label>
				</td>
			  </tr>
			   <tr style="display:none">
		    	<td>
		    		<label><input name="queryButtonType" type="radio" value="6"/><th:block th:text="#{'l120m01a.queryButtonType6'}"><!--退回分行更正--></th:block></label>
				</td>
			  </tr>
			   <tr style="display:none">
		    	<td>
		    		<label><input name="queryButtonType" type="radio" value="7"/><th:block th:text="#{'l120m01a.queryButtonType7'}"><!--退回區域中心更正--></th:block></label>
				</td>
			  </tr>
			  <tr style="display:none">
		    	<td>
		    		<label><input name="queryButtonType" type="radio" value="9"/><th:block th:text="#{'docStatus.L3M'}"><!--提會待登錄--></th:block></label>
				</td>
			  </tr>
			  <tr style="display:none">
		    	<td>
		    		<label><input name="queryButtonType" type="radio" value="10"/><th:block th:text="#{'docStatus.L4M'}"><!--提會待覆核--></th:block></label>
				</td>
			  </tr>
			  <tr style="display:none">
		      	<td>
		      		<label><input name="queryButtonType" type="radio" value="1"/><th:block th:text="#{'l120m01a.queryButtonType1'}"><!--退回經辦修改 --></th:block></label>
				</td>
			  </tr>			  
		    </table>
		</div>
		
		<div id="backCaseBox" style="display:none" >
			<table>
		      <tr>
		      	<td>
		      		<label><input name="backCaseRadio" type="radio" value="1"/><th:block th:text="#{'l120m01a.backCaseRadio1'}"><!--撤件 --></th:block></label>
				</td>
			  </tr>
		      <tr>
		    	<td>
		    		<label><input name="backCaseRadio" type="radio" value="2"/><th:block th:text="#{'l120m01a.backCaseRadio2'}"><!--待陳復--></th:block></label>
				</td>
			  </tr>
		    </table>
		</div>
		<!--J-110-0374 Web e-Loan 為加強區域營運中心授信案件之審查效率, 增加企/消金授信簽報案件經區域營運中心流程進度控管表-->
		<!-- 撤件日ThickBox內容 -->
		<div id="divCaseCancelDate" style="display:none" >
			<form id="formCaseCancelDate">
				<table>
			      <tr>
			      	<td>
						 <td class="hd1">
                            <th:block th:text="#{'l120s17a.caseCancelDate'}">撤件日期</th:block>：&nbsp;&nbsp;
                        </td>
                        <td>
                            <input id="caseCancelDate" name="caseCancelDate"  class="date required"  type="text"   size="8" maxlength="10" />
                        </td>
					</td>
				  </tr>
			    </table>
			</form>
		</div>
		<div id="sendTo" style="display:none" >
			<table>
		      <tr id="sendTo1">
		      	<td>
		      		<label><input name="hqMeetFlag" type="radio" value="1"/><th:block th:text="#{'l120m01a.sendTo1'}"><!--提授審會 --></th:block></label>
				</td>
			  </tr>
		      <tr id="sendTo2">
		    	<td>
		    		<label><input name="hqMeetFlag" type="radio" value="2"/><th:block th:text="#{'l120m01a.sendTo2'}"><!--提催收會--></th:block></label>
				</td>
			  </tr>
			  <!--J-113-0337  配合本行將於第18屆董事會設置審計委員會替代監查人，請於授審處之授信管理系統→「授信審查」頁面→「案件簽報書」項下增加「提審計委員會」選項。-->
              <tr id="sendTo4">
                  <td>
                      <label><input name="hqMeetFlag" type="radio" value="4"/><th:block th:text="#{'l120m01a.sendTo4'}"><!--提審計委員會--></th:block></label>
                  </td>
              </tr>
			  <tr id="sendTo3">
		    	<td>
		    		<label><input name="hqMeetFlag" type="radio" value="3"/><th:block th:text="#{'l120m01a.sendTo3'}"><!--提常董會--></th:block></label>
				</td>
			  </tr>
			  <!--J-109-0479_05097_B1001 Web e-Loan簽報書增加各別流程控管階段的時間點並提供列印案件階段進度及統計excel下載-->
			  <!-- 登錄 提會日期 -->
			  <tr > 
		    	<td>
		    		<th:block th:text="#{'l120s17a.meetingDate'}">提會日期</th:block>：
					<input id="meetingDate" name="meetingDate"  class="date required"  type="text"   size="8" maxlength="10" />
					<span class="text-red">
					(YYYY-MM-DD)
					</span>
				</td>
			  </tr>
		    </table>
		</div>
		<div id="backReasonBox" style="display:none" >
			<textarea id="backReasonTextarea" maxlengthC="512" rows="5" cols="30"></textarea>
		</div>
		
		<!--J-111-0551_05097_B1006 Web e-Loan授信之信用風險管理遵循檢核表及借款人暨關係戶與本行授信往來情形及利潤貢獻度納入在途案件之額度-->
		<div id="approveUnestablshExlBox" style="display:none" >
			<span>
				若有多個統編要查詢請用逗號隔開，每筆統編最後一碼須為重複序號
				<br>
				(企業戶8碼統編+1碼重複序號共9碼，OBU統編或個人戶身分證ID 10碼+1碼重複序號共11碼)
				<br>
                                          例如:123456780,876543210,A1234567890
				<br>
				請不要按Enter換行
			</span>
			<br>
			<textarea id="approveUnestablshExlTextarea" maxlengthC="512" rows="3" cols="80"></textarea>
			<br>
			<span>
				說明：
				<br>
				在途授信額度係指已核准未簽約或雖已簽約惟尚未引入帳務系統之授信額度，倘授信案件客戶確實已不同意敘做或實際簽約額度低於核准額度時，請確實於eloan簽報系統「已核准授信額度辦理狀態報送作業」執行，將該額度序號選擇「不簽約-註銷額度」之選項，或選擇「已簽約」之選項，並完成該選項內實際簽約(承做)額度之建置作業。
				 
			</span>
		</div>
		
		<!-- 特殊分行會簽後呈主管覆核選擇界面 -->
		<div id="spectialSendTo" style="display:none" >
			<table>
		      <tr>
		      	<td>
		      		<label><input name="spectialFlag" type="radio" value="B" checked="checked"/><th:block th:text="#{'l120m01a.queryButtonType9'}"><!--呈授管處審核批覆 --></th:block></label>
				</td>
			  </tr>
			  <!--(108)第 3230 號-->
		      <tr id="spectialSendToShowForSign">
		    	<td>
		    		<label><input name="spectialFlag" type="radio" value="A"/><th:block th:text="#{'button.send'}"><!--呈主管覆核--></th:block></label>
				</td>
			  </tr>
		    </table>
		</div>		
		<!-- 登錄 ThickBox內容 -->
		<div id="signThick" style="display:none" >
			<input type="hidden" id="_tItemDscr09" name="_tItemDscr09"/>			
			<table>
		      <tr>
		      	<td>
		      		<label><input name="sign" type="radio" value="1" checked="checked" onclick="showCk(this,'showCk');"/><th:block th:text="#{'l120m01a.sign'}"><!--會簽意見 --></th:block></label>
				</td>
			  </tr>
		      <tr id="showCk" style="display:;">
		      	<td>
			       <div style="width:100%">
			       &nbsp;&nbsp;<textarea class="tckeditor" showType="b" showNewLineMessage="Y" distanceWord="49" th:attr="displayMessage=#{'l120m01a.otherplace'}" id="tItemDscr09" name="tItemDscr09" cols="115" rows="10"></textarea>
			       </div>					
				</td>
			  </tr>
		      <tr>
		      	<td>
		      		<label><input name="sign" type="radio" value="999" onclick="hideCk(this,'showCk');checkCk('signThick','showCk');"/><th:block th:text="#{'l120m01a.sign2'}"><!--登錄簽章欄人員 --></th:block></label>
				</td>
			  </tr>			  			  
		    </table>
		</div>
		<!-- 登錄 ThickBox內容 -->
		<div id="signThick2" style="display:none" >
			<input type="hidden" id="_tItemDscr0A" name="_tItemDscr0A"/>
			<input type="hidden" id="_tItemDscr0B" name="_tItemDscr0B"/>
			<input type="hidden" id="_htItemDscr0C" name="_htItemDscr0C"/>
			<table>
		      <tr>
		      	<td>
		      		<label class="sSpectial"><input name="login" type="radio" value="1" onclick="hideCk(this,'showCk2');hideCk(this,'showCkHead');checkCk('signThick2','showCk2');checkCk('signThick2a','showCkHead');" checked="checked"/><th:block th:text="#{'l120m01a.caselevel'}"><!--案件審核層級 --></th:block></label>
				</td>
			  </tr>
			  <!--J-109-0479_05097_B1001 Web e-Loan簽報書增加各別流程控管階段的時間點並提供列印案件階段進度及統計excel下載-->
			  <tr>
		      	<td>
		      		<label class="sSpectial"><input name="login" type="radio" value="991" onclick="hideCk(this,'showCk2');hideCk(this,'showCkHead');checkCk('signThick2','showCk2');checkCk('signThick2a','showCkHead');" /><th:block th:text="#{'l120s17a.caseReceivedDate'}">案件收件日期</th:block></label>
				</td>
			  </tr>
		      <tr id="hideHeadSign">
		      	<td>
					<label class="hSpectial"><input name="login" type="radio" value="2" onclick="showCk(this,'showCk2');hideCk(this,'showCkHead');checkCk('signThick2a','showCkHead');" /><th:block th:text="#{'l120m01a.looksay'}"><!--補充意見及審查意見 --></th:block></label>
				</td>
			  </tr>
		      <tr id="showCk2" style="display:none;">
		      	<td>
				   <div style="width:100%">
				   	&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
			         <span class="hareaChk"><textarea class="tckeditor" showType="b" showNewLineMessage="Y" distanceWord="49" th:attr="displayMessage=#{'l120m01a.moresay'}" id="tItemDscr0A" name="tItemDscr0A" cols="115" rows="10"></textarea>&nbsp;&nbsp;</span>
					 <textarea class="tckeditor" showType="b" showNewLineMessage="Y" distanceWord="49" th:attr="displayMessage=#{'l120m01a.admin1'}" id="tItemDscr0B" name="tItemDscr0B" cols="115" rows="10"></textarea>
			       </div>					
				</td>
			  </tr>			  
			   <tr>
		      	<td>
					<label id="spectialId" class="hide"><input name="login" type="radio" value="3" onclick="hideCk(this,'showCk2');hideCk(this,'showCkHead');checkCk('signThick2','showCk2');checkCk('signThick2a','showCkHead');" /><th:block th:text="#{'l120m01a.decide2'}"><!--會議決議--></th:block></label>
				</td>
			  </tr>			  
		      <tr>
		      	<td>
		      		<label class="hSpectial"><input name="login" type="radio" value="999" onclick="hideCk(this,'showCk2');hideCk(this,'showCkHead');checkCk('signThick2','showCk2');checkCk('signThick2a','showCkHead');" /><th:block th:text="#{'l120m01a.sign2'}"><!--登錄簽章欄人員 --></th:block></label>
				</td>
			  </tr>
		      <tr id="specitalHead">
		      	<td>
		      		<label><input name="login" type="radio" value="998" onclick="hideCk(this,'showCk2');showCk(this,'showCkHead');checkCk('signThick2','showCk2');"/><th:block th:text="#{'l120m01a.sign'}"><!--會簽意見 --></th:block></label>
				</td>
			  </tr>
			  <!-- J-110-0375 常董稿案由 Agenda for Managing Director's Draft -->
			  <tr id="showMdDraft">
		      	<td>
		      		<label><input name="login" type="radio" value="997"/><th:block th:text="#{'l120m01a.gistForMd'}"><!--常董稿案由 --></th:block></label>
				</td>
			  </tr>			  
			  <tr id="showCkHead" class="hide">
			  	<td>
			       <div style="width:100%">
			       	&nbsp;&nbsp;<span id="hCkHead" class="hide"><textarea class="tckeditor" showType="b"  showNewLineMessage="Y" distanceWord="49" th:attr="displayMessage=#{'l120m01a.otherplace1'}" id="htItemDscr0C" name="htItemDscr0C" cols="115" rows="10"></textarea></span>
			       </div>			  		
			  	</td>
			  </tr>			  
		    </table>
		</div>
		<!-- 登錄 ThickBox內容 -->
		<div id="signCaseLvl" style="display:none" >
			<form id="formCaseLvl">
				<table>
			      <tr>
			      	<td>
						<select class="max" maxlength="2" id="caseLvl"
								name="caseLvl"><!--案件審核層級--></select> 
					</td>
				  </tr>
				  <tr>
				  	<td>
						<span id="highestCaseLvlOnSelect" class="highestCaseLvlSpan"></span>
				  	</td>
				  </tr>
			    </table>
			</form>
			<!-- J-110-0493_11557_B1001 -->
			<!-- 檢查利害關係人授信額度合計是否達新台幣1億元以上 -->
			<div id="lmsm06_panel_for_caseLvl" th:insert="~{ base/panels/LMSM06Panel :: panelFragmentBody }"></div>
		</div>
		<!-- J-110-0375 常董稿案由 Agenda for Managing Director's Draft -->
		<div id="gistForMdBox" style="display:none" >
			<form id="gistForMdForm">
				<table>
					<tr>
						<td>
							<textarea id="itemDscrY" name="itemDscrY" class="max txt_mult" maxlengthC="1365" cols="70" rows="10" style="line-height: 20px;"></textarea>
						</td>
					</tr>
				</table>
			</form>
		</div>
		<!--J-109-0479_05097_B1001 Web e-Loan簽報書增加各別流程控管階段的時間點並提供列印案件階段進度及統計excel下載-->
		<!-- 登錄 案件收件日期ThickBox內容 -->
		<div id="signCaseReceivedDate" style="display:none" >
			<form id="formCaseReceivedDate">
				<table>
			      <tr>
			      	<td>
						 <td class="hd1">
                            <th:block th:text="#{'l120s17a.caseReceivedDate'}">案件收件日期</th:block>：&nbsp;&nbsp;
                        </td>
                        <td>
                            <input id="caseReceivedDate" name="caseReceivedDate"  class="date"  type="text"   size="8" maxlength="10" />
                            <span class="text-red">
							<br>
							<th:block th:text="#{'l120s17a.caseReceivedDateMemo'}">(YYYY-MM-DD，分行簽報內容最終定稿送呈日)</th:block>
							</span>
                        </td>
					</td>
				  </tr>
			    </table>
			</form>
		</div>
		 <div id="LMS1200V62Thickbox1" style="display:none">
	      <form id="LMS1200V62Form1">
		    <table width="100%" class="tb2">
		      <tbody>
		  	    <tr>
					<td>
						<input type="text" class="max required" id="rptTitle1a" name="rptTitle1a" maxlength="3" size="5" />
						<th:block th:text="#{'_l120m01a.year'}">民國年</th:block>
						<input type="text" class="max required" id="rptTitle1b" name="rptTitle1b" maxlength="2" size="5" />
						<th:block th:text="#{'_l120m01a.month'}">月</th:block>
						<input type="text" class="max required" id="rptTitle1c" name="rptTitle1c" maxlength="2" size="5" />
						<th:block th:text="#{'_l120m01a.day'}">日</th:block>
						<input type="text" class="max required" id="rptTitle1d" name="rptTitle1d" maxlength="4" size="4" />
						<th:block th:text="#{'_l120m01a.times'}">次<br/></th:block>
						<b><span class="field" id="rptTitle1e" name="rptTitle1e"></span><th:block th:text="#{'_l120m01a.select1'}">授信審議小組會議</th:block></b>
					</td>
		        </tr>
		      </tbody>
		    </table>
	      </form>
	      </div>		
		<!-- 登錄 ThickBox內容 -->
		<div id="signThick3" style="display:none" >
			<input type="hidden" id="_tItemDscr07" name="_tItemDscr07"/>
			<input type="hidden" id="_tItemDscr08" name="_tItemDscr08"/>
			<table>
             <tr>
                <td>
                    <label>
                        <input name="login2" type="radio" value="998" onclick="hideCk(this,'showCk3');hideCk(this,'showCk3a');checkCk('signThick3','showCk3');checkCk('signThick3','showCk3a');" checked="checked"/><th:block th:text="#{'l120m01a.caselevel'}"><!--案件審核層級 --></th:block>
                    </label>
                </td>
             </tr>
			 <!--J-109-0479_05097_B1001 Web e-Loan簽報書增加各別流程控管階段的時間點並提供列印案件階段進度及統計excel下載-->
			 <tr>
                <td>
                    <label>
                        <input name="login2" type="radio" value="990" onclick="hideCk(this,'showCk3');hideCk(this,'showCk3a');checkCk('signThick3','showCk3');checkCk('signThick3','showCk3a');" /><th:block th:text="#{'l120s17a.caseReceivedDate'}"><!--案件收件日期 --></th:block>
                    </label>
                </td>
             </tr>				
		      <tr>
		      	<td>
		      		<label><input name="login2" type="radio" value="1" onclick="hideCk(this,'showCk3');hideCk(this,'showCk3a');checkCk('signThick3','showCk3');checkCk('signThick3','showCk3a');"/><th:block th:text="#{'l120m01a.casetitle'}"><!--營運中心授審會會期 --></th:block></label>
				</td>
			  </tr>
		      <tr>
		      	<td>
					<label><input name="login2" type="radio" value="2" onclick="showCk(this,'showCk3');hideCk(this,'showCk3a');checkCk('signThick3','showCk3a');" /><th:block th:text="#{'l120m01a.looksay2'}"><!--營運中心說明及意見 --></th:block></label>
				</td>
			  </tr>			  
		      <tr id="showCk3" style="display:none;">
		      	<td>
				   <div style="width:100%">
				   	&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
			        <textarea class="tckeditor" showType="b" showNewLineMessage="Y" distanceWord="49" th:attr="displayMessage=#{'l120m01a.otherplace2'}" id="tItemDscr07" name="tItemDscr07" cols="115" rows="10"></textarea>
			       </div>					
				</td>
			  </tr>
		      <tr>
		      	<td>
					<label><input name="login2" type="radio" value="3" onclick="showCk(this,'showCk3a');hideCk(this,'showCk3');checkCk('signThick3','showCk3');" /><th:block th:text="#{'l120m01a.looksay3'}"><!--營運中心會議決議 --></th:block></label>
				</td>
			  </tr>			  
		      <tr id="showCk3a" style="display:none;">
		      	<td>
				   <div style="width:100%">
				   	&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
					<button type="button" onclick="loginTitle()">
						<span class="text-only"><th:block th:text="#{'l120m01a.bt04'}">登錄標題</th:block></span>
					</button>
					 <!--<textarea class="tckeditor" showType="b" wicket:message="displayMessage:l120m01a.decide2" id="tItemDscr08" name="tItemDscr08" cols="115" rows="10"></textarea>-->					 
			       </div>					
				</td>
			  </tr>			  
		      <!--<tr>
		      	<td>
					<label><input name="login2" type="radio" value="3" onclick="hideCk(this,'showCk3');hideCk(this,'showCk3a');checkCk('signThick3','showCk3');checkCk('signThick3','showCk3a');" /><th:block th:text="#{'l120m01a.bt04'}">登錄標題</th:block></label>
				</td>
			  </tr>-->			  			  
		      <tr>
		      	<td>
		      		<label><input name="login2" type="radio" value="999" onclick="hideCk(this,'showCk3');hideCk(this,'showCk3a');checkCk('signThick3','showCk3');checkCk('signThick3','showCk3a');"/><th:block th:text="#{'l120m01a.sign2'}"><!--登錄簽章欄人員 --></th:block></label>
				</td>
			  </tr>				  
		    </table>
		</div>
        <div id="thickLms7205Grid" style="display:none;">
            <div id="lms7205Grid" ></div>
        </div>
		<!-- 營運中心(授管處)會議決議共用元件 -->
		<div id="lmsm04_panel" th:insert="~{ base/panels/LMSM04Panel :: panelFragmentBody }"></div>
		<div class="tb2" id="signContent" style="display:none;" >
			<div>
				<form id="formSea">	 
					<table width=100%>
						<tr>
							<td class="hd1" width="50%"><th:block th:text="#{'l120m01a.title16'}">單位/授權主管：</th:block>&nbsp;&nbsp;</td>
							<td width="50%">
								<select class="boss" id="sSeaManager" name="sSeaManager"></select>
								<button type="button" onclick="lmsM03Json.beforeOpen('sSeaManager')">
									<span class="text-only">常用主管</span>
								</button>								
							</td>
						</tr>
						<tr>
							<td class="hd1"><th:block th:text="#{'l120m01a.title17'}">授信/覆核主管：</th:block>&nbsp;&nbsp;</td>
							<td>
								<select class="boss" id="sSeaBoss" name="sSeaBoss"></select>
								<button type="button" onclick="lmsM03Json.beforeOpen('sSeaBoss')">
									<span class="text-only">常用主管</span>
								</button>								
							</td>
						</tr>
						<tr>
							<td class="hd1"><th:block th:text="#{'l120m01a.title18'}">帳戶管理員：</th:block>&nbsp;&nbsp;</td>
							<td>
								<select class="boss" id="sSeaAoName" name="sSeaAoName"></select>
								<button type="button" onclick="lmsM03Json.beforeOpen('sSeaAoName')">
									<span class="text-only">常用主管</span>
								</button>								
							</td>
						</tr>
						<tr>
							<td class="hd1"><th:block th:text="#{'l120m01a.title19'}">經辦：</th:block>&nbsp;&nbsp;</td>
							<td>
								<select class="boss" id="sSeaAppraiserCN" name="sSeaAppraiserCN"></select>
								<button type="button" onclick="lmsM03Json.beforeOpen('sSeaAppraiserCN')">
									<span class="text-only">常用主管</span>
								</button>								
							</td>
						</tr>
						<tr>
							<td class="hd1"><th:block th:text="#{'l120m01a.managerId2'}"><!--單位主管--></th:block>：&nbsp;&nbsp;</td>
							<td><select class="boss2" id="sUnitManager1" name="sUnitManager1"></select></td>
						</tr>																		
					</table>				      
<!--			       <div style="width:100%">
			         <textarea class="ickeditor" id="tItemDscr09" name="tItemDscr09" cols="115" rows="10"></textarea>
			       </div>-->
			   </form>				
			</div>
		</div>
		<div id="signContent2" style="display:none" >
			<table class="tb2" width=100%>
				<tr>
					<td class="hd1" width="50%"><th:block th:text="#{'l120m01a.title19'}">經辦：</th:block>&nbsp;&nbsp;
					</td>
					<td width="50%">
						<select class="boss" id="sHeadAppraiser" name="sHeadAppraiser"></select>
						<!--<button type="button" onclick="lmsM03Json.beforeOpen('sHeadAppraiser')">
							<span class="text-only">常用主管</span>
						</button>-->						
					</td>					
				</tr>
				<tr>
					<td class="hd1"><th:block th:text="#{'l120m01a.title26'}">覆核：</th:block>&nbsp;&nbsp;
					</td>
					<td>
						<select class="boss" id="sHeadReCheck" name="sHeadReCheck"></select>
						<button type="button" onclick="lmsM03Json.beforeOpen('sHeadReCheck')">
							<span class="text-only">常用主管</span>
						</button>						
					</td>					
				</tr>								
				<tr>
					<td class="hd1"><th:block th:text="#{'l120m01a.title25'}">副處長：</th:block>&nbsp;&nbsp;
					</td>
					<td>
						<select class="boss" id="sHeadSubLeader" name="sHeadSubLeader"></select>
						<button type="button" onclick="lmsM03Json.beforeOpen('sHeadSubLeader')">
							<span class="text-only">常用主管</span>
						</button>						
					</td>					
				</tr>
				<tr>
					<td class="hd1"><th:block th:text="#{'l120m01a.title24'}">協理：</th:block>&nbsp;&nbsp;
					</td>
					<td>
						<select class="boss" id="sHeadLeader" name="sHeadLeader"></select>
						<button type="button" onclick="lmsM03Json.beforeOpen('sHeadLeader')">
							<span class="text-only">常用主管</span>
						</button>						
					</td>					
				</tr>				
				<tr>
					<td class="hd1"><th:block th:text="#{'l120m01a.managerId2'}"><!--單位主管--></th:block>：&nbsp;&nbsp;</td>
					<td><select class="boss2" id="sUnitManager2" name="sUnitManager2"></select></td>
				</tr>								
			</table>			
<!--	       <fieldset>
	       	<legend><b><th:block th:text="#{'l120m01a.moresay'}">補充說明</th:block></b></legend>
			   <div style="width:100%">
		         <textarea class="ickeditor" id="tItemDscr0A" name="tItemDscr0A" cols="115" rows="10"></textarea>
		       </div>
	       </fieldset>
		   	<fieldset>
	       	<legend><b><th:block th:text="#{'l120m01a.admin1'}">授信審查意見</th:block></b></legend>
			   <div style="width:100%">
		         <textarea class="ickeditor" id="tItemDscr0B" name="tItemDscr0B" cols="115" rows="10"></textarea>
		       </div>
	       </fieldset>-->
		</div>
		<div class="tb2" id="signContent3" style="display:none" >				
				<table width=100%>
					<tr>
						<td class="hd1" width="50%"><th:block th:text="#{'l120m01a.title19'}">經辦：</th:block>&nbsp;&nbsp;
						</td>
						<td width="50%">
							<select class="boss" id="sAreaAppraiser" name="sAreaAppraiser"></select>
							<!--button type="button" onclick="lmsM03Json.beforeOpen('sAreaAppraiser')">
								<span class="text-only">常用主管</span>
							</button>-->							
						</td>						
					</tr>
					<tr>
						<td class="hd1"><th:block th:text="#{'l120m01a.title23'}">襄理：</th:block>&nbsp;&nbsp;
						</td>
						<td>
							<select class="boss" id="sAreaManager" name="sAreaManager"></select>
							<button type="button" onclick="lmsM03Json.beforeOpen('sAreaManager')">
								<span class="text-only">常用主管</span>
							</button>							
						</td>						
					</tr>
					<tr>
						<td class="hd1"><th:block th:text="#{'l120m01a.title22'}">副營運長：</th:block>&nbsp;&nbsp;
						</td>
						<td>
							<select class="boss" id="sAreaSubLeader" name="sAreaSubLeader"></select>
							<button type="button" onclick="lmsM03Json.beforeOpen('sAreaSubLeader')">
								<span class="text-only">常用主管</span>
							</button>						
						</td>						
					</tr>															
					<tr>
						<td class="hd1"><th:block th:text="#{'l120m01a.title21'}">營運長：</th:block>&nbsp;&nbsp;
						</td>
						<td>
							<select class="boss" id="sAreaLeader" name="sAreaLeader"></select>
							<button type="button" onclick="lmsM03Json.beforeOpen('sAreaLeader')">
								<span class="text-only">常用主管</span>
							</button>							
						</td>						
					</tr>
					<tr>
						<td class="hd1"><th:block th:text="#{'l120m01a.managerId2'}"><!--單位主管--></th:block>：&nbsp;&nbsp;</td>
						<td><select class="boss2" id="sUnitManager3" name="sUnitManager3"></select></td>
					</tr>										
				</table>			
<!--		       <fieldset>
		       	<legend><b><th:block th:text="#{'l120m01a.otherplace2'}">營運中心意見</th:block></b></legend>
				   <div style="width:100%">
			         <textarea class="ickeditor" id="tItemDscr07" name="tItemDscr07" cols="115" rows="10"></textarea>
			       </div>
		       </fieldset>
			   	<fieldset>
		       	<legend>
		       		<form id="areaForm">			
			       		<b><input type="text" class="max" id="itemTitle" name="itemTitle" size="50" maxlength="120" maxlengthC="40" disabled="true"></b>
						<button type="button" onclick="logTitle()">
							<span class="text-only"><th:block th:text="#{'l120m01a.bt04'}">登錄標題</th:block></span>
						</button>
					</form>
				</legend>
				   <div style="width:100%">
			         <textarea class="ickeditor" id="tItemDscr08" name="tItemDscr08" cols="115" rows="10"></textarea>
			       </div>
		       </fieldset>-->		  
		</div>
		<div id="signContent3a" style="display:none" >				
       		<form id="areaForm">
       			<input type="hidden" id="_itemTitle" name="_itemTitle">		
				<input type="hidden" id="_itemTitle2" name="_itemTitle2">	
	       		<b><input type="text" class="max" id="itemTitle" name="itemTitle" size="50" maxlength="120" maxlengthC="40" value="Title" disabled="true"></b>
				<button type="button" onclick="logTitle()">
					<span class="text-only"><th:block th:text="#{'l120m01a.bt04'}">登錄標題</th:block></span>
				</button>
			</form>
		</div>		
		<div id="selectItemTitle" style="display:none;">
			<select id="sItemTitle" name="sItemTitle"></select>
		</div>
		
		<div id="selectHqAppraiser" style="display:none" >
			  <form id="selectHqForm">
	         	<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
	                 <tr>
	                    <td><select id="hqAppraiser" name="hqAppraiser"></select></td>
	                 </tr>
	           	 </table>
				</form>
		</div>
		
		<div id="selectBossBox"  style="display:none;">
			  <form id="selectBossForm">
	         	<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
	                 <tr>
	            		<td class="hd1"><th:block th:text="#{'l120m01a.aoPerson'}"><!--  帳戶管理員--></th:block>&nbsp;&nbsp;</td>
	                    <td>
	                    	<select id="AOPerson" name="AOPerson" class="boss"></select>&nbsp;
							<span id="bAOPerson">
								<button type="button" onclick="lmsM03Json.beforeOpen('AOPerson')">
									<span class="text-only">常用主管</span>
								</button>								
							</span>							
						</td>
	                 </tr>
					 <tr>
	            		<td class="hd1" width="60%"><th:block th:text="#{'l120m01a.selectBoss'}"><!--  授信主管人數--></th:block>&nbsp;&nbsp;</td>
	                    <td width="40%"><select id="numPerson" name="numPerson">
	                    		<option value="1">1</option>
	                    		<option value="2">2</option>
	                            <option value="3">3</option>
								<option value="4">4</option>
	                    		<option value="5">5</option>
	                            <option value="6">6</option>
								<option value="7">7</option>
	                    		<option value="8">8</option>
	                            <option value="9">9</option>
								<option value="10">10</option>
	                    	</select>
							</td>
	                 </tr>
	                 <tr >
	                 	<td class="hd1" ><th:block th:text="#{'l120m01a.bossId'}"><!--  授信主管--></th:block>&nbsp;&nbsp;</td>
	            		<td >
	            			<div >
	            				<th:block th:text="#{'l120m01a.no'}"><!-- 第--></th:block>1<th:block th:text="#{'l120m01a.site'}"><!--  位--></th:block>
								<th:block th:text="#{'l120m01a.bossId'}"><!--  授信主管--></th:block>&nbsp;&nbsp;
								<select id="mainBoss" name="boss1" class="boss"></select>&nbsp;
								<button type="button" onclick="lmsM03Json.beforeOpen('mainBoss')">
									<span class="text-only">常用主管</span>
								</button>								
								<span id="newBossSpan"></span>
							</div>
	                 	</td>
	                 </tr>
	                 <tr >
	            		<td class="hd1"><th:block th:text="#{'l120m01a.managerId'}"><!--  經副襄理--></th:block>&nbsp;&nbsp;</td>
	                    <td>
	                    	<select id="sManager" name="sManager" class="boss"></select>&nbsp;
							<button type="button" onclick="lmsM03Json.beforeOpen('sManager')">
								<span class="text-only">常用主管</span>
							</button>							
						</td>
	                 </tr>
					<tr>
						<td class="hd1"><th:block th:text="#{'l120m01a.managerId2'}"><!--單位主管--></th:block>&nbsp;&nbsp;</td>
						<td><select class="boss2" id="sUnitManager" name="sUnitManager"></select></td>
					</tr>					 
	           	 </table>
				</form>
  		</div>
		<div id="openChecDatekBox" style="display:none"> 
			<div>
				<input id="forCheckDate" type="text" size="10" maxlength="10" class="date"/>	
			</div>
		</div>
		<!-- 常用主管名單共用元件 -->
		<div id="lmsm03_panel" th:insert="~{ base/panels/LMSM03Panel :: panelFragmentBody }"></div>
		<div id="lmss7305_panel" openFlag="true" th:insert="~{ base/panels/LMS7301M01Panel :: panelFragmentBody }"></div>
		<!-- 授管處列印資信簡表Grid -->
	    <div id="tcesGrid120" style="display: none;">
			<form id="formCesGrid120">
				<div>
					<div id="gridSelCes120" width="100%"
						style="margin-left: 10px; margin-right: 10px">
					</div>
				</div>			
			</form>
	    </div>
		
		<!--J-109-0371_05097_B1002 簡化青年創業及啟動金貸款簽報書簽案流程-->
		<div id="printContractThickBox" style="display:none;"></div>

        <div id="printContractView" style="display:none;">
            <div id="printContractListGrid"></div>
        </div>
	</th:block>
</body>
</html>
