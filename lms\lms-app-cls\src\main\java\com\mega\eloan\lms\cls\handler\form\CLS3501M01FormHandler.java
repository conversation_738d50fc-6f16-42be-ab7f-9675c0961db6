package com.mega.eloan.lms.cls.handler.form;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.ElsUser;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.cls.pages.CLS3501M01Page;
import com.mega.eloan.lms.cls.service.CLS3501Service;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.mfaloan.service.MisIcbcBrService;
import com.mega.eloan.lms.model.C124M01A;
import com.mega.eloan.lms.model.C124M01B;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.iisi.cap.utils.CapWebUtil;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.core.FlowException;

/**
 * <pre>
 * 中小信保整批申請
 * </pre>
 *
 * @since 2020/05/16
 * <AUTHOR>
 * @version <ul>
 *          <li>2020/05/16,EL09301,new
 *          </ul>
 */
@Scope("request")
@Controller("cls3501m01formhandler")
@DomainClass(C124M01A.class)
public class CLS3501M01FormHandler extends AbstractFormHandler {

    @Resource
    BranchService branchService;

    @Resource
    MisIcbcBrService misIcbcBrService;

    @Resource
    UserInfoService userInfoService;

    @Resource
    LMSService lmsService;

    @Resource
    CLSService clsService;

    @Resource
    EloandbBASEService eloandbService;

    @Resource
    CLS3501Service cls3501Service;

    MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
    Properties prop = MessageBundleScriptCreator.getComponentResource(CLS3501M01Page.class);

	public IResult echo_custId(PageParameters params) throws CapException {
        // 儲存and檢核
        CapAjaxFormResult result = new CapAjaxFormResult();

        result.set("custId", Util.trim(params.getString("custId")));
        result.set("dupNo", Util.trim(params.getString("dupNo")));
        result.set("custName", Util.trim(params.getString("custName")));
        return result;
    }

	public IResult checkCust(PageParameters params) throws CapException {
        String custId = Util.trim(params.getString("custId"));
        String dupNo = Util.trim(params.getString("dupNo"));
        CapAjaxFormResult result = new CapAjaxFormResult();

        // 取得紓困版本，目前是V2
        String c124m01aVer = Util.trim(lmsService.getSysParamDataValue("LMS_C124M01A_VER"));
        Integer verNo = (Util.isNumeric(c124m01aVer) ? NumberUtils.toInt(c124m01aVer) : 0);

        // 檢查是否有資料 全行只能有一筆
        List<C124M01A> list = cls3501Service.findC124m01aByCustIdAndDupNo(
                custId, dupNo, verNo);
        if(list != null){
            int count = list.size();
            if(count > 0){
                StringBuffer sb = new StringBuffer();
                for(C124M01A c124m01a : list){
                    String brId = Util.trim(c124m01a.getOwnBrId());
                    String docStatus = Util.trim(c124m01a.getDocStatus());
                    // cls3501.error001=客戶統編:{0}　{1} 已於 分行:{2}簽案，案件狀態:{3}
                    String msg = MessageFormat.format(prop.getProperty("cls3501.error001"),
                            custId, dupNo, brId, getMessage("docStatus." + docStatus));
                    sb.append((sb.length() > 0 ? "<br>" : ""));
                    sb.append(msg);
                }
                result.set("isEixst", sb.toString());
            } else {

            }
        } else {

        }

        return result;
    }

    @DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult newC124M01A(PageParameters params) throws CapException {
        C124M01A c124m01a = new C124M01A();
        String mainid = "";
        mainid = IDGenerator.getUUID();
        c124m01a.setDocStatus(CreditDocStatusEnum.海外_編製中.getCode());
        c124m01a.setOwnBrId(user.getUnitNo());
        c124m01a.setMainId(mainid);
        String txCode = Util.trim(params.getString(EloanConstants.TRANSACTION_CODE));
        c124m01a.setTxCode(txCode);
		c124m01a.setDocURL(CapWebUtil.getDocUrl(CLS3501M01Page.class));
        c124m01a.setDataStatus("");
        c124m01a.setType(131);
        c124m01a.setCustId(Util.trim(params.getString("custId", null)));
        c124m01a.setDupNo(Util.trim(params.getString("dupNo", null)));
        c124m01a.setCustName(Util.trim(params.getString("custName", null)));
        c124m01a.setApproveNo(Util.trim(params.getString("custId", null)));

        // 取得紓困版本
        String c124m01aVer = Util.trim(lmsService.getSysParamDataValue("LMS_C124M01A_VER"));
        Integer verNo = (Util.isNumeric(c124m01aVer) ? NumberUtils.toInt(c124m01aVer) : 0);
        if(verNo == 0){
            throw new CapMessageException("C124M01A參數設定錯誤請洽資訊處", getClass());
        } else {
            c124m01a.setVersion(verNo);
        }

        this.importCustData(c124m01a);
        this.importImporterData(c124m01a);

        cls3501Service.save(c124m01a);

        CapAjaxFormResult result = new CapAjaxFormResult();
        result.set(EloanConstants.OID, c124m01a.getOid());
        result.set(EloanConstants.MAIN_ID, c124m01a.getMainId());
        result.set(EloanConstants.MAIN_OID, c124m01a.getOid());
        result.set(EloanConstants.MAIN_DOC_STATUS, c124m01a.getDocStatus());
        return result;
    }

    @DomainAuth(value = AuthType.Query)
	public IResult queryC124M01A(PageParameters params) throws CapException {
        int page = Util.parseInt(params.getString(EloanConstants.PAGE));
        CapAjaxFormResult result = new CapAjaxFormResult();
        String mainOid = params.getString(EloanConstants.MAIN_OID);
        C124M01A c124m01a = cls3501Service.findModelByOid(C124M01A.class, mainOid);
        if(c124m01a == null){
            if (!Util.isEmpty(mainOid)) {
                // 第一次啟案自動塞入前案資訊
            } else {
                // 開啟新案帶入起案的分行和目前文件狀態
                result.set("docStatus", this.getMessage("docStatus." + CreditDocStatusEnum.海外_編製中.getCode()));
                result.set("ownBrId", user.getUnitNo());
                result.set("ownBrName", StrUtils.concat(" ", branchService.getBranchName(user.getUnitNo())));
                result.set("docStatusVal", CreditDocStatusEnum.海外_編製中.getCode());
            }
        } else {
            if (!Util.isEmpty(mainOid)) {
                result = formatResultShow(result, c124m01a, page);
                result.set("grntPaper", Util.trim(c124m01a.getGrntPaper()));
                result.set("neighborhood", Util.trim(c124m01a.getNeighborhood()));
            }
        }

        return defaultResult( params, c124m01a, result);
    }

    /**
     * 格式化顯示訊息
     * @return
     * @throws CapException
     */
    @SuppressWarnings("unchecked")
    private CapAjaxFormResult formatResultShow(CapAjaxFormResult result, C124M01A c124m01a, Integer page)
            throws CapException {
        String mainId = c124m01a.getMainId();

        switch (page) {
            case 1:
                result = DataParse.toResult(c124m01a);

                List<C124M01B> c124m01bList = (List<C124M01B>) cls3501Service.findListByMainId(C124M01B.class, mainId);
                if (!Util.isEmpty(c124m01bList)) { // 取得人員職稱
                    StringBuilder bossId = new StringBuilder("");
                    for (C124M01B c124m01b : c124m01bList) {
                        // 要加上人員代碼
                        String type = Util.trim(c124m01b.getStaffJob());
                        String userId = Util.trim(c124m01b.getStaffNo());
                        String value = Util.trim(lmsService.getUserName(userId));
                        if ("L1".equals(type)) {    // L1. 分行經辦
                            result.set("showApprId", userId + " " + value);
                        } else if ("L3".equals(type)) {    // L3. 分行授信主管
                            bossId.append(bossId.length() > 0 ? "<br/>" : "");
                            bossId.append(userId).append(" ").append(value);
                        } else if ("L4".equals(type)) {    // L4. 分行覆核主管
                            result.set("reCheckId", userId + " " + value);
                        } else if ("L5".equals(type)) {    // L5. 經副襄理
                            result.set("managerId", userId + " " + value);
                        } else if ("L6".equals(type)) {    // L6. 總行經辦
                            result.set("mainApprId", userId + " " + value);
                        } else if ("L7".equals(type)) {    // L7.總行主管
                            result.set("mainReCheckId", userId + " " + value);
                        }
                    }
                    result.set("bossId", bossId.toString());
                }
                result.set("ownBrName",	" " + branchService.getBranchName(c124m01a.getOwnBrId()));

                result.set("creator", lmsService.getUserName(c124m01a.getCreator()));
                result.set("updater", lmsService.getUserName(c124m01a.getUpdater()));
                result.set("createTime", Util.nullToSpace(TWNDate.valueOf(c124m01a.getCreateTime())));
                result.set("updateTime", Util.nullToSpace(TWNDate.valueOf(c124m01a.getUpdateTime())));
                result.set("docStatus",	getMessage("docStatus." + c124m01a.getDocStatus()));
                break;
        }// close switch case

        result.set("custId", Util.trim(c124m01a.getCustId()));
        result.set("dupNo", Util.trim(c124m01a.getDupNo()));
        result.set("custName", Util.trim(c124m01a.getCustName()));
        result.set(EloanConstants.OID, CapString.trimNull(c124m01a.getOid()));

        return result;
    }

    @DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteC124M01A(PageParameters params) throws CapException {
        CapAjaxFormResult result = new CapAjaxFormResult();
        String[] oids = params.getStringArray("oids");
        if (oids.length > 0) {
            if (cls3501Service.deleteC124M01As(oids)) {
                result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
						.getMainMessage(UtilConstants.AJAX_RSP_MSG.刪除成功));
            }
        }
        return result;
    }

    private String[] parseTel(String tel) {
        String inputStr = tel;
        String patternStr = "\\(?(\\d+)\\)?-?(\\d+)";

        String[] tels = new String[] { "0", "0" };

        Pattern pattern = Pattern.compile(patternStr);
        Matcher matcher = pattern.matcher(inputStr);
        boolean matchFound = matcher.find();
        while (matchFound) {
            System.out.println(matcher.start() + "-" + matcher.end());
            for (int i = 0; i <= matcher.groupCount(); i++) {
                String groupStr = matcher.group(i);
                System.out.println(i + ":" + groupStr);
                if (i == 1) {
                    tels[0] = groupStr;
                } else if (i == 2) {
                    tels[1] = groupStr;
                }
            }

            if (matcher.end() + 1 <= inputStr.length()) {
                matchFound = matcher.find(matcher.end());
            } else {
                break;
            }
        }

        String firstNumber = CapString.trimNull(tels[0]);
        if (firstNumber.length() > 2) {
            tels[0] = StringUtils.left(firstNumber, 2);
            String remain = firstNumber.substring(2);
            tels[1] = remain + tels[1];
        }

        return tels;
    }

    @DomainAuth(AuthType.Modify)
	public IResult saveMain(PageParameters params) throws CapException {
        return _saveAction(params, "N");
    }

    @DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult tempSave(PageParameters params) throws CapException {
        return _saveAction(params, "Y");
    }

	private CapAjaxFormResult _saveAction(PageParameters params,
			String tempSave) throws CapException {
        SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, tempSave);
        Boolean showMsg = params.getAsBoolean("showMsg", false);

        CapAjaxFormResult result = new CapAjaxFormResult();
        String mainOid = params.getString(EloanConstants.MAIN_OID);
        C124M01A c124m01a = null;
        if (Util.isNotEmpty(mainOid)) {
            try{
                c124m01a = cls3501Service.findModelByOid(C124M01A.class, mainOid);

                CapBeanUtil.map2Bean(params, c124m01a);

                cls3501Service.save(c124m01a);

                if (Util.notEquals("Y",
                        SimpleContextHolder.get(EloanConstants.TEMPSAVE_RUN))) {
                    String errMsg = this.checkData(c124m01a);
                    if (Util.isEmpty(errMsg)) {
                        if (showMsg) {
							errMsg = RespMsgHelper.getMainMessage(
									UtilConstants.AJAX_RSP_MSG.儲存成功);
                        }
                    } else {
                        if (showMsg) {

                        }else{
                            throw new CapMessageException(errMsg, getClass());
                        }
                    }
                    result.set(CapConstants.AJAX_NOTIFY_MESSAGE, errMsg);
                }
            }catch(Exception e){
                logger.error(StrUtils.getStackTrace(e));
                throw new CapException(e, getClass());
            }
        }

        return defaultResult( params, c124m01a, result);
    }

    private CapAjaxFormResult defaultResult(PageParameters params, C124M01A c124m01a,
                                            CapAjaxFormResult result) throws CapException {
        // required information
        result.set("docStatusVal", c124m01a.getDocStatus());
        result.set(EloanConstants.MAIN_OID,	CapString.trimNull(c124m01a.getOid()));
        result.set(EloanConstants.MAIN_DOC_STATUS, CapString.trimNull(c124m01a.getDocStatus()));
        result.set("showCustId", StrUtils.concat(CapString.trimNull(c124m01a.getCustId()), " ",
                CapString.trimNull(c124m01a.getDupNo()), " ", CapString.trimNull(c124m01a.getCustName())));
        result.set(EloanConstants.PAGE, Util.trim(params.getString(EloanConstants.PAGE)));
        result.set(EloanConstants.MAIN_ID, Util.trim(c124m01a.getMainId()));
        return result;
    }

    private String checkData(C124M01A c124m01a){
        StringBuffer sb = new StringBuffer();

        if(c124m01a==null){
            // cls3501.error002=查無此案
            sb.append((sb.length() > 0 ? "<br>" : ""));
            sb.append(prop.getProperty("cls3501.error002"));
        }

        BigDecimal applyLoan = c124m01a.getApplyLoan();
        if(applyLoan.compareTo(BigDecimal.valueOf(100000)) == 1){
            // cls3501.error003=申請額度上線為新台幣10萬元
            sb.append((sb.length() > 0 ? "<br>" : ""));
            sb.append(prop.getProperty("cls3501.error003"));
        }

        String dist = c124m01a.getDist();
        String zip = c124m01a.getZip();
        if(zip.length() < 3){
            // cls3501.error004=郵遞區號至少輸入3碼
            sb.append((sb.length() > 0 ? "<br>" : ""));
            sb.append(prop.getProperty("cls3501.error004"));
        } else if(Util.notEquals(dist, zip.substring(0, 3))){
            // cls3501.error005=郵遞區號有誤
            sb.append((sb.length() > 0 ? "<br>" : ""));
            sb.append(prop.getProperty("cls3501.error005"));
        }

        String isEmail = Util.trim(c124m01a.getIsEmail());
        String email = Util.trim(c124m01a.getEmail());
        if(Util.equals(isEmail, "1") && Util.isEmpty(email)){
            // cls3501.error006=請輸入個人電子信箱
            sb.append((sb.length() > 0 ? "<br>" : ""));
            sb.append(prop.getProperty("cls3501.error006"));
        }

        String villageName = Util.trim(c124m01a.getVillageName());
        String village = Util.trim(c124m01a.getVillage());
        if(Util.isNotEmpty(villageName) && Util.isEmpty(village)){
            // 請選擇　里/村
            sb.append((sb.length() > 0 ? "<br>" : ""));
            sb.append(prop.getProperty("checkSelect") + "　" +
                    prop.getProperty("village_1") + "/" + prop.getProperty("village_2"));
        }
        if(Util.isEmpty(villageName) && Util.isNotEmpty(village)){
            // cls3501.error007=請輸入里村名
            sb.append((sb.length() > 0 ? "<br>" : ""));
            sb.append(prop.getProperty("cls3501.error007"));
        }

        String tel1 = Util.trim(c124m01a.getTel1());
        String tel2 = Util.trim(c124m01a.getTel2());
        String ownerCellphone1 = Util.trim(c124m01a.getOwnerCellphone1());
        String ownerCellphone2 = Util.trim(c124m01a.getOwnerCellphone2());
        boolean chkTel = false;
        boolean chkCellphone = false;
        if(Util.isNotEmpty(tel1) && Util.isNotEmpty(tel2)){
            chkTel = true;
        }
        if(Util.isNotEmpty(ownerCellphone1) && Util.isNotEmpty(ownerCellphone2)){
            chkCellphone = true;
        }

        if(chkTel || chkCellphone){

        } else {
            // cls3501.error008=請輸入聯絡電話或申請人行動電話
            sb.append((sb.length() > 0 ? "<br>" : ""));
            sb.append(prop.getProperty("cls3501.error008"));
        }


        return sb.toString();
    }

    @DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult getBossList(PageParameters params) throws CapException {
        // 儲存and檢核
        CapAjaxFormResult result = new CapAjaxFormResult();
        // 查詢所選銀行的甲級主管、乙級主管清單
        UserInfoService.SignEnum[] signs = { UserInfoService.SignEnum.首長, UserInfoService.SignEnum.單位主管, UserInfoService.SignEnum.甲級主管,
                UserInfoService.SignEnum.乙級主管 };
        Map<String, String> bossList = userInfoService.findByBrnoAndSignId(
                user.getUnitNo(), signs);
        result.set("bossList", new CapAjaxFormResult(bossList));
        return result;
    }

    /*** 呈主管覆核(呈主管 主管覆核 拆2個method) */
    @SuppressWarnings({ "unchecked" })
    @DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult flowAction(PageParameters params) throws CapException {
        CapAjaxFormResult result = new CapAjaxFormResult();
        // 儲存and檢核
        String oid = params.getString(EloanConstants.MAIN_OID);

        C124M01A c124m01a = cls3501Service.findModelByOid(C124M01A.class, oid);
        String[] formSelectBoss = params.getStringArray("selectBoss");

        if (params.containsKey("flowAction")) {
            if (params.getBoolean("flowAction")) {
//                String showMsg = this.checkSaveData(l140mm3a, "check");
//                if (Util.isNotEmpty(showMsg)) {
//                    throw new CapMessageException(showMsg,	getClass());
//                }
            }
        }

       if (!Util.isEmpty(formSelectBoss) && formSelectBoss.length != 0) {
            String manager = Util.trim(params.getString("manager"));
            List<C124M01B> models = (List<C124M01B>) cls3501Service
                    .findListByMainId(C124M01B.class, c124m01a.getMainId());
            if (!models.isEmpty()) {
                cls3501Service.deleteC124m01bs(models, false);
            }
            List<C124M01B> c124m01bs = new ArrayList<C124M01B>();
            for (String people : formSelectBoss) {
                C124M01B c124m01b = new C124M01B();
                c124m01b.setCreator(user.getUserId());
                c124m01b.setCreateTime(CapDate.getCurrentTimestamp());
                c124m01b.setMainId(c124m01a.getMainId());
                c124m01b.setBranchType(user.getUnitType());
                c124m01b.setBranchId(user.getUnitNo());
                // L1. 分行經辦 L3. 分行授信主管 L4. 分行覆核主管 L5. 經副襄理
                c124m01b.setStaffJob(UtilConstants.STAFFJOB.授信主管L3);
                c124m01b.setStaffNo(people);
                c124m01bs.add(c124m01b);
            }
            C124M01B managerC124m01b = new C124M01B();
            managerC124m01b.setCreator(user.getUserId());
            managerC124m01b.setCreateTime(CapDate.getCurrentTimestamp());
            managerC124m01b.setMainId(c124m01a.getMainId());
            managerC124m01b.setStaffJob(UtilConstants.STAFFJOB.單位授權主管L5);
            managerC124m01b.setStaffNo(manager);
            managerC124m01b.setBranchType(user.getUnitType());
            managerC124m01b.setBranchId(user.getUnitNo());
            c124m01bs.add(managerC124m01b);
            C124M01B apprC124m01b = new C124M01B();
            apprC124m01b.setCreator(user.getUserId());
            apprC124m01b.setCreateTime(CapDate.getCurrentTimestamp());
            apprC124m01b.setMainId(c124m01a.getMainId());
            apprC124m01b.setStaffJob(UtilConstants.STAFFJOB.經辦L1);
            apprC124m01b.setStaffNo(user.getUserId());
            apprC124m01b.setBranchType(user.getUnitType());
            apprC124m01b.setBranchId(user.getUnitNo());
            c124m01bs.add(apprC124m01b);
            cls3501Service.saveC124m01bList(c124m01bs);
        }

        Boolean upMis = false;

        C124M01B c124m01bL4 = new C124M01B();
        // 如果有這個key值表示是輸入chekDate核准日期  = 核定作業
        if (params.containsKey("checkDate")) {
            c124m01a.setApprover(user.getUserId());
            c124m01a.setApproveTime(CapDate.getCurrentTimestamp());
            upMis = true;
            C124M01B c124m01b = cls3501Service.findC124m01b(
                    c124m01a.getMainId(), user.getUnitType(), user.getUnitNo(),
                    user.getUserId(), UtilConstants.STAFFJOB.執行覆核主管L4);
            if (c124m01b == null) {
                c124m01b = new C124M01B();
                c124m01b.setCreator(user.getUserId());
                c124m01b.setCreateTime(CapDate.getCurrentTimestamp());
                c124m01b.setMainId(c124m01a.getMainId());
                c124m01b.setStaffJob(UtilConstants.STAFFJOB.執行覆核主管L4);
                c124m01b.setStaffNo(user.getUserId());
                c124m01b.setBranchType(user.getUnitType());
                c124m01b.setBranchId(user.getUnitNo());
            }
            c124m01bL4 = c124m01b;
        }

        if (!Util.isEmpty(c124m01a)) {
            try {
                // 如果有這值表示非呈主管，要檢查覆核主管和文件最後更新者是否相同
                if (params.containsKey("flowAction")) {
                    // 退回部檢查
                    if (params.getBoolean("flowAction")) {
                        C124M01B c124m01b = cls3501Service.findC124m01b(
                                c124m01a.getMainId(), user.getUnitType(),
                                user.getUnitNo(), user.getUserId(),
                                UtilConstants.STAFFJOB.經辦L1);

                        if (c124m01b != null) {
                            if(params.containsKey("batch") &&
                                    params.getBoolean("batch")){
								result.set("msg",
										RespMsgHelper.getMessage("EFD0053"));
                                return result;
                            } else {
                                // EFD0053=WARN|覆核人員不可與「經辦人員或其它覆核人員」為同一人|
								throw new CapMessageException(
										RespMsgHelper.getMessage("EFD0053"),
										getClass());
                            }
                        } else {
                            cls3501Service.save(c124m01bL4);
                            upMis = true;
                        }
                    }
                }
                cls3501Service.flowAction(c124m01a.getOid(), c124m01a,
                        params.containsKey("flowAction"),
                        params.getAsBoolean("flowAction", false), upMis);
            } catch (FlowException t1) {
                logger.error(
                        "[flowAction] cls3501Service.flowAction FlowException!!",
                        t1);
				throw new CapMessageException(
						RespMsgHelper.getMessage(t1.getMessage()), getClass());
            } catch (Throwable t1) {
                logger.error(
                        "[flowAction]  cls3501Service.flowAction EXCEPTION!!",
                        t1);
                throw new CapMessageException(t1.getMessage(), getClass());
            }
        }

        return new CapAjaxFormResult();
    }

    public void importCustData(C124M01A c124m01a) {
        String custId = Util.trim(c124m01a.getCustId());
        String dupNo = Util.trim(c124m01a.getDupNo());

        String borrowerBirthDay = "";   // 借款人出生日期：線上進件 -> 個人徵信作業基本資料
        String applyLoanDay = "";       // 申請受理日：線上進件
        BigDecimal applyLoan = null;    // 申請額度：線上進件
        String ownerCellphone = "";     // 申請人行動電話：線上進件 -> 個人徵信作業基本資料
        String email = "";              // 個人電子郵箱：線上進件 -> 個人徵信作業基本資料
        String tel = "";                // 聯絡電話：個人徵信作業基本資料
        String city = "";               // 地址：個人徵信作業基本資料
        String dist = "";

        // 線上進件
        Map<String, Object> online = eloandbService.getCustDataFormOnline(custId, dupNo);
        if(online != null && !online.isEmpty()){
            borrowerBirthDay = Util.trim(online.get("BIRTHDAY"));
            applyLoanDay = (Util.trim(online.get("APPLYTS")) == "" ? "" : CapDate.formatDate(
                    CapDate.parseDate(Util.trim(online.get("APPLYTS"))), "yyyy-MM-dd"));
            applyLoan = (Util.trim(online.get("APPLYAMT")) == "" ? null : BigDecimal.valueOf(
                    Double.valueOf(Util.trim(online.get("APPLYAMT")))*10000));
            ownerCellphone = Util.trim(online.get("MTEL"));
            email = Util.trim(online.get("EMAIL"));
        }

        // 個人徵信作業基本資料
        Map<String, Object> base = eloandbService.getCustDataFormBase(custId, dupNo);
        if(base != null && !base.isEmpty()){
            if(Util.isEmpty(borrowerBirthDay)){
                borrowerBirthDay = Util.trim(base.get("BIRTHDAY"));
            }
            tel = Util.trim(base.get("COTEL"));
            if(Util.isEmpty(ownerCellphone)){
                ownerCellphone = Util.trim(base.get("MTEL"));
            }
            if(Util.isEmpty(email)){
                email = Util.trim(base.get("EMAIL"));
            }
            city = Util.trim(base.get("FCITY"));
            dist = Util.trim(base.get("FZIP"));
        }

        c124m01a.setBorrowerBirthDay(CapDate.parseDate(borrowerBirthDay));
        c124m01a.setApplyLoanDay(CapDate.parseDate(applyLoanDay));
        if(applyLoan == null) {
            applyLoan = BigDecimal.valueOf(100000);
        }
        c124m01a.setApplyLoan(applyLoan);
        if (StringUtils.indexOf(tel, "-") >= 0 ||
                StringUtils.indexOf(tel, "(") >= 0 ||
                StringUtils.indexOf(tel, ")") >= 0) {
            String telArr[] = this.parseTel(tel);
            c124m01a.setTel1(telArr[0]);
            c124m01a.setTel2(telArr[1]);
        } else {
            if(Util.isNotEmpty(tel)) {
                if(tel.length() > 2 ) {
                    c124m01a.setTel1(tel.substring(0, 2));
                    if(tel.length() > 10) {
                        c124m01a.setTel2(tel.substring(2, 10));
                    } else {
                        c124m01a.setTel2(tel.substring(2));
                    }
                } else {
                    c124m01a.setTel1(tel);
                    c124m01a.setTel2("");
                }
            } else {
                c124m01a.setTel1("");
                c124m01a.setTel2("");
            }
        }
        if (StringUtils.indexOf(ownerCellphone, "-") >= 0 ||
                StringUtils.indexOf(ownerCellphone, "(") >= 0 ||
                StringUtils.indexOf(ownerCellphone, ")") >= 0) {
            String cpArr[] = this.parseTel(ownerCellphone);
            c124m01a.setOwnerCellphone1(cpArr[0]);
            c124m01a.setOwnerCellphone2(cpArr[1]);
        } else {
            if(Util.isNotEmpty(ownerCellphone)) {
                if(ownerCellphone.length() > 4 ) {
                    c124m01a.setOwnerCellphone1(ownerCellphone.substring(0,4));
                    if(ownerCellphone.length() > 10) {
                        c124m01a.setOwnerCellphone2(ownerCellphone.substring(4,10));
                    } else {
                        c124m01a.setOwnerCellphone2(ownerCellphone.substring(4));
                    }
                } else {
                    c124m01a.setOwnerCellphone1(ownerCellphone);
                    c124m01a.setOwnerCellphone2("");
                }
            } else {
                c124m01a.setOwnerCellphone1("");
                c124m01a.setOwnerCellphone2("");
            }
        }
        if(Util.isEmpty(email)){
            c124m01a.setIsEmail(null);
            c124m01a.setEmail("");
        } else {
            c124m01a.setIsEmail(1);
            c124m01a.setEmail(email);
        }
        c124m01a.setCity(city);
        c124m01a.setDist(dist);
        c124m01a.setZip(dist);
    }

    public void importImporterData(C124M01A c124m01a) {

        Map<String, Object> data = eloandbService.getLastImporterData(user.getUserId());
        if(data != null && !data.isEmpty()){
            c124m01a.setImporterNo(Util.trim(data.get("IMPORTERNO")));
            c124m01a.setImporterName(Util.trim(data.get("IMPORTERNAME")));
            c124m01a.setImporterCellphone1(Util.trim(data.get("IMPORTERCELLPHONE1")));
            c124m01a.setImporterCellphone2(Util.trim(data.get("IMPORTERCELLPHONE2")));
            c124m01a.setImporterTel1(Util.trim(data.get("IMPORTERTEL1")));
            c124m01a.setImporterTel2(Util.trim(data.get("IMPORTERTEL2")));
            c124m01a.setImporterTelExt(Util.trim(data.get("IMPORTERTELEXT")));
            c124m01a.setImporterEmail(Util.trim(data.get("IMPORTEREMAIL")));
        } else {
            c124m01a.setImporterNo(user.getUserId());
            c124m01a.setImporterName("");//user.getUserName());   畫面儲存會自動跟新
            c124m01a.setImporterCellphone1("");
            c124m01a.setImporterCellphone2("");

            Map<String, Object> bankInfo = misIcbcBrService.getBankInfo(user.getUnitNo());
            String TEL = (String) (bankInfo == null ? "" : Util.trim(bankInfo.get("TEL")));
            String tel[] = this.parseTel(TEL);
            c124m01a.setImporterTel1(tel[0]);
            c124m01a.setImporterTel2(tel[1]);

            ElsUser elUser = userInfoService.getUser(user.getUserId());
            String telExt = "";
            String newMailName = "";
            if (elUser != null) {
                telExt = Util.trim(elUser.getTelNo());
                newMailName = Util.trim(elUser.getNewMailName());
            }
            c124m01a.setImporterTelExt(telExt);
            c124m01a.setImporterEmail((Util.isNotEmpty(newMailName) ?
                    (newMailName + "@megabank.com.tw") : ""));
        }
    }
}
