/* 
 * LMS1401S02Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lns.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.lms.lns.panels.LMS1401S02Panel;
import com.mega.eloan.lms.lns.panels.LMS1401S02Panel01;
import com.mega.eloan.lms.lns.panels.LMS1401S02Panel02;
import com.mega.eloan.lms.lns.panels.LMS1401S02Panel03;
import com.mega.eloan.lms.lns.panels.LMS1401S02Panel04;
import com.mega.eloan.lms.lns.panels.LMS1401S02Panel05;
import com.mega.eloan.lms.lns.panels.LMS1401S02Panel06;
import com.mega.eloan.lms.lns.panels.LMS1401S02Panel07;
import com.mega.eloan.lms.lns.panels.LMS1401S02Panel08;
import com.mega.eloan.lms.lns.panels.LMS1401S02Panel09;
import com.mega.eloan.lms.lns.panels.LMS1401S02Panel10;
import com.mega.eloan.lms.lns.panels.LMS1401S02Panel11;
import com.mega.eloan.lms.lns.panels.LMS1401S02Panel13;

/**
 * <pre>
 * 額度明細表主要內容
 * </pre>
 * 
 * @since 2012/02/21
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/02/21,REX,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms1401m01")
public class LMS1401S02Page extends AbstractEloanForm {

	private static final long serialVersionUID = 1L;

	@Override
	public Class<? extends Meta> getDomainClass() {
		return null;
	}

	@Override
	public void execute(ModelMap model, PageParameters params) {
		new LMS1401S02Panel01("_LMS140PanelC_2_1").processPanelData(model, params);
		new LMS1401S02Panel02("_LMS140PanelC_2_2").processPanelData(model, params);
		new LMS1401S02Panel03("_LMS140PanelC_2_3").processPanelData(model, params);
		new LMS1401S02Panel04("_LMS140PanelC_2_4").processPanelData(model, params);
		new LMS1401S02Panel05("_LMS140PanelC_2_5").processPanelData(model, params);
		new LMS1401S02Panel06("_LMS140PanelC_2_6").processPanelData(model, params);
		new LMS1401S02Panel07("_LMS140PanelC_2_7").processPanelData(model, params);
		new LMS1401S02Panel08("_LMS140PanelC_2_8").processPanelData(model, params);
		new LMS1401S02Panel09("_LMS140PanelC_2_9").processPanelData(model, params);
		new LMS1401S02Panel10("_LMS140PanelC_2_10").processPanelData(model, params);
		new LMS1401S02Panel11("_LMS140PanelC_2_11").processPanelData(model, params);
		// J-107-09990_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊
		new LMS1401S02Panel13("_LMS140PanelC_2_13").processPanelData(model, params);

		renderJsI18N(LMS1401S02Panel.class);
		renderJsI18N(LMS1401S02Page.class);
		renderJsI18N(LMS1401S02Panel01.class);
		renderJsI18N(LMS1401S02Panel05.class);
	}

	@Override
	protected String getViewName() {
		return "common/pages/None";
	}

}
