initDfd.done(function(json){
//    alert("mow");
//    alert(json.rateRange1);
//    alert(json.rateRange1Type01);
//    alert("mow");

    if(json.rateRange1 == "Y"){
        $("#rateRange1View").show();
        
        if(json.jsShow_rateRange1View_N2=="Y"){
        	$("#rateRange1View_default").hide();
            $("#rateRange1View_N2").show();
        	
        }else{
        	$("#rateRange1View_default").show();
            $("#rateRange1View_N2").hide();
            
            //~~~~~~~~~~

            if(json.rateRange1Type01 == "Y"){ //第1段 是 固定利率
                $("#rateRange1Type01View").show();
                $("#rateRange1TypeOtherView").hide();
            } else {
                $("#rateRange1Type01View").hide();
                $("#rateRange1TypeOtherView").show();
            }
        }
        
    } else {
        $("#rateRange1View").hide();
    }
    if(json.rateRange2 == "Y"){
        $("#rateRange2View").show();
        if(json.rateRange2Type01 == "Y"){ //第2段 是 固定利率
            $("#rateRange2Type01View").show();
            $("#rateRange2TypeOtherView").hide();
        } else {
            $("#rateRange2Type01View").hide();
            $("#rateRange2TypeOtherView").show();
        }
    } else {
        $("#rateRange2View").hide();
    }

    if(json.advancedRedemptionDesc==""){
    	$("#advancedRedemptionDesc").hide();
    }

    if(json.relatedPersonId=="" && json.guaranteePlan==""){
    	$("#guaranteeAmt").hide();
    	$("#guaranteeAmtDollarUnit").hide();
    }
    
    if($("#ploanUploadGrid").length > 0){
    	$("#ploanUploadGrid").iGrid({
            handler: 'cls3401gridhandler',                
            height: 70,                
            shrinkToFit: false, 
	        needPager: false,
	        postData : {
				formAction : "queryPloanUploadGrid",
				mainId:responseJSON.mainId
			},
			colModel : [ {
				colHeader :i18n.cls3401m08['label.ploanUploadGrid.srcFileName'],//檔案名稱,
				name : 'srcFileName', width : 140, align: "left", sortable : false,
				formatter : 'click', onclick : attch_openDoc
			}, {
				colHeader : i18n.cls3401m08['label.ploanUploadGrid.fileDesc'],//檔案說明
				name : 'fileDesc', width : 180, sortable : false
			}, {
				colHeader : i18n.cls3401m08['label.ploanUploadGrid.uploadTime'],//上傳時間
				name : 'uploadTime', width : 140, sortable : false
			}, {
				name : 'oid',
				hidden : true
			}],
            ondblClickRow: function(rowid){
            }
    	});    
    }

    if(json.is_cntrNo_belong_co70647919_c101 =="Y"){
    	$("tr.dataForLoanPlan_C101").show();
    }

    //特定選項lock，可以看CLS3401S081Panel.html的class
    $(".detail_lock").readOnlyChilds(true);

    //金額加上comma符號
    $('#tabForm').find(".numeric").each(function() {
        $(this).val(util.addComma($(this).val()));
    }).change(function() {
        $(this).val(util.addComma($(this).val()));
    });
});


function attch_openDoc(cellvalue, options, rowObject){
    $.capFileDownload({
        handler:"simplefiledwnhandler",
        data : {
            fileOid:rowObject.oid
        }
    });
}