package com.mega.eloan.lms.mfaloan.service;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.mega.eloan.lms.mfaloan.bean.ELF412B;

public interface MisELF412BService {
	/**
	 * 依分行別取得覆審名單資料
	 * 
	 * @param branch
	 * @return
	 */
	List<Map<String, Object>> getByKeyWithBasicData(String branch);

	Map<String, Object> getByKeyWithBasicData(String branch, String custId,
			String dupNo);

	Map<String, Object> getDataWithPEO(String branch, String custId,
			String dupNo);

	public ELF412B findByPk(String branch, String custId, String dupNo);

	public List<ELF412B> findByBranch(String branch);

	public int updateELF412BNckdFlag(Date ELF412B_LRDATE,
			String ELF412B_NEWADD, String ELF412B_NEWDATE,
			String ELF412B_NCKDFLAG, Date ELF412B_NCKDDATE,
			String ELF412B_NCKDMEMO, Date ELF412B_NEXTNWDT,
			Date ELF412B_NEXTLTDT, Timestamp ELF412B_TMESTAMP,
			Date ELF412B_UPDDATE, String ELF412B_UPDATER,
			String ELF412B_BRANCH, String ELF412B_CUSTID, String ELF412B_DUPNO);

}
