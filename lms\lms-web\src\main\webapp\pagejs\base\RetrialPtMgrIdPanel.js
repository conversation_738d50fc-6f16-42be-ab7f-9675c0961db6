var RetrialPtMgrIdPanelAction = {
	open: function(options, choseDoneDfd){
		
		var grid_id = "divRetrialPtMgrIdPanel_grid";
		var $f = $("#RetrialPtMgrIdPanelForm");
		var dialog_height = 235;
		if(true){
			/* 
			  	若不每次都 new iGrid, 在 trigger ondblClickRow 只會抓到第一次 initialize 傳入的 dfd 
			  	所以在 html 定義一個 grid_wrap
			  	再每次都 empty 並重建 grid_id
			*/
			$("#divRetrialPtMgrIdPanel_grid_wrap").empty();
			$("#divRetrialPtMgrIdPanel_grid_wrap").html("<div id='"+grid_id+"'>");
			$f.reset();
			//========
			var my_post_data = {
				formAction : "queryElsUser"
			};
			
			if($("#"+grid_id+".ui-jqgrid-btable").length >0){
	    		$("#"+grid_id).jqGrid("setGridParam", {
					postData : my_post_data,
					search : true
				}).trigger("reloadGrid");	        		
	    	}else{	
	    		$("#"+grid_id).empty();
	    		$("#"+grid_id).iGrid({
					handler : 'lms2401gridhandler',
					height : dialog_height,
					rowNum : 10,
					sortname : 'userId',
					sortorder: 'asc',
					postData : my_post_data,
					multiselect: false,
					shrinkToFit: false,
					colModel : [ {
						colHeader : '行員編號', name : 'userId', align : "left", width : 120, sortable : true						
					}, {colHeader : '行員姓名', name : 'userName', align : "left", width : 200, sortable : true
					}
					],
					ondblClickRow: function(rowid){
						call_sel();            		
					}
				});
	    	}	
		}
		
		$("#divRetrialPtMgrIdPanel").thickbox({		
			title : options.title||'', width : 500, height : dialog_height+220, modal : false, i18n:i18n.def,
			buttons : {		
				"query" : function(){
					$("#"+grid_id).jqGrid("setGridParam", {
						postData : $.extend(my_post_data, $f.serializeData()),
						search : true
					}).trigger("reloadGrid");	
				},
				"import" : function(){
					call_sel();
				},
				"close" : function(){
					$.thickbox.close();
				}
			}
		});
		
		//=====
		function call_sel(){
			var $gridview = $("#"+grid_id);
        	var rowid = $gridview.getGridParam('selrow');
        	if(rowid){
        		var data = $gridview.getRowData(rowid);
        		if(data){        			
        			$.thickbox.close();
            		
            		choseDoneDfd.resolve( {'ptMgrId': data.userId } );	
        		}else{
					API.showMessage(i18n.def['grid.selrow']);//"請先選擇一筆資料。"
				}	        		
        	}else{
        		API.showMessage(i18n.def['grid_selector']);//"請選擇資料"	            		
        	}
		}
	}    
};
