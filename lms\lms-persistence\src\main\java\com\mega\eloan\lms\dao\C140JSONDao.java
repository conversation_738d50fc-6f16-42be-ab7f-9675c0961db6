/* 
 * C140JSONDao.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;
import tw.com.iisi.cap.model.GenericBean;

import com.mega.eloan.lms.model.C140JSON;

/**
 * <pre>
 * 徵信調查報告書副檔 JSON
 * </pre>
 * 
 * @since 2011/9/20
 * <AUTHOR>
 * @version <ul>
 *          <li>new
 *          </ul>
 */
public interface C140JSONDao extends IGenericDao<C140JSON> {

	/**
	 * 取得JSON資料
	 * 
	 * @param uid
	 *            String
	 * @param mainId
	 *            String
	 * @return C140JSON
	 */
	List<C140JSON> findByMainPid(String uid, String mainId);
	
	/**
	 * 取得JSON資料
	 * 
	 * @param uid
	 *            String
	 * @param mainId
	 *            String
	 * @param tab
	 *            String
	 * @return C140JSON
	 */
	List<C140JSON> findByMainPidTab(String uid, String mainId, String tab);

	/**
	 * 取得JSON資料
	 * 
	 * @param uid
	 *            String
	 * @param mainId
	 *            String
	 * @param tab
	 *            String
	 * @param subTab
	 *            String
	 * @return C140JSON
	 */
	C140JSON findByMainPidTab(String uid, String mainId, String tab,
			String subTab);

	/**
	 * 刪除所有資料
	 * 
	 * @param meta
	 *            GenericBean
	 * @return int
	 */
	int deleteByMeta(GenericBean meta);
}
