/* 
 * LMSUtil.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.common;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.DecimalFormat;
import java.text.Format;
import java.text.MessageFormat;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.TreeSet;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.i18n.LocaleContextHolder;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.BranchTypeEnum;
import com.mega.eloan.common.enums.TypCdEnum;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.constants.ScoreCardLoan;
import com.mega.eloan.lms.base.constants.URLConstant;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.constants.UtilConstants.L140M01MCbcCase;
import com.mega.eloan.lms.base.pages.AbstractOverSeaCLSPage;
import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.eloan.lms.base.panels.LMSL140M01MPanel;
import com.mega.eloan.lms.base.panels.LMSS20APanel;
import com.mega.eloan.lms.base.service.ProdService;
import com.mega.eloan.lms.model.C101M01A;
import com.mega.eloan.lms.model.C101S01A;
import com.mega.eloan.lms.model.C101S01B;
import com.mega.eloan.lms.model.C101S01C;
import com.mega.eloan.lms.model.C101S01D;
import com.mega.eloan.lms.model.C101S01E;
import com.mega.eloan.lms.model.C101S01F;
import com.mega.eloan.lms.model.C101S01G;
import com.mega.eloan.lms.model.C101S01G_N;
import com.mega.eloan.lms.model.C101S01H;
import com.mega.eloan.lms.model.C101S01I;
import com.mega.eloan.lms.model.C101S01J;
import com.mega.eloan.lms.model.C101S01K;
import com.mega.eloan.lms.model.C101S01L;
import com.mega.eloan.lms.model.C101S01M;
import com.mega.eloan.lms.model.C101S01N;
import com.mega.eloan.lms.model.C101S01O;
import com.mega.eloan.lms.model.C101S01P;
import com.mega.eloan.lms.model.C101S01Q;
import com.mega.eloan.lms.model.C101S01Q_N;
import com.mega.eloan.lms.model.C101S01R;
import com.mega.eloan.lms.model.C101S01R_N;
import com.mega.eloan.lms.model.C101S01S;
import com.mega.eloan.lms.model.C101S01U;
import com.mega.eloan.lms.model.C101S01W;
import com.mega.eloan.lms.model.C101S01X;
import com.mega.eloan.lms.model.C101S01Y;
import com.mega.eloan.lms.model.C101S01Z;
import com.mega.eloan.lms.model.C101S02A;
import com.mega.eloan.lms.model.C101S02B;
import com.mega.eloan.lms.model.C101S02C;
import com.mega.eloan.lms.model.C101S02S;
import com.mega.eloan.lms.model.C102M01A;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.C120S01B;
import com.mega.eloan.lms.model.C120S01C;
import com.mega.eloan.lms.model.C120S01D;
import com.mega.eloan.lms.model.C120S01E;
import com.mega.eloan.lms.model.C120S01F;
import com.mega.eloan.lms.model.C120S01G;
import com.mega.eloan.lms.model.C120S01H;
import com.mega.eloan.lms.model.C120S01I;
import com.mega.eloan.lms.model.C120S01J;
import com.mega.eloan.lms.model.C120S01K;
import com.mega.eloan.lms.model.C120S01L;
import com.mega.eloan.lms.model.C120S01M;
import com.mega.eloan.lms.model.C120S01N;
import com.mega.eloan.lms.model.C120S01O;
import com.mega.eloan.lms.model.C120S01P;
import com.mega.eloan.lms.model.C120S01Q;
import com.mega.eloan.lms.model.C120S01R;
import com.mega.eloan.lms.model.C120S01S;
import com.mega.eloan.lms.model.C120S01U;
import com.mega.eloan.lms.model.C120S01W;
import com.mega.eloan.lms.model.C120S01X;
import com.mega.eloan.lms.model.C120S01Y;
import com.mega.eloan.lms.model.C120S01Z;
import com.mega.eloan.lms.model.C120S02A;
import com.mega.eloan.lms.model.C120S02B;
import com.mega.eloan.lms.model.C120S02C;
import com.mega.eloan.lms.model.C120S02S;
import com.mega.eloan.lms.model.C160S01C;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01I;
import com.mega.eloan.lms.model.L120S01B;
import com.mega.eloan.lms.model.L120S01D;
import com.mega.eloan.lms.model.L120S01M;
import com.mega.eloan.lms.model.L120S01Q;
import com.mega.eloan.lms.model.L120S03A;
import com.mega.eloan.lms.model.L120S09A;
import com.mega.eloan.lms.model.L120S09B;
import com.mega.eloan.lms.model.L120S24A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01A_BF;
import com.mega.eloan.lms.model.L140M01C;
import com.mega.eloan.lms.model.L140M01C_BF;
import com.mega.eloan.lms.model.L140M01D;
import com.mega.eloan.lms.model.L140M01D_BF;
import com.mega.eloan.lms.model.L140M01E;
import com.mega.eloan.lms.model.L140M01M;
import com.mega.eloan.lms.model.L140M01Q;
import com.mega.eloan.lms.model.L140M01T;
import com.mega.eloan.lms.model.L140S02A;
import com.mega.eloan.lms.model.L730M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import jxl.format.Alignment;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WriteException;
import net.sf.cglib.beans.BeanMap;
import sun.misc.BASE64Decoder;
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapMath;
import tw.com.jcs.common.Arithmetic;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.PropUtil;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * BY 專案共用Util
 * </pre>
 * 
 * @since 2012/1/13
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/13,REX,new
 *          <li>2012/1/13,REX add forFiveBossId 取後五碼員編<br>
 *          <li>2012/1/13,REX add isContainValue 檢核性質是否有包含該值<br>
 *          <li>2012/1/13,REX add covertAs400Time 轉換AS400的時間格式<br>
 *          <li>2013/07/08,Rex,修改值錯誤評等6,7為B
 *          </ul>
 */
public class LMSUtil {
	private static final Logger logger = LoggerFactory.getLogger(Util.class);
	// 若採用 static final Properties :var =
	// MessageBundleScriptCreator.getComponentResource(?.class); 的寫法，是否可能抓到
	// private static final Properties prop_AbstractOverSeaCLSPage =
	// MessageBundleScriptCreator
	// .getComponentResource(AbstractOverSeaCLSPage.class);
	public static final String 地政士黑名單警示名單 = "Prompt";
	public static final String 地政士黑名單拒絕名單 = "Block";
	public static BASE64Decoder decoder = new sun.misc.BASE64Decoder();

	/**
	 * 個金徵信借款人
	 */
	public static final Class<?>[] C120Class = { C120M01A.class,
			C120S01A.class, C120S01B.class, C120S01C.class, C120S01D.class,
			C120S01E.class, C120S01F.class, C120S01G.class, C120S01H.class,
			C120S01I.class, C120S01J.class, C120S01K.class, C120S01L.class,
			C120S01M.class, C120S01N.class, C120S01O.class, C120S01P.class,
			C120S01Q.class, C120S01R.class, C120S01S.class, C120S01U.class,
			C120S01W.class, C120S01X.class, C120S01Y.class, C120S01Z.class,
			C120S02A.class, C120S02B.class, C120S02S.class, C120S02C.class }; // 未來增加 Table 時
	// 需(A)一併修改 ClsUtil.java 裡的 changeClass(Class<?> clazz)
	// 需(B)一併修改LMSServiceImpl 裡的 updCustId(...) 聯徵來文，客戶ID重配號
	public static final Class<?>[] C101Class = { C101M01A.class,
			C101S01A.class, C101S01B.class, C101S01C.class, C101S01D.class,
			C101S01E.class, C101S01F.class, C101S01G.class, C101S01H.class,
			C101S01I.class, C101S01J.class, C101S01K.class, C101S01L.class,
			C101S01M.class, C101S01N.class, C101S01O.class, C101S01P.class,
			C101S01Q.class, C101S01R.class, C101S01S.class, C101S01U.class,
			C101S01W.class, C101S01X.class, C101S01Y.class, C101S01Z.class,
			C101S02A.class, C101S02B.class, C101S02S.class, C101S02C.class };
	
	//消金評等雙軌資料檔
	public static final Class<?>[] C101_NClass = { C101S01G_N.class,
		C101S01Q_N.class, C101S01R_N.class };

	public static boolean disableC101S01F_C120S01F() {
		return true;
	}

	/**
	 * 
	 * 取得簽報書上傳案號-> 民國年 + 分行別+{LMS企金、CLS個金}+末五碼流水號
	 * 
	 * @param l120m01a
	 *            簽報書
	 * @return 案號 民國年 + 分行別+{LMS企金、CLS個金}+末五碼流水號
	 */
	public static String getUploadCaseNo(L120M01A l120m01a) {

		return getUploadCaseNo(l120m01a, true);
	}

	/**
	 * 取得簽報書上傳案號-> 民國年 + 分行別+{LMS企金、CLS個金}+末五碼流水號
	 * 
	 * @param l120m01a
	 *            簽報書
	 * @param showSchema
	 *            是否顯示Schema
	 * @return 案號 民國年 + 分行別+{LMS企金、CLS個金}+末五碼流水號
	 */
	public static String getUploadCaseNo(L120M01A l120m01a, boolean showSchema) {
		String schema = "";
		if (showSchema) {
			if (UtilConstants.Casedoc.DocType.個金.equals(l120m01a.getDocType())) {
				schema = UtilConstants.CaseSchema.個金;
			} else {
				schema = UtilConstants.CaseSchema.企金;
			}
		}
		return StrUtils.concat(l120m01a.getCaseYear() - 1911,
				l120m01a.getCaseBrId(), schema,
				Util.addZeroWithValue(Util.trim(l120m01a.getCaseSeq()), 5));
	}

	/**
	 * 取得簽報書上傳案號-> 民國年 + 分行別+{LMS企金、CLS個金}+末三碼流水號
	 * 
	 * @param l120m01a
	 *            簽報書
	 * @param showSchema
	 *            是否顯示Schema
	 * @return 案號 民國年 + 分行別+{LMS企金、CLS個金}+末三碼流水號
	 */
	public static String getUploadCaseNo3(L120M01A l120m01a, boolean showSchema) {
		String schema = "";
		if (showSchema) {
			if (UtilConstants.Casedoc.DocType.個金.equals(l120m01a.getDocType())) {
				schema = UtilConstants.CaseSchema.個金;
			} else {
				schema = UtilConstants.CaseSchema.企金;
			}
		}
		return StrUtils.concat(l120m01a.getCaseYear() - 1911,
				l120m01a.getCaseBrId(), schema,
				Util.addZeroWithValue(l120m01a.getCaseSeq(), 3));
	}

	/**
	 * 取得授信報案考核表上傳案號-> 民國年 + 分行名稱+(兆)授字第+末五碼流水號
	 * 
	 * @param l730m01a
	 *            授信報案考核表
	 * @return 民國年 + 分行名稱+(兆)授字第+末五碼流水號
	 */
	public static String getUploadCaseNo(L730M01A l730m01a) {
		String projNo = "";
		// 案號
		projNo = Util.trim(l730m01a.getProjNo());
		if (!Util.isEmpty(projNo)) {
			projNo = StrUtils.concat(Util.toFullCharString(CapDate
					.convertDateToTaiwanYear(Util.toSemiCharString(projNo
							.substring(0, 4)))), projNo.substring(4,
					projNo.length() - 6), Util.toFullCharString(Util
					.addZeroWithValue(
							Util.parseInt(Util.toSemiCharString(projNo
									.substring(projNo.length() - 6,
											projNo.length() - 1))), 5)), projNo
					.substring(projNo.length() - 1));
		}
		return projNo;
	}

	/**
	 * 取得上傳案號-> 民國年 + 分行別+{LMS企金、CLS個金}+末五碼流水號
	 * 
	 * 
	 * @return 案號 918 + FMS + 民國年月日時分
	 */
	public static String getUploadCaseNoForCentralBankMarkMaintenance() {

		SimpleDateFormat sf = new SimpleDateFormat("MMddHHmm");
		Calendar calendar = Calendar.getInstance();
		String dayTime = sf.format(calendar.getTime());
		String chineseYear = CapDate.convertDateToTaiwanYear(String
				.valueOf(calendar.get(Calendar.YEAR)));
		return UtilConstants.BankNo.授管處 + "FMS" + chineseYear + dayTime;
	}

	/**
	 * 上傳員編為五碼 ，目前員編為六碼取後五碼
	 * 
	 * <pre>
	 * @param value
	 *            員編
	 * 
	 * ex: forFiveBossId("012345") =12345
	 * @return 五碼的員編
	 * </pre>
	 */
	public static String forFiveBossId(String value) {
		if (Util.isEmpty(value)) {
			return null;
		}
		return Util.getRightStr(value, 5);
	}

	/**
	 * 
	 * 檢核性質是否有包含該值
	 * 
	 * <pre>
	 * @param value
	 *            被 | 劃分的值
	 * @param checkData
	 *            要檢查的值
	 * 
	 *            ex: isContainValue("1"|"2", "1") = true
	 * @return true 包含 false 不包含
	 * </pre>
	 */
	public static boolean isContainValue(String value, String checkData) {
		if (Util.isEmpty(value)) {
			return false;
		}
		String[] prorertyArray = value.split(UtilConstants.Mark.SPILT_MARK);
		for (String data : prorertyArray) {
			if (checkData.equals(data)) {
				return true;
			}
		}
		return false;
	}

	/**
	 * 轉換AS400的時間格式
	 * 
	 * <pre>
	 * 針對時間格式 Timestamp 
	 *            Date
	 *            或格式為yyyy-mm-dd的時間格式轉換成數字
	 * </pre>
	 * 
	 * @param objects
	 *            List<Object[]>
	 * @return List<Object[]>
	 */
	public static List<Object[]> covertAs400Time(List<Object[]> objects) {
		List<Object[]> newList = new ArrayList<Object[]>();

		for (Object[] value : objects) {
			newList.add(covertAs400Time(value));
		}

		return newList;
	}

	/**
	 * 轉換AS400的時間格式
	 * 
	 * <pre>
	 * 針對時間格式 Timestamp 
	 *            Date
	 *            或格式為yyyy-mm-dd的時間格式轉換成數字
	 * </pre>
	 * 
	 * @param objects
	 *            object陣列
	 * @return Object[]
	 */
	public static Object[] covertAs400Time(Object[] objects) {

		List<Object> temp = new ArrayList<Object>();

		for (Object b : objects) {
			if (b == null) {
				temp.add(null);
			} else if (b.getClass() == Timestamp.class) {
				temp.add(covertAs400Time((Timestamp) b));
			} else if (b.getClass() == Date.class) {
				temp.add(CapDate.formatDate((Date) b, "yyyyMMdd"));
			} else if (b.toString().matches("^\\d{4}-\\d{1,2}-\\d{1,2}$")) {
				temp.add(new BigDecimal(b.toString().replaceAll("\\D", "")));
			} else {
				temp.add(b);
			}
		}
		return temp.toArray();
	}

	/**
	 * 字串後補0
	 * 
	 * @param str
	 * <br/>
	 * @param num
	 *            不足幾位補零 <br/>
	 * @return 補0後之字串 <br/>
	 * <AUTHOR> EX: addZeroWithValue("1",3) -> "100"
	 */
	public static String addZeroWithValue(String str, int num) {
		StringBuffer sb = new StringBuffer();
		for (int i = 0, len = num - Util.trim(str).length(); i < len; i++) {
			sb.append("0");
		}
		return str + sb.toString();
	}

	/**
	 * 轉換As400時間
	 * 
	 * @param time
	 *            時間格式
	 * @return BigDecimal
	 */
	public static BigDecimal covertAs400Time(Timestamp time) {

		return new BigDecimal(
				StringUtils.left(
						addZeroWithValue(time.toString().replaceAll("\\D", ""),
								17), 17));

	}

	/**
	 * 取得組合字串
	 * 
	 * @param temp
	 *            暫存的 StringBuffer
	 * @param params
	 *            字串物件
	 * @return 組合字串
	 */
	public final static String concat(StringBuffer temp, Object... params) {
		temp.setLength(0);
		for (Object o : params) {
			if (o instanceof byte[]) {
				temp.append(new String((byte[]) o));
			} else {
				temp.append(String.valueOf(o));
			}
		}
		return temp.toString();
	}

	/**
	 * 轉換小數點後非0的數字
	 * 
	 * @param num
	 *            要處理的值
	 * @return new BigDecimal
	 * 
	 *         <pre>
	 * 
	 * 		當有小數點時，預設顯示為進位到小數點兩位
	 *  		EX:0.02184時，則顯示 0.02
	 * 		但是，當小數點前兩為都是0時，則進位並顯示到最後一個不為0的小數，且不設限小數五位
	 *  		EX:0.00123時，則顯示 0.001 ;
	 *  		   0.00178時，則顯示 0.002
	 *  		0.00000167時，則顯示 0.000002 (不設限小數五位)
	 * 
	 * </pre>
	 */
	public static String calcZero(BigDecimal num) {
		NumberFormat n = new DecimalFormat("#.###############");
		int maxScale = 11;
		for (int i = 1; i < maxScale; i++) {
			if (num.movePointRight(i).compareTo(BigDecimal.ONE) == 1) {
				if (i > 2) {
					return n.format(num.setScale(i, BigDecimal.ROUND_HALF_DOWN));
				} else {
					return n.format(num.setScale(2, BigDecimal.ROUND_HALF_DOWN));
				}

			}
		}
		return "0";
	}

	/**
	 * 取得地區別
	 * 
	 * @return locale
	 */
	public static Locale getLocale() {
		// zh_TW: 正體中文
		// zh_CN: 簡體中文
		// en_US: 英文
		Locale locale = LocaleContextHolder.getLocale();
		if (locale == null)
			locale = Locale.getDefault();
		return locale;
	}

	/**
	 * 取得Flow代號
	 * 
	 * @param IBranchs
	 *            分行資料
	 * @return flow 的名稱
	 */
	public static String getFlowCode(IBranch ibranch) {
		String flowName = "";
		String brNoType = ibranch.getCountryType();

		if (UtilConstants.Country.泰國.equals(brNoType)) {
			flowName = "LMS1205TH01Flow";
		} else if (UtilConstants.Country.加拿大.equals(brNoType)
				|| UtilConstants.Country.美國.equals(brNoType)
				|| UtilConstants.Country.柬埔寨.equals(brNoType)
				|| UtilConstants.Country.日本.equals(brNoType)) {
			flowName = "LMS1205CA01Flow";
			// 如果日本流程要走澳洲流程只要加在這就好
		} else if (UtilConstants.Country.澳洲.equals(brNoType)) {
			flowName = "LMS1205AU01Flow";
		} else if (UtilConstants.Country.中國.equals(brNoType)) {
			// 原寧波分行與吳江支行走不同FLOW，後來企劃處簽准一率走COUNTRY HEAD模式
			// String unitType = ibranch.getUnitType();
			// // Q - 泰國以外海外總行(海外總行澳洲加拿大)
			// if (Util.equals(unitType, UtilConstants.unitType.泰國以外海外總行)
			// || Util.equals(unitType, UtilConstants.unitType.海外分行當地有總行)) {
			// // 蘇州分行 吳江支行 走 LMS1205CA01Flow
			// flowName = "LMS1205CA01Flow";
			// } else {
			// // 寧波分行走海外一般分行--LMS1205Flow
			// flowName = "LMS1205Flow";
			// }
			flowName = "LMS1205CA01Flow";

		} else {
			if (isSpecialBranch(ibranch.getBrNo())) {
				// 總部分行
				flowName = "LMS1201Flow";
			} else if (BranchTypeEnum.一般分行.getCode().equals(
					ibranch.getUnitType())
					|| BranchTypeEnum.簡易分行.getCode().equals(
							ibranch.getUnitType())) {
				// 國內分行
				flowName = "LMS1200Flow";
			} else {
				// 海外其他分行
				flowName = "LMS1205Flow";
			}
		}
		return flowName;
	}

	/**
	 * 判斷是否為總處營業單位
	 * 
	 * @param brid
	 *            分行代碼
	 * @return true -> 是 false -> 否
	 */
	public static boolean isSpecialBranch(String brid) {
		if (UtilConstants.BankNo.國外部.equals(brid)
				|| UtilConstants.BankNo.財富管理處.equals(brid)
				|| UtilConstants.BankNo.國金部.equals(brid)
				|| UtilConstants.BankNo.財務部.equals(brid)
				|| UtilConstants.BankNo.金控總部分行.equals(brid)
				|| UtilConstants.BankNo.授信行銷處.equals(brid)
				|| UtilConstants.BankNo.消金業務處.equals(brid)
				|| UtilConstants.BankNo.私銀處作業組.equals(brid)) {
			return true;
		} else if (UtilConstants.BankNo.授管處.equals(brid)) {
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			String unitType = user.getUnitType();
			// 判斷授管處審查條件時
			if (Util.notEquals(unitType, "S")) {
				return true;
			} else {
				return false;
			}
		} else {
			return false;
		}
	}

	/**
	 * 本程式已廢除
	 * 
	 * 取得Flow代號 海外使用-----2015/02/26 取消不用 改用 getFlowCode(IBranch branch)
	 * 
	 * J-109-0363_05097_B1001 Web
	 * e-Loan授信修改日本地區分行大阪分行授權外案件經由東京分行放行後送呈授信審查處(Country Head 模式)。
	 * 
	 * @param brNoType
	 *            分行國別
	 * @return flow 的名稱
	 */
	public static String getFlowCode(String brNoType) {
		String flowName = "";
		if (UtilConstants.Country.泰國.equals(brNoType)) {
			flowName = "LMS1205TH01Flow";
		} else if (UtilConstants.Country.加拿大.equals(brNoType)
				|| UtilConstants.Country.美國.equals(brNoType)
				|| UtilConstants.Country.日本.equals(brNoType)) {
			flowName = "LMS1205CA01Flow";
			// 如果日本流程要走澳洲流程只要加在這就好
		} else if (UtilConstants.Country.澳洲.equals(brNoType)) {
			flowName = "LMS1205AU01Flow";
		} else if (UtilConstants.Country.中國.equals(brNoType)) {
			flowName = "LMS1205CA01Flow";

		} else {
			flowName = "LMS1205Flow";
		}
		return flowName;
	}

	/**
	 * 轉換BigDecimal
	 * 
	 * @param input
	 * @return
	 */
	public static BigDecimal toBigDecimal(String input) {
		if (input == null) {
			return null;
		} else if ("".equals(Util.nullToSpace(Util.trim(input)))) {
			return null;
		} else {
			return new BigDecimal(input);
		}
	}

	/**
	 * 轉換BigDecimal
	 * 
	 * @param input
	 * @return
	 */
	public static BigDecimal toBigDecimal(Object input) {
		return LMSUtil.toBigDecimal(Util.nullToSpace(input));
	}

	/**
	 * 轉換BigDecimal
	 * 
	 * @param input
	 * @return
	 */
	public static BigDecimal nullToZeroBigDecimal(String input) {
		if (input == null) {
			return BigDecimal.ZERO;
		} else if ("".equals(Util.nullToSpace(Util.trim(input)))) {
			return BigDecimal.ZERO;
		} else {
			return new BigDecimal(input);
		}
	}

	/**
	 * 轉換BigDecimal
	 * 
	 * @param input
	 * @return
	 */
	public static BigDecimal nullToZeroBigDecimal(Object input) {
		return LMSUtil.nullToZeroBigDecimal(Util.nullToSpace(input));
	}

	/**
	 * 取得附加檔案預設上傳目錄
	 * 
	 * @param brNo
	 * @param mainId
	 * @param forderName
	 * @return
	 */
	public static String getUploadFilePath(String brNo, String mainId,
			String forderName) {
		return PropUtil.getProperty("docFile.dir") + "/"
				+ PropUtil.getProperty("systemId").toUpperCase() + "/" + brNo
				+ "/" + CapDate.getCurrentDate("yyyy") + "/" + mainId + "/"
				+ forderName;
	}

	/**
	 * 計算MapGrid 筆數與顯示資料
	 * 
	 * @param AllData
	 *            查詢出來的全部資料
	 * @param search
	 *            搜尋條件
	 * @return MapGrid
	 */
	public static Page<Map<String, Object>> getMapGirdDataRow(
			List<Map<String, Object>> AllData, ISearch search) {
		/*
		 * TODO 當【查詢出來的資料筆數 > Grid每頁筆數】時，應該有第2、3...頁，以下寫法的 總頁數只有1頁(會讓人誤會)
		 * Page<Map<String, Object>> n_page = LMSUtil.getMapGirdDataRow(list,
		 * pageSetting); return new CapMapGridResult(n_page.getContent(),
		 * n_page.getTotalRow());
		 * 
		 * 在 CapMapGridResult 的第2個參數，應傳入總筆數，而非「過濾後」的筆數
		 */
		int start = search.getFirstResult();
		int pagNumber = search.getMaxResults();
		// 要顯示的資料
		int end = start + pagNumber > AllData.size() ? start
				+ (AllData.size() - start) : start + pagNumber;
		List<Map<String, Object>> beanListnew = new ArrayList<Map<String, Object>>();
		for (int b = start; b < end; b++) {
			Map<String, Object> rowData = AllData.get(b);
			beanListnew.add(rowData);
		}
		return new Page<Map<String, Object>>(beanListnew, AllData.size(),
				search.getMaxResults(), search.getFirstResult());
	}

	/**
	 * 是否含有遠匯科目 "961", "962", "963", "964", "950","971"
	 * 
	 * @param l140m01a
	 *            額度明細表主檔
	 * @return true | false
	 */
	public static Boolean isNeedX(L140M01A l140m01a) {
		Boolean result = false;
		if (l140m01a == null) {
			return result;
		}
		Set<L140M01C> l140m01cs = l140m01a.getL140m01c();
		// 遠匯科目
		String checkItem = "961,962,963,964,950,971,9611,Z12"; // Z12,Z13,Z14理論上也要X
																// 但是考量舊案
																// 改為LMS1401S02Panel02.js
																// 新案不得選Z13、Z14
		if (l140m01cs != null) {
			for (L140M01C l140m01c : l140m01cs) {
				if (checkItem.indexOf(l140m01c.getLoanTP()) != -1) {
					result = true;
				}
			}
		}
		return result;
	}

	/**
	 * 檢查有遠匯授信科目之額度序號第八碼必須為X
	 * 
	 * @param l140m01a
	 *            額度明細表
	 * @param parent
	 *            語系檔
	 * @param keyWord
	 *            攤貸比率字串
	 * @return 錯誤訊息
	 */

	public static String cntrNoforX(L140M01A l140m01a, String keyWord) {
		String cntrNo = Util.trim(l140m01a.getCntrNo());

		String errorMsg = "";
		if (isNeedX(l140m01a)) {
			// EFD3041=INFO|遠匯科目之額度序號第八碼必須為X。|
			errorMsg = RespMsgHelper.getMainMessage("EFD3041");
			if (Util.isNotEmpty(cntrNo)) {
				// 檢查額度序號
				if (!"X".equals(cntrNo.substring(7, 8))) {
					return errorMsg;
				}
			}
			// 檢查聯行攤貸比率額度序號
			Set<L140M01E> l140m01es = l140m01a.getL140m01e();
			if (l140m01es != null) {
				for (L140M01E l140m01e : l140m01es) {
					if (!"X".equals(l140m01e.getShareNo().substring(7, 8))) {
						return keyWord + errorMsg;
					}
				}
			}

		} else {
			// EFD3042=INFO|一般授信科目之額度序號第八碼不得為X。|
			errorMsg = RespMsgHelper.getMainMessage("EFD3042");
			if (Util.isNotEmpty(cntrNo)) {
				// 檢查額度序號
				if ("X".equals(cntrNo.substring(7, 8))) {
					return errorMsg;
				}
			}
			// 檢查聯行攤貸比率額度序號
			Set<L140M01E> l140m01es = l140m01a.getL140m01e();
			if (l140m01es != null) {
				for (L140M01E l140m01e : l140m01es) {
					if ("X".equals(l140m01e.getShareNo().substring(7, 8))) {
						return keyWord + errorMsg;
					}
				}
			}

		}
		return "";
	}

	public static WritableCellFormat setCellFormat(WritableCellFormat format,
			WritableFont font, Alignment alignment) throws WriteException {
		return LMSUtil.setCellFormat(format, font, alignment, true);
	}

	/**
	 * @param format
	 * @param font
	 * @param alignment
	 * @return
	 * @throws WriteException
	 */
	public static WritableCellFormat setCellFormat(WritableCellFormat format,
			WritableFont font, Alignment alignment, boolean borderResult)
			throws WriteException {
		return LMSUtil.setCellFormat(format, font, alignment, borderResult,
				true);
	}

	/**
	 * 設定字型格式
	 * 
	 * @param format
	 *            格式
	 * @param font
	 *            自行
	 * @param alignment
	 *            靠左靠右
	 * @param borderResult
	 *            設置邊框
	 * @param wrapResult
	 *            自動換行
	 * @return
	 * @throws WriteException
	 */
	public static WritableCellFormat setCellFormat(WritableCellFormat format,
			WritableFont font, Alignment alignment, boolean borderResult,
			boolean wrapResult) throws WriteException {
		format = new WritableCellFormat(font);
		format.setVerticalAlignment(jxl.format.VerticalAlignment.TOP);
		format.setAlignment(alignment);
		// 自動換行
		format.setWrap(wrapResult);
		// 設置邊框
		if (borderResult) {
			format.setBorder(jxl.format.Border.ALL,
					jxl.format.BorderLineStyle.THIN);
		}
		return format;
	}

	/**
	 * 取得上下幾個個月的第一天
	 * 
	 * @return
	 */
	public static Date getExMonthFirstDay(int month) {
		return LMSUtil.getExMonthDay(month, true, true);
	}

	/**
	 * 取得上下幾個個月的最後一天
	 * 
	 * @return
	 */
	public static Date getExMonthLastDay(int month) {
		return LMSUtil.getExMonthDay(month, true, false);
	}

	/**
	 * 取得上下幾個個月的同一天
	 * 
	 * @return
	 */
	public static Date getExMonthDay(int month) {
		return LMSUtil.getExMonthDay(month, false, false);
	}

	/**
	 * 取得上下幾個月的第一天或最後一天
	 * 
	 * @return
	 */
	@SuppressWarnings("deprecation")
	public static Date getExMonthDay(int month, boolean setDateResult,
			boolean setFirstResult) {
		// Date date = CapDate.getCurrentTimestamp();
		// date.setMonth(date.getMonth() + month);
		// if (setDateResult) {
		// if (setFirstResult) {
		// date.setDate(1);
		// } else {
		// date.setDate(CapDate.getDayOfMonth(TWNDate.toAD(date)
		// .split("-")[0], TWNDate.toAD(date).split("-")[1]));
		// }
		// }
		//
		// return date;

		Calendar today = Calendar.getInstance();
		today.add(Calendar.MONTH, month);
		if (setDateResult) {

			if (setFirstResult) {
				// 第一天
				today.set(Calendar.DAY_OF_MONTH,
						today.getActualMinimum(Calendar.DAY_OF_MONTH));
			} else {
				// 最後一天
				today.set(Calendar.DAY_OF_MONTH,
						today.getActualMaximum(Calendar.DAY_OF_MONTH));

			}
		}

		return today.getTime();
	}

	/**
	 * 取得特定年月的第一天或最後一天
	 * 
	 * @return
	 */
	@SuppressWarnings("deprecation")
	public static Date getExMonthDay(int year, int month,
			boolean setDateResult, boolean setFirstResult) {
		Date date = CapDate.getCurrentTimestamp();
		date.setYear(year - 1900);
		date.setMonth(month - 1);
		if (setDateResult) {
			if (setFirstResult) {
				date.setDate(1);
			} else {
				date.setDate(CapDate.getDayOfMonth(TWNDate.toAD(date)
						.split("-")[0], TWNDate.toAD(date).split("-")[1]));
			}
		}

		return date;
	}

	/**
	 * 將params轉換為JSON物件
	 * 
	 * @param params
	 * @return
	 */
	public static JSONObject convertParamsToJSON(PageParameters params) {
		if (params.isEmpty()) {
			return new JSONObject();
		} else {
			CapAjaxFormResult capParam = new CapAjaxFormResult(params);
			return JSONObject.fromObject(capParam);
		}
	}

	/**
	 * 檢查subString方法是否可用
	 * 
	 * @param str
	 *            字串
	 * @param start
	 *            擷取字串開始點
	 * @return true: 可用, false: 不可用
	 */
	public static boolean checkSubStr(String str, int start) {
		if (start < 0 || start > str.length()) {
			return false;
		} else {
			return true;
		}
	}

	/**
	 * 檢查subString方法是否可用
	 * 
	 * @param str
	 *            字串
	 * @param start
	 *            擷取字串開始點
	 * @param end
	 *            擷取字串結束點
	 * @return true: 可用, false: 不可用
	 */
	public static boolean checkSubStr(String str, int start, int end) {
		if (start < 0 || start > end || end > str.length()) {
			return false;
		} else {
			return true;
		}
	}

	public static Page<Map<String, Object>> setPageMap(
			List<Map<String, Object>> rows, ISearch search) {
		return LMSUtil.setPageMap(rows, null, search);
	}

	/**
	 * 算該分頁有幾筆資料 跟換頁顯示資料
	 * 
	 * @param rows
	 * @param columnAddDataList
	 *            須新增欄位使用
	 * @param search
	 * @return
	 */
	public static Page<Map<String, Object>> setPageMap(
			List<Map<String, Object>> rows,
			List<Map<String, Object>> columnAddDataList, ISearch search) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		int start = search.getFirstResult();
		int pagNumber = search.getMaxResults();
		int end = start + pagNumber > rows.size() ? start
				+ (rows.size() - start) : start + pagNumber;
		for (int b = start; b < end; b++) {
			Map<String, Object> map = rows.get(b);
			if (columnAddDataList != null) {
				Map<String, Object> columnAddDataMap = columnAddDataList.get(b);
				for (String key : columnAddDataMap.keySet()) {
					map.put(key, columnAddDataMap.get(key));
				}
			}
			list.add(map);
		}
		return new Page<Map<String, Object>>(list, rows.size(),
				search.getMaxResults(), search.getFirstResult());
	}

	/**
	 * 判斷是否為 國內個金案件
	 * 
	 * @param l120m01a
	 *            簽報書主檔
	 * @return true | false
	 */
	public static boolean isClsCase(L120M01A l120m01a) {
		if (URLConstant.國內個金_簽報書.equals(l120m01a.getDocURL())) {
			return true;
		}
		return false;
	}

	/**
	 * 判斷是否為海外案件
	 * 
	 * @param l120m01a
	 * @return
	 */
	public static boolean isTypCd5Case(L120M01A l120m01a) {
		if (TypCdEnum.海外.getCode().equals(l120m01a.getTypCd())) {
			return true;
		}
		return false;
	}

	/**
	 * 加總BigDecimal
	 * 
	 * @param totals
	 * @return
	 */
	public static BigDecimal addTotal(BigDecimal... totals) {
		BigDecimal num = BigDecimal.ZERO;
		for (BigDecimal total : totals) {
			if (total == null) {
				total = BigDecimal.ZERO;
			}
			num = num.add(total);
		}
		return num;
	}

	/**
	 * 加總BigDecimal
	 * 
	 * @param totals
	 * @return
	 */
	public static BigDecimal subTotal(BigDecimal total, BigDecimal... nums) {
		for (BigDecimal num : nums) {
			if (num == null) {
				num = BigDecimal.ZERO;
			}
			total = total.subtract(num);
		}
		return total;
	}

	/**
	 * 檢查是否為OBU 第三碼為Z即為OBU
	 * 
	 * @param custId
	 *            客戶統編
	 * @return
	 */
	public static boolean isObuId(String custId) {
		if ("Z".equals(custId.substring(2, 3))) {
			return true;
		}
		return false;
	}

	/**
	 * 取得動用期限對應
	 * 
	 * @param useDeadline
	 *            useDeadline
	 * @param desp1
	 *            desp1
	 * @return 取得useDeadline對應
	 */
	public static String getUseDeadline(String useDeadline, String desp1,
			Properties prop) {
		String str = null;

		if ("0".equals(useDeadline) || "1".equals(useDeadline)
				|| "5".equals(useDeadline)) {
			str = desp1;
		} else if ("2".equals(useDeadline)) {
			if (Util.equals(LMSUtil.getLocale(), "en")) {
				str = desp1 + " " + prop.getProperty("L140M01as02.002") + " "
						+ prop.getProperty("L140M01as02.001");
			} else {
				str = prop.getProperty("L140M01as02.001") + " " + desp1 + " "
						+ prop.getProperty("L140M01as02.002");
			}

		} else if ("3".equals(useDeadline)) {
			if (Util.equals(LMSUtil.getLocale(), "en")) {
				str = desp1 + " " + prop.getProperty("L140M01as02.002") + " "
						+ prop.getProperty("L140M01as02.003");
			} else {
				str = prop.getProperty("L140M01as02.003") + " " + desp1 + " "
						+ prop.getProperty("L140M01as02.002");
			}

		} else if ("4".equals(useDeadline)) {
			if (Util.equals(LMSUtil.getLocale(), "en")) {
				str = desp1 + " " + prop.getProperty("L140M01as02.002") + " "
						+ prop.getProperty("L140M01as02.004");
			} else {
				str = prop.getProperty("L140M01as02.004") + " " + desp1 + " "
						+ prop.getProperty("L140M01as02.002");
			}
		} else if ("6".equals(useDeadline)) {
			// 自核准日起MM個月內首次動撥
			str = MessageFormat.format(prop.getProperty("L140M01as02.007"),
					desp1);
		} else if ("7".equals(useDeadline)) {
			// 自簽約日起MM個月內首次動撥
			str = MessageFormat.format(prop.getProperty("L140M01as02.008"),
					desp1);
		} else if ("8".equals(useDeadline)) {
			// J-110-0320 為符營業單位陳報團貸實務作業，於ELOAN系統新增團貸動用期限選項
			// 自核准日起~YYYY-MM-DD
			str = prop.getProperty("L140M01as02.001") + desp1;
		} else {
			str = "";
		}
		return str;
	}

	/**
	 * 取得property對應
	 * 
	 * @param proPerty
	 *            property
	 * @return property對應
	 */
	public static String getProPerty(String proPerty, Properties prop) {
		StringBuffer str = new StringBuffer();
		String[] temp = proPerty.split("\\|");
		for (String perty : temp) {
			if ("1".equals(perty)) {
				str.append(prop.getProperty("L140M01a.type1")).append("、");
			} else if ("2".equals(perty)) {
				str.append(prop.getProperty("L140M01a.type2")).append("、");
			} else if ("3".equals(perty)) {
				str.append(prop.getProperty("L140M01a.type3")).append("、");
			} else if ("4".equals(perty)) {
				str.append(prop.getProperty("L140M01a.type4")).append("、");
			} else if ("5".equals(perty)) {
				str.append(prop.getProperty("L140M01a.type5")).append("、");
			} else if ("6".equals(perty)) {
				str.append(prop.getProperty("L140M01a.type6")).append("、");
			} else if ("7".equals(perty)) {
				str.append(prop.getProperty("L140M01a.type7")).append("、");
			} else if ("8".equals(perty)) {
				str.append(prop.getProperty("L140M01a.type8")).append("、");
			} else if ("9".equals(perty)) {
				str.append(prop.getProperty("L140M01a.type9")).append("、");
			} else if ("10".equals(perty)) {
				str.append(prop.getProperty("L140M01a.type10")).append("、");
			} else if ("11".equals(perty)) {
				str.append(prop.getProperty("L140M01a.type11")).append("、");
			} else if ("12".equals(perty)) {
				str.append(prop.getProperty("L140M01a.type12")).append("、");
			} else if ("13".equals(perty)) {
				str.append(prop.getProperty("L140M01a.type13")).append("、");
			}
		}
		if (str.length() == 0) {
			str.append("、");
		}

		return str.toString().substring(0, str.length() - 1);
	}

	/**
	 * 檢查是否為自然人
	 * <p>
	 * 台灣統編和 和開頭兩碼是英文後八碼為數字為自然人
	 * 
	 * @param id
	 *            統編
	 * @return
	 */
	public static boolean check2(String id) {
		if (id.matches("^[a-zA-Z](1|2)\\d{8}$")
				|| id.matches("^[a-zA-Z](8|9)\\d{8}$")
				|| id.matches("^[a-zA-Z]{2}\\d{8}$")) {
			return true;
		}

		// J-111-0125 因應obu可以承做外國自然人授信業務(法規未予限制),
		// 開放eloan(包含個人授信/徵信/信評等系統)相關對外國自然人統編的限制
		if (checkCustId_foreignerPersonalTIN(id)) {
			return true;
		}
		return false;
	}

	/**
	 * 「稅籍編號」：以「西元出生年月日八碼」加「英文姓名前兩字母兩碼」的組合
	 * 
	 * @param id
	 * @return
	 */
	public static boolean checkCustId_foreignerPersonalTIN(String id) {
		if (id.matches("^\\d{8}[A-Z]{2}$")) {
			return true;
		}
		return false;
	}

	/**
	 * 檢查是否為銀行法 或44 45 需檢查項目
	 * 
	 * @param l120s01d
	 *            企金銀行法／金控法利害關係人檔
	 * @return true | false
	 * 
	 *         調整 J-105-0250 2016-12-01
	 *         請於A-Loan與e-Loan系統增加是否為利害關係人之檢查或自動更新功能如下修改內容。
	 *         1.對個人戶短擔（與短放）續約案，請於A-Loan系統交易代號L56A及L503增加可連動檢查簽案時之
	 *         利害關係人資料是否需更新或自動更新之功能。
	 *         2.另對個金授信案件(除下列第4項所述之無擔保放款案件外)，請增加利害關係人檢查功能如下：
	 *         （1）請於e-Loan簽案（含新做、續約或增、減額及變更條件等）時，若個金授信戶為利害關係人，
	 *         則授信科目不得為無擔保放款科目（消費者放款科目除外）。
	 *         （2）請於e-Loan動審表增加檢核，若個金授信戶簽案時為利害關係人，則不得有無擔保放款科目 (消費者放款科目除外）。
	 *         3.對有共同借款人之案件，只要其中1人為利害關係人，亦比照上述1及2辦理。
	 *         （所稱利害關係人指銀行法32、33條及金控法44條利害關係人）
	 *         4.對授信戶簽訂之無擔保授信契約日期早於成為本行利害關係人之個金案件，增加得適用「無擔保放款」
	 *         科目之註記選項，且額度可為「無擔保放款」科目。
	 */
	public static boolean isUnsecureFlag(L120S01D l120s01d) {
		if (l120s01d == null) {
			return false;
		}

		/*
		 * J-105-0250 return ("1".equals(Util.trim(l120s01d.getMbRlt())) ||
		 * "1".equals(Util.trim(l120s01d.getMhRlt44())) || "1"
		 * .equals(Util.trim(l120s01d.getMhRlt45())));
		 */

		// 取消檢核金控法45條
		return ("1".equals(Util.trim(l120s01d.getMbRlt())) || "1".equals(Util
				.trim(l120s01d.getMhRlt44())));
	}

	/**
	 * 截出 三碼分行代碼
	 * 
	 * @param slBank
	 *            行庫代號
	 * @param slBranch
	 *            分行代號
	 * @return
	 */
	public static String trimSlBranch(String slBank, String slBranch) {
		String result = Util.trim(slBranch);
		if (Util.isNotEmpty(slBank) && slBank.length() == 3
				&& slBranch.length() == 7) {
			result = Util.getLeftStr(slBranch.replace(slBank, ""), 3);
		}
		return result;
	}

	/**
	 * 將null欄位轉換成N.A.
	 * 
	 * @param col
	 *            任意欄位
	 * @return
	 */
	public static String nullToNa(Object col) {
		if (col == null) {
			return "N.A.";
		} else {
			return Util.nullToSpace(col);
		}
	}

	/**
	 * 維護央行購屋/空地/建屋貸款註記資訊 組字
	 * 
	 * @param l140m01m
	 *            model
	 * @param codeMap
	 *            代碼對應表 L140M01M_cbcCase 維護央行項目 L140M01M_plusReason
	 *            L140M01M_cbcCase =4 的原因選項 city 縣市名稱 area 鄉鎮市區名稱
	 * @return
	 */
	public static String L140M01MStr(L140M01M l140m01m,
			Map<String, CapAjaxFormResult> codeMap) {
		// L140M01M_cbcCase 維護央行項目
		// L140M01M_plusReason L140M01M_cbcCase =4 的原因選項
		StringBuffer temp = new StringBuffer();
		if (l140m01m != null) {
			Properties prop = MessageBundleScriptCreator
					.getComponentResource(LMSL140M01MPanel.class);

			String isLimitCustStr = "";
			String isHighHouseStr = "";
			String isLimitCust = Util.trim(l140m01m.getIsLimitCust());
			String isHighHouse = Util.trim(l140m01m.getIsHighHouse());

			String cbcCase = Util.trim(l140m01m.getCbcCase());
			String br = "<br/>";
			if (Util.isNotEmpty(isLimitCust)) {
				// L140M01M.isLimitCustY=本案是否為受限戶 ■本案為受限戶 □本案非受限戶
				// L140M01M.isLimitCustN=本案是否為受限戶 □本案為受限戶 ■本案非受限戶
				isLimitCustStr = br
						+ prop.getProperty("L140M01M.isLimitCust" + isLimitCust);
			}
			if (Util.isNotEmpty(isHighHouse)) {
				// L140M01M.isHighHouseY=本案購是否為高價住宅 ■本案為高價住宅 □本案非高價住宅
				// L140M01M.isHighHouseN=本案購是否為高價住宅 □本案為高價住宅 ■本案非高價住宅
				isHighHouseStr = br
						+ prop.getProperty("L140M01M.isHighHouse" + isHighHouse);
			}
			String cbcCaseStr = Util.trim(codeMap.get("L140M01M_cbcCase").get(
					cbcCase));
			temp.append(cbcCaseStr);

			if (L140M01MCbcCase.自然人.equals(cbcCase)
					&& codeMap.get("L140M01M_peopleMortgageDetail") != null) {
				Object str = codeMap.get("L140M01M_peopleMortgageDetail").get(
						l140m01m.getRealEstateLoanLimitReason());
				temp.append(str == null ? "" : br + str);
			}

			if (L140M01MCbcCase.公司法人.equals(cbcCase)
					&& codeMap.get("L140M01M_companyMortgageDetail") != null) {
				Object str = codeMap.get("L140M01M_companyMortgageDetail").get(
						l140m01m.getRealEstateLoanLimitReason());
				temp.append(str == null ? "" : br + str);
			}

			temp.append(isLimitCustStr);
			temp.append(isHighHouseStr);
			// 本案亦屬自然人第3戶(含)以上購置房貸。
			if (Arrays.asList(RealEstateLoanUtil.specificVersion).contains(
					l140m01m.getVersion())
					&& "A".equals(l140m01m.getRealEstateLoanLimitReason())
					&& "Y".equals(l140m01m.getIs3rdHignHouse())) {
				temp.append("，" + prop.getProperty("L140M01M.3rdHighHouse"));
			}

			if (UtilConstants.L140M01MCbcCase.自然人.equals(cbcCase)) {
				if (Util.isNotEmpty(Util.trim(l140m01m.getCustYN()))) {
					temp.append(br);
					temp.append(prop.getProperty("L140M01M.custYN"));
					temp.append("：");
					temp.append(Util.nullToSpace(prop.getProperty("have."
							+ l140m01m.getCustYN())));
				}

				temp.append(br);
				temp.append(prop.getProperty("L140M01M.buildYN_cbcCase1"));
				temp.append("：");
				temp.append(Util.nullToSpace(prop.getProperty("YesNo."
						+ l140m01m.getBuildYN())));
				temp.append(br);
				temp.append(prop.getProperty("L140M01M.004"));
				temp.append("：");
				temp.append(codeMap.get("L140M01M_city").get("city"));
				temp.append(codeMap.get("L140M01M_city").get("area"));
				temp.append(codeMap.get("L140M01M_city").get("site3"));
				temp.append(br);
				temp.append(prop.getProperty("L140M01M.timeVal"));// 時價
				temp.append("：");
				temp.append(NumConverter.addComma(Util.trim(l140m01m
						.getTimeVal())));
				temp.append(prop.getProperty("L140M01M.NT"));
				temp.append(br);
				temp.append(prop.getProperty("L140M01M.appAmt"));
				temp.append("：");
				temp.append(NumConverter.addComma(Util.trim(l140m01m
						.getAppAmt())));
				temp.append(prop.getProperty("L140M01M.NT"));
				temp.append(br);
				/*
				 * 鑑價 temp.append(prop.getProperty("L140M01M.nowAMT"));
				 * temp.append("：");
				 * temp.append(NumConverter.addComma(Util.trim(l140m01m
				 * .getNowAMT())));
				 * temp.append(prop.getProperty("L140M01M.NT"));
				 * temp.append(br);
				 */
				temp.append(prop.getProperty("L140M01M.valueAMT"));
				temp.append("：");
				temp.append(NumConverter.addComma(Util.trim(l140m01m
						.getValueAMT())));
				temp.append(prop.getProperty("L140M01M.NT"));
				temp.append(br);
				// L140M01M.008=擔保品總貸款成數(%)
				temp.append(prop.getProperty("L140M01M.008"));
				// L140M01M.003=(至小數2位)(分母為擔保品估值，最高為估值之六成；高價住宅貸款最高為買價或估值金額較低者之五成)
				temp.append(prop.getProperty("L140M01M.003"));
				temp.append("：");
				temp.append(Util.trim(l140m01m.getPayPercent()));
				temp.append(br);
				_l140m01m_CommonYN_ShareCollYN(temp, prop, br, l140m01m);

			} else if (UtilConstants.L140M01MCbcCase.公司法人.equals(cbcCase)) {
				temp.append(br);
				temp.append(prop.getProperty("L140M01M.buildYN_cbcCase1"));
				temp.append("：");
				temp.append(Util.nullToSpace(prop.getProperty("YesNo."
						+ l140m01m.getBuildYN())));
				temp.append(br);
				temp.append(prop.getProperty("L140M01M.004"));
				temp.append("：");
				temp.append(Util.trim(codeMap.get("L140M01M_city").get("city")));
				temp.append(Util.trim(codeMap.get("L140M01M_city").get("area")));
				temp.append(Util
						.trim(codeMap.get("L140M01M_city").get("site3")));
				temp.append(br);
				temp.append(prop.getProperty("L140M01M.timeVal"));// 時價
				temp.append("：");
				temp.append(NumConverter.addComma(Util.trim(l140m01m
						.getTimeVal())));
				temp.append(prop.getProperty("L140M01M.NT"));
				temp.append(br);
				temp.append(prop.getProperty("L140M01M.appAmt"));
				temp.append("：");
				temp.append(NumConverter.addComma(Util.trim(l140m01m
						.getAppAmt())));
				temp.append(prop.getProperty("L140M01M.NT"));
				temp.append(br);
				/*
				 * 鑑價 temp.append(prop.getProperty("L140M01M.nowAMT"));
				 * temp.append("：");
				 * temp.append(NumConverter.addComma(Util.trim(l140m01m
				 * .getNowAMT())));
				 * temp.append(prop.getProperty("L140M01M.NT"));
				 * temp.append(br);
				 */
				temp.append(prop.getProperty("L140M01M.valueAMT"));
				temp.append("：");
				temp.append(NumConverter.addComma(Util.trim(l140m01m
						.getValueAMT())));
				temp.append(prop.getProperty("L140M01M.NT"));
				temp.append(br);
				// L140M01M.009=擔保品整筆貸款之貸款成數(%)
				temp.append(prop.getProperty("L140M01M.009"));
				// L140M01M.003=(至小數2位)(分母為擔保品估值，最高為估值之六成；高價住宅貸款最高為買價或估值金額較低者之五成)
				temp.append(prop.getProperty("L140M01M.003"));
				temp.append("：");
				temp.append(Util.trim(l140m01m.getPayPercent()));
				temp.append(br);
				_l140m01m_CommonYN_ShareCollYN(temp, prop, br, l140m01m);

			} else if (UtilConstants.L140M01MCbcCase.土地抵押貸款.equals(cbcCase)) {
				temp.append(br);
				temp.append(prop.getProperty("L140M01M.houseYN"));
				temp.append("：");
				temp.append(Util.nullToSpace(prop.getProperty("have1."
						+ l140m01m.getHouseYN())));
				temp.append(br);
				temp.append(prop.getProperty("L140M01M.houseType"));
				temp.append("：");
				temp.append(Util.nullToSpace(prop
						.getProperty("L140M01M.houseType"
								+ l140m01m.getHouseType())));
				temp.append(br);
				temp.append(prop.getProperty("L140M01M.purposeType"));
				temp.append("：");
				temp.append(Util.nullToSpace(prop
						.getProperty("L140M01M.purposeType"
								+ l140m01m.getPurposeType())));
				temp.append(br);
				temp.append(prop.getProperty("L140M01M.cmsType"));
				temp.append("：");
				temp.append(Util.nullToSpace(prop
						.getProperty("L140M01M.cmsType" + l140m01m.getCmsType())));
				temp.append(br);
				temp.append(prop.getProperty("L140M01M.004"));
				temp.append("：");
				temp.append(codeMap.get("L140M01M_city").get("city"));
				temp.append(codeMap.get("L140M01M_city").get("area"));
				temp.append(codeMap.get("L140M01M_city").get("site3"));
				temp.append(br);
				temp.append(prop.getProperty("L140M01M.appAmt"));
				temp.append("：");
				temp.append(NumConverter.addComma(Util.trim(l140m01m
						.getAppAmt())));
				temp.append(prop.getProperty("L140M01M.NT"));
				temp.append(br);
				temp.append(prop.getProperty("L140M01M.valueAMT"));
				temp.append("：");
				temp.append(NumConverter.addComma(Util.trim(l140m01m
						.getValueAMT())));
				temp.append(prop.getProperty("L140M01M.NT"));
				temp.append(br);
				// L140M01M.009=擔保品整筆貸款之貸款成數(%)
				temp.append(prop.getProperty("L140M01M.009"));
				// L140M01M.011=(至小數2位)(分母為擔保品估值)
				temp.append(prop.getProperty("L140M01M.011"));
				temp.append("：");
				temp.append(Util.trim(l140m01m.getPayPercent()));
				temp.append(br);
				temp.append(prop.getProperty("L140M01M.keepYN"));
				temp.append("：");
				temp.append(Util.nullToSpace(prop.getProperty("have1."
						+ l140m01m.getKeepYN())));
				temp.append(br);
				// 最新預計動工日
				if (l140m01m.getCbControlLstDate() != null) {
					temp.append(prop.getProperty("L140M01M.cbControlLstDate"));
					temp.append("：");
					temp.append(CapDate.parseSQLDate(l140m01m
							.getCbControlLstDate()));
					temp.append(br);
				}
				_l140m01m_CommonYN_ShareCollYN(temp, prop, br, l140m01m);

			} else if (UtilConstants.L140M01MCbcCase.非央行自然人.equals(cbcCase)) {
				// L140M01M.007=原因
				temp.append("，");
				temp.append(prop.getProperty("L140M01M.007"));
				temp.append("：");
				String plusReason = Util.trim(l140m01m.getPlusReason());
				String plusReasonStr = Util.trim(codeMap.get(
						"L140M01M_plusReason").get(plusReason));
				if ("5".equals(plusReason)) {
					plusReasonStr = Util.trim(l140m01m.getPlusReasonMeMo());
					if (Util.isEmpty(plusReasonStr)) {
						// noHave=無
						plusReasonStr = prop.getProperty("noHave");

					}
				}
				temp.append(plusReasonStr);
				temp.append("。");
				temp.append(br);

				if (is_cls_prod_67(l140m01m)) {
					temp.append(prop.getProperty("L140M01M.004"));
					temp.append("：");
					temp.append(codeMap.get("L140M01M_city").get("city"));
					temp.append(codeMap.get("L140M01M_city").get("area"));
					temp.append(codeMap.get("L140M01M_city").get("site3"));
					temp.append(br);
					// =========
					temp.append(prop.getProperty("L140M01M.appAmt"));
					temp.append("：");
					temp.append(NumConverter.addComma(Util.trim(l140m01m
							.getAppAmt())));
					temp.append(prop.getProperty("L140M01M.NT"));
					temp.append(br);
					// =========
					temp.append(prop.getProperty("L140M01M.loanAmt"));
					temp.append("：");
					temp.append(NumConverter.addComma(Util.trim(l140m01m
							.getLoanAmt())));
					temp.append(prop.getProperty("L140M01M.NT"));
					temp.append(br);
					// =========
					temp.append(prop.getProperty("L140M01M.house_age"));
					temp.append("：");
					temp.append(pretty_numStr(l140m01m.getHouse_age()));
					temp.append(prop.getProperty("L140M01M.house_age.unit"));
					temp.append(br);
					// =========
				}

			}
			temp.append(landBuildStr(l140m01m, codeMap));
		}
		return temp.toString();
	}

	private static void _l140m01m_CommonYN_ShareCollYN(StringBuffer temp,
			Properties prop, String br, L140M01M l140m01m) {
		temp.append(prop.getProperty("L140M01M.commonYN"));
		temp.append("：");
		temp.append(Util.nullToSpace(prop.getProperty("have1."
				+ l140m01m.getCommonYN())));
		temp.append(br);
		temp.append(prop.getProperty("L140M01M.shareCollYN"));
		temp.append("：");
		temp.append(Util.nullToSpace(prop.getProperty("have1."
				+ l140m01m.getShareCollYN())));
		temp.append(br);
		if (Util.equals(l140m01m.getCommonYN(), "Y")
				|| Util.equals(l140m01m.getShareCollYN(), "Y")) {
			temp.append(prop.getProperty("L140M01M.shareCollAmt"));
			temp.append("：");
			temp.append(NumConverter.addComma(Util.trim(l140m01m
					.getShareCollAmt())));
			temp.append(prop.getProperty("L140M01M.NT"));
			temp.append(br);
		}
	}

	// 組土建融預約額度的字句
	public static String landBuildStr(L140M01M l140m01m,
			Map<String, CapAjaxFormResult> codeMap) {
		StringBuffer temp = new StringBuffer();
		if (l140m01m != null) {
			Properties prop = MessageBundleScriptCreator
					.getComponentResource(LMSL140M01MPanel.class);
			String isLandBuildYNStr = "";
			String isLandBuildYN = Util.trim(l140m01m.getLandBuildYN());
			String br = "<br/>";
			if (Util.isNotEmpty(isLandBuildYN)) {
				// L140M01M.landBuildYNY=本案是否土建融案： ■本案為土建融案 □本案非土建融案
				// L140M01M.landBuildYNN=本案是否土建融案： □本案為土建融案 ■本案非土建融案
				isLandBuildYNStr = br
						+ prop.getProperty("L140M01M.landBuildYN"
								+ isLandBuildYN);
			}
			temp.append(isLandBuildYNStr);
			temp.append(br);
			if ("Y".equals(isLandBuildYN)) {
				temp.append(prop.getProperty("L140M01M.prodClass")); // 產品種類
				temp.append("：");
				temp.append(Util.trim(l140m01m.getProdClass()));
				temp.append(br);
				temp.append(prop.getProperty("L140M01M.landArea")); // 土地面積
				temp.append("：");
				temp.append(Util.trim(l140m01m.getAreaLand()));
				temp.append(prop.getProperty("L140M01M.AreaP")); // 平方公尺
				temp.append(br);
				temp.append(prop.getProperty("L140M01M.waitMonth")); // 預計撥款至動工期間月數
				temp.append("：");
				temp.append(Util.trim(l140m01m.getWaitMonth()));
				temp.append(br);
				temp.append(prop.getProperty("L140M01M.buildDate")); // 預計取得建照日期
				temp.append("：");
				temp.append(CapDate.formatDate(l140m01m.getBuildDate(),
						"yyyy-MM-dd"));
				temp.append(br);
				temp.append(prop.getProperty("L140M01M.landType")); // 土地使用分區
				temp.append("：");
				temp.append(Util.nullToSpace(prop
						.getProperty("L140M01M.landType"
								+ l140m01m.getLandType())));
				temp.append(br);
				temp.append(prop.getProperty("L140M01M.locationCd"));
				temp.append("：");
				temp.append(codeMap.get("L140M01M_landbuidcity").get(
						"landbuidcity"));
				temp.append(codeMap.get("L140M01M_landbuidcity").get(
						"landbuidarea"));
				temp.append(codeMap.get("L140M01M_landbuidcity").get("site3e"));
				temp.append(br);
			}
		}
		return temp.toString();
	}

	/**
	 * 大陸地區授信業務控管註記　組字
	 * 
	 * @param l140m01q
	 * @param codeMap
	 * @return
	 */
	public static String L140M01QStr(L140M01Q l140m01q,
			Map<String, CapAjaxFormResult> codeMap) {
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMSL140M01MPanel.class);
		StringBuffer msg = new StringBuffer();
		String cnLoanFg = l140m01q.getCnLoanFg();
		String directFg = l140m01q.getDirectFg();
		// //J-103-0314-001 Web e-Loan授信管理系統修改額度明細檢核附表有關大陸地區授信業務控管註記。
		String cnBusKind = l140m01q.getCnBusKind();
		String sTradeFg = l140m01q.getsTradeFg();

		BigDecimal guar1Rate = l140m01q.getGuar1Rate();
		BigDecimal guar2Rate = l140m01q.getGuar2Rate();
		BigDecimal guar3Rate = l140m01q.getGuar3Rate();
		BigDecimal coll1Rate = l140m01q.getColl1Rate();
		BigDecimal coll2Rate = l140m01q.getColl2Rate();
		BigDecimal coll3Rate = l140m01q.getColl3Rate();
		BigDecimal coll4Rate = l140m01q.getColl4Rate();
		BigDecimal coll5Rate = l140m01q.getColl5Rate();

		msg.append(prop.getProperty("L140M01Q.cnLoanFg" + cnLoanFg));
		if ("Y".equals(cnLoanFg)) {
			msg.append("<br>");
			String iGolFlag = l140m01q.getiGolFlag();
			msg.append(prop.getProperty("L140M01Q.iGolFlag" + iGolFlag));
			msg.append("<br>");

			msg.append(prop.getProperty("L140M01Q.directFg"));
			msg.append(Util
					.trim(codeMap.get("L140M01Q_directFg").get(directFg)));
			msg.append("<br>");

			// //J-103-0314-001 Web e-Loan授信管理系統修改額度明細檢核附表有關大陸地區授信業務控管註記。
			if (Util.equals(directFg, "12") || Util.equals(directFg, "14")) {
				msg.append(prop.getProperty("L140M01Q.cnBusKind"));
				msg.append(Util.trim(codeMap.get("L140M01Q_cnBusKind").get(
						cnBusKind)));
				msg.append("<br>");
			}

			msg.append(prop.getProperty("L140M01Q.sTradeFg" + sTradeFg));
			if ("N".equals(sTradeFg)) {
				msg.append("<br>");

				BigDecimal total = guar1Rate.add(guar2Rate).add(guar3Rate)
						.add(coll1Rate).add(coll2Rate).add(coll3Rate)
						.add(coll4Rate).add(coll5Rate);

				if (total.intValue() == 100) {
					msg.append(prop.getProperty("L140M01Q.rickTrFgY"));
					msg.append("<br>");
					msg.append(prop.getProperty("L140M01Q.guaranty")).append(
							"：");
					msg.append("(1)").append(guar1Rate).append("%").append("，");
					msg.append("(2)").append(guar2Rate).append("%").append("，");
					msg.append("(3)").append(guar3Rate).append("%").append("。");
					msg.append("<br>");
					msg.append(prop.getProperty("L140M01Q.collateral")).append(
							"：");
					msg.append("(1)").append(coll1Rate).append("%").append("，");
					msg.append("(2)").append(coll2Rate).append("%").append("，");
					msg.append("(3)").append(coll3Rate).append("%").append("，");
					msg.append("(4)").append(coll4Rate).append("%").append("，");
					msg.append("(5)").append(coll5Rate).append("%").append("。");
				} else {
					msg.append(prop.getProperty("L140M01Q.rickTrFgN"));
				}
			}
		}

		// 調整前的組字，顯示差異部份
		StringBuffer beforeMsg = new StringBuffer();

		String bcnLoanFg = Util.trim(l140m01q.getBcnLoanFg());
		if (!"".equals(bcnLoanFg)) {
			if (!bcnLoanFg.equals(cnLoanFg)) {
				beforeMsg.append(prop.getProperty("L140M01Q.cnLoanFg"
						+ bcnLoanFg));
				beforeMsg.append("<br>");
			}
			if ("Y".equals(bcnLoanFg)) {

				String biGolFlag = Util.trim(l140m01q.getBiGolFlag());
				if (!"".equals(biGolFlag)) {
					beforeMsg.append(prop.getProperty("L140M01Q.iGolFlag"
							+ biGolFlag));
					beforeMsg.append("<br>");
				}

				String bdirectFg = Util.trim(l140m01q.getBdirectFg());
				if (!"".equals(bdirectFg) && !bdirectFg.equals(directFg)) {
					beforeMsg.append(prop.getProperty("L140M01Q.directFg"));
					beforeMsg.append(Util.trim(codeMap.get("L140M01Q_directFg")
							.get(bdirectFg)));
					beforeMsg.append("<br>");
				}

				// J-103-0314-001 Web e-Loan授信管理系統修改額度明細檢核附表有關大陸地區授信業務控管註記。
				String tCnBusKind = Util.trim(l140m01q.getCnBusKind());
				String tBcnBusKind = Util.trim(l140m01q.getBcnBusKind());

				if (!(Util.equals(directFg, "12") || Util
						.equals(directFg, "14"))) {
					tCnBusKind = "";
				}
				if (!(Util.equals(bdirectFg, "12") || Util.equals(bdirectFg,
						"14"))) {
					tBcnBusKind = "";
				}

				if (!"".equals(tBcnBusKind) && !tBcnBusKind.equals(tCnBusKind)) {
					beforeMsg.append(prop.getProperty("L140M01Q.cnBusKind"));
					beforeMsg.append(Util.trim(codeMap
							.get("L140M01Q_cnBusKind").get(tBcnBusKind)));
					beforeMsg.append("<br>");
				}

				String bsTradeFg = Util.trim(l140m01q.getBsTradeFg());
				if (!"".equals(bsTradeFg)) {
					if (!bsTradeFg.equals(sTradeFg)) {
						beforeMsg.append(prop.getProperty("L140M01Q.sTradeFg"
								+ bsTradeFg));
						beforeMsg.append("</br>");
						// 兩岸間短期貿易融資額度註記
					}

					if ("N".equals(bsTradeFg)) {

						BigDecimal bguar1Rate = l140m01q.getBguar1Rate();
						BigDecimal bguar2Rate = l140m01q.getBguar2Rate();
						BigDecimal bguar3Rate = l140m01q.getBguar3Rate();
						BigDecimal bcoll1Rate = l140m01q.getBcoll1Rate();
						BigDecimal bcoll2Rate = l140m01q.getBcoll2Rate();
						BigDecimal bcoll3Rate = l140m01q.getBcoll3Rate();
						BigDecimal bcoll4Rate = l140m01q.getBcoll4Rate();
						BigDecimal bcoll5Rate = l140m01q.getBcoll5Rate();

						BigDecimal btotal = bguar1Rate.add(bguar2Rate)
								.add(bguar3Rate).add(bcoll1Rate)
								.add(bcoll2Rate).add(bcoll3Rate)
								.add(bcoll4Rate).add(bcoll5Rate);

						if (btotal.intValue() == 100) {

							if (!bguar1Rate.equals(guar1Rate)
									|| !bguar2Rate.equals(guar2Rate)
									|| !bguar3Rate.equals(guar3Rate)
									|| !bcoll1Rate.equals(coll1Rate)
									|| !bcoll2Rate.equals(coll2Rate)
									|| !bcoll3Rate.equals(coll3Rate)
									|| !bcoll4Rate.equals(coll4Rate)
									|| !bcoll5Rate.equals(coll5Rate)) {
								beforeMsg.append(prop
										.getProperty("L140M01Q.rickTrFgY"));
								beforeMsg.append("<br>");
								beforeMsg.append(
										prop.getProperty("L140M01Q.guaranty"))
										.append("：");
								beforeMsg.append("(1)").append(bguar1Rate)
										.append("%").append("，");
								beforeMsg.append("(2)").append(bguar2Rate)
										.append("%").append("，");
								beforeMsg.append("(3)").append(bguar3Rate)
										.append("%").append("。");
								beforeMsg.append("<br>");
								beforeMsg
										.append(prop
												.getProperty("L140M01Q.collateral"))
										.append("：");
								beforeMsg.append("(1)").append(bcoll1Rate)
										.append("%").append("，");
								beforeMsg.append("(2)").append(bcoll2Rate)
										.append("%").append("，");
								beforeMsg.append("(3)").append(bcoll3Rate)
										.append("%").append("，");
								beforeMsg.append("(4)").append(bcoll4Rate)
										.append("%").append("，");
								beforeMsg.append("(5)").append(bcoll5Rate)
										.append("%").append("。");
							}

						} else {
							beforeMsg.append(prop
									.getProperty("L140M01Q.rickTrFgN"));
						}
					}
				}

			}
		}
		// 如果變更前後不一樣
		if (beforeMsg.length() > 0) {
			msg.append("<br><br>");
			msg.append(prop.getProperty("L140M01Q.adjustBefore"));
			msg.append("<br>");
			msg.append(beforeMsg);
		}
		return msg.toString();
	}

	/**
	 * 轉換借款人關係
	 * 
	 * @param custRlt
	 *            關係值 1X OR X.......
	 * @param codeMap
	 *            codetype Relation_type1、elation_type2 、 Relation_type31、
	 *            Relation_type32
	 * @return
	 */
	public static String changeCustRlt(String custRlt,
			Map<String, CapAjaxFormResult> codeMap) {
		StringBuffer temp = new StringBuffer();
		if (Util.isNotEmpty(Util.trim(custRlt))) {
			temp.append(custRlt);
			temp.append("-");
			int rkindm = custRlt.indexOf("X");
			switch (rkindm) {
			case 1:
				temp.append(codeMap.get(UtilConstants.CodeTypeItem.企業關係).get(
						custRlt));
				break;
			case 0:
				temp.append(codeMap.get(UtilConstants.CodeTypeItem.親屬關係).get(
						custRlt));
				break;
			case -1:
				temp.append(codeMap.get(UtilConstants.CodeTypeItem.綜合關係_企業)
						.get(custRlt.substring(0, 1)));
				temp.append("-");
				temp.append(codeMap.get(UtilConstants.CodeTypeItem.綜合關係_親屬)
						.get(custRlt.substring(1, 2)));
				break;
			}

		}
		return temp.toString();
	}

	/**
	 * 取得授信科目 和 科子目 限額
	 * 
	 * @param l140m01a
	 *            額度明細表
	 * @return <授信科目,幣別+金額>
	 */
	public static HashMap<String, String> getItemList(L140M01A l140m01a) {
		HashMap<String, String> itemList = new HashMap<String, String>();
		if (l140m01a != null) {
			// 授信科目
			if (l140m01a.getL140m01c() != null) {

				// J-105-0203-001 Web e-Loan未有限額條件之科目改以同總核准額度上傳a-Loan
				String[] exceptSubjectArr = new String[] { "102", "202", "104",
						"204", "404" };

				for (L140M01C l140m01c : l140m01a.getL140m01c()) {

					// BGN J-105-0203-001 Web e-Loan未有限額條件之科目改以同總核准額度上傳a-Loan
					String key = Util.getLeftStr(l140m01c.getLoanTP(), 3);

					// 透支科目還是要帶現請額度 102 202 104 204

					// 預設為同總核准額度(額度為0、幣別為空白)
					BigDecimal currentApplyAmt = BigDecimal.ZERO;
					String currentApplyCurr = l140m01a.getCurrentApplyCurr();

					if (Util.isEmpty(l140m01a.getCurrentApplyAmt())
							|| Util.isEmpty(l140m01a.getCurrentApplyCurr())) {
						continue;
					}

					if (Arrays.asList(exceptSubjectArr).contains(key)) {
						currentApplyAmt = l140m01a.getCurrentApplyAmt();
						// currentApplyCurr = l140m01a.getCurrentApplyCurr();
					}
					// END J-105-0203-001 Web e-Loan未有限額條件之科目改以同總核准額度上傳a-Loan

					String value = Util.addSpaceWithValue(currentApplyCurr, 3)
							+ Util.parseBigDecimal(currentApplyAmt);
					itemList.put(key, value);
				}
			}
			// 科子目
			if (l140m01a.getL140m01d() != null) {
				for (L140M01D l140m01d : l140m01a.getL140m01d()) {
					BigDecimal lmtAmt = l140m01d.getLmtAmt();
					String lmtCurr = l140m01d.getLmtCurr();
					if (!"1".equals(l140m01d.getLmtType())
							|| Util.isEmpty(lmtAmt) || Util.isEmpty(lmtCurr)) {
						continue;
					}
					String key = Util.getLeftStr(l140m01d.getSubject(), 3);

					String value = Util.addSpaceWithValue(lmtCurr, 3)
							+ Util.parseBigDecimal(lmtAmt);
					// 有限額時則以限額金額替代
					itemList.put(key, value);
				}
			}
		}
		return itemList;
	}

	/**
	 * 取得授信科目 和 科子目 限額
	 * 
	 * @param l140m01a_bf
	 *            額度明細表
	 * @return <授信科目,幣別+金額>
	 */
	public static HashMap<String, String> getItemListByBf(
			L140M01A_BF l140m01a_bf, List<L140M01C_BF> l140m01c_bfs,
			List<L140M01D_BF> l140m01d_bfs) {
		HashMap<String, String> itemList = new HashMap<String, String>();
		if (l140m01a_bf != null) {
			// 授信科目
			if (l140m01a_bf != null) {
				// J-105-0203-001 Web e-Loan未有限額條件之科目改以同總核准額度上傳a-Loan
				String[] exceptSubjectArr = new String[] { "102", "202", "104",
						"204", "404" };
				for (L140M01C_BF l140m01c : l140m01c_bfs) {

					// BGN J-105-0203-001 Web e-Loan未有限額條件之科目改以同總核准額度上傳a-Loan
					String key = Util.getLeftStr(l140m01c.getLoanTP(), 3);

					// 透支科目還是要帶現請額度 102 202 104 204

					// 預設為同總核准額度(額度為0、幣別為空白)
					BigDecimal currentApplyAmt = BigDecimal.ZERO;
					String currentApplyCurr = l140m01a_bf.getAPLCurr_BF();

					if (Util.isEmpty(l140m01a_bf.getCurrentApply_BF())
							|| Util.isEmpty(l140m01a_bf.getAPLCurr_BF())) {
						continue;
					}

					if (Arrays.asList(exceptSubjectArr).contains(key)) {
						currentApplyAmt = l140m01a_bf.getCurrentApply_BF();
						// currentApplyCurr = l140m01a_bf.getAPLCurr_BF();
					}
					// END J-105-0203-001 Web e-Loan未有限額條件之科目改以同總核准額度上傳a-Loan

					String value = Util.addSpaceWithValue(currentApplyCurr, 3)
							+ Util.parseBigDecimal(currentApplyAmt);
					itemList.put(key, value);
				}
			}
			// 科子目
			if (l140m01a_bf != null) {
				for (L140M01D_BF l140m01d : l140m01d_bfs) {
					BigDecimal lmtAmt = l140m01d.getLmtAmt();
					String lmtCurr = l140m01d.getLmtCurr();
					if (!"1".equals(l140m01d.getLmtType())
							|| Util.isEmpty(lmtAmt) || Util.isEmpty(lmtCurr)) {
						continue;
					}
					String key = Util.getLeftStr(l140m01d.getSubject(), 3);

					String value = Util.addSpaceWithValue(lmtCurr, 3)
							+ Util.parseBigDecimal(lmtAmt);
					// 有限額時則以限額金額替代
					itemList.put(key, value);
				}
			}
		}
		return itemList;
	}

	private static String[] Ignore = { "oid", "mainId", "updateTime",
			"updater", "creator", "createTime", "randomCode" };

	/**
	 * 比較兩個bean的值是否相同
	 * 
	 * @param obj1
	 * @param obj2
	 * @param needFilds
	 *            需要比對的欄位
	 * @return
	 */
	public static boolean compareBean(Object obj1, Object obj2,
			String[] needFilds) {
		BeanMap beanMap1 = BeanMap.create(obj1);
		BeanMap beanMap2 = BeanMap.create(obj2);
		for (Object key : beanMap1.keySet()) {
			String name = Util.trim(key);
			if (needFilds != null && !ArrayUtils.contains(needFilds, name)) {
				continue;
			}

			if (ArrayUtils.contains(Ignore, name)) {
				if (!Util.trim(beanMap2.get(name)).equals(
						Util.trim(beanMap1.get(name)))) {
					logger.error(name
							+ "--> "
							+ Util.trim(beanMap2.get(name)).equals(
									Util.trim(beanMap1.get(name))));
					return false;
				}
			}
		}
		return true;
	}

	/**
	 * 取得每月應付本息金額之平均攤還率 公式{[(1＋月利率)^月數]×月利率}÷{[(1＋月利率)^月數]－1}
	 * 
	 * @param monthrate
	 *            月利率
	 * @param month
	 *            剩餘月數
	 * @return 每月應付本息金額之平均攤還率
	 */
	private static BigDecimal getRate(BigDecimal monthrate, Integer month) {
		return Arithmetic
				.div(Arithmetic.mul(
						monthrate.add(new BigDecimal(1)).pow(month), monthrate),
						Arithmetic.sub(
								monthrate.add(new BigDecimal(1)).pow(month),
								new BigDecimal(1)));

	}

	/**
	 * 取得期付金日期對照表Map<String, String>
	 */
	private static Map<String, String> getRateMap(Integer periods,
			Calendar calendar, BigDecimal Dbalance, BigDecimal periodprincipal,
			BigDecimal periodinterest, BigDecimal periodpay,
			BigDecimal rDbalance) {
		Map<String, String> map = new HashMap<String, String>();
		Format fm1 = new DecimalFormat("#,###");
		map.put("ReportBean.column01", String.valueOf(periods));
		map.put("ReportBean.column02", TWNDate.toTW(calendar.getTime()));
		map.put("ReportBean.column03",
				String.valueOf(fm1.format(Arithmetic.round(Dbalance, 0))));
		map.put("ReportBean.column06", String.valueOf(fm1.format(Arithmetic
				.round(periodprincipal, 0))));
		map.put("ReportBean.column07",
				String.valueOf(fm1.format(Arithmetic.round(periodinterest, 0))));
		map.put("ReportBean.column08",
				String.valueOf(fm1.format(Arithmetic.round(periodpay, 0))));
		map.put("ReportBean.column09",
				String.valueOf(fm1.format(Arithmetic.round(rDbalance, 0))));
		return map;
	}

	/**
	 * 取得期付金日期對照表List<Map<String, String>>
	 * 
	 * @param balance
	 *            (BigDecimal)貸款金額
	 * @param years
	 *            (Integer)期限(年)
	 * @param month
	 *            (Integer)期限(月)
	 * @param cycle
	 *            (Double)繳款周期(12.0=按月繳款 26.0=雙周繳款)
	 * @param repaymod
	 *            (String)繳款方式 ("0"=本息平均攤還 "1"=本金平均攤還)
	 * @param NowEnd
	 *            (Integer)寬限期結束期
	 * @param NowFrom
	 *            (Integer)寬限期起期
	 * @param date
	 *            (Date)起始日期
	 * @param PayWayAmt
	 *            (Double)每期期付金
	 * @param rates
	 *            [][] (Double)分段利率{{起期,迄期,利率(%)},{},...}
	 * 
	 * @return list 每期攤還 ReportBean.column01(期數) ReportBean.column02(日期)
	 *         ReportBean.column03(期初餘額) ReportBean.column06(期付金本金)
	 *         ReportBean.column07(期付金利息) ReportBean.column08(期付金金額)
	 *         ReportBean.column09(末期餘額)
	 */

	/*
	 * Ｑ：貸款400萬,10年(共120期) 選擇3-本金平均攤還(按月繳款，每期攤還本金：41,000元)寬限期001期 －
	 * 024期，按月付息，餘096期本金平均攤還。
	 * 
	 * 產生「 期付金對照表」的 PDF，出現ERROR
	 * 
	 * Ａ：4000000/96（已扣寬限期）=41666.6666 針對 keyin 每期攤還金額 ● 若輸入數字 >=41,667 元 → ok ●
	 * 若輸入數字 <=41,666 元 → error
	 */
	// TODO BTT L210 有期付金試算功能
	public static List<Map<String, String>> getRateList(BigDecimal balance,
			Integer years, Integer month, Double cycle, String repaymod,
			Integer NowEnd, Integer NowFrom, Date date, Double PayWayAmt,
			Double rates[][]) throws CapException {
		boolean periodAmt_ROUNDUP = false;
		return getRateList(balance, years, month, cycle, repaymod, NowEnd,
				NowFrom, date, PayWayAmt, rates, periodAmt_ROUNDUP);
	}

	public static List<Map<String, String>> getRateList(BigDecimal balance,
			Integer years, Integer month, Double cycle, String repaymod,
			Integer NowEnd, Integer NowFrom, Date date, Double PayWayAmt,
			Double rates[][], boolean periodAmt_ROUNDUP) throws CapException {
		List<Map<String, String>> list = new ArrayList<Map<String, String>>();
		// -------------設定 寬限期----------------------------
		int graceperiod = 0;// 寬限期
		if (NowEnd != 0 && NowFrom != 0) {
			graceperiod = NowEnd - NowFrom + 1;
		}
		final int installments = (int) (Arithmetic.mul(Util.parseDouble(years),
				12) + Util.parseDouble(month));// 總月數
		BigDecimal Dbalance = balance;// 餘額
		// -----------------設定日期計算方法---------------
		int datetype;
		int typecount;
		if (cycle == 12.0) {
			// 按月
			datetype = Calendar.MONTH;
			typecount = 1;
		} else {
			// 雙周
			datetype = Calendar.WEEK_OF_MONTH;
			typecount = 2;
		}
		// -----------------設定開始日期---------------
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		// -----------------設定結束日期---------------
		Calendar endDate = Calendar.getInstance();
		endDate.setTime(date);
		endDate.set(Calendar.MONTH, calendar.get(Calendar.MONTH) + installments);// 設定結束日期
		Date Edate = endDate.getTime();
		// --------------------------------------
		int remainperiod = installments;// 剩餘期數
		int Reremainperiod = remainperiod;// 每月應付本息金額之平均攤還率(剩餘期數)
		BigDecimal yearrate = new BigDecimal(0);// 年利率
		BigDecimal Rebalance = Dbalance;// 每月應付本息金額之平均攤還率(餘額)
		BigDecimal monthrate = new BigDecimal(0);
		BigDecimal rate = new BigDecimal(0);
		BigDecimal periodpay;// 期付金金額
		BigDecimal periodinterest;
		BigDecimal periodprincipal = new BigDecimal(0);
		BigDecimal countrate = new BigDecimal(0);
		// -----------------計算金額-------------------
		if (Util.equals(repaymod, "0") && cycle == 12.0) {
			// 本息平均攤還按月繳款-------------------------------------------------------------------------------
			/*
			 * A.試算公式： 每月應付本息金額之平均攤還率 ＝{[(1＋月利率)^月數]×月利率}÷{[(1＋月利率)^月數]－1}
			 * (公式中：月利率 ＝ 年利率／12 ； 月數=貸款年期 ｘ 12)
			 * 
			 * B.每月應攤還本金與利息試算：
			 * ■平均每月應攤付本息金額＝貸款本金×每月應付本息金額之平均攤還率＝每月應還本金金額＋每月應付利息金額
			 * ■每月應付利息金額＝本金餘額×月利率 ■每月應還本金金額＝平均每月應攤付本息金額－每月應付利息金額
			 */
			for (int i = 1; i <= installments; i++) {
				int dates = calendar.get(datetype);
				int periods = i;
				// 取得利率
				for (int rcount = 0; rcount < rates.length; rcount++) {
					if (periods >= rates[rcount][0] && i <= rates[rcount][1]) {
						yearrate = Util.parseBigDecimal(rates[rcount][2]);
						// 若進入新的分段利率、第一期、最後一期則重新取得餘額、剩餘期數、每月應付本息金額之平均攤還率
						if (periods == rates[rcount][0] || i == 1
								|| i == installments || i == NowEnd + 1) {
							Rebalance = Dbalance;
							Reremainperiod = remainperiod - graceperiod;
							if (i >= NowEnd) {
								Reremainperiod = Reremainperiod + graceperiod;
							}
							monthrate = Arithmetic.div(
									Arithmetic.div(yearrate,
											Util.parseBigDecimal(100)),
									Util.parseBigDecimal(cycle));// 月利率
							rate = getRate(monthrate, Reremainperiod);// 每月應付本息金額之平均攤還率
						}
						break;
					}
				}
				if (i >= NowFrom && i <= NowEnd) {
					// 寬限期間
					monthrate = Arithmetic.div(
							Arithmetic.div(yearrate, new BigDecimal(100)),
							Util.parseBigDecimal(cycle));// 月利率
					periodinterest = Arithmetic.round(
							Arithmetic.mul(Dbalance, monthrate), 0);// 期付金利息
					periodpay = periodinterest;// 期付金金額
					periodprincipal = new BigDecimal(0);
				} else {
					// 非寬限期間
					if (periodAmt_ROUNDUP) {
						periodpay = Arithmetic.ceil(
								Arithmetic.mul(Rebalance, rate), 0);
					} else {
						periodpay = Arithmetic.round(
								Arithmetic.mul(Rebalance, rate), 0);// 期付金金額(平均每月應攤付本息金額)
					}
					periodinterest = Arithmetic.round(
							Arithmetic.mul(Dbalance, monthrate), 0);// 期付金利息
					periodprincipal = Arithmetic.sub(
							Arithmetic.round(periodpay, 0),
							Arithmetic.round(periodinterest, 0));// 期付金本金
				}
				list.add(getRateMap(periods, calendar, Dbalance,
						periodprincipal, periodinterest, periodpay,
						Arithmetic.sub(Dbalance, periodprincipal)));
				Dbalance = Arithmetic.sub(Dbalance, periodprincipal);// 餘額
				calendar.set(datetype, dates + typecount);// 下期日期
				remainperiod = remainperiod - 1;// 剩餘期數
				countrate = countrate.add(periodinterest);
			}
		} else if (Util.equals(repaymod, "0") && cycle == 26.0) {
			// 本息平均攤還 雙周繳款
			BigDecimal tweekrate = new BigDecimal(0); // 雙周利率
			int periods = 0;// 目前期數
			while (Dbalance.doubleValue() > 0) {
				periods++;
				int dates = calendar.get(datetype);
				// 計算利率
				for (int rcount = 0; rcount < rates.length; rcount++) {
					if (periods >= rates[rcount][0]
							&& periods <= rates[rcount][1]) {
						yearrate = Util.parseBigDecimal(rates[rcount][2]);
						// 若進入新的分段利率則重新取得餘額、剩餘期數、每月應付本息金額之平均攤還率
						if (periods == rates[rcount][0]) {
							Date Sdate = calendar.getTime();
							BigDecimal count = Arithmetic.floor(Util
									.parseBigDecimal(CapDate.calculateDays(
											Edate, Sdate)), 0);
							Reremainperiod = Arithmetic.floor(
									Arithmetic.mul(count, Arithmetic.div(
											new BigDecimal(12), new BigDecimal(
													365))), 0).intValue();
							monthrate = Arithmetic.div(Arithmetic.div(yearrate,
									new BigDecimal(100)), new BigDecimal(12));// 取得月利率
							tweekrate = Arithmetic.mul(Arithmetic.div(yearrate,
									new BigDecimal(100)), Arithmetic.div(
									new BigDecimal(14), new BigDecimal(365)));// 取得雙周利率=年利率*14/365
							rate = getRate(monthrate, Reremainperiod);// 每月應付本息金額之平均攤還率
							Rebalance = Dbalance;
						}
						break;
					}
				}
				periodinterest = Arithmetic.round(
						Arithmetic.mul(Dbalance, tweekrate), 0);// 期付金利息=餘額*雙周利率
				if (Dbalance.doubleValue() > periodprincipal.doubleValue()) {
					periodpay = Arithmetic.div(Arithmetic.mul(Rebalance, rate),
							new BigDecimal(2), 0);// 期付金金額=平均每月應攤付本息金額/2)
					periodprincipal = Arithmetic.sub(
							Arithmetic.round(periodpay, 0),
							Arithmetic.round(periodinterest, 0));// 期付金本金=期付金-利息
				} else {
					periodprincipal = Dbalance;
					periodpay = periodprincipal.add(periodinterest);
				}
				list.add(getRateMap(periods, calendar, Dbalance,
						periodprincipal, periodinterest, periodpay,
						Arithmetic.sub(Dbalance, periodprincipal)));
				Dbalance = Arithmetic.sub(Dbalance, periodprincipal);// 餘額
				calendar.set(datetype, dates + typecount);// 下期日期
				remainperiod = remainperiod - 1;// 剩餘期數
			}
		} else if (Util.equals(repaymod, "1") && cycle == 12.0) {
			// 本金平均攤還 按月繳款---------------------------
			periodprincipal = Util.parseBigDecimal(PayWayAmt);// 期付金本金
			if (BigDecimal.ZERO.compareTo(periodprincipal) == 0) {
				throw new CapException("***PayWayAmt*** can not be zero",
						LMSUtil.class);
			}

			int periods = 0;// 目前期數
			while (Dbalance.doubleValue() > 0.0) {
				periods++;
				int dates = calendar.get(datetype);
				for (int rcount = 0; rcount < rates.length; rcount++) {
					if (periods >= rates[rcount][0]
							&& periods <= rates[rcount][1]) {
						yearrate = Util.parseBigDecimal(rates[rcount][2]);
						break;
					}
				}
				if (periodprincipal.doubleValue() > Dbalance.doubleValue()) {
					periodprincipal = Dbalance;
				}
				monthrate = Arithmetic.div(
						Arithmetic.div(yearrate, new BigDecimal(100)),
						new BigDecimal(cycle));// 月利率
				periodinterest = Arithmetic.round(
						Arithmetic.mul(Dbalance, monthrate), 2);// 期付金利息
				if (periods >= NowFrom && periods <= NowEnd) {
					periodpay = periodinterest;// 期付金金額
					list.add(getRateMap(periods, calendar, Dbalance,
							BigDecimal.ZERO, periodinterest, periodpay,
							Dbalance));
					calendar.set(datetype, dates + typecount);
					remainperiod = remainperiod - 1;
				} else {
					periodpay = periodprincipal.add(periodinterest);// 期付金金額
					list.add(getRateMap(periods, calendar, Dbalance,
							periodprincipal, periodinterest, periodpay,
							Arithmetic.sub(Dbalance, periodprincipal)));
					Dbalance = Arithmetic.sub(Dbalance, periodprincipal);// 餘額
					calendar.set(datetype, dates + typecount);
				}
			}
		} else if (Util.equals(repaymod, "1") && cycle == 26.0) {
			// 本金平均攤還 雙周繳款---------------------------
			periodprincipal = Util.parseBigDecimal(PayWayAmt);// 期付金本金
			if (BigDecimal.ZERO.compareTo(periodprincipal) == 0) {
				throw new CapException("***PayWayAmt*** can not be zero",
						LMSUtil.class);
			}

			int periods = 0;// 目前期數
			while (Dbalance.doubleValue() > 0) {
				periods++;
				int dates = calendar.get(datetype);
				for (int rcount = 0; rcount < rates.length; rcount++) {
					if (periods >= rates[rcount][0]
							&& periods <= rates[rcount][1]) {
						yearrate = Util.parseBigDecimal(rates[rcount][2]);
						break;
					}
				}
				if (periodprincipal.doubleValue() >= Dbalance.doubleValue()) {
					periodprincipal = Dbalance;
				}
				monthrate = Arithmetic
						.mul(Arithmetic.div(yearrate, new BigDecimal(100)),
								Arithmetic.div(new BigDecimal(14),
										new BigDecimal(365)));// 利率
				periodinterest = Arithmetic.round(
						Arithmetic.mul(Dbalance, monthrate), 2);// 期付金利息
				periodpay = periodprincipal.add(periodinterest);// 期付金金額
				list.add(getRateMap(periods, calendar, Dbalance,
						periodprincipal, periodinterest, periodpay,
						Arithmetic.sub(Dbalance, periodprincipal)));
				Dbalance = Arithmetic.sub(Dbalance, periodprincipal);// 餘額
				calendar.set(datetype, dates + typecount);
			}
		}
		return list;
	}

	/**
	 * 是否為團貸母戶案件
	 * 
	 * @param l120m01a
	 * @return
	 */
	public static boolean isParentCase(L120M01A l120m01a) {
		return UtilConstants.Casedoc.DocCode.團貸案件.equals(l120m01a.getDocCode());
	}

	public static String convertMark(JSONArray word) {
		StringBuilder temp = new StringBuilder("");
		temp.setLength(0);
		for (int b = 0, count = word.size(); b < count; b++) {
			temp.append(temp.length() > 0 ? UtilConstants.Mark.MARK : "");
			temp.append(word.get(b));
		}
		return temp.toString();
	}

	public static String[] convertMark(String word) {
		return Util.trim(word).split(UtilConstants.Mark.SPILT_MARK);
	}

	/**
	 * 將產品種類轉換成ELF431_KINDNO
	 * 
	 * @param value
	 * @return
	 */
	public static String getELF431_KINDNO(String value) {
		String kindNo = "";

		int prodkind = Util.parseInt(value);
		switch (prodkind) {
		case 28:
			kindNo = "1";
			break;
		case 35:
			kindNo = "2";
			break;
		case 56:
			kindNo = "3";
			break;
		case 59:
			kindNo = "4";
			break;
		}

		return kindNo;
	}

	/**
	 * 取得最終評等結果
	 * 
	 * @param value
	 * @return
	 */
	public static String getFinalGrade(String value) {
		/*
		 * //J-111-0221 僅顯示評等1~10等，無須再轉換 String result = ""; switch
		 * (Util.parseInt(Util.trim(value))) { case 1: case 2: case 3: result =
		 * "特A"; break; case 4: case 5: result = "A"; break; case 6: case 7: //
		 * 2013/07/08,Rex,修改值錯誤評等6,7為B result = "B"; break; case 8: result =
		 * "C"; break; case 9: case 10: result = "D"; break; } return result;
		 */
		return Util.trim(value);
	}

	/**
	 * 取得最終評等(特A、A...)
	 * 
	 * @param markModel
	 * @param value
	 * @return
	 */
	public static String getFinalGrade(String markKind, String value) {

		if (Util.equals(UtilConstants.L140S02AModelKind.非房貸, markKind)) {
			return value;
		} else if (Util.equals(UtilConstants.L140S02AModelKind.卡友貸, markKind)) {
			return value;
		}
		return getFinalGrade(value); // 將[1~10等]轉成[特A, A]
	}

	/**
	 * 取得最終評等結果(DW MIS上傳用)
	 * 
	 * @param value
	 * @return
	 */
	public static String getFinalGradeUpUse(String value) {
		/*
		 * //J-111-0221 僅顯示評等1~10等，無須再轉換 String result = ""; switch
		 * (Util.parseInt(Util.trim(value))) { case 1: case 2: case 3: result =
		 * "UA"; break; case 4: case 5: result = "A"; break; case 6: case 7:
		 * result = "B"; break; case 8: result = "C"; break; case 9: case 10:
		 * result = "D"; break; } return result;
		 */
		return Util.trim(value);
	}

	/**
	 * 取得最終評等結果(DW MIS上傳用)
	 * 
	 * @param markModel
	 * @param value
	 * @return
	 */
	public static String getFinalGradeUpUse(String markKind, String value) {

		if (Util.equals(UtilConstants.L140S02AModelKind.非房貸, markKind)) {
			return value;
		} else if (Util.equals(UtilConstants.L140S02AModelKind.卡友貸, markKind)) {
			return value;
		}
		return getFinalGradeUpUse(value); // 將[1~10等]轉成[UA, A]
	}

	/**
	 * 若原有 4 個產品 1, 2, 3, 4 刪掉 1,3 剩 2, 4
	 * 
	 * 原本的邏輯: seq+64 會印出 B, D 現在以 seq 排序, 把 2-A, 4-B, 印出 A, B
	 * 
	 * 加入 uiSeq 的概念. order by uiSeq, seq
	 */
	public static Map<Integer, String> getPrintStrForProdSeqNo(Object[] listArr) {

		Map<Integer, String> r = new HashMap<Integer, String>();
		/*
		 * 為了要 order by uiSeq, seq 把 uiSeq 轉成 5 碼文字, 前補0 (1 → 00001), 串接 _, 串接
		 * seq 轉成 5 碼文字, 前補0 (1 → 00001)
		 * 
		 * EX: [uiSeq=1, seq=3][uiSeq=2, seq=1]
		 * 
		 * uiseq_seq_strSet 的內容 00001_00003 00002_00001
		 */
		TreeSet<String> uiseq_seq_strSet = new TreeSet<String>();
		for (Object o : listArr) {
			Integer uiSeq = null;
			Integer seq = null;
			if (o instanceof C160S01C) {
				C160S01C c160s01c = (C160S01C) o;
				uiSeq = c160s01c.getUiSeq();
				seq = c160s01c.getSeq();
			} else if (o instanceof L140S02A) {
				L140S02A l140s02a = (L140S02A) o;
				uiSeq = l140s02a.getUiSeq();
				seq = l140s02a.getSeq();
			}

			if (Util.isNotEmpty(seq)) {
				String k = Util.addZeroWithValue(uiSeq == null ? 0 : uiSeq, 5)
						+ "_" + Util.addZeroWithValue(seq == null ? 0 : seq, 5);
				uiseq_seq_strSet.add(k);
			}
		}

		int idx = 65;
		for (String k : uiseq_seq_strSet) {
			Integer seq = Util.parseInt(k.substring(6));

			// ascii code: 65 A
			r.put(seq, String.valueOf((char) idx));
			idx++;
		}
		return r;
	}

	/**
	 * 取得泰國地區的全行代碼
	 */
	public static Set<String> get_TH_BRNO_SET() {
		HashSet<String> r = new HashSet<String>();
		r.add(UtilConstants.BankNo.泰國曼谷總行);
		r.add(UtilConstants.BankNo.曼谷春武里分行);
		r.add(UtilConstants.BankNo.挽那分行);
		r.add(UtilConstants.BankNo.萬磅分行);
		r.add(UtilConstants.BankNo.羅勇分行);
		return r;
	}
	
	/**
	 * 取得越南地區的全行代碼
	 */
	public static Set<String> get_VN_BRNO_SET() {
		HashSet<String> r = new HashSet<String>();
		r.add(UtilConstants.BankNo.胡志明市分行);
		return r;
	}

	/**
	 * 取得日本地區的全行代碼
	 */
	public static Set<String> get_JP_BRNO_SET() {
		HashSet<String> r = new HashSet<String>();
		r.add(UtilConstants.BankNo.東京分行);
		r.add(UtilConstants.BankNo.大阪分行);
		return r;
	}

	/**
	 * 取得澳洲地區的全行代碼
	 */
	public static Set<String> get_AU_BRNO_SET() {
		HashSet<String> r = new HashSet<String>();
		r.add(UtilConstants.BankNo.雪梨分行);
		r.add(UtilConstants.BankNo.布里斯本分行);
		r.add(UtilConstants.BankNo.墨爾本分行);
		return r;
	}
	
	/**
	 * 取得法國地區的全行代碼
	 */
	public static Set<String> get_FR_BRNO_SET() {
		HashSet<String> r = new HashSet<String>();
		r.add(UtilConstants.BankNo.法國巴黎分行);
		return r;
	}
	
	/**
	 * 取得加拿大地區的全行代碼
	 */
	public static Set<String> get_CA_BRNO_SET() {
		HashSet<String> r = new HashSet<String>();
		r.add(UtilConstants.BankNo.加拿大多倫多);
		r.add(UtilConstants.BankNo.加拿大溫哥華);
		return r;
	}

	public static boolean isTypcd5CntrNo(String cntrNo) {
		return (UtilConstants.Casedoc.typCd.海外.equals(StringUtils.substring(
				cntrNo, 3, 4)));
	}

	/**
	 * 取得簽報書上傳案號-> 民國年 + 分行別+{LMS企金、CLS個金}+末五碼流水號
	 * 
	 * @param l120m01a
	 *            簽報書
	 * @param showSchema
	 *            是否顯示Schema
	 * @return 案號 民國年 + 分行別+{LMS企金、CLS個金}+末五碼流水號
	 */
	public static String getUploadCaseNo(String docType, int caseYear,
			String CaseBrId, int CaseSeq, boolean showSchema) {
		String schema = "";
		if (showSchema) {
			if (UtilConstants.Casedoc.DocType.個金.equals(docType)) {
				schema = UtilConstants.CaseSchema.個金;
			} else {
				schema = UtilConstants.CaseSchema.企金;
			}
		}
		return StrUtils.concat(caseYear - 1911, CaseBrId, schema,
				Util.addZeroWithValue(Util.trim(CaseSeq), 5));
	}

	/**
	 * 取得 custId補足10碼 + dupNo
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public static String getCustKey_len10custId(String custId, String dupNo) {
		return Util.addSpaceWithValue(custId, 10) + dupNo;
	}

	/**
	 * 比較物件的值
	 */
	public static Map<String, Object[]> diffFieldValue_inColumn(Object o1,
			Object o2, Set<String> colSet) {
		return _diffFieldValue_cmpColumn(o1, o2, true, colSet);
	}

	public static Map<String, Object[]> diffFieldValue_exColumn(Object o1,
			Object o2, Set<String> colSet) {
		return _diffFieldValue_cmpColumn(o1, o2, false, colSet);
	}

	public static String dump_diffFieldValue(Map<String, Object[]> m) {
		List<String> r = new ArrayList<String>();
		for (String k : m.keySet()) {
			Object[] arr = m.get(k);
			r.add("key=" + k + "[" + arr[0] + "][" + arr[1] + "]");
		}
		return StringUtils.join(r, "\r\n");
	}

	private static Map<String, Object[]> _diffFieldValue_cmpColumn(Object o1,
			Object o2, boolean isIn, Set<String> raw_colSet) {
		Map<String, String> colSetPart = new HashMap<String, String>();
		for (String k : raw_colSet) {
			colSetPart.put(k.toLowerCase(), k);
		}
		boolean has_colSetPart = colSetPart.size() > 0;
		// ----------------
		Set<String> colSetAll = new HashSet<String>();

		Map<String, Object[]> r = new LinkedHashMap<String, Object[]>();
		BeanMap m1 = BeanMap.create(o1);
		BeanMap m2 = BeanMap.create(o2);

		for (Object col : m1.keySet()) {
			Object v1 = m1.get(col);
			Object v2 = m2.get(col);
			/*
			 * 用 beanMap 去取 C120S01C.yFamAmt 時 雖然在 model 裡是 yFamAmt，但
			 * BeanMap.create(...)抓到的卻是 YFamAmt(第1碼的大小寫有差) 為免這種狀況，統一用「小寫」去比對
			 */
			String str_col = Util.trim(col).toLowerCase();
			colSetAll.add(str_col);

			if (has_colSetPart) {
				if (isIn) {
					if (!colSetPart.keySet().contains(str_col)) {
						continue;
					}
				} else {
					if (colSetPart.keySet().contains(str_col)) {
						continue;
					}
				}
			}

			if (!eq_with_null(v1, v2)) {
				// 傳入的 yFamAmt，在 colSetPart 是{yfamamt, yFamAmt}
				// 為了讓顯示的欄位中文能正常，應回傳 yFamAmt
				r.put(getDesc(colSetPart, str_col), new Object[] { v1, v2 });
			}
		}

		if (true) {
			Set<String> notMatchColSet = elm_onlyLeft(colSetPart.keySet(),
					colSetAll);
			if (notMatchColSet.size() > 0) {
				logger.info("_diffFieldValue_cmpColumn not contains【"
						+ notMatchColSet + "】 in " + colSetAll);
			}
		}
		return r;
	}

	/**
	 * equals() checks if the BigDecimal objects are exactly the same in every
	 * aspect. compareTo() "only" compares their numeric value.
	 * 
	 * 若傳入 new BigDecimal("1") 及 new BigDecimal("1.00") ‧用 equals() , return
	 * false ‧用 compareTo() , return true
	 */
	public static boolean eq_with_null(Object v1, Object v2) {

		if (v1 != null && v2 != null && v1 instanceof BigDecimal
				&& v2 instanceof BigDecimal) {
			return ((BigDecimal) v1).compareTo((BigDecimal) v2) == 0;
		}
		// 為免 '' 與 ' ' 不同，改成用 Util.equals()
		// 可能 CHAR(1) 的欄位，用 DataParse.toBean(jsonStr, genericBean) 抓到的值是 ''
		// 但用 JPA 抓到的值是 ' '
		// return (v1 == null ? v2 == null : v1.equals(v2));
		return (v1 == null ? v2 == null : Util.equals(v1, v2, false));
	}

	/**
	 * 比較[年、月、日]
	 */
	public static boolean cmpDate(Date d1, String sign, Date d2) {
		return cmp_use_flag(d1, sign, d2, UtilConstants.DateFormat.YYYY_MM_DD);
	}

	/**
	 * 比較[年、月].不比較日
	 */
	public static boolean cmp_yyyyMM(Date d1, String sign, Date d2) {
		return cmp_use_flag(d1, sign, d2, UtilConstants.DateFormat.YYYY_MM);
	}

	private static boolean cmp_use_flag(Date d1, String sign, Date d2,
			String fmt) {
		int a = Integer.parseInt(CapDate.formatDate(d1, fmt).replace("-", ""));
		int b = Integer.parseInt(CapDate.formatDate(d2, fmt).replace("-", ""));
		if (sign.equals("<")) {
			return (a < b);
		} else if (sign.equals("<=")) {
			return (a <= b);
		} else if (sign.equals("==")) {
			return (a == b);
		} else if (sign.equals(">=")) {
			return (a >= b);
		} else if (sign.equals(">")) {
			return (a > b);
		}
		// default 用 < 判斷
		return (a < b);
	}

	/** 參考 CapBeanUtil.map2Bean() */
	public static int writeParamsToJson(PageParameters params,
			JSONObject jsonObject, Set<String> cols) {
		int write_cnt = 0;
		for (String key : cols) {
			boolean exist_params = params.containsKey(key);
			boolean exist_jsonObj = jsonObject.containsKey(key);
			/*
			 * 也許要對 keyin 資料，做 escape。Ref
			 * https://stackoverflow.com/questions/21576475
			 * /escape-json-string-in-java
			 */
			if (exist_params && exist_jsonObj) {
				jsonObject.put(key, params.getString(key));
				++write_cnt;
			} else {
				continue;
			}
		}
		return write_cnt;
	}

	@SuppressWarnings("unchecked")
	public static int addJsonToStringMap(Map<String, String> map,
			JSONObject jsonObject) {
		if (jsonObject != null && !jsonObject.isEmpty()) {
			map.putAll(JSONUtil.parseJsonToStringMap(jsonObject));
			return jsonObject.size();
		}
		return 0;
	}

	@SuppressWarnings("unchecked")
	public static int addJsonToMap(Map<String, Object> map,
			JSONObject jsonObject) {
		if (jsonObject != null && !jsonObject.isEmpty()) {
			map.putAll(jsonObject);
			return jsonObject.size();
		}
		return 0;
	}

	public static int addJsonToMap(Map<String, Object> map,
			JSONObject jsonObject, String[] cols) {
		Set<String> set = new HashSet<String>();
		if (cols != null && cols.length > 0) {
			for (String colName : cols) {
				set.add(colName);
			}
		}
		return addJsonToMap(map, jsonObject, set);
	}

	public static int addJsonToMap(Map<String, Object> map,
			JSONObject jsonObject, Collection<String> cols) {
		int add_cnt = 0;
		if (cols.size() > 0) {
			for (String colName : cols) {
				if (jsonObject.containsKey(colName)) {
					map.put(colName, jsonObject.get(colName));
					++add_cnt;
				} else {
					continue;
				}
			}
		}
		return add_cnt;
	}

	public static void addJsonToResult(CapAjaxFormResult result,
			JSONObject jsonObject, String[] cols) {
		Set<String> set = new HashSet<String>();
		if (cols != null && cols.length > 0) {
			for (String colName : cols) {
				set.add(colName);
			}
		}
		addJsonToResult(result, jsonObject, set);
	}

	public static void addJsonToResult(CapAjaxFormResult result,
			JSONObject jsonObject, Collection<String> cols) {
		if (cols.size() > 0) {
			Map<String, Object> map = new HashMap<String, Object>();
			if (addJsonToMap(map, jsonObject, cols) > 0) {
				result.putAll(map);
			}
		}
	}

	public static void addMetaToResult(CapAjaxFormResult result,
			GenericBean meta, String[] cols) throws CapException {
		Map<String, Object> m = new HashMap<String, Object>();
		meta_to_map(m, meta, cols);
		result.putAll(m);
	}

	@SuppressWarnings("unchecked")
	public static void meta_to_map(Map<String, Object> m, GenericBean meta,
			String[] cols) throws CapException {
		Map<String, IFormatter> format = new HashMap<String, IFormatter>();
		// ---
		// meta.toJSONObject 當 column 為 null,未加入 map
		if (meta != null) {
			m.putAll(meta.toJSONObject(cols, format));
		}

		for (String col : cols) {
			if (!m.containsKey(col)) {
				m.put(col, "");
			}
		}
	}

	/**
	 * 上傳至 DW,MIS 的家庭年收入(夫妻年收入), 最大傳 5 位
	 * 
	 * DW_RKAPPLICANT.HINCOME DECIMAL(10,0) DW_RKSCORE.HINCOME_REG DECIMAL(6,0)
	 * ELF500.ELF500_HINCOME DECIMAL(5,0)
	 * 
	 * 為了一致,取最小的 5 位
	 * 
	 * @param s
	 * @return
	 */
	public static BigDecimal getUploadYFamAmt(BigDecimal s) {
		if (CapMath.compare(s, "99999") > 0) {
			return CapMath.getBigDecimal("99999");
		}
		return s;
	}

	public static void setL120M01M(CapAjaxFormResult formResult,
			L120S01M l120s01m) {
		formResult.set("l120s01m_queryDate", Util.trim((l120s01m == null ? ""
				: TWNDate.toAD(l120s01m.getQueryDate()))));
	}

	public static String getNotEmptyVal_str(Map<String, Object> m, String k1,
			String k2) {
		if (m != null) {
			List<String> k_list = new ArrayList<String>();
			k_list.add(k1);
			k_list.add(k2);
			// ---
			for (String k : k_list) {
				String v = Util.trim(m.get(k));
				if (Util.isNotEmpty(v)) {
					return v;
				}
			}
		}

		return "";
	}

	public static String getDesc(Map<String, String> map, String k) {
		if (map.containsKey(k)) {
			return map.get(k);
		} else {
			return k;
		}
	}

	// UPGRADE: genHtmlComponent()方法已棄用，待確定其他引用去除後，即可刪掉
//	public static Component genHtmlComponent(String id, boolean visible) {
//		Label r = new Label(id, "");
//		r.setVisible(visible);
//		return r;
//	}

	public static Set<String> elm_onlyLeft(Collection<String> a,
			Collection<String> b) {
		Set<String> r = new HashSet<String>();
		for (String elm : a) {
			if (b.contains(elm)) {
				// both
			} else {
				r.add(elm);
			}
		}
		return r;
	}

	public static Set<String> elm_onlyRight(Collection<String> a,
			Collection<String> b) {
		Set<String> r = new HashSet<String>();
		for (String elm : b) {
			if (a.contains(elm)) {
				// both
			} else {
				r.add(elm);
			}
		}
		return r;
	}

	public static Set<String> elm_join(Collection<String> a,
			Collection<String> b) {
		Set<String> r = new HashSet<String>();
		for (String elm : a) {
			if (b.contains(elm)) {
				r.add(elm);
			}
		}
		return r;
	}

	public static C101S01G copy_to_C101S01G(C120S01G model_g)
			throws CapException {
		if (model_g != null) {
			C101S01G c101s01g = new C101S01G();
			DataParse.copy(model_g, c101s01g);
			return c101s01g;
		}
		return null;
	}

	public static C120S01G copy_to_C120S01G(C101S01G model_g)
			throws CapException {
		if (model_g != null) {
			C120S01G c120s01g = new C120S01G();
			DataParse.copy(model_g, c120s01g);
			return c120s01g;
		}
		return null;
	}

	public static C101S01Q copy_to_C101S01Q(C120S01Q model_q)
			throws CapException {
		if (model_q != null) {
			C101S01Q c101s01q = new C101S01Q();
			DataParse.copy(model_q, c101s01q);
			return c101s01q;
		}
		return null;
	}

	public static C120S01Q copy_to_C120S01Q(C101S01Q model_q)
			throws CapException {
		if (model_q != null) {
			C120S01Q c120s01q = new C120S01Q();
			DataParse.copy(model_q, c120s01q);
			return c120s01q;
		}
		return null;
	}

	public static C101S01R copy_to_C101S01R(C120S01R model_r)
			throws CapException {
		if (model_r != null) {
			C101S01R c101s01r = new C101S01R();
			DataParse.copy(model_r, c101s01r);
			return c101s01r;
		}
		return null;
	}

	public static C120S01R copy_to_C120S01R(C101S01R model_r)
			throws CapException {
		if (model_r != null) {
			C120S01R c120s01r = new C120S01R();
			DataParse.copy(model_r, c120s01r);
			return c120s01r;
		}
		return null;
	}

	public static C120S01A copy_to_C120S01A(C101S01A model) throws CapException {
		if (model != null) {
			C120S01A c120s01a = new C120S01A();
			DataParse.copy(model, c120s01a);
			return c120s01a;
		}
		return null;
	}

	public static C120S01B copy_to_C120S01B(C101S01B model) throws CapException {
		if (model != null) {
			C120S01B c120s01b = new C120S01B();
			DataParse.copy(model, c120s01b);
			return c120s01b;
		}
		return null;
	}

	public static C120S01C copy_to_C120S01C(C101S01C model) throws CapException {
		if (model != null) {
			C120S01C c120s01c = new C120S01C();
			DataParse.copy(model, c120s01c);
			return c120s01c;
		}
		return null;
	}

	public static C120S01E copy_to_C120S01E(C101S01E model) throws CapException {
		if (model != null) {
			C120S01E c120s01e = new C120S01E();
			DataParse.copy(model, c120s01e);
			return c120s01e;
		}
		return null;
	}

	public static boolean check_L140M01M_ChkYN(L140M01M l140m01m) {
		if (l140m01m != null) {
			if (Util.equals("Y", l140m01m.getCheckYN())
					&& Util.equals(UtilConstants.DEFAULT.否, l140m01m.getChkYN())) {
				return false;
			}
		}
		return true;
	}

	/*
	 * 在 CLS1141M01Page.js 的 function check_clsRatingModel(){...} =>
	 * cls1141m01formhandler :: check_clsRatingModel 會判斷 varVersion 是否過期，而去 lock
	 * 簽報書頁面(重引latest version 後才解除)
	 */
	// public static final String V1_3_HOUSE_LOAN = "1.3";
	// public static final String V2_0_HOUSE_LOAN = "2.0"; // J-106-0187

	/**
	 * 值同C101S01Q.varVer，才能依 varVer 重算
	 */
	// public static final String V1_0_NOT_HOUSE_LOAN = "1.0";
	// public static final String V1_9_NOT_HOUSE_LOAN = "1.9"; // J-108-0041
	// public static final String V2_0_NOT_HOUSE_LOAN = "2.0"; // J-103-0335
	// public static final String V2_1_NOT_HOUSE_LOAN = "2.1"; // J-108-0105

	// 卡友貸
	// public static final String V2_1_CARD_LOAN = "2.1";

	// private static Date[] _S01GS01Q_configDate(String tbl, String varVer) {
	// String d1 = "0001-01-01";
	// String d2 = "9999-12-31";// 生效日
	// String d3 = "1911-01-01";// 預設無 buffer(過渡迄日)
	// if (Util.equals("G", tbl)) {
	// if (Util.equals(V2_0_HOUSE_LOAN, varVer)) {
	/*
	 * 本行新改版「消金房貸申請信用評等模型」訂於106年11月30日上線
	 * 
	 * 有關房貸在途案件之處理，考量營業單位承辦案件之作業時程，聯徵中心及票信查詢日期為106年11月20日至106年11月29日編製中之簽報書，106
	 * 年12月15日前仍可送呈覆核，106年12月16日起均須以新版模型評等後，才能送呈覆核。
	 */
	// d1 = "2017-11-20";
	// d2 = "2017-11-30";// G2.0的生效日
	// d3 = "2017-12-15";
	// }
	// } else if (Util.equals("Q", tbl)) {
	// if (Util.equals(V2_0_NOT_HOUSE_LOAN, varVer)) {
	// d1 = "2014-12-21";
	// d2 = "2015-01-01";// Q2.0的生效日
	// d3 = "2015-01-15";
	// }else if (Util.equals(V2_1_NOT_HOUSE_LOAN, varVer)) {
	// d1 = "2019-08-23"; //J-108-0105
	// d2 = "2019-08-30";// Q2.1的生效日
	// d3 = "2019-09-15";
	// }
	// }
	// return new Date[] { CapDate.parseDate(d1), CapDate.parseDate(d2),
	// CapDate.parseDate(d3) };
	// }

	// ==================================================
	public static final String MOWTYPE_M_CHK01 = "MOWTYPE_M_CHK01";

	// public static Date S01GS01Q_ActiveDate(String tbl, String varVer) {
	// return _S01GS01Q_configDate(tbl, varVer)[1];
	// }

	// ==================================================
	// public static boolean S01GS01Q_inBufferPeriod(String tbl, String varVer)
	// {
	// Date buffer_end_date = _S01GS01Q_configDate(tbl, varVer)[2];
	//
	// if (LMSUtil.cmpDate(new Date(), ">=", S01GS01Q_ActiveDate(tbl, varVer))
	// && LMSUtil.cmpDate(new Date(), "<=", buffer_end_date)) {
	// return true;
	// }
	//
	// return false;
	// }

	// ==================================================
	// public static boolean S01GS01Q_inBufferPeriodQdateExpired(String tbl,
	// String varVer, List<C120S01E> list) {
	// String cmpSign = "<";
	// Date cmp_qDate = _S01GS01Q_configDate(tbl, varVer)[0];
	//
	// for (C120S01E c120s01e : list) {
	// 檢查 QDATE 是否在允許區間
	// if ((c120s01e.getEJcicQDate() != null && LMSUtil.cmpDate(
	// c120s01e.getEJcicQDate(), cmpSign, cmp_qDate))
	// || (c120s01e.getEChkQDate() != null && LMSUtil.cmpDate(
	// c120s01e.getEChkQDate(), cmpSign, cmp_qDate))) {
	// return true;
	// }
	// }
	// return false;
	// }

	public static boolean isProdKind_02_04(String prodKind) {
		if (Util.equals(ProdService.ProdKindEnum.行家理財貸款_短期_02.getCode(),
				prodKind)
				|| Util.equals(ProdService.ProdKindEnum.綜合理財_04.getCode(),
						prodKind)) {
			return true;
		}
		return false;
	}

	/**
	 * 續約-2,提前續約-11
	 */
	public static boolean chgCon_isProperty_has_renewal(String property,
			String subProperty) {
		Set<String> r = new HashSet<String>();

		for (String propStr : StringUtils.split(Util.trim(property), "|")) {
			r.add(Util.trim(propStr));
		}
		for (String propStr : StringUtils.split(Util.trim(subProperty), "|")) {
			r.add(Util.trim(propStr));
		}

		return (r.contains(UtilConstants.Cntrdoc.Property.續約) || r
				.contains(UtilConstants.Cntrdoc.Property.提前續約者));
	}

	public static String cntrNoProperty_code_orderBy(String property,
			String subProperty) {
		Set<String> inputSet = new HashSet<String>();
		if (true) {
			for (String propStr : StringUtils.split(Util.trim(property), "|")) {
				inputSet.add(Util.trim(propStr));
			}
			for (String propStr : StringUtils
					.split(Util.trim(subProperty), "|")) {
				inputSet.add(Util.trim(propStr));
			}
		}

		List<String> r = new ArrayList<String>();
		if (inputSet.size() > 0) {
			// =========
			// 依 下列 的順序來排
			LinkedHashSet<String> orderSet = new LinkedHashSet<String>();
			if (true) {
				orderSet.add(UtilConstants.Cntrdoc.Property.報價);
				orderSet.add(UtilConstants.Cntrdoc.Property.新做);
				orderSet.add(UtilConstants.Cntrdoc.Property.增額);
				orderSet.add(UtilConstants.Cntrdoc.Property.紓困);
				orderSet.add(UtilConstants.Cntrdoc.Property.協議清償);
				orderSet.add(UtilConstants.Cntrdoc.Property.減額);
				orderSet.add(UtilConstants.Cntrdoc.Property.變更條件);
				orderSet.add(UtilConstants.Cntrdoc.Property.續約);
				orderSet.add(UtilConstants.Cntrdoc.Property.提前續約者);
				orderSet.add(UtilConstants.Cntrdoc.Property.展期);
				orderSet.add(UtilConstants.Cntrdoc.Property.流用);
				orderSet.add(UtilConstants.Cntrdoc.Property.取消);
				orderSet.add(UtilConstants.Cntrdoc.Property.不變);
			}
			// =========
			if (true) {
				Set<String> joinSet = LMSUtil.elm_join(inputSet, orderSet);
				for (String k : orderSet) {
					if (joinSet.contains(k)) {
						r.add(k);
					}
				}
			}

			if (true) {
				// 加強判斷，若有不在 orderSet 的代碼,也要出現
				Set<String> notInOrderSet = LMSUtil.elm_onlyLeft(inputSet,
						orderSet);
				if (notInOrderSet.size() > 0) {
					r.addAll(notInOrderSet);
				}
			}
		}
		return StringUtils.join(r, "|");
	}

	public static String cntrNoProperty_desc_orderBy(L140S02A l140s02a,
			String signal, Map<String, String> code_desc_map) {
		String src_property = cntrNoProperty_code_orderBy(
				l140s02a.getProperty(), l140s02a.getSubProperty());
		return cntrNoProperty_code_to_desc(src_property, code_desc_map, signal);
	}

	public static String cntrNoProperty_code_to_desc(String src_property,
			Map<String, String> code_desc_map, String signal) {
		List<String> r = new ArrayList<String>();
		String[] arr = StringUtils.split(Util.trim(src_property), "|");
		if (arr == null || arr.length == 0) {

		} else {
			for (String propCode : arr) {
				r.add(LMSUtil.getDesc(code_desc_map, propCode));
			}
		}
		return StringUtils.join(r, signal);
	}

	public static void set_property_to_NotChange(L140S02A l140s02a) {
		l140s02a.setProperty(UtilConstants.Cntrdoc.Property.不變);
		l140s02a.setSubProperty("");
	}

	public static void set_property_to_Cancel(L140S02A l140s02a) {
		l140s02a.setProperty(UtilConstants.Cntrdoc.Property.取消);
		l140s02a.setSubProperty("");
	}

	public static boolean isLost_l140s02a_grade1(L140S02A l140s02a) {
		if (l140s02a != null) {
			String property = l140s02a.getProperty();
			if (UtilConstants.Cntrdoc.Property.不變.equals(property)
					|| UtilConstants.Cntrdoc.Property.取消.equals(property)) {
				// 不需評等, grade1 可空白
				return false;
			}
			// -------------------
			String modelKind = Util.trim(l140s02a.getModelKind());
			if (Util.equals(UtilConstants.L140S02AModelKind.免辦, modelKind)) {
				// 免辦, 不檢查
				return false;
			}
			if (Util.isNotEmpty(Util.trim(l140s02a.getGrade1()))) {
				return false;// 有分數
			} else {
				if (Util.equals(UtilConstants.L140S02AModelKind.房貸,
						l140s02a.getModelKind())) {
					return true;
				} else if (Util.equals(UtilConstants.L140S02AModelKind.非房貸,
						l140s02a.getModelKind())) {
					return true;
				} else if (Util.equals(UtilConstants.L140S02AModelKind.卡友貸,
						l140s02a.getModelKind())) {
					return true;
				} else {
					// ...
				}
			}
		}
		return true;
	}

	/**
	 * 在 L130S01ADaoImpl 中，原程式指定 sqlColumn 要再加上 ctlDscr
	 */
	public static final String L130S01A_A01 = "A01";
	public static final String L130S01A_A32 = "A32";

	public static boolean isBusCode_060000_130300(String busCode) {
		if (Util.equals("060000", busCode) || Util.equals("130300", busCode)) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * N：自然人 ； C：法人
	 */
	public static String get_mapped_renCd(C120S01A c120s01a) {
		String busCode = Util.trim(c120s01a.getBusCode());
		if (isBusCode_060000_130300(busCode)) {
			return "N";
		} else {
			return "C";
		}
	}

	public static String l120s01h_dataName__to__c120s01d_mateFlag(
			String l120s01h_dataName) {
		Map<String, String> m = new HashMap<String, String>();
		m.put("0", "A");// 不登錄配偶資料
		m.put("1", "B");// 列於本欄
		m.put("2", "C");// 同本案借保人
		if (m.containsKey(l120s01h_dataName)) {
			return m.get(l120s01h_dataName);
		} else {
			return l120s01h_dataName;
		}
	}

	public static boolean isOverSea_CLS(L120M01A l120m01a) {
		return Util.equals(UtilConstants.Casedoc.DocType.個金,
				l120m01a.getDocType())
				&& Util.equals(UtilConstants.Casedoc.typCd.海外,
						l120m01a.getTypCd());
	}

	/**
	 * 按長度分割字串
	 * 
	 * @param content
	 *            要分割的字串
	 * @param len
	 *            分割的每段長度
	 * @return
	 */
	public static String[] split(String content, int len) {

		if (content == null || content.equals("")) {
			return null;
		}

		String[] tokens = content.split("(?<=\\G.{" + len + "})");

		/*
		 * int len2 = content.length(); if (len2 <= len) { return new String[] {
		 * content }; } else { int i = len2 / len + 1; System.out.println("i:" +
		 * i); String[] strA = new String[i]; int j = 0; int begin = 0; int end
		 * = 0; while (j < i) { begin = j * len; end = (j + 1) * len; if (end >
		 * len2) end = len2; strA[j] = content.substring(begin, end); //
		 * System.out.println(strA[j]+"<br/>"); j = j + 1; } return strA; }
		 */

		return tokens;

	}

	public static boolean needCheck72_2(L120M01A l120m01a, L140M01A l140m01a) {
		String cntrBranch = Util.trim(StringUtils.substring(
				l140m01a.getCntrNo(), 0, 3));

		if ((Util.equals(l140m01a.getProPerty(),
				UtilConstants.Cntrdoc.Property.不變) && Util.notEquals(
				cntrBranch, l120m01a.getCaseBrId()))
				|| Util.equals(l140m01a.getProPerty(),
						UtilConstants.Cntrdoc.Property.取消)) {
			// skip
			return false;
		}
		return true;
	}

	public static boolean is722QDateExpired(L140M01A l140m01a) {
		if (l140m01a.getIs722QDate() != null) {
			Date currentDate = new Date();
			int diffDays = CapDate.calculateDays(currentDate,
					l140m01a.getIs722QDate());
			if (diffDays > 31) {
				return true;
			}
		}
		return false;
	}

	public static Map<String, Object> jsonStrToMap(String jsonStr) {
		return jsonToMap(JSONObject.fromObject(jsonStr));
	}

	public static Map<String, Object> jsonToMap(JSONObject json) {
		Map<String, Object> retMap = new HashMap<String, Object>();

		if (json != null) {
			retMap = jsonobject_toMap(json);
		}
		return retMap;
	}

	@SuppressWarnings("unchecked")
	public static Map<String, Object> jsonobject_toMap(JSONObject object) {
		Map<String, Object> map = new HashMap<String, Object>();

		Iterator<String> keysItr = object.keys();
		while (keysItr.hasNext()) {
			String key = keysItr.next();
			Object value = object.get(key);

			if (value instanceof JSONArray) {
				value = jsonarray_toList((JSONArray) value);
			}

			else if (value instanceof JSONObject) {
				value = jsonobject_toMap((JSONObject) value);
			}
			map.put(key, value);
		}
		return map;
	}

	public static List<Object> jsonarray_toList(JSONArray array) {
		List<Object> list = new ArrayList<Object>();
		for (int i = 0; i < array.size(); i++) {
			Object value = array.get(i);
			if (value instanceof JSONArray) {
				value = jsonarray_toList((JSONArray) value);
			}

			else if (value instanceof JSONObject) {
				value = jsonobject_toMap((JSONObject) value);
			}
			list.add(value);
		}
		return list;
	}

	public static String pretty_numStr(BigDecimal r) {
		if (r == null) {
			return "";
		} else if (r.compareTo(BigDecimal.ZERO) == 0) {
			/*
			 * 為了在處理 lnf916s_accu_int
			 */
			return "0";
		} else {
			return r.stripTrailingZeros().toPlainString();
		}
	}

	/**
	 * J-104-0097-001 配合業報指示，有關建商餘屋貸款之餘額列入控管，申請於e-Loan管理系統授信簽報書之額度明細表增加管控註記
	 * 
	 * @param l140m01m
	 *            model
	 * @param codeMap
	 *            代碼對應表 L140M01M_cbcCase 維護央行項目 L140M01M_plusReason
	 *            L140M01M_cbcCase =4 的原因選項 city 縣市名稱 area 鄉鎮市區名稱
	 * @return
	 */
	public static String buildRemainHouseLoanStr(L140M01M l140m01m,
			Map<String, CapAjaxFormResult> codeMap) {
		// L140M01M_cbcCase 維護央行項目
		// L140M01M_plusReason L140M01M_cbcCase =4 的原因選項
		StringBuffer temp = new StringBuffer();
		if (l140m01m != null) {
			// Properties prop = MessageBundleScriptCreator
			// .getComponentResource(LMSL140M01MPanel.class);

			// String isLimitCustStr = "";
			// String isHighHouseStr = "";
			// String plusReasonStr = Util.trim(codeMap.get(
			// "lms140_remainLoanClass").get(l140m01m.getRemainLoanClass()));

			// String br = "<br/>";

			// temp.append(prop.getProperty("L140M01M.remainLoanClass"));
			// temp.append("：");
			// temp.append(plusReasonStr);
			// temp.append(br);
			// temp.append(prop.getProperty("L140M01M.004"));
			// temp.append("：");
			temp.append(Util.trim(codeMap.get("L140M01M_city").get("city")));
			temp.append(Util.trim(codeMap.get("L140M01M_city").get("area")));
			temp.append(Util.trim(codeMap.get("L140M01M_city").get("site3")));
			// temp.append(br);

		}
		return temp.toString();
	}

	/**
	 * J-105-0155-001 Web e-Loan國內、海外企金額度明細表增加『約定融資額度註記』欄位與上傳a-Loan功能 _ 為N.A.
	 * 上傳時改為""
	 */
	public static String fetch_l140m01a_except(L140M01A l140m01a) {
		String exceptFlag = l140m01a.getExceptFlag();
		// 把 _ 替換為 ""
		if (Util.equals(exceptFlag, "_")) {
			return "";
		}
		return Util.trim(exceptFlag);
	}

	/**
	 * 依照上傳案號格式化成異常通報顯示案號-> 民國年 + 分行名稱+(兆)授字第+末五碼流水號
	 * 
	 * @param 上傳案號格式
	 *            (102005LMS00066)
	 * @return 民國年 + 分行名稱+(兆)授字第+末五碼流水號
	 */
	public static String convertDocumentNoToCaseNo(BranchService branch,
			String projNo) {
		if (Util.isNotEmpty(projNo)) {
			String cYear = (LMSUtil.checkSubStr(projNo, 0, 3)) ? projNo
					.substring(0, 3) : projNo;
			String brno = (LMSUtil.checkSubStr(projNo, 3, 6)) ? projNo
					.substring(3, 6) : projNo;
			String brnoName = branch.getBranchName(brno);
			brnoName = brnoName.replace("分行", UtilConstants.Mark.SPACE);

			int projNoLen = projNo.length();
			String caseSeq = "";
			if (projNoLen < 14) {
				caseSeq = (LMSUtil.checkSubStr(projNo, projNo.length() - 3)) ? projNo
						.substring(projNo.length() - 3) : projNo;
			} else {
				caseSeq = (LMSUtil.checkSubStr(projNo, projNo.length() - 5)) ? projNo
						.substring(projNo.length() - 5) : projNo;
			}

			projNo = StrUtils.concat(cYear, brnoName, "(兆)授字第", caseSeq, "號");
		}
		return projNo;
	}

	public static boolean lackBlackCode(List<L120S09A> l120s09a_list) {
		for (L120S09A l120s09a : l120s09a_list) {
			if (Util.equals(Util.trim(l120s09a.getBlackListCode()), "")) {
				return true;
			}
		}
		return false;
	}

	public static Date maxQueryDateS(List<L120S09A> l120s09a_list) {
		Date r = null;
		for (L120S09A l120s09a : l120s09a_list) {
			if (r == null) {
				r = l120s09a.getQueryDateS();
			}
			if (r != null && l120s09a.getQueryDateS() != null
					&& cmpDate(r, ">", l120s09a.getQueryDateS())) {
				r = l120s09a.getQueryDateS();
			}
		}
		return r;
	}

	public static List<Map<String, String>> setL120S09aData(
			List<Map<String, String>> titleRows, List<L120S09A> listL120s09a,
			Map<String, String> blackListCodeMap,
			Map<String, String> custRelationMap, Properties prop,
			Map<String, String> rptVariableMap, String printFactoringTimeStr) {

		int totCount = listL120s09a.size();
		int count = 0;
		for (L120S09A l120s09a : listL120s09a) {
			count = count + 1;
			titleRows = _setL120S09aDetailData(titleRows, l120s09a,
					blackListCodeMap, custRelationMap, count, totCount, prop);

		}

		// J-106-0057-001 Web e-Loan授信管理系統新增「應收帳款承購無追索權-買方黑名單查詢」功能
		String isFactoring_Without_Recourse_Buyer_Status = "3"; // 預設為不適用
		Timestamp chkTime = null;
		// J-113-0082 配合法務部新規，於AML頁籤新增引入「受告誡處分」資訊 
		StringBuilder cmfwarnpResults_1 = new StringBuilder(); //查詢結果=1(有)
		StringBuilder cmfwarnpResults_3 = new StringBuilder(); //查詢結果=3(不適用)
		Properties lmss20a_pop = MessageBundleScriptCreator
							.getComponentResource(LMSS20APanel.class);
		for (L120S09A l120s09a : listL120s09a) {
			if (chkTime == null) {
				chkTime = l120s09a.getUpdateTime();
				if (chkTime == null) {
					chkTime = l120s09a.getCreateTime();
				}
			}

			String custRelation = l120s09a.getCustRelation();
			if (Util.notEquals(isFactoring_Without_Recourse_Buyer_Status, "1")) {
				String blackListCode = Util.trim(l120s09a.getBlackListCode());
				String[] prorertyArray = custRelation.split(",");
				for (String data : prorertyArray) {
					if (UtilConstants.Casedoc.L120s09aBlackListCtlTarget.應收帳款買方無追索
							.equals(data)) {
						if (Util.equals(
								blackListCode,
								UtilConstants.Casedoc.L120s09aBlackListCode.是黑名單)) {
							isFactoring_Without_Recourse_Buyer_Status = "1"; // 有

						} else if (Util
								.equals(blackListCode,
										UtilConstants.Casedoc.L120s09aBlackListCode.可能是黑名單)) {
							isFactoring_Without_Recourse_Buyer_Status = "1"; // 有

						} else if (Util
								.equals(blackListCode,
										UtilConstants.Casedoc.L120s09aBlackListCode.未列於黑名單)) {
							isFactoring_Without_Recourse_Buyer_Status = "2"; // 無
						}
						break;
					}
				}
			}
			
			// J-113-0082 配合法務部新規，於AML頁籤新增引入「受告誡處分」資訊 
			if(UtilConstants.Casedoc.L120s09aCmfwarnpResultCode.有
					.equals(Util.trim(l120s09a.getCmfwarnpResult()))){
				if(Util.isNotEmpty(cmfwarnpResults_1)){//有超過1筆
					cmfwarnpResults_1.append("</br>");
				}
				cmfwarnpResults_1.append(MessageFormat.format(
						lmss20a_pop.getProperty("L120S09a.cmfwarnpResult.Msg2"),
						Util.trim(l120s09a.getCustId()),
						Util.trim(l120s09a.getCustName()),
						Util.trim(l120s09a.getCmfwarnpQueryResultInfo())));
			}
			if(UtilConstants.Casedoc.L120s09aCmfwarnpResultCode.不適用
					.equals(Util.trim(l120s09a.getCmfwarnpResult()))){
				if(Util.isNotEmpty(cmfwarnpResults_3)){//有超過1筆
					cmfwarnpResults_3.append("</br>");
					
				}
				cmfwarnpResults_3.append(MessageFormat.format(
						lmss20a_pop.getProperty("L120S09a.cmfwarnpResult.Msg4"),
						Util.trim(l120s09a.getCustId()),
						Util.trim(l120s09a.getCustName()),
						(l120s09a.getCmfwarnpQueryTime() == null ? "" : 
							CapDate.formatDate(l120s09a.getCmfwarnpQueryTime(),UtilConstants.DateFormat.YYYY_MM_DD) )));
			}

		}

		// J-106-0057-001 Web e-Loan授信管理系統新增「應收帳款承購無追索權-買方黑名單查詢」功能
		// FACTORINGWITHOUTRECOURSEBUYER
		// 1=有 2=無 3=不適用
		if (chkTime != null) {

			Timestamp startTime = CapDate.getCurrentTimestamp();

			if (Util.notEquals(printFactoringTimeStr, "")) {
				startTime = CapDate
						.convertStringToTimestamp(printFactoringTimeStr);
			}

			if (!chkTime.before(startTime)) {
				rptVariableMap
						.put("FACTORINGWITHOUTRECOURSEBUYER",
								Util.trim(showYNPic_L120S09A(
										Util.trim(isFactoring_Without_Recourse_Buyer_Status),
										prop)));// 黑名單查詢結果
			} else {
				rptVariableMap.put("FACTORINGWITHOUTRECOURSEBUYER", "");// 黑名單查詢結果
			}
		} else {
			rptVariableMap.put("FACTORINGWITHOUTRECOURSEBUYER", "");// 黑名單查詢結果
		}
		
		// J-113-0082 配合法務部新規，於AML頁籤新增引入「受告誡處分」資訊
		StringBuilder cmfwarnpResultDesc = new StringBuilder();
		if (Util.isNotEmpty(cmfwarnpResults_1)) {
			cmfwarnpResultDesc.append(lmss20a_pop.getProperty("L120S09a.cmfwarnpResult.Msg1"))
				.append("</br>").append(cmfwarnpResults_1.toString());
		}
		if (Util.isNotEmpty(cmfwarnpResults_3)) {
			if(Util.isNotEmpty(cmfwarnpResultDesc)){
				cmfwarnpResultDesc.append("</br>");
			}
			cmfwarnpResultDesc.append(lmss20a_pop.getProperty("L120S09a.cmfwarnpResult.Msg3"))
				.append("</br>").append(cmfwarnpResults_3.toString());
		}
		
		rptVariableMap.put("L120S09A.CmfwarnpResultDesc", cmfwarnpResultDesc.toString());// 受告誡處分查詢結果

		return titleRows;
	}

	private static String showYNPic_L120S09A(String type, Properties prop) {
		StringBuffer str = new StringBuffer();
		if ("1".equals(type)) {
			str.append("■");
		} else {
			str.append("□");
		}
		str.append(prop.getProperty("COMMON3.Y"));
		if ("2".equals(type)) {
			str.append("■");
		} else {
			str.append("□");
		}
		str.append(prop.getProperty("COMMON3.N"));
		if ("3".equals(type)) {
			str.append("■");
		} else {
			str.append("□");
		}
		// AML.NONFACTORING=非應收帳款承購業務
		str.append(prop.getProperty("COMMON3.K") + "("
				+ (prop.getProperty("AML.NONFACTORING")) + ")");
		return str.toString();
	}

	private static List<Map<String, String>> _setL120S09aDetailData(
			List<Map<String, String>> titleRows, L120S09A l120s09a,
			Map<String, String> blackListCodeMap,
			Map<String, String> custRelationMap, int count, int totCount,
			Properties prop_CLS1141R01RptServiceImpl) {
		Map<String, String> map = Util.setColumnMap();

		map.put("ReportBean.column01",
				Util.nullToSpace(l120s09a.getCustId() + l120s09a.getDupNo()));// ID
		map.put("ReportBean.column02", Util.nullToSpace(l120s09a.getCustName()));// 戶名

		map.put("ReportBean.column03",
				Util.nullToSpace(l120s09a.getCustEName()));// 英文戶名

		StringBuilder sb = new StringBuilder();
		String[] newItem = Util.trim(l120s09a.getCustRelation()).split(",");
		// 對陣列進行排序

		int i, j;
		String tmp;
		for (i = newItem.length - 1; i >= 0; i = i - 1) {
			for (j = 0; j < i; j = j + 1) {
				// if (newItem[j] > newItem[i])// 換（"小於"是由大到小）
				if (Util.parseInt((String) newItem[j]) > Util
						.parseInt((String) newItem[i]))// 換（"小於"是由大到小）
				{
					tmp = newItem[j];
					newItem[j] = newItem[i];
					newItem[i] = tmp;
				}
			}
		}

		for (String s : newItem) {
			if (sb.length() > 0)
				sb.append("、");
			sb.append(Util.trim(Util.trim(custRelationMap.get(s))));
		}
		map.put("ReportBean.column04", Util.nullToSpace(sb.toString()));// 相關身分

		map.put("ReportBean.column05", Util
				.nullToSpace(_setL120S09aShowYNPicAml(
						Util.nullToSpace(l120s09a.getBlackListCode()),
						prop_CLS1141R01RptServiceImpl)));// 黑名單查詢結果

		// J-107-0059-001 Web e-Loan 授信簽報書與動審表之AML頁籤及列印檢核表時，增加引進風險等級
		// 風險等級
		if (Util.notEquals(Util.trim(l120s09a.getLuvRiskLevel()), "")) {
			map.put("ReportBean.column06", Util
					.trim(prop_CLS1141R01RptServiceImpl
							.getProperty("AML.riskLvl_"
									+ Util.trim(l120s09a.getLuvRiskLevel()))));
		} else {
			map.put("ReportBean.column06", "");
		}

		if (Util.notEquals(Util.trim(l120s09a.getCountry()), "")) {
			map.put("ReportBean.column07", Util.trim(l120s09a.getCountry()));
		} else {
			map.put("ReportBean.column07", "");
		}

		// 控制標題列
		if (count == 1) {
			map.put("ReportBean.column15", "AML01");
		} else {
			map.put("ReportBean.column15", "");
		}

		// 控制結尾列
		if (count == totCount) {
			map.put("ReportBean.column16", "AML99");
		} else {
			map.put("ReportBean.column16", "");
		}

		titleRows.add(map);
		return titleRows;
	}

	private static BigDecimal _floor_1000(BigDecimal v) {
		return Arithmetic.div_floor(v, new BigDecimal(1000), 0).multiply(
				new BigDecimal(1000));
	}

	public static BigDecimal prod_rmRctAmt_upperLimit(BigDecimal sum_totLnAmt,
			int totalPeriod, RoundingMode roundingMode) {
		if (totalPeriod > 0) {
			return sum_totLnAmt.divide(new BigDecimal(totalPeriod), 0,
					roundingMode);
		}
		return BigDecimal.ZERO;
	}

	public static BigDecimal prod_rmRctAmt_upperLimit(BigDecimal sum_totLnAmt,
			int totalPeriod) {
		return prod_rmRctAmt_upperLimit(sum_totLnAmt, totalPeriod,
				RoundingMode.FLOOR);
	}

	public static BigDecimal prod_rmIntMax(BigDecimal rmRctAmt) {
		// 每月撥款金額的1/3(計算至千元，以下捨去)
		return _floor_1000(Arithmetic.div(rmRctAmt, new BigDecimal(3)));
	}

	private static String _setL120S09aShowYNPicAml(String type,
			Properties prop_CLS1141R01RptServiceImpl) {
		StringBuffer str = new StringBuffer();
		if ("02".equals(type)) {
			str.append("■");
		} else {
			str.append("□");
		}
		str.append(prop_CLS1141R01RptServiceImpl.getProperty("AML.CON02"));
		if ("04".equals(type)) {
			str.append("■");
		} else {
			str.append("□");
		}
		str.append(prop_CLS1141R01RptServiceImpl.getProperty("AML.CON04"));
		if ("00".equals(type)) {
			str.append("■");
		} else {
			str.append("□");
		}
		str.append(prop_CLS1141R01RptServiceImpl.getProperty("AML.CON00"));
		return str.toString();
	}

	public static String getUploadLocationCd(String LocationCd) {
		Integer No = Util.parseInt(Util.getRightStr(LocationCd, 2));
		String En = Util.getLeftStr(LocationCd, 1);
		if (No > 50) {
			if (Util.equals(En, "B")) {
				En = "L";
			} else if (Util.equals(En, "D")) {
				En = "R";
			} else if (Util.equals(En, "E")) {
				En = "S";
			}
			No = No - 50;
			LocationCd = En + Util.addZeroWithValue(No.toString(), 2);
		}
		return LocationCd;
	}

	public static void clean_unused_field_old_version(L140M01M l140m01m) {
		if (l140m01m == null) {
			return;
		}

		if (Util.equals(l140m01m.getCbcCase(), "1")) {
			l140m01m.setRealEstateLoanLimitReason("");
			l140m01m.setHouse_age(null);
			l140m01m.setLoanAmt(null);

		} else if (Util.equals(l140m01m.getCbcCase(), "2")) {

			l140m01m.setAppAmt(BigDecimal.ZERO);
			l140m01m.setNowAMT(BigDecimal.ZERO);
			// ======
			l140m01m.setHouse_age(null);
			l140m01m.setLoanAmt(null);
		} else if (Util.equals(l140m01m.getCbcCase(), "4")) {
			if (is_cls_prod_67(l140m01m)) {
				// 有 以房養老 要填寫的欄位
			} else {
				l140m01m.setCityId("");
				l140m01m.setAreaId("");
				l140m01m.setSit3No(null);
				l140m01m.setSit4No("");
				l140m01m.setAppAmt(BigDecimal.ZERO);
				l140m01m.setHouse_age(null);
				l140m01m.setLoanAmt(null);
			}

			l140m01m.setNowAMT(BigDecimal.ZERO);
			l140m01m.setValueAMT(BigDecimal.ZERO);

		} else if (Util.equals(l140m01m.getCbcCase(), "5")) {
			l140m01m.setRealEstateLoanLimitReason("");
			l140m01m.setHouse_age(null);
			l140m01m.setLoanAmt(null);
		}
	}

	public static void clean_unused_field_new_version(L140M01M l140m01m,
			String version) {
		if (l140m01m == null) {
			return;
		}

		if (Util.equals(l140m01m.getCbcCase(), "1")) {
			l140m01m.setHouse_age(null);
			l140m01m.setLoanAmt(null);

			if (!Arrays.asList(RealEstateLoanUtil.specificVersion).contains(
					l140m01m.getVersion())
					|| !"A".equals(l140m01m.getRealEstateLoanLimitReason())) {
				l140m01m.setIs3rdHignHouse(null);
			}

		} else if (Util.equals(l140m01m.getCbcCase(), "2")) {
			// ======
			l140m01m.setHouse_age(null);
			l140m01m.setLoanAmt(null);
		} else if (Util.equals(l140m01m.getCbcCase(), "4")) {
			if (is_cls_prod_67(l140m01m)) {
				// 有 以房養老 要填寫的欄位
			} else {
				l140m01m.setCityId("");
				l140m01m.setAreaId("");
				l140m01m.setSit3No(null);
				l140m01m.setSit4No("");
				l140m01m.setAppAmt(BigDecimal.ZERO);
				l140m01m.setHouse_age(null);
				l140m01m.setLoanAmt(null);
			}

			l140m01m.setNowAMT(BigDecimal.ZERO);
			l140m01m.setValueAMT(BigDecimal.ZERO);

		} else if (Util.equals(l140m01m.getCbcCase(), "5")) {
			l140m01m.setHouse_age(null);
			l140m01m.setLoanAmt(null);

			if (UtilConstants.L140m01mVersion.VERSION_20210319.equals(version)
					|| UtilConstants.L140m01mVersion.VERSION_20210924
							.equals(version)
					|| UtilConstants.L140m01mVersion.VERSION_20211217
							.equals(version)) {
				l140m01m.setRealEstateLoanLimitReason("");
			}
		}
	}

	public static BigDecimal fetch_BigDecimal_from_json(JSONObject data,
			String keyCol) {
		String val = Util.trim(data.optString(keyCol));
		if (Util.isEmpty(val)) {
			return null;
		} else {
			return Util.parseBigDecimal(val);
		}
	}

	public static String convert_bigvalue(BigDecimal val) {
		String r = "";
		if (val != null) {
			DecimalFormat df = new DecimalFormat("#,###");
			r = df.format(val);
		}
		return r;
	}

	public static boolean is_cls_prod_67(L140M01M l140m01m) {
		if (l140m01m == null) {

		} else {
			if (Util.equals(l140m01m.getCbcCase(), "4")
					&& Util.equals(l140m01m.getPlusReason(), "8")) {
				return true;
			}
		}
		return false;
	}

	public static String build_rskFlag_printing(Properties prop_CLS1021M01Page,
			C102M01A c102m01a) {
		String rptId = Util.trim(c102m01a.getRptId());
		String rskFlag = c102m01a.getRskFlag();
		String key = "";
		if (Util.equals(rptId, LMSUtil.C102M01A_RPTID_V202208)) {

		} else if (Util.equals(rptId, LMSUtil.C102M01A_RPTID_V20171231)) {
			if (Util.equals("3", rskFlag)) {
				key = "RSKFLAG.V20171231.3";
			} else if (Util.equals("4", rskFlag)) {
				key = "RSKFLAG.V20171231.4";
			} else if (Util.equals("1", rskFlag)) {
				key = "RSKFLAG.V20171231.1";
			} else if (Util.equals("2", rskFlag)) {
				key = "RSKFLAG.V20171231.2"; // 例如：店面的風險權數100%
			} else {
				key = "RSKFLAG.V20171231.EMPTY";
			}
		} else {
			if (Util.equals("1", rskFlag)) {
				key = "RSKFLAG.1";
			} else if (Util.equals("2", rskFlag)) {
				key = "RSKFLAG.2";
			} else {
				key = "RSKFLAG.EMPTY";
			}
		}

		if (Util.isEmpty(key)) {
			return "";
		} else {
			return Util.trim(prop_CLS1021M01Page.getProperty(key));
		}

	}

	public static boolean isAloanDate_LT_100_04_21(C102M01A c102m01a) {
		return isAloanDate_LT_100_04_21(c102m01a.getALoanDate());
	}

	public static boolean isAloanDate_LT_100_04_21(Date d) {
		if (d != null
				&& LMSUtil.cmpDate(d, "<", CapDate.parseDate("2011-04-21"))) {
			return true;
		}
		return false;
	}

	public static boolean since_20171231() {
		return _since_given_date("2017-12-31");
	}

	private static boolean _since_given_date(String activeDate) {
		if (LMSUtil.cmpDate(CapDate.getCurrentTimestamp(), ">=",
				CapDate.parseDate(activeDate))) {
			return true;
		}
		return false;
	}

	public static String get_C102M01A_RPTID_latestVersion() {
		if (_since_given_date("2022-08-01")) {
			return LMSUtil.C102M01A_RPTID_V202208;
		}

		if (since_20171231()) {
			return LMSUtil.C102M01A_RPTID_V20171231;
		}
		/*
		 * String activeDate = "2017-12-31"; if
		 * (LMSUtil.cmpDate(CapDate.getCurrentTimestamp(), ">=",
		 * CapDate.parseDate(activeDate))) { return
		 * LMSUtil.C102M01A_RPTID_V20171231; } else { // still null }
		 */
		return "";
	}

	/**
	 * 上傳至中心的風險權數，溯及既往
	 * 
	 * @param c102m01a
	 * @param own_house
	 * @param raw_ln_purpose
	 *            {由CLS1021M01FormHandler寫入,會有值; 但在額度明細表>產品資訊,空白}
	 * @return
	 */
	public static Integer build_ELF501_RISK_RATING(C102M01A c102m01a,
			String own_house, String raw_ln_purpose) {
		String rptId = Util.trim(c102m01a.getRptId());
		String rskFlag = Util.trim(c102m01a.getRskFlag());
		Date aLoanDate = c102m01a.getALoanDate();
		return build_ELF501_RISK_RATING(rptId, rskFlag, aLoanDate, own_house,
				raw_ln_purpose);
	}

	public static String build_rskFlag_from_riskRating(String rptId,
			String xxx_own_house, int raw_risk_rating, Date lnf030_loan_date,
			String lnf030_ln_purpose) {
		boolean ownHouse = Util.equals("Y", xxx_own_house);
		int risk_rating = 100;
		if (raw_risk_rating > 0) {
			// 有碰到 LNF033_OWN_HOUSE=N, 但 LNF033_RISK_RATING=0
			risk_rating = raw_risk_rating;
		}

		if (Util.equals(rptId, LMSUtil.C102M01A_RPTID_V202208)) {
			return "";
		} else if (Util.equals(rptId, LMSUtil.C102M01A_RPTID_V20171231)) {
			// 規則有[35% , 75% , 45% , 100%]
			if (isAloanDate_LT_100_04_21(lnf030_loan_date)) {
				if (ownHouse) {
					// (可能 經辦 選較差的風險權數)
					return (risk_rating <= 45 ? "3" : "1"); // 等同<75
				} else {
					return (risk_rating <= 75 ? "1" : "2");// 住宅vs店面
				}
			} else {
				if (ownHouse) {
					return (risk_rating <= 45 ? "3" : "4"); // 等同<75
				} else {
					return (risk_rating <= 75 ? "4" : "2");// 住宅vs店面
				}
			}
		} else {
			// 舊規則[只有45% , 100%]
			if (isAloanDate_LT_100_04_21(lnf030_loan_date)) {
				return "1";
			} else {
				if (ownHouse) {
					return (risk_rating <= 45 ? "1" : "2"); // 等同<75
				} else {
					return "2";
				}
			}
		}
	}

	public static Integer build_ELF501_RISK_RATING(String rptId,
			String rskFlag, Date aLoanDate, String own_house,
			String raw_ln_purpose) {
		/*
		 * 詢問 授管處 家玲, 若作店面使用, 融資業務分類 也可能選 L-購置住宅貸款（非自用） 所以, 不能用 融資業務分類
		 * 是否在[M,L,2]來判斷
		 */
		if (Util.equals(LMSUtil.get_C102M01A_RPTID_latestVersion(),
				LMSUtil.C102M01A_RPTID_V202208)) {
			return -1;
		} else if (Util.equals(LMSUtil.get_C102M01A_RPTID_latestVersion(),
				LMSUtil.C102M01A_RPTID_V20171231)) {
			if (Util.equals(rptId, LMSUtil.C102M01A_RPTID_V202208)) {
				return -1;
			} else if (Util.equals(rptId, LMSUtil.C102M01A_RPTID_V20171231)) {
				// 〔2017-12-31 ~ 9999-12-31 〕
				if (Util.equals(rskFlag, "3")) {
					return 35;
				} else if (Util.equals(rskFlag, "4")) {
					return 75;
				} else if (Util.equals(rskFlag, "1")) {
					return 45;
				} else if (Util.equals(rskFlag, "2")) {
					return 100;
				}
			} else {
				// 〔e-loan 上線 ~ 2017-12-30〕舊案
				// 之前簽案，但在2017-12-31後才做動審，應該要套用新規則
				if (LMSUtil.isAloanDate_LT_100_04_21(aLoanDate) == false) {
					// 目前首撥日>=100_04_21
					if (Util.equals("Y", own_house)) {
						return Util.equals(rskFlag, "1") ? 35 : 75;
					} else {
						return 75;
					}
				} else {
					// <100_04_21
					return Util.equals("Y", own_house) ? 35 : 45;
				}
			}
		} else {
			if (Util.equals(rskFlag, "1")) {
				return 45;
			} else if (Util.equals(rskFlag, "2")) {
				return 100;
			}
		}
		return 100;
	}

	public static Map<String, String> get_C102M01A_checkMsg(String rptId,
			String selfCheck, Date aloanDate, String rskFlag,
			Properties prop_cls1021m01, String splitSign) {
		List<String> prompt_list = new ArrayList<String>();
		List<String> error_list = new ArrayList<String>();
		boolean isAloanDate_LT_100_04_21 = isAloanDate_LT_100_04_21(aloanDate);

		// 風險權數{1：45% , 2：100% , 3：35% , 4：75%}
		if (Util.equals(rptId, LMSUtil.C102M01A_RPTID_V202208)) {

		} else if (Util.equals(rptId, LMSUtil.C102M01A_RPTID_V20171231)) {

			if (Util.equals("Y", selfCheck)
					&& isAloanDate_LT_100_04_21 == false
					&& Util.equals("4", rskFlag)) {
				// 提示 是否合理(可以35卻手動改75)
				prompt_list.add(prop_cls1021m01
						.getProperty("C102M01A.tReCheckData.V20171231"));
			}
			// ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			if (Util.equals("N", selfCheck) && Util.equals("3", rskFlag)) {
				// 非自用住宅,不應採用 "較低的風險權數35%"
				// p.s. 不分首撥日
				error_list.add(prop_cls1021m01.getProperty("C102M01A.selfChkN")
						+ "，"
						+ prop_cls1021m01
								.getProperty("C102M01A.tReCheckData.ERR3"));
			}

			if (isAloanDate_LT_100_04_21 && Util.equals("4", rskFlag)) {
				// 首撥日期在2011-04-21之前, 皆為1:45%(不可選75%)
				error_list.add(prop_cls1021m01
						.getProperty("C102M01A.aLoanDate_LT")
						+ prop_cls1021m01
								.getProperty("C102M01A.tReCheckData.ERR4"));
			}
			// ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			if (Util.equals("1", rskFlag)) {
				if (Util.equals("N", selfCheck) && isAloanDate_LT_100_04_21) {
					// ok
				} else if (isAloanDate_LT_100_04_21 == false) {
					// 首撥日期在2011-04-21以後，風險權數不應為45%
					error_list.add(prop_cls1021m01
							.getProperty("C102M01A.aLoanDate_GE")
							+ "，"
							+ prop_cls1021m01
									.getProperty("C102M01A.tReCheckData.ERR1"));
				} else if (Util.equals("Y", selfCheck)
						&& isAloanDate_LT_100_04_21) {
					// 首撥日期在2011-04-21之前自用住宅，風險權數不應為45%
					// p.s. 由 35% 改 45%, 可能可以
					prompt_list.add(prop_cls1021m01
							.getProperty("C102M01A.aLoanDate_LT")
							+ prop_cls1021m01.getProperty("C102M01A.selfChkY")
							+ "，"
							+ prop_cls1021m01
									.getProperty("C102M01A.tReCheckData.ERR1"));
				}
			}
		} else {
			if (Util.equals("3", rskFlag)) {
				error_list.add(prop_cls1021m01
						.getProperty("C102M01A.tReCheckData.ERR3")); // 尚未生效
			} else if (Util.equals("4", rskFlag)) {
				error_list.add(prop_cls1021m01
						.getProperty("C102M01A.tReCheckData.ERR4")); // 尚未生效
			}
			// =============
			// 只有45, 100可選

			if (Util.equals("Y", selfCheck)
					&& isAloanDate_LT_100_04_21 == false
					&& Util.equals("2", rskFlag)) {
				// 提示 是否合理 (可以45卻手動改100)
				prompt_list.add(prop_cls1021m01
						.getProperty("C102M01A.tReCheckData"));
			}
			// ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			if (isAloanDate_LT_100_04_21 && Util.equals("2", rskFlag)) {
				// 首撥日期在2011-04-21之前風險權數不應為100%
				// p.s. 不論[自用住宅, 非自用住宅]
				error_list.add(prop_cls1021m01
						.getProperty("C102M01A.aLoanDate_LT")
						+ prop_cls1021m01
								.getProperty("C102M01A.tReCheckData.ERR2"));
			}
			// ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			if (Util.equals("N", selfCheck)
					&& isAloanDate_LT_100_04_21 == false
					&& Util.equals("1", rskFlag)) {
				// 非自用住宅，風險權數不應為45%
				error_list.add(prop_cls1021m01.getProperty("C102M01A.selfChkN")
						+ "，"
						+ prop_cls1021m01
								.getProperty("C102M01A.tReCheckData.ERR1"));
			}
		}
		// ==============================
		Map<String, String> map = new HashMap<String, String>();
		if (error_list.size() > 0) {
			map.put("E", StringUtils.join(error_list, splitSign));
		} else {
			if (prompt_list.size() > 0) {
				map.put("C", StringUtils.join(prompt_list, splitSign));
			}
		}
		return map;
	}

	public static final String C102M01A_RPTID_V20171231 = "V20171231";
	public static final String C102M01A_RPTID_V202208 = "V202208"; // J-111-0096
																	// Web
																	// e-Loan消金因應不動產暴險以貸放比率(LTV)決定適用之風險權數，消金個人戶授信案件免填「風險權數」(取消現行系統要求風險權數影響數評估)

	public static String getRemoteHost(HttpServletRequest req) {
		return req.getRemoteHost();
	}

	/**
	 * 如傳入參數為 null 也可通過檢核。 當在 簽報書 引入 借款人 這一關, 去擋 jcicQDate!=null <br/>
	 * 遇到 {S-擔保品提供人, 團貸母戶公司戶}, 也要能處理
	 * 
	 * @param jcicQDate
	 * @param etchQDate
	 * @return
	 */
	public static boolean qdate_expired(Date jcicQDate, Date etchQDate) {
		String dateOfMonthsAgo = "";
		{
			Date today = new Date();
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(today);
			calendar.add(Calendar.MONTH, -1);
			TWNDate.toAD(calendar.getTime());
			dateOfMonthsAgo = TWNDate.toAD(calendar.getTime());
		}

		String ejcic = dateOfMonthsAgo;
		String echk = dateOfMonthsAgo;
		if (jcicQDate != null) {
			ejcic = TWNDate.toAD(jcicQDate);
		}
		if (etchQDate != null) {
			echk = TWNDate.toAD(etchQDate);
		}

		if (ejcic.compareTo(dateOfMonthsAgo) < 0
				|| echk.compareTo(dateOfMonthsAgo) < 0) {
			return true;
		}
		return false;
	}

	/**
	 * 取得評等訊息, 若 type==TYPE_RAW => 原本的訊息<br/>
	 * 若 type==TYPE_RPT => 原本的訊息+{ 應呈報總處核准 or 若敘做「非十足擔保」授信案件時，應呈報總處核准}<br/>
	 * 若 type==TYPE_UI => color_red(訊息TYPE_RPT)
	 * 
	 * @param model
	 * @return
	 */
	public static String getGradeMessage(GenericBean model_GorQ,
			String markModel, int type, String bailout_flag,
			String checkItemRange) {
		if (model_GorQ != null) {
			try {
				if (Util.equals(Util.trim(checkItemRange), "")) {
					checkItemRange = UtilConstants.CheckItemRange.defult;
				}
				return _getGradeMessage(DataParse.toJSON(model_GorQ),
						markModel, type, bailout_flag, checkItemRange);
			} catch (CapException e) {
				logger.error("取得評等訊息 發生錯誤", e);
			}
		}
		return null;
	}

	/**
	 * 取得評等訊息
	 * 
	 * @param json
	 * @return
	 */
	private static String _getGradeMessage(JSONObject json, String markModel,
			int type, String bailout_flag, String checkItemRange) {
		List<String> list = new ArrayList<String>();
		Properties prop_AbstractOverSeaCLSPage = MessageBundleScriptCreator
				.getComponentResource(AbstractOverSeaCLSPage.class);
		if (json != null && prop_AbstractOverSeaCLSPage != null) {
			boolean match1to2 = false;
			boolean match3to5 = false;
			boolean match6to8 = false;
			if (Util.equals(UtilConstants.L140S02AModelKind.房貸, markModel)) {
				// 第1~2項
				if (UtilConstants.haveNo.有.equals(Util.trim(json
						.get("chkItem1")))) {
					list.add(Util.trim(prop_AbstractOverSeaCLSPage
							.getProperty("C101S01G.chkItem1")));
					match1to2 = true;
				}
				if (UtilConstants.haveNo.有.equals(Util.trim(json
						.get("chkItem2")))) {
					list.add(Util.trim(prop_AbstractOverSeaCLSPage
							.getProperty("C101S01G.chkItem2")));
					match1to2 = true;
				}
				// 第3~5項
				if (UtilConstants.haveNo.有.equals(Util.trim(json
						.get("chkItem3")))) {
					list.add(Util.trim(prop_AbstractOverSeaCLSPage
							.getProperty("C101S01G.chkItem3")));
					match3to5 = true;
				}
				if (UtilConstants.haveNo.有.equals(Util.trim(json
						.get("chkItem4")))) {
					list.add(Util.trim(prop_AbstractOverSeaCLSPage
							.getProperty("C101S01G.chkItem4")));
					match3to5 = true;
				}
				if (UtilConstants.haveNo.有.equals(Util.trim(json
						.get("chkItem5")))) {
					list.add(Util.trim(prop_AbstractOverSeaCLSPage
							.getProperty("C101S01G.chkItem5")));
					match3to5 = true;
				}
				// 第6~8項
				if (UtilConstants.haveNo.有.equals(Util.trim(json
						.get("chkItem6")))) {
					list.add(Util.trim(prop_AbstractOverSeaCLSPage
							.getProperty("C101S01G.chkItem6")));
					match6to8 = true;
				}
				if (UtilConstants.haveNo.有.equals(Util.trim(json
						.get("chkItem7")))) {
					list.add(Util.trim(prop_AbstractOverSeaCLSPage
							.getProperty("C101S01G.chkItem7")));
					match6to8 = true;
				}
				if (UtilConstants.haveNo.有.equals(Util.trim(json
						.get("chkItem8")))) {
					list.add(Util.trim(prop_AbstractOverSeaCLSPage
							.getProperty("C101S01G.chkItem8")));
					match6to8 = true;
				}
			} else if (Util.equals(UtilConstants.L140S02AModelKind.非房貸,
					markModel)) { // C101S01G.chkItem1 vs C101S01Q.chkItem1
				// 第1~2項
				if (UtilConstants.haveNo.有.equals(Util.trim(json
						.get("chkItem1")))) {
					list.add(Util.trim(prop_AbstractOverSeaCLSPage
							.getProperty("C101S01Q.chkItem1")));
					match1to2 = true;
				}
				if (UtilConstants.haveNo.有.equals(Util.trim(json
						.get("chkItem2")))) {
					list.add(Util.trim(prop_AbstractOverSeaCLSPage
							.getProperty("C101S01Q.chkItem2")));
					match1to2 = true;
				}
				// 第3~5項
				if (UtilConstants.haveNo.有.equals(Util.trim(json
						.get("chkItem3")))) {
					list.add(Util.trim(prop_AbstractOverSeaCLSPage
							.getProperty("C101S01Q.chkItem3")));
					match3to5 = true;
				}
				if (UtilConstants.haveNo.有.equals(Util.trim(json
						.get("chkItem4")))) {
					list.add(Util.trim(prop_AbstractOverSeaCLSPage
							.getProperty("C101S01Q.chkItem4")));
					match3to5 = true;
				}
				if (UtilConstants.haveNo.有.equals(Util.trim(json
						.get("chkItem5")))) {
					list.add(Util.trim(prop_AbstractOverSeaCLSPage
							.getProperty("C101S01Q.chkItem5")));
					match3to5 = true;
				}
				// 第6~8項
				if (UtilConstants.haveNo.有.equals(Util.trim(json
						.get("chkItem6")))) {
					list.add(Util.trim(prop_AbstractOverSeaCLSPage
							.getProperty("C101S01Q.chkItem6")));
					match6to8 = true;
				}
				if (UtilConstants.haveNo.有.equals(Util.trim(json
						.get("chkItem7")))) {
					list.add(Util.trim(prop_AbstractOverSeaCLSPage
							.getProperty("C101S01Q.chkItem7")));
					match6to8 = true;
				}
				if (UtilConstants.haveNo.有.equals(Util.trim(json
						.get("chkItem8")))) {
					list.add(Util.trim(prop_AbstractOverSeaCLSPage
							.getProperty("C101S01Q.chkItem8")));
					match6to8 = true;
				}
			} else if (Util.equals(UtilConstants.L140S02AModelKind.卡友貸,
					markModel)) {
				// 第1~2項
				if (UtilConstants.haveNo.有.equals(Util.trim(json
						.get("chkItem1")))) {
					list.add(Util.trim(prop_AbstractOverSeaCLSPage
							.getProperty("C101S01R.chkItem1")));
					match1to2 = true;
				}
				if (UtilConstants.haveNo.有.equals(Util.trim(json
						.get("chkItem2")))) {
					list.add(Util.trim(prop_AbstractOverSeaCLSPage
							.getProperty("C101S01R.chkItem2")));
					match1to2 = true;
				}
				// 第3~5項
				if (UtilConstants.haveNo.有.equals(Util.trim(json
						.get("chkItem3")))) {
					list.add(Util.trim(prop_AbstractOverSeaCLSPage
							.getProperty("C101S01R.chkItem3")));
					match3to5 = true;
				}
				if (UtilConstants.haveNo.有.equals(Util.trim(json
						.get("chkItem4")))) {
					list.add(Util.trim(prop_AbstractOverSeaCLSPage
							.getProperty("C101S01R.chkItem4")));
					match3to5 = true;
				}
				if (UtilConstants.haveNo.有.equals(Util.trim(json
						.get("chkItem5")))) {
					list.add(Util.trim(prop_AbstractOverSeaCLSPage
							.getProperty("C101S01R.chkItem5")));
					match3to5 = true;
				}
				// 第6~8項
				if (UtilConstants.haveNo.有.equals(Util.trim(json
						.get("chkItem6")))) {
					list.add(Util.trim(prop_AbstractOverSeaCLSPage
							.getProperty("C101S01R.chkItem6")));
					match6to8 = true;
				}
				if (UtilConstants.haveNo.有.equals(Util.trim(json
						.get("chkItem7")))) {
					list.add(Util.trim(prop_AbstractOverSeaCLSPage
							.getProperty("C101S01R.chkItem7")));
					match6to8 = true;
				}
				if (UtilConstants.haveNo.有.equals(Util.trim(json
						.get("chkItem8")))) {
					list.add(Util.trim(prop_AbstractOverSeaCLSPage
							.getProperty("C101S01R.chkItem8")));
					match6to8 = true;
				}
			}
			boolean matchMajor = false;
			boolean matchMinor = false;

			if (checkItemRange.equals("1to2")) {
				if (match1to2) {
					matchMajor = true;
				} else if (match3to5 || match6to8) {
					matchMinor = true;
				}
			} else if (checkItemRange.equals("1to5")) {
				if (match1to2 || match3to5) {
					matchMajor = true;
				} else if (match6to8) {
					matchMajor = true;
				}
			} else if (checkItemRange.equals("1to8")) {
				if (match1to2 || match3to5 || match6to8) {
					matchMajor = true;
				}
			}
			OverSeaUtil.proc_nega_msg(list, matchMajor, matchMinor, type,
					bailout_flag);
		}
		return StringUtils.join(list, EloanConstants.HTML_NEWLINE);
	}

	public static String getKCS003_J10(Integer j10) {
		if (j10 == null) {
			return "";
		}
		if (j10 == 0) {
			return "無法評分";
		} else if (j10 == 200) {
			return "固定評分";
		} else {
			return j10 + "分";
		}
	}

	public static String getKCS003Reason(GenericBean model_R, String joinStr) {
		String default_str = "無";

		if (model_R != null) {
			String key1 = "";
			String key2 = "";
			String key3 = "";
			String key4 = "";
			String keyAddl = "";
			String desc1 = "";
			String desc2 = "";
			String desc3 = "";
			String desc4 = "";
			String descAddl = "";
			try {
				JSONObject json = DataParse.toJSON(model_R);
				key1 = Util.trim(json.get(ScoreCardLoan.column.KCS003理由代碼一));
				key2 = Util.trim(json.get(ScoreCardLoan.column.KCS003理由代碼二));
				key3 = Util.trim(json.get(ScoreCardLoan.column.KCS003理由代碼三));
				key4 = Util.trim(json.get(ScoreCardLoan.column.KCS003理由代碼四));
				keyAddl = Util
						.trim(json.get(ScoreCardLoan.column.KCS003附加理由代碼));

				desc1 = Util.trim(json.get(ScoreCardLoan.column.KCS003理由說明一));
				desc2 = Util.trim(json.get(ScoreCardLoan.column.KCS003理由說明二));
				desc3 = Util.trim(json.get(ScoreCardLoan.column.KCS003理由說明三));
				desc4 = Util.trim(json.get(ScoreCardLoan.column.KCS003理由說明四));
				descAddl = Util.trim(json
						.get(ScoreCardLoan.column.KCS003附加理由說明));
			} catch (CapException e) {
				logger.error("取得KCS003Reason訊息 發生錯誤", e);
			}
			String msg1 = _KCS003_msg(key1, desc1);
			String msg2 = _KCS003_msg(key2, desc2);
			String msg3 = _KCS003_msg(key3, desc3);
			String msg4 = _KCS003_msg(key4, desc4);
			String msgAddl = _KCS003_msg(keyAddl, descAddl);
			List<String> list = new ArrayList<String>();
			if (Util.isNotEmpty(msg1)) {
				list.add(msg1);
			}
			if (Util.isNotEmpty(msg2)) {
				list.add(msg2);
			}
			if (Util.isNotEmpty(msg3)) {
				list.add(msg3);
			}
			if (Util.isNotEmpty(msg4)) {
				list.add(msg4);
			}
			if (Util.isNotEmpty(msgAddl)) {
				list.add(msgAddl);
			}
			if (list.size() == 0) {
				return default_str;
			}
			return StringUtils.join(list, joinStr);
		}
		return "";
	}

	private static String _KCS003_msg(String key, String val) {
		if (Util.isNotEmpty(key) || Util.isNotEmpty(val)) {
			return key + "：" + val;
		}
		return key + val;
	}

	/**
	 * 
	 * 檢核性質是否有包含該值
	 * 
	 * <pre>
	 * @param value
	 *            被 | 劃分的值
	 * @param checkData
	 *            要檢查的值
	 * 
	 *            ex: isContainValue("1"|"2", "1") = true
	 * @return true 包含 false 不包含
	 * </pre>
	 */
	public static boolean isContainValue(String value, String checkData,
			String splitMark) {
		if (Util.isEmpty(value)) {
			return false;
		}

		if (Util.equals(Util.trim(splitMark), "")) {
			splitMark = UtilConstants.Mark.SPILT_MARK;
		}

		String[] prorertyArray = value.split(splitMark);
		for (String data : prorertyArray) {
			if (checkData.equals(data)) {
				return true;
			}
		}
		return false;
	}

	/**
	 * 取得格式化後篩選條件
	 * 
	 * @param jsonStr
	 * @return
	 */
	public static String getFormatItemDscr(String jsonStr, Properties prop,
			Map<String, String> typCdMap,
			Map<String, String> l120M01A_docTypeMap,
			Map<String, String> l120M01A_docKindMap,
			Map<String, String> l120M01A_docCodeMap) {
		// 組查詢條件字串

		StringBuffer itemDscrBrff = new StringBuffer("");
		if (Util.notEquals(Util.trim(jsonStr), "")) {
			JSONObject filterForm = JSONObject.fromObject(Util.trim(jsonStr));

			Iterator keys = filterForm.keys();

			while (keys.hasNext()) {
				String strKey = (String) keys.next();
				if (Util.equals(strKey, "fxCurr")
						|| Util.equals(strKey, "fxLnSubject")
						|| Util.equals(strKey, "fxCollateral1")
						|| Util.equals(strKey, "fxRateText1")
						// J-112-0449_05097_B1003 Web
						// e-Loan企金額度明細表新增主要用途查詢條件
						|| Util.equals(strKey, "fxBldUse")
						// 以下為個金
						|| Util.equals(strKey, "fxProdKind")
						|| Util.equals(strKey, "fxLnSubjectCls")
						|| Util.equals(strKey, "fxRateTextCls")) {
					// 不印
				} else {
					String strVal = Util.trim(filterForm.optString(strKey, ""));
					if (Util.notEquals(strVal, "")) {

						if (Util.equals(strKey, "typCd")) {
							strVal = Util.nullToSpace(typCdMap.get(strVal));
						}

						if (Util.equals(strKey, "docType")) {
							strVal = Util.nullToSpace(l120M01A_docTypeMap
									.get(strVal));
						}

						if (Util.equals(strKey, "docKind")) {
							strVal = Util.nullToSpace(l120M01A_docKindMap
									.get(strVal));
						}

						if (Util.equals(strKey, "docCode")) {
							strVal = Util.nullToSpace(l120M01A_docCodeMap
									.get(strVal));
						}

						if (Util.equals(strKey, "fxCrKind")) {
							strVal = prop.getProperty("L784M01A.fxCrKind_"
									+ strVal);
						}

						if (Util.equals(strKey, "fxGuarantor")) {
							if (Util.equals(strVal, "Y")) {
								strVal = "有";
							}

							if (Util.equals(strVal, "N")) {
								strVal = "無";
							}
						}

						if (Util.equals(strKey, "fxIsCls")) {
							if (Util.equals(strVal, "Y")) {
								strVal = "是";
							}

							if (Util.equals(strVal, "N")) {
								strVal = "否";
							}
						}

						itemDscrBrff.append(prop.getProperty("L784M01A."
								+ strKey)
								+ ":" + strVal);
						itemDscrBrff.append("<br>");
					}
				}

			}
		}

		return itemDscrBrff.toString();
	}

	public static String gen_type_subType_l140m01t_key(L140M01T l140m01t) {
		String type = Util.trim(l140m01t.getEstateType());
		String subType = Util.trim(l140m01t.getEstateSubType());
		String key = type;
		if (Util.isNotEmpty(subType)) {
			key = type + "_" + subType;
		}
		return key;
	}

	public static Set<String> gen_type_subType_l140m01t_key(
			List<L140M01T> l140m01t_list) {
		Set<String> r = new HashSet<String>();
		for (L140M01T l140m01t : l140m01t_list) {
			String key = gen_type_subType_l140m01t_key(l140m01t);
			r.add(key);
		}
		return r;
	}

	public static Map<String, List<L140M01T>> gen_type_subType_l140m01t_map(
			List<L140M01T> l140m01t_list) {
		Map<String, List<L140M01T>> r = new HashMap<String, List<L140M01T>>();
		for (L140M01T l140m01t : l140m01t_list) {
			String key = gen_type_subType_l140m01t_key(l140m01t);
			if (!r.containsKey(key)) {
				r.put(key, new ArrayList<L140M01T>());
			}
			r.get(key).add(l140m01t);
		}
		return r;
	}

	public static String estateType_estateSubType_desc(String estateType,
			String estateSubType, Map<String, String> m_type,
			Map<String, String> m_subtype) {
		String p1 = estateType;
		String p2 = estateSubType;

		if (true) {
			String p1_desc = getDesc(m_type, estateType);
			if (!Util.equals(p1, p1_desc)) {
				p1 = (p1 + " " + p1_desc);
			}
		}
		if (Util.isNotEmpty(estateSubType)) {
			p2 = getDesc(m_subtype, estateSubType);
		}
		if (Util.isEmpty(p2)) {
			return p1;
		} else {
			return p1 + "-" + p2;
		}
	}

	/**
	 * 參考 LMSServiceImpl :: checkCaseIs72_2 ( ... ) 另在
	 * LMSUtil.needCheck72_2(...) 已排除[性質=取消]
	 */
	public static String is722_cls_only(List<L140M01T> l140m01ts) {
		if (l140m01ts.size() == 0) {
			return "";
		}

		// 只要任一筆資料不完整
		for (L140M01T data : l140m01ts) {
			String checkYN = data.getCheckYN();
			// 資料如果輸入未完整，就不做檢核
			if ("N".equals(checkYN)) {
				return "";
			}
		}

		int is722_Y = 0;
		int is722_N = 0;
		for (L140M01T data : l140m01ts) {
			if (UtilConstants.L140M01T_estatType.都更危老.equals(data
					.getEstateType())) {
				// 重建類型001
				// 只要不是都更，危老，都屬72-2
				if (UtilConstants.L140M01T_estatSubType.一般.equals(data
						.getEstateSubType())
						|| UtilConstants.L140M01T_estatSubType.其它都更危老
								.equals(data.getEstateSubType())) {
					++is722_Y;
				} else if (UtilConstants.L140M01T_estatSubType.危老.equals(data
						.getEstateSubType())) {
					// 2.已送件未核定 3.已核定 4.已取得建照
					String estateStatus = data.getEstateStatus();
					// J-110-0054_10702_B1001 Web e-Loan額度明細表不動產暨72-2相關註記修改
					String estateOwner = Util.trim(data.getEstateOwner());
					if (Util.equals(estateOwner, UtilConstants.DEFAULT.否)) {
						++is722_Y;
					} else {
						if (("3".equals(estateStatus) || "4"
								.equals(estateStatus))
								&& Util.equals(estateOwner,
										UtilConstants.DEFAULT.是)) {
							++is722_N;
						} else if ("3".equals(estateStatus)
								|| "4".equals(estateStatus)) {

							++is722_N;
						} else {
							++is722_Y;
						}
					}
				} else if (UtilConstants.L140M01T_estatSubType.都更.equals(data
						.getEstateSubType())) {
					// 3.已核定 4.已取得建照
					String estateStatus = data.getEstateStatus();
					// J-109-0248_05097_B1001 Web
					// e-Loan授信都更之計畫進度，將已送件未核定納入排除72-2項目
					if ("2".equals(estateStatus) || "3".equals(estateStatus)
							|| "4".equals(estateStatus)) {

						++is722_N;
					} else {
						++is722_Y;
					}
				}
			} else {
				// 其它項都不屬72-2
				// 全部資料只要有一項不屬722，則全部都不是722
				// 所以就不需再判斷其它資料了
				return "N";
			}
		}

		if (is722_N > 0) {
			return "N"; // 其中一項可排除
		} else {
			if (is722_Y > 0) {
				return "Y";
			} else {
				return ""; // 結果[Y/N]都是0筆, 應不會落在這裡
			}
		}
	}

	public static boolean is722_exItem_cls(String estateType) {
		if (estateType.startsWith("A")
				|| estateType.startsWith("B")
				|| Util.equals(UtilConstants.L140M01T_estatType.C01, estateType)
				|| Util.equals(UtilConstants.L140M01T_estatType.C02, estateType)) {
			return true;
		}
		return false;
	}

	public static TreeSet<String> parse_C120M01A_markModel(String str) {
		TreeSet<String> set = new TreeSet<String>();
		for (String raw_val : str.split("\\|")) {
			String val = Util.trim(raw_val);
			if (Util.isNotEmpty(val)) {
				set.add(val);
			}
		}
		return set;
	}

	// public static String upDW_column_C_FLAG(C120S01Q c120s01q){
	// String varVer = c120s01q.getVarVer();
	// if(Util.equals("", varVer)
	// || Util.equals(LMSUtil.V1_0_NOT_HOUSE_LOAN, varVer)
	// || Util.equals(LMSUtil.V1_9_NOT_HOUSE_LOAN, varVer)
	// || Util.equals(LMSUtil.V2_0_NOT_HOUSE_LOAN, varVer) ){
	// return "";
	// }

	/*
	 * 非房貸模型2.1 , 增加卡友貸 為與 卡友貸 區分, 原版的非房貸模型上傳 N
	 */
	// return "N";
	// }

	// public static String upDW_column_MowType(C120S01R c120s01r){
	// 回傳的 mowType == N
	// return "N";
	// }

	// J-108-0217_10702_B1001 消金界接IVR語音系統查詢及檢核
	public static final String 特定金錢信託受益權自行設質擔保授信 = "08";

	/**
	 * J-108-0166 社會與環境風險評估改版 set L120M01I
	 */
	public static void setL120m01i(L120M01I l120m01i, JSONObject jsonL1205s07)
			throws CapMessageException {
		try {
			l120m01i.setHasD1(jsonL1205s07.optString("hasD1", ""));
			l120m01i.setHasD2(jsonL1205s07.optString("hasD2", ""));
			l120m01i.setHasD3(jsonL1205s07.optString("hasD3", ""));
			if (Util.equals("Y", jsonL1205s07.optString("hasD1", ""))) {
				String[] item1 = StringUtils.split(
						Util.trim(l120m01i.get("item1_D1").toString()), "|");
				if (Arrays.asList(item1).contains("2")) {
					l120m01i.setItemId_D1(Util.trim(jsonL1205s07.optString(
							"itemId_D1", "")));
					l120m01i.setItemDupNo_D1(Util.trim(jsonL1205s07.optString(
							"itemDupNo_D1", "")));
					l120m01i.setItemName_D1(Util.trim(jsonL1205s07.optString(
							"itemName_D1", "")));
				} else {
					l120m01i.setItemId_D1("");
					l120m01i.setItemDupNo_D1("");
					l120m01i.setItemName_D1("");
				}
				String[] item2 = StringUtils.split(
						Util.trim(l120m01i.get("item2_D1").toString()), "|");
				if (Arrays.asList(item2).contains("6")) {
					l120m01i.setItemMemo_D1(Util.trim(jsonL1205s07.optString(
							"itemMemo_D1", "")));
				} else {
					l120m01i.setItemMemo_D1("");
				}
			} else {
				l120m01i.setItem1_D1("");
				l120m01i.setItemId_D1("");
				l120m01i.setItemDupNo_D1("");
				l120m01i.setItemName_D1("");
				l120m01i.setItem2_D1("");
				l120m01i.setItemMemo_D1("");
				l120m01i.setItem3_D1("");
			}
			if (Util.equals("Y", jsonL1205s07.optString("hasD2", ""))) {
				String[] item1 = StringUtils.split(
						Util.trim(l120m01i.get("item1_D2").toString()), "|");
				if (Arrays.asList(item1).contains("2")) {
					l120m01i.setItemId_D2(Util.trim(jsonL1205s07.optString(
							"itemId_D2", "")));
					l120m01i.setItemDupNo_D2(Util.trim(jsonL1205s07.optString(
							"itemDupNo_D2", "")));
					l120m01i.setItemName_D2(Util.trim(jsonL1205s07.optString(
							"itemName_D2", "")));
				} else {
					l120m01i.setItemId_D2("");
					l120m01i.setItemDupNo_D2("");
					l120m01i.setItemName_D2("");
				}
				String[] item2 = StringUtils.split(
						Util.trim(l120m01i.get("item2_D2").toString()), "|");
				if (Arrays.asList(item2).contains("6")) {
					l120m01i.setItemMemo_D2(Util.trim(jsonL1205s07.optString(
							"itemMemo_D2", "")));
				} else {
					l120m01i.setItemMemo_D2("");
				}
			} else {
				l120m01i.setItem1_D2("");
				l120m01i.setItemId_D2("");
				l120m01i.setItemDupNo_D2("");
				l120m01i.setItemName_D2("");
				l120m01i.setItem2_D2("");
				l120m01i.setItemMemo_D2("");
			}
			if (Util.equals("Y", jsonL1205s07.optString("hasD3", ""))) {
				String[] item1 = StringUtils.split(
						Util.trim(l120m01i.get("item1_D3").toString()), "|");
				if (Arrays.asList(item1).contains("2")) {
					l120m01i.setItemId_D3(Util.trim(jsonL1205s07.optString(
							"itemId_D3", "")));
					l120m01i.setItemDupNo_D3(Util.trim(jsonL1205s07.optString(
							"itemDupNo_D3", "")));
					l120m01i.setItemName_D3(Util.trim(jsonL1205s07.optString(
							"itemName_D3", "")));
				} else {
					l120m01i.setItemId_D3("");
					l120m01i.setItemDupNo_D3("");
					l120m01i.setItemName_D3("");
				}
				String[] item2 = StringUtils.split(
						Util.trim(l120m01i.get("item2_D3").toString()), "|");
				if (Arrays.asList(item2).contains("5")) {
					l120m01i.setItemMemo_D3(Util.trim(jsonL1205s07.optString(
							"itemMemo_D3", "")));
				} else {
					l120m01i.setItemMemo_D3("");
				}
			} else {
				l120m01i.setItem1_D3("");
				l120m01i.setItemId_D3("");
				l120m01i.setItemDupNo_D3("");
				l120m01i.setItemName_D3("");
				l120m01i.setItem2_D3("");
				l120m01i.setItemMemo_D3("");
			}
		} catch (Exception e) {
			logger.error("[setL120m01i] EXCEPTION!!", e);
			throw new CapMessageException(e.getMessage(), LMSUtil.class);
		}
	}

	public static void setL120s01q(L120S01Q l120s01q, JSONObject jsonL1205s07)
			throws CapMessageException {
		try {
			l120s01q.setHasD1(jsonL1205s07.optString("hasD1", ""));
			l120s01q.setHasD2(jsonL1205s07.optString("hasD2", ""));
			l120s01q.setHasD3(jsonL1205s07.optString("hasD3", ""));
			if (Util.equals("Y", jsonL1205s07.optString("hasD1", ""))
					|| (Util.equals("N", jsonL1205s07.optString("hasD1", ""))
						&& (Util.equals(Util.nullToSpace(l120s01q.getVer()), UtilConstants.Casedoc.L120S01Q_Ver04)
								|| Util.equals(Util.nullToSpace(l120s01q.getVer()),
												UtilConstants.Casedoc.L120S01Q_Ver05)
								|| Util.equals(Util.nullToSpace(l120s01q.getVer()),
												UtilConstants.Casedoc.L120S01Q_Ver06)
								|| Util.equals(Util.nullToSpace(l120s01q.getVer()),
												UtilConstants.Casedoc.L120S01Q_Ver07)
								|| Util.equals(Util.nullToSpace(l120s01q.getVer()),
												UtilConstants.Casedoc.L120S01Q_Ver08)))) {
				String[] item1 = StringUtils.split(
						Util.trim(l120s01q.get("item1_D1").toString()), "|");
				if (Arrays.asList(item1).contains("2")) {
					l120s01q.setItemId_D1(Util.trim(jsonL1205s07.optString(
							"itemId_D1", "")));
					l120s01q.setItemDupNo_D1(Util.trim(jsonL1205s07.optString(
							"itemDupNo_D1", "")));
					l120s01q.setItemName_D1(Util.trim(jsonL1205s07.optString(
							"itemName_D1", "")));
				} else {
					l120s01q.setItemId_D1("");
					l120s01q.setItemDupNo_D1("");
					l120s01q.setItemName_D1("");
				}
				String[] item2 = StringUtils.split(
						Util.trim(l120s01q.get("item2_D1").toString()), "|");
				if (Arrays.asList(item2).contains("6")) {
					l120s01q.setItemMemo_D1(Util.trim(jsonL1205s07.optString(
							"itemMemo_D1", "")));
				} else {
					l120s01q.setItemMemo_D1("");
				}
			} else {
				l120s01q.setItem1_D1("");
				l120s01q.setItemId_D1("");
				l120s01q.setItemDupNo_D1("");
				l120s01q.setItemName_D1("");
				l120s01q.setItem2_D1("");
				l120s01q.setItemMemo_D1("");
				l120s01q.setItem3_D1("");
			}
			if (Util.equals("Y", jsonL1205s07.optString("hasD2", ""))
					|| (Util.equals("N", jsonL1205s07.optString("hasD2", ""))
						&& (Util.equals(Util.nullToSpace(l120s01q.getVer()), UtilConstants.Casedoc.L120S01Q_Ver04)
							|| Util.equals(Util.nullToSpace(l120s01q.getVer()),
											UtilConstants.Casedoc.L120S01Q_Ver05)
							|| Util.equals(Util.nullToSpace(l120s01q.getVer()),
											UtilConstants.Casedoc.L120S01Q_Ver06)
							|| Util.equals(Util.nullToSpace(l120s01q.getVer()),
											UtilConstants.Casedoc.L120S01Q_Ver07)
							|| Util.equals(Util.nullToSpace(l120s01q.getVer()),
											UtilConstants.Casedoc.L120S01Q_Ver08)))) {
				String[] item1 = StringUtils.split(
						Util.trim(l120s01q.get("item1_D2").toString()), "|");
				if (Arrays.asList(item1).contains("2")) {
					l120s01q.setItemId_D2(Util.trim(jsonL1205s07.optString(
							"itemId_D2", "")));
					l120s01q.setItemDupNo_D2(Util.trim(jsonL1205s07.optString(
							"itemDupNo_D2", "")));
					l120s01q.setItemName_D2(Util.trim(jsonL1205s07.optString(
							"itemName_D2", "")));
				} else {
					l120s01q.setItemId_D2("");
					l120s01q.setItemDupNo_D2("");
					l120s01q.setItemName_D2("");
				}
				String[] item2 = StringUtils.split(
						Util.trim(l120s01q.get("item2_D2").toString()), "|");
				if (Arrays.asList(item2).contains("6")) {
					l120s01q.setItemMemo_D2(Util.trim(jsonL1205s07.optString(
							"itemMemo_D2", "")));
				} else {
					l120s01q.setItemMemo_D2("");
				}
			} else {
				l120s01q.setItem1_D2("");
				l120s01q.setItemId_D2("");
				l120s01q.setItemDupNo_D2("");
				l120s01q.setItemName_D2("");
				l120s01q.setItem2_D2("");
				l120s01q.setItemMemo_D2("");
			}
			if (Util.equals("Y", jsonL1205s07.optString("hasD3", ""))
					|| (Util.equals("N", jsonL1205s07.optString("hasD3", ""))
						&& (Util.equals(Util.nullToSpace(l120s01q.getVer()), UtilConstants.Casedoc.L120S01Q_Ver04)
							|| Util.equals(Util.nullToSpace(l120s01q.getVer()),
											UtilConstants.Casedoc.L120S01Q_Ver05)
							|| Util.equals(Util.nullToSpace(l120s01q.getVer()),
											UtilConstants.Casedoc.L120S01Q_Ver06)
							|| Util.equals(Util.nullToSpace(l120s01q.getVer()),
											UtilConstants.Casedoc.L120S01Q_Ver07)
							|| Util.equals(Util.nullToSpace(l120s01q.getVer()),
											UtilConstants.Casedoc.L120S01Q_Ver08)))) {
				String[] item1 = StringUtils.split(
						Util.trim(l120s01q.get("item1_D3").toString()), "|");
				if (Arrays.asList(item1).contains("2")) {
					l120s01q.setItemId_D3(Util.trim(jsonL1205s07.optString(
							"itemId_D3", "")));
					l120s01q.setItemDupNo_D3(Util.trim(jsonL1205s07.optString(
							"itemDupNo_D3", "")));
					l120s01q.setItemName_D3(Util.trim(jsonL1205s07.optString(
							"itemName_D3", "")));
				} else {
					l120s01q.setItemId_D3("");
					l120s01q.setItemDupNo_D3("");
					l120s01q.setItemName_D3("");
				}
				String[] item2 = StringUtils.split(
						Util.trim(l120s01q.get("item2_D3").toString()), "|");
				if (Arrays.asList(item2).contains("5")) {
					l120s01q.setItemMemo_D3(Util.trim(jsonL1205s07.optString(
							"itemMemo_D3", "")));
				} else {
					l120s01q.setItemMemo_D3("");
				}
			} else {
				l120s01q.setItem1_D3("");
				l120s01q.setItemId_D3("");
				l120s01q.setItemDupNo_D3("");
				l120s01q.setItemName_D3("");
				l120s01q.setItem2_D3("");
				l120s01q.setItemMemo_D3("");
			}

			if(Util.equals(Util.nullToSpace(l120s01q.getVer()),
					UtilConstants.Casedoc.L120S01Q_Ver04)
                    || Util.equals(Util.nullToSpace(l120s01q.getVer()),
                        UtilConstants.Casedoc.L120S01Q_Ver05)
					|| Util.equals(Util.nullToSpace(l120s01q.getVer()),
						UtilConstants.Casedoc.L120S01Q_Ver06)
					|| Util.equals(Util.nullToSpace(l120s01q.getVer()),
						UtilConstants.Casedoc.L120S01Q_Ver07)
					|| Util.equals(Util.nullToSpace(l120s01q.getVer()),
						UtilConstants.Casedoc.L120S01Q_Ver08)){
				l120s01q.setHasLegality(null);
			}
			String hasBadFaith = Util.trim(jsonL1205s07.optString(
					"hasBadFaith", ""));
			if (Util.equals(hasBadFaith, "Y")) {
				String[] badFaithItem = StringUtils.split(
						Util.trim(l120s01q.get("badFaithItem").toString()), "|");
				if (Arrays.asList(badFaithItem).contains("5")) {
					l120s01q.setBadFaithMemo(Util.trim(jsonL1205s07.optString(
							"badFaithMemo", "")));
				} else {
					l120s01q.setBadFaithMemo("");
				}
			} else {
				l120s01q.setBadFaithMemo("");
			}

			if(Util.equals(Util.nullToSpace(l120s01q.getVer()),
					UtilConstants.Casedoc.L120S01Q_Ver07)
					|| Util.equals(Util.nullToSpace(l120s01q.getVer()),
							UtilConstants.Casedoc.L120S01Q_Ver08)){
				String hasSustainEval = Util.trim(jsonL1205s07.optString(
						"hasSustainEval", ""));
				if (Util.equals(hasSustainEval, "Y")) {

				} else {
					l120s01q.setSeRec(null);
					l120s01q.setSeRateSum(null);
					l120s01q.setSeRec_Y(null);
					l120s01q.setSeRatePct(null);
				}
			}
		} catch (Exception e) {
			logger.error("[setL120s01q] EXCEPTION!!", e);
			throw new CapMessageException(e.getMessage(), LMSUtil.class);
		}
	}

	public static String showPic(String type, String value, Properties prop,
			Map<String, CapAjaxFormResult> codeTypes, String kind) {
		StringBuffer sb = new StringBuffer("");

		if (Util.equals("YN", type)) {
			if (Util.equals("Y", value)) {
				sb.append(prop.getProperty("L120M01I.HASDUTY_Y"));
			} else if (Util.equals("N", value)) {
				sb.append(prop.getProperty("L120M01I.HASDUTY_N"));
			} else {
				sb.append(prop.getProperty("L120M01I.HASDUTY_N"));
			}
		}

		return sb.toString();
	}

	public static String showPic2(String type, Map<String, String> value,
			Properties prop, Map<String, CapAjaxFormResult> codeTypes,
			String kind) {
		StringBuffer sb = new StringBuffer("");

		if (Util.equals("checkbox1", type)) {
			if (Util.isNotEmpty(value)) {
				String[] item1 = StringUtils.split(
						Util.trim(value.get("item1")), "|");

				sb.append(prop.getProperty("L120M01I.01") + "<br>");

				int count = codeTypes.get("LMSS07_equator01").toString()
						.split(",").length;
				for (int i = 0; i < count; i++) {
					String a = Integer.toString(i + 1);
					if (i > 0) {
						sb.append("<br>");
					}
					sb.append((Arrays.asList(item1).contains(a) ? "■" : "□"));
					sb.append(codeTypes.get("LMSS07_equator01").get(a));
				}
				if (Arrays.asList(item1).contains("2")) {
					sb.append("<br>");
					sb.append(prop.getProperty("L120M01I.01_1"));
					sb.append(Util.trim(value.get("itemId"))
							+ Util.trim(value.get("itemDupNo")));
					sb.append(prop.getProperty("L120M01I.01_2"));
					sb.append(Util.trim(value.get("itemName")));
				}
			}
		} else if (Util.equals("checkbox2", type)) {
			if (Util.isNotEmpty(value)) {
				String[] item2 = StringUtils.split(
						Util.trim(value.get("item2")), "|");

				sb.append(prop.getProperty("L120M01I.02")
						+ prop.getProperty("L120M01I.02_" + kind + "_0")
						+ "<br>");

				int count = codeTypes.get("LMSS07_" + kind + "_02").toString()
						.split(",").length;
				for (int i = 0; i < count; i++) {
					String a = Integer.toString(i + 1);
					if (i > 0) {
						sb.append(Util.equals("D1", kind) ? "、" : "<br>");
					}
					sb.append((Arrays.asList(item2).contains(a) ? "■" : "□"));
					sb.append(codeTypes.get("LMSS07_" + kind + "_02").get(a));
					if (Util.equals("D1", kind) || Util.equals("D2", kind)) {
						if (Util.equals(a, "6")) {
							if (Util.isNotEmpty(value.get("itemMemo"))) {
								sb.append(Util.equals("D2", kind) ? ("( " + prop
										.getProperty("L120M01I.02_1")) : "");
								sb.append(Util.equals("D1", kind) ? "：" : "");
								sb.append(value.get("itemMemo"));
								sb.append(Util.equals("D2", kind) ? " )" : "");
							}
						}
					} else if (Util.equals("D3", kind)) {
						if (Util.equals(a, "5")) {
							if (Util.isNotEmpty(value.get("itemMemo"))) {
								sb.append("( " + prop.getProperty("L120M01I.02_1")
										+ value.get("itemMemo") + " )");
							}
						}
					}
				}
			}
		} else if (Util.equals("checkbox0", type)) {
			if (Util.isNotEmpty(value)) {
				String[] item0 = StringUtils.split(
						Util.trim(value.get("item0")), "|");
				int count = codeTypes.get(kind).toString()
						.split(",").length;
				for (int i = 0; i < count; i++) {
					String a = Integer.toString(i + 1);
					if (i > 0) {
						sb.append("<br>");
					}
					sb.append((Arrays.asList(item0).contains(a) ? "■" : "□"));
					sb.append(codeTypes.get(kind).get(a));
					if (Util.equals("LMSS07_badFaithItem", kind)) {
						if (Util.equals(a, "5")) {
							if (Util.isNotEmpty(value.get("itemMemo"))) {
								sb.append("( " + prop.getProperty("L120M01I.02_1"));
								sb.append(value.get("itemMemo"));
								sb.append(" )");
							}
						}
					}
				}
			}
		} else if (Util.equals("radio", type)) {
			if (Util.isNotEmpty(value)) {
				sb.append(prop.getProperty("L120M01I.03")
						+ prop.getProperty("L120M01I.03_" + kind + "_0")
						+ "<br>");

				int count = codeTypes.get("LMSS07_" + kind + "_03").toString()
						.split(",").length;
				for (int i = 0; i < count; i++) {
					String a = Integer.toString(i + 1);
					if (i > 0) {
						sb.append("<br>");
					}
					sb.append(Util.equals(a, Util.trim(value.get("item3"))) ? "■"
							: "□");
					sb.append(codeTypes.get("LMSS07_" + kind + "_03").get(a));
				}
			}
		}

		return sb.toString();
	}

	/**
	 * J-108-0166 社會與環境風險評估改版 get L120M01I
	 */
	public static CapAjaxFormResult getL120m01i(L120M01I l120m01i)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		result.set("hasD1", Util.trim(l120m01i.getHasD1()));
		result.set("hasD2", Util.trim(l120m01i.getHasD2()));
		result.set("hasD3", Util.trim(l120m01i.getHasD3()));
		result.set("item1_D1", Util.trim(l120m01i.getItem1_D1()));
		result.set("itemId_D1", Util.trim(l120m01i.getItemId_D1()));
		result.set("itemDupNo_D1", Util.trim(l120m01i.getItemDupNo_D1()));
		result.set("itemName_D1", Util.trim(l120m01i.getItemName_D1()));
		result.set("item2_D1", Util.trim(l120m01i.getItem2_D1()));
		result.set("itemMemo_D1", Util.trim(l120m01i.getItemMemo_D1()));
		result.set("item3_D1", Util.trim(l120m01i.getItem3_D1()));
		result.set("item1_D2", Util.trim(l120m01i.getItem1_D2()));
		result.set("itemId_D2", Util.trim(l120m01i.getItemId_D2()));
		result.set("itemDupNo_D2", Util.trim(l120m01i.getItemDupNo_D2()));
		result.set("itemName_D2", Util.trim(l120m01i.getItemName_D2()));
		result.set("item2_D2", Util.trim(l120m01i.getItem2_D2()));
		result.set("itemMemo_D2", Util.trim(l120m01i.getItemMemo_D2()));
		result.set("item1_D3", Util.trim(l120m01i.getItem1_D3()));
		result.set("itemId_D3", Util.trim(l120m01i.getItemId_D3()));
		result.set("itemDupNo_D3", Util.trim(l120m01i.getItemDupNo_D3()));
		result.set("itemName_D3", Util.trim(l120m01i.getItemName_D3()));
		result.set("item2_D3", Util.trim(l120m01i.getItem2_D3()));
		result.set("itemMemo_D3", Util.trim(l120m01i.getItemMemo_D3()));

		return result;
	}

	public static CapAjaxFormResult getL120s01q(L120S01Q l120s01q)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		result.set("hasDuty", Util.trim(l120s01q.getHasDuty()));
		result.set("hasDeeds", Util.trim(l120s01q.getHasDeeds()));
		result.set("hasLegality", Util.trim(l120s01q.getHasLegality()));
		result.set("hasBadFaith", Util.trim(l120s01q.getHasBadFaith()));
		result.set("badFaithMemo", Util.trim(l120s01q.getBadFaithMemo()));
		result.set("hasD1", Util.trim(l120s01q.getHasD1()));
		result.set("hasD2", Util.trim(l120s01q.getHasD2()));
		result.set("hasD3", Util.trim(l120s01q.getHasD3()));
		result.set("hasESG", Util.trim(l120s01q.getHasESG()));
		result.set("hasCnLim", Util.trim(l120s01q.getHasCnLim()));
		result.set("item1_D1", Util.trim(l120s01q.getItem1_D1()));
		result.set("itemId_D1", Util.trim(l120s01q.getItemId_D1()));
		result.set("itemDupNo_D1", Util.trim(l120s01q.getItemDupNo_D1()));
		result.set("itemName_D1", Util.trim(l120s01q.getItemName_D1()));
		result.set("item2_D1", Util.trim(l120s01q.getItem2_D1()));
		result.set("itemMemo_D1", Util.trim(l120s01q.getItemMemo_D1()));
		result.set("item3_D1", Util.trim(l120s01q.getItem3_D1()));
		result.set("item1_D2", Util.trim(l120s01q.getItem1_D2()));
		result.set("itemId_D2", Util.trim(l120s01q.getItemId_D2()));
		result.set("itemDupNo_D2", Util.trim(l120s01q.getItemDupNo_D2()));
		result.set("itemName_D2", Util.trim(l120s01q.getItemName_D2()));
		result.set("item2_D2", Util.trim(l120s01q.getItem2_D2()));
		result.set("itemMemo_D2", Util.trim(l120s01q.getItemMemo_D2()));
		result.set("item1_D3", Util.trim(l120s01q.getItem1_D3()));
		result.set("itemId_D3", Util.trim(l120s01q.getItemId_D3()));
		result.set("itemDupNo_D3", Util.trim(l120s01q.getItemDupNo_D3()));
		result.set("itemName_D3", Util.trim(l120s01q.getItemName_D3()));
		result.set("item2_D3", Util.trim(l120s01q.getItem2_D3()));
		result.set("itemMemo_D3", Util.trim(l120s01q.getItemMemo_D3()));
		result.set("esgAgency", Util.trim(l120s01q.getEsgAgency()));
		result.set("esgGrade", Util.trim(l120s01q.getEsgGrade()));
		result.set("cnLimMemo", Util.trim(l120s01q.getCnLimMemo()));
		Properties pop = MessageBundleScriptCreator.getComponentResource(LMSCommomPage.class);
		String sustainEval = "";
		if (Util.isNotEmpty(Util.trim(l120s01q.getQSustainEvalDate()))) {
			if (l120s01q.getSeRec() != null && l120s01q.getSeRateSum() != null
					&& l120s01q.getSeRec_Y() != null && l120s01q.getSeRatePct() != null) {
				result.set("hasSustainEval", "Y");
			} else {
				result.set("hasSustainEval", "N");
				sustainEval = pop.getProperty("L120S01Q.noData");
			}
		}
		result.set("sustainEval", sustainEval);

		// J-111-0107_05097_B1001 Web e-Loan企金增加借戶ESG外部綜合評分資料相關資料。
		result.set("custId_l120s01q", Util.trim(l120s01q.getCustId()));
		result.set("dupNo_l120s01q", Util.trim(l120s01q.getDupNo()));

		result.set("badFaithItem", Util.trim(l120s01q.getBadFaithItem()));

		return result;
	}

	/**
	 * J-108-0166 社會與環境風險評估改版 get L120M01I
	 */
	public static String multipleToString(JSONObject json, String name)
			throws CapException {
		StringBuilder sb = new StringBuilder();
		Object value = json.get(name);
		if (value != null) {
			if (value instanceof JSONArray) {
				JSONArray jsonArray = (JSONArray) value;
				for (Object obj : jsonArray.toArray()) {
					if (Util.isNotEmpty(sb))
						sb.append("|");
					sb.append(Util.trim(obj));
				}
			} else if (value.toString().length() == 1) {
				sb.append(Util.trim(value));
			}
		}
		return sb.toString();
	}

	/**
	 * J-108-0166 社會與環境風險評估改版 check L120M01I
	 */
	public static boolean checkL120m01i(L120M01I l120m01i) throws CapException {
		boolean err = false;

		if (l120m01i == null) {
			err = true;
		} else if (Util.equals("02", l120m01i.getVer())) {
			if (Util.equals(l120m01i.getHasD1(), "")
					|| Util.equals(l120m01i.getHasD2(), "")
					|| Util.equals(l120m01i.getHasD3(), "")) {
				err = true;
			}
			if (Util.equals(l120m01i.getHasD1(), "Y")
					&& (Util.equals(l120m01i.getItem1_D1(), "")
							|| Util.equals(l120m01i.getItem2_D1(), "") || Util
							.equals(l120m01i.getItem3_D1(), ""))) {
				err = true;
			}
			if (Util.equals(l120m01i.getHasD2(), "Y")
					&& (Util.equals(l120m01i.getItem1_D2(), "") || Util.equals(
							l120m01i.getItem2_D2(), ""))) {
				err = true;
			}
			if (Util.equals(l120m01i.getHasD3(), "Y")
					&& (Util.equals(l120m01i.getItem1_D3(), "") || Util.equals(
							l120m01i.getItem2_D3(), ""))) {
				err = true;
			}
			String[] item1_D1_arr = StringUtils.split(
					Util.trim(l120m01i.getItem1_D1()), "|");
			if (Arrays.asList(item1_D1_arr).contains("2")
					&& (Util.equals(Util.trim(l120m01i.getItemId_D1()), "")
							|| Util.equals(
									Util.trim(l120m01i.getItemDupNo_D1()), "") || Util
							.equals(Util.trim(l120m01i.getItemName_D1()), ""))) {
				err = true;
			}
			String[] item1_D2_arr = StringUtils.split(
					Util.trim(l120m01i.getItem1_D2()), "|");
			if (Arrays.asList(item1_D2_arr).contains("2")
					&& (Util.equals(Util.trim(l120m01i.getItemId_D2()), "")
							|| Util.equals(
									Util.trim(l120m01i.getItemDupNo_D2()), "") || Util
							.equals(Util.trim(l120m01i.getItemName_D2()), ""))) {
				err = true;
			}
			String[] item1_D3_arr = StringUtils.split(
					Util.trim(l120m01i.getItem1_D3()), "|");
			if (Arrays.asList(item1_D3_arr).contains("2")
					&& (Util.equals(Util.trim(l120m01i.getItemId_D3()), "")
							|| Util.equals(
									Util.trim(l120m01i.getItemDupNo_D3()), "") || Util
							.equals(Util.trim(l120m01i.getItemName_D3()), ""))) {
				err = true;
			}
			String[] item2_D1_arr = StringUtils.split(
					Util.trim(l120m01i.getItem2_D1()), "|");
			if (Arrays.asList(item2_D1_arr).contains("6")
					&& Util.equals(Util.trim(l120m01i.getItemMemo_D1()), "")) {
				err = true;
			}
			String[] item2_D2_arr = StringUtils.split(
					Util.trim(l120m01i.getItem2_D2()), "|");
			if (Arrays.asList(item2_D2_arr).contains("6")
					&& Util.equals(Util.trim(l120m01i.getItemMemo_D2()), "")) {
				err = true;
			}
			String[] item2_D3_arr = StringUtils.split(
					Util.trim(l120m01i.getItem2_D3()), "|");
			if (Arrays.asList(item2_D3_arr).contains("5")
					&& Util.equals(Util.trim(l120m01i.getItemMemo_D3()), "")) {
				err = true;
			}
		}

		return err;
	}

	public static boolean checkL120s01q(L120S01Q l120s01q, L120M01A l120m01a, L120S01B l120s01b, boolean verChk)
			throws CapException {
		boolean err = false;

		boolean needChk = true;
		if (l120s01b != null) {
			String busCode = Util.trim(l120s01b.getBusCode());
			if (LMSUtil.isBusCode_060000_130300(busCode)) {
				needChk = false;
			}
		}

		if (l120s01q == null) {
			err = true;
		} else if (Util.notEquals("01", l120s01q.getVer())) {
			if (Util.equals(l120s01q.getHasD1(), "")
					|| Util.equals(l120s01q.getHasD2(), "")
					|| Util.equals(l120s01q.getHasD3(), "")) {
				err = true;
			}
			if (Util.equals(l120s01q.getHasD1(), "Y")
					&& (Util.equals(l120s01q.getItem1_D1(), "")
							|| Util.equals(l120s01q.getItem2_D1(), "") || Util
							.equals(l120s01q.getItem3_D1(), ""))) {
				err = true;
			}
			if (Util.equals(l120s01q.getHasD2(), "Y")
					&& (Util.equals(l120s01q.getItem1_D2(), "") || Util.equals(
							l120s01q.getItem2_D2(), ""))) {
				err = true;
			}
			if (Util.equals(l120s01q.getHasD3(), "Y")
					&& (Util.equals(l120s01q.getItem1_D3(), "") || Util.equals(
							l120s01q.getItem2_D3(), ""))) {
				err = true;
			}
			String[] item1_D1_arr = StringUtils.split(
					Util.trim(l120s01q.getItem1_D1()), "|");
			if (Arrays.asList(item1_D1_arr).contains("2")
					&& (Util.equals(Util.trim(l120s01q.getItemId_D1()), "")
							|| Util.equals(
									Util.trim(l120s01q.getItemDupNo_D1()), "") || Util
							.equals(Util.trim(l120s01q.getItemName_D1()), ""))) {
				err = true;
			}
			String[] item1_D2_arr = StringUtils.split(
					Util.trim(l120s01q.getItem1_D2()), "|");
			if (Arrays.asList(item1_D2_arr).contains("2")
					&& (Util.equals(Util.trim(l120s01q.getItemId_D2()), "")
							|| Util.equals(
									Util.trim(l120s01q.getItemDupNo_D2()), "") || Util
							.equals(Util.trim(l120s01q.getItemName_D2()), ""))) {
				err = true;
			}
			String[] item1_D3_arr = StringUtils.split(
					Util.trim(l120s01q.getItem1_D3()), "|");
			if (Arrays.asList(item1_D3_arr).contains("2")
					&& (Util.equals(Util.trim(l120s01q.getItemId_D3()), "")
							|| Util.equals(
									Util.trim(l120s01q.getItemDupNo_D3()), "") || Util
							.equals(Util.trim(l120s01q.getItemName_D3()), ""))) {
				err = true;
			}
			String[] item2_D1_arr = StringUtils.split(
					Util.trim(l120s01q.getItem2_D1()), "|");
			if (Arrays.asList(item2_D1_arr).contains("6")
					&& Util.equals(Util.trim(l120s01q.getItemMemo_D1()), "")) {
				err = true;
			}
			String[] item2_D2_arr = StringUtils.split(
					Util.trim(l120s01q.getItem2_D2()), "|");
			if (Arrays.asList(item2_D2_arr).contains("6")
					&& Util.equals(Util.trim(l120s01q.getItemMemo_D2()), "")) {
				err = true;
			}
			String[] item2_D3_arr = StringUtils.split(
					Util.trim(l120s01q.getItem2_D3()), "|");
			if (Arrays.asList(item2_D3_arr).contains("5")
					&& Util.equals(Util.trim(l120s01q.getItemMemo_D3()), "")) {
				err = true;
			}

			if (Util.notEquals("02", l120s01q.getVer())) { // 2020最新為 ver 03
															// 檢查欄位
				if (Util.equals(l120s01q.getHasESG(), "")) {
					err = true;
				}

				// J-111-0107_05097_B1001 Web e-Loan企金增加借戶ESG外部綜合評分資料相關資料。
				// 國內企金
				if (UtilConstants.Casedoc.DocType.企金.equals(Util.trim(l120m01a
						.getDocType()))
						&& Util.notEquals(Util.trim(l120m01a.getTypCd()),
								UtilConstants.Casedoc.typCd.海外)) {
					// 國內企金才有借戶ESG評分相關資訊
					// 不管客戶是否有ESG評等欄位為Y/N，都要引進借戶ESG評分相關資訊
					if (Util.isEmpty(l120s01q.getEsgReceiveDate())) {
						err = true;
					}

					if (Util.equals(l120s01q.getHasESG(), "Y")) {
						// 客戶有ESG評等
						if (!isL120s01qHasEsgScore(l120s01q)) {
							// 借戶ESG評分相關資訊引進都是無或空白，則必須輸入原有的ESG評等機構欄位
							if ((Util.equals(
									Util.trim(l120s01q.getEsgAgency()), "") || Util
									.equals(Util.trim(l120s01q.getEsgGrade()),
											""))) {
								err = true;
							}
						} else {
							// 借戶ESG評分相關資訊引進都是無或空白，則原有的ESG評等機構欄位非必要輸入
						}
					}
				} else {
					// 海外不檢核借戶ESG評分相關資訊
					if (Util.equals(l120s01q.getHasESG(), "Y")
							&& (Util.equals(Util.trim(l120s01q.getEsgAgency()),
									"") || Util.equals(
									Util.trim(l120s01q.getEsgGrade()), ""))) {
						err = true;
					}
				}

				if (Util.equals(l120s01q.getHasCnLim(), "")) {
					err = true;
				}
				if (Util.equals(l120s01q.getHasCnLim(), "Y")
						&& Util.isEmpty(Util.trim(l120s01q.getCnLimMemo()))) {
					err = true;
				}

				if (Util.equals(
						Util.nullToSpace(l120s01q.getFinalAssessment()), "")) {
					err = true;
				}

				if (Util.equals(Util.nullToSpace(l120s01q.getVer()),
						UtilConstants.Casedoc.L120S01Q_Ver04)
                        || Util.equals(Util.nullToSpace(l120s01q.getVer()),
                            UtilConstants.Casedoc.L120S01Q_Ver05)
						|| Util.equals(Util.nullToSpace(l120s01q.getVer()),
							UtilConstants.Casedoc.L120S01Q_Ver06)
						|| Util.equals(Util.nullToSpace(l120s01q.getVer()),
							UtilConstants.Casedoc.L120S01Q_Ver07)
						|| Util.equals(Util.nullToSpace(l120s01q.getVer()),
							UtilConstants.Casedoc.L120S01Q_Ver08)) {
					if (Util.equals(l120s01q.getHasD1(), "N")
							&& (Util.equals(l120s01q.getItem1_D1(), "")
							|| Util.equals(l120s01q.getItem2_D1(), ""))) {
						err = true;
					}

					if (Util.equals(l120s01q.getHasD2(), "N")
							&& (Util.equals(l120s01q.getItem1_D2(), "")
							|| Util.equals(l120s01q.getItem2_D2(), ""))) {
						err = true;
					}

					if (Util.equals(l120s01q.getHasD3(), "N")
							&& (Util.equals(l120s01q.getItem1_D3(), "")
							|| Util.equals(l120s01q.getItem2_D3(), ""))) {
						err = true;
					}

					if (Util.equals(l120s01q.getHasBadFaith(), "")
							|| Util.equals(l120s01q.getBadFaithItem(), "")) {
						err = true;
					}
					String[] badFaithItem_arr = StringUtils.split(
							Util.trim(l120s01q.getBadFaithItem()), "|");
					if (Arrays.asList(badFaithItem_arr).contains("5")
							&& Util.equals(Util.trim(l120s01q.getBadFaithMemo()), "")) {
						err = true;
					}
				}

				if (Util.equals(Util.nullToSpace(l120s01q.getVer()),
						UtilConstants.Casedoc.L120S01Q_Ver05)
						|| Util.equals(Util.nullToSpace(l120s01q.getVer()),
							UtilConstants.Casedoc.L120S01Q_Ver06)
						|| Util.equals(Util.nullToSpace(l120s01q.getVer()),
							UtilConstants.Casedoc.L120S01Q_Ver07)
						|| Util.equals(Util.nullToSpace(l120s01q.getVer()),
							UtilConstants.Casedoc.L120S01Q_Ver08)) {
					String isHighEnv = Util.nullToSpace(l120s01q.getIsHighEnv());
					String isHighCarbonEms = Util.nullToSpace(l120s01q.getIsHighCarbonEms());
					String isDeCarbonEms = Util.nullToSpace(l120s01q.getIsDeCarbonEms());
					String isSustain = Util.nullToSpace(l120s01q.getIsSustain());
					String sustainMemo = Util.nullToSpace(l120s01q.getSustainMemo());
					String sbtiIsCommited = Util.nullToSpace(l120s01q.getSbtiIsCommited());
					if (Util.equals(
							Util.nullToSpace(l120s01q.getQCesEsgDate()), "")) {
						err = true;
					}
					if (Util.equals(isHighEnv, "")) {
						err = true;
					}
					if (Util.equals(isHighCarbonEms, "")) {
						err = true;
					}
					if (Util.equals(isDeCarbonEms, "")) {
						err = true;
					}
					if (Util.equals(isSustain, "")) {
						err = true;
					}

					if (Util.equals(Util.nullToSpace(l120s01q.getVer()),
							UtilConstants.Casedoc.L120S01Q_Ver06)
							|| Util.equals(Util.nullToSpace(l120s01q.getVer()),
								UtilConstants.Casedoc.L120S01Q_Ver07)
							|| Util.equals(Util.nullToSpace(l120s01q.getVer()),
								UtilConstants.Casedoc.L120S01Q_Ver08)) {
						if (Util.equals(
								Util.nullToSpace(l120s01q.getQEsgSbtiDate()), "")) {
							err = true;
						}
					}

					if (verChk && needChk) {
						if (Util.notEquals(isHighEnv, "Y") && Util.notEquals(isHighEnv, "N")) {
							err = true;
						}
						if (Util.notEquals(isHighCarbonEms, "Y") && Util.notEquals(isHighCarbonEms, "N")) {
							err = true;
						}
						if (Util.notEquals(isDeCarbonEms, "Y") && Util.notEquals(isDeCarbonEms, "N")) {
							err = true;
						}
						if (Util.notEquals(isSustain, "Y") && Util.notEquals(isSustain, "N")) {
							err = true;
						}

						// L120S01Q.noData=查無資料
						if (Util.isEmpty(sbtiIsCommited) || Util.equals(sbtiIsCommited, "查無資料")) {
							err = true;
						}
					}

					if (Util.equals(isSustain, "Y") && Util.isEmpty(sustainMemo)) {
						err = true;
					}
				}

				if (UtilConstants.Casedoc.DocType.企金.equals(Util.trim(l120m01a.getDocType()))
						&& Util.notEquals(Util.trim(l120m01a.getTypCd()), UtilConstants.Casedoc.typCd.海外)) {
					if (Util.equals(Util.nullToSpace(l120s01q.getVer()),
							UtilConstants.Casedoc.L120S01Q_Ver07)
							|| Util.equals(Util.nullToSpace(l120s01q.getVer()),
									UtilConstants.Casedoc.L120S01Q_Ver08)) {
						if (Util.isEmpty(Util.nullToSpace(l120s01q.getQSustainEvalDate()))) {
							err = true;
						}
					}
				}
				
				// J-113-0442  企金簽報書「社會責任與環境風險評估」新增赤道原則相關欄位
				if(Util.equals(Util.nullToSpace(l120s01q.getVer()),UtilConstants.Casedoc.L120S01Q_Ver08)){
					String epsFlag = Util.nullToSpace(l120s01q.getEpsFlag());
					if(Util.isEmpty(epsFlag)){
						err = true;
					}
				}
			}
		}

		return err;
	}

	// J-111-0107_05097_B1001 Web e-Loan企金增加借戶ESG外部綜合評分資料相關資料。
	public static boolean isL120s01qEsgScoreNone(String esgInput) {
		if (Util.equals(Util.trim(esgInput), "")
				|| Util.equals(Util.trim(esgInput), "無")
				|| Util.equals(Util.trim(esgInput), "无")
				|| Util.equals(Util.trim(esgInput), "N.A.")) {
			return true;
		}
		return false;
	}

	// J-111-0107_05097_B1001 Web e-Loan企金增加借戶ESG外部綜合評分資料相關資料。
	public static boolean isL120s01qHasEsgScore(L120S01Q l120s01q) {
		boolean hasEsgScore = false;
		if (Util.isNotEmpty(l120s01q.getEsgReceiveDate())) {
			if (isL120s01qEsgScoreNone(l120s01q.getEsgScore())
					&& isL120s01qEsgScoreNone(l120s01q.getMsciLevel())
					&& isL120s01qEsgScoreNone(l120s01q.getEsgLevel())
					&& isL120s01qEsgScoreNone(l120s01q.getCompanyGovernance())
					&& isL120s01qEsgScoreNone(l120s01q.getIssLevel())
					&& isL120s01qEsgScoreNone(l120s01q.getMoody())
					&& isL120s01qEsgScoreNone(l120s01q.getSpScore())
					&& isL120s01qEsgScoreNone(l120s01q.getSinopac())
					&& isL120s01qEsgScoreNone(l120s01q.getSustainability())) {
				// 借戶ESG評分相關資訊引進都是無或空白
				// J-112-0063_05097_B1001 Web e-Loan配合集保結算所網站更新，增加moody ESG評分
			} else {

				hasEsgScore = true;
			}
		}

		return hasEsgScore;
	}

	// J-112-0337 配合授審處，在簽報書及常董會提案稿，社會責任與環境風險評估大項中，增加本行ESG風險評級結果
	public static String getL120s01qFinalAssessmentCode(L120S01Q l120s01q) {
		String faCode = null;

		if (l120s01q != null) {
			String finalAssessment = Util.trim(l120s01q.getFinalAssessment());
			if (Util.equals(finalAssessment, "不予承作")
					|| Util.equals(finalAssessment, "Reject Accepted")) {
				faCode = "RA";
			} else if (Util.equals(finalAssessment, "低風險")
					|| Util.equals(finalAssessment, "LOW")) {
				faCode = "L";
			} else if (Util.equals(finalAssessment, "中風險")
					|| Util.equals(finalAssessment, "MEDIUM")) {
				faCode = "M";
			} else if (Util.equals(finalAssessment, "高風險")
					|| Util.equals(finalAssessment, "HIGH")) {
				faCode = "H";
			} else if (Util.equals(finalAssessment, "無須風險檢核")
					|| Util.equals(finalAssessment, "N/A")) {
				faCode = "NA";
			} else if (Util.equals(finalAssessment, "查無資料")) {
				// 比徵信多這個判斷
				faCode = "None";
			} else {
				faCode = "0";
			}
		}

		return faCode;
	}

	public static boolean isOutNewVer(L120M01A l120m01a, L120M01I l120m01i) {
		boolean result = false;
		if (l120m01a != null) {
			if (UtilConstants.Casedoc.DocType.企金.equals(Util.trim(l120m01a
					.getDocType()))
					&& UtilConstants.Casedoc.DocKind.授權外.equals(Util
							.trim(l120m01a.getDocKind()))
					&& UtilConstants.Casedoc.DocCode.一般.equals(Util
							.trim(l120m01a.getDocCode()))) {
				// 用ver欄位判斷 簽報書版本
				if (l120m01i != null) {
					String verStr = Util.trim(l120m01i.getVer());
					if (Util.isNotEmpty(verStr)) {
						int ver = Integer.valueOf(verStr).intValue();
						// 改版本這裡要更新!!!!!!!!!!!!!!!
						String oldVer = "02"; // 上一版 版本
						int oldInt = Integer.valueOf(oldVer).intValue();
						if (ver > oldInt) { // 最新版本
							// J-110-0358 海外授權外改版
							// L120M01I 多 PrintVer 欄位 => 因應國內海外列印不同時改版生效而新增的欄位
							// 國內新版在上線時都有補PrintVer 海外案件程式生效後才會有PrintVer值
							String printVerStr = Util.trim(l120m01i
									.getPrintVer());
							if (Util.isNotEmpty(printVerStr)) {
								// 不用參數比較
								// UtilConstants.Casedoc.L120M01I_PrintVer
								// 因為改版的話code應該也會需要改....
								if (Util.equals(printVerStr, "03")) {
									result = true;
								}
							}
						}
					}
				}
			}
		}
		return result;
	}

	public static String getPrintVerStr(L120M01A l120m01a, L120M01I l120m01i,
			Boolean printR30) {
		// 注意回傳的 printVerStr 盡量不能重複
		/**
		 * 授權內新版：v202110 授權外新版：v202011 (LMSUtil.isOutNewVer)
		 * 授權外簽報書改版&「授信信用風險管理」遵循檢核表：v202212
		 **/
		// J-112-0505 社會責任改版
		String printVer = "";
		if (l120m01a != null) {
			if (printR30) {
				if (l120m01i != null) {
					if (Util.equals(l120m01i.getPrintVer(),
							UtilConstants.Casedoc.L120M01I_PrintVer04)) {
						printVer = "v202212";
					}
				}
			} else {
				if (UtilConstants.Casedoc.DocKind.授權內.equals(Util.trim(l120m01a
						.getDocKind()))) {
					if (l120m01i != null) {
						if (Util.equals(l120m01i.getVer(),
								UtilConstants.Casedoc.L120M01I_Ver03)
								&& (Util.equals(
										l120m01i.getPrintVer(),
										UtilConstants.Casedoc.L120M01I_PrintVer03) || Util
										.equals(l120m01i.getPrintVer(),
												UtilConstants.Casedoc.L120M01I_PrintVer04))) {
							printVer = "v202110";
						}
					}
				} else if ( // 取代 LMSUtil.isOutNewVer
				UtilConstants.Casedoc.DocType.企金.equals(Util.trim(l120m01a
						.getDocType()))
						&& UtilConstants.Casedoc.DocKind.授權外.equals(Util
								.trim(l120m01a.getDocKind()))
						&& UtilConstants.Casedoc.DocCode.一般.equals(Util
								.trim(l120m01a.getDocCode()))) {
					if (l120m01i != null) {
						String verStr = Util.trim(l120m01i.getVer());
						if (Util.isNotEmpty(verStr)) {
							int ver = Integer.valueOf(verStr).intValue();
							// 改版本這裡要更新!!!!!!!!!!!!!!!
							String oldVer = "02"; // 上一版 版本
							int oldInt = Integer.valueOf(oldVer).intValue();
							if (ver > oldInt) { // 最新版本
								// J-110-0358 海外授權外改版
								// L120M01I 多 PrintVer 欄位 =>
								// 因應國內海外列印不同時改版生效而新增的欄位
								// 國內新版在上線時都有補PrintVer 海外案件程式生效後才會有PrintVer值
								String printVerStr = Util.trim(l120m01i
										.getPrintVer());
								if (Util.isNotEmpty(printVerStr)) {
									// 不用參數比較
									// UtilConstants.Casedoc.L120M01I_PrintVer
									// 因為改版的話code應該也會需要改....
									if (Util.equals(
											printVerStr,
											UtilConstants.Casedoc.L120M01I_PrintVer03)) {
										printVer = "v202011";
									} else if (Util
											.equals(printVerStr,
													UtilConstants.Casedoc.L120M01I_PrintVer04)) {
										// J-111-0551 在途授信額度 L120M01I_PrintVer
										// 改為 04
										printVer = "v202212";
									}
								}
							}
						}
					}
				}
			}
		}
		return printVer;
	}

	/**
	 * L120S24A 畫面呈現、報表處理數字欄位處理
	 * 
	 * @param value
	 * @param divideThou
	 * @param addPercent
	 * @return
	 */
	public static String processL120s24aBigDecimal(BigDecimal value,
			boolean divideThou, boolean addPercent) {
		// DecimalFormat df = new
		// DecimalFormat("###,###,###,###,###,###,###,##0.##");
		DecimalFormat df = new DecimalFormat(
				"###,###,###,###,###,###,###,##0.##");
		if (value != null && value.compareTo(BigDecimal.ZERO) != 0) {
			if (divideThou) {
				BigDecimal big1000 = new BigDecimal("1000");
				value = value.divide(big1000, 2, RoundingMode.HALF_UP);
			}
		}

		String res = "";
		if (Util.nullToSpace(value) == null) {
			res = "";
		} else if (value == null) {
			// 一定要做好判斷，不然會有可能造成整筆formatter有問題....
			res = "";
		} else {
			res = df.format(value);

			if (addPercent) {
				res = res + "%";
			}
		}
		return res;
	}

	/**
	 * L120S24A 判斷是否要紀錄LTV值於備註
	 * 
	 * @param remarkLTVSB
	 * @param l120s24a
	 * @return
	 */
	public static StringBuilder recordRemarkLTV(StringBuilder remarkLTVSB,
			L120S24A l120s24a, Properties popV20220812,
			Map<String, String> LTVClassMap) {
		if (Util.isNotEmpty(remarkLTVSB)) {
			remarkLTVSB.append("<BR/>");
		}

		// 會進到這裡的都是LTV法選"是"，所以要判斷他需不需要輸入LTV
		// I.央行管制暴險->"否"
		if ("N".equals(Util.trim(l120s24a.getIsCBControl_s24a()))) {

			String LTVClass_s24a = Util.trim(l120s24a.getLTVClass_s24a());
			// 住宅、商用
			if ("1".equals(LTVClass_s24a) || "2".equals(LTVClass_s24a)) {

				// 土地Y, 農林Y->純土地，要輸LTV
				// 土地Y, 農林N->非合格，不用輸LTV
				// 土地N->合格，要輸LTV

				// 本額度之不動產擔保品為純土地
				if ("Y".equals(Util.trim(l120s24a.getEstateIsLand_s24a()))) {
					// L.該筆不動產為農地、林地->是
					if ("Y".equals(Util.trim(l120s24a.getIsFarmWood_s24a()))) {
						// 要輸LTV
						return processRemarkLTV(remarkLTVSB, l120s24a, true,
								popV20220812, LTVClassMap);
					}
				} else {
					// 要輸LTV
					return processRemarkLTV(remarkLTVSB, l120s24a, true,
							popV20220812, LTVClassMap);
				}
			}
		}

		// 上面要輸LTV沒中的部分，剩下都是不需輸LTV的情況
		return processRemarkLTV(remarkLTVSB, l120s24a, false, popV20220812,
				LTVClassMap);
	}

	/**
	 * L120S24A 組合LTV說明字串用
	 * 
	 * @param remarkLTVSB
	 * @param l120s24a
	 * @return
	 */
	public static StringBuilder processRemarkLTV(StringBuilder remarkLTVSB,
			L120S24A l120s24a, boolean haveLTV, Properties popV20220812,
			Map<String, String> LTVClassMap) {
		remarkLTVSB.append(popV20220812.getProperty(
				"L120S24A.remarkLTV.cntrNo", "額度序號"));
		remarkLTVSB.append(Util.trim(l120s24a.getCntrNo_s24a()) + " ");
		if (haveLTV) {
			remarkLTVSB.append(popV20220812.getProperty(
					"L120S24A.remarkLTV.message1", "採"));

			// 1 住宅、2 商用
			if (Util.isNotEmpty(l120s24a.getLTVClass_s24a())) {
				remarkLTVSB
						.append(LTVClassMap.get(l120s24a.getLTVClass_s24a()));
			}

			if ("1".equals(Util.trim(l120s24a.getLTVType_s24a()))) {
				remarkLTVSB.append(popV20220812.getProperty("L120S24A.normal",
						"一般"));
			} else if ("2".equals(Util.trim(l120s24a.getLTVType_s24a()))) {
				remarkLTVSB.append(popV20220812.getProperty("L120S24A.income",
						"收益"));
			} else {

			}
			remarkLTVSB.append(popV20220812.getProperty(
					"L120S24A.remarkLTV.message2", "計算"));

			String LTV_s24a = processL120s24aBigDecimal(l120s24a.getLTV_s24a(),
					false, false);
			remarkLTVSB.append("，LTV=" + LTV_s24a + "%");
		} else {
			remarkLTVSB.append(popV20220812.getProperty(
					"L120S24A.remarkLTV.message3", "無須輸入LTV值"));
		}

		return remarkLTVSB;
	}

	/**
	 * L120S24A 判斷是否要紀錄LTV值於備註
	 * 
	 * @param remarkLTVSB
	 * @param l120s24a
	 * @return
	 */
	public static StringBuilder recordRemarkLTV_2025(StringBuilder remarkLTVSB,
			L120S24A l120s24a, Properties popV20250101,
			Map<String, String> LTVClassMap) {
		if (Util.isNotEmpty(remarkLTVSB)) {
			remarkLTVSB.append("<BR/>");
		}

		// 會進到這裡的都是LTV法選"是"，所以要判斷他需不需要輸入LTV
		// I.央行管制暴險->"否"
		if ("N".equals(Util.trim(l120s24a.getIsCBControl_s24a()))) {

			String LTVClass_s24a = Util.trim(l120s24a.getLTVClass_s24a());
			// 住宅、商用
			if ("1".equals(LTVClass_s24a) || "2".equals(LTVClass_s24a)) {

				// 土地Y, 農林Y->純土地，要輸LTV
				// 土地Y, 農林N->非合格，不用輸LTV
				// 土地N->合格，要輸LTV

				// 本額度之不動產擔保品為純土地
				if ("Y".equals(Util.trim(l120s24a.getEstateIsLand_s24a()))) {
					// L.該筆不動產為農地、林地->是
					if ("Y".equals(Util.trim(l120s24a.getIsFarmWood_s24a()))) {
						// 要輸LTV
						return processRemarkLTV_2025(remarkLTVSB, l120s24a, 1,
								popV20250101, LTVClassMap);
					}else{
						// 不用輸LTV，但要有說明
						return processRemarkLTV_2025(remarkLTVSB, l120s24a, 2,
								popV20250101, LTVClassMap);
					}
				} else {
					// 要輸LTV
					return processRemarkLTV_2025(remarkLTVSB, l120s24a, 1,
							popV20250101, LTVClassMap);
				}
			}
		}

		// 上面要輸LTV沒中的部分，剩下都是不需輸LTV的情況
		return processRemarkLTV_2025(remarkLTVSB, l120s24a, 3, popV20250101,
				LTVClassMap);
	}
	
	/**
	 * L120S24A 組合LTV說明字串用
	 * 
	 * 不論需不需要輸入LTV，都會列出分類等等的說明
	 * 
	 * @param remarkLTVSB
	 * @param l120s24a
	 * @return
	 */
	public static StringBuilder processRemarkLTV_2025(
			StringBuilder remarkLTVSB, L120S24A l120s24a, int type,
			Properties popV20250101, Map<String, String> LTVClassMap) {
		remarkLTVSB.append(popV20250101.getProperty(
				"L120S24A.remarkLTV.cntrNo", "額度序號"));
		remarkLTVSB.append(Util.trim(l120s24a.getCntrNo_s24a()) + " ");
		// type1:要輸入LTV法，且列出分類、類別
		// type2:不用輸入LTV法，且列出分類、類別
		// type3:不用輸入LTV法，不用額外顯示什麼欄位
		
		if (type == 1) {
			// type1:要輸入LTV法，且列出分類、類別
			remarkLTVSB.append(popV20250101.getProperty(
					"L120S24A.remarkLTV.message1", "採"));

			// 1 住宅、2 商用
			if (Util.isNotEmpty(l120s24a.getLTVClass_s24a())) {
				remarkLTVSB
						.append(LTVClassMap.get(l120s24a.getLTVClass_s24a()));
			}

			if ("1".equals(Util.trim(l120s24a.getLTVType_s24a()))) {
				remarkLTVSB.append(popV20250101.getProperty("L120S24A.normal",
						"一般"));
			} else if ("2".equals(Util.trim(l120s24a.getLTVType_s24a()))) {
				remarkLTVSB.append(popV20250101.getProperty("L120S24A.income",
						"收益"));
			} else {

			}
			remarkLTVSB.append(popV20250101.getProperty(
					"L120S24A.remarkLTV.message2", "計算"));

			String LTV_s24a = processL120s24aBigDecimal(l120s24a.getLTV_s24a(),
					false, false);
			remarkLTVSB.append("，LTV=" + LTV_s24a + "%");
		} else if (type == 2) {
			// type2:不用輸入LTV法，且列出分類、類別
			// 會走到這就只有非央行管制->住宅、商用->一般、收益->純土地->非農地
			// ex:商用一般非農地、林地
			remarkLTVSB.append(popV20250101.getProperty(
					"L120S24A.remarkLTV.message1", "採"));

			// 1 住宅、2 商用
			if (Util.isNotEmpty(l120s24a.getLTVClass_s24a())) {
				remarkLTVSB
						.append(LTVClassMap.get(l120s24a.getLTVClass_s24a()));
			}

			if ("1".equals(Util.trim(l120s24a.getLTVType_s24a()))) {
				remarkLTVSB.append(popV20250101.getProperty("L120S24A.normal",
						"一般"));
			} else if ("2".equals(Util.trim(l120s24a.getLTVType_s24a()))) {
				remarkLTVSB.append(popV20250101.getProperty("L120S24A.income",
						"收益"));
			} else {

			}
			// 本額度之不動產擔保品為純土地
			if ("Y".equals(Util.trim(l120s24a.getEstateIsLand_s24a()))) {
				// L.該筆不動產為農地、林地->非農地、林地
				if ("N".equals(Util.trim(l120s24a.getIsFarmWood_s24a()))) {
					remarkLTVSB.append("非農地、林地");
				}
			}
			remarkLTVSB.append(popV20250101.getProperty(
					"L120S24A.remarkLTV.message3", "無須輸入LTV值"));
		} else {
			// type3:不用輸入LTV法，不用額外顯示什麼欄位
			remarkLTVSB.append(popV20250101.getProperty(
					"L120S24A.remarkLTV.message3", "無須輸入LTV值"));
		}

		return remarkLTVSB;
	}
	
	/**
	 * L120S24A 判斷是否要紀錄LTV值於備註
	 * 
	 * 2025版的多顯示央行管制類別
	 * 
	 * @param remarkLTVSB
	 * @param l120s24a
	 * @param popV20220812
	 * @param LTVClassMap
	 * @return
	 */
	public static StringBuilder recordRemarkLTVAndCBControl(
			StringBuilder remarkLTVSB, L120S24A l120s24a,
			Properties popV20220812, Map<String, String> LTVClassYMap,
			Map<String, String> LTVClassNMap) {
		// step1.先跑輸不輸LTV法
		recordRemarkLTV_2025(remarkLTVSB, l120s24a, popV20220812, LTVClassNMap);
		// step2.跑顯不顯示央行管制
		// I.央行管制暴險->"是"
		if ("Y".equals(Util.trim(l120s24a.getIsCBControl_s24a()))) {
			remarkLTVSB.append("，央行管制:");

			// 1 住宅 一般 公司法人購置住宅貸款、2 住宅 收益 公司法人購置住宅貸款......
			if (Util.isNotEmpty(l120s24a.getLTVClass_s24a())) {
				remarkLTVSB
						.append(LTVClassYMap.get(l120s24a.getLTVClass_s24a()));
			}
		}

		return remarkLTVSB;
	}

	/**
	 * L120S24A 紀錄特殊融資類別於備註
	 * 
	 * 2025版的
	 * 
	 * @param remarkLTVSB
	 * @param l120s24a
	 * @param popV20220812
	 * @param LTVClassMap
	 * @return
	 */
	public static StringBuilder recordRemarkSpecialFinRiskType(
			StringBuilder remarkSpecialFinRiskTypeSB, L120S24A l120s24a,
			Properties popV20220812, Map<String, String> specialFinRiskTypeMap) {

		// 會進來的都已經是有特殊融資類別的
		if (Util.isNotEmpty(remarkSpecialFinRiskTypeSB)) {
			remarkSpecialFinRiskTypeSB.append("<BR/>");
		}

		remarkSpecialFinRiskTypeSB.append(popV20220812.getProperty(
				"L120S24A.remarkLTV.cntrNo", "額度序號"));
		remarkSpecialFinRiskTypeSB.append(Util.trim(l120s24a.getCntrNo_s24a())
				+ " 為特殊融資，類別為");
		String specialFinRiskType_s24a = Util.trim(l120s24a
				.getSpecialFinRiskType_s24a());
		remarkSpecialFinRiskTypeSB.append(Util.trim(specialFinRiskTypeMap
				.get(specialFinRiskType_s24a)));
		return remarkSpecialFinRiskTypeSB;
	}

	/**
	 * L120S24A 紀錄信保成數於備註
	 * 
	 * 2025版的
	 * 
	 * @param remarkLTVSB
	 * @param l120s24a
	 * @param popV20220812
	 * @param LTVClassMap
	 * @return
	 */
	public static StringBuilder recordRemarkGutPercent(
			StringBuilder remarkGutPercentSB, L120S24A l120s24a,
			Properties popV20220812, Map<String, String> gutClassMap) {

		// 會進來的都已經是有保證資訊
		if (Util.isNotEmpty(remarkGutPercentSB)) {
			remarkGutPercentSB.append("<BR/>");
		}

		remarkGutPercentSB.append(popV20220812.getProperty(
				"L120S24A.remarkLTV.cntrNo", "額度序號"));
		remarkGutPercentSB.append(Util.trim(l120s24a.getCntrNo_s24a()) + " 有");
		String gutClass_s24a = Util.trim(l120s24a.getGutClass_s24a());// 保證類別
		remarkGutPercentSB.append(Util.trim(gutClassMap.get(gutClass_s24a)));

		BigDecimal gutPercent_s24a = l120s24a.getGutPercent_s24a();
		if (gutPercent_s24a != null) {
			// 有數字才抓，沒有就空白
			remarkGutPercentSB.append(processL120s24aBigDecimal(
					gutPercent_s24a, false, true));
		}
		remarkGutPercentSB.append("保證");
		return remarkGutPercentSB;
	}
	
	/**
	 * J-108-0166 選擇「核定」者：額度性質為「新做」之額度序號，其現請額度不能為「零」 為批覆書時多判斷， 原本明細表是0
	 * 批覆也是0，就不用擋
	 */
	public static StringBuffer checkNewAmt(L140M01A l140m01a,
			StringBuffer checkList, String itemType, L140M01A l140m01aItemType1) {
		if (LMSUtil.isContainValue(Util.trim(l140m01a.getProPerty()),
				UtilConstants.Cntrdoc.Property.新做)) {
			if (l140m01a.getCurrentApplyAmt() == null
					|| BigDecimal.ZERO.compareTo(l140m01a.getCurrentApplyAmt()) == 0
					|| Util.isEmpty(Util.trim(l140m01a.getCurrentApplyCurr()))) {

				// 為批覆書時多判斷， 原本明細表是0 批覆也是0，就不用擋
				if ("2".equals(itemType)
						&& (l140m01aItemType1.getCurrentApplyAmt() == null || BigDecimal.ZERO
								.compareTo(l140m01aItemType1
										.getCurrentApplyAmt()) == 0)) {

				} else {
					checkList.append(checkList.length() > 0 ? "、" : "");
					checkList.append(Util.trim(l140m01a.getCntrNo()));
				}
			}
		}
		return checkList;
	}

	public static String encodeRefNo_inOracleFormat(String trim_l120s09b_refNo,
			String trim_l120s09b_uniqueKey) {
		return StringUtils.substringBefore(trim_l120s09b_refNo, "-") + "-"
				+ Util.getRightStr(trim_l120s09b_uniqueKey, 6);
	}

	public static void setRefNo_inOracleFormat(L120S09B l120s09b) {
		if (l120s09b != null) {
			String org_refNo = Util.trim(l120s09b.getRefNo());
			String new_refNo = LMSUtil.encodeRefNo_inOracleFormat(org_refNo,
					Util.trim(l120s09b.getUniqueKey()));
			if (!Util.equals(new_refNo, org_refNo, false)) {
				l120s09b.setRefNo(new_refNo);
			}
		}
	}

	@SuppressWarnings("restriction")
	public static byte[] base64StringToImage(String base64String)
			throws Exception {
		return decoder.decodeBuffer(base64String);
	}

	public static List<Object> get_notEmpty_One_or_Multiple_Data(
			JSONObject jsonObject, String chose_key) {
		List<Object> r = new ArrayList<Object>();
		String dataStr = Util.trim(jsonObject.optString(chose_key));
		JSONArray data_list = jsonObject.optJSONArray(chose_key);
		if (data_list != null) {
			int data_list_size = data_list.size();
			for (int i = 0; i < data_list_size; i++) {
				Object val = data_list.get(i);
				if (Util.isNotEmpty(Util.trim(val))) {
					r.add(val);
				}
			}
		}
		if ((data_list == null || data_list.size() == 0)
				&& Util.isNotEmpty(dataStr)) {
			// 雖然在塞資料時, 是用 JSONArray
			// 但存入 DB 時，若只有1筆
			r.add(dataStr);
		}
		return r;
	}

	public static void l120s03a_clear(L120S03A l120s03a) {
		if (UtilConstants.Casedoc.L120s03aCrdFlag.信保.equals(l120s03a
				.getCrdFlag())) {
			l120s03a.setCollAmt(null);
			l120s03a.setRskRatio(null); // 風險權數(非信保)
			l120s03a.setRskAmt1(null);
			l120s03a.setRskr1(null);
			l120s03a.setCamt1(null);
			l120s03a.setBisr1(null);
			l120s03a.setCostr1(null);
		} else {
			// 非信保
			l120s03a.setCrdRatio(null);
			l120s03a.setRskMega(null);
			l120s03a.setRskCrd(null);
			// ~~~~~~~~
			l120s03a.setRskAmt2(null);
			l120s03a.setRskr2(null);
			l120s03a.setCamt2(null);
			l120s03a.setBisr2(null);
			l120s03a.setCostr2(null);
		}
	}

	public static void copy_column_from_L120S03A_to_L140M01A(L120S03A l120s03a,
			L140M01A l140m01a) {
		if (UtilConstants.Casedoc.L120s03aCrdFlag.信保.equals(l120s03a
				.getCrdFlag())) {
			// 信保
			// l140m01a.setItemC(BigDecimal.valueOf(new Double("100")));

			// J-104-0084-001 Web
			// e-Loan授信系統修改風險權數計算明細表，就信保基金保證之授信案，風險權數改為得由經辦修改
			if (!l120s03a.getCrdRskRatio().isNaN()) {
				l140m01a.setItemC(BigDecimal.valueOf(l120s03a.getCrdRskRatio()));

			} else {
				l140m01a.setItemC(BigDecimal.valueOf(new Double("100")));
			}

			if (!l120s03a.getRskr2().isNaN()) {
				l140m01a.setRItemD(BigDecimal.valueOf(l120s03a.getRskr2()));
			}
			if (!l120s03a.getBisr2().isNaN()) {
				l140m01a.setItemF(BigDecimal.valueOf(l120s03a.getBisr2()));
			}
			if (!l120s03a.getCostr2().isNaN()) {
				l140m01a.setItemG(BigDecimal.valueOf(l120s03a.getCostr2()));
			}
		} else {
			// 非信保
			if (!l120s03a.getRskRatio().isNaN()) {
				l140m01a.setItemC(BigDecimal.valueOf(l120s03a.getRskRatio()));
			}
			if (!l120s03a.getRskr1().isNaN()) {
				l140m01a.setRItemD(BigDecimal.valueOf(l120s03a.getRskr1()));
			}
			if (!l120s03a.getBisr1().isNaN()) {
				l140m01a.setItemF(BigDecimal.valueOf(l120s03a.getBisr1()));
			}
			if (!l120s03a.getCostr1().isNaN()) {
				l140m01a.setItemG(BigDecimal.valueOf(l120s03a.getCostr1()));
			}
		}
	}

	@SuppressWarnings("unused")
	public static void calculateBis(CapAjaxFormResult formResult,
			Properties prop_CLSS07APage02) throws CapMessageException {
		String crdFlag = Util.trim(formResult.get("crdFlag")); // 畫面上的信保註記

		if (UtilConstants.Casedoc.L120s03aCrdFlag.信保.equals(crdFlag)) {
			// 信保
			if (Util.isEmpty(Util.trim(formResult.get("bisr2")))) {
				formResult.set("bisr2", "0");
				// checkSpace = true;
			}
		} else {
			// 非信保
			if (Util.isEmpty(Util.trim(formResult.get("bisr1")))) {
				formResult.set("bisr1", "0");
				// checkSpace = true;
			}
		}
		// if (checkSpace) {
		// // L1205S07.error18 占資本適足率為空，無法計算
		// Properties pop = MessageBundleScriptCreator
		// .getComponentResource(LMSS07Panel.class);
		// throw new CapMessageException(pop.getProperty("L1205S07.error18"),
		// getClass());
		// }
		double applyAmt = Util
				.parseDouble(Util.trim(formResult.get("applyAmt")));
		double fcltAmt = Util.parseDouble(Util.trim(formResult.get("fcltAmt")));

		// J-105-0194-001 Web e-Loan國內海外授信管理系統，額度性質為取消時，風險權數計算之額度金額改以前准額度引進。
		if (false) {
			// applyAmt == 0
			if (UtilConstants.Casedoc.L120s03aCrdFlag.信保.equals(crdFlag)) {
				// 信保
				formResult.set("crdRatio", "0");
				formResult.set("rskMega", "0");
				formResult.set("rskCrd", "0");
				formResult.set("rskAmt2", "0");
				formResult.set("rskr2", "0");
				formResult.set("camt2", "0");
				formResult.set("bisr2", "0");
				formResult.set("costr2", "0");
				formResult.set("crdRskRatio", "0");

			} else {
				// 非信保
				formResult.set("collAmt", "0");
				formResult.set("rskRatio", "0");
				formResult.set("rskAmt1", "0");
				formResult.set("rskr1", "0");
				formResult.set("camt1", "0");
				formResult.set("bisr1", "0");
				formResult.set("costr1", "0");
				formResult.set("rskCrd", UtilConstants.Mark.SPACE);
				formResult.set("crdRskRatio", "");
			}
		} else {
			if (UtilConstants.Casedoc.L120s03aCrdFlag.信保.equals(crdFlag)) {
				// 信保
				long crdRatio = Util.parseLong(Util.trim(formResult
						.get("crdRatio")));

				// double rskMega = fcltAmt * ((double) (100 - crdRatio) / 100);
				// double rskCrd = (fcltAmt - rskMega) * 0.2;
				// J-104-0084-001 BGN Web
				// e-Loan授信系統修改風險權數計算明細表，就信保基金保證之授信案，風險權數改為得由經辦修改
				double crdRskRatio = Util.parseDouble(Util.trim(formResult
						.get("crdRskRatio")));
				double rskMega = fcltAmt * ((double) (100 - crdRatio) / 100)
						* ((double) crdRskRatio / 100);
				double rskCrd = (fcltAmt * ((double) crdRatio / 100)) * 0.2;
				// J-104-0084-001 END Web
				// e-Loan授信系統修改風險權數計算明細表，就信保基金保證之授信案，風險權數改為得由經辦修改

				double rskAmt2 = rskMega + rskCrd;
				double rskr2 = (rskAmt2 / fcltAmt) * 100;
				double camt2 = rskAmt2 * 0.1;
				BigDecimal bisr2 = BigDecimal.valueOf(rskAmt2 * 0.00007);
				bisr2 = bisr2.divide(new BigDecimal("100"), 5,
						BigDecimal.ROUND_HALF_UP);
				String costr2 = CapMath
						.round(Util.trim(rskAmt2 * 0.001 / (double) applyAmt
								* 100), 3);
				formResult.set("crdRatio", Util.trim(crdRatio));
				formResult.set("crdRskRatio",
						CapMath.round(Util.trim(crdRskRatio), 2));
				formResult.set("rskMega", CapMath.round(Util.trim(rskMega), 2));
				formResult.set("rskCrd", CapMath.round(Util.trim(rskCrd), 2));
				formResult.set("rskAmt2", CapMath.round(Util.trim(rskAmt2), 2));
				formResult.set("rskr2", CapMath.round(Util.trim(rskr2), 2));
				formResult.set("camt2", CapMath.round(Util.trim(camt2), 2));
				formResult.set("bisr2", Util.trim(bisr2.toString()));
				formResult.set("costr2", costr2);
			} else {
				// 非信保
				if (Util.isEmpty(Util.trim(formResult.get("collAmt")))
						|| Util.isEmpty(Util.trim(formResult.get("rskRatio")))) {

					// 【合格擔保品抵減額】或【風險權數（保證人請註記）】為空，請確認
					throw new CapMessageException(
							prop_CLSS07APage02.getProperty("L120S03A.error1"),
							LMSUtil.class);
				}
				double collAmt = Util.parseDouble(Util.trim(formResult
						.get("collAmt")));
				double rskRatio = Util.parseDouble(Util.trim(formResult
						.get("rskRatio")));
				double rskAmt1 = (fcltAmt - collAmt) * rskRatio / 100;
				/*
				 * 建霖說： 針對「非信保」案件，若「B.合格擔保品抵減額」大於「A.授信額度」，導致「D.風險抵減後曝險額」為負數時，
				 * 則「D.風險抵減後曝險額」固定為 0 ，其餘相關欄位 D-1, E, F, G 計算後應該也都會是 0 。
				 * 
				 * Miller add 2012/07/10
				 */
				if (rskAmt1 <= 0) {
					rskAmt1 = 0;
				}
				double rskr1 = rskAmt1 / fcltAmt * 100;
				double camt1 = rskAmt1 * 0.1;
				BigDecimal bisr1 = BigDecimal.valueOf(rskAmt1 * 0.00007);
				bisr1 = bisr1.divide(new BigDecimal("100"), 5,
						BigDecimal.ROUND_HALF_UP);
				String costr1 = CapMath
						.round(Util.trim(rskAmt1 * 0.001 / (double) applyAmt
								* 100), 3);
				formResult.set("collAmt", Util.trim(collAmt));
				formResult.set("rskRatio",
						Util.trim(CapMath.round(Util.trim(rskRatio), 2)));
				formResult.set("rskAmt1", CapMath.round(Util.trim(rskAmt1), 2));
				formResult.set("rskr1",
						Util.trim(CapMath.round(Util.trim(rskr1), 2)));
				formResult.set("camt1", CapMath.round(Util.trim(camt1), 2));
				formResult.set("bisr1", bisr1.toString());
				formResult.set("costr1", costr1);
				formResult.set("rskCrd", UtilConstants.Mark.SPACE);
				formResult.set("crdRskRatio", "");
			}
		}
	}

	public static Map<String, String> getApplyCodeMap() {
		Properties prop_LMSCommomPage = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);
		Map<String, String> applyCodeMap = new HashMap<String, String>();
		applyCodeMap.put("1", prop_LMSCommomPage
				.getProperty("C101S01A.idCheckInquiry.firstIssue"));// 初發
		applyCodeMap.put("2", prop_LMSCommomPage
				.getProperty("C101S01A.idCheckInquiry.reissue"));// 補發
		applyCodeMap.put("3", prop_LMSCommomPage
				.getProperty("C101S01A.idCheckInquiry.renewal"));// 換發
		return applyCodeMap;
	}

	public static String getIdCardCheckReturnMessage(String id,
			String checkIdCardApply) {

		Properties prop_LMSCommomPage = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);

		Map<String, String> rtnMap = new HashMap<String, String>();
		rtnMap.put("1", prop_LMSCommomPage
				.getProperty("C101S01A.idCheckInquiry.message.1"));
		rtnMap.put("2", MessageFormat.format(prop_LMSCommomPage
				.getProperty("C101S01A.idCheckInquiry.message.2"), id));
		rtnMap.put("3", MessageFormat.format(prop_LMSCommomPage
				.getProperty("C101S01A.idCheckInquiry.message.3"), id));
		rtnMap.put("4", MessageFormat.format(prop_LMSCommomPage
				.getProperty("C101S01A.idCheckInquiry.message.4"), id));
		rtnMap.put("5", MessageFormat.format(prop_LMSCommomPage
				.getProperty("C101S01A.idCheckInquiry.message.5"), id));
		rtnMap.put("6", MessageFormat.format(prop_LMSCommomPage
				.getProperty("C101S01A.idCheckInquiry.message.6"), id));
		rtnMap.put("7", MessageFormat.format(prop_LMSCommomPage
				.getProperty("C101S01A.idCheckInquiry.message.7"), id));
		rtnMap.put("8", prop_LMSCommomPage
				.getProperty("C101S01A.idCheckInquiry.message.8"));
		return rtnMap.get(checkIdCardApply);
	}

	public static Map<String, String> getRpaStatusMap() {

		Properties prop_LMSCommomPage = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);
		Map<String, String> m = new HashMap<String, String>();
		m.put(UtilConstants.RPA.STATUS.查詢中,
				prop_LMSCommomPage.getProperty("RPA.status.inquire"));
		m.put(UtilConstants.RPA.STATUS.查詢完成,
				prop_LMSCommomPage.getProperty("RPA.status.queryComplete"));
		m.put(UtilConstants.RPA.STATUS.查詢失敗,
				prop_LMSCommomPage.getProperty("RPA.status.queryFailed"));
		return m;
	}
	
	public static Map<String, String> getProkindRatePlanConditonMap() {
		Map<String, String> map = new HashMap<String, String>();
		map.put("5906", "A");//青安(31-歡喜房貸專案 + 06-青年安家購屋優惠貸款)
		map.put("3120", "C");//自住型成長(31-歡喜房貸專案 + 20-自住型房貸成長方案)
		map.put("311", "D"); //投資型(31-歡喜房貸專案 + 不動產投資型)
		map.put("312", "B"); //自住型(31-歡喜房貸專案 + 不動產自住型)
		map.put("03Y1", "F");//行家理財貸款-中長期 + 菁英人員信貸
		map.put("02Y2", "F");//行家理財貸款-短期     + 菁英人員理財型房貸
		map.put("03Y2", "F");//行家理財貸款-中長期 + 菁英人員理財型房貸
		map.put("68Y2", "F");//行家理財貸款-中期循環 + 菁英人員理財型房貸
		map.put("49Y3", "F");//次順位房貸 + 菁英次順位房貸
		return map;
	}
	
	public static Map<String, String> getIndustrialAreaHouseDeductionMap() {
		Map<String, String> map = new HashMap<String, String>();
		map.put("379160000A_4_N_Y", "0.5"); //台北市_A+區_All(排除住家)_住家用_-0.5成
		map.put("376410000A_4_N_Y", "0.5"); //新北市_A+區_all(排除住家)_住家用_-0.5成
		map.put("376410000A_1_N_Y", "0.5"); //新北市, A區_all(排除住家)_住家用_-0.5成
		return map;
	}
	
	public static Map<String, BigDecimal> getTypeBCDRatioLimitationMap() {
		Map<String, BigDecimal> map = new HashMap<String, BigDecimal>();
		map.put("379160000A_4_Y_Y", new BigDecimal(8)); //台北市_A+區_住家用_住家用
		map.put("376410000A_1_Y_Y", new BigDecimal(8)); //新北市_全區(A區)_住家用_住家用
		map.put("376410000A_2_Y_Y", new BigDecimal(8)); //新北市_全區(B區)_住家用_住家用
		map.put("376410000A_4_Y_Y", new BigDecimal(8)); //新北市_全區(A+區)_住家用_住家用
		map.put("OTHER_1_Y_Y", new BigDecimal(8));      //其餘地區(非雙北)_全區(A區)_住家用_住家用
		map.put("OTHER_2_Y_Y", new BigDecimal(8));      //其餘地區(非雙北)_全區(B區)_住家用_住家用
		map.put("OTHER_4_Y_Y", new BigDecimal(8));      //其餘地區(非雙北)_全區(A+區)_住家用_住家用
		return map;
	}
	
	public static Map<String, BigDecimal> getTypeARatioLimitationMap() {
		Map<String, BigDecimal> map = new HashMap<String, BigDecimal>();
		map.put("379160000A_4_Y_Y", new BigDecimal(8));  //台北市_A+區_住家用_住家用
		map.put("376410000A_2_N_Y", new BigDecimal(6.5));//新北市_B區_非住家用_住家用
		map.put("376410000A_1_Y_Y", new BigDecimal(8));  //新北市_全區(A區)_住家用_住家用
		map.put("376410000A_2_Y_Y", new BigDecimal(8));  //新北市_全區(B區)_住家用_住家用
		map.put("376410000A_4_Y_Y", new BigDecimal(8));  //新北市_全區(A+區)_住家用_住家用
		map.put("OTHER_1_Y_Y", new BigDecimal(8));       //其餘地區(非雙北)_全區(A區)_住家用_住家用
		map.put("OTHER_2_Y_Y", new BigDecimal(8));       //其餘地區(非雙北)_全區(B區)_住家用_住家用
		map.put("OTHER_4_Y_Y", new BigDecimal(8));       //其餘地區(非雙北)_全區(A+區)_住家用_住家用
		map.put("OTHER_1_N_Y", new BigDecimal(6.5));     //其餘地區(非雙北)_全區(A區)_非住家用_住家用
		map.put("OTHER_2_N_Y", new BigDecimal(6.5));     //其餘地區(非雙北)_全區(B區)_非住家用_住家用
		map.put("OTHER_4_N_Y", new BigDecimal(6.5));     //其餘地區(非雙北)_全區(A+區)_非住家用_住家用
		return map;
	}
	
	public static List<String> getElitePlanY1Y2Y3Codes() {
		return Arrays.asList(new String[] {"Y1", "Y2", "Y3"});
	}

	/**
	 * 處理額度明細表一些特殊規格的值
	 *
	 * @param jsonL140m01a
	 */
	public static void transJsonValue(JSONObject jsonL140m01a) {
		if (jsonL140m01a.containsKey("rescueItemLKind")) {
			boolean isArr = jsonL140m01a.get("rescueItemLKind") instanceof JSONArray;
			int sum = 0;
			if (isArr) {
				JSONArray rescueItemLKind = jsonL140m01a.getJSONArray("rescueItemLKind");
				for (int i = 0; i < rescueItemLKind.size(); i++) {
					sum = sum + rescueItemLKind.getInt(i);
				}
			} else {
				sum = Util.parseInt(jsonL140m01a.get("rescueItemLKind"));
			}
			if (sum == 0) {
				jsonL140m01a.put("rescueItemLKind", "");
			} else {
				jsonL140m01a.put("rescueItemLKind", sum);
			}
		}
	}

	public static List<String> numberToMulitBiNumer(Integer number) {
		List<String> values = new ArrayList<String>();
		if (number != null && number != 0) {
			int index = 0;
			while (index <=Integer.MAX_VALUE) {
				int value = 1 << index;
				if (value > number) {
					break;
				} else {
					if ((number & value) == value) {
						values.add(String.valueOf(value));
					}
				}
				index++;
			}
		}
		return values;
	}

}
