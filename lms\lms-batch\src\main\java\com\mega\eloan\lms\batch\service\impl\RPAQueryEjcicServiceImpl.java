package com.mega.eloan.lms.batch.service.impl;

import javax.annotation.Resource;

import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.exception.GWException;
import com.mega.eloan.common.gwclient.EJCICGwClient;
import com.mega.eloan.common.gwclient.EJCICGwReqMessage;
import com.mega.eloan.common.gwclient.GWLogger;
import com.mega.eloan.common.gwclient.GWType;
import com.mega.eloan.common.service.GWLogService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.lns.service.LMS1201Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * RPA 回傳勞工紓困貸款案件狀態
 * </pre>
 * <p>
 * LOCAL Test URL example ： http://localhost/ces-web/app/schedulerRPA
 * <p>
 * Post Request : {"serviceId":"getSmallBusStatus", "vaildIP":"N",
 * "request":{"responseCode":"1","custId":"13724746","brNo":"007",
 * "rpaUserId","078001"}}
 * <p>
 * SIT http://*************/ces-web/app/schedulerRPA
 * 
 * <AUTHOR>
 * @version <ul>
 *          <li>2021/6/2,EL07623,new
 *          </ul>
 * @since 2021/6/2
 */
@Service("queryEjcicService")
public class RPAQueryEjcicServiceImpl extends AbstractCapService implements
		WebBatchService {

	private static Logger logger = LoggerFactory
			.getLogger(RPAQueryEjcicServiceImpl.class);

	@Resource
	LMS1201Service service1201;

	@Resource
	SysParameterService sysParameterService;

	@Resource
	GWLogService gwLogService;

	@Resource
	EloandbBASEService eloandbBASEService;

	@Resource
	BranchService branchService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	EJCICGwClient ejcicClient;

	@Value("${systemId}")
	private String sysId;

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.common.batch.service.WebBatchService#execute(net.sf.json
	 * .JSONObject)
	 * 
	 * REQUEST: http://localhost:9081/lms-web/app/schedulerRPA
	 * {"serviceId":"queryEjcicService"
	 * ,"vaildIP":"N","request":{"custId":"10101013"
	 * ,"dupNo":"0","brNo":"010","rpaUserId":"007623"}}
	 * 
	 * RESPONSE: {"rc":0,"rcmsg":"SUCCESS","message":"執行成功","rcUrl":
	 * "http://***************/ejcic/combination/JCIC0444.jsp?deptid=010&branchnm=%C4%F5%B6%AE%A4%C0%A6%E6&prodid=P1&queryid=10101013&empname=%B6%C0_007623&empid=007623&apid=EL&pur=B4A&purpose=1&cbdeptid=0103&key=ELKEY17925320881792"
	 * }
	 */
	@Override
	public JSONObject execute(JSONObject json) {
		JSONObject mag;
		logger.info("queryEjcicService 啟動========================");
		logger.info("傳入參數==>[{}]", json.toString());
		GWLogger gwlogger = new GWLogger(GWType.GWTYPE_RPA, gwLogService,
				sysParameterService);

		JSONObject req = json.getJSONObject("request");
		String errorMsg = "";
		String custId = req.optString("custId", "");
		String dupNo = req.optString("dupNo", "");
		String brNo = req.optString("brNo", "");
		String prodId = req.optString("prodId", "");
		String rpaUserId = req.optString("rpaUserId", "");
		String purpose_for_PACK = req.optString("purpose", ""); // 查詢目的{1:企業授信,
		// 2:房屋貸款,3:消費性貸款,
		// 4:留學生貸款}
		
		gwlogger.logBegin(sysId, custId, "queryEjcicService", req.toString(),
				System.currentTimeMillis());

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String userId = rpaUserId;

		String empname = userInfoService.getUserName(userId);
		String deptid = brNo;
		String cbdeptid = "";
		String deptnm = "";
		IBranch iBranch = branchService.getBranch(brNo);
		if (iBranch != null) {
			cbdeptid = iBranch.getBrNo() + iBranch.getChkNo();
			deptnm = iBranch.getBrName();
		}

		// A_PUR2["a"]='1';
		// A_PUR2["b"]='2';
		// A_PUR2["ab"]='3';
		// A_PUR2["c"]='4';
		// A_PUR2["ac"]='5';
		// A_PUR2["bc"]='6';
		// A_PUR2["abc"]='7';
		// A_PUR2["d"]='8';
		// A_PUR2["ad"]='9';
		// A_PUR2["bd"]='A';
		// A_PUR2["abd"]='B';
		// A_PUR2["cd"]='C';
		// A_PUR2["acd"]='D';
		// A_PUR2["bcd"]='E';
		// A_PUR2["abcd"]='F';
		// A_PUR2["e"]='G';
		// A_PUR2["ae"]='H';
		// A_PUR2["be"]='I';
		// A_PUR2["abe"]='J';
		// A_PUR2["ce"]='K';
		// A_PUR2["ace"]='L';
		// A_PUR2["bce"]='M';
		// A_PUR2["abce"]='N';
		// A_PUR2["de"]='O';
		// A_PUR2["ade"]='P';
		// A_PUR2["bde"]='Q';
		// A_PUR2["abde"]='R';
		// A_PUR2["cde"]='S';
		// A_PUR2["acde"]='T';
		// A_PUR2["bcde"]='U';
		// A_PUR2["abcde"]='V';
		// A_PUR2["x"]='X';
		// A_PUR2["y"]='Y';
		// A_PUR2["w"]='W';
		// A_PUR2["z"]='Z';
		// String pur = "C4B"; // 查詢理由選項123

		String pur = "B4A"; // 查詢理由選項123 第一層 B.原業務往來 第二層 4放款業務(c) 第三層
		// A.取得當事人書面同意

		 

		String callAPI_URL_ejcic_PACK = "";
		String resp_url = "";

		EJCICGwReqMessage ejcicReq_PACK = new EJCICGwReqMessage();
		ejcicReq_PACK.setSysId("LMS");
		ejcicReq_PACK.setMsgId(IDGenerator.getUUID());
		ejcicReq_PACK.setQueryid(custId);
		ejcicReq_PACK.setEmpid(userId);
		ejcicReq_PACK.setEmpname(empname);
		ejcicReq_PACK.setDeptid(deptid);
		ejcicReq_PACK.setCbdeptid(cbdeptid);
		ejcicReq_PACK.setBranchnm(deptnm);
		ejcicReq_PACK.setPur(pur);

		ejcicReq_PACK.setProdid(prodId);
		ejcicReq_PACK.setPurpose(purpose_for_PACK);

		try {
			callAPI_URL_ejcic_PACK = Util.trim(ejcicClient
					.get_callAPI_URL(ejcicReq_PACK));
			resp_url = callAPI_URL_ejcic_PACK;
		} catch (Exception e) {
			errorMsg = e.toString();
		}

		logger.info("queryEjcicService 結束========================");

		GWException gwException = null;
		if (!CapString.isEmpty(errorMsg)) {
			logger.info(errorMsg);
			gwException = new GWException(errorMsg, getClass(),
					GWException.GWTYPE_RPA);
		}

		if (!CapString.isEmpty(errorMsg)) {
			logger.info(errorMsg);
		} else {
			logger.info("執行成功");
		}

		if (!CapString.isEmpty(errorMsg)) {
			// mag = JSONObject
			// .fromObject("{\"rc\": 0, \"rcmsg\": \"FAIL\", \"message\":\" "
			// + errorMsg + "\"}");

			JSONObject jsonVal = new JSONObject();
			jsonVal.put("rc", "1");
			jsonVal.put("rcmsg", "FAIL");
			jsonVal.put("message", errorMsg);
			mag = jsonVal;

		} else {
			// mag = JSONObject
			// .fromObject("{\"rc\": 0, \"rcmsg\": \"SUCCESS\", \"message\":\"執行成功\", \"rcUrl\":\""
			// + resp_url + "\"}");

			JSONObject jsonVal = new JSONObject();
			jsonVal.put("rc", "0");
			jsonVal.put("rcmsg", "SUCCESS");
			jsonVal.put("message", "執行成功");
			jsonVal.put("rcUrl", resp_url);
			mag = jsonVal;
		}

		gwlogger.logEnd(mag.toString(), gwException, "0");
		return mag;
	}

}
