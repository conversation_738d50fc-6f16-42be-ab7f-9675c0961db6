package com.mega.eloan.lms.batch.service.impl;

import javax.annotation.Resource;

import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;

import tw.com.iisi.cap.service.AbstractCapService;

/**
 * 消金收入明細增加代碼，並做資料轉換
 *
 * <AUTHOR>
 */
@Service("lmsbatch0011serviceimpl")
public class LmsBatch0011ServiceImpl extends AbstractCapService implements
        WebBatchService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());


    @Resource
    EloandbBASEService eloandbService;


    @Override
    public JSONObject execute(JSONObject json) {

        JSONObject result = null;

        try {

            logger.info("LmsBatch0011ServiceImpl開始執行");

            //A01=>A01	年收入：所得清單/完稅證明
            //A02=>A02	年收入：扣繳憑單
            //A03=>A03	年收入：公司整體薪酬表
            //A04=>A04	年收入：企業年報之董監酬勞
            //A05=>A05	年收入：聘書

            //A06=>B01	月收入：薪轉存摺(固定薪)
            //A07=>B02	月收入：薪轉存摺(業務職)
            //A08=>B03	月收入：勞保/健保投保明細
            //-=>B04	月收入：薪資單(固定薪) 新增代碼
            //-=>B05	月收入：薪資單(業務職) 新增代碼
            //A09=>C01	營收/執業收入：401/403/405報表
            //A10=>C02	營收/執業收入：營收存摺明細
            //A11=>C03	營收/執業收入：營所稅結算申報書
            //A12=>D01	其他收入：租金收入
            //A13=>D02	其他收入：退休俸
            //A14=>D03	其他收入：現金入帳存摺(非營收類)
            //A15=>D04	其他收入：其他非上述收入
            eloandbService.update("update com.bcodetype set codevalue = 'B01', CODEORDER= 6  where CODETYPE = 'c101s01w_incomeItem'  and codevalue = 'A06'");
            eloandbService.update("update com.bcodetype set codevalue = 'B02', CODEORDER= 7  where CODETYPE = 'c101s01w_incomeItem'  and codevalue = 'A07'");
            eloandbService.update("update com.bcodetype set codevalue = 'B03', CODEORDER= 8  where CODETYPE = 'c101s01w_incomeItem'  and codevalue = 'A08'");
            eloandbService.update("update com.bcodetype set codevalue = 'C01', CODEORDER= 11 where CODETYPE = 'c101s01w_incomeItem'  and codevalue = 'A09'");
            eloandbService.update("update com.bcodetype set codevalue = 'C02', CODEORDER= 12 where CODETYPE = 'c101s01w_incomeItem'  and codevalue = 'A10'");
            eloandbService.update("update com.bcodetype set codevalue = 'C03', CODEORDER= 13 where CODETYPE = 'c101s01w_incomeItem'  and codevalue = 'A11'");
            eloandbService.update("update com.bcodetype set codevalue = 'D01', CODEORDER= 14 where CODETYPE = 'c101s01w_incomeItem'  and codevalue = 'A12'");
            eloandbService.update("update com.bcodetype set codevalue = 'D02', CODEORDER= 15 where CODETYPE = 'c101s01w_incomeItem'  and codevalue = 'A13'");
            eloandbService.update("update com.bcodetype set codevalue = 'D03', CODEORDER= 16 where CODETYPE = 'c101s01w_incomeItem'  and codevalue = 'A14'");
            eloandbService.update("update com.bcodetype set codevalue = 'D04', CODEORDER= 17 where CODETYPE = 'c101s01w_incomeItem'  and codevalue = 'A15'");
            eloandbService.update("INSERT INTO COM.BCODETYPE (OID, CODETYPE, CODEVALUE, CODEDESC, LOCALE, CODEDESC2, CODEDESC3, CODEORDER, LASTMODIFYBY, LASTMODIFYTIME) VALUES(get_oid(), 'c101s01w_incomeItem', 'B04', '月收入：薪資單(固定薪)', 'zh_TW', 'formula_8', ' (公式八)', 9, 'system', current timestamp)");
            eloandbService.update("INSERT INTO COM.BCODETYPE (OID, CODETYPE, CODEVALUE, CODEDESC, LOCALE, CODEDESC2, CODEDESC3, CODEORDER, LASTMODIFYBY, LASTMODIFYTIME) VALUES(get_oid(), 'c101s01w_incomeItem', 'B05', '月收入：薪資單(業務職)', 'zh_TW', 'formula_9', ' (公式九)', 10, 'system', current timestamp)");

            eloandbService.update("update lms.c101s01w set INCOMEITEM = 'B01' where INCOMEITEM = 'A06'");
            eloandbService.update("update lms.c101s01w set INCOMEITEM = 'B02' where INCOMEITEM = 'A07'");
            eloandbService.update("update lms.c101s01w set INCOMEITEM = 'B03' where INCOMEITEM = 'A08'");
            eloandbService.update("update lms.c101s01w set INCOMEITEM = 'C01' where INCOMEITEM = 'A09'");
            eloandbService.update("update lms.c101s01w set INCOMEITEM = 'C02' where INCOMEITEM = 'A10'");
            eloandbService.update("update lms.c101s01w set INCOMEITEM = 'C03' where INCOMEITEM = 'A11'");
            eloandbService.update("update lms.c101s01w set INCOMEITEM = 'D01' where INCOMEITEM = 'A12'");
            eloandbService.update("update lms.c101s01w set INCOMEITEM = 'D02' where INCOMEITEM = 'A13'");
            eloandbService.update("update lms.c101s01w set INCOMEITEM = 'D03' where INCOMEITEM = 'A14'");
            eloandbService.update("update lms.c101s01w set INCOMEITEM = 'D04' where INCOMEITEM = 'A15'");

            eloandbService.update("update lms.c120s01w set INCOMEITEM = 'B01' where INCOMEITEM = 'A06'");
            eloandbService.update("update lms.c120s01w set INCOMEITEM = 'B02' where INCOMEITEM = 'A07'");
            eloandbService.update("update lms.c120s01w set INCOMEITEM = 'B03' where INCOMEITEM = 'A08'");
            eloandbService.update("update lms.c120s01w set INCOMEITEM = 'C01' where INCOMEITEM = 'A09'");
            eloandbService.update("update lms.c120s01w set INCOMEITEM = 'C02' where INCOMEITEM = 'A10'");
            eloandbService.update("update lms.c120s01w set INCOMEITEM = 'C03' where INCOMEITEM = 'A11'");
            eloandbService.update("update lms.c120s01w set INCOMEITEM = 'D01' where INCOMEITEM = 'A12'");
            eloandbService.update("update lms.c120s01w set INCOMEITEM = 'D02' where INCOMEITEM = 'A13'");
            eloandbService.update("update lms.c120s01w set INCOMEITEM = 'D03' where INCOMEITEM = 'A14'");
            eloandbService.update("update lms.c120s01w set INCOMEITEM = 'D04' where INCOMEITEM = 'A15'");

            result = JSONObject.fromObject(WebBatchCode.RC_SUCCESS);
            result.element(WebBatchCode.P_RESPONSE, "LmsBatch0011ServiceImpl執行成功！");

            logger.info("LmsBatch0011ServiceImpl執行成功");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            result = JSONObject.fromObject(WebBatchCode.RC_ERROR);
            result.element(WebBatchCode.P_RESPONSE,
                    "LmsBatch0011ServiceImpl執行失敗！====>" + e.getMessage());
        }
        return result;
    }

}
