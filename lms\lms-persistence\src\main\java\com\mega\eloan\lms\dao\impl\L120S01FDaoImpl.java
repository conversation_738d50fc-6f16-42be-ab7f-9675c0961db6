package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L120S01FDao;
import com.mega.eloan.lms.model.L120S01F;


/** 企金存放款外匯往來檔 **/
@Repository
public class L120S01FDaoImpl extends LMSJpaDao<L120S01F, String>
	implements L120S01FDao {

	@Override
	public L120S01F findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}
	@Override
	public List<L120S01F> findByCustIdDupId(String custId,String DupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", DupNo);
		List<L120S01F> list = createQuery(L120S01F.class,search).getResultList();
		return list;
	}
	@Override
	public List<L120S01F> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L120S01F> list = createQuery(L120S01F.class,search).getResultList();
		return list;
	}
	@Override
	public L120S01F findByUniqueKey(String mainId,String custId,String dupNo){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
	
		return findUniqueOrNone(search);
	}
	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> findL120s01f(String mainId) {
		Query query = getEntityManager().createNamedQuery(
				"c120m01b.selL120s01f");
		query.setParameter("mainId", mainId); //設置參數
		return query.getResultList();
	}
	@Override
	public int delModel(String mainId){
		Query query = getEntityManager().createNamedQuery("L120S01F.delModel");
		query.setParameter("MAINID", mainId); //設置參數
		return query.executeUpdate();
	}
}