/* 
 * C160S01G.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 分段利率明細檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "C160S01G", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "seq" }))
public class C160S01G extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 序號 **/
	@Digits(integer = 5, fraction = 0, groups = Check.class)
	@Column(name = "SEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer seq;

	/** 期別-起 **/
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "BGNNUM", columnDefinition = "DECIMAL(3,0)")
	private Integer bgnNum;

	/** 期別-迄 **/
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "ENDNUM", columnDefinition = "DECIMAL(3,0)")
	private Integer endNum;

	/** 利率基礎 **/
	@Size(max = 2)
	@Column(name = "RATETYPE", length = 2, columnDefinition = "CHAR(2)")
	private String rateType;

	/** 加減碼利率 **/
	@Digits(integer = 6, fraction = 4, groups = Check.class)
	@Column(name = "PMRATE", columnDefinition = "DECIMAL(6,4)")
	private BigDecimal pmRate;

	/**
	 * 利率方式
	 * <p/>
	 * 單選：<br/>
	 * 1.固定利率<br/>
	 * 2.機動利率<br/>
	 * 3.定期浮動
	 */
	@Size(max = 1)
	@Column(name = "RATEFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String rateFlag;

	/**
	 * 利率變動方式
	 * <p/>
	 * 單選：<br/>
	 * 1.每『月/三個月/半年/九個月/年』調整乙次<br/>
	 * 2.每年2、8月1日調整乙次<br/>
	 * 3.每年4、10月1日調整乙次
	 */
	@Size(max = 1)
	@Column(name = "RATECHGWAY", length = 1, columnDefinition = "CHAR(1)")
	private String rateChgWay;

	/**
	 * 利率變動方式(每『月/三個月/半年/九個月/年』調整乙次)
	 * <p/>
	 * 單選：<br/>
	 * 01.月<br/>
	 * 03.三個月<br/>
	 * 06.半年<br/>
	 * 09.九個月<br/>
	 * 12.年
	 */
	@Size(max = 2)
	@Column(name = "RATECHGWAY2", length = 2, columnDefinition = "CHAR(2)")
	private String rateChgWay2;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得序號 **/
	public Integer getSeq() {
		return this.seq;
	}

	/** 設定序號 **/
	public void setSeq(Integer value) {
		this.seq = value;
	}

	/** 取得期別-起 **/
	public Integer getBgnNum() {
		return this.bgnNum;
	}

	/** 設定期別-起 **/
	public void setBgnNum(Integer value) {
		this.bgnNum = value;
	}

	/** 取得期別-迄 **/
	public Integer getEndNum() {
		return this.endNum;
	}

	/** 設定期別-迄 **/
	public void setEndNum(Integer value) {
		this.endNum = value;
	}

	/** 取得利率基礎 **/
	public String getRateType() {
		return this.rateType;
	}

	/** 設定利率基礎 **/
	public void setRateType(String value) {
		this.rateType = value;
	}

	/** 取得加減碼利率 **/
	public BigDecimal getPmRate() {
		return this.pmRate;
	}

	/** 設定加減碼利率 **/
	public void setPmRate(BigDecimal value) {
		this.pmRate = value;
	}

	/**
	 * 取得利率方式
	 * <p/>
	 * 單選：<br/>
	 * 1.固定利率<br/>
	 * 2.機動利率<br/>
	 * 3.定期浮動
	 */
	public String getRateFlag() {
		return this.rateFlag;
	}

	/**
	 * 設定利率方式
	 * <p/>
	 * 單選：<br/>
	 * 1.固定利率<br/>
	 * 2.機動利率<br/>
	 * 3.定期浮動
	 **/
	public void setRateFlag(String value) {
		this.rateFlag = value;
	}

	/**
	 * 取得利率變動方式
	 * <p/>
	 * 單選：<br/>
	 * 1.每『月/三個月/半年/九個月/年』調整乙次<br/>
	 * 2.每年2、8月1日調整乙次<br/>
	 * 3.每年4、10月1日調整乙次
	 */
	public String getRateChgWay() {
		return this.rateChgWay;
	}

	/**
	 * 設定利率變動方式
	 * <p/>
	 * 單選：<br/>
	 * 1.每『月/三個月/半年/九個月/年』調整乙次<br/>
	 * 2.每年2、8月1日調整乙次<br/>
	 * 3.每年4、10月1日調整乙次
	 **/
	public void setRateChgWay(String value) {
		this.rateChgWay = value;
	}

	/**
	 * 取得利率變動方式(每『月/三個月/半年/九個月/年』調整乙次)
	 * <p/>
	 * 單選：<br/>
	 * 01.月<br/>
	 * 03.三個月<br/>
	 * 06.半年<br/>
	 * 09.九個月<br/>
	 * 12.年
	 */
	public String getRateChgWay2() {
		return this.rateChgWay2;
	}

	/**
	 * 設定利率變動方式(每『月/三個月/半年/九個月/年』調整乙次)
	 * <p/>
	 * 單選：<br/>
	 * 01.月<br/>
	 * 03.三個月<br/>
	 * 06.半年<br/>
	 * 09.九個月<br/>
	 * 12.年
	 **/
	public void setRateChgWay2(String value) {
		this.rateChgWay2 = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
