package com.mega.eloan.lms.batch.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.common.LrsUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.batch.service.LmsBatchCommonService;
import com.mega.eloan.lms.dao.L180R19HDao;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.L180R19H;
import com.mega.eloan.lms.rpt.service.LMS9511R01XLSService;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.annotation.NonTransactional;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 *  SLMS-00014 企金個人基本資料補行業別(含海外個人戶)
 * </pre>
 * 
 * @since 2013/8/13
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/8/13,007625,new
 *          </ul>
 */
@Service("RetrialBatchService")
public class RetrialBatchServiceImpl extends AbstractCapService implements
		WebBatchService {

	private static Logger LOGGER = LoggerFactory
			.getLogger(LmsBatch0002ServiceImpl.class);

	@Resource
	LmsBatchCommonService lmsBatchCommonService;

	@Resource
	MisdbBASEService misdbBASEService;

	@Resource
	LMS9511R01XLSService lms9511r01xlsService;

	@Resource
	BranchService branchService;

	@Resource
	RetrialService retrialService;

	@Resource
	L180R19HDao l180r19hDao;

	@Resource
	EloandbBASEService eloandbBASEService;

	@NonTransactional
	@Override
	public JSONObject execute(JSONObject json) {
		JSONObject result = new JSONObject();

		try {

			JSONObject request = json.getJSONObject("request");
			String type = request.getString("type");
			String qBaseDate = Util.trim(request.getString("baseDate"));

			// type : 1=產生覆審-主辦 逾期未覆審 2= 產生覆審-自辦 逾期未覆審 3= 產生覆審-主辦+自辦 逾期未覆審

			if (Util.equals(type, "1") || Util.equals(type, "3")) {
				// J-107-0342_05097_B1001 Web e-Loan授信系統新增覆審考核相關報表
				// *超重要***********************************************************************
				// 寫LMS.L180R19H、LMS.LELF412A
				// 要再另外的Service寫，要不然會受到@NonTransactional影響，最後一筆不會寫入

				// J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
				// 已含價金履約，所以不另外再弄一隻LrsUtil.CTLTYPE_價金履約
				// 主辦+價金履約
				String errMsg = this.generateL180r19Data_ctlType(qBaseDate,
						LrsUtil.CTLTYPE_主辦覆審);
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE,
							"RetrialBatchServiceImpl-generateL180r19Data_ctlType A主辦/價金履約覆審 執行失敗！==>"
									+ errMsg);
					return result;
				}

			}

			if (Util.equals(type, "2") || Util.equals(type, "3")) {
				// J-107-0342_05097_B1001 Web e-Loan授信系統新增覆審考核相關報表
				// *超重要***********************************************************************
				// 寫LMS.L180R19H、LMS.LELF412B
				// 要再另外的Service寫，要不然會受到@NonTransactional影響，最後一筆不會寫入
				String errMsg = this.generateL180r19Data_ctlType(qBaseDate,
						LrsUtil.CTLTYPE_自辦覆審);
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE,
							"RetrialBatchServiceImpl-generateL180r19Data_ctlType B自辦覆審 執行失敗！==>"
									+ errMsg);
					return result;
				}
			}

			result = WebBatchCode.RC_SUCCESS;
			result.element(WebBatchCode.P_RESPONSE,
					"RetrialBatchServiceImpl執行成功！");

		} catch (Exception ex) {
			ex.printStackTrace();
			result = WebBatchCode.RC_ERROR;
			result.element(
					WebBatchCode.P_RESPONSE,
					"RetrialBatchServiceImpl執行失敗！==>"
							+ ex.getLocalizedMessage());

		}

		return result;
	}

	/**
	 * J-107-0342_05097_B1002 Web e-Loan授信系統新增覆審考核相關報表
	 * 
	 * @param qBaseDate
	 * @param ctlType
	 * @return
	 */
	@NonTransactional
	public String generateL180r19Data_ctlType(String qBaseDate, String ctlType) {
		StringBuffer errMsgBuff = new StringBuffer("");

		List<IBranch> branchList = branchService.getAllBranch();
		List<IBranch> removeList = new ArrayList<IBranch>();
		// 組出不需要的銀行資料
		for (IBranch branch : branchList) {
			// 要排除的資料 "117","009","088","243","001","011","024","078" ,
			// Left(strtBrno,1) = "9"
			if ("009".equals(branch.getBrNo())
					|| "088".equals(branch.getBrNo())
					|| "243".equals(branch.getBrNo())
					|| "001".equals(branch.getBrNo())
					|| "011".equals(branch.getBrNo())
					|| "024".equals(branch.getBrNo())
					|| "109".equals(branch.getBrNo())
					|| "089".equals(branch.getBrNo())) {
				removeList.add(branch);
			} else if ("Y".equals(branch.getBrekFlag())) {
				removeList.add(branch);
			} else if (branch.getBrNo().startsWith("9")) {
				removeList.add(branch);
			} else if (Util.notEquals(branch.getCountryType(), "TW")) {
				removeList.add(branch);
			} else if (UtilConstants.BrNoType.國外.equals(branch.getBrNoFlag())) {
				removeList.add(branch);
			}
		}
		branchList.removeAll(removeList);

		Date baseDate = null;

		if (Util.equals(qBaseDate, "")) {
			// 沒有qBaseDate代表是排程產生
			baseDate = CapDate.shiftDays(
					Util.parseDate(CapDate.formatDate(
							CapDate.getCurrentTimestamp(), "yyyy-MM")
							+ "-01"), -1); // 上個月底
		} else {
			// 有qBaseDate代表是手動單筆產生
			baseDate = Util.parseDate(qBaseDate);
		}

		String chkDateBgn = CapDate.formatDate(baseDate, "yyyy-MM") + "-01";
		String chkDateEnd = CapDate.formatDate(baseDate, "yyyy-MM-dd");
		String docType = UtilConstants.Casedoc.DocType.企金;

		// 開始產生資料
		boolean isWriteL180R19 = true;
		String l180r19hMainId = IDGenerator.getUUID();
		Date l180r19hDataDate = baseDate;

		for (IBranch branch : branchList) {
			String brNo = branch.getBrNo();

			if (retrialService.existL180M01Z_sysMonth(brNo) == false) {
				errMsgBuff.append(brNo + "分行資料尚未匯入完成。");
				continue;
			}

			// 檢核該分行這個月是否已經產生
			// 已產生就PASS，沒產生才做

			Map<String, Object> l180r19hMap = eloandbBASEService
					.findL180r19hHasDone(docType, chkDateBgn, chkDateEnd,
							ctlType, brNo);

			if (l180r19hMap != null && !l180r19hMap.isEmpty()) {
				String tCount = Util.trim(MapUtils.getString(l180r19hMap,
						"TCOUNT", "0"));
				if (Util.parseBigDecimal(tCount).compareTo(BigDecimal.ZERO) > 0) {
					// 有999999999 代表該分行上個月底資料已經產生，就不用再執行了
					continue;
				}
			}

			// 刪除L180R19H已產生到一半的資料
			eloandbBASEService.L180R19H_deleteExistData(docType, chkDateBgn,
					chkDateEnd, ctlType, brNo);

			if (Util.equals(LrsUtil.CTLTYPE_主辦覆審, ctlType)) {

				// 主辦+價金履約

				eloandbBASEService.L180R19H_deleteExistData(docType,
						chkDateBgn, chkDateEnd, LrsUtil.CTLTYPE_價金履約, brNo);

				// 刪除LELF412A已產生到一半的資料
				eloandbBASEService.LELF412A_deleteExistData(chkDateBgn,
						chkDateEnd, brNo);

				// 刪除LELF412C已產生到一半的資料
				eloandbBASEService.LELF412C_deleteExistData(chkDateBgn,
						chkDateEnd, brNo);

				Map<String, Integer> headerMap = lms9511r01xlsService
						.getHeaderMap_flms180R01();

				// 主辦+價金履約
				List<String[]> rows = lms9511r01xlsService
						.gfnGenerateCTL_FLMS180R01(brNo, baseDate,
								headerMap.size(), isWriteL180R19,
								l180r19hMainId, l180r19hDataDate);
			} else if (Util.equals(LrsUtil.CTLTYPE_自辦覆審, ctlType)) {
				// 刪除LELF412B已產生到一半的資料
				eloandbBASEService.LELF412B_deleteExistData(chkDateBgn,
						chkDateEnd, brNo);

				Map<String, Integer> headerMap = lms9511r01xlsService
						.getHeaderMap_flms180R01B();

				List<String[]> rows = lms9511r01xlsService
						.gfnGenerateCTL_FLMS180R01B(brNo, baseDate,
								headerMap.size(), isWriteL180R19,
								l180r19hMainId, l180r19hDataDate);

			}

			// 寫LMS.L180R19H
			// 要再另外的Service寫，要不然會受到@NonTransactional影響，最後一筆不會寫入
			if (true) {

				L180R19H l180r19h = new L180R19H();

				l180r19h.setMainId(l180r19hMainId);
				l180r19h.setDataDate(l180r19hDataDate);
				l180r19h.setDocType(docType);
				l180r19h.setCtlType(ctlType);
				l180r19h.setBranch(brNo);
				l180r19h.setCustId("9999999999");
				l180r19h.setDupNo("9");
				l180r19h.setCntrNo("999999999999");
				l180r19h.setCreator("S00063"); // SLMS-00063
				l180r19h.setCreateTime(CapDate.getCurrentTimestamp());
				l180r19h.setUpdater(null);
				l180r19h.setUpdateTime(null);

				lms9511r01xlsService.saveL180r19h(l180r19h);

			}

			if (Util.equals(LrsUtil.CTLTYPE_主辦覆審, ctlType)) {
				// 主辦+價金履約
				// 因為跑gfnGenerateCTL_FLMS180R01時，主辦與價金履約一起跑了，所以就也要塞一筆LrsUtil.CTLTYPE_價金履約
				// 的 999999
				L180R19H l180r19h = new L180R19H();

				l180r19h.setMainId(l180r19hMainId);
				l180r19h.setDataDate(l180r19hDataDate);
				l180r19h.setDocType(docType);
				l180r19h.setCtlType(LrsUtil.CTLTYPE_價金履約);
				l180r19h.setBranch(brNo);
				l180r19h.setCustId("9999999999");
				l180r19h.setDupNo("9");
				l180r19h.setCntrNo("999999999999");
				l180r19h.setCreator("S00063"); // SLMS-00063
				l180r19h.setCreateTime(CapDate.getCurrentTimestamp());
				l180r19h.setUpdater(null);
				l180r19h.setUpdateTime(null);

				lms9511r01xlsService.saveL180r19h(l180r19h);
			}

		}

		return errMsgBuff.toString();
	}

}
