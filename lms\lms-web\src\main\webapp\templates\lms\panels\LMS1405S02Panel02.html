<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
    	<th:block th:fragment="LMS1405S02Panel02">
    		<form id="L140M01AForm2" name="L140M01AForm2">
                <button type="button" id="showBeforeBt">
                    <span class="text-only">
                    	<th:block th:text="#{'L140M01a.beforeContent'}"><!--顯示變更前內容--></th:block>
                    </span>
                </button>
                <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0" id="formTabTable2">
                	<tr>
						<td class="hd1" style="width:25%">
							<span style="float:left;display:none" class="caseSpan">
								<input type="checkbox" id="caseBox2_1" class="caseBox"></input>
							</span>
							<span class="text-red">＊</span>
							<th:block th:text="#{'L140M01a.type'}"><!--性質--></th:block>&nbsp;&nbsp;<br>
							<button type="button" id="loginTypeBT">
								<span class="text-only">
									<th:block th:text="#{'other.login'}"><!--登錄--></th:block>
								</span>
							</button>
						</td>
						<td colspan="3" style="width:75%">
							<input type="text" id="proPerty" name="proPerty" style="display:none"></input>
							<input type="text" size="100" maxlength="100" id="proPertyshow" name="proPertyshow" class="caseReadOnly" readonly></input>
							<span id="showFirstBossSpan" style="float:right;display:none" class="text-red">
								<th:block th:text="#{'L140M01a.firstBoss'}"><!--初貸主管--></th:block>：&nbsp;&nbsp;
								<span id="showFirstBoss" class="field"></span>
							</span>
							<button type="button" id="queryL140S05A">
								<span class="text-only">
									<th:block th:text="#{'btn.L140S05A'}"><!--變更條件項目--></th:block>
								</span>
							</button>
						</td>
                	</tr>
					<tr>
						<td class="hd1" style="width:25%" rowspan="2">
							<span class="text-red">＊</span>
							<th:block th:text="#{'L140M01a.snoKind'}"><!--額度控管種類 --></th:block>&nbsp;&nbsp;
						</td>
						<td style="width:25%">
							<select id="snoKind" name="snoKind" class="nodisabled" style="width: 250px"></select>
						</td>
						<td class="hd1" style="width:25%">
							<span style="float:left;display:none" class="caseSpan">
								<input type="checkbox" id="caseBox2_26" class="caseBox"></input>
							</span>
							<th:block th:text="#{'L140M01a.cntrNoCom'}"><!--共用額度序號--></th:block>&nbsp;&nbsp;<br>
							<button type="button" id="commonNumBt">
								<span class="text-only">
									<th:block th:text="#{'btn.number'}"><!--給號--></th:block>
								</span>
							</button>
						</td>
						<td style="width:25%">
							<span id="commSno" class="field"></span>
						</td>
					</tr>
					<tr>
						<td style="width:25%">
							●<th:block th:text="#{'L140M01a.hasLoanAssign'}"><!--本行帳務是否有聯貸拆帳作業--></th:block>&nbsp;&nbsp;
							<label>
								<input id="hasLoanAssign" name="hasLoanAssign" type="radio" value="Y" class="nodisabled"></input>
								<th:block th:text="#{'yes'}"><!--是--></th:block>
							</label>
							<label>
								<input id="hasLoanAssign" name="hasLoanAssign" type="radio" value="N" class="nodisabled"></input>
								<th:block th:text="#{'no'}"><!--否--></th:block>
							</label><br>
							<div id="showIsEfin">
								●<th:block th:text="#{'L140M01a.isEfin'}"><!--是否為供應鏈融資--></th:block>&nbsp;&nbsp;
								<label>
									<input id="isEfin" name="isEfin" type="radio" value="Y" class="nodisabled"></input>
									<th:block th:text="#{'yes'}"><!--是--></th:block>
								</label>
								<label>
									<input id="isEfin" name="isEfin" type="radio" value="N" class="nodisabled"></input>
									<th:block th:text="#{'no'}"><!--否--></th:block>
								</label><br>
							</div>
						</td>
						<!--J-110-0485_05097_B1001 於簽報書新增LGD欄位-->
						<td class="hd1" style="width:25%">
							<div class = "showArAccManager">
								<span class="text-red">＊</span>
								<th:block th:text="#{'L140M01a.arAccManagerType'}">帳款管理商類別</th:block>&nbsp;&nbsp;
							</div>	
						</td>
						<td style="width:25%">
							<div class="showArAccManager">
								<select name="arAccManagerType" id="arAccManagerType" comboKey="lms140_arAccManagerType" comboType="2" space="true" class="nodisabled"></select>
								
								<!--J-110-0485_05097_B1001 於簽報書新增LGD欄位-->
								<div class="showLgdEffect" style="display:none">
									<div id="showArAccPercent">
										<!--J-110-0485_05097_B1001 於簽報書新增LGD欄位-->
										<th:block th:text="#{'L140M01a.arAccPercent'}">帳款管理商保證成數</th:block>
										<input type="text" id="arAccPercent" name="arAccPercent" class="numeric" maxlength="6" size="6" integer="3" fraction="2"></input>％<br>
										<!--J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊 
											<wicket:message key="L140M01a.arAccManagerId">帳款管理商ID</wicket:message>&nbsp;&nbsp;：
											<input type="text" id="arAccManagerId" name="arAccManagerId" class="nodisabled" size="12" maxlength="11" />
											<br>
											<wicket:message key="L140M01a.arAccManagerNm">帳款管理商名稱</wicket:message>&nbsp;&nbsp;：
											<input type="text" id="arAccManagerNm" name="arAccManagerNm" class="nodisabled" size="40" maxlength="120" maxlengthC="40" />
										-->
									</div>
								</div>
							</div>
						</td>
					</tr>
					<tr>
						<td class="hd1">
							<span class="text-red">＊</span>
							<th:block th:text="#{'L140M01a.cntrNo'}"><!--額度序號--></th:block>&nbsp;&nbsp;<br>
							<button type="button" id="newFcltNo">
								<span class="text-only">
									<th:block th:text="#{'btn.number'}"><!--給號--></th:block>
								</span>
							</button>
						</td>
						<td>
							<input id="cntrNo" name="cntrNo" class="caseReadOnly" readonly="readonly" size="13"></input>
							<span id="showCntrNoName"></span>
						</td>
						<td class="hd1">
							<th:block th:text="#{'L140M01a.cntrNo1'}"><!--原始額度序號--></th:block>&nbsp;&nbsp;<br>
							<th:block th:text="#{'L140M01a.cntrNo2'}"><!--原始控管種類--></th:block>&nbsp;&nbsp;
						</td>
						<td></td>
					</tr>
					<tr>
						<td class="hd1">
							<span style="float:left;display:none" class="caseSpan">
								<input type="checkbox" id="caseBox2_2" class="caseBox"></input>
							</span>
							<span class="text-red">＊</span>
							<th:block th:text="#{'L140M01a.sbjProperty'}"><!--額度性質--></th:block>&nbsp;&nbsp;
						</td>
						<td>
							<select id="sbjProperty" name="sbjProperty"></select>
						</td>
						<td class="hd1">
							<span style="float:left;display:none" class="caseSpan">
								<input type="checkbox" id="caseBox2_24" class="caseBox"></input>
							</span>
							<th:block th:if="${_panel_fieldRating_old1}">
								<span class="text-red">＊</span>
								<th:block th:text="#{'L140M01a.facilityRating'}"><!--額度評等--></th:block>
							</th:block>
							<th:block th:if="${_panel_fieldRating_new1}">
							</th:block>&nbsp;&nbsp;
						</td>
						<td>
							<th:block th:if="${_panel_fieldRating_old2}">
								<input type="text" id="facilityRating" name="facilityRating" size="30" maxlength="120" maxlengthC="100"></input>
							</th:block>
							<th:block th:if="${_panel_fieldRating_new2}">
								<input type="hidden" id="facilityRating" name="facilityRating"></input>&nbsp;
							</th:block>
						</td>
					</tr>
					<!--BGN J-104-0219-001 修改e-Loan授信管理系統企金額度明細表申請內容之「額度性質」欄，有關為無擔保授信不計入限額註記項目之「說明」及其下拉選項。-->
					<tr>
						<td class="hd1" style="width:25%">
							<span style="float:left;display:none" class="caseSpan">
								<input type="checkbox" id="caseBox2_25" class="caseBox"></input>
							</span>
							<th:block th:text="#{'L140M01a.noLoan'}"><!--不計入同一關係戶代號--></th:block>&nbsp;&nbsp;
						</td>
						<td colspan="3" style="width:75%">
							<select name="noLoan" id="noLoan" style="width: 600px;"></select>
						</td>
					</tr>
					<tr id="unsecureFlagSpan">
						<td class="hd1">
							<span style="float:left;display:none" class="caseSpan">
								<input type="checkbox" id="caseBox2_26" class="caseBox"></input>
							</span>
							<span class="text-red">＊</span>
							<th:block th:text="#{'L140M01a.unsecureFlag'}"><!--利害關係人敘作無擔保授信註記--></th:block>&nbsp;&nbsp;
						</td>
						<td colspan="3">
							<span class="text-red">
								＊<th:block th:text="#{'L140M01a.message96'}">說明：「授信戶為銀行法/金控法利害關係人且本筆授信額度性質為無擔保，請勾選所符合之下列項目」</th:block><br>
								<select name="unsecureFlag" id="unsecureFlag"></select>
								<!--class="required"-->
							</span>
						</td>
					</tr>
					<!--END J-104-0219-001 修改e-Loan授信管理系統企金額度明細表申請內容之「額度性質」欄，有關為無擔保授信不計入限額註記項目之「說明」及其下拉選項。-->
					<tr>
						<td class="hd1">
							<span style="float:left;display:none" class="caseSpan">
								<input type="checkbox" id="caseBox2_3" class="caseBox"></input>
							</span>
							<span class="text-red">＊</span>
							<th:block th:text="#{'L140M01c.item'}"><!--授信科目--></th:block>&nbsp;&nbsp;<br>
							<button id="itemTypeSelect" type="button">
								<span class="text-only">
									<th:block th:text="#{'other.login'}"><!--登錄--></th:block>
								</span>
							</button>
						</td>
						<td colspan="3">
							<span id="lnSubject" name="lnSubject" class="field"></span>
							<!-- 授信科目 -->
						</td>
					</tr>
					<!--G-113-0145  授信新做額度於eloan簽報核准後，自動傳送AS400執行3X02，以利央行RDT報表傳送。-->
					<tr class="onlyForThailand">
						<td class="hd1">
							<th:block th:text="#{'L140M01A.appNo'}"><!--申請案件號碼--></th:block><br>
							<button type="button" id="appNoBT">
								<span class="text-only">
									<th:block th:text="#{'other.login'}"><!--登錄--></th:block>
								</span>
							</button>
						</td>
						<td colspan="3">
							<input type="text" id="appNo" name="appNo"></input>
						</td>
					</tr>
					<tr class="onlyForThailand">
						<td class="hd1">
							<th:block th:text="#{'L140M01A.loanAndContType'}"><!--授信用途科目別--></th:block><br>
							<button type="button" id="loanAndContTypeBT">
								<span class="text-only">
									<th:block th:text="#{'other.login'}"><!--登錄--></th:block>
								</span>
							</button>
						</td>
						<td colspan="3">
							<input type="text" id="loanAndContType" name="loanAndContType" style="display:none"></input>
							<textarea id="loanAndContTypeShow" name="loanAndContTypeShow" cols="100" rows="4" class="caseReadOnly" readonly></textarea>
						</td>
					</tr>
					<th:block th:if="${_panel_fieldRating_old3}"></th:block>
					<th:block th:if="${_panel_fieldRating_new3}">
						<tr>
							<td class="hd1">
								<th:block th:text="#{'label.combineRatingInfo'}">信用評等</th:block>&nbsp;&nbsp;
							</td>
							<td colspan="3">
								<span id="l140m01a_cls_grade" class="field"></span>
							</td>
						</tr>
					</th:block>
					<tr id="derivEvalSpan">
						<td class="hd1">
							<span style="float:left;display:none" class="caseSpan">
								<input type="checkbox" id="caseBox2_31" class="caseBox"></input>
							</span>
							<span class="text-red">＊</span>
							<th:block th:text="#{'L140M01a.derivEval'}"></th:block>
						</td>
						<td colspan="3">
							<select id="derivEval" name="derivEval" class="nodisabled"></select>
						</td>
					</tr>
					<tr>
						<td class="hd1">
							<span style="float:left;display:none" class="caseSpan">
								<input type="checkbox" id="caseBox2_23" class="caseBox"></input>
							</span>
							<th:block th:text="#{'L140M01a.derivatives'}"><!--衍生性金融商品期數--></th:block>&nbsp;&nbsp;
							<div id="showLoginDervBT">
								<button type="button" id="loginDervBT">
									<span class="text-only">
										<th:block th:text="#{'other.login'}"><!--登錄--></th:block>
									</span>
								</button>
							</div>
							<!--N-105-0127-001 Web e-Loan配合衍生性金融商品風險係數修改-->
							<div id="showLoginDervBT_N1050127" style="display:none">
								<button type="button" id="loginDervBT_N1050127">
									<span class="text-only">
										<th:block th:text="#{'other.login'}"><!--登錄--></th:block>
									</span>
								</button>
							</div>
						</td>
						<td>
							<!-- <select id="derivatives" name="derivatives" class="nodisabled" multiple="multiple" />  -->
							<input type="text" id="derivatives" name="derivatives" style="display:none"></input>
							<textarea id="derivativeShow" name="derivativeShow" class="caseReadOnly" cols="30" rows="5" readonly></textarea>
							<!--N-105-0127-001 Web e-Loan配合衍生性金融商品風險係數修改-->
							<span id="derivativeVersion" class="field" style="display:none"></span>
						</td>
						<td class="hd1">
							<th:block th:text="#{'L140M01a.derivativesNum'}"><!--風險係數 --></th:block>&nbsp;&nbsp;
						</td>
						<td>
							<!-- <input type="text" id="derivativesNum" name="derivativesNum" class="number caseReadOnly" readonly/> -->
							<input type="text" id="derivativesNumDscr" name="derivativesNumDscr" class="caseReadOnly" readonly></input>%
							<input id="isDerivatives" name="isDerivatives" style="display:none"></input>
						</td>
					</tr>
					<tr>
						<td class="hd1">
							<span class="text-red">＊</span>
							<th:block th:text="#{'L140M01c.lmtDays'}"><!--清償期限--></th:block>&nbsp;&nbsp;
						</td>
						<td colspan="3">
							<!--<input id="payDeadline" name="payDeadline" size="100" class="caseReadOnly" readonly/>-->
							<textarea id="payDeadline" name="payDeadline" cols="80" rows="3" class="caseReadOnly" readonly></textarea>
						</td>
					</tr>
					<tr>
						<td class="hd1">
							<span style="float:left;display:none" class="caseSpan">
								<input type="checkbox" id="caseBox2_4" class="caseBox"></input>
							</span>
							<th:block th:text="#{'L140M01a.lastValue'}"><!--前准額度--></th:block>&nbsp;&nbsp;
						</td>
						<td colspan="2">
							<th:block th:text="#{'L782M01A.applyCurr'}"><!--幣別--></th:block>：<select id="LVCurr" name="LVCurr" class="money"></select>
							<th:block th:text="#{'L140M01h.cp1Fee'}"><!--金額--></th:block>：<input type="text" id="LVAmt" name="LVAmt" size="18" maxlength="22" integer="15" fraction="2" class="numeric"></input>
							(<th:block th:text="#{'other.sign'}"><!--單位--></th:block>：<th:block th:text="#{'other.money'}"><!--元--></th:block>)
						</td>
						<td rowspan="3">
							<div align="right">
								<label>
									<input type="checkbox" id="lastValueRefP1" name="lastValueRefP1" value="1" class="nodisabled"></input>
									<th:block th:text="#{'L140M01a.page'}"><!--同前頁--></th:block>
								</label>
							</div>
						</td>
					</tr>
					<tr>
						<td class="hd1">
							<span style="float:left;display:none" class="caseSpan">
								<input type="checkbox" id="caseBox2_17" class="caseBox"></input>
							</span>
							<th:block th:text="#{'L140M01a.LV2Amt'}"><!--前准批覆授信額度--></th:block>&nbsp;&nbsp;
						</td>
						<td colspan="2">
							<th:block th:text="#{'L782M01A.applyCurr'}"><!--幣別--></th:block>：<select id="LV2Curr" name="LV2Curr" class="money"></select>
							<th:block th:text="#{'L140M01h.cp1Fee'}"><!--金額--></th:block>：<input type="text" id="LV2Amt" name="LV2Amt" size="18" maxlength="22" integer="15" fraction="2" class="numeric"></input>
							(<th:block th:text="#{'other.sign'}"><!--單位--></th:block>：<th:block th:text="#{'other.money'}"><!--元--></th:block>)
						</td>
					</tr>
					<tr>
						<td class="hd1">
							<span style="float:left;display:none" class="caseSpan">
								<input type="checkbox" id="caseBox2_18" class="caseBox"></input>
							</span>
							<th:block th:text="#{'L140M01a.LVAssureAmt'}"><!--前准批覆擔保授信額度--></th:block>&nbsp;&nbsp;
						</td>
						<td colspan="2">
							<th:block th:text="#{'L782M01A.applyCurr'}"><!--幣別--></th:block>：<select id="LVAssureCurr" name="LVAssureCurr" class="money"></select>
							<th:block th:text="#{'L140M01h.cp1Fee'}"><!--金額--></th:block>：<input type="text" id="LVAssureAmt" name="LVAssureAmt" size="18" maxlength="22" integer="15" fraction="2" class="numeric"></input>
							(<th:block th:text="#{'other.sign'}"><!--單位--></th:block>：<th:block th:text="#{'other.money'}"><!--元--></th:block>)
						</td>
					</tr>
					<tr>
						<td class="hd1">
							<span style="float:left;display:none" class="caseSpan">
								<input type="checkbox" id="caseBox2_5" class="caseBox"></input>
							</span>
							<th:block th:text="#{'L140M01a.bLAmt'}"><!--餘額--></th:block>&nbsp;&nbsp;
						</td>
						<td>
							<th:block th:text="#{'L782M01A.applyCurr'}"><!--幣別--></th:block>：<select id="BLCurr" name="BLCurr" class="money"></select>
							<th:block th:text="#{'L140M01h.cp1Fee'}"><!--金額--></th:block>：<input type="text" id="BLAmt" name="BLAmt" size="18" maxlength="22" integer="15" fraction="2" class="numeric"></input>
							(<th:block th:text="#{'other.sign'}"><!--單位--></th:block>：<th:block th:text="#{'other.money'}"><!--元--></th:block>)
						</td>
						<!--J-110-0485_05097_B1001 於簽報書新增LGD欄位-->
						<!--J-110-0485_05097_B1002 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能-->
						<!--2022-01-19 研議e-Loan授信管理系統之LGD及額度暴險估算規則相關議題-->
						<td class="hd1">
							<!--J-110-0485_05097_B1001 於簽報書新增LGD欄位-->
							<div style="display:none">
								<!--class="showLgdEffect" -->
								<span style="float:left;display:none" class="caseSpan">
									<input type="checkbox" id="caseBox2_29" class="caseBox"></input>
								</span>
								<th:block th:text="#{'L140M01a.rcvInt'}">應收利息金額</th:block>&nbsp;&nbsp;
							</div>
						</td>
						<td>
							<div style="display:none">
								<!--class="showLgdEffect" -->
								<select id="rcvCurr" name="rcvCurr" class="money"></select>
								<input type="text" id="rcvInt" name="rcvInt" size="18" maxlength="22" integer="15" fraction="2" class="numeric"></input>
								<th:block th:text="#{'other.money'}"><!--元--></th:block>
							</div>
						</td>
					</tr>
					<tr>
						<td class="hd1">
							<span style="float:left;display:none" class="caseSpan">
								<input type="checkbox" id="caseBox2_6" class="caseBox"></input>
							</span>
							<th:block th:text="#{'L140M01a.lCAmt'}"><!--購料放款案下--></th:block>&nbsp;&nbsp;<br>
							<th:block th:text="#{'L140M01a.lCAmt2'}"><!--已開狀未到單金額--></th:block>&nbsp;&nbsp;
						</td>
						<td colspan="3">
						<th:block th:text="#{'L782M01A.applyCurr'}"><!--幣別--></th:block>：<select id="LCCurr" name="LCCurr" class="money"></select>
							<th:block th:text="#{'L140M01h.cp1Fee'}"><!--金額--></th:block>：<input type="text" id="LCAmt" name="LCAmt" size="18" maxlength="22" integer="15" fraction="2" class="numeric"></input>
							(<th:block th:text="#{'other.sign'}"><!--單位--></th:block>：<th:block th:text="#{'other.money'}"><!--元--></th:block>)
						</td>
					</tr>
					<tr>
						<!--J-106-0232-001 Web e-Loan國內、海外企金授信衍生性金融商品額度明細表新增淨值與額外信用增強-->
						<!--J-111-0461_05097_B1002 授信額度合計新增瑕疵押匯額度、與合計總授信額度(授信額度+出口瑕疵額度)-->
						<td class="hd1" rowspan="3">
							<span style="float:left;display:none" class="caseSpan">
								<input type="checkbox" id="caseBox2_7" class="caseBox"></input>
							</span>
							<span class="text-red">＊</span>
							<th:block th:text="#{'L140M01a.moneyAmt'}"><!--現請額度--></th:block>&nbsp;&nbsp;
						</td>
						<td colspan="2">
							<select id="currentApplyCurr" name="currentApplyCurr" class="money"></select>
							<input type="text" id="currentApplyAmt" name="currentApplyAmt" size="18" maxlength="22" integer="15" fraction="2" class="numeric"></input>
							<th:block th:text="#{'other.money'}"><!--元--></th:block>
							<select id="reUse" name="reUse"></select><br>
							<!--<span class="text-red">※<wicket:message key="L140M01a.message23">若為「條件變更」但並未變更額度時，此欄需填原准額度</wicket:message></span>-->
							<br><br><select id="otherCurr" name="otherCurr"></select><br>
							<span class="text-red">
								※<th:block th:text="#{'L140M01a.message24'}"><!--若未勾選則動用只限定所報請的幣別，不得動用其他貨幣--></th:block>！
							</span>
						</td>
						<td rowspan="2">
							<div align="right">
								<label>
									<input type="checkbox" id="CurrentAppRef" name="CurrentAppRef" value="1" class="nodisabled"></input>
									<th:block th:text="#{'L140M01a.page'}"><!--同前頁--></th:block>
								</label>
							</div>
						</td>
					</tr>
					<!--J-106-0232-001 Web e-Loan國內、海外企金授信衍生性金融商品額度明細表新增淨值與額外信用增強-->
					<tr>
						<td colspan="2">
							<div class="showNoneHedge" style="display:none">
								<span class="text-red">＊</span>
								<th:block th:text="#{'L140M01a.enhanceAmt'}"><!--非避險額度之額外信用增強額度--></th:block>&nbsp;&nbsp;：
								<input type="text" id="enhanceAmt" name="enhanceAmt" size="18" maxlength="22" integer="15" fraction="2" class="numeric nodisabled"></input>
								<th:block th:text="#{'other.money'}"><!--元--></th:block><br> 
								(<th:block th:text="#{'L140M01a.enhanceAmtMemo'}"><!--衍生性金融商品當交易目的為非避險時，現請額度=自身淨值額度＋額外信用增強額度--></th:block>)
							</div>
						</td>
					</tr>
					<!--J-111-0461_05097_B1002 授信額度合計新增瑕疵押匯額度、與合計總授信額度(授信額度+出口瑕疵額度)-->
					<!--J-108-0302-001 是否符合出口實績規範-->
					<tr>
						<td id="show_Experf">
							<th:block th:text="#{'L140M01a.experf_fg'}">是否符合出口實績規範</th:block>&nbsp;&nbsp;：
							<label>
								<input id="experf_fg" name="experf_fg" type="radio" value="Y" class="nodisabled"></input>
								<th:block th:text="#{'yes'}"></th:block>
								<input name="experf_fg" type="radio" value="N" class="nodisabled"></input>
								<th:block th:text="#{'no'}"></th:block>
							</label><br>
							<th:block th:text="#{'L140M01a.flaw_fg'}">瑕疵額度控管方式</th:block>&nbsp;&nbsp;：<select id="flaw_fg" name="flaw_fg" class="nodisabled"></select>
							<br><br>
							<span class="text-red">
								<th:block th:text="#{'L140M01a.experf_tips'}"></th:block>
							</span>
						</td>
						<td id="show_flaw_amt">
							<th:block th:text="#{'L140M01a.flaw_amt'}">瑕疵單據押匯限額</th:block>&nbsp;&nbsp;：<br>
							<input type="text" name="flaw_amt" id="flaw_amt" integer="13" fraction="2" size="16" class="numeric"></input>
							<th:block th:text="#{'other.money'}"><!--元--></th:block>
						</td>
					</tr>
					<tr id="countSayTr" style="display:none">
						<td class="hd1">
							<span style="float:left;display:none" class="caseSpan">
								<input type="checkbox" id="caseBox2_8" class="caseBox"></input>
							</span>
							<th:block th:text="#{'L140M01a.countSay'}"><!--聯貸說明--></th:block>：&nbsp;&nbsp;
						</td>
						<td colspan="3">
							<textarea id="countSay" name="countSay" cols="70" rows="5" class="max txt_mult" maxlengthC="340"></textarea>
						</td>
					</tr>
					<tr>
						<td class="hd1" rowspan="3">
							<span style="float:left;display:none" class="caseSpan">
								<input type="checkbox" id="caseBox2_19" class="caseBox"></input>
							</span>
							<th:block th:text="#{'L140M01a.collectPay'}"><!--借款收付彙計數--></th:block>&nbsp;&nbsp; <br>
							<button type="button" id="collectBT">
								<span class="text-only" style="width: 80px">
									<th:block th:text="#{'other.login'}"><!--登錄--></th:block>
								</span>
							</button><br>
							<button type="button" id="inculeUsePar">
								<span class="text-only" style="width: 80px">
									<th:block th:text="#{'L140M01a.message89'}"><!--引進平均動用率(資簡)--></th:block>
								</span>
							</button><br>
							<button type="button" id="inculeUseParAloan">
								<span class="text-only" style="width: 80px">
									<th:block th:text="#{'L140M01a.message89A'}"><!--引進平均動用率(帳務)--></th:block>
								</span>
							</button>
						</td>
						<td colspan="2">
							<span>(<th:block th:text="#{'L140M01a.Pay'}"><!--付--></th:block>)</span>
							<table width="60%" border="0" cellspacing="0" cellpadding="0" id="CPTable">
								<tr>
									<td><span id="CPCURR" class="field"></span></td>
									<td class="pghead"><span id="CPAMT" class="field"></span></td>
								</tr>
							</table>
						</td>
						<td rowspan="3">
							<div align="right">
								<label>
									<input type="checkbox" id="collectPayRef" name="collectPayRef" value="1" class="nodisabled"></input>
									<th:block th:text="#{'L140M01a.page'}"><!--同前頁--></th:block>
								</label>
							</div>
						</td>
					</tr>
					<tr>
						<td colspan="2">
							<div>(<th:block th:text="#{'L140M01a.Accept'}"><!--收--></th:block>)</div>
							<table width="60%" border="0" cellspacing="0" cellpadding="0" id="CATable">
								<tr>
									<td><span id="CACURR" class="field"></span></td>
									<td class="pghead"><span id="CAAMT" class="field"></span></td>
								</tr>
							</table>
						</td>
					</tr>
					<tr>
						<td colspan="2">
							<th:block th:text="#{'L140M01a.message97'}">資料基準日</th:block>:
							<input type="text" name="useParDate" id="useParDate" class="date"></input>&nbsp;&nbsp;&nbsp;&nbsp;
							<th:block th:text="#{'L140M01a.message87'}">平均動用率</th:block>:
							<input type="text" name="usePar" id="usePar" class="numeric" positiveonly="false" integer="3" fraction="2" maxlength="6" size="7"></input>
						</td>
					</tr>
					<tr>
						<td class="hd1">
							<span style="float:left;display:none" class="caseSpan">
								<input type="checkbox" id="caseBox2_9" class="caseBox"></input>
							</span>
							<span class="text-red">＊</span>
							<th:block th:text="#{'L140M01a.useDeadline'}"><!--動用期限--></th:block>&nbsp;&nbsp;
						</td>
						<td colspan="3">
							<select id="useDeadline" name="useDeadline"></select>
							<input type="text" id="desp1" name="desp1" style="display:none"></input>
							<input type="text" id="desp2" name="desp2" style="display:none"></input>
							<span id="moveDurOtfromSpan" style="display:none">
								<input type="text" id="moveDurOtFrom" name="moveDurOtFrom" size="10"></input> ~ <input type="text" id="moveDurOtEnd" name="moveDurOtEnd" size="10"></input>
							</span>
						</td>
					</tr>
					<tr id="clsLgdInfoTr" style="display:none;">
						<td class="hd1">
							<th:block th:text="#{'L140M01A.clsLgdInfo'}"><!--個人戶額度違約損失率資訊--></th:block>&nbsp;&nbsp;
						</td>
						<td colspan="3">
							<p id="clsLgdInfo"></p>
							<div id="expectLgdDesc"></div>
						</td>
					</tr>
					<tr>
						<td class="hd1">
							<th:block th:text="#{'L140M01a.itemC'}">風險權數</th:block>(<th:block th:text="#{'L140M01a.itemC1'}">抵減前</th:block>)&nbsp;&nbsp;
						</td>
						<td>
							<input type="text" id="itemC" name="itemC" size="10" maxlength="18" class="numeric nodisabled" integer="13" fraction="2"></input>%<br>
						</td>
						<td class="hd1">
							<th:block th:text="#{'L140M01a.itemC'}">風險權數</th:block>(<th:block th:text="#{'L140M01a.itemC2'}">抵減後</th:block>)&nbsp;&nbsp;
						</td>
						<td>
							<input type="text" id="rItemD" name="rItemD" size="10" maxlength="18" class="numeric nodisabled" integer="13" fraction="2"></input>%
						</td>
					</tr>
					<tr>
						<td class="hd1">
							<span class="text-red">＊</span>
							<th:block th:text="#{'L140M01a.nPL'}"><!--分行逾放比--></th:block>&nbsp;&nbsp;
						</td>
						<td colspan="3">
							<input type="text" id="npl" name="npl" class="nodisabled" size="90" maxlength="120" maxlengthC="100"></input><br>
							<th:block th:text="#{'other.dateYYYYMM'}"><!--資料年月--></th:block>
							<input type="text" id="npldate" name="npldate" size="6" maxlength="7" class="nodisabled"></input><br>
							<button type="button" id="getNPL" class="noHideBt">
								<span class="text-only">
									<th:block th:text="#{'btn.NPL'}"><!--引進上月底逾期放款分析表--></th:block>
								</span>
							</button><br>
							<span class="text-red">
								※<th:block th:utext="#{'L140M01a.message46'}"><!--請參考報表查詢系統月初產生之「LLMLN091 逾期放款分析表」--></th:block>(DBU+OBU)
							</span>
						</td>
					</tr>
					<tr>
						<td class="hd1">
							<span style="float:left;display:none" class="caseSpan">
								<input type="checkbox" id="caseBox2_11" class="caseBox"></input>
							</span>
							<th:block th:text="#{'title.14'}"><!--本票--></th:block>&nbsp;&nbsp;<br>
							<button type="button" id="localPageBT">
								<span class="text-only">
									<th:block th:text="#{'btn.word'}"><!--詞庫--></th:block>
								</span>
							</button>
						</td>
						<td colspan="2">
							<textarea class="max txt_mult" rows="2" cols="55" id="checkNote" name="checkNote" maxlength="1050" maxlengthC="350"></textarea>
						</td>
						<td>
							<div align="right">
								<label>
									<input type="checkbox" id="checkNoteRef" name="checkNoteRef" value="1" class="nodisabled"></input>
									<th:block th:text="#{'L140M01a.page'}"><!--同前頁--></th:block>
								</label>
							</div>
						</td>
					</tr>
					<tr>
						<td class="hd1">
							<span style="float:left;display:none" class="caseSpan">
								<input type="checkbox" id="caseBox2_12" class="caseBox"></input>
							</span>
							<span class="text-red">＊</span>
							<th:block th:text="#{'L140M01is02.guarantor'}"><!--保證人--></th:block>&nbsp;&nbsp;<br>
							<!--J-107-0206_05097_B1001 Web e-Loan海外版企金授信簽報書保證人部分比照國內版提供一般保證人及連帶保證人選項以供選擇,以正確呈現保證關係-->
							<select name="guarantorType" id="guarantorType" comboKey="lms140_guarantorType" comboType="2"></select>
							<!--<wicket:message key="L140M01a.conPersonNew">連保人</wicket:message>&nbsp;&nbsp;-->
							<br>
							<button type="button" id="toglePersonBT">
								<span class="text-only">
									<th:block th:text="#{'other.login'}"><!--other.login--></th:block>
								</span>
							</button>
						</td>
						<td>
							<div>
								<b><th:block th:text="#{'L140M01a.message136'}"><!--保證人是否按一定比率負担保證責任--></th:block>?</b>
								<label>
									<input type="radio" id="guaPercentFg" name="guaPercentFg" value="Y" class="caseReadOnly" readonly></input>
									<th:block th:text="#{'yes'}"><!--是--></th:block>
								</label>
								<label>
									<input type="radio" name="guaPercentFg" value="N" class="caseReadOnly" readonly></input>
									<th:block th:text="#{'no'}"><!--否--></th:block>
								</label>
								<!--J-110-0040_05097_B1001 Web e-Loan增加「本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)」註記-->
								<br>
								<!--J-110-0040-TODO-->
								<b><th:block th:text="#{'L140M01a.message269'}">本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)</th:block>?</b>
								<label>
									<input type="radio" id="guaNaExposure" name="guaNaExposure" value="Y" class="caseReadOnly" readonly></input>
									<th:block th:text="#{'yes'}"><!--是--></th:block>
								</label>
								<label>
									<input type="radio" name="guaNaExposure" value="N" class="caseReadOnly" readonly></input>
									<th:block th:text="#{'no'}"><!--否--></th:block>
								</label>
							</div>
							<textarea id="guarantor" name="guarantor" class="caseReadOnly" rows="7" maxlength="1800" maxlengthC="600" readonly></textarea>
							<!--J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式-->
							<br>
							<label>
								<input type="checkbox" id="guarantorRef" name="guarantorRef" value="1" class="nodisabled"></input>
								<th:block th:text="#{'L140M01a.page'}">同前頁</th:block>
							</label>
						</td>
						<td class="hd1">
							<!--J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式-->
							<span style="float:left;display:none" class="caseSpan">
								<input type="checkbox" id="caseBox2_28" class="caseBox"></input>
							</span>
							<span class="text-red">＊</span>
							<th:block th:text="#{'L140M01a.guarantor1'}"><!--物上保證人--></th:block>&nbsp;&nbsp;<br>
							<button type="button" id="toglePersonBT1">
								<span class="text-only">
									<th:block th:text="#{'other.login'}"><!--other.login--></th:block>
								</span>
							</button>
						</td>
						<td>
							<!--J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式-->
							<textarea id="guarantor1" name="guarantor1" class="caseReadOnly" rows="7" maxlength="900" maxlengthC="300" readonly></textarea><br>
							<label>
								<input type="checkbox" id="guarantor1Ref" name="guarantor1Ref" value="1" class="nodisabled"></input>
								<th:block th:text="#{'L140M01a.page'}">同前頁</th:block>
							</label>
						</td>
					</tr>
					<tr>
						<td class="hd1">
							<span style="float:left;display:none" class="caseSpan">
								<input type="checkbox" id="caseBox2_21" class="caseBox"></input>
							</span>
							<span class="text-red">＊</span>
							<th:block th:text="#{'L140M01a.message86'}">共同借款人</th:block><br>
							<button type="button" id="commonBorrowerBt">
								<span class="text-only">
									<th:block th:text="#{'other.login'}"><!--other.login--></th:block>
								</span>
							</button>
						</td>
						<td colspan="3">
							<span id="l140m01jStr" class="field"></span>
						</td>
					</tr>
					<tr id="connectionAcTr" style="display:none">
						<td class="hd1">
							<span style="float:left;display:none" class="caseSpan">
								<input type="checkbox" id="caseBox2_25" class="caseBox"></input>
							</span>
							<th:block th:text="#{'L140M01a.connectionAc'}"><!--關連戶--></th:block>：&nbsp;&nbsp;
						</td>
						<td colspan="3">
							<textarea id="connectionAc" name="connectionAc" cols="74" rows="5" class="max txt_mult" maxlength="900" maxlengthC="300"></textarea>
						</td>
					</tr>
					<tr>
						<td class="hd1">
							<span style="float:left;display:none" class="caseSpan">
								<input type="checkbox" id="caseBox2_13" class="caseBox"></input>
							</span>
							<th:block th:text="#{'L140M01a.noInsuReason'}"><!--本案未送保原因--></th:block>&nbsp;&nbsp;
						</td>
						<td>
							<select id="noInsuReason" name="noInsuReason" style="width:200px"></select>
							<span id="noInsuReasonOtherSpan" style="display:none">
								<textarea id="noInsuReasonOther" name="noInsuReasonOther" maxlength="900" maxlengthC="300"></textarea>
								<span id="hideMark" style="display:none">%</span>
							</span>
						</td>
						<td class="hd1">
							<span style="float:left;display:none" class="caseSpan">
								<input type="checkbox" id="caseBox2_14" class="caseBox"></input>
							</span>
							<th:block th:text="#{'L140M01a.assureTotECurr'}"><!--擔保授信額度調整--></th:block>&nbsp;&nbsp;<br>
							<button type="button" id="sayBt" class="forview">
								<span class="text-only">
									<th:block th:text="#{'L140M01a.say'}"><!--L140M01a.say=說明--></th:block>
								</span>
							</button>
						</td>
						<td>
							<span id="assureTotECurr" class="field"></span>&nbsp;
							<input type="text" id="assureTotEAmt" name="assureTotEAmt" size="18" maxlength="22" integer="15" fraction="2" class="numeric"></input>
							<th:block th:text="#{'other.money'}"><!--元--></th:block>
						</td>
					</tr>
					<tr>
						<td class="hd1">
							<span style="float:left;display:none" class="caseSpan">
								<input type="checkbox" id="caseBox2_22" class="caseBox"></input>
							</span>
							<th:block th:text="#{'L140M01a.chinaLoan'}">大陸地區授信業務控管註記</th:block>
						</td>
						<td>
							<button type="button" id="queryL140M01Q" class="forview">
								<span class="text-only">
									<th:block th:text="#{'button.search'}">查詢</th:block>
								</span>
							</button>
							<button type="button" id="modifyL140M01Q">
								<span class="text-only">
									<th:block th:text="#{'button.modify'}">修改</th:block>
								</span>
							</button><br>
							<select name="pageNumY" id="pageNumY">
								<option value="0" disabled>
									<th:block th:text="#{'L140M01b.printMain'}"><!--印於主表--></th:block>
								</option>
								<option value="1" disabled>
									<th:block th:text="#{'L140M01b.print01'}"><!--印於附表(一)--></th:block>
								</option>
								<option value="2" disabled>
									<th:block th:text="#{'L140M01b.print02'}"><!--印於附表(二)--></th:block>
								</option>
								<option value="3" disabled>
									<th:block th:text="#{'L140M01b.print03'}"><!--印於附表(三)--></th:block>
								</option>
								<option value="4" selected="selected">
									<th:block th:text="#{'L140M01b.print04'}"><!--印於檢核表--></th:block>
								</option>
							</select>
						</td>
					</tr>
					<!--後來新增的部分-->
					<tr>
						<td class="hd1">
							<th:block th:text="#{'L140M01a.LVTotAmt'}"><!--前准額度合計--></th:block>&nbsp;&nbsp;<br>
							<span class="text-red">
								<th:block th:text="#{'L140M01a.theNumer'}"><!--本數字由--></th:block>
							</span>&nbsp;&nbsp;<br>
							<span class="text-red">
								<th:block th:text="#{'L140M01a.LVTotAmt2'}"><!--【前准批覆授信額度】合計--></th:block>
							</span>&nbsp;&nbsp;
						</td>
						<td class="rt">
							<span id="LVTotCurr" class="field"></span>&nbsp;&nbsp;
							<span id="LVTotAmt" class="field"></span>&nbsp;
							<th:block th:text="#{'other.money'}"><!--元--></th:block>
						</td>
						<td class="hd1">
							<th:block th:text="#{'L140M01a.count2'}"><!--其中擔保合計--></th:block>&nbsp;&nbsp;<br>
							<span class="text-red">
								<th:block th:text="#{'L140M01a.theNumer'}"><!--本數字由--></th:block>
							</span>&nbsp;&nbsp;<br>
							<span class="text-red">
								<th:block th:text="#{'L140M01a.LVAssTotAmt'}"><!--【前准批覆擔保授信額度】合計--></th:block>
							</span>&nbsp;&nbsp;
						</td>
						<td class="rt">
							<span id="LVAssTotCurr" class="field"></span>&nbsp;&nbsp;
							<span id="LVAssTotAmt" class="field"></span>&nbsp;
							<th:block th:text="#{'other.money'}"><!--元--></th:block>
						</td>
					</tr>
					<tr>
						<td class="hd1">
							<th:block th:text="#{'L140M01a.incApplyTotAmt'}"><!--新做、增額合計--></th:block>&nbsp;&nbsp;<br>
							<span class="text-red">
								<th:block th:text="#{'L140M01a.incApplyTotAmt2'}"><!--新作、增額合計 = 授信額度合計- 前准額度 + 取消、減額合計--></th:block>
							</span>&nbsp;&nbsp;
						</td>
						<td class="rt">
							<span id="incApplyTotCurr" class="field"></span>&nbsp;&nbsp;
							<span id="incApplyTotAmt" class="field"></span>&nbsp;
							<th:block th:text="#{'other.money'}"><!--元--></th:block>
						</td>
						<td class="hd1">
							<th:block th:text="#{'L140M01a.count2'}"><!--其中擔保合計--></th:block>&nbsp;&nbsp;
						</td>
						<td class="rt">
							<span id="incAssTotCurr" class="field"></span>&nbsp;&nbsp;
							<span id="incAssTotAmt" class="field"></span>&nbsp;
							<th:block th:text="#{'other.money'}"><!--元--></th:block>
						</td>
					</tr>
					<tr>
						<td class="hd1">
							<th:block th:text="#{'L140M01a.decApplyTotAmt'}"><!--取消、減額合計--></th:block>&nbsp;&nbsp;
						</td>
						<td class="rt">
							<span id="decApplyTotCurr" class="field"></span>&nbsp;&nbsp;
							<span id="decApplyTotAmt" class="field"></span>&nbsp;
							<th:block th:text="#{'other.money'}"><!--元--></th:block>
						</td>
						<td class="hd1">
							<th:block th:text="#{'L140M01a.count2'}"><!--其中擔保合計--></th:block>&nbsp;&nbsp;
						</td>
						<td class="rt">
							<span id="decAssTotCurr" class="field"></span>&nbsp;&nbsp;
							<span id="decAssTotAmt" class="field"></span>&nbsp;
							<th:block th:text="#{'other.money'}"><!--元--></th:block>
						</td>
					</tr>
					<!--後來新增的部分 END-->
					<tr>
						<td class="hd1">
							<span style="float:left;display:none" class="caseSpan">
								<input type="checkbox" id="caseBox2_10" class="caseBox"></input>
							</span>
							<span id="editMark" style="color:#FFFF00">＊</span>
							<th:block th:text="#{'L140M01a.count'}"><!--授信額度合計--></th:block>&nbsp;&nbsp;
						</td>
						<td class="rt">
							<span id="LoanTotCurr" class="field"></span>&nbsp;&nbsp;
							<span id="LoanTotAmt" class="field"></span>&nbsp;
							<th:block th:text="#{'other.money'}"><!--元--></th:block>
						</td>
						<td class="hd1">
							<th:block th:text="#{'L140M01a.count2'}"><!--其中擔保合計--></th:block>&nbsp;&nbsp;
						</td>
						<td class="rt">
							<span id="assureTotCurr" class="field"></span>&nbsp;&nbsp;
							<span id="assureTotAmt" class="field"></span>&nbsp;
							<th:block th:text="#{'other.money'}"><!--元--></th:block>
						</td>
					</tr>
					<!--J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能-->
					<tr class="showLgdTotAmt">
						<td class="hd1">
							<span style="float:left;display:none" class="caseSpan">
								<input type="checkbox" id="caseBox2_30" class="caseBox"></input>
							</span>
							<span id="editMarkLgd" style="color:#FFFF00">＊</span>
							<span id="label_lgdTotAmt_T"></span>
							<!--授信授權額度合計-->
						</td>
						<td colspan="3">
							<!--J-112-0037_05097_B1004 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計-->
							<p id="lgdTotAmt"></p>
						</td>
					</tr>
					<tr>
						<td class="hd1">
							<th:block th:text="#{'L140M01a.loanTotLCurr'}"><!--衍生性金融商品--></th:block>&nbsp;&nbsp;<br>
							<th:block th:text="#{'L140M01a.loanTotLCurr1'}"><!--額度合計--></th:block>&nbsp;&nbsp;
						</td>
						<td class="rt">
							<span id="LoanTotZCurr" class="field"></span>&nbsp;&nbsp;
							<span id="LoanTotZAmt" class="field"></span>&nbsp;
							<th:block th:text="#{'other.money'}"><!--元--></th:block>
						</td>
						<td class="hd1"></td>
						<td class="rt"></td>
						<!--
                        <td class="hd1">
                            <wicket:message key="L140M01a.loanTotLCurr">衍生性金融商品</wicket:message>&nbsp;&nbsp;
                            <br/>
                            <wicket:message key="L140M01a.loanTotLCurr2">相當授信額度合計</wicket:message>&nbsp;&nbsp;
                        </td>
                        <td class="rt">
                            <span id="LoanTotLCurr" class="field"/>&nbsp;&nbsp;<span id="LoanTotLAmt" class="field"/>&nbsp;<wicket:message key="other.money"> 元</wicket:message>
                        </td>
						-->
					</tr>
					<tr id="multiAmtTr">
						<td class="hd1">
							<th:block th:text="#{'L140M01a.multiAmt'}"><!--L140M01a.multiAmt=各幣別授信額度合計--></th:block>&nbsp;&nbsp;
						</td>
						<td class="rt">
							<textarea id="multiAmt" name="multiAmt" rows="6" readonly="readonly" class="caseReadOnly"></textarea>
						</td>
						<td class="hd1">
							<th:block th:text="#{'L140M01a.multiAssureAmt'}"><!--L140M01a.multiAssureAmt=各幣別擔保授信額度合計--></th:block>&nbsp;&nbsp;
						</td>
						<td class="rt">
							<textarea id="multiAssureAmt" name="multiAssureAmt" rows="6" readonly="readonly" class="caseReadOnly"></textarea>
						</td>
					</tr>
					<tr>
						<td class="hd1">
							<span style="float:left;display:none" class="caseSpan">
								<input type="checkbox" id="caseBox2_16" class="caseBox"></input>
							</span>
							<th:block th:text="#{'L140M01a.rmk'}"><!--備註--></th:block>&nbsp;&nbsp;
						</td>
						<td colspan="3">
							<textarea type="text" id="Rmk" name="Rmk" maxlengthC="512" cols="70"></textarea><br>
							<span class="text-red">
								※<th:block th:text="#{'L140M01a.message38'}"><!--本欄供填寫「額度合計內含之外匯額度或該公司另於聯行之額度合計數」--></th:block>。
							</span>
						</td>
					</tr>
                </table>
				<div id="toglePersonBox" style="display:none;">
					<!--連保人 thickbox -->
					<span class="text-red">
						<th:block th:text="#{'L140M01a.message76'}"><!--※請按[關閉]將「自然人」、「法人」及「備註」資料，寫回到前一頁「連保人」欄位。--></th:block>
					</span>
					<div id="toglePersonTabs" class="tabs">
						<ul>
							<li>
								<a href="#tabs-f_1">
									<b><th:block th:text="#{'L140S02Tab.2_01'}"><!--自然人--></th:block></b>
								</a>
							</li>
							<li>
								<a href="#tabs-f_2">
									<b><th:block th:text="#{'L140S02Tab.2_02'}"><!--法人--></th:block></b>
								</a>
							</li>
							<!--J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式-->
							<li class="showGuarantorMemo" style="display:none;">
								<a href="#tabs-f_3">
									<b><th:block th:text="#{'L140M01a.rmk'}"><!--備註--></th:block></b>
								</a>
							</li>
						</ul>
						<div class="tabCtx-warp">
							<div id="tabs-f_1" class="content">
								<div id="gridviewNatural"></div>
							</div>
							<!--end tabs-f_1-->
							<div id="tabs-f_2" class="content">
								<div id="gridviewCorporate"></div>
							</div>
							<!--end tabs-f_2-->
							<!--J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式-->
							<div id="tabs-f_3" class="content showGuarantorMemo" style="display:none;">
								<textarea id="guarantorMemo" name="guarantorMemo" rows="10" cols="40" maxlengthC="300" style="width: 610px; height: 169px;"></textarea>
							</div>
						</div>
						<!--end tabCtx-warp-->
					</div>
				</div>
				<!--連保人 thickbox END-->
				<span class="text-red">
					※<th:block th:text="#{'L140M01a.message39'}"><!--請按[關閉]回到前一頁執行[計算授信額度合計]，系統會自動算出[授信額度合計]及[其中擔保合計]--></th:block>。
				</span>
    		</form>
			<!--L140M01AForm2 close-->
			<input id="tabFormId" name="tabFormId" style="display:none"></input>
			<input id="tabFormMainId" name="tabFormMainId" style="display:none"></input>
			<div id="newToglePersonBox" style="display:none;">
				<!--新增連保人 thickbox -->
				<form id="L140M01IForm">
					<table class="tb2" width="100%" border="0" cellpadding="0" cellspacing="0" id="newToglePersonTable">
						<tr>
							<td width="70%" class="hd1">
								<th:block th:text="#{'L140M01a.message21'}"><!--連保人類型--></th:block>&nbsp;&nbsp;
							</td>
							<td width="30%">
								<label>
									<input type="radio" id="toglePersonType" name="toglePersonType" value="1" class="required"></input>
									<th:block th:text="#{'L140S02Tab.2_01'}"><!--自然人--></th:block>
								</label>
								<label>
									<input type="radio" id="toglePersonType" name="toglePersonType" value="2" class="required"></input>
									<th:block th:text="#{'L140S02Tab.2_02'}"><!--法人--></th:block>
								</label>
							</td>
						</tr>
						<tr>
							<td width="70%" class="hd1" style="width:25%">
								<th:block th:text="#{'L140M01a.message22'}"><!--統一編號和重覆序號--></th:block>&nbsp;&nbsp;
							</td>
							<td width="30%">
								<input type="text" id="rId" name="rId" size="10" maxlength="10" class="required upText"></input>
								<input type="text" id="rDupNo" name="rDupNo" size="1" maxlength="1" class="required upText obuText"></input>
							</td>
						</tr>
						<tr>
							<td class="hd1">
								<th:block th:text="#{'L140M01a.conPersonName'}"><!--連保人名稱--></th:block>&nbsp;&nbsp;
							</td>
							<td>
								<input type="text" size="30" id="rName" name="rName" maxlength="60" maxlengthC="40" class="required"></input>
								<br>&nbsp;
							</td>
						</tr>
						<tr>
							<td class="hd1">
								<th:block th:text="#{'L140M01a.county'}"><!--國別--></th:block>&nbsp;&nbsp;
							</td>
							<td>
								<select id="rCountry" name="rCountry" class="country"></select>
							</td>
						</tr>
						<tr>
							<td class="hd1">
								<th:block th:text="#{'L140M01a.county'}"><!--國別--></th:block>&nbsp;&nbsp;
							</td>
							<td>
								<select id="rCountry" name="rCountry" class="country"></select>
							</td>
						</tr>
						<tr>
							<td class="hd1">
								<th:block th:text="#{'L140M01a.conPersonRation'}"><!--借款人關係--></th:block>&nbsp;&nbsp;<br>
								<button type="button" id="relationshipPeopleBT" class="noHideBt">
									<span class="text-only">
										<th:block th:text="#{'button.login'}"><!--登錄--></th:block>
									</span>
								</button>
							</td>
							<td>
								<span id="showName" class="field"></span>
							</td>
						</tr>
						<tr id="showGuaPercent">
							<td class="hd1">
								<th:block th:text="#{'L140M01a.guaPercent'}"><!--保證人負担保證責任比率--></th:block>&nbsp;&nbsp;
							</td>
							<td>
								<input type="text" size="18" id="guaPercent" name="guaPercent" maxlength="7" integer="3" fraction="2" class="numeric"></input>％<br>&nbsp;
							</td>
						</tr>
						<tr id="ShoeGuarantorTypeItem">
							<td width="70%" class="hd1">
								<th:block th:text="#{'L140M01is02.title'}"><!--保證人種類--></th:block>&nbsp;&nbsp;
							</td>
							<td width="30%">
								<label>
									<input type="radio" id="guarantorTypeItem" name="guarantorTypeItem" value="1" class="required"></input>
									<th:block th:text="#{'L140M01is02.1'}"><!--連帶保證人--></th:block>
								</label>
								<label>
									<input type="radio" id="guarantorTypeItem" name="guarantorTypeItem" value="2" class="required"></input>
									<th:block th:text="#{'L140M01is02.2'}"><!--一般保證人--></th:block>
								</label>
							</td>
						</tr>
					</table>
					<input type="text" id="rKindD" name="rKindD" style="display:none"></input>
					<!--關係細項-->
					<input type="text" id="rKindM" name="rKindM" style="display:none"></input>
					<!--主要關係-->
					<input type="text" id="l140m01iFormOid" name="l140m01iFormOid" style="display:none"></input>
					<!--相關身份 J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式-->
					<input type="text" id="rType" name="rType" style="display:none"></input>
				</form>
			</div>
			<!--新增連保人 thickbox END--><!--新增收付彙計數 thickbox  -->
			<div id="addCollectBox" style="display:none">
				<form id="L140M01KForm" name="L140M01KForm">
					<table class="tb2" border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td width="50%" class="hd1">
								<th:block th:text="#{'L140M01a.Pay'}"><!--付--></th:block>&nbsp;&nbsp;
							</td>
							<td width="50%">
								<select id="CPCurr" name="CPCurr" class="money"></select>
								<input id="CollectPay" name="CollectPay" type="text" size="18" maxlength="19" integer="15" fraction="2" class="numeric"></input>
							</td>
						</tr>
						<tr>
							<td width="50%" class="hd1">
								<th:block th:text="#{'L140M01a.Accept'}"><!--收--></th:block>&nbsp;&nbsp;
							</td>
							<td width="50%">
								<select id="CACurr" name="CACurr" class="money"></select>
								<input id="CollectAccept" name="CollectAccept" type="text" size="18" maxlength="19" integer="15" fraction="2" class="numeric"></input>
							</td>
						</tr>
					</table>
				</form>
			</div>
			<!--新增收付彙計數 thickbox  END-->
			<div id="loginType" style="display:none; margin-top:5px;">
				<!--性質選擇thickbox -->
				<table border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td>
							<label>
								<input type="checkbox" name="cb1" value="13"></input>
								<th:block th:text="#{'L140M01a.type13'}"><!--報價--></th:block>
							</label>
						</td>
						<td>
							<label>
								<input type="checkbox" name="cb1" value="1"></input>
								<th:block th:text="#{'L140M01a.type1'}"><!--新做--></th:block>
							</label>
						</td>
					</tr>
					<tr>
						<td>
							<label>
								<input type="checkbox" name="cb1" value="5"></input>
								<th:block th:text="#{'L140M01a.type5'}"><!--增額--></th:block>
							</label>
						</td>
						<td>
							<label>
								<input type="checkbox" name="cb1" value="10"></input>
								<th:block th:text="#{'L140M01a.type10'}"><!--紓困--></th:block>
							</label>
						</td>
					</tr>
					<tr>
						<td>
							<label>
								<input type="checkbox" name="cb1" value="12"></input>
								<th:block th:text="#{'L140M01a.type12'}"><!--協議清償--></th:block>
							</label>
						</td>
						<td>
							<label>
								<input type="checkbox" name="cb1" value="6"></input>
								<th:block th:text="#{'L140M01a.type6'}">減額</th:block>
							</label>
						</td>
					</tr>
					<tr>
						<td>
							<label>
								<input type="checkbox" name="cb1" value="3"></input>
								<th:block th:text="#{'L140M01a.type3'}"><!--變更條件--></th:block>
							</label>
						</td>
						<td>
							<label>
								<input type="checkbox" name="cb1" value="2"></input>
								<th:block th:text="#{'L140M01a.type2'}">續約</th:block>
							</label>
						</td>
					</tr>
					<tr>
						<td>
							<label>
								<input type="checkbox" name="cb1" value="11"></input>
								<th:block th:text="#{'L140M01a.type11'}"><!--提前續約--></th:block>
							</label>
						</td>
						<td>
							<label>
								<input type="checkbox" name="cb1" value="9"></input>
								<th:block th:text="#{'L140M01a.type9'}">展期(不良授信案)</th:block>
							</label>
						</td>
					</tr>
					<tr>
						<td>
							<label>
								<input type="checkbox" name="cb1" value="4"></input>
								<th:block th:text="#{'L140M01a.type4'}"><!--流用--></th:block>
							</label>
						</td>
						<td>
							<label>
								<input type="checkbox" name="cb1" value="8"></input>
								<th:block th:text="#{'L140M01a.type8'}">取消</th:block>
							</label>
						</td>
					</tr>
					<tr>
						<td>
							<label>
								<input type="checkbox" name="cb1" value="7"></input>
								<th:block th:text="#{'L140M01a.type7'}"><!--不變--></th:block>
							</label>
						</td>
						<td></td>
					</tr>
				</table>
			</div>
			<!--性質選擇thickbox END -->
			<div id="commonNumBox" style="display:none">
				<!--共用額度序號 thickbox  -->
				<table width="500px" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td>
							<th:block th:text="#{'L140M01a.message40'}"><!--注意:此欄位僅供「二家公司」或「OBU/DBU」共用額度時使用--></th:block><br>
							<th:block th:text="#{'L140M01a.message41'}"><!--請選擇共用額度序號來源--></th:block>
						</td>
					</tr>
					<tr>
						<td>
							<label>
								<input type="radio" name="commonNumRadio" value="commonNow"></input>
								<th:block th:text="#{'L140M01a.message42'}"><!--選擇本簽報書中的額度序號--></th:block>
							</label>
						</td>
					</tr>
					<tr>
						<td>
							<label>
								<input type="radio" name="commonNumRadio" value="commonOther"></input>
								<th:block th:text="#{'L140M01a.message43'}"><!--選擇其他簽報書中的額度序號--></th:block>
							</label>
						</td>
					</tr>
					<tr>
						<td>
							<label>
								<input type="radio" name="commonNumRadio" value="clean"></input>
								<th:block th:text="#{'L140M01a.message44'}"><!--清除共用額度序號--></th:block>
							</label>
						</td>
					</tr>
				</table>
			</div>
			<!--共用額度序號 thickbox END -->
			<div id="commonNumOtherBox" style="display:none">
				<!--共用額度序號簽報書選擇 thickbox  -->
				<table width="650px" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td>
							<div id="gridviewConnomOther"></div>
						</td>
					</tr>
				</table>
			</div>
			<!--共用額度序號簽報書選擇thickbox END -->
			<div id="commonNumOtherSelectBox" style="display:none">
				<!--共用額度序號簽報書選擇 thickbox  -->
				<table width="500px" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td>
							<div id="gridviewConnomOtherSelect"></div>
						</td>
					</tr>
				</table>
			</div><!--共用額度序號簽報書選擇thickbox END -->
			<div id="itemTypeBox" style="display:none">
				<!--授信科目 thickbox-->
				<span>
					<th:block th:text="#{'L140M01a.message45'}"><!--科目性質--></th:block>：<span id="itemTypename"></span>
				</span><br>
				<button type="button" onclick="upDownBox(true)">
					<span class="text-only">
						<th:block th:text="#{'btn.upSeqno'}">向上移動</th:block>
					</span>
				</button>
				<button type="button" onclick="upDownBox(false)">
					<span class="text-only">
						<th:block th:text="#{'btn.downSeqno'}">向下移動</th:block>
					</span>
				</button>
				<div id="gridviewitemType"></div>
			</div>
			<div id="newitemTypeBox" style="display:none;">
				<!--新增授信科目 thickbox-->
				<form id="L140M01CForm">
					<table class="tb2" border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td width="50%" class="hd1">
								<th:block th:text="#{'L140M01c.item'}"><!--授信科目--></th:block>&nbsp;&nbsp;
							</td>
							<td width="50%">
								<select id="loanTP" name="loanTP" class="required"></select>
								<!-- 從資料庫抓出select 資料 -->
							</td>
						</tr>
						<tr id="showWhenDervSubject">
							<td class="hd1">
								<th:block th:text="#{'L140M01c.subjPurpose'}"><!--交易目的--></th:block>&nbsp;&nbsp;
							</td>
							<td>
								<label>
									<input type="radio" name="subjPurpose" value="1" class="required"></input>
									<th:block th:text="#{'L140M01c.subjPurpose1'}"><!--避險--></th:block>
								</label>
								<label>
									<input type="radio" name="subjPurpose" value="2" class="required"></input>
									<th:block th:text="#{'L140M01c.subjPurpose2'}"><!--非避險--></th:block>
								</label>
							</td>
						</tr>
						<tr>
							<td class="hd1">
								<th:block th:text="#{'L140M01c.subjDscr'}"><!--科目補充說明--></th:block>&nbsp;&nbsp;
							</td>
							<td>
								<input type="text" size="50" maxlength="200" maxlengthC="66" id="subjDscr" name="subjDscr"></input>
							</td>
						</tr>
						<tr>
							<td class="hd1">
								<th:block th:text="#{'L140M01c.lmtDays'}"><!--清償期限--></th:block>&nbsp;&nbsp;
							</td>
							<td>
                                <span id="hidelimit" class='field_l140m01c_lmtDays_lmtOther field_l140m01c_lmtDays'>
									<input type="text" name="lmtDays" id="lmtDays" class="numeric required" positiveonly="false" integer="5" maxlength="5" size="5"></input>
									<!--
									<select id="lmtDays" name="lmtDays">
									<option value="30" selected="selected">30</option>
									<option value="60" >60</option>
									<option value="90" >90</option>
									<option value="180" >180</option>
									<option value="365" >365</option>
									</select>
									-->
									<th:block th:text="#{'other.day'}"><!--天--></th:block>
                                </span>&nbsp;
								<label class='field_l140m01c_lmtDays_lmtOther'>
									<input id="lmtOther" name="lmtOther" value="1" type="checkbox"></input>
									<th:block th:text="#{'L140M01c.lmtOther2'}"><!--詳其他敘做條件--></th:block>
								</label>
								<span class='field_l140m01c_lnYear_lnMonth'>
									<input type='text' id='lnYear'  name='lnYear'  class='numeric' size="2" maxlength="2" integer="2"></input>
									<th:block th:text="#{'L140M01c.lnYear.unit'}"></th:block>&nbsp;
									<input type='text' id='lnMonth' name='lnMonth' class='numeric' size="2" maxlength="2" integer="2"></input>
									<th:block th:text="#{'L140M01c.lnMonth.unit'}"></th:block>
								</span>
								<!-- 
								在 LMS1405M01FormHandler::queryL140m01cByOid(...)
								將 l140m01c.oid 填入到  itemOid 
								-->
								<input id="itemOid" name="itemOid" type="text" style="display:none"></input>&nbsp;<br>
								<span id="lmtDaysText"></span>
							</td>
						</tr>
						<tr class='field_l140m01c_c121Relate'>
							<td class="hd1">
								<th:block th:text="#{'label.combineRatingInfo'}">信用評等</th:block>&nbsp;
							</td>
							<td>
								<input type="hidden" id="c121MainId" name="c121MainId"></input>
								<input type="hidden" id="c121RatingId" name="c121RatingId"></input>
								<input type="hidden" id="c121CustId" name="c121CustId"></input>
								<input type="hidden" id="c121DupNo" name="c121DupNo"></input>
								<input type="hidden" id="grade" name="grade"></input>
								<input type="hidden" id="c121CmsType" name="c121CmsType"></input>
								<input type="hidden" id="c121LocationType" name="c121LocationType"></input>
								<input type="hidden" id="modelType" name="modelType"></input>
								<input type="hidden" id="varVer" name="varVer"></input>
								<table class='tb2' width='100%'>
									<tr>
										<td class='noborder' width='90%'>
											<span id="l140m01c_cls_grade" class='field'></span>&nbsp;&nbsp;
										</td>
										<td class='noborder' nowrap>
											<button type="button" id="loginGradeBt">
												<span class="text-only">
													<th:block th:text="#{'L140M01c.btRegGrage'}">登錄</th:block>
												</span>
											</button>
											<button type="button" id="cleanGradeBt">
												<span class="text-only">
													<th:block th:text="#{'L140M01c.btClrGrage'}">清除</th:block>
												</span>
											</button>
										</td>
									</tr>
								</table>&nbsp;
							</td>
						</tr>
					</table>
				</form>
			</div>
			<!--新增額度序號 thickbox -->
			<div id="newFcltNoBox" style="display:none">
				<b><wicket:message key="L140M01a.message29"><!-- 請選擇額度序號來源--></wicket:message></b>
				<table width="580px" border="0" cellpadding="0" cellspacing="0" id="newFcltNo_YES">
					<tr>
						<td>
							<label>
								<input type="radio" name="newFcltNoRadio" value="new"></input>
								<th:block th:text="#{'L140M01a.message30'}"><!--產生新號(適用於「新做」案件)--></th:block>
							</label><br>
							<table id="tb_newFcltNo_Yes" style="margin-left:20px;margin-top:5px; display:none">
								<tr>
									<td colspan="2">
										<b><th:block th:text="#{'L140M01a.message26'}"><!--本額度之作帳分行(帳務管理行)是否同為簽報分行--></th:block></b>
									</td>
								</tr>
								<tr>
									<td>
										<input type="radio" name="contentBrankRadio" value="Yes" checked></input>
									</td>
									<td>
										<th:block th:text="#{'L140M01a.contentBrankRadioYes'}"><!--是，由簽報行統籌額度管理及帳務還本付息事項--></th:block>
									</td>
								</tr>
								<tr>
									<td>
										<input type="radio" name="contentBrankRadio" value="NO"></input>
									</td>
									<td>
										<th:block th:text="#{'L140M01a.contentBrankRadioNo'}"><!--否，請輸入作帳分行代號(三碼)，將另由作帳行統籌額度管理及帳務還本付息事項--></th:block>
									</td>
								</tr>
								<tr>
									<td></td>
									<td class="desc_zh_only">
										<span style="margin-left: -8px;">(</span>
										<span>
											<th:block th:text="#{'L140M01a.contentBrankRadioNoDesc'}">
												<!-- (如本額度係由002分行辦理簽報作業，002與0B6分行共同攤貸，帳掛0B6分行，則請輸入0B6分行代號；
												或由007分行簽報，002與007分行共同攤貸，由002分行辦理後續簽約、動撥及還本付息等事宜，則請輸入002分行代號)-->
											</th:block>
										</span>
										<span>)</span>
									</td>
								</tr>
								<tr id="tr_contentBrankRadio_NO" style="display:none">
									<td></td>
									<td>
										<b class="desc_zh_only">
											<th:block th:text="#{'L140M01a.message28'}"><!--請輸入欲產生額度序號之作帳行分行代碼(三碼)--></th:block>
										</b><br>
										<input type="text" id="newFcltNo_No_bankNum" maxlength="3" minlength="3" size="3" class="branchNo upText"></input>&nbsp;&nbsp;
										<button type="button" id="selectBranchBt">
											<span class="text-only">
												<th:block th:text="#{'other.login'}"><!--登錄--></th:block>
											</span>
										</button>
									</td>
								</tr>
							</table>
						</td>
					</tr>
					<tr>
						<td>
							<div>
								<label>
									<input type="radio" name="newFcltNoRadio" value="original"></input>
									<th:block th:text="#{'L140M01a.message32'}"><!--登錄原案額度序號(適用於舊案續約及條件變更)--></th:block>
								</label><br>
								<b class="desc_en_only" style="margin-left: 23px; margin-top: 1px;">
									<!--只有英文要特別顯示這區塊--> (applicable to renewal cases and cases involving change of terms)
								</b>
							</div>
							<table id="tb_newFcltNo_NO" style="margin-left:20px;margin-top:5px;display:none;">
								<tr>
									<td>
										<span id="originalInput" style="display:none">
											<b>
												<th:block th:text="#{'L140M01a.message33'}"><!--請輸入原額度序號: 該舊額度序號須已執行轉換，轉換後新編碼之額度序號--></th:block><br>
												【<th:block th:text="#{'L140M01a.message68'}"><!--額度序號長度應為12碼，編碼原則:XXX(分行代號)+X(1:DBU,4:OBU,5:海外)+YYY(年度)+99999(流水號)--></th:block>】
											</b><br>
											<input type="text" id="originalText" size="11" minlength="12" maxlength="12" class="upText"></input>
										</span>
									</td>
								</tr>
							</table>
						</td>
					</tr>
					<tr>
						<td>
							<label>
								<input type="radio" name="newFcltNoRadio" value="clean"></input>
								<th:block th:text="#{'L140M01a.message27'}"><!--清除額度序號--></th:block>
							</label>
						</td>
					</tr>
				</table>
				<br><br>
			</div>
			<!--新增額度序號 thickbox END-->
			<div id="newFcltNoSelectBox" style="display:none">
				<!--新增預約額度序號 thickbox -->
				<span>
					<b><th:block th:text="#{'L140M01a.message36'}"><!--請選取預約之額度序號--></th:block></b>
					<select id="newFcltNoSelect"></select>
				</span>
			</div>
			<!--新增預約額度序號 thickbox END-->
			<div id="localPageBox" style="display:none;">
				<!--本票詞庫 thickbox -->
				<table width="300px" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td>
							<th:block th:text="#{'L140M01a.message37'}"><!--請選擇本票詞庫--></th:block>：
						</td>
					</tr>
					<tr>
						<td>
							<!--<select id="localPageSelect" name="localPageSelect" combokey="lms1405s02_checkNote" />-->
							<select id="localPageSelect" name="localPageSelect"></select>
						</td>
					</tr>
				</table>
			</div>
			<!--本票詞庫 thickbox END-->
			<div id="relationshipBox" style="display:none;">
				<!--關係 thickbox -->
				<!-- <select id="relationshipSelect" combokey="lms1205s01_RelClass" />-->
				<select id="relationshipSelect" class="nodisabled"></select>
				<!--<br/>-->
				<!--<b><span id="relationshipName">請選擇企業關係人(含法人、自然人):</span></b>-->
				<!--<br/>-->
				<span id="the1">
					<!--<select id="rationSelect1" name="rationSelect1"  combokey="Relation_type1"/>-->
					<select id="rationSelect1" name="rationSelect1" class="nodisabled"></select>
				</span><br>
				<span id="the2" style="display:none;">
					<!-- <select id="rationSelect2" name="rationSelect2"  combokey="Relation_type2"/>-->
					<select id="rationSelect2" name="rationSelect2" class="nodisabled"></select>
				</span>
				<span id="the3" style="display:none;">
					<!--<select id="rationSelect31" name="rationSelect31"  combokey="Relation_type31"/><select id="rationSelect32" name ="rationSelect32" combokey="Relation_type32"/>  -->
					<select id="rationSelect31" name="rationSelect31" class="nodisabled"></select>
					<select id="rationSelect32" name="rationSelect32" class="nodisabled"></select>
				</span>
			</div>
			<!--關係 thickbox END-->
			<div id="sayBox" style="display:none">
				<!--擔保授信額度調整說明 thickbox  -->
				<span>
					1. <th:block th:text="#{'L140M01a.message64'}">L140M01a.message64=此欄主要供「應收信用狀款項」科目當其額度性質為「擔保」時，用來調整計算後的「其中擔保合計」之用。</th:block><br>
					2. <th:block th:text="#{'L140M01a.message65'}">L140M01a.message65=請離開回到前一頁執行【計算授信額度合計】方能得到扣除「擔保授信額度調整」後的「其中擔保合計」金額。</th:block><br>
					3. <th:block th:text="#{'L140M01a.message66'}">L140M01a.message66=此欄之幣別單位與「現請額度」之幣別相同(儲存後會自動顯示)。</th:block>
				</span>
			</div>
			<!--擔保授信額度調整說明 thickbox END -->
			<div id="selectDbuOrObuBox" style="display:none">
				<label>
					<input type="radio" name="DBUorOBURadio" value="1" checked="checked"></input>DBU
				</label><br>
				<label>
					<input type="radio" name="DBUorOBURadio" value="4"></input>OBU
				</label>
			</div>
			<!--收付彙計數 thickbox  -->
			<div id="collectBox" style="display:none">
				<div id="gridviewCollect"></div>
			</div><!--收付彙計數 thickbox  END--><!--顯示變更前內容 thickbox  -->
			<div id="showBeforeBox" style="display:none">
				<!--本額度有無送保	headItem1BF
				保證成數	gutPercentBF
				信保首次動用有效期限	gutCutDateBF
				授信科目	lnSubjectBF
				現請額度－幣別	currApplyCurrBF
				現請額度－金額	currApplyAmtBF
				現請額度－是否循環使用	reUseBF
				現請額度－動用幣別	otherCurrBF
				動用期限	useDeadlineBF
				動用期限－其他	desp1BF
				連保人	guarantoBF-->
				<table id="BFcontent" class="tb2" width="550px" border="0" cellspacing="0" cellpadding="0">
					<tr>
						<td class="hd1" style="width:25%">
							<th:block th:text="#{'doc.caseNo'}"><!--案號--></th:block>&nbsp;&nbsp;
						</td>
						<td colspan="3" style="width:75%">
							<span id="caseNoBF" name="caseNoBF"></span>
						</td>
					</tr>
					<tr>
						<td class="hd1" style="width:25%">
							<th:block th:text="#{'L140M01a.headItem1'}"><!--本額度有無送保--></th:block>&nbsp;&nbsp;
						</td>
						<td colspan="3" style="width:75%">
							<label>
								<input id="headItem1BF" name="headItem1BF" type="radio" value="N" disabled></input>
								<th:block th:text="#{'nohave'}"><!--無--></th:block>
							</label>
							<label>
								<input id="headItem1BF" name="headItem1BF" type="radio" value="Y" disabled></input>
								<th:block th:text="#{'have'}"><!--有--></th:block>
							</label>
						</td>
					</tr>
					<tr id="BFheadItem1Tr" style="display:none;">
						<td class="hd1" style="width:25%">
							<th:block th:text="#{'L140M01a.gratio'}"><!--保證成數--></th:block>&nbsp;&nbsp;
						</td>
						<td style="width:25%">
							<span id="gutPercentBF" name="gutPercentBF"></span>%
						</td>
						<td style="width:25%" class="hd1">
							<th:block th:text="#{'L140M01a.gutCutDate'}"><!--信保首次動用有效期限--></th:block>&nbsp;&nbsp;
						</td>
						<td style="width:25%">
							<span id="gutCutDateBF" name="gutCutDateBF"></span>
						</td>
					</tr>
					<tr>
						<td class="hd1">
							<th:block th:text="#{'L140M01c.item'}"><!--授信科目--></th:block>&nbsp;&nbsp;
						</td>
						<td colspan="3">
							<span id="lnSubjectBF" name="lnSubjectBF"></span>
						</td>
					</tr>
					<tr>
						<td class="hd1">
							<th:block th:text="#{'L140M01a.moneyAmt'}"><!--現請額度--></th:block>&nbsp;&nbsp;
						</td>
						<td colspan="3">
							<span id="currApplyCurrBF" name="currApplyCurrBF"></span>&nbsp;&nbsp;
							<span id="currApplyAmtBF" name="currApplyAmtBF"></span>&nbsp;
							<th:block th:text="#{'other.money'}"><!--元--></th:block>&nbsp;&nbsp;
							<span id="reUseBF" name="reUseBF"></span><br>
							<span id="otherCurrBF" name="otherCurrBF"></span>
						</td>
					</tr>
					<tr>
						<td class="hd1">
							<th:block th:text="#{'L140M01a.useDeadline'}"><!--動用期限--></th:block>&nbsp;&nbsp;
						</td>
						<td colspan="3">
							<span id="useDeadlineBF" name="useDeadlineBF"></span>
						</td>
					</tr>
					<tr>
						<td class="hd1">
							<th:block th:text="#{'L140M01a.conPersonNew'}"><!--連保人--></th:block>&nbsp;&nbsp;
						</td>
						<td colspan="3">
							<span id="guarantoBF" name="guarantoBF"></span>
						</td>
					</tr>
					<tr>
						<td class="hd1">
							<th:block th:text="#{'L140S02Tab.3_04'}"><!--限額條件敘述--></th:block>&nbsp;&nbsp;
						</td>
						<td colspan="3">
							<span id="itemDsc1BF" name="itemDsc1BF"></span>
						</td>
					</tr>
					<tr>
						<td class="hd1">
							<th:block th:text="#{'L140S02Tab.4_02'}"><!--利(費)率敘述--></th:block>&nbsp;&nbsp;
						</td>
						<td colspan="3">
							<span id="itemDsc2BF" name="itemDsc2BF"></span>
						</td>
					</tr>
				</table>
			</div>
			<!--顯示共同借款人 thickbox-->
			<div id="commonBorrowerBox" style="display:none">
				<div id="commonBorrowerGridVeiw"></div>
			</div><!--引進共同借款人 thickbox  -->
			<div id="selectBorrowerBox" style="display:none">
				<div id="selectBorrowerGridVeiw"></div>
			</div><!--引進平均動用率 thickbox-->
			<div id="selectUseParBox" style="display:none">
				<div id="selectUseParGridview"></div>
			</div><!--顯示徵信報告書 thickbox-->
			<div id="selectCesPeopleBox" style="display:none">
				<div id="selectCesPeopleGridview"></div>
			</div><!-- end 顯示徵信報告書 thickbox-->
			<!--  共同借款人維護關係 thickbox-->
			<div id="L140M01JPeopleBox" style="display:none">
				<form action="" id="L140M01JForm" name="L140M01JForm">
					<table class="tb2" width="100%" border="0" cellpadding="0" cellspacing="0" id="newToglePersonTable">
						<tr>
							<td width="70%" class="hd1" style="width:25%">
								<th:block th:text="#{'L140M01a.message22'}"><!--統一編號和重覆序號--></th:block>&nbsp;&nbsp;
							</td>
							<td width="30%">
								<span id="L140M01J_custId" name="L140M01J_custId" class="field"></span>&nbsp;&nbsp;
								<span id="L140M01J_dupNo" name="L140M01J_dupNo" class="field"></span>
							</td>
						</tr>
						<tr>
							<td class="hd1">
								<th:block th:text="#{'doc.custName'}"><!--姓名/名稱--></th:block>&nbsp;&nbsp;
							</td>
							<td>
								<span id="L140M01J_custName" name="L140M01J_custName" class="field"></span>
							</td>
						</tr>
						<tr>
							<td class="hd1">
								<th:block th:text="#{'L140M01a.conPersonRation'}"><!--借款人關係--></th:block>&nbsp;&nbsp;<br>
								<button type="button" id="L140M01JPeopleBT" class="noHideBt">
									<span class="text-only">
										<th:block th:text="#{'button.login'}"><!--登錄--></th:block>
									</span>
								</button>
							</td>
							<td>
								<span id="L140M01J_showName" class="field"></span>
								<input type="hidden" id="L140M01J_custRlt" name="L140M01J_custRlt"></input>
							</td>
						</tr>
					</table>
				</form>
			</div>
			<!-- end 共同借款人維護關係 thickbox-->
			<!--顯示變更前內容 thickbox  END-->
			<div id="loginDervPeriod" style="display:none; margin-top:5px;">
				<!--衍生性金融商品期數選擇thickbox 從CODETYPE lms1405s02_AllDervPeriod 來-->
				<table border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td>
							<input type="checkbox" name="dp1" id="dp1"></input>
						</td>
					</tr>
				</table>
			</div>
			<div id="loginDervPeriod_OLD" style="display:none; margin-top:5px;">
				<!--取消不用，改以loginDervPeriod為主 XXXXXXXXXXXXXXXXXXXXXOLD衍生性金融商品期數選擇thickboxXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX -->
				<table border="0" cellpadding="0" cellspacing="0">
					<tr id="trDerv1" style="display:none">
						<td>
							<label>
								<input type="checkbox" name="dp1" value="1"></input>
								<th:block th:text="#{'L140M01a.DERVPERIOD1'}"><!--六個月期(含)以內--></th:block>
							</label>
						</td>
					</tr>
					<tr id="trDerv2" style="display:none">
						<td>
							<label>
								<input type="checkbox" name="dp1" value="2"></input>
								<th:block th:text="#{'L140M01a.DERVPERIOD2'}"><!--六至十二個月期(含)以內--></th:block>
							</label>
						</td>
					</tr>
					<tr id="trDerv8" style="display:none">
						<td>
							<label>
								<input type="checkbox" name="dp1" value="8"></input>
								<th:block th:text="#{'L140M01a.DERVPERIOD8'}"><!--一至二年期(含)以內--></th:block>
							</label>
						</td>
					</tr>
					<tr id="trDerv3" style="display:none">
						<td>
							<label>
								<input type="checkbox" name="dp1" value="3"></input>
								<th:block th:text="#{'L140M01a.DERVPERIOD3'}"><!--二年期(含)以內--></th:block>
							</label>
						</td>
					</tr>
					<tr id="trDerv10" style="display:none">
						<td>
							<label>
								<input type="checkbox" name="dp1" value="10"></input>
								<th:block th:text="#{'L140M01a.DERVPERIOD10'}"><!--二至三年期(含)以內--></th:block>
							</label>
						</td>
					</tr>
					<tr id="trDerv4" style="display:none">
						<td>
							<label>
								<input type="checkbox" name="dp1" value="4"></input>
								<th:block th:text="#{'L140M01a.DERVPERIOD4'}"><!--三年期(含)以內--></th:block>
							</label>
						</td>
					</tr>
					<tr id="trDerv5" style="display:none">
						<td>
							<label>
								<input type="checkbox" name="dp1" value="5"></input>
								<th:block th:text="#{'L140M01a.DERVPERIOD5'}"><!--四年期(含)以內--></th:block>
							</label>
						</td>
					</tr>
					<tr id="trDerv9" style="display:none">
						<td>
							<label>
								<input type="checkbox" name="dp1" value="9"></input>
								<th:block th:text="#{'L140M01a.DERVPERIOD9'}"><!--三至五年期(含)以內--></th:block>
							</label>
						</td>
					</tr>
					<tr id="trDerv6" style="display:none">
						<td>
							<label>
								<input type="checkbox" name="dp1" value="6"></input>
								<th:block th:text="#{'L140M01a.DERVPERIOD6'}"><!--五年期(含)以內--></th:block>
							</label>
						</td>
					</tr>
					<tr id="trDerv11" style="display:none">
						<td>
							<label>
								<input type="checkbox" name="dp1" value="11"></input>
								<th:block th:text="#{'L140M01a.DERVPERIOD11'}"><!--五至七年期(含)以內--></th:block>
							</label>
						</td>
					</tr>
					<tr id="trDerv12" style="display:none">
						<td>
							<label>
								<input type="checkbox" name="dp1" value="12"></input>
								<th:block th:text="#{'L140M01a.DERVPERIOD12'}"><!--七至十年期--></th:block>
							</label>
						</td>
					</tr>
					<tr id="trDerv7" style="display:none">
						<td>
							<label>
								<input type="checkbox" name="dp1" value="7"></input>
								<th:block th:text="#{'L140M01a.DERVPERIOD7'}"><!--五年期以上--></th:block>
							</label>
						</td>
					</tr>
				</table>
			</div>
			<!--衍生性選擇thickbox END -->
			<!--登錄連保人-保證人按一定比率負担保證責任 thickbox END-->
			<div id="burdenCertainPercentage" style="display:none">
				<span>
					<b><th:block th:text="#{'L140M01a.message136'}"><!--保證人是否按一定比率負担保證責任--></th:block>?
					</b>
					<label>
						<input type="radio" id="guaPercentFgTmp" name="guaPercentFgTmp" value="Y"></input>
						<th:block th:text="#{'yes'}"><!--是--></th:block>
					</label>
					<label>
						<input type="radio" name="guaPercentFgTmp" value="N"></input>
						<th:block th:text="#{'no'}"><!--否--></th:block>
					</label>
					<!--J-110-0040_05097_B1001 Web e-Loan增加「本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)」註記-->
					<br>
					<!--J-110-0040-TODO-->
					<b><th:block th:text="#{'L140M01a.message269'}">本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)</th:block>?</b>
					<label>
						<input type="radio" id="guaNaExposureTmp" name="guaNaExposureTmp" value="Y"></input>
						<th:block th:text="#{'yes'}">是</th:block>
					</label>
					<label>
						<input type="radio" name="guaNaExposureTmp" value="N"></input>
						<th:block th:text="#{'no'}">否</th:block>
					</label>
				</span><br>
			</div>
			<div id="divL140S02C_chooseC121MainIdCustIdDup" style="display:none">
				<div id='gridL140S02C_chooseC121MainIdCustIdDup'></div>
			</div>
			<div id="divQueryUseParAloanInputCntrNo" style="display:none">
				<input type="text" id="queryUseParAloanInputCntrNo" name="queryUseParAloanInputCntrNo" size="11" minlength="12" maxlength="12" class="upText"></input>
			</div>
			<!--N-105-0127-001 Web e-Loan配合衍生性金融商品風險係數修改-->
			<div id="loginDervPeriod_N1050127" style="display:none; margin-top:5px;">
				<form id="loginDervPeriodForm">
					<span>
						<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
							<tr>
								<td>
									<b><th:block th:text="#{'L140M01a.maxDervPeriod'}">衍生性金融商品最長期數</th:block>：</b>
								</td>
								<td>
									<select id="maxDervPeriod" name="maxDervPeriod" class="nodisabled required"></select>
								</td>
							</tr>
							<tr>
								<td>
									<b><th:block th:text="#{'L140M01a.maxDervNum'}">最大未來潛在暴險額計算權數</th:block>：</b>
								</td>
								<td>
									<input type="text" id="maxDervNum" name="maxDervNum" size="10" maxlength="10" class="numeric required" integer="5" fraction="4"></input>%
									<button type="button" id="applyMaxDervNumBt">
										<span class="text-only">
											<th:block th:text="#{'L140M01a.applyMaxDervNumBt'}">取得高風險組計算權數</th:block>
										</span>
									</button>
								</td>
							</tr>
						</table>
					</span>
				</form>
			</div>
			<!--J-107-0256_05097_B1001 Web e-Loan企金授信簽報書額度明細表，針對額度性質選列「擔保」者，增加提示不得列擔保科目的擔保品項目-->
			<div id="showSbjPropertyChgMsgBox" style="display:none">
				<div>※額度性質為擔保，有關擔保科目可適用之擔保品請參考「DBU、OBU及海外分支機構辦理擔保授信分析比較表」<br></div>
				<a href="/lms-web/img/lms/Guaranteed_credit_analysis_comparison_1041102.doc" target="_blank">
					<th:block th:text="#{'L140M01a.OpenDescription'}">開啟說明</th:block>
				</a>
			</div>
			<!--J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定-->
			<div id="setGuarantorSeqThickBox" style="display:none;">
				<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
					<tr>
						<td class="hd1">
							<th:block th:text="#{'L140M01a.message15'}"><!--注意事項--></th:block>&nbsp;&nbsp;
						</td>
						<td>
							<span class="text-red">
								(1)<th:block th:text="#{'L140M01a.message264'}">且保證人(一般/連帶)亦為企業戶時，需要填列保證人信用品質順序。</th:block>
							</span>
						</td>
					</tr>
					<tr>
						<td class="hd1">
							<th:block th:text="#{'L140M01a.message17'}"><!--操作說明--></th:block>&nbsp;&nbsp;
						</td>
						<td>
							<span class="text-red">
								(1)<th:block th:text="#{'L140M01a.message265'}">設定各保證人之優先順序，由1開始依序編排，不得跳號。</th:block>
							</span><br>
							<span class="text-red">
								(2)<th:block th:text="#{'L140M01a.message262'}">保證人必需設定信用品質順序，直到該額度之保證人之負担保證責任比率合計達100%。</th:block>
							</span><br>
							<span class="text-red">
								(3)<th:block th:text="#{'L140M01a.message266'}">執行寫回額度明細表。</th:block>
							</span>
						</td>
					</tr>
				</table>
				<div id="gridViewGuarantorSeq"></div>
			</div>
			<!--G-113-0145 授信新做額度於eloan簽報核准後，自動傳送AS400執行3X02，以利央行RDT報表傳送。(僅泰國分行用) start-->
			<!-- 授信科目用途別選單-->
			<div id="loanAndContTypeThickBox" style="display:none;">
				<!--選擇thickbox -->
				<table border="0" cellpadding="0" cellspacing="0" width="100%">
					<tr>
						<td>
							<input type="checkbox" name="checkbox_loanAndContType" id="checkbox_loanAndContType"></input>
						</td>
					</tr>
				</table>
			</div>
			<!-- 申請案件號碼選單 thickbox-->
			<div id="appNoThickBox" style="display:none;">
				<span>
					<b><th:block th:text="#{'L140M01A.chooseAppNo'}"><!--請選取申請案件號碼--></th:block></b>
					<select id="appNoSelect"></select>
				</span>
			</div>
			<!--G-113-0145 授信新做額度於eloan簽報核准後，自動傳送AS400執行3X02，以利央行RDT報表傳送。(僅泰國分行用) end-->
    	</th:block>
    </body>
</html>
