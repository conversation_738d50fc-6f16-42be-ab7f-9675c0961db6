package com.mega.eloan.lms.lms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;

/**
 * <pre>
 * 澳洲地區消金偣款人基本資料
 * </pre>
 * 
 * @since 2015/6/15
 * <AUTHOR>
 * @version <ul>
 *          <li>2015/6/15,EL08034,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms1025v00")
public class LMS1025V00Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		// 主管跟經辦都會出現的按鈕
		addToButtonPanel(model, LmsButtonEnum.Filter, LmsButtonEnum.Add, LmsButtonEnum.Delete);
		renderJsI18N(LMS1015V01Page.class);
		renderJsI18N(LMS1015M01Page.class);

        model.addAttribute("loadScript", "require(['pagejs/lms/LMS1025MAINPage'], function() {loadScript('pagejs/lms/LMS1025V00Page');});");
        
		// renderJsI18N(AbstractOverSeaCLSPage.class);
		// add(new OverSeaCLSOuterPanel("divOverSeaCLSPanel"));
	}

}