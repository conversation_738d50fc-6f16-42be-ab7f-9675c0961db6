<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <body>
        <th:block th:fragment="panelFragmentBody">
	    <script type="text/javascript">
	      	loadScript('pagejs/lns/LMS1601S06Panel');
	    </script>
<!-- 	    <script type="text/javascript" src="pagejs/lns/LMS1601S06Panel.js?r=20220414"></script> -->
            <form id="L166M01AForm">
                <fieldset>
                    <legend>
                        <strong>
                            <th:block th:text="#{'L163M01A.rpaItem'}">RPA資料查詢</th:block>
                        </strong>
                    </legend>
					<button id="importQueryList" type="button">
                        <span class="text-only">重新引進查詢名單 </span>
                    </button>
                    <button id="rpaQuery" type="button">
                        <span class="text-only">一鍵查詢 </span>
                    </button>
                    <span style='margin-right:36px;' class='color-red'>(點選即一鍵查詢下列資料)</span>
					<br/>
                    <div>
                        <span class="color-red " id="href_OneBtnQuery">＊初次使用請先「引進查詢名單」，查詢項目及對象請參閱下方說明：</span>
						<br>
						<span class="color-red " id="href_OneBtnQuery">註1.持股10%以上大股東、經理人名單及查詢項目請自行新增。</span>
                    </div>
                    <table border='1' width="100%">
                        <tr>
                            <td width="50%" style='background-color:#D6EAF8; '>
                                外部系統&nbsp;<span style="color:blue;">[預設查詢對象]</span>
					      <span style="color:red;">[自建查詢對象]</span>
                            </td>
                            <td width="50%" style='background-color:#AED6F1; '>
                                資料建檔系統&nbsp;<span style="color:blue;">[預設查詢對象]</span>
							 <span style="color:red;">[自建查詢對象]</span>
                            </td>
                        </tr>
                        <tr style='vertical-align:top;'>
                            <td nowrap="nowrap" style='border-bottom:0px; padding-right:12px;'>
                                <table border='0' class='tb2'>
                                    <tr style='vertical-align:top;'>
                                        <td class='noborder'>
                                            01.公司登記事項卡(商工資料)&nbsp;<span style="color:blue;">[借戶企業、共同借款人]</span>
                                            <br/>
                                            02.稅籍登記資料公示查詢&nbsp;<span style="color:blue;">[借戶企業、共同借款人]</span>
                                            <br/>
                                            03.身份證換補查詢&nbsp;<span style="color:blue;">[借戶個人、負責人、連保人個人]</span>
                                            <br/>
                                            04.受監護輔助宣告查詢(家事公告)&nbsp;<span style="color:blue;">[借戶個人、負責人、連保人個人]</span>
											                                   
                                        </td>
                                    </tr>
                                </table>
                            </td>
                            <td nowrap="nowrap" style='border-bottom:0px; padding-right:12px;'>
                                <table border='0' class='tb2'>
                                    <tr style='vertical-align:top;'>
                                        <td class='noborder'>
                                        	<!--J-110-0540_05097_B1001 Web e-Loan企金授信配合調整E-loan系統動用審核表部分內容-->
                                            05. 利害關係人查詢 &nbsp;<span style="color:blue;">[借戶、共同借款人、負責人、董監事]</span>
											                       <span style="color:red;">[經理人、持股10%以上大股東]</span>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                    <br/>
                    <button id="addQuery" type="button">
                        <span class="text-only">新增查詢名單 </span>
                    </button>
                    <button id="deleteQuery" type="button">
                        <span class="text-only">刪除名單 </span>
                    </button>
                    <div id="gridviewQuery" ></div>
                    <br/>
                    <button id="refresh" type="button">
                        <span class="text-only">取回查詢結果 </span>
                    </button>
                    <button id="reTry" type="button">
                        <span class="text-only">單筆重新查詢 </span>
                    </button>
                    <button id="printAll" type="button" class="forview">
                        <span class="text-only">一鍵列印 </span>
                    </button>
					<br><span style='margin-right:36px;' class='color-red'>查詢完成後，請確認資料是否可以正常列印。</span>
                    <div id="gridviewRpaInfo" ></div>
                </fieldset>
            </form><!-- pop up screen -->
            <div id="L160S01EDetail" style="display: none;">
                <form id="L160S01EForm01">
                    <!--<fieldset style="width:900px;">-->
                    <fieldset>
                        <legend>
                            <b>資料查詢名單</b>
                        </legend>
                        <p/><b>文件產生方式：</b>
                        <span class="field" id="createBY2" name="createBY2"></span>
                        <span class="field color-blue max" id="createBY" name="createBY" maxlength="3" style="display:none;"></span>
                        <p/>
                        <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                            <tbody>
                                <tr>
                                    <td width="20%" class="hd1">
                                        與本案關係&nbsp;&nbsp;
                                    </td>
                                    <td width="30%">
                                        <input type="checkbox" name="custRelation" id="custRelation" />
                                    </td>
                                </tr>
                                <tr>
                                    <td width="20%" class="hd1">
                                        查詢項目&nbsp;&nbsp;
                                    </td>
                                    <td width="30%">
                                        <input type="checkbox" name="type" id="type" />
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <br/>
                        <div class="funcContainer">
                        </div>
						<input type="hidden" id="oid" name="oid"/>
                        <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                            <tbody>
                                <tr>
                                    <td class="hd2" colspan="4">
                                        名單內容
                                    </td>
                                </tr>
                                <tr>
                                    <td class="hd1" width="20%">
                                        本案關係人統編&nbsp;&nbsp;
                                    </td>
                                    <td width="30%">
                                        <input type="text" id="custId" name="custId" class="upText" maxlength="10"/>
                                    </td>
                                    <td class="hd1" width="20%">
                                        重覆序號&nbsp;&nbsp;
                                    </td>
                                    <td width="30%">
                                        <input type="text" id="dupNo" name="dupNo" maxlength="1" size="5"/>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="hd1" width="20%">
                                        戶名&nbsp;&nbsp;
                                        <br>
                                    </td>
                                    <td width="30%" colspan="3">
                                        <input type="text" id="custName" name="custName" class="required halfText" size="100" maxlength="150" maxlengthC="50" />
                                    </td>
                                </tr>
                                <tr>
                                    <td class="hd1" width="20%">
                                        查詢日&nbsp;&nbsp;
                                    </td>
                                    <td width="30%" colspan="3">
                                        <span id="queryDateS" class="field"></span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="hd1" width="30%">
                                        <th:block th:text="#{'L163M01A.queryIdDate'}">發證日期</th:block>&nbsp;&nbsp;
                                    </td>
                                    <td width="70%" colspan="3">
                                        <th:block th:text="#{'L163M01A.queryIdDateTitle'}">
                                            民國
                                        </th:block>
                                        <input type="text" class="number" id="idDateYear" name="idDateYear" maxlength="3" size="5" />
                                        <th:block th:text="#{'L163M01A.queryIdDateYear'}">
                                            年
                                        </th:block>
                                        <input type="text" class="number max min" id="idDateMonth" name="idDateMonth" maxlength="2" size="5" min="1" max="12"/>
                                        <th:block th:text="#{'L163M01A.queryIdDateMonth'}">
                                            月
                                        </th:block>
                                        <input type="text" class="number max min" id="idDateDay" name="idDateDay" maxlength="2" size="5" min="1" max="31" />
                                        <th:block th:text="#{'L163M01A.queryIdDateDay'}">
                                            日
                                        </th:block>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="hd1">
                                        <th:block th:text="#{'L163M01A.queryIdSite'}">發證地點</th:block>&nbsp;&nbsp;
                                    </td>
                                    <td colspan="3">
                                        <select id="idSite" name="idSite" class="" combokey="lms1601m01_IDSite" combotype="2" space="true" ></select>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="hd1">
                                        <th:block th:text="#{'L163M01A.queryIdChangeType'}">領補換類別</th:block>&nbsp;&nbsp;
                                    </td>
                                    <td colspan="3">
                                        <select id="idChangeType" name="idChangeType" class="">
                                            <option value="">請選擇</option>
                                            <option value="1">初發</option>
                                            <option value="2">補發</option>
                                            <option value="3">換發</option>
                                        </select>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </fieldset>
                </form>
            </div>
        </th:block>
    </body>
</html>
