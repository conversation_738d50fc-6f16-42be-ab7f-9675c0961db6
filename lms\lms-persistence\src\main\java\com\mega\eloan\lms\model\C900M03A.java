package com.mega.eloan.lms.model;

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/** FTP_H檔 **/
@Entity
@Table(name="C900M03A", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class C900M03A extends GenericBean implements IDataObject {

	private static final long serialVersionUID = 1L;
	
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(unique = true, nullable = false, length = 32, columnDefinition = "CHAR(32)")
	private String oid;
	
	@Column(name="FN", length=10, columnDefinition="CHAR(10)")
	private String fn;

	@Column(name="GENDATE", length=8, columnDefinition="CHAR(8)")
	private String genDate;
	
	@Column(name="GENTIME", length=6, columnDefinition="CHAR(6)")
	private String genTime;
	
	@Digits(integer=9, fraction=0)
	@Column(name="GENCNT", columnDefinition="DEC(9,0)")
	private BigDecimal genCnt;
	

	public String getOid() {
		return this.oid;
	}
	
	public void setOid(String value) {
		this.oid = value;
	}

	public String getFn() {
		return fn;
	}

	public void setFn(String fn) {
		this.fn = fn;
	}

	public String getGenDate() {
		return genDate;
	}

	public void setGenDate(String genDate) {
		this.genDate = genDate;
	}

	public String getGenTime() {
		return genTime;
	}

	public void setGenTime(String genTime) {
		this.genTime = genTime;
	}

	public BigDecimal getGenCnt() {
		return genCnt;
	}

	public void setGenCnt(BigDecimal genCnt) {
		this.genCnt = genCnt;
	}
}
