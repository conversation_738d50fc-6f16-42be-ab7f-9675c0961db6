package com.mega.eloan.lms.cls.report.impl;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.cls.report.CLS1220R01RptService;
import com.mega.eloan.lms.cls.service.CLS1220Service;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.C120S01B;
import com.mega.eloan.lms.model.C120S01D;
import com.mega.eloan.lms.model.C122M01A;

import tw.com.iisi.cap.formatter.NumericFormatter;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.PdfTools;
import tw.com.jcs.common.report.ReportGenerator;

@Service("cls1220r01rptservice")
public class CLS1220R01RptServiceImpl implements FileDownloadService,
		CLS1220R01RptService {

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(CLS1220R01RptServiceImpl.class);

	@Resource
	CLS1220Service cls1220Service;
	
	@Resource
	CodeTypeService codeTypeService;
	
	@Override
	public byte[] getContent(PageParameters params)
			throws FileNotFoundException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
			baos = (ByteArrayOutputStream) this.generateReport(params);
			return baos.toByteArray();
		} finally {
			if (baos != null) {
				baos.close();
			}

		}
	}

	private OutputStream generateReport(PageParameters params)
			throws FileNotFoundException, IOException, Exception {
		
		List<InputStream> list = new LinkedList<InputStream>();
		String[] dataSplit = Util.trim(params.getString("rptOid")).split("\\|");
		OutputStream outputStream = null;
		Locale locale = null;
		
		try {
			locale = LMSUtil.getLocale();
			Properties propEloanPage = MessageBundleScriptCreator.getComponentResource(AbstractEloanPage.class);
			int subLine = 9;//此數值對應的(x,y).要查 PdfTools.並不一定是愈小,愈上面
			for (String temp : dataSplit) {
				Map<InputStream,Integer> pdfNameMap = new LinkedHashMap<InputStream,Integer>();
				outputStream = null;
				String oid = temp.split("\\^")[0];
				C122M01A c122m01a = cls1220Service.getC122M01A_byOid(oid);
				outputStream = genCLS1220R01(locale, c122m01a);
				if(outputStream != null){
					pdfNameMap.put(new ByteArrayInputStream(((ByteArrayOutputStream) outputStream).toByteArray())
						, subLine);
				}else{
					pdfNameMap.put(null,subLine);
				}
				
				if(pdfNameMap != null && pdfNameMap.size() > 0){
					outputStream = new ByteArrayOutputStream();
					PdfTools.mergeReWritePagePdf(pdfNameMap, outputStream, propEloanPage.getProperty("PaginationText")
							, true, locale, subLine);
					list.add(new ByteArrayInputStream(((ByteArrayOutputStream) outputStream).toByteArray()));
				}
			}
			outputStream = new ByteArrayOutputStream();
			PdfTools.mergeReWritePagePdf(list, outputStream);
			
		} finally {
			
		}
		return outputStream;
	}
	public OutputStream genCLS1220R01(Locale locale, C122M01A c122m01a)
			throws FileNotFoundException, IOException, Exception {
		OutputStream outputStream = null;

		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		List<Map<String, String>> rptrowList = new ArrayList<Map<String, String>>();
		
		ReportGenerator generator = new ReportGenerator(
				"report/cls/CLS1220R01_" + locale.toString() + ".rpt");

		Map<String, String> cls_mateFlag = codeTypeService
				.findByCodeType("cls_mateFlag");
		Map<String, String> cls1131m01_edu = codeTypeService
				.findByCodeType("cls1131m01_edu");
		Map<String, String> cls1131m01_othType = codeTypeService
				.findByCodeType("cls1131m01_othType");
		Map<String, String> marry = codeTypeService.findByCodeType("marry");
		Map<String, String> lms1205s01_jobTitle = codeTypeService
				.findByCodeType("lms1205s01_jobTitle");
		
		Map<String, String> purposeMap = codeTypeService.findByCodeType("cls1141_purpose");
		Map<String, String> resourceMap = codeTypeService.findByCodeType("cls1141_resource");
		
		StringBuffer sign = new StringBuffer();


		C120S01A c120s01a = cls1220Service.findC120S01A(c122m01a.getMainId(), c122m01a.getCustId(), c122m01a.getDupNo());
		C120S01B c120s01b = cls1220Service.findC120S01B(c122m01a.getMainId(), c122m01a.getCustId(), c122m01a.getDupNo());
		if(c120s01a==null){
			c120s01a = new C120S01A();
		}
		if(c120s01b==null){
			c120s01b = new C120S01B();
		}
		//C120S01C c120s01c = cls1220Service.findC120S01C(c122m01a.getMainId(), c122m01a.getCustId(), c122m01a.getDupNo());
		C120S01D c120s01d = new C120S01D();
		//c120s01d.setMateFlag("A");
		
		String signChar = "<br>　　　　　　　　　　　　　　　　　　　　　　　　　　簽章<br>";
		sign.append("<font size='1'>主借款人：</font>(" + c122m01a.getCustId() + ")"
				+ signChar);

		rptVariableMap.put("custId", c122m01a.getCustId());
		rptVariableMap.put("custName", c122m01a.getCustName());
		rptVariableMap.put("birthday", c120s01a.getBirthday() == null ? ""
				: CapDate.formatDate(c120s01a.getBirthday(), "yyyy-MM-dd"));
		rptVariableMap.put("edu", cls1131m01_edu.get(c120s01a.getEdu()));
		rptVariableMap.put("child",
				String.valueOf(CapString.trimNull(c120s01a.getChild())));

		String comTarget = CapString.trimNull(c120s01b.getComTarget());
		comTarget = comTarget.indexOf("請選擇") >= 0 ? "" : comTarget;
		rptVariableMap.put("comTarget", comTarget);
		rptVariableMap.put("comName", c120s01b.getComName());
		rptVariableMap.put("comTel", c120s01b.getComTel());
		rptVariableMap.put("jobTitle",
				lms1205s01_jobTitle.get(c120s01b.getJobTitle()));
		rptVariableMap.put("seniority", LMSUtil.pretty_numStr(c120s01b.getSeniority()));

		String othType = Util.trim(c120s01b.getOthType());
		String[] othTypes = othType.split("\\|");
		StringBuffer othName = new StringBuffer();
		boolean flag = false;
		for (String othCode : othTypes) {
			if ("".equals(othCode)) {
				continue;
			}
			if (flag == true) {
				othName.append("，");
			}
			othName.append(cls1131m01_othType.get(othCode));
			flag = true;
		}

		rptVariableMap.put("othType", othName.toString());
		rptVariableMap.put("othCurr", Util.trim(c120s01b.getOthCurr()));
		rptVariableMap.put("othAmt", Util.trim(c120s01b.getOthAmt()));
		rptVariableMap.put("payCurr", Util.trim(c120s01b.getPayCurr()));
		rptVariableMap.put("payAmt", Util.trim(c120s01b.getPayAmt()));

		rptVariableMap
				.put("mateFlag", cls_mateFlag.get(c120s01d.getMateFlag()));
		rptVariableMap
				.put("mCustId", Util.trim(c120s01d.getMCustId()));
		rptVariableMap.put("mName", Util.trim(c120s01d.getMName()));
		rptVariableMap.put("mBirthday", c120s01d.getMBirthday() == null ? ""
				: CapDate.formatDate(c120s01d.getMBirthday(), "yyyy-MM-dd"));
		rptVariableMap.put("mComName",
				Util.trim(c120s01d.getMComName()));

		String mComTarget = Util.trim(c120s01d.getMComTarget());
		mComTarget = mComTarget.indexOf("請選擇") >= 0 ? "" : mComTarget;
		rptVariableMap.put("mComTarget", mComTarget);
		rptVariableMap
				.put("mComTel", Util.trim(c120s01d.getMComTel()));
		rptVariableMap.put("mJobTitle", c120s01d.getMJobTitle());
		rptVariableMap.put("mSeniority", Util.trim(c120s01d.getMSeniority()));
		rptVariableMap.put("mPayAmt", Util.trim(c120s01d.getMPayAmt()));
		rptVariableMap.put("mPayCurr", Util.trim(c120s01d.getMPayCurr()));

		String fTarget = CapString.trimNull(c120s01a.getFTarget());
		fTarget = fTarget.indexOf("請選擇") >= 0 ? "" : fTarget;
		rptVariableMap.put("fTarget", Util.trim(fTarget));
		rptVariableMap.put("fTel", Util.trim(c120s01a.getFTel()));
		rptVariableMap.put("email", Util.trim(c120s01a.getEmail()));

		String coTarget = Util.trim(c120s01a.getCoTarget());
		coTarget = coTarget.indexOf("請選擇") >= 0 ? "" : coTarget;
		rptVariableMap.put("coTarget", coTarget);
		rptVariableMap.put("coTel", Util.trim(c120s01a.getCoTel()));
		String dpBankName = Util.trim(c120s01a.getDpBankName());
		dpBankName = dpBankName.indexOf("請選擇") >= 0 ? "" : dpBankName;
		rptVariableMap.put("dpBankName", dpBankName);
		rptVariableMap.put("mTel", Util.trim(c120s01a.getMTel()));
		rptVariableMap.put("dpAcct", Util.trim(c120s01a.getDpAcct()));
		rptVariableMap.put("marry", Util.trim(marry.get(c120s01a.getMarry())));

		rptVariableMap.put("sign", sign.toString());
		rptVariableMap.put("currentApplyCurr", c122m01a.getApplyCurr());
		rptVariableMap.put("currentApplyAmt", new NumericFormatter().reformat(Util.trim(c122m01a.getApplyAmt()))+"萬");

		String maturity = "";
		String purpose = "";
		String resource = "";
		String extInfo = "";
		String notifyWay = "";
		if(true){
			if(c122m01a.getMaturity()!=null){
				maturity = pretty_numStr(Util.trim(c122m01a.getMaturity()))+"年"; 
			}
			if(Util.isNotEmpty(Util.trim(c122m01a.getPurpose()))){
				purpose = toStr(c122m01a.getPurpose(), purposeMap);
				
			}
			if(Util.isNotEmpty(Util.trim(c122m01a.getResource()))){
				resource = toStr(c122m01a.getResource(), resourceMap);
			}
			if(true){
				String nowExtend = Util.trim(c122m01a.getNowExtend());
				if(Util.equals("Y", nowExtend)){				
					if(c122m01a.getExtYear()!=null){
						extInfo = "有 "+pretty_numStr(Util.trim(c122m01a.getExtYear()))+"年";
					}else{
						extInfo = "有";
					}
				}else if(Util.equals("N", nowExtend)){
					extInfo = "無";
				}else{
					
				}
			}
			
			if(true){
				notifyWay = Util.trim(c122m01a.getNotifyWay());
				if(Util.equals("1", notifyWay)){
					notifyWay = "■郵寄    □e-mail   □不通知  (三選一)";
				}else if(Util.equals("2", notifyWay)){
					notifyWay = "□郵寄    ■e-mail   □不通知  (三選一)";
				}else if(Util.equals("3", notifyWay)){
					notifyWay = "□郵寄    □e-mail   ■不通知  (三選一)";
				}else {
					notifyWay = "□郵寄    □e-mail   □不通知  (三選一)";
				}
			}
		}
		rptVariableMap.put("maturity", maturity);
		rptVariableMap.put("purpose", purpose);
		rptVariableMap.put("resource", resource);
		rptVariableMap.put("extInfo", extInfo);
		rptVariableMap.put("notifyWay", notifyWay);
		
		generator.setVariableData(rptVariableMap);

		generator.setRowsData(rptrowList);
		outputStream = generator.generateReport();

		return outputStream;
	}
	
	private String toStr(String raw_src, Map<String, String> map){
		String src = Util.trim(raw_src);
		List<String> r = new ArrayList<String>();
		if(Util.isNotEmpty(src)){
			for(String s: src.split("\\|")){
				r.add(LMSUtil.getDesc(map, s));
			}
		}
		return StringUtils.join(r, "、");
	}
	
	private String pretty_numStr(String r){
		int idx = r.lastIndexOf(".");
		if(idx>0){
			String bf = r.substring(0, idx);
			String point = r.substring(idx, idx+1);
			String af = r.substring(idx+1);
			for(int i=0;i<10;i++){
				if(af.endsWith("0")){
					af = af.substring(0, af.length()-1);
				}
			}
			return bf+(Util.isNotEmpty(af)?(point+af):"");	
		}else{
			return r; 
		}
		
	}
}
