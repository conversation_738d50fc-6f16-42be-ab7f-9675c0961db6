<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<artifactId>lms</artifactId>
		<groupId>com.mega.eloan</groupId>
		<version>1.0.0-SNAPSHOT</version>
	</parent>
	<artifactId>lms-app-lms</artifactId>

	<dependencies>
		<dependency>
			<groupId>com.mega.eloan</groupId>
			<artifactId>lms-persistence</artifactId>
			<version>${project.version}</version>
			<type>jar</type>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>com.mega.eloan</groupId>
			<artifactId>lms-config</artifactId>
			<version>${project.version}</version>
			<type>jar</type>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>com.mega.eloan</groupId>
			<artifactId>lms-app</artifactId>
			<version>${project.version}</version>
			<type>jar</type>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>com.mega.eloan</groupId>
			<artifactId>lms-gateway</artifactId>
			<version>${project.version}</version>
			<type>jar</type>
			<scope>compile</scope>
		</dependency>
		<!-- JAVA Create Word doc -->
		<dependency>
		   <groupId>com.lowagie</groupId>
		   <artifactId>itext</artifactId>
		   <version>2.1.5</version>
		   <type>jar</type>
		   <scope>compile</scope>
		</dependency>
		<dependency>
	      <groupId>com.itextpdf</groupId>
	      <artifactId>itext-asian</artifactId>
	      <version>5.1.1</version>
		  <type>jar</type>
		  <scope>compile</scope>		  
	    </dependency>
		<dependency>
			<groupId>net.sourceforge.jexcelapi</groupId>
			<artifactId>jxl</artifactId>
			<version>2.6.12</version>
		</dependency>
	</dependencies>
</project>