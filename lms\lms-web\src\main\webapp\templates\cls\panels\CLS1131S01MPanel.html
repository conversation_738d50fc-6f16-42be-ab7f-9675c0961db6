<html xmlns="http://www.w3.org/1999/xhtml" 
        xmlns:th="http://www.thymeleaf.org">
	<body>
		<th:block th:fragment="panelFragmentBody">
                        <style>
				#C101M01ADiv .hd3_noscore {
				    font-weight: bold;
					align: center;
					text-align:center;
				}
				#C101M01ADiv .hd3_c101s01g {
				    font-weight: bold;
				    background: #EBFFEB;
				    color: #000000;
					align: center;
					text-align:center;
				}
				#C101M01ADiv .hd3_c101s01q {
				    font-weight: bold;
				    background: #FFD6FF;
				    color: #000000;
					align: center;
					text-align:center;
				}
				#C101M01ADiv .hd3_c101s01r {
				    font-weight: bold;
				    background: #FFD6FF;
				    color: #000000;
					align: center;
					text-align:center;
				}
			</style>
			<!-- 個金基本資料檔 -->
			<div id="C101M01ADiv" name="C101M01ADiv" >
				<form id="C101M01AForm" name="C101M01AForm" >
					<div class="naturalClass">
					<div>
						<table border='0'>
							<tr>
								<td class='noborder'><span class="text-red">＊</span><th:block th:text="#{'C101M01A.markModel.chose'}">請選擇適用之模型</th:block>
								</td>
								<td class='noborder'>
									<label style="letter-spacing:0px;cursor:pointer;">
										<input id="markModel" class="" type="checkbox" value="1" name="markModel">
										<th:block th:text="#{'C101M01A.markModel.1'}">房貸申請信用評等</th:block>
									</label>
								</td>
								<td class='noborder'>
									<label style="letter-spacing:0px;cursor:pointer;">
										<input id="markModel" class="" type="checkbox" value="2" name="markModel">
										<th:block th:text="#{'C101M01A.markModel.2'}">非房貸申請信用評等</th:block>
									</label>
								</td>
								<td class='noborder'>
									<label style="letter-spacing:0px;cursor:pointer;">
										<input id="markModel" class="" type="checkbox" value="0" name="markModel">
										<th:block th:text="#{'C101M01A.markModel.0'}">免辦評等</th:block>
									</label>
								</td>
							</tr>
						<th:block th:if="${HS_MARKMODEL_3}"><tr>
								<td class='noborder'>&nbsp;
								</td>
								<td class='noborder'>&nbsp;
								</td>
								<td class='noborder'>
									<label style="letter-spacing:0px;cursor:pointer;">
										<input id="markModel" class="" type="checkbox" value="3" name="markModel">
										<th:block th:text="#{'C101M01A.markModel.3'}">非房貸申請信用評等:專案信貸(非團體)</th:block>
									</label>
								</td>
								<td class='noborder'>&nbsp;
								</td>
							</tr>
							<tr>
								<td class='noborder'>
									<span class="text-red">＊</span>
									<label style="letter-spacing:0px;cursor:pointer;">
										<input id="isBailout4" class="" type="checkbox" value="Y" name="isBailout4">
										勞工紓困4.0
									</label>
								</td>
								<td class='noborder'>
									<label style="letter-spacing:0px;cursor:pointer;">
										<input id="concentrateCredit" class="" type="checkbox" value="Y" name="concentrateCredit">
										<th:block th:text="#{'title.C101S01Z'}">信貸集中徵信</th:block>
									</label>
								</td>
								<td class='noborder'>
									<label style="letter-spacing:0px;cursor:pointer;">
										<input id="prodKindFlag" class="" type="checkbox" value="Y" name="prodKindFlag">
										行員消貸系統初評
									</label>
								</td>
								<td class='noborder'>&nbsp;
								</td>
							</tr>
						</th:block>						</table>
						
						
					</div>
					<div id="fieldset_markModel_0" style="display:none">
						<span class="text-red">＊免辦評等</span>
						<div id="gradeDiv_markModel_0" >
							<table class="tb2" width="100%">
							<tr>
								<td class="hd2 hd3_noscore" ><th:block th:text="#{'C101S01G.alertMsg'}">票交所與聯徵特殊負面資訊</th:block></td>
							</tr>
							<tr>
								<td><span id="alertMsg_markModel_0" class="field" ></span>&nbsp;</td>
							</tr>
							</table>
						</div>
					</div>
					<fieldset id="fieldset_markModel_1" style="display:none">
						<legend>
                             <b>房貸信評</b>
                        </legend>
					<div id="gradeDiv_markModel_1" >
						<div id="href_HouseLoanNoticeItem"><span class="text-red">＊評等前<u style="cursor:pointer;">請先參閱注意事項(房貸申請信用評等)</u></span></div>						
						<button type="button" id="btScoreInfo_markModel_1" name="btScoreInfo_markModel_1" class="forview" ><span class="text-only"><th:block th:text="#{'C101S01G.btScoreInfo'}">開啟等級評分表</th:block></span></button>
						<!--<div id="href_LtvInfo"><span class="text-red"><u style="cursor:pointer;">(房貸利率試算規則說明)</u></span><input type="hidden" id="ltv_version" name="ltv_version"/></div>-->
						<button type="button" id="btLtvInfo" name="btLtvInfo" class="forview"><span class="text-only"><th:block th:text="#{'LTV.btLtv'}">房貸利率試算</th:block></span></button>
						<!--<div style="color:red;font-weight:bold;"><span class="text-red" id="span_HouseInfoText" style="display:none">本案房貸於授權內不得低於<span class="text-red" id="span_HouseRate">99.99</span>% 。</span></div>-->
						<div style="color:red;font-weight:bold;font-size: 18px;"><span id="span_HouseResultMsg" style="display:none"></span></div>
						<div style="color:red;font-weight:bold;font-size: 18px;"><span id="span_HouseErrorText" style="display:none"></span></div>
						<!-- 評等等級表 -->
						<table class="tb2" width="100%">
							<tr>
								<td width="12%" class="hd3_c101s01g" ><th:block th:text="#{'label.varVer'}">版本</th:block></td>
								<td width="6%" ><span id="varVer_markModel_1" ></span>&nbsp;</td>
								<td width="15%" class="hd3_c101s01g" ><th:block th:text="#{'C101S01G.grade1'}">初始評等</th:block></td>
								<td width="12%" ><span id="grade1_markModel_1" class="field" ></span>&nbsp;</td>
								<td width="15%" class="hd3_c101s01g" ><th:block th:text="#{'C101S01G.grade2'}">調整評等</th:block></td>
								<td width="12%" >
									<!-- 若在 class 屬性添加 readOnly 會使 'text-decoration': 'none', 'cursor': 'default' => 造成在簽報書無法開啟 adjust 頁面 -->
									<a href="#" id="btAdjust_markModel_1" class="" ><span id="grade2Status_markModel_1" class="field" ></span></a>
									<!-- <button type="button" id="btAdjust" class="forview" ><span class="text-only"><th:block th:text="#{'C101S01G.adjust'}">調整</th:block></span></button> -->
								</td>
								<td width="15%" class="hd3_c101s01g" ><th:block th:text="#{'C101S01G.grade3'}">最終評等</th:block></td>
								<td width="12%" ><span id="grade3_markModel_1" class="field" ></span>&nbsp;</td>
							</tr>
							
							<!-- 雙軌評分顯示 Start -->
							<tr id = "scoreDoubleTrack_G_notes" style="display:none;">
								<td colspan="8" class="" ><th:block th:text="#{'scoreDoubleTrack.title.G3_0'}">房貸模型3.0尚未正式啟用，僅供參考</th:block></td>
							</tr>
							<tr id = "scoreDoubleTrack_G_info" style="display:none;">
								<td width="12%" class="hd3_c101s01g" style="color:gray;"><th:block th:text="#{'label.varVer'}">版本</th:block></td>
								<td width="6%" style="color:gray;"><span id="varVer_markModel_1_N" ></span>&nbsp;</td>
								<td width="15%" class="hd3_c101s01g" style="color:gray;"><th:block th:text="#{'C101S01G.grade1'}">初始評等</th:block></td>
								<td width="12%" style="color:gray;"><span id="grade1_markModel_1_N" class="field" ></span>&nbsp;</td>
								<td width="15%" class="hd3_c101s01g" style="color:gray;"><th:block th:text="#{'C101S01G.grade2'}">調整評等</th:block></td>
								<td width="15%" class="field" style="color:gray;"><th:block th:text="#{'C101S01G.noChange'}">(無法調整)</th:block></td>
								<td width="15%" class="hd3_c101s01g" style="color:gray;"><th:block th:text="#{'C101S01G.grade3'}">最終評等</th:block></td>
								<td width="12%" style="color:gray;"><span id="grade3_markModel_1_N" class="field" ></span>&nbsp;</td>
							</tr>
							<!-- 雙軌評分顯示 End -->
							
							<tr>
								<td colspan="8" class="hd3_c101s01g" ><th:block th:text="#{'C101S01G.alertMsg'}">票交所與聯徵特殊負面資訊</th:block></td>
							</tr>
							<tr>
								<td colspan="8" ><span id="alertMsg_markModel_1" class="field" ></span>&nbsp;</td>
							</tr>
							<!-- 房貸區塊 -->
							<tr class='hs_jcicFlg hs_jcicFlg_VNN_'>
								<td colspan="8" class="text-red">
								*請注意：該客戶暫無足夠信用往來資訊可供模型評分。<br/>
								信用往來資訊不足原因包含未持有信用卡，且無授信往來紀錄，請加強對客戶其他面向之資信評估，謹慎審視風險。
								<table border='0'>
									<tr style='vertical-align:top;'>
										<td nowrap class="text-red noborder">註1:</td>
										<td class="text-red noborder">
											是否持有信用卡、授信往來紀錄之資料來源為 <span class='date_jcicFlg_V_NN'></span> 之聯徵 KRM040(信用卡戶帳款金額、循環比率及無擔保授信資訊) 及 BAM095(授信額度,擔保品,金額,還款紀錄資訊) 查詢結果。
										</td>
									</tr>
									<tr>
										<td nowrap class="text-red noborder">註2:</td>
										<td class="text-red noborder">
											當客戶同時無聯徵KRM040及BAM095查詢結果時才會顯示此訊息。
										</td>
									</tr>
								</table>
								
								</td>										
							</tr>
						</table>
					</div>
					</fieldset>
					<fieldset id="fieldset_markModel_2" style="display:none">
						<legend>
                             <b>非房貸信評</b>
                        </legend>
					<div id="gradeDiv_markModel_2" >
						<div id="href_NotHouseLoanNoticeItem"><span class="text-red">＊評等前<u style="cursor:pointer;">請先參閱注意事項(非房貸申請信用評等)</u></span></div>						
						<button type="button" id="btScoreInfo_markModel_2" name="btScoreInfo_markModel_2" class="forview" ><span class="text-only"><th:block th:text="#{'C101S01G.btScoreInfo'}">開啟等級評分表</th:block></span></button>
						<!-- 評等等級表 -->
						<table class="tb2" width="100%">
							<tr>
								<td width="12%" class="hd3_c101s01q" ><th:block th:text="#{'label.varVer'}">版本</th:block></td>
								<td width="6%" ><span id="varVer_markModel_2" ></span>&nbsp;</td>								
								<td width="15%" class="hd3_c101s01q" ><th:block th:text="#{'C101S01G.grade1'}">初始評等</th:block></td>
								<td width="12%" ><span id="grade1_markModel_2" class="field" ></span>&nbsp;</td>
								<td width="15%" class="hd3_c101s01q" ><th:block th:text="#{'C101S01G.grade2'}">調整評等</th:block></td>
								<td width="12%" >
									<!-- 若在 class 屬性添加 readOnly 會使 'text-decoration': 'none', 'cursor': 'default' => 造成在簽報書無法開啟 adjust 頁面 -->
									<a href="#" id="btAdjust_markModel_2" class="" ><span id="grade2Status_markModel_2" class="field" ></span></a>
									<!-- <button type="button" id="btAdjust" class="forview" ><span class="text-only"><th:block th:text="#{'C101S01G.adjust'}">調整</th:block></span></button> -->
								</td>
								<td width="15%" class="hd3_c101s01q" ><th:block th:text="#{'C101S01G.grade3'}">最終評等</th:block></td>
								<td width="12%" ><span id="grade3_markModel_2" class="field" ></span>&nbsp;</td>
							</tr>
							<!-- 雙軌評分顯示 Start -->
							<tr id = "scoreDoubleTrack_Q_notes" style="display: none;">
								<td colspan="8" class="" style="color:gray;"><th:block th:text="#{'scoreDoubleTrack.title.Q4_0'}">非房貸模型4.0尚未正式啟用，僅供參考</th:block></td>
							</tr>
							<tr id = "scoreDoubleTrack_Q_info" style="display: none;">
								<td width="12%" class="hd3_c101s01q" style="color:gray;"><th:block th:text="#{'label.varVer'}">版本</th:block></td>
								<td width="6%" style="color:gray;"><span id="varVer_markModel_2_N" ></span>&nbsp;</td>
								<td width="15%" class="hd3_c101s01q" style="color:gray;"><th:block th:text="#{'C101S01G.grade1'}">初始評等</th:block></td>
								<td width="12%" style="color:gray;"><span id="grade1_markModel_2_N" class="field" ></span>&nbsp;</td>
								<td width="15%" class="hd3_c101s01q" style="color:gray;"><th:block th:text="#{'C101S01G.grade2'}">調整評等</th:block></td>
								<td width="15%" class="field" style="color:gray;"><th:block th:text="#{'C101S01G.noChange'}">(無法調整)</th:block></td>
								<td width="15%" class="hd3_c101s01q" style="color:gray;"><th:block th:text="#{'C101S01G.grade3'}">最終評等</th:block></td>
								<td width="12%" style="color:gray;"><span id="grade3_markModel_2_N" class="field" ></span>&nbsp;</td>
							</tr>
							<!-- 雙軌評分顯示 End -->
							<tr>
								<td colspan="8" class="hd3_c101s01q" ><th:block th:text="#{'C101S01G.alertMsg'}">票交所與聯徵特殊負面資訊</th:block></td>
							</tr>
							<tr>
								<td colspan="8" ><span id="alertMsg_markModel_2" class="field" ></span>&nbsp;</td>								
							</tr>
							<!-- 非房貸區塊 -->
							<tr class='hs_jcicFlg hs_jcicFlg_VNN_'>
								<td colspan="8" class="text-red">
								*請注意：該客戶暫無足夠信用往來資訊可供模型評分。<br/>
								信用往來資訊不足原因包含未持有信用卡，且無授信往來紀錄，請加強對客戶其他面向之資信評估，謹慎審視風險。
								<table border='0'>
									<tr style='vertical-align:top;'>
										<td nowrap class="text-red noborder">註1:</td>
										<td class="text-red noborder">
											是否持有信用卡、授信往來紀錄之資料來源為 <span class='date_jcicFlg_V_NN'></span> 之聯徵 KRM040(信用卡戶帳款金額、循環比率及無擔保授信資訊) 及 BAM095(授信額度,擔保品,金額,還款紀錄資訊) 查詢結果。
										</td>
									</tr>
									<tr>
										<td nowrap class="text-red noborder">註2:</td>
										<td class="text-red noborder">
											當客戶同時無聯徵KRM040及BAM095查詢結果時才會顯示此訊息。
										</td>
									</tr>
								</table>
								
								</td>										
							</tr>
						</table>
					</div>
					</fieldset>		
					<fieldset id="fieldset_markModel_3" style="display:none">
						<legend>
                             <b>非房貸信評:專案信貸(非團體)</b>
                        </legend>
					<div id="gradeDiv_markModel_3" >
						<table border='0'>
							<tr>
								<td class='noborder'>
									<div id="href_CardLoanNoticeItem"><span class="text-red">＊評等前<u style="cursor:pointer;">請先參閱注意事項(非房貸申請信用評等:專案信貸(非團體))</u></span></div>
								</td>
								<td class='noborder' width='200px'>
									&nbsp;
								</td>
								<td class='noborder'>
									&nbsp;
								</td>
							</tr>
						</table>
												
						<button type="button" id="btScoreInfo_markModel_3" name="btScoreInfo_markModel_3" class="forview" ><span class="text-only"><th:block th:text="#{'C101S01G.btScoreInfo'}">開啟等級評分表</th:block></span></button>						
						<table class="tb2" width="100%">
							<tr>
								<td width="5%" class="hd3_c101s01r" ><th:block th:text="#{'label.varVer'}">版本</th:block></td>
								<td width="3%" ><span id="varVer_markModel_3" ></span>&nbsp;</td>								
								<td width="8%" class="hd3_c101s01r" ><th:block th:text="#{'C101S01R.grade1'}">初始評等</th:block></td>
								<td width="3%" ><span id="grade1_markModel_3" class="field" ></span>&nbsp;</td>
								
								<td width="8%" class="hd3_c101s01r" ><th:block th:text="#{'C101S01R.sprtRating'}">支援評等</th:block></td>
								<td width="3%" ><span id="sprtRating_markModel_3" class="field" ></span>&nbsp;</td>
								<td width="8%" class="hd3_c101s01r" ><th:block th:text="#{'C101S01R.grade2'}">調整評等</th:block></td>
								<td width="7%" ><!-- 若在 class 屬性添加 readOnly 會使 'text-decoration': 'none', 'cursor': 'default' => 造成在簽報書無法開啟 adjust 頁面 -->
												<a href="#" id="btAdjust_markModel_3" class="" ><span id="grade2Status_markModel_3" class="field" ></span></a>									
								</td>
								<td width="12%" class="hd3_c101s01r" ><th:block th:text="#{'C101S01R.grade3'}">模型最終評等</th:block></td>
								<td width="3%" ><span id="grade3_markModel_3" class="field" ></span>&nbsp;</td>
							</tr>
							
							<!-- 雙軌評分顯示 Start -->
							<tr id = "scoreDoubleTrack_R_notes" style="display: none;">
								<td colspan="8" class="" style="color:gray;"><th:block th:text="#{'scoreDoubleTrack.title.Q4_0'}">非房貸模型4.0尚未正式啟用，僅供參考</th:block></td>
							</tr>
							<tr id = "scoreDoubleTrack_R_info" style="display: none;">
								<td width="5%" class="hd3_c101s01r" style="color:gray;"><th:block th:text="#{'label.varVer'}">版本</th:block></td>
								<td width="3%" style="color:gray;"><span id="varVer_markModel_3_N" ></span>&nbsp;</td>								
								<td width="8%" class="hd3_c101s01r" style="color:gray;"><th:block th:text="#{'C101S01R.grade1'}">初始評等</th:block></td>
								<td width="3%" style="color:gray;"><span id="grade1_markModel_3_N" class="field" ></span>&nbsp;</td>
								<td width="8%" class="hd3_c101s01r" style="color:gray;"><th:block th:text="#{'C101S01R.sprtRating'}">支援評等</th:block></td>
								<td width="3%" style="color:gray;"><span id="sprtRating_markModel_3_N" class="field" ></span>&nbsp;</td>
								<td width="8%" class="hd3_c101s01r" style="color:gray;"><th:block th:text="#{'C101S01R.grade2'}">調整評等</th:block></td>
								<td width="7%" class="field" style="color:gray;"><th:block th:text="#{'C101S01G.noChange'}">(無法調整)</th:block></td>		
								<td width="12%" class="hd3_c101s01r" style="color:gray;"><th:block th:text="#{'C101S01R.grade3'}">模型最終評等</th:block></td>
								<td width="3%" style="color:gray;"><span id="grade3_markModel_3_N" class="field" ></span>&nbsp;</td>
							</tr>
							<!-- 雙軌評分顯示 End -->
							
							<tr>
								<td class="hd3_c101s01r" colspan="3"><th:block th:text="#{'C101S01R.JCIC_j10'}">聯徵J10</th:block></td>
								<td colspan="7"><span id="j10_markModel_3" class="field" ></span>&nbsp;</td>
							</tr>	
							<tr style="vertical-align:top;">	
								<td class="hd3_c101s01r" colspan="3">聯徵個人信用評分（J10）理由說明代號與文字&nbsp;&nbsp;<span id="href_TW_J10_REASON" class="text-red"><u style="cursor:pointer;">理由代碼對照表</u></span>
								</td>
								<td colspan="7">
									<span id="KCS003Reason_markModel_3" class="field" ></span>&nbsp;	
								</td>								
							</tr>	
							<tr>
								<td colspan="10" class="hd3_c101s01r" ><th:block th:text="#{'C101S01G.alertMsg'}">票交所與聯徵特殊負面資訊</th:block></td>
							</tr>
							<tr>
								<td colspan="10" ><span id="alertMsg_markModel_3" class="field" ></span>&nbsp;</td>								
							</tr>	
							<!-- 卡友貸區塊 -->
							<tr class='hs_jcicFlg hs_jcicFlg_VNN_'>
								<td colspan="10" class="text-red">
								*請注意：該客戶暫無足夠信用往來資訊可供模型評分。<br/>
								信用往來資訊不足原因包含未持有信用卡，且無授信往來紀錄，請加強對客戶其他面向之資信評估，謹慎審視風險。
								<table border='0'>
									<tr style='vertical-align:top;'>
										<td nowrap class="text-red noborder">註1:</td>
										<td class="text-red noborder">
											是否持有信用卡、授信往來紀錄之資料來源為 <span class='date_jcicFlg_V_NN'></span> 之聯徵 KRM040(信用卡戶帳款金額、循環比率及無擔保授信資訊) 及 BAM095(授信額度,擔保品,金額,還款紀錄資訊) 查詢結果。
										</td>
									</tr>
									<tr>
										<td nowrap class="text-red noborder">註2:</td>
										<td class="text-red noborder">
											當客戶同時無聯徵KRM040及BAM095查詢結果時才會顯示此訊息。
										</td>
									</tr>
								</table>
								
								</td>										
							</tr>
							<tr>
								<td colspan="10"  class="text-red">
								註：請注意，辦理非房貸信評:專案信貸(非團體)時，須於e-JCIC查詢系統中，查詢「組合查詢產品七（個人信用狀況）」時，"同一天" 加查「標準查詢-J10個人信用評分」，方能產出評等。									
								</td>								
							</tr>	
							<tr style="vertical-align:top;">
								<td class="hd3_c101s01r" colspan="3">具擔保放款(不含信保)註記&nbsp;&nbsp;<span id="href_isKind" class="text-red"></span>
								</td>
								<td colspan="1">
									<span id="isKind" class="field" ></span>&nbsp;	
								</td>
							</tr>
						
						</table>
					</div>
					</fieldset>							 
					</div>
					<!-- 本行婉卻資訊 -->
					<table id="rejectInfo" class="tb2 startHide" width="100%">
						<tr>
							<td width="18%" class="hd2" align="right" ><th:block th:text="#{'C101M01A.bfRejBranch'}">前婉卻分行</th:block>&nbsp;&nbsp;</td>
							<td width="32%" ><span id="bfRejBranch" class="field" ></span></td>
							<td width="18%" class="hd2" align="right" ><th:block th:text="#{'C101M01A.bfRejDate'}">前婉卻日期</th:block>&nbsp;&nbsp;</td>
							<td width="32%" ><span id="bfRejDate" class="field" ></span></td>
						</tr>
						<tr>
							<td width="18%" class="hd2" align="right" ><th:block th:text="#{'C101M01A.bfRejReason'}">前婉卻原因</th:block>&nbsp;&nbsp;</td>
							<td width="32%" colspan="3"><span id="bfRejReason" class="field" ></span></td>
						</tr>
						<tr>
							<td width="18%" class="hd2" align="right" ><th:block th:text="#{'C101M01A.bfRejCase'}">前婉卻狀態</th:block>&nbsp;&nbsp;</td>
							<td width="32%" colspan="3"><input type="radio" id="bfRejCase" name="bfRejCase" codeType="lms_rejCase" /></td>
						</tr>
						<tr>
							<td width="18%" class="hd2" align="right" ><th:block th:text="#{'C101M01A.rejectCase'}">現婉卻狀態</th:block>&nbsp;&nbsp;</td>
							<td width="32%" >
								<input type="radio" id="rejectCase" name="rejectCase" codeType="lms_rejCase" />
                                <div id="erjButton">
                                    <button type="button" id="btEditReject">
                                        <span class="text-only"><th:block th:text="#{'C101M01A.editReject'}">修改婉卻控管種類</th:block></span>
                                    </button>
                                </div>
								<br/>(<th:block th:text="#{'C101M01A.limit'}">限由授權外調整</th:block>)
							</td>
							<td colspan="2">
								<textarea id="rejectMemo" name="rejectMemo" style="width:90%" class="max" maxlength="384" ></textarea>
							</td>
						</tr>
					</table>
					
					<!-- 異常通報紀錄 -->
					<div id="abnormalInfo" style='display:none;'>						
						<table  class="tb2" width="100%">
							<tr>
								<td width="30%" class="hd2" align="right" ><th:block th:text="#{'C101S01E.isQdata29'}">異常通報紀錄</th:block>&nbsp;</td>
								<td width="70%">
									<input type="radio" id="isAbnormal" name="isAbnormal" value='Y' disabled /><th:block th:text="#{'have'}">有</th:block>&nbsp;
										<!-- 開啟文件 -->										
										<button type="button" id="btViewAbnormalDoc" class="isAbnormal_Y">
				                        	<span class="text-only">調閱</span>
				                        </button>	
										
									<input type="radio" id="isAbnormal" name="isAbnormal" value='N' disabled  style='padding-left:30px;' /><th:block th:text="#{'nohave'}">無</th:block>&nbsp;													
									
									<span class="" style='padding-left:30px;'><th:block th:text="#{'C101M01A.abnormalReadDate.abbr'}">查詢日期</th:block>：</span>
									<span id="abnormalReadDate" class="field " ></span>
									<div class="isAbnormal_Y">
										<span class="text-red">申貸戶曾有異常通報紀錄，最近一次通報資訊如下：</span>
									</div>																	
								</td>
							</tr>
							<tr class="isAbnormal_Y">
								<td class="hd2" align="right" ><th:block th:text="#{'C101M01A.abnormalBrNo'}">通報分行</th:block>&nbsp;</td>
								<td><span id="abnormalBrNo" class="field" ></span>&nbsp;</td>
							</tr>
							<tr class="isAbnormal_Y">
								<td class="hd2" align="right" ><th:block th:text="#{'C101M01A.abnormalDate'}">通報日期</th:block>&nbsp;</td>
								<td><span id="abnormalDate" class="field" ></span>&nbsp;</td>
							</tr>
							<tr class="isAbnormal_Y">
								<td class="hd2" align="right" ><th:block th:text="#{'C101M01A.abnormalStatus'}">目前異常通報狀態</th:block>&nbsp;</td>
								<td>
									<select name="abnormalStatus" id="abnormalStatus" readonly="readonly" disabled="true">
										<option value="" disabled="true">--請選擇--</option>
										<option value="Y" disabled="true"><th:block th:text="#{'C101M01A.abnormalStatus.Y'}">已解除</th:block></option>
				                        <option value="N" disabled="true"><th:block th:text="#{'C101M01A.abnormalStatus.N'}">未解除</th:block></option>		                      
				                    </select>
								</td>
							</tr>
						</table>
					</div>
					<table class="tb2" width="100%">
						<tr>
							<td colspan="3" style="color:red;background:#F5EBFF;margin-left:10px;" align="center">
								<th:block th:text="#{'C101M01A.batchSelectEjcicPrecautions.title01'}">徵信應注意事項：</th:block>
								<input id="batchSelectNoDoubtItem" class="" type="checkbox" value="Y" name="batchSelectNoDoubtItem">
								&nbsp;<th:block th:text="#{'C101M01A.batchSelectEjcicPrecautions.title02'}">(整批勾選)如經確認無下列情況，可直接點選此項。</th:block>
							</td>
						</tr>
						<tr>
							<td width="50%" class="hd2" align="right" ><span class="text-red">＊</span><th:block th:text="#{'C101M01A.isCheckOriginalDocument'}">已確實核對申貸文件正本資料</th:block>&nbsp;</td>
							<td width="10%">
								<input type="radio" id="isCheckOriginalDocument" name="isCheckOriginalDocument" value='Y' /><th:block th:text="#{'rdo.yes'}">是</th:block>&nbsp;
								<input type="radio" id="isCheckOriginalDocument" name="isCheckOriginalDocument" value='N' style='padding-left:30px;' /><th:block th:text="#{'rdo.no'}">否</th:block>&nbsp;													
							</td>
							<td>
								<div style="margin-left:43px;text-indent:-43px;">
									<font color="red">提醒：</font>
									<th:block th:text="#{'C101M01A.loanDocCheckFlag'}">申貸證明文件(如:身分證影本, 買賣合約書影本...等)應已加蓋「與正本相符」及「限辦理本行授信業務使用」之樣章</th:block>
									<font color="red"><th:block th:text="#{'C101M01A.notApplicableToOnlineLoan'}">(線上申貸案件不適用)</th:block></font>
								</div>
							</td>
						</tr>
						<tr>
							<td width="30%" class="hd2" align="right" ><span class="text-red">＊</span><th:block th:text="#{'C101M01A.isNoteBorrower'}">本案是否已確實直接照會借保人本人無誤</th:block>&nbsp;</td>
							<td colspan="2">
								<input type="radio" id="isNoteBorrower" name="isNoteBorrower" value='Y' /><th:block th:text="#{'rdo.yes'}">是</th:block>&nbsp;
								<input type="radio" id="isNoteBorrower" name="isNoteBorrower" value='N' style='padding-left:30px;' /><th:block th:text="#{'rdo.no'}">否</th:block>&nbsp;
								<span id="noteItem" class="text-red"><u style="cursor:pointer;"><th:block th:text="#{'isSuspectedHeadAccount.note.a'}">注意事項</th:block></u></span>									
							</td>
						</tr>
						<tr>
							<td width="30%" class="hd2" align="right" ><span class="text-red">＊</span><th:block th:text="#{'C101M01A.isImportDataMatch'}">引入資料是否與客戶申請書填寫內容相符</th:block>&nbsp;</td>
							<td colspan="2">
								<input type="radio" id="isImportDataMatch" name="isImportDataMatch" value='Y' /><th:block th:text="#{'rdo.yes'}">是</th:block>&nbsp;
								<input type="radio" id="isImportDataMatch" name="isImportDataMatch" value='N' style='padding-left:30px;' /><th:block th:text="#{'rdo.no'}">否</th:block>&nbsp;													
							</td>
						</tr>
						<tr>
							<td width="30%" class="hd2" align="right" ><span class="text-red">＊</span><th:block th:text="#{'C101M01A.isFullApplyDocument'}">借款人或保證人提供申請資料及證明文件過於完整</th:block>&nbsp;</td>
							<td colspan="2">
								<input type="radio" id="isFullApplyDocument" name="isFullApplyDocument" value='Y' /><th:block th:text="#{'rdo.yes'}">是</th:block>&nbsp;
								<input type="radio" id="isFullApplyDocument" name="isFullApplyDocument" value='N' style='padding-left:30px;' /><th:block th:text="#{'rdo.no'}">否</th:block>&nbsp;													
							</td>
						</tr>
						<tr>
							<td width="30%" class="hd2" align="right" ><span class="text-red">＊</span><th:block th:text="#{'C101M01A.isDocSignatureNotMatch'}">買賣契約書(如有)、借款契約、借款申請書簽名任一不一致</th:block>&nbsp;</td>
							<td colspan="2">
								<input type="radio" id="isDocSignatureNotMatch" name="isDocSignatureNotMatch" value='Y' /><th:block th:text="#{'rdo.yes'}">是</th:block>&nbsp;
								<input type="radio" id="isDocSignatureNotMatch" name="isDocSignatureNotMatch" value='N' style='padding-left:30px;' /><th:block th:text="#{'rdo.no'}">否</th:block>&nbsp;													
							</td>
						</tr>
						<tr>
							<td width="30%" class="hd2" align="right" ><span class="text-red">＊</span><th:block th:text="#{'C101M01A.isOwnerBuyerNotSamePerson'}">借款人、擔保品所有權人與房屋契約書之買方不同人(非房貸點"否/不適用")</th:block>&nbsp;</td>
							<td colspan="2">
								<input type="radio" id="isOwnerBuyerNotSamePerson" name="isOwnerBuyerNotSamePerson" value='Y' /><th:block th:text="#{'rdo.yes'}">是</th:block>&nbsp;
								<input type="radio" id="isOwnerBuyerNotSamePerson" name="isOwnerBuyerNotSamePerson" value='N' style='padding-left:30px;' />
								<th:block th:text="#{'rdo.no'}">否</th:block>/<th:block th:text="#{'rdo.notApplicable'}">不適用</th:block>
							</td>
						</tr>
						<tr>
							<td width="30%" class="hd2" align="right" ><span class="text-red">＊</span><th:block th:text="#{'C101M01A.isWithNonRelatives'}">由非親屬之第三人陪同申辦貸款</th:block>&nbsp;</td>
							<td colspan="2">
								<input type="radio" id="isWithNonRelatives" name="isWithNonRelatives" value='Y' /><th:block th:text="#{'rdo.have'}">有</th:block>&nbsp;
								<input type="radio" id="isWithNonRelatives" name="isWithNonRelatives" value='N' style='padding-left:30px;' /><th:block th:text="#{'rdo.nohave'}">無</th:block>&nbsp;													
							</td>
						</tr>
						<tr>
							<td width="30%" class="hd2" align="right" ><span class="text-red">＊</span><th:block th:text="#{'C101M01A.isPointAppropriationDate'}">指定撥款日期及時間，且無法提出合理解釋</th:block>&nbsp;</td>
							<td colspan="2">
								<input type="radio" id="isPointAppropriationDate" name="isPointAppropriationDate" value='Y' /><th:block th:text="#{'rdo.yes'}">是</th:block>&nbsp;
								<input type="radio" id="isPointAppropriationDate" name="isPointAppropriationDate" value='N' style='padding-left:30px;' />
								<th:block th:text="#{'rdo.no'}">否</th:block>/<th:block th:text="#{'rdo.noDesignation'}">無指定</th:block>											
							</td>
						</tr>
						<tr>
							<td width="30%" class="hd2" align="right" ><span class="text-red">＊</span><th:block th:text="#{'C101M01A.isDontKnowOwnAffairs'}">對自己從事之行業或職業性質與內容、購屋或借款目的不瞭解或毫無概念</th:block>&nbsp;</td>
							<td colspan="2">
								<input type="radio" id="isDontKnowOwnAffairs" name="isDontKnowOwnAffairs" value='Y' /><th:block th:text="#{'rdo.yes'}">是</th:block>&nbsp;
								<input type="radio" id="isDontKnowOwnAffairs" name="isDontKnowOwnAffairs" value='N' style='padding-left:30px;' /><th:block th:text="#{'rdo.no'}">否</th:block>&nbsp;													
							</td>
						</tr>
						<tr>
							<td width="30%" class="hd2" align="right" ><span class="text-red">＊</span><th:block th:text="#{'C101M01A.isDontExplainEjcicRecord'}">於聯徵具被查詢紀錄，卻無法說明原因</th:block>&nbsp;</td>
							<td colspan="2">
								<input type="radio" id="isDontExplainEjcicRecord" name="isDontExplainEjcicRecord" value='Y' /><th:block th:text="#{'rdo.yes'}">是</th:block>&nbsp;
								<input type="radio" id="isDontExplainEjcicRecord" name="isDontExplainEjcicRecord" value='N' style='padding-left:30px;' />
								<th:block th:text="#{'rdo.no'}">否</th:block>/<th:block th:text="#{'rdo.noRecord'}">無紀錄</th:block>												
							</td>
						</tr>
						<tr>
							<td width="30%" class="hd2" align="right" ><span class="text-red">＊</span><th:block th:text="#{'C101M01A.isPayOtherFee'}">支付銀行收取開辦手續費以外之費用以完成貸款目的(含確認有無代辦業者收費)</th:block>&nbsp;</td>
							<td colspan="2">
								<input type="radio" id="isPayOtherFee" name="isPayOtherFee" value='Y' /><th:block th:text="#{'rdo.yes'}">是</th:block>&nbsp;
								<input type="radio" id="isPayOtherFee" name="isPayOtherFee" value='N' style='padding-left:30px;' /><th:block th:text="#{'rdo.no'}">否</th:block>&nbsp;													
							</td>
						</tr>
						<tr>
							<td width="30%" class="hd2" align="right" ><span class="text-red">＊</span><th:block th:text="#{'C101M01A.isCashFromOthers'}">借戶所得來自建築業者、代銷、仲介時，或其身分屬前述對象之關係戶(股東、員工)時，是否由其交易資料確認購屋價金來自第三人，且無法佐證其與第三人之關係。</th:block>&nbsp;</td>
							<td colspan="2">
								<input type="radio" id="isCashFromOthers" name="isCashFromOthers" value='Y' /><th:block th:text="#{'rdo.yes'}">是</th:block>&nbsp;
								<input type="radio" id="isCashFromOthers" name="isCashFromOthers" value='N' style='padding-left:30px;' />
								<th:block th:text="#{'rdo.no'}">否</th:block>/<th:block th:text="#{'rdo.notApplicable'}">不適用</th:block>												
							</td>
						</tr>
					</table>
						
					<!-- J-113-0341 個金徵信作業新增「年輕族群客戶加強關懷提問單」 -->
					<fieldset id="fieldset_youngCareList" style="display:none;">
						<legend>
							<b><th:block th:text="#{'C101M01A.youngCareList'}">年輕族群客戶加強關懷提問單</th:block></b>
                        </legend>
					<th:block th:text="#{'C101M01A.youngCareListDesc'}">【年齡18歲以上未滿22歲之客戶申辦本行消金授信產品者適用】</th:block>
					<table class="tb2" width="100%">
						<tr>
							<td width="20%" class="hd2" align="center"><th:block th:text="#{'C101M01A.youngCareItem.title'}">關懷項目</th:block></td>
							<td width="60%" class="hd2" align="center"><th:block th:text="#{'C101M01A.youngCareContent.title'}">關懷內容</th:block></td>
							<td width="20%" class="hd2" align="center"><th:block th:text="#{'C101M01A.youngCareResult.title'}">關懷結果</th:block></td>
						</tr>
						<tr>
							<td width="20%" align="center"><span class="text-red">＊</span><th:block th:text="#{'C101M01A.youngCareItem1'}">申請目的</th:block></td>
							<td width="60%"><th:block th:text="#{'C101M01A.youngCareContent1'}">對於本次貸款申請目的及借款用途能清楚說明?</th:block></td>
							<td width="20%" align="center">
								<label><input type="radio" id="youngCareResult1" name="youngCareResult1" value='Y'/><th:block th:text="#{'rdo.yes'}">是</th:block></label>&nbsp;
								<label><input type="radio" id="youngCareResult1" name="youngCareResult1" value='N' style='padding-left:30px;'/><th:block th:text="#{'rdo.no'}">否</th:block></label>
							</td>
						</tr>
						<tr>
							<td width="20%" align="center"><span class="text-red">＊</span><th:block th:text="#{'C101M01A.youngCareItem2'}">產品認知</th:block></td>
							<td width="60%">
								<th:block th:text="#{'C101M01A.youngCareContent2_1'}">1.清楚認知申請業務為貸款產品及還款方式?</th:block>
								<br>
								<th:block th:text="#{'C101M01A.youngCareContent2_2'}">2.瞭解如有不正常還款行為將影響個人信用?</th:block>
							</td>
							<td width="20%" align="center">
								<label><input type="radio" id="youngCareResult2_1" name="youngCareResult2_1" value='Y'/><th:block th:text="#{'rdo.yes'}">是</th:block></label>&nbsp;
								<label><input type="radio" id="youngCareResult2_1" name="youngCareResult2_1" value='N' style='padding-left:30px;'/><th:block th:text="#{'rdo.no'}">否</th:block></label>
								<br>
								<label><input type="radio" id="youngCareResult2_2" name="youngCareResult2_2" value='Y'/><th:block th:text="#{'rdo.yes'}">是</th:block></label>&nbsp;
								<label><input type="radio" id="youngCareResult2_2" name="youngCareResult2_2" value='N' style='padding-left:30px;'/><th:block th:text="#{'rdo.no'}">否</th:block></label>
							</td>
						</tr>
						<tr>
							<td width="20%" align="center"><span class="text-red">＊</span><th:block th:text="#{'C101M01A.youngCareItem3'}">償債能力</th:block></td>
							<td width="60%">
								<th:block th:text="#{'C101M01A.youngCareContent3_1'}">1.客戶是否具有穩定之還款來源?</th:block>
								<br>
								<th:block th:text="#{'C101M01A.youngCareContent3_2'}">2.已衡量自身財務狀況，並瞭解本次申請對財務狀況可能造成之影響?</th:block>
							</td>
							<td width="20%" align="center">
								<label><input type="radio" id="youngCareResult3_1" name="youngCareResult3_1" value='Y' /><th:block th:text="#{'rdo.yes'}">是</th:block></label>&nbsp;
								<label><input type="radio" id="youngCareResult3_1" name="youngCareResult3_1" value='N' style='padding-left:30px;' /><th:block th:text="#{'rdo.no'}">否</th:block></label>
								<br>
								<label><input type="radio" id="youngCareResult3_2" name="youngCareResult3_2" value='Y' /><th:block th:text="#{'rdo.yes'}">是</th:block></label>&nbsp;
								<label><input type="radio" id="youngCareResult3_2" name="youngCareResult3_2" value='N' style='padding-left:30px;' /><th:block th:text="#{'rdo.no'}">否</th:block></label>
							</td>
						</tr>
						<tr>
							<td width="20%" align="center"><span class="text-red">＊</span><th:block th:text="#{'C101M01A.youngCareItem4'}">案件來源</th:block></td>
							<td width="60%">
								<th:block th:text="#{'C101M01A.youngCareContent4_1'}">1.是否有本行人員勸誘投資金融商品?</th:block>
								<br>
								<th:block th:text="#{'C101M01A.youngCareContent4_2'}">2.能清楚說明如何得知本次申請資訊?</th:block>
							</td>
							<td width="20%" align="center">
								<label><input type="radio" id="youngCareResult4_1" name="youngCareResult4_1" value='Y' /><th:block th:text="#{'rdo.yes'}">是</th:block></label>&nbsp;
								<label><input type="radio" id="youngCareResult4_1" name="youngCareResult4_1" value='N' style='padding-left:30px;' /><th:block th:text="#{'rdo.no'}">否</th:block></label>
								<br>
								<label><input type="radio" id="youngCareResult4_2" name="youngCareResult4_2" value='Y' /><th:block th:text="#{'rdo.yes'}">是</th:block></label>&nbsp;
								<label><input type="radio" id="youngCareResult4_2" name="youngCareResult4_2" value='N' style='padding-left:30px;' /><th:block th:text="#{'rdo.no'}">否</th:block></label>
							</td>
						</tr>
						<tr>
							<td width="20%" align="center"><span class="text-red">＊</span><th:block th:text="#{'C101M01A.youngCareItem5'}">告知與揭露</th:block></td>
							<td width="60%"><th:block th:text="#{'C101M01A.youngCareContent5'}">已向客戶充分說明本次業務之重要內容及相關權利義務。</th:block></td>
							<td width="20%" align="center">
								<label><input type="radio" id="youngCareResult5" name="youngCareResult5" value='Y' /><th:block th:text="#{'rdo.yes'}">是</th:block></label>&nbsp;
								<label><input type="radio" id="youngCareResult5" name="youngCareResult5" value='N' style='padding-left:30px;' /><th:block th:text="#{'rdo.no'}">否</th:block></label>
							</td>
						</tr>
						<tr>
							<td colspan="3">
								<span class="text-red">＊</span><th:block th:text="#{'C101M01A.youngCareMemo'}">備註</th:block>:<br>
								<textarea id="youngCareMemo" name="youngCareMemo" style="width:60%;height:60px;" class="max" maxlength="300" ></textarea>
							</td>
						</tr>
					</table>
					</fieldset>
					
					<button type="button" id="btImportCust" name="btImportCust"><span class="text-only"><th:block th:text="#{'C101S01A.btImportCust'}">引進借保人資料</th:block></span></button>
					<button type="button" id="btImportWebBankApply" name="btImportWebBankApply"><span class="text-only"><th:block th:text="#{'C101S01A.btImportWebBankApply'}">引進線上增貸資料</th:block></span></button>
					<button type="button" id="btImportWebProd69" name="btImportWebProd69"><span class="text-only"><th:block th:text="#{'C101S01A.btImportWebProd69'}">引進線上勞工紓困資料</th:block></span></button>
					<button type="button" id="btImportWebPLOAN" name="btImportWebPLOAN"><span class="text-only"><th:block th:text="#{'C101S01A.btImportWebPLOAN'}">引進線上貸款資料</th:block></span></button>
					<table class="tb2" width="100%">
						<tr>
							<td width="18%" class="hd2" align="right" ><span class="text-red">＊</span>
								<!--<th:block th:text="#{'C101M01A.custId'}">身分證統編</th:block>-->
								<label id="creditShow"><th:block th:text="#{'C101M01A.custId'}">身分證統編</th:block></label>
							</td>
							<td width="32%" >
								<span id="custId" class="field"></span>&nbsp;
								<span id="dupNo" class="field" ></span>
								<span id="ownBrId" class="field" style="display:none;"></span>
							</td>
							<td width="18%" class="hd2" align="right" ><span class="text-red">＊</span><th:block th:text="#{'C101M01A.custName'}">借款人姓名</th:block>&nbsp;&nbsp;</td>
							<td width="32%" >
								<!--<span id="custName" class="field" ></span>-->
								<input type="text" id="custName" name="custName" maxlength="40" maxlengthC="40"  readonly="readonly"/>
								
								<a id="chgNameLink" href="#"  style="display:none;">(<th:block th:text="#{'C101M01A.chgName'}">更改借款人姓名</th:block>)</a>
								
							<div id="chgNameThickBox" style="display:none;">
					             <table class="tb2" width="100%">
						             <tr>
										<td class="hd2" align="right" ><th:block th:text="#{'C101M01A.chgName'}">更改借款人姓名</th:block>&nbsp;&nbsp;</td>
										<td><input type="text" id="chgCustName" name="chgCustName" class="required max" onblur="this.value = (this.value || '').toUpperCase();" maxlength="60" /></td>
									 </tr>
					             </table>							
						    </div>
								
								<!--<a class='chgCustName' style='display:none'>更改名稱</a>  -->
								<button type="button" id='btnImpCustName' name='btnImpCustName'><span class="text-only">重引</span></button>
								<span id='href_DifficultCharSoln'><u style="cursor:pointer;">說明</u></span>
							</td>
						</tr>
						<tr>
							<!-- <td width="18%" class="hd2" align="right" ><th:block th:text="#{'C101M01A.typCd'}">客戶型態(區部別)</th:block>&nbsp;&nbsp;</td> -->
							<!-- <td width="32%" ><input type="text" id="typCd" name="typCd" class="max" maxlength="1" /></td> -->
							<td width="18%" class="hd2" align="right" ><th:block th:text="#{'C101M01A.custNo'}">客戶編號</th:block>&nbsp;&nbsp;</td>
							<td width="32%" ><input type="text" id="custNo" name="custNo" class="max" maxlength="60" /></td>
							<td width="18%" class="hd2" align="right" ><th:block th:text="#{'C101M01A.staffNo'}">職工編號</th:block>&nbsp;&nbsp;</td>
							<td width="32%" ><input type="text" id="staffNo" name="staffNo" class="max" maxlength="20" /></td>
						</tr>
						<tr>
							<td width="18%" class="hd2" align="right" ><th:block th:text="#{'C101M01A.cmpId'}">負責事業體統一編號</th:block>&nbsp;&nbsp;</td>
							<td width="32%" ><input type="text" id="idxNo" name="idxNo" class="max" maxlength="10" /></td>
							<td width="18%" class="hd2" align="right" ><th:block th:text="#{'C101M01A.cmpNm'}">負責事業體名稱</th:block>&nbsp;&nbsp;</td>
							<td width="32%" ><input type="text" id="cmpNm" name="cmpNm" class="max" maxlength="50" /></td>
						</tr>
						<tr>
							<td class="hd2" align="right" ><span class="text-red">＊</span><th:block th:text="#{'C101S01A.ntCode'}">國別</th:block>&nbsp;</td>
							<td ><select id="ntCode" name="ntCode" class="required" codeType="CountryCode" itemStyle="format:{value}-{key}" ></select></td>
							<td class="hd2" align="right" ><th:block th:text="#{'C120S01A.newCustFlag'}">新往來客戶註記</th:block>&nbsp;</td>
							<td >
								<label><input type="radio" id="newCustFlag" name="newCustFlag" value="Y" disabled="true"/><th:block th:text="#{'C120S01A.newCustFlag.Y'}">是</th:block></label>
								<label><input type="radio" id="newCustFlag" name="newCustFlag" value="N" disabled="true"/><th:block th:text="#{'C120S01A.newCustFlag.N'}">否</th:block></label>
							</td>
						</tr>
					</table>
				</form>
			</div>
			<div id="noteItemThickBox" style="display:none;" >
				<p>
					<th:block th:text="#{'isSuspectedHeadAccount.note.b'}">訪談中請注意下列事項</th:block><br/>
					1.<th:block th:text="#{'isSuspectedHeadAccount.note.message01'}">借款人是否有對自己從事之行業或職業性質與內容不瞭解或毫無概念的情況</th:block><br/>
					2.<th:block th:text="#{'isSuspectedHeadAccount.note.message02'}">借款人於聯徵中心有被查詢紀錄，惟照會時卻不清楚申貸之銀行，且無法說明原因說明原因</th:block><br/>
					3.<th:block th:text="#{'isSuspectedHeadAccount.note.message03'}">由非親屬之第三人陪同申辦貸款、且代為回答詢問案件問題</th:block><br/>
					4.<th:block th:text="#{'isSuspectedHeadAccount.note.message04'}">借款人有支付銀行收取之開辦手續費以外之費用</th:block>
				</p>
			</div>
			<!--<script type="text/javascript" src="../pagejs/cls/CLS1151S02Panel.js"></script>-->
                </th:block>
    </body>
	
	
</html>
