package com.mega.eloan.lms.cls.service.impl;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.AbstractCapService;

import com.mega.eloan.common.gwclient.IVRGwClient;
import com.mega.eloan.lms.cls.service.CLS3301Service;
import com.mega.eloan.common.gwclient.IVRGwReqMessage;

@Service
public class CLS3301ServiceImpl extends AbstractCapService implements
CLS3301Service {
	@Resource
	private IVRGwClient ivrGwClient;
	
	public List<Map<String, Object>> find(IVRGwReqMessage req) {
		/*
			http://192.168.211.127/megabank/web/admin/midi_rec_edit.php?lightID=ISEhfDAwODAzNHxFTHwwODZBOTQzNDRBRjk2QTY2NTA2NzJFQThCMTJBODdBQXwxNTIwNDE5ODAwMDR3&&UserID=008034&RECORD_FILENAME=201906050090039684320
		 */

		//ivrGwClient.init();
		List<Map<String, Object>> list = ivrGwClient.send(req);
		
		return list;
	}
	
	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		return null;
	}
	
	@Override
	public void delete(GenericBean... entity) {
		for (GenericBean model : entity) {
			if (model != null) {

				}

		}
	}
	
	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {

		return null;
	}
	
	@Override
	public void save(GenericBean... entity) {
		for (GenericBean model : entity) {
			if (model != null) {
				
			}
		}

	}
	
	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {

		return null;
	}
}
