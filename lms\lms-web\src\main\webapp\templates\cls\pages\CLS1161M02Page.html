<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="innerPageBody">
        	<style>
				.hd3_c101s01q {
				    font-weight: bold;
				    background: #FFD6FF;
				    color: #000000;
					align: center;
					text-align:center;
				}
			</style>
        	<script type="text/javascript">loadScript('pagejs/cls/CLS1161M02Page');</script>
			
            <div class="button-menu funcContainer" id="buttonPanel">
                <button id="btnExit" class="forview">
                    <span class="ui-icon ui-icon-jcs-01"></span>
                    <th:block th:text="#{'button.exit'}">離開</th:block>
                </button>
            </div>
			<div class="tit2 color-black">
				<form id="baseForm" >
					<th:block th:text="#{'title.tab00'}">中鋼整批匯入</th:block>：
					(<span id="typCd" class="text-red" ></span>)
					<span id="baseCustId" class="field color-blue" ></span>&nbsp;<span id="baseDupNo" class="field color-blue" ></span>&nbsp;<span id="custInfo" class="field color-blue" ></span>					
				</form>
			</div>
            <div id="mainTab" class="tabs doc-tabs">
                <ul>
                	<li id="mainTab01" ><a href="#tab-01" goto="01"><b><th:block th:text="#{'title.tab01'}">文件資訊</th:block></b></a></li>
                <th:block th:if="${_for912View_visible}"><li id="mainTab02" ><a href="#tab-02" goto="02"><b><th:block th:text="#{'title.tab02'}">詳細資料</th:block></b></a></li>                   
                </th:block>
                </ul>
                <div class="tabCtx-warp">
                    <div th:id="${tabIdx}" th:insert="${panelName} :: ${panelFragmentName}"></div>
                </div>
            </div>
			
			<!-- 非房貸信用評分調整表 -->
			<div id="adjustNotHouseLoanSheet" ></div>
			
			<div id='divAdjustReasonCfmMsg' style='display:none'>		
				<div id="adjustReasonCfmMsg">
				</div>
			</div>
			<div id='divAdjustReasonAlwaysCfmMsg' style='display:none'>		
				<div id="adjustReasonAlwaysCfmMsg">
				</div>
			</div>
			
        	<form id="form_markModel_2" >     
				<div id="div_form_markModel_2"  style="display:none">				   			
				<fieldset id="fieldset_markModel_2">
					<legend>
		                 <b>非房貸信評</b>
		            </legend>
					<div id="gradeDiv_markModel_2" >
						<div id="href_NotHouseLoanNoticeItem"><span class="text-red">＊評等前<u style="cursor:pointer;">請先參閱注意事項(非房貸申請信用評等)</u></span></div>					
						
						<!-- 評等等級表 -->
						<table class="tb2" width="100%">
							<tr>
								<td width="12%" class="hd3_c101s01q" ><th:block th:text="#{'label.varVer'}">版本</th:block></td>
								<td width="6%" ><span id="varVer_markModel_2" ></span>&nbsp;</td>								
								<td width="15%" class="hd3_c101s01q" ><th:block th:text="#{'C101S01G.grade1'}">初始評等</th:block></td>
								<td width="12%" ><span id="grade1_markModel_2" class="field" ></span>&nbsp;</td>
								<td width="15%" class="hd3_c101s01q" ><th:block th:text="#{'C101S01G.grade2'}">調整評等</th:block></td>
								<td width="12%" >
									<a href="#" id="btAdjust_markModel_2" class="readOnly" style='color:black;' 
									onclick='openAdjust()'>
										<span id="grade2Status_markModel_2" class="field" ></span>
									</a>							
								</td>
								<td width="15%" class="hd3_c101s01q" ><th:block th:text="#{'C101S01G.grade3'}">最終評等</th:block></td>
								<td width="12%" ><span id="grade3_markModel_2" class="field" ></span>&nbsp;</td>
							</tr>
							<tr>
								<td colspan="8" class="hd3_c101s01q" ><th:block th:text="#{'C101S01G.alertMsg'}">票交所與聯徵特殊負面資訊</th:block></td>
							</tr>
							<tr>
								<td colspan="8" ><span id="alertMsg_markModel_2" class="field" ></span>&nbsp;</td>								
							</tr>
							<tr class='hs_jcicFlg hs_jcicFlg_VNN_'>
								<td colspan="8" class="text-red">＊該借保人無信用卡及授信紀錄，無足夠信用往來資訊可供模型評分，請加強對客戶其他面向之資信評估，謹慎審視風險。&nbsp;</td>								
							</tr>
							<tr class='hs_jcicFlg hs_jcicFlg_V_NN'>
								<td colspan="8" class="text-red">
								*請注意：該客戶暫無足夠信用往來資訊可供模型評分。<br/>
								信用往來資訊不足原因包含未持有信用卡，且無授信往來紀錄，請加強對客戶其他面向之資信評估，謹慎審視風險。
								<table border='0'>
									<tr style='vertical-align:top;'>
										<td nowrap class="text-red noborder">註1:</td>
										<td class="text-red noborder">
											是否持有信用卡、授信往來紀錄之資料來源為 <span class='date_jcicFlg_V_NN'></span> 之聯徵 KRS008(持有信用卡紀錄) 及 BAM095(授信額度,擔保品,金額,還款紀錄資訊) 查詢結果。
										</td>
									</tr>
									<tr>
										<td nowrap class="text-red noborder">註2:</td>
										<td class="text-red noborder">
											當客戶同時無聯徵KRS008及BAM095查詢結果時才會顯示此訊息。
										</td>
									</tr>
								</table>
								
								</td>								
							</tr>
						</table>
					</div>
				</fieldset>	
				</div>
        	</form>
		</th:block>
    </body>
</html>
