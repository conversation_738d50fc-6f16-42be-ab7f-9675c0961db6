package com.mega.eloan.lms.mfaloan.service.impl;

import java.io.File;
import java.io.IOException;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import tw.com.jcs.common.Util;

import jxl.Sheet;
import jxl.Workbook;
import jxl.read.biff.BiffException;
import jxl.write.Label;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import jxl.write.WriteException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;


import com.mega.eloan.lms.mfaloan.service.MisELF386Service;

@Service
public class MisELF386ServiceImpl extends AbstractMFAloanJdbc implements
		MisELF386Service {
	private static final Logger logger = LoggerFactory
	.getLogger(MisELF386ServiceImpl.class);
	@Override
	public List<Map<String, Object>> getELF386data(String UnitNo) {
		return getJdbc().queryForList("MIS.ELF386.SEARCH",
				new String[] { UnitNo }, 0, 2000);
	}

	@Override
	public List<Map<String, Object>> getELF500data(String custId, String dupId) {
		return getJdbc().queryForList("MIS.ELF500.SEARCH",
				new String[] { custId, dupId }, 0, 1500);
	}

	@Override
	public List<Map<String, Object>> getLNF010data(String custId) {
		return getJdbc().queryForList("MIS.LNF010.SEARCH",
				new String[] { custId }, 0, 1500);
	}

	@Override
	public void LNF010Insert(String custId, String YPAY, String OMONEY,
			String user) {
		getJdbc().update("MIS.LNF010.Insert",
				new String[] { custId, YPAY, OMONEY, user });
	}

	@Override
	public void LNF010Update(String custId, String YPAY, String OMONEY,
			String user) {
		getJdbc().update("MIS.LNF010.UpdatebycustId",
				new String[] { YPAY, OMONEY, user, custId });
	}

	@Override
	public void LNF010UpdateSingle(String custId, String YPAY, String OMONEY,
			String JobClass, String user) {
		getJdbc().update("MIS.LNF010.UpdateSingle",
				new String[] { YPAY, OMONEY, JobClass, user, custId });
	}

	@Override
	public void LNF010InsertSingle(String custId, String YPAY, String OMONEY,
			String JobClass, String user) {
		getJdbc().update("MIS.LNF010.InsertSingle",
				new String[] { custId, YPAY, OMONEY, JobClass, user });
	}

	@Override
	public void ELF500Update(String custId, String YPAY, String OMONEY) {
		String tid = (String) custId.subSequence(0, 10);
		getJdbc().update("MIS.ELF500.UpdateCustData",
				new String[] { YPAY, OMONEY, tid });
	}

	public void updateSingle(String tId, String tDup, String yPay,
			String oMoney, String JobClass, String user) {
		String custId = tId + tDup;
		if (checkELF500(custId)) {
			if (checkLNF010(custId)) {
				LNF010InsertSingle(custId, yPay, oMoney, JobClass, user);
			} else {
				LNF010UpdateSingle(custId, yPay, oMoney, JobClass, user);
			}
		} else {
			ELF500Update(custId, yPay, oMoney);
			if (checkLNF010(custId)) {
				LNF010InsertSingle(custId, yPay, oMoney, JobClass, user);
			} else {
				LNF010UpdateSingle(custId, yPay, oMoney, JobClass, user);
			}
		}
	}
	
	public void updateExcel(String file, String user,String[] info) {
		try {
			String succes="succes";
			String error="error";
			Workbook workbook = Workbook.getWorkbook(new File(file));
			Sheet sheet = workbook.getSheet(0);
			Map<String, Object> mapTYPE = new HashMap<String, Object>();
			for (int i = 1; i < sheet.getRows(); i++) {
				String custId = sheet.getCell(1, i).getContents();
				String YPAY = sheet.getCell(5, i).getContents();
				String OMONEY = sheet.getCell(6, i).getContents();
				double YpayInt = Util.parseDouble(YPAY);
				double OmoneyInt = Util.parseDouble(OMONEY);
				if (YpayInt <= 0 && OmoneyInt <= 0) {
					mapTYPE.put(succes + i, " ");
					mapTYPE.put(error + i, info[2]);
				} else if (YpayInt > 99999 || OmoneyInt>99999) {
					mapTYPE.put(succes + i, " ");
					mapTYPE.put(error + i, info[3]);
				} else {
					if (checkELF500(custId)) {
						if (checkLNF010(custId)) {
							LNF010Insert(custId, YPAY, OMONEY, user);
							mapTYPE.put(succes + i, info[0]);
							mapTYPE.put(error + i, " ");
						} else {
							LNF010Update(custId, YPAY, OMONEY, user);
							mapTYPE.put(succes + i, info[1]);
							mapTYPE.put(error + i, " ");
						}
					} else {
						ELF500Update(custId, YPAY, OMONEY);
						if (checkLNF010(custId)) {
							LNF010Insert(custId, YPAY, OMONEY, user);
							mapTYPE.put(succes + i, info[0]);
							mapTYPE.put(error + i, " ");
						} else {
							LNF010Update(custId, YPAY, OMONEY, user);
							mapTYPE.put(succes + i, info[1]);
							mapTYPE.put(error + i, " ");
						}
					}
				}
			}
			tranSportExcel(mapTYPE, file, file);
		} catch (BiffException e) {
			logger.error("updateExcel BiffException Error",e);
		} catch (IOException e) {
			logger.error("updateExcel IOException Error",e);
		}
	}

	// 將資料輸出到檔案
	@Override
	public void tranSportExcel(Map<String, Object> list, String file,
			String save) {
		try {
			Workbook a;
			a = Workbook.getWorkbook(new File(file));
			WritableWorkbook test = Workbook.createWorkbook(new File(save), a);
			WritableSheet sheet = test.getSheet(0);
			for (int i = 1; i < list.size(); i++) {
				if (list.get("succes" + i) != null) {
					Label label = new Label(8, i, list.get("succes" + i)
							.toString());
					sheet.addCell(label);
				}
				if (list.get("error" + i) != null) {
					Label labelCNAME = new Label(9, i, list.get("error" + i)
							.toString());
					sheet.addCell(labelCNAME);
				}
			}
			test.write();
			test.close();
		} catch (BiffException e) {
			logger.error("updateExcel BiffException Error",e);
		} catch (IOException e) {
			logger.error("updateExcel IOException Error",e);
		} catch (WriteException e) {
			logger.error("updateExcel WriteException Error",e);
		}
	}

	// 判斷ELF500內是否無資料
	public boolean checkELF500(String custId) {
		String tid = (String) custId.subSequence(0, 10);
		String tdup = custId.substring(10);
		if (getELF500data(tid, tdup).isEmpty()) {
			return true;// 無資料
		} else {
			return false;// 有資料
		}

	}

	// 判斷LN.LNF010內是否無資料
	public boolean checkLNF010(String custId) {
		if (getLNF010data(custId).isEmpty()) {
			return true;// 無資料
		} else {
			return false;// 有資料
		}
	}
}
