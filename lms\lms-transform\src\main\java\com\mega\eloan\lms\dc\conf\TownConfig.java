/* 
 * TownConfig.java
 *
 * IBM Confidential
 * GBS Source Materials
 * 
 * Copyright (c) 2013 IBM Corp. 
 * All Rights Reserved.
 */
package com.mega.eloan.lms.dc.conf;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.configuration.Configuration;
import org.apache.commons.configuration.PropertiesConfiguration;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.mega.eloan.lms.dc.base.DCException;

/**
 * <pre>
 * TownConfig :鄉鎮代碼對照
 * </pre>
 * 
 * @since 2013/3/12
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/3/12,Bang,new
 *          </ul>
 */
public class TownConfig {

	private static Logger logger = LoggerFactory.getLogger(TownConfig.class);

	private static final String CONFIG_FILE = "lmsdc/conf/town.properties";

	private static Map<String, String> mapNameVal = new LinkedHashMap<String, String>();
	private static Map<String, String> mapCodeVal = new LinkedHashMap<String, String>();

	private static TownConfig config = new TownConfig();

	public static TownConfig getInstance() {
		return config;
	}

	private TownConfig() {
		try {
			this.load();
		} catch (Exception ex) {
			throw new DCException("讀取town.properties設定檔錯誤！", ex);
		}
	}

	private void load() throws Exception {
		Configuration conf = new PropertiesConfiguration(CONFIG_FILE);

		Iterator<String> itor = conf.getKeys();
		String key;
		String value;
		while (itor.hasNext()) {
			key = StringUtils.trimToEmpty(itor.next());
			value = StringUtils.trimToEmpty(conf.getString(key));
			mapNameVal.put(key, value);
			mapCodeVal.put(value, key);

			if (logger.isDebugEnabled()) {
				logger.debug("key=" + key + ",value=" + value);
			}
		}
	}

	/**
	 * 依鄉鎮名取得對應代碼
	 * 
	 * @param ctName
	 * @return
	 */
	public String getTownCode(String twName) {
		return mapCodeVal.get(twName);
	}

	/**
	 * 依代碼取得對應鄉鎮名
	 * 
	 * @param ctCode
	 * @return
	 */
	public String getTownName(String twCode) {
		return mapNameVal.get(twCode);
	}

	/**
	 * 取得所有鄉鎮代碼
	 * 
	 * @return List
	 */
	public List<String> getTownCodeList() {
		List<String> keyList = new ArrayList<String>(mapCodeVal.values());
		return keyList;
	}

	@Override
	public String toString() {
		StringBuffer str = new StringBuffer();
		for (Map.Entry<String, String> entry : mapNameVal.entrySet()) {
			str.append(entry.getKey()).append("=").append(entry.getValue())
					.append("\n");
		}
		return str.toString();
	}
}
