package tw.com.jcs.auth;

import java.util.List;

import tw.com.jcs.auth.model.Branch;

/**
 * <pre>
 * 單位相關資訊(已棄用)
 * </pre>
 * 
 * @since 2022年12月21日
 * <AUTHOR> @version
 *          <ul>
 *          <li>2022年12月21日
 *          </ul>
 */
@Deprecated
public interface BranchService {

    /**
     * 取得單位資訊
     * 
     * @param branchId
     *            分行代號
     * @return
     */
    Branch getBranch(String branchId);

    /**
     * 取得單位名稱
     * 
     * @param branchId
     *            分行代號
     * @return
     */
    String getBranchName(String branchId);

    /**
     * 取得全部單位
     * 
     * @return
     */
    List<Branch> getAllBranch();

    /**
     * 依單位類別取得隸屬分行清單
     * 
     * @param unitType
     *            單位類別
     * @return
     */
    List<Branch> getBranchByUnitType(String... unitType);

    /**
     * 依１國內２國外３總處４子銀行 取得隸屬分行清單
     * 
     * @param flag
     * @return
     */
    List<Branch> getBranchByFlag(String... flag);

    /**
     * 依區域中心取得隸屬分行清單
     * 
     * @param area
     *            區域中心
     * @return
     */
    List<Branch> getBranchByArea(String... area);

    /**
     * 依區域中心代碼取得隸屬分行清單
     * 
     * @param groupId
     *            區域中心代碼
     * @return
     */
    List<Branch> getBranchOfGroup(String... groupId);

    /**
     * 依母行代碼取得隸屬子行清單
     * 
     * @param parentId
     *            母行代碼
     * @return
     */
    List<Branch> getBranchOfParent(String... parentId);
}
