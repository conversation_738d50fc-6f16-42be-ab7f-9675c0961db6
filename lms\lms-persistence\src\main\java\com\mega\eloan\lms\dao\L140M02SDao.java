/* 
 * L140M02SDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140M02S;

/** 應收帳款買方額度資訊主檔 **/
public interface L140M02SDao extends IGenericDao<L140M02S> {

	L140M02S findByOid(String oid);

	List<L140M02S> findByMainId(String mainId);

	L140M02S findByUniqueKey(String mainId, String type, Integer itemSeq);

	List<L140M02S> findByIndex01(String mainId, String type, Integer itemSeq);

	List<L140M02S> findByMainIdType(String mainId, String type);

	List<L140M02S> findByMainIdTypeCustId(String mainId, String type,
			String custId, String dupNo);
	
	public L140M02S findByMainIdTypeCustIdCntrNo(String mainId, String type,
			String custId, String dupNo,String cntrNo);
}