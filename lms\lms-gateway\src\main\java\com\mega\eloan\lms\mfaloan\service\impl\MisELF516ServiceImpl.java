/* 
 * MisELF500ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.mfaloan.bean.ELF516;
import com.mega.eloan.lms.mfaloan.service.MisELF516Service;

/**
 * <pre>
 * 可疑代辦案件註記檔 MIS.ELF516
 */
@Service
public class MisELF516ServiceImpl extends AbstractMFAloanJdbc implements
		MisELF516Service {

	@Override
	public List<ELF516> findByCustId(String custId, String dupNo) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForList(
				"MIS.ELF516_findByCustId", new Object[] { custId, dupNo }, "ELF516_");

		List<ELF516> list = new ArrayList<ELF516>();
		for (Map<String, Object> row : rowData) {
			ELF516 model = new ELF516();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}
	
	@Override
	public List<ELF516> findByCustIdBrNoYYYYMM(String brNo, String custId, String dupNo, String yyyyMM_beg, String yyyyMM_end){
		List<Map<String, Object>> rowData = this.getJdbc().queryForList(
				"MIS.ELF516_findByCustIdBrnoYyyymm", new Object[] { custId, dupNo, brNo+"%", yyyyMM_beg, yyyyMM_end }, "ELF516_");

		List<ELF516> list = new ArrayList<ELF516>();
		for (Map<String, Object> row : rowData) {
			ELF516 model = new ELF516();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
		
	}
	
	@Override
	public ELF516 findByCustIdAndCntrnoAndYYYYMM(String custId, String dupNo, String cntrNo, String yyyyMM) {
		Map<String, Object> rowData = this.getJdbc().queryForMap(
		"MIS.ELF516_findByCustIdCntrnoYyyymm",
		new Object[] { custId, dupNo, cntrNo, yyyyMM });
		
		if (rowData == null) {
			return null;
		} else {
			ELF516 model = new ELF516();
			DataParse.map2Bean(rowData, model);
			return model;
		}
	}
	
	@Override
	public void updateC250M01A(String LNFLAG, String OTHERMEMO, String loanNo,
			String updater, String approver, String branchComm, String otherDesc, String yyyyMM, String cntrNo) {
		this.getJdbc().update(
				"MIS.ELF516_updateC250M01AData",
				new Object[] { LNFLAG, OTHERMEMO, loanNo, updater, approver, branchComm, otherDesc, yyyyMM, cntrNo});
	}
	
	@Override
	public List<ELF516> findForCLS180R18(String brno_area, String yyyyMM_beg, String yyyyMM_end, String lngeFlag){
		List<Map<String, Object>> rowData = this.getJdbc().queryForList(
				"MIS.ELF516_ForCLS180R18", new Object[] { yyyyMM_beg, yyyyMM_end, brno_area, lngeFlag }, "ELF516_");

		List<ELF516> list = new ArrayList<ELF516>();
		for (Map<String, Object> row : rowData) {
			ELF516 model = new ELF516();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}
}
