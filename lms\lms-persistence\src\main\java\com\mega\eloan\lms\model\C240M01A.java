/* 
 * C240M01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.util.Date;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import org.apache.commons.lang3.builder.ToStringExclude;

import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 覆審工作底稿主檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "C240M01A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class C240M01A extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;
	/**
	 * JOIN條件
	 * 
	 */
	@ToStringExclude
	@OneToMany(mappedBy = "c240m01a", fetch = FetchType.LAZY)
	private Set<C240A01A> c240a01a;

	public Set<C240A01A> getC240a01a() {
		return c240a01a;
	}

	public void setC240a01a(Set<C240A01A> c240a01a) {
		this.c240a01a = c240a01a;
	}

	/** 分行 **/
	@Column(name = "BRANCHID", length = 3, columnDefinition = "CHAR(3)")
	private String branchId;

	/** 匯率日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "AGNT_DT", columnDefinition = "DATE")
	private Date agnt_dt;

	/** 本位幣轉台幣匯率 **/
	@Column(name = "AGNT_TWD_RT", columnDefinition = "DECIMAL(12, 8)")
	private Double agnt_twd_rt;

	/**
	 * 轉本位幣匯率
	 * <p/>
	 * 記錄折算當時各幣別匯率備查<br/>
	 * USD:9999.99999999;<br/>
	 * EUR:9999.99999999;<br/>
	 * …
	 */
	@Column(name = "AGNT_LOC_RT", length = 512, columnDefinition = "VARCHAR(512)")
	private String agnt_loc_rt;

	/**
	 * 覆審批號
	 * <p/>
	 * ※預設值「null」，於執行「重新產生批號」時與「明細檔_覆審序號」一起給號，依「資料年月(dataDate)的年度」與「分行(branchId)
	 * 」<br/>
	 * max(batchNO)+1<br/>
	 * 顯示格式：001
	 */
	@Column(name = "BATCHNO", columnDefinition = "DECIMAL(3,0)")
	private Integer batchNO;

	/**
	 * 本次覆審資料截至
	 * <p/>
	 * 為產生“資料年月”
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "DATAENDDATE", columnDefinition = "DATE")
	private Date dataEndDate;

	/**
	 * 本次預計覆審日期
	 * <p/>
	 * 預設為空，可修改
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "EXPECTEDRETRIALDATE", columnDefinition = "DATE")
	private Date expectedRetrialDate;

	/** 本次覆審共計 筆需覆審 **/
	@Column(name = "REQUANTITY", columnDefinition = "DECIMAL(3,0)")
	private Integer reQuantity;

	/**
	 * 覆審類別8-1年度
	 * <p/>
	 * YYYY 系統年份
	 */
	@Column(name = "YEAROFREVIEW", length = 4, columnDefinition = "VARCHAR(4)")
	private String yearOfReview;

	/**
	 * 覆審類別8-1總筆數
	 * <p/>
	 * ELF491_REMOMO左邊13個字元
	 */
	@Column(name = "PLOTSOFREVIEW", columnDefinition = "DECIMAL(3,0)")
	private Integer plotsOfReview;

	/**
	 * 覆審類別8-1已抽樣筆數
	 * <p/>
	 * ELF491_REMOMO右邊13個字元
	 */
	@Column(name = "SAMPLINGCOUNT", columnDefinition = "DECIMAL(3,0)")
	private Integer samplingCount;

	/**
	 * 覆審類別8-1抽樣比率
	 * <p/>
	 * %
	 */
	@Column(name = "SAMPLINGRATE", columnDefinition = "DECIMAL(3,0)")
	private Integer samplingRate;

	/**
	 * 現有效戶數
	 * <p/>
	 * ELF489各分行，有8-1的個數
	 */
	@Column(name = "EFFECTIVECOUNT", columnDefinition = "DECIMAL(6,0)")
	private Integer effectiveCount;

	/**
	 * 本次欲抽樣比率
	 * <p/>
	 * % 使用者輸入
	 */
	@Column(name = "THISSAMPLINGRATE", columnDefinition = "DECIMAL(3,0)")
	private Integer thisSamplingRate;

	/** 本次抽樣戶數 **/
	@Column(name = "THISSAMPLINGCOUNT", columnDefinition = "DECIMAL(3,0)")
	private Integer thisSamplingCount;

	/** 覆審工作底稿EXCEL檔資料日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "EXCELDATE", columnDefinition = "DATE")
	private Date excelDate;

	/** 覆審工作底稿EXCEL檔檔案位置 **/
	@Column(name = "EXCELFILE", length = 64, columnDefinition = "VARCHAR(64)")
	private String excelFile;

	/** 預定覆審名單EXCEL檔資料日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "BRANCHDATE", columnDefinition = "DATE")
	private Date branchDate;

	/** 預定覆審名單EXCEL檔檔案位置 **/
	@Column(name = "BRANCHFILE", length = 64, columnDefinition = "VARCHAR(64)")
	private String branchFile;

	/** 覆審名單驗證EXCEL檔資料日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "CHECKDATE", columnDefinition = "DATE")
	private Date checkDate;

	/** 覆審名單驗證EXCEL檔檔案位置 **/
	@Column(name = "CHKEXCELFILE", length = 64, columnDefinition = "VARCHAR(64)")
	private String chkExcelFile;

	/** 覆審主管 **/
	@Column(name = "REMANAGERID", length = 6, columnDefinition = "CHAR(6)")
	private String reManagerId;

	/** 覆審主管核准時間 **/
	@Column(name = "RECHECKTIME", columnDefinition = "TIMESTAMP")
	private Date reCheckTime;

	/** 覆審人員 **/
	@Column(name = "HQAPPRAISERID", length = 6, columnDefinition = "CHAR(6)")
	private String hqAppraiserId;

	/**
	 * RPTID
	 * <p/>
	 * 電子表單列印套版版本ID
	 */
	@Column(name = "RPTID", length = 32, columnDefinition = "VARCHAR(32)")
	private String rptId;

	/** RPA執行日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "RPADATE", columnDefinition = "DATE")
	private Date rpaDate;

	/**
	 * 處理狀態
	 * <p/>
	 * A01:查詢中<br/>
	 * A02:查詢完成<br/>
	 * A03:查詢失敗
	 */
	@Size(max = 3)
	@Column(name = "STATUS", length = 3, columnDefinition = "CHAR(3)")
	private String status;

	/** 取得分行 **/
	public String getBranchId() {
		return this.branchId;
	}

	/** 設定分行 **/
	public void setBranchId(String value) {
		this.branchId = value;
	}

	/** 取得匯率日期 **/
	public Date getAgnt_dt() {
		return this.agnt_dt;
	}

	/** 設定匯率日期 **/
	public void setAgnt_dt(Date value) {
		this.agnt_dt = value;
	}

	/** 取得本位幣轉台幣匯率 **/
	public Double getAgnt_twd_rt() {
		return this.agnt_twd_rt;
	}

	/** 設定本位幣轉台幣匯率 **/
	public void setAgnt_twd_rt(Double value) {
		this.agnt_twd_rt = value;
	}

	/**
	 * 取得轉本位幣匯率
	 * <p/>
	 * 記錄折算當時各幣別匯率備查<br/>
	 * USD:9999.99999999;<br/>
	 * EUR:9999.99999999;<br/>
	 * …
	 */
	public String getAgnt_loc_rt() {
		return this.agnt_loc_rt;
	}

	/**
	 * 設定轉本位幣匯率
	 * <p/>
	 * 記錄折算當時各幣別匯率備查<br/>
	 * USD:9999.99999999;<br/>
	 * EUR:9999.99999999;<br/>
	 * …
	 **/
	public void setAgnt_loc_rt(String value) {
		this.agnt_loc_rt = value;
	}

	/**
	 * 取得覆審批號
	 * <p/>
	 * ※預設值「null」，於執行「重新產生批號」時與「明細檔_覆審序號」一起給號，依「資料年月(dataDate)的年度」與「分行(branchId)
	 * 」<br/>
	 * max(batchNO)+1<br/>
	 * 顯示格式：001
	 */
	public Integer getBatchNO() {
		return this.batchNO;
	}

	/**
	 * 設定覆審批號
	 * <p/>
	 * ※預設值「null」，於執行「重新產生批號」時與「明細檔_覆審序號」一起給號，依「資料年月(dataDate)的年度」與「分行(branchId)
	 * 」<br/>
	 * max(batchNO)+1<br/>
	 * 顯示格式：001
	 **/
	public void setBatchNO(Integer value) {
		this.batchNO = value;
	}

	/**
	 * 取得本次覆審資料截至
	 * <p/>
	 * 為產生“資料年月”
	 */
	public Date getDataEndDate() {
		return this.dataEndDate;
	}

	/**
	 * 設定本次覆審資料截至
	 * <p/>
	 * 為產生“資料年月”
	 **/
	public void setDataEndDate(Date value) {
		this.dataEndDate = value;
	}

	/**
	 * 取得本次預計覆審日期
	 * <p/>
	 * 預設為空，可修改
	 */
	public Date getExpectedRetrialDate() {
		return this.expectedRetrialDate;
	}

	/**
	 * 設定本次預計覆審日期
	 * <p/>
	 * 預設為空，可修改
	 **/
	public void setExpectedRetrialDate(Date value) {
		this.expectedRetrialDate = value;
	}

	/** 取得本次覆審共計 筆需覆審 **/
	public Integer getReQuantity() {
		return this.reQuantity;
	}

	/** 設定本次覆審共計 筆需覆審 **/
	public void setReQuantity(Integer value) {
		this.reQuantity = value;
	}

	/**
	 * 取得覆審類別8-1年度
	 * <p/>
	 * YYYY 系統年份
	 */
	public String getYearOfReview() {
		return this.yearOfReview;
	}

	/**
	 * 設定覆審類別8-1年度
	 * <p/>
	 * YYYY 系統年份
	 **/
	public void setYearOfReview(String value) {
		this.yearOfReview = value;
	}

	/**
	 * 取得覆審類別8-1總筆數
	 * <p/>
	 * ELF491_REMOMO左邊13個字元
	 */
	public Integer getPlotsOfReview() {
		return this.plotsOfReview;
	}

	/**
	 * 設定覆審類別8-1總筆數
	 * <p/>
	 * ELF491_REMOMO左邊13個字元
	 **/
	public void setPlotsOfReview(Integer value) {
		this.plotsOfReview = value;
	}

	/**
	 * 取得覆審類別8-1已抽樣筆數
	 * <p/>
	 * ELF491_REMOMO右邊13個字元
	 */
	public Integer getSamplingCount() {
		return this.samplingCount;
	}

	/**
	 * 設定覆審類別8-1已抽樣筆數
	 * <p/>
	 * ELF491_REMOMO右邊13個字元
	 **/
	public void setSamplingCount(Integer value) {
		this.samplingCount = value;
	}

	/**
	 * 取得覆審類別8-1抽樣比率
	 * <p/>
	 * %
	 */
	public Integer getSamplingRate() {
		return this.samplingRate;
	}

	/**
	 * 設定覆審類別8-1抽樣比率
	 * <p/>
	 * %
	 **/
	public void setSamplingRate(Integer value) {
		this.samplingRate = value;
	}

	/**
	 * 取得現有效戶數
	 * <p/>
	 * ELF489各分行，有8-1的個數
	 */
	public Integer getEffectiveCount() {
		return this.effectiveCount;
	}

	/**
	 * 設定現有效戶數
	 * <p/>
	 * ELF489各分行，有8-1的個數
	 **/
	public void setEffectiveCount(Integer value) {
		this.effectiveCount = value;
	}

	/**
	 * 取得本次欲抽樣比率
	 * <p/>
	 * % 使用者輸入
	 */
	public Integer getThisSamplingRate() {
		return this.thisSamplingRate;
	}

	/**
	 * 設定本次欲抽樣比率
	 * <p/>
	 * % 使用者輸入
	 **/
	public void setThisSamplingRate(Integer value) {
		this.thisSamplingRate = value;
	}

	/** 取得本次抽樣戶數 **/
	public Integer getThisSamplingCount() {
		return this.thisSamplingCount;
	}

	/** 設定本次抽樣戶數 **/
	public void setThisSamplingCount(Integer value) {
		this.thisSamplingCount = value;
	}

	/** 取得覆審工作底稿EXCEL檔資料日期 **/
	public Date getExcelDate() {
		return this.excelDate;
	}

	/** 設定覆審工作底稿EXCEL檔資料日期 **/
	public void setExcelDate(Date value) {
		this.excelDate = value;
	}

	/** 取得覆審工作底稿EXCEL檔檔案位置 **/
	public String getExcelFile() {
		return this.excelFile;
	}

	/** 設定覆審工作底稿EXCEL檔檔案位置 **/
	public void setExcelFile(String value) {
		this.excelFile = value;
	}

	/** 取得預定覆審名單EXCEL檔資料日期 **/
	public Date getBranchDate() {
		return this.branchDate;
	}

	/** 設定預定覆審名單EXCEL檔資料日期 **/
	public void setBranchDate(Date value) {
		this.branchDate = value;
	}

	/** 取得預定覆審名單EXCEL檔檔案位置 **/
	public String getBranchFile() {
		return this.branchFile;
	}

	/** 設定預定覆審名單EXCEL檔檔案位置 **/
	public void setBranchFile(String value) {
		this.branchFile = value;
	}

	/** 取得覆審名單驗證EXCEL檔資料日期 **/
	public Date getCheckDate() {
		return this.checkDate;
	}

	/** 設定覆審名單驗證EXCEL檔資料日期 **/
	public void setCheckDate(Date value) {
		this.checkDate = value;
	}

	/** 取得覆審名單驗證EXCEL檔檔案位置 **/
	public String getChkExcelFile() {
		return this.chkExcelFile;
	}

	/** 設定覆審名單驗證EXCEL檔檔案位置 **/
	public void setChkExcelFile(String value) {
		this.chkExcelFile = value;
	}

	/** 取得覆審主管 **/
	public String getReManagerId() {
		return this.reManagerId;
	}

	/** 設定覆審主管 **/
	public void setReManagerId(String value) {
		this.reManagerId = value;
	}

	/** 取得覆審主管核准時間 **/
	public Date getReCheckTime() {
		return this.reCheckTime;
	}

	/** 設定覆審主管核准時間 **/
	public void setReCheckTime(Date value) {
		this.reCheckTime = value;
	}

	/** 取得覆審人員 **/
	public String getHqAppraiserId() {
		return this.hqAppraiserId;
	}

	/** 設定覆審人員 **/
	public void setHqAppraiserId(String value) {
		this.hqAppraiserId = value;
	}

	/**
	 * 取得RPTID
	 * <p/>
	 * 電子表單列印套版版本ID
	 */
	public String getRptId() {
		return this.rptId;
	}

	/**
	 * 設定RPTID
	 * <p/>
	 * 電子表單列印套版版本ID
	 **/
	public void setRptId(String value) {
		this.rptId = value;
	}

	/**
	 * 設定RPADATE
	 * <p/>
	 * RPA執行日期
	 **/
	public void setRpaDate(Date rpaDate) {
		this.rpaDate = rpaDate;
	}

	/**
	 * 取得RPADATE
	 * <p/>
	 * RPA執行日期
	 */
	public Date getRpaDate() {
		return rpaDate;
	}

	/**
	 * 取得處理狀態
	 * <p/>
	 * A01:查詢中<br/>
	 * A02:查詢完成<br/>
	 * A03:查詢失敗
	 */
	public String getStatus() {
		return this.status;
	}

	/**
	 * 設定處理狀態
	 * <p/>
	 * A01:查詢中<br/>
	 * A02:查詢完成<br/>
	 * A03:查詢失敗
	 **/
	public void setStatus(String value) {
		this.status = value;
	}

}
