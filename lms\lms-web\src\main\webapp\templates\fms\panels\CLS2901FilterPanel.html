<?xml version="1.0" encoding="UTF-8"?>
 <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:wicket="http://wicket.apache.org/">
    <body>
        <wicket:panel>
            <div id="filterBox" style="display:none">
                <form id="filterForm">
                    <div class="" id="">
                        <table class="tb2" border="0" cellpadding="0" cellspacing="0" width="100%">
                            <tbody>
								<tr>
                                    <td style="text-align: right;width:30%;" class="hd1">
                                        <wicket:message key="C900M01J.custId">被通報統一編號</wicket:message>&nbsp;&nbsp;
                                    </td>
                                    <td>
                                    	<input type="text" id="custId" name="custId" class="" maxlength="10" size="10" />
                                    </td>
                                </tr>    
                                <tr>
                                    <td style="text-align: right;width:30%;" class="hd1">
                                        <wicket:message key="C900M01J.category">被通報類別</wicket:message>&nbsp;&nbsp;
                                    </td>
                                    <td>
										<label><input type="radio" id="category" name="category" value="" /><wicket:message key="C900M01J.category.ALL">全部</wicket:message></label>
										<div style='margin-left:36px;'>
										<label><input type="radio" id="category" name="category" value="P" /><wicket:message key="C900M01J.category.P">疑似代辦案件</wicket:message></label> <br/>
										<label><input type="radio" id="category" name="category" value="B" /><wicket:message key="C900M01J.category.B">疑似人頭戶案件</wicket:message></label> <br/> 
										<label><input type="radio" id="category" name="category" value="I" /><wicket:message key="C900M01J.category.I">主管機關通報</wicket:message></label> 
										
										</div>
                                    </td>
                                </tr>
								                                
                            </tbody>
                        </table>
                    </div>
                </form>
            </div>
            <script>
                var FilterAction = {
                    formId: "#filterForm",
                    gridId: "#gridview",
                    openBox: function(){
                        var $form = $(this.formId).reset();
                        if(true){
							$form.find("[name=category]").attr('checked', false).filter("[value='']").trigger('click').attr("checked", true);
						}
						
                        $("#filterBox").thickbox({
                            //query=查詢
                            title: i18n.def["query"],
                            width: 550,
                            height: 320,
                            modal: true,
                            i18n: i18n.def,
                            readOnly: false,
                            align: "center",
                            valign: "bottom",
                            buttons: {
                                "sure": function(){
                                    if (!$form.valid()) {
                                        return false;
                                    }
                                    FilterAction.reloadGrid(JSON.stringify($form.serializeData()));
                                    $.thickbox.close();
                                },
                                "cancel": function(){
                                    $.thickbox.close();
                                }
                            }
                        });
                    },
                    /**更新grid
                     *
                     * @param {Object} data 查詢條件
                     */
                    reloadGrid: function(data){
                        $(this.gridId).jqGrid("setGridParam", {
                            postData: {
                                formAction: "queryMain",
                                filetData: data
                            },
                            page: 1,
                            search: true
                        }).trigger("reloadGrid");
                    }
                };
            </script>
        </wicket:panel>
    </body>
</html>
