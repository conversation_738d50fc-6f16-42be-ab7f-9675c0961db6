/* 
 * L830M01B.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 帳戶管理員維護明細 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L830M01B", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L830M01B extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * not null
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * 編製單位代號<p/>
	 * 評等單位/編製單位
	 */
	@Size(max=3)
	@Column(name="OWNBRID", length=3, columnDefinition="CHAR(3)")
	private String ownBrId;

	/** 
	 * 目前文件狀態<p/>
	 * 編製中=01O<br/>
	 *  待覆核=02O<br/>
	 *  已核准=05O
	 */
	@Size(max=3)
	@Column(name="DOCSTATUS", length=3, columnDefinition="CHAR(3)")
	private String docStatus;

	/** 
	 * 文件URL<p/>
	 * CLS:國內個金<br/>
	 *  LMS:國內企金<br/>
	 *  NULL:海外案件
	 */
	@Size(max=40)
	@Column(name="DOCURL", length=40, columnDefinition="VARCHAR(40)")
	private String docURL;

	/** 
	 * 維護項目<p/>
	 * 單筆維護=S<br/>
	 *  批次維護=B
	 */
	@Size(max=1)
	@Column(name="MAINTAINTYPE", length=1, columnDefinition="CHAR(1)")
	private String maintainType;

	/** 
	 * 帳戶管理員行編(篩選條件)<p/>
	 * 帳戶管理員行編
	 */
	@Size(max=20)
	@Column(name="FILTERAOID", length=20, columnDefinition="VARCHAR(20)")
	private String filterAOid;

	/** 
	 * 統一編號(篩選條件)<p/>
	 * 統一編號
	 */
	@Size(max=10)
	@Column(name="FILTERCUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String filterCustId;

	/** 
	 * 重複序號(篩選條件)<p/>
	 * 重複序號
	 */
	@Size(max=1)
	@Column(name="FILTERDUPNO", length=1, columnDefinition="CHAR(1)")
	private String filterDupNo;

	/** 
	 * 客戶名稱(篩選條件)<p/>
	 * 客戶名稱
	 */
	@Size(max=120)
	@Column(name="FILTERCUSTNAME", length=120, columnDefinition="VARCHAR(120)")
	private String filterCustName;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 核准人員號碼 **/
	@Size(max=6)
	@Column(name="APPROVER", length=6, columnDefinition="CHAR(6)")
	private String approver;

	/** 核准日期 **/
	@Column(name="APPROVETIME", columnDefinition="TIMESTAMP")
	private Timestamp approveTime;

	/** 邏輯刪除日期 **/
	@Column(name="DELETEDTIME", columnDefinition="TIMESTAMP")
	private Timestamp deletedTime;

	/** 原帳戶管理員 **/
	@Size(max=6)
	@Column(name="ORIGAOID", length=6, columnDefinition="CHAR(6)")
	private String origAOid;

	/** 新帳戶管理員 **/
	@Size(max=6)
	@Column(name="NEWAOID", length=6, columnDefinition="CHAR(6)")
	private String newAOid;

	/** 
	 * 取得oid<p/>
	 * not null
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  not null
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得編製單位代號<p/>
	 * 評等單位/編製單位
	 */
	public String getOwnBrId() {
		return this.ownBrId;
	}
	/**
	 *  設定編製單位代號<p/>
	 *  評等單位/編製單位
	 **/
	public void setOwnBrId(String value) {
		this.ownBrId = value;
	}

	/** 
	 * 取得目前文件狀態<p/>
	 * 編製中=01O<br/>
	 *  待覆核=02O<br/>
	 *  已核准=05O
	 */
	public String getDocStatus() {
		return this.docStatus;
	}
	/**
	 *  設定目前文件狀態<p/>
	 *  編製中=01O<br/>
	 *  待覆核=02O<br/>
	 *  已核准=05O
	 **/
	public void setDocStatus(String value) {
		this.docStatus = value;
	}
	
	@SuppressWarnings("rawtypes")
	public void setDocStatus(Enum docStatusEnum) {
		this.docStatus = docStatusEnum.toString();
	}

	/** 
	 * 取得文件URL<p/>
	 * CLS:國內個金<br/>
	 *  LMS:國內企金<br/>
	 *  NULL:海外案件
	 */
	public String getDocURL() {
		return this.docURL;
	}
	/**
	 *  設定文件URL<p/>
	 *  CLS:國內個金<br/>
	 *  LMS:國內企金<br/>
	 *  NULL:海外案件
	 **/
	public void setDocURL(String value) {
		this.docURL = value;
	}

	/** 
	 * 取得維護項目<p/>
	 * 單筆維護=S<br/>
	 *  批次維護=B
	 */
	public String getMaintainType() {
		return this.maintainType;
	}
	/**
	 *  設定維護項目<p/>
	 *  單筆維護=S<br/>
	 *  批次維護=B
	 **/
	public void setMaintainType(String value) {
		this.maintainType = value;
	}


	/** 
	 * 取得帳戶管理員行編(篩選條件)<p/>
	 * 帳戶管理員行編
	 */
	public String getFilterAOid() {
		return this.filterAOid;
	}
	/**
	 *  設定帳戶管理員行編(篩選條件)<p/>
	 *  帳戶管理員行編
	 **/
	public void setFilterAOid(String value) {
		this.filterAOid = value;
	}

	/** 
	 * 取得統一編號(篩選條件)<p/>
	 * 統一編號
	 */
	public String getFilterCustId() {
		return this.filterCustId;
	}
	/**
	 *  設定統一編號(篩選條件)<p/>
	 *  統一編號
	 **/
	public void setFilterCustId(String value) {
		this.filterCustId = value;
	}

	/** 
	 * 取得重複序號(篩選條件)<p/>
	 * 重複序號
	 */
	public String getFilterDupNo() {
		return this.filterDupNo;
	}
	/**
	 *  設定重複序號(篩選條件)<p/>
	 *  重複序號
	 **/
	public void setFilterDupNo(String value) {
		this.filterDupNo = value;
	}

	/** 
	 * 取得客戶名稱(篩選條件)<p/>
	 * 客戶名稱
	 */
	public String getFilterCustName() {
		return this.filterCustName;
	}
	/**
	 *  設定客戶名稱(篩選條件)<p/>
	 *  客戶名稱
	 **/
	public void setFilterCustName(String value) {
		this.filterCustName = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 取得核准人員號碼 **/
	public String getApprover() {
		return this.approver;
	}
	/** 設定核准人員號碼 **/
	public void setApprover(String value) {
		this.approver = value;
	}

	/** 取得核准日期 **/
	public Timestamp getApproveTime() {
		return this.approveTime;
	}
	/** 設定核准日期 **/
	public void setApproveTime(Timestamp value) {
		this.approveTime = value;
	}

	/** 取得邏輯刪除日期 **/
	public Timestamp getDeletedTime() {
		return this.deletedTime;
	}
	/** 設定邏輯刪除日期 **/
	public void setDeletedTime(Timestamp value) {
		this.deletedTime = value;
	}

	/** 取得原帳戶管理員 **/
	public String getOrigAOid() {
		return this.origAOid;
	}
	/** 設定原帳戶管理員 **/
	public void setOrigAOid(String value) {
		this.origAOid = value;
	}

	/** 取得新帳戶管理員 **/
	public String getNewAOid() {
		return this.newAOid;
	}
	/** 設定新帳戶管理員 **/
	public void setNewAOid(String value) {
		this.newAOid = value;
	}
}
