//是否需要先儲存才可換頁
$.extend(window, {
    _requiredSave: false,
    /**
     * 設定畫面是否需要儲存才可動作
     * @param {boolean} b
     */
    setRequiredSave: function(b){
        window._requiredSave = b;
    },
    /**
     * 取得參數
     */
    getRequiredSave: function(){
        return window._requiredSave ? true : false;
    },
    /**
     * 預設畫面需儲存預設動作
     * @param {string} 手動設定之訊息，如無則為"煩請先儲存後，再執行其動作，謝謝"
     */
    checkRequiredSave: function(message){
        if (getRequiredSave()) {
            API.showErrorMessage(message || i18n.def["requireSave"]);
            return false;
        }
        else {
            return true;
        }
    },
    _ignoreTempSave: false,
    /**
     * 設定忽略tab 切換自動儲存
     */
    setIgnoreTempSave: function(b){
        window._ignoreTempSave = b;
    },
    /**
     * 取得參數
     */
    getIgonreTempSave: function(){
        return window._ignoreTempSave ? true : false;
        
	},
	/*設定忽略localSave init*/
	_igonreLocalSave: false,
	setIgonreLocalSave: function(b){
	    window._igonreLocalSave = b
	},
	isIgonreLocalSave: function(){
	    return window._igonreLocalSave;
	}
})

/* mega API */
var Mega = {
    message: function(css, message, title, fn){
        var msg = $("<div id='txnMessage" + parseInt(Math.random() * 100000, 10) + "' class='" + css + "' style='position:absolute;z-index:-1;top:0;left:0;visible:hidden'><div class='msgContent'></div></div>").appendTo("body");
        var _content = msg.find(".msgContent").html(message);
        var width = msg.find(".msgContent").width() + 50;
        var height = msg.find(".msgContent").height() + 100;
        var winWidth = $(window).width() - 30;
        var winHeight = $(window).height() - 50;
        var btn = {};
        btn[i18n && i18n.def && i18n.def.close || "close"] = function(){
            $.thickbox.close();
            msg.remove();
            fn && fn.call(_content);
        };
        msg.thickbox({
            title: i18n && i18n.def && i18n.def.confirmTitle || "提示",
            modal: true,
            //width: width + 50,
            //height: height + 100,
            width: (width > winWidth ? winWidth : width),
            height: (height > winHeight ? winHeight : height),
            align: 'right',
            valign: 'bottom',
            buttons: btn,
            data: {
                mode: 'msg'
            }
        });
        $(_content).closest(".TB_window").draggable({
            handle: ".TB_title",
            containment: ".TB_overlay"
        }).addClass(css).css({
            minWidth: 250
        }).find(".TB_ajaxContent").css("minHeight", "70");
        return $(_content).closest(".TB_window");
    }
};
//自定message 層級
ilog.addMessageLevel([{
    level: 'errorMessage',
    css: 'log-error-message',
    fn: Mega.message
}, {
    level: 'message',
    css: 'log-message',
    fn: Mega.message
}, {
    level: 'message',
    css: 'log-message',
    fn: Mega.message
}]);

//add jqGrid foramter
require(['jqgrid'], function(){
  $.extend($.fn.fmatter, {
    click: (function(){
        var _click = function(cellvalue, options, rowdata){
            return "<a href=\"#\" role=\"gridcellclick\" cellvalue=\"" + cellvalue + "\" idname=\"" + options.colModel.name + "\" rowid=\"" + options.rowId + "\">" + cellvalue + "</a>";
        };
        
        _click.unformat = function(cellvalue, options){
            return cellvalue;
        };
        return _click;
    })(),
    
    fileDownload: (function(){
        var _download = function(cellvalue, options, rowdata){
            return "<a href=\"#\" role=\"gridcelldownload\" cellvalue=\"" + cellvalue + "\" idname=\"" + options.colModel.name + "\" rowid=\"" + options.rowId + "\">" + (i18n && i18n.def.download || "下載") + "</a>";
        };
        
        _download.unformat = function(cellvalue, options){
            return cellvalue;
        };
        return _download;
    })()
  });

//20250312 ces沒有這段 TODO
$.extend($.fmatter.util,{
	// 修改jqgrid的NumberFormat, 增加removeTrailingZero參數
	NumberFormat : function(nData,opts){
		if(!$.fmatter.isNumber(nData)) {
			nData *= 1;
		}
		if($.fmatter.isNumber(nData)) {
			var bNegative = (nData < 0);
			var sOutput = nData + "";
			var sDecimalSeparator = (opts.decimalSeparator) ? opts.decimalSeparator : ".";
			var nDotIndex;
			if($.fmatter.isNumber(opts.decimalPlaces)) {
				// Round to the correct decimal place
				var nDecimalPlaces = opts.decimalPlaces;
				var nDecimal = Math.pow(10, nDecimalPlaces);
				sOutput = Math.round(nData*nDecimal)/nDecimal + "";
				nDotIndex = sOutput.lastIndexOf(".");
				if(nDecimalPlaces > 0) {
				// Add the decimal separator
					if(nDotIndex < 0) {
						sOutput += sDecimalSeparator;
						nDotIndex = sOutput.length-1;
					}
					// Replace the "."
					else if(sDecimalSeparator !== "."){
						sOutput = sOutput.replace(".",sDecimalSeparator);
					}
				// Add missing zeros
					while((sOutput.length - 1 - nDotIndex) < nDecimalPlaces) {
						sOutput += "0";
					}
					
					if(opts.removeTrailingZero){
						var tNum = parseFloat(sOutput);
						sOutput = tNum + "";
					}
					
				}
			}
			if(opts.thousandsSeparator) {
				var sThousandsSeparator = opts.thousandsSeparator;
				nDotIndex = sOutput.lastIndexOf(sDecimalSeparator);
				nDotIndex = (nDotIndex > -1) ? nDotIndex : sOutput.length;
				var sNewOutput = sOutput.substring(nDotIndex);
				var nCount = -1;
				for (var i=nDotIndex; i>0; i--) {
					nCount++;
					if ((nCount%3 === 0) && (i !== nDotIndex) && (!bNegative || (i > 1))) {
						sNewOutput = sThousandsSeparator + sNewOutput;
					}
					sNewOutput = sOutput.charAt(i-1) + sNewOutput;
				}
				sOutput = sNewOutput;
			}
			// Prepend prefix
			sOutput = (opts.prefix) ? opts.prefix + sOutput : sOutput;
			// Append suffix
			sOutput = (opts.suffix) ? sOutput + opts.suffix : sOutput;
			return sOutput;
			
		} else {
			return nData;
		}
	}
})
});

 

//overrite && new CommonAPI
var MegaApi = $.extend(API, {
    ajaxErrorMessage: ilog.errorMessage,
    ajaxNotifyMessage: ilog.message,
    showMessage: ilog.message,
    /**
     * 顯示錯誤訊息
     * @param {String} title
     * @param {String} message
     * @param {Function} closeAction
     */
    showErrorMessage: function(title, message, closeAction){
        if (!message) {
            message = title;
            title = "";
        }
        else 
            if ($.isFunction(message)) {
                closeAction = message;
                message = title;
                title = "";
            }
        var dialog = ilog.errorMessage(message);
        title && dialog.find(".TB_ajaxWindowTitle").text(title);
        closeAction && dialog.find("button").click(closeAction);
        dialog = null;
        
    },
    /**
     * 顯示一般訊息
     * @param {String} title
     * @param {String} message
     * @param {Function} closeAction
     */
    showPopMessage: function(title, message, closeAction){
        if (!message) {
            message = title;
            title = "";
        }
        else 
            if ($.isFunction(message)) {
                closeAction = message;
                message = title;
                title = "";
            }
        var dialog = ilog.message(message);
        title && dialog.find(".TB_ajaxWindowTitle").text(title);
        closeAction && dialog.find("button").click(closeAction);
        dialog = null;
        
    },
    
    /**
     * 顯示確認對話框
     * @param {String} message
     * @param {Function} action function(boolean){} // 回傳TRUE FALSE 以利使用
     */
    confirmMessage: function(message, action){
        var msg = $("<div id='cnfMessage" + parseInt(Math.random() * 100000, 10) + "' class='' style='position:absolute;z-index:-1;top:0;left:0;visible:hidden'><div class='msgContent'></div></div>").appendTo("body");
        var _content = msg.find(".msgContent").html(message);
        //var width = msg.find(".msgContent").width(), height = msg.find(".msgContent").height();
        var width = msg.find(".msgContent").width() + 50;
        var height = msg.find(".msgContent").height() + 100;
        var winWidth = $(window).width() - 30;
        var winHeight = $(window).height() - 50;
        var btn = {};
        var dfd = $.Deferred();
        dfd.done(function(){
            $.thickbox.close();
            msg.remove();
        });
        action && dfd.done(action);
        btn[i18n && i18n.def && i18n.def.sure || "Yes"] = function(){
            dfd.resolve(true);
        };
        btn[i18n && i18n.def && i18n.def.cancel || "cancel"] = function(){
            dfd.resolve(false);
        };
        msg.thickbox({
            title: i18n && i18n.def && i18n.def.confirmTitle || "提示",
            modal: true,
            width: (width > winWidth ? winWidth : width),
            height: (height > winHeight ? winHeight : height),
            align: 'right',
            valign: 'bottom',
            buttons: btn,
            data: {
                mode: 'msg'
            }
        });
        $(_content).closest(".TB_window").draggable({
            handle: ".TB_title",
            containment: ".TB_overlay"
        }).css({
            minWidth: 250
        }).find(".TB_ajaxContent").css("minHeight", "70");
        return $(_content).closest(".TB_window");
    },
    
    /**
     * 產生對話框
     * @param {Object} settings
     * @param {String} action
     */
    iConfirmDialog: function(settings, action){
        var s = $.extend({
            message: i18n.def.actoin_001 || ""
        }, settings);
        var msg = $("<div id='cnfMessage" + parseInt(Math.random() * 100000, 10) + "' class='' style='position:absolute;z-index:-1;top:0;left:0;visible:hidden'><div class='msgContent'></div></div>").appendTo("body");
        var _content = msg.find(".msgContent").html(s.message);
        var width = msg.find(".msgContent").width(), height = msg.find(".msgContent").height();
        var btn = s.buttons ||
        API.createJSON([{
            key: i18n.def.close,
            value: function(){
                $.thickbox.close()
            }
        }]);
        msg.thickbox({
            title: s.title || i18n && i18n.def && i18n.def.confirmTitle || "提示",
            modal: true,
            width: width + 50,
            height: height + 100,
            align: 'right',
            valign: 'bottom',
            buttons: btn,
            data: {
                mode: 'msg'
            }
        });
        $(_content).closest(".TB_window").draggable({
            handle: ".TB_title",
            containment: ".TB_overlay"
        }).css({
            minWidth: 250
        }).find(".TB_ajaxContent").css("minHeight", "70");
    },
    
    /**
     * 流程/儲存相關Dialog
     * @param {Object} settings
     */
    flowConfirmAction: function(settings){
        var s = $.extend(true, {
            autoInject: true, //是否自動將回傳資料放至畫面
            formId: '', //畫面FormId
            checkForm: true, //如有設定formId 則是否需要做 valid
            message: '', //確認對話框訊息
            handler: '', //handler
            action: '', //formActin
            data: {}, //需append 之資料
            success: $.noop,
            done: $.noop,
            localSave: false
        }, settings);
        if (s.checkForm && s.formId && $("#" + s.formId).length > 0 && !$("#" + s.formId).valid()) {
            return;
        }
        function _send(res){
            if (res) {
                var _sendData = $.extend(s.formId && $("#" + s.formId).serializeData() || {}, s.data);
                var tempData = s.localSave &&
                JSON.stringify($.extend({}, responseJSON, _sendData, {
                    _pa: s.handler,
                    formAction: s.action
                })) || {};
                $.ajax({
                    handler: s.handler,
                    action: s.action,
                    data: _sendData,
                    success: function(json){
                        json.mainOid && (window.name = json.mainOid);
                        //uniqe data
                        var pData = $("#mainOid,#mainId,#uid,#mainDocStatus");
                        var _form = s.formId && $("#" + s.formId) || $("body");
                        $.each(["mainOid", "mainId", "uid", "mainDocStatus"], function(i, v){
                            if (json[v]) {
                                if (pData.filter("#" + v).length) {
                                    pData.filter("#" + v).val(json[v]);
                                }
                                else {
                                    _form.append($("<input type='hidden' id='" + v + "'" +
                                    " name='" +
                                    v +
                                    "'" +
                                    " value='" +
                                    json[v] +
                                    "'" +
                                    " />"));
                                }
                                delete json[v];
                            }
                        });
                        API.triggerOpener();
                        setRequiredSave(false);
                        if (s.autoInject) {
                            $(s.formId ? ('#' + s.formId) : 'body').injectData(json);
                        }
                        s.success && s.success.apply(this, [json]);
                    },
                    error: function(){
                        s.localSave && tempSave.error && tempSave.error(tempData);
                    }
                }).done(s.done);
            }
        }
        if (s.message == false) {
            _send(true);
        }
        else {
            API.confirmMessage(s.message || i18n.def['actoin_001'], _send);
        }
    },
    
    /**
     * 上傳對話框
     * @param {Object} s
     */
    uploadDialog: function(s){
        s = $.extend({
            handler: Properties.ckFileUploadHandler,
            fieldId: "",
            title: i18n && i18n.def.insertfile || "請選擇需插入之檔案",
            fileDescId: "",
            fileCheck: false,
            successMsg: false,
            dupFiles: false, //判斷重複檔案名稱
            success: $.noop,
            beforeUpload: $.noop,
            multiple : false,
            data: {
                mainOid: $("#mainOid").val(), // for mega eloan
                crYear: $("#crYear").val()//, // for mega eloan                
            }
        }, s);

        if (!s.fieldId) {
            alert("field error");
        }
         
        var imgDialog = $("#uploadFileTB");
        if (!imgDialog.length) {
            imgDialog = $("<div id=\"uploadFileTB\" title=\"" + s.title + "\" style=\"display:none\"><div id='uploadFileDialog'><input type=\"file\" id=\"" + s.fieldId.replace(/\\/,"") + "\" name=\"" + s.fieldId.replace(/\\/,"") + "\" " + s.fieldIdHtml + (s.multiple?" multiple " :"") +"/></div></div>").appendTo("body");
            s.fileDescId && $("<div id='fileDescTitle' class='tit'>" + (s.fileDescTitle || i18n && i18n.def["uploadFile.srcFileDesc"] || "檔案說明:") + "</div><input type='text' id='" + s.fileDescId + "' name='" + s.fileDescId + "' " + s.fileDescHtml + "/>").appendTo("#uploadFileDialog");
            s.subTitle && $("<div id='subTitle' class='tit'>" + s.subTitle + "</div>").appendTo("#uploadFileDialog");
        }
        imgDialog.thickbox({
            width: s.width || s.fileDescId && 300 || 280,
            height: s.height || s.fileDescId && 190 || 70,
            align: "right",
            valign: "bottom",
            buttons: (function(){
                var b = {};
                b[i18n && i18n.def.uploadbutton || "上傳"] = function(){
                	var flag = true;
                	if (s.beforeUpload) {
                		flag = s.beforeUpload();
                	}
                	if (flag == false) {
                		return;
                	}
                    $.capFileUpload({
						handler: s.handler,// 2010-10-26 Sunkist edited, Properties.ckFileUploadHandler,
						fileCheck: s.fileCheck,
						fileElementId: s.fieldId,
						limitSize: s.limitSize || 3145728,//3*1024*1024
						successMsg: s.successMsg,
						dupFiles: s.dupFiles,
						data: $.extend(s.data, {
						  fieldId: s.fieldId,
						  fileEncoding: s.fileEncoding || 'utf-8'
						}, s.fileDescId && {
						  fileDesc: $("#uploadFileDialog").find("#" + s.fileDescId).val()
						} || {}),
						success: function(json){
						    // 2012-06-20 變更順序, 先關閉thickbox, 在執行後面success方法 edited by Vance
						    $.thickbox.close();
						    s.success(json);
						    imgDialog.empty().remove();
						}
                    });
                };
                b[i18n && i18n.def.cancel || "取消"] = function(){
                    $.thickbox.close();
                    imgDialog.empty().remove();
                };
                return b;
            })()
        });
    },
    /**
     * 產生dialog Grid 以供引入使用
     * @param {Object} settings
     */
    includeGrid: function(settings, action){
        if (typeof settings === 'string' && !action || typeof settings !== 'string' && !settings.id) 
            return;
        var brd = $("#" + settings.id || settings || "");
        var s = $.extend(true, {
            handler: '',
            action: '',
            title: '',
            sortname: '',
            sortorder: 'asc',
            subtitle: i18n.def.grid_selector,
            data: {},
            multiselect: false,
            colModel: null,
            open: $.noop,
            close: $.noop,
            localFirst: true,
            loadonce: true,
            autoSetResponse: {},
            buttons: {},
            loadComplete: function(){
            }
        }, settings || {});
        
        brd = (brd.length > 0 ? brd : (function(){
            brd = $("<div id='" + settings.id + "' title='" + s.title + "' style='display:none'></div>").append("<div id='" + settings.id + "Grid' />").appendTo($('body'));
            var tGrid = brd.find("#" + settings.id + "Grid").iGrid({
                datatype: 'json',
                handler: s.handler,
                action: s.action,
                postData: s.data,
                height: 200,
                sortname: s.sortname,
                sortorder: s.sortorder,
                needPager: false,
                autowidth: false,
                freezeWidth: s.freezeWidth || "",
                multiselect: s.multiselect,
                caption: s.subtitle,
                colModel: s.colModel,
                rowNum: 1000,
                loadonce: s.loadonce,
                loadComplete: s.loadComplete,
				divWidth: s.divWidth,
                ondblClickRow: function(rid){
                }
            });
            return brd;
        })());
        for (var key in s.autoSetResponse) {
            $("#" + s.autoSetResponse[key]).val('');
        }
        brd.find("#" + settings.id + "Grid").trigger("reloadGrid");
        
        brd.thickbox({
            //modal: true,
            width: s.freezeWidth ? (s.freezeWidth + 20) : 0,
            height: s.freezeHeight ? (s.freezeHeight) : 360,
            align: 'center',
            valign: 'bottom',
            open: s.open,
            close: s.close,
            buttons: (function(){
                var _btn = {}
                if (s.buttons) {
                    var grid = $cap("#" + settings.id + "Grid");
                    for (var key in s.buttons) {
                        _btn[key] = (function(fn, g){
                            return function(){
                                fn.call(brd, grid)
                            }
                        })(s.buttons[key], grid)
                    }
                }
                return _btn;
            })()
        });
        
        return brd;
    },
    /**
     * 引入分行資料
     * @param {Object} settings
     */
    showAllBranch: function(settings){
        return MegaApi.includeGrid($.extend({
            handler: 'commongridhandler',
            action: settings.action || "queryAllBranch",
            btnAction: null,
            id: settings.id || "queryAllBranch",
            freezeWidth: 450,
            freezeHeight: 380,
			divWidth:0,
            title: settings.title || i18n.def["grid.showAllBranch"],
            subtitle: settings.subtitle,
            colModel: [{
                colHeader: i18n.def['grid.branchNo'],
                name: 'brNo',
                width: 80,
                sortable: false
            }, {
                name: 'brName',
                colHeader: i18n.def['grid.branchName'],
                width: 150,
                sortable: false
            }, {
                name: 'brnGroup',
                width: 150,
                sortable: false,
                hidden: true
            }, {
                name: 'brnGrpName',
                colHeader: i18n.def['grid.branchGroup'],
                width: 200,
                sortable: false
            }],
            open: function(){
                this.find("button").button().addClass("forview");
            },
            buttons: API.createJSON([{
                key: i18n.def.sure,
                value: function(grid){
                    var ret = grid.getSelRowDatas();
                    if (ret) {
                        settings.btnAction.call(this, grid, ret);
                    }
                    else {
                        API.showErrorMessage(i18n.def['grid_selector']);
                    }
                }
            }, {
                key: i18n.def.cancel,
                value: function(){
                    $.thickbox.close();
                }
            }])
        }, settings));
        
    },
    /**
     * create default dialog button
     * @param {Function} fn
     */
    createDefDialogButton: function(fn){
        return API.createJSON([{
            key: i18n.def.sure,
            value: function(){
                fn && fn.apply(this, arguments);
            }
        }, {
            key: i18n.def.cancel,
            value: function(){
                $.thickbox.close();
            }
        }]);
    },
    
    /**
     * 引入客戶資訊(徵信沿用)
     * @param {Object} settings
     */
    includeIdCes: function(settings){
        var _id = settings.autoResponse && settings.autoResponse.id || 'custId';
        var _id = $("#" + _id);
        if (_id.val() == '' || _id.valid()) {
            settings = $.extend({
                defaultCustType: '2',
                forceNewUser: false
            }, settings);
            CommonAPI.openQueryBox(settings);
        }
    },
    /**
     * 引入客戶資訊
     * @param {Object} settings
     */
    includeId: function(settings){
        var s = $.extend({
            handler: 'customerformhandler', // handler
            action: 'custQuery', // action
            autoResponse: { // 是否自動回填資訊 
                "id": "custId", //   統一編號欄位ID
                "dupno": "dupNo", //   重覆編號欄位ID
                "name": "custName" //   客戶名稱欄位ID
            },
            defaultValue: '', // 預設輸入統編
            checkRule: false, // 檢核條件
            addNew: true, // 是否有新客戶
            sendId: "sendId", // 傳送server id
            idMaxlen: 10,
            idMinlen: 8,
            onlyC: false, // 是只有cust 類型
            btnAction: function(id, dupno, name){ // 按下確定後動作
            },
            title: i18n.def['includeId.title'] || "客戶資料查詢", //dialog title
            subTitle: i18n.def["includeId.subTitle"] || "統一編號" // dialog 提示訊息
        }, settings || {});
        var _idd = $("#includeIdDialog");
        if (!_idd.length) {
            _idd = $("<div id='includeIdDialog' style='display:none'><div id='includeIdDiv'><form id='includeIdForm' onsubmit='return false;'><div id='actions'/><span id='subTitle' /><br/><input type='text' id='sendId' name='sendId' class='required'/></form><form><div class='includeList'/></form></div></div>").appendTo("body");
        }
        _idd.find("#sendId").val(s.defaultValue || "");
        _idd.find("#sendId").attr("class", "").addClass("required").attr('maxlength', s.idMaxlen).attr('minlength', s.idMinlen);
        if (s.checkRule) {
            if (typeof s.checkRule === 'string') {
                _idd.find("#sendId").addClass(s.checkRule);
            }
            else {
                //TODO
            }
        }
        _idd.find("#subTitle").text(s.subTitle);
        _idd.thickbox({
            title: s.title,
            modal: true,
            width: 300,
            height: 100,
            align: 'center',
            valign: 'bottom',
            open: function(){
                this.find(".TB_ajaxContent").css("height", "auto");
                this.find("button[id=" + i18n.def.query + "]").show();
                this.find("button[id=" + i18n.def.sure + "]").hide();
            },
            close: function(){
                $("#includeIdDiv").find("#sendId").readOnly(false).end().find("form").reset().end().find(".includeList").empty();
            },
            buttons: API.createJSON([{
                key: i18n.def.query,
                value: function(){
                    var $this = this;
                    var _form = $this.find("form");
                    if (_form.valid()) {
                        var _list = this.find(".includeList").empty();
                        var _id = _form.find("#sendId").val().toUpperCase();
                        _form.find("#sendId").val(_id);
                        $.ajax({
                            handler: s.handler,
                            action: s.action,
                            data: API.createJSON([{
                                key: s.sendId,
                                value: _id
                            }]),
                            success: function(json){
                                var max = -1;
                                _list.append("<hr />");
                                for (var type in json) {
                                    if (s.onlyC && type != 'C' || type == 'newNo') 
                                        continue;
                                    for (var key in json[type]) {
                                        _list.append("<div class='inlcudeCustList' data-type='" + type + "' data-id='" + _id + "' data-name='" + json[type][key] + "' data-dupno='" + key + "'><a><b>" + key + "</b>" + json[type][key] + "</a></div>");
                                        max++;
                                    }
                                }
                                if (s.addNew) {
                                    _list.append("<div class='inlcudeCustList newData' data-id='" + _id + "' data-dupno='" + json.newNo + "'><b>" + json.newNo + "</b><input type='text' id='newCustName' class='required' name='newCustName' size='16' value='' /> " + (i18n.def['includeId.newCustName'] || "(新客戶)"));
                                }
                                else 
                                    if (max == -1) {
                                        API.showErrorMessage(i18n.def['includeId.noData'] || "查無資料");
                                    }
                                if (s.addNew || max > -1) {
                                    $this.find("button[id=" + i18n.def.sure + "]").show();
                                    if (max > -1) {
                                        $this.find("button[id=" + i18n.def.query + "]").hide();
                                        _form.find("#sendId").readOnly(true);
                                    }
                                }
                            }
                        });
                    }
                }
            }, {
                key: i18n.def.sure,
                value: function(){
                    var $this = this, sel = $this.find(".selectItem");
                    var _id = $this.find("#sendId").val();
                    if (!sel.length) {
                        API.showErrorMessage(i18n.def["includeId.selData"] || "請選擇一筆資料!!");
                    }
                    else {
                        if (sel.is(".newData") && !this.find("#newCustName").valid()) {
                            return;
                        }
                        else {
                            sel.is(".newData") && sel.data("name", this.find("#newCustName").val());
                        }
                        if (s.autoResponse) {
                            s.autoResponse['id'] && $("#" + s.autoResponse['id']).val(_id);
                            s.autoResponse['dupno'] && $("#" + s.autoResponse['dupno']).val(sel.data("dupno"));
                            s.autoResponse['name'] && $("#" + s.autoResponse['name']).val(sel.data("name"));
                        }
                        s.btnAction && s.btnAction.apply(this, [_id, sel.data("dupno"), sel.data("name")]);
                        $.thickbox.close();
                    }
                }
            }, {
                key: i18n.def.cancel,
                value: function(){
                    $.thickbox.close();
                }
            }])
        });
    },
    /**
     * 動態於後端取得下拉選單資料
     */
    loadiCombos: function(settings){
        var s = $.extend({
            _this: $(this),
            reload: false //是否每次都重新取得下拉選單資料
        }, settings || {});
        var combos, iComboaction = [], iCombokey = [];
        (combos = s._this.find('select[iComboKey],select[iComboaction]')).each(function(){
            var key = null;
            if (s.reload || !$(this).find(">option").length) {
                (key = $(this).attr("iComboKey")) && iCombokey.push(key);
                (key = $(this).attr("iComboaction")) && iComboaction.push(key);
            }
        });
        CommonAPI.loadCombos(iCombokey, iComboaction);
        combos.each(function(){
            var $cthis = $(this);
            $cthis.setOptions(icombos[$cthis.attr("iComboKey") || $cthis.attr("iComboaction")], false);
        });
        combos = iComboaction = iComboKey = null;
    },
    
    /**link to other eloan system
     * MegaApi.linkToMS({
     * 	system : ADM|CES|CMS|COL|DEB|LMS|RPS,
     *  url : 'adm/adm101m01/01',
     *  mainId : 'xxxxxxxxxxxxxxxxxxxx',
     *  stxCode : '12345',
     *  data :{
     *  	mainOid:'xxxxxxxxxxxxxxxxxxxxxx',
     *  	docReadOnly:true,
     *      ......
     *  }
     * })
     * */
    linkToMS: function(options){
        $.ajax({
            handler: "codetypehandler",
            action: "getSystemURL",
            type: "POST",
            data: $.extend({}, options),
            success: function(resp){
                $.form.submit({
                    url: resp.ssoverify,
                    target: options.mainId,
                    encode: true,
                    data: {
                        sendData: JSON.stringify($.extend({
                            mainId: options.mainId
                        }, options.data, {
                            url: resp.redirect
                        }))
                    }
                });
            }
        });
    }
});

//0024建檔
$.extend(CommonAPI, {
    openQueryBox: function(settings){
        var s = $.extend({
            handler: 'customerformhandler', // handler
            auto: true,//資料是否回填
            // ******************
            // UFO@2012.05.07 新增參數
            forceNewUser: true, //是否強迫新增使用者(false:建檔失敗會詢問是否結束並將值回傳頁面且dupno & name 解除readonly)
            doNewUser: true, //是否允許新客戶   (false:不出現新客戶選單)
            isInSide: true, //是否為國內   
            defaultCustType: "1", //預設個人(1)或公司(2)
            // ******************                    
            defaultValue: "", //預設輸入統編
            defaultName: "", //預設輸入姓名
            divId: "", //在哪個div 底下
            autoResponse: { // 是否自動回填資訊 
                id: "custId", //   統一編號欄位ID
                dupno: "dupNo", //   重覆編號欄位ID
                name: "custName" //   客戶名稱欄位ID
            },
            fn: $.noop, //當取號或選擇客戶後回傳的程式
            /*{
             * custid 統一編號,
             dupno 重覆序號,
             name 中文姓名,
             lname 英文姓名,
             buscd 行業別}*/
            sendId: "sendId", // 傳送server id,
            btnAction: function(id, dupno, name){
            }, // 按下確定後動作
            title: i18n.def['includeId.title'] || "客戶資料查詢" //dialog title
        }, settings || {});
        
        if ($("#includeCustDiv").length > 0) {
            $("#includeCustDiv").remove();
        }
        
        $("body").append("<div id='includeCustDiv' style='display:none'>" +
        "<form id='includeCustForm' onsubmit='return false;'>" +
        "<label><input name='custType' type='radio' class='required' value='1' checked/>" +
        i18n.def['creatCust.queryType1'] +
        "</label>" +
        "<label><input name='custType' type='radio' class='required' value='2' />" +
        i18n.def['creatCust.queryType2'] +
        "</label>" +
        "<br/>" +
        "<input id='queryInput' maxlength='10' minlength='3' size='11' class='alphanum required' style='margin-right:10px'/>" +
        "<button  id='queryButton' type='button' class='ui-button ui-widget ui-state-default ui-corner-all ui-button-text-only fg-button' ><span class='ui-button-text'><span class='text-only'>" +
        i18n.def.query +
        "</span></span></button>" +
        "<br/><br/><hr/>" +
        "<span id='custSelectDiv'/><br/>" +
        "<span id='custSelectInfo' style='width:200px' />" +
        "</form>" +
        "</div>");
        var $form = $("#includeCustForm"), $queryInput = $form.find("#queryInput"), $custSelectDiv = $("#custSelectDiv"), newUser = "<option value='new'>" + i18n.def['includeId.newCustName'] + "</>", $boxDiv = $("#includeCustDiv"), $queryButton = $form.find("#queryButton");
        
        $queryButton.addClass("fg-button").wrap("<span class=\"fg-buttonset\" />").wrap("<span class=\"fg-child\" />");
        
        $queryButton.click(function(){
            $queryInput.val($.trim($queryInput.val()));
            CommonAPI.query(s);
        });
        
        if (s.defaultValue || s.defaultValue != "") {
            $queryInput.val(s.defaultValue);
        }
        else {
            $queryInput.val(s.defaultName);
        }
        $queryInput.val($.trim($queryInput.val()));
        $("[name=custType]").change(function(){
            var value = $("[name=custType]:checked").val();
            if (value == "1") {
                $queryInput.attr({
                    "maxlength": "10",
                    "minlength": '3',
                    "size": "11",
                    "class": "alphanum required halfword"
                });
                $("#includeCustForm").parent(".TB_ajaxContent").css("height", "132px");
                $("#custSelectInfo").empty();
            }
            else {
                $queryInput.attr({
                    "maxlength": "70",
                    "minlength": '3',
                    "size": "40",
                    "class": "required halfword"
                });
            }
        });
        
        
        $boxDiv.thickbox({ // 使用選取的內容進行彈窗
            title: s.title,
            width: 400,
            height: 210,
            align: "center",
            valign: "bottom",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var $select = $("#custSelect");
                    var cust = "";
                    var $boxDiv = $("#includeCustDiv");
                    var custid = "";
                    var dupNo = "";
                    var name = "";
                    var lname = "";
                    var buscd = "";
                    if ($select.find("option").length == 0) {
                        return API.showErrorMessage(i18n.def['includeId.selData']);
                    }
                    //取得下拉選單查詢的方式 1、統編查詢 2、英文名查詢
                    if ($select.data("querytype") == "1") {
                        var $selectData = $(":selected", $select);
                        if ($selectData.val() == 'new') {
                            $.thickbox.close();
                            CommonAPI.openNewCustBox(DOMPurify.sanitize($queryInput.val()), "1", $.extend(s, {
                                selDupNo: $selectData.attr('data-dupno') || '0'
                            }));
                            $boxDiv.remove();
                        }
                        else {
                            custid = $selectData.attr('data-custid');
                            //custid = selectData.data('custid');
                            dupNo = $selectData.attr('data-dupno');
                            name = $selectData.attr('data-cname');
                            buscd = $selectData.attr('data-buscd');
                            lname = $selectData.attr('data-lname');
                            $.thickbox.close();
                            s.fn({
                                custid: custid,
                                dupno: dupNo,
                                name: name,
                                lname: lname,
                                buscd: buscd
                            });
                            if (s.auto) {
                                if (s.divId && s.divId != "") {
                                    $("#" + s.divId).find("#" + s.autoResponse.id).val(custid);
                                    $("#" + s.divId).find("#" + s.autoResponse.dupno).val(dupNo);
                                    $("#" + s.divId).find("#" + s.autoResponse.name).val($.trim(name));
                                }
                                else {
                                    $("#" + s.autoResponse.id).val(custid);
                                    $("#" + s.autoResponse.dupno).val(dupNo);
                                    $("#" + s.autoResponse.name).val($.trim(name));
                                }
                            }
                            s.btnAction && s.btnAction.apply(this, [custid, dupNo, name]);
                            $boxDiv.remove();
                        }
                    }
                    else {
                        if ($select.find(":selected").val() == 'new') {
                            $.thickbox.close();
                            CommonAPI.openNewCustBox(DOMPurify.sanitize($queryInput.val()), "2", s);
                            $boxDiv.remove();
                        }
                        else {
                            name = $queryInput.val();
                            dupNo = $(":selected", $select).attr('data-dupno');
                            buscd = $(":selected", $select).attr('data-buscd');
                            custid = $select.find('option:selected').attr('data-custid');
                            //custid =$(":selected",$select).data('custid');
                            $.thickbox.close();
                            s.fn({
                                custid: custid,
                                dupno: dupNo,
                                name: name,
                                lname: name,
                                buscd: buscd
                            });
                            if (s.auto) {
                                if (s.divId && s.divId != "") {
                                    $("#" + s.divId).find("#" + s.autoResponse.id).val(custid);
                                    $("#" + s.divId).find("#" + s.autoResponse.dupno).val(dupNo);
                                    $("#" + s.divId).find("#" + s.autoResponse.name).val(name);
                                }
                                else {
                                    $("#" + s.autoResponse.id).val(custid);
                                    $("#" + s.autoResponse.dupno).val(dupNo);
                                    $("#" + s.autoResponse.name).val(name);
                                }
                            }
                            s.btnAction && s.btnAction.apply(this, [custid, dupNo, name]);
                            $boxDiv.remove();
                        }
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                    $("div#includeCustDiv").remove();
                }
            }
        });
        if (s.defaultValue != "") {
            $("[name=custType]").attr("disabled", true);
            $queryInput.attr("disabled", true);
            $("#includeCustForm #queryButton").hide();
            CommonAPI.query(s, 1);
        }
        else 
            if (s.defaultName != "") {
                $("[name=custType]").attr("disabled", true);
                $("[name=custType][value='2']").attr("checked", true);
                $queryInput.attr("disabled", true);
                $("#includeCustForm #queryButton").hide();
                CommonAPI.query(s, 2);
            }
    },
    openNewCustBox: function(custId, queryType, s){
        var div = $("#includeNewCustDiv");
        if (div.length > 0) {
            div.remove();
        }
        if (queryType == '1' && s.isInSide) { //依統編
            var _nameId = s.autoResponse.name;
			$("body").append("<div id='includeNewCustDiv' style='display:none'><form id='includeNewCustForm1' onsubmit='return false;'><input type='radio' name='_name' value='L' checked>"+i18n.def['creatCust.LNAME']+"&nbsp;<input id='"+_nameId+"' name='"+_nameId+"' maxlength='40' size='40' class='fullText' maxlengthC='13'  /><br/><input type='radio' name='_name' value='E' >"+i18n.def['creatCust.ENAME']+"：<input id='E"+_nameId+"' name='E"+_nameId+"' maxlength='70'  size='40' class='halfword' /></form>");
			// 增加可選擇輸入中文全型或英文半型
			var newCustForm1= $("#includeNewCustForm1");
			pageInit.call(newCustForm1);				
			newCustForm1.find("input[name=_name]").click(function(){
				if($(this).val()=='L'){
					newCustForm1.find("#"+_nameId).readOnly(false).addClass('required').focus();
					newCustForm1.find("#E"+_nameId).readOnly(true).removeClass('required').val('');
				}else{
					newCustForm1.find("#"+_nameId).readOnly(true).removeClass('required').val('');
					newCustForm1.find("#E"+_nameId).readOnly(false).addClass('required').focus();
				}
			});
			newCustForm1.find("input[name=_name]:checked").trigger('click');
			$("#includeNewCustDiv").thickbox({	// 使用選取的內容進行彈窗
				title : i18n.def['includeId.newCustName'],
			    width : 350,height : 180,align : "center",valign : "bottom",
			    i18n:i18n.def,
			    buttons : {
			    	"sure" : function(){
						if (newCustForm1.valid()) {
							var _f = s.divId && s.divId != "" ? "#" + s.divId : "body";
							var _name = 'L' == newCustForm1.find("input[name=_name]:checked").val() ? newCustForm1.find("#" + _nameId).val() : newCustForm1.find("#E" + _nameId).val();
							
							
							
							if (s.autoResponse.id && s.autoResponse.id != '') {
								$(_f).find("#" + s.autoResponse.id).val(custId).end().find("#" + s.autoResponse.name).val(_name).end().find("#" + s.autoResponse.dupno).val(s.selDupNo);
							}
							$.thickbox.close();
							s.btnAction && s.btnAction.apply(this, [custId, s.selDupNo, _name, _name]);
						}
			    	},
			    	"cancel" : function(){
			    		$.thickbox.close();
				    }
			    }
			});
        }
        else { //依名稱
            //新客戶的資料建檔
            var createByOracle;
            $.ajax({
                handler: s.handler,
                action: 'createMegaID_ByOracle',
                success: function(obj){
                    createByOracle = obj.createByOracle;
                    if("Y" == createByOracle){
                        $("body").append("<div id='includeNewCustDiv' style='display:none'>" +
                             "<form id='includeNewCustForm'>"+
                             "<span id='newCustId'  name='newCustId' >"+custId+"</span><br/>" +
                             "<span class='text-red'><b>＊</b></span>"+ i18n.def['creatCust.custType']+
                             "：<label><input name='custType' type='radio' class='required' value='1' />"+i18n.def['creatCust.custType1']+"</label>" +
                             "<label><input name='custType' type='radio' class='required' value='2' />"+i18n.def['creatCust.custType2']+"</label><br/>"+
                             "<span class=''><span class='text-red'><b>＊</b></span>"+ i18n.def['creatCust.headNation']+"：<select id='headNation' name='headNation' class='CountryCode'/><br/>"+
                             "<span class='text-red'><b>＊</b></span>"+i18n.def['creatCust.regNation']+"：<select id='regNation'  name='regNation' class='CountryCode'/><br/></span>"+
                             "<span class='text-red'><b>＊</b></span>"+i18n.def['creatCust.birthday']+"：<input id='custBirthday' name='custBirthday' class='required data' size='9' maxlength='10'/><br/>"+
                             "<span id='custLicenseTypeSpan' style='display:none'><span class='text-red'><b>＊</b></span>"+i18n.def['creatCust.licenseType']+"：<select id='custLicenseType' name='custLicenseType'  /><br/>"+
                             i18n.def['creatCust.licenseNO']+"：<input id='custLicenseNO' name='custLicenseNO' maxlength='40'  size='40'   /><br/></span>"+
                             "<span class='caseType1' style='display:none'><span class='text-red'><b>＊</b></span>"+i18n.def['creatCust.bstbl']+"："+
                             "<label><input name='custType1' type='radio' class='required' value='060000' />"+i18n.def['creatCust1.060000']+"</label>"+
                             "<label><input name='custType1' type='radio' class='required' value='130300' />"+i18n.def['creatCust1.130300']+"</label><br/>"+
                             "</span>"+
                             "<span class='caseType2' style='display:none'><span class='text-red'><b>＊</b></span>"+i18n.def['creatCust.bstbl']+"：<select id='creatCustBstbl' name='creatCustBstbl' class='required' /><br/>"+
                             "<span class='text-red'><b>＊</b></span>"+i18n.def['creatCust.bstb2']+"：<select id='creatCustBstb2' name='creatCustBstb2' class='required' ><option value=''>"+ i18n.def.comboSpace+ "</option></select><br/>"+
                             "<span class='text-red'><b>＊</b></span>"+i18n.def['creatCust.bstb3']+"：<select id='creatCustBstb3' name='creatCustBstb3' class='required' ><option value=''>"+ i18n.def.comboSpace+ "</option></select><br/>"+
                             i18n.def['creatCust.bstb4']+"：<select id='creatCustBstb4' name='creatCustBstb4'><option value=''>"+ i18n.def.comboSpace+ "</option></select><br/></span>"+
                             //i18n.def['creatCust.localId']+"：<input id='localId'  name='localId' maxlength='9'  size='9' /><br/>"+
                             //i18n.def['creatCust.CNAME']+"：<input id='custCName' name='custCName' maxlength='40'  size='40' class='fullText' maxlengthC='35'  /><br/>"+
                             //i18n.def['creatCust.LNAME']+"：<input id='custLName' name='custLName' maxlength='40'  size='40' class='fullText' maxlengthC='35'  /><br/>"+
                             "<span class='text-red'><b>＊</b></span>" + i18n.def['creatCust.ENAME'] +"：<input id='custEName' name='custEName' maxlength='70'  size='40' class='halfword' /><br/>"+
                             //"E-Mail：<input id='custEmail' name='custEmail' class='email' maxlength='80' size='60' /><br/>"+
                                    "</form>" +
                                    "</div>");
                    }else{
                        $("body").append("<div id='includeNewCustDiv' style='display:none'>" +
                        "<form id='includeNewCustForm'>" +
                        "<span id='newCustId'  name='newCustId' >" +
                        custId +
                        "</span><br/>" +
                        "<span class='text-red'><b>＊</b></span>" +
                        i18n.def['creatCust.custType'] +
                        "：<label><input name='custType' type='radio' class='required' value='1' />" +
                        i18n.def['creatCust.custType1'] +
                        "</label>" +
                        "<label><input name='custType' type='radio' class='required' value='2' />" +
                        i18n.def['creatCust.custType2'] +
                        "</label><br/>" +
                        "<span class='caseType2' style='display:none'><span class='text-red'><b>＊</b></span>" +
                        i18n.def['creatCust.headNation'] +
                        "：<select id='headNation' name='headNation' class='CountryCode'/><br/>" +
                        "<span class='text-red'><b>＊</b></span>" +
                        i18n.def['creatCust.regNation'] +
                        "：<select id='regNation'  name='regNation' class='CountryCode'/><br/></span>" +
                        "<span class='text-red'><b>＊</b></span>"+i18n.def['creatCust.birthday'] +
                        "：<input id='custBirthday' name='custBirthday' class='required data' size='9' maxlength='10'/><br/>" +
                        "<span id='custLicenseTypeSpan' style='display:none'><span class='text-red'><b>＊</b></span>" +
                        i18n.def['creatCust.licenseType'] +
                        "：<select id='custLicenseType' name='custLicenseType'  /><br/>" +
                        i18n.def['creatCust.licenseNO'] +
                        "：<input id='custLicenseNO' name='custLicenseNO' maxlength='40'  size='40'   /><br/></span>" +
                        "<span class='caseType1' style='display:none'><span class='text-red'><b>＊</b></span>" +
                        i18n.def['creatCust.bstbl'] +
                        "：" +
                        "<label><input name='custType1' type='radio' class='required' value='060000' />" +
                        i18n.def['creatCust1.060000'] +
                        "</label>" +
                        "<label><input name='custType1' type='radio' class='required' value='130300' />" +
                        i18n.def['creatCust1.130300'] +
                        "</label><br/>" +
                        "</span>" +
                        "<span class='caseType2' style='display:none'><span class='text-red'><b>＊</b></span>" +
                        i18n.def['creatCust.bstbl'] +
                        "：<select id='creatCustBstbl' name='creatCustBstbl' class='required' /><br/>" +
                        "<span class='text-red'><b>＊</b></span>"+i18n.def['creatCust.bstb2'] +
                        "：<select id='creatCustBstb2' name='creatCustBstb2' class='required' ><option value=''>" +
                        i18n.def.comboSpace +
                        "</option></select><br/>" +
                        "<span class='text-red'><b>＊</b></span>"+i18n.def['creatCust.bstb3'] +
                        "：<select id='creatCustBstb3' name='creatCustBstb3' class='required' ><option value=''>" +
                        i18n.def.comboSpace +
                        "</option></select><br/>" +
                        i18n.def['creatCust.bstb4'] +
                        "：<select id='creatCustBstb4' name='creatCustBstb4'><option value=''>" +
                        i18n.def.comboSpace +
                        "</option></select><br/></span>" +
                        i18n.def['creatCust.localId'] +
                        "：<input id='localId'  name='localId' maxlength='9'  size='9' /><br/>" +
                        i18n.def['creatCust.CNAME'] +
                        "：<input id='custCName' name='custCName' maxlength='40'  size='40' class='fullText' maxlengthC='13'  /><br/>" +
                        i18n.def['creatCust.LNAME'] +
                        "：<input id='custLName' name='custLName' maxlength='40'  size='40' class='fullText' maxlengthC='13'  /><br/>" +
                        i18n.def['creatCust.ENAME'] +
                        "：<input id='custEName' name='custEName' maxlength='70'  size='40' class='halfword' /><br/>" +
                        "E-Mail：<input id='custEmail' name='custEmail' class='email' maxlength='80' size='60' /><br/>" +
                        i18n.def['creatCust.MEMO'] +
                        "</form>" +
                        "</div>");
                    }
                    var $form = $("#includeNewCustForm"), $boxDiv = $("#includeNewCustDiv");
                    if (queryType == "1") {
                        $form.find("#newCustId").show();
                    }
                    else {
                        $form.find("#newCustId").hide();
                        $form.find("#custEName").val(custId);
                    }
                    // init fullText
                    $form.find("#custCName,#custLName").bind("change", function(){$(this).__val(Properties.itemMaskRule["[class*=fullText]"].call($(this)));})
                    //帶入目前登錄行國別
                    var country = CommonAPI.getBstbl("5", "", s).country;
                    var obj = CommonAPI.loadCombos(["CountryCode", "custLicenseType1", "custLicenseType2"]);
                    $(".CountryCode").setItems({
                        item: obj.CountryCode,
                        format: "{value} - {key}",
                        value: country || ""
                    });
                    if ('CN' == country) {
                        var type = $("[name=custType]:checked").val();
                        $("#custLicenseType").setItems({
                            item: obj["custLicenseType" + type],
                            format: "{value} - {key}",
                            space: false
                        });
                        $("#custLicenseTypeSpan").show();
                    }
                    else {
                        $("#custLicenseTypeSpan").hide();
                    }

                    $("#creatCustBstbl").setItems({
                        item: CommonAPI.getBstbl("1", "", s),
                        format: "{value} - {key}",
                        fn: function(){
                            var value = $(this).val();
                            if (value != "") {
                                $("#creatCustBstb2").setItems({
                                    item: CommonAPI.getBstbl("2", value, s),
                                    format: "{value} - {key}",
                                    fn: function(){
                                        var value = $(this).val();
                                        if (value) {
                                            $("#creatCustBstb3").setItems({
                                                item: CommonAPI.getBstbl("3", value, s),
                                                format: "{value} - {key}",
                                                fn: function(){
                                                    var value = $(this).val();
                                                    if (value) {
                                                        $("#creatCustBstb4").setItems({
                                                            item: CommonAPI.getBstbl("4", value, s),
                                                            format: "{value} - {key}"
                                                        });
                                                    }
                                                    else {
                                                        $("#creatCustBstb4").html("<option value=''>" + i18n.def.comboSpace + "</option>");
                                                    }
                                                }
                                            });
                                        }
                                        else {
                                            $("#creatCustBstb4").html("<option value=''>" + i18n.def.comboSpace + "</option>");
                                        }
                                    }
                                });
                                $("#creatCustBstb3").html("<option value=''>" + i18n.def.comboSpace + "</option>");
                                $("#creatCustBstb4").html("<option value=''>" + i18n.def.comboSpace + "</option>");
                            }
                            else {
                                $("#creatCustBstb2").html("<option value=''>" + i18n.def.comboSpace + "</option>");
                                $("#creatCustBstb3").html("<option value=''>" + i18n.def.comboSpace + "</option>");
                                $("#creatCustBstb4").html("<option value=''>" + i18n.def.comboSpace + "</option>");
                            }
                        }

                    });


                    $("#regNation").change(function(){
                        if ($(this).val() == 'CN') {
                            var type = $("[name=custType]:checked").val();
                            $("#custLicenseType").setItems({
                                item: obj["custLicenseType" + type],
                                format: "{value} - {key}",
                                space: false
                            });
                            $("#custLicenseTypeSpan").show();
                        }
                        else {
                            $("#custLicenseTypeSpan").hide();
                        }
                    });

                    if ($("[name=custType]:checked").val() == "1") {
                        $("#includeNewCustForm").find("span.caseType1").show();
                        $("#regNation").val(country);
                        $("#includeNewCustForm #custType1").val("060000");
                    }
                    $("[name=custType]").change(function(){
                        var custTypeValue = $("[name=custType]:checked").val();
                        $("#custLicenseType").setItems({
                            item: obj["custLicenseType" + custTypeValue],
                            format: "{value} - {key}",
                            space: false
                        });
                        //當等於公司戶
                        if (custTypeValue == "2") {
                            $("#includeNewCustForm").find("span.caseType2").show();
                            $("#includeNewCustForm").find("span.caseType1").hide();
                        }
                        else {
                            $("#regNation").val(country);
                            if (country == 'CN') {
                                var type = $("[name=custType]:checked").val();
                                $("#custLicenseType").setItems({
                                    item: obj["custLicenseType" + type],
                                    format: "{value} - {key}",
                                    space: false
                                });
                                $("#custLicenseTypeSpan").show();
                            }
                            else {
                                $("#custLicenseTypeSpan").hide();
                            }
                            $("#includeNewCustForm").find("span.caseType1").show();
                            $("#includeNewCustForm").find("span.caseType2").hide();
                            $("#includeNewCustForm#custType1").val("060000");
                        }
                    });

                    // *************
                    // UFO@2012.05.07 增加判斷custType預設值
                    if (s.defaultCustType == "2") {
                        $form.find("input[name=custType][value=2]").attr("checked", true);
                    }
                    else {
                        $form.find("input[name=custType][value=1]").attr("checked", true);
                    }
                    $form.find("[name=custType]").change();
                    // *************

                    $("#custBirthday").datepicker();
                    $form.validate();

                    $boxDiv.thickbox({ // 使用選取的內容進行彈窗
                        title: i18n.def['includeId.newCustName'],
                        width: 530,
                        height: 450,
                        align: "center",
                        valign: "bottom",
                        i18n: i18n.def,
                        buttons: {
                            "sure": function(){
                                if (!$form.valid()) {
                                    return false;
                                }
                                var cName = $("#custCName").val();
                                var eName = $("#custEName").val();
                                var lName = $("#custLName").val();
                                var bstb1 = $("#creatCustBstb1").val();
                                var bstb2 = $("#creatCustBstb2").val();
                                var bstb3 = $("#creatCustBstb3").val();
                                //var bstb4=$("#creatCustBstb4").val()
                                if (bstb1 == "" && bstb2 == "" && bstb3 == "") {
                                    //creatCust.bstbError=請選擇一種行業別
                                    return CommonAPI.showMessage(i18n.def['creatCust.bstbError']);
                                }
                                var __newCustIdVal = $form.find("#newCustId").val();
                                var custTypeChecked = $form.find("input[name=custType]:checked").val();
                                if ("1" == queryType && "TW" == $form.find("#headNation").val()){
                                    if("1" == custTypeChecked && !CommonAPI.checkTWID(__newCustIdVal)) {
                                        return CommonAPI.showMessage(i18n.def['val.twid']);
                                    }else if ("2" == custTypeChecked && !CommonAPI.checkCompanyNo(__newCustIdVal)){
                                        return CommonAPI.showMessage(i18n.def['val.compNo']);
                                    }
                                }
                                $.ajax({
                                    handler: s.handler,
                                    action: 'addNewCust',
                                    data: {
                                        formData: JSON.stringify($form.serializeData()),
                                        custId: __newCustIdVal,
                                        queryType: queryType
                                    },
                                    success: function(obj){
                                        $.thickbox.close();
                                        s.fn({
                                            custid: obj.MEGA_ID.slice(0, 10),
                                            dupno: obj.MEGA_ID.slice(10),
                                            name: obj.CNAME,
                                            lname: obj.ENAME,
                                            buscd: obj.buscd
                                        });
                                        if (s.auto) {
                                            if (s.divId && s.divId != "") {
                                                $("#" + s.divId).find("#" + s.autoResponse.id).val(obj.MEGA_ID.slice(0, 10));
                                                $("#" + s.divId).find("#" + s.autoResponse.dupno).val(obj.MEGA_ID.slice(10));
                                                $("#" + s.divId).find("#" + s.autoResponse.name).val(obj.ENAME);
                                            }
                                            else {
                                                $("#" + s.autoResponse.id).val(obj.MEGA_ID.slice(0, 10));
                                                $("#" + s.autoResponse.dupno).val(obj.MEGA_ID.slice(10));
                                                $("#" + s.autoResponse.name).val(obj.ENAME);
                                            }
                                        }
                                        s.btnAction && s.btnAction.apply(this, [obj.MEGA_ID.slice(0, 10), obj.MEGA_ID.slice(10), obj.ENAME]);
                                        $boxDiv.remove();
                                    },
                                    error: function(obj){
                                        if (s.forceNewUser) {
                                            return;
                                        }
                                        CommonAPI.confirmMessage(i18n.def['creatCust.failAndConfirmExit'], function(b){
                                            if (b) {
                                                var custIdVal = $form.find("#newCustId").html();
                                                var ename = $("#custEName").val();
                                                if (s.auto) {
                                                    if (s.divId && s.divId != "") {
                                                        $("#" + s.divId).find("#" + s.autoResponse.id).val(custIdVal);
                                                        $("#" + s.divId).find("#" + s.autoResponse.dupno).val("0");
                                                        $("#" + s.divId).find("#" + s.autoResponse.name).val(ename);
                                                        $("#" + s.divId).find("input#" + s.autoResponse.dupno).removeAttr("readonly");
                                                        $("#" + s.divId).find("input#" + s.autoResponse.name).removeAttr("readonly");
                                                    }
                                                    else {
                                                        $("#" + s.autoResponse.id).val(custIdVal);
                                                        $("#" + s.autoResponse.dupno).val("0");
                                                        $("#" + s.autoResponse.name).val(ename);
                                                        $("input#" + s.autoResponse.dupno).removeAttr("readonly");
                                                        $("input#" + s.autoResponse.name).removeAttr("readonly");
                                                    }
                                                }
                                                $.thickbox.close();
                                                s.btnAction && s.btnAction.apply(this, [custIdVal, "0", ename]);
                                                $boxDiv.remove();
                                            }
                                        });
                                    }
                                });
                            },
                            "cancel": function(){
                                $.thickbox.close();
                                $boxDiv.remove();
                            }
                        }
                    });
                }
            });
        }
    },
    
    queryEngName: function(name){
        var queryResult = {};
        $.ajax({
            handler: "customerformhandler",
            action: 'queryEngName',
            async: false,
            data: {
                engName: name
            },
            success: function(obj){
                queryResult = obj;
                /* custID:同英文戶名客戶ID,
                 * dupNo:重複序號,
                 * companyDate:同客戶ID公司戶設立日,
                 * reqID:負責人ID,
                 * swiftID:SWIFT-ID,//同業代碼
                 * regNation:公司註冊地國別
                 */
            }
        });
        return queryResult;
    },
    /*
     * 取得行業別清單
     * @param key為空時，抓大項，有值時抓中項
     * type 1~4　抓取行業別
     * type 5抓取目前登錄行國別
     *
     * */
    getBstbl: function(type, key, s){
        var itemValue = {};
        $.ajax({
            handler: s.handler,
            action: 'getBstbl',
            async: false,
            data: {
                key: key || "",
                type: type
            },
            success: function(item){
                itemValue = item;
                function isEmpty(obj){
                    for (var prop in obj) {
                        if (obj.hasOwnProperty(prop)) 
                            return false;
                    }                        
                    return true;
                }
                if ('2' == type) {
                    if (!isEmpty(item)) {
                        $("#creatCustBstb2,#creatCustBstb3").addClass('required');
                    }
                    else {
                        $("#creatCustBstb2,#creatCustBstb3").removeClass('required');
                    }
                }
                else if ('3' == type) {
                    if (!isEmpty(item)) {
                        $("#creatCustBstb3").addClass('required');
                    }
                    else {
                        $("#creatCustBstb3").removeClass('required');
                    }
                }
            }
        });
        return itemValue;
    },
    query: function(s, queryType){
        var $form = $("#includeCustForm"), $queryInput = $form.find("#queryInput"), $custSelectDiv = $("#custSelectDiv"), newUser = "<option value='new'>" + i18n.def['includeId.newCustName'] + "</>", $boxDiv = $("#includeCustDiv");
        if (!$form.valid()) {
            return false;
        }
        $queryInput.val($queryInput.val().toUpperCase());
        var type = "";
        if (queryType == undefined) {
            type = $form.find("[name=custType]:checked").val();
        }
        else {
            type = queryType;
        }
        
        $custSelectDiv.empty();
        $custSelectDiv.append("<select id='custSelect' style='width:350px' data-querytype='" + DOMPurify.sanitize(type) + "'/>");
        
        if (type == "1") {
        
            $.ajax({
                handler: 'customerformhandler',
                action: 'custQueryBy0024',
                data: {
                    sendId: $queryInput.val()
                },
                success: function(obj){
                    var $select = $("#custSelect");
                    var tempStr = "";
                    $("#custSelectInfo").empty();
                    if (obj.D) {
                        for (var i in obj.D) {
                            //當為8碼時只顯示為0的重覆序號
                            if ($queryInput.val().length == 8) {
                                if (obj.D[i].dupno != "0") {
                                    continue;
                                }
                            }
                            var tmp = $("<option></option>");
                            tmp.attr("value", obj.D[i].dupno);
                            for (var key in obj.D[i]) {
                                //tempStr+=" data-"+key+"='"+ obj.D[i][key]+"'";
                                tmp.attr("data-" + key , obj.D[i][key]);
                            }
							//戶名為 O'RITE　INTL　CORP 時下面程式會有問題，會變成只抓到O
                            //tempStr+="<option value='"+obj.D[i].dupno+"' ";
                            //for(var key in obj.D[i]){
                            //	tempStr+=" data-"+key+"='"+ obj.D[i][key]+"'";
                            //}
                            //tempStr+=">"+obj.D[i].dupno+" "+obj.D[i].cname +"</>";
                            tmp.text(obj.D[i].dupno + " " + obj.D[i].cname);
							$select.append(tmp);  
                        }                        
                                              
                        if (s == undefined || s.doNewUser) {
                            $select.append("<option value='new' data-id='" + DOMPurify.sanitize($queryInput.val()) + "' data-dupNo='" + DOMPurify.sanitize(obj.newNo) + "'>" + i18n.def['includeId.newCustName'] + "</>");
                        }
                    }
                    else {
                        if (s == undefined || s.doNewUser) {
                            $select.append(newUser);
                            
                        }
                        else {
                            $select.attr("readonly", "readonly");
                        }
                        API.showErrorMessage(i18n.def['includeId.noData']);
                    }
                }
            });
        }
        else {
            //以英文名查詢
            /* custid:同英文戶名客戶ID,
             * dupno:重複序號,
             * companydate:同客戶ID公司戶設立日,
             * reqid:負責人ID,
             * swiftid:SWIFT-ID,//同業代碼
             * regnation:公司註冊地國別
             */
            var $select = $("#custSelect");
            var result = CommonAPI.queryEngName($queryInput.val());
            if (!$.isEmptyObject(result)) {
                $select.empty();
                
                var tempStr = "";
                var value = result.val;
                for (var i in value) {
                    tempStr += "<option value='" + value[i].dupno + "' ";
                    for (var key in value[i]) {
                        tempStr += " data-" + key + "='" + value[i][key] + "'";
                    }
                    tempStr += ">" + value[i].dupno + " " + value[i].custid + "</>";
                }
                $select.html(DOMPurify.sanitize(tempStr));
                $select.append(newUser);
                //綁定change事件 
                $select.change(function(){
                    //當非英文名查詢會傳回undefined
                    var $selectData = $(":selected", $(this));
                    if ($(this).data("querytype") == "2") {
                        function padLeft(str,length){
                            if(str.length >= length)
                                return str;
                            else
                                return padLeft("0" +str,length);
                        }
                        /*creatCust.companyDate=同客戶ID公司戶設立日
                         creatCust.reqID=負責人ID
                         creatCust.swiftID=同業代碼
                         creatCust.headNation=公司註冊地國別*/
                        var tempHtml = "<span >" + i18n.def['creatCust.companyDate'] + "：" + ($selectData.data("companydate") ? $selectData.data("companydate") : "") + "<br/>" +
                        i18n.def['creatCust.reqID'] +
                        "：" +
                        ($selectData.data("reqid") ? $selectData.data("reqid") : "") +
                        "<br/>" +
                        i18n.def['creatCust.swiftID'] +
                        "：" +
                        ($selectData.data("swiftid") ? $selectData.data("swiftid") : "") +
                        "<br/>" +
                        i18n.def['creatCust.buscd'] +
                        "：" +
                        ($selectData.data("buscd") ? padLeft($selectData.data("buscd"),6) : "") +
                        "<br/>" +
                        i18n.def['creatCust.headNation'] +
                        "：" +
                        ($selectData.data("regnation") ? $selectData.data("regnation") : "") +
                        "</span>";
                        $("#custSelectInfo").html(tempHtml);
                        $("#includeCustForm").parent(".TB_ajaxContent").css("height", "220px");
                    }
                    else {
                        $("#includeCustForm").parent(".TB_ajaxContent").css("height", "132px");
                        $("#custSelectInfo").empty();
                    }
                });
                $select.trigger("change");
            }
            else {
                $select.empty();
                if (s == undefined || s.doNewUser) {
                    $select.append(newUser);
                }
                else {
                    $select.attr("readonly", "readonly");
                }
            }
        }
    }
});


(function($){
    //overrite tabs
    if ($.fn.tabs) {
        var _tabs = $.fn.tabs;
        $.fn.tabs = function(){
            var args = arguments;
            this.each(function(){
                var $this = $(this);
                if (!$this.find("ul:first").is(".ui-tabs-nav")) {
                    _tabs.apply($this).find("ul:first > li").click(function(e){
                        var $this = $(this);
                        if ($(this).find("a").attr("goto")) {
                            $.blockUI({
                                fadeIn: 0,
                                fadeOut: 0,
                                message: i18n.def.tabchange + '  <img src="' + webroot + '/img/ajax-loader.gif"/>'
                            });
                            e.preventDefault();
                            setCloseConfirm(false);
                            setTimeout(function(){
                                var dfd = $.Deferred();
                                dfd.done(function(json){
                                    Properties.window.isChangeTab = true;
                                    var tData = $.extend({}, responseJSON || {}, {
                                        mainOid: $("#mainOid").val(),
                                        mainDocStatus: $("#mainDocStatus").val(),
                                        mainId: $("#mainId").val()
                                    });
                                    delete tData.page;
                                    delete tData._pa;
                                    delete tData.formAction;
                                    delete tData.Auth;
                                    $.form.submit({
                                        url: $this.find("a").attr("goto"),
                                        data: tData
                                    });
                                });
                                if (!getIgonreTempSave() && window.tempSave && window.tempSave.handler) {
                                    if ((window.tempSave.beforeCheck ? window.tempSave.beforeCheck() : true)) {
                                        if (!checkRequiredSave()) {
                                            $.unblockUI();
                                            return;
                                        }
                                        window.tempSave(dfd);
                                    }
                                    else {
                                        $.unblockUI();
                                    }
                                    return false;
                                }
                                else {
                                    dfd.resolve();
                                }
                            }, 50);
                            return false;
                        }
                    });
                    // index scroll
                    //var ind = $this.find("ul:first > li").index($this.find("ul:first > li.current-tab:first"));
                    //$this.tabs('select', ind > 0 ? ind : 0);
                    //ind = null;
                }
                else {
                    _tabs.apply($this, args);
                }
            });
            return this;
        };
    }
    //override thickbox
    if ($.fn.thickbox) {
        var _thickbox = $.fn.thickbox;
        $.fn.thickbox = function(){
            var open = arguments && arguments[0].open || undefined;
            var dialog = _thickbox.call(this, $.extend({}, arguments[0], {
                open: function(){
                    API.resize(this);
                    open && open.apply(this);
                }
            }));
            
        };
    }
    
    
    // add click action
	require(['common.jqgrid'], function() {
		var _iGrid = $.fn.iGrid;
		$.fn.iGrid = function() {
			var grid = null;
			grid = _iGrid.apply(this, arguments);
			var parms = grid.jqGrid('getGridParam', 'colModel');
			grid.on('click', "a[role=gridcellclick]", function(event) {
				var $this = $(this),
					id = $(this).attr("idname");
				for (var op in parms) {
					if (parms[op].name == id) {
						parms[op].onclick && parms[op].onclick.call(this, $(this).attr("cellvalue"), parms[op], grid.getRowData($(this).attr("rowid")), event);
					}
				}
			}).on('click', "a[role=gridcelldownload]", function(event) {
				var $this = $(this);
				$.form.submit({
					url: 'file',
					data: {
						id: $(this).attr("cellvalue")
					},
					target: "_blank"
				});
			});

			return grid;
		};
	});
    //override remove
    var __remove = jQuery.fn.remove;
    jQuery.fn.remove = function(){
        return __remove.apply(this.add(this.filter("button").closest(".fg-buttonset")), arguments);
    }
    
    //override hide
    var __hide = jQuery.fn.hide;
    jQuery.fn.hide = function(){
        return __hide.apply(this.add(this.filter("button").closest(".fg-buttonset")), arguments);
    }
    
    //override show
    var __show = jQuery.fn.show;
    jQuery.fn.show = function(){
        return __show.apply(this.add(this.filter("button").closest(".fg-buttonset")), arguments);
    }
})(jQuery);

/**
 * sso control call SSOC.startSSOC() with start
 */
var SSOC = {
	temp_timer : undefined,
	timeCheckTime: (28 * 60 * 1000),
    retry: 1,
    isTimeout: true,
    checkLink: "sso/dfs",
    
    checkSession: function(){
        (SSOC.isTimeout && ((SSOC.tReTry--) <= 0)) ? SSOC.doTimeOutSession() : SSOC.doRefreshSession().startSSOC(SSOC.tReTry);
    },
    
    doRefreshSession: function(){
        // 傳送至後端更新SSO
        ilog.debug(SSOC.tReTry);
        $.ajax({
            dataType: 'json',
            type: "POST",
            url: __ajaxHandler,
            data: {
                _pa: "codetypehandler",
                formAction: "doCheckSSO"
            }
        });
        return this;
    },
    
	doTimeOutSession: function(){
	    ilog.debug("SSOC.doTimeOutSession");
	    // 登出Session
	    $.ajax({
	        type: "POST",
	        url: relativePathPrefix + SSOC.checkLink,
	        data: {
	            check: true
	        }
	    }).done(function(json) {
	      if (json.lg == true) {
	        CommonAPI.showPopMessage(i18n.def.sessionTimeout, function(){
	          logout();
	        });
	      }
	      else {
	        SSOC.startSSOC(0);
	      }
	    });
	    SSOC.isTimeout = true;
	},
    
    setIsTimeout: function(event){
        SSOC.tReTry = SSOC.retry;
        SSOC.isTimeout = false;
        SSOC.unBindCheckPoint();
    },
    
    bindCheckPoint: function(){
        $('body').bind("keydown", SSOC.setIsTimeout);
        $('body').bind("mousemove", SSOC.setIsTimeout);
    },
    
    unBindCheckPoint: function(){
        $('body').unbind("keydown", SSOC.setIsTimeout);
        $('body').unbind("mousemove", SSOC.setIsTimeout);
    },
    
    startSSOC: function(reTry){
        SSOC.isTimeout = true;
        SSOC.bindCheckPoint();
        SSOC.tReTry = (reTry == undefined) ? SSOC.retry : reTry;
        this.temp_timer = setTimeout(SSOC.checkSession, SSOC.timeCheckTime);
    },
    stopSSOC: function(){
    	clearTimeout(this.temp_timer);
    }
};

var MSGCHKR = {
	temp_timer : undefined,
	timeCheckTime: (10 * 60 * 1000),
    checkSession: function(){
        MSGCHKR.doCheckPunchMsg().startMSGCHKR();
    },
    doCheckPunchMsg: function(){
        $.ajax({
            dataType: 'json',
            type: "POST",
            url: __ajaxHandler,
            data: {
                _pa: "sysmessageformhandler",
                formAction: "checkPunchMsg"
            },
            success: function(json){
                if (json.result != "") {
                    ilog.server(json.result);
                }
            }
        });
        return this;
    },
    startMSGCHKR: function(){
    	this.temp_timer = setTimeout(MSGCHKR.checkSession, MSGCHKR.timeCheckTime);
    },
    stopMSGCHKR: function(){
    	clearTimeout(this.temp_timer);
    }
};

function timer_long_ajax_beg(t){
	SSOC.doRefreshSession();
	//~~~~~~
	SSOC.stopSSOC();    
	MSGCHKR.stopMSGCHKR();		
}
function timer_long_ajax_end(){
	SSOC.doRefreshSession();
	//~~~~~~
	SSOC.startSSOC();
    MSGCHKR.startMSGCHKR();
}

var localErrorTemp;
// document onload
$(function(){
    SSOC.startSSOC();
    MSGCHKR.startMSGCHKR();
    // select current page
	/* upgradetodo test ces已刪除這段，不註解前端會有錯誤
    if (typeof responseJSON != "undefined" && responseJSON.page) {
        var ul = $("body").find(".tabs:first  ul:first"), current_tab = ul.find("li > a[goto=" + responseJSON.page + "]").parent();
        var ind = ul.children("li").index(current_tab);
        ul.closest(".tabs").tabs('select', ind > 0 ? ind : 0);
    }
	*/
    try {
        isIgonreLocalSave() ? localdfd.resolve(false) : localErrorTemp = $("#localerrortemp").localSave();
    } 
    catch (e) {
    }
	$('body').on('click', '.inlcudeCustList', function(){
        $(this).addClass("selectItem").siblings("div").removeClass("selectItem");
    });
    $('#leftFrame').find('.left-nav').find('a').click(function(ev){
        var $this = $(this), _target;
        if (!$this.attr("target")) {
            ev.preventDefault();
            if ($this.siblings("ul").length) {
                var sel = $this.siblings("ul");
                sel.is(":visible") ? sel.hide().parent("li").children("a").removeClass('clicked') : sel.show().parent("li").children("a").addClass('clicked');
            }
            return;
        }
        else 
            if ((_target = $("#" + $this.attr("target"))).length) {
                i18n.reset();
                _target.empty().load($this.attr("href"), function(){
                    $(this).hide().fadeIn("slow");
                    __ajaxHandler.indexOf('x=') > -1 && (__ajaxHandler = $this.attr("href") + "&" + __ajaxHandler.substr(__ajaxHandler.indexOf('x=')));
                });
                $this.closest('.left-nav').find('.selected').removeClass('selected');
                $this.add($this.parents("li").children("a")).addClass('selected');
            }
            else {
                window.open($this.attr("href"));
            }
        ev.preventDefault();
    }).end().end().find("#gohome").click(function(){
        API.confirmMessage(i18n.def['flow.exit'], function(res){
            res && logout();
        });
    });
    
    $("#btnExit").click(function(){
        API.confirmMessage(i18n.def['flow.exit'], function(res){
            res && window.close();
        });
        //    	//modify by fantasy 2013/01/30
        //    	if (FormAction && FormAction.open && $('#btnSave').size() > 0){
        //    		FormAction.exit();
        //    	}else{
        //        API.confirmMessage(i18n.def['flow.exit'], function(res){
        //            res && window.close();
        //        });
        //    	}
    });
    $("button[savefor]").click(function(){
        $cap("#" + $(this).attr("savefor")).openHistory();
    });
    //add textarea line max length
    $("textarea[linelength]").blur(function(){
        var $this = $(this);
        if ($this.attr("linelength")) {
            var astr = [];
            $.each($this.val().split('\n'), function(i, v){
                astr = astr.concat(v.match(new RegExp(".{" + $this.attr("linelength") + "}|.{1," + ($this.attr("linelength") * 1 - 1) + "}$", "g")));
            });
            $this.val(astr.join('\n'));
            astr = null;
        }
        $this = null;
    });
    
    if (typeof responseJSON != "undefined" && responseJSON.docReadonly) {
        $("body").lockDoc(0);
        _docReadonly = 1;
        setIgnoreTempSave(true);
    }
});

//設定AJAX的傳送值 add by fantasy 2011/05/23
$(function(){
    $.ajaxSetup({
        data: typeof responseJSON != "undefined" && responseJSON || {}
    });
});

//flow action add by fantasy 2011/08/26
var FlowAction = {
    start: function(){
        //maybe to do...
    },
    cannel: function(){
        //maybe to do...
    },
    run: function(defName, next, actionName){
        //maybe to do show manager list
        if (FormAction && FormAction.open) {
            if (FlowAction.clientCheck(actionName)) {
                FlowAction.exe(defName, next);
            }
        }
        else {
            FlowAction.serviceCheck(defName, next, actionName);
        }
    },
    exe: function(defName, next){
        $.ajax({
            type: "POST",
            handler: "flowactionformhandler",
            action: "run",
            data: {
                //mainid: responseJSON.mainId,
                //不知為什麼txCode會變成object
                txCode: (responseJSON ? responseJSON.txCode : ""),
                defName: defName,
                next: next,
                showMsg: true
            },
            success: function(responseData){
                CommonAPI.triggerOpener("gridview", "reloadGrid");
                setCloseConfirm(false);
                window.close();
            }
        });
    },
    clientCheck: function(actionName){
        if (!FormAction.check()) {
            //i18n.def.confirmContinueRun 資料已異動，尚未儲存，是否繼續執行?
            return confirm(i18n.def.confirmContinueRun);
        }
        else {
            //i18n.def.actoin_001 是否執行此動作?
            return confirm(FlowAction.display(actionName) + i18n.def.actoin_001);
        }
        return true;
    },
    serviceCheck: function(defName, next, actionName){
        $.ajax({
            type: "POST",
            handler: "flowactionformhandler",
            action: "check",
            success: function(responseData){
                if (responseData.result) {
                    //i18n.def.confirmContinueRun 資料已異動，尚未儲存，是否繼續執行?
                    if (!confirm(i18n.def.confirmContinueRun)) {
                        return;
                    }
                }
                //i18n.def.actoin_001 是否執行此動作?
                else 
                    if (!confirm(FlowAction.display(actionName) + i18n.def.actoin_001)) {
                        return;
                    }
                FlowAction.exe(defName, next);
            }
        });
    },
    display: function(actionName){
        return (actionName != null ? "[" + actionName + "]" : "");
    }
};

/**
 * 主管清單 add by fantasy 2012/09/26
 */
var ManagerAction = {
    id: '_managerThickBox',
    itemId: '_managerItem',
    open: function(data){
        //set option default
        var options = $.extend({
            confirm: true,
            handler: 'flowactionformhandler',
            action: 'manager',
            title: i18n.def['err.chooseBoss'] || '',
            sign: ['單位主管'], //首長,單位主管,甲級主管,乙級主管,經辦人員
            unit: '', //userInfo.unitNo default login unitNo
            callback: function(managerId){
                alert('manager ID:' + managerId + '\nplease implements callback!');
            }
        }, data);
        //find & create
        var $tb = $('#' + ManagerAction.id);
        if ($tb.length == 0) {
            $('body').append('<div id="' + ManagerAction.id + '" style="display:none;"><div>' +
            '<table class="tb2" width="100%"><tr><td width="30%" class="hd2" align="right">' +
            (i18n.def['name'] || '名稱') +
            '</td><td>' +
            '<select id="' +
            ManagerAction.itemId +
            '" name="' +
            ManagerAction.itemId +
            '" />' +
            '</td></tr></table>' +
            '</div></div>');
        }
        //get manager data
        $.ajax({
            type: "POST",
            handler: options.handler,
            action: options.action,
            data: {
                sign: options.sign,
                unit: options.unit
            },
            success: function(responseData){
            	var thisItem = $('#' + ManagerAction.itemId);
            	thisItem.setItems({
                    item: responseData.manager,
                    format: "{key}"
                });
                ManagerAction.popup(options);
            }
        });
    },
    popup: function(options){
        $('#' + ManagerAction.id).thickbox({ // 使用選取的內容進行彈窗
            title: options.title,
            width: 300,
            height: 100,
            valign: 'bottom',
            align: 'center',
            buttons: {
                'sure': function(){
                    var value = $.trim($('#' + ManagerAction.itemId).val());
                    if (value) {
                        if (options.confirm) {
                            MegaApi.confirmMessage(i18n.def["confirmApply"], function(action){
                                if (action) {
                                    $.thickbox.close();
                                    options.callback(value);
                                }
                            });
                        }
                        else {
                            $.thickbox.close();
                            options.callback(value);
                        }
                    }
                    else {
                        MegaApi.showErrorMessage(i18n.def['confirmTitle'], i18n.def['err.chooseBoss']);
                    }
                },
                'close': function(){
                    $.thickbox.close();
                }
            }
        });
    }
};

/**
 * 檢查Form data有無異動 edit by fantasy 2012/05/11
 */
var FormAction = {
    open: true,
    init: function(id){
        if ($.isArray(id)) {
            for (var value in id) {
                FormAction.initData(id[value]);
            }
        }
        else 
            if (id) {
                FormAction.initData(id);
            }
            else {
                $("form").each(function(){
                    FormAction.initData($(this).attr("id"));
                });
            }
    },
    check: function(id, fn){
        var result = true;
        
        if ($.isFunction(id)) {
            $("form").each(function(){
                if (result) 
                    result = FormAction.checkData($(this).attr("id"));
                ;
            });
            FormAction.checkIsFunction(result, id);
        }
        else 
            if ($.isArray(id)) {
                for (var value in id) {
                    if (result) 
                        result = FormAction.checkData(id[value]);
                }
                FormAction.checkIsFunction(result, fn);
            }
            else {
                if (id) {
                    result = FormAction.checkData(id);
                }
                else {
                    $("form").each(function(){
                        if (result) 
                            result = FormAction.checkData($(this).attr("id"));
                        ;
                    });
                }
                FormAction.checkIsFunction(result, fn);
            }
        return result;
    },
    checkIsFunction: function(result, fn){
        if ($.isFunction(fn)) {
            if (!result) {
                MegaApi.confirmMessage(i18n.def["confirmContinueRun"], function(action){
                    if (action) 
                        fn();
                });
            }
            else {
                fn();
            }
        }
    },
    initData: function(formId){
        if (FormAction.isForm(formId)) {
            setTimeout(function(){
                $("body").data(formId, FormAction.getData(formId));
            }, 100);
        }
    },
    checkData: function(formId, fn){
        var result = true;
        if (FormAction.isForm(formId)) {
            var beforeData = $("body").data(formId);
            var currData = FormAction.getData(formId);
            result = (beforeData == currData);
        }
        FormAction.checkIsFunction(result, fn);
        return result;
    },
    isForm: function(formId){
        return (($("#" + formId).attr("tagName") || '').toLowerCase() == 'form');
    },
    getData: function(formId){
        return $("#" + formId).find("input,select,textarea").filter(":not(disabled):not(readonly)").serialize();
    },
	//20250312 TODO cse沒有先註解
    //window.onunload 傳入 document.body
	/*
    processMemoryLeak: function(){
        if ($.browser.msie && $.browser.version <= 7) {
            $(window).unload(function(){
                FormAction.purge(document.body);
                CollectGarbage();
            });
        }
    },
	*/
    purge: function(d){
        var a = d.attributes, i, l, n;
        if (a) {
            for (i = a.length - 1; i >= 0; i -= 1) {
                n = a[i].name;
                //if (typeof d[n] === 'function') {
                //	  d[n] = null;
                //}
                d[n] = null;
            }
        }
        a = d.childNodes;
        if (a) {
            l = a.length;
            for (i = 0; i < l; i += 1) {
                FormAction.purge(d.childNodes[i]);
            }
        }
    },
    /**
     * exit add by fantasy 2013/01/30
     */
    exit: function(){
        if ($('#btnSave').is(':hidden')) {
            API.confirmMessage(i18n.def['flow.exit'], function(res){
                res && window.close();
            });
        }
        else {
            FormAction.confirm();
        }
    },
    /**
     * confirm add by fantasy 2013/01/30
     */
    confirm: function(){
        if ($('#FormActionConfirmThickBox').length == 0) {
            $('body').append('<div id="FormActionConfirmThickBox" style="display:none;" >' +
            '<div>' +
            i18n.def['flow.exit'] +
            '</div></div>');
        }
        $('#FormActionConfirmThickBox').thickbox({
            title: i18n.def['confirmTitle'] || '提示',
            width: 300,
            height: 125,
            align: 'right',
            valign: 'bottom',
            buttons: {
                'saveDataClose': function(){
                    FormAction.saveDataClose();
                },
                'CloseWithoutSave': function(){
                    window.close();
                },
                'cancel': function(){
                    $.thickbox.close();
                }
            }
        });
    },
    /**
     * saveDataClose add by fantasy 2013/01/30
     */
    deferred: null,
    saveDataClose: function(){
        FormAction.deferred = $.Deferred().done(function(){
            window.close();
        });
        $('#btnSave').click();
    }
};


window.tempSave = function(dfd){
    var _sendData = window.tempSave.sendData && window.tempSave.sendData() || {};
    var tempData = JSON.stringify($.extend({}, responseJSON, _sendData, {
        _pa: window.tempSave.handler,
        formAction: window.tempSave.action
    }));
    return $.ajax({
        global: dfd ? false : true,
        handler: window.tempSave.handler,
        action: window.tempSave.action,
        data: _sendData,
        success: function(json){
            dfd && dfd.resolve(json);
            localErrorTemp && localErrorTemp.removeAll();
        },
        error: function(){
            $.unblockUI();
            tempSave.error && tempSave.error(tempData);
        }
    });
};

//畫面切換table 所需設定之資料 如無設定 則直接切換
$.extend(window.tempSave, {
    handler: "",
    action: "",
    beforeCheck: function(){
        return true;
    },
	asyncBeforeCheck: function(){
	  var dfd = $.Deferred();
	  dfd.resolve(true);
	  
	  return dfd.promise();
	},
    sendData: function(){
        return {};
    },
    success: function(json){
    },
    error: function(tempData){
        localErrorTemp && localErrorTemp.add(tempData);
    }
});

//20250312 ces沒有先註解 TODO
//process memory
/*
$(function(){
    //ie 7 process memory leak add by Fantasy 2012/05/31
    FormAction.processMemoryLeak();
    //ie 7 process menory
    if ($.browser.msie && $.browser.version <= 7) {
        $(window).unload(function(){
            try {
                //this.js
                if (Mega) 
                    Mega = null;
                if (MegaApi) 
                    MegaApi = null;
                if (FormAction) 
                    FormAction = null;
                if (FlowAction) 
                    FlowAction = null;
                if (SSOC) 
                    SSOC = null;
                //common.js
                if (i18n) 
                    i18n = null;
                if ($cap) 
                    $cap = null;
                if (currentPage) 
                    currentPage = null
                if (CommonAPI) 
                    CommonAPI = null;
                if (errorCheck) 
                    errorCheck = null;
                if (getErrorMessage) 
                    getErrorMessage = null;
                if (pageInit) 
                    pageInit = null;
                //ckeditor
                $('body').find(".ickeditor").each(function(){
                    var editor = CKEDITOR.instances[$(this).attr("name")];
                    if (editor) 
                        editor.destroy(true);
                    editor = null;
                });
                if (CKEDITOR) 
                    CKEDITOR = null;
                //jquery
                if ($) 
                    $ = null;
                if (jQuery) 
                    jQuery = null
                //未公開的函數CollectGarbage，這個函數是用來進行內存釋放的
                CollectGarbage();
            } 
            catch (e) {
                alert(e);
            }
        });
    }
});
*/
