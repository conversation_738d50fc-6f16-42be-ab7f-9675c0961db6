/* 
 * L210S01BDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L210S01B;

/** 同業聯貸攤貸比率檔 **/
public interface L210S01BDao extends IGenericDao<L210S01B> {

	L210S01B findByOid(String oid);

	List<L210S01B> findByMainId(String mainId);
	
	List<L210S01B> findByOids(String[] oids);

	L210S01B findByUniqueKey(String mainId, Integer seq, String chgFlag);

	List<L210S01B> findByIndex01(String mainId, Integer seq, String chgFlag);
	
	List<L210S01B> findByMainIdAndChgFlag(String mainId, String chgFlag);
}