/* 
 * C120S01S.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 個金客戶報表綜合資訊檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C120S01S", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId","custId","dupNo","dataType","fileSeq"}))
public class C120S01S extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 身分證統編 **/
	@Size(max=10)
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Size(max=1)
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 
	 * 資料類型{1:往來客戶信用異常資料, 2:客戶是否為利害關係人資料, 3:婉卻紀錄資料, 4:證券暨期貨違約交割紀錄, 5:行內_身分證驗證} ClsConstants.C101S01S_dataType
	 */
	@Size(max=1)
	@Column(name="DATATYPE", length=1, columnDefinition="CHAR(1)")
	private String dataType;

	/** 
	 * 資料狀態 全部為0代表正常, 任一碼為1代表資料異常<p/>
	 * 當dataType=1時, 000,   第1碼-發查聯徵負面信用資訊紀錄, 第2碼-2007年1月以後票交所拒往紀錄, 第3碼-票交所近7年退票資料
	 * 當dataType=2時, 00000, 第1碼-是否為銀行法利害關係人,   第2碼-是否為金控法第44條利害關係人,第3碼-是否為金控法第45條利害關係人, 
	 * 						   第4碼-是否為實質關係人(授信以外交易), 第5碼-公司法與本行董事具有控制從屬關係公司
	 * 當dataType=3時, 00,    第1碼-本行有無婉卻記錄, 第2碼-金控有無婉卻記錄
	 * 當dataType=4時, 0	, 第1碼-查詢結果
	 */
	@Size(max=10)
	@Column(name="DATASTATUS", length=10, columnDefinition="VARCHAR(10)")
	private String dataStatus;

	/** 
	 * 報表檔案<p/>
	 * Pdf檔
	 */
	@Size(max=5120000)
	@Column(name="REPORTFILE", length=5120000, columnDefinition="BLOB(5120000)")
	private byte[] reportFile;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;
	
	/** 檔案序號 **/
	@Column(name="FILESEQ", length=1, columnDefinition="CHAR(1)")
	private String fileSeq;
	
	/** 資料建立時間 **/
	@Column(name="DATACREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp dataCreateTime;
	
	/**
	 * 報表檔案存檔類型<BR>
	 * <li>J:JSON
	 */
	@Column(name = "REPORTFILETYPE", length = 1, columnDefinition = "CHAR(1)")
	private String reportFileType;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得身分證統編 **/
	public String getCustId() {
		return this.custId;
	}
	/** 設定身分證統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得身分證統編重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}
	/** 設定身分證統編重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 
	 * 取得資料類型{1:往來客戶信用異常資料, 2:客戶是否為利害關係人資料, 3:婉卻紀錄資料, 4:證券暨期貨違約交割紀錄, 5:行內_身分證驗證}
	 */
	public String getDataType() {
		return this.dataType;
	}
	/**
	 *  設定資料類型{1:往來客戶信用異常資料, 2:客戶是否為利害關係人資料, 3:婉卻紀錄資料, 4:證券暨期貨違約交割紀錄, 5:行內_身分證驗證}
	 */
	public void setDataType(String value) {
		this.dataType = value;
	}

	/** 
	 * 資料狀態 全部為0代表正常, 任一碼為1代表資料異常<p/>
	 * 當dataType=1時, 000,   第1碼-發查聯徵負面信用資訊紀錄, 第2碼-2007年1月以後票交所拒往紀錄, 第3碼-票交所近7年退票資料
	 * 當dataType=2時, 00000, 第1碼-是否為銀行法利害關係人,   第2碼-是否為金控法第44條利害關係人,第3碼-是否為金控法第45條利害關係人, 
	 * 						   第4碼-是否為實質關係人(授信以外交易), 第5碼-公司法與本行董事具有控制從屬關係公司
	 * 當dataType=3時, 00,    第1碼-本行有無婉卻記錄, 第2碼-金控有無婉卻記錄
	 * 當dataType=4時, 0	, 第1碼-查詢結果
	 */
	public String getDataStatus() {
		return this.dataStatus;
	}
	/** 
	 * 資料狀態 全部為0代表正常, 任一碼為1代表資料異常<p/>
	 * 當dataType=1時, 000,   第1碼-發查聯徵負面信用資訊紀錄, 第2碼-2007年1月以後票交所拒往紀錄, 第3碼-票交所近7年退票資料
	 * 當dataType=2時, 00000, 第1碼-是否為銀行法利害關係人,   第2碼-是否為金控法第44條利害關係人,第3碼-是否為金控法第45條利害關係人, 
	 * 						   第4碼-是否為實質關係人(授信以外交易), 第5碼-公司法與本行董事具有控制從屬關係公司
	 * 當dataType=3時, 00,    第1碼-本行有無婉卻記錄, 第2碼-金控有無婉卻記錄
	 * 當dataType=4時, 0	, 第1碼-查詢結果
	 */
	public void setDataStatus(String value) {
		this.dataStatus = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
	
	public byte[] getReportFile() {
		return reportFile;
	}
	public void setReportFile(byte[] reportFile) {
		this.reportFile = reportFile;
	}
	
	public String getFileSeq() {
		return fileSeq;
	}
	public void setFileSeq(String fileSeq) {
		this.fileSeq = fileSeq;
	}
	
	public Timestamp getDataCreateTime() {
		return dataCreateTime;
	}
	public void setDataCreateTime(Timestamp dataCreateTime) {
		this.dataCreateTime = dataCreateTime;
	}
	public void setReportFileType(String reportFileType) {
		this.reportFileType = reportFileType;
	}
	public String getReportFileType() {
		return reportFileType;
	}
}
