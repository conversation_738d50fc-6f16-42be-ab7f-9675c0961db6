/* 
 * OnlineDCMain.java
 *
 * IBM Confidential
 * GBS Source Materials
 * 
 * Copyright (c) 2013 IBM Corp. 
 * All Rights Reserved.
 */
package com.mega.eloan.lms.dc.main;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.mega.eloan.lms.dc.action.CombineKeyCTxt;
import com.mega.eloan.lms.dc.action.CreateLoadSQL;
import com.mega.eloan.lms.dc.action.RowData;
import com.mega.eloan.lms.dc.base.DCException;
import com.mega.eloan.lms.dc.conf.ConfigData;
import com.mega.eloan.lms.dc.conf.MainConfig;
import com.mega.eloan.lms.dc.thread.ChkColThread;
import com.mega.eloan.lms.dc.thread.CopyTextThread;
import com.mega.eloan.lms.dc.thread.ExportThread;
import com.mega.eloan.lms.dc.thread.ParserThread;

/**
 * <pre>
 * OnlineDCMain
 * </pre>
 * 
 * @since 2013/3/7
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/3/7,UFO,new
 *          </ul>
 */
public class OnlineDCHandler {
	private static Logger logger = LoggerFactory.getLogger(OnlineDCHandler.class);

	private String mainId = null;
	private ConfigData config = null;

	public static void main(String[] args) {
		try {
			/**
			 * 企金： EL005\EL1LMSB1.NSF;VLMSDB201B; EL005\EL1LMSB1.NSF;VLMS14020;
			 * EL005\EL1LMSB1.NSF;VLMS11020; EL005\EL1LMSB1.NSF;VLMS14011; 個金：
			 * EL004\EL1CLSB1.NSF;VCLS10105Z; EL004\EL1CLSB1.NSF;VCLS10111;
			 * EL004\EL1CLSB1.NSF;VCLS10112; EL004\EL1CLSB1.NSF;VCLS10132A;
			 * EL004\EL1CLSB1.NSF;VCLS10130; EL004\EL1CLSB1.NSF;VCLS00101;
			 * EL004\EL1CLSB1.NSF;VCLS10123; EL004\EL1CLSB1.NSF;VCLS09105;
			 * EL004\EL1CLSB1.NSF;VCLS00113;
			 */

			/*
			 * String nsfName = "EL1LMSB1.NSF"; List<String> viewList = new
			 * ArrayList<String>(); viewList.add("VLMSDB201B");
			 * viewList.add("VLMS14020"); viewList.add("VLMS11020");
			 * viewList.add("VLMS14011"); String mainId = "T05141"; String ip =
			 * "***************"; String schema = "LMS"; String dbType =
			 * "ELOANDB"; String path = "Z01";
			 */

			// String nsfName = args[0];
			// List<String> viewList = new ArrayList<String>();
			// String[] v = args[1].split(";");
			// for (String t : v) {
			// viewList.add(t);
			// }
			// String mainId = args[2];
			// String ip = args[3];
			// String schema = args[4];
			// String dbType = args[5];
			// String path = args[6];
			
			// 個金
			// EL1CLSB1.NSF VCLS10105Z;VCLS10130;VCLS10111;VCLS10112;VCLS00101;VCLS10123;VCLS09105;VCLS00113 T06131 *************** CLS ELOANDB 999
			// 企金
			// EL1LMSB1.NSF VLMSDB201Z;VLMS14020;VLMS11020;VLMS14011 T06111 *************** LMS ELOANDB Z01
			String nsfName = "EL1CLSB1.NSF";
			List<String> viewList = new ArrayList<String>();
			String[] v = "VCLS10105Z;VCLS10130;VCLS10111;VCLS10112;VCLS00101;VCLS10123;VCLS09105;VCLS00113"
					.split(";");
			// String[] v ="VLMSDB201Z".split(";");
			for (String t : v) {
				viewList.add(t);
			}
			String mainId = "T06131";
			String ip = "***************";
			String schema = "CLS";
			String dbType = "ELOANDB";
			String path = "999";

			// EL1LMSB1.NSF VLMSDB201Z;VLMS14020G;VLMS11020G;VLMS14011G T05201
			// *************** LMS ELOANDB 007

			OnlineDCHandler dcHandler = new OnlineDCHandler();
			List<RowData> sqlList = dcHandler.runAll(nsfName, viewList, mainId,
					ip, schema, dbType, path);

			int idx = 0;
			logger.info("PG Debug MSG ====================================================");
			for (RowData sql : sqlList) {
				logger.info("SQL #" + idx + "=" + sql);
				if (sql.isClobTb()) {
					logger.info("SQL #" + idx + "[" + sql.getClobString() + "]");
				}
				idx++;
			}
			logger.info("PG Debug MSG ====================================================");

			dcHandler.saveAttachment();

			dcHandler.cleanUp();

		} catch (Exception ex) {
			logger.error("EXCEPTION!!", ex);
		}
	}

	private void init(String nsfName, List<String> viewList, String mainId,
			String ip, String schema, String dbType, String brNo) {

		this.mainId = mainId;

		if (logger.isDebugEnabled()) {
			StringBuffer tmp = new StringBuffer();
			tmp.append("nsfName=").append(nsfName).append("\n");
			tmp.append("mainId=").append(this.mainId).append("\n");
			tmp.append("ip=").append(ip).append("\n");
			tmp.append("schema=").append(schema).append("\n");
			tmp.append("dbType=").append(dbType).append("\n");
			tmp.append("brNo=").append(brNo).append("\n");
			tmp.append("viewList=")
					.append(ToStringBuilder.reflectionToString(viewList))
					.append("\n");
			logger.debug(tmp.toString());
		}
		// /檢查不可為空
		StringBuffer errSb = new StringBuffer();
		if (StringUtils.isBlank(nsfName)) {
			errSb.append("nsfName不應為空值！");
		}
		if (StringUtils.isBlank(mainId)) {
			errSb.append("mainId不應為空值！");
		}
		if (StringUtils.isBlank(ip)) {
			errSb.append("ip不應為空值！");
		}
		if (StringUtils.isBlank(schema)) {
			errSb.append("schema不應為空值！");
		}
		if (StringUtils.isBlank(dbType)) {
			errSb.append("dbType不應為空值！");
		}

		if (StringUtils.isBlank(brNo)) {
			errSb.append("brNo不應為空值！");
		}
		if (viewList == null || viewList.size() == 0) {
			errSb.append("viewList不應為空值！");
		}
		// 一次檢查所有的參數後再回傳
		if (errSb.length() > 0) {
			throw new DCException(errSb.toString());
		}
		if (mainId.length() > 6) {
			throw new DCException("mainId長度不可超過6碼！此次傳入值為【" + mainId + "】");
		}
		if (schema.equalsIgnoreCase("LMS") && viewList.size() != 4) {
			throw new DCException("企金應有4個View，傳入值卻有" + viewList.size()
					+ "個View");
		} else if (schema.equalsIgnoreCase("CLS") && viewList.size() != 8) {
			throw new DCException("個金應有8個View，傳入值卻有" + viewList.size()
					+ "個View");
		} else if (!schema.equalsIgnoreCase("LMS")
				&& !schema.equalsIgnoreCase("CLS")) {
			throw new DCException("傳入的schema不正確，應為LMS/CLS之一，而非" + schema);
		}
		MainConfig.getInstance().setOnlineMode(true);
		config = MainConfig.getInstance().getConfig();
		config.setOnlineData(ip, mainId, schema, viewList);
		logger.info("LMSView = " + config.getLMSViewName());
	}

	/**
	 * @param nsfName
	 *            NSF名稱
	 * @param viewList
	 *            ViewName的List
	 * @param mainId
	 *            代入config.properties中的TODAY
	 * @param ip
	 *            NSF所在位置IP，代入config.properties中的dominoServerIp
	 * @param schema
	 *            連結的 schema or 目前執行的系統名稱 Ex:LMS or CLS
	 * @param dbType
	 *            連結的DB名稱 Ex:DELOANDB
	 * @param brNo
	 *            分行代號
	 * @return List<String>:已組成SQL語法之文字
	 * @throws DCException
	 *             {@link }com.mega.eloan.lms.dc.base.DCException
	 */
	public synchronized List<RowData> runAll(String nsfName,
			List<String> viewList, String mainId, String ip, String schema,
			String dbType, String brNo) throws DCException {

		this.init(nsfName, viewList, mainId, ip, schema, dbType, brNo);

		List<RowData> sqlList = null;

		String viewFilename = null;
		try {

			logger.info("初始化BaseAction,創建主要目錄及設定主要資訊...");
			viewFilename = genViewListFile(nsfName, viewList, schema, brNo);
			// 執行DXLExport
			logger.info("\n\n 開始執行輸出.dxl檔...\n ");
			new ExportThread(schema, viewFilename).export(config);

			// 執行DXLParser
			logger.info("\n\n 開始執行 DXLparser...\n ");
			new ParserThread(schema, viewFilename).parser(config);

			logger.info("\n\n 開始執行合併L120M01C檔案...\n ");
			new CombineKeyCTxt(config, schema);

			// 執行CopyAllTextFile
			logger.info("\n\n 開始匯集各分行的TEXT檔至load_db2目錄下...\n ");
			new CopyTextThread(schema).copyAllText(config);

			// 執行ColumnTruncate
			logger.info("\n\n 開始執行DB欄位與資料長度格式之檢核...\n ");
			new ChkColThread(dbType, schema, "db2").chkColumn(config);

			// 執行CreateLoadSQL
			logger.info("\n\n 開始建立SQL指令...\n ");
			CreateLoadSQL genSqlCreator = new CreateLoadSQL();
			genSqlCreator.setConfigData(config);
			sqlList = genSqlCreator.genSql(dbType, schema);
			logger.info("\\###########程式結束：產生SQL共計" + sqlList.size() + "筆");
			return sqlList;
		} catch (Exception ex) {
			throw new DCException(ex);
		} finally {
			if (viewFilename != null) {
				// 清檔
				try {
					FileUtils.forceDelete(new File(viewFilename));
					logger.debug("檔案刪除成功！viewListFN=" + viewFilename);
				} catch (IOException ex) {
					logger.error("FileUtils.forceDelete EXCEPTION!!", ex);
				}
			}
		}
	}

	public void saveAttachment() {

	}

	public void cleanUp() {

	}

	/**
	 * 產生ViewList檔案的內容
	 * 
	 * @param nsfName
	 *            NSF名稱
	 * @param viewList
	 *            ViewName的List
	 * @param schema
	 *            連結的 schema or 目前執行的系統名稱
	 * @param brNo
	 *            分行代號
	 * @return ViewList的檔名
	 * @throws Exception
	 */
	private String genViewListFile(String nsfName, List<String> viewList,
			String schema, String brNo) throws Exception {

		checkViewList(viewList);

		ConfigData conf = MainConfig.getInstance().getConfig();
		String viewListFN = conf.getDC_ONLINE_FILE_ROOT() + File.separator
				+ conf.getHomeName() + File.separator + schema + "ViewList_"
				+ System.nanoTime() + "." + conf.getViewListExt();

		StringBuffer content = new StringBuffer();
		for (String view : viewList) {
			content.append("EL").append(brNo).append("\\").append(nsfName)
					.append(";").append(view).append(";").append("\n");
		}

		FileUtils.write(new File(viewListFN), content.toString(), "UTF-8");
		logger.debug("檔案建立成功！viewListFN=" + viewListFN + "\n"
				+ content.toString());

		return viewListFN;

	}

	/**
	 * 檢查ViewList的值
	 * 
	 * @param viewList
	 */
	private void checkViewList(List<String> viewList) {

	}

}
