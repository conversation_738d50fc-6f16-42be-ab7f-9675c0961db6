/* 
 * LMS9541GridHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.handler.grid;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.lms.model.L810M01A;
import com.mega.eloan.lms.rpt.pages.LMS9541V02Page;
import com.mega.eloan.lms.rpt.service.LMS9541V02Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 優惠房貸報表 - 統計表
 * </pre>
 * 
 * @since 2012/12/06
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/06,Vector,new
 *          </ul>
 */
@Scope("request")
@Controller("lms9541v02gridhandler")
public class LMS9541V02GridHandler extends AbstractGridHandler {

	@Resource
	LMS9541V02Service service;

	@Resource
	BranchService branch;

	final int BASE = 10000;// 輸出金額單位：萬元

	final String[] colNames = { "oid", "useType", "rptType", "rptName",
			"endDate", "creator", "createTime", "updater", "updateTime" };

	/**
	 * 查詢Grid 資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */

	@SuppressWarnings({ "rawtypes", "unchecked" })
	public CapMapGridResult query(ISearch pageSetting, PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "brno",
				user.getUnitNo());
		pageSetting.addSearchModeParameters(SearchMode.LESS_THAN, "useType",
				"4");
		Page page = service.findPage(L810M01A.class, pageSetting);
		List<Map<String, Object>> result = new LinkedList<Map<String, Object>>();
		List<L810M01A> data = page.getContent();
		if (data != null) {
			Properties pop = MessageBundleScriptCreator
					.getComponentResource(LMS9541V02Page.class);
			for (int i = 0; i < data.size(); i++) {
				L810M01A pivot = data.get(i);
				Map<String, Object> record = new HashMap<String, Object>();
				String useTypeName = "";
				switch (Util.parseInt(pivot.getUseType())) {
				case 1:
					useTypeName = pop.getProperty("byBranch");
					break;
				case 2:
					useTypeName = pop.getProperty("byCounty");
					break;
				case 3:
					useTypeName = pop.getProperty("number");
				}
				record.put("useTypeName", useTypeName);
				for (int x = 0; x < colNames.length; x++) {
					record.put(colNames[x], pivot.get(colNames[x]));
				}
				result.add(record);
			}
		}
		return new CapMapGridResult(result, result.size());
	}

	/**
	 * 調閱資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public CapMapGridResult queryCityGrid(ISearch pageSetting,
			PageParameters params) throws CapException {
		List<Map<String, Object>> data = service.getCityData(params
				.getString("type"));
		// 解決不會換頁問題...
		int page = Util.parseInt(params.getString("gridPage")) - 1, cols = Util
				.parseInt(params.getString("rows"));
		int begin = page * cols, end = (page + 1) * cols;
		end = end > data.size() ? data.size() : end;
		List<Map<String, Object>> result = data;
		if (begin <= end) {
			result = data.subList(begin, end);
		}
		return new CapMapGridResult(result, data.size());
	}

	/**
	 * 調閱資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public CapMapGridResult queryBrnoGrid(ISearch pageSetting,
			PageParameters params) throws CapException {
		List<Map<String, Object>> data = service.getBrnoData(
				params.getString("type"), params.getString("endDate"));
		// 解決不會換頁問題...
		int page = Util.parseInt(params.getString("gridPage")) - 1, cols = Util
				.parseInt(params.getString("rows"));
		int begin = page * cols, end = (page + 1) * cols;
		end = end > data.size() ? data.size() : end;
		List<Map<String, Object>> result = data;
		if (begin <= end) {
			result = data.subList(begin, end);
		}
		return new CapMapGridResult(result, data.size());
	}
}
