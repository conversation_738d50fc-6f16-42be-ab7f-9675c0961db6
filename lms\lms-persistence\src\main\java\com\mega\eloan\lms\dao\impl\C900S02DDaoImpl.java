package com.mega.eloan.lms.dao.impl;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.lms.dao.C900S02DDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C900S02D;

/** 總處特殊控管額度資料 **/
@Repository
public class C900S02DDaoImpl extends LMSJpaDao<C900S02D, String>
	implements C900S02DDao {

	@Override
	public C900S02D findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public C900S02D findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		return findUniqueOrNone(search);
	}
	
	@Override
	public C900S02D findApprovedCategoryCntrNo(String category, String cntrNo){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "category", category);
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime","");
		search.addSearchModeParameters(SearchMode.EQUALS, EloanConstants.DOC_STATUS, FlowDocStatusEnum.已核准.getCode());
		return findUniqueOrNone(search);
	}	
}