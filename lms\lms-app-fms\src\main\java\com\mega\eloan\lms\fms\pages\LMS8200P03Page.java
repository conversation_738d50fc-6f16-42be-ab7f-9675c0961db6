package com.mega.eloan.lms.fms.pages;

import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.pages.AbstractOutputPage;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.RPAProcessService;
import com.mega.eloan.lms.fms.panels.LMS820M01S5Panel;
import com.mega.eloan.lms.fms.service.LMS8200Service;
import com.mega.eloan.lms.model.C120S04W;
import com.mega.eloan.lms.model.L820M01S;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.util.CapString;

/**
 * <pre>
 * 一件列印HTML
 * </pre>
 */
@Controller@RequestMapping(path = "/fms/lms8200p03")
public class LMS8200P03Page extends AbstractOutputPage {
	@Autowired
	LMS8200Service lms8200Service;

	@Autowired
	CLSService clsService;
	
	@Autowired
	LMSService lmsService;

	@Autowired
	CodeTypeService codeTypeService;
	
	@Autowired
	RPAProcessService rpaProcessService;

	private static final long serialVersionUID = 1L;

	private static final String PAGE_BREAK_DIV = "<div class=\"pageBreak\">&nbsp;</div>";

	public LMS8200P03Page() {
		super();
	}

	// UPGRADE: 後續沒有用到就可以刪掉
//	@Override
//	protected void setHeaders(WebResponse response) {
//		if (true) {
//			response.setHeader("X-UA-Compatible", "IE=11");
//		}
//	}

	@Override
	public String getOutputString(ModelMap model, PageParameters params) {
		setNeedHtml(true); // need html

		setJavascript(new String[] { "pagejs/fms/LMS8200P03Page.js" });

		// 設定隱藏欄位
		String[] hideInput = { EloanConstants.MAIN_ID, "custId", "dupNo",
				"queryType", "dataType"};
		
		// UPGRADE: 待確認以下升級是否正確
//		for (String id : hideInput) {
//			String value = CapString.trimNull((params.getString(id)));
//			TextField<?> textField = (TextField<?>) new TextField<Object>(id)
//					.add(new SimpleAttributeModifier("id", id))
//					.add(new SimpleAttributeModifier("name", id))
//					.add(new SimpleAttributeModifier("type", "text"))
//					.add(new SimpleAttributeModifier("value", value));
//			add(textField);
//		}
		List<Map<String, String>> inputFields = new ArrayList<>();
		for (String id : hideInput) {
		    String value = CapString.trimNull(params.getString(id));
		    Map<String, String> field = new HashMap<>();
		    field.put("id", id);
		    field.put("name", id);
		    field.put("type", "text");
		    field.put("value", value);
		    inputFields.add(field);
		}
		model.addAttribute("inputFields", inputFields);

		// 沒有傳入dataType:印全部
		String dataType = (params.getString("dataType"));
		if (dataType == null) {
			StringBuilder sb = new StringBuilder();
			//sb.append(this.getP01HTMLOutputString(params));
			//sb.append(this.getP02HTMLOutputString(params));
			String result = sb.append("&nbsp").toString().replaceFirst(PAGE_BREAK_DIV, "");
			// 若產出頁面有多餘空白字元長度資料則一鍵列印原PDF檔加入換頁
			if (result.length() > "&nbsp".length()) {
				params.put("addPageBreak", true);
			} else {
				params.put("addPageBreak", false);
			}
			printAll(params, model);
			return result;
		} else {
			// 單一HTML不加換頁
			params.put("addPageBreak", false);
			printOne(params, model);
		}

		return "&nbsp;";
	}

	/**
	 * RPS原PDF檔資料轉HTML呈現
	 * 
	 * @return
	 */
	private Set<String> getPanelIdSet() {
		Set<String> panelIdSet = new HashSet<String>();
		//panelIdSet.add("CLS101S01S1");// 往來客戶信用異常資料
		//panelIdSet.add("CLS101S01S2");// 客戶是否為利害關係人資料
		//panelIdSet.add("CLS101S01S3");// 婉卻紀錄資料
		//panelIdSet.add("CLS101S01S4");// 證券違約交割資料
		panelIdSet.add("LMS820M01S5");// 內政部國民身分證領換補資料
		//panelIdSet.add("CLS101S01S6");// RPA受監護輔助宣告查詢
		return panelIdSet;
	}

	/**
	 * 一鍵列印原PDF檔
	 */
	private void printAll(PageParameters params, ModelMap model) {
		params.put("printAll", true);
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String custId = params.getString("custId");
		String dupNo = params.getString("dupNo");

		Set<String> panelIdSet = getPanelIdSet();
		try {
			List<L820M01S> m01s_list = new ArrayList<L820M01S>();
			m01s_list.addAll(lms8200Service.findL820M01S_byIdDupDataType(mainId, custId, dupNo, "5"));// 行內_身分證驗證
			for (L820M01S l820m01s : m01s_list) {
				String dataType = l820m01s.getDataType();
				if ("J".equals(l820m01s.getReportFileType())) {
					addPanel(dataType, params, panelIdSet, model);
				}
			}

			C120S04W c120s04w = this.rpaProcessService.getC120S04WBy(mainId,
					custId);
			if (null != c120s04w && "A02".equals(c120s04w.getStatus())) {
				addPanel("6", params,panelIdSet, model);//RPA受監護輔助宣告查詢
			}
			
			// 未產生panel的地方放空Panel
			for (String id : panelIdSet) {
				// UPGRADE: 前端須配合改Thymeleaf的樣式
				// add(new EmptyPanel(id));
				model.addAttribute("panelId", id);

			}
		} catch (SecurityException e) {
			e.printStackTrace();
		} catch (IllegalArgumentException e) {
			e.printStackTrace();
		}
	}

	/**
	 * 單一列印原PDF檔
	 * 
	 * @throws ClassNotFoundException
	 * @throws NoSuchMethodException
	 * @throws SecurityException
	 * @throws InvocationTargetException
	 * @throws IllegalAccessException
	 * @throws InstantiationException
	 * @throws IllegalArgumentException
	 */
	private void printOne(PageParameters params, ModelMap model) {
		String dataType = params.getString("dataType");
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String custId = params.getString("custId");
		String dupNo = params.getString("dupNo");
		
		Set<String> panelIdSet = getPanelIdSet();
		List<? extends GenericBean> list = new ArrayList<GenericBean>();
		try {
			list = lms8200Service.findL820M01S_byIdDupDataType(mainId, custId, dupNo, dataType);

			if (list != null && list.size() > 0) {
				String reportFileType = ((L820M01S) list.get(0)).getReportFileType();
				if ("J".equals(reportFileType)) {
					addPanel(dataType, params, panelIdSet, model);
				}
			}
			for (String id : panelIdSet) {
				// UPGRADE: 前端須配合改Thymeleaf的樣式
				// add(new EmptyPanel(id));
				model.addAttribute("panelId", id);
			}
		} catch (SecurityException e) {
			e.printStackTrace();
		} catch (IllegalArgumentException e) {
			e.printStackTrace();
		}
	}

	/**
	 * 依panelIdSet內的id設定panel，設定完成後移除Set內id
	 * 
	 * @param dataType
	 * @param params
	 * @param panelIdSet
	 */
	private void addPanel(String dataType, PageParameters params,
			Set<String> panelIdSet, ModelMap model) {
		boolean printAll = params.getAsBoolean("printAll", false);
		// 印全部時，判斷非第一個產生的頁面時，加入換頁
		if (printAll == true && panelIdSet.size() < 4) {
			params.put("addPageBreak", true);
		}
		String id = "LMS820M01S" + dataType;
		Panel panel = null;
		if ("5".equals(dataType) && panelIdSet.contains(id)) {//行內_身分證驗證
			panel = new LMS820M01S5Panel(id, params);
		} else if ("6".equals(dataType) && panelIdSet.contains(id)) {//RPA受監護輔助宣告查詢
			//panel = new CLS101S01S6Panel(id, params);
		}
		else {
			// UPGRADE: 前端須配合改Thymeleaf的樣式
			// panel = new EmptyPanel(id);
			model.addAttribute("panelId", id);
		}
		panel.processPanelData(model, params);
		// 加入後set移除
		panelIdSet.remove(id);
	}

	@Override
	protected String getViewName() {
		// TODO Auto-generated method stub
		return null;
	}


}
