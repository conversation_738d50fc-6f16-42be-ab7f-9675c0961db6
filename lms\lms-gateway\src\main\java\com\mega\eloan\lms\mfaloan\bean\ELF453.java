/* 
 * ELF453.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.mfaloan.bean;


import javax.persistence.*;

import tw.com.iisi.cap.model.GenericBean;


/** ACH檔 **/

public class ELF453 extends GenericBean{

	private static final long serialVersionUID = 1L;

	/** 交易代號 **/
	@Column(name="ELF453_TIX", length=3, columnDefinition="CHAR(3)")
	private String elf453_tix;

	/** 發動者統一編號 **/
	@Column(name="ELF453_CID", length=10, columnDefinition="CHAR(10)")
	private String elf453_cid;

	/** 收受行代號 **/
	@Column(name="ELF453_RBANK", length=7, columnDefinition="CHAR(7)")
	private String elf453_rbank;

	/** 委繳戶帳號 **/
	@Column(name="ELF453_RCLNO", length=16, columnDefinition="CHAR(16)")
	private String elf453_rclno;

	/** 委繳戶統一編號 **/
	@Column(name="ELF453_RID", length=10, columnDefinition="CHAR(10)")
	private String elf453_rid;

	/** 用戶號碼 **/
	@Column(name="ELF453_USERNO", length=20, columnDefinition="CHAR(20)",unique = true)
	private String elf453_userno;

	/** 新增或取消 **/
	@Column(name="ELF453_ADMARK", length=1, columnDefinition="CHAR(1)")
	private String elf453_admark;

	/** 日期 **/
	@Column(name="ELF453_DATE", length=8, columnDefinition="CHAR(8)")
	private String elf453_date;

	/** 發動行代號 **/
	@Column(name="ELF453_PBANK", length=7, columnDefinition="CHAR(7)")
	private String elf453_pbank;

	/** 發動者專用區 **/
	@Column(name="ELF453_NOTE", length=20, columnDefinition="CHAR(20)")
	private String elf453_note;

	/** 交易型態 **/
	@Column(name="ELF453_TYPE", length=1, columnDefinition="CHAR(1)")
	private String elf453_type;

	/** 回覆訊息 **/
	@Column(name="ELF453_RCODE", length=1, columnDefinition="CHAR(1)")
	private String elf453_rcode;

	/** 備用 **/
	@Column(name="ELF453_FILLER", length=12, columnDefinition="CHAR(12)")
	private String elf453_filler;

	/** 消金案號 **/
	@Column(name="ELF453_PRJNO", length=14, columnDefinition="CHAR(14)")
	private String elf453_prjno;
	
	/** 進帳行代號 **/
	@Column(name="ELF453_IBANK", length=7, columnDefinition="CHAR(7)")
	private String elf453_ibank;

	/** 進帳戶帳號 **/
	@Column(name="ELF453_ICLNO", length=16, columnDefinition="CHAR(16)")
	private String elf453_iclno;

	/** 進帳戶統一編號 **/
	@Column(name="ELF453_IID", length=10, columnDefinition="CHAR(10)")
	private String elf453_iid;

	/** 取得交易代號 **/
	public String getElf453_tix() {
		return this.elf453_tix;
	}
	/** 設定交易代號 **/
	public void setElf453_tix(String value) {
		this.elf453_tix = value;
	}

	/** 取得發動者統一編號 **/
	public String getElf453_cid() {
		return this.elf453_cid;
	}
	/** 設定發動者統一編號 **/
	public void setElf453_cid(String value) {
		this.elf453_cid = value;
	}

	/** 取得收受行代號 **/
	public String getElf453_rbank() {
		return this.elf453_rbank;
	}
	/** 設定收受行代號 **/
	public void setElf453_rbank(String value) {
		this.elf453_rbank = value;
	}

	/** 取得委繳戶帳號 **/
	public String getElf453_rclno() {
		return this.elf453_rclno;
	}
	/** 設定委繳戶帳號 **/
	public void setElf453_rclno(String value) {
		this.elf453_rclno = value;
	}

	/** 取得委繳戶統一編號 **/
	public String getElf453_rid() {
		return this.elf453_rid;
	}
	/** 設定委繳戶統一編號 **/
	public void setElf453_rid(String value) {
		this.elf453_rid = value;
	}

	/** 取得用戶號碼 **/
	public String getElf453_userno() {
		return this.elf453_userno;
	}
	/** 設定用戶號碼 **/
	public void setElf453_userno(String value) {
		this.elf453_userno = value;
	}

	/** 取得新增或取消 **/
	public String getElf453_admark() {
		return this.elf453_admark;
	}
	/** 設定新增或取消 **/
	public void setElf453_admark(String value) {
		this.elf453_admark = value;
	}

	/** 取得日期 **/
	public String getElf453_date() {
		return this.elf453_date;
	}
	/** 設定日期 **/
	public void setElf453_date(String value) {
		this.elf453_date = value;
	}

	/** 取得發動行代號 **/
	public String getElf453_pbank() {
		return this.elf453_pbank;
	}
	/** 設定發動行代號 **/
	public void setElf453_pbank(String value) {
		this.elf453_pbank = value;
	}

	/** 取得發動者專用區 **/
	public String getElf453_note() {
		return this.elf453_note;
	}
	/** 設定發動者專用區 **/
	public void setElf453_note(String value) {
		this.elf453_note = value;
	}

	/** 取得交易型態 **/
	public String getElf453_type() {
		return this.elf453_type;
	}
	/** 設定交易型態 **/
	public void setElf453_type(String value) {
		this.elf453_type = value;
	}

	/** 取得回覆訊息 **/
	public String getElf453_rcode() {
		return this.elf453_rcode;
	}
	/** 設定回覆訊息 **/
	public void setElf453_rcode(String value) {
		this.elf453_rcode = value;
	}

	/** 取得備用 **/
	public String getElf453_filler() {
		return this.elf453_filler;
	}
	/** 設定備用 **/
	public void setElf453_filler(String value) {
		this.elf453_filler = value;
	}

	/** 取得消金案號 **/
	public String getElf453_prjno() {
		return this.elf453_prjno;
	}
	/** 設定消金案號 **/
	public void setElf453_prjno(String value) {
		this.elf453_prjno = value;
	}
	
	/** 取得進帳行代號 **/
	public String getElf453_ibank() {
		return elf453_ibank;
	}
	/** 設定進帳行代號 **/
	public void setElf453_ibank(String elf453_ibank) {
		this.elf453_ibank = elf453_ibank;
	}
	
	/** 取得進帳戶帳號 **/
	public String getElf453_iclno() {
		return elf453_iclno;
	}
	/** 設定進帳戶帳號 **/
	public void setElf453_iclno(String elf453_iclno) {
		this.elf453_iclno = elf453_iclno;
	}
	
	/** 取得進帳戶統一編號**/
	public String getElf453_iid() {
		return elf453_iid;
	}
	/** 設定進帳戶統一編號 **/
	public void setElf453_iid(String elf453_iid) {
		this.elf453_iid = elf453_iid;
	}
	
}
