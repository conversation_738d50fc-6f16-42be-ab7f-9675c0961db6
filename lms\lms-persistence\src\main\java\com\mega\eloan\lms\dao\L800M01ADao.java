/* 
 * L800M01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L800M01A;

/** 常用主管資料檔 **/
public interface L800M01ADao extends IGenericDao<L800M01A> {

	L800M01A findByOid(String oid);
	
	List<L800M01A> findByMainId(String mainId);
	
	L800M01A findByUniqueKey(String brno, String zhuGuan);

	List<L800M01A> findByIndex01(String brno, String zhuGuan);

	List<L800M01A> findByBrno();
}