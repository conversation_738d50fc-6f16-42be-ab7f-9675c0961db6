/*
 * NumericFormatter.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
 */
package tw.com.iisi.cap.formatter;

import java.math.BigDecimal;
import java.text.DecimalFormat;

import tw.com.iisi.cap.exception.CapFormatException;
import tw.com.iisi.cap.util.CapMath;

/**
 * <pre>
 * 數字字串的formatter
 * </pre>
 * 
 * @since 2010/11/24
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2010/11/24,iristu,new
 *          <li>2011/9/06,tammychen, handle BigDecimal
 *          </ul>
 */
@SuppressWarnings("serial")
public class NumericFormatter implements IFormatter {

    private final String pattern;

    /**
     * 設置數字的預設格式 <br>
     * {@code "###,##0"}
     */
    public NumericFormatter() {
        this.pattern = "###,##0";
    }

    /**
     * 設置數字的格式
     * 
     * @param pattern
     */
    public NumericFormatter(String pattern) {
        this.pattern = pattern;
    }

    /**
     * <pre>
     * 數字的Formatter
     * </pre>
     * 
     * @param in
     *            input
     * @return String
     * @throws CapFormatException
     */
    @SuppressWarnings("unchecked")
    @Override
    public String reformat(Object in) throws CapFormatException {
        DecimalFormat _nf = new DecimalFormat(pattern);
        BigDecimal dec = in instanceof BigDecimal ? (BigDecimal) in : CapMath.getBigDecimal((String) in);
        return _nf.format(dec);
    }

}
