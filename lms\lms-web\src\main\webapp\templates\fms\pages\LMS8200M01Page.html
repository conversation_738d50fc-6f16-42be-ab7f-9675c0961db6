<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:wicket="http://wicket.apache.org/">
   <body>
      <wicket:extend>
         <script type="text/javascript" src="pagejs/fms/LMS8200M01Page.js?r=20250107"></script>
         <div class="button-menu funcContainer" id="buttonPanel">
            <!--編製中 -->
            <wicket:enclosure>
               <span wicket:id="_btnDOC_EDITING" />
               <button id="btnSave">
                  <span class="ui-icon ui-icon-jcs-04" />
                  <wicket:message key="button.save">
                     <!--儲存-->
                  </wicket:message>
               </button>
               <button id="btnSend" >
                  <span class="ui-icon ui-icon-jcs-02" />
                  <wicket:message key="button.send" >
                     <!--呈主管覆核-->
                  </wicket:message>
               </button>
            </wicket:enclosure>
            <!--待覆核 -->
            <wicket:enclosure>
               <span wicket:id="_btnWAIT_APPROVE" />
               <button id="btnCheck" >
                  <span class="ui-icon ui-icon-jcs-106" />
                  <wicket:message key="button.check" >
                     <!--覆核-->
                  </wicket:message>
               </button>
            </wicket:enclosure>
            <button id="btnExit"  class="forview">
               <span class="ui-icon ui-icon-jcs-01"></span>
               <wicket:message key="button.exit">
                  <!--離開-->
               </wicket:message>
            </button>
         </div>
         <div class="tit2 color-black">
            <wicket:message key="title">
               <!-- 以房養老案件查詢結果-->
            </wicket:message>
            - 
            <!--(<span id="showTypCd" class="text-red"/>)--><span id="showCustId" class="color-blue" />
            <span id="custId" style="display:none"/>
            <span id="dupNo" style="display:none"/>
         </div>
         <div class="tit2 color-black">
            <!--<wicket:message key="cntrNo">-->
               <!-- 額度序號-->
            <!--</wicket:message>-->
           <!-- ：-->
            <span id="cntrNo" class="color-blue" style="display:none"/>
         </div>
         <!--J-111-0525_11850_B1001 Web e-Loan以房養老貸款撥款前查詢結果相關欄位-->
         <div class="tabs">
            <ul>
               <li>
                  <a href="#tab-1_1"><b><wicket:message key="L820M01A.title01">基本資訊</wicket:message></b></a>
               </li>
               <li id="tab2" style="float:left;">
                  <a href="#tab-1_2"><b><wicket:message key="L820M01A.title02">以房養老案件查詢結果維護</wicket:message></b></a>
               </li>
            </ul>
            <div class="tabCtx-warp">
	            <div id="tab-1_1"> 
	               <div id="docPanel">
	               	 <form id="mainPanel">
		             	<fieldset>
		                     <legend>
		                        <b>基本資訊</b>
		                     </legend>
		                     <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
		                        <tbody>
		                           <tr>
		                              <td width="25%" class="hd1" rowspan="3">
		                                 <span>維護項目&nbsp;&nbsp;</span>
		                              </td>
		                              <td width="35%">	                                 										 
										 <label><wicket:message key="L820M01A.ejType01">內政部戶政系統-身分證補換發查詢</wicket:message></label>                                
		                              </td>
									  <td width="40%">
										  <label style="font-weight: normal; letter-spacing:normal; padding:0px;">
										  	<input type="radio" name="ej_ST_ID_CARD_CHECK" id="id_ej_ST_ID_CARD_CHECK_1" value="Y" disabled><wicket:message key="L820M01A.status01">通過</wicket:message>
										  </label>
										  <label style="font-weight: normal; letter-spacing:normal; padding:0px;">
											<input type="radio" name="ej_ST_ID_CARD_CHECK" id="id_ej_ST_ID_CARD_CHECK_2" value="N" disabled><wicket:message key="L820M01A.status02">未通過</wicket:message>
										  </label>
										  <label style="font-weight: normal; letter-spacing:normal; padding:0px;">
											<input type="radio" name="ej_ST_ID_CARD_CHECK" id="id_ej_ST_ID_CARD_CHECK_3" value="I" disabled><wicket:message key="L820M01A.status03">查詢中</wicket:message>
										  </label>
									  </td>
		                           </tr>
								   <tr>
										<td width="25%">	
											<label><wicket:message key="L820M01A.ejType02">司法院家事查詢</wicket:message></label>
										</td>
										<td width="40%">
											<label style="font-weight: normal; letter-spacing:normal; padding:0px;">
										 		<input type="radio" name="ej_ST_FA_QUERY" id="id_ej_ST_FA_QUERY_1" value="Y" disabled><wicket:message key="L820M01A.status01">通過</wicket:message>
									  	 	</label>
										 	<label style="font-weight: normal; letter-spacing:normal; padding:0px;">
										 		<input type="radio" name="ej_ST_FA_QUERY" id="id_ej_ST_FA_QUERY_2" value="N" disabled><wicket:message key="L820M01A.status02">未通過</wicket:message>
									  	 	</label>
										 	<label style="font-weight: normal; letter-spacing:normal; padding:0px;">
										 		<input type="radio" name="ej_ST_FA_QUERY" id="id_ej_ST_FA_QUERY_3" value="I" disabled><wicket:message key="L820M01A.status03">查詢中</wicket:message>
									  	 	</label> 
										</td>
								   </tr>
								   <tr>
										<td width="25%">	
											<label><wicket:message key="L820M01A.ejType03">財富管理往來查詢</wicket:message></label>
										</td>
										<td width="40%">
											<label style="font-weight: normal; letter-spacing:normal; padding:0px;">
										 		<input type="radio" name="ej_ST_C350_QUERY" id="id_ej_ST_C350_QUERY_1" value="Y" disabled><wicket:message key="L820M01A.status01">通過</wicket:message>
									  	 	</label>
										 	<label style="font-weight: normal; letter-spacing:normal; padding:0px;">
										 		<input type="radio" name="ej_ST_C350_QUERY" id="id_ej_ST_C350_QUERY_2" value="N" disabled><wicket:message key="L820M01A.status02">未通過</wicket:message>
									  	 	</label>
										 	<label style="font-weight: normal; letter-spacing:normal; padding:0px;">
										 		<input type="radio" name="ej_ST_C350_QUERY" id="id_ej_ST_C350_QUERY_3" value="I" disabled><wicket:message key="L820M01A.status03">查詢中</wicket:message>
									  	 	</label> 
										</td>
								   </tr>
		                        </tbody>
		                     </table>
		                  </fieldset>
						  <fieldset>
						  	<legend>
		                        <b>
		                           <wicket:message key="L820M01C.caseInf">
		                              <!-- 撥款資訊 -->
		                           </wicket:message>
		                        </b>
		                     </legend>
							 <div style="margin-top:5px;" id="dataArchivalRecordData">
	                         	<div id="caseinf_gridview"/>
							 </div>
						  </fieldset>
						  <fieldset>
						  	<legend>
						  		<b><wicket:message key="L820M01A.sign">批示</wicket:message>&<wicket:message key="L820M01A.comm">審核意見</wicket:message></b>
							</legend>
							<table class="tb2" width="100%">
								<tr>
									<td width="50%" class="hd2" align="center"><wicket:message key="L820M01A.sign">批示</wicket:message></td>
									<td width="50%" class="hd2" align="center">
										<wicket:message key="L820M01A.comm">審核意見</wicket:message>
										<button type="button" id="btnPhrase" >
											<span class="text-only"><wicket:message key="button.Phrase" >片語</wicket:message></span>
										</button>
									</td>
								</tr>
								<tr>
									<td align="center" ><textarea id="sign" name="sign" cols="50" rows="7" maxlength="3072" maxlengthC="1024"></textarea></td>
									<td align="center" ><textarea id="comm" name="comm" cols="50" rows="7" maxlength="3072" maxlengthC="1024"></textarea></td>
								</tr>
							</table>
						  </fieldset>
		                  <fieldset>
		                     <legend>
		                        <b>
		                           <wicket:message key="doc.docUpdateLog">
		                              <!-- 文件異動紀錄 -->
		                           </wicket:message>
		                        </b>
		                     </legend>
		                     <div class="funcContainer">
		                        <div class="funcContainer">
		                           <!-- 文件異動紀錄--> 
		                           <div wicket:id="_docLog" /></div>
		                        </div>
		                        <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
		                           <tbody>
		                              <tr>
		                                 <td width="35%" class="hd1">
		                                    <wicket:message key="doc.creator">
		                                       <!--  文件建立者-->
		                                    </wicket:message>
		                                    &nbsp;&nbsp;
		                                 </td>
		                                 <td width="15%">
		                                    <span id='creator'/>(<span id='createTime'/>)
		                                 </td>
		                                 <td width="30%" class="hd1">
		                                    <wicket:message key="doc.lastUpdater">
		                                       <!--  最後異動者-->
		                                    </wicket:message>
		                                    &nbsp;&nbsp;
		                                 </td>
		                                 <td width="20%">
		                                    <span id='updater'/>(<span id='updateTime'/>)
		                                 </td>
		                              </tr>
		                              <tr>
		                                 <td class="hd1">
		                                 </td>
		                                 <td>
		                                 </td>
		                                 <td class="hd1">
		                                    <wicket:message key="doc.docCode">
		                                       <!--文件亂碼-->
		                                    </wicket:message>
		                                    &nbsp;&nbsp;
		                                 </td>
		                                 <td>
		                                    <span id="randomCode" />
		                                 </td>
		                              </tr>
		                           </tbody>
		                        </table>
		                  </fieldset>
		                  <fieldset>
			                  <div id="tabs-appr" class="tabs" style='width:99%;'>
				                  <ul>
				                  <li>
				                  <a href="#tabs-appr01"><b><wicket:message key="L820M01B.title01"></wicket:message></b></a>
				                  </li>
				                  </ul>
				                  <div class="tabCtx-warp">
				                  <div id="tabs-appr01" class="content">
				                  <table width="100%">
				                  <tr>
				                  <td width="12%" class="rt">
				                  <b class="text-red">
				                  <wicket:message key="L820M01B.managerId"><!--經副襄理--></wicket:message>：
				                  </b>
				                  </td>
				                  <td width="12%" class="lt">
				                  <span id="managerId" />
				                  </td>
				                  <td width="12%" class="rt">
				                  <b class="text-red">
				                  <wicket:message key="L820M01B.bossId"><!-- 授信主管--></wicket:message>：
				                  </b>
				                  </td>
				                  <td width="12%" class="lt">
				                  <span id="bossId" />
				                  </td>
				                  <td width="12%" class="rt">
				                  <b class="text-red">
				                  <wicket:message key="L820M01B.reCheckId"><!--覆核主管--></wicket:message>：
				                  </b>
				                  </td>
				                  <td width="12%" class="lt">
				                  <span id="reCheckId"/>
				                  </td>
				                  <td width="12%" class="rt">
				                  <b class="text-red">
				                  <wicket:message key="L820M01B.apprId"><!--  經辦--></wicket:message>：
				                  </b>
				                  </td>
				                  <td width="12%" class="lt">
				                  <span id="showApprId"/>
				                  </td>
				                  </tr>
				                  </table>
				                  </div>
				                  </div>
		                  	</div>				   
		                  </fieldset>
					  </form>
	                  </div>
	               </div>
				<div id="tab-1_2">
            	<form id="L820M01Form" name="L820M01Form" >
	          		<fieldset>
	                     <legend>
	                        <strong>以房養老案件查詢結果維護</strong>
	                     </legend>
	                     <span id="exItem" style="display:none"/>
	                     <span id="exItemOn" style="display:none"/>
	                     <table border='0'>
	                        <tr style='vertical-align:top'>
	                           <td style='border:0; padding-right:30px;'>
	                              <button type="button" id="btnOnQuery" name="btnOnQuery"  class="btnSendOneButtonQuery "><span class="text-only">一鍵查詢</span></button>
	                              <span class='color-red'>
	                                 <wicket:message key="L820M01S.P7_btn_onquery_memo">(點選即一鍵查詢下列資料)</wicket:message>
	                              </span>
	                           </td>
	                        </tr>
	                     </table>
	                     <table border='1' width="100%">
	                        <tr>
	                           <td style='background-color:#D6EAF8; width:33%;'>內政部戶政司網站
	                           </td>
	                           <td style='background-color:#AED6F1; width:33%;'>司法院網站
	                           </td>
							   <td style='background-color:#AED6F1; width:33%;'>財富管理往來查詢(資料倉儲)
	                           </td>
	                        </tr>
	                        <tr style='vertical-align:top;'>
	                           <td  style='border-bottom:0px;'>
	                              <table border='0' class='tb2'>
	                                 <tr style='vertical-align:top;'>
	                                    <td class='noborder' >國民身分證領換補<br/>資料查詢
	                                    </td>
	                                 </tr>
	                              </table>
	                           </td>
	                           <td  style='border-bottom:0px; '>
	                              <table border='0' class='tb2'>
	                                 <tr style='vertical-align:top;'>
	                                    <td class='noborder' >受監護/輔助宣告<br/>資料查詢
	                                    </td>
	                                 </tr>
	                              </table>
	                           </td>
							   <td  style='border-bottom:0px; '>
	                              <table border='0' class='tb2'>
	                                 <tr style='vertical-align:top;'>
	                                    <td class='noborder' >財富管理往來<br/>資料查詢
	                                    </td>
	                                 </tr>
	                              </table>
							   </td>
	                        </tr>
	                        <tr>
	                           <td nowrap style='border-top:0px; padding-bottom:12px;'><u id='btn_qry_API_L820M01S'>查詢身分證領換補</u>	
	                           </td>
	                           <td nowrap style='border-top:0px; padding-bottom:12px;'><u id='btn_qry_RPA_L820M01W'>查詢受監護/輔助</u>	
	                           </td>
							    <td nowrap style='border-top:0px; padding-bottom:12px;'><u id='btn_qry_RPA_L820M01E'>查詢財富管理往來</u>	
	                           </td>
	                        </tr>
	                     </table>
	                    <!-- <button type="button" id="btGetOneBtnQuery" name="btGetOneBtnQuery" class=" "><span class="text-only">取得查詢結果</span></button> -->
	                     <div style="margin-top:5px;" id="dataArchivalRecordData">
	                        <div id="gridview1"/>
						 </div>
	                  </fieldset>
				  </form>
               </div>
            </div>
		</div>
            <span id="mainId" style="display:none"/>

         <div id="openChecDatekBox" style="display:none">
            <div>
               <input id="forCheckDate" type="text" size="10" maxlength="10" class="date">	
            </div>
         </div>
		 
		 <div id="openCheckBox" style="display:none"> 
			<div>
				<span id="check1" style="display:none">
				 	<label><input name="checkRadio" type="radio" value="3"><wicket:message key="L820M01M.bt12"><!--  核准--></wicket:message></label><br/>
					<label><input name="checkRadio" type="radio" value="1"><wicket:message key="L820M01M.bt11"><!--  退回經辦修改--></wicket:message></label>
				</span>
			</div>
		</div>
		<div id="openChecDatekBox" style="display:none"> 
			<div>
				<input id="forCheckDate" type="text" size="10" maxlength="10" class="date">	
			</div>
		</div>
		<div id="selectBossBox"  style="display:none;">
			 <form id="selectBossForm">
	         	<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
	            	<tr>
	            		<td class="hd1" width="60%"><wicket:message key="L820M01B.selectBoss"><!--  授信主管人數--></wicket:message>&nbsp;&nbsp;</td>
	                    <td width="40%">
							<select id="numPerson" name="numPerson">
	                    		<option value="1">1</option>
	                    		<option value="2">2</option>
	                            <option value="3">3</option>
								<option value="4">4</option>
	                    		<option value="5">5</option>
	                            <option value="6">6</option>
								<option value="7">7</option>
	                    		<option value="8">8</option>
	                            <option value="9">9</option>
								<option value="10">10</option>
	                    	</select>
						</td>
	                </tr>
	                <tr >
	                	<td class="hd1" ><wicket:message key="L820M01B.bossId"><!--  授信主管--></wicket:message>&nbsp;&nbsp;</td>
	            		<td>
	            			<div id="bossItem"></div>
	                 	</td>
	                </tr>
	                <tr>
	            		<td class="hd1"><wicket:message key="L820M01B.managerId"><!--經副襄理--></wicket:message>&nbsp;&nbsp;</td>
	                    <td><div id="managerItem"></div></td>
	                 </tr>
	           	 </table>
			</form>
  		</div>
         <div id="queryAdcThickBox" style="display:none;" >
         <div id="queryAdcGridview" /> </div>
		 <!--財富管理往來查詢明細-->
		 <div id="queryC350Box" style="display:none">
		 	<form id="L820M01EForm" name="L820M01EForm" >
		 		<span class="color-red" id="span_c350_info_date"></span>
			 	<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
			 		<tr>
			 			<td class="hd1" >
			 				<wicket:message key="L820M01E.in_tr_sc_e"><!-- ETF、外國債劵、連動債	--></wicket:message>&nbsp;&nbsp;
		            	</td>
		            	<td>
		            		<input type="text" id="in_tr_sc_e" name="in_tr_sc_e" size="18" maxlength="22" integer="17" fraction="2" class="numeric" />
		            	</td>
			 		</tr>
					<tr>
						<td class="hd1" >
			 				<wicket:message key="L820M01E.in_tr_fu"><!-- 基金 --></wicket:message>&nbsp;&nbsp;
		            	</td>
		            	<td>
		            		<input type="text" id="in_tr_fu" name="in_tr_fu" size="18" maxlength="22" integer="17" fraction="2" class="numeric" />
		            	</td>
					</tr>
					<tr>
						<td class="hd1" >
			 				<wicket:message key="L820M01E.in_wm_aum"><!-- 理財AUM(A)+(B)+(C)--></wicket:message>&nbsp;&nbsp;
		            	</td>
		            	<td>
		            		<input type="text" id="in_wm_aum" name="in_wm_aum" size="18" maxlength="22" integer="17" fraction="2" class="numeric" />
		            	</td>
					</tr>
					<tr>
						<td class="hd1" >
			 				<wicket:message key="L820M01E.in_wm_fd"><!-- (A)信託商品月底餘額--></wicket:message>&nbsp;&nbsp;
		            	</td>
		            	<td>
		            		<input type="text" id="in_wm_fd" name="in_wm_fd" size="18" maxlength="22" integer="17" fraction="2" class="numeric" />
		            	</td>
					</tr>
					<tr>
						<td class="hd1" >
			 				<wicket:message key="L820M01E.in_wm_ia"><!-- (B)累積已繳保費/保單價值--></wicket:message>&nbsp;&nbsp;
		            	</td>
		            	<td>
		            		<input type="text" id="in_wm_ia" name="in_wm_ia" size="18" maxlength="22" integer="17" fraction="2" class="numeric" />
		            	</td>
					</tr>
					<tr>
						<td class="hd1" >
			 				<wicket:message key="L820M01E.in_wm_std"><!-- (C)優利投資月平均餘額--></wicket:message>&nbsp;&nbsp;
		            	</td>
		            	<td>
		            		<input type="text" id="in_wm_std" name="in_wm_std" size="18" maxlength="22" integer="17" fraction="2" class="numeric" />
		            	</td>
					</tr>
					<tr>
						<td class="hd1" >
			 				<wicket:message key="L820M01E.in_wm_bal"><!--  累積下單金額--></wicket:message>&nbsp;&nbsp;
		            	</td>
		            	<td>
		            		<input type="text" id="in_wm_bal" name="in_wm_bal" size="18" maxlength="22" integer="17" fraction="2" class="numeric" />
		            	</td>
					</tr>	
					<tr>
						<td class="hd1" >
			 				<wicket:message key="L820M01E.in_wm_fee"><!--  累計手收金額--></wicket:message>&nbsp;&nbsp;
		            	</td>
		            	<td>
		            		<input type="text" id="in_wm_fee" name="in_wm_fee" size="18" maxlength="22" integer="17" fraction="2" class="numeric" />
		            	</td>
					</tr>
			 	</table>
			</form>
		 </div>
        
      </wicket:extend>
   </body>
</html>