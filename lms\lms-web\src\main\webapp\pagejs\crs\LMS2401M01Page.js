var initDfd = initDfd || new $.Deferred();
var _handler = "lms2401m01formhandler";

$(function(){
	var tabForm = $("#tabForm");
	var btnPanel = $("#buttonPanel");
	
	$.form.init({
		formHandler:_handler, 
		formAction:'query', 
		loadSuccess:function(json){
			
			// 控制頁面 Read/Write
			if(!$("#buttonPanel").find("#btnSave").is("button") || json.lock) {
				tabForm.lockDoc();
			}
			tabForm.injectData(json);
			initDfd.resolve(json);	
			
			ilog.debug("c240_mainId="+json.mainId );
	}});
	
	var $gridNckdFlagO = $("#gridNckdFlagO").iGrid({
        handler: 'lms2401gridhandler',        
        height: 120,
        postData: {
        	mainOid: $("#mainOid").val(),
            formAction: "queryNckdFlagO"
        },
        sortname: "dataEndDate",
        sortorder: "desc",
        needPager: false,        
        shrinkToFit: false,       
        colModel: [
          {//分行名稱
        	  colHeader: i18n.lms2401m01['grid.nCkdFlagO_branchId'], name: 'show_branchId', width: 150, sortable: false, align: "left" 
          },{//覆審資料日期
            colHeader: i18n.lms2401m01['grid.nCkdFlagO_dataEndDate'], name: 'dataEndDate', width: 120, sortable: true, align: "left"          
          }, {//預計覆審日
		    colHeader: i18n.lms2401m01['grid.nCkdFlagO_expectedRetrialDate'], name: 'expectedRetrialDate', width: 120, sortable: true, align: "left"
         }
        , { name: 'branchId', hidden: true }  
        , { name: 'oid', hidden: true }
        , { name: 'mainId', hidden: true }
        ],        
        ondblClickRow: function(rowid){
        	gridClickNckdFlagO(null, null, $("#gridNckdFlagO").getRowData(rowid));
        }        
    });		

	btnPanel.find("#btnSave").click(function(){		
		saveAction().done(function(json_saveAction){
			if(json_saveAction.saveOkFlag){
				tabForm.injectData(json_saveAction);
            	//更新 LMS2405V01 的 Grid
                CommonAPI.triggerOpener("gridview", "reloadGrid");
                
				API.showMessage(i18n.def.saveSuccess);	
			}
        });
	}).end().find("#btnSend").click(function(){
		saveAction().done(function(json_saveAction){
    		if(json_saveAction.saveOkFlag){
    			API.confirmMessage(i18n.def.confirmApply, function(result){
    	            if (result) {
    	            	$.ajax({
    	    				type: "POST",
    	    		        handler: _handler,
    	    		           data: { formAction: "genCrsProjectNo", mainOid: json_saveAction.mainOid }
    	    		           }).done(function(responseData){
    	    		        	   flowAction();    	
    	    			});    	
    	        	}
    	    	});
    		}
    	});		
	}).end().find("#btnAccept").click(function(){
		var _id = "_div_btnAccept";
		var _form = _id+"_form";
		if ($("#"+_id).length == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");
			dyna.push("<form id='"+_form+"'>");
			dyna.push("	<table><tr><td>");
			dyna.push("		<label><input type='radio' name='decisionExpr' value='1' class='required' />核准</label>");
			dyna.push("		<label><input type='radio' name='decisionExpr' value='2' class='required' />退回</label>");
			dyna.push(" </td></tr></table>");
			dyna.push("</form>");
			
			dyna.push("</div>");
			
		     $('body').append(dyna.join(""));
		}
		//clear data
		$("#"+_form).reset();
		
		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
	        title: i18n.def["confirmApprove"],
	        width: 380,
            height: 100,
            align: "center",
            valign: "bottom",
            modal: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if (!$("#"+_form).valid()) {
                        return;
                    }
                    var val = $("#"+_form).find("[name='decisionExpr']:checked").val();
                    if(val=="1"){
                    	flowAction({'decisionExpr':'核定'});
                    }else if(val=="2"){
                    	flowAction({'decisionExpr':'退回'});
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
	    });
	}).end().find("#btnSendRetrialReport").click(function(){
		askReImportLN().done(function(){
			//gfnCreatEjcicDataToCsv 和 【產生Excel － Ejcic整批上傳名單】 相同
			flowAction();
        });        
	}).end().find("#btnRetrialEvaluation").click(function(){
		saveAction().done(function(json_saveAction){
			if(json_saveAction.saveOkFlag){
				//列印 覆審考核表
				$.form.submit({
	                   url: "../../simple/FileProcessingService",
	                   target: "_blank",
	                   data: {
	                       'oid': $("#mainOid").val(),
	                       'fileDownloadName': "lms2401r01.pdf",
	                       serviceName: "lms2401r01rptservice"            
	                   }
	            });
			}
        });
		
	}).end().find("#btnSendBtt").click(function(){		
		saveAction().done(function(json_saveAction){
			if(json_saveAction.saveOkFlag){
				$.ajax({
	                type: "POST",
	                handler: _handler,
	                data:{formAction: "sendBtt",mainOid: json_saveAction.mainOid}                
	                }).done(function(json_sendBtt){
	                	API.showMessage(i18n.def.runSuccess);
				});
				
			}
        });
	}).end().find("#btnNckdFlagO").click(function(){
		var _id = "_div_btnNckdFlagO";
		
		$gridNckdFlagO.trigger("reloadGrid");
		
		if($gridNckdFlagO.getGridParam("records")>0){
			$("#"+_id).thickbox({
		        title: i18n.lms2401m01["label.nCkdFlagO"],//請選取比對覆審工作底稿
		        width: 500,
	            height: 270,
	            align: "center",
	            valign: "bottom",
	            modal: false,
	            i18n: i18n.def,
	            buttons: {
	                "sure": function(){
	                	 var data = $gridNckdFlagO.getSingleData();
	                     if (data) {
	                    	 $.ajax({
	                             type: "POST",
	                             handler: _handler,
	                             data: {
	                            	 mainOid: $("#mainOid").val(),
	                                 formAction: "saveNoCTL_O",	                                 
	                                 cmpOid: data.oid 
	                             }
	                             }).done(function(responseData){
	                            	 $("#tabForm").injectData(responseData);
	                            	 
	                            	 //更新 LMS2401S02 的 grid
	                            	 $("#gridview").trigger("reloadGrid");
	    	                         
	    	                     	 //更新 LMS2405V01 的 Grid
	    	                     	 CommonAPI.triggerOpener("gridview", "reloadGrid");
	    	                     	 
	    	                     	$.thickbox.close();
	    	                     	
	    	                     	API.showMessage(i18n.def.runSuccess);
	                         });
	                         
	                     }      
	                },
	                "cancel": function(){
	                    $.thickbox.close();
	                }
	            }
		    });
		}else{
			API.showMessage(i18n.lms2401m01["ui_lms2401.msg06"]);//無可供比對之覆審工作底稿
		}
	}).end().find("#btnAddNotShowCust").click(function(){
		//ui_lms2401.msg16=是否補抓名單，應覆審資料截至
		CommonAPI.confirmMessage(i18n.lms2401m01["ui_lms2401.msg16"]+($("#dataEndDate").val()||""), function(b){
            if (b) {
            	$.ajax({
                    handler: _handler,
                    data: {
                    	mainOid: $("#mainOid").val(),
                        formAction: "addNotShowCust"
                    }
                    }).done(function(json){
                    	$("#tabForm").injectData(json); //update 應覆審件數

                   	 	//更新 LMS2401S02 的 grid
                   	 	$("#gridview").trigger("reloadGrid");
                        
                    	 //更新 LMS2405V01 的 Grid
                    	 CommonAPI.triggerOpener("gridview", "reloadGrid");
                    	 
                    	$.thickbox.close();
                    	
                    	API.showMessage(json.msg);
                });
            }
        });
	}).end().find("#btnModifyDate").click(function(){
		var _id = "_div_btnModifyDate";
		var _form = _id+"_form";
		/*
		 * 日期的下拉選單, 寫在js 內會帶不出來,所以仍寫在 html 中
		 */
		
		//clear data
		$("#"+_form).reset();
		
		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
	        title: i18n.lms2401m01["button.ExceptRetrialDate"],
	        width: 380,
            height: 100,
            align: "center",
            valign: "bottom",
            modal: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if (!$("#"+_form).valid()) {
                        return;
                    }
                    saveAction().done(function(json_saveAction){
            			if(json_saveAction.saveOkFlag){
            				$.ajax({
                                type: "POST",
                                handler: _handler,
                                data:$.extend({
                                	formAction: "saveDefault",
                                    mainOid: json_saveAction.mainOid
                                    }, 
                                    $("#"+_form).serializeData()
                                )	
                                }).done(function(json_saveDefault){
                                	tabForm.injectData(json_saveDefault);
                                	//更新 LMS2405V01 的 Grid
                                	CommonAPI.triggerOpener("gridview", "reloadGrid");
                            });
            				$.thickbox.close();
            			}
                    });                    
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
	    });
		
	});	
	
	var askReImportLN = function(opts){
		var my_dfd = $.Deferred();
		
		API.confirmMessage("「傳送分行時，是否要重新引進帳務資料？」", function(result){
            if (result) {
            	$.ajax({
                   type: "POST",
                   handler: _handler,
                   data: {
                       formAction: "importLN",
                       'oid_arr': '',
                       'allOidFlag': 'Y',
                       mainOid: $("#mainOid").val()
                   }
                   }).done(function(responseData){
                	   my_dfd.resolve();
               });    	
        	}else{
        		my_dfd.resolve();
        	}
    	});		
		
		return my_dfd.promise();
	}
	
	var saveAction = function(opts){
		if(tabForm.valid()){
			return $.ajax({
                type: "POST",
                handler: _handler,
                data:$.extend( {
                	formAction: "saveMain",
                    page: responseJSON.page,
                    mainOid: responseJSON.mainOid
                    }, 
                    tabForm.serializeData(),
                    ( opts||{} )
                )                
                }).done(function(json_saveMain){
                	//...
            });
		}else{
			return $.Deferred();
		}
	}
	
	var flowAction = function(opts){
		return $.ajax({
            type: "POST",
            handler: _handler, action: "flowAction",
            data:$.extend( {
            	mainOid: $("#mainOid").val(), 
            	mainDocStatus: $("#mainDocStatus").val() 
                }
                , ( opts||{} )
            )                
            }).done(function(json){
            	API.triggerOpener(); window.close();
        });
	}
	
	function gridClickNckdFlagO(cellvalue, options, rowObject){
		//var c240m01a_oid = rowObject.oid;
	}
});

$.extend(window.tempSave,{
	handler: _handler, // handler 名稱
	action: "tempSave", // action Method
	beforeCheck:function(){ // return false or true		
		return $("#tabForm").valid();
	},sendData:function(){ // 需上送之資料集合(Map<String,String>)
		return $("#tabForm").serializeData();
	}
});


function test(){
	$.ajax({type: "POST", handler: 'lms2401m01formhandler', 
		data: { formAction: "test" }
		}).done(function(responseData){
	});
	/*
	$.ajax({type: "POST", handler: 'lms2401m01formhandler', 
		data: { formAction: "empty900toNew", mainOid: $("#mainOid").val(),'newBrId':'xxx' },
		success: function(responseData){
		}
	}); 
	 */
}