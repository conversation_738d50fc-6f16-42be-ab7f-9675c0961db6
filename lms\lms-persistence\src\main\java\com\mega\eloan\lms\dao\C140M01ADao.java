/* 
 * C140M01ADao.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C140M01A;

/**
 * <pre>
 * 徵信調查報告書主檔
 * </pre>
 * 
 * @since 2011/9/20
 * <AUTHOR>
 * @version <ul>
 *          <li>new
 *          </ul>
 */
public interface C140M01ADao extends IGenericDao<C140M01A> {
	
	/**
	 * 刪除徵信調查報告書(call ces.deleteC140)
	 * 
	 * @param mainId
	 *            文件編號
	 */
	void deleteC140(String mainId);
	
	/**
	 * 以mainId查詢徵信調查報告書
	 * 
	 * @param mainId
	 *            文件編號
	 * @return C140M01A
	 */
	C140M01A findByMainId(String mainId);
}
