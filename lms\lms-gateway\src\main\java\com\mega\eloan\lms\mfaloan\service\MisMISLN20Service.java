/* 
 *MisMISLN20Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.mfaloan.service;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

import com.mega.eloan.lms.mfaloan.bean.MISLN20;

/**
 * <pre>
 * 授信額度檔(昨日) Mis.MISLN20
 * </pre>
 * 
 * @since 2012/11/19
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/11/19,REX,new
 *          </ul>
 */
public interface MisMISLN20Service {

	/**
	 * 檢查額度序號額度控管種類是否正確
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @param cntrNo
	 *            額度序號
	 * @return <pre>
	 * LNF020_FACT_TYPE
	 * </pre>
	 */
	public String findFactType(String custId, String dupNo, String cntrNo);

	/**
	 * 抓出此客戶所有案件
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @return <pre>
	 * 
	 * </pre>
	 */
	public List<MISLN20> findByCustId(String custId, String dupNo);

	/**
	 * 抓出此客戶該額度序號案件
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @param cntrNo
	 *            額度序號
	 * @return
	 */
	public MISLN20 findByKey(String custId, String dupNo, String cntrNo);

	/**
	 * 
	 * 抓取 查詢擔保品已敘做總數餘額
	 * 
	 * @param cntrNo
	 *            額度序號
	 * @return <pre>
	 * LNF022_CUST_ID, 客戶統編
	 * LNF022_BR_NO , 分行代號
	 * LNF022_CONTRACT, 額度序號
	 * beg,最小動用日期 
	 *  quota, 總額度
	 *  bal 餘額
	 * </pre>
	 */
	public Map<String, Object> findSumBycntrNo(String cntrNo);

	Map<String, Object> findByCustIdCntrNo(String custId, String dupNo,
			String cntrNo);

	List<MISLN20> findByCustIdCntrNoList(String custId, String dupNo,
			String cntrNo);

	/**
	 * 找出已取消的額度序號
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	List<MISLN20> findByCustIdIsCanCel(String custId, String dupNo);

	/**
	 * 找出已取消的額度序號
	 * 
	 * @param custIdSet
	 *            客戶編號
	 * @return
	 */
	HashMap<String, String> findCancelCntrNoByCustId(HashSet<String> custIdSet);

	/**
	 * 取得聯貸案資訊
	 * 
	 * @param cntrNo
	 *            額度序號
	 * @return
	 */
	List<Map<String, Object>> findGetL140M01E(String cntrNo);

	/**
	 * @param grpCntrNo
	 * @return
	 */
	List<MISLN20> findByGrpCntrNoIsCanCel(String grpCntrNo);

	/**
	 * 取得 價金履保 資訊
	 * 
	 * @return
	 */
	List<Map<String, Object>> findLNF660();

	List<Map<String, Object>> findLNF034_H(String contract_M,
			String lnf034_CP_BANK_CD, String sDate, String gDate);

	List<Map<String, Object>> findLNF034_OTHER(String contract_M, String brNo,
			String sDate, String gDate);

	List<Map<String, Object>> findLNF034_H_lcNo(String contract_M,
			String lnf034_CP_BANK_CD, String lcNo);

	List<Map<String, Object>> findLNF034_OTHER_lcNo(String contract_M,
			String brNo, String lcNo);

	/**
	 * 同一筆履保序號，可能在N個分行都有承作
	 */
	List<Map<String, Object>> findLNF034_H_CNT(String lnf660_m_contract,
			String date_s, String date_e);

	/**
	 * 同一筆履保序號，可能在N個分行都有承作
	 */
	List<Map<String, Object>> findLNF034_OTHER_CNT(String lnf660_m_contract,
			String date_s, String date_e);

	/**
	 * 參考是否改用 public List<MISLN20> findByCustId(String custId, String dupNo)
	 * 會更合理
	 */
	List<Map<String, Object>> findByCustIdDupNoIncludeCancel(
			String lnf020_cust_id);

	/**
	 * 用ID找出已銷戶的度資料
	 * 
	 * @param search
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	List<Map<String, Object>> findCancelCntrNoByCustId(String custId,
			String dupNo);

	/**
	 * 用cntrNo找出已銷戶的度資料
	 * 
	 * @param search
	 * @return
	 */
	List<MISLN20> queryCancelCntrnoByCntrno(String cntrNo);

	List<MISLN20> query_LNF020_DOCUMENT_NO(String lnf020_document_no);

	/**
	 * J-104-0284-001 額度明細表檢核供應鏈融資賣放限週轉科目 取得LNF020_PROJ_CLASS
	 * 
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @return
	 */
	String findProjClass(String custId, String dupNo, String cntrNo);

	/**
	 * 
	 * @param cntrNo
	 * @return
	 */
	public Map<String, Object> findByCntrNo(String cntrNo);

	/**
	 * J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
	 * 
	 * @param brNo
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public Map<String, Object> findCtlTypeByBrNoAndCustId(String brNo,
			String custId, String dupNo);

	/**
	 * J-108-0040_05097_B1001 Web e-Loan企金授信新增108年度新核准往來客戶及新增放款額度統計表
	 * 
	 * @param endDate
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public List<Map<String, Object>> findIsNewCust(String custId, String dupNo,
			String minDate, String maxDate);

	/**
	 * J-108-0078_05097_B1001
	 * 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。
	 * 
	 * @param endDate
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public List<Map<String, Object>> findIsNewCustForCTL(String custId,
			String dupNo, String maxDate);

	/**
	 * J-108-0217_05097_B1001 Web e-Loan國內企金授信額度明細表配合增加特定金錢信託受益權自行設質擔保授信專案種類
	 * 
	 * @param custId
	 * @param dupNo
	 * @param projClass
	 * @return
	 */
	public List<Map<String, Object>> findByProjClassAndCustId(String custId,
			String dupNo, String projClass);

//	public HashMap<String, String> findCntrNoByCustId(String custId);

	/**
	 * J-110-0304_05097_B1007 Web e-Loan授信覆審配合RPA作業修改
	 * 
	 * 覆審RPA時判斷主借款人於該分行下有無有效額度
	 * 
	 * @param brNo
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public List<Map<String, Object>> findRetrialEffectCust(String brNo,
			String custId, String dupNo);

	/**
	 * J-110-0272_10702_B1001 Web e-Loan消金覆審邏輯調整，找出信保額度
	 *
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @return <pre>
	 *
	 * </pre>
	 */
	public List<MISLN20> findByCustId_R3R4(String custId, String dupNo);

	public List<Map<String, Object>> findByUncanceledAccountCase(List<String> custIdDupNoList);
	
	public List<MISLN20> findByCustIdAndContract(String custId, String dupNo, String cntrNo);
}
