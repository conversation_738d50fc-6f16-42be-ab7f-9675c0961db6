/* 
 * C801M01BDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.C801M01BDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C801M01B;

/** 個人資料檔案清冊明細檔 **/
@Repository
public class C801M01BDaoImpl extends LMSJpaDao<C801M01B, String> implements
		C801M01BDao {

	@Override
	public C801M01B findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C801M01B> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.setMaxResults(Integer.MAX_VALUE);
		_setOrderBy(search);
		List<C801M01B> list = createQuery(C801M01B.class,search).getResultList();
		return list;
	}
	
	@Override
	public List<C801M01B> findByMainId_itemType(String mainId, String itemType) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "itemType", itemType);
		search.setMaxResults(Integer.MAX_VALUE);
		_setOrderBy(search);
		List<C801M01B> list = createQuery(C801M01B.class,search).getResultList();
		return list;
	}
	
	@Override
	public int deleteByMainId(String mainId) {
		Query query = entityManager.createNamedQuery("C801M01B.deleteMainId");
		query.setParameter("MAINID", mainId);
		return query.executeUpdate();
	}
	
	private void _setOrderBy(ISearch search){//order by
		Map<String, Boolean> map = new LinkedHashMap<String, Boolean>();
		map.put("itemType", false);
		map.put("itemSeq", false);
		search.setOrderBy(map);	
	}
}