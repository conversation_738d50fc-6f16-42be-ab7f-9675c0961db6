package com.mega.eloan.lms.fms.service.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.DocLogEnum;
import com.mega.eloan.common.model.BELDFM02;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.ElDeleteFileService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.dao.L140MM5ADao;
import com.mega.eloan.lms.dao.L140MM5BDao;
import com.mega.eloan.lms.dao.L140MM5CDao;
import com.mega.eloan.lms.fms.service.LMS7700Service;
import com.mega.eloan.lms.model.L140MM5A;
import com.mega.eloan.lms.model.L140MM5B;
import com.mega.eloan.lms.model.L140MM5C;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.core.FlowException;
import tw.com.jcs.flow.service.FlowService;

/**
 * <pre>
 * 電子文件維護作業
 * </pre>
 * 
 * @since 2019
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Service
public class LMS7700ServiceImpl extends AbstractCapService implements
		LMS7700Service {
	private static final Logger logger = LoggerFactory.getLogger(LMS7700ServiceImpl.class);
	
	@Resource
	L140MM5ADao l140mm5aDao;
	
	@Resource
	L140MM5BDao l140mm5bDao;
	
	@Resource
	L140MM5CDao l140mm5cDao;
	
	@Resource
	ElDeleteFileService elDFService;
	
	@Resource
	DocLogService docLogService;

	@Resource
	FlowService flowService;
	
	@Resource
	TempDataService tempDataService;

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == L140MM5A.class) {
			return l140mm5aDao.findPage(search);
		} else if(clazz == L140MM5B.class){
			return l140mm5bDao.findPage(search);
		} else if(clazz == L140MM5C.class){
			return l140mm5cDao.findPage(search);
		}
		return null;
	}
	
	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		if (clazz == L140MM5A.class) {
			return (T) l140mm5aDao.findByOid(oid);
		} else if(clazz == L140MM5B.class){
			return (T) l140mm5bDao.findByOid(oid);
		} else if(clazz == L140MM5C.class){
			return (T) l140mm5cDao.findByOid(oid);
		}
		return null;
	}
	
	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		if (clazz == L140MM5A.class) {
			return l140mm5aDao.findByMainId(mainId);
		} else if (clazz == L140MM5B.class) {
			return l140mm5bDao.findByMainId(mainId);
		} else if (clazz == L140MM5C.class) {
			return l140mm5cDao.findByMainId(mainId);
		}
		return null;
	}
	
	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L140MM5A) {
					if (Util.isEmpty(((L140MM5A) model).getOid())) {
						((L140MM5A) model).setCreator(user.getUserId());
						((L140MM5A) model).setCreateTime(CapDate
								.getCurrentTimestamp());
						l140mm5aDao.save((L140MM5A) model);
						flowService.start("LMS7700Flow",
								((L140MM5A) model).getOid(), user.getUserId(),
								user.getUnitNo());
					} else {
						// 當文件狀態為編製中時文件亂碼才變更
						((L140MM5A) model).setUpdater(user.getUserId());
						((L140MM5A) model).setUpdateTime(CapDate
								.getCurrentTimestamp());
						l140mm5aDao.save((L140MM5A) model);
						if (!"Y".equals(SimpleContextHolder
								.get(EloanConstants.TEMPSAVE_RUN))) {
							tempDataService.deleteByMainId(((L140MM5A) model)
									.getMainId());
							docLogService.record(((L140MM5A) model).getOid(),
									DocLogEnum.SAVE);
						}
					}
				} else if (model instanceof L140MM5B) {
					((L140MM5B) model).setUpdater(user.getUserId());
					((L140MM5B) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l140mm5bDao.save((L140MM5B) model);
				} else if (model instanceof L140MM5C) {
					((L140MM5C) model).setUpdater(user.getUserId());
					((L140MM5C) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l140mm5cDao.save((L140MM5C) model);
				}
			}
		}
	}
	
	@Override
	public List<L140MM5C> findL140mm5csByMainId(String mainId) {
		return l140mm5cDao.findByMainId(mainId);
	}
	
	@Override
	public void saveL140MM5Cs(List<L140MM5C> l140mm5cs) {
		l140mm5cDao.save(l140mm5cs);
	}
	
	@Override
	public boolean deleteL140mm5cs(String[] oids) {
		boolean flag = false;
		List<L140MM5C> l140mm5cs = new ArrayList<L140MM5C>();
		for (int i = 0, size = oids.length; i < size; i++) {
			L140MM5C l140mm5c = (L140MM5C) findModelByOid(L140MM5C.class,
					oids[i]);
			l140mm5cs.add(l140mm5c);
			l140mm5cDao.delete(l140mm5c);
			docLogService.record(oids[i], DocLogEnum.DELETE);
		}
		if (!l140mm5cs.isEmpty()) {
			flag = true;
		}
		return flag;
	}
	
	@Override
	public void updL140mm5aCnt(String oid) {
		L140MM5A l140mm5a = l140mm5aDao.findByOid(oid);
		int cnt = 0;
		if(l140mm5a != null && Util.isNotEmpty(l140mm5a)){
			cnt = l140mm5a.getCnt();
			List<L140MM5C> l140mm5cs = l140mm5cDao.findByMainId(l140mm5a.getMainId());
			cnt = l140mm5cs.size();
		}
		l140mm5a.setCnt(cnt);
		this.save(l140mm5a);
	}
	
	@Override
	public void deleteL140mm5bs(List<L140MM5B> l140mm5bs, boolean isAll) {
		if (isAll) {
			l140mm5bDao.delete(l140mm5bs);
		} else {
			List<L140MM5B> L140MM5BsOld = new ArrayList<L140MM5B>();
			for (L140MM5B l140mm5b : l140mm5bs) {
				String staffJob = l140mm5b.getStaffJob();
				if (!("L6".equals(staffJob) || "L7".equals(staffJob))) {
					L140MM5BsOld.add(l140mm5b);
				}
			}
			l140mm5bDao.delete(L140MM5BsOld);
		}
	}
	
	@Override
	public void saveL140mm5bList(List<L140MM5B> list) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (L140MM5B l140mm5b : list) {
			l140mm5b.setUpdater(user.getUserId());
			l140mm5b.setUpdateTime(CapDate.getCurrentTimestamp());
		}
		l140mm5bDao.save(list);
	}
	
	@Override
	public L140MM5B findL140mm5b(String mainId, String branchType,
			String branchId, String staffNo, String staffJob) {
		return l140mm5bDao.findByUniqueKey(mainId, branchType, branchId,
				staffNo, staffJob);
	}
	
	@Override
	public void flowAction(String mainOid, L140MM5A model, boolean setResult,
			boolean resultType, boolean upCom) throws Throwable {
		try {
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			FlowInstance inst = flowService.createQuery().id(mainOid).query();
			if (inst == null) {
				inst = flowService.start("LMS7700Flow",
						((L140MM5A) model).getOid(), user.getUserId(),
						user.getUnitNo());
			}
			if (setResult) {
				inst.setDeptId(user.getUnitNo());
				inst.setUserId(user.getUserId());
				// resultType 控制前進還是後退
				// 當有先行動用的狀態 是到03O 非先行動用表示已完成 到05O
				inst.setAttribute("result", resultType ? "核准" : "退回");
				if (resultType) {
					if (upCom) {
						L140MM5A l140mm5a = (L140MM5A) findModelByOid(
								L140MM5A.class, mainOid);
						// 動審表簽章欄檔取得人員職稱
						List<L140MM5B> l140mm5blist = l140mm5bDao
								.findByMainId(l140mm5a.getMainId());
						String apprId = "";
						String reCheckId = "";

						for (L140MM5B l140mm5b : l140mm5blist) {
							String StaffJob = Util.trim(l140mm5b.getStaffJob());// 取得人員職稱
							String StaffNo = Util.trim(l140mm5b.getStaffNo());// 取得行員代碼
							if (Util.equals(StaffJob, "L1")) {// 分行經辦
								apprId = StaffNo;
							} else if (Util.equals(StaffJob, "L4")) {// 分行覆核主管
								reCheckId = StaffNo;
							}
						}
						// 若人員職稱為空值改取l140mm4a上的人員資料
						if (Util.isEmpty(apprId)) {
							apprId = l140mm5a.getUpdater();
						}
						if (Util.isEmpty(reCheckId)) {
							reCheckId = l140mm5a.getApprover();
						}
						
						String mainId = l140mm5a.getMainId();

						List<L140MM5C> l140mm5cList = l140mm5cDao.findByMainIdIsDelete(
								mainId, "Y");
						if (l140mm5cList != null && Util.isNotEmpty(l140mm5cList)) {
							for(L140MM5C l140mm5c : l140mm5cList){
								BELDFM02 beldfm02 = this.convertBELDFM02(l140mm5c);
								elDFService.saveM02(beldfm02);
							}
						}						
					}
				}
			}
			inst.next();

		} catch (FlowException e) {
			Throwable t1 = e;
			while (t1.getCause() != null) {
				t1 = t1.getCause();
			}
			throw t1;
		}
	}
	
	private BELDFM02 convertBELDFM02(L140MM5C l140mm5c){
		BELDFM02 beldfm02 = new BELDFM02();
		
		beldfm02.setMainId(IDGenerator.getUUID());
		beldfm02.setOwnBrId(l140mm5c.getOwnBrId());
		beldfm02.setCustId(l140mm5c.getCustId());
		beldfm02.setDupNo(l140mm5c.getDupNo());
		beldfm02.setCustName(l140mm5c.getCustName());
		beldfm02.setCes(l140mm5c.getCes());
		beldfm02.setCms(l140mm5c.getCms());
		beldfm02.setCol(l140mm5c.getCol());
		beldfm02.setLms(l140mm5c.getLms());
		beldfm02.setRps(l140mm5c.getRps());
		beldfm02.setIsDelete(l140mm5c.getIsDelete());
		beldfm02.setReason(l140mm5c.getReason());
		beldfm02.setDataFrom(l140mm5c.getDataFrom());
		beldfm02.setClose_date(l140mm5c.getCloseDate());
		beldfm02.setCreator(l140mm5c.getCreator());
		beldfm02.setCreateTime(l140mm5c.getCreateTime());
		beldfm02.setUpdater(l140mm5c.getUpdater());
		beldfm02.setUpdateTime(l140mm5c.getUpdateTime());

		return beldfm02;
	}

	@Override
	public void delete(GenericBean... entity) {
		// TODO Auto-generated method stub
	}
}
