/**
 * 土建融案檢視清單共用js
 */
var initS08eJson = {
	handlerName : null,
	// 設定handler名稱
	setHandler : function(){
		if(responseJSON.docURL == "/lms/lms1201m01"){
			// 授權外企金
			this.handlerName = "lms1201formhandler";
		}else if(responseJSON.docURL == "/lms/lms1101m01"){
			// 授權內企金
			this.handlerName = "lms1101formhandler";
		}else if(responseJSON.docURL == "/lms/lms1211m01"){
			// 授權外個金
			this.handlerName = "lms1211formhandler";
		}else if(responseJSON.docURL == "/lms/lms1111m01"){
			this.handlerName = "lms1111formhandler";
		}else{
			this.handlerName = "lms1301formhandler";
		}		
	}
};

initDfd.done(function() {
	//2012_07_20_rex add 取得sso 連線資訊 
	BrowserAction.init();
	setCloseConfirm(true);
	initS08eJson.setHandler();
/*
	$("#tformL120m01e7").find("#hchildMap").hide();
	$("#tformL120m01e7").find("#hbranchId").hide();
*/	
});

/**
 * 土建融案檢視清單讀取分頁
 */
function queryL120s07a(){
	if($("#lmss08e_page").attr("open") == "true"){
		$("#lmss08e_page").load("../../lms/lmss08e",function(){
			queryL120s07a2();
		});
		$("#lmss08e_page").attr("open",false);
	}else{
		queryL120s07a2();		
	}
}

/**
 * 土建融案檢視清單查詢內容
 */
function queryL120s07a2(){
	var $tformL120m01e7 = $("#tformL120m01e7");
	$.ajax({ 
		handler : initS08eJson.handlerName,
		type : "POST",
		dataType : "json",
		action : "queryL120s07a",
		data : {
			mainId : responseJSON.mainId
		},
		success : function(json) {
			$tformL120m01e7.setData(json.tformL120m01e7);
			// 開啟土建融案檢視清單ThickBox
			seachKind7();
		}
	});	
}

/**
 * 土建融案檢視清單ThickBox
 */
function seachKind7() {
	$("#buildThick").thickbox({ // 使用選取的內容進行彈窗
		title : i18n.lmscommom["other.msg129"],	//other.msg129=土建融案檢視清單
		width : 850,
		height : 450,
		modal : true,
		i18n : i18n.def,
		buttons : {
			"saveData" : function() {
				var $tformL120m01e7 = $("#tformL120m01e7");
				if($tformL120m01e7.valid()){
					$.ajax({ 
						handler : initS08eJson.handlerName,
						type : "POST",
						dataType : "json",
						action : "saveL120s07a",
						data : {
							mainId : responseJSON.mainId,
							tformL120m01e7 : JSON.stringify($tformL120m01e7.serializeData())
						},
						success : function(json) {
							$("#formL120m01e").find("#docDscr7").html(DOMPurify.sanitize(json.formL120m01e.docDscr7));
						}
					});
				}
			},
			"del" : function() {
		        // confirmDelete=是否確定刪除?
				CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
				if (b) {					
						$.ajax({ 
							handler : initS08eJson.handlerName,
							type : "POST",
							dataType : "json",
							action : "deleteL120s07a",
							data : {
								mainId : responseJSON.mainId
							},
							success : function(json) {
								$.thickbox.close();
								$.thickbox.close();
								CommonAPI.showMessage(json.NOTIFY_MESSAGE);
							}
						});
					}				
				});
			},
			"print" : function() {
				printR25();
			},						
			"close" : function() {
				 API.confirmMessage(i18n.def['flow.exit'], function(res){
						if(res){
							$.thickbox.close();
						}
			        });
			}
		}
	});
}

/**
 * 土建融清除內容
 */
function clearBuild(){
	$.ajax({ 
		handler : initS08eJson.handlerName,
		type : "POST",
		dataType : "json",
		action : "deleteL120s07a",
		data : {
			mainId : responseJSON.mainId
		},
		success : function(json) {
			$.thickbox.close();
			$.ajax({ // 查詢主要借款人資料
				handler : initS08eJson.handlerName,
				type : "POST",
				dataType : "json",
				data : {
					formAction : "clearContent",
					mainId : responseJSON.mainId,
					docType : 7			
				},
				success : function(json) {
					$("#formL120m01e").find("#docDscr7").html("");
					$("#formL120m01e").find("#docDscr7").val("");
				}
			});	
		}
	});	
}

/**
 * 土建融列印報表
 */
function printR25(){
	var pdfName = "l120r01.pdf";
	var count = 0;
	var content = "";
	content = "R25" + "^" + "";
	$.form.submit({
        url: "../../simple/FileProcessingService",
        target: "_blank",
        data: {
        	mainId : responseJSON.mainId,
        	rptOid : content,
			fileDownloadName : pdfName,
			serviceName : "lms1201r01rptservice"
        }
    });
}