/**
 * IUserDao.java
 *
 * Copyright (c) 2009 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
*/
package tw.com.iisi.cap.security.dao;

import java.util.List;

import tw.com.iisi.cap.security.model.IRole;
import tw.com.iisi.cap.security.model.IUser;

/**
 * <pre>
 * interface IUserDao. 
 * 取得使用者資料
 * </pre>
 * 
 * <pre>
 * $Date: 2010-08-25 11:25:55 +0800 (星期三, 25 八月 2010) $
 * $Author: iris $
 * $Revision: 185 $
 * $HeadURL: svn://***********/MICB_ISDOC/cap/cap-core/src/main/java/tw/com/iisi/cap/security/dao/IUserDao.java $
 * </pre>
 *
 * <AUTHOR>
 * @version $Revision: 185 $
 * @version
 *          <ul>
 *          <li>2010/7/26,iristu,new
 *          </ul>
 * @param <T>
 *            the model extends IUser
 */
public interface IUserDao<T extends IUser> {

    /**
     * 以登入ID取得使用者
     * 
     * @param loginId
     *            登入Id
     * @param unitNo
     *            使用者所屬單位代碼
     * @return
     */
    T getUserByLoginId(String loginId, String unitNo);

    /**
     * 從使用者取得角色
     * 
     * @param user
     *            使用者
     * @return
     */
    List<? extends IRole> getRoleByUser(T user);
}
