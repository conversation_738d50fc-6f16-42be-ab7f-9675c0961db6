package com.mega.eloan.lms.fms.flow;

import org.springframework.stereotype.Component;

import com.mega.eloan.common.flow.AbstractFlowHandler;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.model.L140MM3A;

import tw.com.jcs.flow.FlowInstance;


/**
 * <pre>
 * 都更危老註記維護作業流程
 * </pre>
 * 
 * @since 2018
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Component
public class LMS7500Flow extends AbstractFlowHandler {

	@Transition(node = "待覆核")
	public void test3(FlowInstance instance) {
	
	}
	
	@Override
	public Class<? extends Meta> getDomainClass() {
		return L140MM3A.class;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Class getDocStatusEnumClass() {
		return CreditDocStatusEnum.class;
	}
}