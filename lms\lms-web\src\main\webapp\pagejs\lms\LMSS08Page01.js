initDfd.done(function() {
	gridborrowC();	
	var s41 = $("#thickboxPeo"), s41dialog = $("#s41"), form = $("#tabForm_1"), s41t1t2form = $("#s41t2form"), s41t3form = $("#s41t3form"), s41t4form = $("#s41t4form");
	
    var s41grid, s41t2grid, s41t3grid, s41t4grid;
    var subOid = form.find("#subOid"), subMainId = form.find("#subMainId"), subUid = form.find("#subUid");
	
    s41grid = s41.find("#s41grid").iGrid({
        handler:'lms1205gridhandler',height:180,needPager:false,
        postData:{gridMainId: responseJSON.mainId,gridUid: responseJSON.mainId, formAction:'queryViewA'},
        caption: i18n.lmss08['ces1405.4101'],//'主要負責人及連保人資信狀況',
        colModel: [{colHeader: i18n.lmss08['L120S08.grid20'],//"借款人統編",
            name: 'l120m01e.docCustId', sortable: true
        }, {colHeader: i18n.lmss08['ces1405.4102'],//"身分證號碼",
            name: 'pcId', sortable: true
        }, {colHeader: i18n.lmss08['ces1405.4103'],//"姓名",
            name: 'pcName'
        }, {colHeader: i18n.lmss08['ces1405.4104'],//"負責人／連保人",
            name: 'pcType'
        }, {colHeader: i18n.lmss08['ces1405.4105'],//"職稱",
            name: 'pcTitle'
        }, {colHeader: i18n.lmss08['ces1405.4106'],//"性別",
            name: 'pcSex'
        }, {name: 'oid', hidden: true}, {name: 'mainId', hidden: true}, {name: 'uid', hidden: true}],
        ondblClickRow: function(rowid){
        	var ret = s41grid.getRowData(rowid);
        	openS41Dialog({subOid:ret.oid,subMainId:ret.mainId,subUid:ret.uid});
        }
    });

    s41.find("#s41btnAdd").click(function(){//主要-新增
    	openpersonC();
    }).end().find("#s41btnDel").click(function(){//主要-刪除
    	deleteGridRow(s41grid,'41');
    });
	
	function deleteGridRow(igrid,ipage){/*刪除選取的grid資料*/
    	var ret = igrid.getSelRowDatas();
        if (ret) {
			CommonAPI.confirmMessage(i18n.def["action_003"], function(b){
			if (b) {					
		        	$.ajax({
		        		handler: 'lms1205formhandler',message: i18n.def.action_003,
			            data: {page:ipage,deleteMainOid:ret.oid,formAction: "delete"},
			            success: function(json){
			            	$("#formL120m01e").find("#docDscr3").html(DOMPurify.sanitize(json.formL120m01e.docDscr3));
							CommonAPI.showPopMessage(i18n.def.confirmDeleteSuccess);
			            	igrid.trigger("reloadGrid");
			            }
			         });
				}				
			});
        }else{
            CommonAPI.showErrorMessage(i18n.def["grid.selrow"]);
        }
    }

	function openpersonC(){
		$.ajax({
			handler : "lms1205formhandler",
			type : "POST",
			dataType : "json",
			data : 
			{
				formAction : "checkBorrow",
				mainId : responseJSON.mainId
			},
			success : function(json) {
				uGridborrowC();
				$("#openpersonC").thickbox({     // 使用選取的內容進行彈窗
					title : i18n.lmss08["L120S08.thickbox15"],
					width : 600,
					height : 500,
					modal : true,
					valign: "bottom",
					align: "center",
					i18n:i18n.def,
					buttons: {             
						"sure": function() {
							var gridId = $("#gridborrowC").getGridParam('selrow');
							var gridData = $("#gridborrowC").getRowData(gridId);
							if(gridId == null){
								CommonAPI.showMessage(i18n.lmss08['L120S08.alert1']);
							}else{
								$.thickbox.close();
								openS41Dialog({});
								$("#tabForm_1").find("#lienCustId").val(gridData.custId);
								$("#tabForm_1").find("#lienDupNo").val(gridData.dupNo);
							}			    
						},
						"cancel": function() {					 
							 API.confirmMessage(i18n.def['flow.exit'], function(res){
									if(res){
										$.thickbox.close();
									}
						        });
						}
					}			
				});
			}
		});		
	}
	
	    //登錄主要負責人連保人------------------
    var s41dialogDiv = $("#thickboxPeo"),tabForm = $("#tabForm_1");
    function openS41Dialog(data){
		var dialogOption = {
			modal: true,
			height: 480,
			width: 940,
			open: function(){
				var cs = tabForm.find("input[name=isRejt]");
				cs.readOnly(true).trigger("click");
				tabForm.find("#currCR,#currDE,#currGU,#mycurr,#Curr,#landCurr,#buCurr").val("TWD").end().find("#amtUnitDE,#amtUnitCR,#amtUnitGU,#myamtUnit,#amtUnit,#landAmtUnit,#buAmtUnit").val("1000");
				data.subOid &&
				$.ajax({
					handler: 'lms1205formhandler',
					data: {
						page: '41',
						subOid: data.subOid,
						formAction: "query"
					},
					success: function(json){
						tabForm.injectData(json);
						var claz = cs.attr("class"), readonly = json[claz + "Flag"] != '2';
						cs.readOnly(readonly).end().find("tbody[id^=" + claz + "]").readOnlyChilds(readonly);
						tabForm.find("#s41t2grid,#s41t3grid,#s41t4grid").each(function(){
							$(this).jqGrid('setGridParam', {
								postData: {
									gridMainId: data.subMainId,
									gridUid: data.subUid
								}
							}).trigger("reloadGrid");
						});
					}
				});
			},
			close: function(){
				tabForm.reset();
			},
			buttons: API.createJSON([{
				key: i18n.def.saveData,
				value: function(){
					if (tabForm.find("input[name=pcType]").is(":checked")) {
						if (tabForm.valid()) {
							$.ajax({
								handler: 'lms1205formhandler',
								data: $.extend(tabForm.serializeData(), {
									mainOid: s41.find("#mainOid").val(),
									mainId: responseJSON.mainId,
									uid: s41.find("#uid").val(),
									subOid: data.subOid,
									page : "41",
									lienCustId : $("#tabForm_1").find("#lienCustId").val(),
									lienDupNo : $("#tabForm_1").find("#lienDupNo").val(),
									formAction: "tempSave2"
								}),
								success: function(json){
									tabForm.injectData(json);
									s41grid.trigger("reloadGrid");
		 							 var ids = new Array();
									 ids = $(s41grid).jqGrid('getDataIDs');
									 var list = "";
									 var sign = ",";
									 var count = 0;
									 for ( var id in ids) {								 
								 		 var rows = $(s41grid).jqGrid('getRowData',	ids[id]);					 		 
										 if (rows.pcName != 'undefined'&& rows.pcName != null&& rows.pcName != 0) {
											 list += ((list == "") ? "": sign)+ rows.pcName;
										 }
										 count++;
									 }
				                      $.ajax({
				                          handler: 'lms1205formhandler',
				                          data: $.extend(form.serializeData(), {
				                          	formAction : "saveAndSetGua",
											  formL120m01e : JSON.stringify($("#formL120m01e").serializeData()),
				                              mainOid: $("#mainOid").val(),
				                              mainId: responseJSON.mainId,
				                              pcNames : list
				                          }),
				                          success: function(json){
				                              //$("#formL120m01e").setData(json.formL120m01e,false);
											  $("#formL120m01e").find("#docDscr3").html(DOMPurify.sanitize(json.formL120m01e.docDscr3));
				                              API.showMessage(i18n.def.saveSuccess);
				                          }
				                      });		                              		                              
		                              //$.thickbox.close();
								}
							});
						}
					}
					else {
						CommonAPI.showErrorMessage(i18n.lmss08["ces1405.4104"] + i18n.def["val.required"]);
					}
				}
			}, {
				key: i18n.def.close,
				value: function(){
					$.thickbox.close();
					tabForm.reset();
				}
			}])
		};
		$("#s41dialog").thickbox(dialogOption);
	}
	
	// 以下開始實作連保人細部功能
	tabForm.bind('reset',function(){
    	tabForm.find("#s41t2grid,#s41t3grid,#s41t4grid").clearGridData();
    });

    //本人及配偶資料----------------
    var s41t1 = tabForm.find("#s41t1");
    s41t1.find("input[name=isRejt]").click(function(){
    	var cs = $(this), claz=cs.attr("class");
    	s41t1.find("#" + claz + cs.val()).show().siblings("[id^=" + claz + "]").hide();
    }).end().find("#pcId").blur(function(){//統一編號
    	if ($(this).valid()) {
    		s41t1.find("input[name=pcSex]").attr('checked', false).filter("[value='"+$(this).val().substr(1, 1) +"']").trigger('click').attr("checked", true);
        } else {
        	s41t1.find("input[name=pcSex]").attr('checked', false);
        }
    }).end().find("#s41t1btnPullin").click(function(){//引進負責人/連保人基本資料
    	API.includeId({
            autoResponse: { "id": "pcId", "dupno": "pcDupNo", "name": "pcName"},
			defaultValue:s41t1.find("#pcId").val(),
            btnAction: function(id, dupno, name){
                $.ajax({
                    handler: 'lms1205formhandler',
                    data: { pcId: id, dupNo: dupno, formAction: "getCustInfo" },
                    success: function(json){
                    	s41t1.injectData(json);
                		var cs = s41t1.find("input[name=isRejt]"), claz=cs.attr("class"), readonly = json[claz+"Flag"]!='2';
                		cs.readOnly(readonly).end().find("tbody[id^="+claz+"]").readOnlyChilds(readonly);
                    }
                });
            }
        });
    });
    //經營事業----------------
    var s41t2 = tabForm.find("#s41t2") , s41t2form = $("#s41t2form") ;
    s41t2.find("#s41t2btnAdd").click(function(){ //新增
    	var records = s41t2grid.getGridParam("records");
        if(records>=5){			
			API.showMessage(i18n.msg('EFD2039').replace(/\${item}/,'').replace(/\${maxRows}/,'5'));
        	//API.showMessage(i18n.msg('EFD2039',{'item':'','maxRows':'5'}));//$\{item\}明細資料最多$\{maxRows\}筆
        	return;
        }
        openSubDetail(s41t2form,'s41t2dialog',300,600,s41t2grid,{
        	qryAction:'query41S',saveAction:'save41S',type:'A',subOidS:'',mainId: responseJSON.mainId,subOid:tabForm.find("#subOid").val()
			, subMainId:tabForm.find("#subMainId").val(), subUid:tabForm.find("#subUid").val()
    	});
    }).end().find("#s41t2btnDel").click(function(){//刪除
    	deleteSubDetail(s41t2grid,'A');
    });
    //本人之土地-----------
    var s41t3=tabForm.find("#s41t3"),s41t3form = $("#s41t3form");
    s41t3.find("#s41t3btnAdd").click(function(){//新增
    	var records = s41t3grid.getGridParam("records");
        if(records>=3){////$\{item\}明細資料最多$\{maxRows\}筆
        	API.showMessage(i18n.msg('EFD2039').replace(/\${item}/,'').replace(/\${maxRows}/,'3'));
        	//API.showMessage(i18n.msg('EFD2039',{'item':'','maxRows':'3'}));
        	return;
        }
        openSubDetail(s41t3form,'s41t3dialog',580,750,s41t3grid,{
        	qryAction:'query41S',saveAction:'save41S',type:'B',subOidS:'',mainId: responseJSON.mainId,subOid:tabForm.find("#subOid").val()
			, subMainId:tabForm.find("#subMainId").val(), subUid:tabForm.find("#subUid").val()
    	});
    }).end().find("#s41t3btnDel").click(function(){
    	deleteSubDetail(s41t3grid,'B');
    });
    s41t3form.find("#landUse1").change(function(){ //用途-使用分區-種類
    	s41t3form.find("#landUse2").setOptions("LandUse2" + $(this).val(), false);
    }).end().find("#landLevel").change(function(){//地目等則
    	($(this).val() == '99') ? s41t3form.find("#landCust").show() : s41t3form.find("#landCust").hide().val('');
    }).end().find("#landBp").blur(function(){ //(坪)
    	numConP2M(s41t3form,'landBp', 'landBm', 'landUnit1');
    }).end().find("#landAp").blur(function(){ //(坪)
    	numConP2M(s41t3form,'landAp', 'landAm', 'landUnit2');
    }).end().find("#landBm").blur(function(){
    	numConM2P(s41t3form,'landBp', 'landBm', 'landUnit1');
    }).end().find("#landAm").blur(function(){
    	numConM2P(s41t3form,'landAp', 'landAm', 'landUnit2');
    }).end().find("#landUnit1").change(function(){
    	s41t3form.find("#landBp").trigger('blur');
    }).end().find("#landUnit2").change(function(){
    	s41t3form.find("#landAp").trigger('blur');
    }).end().find("#s41t3btnCnt").click(function(){//持分面積-計算
    	var _landRateC = s41t3form.find("#landRateC").val(),_landRateD = s41t3form.find("#landRateD").val();
    	if (_landRateC == '' || _landRateD == '') {
            CommonAPI.showErrorMessage(i18n.msg["EFD2047"]);
        } else {
        	var q = /[,]/g;
        	var _landBp = s41t3form.find("#landBp").val(),_landBm = s41t3form.find("#landBm").val();
        	_landRateC = parseFloat(String(_landRateC).replace(q,""));
        	_landRateD = parseFloat(String(_landRateD).replace(q,""));
        	_landBp = parseFloat(String(_landBp).replace(q,""));
        	_landBm = parseFloat(String(_landBm).replace(q,""));
        	var landAp = Math.floor((_landRateC/_landRateD)*_landBp*100)/100;
        	var landAm = Math.floor((_landRateC/_landRateD)*_landBm*100)/100;
            s41t3form.find("#landAp").val(isNaN(landAp)?"":landAp);
            s41t3form.find("#landAm").val(isNaN(landAm)?"":landAm);
            s41t3form.find("#landUnit2").val(s41t3form.find("#landUnit1").val());
        }
    });
    //本人之建物--------------
    var s41t4=tabForm.find("#s41t4"),s41t4form = $("#s41t4form");
    s41t4.find("#s41t4btnAdd").click(function(){
    	var records = s41t4grid.getGridParam("records");
        if(records>=3){
			API.showMessage(i18n.msg('EFD2039').replace(/\${item}/,'').replace(/\${maxRows}/,'3'));
			//API.showMessage(i18n.msg('EFD2039',{'item':'','maxRows':'3'}));
        	return;
        }
        openSubDetail(s41t4form,'s41t4dialog',500,700,s41t4grid,{
        	qryAction:'query41S',saveAction:'save41S',type:'C',subOidS:'',mainId: responseJSON.mainId,subOid:tabForm.find("#subOid").val()
			, subMainId:tabForm.find("#subMainId").val(), subUid:tabForm.find("#subUid").val()
    	});
    }).end().find("#s41t4btnDel").click(function(){
    	deleteSubDetail(s41t4grid,'C');
    });
    s41t4form.find("#buP").blur(function(){//(坪)
    	numConP2M(s41t4form,'buP', 'buBuM', 'buUnit');
    }).end().find("#buBuM").blur(function(){
    	numConM2P(s41t4form,'buP', 'buBuM', 'buUnit');
    }).end().find("#buUnit").change(function(){
    	s41t4form.find("#buP").trigger('blur');
    });
    //經濟狀況-------------
    var s41t5 = tabForm.find("#s41t5");
    s41t5.find("#s41t5btn").click(function(){//引進本行金額
        $.ajax({
            handler: 'lms1205formhandler',
            data: { pcId: s41t1.find("#pcId").val(),pcDupNo: s41t1.find("#pcDupNo").val(),formAction: "getEldpf" },
            success: function(json){ s41t5.injectData(json); }
        });
    }).end().find("#s41t5btnCountTot").click(function(){//計算合計
    	$.ajax({
            handler: "lms1205formhandler",
            data: {
                AMT: [s41t5.find("#depAmnt1").val(), s41t5.find("#depAmnt2").val(), s41t5.find("#depAmnt3").val()],
				formAction: "countTot"
            },
            success: function(json){ s41t5.find("#dep_Tot_amnt").val(json.result); }
        });
    }).end().find("#s41t5btn2CountTot").click(function(){//計算合計
    	$.ajax({
            handler: "lms1205formhandler", action: "countTot",
            data: {
                AMT: [s41t5.find("#borAmnt1").val(), s41t5.find("#borAmnt2").val(), s41t5.find("#borAmnt3").val()]
            },
            success: function(json){ s41t5.find("#bor_Tot_amnt").val(json.result); }
        });
    }).end().find("#currDE").change(function(){
		s41t5.find("select[class^=cCur]").val($(this).val());
	}).end().find("#amtUnitDE").change(function(){
		s41t5.find("select[class^=cUnit]").val($(this).val());
	}).end().find('input[name^="depNo"]').each(function(){
		$(this).blur(function(){				
				$.ajax({
					handler: "lms1205formhandler",
					global:false,
					data: {bid_1_1: $(this).val(),formAction: "s71queryBs1_1Nm", id:"depName"+$(this).attr("id").substring(5)},
					success: function(d){
						d.colNm && $("#"+d.colNm).val(d.bs1_1);
					}
				});
			});
	});
    //信用情形及商場風評-------------
    var s41t7 = tabForm.find("#s41t7");
    s41t7.find("#isCredit").click(function(){/*信用情形*/
    	$(this).val($(this).is(":checked") ? "0" : "1");
        s41t7.find("#Div" + $(this).attr("name"))[$(this).is(":checked") ? "hide" : "show"]();
    }).end().find("#s41t7btnQry").click(function(){//查詢負責人(連保人)信用情形
    	API.getETCH({
            id: "pcId",
            autoResponse: { "date": "crdD3", "year": "crdLyear", "rtcg": "refTick2"}
        });
    	// J-112-0534 因應兆豐金控自113.1.1下架證券違約交割/上市櫃觀察名單，取消查詢
    	/* 先直接註解功能
        $.ajax({
            handler: "lms1205formhandler",
            data: { custId: s41t1.find("#pcId").val(),formAction: "getCURIQ01"},
            success:function(json){ 
            	s41t7.injectData(json); 
            	(json.msg);// && CommonAPI.showErrorMessage(json.msg);
            }
        });
    	*/
    }); 
    //主債務、共同債務及保證債務情形--------------------
    var s41t9 = tabForm.find("#s41t9");
    s41t9.find("#s41t9btnImLoan").click(function(){
    	$.ajax({
            handler: "lms1205formhandler",
            data: {custId: s41t1.find("#pcId").val(),dupNo: s41t1.find("#pcDupNo").val(),formAction: "s07ImLoanData"},
            success: function(json){s41t9.injectData(json);}
        });
    });
    
    var s41t2grid = tabForm.find("#s41t2grid").iGrid({/*經營事業*/
        handler:'lms1205gridhandler',height: 120,needPager: false,localFirst: true,
        postData: {gridMainId: '',gridUid: '', formAction: 'queryView41SA'},
        colModel: [{colHeader: i18n.lmss08['ces1405.4107'],//"其他投資事業名稱",
            name: 'invBusna1',width: 100
        }, {colHeader: i18n.lmss08['ces1405.4108'],//"擔任職務",
            name: 'invKind1',align: 'center',width: 100
        }, {colHeader: i18n.lmss08['ces1405.4109'],//"幣別",
            name: 'invCap11',width: 100, align: 'center'
        }, {colHeader: i18n.lmss08['ces1405.4110'],//"實收資本額",
            name: 'invUnit11',width: 100,align: 'right',
            formatter: 'number',formatoptions: {decimalSeparator: ",",thousandsSeparator: ",",decimalPlaces: 0}
        }, {colHeader: i18n.lmss08['ces1405.4111'],//"單位",
            width: 50, align: 'center',name: 'invCap21'
        }, {colHeader: i18n.lmss08['ces1405.4112'],// "實有股份/出資額",
            width: 100,align: 'right',name: 'invUnit21',
            formatter: 'number',formatoptions: {decimalSeparator: ",",thousandsSeparator: ",",decimalPlaces: 0}
        }, {colHeader: i18n.lmss08['ces1405.4113'],//"單位",
            width: 50,align: 'center',name: 'amtUnitST', formatter: function(data){
				if(data == ""){
					return i18n.lmss08['ces1405.41127'];
				}else{
					return data;
				}
			}
        }, {name: 'oid',hidden: true},{name: 'mainId', hidden: true},{name: 'pid', hidden: true}],
        ondblClickRow: function(rowid){
        	var ret = s41t2grid.getRowData(rowid);
        	openSubDetail(s41t2form,'s41t2dialog',300,600,s41t2grid,{
        		qryAction:'query41S',saveAction:'save41S',type:'A',mainId: responseJSON.mainId,subOidS:ret.oid,
        		subOid:tabForm.find("#subOid").val(),subUid:ret.pid,subMainId:ret.mainId
        	});
        }
    });
    
    var s41t3grid = tabForm.find("#s41t3grid").iGrid({/*土地*/
        handler: 'lms1205gridhandler',height: 75,needPager: false,localFirst: true,
        postData: {gridMainId: '',gridUid: '',formAction: 'queryView41SB'},
        colModel: [{colHeader: i18n.lmss08['ces1405.4114'],//"座落地址",
            name: 'landAddr',width: 180
        }, {colHeader: i18n.lmss08['ces1405.4115'],//"用途",
            name: 'landUse',width: 100
        }, {colHeader: i18n.lmss08['ces1405.4116'],//"地目等則",
            name: 'landLevel',width: 70,align: 'center'
        }, {colHeader: i18n.lmss08['ces1405.4117'],//"地號",
            name: 'landNum',width: 50,align: 'center'
        }, {colHeader: i18n.lmss08['ces1405.4118'],//"持分",
            name: 'landRate',width: 50,align: 'center'
        }, {colHeader: i18n.lmss08['ces1405.4119'],//"總面積",
            name: 'landBp',width: 80,align: 'center',
            formatter: function(val){
                return val == null ? '' : val + i18n.lmss08['ces1405.4125'];
            }
        }, {colHeader: i18n.lmss08['ces1405.4120'],//"持分面積",
            name: 'landAp',width: 80,align: 'center',
            formatter: function(val){
                return val == null ? '' : val + i18n.lmss08['ces1405.4125'];
            }
        }, {colHeader: i18n.lmss08['ces1405.4121'],//"他項權利",
            name: 'landMp',width: 180
        }, {name: 'oid',hidden: true},{name: 'mainId', hidden: true},{name: 'pid', hidden: true}],
        ondblClickRow: function(rowid){
        	var ret = s41t3grid.getRowData(rowid);
        	openSubDetail(s41t3form,'s41t3dialog',580,750,s41t3grid,{
        		qryAction:'query41S',saveAction:'save41S',type:'B',mainId: responseJSON.mainId,subOidS:ret.oid,
        		subOid:tabForm.find("#subOid").val(),subUid:ret.pid,subMainId:ret.mainId
        	});
        }
    });
    
    var s41t4grid = tabForm.find("#s41t4grid").iGrid({/*建物*/
        handler: 'lms1205gridhandler',height:75,needPager:false,localFirst:true,
        postData: {gridMainId: '',gridUid: '',formAction:'queryView41SC'},
        colModel: [{colHeader: i18n.lmss08['ces1405.4114'],//"座落地址",
            name: 'buAddr',width: 200
        }, {colHeader: i18n.lmss08['ces1405.4115'],//"用途",
            name: 'buUse',width: 120
        }, {colHeader: i18n.lmss08['ces1405.4122'],//"建號",
            name: 'buNum',width: 120,align: 'center'
        }, {colHeader: i18n.lmss08['ces1405.4123'],//"構造",
            name: 'buStru',width: 120,align: 'center'
        }, {colHeader: i18n.lmss08['ces1405.4124'],//"面積",
            name: 'buP',width: 150,align: 'center',
            formatter: function(val){return val == null ? '' : val + i18n.lmss08['ces1405.4125'];}
        }, {colHeader: i18n.lmss08['ces1405.4121'],//"他項權利",
            name: 'buMp',width: 180
        }, {name: 'oid',hidden: true},{name: 'mainId', hidden: true},{name: 'pid', hidden: true}],
        ondblClickRow: function(rowid){
        	var ret = s41t4grid.getRowData(rowid);
        	openSubDetail(s41t4form,'s41t4dialog',500,700,s41t4grid,{
        		qryAction:'query41S',saveAction:'save41S',type:'C',mainId: responseJSON.mainId,subOidS:ret.oid,
        		subOid:tabForm.find("#subOid").val(),subUid:ret.pid,subMainId:ret.mainId
        	});
        }
    });
    
    function numConP2M(form,p, m, unit){
        if (!form.find("#" + p).valid() || form.find("#" + p).val() == '') {return;}
        $.ajax({
            handler: "lms1205formhandler",
            data: {p2m: form.find("#" + p).val(),unit: form.find("#" + unit).val(),formAction: "numConvert"},
            success: function(json){form.find("#" + m).val(json.area);}
        });
    }
    
    function numConM2P(form,p, m, unit){
        if (!form.find("#" + m).valid() || form.find("#" + m).val() == '') {return;}
        $.ajax({
            handler: "lms1205formhandler",
            data: {m2p: form.find("#" + m).val(),unit: form.find("#" + unit).val(),formAction: "numConvert"},
            success: function(json){form.find("#" + p).val(json.area);}
        });
    }
	
		$.extend(API, {
		//查詢票信及債信狀況
		getETCH : function(settings){
			var s = $.extend({
				formId: tabForm,
				id: "custId",
		        autoResponse: { // 是否自動回填資訊 
		            "date": "date", 
		            "year": "year", 
		            "rtcg": "rtcg" 
		        }
		    }, settings || {});
			
			$.ajax({
		        handler: "lms1205formhandler",
		        data: {
		        	custId: $("#" + s['id']).val(),
					formAction: "getETCH"
		        },
		        success:function(json){
		        	var form = $("#" + s['formId']);
		        	form.find("#" + s.autoResponse['date']).val(json.date);
		        	form.find("#" + s.autoResponse['year']).val(json.year);
		        	form.find("#" + s.autoResponse['rtcg']).val(json.rtcg);
		        }
		    });
		}
	});	

});

function saveAction(json){
    return $.ajax($.extend({
        handler: "lms1205formhandler",formAction: "save",
        success: function(json){
            window.name = json.mainOid;
            setRequiredSave(false);
        }
    }, json || {}));
}

function openSubDetail(subForm,dialog,_height,_width,grid,data){
    if (!data.subOid) {CommonAPI.showErrorMessage(i18n.def["requireSave"]);return;}
    subForm.find("#subOid"+data.type).val(data.subOidS);
    $("#"+dialog).thickbox({
        modal: true,height:_height,width:_width,
        open: function(){
			tabForm.find("#currCR,#currDE,#currGU,#mycurr,#Curr,#landCurr,#buCurr").val("TWD").end().find("#amtUnitDE,#amtUnitCR,#amtUnitGU,#myamtUnit,#amtUnit,#landAmtUnit,#buAmtUnit").val("1000");
        	data.subOidS &&
            $.ajax({
                handler: 'lms1205formhandler',
                data: {type: data.type,subOidS: data.subOidS,formAction: data.qryAction},
                success: function(json){
                	subForm.injectData(json);
                	if(data.type=='B'){
                		subForm.find("#landLevel, #landUse1").change().end().find("#landUse2").val(json.landUse2);
                	}
                }
            });
        	data.type == 'A'&&subForm.find("select[class^=spAmtUnit]").setOptions({0: i18n.lmss08['ces1405.41127']}, true);
        },
        close:function(){
			subForm.find("select[class^=spAmtUnit]").removeOptions(['0']);
        	subForm.reset();
        },
        buttons: API.createJSON([{
            key: i18n.def.saveData,
            value: function(){
                if (subForm.valid()) {
                    $.ajax({
                        handler: 'lms1205formhandler',
                        data: $.extend(subForm.serializeData(), {
                            page:data.type,subOidS:data.subOidS,
                            subOid:data.subOid,mainOid:$("#thickboxPeo").find("#mainOid").val(),
							formAction: data.saveAction
                        }),
                        success: function(json){
                            grid.jqGrid('setGridParam', {
                                postData: {gridMainId:data.subMainId,gridUid: data.subUid}
                            }).trigger("reloadGrid");
                            $.thickbox.close();
                        }
                    });
                }
            }
        }, {key: i18n.def.close,
            value: function(){$.thickbox.close();subForm.reset();}
        }])
    });
}

function deleteSubDetail(grid,type){
	var ret = grid.getSelRowDatas();
    if (ret) {
		API.flowConfirmAction({
			message: i18n.def["action_003"],
			handler: 'lms1205formhandler',
			data: {
				type: type,
				deleteSubOid: ret.oid,
				formAction: "deleteS"
			},
			success: function(){
				CommonAPI.showPopMessage(i18n.def["confirmDeleteSuccess"]);
				grid.trigger("reloadGrid");
			}
		});
    } else {
        CommonAPI.showErrorMessage(i18n.def["grid.selrow"]);
    }
}

function uGridborrowC(){
    $("#gridborrowC").jqGrid("setGridParam", {
        postData: {
            formAction: "queryL120s01aToGetData",
            rowNum: 10
        },
        search: true
    }).trigger("reloadGrid");
}

function gridborrowC(){
	$("#gridborrowC").iGrid({
		handler: 'lms1205gridhandler',
		//width: "100px",
		height: 350,
		sortname : 'custId',
		postData : {
			formAction : "queryL120s01aToGetData",
			rowNum:10
		},
		caption: "&nbsp;",
		hiddengrid : false,
		rownumbers:true,
		colModel: [{
			colHeader: i18n.lmss08["L120S08.grid17"],//"身分證統編"
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			name : 'custId' //col.id
		},{
			colHeader : i18n.lmss08["L120S08.grid18"],//"借款人姓名"
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			//formatter : 'click',
			//onclick : function,
			name : 'custName' //col.id
		},{
			colHeader: i18n.lmss08["L120S08.grid19"],//"最後異動日期"
			align : "center",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			//formatter : 'click',
			//onclick : function,
			name : 'updateTime' //col.id
		}, {
			colHeader : "dupNo",
			name : 'dupNo',
			hidden : true
		}, {
			colHeader : "oid",
			name : 'oid',
			hidden : true
		}],
		ondblClickRow: function(rowid){
		}
	});
}