<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="panelFragmentBody">
            <form id="L180M02AForm">
                <fieldset>
                    <legend>
                        <th:block th:text="#{'doc.docinfo'}">
                            文件資訊
                        </th:block>
                    </legend>
                    <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tbody>
                            <tr>
                                <td class="hd1">
                                    <th:block th:text="#{'L180M01A.branchId'}">
                                        分行名稱
                                    </th:block>&nbsp;&nbsp;
                                </td>
                                <td width="40%">
                                    <span id="elfBranch" class="field reset"></span>&nbsp;<span id="elfBranchName"></span>
                                </td>
                                <td class="hd1">
                                    <th:block th:text="#{'L180M02A.elfUpdDate'}">
                                        上次人工維護日
                                    </th:block>&nbsp;&nbsp;
                                </td>
                                <td width="30%">
                                    <span id="elfUpdDate" class="field reset"></span>
                                </td>
                            </tr>
                            <tr>
                                <td class="hd1">
                                    <th:block th:text="#{'L180M02A.docStatus'}">
                                        文件狀態
                                    </th:block>&nbsp;&nbsp;
                                </td>
                                <td>
                                    <b><span class="color-red reset" id="docStatusName"></span></b>&nbsp;&nbsp;
                                </td>
                                <td class="hd1" valign="middle">
                                    <th:block th:text="#{'L180M02A.elfUpdater'}">
                                        上次人工調整ID
                                    </th:block>&nbsp;&nbsp;
                                </td>
                                <td valign="middle">
                                    <span id="elfUpdater" class="field reset"></span>
                                </td>
                            </tr>
                            <tr>
                                <td class="hd1" valign="middle">
                                    <th:block th:text="#{'L180M02A.custName'}">
                                        借款人
                                    </th:block>&nbsp;&nbsp;
                                </td>
                                <td>
                                    <span id="custId" class="field reset"></span>&nbsp;&nbsp;重覆序號：<span id="dupNo" class="field reset"></span>
                                    <br/>
                                    (<span class="color-red reset" id="typCdName"></span>)<span id="custName" class="field reset"></span>
                                </td>
                                <td class="hd1">
                                    <th:block th:text="#{'L180M02A.elfTmeStamp'}">
                                        上次資料更新日
                                    </th:block>&nbsp;&nbsp;
                                </td>
                                <td>
                                    <span id="elfTmeStamp" class="field reset"></span>
                                </td>
                            </tr>
                            <tr>
                                <td class="hd1" valign="middle">
                                    <th:block th:text="#{'L180M02A.elfCState'}">
                                        戶況
                                    </th:block>&nbsp;&nbsp;
                                </td>
                                <td valign="middle">
                                    <span id="cState" class="reset"></span>
									<input type="hidden" id="elfCState" name="elfCState" />
                                </td>
                                <td class="hd1" valign="middle">
                                    <th:block th:text="#{'L180M02A.elfCancelDt'}">
                                        銷戶日
                                    </th:block>&nbsp;&nbsp;
                                </td>
                                <td valign="middle">
                                    <span id="elfCancelDt" class="reset"></span>
                                </td>
                            </tr>
                            <tr>
                                <td class="hd1">
                                    <th:block th:text="#{'L180M02A.uLoan'}">
                                        是否為聯貸案
                                    </th:block>&nbsp;&nbsp;
                                </td>
                                <td>
                                    <label>
                                        <input id="uCase" name="uCase" type="radio" value="Y" onClick="if (this.checked){ $('#radio2_chk').show();}"/>
                                        <th:block th:text="#{'L180M02A.Y'}">是</th:block>
                                    </label>
                                    <label>
                                    	<input id="uCase" name="uCase" type="radio" value="N" onClick="if (this.checked){ $('#radio2_chk').hide();$('input[name=uCaseRole]').attr('checked',''); }" checked/>
                                    	<th:block th:text="#{'L180M02A.N'}">否</th:block>
									</label>
                                    <div id="radio2_chk" style="display:none;">
                                        <label>
                                            <input id="uCaseRole" name="uCaseRole" type="radio" value="1" />
                                            <th:block th:text="#{'L180M02A.uLoan1'}">主辦行</th:block>
                                        </label>
                                        <label>
                                            <input id="uCaseRole" name="uCaseRole" type="radio" value="2" />
                                            <th:block th:text="#{'L180M02A.uLoan2'}">參貸行</th:block>
                                        </label>
                                        <label>
                                            <input id="uCaseRole" name="uCaseRole" type="radio" value="3"/>
                                            <th:block th:text="#{'L180M02A.uLoan3'}">參貸同業</th:block>
                                        </label>
                                    </div>
                                </td>
                                <td class="hd1">&nbsp;&nbsp;</td>
                                <td></td>
                            </tr>
                        </tbody>
                    </table>
                </fieldset>
                <br/>
                <fieldset>
                    <legend>
                        <th:block th:text="#{'doc.docUpdateLog'}">文件異動紀錄</th:block>
                    </legend>
                    <div class="funcContainer">
                        <div wicket:id="_docLog" />
                    </div>
                    <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tbody>
                            <tr>
                                <td class="hd1">
                                    <th:block th:text="#{'doc.creator'}">
                                        文件建立者
                                    </th:block>&nbsp;&nbsp;
                                </td>
                                <td width="30%">
                                    <span id="creator" class="reset"></span>
                                </td>
                                <td class="hd1">
                                    <th:block th:text="#{'doc.lastUpdater'}">
                                        最後異動者
                                    </th:block>&nbsp;&nbsp;
                                </td>
                                <td>
                                    <span id="updater" class="reset"></span>
                                </td>
                            </tr>
                            <tr>
                                <td class="hd1">
                                </td>
                                <td>
                                </td>
                                <td class="hd1">
                                    <th:block th:text="#{'doc.docCode'}">
                                        報表亂碼
                                    </th:block>&nbsp;&nbsp;
                                </td>
                                <td>
                                    <span id="randomCode" class="reset"></span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </fieldset>
                <fieldset>
                    <legend>
                        <th:block th:text="#{'doc.endreport'}">
                        </th:block>
                    </legend>
                    <div class="tabs">
                        <ul><li>
                            <a href="#taba-01"><b>
                            	<th:block th:text="#{'tab.title'}"></th:block>
                            </b></a>
                        </li></ul>
                        <div class="tabCtx-warp">
                            <div id="tabsa-01">
                                <table width="100%">
                                    <tr>
                                        <td align="left" width="25%">
                                            <b><span class="text-red"><th:block th:text="#{'appraiser'}">覆核：</th:block></span></b>
                                            <span id="approverCN" class="reset"></span>
                                        </td>
                                        <td align="left" width="25%">
                                            <b><span class="text-red"><th:block th:text="#{'apprId'}">經辦：</th:block></span></b>
                                            <span id="apprId" class="reset"></span>
                                        </td>
                                        <td align="right" width="10%"></td>
                                        <td width="15%"></td>
                                        <td align="right" width="10%"></td>
                                        <td width="15%"></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </fieldset>
            </form>
        </th:block>
    </body>
</html>
