/* 
 * L120M01J.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 應收帳款買方額度資訊主檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L120M01J", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId","type","custId","dupNo","custId2","dupNo2","cntrNo"}))
public class L120M01J extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * 類型<p/>
	 * 附表A(借款人買方額度所對應之賣方)
	 */
	@Size(max=1)
	@Column(name="TYPE", length=1, columnDefinition="CHAR(1)")
	private String type;

	/** 
	 * 統一編號<p/>
	 * 申貸戶ID
	 */
	@Size(max=10)
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 重覆序號 **/
	@Size(max=1)
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 客戶名稱 **/
	@Size(max=120)
	@Column(name="CUSTNAME", length=120, columnDefinition="VARCHAR(120)")
	private String custName;

	/** 
	 * 列印順序<p/>
	 */
	@Digits(integer=5, fraction=0, groups = Check.class)
	@Column(name="PRINTSEQ", columnDefinition="DECIMAL(5,0)")
	private Integer printSeq;
	
	/** 
	 * 序號<p/>
	 * 合計=99999
	 */
	@Digits(integer=5, fraction=0, groups = Check.class)
	@Column(name="ITEMSEQ", columnDefinition="DECIMAL(5,0)")
	private Integer itemSeq;

	/** 
	 * 統一編號<p/>
	 * 賣方ID<br/>
	 *  合計= 9999999999
	 */
	@Size(max=10)
	@Column(name="CUSTID2", length=10, columnDefinition="VARCHAR(10)")
	private String custId2;

	/** 
	 * 重覆序號<p/>
	 * 合計= 9
	 */
	@Size(max=1)
	@Column(name="DUPNO2", length=1, columnDefinition="CHAR(1)")
	private String dupNo2;

	/** 
	 * 客戶名稱<p/>
	 * 合計
	 */
	@Size(max=120)
	@Column(name="CUSTNAME2", length=120, columnDefinition="VARCHAR(120)")
	private String custName2;

	/** 
	 * 承購額度序號<p/>
	 * TYPE A才有值，合計為 999999999999
	 */
	@Size(max=12)
	@Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)")
	private String cntrNo;

	/** 
	 * 實際承購幣別<p/>
	 * TYPE A才有值
	 */
	@Size(max=3)
	@Column(name="FACTORCURR", length=3, columnDefinition="CHAR(3)")
	private String factorCurr;

	/** 
	 * 實際承購金額-原幣<p/>
	 * TYPE A才有值
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="FACTORAMT", columnDefinition="DECIMAL(17,2)")
	private BigDecimal factorAmt;
	
	/** 
	 * 調整後實際承購金額-原幣<p/>
	 * TYPE A才有值
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="FACTORADJAMT", columnDefinition="DECIMAL(17,2)")
	private BigDecimal factorAdjAmt;

	/** 
	 * 調整後實際承購金額-TWD<p/>
	 * TYPE A才有值
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="FACTORAMTNT", columnDefinition="DECIMAL(17,2)")
	private BigDecimal factorAmtNt;

	/** 資料日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="DATADATE", columnDefinition="Date")
	private Date dataDate;

	/** 備註-匯率 **/
	@Size(max=600)
	@Column(name="REFRATE", length=600, columnDefinition="VARCHAR(600)")
	private String refRate;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 版本號 **/
	@Size(max=2)
	@Column(name="version", length=2, columnDefinition="VARCHAR(2)")
	private String version;
	
	/** 此賣方是否有無追索額度 **/
	@Size(max=1)
	@Column(name="haveNonCntrNo", length=1, columnDefinition="VARCHAR(1)")
	private String haveNonCntrNo;
	
	/** 
	 * 實際承購無追索金額-原幣<p/>
	 * TYPE A才有值
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="FACTORNONAMT", columnDefinition="DECIMAL(17,2)")
	private BigDecimal factorNonAmt;
	
	/** 
	 * 調整後實際承購無追索金額-原幣<p/>
	 * TYPE A才有值
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="FACTORADJNONAMT", columnDefinition="DECIMAL(17,2)")
	private BigDecimal factorAdjNonAmt;

	/** 
	 * 調整後實際承購無追索金額-TWD<p/>
	 * TYPE A才有值
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="FACTORADJNONAMTNT", columnDefinition="DECIMAL(17,2)")
	private BigDecimal factorAdjNonAmtNt;
	
	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得類型<p/>
	 * 附表A(借款人買方額度所對應之賣方)
	 */
	public String getType() {
		return this.type;
	}
	/**
	 *  設定類型<p/>
	 *  附表A(借款人買方額度所對應之賣方)
	 **/
	public void setType(String value) {
		this.type = value;
	}

	/** 
	 * 取得統一編號<p/>
	 * 申貸戶ID
	 */
	public String getCustId() {
		return this.custId;
	}
	/**
	 *  設定統一編號<p/>
	 *  申貸戶ID
	 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得重覆序號 **/
	public String getDupNo() {
		return this.dupNo;
	}
	/** 設定重覆序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得客戶名稱 **/
	public String getCustName() {
		return this.custName;
	}
	/** 設定客戶名稱 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/** 
	 * 取得序號<p/>
	 * 合計=99999
	 */
	public Integer getItemSeq() {
		return this.itemSeq;
	}
	/**
	 *  設定序號<p/>
	 *  合計=99999
	 **/
	public void setItemSeq(Integer value) {
		this.itemSeq = value;
	}

	/** 
	 * 取得統一編號<p/>
	 * 賣方ID<br/>
	 *  合計= 9999999999
	 */
	public String getCustId2() {
		return this.custId2;
	}
	/**
	 *  設定統一編號<p/>
	 *  賣方ID<br/>
	 *  合計= 9999999999
	 **/
	public void setCustId2(String value) {
		this.custId2 = value;
	}

	/** 
	 * 取得重覆序號<p/>
	 * 合計= 9
	 */
	public String getDupNo2() {
		return this.dupNo2;
	}
	/**
	 *  設定重覆序號<p/>
	 *  合計= 9
	 **/
	public void setDupNo2(String value) {
		this.dupNo2 = value;
	}

	/** 
	 * 取得客戶名稱<p/>
	 * 合計
	 */
	public String getCustName2() {
		return this.custName2;
	}
	/**
	 *  設定客戶名稱<p/>
	 *  合計
	 **/
	public void setCustName2(String value) {
		this.custName2 = value;
	}

	/** 
	 * 取得承購額度序號<p/>
	 * TYPE A才有值，合計為 999999999999
	 */
	public String getCntrNo() {
		return this.cntrNo;
	}
	/**
	 *  設定承購額度序號<p/>
	 *  TYPE A才有值，合計為 999999999999
	 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/** 
	 * 取得實際承購幣別<p/>
	 * TYPE A才有值
	 */
	public String getFactorCurr() {
		return this.factorCurr;
	}
	/**
	 *  設定實際承購幣別<p/>
	 *  TYPE A才有值
	 **/
	public void setFactorCurr(String value) {
		this.factorCurr = value;
	}

	/** 
	 * 取得實際承購金額-原幣<p/>
	 * TYPE A才有值
	 */
	public BigDecimal getFactorAmt() {
		return this.factorAmt;
	}
	/**
	 *  設定實際承購金額-原幣<p/>
	 *  TYPE A才有值
	 **/
	public void setFactorAmt(BigDecimal value) {
		this.factorAmt = value;
	}

	/** 
	 * 取得實際承購金額-TWD<p/>
	 * TYPE A才有值
	 */
	public BigDecimal getFactorAmtNt() {
		return this.factorAmtNt;
	}
	/**
	 *  設定實際承購金額-TWD<p/>
	 *  TYPE A才有值
	 **/
	public void setFactorAmtNt(BigDecimal value) {
		this.factorAmtNt = value;
	}

	/** 取得資料日期 **/
	public Date getDataDate() {
		return this.dataDate;
	}
	/** 設定資料日期 **/
	public void setDataDate(Date value) {
		this.dataDate = value;
	}

	/** 取得備註-匯率 **/
	public String getRefRate() {
		return this.refRate;
	}
	/** 設定備註-匯率 **/
	public void setRefRate(String value) {
		this.refRate = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
	
	/** 設定調整後實際承購金額-原幣 **/
	public void setFactorAdjAmt(BigDecimal factorAdjAmt) {
		this.factorAdjAmt = factorAdjAmt;
	}
	
	/** 取得調整後實際承購金額-原幣 **/
	public BigDecimal getFactorAdjAmt() {
		return factorAdjAmt;
	}
	
	/** 設定列印順序 **/
	public void setPrintSeq(Integer printSeq) {
		this.printSeq = printSeq;
	}
	
	/** 取得列印順序 **/
	public Integer getPrintSeq() {
		return printSeq;
	}
	
	
	/** 取得版本號 **/
	public String getVersion() {
		return this.version;
	}
	/** 設定版本號**/
	public void setVersion(String version) {
		this.version = version;
	}
	
	/** 取得此賣方是否有無追索額度 **/
	public String getHaveNonCntrNo() {
		return this.haveNonCntrNo;
	}
	/** 設定此賣方是否有無追索額度 **/
	public void setHaveNonCntrNo(String haveNonCntrNo) {
		this.haveNonCntrNo = haveNonCntrNo;
	}
	
	/** 設定實際承購無追索金額-原幣 **/
	public void setFactorNonAmt(BigDecimal factorNonAmt) {
		this.factorNonAmt = factorNonAmt;
	}
	
	/** 取得實際承購無追索金額-原幣 **/
	public BigDecimal getFactorNonAmt() {
		return factorNonAmt;
	}
	
	/** 設定調整後實際承購無追索金額-原幣 **/
	public void setFactorAdjNonAmt(BigDecimal factorAdjNonAmt) {
		this.factorAdjNonAmt = factorAdjNonAmt;
	}
	
	/** 取得調整後實際承購無追索金額-原幣 **/
	public BigDecimal getFactorAdjNonAmt() {
		return factorAdjNonAmt;
	}
	
	/** 設定調整後實際承購無追索金額-TWD **/
	public void setFactorAdjNonAmtNt(BigDecimal factorAdjNonAmtNt) {
		this.factorAdjNonAmtNt = factorAdjNonAmtNt;
	}
	
	/** 取得調整後實際承購無追索金額-TWD **/
	public BigDecimal getFactorAdjNonAmtNt() {
		return factorAdjNonAmtNt;
	}
}
