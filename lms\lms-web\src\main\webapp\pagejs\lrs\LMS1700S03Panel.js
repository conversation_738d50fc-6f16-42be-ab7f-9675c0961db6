var initDfd = initDfd || new $.Deferred();
var initAll = initAll || new $.Deferred();

initDfd.done(function(json){
	var gridF101M01A = $("#gridF101M01A").iGrid({
		handler : 'lms1700gridhandler',
		height : 270, // 設定高度
		postData : {
			'mainOid' : $("#mainOid").val()
		},
		multiselect : true, // 是否開啟多選
		needPager: false,
		colModel : [ 
           {name : 'mainId', hidden : true}
         , {//年度
			colHeader :i18n.lms1700m01["label.gridF101M01A.year"], name : 'year', align : "center", width : 40
		}, {//期間(起)
			colHeader :i18n.lms1700m01["label.gridF101M01A.sDate"], name : 'sDate', align : "left", width : 65
		}, {//期間(迄)
			colHeader : i18n.lms1700m01["label.gridF101M01A.eDate"], name : 'eDate', align : "left", width : 65
		}, {//幣別
			colHeader :i18n.lms1700m01["label.gridF101M01A.curr"], name : 'curr', align : "center", width : 50
		}, {//財報種類{0-GAAP, 1-IFRS, 2-EAS}  J-109-0279_05097_B1001 e-Loan企金簽報書配合徵信IFRS改版與新增EAS會計準則相關修改
			colHeader :i18n.lms1700m01["label.gridF101M01A.gaapFlag"], name : 'gaapFlag', align : "left", width : 80
		}, {//行業別
			colHeader :i18n.lms1700m01["label.gridF101M01A.tradeType"], name : 'tradeType', align : "left", width : 100
		}, {//合併
			colHeader :i18n.lms1700m01["label.gridF101M01A.conso"], name : 'conso', align : "center", width : 40         
		}, {//確認日期
			colHeader : i18n.lms1700m01["label.gridF101M01A.approveTime"], name : 'approveTime', align : "left", width : 70
		}, {//類型
			colHeader :i18n.lms1700m01["label.gridF101M01A.periodType"], name : 'periodType', align : "left", width : 100
        }, {//單位
			colHeader :i18n.lms1700m01["label.gridF101M01A.amtUnit"],name : 'amtUnit', align : "right", width : 60
        }
      ]
	});
	
	var defRatioArr = ["20", "11", "12", "22"];
	
	function build_selItem(json_selItem){
		$.each(json_selItem, function(itemName, kvMap) {
			if(itemName=="finRatio"){
				
				//========================================
				var col_cnt = 4;
	        	var elmArr = [];
	        	
				$.each(json_selItem[itemName], function(idx, kVal) {
					var _cn = "";
					if($.inArray( kVal['value'], defRatioArr )===-1){
						_cn = "ratio_not_default"; 
					}else{
						_cn = "ratio_default"; 
					}					
					elmArr.push("<label style='letter-spacing:0px;cursor:pointer;'><input value='"+kVal['value']+"' "
							+" id='"+itemName+"' name='"+itemName+"' type='checkbox' "
							+" class='"+_cn+"'>"+kVal['desc']+"</label>");
				});
				//補empty col
	        	var addcnt = (col_cnt - (elmArr.length % col_cnt));
	        	if(addcnt==col_cnt){
	        		addcnt = 0;
	        	}
	        	for(var i=0;i<addcnt;i++){
	        		elmArr.push("&nbsp;");  
	        	}
				var dyna = [];
	        	dyna.push("<table width='100%' border='0' cellspacing='0' cellpadding='0'>");
	        	dyna.push("<tr>");
	        	for(var i=0;i<elmArr.length;i++){
	        		dyna.push("<td>"+elmArr[i]+"</td>");	            		
	        		if( (i+1) % col_cnt==0){
	        			dyna.push("</tr><tr>");
	        		}
	        	}
	        	dyna.push("</tr>");        	
	        	dyna.push("</table>");
	        	
	        	$("#div_"+itemName).html(dyna.join("\n"));
			}
			
		});
	}
	build_selItem(json.selItem)
	
	$("#btn_impL70M01C").click(function(){
		
		chose_F101M01A().done(function(r_chose_F101M01A){
			chose_ratioNo().done(function(r_chose_ratioNo){
				$.ajax({
					type: "POST",
					handler: _handler,
					data:$.extend(
        				{'formAction': 'getFinData'}
        				, r_chose_F101M01A
        				, r_chose_ratioNo), 
				}).done(function(json){
					var tabForm = $("#tabForm");
					tabForm.injectData(json);
				});
			});
		});
		
	});	
	function chose_F101M01A(){
		var my_dfd = $.Deferred();
		gridF101M01A.jqGrid("setGridParam", {
	        postData: {
	    		formAction : "queryCESF101"
	        },			
	        search: true
	    }).trigger("reloadGrid");
		
		$("#div_gridF101M01A").thickbox({ // 使用選取的內容進行彈窗
	        title: '限已覆核財務報表', width: 800, height: 400, align: "center", valign: "bottom", modal: false, i18n: i18n.def,
            buttons: {
                "sure": function(){

					var rows = gridF101M01A.getGridParam('selarrrow');
					if (rows == "") {
						return CommonAPI.showMessage(i18n.def.action_005);
					} 
					if(rows.length > 3){
						//ui_lms1700.msg18=最多只能引入三筆財報
						return CommonAPI.showMessage(i18n.lms1700m01["ui_lms1700.msg18"]);
					}
					//===
					var nowCurr = "";
					var nowUnit = "";
					for (var i = 0; i < rows.length; i++) {
			            if (rows[i] != "") {
			            	var data = gridF101M01A.getRowData(rows[i]);
			            	if(nowCurr == ''){
			            		nowCurr = data.curr;
			            	}else{
			            		if(nowCurr != data.curr){
			            			return CommonAPI.showMessage(i18n.lms1700m01["ui_lms1700.msg19"]);
			            		}
			            	}
			            	if(nowUnit == ''){
			            		nowUnit = data.amtUnit;
			            	}else{
			            		if(nowUnit != data.amtUnit){
			            			return CommonAPI.showMessage(i18n.lms1700m01["ui_lms1700.msg19"]);
			            		}
			            	}
			            }
					}
					//===
					var list = [];
					for (var i = 0; i < rows.length; i++) {
			            if (rows[i] != "") {
			            	var data = gridF101M01A.getRowData(rows[i]);
			            	list.push(data.mainId);
			            }
					}
					$.thickbox.close();
					my_dfd.resolve(	{'f101m01a_mainIds':list.join("|") });				
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
	    });
		return my_dfd.promise();
	}	
	function chose_ratioNo(){
		var my_dfd = $.Deferred();
		
		if(true){
			$("[name=finRatio]:checked").removeAttr("checked");			
			$("[name=finRatio].ratio_default").attr("checked", "checked");
		}
		$("#div_finRatio").thickbox({ // 使用選取的內容進行彈窗
	        title: '',
	        width: 680, height: 340, align: "center", valign: "bottom", modal: false, i18n: i18n.def,
            buttons: {
                "sure": function(){
                	var _finRatioArr = $("[name=finRatio]:checked");
                	if(_finRatioArr.length > 4 ){
                		//ui_lms1700.msg17=選項不能多於四筆
			    		return CommonAPI.showMessage(i18n.lms1700m01["ui_lms1700.msg17"]);
			    	}else if(_finRatioArr.length == 0){
			    		//action_005=請先選取一筆以上之資料列
			    		return CommonAPI.showMessage(i18n.def.action_005);
			    	}
                	var ratioNo1 = "";
                	var ratioNo2 = "";
                	var ratioNo3 = "";
                	var ratioNo4 = "";
                	if(true){
                		var _tempArr = [];
                		$.each(defRatioArr, function(idx, val) {	
            				$("[name=finRatio][value="+val+"].ratio_default:checked").map(function(){
            					_tempArr.push( $(this).val() );
            				});	        	
            			});
                		
                		if(true){                			
                			$("[name=finRatio].ratio_not_default:checked").map(function(){ 
                    			_tempArr.push( $(this).val() ); 
                    		});
                		}
                		
                		var addcnt = (4 - _tempArr.length);                    	
                    	for(var i=0;i<addcnt;i++){
                    		_tempArr.push("");  
                    	}
                    	ratioNo1 = _tempArr[0];
                    	ratioNo2 = _tempArr[1];
                    	ratioNo3 = _tempArr[2];
                    	ratioNo4 = _tempArr[3];
                	}                	
			     	
                    $.thickbox.close();
                    
                    my_dfd.resolve(	{'ratioNo1':ratioNo1
                    				,'ratioNo2':ratioNo2
                    				,'ratioNo3':ratioNo3
                    				,'ratioNo4':ratioNo4});
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
	    });
    	    		
    	return my_dfd.promise();
	}
	
});
