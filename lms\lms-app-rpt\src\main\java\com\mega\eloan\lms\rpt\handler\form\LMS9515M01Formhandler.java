/* 
 * LMS9515M01Formhandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.handler.form;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.TreeMap;

import javax.annotation.Resource;

import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.enums.BranchTypeEnum;
import com.mega.eloan.common.enums.UnitTypeEnum;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.model.L784S01A;
import com.mega.eloan.lms.model.L784S07A;
import com.mega.eloan.lms.rpt.pages.LMS9515V01Page;
import com.mega.eloan.lms.rpt.service.LMS9515R01RptService;
import com.mega.eloan.lms.rpt.service.LMS9515Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 管理報表
 * </pre>
 * 
 * @since 2011/11/25
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/11/25,jessica,new
 *          </ul>
 */

@Scope("request")
@Controller("lms9515m01formhandler")
public class LMS9515M01Formhandler extends AbstractFormHandler {
	//復原TFS J-111-0636_05097_B100X
	@Resource
	LMS9515Service service9515;

	@Resource
	CodeTypeService codetypeService;

	@Resource
	BranchService branchService;

	@Resource
	LMS9515R01RptService lms9515r01rptService;

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getStartEndDate(PageParameters params)
			throws CapException {
		Date dateStartDate = LMSUtil.getExMonthFirstDay(1);
		Date dateEndDate = LMSUtil.getExMonthLastDay(1);
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set("startDate", TWNDate.toAD(dateStartDate));
		result.set("endDate", TWNDate.toAD(dateEndDate));
		return result;
	}

	/**
	 * <pre>
	 * 產生(報表)
	 * 1. 授信契約已逾期控制表      
	 * 2. 已敘做授信案件清單(代理程式：ALMS18001)
	 * 3. 信保案件未動用屆期清單                                          
	 * 4. 授信契約產生主辦聯貸案一覽表(國金部用)(Word：LMSDoc7.dot)
	 * 5. 授信案件統計表(授管處、企劃處用)(Excel：無範本檔)
	 * 6. 營運中心每日授權外授信案件清單(代理程式：ALMS38002)
	 * 7. 常董會及申報案件統計表                                    
	 * 8. 各級授權範圍內承做授信案件統計表  
	 * 
	 * 10. 過去半年內董事會（或常董會）權限核定之企業戶授信案件名單
	 * @param params PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 * </pre>
	 * 
	 * @throws IOException
	 */

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult transportRpt(PageParameters params)
			throws CapException, IOException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		List<Map<String, Object>> list = null;

		// 畫面傳入分行代碼 (當需指定分行時使用)
		String brNo = user.getUnitNo();
		Date dataStartDate = null;
		Date dataEndDate = null;
		Date dataDate = null;
		// searchEnDate = CapDate.for
		// 資料夾名
		String listName = "";
		HashMap<String, String> param = new HashMap<String, String>();
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS9515V01Page.class);
		// 取得登入所在分行代碼
		String searchAction = params.getString("searchAction");
		List<L784S07A> l784s07aList = null;

		int action = Util.parseInt(searchAction);
		switch (action) {
		case 1:
			// 兩個月前
			dataStartDate = LMSUtil.getExMonthFirstDay(-2);
			dataEndDate = LMSUtil.getExMonthLastDay(-1);
			list = service9515.findType1ByBrNoAndDate(brNo, dataStartDate,
					dataEndDate, UtilConstants.Casedoc.DocType.個金, null);
			listName = "pdf1";
			// 產生報表
			lms9515r01rptService.generateReport(list, action, listName, brNo,
					dataStartDate, dataEndDate,
					UtilConstants.Casedoc.DocType.個金);
			break;
		case 2:
			String byAccGroup = params.getString("byAccGroup");
			//J-112-0JJJ_05097_B1001 Web e-Loan日本地區分行簽報書新增管理行授權內案件權限及相關修改
			String byCountryHead = params.getString("byCountryHead");
			
			dataStartDate = TWNDate.valueOf(params.getString("startDate")
					+ "-01");
			dataEndDate = TWNDate.valueOf(params.getString("startDate")
					+ "-"
					+ CapDate.getDayOfMonth(
							TWNDate.toAD(dataStartDate).split("-")[0], TWNDate
									.toAD(dataStartDate).split("-")[1]));
			listName = "pdf2";
			// OvUnitNo 取得登入所在分行代碼 只撈授權內
			List<L784S01A> l784s01List = null;
			if (UnitTypeEnum.營運中心.getCode().equals(
					UnitTypeEnum.convertToUnitType(user.getUnitType())
							.getCode())) {
				l784s01List = service9515.findType2ByAreaAndDate(brNo,
						CreditDocStatusEnum.海外_已核准.getCode(),
						UtilConstants.Casedoc.DocKind.授權外,
						FlowDocStatusEnum.已核准.getCode(), dataStartDate,
						dataEndDate);
			} else {
				String rptMode = "1";
				if (Util.equals(byAccGroup, "Y")) {
					rptMode = "2"; // Y01、Z01有勾Y時走總行模式，其他走分行模式
				}else if (Util.equals(byCountryHead, "Y")) {
					//J-112-0JJJ_05097_B1001 Web e-Loan日本地區分行簽報書新增管理行授權內案件權限及相關修改
					rptMode = "3"; // 0A7有勾Y時走管理行模式，包含當地所有管理行授權內案件
				} else {
					rptMode = "1"; // 分行模式
				}
				// brNo 不能亂改，因為後續會寫到授權TABLE有關
				// L784A01A，所以0E1、0E3要到service9515.findType2ByBrNoAndDate才能改
				l784s01List = service9515.findType2ByBrNoAndDate(rptMode, brNo,
						dataStartDate, dataEndDate,
						CreditDocStatusEnum.海外_已核准.getCode(),
						UtilConstants.Casedoc.DocKind.授權內,
						FlowDocStatusEnum.已核准.getCode());
			}
			// 產生報表
			lms9515r01rptService.generateReport(l784s01List, action, listName,
					brNo, dataStartDate, dataEndDate, "");
			break;
		case 3:
			// dataStartDate = this.getExMonthFirstDay(-1);
			listName = "pdf3";
			String lms9515v01From3 = params.getString("lms9515v01From3");
			JSONObject jobject = JSONObject.fromObject(lms9515v01From3);
			dataStartDate = TWNDate.valueOf(jobject.getString("sdateNw3a"));
			dataEndDate = TWNDate.valueOf(jobject.getString("edateNw3a"));
			// 登入所在分行(ovUnitNo)
			list = service9515.findType3ByBrNoAndDate(brNo,
					TWNDate.toAD(dataStartDate), TWNDate.toAD(dataEndDate),
					UtilConstants.Casedoc.DocType.個金);
			// 產生報表
			lms9515r01rptService.generateReport(list, action, listName, brNo,
					dataStartDate, dataEndDate,
					UtilConstants.Casedoc.DocType.個金);
			break;
		case 4:
			// TODO:
			// 此功能當初再寫授信管理報表時JASON有說日後才G補上(授信契約產生主辦聯貸案一覽表(國金部用)(Word：LMSDoc7.dot))

			break;
		case 5:
			String lms9515v01From5 = params.getString("lms9515v01From5");
			JSONObject jobject5 = JSONObject.fromObject(lms9515v01From5);

			dataStartDate = TWNDate
					.valueOf(jobject5.getString("sdate") + "-01");
			dataEndDate = TWNDate.valueOf(jobject5.getString("edate") + "-01");
			dataEndDate = TWNDate.valueOf(jobject5.getString("edate")
					+ "-"
					+ CapDate.getDayOfMonth(TWNDate.toAD(dataEndDate)
							.split("-")[0], TWNDate.toAD(dataEndDate)
							.split("-")[1]));

			List<Map<String, Object>> list5 = new ArrayList<Map<String, Object>>();
			String otherCondition = null;
			List<IBranch> ovUnitNolist = null;
			if (Integer.parseInt(TWNDate.toAD(TWNDate.valueOf(dataStartDate))
					.replace("-", "")) > Integer.parseInt(TWNDate.toAD(
					TWNDate.valueOf(dataEndDate)).replace("-", ""))) {
				result.set("check", false);
				result.set("checkDate", true);
				param.put(
						"msg",
						pop.getProperty("L784M01a.startDate")
								+ pop.getProperty("L784M01a.error2"));
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0025", param), getClass());
			}

			// 海外分行(931)
			ovUnitNolist = null;
			otherCondition = "ELF447_SYSTYPE IN ('CLS','LMS')";
			ovUnitNolist = branchService
					.getBranchByUnitType(BranchTypeEnum.海外分行.getCode());
			list = this.getUnitNoStr(ovUnitNolist, TWNDate.toAD(dataStartDate),
					TWNDate.toAD(dataEndDate), otherCondition);
			// 金控總部(201)
			brNo = "201";
			// TODO: 金控總部 要自己撈以下有哪些分行代碼
			ovUnitNolist = null;
			ovUnitNolist = service9515.findBranch201();
			otherCondition = "ELF447_SYSTYPE IN ('CLS','LMS')";
			list = this.getUnitNoStr(ovUnitNolist, TWNDate.toAD(dataStartDate),
					TWNDate.toAD(dataEndDate), otherCondition);
			// 北一區營運中心
			ovUnitNolist = null;
			otherCondition = "(ELF447_SYSTYPE = 'LMS' OR (ELF447_SYSTYPE = 'CLS' AND ELF447_CASELEVEL <> '9' ) )";
			ovUnitNolist = branchService
					.getBranchOfGroup(UtilConstants.RPTREPORT.OPERATIONS.北一區營運中心);
			list = this.getUnitNoStr(ovUnitNolist, TWNDate.toAD(dataStartDate),
					TWNDate.toAD(dataEndDate), otherCondition);
			// 北二區營運中心
			ovUnitNolist = null;
			ovUnitNolist = branchService
					.getBranchOfGroup(UtilConstants.RPTREPORT.OPERATIONS.北二區營運中心);
			otherCondition = " (ELF447_SYSTYPE = 'LMS' OR (ELF447_SYSTYPE = 'CLS' AND ELF447_CASELEVEL <> '9' ) ) ";
			list = this.getUnitNoStr(ovUnitNolist, TWNDate.toAD(dataStartDate),
					TWNDate.toAD(dataEndDate), otherCondition);
			// 桃竹苗區營運中心
			ovUnitNolist = null;
			ovUnitNolist = branchService
					.getBranchOfGroup(UtilConstants.RPTREPORT.OPERATIONS.桃竹苗區營運中心);
			otherCondition = " (ELF447_SYSTYPE = 'LMS' OR (ELF447_SYSTYPE = 'CLS' AND ELF447_CASELEVEL <> '9' ) ) ";
			list = this.getUnitNoStr(ovUnitNolist, TWNDate.toAD(dataStartDate),
					TWNDate.toAD(dataEndDate), otherCondition);
			// 中區營運中心
			ovUnitNolist = null;
			ovUnitNolist = branchService
					.getBranchOfGroup(UtilConstants.RPTREPORT.OPERATIONS.中區營運中心);
			otherCondition = " (ELF447_SYSTYPE = 'LMS' OR (ELF447_SYSTYPE = 'CLS' AND ELF447_CASELEVEL <> '9' ) ) ";
			list = this.getUnitNoStr(ovUnitNolist, TWNDate.toAD(dataStartDate),
					TWNDate.toAD(dataEndDate), otherCondition);
			// 南區營運中心
			ovUnitNolist = null;
			ovUnitNolist = branchService
					.getBranchOfGroup(UtilConstants.RPTREPORT.OPERATIONS.南區營運中心);
			otherCondition = " (ELF447_SYSTYPE = 'LMS' OR (ELF447_SYSTYPE = 'CLS' AND ELF447_CASELEVEL <> '9' ) ) ";
			list = this.getUnitNoStr(ovUnitNolist, TWNDate.toAD(dataStartDate),
					TWNDate.toAD(dataEndDate), otherCondition);
			if (list != null) {
				list5.addAll(list);
			}
			String listName5 = "excel";
			// TODO jessica沒寫啦
			service9515.tranSportExcel(list5, listName5);
			break;
		case 6:
			// benDate = params.getString("startDate");
			dataStartDate = CapDate.getCurrentTimestamp();
			Calendar cal = Calendar.getInstance();
			cal.setTime(dataStartDate);
			cal.add(Calendar.DAY_OF_MONTH, -1);
			dataStartDate = cal.getTime();
			listName = "pdf6";
			brNo = user.getUnitNo();
			list = service9515.findType6ByBrNoAndDate(brNo,
					TWNDate.toAD(dataStartDate),
					UtilConstants.Casedoc.DocType.企金個金,
					UtilConstants.Casedoc.DocKind.授權外);

			// 產生報表
			lms9515r01rptService.generateReport(list, action, listName, brNo,
					dataStartDate, dataEndDate,
					UtilConstants.Casedoc.DocType.企金個金);
			break;
		case 7:
			listName = "pdf7";
			dataDate = TWNDate.valueOf(params.getString("startDate") + "-01");
			dataStartDate = TWNDate
					.valueOf(TWNDate.toAD(dataDate).split("-")[0] + "-01-01");
			dataEndDate = TWNDate.valueOf(TWNDate.toAD(dataDate).split("-")[0]
					+ "-12-31");
			Map<String, Object> l784m01aMap = service9515.selL784M01A("7",
					TWNDate.toAD(dataDate).split("-")[0]);
			String mainId = IDGenerator.getRandomCode();
			String randomCode = IDGenerator.getRandomCode();
			if (l784m01aMap == null) {
				mainId = IDGenerator.getRandomCode();
				randomCode = IDGenerator.getRandomCode();
			} else {
				mainId = Util.trim(l784m01aMap.get("MAINID"));
				randomCode = Util.trim(l784m01aMap.get("RANDOMCODE"));
			}

			// 查詢ELCSECNT再新增至L784S07A
			l784s07aList = service9515.findType7ByBrNoAndDate(
					TWNDate.toTW(dataDate).split("/")[0], TWNDate
							.toTW(dataDate).split("/")[1],
					UtilConstants.Casedoc.DocType.企金個金, mainId);

			// 存進 sL784M01A
			service9515.saveFlieName(l784s07aList, null, action,
					user.getUnitNo(), dataStartDate, dataEndDate, mainId,
					UtilConstants.Casedoc.DocType.企金個金, randomCode);

			break;
		case 8:
			listName = "pdf8";
			dataStartDate = LMSUtil.getExMonthFirstDay(-1);
			brNo = user.getUnitNo();
			list = service9515.findType8ByBrNoAndDate(brNo, dataStartDate,
					UtilConstants.Casedoc.DocType.企金,
					UtilConstants.Casedoc.DocKind.授權內);
			// 產生報表
			lms9515r01rptService.generateReport(list, action, listName, brNo,
					dataStartDate, dataEndDate,
					UtilConstants.Casedoc.DocType.企金);
			break;
		case 9:
			listName = "LMSDOC20";
			dataStartDate = TWNDate.valueOf(params.getString("startDate")
					+ "-01");
			dataEndDate = TWNDate.valueOf(params.getString("startDate")
					+ "-"
					+ CapDate.getDayOfMonth(
							TWNDate.toAD(dataStartDate).split("-")[0], TWNDate
									.toAD(dataStartDate).split("-")[1]));
			List<IBranch> branchList = branchService.getAllBranch();
			// 依照組別去分 0~9
			List<IBranch> branchType0List = new ArrayList<IBranch>();
			List<IBranch> branchType1List = new ArrayList<IBranch>();
			List<IBranch> branchType2List = new ArrayList<IBranch>();
			List<IBranch> branchType3List = new ArrayList<IBranch>();
			List<IBranch> branchType4List = new ArrayList<IBranch>();
			List<IBranch> branchType5List = new ArrayList<IBranch>();
			List<IBranch> branchType6List = new ArrayList<IBranch>();
			List<IBranch> branchType7List = new ArrayList<IBranch>();
			List<IBranch> branchType8List = new ArrayList<IBranch>();
			// 國金部特別被歸在國外 列在第一筆 所以額外開個list紀錄
			List<IBranch> branchType9SList = new ArrayList<IBranch>();
			List<IBranch> branchType9List = new ArrayList<IBranch>();
			List<IBranch> removeList = new ArrayList<IBranch>();
			for (IBranch branch : branchList) {
				// 要排除的資料 "117","009","088","243","001","011","024","078" ,
				// Left(strtBrno,1) = "9"
				if ("117".equals(branch.getBrNo())
						|| "009".equals(branch.getBrNo())
						|| "088".equals(branch.getBrNo())
						|| "243".equals(branch.getBrNo())
						|| "001".equals(branch.getBrNo())
						|| "011".equals(branch.getBrNo())
						|| "024".equals(branch.getBrNo())
						|| "078".equals(branch.getBrNo())) {
					removeList.add(branch);
				} else if (branch.getBrNo().startsWith("9")) {
					removeList.add(branch);
				}
			}
			branchList.removeAll(removeList);
			// 先將國內的放入branchType1Map 還有將國外的放入branchType2Map
			for (IBranch branch : branchList) {
				if ("007".equals(branch.getBrNo())
						|| "201".equals(branch.getBrNo())
						|| "149".equals(branch.getBrNo())) {
					branchType0List.add(branch);
				} else if (UtilConstants.BrNoType.國內.equals(branch
						.getBrNoFlag())) {
					if ("931".equals(branch.getBrnGroup())) {
						branchType2List.add(branch);
					} else if ("932".equals(branch.getBrnGroup())) {
						branchType3List.add(branch);
					} else if ("933".equals(branch.getBrnGroup())) {
						branchType4List.add(branch);
					} else if ("920".equals(branch.getBrnGroup())
							|| "934".equals(branch.getBrnGroup())) {
						branchType5List.add(branch);
					} else if ("922".equals(branch.getBrnGroup())
							|| "935".equals(branch.getBrnGroup())
							|| "081".equals(branch.getBrNo())) {
						branchType6List.add(branch);
					} else if ("025".equals(branch.getBrNo())) {
						branchType9SList.add(branch);
					} else {
						branchType1List.add(branch);
					}
				} else {
					branchType9List.add(branch);
				}
			}

			String yearBgn = "";
			String yearEnd = "";
			String queryBgn = "";
			String queryEnd = "";

			// public List<Map<String, Object>> findType9BystartYMendYM(String
			// TWYM,
			// String startTWYM, String endTWYM, String type)

			yearBgn = String
					.format("%03d",
							Integer.parseInt(TWNDate.toTW(dataStartDate).split(
									"/")[0]))
					+ "01";
			yearEnd = String
					.format("%03d",
							Integer.parseInt(TWNDate.toTW(dataStartDate).split(
									"/")[0]))
					+ String.format(
							"%02d",
							Integer.parseInt(TWNDate.toTW(dataStartDate).split(
									"/")[1]));

			queryBgn = String
					.format("%03d",
							Integer.parseInt(TWNDate.toTW(dataStartDate).split(
									"/")[0]))
					+ String.format(
							"%02d",
							Integer.parseInt(TWNDate.toTW(dataStartDate).split(
									"/")[1]));

			queryEnd = String
					.format("%03d",
							Integer.parseInt(TWNDate.toTW(dataStartDate).split(
									"/")[0]))
					+ String.format(
							"%02d",
							Integer.parseInt(TWNDate.toTW(dataStartDate).split(
									"/")[1]));

			list = service9515.findType9BystartYMendYM(queryBgn, queryEnd,
					yearBgn, yearEnd, "1");

			// 照平均扣分排
			/*
			 * list = service9515.findType9BystartYMendYM( String.format(
			 * "%03d", Integer.parseInt(TWNDate.toTW(dataStartDate).split(
			 * "/")[0])) + String.format("%02d", Integer.parseInt(TWNDate
			 * .toTW(dataStartDate).split("/")[1])), String.format( "%03d",
			 * Integer.parseInt(TWNDate.toTW(dataStartDate).split( "/")[0])) +
			 * "01", String.format( "%03d",
			 * Integer.parseInt(TWNDate.toTW(dataStartDate).split( "/")[0])) +
			 * String.format("%02d", Integer.parseInt(TWNDate
			 * .toTW(dataStartDate).split("/")[1])), "1");
			 */

			Map<String, Map<String, String>> map01 = new LinkedHashMap<String, Map<String, String>>();
			map01 = this
					.setBrClassMapSort(map01, branchType0List, list, "AVGT");
			map01 = this
					.setBrClassMapSort(map01, branchType1List, list, "AVGT");
			map01 = this
					.setBrClassMapSort(map01, branchType2List, list, "AVGT");
			map01 = this
					.setBrClassMapSort(map01, branchType3List, list, "AVGT");
			map01 = this
					.setBrClassMapSort(map01, branchType4List, list, "AVGT");
			map01 = this
					.setBrClassMapSort(map01, branchType5List, list, "AVGT");
			map01 = this
					.setBrClassMapSort(map01, branchType6List, list, "AVGT");
			map01 = this
					.setBrClassMapSort(map01, branchType7List, list, "AVGT");
			map01 = this
					.setBrClassMapSort(map01, branchType8List, list, "AVGT");
			// 海外
			Map<String, Map<String, String>> map01Over = new LinkedHashMap<String, Map<String, String>>();
			map01Over = this.setBrClassMapSort(map01Over, branchType9SList,
					list, "AVGT");
			map01Over = this.setBrClassMapSort(map01Over, branchType9List,
					list, "AVGT");
			// 照總扣分排

			list = service9515.findType9BystartYMendYM(queryBgn, queryEnd,
					yearBgn, yearEnd, "2");
			/*
			 * list = service9515.findType9BystartYMendYM( String.format(
			 * "%03d", Integer.parseInt(TWNDate.toTW(dataStartDate).split(
			 * "/")[0])) + String.format("%02d", Integer.parseInt(TWNDate
			 * .toTW(dataStartDate).split("/")[1])), String.format( "%03d",
			 * Integer.parseInt(TWNDate.toTW(dataStartDate).split( "/")[0])) +
			 * "01", String.format( "%03d",
			 * Integer.parseInt(TWNDate.toTW(dataStartDate).split( "/")[0])) +
			 * String.format("%02d", Integer.parseInt(TWNDate
			 * .toTW(dataStartDate).split("/")[1])), "2");
			 */

			Map<String, Map<String, String>> map02 = new LinkedHashMap<String, Map<String, String>>();
			map02 = this
					.setBrClassMapSort(map02, branchType0List, list, "AVGF");
			map02 = this
					.setBrClassMapSort(map02, branchType1List, list, "AVGF");
			map02 = this
					.setBrClassMapSort(map02, branchType2List, list, "AVGF");
			map02 = this
					.setBrClassMapSort(map02, branchType3List, list, "AVGF");
			map02 = this
					.setBrClassMapSort(map02, branchType4List, list, "AVGF");
			map02 = this
					.setBrClassMapSort(map02, branchType5List, list, "AVGF");
			map02 = this
					.setBrClassMapSort(map02, branchType6List, list, "AVGF");
			map02 = this
					.setBrClassMapSort(map02, branchType7List, list, "AVGF");
			map02 = this
					.setBrClassMapSort(map02, branchType8List, list, "AVGF");
			// 海外
			Map<String, Map<String, String>> map02Over = new LinkedHashMap<String, Map<String, String>>();
			map02Over = this.setBrClassMapSort(map02Over, branchType9SList,
					list, "AVGF");
			map02Over = this.setBrClassMapSort(map02Over, branchType9List,
					list, "AVGF");
			service9515.tranSportExcel9(list, UtilConstants.Casedoc.DocType.企金,
					action, dataStartDate, dataEndDate, map01, map02,
					map01Over, map02Over, branchType0List, branchType1List,
					branchType2List, branchType3List, branchType4List,
					branchType5List, branchType6List, branchType7List,
					branchType8List, branchType9SList, branchType9List,
					listName);
			break;
		case 10:
			listName = "pdf10";
			String from3 = params.getString("lms9515v01From3");
			JSONObject object = JSONObject.fromObject(from3);
			dataStartDate = TWNDate.valueOf(object.getString("sdateNw3a"));
			dataEndDate = TWNDate.valueOf(object.getString("edateNw3a"));
			List<Map<String, Object>> listData = new ArrayList<Map<String, Object>>();
			listData = service9515.findType10ByBrNoAndDate(brNo,
					TWNDate.toAD(dataStartDate), TWNDate.toAD(dataEndDate));
			list = listData;
			// 產生報表
			lms9515r01rptService.generateReport(list, action, listName, brNo,
					dataStartDate, dataEndDate,
					UtilConstants.Casedoc.DocType.企金);
			break;
		}
		// EFD0018=INFO|執行成功|
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0018"));

		return result;
	}

	/**
	 * 將營業單位授信報案考核彙總表作排序
	 * 
	 * @param map01
	 * @param branchList
	 * @param list
	 * @return
	 */
	private Map<String, Map<String, String>> setBrClassMapSort(
			Map<String, Map<String, String>> map01, List<IBranch> branchList,
			List<Map<String, Object>> list, String field) {
		// 這裡只會有北一區 或是北二區 特定資料
		List<Map<String, Object>> list2 = new ArrayList<Map<String, Object>>();
		for (Map<String, Object> map : list) {
			for (IBranch branch : branchList) {
				if (branch.getBrNo().equals(Util.trim(map.get("BRANCHID")))) {
					BigDecimal number = LMSUtil.toBigDecimal(map.get(field));
					if (number.doubleValue() != 0) {
						map01 = this.setTFDataMap(map01, map, branch);
						list2.add(map);
					}
				}
			}
		}
		for (int brClass = 0; brClass <= 5; brClass++) {
			for (IBranch branch : branchList) {
				boolean result = true;
				for (Map<String, Object> map : list2) {
					if (branch.getBrNo().equals(Util.trim(map.get("BRANCHID")))) {
						result = false;
					}
				}
				if (result) {
					Map<String, Object> innMap = null;
					for (Map<String, Object> innMap2 : list) {
						if (Util.trim(innMap2.get("BRANCHID")).equals(
								branch.getBrNo())) {
							innMap = innMap2;
						}
					}
					map01 = this.setTFDataMap(map01, innMap, branch);
				}
			}
		}
		return map01;
	}

	/**
	 * 存放營業單位授信報案考核彙總表的平均扣分總扣分資訊
	 * 
	 * @param map01
	 * @param map
	 * @return
	 */
	private Map<String, Map<String, String>> setTFDataMap(
			Map<String, Map<String, String>> map01, Map<String, Object> map,
			IBranch branch) {
		if (map == null)
			map = new LinkedHashMap<String, Object>();
		Map<String, String> innMap = new LinkedHashMap<String, String>();
		innMap.put("TCOUNT", Util.trim(map.get("TCOUNT")));
		innMap.put("TITEMALL", Util.trim(map.get("TITEMALL")));
		innMap.put("FCOUNT", Util.trim(map.get("FCOUNT")));
		innMap.put("FITEMALL", Util.trim(map.get("FITEMALL")));
		innMap.put("AVGF", Util.trim(map.get("AVGF")));
		innMap.put("AVGT", Util.trim(map.get("AVGT")));
		innMap.put("BRCLASS", branch.getBrClass());
		innMap.put("BRNGROUP", branch.getBrnGroup());
		innMap.put("BRNAME", branch.getBrName());
		innMap.put("BRNO", branch.getBrNo());
		map01.put(branch.getBrNo(), innMap);
		return map01;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult saveL784S07AData(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString("mainId");
		String data = params.getString("data");
		JSONObject jsonData = JSONObject.fromObject(data);

		List<L784S07A> l784s07aList = service9515.findL784S07AByMainId(mainId);
		List<L784S07A> list = new ArrayList<L784S07A>();
		for (L784S07A l784s07a : l784s07aList) {
			String oid = l784s07a.getOid();
			if (jsonData.containsKey(oid)) {
				String dataInsert = jsonData.getString(oid);
				if (!"|".equals(dataInsert)) {
					String temp[] = dataInsert.split("\\|");
					l784s07a.setCItem12Rec(Util.parseInt(temp[0].replace(",",
							"").replace(".", "")));
					l784s07a.setCItem12Amt(temp.length > 1 ? LMSUtil
							.toBigDecimal(temp[1].replace(",", "").replace(".",
									"")) : null);
					l784s07a.setUpdateTime(CapDate.getCurrentTimestamp());
					l784s07a.setUpdater(user.getUserId());
					list.add(l784s07a);
				}

			}
		}
		service9515.savel784s07aList(list);
		// EFD0017 儲存成功
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0017"));
		return result;
	}

	/**
	 * map的 value排序方式 照升冪
	 * 
	 * @param branchTypeMap
	 * @return
	 */
	// private Map<IBranch, Integer> sortMapValue(Map<IBranch, Integer>
	// branchTypeMap) {
	// Map<IBranch, Integer> returnMap = new HashMap<IBranch, Integer>();
	// ArrayList<Entry<IBranch, Integer>> list = new ArrayList<Entry<IBranch,
	// Integer>>(
	// branchTypeMap.entrySet());
	//
	// Collections.sort(list, new Comparator<Map.Entry<IBranch, Integer>>() {
	// public int compare(Map.Entry<IBranch, Integer> o1,
	// Map.Entry<IBranch, Integer> o2) {
	// return (o1.getValue() - o2.getValue());
	// }
	// });
	//
	// for (Entry<IBranch, Integer> entry : list) {
	// returnMap.put(entry.getKey(), entry.getValue());
	// //System.out.println(entry.getKey() + "::::" + entry.getValue());
	// }
	// return returnMap;
	//
	// }

	/**
	 * 將需要用到的銀行都組成str字串
	 * 
	 * @param ovUnitNolist
	 * @return
	 * @throws CapException
	 */
	private List<Map<String, Object>> getUnitNoStr(List<IBranch> ovUnitNolist,
			String benDate, String endDate, String otherCondition)
			throws CapException {
//		StringBuffer strBuffer = new StringBuffer();
		if (ovUnitNolist != null) {
//			for (IBranch ib : ovUnitNolist) {
//				strBuffer.append(strBuffer.length() > 0 ? "," : "");
//				strBuffer.append("'" + Util.trim(ib.getBrNo()) + "'");
//			}
			return service9515.findType5ByBrNoAndDate(ovUnitNolist,
					benDate, endDate, otherCondition);
		}
		return null;
	}

	/**
	 * 7. 常董會及申報案件統計表 (查詢L748S07A裡此筆資料)
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL748s07a(PageParameters params)
			throws CapException {
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS9515V01Page.class);
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString("mainOid");
		L784S07A l784s07a = new L784S07A();
		l784s07a = service9515.findModelByOid(L784S07A.class, oid);
		String num = l784s07a.getApprMM();
		result.set("apprMM1", prop.getProperty("month.01"));
		result.set("apprMM2", prop.getProperty("month.02"));
		result.set("apprMM3", prop.getProperty("month.03"));
		result.set("apprMM4", prop.getProperty("month.04"));
		result.set("apprMM5", prop.getProperty("month.05"));
		result.set("apprMM6", prop.getProperty("month.06"));
		result.set("apprMM7", prop.getProperty("month.07"));
		result.set("apprMM8", prop.getProperty("month.08"));
		result.set("apprMM9", prop.getProperty("month.09"));
		result.set("apprMM10", prop.getProperty("month.10"));
		result.set("apprMM11", prop.getProperty("month.11"));
		result.set("apprMM12", prop.getProperty("month.12"));

		result.set("cItem1Rec" + num, l784s07a.getCItem1Rec());
		result.set("cItem1Amt" + num, l784s07a.getCItem1Amt());
		result.set("cItem2Rec" + num, l784s07a.getCItem2Rec());
		result.set("cItem2Amt" + num, l784s07a.getCItem2Amt());

		result.set("cItem3Rec" + num, l784s07a.getCItem3Rec());
		result.set("cItem3Amt" + num, l784s07a.getCItem3Amt());

		// result.set("cItem12Rec" + num, l784s07a.getCItem3Rec());
		// result.set("cItem3Amt" + num, l784s07a.getCItem3Amt());
		// result.set("cItem12Amt" + num, "22,000");

		result.set("cItem4Rec" + num, l784s07a.getCItem4Rec());
		result.set("cItem4Amt" + num, l784s07a.getCItem4Amt());
		result.set("cItem5Rec" + num, l784s07a.getCItem5Rec());
		result.set("cItem5Amt" + num, l784s07a.getCItem5Amt());
		result.set("cItem7Rec" + num, l784s07a.getCItem7Rec());
		result.set("cItem8Rec" + num, l784s07a.getCItem8Rec());
		result.set("cItem9Rec" + num, l784s07a.getCItem9Rec());
		result.set("cItem10Rec" + num, l784s07a.getCItem10Rec());
		result.set("cItem11Rec" + num, l784s07a.getCItem11Rec());

		result.set("brNo", branchService.getBranchName(l784s07a.getBrNo()));
		result.set("yy", l784s07a.getApprYY());
		result.set("yearTrue", num);

		return result;
	}

	/**
	 * 7. 常董會及申報案件統計表(儲存L784s07a)
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult saveL784s07a(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String oid = params.getString("mainOid");
		int sum = Util.parseInt(params.getString("sum"));
		BigDecimal sumAmt = new BigDecimal(params.getString("sumAmt"));
		L784S07A l784s07a = new L784S07A();
		l784s07a = service9515.findModelByOid(L784S07A.class, oid);
		l784s07a.setCItem12Rec(sum);
		l784s07a.setCItem12Amt(sumAmt);
		service9515.save(l784s07a);

		// EFD0017 儲存成功
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0017"));
		return result;
	}

	/**
	 * 7. 常董會及申報案件統計表([計算]合計 筆數和金額)
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult sum784s07(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();

		int i1 = Util.parseInt(params.getString("i1"));
		int i2 = Util.parseInt(params.getString("i2"));
		int i3 = Util.parseInt(params.getString("i3"));
		int i4 = Util.parseInt(params.getString("i4"));

		long j1 = Util.parseLong(params.getString("j1"));
		long j2 = Util.parseLong(params.getString("j2"));
		long j3 = Util.parseLong(params.getString("j3"));
		long j4 = Util.parseLong(params.getString("j4"));

		int iSum = i1 + i2 + i3 + i4;
		long jSumAmt = j1 + j2 + j3 + j4;
		result.set("sum", String.valueOf(iSum)); //配合弱掃修改，避免數字0被DOMPurify.sanitize變為空白，改傳字串
		result.set("sumAmt", String.valueOf(jSumAmt));

		return result;
	}

	/**
	 * 2.已敘做授信案件清單(搜尋此筆資料)
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL784S01a(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS9515V01Page.class);
		String oids = params.getString("mainOids");
		L784S01A l784s01a = null;
		if (oids.indexOf("|") == -1) {
			l784s01a = service9515.findModelByOid(L784S01A.class, oids);
			if ((l784s01a.getHqCheckDate() == null)) {
				result.set("hqCheckDate",
						TWNDate.toAD(CapDate.getCurrentTimestamp()));
			} else {
				result.set("hqCheckDate",
						TWNDate.toAD(l784s01a.getHqCheckDate()));
			}
			if (!"".equals(Util.trim(l784s01a.getHqCheckMemo()))) {
				result.set("hqCheckMemo", Util.trim(l784s01a.getHqCheckMemo()));
			} else {
				result.set("hqCheckMemo", pop.getProperty("hqCheckMemoDefault"));
			}
			result.set("oid", l784s01a.getOid());
		} else {
			result.set("hqCheckDate",
					TWNDate.toAD(CapDate.getCurrentTimestamp()));
			result.set("hqCheckMemo", pop.getProperty("hqCheckMemoDefault"));
			result.set("oids", oids);
		}
		return result;
	}

	/**
	 * 2.已敘做授信案件清單-授管處儲存資料
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult saveRemarkNotes(PageParameters params)
			throws CapException {
		String oids = params.getString("oids", "");
		CapAjaxFormResult result = new CapAjaxFormResult();
		L784S01A l784s01a = null;
		String lms9515v01From2 = params.getString("lms9515v01From2");
		JSONObject jobject2 = JSONObject.fromObject(lms9515v01From2);
		String hqCheckMemo = jobject2.getString("hqCheckMemo");
		String hqCheckDate = jobject2.getString("hqCheckDate");
		try {
			if (oids.indexOf("|") == -1) {
				l784s01a = service9515.findModelByOid(L784S01A.class, oids);
				l784s01a.setHqCheckDate(CapDate.parseDate(hqCheckDate));
				l784s01a.setHqCheckMemo(hqCheckMemo);
				service9515.save(l784s01a);
			} else {
				String temp[] = oids.split("\\|");
				for (String oid : temp) {
					l784s01a = service9515.findModelByOid(L784S01A.class, oid);
					l784s01a.setHqCheckDate(CapDate.parseDate(hqCheckDate));
					l784s01a.setHqCheckMemo(hqCheckMemo);
					service9515.save(l784s01a);
				}
			}
		} catch (Exception e) {
			throw new CapMessageException(RespMsgHelper.getMessage(RespMsgHelper.getMainMessage("EFD0025")),
					getClass());
		}

		// EFD0017 儲存成功
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0017"));
		return result;
	}

	/**
	 * 搜尋分行
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 **/
	public IResult queryBranch(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String reportType = params.getString("reportType", "");
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Map<String, String> m = new TreeMap<String, String>();

		if (UtilConstants.RPTREPORT.REPORTTYPE.營運中心每日授權外授信案件清單
				.equals(reportType)) {
			String brName = branchService.getBranchName(user.getUnitNo());
			String brCode = user.getUnitNo();
			m.put(brCode, brName);
		} else {
			List<IBranch> bank = branchService.getBranchOfGroup(user
					.getUnitNo());
			bank.add(branchService.getBranch(user.getUnitNo()));

			if (UtilConstants.RPTREPORT.OPERATIONS.北一區營運中心.equals(user
					.getUnitNo())
					|| UtilConstants.RPTREPORT.OPERATIONS.北二區營運中心.equals(user
							.getUnitNo())
					|| UtilConstants.RPTREPORT.OPERATIONS.桃竹苗區營運中心.equals(user
							.getUnitNo())
					|| UtilConstants.RPTREPORT.OPERATIONS.中區營運中心.equals(user
							.getUnitNo())
					|| UtilConstants.RPTREPORT.OPERATIONS.南區營運中心.equals(user
							.getUnitNo())) {
				String[] s = { BranchTypeEnum.海外分行.getCode() };
				List<IBranch> bank2 = branchService.getBranchByUnitType(s);

				for (IBranch b : bank2) {
					String brName = Util.trim(b.getBrName());
					String brCode = b.getBrNo();
					m.put(brCode, brName);
				}
			}
			for (IBranch b : bank) {
				String brName = Util.trim(b.getBrName());
				String brCode = b.getBrNo();
				m.put(brCode, brName);
			}
		}
		CapAjaxFormResult bankList = new CapAjaxFormResult(m);
		result.set("item", bankList);
		return result;
	}

	/**
	 * 搜尋 報表類型
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 **/
	public IResult queryReportType(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String brNo = user.getUnitNo();
		IBranch branch = branchService.getBranch(brNo);
		// TreeMap排序
		// lms9515v01_proPerty 管理報表種類
		Map<String, String> items = codetypeService
				.findByCodeType("lms9515v01_proPerty");
		Map<String, String> m = new TreeMap<String, String>();
		for (String key : items.keySet()) {
			String value = (String) items.get(key);

			switch (Util.parseInt(brNo)) {
			// 授管處
			case 918:
				if ("2".equals(key)) {
					m.put(key, value);
				}
				// if ("5".equals(key)) {
				// m.put(key, value);
				// }
				if ("6".equals(key)) {
					m.put(key, value);
				}
				if ("7".equals(key)) {
					m.put(key, value);
				}
				if ("8".equals(key)) {
					m.put(key, value);
				}
				if ("9".equals(key)) {
					m.put(key, value);
				}
				if ("10".equals(key)) {
					m.put(key, value);
				}
				break;
			// 營運中心
			case 931:
				if ("2".equals(key)) {
					m.put(key, value);
				}
				if ("6".equals(key)) {
					m.put(key, value);
				}
				break;
			case 932:
				if ("2".equals(key)) {
					m.put(key, value);
				}
				if ("6".equals(key)) {
					m.put(key, value);
				}
				break;
			case 933:
				if ("2".equals(key)) {
					m.put(key, value);
				}
				if ("6".equals(key)) {
					m.put(key, value);
				}
				break;
			case 934:
				if ("2".equals(key)) {
					m.put(key, value);
				}
				if ("6".equals(key)) {
					m.put(key, value);
				}
				break;
			case 935:
				if ("2".equals(key)) {
					m.put(key, value);
				}
				if ("6".equals(key)) {
					m.put(key, value);
				}
				break;
			// 分行
			default:
				if ("1".equals(key)) {
					m.put(key, value);
				}
				if ("2".equals(key)) {
					m.put(key, value);
				}
				if ("3".equals(key)) {
					m.put(key, value);
				}

				if (isForeignbranch(branch) && "10".equals(key)) {
					m.put(key, value);
				}
				break;
			}
		}

		;
		CapAjaxFormResult type = new CapAjaxFormResult(m);
		result.set("item", type);
		return result;
	}

	/**
	 * 刪除
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult delete(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String searchAction = params.getString("searchAction");
		int i = Util.parseInt(searchAction);
		String listName = "";
		switch (i) {
		case 1:
			listName = "pdf1";
			break;
		case 2:
			listName = "pdf2";
			break;
		case 3:
			listName = "pdf3";
			break;
		}

		String[] oids = params.getStringArray("oids");
		if (oids.length > 0) {
			if (service9515.delete(oids, listName)) {
				// EFD0019=INFO|刪除成功|
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0019"));
			} else {
				// 判斷「LMS.L784M01A.hqCheckDate」，若已有值則不可刪除，並跳出訊息窗「此報表授管處已完成備查。」
				result.set("hqCheckDate", "true");
			}
		}

		return result;
	}

	/**
	 * 傳送授管處
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult sendDocTypeReport(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString("oid");
		if (service9515.cheaksendLastTime(oid)) {
			// 此報表已傳送過
			result.set("sendLastTime", true);
		} else {
			// 傳送成功
		}

		return result;
	}

	// 判斷是否為海外子母行
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getAccGroup(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String unitNo = Util.trim(params.getString("unitNo"));

		if (Util.notEquals(unitNo, "")) {
			String accGroup = branchService.getBranch(unitNo).getAccGroup();
			String unitType = branchService.getBranch(unitNo).getUnitType();
			if (Util.notEquals(accGroup, "")
					&& (unitType.equals("Q") || unitType.equals("R"))) {
				result.set("accGroup", "Y");
			} else {
				result.set("accGroup", "");
			}
		} else {
			result.set("accGroup", "");
		}
		
		//J-112-0JJJ_05097_B1001 Web e-Loan日本地區分行簽報書新增管理行授權內案件權限及相關修改
		result.set("countryType", branchService.getBranch(unitNo).getCountryType());

		return result;
	}

	private boolean isForeignbranch(IBranch branch) {
		String br = branch.getUnitType();
		if (BranchTypeEnum.海外分行.isEquals(br)
				|| BranchTypeEnum.海外總行澳洲加拿大.isEquals(br)
				|| BranchTypeEnum.海外總行泰國.isEquals(br)
				|| BranchTypeEnum.大陸分行.isEquals(br)
				|| BranchTypeEnum.海外分行當地有總行.isEquals(br)) {// 海外分行
			return true;
		}
		return false;
	}
}
