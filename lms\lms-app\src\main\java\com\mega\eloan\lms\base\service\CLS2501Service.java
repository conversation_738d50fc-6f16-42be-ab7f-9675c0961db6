package com.mega.eloan.lms.base.service;

/* 
 * LMS140MService.java
 * 
 * Copyright (c) 2011-2013 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
import java.io.IOException;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.model.C250M01A;
import com.mega.eloan.lms.model.C250M01E;

/**
 * <pre>
 * 可疑代辦案件註記作業
 * </pre>
 * 
 * @since 2014/08/28
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
public interface CLS2501Service extends AbstractService {

	/**
	 * 刪除可疑代辦案件註記主檔資料 根據所選的oid
	 */
	boolean deleteC250M01As(String[] oids);

	/**
	 * 儲存C250M01E．案件簽章欄檔
	 */
	public void saveC250M01EList(List<C250M01E> list);

	/**
	 * 查詢 C250M01E．案件簽章欄檔
	 */
	public C250M01E findC250M01E(String mainId, String branchType,
			String branchId, String staffNo, String staffJob);

	/**
	 * 其它到結案所用的flow
	 */
	public void flowAction(String mainOid, C250M01A model, boolean setResult,
			boolean resultType, boolean upMis) throws Throwable;

	public void deleteC250M01Es(List<C250M01E> c250m01es, boolean isAll);

	public void saveC250M01AForBatchCheck(C250M01A model, String userId, String unitNo);

	public String getEjcicB29InquiryData(String c250m01a_oid);

	public List<Map<String, String>> getOverduePaymentRecordByC250M01A(String oid) throws JsonParseException, JsonMappingException, IOException;
}