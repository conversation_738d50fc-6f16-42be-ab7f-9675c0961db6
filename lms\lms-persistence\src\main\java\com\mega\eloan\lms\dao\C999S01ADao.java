/* 
 * C999S01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;


import com.mega.eloan.lms.model.C999S01A;


/** 個金約據書產品種類檔 **/
public interface C999S01ADao extends IGenericDao<C999S01A> {

	C999S01A findByOid(String oid);
	
	List<C999S01A> findByMainId(String mainId);

	List<C999S01A> findByIndex01(String mainId, String uid);

	List<C999S01A> findByIndex02(String mainId, String cntrNo, String prodKind, String subjCode);
	
	List<C999S01A> findByCntrNo(String CntrNo);
	
	List<C999S01A> findByCustIdDupId(String custId,String DupNo);
}