//editor default config
$.extend(CKEDITOR.config, {
    //toolbar_Full : [['Source', '-', 'Save', 'NewPage', 'Preview', '-', 'Templates'], ['Cut', 'Copy', 'Paste', 'PasteText', 'Paste<PERSON>rom<PERSON>ord', '-', '<PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'], ['Undo', 'Red<PERSON>', '-', 'Find', 'Replace', '-', 'SelectAll', 'RemoveFormat'], ['Form', 'Checkbox', 'Radio', 'TextField', 'Textarea', 'Select', 'Button', 'ImageButton', 'HiddenField'], '/', ['Bold', 'Italic', 'Underline', 'Strike', '-', 'Subscript', 'Superscript'], ['NumberedList', 'BulletedList', '-', 'Outdent', 'Indent', 'Blockquote', 'CreateDiv'], ['JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock'], ['BidiLtr', 'BidiRtl'], ['<PERSON>', 'Unlink', 'Anchor'], ['Image', 'Flash', 'Table', 'HorizontalRule', 'Smiley', 'SpecialChar', 'PageBreak', 'Iframe'], '/', ['Styles', 'Format', 'Font', 'FontSize'], ['TextColor', 'BGColor'], ['Maximize', 'ShowBlocks', '-', 'About']],
	toolbarCanCollapse: false,
    resize_enabled: true,
    disableObjectResizing: false,
    toolbar_TwoIcons: [[  'Cut', 'Copy', 'Paste', 'PasteFromWord', '-', 'Print', '-','Undo', 'Redo', '-', 'Find', 'Replace', '-', 'SelectAll', '-','Table', '-', 'Bold', 'Italic', 'Underline', 'Strike', 'FontSize', 'TextColor', 'BGColor', '-', 'Outdent', 'Indent', '-', 'NumberedList', 'BulletedList', '-', 'JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock', '-', 'Link', 'Unlink',/*'Image',*/ 'InsertImage', '-'/*,'LocalSave'*/, '-','RemoveAll','-','Source','lineheight']],
    toolbar: 'TwoIcons',
    enterMode : CKEDITOR.ENTER_BR,
	extraPlugins:"RemoveAll,InsertImage,lineheight,tableresize"
});



//ckeditor dialog config
CKEDITOR.on('dialogDefinition', function(ev){
    var dialogName = ev.data.name;
    var dialogDefinition = ev.data.definition;
    
    if (dialogName == 'link') {
        dialogDefinition.removeContents('advanced');
        dialogDefinition.removeContents('target');
    }
    if (dialogName == 'image') {
    
        dialogDefinition.removeContents('advanced');
        // dialogDefinition.removeContents('info');
        dialogDefinition.removeContents('Link');
        
    }
    // ilog.debug(dialogDefinition)
});

function isDOM(obj){
	if( typeof HTMLElement === 'object' ){
		return obj instanceof HTMLElement;
	}else{
		return obj && typeof obj === 'object' && obj.nodeType === 1 && typeof obj.nodeName === 'string';
	}
}

function removeSelfAndChildStyle(obj){
	//要移除的font
	var rmTagArr = ['font'];
	$obj = $(obj);
	for(var a = 0; a < rmTagArr.length; a++){
		if($obj.parent().is(rmTagArr[a])){
			$obj.unwrap();
			break;
		}
	}
	//要移除的css
	var rmAttrArr = ['font-size','font-family','color','background-color'];
	for(var b = 0; b < rmAttrArr.length; b++){
		if(obj.style[rmAttrArr[b]] !== undefined){
			obj.style[rmAttrArr[b]] = '';
		}
	}
	//找到自己的兒子們
	var allChilds = obj.childNodes;
	for(var j = 0; j < allChilds.length; j++) {
		//若兒子也是DOM物件跑遞迴把所有向下DOM物件拔掉css、font
		if(isDOM(allChilds[j])){
			removeSelfAndChildStyle(allChilds[j]);
		}
	}
}

/*將ckeditor貼上表格時自動將框線粗細設為1*/
CKEDITOR.on('instanceReady', function(ev) {
	ev.editor.on('paste', function(evt) {
		//加上頭尾<div>，以免格式有闕漏時造成資料不見(word會發生)
		var cleanString = '';
		var tempObj = $('<div>' + evt.data['html'] + '</div>');
		var domNodes = tempObj[0].childNodes;//取得原來的資料
		for(var i=0;i<domNodes.length;i++){
			//跑迴圈將一個個的node進行判斷是否要移除多餘的css、font
			if(isDOM(domNodes[i])){
				removeSelfAndChildStyle(domNodes[i]);
				cleanString += domNodes[i].outerHTML || '';
			}else{
				cleanString += domNodes[i].nodeValue || '';
			}
		}
		evt.data['html'] = cleanString;
		
		var patt=/<table([^>]*[^/])>/gi;
		var m = evt.data['html'].match(patt);		
		for(var i=0;m&&i<m.length;i++){			
			var borderAttr = $(m[i]).attr('border');
			if(!borderAttr||borderAttr < 1){
				evt.data['html'] = evt.data['html'].replace(m[i],$(m[i]).attr('border',1)[0].outerHTML.replace('</table>',''));
			}
		}
		
		// 將input text轉換成顯示值
		var patt=/<input([^>]*[^/])>/gi;
		var m = evt.data['html'].match(patt);
		for(var i=0;m&&i<m.length;i++){	
			if( $(m[i]).attr('type') == "text" ){
				evt.data['html'] = evt.data['html'].replace(m[i],$(m[i]).attr('value') );
			}
		}
		evt.data['html']='<P>'+evt.data['html']+'</p>';
	}, null, null, 11);
	
	//強制將p tag加上css，因為inet列印時無法使用css file
	ev.editor.dataProcessor.htmlFilter.addRules({
	    elements :{
	        p : function( element ){
				if ( !element.attributes.style ){
		        	element.attributes.style = 'margin:0px;padding:0px;font-family: 新細明體;font-size: 16px;';
				}
	        }
	    }
	});
});

(function($){
    var create = $.fn.ckeditor;
    $.fn.ckeditor = function(){
        var $this = this, tmp = create.apply($this, arguments), randomID = "ckfileupload" + parseInt(Math.random() * 1000, 10);
        tmp.ckeditorGet().addCommand('InsertImageCmd', {
            exec: function(editor){
            	var dialogId = "insertImageDialog" + $this.attr("id");
                var imgDialog = $("#"+dialogId);
                if (!imgDialog.size()) {
                	imgDialog = $("<div id=\"" + dialogId + "\" title=\"" + (i18n && i18n.def.insertfile || "請選擇需插入之檔案") + "\" style=\"display:none\"><input type=\"file\" id=\"inserImgUploadFile\" name=\"inserImgUploadFile\" /></div>").appendTo("body");
                }
                imgDialog.thickbox({
                    width: 280,
                    height: 70,
                    align: "right",
                    valign: "bottom",
                    buttons: (function(){
                        var b = {};
                        b[i18n && i18n.def.uploadbutton || "上傳"] = function(){
                        	//var uploadDatas = $("#mainOid,#mainId,#crYear");
                        	var uploadDatas = $("#mainOid,#mainId,#crYear,#oid");
                            $.capFileUpload({
                                handler: Properties.ckFileUploadHandler,
                                fileCheck: [".jpg|.png|.gif"],
                                fileElementId: "inserImgUploadFile",
                                successMsg: null,
                                data: {
                                	getImgDimension:true,
                                	oid: uploadDatas.filter("#oid").val(), // for mega eloan
                                    mainId: uploadDatas.filter("#mainId").val()//, // for mega eloan
                                },
                                success: function(json){
                                    $.thickbox.close();
                                    imgDialog.empty().remove();
                                    setTimeout(function(){
                                        tmp.ckeditorGet().insertHtml("&nbsp;");
                                        tmp.ckeditorGet().insertHtml("<img src=\"" + json.url + "\" style=\"width:"+json.imgWidth+"px;height:"+json.imgHeight+"px\" />");
                                    }, 100);
                                }
                            });
                        };
                        b[i18n && i18n.def.cancel || "取消"] = function(){
                        	imgDialog.empty().remove();
                            $.thickbox.close()
                        };
                        return b;
                    })()
                });
                
            }
        });
//        tmp.ckeditorGet().ui.addButton('InsertImage', {
//            label: 'Insert Image',
//            command: 'InsertImageCmd'
//        });
        
        
        tmp.ckeditorGet().addCommand('LocalSave', {
            exec: function(editor){
                $cap($this).openHistory();
            }
        });
        tmp.ckeditorGet().ui.addButton('LocalSave', {
            label: 'Local Save',
            command: 'LocalSave'
        });
        
        return tmp;
    };
})(jQuery);

