/* 
 * L730S01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 授信報案考核表明細檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L730S01A", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId","chkYM","sysType","itemType","itemNo"}))
public class L730S01A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 
	 * 文件編號<p/>
	 * 同L730M01A.mainId
	 */
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * 資料年月<p/>
	 * 同L730M01A.chkYM
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="CHKYM", columnDefinition="DATE")
	private Date chkYM;

	/** 
	 * 企/個金案件<p/>
	 * 同L730M01A.sysType<br/>
	 *  企金案件：LMS<br/>
	 *  個金案件：CLS
	 */
	@Column(name="SYSTYPE", length=3, columnDefinition="CHAR(3)")
	private String sysType;

	/** 
	 * 種類<p/>
	 * A1.額度明細表(一)<br/>
	 *  A2.額度明細表(二)<br/>
	 *  B1.資信簡表<br/>
	 *  C1.估價報告書<br/>
	 *  D1.簽報書
	 */
	@Column(name="ITEMTYPE", length=2, columnDefinition="CHAR(2)")
	private String itemType;

	/** 
	 * 項目<p/>
	 * itemType A1~D1項目合併編號<br/>
	 *  註1.(001~050)
	 */
	@Column(name="ITEMNO", length=3, columnDefinition="CHAR(3)")
	private String itemNo;

	/** 單項分數 **/
	@Column(name="ITEMSCOR", columnDefinition="DECIMAL(7,2)")
	private BigDecimal itemScor;

	/** 疏失次數 **/
	@Column(name="ITEMCNT", columnDefinition="DECIMAL(7,2)")
	private BigDecimal itemCnt;

	/** 
	 * 扣分<p/>
	 * itemAll = (itemScor * itemCnt)
	 */
	@Column(name="ITEMALL", columnDefinition="DECIMAL(17,2)")
	private BigDecimal itemAll;

	/** 
	 * 備註<p/>
	 * 100個全型字<br/>
	 *  ※上傳至DW為CHAR(202)
	 */
	@Column(name="ITEMMEMO", length=300, columnDefinition="VARCHAR(300)")
	private String itemMemo;

	/** 建立人員號碼 **/
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Date updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 
	 * 取得文件編號<p/>
	 * 同L730M01A.mainId
	 */
	public String getMainId() {
		return this.mainId;
	}
	/**
	 *  設定文件編號<p/>
	 *  同L730M01A.mainId
	 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得資料年月<p/>
	 * 同L730M01A.chkYM
	 */
	public Date getChkYM() {
		return this.chkYM;
	}
	/**
	 *  設定資料年月<p/>
	 *  同L730M01A.chkYM
	 **/
	public void setChkYM(Date value) {
		this.chkYM = value;
	}

	/** 
	 * 取得企/個金案件<p/>
	 * 同L730M01A.sysType<br/>
	 *  企金案件：LMS<br/>
	 *  個金案件：CLS
	 */
	public String getSysType() {
		return this.sysType;
	}
	/**
	 *  設定企/個金案件<p/>
	 *  同L730M01A.sysType<br/>
	 *  企金案件：LMS<br/>
	 *  個金案件：CLS
	 **/
	public void setSysType(String value) {
		this.sysType = value;
	}

	/** 
	 * 取得種類<p/>
	 * A1.額度明細表(一)<br/>
	 *  A2.額度明細表(二)<br/>
	 *  B1.資信簡表<br/>
	 *  C1.估價報告書<br/>
	 *  D1.簽報書
	 */
	public String getItemType() {
		return this.itemType;
	}
	/**
	 *  設定種類<p/>
	 *  A1.額度明細表(一)<br/>
	 *  A2.額度明細表(二)<br/>
	 *  B1.資信簡表<br/>
	 *  C1.估價報告書<br/>
	 *  D1.簽報書
	 **/
	public void setItemType(String value) {
		this.itemType = value;
	}

	/** 
	 * 取得項目<p/>
	 * itemType A1~D1項目合併編號<br/>
	 *  註1.(001~050)
	 */
	public String getItemNo() {
		return this.itemNo;
	}
	/**
	 *  設定項目<p/>
	 *  itemType A1~D1項目合併編號<br/>
	 *  註1.(001~050)
	 **/
	public void setItemNo(String value) {
		this.itemNo = value;
	}

	/** 取得單項分數 **/
	public BigDecimal getItemScor() {
		return this.itemScor;
	}
	/** 設定單項分數 **/
	public void setItemScor(BigDecimal value) {
		this.itemScor = value;
	}

	/** 取得疏失次數 **/
	public BigDecimal getItemCnt() {
		return this.itemCnt;
	}
	/** 設定疏失次數 **/
	public void setItemCnt(BigDecimal value) {
		this.itemCnt = value;
	}

	/** 
	 * 取得扣分<p/>
	 * itemAll = (itemScor * itemCnt)
	 */
	public BigDecimal getItemAll() {
		return this.itemAll;
	}
	/**
	 *  設定扣分<p/>
	 *  itemAll = (itemScor * itemCnt)
	 **/
	public void setItemAll(BigDecimal value) {
		this.itemAll = value;
	}

	/** 
	 * 取得備註<p/>
	 * 100個全型字<br/>
	 *  ※上傳至DW為CHAR(202)
	 */
	public String getItemMemo() {
		return this.itemMemo;
	}
	/**
	 *  設定備註<p/>
	 *  100個全型字<br/>
	 *  ※上傳至DW為CHAR(202)
	 **/
	public void setItemMemo(String value) {
		this.itemMemo = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}
}
