$(document).ready(function() {
	$("input").attr("disabled", true);
	
});

function PageSetting(){

	$("input[name='item1_5']").change(function() {
		clearNotWorker();
    });
	
	$("input[name='item2_1']").change(function() {
		item2_1Change();
    });
	
	$("input[name='item2_2']").change(function() {
		item2_2Change();
    });
	
	$("input[name='item2_3']").change(function() {
		item2_3Change();
    });
	$("input[name='item4_1']").change(function() {
		item4Change();
    });
	$("input[name='item4_1_sub2']").change(function() {
		if($("input[name='item4_1_sub2'][value='2']").attr('checked')){
			$("#signErr").attr("style","display:block;");
		}else{
			$("#signErr").attr("style","display:none;");
		}
    });
	$("input[type='radio']").change(function() {
		var r=$(this);
		var text=$(this).parent().children('input[type=text]');
		r.addClass("required");
		text.attr("disabled", false);
		
		//前後欄位清空
		r.parent().parent().next().children().find('input[type=text]').val("");
		r.parent().parent().prev().children().find('input[type=text]').val("");
		
		//前後欄位required移除
		r.parent().parent().next().children().find('input[type=text]').removeClass("required");
		r.parent().parent().next().children().find('input[type=text]').removeClass("data-error");
		r.parent().parent().prev().children().find('input[type=text]').removeClass("required");
		r.parent().parent().prev().children().find('input[type=text]').removeClass("data-error");
		if(text.get(0) !== undefined){
			r.parent().parent().parent().prev().children().find('input[type=text]').removeClass("required");
			r.parent().parent().parent().prev().children().find('input[type=text]').removeClass("data-error");
		}
		r.parent().find('input[type=text]').attr("disabled", false);
		r.parent().find('input[type=text]').addClass("required");
		
		//前後欄位disabled
		r.parent().parent().next().children().find('input[type=text]').attr("disabled", true);
		r.parent().parent().prev().children().find('input[type=text]').attr("disabled", true);
		if(text.get(0) !== undefined){
			r.parent().parent().parent().next().children().find('input[type=text]').val("");
			r.parent().parent().parent().prev().children().find('input[type=text]').val("");
			
			r.parent().parent().parent().next().children().find('input[type=text]').attr("disabled", true);
			r.parent().parent().parent().prev().children().find('input[type=text]').attr("disabled", true);
		}
		if($("input[name='item2_5']:checked").val()==3){
			$("input[name='item2_5']:checked").parent().parent().parent().prev().children().find('input[type=text]').val("");
			$("input[name='item2_5']:checked").parent().parent().parent().prev().children().find('input[type=text]').removeClass("required");
			$("input[name='item2_5']:checked").parent().parent().parent().prev().children().find('input[type=text]').removeClass("data-error");
			$("input[name='item2_5']:checked").parent().parent().parent().prev().children().find('input[type=text]').attr("disabled", true);
		}
    });
}
function clearNotWorker(){
	if ($("input[name='item1_5']:checked").val() == '2') {
//		$("input[name='item1_6']").attr("disabled", "disabled");
//    	$("input[name='item1_7']").attr("disabled", "disabled");
//    	$("#item1_6_4_NEEDREASON1").attr("disabled", "disabled");
//    	$("#item1_7_5_NEEDREASON1").attr("disabled", "disabled");
    	
    	//$("input[name='item1_6']").attr("checked", false);;
//    	$("input[name='item1_7']").attr("checked", false);
//    	$("#item1_6_4_NeedReason1").val("");
//    	$("#item1_7_5_NeedReason1").val("");
//    	$("#item1_6_4_NeedReason1").attr("disabled", "disabled");
//    	$("#item1_7_5_NeedReason1").attr("disabled", "disabled");
//		$("input[name='item1_6'][value='3']").attr("checked", true);
//    	$("input[name='item1_7'][value='4']").attr("checked", true);
    }
	else{
//		$("input[name='item1_6']").attr("disabled", false);
//    	$("input[name='item1_7']").attr("disabled", false);
//    	$("#item1_6_4_NEEDREASON1").attr("disabled", false);
//    	$("#item1_7_5_NEEDREASON1").attr("disabled", false);
	}
	
}
function item2_1Change(){
//	if ($("input[name='item2_1']:checked").val() == '2') {
//		$("input[name='item2_1_sub1'][value='3']").attr("checked", true);
//    	$("input[name='item2_1_sub2'][value='3']").attr("checked", true);
//    	$("input[name='item2_1_sub3'][value='3']").attr("checked", true);
//    	$("input[name='item2_1_sub4'][value='3']").attr("checked", true);
//    }
}
function item2_2Change(){
//	if ($("input[name='item2_2']:checked").val() == '2') {
//		$("input[name='item2_2_sub1'][value='4']").attr("checked", true);
//		$("input[name='item2_2_sub2'][value='3']").attr("checked", true);
//    }
}
function item2_3Change(){
//	if ($("input[name='item2_3']:checked").val() == '2') {
//		$("input[name='item2_3_sub1'][value='4']").attr("checked", true);
//    	$("input[name='item2_3_sub2'][value='4']").attr("checked", true);
//    	$("input[name='item2_3_sub3'][value='3']").attr("checked", true);
//    }
}

function item4Change(){
	if ($("input[name='item4_1']:checked").val() == '2') {
		$("input[name='item4_1_sub1']").attr("disabled", true);
    	$("input[name='item4_1_sub2']").attr("disabled", true);
    	$("input[name='item4_1_sub3']").attr("disabled", true);
    	$("input[name='item4_1_sub4']").attr("disabled", true);
    	$("input[name='item4_1_sub5']").attr("disabled", true);
    	$("input[name='item4_1_sub1']").attr("checked", false);
    	$("input[name='item4_1_sub2']").attr("checked", false);
    	$("input[name='item4_1_sub3']").attr("checked", false);
    	$("input[name='item4_1_sub4']").attr("checked", false);
    	$("input[name='item4_1_sub5']").attr("checked", false);
    	$("#item4_1_sub1_2_NeedReason1").val("");
    	$("#item4_1_sub3_2_NeedReason1").val("");
    	$("#item4_1_sub3_2_NeedReason2").val("");
    	$("#item4_1_sub4_2_NeedReason1").val("");
    	$("#signErr").attr("style","display:none;");
    	$("#item4_1_sub1_2_NeedReason1").attr("disabled", true);
    	$("#item4_1_sub3_2_NeedReason1").attr("disabled", true);
    	$("#item4_1_sub3_2_NeedReason2").attr("disabled", true);
    	$("#item4_1_sub4_2_NeedReason1").attr("disabled", true);
    }
	else{
		$("input[name='item4_1_sub1']").attr("disabled", false);
    	$("input[name='item4_1_sub2']").attr("disabled", false);
    	$("input[name='item4_1_sub3']").attr("disabled", false);
    	$("input[name='item4_1_sub4']").attr("disabled", false);
    	$("input[name='item4_1_sub5']").attr("disabled", false);
	}
}


