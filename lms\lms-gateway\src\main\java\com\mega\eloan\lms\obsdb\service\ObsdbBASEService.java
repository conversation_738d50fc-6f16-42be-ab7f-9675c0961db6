package com.mega.eloan.lms.obsdb.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import tw.com.iisi.cap.util.CapDate;

/**
 * <pre>
 * 對大陸地區授信業務控管註記
 * </pre>
 * 
 * @since 2013/7/15
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/7/15,007625,new
 *          </ul>
 */
public interface ObsdbBASEService {

	/**
	 * J-105-0202-001 Web e-Loan企金授信修改客戶統編。
	 * 
	 * @param BRNID
	 * @param orgCustId
	 * @param orgDupNo
	 * @param newCustId
	 * @param newDupNo
	 * @param documentNo
	 * @param cntrNo
	 * @param rptMainId
	 */
	public void updateJ1050202_by_custIdAndDupNo_byCaseBrId(String BRNID,
			String orgCustId, String orgDupNo, String newCustId,
			String newDupNo, String documentNo, String cntrNo, String rptMainId);

	public void updateJ1050202_by_custIdAndDupNo_byCntrBranch(
			Map<String, Object> BRNIDs, String orgCustId, String orgDupNo,
			String newCustId, String newDupNo, String documentNo,
			String cntrNo, String rptMainId);

	public void updateJ1050202_by_custIdAndDupNo_byDrawdownBranch(
			Map<String, Object> BRNIDs, String orgCustId, String orgDupNo,
			String newCustId, String newDupNo, String documentNo,
			String cntrNo, String rptMainId);

	/**
	 * G-106-0333-001 Web e-Loan 授信系統配合加拿大分行改制調整簽報書相關資料
	 * 
	 * @param BRNID
	 */
	public void doLmsBatch0012(String BRNID);

	/**
	 * G-106-0333-001 Web e-Loan 授信系統配合加拿大分行改制調整簽報書相關資料
	 * 
	 * @param BRNID
	 */
	public void doLmsBatch0013(String BRNID);

	// G-107-0642_05097_B1001 配合羅勇分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權
	public Map<String, Object> findElf515ByCntrNoForBt(String BRNID,
			String cntrNo);

	// G-107-0642_05097_B1001 配合羅勇分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權
	public void updateElf515ByCntrNoByCntrNoForBt(String BRNID,
			String newCntrNo, String oldCntrNo, String modifyUnit);

	/**
	 * J-109-0341_05097_B1001 Web e-Loan異常通報增加授信戶信評大幅貶落事項並傳送國內海外帳務系統
	 * 
	 * @param BRNID
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @param caseBrid
	 * @param caseNo
	 */
	public void updateUnNormal4(String BRNID, String mainId, String custId,
			String dupNo, String caseBrid, String caseNo,
			boolean canCloseAllBranch);

	/**
	 * J-109-0341_05097_B1001 Web e-Loan異常通報增加授信戶信評大幅貶落事項並傳送國內海外帳務系統
	 * 
	 * @param BRNID
	 * @param isClosed
	 * @param closeDate
	 * @param closeCaseNo
	 * @param closeMainId
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @param caseBrid
	 */
	public void update0851UnNormal5(String BRNID, String isClosed,
			String closeDate, String closeCaseNo, String closeMainId,
			String mainId, String custId, String dupNo, String caseBrid);

	/**
	 * J-109-0341_05097_B1001 Web e-Loan異常通報增加授信戶信評大幅貶落事項並傳送國內海外帳務系統
	 * 
	 * @param BRNID
	 * @param custId
	 * @param dupNo
	 * @param brno
	 * @return
	 */
	public boolean selUnNormal4(String BRNID, String custId, String dupNo,
			String brno);

	/**
	 * J-109-0341_05097_B1001 Web e-Loan異常通報增加授信戶信評大幅貶落事項並傳送國內海外帳務系統
	 * 
	 * @param BRNID
	 * @param custId
	 * @param dupNo
	 * @param custName
	 * @param brno
	 * @param lostAmt
	 * @param collStat
	 * @param process
	 * @param sameIdea
	 * @param createTime
	 * @param mdClass
	 * @param ndCode
	 */
	public void insertUnNormal4(String BRNID, String custId, String dupNo,
			String custName, String brno, long lostAmt, String collStat,
			String process, String sameIdea, Date createTime, String mdClass,
			String ndCode);

	/**
	 * J-109-0341_05097_B1001 Web e-Loan異常通報增加授信戶信評大幅貶落事項並傳送國內海外帳務系統
	 * 
	 * @param custId
	 * @param dupNo
	 * @param caseBrid
	 * @param ndCode
	 */
	public void update0851UnNormaData(String BRNID, String custId,
			String dupNo, String caseBrid, String ndCode);

	/**
	 * J-111-0423_05097_B1001 Web
	 * e-Loan企金授信就海外分行承做永續績效連結授信案(如附件)，於E-Loan「永續績效連結授信」相關註記
	 * 
	 */
	public void updateEsgDataFromLms2105v01ServiceImpl(String BRNID,
			String cntrNo, String esgSustainLoan, String esgSustainLoanType,
			String esgSustainLoanUnReach);

	/**
	 * J-111-0633_05097_B1001 Web e-Loan授信系統不動產暨72-2相關資訊註記維護之頁面，增列補鍵產品種類33、34之功能
	 * 
	 * @param cntrNo
	 * @param prodKind
	 * @param adcCaseNo
	 */
	public void updateAdcInfo(String BRNID, String cntrNo,
			String prodKind, String adcCaseNo);

}
