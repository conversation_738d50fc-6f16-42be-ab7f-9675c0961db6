/* 
 * L260M01ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;
import tw.com.jcs.common.Util;

import com.mega.eloan.lms.dao.L260M01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L260M01A;

/** 貸後管理主檔 **/
//
@Repository
public class L260M01ADaoImpl extends LMSJpaDao<L260M01A, String>
	implements L260M01ADao {

	@Override
	public L260M01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public L260M01A findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		return findUniqueOrNone(search);
	}
			
	@Override
	public List<L260M01A> findByDocStatus(String docStatus){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "docStatus", docStatus);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L260M01A> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L260M01A> findByIndex01(String mainId){
		ISearch search = createSearchTemplete();
		List<L260M01A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.setMaxResults(Integer.MAX_VALUE);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L260M01A> findByIndex02(String custId, String dupNo, String cntrNo, String loanNo){
		ISearch search = createSearchTemplete();
		List<L260M01A> list = null;
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (cntrNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		if (loanNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "loanNo", loanNo);
		search.setMaxResults(Integer.MAX_VALUE);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L260M01A> findByIndex03(String custId, String dupNo, String cntrNo, String loanNo,
										String docStatus, String branchId){
		ISearch search = createSearchTemplete();
		List<L260M01A> list = null;
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (cntrNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		if (loanNo != null && Util.isNotEmpty(Util.trim(loanNo)))
			search.addSearchModeParameters(SearchMode.EQUALS, "loanNo", loanNo);
		if (docStatus != null)
			search.addSearchModeParameters(SearchMode.NOT_EQUALS, "docStatus", docStatus);
		if (branchId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", branchId);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		search.setMaxResults(Integer.MAX_VALUE);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L260M01A> findByIndex04(String ownBrId, String custId, String dupNo,
			String cntrNo, String loanNo, String[] docStatusArray){
		ISearch search = createSearchTemplete();
		List<L260M01A> list = null;
		if (ownBrId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", ownBrId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (cntrNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		if (loanNo != null && Util.isNotEmpty(Util.trim(loanNo)))
			search.addSearchModeParameters(SearchMode.EQUALS, "loanNo", loanNo);
		if (docStatusArray != null)
			search.addSearchModeParameters(SearchMode.IN, "docStatus", docStatusArray);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		search.setMaxResults(Integer.MAX_VALUE);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
	
	@Override
	public List<L260M01A> findByNotEqualsDocsStatus(String custId,
			String dupNo, String cntrNo, String loanNo, String[] docStatusAry,
			String branchId) {
		ISearch search = createSearchTemplete();
		List<L260M01A> list = null;
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (cntrNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		if (loanNo != null && Util.isNotEmpty(Util.trim(loanNo)))
			search.addSearchModeParameters(SearchMode.EQUALS, "loanNo", loanNo);
		if (Util.isNotEmpty(docStatusAry)) {
			for (String docStatus : docStatusAry) {
				search.addSearchModeParameters(SearchMode.NOT_EQUALS,
						"docStatus", docStatus);
			}
		}
		if (branchId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
					branchId);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		search.setMaxResults(Integer.MAX_VALUE);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}
}