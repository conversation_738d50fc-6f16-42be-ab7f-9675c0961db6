/* 
 * C160S01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C160S01A;

/** 擔保品資料明細檔 **/
public interface C160S01ADao extends IGenericDao<C160S01A> {

	C160S01A findByOid(String oid);
	
	List<C160S01A> findByMainId(String mainId);
	
	List<C160S01A> findByMainIdRefMainId(String mainId,String refmainId);
	
	C160S01A findByUniqueKey(String mainId, Integer seqNo, String refmainId);

	List<C160S01A> findByIndex01(String mainId, Integer seqNo, String refmainId);

	List<C160S01A> findByIndex02(String mainId, String collNo, String custId);
}