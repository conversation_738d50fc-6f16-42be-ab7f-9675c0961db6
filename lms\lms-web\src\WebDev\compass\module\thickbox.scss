/* 此檔案已由richard修改過 */
/* ----------------------------------------------------------------------------------------------------------------*/
/* ---------->>> global settings needed for thickbox <<<-----------------------------------------------------------*/
/* ----------------------------------------------------------------------------------------------------------------*/
*{padding: 0; margin: 0;}

/* ----------------------------------------------------------------------------------------------------------------*/
/* ---------->>> thickbox specific link and font settings <<<------------------------------------------------------*/
/* ----------------------------------------------------------------------------------------------------------------*/
.TB_window {
	font-size: 1em;
	color: #333333;
	min-width:249px;
}

.TB_secondLine {
	font-size: 0.9em;
	color:#666666;
}
/*
.TB_window a:link {color: #666666;}
.TB_window a:visited {color: #666666;}
.TB_window a:hover {color: #000;}
.TB_window a:active {color: #666666;}
.TB_window a:focus{color: #666666;}
*/
/* ----------------------------------------------------------------------------------------------------------------*/
/* ---------->>> thickbox settings <<<-----------------------------------------------------------------------------*/
/* ----------------------------------------------------------------------------------------------------------------*/
.TB_overlay {
	position: fixed;
	z-index:100;
	top: 0px;
	left: 0px;
	height:100%;
	width:100%;
}

.TB_overlayMacFFBGHack {background: url(macFFBgHack.png) repeat;}
.TB_overlayBG {
	background-color:#F5EEFC;
	filter:alpha(opacity=75);
	-moz-opacity: 0.75;
	opacity: 0.75;
}

* html .TB_overlay { /* ie6 hack */
     position: absolute;
     height: expression(document.body.scrollHeight > document.body.offsetHeight ? document.body.scrollHeight : document.body.offsetHeight + 'px');
}

.TB_window {
	position: fixed;
	background: #ffffff;
	z-index: 102;
	color:#000000;
	display:none;
	/* border: 0px double #525252; */
	/*border: 1px solid #525252;*/
	text-align:left;
	/* top:48.5%; */
	top:46.5%;
	left:50%;
}

* html .TB_window { /* ie6 hack */
	position: absolute;
	margin-top: expression(0 - parseInt(this.offsetHeight / 2) + (TBWindowMargin = document.documentElement && document.documentElement.scrollTop || document.body.scrollTop) + 'px');
}
.TB_ajaxContent{
	position:relative;
}
.TB_window img.TB_Image {
	display:block;
	margin: 15px 0 0 15px;
	border-right: 1px solid #ccc;
	border-bottom: 1px solid #ccc;
	border-top: 1px solid #666;
	border-left: 1px solid #666;
}

.TB_caption{
	height:25px;
	padding:7px 30px 10px 25px;
	float:left;
}

.TB_closeWindow{
	height:25px;
	padding:11px 25px 10px 0;
	float:right;
}

.TB_closeAjaxWindow{
	padding:0 5px 0 0; /*邊框空白值 宣告四邊不同值順序為上、右、下、左*/
	text-align:right;
	float:right;
}

.TB_closeAjaxWindowFixIE{
	padding:0 5px 0 0; /*邊框空白值 宣告四邊不同值順序為上、右、下、左*/
	margin: -5pt 0 0 0; /*邊界值 宣告四邊不同值順序為上、右、下、左*/
	text-align:right;
	float:right;
}

.TB_ajaxWindowTitle{
	float:left;
	padding:0 0 0 10px;
}

.TB_buttonBar {
	background-color: #EBEBFF;
	padding:3px 10px 3px 10px;
 /* border-top : 1px solid #6699cc; */ /*上框線*/ 
	border-bottom : 1px solid #6699cc; /*下框線*/
	border-left : 1px solid #6699cc; /*左框線*/
	border-right : 1px solid #6699cc; /*右框線*/
	/* filter : alpha(opacity=90,style=1); */
	/* border: 1px dotted #525252; */
	/* border: 1px dotted #69c; */
}

.TB_buttonBar_bottom {
	background-color: #EBEBFF;
	padding:3px 10px 3px 10px;
  border-top : 1px solid #6699cc;  /*上框線*/ 
	/* border-bottom : 1px solid #6699cc; */ /*下框線*/
	border-left : 1px solid #6699cc; /*左框線*/
	border-right : 1px solid #6699cc; /*右框線*/
	position:relative;
	/* filter : alpha(opacity=90,style=1); */
	/* border: 1px dotted #525252; */
	/* border: 1px dotted #69c; */
}
/*
.TB_buttonBar button {
	margin-right:5px;
	border:1px solid #bbb;
	background-color:#fff;
	color: #666;
	cursor:pointer;
}

.TB_buttonBar button:hover {
	border-top:1px solid #eee;
	border-left:1px solid #eee;
	border-right:1px solid #999;
	border-bottom:1px solid #999;
	color: #000;
	background-color:#def;
}
*/
.TB_buttonBar button img {
	margin-right:2px;
}

.TB_buttons {
	font-size: 1em;
	color: #333333;
}

.TB_title {
	font-weight:bold;
	color: #FFFFFF;
	background-color:#6699cc;
	border-top : 1px solid #6699cc;  /*上框線*/
	border-bottom : 1px solid #6699cc; /*下框線*/
	border-left : 1px solid #6699cc; /*左框線*/
	border-right : 1px solid #6699cc; /*右框線*/
  /* filter : alpha(opacity=90,style=4); */
	height:27px;
	line-height:27px;
	vertical-align:middle;
}

.TB_ajaxContent{
	min-height:50px;
	clear:both;
	padding:5px 10px 5px 10px;
	/*margin:10px 20px;*/
	overflow:auto;
	text-align:left;
	/*line-height:1.4em;*/ /*mark by fantasy 2013/05/24 fix tckeditor preview*/
	border-left : 1px solid #6699cc; /*左框線*/
	border-right : 1px solid #6699cc; /*右框線*/
}

.TB_ajaxContent.TB_modal{
	/*padding:15px;*/
}

.TB_ajaxContent p{
	padding:5px 0px 5px 0px;
}

.TB_load{
	position: fixed;
	display:none;
	height:13px;
	width:208px;
	z-index:103;
	top: 50%;
	left: 50%;
	margin: -6px 0 0 -104px; /* -height/2 0 0 -width/2 */
}

* html .TB_load { /* ie6 hack */
position: absolute;
margin-top: expression(0 - parseInt(this.offsetHeight / 2) + (TBWindowMargin = document.documentElement && document.documentElement.scrollTop || document.body.scrollTop) + 'px');
}

.TB_HideSelect{
	z-index:99;
	position:fixed;
	top: 0;
	left: 0;
	background-color:#fff;
	border:none;
	filter:alpha(opacity=0);
	-moz-opacity: 0;
	opacity: 0;
	height:100%;
	width:100%;
}

* html .TB_HideSelect { /* ie6 hack */
     position: absolute;
     height: expression(document.body.scrollHeight > document.body.offsetHeight ? document.body.scrollHeight : document.body.offsetHeight + 'px');
}

.TB_iframeContent{
	clear:both;
	border:none;
	/* margin:0; */
	margin-bottom: -3px; /*下邊界值*/
	border-left : 1px solid #6699cc; /*左框線*/
	border-right : 1px solid #6699cc; /*右框線*/
}

.TB_closeWindowButton,
.TB_closeWindowButton:active,
.TB_closeWindowButton:visited,
.TB_closeWindowButton:hover {
	text-decoration: none;
}

.TB_b1f, .TB_b2f, .TB_b3f, .TB_b4f{font-size:1px; overflow:hidden; display:block; background:#EBEBFF; border-right:1px solid #6699cc; border-left:1px solid #6699cc;}
.TB_b1f {height:1px; margin:0 5px; background:#6699cc;}
.TB_b2f {height:1px; margin:0 3px; border-right-width:2px; border-left-width:2px;}
.TB_b3f {height:1px; margin:0 2px;}
.TB_b4f {height:2px; margin:0 1px;}
.TB_head {background:#6699cc;}
/*
div.content, div.head{ padding-left:15px; color:#6699cc; border-right:1px solid #6699cc; border-left:1px solid #6699cc;}
div.head h1 {margin:0px; background:#6699cc; font-size:22px;}
*/
.TB_content {background:#6699cc; padding-top:5px;}
