
$(function(){
	var LMS7830Action = {
	    fhandle: "lms7830m01formhandler",
	    ghandle: "lms7830gridhandler",
	    //按鈕-開始查詢
	    query: function(){
	        $("#inputCustId").val("");
	        $("#queryTable").thickbox({
	            // 開始查詢
	            title: i18n.lms7830m01['button.search'],
	            width: 400,
	            height: 170,
	            modal: true,
	            valign: "bottom",
	            i18n: i18n.def,
	            align: "center",
	            buttons: {
	                "sure": function(){
	                    if (!$("#queryForm").valid()) {
	                        return;
	                    }
	                    $.ajax({
	                        handler: LMS7830Action.fhandle,//lms7830m01formhandler
	                        data: {
	                            formAction: "queryCustData",
	                            custId: $("#inputCustId").val(),
	                            type: $("#txCdselect").val()
	                        },
						}).done(function(obj){
							$.thickbox.close();
							if (obj.size == "1") {
							    var dupNo = ""
							    for (var b in obj.item) {
							        dupNo = b;
							    }
							    LMS7830Action.gridRelod(dupNo);
							    $("#dupNo").text(dupNo);
							    $("#custName").text(obj.item[0]);
							} else {
							    $("#custIdSelect").setItems({
							        item: obj.item,
							        format: "{value} {key}"
							    });
							    LMS7830Action.selectPeople();
							}
							$("#queryDate").text(obj.queryDate);
							$("#custId").text(obj.custId);
							$("#typCd").text(obj.typCd);
						});
	                },
	                "cancel": function(){
	                    $.thickbox.close();
	                }
	            }
	        });
	    },
	    //選擇人員清單
	    selectPeople: function(){
	        $("#selectPeopleBox").thickbox({
	            // 開始查詢
	            title: i18n.lms7830m01['button.search'],
	            width: 400,
	            height: 170,
	            modal: true,
	            valign: "bottom",
	            i18n: i18n.def,
	            align: "center",
	            buttons: {
	                "sure": function(){
	                    LMS7830Action.gridRelod($("#custIdSelect").val());
	                    var idName = $("#custIdSelect :selected").text().split(" ");
	                    $("#dupNo").text(idName[0]);
	                    $("#custName").text(idName[1]);
	                    $.thickbox.close();
	                }
	            }
	        });
	    },
	    gridRelod: function(dupNo){
	        $("#gridView").jqGrid("setGridParam", {
	            postData: {
	                custId: $("#inputCustId").val(),
	                dupNo: dupNo
	            },
	            search: true
	        }).trigger("reloadGrid");
	    }
	};
	
    $("#txCdselect").removeOptions("0");
    LMS7830Action.query();
    var btn = $("#buttonPanel");
    btn.find("#btnSearch").click(function(showMsg){
        LMS7830Action.query();
    }).end().find("#btnExit").click(function(){
        setCloseConfirm(true);
    });
    var grid = $("#gridView").iGrid({
        handler: LMS7830Action.ghandle,//'lms7830gridhandler'
        height: 350,
        rowNum: 15,
        sortname: 'brNo',
        postData: {
            formAction: "queryL783m01a"
        },
        colModel: [{
            colHeader: i18n.lms7830m01['L783M01A.brNo'],// 分行別,
            name: 'brNo',
            width: 120,
            align: "left",
            sortable: true
        }, {
            colHeader: i18n.lms7830m01['L783M01A.cntrNo'],// 額度序號
            name: 'cntrNo',
            width: 140,
            sortable: true
        }, {
            colHeader: i18n.lms7830m01['L783M01A.status'],// 狀態
            name: 'status',
            width: 140,
            sortable: true
        }, {
            colHeader: i18n.lms7830m01['L783M01A.cType'],// 授權等級
            name: 'cType',
            width: 100,
            sortable: true
        }, {
            colHeader: i18n.lms7830m01['L783M01A.caseDate'],// 簽案日期
            name: 'caseDate',
            width: 80,
            sortable: true,
            align: "center"
        }]
    });
});
