/* 
 * L140M01GDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao.impl;

import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L140M01GDao;
import com.mega.eloan.lms.model.L140M01G;

/** 額度利費率主檔 **/
@Repository
public class L140M01GDaoImpl extends LMSJpaDao<L140M01G, String>
	implements L140M01GDao {

	@Override
	public L140M01G findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}
	@Override
	public List<L140M01G> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addOrderBy("createTime", false);
		List<L140M01G> list = createQuery(L140M01G.class,search).getResultList();
		return list;
	}
	@Override
	public L140M01G findByUniqueKey(String mainId,Integer rateSeq,String rateType){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "rateSeq", rateSeq);
		search.addSearchModeParameters(SearchMode.EQUALS, "rateType", rateType);
	
		return findUniqueOrNone(search);
	}
	@Override
	public List<L140M01G> findByMainIdAndRateSeq(String mainId, int rateSeq) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "rateSeq", rateSeq);
		List<L140M01G> list = createQuery(L140M01G.class,search).getResultList();
		return list;
	}
	@Override
	public L140M01G findByMainIdAndRatetype(String mainId, String rateType) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "rateType", rateType);
		return findUniqueOrNone(search);
	}
}