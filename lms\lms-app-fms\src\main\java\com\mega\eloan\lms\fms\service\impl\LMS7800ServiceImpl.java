package com.mega.eloan.lms.fms.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.DocLogEnum;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.dao.L140MM6ADao;
import com.mega.eloan.lms.dao.L140MM6BDao;
import com.mega.eloan.lms.dao.L140MM6CDao;
import com.mega.eloan.lms.dao.L180R46ADao;
import com.mega.eloan.lms.fms.pages.LMS7800M01Page;
import com.mega.eloan.lms.fms.service.LMS7800Service;
import com.mega.eloan.lms.model.L140MM6A;
import com.mega.eloan.lms.model.L140MM6B;
import com.mega.eloan.lms.model.L140MM6C;
import com.mega.eloan.lms.model.L180R46A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.core.FlowException;
import tw.com.jcs.flow.service.FlowService;

/**
 * <pre>
 * 共同行銷維護作業
 * </pre>
 * 
 * @since 2019
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Service
public class LMS7800ServiceImpl extends AbstractCapService implements
		LMS7800Service {

	@Resource
	FlowService flowService;
	
	@Resource
	TempDataService tempDataService;

	@Resource
	DocLogService docLogService;
	
	@Resource
	L140MM6ADao l140mm6aDao;
	
	@Resource
	L140MM6BDao l140mm6bDao;
	
	@Resource
	L140MM6CDao l140mm6cDao;
	
	@Resource
	L180R46ADao l180r46aDao;

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == L140MM6A.class) {
			return l140mm6aDao.findPage(search);
		} else if(clazz == L140MM6B.class){
			return l140mm6bDao.findPage(search);
		} else if(clazz == L140MM6C.class){
			return l140mm6cDao.findPage(search);
		}
		return null;
	}
	
	@Override
	public boolean deleteL140mm6as(String[] oids) {
		boolean flag = false;
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<L140MM6A> l140mm6as = new ArrayList<L140MM6A>();
		for (int i = 0, size = oids.length; i < size; i++) {
			L140MM6A l140mm6a = (L140MM6A) findModelByOid(L140MM6A.class,
					oids[i]);
			// 設定刪除並非直接刪除 ，只是標記刪除時間
			l140mm6a.setDeletedTime(CapDate.getCurrentTimestamp());
			l140mm6a.setUpdater(user.getUserId());
			l140mm6as.add(l140mm6a);
			docLogService.record(l140mm6a.getOid(), DocLogEnum.DELETE);
		}
		if (!l140mm6as.isEmpty()) {
			l140mm6aDao.save(l140mm6as);
			flag = true;
		}
		return flag;
	}
	
	@Override
	public List<L140MM6C> findL140mm6csByMainId(String mainId) {
		return l140mm6cDao.findByIndex01(mainId);
	}
	
	@Override
	public  Map<String, String> getData(L140MM6A l140mm6a, boolean newData) throws CapException {
		Map<String, String> returnMap = new LinkedHashMap<String, String>();
		String mainId = l140mm6a.getMainId();
		String cntrNo = l140mm6a.getCntrNo();
		returnMap.put("mainId", mainId);
		
		if (Util.equals(cntrNo, "")) {
			Properties pop = MessageBundleScriptCreator.getComponentResource(LMS7800M01Page.class);
			// 額度序號不得為空白
			throw new CapMessageException(pop.getProperty("cntrNoEmpty"), getClass());
		}
		
		if(newData){
			List<L180R46A> list = l180r46aDao.findByIndex02(cntrNo);
			if (list != null && list.size()>0 ) {
				this.newL140mm6c(mainId, list);
			}
		}
		
		returnMap.put("cntrNo", cntrNo);
		
		return returnMap;
	}
	
	public void newL140mm6c(String mainId, List<L180R46A> list) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<L140MM6C> l140mm6cs = new ArrayList<L140MM6C>();
		for(L180R46A l180r46a : list){
			L140MM6C l140mm6c = new L140MM6C();
			l140mm6c.setMainId(mainId);
			l140mm6c.setCreateTime(CapDate.getCurrentTimestamp());
			l140mm6c.setCreator(user.getUserId());
			
			l140mm6c.setType(l180r46a.getType());
			l140mm6c.setCheckYN(l180r46a.getCheckYN());
			l140mm6c.setResult(l180r46a.getResult());
			l140mm6c.setContact(l180r46a.getContact());
			l140mm6c.setMemo(l180r46a.getMemo());
			
			l140mm6cs.add(l140mm6c);
		}
		l140mm6cDao.save(l140mm6cs);
	}
	
	@Override
	public L180R46A findL180r46aByCntrNoType(String cntrNo, String type) {
		return l180r46aDao.findByCntrNoType(cntrNo, type);
	}
	
	@Override
	public L140MM6C findL140mm6c(String mainId, String type) {
		return l140mm6cDao.findByIndex02(mainId, type);
	}
	
	@Override
	public void deleteL140mm6bs(List<L140MM6B> l140mm6bs, boolean isAll) {
		if (isAll) {
			l140mm6bDao.delete(l140mm6bs);
		} else {
			List<L140MM6B> L140MM6BsOld = new ArrayList<L140MM6B>();
			for (L140MM6B l140mm6b : l140mm6bs) {
				String staffJob = l140mm6b.getStaffJob();
				if (!("L6".equals(staffJob) || "L7".equals(staffJob))) {
					L140MM6BsOld.add(l140mm6b);
				}
			}
			l140mm6bDao.delete(L140MM6BsOld);
		}
	}
	
	@Override
	public void saveL140mm6bList(List<L140MM6B> list) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (L140MM6B l140mm6b : list) {
			l140mm6b.setUpdater(user.getUserId());
			l140mm6b.setUpdateTime(CapDate.getCurrentTimestamp());
		}
		l140mm6bDao.save(list);
	}
	
	@Override
	public L140MM6B findL140mm6b(String mainId, String branchType,
			String branchId, String staffNo, String staffJob) {
		return l140mm6bDao.findByUniqueKey(mainId, branchType, branchId,
				staffNo, staffJob);
	}
	
	@Override
	public void flowAction(String mainOid, L140MM6A model, boolean setResult,
			boolean resultType, boolean upMis) throws Throwable {
		try {
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			FlowInstance inst = flowService.createQuery().id(mainOid).query();
			if (inst == null) {
				inst = flowService.start("LMS7800Flow",
						((L140MM6A) model).getOid(), user.getUserId(),
						user.getUnitNo());
			}
			if (setResult) {
				inst.setDeptId(user.getUnitNo());
				inst.setUserId(user.getUserId());
				// resultType 控制前進還是後退
				// 當有先行動用的狀態 是到03O 非先行動用表示已完成 到05O
				inst.setAttribute("result", resultType ? "核准" : "退回");
				if (resultType) {
					//save((L140MM4A) model);
					if (upMis) {
						L140MM6A l140mm6a = (L140MM6A) findModelByOid(
								L140MM6A.class, mainOid);
						// 動審表簽章欄檔取得人員職稱
						List<L140MM6B> l140mm6blist = l140mm6bDao
								.findByMainId(l140mm6a.getMainId());
						String apprId = "";
						String reCheckId = "";

						for (L140MM6B l140mm6b : l140mm6blist) {
							String StaffJob = Util.trim(l140mm6b.getStaffJob());// 取得人員職稱
							String StaffNo = Util.trim(l140mm6b.getStaffNo());// 取得行員代碼
							if (Util.equals(StaffJob, "L1")) {// 分行經辦
								apprId = StaffNo;
							} else if (Util.equals(StaffJob, "L4")) {// 分行覆核主管
								reCheckId = StaffNo;
							}
						}
						// 若人員職稱為空值改取l140mm6a上的人員資料
						if (Util.isEmpty(apprId)) {
							apprId = l140mm6a.getUpdater();
						}
						if (Util.isEmpty(reCheckId)) {
							reCheckId = l140mm6a.getApprover();
						}

						String mainId = l140mm6a.getMainId();
						String cntrNo = l140mm6a.getCntrNo();

						// 更新回L180R46A
						List<L140MM6C> l140mm6cList = l140mm6cDao.findByIndex01(mainId);
						if (l140mm6cList != null && Util.isNotEmpty(l140mm6cList)) {
							for(L140MM6C l140mm6c : l140mm6cList){
								L180R46A l180r46a = this.convertL180r46a(
										cntrNo, l140mm6a, l140mm6c);
								l180r46aDao.save(l180r46a);
							}
						}					
					}
				}
			}
			inst.next();

		} catch (FlowException e) {
			Throwable t1 = e;
			while (t1.getCause() != null) {
				t1 = t1.getCause();
			}
			throw t1;
		}
	}
	
	private L180R46A convertL180r46a(String cntrNo, L140MM6A l140mm6a, L140MM6C l140mm6c){
		L180R46A l180r46a = new L180R46A();
		l180r46a = l180r46aDao.findByCntrNoType(cntrNo, l140mm6c.getType());

		l180r46a.setCustId(l140mm6a.getCustId());
		l180r46a.setDupNo(l140mm6a.getDupNo());
		l180r46a.setCustName(l140mm6a.getCustName());
		
		l180r46a.setType(l140mm6c.getType());
		l180r46a.setCheckYN(l140mm6c.getCheckYN());
		l180r46a.setResult(l140mm6c.getResult());
		l180r46a.setContact(l140mm6c.getContact());
		l180r46a.setMemo(l140mm6c.getMemo());
//			l180r46a.setCnt(0);
//			l180r46a.setNotifyTime(null);
		
		l180r46a.setAppraiser(l140mm6a.getAppraiser());
		l180r46a.setInfoAppraiser(l140mm6a.getInfoAppraiser());
		l180r46a.setUpdFrom("FMS");
		l180r46a.setUpdater(l140mm6c.getUpdater());
		l180r46a.setUpdateTime(l140mm6c.getUpdateTime());

		return l180r46a;
	}
	
	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		if (clazz == L140MM6A.class) {
			return l140mm6aDao.findByIndex01(mainId);
		} else if (clazz == L140MM6B.class) {
			return l140mm6bDao.findByMainId(mainId);
		} else if (clazz == L140MM6C.class) {
			return l140mm6cDao.findByIndex01(mainId);
		}
		return null;
	}
	
	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		if (clazz == L140MM6A.class) {
			return (T) l140mm6aDao.findByOid(oid);
		} else if(clazz == L140MM6B.class){
			return (T) l140mm6bDao.findByOid(oid);
		} else if(clazz == L140MM6C.class){
			return (T) l140mm6cDao.findByOid(oid);
		}
		return null;
	}

	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L140MM6A) {
					if (Util.isEmpty(((L140MM6A) model).getOid())) {
						((L140MM6A) model).setCreator(user.getUserId());
						((L140MM6A) model).setCreateTime(CapDate
								.getCurrentTimestamp());
						l140mm6aDao.save((L140MM6A) model);

						flowService.start("LMS7800Flow",
								((L140MM6A) model).getOid(), user.getUserId(),
								user.getUnitNo());
					} else {
						// 當文件狀態為編製中時文件亂碼才變更

						((L140MM6A) model).setUpdater(user.getUserId());
						((L140MM6A) model).setUpdateTime(CapDate
								.getCurrentTimestamp());
						l140mm6aDao.save((L140MM6A) model);
						if (!"Y".equals(SimpleContextHolder
								.get(EloanConstants.TEMPSAVE_RUN))) {
							tempDataService.deleteByMainId(((L140MM6A) model)
									.getMainId());
							docLogService.record(((L140MM6A) model).getOid(),
									DocLogEnum.SAVE);
						}
					}
				} else if (model instanceof L140MM6B) {
					((L140MM6B) model).setUpdater(user.getUserId());
					((L140MM6B) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l140mm6bDao.save((L140MM6B) model);
				} else if (model instanceof L140MM6C) {
					((L140MM6C) model).setUpdater(user.getUserId());
					((L140MM6C) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l140mm6cDao.save((L140MM6C) model);
				}
			}
		}
	}

	public String changeDateToString(Date datetime) {
		String date = "";
		date = (Util.equals(Util.nullToSpace(datetime) , "") ? "" :
			CapDate.formatDate(datetime,UtilConstants.DateFormat.YYYY_MM_DD));
		return date;
	}
	
	@Override
	public void delete(GenericBean... entity) {
		// TODO Auto-generated method stub
	}
}
