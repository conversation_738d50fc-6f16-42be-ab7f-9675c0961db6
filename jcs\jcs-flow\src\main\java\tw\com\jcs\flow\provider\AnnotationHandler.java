package tw.com.jcs.flow.provider;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.core.FlowException;

/**
 * <h1>基於Annotation的流程處理器</h1> 可根據@Transition所定義的名稱，執行該Method<br/>
 * 如未指定@Transition的路徑名稱，預設為Method的名稱<br/>
 * <br/>
 *
 * 可選擇性覆寫以下2個Method：<br/>
 * <ul>
 * <li>before：每次執行前都會執行此Method</li>
 * <li>after：每次執行後都會執行此Method</li>
 * </ul>
 *
 * <AUTHOR> Software Inc.
 */
public class AnnotationHandler implements FlowHandler {

    @Retention(RetentionPolicy.RUNTIME)
    @Target(ElementType.METHOD)
    public static @interface Transition {
        String value() default "";

        String node() default "";

        @Retention(RetentionPolicy.RUNTIME)
        @Target(ElementType.METHOD)
        public static @interface List {
            Transition[] value() default {};
        }
    }

    Map<String, List<Method>> handlerMap;

    /**
     * 新增節點跟Transition相關的method
     */
    public AnnotationHandler() {
        handlerMap = new HashMap<String, List<Method>>();
        Method[] methods = this.getClass().getMethods();
        for (Method method : methods) {
            java.util.List<Transition> hdleList = new LinkedList<Transition>();
            Transition.List hdles = method.getAnnotation(Transition.List.class);
            if (hdles != null) {
                for (Transition hdle : hdles.value()) {
                    hdleList.add(hdle);
                }
            }
            Transition hdle = method.getAnnotation(Transition.class);
            if (hdle != null) {
                hdleList.add(hdle);
            }
            for (Transition ts : hdleList) {
                if (ts.value().length() == 0 && ts.node().length() > 0) {
                    // 增加節點的關聯Method -> for有指定node，未指定transition
                    appendHandler(ts.node(), method);
                } else {
                    // 增加Transition的關聯Method
                    appendHandler(ts.node() + "_" + ts.value(), method);
                }
            }
        }
    }

    /**
     * 建立List加入傳入的key跟value
     * 
     * @param key
     * @param method
     */
    private void appendHandler(String key, Method method) {
        List<Method> mList = handlerMap.get(key);
        if (mList == null) {
            mList = new LinkedList<Method>();
            handlerMap.put(key, mList);
        }
        mList.add(method);
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.flow.provider.FlowHandler#handle(tw.com.jcs.flow.FlowInstance, java.lang.String, java.lang.String)
     */
    public void handle(FlowInstance instance, String nodeName, String transition) {
        try {
            before(instance, nodeName, transition);
            // 先嘗試找[節點名_路徑名]的Method
            List<Method> methods = handlerMap.get(nodeName + "_" + transition);
            // 再嘗試找未指定節點的[_路徑名]的Method
            List<Method> methodsDefault = handlerMap.get("_" + transition);
            // 如果有節點名，再嘗試找僅指定[節點名]的Method
            List<Method> methodsNode = nodeName.length() > 0 ? handlerMap.get(nodeName) : null;

            // 先執行完整符合的Method
            if (methods != null) {
                for (Method method : methods) {
                    method.invoke(this, instance);
                }
            }
            // 再執行路徑符合的Method
            if (methodsDefault != null) {
                for (Method method : methodsDefault) {
                    method.invoke(this, instance);
                }
            }
            // 最後執行節點符合的Method
            if (methodsNode != null) {
                for (Method method : methodsNode) {
                    method.invoke(this, instance);
                }
            }
            after(instance, nodeName, transition);
        } catch (Exception e) {
            throw new FlowException(e);
        }
    }

    /**
     * 執行節點前，先執行的步驟 - 驗證文件狀態等
     * 
     * @param instance
     * @param nodeName
     * @param transition
     */
    protected void before(FlowInstance instance, String nodeName, String transition) {
    }

    /**
     * 執行節點後，執行的步驟
     * 
     * @param instance
     * @param nodeName
     * @param transition
     */
    protected void after(FlowInstance instance, String nodeName, String transition) {
    }

}
