/* 
 * L140M01E.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/** 額度聯行攤貸比例檔 **/
@NamedEntityGraph(name = "L140M01E-entity-graph", attributeNodes = { @NamedAttributeNode("l140m01a") })
@Entity
@Table(name = "L140M01E", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "flag", "shareBrId" }))
public class L140M01E extends GenericBean implements IDataObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 顯示利率(非DB欄位) **/
	@Transient
	private String showRate;
	
	/** 取得顯示利率(非DB欄位) **/
	public String getShowRate() {
		return showRate;
	}
	/** 設定顯示利率(非DB欄位) **/
	public void setShowRate(String showRate) {
		this.showRate = showRate;
	}

	/** 文件編號 **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 國內分行/海外分行
	 * <p/>
	 * 100/09/26新增<br/>
	 * 1.國內, 2.國外, 3.總處, 4.子銀行
	 */
	@Column(name = "FLAG", length = 1, columnDefinition = "CHAR(1)")
	private String flag;

	/** 攤貸分行 **/
	@Column(name = "SHAREBRID", length = 3, columnDefinition = "CHAR(3)")
	private String shareBrId;

	/**
	 * 計算攤貸方式
	 * <p/>
	 * 100/11/11新增<br/>
	 * 1.依攤貸金額<br/>
	 * 2.依攤貸比例<br/>
	 * 
	 */
	@Column(name = "SHAREFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String shareFlag;

	/**
	 * 攤貸比例分子
	 * <p/>
	 * 101/02/24調整
	 */
	@Column(name = "SHARERATE1", columnDefinition = "DECIMAL(15,0)")
	private BigDecimal shareRate1;

	/**
	 * 攤貸比例分母
	 * <p/>
	 * 101/02/24調整
	 */
	@Column(name = "SHARERATE2", columnDefinition = "DECIMAL(15,0)")
	private BigDecimal shareRate2;

	/**
	 * 攤貸金額
	 * <p/>
	 * 100/11/11補充說明<br/>
	 * ※攤貸比例折算攤貸金額，若有「餘數」則計入本筆額度明細表之「掛帳行」<br/>
	 * 101/02/24調整
	 */
	@Column(name = "SHAREAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal shareAmt;

	/**
	 * 聯貸總金額
	 * <p/>
	 * 101/02/24調整<br/>
	 * invisible
	 */
	@Column(name = "TOTALAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal totalAmt;

	/**
	 * 海外聯貸參貸行額度序號
	 * <p/>
	 * ※海外帳務系統必須獨立取號<br/>
	 * (國內則共用同一額度序號)
	 */
	@Column(name = "SHARENO", length = 12, columnDefinition = "CHAR(12)")
	private String shareNo;

	/** 建立人員號碼 **/
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Date updateTime;

	/**
	 * JOIN條件 關聯檔
	 * 
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "MAINID", referencedColumnName = "MAINID", insertable = false, updatable = false)
	private L140M01A l140m01a;

	public L140M01A getL140m01a() {
		return l140m01a;
	}

	public void setL140m01a(L140M01A l140m01a) {
		this.l140m01a = l140m01a;
	}
	
	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得國內分行/海外分行
	 * <p/>
	 * 100/09/26新增<br/>
	 * 1.國內, 2.國外, 3.總處, 4.子銀行
	 */
	public String getFlag() {
		return this.flag;
	}

	/**
	 * 設定國內分行/海外分行
	 * <p/>
	 * 100/09/26新增<br/>
	 * 1.國內, 2.國外, 3.總處, 4.子銀行
	 **/
	public void setFlag(String value) {
		this.flag = value;
	}

	/** 取得攤貸分行 **/
	public String getShareBrId() {
		return this.shareBrId;
	}

	/** 設定攤貸分行 **/
	public void setShareBrId(String value) {
		this.shareBrId = value;
	}

	/**
	 * 取得計算攤貸方式
	 * <p/>
	 * 100/11/11新增<br/>
	 * 1.依攤貸比例<br/>
	 * 2.依攤貸金額
	 */
	public String getShareFlag() {
		return this.shareFlag;
	}

	/**
	 * 設定計算攤貸方式
	 * <p/>
	 * 100/11/11新增<br/>
	 * 1.依攤貸比例<br/>
	 * 2.依攤貸金額
	 **/
	public void setShareFlag(String value) {
		this.shareFlag = value;
	}

	/**
	 * 取得攤貸比例分子
	 * <p/>
	 * 101/02/24調整
	 */
	public BigDecimal getShareRate1() {
		return this.shareRate1;
	}

	/**
	 * 設定攤貸比例分子
	 * <p/>
	 * 101/02/24調整
	 **/
	public void setShareRate1(BigDecimal value) {
		this.shareRate1 = value;
	}

	/**
	 * 取得攤貸比例分母
	 * <p/>
	 * 101/02/24調整
	 */
	public BigDecimal getShareRate2() {
		return this.shareRate2;
	}

	/**
	 * 設定攤貸比例分母
	 * <p/>
	 * 101/02/24調整
	 **/
	public void setShareRate2(BigDecimal value) {
		this.shareRate2 = value;
	}

	/**
	 * 取得攤貸金額
	 * <p/>
	 * 100/11/11補充說明<br/>
	 * ※攤貸比例折算攤貸金額，若有「餘數」則計入本筆額度明細表之「掛帳行」<br/>
	 * 101/02/24調整
	 */
	public BigDecimal getShareAmt() {
		return this.shareAmt;
	}

	/**
	 * 設定攤貸金額
	 * <p/>
	 * 100/11/11補充說明<br/>
	 * ※攤貸比例折算攤貸金額，若有「餘數」則計入本筆額度明細表之「掛帳行」<br/>
	 * 101/02/24調整
	 **/
	public void setShareAmt(BigDecimal value) {
		this.shareAmt = value;
	}

	/**
	 * 取得聯貸總金額
	 * <p/>
	 * 101/02/24調整<br/>
	 * invisible
	 */
	public BigDecimal getTotalAmt() {
		return this.totalAmt;
	}

	/**
	 * 設定聯貸總金額
	 * <p/>
	 * 101/02/24調整<br/>
	 * invisible
	 **/
	public void setTotalAmt(BigDecimal value) {
		this.totalAmt = value;
	}

	/**
	 * 取得海外聯貸參貸行額度序號
	 * <p/>
	 * ※海外帳務系統必須獨立取號<br/>
	 * (國內則共用同一額度序號)
	 */
	public String getShareNo() {
		return this.shareNo;
	}

	/**
	 * 設定海外聯貸參貸行額度序號
	 * <p/>
	 * ※海外帳務系統必須獨立取號<br/>
	 * (國內則共用同一額度序號)
	 **/
	public void setShareNo(String value) {
		this.shareNo = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}
}
