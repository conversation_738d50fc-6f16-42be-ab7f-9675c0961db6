package com.mega.eloan.lms.base.service;



import tw.com.iisi.cap.exception.CapMessageException;

/**
 * <pre>
 * 授信給號程式
 * </pre>
 * 
 * @since 2011/9/2
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/9/2,Fantasy,new
 *          </ul>
 */
public interface NumberService {

	void init();

	@SuppressWarnings("rawtypes")
	String getNumber(Class clazz);

	@SuppressWarnings("rawtypes")
	String getNumber(Class clazz, String BranchId);

	@SuppressWarnings("rawtypes")
	String getNumber(Class clazz, String BranchId, String year);

	/**
	 * 取得核准文號
	 * 
	 * @param date
	 *            核准日期
	 * @return 號碼
	 */
	int getNumber(String date);

	/**
	 * 取得核准文號
	 * 
	 * @param date
	 *            核准日期
	 * 
	 * @param brNo
	 *            分行號碼
	 * @return 號碼
	 */
	int getNumber(String date, String brNo);

	/**
	 * 案號 year(yyyy)branchId(分行代碼)(兆)clazz字第number號
	 * 
	 * @param clazz
	 * @param branchId
	 * @param year
	 * @param number
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	String getCaseNumber(Class clazz, String branchId,
			String year, String number);

	/**
	 * @param year
	 * @param brno
	 * @param name
	 * @return
	 */
	//String getNumber(String year, String brno, String name);

	String getNumberWithMax(Class<?> clazz, String branchId, String year, int maxVal) throws CapMessageException;
}
