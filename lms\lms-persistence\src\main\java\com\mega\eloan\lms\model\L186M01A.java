/* 
 * L186M01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;

/** 覆審抽樣資料記錄檔 **/
@Entity
@Table(name="L186M01A", uniqueConstraints = @UniqueConstraint(columnNames = {"dataDate","branchId","custId","dupNo","randomType"}))
public class L186M01A extends GenericBean implements IDataObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 
	 * 資料日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="DATADATE", columnDefinition="DATE")
	private Date dataDate;

	/** 
	 * 分行別<p/>
	 * ELF412_BRANCH
	 */
	@Size(max=3)
	@Column(name="BRANCHID", length=3, columnDefinition="CHAR(3)")
	private String branchId;

	/** 
	 * 借款人ID<p/>
	 * ELF412_CUSTID
	 */
	@Size(max=10)
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 
	 * 借款人重覆序號<p/>
	 * ELF412_DUPNO
	 */
	@Size(max=1)
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 
	 * 抽樣種類<p/>
	 * ELF412_RANDOMTYPE
	 */
	@Size(max=1)
	@Column(name="RANDOMTYPE", length=1, columnDefinition="VARCHAR(1)")
	private String randomType;

	/** 
	 * 覆審報告表mainId<p/>
	 * L170M01A的mainId
	 */
	@Size(max=32)
	@Column(name="CASEMAINID", length=32, columnDefinition="CHAR(32)")
	private String caseMainId;

	/** 覆審報告表完成日期 **/
	@Column(name="FINISHTIME", columnDefinition="TIMESTAMP")
	private Timestamp finishTime;

	/** 純小規模營業人 **/
	@Size(max=1)
	@Column(name="ISSMALLBUSS", length=1, columnDefinition="VARCHAR(1)")
	private String isSmallBuss;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 
	 * 取得資料日期<p/>
	 * ELF411_DATAYM:YYYY-MM-01
	 */
	public Date getDataDate() {
		return this.dataDate;
	}
	/**
	 *  設定資料日期<p/>
	 *  ELF411_DATAYM:YYYY-MM-01
	 **/
	public void setDataDate(Date value) {
		this.dataDate = value;
	}

	/** 
	 * 取得分行別<p/>
	 * ELF411_BRNO
	 */
	public String getBranchId() {
		return this.branchId;
	}
	/**
	 *  設定分行別<p/>
	 *  ELF411_BRNO
	 **/
	public void setBranchId(String value) {
		this.branchId = value;
	}

	/** 
	 * 取得借款人ID<p/>
	 * ELF412_CUSTID
	 */
	public String getCustId() {
		return this.custId;
	}
	/**
	 *  設定借款人ID<p/>
	 *  ELF412_CUSTID
	 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 
	 * 取得借款人重覆序號<p/>
	 * ELF412_DUPNO
	 */
	public String getDupNo() {
		return this.dupNo;
	}
	/**
	 *  設定借款人重覆序號<p/>
	 *  ELF412_DUPNO
	 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 
	 * 取得抽樣種類<p/>
	 * ELF412_RANDOMTYPE
	 */
	public String getRandomType() {
		return this.randomType;
	}
	/**
	 *  設定抽樣種類<p/>
	 *  ELF412_RANDOMTYPE
	 **/
	public void setRandomType(String value) {
		this.randomType = value;
	}

	/** 
	 * 取得覆審報告表mainId<p/>
	 * L170M01A的mainId
	 */
	public String getCaseMainId() {
		return this.caseMainId;
	}
	/**
	 *  設定覆審報告表mainId<p/>
	 *  L170M01A的mainId
	 **/
	public void setCaseMainId(String value) {
		this.caseMainId = value;
	}

	/** 取得覆審報告表完成日期 **/
	public Timestamp getFinishTime() {
		return this.finishTime;
	}
	/** 設定覆審報告表完成日期 **/
	public void setFinishTime(Timestamp value) {
		this.finishTime = value;
	}

	/** 取得純小規模營業人 **/
	public String getIsSmallBuss() {
		return this.isSmallBuss;
	}
	/** 設定純小規模營業人 **/
	public void setIsSmallBuss(String value) {
		this.isSmallBuss = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
