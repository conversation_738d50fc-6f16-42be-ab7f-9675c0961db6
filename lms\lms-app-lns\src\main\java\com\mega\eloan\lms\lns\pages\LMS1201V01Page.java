/* 
 * LMS1201V01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lns.pages;

import java.util.ArrayList;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.lns.panels.LMSS08APanel;

/**
 * <pre>
 * 授信簽報書編制中
 * </pre>
 * 
 * @since 2011/10/19
 * <AUTHOR> Lin
 * @version <ul>
 *          <li>2011/10/19,Miller Lin,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms1201v01")
public class LMS1201V01Page extends AbstractEloanInnerView {

	@Autowired
	LMSService lmsService;

	@Override
	public void execute(ModelMap model, PageParameters params) {
		// 設定文件狀態(交易代碼)
		setGridViewStatus(CreditDocStatusEnum.海外_編製中);
		// 加上Button
		ArrayList<LmsButtonEnum> btns = new ArrayList<LmsButtonEnum>();
		// 主管跟經辦都會出現的按鈕

		// 只有主管出現的按鈕
		if (this.getAuth(AuthType.Accept)) {
			btns.add(LmsButtonEnum.View);
		}
		// 只有經辦出現的按鈕
		if (this.getAuth(AuthType.Modify)) {
			btns.add(LmsButtonEnum.Add);
			btns.add(LmsButtonEnum.Modify);
			btns.add(LmsButtonEnum.Delete);
			btns.add(LmsButtonEnum.ChangeCaseFormat);
			btns.add(LmsButtonEnum.ChangeVer);

			// 編製中顯示小規模營業人RAP專用按鈕
			String LMS_RPA_GEN_SMALLBUSS_C_ON = Util.trim(lmsService
					.getSysParamDataValue("LMS_RPA_GEN_SMALLBUSS_C_ON"));
			if (Util.equals(LMS_RPA_GEN_SMALLBUSS_C_ON, "Y")) {
				btns.add(LmsButtonEnum.SmallBussCRpa);
			}

			// J-110-0CCC_05097_B1001 Web e-Loan新增國發基金協助新創事業紓困融資加碼方案微型企業簽報書格式
			// 編製中顯示國發基金協助新創事業紓困融資加碼方案RAP專用按鈕
			String LMS_RPA_GEN_CASETYPE003_ON = Util.trim(lmsService
					.getSysParamDataValue("LMS_RPA_GEN_CASETYPE003_ON"));
			if (Util.equals(LMS_RPA_GEN_CASETYPE003_ON, "Y")) {
				btns.add(LmsButtonEnum.StartUpReliefRpa);
			}

			// btns.add(CreditButtonEnum.Change);
		}
		addToButtonPanel(model, btns.toArray(new LmsButtonEnum[] {}));

		renderJsI18N(LMS1201V01Page.class);
		renderJsI18N(LMSCommomPage.class);
		renderJsI18N(LMSS08APanel.class);
		
		//model.addAttribute("loadScript", "loadScript('pagejs/lns/LMS1201V01Page');");
	}// ;


}
