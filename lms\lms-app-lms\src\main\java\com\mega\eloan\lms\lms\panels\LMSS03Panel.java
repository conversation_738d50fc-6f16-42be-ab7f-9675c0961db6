/* 
 * LMS1205S03Panel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * 額度明細表(企金授權外)
 * </pre>
 * 
 * @since 2012/1/19
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/19,<PERSON>,new
 *          </ul>
 */
public class LMSS03Panel extends Panel {

	public LMSS03Panel(String id) {
		super(id);
	}

	public LMSS03Panel(String id, boolean updatePanelName) {
		super(id, updatePanelName);
	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);

		new LMS1405S01Panel("lms1405s01_panel").processPanelData(model, params);
		new LMS1405S02Panel("lms1405s02_panel").processPanelData(model, params);
		new LMS1405S04Panel("lms1405s04_panel").processPanelData(model, params);
		// J-108-0288_05097_B1001 Web e-Loan授信系統新增合併關係企業額度彙總表
		new LMS1405S06Panel("lms1405s06_panel").processPanelData(model, params);
		new LMS1405S07Panel("lms1405s07_panel").processPanelData(model, params);
		// J-110-0485_05097_B1003 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
		new LMS1405S08Panel("lms1405s08_panel").processPanelData(model, params);
		// J-111-0397 RWA
		new LMS1405S09Panel("lms1405s09_panel").processPanelData(model, params);
		// J-111-0454 調整e-Loan簽報書中「風險權數」頁籤之計算邏輯
		new LMS1405S10Panel("lms1405s10_panel").processPanelData(model, params);
		// BIS
		new LMS1405S11Panel("lms1405s11_panel").processPanelData(model, params);
	}

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

}
