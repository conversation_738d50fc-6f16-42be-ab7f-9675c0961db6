/* 
 *LNLNF033ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.mfaloan.bean.LNF033;
import com.mega.eloan.lms.mfaloan.service.LNLNF033Service;

/**
 * <pre>
 * LN.LN033 期付金控制檔
 * </pre>
 * 
 * @since 2012/11/7
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/11/7,REX,new
 *          </ul>
 */
@Service
public class LNLNF033ServiceImpl extends AbstractMFAloanJdbc implements
		LNLNF033Service {

	@Override
	public LNF033 findByKey(String loanNo) {
		Map<String, Object> rowData = this.getJdbc().queryForMap(
				"LN.LNF033_findByLoanNo", new Object[] { loanNo });
		LNF033 model = null;
		if (rowData != null) {
			model = new LNF033();
			DataParse.map2Bean(rowData, model);
		}
		return model;
	}
}
