/* 
 * C120S01J.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;

import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 個金相關查詢結果檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C120S01J", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId","custId","dupNo"}))
public class C120S01J extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 身分證統編 **/
	@Size(max=10)
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Size(max=1)
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 
	 * 銀行法查詢結果<p/>
	 * C101S01E.isQdata2<br/>
	 *  Y.利害關係人<br/>
	 *  N.非利害關係人
	 */
	@Size(max=1)
	@Column(name="QRESULT", length=1, columnDefinition="CHAR(1)")
	private String Qresult;

	/** 
	 * 金控法44條查詢結果<p/>
	 * C101S01E.isQdata3<br/>
	 *  Y.金控利害關係人<br/>
	 *  N.非金控利害關係人
	 */
	@Size(max=1)
	@Column(name="XQRESULT", length=1, columnDefinition="CHAR(1)")
	private String XQResult;

	/** 
	 * 金控法45條查詢結果<p/>
	 * C101S01E.isQdata16<br/>
	 *  Y.金控利害關係人<br/>
	 *  N.非金控利害關係人
	 */
	@Size(max=1)
	@Column(name="X45QRESULT", length=1, columnDefinition="CHAR(1)")
	private String X45QResult;

	/** 
	 * 主借款人之授信總餘額<p/>
	 * C101S01E.isQdata4<br/>
	 *  單位：新台幣元
	 */
	@Digits(integer=15, fraction=0, groups = Check.class)
	@Column(name="MAINQUOTABAL", columnDefinition="DECIMAL(15,0)")
	private BigDecimal mainQuotaBal;

	/** 
	 * 主借款人之授信總餘額(無擔保)<p/>
	 * C101S01E.isQdata4<br/>
	 *  單位：新台幣元
	 */
	@Digits(integer=15, fraction=0, groups = Check.class)
	@Column(name="BALN", columnDefinition="DECIMAL(15,0)")
	private BigDecimal balN;

	/** 
	 * 借款人加計本筆貸款後金融機構總授信金額<p/>
	 * C101S01E.isQdata4<br/>
	 *  單位：新台幣元
	 */
	@Digits(integer=15, fraction=0, groups = Check.class)
	@Column(name="MAINTOTQTA", columnDefinition="DECIMAL(15,0)")
	private BigDecimal mainTotQta;

	/** 
	 * 本行淨值<p/>
	 * C101S01E.isQdata5<br/>
	 *  單位：新台幣元<br/>
	 *  Select REYEAR, REPURE FROM MIS.ELRELIMT  Order by REYEAR desc
	 */
	@Digits(integer=15, fraction=0, groups = Check.class)
	@Column(name="NETVALUE", columnDefinition="DECIMAL(15,0)")
	private BigDecimal netValue;

	/** 
	 * 借款人在本行授信總餘額<p/>
	 * C101S01E.isQdata5<br/>
	 *  單位：新台幣元
	 */
	@Digits(integer=15, fraction=0, groups = Check.class)
	@Column(name="TBAL", columnDefinition="DECIMAL(15,0)")
	private BigDecimal tBal;

	/** 
	 * 有無超過本行淨值百分之三<p/>
	 * C101S01E.isQdata5<br/>
	 *  Y.有, N.無
	 */
	@Size(max=1)
	@Column(name="RATIO2", length=1, columnDefinition="CHAR(1)")
	private String ratio2;

	/** 
	 * 借款人在本行無擔保授信總餘額<p/>
	 * C101S01E.isQdata5<br/>
	 *  單位：新台幣元
	 */
	@Digits(integer=15, fraction=0, groups = Check.class)
	@Column(name="NBAL", columnDefinition="DECIMAL(15,0)")
	private BigDecimal nBal;

	/** 
	 * 有無超過本行淨值百分之一<p/>
	 * C101S01E.isQdata5<br/>
	 *  Y.有, N.無
	 */
	@Size(max=1)
	@Column(name="RATIO1", length=1, columnDefinition="CHAR(1)")
	private String ratio1;

	/** 
	 * 黑名單查詢日期<p/>
	 * C101S01E.isQdata7
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="BLACKRECQRY", columnDefinition="DATE")
	private Date blackRecQry;

	/** 
	 * 黑名單查詢英文名稱<p/>
	 * C101S01E.isQdata7<br/>
	 *  64個全型字
	 */
	@Size(max=192)
	@Column(name="ENAME", length=192, columnDefinition="VARCHAR(192)")
	private String eName;

	/** 
	 * 黑名單查詢結果<p/>
	 * C101S01E.isQdata7<br/>
	 *  ICustomerService{00=未列於黑名單, 02=是黑名單, 04=可能是黑名單}
	 */
	@Size(max=2)
	@Column(name="ANS1", length=2, columnDefinition="VARCHAR(2)")
	private String ans1;

	/** 
	 * 違約交割查詢日期<p/>
	 * C101S01E.isQdata8
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="QRYDEFAULT", columnDefinition="DATE")
	private Date qryDefault;

	/** 
	 * 違約交割紀錄<p/>
	 * C101S01E.isQdata8<br/>
	 *  Y.有, N.無
	 */
	@Size(max=1)
	@Column(name="DEFAULTREC", length=1, columnDefinition="CHAR(1)")
	private String defaultRec;

	/** 
	 * 違約交割說明事項<p/>
	 * C101S01E.isQdata8<br/>
	 *  100個全型字
	 */
	@Size(max=300)
	@Column(name="DEFAULTMEMO", length=300, columnDefinition="VARCHAR(300)")
	private String defaultMemo;

	/** 
	 * 最近一次退票日期<p/>
	 * C101S01E.isQdata9
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="RTD", columnDefinition="DATE")
	private Date rtD;

	/** 
	 * 退票未清償註記總張數<p/>
	 * C101S01E.isQdata9
	 */
	@Digits(integer=5, fraction=0, groups = Check.class)
	@Column(name="RTNT", columnDefinition="DECIMAL(5,0)")
	private Integer rtNt;

	/** 
	 * 退票未清償註記總金額<p/>
	 * C101S01E.isQdata9<br/>
	 *  單位：新台幣仟元
	 */
	@Digits(integer=15, fraction=0, groups = Check.class)
	@Column(name="RTNM", columnDefinition="DECIMAL(15,0)")
	private BigDecimal rtNm;

	/** 
	 * 退票已清償註記總張數<p/>
	 * C101S01E.isQdata9
	 */
	@Digits(integer=5, fraction=0, groups = Check.class)
	@Column(name="RTYT", columnDefinition="DECIMAL(5,0)")
	private Integer rtYt;

	/** 
	 * 退票已清償註記總金額<p/>
	 * C101S01E.isQdata9<br/>
	 *  單位：新台幣仟元
	 */
	@Digits(integer=15, fraction=0, groups = Check.class)
	@Column(name="RTYM", columnDefinition="DECIMAL(15,0)")
	private BigDecimal rtYm;

	/** 
	 * 拒絕往來日期<p/>
	 * C101S01E.isQdata10
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="RDD", columnDefinition="DATE")
	private Date rdD;

	/** 
	 * 訂約金額<p/>
	 * C101S01E.isQdata11<br/>
	 *  單位：新台幣元
	 */
	@Digits(integer=15, fraction=0, groups = Check.class)
	@Column(name="BAAGM", columnDefinition="DECIMAL(15,0)")
	private BigDecimal baAgm;

	/** 
	 * 授信餘額<p/>
	 * C101S01E.isQdata11<br/>
	 *  單位：新台幣元
	 */
	@Digits(integer=15, fraction=0, groups = Check.class)
	@Column(name="BAAVM", columnDefinition="DECIMAL(15,0)")
	private BigDecimal baAvm;

	/** 
	 * 逾期金額<p/>
	 * C101S01E.isQdata11<br/>
	 *  單位：新台幣元
	 */
	@Digits(integer=15, fraction=0, groups = Check.class)
	@Column(name="BAODM", columnDefinition="DECIMAL(15,0)")
	private BigDecimal baOdm;

	/** 
	 * 逾催呆說明<p/>
	 * C101S01E.isQdata11<br/>
	 *  100個全型字<br/>
	 *  (註)從債務有逾期紀錄者：XXX、XXX、XXX；逾期金額為：9,999,999、9,999,999、9,999,999千元。<br/>
	 *  (註)查借款人、連保人 無 從債務逾期金額。
	 */
	@Size(max=300)
	@Column(name="DIROVNOTE", length=300, columnDefinition="VARCHAR(300)")
	private String dirOvNote;

	/** 
	 * 是否有強制停用記錄<p/>
	 * C101S01E.isQdata13
	 */
	@Size(max=1)
	@Column(name="CSTRD", length=1, columnDefinition="CHAR(1)")
	private String cstRd;

	/** 
	 * 最近一次強制停用日期<p/>
	 * C101S01E.isQdata13
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="CSTDT", columnDefinition="DATE")
	private Date cstDt;

	/** 
	 * 身分證補、換發紀錄<p/>
	 * C101S01E.isQdata12<br/>
	 *  1.有、2.無、3.N.A
	 */
	@Size(max=1)
	@Column(name="ISQDATA12", length=1, columnDefinition="CHAR(1)")
	private String isQdata12;

	/** 
	 * 成年監護制度查詢紀錄<p/>
	 * C101S01E.isQdata15<br/>
	 *  1.有、2.無、3.N.A
	 */
	@Size(max=1)
	@Column(name="ISQDATA15", length=1, columnDefinition="CHAR(1)")
	private String isQdata15;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 疑似偽造證件通報公司編號 **/
	@Size(max=10)
	@Column(name="RCOMID", length=10, columnDefinition="VARCHAR(10)")
	private String rComId;
	
	/** 疑似偽造證件通報公司名稱 **/
	@Size(max=250)
	@Column(name="RCOMNAME", length=250, columnDefinition="VARCHAR(250)")
	private String rComName;

	/** 疑似偽造證件通報單位地址 **/
	@Size(max=300)
	@Column(name="RCOMTARGET", length=300, columnDefinition="VARCHAR(300)")
	private String rComTarget;

	/** 疑似偽造證件通報來源文號 **/
	@Size(max=128)
	@Column(name="RSOURCENO", length=128, columnDefinition="VARCHAR(128)")
	private String rSourceNo;
	
	/** 疑似偽造證件通報資料來源 **/
	@Size(max=1)
	@Column(name="RDATASRC", length=1, columnDefinition="CHAR(1)")
	private String rDataSrc;
	
	/** 地政士比對結果(ref C900M01H.ctlFlag){0:比對後無match資料, '':未執行比對}  => 會把 C101S01Y 最嚴重的ctlFlag寫至 C101S01J */
	@Size(max=1)
	@Column(name="LAACTLFLAG", length=1, columnDefinition="CHAR(1)")
	private String laaCtlFlag;
	
	/** AML RefNo' */
	@Column(name = "AMLREFNO", length = 28, columnDefinition = "VARCHAR(28)")
	private String amlRefNo;
	
	/** 陪同/代辦人員比對結果(ref C900M01J.mainId) */
	@Column(name = "AGENTPIDCMP", length = 32, columnDefinition = "VARCHAR(32)")
	private String agentPIdCmp;
	
	/** 對應L120S09B_OID */
	@Column(name = "AMLREFOID", length = 32, columnDefinition = "VARCHAR(32)")
	private String amlRefOid;
	
	/** 地政士比對 拒絕地政士，判斷ctl是否等於5，且 符合 永慶房屋直營店或信義房屋名義仲介成交案件 及 懲戒紀錄是否屬地政士法第17條 ，則可續做  => 移至 C120S01Y  */
	@Size(max=1)
	@Column(name="LAAMATCHRULEFLAG", length=1, columnDefinition="VARCHAR(1)")
	private String laaMatchRuleFlag;
	
	/** 
	 * T70違約交割查詢日期<p/>
	 * C101S01E.isQdata30
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="QRYDEFAULTT70", columnDefinition="DATE")
	private Date qryDefaultT70;
	
	/** 
	 * T70違約交割紀錄<p/>
	 * C101S01E.isQdata30<br/>
	 *  Y.有, N.無
	 */
	@Size(max=1)
	@Column(name="DEFAULTRECT70", length=1, columnDefinition="CHAR(1)")
	private String defaultRecT70;
	
	/** 
	 * T70未清償總餘額<p/>
	 * C101S01E.isQdata30<br/>
	 *  Y.有, N.無
	 */
	@Digits(integer = 10, fraction = 0)
	@Column(name = "UNSETTLEDBAL", length = 1, columnDefinition = "DECIMAL(10,0)")
	private BigDecimal unsettledBal;
	
	/** 
	 * 聯徵B42從債務查詢－擔保品類別<p/>
	 * C101S01E.isQdata31<br/>
	 *  1.有、2.無、3.N.A
	 */
	@Size(max=1)
	@Column(name="QRYCOLLATERALB42", length=1, columnDefinition="CHAR(1)")
	private String qryCollateralB42;
	
	/** 
	 * 聯徵B42共同債務查詢－擔保品類別<p/>
	 * C101S01E.isQdata32<br/>
	 *  1.有、2.無、3.N.A
	 */
	@Size(max=1)
	@Column(name="QRYMUTUALDEBTB42", length=1, columnDefinition="CHAR(1)")
	private String qryMutualDebtB42;
	
	/** 
	 * 借款人三年內購置不動產結案資訊，是否有近一年有二戶以上授信借貸結案紀錄<p/>
	 * C101S01E.isQdata33<br/>
	 *  1.有、2.無、3.N.A
	 */
	@Size(max=1)
	@Column(name="OVER2DATAPASTY", length=1, columnDefinition="CHAR(1)")
	private String over2DataPastY;
	
	/** 
	 * 借款人三年內購置不動產結案資訊，是否有近三年有二戶以上授信借貸結案紀錄<p/>
	 * C101S01E.isQdata34<br/>
	 *  1.有、2.無、3.N.A
	 */
	@Size(max=1)
	@Column(name="OVER2DATAPAST3Y", length=1, columnDefinition="CHAR(1)")
	private String over2DataPast3y;
	
	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得身分證統編 **/
	public String getCustId() {
		return this.custId;
	}
	/** 設定身分證統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得身分證統編重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}
	/** 設定身分證統編重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 
	 * 取得銀行法查詢結果<p/>
	 * C101S01E.isQdata2<br/>
	 *  Y.利害關係人<br/>
	 *  N.非利害關係人
	 */
	public String getQresult() {
		return this.Qresult;
	}
	/**
	 *  設定銀行法查詢結果<p/>
	 *  C101S01E.isQdata2<br/>
	 *  Y.利害關係人<br/>
	 *  N.非利害關係人
	 **/
	public void setQresult(String value) {
		this.Qresult = value;
	}

	/** 
	 * 取得金控法44條查詢結果<p/>
	 * C101S01E.isQdata3<br/>
	 *  Y.金控利害關係人<br/>
	 *  N.非金控利害關係人
	 */
	public String getXQResult() {
		return this.XQResult;
	}
	/**
	 *  設定金控法44條查詢結果<p/>
	 *  C101S01E.isQdata3<br/>
	 *  Y.金控利害關係人<br/>
	 *  N.非金控利害關係人
	 **/
	public void setXQResult(String value) {
		this.XQResult = value;
	}

	/** 
	 * 取得金控法45條查詢結果<p/>
	 * C101S01E.isQdata16<br/>
	 *  Y.金控利害關係人<br/>
	 *  N.非金控利害關係人
	 */
	public String getX45QResult() {
		return this.X45QResult;
	}
	/**
	 *  設定金控法45條查詢結果<p/>
	 *  C101S01E.isQdata16<br/>
	 *  Y.金控利害關係人<br/>
	 *  N.非金控利害關係人
	 **/
	public void setX45QResult(String value) {
		this.X45QResult = value;
	}

	/** 
	 * 取得主借款人之授信總餘額<p/>
	 * C101S01E.isQdata4<br/>
	 *  單位：新台幣元
	 */
	public BigDecimal getMainQuotaBal() {
		return this.mainQuotaBal;
	}
	/**
	 *  設定主借款人之授信總餘額<p/>
	 *  C101S01E.isQdata4<br/>
	 *  單位：新台幣元
	 **/
	public void setMainQuotaBal(BigDecimal value) {
		this.mainQuotaBal = value;
	}

	/** 
	 * 取得主借款人之授信總餘額(無擔保)<p/>
	 * C101S01E.isQdata4<br/>
	 *  單位：新台幣元
	 */
	public BigDecimal getBalN() {
		return this.balN;
	}
	/**
	 *  設定主借款人之授信總餘額(無擔保)<p/>
	 *  C101S01E.isQdata4<br/>
	 *  單位：新台幣元
	 **/
	public void setBalN(BigDecimal value) {
		this.balN = value;
	}

	/** 
	 * 取得借款人加計本筆貸款後金融機構總授信金額<p/>
	 * C101S01E.isQdata4<br/>
	 *  單位：新台幣元
	 */
	public BigDecimal getMainTotQta() {
		return this.mainTotQta;
	}
	/**
	 *  設定借款人加計本筆貸款後金融機構總授信金額<p/>
	 *  C101S01E.isQdata4<br/>
	 *  單位：新台幣元
	 **/
	public void setMainTotQta(BigDecimal value) {
		this.mainTotQta = value;
	}

	/** 
	 * 取得本行淨值<p/>
	 * C101S01E.isQdata5<br/>
	 *  單位：新台幣元<br/>
	 *  Select REYEAR, REPURE FROM MIS.ELRELIMT  Order by REYEAR desc
	 */
	public BigDecimal getNetValue() {
		return this.netValue;
	}
	/**
	 *  設定本行淨值<p/>
	 *  C101S01E.isQdata5<br/>
	 *  單位：新台幣元<br/>
	 *  Select REYEAR, REPURE FROM MIS.ELRELIMT  Order by REYEAR desc
	 **/
	public void setNetValue(BigDecimal value) {
		this.netValue = value;
	}

	/** 
	 * 取得借款人在本行授信總餘額<p/>
	 * C101S01E.isQdata5<br/>
	 *  單位：新台幣元
	 */
	public BigDecimal getTBal() {
		return this.tBal;
	}
	/**
	 *  設定借款人在本行授信總餘額<p/>
	 *  C101S01E.isQdata5<br/>
	 *  單位：新台幣元
	 **/
	public void setTBal(BigDecimal value) {
		this.tBal = value;
	}

	/** 
	 * 取得有無超過本行淨值百分之三<p/>
	 * C101S01E.isQdata5<br/>
	 *  Y.有, N.無
	 */
	public String getRatio2() {
		return this.ratio2;
	}
	/**
	 *  設定有無超過本行淨值百分之三<p/>
	 *  C101S01E.isQdata5<br/>
	 *  Y.有, N.無
	 **/
	public void setRatio2(String value) {
		this.ratio2 = value;
	}

	/** 
	 * 取得借款人在本行無擔保授信總餘額<p/>
	 * C101S01E.isQdata5<br/>
	 *  單位：新台幣元
	 */
	public BigDecimal getNBal() {
		return this.nBal;
	}
	/**
	 *  設定借款人在本行無擔保授信總餘額<p/>
	 *  C101S01E.isQdata5<br/>
	 *  單位：新台幣元
	 **/
	public void setNBal(BigDecimal value) {
		this.nBal = value;
	}

	/** 
	 * 取得有無超過本行淨值百分之一<p/>
	 * C101S01E.isQdata5<br/>
	 *  Y.有, N.無
	 */
	public String getRatio1() {
		return this.ratio1;
	}
	/**
	 *  設定有無超過本行淨值百分之一<p/>
	 *  C101S01E.isQdata5<br/>
	 *  Y.有, N.無
	 **/
	public void setRatio1(String value) {
		this.ratio1 = value;
	}

	/** 
	 * 取得黑名單查詢日期<p/>
	 * C101S01E.isQdata7
	 */
	public Date getBlackRecQry() {
		return this.blackRecQry;
	}
	/**
	 *  設定黑名單查詢日期<p/>
	 *  C101S01E.isQdata7
	 **/
	public void setBlackRecQry(Date value) {
		this.blackRecQry = value;
	}

	/** 
	 * 取得黑名單查詢英文名稱<p/>
	 * C101S01E.isQdata7<br/>
	 *  64個全型字
	 */
	public String getEName() {
		return this.eName;
	}
	/**
	 *  設定黑名單查詢英文名稱<p/>
	 *  C101S01E.isQdata7<br/>
	 *  64個全型字
	 **/
	public void setEName(String value) {
		this.eName = value;
	}

	/** 
	 * 取得黑名單查詢結果<p/>
	 * C101S01E.isQdata7<br/>
	 *  ICustomerService{00=未列於黑名單, 02=是黑名單, 04=可能是黑名單}
	 */
	public String getAns1() {
		return this.ans1;
	}
	/**
	 *  設定黑名單查詢結果<p/>
	 *  C101S01E.isQdata7<br/>
	 *  ICustomerService{00=未列於黑名單, 02=是黑名單, 04=可能是黑名單}
	 **/
	public void setAns1(String value) {
		this.ans1 = value;
	}

	/** 
	 * 取得違約交割查詢日期<p/>
	 * C101S01E.isQdata8
	 */
	public Date getQryDefault() {
		return this.qryDefault;
	}
	/**
	 *  設定違約交割查詢日期<p/>
	 *  C101S01E.isQdata8
	 **/
	public void setQryDefault(Date value) {
		this.qryDefault = value;
	}

	/** 
	 * 取得違約交割紀錄<p/>
	 * C101S01E.isQdata8<br/>
	 *  Y.有, N.無
	 */
	public String getDefaultRec() {
		return this.defaultRec;
	}
	/**
	 *  設定違約交割紀錄<p/>
	 *  C101S01E.isQdata8<br/>
	 *  Y.有, N.無
	 **/
	public void setDefaultRec(String value) {
		this.defaultRec = value;
	}

	/** 
	 * 取得違約交割說明事項<p/>
	 * C101S01E.isQdata8<br/>
	 *  100個全型字
	 */
	public String getDefaultMemo() {
		return this.defaultMemo;
	}
	/**
	 *  設定違約交割說明事項<p/>
	 *  C101S01E.isQdata8<br/>
	 *  100個全型字
	 **/
	public void setDefaultMemo(String value) {
		this.defaultMemo = value;
	}

	/** 
	 * 取得最近一次退票日期<p/>
	 * C101S01E.isQdata9
	 */
	public Date getRtD() {
		return this.rtD;
	}
	/**
	 *  設定最近一次退票日期<p/>
	 *  C101S01E.isQdata9
	 **/
	public void setRtD(Date value) {
		this.rtD = value;
	}

	/** 
	 * 取得退票未清償註記總張數<p/>
	 * C101S01E.isQdata9
	 */
	public Integer getRtNt() {
		return this.rtNt;
	}
	/**
	 *  設定退票未清償註記總張數<p/>
	 *  C101S01E.isQdata9
	 **/
	public void setRtNt(Integer value) {
		this.rtNt = value;
	}

	/** 
	 * 取得退票未清償註記總金額<p/>
	 * C101S01E.isQdata9<br/>
	 *  單位：新台幣仟元
	 */
	public BigDecimal getRtNm() {
		return this.rtNm;
	}
	/**
	 *  設定退票未清償註記總金額<p/>
	 *  C101S01E.isQdata9<br/>
	 *  單位：新台幣仟元
	 **/
	public void setRtNm(BigDecimal value) {
		this.rtNm = value;
	}

	/** 
	 * 取得退票已清償註記總張數<p/>
	 * C101S01E.isQdata9
	 */
	public Integer getRtYt() {
		return this.rtYt;
	}
	/**
	 *  設定退票已清償註記總張數<p/>
	 *  C101S01E.isQdata9
	 **/
	public void setRtYt(Integer value) {
		this.rtYt = value;
	}

	/** 
	 * 取得退票已清償註記總金額<p/>
	 * C101S01E.isQdata9<br/>
	 *  單位：新台幣仟元
	 */
	public BigDecimal getRtYm() {
		return this.rtYm;
	}
	/**
	 *  設定退票已清償註記總金額<p/>
	 *  C101S01E.isQdata9<br/>
	 *  單位：新台幣仟元
	 **/
	public void setRtYm(BigDecimal value) {
		this.rtYm = value;
	}

	/** 
	 * 取得拒絕往來日期<p/>
	 * C101S01E.isQdata10
	 */
	public Date getRdD() {
		return this.rdD;
	}
	/**
	 *  設定拒絕往來日期<p/>
	 *  C101S01E.isQdata10
	 **/
	public void setRdD(Date value) {
		this.rdD = value;
	}

	/** 
	 * 取得訂約金額<p/>
	 * C101S01E.isQdata11<br/>
	 *  單位：新台幣元
	 */
	public BigDecimal getBaAgm() {
		return this.baAgm;
	}
	/**
	 *  設定訂約金額<p/>
	 *  C101S01E.isQdata11<br/>
	 *  單位：新台幣元
	 **/
	public void setBaAgm(BigDecimal value) {
		this.baAgm = value;
	}

	/** 
	 * 取得授信餘額<p/>
	 * C101S01E.isQdata11<br/>
	 *  單位：新台幣元
	 */
	public BigDecimal getBaAvm() {
		return this.baAvm;
	}
	/**
	 *  設定授信餘額<p/>
	 *  C101S01E.isQdata11<br/>
	 *  單位：新台幣元
	 **/
	public void setBaAvm(BigDecimal value) {
		this.baAvm = value;
	}

	/** 
	 * 取得逾期金額<p/>
	 * C101S01E.isQdata11<br/>
	 *  單位：新台幣元
	 */
	public BigDecimal getBaOdm() {
		return this.baOdm;
	}
	/**
	 *  設定逾期金額<p/>
	 *  C101S01E.isQdata11<br/>
	 *  單位：新台幣元
	 **/
	public void setBaOdm(BigDecimal value) {
		this.baOdm = value;
	}

	/** 
	 * 取得逾催呆說明<p/>
	 * C101S01E.isQdata11<br/>
	 *  100個全型字<br/>
	 *  (註)從債務有逾期紀錄者：XXX、XXX、XXX；逾期金額為：9,999,999、9,999,999、9,999,999千元。<br/>
	 *  (註)查借款人、連保人 無 從債務逾期金額。
	 */
	public String getDirOvNote() {
		return this.dirOvNote;
	}
	/**
	 *  設定逾催呆說明<p/>
	 *  C101S01E.isQdata11<br/>
	 *  100個全型字<br/>
	 *  (註)從債務有逾期紀錄者：XXX、XXX、XXX；逾期金額為：9,999,999、9,999,999、9,999,999千元。<br/>
	 *  (註)查借款人、連保人 無 從債務逾期金額。
	 **/
	public void setDirOvNote(String value) {
		this.dirOvNote = value;
	}

	/** 
	 * 取得是否有強制停用記錄<p/>
	 * C101S01E.isQdata13
	 */
	public String getCstRd() {
		return this.cstRd;
	}
	/**
	 *  設定是否有強制停用記錄<p/>
	 *  C101S01E.isQdata13
	 **/
	public void setCstRd(String value) {
		this.cstRd = value;
	}

	/** 
	 * 取得最近一次強制停用日期<p/>
	 * C101S01E.isQdata13
	 */
	public Date getCstDt() {
		return this.cstDt;
	}
	/**
	 *  設定最近一次強制停用日期<p/>
	 *  C101S01E.isQdata13
	 **/
	public void setCstDt(Date value) {
		this.cstDt = value;
	}

	/** 
	 * 取得身分證補、換發紀錄<p/>
	 * C101S01E.isQdata12<br/>
	 *  1.有、2.無、3.N.A
	 */
	public String getIsQdata12() {
		return this.isQdata12;
	}
	/**
	 *  設定身分證補、換發紀錄<p/>
	 *  C101S01E.isQdata12<br/>
	 *  1.有、2.無、3.N.A
	 **/
	public void setIsQdata12(String value) {
		this.isQdata12 = value;
	}

	/** 
	 * 取得成年監護制度查詢紀錄<p/>
	 * C101S01E.isQdata15<br/>
	 *  1.有、2.無、3.N.A
	 */
	public String getIsQdata15() {
		return this.isQdata15;
	}
	/**
	 *  設定成年監護制度查詢紀錄<p/>
	 *  C101S01E.isQdata15<br/>
	 *  1.有、2.無、3.N.A
	 **/
	public void setIsQdata15(String value) {
		this.isQdata15 = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
	
	/** 取得疑似偽造證件通報公司編號 **/
	public String getRComId() {
		return this.rComId;
	}
	/** 設定疑似偽造證件通報公司編號 **/
	public void setRComId(String value) {
		this.rComId = value;
	}
	
	/** 取得疑似偽造證件通報公司名稱 **/
	public String getRComName() {
		return this.rComName;
	}
	/** 設定疑似偽造證件通報公司名稱 **/
	public void setRComName(String value) {
		this.rComName = value;
	}
	
	/** 取得疑似偽造證件通報單位地址 **/
	public String getRComTarget() {
		return this.rComTarget;
	}
	/** 設定疑似偽造證件通報單位地址 **/
	public void setRComTarget(String value) {
		this.rComTarget = value;
	}
	
	/** 取得疑似偽造證件通報來源文號 **/
	public String getRSourceNo() {
		return this.rSourceNo;
	}
	/** 設定疑似偽造證件通報來源文號 **/
	public void setRSourceNo(String value) {
		this.rSourceNo = value;
	}
	
	/** 取得疑似偽造證件通報資料來源 **/
	public String getRDataSrc() {
		return this.rDataSrc;
	}
	/** 設定疑似偽造證件通報資料來源 **/
	public void setRDataSrc(String value) {
		this.rDataSrc = value;
	}

	/** 取得地政士比對結果(ref C900M01H.ctlFlag) **/
	public String getLaaCtlFlag() {
		return this.laaCtlFlag;
	}
	/** 設定地政士比對結果(ref C900M01H.ctlFlag) **/
	public void setLaaCtlFlag(String value) {
		this.laaCtlFlag = value;
	}

	/** 取得 **/
	public String getAmlRefNo() {
		return this.amlRefNo;
	}
	/** 設定 **/
	public void setAmlRefNo(String value) {
		this.amlRefNo = value;
	}
	
	/** 取得陪同/代辦人員比對結果(ref C900M01J.mainId) **/
	public String getAgentPIdCmp() {
		return agentPIdCmp;
	}
	/** 設定陪同/代辦人員比對結果(ref C900M01J.mainId) **/
	public void setAgentPIdCmp(String agentPIdCmp) {
		this.agentPIdCmp = agentPIdCmp;
	}	

	/** 取得對應L120S09B_OID**/
	public String getAmlRefOid() {
		return amlRefOid;
	}
	/** 設定對應L120S09B_OID**/
	public void setAmlRefOid(String amlRefOid) {
		this.amlRefOid = amlRefOid;
	}	
	
	/** 取得地政士黑名單 判斷拒往戶符合特殊條件仍可續做註記**/
	public String getLaaMatchRuleFlag() {
		return laaMatchRuleFlag;
	}
	/** 設定取得地政士黑名單 判斷拒往戶符合特殊條件仍可續做註記**/
	public void setLaaMatchRuleFlag(String laaMatchRuleFlag) {
		this.laaMatchRuleFlag = laaMatchRuleFlag;
	}
	
	public Date getQryDefaultT70() {
		return qryDefaultT70;
	}
	public void setQryDefaultT70(Date qryDefaultT70) {
		this.qryDefaultT70 = qryDefaultT70;
	}
	public String getDefaultRecT70() {
		return defaultRecT70;
	}
	public void setDefaultRecT70(String defaultRecT70) {
		this.defaultRecT70 = defaultRecT70;
	}
	public BigDecimal getUnsettledBal() {
		return unsettledBal;
	}
	public void setUnsettledBal(BigDecimal unsettledBal) {
		this.unsettledBal = unsettledBal;
	}
	public String getQryCollateralB42() {
		return qryCollateralB42;
	}
	public void setQryCollateralB42(String qryCollateralB42) {
		this.qryCollateralB42 = qryCollateralB42;
	}
	public String getQryMutualDebtB42() {
		return qryMutualDebtB42;
	}
	public void setQryMutualDebtB42(String qryMutualDebtB42) {
		this.qryMutualDebtB42 = qryMutualDebtB42;
	}
	public String getOver2DataPastY() {
		return over2DataPastY;
	}
	public void setOver2DataPastY(String over2DataPastY) {
		this.over2DataPastY = over2DataPastY;
	}
	public String getOver2DataPast3y() {
		return over2DataPast3y;
	}
	public void setOver2DataPast3y(String over2DataPast3y) {
		this.over2DataPast3y = over2DataPast3y;
	}
	
}
