package com.mega.eloan.lms.cls.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;

import tw.com.jcs.common.Util;

/**
 * <pre>
 * 個金個人資料清冊作業  - 申貸文件
 * </pre>
 * 
 * @since 2014/04/04
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
public class CLS8011S02Panel extends Panel {

	private String itemType;

	public CLS8011S02Panel(String id) {
		super(id);
	}

	public CLS8011S02Panel(String id, boolean updatePanelName,
			String itemType) {
		super(id, updatePanelName);
		this.itemType = itemType;
	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);

		boolean isA = false;
		boolean isB = false;
		boolean isC = false;
		boolean isOther = false;
		
		if(Util.equals("A", itemType)){
			isA = true;
		}else if(Util.equals("B", itemType)){
			isB = true;
		}else if(Util.equals("C", itemType)){
			isC = true;
		}else {
			isOther = true;
		}

		// UPGRADE: 前端須配合改Thymeleaf的樣式
//		add(_getComp("pageItemTypeA", isA));
//		add(_getComp("pageItemTypeB", isB));
//		add(_getComp("pageItemTypeC", isC));
//		add(_getComp("pageItemTypeOther", isOther));
		model.addAttribute("pageItemTypeA", isA);
		model.addAttribute("pageItemTypeB", isB);
		model.addAttribute("pageItemTypeC", isC);
		model.addAttribute("pageItemTypeOther", isOther);
	}

	/**/
	private static final long serialVersionUID = 1L;

	// UPGRADE: 前端須配合改Thymeleaf的樣式
//	private Component _getComp(String id, boolean visible){
//		Label r = new Label(id, "");
//		r.setVisible(visible);
//		return r;
//	}
}
