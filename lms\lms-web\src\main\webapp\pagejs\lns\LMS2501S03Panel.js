var pageAction = {
    handler: 'lms2501m01formhandler',
    grid: null,
    IVRGrid: null,
    build: function(obj){
    	var gridview_colModel =  [{
            colHeader: i18n.lms2501m01['cls3301v00.custId'],//'統編',
            name: 'custId',
            align: "center",
            sortable: false,
            width: 50
        },{
            colHeader: i18n.lms2501m01['cls3301v00.custName'],//'戶名',
            name: 'custName',
            align: "center",
            sortable: false,
            width: 50
        },{
            colHeader: i18n.lms2501m01['cls3301v00.record_FileName'],//'錄音檔名',
            name: 'record_FileName',
            sortable: false,
            align: "center",
            formatter: 'click',
            onclick: pageAction.openDoc,
            width: 200
        },{
            colHeader: i18n.lms2501m01['cls3301v00.record_FileName'],//'錄音檔名但無副檔名',
            name: 'record_FileName2',
            hidden: true
        },{
            colHeader: i18n.lms2501m01['cls3301v00.record_Url'],//"RECORD_URL",
            name: 'record_Url',
            hidden: true
        },{
            name: 'record_UserID',
            hidden: true
        },{
            name: 'record_LightId',
            hidden: true
        }];
    	
    	var selectgridview_colModel =  [{
            colHeader: i18n.lms2501m01['cls3301v00.custId'],//'統編',
            name: 'custId',
            align: "center",
            sortable: false,
            width: 60
        },{
            colHeader: i18n.lms2501m01['cls3301v00.record_Bran_Name'],//'分行',
            name: 'record_Bran_Name',
            sortable: false,
            width: 50
        }, {
            colHeader: i18n.lms2501m01['cls3301v00.record_FileName'],//'錄音檔名',
            name: 'record_FileName',
            sortable: false,
            width: 120
        },{
            colHeader: i18n.lms2501m01['cls3301v00.record_User_Code'],//'建檔人員',
            name: 'record_User_Name',
            align: "center",
            sortable: false,
            width: 50
        },{
            colHeader: i18n.lms2501m01['cls3301v00.record_Create_Date'],//'建檔日期',
            name: 'record_Create_Date',
            align: "center",
            sortable: false,
            width: 80
        },{
            colHeader: i18n.lms2501m01['cls3301v00.mgr_User_Code'],//'覆核主管',
            name: 'mgr_User_Name',
            align: "center",
            sortable: false,
            width: 50
        },{
            colHeader: i18n.lms2501m01['cls3301v00.mgr_Sign_Date'],//'主管覆核日期',
            name: 'mgr_Sign_Date',
            align: "center",
            sortable: false,
            width: 80
        },{
            colHeader: i18n.lms2501m01['cls3301v00.record_Url'],//"RECORD_URL",
            name: 'record_Url',
            hidden: true
        },{
            colHeader: i18n.lms2501m01['cls3301v00.record_FileName'],//'錄音檔名但無副檔名',
            name: 'record_FileName2',
            hidden: true
        },{
            name: 'record_UserID',
            hidden: true
        },{
            name: 'record_LightId',
            hidden: true
        }];

        pageAction.grid = $("#gridview").iGrid({
            //localFirst: true,
            handler: 'lms2501gridhandler',
            height: 250,
            action: "queryIVRgrid", //
            rowNum: 15,
            rownumbers: true,
            colModel: gridview_colModel
        });

        pageAction.IVRGrid = $("#lmsIVRGrid").iGrid({
            localFirst: true,
            handler: 'lms2501gridhandler',
            height: 210,
            action: "queryIVRFiltergird",
            multiselect: true,
            hideMultiselect: false,
            rowNum: 15,
            rownumbers: true,
            colModel: selectgridview_colModel
        });
    	
    },
    reloadIVRGrid: function(data){
        if (data) {
            pageAction.grid.jqGrid("setGridParam", {
                postData: data,
                page: 1,
                search: true
            }).trigger("reloadGrid");
        } else {
            pageAction.grid.trigger('reloadGrid');
        }
    },
    openClsIVRList: function(){
        pageAction.IVRGrid.reload({
            findId: ""
        });
        $('#selectIVRBox').thickbox({
            title: '',
            width: 800,
            height: 380,
            modal: true,
            align: 'center',
            valign: 'bottom',
            i18n: i18n.def,
            buttons: {
                'sure': function(){
                    var datas = pageAction.getCustRowData();
                    if (datas) {
                        var rows = [];
                        for (var name in datas) {
                            var data = datas[name];
                            rows.push(data.custId + ";" + data.record_FileName);
                        }
                        $.ajax({
                            handler: pageAction.handler,
                            action: 'addIVRList',
                            formId: 'empty', //test
                            data: {
                                rows: rows
                            },
                            success: function(response){
                                $.thickbox.close();
                                pageAction.reloadIVRGrid();
                            }
                        });
                    }
                },
                'cancel': function(){
                    $.thickbox.close();
                }
            }
        });
    },
    /**
     * 取得資料表之選擇列(多筆)
     */
    getCustRowData: function(){
        var datas = pageAction.IVRGrid.getSelRowDatas();
        if (!datas) {
            MegaApi.showErrorMessage(i18n.def["confirmTitle"], i18n.def["action_005"]);
        }
        return datas;
    },
    getgridRowData: function(){
        var datas = pageAction.grid.getSelRowDatas();
        if (!datas) {
            MegaApi.showErrorMessage(i18n.def["confirmTitle"], i18n.def["action_005"]);
        }
        return datas;
    },
    openDoc:function(cellvalue, options, rowObject){
    	$.form.submit({
            url: rowObject.record_Url,
            target: "_blank",
            data: {
            	lightID: rowObject.record_LightId,
            	UserID: rowObject.record_UserID,
            	RECORD_FILENAME: rowObject.record_FileName2
            }
        });
    }
}
$(document).ready(function(){
	var obj=null;
	pageAction.build(obj);
    $("#selectIVR").click(function(){
    	pageAction.openClsIVRList();
	});
    $("#deleteIVR").click(function(){
    	var data = pageAction.getgridRowData();
        if (data) {
            MegaApi.confirmMessage(i18n.def["confirmDelete"], function(action){
                if (action) {

                    $.ajax({
                        handler: pageAction.handler,
                        action: 'deleteIVRList',
                        data: {
                        	record_FileName :data.record_FileName,
                            deleteCustId: data.custId
                        },
                        success: function(response){
                            pageAction.grid.reload();
                            MegaApi.showPopMessage(i18n.def["confirmTitle"], i18n.def["confirmDeleteSuccess"]);
                        }
                    });
                }
            });
        }

	});
});