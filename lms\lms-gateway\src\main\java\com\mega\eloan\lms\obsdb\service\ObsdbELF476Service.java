/* 
 *ObsdbELF476 Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.obsdb.service;

import java.util.List;

/**
 * <pre>
 * 企金簽案費率檔  ELF476
 * </pre>
 * 
 * @since 2012/1/4
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/4,REX,new
 *          </ul>
 */
public interface ObsdbELF476Service {
	/**
	 * 新增
	 * 
	 * @param BRNID
	 *            上傳銀行代碼
	 * @param dataList
	 *            sql 陣列
	 * 
	 *            <pre>
	 * 
	 *  cntrNo 額度序號
	 *  subject 科目
	 *  sDate 核准日期
	 *  rType  保證/費率種類 1:商業本票 2.開發保證函 3.公司債保證 4.承兌費率
	 *  cp_Type  商業本票種類
	 *  cp1_Rate   年費率1
	 *  cp1_Fee    最低收費新台幣X元
	 *  cp2_Rate1  年費率2
	 *  cp2_Rate2  若由本行承銷則年費率X
	 *  cp_Des     商業本票文字敘述
	 *  cf_Type    開發保證函種類
	 *  cf1_Rate   年費率1
	 *  cf1_mth1   每X個月為一期按期計收
	 *  cf1_md     計收方法
	 *  cf1_mth2   X月
	 *  cf2_Rate   年費率2
	 *  cf2_md     計收方法
	 *  cf2_mth    X月
	 *  cf_des     開發保證函文字敘述
	 *  cpy_type   公司債保證種類
	 *  cpy1_rate  年費率1
	 *  cpy1_mth1  每X個月為一期按期計收
	 *  cpy1_md    計收方法
	 *  cpy1_mth2  X月
	 *  cpy2_rate  年費率2
	 *  cpy2_md  計收方法
	 *  cpy2_mth   X月
	 *  cpy_des  公司債文字敘述
	 *  pa_type  承兌費率
	 *  pa1_rate  年費率1
	 *  pa1_md   計收方法
	 *  pa1_mth   X月
	 *  pa2_rate   年費率2
	 *  pa2_md   計收方法
	 *  pa2_mth   X月
	 *  pa_des  承兌費率文字敘述
	 *  updater   資料修改人（行員代號） 
	 *  TMESTAMP  資料修改時間
	 * 
	 * </pre>
	 */
	void insert(String BRNID, List<Object[]> dataList);

	/**
	 * 刪除
	 * 
	 * @param cntrNo
	 *            額度序號
	 * @param subject
	 *            授信科目
	 * @param sDate
	 *            系統時間
	 *@param rtype
	 *            系統時間
	 */
	public void delByKey(String BRNID, String cntrNo, String subject,
			String sDate, String rtype);
}
