/* 
 * LMS0005V00Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.model.L000M01A;

import tw.com.iisi.cap.model.GenericBean;

/**
 * <pre>
 * 近期已收案件(個金)
 * </pre>
 * 
 * @since 2012/1/12
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/12,REX,new
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls0001v00")
public class CLS0001V00Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		// setGridViewStatus(CreditDocStatusEnum.海外_編製中);
		// 加上Button

		//renderJsI18N(CLS1141V01Page.class);
		addToButtonPanel(model, LmsButtonEnum.View);
		renderJsI18N(CLS0001V00Page.class);
	}

	@Override
	protected Class<? extends GenericBean> getDomainClass() {
		return L000M01A.class;
	}

}
