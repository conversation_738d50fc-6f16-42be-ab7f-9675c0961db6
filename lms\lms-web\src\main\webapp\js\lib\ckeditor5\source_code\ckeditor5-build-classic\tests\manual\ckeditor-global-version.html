<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<title>CKEditor 5 – classic editor build – global variable test</title>
	<style>
		body {
			max-width: 800px;
			margin: 20px auto;
		}
	</style>
</head>
<body>

<h1>CKEditor 5 – classic editor build – global variable test</h1>

<h2>In the default language</h2>

<div id="editor">
	<h2>About CKEditor&nbsp;5</h2>

	<p>This is <a href="https://ckeditor.com">CKEditor&nbsp;5</a>.</p>
</div>

<h2>In German</h2>

<div id="editor-de">
	<h2>About CKEditor&nbsp;5</h2>

	<p>This is <a href="https://ckeditor.com">CKEditor&nbsp;5</a>.</p>
</div>

<script src="../../build/ckeditor.js"></script>
<script src="../../build/translations/de.js"></script>
<script>
	ClassicEditor.create( document.querySelector( '#editor' ) )
		.then( editor => {
			window.editor = editor;
		} )
		.catch( error => {
			console.error( 'There was a problem initializing the editor.', error );
		} );

	ClassicEditor
		.create( document.querySelector( '#editor-de' ), {
			language: 'de'
		} )
		.then( editor => {
			window.editor = editor;
		} )
		.catch( error => {
			console.error( 'There was a problem initializing the editor.', error );
		} );
</script>

</body>
</html>
