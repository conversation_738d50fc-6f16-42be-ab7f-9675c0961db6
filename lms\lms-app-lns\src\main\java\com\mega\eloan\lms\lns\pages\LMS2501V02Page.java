package com.mega.eloan.lms.lns.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;

/**
 * 企金模擬動審-待覆核
 * 
 * <AUTHOR>
 * 
 */
@Controller
@RequestMapping("/lms/lms2501v02")
public class LMS2501V02Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		setGridViewStatus(CreditDocStatusEnum.海外_待覆核);
		// 加上Button
		addToButtonPanel(model, LmsButtonEnum.View);
		renderJsI18N(LMS2501V01Page.class);
		
		model.addAttribute("hasHtml", false);
		model.addAttribute("loadScript", "loadScript('pagejs/lns/LMS2501V01Page');");
	}

}
