/* 
 * L120S19B.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 無紙化簽報授信條件修改檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L120S19B", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L120S19B extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 
	 * 文件編號
	 * 簽報書MAINID
	 */
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * 授信類別
	 * 貸款額度:quota, 貸款期間:term, 貸款利率: rate, 貸款費用: fare
	 */
	@Size(max=10)
	@Column(name="TYPE", length=10, columnDefinition="VARCHAR(10)")
	private String type;

	/** 授信條件修改內容 **/
	@Size(max=3000)
	@Column(name="CONTENT", length=3000, columnDefinition="VARCHAR(3000)")
	private String content;

	/** 備註 **/
	@Size(max=300)
	@Column(name="REMARK", length=300, columnDefinition="VARCHAR(300)")
	private String remark;

	/** 
	 * 修改角色
	 * AO(AO修改), RV(審查修改)
	 */
	@Size(max=2)
	@Column(name="ROLE", length=2, columnDefinition="CHAR(2)")
	private String role;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 
	 * 取得文件編號<p/>
	 * 簽報書MAINID
	 */
	public String getMainId() {
		return this.mainId;
	}
	/**
	 *  設定文件編號<p/>
	 *  簽報書MAINID
	 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得授信類別<p/>
	 * 貸款額度:quota, 貸款期間:term, 貸款利率: rate, 貸款費用: fare
	 */
	public String getType() {
		return this.type;
	}
	/**
	 *  設定授信類別<p/>
	 *  貸款額度:quota, 貸款期間:term, 貸款利率: rate, 貸款費用: fare
	 **/
	public void setType(String value) {
		this.type = value;
	}

	/** 取得授信條件修改內容 **/
	public String getContent() {
		return this.content;
	}
	/** 設定授信條件修改內容 **/
	public void setContent(String value) {
		this.content = value;
	}

	/** 取得備註 **/
	public String getRemark() {
		return this.remark;
	}
	/** 設定備註 **/
	public void setRemark(String value) {
		this.remark = value;
	}

	/** 
	 * 取得修改角色<p/>
	 * AO(AO修改), RV(審查修改)
	 */
	public String getRole() {
		return this.role;
	}
	/**
	 *  設定修改角色<p/>
	 *  AO(AO修改), RV(審查修改)
	 **/
	public void setRole(String value) {
		this.role = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}
}
