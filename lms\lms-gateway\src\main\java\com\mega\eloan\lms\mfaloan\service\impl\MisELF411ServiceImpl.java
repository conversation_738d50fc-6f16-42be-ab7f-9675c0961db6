package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;

import tw.com.jcs.common.Util;

import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.mfaloan.bean.ELF411;
import com.mega.eloan.lms.mfaloan.service.MisELF411Service;

@Service
public class MisELF411ServiceImpl extends AbstractMFAloanJdbc implements
		MisELF411Service {
	@Override
	public List<ELF411> find_ELF411_NEWADD_notEmpty(String dataym, String branch) {
		return find_ELF411_NEWADD_notEmpty(dataym, branch, "", "");
	}

	@Override
	public List<ELF411> find_ELF411_NEWADD_notEmpty(String dataym,
			String branch, String custId, String dupNo) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForListWithMax(
				"MIS.ELF411.GetByCustBrIdDate",
				new String[] { dataym, branch, custId + "%", dupNo + "%" });
		return toELF411(rowData);
	}

	@Override
	public String getMaxDataYM() {
		Map<String, Object> row = getJdbc().queryForMap(
				"MIS.ELF411.getMaxDataYM", null);
		return Util.trim(MapUtils.getString(row, "ELF411_DATAYM"));
	}

	@Override
	public List<ELF411> getData(String branch, String dataym) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForListWithMax(
				"MIS.ELF411.GetByBrDate", new String[] { branch, dataym });
		return toELF411(rowData);
	}

	@Override
	public boolean isELF411Ready(String dataym) {
		int fetch_size = 1;
		List<Map<String, Object>> rowData = this.getJdbc().queryForList(
				"MIS.ELF411.GetByBrDate", new String[] { "999", dataym }, 0,
				fetch_size);
		return (rowData != null && rowData.size() > 0);
	}

	private List<ELF411> toELF411(List<Map<String, Object>> rowData) {
		List<ELF411> list = new ArrayList<ELF411>();
		for (Map<String, Object> row : rowData) {
			ELF411 model = new ELF411();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}

	@Override
	public List<Map<String, Object>> gfnGenerateCTL_FLMS180R11(String branch,
			String begDATAYM, String endDATAYM) {
		return getJdbc().queryForListWithMax(
				"MIS.ELF411.gfnGenerateCTL_FLMS180R11",
				new String[] { begDATAYM, endDATAYM, branch, branch });
	}

	/**
	 * 取得最小實地覆審基準日
	 * J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
	 * @param brNo
	 * @param contracts
	 * @return
	 */
	@Override
	public List<Map<String, Object>> getMinDataYMByBrNoAndContract(String brNo,
			String[] contracts) {
		List<Object> params = new ArrayList<Object>();
		String contractParam = Util.genSqlParam(contracts);
		params.add(brNo);
		params.addAll(Arrays.asList(contracts));
		return this.getJdbc().queryForAllListByCustParam(
				"MIS.ELF411.getMinDataYMByBrNoAndContract",
				new Object[] { contractParam },
				params.toArray(new Object[0]));

	}
	
	/**
	 * 取得最大實地覆審基準日
	 * J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
	 * @param brNo
	 * @param contracts
	 * @return
	 */
	@Override
	public List<Map<String, Object>> getMaxDataYMByBrNoAndContract(String brNo,
			String[] contracts) {
		List<Object> params = new ArrayList<Object>();
		String contractParam = Util.genSqlParam(contracts);
		params.add(brNo);
		params.addAll(Arrays.asList(contracts));
		return this.getJdbc().queryForAllListByCustParam(
				"MIS.ELF411.getMaxDataYMByBrNoAndContract",
				new Object[] { contractParam },
				params.toArray(new Object[0]));

	}

}
