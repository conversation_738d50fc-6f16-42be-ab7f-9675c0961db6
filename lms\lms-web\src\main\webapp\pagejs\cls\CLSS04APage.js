/**
 *引進案由企個金共用元件js 
 */
var initS04aJson = {
	handlerName : null,
	// 設定handler名稱
	setHandler : function(){
		if(responseJSON.docURL == "/lms/lms1201m01"){
			// 授權外企金
			this.handlerName = "lms1201formhandler";
		}else if(responseJSON.docURL == "/lms/lms1101m01"){
			// 授權內企金
			this.handlerName = "lms1101formhandler";
		}else if(responseJSON.docURL == "/lms/lms1211m01"){
			// 授權外個金
			this.handlerName = "lms1211formhandler";
		}else if(responseJSON.docURL == "/lms/lms1111m01"){
			this.handlerName = "lms1111formhandler";
		}else{
			this.handlerName = "lms1301formhandler";
		}		
	}
};

$(document).ready(function() {
	initS04aJson.setHandler();	
	$.form.init({
        formHandler: initS04aJson.handlerName,
        formPostData:{
        	formAction : "queryLms1205m01",
        	oid : responseJSON.oid,
        	page : responseJSON.page,
        	mainId : responseJSON.mainId,
        	docType : responseJSON.docType,
        	docCode : responseJSON.docCode,
        	docKind : responseJSON.docKind,
			docStatus : responseJSON.mainDocStatus,
			areaDocstatus : responseJSON.areaDocstatus,
			txCode : responseJSON.txCode,
			itemDscr03 : "",
			itemDscr05 : "",
			ffbody : ""			
        },
		loadSuccess:function(jsonInit){
			$("#LMS1205S04Form").find("#gist").val(jsonInit.showBorrowData.gist);
			if(responseJSON.page != "01"){
				var $showBorrowData = $("#showBorrowData");
				$showBorrowData.reset();
				$showBorrowData.setData(jsonInit.showBorrowData,false);	
			}
		}
    });
});

/**
 * 引進案由
 */
function getCase(){
	$.ajax({		//查詢主要借款人資料
		handler : initS04aJson.handlerName,
		type : "POST",
		dataType : "json",
		action : "getCase",
		data : {
			mainId : responseJSON.mainId
		},		
		success : function(json) {
			$("#LMS1205S04Form").setData(json);
		}
	});	
}