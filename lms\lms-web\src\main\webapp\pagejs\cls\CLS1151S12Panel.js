var dfd31 = new $.Deferred(), dfd32 = new $.Deferred(), dfd33 = new $.Deferred(), dfd7 = new $.Deferred();
$(document).ready(function(){
    var inits = {};
    inits['fhandle'] = _M.fhandle;
    inits['ghandle'] = _M.ghandle;
    inits['toreadOnly'] = _M.isReadOnly;
    
    dfd31.done(gridviewitemChildren);//科子目限額
    dfd32.done(gridviewitemChildren2);
    
    dfd7.done(queryItemSelect);//查詢科子目項目
    //====================button event ================================
    //新增科子目
    $("#newItemChildren1Bt").click(function(){
        newItemChildren1(null, null, null, 'isNew');
    });
    
    //新增科子目合併限額
    $("#newItemChildren2Bt").click(function(){
        newItemChildren2();
    });
    
    //變更科子目限額選擇的顏色
    $("[name=subject2]").live("click", function(){
        var $this = $(this);
        if ($this.attr("checked")) {
            $this.closest("td").css("background", "#C0C0C0");
        }
        else {
            $this.closest("td").css("background", "#FFFFFF");
        }
    });
    
    //當授信科科目內詳其他續做條件被點選就隱藏天數的select
    $("#lmtOther").click(function(){
        $(this).attr("checked") ? $("#hidelimit").hide() : $("#hidelimit").show();
    });
    
    //刪除科子目
    $("#removeGridviewitemChildren").click(function(){
        var gridID = $("#gridviewitemChildren").getGridParam('selarrrow');
        if (gridID == "") {
            //TMMDeleteError=請先選擇需修改(刪除)之資料列
            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
            
        }
        //confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                var gridIDList = [];
                for (var i = 0; i < gridID.length; i++) {
                    gridIDList[i] = $("#gridviewitemChildren").getRowData(gridID[i]).oid;
                }
                $.ajax({
                    handler: inits.fhandle,
                    data: {
                        formAction: "deleteC900M01F",
                        tabFormMainId: _M.tabMainId,
                        Idlist: gridIDList,
                        mode: "1"
                    },
                    success: function(obj){
                    
                        if (obj && obj.drc) {
                            $("#itemDscr1").val(obj.drc);
                        }
                        $("#gridviewitemChildren").trigger("reloadGrid");
                    }
                });
            }
        });
    });
    
    //刪除科子目合併
    $("#removeGridviewitemChildren2").click(function(){
    
        var gridID = $("#gridviewitemChildren2").getGridParam('selarrrow');
        if (gridID == "") {
            //TMMDeleteError=請先選擇需修改(刪除)之資料列
            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
        }
        
        //confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                var gridIDList = [];
                for (var i = 0; i < gridID.length; i++) {
                    gridIDList[i] = $("#gridviewitemChildren2").getRowData(gridID[i]).oid;
                }
                $.ajax({
                    handler: inits.fhandle,
                    data: {
                        formAction: "deleteC900M01F",
                        tabFormMainId: _M.tabMainId,
                        Idlist: gridIDList,
                        mode: "1"
                    },
                    success: function(obj){
                        if (obj && obj.drc) {
                            $("#itemDscr1").val(obj.drc);
                        }
                        $("#gridviewitemChildren2").trigger("reloadGrid");
                    }
                });
            }
        });
    });
    
    $("#cls1151s12").click(function(){
        dfd31.resolve();
        dfd7.resolve();
    });
    $("#tab12_2,#tab12_4").find("a").click(function(){
        var $thisId = $(this).parent("li").attr("id");
        switch ($thisId) {
            //科子目頁籤
            case "tab12_2":
                dfd32.resolve();
                $("#gridviewitemChildren2").jqGrid("setGridParam", {//當文件載完再將科子目的grid更新
                    sortname: 'createTime',
                    sortorder: 'asc',
                    postData: {
                        formAction: "queryC900M01F",
                        tabFormMainId: _M.tabMainId,
                        lmtType: "2"
                    },
                    search: true
                }).trigger("reloadGrid");
                break;
            case "tab12_4":
                break;
        }
    });
    
    
    $("#limitWordAll").click(function(){
        //組成字串從sql 裡面撈
        $("#itemDscr1").html("");
        $.ajax({
            handler: inits.fhandle,
            data: {
                formAction: "saveDscr1",
                tabFormMainId: _M.tabMainId,
                type: "1",
                pageNum: $("#pageNum1").val(),
                mode: "1"
            },
            success: function(obj){
                $("#itemDscr1").val(obj.drc);
            }//close success
        });//close ajax
    });
    
    //驗證是不是數字   
    function isNumber(val){
        return /\d/.test(val);
    }
    //====================button event End================================
    
    //====================Grid Code ======================================
    
    /**  科子目限額grid  */
    function gridviewitemChildren(){
        $("#gridviewitemChildren").iGrid({
            handler: inits.ghandle,
            height: 170,
            rownumbers: true,
            multiselect: true,
            hideMultiselect: false,
            sortname: 'subjSeq|createTime',
            sortorder: 'asc|asc',
            rowNum: 10,
            postData: {
                formAction: "queryC900M01F",
                tabFormMainId: _M.tabMainId,
                lmtType: "1"
            },
            autowidth: true,
            colModel: [{
                colHeader: i18n.cls1151s01["C900M01F.loanTP"],//科目
                name: 'subjectDesc',
                align: "left",
                width: 180,
                type: "1",
                formatter: 'click',
                onclick: newItemChildren1, sortable:false //於C900M01F 無欄位 subjectDesc
            }, {
                colHeader: i18n.cls1151s01["C900M01F.applyCurr"],//"幣別",
                name: 'lmtCurr',
                width: 40,
                align: "center"
            }, {
                colHeader: i18n.cls1151s01["C900M01F.lmtMoney"],//"額度限額",
                width: 100,
                name: 'lmtAmt',
                align: "right",
                formatter: 'currency',
                formatoptions: {
                    thousandsSeparator: ",",
                    removeTrailingZero: true,
                    decimalPlaces: 2//小數點到第幾位
                }
            }, {
                colHeader: i18n.cls1151s01["C900M01F.subjDscr"],//"科目補充說明",
                name: 'subjDscr',
                width: 80,
                align: "center"
            }, {
                colHeader: i18n.cls1151s01["C900M01F.lmtDays"],//"清償期限",
                name: 'lmtDays',
                width: 70,
                align: "center",
                formatter: itemFormatter2
            }, {
                name: 'oid',
                hidden: true
            }, {
                name: 'subject',
                hidden: true
            }, {
                name: 'lmtOther',
                hidden: true
            }],
            ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
                var data = $("#gridviewitemChildren").getRowData(rowid);
                newItemChildren1(null, null, data);
            }
        });
    }// close gridviewitemChildren fn(x)
    //當其他續做條件被勾選了
    function itemFormatter2(cellvalue, otions, rowObject){
        var itemName = '';
        if (rowObject[7] == '1') {
            //C900M01F.lmtOther2=詳產品資訊  
            itemName = i18n.cls1151s01['C900M01F.lmtOther2'];
        }
        else {
            itemName = cellvalue;
        }
        return itemName;
    }
    /**  科子目合併限額grid  */
    function gridviewitemChildren2(){
        $("#gridviewitemChildren2").iGrid({
            handler: inits.ghandle,
            height: 170,
            rowNum: 10,
            rownumbers: true,
            multiselect: true,
            hideMultiselect: false,
            sortname: 'createTime',
            sortorder: 'asc',
            postData: {
                formAction: "queryC900M01F",
                tabFormMainId: _M.tabMainId,
                lmtType: "2"
            },
            autowidth: true,
            colModel: [{
                colHeader: i18n.cls1151s01["C900M01F.loanTP"],//"科目"
                name: 'subject',
                align: "left",
                width: 350,
                sortable: true,
                type: "2",
                //在LMS1405s02panel02.js的格式化function
                formatter: "click",
                onclick: newItemChildren2
            
            }, {
                colHeader: i18n.cls1151s01["C900M01F.applyCurr"],//幣別,
                name: 'lmtCurr',
                align: "center",
                width: 40,
                sortable: true
            }, {
                colHeader: i18n.cls1151s01["C900M01F.lmtMoney"],//"額度限額",
                width: 100,
                name: 'lmtAmt',
                align: "right",
                sortable: true,
                formatter: 'currency',
                formatoptions: {
                    thousandsSeparator: ",",
                    removeTrailingZero: true,
                    decimalPlaces: 2//小數點到第幾位
                }
            }, {
                name: 'oid',
                hidden: true
            }],
            ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
                var data = $("#gridviewitemChildren2").getRowData(rowid);
                newItemChildren2(null, null, data);
            }
        });
    }//close gridviewitemChildren2
    //====================Grid Code End======================================
    
    //====================thickbox Code======================================
    function openNewItemChildrenBox1(){
		
		var btnArr = {};
		if(_M.isReadOnly){
			
		}else{
		btnArr["saveData"] = function(){
            if (!$("#C900M01FForm1").valid()) {
                return false;
            }
            if ($("#subject1").val() == "") {
                //C900M01F.loanTP = 科目  C900M01F.error07=請選擇
                return CommonAPI.showMessage(i18n.cls1151s01["C900M01F.error07"] + i18n.cls1151s01["C900M01F.loanTP"]);
            }
            
            if ($("#lmtCurr1").val() == "") {
                //C900M01F.applyCurr=幣別  C900M01F.error07=請選擇
                return CommonAPI.showMessage(i18n.cls1151s01["C900M01F.error07"] + i18n.cls1151s01["C900M01F.applyCurr"]);
            }
            
            var tSubject = $("#subject1").val();
            if (tSubject == "102" || tSubject == "104" || tSubject == "202" || tSubject == "204" || tSubject == "404") {
                if (parseInt($("#lmtAmt1").val(), 10) == 0) {
                    //C900M01F.error08=額度限額不得為
                    return CommonAPI.showMessage(i18n.cls1151s01["C900M01F.error08"] + "0");
                }
            } else if (tSubject == "205") {
				// C900M01F.error06=短期存單(綜存)擔保放款額度，非屬授信設定項目。
				return CommonAPI.showMessage(i18n.cls1151s01["C900M01F.error06"]);
			} else if (tSubject == "211" || tSubject.substring(0, 1) == "7" || tSubject.substring(0, 1) == "8") {
				// C900M01F.error03=請注意!!所選的科子目{0}非屬個金業務所搭配的科目，請再次確認是否無誤！
                    alert(i18n.cls1151s01["C900M01F.error03"].replace("{0}", tSubject));
                }
//                    FormAction.open = true;
            //console.log("BBB:" + _M.tabMainId);                    
            $.ajax({
                handler: inits.fhandle,
                data: {
                    formAction: "saveC900M01F",
                    lmtType: "1",
                    tabFormMainId: _M.tabMainId,
                    lmtSeq: $("#lmtSeq1").val(),
                    itemList: $("#subject1").val()  
                },
                success: function(obj){
                    if (obj && obj.drc) {
                        $("#itemDscr1").val(obj.drc);
                    }
//                            FormAction.open = false;
                    $.thickbox.close();
                    $("#gridviewitemChildren").trigger("reloadGrid");
                }
            });
        };	
		}
		
         btnArr["close"] = function(){
              $.thickbox.close();
         };	
		
        $("#newItemChildrenBox1").thickbox({
            // 科子目
            //page12.001=科子目限額
            title: i18n.cls1151s01["page12.001"],
            width: 550,
            height: 300,
            readOnly: ((_openerLockDoc == "1" || inits.toreadOnly) || _M.isReadOnly),
            i18n: i18n.def,
            modal: true,
            open: function(){
				var lmtCurr1 = $("#lmtCurr1").val();
				if (lmtCurr1 == ""){
					$("#lmtCurr1").val("TWD");
				}
            },
            buttons: btnArr
        });
    }
    function newItemChildren1(cellvalue, type, data, isNew){
        $("#C900M01FForm1").reset();
		$("#lmtOther").triggerHandler("click")
        if (isNew == 'isNew') {
            $.ajax({
                handler: inits.fhandle,
                data: {
                    formAction: "querySubjCodeSelect",
                    oid: data && data.oid,
                    tabFormMainId: _M.tabMainId,
                    tMode: "new"
                },
                success: function(json){
                    $("#subject1").attr("disabled", false);
                    $("#subject1").empty();
                    $.each(json.mapOrder, function(idx, k){
                        var currobj = {};
                        currobj[k] = json.map[k];
                        $("#subject1").setItems({
                            item: currobj,
                            format: "{value} - {key}",
                            clear: false,
                            space: false
                        });
                    });
                    $("#C900M01FForm1").injectData(json);
                    $("#lmtAmt1").val(json.lmtAmt);
                    $("#lmtCurr1").val(json.lmtCurr);
                    $("#subject1").val(json.subject);
                    $("#lmtSeq1").val(json.lmtSeq);
                    openNewItemChildrenBox1();
                }
            });
        }
        else {
            $.ajax({
                handler: inits.fhandle,
                data: {
                    formAction: "querySubjCodeSelect",
                    oid: data && data.oid,
                    tabFormMainId: _M.tabMainId,
                    tMode: "old"
                },
                success: function(json){
                    $("#subject1").empty();
                    $.each(json.mapOrder, function(idx, k){
                        var currobj = {};
                        currobj[k] = json.map[k];
                        $("#subject1").setItems({
                            item: currobj,
                            format: "{value} - {key}",
                            clear: false,
                            space: false
                        });
                    });                
                    
                    $.ajax({
                        handler: inits.fhandle,
                        data: {
                            formAction: "queryC900M01F",
                            oid: data && data.oid,
                            tabFormMainId: _M.tabMainId
                        },
                        success: function(json){
                            $("#C900M01FForm1").injectData(json);
                            $("#lmtAmt1").val(json.lmtAmt);
                            $("#lmtCurr1").val(json.lmtCurr);
                            $("#subject1").val(json.subject);
                            $("#lmtSeq1").val(json.lmtSeq);							
                            $("#subject1").attr("disabled", true);
                            openNewItemChildrenBox1();
                        }
                    });
                }
            });
        }
    }
    function openNewItemChildrenBox2(){
		
		var btnArr2 = {};
		if(_M.isReadOnly){
			
		}else{
		btnArr2["saveData"] = function(){
          if (!$("#C900M01FForm2").valid()) {
              return false;
          }
          if ($("#lmtAmt2").val() == 0) {
              $("#lmtAmt2").val("");                  
                return CommonAPI.showMessage(i18n.cls1151s01["C900M01F.error08"] + "0"); //C900M01F.error08=額度限額不得為
          }                    
          if ($("#lmtCurr2").val() == "") {                        
                return CommonAPI.showMessage(i18n.cls1151s01["C900M01F.error07"] + i18n.cls1151s01["C900M01F.applyCurr"]); //C900M01F.applyCurr=幣別
          }
          var $item = $("[name=subject2]").filter(":checked");
          if ($item.length == 0) {
                return CommonAPI.showMessage(i18n.cls1151s01["C900M01F.error07"]);
          } else if ($item.length == 1) {
                return CommonAPI.showMessage(i18n.cls1151s01["C900M01F.error11"]); // C900M01F.error11=合併限額作業至少要勾選一筆以上！
          }

          var itemList = $item.map(function(){
                return $(this).val()
          }).toArray();
//                    FormAction.open = true;
                    $.ajax({
                        handler: inits.fhandle,
                        data: {
                            formAction: "saveC900M01F",
                            lmtType: "2",
                            tabFormMainId: _M.tabMainId,
                            lmtSeq: $("#lmtSeq2").val(),
                            subitem: $("#subject2").val(),
                            itemList: itemList.join("|")    
                        },
                        success: function(obj){
                            if (obj && obj.drc) {
                                $("#itemDscr1").val(obj.drc);
                            }
//                            FormAction.open = false;
                            $.thickbox.close();
                            $("#gridviewitemChildren2").trigger("reloadGrid");
                        }
                    });
        };	
		}		
         btnArr2["close"] = function(){
              $.thickbox.close();
         };	
		
		
        $("#newItemChildrenBox2").thickbox({ // 科子目合併
            title: i18n.cls1151s01["page12.002"],
            width: 580,
            height: 400,
            readOnly: ((_openerLockDoc == "1" || inits.toreadOnly) || _M.isReadOnly),
            i18n: i18n.def,
            modal: true,
            open: function(){				
				var lmtCurr2 = $("#lmtCurr2").val();
				if (lmtCurr2 == ""){
					$("#lmtCurr2").val("TWD");
				}
            },
           buttons: btnArr2
        });
    }
    function newItemChildren2(cellvalue, type, data){
        var gridData = $('#gridviewitemChildren').jqGrid('getRowData');
        
		if (gridData == "") {            
            return CommonAPI.showMessage(i18n.cls1151s01["C900M01F.error02"]); // C900M01F.error02=科子目額度限額尚未登錄任何一筆資料
        }
		var items = {};
        $.each(gridData, function(index, data){
            items[data["subject"]] = data["subjectDesc"];
        });		
		
        $("#subject2").setItems({
            size: 1,
            item: items
        });
        //		$('#gridviewitemChildren').jqGrid('getGridParam', 'records')
					if (gridData.length ==1) {						
            			return CommonAPI.showMessage(i18n.cls1151s01["C900M01F.error05"]); // C900M01F.error05=科子目額度限額只有一筆資料無需設定合併限額
					}
        
        //初始化
        $("#C900M01FForm2").reset();
        $("[name=subject2]").closest("td").css("background", "#FFFFFF");
        
        if (!$.isEmptyObject(data)) {
            $.ajax({
                handler: inits.fhandle,
                data: {
                    formAction: "queryC900M01F",
                    oid: data && data.oid,
                    noOpenDoc: true
                },
                success: function(obj){
                    $("#C900M01FForm2").injectData(obj);
                    var list = obj.subject.split("|");					
                    for (var i = 0; i < list.length; i++) {//轉換科目代碼將值帶入
                        $("[name=subject2][value=" + list[i] + "]").attr("checked", true).trigger('click').attr("checked", true);
                    }//close for
                    $("#lmtAmt2").val(obj.lmtAmt);
                    $("#lmtCurr2").val(obj.lmtCurr);
                    $("#lmtSeq2").val(obj.lmtSeq);
                    
                    openNewItemChildrenBox2();
                }
            });
        }
        else {
            openNewItemChildrenBox2();
        }
    }
    //查詢科子目項目
    function queryItemSelect(){
        $.ajax({
            handler: inits.fhandle,
            data: {
                formAction: "querySubjCodeSelect"
            },
            success: function(json){
                // console.log("AAA:" + json.mapOrder);
                $.each(json.mapOrder, function(idx, k){
                    var currobj = {};
                    currobj[k] = json.map[k];
                    
                    //select
                    //$("#subject2").setItems({ item: currobj, format: "{value} {key}", clear:false, space: false });
                    
                    $("#subject2").setItems({
                        size: 1,
                        item: json.map02
                    });
                });
                
            }
        });
    }
});

//向上或向下移動
function upDownBox(upDown){
    //$("input[name='upDown']").removeAttr("checked");
    var rows = $("#gridviewitemChildren").getGridParam('selarrrow');
    if (!rows || rows == "") {
        return CommonAPI.showMessage(i18n.def["grid.selrow"])
    }
    
    if (rows.length > 1) {
        return CommonAPI.showMessage(i18n.def["grid.selrow"])
    }
    
    var list = "";
    var data;
    if (rows != 'undefined' && rows != null) {
        data = $("#gridviewitemChildren").getRowData(rows);
        list = data.oid;
    }
    upOrDownF(data, upDown);
}

function upOrDownF(data, upDown){
    $.ajax({
        type: "POST",
        handler: "cls1151m01formhandler",
        data: {
            formAction: "changeDeSeqNum",
            detailOid: data.oid,
            tabFormMainId: _M.tabMainId,
            kind: data.KINDHIDE,
            upOrDown: upDown
        },
        success: function(json){        
            $("#gridviewitemChildren").trigger("reloadGrid");//.setSelection(1,true);
            //調整科目順序後要重新組利費率的字 
            $.ajax({
                handler: "cls1151m01formhandler",
                data: {//把資料轉成json
                    formAction: "saveDscr1",//儲存所有利費率描述
                    tabFormMainId: _M.tabMainId,
                    pageNum: $("#pageNum1").val(),
					type: "1",
                	mode: "1"
                },
                success: function(json){
                    $("#itemDscr1").val(json.drc);                    
                }
            }); //close ajax 
        }
    });
}
