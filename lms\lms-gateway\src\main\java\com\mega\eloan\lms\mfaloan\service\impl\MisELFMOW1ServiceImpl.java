package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.mfaloan.service.MisELFMOW1Service;

@Service
public class MisELFMOW1ServiceImpl extends AbstractMF<PERSON>loanJdbc implements
	MisELFMOW1Service {

	@Override
	public List<Map<String,Object>> findelfmow1ByCustIdYMoney(String custId, String dupNo){

		return this.getJdbc().queryForList(
				"ELFMOW1.selByCustidYMoney",
				new Object[] { custId, dupNo});
	}

	@Override
	public List<Map<String,Object>> findelfmow1ByCustIdNMoney(String custId, String dupNo){

		return this.getJdbc().queryForList(
				"ELFMOW1.selByCustidNMoney",
				new Object[] { custId, dupNo});
	}
	
	@Override
	public Map<String, Object> findLrs1(String custId, String dupNo){
		return this.getJdbc().queryForMap(
				"ELFMOW1.selByLrs1",
				new String[] { custId, dupNo});
	}
	
	@Override
	public Map<String, Object> findLrs2(String custId, String dupNo){
		return this.getJdbc().queryForMap(
				"ELFMOW1.selByLrs2",
				new String[] { custId, dupNo});
	}
	
}
