#-----------------------
# HIBERNATE PROPERTIES
#-----------------------

hibernate.connection.useUnicode=true
hibernate.connection.charSet=UTF-8

#-- 2d level cache ---------------
hibernate.cache.use_query_cache==true

#-- logging debug information --
hibernate.show_sql=false
hibernate.format_sql=true
hibernate.generate_statistics=false
hibernate.use_sql_comments=false

#-- misc --
hibernate.bytecode.use_reflection_optimizer=true
hibernate.jdbc.use_scrollable_resultset=false
hibernate.jdbc.use_streams_for_binary=true
hibernate.jdbc.fetch_size=300

#-----------------------
# update,create,create-drop,validate
#-----------------------
hibernate.hbm2ddl.auto=none

hibernate.hql.bulk_id_strategy=org.hibernate.hql.spi.id.inline.InlineIdsOrClauseBulkIdStrategy
hibernate.temp.use_jdbc_metadata_defaults=false
spring.jpa.properties.hibernate.hql.bulk_id_strategy=org.hibernate.hql.spi.id.inline.InlineIdsOrClauseBulkIdStrategy
spring.jpa.properties.hibernate.temp.use_jdbc_metadata_defaults=false

# [refs #94] for dev c3p0 connection pool usage
hibernate.c3p0.debugUnreturnedConnectionStackTraces=true