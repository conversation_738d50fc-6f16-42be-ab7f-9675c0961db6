/* 
 *  LMS9530ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.dao.DocFileDao;
import com.mega.eloan.common.exception.GWException;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.lms.base.common.BranchRate;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dao.L120S01ADao;
import com.mega.eloan.lms.dao.L120S01CDao;
import com.mega.eloan.lms.dao.L120S04ADao;
import com.mega.eloan.lms.dao.L120S04BDao;
import com.mega.eloan.lms.dao.L120S04CDao;
import com.mega.eloan.lms.dao.L120S04EDao;
import com.mega.eloan.lms.dao.L260M01DDao;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.lms.panels.LMSS07Panel;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.mfaloan.service.MisElcrcoService;
import com.mega.eloan.lms.mfaloan.service.MisGrpcmpService;
import com.mega.eloan.lms.mfaloan.service.MisGrpdtlService;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120S01A;
import com.mega.eloan.lms.model.L120S01C;
import com.mega.eloan.lms.model.L120S04A;
import com.mega.eloan.lms.model.L120S04B;
import com.mega.eloan.lms.model.L120S04C;
import com.mega.eloan.lms.model.L120S04E;
import com.mega.eloan.lms.model.L260M01D;
import com.mega.eloan.lms.obsdb.service.MisELF001Service;
import com.mega.eloan.lms.rpt.panels.LMSS07APanel;
import com.mega.eloan.lms.rpt.service.LMS9535Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapMath;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.Util;

@Service
public class LMS9535ServiceImpl extends AbstractCapService implements
		LMS9535Service {

	protected final Logger logger = LoggerFactory.getLogger(getClass());
	@Resource
	LMSService lmsService;
	@Resource
	L120M01ADao l120m01aDao;
	@Resource
	L120S01ADao l120s01aDao;
	@Resource
	L120S04ADao l120s04aDao;
	@Resource
	L120S04BDao l120s04bDao;
	@Resource
	L120S04CDao l120s04cDao;
    @Resource
    L120S04EDao l120s04eDao;
	@Resource
	L120S01CDao l120s01cDao;
	
	// J-112-0307
	// 於eloan貸後管理追蹤檢核表維護功能中，新增一鍵生成公司訪問紀錄表及借戶、負責人、所屬企業集團等關係戶於本行各項業務往來彙總表。
	@Resource
	L260M01DDao l260m01dDao;
	
	@Resource
	DocFileDao docFileDao;
	@Resource
	DwdbBASEService dwdbService;
	@Resource
	MisCustdataService misCustdataService;
	@Resource
	MisGrpcmpService misGrpcmpService;
	@Resource
	MisGrpdtlService misGrpdtlService;
	@Resource
	MisElcrcoService misElcrcoService;
	@Resource
	TempDataService tempDataService;
	@Resource
	MisELF001Service misELF003Service;

	// 關係戶於本行各項業務往來檔
	@Override
	public L120S04A findL120s04aByOid(String oid) {
		return l120s04aDao.findByOid(oid);
	}

	@Override
	public L120S04A findL120s04aByUniqueKey(String mainId, String custId,
			String dupNo, String custName) {
		return l120s04aDao.findByUniqueKey(mainId, custId, dupNo, custName);
	}

	@Override
	public List<L120S04A> findL120s04aByMainId(String mainId) {
		return l120s04aDao.findByMainId(mainId);
	}

	@Override
	public List<L120S04A> findL120s04aByMainIdPrtFlag(String mainId,
			String prtFlag) {
		// return l120s04aDao.findByMainIdPrtFlag(mainId, prtFlag);

		List<L120S04A> tempOrg = l120s04aDao.findByMainIdPrtFlag(mainId,
				prtFlag);
		List<L120S04A> tempNew = new ArrayList<L120S04A>(tempOrg);

		if (tempNew != null && !tempNew.isEmpty()) {

			Collections.sort(tempNew, new Comparator<L120S04A>() {

				@Override
				public int compare(L120S04A object1, L120S04A object2) {
					// TODO Auto-generated method stub
					int cr = 0;
					String[] resStr1 = Util.trim(object1.getCustRelation())
							.split(",");
					Arrays.sort(resStr1);
					String[] resStr2 = Util.trim(object2.getCustRelation())
							.split(",");
					Arrays.sort(resStr2);

					int a = resStr2[0].compareTo(resStr1[0]);

					if (a != 0) {
						cr = (a > 0) ? -2 : 4;
					} else {
						long b = object2.getProfit() - object1.getProfit();
						if (b != 0) {
							cr = (b > 0) ? 3 : -3;
						} else {
							int c = object2.getCustId().compareTo(
									object1.getCustId());
							if (c != 0) {
								cr = (c > 0) ? -4 : 2;
							} else {
								// String oid1 = object1.getOid();
								// String oid2 = object2.getOid();
								// int oidFlag = oid2.compareTo(oid2);
								// if(oidFlag != 0){
								// cr = (oidFlag > 0)? -5:1;
								// }
							}
						}
					}

					return cr;
				}
			});
		}
		return tempNew;
	}

	@Override
	public void saveL120s04aList(List<L120S04A> list) {
		if (!list.isEmpty()) {
			l120s04aDao.save(list);
			// for (L120S04A model : list) {
			// // 記錄文件異動記錄
			// docLogService.record(model.getOid(), DocLogEnum.SAVE);
			// }
		}
	}

	@Override
	public void deleteListL120s04a(List<L120S04A> list) {
		List<String> listOid = new ArrayList<String>();
		for (L120S04A model : list) {
			listOid.add(model.getOid());
		}
		l120s04aDao.delete(list);
		// for (String oid : listOid) {
		// // 記錄文件異動記錄
		// docLogService.record(oid, DocLogEnum.DELETE);
		// }
	}

	@Override
	public L120S04B findL120s04bByOid(String oid) {
		return l120s04bDao.findByOid(oid);
	}

	@Override
	public List<L120S04B> findL120s04bByMainId(String mainId) {
		return l120s04bDao.findByMainId(mainId);
	}

	@Override
	public void saveL120s04BList(List<L120S04B> list) {
		if (!list.isEmpty()) {
			l120s04bDao.save(list);
		}
	}

	@Override
	public void deleteListL120s04b(List<L120S04B> list) {
		List<String> listOid = new ArrayList<String>();
		for (L120S04B model : list) {
			listOid.add(model.getOid());
		}
		l120s04bDao.delete(list);
	}

	@Override
	public L120S04C findL120s04cByOid(String oid) {
		return l120s04cDao.findByOid(oid);
	}

	@Override
	public L120S04E findL120s04eByOid(String oid) {
		return l120s04eDao.findByOid(oid);
	}

	@Override
	public List<L120S04C> findL120s04cByMainId(String mainId) {
		return l120s04cDao.findByMainId(mainId);
	}

    @Override
    public List<L120S04E> findL120s04eByMainId(String mainId) {
        return l120s04eDao.findByMainId(mainId);
    }

	@Override
	public void saveL120s04cList(List<L120S04C> list) {
		if (!list.isEmpty()) {
			l120s04cDao.save(list);
		}
	}

	@Override
	public void saveL120s04eList(List<L120S04E> list) {
		if (!list.isEmpty()) {
			l120s04eDao.save(list);
		}
	}

	@Override
	public void deleteAllWanLai(List<L120S04A> list1, List<L120S04B> list2,
			List<L120S04C> list3, List<DocFile> docFiles) {
		if (list1 != null & !list1.isEmpty()) {
			l120s04aDao.delete(list1);
		}
		if (list2 != null & !list2.isEmpty()) {
			l120s04bDao.delete(list2);
		}
		if (list3 != null & !list3.isEmpty()) {
			l120s04cDao.delete(list3);
		}
		if (docFiles != null & !docFiles.isEmpty()) {
			docFileDao.delete(docFiles);
		}
	}

	@Override
	public void saveL120s04bc(List<L120S04C> list, L120S04B model) {
		this.saveL120s04cList(list);
		this.save(model);
	}

	@Override
	public void deleteListL120s04c(List<L120S04C> list) {
		List<String> listOid = new ArrayList<String>();
		for (L120S04C model : list) {
			listOid.add(model.getOid());
		}
		l120s04cDao.delete(list);
	}

    @Override
    public void deleteListL120s04e(List<L120S04E> list) {
        List<String> listOid = new ArrayList<String>();
        for (L120S04E model : list) {
            listOid.add(model.getOid());
        }
        l120s04eDao.delete(list);
    }

	@Override
	public void deleteListL120s04(List<L120S04B> listS04b,
			List<L120S04C> listS04c, List<L120S04E> listS04e) {
		if (!listS04b.isEmpty()) {
			this.deleteListL120s04b(listS04b);
		}
		if (!listS04c.isEmpty()) {
			this.deleteListL120s04c(listS04c);
		}
        if (!listS04e.isEmpty()) {
            this.deleteListL120s04e(listS04e);
        }
	}

	@SuppressWarnings("unused")
	@Override
	public void importL120s04b(String mainId, String custId,
			String dupNo, String queryDateS, String queryDateE)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		queryDateS = (queryDateS.length() == 7) ? queryDateS + "-01"
				: queryDateS;
		queryDateE = (queryDateE.length() == 7) ? queryDateE + "-01"
				: queryDateE;
		// CapAjaxFormResult result = new CapAjaxFormResult();
		// L120M01A meta = l120m01aDao.findByMainId(mainId);
		BranchRate branchRate = lmsService.getBranchRate(user.getUnitNo());
		List<L120S04A> listL120s04a = this.findL120s04aByMainId(mainId);
		// L120S04B l120s04b = new L120S04B();
		JSONObject json = new JSONObject();
		JSONObject jsonData = new JSONObject();
		// J-112-0078 配合企金處，修改「借戶暨關係戶與本行往來實績彙總表」中，增列各業務別利潤貢獻度欄位等。
		JSONObject jsonS04e = new JSONObject();
		Date dQueryDateS = CapDate.getDate(queryDateS,
				UtilConstants.DateFormat.YYYY_MM_DD);
		Date dQueryDateE = CapDate.getDate(queryDateE,
				UtilConstants.DateFormat.YYYY_MM_DD);
		String MAX_CYC_MN = null;
		String MIN_CYC_MN = null;
		// 管理報表往來彙總查詢改成使用者自行輸入custId與dupNo
		String mCustId = Util.trim(custId);
		String mDupNo = Util.trim(dupNo);
		String _mDupNo = "0".equals(Util.trim(dupNo)) ? "" : Util.trim(dupNo);
		String tGPID = null;
		String tGPName = null;
		String tID = null;
		String tDUPNO = null;
		String tCUSTNAME = null;
		String tGRADE = null;
		String end3Mon = null;
		double bal = 0;
		double balSalary = 0;
		double balTrustFdta = 0;

		if (listL120s04a.isEmpty()) {
			// L1205S07.error14 = 必須先執行【引進各關係戶往來彙總】才能執行本功能！
			Properties pop = MessageBundleScriptCreator
					.getComponentResource(LMSS07Panel.class);
			throw new CapMessageException(RespMsgHelper.getMessage(
					"EFD0015", pop.getProperty("L1205S07.error14")), getClass());
		} else {
			// 將除了集團、與關係企業計算合計以外的借款人資料記錄下來
			// a Method
			for (L120S04A model : listL120s04a) {
				String custRel = Util.trim(model.getCustRelation());
				if (!custRel.contains("3") && !custRel.contains("4")) {
					String theDupNo = Util.trim(model.getDupNo());
					StringBuilder sbFullId = new StringBuilder();
					sbFullId.append(Util.trim(model.getCustId())).append(
							Util.trim(theDupNo).equals("0") ? " " : Util
									.trim(theDupNo));
					json.put(sbFullId.toString(),
							Util.trim(model.getCustName()));
				}
			}
			// a Method

			Date max_cyc_mn = dwdbService.getMD_CUPFM_OTS_max_cyc_mn();
			if (max_cyc_mn == null) {
				// 資料倉儲無最近三個月("+Left(c_bgn_date,7)+"～"+Left(c_end_date,7) +")之資料
				Properties pop = MessageBundleScriptCreator
						.getComponentResource(LMSS07Panel.class);
				throw new CapMessageException(RespMsgHelper.getMessage(
						"EFD0015", MessageFormat.format(
								pop.getProperty("L1205S07.error20"),
								CapDate.formatDate(dQueryDateS,
										UtilConstants.DateFormat.YYYY_MM_DD)
										.substring(7),
								CapDate.formatDate(dQueryDateE,
										UtilConstants.DateFormat.YYYY_MM_DD)
										.substring(7))), getClass());
			}

			MAX_CYC_MN = CapDate.formatDate(max_cyc_mn,
					UtilConstants.DateFormat.YYYY_MM_DD);
			MIN_CYC_MN = Util
					.trim(Util.parseInt(MAX_CYC_MN.substring(0, 4)) - 2)
					+ "-01-01";
			try {
				List<Map<String, Object>> listGrpMap = misGrpcmpService
						.findGrpcmpSelGrpdtl(mCustId, mDupNo);
				for (Map<String, Object> map : listGrpMap) {
					// 隸屬集團代號
					tGPID = Util.trim(map.get("GRPID"));
					jsonData.put("GpID", tGPID);
					// l120s04b.setGrpNo(tGPID);
					// 隸屬集團名稱
					tGPName = Util.trim(map.get("GRPNM"));
					jsonData.put("GpName", tGPName);
					// l120s04b.setGrpName(tGPName);
					break;
				}
			} catch (GWException e) {
				// 引進借款人"+rptDoc.borrower_id(0)+"
				// " + rptDoc.borrower(0)+"集團資訊錯誤
				Properties pop = MessageBundleScriptCreator
						.getComponentResource(LMSS07Panel.class);
				throw new CapMessageException(RespMsgHelper.getMessage(
						"EFD0015", MessageFormat.format(
								pop.getProperty("L1205S07.error21"), mCustId,
								mDupNo)), getClass());
			}

			try {
				Map<String, Object> mapGrpData = getMainGrpData(tGPID);
				// 集團評等年度
				jsonData.put("GpDATAYY",
						Util.parseInt(mapGrpData.get("tDATAYY")) + 1911);
				// l120s04b.setGrpYear(Util.parseInt(mapGrpData.get("tDATAYY"))
				// + 1911);
				// 集團評等
				tGRADE = Util.trim(mapGrpData.get("tGRADE"));
				jsonData.put("GpGRADE", tGRADE);

				// J-107-0087-001 Web
				// e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。
				jsonData.put("GRPSIZE", Util.trim(MapUtils.getObject(
						mapGrpData, "tGRPSIZE", "")));
				jsonData.put("GRPLEVEL", Util.trim(MapUtils.getObject(
						mapGrpData, "tGRPLEVEL", "")));

				// l120s04b.setGrpGrrd(tGRADE);
			} catch (GWException e) {
				// "引進借款人"+rptDoc.borrower_id(0)+" " +
				// rptDoc.borrower(0)+"集團評等資訊錯誤"
				Properties pop = MessageBundleScriptCreator
						.getComponentResource(LMSS07Panel.class);
				throw new CapMessageException(RespMsgHelper.getMessage(
						"EFD0015", MessageFormat.format(
								pop.getProperty("L1205S07.error22"), mCustId,
								mDupNo)), getClass());
			}

            // J-111-0052 修改借戶暨關係戶與本行往來實績彙總表
            try {
                String fullCustId = Util.addSpaceWithValue(mCustId, 10) + mDupNo;
				jsonData.put("keyCustId", mCustId);
				jsonData.put("keyDupNo", mDupNo);
                // DW 如果重覆序號為0，會變空白，非0的值會正常顯示
				String dwFullCustId = Util.addSpaceWithValue(mCustId, 10) + _mDupNo;
                jsonData.put("HINSDATE", null);
                jsonData.put("HINSAMT", null);
                jsonData.put("HINSRATE", null);
                jsonData.put("HINSAVGAMT", null);
                jsonData.put("HINSAVGRATE", null);
                if(lmsService.isHinsCust(fullCustId)) {
                    Map<String, Object> hinsMap = dwdbService.getOTS_HINS_ASCT_maxCycMn();
                    if (hinsMap != null) {
                        int mnCnt = ((hinsMap.get("MNCNT") != null) ? ((Integer) hinsMap.get("MNCNT")) : 0);
//                        Date maxMn = (Date) hinsMap.get("MAXMN");      Date begMn = (Date) hinsMap.get("BEGMN");
                        Date maxMn = ((hinsMap.get("MAXMN") != null) ? ((Date) hinsMap.get("MAXMN")) : null);
                        Date begMn = ((hinsMap.get("BEGMN") != null) ? ((Date) hinsMap.get("BEGMN")) : null);
                        BigDecimal hinsAmt = BigDecimal.ZERO;
                        BigDecimal hinsRate = BigDecimal.ZERO;
                        BigDecimal hinsAvgAmt = BigDecimal.ZERO;
                        BigDecimal hinsAvgRate = BigDecimal.ZERO;
                        if(mnCnt > 0 && maxMn != null && begMn!= null){
                            Map<String, Object> hinsDataMap = dwdbService.getOTS_HINS_ASCT_data(dwFullCustId, maxMn);
                            if (hinsDataMap != null) {
								hinsAmt = ((hinsDataMap.get("SUMCT") != null) ? ((BigDecimal) hinsDataMap.get("SUMCT")) : null);
								hinsRate = ((hinsDataMap.get("AVGRT") != null) ? ((BigDecimal) hinsDataMap.get("AVGRT")) : null);
								// 轉千元
								hinsAmt = hinsAmt.divide(new BigDecimal("1000")).setScale(2, BigDecimal.ROUND_HALF_UP);
								hinsRate = hinsRate.setScale(6, BigDecimal.ROUND_HALF_UP);
                            }
                            Map<String, Object> hinsSumDataMap = dwdbService.getOTS_HINS_ASCT_sumData(dwFullCustId, begMn, maxMn);
                            if (hinsSumDataMap != null) {
                                BigDecimal totalCt = ((hinsSumDataMap.get("TOTALCT") != null) ? ((BigDecimal) hinsSumDataMap.get("TOTALCT")) : null);
                                BigDecimal totalRt = ((hinsSumDataMap.get("TOTALRT") != null) ? ((BigDecimal) hinsSumDataMap.get("TOTALRT")) : null);
								int divMnCnt = ((mnCnt > 6) ? 6 : mnCnt);
                                if(totalCt != null){
									// 除月數得平均後轉千元
									hinsAvgAmt = totalCt.divide(new BigDecimal(divMnCnt)).divide(new BigDecimal("1000"))
											.setScale(2, BigDecimal.ROUND_HALF_UP);
								}
								if(totalRt != null){
									hinsAvgRate = totalRt.divide(new BigDecimal(divMnCnt));
									hinsAvgRate = hinsAvgRate.setScale(6, BigDecimal.ROUND_HALF_UP);
								}
                            }
							jsonData.put("HINSDATE", Util.trim(CapDate.formatDate(maxMn, "yyyy")
									+ "/" + CapDate.formatDate(maxMn, "MM")));
							jsonData.put("HINSAMT", Util.trim(hinsAmt));
							jsonData.put("HINSRATE", Util.trim(hinsRate));
							jsonData.put("HINSAVGAMT", Util.trim(hinsAvgAmt));
							jsonData.put("HINSAVGRATE", Util.trim(hinsAvgRate));
                        }
                    }
                }
            } catch (Exception e) {

            }

			// a Method
			if (!json.isEmpty()) {
				for (Object jsonKey : json.keySet()) {
					String sJsonKey = (String) jsonKey;
					String jsonVal = Util.trim(json.get(sJsonKey));
					// 擷取 "+tID+" "+ xr + " 與本行往來實績資料
					tID = (Util.isEmpty(sJsonKey)) ? null : sJsonKey.substring(
							0, sJsonKey.length() - 1);
					tDUPNO = (Util.isEmpty(sJsonKey)) ? null : sJsonKey
							.substring(sJsonKey.length() - 1);
					tCUSTNAME = jsonVal;
					String _MAX_CYC_MN = MAX_CYC_MN.replace("-", "");
					dQueryDateE = CapDate.getDate(MAX_CYC_MN,
							UtilConstants.DateFormat.YYYY_MM_DD);
					dQueryDateS = CapDate.getDate(CapDate
							.formatyyyyMMddToDateFormat(
									CapDate.addMonth(_MAX_CYC_MN, -5),
									UtilConstants.DateFormat.YYYY_MM_DD),
							UtilConstants.DateFormat.YYYY_MM_DD);
					JSONObject tempJson = getL120S04B(mainId, tID, tDUPNO,
							CapDate.formatDate(dQueryDateS,
									UtilConstants.DateFormat.YYYY_MM_DD),
							CapDate.formatDate(dQueryDateE,
									UtilConstants.DateFormat.YYYY_MM_DD));
					if (tempJson == null) {
						// "引進"+tID+" " + xr+"業務往來資訊錯誤"
						Properties pop = MessageBundleScriptCreator
								.getComponentResource(LMSS07Panel.class);
						throw new CapMessageException(RespMsgHelper.getMessage(
								"EFD0015", MessageFormat.format(
										pop.getProperty("L1205S07.error23"),
										tID, tDUPNO)), getClass());
					}
					jsonData.put("Rate3_1",
							CapDate.formatDate(dQueryDateS, "yyyy") + "/"
									+ CapDate.formatDate(dQueryDateS, "MM"));

					jsonData.put("Rate3_2",
							CapDate.formatDate(dQueryDateE, "yyyy") + "/"
									+ CapDate.formatDate(dQueryDateE, "MM"));

					jsonData.put(
							"Rate3_3",
							Util.trim(Util.parseLong(jsonData.get("Rate3_3"))
									+ tempJson.getLong("depTime")
									+ tempJson.getLong("depFixed")));
					jsonData.put(
							"Rate3_4",
							Util.trim(Util.parseLong(jsonData.get("Rate3_4"))
									+ tempJson.getLong("depTime")));

					// // 設定主要集團企業最近一年起日
					// l120s04b.setMainGrpDateS(dQueryDateS);
					// // 設定主要集團企業最近一年迄日
					// l120s04b.setMainGrpDateE(dQueryDateE);
					// // 本行平均存款合計－金額
					// l120s04b.setMegaAvgAmt(new BigDecimal(tempJson
					// .getLong("depTime")
					// + tempJson.getLong("depFixed")));
					// // 活期性存款－金額
					// l120s04b.setDemandAmt(new BigDecimal(tempJson
					// .getLong("depTime")));

					// a Method
					// 101/1-3月 平均存款 平均授信 開狀及匯出 出押及匯入
					// L120S04C
					// L120S04C l120s04c = new L120S04C();
					dQueryDateS = (MAX_CYC_MN.length() < 4) ? null : CapDate
							.getDate(MAX_CYC_MN.substring(0, 4) + "-01-01",
									UtilConstants.DateFormat.YYYY_MM_DD);
					dQueryDateE = (MAX_CYC_MN.length() < 7) ? null : CapDate
							.getDate(MAX_CYC_MN.substring(0, 7) + "-01",
									UtilConstants.DateFormat.YYYY_MM_DD);
					// l120s04c.setDocDateS(dQueryDateS);
					// l120s04c.setDocDateE(dQueryDateE);
					jsonData.put("field1_3",
							CapDate.formatDate(dQueryDateS, "yyyy") + "/1~"
									+ CapDate.formatDate(dQueryDateE, "M")
									+ "月");
					jsonData.put("field1_3_1",
							CapDate.formatDate(dQueryDateS, "yyyy") + "/1~"
									+ CapDate.formatDate(dQueryDateE, "M")
									+ "月");
					// J-112-0078 配合企金處，修改「借戶暨關係戶與本行往來實績彙總表」中，增列各業務別利潤貢獻度欄位等。
					// 當年度
					jsonS04e.put("docDate_0",
							CapDate.formatDate(dQueryDateS, "yyyy") + "/1~"
									+ CapDate.formatDate(dQueryDateE, "M")
									+ "月");

					JSONObject tempJson3 = getL120S04B(mainId, tID, tDUPNO,
							CapDate.formatDate(dQueryDateS,
									UtilConstants.DateFormat.YYYY_MM_DD),
							CapDate.formatDate(dQueryDateE,
									UtilConstants.DateFormat.YYYY_MM_DD));
					List<Map<String, Object>> mapBal = this.dwdbService
							.findDW_DM_CUBCPCM_TOTAL_ATTRIBUTE(
									tID,
									tDUPNO,
									CapDate.formatDate(dQueryDateS,
											UtilConstants.DateFormat.YYYY_MM_DD),
									CapDate.formatDate(dQueryDateE,
											UtilConstants.DateFormat.YYYY_MM_DD));
					List<?> rows8 = this.dwdbService
							.findDW_DM_CUBCPCMOVS_TOTAL_ATTRIBUTE(
									tID,
									tDUPNO,
									CapDate.formatDate(dQueryDateS,
											UtilConstants.DateFormat.YYYY_MM_DD),
									CapDate.formatDate(dQueryDateE,
											UtilConstants.DateFormat.YYYY_MM_DD));
					Iterator<?> it8 = rows8.iterator();
					if (tempJson3 == null) {
						// "引進"+tID+" " + xr+"業務往來資訊錯誤"
						Properties pop = MessageBundleScriptCreator
								.getComponentResource(LMSS07Panel.class);
						throw new CapMessageException(RespMsgHelper.getMessage(
								"EFD0015", MessageFormat.format(
										pop.getProperty("L1205S07.error23"),
										tID, tDUPNO)), getClass());
					}
					if (mapBal.isEmpty()) {
						// "引進"+tID+" " + xr+"利潤貢獻度錯誤"
						Properties pop = MessageBundleScriptCreator
								.getComponentResource(LMSS07Panel.class);
						throw new CapMessageException(RespMsgHelper.getMessage(
								"EFD0015", MessageFormat.format(
										pop.getProperty("L1205S07.error24"),
										tID, tDUPNO)), getClass());
					}
					for (Map<String, Object> mapAttri : mapBal) {
						bal = Util.parseDouble(Util.trim(mapAttri
								.get("TOTAL_ATTRIBUTE")));
						balSalary = Util.parseDouble(Util.trim(mapAttri
								.get("SLDP_TOTAL")));
						balTrustFdta = Util.parseDouble(Util.trim(mapAttri
								.get("FDTA_T_TOTAL")));
						if (bal != 0) {
							bal = Util.parseDouble(CapMath.round(
									Util.trim(bal / 1000), 0));
						}
						if (balSalary != 0) {
							balSalary = Util.parseDouble(CapMath.round(
									Util.trim(balSalary / 1000), 0));
						}
						if (balTrustFdta != 0) {
							balTrustFdta = Util.parseDouble(CapMath.round(
									Util.trim(balTrustFdta / 1000), 0));
						}

						break;
					}
					// 海外貢獻度(非存款) Miller added at 2012/07/27
					while (it8.hasNext()) {
						Map<?, ?> dataMap8 = (Map<?, ?>) it8.next();
						double seaBal = Util.parseDouble(Util.trim(dataMap8
								.get("TOTAL_ATTRIBUTE")));
						double seaSalaryBal = Util.parseDouble(Util
								.trim(dataMap8.get("SLDP_TOTAL")));
						double seaTrustFdtaBal = Util.parseDouble(Util
								.trim(dataMap8.get("FDTA_T_TOTAL")));
						String curr = Util.trim(dataMap8.get("CURR"));
						if (seaBal != 0) {
							seaBal = branchRate.toTWDAmt(
									(Util.isEmpty(curr)) ? "TWD" : curr,
									LMSUtil.toBigDecimal(seaBal)).doubleValue();
							seaBal = Util.parseDouble(CapMath.round(
									Util.trim(seaBal / 1000), 0));
						}
						if (seaSalaryBal != 0) {
							seaSalaryBal = branchRate.toTWDAmt(
									(Util.isEmpty(curr)) ? "TWD" : curr,
									LMSUtil.toBigDecimal(seaSalaryBal))
									.doubleValue();
							seaSalaryBal = Util.parseDouble(CapMath.round(
									Util.trim(seaSalaryBal / 1000), 0));
						}
						if (seaTrustFdtaBal != 0) {
							seaTrustFdtaBal = branchRate.toTWDAmt(
									(Util.isEmpty(curr)) ? "TWD" : curr,
									LMSUtil.toBigDecimal(seaTrustFdtaBal))
									.doubleValue();
							seaTrustFdtaBal = Util.parseDouble(CapMath.round(
									Util.trim(seaTrustFdtaBal / 1000), 0));
						}
						bal += seaBal;
						balSalary += seaSalaryBal;
						balTrustFdta += seaTrustFdtaBal;
					}
					end3Mon = CapDate.formatDate(dQueryDateE, "MM");

					// J-112-0078 配合企金處，修改「借戶暨關係戶與本行往來實績彙總表」中，增列各業務別利潤貢獻度欄位等。
					boolean docKind1 = false;
					if (Util.trim(tID).equals(mCustId)
							&& Util.trim(tDUPNO).equals(_mDupNo)) {
						jsonData.put("field2_3", Util.trim(Util
								.parseLong(tempJson3.get("depTime"))
								+ Util.parseLong(tempJson3.get("depFixed"))));
						jsonData.put("field3_3",
								Util.trim(tempJson3.get("loanAvgBal")));
						jsonData.put("field4_3", Util.trim(Util
								.parseLong(tempJson3.get("exchgImpAmt"))
								+ Util.parseLong(tempJson3.get("exchgOutAmt"))));
						jsonData.put("field5_3", Util.trim(Util
								.parseLong(tempJson3.get("exchgExpAmt"))
								+ Util.parseLong(tempJson3.get("exchgInAmt"))));
						jsonData.put("field6_3", Util.trim(bal));
						jsonData.put("field8_3",
								Util.trim(tempJson3.get("IN_LN_FA_B")));
						jsonData.put("field9_3",
								Util.trim(tempJson3.get("IN_LN_FA_S")));
						jsonData.put("fieldB_3", Util.trim(balSalary));
						jsonData.put("fieldC_3", Util.trim(balTrustFdta));
						docKind1 = true;
					}
					jsonData.put("field2_3_1", Util.trim(Util
							.parseLong(jsonData.get("field2_3_1"))
							+ Util.parseLong(tempJson3.get("depTime"))
							+ Util.parseLong(tempJson3.get("depFixed"))));
					jsonData.put("field3_3_1", Util.trim(Util
							.parseLong(jsonData.get("field3_3_1"))
							+ Util.parseLong(tempJson3.get("loanAvgBal"))));
					jsonData.put("field4_3_1", Util.trim(Util
							.parseLong(jsonData.get("field4_3_1"))
							+ Util.parseLong(tempJson3.get("exchgImpAmt"))
							+ Util.parseLong(tempJson3.get("exchgOutAmt"))));
					jsonData.put("field5_3_1", Util.trim(Util
							.parseLong(jsonData.get("field5_3_1"))
							+ Util.parseLong(tempJson3.get("exchgExpAmt"))
							+ Util.parseLong(tempJson3.get("exchgInAmt"))));
					jsonData.put("field6_3_1", Util.trim(Util
							.parseLong(jsonData.get("field6_3_1"))
							+ Util.parseLong(bal)));
					jsonData.put("field8_3_1", Util.trim(Util
							.parseLong(jsonData.get("field8_3_1"))
							+ Util.parseLong(tempJson3.get("IN_LN_FA_B"))));
					jsonData.put("field9_3_1", Util.trim(Util
							.parseLong(jsonData.get("field9_3_1"))
							+ Util.parseLong(tempJson3.get("IN_LN_FA_S"))));
					jsonData.put("fieldB_3_1", Util.trim(Util
							.parseLong(jsonData.get("fieldB_3_1"))
							+ Util.parseLong(balSalary)));
					jsonData.put("fieldC_3_1", Util.trim(Util
							.parseLong(jsonData.get("fieldC_3_1"))
							+ Util.parseLong(balTrustFdta)));

					// J-112-0078 配合企金處，修改「借戶暨關係戶與本行往來實績彙總表」中，增列各業務別利潤貢獻度欄位等。
					// 利潤貢獻度增列業務別占比
					jsonS04e = lmsService.getAttributesGroupByBC3_CD(jsonS04e, docKind1, "0", tID, tDUPNO,
							CapDate.formatDate(dQueryDateS, UtilConstants.DateFormat.YYYY_MM_DD),
							CapDate.formatDate(dQueryDateE, UtilConstants.DateFormat.YYYY_MM_DD));

					// a Method
					// 100年 平均存款 平均授信 開狀及匯出 出押及匯入
					String year1 = (MAX_CYC_MN.length() < 4) ? null
							: MAX_CYC_MN.substring(0, 4);
					if (Util.isNotEmpty(year1)) {
						dQueryDateS = CapDate.getDate(
								Util.trim(Util.parseInt(year1) - 1) + "-01-01",
								UtilConstants.DateFormat.YYYY_MM_DD);
						dQueryDateE = CapDate.getDate(
								Util.trim(Util.parseInt(year1) - 1) + "-12-01",
								UtilConstants.DateFormat.YYYY_MM_DD);
					}
					JSONObject tempJson4 = getL120S04B(mainId, tID, tDUPNO,
							CapDate.formatDate(dQueryDateS,
									UtilConstants.DateFormat.YYYY_MM_DD),
							CapDate.formatDate(dQueryDateE,
									UtilConstants.DateFormat.YYYY_MM_DD));
					List<Map<String, Object>> mapBal2 = this.dwdbService
							.findDW_DM_CUBCPCM_TOTAL_ATTRIBUTE(
									tID,
									tDUPNO,
									CapDate.formatDate(dQueryDateS,
											UtilConstants.DateFormat.YYYY_MM_DD),
									CapDate.formatDate(dQueryDateE,
											UtilConstants.DateFormat.YYYY_MM_DD));
					List<?> rows82 = this.dwdbService
							.findDW_DM_CUBCPCMOVS_TOTAL_ATTRIBUTE(
									tID,
									tDUPNO,
									CapDate.formatDate(dQueryDateS,
											UtilConstants.DateFormat.YYYY_MM_DD),
									CapDate.formatDate(dQueryDateE,
											UtilConstants.DateFormat.YYYY_MM_DD));
					Iterator<?> it82 = rows82.iterator();
					if (tempJson4 == null) {
						// "引進"+tID+" " + xr+"業務往來資訊錯誤"
						Properties pop = MessageBundleScriptCreator
								.getComponentResource(LMSS07Panel.class);
						throw new CapMessageException(RespMsgHelper.getMessage(
								"EFD0015", MessageFormat.format(
										pop.getProperty("L1205S07.error23"),
										tID, tDUPNO)), getClass());
					}
					if (mapBal2.isEmpty()) {
						// "引進"+tID+" " + xr+"利潤貢獻度錯誤"
						Properties pop = MessageBundleScriptCreator
								.getComponentResource(LMSS07Panel.class);
						throw new CapMessageException(RespMsgHelper.getMessage(
								"EFD0015", MessageFormat.format(
										pop.getProperty("L1205S07.error24"),
										tID, tDUPNO)), getClass());
					}
					for (Map<String, Object> mapAttri : mapBal2) {
						bal = Util.parseDouble(Util.trim(mapAttri
								.get("TOTAL_ATTRIBUTE")));
						balSalary = Util.parseDouble(Util.trim(mapAttri
								.get("SLDP_TOTAL")));
						balTrustFdta = Util.parseDouble(Util.trim(mapAttri
								.get("FDTA_T_TOTAL")));
						if (bal != 0) {
							bal = Util.parseDouble(CapMath.round(
									Util.trim(bal / 1000), 0));
						}
						if (balSalary != 0) {
							balSalary = Util.parseDouble(CapMath.round(
									Util.trim(balSalary / 1000), 0));
						}
						if (balTrustFdta != 0) {
							balTrustFdta = Util.parseDouble(CapMath.round(
									Util.trim(balTrustFdta / 1000), 0));
						}
						break;
					}
					// 海外貢獻度(非存款) Miller added at 2012/07/27
					while (it82.hasNext()) {
						Map<?, ?> dataMap8 = (Map<?, ?>) it82.next();
						double seaBal = Util.parseDouble(Util.trim(dataMap8
								.get("TOTAL_ATTRIBUTE")));
						double seaSalaryBal = Util.parseDouble(Util
								.trim(dataMap8.get("SLDP_TOTAL")));
						double seaTrustFdtaBal = Util.parseDouble(Util
								.trim(dataMap8.get("FDTA_T_TOTAL")));
						String curr = Util.trim(dataMap8.get("CURR"));
						if (seaBal != 0) {
							seaBal = branchRate.toTWDAmt(
									(Util.isEmpty(curr)) ? "TWD" : curr,
									LMSUtil.toBigDecimal(seaBal)).doubleValue();
							seaBal = Util.parseDouble(CapMath.round(
									Util.trim(seaBal / 1000), 0));
						}
						if (seaSalaryBal != 0) {
							seaSalaryBal = branchRate.toTWDAmt(
									(Util.isEmpty(curr)) ? "TWD" : curr,
									LMSUtil.toBigDecimal(seaSalaryBal))
									.doubleValue();
							seaSalaryBal = Util.parseDouble(CapMath.round(
									Util.trim(seaSalaryBal / 1000), 0));
						}
						if (seaTrustFdtaBal != 0) {
							seaTrustFdtaBal = branchRate.toTWDAmt(
									(Util.isEmpty(curr)) ? "TWD" : curr,
									LMSUtil.toBigDecimal(seaTrustFdtaBal))
									.doubleValue();
							seaTrustFdtaBal = Util.parseDouble(CapMath.round(
									Util.trim(seaTrustFdtaBal / 1000), 0));
						}

						bal += seaBal;
						balSalary += seaSalaryBal;
						balTrustFdta += seaTrustFdtaBal;

					}
					jsonData.put("field1_2",
							CapDate.formatDate(dQueryDateS, "yyyy") + "年");
					jsonData.put("field1_2_1",
							CapDate.formatDate(dQueryDateS, "yyyy") + "年");
					// J-112-0078 配合企金處，修改「借戶暨關係戶與本行往來實績彙總表」中，增列各業務別利潤貢獻度欄位等。
					// 前一年度 year1
					jsonS04e.put("docDate_1", CapDate.formatDate(dQueryDateS, "yyyy") + "年");

					if (Util.trim(tID).equals(mCustId)
							&& Util.trim(tDUPNO).equals(_mDupNo)) {
						jsonData.put("field2_2", Util.trim(Util
								.parseLong(tempJson4.get("depTime"))
								+ Util.parseLong(tempJson4.get("depFixed"))));
						jsonData.put("field3_2",
								Util.trim(tempJson4.get("loanAvgBal")));
						jsonData.put("field4_2", Util.trim(Util
								.parseLong(tempJson4.get("exchgImpAmt"))
								+ Util.parseLong(tempJson4.get("exchgOutAmt"))));
						jsonData.put("field5_2", Util.trim(Util
								.parseLong(tempJson4.get("exchgExpAmt"))
								+ Util.parseLong(tempJson4.get("exchgInAmt"))));
						jsonData.put("field6_2", Util.trim(bal));
						jsonData.put("field8_2",
								Util.trim(tempJson4.get("IN_LN_FA_B")));
						jsonData.put("field9_2",
								Util.trim(tempJson4.get("IN_LN_FA_S")));
						jsonData.put("fieldB_2", Util.trim(balSalary));
						jsonData.put("fieldC_2", Util.trim(balTrustFdta));
					}
					jsonData.put("field2_2_1", Util.trim(Util
							.parseLong(jsonData.get("field2_2_1"))
							+ Util.parseLong(tempJson4.get("depTime"))
							+ Util.parseLong(tempJson4.get("depFixed"))));
					jsonData.put("field3_2_1", Util.trim(Util
							.parseLong(jsonData.get("field3_2_1"))
							+ Util.parseLong(tempJson4.get("loanAvgBal"))));
					jsonData.put("field4_2_1", Util.trim(Util
							.parseLong(jsonData.get("field4_2_1"))
							+ Util.parseLong(tempJson4.get("exchgImpAmt"))
							+ Util.parseLong(tempJson4.get("exchgOutAmt"))));
					jsonData.put("field5_2_1", Util.trim(Util
							.parseLong(jsonData.get("field5_2_1"))
							+ Util.parseLong(tempJson4.get("exchgExpAmt"))
							+ Util.parseLong(tempJson4.get("exchgInAmt"))));
					jsonData.put("field6_2_1", Util.trim(Util
							.parseLong(jsonData.get("field6_2_1"))
							+ Util.parseLong(bal)));
					jsonData.put("field8_2_1", Util.trim(Util
							.parseLong(jsonData.get("field8_2_1"))
							+ Util.parseLong(tempJson4.get("IN_LN_FA_B"))));
					jsonData.put("field9_2_1", Util.trim(Util
							.parseLong(jsonData.get("field9_2_1"))
							+ Util.parseLong(tempJson4.get("IN_LN_FA_S"))));
					jsonData.put("fieldB_2_1", Util.trim(Util
							.parseLong(jsonData.get("fieldB_2_1"))
							+ Util.parseLong(balSalary)));
					jsonData.put("fieldC_2_1", Util.trim(Util
							.parseLong(jsonData.get("fieldC_2_1"))
							+ Util.parseLong(balTrustFdta)));

					// J-112-0078 配合企金處，修改「借戶暨關係戶與本行往來實績彙總表」中，增列各業務別利潤貢獻度欄位等。
					// 利潤貢獻度增列業務別占比
					jsonS04e = lmsService.getAttributesGroupByBC3_CD(jsonS04e, docKind1, "1", tID, tDUPNO,
							CapDate.formatDate(dQueryDateS, UtilConstants.DateFormat.YYYY_MM_DD),
							CapDate.formatDate(dQueryDateE, UtilConstants.DateFormat.YYYY_MM_DD));

					// a Method
					// 99年 平均存款 平均授信 開狀及匯出 出押及匯入
					String year2 = (MAX_CYC_MN.length() < 4) ? null
							: MAX_CYC_MN.substring(0, 4);
					if (Util.isNotEmpty(year2)) {
						dQueryDateS = CapDate.getDate(
								Util.trim(Util.parseInt(year2) - 2) + "-01-01",
								UtilConstants.DateFormat.YYYY_MM_DD);
						dQueryDateE = CapDate.getDate(
								Util.trim(Util.parseInt(year2) - 2) + "-12-01",
								UtilConstants.DateFormat.YYYY_MM_DD);
					}
					JSONObject tempJson5 = getL120S04B(mainId, tID, tDUPNO,
							CapDate.formatDate(dQueryDateS,
									UtilConstants.DateFormat.YYYY_MM_DD),
							CapDate.formatDate(dQueryDateE,
									UtilConstants.DateFormat.YYYY_MM_DD));
					List<Map<String, Object>> mapBal3 = this.dwdbService
							.findDW_DM_CUBCPCM_TOTAL_ATTRIBUTE(
									tID,
									tDUPNO,
									CapDate.formatDate(dQueryDateS,
											UtilConstants.DateFormat.YYYY_MM_DD),
									CapDate.formatDate(dQueryDateE,
											UtilConstants.DateFormat.YYYY_MM_DD));
					List<?> rows83 = this.dwdbService
							.findDW_DM_CUBCPCMOVS_TOTAL_ATTRIBUTE(
									tID,
									tDUPNO,
									CapDate.formatDate(dQueryDateS,
											UtilConstants.DateFormat.YYYY_MM_DD),
									CapDate.formatDate(dQueryDateE,
											UtilConstants.DateFormat.YYYY_MM_DD));
					Iterator<?> it83 = rows83.iterator();
					if (tempJson5 == null) {
						// "引進"+tID+" " + xr+"業務往來資訊錯誤"
						Properties pop = MessageBundleScriptCreator
								.getComponentResource(LMSS07Panel.class);
						throw new CapMessageException(RespMsgHelper.getMessage(
								"EFD0015", MessageFormat.format(
										pop.getProperty("L1205S07.error23"),
										tID, tDUPNO)), getClass());
					}
					if (mapBal3.isEmpty()) {
						// "引進"+tID+" " + xr+"利潤貢獻度錯誤"
						Properties pop = MessageBundleScriptCreator
								.getComponentResource(LMSS07Panel.class);
						throw new CapMessageException(RespMsgHelper.getMessage(
								"EFD0015", MessageFormat.format(
										pop.getProperty("L1205S07.error24"),
										tID, tDUPNO)), getClass());
					}
					for (Map<String, Object> mapAttri : mapBal3) {
						bal = Util.parseDouble(Util.trim(mapAttri
								.get("TOTAL_ATTRIBUTE")));
						balSalary = Util.parseDouble(Util.trim(mapAttri
								.get("SLDP_TOTAL")));
						balTrustFdta = Util.parseDouble(Util.trim(mapAttri
								.get("FDTA_T_TOTAL")));
						if (bal != 0) {
							bal = Util.parseDouble(CapMath.round(
									Util.trim(bal / 1000), 0));
						}
						if (balSalary != 0) {
							balSalary = Util.parseDouble(CapMath.round(
									Util.trim(balSalary / 1000), 0));
						}
						if (balTrustFdta != 0) {
							balTrustFdta = Util.parseDouble(CapMath.round(
									Util.trim(balTrustFdta / 1000), 0));
						}
						break;
					}
					// 海外貢獻度(非存款) Miller added at 2012/07/27
					while (it83.hasNext()) {
						Map<?, ?> dataMap8 = (Map<?, ?>) it83.next();
						double seaBal = Util.parseDouble(Util.trim(dataMap8
								.get("TOTAL_ATTRIBUTE")));
						double seaSalaryBal = Util.parseDouble(Util
								.trim(dataMap8.get("SLDP_TOTAL")));
						double seaTrustFdtaBal = Util.parseDouble(Util
								.trim(dataMap8.get("FDTA_T_TOTAL")));
						String curr = Util.trim(dataMap8.get("CURR"));
						if (seaBal != 0) {
							seaBal = branchRate.toTWDAmt(
									(Util.isEmpty(curr)) ? "TWD" : curr,
									LMSUtil.toBigDecimal(seaBal)).doubleValue();
							seaBal = Util.parseDouble(CapMath.round(
									Util.trim(seaBal / 1000), 0));
						}
						if (seaSalaryBal != 0) {
							seaSalaryBal = branchRate.toTWDAmt(
									(Util.isEmpty(curr)) ? "TWD" : curr,
									LMSUtil.toBigDecimal(seaSalaryBal))
									.doubleValue();
							seaSalaryBal = Util.parseDouble(CapMath.round(
									Util.trim(seaSalaryBal / 1000), 0));
						}
						if (seaTrustFdtaBal != 0) {
							seaTrustFdtaBal = branchRate.toTWDAmt(
									(Util.isEmpty(curr)) ? "TWD" : curr,
									LMSUtil.toBigDecimal(seaTrustFdtaBal))
									.doubleValue();
							seaTrustFdtaBal = Util.parseDouble(CapMath.round(
									Util.trim(seaTrustFdtaBal / 1000), 0));
						}
						bal += seaBal;
						balSalary += seaSalaryBal;
						balTrustFdta += seaTrustFdtaBal;
					}
					jsonData.put("field1_1",
							CapDate.formatDate(dQueryDateS, "yyyy") + "年");
					jsonData.put("field1_1_1",
							CapDate.formatDate(dQueryDateS, "yyyy") + "年");

					if (Util.trim(tID).equals(mCustId)
							&& Util.trim(tDUPNO).equals(_mDupNo)) {
						jsonData.put("field2_1", Util.trim(Util
								.parseLong(tempJson5.get("depTime"))
								+ Util.parseLong(tempJson5.get("depFixed"))));
						jsonData.put("field3_1",
								Util.trim(tempJson5.get("loanAvgBal")));
						jsonData.put("field4_1", Util.trim(Util
								.parseLong(tempJson5.get("exchgImpAmt"))
								+ Util.parseLong(tempJson5.get("exchgOutAmt"))));
						jsonData.put("field5_1", Util.trim(Util
								.parseLong(tempJson5.get("exchgExpAmt"))
								+ Util.parseLong(tempJson5.get("exchgInAmt"))));
						jsonData.put("field6_1", Util.trim(bal));
						jsonData.put("field8_1",
								Util.trim(tempJson5.get("IN_LN_FA_B")));
						jsonData.put("field9_1",
								Util.trim(tempJson5.get("IN_LN_FA_S")));
						jsonData.put("fieldB_1", Util.trim(balSalary));
						jsonData.put("fieldC_1", Util.trim(balTrustFdta));
					}
					jsonData.put("field2_1_1", Util.trim(Util
							.parseLong(jsonData.get("field2_1_1"))
							+ Util.parseLong(tempJson5.get("depTime"))
							+ Util.parseLong(tempJson5.get("depFixed"))));
					jsonData.put("field3_1_1", Util.trim(Util
							.parseLong(jsonData.get("field3_1_1"))
							+ Util.parseLong(tempJson5.get("loanAvgBal"))));
					jsonData.put("field4_1_1", Util.trim(Util
							.parseLong(jsonData.get("field4_1_1"))
							+ Util.parseLong(tempJson5.get("exchgImpAmt"))
							+ Util.parseLong(tempJson5.get("exchgOutAmt"))));
					jsonData.put("field5_1_1", Util.trim(Util
							.parseLong(jsonData.get("field5_1_1"))
							+ Util.parseLong(tempJson5.get("exchgExpAmt"))
							+ Util.parseLong(tempJson5.get("exchgInAmt"))));
					jsonData.put("field6_1_1", Util.trim(Util
							.parseLong(jsonData.get("field6_1_1"))
							+ Util.parseLong(bal)));
					jsonData.put("field8_1_1", Util.trim(Util
							.parseLong(jsonData.get("field8_1_1"))
							+ Util.parseLong(tempJson5.get("IN_LN_FA_B"))));
					jsonData.put("field9_1_1", Util.trim(Util
							.parseLong(jsonData.get("field9_1_1"))
							+ Util.parseLong(tempJson5.get("IN_LN_FA_S"))));
					jsonData.put("fieldB_1_1", Util.trim(Util
							.parseLong(jsonData.get("fieldB_1_1"))
							+ Util.parseLong(balSalary)));
					jsonData.put("fieldC_1_1", Util.trim(Util
							.parseLong(jsonData.get("fieldC_1_1"))
							+ Util.parseLong(balTrustFdta)));
				}

				// a Method
				for (int i = 1; i <= 3; i++) {
					if (i == 3) {
						// 因資料未滿一年，所以報酬率要年化
						if (Util.parseDouble(jsonData.get("field3_"
								+ Util.trim(i)))
								- Util.parseDouble(jsonData.get("field8_"
										+ Util.trim(i)))
								+ Util.parseDouble(jsonData.get("field9_"
										+ Util.trim(i))) > 0) {
							jsonData.put(
									"field7_" + Util.trim(i),
									Util.trim(Util.parseDouble(

									CapMath.round(
											Util.trim((Util.parseDouble(Util.trim(jsonData
													.get("field6_"
															+ Util.trim(i)))) / (Util.parseDouble(Util.trim(jsonData
													.get("field3_"
															+ Util.trim(i))))
													- Util.parseDouble(Util.trim(jsonData.get("field8_"
															+ Util.trim(i)))) + Util.parseDouble(Util.trim(jsonData
													.get("field9_"
															+ Util.trim(i))))))
													/ Util.parseDouble(end3Mon)
													* 12), 4)) * 100));
						} else {
							jsonData.put("field7_" + Util.trim(i), "N.A.");
						}
						if (Util.parseDouble(jsonData.get("field3_"
								+ Util.trim(i) + "_1"))
								- Util.parseDouble(jsonData.get("field8_"
										+ Util.trim(i) + "_1"))
								+ Util.parseDouble(jsonData.get("field9_"
										+ Util.trim(i) + "_1")) > 0) {
							jsonData.put(
									"field7_" + Util.trim(i) + "_1",
									Util.trim(Util.parseDouble(CapMath.round(
											Util.trim((Util.parseDouble(Util.trim(jsonData
													.get("field6_"
															+ Util.trim(i)
															+ "_1"))) / (Util.parseDouble(Util.trim(jsonData
													.get("field3_"
															+ Util.trim(i)
															+ "_1")))
													- Util.parseDouble(Util.trim(jsonData.get("field8_"
															+ Util.trim(i)
															+ "_1"))) + Util.parseDouble(Util.trim(jsonData
													.get("field9_"
															+ Util.trim(i)
															+ "_1")))))
													/ Util.parseDouble(end3Mon)
													* 12), 4)) * 100));
						} else {
							jsonData.put("field7_" + Util.trim(i) + "_1",
									"N.A.");
						}
					} else {
						if (Util.parseDouble(jsonData.get("field3_"
								+ Util.trim(i)))
								- Util.parseDouble(jsonData.get("field8_"
										+ Util.trim(i)))
								+ Util.parseDouble(jsonData.get("field9_"
										+ Util.trim(i))) > 0) {
							jsonData.put(
									"field7_" + Util.trim(i),
									Util.trim(Util.parseDouble(

									CapMath.round(
											Util.trim((Util.parseDouble(Util.trim(jsonData
													.get("field6_"
															+ Util.trim(i)))) / (Util.parseDouble(Util.trim(jsonData
													.get("field3_"
															+ Util.trim(i))))
													- Util.parseDouble(Util.trim(jsonData.get("field8_"
															+ Util.trim(i)))) + Util.parseDouble(Util.trim(jsonData
													.get("field9_"
															+ Util.trim(i))))))),
											4)) * 100));
						} else {
							jsonData.put("field7_" + Util.trim(i), "N.A.");
						}
						if (Util.parseDouble(jsonData.get("field3_"
								+ Util.trim(i) + "_1"))
								- Util.parseDouble(jsonData.get("field8_"
										+ Util.trim(i) + "_1"))
								+ Util.parseDouble(jsonData.get("field9_"
										+ Util.trim(i) + "_1")) > 0) {
							jsonData.put(
									"field7_" + Util.trim(i) + "_1",
									Util.trim(Util.parseDouble(CapMath.round(
											Util.trim((Util.parseDouble(Util.trim(jsonData
													.get("field6_"
															+ Util.trim(i)
															+ "_1"))) / (Util.parseDouble(Util.trim(jsonData
													.get("field3_"
															+ Util.trim(i)
															+ "_1")))
													- Util.parseDouble(Util.trim(jsonData.get("field8_"
															+ Util.trim(i)
															+ "_1"))) + Util.parseDouble(Util.trim(jsonData
													.get("field9_"
															+ Util.trim(i)
															+ "_1")))))), 4)) * 100));
						} else {
							jsonData.put("field7_" + Util.trim(i) + "_1",
									"N.A.");
						}
					}
				}

				if (Util.isNotEmpty(Util.trim(jsonData.get("Rate3_3")))
						&& !"0".equals(Util.trim(jsonData.get("Rate3_3")))) {
					jsonData.put("Rate3_5", Util.trim(Util.parseDouble(CapMath
							.round(Util.trim(Util.parseDouble(Util
									.trim(jsonData.get("Rate3_4")))
									/ Util.parseDouble(Util.trim(jsonData
											.get("Rate3_3")))), 4)) * 100));
				} else {
					jsonData.put("Rate3_5", "N.A.");
				}
			}
			// }
		}
		// result.set("l120s04b", DataParse.toResult(l120s04b));

		// 開始匯率轉換
		String[] jsonKeys = Util.getMapKey(jsonData);
		for (String jsonKey : jsonKeys) {
			if (jsonKey.contains("field4") || jsonKey.contains("field5")) {
				jsonData.put(jsonKey, CapMath.round(
						branchRate
								.toUSDAmt(
										"TWD",
										new BigDecimal(Util.trim(jsonData
												.get(jsonKey)))).toString(), 0));
			}
		}
		// result.set("jsonData", DataParse.toResult(jsonData));

		// J-103-0419-001 Web e-Loan授信管理系統關係戶往來實績彙總表(ROA表)新增引進風險性資產平均餘額報酬率
		importL120s04b_Risk_weighted_Assets(mainId, custId, dupNo,
				queryDateS, queryDateE, jsonData, branchRate);

		// M-112-0140 「借戶暨關係戶與本行往來實績彙總表」下方各業務別利潤貢獻度，另以附註方式說明薪轉戶持有信用卡之貢獻度。
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS07Panel.class);
		lmsService.importL120s04b_SalaryCard(mainId, custId, dupNo,
                queryDateS, queryDateE, pop, jsonData);

		this.saveL120S04BC(mainId, jsonData);
		// J-112-0078 配合企金處，修改「借戶暨關係戶與本行往來實績彙總表」中，增列各業務別利潤貢獻度欄位等。
		this.saveL120S04E(mainId, jsonS04e);
		// return result;
	}

	/**
	 * 將JsonData資料儲存到L120S04B,L120S04C Model裡
	 * 
	 * @param mainId
	 * @param jsonData
	 */
	private void saveL120S04BC(String mainId, JSONObject jsonData) {
		// J-107-0087-001 Web
		// e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。
		// J-111-0052 修改借戶暨關係戶與本行往來實績彙總表
		// M-112-0140 「借戶暨關係戶與本行往來實績彙總表」下方各業務別利潤貢獻度，另以附註方式說明薪轉戶持有信用卡之貢獻度。
		String[] s4aCol = new String[] { "GpID", "GpName", "GpDATAYY",
				"GpGRADE", "Rate1_2", "Rate1_3", "Rate1", "Rate3_1", "Rate3_2",
				"Rate3_3", "Rate3_4", "Rate3_5", "Rate4_2", "Rate4_3", "Rate4",
				"Rate5_2", "Rate5_3", "Rate5", "GRPSIZE", "GRPLEVEL",
				"HINSDATE", "HINSAMT", "HINSRATE", "HINSAVGAMT", "HINSAVGRATE",
				"keyCustId", "keyDupNo", "salaryAcct", "salaryCardAcct",
				"salaryCardDateS", "salaryCardDateE", "salaryCardPcAmt" };
		String[] s04bCola1 = new String[] { "field1", "field2", "field3",
				"field8", "field9", "field4", "field5", "field6_1", "field7_1",
				"fieldB_1", "fieldC_1" };
		String[] s04bCola2 = new String[] { "field1_1", "field2_1", "field3_1",
				"field8_1", "field9_1", "field4_1", "field5_1", "field6_1",
				"field7_1", "fieldB_1", "fieldC_1" };
		String[] s04bCola3 = new String[] { "field1_2", "field2_2", "field3_2",
				"field8_2", "field9_2", "field4_2", "field5_2", "field6_2",
				"field7_2", "fieldB_2", "fieldC_2" };
		String[] s04bCola4 = new String[] { "field1_3", "field2_3", "field3_3",
				"field8_3", "field9_3", "field4_3", "field5_3", "field6_3",
				"field7_3", "fieldB_3", "fieldC_3" };
		String[] s04bColb1 = new String[] { "field1_1_1", "field2_1_1",
				"field3_1_1", "field8_1_1", "field9_1_1", "field4_1_1",
				"field5_1_1", "field6_1_1", "field7_1_1", "fieldB_1_1",
				"fieldC_1_1" };
		String[] s04bColb2 = new String[] { "field1_2_1", "field2_2_1",
				"field3_2_1", "field8_2_1", "field9_2_1", "field4_2_1",
				"field5_2_1", "field6_2_1", "field7_2_1", "fieldB_2_1",
				"fieldC_2_1" };
		String[] s04bColb3 = new String[] { "field1_3_1", "field2_3_1",
				"field3_3_1", "field8_3_1", "field9_3_1", "field4_3_1",
				"field5_3_1", "field6_3_1", "field7_3_1", "fieldB_3_1",
				"fieldC_3_1" };
		L120S04B l120s04b = new L120S04B();
		List<L120S04C> listL120s04c = new ArrayList<L120S04C>();
		// 進行設值關係戶於本行往來實績彙總表主檔
		l120s04b = setL120s04b(mainId, jsonData, s4aCol);
		// 進行設值關係戶於本行往來實績彙總表明細檔
		listL120s04c.add(setL120s04c(mainId, "1", jsonData, s04bCola1));
		listL120s04c.add(setL120s04c(mainId, "1", jsonData, s04bCola2));
		listL120s04c.add(setL120s04c(mainId, "1", jsonData, s04bCola3));
		listL120s04c.add(setL120s04c(mainId, "1", jsonData, s04bCola4));
		listL120s04c.add(setL120s04c(mainId, "2", jsonData, s04bColb1));
		listL120s04c.add(setL120s04c(mainId, "2", jsonData, s04bColb2));
		listL120s04c.add(setL120s04c(mainId, "2", jsonData, s04bColb3));
		// 進行儲存
		l120s04b.setDocKind("0");
		this.save(l120s04b);
		this.saveL120s04cList(listL120s04c);
	}

	private void saveL120S04E(String mainId, JSONObject jsonS04e) {

		List<L120S04E> listL120s04e = new ArrayList<L120S04E>();
		listL120s04e.add(setL120s04e(mainId, "1", "0", jsonS04e));
		listL120s04e.add(setL120s04e(mainId, "1", "1", jsonS04e));
		/*
		// 目前只需要借款人
		listL120s04e.add(setL120s04e(mainId, "2", "0", jsonS04e));
		listL120s04e.add(setL120s04e(mainId, "2", "1", jsonS04e));
		*/
		this.saveL120s04eList(listL120s04e);
	}

	/**
	 * 將JsonData資料設定到L120S04B裡
	 * 
	 * @param mainId
	 * @param docKind
	 * @param jsonData
	 * @param cols
	 * @return
	 */
	private L120S04B setL120s04b(String mainId, JSONObject jsonData,
			String[] cols) {
		L120S04B l120s04b = initL120s04b(mainId);
		for (String col : cols) {
			if (jsonData.containsKey(col)) {
				l120s04b = setColToModel(l120s04b, col, jsonData);
			}
		}
		return l120s04b;
	}

	/**
	 * 初始化關係戶於本行往來實績彙總表主檔
	 * 
	 * @param mainId
	 * @param docKind
	 * @return
	 */
	private L120S04B initL120s04b(String mainId) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Timestamp timeNow = CapDate.getCurrentTimestamp();
		L120S04B l120s04b = new L120S04B();
		l120s04b.setMainId(mainId);
		l120s04b.setGrpNo(null);
		l120s04b.setGrpName(null);
		l120s04b.setGrpYear(null);
		l120s04b.setGrpGrrd(null);
		l120s04b.setMainGrpDateS(null);
		l120s04b.setMainGrpDateE(null);
		l120s04b.setMainGrpAvgRate(BigDecimal.ZERO);
		l120s04b.setDepositDateS(null);
		l120s04b.setDepositDateE(null);
		l120s04b.setMegaAvgAmt(BigDecimal.ZERO);
		l120s04b.setDemandAmt(BigDecimal.ZERO);
		l120s04b.setDemandAvgRate(BigDecimal.ZERO);
		l120s04b.setCreateTime(timeNow);
		l120s04b.setCreator(user.getUserId());
		l120s04b.setUpdateTime(timeNow);
		l120s04b.setUpdater(user.getUserId());
		// J-107-0087-001 Web
		// e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。
		l120s04b.setGrpSize(null);
		l120s04b.setGrpLevel(null);
		// M-112-0140 「借戶暨關係戶與本行往來實績彙總表」下方各業務別利潤貢獻度，另以附註方式說明薪轉戶持有信用卡之貢獻度。
		l120s04b.setSalaryAcct(new Long("0"));
		l120s04b.setSalaryCardAcct(new Long("0"));
		l120s04b.setSalaryCardDateS(null);
		l120s04b.setSalaryCardDateE(null);
		l120s04b.setSalaryCardPcAmt(BigDecimal.ZERO);
		return l120s04b;
	}

	/**
	 * 將JsonData資料設定到L120S04C裡
	 * 
	 * @param mainId
	 * @param docKind
	 * @param jsonData
	 * @param cols
	 * @return
	 */
	private L120S04C setL120s04c(String mainId, String docKind,
			JSONObject jsonData, String[] cols) {
		L120S04C l120s04c = initL120s04c(mainId, docKind);
		for (String col : cols) {
			if (jsonData.containsKey(col)) {
				l120s04c = setColToModel(l120s04c, col, jsonData);
			}
		}
		return l120s04c;
	}

	/**
	 * 初始化關係戶於本行往來實績彙總表明細檔
	 * 
	 * @param mainId
	 * @param docKind
	 * @return
	 */
	private L120S04C initL120s04c(String mainId, String docKind) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Timestamp timeNow = CapDate.getCurrentTimestamp();
		L120S04C l120s04c = new L120S04C();
		l120s04c.setMainId(mainId);
		l120s04c.setDocKind(docKind);
		l120s04c.setDocDate(null);
		l120s04c.setDocDateE(null);
		l120s04c.setAvgDepositAmt(BigDecimal.ZERO);
		l120s04c.setAvgLoanAmt(BigDecimal.ZERO);
		l120s04c.setRcvBuyAvgAmt(BigDecimal.ZERO);
		l120s04c.setRcvSellAvgAmt(BigDecimal.ZERO);
		l120s04c.setExportAmt(BigDecimal.ZERO);
		l120s04c.setImportAmt(BigDecimal.ZERO);
		l120s04c.setProfitAmt(BigDecimal.ZERO);
		l120s04c.setProfitSalaryAmt(BigDecimal.ZERO);
		l120s04c.setProfitTrustFdtaAmt(BigDecimal.ZERO);
		l120s04c.setProfitRate(BigDecimal.ZERO);
		l120s04c.setCreateTime(timeNow);
		l120s04c.setCreator(user.getUserId());
		l120s04c.setUpdateTime(timeNow);
		l120s04c.setUpdater(user.getUserId());
		return l120s04c;
	}

	private L120S04E setL120s04e(String mainId, String docKind, String year,
		JSONObject jsonData) {
		L120S04E l120s04e = initL120s04e(mainId, docKind);

		// J-112-0147 配合企金處，「借戶暨關係戶與本行往來實績彙總表」下方各業務別利潤貢獻度欄位，增列「其他(信託等)」。
		String[] cols = new String[] { "docDate", "loanAmt", "depFxAmt",
				"wmAmt", "dervAmt", "salaryAmt", "cardAmt", "otherAmt", "totalAmt" };

		// 編碼規則：欄位名_year_docKind
		// year => 0：當年度    1：前一年度  2：前前年度 ...以此類推
		// docKind => 1：借款人  2：借款人暨關係戶
		//      ex.     loanAmt_0_1 => 借款人之當年度的授信_利潤貢獻金額

		for (String col : cols) {
			String tempCol = col + "_" + year + "_" + docKind;
			if(Util.equals(col, "docDate")){
				tempCol = col + "_" + year;
			}
			if (jsonData.containsKey(tempCol)) {
				if (Util.isNotEmpty(jsonData.get(tempCol))) {
					if (tempCol.contains("docDate")) {
						l120s04e.setDocDate(Util.trim(jsonData.get(tempCol)));
					} else if (tempCol.contains("loanAmt")) {
						l120s04e.setLoanPcAmt(new BigDecimal(Util.trim(jsonData.get(tempCol))));
					} else if (tempCol.contains("depFxAmt")) {
						l120s04e.setDepFxPcAmt(new BigDecimal(Util.trim(jsonData.get(tempCol))));
					} else if (tempCol.contains("wmAmt")) {
						l120s04e.setWmPcAmt(new BigDecimal(Util.trim(jsonData.get(tempCol))));
					} else if (tempCol.contains("dervAmt")) {
						l120s04e.setDervPcAmt(new BigDecimal(Util.trim(jsonData.get(tempCol))));
					} else if (tempCol.contains("salaryAmt")) {
						l120s04e.setSalaryPcAmt(new BigDecimal(Util.trim(jsonData.get(tempCol))));
					} else if (tempCol.contains("cardAmt")) {
						l120s04e.setCardPcAmt(new BigDecimal(Util.trim(jsonData.get(tempCol))));
					} else if (tempCol.contains("otherAmt")) {
						// J-112-0147 配合企金處，「借戶暨關係戶與本行往來實績彙總表」下方各業務別利潤貢獻度欄位，增列「其他(信託等)」。
						l120s04e.setOtherPcAmt(new BigDecimal(Util.trim(jsonData.get(tempCol))));
					} else if (tempCol.contains("totalAmt")) {
						l120s04e.setTotalPcAmt(new BigDecimal(Util.trim(jsonData.get(tempCol))));
					}
				}
			}
		}
		return l120s04e;
	}

	private L120S04E initL120s04e(String mainId, String docKind) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Timestamp timeNow = CapDate.getCurrentTimestamp();
		L120S04E l120s04e = new L120S04E();
		l120s04e.setMainId(mainId);
		l120s04e.setDocKind(docKind);
		l120s04e.setDocDate(null);
		l120s04e.setCreateTime(timeNow);
		l120s04e.setCreator(user.getUserId());
		l120s04e.setUpdateTime(timeNow);
		l120s04e.setUpdater(user.getUserId());
		l120s04e.setLoanPcAmt(BigDecimal.ZERO);
		l120s04e.setDepFxPcAmt(BigDecimal.ZERO);
		l120s04e.setWmPcAmt(BigDecimal.ZERO);
		l120s04e.setDervPcAmt(BigDecimal.ZERO);
		l120s04e.setSalaryPcAmt(BigDecimal.ZERO);
		l120s04e.setCardPcAmt(BigDecimal.ZERO);
		// J-112-0147 配合企金處，「借戶暨關係戶與本行往來實績彙總表」下方各業務別利潤貢獻度欄位，增列「其他(信託等)」。
		l120s04e.setOtherPcAmt(BigDecimal.ZERO);
		l120s04e.setTotalPcAmt(BigDecimal.ZERO);
		return l120s04e;
	}

	/**
	 * 依JsonData內欄位名稱將數值設定到對應欄位
	 * 
	 * @param model
	 * @param col
	 * @param jsonData
	 * @return
	 */
	private L120S04B setColToModel(L120S04B model, String col,
			JSONObject jsonData) {
		if (("GpID").equals(col)) {
			// 隸屬集團代號 grpNo
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setGrpNo(Util.trim(jsonData.get(col)));
			}
		} else if (("GpName").equals(col)) {
			// 隸屬集團 grpName
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setGrpName(Util.trim(jsonData.get(col)));
			}
		} else if (("GpDATAYY").equals(col)) {
			// 集團評等年度 grpYear
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setGrpYear(Util.parseInt(NumConverter.delCommaString(Util
						.trim(jsonData.get(col)))));
			}
		} else if (("GpGRADE").equals(col)) {
			// 集團評等 grpGrrd
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setGrpGrrd(Util.trim(jsonData.get(col)));
			}
		} else if (("Rate1_2").equals(col)) {
			// 主要集團企業最近一年起日 mainGrpDateS
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setMainGrpDateS(Util.trim(jsonData.get(col)));
			}
		} else if (("Rate1_3").equals(col)) {
			// 主要集團企業最近一年迄日 mainGrpDateE
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setMainGrpDateE(Util.trim(jsonData.get(col)));
			}
		} else if (("Rate1").equals(col)) {
			// 主要集團企業-平均餘額報酬率 mainGrpAvgRate
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setMainGrpAvgRate(("N.A.".equals(Util.trim(jsonData
						.get(col)))) ? null : new BigDecimal(Util.trim(jsonData
						.get(col))));
			}
		} else if (("Rate3_1").equals(col)) {
			// 借戶暨關係戶近半年起日 depositDateS
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setDepositDateS(Util.trim(jsonData.get(col)));
			}
		} else if (("Rate3_2").equals(col)) {
			// 借戶暨關係戶近半年迄日 depositDateE
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setDepositDateE(Util.trim(jsonData.get(col)));
			}
		} else if (("Rate3_3").equals(col)) {
			// 本行平均存款合計－金額 megaAvgAmt
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setMegaAvgAmt(new BigDecimal(Util.trim(jsonData.get(col))));
			}
		} else if (("Rate3_4").equals(col)) {
			// 活期性存款－金額 demandAmt
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setDemandAmt(new BigDecimal(Util.trim(jsonData.get(col))));
			}
		} else if (("Rate3_5").equals(col)) {
			// 活期性存款所占比率 demandAvgRate
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setDemandAvgRate(("N.A.".equals(Util.trim(jsonData
						.get(col)))) ? null : new BigDecimal(Util.trim(jsonData
						.get(col))));
			}
		} else if (("Rate4_2").equals(col)) {
			// 主借款人最近一年起日 mainGrpDateS
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setMainGrpDateMS(Util.trim(jsonData.get(col)));
			}
		} else if (("Rate4_3").equals(col)) {
			// 主借款人最近一年迄日 mainGrpDateE
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setMainGrpDateME(Util.trim(jsonData.get(col)));
			}
		} else if (("Rate4").equals(col)) {
			// 主借款人-平均餘額報酬率 mainGrpAvgRate
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setMainGrpAvgRateM(("N.A.".equals(Util.trim(jsonData
						.get(col)))) ? null : new BigDecimal(Util.trim(jsonData
						.get(col))));
			}
		} else if (("Rate5_2").equals(col)) {
			// 主借款人最近一年起日 mainGrpDateS
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setMainGrpDateRS(Util.trim(jsonData.get(col)));
			}
		} else if (("Rate5_3").equals(col)) {
			// 主借款人最近一年迄日 mainGrpDateE
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setMainGrpDateRE(Util.trim(jsonData.get(col)));
			}
		} else if (("Rate5").equals(col)) {
			// 主借款人-平均餘額報酬率 mainGrpAvgRate
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setMainGrpAvgRateR(("N.A.".equals(Util.trim(jsonData
						.get(col)))) ? null : new BigDecimal(Util.trim(jsonData
						.get(col))));
			}
			// J-107-0087-001 Web
			// e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級
		} else if (("GRPSIZE").equals(col)) {
			// 集團企業規模 grpSize
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setGrpSize(Util.trim(jsonData.get(col)));
			}
		} else if (("GRPLEVEL").equals(col)) {
			// 集團企業規模級別 grpLevel
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setGrpLevel(Util.trim(jsonData.get(col)));
			}
		} else if (("HINSDATE").equals(col)) {
			// 高利拆單_截至日期 hinsDate
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setHinsDate(Util.trim(jsonData.get(col)));
			}
		} else if (("HINSAMT").equals(col)) {
			// 高利拆單_共計金額(TWD千元) hinsAmt
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setHinsAmt(new BigDecimal(Util.trim(jsonData.get(col))));
			}
		} else if (("HINSRATE").equals(col)) {
			// 高利拆單_平均利率(%) hinsRate
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setHinsRate(new BigDecimal(Util.trim(jsonData.get(col))));
			}
		} else if (("HINSAVGAMT").equals(col)) {
			// 高利拆單_近半年平均金額(TWD千元) hinsAvgAmt
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setHinsAvgAmt(new BigDecimal(Util.trim(jsonData.get(col))));
			}
		} else if (("HINSAVGRATE").equals(col)) {
			// 高利拆單_近半年平均利率(%) hinsAvgRate
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setHinsAvgRate(new BigDecimal(Util.trim(jsonData.get(col))));
			}
		} else if (("salaryAcct").equals(col)) {
			// M-112-0140 「借戶暨關係戶與本行往來實績彙總表」下方各業務別利潤貢獻度，另以附註方式說明薪轉戶持有信用卡之貢獻度。
			// 前一年底薪轉戶數
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setSalaryAcct(new Long(Util.trim(jsonData.get(col))));
			}
		} else if (("salaryCardAcct").equals(col)) {
			// M-112-0140 「借戶暨關係戶與本行往來實績彙總表」下方各業務別利潤貢獻度，另以附註方式說明薪轉戶持有信用卡之貢獻度。
			// 前一年底薪轉戶-信用卡持卡人數
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setSalaryCardAcct(new Long(Util.trim(jsonData.get(col))));
			}
		} else if (("salaryCardDateS").equals(col)) {
			// M-112-0140 「借戶暨關係戶與本行往來實績彙總表」下方各業務別利潤貢獻度，另以附註方式說明薪轉戶持有信用卡之貢獻度。
			// 薪轉信用卡貢獻度起日
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setSalaryCardDateS(Util.trim(jsonData.get(col)));
			}
		} else if (("salaryCardDateE").equals(col)) {
			// M-112-0140 「借戶暨關係戶與本行往來實績彙總表」下方各業務別利潤貢獻度，另以附註方式說明薪轉戶持有信用卡之貢獻度。
			// 薪轉信用卡貢獻度迄日
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setSalaryCardDateE(Util.trim(jsonData.get(col)));
			}
		} else if (("salaryCardPcAmt").equals(col)) {
			// M-112-0140 「借戶暨關係戶與本行往來實績彙總表」下方各業務別利潤貢獻度，另以附註方式說明薪轉戶持有信用卡之貢獻度。
			// 薪轉信用卡_利潤貢獻
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setSalaryCardPcAmt(new BigDecimal(Util.trim(jsonData.get(col))));
			}
		} else if (("keyCustId").equals(col)) {
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setKeyCustId(Util.trim(jsonData.get(col)));
			}
		} else if (("keyDupNo").equals(col)) {
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setKeyDupNo(Util.trim(jsonData.get(col)));
			}
		}
		return model;
	}

	/**
	 * 依JsonData內欄位名稱將數值設定到對應欄位
	 * 
	 * @param model
	 * @param col
	 * @param jsonData
	 * @return
	 */
	private L120S04C setColToModel(L120S04C model, String col,
			JSONObject jsonData) {
		if (col.contains("field1")) {
			// 資料年月(起) docDateS 資料年月(迄) docDateE
			if (col.equals("field1_3") || col.equals("field1_3_1")) {
				// 101/1-7月
				if (Util.isNotEmpty(jsonData.get(col))) {
					String colDate = Util.trim(jsonData.get(col));
					model.setDocDate(colDate);
					// model.setDocDateE(CapDate.getDate(
					// colDate.substring(0, colDate.length() - 1) + "-01",
					// UtilConstants.DateFormat.YYYY_MM_DD));
				}
			} else {
				// YYYY 年
				if (Util.isNotEmpty(jsonData.get(col))) {
					String colDate = Util.trim(jsonData.get(col));
					model.setDocDate(colDate);
				}
			}
		} else if (col.contains("field2")) {
			// 平均存款－金額 avgDepositAmt
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setAvgDepositAmt(new BigDecimal(Util.trim(jsonData
						.get(col))));
			}
		} else if (col.contains("field3")) {
			// A平均授信－金額 avgLoanAmt
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setAvgLoanAmt(new BigDecimal(Util.trim(jsonData.get(col))));
			}
		} else if (col.contains("field8")) {
			// Ｂ應收帳款無追索買方承購平均餘額－金額 rcvBuyAvgAmt
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setRcvBuyAvgAmt(new BigDecimal(Util.trim(jsonData
						.get(col))));
			}
		} else if (col.contains("field9")) {
			// Ｃ應收帳款無追索權賣方融資平均餘額－金額 rcvSellAvgAmt
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setRcvSellAvgAmt(new BigDecimal(Util.trim(jsonData
						.get(col))));
			}
		} else if (col.contains("field4")) {
			// 進押及匯出－金額 exportAmt
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setExportAmt(new BigDecimal(Util.trim(jsonData.get(col))));
			}
		} else if (col.contains("field5")) {
			// 出押及匯入－金額 importAmt
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setImportAmt(new BigDecimal(Util.trim(jsonData.get(col))));
			}
		} else if (col.contains("field6")) {
			// Ｄ利潤貢獻－金額 profitAmt
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setProfitAmt(new BigDecimal(Util.trim(jsonData.get(col))));
			}
		} else if (col.contains("field7")) {
			// 報酬率 profitRate
			if (Util.isNotEmpty(jsonData.get(col))
					&& !"N.A.".equals(Util.trim(jsonData.get(col)))) {
				model.setProfitRate(new BigDecimal(Util.trim(jsonData.get(col))));
			}
		} else if (col.contains("fieldB")) {
			// 企業戶員工薪轉貢獻度
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setProfitSalaryAmt(new BigDecimal(Util.trim(jsonData
						.get(col))));
			}
		} else if (col.contains("fieldC")) {
			// 信託專戶利差
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setProfitTrustFdtaAmt(new BigDecimal(Util.trim(jsonData
						.get(col))));
			}
		}
		return model;
	}

	/**
	 * 取得集團信用評等及年度
	 * 
	 * @param gpId
	 *            集團代號
	 * @return
	 */
	private Map<String, Object> getMainGrpData(String gpId) {
		Map<String, Object> map = new HashMap<String, Object>();
		Map<String, Object> mapGrpYY = misGrpdtlService.findGrpdtl_selGrpyy();
		String grpYY = null;
		if (!mapGrpYY.isEmpty()) {
			grpYY = Util.trim(mapGrpYY.get("GRPYY"));
			map.put("tDATAYY", grpYY);
		}

		// J-107-0087-001 Web
		// e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。
		boolean newGrpGrade = lmsService.isNewGrpGrade(grpYY, false);
		String defultNoGrade = lmsService.getGrpNoGrade(newGrpGrade);

		if (Util.isEmpty(grpYY)) {
			map.put("tDATAYY", "");
			// J-107-0087-001 Web
			// e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。
			map.put("tGRADE", defultNoGrade);
			map.put("tGRPSIZE", "");
			map.put("tGRPLEVEL", "");
		}

		List<Map<String, Object>> mapGrpGrades = misGrpcmpService
				.findGrpcmpSelGrpGrade(gpId, grpYY);
		if (!mapGrpGrades.isEmpty()) {
			for (Map<String, Object> mapGrpGrade : mapGrpGrades) {
				String value = Util.trim(mapGrpGrade.get("GRADE"));

				// J-107-0087-001 Web
				// e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。
				String size = MapUtils.getString(mapGrpGrade, "GRPSIZE", "");
				String level = MapUtils.getString(mapGrpGrade, "GRPLEVEL", "");
				if (Util.isEmpty(value)) {
					value = defultNoGrade;
				}
				map.put("tGRADE", value);

				// J-107-0087-001 Web
				// e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。
				map.put("tGRPSIZE", size);
				map.put("tGRPLEVEL", level);
			}
		} else {
			map.put("tGRADE", defultNoGrade);

			// J-107-0087-001 Web
			// e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。
			map.put("tGRPSIZE", "");
			map.put("tGRPLEVEL", "");
		}
		return map;
	}

	@Override
	public void setTotal(String mainId, Map<String, Long> map, JSONObject json) {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS07APanel.class);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<L120S04A> listL120s04a = this.findL120s04aByMainId(mainId);
		// 檢查是否已有計算好的集團合計或關係合計
		if (listL120s04a != null) {
			for (L120S04A model : listL120s04a) {
				if ("3".equals(model.getCustRelation())
						|| "4".equals(model.getCustRelation())) {
					// 如果有則刪除
					this.delete(model);
				}
			}
		}
		List<Object[]> list = l120s04aDao.findL120s04a(mainId);
		List<Object[]> list2 = l120s04aDao.findL120s04a2(mainId);
		// 集團合計
		L120S04A l120s04a1 = new L120S04A();
		l120s04a1.setMainId(mainId);
		l120s04a1.setCreateTime(CapDate.getCurrentTimestamp());
		l120s04a1.setCreator(user.getUserId());
		l120s04a1.setCustRelation("3");
		l120s04a1.setPrtFlag("1");
		l120s04a1.setCustId("");
		l120s04a1.setCustName(pop.getProperty("L1205S07.index5"));
		// 關係合計
		L120S04A l120s04a2 = new L120S04A();
		l120s04a2.setMainId(mainId);
		l120s04a2.setCreateTime(CapDate.getCurrentTimestamp());
		l120s04a2.setCreator(user.getUserId());
		l120s04a2.setCustRelation("4");
		l120s04a2.setPrtFlag("1");
		l120s04a2.setCustId("");
		l120s04a2.setCustName(pop.getProperty("L1205S07.index4"));
		if (list != null) {
			// 找到資料時(計算後有值)
			for (Object[] row : list) {
				// 貢獻度(集團)
				Long tprofit = (Long) row[34];
				// 放款額度(集團)
				Long tloanQuota = (Long) row[2];
				// 放款餘額(集團)
				Long tloanAvgBal = (Long) row[3];
				// 活期存款(集團)
				Long tdepTime = (Long) row[0];

				// 開始設定集團合計

				l120s04a1.setDepTime((Long) row[0]);
				l120s04a1.setDepFixed((Long) row[1]);
				l120s04a1.setLoanQuota((Long) row[2]);
				l120s04a1.setLoanAvgBal((Long) row[3]);
				// l120s04a1.setLoanAvgRate(0.0);
				l120s04a1
						.setExchgImpRec(new Integer(((Long) row[4]).toString()));
				l120s04a1.setExchgImpAmt((Long) row[5]);
				l120s04a1
						.setExchgExpRec(new Integer(((Long) row[6]).toString()));
				l120s04a1.setExchgExpAmt((Long) row[7]);
				l120s04a1
						.setExchgOutRec(new Integer(((Long) row[8]).toString()));
				l120s04a1.setExchgOutAmt((Long) row[9]);
				l120s04a1
						.setExchgInRec(new Integer(((Long) row[10]).toString()));
				l120s04a1.setExchgInAmt((Long) row[11]);
				l120s04a1.setDerOption((Long) row[12]);
				l120s04a1.setDerRateExchg((Long) row[13]);
				l120s04a1.setDerCCS((Long) row[14]);
				l120s04a1.setDerSWAP((Long) row[15]);
				l120s04a1.setTrustBond((Long) row[16]);
				l120s04a1.setTrustFund((Long) row[17]);
				l120s04a1.setTrustSetAcct((Long) row[18]);
				l120s04a1.setTrustOther((Long) row[19]);
				l120s04a1.setWealthTrust((Long) row[20]);
				l120s04a1.setWealthInsCom((Long) row[21]);
				l120s04a1.setWealthInvest((Long) row[22]);
				l120s04a1.setSalaryRec((Long) row[23]);
				l120s04a1.setSalaryFixed((Long) row[24]);
				l120s04a1.setSalaryMortgage((Long) row[25]);
				l120s04a1.setSalaryConsumption((Long) row[26]);
				l120s04a1.setSalaryCard((Long) row[27]);
				l120s04a1.setSalaryNetwork((Long) row[28]);
				l120s04a1.setCardCommercial((Long) row[29]);
				l120s04a1.setCardCoBranded((String) row[30]);
				l120s04a1.setGEBTWDRec((Long) row[31]);
				l120s04a1.setGEBOTHRec((Long) row[32]);
				l120s04a1.setGEBLCRec((Long) row[33]);
				l120s04a1.setProfit((Long) row[34]);
				l120s04a1.setCardNoneCommercial((Long) row[35]);
				l120s04a1.setProfitSalary((Long) row[36]);
				l120s04a1.setProfitTrustFdta((Long) row[37]);
				// M-104-0172-001 二維表收信新增AR買方額度餘額
				l120s04a1.setRcvBuyFactAmt((Long) row[38]);
				l120s04a1.setRcvBuyAvgBal((Long) row[39]);

				// 將計算好結果存到Map裡以傳到前端
				map.put("profit01", tprofit);
				map.put("loanQuota01", tloanQuota);
				map.put("loanAvgBal01", tloanAvgBal);
				map.put("depTime01", tdepTime);
			}
		} else {
			// 找不到資料時(即計算後為空值)
			// 開始設定集團合計
			l120s04a1.setDepTime(new Long("0"));
			l120s04a1.setDepFixed(new Long("0"));
			l120s04a1.setLoanQuota(new Long("0"));
			l120s04a1.setLoanAvgBal(new Long("0"));
			// l120s04a1.setLoanAvgRate(0.0);
			l120s04a1.setExchgImpRec(new Integer("0"));
			l120s04a1.setExchgImpAmt(new Long("0"));
			l120s04a1.setExchgExpRec(new Integer("0"));
			l120s04a1.setExchgExpAmt(new Long("0"));
			l120s04a1.setExchgOutRec(new Integer("0"));
			l120s04a1.setExchgOutAmt(new Long("0"));
			l120s04a1.setExchgInRec(new Integer("0"));
			l120s04a1.setExchgInAmt(new Long("0"));
			l120s04a1.setDerOption(new Long("0"));
			l120s04a1.setDerRateExchg(new Long("0"));
			l120s04a1.setDerCCS(new Long("0"));
			l120s04a1.setDerSWAP(new Long("0"));
			l120s04a1.setTrustBond(new Long("0"));
			l120s04a1.setTrustFund(new Long("0"));
			l120s04a1.setTrustSetAcct(new Long("0"));
			l120s04a1.setTrustOther(new Long("0"));
			l120s04a1.setWealthTrust(new Long("0"));
			l120s04a1.setWealthInsCom(new Long("0"));
			l120s04a1.setWealthInvest(new Long("0"));
			l120s04a1.setSalaryRec(new Long("0"));
			l120s04a1.setSalaryFixed(new Long("0"));
			l120s04a1.setSalaryMortgage(new Long("0"));
			l120s04a1.setSalaryConsumption(new Long("0"));
			l120s04a1.setSalaryCard(new Long("0"));
			l120s04a1.setSalaryNetwork(new Long("0"));
			l120s04a1.setCardCommercial(new Long("0"));
			l120s04a1.setCardCoBranded(new String("N"));
			l120s04a1.setGEBTWDRec(new Long("0"));
			l120s04a1.setGEBOTHRec(new Long("0"));
			l120s04a1.setGEBLCRec(new Long("0"));
			l120s04a1.setProfit(new Long("0"));
			l120s04a1.setCardNoneCommercial(new Long("0"));
			l120s04a1.setProfitSalary(new Long("0"));
			l120s04a1.setProfitTrustFdta(new Long("0"));
			// M-104-0172-001 二維表收信新增AR買方額度餘額
			l120s04a1.setRcvBuyFactAmt(new Long("0"));
			l120s04a1.setRcvBuyAvgBal(new Long("0"));

			// 將計算好結果存到Map裡以傳到前端
			map.put("profit01", new Long("0"));
			map.put("loanQuota01", new Long("0"));
			map.put("loanAvgBal01", new Long("0"));
			map.put("depTime01", new Long("0"));
		}
		if (list2 != null) {
			for (Object[] row : list2) {

				// 貢獻度(集團)
				Long tprofit2 = (Long) row[34];
				// 放款額度(集團)
				Long tloanQuota2 = (Long) row[2];
				// 放款餘額(集團)
				Long tloanAvgBal2 = (Long) row[3];
				// 活期存款(集團)
				Long tdepTime2 = (Long) row[0];

				// 開始設定關係合計
				l120s04a2.setDepTime((Long) row[0]);
				l120s04a2.setDepFixed((Long) row[1]);
				l120s04a2.setLoanQuota((Long) row[2]);
				l120s04a2.setLoanAvgBal((Long) row[3]);
				// l120s04a2.setLoanAvgRate(0.0);
				l120s04a2
						.setExchgImpRec(new Integer(((Long) row[4]).toString()));
				l120s04a2.setExchgImpAmt((Long) row[5]);
				l120s04a2
						.setExchgExpRec(new Integer(((Long) row[6]).toString()));
				l120s04a2.setExchgExpAmt((Long) row[7]);
				l120s04a2
						.setExchgOutRec(new Integer(((Long) row[8]).toString()));
				l120s04a2.setExchgOutAmt((Long) row[9]);
				l120s04a2
						.setExchgInRec(new Integer(((Long) row[10]).toString()));
				l120s04a2.setExchgInAmt((Long) row[11]);
				l120s04a2.setDerOption((Long) row[12]);
				l120s04a2.setDerRateExchg((Long) row[13]);
				l120s04a2.setDerCCS((Long) row[14]);
				l120s04a2.setDerSWAP((Long) row[15]);
				l120s04a2.setTrustBond((Long) row[16]);
				l120s04a2.setTrustFund((Long) row[17]);
				l120s04a2.setTrustSetAcct((Long) row[18]);
				l120s04a2.setTrustOther((Long) row[19]);
				l120s04a2.setWealthTrust((Long) row[20]);
				l120s04a2.setWealthInsCom((Long) row[21]);
				l120s04a2.setWealthInvest((Long) row[22]);
				l120s04a2.setSalaryRec((Long) row[23]);
				l120s04a2.setSalaryFixed((Long) row[24]);
				l120s04a2.setSalaryMortgage((Long) row[25]);
				l120s04a2.setSalaryConsumption((Long) row[26]);
				l120s04a2.setSalaryCard((Long) row[27]);
				l120s04a2.setSalaryNetwork((Long) row[28]);
				l120s04a2.setCardCommercial((Long) row[29]);
				l120s04a2.setCardCoBranded((String) row[30]);
				l120s04a2.setGEBTWDRec((Long) row[31]);
				l120s04a2.setGEBOTHRec((Long) row[32]);
				l120s04a2.setGEBLCRec((Long) row[33]);
				l120s04a2.setProfit((Long) row[34]);
				l120s04a2.setCardNoneCommercial((Long) row[35]);
				l120s04a2.setProfitSalary((Long) row[36]);
				l120s04a2.setProfitTrustFdta((Long) row[37]);

				// M-104-0172-001 二維表收信新增AR買方額度餘額
				l120s04a2.setRcvBuyFactAmt((Long) row[38]);
				l120s04a2.setRcvBuyAvgBal((Long) row[39]);

				// 將計算好結果存到Map裡以傳到前端
				map.put("profit02", tprofit2);
				map.put("loanQuota02", tloanQuota2);
				map.put("loanAvgBal02", tloanAvgBal2);
				map.put("depTime02", tdepTime2);
			}
		} else {
			// 開始設定關係合計
			l120s04a2.setDepTime(new Long("0"));
			l120s04a2.setDepFixed(new Long("0"));
			l120s04a2.setLoanQuota(new Long("0"));
			l120s04a2.setLoanAvgBal(new Long("0"));
			// l120s04a2.setLoanAvgRate(0.0);
			l120s04a2.setExchgImpRec(new Integer("0"));
			l120s04a2.setExchgImpAmt(new Long("0"));
			l120s04a2.setExchgExpRec(new Integer("0"));
			l120s04a2.setExchgExpAmt(new Long("0"));
			l120s04a2.setExchgOutRec(new Integer("0"));
			l120s04a2.setExchgOutAmt(new Long("0"));
			l120s04a2.setExchgInRec(new Integer("0"));
			l120s04a2.setExchgInAmt(new Long("0"));
			l120s04a2.setDerOption(new Long("0"));
			l120s04a2.setDerRateExchg(new Long("0"));
			l120s04a2.setDerCCS(new Long("0"));
			l120s04a2.setDerSWAP(new Long("0"));
			l120s04a2.setTrustBond(new Long("0"));
			l120s04a2.setTrustFund(new Long("0"));
			l120s04a2.setTrustSetAcct(new Long("0"));
			l120s04a2.setTrustOther(new Long("0"));
			l120s04a2.setWealthTrust(new Long("0"));
			l120s04a2.setWealthInsCom(new Long("0"));
			l120s04a2.setWealthInvest(new Long("0"));
			l120s04a2.setSalaryRec(new Long("0"));
			l120s04a2.setSalaryFixed(new Long("0"));
			l120s04a2.setSalaryMortgage(new Long("0"));
			l120s04a2.setSalaryConsumption(new Long("0"));
			l120s04a2.setSalaryCard(new Long("0"));
			l120s04a2.setSalaryNetwork(new Long("0"));
			l120s04a2.setCardCommercial(new Long("0"));
			l120s04a2.setCardCoBranded(new String("N"));
			l120s04a2.setGEBTWDRec(new Long("0"));
			l120s04a2.setGEBOTHRec(new Long("0"));
			l120s04a2.setGEBLCRec(new Long("0"));
			l120s04a2.setProfit(new Long("0"));
			l120s04a2.setCardNoneCommercial(new Long("0"));
			l120s04a2.setProfitSalary(new Long("0"));
			l120s04a2.setProfitTrustFdta(new Long("0"));

			// M-104-0172-001 二維表收信新增AR買方額度餘額
			l120s04a2.setRcvBuyFactAmt(new Long("0"));
			l120s04a2.setRcvBuyAvgBal(new Long("0"));

			// 將計算好結果存到Map裡以傳到前端
			map.put("profit02", new Long("0"));
			map.put("loanQuota02", new Long("0"));
			map.put("loanAvgBal02", new Long("0"));
			map.put("depTime02", new Long("0"));
		}
		// L120M01A model = l120m01aDao.findByMainId(mainId);
		String queryDateS = Util.nullToSpace(json.get("queryDateS"));
		String queryDateE = Util.nullToSpace(json.get("queryDateE"));
		if (l120s04a1 != null) {
			l120s04a1.setChkYN("Y");
			if (!Util.isEmpty(queryDateS)) {
				if (queryDateS.length() == 7) {
					queryDateS = queryDateS + "-01";
				}
				l120s04a1.setQueryDateS(Util.parseDate(Util.trim(queryDateS)));
			} else {
				l120s04a1.setQueryDateS(null);
			}
			if (!Util.isEmpty(queryDateE)) {
				if (queryDateE.length() == 7) {
					queryDateE = queryDateE + "-01";
				}
				l120s04a1.setQueryDateE(Util.parseDate(Util.trim(queryDateE)));
			} else {
				l120s04a1.setQueryDateE(null);
			}
		}
		if (l120s04a2 != null) {
			l120s04a2.setChkYN("Y");
			if (!Util.isEmpty(queryDateS)) {
				l120s04a2.setQueryDateS(Util.parseDate(Util.trim(queryDateS)));
			} else {
				l120s04a2.setQueryDateS(null);
			}
			if (!Util.isEmpty(Util.nullToSpace(json.get("queryDateE")))) {
				l120s04a2.setQueryDateE(Util.parseDate(Util.trim(queryDateE)));
			} else {
				l120s04a2.setQueryDateE(null);
			}
		}
		this.save(l120s04a1, l120s04a2);
	}

	/**
	 * 取得完整Id(統編加重覆序號)
	 * 
	 * @param custid
	 * @param dupNo
	 * @return
	 */
	private String getAllCust(String custid, String dupNo) {
		StringBuilder strb = new StringBuilder();
		if ("0".equals(dupNo)) {
			dupNo = "";
		}
		return strb.append(custid).append(dupNo).toString();
	}

	/**
	 * 初始化L120S04A(設定文件建立者以及建立時間和MainId)
	 * 
	 * @param l120s04a
	 */
	private void defaultL120s04a(L120S04A l120s04a, String MainId,
			String queryDateS, String queryDateE) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		l120s04a.setMainId(MainId);
		l120s04a.setCreator(user.getUserId());
		l120s04a.setCreateTime(CapDate.getCurrentTimestamp());
		l120s04a.setQueryDateS(Util.parseDate(queryDateS));
		l120s04a.setQueryDateE(Util.parseDate(queryDateE));
		l120s04a.setCreateBY("SYS");
		l120s04a.setPrtFlag("1");
	}

	@Override
	public int checkDate(String queryDateS, String queryDateE, JSONObject json) {
		List<?> rows = this.dwdbService.findDWADM_MD_CUPFM_OTS_selDate();
		Iterator<?> it = rows.iterator();
		String MAX_CYC_MN = "";
		String MIN_CYC_MN = "";
		if (it.hasNext()) {
			Map<?, ?> dataMap = (Map<?, ?>) it.next();
			MAX_CYC_MN = Util.trim(Util.nullToSpace(dataMap.get("MAX_CYC_MN")));
			MIN_CYC_MN = Util.trim(Util.nullToSpace(dataMap.get("MIN_CYC_MN")));
			json.put("MAX_CYC_MN", MAX_CYC_MN);
			json.put("MIN_CYC_MN", MIN_CYC_MN);
			// compareTo用法
			// 使用者輸入資料小於資料查詢-> >0
			// 使用者輸入資料大於資料查詢-> <0
			// 使用者輸入資料等於資料查詢-> =0
			if (CapDate.parseDate(MIN_CYC_MN).compareTo(
					CapDate.parseDate(queryDateS)) > 0
					// || CapDate.parseDate(MIN_CYC_MN).compareTo(
					// CapDate.parseDate(queryDateS)) < 0
					|| "".equals(MIN_CYC_MN)) {
				// if (CapDate.parseDate(MIN_CYC_MN).compareTo(
				// CapDate.parseDate(queryDateS)) < 0) {
				// return 1;
				// }
				// else
				// 當使用者輸入起日小於資料查詢起日
				if (CapDate.parseDate(MIN_CYC_MN).compareTo(
						CapDate.parseDate(queryDateS)) > 0) {
					return 2;
				} else {
					return 3;
				}
			}
			if (CapDate.parseDate(MAX_CYC_MN).compareTo(
					CapDate.parseDate(queryDateE)) < 0
					// || CapDate.parseDate(MAX_CYC_MN).compareTo(
					// CapDate.parseDate(queryDateE)) > 0
					|| "".equals(MAX_CYC_MN)) {
				// 當使用者輸入迄日大於資料查詢迄日
				if (CapDate.parseDate(MAX_CYC_MN).compareTo(
						CapDate.parseDate(queryDateE)) < 0) {
					return 4;
				}
				// if (CapDate.parseDate(MAX_CYC_MN).compareTo(
				// CapDate.parseDate(queryDateE)) > 0) {
				// return 5;
				// }
				else {
					return 6;
				}
			}
		}
		return 0;
	}

	@SuppressWarnings({ "unused", "rawtypes" })
	@Override
	public List<L120S04A> findL120s04a(String mainId, String custId,
			String dupNo, String custName, String queryDateS, String queryDateE) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		L120M01A meta = l120m01aDao.findByMainId(mainId);
		// 依目前簽案行做計算幣別
		BranchRate branchRate = lmsService.getBranchRate(user.getUnitNo());
		List<L120S04A> list = new ArrayList<L120S04A>();
		Map<String, L120S04A> map = new HashMap<String, L120S04A>();
		List<String> listCname = new ArrayList<String>();
		L120S04A l120s04a = new L120S04A();
		defaultL120s04a(l120s04a, mainId, queryDateS, queryDateE);
		List<?> rows1 = this.misCustdataService.findCustdataForList(custId,
				dupNo);
		Iterator<?> it1 = rows1.iterator();
		// 負責人統編
		String manageId = custId;
		// 負責人重覆序號
		String manageDup = dupNo;
		// 負責人名稱
		String manageName = custName;
		// 活期存款
		long depTime = 0;
		// 定期存款
		long depFixed = 0;
		// 額度
		long loanQuota = 0;
		// 平均餘額
		long loanAvgBal = 0;
		// 平均動用率
		BigDecimal loanAvgRate = new BigDecimal("0");
		// 進口筆數
		long exchgImpRec = 0;
		// 進口金額
		long exchgImpAmt = 0;
		// 出口筆數
		long exchgExpRec = 0;
		// 出口金額
		long exchgExpAmt = 0;
		// 匯出筆數
		long exchgOutRec = 0;
		// 匯出金額
		long exchgOutAmt = 0;
		// 匯入筆數
		long exchgInRec = 0;
		// 匯入金額
		long exchgInAmt = 0;
		// 選擇權
		long derOption = 0;
		// 利率交換
		long derRateExchg = 0;
		// 換匯換利
		long derCCS = 0;
		// 遠匯
		String derDraft = "0";
		// 遠匯(含SWAP)
		long derSWAP = 0;
		// 國內外基金債券
		long trustBond = 0;
		// 基金保管
		long trustFund = 0;
		// 集管
		long trustSetAcct = 0;
		// 有價證券信託
		String trustSecurities = "0";
		// 不動產信託
		String trustREITs = "0";
		// 福儲信託
		String trustWelDep = "0";
		// 其他信託
		long trustOther = 0;
		// 信託
		long wealthTrust = 0;
		// 保險佣金
		long wealthInsCom = 0;
		// 雙元投資
		long wealthInvest = 0;
		// 薪轉戶數
		long salaryRec = 0;
		// 定期定額戶數
		long salaryFixed = 0;
		// 房貸戶數
		long salaryMortgage = 0;
		// 消貸戶數
		long salaryConsumption = 0;
		// 信用卡持卡人數
		long salaryCard = 0;
		// 個人網銀戶數
		long salaryNetwork = 0;
		// 商務卡
		long cardCommercial = 0;
		// 非商務卡
		long cardNoneCommercial = 0;
		// 台幣交易筆數
		long GEBTWDRec = 0;
		// 外幣交易筆數
		long GEBOTHRec = 0;
		// 信用狀交易筆數
		long GEBLCRec = 0;
		// 利潤貢獻度
		String profit = "0";
		// 活期存款
		String depTime1 = "0";
		// 定期存款
		String depFixed1 = "0";
		// 額度
		String loanQuota1 = "0";
		// 平均餘額
		String loanAvgBal1 = "0";
		// 平均動用率
		String loanAvgRate1 = "0";
		// 進口筆數
		String exchgImpRec1 = "0";
		// 進口金額
		String exchgImpAmt1 = "0";
		// 出口筆數
		String exchgExpRec1 = "0";
		// 出口金額
		String exchgExpAmt1 = "0";
		// 匯出筆數
		String exchgOutRec1 = "0";
		// 匯出金額
		String exchgOutAmt1 = "0";
		// 匯入筆數
		String exchgInRec1 = "0";
		// 匯入金額
		String exchgInAmt1 = "0";
		// 選擇權
		String derOption1 = "0";
		// 利率交換
		String derRateExchg1 = "0";
		// 換匯換利
		String derCCS1 = "0";
		// 遠匯
		String derDraft1 = "0";
		// 遠匯(含SWAP)
		String derSWAP1 = "0";
		// 國內外基金債券
		String trustBond1 = "0";
		// 基金保管
		String trustFund1 = "0";
		// 集管
		String trustSetAcct1 = "0";
		// 有價證券信託
		String trustSecurities1 = "0";
		// 不動產信託
		String trustREITs1 = "0";
		// 福儲信託
		String trustWelDep1 = "0";
		// 其他信託
		String trustOther1 = "0";
		// 信託
		String wealthTrust1 = "0";
		// 保險佣金
		String wealthInsCom1 = "0";
		// 雙元投資
		String wealthInvest1 = "0";
		// 薪轉戶數
		String salaryRec1 = "0";
		// 定期定額戶數
		String salaryFixed1 = "0";
		// 房貸戶數
		String salaryMortgage1 = "0";
		// 消貸戶數
		String salaryConsumption1 = "0";
		// 信用卡持卡人數
		String salaryCard1 = "0";
		// 個人網銀戶數
		String salaryNetwork1 = "0";
		// 商務卡
		String cardCommercial1 = "0";
		// 非商務卡
		String cardNoneCommercial1 = "0";
		// 聯名卡
		String cardCoBranded1 = "N";
		// 台幣交易筆數
		String GEBTWDRec1 = "0";
		// 外幣交易筆數
		String GEBOTHRec1 = "0";
		// 信用狀交易筆數
		String GEBLCRec1 = "0";
		// 利潤貢獻度
		String profit1 = "0";

		// M-104-0172-001 二維表收信新增AR買方額度餘額
		String IN_LN_FA_B = "0";
		String IN_LN_FA_S = "0";
		long in_ln_fa_b = 0;
		long in_ln_fa_s = 0;
		String IN_LN_FACT_AMT_FA_S = "0";
		String IN_LN_FACT_AMT_FA_B = "0";
		long in_ln_fact_amt_fa_b = 0;
		long in_ln_fact_amt_fa_s = 0;

		String IN_CC_CC_ACT = "";
		String BR_CD = "";

		l120s04a.setCustId(manageId);
		l120s04a.setDupNo(manageDup);
		l120s04a.setCustName(manageName);
		l120s04a.setCustRelation("1");
		map.put(getAllCust(manageId, manageDup), l120s04a);
		listCname.add(getAllCust(manageId, manageDup));
		if (it1.hasNext()) {
			l120s04a = new L120S04A();
			defaultL120s04a(l120s04a, mainId, queryDateS, queryDateE);
			Map<?, ?> dataMap1 = (Map<?, ?>) it1.next();
			manageId = Util.trim(Util.nullToSpace(dataMap1.get("SUP1ID")));
			manageDup = Util.trim(Util.nullToSpace(dataMap1.get("SUP1DUPNO")));
			if ("".equals(manageDup)) {
				// 重覆序號為空則設為0
				manageDup = "0";
			}
			manageName = Util.trim(Util.nullToSpace(dataMap1.get("SUP1CNM")));
			if (!Util.isEmpty(manageId) && !Util.isEmpty(manageDup)) {
				l120s04a.setCustId(manageId);
				l120s04a.setDupNo(manageDup);
				l120s04a.setCustName(manageName);
				l120s04a.setCustRelation("2");
				map.put(getAllCust(manageId, manageDup), l120s04a);
				listCname.add(getAllCust(manageId, manageDup));
			}
		}
		// 集團代號
		String gid = "";
		// 集團名稱
		String gname = "";
		List<?> rows2 = this.misGrpcmpService.findGrpcmpForGrpid(custId, dupNo);
		Iterator<?> it2 = rows2.iterator();
		while (it2.hasNext()) {
			Map<?, ?> dataMap2 = (Map<?, ?>) it2.next();
			gid = Util.trim(Util.nullToSpace(dataMap2.get("GRPID")));
		}

		if (!"".equals(gid)) {
			List<?> rows3 = this.misGrpdtlService.findGrpdtlForGrpnm(gid);
			Iterator<?> it3 = rows3.iterator();
			while (it3.hasNext()) {
				Map<?, ?> dataMap3 = (Map<?, ?>) it3.next();
				gname = Util.trim(Util.nullToSpace(dataMap3.get("GRPNM")));
			}

			// 讀取集團明細 .......
			List<?> rows4 = this.misGrpcmpService.findGrpcmpForCmpnm(gid);
			Iterator<?> it4 = rows4.iterator();
			while (it4.hasNext()) {
				Map<?, ?> dataMap4 = (Map<?, ?>) it4.next();
				manageId = Util.trim(Util.nullToSpace(dataMap4.get("BAN")));
				manageDup = Util.trim(Util.nullToSpace(dataMap4.get("DUPNO")));
				manageName = Util.toSemiCharString(Util.trim(String
						.valueOf(dataMap4.get("CMPNM"))));
				l120s04a = map.get(getAllCust(manageId, manageDup));
				// l120s04a = this.findL120s04aByUniqueKey(mainId, manageId,
				// manageDup, manageName);
				if (l120s04a == null) {
					l120s04a = new L120S04A();
					defaultL120s04a(l120s04a, mainId, queryDateS, queryDateE);
				}
				l120s04a.setCustId(manageId);
				l120s04a.setDupNo(manageDup);
				l120s04a.setCustName(manageName);
				if (Util.isEmpty(Util.trim(l120s04a.getCustRelation()))) {
					l120s04a.setCustRelation("5");
				} else {
					if (l120s04a.getCustRelation().indexOf("5") == -1) {
						l120s04a.setCustRelation(l120s04a.getCustRelation()
								+ ",5");
					}
				}
				map.put(getAllCust(manageId, manageDup), l120s04a);
				listCname.add(getAllCust(manageId, manageDup));
			}
		}
		List<?> rows5 = this.misElcrcoService.findElcrecomByIdDupno2(custId,
				dupNo);
		Iterator<?> it5 = rows5.iterator();
		while (it5.hasNext()) {
			Map<?, ?> dataMap5 = (Map<?, ?>) it5.next();
			manageId = Util.trim(Util.nullToSpace(dataMap5.get("BAN")));
			manageDup = Util.trim(Util.nullToSpace(dataMap5.get("DUPNO")));
			manageName = Util.toSemiCharString(Util.trim(String
					.valueOf(dataMap5.get("CNAME"))));
			l120s04a = map.get(getAllCust(manageId, manageDup));
			// l120s04a = this.findL120s04aByUniqueKey(mainId, manageId,
			// manageDup, manageName);
			if (l120s04a == null) {
				l120s04a = new L120S04A();
				defaultL120s04a(l120s04a, mainId, queryDateS, queryDateE);
			}
			l120s04a.setCustId(manageId);
			l120s04a.setDupNo(manageDup);
			l120s04a.setCustName(manageName);
			if (Util.isEmpty(Util.trim(l120s04a.getCustRelation()))) {
				l120s04a.setCustRelation("6");
			} else {
				if (l120s04a.getCustRelation().indexOf("6") == -1) {
					l120s04a.setCustRelation(l120s04a.getCustRelation() + ",6");
				}
			}
			// l120s04a.setCustRelation("6");
			map.put(getAllCust(manageId, manageDup), l120s04a);
			listCname.add(getAllCust(manageId, manageDup));
		}
		for (String x : listCname) {
			L120S04A model = map.get(x);
			manageId = model.getCustId();
			manageDup = model.getDupNo();
			List<?> rows6 = this.dwdbService.findDW_MD_CUPFM_OTS_selCYC_MN(
					manageId, manageDup, queryDateS, queryDateE);
			Iterator<?> it6 = rows6.iterator();
			// 聯名卡
			String cardCoBranded = "N";
			while (it6.hasNext()) {
				l120s04a = new L120S04A();
				defaultL120s04a(l120s04a, mainId, queryDateS, queryDateE);
				Map<?, ?> dataMap6 = (Map<?, ?>) it6.next();
				String date = CapDate.formatDate(CapDate.parseDate(String
						.valueOf(dataMap6.get("CYC_MN"))), "yyyy-MM-dd");
				dataMap6.get("CUST_KEY");
				BR_CD = Util.nullToSpace(dataMap6.get("BR_CD")); // 999代表全行
				depTime1 = Util.nullToSpace(dataMap6.get("IN_DP"));
				dataMap6.get("IN_DP_G");
				depFixed1 = Util.nullToSpace(dataMap6.get("IN_CT"));
				loanAvgBal1 = Util.nullToSpace(dataMap6.get("IN_LN_USE"));
				loanAvgRate1 = Util.nullToSpace(dataMap6.get("IN_LN_AVGRT"));
				loanQuota1 = Util.nullToSpace(dataMap6.get("IN_LN_FACT_AMT"));
				exchgImpAmt1 = Util.nullToSpace(dataMap6.get("IN_IM"));
				exchgExpAmt1 = Util.nullToSpace(dataMap6.get("IN_EX_BP"));
				exchgImpRec1 = Util.nullToSpace(dataMap6.get("IN_IM_TXN"));
				exchgExpRec1 = Util.nullToSpace(dataMap6.get("IN_EX_TXN"));
				exchgOutAmt1 = Util.nullToSpace(dataMap6.get("IN_OR"));
				exchgInAmt1 = Util.nullToSpace(dataMap6.get("IN_IR"));
				exchgOutRec1 = Util.nullToSpace(dataMap6.get("IN_OR_TXN"));
				exchgInRec1 = Util.nullToSpace(dataMap6.get("IN_IR_TXN"));
				derOption1 = Util.nullToSpace(dataMap6.get("IN_DV_OP"));
				derRateExchg1 = Util.nullToSpace(dataMap6.get("IN_DV_RE"));
				derCCS1 = Util.nullToSpace(dataMap6.get("IN_DV_ER"));
				derSWAP1 = Util.nullToSpace(dataMap6.get("IN_DV_FR"));
				wealthTrust1 = Util.nullToSpace(dataMap6.get("IN_WM_F_FEE"));
				wealthInsCom1 = Util.nullToSpace(dataMap6.get("IN_WM_I_FEE"));
				wealthInvest1 = Util.nullToSpace(dataMap6.get("IN_WM_S_FEE"));
				trustFund1 = Util.nullToSpace(dataMap6.get("IN_TR_FU"));
				trustSetAcct1 = Util.nullToSpace(dataMap6.get("IN_TR_CF"));
				trustBond1 = Util.nullToSpace(dataMap6.get("IN_TR_SC"));
				trustOther1 = Util.nullToSpace(dataMap6.get("IN_TR_OTS"));
				cardCommercial1 = Util.nullToSpace(dataMap6.get("IN_CC_CC"));
				cardNoneCommercial1 = Util
						.nullToSpace(dataMap6.get("IN_CC_IV"));
				IN_CC_CC_ACT = Util.nullToSpace(dataMap6.get("IN_CC_CC_ACT"));
				cardCoBranded1 = Util.nullToSpace(dataMap6.get("IN_CC_JC_ACT"));
				salaryRec1 = Util.nullToSpace(dataMap6.get("IN_ST"));
				salaryFixed1 = Util.nullToSpace(dataMap6.get("IN_ST_FD"));
				salaryMortgage1 = Util.nullToSpace(dataMap6.get("IN_ST_LN_1"));
				salaryConsumption1 = Util.nullToSpace(dataMap6
						.get("IN_ST_LN_2"));
				salaryCard1 = Util.nullToSpace(dataMap6.get("IN_ST_CC"));
				salaryNetwork1 = Util.nullToSpace(dataMap6.get("IN_ST_NB"));
				GEBTWDRec1 = Util.nullToSpace(dataMap6.get("IN_GEB_NTD_TXN"));
				GEBOTHRec1 = Util.nullToSpace(dataMap6.get("IN_GEB_NTD_N_TXN"));
				GEBLCRec1 = Util.nullToSpace(dataMap6.get("IN_GEB_LC_TXN"));
				dataMap6.get("DW_CR_DT");
				dataMap6.get("DW_LST_MNT_DT");

				// M-104-0172-001 二維表收信新增AR買方額度餘額
				// 應收帳款無追索買方承購平均餘額(IN_LN_FA_B)
				IN_LN_FA_B = Util.nullToSpace(dataMap6.get("IN_LN_FA_B"));
				// 應收帳款無追索權賣方融資平均餘額(IN_LN_FA_S)
				IN_LN_FA_S = Util.nullToSpace(dataMap6.get("IN_LN_FA_S"));
				// 授信-應收帳款買方有效額度(等值台幣)(資料來源LNF02P)
				IN_LN_FACT_AMT_FA_B = Util.nullToSpace(dataMap6
						.get("IN_LN_FACT_AMT_FA_B"));
				// 授信-應收帳款賣方有效額度(等值台幣)(資料來源LNF02P)
				IN_LN_FACT_AMT_FA_S = Util.nullToSpace(dataMap6
						.get("IN_LN_FACT_AMT_FA_S"));

				if (date.equals(queryDateE)) {
					loanQuota += Util.parseLong(loanQuota1);
					trustFund += Util.parseLong(trustFund1);
					trustSetAcct += Util.parseLong(trustSetAcct1);
					trustBond += Util.parseLong(trustBond1);
					trustOther += Util.parseLong(trustOther1);

					salaryRec += Util.parseLong(salaryRec1);
					salaryFixed += Util.parseLong(salaryFixed1);
					salaryMortgage += Util.parseLong(salaryMortgage1);
					salaryConsumption += Util.parseLong(salaryConsumption1);
					salaryCard += Util.parseLong(salaryCard1);
					salaryNetwork += Util.parseLong(salaryNetwork1);

					if ("Y".equals(cardCoBranded1)) {
						cardCoBranded = "Y";
					} else {
						cardCoBranded = "N";
					}

					// M-104-0172-001 二維表收信新增AR買方額度餘額
					// 授信-應收帳款買方有效額度(等值台幣)(資料來源LNF02P)
					in_ln_fact_amt_fa_b += Util.parseLong(IN_LN_FACT_AMT_FA_B);
					// 授信-應收帳款賣方有效額度(等值台幣)(資料來源LNF02P)
					in_ln_fact_amt_fa_s += Util.parseLong(IN_LN_FACT_AMT_FA_S);

					/*
					 * if ("Y".equals(cardCoBranded1)) { cardCoBranded = "Y"; }
					 * else { cardCoBranded = "N"; }
					 */

				}
				if ("999".equals(BR_CD)) {
					// 全行、用來計算平均動用率
					loanAvgRate = loanAvgRate.add(new BigDecimal(loanAvgRate1));
				}
				depTime += Util.parseLong(depTime1);
				depFixed += Util.parseLong(depFixed1);
				loanAvgBal += Util.parseLong(loanAvgBal1);
				exchgImpAmt += Util.parseLong(exchgImpAmt1);
				exchgExpAmt += Util.parseLong(exchgExpAmt1);
				exchgImpRec += Util.parseLong(exchgImpRec1);
				exchgExpRec += Util.parseLong(exchgExpRec1);
				exchgOutAmt += Util.parseLong(exchgOutAmt1);
				exchgInAmt += Util.parseLong(exchgInAmt1);
				exchgOutRec += Util.parseLong(exchgOutRec1);
				exchgInRec += Util.parseLong(exchgInRec1);
				derOption += Util.parseLong(derOption1);
				derRateExchg += Util.parseLong(derRateExchg1);
				derCCS += Util.parseLong(derCCS1);
				derSWAP += Util.parseLong(derSWAP1);
				wealthTrust += Util.parseLong(wealthTrust1);
				wealthInsCom += Util.parseLong(wealthInsCom1);
				wealthInvest += Util.parseLong(wealthInvest1);
				cardCommercial += Util.parseLong(cardCommercial1);
				cardNoneCommercial += Util.parseLong(cardNoneCommercial1);
				GEBTWDRec += Util.parseLong(GEBTWDRec1);
				GEBOTHRec += Util.parseLong(GEBOTHRec1);
				GEBLCRec += Util.parseLong(GEBLCRec1);
				// M-104-0172-001 二維表收信新增AR買方額度餘額
				// 應收帳款無追索買方承購平均餘額(IN_LN_FA_B)
				in_ln_fa_b += Util.parseLong(IN_LN_FA_B);
				// 應收帳款無追索權賣方融資平均餘額(IN_LN_FA_S)
				in_ln_fa_s += Util.parseLong(IN_LN_FA_S);

			}
			// 算起迄年月的差異月數
			int monDiff = (Util.parseInt(queryDateE.substring(0, 4)) * 12 + Util
					.parseInt(queryDateE.substring(5, 7)))
					- (Util.parseInt(queryDateS.substring(0, 4)) * 12 + Util
							.parseInt(queryDateS.substring(5, 7))) + 1;
			// 換算仟元
			model.setLoanQuota((long) Math.round((double) loanQuota / 1000));
			model.setTrustFund((long) Math.round((double) trustFund / 1000));
			model.setTrustSetAcct((long) Math
					.round((double) trustSetAcct / 1000));
			model.setTrustBond((long) Math.round((double) trustBond / 1000));
			model.setTrustOther((long) Math.round((double) trustOther / 1000));
			model.setExchgImpAmt((long) Math.round((double) exchgImpAmt / 1000));
			model.setExchgExpAmt((long) Math.round((double) exchgExpAmt / 1000));
			model.setExchgOutAmt((long) Math.round((double) exchgOutAmt / 1000));
			model.setExchgInAmt((long) Math.round((double) exchgInAmt / 1000));
			model.setDerOption((long) Math.round((double) derOption / 1000));
			model.setDerRateExchg((long) Math
					.round((double) derRateExchg / 1000));
			model.setDerCCS((long) Math.round((double) derCCS / 1000));
			model.setDerSWAP((long) Math.round((double) derSWAP / 1000));
			model.setWealthTrust((long) Math.round((double) wealthTrust / 1000));
			model.setWealthInsCom((long) Math
					.round((double) wealthInsCom / 1000));
			model.setWealthInvest((long) Math
					.round((double) wealthInvest / 1000));
			model.setCardCommercial((long) Math
					.round((double) cardCommercial / 1000));
			model.setCardNoneCommercial((long) Math
					.round((double) cardNoneCommercial / 1000));
			// 交易筆數不須處理
			model.setExchgImpRec((int) exchgImpRec);
			model.setExchgExpRec((int) exchgExpRec);
			model.setExchgOutRec((int) exchgOutRec);
			model.setExchgInRec((int) exchgInRec);
			model.setSalaryRec(salaryRec);
			model.setSalaryFixed(salaryFixed);
			model.setSalaryMortgage(salaryMortgage);
			model.setSalaryConsumption(salaryConsumption);
			model.setSalaryCard(salaryCard);
			model.setSalaryNetwork(salaryNetwork);
			model.setGEBTWDRec(GEBTWDRec);
			model.setGEBOTHRec(GEBOTHRec);
			model.setGEBLCRec(GEBLCRec);
			model.setCardCoBranded(cardCoBranded);
			// 算月平均【不】換算仟元
			model.setLoanAvgRate(loanAvgRate.divide(
					BigDecimal.valueOf(monDiff), 0, BigDecimal.ROUND_HALF_UP)
					.doubleValue());
			// 算月平均並換算仟元
			model.setDepTime((long) Math
					.round((depTime / (double) monDiff) / 1000));
			model.setDepFixed((long) Math
					.round((depFixed / (double) monDiff) / 1000));
			model.setLoanAvgBal((long) Math
					.round((loanAvgBal / (double) monDiff) / 1000));
			// 設定其他欄位
			// 有價證券信託
			model.setTrustSecurities((long) 0);
			// 不動產信託
			model.setTrustREITs((long) 0);
			// 福儲信託
			model.setTrustWelDep((long) 0);

			// M-104-0172-001 二維表收信新增AR買方額度餘額
			// 授信-應收帳款買方有效額度(等值台幣)(資料來源LNF02P)
			model.setRcvBuyFactAmt((long) Math
					.round((double) in_ln_fact_amt_fa_b / 1000));
			// 授信-應收帳款無追索權買方承購月平均餘額(等值台幣)(資料來源LNF150)
			model.setRcvBuyAvgBal((long) Math
					.round((in_ln_fa_b / (double) monDiff) / 1000));

			// 讀取並計算貢獻度
			List<?> rows7 = this.dwdbService.findDW_DM_CUBCPCM_TOTAL_ATTRIBUTE(
					manageId, manageDup, queryDateS, queryDateE);
			List<?> rows8 = this.dwdbService
					.findDW_DM_CUBCPCMOVS_TOTAL_ATTRIBUTE(manageId, manageDup,
							queryDateS, queryDateE);
			Iterator<?> it7 = rows7.iterator();
			Iterator<?> it8 = rows8.iterator();
			double conTri = 0;
			double conSalaryTri = 0;
			double conTrustFdtaTri = 0;
			// 國內貢獻度(存款,非存款)
			if (it7.hasNext()) {
				Map<?, ?> dataMap7 = (Map<?, ?>) it7.next();
				conTri += Util.parseDouble(Util.trim(dataMap7
						.get("TOTAL_ATTRIBUTE")));
				conSalaryTri += Util.parseDouble(Util.trim(dataMap7
						.get("SLDP_TOTAL")));
				conTrustFdtaTri += Util.parseDouble(Util.trim(dataMap7
						.get("FDTA_T_TOTAL")));
			}

			// 海外貢獻度(非存款) Miller added at 2012/07/27
			while (it8.hasNext()) {
				Map<?, ?> dataMap8 = (Map<?, ?>) it8.next();
				double seaBal = Util.parseDouble(Util.trim(dataMap8
						.get("TOTAL_ATTRIBUTE")));
				double seaSalaryBal = Util.parseDouble(Util.trim(dataMap8
						.get("SLDP_TOTAL")));
				double seaTrustFdtaBal = Util.parseDouble(Util.trim(dataMap8
						.get("FDTA_T_TOTAL")));
				String curr = Util.trim(dataMap8.get("CURR"));
				if (seaBal != 0) {
					seaBal = branchRate.toTWDAmt(
							(Util.isEmpty(curr)) ? "TWD" : curr,
							LMSUtil.toBigDecimal(seaBal)).doubleValue();
				}
				if (seaSalaryBal != 0) {
					seaSalaryBal = branchRate.toTWDAmt(
							(Util.isEmpty(curr)) ? "TWD" : curr,
							LMSUtil.toBigDecimal(seaSalaryBal)).doubleValue();
				}
				if (seaTrustFdtaBal != 0) {
					seaTrustFdtaBal = branchRate.toTWDAmt(
							(Util.isEmpty(curr)) ? "TWD" : curr,
							LMSUtil.toBigDecimal(seaTrustFdtaBal))
							.doubleValue();
				}
				conTri += seaBal;
				conSalaryTri += seaSalaryBal;
				conTrustFdtaTri += seaTrustFdtaBal;
			}

			// 開始透過AS400 取得海外存款貢獻度 Miller added at 2012/07/20
			Map mapAs400 = null;
			if (meta != null) {
				String typCd = Util.trim(meta.getTypCd());
				if (UtilConstants.Casedoc.typCd.海外.equals(typCd)) {
					try {
						Date dQueryDateS = CapDate.getDate(queryDateS,
								UtilConstants.DateFormat.YYYY_MM_DD);
						Date dQueryDateE = CapDate.getDate(queryDateE,
								UtilConstants.DateFormat.YYYY_MM_DD);
						String elf003SDate = CapDate.formatDate(dQueryDateS,
								"yyyyMM");
						String elf003EDate = CapDate.formatDate(dQueryDateE,
								"yyyyMM");
						mapAs400 = misELF003Service
								.findELF003ProfitContributeByIdDate(
										Util.trim(manageId),
										Util.trim(manageDup),
										Util.trim(meta.getCaseBrId()),
										elf003SDate, elf003EDate);
					} catch (GWException gw) {
						throw gw;
					} catch (Exception e) {
						logger.error(e.getMessage());
					}
				}
			}

			BigDecimal exchangeRate = null;

			if (!CollectionUtils.isEmpty(mapAs400)) {
				String tmpStr = MapUtils.getString(mapAs400, "ELF003_LOC_CURR");
				if (!CapString.isEmpty(tmpStr)) {
					exchangeRate = branchRate.toTWDRate(tmpStr);
				} else {
					exchangeRate = BigDecimal.ONE;
				}
				tmpStr = MapUtils.getString(mapAs400, "ELF003_AMT", "0");
				BigDecimal tmpBD = new BigDecimal(tmpStr);
				conTri += tmpBD.multiply(exchangeRate).longValue();
			}

			conTri = Util
					.parseDouble(CapMath.round(Util.trim(conTri / 1000), 0));
			model.setProfit((long) conTri);
			conSalaryTri = Util.parseDouble(CapMath.round(
					Util.trim(conSalaryTri / 1000), 0));
			model.setProfitSalary((long) conSalaryTri);
			conTrustFdtaTri = Util.parseDouble(CapMath.round(
					Util.trim(conTrustFdtaTri / 1000), 0));
			model.setProfitTrustFdta((long) conTrustFdtaTri);

			// 起始日(民國年)
			StringBuilder relDateS = new StringBuilder();
			String dateS = Util
					.addZeroWithValue(
							CapDate.convertDateToTaiwanYear(queryDateS
									.substring(0, 4)), 3);
			String dateE = Util
					.addZeroWithValue(
							CapDate.convertDateToTaiwanYear(queryDateE
									.substring(0, 4)), 3);
			relDateS.append(dateS).append("/")
					.append(queryDateS.substring(5, 7));
			// 迄日(民國年)
			StringBuilder relDateE = new StringBuilder();
			relDateE.append(dateE).append("/")
					.append(queryDateE.substring(5, 7));
			StringBuilder depMemo = new StringBuilder();
			StringBuilder loanQMemo = new StringBuilder();
			StringBuilder loanABMemo = new StringBuilder();
			StringBuilder exchgMemo = new StringBuilder();
			StringBuilder derMemo = new StringBuilder();
			StringBuilder trustMemo = new StringBuilder();
			StringBuilder wealthMemo = new StringBuilder();
			StringBuilder salaryMemo = new StringBuilder();
			StringBuilder cardComMemo = new StringBuilder();
			StringBuilder cardBrnMemo = new StringBuilder();
			StringBuilder GEBMemo = new StringBuilder();
			StringBuilder GEBLCMemo = new StringBuilder();
			StringBuilder profitMemo = new StringBuilder();
			Properties pop = MessageBundleScriptCreator
					.getComponentResource(LMSS07APanel.class);
			if (Util.parseInt(dateS) >= 0 && Util.parseInt(dateE) >= 0
					&& Util.parseInt(dateS) <= 999
					&& Util.parseInt(dateE) <= 999) {
				depMemo.append(relDateS).append("~").append(relDateE)
						.append(pop.getProperty("L1205S07.form1"));
				loanQMemo.append(relDateE).append(
						pop.getProperty("L1205S07.form2"));
				loanABMemo.append(relDateS).append("~").append(relDateE);
				exchgMemo.append(relDateS).append("~").append(relDateE)
						.append(pop.getProperty("L1205S07.form3"));
				derMemo.append(relDateS).append("~").append(relDateE)
						.append(pop.getProperty("L1205S07.form3"));
				trustMemo.append(relDateE).append(
						pop.getProperty("L1205S07.form4"));
				wealthMemo.append(relDateS).append("~").append(relDateE)
						.append(pop.getProperty("L1205S07.form5"));
				salaryMemo.append(relDateE).append(
						pop.getProperty("L1205S07.form6"));
				cardComMemo.append(relDateS).append("~").append(relDateE)
						.append(pop.getProperty("L1205S07.form7"));
				cardBrnMemo.append(relDateE).append(
						pop.getProperty("L1205S07.form8"));
				GEBMemo.append(relDateS).append("~").append(relDateE)
						.append(pop.getProperty("L1205S07.form9"));
				GEBLCMemo.append(relDateS).append("~").append(relDateE);
				profitMemo.append(relDateS).append("~").append(relDateE);
			}
			// 設定資料基期
			list.add(model);
			loanQuota = 0;
			trustFund = 0;
			trustSetAcct = 0;
			trustBond = 0;
			trustOther = 0;
			salaryRec = 0;
			salaryFixed = 0;
			salaryMortgage = 0;
			salaryConsumption = 0;
			salaryCard = 0;
			salaryNetwork = 0;
			loanAvgRate = new BigDecimal("0");
			depTime = 0;
			depFixed = 0;
			loanAvgBal = 0;
			exchgImpAmt = 0;
			exchgExpAmt = 0;
			exchgImpRec = 0;
			exchgExpRec = 0;
			exchgInAmt = 0;
			exchgOutAmt = 0;
			exchgOutRec = 0;
			exchgInRec = 0;
			derOption = 0;
			derRateExchg = 0;
			derCCS = 0;
			derSWAP = 0;
			wealthTrust = 0;
			wealthInsCom = 0;
			wealthInvest = 0;
			cardCommercial = 0;
			cardNoneCommercial = 0;
			GEBTWDRec = 0;
			GEBOTHRec = 0;
			GEBLCRec = 0;

			// M-104-0172-001 二維表收信新增AR買方額度餘額
			in_ln_fa_b = 0;
			in_ln_fa_s = 0;
			in_ln_fact_amt_fa_b = 0;
			in_ln_fact_amt_fa_s = 0;

		}
		return list;
	}

	/**
	 * 取得業務往來資訊
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @param queryDateS
	 * @param queryDateE
	 * @return
	 */
	@SuppressWarnings("unused")
	private JSONObject getL120S04B(String mainId, String custId, String dupNo,
			String queryDateS, String queryDateE) {
		JSONObject jsonData = new JSONObject();
		// L120M01A meta = l120m01aDao.findByMainId(mainId);
		// 活期存款
		long depTime = 0;
		// 定期存款
		long depFixed = 0;
		// 額度
		long loanQuota = 0;
		// 平均餘額
		long loanAvgBal = 0;
		// 平均動用率
		BigDecimal loanAvgRate = new BigDecimal("0");
		// 進口筆數
		long exchgImpRec = 0;
		// 進口金額
		long exchgImpAmt = 0;
		// 出口筆數
		long exchgExpRec = 0;
		// 出口金額
		long exchgExpAmt = 0;
		// 匯出筆數
		long exchgOutRec = 0;
		// 匯出金額
		long exchgOutAmt = 0;
		// 匯入筆數
		long exchgInRec = 0;
		// 匯入金額
		long exchgInAmt = 0;
		// 選擇權
		long derOption = 0;
		// 利率交換
		long derRateExchg = 0;
		// 換匯換利
		long derCCS = 0;
		// 遠匯
		String derDraft = "0";
		// 遠匯(含SWAP)
		long derSWAP = 0;
		// 國內外基金債券
		long trustBond = 0;
		// 基金保管
		long trustFund = 0;
		// 集管
		long trustSetAcct = 0;
		// 有價證券信託
		String trustSecurities = "0";
		// 不動產信託
		String trustREITs = "0";
		// 福儲信託
		String trustWelDep = "0";
		// 其他信託
		long trustOther = 0;
		// 信託
		long wealthTrust = 0;
		// 保險佣金
		long wealthInsCom = 0;
		// 雙元投資
		long wealthInvest = 0;
		// 薪轉戶數
		long salaryRec = 0;
		// 定期定額戶數
		long salaryFixed = 0;
		// 房貸戶數
		long salaryMortgage = 0;
		// 消貸戶數
		long salaryConsumption = 0;
		// 信用卡持卡人數
		long salaryCard = 0;
		// 個人網銀戶數
		long salaryNetwork = 0;
		// 商務卡
		long cardCommercial = 0;
		// 非商務卡
		long cardNoneCommercial = 0;
		// 聯名卡
		String cardCoBranded = "N";
		// 台幣交易筆數
		long GEBTWDRec = 0;
		// 外幣交易筆數
		long GEBOTHRec = 0;
		// 信用狀交易筆數
		long GEBLCRec = 0;
		// 利潤貢獻度
		String profit = "0";
		// 活期存款
		String depTime1 = "0";
		// 定期存款
		String depFixed1 = "0";
		// 額度
		String loanQuota1 = "0";
		// 平均餘額
		String loanAvgBal1 = "0";
		// 平均動用率
		String loanAvgRate1 = "0";
		// 進口筆數
		String exchgImpRec1 = "0";
		// 進口金額
		String exchgImpAmt1 = "0";
		// 出口筆數
		String exchgExpRec1 = "0";
		// 出口金額
		String exchgExpAmt1 = "0";
		// 匯出筆數
		String exchgOutRec1 = "0";
		// 匯出金額
		String exchgOutAmt1 = "0";
		// 匯入筆數
		String exchgInRec1 = "0";
		// 匯入金額
		String exchgInAmt1 = "0";
		// 選擇權
		String derOption1 = "0";
		// 利率交換
		String derRateExchg1 = "0";
		// 換匯換利
		String derCCS1 = "0";
		// 遠匯
		String derDraft1 = "0";
		// 遠匯(含SWAP)
		String derSWAP1 = "0";
		// 國內外基金債券
		String trustBond1 = "0";
		// 基金保管
		String trustFund1 = "0";
		// 集管
		String trustSetAcct1 = "0";
		// 有價證券信託
		String trustSecurities1 = "0";
		// 不動產信託
		String trustREITs1 = "0";
		// 福儲信託
		String trustWelDep1 = "0";
		// 其他信託
		String trustOther1 = "0";
		// 信託
		String wealthTrust1 = "0";
		// 保險佣金
		String wealthInsCom1 = "0";
		// 雙元投資
		String wealthInvest1 = "0";
		// 薪轉戶數
		String salaryRec1 = "0";
		// 定期定額戶數
		String salaryFixed1 = "0";
		// 房貸戶數
		String salaryMortgage1 = "0";
		// 消貸戶數
		String salaryConsumption1 = "0";
		// 信用卡持卡人數
		String salaryCard1 = "0";
		// 個人網銀戶數
		String salaryNetwork1 = "0";
		// 商務卡
		String cardCommercial1 = "0";
		// 非商務卡
		String cardNoneCommercial1 = "0";
		// 聯名卡
		String cardCoBranded1 = "N";
		// 台幣交易筆數
		String GEBTWDRec1 = "0";
		// 外幣交易筆數
		String GEBOTHRec1 = "0";
		// 信用狀交易筆數
		String GEBLCRec1 = "0";
		// 利潤貢獻度
		String profit1 = "0";

		String IN_CC_CC_ACT = "";
		String BR_CD = "";
		String DW_CR_DT = "0";
		String DW_LST_MNT_DT = "0";
		String IN_LN_FA_B = "0";
		String IN_LN_FA_S = "0";
		long in_ln_fa_b = 0;
		long in_ln_fa_s = 0;

		// M-104-0172-001 二維表收信新增AR買方額度餘額
		String IN_LN_FACT_AMT_FA_S = "0";
		String IN_LN_FACT_AMT_FA_B = "0";
		long in_ln_fact_amt_fa_b = 0;
		long in_ln_fact_amt_fa_s = 0;

		custId = (custId + "          ").substring(0, 10);
		if ("0".equals(dupNo)) {
			dupNo = " ";
		}

		List<?> rows6 = this.dwdbService.findDW_MD_CUPFM_OTS_selCYC_MN(custId,
				dupNo, queryDateS, queryDateE);
		Iterator<?> it6 = rows6.iterator();
		while (it6.hasNext()) {
			Map<?, ?> dataMap6 = (Map<?, ?>) it6.next();
			String date = CapDate.formatDate(
					CapDate.parseDate(String.valueOf(dataMap6.get("CYC_MN"))),
					"yyyy-MM-dd");
			dataMap6.get("CUST_KEY");
			BR_CD = Util.nullToSpace(dataMap6.get("BR_CD")); // 999代表全行
			depTime1 = Util.nullToSpace(dataMap6.get("IN_DP"));
			dataMap6.get("IN_DP_G");
			depFixed1 = Util.nullToSpace(dataMap6.get("IN_CT"));
			loanAvgBal1 = Util.nullToSpace(dataMap6.get("IN_LN_USE"));
			loanAvgRate1 = Util.nullToSpace(dataMap6.get("IN_LN_AVGRT"));
			loanQuota1 = Util.nullToSpace(dataMap6.get("IN_LN_FACT_AMT"));
			exchgImpAmt1 = Util.nullToSpace(dataMap6.get("IN_IM"));
			exchgExpAmt1 = Util.nullToSpace(dataMap6.get("IN_EX_BP"));
			exchgImpRec1 = Util.nullToSpace(dataMap6.get("IN_IM_TXN"));
			exchgExpRec1 = Util.nullToSpace(dataMap6.get("IN_EX_TXN"));
			exchgOutAmt1 = Util.nullToSpace(dataMap6.get("IN_OR"));
			exchgInAmt1 = Util.nullToSpace(dataMap6.get("IN_IR"));
			exchgOutRec1 = Util.nullToSpace(dataMap6.get("IN_OR_TXN"));
			exchgInRec1 = Util.nullToSpace(dataMap6.get("IN_IR_TXN"));
			derOption1 = Util.nullToSpace(dataMap6.get("IN_DV_OP"));
			derRateExchg1 = Util.nullToSpace(dataMap6.get("IN_DV_RE"));
			derCCS1 = Util.nullToSpace(dataMap6.get("IN_DV_ER"));
			derSWAP1 = Util.nullToSpace(dataMap6.get("IN_DV_FR"));
			wealthTrust1 = Util.nullToSpace(dataMap6.get("IN_WM_F_FEE"));
			wealthInsCom1 = Util.nullToSpace(dataMap6.get("IN_WM_I_FEE"));
			wealthInvest1 = Util.nullToSpace(dataMap6.get("IN_WM_S_FEE"));
			trustFund1 = Util.nullToSpace(dataMap6.get("IN_TR_FU"));
			trustSetAcct1 = Util.nullToSpace(dataMap6.get("IN_TR_CF"));
			trustBond1 = Util.nullToSpace(dataMap6.get("IN_TR_SC"));
			trustOther1 = Util.nullToSpace(dataMap6.get("IN_TR_OTS"));
			cardCommercial1 = Util.nullToSpace(dataMap6.get("IN_CC_CC"));
			cardNoneCommercial1 = Util.nullToSpace(dataMap6.get("IN_CC_IV"));
			IN_CC_CC_ACT = Util.nullToSpace(dataMap6.get("IN_CC_CC_ACT"));
			cardCoBranded1 = Util.nullToSpace(dataMap6.get("IN_CC_JC_ACT"));
			salaryRec1 = Util.nullToSpace(dataMap6.get("IN_ST"));
			salaryFixed1 = Util.nullToSpace(dataMap6.get("IN_ST_FD"));
			salaryMortgage1 = Util.nullToSpace(dataMap6.get("IN_ST_LN_1"));
			salaryConsumption1 = Util.nullToSpace(dataMap6.get("IN_ST_LN_2"));
			salaryCard1 = Util.nullToSpace(dataMap6.get("IN_ST_CC"));
			salaryNetwork1 = Util.nullToSpace(dataMap6.get("IN_ST_NB"));
			GEBTWDRec1 = Util.nullToSpace(dataMap6.get("IN_GEB_NTD_TXN"));
			GEBOTHRec1 = Util.nullToSpace(dataMap6.get("IN_GEB_NTD_N_TXN"));
			GEBLCRec1 = Util.nullToSpace(dataMap6.get("IN_GEB_LC_TXN"));
			DW_CR_DT = Util.nullToSpace(dataMap6.get("DW_CR_DT"));
			DW_LST_MNT_DT = Util.nullToSpace(dataMap6.get("DW_LST_MNT_DT"));

			// 應收帳款無追索買方承購平均餘額(IN_LN_FA_B)
			IN_LN_FA_B = Util.nullToSpace(dataMap6.get("IN_LN_FA_B"));
			// 應收帳款無追索權賣方融資平均餘額(IN_LN_FA_S)
			IN_LN_FA_S = Util.nullToSpace(dataMap6.get("IN_LN_FA_S"));

			// M-104-0172-001 二維表收信新增AR買方額度餘額
			// 授信-應收帳款買方有效額度(等值台幣)(資料來源LNF02P)
			IN_LN_FACT_AMT_FA_B = Util.nullToSpace(dataMap6
					.get("IN_LN_FACT_AMT_FA_B"));
			// 授信-應收帳款賣方有效額度(等值台幣)(資料來源LNF02P)
			IN_LN_FACT_AMT_FA_S = Util.nullToSpace(dataMap6
					.get("IN_LN_FACT_AMT_FA_S"));

			dataMap6.get("DW_CR_DT");
			dataMap6.get("DW_LST_MNT_DT");
			if (date.equals(queryDateE)) {
				loanQuota += Util.parseLong(loanQuota1);
				trustFund += Util.parseLong(trustFund1);
				trustSetAcct += Util.parseLong(trustSetAcct1);
				trustBond += Util.parseLong(trustBond1);
				trustOther += Util.parseLong(trustOther1);

				salaryRec += Util.parseLong(salaryRec1);
				salaryFixed += Util.parseLong(salaryFixed1);
				salaryMortgage += Util.parseLong(salaryMortgage1);
				salaryConsumption += Util.parseLong(salaryConsumption1);
				salaryCard += Util.parseLong(salaryCard1);
				salaryNetwork += Util.parseLong(salaryNetwork1);

				if ("Y".equals(cardCoBranded1)) {
					cardCoBranded = "Y";
				} else {
					cardCoBranded = "Y";
				}

				// M-104-0172-001 二維表收信新增AR買方額度餘額
				// 授信-應收帳款買方有效額度(等值台幣)(資料來源LNF02P)
				in_ln_fact_amt_fa_b += Util.parseLong(IN_LN_FACT_AMT_FA_B);
				// 授信-應收帳款賣方有效額度(等值台幣)(資料來源LNF02P)
				in_ln_fact_amt_fa_s += Util.parseLong(IN_LN_FACT_AMT_FA_S);

				/*
				 * if ("Y".equals(cardCoBranded1)) { cardCoBranded = "Y"; } else
				 * { cardCoBranded = "N"; }
				 */

			}
			if ("999".equals(BR_CD)) {
				// 全行、用來計算平均動用率
				loanAvgRate = loanAvgRate.add(new BigDecimal(loanAvgRate1));
			}
			depTime += Util.parseLong(depTime1);
			depFixed += Util.parseLong(depFixed1);
			loanAvgBal += Util.parseLong(loanAvgBal1);
			exchgImpAmt += Util.parseLong(exchgImpAmt1);
			exchgExpAmt += Util.parseLong(exchgExpAmt1);
			exchgImpRec += Util.parseLong(exchgImpRec1);
			exchgExpRec += Util.parseLong(exchgExpRec1);
			exchgOutAmt += Util.parseLong(exchgOutAmt1);
			exchgInAmt += Util.parseLong(exchgInAmt1);
			exchgOutRec += Util.parseLong(exchgOutRec1);
			exchgInRec += Util.parseLong(exchgInRec1);
			derOption += Util.parseLong(derOption1);
			derRateExchg += Util.parseLong(derRateExchg1);
			derCCS += Util.parseLong(derCCS1);
			derSWAP += Util.parseLong(derSWAP1);
			wealthTrust += Util.parseLong(wealthTrust1);
			wealthInsCom += Util.parseLong(wealthInsCom1);
			wealthInvest += Util.parseLong(wealthInvest1);
			cardCommercial += Util.parseLong(cardCommercial1);
			cardNoneCommercial += Util.parseLong(cardNoneCommercial1);
			GEBTWDRec += Util.parseLong(GEBTWDRec1);
			GEBOTHRec += Util.parseLong(GEBOTHRec1);
			GEBLCRec += Util.parseLong(GEBLCRec1);

			in_ln_fa_b += Util.parseLong(IN_LN_FA_B);
			in_ln_fa_s += Util.parseLong(IN_LN_FA_S);
		}

		// 算起迄年月的差異月數
		int monDiff = (Util.parseInt(queryDateE.substring(0, 4)) * 12 + Util
				.parseInt(queryDateE.substring(5, 7)))
				- (Util.parseInt(queryDateS.substring(0, 4)) * 12 + Util
						.parseInt(queryDateS.substring(5, 7))) + 1;
		// 換算仟元
		jsonData.put("loanQuota", (long) Math.round((double) loanQuota / 1000));
		jsonData.put("trustFund", (long) Math.round((double) trustFund / 1000));
		jsonData.put("trustSetAcct",
				(long) Math.round((double) trustSetAcct / 1000));
		jsonData.put("trustBond", (long) Math.round((double) trustBond / 1000));
		jsonData.put("trustOther",
				(long) Math.round((double) trustOther / 1000));
		jsonData.put("exchgImpAmt",
				(long) Math.round((double) exchgImpAmt / 1000));
		jsonData.put("exchgExpAmt",
				(long) Math.round((double) exchgExpAmt / 1000));
		jsonData.put("exchgOutAmt",
				(long) Math.round((double) exchgOutAmt / 1000));
		jsonData.put("exchgInAmt",
				(long) Math.round((double) exchgInAmt / 1000));
		jsonData.put("derOption", (long) Math.round((double) derOption / 1000));
		jsonData.put("derRateExchg",
				(long) Math.round((double) derRateExchg / 1000));
		jsonData.put("derCCS", (long) Math.round((double) derCCS / 1000));
		jsonData.put("derSWAP", (long) Math.round((double) derSWAP / 1000));
		jsonData.put("wealthTrust",
				(long) Math.round((double) wealthTrust / 1000));
		jsonData.put("wealthInsCom",
				(long) Math.round((double) wealthInsCom / 1000));
		jsonData.put("wealthInvest",
				(long) Math.round((double) wealthInvest / 1000));
		jsonData.put("cardCommercial",
				(long) Math.round((double) cardCommercial / 1000));
		jsonData.put("cardNoneCommercial",
				(long) Math.round((double) cardNoneCommercial / 1000));
		// 交易筆數不須處理
		jsonData.put("exchgImpRec", (int) exchgImpRec);
		jsonData.put("exchgExpRec", (int) exchgExpRec);
		jsonData.put("exchgOutRec", (int) exchgOutRec);
		jsonData.put("exchgInRec", (int) exchgInRec);
		jsonData.put("salaryRec", salaryRec);
		jsonData.put("salaryFixed", salaryFixed);
		jsonData.put("salaryMortgage", salaryMortgage);
		jsonData.put("salaryConsumption", salaryConsumption);
		jsonData.put("salaryCard", salaryCard);
		jsonData.put("salaryNetwork", salaryNetwork);
		jsonData.put("gEBTWDRec", GEBTWDRec);
		jsonData.put("gEBOTHRec", GEBOTHRec);
		jsonData.put("gEBLCRec", GEBLCRec);
		jsonData.put("cardCoBranded", cardCoBranded);
		// 算月平均【不】換算仟元
		jsonData.put(
				"loanAvgRate",
				loanAvgRate.divide(BigDecimal.valueOf(monDiff), 0,
						BigDecimal.ROUND_HALF_UP).doubleValue());
		// 算月平均並換算仟元
		jsonData.put("depTime",
				(long) Math.round((depTime / (double) monDiff) / 1000));
		jsonData.put("depFixed",
				(long) Math.round((depFixed / (double) monDiff) / 1000));
		jsonData.put("loanAvgBal",
				(long) Math.round((loanAvgBal / (double) monDiff) / 1000));
		// 設定其他欄位
		// 有價證券信託
		jsonData.put("trustSecurities", (long) 0);
		// 不動產信託
		jsonData.put("trustREITs", (long) 0);
		// 福儲信託
		jsonData.put("trustWelDep", (long) 0);

		// 應收帳款無追索買方承購平均餘額(IN_LN_FA_B)
		jsonData.put("IN_LN_FA_B",
				(long) Math.round((in_ln_fa_b / (double) monDiff) / 1000));
		// 應收帳款無追索權賣方融資平均餘額(IN_LN_FA_S)
		jsonData.put("IN_LN_FA_S",
				(long) Math.round((in_ln_fa_s / (double) monDiff) / 1000));

		// M-104-0172-001 二維表收信新增AR買方額度餘額
		// 授信-應收帳款買方有效額度(等值台幣)(資料來源LNF02P)
		jsonData.put("IN_LN_FACT_AMT_FA_B",
				(long) Math.round((double) in_ln_fact_amt_fa_b / 1000));
		// 授信-應收帳款賣方有效額度(等值台幣)(資料來源LNF02P)
		jsonData.put("IN_LN_FACT_AMT_FA_S",
				(long) Math.round((double) in_ln_fact_amt_fa_s / 1000));

		// 讀取並計算貢獻度
		// List<?> rows7 = this.dwdbService.findDW_DM_CUBCPCM_TOTAL_ATTRIBUTE(
		// custId + dupNo, queryDateS, queryDateE);
		// List<?> rows8 =
		// this.dwdbService.findDW_DM_CUBCPCMOVS_TOTAL_ATTRIBUTE(
		// custId + dupNo, queryDateS, queryDateE);
		// Iterator<?> it7 = rows7.iterator();
		// Iterator<?> it8 = rows8.iterator();
		long conTri = 0;
		// 國內貢獻度(存款,非存款)
		// if (it7.hasNext()) {
		// Map<?, ?> dataMap7 = (Map<?, ?>) it7.next();
		// conTri = Util.parseLong(Util.trim(Util.nullToSpace(dataMap7
		// .get("TOTAL_ATTRIBUTE"))));
		// if (conTri != 0) {
		// conTri = Util.parseLong(CapMath.round(Util.trim(conTri / 1000),
		// 0));
		// }
		// }
		// // 海外貢獻度(非存款) Miller added at 2012/07/27
		// if (it8.hasNext()) {
		// Map<?, ?> dataMap8 = (Map<?, ?>) it8.next();
		// conTri += Util.parseLong(Util.trim(Util.nullToSpace(dataMap8
		// .get("TOTAL_ATTRIBUTE"))));
		// }
		// // 開始透過AS400 取得海外存款貢獻度 Miller added at 2012/07/20
		// Map mapAs400 = null;
		// try {
		// mapAs400 = misELF003Service.findELF003ProfitContributeById(
		// Util.trim(custId), Util.trim(dupNo),
		// Util.trim(meta.getCaseBrId()));
		// } catch (GWException gw) {
		// throw gw;
		// } catch (Exception e) {
		// logger.error(e.getMessage());
		// }
		//
		// // 依目前簽案行做計算幣別
		// BranchRate branchRate = lmsService.getBranchRate(user.getUnitNo());
		//
		// BigDecimal exchangeRate = null;
		//
		// if (!CollectionUtils.isEmpty(mapAs400)) {
		// String tmpStr = MapUtils.getString(mapAs400, "ELF003_LOC_CURR");
		// if (!CapString.isEmpty(tmpStr)) {
		// exchangeRate = branchRate.toTWDRate(tmpStr);
		// } else {
		// exchangeRate = BigDecimal.ONE;
		// }
		// tmpStr = MapUtils.getString(mapAs400, "ELF003_AMT", "0");
		// BigDecimal tmpBD = new BigDecimal(tmpStr);
		// conTri += tmpBD.multiply(exchangeRate).longValue();
		// }
		// conTri = (long) Math.round(conTri / 1000);
		jsonData.put("CUBCPCM", conTri);
		return jsonData;
	}

	@Override
	public void save(GenericBean... entity) {
		// 進行無限多筆儲存
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L120S04A) {
					((L120S04A) model).setUpdater(user.getUserId());
					((L120S04A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l120s04aDao.save((L120S04A) model);
					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(((L120S04A) model)
								.getMainId());
					}
				} else if (model instanceof L120S04B) {
					((L120S04B) model).setUpdater(user.getUserId());
					((L120S04B) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l120s04bDao.save((L120S04B) model);
					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(((L120S04B) model)
								.getMainId());
					}
				} else if (model instanceof L120S04C) {
					((L120S04C) model).setUpdater(user.getUserId());
					((L120S04C) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l120s04cDao.save((L120S04C) model);
					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(((L120S04C) model)
								.getMainId());
					}
				}
			}
		}
	}

	@Override
	public void delete(GenericBean... entity) {
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L120S04A) {
					l120s04aDao.delete((L120S04A) model);
				} else if (model instanceof L120S04B) {
					l120s04bDao.delete((L120S04B) model);
				} else if (model instanceof L120S04C) {
					l120s04cDao.delete((L120S04C) model);
				}
			}
		}
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == L120S04A.class) {
			return l120s04aDao.findPage(search);
		} else if (clazz == L120S04B.class) {
			return l120s04bDao.findPage(search);
		} else if (clazz == L120S04C.class) {
			return l120s04cDao.findPage(search);
		} else if (clazz == L120S01A.class) {
			return l120s01aDao.findPage(search);
		}
		return null;
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {	
	    if (clazz == L260M01D.class) {
	    	// J-112-0307
	    	// 於eloan貸後管理追蹤檢核表維護功能中，新增一鍵生成公司訪問紀錄表及借戶、負責人、所屬企業集團等關係戶於本行各項業務往來彙總表。
		   return (T) l260m01dDao.findByOid(oid);
	    }				
		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		return null;
	}

	@Override
	public List<L120S01C> findL120s01cByCustId(String mainId, String custId,
			String dupNo) {
		return l120s01cDao.findByCustId(mainId, custId, dupNo);
	}
	
	@Override
	public void impBisEstimatedReturn(String kind,
			String mainId, String queryDateE, JSONObject jsonData,
			BranchRate branchRate, String qCustId, String qDupNo,
			Boolean isKindA, BigDecimal factAmtIncrease) throws CapException {

		L120M01A meta = lmsService.findModelByMainId(L120M01A.class, mainId);

		List<L120S04A> listL120s04a = this.findL120s04aByMainId(mainId);

		JSONObject json = new JSONObject();

		// 查詢起日-往前推一年
		Date dQueryDate12S = CapDate.addMonth(Util.parseDate(queryDateE), -11);
		// 查詢迄日
		Date dQueryDateE = Util.parseDate(queryDateE);
		String tID = null;
		String tDUPNO = null;
		double bal = 0;
		
		// e-Loan簽報書BIS評估表調整海外分行存款貢獻度之年化處理及非授信貢獻度為負時應顯示之警語
		Map<String, Object> negativeMap = new HashMap<String, Object>();

		if (listL120s04a.isEmpty()) {
			// L120S25A.errMsg03= 必須先執行【引進各關係戶往來彙總】才能執行本功能！
			Properties pop = MessageBundleScriptCreator
					.getComponentResource(LMSCommomPage.class);
			throw new CapMessageException(RespMsgHelper.getMessage(
					"EFD0015", pop.getProperty("L1205S07.error14")), getClass());
		} else {

			// kind 說明:
			// 1:借款人
			// 2:關係企業 ==> J-112-0044 配合kind A 停用
			// 3.關係企業+借款人 ==> J-112-0044 配合kind A 2023/02/10 改為 "借款人+集團企業"
			// A.個體+集團　　2023/01/18 風控處改個體(借款人)；集團(借款人+集團企業)

			// 組成查詢名單
			if (Util.equals(kind, "1")) {
				// 查詢主借戶時要以合理性分細表的借戶統編來查，非以簽報書主借款人統編
				String tFullId = Util.trim(qCustId) + "-" + Util.trim(qDupNo);
				json.put(tFullId, tFullId);

			} else {
				for (L120S04A model : listL120s04a) {
					String custRel = Util.trim(model.getCustRelation());
					String custId = Util.trim(model.getCustId());
					String dupNo = Util.trim(model.getDupNo());
					StringBuilder sbFullId = new StringBuilder();
					sbFullId.append(custId).append("-").append(dupNo);

					if (!custRel.contains("3") && !custRel.contains("4")) {
						if (Util.equals(kind, "2") || Util.equals(kind, "3")) {
							// 2:關係企業 ==> 停用
							// 3.關係企業+借款人 ==> 2023/02/10 改為 "借款人+集團企業"
							// custRel 從 6 改 5
							if (custRel.contains("5")) {
								json.put(sbFullId.toString(),
										Util.trim(model.getCustName()));
							}
						}
					}
				}
			}
			
			if (Util.equals(kind, "3")) {
				// 理論上該筆ID已於處理集團時包含在json了，這邊只是保險
				// 因是借款人+集團企業，沒有集團時至少要引進借款人本人
				String tFullId = Util.trim(qCustId) + "-" + Util.trim(qDupNo);
				json.put(tFullId, tFullId);
			}

			// 初始合計--所有查詢人的合計******************************
			BigDecimal totalNoneLoanProfit = BigDecimal.ZERO; // 一年內非授信
			BigDecimal totalLoanBal = BigDecimal.ZERO; // 授信餘額
			

			long loanAvgBal = 0; // 平均餘額
			String loanAvgBal1 = "0"; // 平均餘額

			// a Method
			if (!json.isEmpty()) {
				for (Object jsonKey : json.keySet()) {
					loanAvgBal = 0; // 平均餘額
					String sJsonKey = (String) jsonKey;
					tID = sJsonKey.split("-")[0];
					tDUPNO = sJsonKey.split("-")[1];
					// ********************國內*********************************************************
					List<Map<String, Object>> noneLoanTw = this.dwdbService
							.findDW_DM_CUBCPCM_TOTAL_ATTRIBUTE_None_Loan(
									tID,
									tDUPNO,
									CapDate.formatDate(dQueryDate12S,
											UtilConstants.DateFormat.YYYY_MM_DD),
									CapDate.formatDate(dQueryDateE,
											UtilConstants.DateFormat.YYYY_MM_DD));

					Map<String, String> cycMn_tw = new HashMap<String, String>();
					BigDecimal noneLoanProfitTw = BigDecimal.ZERO;
					for (Map<String, Object> noneLoan : noneLoanTw) {

						String CYN_NN = Util.trim(MapUtils.getString(noneLoan,
								"CYC_MN"));
						if (!cycMn_tw.containsKey(CYN_NN)) {
							cycMn_tw.put(CYN_NN, CYN_NN);
						}

						bal = Util.parseDouble(Util.trim(noneLoan
								.get("TOTAL_ATTRIBUTE")));

						noneLoanProfitTw = noneLoanProfitTw.add(BigDecimal
								.valueOf(bal));
					}

					if (cycMn_tw.size() > 0 && cycMn_tw.size() < 12) {
						// 未滿一年要年化
						noneLoanProfitTw = noneLoanProfitTw.divide(
								new BigDecimal(cycMn_tw.size()), 2,
								RoundingMode.HALF_UP).multiply(
								new BigDecimal(12));
					}

					// ********************海外*********************************************************
					// 一年內授信 loan
					List<Map<String, Object>> noneLoanOv = this.dwdbService
							.findDW_DM_CUBCPCMOVS_TOTAL_ATTRIBUTE_None_Loan(
									tID,
									tDUPNO,
									CapDate.formatDate(dQueryDate12S,
											UtilConstants.DateFormat.YYYY_MM_DD),
									CapDate.formatDate(dQueryDateE,
											UtilConstants.DateFormat.YYYY_MM_DD));

					Map<String, String> cycMn_ov = new HashMap<String, String>();
					BigDecimal noneLoanProfitOv = BigDecimal.ZERO;
					for (Map<String, Object> noneLoan : noneLoanOv) {

						String CYN_NN = Util.trim(MapUtils.getString(noneLoan,
								"CYC_MN"));
						if (!cycMn_ov.containsKey(CYN_NN)) {
							cycMn_ov.put(CYN_NN, CYN_NN);
						}

						double seaBal = Util.parseDouble(Util.trim(noneLoan
								.get("TOTAL_ATTRIBUTE")));

						String curr = Util.trim(noneLoan.get("CURR"));
						if (seaBal != 0) {
							seaBal = branchRate.toTWDAmt(
									(Util.isEmpty(curr)) ? "TWD" : curr,
									LMSUtil.toBigDecimal(seaBal)).doubleValue();
						}

						noneLoanProfitOv = noneLoanProfitOv.add(BigDecimal
								.valueOf(seaBal));

					}

					if (cycMn_ov.size() > 0 && cycMn_ov.size() < 12) {
						// 未滿一年要年化
						noneLoanProfitOv = noneLoanProfitOv.divide(
								new BigDecimal(cycMn_ov.size()), 2,
								RoundingMode.HALF_UP).multiply(
								new BigDecimal(12));
					}

					// ***************************非授信國內+海外*******************************
					totalNoneLoanProfit = totalNoneLoanProfit.add(
							noneLoanProfitTw).add(noneLoanProfitOv);

					// 海外貢獻度(存款)*************************************************************
					Map mapAs400 = null;
					if (meta != null) {
						String typCd = Util.trim(meta.getTypCd());
						if (UtilConstants.Casedoc.typCd.海外.equals(typCd)) {
							try {
								String elf003SDate = CapDate.formatDate(
										dQueryDate12S, "yyyyMM");
								String elf003EDate = CapDate.formatDate(
										dQueryDateE, "yyyyMM");
								mapAs400 = misELF003Service
										.findELF003ProfitContributeByIdDate(
												Util.trim(tID),
												Util.trim(tDUPNO),
												Util.trim(meta.getCaseBrId()),
												elf003SDate, elf003EDate);
							} catch (GWException gw) {
								Properties pop = MessageBundleScriptCreator
										.getComponentResource(LMSCommomPage.class);
								throw new CapMessageException(
										RespMsgHelper.getMessage("EFD0015",
												MessageFormat.format(
														pop.getProperty("L1205S07.error24"),
														tID, tDUPNO)),
										getClass());
							} catch (Exception e) {
								Properties pop = MessageBundleScriptCreator
										.getComponentResource(LMSCommomPage.class);
								throw new CapMessageException(
										RespMsgHelper.getMessage("EFD0015",
												MessageFormat.format(
														pop.getProperty("L1205S07.error24"),
														tID, tDUPNO)),
										getClass());
							}
						}
					}

					BigDecimal exchangeRate = null;
					if (mapAs400 != null && !mapAs400.isEmpty()) {

						String tmpStr = MapUtils.getString(mapAs400,
								"ELF003_LOC_CURR");

						BigDecimal ELF003_YYMM_COUNT = Util
								.parseBigDecimal(MapUtils.getString(mapAs400,
										"ELF003_YYMM_COUNT"));

						if (!CapString.isEmpty(tmpStr)) {
							exchangeRate = branchRate.toTWDRate(tmpStr);
						} else {
							exchangeRate = BigDecimal.ONE;
						}
						tmpStr = MapUtils
								.getString(mapAs400, "ELF003_AMT", "0");
						BigDecimal tmpBD = new BigDecimal(tmpStr);

						if (ELF003_YYMM_COUNT.compareTo(BigDecimal.ZERO) > 0
								&& ELF003_YYMM_COUNT.compareTo(new BigDecimal(
										12)) < 0) {
							// 未滿一年要年化
							tmpBD = tmpBD.divide(ELF003_YYMM_COUNT, 2,
									RoundingMode.HALF_UP).multiply(
									new BigDecimal(12));
						}
						
						// e-Loan簽報書BIS評估表調整海外分行存款貢獻度之年化處理及非授信貢獻度為負時應顯示之警語
						String negativeStr = MapUtils.getString(mapAs400,
								"ELF003_LAST_HAS_NEGATIVE");
						if (Util.equals(negativeStr, "Y")) {
							String negativeKey = Util.trim(tID)
									+ "-"
									+ (Util.equals(Util.trim(tDUPNO), "") ? "0"
											: tDUPNO);
							negativeMap.put(negativeKey, negativeKey);
						}

						totalNoneLoanProfit = totalNoneLoanProfit.add(tmpBD
								.multiply(exchangeRate));

					}
					
					// 往來明細
					List<?> rows = this.dwdbService.findDW_MD_CUPFM_OTS_selCYC_MN(tID, tDUPNO, CapDate
							.formatDate(dQueryDate12S, "yyyy-MM-dd"),
							CapDate.formatDate(dQueryDateE,
									"yyyy-MM-dd"));
					Iterator<?> it = rows.iterator();
					while (it.hasNext()) {
						Map<?, ?> dataMap = (Map<?, ?>) it.next();
						loanAvgBal1 = Util.nullToSpace(dataMap.get("IN_LN_USE"));
						loanAvgBal += Util.parseLong(loanAvgBal1);
					}
					
					// 授信月平均
					long avgLoanAvgBal = (long) Math.round((loanAvgBal / (double) 12));
					
					// 授信月平均
					totalLoanBal = totalLoanBal.add(new BigDecimal(avgLoanAvgBal));
					
				}
				
				if (isKindA) {
					if (Util.equals(kind, "1")) {
						// 近一年非授信貢獻度
						jsonData.put("bisNoneLoanProfit", totalNoneLoanProfit
								.divide(BigDecimal.valueOf(1000), 0,
										RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());
						// 近一年授信餘額
						jsonData.put("bisLoanBal", totalLoanBal.divide(
								BigDecimal.valueOf(1000), 0,
								RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());

						// 新作增額額度
						jsonData.put("bisFactAmtIncrease", factAmtIncrease.stripTrailingZeros().toPlainString());
					} else if (Util.equals(kind, "3")) {
						// 近一年非授信貢獻度
						jsonData.put("bisNoneLoanProfit_1", totalNoneLoanProfit
								.divide(BigDecimal.valueOf(1000), 0,
										RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());
						// 近一年授信餘額
						jsonData.put("bisLoanBal_1", totalLoanBal.divide(
								BigDecimal.valueOf(1000), 0,
								RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());

						// 新作增額額度
						jsonData.put("bisFactAmtIncrease_1", factAmtIncrease.stripTrailingZeros().toPlainString());
					}
				} else {
					// 近一年非授信貢獻度
					jsonData.put("bisNoneLoanProfit", totalNoneLoanProfit
							.divide(BigDecimal.valueOf(1000), 0,
									RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());
					// 近一年授信餘額
					jsonData.put("bisLoanBal", totalLoanBal.divide(
							BigDecimal.valueOf(1000), 0, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());

					// 新作增額額度
					jsonData.put("bisFactAmtIncrease", factAmtIncrease.stripTrailingZeros().toPlainString());
					// 近一年非授信貢獻度_集團
					jsonData.put("bisNoneLoanProfit_1", BigDecimal.ZERO);
					// 近一年授信餘額_集團
					jsonData.put("bisLoanBal_1", BigDecimal.ZERO);
					// 新作增額額度_集團
					jsonData.put("bisFactAmtIncrease_1", BigDecimal.ZERO);
				}

				// e-Loan簽報書BIS評估表調整海外分行存款貢獻度之年化處理及非授信貢獻度為負時應顯示之警語
				StringBuffer negativeAttrbute = new StringBuffer("");
				if (negativeMap != null && !negativeMap.isEmpty()) {
					for (String nKey : negativeMap.keySet()) {
						negativeAttrbute
								.append((Util.notEquals(
										negativeAttrbute.toString(), "") ? "、"
										: "")).append(nKey);
					}
				}
				jsonData.put("negativeAttrbute", negativeAttrbute.toString());

			} else {
				// L120S08A.error15=關係戶於本行各項業務往來(二維表)無符合之查詢對象
				Properties pop = MessageBundleScriptCreator
						.getComponentResource(LMSCommomPage.class);
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0015", pop.getProperty("L120S08A.error15")),
						getClass());
			}
		}
	}

	// J-103-0419-001 Web e-Loan授信管理系統關係戶往來實績彙總表(ROA表)新增引進風險性資產平均餘額報酬率
	@SuppressWarnings("unused")
	@Override
	public void importL120s04b_Risk_weighted_Assets(String mainId, String custId, String dupNo, String queryDateS,
			String queryDateE, JSONObject jsonData, BranchRate branchRate)
			throws CapException {

		/*
		 * mode1:集團企業2.主借款人3.....
		 */

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		queryDateS = (queryDateS.length() == 7) ? queryDateS + "-01"
				: queryDateS;
		queryDateE = (queryDateE.length() == 7) ? queryDateE + "-01"
				: queryDateE;
		// CapAjaxFormResult result = new CapAjaxFormResult();
		// L120M01A meta = l120m01aDao.findByMainId(mainId);
		List<L120S04A> listL120s04a = this.findL120s04aByMainId(mainId);
		// L120S04B l120s04b = new L120S04B();
		JSONObject json = new JSONObject();
		JSONObject jsonM = new JSONObject();
		JSONObject jsonG = new JSONObject();

		Date dQueryDateS = CapDate.getDate(queryDateS,
				UtilConstants.DateFormat.YYYY_MM_DD);
		Date dQueryDateE = CapDate.getDate(queryDateE,
				UtilConstants.DateFormat.YYYY_MM_DD);
		String MAX_CYC_MN = null;
		String MIN_CYC_MN = null;
		// 管理報表往來彙總查詢改成使用者自行輸入custId與dupNo
		String mCustId = Util.trim(custId);
		String mDupNo = Util.trim(dupNo);
		String _mDupNo = "0".equals(Util.trim(dupNo)) ? "" : Util.trim(dupNo);
		String tGPID = null;
		String tGPName = null;
		String tID = null;
		String tDUPNO = null;
		String tCUSTNAME = null;
		String tGRADE = null;
		String end3Mon = null;
		double bal = 0;

		if (listL120s04a.isEmpty()) {
			// L1205S07.error14 = 必須先執行【引進各關係戶往來彙總】才能執行本功能！
			Properties pop = MessageBundleScriptCreator
					.getComponentResource(LMSS07Panel.class);
			throw new CapMessageException(RespMsgHelper.getMessage(
					"EFD0015", pop.getProperty("L1205S07.error14")), getClass());
		} else {
			// 將除了集團、與關係企業計算合計以外的借款人資料記錄下來
			// a Method
			for (L120S04A model : listL120s04a) {
				String custRel = Util.trim(model.getCustRelation());
				String theDupNo = Util.trim(model.getDupNo());
				StringBuilder sbFullId = new StringBuilder();
				sbFullId.append(Util.trim(model.getCustId())).append(
						Util.trim(theDupNo).equals("0") ? " " : Util
								.trim(theDupNo));
				if (!custRel.contains("3") && !custRel.contains("4")) {
					// 全部名單
					json.put(sbFullId.toString(),
							Util.trim(model.getCustName()));

					// 集團企業
					if (custRel.contains("5")) {
						jsonG.put(sbFullId.toString(),
								Util.trim(model.getCustName()));
					}

					// 主借戶
					if (custRel.contains("1")) {
						if (Util.equals(Util.trim(custId),
								Util.trim(model.getCustId()))) {
							jsonM.put(sbFullId.toString(),
									Util.trim(model.getCustName()));
						}
					}
				}

			}
			// a Method

			Date max_cyc_mn = dwdbService.getOTS_BSL2CSNET_AVG_max_cyc_mn();
			if (max_cyc_mn == null) {
				// L1205S07.error26=資料倉儲無風險性資產平均餘額之資料(DWADM.OTS_BSL2CSNET_AVG)
				Properties pop = MessageBundleScriptCreator
						.getComponentResource(LMSS07Panel.class);
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0015", pop.getProperty("L1205S07.error26")),
						getClass());
			}

			MAX_CYC_MN = CapDate.formatDate(max_cyc_mn,
					UtilConstants.DateFormat.YYYY_MM_DD);
			String _MAX_CYC_MN = MAX_CYC_MN.replace("-", "");
			// 依照風險性資產平均餘額(DWADM.OTS_BSL2CSNET_AVG)最大資料日往前推一年
			dQueryDateE = CapDate.getDate(MAX_CYC_MN,
					UtilConstants.DateFormat.YYYY_MM_DD);
			dQueryDateS = CapDate.getDate(CapDate.formatyyyyMMddToDateFormat(
					CapDate.addMonth(_MAX_CYC_MN, -11),
					UtilConstants.DateFormat.YYYY_MM_DD),
					UtilConstants.DateFormat.YYYY_MM_DD);

			// 主借款人與關係戶
			BigDecimal totalRaBal = BigDecimal.ZERO;
			BigDecimal totalAttrOTSBal = BigDecimal.ZERO;
			BigDecimal totalAttrOVSBal = BigDecimal.ZERO;
			// 主借款人
			BigDecimal totalRaBalM = BigDecimal.ZERO;
			BigDecimal totalAttrOTSBalM = BigDecimal.ZERO;
			BigDecimal totalAttrOVSBalM = BigDecimal.ZERO;
			// 主要集團
			BigDecimal totalRaBalG = BigDecimal.ZERO;
			BigDecimal totalAttrOTSBalG = BigDecimal.ZERO;
			BigDecimal totalAttrOVSBalG = BigDecimal.ZERO;

			// a Method
			if (!json.isEmpty()) {
				for (Object jsonKey : json.keySet()) {
					String sJsonKey = (String) jsonKey;
					String jsonVal = Util.trim(json.get(sJsonKey));
					// 擷取 "+tID+" "+ xr + " 與本行往來實績資料
					tID = (Util.isEmpty(sJsonKey)) ? null : sJsonKey.substring(
							0, sJsonKey.length() - 1);
					tDUPNO = (Util.isEmpty(sJsonKey)) ? null : sJsonKey
							.substring(sJsonKey.length() - 1);
					tCUSTNAME = jsonVal;

					// 風險性資產平均餘額
					List<Map<String, Object>> raBal = this.dwdbService
							.findOTS_BSL2CSNET_AVG_TOTAL_RA_AMT(
									tID,
									tDUPNO,
									CapDate.formatDate(dQueryDateS,
											UtilConstants.DateFormat.YYYY_MM_DD),
									CapDate.formatDate(dQueryDateE,
											UtilConstants.DateFormat.YYYY_MM_DD));

					// 國內貢獻度
					List<Map<String, Object>> attrOTSBal = this.dwdbService
							.findDW_DM_CUBCPCM_TOTAL_ATTRIBUTE(
									tID,
									tDUPNO,
									CapDate.formatDate(dQueryDateS,
											UtilConstants.DateFormat.YYYY_MM_DD),
									CapDate.formatDate(dQueryDateE,
											UtilConstants.DateFormat.YYYY_MM_DD));

					// 海外貢獻度
					List<?> rows8 = this.dwdbService
							.findDW_DM_CUBCPCMOVS_TOTAL_ATTRIBUTE(
									tID,
									tDUPNO,
									CapDate.formatDate(dQueryDateS,
											UtilConstants.DateFormat.YYYY_MM_DD),
									CapDate.formatDate(dQueryDateE,
											UtilConstants.DateFormat.YYYY_MM_DD));
					Iterator<?> it8 = rows8.iterator();

					if (raBal.isEmpty()) {
						// L1205S07.error27 = 引進{0}&nbsp;{1}風險性資產平均餘額錯誤
						Properties pop = MessageBundleScriptCreator
								.getComponentResource(LMSS07Panel.class);
						throw new CapMessageException(RespMsgHelper.getMessage(
								"EFD0015", MessageFormat.format(
										pop.getProperty("L1205S07.error27"),
										tID, tDUPNO)), getClass());
					}
					if (attrOTSBal.isEmpty()) {
						// L1205S07.error24 = 引進{0}&nbsp;{1}利潤貢獻度錯誤
						Properties pop = MessageBundleScriptCreator
								.getComponentResource(LMSS07Panel.class);
						throw new CapMessageException(RespMsgHelper.getMessage(
								"EFD0015", MessageFormat.format(
										pop.getProperty("L1205S07.error24"),
										tID, tDUPNO)), getClass());
					}

					// 合計*******************************************************
					for (Map<String, Object> ra : raBal) {
						bal = Util
								.parseDouble(Util.trim(ra.get("TOTAL_RA_AMT")));
						totalRaBal = totalRaBal.add(new BigDecimal(bal));

						if (Util.notEquals(Util.trim(jsonG.get(sJsonKey)), "")) {
							totalRaBalG = totalRaBalG.add(new BigDecimal(bal));
						}

						if (Util.notEquals(Util.trim(jsonM.get(sJsonKey)), "")) {
							totalRaBalM = totalRaBalM.add(new BigDecimal(bal));
						}

						break;
					}

					for (Map<String, Object> attrOTS : attrOTSBal) {
						bal = Util.parseDouble(Util.trim(attrOTS
								.get("TOTAL_ATTRIBUTE")));
						totalAttrOTSBal = totalAttrOTSBal.add(BigDecimal
								.valueOf(bal));

						if (Util.notEquals(Util.trim(jsonG.get(sJsonKey)), "")) {
							totalAttrOTSBalG = totalAttrOTSBalG.add(BigDecimal
									.valueOf(bal));
						}

						if (Util.notEquals(Util.trim(jsonM.get(sJsonKey)), "")) {
							totalAttrOTSBalM = totalAttrOTSBalM.add(BigDecimal
									.valueOf(bal));
						}

						break;
					}

					// 海外貢獻度(非存款) Miller added at 2012/07/27
					while (it8.hasNext()) {
						Map<?, ?> dataMap8 = (Map<?, ?>) it8.next();
						double seaBal = Util.parseDouble(Util.trim(dataMap8
								.get("TOTAL_ATTRIBUTE")));
						String curr = Util.trim(dataMap8.get("CURR"));
						if (seaBal != 0) {
							seaBal = branchRate.toTWDAmt(
									(Util.isEmpty(curr)) ? "TWD" : curr,
									LMSUtil.toBigDecimal(seaBal)).doubleValue();
						}
						totalAttrOVSBal = totalAttrOVSBal.add(BigDecimal
								.valueOf(seaBal));

						if (Util.notEquals(Util.trim(jsonG.get(sJsonKey)), "")) {
							totalAttrOVSBalG = totalAttrOVSBalG.add(BigDecimal
									.valueOf(seaBal));
						}

						if (Util.notEquals(Util.trim(jsonM.get(sJsonKey)), "")) {
							totalAttrOVSBalM = totalAttrOVSBalM.add(BigDecimal
									.valueOf(seaBal));
						}

					}

					// ************************************************************************
				}

				// 計算 集團風險性資產平均餘額報酬率

				// 主借款人及關係戶
				jsonData.put("Rate5_2", CapDate.formatDate(dQueryDateS, "yyyy")
						+ "/" + CapDate.formatDate(dQueryDateS, "MM"));

				jsonData.put("Rate5_3", CapDate.formatDate(dQueryDateE, "yyyy")
						+ "/" + CapDate.formatDate(dQueryDateE, "MM"));

				if (BigDecimal.ZERO.compareTo(totalRaBal) == 0) {
					jsonData.put("Rate5", "N.A.");
				} else {
					BigDecimal totalAttr = totalAttrOTSBal.add(totalAttrOVSBal);
					if (BigDecimal.ZERO.compareTo(totalAttr) == 0) {
						jsonData.put("Rate5", "0");
					} else {
						BigDecimal mainGrpAvgRate = totalAttr.multiply(
								BigDecimal.valueOf(100)).divide(
								totalRaBal.divide(BigDecimal.valueOf(12), 4,
										BigDecimal.ROUND_HALF_UP), 2,
								BigDecimal.ROUND_HALF_UP);
						jsonData.put("Rate5", mainGrpAvgRate.toString());
					}

				}

				// 主借款人--只有一筆
				jsonData.put("Rate4_2", CapDate.formatDate(dQueryDateS, "yyyy")
						+ "/" + CapDate.formatDate(dQueryDateS, "MM"));

				jsonData.put("Rate4_3", CapDate.formatDate(dQueryDateE, "yyyy")
						+ "/" + CapDate.formatDate(dQueryDateE, "MM"));

				if (BigDecimal.ZERO.compareTo(totalRaBalM) == 0) {
					jsonData.put("Rate4", "N.A.");
				} else {
					BigDecimal totalAttr = totalAttrOTSBalM
							.add(totalAttrOVSBalM);
					if (BigDecimal.ZERO.compareTo(totalAttr) == 0) {
						jsonData.put("Rate4", "0");
					} else {
						BigDecimal mainGrpAvgRate = totalAttr.multiply(
								BigDecimal.valueOf(100)).divide(
								totalRaBalM.divide(BigDecimal.valueOf(12), 4,
										BigDecimal.ROUND_HALF_UP), 2,
								BigDecimal.ROUND_HALF_UP);
						jsonData.put("Rate4", mainGrpAvgRate.toString());
					}

				}

				// 集團企業
				jsonData.put("Rate1_2", CapDate.formatDate(dQueryDateS, "yyyy")
						+ "/" + CapDate.formatDate(dQueryDateS, "MM"));

				jsonData.put("Rate1_3", CapDate.formatDate(dQueryDateE, "yyyy")
						+ "/" + CapDate.formatDate(dQueryDateE, "MM"));

				if (BigDecimal.ZERO.compareTo(totalRaBalG) == 0) {
					jsonData.put("Rate1", "N.A.");
				} else {
					BigDecimal totalAttr = totalAttrOTSBalG
							.add(totalAttrOVSBalG);
					if (BigDecimal.ZERO.compareTo(totalAttr) == 0) {
						jsonData.put("Rate1", "0");
					} else {
						BigDecimal mainGrpAvgRate = totalAttr.multiply(
								BigDecimal.valueOf(100)).divide(
								totalRaBalG.divide(BigDecimal.valueOf(12), 4,
										BigDecimal.ROUND_HALF_UP), 2,
								BigDecimal.ROUND_HALF_UP);
						jsonData.put("Rate1", mainGrpAvgRate.toString());
					}

				}

			}
			// }
		}

		// return result;
	}
}
