<html xmlns="http://www.w3.org/1999/xhtml" 
        xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="panelFragmentBody">
        <script type="text/javascript">
	    		require(['pagejs/lns/LMS2301M01Page'], function(){
	    			loadScript('pagejs/lns/LMS2301S03Panel');
	    		});
	    </script>
            <form action="">
                <button type="button" id="pullinAgain">
                    <span class="text-only"><th:block th:text="#{'button.pullinAgain'}"><!-- 重新引進--></th:block></span>
                </button>
            </form>
            <div id="CntrDocGridView"></div>
            <div id="openCnrtDocBox" name="openCnrtDocBox" style="display:none;">
                <form id="CntrDocForm" name="CntrDocForm">
                    <table class="tb2" style="width:90%">
                        <tr>
                            <td class="hd1" style="width:20%">
                                <span><th:block th:text="#{'l230m01a.status'}"><!-- 狀態註記(新案才需要維護)--></th:block></span>
                            </td>
                            <td>
                                <select id="nuseMemo" name="nuseMemo" combokey="lms2305m01_status" space="true" class="required"></select>
                            </td>
                        </tr>
                        <tr id="signDateTr">
                            <td class="hd1">
                                <span><th:block th:text="#{'l230m01a.signingDay'}"><!-- 已簽約日--></th:block></span>
                            </td>
                            <td>
                                <input type="text" id="signDate" name="signDate" class="date required"/>
                            </td>
                        </tr>
                        <tr id="resonTr">
                            <td class="hd1">
                                <th:block th:text="#{'l230m01a.reason'}"><!-- 不簽約原因--></th:block>
                            </td>
                            <td>
                                <input type="checkbox" name="reason" id="reason" class="required"/>
                                <br/>
                                <span id="reasonDrcSapn"><th:block th:text="#{'l230m01a.reasonDrc'}"><!-- 不簽約原因說明--></th:block>
                                    <br/>
                                    <textarea type="text" name="reasonDrc" id="reasonDrc" maxlengthC="100" class="required" cols="45" rows="3"></textarea>
                                </span>
                            </td>
                        </tr>
						<!--J-111-0551_05097_B1003 Web e-Loan授信之信用風險管理遵循檢核表及借款人暨關係戶與本行授信往來情形及利潤貢獻度納入在途案件之額度-->
                        <tr id="signAmtTr">
							<td class="hd1">
                                <th:block th:text="#{'l230s01a.isSignAmtLowerApplyAmt'}">本案實際簽約額度低於核准額度</th:block>&nbsp;&nbsp;
                            </td>
                            <td>
                            	<th:block th:text="#{'l230m01a.currentApplyAmt'}">現請額度</th:block>：
								<span id="currentApplyCurr" class="field"></span>&nbsp;&nbsp;<span id="currentApplyAmt" class="field numeric"></span>
								<br>
                            	<label>
		                            <input id="isSignAmtLowerApplyAmt" name="isSignAmtLowerApplyAmt" type="radio"  value="Y" class="required"/>
		                              <th:block th:text="#{'yes'}"><!-- 是--></th:block>
		                            </label>
		                            <label>
		                              <input  name="isSignAmtLowerApplyAmt" type="radio"  value="N" class="nodisabled"/>
		                              <th:block th:text="#{'no'}"><!-- 否--></th:block>
		                        </label>
								<br>
                            	<div id="showSignAmt">
									<th:block th:text="#{'l230s01a.signAmt'}">實際簽約額度</th:block>：
									<br>
                            		<th:block th:text="#{'l230s01a.signAmt_S'}">有擔保</th:block>
									<input type="text" id="signAmt_S" name="signAmt_S" size="18" maxlength="22" integer="15" fraction="2" class="numeric required"/>
                                    <th:block th:text="#{'other.money'}"><!-- 元--></th:block>
									<br>
									<th:block th:text="#{'l230s01a.signAmt_N'}">無擔保</th:block>
									<input type="text" id="signAmt_N" name="signAmt_N" size="18" maxlength="22" integer="15" fraction="2" class="numeric required"/>
                                    <th:block th:text="#{'other.money'}"><!-- 元--></th:block>
								</div>
                            </td>
                        </tr>
                    </table>
                </form>
            </div>
		</th:block>
    </body>
</html>
