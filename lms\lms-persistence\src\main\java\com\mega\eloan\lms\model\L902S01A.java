/* 
 * L902S01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 私募基金旗下事業明細檔 **/
@NamedEntityGraph(name = "L902S01A-entity-graph", attributeNodes = { @NamedAttributeNode("l902m01a") })
@Entity
@Table(name="L902S01A", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L902S01A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "MAINID", referencedColumnName = "MAINID", insertable = false, updatable = false)
	private L902M01A l902m01a;

	public L902M01A getL902m01a() {
		return l902m01a;
	}

	public void setL902m01a(L902M01A l902m01a) {
		this.l902m01a = l902m01a;
	}
	
	
	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * 私募基金代碼<p/>
	 * NOT NULL
	 */
	@Column(name="PENO", length=4, columnDefinition="CHAR(4)")
	private String peNo;

	/** 
	 * 客戶統編<p/>
	 * NOT NULL
	 */
	@Size(max=10)
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 
	 * 重覆序號<p/>
	 * NOT NULL
	 */
	@Size(max=1)
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 客戶名稱 **/
	@Size(max=120)
	@Column(name="CUSTNAME", length=120, columnDefinition="VARCHAR(120)")
	private String custName;

	/** 最後異動簽報書MainId **/
	@Size(max=32)
	@Column(name="RPTMAINID", length=32, columnDefinition="CHAR(32)")
	private String rptMainId;
	
	/** 最後異動簽報書案號 **/
	@Size(max=20)
	@Column(name="DOCUMENTNO", length=20, columnDefinition="VARCHAR(20)")
	private String documentNo;

	/** 核准日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ENDDATE", columnDefinition="DATE")
	private Date endDate;

	/** 臨時性客戶統編 **/
	@Size(max=10)
	@Column(name="TEMPCUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String tempCustId;

	/** 臨時性重覆序號 **/
	@Size(max=1)
	@Column(name="TEMPDUPNO", length=1, columnDefinition="CHAR(1)")
	private String tempDupNo;

	/** 臨時性簽報書案號 **/
	@Size(max=20)
	@Column(name="TEMPDOCUMENTNO", length=20, columnDefinition="VARCHAR(20)")
	private String tempDocumentNo;

	/** 臨時性簽報書MainId **/
	@Size(max=32)
	@Column(name="TEMPRPTMAINID", length=32, columnDefinition="CHAR(32)")
	private String tempRptMainId;

	/** 臨時性統編已轉換 **/
	@Size(max=1)
	@Column(name="TEMPEXECFG", length=1, columnDefinition="CHAR(1)")
	private String tempExecFg;

	/** 轉換執行時間 **/
	@Column(name="TEMPEXECTIME", columnDefinition="TIMESTAMP")
	private Timestamp tempExecTime;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String Creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 刪除註記 **/
	@Column(name="DELETEDTIME", columnDefinition="TIMESTAMP")
	private Timestamp deletedTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得私募基金代碼<p/>
	 * NOT NULL
	 */
	public String getPeNo() {
		return this.peNo;
	}
	/**
	 *  設定私募基金代碼<p/>
	 *  NOT NULL
	 **/
	public void setPeNo(String value) {
		this.peNo = value;
	}

	/** 
	 * 取得客戶統編<p/>
	 * NOT NULL
	 */
	public String getCustId() {
		return this.custId;
	}
	/**
	 *  設定客戶統編<p/>
	 *  NOT NULL
	 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 
	 * 取得重覆序號<p/>
	 * NOT NULL
	 */
	public String getDupNo() {
		return this.dupNo;
	}
	/**
	 *  設定重覆序號<p/>
	 *  NOT NULL
	 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得客戶名稱 **/
	public String getCustName() {
		return this.custName;
	}
	/** 設定客戶名稱 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/** 取得最後異動簽報書MainId **/
	public String getRptMainId() {
		return this.rptMainId;
	}
	/** 設定最後異動簽報書MainId **/
	public void setRptMainId(String value) {
		this.rptMainId = value;
	}

	/** 取得核准日期 **/
	public Date getEndDate() {
		return this.endDate;
	}
	/** 設定核准日期 **/
	public void setEndDate(Date value) {
		this.endDate = value;
	}

	/** 取得臨時性客戶統編 **/
	public String getTempCustId() {
		return this.tempCustId;
	}
	/** 設定臨時性客戶統編 **/
	public void setTempCustId(String value) {
		this.tempCustId = value;
	}

	/** 取得臨時性重覆序號 **/
	public String getTempDupNo() {
		return this.tempDupNo;
	}
	/** 設定臨時性重覆序號 **/
	public void setTempDupNo(String value) {
		this.tempDupNo = value;
	}

	/** 取得臨時性簽報書案號 **/
	public String getTempDocumentNo() {
		return this.tempDocumentNo;
	}
	/** 設定臨時性簽報書案號 **/
	public void setTempDocumentNo(String value) {
		this.tempDocumentNo = value;
	}

	/** 取得臨時性簽報書MainId **/
	public String getTempRptMainId() {
		return this.tempRptMainId;
	}
	/** 設定臨時性簽報書MainId **/
	public void setTempRptMainId(String value) {
		this.tempRptMainId = value;
	}

	/** 取得臨時性統編已轉換 **/
	public String getTempExecFg() {
		return this.tempExecFg;
	}
	/** 設定臨時性統編已轉換 **/
	public void setTempExecFg(String value) {
		this.tempExecFg = value;
	}

	/** 取得轉換執行時間 **/
	public Timestamp getTempExecTime() {
		return this.tempExecTime;
	}
	/** 設定轉換執行時間 **/
	public void setTempExecTime(Timestamp value) {
		this.tempExecTime = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.Creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.Creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 取得刪除註記 **/
	public Timestamp getDeletedTime() {
		return this.deletedTime;
	}
	/** 設定刪除註記 **/
	public void setDeletedTime(Timestamp value) {
		this.deletedTime = value;
	}

	/** 設定最後異動簽報書案號 **/
	public void setDocumentNo(String documentNo) {
		this.documentNo = documentNo;
	}

	/** 取得最後異動簽報書案號 **/
	public String getDocumentNo() {
		return documentNo;
	}
}
