package com.mega.eloan.lms.rpt.report.impl;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.dao.L784S07ADao;
import com.mega.eloan.lms.dao.LMSRPTDao;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.model.L784S07A;
import com.mega.eloan.lms.model.LMSRPT;
import com.mega.eloan.lms.rpt.pages.LMS9515V01Page;
import com.mega.eloan.lms.rpt.report.LMS9511R06RptService;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;

import jxl.Sheet;
import jxl.Workbook;
import jxl.format.Alignment;
import jxl.format.Border;
import jxl.format.BorderLineStyle;
import jxl.format.VerticalAlignment;
import jxl.read.biff.BiffException;
import jxl.write.Label;
import jxl.write.NumberFormats;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import jxl.write.WriteException;
import jxl.write.biff.RowsExceededException;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * 產生常董會Excel  918專用產當月利於統計的Excel表
 * 資料內容同LMS9511R02那一張，但樣式不一樣
 * J-111-0329 常董會報告事項彙總及申報案件數統計表可下載成EXCEL檔
 */
@Service("lms9511r06rptservice")
public class LMS9511R06RptServiceImpl implements FileDownloadService, LMS9511R06RptService {
	//復原TFS J-111-0636_05097_B100X
	protected static final Logger LOGGER = LoggerFactory.getLogger(LMS9511R06RptServiceImpl.class);

	@Resource
	BranchService branch;
	
	@Resource
	EloandbBASEService eloandbBASEService;
	
	@Resource
	LMSRPTDao lmsrptDao;

	@Resource
	L784S07ADao l784s07aDao;
	
	Map<String, String> brNameMap;
	
	@Resource
	SysParameterService sysParameterService;
	
	@Override
	public byte[] getContent(PageParameters params) throws FileNotFoundException, IOException, Exception {
		// 等於是LMS9511R02 printType->allByAppr
		// 單月的資料，三個頁籤分別放企金、個金、企+消
		// 只是資料的順序有整理過，Excel的樣式調整成授審處好用公式計算的Excel
		
		ByteArrayOutputStream baos = null;
		try {
			baos = (ByteArrayOutputStream) this.generateXls(params);
			return baos.toByteArray();
		} finally {
			if (baos != null) {
				baos.close();
			}

		}
	}

	private ByteArrayOutputStream generateXls(PageParameters params)
	throws IOException, Exception {
		String mainId = params.getString("mainId", "");//LMSRPT.mainId
		String brNo = params.getString("brNo", "");
		String startDate = params.getString("startDate");
		
		//mainId = "82837b65b86f49698221e72411bbb774";
		//startDate = "2021-05";
		
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		genXls(outputStream, mainId, startDate, brNo);
		
		if (outputStream != null) {
			outputStream.flush();
		}
		return outputStream;
	}

	private void genXls(ByteArrayOutputStream outputStream, String mainId, String startDate, String brNo) throws IOException,
			WriteException, CapException {
		WritableWorkbook workbook = null;
		
		Properties prop = null;
		String apprYY = null;
		String apprMM = null;
		Date dataDate = null;

		apprYY = "";
		apprMM = "";

		LMSRPT lmsRpt = null;
		List<L784S07A> l784s07aLmsList = null;// 企金
		List<Map<String, String>> titleRowsLms = new LinkedList<Map<String, String>>();
		
		List<L784S07A> l784s07aClsList = null;// 個金
		List<Map<String, String>> titleRowsCls = new LinkedList<Map<String, String>>();
		
		List<L784S07A> l784s07aAllList = null;// 企+個
		List<Map<String, String>> titleRowsAll = new LinkedList<Map<String, String>>();
		
		List<L784S07A> l784s07aAllListRepair = null;// 企+個總數for 修補缺少的分行資料用
		
		try {
			lmsRpt = lmsrptDao.findByIndex03(mainId);
			if (lmsRpt == null)
				lmsRpt = new LMSRPT();
			apprYY = TWNDate.toAD(lmsRpt.getDataDate()).split("-")[0];
			apprMM = TWNDate.toAD(lmsRpt.getDataDate()).split("-")[1];

			dataDate = TWNDate.valueOf(startDate + "-01");
			apprYY = TWNDate.toTW(dataDate).split("/")[0];
			apprMM = TWNDate.toTW(dataDate).split("/")[1];
			
			// 1.查資料  DB模式 or Excel模式
			// 2.修補缺少的分行資料
			// 3.刪除簡易分行、總行單位、支行
			// 4.做merge支行
			// 5.排序分區、海外
			// 6.整理欄位資料，算分區小計、合計
			// 7.輸出至Excel各自的頁籤
			
			// 1.查資料  DB模式 or Excel模式
			if(false){
				// 本機Excel模式
				l784s07aLmsList = dummyfindByMainIdApprYM(mainId, apprYY,
						apprMM, "1");// 企金
				l784s07aClsList = dummyfindByMainIdApprYM(mainId, apprYY,
						apprMM, "2");// 企金
				l784s07aAllList = dummyfindByMainIdApprYM(mainId, apprYY,
						apprMM, "3");// 企金
				
				l784s07aAllListRepair = dummyfindByMainIdApprYM(mainId, apprYY,
						apprMM, "3");// 企+個
			}else{
				// 線上查DB模式
				l784s07aLmsList = l784s07aDao.findByMainIdApprYM(mainId, apprYY,
						apprMM, "1");// 企金
				l784s07aClsList = l784s07aDao.findByMainIdApprYM(mainId, apprYY,
						apprMM, "2");// 個金
				l784s07aAllList = l784s07aDao.findByMainIdApprYM(mainId, apprYY,
						apprMM, "3");// 企+個
				
				l784s07aAllListRepair = l784s07aDao.findByMainIdApprYM(mainId, apprYY,
						apprMM, "3");// 企+個
			}
			
			// 2.修補缺少的分行資料
			// 修補企金、個金即可
			l784s07aLmsList = repairL784S07AList(l784s07aAllListRepair, l784s07aLmsList);
			l784s07aClsList = repairL784S07AList(l784s07aAllListRepair, l784s07aClsList);
			
			// 3.刪除簡易分行、總行單位、支行
			l784s07aLmsList = deleteL784S07AList(l784s07aLmsList);
			l784s07aClsList = deleteL784S07AList(l784s07aClsList);
			l784s07aAllList = deleteL784S07AList(l784s07aAllList);

			// 4.做merge支行
			l784s07aLmsList = mergeL784S07AList(l784s07aLmsList);
			l784s07aClsList = mergeL784S07AList(l784s07aClsList);
			l784s07aAllList = mergeL784S07AList(l784s07aAllList);

			// 5.排序分區、海外
			l784s07aLmsList = sortL784S07AList(l784s07aLmsList);
			l784s07aClsList = sortL784S07AList(l784s07aClsList);
			l784s07aAllList = sortL784S07AList(l784s07aAllList);
			
			// 6.整理欄位資料，算分區小計、合計
			prop = MessageBundleScriptCreator
			.getComponentResource(LMS9515V01Page.class);
			
			// 整理欄位資料
			titleRowsLms = this.setL9515type7DataTitleRowsByApprYM(titleRowsLms,
					l784s07aLmsList, prop);
			titleRowsCls = this.setL9515type7DataTitleRowsByApprYM(titleRowsCls,
					l784s07aClsList, prop);
			titleRowsAll = this.setL9515type7DataTitleRowsByApprYM(titleRowsAll,
					l784s07aAllList, prop);
			
			
		} finally {

		}
			
		// ---
		workbook = Workbook.createWorkbook(outputStream);

		WritableFont headFont10 = new WritableFont(
				WritableFont.createFont("標楷體"), 10);
		WritableCellFormat cellFormatL_10 = new WritableCellFormat(
				headFont10);
		{
			cellFormatL_10.setAlignment(Alignment.LEFT);
			cellFormatL_10.setWrap(true);
		}
		// ======
		WritableFont headFont12 = new WritableFont(
				WritableFont.createFont("標楷體"), 12);
		WritableCellFormat cellFormatL = new WritableCellFormat(headFont12);
		{
			cellFormatL.setAlignment(Alignment.LEFT);
			cellFormatL.setWrap(true);
		}

		WritableCellFormat cellFormatR = new WritableCellFormat(headFont12);
		{
			cellFormatR.setAlignment(Alignment.RIGHT);
			cellFormatL.setWrap(true);
		}
		
		WritableCellFormat cellFormatL_NoWrap = new WritableCellFormat(headFont12);
		{
			cellFormatL.setAlignment(Alignment.LEFT);
			cellFormatL.setWrap(false);
		}

		WritableCellFormat cellFormatR_NoWrap = new WritableCellFormat(headFont12);
		{
			cellFormatR.setAlignment(Alignment.RIGHT);
			cellFormatL.setWrap(false);
		}
		

		WritableCellFormat cellFormatL_Border = new WritableCellFormat(
				cellFormatL);
		{
			cellFormatL_Border.setBorder(Border.ALL, BorderLineStyle.THIN);
		}

		WritableCellFormat cellFormatR_Border = new WritableCellFormat(
				cellFormatR);
		{
			cellFormatR_Border.setBorder(Border.ALL, BorderLineStyle.THIN);
		}
		// ======
		WritableFont headFont14 = new WritableFont(
				WritableFont.createFont("標楷體"), 14);
		WritableCellFormat cellFormatC_14 = new WritableCellFormat(
				headFont14);
		{
			cellFormatC_14.setAlignment(Alignment.CENTRE);
			cellFormatC_14.setWrap(true);
		}
		WritableCellFormat amtTWDFormat = new WritableCellFormat(headFont14, NumberFormats.FORMAT1);
		if(true){
			amtTWDFormat.setVerticalAlignment(VerticalAlignment.TOP);
			amtTWDFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
		}
		// ======
		// 7.輸出至Excel各自的頁籤
		// 輸出至Excel各自的頁籤
		writeSheet(workbook, "企金", 0, apprYY, apprMM, cellFormatL_Border, cellFormatL, cellFormatR, cellFormatL_NoWrap, cellFormatR_NoWrap, amtTWDFormat, titleRowsLms);
		writeSheet(workbook, "個金", 1, apprYY, apprMM, cellFormatL_Border, cellFormatL, cellFormatR, cellFormatL_NoWrap, cellFormatR_NoWrap, amtTWDFormat, titleRowsCls);
		writeSheet(workbook, "企金+個金", 2, apprYY, apprMM, cellFormatL_Border, cellFormatL, cellFormatR, cellFormatL_NoWrap, cellFormatR_NoWrap, amtTWDFormat, titleRowsAll);
		
		workbook.write();
		workbook.close();
	}
	
	private void writeSheet(WritableWorkbook workbook, String sheetTitle, int sheetNum, String apprYY, String apprMM, WritableCellFormat cellFormatL_Border,
			WritableCellFormat cellFormatL, WritableCellFormat cellFormatR, WritableCellFormat cellFormatL_NoWrap, WritableCellFormat cellFormatR_NoWrap,	
			WritableCellFormat amtTWDFormat, List<Map<String, String>> titleRowsLms) throws RowsExceededException, WriteException, IOException{
		
		WritableSheet sheet1 = workbook.createSheet(sheetTitle, sheetNum);
		
		// 第0行
		// 第1行
		int rowIdx = 1;
		sheet1.addCell(new Label(1, rowIdx, "附表", cellFormatL_NoWrap));
		sheet1.addCell(new Label(4, rowIdx, "本行各營業單位", cellFormatL_NoWrap));
		sheet1.addCell(new Label(6, rowIdx, apprYY + "年", cellFormatL_NoWrap));
		sheet1.addCell(new Label(7, rowIdx, apprMM + "月份", cellFormatL_NoWrap));
		sheet1.addCell(new Label(8, rowIdx, "常董會報告事項彙總及申報案件數統計表", cellFormatL_NoWrap));

		// 第2行
		rowIdx = 2;
		sheet1.addCell(new Label(8, rowIdx, "單位 : 新台幣千元", cellFormatR_NoWrap));
		
		// 第3行
		rowIdx = 3;
		sheet1.addCell(new Label(0, rowIdx, "", cellFormatL));
		sheet1.addCell(new Label(1, rowIdx, "", cellFormatL_Border));
		sheet1.addCell(new Label(2, rowIdx, "新做", cellFormatL_Border));
		sheet1.mergeCells(2, rowIdx, 5, rowIdx);// 合併單元格
		sheet1.addCell(new Label(6, rowIdx, "續約", cellFormatL_Border));
		sheet1.addCell(new Label(7, rowIdx, "", cellFormatL_Border));
		sheet1.addCell(new Label(8, rowIdx, "變更條件", cellFormatL_Border));
		sheet1.mergeCells(8, rowIdx, 9, rowIdx);// 合併單元格
		sheet1.addCell(new Label(10, rowIdx, "逾放展期  轉正常", cellFormatL_Border));
		sheet1.mergeCells(10, rowIdx, 11, rowIdx);// 合併單元格
		sheet1.addCell(new Label(12, rowIdx, "合計", cellFormatL_Border));
		sheet1.mergeCells(12, rowIdx, 13, rowIdx);// 合併單元格
		sheet1.addCell(new Label(14, rowIdx, "新台幣平均利率", cellFormatL_Border));
		sheet1.mergeCells(14, rowIdx, 14, rowIdx+2);// 合併單元格
		sheet1.addCell(new Label(15, rowIdx, "美金平均利率", cellFormatL_Border));
		sheet1.mergeCells(15, rowIdx, 15, rowIdx+2);// 合併單元格
		sheet1.addCell(new Label(16, rowIdx, "無 擔 保 授 信", cellFormatL_Border));
		sheet1.mergeCells(16, rowIdx, 17, rowIdx);
		sheet1.addCell(new Label(18, rowIdx, "擔 保 授 信", cellFormatL_Border));
		sheet1.mergeCells(18, rowIdx, 19, rowIdx);// 合併單元格
		sheet1.addCell(new Label(20, rowIdx, "錯誤訊息", cellFormatL_Border));
		sheet1.addCell(new Label(21, rowIdx, "錯誤訊息", cellFormatL_Border));
		sheet1.addCell(new Label(22, rowIdx, "申報案件", cellFormatL_Border));
		sheet1.addCell(new Label(23, rowIdx, "核准案件", cellFormatL_Border));
		sheet1.addCell(new Label(24, rowIdx, "授權內案件", cellFormatL_Border));
		sheet1.addCell(new Label(25, rowIdx, "提會案件", cellFormatL_Border));
		sheet1.addCell(new Label(26, rowIdx, "常董會案件", cellFormatL_Border));
		sheet1.addCell(new Label(27, rowIdx, "覆審案件", cellFormatL_Border));
		
		// 第4行
		rowIdx = 4;
		sheet1.addCell(new Label(0, rowIdx, "", cellFormatL));
		sheet1.addCell(new Label(1, rowIdx, "", cellFormatL_Border));
		sheet1.addCell(new Label(2, rowIdx, "筆數", cellFormatL_Border));
		sheet1.mergeCells(2, rowIdx, 4, rowIdx);// 合併單元格
		sheet1.addCell(new Label(5, rowIdx, "金額", cellFormatL_Border));
		sheet1.mergeCells(5, rowIdx, 5, rowIdx+1);// 合併單元格
		sheet1.addCell(new Label(6, rowIdx, "筆數", cellFormatL_Border));
		sheet1.mergeCells(6, rowIdx, 6, rowIdx+1);// 合併單元格
		sheet1.addCell(new Label(7, rowIdx, "金額", cellFormatL_Border));
		sheet1.mergeCells(7, rowIdx, 7, rowIdx+1);// 合併單元格
		sheet1.addCell(new Label(8, rowIdx, "筆數", cellFormatL_Border));
		sheet1.mergeCells(8, rowIdx, 8, rowIdx+1);// 合併單元格
		sheet1.addCell(new Label(9, rowIdx, "金額", cellFormatL_Border));
		sheet1.mergeCells(9, rowIdx, 9, rowIdx+1);// 合併單元格
		sheet1.addCell(new Label(10, rowIdx, "筆數", cellFormatL_Border));
		sheet1.mergeCells(10, rowIdx, 10, rowIdx+1);// 合併單元格
		sheet1.addCell(new Label(11, rowIdx, "金額", cellFormatL_Border));
		sheet1.mergeCells(11, rowIdx, 11, rowIdx+1);// 合併單元格
		sheet1.addCell(new Label(12, rowIdx, "筆數", cellFormatL_Border));
		sheet1.mergeCells(12, rowIdx, 12, rowIdx+1);// 合併單元格
		sheet1.addCell(new Label(13, rowIdx, "金額", cellFormatL_Border));
		sheet1.mergeCells(13, rowIdx, 13, rowIdx+1);// 合併單元格
		sheet1.addCell(new Label(16, rowIdx, "筆數", cellFormatL_Border));
		sheet1.mergeCells(16, rowIdx, 16, rowIdx+1);// 合併單元格
		sheet1.addCell(new Label(17, rowIdx, "金額", cellFormatL_Border));
		sheet1.mergeCells(17, rowIdx, 17, rowIdx+1);// 合併單元格
		sheet1.addCell(new Label(18, rowIdx, "筆數", cellFormatL_Border));
		sheet1.mergeCells(18, rowIdx, 18, rowIdx+1);// 合併單元格
		sheet1.addCell(new Label(19, rowIdx, "金額", cellFormatL_Border));
		sheet1.mergeCells(19, rowIdx, 19, rowIdx+1);// 合併單元格
		sheet1.addCell(new Label(20, rowIdx, "", cellFormatL_Border));
		sheet1.addCell(new Label(21, rowIdx, "", cellFormatL_Border));
		sheet1.addCell(new Label(22, rowIdx, "", cellFormatL_Border));
		sheet1.addCell(new Label(23, rowIdx, "", cellFormatL_Border));
		sheet1.addCell(new Label(24, rowIdx, "", cellFormatL_Border));
		sheet1.addCell(new Label(25, rowIdx, "", cellFormatL_Border));
		sheet1.addCell(new Label(26, rowIdx, "", cellFormatL_Border));
		sheet1.addCell(new Label(27, rowIdx, "", cellFormatL_Border));
		
		// 第5行
		rowIdx = 5;
		sheet1.addCell(new Label(1, rowIdx, "行名", cellFormatL_Border));
		sheet1.addCell(new Label(2, rowIdx, "新戶", cellFormatL_Border));
		sheet1.addCell(new Label(3, rowIdx, "舊戶", cellFormatL_Border));
		sheet1.addCell(new Label(4, rowIdx, "合計", cellFormatL_Border));
		sheet1.addCell(new Label(20, rowIdx, "", cellFormatL_Border));
		sheet1.addCell(new Label(21, rowIdx, "", cellFormatL_Border));
		sheet1.addCell(new Label(22, rowIdx, "", cellFormatL_Border));
		sheet1.addCell(new Label(23, rowIdx, "", cellFormatL_Border));
		sheet1.addCell(new Label(24, rowIdx, "", cellFormatL_Border));
		sheet1.addCell(new Label(25, rowIdx, "", cellFormatL_Border));
		sheet1.addCell(new Label(26, rowIdx, "", cellFormatL_Border));
		sheet1.addCell(new Label(27, rowIdx, "", cellFormatL_Border));
		
		// 純設定每一欄的欄寬
		Map<String, Integer> headerMap = new LinkedHashMap<String, Integer>();
		headerMap.put("A", 15);
		headerMap.put("B", 15);
		headerMap.put("C", 15);
		headerMap.put("D", 15);
		headerMap.put("E", 15);
		headerMap.put("F", 15);
		headerMap.put("G", 15);
		headerMap.put("H", 15);
		headerMap.put("I", 15);
		headerMap.put("J", 15);
		headerMap.put("K", 15);
		headerMap.put("L", 15);
		headerMap.put("M", 15);
		headerMap.put("N", 15);
		headerMap.put("O", 15);
		headerMap.put("P", 15);
		headerMap.put("Q", 15);
		headerMap.put("R", 15);
		headerMap.put("S", 15);
		headerMap.put("T", 15);
		headerMap.put("U", 15);
		headerMap.put("V", 15);
		headerMap.put("W", 15);
		headerMap.put("X", 15);
		headerMap.put("Y", 15);
		headerMap.put("Z", 15);
		headerMap.put("AA", 15);
		headerMap.put("AB", 15);

		// ==============================
		if(true){
			int colIdx = 0;
			for (String h : headerMap.keySet()) {
				int colWidth = headerMap.get(h);
				sheet1.setColumnView(colIdx, colWidth);
				// ---
				colIdx++;
			}
		}
		// ==============================
		
		int totalColSize = headerMap.size();

		List<String[]> rows = new ArrayList<String[]>();
		
		for (Map<String, String> map_row : titleRowsLms) {				
			// ---
			String[] arr = new String[totalColSize];
			for (int i_col = 0; i_col < totalColSize; i_col++) {
				arr[i_col] = "";
			}
			//分行代號
			arr[0] = Util.trim(MapUtils.getString(map_row, "ReportBean.column00"));
			//行名
			arr[1] = Util.trim(MapUtils.getString(map_row, "ReportBean.column01"));
			//新做 新戶
			arr[2] = Util.trim(MapUtils.getString(map_row, "ReportBean.column22"));
			//新做 舊戶
			arr[3] = Util.trim(MapUtils.getString(map_row, "ReportBean.column24"));
			//新做 合計
			arr[4] = Util.trim(MapUtils.getString(map_row, "ReportBean.column02"));
			//新做 金額
			arr[5] = Util.trim(MapUtils.getString(map_row, "ReportBean.column03"));
			//續約 筆數
			arr[6] = Util.trim(MapUtils.getString(map_row, "ReportBean.column04"));
			//續約 金額
			arr[7] = Util.trim(MapUtils.getString(map_row, "ReportBean.column05"));
			//變更條件 筆數
			arr[8] = Util.trim(MapUtils.getString(map_row, "ReportBean.column06"));
			//變更條件 金額
			arr[9] = Util.trim(MapUtils.getString(map_row, "ReportBean.column07"));
			//逾放展期  轉正常	 筆數
			arr[10] = Util.trim(MapUtils.getString(map_row, "ReportBean.column08"));
			//逾放展期  轉正常	 金額
			arr[11] = Util.trim(MapUtils.getString(map_row, "ReportBean.column09"));
			//合計 筆數
			arr[12] = Util.trim(MapUtils.getString(map_row, "ReportBean.column10"));
			//合計 金額
			arr[13] = Util.trim(MapUtils.getString(map_row, "ReportBean.column11"));
			//新台幣平均利率
			arr[14] = "";
			//美金平均利率
			arr[15] = "";
			//無 擔 保 授 信 筆數
			arr[16] = Util.trim(MapUtils.getString(map_row, "ReportBean.column12"));
			//無 擔 保 授 信 金額
			arr[17] = Util.trim(MapUtils.getString(map_row, "ReportBean.column13"));
			//擔 保 授 信 筆數
			arr[18] = Util.trim(MapUtils.getString(map_row, "ReportBean.column14"));
			//擔 保 授 信 金額
			arr[19] = Util.trim(MapUtils.getString(map_row, "ReportBean.column15"));
			//錯誤訊息
			arr[20] = "";
			//錯誤訊息
			arr[21] = "";
			//申報案件
			arr[22] = Util.trim(MapUtils.getString(map_row, "ReportBean.column16"));
			//核准案件
			arr[23] = Util.trim(MapUtils.getString(map_row, "ReportBean.column17"));
			//授權內案件
			arr[24] = Util.trim(MapUtils.getString(map_row, "ReportBean.column18"));
			//提會案件
			arr[25] = Util.trim(MapUtils.getString(map_row, "ReportBean.column19"));
			//常董會案件
			arr[26] = Util.trim(MapUtils.getString(map_row, "ReportBean.column20"));
			//覆審案件
			arr[27] = Util.trim(MapUtils.getString(map_row, "ReportBean.column21"));
			// ---
			rows.add(arr);
		}
		
		Integer[] isStringColumn = {0, 1, 14, 15, 20, 21};
		List<Integer> isStringColumnList = Arrays.asList(isStringColumn);
		if(true){
			// 資料開始的row
			rowIdx = 6;
			int i_row = 0;
			for (String[] arr : rows) {				

				// 不套用數值format的行數!!(整行都不套用) ex:海外分行前補一行"國內分行平均利率"
				boolean isStringRow = false;
				// 目前先寫死看會有什麼欄位可以當判斷
				if("國內分行平均利率".equals(arr[1])){
					isStringRow = true;
				}
				
				for(int i_col = 0; i_col<totalColSize; i_col++){
					if(!isStringColumnList.contains(i_col) && !isStringRow){
						sheet1.addCell(new jxl.write.Number(i_col, rowIdx + i_row, Integer.valueOf(arr[i_col]) , amtTWDFormat));
					}else if(i_col == 0){
						sheet1.addCell(new Label(i_col, rowIdx + i_row, arr[i_col], cellFormatR));
					}else{
						sheet1.addCell(new Label(i_col, rowIdx + i_row, arr[i_col], cellFormatL_Border));
					}
				}				
				// ---
				i_row++;
			}
		}
		// ---
	}

	/**
	 * 從LMS9511R02RptServiceImpl抄來的，因為還要微調，就搬過來自己這支改
	 * @param titleRows
	 * @param list
	 * @param propV01
	 * @param printType
	 * @return
	 * @throws CapException
	 */
	private List<Map<String, String>> setL9515type7DataTitleRowsByApprYM(
			List<Map<String, String>> titleRows, List<L784S07A> list,
			Properties propV01) throws CapException {
		try {

			Map<String, String> mapInTitleRows = null;
			
			// for分區合計
			BigDecimal areaColumn02 = BigDecimal.ZERO;
			BigDecimal areaColumn03 = BigDecimal.ZERO;
			BigDecimal areaColumn04 = BigDecimal.ZERO;
			BigDecimal areaColumn05 = BigDecimal.ZERO;
			BigDecimal areaColumn06 = BigDecimal.ZERO;
			BigDecimal areaColumn07 = BigDecimal.ZERO;
			BigDecimal areaColumn08 = BigDecimal.ZERO;
			BigDecimal areaColumn09 = BigDecimal.ZERO;
			BigDecimal areaColumn10 = BigDecimal.ZERO;
			BigDecimal areaColumn11 = BigDecimal.ZERO;
			BigDecimal areaColumn12 = BigDecimal.ZERO;
			BigDecimal areaColumn13 = BigDecimal.ZERO;
			BigDecimal areaColumn14 = BigDecimal.ZERO;
			BigDecimal areaColumn15 = BigDecimal.ZERO;
			BigDecimal areaColumn16 = BigDecimal.ZERO;
			BigDecimal areaColumn17 = BigDecimal.ZERO;
			BigDecimal areaColumn18 = BigDecimal.ZERO;
			BigDecimal areaColumn19 = BigDecimal.ZERO;
			BigDecimal areaColumn20 = BigDecimal.ZERO;
			BigDecimal areaColumn21 = BigDecimal.ZERO;

			BigDecimal areaColumn22 = BigDecimal.ZERO;
			BigDecimal areaColumn23 = BigDecimal.ZERO;
			BigDecimal areaColumn24 = BigDecimal.ZERO;
			BigDecimal areaColumn25 = BigDecimal.ZERO;
			
			// for整份合計
			BigDecimal totalColumn02 = BigDecimal.ZERO;
			BigDecimal totalColumn03 = BigDecimal.ZERO;
			BigDecimal totalColumn04 = BigDecimal.ZERO;
			BigDecimal totalColumn05 = BigDecimal.ZERO;
			BigDecimal totalColumn06 = BigDecimal.ZERO;
			BigDecimal totalColumn07 = BigDecimal.ZERO;
			BigDecimal totalColumn08 = BigDecimal.ZERO;
			BigDecimal totalColumn09 = BigDecimal.ZERO;
			BigDecimal totalColumn10 = BigDecimal.ZERO;
			BigDecimal totalColumn11 = BigDecimal.ZERO;
			BigDecimal totalColumn12 = BigDecimal.ZERO;
			BigDecimal totalColumn13 = BigDecimal.ZERO;
			BigDecimal totalColumn14 = BigDecimal.ZERO;
			BigDecimal totalColumn15 = BigDecimal.ZERO;
			BigDecimal totalColumn16 = BigDecimal.ZERO;
			BigDecimal totalColumn17 = BigDecimal.ZERO;
			BigDecimal totalColumn18 = BigDecimal.ZERO;
			BigDecimal totalColumn19 = BigDecimal.ZERO;
			BigDecimal totalColumn20 = BigDecimal.ZERO;
			BigDecimal totalColumn21 = BigDecimal.ZERO;

			BigDecimal totalColumn22 = BigDecimal.ZERO;
			BigDecimal totalColumn23 = BigDecimal.ZERO;
			BigDecimal totalColumn24 = BigDecimal.ZERO;
			BigDecimal totalColumn25 = BigDecimal.ZERO;

			mapInTitleRows = this.setColumnMap2("");

			String tempArea = "";
			for (int i = 0; i < list.size(); i++) {
				L784S07A l784s07a = list.get(i);
				String brno = null;
				String column01 = null;
				BigDecimal column02 = null;
				BigDecimal column03 = null;
				BigDecimal column04 = null;
				BigDecimal column05 = null;
				BigDecimal column06 = null;
				BigDecimal column07 = null;
				BigDecimal column08 = null;
				BigDecimal column09 = null;
				BigDecimal column10 = null;
				BigDecimal column11 = null;
				BigDecimal column12 = null;
				BigDecimal column13 = null;
				BigDecimal column14 = null;
				BigDecimal column15 = null;
				BigDecimal column16 = null;
				BigDecimal column17 = null;
				BigDecimal column18 = null;
				BigDecimal column19 = null;
				BigDecimal column20 = null;
				BigDecimal column21 = null;

				BigDecimal column22 = null;
				BigDecimal column23 = null;
				BigDecimal column24 = null;
				BigDecimal column25 = null;

				// 分行別
				brno = Util.nullToSpace(l784s07a.getBrNo());
				IBranch thisBranch = branch.getBranch(Util.trim(brno));
				String brNoArea = Util.trim(thisBranch.getBrNoArea());
				//007國外部、025國金分行、201金控總部分行自己歸在一類
				if(brno.equals("007") || brno.equals("025") ||
						brno.equals("201") ||
						brno.equals("149") ){
					brNoArea = "9";
				}
				
				//===================分區開始
				// 代表換區了，先把分區合計做輸出
				// 還要把分區合計的欄位都清零
				
				// 第一筆的area先給值
				if(tempArea.equals("")){
					tempArea = Util.trim(thisBranch.getBrNoArea());
				}
				if(thisBranch != null && !brNoArea.equals(tempArea)){
					mapInTitleRows = this.setColumnMap2("");
					mapInTitleRows.put("ReportBean.column01", "小計");
					mapInTitleRows.put("ReportBean.column02",
							this.formatBigDecimal(areaColumn02));
					mapInTitleRows.put("ReportBean.column03",
							this.formatBigDecimal(areaColumn03));
					mapInTitleRows.put("ReportBean.column04",
							this.formatBigDecimal(areaColumn04));
					mapInTitleRows.put("ReportBean.column05",
							this.formatBigDecimal(areaColumn05));
					mapInTitleRows.put("ReportBean.column06",
							this.formatBigDecimal(areaColumn06));
					mapInTitleRows.put("ReportBean.column07",
							this.formatBigDecimal(areaColumn07));
					mapInTitleRows.put("ReportBean.column08",
							this.formatBigDecimal(areaColumn08));
					mapInTitleRows.put("ReportBean.column09",
							this.formatBigDecimal(areaColumn09));
					mapInTitleRows.put("ReportBean.column10",
							this.formatBigDecimal(areaColumn10));
					mapInTitleRows.put("ReportBean.column11",
							this.formatBigDecimal(areaColumn11));
					mapInTitleRows.put("ReportBean.column12",
							this.formatBigDecimal(areaColumn12));
					mapInTitleRows.put("ReportBean.column13",
							this.formatBigDecimal(areaColumn13));
					mapInTitleRows.put("ReportBean.column14",
							this.formatBigDecimal(areaColumn14));
					mapInTitleRows.put("ReportBean.column15",
							this.formatBigDecimal(areaColumn15));
					mapInTitleRows.put("ReportBean.column16",
							this.formatBigDecimal(areaColumn16));
					mapInTitleRows.put("ReportBean.column17",
							this.formatBigDecimal(areaColumn17));
					mapInTitleRows.put("ReportBean.column18",
							this.formatBigDecimal(areaColumn18));
					mapInTitleRows.put("ReportBean.column19",
							this.formatBigDecimal(areaColumn19));
					mapInTitleRows.put("ReportBean.column20",
							this.formatBigDecimal(areaColumn20));
					mapInTitleRows.put("ReportBean.column21",
							this.formatBigDecimal(areaColumn21));

					mapInTitleRows.put("ReportBean.column22",
							this.formatBigDecimal(areaColumn22));
					mapInTitleRows.put("ReportBean.column23",
							this.formatBigDecimal(areaColumn23));
					mapInTitleRows.put("ReportBean.column24",
							this.formatBigDecimal(areaColumn24));
					mapInTitleRows.put("ReportBean.column25",
							this.formatBigDecimal(areaColumn25));

					titleRows.add(mapInTitleRows);
					
					areaColumn02 = BigDecimal.ZERO;
					areaColumn03 = BigDecimal.ZERO;
					areaColumn04 = BigDecimal.ZERO;
					areaColumn05 = BigDecimal.ZERO;
					areaColumn06 = BigDecimal.ZERO;
					areaColumn07 = BigDecimal.ZERO;
					areaColumn08 = BigDecimal.ZERO;
					areaColumn09 = BigDecimal.ZERO;
					areaColumn10 = BigDecimal.ZERO;
					areaColumn11 = BigDecimal.ZERO;
					areaColumn12 = BigDecimal.ZERO;
					areaColumn13 = BigDecimal.ZERO;
					areaColumn14 = BigDecimal.ZERO;
					areaColumn15 = BigDecimal.ZERO;
					areaColumn16 = BigDecimal.ZERO;
					areaColumn17 = BigDecimal.ZERO;
					areaColumn18 = BigDecimal.ZERO;
					areaColumn19 = BigDecimal.ZERO;
					areaColumn20 = BigDecimal.ZERO;
					areaColumn21 = BigDecimal.ZERO;

					areaColumn22 = BigDecimal.ZERO;
					areaColumn23 = BigDecimal.ZERO;
					areaColumn24 = BigDecimal.ZERO;
					areaColumn25 = BigDecimal.ZERO;

					// 第一家海外分行前空一列，以便本處填入「國內分行平均利率」
					// 所以在"9" 國外部、國金分行、金控總部分行 三家小計後插入一筆
					if("9".equals(tempArea)){
						mapInTitleRows = this.setColumnMap2("");
						mapInTitleRows.put("ReportBean.column01", "國內分行平均利率");
						titleRows.add(mapInTitleRows);
					}
				}
				tempArea = brNoArea;// 用tempArea存放目前在處理哪一區
				//===================分區結束

				//===================單筆開始
				String changeName = changeBranchName(brno);// 海外會置換為分行名稱，ex荷蘭行、倫敦行
				if(Util.isEmpty(changeName)){
					column01 = Util.nullToSpace(branch.getBranchName(Util.trim(brno)));
				}else{
					column01 = changeName;
				}
				// 新做
				column02 = LMSUtil.toBigDecimal(l784s07a.getCItem1Rec());
				column03 = LMSUtil.toBigDecimal(l784s07a.getCItem1Amt());
				// 續約
				column04 = LMSUtil.toBigDecimal(l784s07a.getCItem2Rec());
				column05 = LMSUtil.toBigDecimal(l784s07a.getCItem2Amt());
				// 變更條件
				column06 = LMSUtil.toBigDecimal(l784s07a.getCItem3Rec());
				column07 = LMSUtil.toBigDecimal(l784s07a.getCItem3Amt());
				// 逾放展期、轉正常
				column08 = LMSUtil.toBigDecimal(l784s07a.getCItem12Rec());
				column09 = LMSUtil.toBigDecimal(l784s07a.getCItem12Amt());
				// 無擔保授信
				column12 = LMSUtil.toBigDecimal(l784s07a.getCItem4Rec());
				column13 = LMSUtil.toBigDecimal(l784s07a.getCItem4Amt());
				// 擔保授信
				column14 = LMSUtil.toBigDecimal(l784s07a.getCItem5Rec());
				column15 = LMSUtil.toBigDecimal(l784s07a.getCItem5Amt());
				// 合計
				column10 = this.bigAdd(column02, column04);
				column10 = this.bigAdd(column10, column06);
				column10 = this.bigAdd(column10, column08);

				column11 = this.bigAdd(column03, column05);
				column11 = this.bigAdd(column11, column07);
				column11 = this.bigAdd(column11, column09);

				column16 = LMSUtil.toBigDecimal(l784s07a.getCItem6Rec());
				column17 = LMSUtil.toBigDecimal(l784s07a.getCItem7Rec());
				column18 = LMSUtil.toBigDecimal(l784s07a.getCItem8Rec());
				column19 = LMSUtil.toBigDecimal(l784s07a.getCItem9Rec());
				column20 = LMSUtil.toBigDecimal(l784s07a.getCItem10Rec());
				column21 = LMSUtil.toBigDecimal(l784s07a.getCItem11Rec());

				// 新做新客戶
				column22 = LMSUtil.toBigDecimal(l784s07a.getCItem1NewRec());
				column23 = LMSUtil.toBigDecimal(l784s07a.getCItem1NewAmt());
				// 新做舊客戶
				column24 = LMSUtil.toBigDecimal(l784s07a.getCItem1OldRec());
				column25 = LMSUtil.toBigDecimal(l784s07a.getCItem1OldAmt());
				
				mapInTitleRows = this.setColumnMap2("");
				mapInTitleRows.put("ReportBean.column00", Util.trim(brno));
				mapInTitleRows.put("ReportBean.column01", column01);
				mapInTitleRows.put("ReportBean.column02",
						this.formatBigDecimal(column02));
				mapInTitleRows.put("ReportBean.column03",
						this.formatBigDecimal(column03));
				mapInTitleRows.put("ReportBean.column04",
						this.formatBigDecimal(column04));
				mapInTitleRows.put("ReportBean.column05",
						this.formatBigDecimal(column05));
				mapInTitleRows.put("ReportBean.column06",
						this.formatBigDecimal(column06));
				mapInTitleRows.put("ReportBean.column07",
						this.formatBigDecimal(column07));
				mapInTitleRows.put("ReportBean.column08",
						this.formatBigDecimal(column08));
				mapInTitleRows.put("ReportBean.column09",
						this.formatBigDecimal(column09));
				mapInTitleRows.put("ReportBean.column10",
						this.formatBigDecimal(column10));
				mapInTitleRows.put("ReportBean.column11",
						this.formatBigDecimal(column11));
				mapInTitleRows.put("ReportBean.column12",
						this.formatBigDecimal(column12));
				mapInTitleRows.put("ReportBean.column13",
						this.formatBigDecimal(column13));
				mapInTitleRows.put("ReportBean.column14",
						this.formatBigDecimal(column14));
				mapInTitleRows.put("ReportBean.column15",
						this.formatBigDecimal(column15));
				mapInTitleRows.put("ReportBean.column16",
						this.formatBigDecimal(column16));
				mapInTitleRows.put("ReportBean.column17",
						this.formatBigDecimal(column17));
				mapInTitleRows.put("ReportBean.column18",
						this.formatBigDecimal(column18));
				mapInTitleRows.put("ReportBean.column19",
						this.formatBigDecimal(column19));
				mapInTitleRows.put("ReportBean.column20",
						this.formatBigDecimal(column20));
				mapInTitleRows.put("ReportBean.column21",
						this.formatBigDecimal(column21));

				mapInTitleRows.put("ReportBean.column22",
						this.formatBigDecimal(column22));
				mapInTitleRows.put("ReportBean.column23",
						this.formatBigDecimal(column23));
				mapInTitleRows.put("ReportBean.column24",
						this.formatBigDecimal(column24));
				mapInTitleRows.put("ReportBean.column25",
						this.formatBigDecimal(column25));

				titleRows.add(mapInTitleRows);
				//===================單筆結束
				
				
				// for分區合計
				//===================分區累加開始
				areaColumn02 = this.bigAdd(areaColumn02, column02);
				areaColumn03 = this.bigAdd(areaColumn03, column03);
				areaColumn04 = this.bigAdd(areaColumn04, column04);
				areaColumn05 = this.bigAdd(areaColumn05, column05);
				areaColumn06 = this.bigAdd(areaColumn06, column06);
				areaColumn07 = this.bigAdd(areaColumn07, column07);
				areaColumn08 = this.bigAdd(areaColumn08, column08);
				areaColumn09 = this.bigAdd(areaColumn09, column09);
				areaColumn10 = this.bigAdd(areaColumn10, column10);
				areaColumn11 = this.bigAdd(areaColumn11, column11);
				areaColumn12 = this.bigAdd(areaColumn12, column12);
				areaColumn13 = this.bigAdd(areaColumn13, column13);
				areaColumn14 = this.bigAdd(areaColumn14, column14);
				areaColumn15 = this.bigAdd(areaColumn15, column15);
				areaColumn16 = this.bigAdd(areaColumn16, column16);
				areaColumn17 = this.bigAdd(areaColumn17, column17);
				areaColumn18 = this.bigAdd(areaColumn18, column18);
				areaColumn19 = this.bigAdd(areaColumn19, column19);
				areaColumn20 = this.bigAdd(areaColumn20, column20);
				areaColumn21 = this.bigAdd(areaColumn21, column21);

				areaColumn22 = this.bigAdd(areaColumn22, column22);
				areaColumn23 = this.bigAdd(areaColumn23, column23);
				areaColumn24 = this.bigAdd(areaColumn24, column24);
				areaColumn25 = this.bigAdd(areaColumn25, column25);
				//===================整份累加開始
				
				// for整份合計
				//===================整份累加開始
				totalColumn02 = this.bigAdd(totalColumn02, column02);
				totalColumn03 = this.bigAdd(totalColumn03, column03);
				totalColumn04 = this.bigAdd(totalColumn04, column04);
				totalColumn05 = this.bigAdd(totalColumn05, column05);
				totalColumn06 = this.bigAdd(totalColumn06, column06);
				totalColumn07 = this.bigAdd(totalColumn07, column07);
				totalColumn08 = this.bigAdd(totalColumn08, column08);
				totalColumn09 = this.bigAdd(totalColumn09, column09);
				totalColumn10 = this.bigAdd(totalColumn10, column10);
				totalColumn11 = this.bigAdd(totalColumn11, column11);
				totalColumn12 = this.bigAdd(totalColumn12, column12);
				totalColumn13 = this.bigAdd(totalColumn13, column13);
				totalColumn14 = this.bigAdd(totalColumn14, column14);
				totalColumn15 = this.bigAdd(totalColumn15, column15);
				totalColumn16 = this.bigAdd(totalColumn16, column16);
				totalColumn17 = this.bigAdd(totalColumn17, column17);
				totalColumn18 = this.bigAdd(totalColumn18, column18);
				totalColumn19 = this.bigAdd(totalColumn19, column19);
				totalColumn20 = this.bigAdd(totalColumn20, column20);
				totalColumn21 = this.bigAdd(totalColumn21, column21);

				totalColumn22 = this.bigAdd(totalColumn22, column22);
				totalColumn23 = this.bigAdd(totalColumn23, column23);
				totalColumn24 = this.bigAdd(totalColumn24, column24);
				totalColumn25 = this.bigAdd(totalColumn25, column25);
				//===================整份累加開始
				
				
			}
			
			//===================最後一筆(海外)小計那列結束
			mapInTitleRows = this.setColumnMap2("");
			mapInTitleRows.put("ReportBean.column01", "小計");
			mapInTitleRows.put("ReportBean.column02",
					this.formatBigDecimal(areaColumn02));
			mapInTitleRows.put("ReportBean.column03",
					this.formatBigDecimal(areaColumn03));
			mapInTitleRows.put("ReportBean.column04",
					this.formatBigDecimal(areaColumn04));
			mapInTitleRows.put("ReportBean.column05",
					this.formatBigDecimal(areaColumn05));
			mapInTitleRows.put("ReportBean.column06",
					this.formatBigDecimal(areaColumn06));
			mapInTitleRows.put("ReportBean.column07",
					this.formatBigDecimal(areaColumn07));
			mapInTitleRows.put("ReportBean.column08",
					this.formatBigDecimal(areaColumn08));
			mapInTitleRows.put("ReportBean.column09",
					this.formatBigDecimal(areaColumn09));
			mapInTitleRows.put("ReportBean.column10",
					this.formatBigDecimal(areaColumn10));
			mapInTitleRows.put("ReportBean.column11",
					this.formatBigDecimal(areaColumn11));
			mapInTitleRows.put("ReportBean.column12",
					this.formatBigDecimal(areaColumn12));
			mapInTitleRows.put("ReportBean.column13",
					this.formatBigDecimal(areaColumn13));
			mapInTitleRows.put("ReportBean.column14",
					this.formatBigDecimal(areaColumn14));
			mapInTitleRows.put("ReportBean.column15",
					this.formatBigDecimal(areaColumn15));
			mapInTitleRows.put("ReportBean.column16",
					this.formatBigDecimal(areaColumn16));
			mapInTitleRows.put("ReportBean.column17",
					this.formatBigDecimal(areaColumn17));
			mapInTitleRows.put("ReportBean.column18",
					this.formatBigDecimal(areaColumn18));
			mapInTitleRows.put("ReportBean.column19",
					this.formatBigDecimal(areaColumn19));
			mapInTitleRows.put("ReportBean.column20",
					this.formatBigDecimal(areaColumn20));
			mapInTitleRows.put("ReportBean.column21",
					this.formatBigDecimal(areaColumn21));
			
			mapInTitleRows.put("ReportBean.column22",
					this.formatBigDecimal(areaColumn22));
			mapInTitleRows.put("ReportBean.column23",
					this.formatBigDecimal(areaColumn23));
			mapInTitleRows.put("ReportBean.column24",
					this.formatBigDecimal(areaColumn24));
			mapInTitleRows.put("ReportBean.column25",
					this.formatBigDecimal(areaColumn25));
			
			titleRows.add(mapInTitleRows);
			//===================最後一筆(海外)小計那列結束

			//===================整份那列合計開始
			mapInTitleRows = this.setColumnMap2(propV01
					.getProperty("month.total"));
			mapInTitleRows.put("ReportBean.column01", "合計");
			mapInTitleRows.put("ReportBean.column02",
					this.formatBigDecimal(totalColumn02));
			mapInTitleRows.put("ReportBean.column03",
					this.formatBigDecimal(totalColumn03));
			mapInTitleRows.put("ReportBean.column04",
					this.formatBigDecimal(totalColumn04));
			mapInTitleRows.put("ReportBean.column05",
					this.formatBigDecimal(totalColumn05));
			mapInTitleRows.put("ReportBean.column06",
					this.formatBigDecimal(totalColumn06));
			mapInTitleRows.put("ReportBean.column07",
					this.formatBigDecimal(totalColumn07));
			mapInTitleRows.put("ReportBean.column08",
					this.formatBigDecimal(totalColumn08));
			mapInTitleRows.put("ReportBean.column09",
					this.formatBigDecimal(totalColumn09));
			mapInTitleRows.put("ReportBean.column10",
					this.formatBigDecimal(totalColumn10));
			mapInTitleRows.put("ReportBean.column11",
					this.formatBigDecimal(totalColumn11));
			mapInTitleRows.put("ReportBean.column12",
					this.formatBigDecimal(totalColumn12));
			mapInTitleRows.put("ReportBean.column13",
					this.formatBigDecimal(totalColumn13));
			mapInTitleRows.put("ReportBean.column14",
					this.formatBigDecimal(totalColumn14));
			mapInTitleRows.put("ReportBean.column15",
					this.formatBigDecimal(totalColumn15));
			mapInTitleRows.put("ReportBean.column16",
					this.formatBigDecimal(totalColumn16));
			mapInTitleRows.put("ReportBean.column17",
					this.formatBigDecimal(totalColumn17));
			mapInTitleRows.put("ReportBean.column18",
					this.formatBigDecimal(totalColumn18));
			mapInTitleRows.put("ReportBean.column19",
					this.formatBigDecimal(totalColumn19));
			mapInTitleRows.put("ReportBean.column20",
					this.formatBigDecimal(totalColumn20));
			mapInTitleRows.put("ReportBean.column21",
					this.formatBigDecimal(totalColumn21));

			mapInTitleRows.put("ReportBean.column22",
					this.formatBigDecimal(totalColumn22));
			mapInTitleRows.put("ReportBean.column23",
					this.formatBigDecimal(totalColumn23));
			mapInTitleRows.put("ReportBean.column24",
					this.formatBigDecimal(totalColumn24));
			mapInTitleRows.put("ReportBean.column25",
					this.formatBigDecimal(totalColumn25));

			titleRows.add(mapInTitleRows);
			//===================整份那列合計結束

		} catch (Exception e) {
			LOGGER.debug("setL9515type7DataTitleRowsByApprYM exception", e);
			throw new CapException();
		}
		return titleRows;

	}
	
	private String changeBranchName(String brNo){
		if(brNameMap == null){
			brNameMap = new HashMap<String, String>();
			brNameMap.put("0A2","紐約行");
			brNameMap.put("0A3","芝行");
			brNameMap.put("0A4","洛行");
			brNameMap.put("0M2","矽谷行");
			brNameMap.put("0D7","多倫多分行");
			brNameMap.put("0D8","溫哥華分行");
			brNameMap.put("0A5","巴行");
			brNameMap.put("0A7","東京行");
			brNameMap.put("0A8","大阪行");
			brNameMap.put("0C3","香港行");
			brNameMap.put("0B2","岷行");
			brNameMap.put("0B6","越南行");
			brNameMap.put("Y01","泰國行");
			brNameMap.put("0D9","仰光行");
			brNameMap.put("0B8","星行");
			brNameMap.put("0C1","納閩行");
			brNameMap.put("0C7","金邊行");
			brNameMap.put("0C8","蘇州行");
			brNameMap.put("0D3","寧波行");
			brNameMap.put("0B0","法國行");
			brNameMap.put("0B1","荷蘭行");
			brNameMap.put("0C4","倫敦行");
			brNameMap.put("0B9","雪梨行");
			brNameMap.put("0C2","東澳行");
			brNameMap.put("0C5","墨行");
		}
		
		String newBrName = brNameMap.get(brNo);
		return Util.trim(newBrName);
	}
	
	/**
	 * 修補缺少的分行資料，用企金+個金的L784S07A當作最大數量
	 * @param l784s07aList
	 * @return
	 */
	private List<L784S07A> repairL784S07AList(List<L784S07A> l784s07aListAll, List<L784S07A> l784s07aListThis){
		
		// 現行list有什麼資料先裝進map
		Map<String, L784S07A> l784s07aMapThis = new HashMap<String, L784S07A>();
		for(L784S07A l784s07aThis : l784s07aListThis){
			l784s07aMapThis.put(l784s07aThis.getBrNo(), l784s07aThis);
		}
		
		
		List<L784S07A> cleanList = new ArrayList<L784S07A>();
		
		// 以企金+個金資料為base，看有哪些分行資料需要補
		for(L784S07A l784s07aAll : l784s07aListAll){
			L784S07A l784s07a = l784s07aMapThis.get(l784s07aAll.getBrNo());
			if(l784s07a != null){
				cleanList.add(l784s07a);
			}else{
				cleanList.add(initL784S07A(l784s07aAll.getBrNo()));
			}
		}
		
		return cleanList;
	}
	
	/**
	 * 刪除不要的分行資料
	 * @param l784s07aList
	 * @return
	 */
	private List<L784S07A> deleteL784S07AList(List<L784S07A> l784s07aList){
		
		List<L784S07A> cleanList = new ArrayList<L784S07A>();
		// 940 企金業務處
		// List<String> needDelete = Arrays.asList(new String[]{"011", "089", "0R5", "0R8", "0R9", "940"});
		String sysDelete = Util.trim(sysParameterService.getParamValue("J-111-0329-DELETE"));
		List<String> needDelete = Arrays.asList(sysDelete.split(","));

		for(L784S07A l784s07a : l784s07aList){

			// 1.刪除簡易型分行如：086駐外交部簡易分行、078成功簡易型分行、081中鋼簡易型分行、116高港簡易型分行。
			IBranch thisBranch = branch.getBranch(Util.trim(l784s07a.getBrNo()));
			String brNo = thisBranch.getBrNo();
			String brClass = thisBranch.getBrClass();
			
			if("Y".equals(brClass)){
				// 不加入
				
			}else{
				// 2.刪除011財務處、089保險代理人處等非屬分行之總行單位。
				// 3.刪除0R5孟買代表處及0R8仰光代表處及0R8海防代表處
				// 是0不是O喔...
				if(needDelete.contains(brNo)){
					// 不加入
					
				}else{
					
					cleanList.add(l784s07a);
				}
			}
		}
		
		return cleanList;
	}
	
	/**
	 * 配合常董會報告，彙整海外支行
	 * 如：金邊行合計=OC6+OC7+OC9+OD2+OD4 蘇州行合計=OC8+OD1+OD5 泰行合計=Y01+Y02+Y03+Y04+Y05
	 * 等於只看到金邊行，看不到"支行"
	 * @param l784s07aList
	 * @return
	 */
	private List<L784S07A> mergeL784S07AList(List<L784S07A> l784s07aList){
		List<L784S07A> cleanList = new ArrayList<L784S07A>();
		
		// 0C7 金邊行
		// List<String> merge0C7 = Arrays.asList(new String[]{"0C6", "0C7", "0C9", "0D2", "0D4"});
		String sys0C7 = Util.trim(sysParameterService.getParamValue("J-111-0329-0C7"));
		List<String> merge0C7 = Arrays.asList(sys0C7.split(","));
		L784S07A br0C7 = initL784S07A("0C7");
		
		// 0C8 蘇州行
		// List<String> merge0C8 = Arrays.asList(new String[]{"0C8", "0D1", "0D5"});
		String sys0C8 = Util.trim(sysParameterService.getParamValue("J-111-0329-0C8"));
		List<String> merge0C8 = Arrays.asList(sys0C8.split(","));
		L784S07A br0C8 = initL784S07A("0C8");
		
		// Y01 泰國行
		// List<String> mergeY01 = Arrays.asList(new String[]{"Y01", "Y02", "Y03", "Y04", "Y05"});
		String sysY01 = Util.trim(sysParameterService.getParamValue("J-111-0329-Y01"));
		List<String> mergeY01 = Arrays.asList(sysY01.split(","));
		L784S07A brY01 = initL784S07A("Y01");
		
		for(L784S07A l784s07a : l784s07aList){
			String brNo = Util.trim(l784s07a.getBrNo());

			if(merge0C7.contains(brNo)){
				megerL784S07A(br0C7, l784s07a);
				
			}else if(merge0C8.contains(brNo)){
				megerL784S07A(br0C8, l784s07a);
				
			}else if(mergeY01.contains(brNo)){
				megerL784S07A(brY01, l784s07a);
				
			}else{
				// 跟上述三個彙整支行都沒關係的分行，就直接放回list
				cleanList.add(l784s07a);
			}
			
		}
		
		// 把三個彙整好的也放回list
		cleanList.add(br0C7);
		cleanList.add(br0C8);
		cleanList.add(brY01);
		
		return cleanList;
	}
	
	private L784S07A initL784S07A(String brNo){
		L784S07A total = new L784S07A();
		total.setBrNo(brNo);
		total.setCItem1Rec(new Integer(0));
		total.setCItem1Amt(BigDecimal.ZERO);
		total.setCItem2Rec(new Integer(0));
		total.setCItem2Amt(BigDecimal.ZERO);
		total.setCItem3Rec(new Integer(0));
		total.setCItem3Amt(BigDecimal.ZERO);
		total.setCItem12Rec(new Integer(0));
		total.setCItem12Amt(BigDecimal.ZERO);
		total.setCItem4Rec(new Integer(0));
		total.setCItem4Amt(BigDecimal.ZERO);
		total.setCItem5Rec(new Integer(0));
		total.setCItem5Amt(BigDecimal.ZERO);
		total.setCItem6Rec(new Integer(0));
		total.setCItem7Rec(new Integer(0));
		total.setCItem8Rec(new Integer(0));
		total.setCItem9Rec(new Integer(0));
		total.setCItem10Rec(new Integer(0));
		total.setCItem11Rec(new Integer(0));
		total.setCItem1NewRec(new Integer(0));
		total.setCItem1NewAmt(BigDecimal.ZERO);
		total.setCItem1OldRec(new Integer(0));
		total.setCItem1OldAmt(BigDecimal.ZERO);
		
		return total;
	}
	/**
	 * L784S07A 合併加總用，只塞EXCEL需要的欄位資料
	 * @param total
	 * @param single
	 * @return
	 */
	private L784S07A megerL784S07A(L784S07A total, L784S07A single){
		
		total.setCItem1Rec(intAdd(total.getCItem1Rec(), single.getCItem1Rec()));
		total.setCItem1Amt(bigAdd(total.getCItem1Amt(), single.getCItem1Amt()));
		total.setCItem2Rec(intAdd(total.getCItem2Rec(), single.getCItem2Rec()));
		total.setCItem2Amt(bigAdd(total.getCItem2Amt(), single.getCItem2Amt()));
		total.setCItem3Rec(intAdd(total.getCItem3Rec(), single.getCItem3Rec()));
		total.setCItem3Amt(bigAdd(total.getCItem3Amt(), single.getCItem3Amt()));
		total.setCItem12Rec(intAdd(total.getCItem12Rec(), single.getCItem12Rec()));
		total.setCItem12Amt(bigAdd(total.getCItem12Amt(), single.getCItem12Amt()));
		total.setCItem4Rec(intAdd(total.getCItem4Rec(), single.getCItem4Rec()));
		total.setCItem4Amt(bigAdd(total.getCItem4Amt(), single.getCItem4Amt()));
		total.setCItem5Rec(intAdd(total.getCItem5Rec(), single.getCItem5Rec()));
		total.setCItem5Amt(bigAdd(total.getCItem5Amt(), single.getCItem5Amt()));
		total.setCItem6Rec(intAdd(total.getCItem6Rec(), single.getCItem6Rec()));
		total.setCItem7Rec(intAdd(total.getCItem7Rec(), single.getCItem7Rec()));
		total.setCItem8Rec(intAdd(total.getCItem8Rec(), single.getCItem8Rec()));
		total.setCItem9Rec(intAdd(total.getCItem9Rec(), single.getCItem9Rec()));
		total.setCItem10Rec(intAdd(total.getCItem10Rec(), single.getCItem10Rec()));
		total.setCItem11Rec(intAdd(total.getCItem11Rec(), single.getCItem11Rec()));
		total.setCItem1NewRec(intAdd(total.getCItem1NewRec(), single.getCItem1NewRec()));
		total.setCItem1NewAmt(bigAdd(total.getCItem1NewAmt(), single.getCItem1NewAmt()));
		total.setCItem1OldRec(intAdd(total.getCItem1OldRec(), single.getCItem1OldRec()));
		total.setCItem1OldAmt(bigAdd(total.getCItem1OldAmt(), single.getCItem1OldAmt()));
		
		return total;
	}
	
	/**
	 * 配合常董會報告，需要依照各分區做小計，所以要先將資料做分區
	 * 海外放最後
	 * @param l784s07aList
	 * @return
	 */
	private List<L784S07A> sortL784S07AList(List<L784S07A> l784s07aList){
		Map<String, List<L784S07A>> sortMap = new HashMap<String, List<L784S07A>>();
		for(L784S07A l784s07a : l784s07aList){
			IBranch thisBranch = branch.getBranch(Util.trim(l784s07a.getBrNo()));
			String brNoArea = thisBranch.getBrNoArea();

			//007國外部、025國金分行、201金控總部分行自己歸在一類
			if(l784s07a.getBrNo().equals("007") || l784s07a.getBrNo().equals("025") ||
					l784s07a.getBrNo().equals("201") ||
					l784s07a.getBrNo().equals("149")){
				brNoArea = "9";
			}
			
			List<L784S07A> sortList = sortMap.get(brNoArea);
			if(sortList == null){
				sortList = new ArrayList<L784S07A>();
			}
			
			sortList.add(l784s07a);
			sortMap.put(brNoArea, sortList);
		}
		
		// 照數字從map取出該area的list放回總list中
		// 未來調這裡的值域即可
		List<L784S07A> allList = new ArrayList<L784S07A>();
		String[] areas = {"1","6","2","3","9","4"};
		
		for(int i=0; i<areas.length; i++){
			List<L784S07A> sortList = sortMap.get(areas[i]);
			if(sortList != null){
				allList.addAll(sortList);
			}
		}
		
		return allList;
	}
	
	private BigDecimal bigAdd(BigDecimal number1, BigDecimal number2) {
		if (number1 == null) {
			number1 = BigDecimal.ZERO;
		}
		if (number2 == null) {
			number2 = BigDecimal.ZERO;
		}
		return number1.add(number2);
	}
	
	private Integer intAdd(Integer number1, Integer number2) {
		if (number1 == null) {
			number1 = 0;
		}
		if (number2 == null) {
			number2 = 0;
		}
		return number1 + number2;
	}
	
	private String formatBigDecimal(BigDecimal number) {
		if (number == null) {
			return "0";
		} else if ("0".equals(String.valueOf(number))) {
			return "0";
		} else {
			// return NumConverter.addComma(number);
			return String.valueOf(number.intValue());
		}
	}
	
	/**
	 * 初始化map 資料，將所有的rportBean的資料初使化，避免少了而產生exception
	 * 
	 * @return Map<String, String> 多值MAP
	 */
	private Map<String, String> setColumnMap2(String month) {
		Map<String, String> values = new LinkedHashMap<String, String>();
		values.put("ReportBean.column01", month);
		for (int i = 2; i <= 60; i++) {
			values.put("ReportBean.column" + String.format("%02d", i), "");
		}
		return values;
	}
			
	// for 本機Excel!!!!!!
	private List<L784S07A> dummyfindByMainIdApprYM(String mainId, String apprYY, String apprMM, String caseDept){
		List<L784S07A> list = new ArrayList<L784S07A>();
		String filePath = "D:/xxxxxxxxx/case" + caseDept + ".xls";
		Workbook workbook = null;
		try {
			workbook = Workbook.getWorkbook(new File(filePath));
		} catch (BiffException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
		Sheet sheet = workbook.getSheet(0);
		for (int row = 1; row < sheet.getRows(); row++) {
			L784S07A l784s07a = new L784S07A();
			
			l784s07a.setBrNo(Util.trim(sheet.getCell(2, row).getContents()));
			l784s07a.setCItem1Rec(getIntger(Util.trim(sheet.getCell(6, row).getContents())));
			l784s07a.setCItem1Amt(getBigDecimal(Util.trim(sheet.getCell(7, row).getContents())));
			l784s07a.setCItem2Rec(getIntger(Util.trim(sheet.getCell(8, row).getContents())));
			l784s07a.setCItem2Amt(getBigDecimal(Util.trim(sheet.getCell(9, row).getContents())));
			l784s07a.setCItem3Rec(getIntger(Util.trim(sheet.getCell(10, row).getContents())));
			l784s07a.setCItem3Amt(getBigDecimal(Util.trim(sheet.getCell(11, row).getContents())));
			l784s07a.setCItem12Rec(getIntger(Util.trim(sheet.getCell(28, row).getContents())));
			l784s07a.setCItem12Amt(getBigDecimal(Util.trim(sheet.getCell(29, row).getContents())));
			l784s07a.setCItem4Rec(getIntger(Util.trim(sheet.getCell(12, row).getContents())));
			l784s07a.setCItem4Amt(getBigDecimal(Util.trim(sheet.getCell(13, row).getContents())));
			l784s07a.setCItem5Rec(getIntger(Util.trim(sheet.getCell(14, row).getContents())));
			l784s07a.setCItem5Amt(getBigDecimal(Util.trim(sheet.getCell(15, row).getContents())));
			l784s07a.setCItem6Rec(getIntger(Util.trim(sheet.getCell(16, row).getContents())));
			l784s07a.setCItem7Rec(getIntger(Util.trim(sheet.getCell(18, row).getContents())));
			l784s07a.setCItem8Rec(getIntger(Util.trim(sheet.getCell(20, row).getContents())));
			l784s07a.setCItem9Rec(getIntger(Util.trim(sheet.getCell(22, row).getContents())));
			l784s07a.setCItem10Rec(getIntger(Util.trim(sheet.getCell(24, row).getContents())));
			l784s07a.setCItem11Rec(getIntger(Util.trim(sheet.getCell(26, row).getContents())));
			l784s07a.setCItem1NewRec(getIntger(Util.trim(sheet.getCell(34, row).getContents())));
			l784s07a.setCItem1NewAmt(getBigDecimal(Util.trim(sheet.getCell(35, row).getContents())));
			l784s07a.setCItem1OldRec(getIntger(Util.trim(sheet.getCell(36, row).getContents())));
			l784s07a.setCItem1OldAmt(getBigDecimal(Util.trim(sheet.getCell(37, row).getContents())));
			
			list.add(l784s07a);
		}
		
		return list;
	}
	
	private Integer getIntger(String value){
		if(Util.isEmpty(value)){
			return new Integer(0);
		}else{
			return new Integer(value);
		}
	}
	
	private BigDecimal getBigDecimal(String value){
		if(Util.isEmpty(value)){
			return new BigDecimal(0);
		}else{
			return new BigDecimal(value);
		}
	}
}
