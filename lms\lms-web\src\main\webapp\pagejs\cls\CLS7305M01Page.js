$(document).ready(function(){
});

function queryL730m01a(){
    $.ajax({
        type: "POST",
        handler: "lms1205formhandler",
        data: {
            formAction: "queryL730M01A",
            mainid: responseJSON.mainId
        },
        success: function(responseData){
            $("#formopenLmsCase").reset();
            $("#formopenLmsCase").setData(responseData.formopenLmsCase, false);
            // 控制分頁頁籤內容唯讀(不包括下拉式選單)
            if (responseJSON.mainDocStatus != "L1H" && responseJSON.mainDocStatus != "L1C") {
                if (responseJSON.readOnly == "true") {
                    $("#formopenLmsCase").readOnlyChilds(true);
                    $("#formopenLmsCase").find("button").hide();
                }
            }
            //開啟授信報案考核表
            openLmsCase();
        }
    });
}

//開啟授信報案考核表ThickBox
function openLmsCase(){
    var tran = responseJSON.docURL.toString().substring(5);
    var title = "";
    if (tran == "lms1205m01") {
        title = i18n.lms1205m01['l120m01a.title20'];
    }
    else 
        if (tran == "lms1215m01") {
            title = i18n.lms1215m01['l120m01a.title20'];
        }
        else 
            if (tran == "lms1105m01") {
                title = i18n.lms1105m01['l120m01a.title20'];
            }
            else 
                if (tran == "lms1115m01") {
                    title = i18n.lms1115m01['l120m01a.title20'];
                }
                else 
                    if (tran == "lms1305m01") {
                        title = i18n.lms1305m01['l120m01a.title20'];
                    }
    // 控制ThickBox唯讀狀態
    if (responseJSON.mainDocStatus == "L1H" || responseJSON.mainDocStatus == "L1C") {
        thickboxOptions.readOnly = false;
    }
    else {
        thickReadOnly();
    }
    $("#openLmsCase").thickbox({
        //l120m01a.title20=授信報案考核表
        title: title,
        width: 960,
        height: 480,
        modal: true,
        i18n: i18n.def,
        buttons: {
            "saveData": function(){
                var $formopenLmsCase = $("#formopenLmsCase");
                if ($formopenLmsCase.valid()) {
                    var tScore = 0;
                    //單項分數群組
                    var itemScores = [];
                    //扣分群組
                    var itemAlls = [];
                    //計算授信報案考核表扣分與總扣分
                    $formopenLmsCase.find(".mathit").sort(function (a, b) {
	                	//中間插入了別的號碼 所以排序後再抓
	                    var aname = window.util.addZeroBefore($(a).attr("name").substring(7), 3);
	                    var bname = window.util.addZeroBefore($(b).attr("name").substring(7), 3);
	                    return (aname > bname) ? 1 : ((aname < bname) ? -1 : 0);
	                }).each(function(i){
                        var $this = $(this);
                        var mul1 = $this.parent().parent().find("td").next().html();
                        var mul2 = $this.val();
                        itemScores.push(mul1);
                        itemAlls.push(mul1 * mul2);
                        $this.parent().next().html(mul1 * mul2);
                    });
                    
                    $formopenLmsCase.find(".totalit").sort(function (a, b) {
	                	//中間插入了別的號碼 所以排序後再抓
	                    var aname = window.util.addZeroBefore($(a).attr("name").substring(8), 3);
	                    var bname = window.util.addZeroBefore($(b).attr("name").substring(8), 3);
	                    return (aname > bname) ? 1 : ((aname < bname) ? -1 : 0);
	                }).each(function(j){
                        if ($(this).html().length == 0) {
                            tScore += 0;
                        }
                        else {
                            tScore += parseInt($(this).html(), 10);
                        }
                    });
                    
                    //將計算後的總扣分結果設定到畫面上
                    $formopenLmsCase.find("#tmGrade").html(tScore);
                    
                    $.ajax({
                        type: "POST",
                        handler: "lms1205formhandler",
                        data: {
                            formAction: "saveL730M01A",
                            mainid: responseJSON.mainId,
                            formopenLmsCase: JSON.stringify($formopenLmsCase.serializeData()),
                            tmGrade: $formopenLmsCase.find("#tmGrade").html(),
                            itemScores: itemScores,
                            itemAlls: itemAlls
                        },
                        success: function(responseData){
                            var $formopenLmsCase = $("#formopenLmsCase");
                            //alert(JSON.stringify(responseData.formopenLmsCase));s
                            //LMS1205S01Form
                            //$formopenLmsCase.reset();
                            $formopenLmsCase.setData(responseData.formopenLmsCase, false);
                            //$.thickbox.close();
                        }
                    });
                }
            },
            "print": function(){
                //saveBeforePrint=執行列印將自動儲存資料，是否繼續此動作? 
                CommonAPI.confirmMessage(i18n.def["saveBeforePrint"], function(b){
                    if (b) {
                        var $formopenLmsCase = $("#formopenLmsCase");
                        if ($formopenLmsCase.valid()) {
                            var tScore = 0;
                            //單項分數群組
                            var itemScores = [];
                            //扣分群組
                            var itemAlls = [];
                            //計算授信報案考核表扣分與總扣分
                            $formopenLmsCase.find(".mathit").find(".mathit").sort(function (a, b) {
                            	//J-113-0260 於國內外e-loan簽報書「營業單位授信報案考核表」增加LGD試算建檔項目
        	                	//中間插入了別的號碼 所以排序後再抓
        	                    var aname = window.util.addZeroBefore($(a).attr("name").substring(7), 3);
        	                    var bname = window.util.addZeroBefore($(b).attr("name").substring(7), 3);
        	                    return (aname > bname) ? 1 : ((aname < bname) ? -1 : 0);
        	                }).each(function(i){
                                var $this = $(this);
                                var mul1 = $this.parent().parent().find("td").next().html();
                                var mul2 = $this.val();
                                itemScores.push(mul1);
                                itemAlls.push(mul1 * mul2);
                                $this.parent().next().html(mul1 * mul2);
                            });
                            
                            $formopenLmsCase.find(".totalit").sort(function (a, b) {
                            	//J-113-0260 於國內外e-loan簽報書「營業單位授信報案考核表」增加LGD試算建檔項目
        	                	//中間插入了別的號碼 所以排序後再抓
        	                    var aname = window.util.addZeroBefore($(a).attr("name").substring(8), 3);
        	                    var bname = window.util.addZeroBefore($(b).attr("name").substring(8), 3);
        	                    return (aname > bname) ? 1 : ((aname < bname) ? -1 : 0);
        	                }).each(function(j){
                                if ($(this).html().length == 0) {
                                    tScore += 0;
                                }
                                else {
                                    tScore += parseInt($(this).html(), 10);
                                }
                            });
                            
                            //將計算後的總扣分結果設定到畫面上
                            $formopenLmsCase.find("#tmGrade").html(tScore);
                            
                            $.ajax({
                                type: "POST",
                                handler: "lms1205formhandler",
                                data: {
                                    formAction: "saveL730M01A",
                                    mainid: responseJSON.mainId,
                                    formopenLmsCase: JSON.stringify($formopenLmsCase.serializeData()),
                                    tmGrade: $formopenLmsCase.find("#tmGrade").html(),
                                    itemScores: itemScores,
                                    itemAlls: itemAlls
                                },
                                success: function(responseData){
                                    var $formopenLmsCase = $("#formopenLmsCase");
                                    //alert(JSON.stringify(responseData.formopenLmsCase));s
                                    //LMS1205S01Form
                                    //$formopenLmsCase.reset();
                                    $formopenLmsCase.setData(responseData.formopenLmsCase, false);
                                    //$.thickbox.close();
                                    var pdfName = "l120r01.pdf";
                                    var count = 0;
                                    var content = "";
                                    content = "R17" + "^" + "";
                                    $.form.submit({
                                        url: "../../simple/FileProcessingService",
                                        target: "_blank",
                                        data: {
                                            mainId: responseJSON.mainId,
                                            rptOid: content,
                                            fileDownloadName: pdfName,
                                            serviceName: "lms1205r01rptservice"
                                        }
                                    });
                                }
                            });
                        }
                    }
                });
            },
            "close": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}
