package com.mega.eloan.lms.cls.report.impl;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.lms.base.common.ClsUtility;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.cls.common.ClsUtil;
import com.mega.eloan.lms.cls.report.CLS1220R01RptService;
import com.mega.eloan.lms.cls.service.CLS1220Service;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.C120S01B;
import com.mega.eloan.lms.model.C122M01A;

import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.PdfTools;
import tw.com.jcs.common.report.ReportGenerator;

/**
 * 員工紓困列印
 */
@Service("cls1220r03rptservice")
public class CLS1220R03RptServiceImpl implements FileDownloadService, CLS1220R01RptService {

	protected static final Logger LOGGER = LoggerFactory.getLogger(CLS1220R03RptServiceImpl.class);

	@Resource
	CLS1220Service cls1220Service;

	@Resource
	CodeTypeService codeTypeService;

	@Override
	public byte[] getContent(PageParameters params) throws FileNotFoundException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
			baos = (ByteArrayOutputStream) this.generateReport(params);
			return baos.toByteArray();
		} finally {
			if (baos != null) {
				baos.close();
			}

		}
	}

	private OutputStream generateReport(PageParameters params) throws FileNotFoundException, IOException, Exception {

		List<InputStream> list = new LinkedList<InputStream>();
		String[] dataSplit = Util.trim(params.getString("rptOid")).split("\\|");
		OutputStream outputStream = null;
		Locale locale = null;

		try {
			locale = LMSUtil.getLocale();
			int subLine = 9;//此數值對應的(x,y).要查 PdfTools.並不一定是愈小,愈上面
			for (String temp : dataSplit) {
				Map<InputStream, Integer> pdfNameMap = new LinkedHashMap<InputStream, Integer>();
				outputStream = null;
				String oid = temp.split("\\^")[0];
				C122M01A c122m01a = cls1220Service.getC122M01A_byOid(oid);
				outputStream = genCLS1220R03(locale, c122m01a);
				if (outputStream != null) {
					pdfNameMap.put(new ByteArrayInputStream(((ByteArrayOutputStream) outputStream).toByteArray()),
							subLine);
				} else {
					pdfNameMap.put(null, subLine);
				}

				if (pdfNameMap != null && pdfNameMap.size() > 0) {
					outputStream = new ByteArrayOutputStream();
					PdfTools.mergeReWritePagePdf(pdfNameMap, outputStream, "", true, locale, subLine);
					list.add(new ByteArrayInputStream(((ByteArrayOutputStream) outputStream).toByteArray()));
				}
			}
			outputStream = new ByteArrayOutputStream();
			PdfTools.mergeReWritePagePdf(list, outputStream);

		} finally {

		}
		return outputStream;
	}

	public OutputStream genCLS1220R03(Locale locale,
			C122M01A c122m01a) throws FileNotFoundException, IOException, Exception {
		OutputStream outputStream = null;

		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		List<Map<String, String>> rptrowList = new ArrayList<Map<String, String>>();

		ReportGenerator generator = new ReportGenerator("report/cls/CLS1220R03_" + locale.toString() + ".rpt");

		String agreeQueryEJ = Util.trim(c122m01a.getAgreeQueryEJ());
		if ("".equals(agreeQueryEJ)) {
			generator = new ReportGenerator("report/cls/CLS1220R03_" + locale.toString() + ".rpt");
		} else if ("Y".equals(agreeQueryEJ)) {
			if(LMSUtil.cmpDate(c122m01a.getCreateTime(), ">=", CapDate.parseDate(ClsUtility.get_labor_bailout_2021_since_date()))){
				generator = new ReportGenerator("report/cls/CLS1220R03_Y2021_" + locale.toString() + ".rpt");
			}else{
				generator = new ReportGenerator("report/cls/CLS1220R03_Y2020_" + locale.toString() + ".rpt");	
			}			
		} else if ("N".equals(agreeQueryEJ)) {
			generator = new ReportGenerator("report/cls/CLS1220R03_N_" + locale.toString() + ".rpt");
		}

		C120S01A c120s01a = cls1220Service.findC120S01A(c122m01a.getMainId(), c122m01a.getCustId(),
				c122m01a.getDupNo());
		C120S01B c120s01b = cls1220Service.findC120S01B(c122m01a.getMainId(), c122m01a.getCustId(),
				c122m01a.getDupNo());
		if (c120s01a == null) {
			c120s01a = new C120S01A();
		}
		if (c120s01b == null) {
			c120s01b = new C120S01B();
		}
		Map<String, String> lms1205s01_jobTitle = codeTypeService.findByCodeType("lms1205s01_jobTitle");

		//戶名
		rptVariableMap.put("custName", CapString.trimNull(c122m01a.getCustName()));
		//住家電話
		rptVariableMap.put("coTel", CapString.trimNull(c120s01a.getCoTel()));
		//婚姻
		rptVariableMap.put("marry", CapString.trimNull(c120s01a.getMarry()));
		//子女
		rptVariableMap.put("child", CapString.trimNull(c120s01a.getChild()));
		//手機
		rptVariableMap.put("mTel", CapString.trimNull(c120s01a.getMTel()));
		//統編
		rptVariableMap.put("custId", CapString.trimNull(c122m01a.getCustId()));
		//email
		rptVariableMap.put("email", CapString.trimNull(c120s01a.getEmail()));
		//生日
		if (c120s01a.getBirthday() != null) {
			String yyyy = CapDate.formatDate(c120s01a.getBirthday(), "yyyy");
			String mm = CapDate.formatDate(c120s01a.getBirthday(), "M");
			String dd = CapDate.formatDate(c120s01a.getBirthday(), "d");
			rptVariableMap.put("birthday", "民國" + (Integer.parseInt(yyyy) - 1911) + "年" + mm + "月" + dd + "日");
		} else {
			rptVariableMap.put("birthday", "民國    年    月    日");
		}
		//教育程度
		rptVariableMap.put("edu", CapString.trimNull(c120s01a.getEdu()));
		//其它收入來源
		rptVariableMap.put("othType", CapString.trimNull(c120s01b.getOthType()));
		//其它收入
		rptVariableMap.put("othAmt", CapString.trimNull(c120s01b.getOthAmt()));
		//服務單位名稱
		rptVariableMap.put("comName", CapString.trimNull(c120s01b.getComName()));
		//年資
		rptVariableMap.put("seniority", LMSUtil.pretty_numStr(ClsUtility.floor_seniorityYM_to_yearVal_because_outer_system((c120s01b.getSeniority()))));
		//職稱
		rptVariableMap.put("jobTitle", CapString.trimNull(lms1205s01_jobTitle.get(c120s01b.getJobTitle())));
		//年薪
		rptVariableMap.put("payAmt", CapString.trimNull(
				c120s01b.getPayAmt() == null ? "" : c120s01b.getPayAmt().stripTrailingZeros().toPlainString()));
		//電話
		rptVariableMap.put("comTel", CapString.trimNull(c120s01b.getComTel()));
		//戶籍地
		String fTarget = CapString.trimNull(c120s01a.getFTarget());
		if ("--請選擇--".equals(fTarget)) {
			fTarget = "";
		}
		rptVariableMap.put("fTarget", CapString.trimNull(fTarget));
		//現住房屋
		rptVariableMap.put("residenceTargetDesc", ClsUtil.build_residenceTargetDesc(c120s01a));
		rptVariableMap.put("houseStatus", CapString.trimNull(c120s01a.getHouseStatus()));

		rptVariableMap.put("custENameLast", CapString.trimNull(c122m01a.getCustENameLast()));
		rptVariableMap.put("custENameFirst", CapString.trimNull(c122m01a.getCustENameFirst()));

		//通訊處
		String coTarget = CapString.trimNull(c120s01a.getCoTarget());
		if ("--請選擇--".equals(coTarget)) {
			coTarget = "";
		}
		rptVariableMap.put("coTarget", CapString.trimNull(coTarget));
		//申請金鎞
		rptVariableMap.put("applyAmt", CapString.trimNull(
				c122m01a.getApplyAmt() == null ? "" : c122m01a.getApplyAmt().stripTrailingZeros().toPlainString()));
		//還款來源
		rptVariableMap.put("resource", CapString.trimNull(c122m01a.getResource()));
		//貸款利息收據通知方式
		rptVariableMap.put("notifyWay", CapString.trimNull(c122m01a.getNotifyWay()));

		Timestamp applyTS = c122m01a.getApplyTS();
		if (applyTS != null) {
			String date = CapDate.formatDate(applyTS, "yyyy-MM-dd");
			String year = String.valueOf(Integer.parseInt(date.substring(0, 4)) - 1911);
			String month = date.substring(5, 7);
			String day = date.substring(8, 10);
			rptVariableMap.put("applyYear", year);
			rptVariableMap.put("applyMonth", month);
			rptVariableMap.put("applyDay", day);
		}


		generator.setVariableData(rptVariableMap);

		generator.setRowsData(rptrowList);
		outputStream = generator.generateReport();

		return outputStream;
	}

}
