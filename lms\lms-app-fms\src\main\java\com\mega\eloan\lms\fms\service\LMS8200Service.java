package com.mega.eloan.lms.fms.service;

import java.util.Calendar;
import java.util.List;
import java.util.Map;

import com.mega.eloan.common.gwclient.IdentificationCheckGwReqMessage;
import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.model.C101S01A;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.L820M01A;
import com.mega.eloan.lms.model.L820M01B;
import com.mega.eloan.lms.model.L820M01E;
import com.mega.eloan.lms.model.L820M01S;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.GenericBean;


/**
 * <pre>
 * 以房養老案件查詢結果維護作業
 * </pre>
 * 
 * @since 2022
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
public interface LMS8200Service extends AbstractService {

	public L820M01A findL820m01aByUniqueKey(String mainId, String custId, String dupNo);

	
	public Map<String, String> getData(L820M01A l820m01a) throws CapException;

	/**
	 * 刪除 以房養老貸款撥款前查詢 根據所選的oid
	 * 
	 * @param oids
	 *            文件編號陣列
	 * @return boolean
	 */
	boolean deleteL820m01as(String[] oids);

	/**
	 * 其它到結案所用的flow
	 * 
	 * @param mainOid
	 *            文件編號
	 * @param model
	 *            資料表
	 * @param setResult
	 *            boolean
	 * @param resultType
	 *            boolean
	 * @throws Throwable
	 */
	public void flowAction(String mainOid, L820M01A model, boolean setResult,
			boolean resultType, boolean upMis) throws Throwable;

	/**
	 * 儲存案件簽章欄檔
	 */
	public void saveL820m01bList(List<L820M01B> list);
	
	/**
	 * 查詢案件簽章欄檔
	 * 
	 * @param mainId
	 *            案件編號
	 * @param staffNo
	 *            員編
	 * @param staffjob
	 *            人員職稱
	 */
	public L820M01B findL820m01b(String mainId, String branchType,
			String branchId, String staffNo, String staffJob);
	
	public void deleteL820m01bs(List<L820M01B> l820m01b, boolean isAll);
	
	/**
	 * 根據條件 搜出該客戶最新的簽報書中的徵信資料
	 * 
	 * @param custId
	 *            客戶編號
	 * @param dupNo
	 *            重覆序號
	 * @return 額度明細表
	 */
	C120S01A findC120m01aByConst(String custId, String dupNo);
	
	/**
	 * 根據條件 搜出該客戶最新的徵信資料
	 * 
	 * @param ownBrId
	 *            所屬分行
	 * @param custId
	 *            客戶編號
	 * @param dupNo
	 *            重覆序號
	 *            
	 */
	C101S01A findC101s01aByConst(String ownBrId, String custId, String dupNo);
	
	/**
	 * 是否為JSON格式
	 * @param byteArr
	 * @return
	 */
	public boolean isJSON(byte[] byteArr);
	
	public byte[] generateJSONObjectForIdCardCheck(IdentificationCheckGwReqMessage req, Calendar currentDateTime) throws Exception;
	
	public L820M01S modifyL820M01SForMixRecordData(String mainId, String custId, String dupNo, String dataType, String dataStatus, byte[] loanCreditDesFile);
	/**
	 * J-111-0525_11850_B1001
	 * 以房養老貸款撥款前查詢結果查詢(行內_身分證驗證、RPA受監護輔助宣告查詢)
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @param dataType
	 * @return
	 */
	public List<L820M01S> findL820M01S_byIdDupDataType(String mainId, String custId, String dupNo, String dataType);

	/**
	 * 排程儲存
	 * @param entity
	 */
	void autosave(GenericBean... entity);

	/**
	 * J-113-0392 E-LOAN「以房養老貸款查詢結果」，維護項目：新增財富管理往來查詢(資料倉儲系統-C350主要業務查詢-銀行往來概況彙總表(消金)最近1個月資料
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public L820M01E genL820M01ERecordData(String mainId, String custId, String dupNo);
	
	public List<L820M01E> findL820M01EList_byIdDup(String mainId, String custId, String dupNo);
	
	public L820M01E findL820M01EByUniqueKey(String mainId, String custId, String dupNo);
}
