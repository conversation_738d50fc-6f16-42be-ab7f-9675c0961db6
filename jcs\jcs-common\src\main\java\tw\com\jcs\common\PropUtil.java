package tw.com.jcs.common;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <pre>
 * PropUtil
 * </pre>
 * 
 * @since 2022年12月13日
 * <AUTHOR> @version
 *          <ul>
 *          <li>2022年12月13日
 *          </ul>
 */
public class PropUtil {

    private static final Logger logger = LoggerFactory.getLogger(PropUtil.class);

    private static Properties properties;

    /**
     * getProperty
     * 
     * @param key
     *            鍵值
     * @return
     */
    public static String getProperty(String key) {
        return PropUtil.getProperty(key, "");
    }

    /**
     * getProperty
     * 
     * @param key
     *            鍵值
     * @param dfValue
     *            預設值
     * @return
     */
    public static String getProperty(String key, String dfValue) {
        // 當properties為null時才new Properties()，避免多執行緒時，properties的內容產生不完全
        if (null == properties) {
            properties = new Properties();
        }
        InputStream is = Thread.currentThread().getContextClassLoader().getResourceAsStream("config.properties");
        try {
            properties.load(is);
        } catch (IOException e) {
            logger.error(e.toString());
        }

        return properties.getProperty(key, dfValue);
    }

    /**
     * getProperties
     * 
     * @param propPath
     * @return
     */
    public static Properties getProperties(String location) {
        Properties result = new Properties();
        InputStream is = null;
        try {
            is = Thread.currentThread().getContextClassLoader().getResourceAsStream(location);
            result.load(is);
        } catch (IOException e) {
            logger.error(e.toString());
        } finally {
            try {
                if (is != null) {
                    is.close();
                    is = null;
                }
            } catch (IOException e) {
                logger.error(e.toString());
            }
        }
        return result;
    }
}
