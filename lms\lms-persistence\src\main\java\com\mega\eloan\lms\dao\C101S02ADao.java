package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C101S02A;

/** 個金聯徵查詢結果 **/
public interface C101S02ADao extends IGenericDao<C101S02A> {

	C101S02A findByOid(String oid);

    List<C101S02A> findByList(String mainId, String custId, String dupNo);

    C101S02A findByItem(String mainId, String custId, String dupNo, String item);

	List<C101S02A> findByCustIdDupId(String custId, String DupNo);

	List<C101S02A> findByMainId(String mainId);

	public int deleteByOid(String oid);
}