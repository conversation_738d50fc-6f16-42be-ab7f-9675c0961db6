package com.mega.eloan.lms.cls.pages;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.lms.base.common.ClsScoreUtil;
import com.mega.eloan.lms.base.service.ScoreService;

import tw.com.jcs.common.Util;

/**
 * <pre>
 * 個金徵信作業 - 評等級數總表
 * </pre>
 * 
 * @since 2015/03/25
 * <AUTHOR>
 * @version <ul>
 *          <li>2015/03/25,EL08034,new
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls1131v03")
public class CLS1131V03Page extends AbstractEloanPage {

	@Autowired
	ScoreService scoreService;

	@Override
	public void execute(ModelMap model, PageParameters params)
			throws Exception {
		//房貸
		String g_varVer = scoreService.get_Version_HouseLoan();
		boolean g_factor_v2_0 = false;
		boolean g_factor_v2_1 = false;
		if(Util.equals(g_varVer, ClsScoreUtil.V2_1_HOUSE_LOAN)){
			g_factor_v2_1 = true;
		}else{
			g_factor_v2_0 = true;
		}
		//非房貸
		String q_varVer = scoreService.get_Version_NotHouseLoan();
		boolean q_factor_v2_0 = false;
		boolean q_factor_v3_0 = false;
		boolean q_factor_v3_1 = false;
		if(Util.equals(q_varVer, ClsScoreUtil.V3_0_NOT_HOUSE_LOAN)){
			q_factor_v3_0 = true;
		}else if(Util.equals(q_varVer, ClsScoreUtil.V3_1_NOT_HOUSE_LOAN)){
			q_factor_v3_1 = true;
		}else{
			q_factor_v2_0 = true;
		}
		model.addAttribute("G_FACTOR_V2_0", g_factor_v2_0);
		model.addAttribute("G_FACTOR_V2_1", g_factor_v2_1);
		model.addAttribute("Q_FACTOR_V2_0", q_factor_v2_0);
		model.addAttribute("Q_FACTOR_V3_0", q_factor_v3_0);
		model.addAttribute("Q_FACTOR_V3_1", q_factor_v3_1);
		
	}

	@Override
	protected String getViewName() {
		return getEloanPagePathByClass(getClass());
	}

}
