/* 
 * L120S05EDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S05E;

/** 借款人集團相關資料檔 **/
public interface L120S05EDao extends IGenericDao<L120S05E> {

	L120S05E findByOid(String oid);

	List<L120S05E> findByMainId(String mainId);

	L120S05E findByUniqueKey(String mainId, String custId, String dupNo);

	List<L120S05E> findByIndex01(String mainId, String custId, String dupNo);

	List<Object[]> findMaxCaseSeq(String mainId, String custId, String dupNo);

	List<Object[]> findTotAmtBSeq(String mainId, String custId, String dupNo);

	int delModel(String mainId, String custId, String dupNo);

}