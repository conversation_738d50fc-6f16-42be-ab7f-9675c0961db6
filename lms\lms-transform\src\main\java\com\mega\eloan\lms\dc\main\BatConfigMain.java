package com.mega.eloan.lms.dc.main;

import java.io.IOException;
import java.util.Properties;

import org.apache.commons.lang.StringUtils;

import com.mega.eloan.lms.dc.conf.MainConfig;

/**<pre>
 * 提供BAT取得設定值
 * </pre>
 * @since  2013/2/23
 * <AUTHOR>
 * @version <ul>
 *           <li>2013/2/23,UFO,new
 *          </ul>
 */
public class BatConfigMain {

	/**
	 * @param args
	 */
	public static void main(String[] args) {
		Properties prop = new Properties();
		try {
			prop.load(BatConfigMain.class.getClassLoader().getResourceAsStream(
					MainConfig.CONFIG_FILE));

			if (args.length == 1 && !StringUtils.isBlank(args[0])) {
				String val = prop.getProperty(args[0]);
				if (!StringUtils.isBlank(val)) {
					System.out.print(StringUtils.trimToEmpty(val));
					System.exit(0);
				} else {
					System.exit(1);
				}
			} else {
				System.exit(1);
			}
		} catch (IOException e) {
			e.printStackTrace();
			System.exit(1);
		}

	}

}
