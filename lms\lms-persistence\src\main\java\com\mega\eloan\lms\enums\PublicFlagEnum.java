/* 
 * PublicFlagEnum.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.enums;

/**
 * <pre>
 * 公開/非公開 enum。
 * </pre>
 * 
 * @since 2011/8/4
 * <AUTHOR> Wang
 * @version <ul>
 *          <li>2011/8/4,Sunkist Wang,new</li>
 *          </ul>
 */
public enum PublicFlagEnum {
    /**
     * 1 公開
     */
    PUBLIC("1"),
    /**
     * 2 非公開
     */
    PRIVATE("2");

    private String code;

    PublicFlagEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public boolean isEquals(Object other) {
        if (other instanceof String) {
            return code.equals(other);
        } else {
            return super.equals(other);
        }
    }

    public static PublicFlagEnum getEnum(String code) {
        for (PublicFlagEnum enums : PublicFlagEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }
}
