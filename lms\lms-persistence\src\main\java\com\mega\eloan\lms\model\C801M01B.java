/* 
 * C801M01B .java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;
import javax.validation.constraints.Digits;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;


/** 個人資料檔案清冊明細檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C801M01B ", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))

public class C801M01B  extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** mainId **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * 清冊明細編號<p/>
	 * 9999999999表自行輸入
	 */
	@Size(max=10)
	@Column(name="ITEMCODE", length=10, columnDefinition="VARCHAR(10)")
	private String itemCode;

	/** 
	 * 顯示順序<p/>
	 * 自行輸入從90001開始？？
	 */
	@Digits(integer=5, fraction=0, groups = Check.class)
	@Column(name="ITEMSEQ", columnDefinition="DECIMAL(5,0)")
	private Integer itemSeq;

	/** 
	 * 清冊項目類別<p/>
	 * A.申貸文件<br/>
	 * B.徵信文件<br/>
	 * C.授信文件
	 */
	@Size(max=1)
	@Column(name="ITEMTYPE", length=1, columnDefinition="CHAR(1)")
	private String itemType;

	/** 
	 * 文件內容<p/>
	 * 100個全型字
	 */
	@Size(max=300)
	@Column(name="ITEMCONTENT", length=300, columnDefinition="VARCHAR(300)")
	private String itemContent;

	/** 
	 * 檢附情形<p/>
	 * 預設N<BR/>
	 * 勾選則設成Y
	 */
	@Size(max=1)
	@Column(name="ITEMCHECK", length=1, columnDefinition="CHAR(1)")
	private String itemCheck;

	/** 
	 * 風險等級<p/>
	 * L.低<br/>
	 *  M.中<br/>
	 *  H.高
	 */
	@Size(max=1)
	@Column(name="RISKLEVEL", length=1, columnDefinition="CHAR(1)")
	private String riskLevel;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得mainId **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定mainId **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得清冊明細編號<p/>
	 * 9999999999表自行輸入
	 */
	public String getItemCode() {
		return this.itemCode;
	}
	/**
	 *  設定清冊明細編號<p/>
	 *  9999999999表自行輸入
	 **/
	public void setItemCode(String value) {
		this.itemCode = value;
	}

	/** 
	 * 取得顯示順序<p/>
	 * 自行輸入從90001開始？？
	 */
	public Integer getItemSeq() {
		return this.itemSeq;
	}
	/**
	 *  設定顯示順序<p/>
	 *  自行輸入從90001開始？？
	 **/
	public void setItemSeq(Integer value) {
		this.itemSeq = value;
	}

	/** 
	 * 取得清冊項目類別<p/>
	 * A.申貸文件<br/>
	 * B.徵信文件<br/>
	 * C.授信文件
	 */
	public String getItemType() {
		return this.itemType;
	}
	/**
	 * 設定清冊項目類別<p/>
	 * A.申貸文件<br/>
	 * B.徵信文件<br/>
	 * C.授信文件
	 */
	public void setItemType(String value) {
		this.itemType = value;
	}

	/** 
	 * 取得文件內容<p/>
	 * 100個全型字
	 */
	public String getItemContent() {
		return this.itemContent;
	}
	/**
	 *  設定文件內容<p/>
	 *  100個全型字
	 **/
	public void setItemContent(String value) {
		this.itemContent = value;
	}

	/** 
	 * 取得檢附情形<p/>
	 * 預設N<BR/>
	 * 勾選則設成Y
	 */
	public String getItemCheck() {
		return this.itemCheck;
	}
	/**
	 *  設定檢附情形<p/>
	 *  預設N<BR/>
	 *  勾選則設成Y
	 **/
	public void setItemCheck(String value) {
		this.itemCheck = value;
	}

	/** 
	 * 取得風險等級<p/>
	 * L.低<br/>
	 *  M.中<br/>
	 *  H.高
	 */
	public String getRiskLevel() {
		return this.riskLevel;
	}
	/**
	 *  設定風險等級<p/>
	 *  L.低<br/>
	 *  M.中<br/>
	 *  H.高
	 **/
	public void setRiskLevel(String value) {
		this.riskLevel = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
