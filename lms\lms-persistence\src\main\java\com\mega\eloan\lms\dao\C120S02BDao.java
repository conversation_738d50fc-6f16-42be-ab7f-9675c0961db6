package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C120S02B;

/** 大數據風險報告查詢結果 **/
public interface C120S02BDao extends IGenericDao<C120S02B> {

	C120S02B findByOid(String oid);

	List<C120S02B> findByMainId(String mainId);

	C120S02B findByUniqueKey(String mainId, String custId, String dupNo);

	public int deleteByOid(String oid);

	List<C120S02B> findByList(String mainId, String custId,
			String dupNo);
}