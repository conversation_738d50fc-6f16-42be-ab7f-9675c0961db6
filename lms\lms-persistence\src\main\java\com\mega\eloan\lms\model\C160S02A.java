/*
 * C101S02A.java
 *
 * Copyright (c) 2011-2012 JC Software Services, Inc.
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 *
 * Licensed Materials - Property of JC Software Services, Inc.
 *
 * This software is confidential and proprietary information of
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/** 動審明細表聯徵查詢結果資料紀錄 **/
@NamedEntityGraph(name = "C160S02A-entity-graph", attributeNodes = { @NamedAttributeNode("c160m01a") })
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "C160S02A", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "custId", "dupNo" }))
public class C160S02A extends GenericBean implements IDataObject, IDocObject {

    private static final long serialVersionUID = 1L;

    /**
     * oid
     * <p/>
     * ROWID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
    @Size(max = 32)
    @Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
    private String oid;

    /** 文件編號 **/
    @Size(max = 32)
    @Column(name="MAINID", length = 32, columnDefinition = "CHAR(32)")
    private String mainId;
    
    /** 身分證統編 **/
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

    /** 聯徵項目 **/
    @Size(max = 32)
    @Column(name = "EJCICITEM", length = 32, columnDefinition = "VARCHAR(32)")
    private String ejcicItem;

    /** 項目內容資料 **/
    @Size(max = 600)
    @Column(name = "JSONOB", length = 600, columnDefinition = "VARCHAR(600)")
    private String jsonOb;

    /** 查詢日期 */
    @Column(name="DATADATE", columnDefinition="DATE")
    private Date dataDate;

    /**
     * 取得oid
     * <p/>
     * ROWID
     */
    public String getOid() {
        return this.oid;
    }

    /**
     * 設定oid
     * <p/>
     * ROWID
     **/
    public void setOid(String value) {
        this.oid = value;
    }

    /** 取得文件編號 **/
    public String getMainId() {
        return this.mainId;
    }

    /** 設定文件編號 **/
    public void setMainId(String value) {
        this.mainId = value;
    }

    public String getEjcicItem() {
		return ejcicItem;
	}

	public void setEjcicItem(String ejcicItem) {
		this.ejcicItem = ejcicItem;
	}

	public String getJsonOb() {
		return jsonOb;
	}

	public void setJsonOb(String jsonOb) {
		this.jsonOb = jsonOb;
	}

	public Date getDataDate() {
		return dataDate;
	}

	public void setDataDate(Date dataDate) {
		this.dataDate = dataDate;
	}


	//bi-directional many-to-one association to C160M01B
    @ManyToOne(fetch = FetchType.LAZY)
	@JoinColumns({
		@JoinColumn(name="MAINID", referencedColumnName="MAINID", nullable=false, insertable = false, updatable = false),
		@JoinColumn(name="CUSTID", referencedColumnName="CUSTID", nullable=false, insertable = false, updatable = false),
		@JoinColumn(name="DUPNO", referencedColumnName="DUPNO", nullable=false, insertable = false, updatable = false)
		})
    private C160M01A c160m01a;

    public void setC101m01b(C160M01A c160m01a) {
        this.c160m01a = c160m01a;
    }

    public C160M01A getC160m01a() {
        return c160m01a;
    }

	public String getCustId() {
		return custId;
	}

	public void setCustId(String custId) {
		this.custId = custId;
	}

	public String getDupNo() {
		return dupNo;
	}

	public void setDupNo(String dupNo) {
		this.dupNo = dupNo;
	}


}
