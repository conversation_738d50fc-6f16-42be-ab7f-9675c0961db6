package com.mega.eloan.lms.crs.service;
import java.util.Date;
import java.util.List;
import java.util.Map;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.service.ICapService;

import com.mega.eloan.lms.crs.common.CrsRuleVO;
import com.mega.eloan.lms.mfaloan.bean.ELF491;
import com.mega.eloan.lms.model.C240M01A;
import com.mega.eloan.lms.model.C240M01C;
import com.mega.eloan.lms.model.C241M01A;
import com.mega.eloan.lms.model.C241M01Z;
public interface LMS2401Service extends ICapService {
	/**
	 * 更新控制檔
	 * @param branch
	 * @param date
	 */
	public void update490(String branch, Date sysMonth_1st);
	
	public void update491_abnormal();
	
	public void update491_J_109_0372_1st();
	/**
	 steps=1|2|3
	 	{1:寫入LNF07A}
	 	{2:由LNF07A寫入中心婉卻檔 MIS.LNUNID }
	 	{3:由LNF07A寫入消金覆審檔 MIS.ELF491 }
	 batchNo=J-109-0344(109)2561_2020mmdd , IT編排的批號, 做為 LNF07A_KEY_1 
	 	● select * from com.bCodeType where codeType like 'J-109-0344%'
	 	● select * from ln.lnf07a where LNF07A_KEY_1 like 'J-109-0344%'
	 postDocNoByBatchNo , 兆銀總授審字第1090057721號函, 做為 LNF07A_EXPLAIN (為了trace)
	 postDateByBatchNo , 授審處的發文日期(在寫入ELF491時，若 ELF491_LRDATE 已在此日期之後，表示 營運中心覆審人員 已在 資訊處 處理前，就先行覆審)
	 firstCrDateByBatchNo, 因授審處會陸續發文，每1個批後的覆審日期應不同 
	 */	
	public void update491_J_109_0372_byBatchNo(String steps, String batchNo_codeType_LNF07A_KEY_1, String postDocNoByBatchNo, Date postDateByBatchNo, Date firstCrDateByBatchNo);
	
	public void proc_crossMonth_loanData(Date sysMonth_1st);
	public void proc_elf591_custid_not_in_ELF491(String param_brNo, String assign_crDate);
	
	/**
	 * 產生名單(用CrsBatchServiceImpl產生時, 因未登入SSO,會抓到999的unitNo)
	 * 
	 * @param branch
	 * @param dateYM
	 */
	public boolean produce(List<String> instIdList, String branch, String dateYM, String userId, String unitNo, String unitType, String latest__aprdcdate) throws CapException;
	/**
	 * 本次欲抽樣 ％, 產生8_1資料
	 */ 
	public boolean produce8_1(C240M01A c240m01a)throws CapException;
	
	public boolean produceNew(C240M01A c240m01a, String custId, String dupNo, String cName)throws CapException;
	public boolean produceNewGrpDetail(C240M01A c240m01a, C241M01A c241m01a_grpMain, String custId, String dupNo, String cName)throws CapException;
	
	public boolean produce95_1(C240M01A c240m01a, String custId, String dupNo, String cName)throws CapException;
	public boolean produce96(C240M01A c240m01a, String custId, String dupNo, String cName, String pa_ym, String pa_trg)throws CapException;
	public boolean produce97(C240M01A meta, List<Map<String, Object>> list, Map<String, C241M01A> existMap, String lnf660_m_contract, String lnf660_loan_class, String comId, String comDupNo, String comName)throws CapException;
	
	public boolean produce_R1R2S(C240M01A c240m01a, String custId, String dupNo, String cName)throws CapException;
	public boolean is_only_projectCreditLoan(String brNo, String custId, String dupNo);
	public boolean produce_projectCreditLoan(C240M01A c240m01a, String custId, String dupNo, String cName, boolean is_only_projectCreditLoan)throws CapException;
	
	/**
	 * 由 Rule 99 反推
	 */
	public void deriveFrom99(C240M01A c240m01a, List<C241M01A> srclist, String userId)throws CapException;
	
	/**
	 * 計算需覆審的為幾筆, 若UI有10筆(8筆A-改期覆審,2筆本次覆審),指定2
	 * @param mainId
	 */
	public void c240m01a_reQuantity_thisSamplingCount(C240M01A c240m01a) throws CapException;
	
	public void deleteC241_classes(C241M01A c241m01a);
	
	/**
	 * 當931在傳送至[受檢單位]時,就先上傳
	 * (可能 受檢單位 的流程要跑 1個月)
	 * 
	 * 為避免 覆審的報表, 顯示為逾期, 所以在送出時,就先上傳ELF491
	 * (此時寫入 Approver, ApproveTime, UpDate)
	 * 
	 * 在已覆核未核定,做整批上傳時, 把 Approver, ApproveTime, UpDate蓋掉
	 */
	public void up_to_mis(C241M01A c241m01a);
	
	public void chkR1R2R4_copy_to_c241m01z(ELF491 elf491, C241M01Z c241m01z);
	public void chkR1R2R4(String brNo, String custId, String d);
	public void validate_R1R2R4(CrsRuleVO crsRuleVO, String rule_no_new, String rule_no);
	public void test_calc(String flag, CrsRuleVO crsRuleVO, String rule_no_new, String rule_no);

	public C240M01C rule95_1_summary(String ruleNo, String brNo, int rule95_allCnt, int rule95_1_baseCnt, String[] donePeriod_arr);
	public C240M01C rule_R1R2S(String ruleNo, String brNo, Date retrial_dt);
	public C240M01C rule_projectCreditLoan_summary(String ruleNo, String brNo, Date retrial_dt);

	public C240M01C rule_R14(String ruleNo, String brNo, Date retrial_dt);
	/**
	 * 產生抽樣單一授信額度在新臺幣一千萬元以下，且為十足擔保授信或經信用保證基金保證成數七成以上者
	 * @param c240m01a
	 * @param custId
	 * @param dupNo
	 * @param cName
	 * @return
	 * @throws CapException
	 */
	boolean produce_R14(C240M01A c240m01a, String custId,
			String dupNo, String cName) throws CapException;
}
