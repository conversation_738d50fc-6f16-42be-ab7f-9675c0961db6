package com.mega.eloan.lms.dc.bean;

import com.mega.eloan.lms.dc.conf.MainConfig;
import com.mega.eloan.lms.dc.util.Util;

public class BaseBean {

	/** oid */
	private String oid = null;
	/** mainId */
	private String mainId = null;
	/** 建立人員號碼 */
	private String creator = MainConfig.getInstance().getConfig().getTODAY();
	/** 建立日期 */
	private String createTime = Util.getCurrentTimestamp();
	/** 異動人員號碼 */
	private String updater = MainConfig.getInstance().getConfig().getTODAY();
	/** 異動日期 */
	private String updateTime = Util.getCurrentTimestamp();

	/**
	 * get oid
	 * 
	 * @return
	 */
	public String getOid() {
		return oid;
	}

	/**
	 * set oid
	 * 
	 * @param oid
	 */
	public void setOid(String oid) {
		this.oid = oid;
	}

	/**
	 * get mainId
	 * 
	 * @return
	 */
	public String getMainId() {
		return mainId;
	}

	/**
	 * set mainId
	 * 
	 * @param mainId
	 */
	public void setMainId(String mainId) {
		this.mainId = mainId;
	}

	/**
	 * get 建立人員號碼
	 * 
	 * @return
	 */
	public String getCreator() {
		return creator;
	}

	/**
	 * set 建立人員號碼
	 * 
	 * @param creator
	 */
	public void setCreator(String creator) {
		this.creator = creator;
	}

	/**
	 * get 建立日期
	 * 
	 * @return
	 */
	public String getCreateTime() {
		return createTime;
	}

	/**
	 * set 建立日期
	 * 
	 * @param createTime
	 */
	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}

	/**
	 * get 異動人員號碼
	 * 
	 * @return
	 */
	public String getUpdater() {
		return updater;
	}

	/**
	 * set 異動人員號碼
	 * 
	 * @param updater
	 */
	public void setUpdater(String updater) {
		this.updater = updater;
	}

	/**
	 * get 異動日期
	 * 
	 * @return
	 */
	public String getUpdateTime() {
		return updateTime;
	}

	/**
	 * set 異動日期
	 * 
	 * @param updateTime
	 */
	public void setUpdateTime(String updateTime) {
		this.updateTime = updateTime;
	}
}
