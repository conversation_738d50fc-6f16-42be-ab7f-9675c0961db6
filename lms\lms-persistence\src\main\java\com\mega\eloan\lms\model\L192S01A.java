package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import com.mega.eloan.common.model.IDocObject;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/**
 * 申請內容檔(含 帳務資料...)
 */
@NamedEntityGraph(name = "L192S01A-entity-graph", attributeNodes = { 
		@NamedAttributeNode("l192m01a"),
		@NamedAttributeNode("l192m01b")
		})
@Entity
@Table(uniqueConstraints = @UniqueConstraint(columnNames = { "mainId", "accNo",
		"mainCustId", "mainDupNo", "quotaNo" }))
public class L192S01A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = -7397811279252678501L;

	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(unique = true, nullable = false, length = 32, columnDefinition = "CHAR(32)")
	private String oid;

	@Column(length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String mainId;

	/** 借款人統一編號,配合團貸新增 */
	@Column(length = 10, columnDefinition = "CHAR(10)", nullable = false)
	private String mainCustId;

	/** 借款人重覆序號,配合團貸新增 */
	@Column(length = 1, columnDefinition = "CHAR(1)", nullable = false)
	private String mainDupNo;

	/** 餘額日期 */
	@Temporal(TemporalType.DATE)
	private Date balDate;

	/** 科目 */
	@Column(length = 30, columnDefinition = "VARCHAR(30)")
	private String subject;

	/** 帳號 */
	@Column(length = 15, columnDefinition = "VARCHAR(15)", nullable = false)
	private String accNo;

	/** 額度序號 */
	@Column(length = 12, columnDefinition = "VARCHAR(12)", nullable = false)
	private String quotaNo;

	/** 額度(幣別) */
	@Column(length = 3, columnDefinition = "VARCHAR(3)")
	private String quotaCurr;

	/** 額度(金額) */
	@Column(columnDefinition = "DECIMAL(15,2)")
	private BigDecimal quotaAmt;

	/** 餘額(幣別) */
	@Column(length = 3, columnDefinition = "VARCHAR(3)")
	private String balCurr;

	/** 餘額(金額) */
	@Column(columnDefinition = "DECIMAL(15,2)")
	private BigDecimal balAmt;

	/** 申請日 */
	@Temporal(TemporalType.DATE)
	private Date appDate;

	/** 簽辦日 */
	@Temporal(TemporalType.DATE)
	private Date signDate;

	/** 動用日 */
	@Temporal(TemporalType.DATE)
	private Date useDate;

	/** 動用期限/動用起訖日(起) */
	@Temporal(TemporalType.DATE)
	private Date fromDate;

	/** 動用期限/動用起訖日(訖) */
	@Temporal(TemporalType.DATE)
	private Date endDate;

	/** 進帳方式/計息方式 */
	@Column(length = 384, columnDefinition = "VARCHAR(384)")
	private String way;

	/** 核准者 */
	@Column(length = 38, columnDefinition = "VARCHAR(38)")
	private String appr;

	/** 存執本票-發票日 */
	@Temporal(TemporalType.DATE)
	private Date checkDate;

	/** 存執本票-幣別 */
	@Column(length = 3, columnDefinition = "VARCHAR(3)")
	private String checkCurr;

	/** 存執本票-金額 */
	@Column(columnDefinition = "DECIMAL(15,2)")
	private BigDecimal checkAmt;

	/** 存執本票-共同發票人/背書人 */
	@Column(length = 60, columnDefinition = "VARCHAR(60)")
	private String endorser;

	/** 建立人員號碼 */
	@Column(length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 */
	@Temporal(TemporalType.TIMESTAMP)
	private Date createTime;

	/** 異動人員號碼 */
	@Column(length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 */
	@Temporal(TemporalType.TIMESTAMP)
	private Date updateTime;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumns({ @JoinColumn(name = "mainId", referencedColumnName = "mainId", insertable = false, updatable = false) })
	private L192M01A l192m01a;
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumns({
		@JoinColumn(name = "MAINID", referencedColumnName = "MAINID", nullable = false, insertable = false, updatable = false),
		@JoinColumn(name = "MAINCUSTID", referencedColumnName = "MAINCUSTID", nullable = false, insertable = false, updatable = false),
		@JoinColumn(name = "MAINDUPNO", referencedColumnName = "MAINDUPNO", nullable = false, insertable = false, updatable = false) })
	private L192M01B l192m01b;

	public String getOid() {
		return oid;
	}

	public void setOid(String oid) {
		this.oid = oid;
	}

	public String getMainId() {
		return mainId;
	}

	public void setMainId(String mainId) {
		this.mainId = mainId;
	}

	/** 餘額日期 */
	public Date getBalDate() {
		return balDate;
	}

	/** 餘額日期 */
	public void setBalDate(Date balDate) {
		this.balDate = balDate;
	}

	/** 科目 */
	public String getSubject() {
		return subject;
	}

	/** 科目 */
	public void setSubject(String subject) {
		this.subject = subject;
	}

	/** 帳號 */
	public String getAccNo() {
		return accNo;
	}

	/** 帳號 */
	public void setAccNo(String accNo) {
		this.accNo = accNo;
	}

	/** 額度序號 */
	public String getQuotaNo() {
		return quotaNo;
	}

	/** 額度序號 */
	public void setQuotaNo(String quotaNo) {
		this.quotaNo = quotaNo;
	}

	/** 額度(幣別) */
	public String getQuotaCurr() {
		return quotaCurr;
	}

	/** 額度(幣別) */
	public void setQuotaCurr(String quotaCurr) {
		this.quotaCurr = quotaCurr;
	}

	/** 額度(金額) */
	public BigDecimal getQuotaAmt() {
		return quotaAmt;
	}

	/** 額度(金額) */
	public void setQuotaAmt(BigDecimal quotaAmt) {
		this.quotaAmt = quotaAmt;
	}

	/** 餘額(幣別) */
	public String getBalCurr() {
		return balCurr;
	}

	/** 餘額(幣別) */
	public void setBalCurr(String balCurr) {
		this.balCurr = balCurr;
	}

	/** 餘額(金額) */
	public BigDecimal getBalAmt() {
		return balAmt;
	}

	/** 餘額(金額) */
	public void setBalAmt(BigDecimal balAmt) {
		this.balAmt = balAmt;
	}

	/** 申請日 */
	public Date getAppDate() {
		return appDate;
	}

	/** 申請日 */
	public void setAppDate(Date appDate) {
		this.appDate = appDate;
	}

	/** 簽辦日 */
	public Date getSignDate() {
		return signDate;
	}

	/** 簽辦日 */
	public void setSignDate(Date signDate) {
		this.signDate = signDate;
	}

	/** 動用日 */
	public Date getUseDate() {
		return useDate;
	}

	/** 動用日 */
	public void setUseDate(Date useDate) {
		this.useDate = useDate;
	}

	/** 動用期限/動用起訖日(起) */
	public Date getFromDate() {
		return fromDate;
	}

	/** 動用期限/動用起訖日(起) */
	public void setFromDate(Date fromDate) {
		this.fromDate = fromDate;
	}

	/** 動用期限/動用起訖日(訖) */
	public Date getEndDate() {
		return endDate;
	}

	/** 動用期限/動用起訖日(訖) */
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	/** 進帳方式/計息方式 */
	public String getWay() {
		return way;
	}

	/** 進帳方式/計息方式 */
	public void setWay(String way) {
		this.way = way;
	}

	/** 核准者 */
	public String getAppr() {
		return appr;
	}

	/** 核准者 */
	public void setAppr(String appr) {
		this.appr = appr;
	}

	/** 存執本票-發票日 */
	public Date getCheckDate() {
		return checkDate;
	}

	/** 存執本票-發票日 */
	public void setCheckDate(Date checkDate) {
		this.checkDate = checkDate;
	}

	/** 存執本票-幣別 */
	public String getCheckCurr() {
		return checkCurr;
	}

	/** 存執本票-幣別 */
	public void setCheckCurr(String checkCurr) {
		this.checkCurr = checkCurr;
	}

	/** 存執本票-金額 */
	public BigDecimal getCheckAmt() {
		return checkAmt;
	}

	/** 存執本票-金額 */
	public void setCheckAmt(BigDecimal checkAmt) {
		this.checkAmt = checkAmt;
	}

	/** 存執本票-共同發票人/背書人 */
	public String getEndorser() {
		return endorser;
	}

	/** 存執本票-共同發票人/背書人 */
	public void setEndorser(String endorser) {
		this.endorser = endorser;
	}

	/** 建立人員號碼 */
	public String getCreator() {
		return creator;
	}

	/** 建立人員號碼 */
	public void setCreator(String creator) {
		this.creator = creator;
	}

	/** 建立日期 */
	public Date getCreateTime() {
		return createTime;
	}

	/** 建立日期 */
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	/** 異動人員號碼 */
	public String getUpdater() {
		return updater;
	}

	/** 異動人員號碼 */
	public void setUpdater(String updater) {
		this.updater = updater;
	}

	/** 異動日期 */
	public Date getUpdateTime() {
		return updateTime;
	}

	/** 異動日期 */
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public L192M01A getL192m01a() {
		return l192m01a;
	}

	public void setL192m01a(L192M01A l192m01a) {
		this.l192m01a = l192m01a;
	}

	/** 借款人統一編號,配合團貸新增 */
	public String getMainCustId() {
		return mainCustId;
	}

	/** 借款人統一編號,配合團貸新增 */
	public void setMainCustId(String mainCustId) {
		this.mainCustId = mainCustId;
	}

	/** 借款人統一編號,配合團貸新增 */
	public String getMainDupNo() {
		return mainDupNo;
	}

	/** 借款人統一編號,配合團貸新增 */
	public void setMainDupNo(String mainDupNo) {
		this.mainDupNo = mainDupNo;
	}

	public L192M01B getL192m01b() {
		return l192m01b;
	}

	public void setL192m01b(L192M01B l192m01b) {
		this.l192m01b = l192m01b;
	}

}
