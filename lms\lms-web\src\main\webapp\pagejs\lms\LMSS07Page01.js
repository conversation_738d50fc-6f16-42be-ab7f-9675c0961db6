initDfd.done(function() {
	// 共用借款人引進Grid
	gridSelCes1("");
	gridSelCes2("");
	gridSelCes3("");	
//	if(responseJSON.docURL == "/lms/lms1205m01"){
//		$("#hideBis").show();
//	}else{
//		$("#hideBis").hide();
//	}
	$("#hideBis").show();
	gridSelectMainId();
	$("#open1").click(function(){
		$("#gridSelectMainId").jqGrid("setGridParam", {//重新設定grid需要查到的資料
			postData : {
				formAction:"queryCesMainIdss2",
				rowNum:10
			},
			search: true
		}).trigger("reloadGrid");  
	});
});

function gridSelectMainId(){
    var gridSelectMainId = $("#gridSelectMainId").iGrid({
		handler : 'lms1205gridhandler',
		height : 175,
		sortname : 'custName',
		postData : {
			formAction : "queryCesMainIdss2",
			rowNum:10
		},
		caption: "&nbsp;",
		hiddengrid : false,
		rownumbers:true,
		rowNum:10,
		//multiselect : true,
		colModel : [ {
			colHeader : i18n.lmss07["L1205S07.grid37"], //主要借款人
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			//formatter : 'click',
			//onclick : function,
			name : 'custName' //col.id
		},{
			colHeader : i18n.lmss07["L1205S07.grid40"], //核准日期
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			//formatter : 'click',
			//onclick : function,
			name : 'approveTime' //col.id
		},{
			colHeader : i18n.lmss07["L1205S07.grid39"], //文件狀態
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			//formatter : 'click',
			//onclick : function,
			name : 'docStatus' //col.id
		},{
			colHeader : i18n.lmss07["L1205S07.grid12"], //徵信報告編號
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			name : 'cesId' //col.id
		},{
			colHeader : i18n.lmss07["L1205S07.grid41"], //文件建立者
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			name : 'creator' //col.id
		},{
			colHeader : "mainId",
			name : 'mainId',
			hidden : true
		}],
		ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
		}
    });
}

