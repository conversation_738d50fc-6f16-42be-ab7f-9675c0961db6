/* 
 * C250A01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C250A01A;

/** 可疑代辦案件註記作業授權檔 **/
public interface C250A01ADao extends IGenericDao<C250A01A> {

	C250A01A findByOid(String oid);
	
	List<C250A01A> findByMainId(String mainId);
	
	C250A01A findByUniqueKey(String mainId, String ownUnit, String authType, String authUnit);

	List<C250A01A> findByIndex01(String mainId, String ownUnit, String authType, String authUnit);
}