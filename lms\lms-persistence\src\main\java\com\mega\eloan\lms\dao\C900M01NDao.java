/* 
 * C900M01MDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C900M01N;

/** 分行可分案人員檔 **/
public interface C900M01NDao extends IGenericDao<C900M01N> {

	C900M01N findByOid(String oid);

	List<C900M01N> findByMainId(String mainId);

	List<C900M01N> findByIndex01(String brNo);

	/**
	 * 查出該分行下，傳入assignEmp的資料
	 * 
	 * @param assignEmp
	 * @return
	 */
	List<C900M01N> findByBrNoAndAssignEmpNo(String brNo, String[] assignEmpNos);

	// 無法處理lastAssignTime有null的狀況，暫時棄用此method
	/**
	 * 查出該分行下，傳入assignEmp的資料，用最後派案時間 lastAssignTime、派案順序 assignOrder做排序
	 * 
	 * @param brNo
	 * @param assignEmpNos
	 * @return
	 */
	// List<C900M01N> findByBrNoAndEmpNoOrderByTimeAndOrder(String brNo,
	// String[] assignEmpNos);
}