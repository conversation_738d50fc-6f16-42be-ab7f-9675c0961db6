package com.mega.eloan.lms.cls.service.impl;

import java.io.File;
import java.io.IOException;
import java.net.URISyntaxException;

import javax.annotation.Resource;

import org.aspectj.util.FileUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.mfaloan.service.MisELF386Service;

import tw.com.iisi.cap.exception.CapException;
import tw.com.jcs.common.PropUtil;

@Service("cls1021s01pdfservice")
public class CLS1021S01PDFServiceImpl implements FileDownloadService {
	@Resource
	MisELF386Service mis386Service;
	protected static final Logger LOGGER = LoggerFactory
			.getLogger(CLS1021S01PDFServiceImpl.class);

	@Override
	public byte[] getContent(PageParameters params) throws CapException, IOException, URISyntaxException {
		String tempPath = PropUtil.getProperty("loadFile.dir")
		+ "pdf/CLS1021S01Explain.pdf";
		File file = new File(Thread.currentThread().getContextClassLoader()
				.getResource(tempPath).toURI());
		byte[] bytes = FileUtil.readAsByteArray(file);
		return bytes;
	}
}
