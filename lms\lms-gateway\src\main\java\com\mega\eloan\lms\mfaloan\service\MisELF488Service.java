package com.mega.eloan.lms.mfaloan.service;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <pre>
 * 覆審(額度層)
 * </pre>
 * 
 * @since 2021/12/22
 * <AUTHOR>
 * @version <ul>
 *          <li>2021/12/22,EL08034,new
 *          </li>
 *          </ul>
 */
public interface MisELF488Service {
	public List<Map<String, Object>> sel_by_brNo_idDup(String elf488_br_no, String elf488_cust_id, String elf488_dup_no);
	public List<Map<String, Object>> sel_by_brNo_idDup(String elf488_br_no, String elf488_cust_id, String elf488_dup_no, Set<String> elf488_status_arr);
	
}
