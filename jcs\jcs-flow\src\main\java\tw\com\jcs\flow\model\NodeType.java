package tw.com.jcs.flow.model;

/**
 * <pre>
 * 流程節點類型
 * </pre>
 * 
 * @since 2023年1月9日
 * <AUTHOR> Software Inc.
 * @version
 *          <ul>
 *          <li>2023年1月9日
 *          </ul>
 */
public enum NodeType {
    PROCESS("process"),
    START("start"),
    END("end"),
    TRANSITION("transition"),
    STATE("state"),
    TASK("task"),
    DECISION("decision"),
    FORK("fork"),
    JOIN("join"),
    SUB("sub-process"),
    SCRIPT("script"),
    CODE("code"),
    BUTTON("button");

    public final String value;

    NodeType(String value) {
        this.value = value;
    }
}
