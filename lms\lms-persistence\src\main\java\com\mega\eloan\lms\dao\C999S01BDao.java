/* 
 * C999S01BDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C999S01B;

/** 個金約據書契約內容檔 **/
public interface C999S01BDao extends IGenericDao<C999S01B> {

	C999S01B findByOid(String oid);
	
	List<C999S01B> findByMainId(String mainId);
	
	C999S01B findByUniqueKey(String mainId, String pid, String type);

	List<C999S01B> findByIndex01(String mainId, String pid, String type);
}