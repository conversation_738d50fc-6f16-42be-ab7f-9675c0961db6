/* 
 *MISLN30Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.mfaloan.service;

import java.util.List;
import java.util.Map;

/**
 * <pre>
 * MIS.MISLN30
 * </pre>
 * 
 * @since 2012/12/26
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/26,REX,new
 *          </ul>
 */
public interface MISLN30Service {
	/**
	 * [個金額度明細表] 檢查承接之前放款帳號
	 * 
	 * @param cntrNo
	 *            額度序號
	 * @return
	 */
	List<Map<String, Object>> findByLNF030_CONTRACT(String cntrNo);

	List<Map<String, Object>> findByLNF030_PersonalData(String brNo);
	
	List<Map<String, Object>> findByLNF030_CoporateData(String brNo);
}
