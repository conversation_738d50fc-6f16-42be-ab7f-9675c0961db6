/*
 * TEJServiceImpl
 */
package com.mega.eloan.lms.tej.service.impl;

import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.exception.CapMessageException;

import com.mega.eloan.lms.tej.service.TEJService;

/**
 * <pre>
 * 取得TEJ Table
 * </pre>
 * 
 * <AUTHOR>
 * @version <ul>
 *          <li>2018/6/15,007623,new
 *          </ul>
 * @since 2018/6/15
 */
@Service
public class TEJServiceImpl extends AbstractTEJJdbc implements TEJService {

	protected final Logger logger = LoggerFactory.getLogger(getClass());

	@Override
	public List<Map<String, Object>> findCrmtabByStockNo(String stockNo)
			throws CapMessageException {
		List<Map<String, Object>> l = getJdbc().queryForList(
				"crmtab.byStockNo", new Object[] { stockNo });
		if (!l.isEmpty()) {
			// 20190906,J-108-0240,已撤銷公開發行就不顯示評等
			String date = findRevokePublicOffering(stockNo);
			if (date != null) {
				logger.info("{},已撤公開發行日:{}", new Object[] { stockNo, date });
				return null;
			}
		}
		return l;
	}

	/**
	 * 用股票代號查詢是否已撤銷公開發行
	 * 
	 * @param stockNo
	 * @return
	 */
	public String findRevokePublicOffering(String stockNo)
			throws CapMessageException {
		Map<String, Object> m = getJdbc().queryForMap(
				"crmstd.findPub_da2ByCoid", new Object[] { stockNo });
		if (m != null && m.containsKey("pub_da2")) {
			return (String) m.get("pub_da2");
		}
		return null;
	}

	@Override
	public Map<String, Object> findCrmstdByCustId(String custId)
			throws CapMessageException {
		return getJdbc().queryForMap("crmstd.byInvoice",
				new Object[] { custId });
	}

	@Override
	public List<Map<String, Object>> findAllTCRIRatingDataByCoId(String coId)
			throws CapMessageException {
		return getJdbc().queryForList("crmstd.findAllTCRIRatingDataByCoId",
				new Object[] { coId });
	}

	@Override
	public Map<String, Object> findUserLogByUserIdRemark(String userId,
			String custId) throws CapMessageException {
		return getJdbc().queryForMap("usageLog.byUserIdRemark",
				new Object[] { userId, custId });
	}

	@Override
	public List<Map<String, Object>> findCompanyGreatEventData(String coId,
			String yymmdd, int seq) throws CapMessageException {
		return getJdbc().queryForList(
				"crmstd.findCompanyGreatEventDataByCoIdAndYymmddAndSeq",
				new Object[] { coId, yymmdd, seq });
	}

	@Override
	public List<Map<String, Object>> findCrmstdAllByPubDate2IsEmpty()
			throws CapMessageException {
		return getJdbc().queryForListWithMax(
				"crmstd.findCrmstdAllByPubDate2IsEmpty", new Object[] {});
	}

	@Override
	public List<Map<String, Object>> findCompanyGreatEventDataGroupByCoIdAndYymmdd(
			String coIdPlusYymmdd) throws CapMessageException {
		return getJdbc().queryForList(
				"crmstd.findCompanyGreatEventDataGroupByCoIdAndYymmdd",
				new Object[] { coIdPlusYymmdd });
	}

}
