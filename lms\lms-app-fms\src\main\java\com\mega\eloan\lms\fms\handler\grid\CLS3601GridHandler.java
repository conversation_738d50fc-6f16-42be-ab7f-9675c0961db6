package com.mega.eloan.lms.fms.handler.grid;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.formatter.UserNameFormatter;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.fms.pages.CLS3601V01Page;
import com.mega.eloan.lms.model.C360M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.jcs.common.Util;

@Scope("request")
@Controller("cls3601gridhandler")
public class CLS3601GridHandler extends AbstractGridHandler {

	@Resource
	CLSService clsService;

	@Resource
	UserInfoService userInfoService;
	
	@Resource
	CodeTypeService codeTypeService;
	
	Properties prop = MessageBundleScriptCreator.getComponentResource(CLS3601V01Page.class);

	/**
	 * 查詢Grid 資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	public CapGridResult queryMain(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String docStatus = Util.nullToSpace(params.getString(EloanConstants.DOC_STATUS));
		
		if (true) {
			//Filet查詢條件
			String custId = Util.trim(params.getString("search_custId"));
			String cntrNo = Util.trim(params.getString("search_cntrNo"));
			
			if(Util.isNotEmpty(custId)){
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
			}
			if(Util.isNotEmpty(cntrNo)){
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
			}
		}
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", user.getUnitNo());
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, EloanConstants.DOC_STATUS, docStatus);	
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");

		if(Util.equals(docStatus, FlowDocStatusEnum.已核准.getCode())){
			pageSetting.addOrderBy("approveTime", true);	
		}else{
			pageSetting.addOrderBy("createTime", true);
		}
		
		Page<? extends GenericBean> page = clsService.findPage(C360M01A.class, pageSetting);
		List<C360M01A> list = (List<C360M01A>) page.getContent();
		Map<String, String> itemTypeMap = clsService.get_codeTypeWithOrder("C360M01A_itemType");
		for (C360M01A model : list) {
			model.setItemTypeDesc(Util.trim(itemTypeMap.get(model.getItemType())));
		}
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());

		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		if(true){
			UserNameFormatter userNameFormatter = new UserNameFormatter(userInfoService, UserNameFormatter.ShowTypeEnum.Name);
			dataReformatter.put("updater", userNameFormatter); // 使用者名稱格式化	
			dataReformatter.put("approver", userNameFormatter); // 使用者名稱格式化
			
			//dataReformatter.put("itemType", new CodeTypeFormatter(codeTypeService, "C360M01A_itemType", ShowTypeEnum.ValSpaceDesc));
		}
		result.setDataReformatter(dataReformatter);
		
		return result;
	}
}
