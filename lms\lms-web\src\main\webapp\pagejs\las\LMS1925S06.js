$(document).ready(function(){

    $("<option></option>").text(i18n.lms1925m01['lms1925s06.009']).prependTo("#selectnames");
    $('#selectnames').find("option:eq(0)").attr("selected", true);
    
    $('#phrase').click(function(){
        $('#select1').show();
    });
    
    $('#selectnames').change(function(){
    
        if ($(this).find("option:eq(0)").attr("selected")) {
            return false;
        }
        var txt = $(this).find(":selected").text();
        $('#processComm').val(txt);
        
        //IE 會再觸發一次onChange事件，所以在第一行做特別處理。FireFox則無此問題
        $(this).find("option:eq(0)").attr("selected", true);
        $('#select1').hide();
    });
});
