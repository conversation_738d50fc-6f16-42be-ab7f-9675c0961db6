package com.mega.eloan.lms.model;

import java.sql.Timestamp;

import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

import com.mega.eloan.common.model.RelativeMeta_;

/**
 * <pre>
 * The persistent class for the C140A01A database table.
 * </pre>
 * @since  2011/9/20
 * <AUTHOR>
 * @version <ul>
 *           <li>2011/9/20,<PERSON>,new
 *          </ul>
 */
@StaticMetamodel(C140A01A.class)
public class C140A01A_ extends RelativeMeta_{
	public static volatile SingularAttribute<C140A01A, Timestamp> authTime;
	public static volatile SingularAttribute<C140A01A, String> authType;
	public static volatile SingularAttribute<C140A01A, String> authUnit;
	public static volatile SingularAttribute<C140A01A, String> owner;
	public static volatile SingularAttribute<C140A01A, String> ownUnit;
	public static volatile SingularAttribute<C140A01A, C140M01A> c140m01a;
}
