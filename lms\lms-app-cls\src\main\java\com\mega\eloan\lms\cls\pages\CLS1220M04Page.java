package com.mega.eloan.lms.cls.pages;

import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.IncomDocStatusEnum;
import com.mega.eloan.lms.cls.panels.CLS1220S01PanelP;
import com.mega.eloan.lms.cls.panels.CLS1220S02PanelP;
import com.mega.eloan.lms.cls.panels.CLS1220S03PanelP;
import com.mega.eloan.lms.cls.panels.CLS1220S06Panel;
import com.mega.eloan.lms.cls.panels.CLS1220S07Panel;
import com.mega.eloan.lms.cls.panels.CLS1220S08Panel;
import com.mega.eloan.lms.cls.service.CLS1220Service;
import com.mega.eloan.lms.model.C122M01A;
import com.mega.eloan.lms.model.C122M01F;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.jcs.auth.AuthService;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 信貸進件
 * </pre>
 * 
 * @since 2020/6/23
 * <AUTHOR>
 * @version <ul>
 *          <li>2020/6/23,EL08034,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/cls1220m04/{page}")
public class CLS1220M04Page extends AbstractEloanForm {
	
	@Autowired
	CLS1220Service service;
	
	@Autowired
	AuthService au;
	
	final String TAB_CTX = "_tabCtx";
	final String TAB_SIGN = "tab-";

	@Override
	public void execute(ModelMap model, PageParameters params) {
		renderJsI18N(CLS1220M04Page.class);
		renderJsI18N(CLS1220V01Page.class);
		model.addAttribute("loadScript", "loadScript('pagejs/cls/CLS1220M04Page');");
		String mainId = params.getString(EloanConstants.MAIN_ID);
		// 依權限設定button
//		add(new AclLabel("_btnDOC_EDITING", params, getDomainClass(),
//				AuthType.Modify, CLSDocStatusEnum.編製中));
		boolean show_btnDOC_EDITING = true;
		boolean show_btnDiscardLoan = true;
		boolean show_btnSEND = false;
		boolean show_tab0203=true;
		boolean show_youngLoanSetting=false;
		boolean show_tit01=true;
	
		C122M01A meta = service.getC122M01A_byMainId(mainId);
		C122M01F c122m01f = meta.getC122m01f();
//		params.remove("mainDocStatus");
//		params.add("mainDocStatus", meta.getDocStatus());
		
		String applyType = meta.getApplyKind();
		String incomType = meta.getIncomType();
		if(Util.isNotEmpty(applyType) && 
				( Util.equals(UtilConstants.C122_ApplyKind.I, applyType) 
				|| Util.equals(UtilConstants.C122_ApplyKind.J, applyType) ) ){
			show_youngLoanSetting=true;//顯示青創表單頁籤
			show_tit01=false;//青創案件不顯示原始表單title
			if(Util.isNotEmpty(incomType) && Util.equals("2", incomType)){
				show_tab0203=false;//青創線上進件不顯示無資料的頁籤
			}
		}
		//========================
		//若操作者 只有 EL00 權限
		Set<String> eloanRoles = MegaSSOSecurityContext.getEloanRoles();
		String pgmDept = MegaSSOSecurityContext.getPGMDept();
		int transactionCode = Util.parseInt(params
				.getString(EloanConstants.TRANSACTION_CODE));
		
//		boolean _Query = au.auth(pgmDept, eloanRoles, transactionCode,
//				AuthType.Query);
		boolean _Accept = au.auth(pgmDept, eloanRoles, transactionCode,
				AuthType.Accept);
		boolean _Modify = au.auth(pgmDept, eloanRoles, transactionCode,
				AuthType.Modify);
		
		String maindocStatus = Util.trim(params.getString("mainDocStatus"));
		if(maindocStatus.startsWith("A") || maindocStatus.startsWith("B")){
			show_btnDiscardLoan = true;
		}else{
			show_btnDiscardLoan = false;
		}

		if(_Accept||_Modify){
		}else{
			show_btnDOC_EDITING = false;
			show_btnDiscardLoan = false;
		}
		
		String docStatus = Util.trim(params.getString("docStatus"));
		if(Util.equals(docStatus, "03O")){ //已結案
			show_btnDOC_EDITING = false;
			show_btnDiscardLoan = false;
		}
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		if(user.getRoles().containsKey("EL02")){
			if(Util.equals(maindocStatus, IncomDocStatusEnum.待初審) ||
					Util.equals(maindocStatus, IncomDocStatusEnum.待派案)
					|| Util.isEmpty(c122m01f) || Util.isEmpty(c122m01f.getSignMegaEmpNo()) ){
				show_btnSEND = true;
			}
		} 
		if(Util.equals("true", params.getString("noOpenDoc"))){
			show_btnDOC_EDITING = false;
			show_btnDiscardLoan = false;
			show_btnSEND = false;
		}
		
		//========================
		model.addAttribute("_btnDOC_EDITING_visible", show_btnDOC_EDITING);
		model.addAttribute("_btnDiscardLoan_visible", show_btnDiscardLoan);
		model.addAttribute("_btnSEND_visible", show_btnSEND);
//		add(new AclLabel("_btnSEND", params, getDomainClass(), AuthType.Accept, IncomDocStatusEnum.待派案));
		model.addAttribute("_btnPRINT_visible", show_youngLoanSetting);// 下載青創文件
		
		
		// tabs
		int page = Util.parseInt(params.getString("page"));
		String tabID = TAB_SIGN + Util.addZeroWithValue(page, 2); // 指定ID
		
		//J-112-0006 線下案件取消顯示[基本資料]及[服務單位]
		if(Util.isNotEmpty(incomType) && Util.equals("1", incomType)){
			show_tab0203 = false;
		}
		
		model.addAttribute("_showTab0203_visible", show_tab0203);
		model.addAttribute("_showTab05_visible", show_youngLoanSetting);// 青創頁籤
		model.addAttribute("_showTit01_visible", show_tit01);// 原始title
		model.addAttribute("_showTit02_visible", !show_tit01);// 青創title
		
		Panel panel = getPanel(meta, page);
		panel.processPanelData(model, params);
		model.addAttribute("tabIdx", tabID);
		
		renderJsI18N(CLS1220M04Page.class);
		renderJsI18N(CLS1131V01Page.class);
		renderJsI18N(CLS1141V01Page.class);
	}

	// 頁籤
	public Panel getPanel(C122M01A meta,int index) {
		Panel panel = null;
		
		switch (index) {
		case 1:
			panel = new CLS1220S01PanelP(TAB_CTX, true, meta);
			break;		
		case 2:
			panel = new CLS1220S02PanelP(TAB_CTX, true);
			break;
		case 3:
			panel = new CLS1220S03PanelP(TAB_CTX, true);
			break;
		case 4:
			renderJsI18N(CLS1220S06Panel.class);
			panel = new CLS1220S06Panel(TAB_CTX, true);
			break;
		case 5:
			if(Util.isNotEmpty(meta) && 
					Util.equals(UtilConstants.C122_ApplyKind.I, meta.getApplyKind())){
				renderJsI18N(CLS1220S07Panel.class);
				panel = new CLS1220S07Panel(TAB_CTX, true);
			}else{
				renderJsI18N(CLS1220S08Panel.class);
				panel = new CLS1220S08Panel(TAB_CTX, true);
			}
			break;
		}
		return panel;
	}
	
	@Override
	public Class<? extends Meta> getDomainClass() {
		return C122M01A.class;
	}
}
