/* 
 * LMS1215FormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.handler.form;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.lms.pages.LMS1205M01Page;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01D;
import com.mega.eloan.lms.model.L120M01F;
import com.mega.eloan.lms.model.L120S17A;
import com.mega.eloan.lms.model.L121M01B;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 授信簽報書(個金授權外) FormHandler
 * </pre>
 * 
 * @since 2011/8/6
 * <AUTHOR> Lin
 * @version <ul>
 *          <li>2011/8/6,Miller Lin,new
 *          </ul>
 */
@Scope("request")
@Controller("lms1215formhandler")
@DomainClass(L120M01A.class)
public class LMS1215M01FormHandler extends LMSM01FormHandler {

	/**
	 * <pre>
	 * 查詢銀行法及金控法44 45條(個金)
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return IResult CapAjaxFormResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getRlt2(PageParameters params)
			throws CapException {
		return super._LMSM01FormHandler_getRlt2(params);
	}

	/**
	 * 查詢主從債務人是否有資料(個金)
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getSel(PageParameters params)
			throws CapException {
		return super._LMSM01FormHandler_getSel(params);
	}

	/**
	 * <pre>
	 * 新增借款人主檔(個金)
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return IResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult addBorrowMain2(PageParameters params)
			throws CapException {
		return super._LMSM01FormHandler_addBorrowMain2(params);
	}

	/**
	 * <pre>
	 * 刪除-(個金)(借款人資料)
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return IResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteBorrowMain2(PageParameters params)
			throws CapException {
		return super._LMSM01FormHandler_deleteBorrowMain2(params);
	}

	/**
	 * 引進借款人資料(個金)
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getCustData3(PageParameters params)
			throws CapException {
		return super._LMSM01FormHandler_getCustData3(params);
	}

	/**
	 * 引進借款人配偶資料(個金)
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getCustData3m(PageParameters params)
			throws CapException {
		return super._LMSM01FormHandler_getCustData3m(params);
	}

	/**
	 * 設定使用者所選擇的申貸戶資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult setCustData3(PageParameters params)
			throws CapException {
		return super._LMSM01FormHandler_setCustData3(params);
	}

	/**
	 * <pre>
	 * 查詢-個金(借款人標籤列)
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return IResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryBorrow2(PageParameters params)
			throws CapException {
		return super._LMSM01FormHandler_queryBorrow2(params);
	}

	/**
	 * <pre>
	 * 儲存-個金(全部借款人內容)
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return IResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveBorrow2(PageParameters params)
			throws CapException {
		return super._LMSM01FormHandler_saveBorrow2(params);
	}

	/**
	 * 儲存會簽內容(國金部)
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveSignContent(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		CapAjaxFormResult L120M01aForm14 = new CapAjaxFormResult();
		CapAjaxFormResult LMS1205S01Form = new CapAjaxFormResult();
		boolean queryArea = params.getBoolean("queryArea");
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String itemDscr09 = params.getString("itemDscr09");
		String sSeaManager = trimNull(params.getString("sSeaManager"));
		String sSeaBoss = trimNull(params.getString("sSeaBoss"));
		String sSeaAoName = trimNull(params.getString("sSeaAoName"));
		String sSeaAppraiserCN = trimNull(params.getString("sSeaAppraiserCN"));
		String sAreaLeader = trimNull(params.getString("sAreaLeader"));
		String sAreaSubLeader = trimNull(params.getString("sAreaSubLeader"));
		String sAreaManager = trimNull(params.getString("sAreaManager"));
		String sAreaAppraiser = trimNull(params.getString("sAreaAppraiser"));

		if (queryArea) {
			LMS1205S01Form
					.set("areaLeader", withIdName(Util.trim(sAreaLeader)));
			LMS1205S01Form.set("areaSubLeader",
					withIdName(Util.trim(sAreaSubLeader)));
			LMS1205S01Form.set("areaManager",
					withIdName(Util.trim(sAreaManager)));
			LMS1205S01Form.set("areaAppraiser",
					withIdName(Util.trim(sAreaAppraiser)));
		} else {
			LMS1205S01Form
					.set("seaManager", withIdName(Util.trim(sSeaManager)));
			LMS1205S01Form.set("seaBoss", withIdName(Util.trim(sSeaBoss)));
			LMS1205S01Form.set("seaAoName", withIdName(Util.trim(sSeaAoName)));
			LMS1205S01Form.set("seaAppraiserCN",
					withIdName(Util.trim(sSeaAppraiserCN)));
		}

		L120M01aForm14.set("itemDscr09", itemDscr09);
		L121M01B model = service1205.findL121m01bByUniqueKey(mainId, "9");
		// List<L120M01F> list = service1205.findL120m01fByMainId(mainId);
		if (model == null) {
			model = new L121M01B();
			model.setMainId(mainId);
			model.setItemType("9");
			model.setCreateTime(CapDate.getCurrentTimestamp());
			model.setCreator(user.getUserId());
		}
		List<L120M01F> saveList = new ArrayList<L120M01F>();

		if (!Util.isEmpty(Util.trim(itemDscr09))) {
			model.setItemDscr(itemDscr09);
		} else {
			model.setItemDscr("");
		}
		try {
			service1205.saveSea(model, saveList);
		} catch (Exception e) {
			logger.error("[saveSignContent] service1205.save EXCEPTION!!", e);
			Map<String, String> param = new HashMap<String, String>();
			param.put("colName", space);
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
		}
		if (params.getAsBoolean("showMsg", true)) {
			// 印出儲存成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0017"));
		}
		result.set("L120M01aForm14", L120M01aForm14);
		result.set("LMS1205S01Form", LMS1205S01Form);
		return result;
	}

	/**
	 * 儲存簽章欄(海外聯貸)
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveSignContentF1(PageParameters params)
			throws CapException {
		// MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		CapAjaxFormResult L120M01aForm14 = new CapAjaxFormResult();
		CapAjaxFormResult LMS1205S01Form = new CapAjaxFormResult();
		boolean queryArea = params.getBoolean("queryArea");
		String mainId = params.getString(EloanConstants.MAIN_ID);
		// String itemDscr09 = params.getString("itemDscr09");
		String sSeaManager = trimNull(params.getString("sSeaManager"));
		String sSeaBoss = trimNull(params.getString("sSeaBoss"));
		String sSeaAoName = trimNull(params.getString("sSeaAoName"));
		String sSeaAppraiserCN = trimNull(params.getString("sSeaAppraiserCN"));
		String sUnitManager1 = trimNull(params.getString("sUnitManager1"));
		String sAreaLeader = trimNull(params.getString("sAreaLeader"));
		String sAreaSubLeader = trimNull(params.getString("sAreaSubLeader"));
		String sAreaManager = trimNull(params.getString("sAreaManager"));
		String sAreaAppraiser = trimNull(params.getString("sAreaAppraiser"));
		String sUnitManager3 = trimNull(params.getString("sUnitManager3"));

		if (queryArea) {
			LMS1205S01Form
					.set("areaLeader", withIdName(Util.trim(sAreaLeader)));
			LMS1205S01Form.set("areaSubLeader",
					withIdName(Util.trim(sAreaSubLeader)));
			LMS1205S01Form.set("areaManager",
					withIdName(Util.trim(sAreaManager)));
			LMS1205S01Form.set("areaAppraiser",
					withIdName(Util.trim(sAreaAppraiser)));
		} else {
			LMS1205S01Form
					.set("seaManager", withIdName(Util.trim(sSeaManager)));
			LMS1205S01Form.set("seaBoss", withIdName(Util.trim(sSeaBoss)));
			LMS1205S01Form.set("seaAoName", withIdName(Util.trim(sSeaAoName)));
			LMS1205S01Form.set("seaAppraiserCN",
					withIdName(Util.trim(sSeaAppraiserCN)));
		}

		List<L120M01F> list = service1205.findL120m01fByMainId(mainId);
		List<L120M01F> saveList = new ArrayList<L120M01F>();
		if (!list.isEmpty()) {
			for (L120M01F l120m01f : list) {
				if ("6".equals(l120m01f.getBranchType())) {
					saveList.add(l120m01f);
				}
			}
		}
		if (!saveList.isEmpty()) {
			service1205.delListL120m01f(saveList);
			saveList.clear();
		}
		// 第一次新增營運簽章欄
		if (queryArea) {
			saveList.add(addSeaL120m01f(mainId, "L6", sAreaLeader));
			saveList.add(addSeaL120m01f(mainId, "L5", sAreaSubLeader));
			saveList.add(addSeaL120m01f(mainId, "L3", sAreaManager));
			saveList.add(addSeaL120m01f(mainId, "L1", sAreaAppraiser));
			saveList.add(addSeaL120m01f(mainId, "L9", sUnitManager1));
		} else {
			saveList.add(addSeaL120m01f(mainId, "L5", sSeaManager));
			saveList.add(addSeaL120m01f(mainId, "L3", sSeaBoss));
			if (!Util.isEmpty(sSeaAoName)) {
				saveList.add(addSeaL120m01f(mainId, "L2", sSeaAoName));
			}
			saveList.add(addSeaL120m01f(mainId, "L1", sSeaAppraiserCN));
			saveList.add(addSeaL120m01f(mainId, "L9", sUnitManager3));
		}

		try {
			service1205.saveSea(null, saveList);
		} catch (Exception e) {
			logger.error("[saveSignContent] service1205.save EXCEPTION!!", e);
			Map<String, String> param = new HashMap<String, String>();
			param.put("colName", space);
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
		}
		if (params.getAsBoolean("showMsg", true)) {
			// 印出儲存成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0017"));
		}
		result.set("L120M01aForm14", L120M01aForm14);
		result.set("LMS1205S01Form", LMS1205S01Form);
		return result;
	}

	/**
	 * 呈主管放行(海外聯貸)
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult sendBossSea(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		L120M01A l120m01a = service1205.findL120m01aByMainId(mainId);
		L121M01B l120m01b = service1205.findL121m01bByUniqueKey(mainId, "9");
		List<L120M01F> list = service1205.findL120m01fByMainId(mainId);
		StringBuilder errorMsg = new StringBuilder();
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1205M01Page.class);
		if (!Util.isEmpty(l120m01a.getAreaDocstatus())
				&& "025".equals(user.getUnitNo())) {
			// 國金部
			boolean haseData = checkPeo(list, UtilConstants.BRANCHTYPE.國金部_營運中心);
			if (!haseData) {
				// 拋出沒有登錄國金部主管訊息
				errorMsg.append(pop.getProperty("l120m01a.error35"))
						.append("、");
			}
		} else if (!Util.isEmpty(l120m01a.getAreaDocstatus())
				&& !"025".equals(user.getUnitNo())) {
			// 營運中心會簽
			boolean haseData = checkPeo(list, UtilConstants.BRANCHTYPE.國金部_營運中心);
			if (!haseData) {
				// 拋出沒有登錄營運中心主管訊息
				errorMsg.append(pop.getProperty("l120m01a.error34"))
						.append("、");
			}
		}

		if (errorMsg.length() > 0) {
			errorMsg.deleteCharAt(errorMsg.length() - 1).append(
					pop.getProperty("l120m01a.error16"));
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0015", errorMsg.toString()), getClass());
		}

		if (l120m01b != null) {
			if (!Util.isEmpty(l120m01b.getItemDscr())) {
				// 會簽單位放行主管
				l120m01a.setAreaApprover(user.getUserId());
				// 會簽單位經辦
				l120m01a.setAreaUpdater(l120m01b.getUpdater());
				// 會簽單位放行時間
				l120m01a.setAreaApprTime(CapDate.getCurrentTimestamp());
				// 會簽文件狀態
				l120m01a.setAreaDocstatus(CreditDocStatusEnum.營運中心_海外聯貸案_待放行
						.getCode());
			} else {
				// 尚未登錄海外聯貸案會簽意見，請登入後再執行本功能！
				throw new CapMessageException(
						pop.getProperty("l120m01a.error25"), getClass());
			}
		} else {
			// 尚未登錄海外聯貸案會簽意見，請登入後再執行本功能！
			throw new CapMessageException(pop.getProperty("l120m01a.error25"),
					getClass());
		}

		// 找出已存在放行人員準備刪除
		L120M01F delL120m01f = null;
		// 準備儲存的放行人員
		L120M01F addL120m01f = addSeaL120m01f(mainId, "L4", user.getUserId());
		if (!list.isEmpty()) {
			for (L120M01F l120m01f : list) {
				if ("6".equals(l120m01f.getBranchType())) {
					if ("L4".equals(Util.trim(l120m01f.getStaffJob()))) {
						delL120m01f = l120m01f;
						break;
					}
				}
			}
		}
		if (delL120m01f != null) {
			service1205.delete(delL120m01f);
		}
		service1205.saveSeaAndDel(l120m01a, l120m01b, addL120m01f);
		// service1205.save(l120m01a, l120m01b);
		// if (params.getAsBoolean("showMsg", true)) {
		// // 印出儲存成功訊息!
		// result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
		// .getMainMessage(this.getComponent(), "EFD0017"));
		// }
		return result;
	}

	/**
	 * 退回會簽意見
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult backSea(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		L120M01A l120m01a = service1205.findL120m01aByMainId(mainId);
		L121M01B l121m01b = service1205.findL121m01bByUniqueKey(mainId, "9");
		if (l121m01b == null) {
			Properties pop = MessageBundleScriptCreator
					.getComponentResource(LMS1205M01Page.class);
			// 尚未登錄海外聯貸案會簽意見，請登入後再執行本功能！
			throw new CapMessageException(pop.getProperty("l120m01a.error25"),
					getClass());
		}
		// 會簽文件狀態
		l120m01a.setAreaDocstatus(CreditDocStatusEnum.營運中心_海外聯貸案_會簽中.getCode());
		service1205.save(l120m01a);
		if (params.getAsBoolean("showMsg", true)) {
			// 印出執行成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0018"));
		}
		return result;
	}

	/**
	 * 退會簽中OR呈已會簽(海外聯貸案用)
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Accept, CheckDocStatus = false)
	public IResult returnGoBossSea(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String frag = params.getString("frag");
		L120M01A l120m01a = service1205.findL120m01aByMainId(mainId);
		L121M01B l120m01b = service1205.findL121m01bByUniqueKey(mainId, "9");
		if (l120m01b != null) {
			if (!Util.isEmpty(l120m01b.getItemDscr())) {
				// 會簽單位放行主管
				l120m01a.setAreaApprover(user.getUserId());
				// 會簽單位經辦
				l120m01a.setAreaUpdater(l120m01b.getUpdater());
				// 會簽單位放行時間
				l120m01a.setAreaApprTime(CapDate.getCurrentTimestamp());
				// 會簽文件狀態
				if ("1".equals(frag)) {
					// 退回經辦修改
					l120m01a.setAreaDocstatus(CreditDocStatusEnum.營運中心_海外聯貸案_會簽中
							.getCode());
				} else if ("8".equals(frag)) {
					// 確認
					l120m01a.setAreaDocstatus(CreditDocStatusEnum.營運中心_海外聯貸案_已會簽
							.getCode());
				}
			}
		} else {
			Properties pop = MessageBundleScriptCreator
					.getComponentResource(LMS1205M01Page.class);
			throw new CapMessageException(pop.getProperty("l120m01a.error25"),
					getClass());
		}
		service1205.save(l120m01a, l120m01b);
		// if (params.getAsBoolean("showMsg", true)) {
		// // 印出儲存成功訊息!
		// result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
		// .getMainMessage(this.getComponent(), "EFD0017"));
		// }
		return result;
	}

	/**
	 * 儲存會簽內容(授管處補充說明+審查意見)
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveSignContent2(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		CapAjaxFormResult L120M01aForm13 = new CapAjaxFormResult();
		CapAjaxFormResult LMS1205S01Form = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String itemDscr0A = params.getString("itemDscr0A");
		String itemDscr0B = params.getString("itemDscr0B");
		String sHeadLeader = trimNull(params.getString("sHeadLeader"));
		String sHeadSubLeader = trimNull(params.getString("sHeadSubLeader"));
		String sHeadReCheck = trimNull(params.getString("sHeadReCheck"));
		String sHeadAppraiser = trimNull(params.getString("sHeadAppraiser"));
		LMS1205S01Form.set("headLeader", withIdName(Util.trim(sHeadLeader)));
		LMS1205S01Form.set("headSubLeader",
				withIdName(Util.trim(sHeadSubLeader)));
		LMS1205S01Form.set("headReCheck", withIdName(Util.trim(sHeadReCheck)));
		LMS1205S01Form.set("headAppraiser",
				withIdName(Util.trim(sHeadAppraiser)));

		L120M01aForm13.set("itemDscr0A", itemDscr0A);
		L120M01aForm13.set("itemDscr0B", itemDscr0B);
		L120M01D model1 = service1205.findL120m01dByUniqueKey(mainId, "A");
		L120M01D model2 = service1205.findL120m01dByUniqueKey(mainId, "B");
		// List<L120M01F> list = service1205.findL120m01fByMainId(mainId);
		if (model1 == null) {
			model1 = new L120M01D();
			model1.setMainId(mainId);
			model1.setItemType("A");
			model1.setCreateTime(CapDate.getCurrentTimestamp());
			model1.setCreator(user.getUserId());
		}
		if (model2 == null) {
			model2 = new L120M01D();
			model2.setMainId(mainId);
			model2.setItemType("B");
			model2.setCreateTime(CapDate.getCurrentTimestamp());
			model2.setCreator(user.getUserId());
		}

		if (!Util.isEmpty(Util.trim(itemDscr0A))) {
			model1.setItemDscr(itemDscr0A);
		} else {
			model1.setItemDscr("");
		}
		if (!Util.isEmpty(Util.trim(itemDscr0B))) {
			model2.setItemDscr(itemDscr0B);
		} else {
			model2.setItemDscr("");
		}
		L120M01A l120m01a = service1205.findL120m01aByMainId(mainId);
		try {
			// service1205.saveArea(l120m01a, model1, model2, saveList);
			service1205.save(l120m01a, model1, model2);
		} catch (Exception e) {
			logger.error("[saveSignContent2] service1205.save EXCEPTION!!", e);
			Map<String, String> param = new HashMap<String, String>();
			param.put("colName", space);
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
		}
		if (params.getAsBoolean("showMsg", true)) {
			// 印出儲存成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0017"));
		}
		result.set("L120M01aForm13", L120M01aForm13);
		result.set("LMS1205S01Form", LMS1205S01Form);
		return result;
	}

	/**
	 * 儲存簽章欄(授管處)
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveSignContentF2(PageParameters params)
			throws CapException {
		return super._LMSM01FormHandler_saveSignContentF2(params);
	}

	// /**
	// * 儲存會簽內容(營運中心說明及意見)
	// *
	// * @param params
	// * PageParameters
	// * @param parent
	// * Component
	// * @return CapAjaxFormResult
	// * @throws CapException
	// */
	// @DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	// public IResult saveSignContent3(PageParameters params)
	// throws CapException {
	// MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
	// CapAjaxFormResult result = new CapAjaxFormResult();
	// CapAjaxFormResult L120M01aForm15 = new CapAjaxFormResult();
	// CapAjaxFormResult LMS1205S01Form = new CapAjaxFormResult();
	// String mainId = params.getString(EloanConstants.MAIN_ID);
	// String itemDscr07 = params.getString("itemDscr07");
	// String itemDscr08 = params.getString("itemDscr08");
	// String sAreaLeader = trimNull(params.getString("sAreaLeader"));
	// String sAreaSubLeader = trimNull(params.getString("sAreaSubLeader"));
	// String sAreaManager = trimNull(params.getString("sAreaManager"));
	// String sAreaAppraiser = trimNull(params.getString("sAreaAppraiser"));
	// String itemTitle = params.getString("itemTitle");
	// String _itemTitle = params.getString("_itemTitle");
	//
	// if (!Util.isEmpty(sAreaLeader)) {
	// LMS1205S01Form
	// .set("areaLeader", withIdName(Util.trim(sAreaLeader)));
	// }
	// if (!Util.isEmpty(sAreaSubLeader)) {
	// LMS1205S01Form.set("areaSubLeader",
	// withIdName(Util.trim(sAreaSubLeader)));
	// }
	// if (!Util.isEmpty(sAreaManager)) {
	// LMS1205S01Form.set("areaManager",
	// withIdName(Util.trim(sAreaManager)));
	// }
	// if (!Util.isEmpty(sAreaAppraiser)) {
	// LMS1205S01Form.set("areaAppraiser",
	// withIdName(Util.trim(sAreaAppraiser)));
	// }
	//
	// L120M01aForm15.set("itemDscr07", itemDscr07);
	// L120M01aForm15.set("itemDscr08", itemDscr08);
	// L120M01D model1 = service1205.findL120m01dByUniqueKey(mainId, "7");
	// L120M01D model2 = service1205.findL120m01dByUniqueKey(mainId, "8");
	// // List<L120M01F> list = service1205.findL120m01fByMainId(mainId);
	// if (model1 == null) {
	// model1 = new L120M01D();
	// model1.setMainId(mainId);
	// model1.setItemType("7");
	// model1.setCreateTime(CapDate.getCurrentTimestamp());
	// model1.setCreator(user.getUserId());
	// }
	// if (model2 == null) {
	// model2 = new L120M01D();
	// model2.setMainId(mainId);
	// model2.setItemType("8");
	// model2.setCreateTime(CapDate.getCurrentTimestamp());
	// model2.setCreator(user.getUserId());
	// }
	//
	// if (!Util.isEmpty(Util.trim(itemDscr07))) {
	// model1.setItemDscr(itemDscr07);
	// } else {
	// model1.setItemDscr("");
	// }
	// if (!Util.isEmpty(Util.trim(itemDscr08))) {
	// model2.setItemDscr(itemDscr08);
	// } else {
	// model2.setItemDscr("");
	// }
	// if (!Util.isEmpty(Util.trim(itemTitle))) {
	// if ("1".equals(Util.trim(_itemTitle))) {
	// L120M01A l120m01a = service1205.findL120m01aByMainId(mainId);
	// model2.setItemTitle(Util.trim(l120m01a.getRptTitleArea1()));
	// } else {
	// model2.setItemTitle(Util.trim(itemTitle));
	// }
	// }
	// L120M01A l120m01a = service1205.findL120m01aByMainId(mainId);
	// try {
	// // service1205.saveArea(l120m01a, model1, model2, saveList);
	// service1205.save(l120m01a, model1, model2);
	// } catch (Exception e) {
	// logger.error("[saveSignContent3] service1205.save EXCEPTION!!", e);
	// Map<String, String> param = new HashMap<String, String>();
	// param.put("colName", space);
	// throw new CapMessageException(RespMsgHelper.getMessage(parent,
	// "EFD0007", param), getClass());
	// }
	// if (params.getAsBoolean("showMsg", true)) {
	// // 印出儲存成功訊息!
	// result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
	// .getMainMessage(this.getComponent(), "EFD0017"));
	// }
	// result.set("L120M01aForm15", L120M01aForm15);
	// result.set("LMS1205S01Form", LMS1205S01Form);
	// return result;
	// }

	// /**
	// * 儲存簽章欄(營運中心)
	// *
	// * @param params
	// * @param parent
	// * @return
	// * @throws CapException
	// */
	// @DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	// public IResult saveSignContentF3(PageParameters params)
	// throws CapException {
	// // MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
	// CapAjaxFormResult result = new CapAjaxFormResult();
	// CapAjaxFormResult L120M01aForm15 = new CapAjaxFormResult();
	// CapAjaxFormResult LMS1205S01Form = new CapAjaxFormResult();
	// String mainId = params.getString(EloanConstants.MAIN_ID);
	// // String itemDscr07 = params.getString("itemDscr07");
	// // String itemDscr08 = params.getString("itemDscr08");
	// String sAreaLeader = trimNull(params.getString("sAreaLeader"));
	// String sAreaSubLeader = trimNull(params.getString("sAreaSubLeader"));
	// String sAreaManager = trimNull(params.getString("sAreaManager"));
	// String sAreaAppraiser = trimNull(params.getString("sAreaAppraiser"));
	// String sUnitManager3 = trimNull(params.getString("sUnitManager3"));
	//
	// LMS1205S01Form.set("areaLeader", withIdName(Util.trim(sAreaLeader)));
	// LMS1205S01Form.set("areaSubLeader",
	// withIdName(Util.trim(sAreaSubLeader)));
	// LMS1205S01Form.set("areaManager", withIdName(Util.trim(sAreaManager)));
	// LMS1205S01Form.set("areaAppraiser",
	// withIdName(Util.trim(sAreaAppraiser)));
	//
	// List<L120M01F> list = service1205.findL120m01fByMainId(mainId);
	//
	// List<L120M01F> saveList = new ArrayList<L120M01F>();
	// if (!Util.isEmpty(sAreaLeader) && !Util.isEmpty(sAreaSubLeader)
	// && !Util.isEmpty(sAreaManager) && !Util.isEmpty(sAreaAppraiser)) {
	// if (!list.isEmpty()) {
	// for (L120M01F model : list) {
	// if ("3".equals(model.getBranchType())) {
	// saveList.add(model);
	// }
	// }
	// }
	// if (!saveList.isEmpty()) {
	// service1205.delListL120m01f(saveList);
	// saveList.clear();
	// }
	// // 第一次新增營運簽章欄
	// saveList.add(addAreaL120m01f(mainId, "L6", sAreaLeader));
	// saveList.add(addAreaL120m01f(mainId, "L5", sAreaSubLeader));
	// saveList.add(addAreaL120m01f(mainId, "L3", sAreaManager));
	// saveList.add(addAreaL120m01f(mainId, "L1", sAreaAppraiser));
	// saveList.add(addAreaL120m01f(mainId, "L9", sUnitManager3));
	// }
	//
	// L120M01A l120m01a = service1205.findL120m01aByMainId(mainId);
	// try {
	// // service1205.saveArea(l120m01a, model1, model2, saveList);
	// service1205.saveArea(l120m01a, saveList);
	// } catch (Exception e) {
	// logger.error("[saveSignContent3] service1205.save EXCEPTION!!", e);
	// Map<String, String> param = new HashMap<String, String>();
	// param.put("colName", space);
	// throw new CapMessageException(RespMsgHelper.getMessage(parent,
	// "EFD0007", param), getClass());
	// }
	// if (params.getAsBoolean("showMsg", true)) {
	// // 印出儲存成功訊息!
	// result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
	// .getMainMessage(this.getComponent(), "EFD0017"));
	// }
	// result.set("L120M01aForm15", L120M01aForm15);
	// result.set("LMS1205S01Form", LMS1205S01Form);
	// return result;
	// }

	/**
	 * 新建國金部簽章欄
	 * 
	 * @param mainId
	 * @param headJob
	 * @param headNo
	 * @return
	 */
	public L120M01F addSeaL120m01f(String mainId, String headJob, String headNo) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		L120M01F l120m01f = new L120M01F();
		l120m01f.setMainId(mainId);
		l120m01f.setBranchType("6");
		l120m01f.setBranchId(user.getUnitNo());
		l120m01f.setStaffJob(headJob);
		l120m01f.setStaffNo(headNo);
		l120m01f.setCreator(user.getUserId());
		l120m01f.setCreateTime(CapDate.getCurrentTimestamp());
		l120m01f.setUpdater(user.getUserId());
		l120m01f.setUpdateTime(CapDate.getCurrentTimestamp());
		return l120m01f;
	}

	/**
	 * 新建授管處簽章欄
	 * 
	 * @param mainId
	 * @param headJob
	 * @param headNo
	 * @return
	 */
	private L120M01F addHeadL120m01f(String mainId, String headJob,
			String headNo) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		L120M01F l120m01f = new L120M01F();
		l120m01f.setMainId(mainId);
		l120m01f.setBranchType("4");
		l120m01f.setBranchId(user.getUnitNo());
		l120m01f.setStaffJob(headJob);
		l120m01f.setStaffNo(headNo);
		l120m01f.setCreator(user.getUserId());
		l120m01f.setCreateTime(CapDate.getCurrentTimestamp());
		l120m01f.setUpdater(user.getUserId());
		l120m01f.setUpdateTime(CapDate.getCurrentTimestamp());
		return l120m01f;
	}

	// /**
	// * 新建營運中心簽章欄
	// *
	// * @param mainId
	// * @param areaJob
	// * @param areaNo
	// * @return
	// */
	// private L120M01F addAreaL120m01f(String mainId, String areaJob,
	// String areaNo) {
	// MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
	// L120M01F l120m01f = new L120M01F();
	// l120m01f.setMainId(mainId);
	// l120m01f.setBranchType("3");
	// l120m01f.setBranchId(user.getUnitNo());
	// l120m01f.setStaffJob(areaJob);
	// l120m01f.setStaffNo(areaNo);
	// l120m01f.setCreator(user.getUserId());
	// l120m01f.setCreateTime(CapDate.getCurrentTimestamp());
	// l120m01f.setUpdater(user.getUserId());
	// l120m01f.setUpdateTime(CapDate.getCurrentTimestamp());
	// return l120m01f;
	// }

	// /**
	// * 查詢會簽內容(營運中心)
	// *
	// * @param params
	// * PageParameters
	// * @param parent
	// * Component
	// * @return CapAjaxFormResult
	// * @throws CapException
	// */
	// @DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	// public IResult querySignContent(PageParameters params)
	// throws CapException {
	// CapAjaxFormResult result = new CapAjaxFormResult();
	// String mainId = params.getString(EloanConstants.MAIN_ID);
	// L121M01B model = service1205.findL121m01bByUniqueKey(mainId, "9");
	// if (model != null) {
	// CapAjaxFormResult formSea = DataParse.toResult(model);
	// formSea.set("itemDscr09", Util.trim(model.getItemDscr()));
	// result.set("formSea", formSea);
	// } else {
	// CapAjaxFormResult formSea = new CapAjaxFormResult();
	// formSea.set("itemDscr09", "");
	// result.set("formSea", formSea);
	// }
	// return result;
	// }

	/**
	 * 查詢會簽內容(授管處補充說明+審查意見)
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult querySignContent2(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		L120M01D l120m01d0A = service1205.findL120m01dByUniqueKey(mainId, "A");
		L120M01D l120m01d0B = service1205.findL120m01dByUniqueKey(mainId, "B");
		CapAjaxFormResult form0AL120m01d = new CapAjaxFormResult();
		CapAjaxFormResult form0BL120m01d = new CapAjaxFormResult();
		if (l120m01d0A != null) {
			form0AL120m01d = DataParse.toResult(l120m01d0A);
			form0AL120m01d.set("tItemDscr0A",
					Util.trim(l120m01d0A.getItemDscr()));
		} else {
			form0AL120m01d = new CapAjaxFormResult();
			form0AL120m01d.set("tItemDscr0A", "");
		}
		if (l120m01d0B != null) {
			form0BL120m01d = DataParse.toResult(l120m01d0B);
			form0BL120m01d.set("tItemDscr0B",
					Util.trim(l120m01d0B.getItemDscr()));
		} else {
			form0BL120m01d = new CapAjaxFormResult();
			form0BL120m01d.set("tItemDscr0B", "");
		}
		form0AL120m01d.add(form0BL120m01d);
		result.set("L120M01aForm13", form0AL120m01d);
		return result;
	}

	// /**
	// * 查詢會簽內容(營運中心說明及意見)
	// *
	// * @param params
	// * PageParameters
	// * @param parent
	// * Component
	// * @return CapAjaxFormResult
	// * @throws CapException
	// */
	// @DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	// public IResult querySignContent3(PageParameters params)
	// throws CapException {
	// CapAjaxFormResult result = new CapAjaxFormResult();
	// String mainId = params.getString(EloanConstants.MAIN_ID);
	// L120M01D l120m01d07 = service1205.findL120m01dByUniqueKey(mainId, "7");
	// L120M01D l120m01d08 = service1205.findL120m01dByUniqueKey(mainId, "8");
	// CapAjaxFormResult form07L120m01d = new CapAjaxFormResult();
	// CapAjaxFormResult form08L120m01d = new CapAjaxFormResult();
	// if (l120m01d07 != null) {
	// form07L120m01d = DataParse.toResult(l120m01d07);
	// form07L120m01d.set("tItemDscr07",
	// Util.trim(l120m01d07.getItemDscr()));
	// } else {
	// form07L120m01d = new CapAjaxFormResult();
	// form07L120m01d.set("tItemDscr07", "");
	// }
	// if (l120m01d08 != null) {
	// form08L120m01d = DataParse.toResult(l120m01d08);
	// form08L120m01d.set("tItemDscr08",
	// Util.trim(l120m01d08.getItemDscr()));
	// } else {
	// form08L120m01d = new CapAjaxFormResult();
	// form08L120m01d.set("tItemDscr08", "");
	// }
	// form07L120m01d.add(form08L120m01d);
	// result.set("L120M01aForm15", form07L120m01d);
	// return result;
	// }

	/**
	 * 提會(提授審、提催收、提常董)
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult sendTo(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String hqMeetFlag = params.getString("hqMeetFlag");
		// J-110-0521_05097_B1001 Web e-Loan海外授信系統增加留存案件流程紀錄
		String meetingDate = Util.trim(params.getString("meetingDate"));
		String mainId = params.getString(EloanConstants.MAIN_ID);
		L120M01A model = service1205.findL120m01aByMainId(mainId);
		if (model != null) {
			if (!"3".equals(hqMeetFlag) && !UtilConstants.Casedoc.HqMeetFlag.審計委員會.equals(hqMeetFlag)) {
				model.setMeetingType(hqMeetFlag);
			}
			model.setHqMeetFlag(hqMeetFlag);
			service1205.save(model);

			// J-110-0521_05097_B1001 Web e-Loan海外授信系統增加留存案件流程紀錄
			// J-109-0479_05097_B1001 Web
			// e-Loan簽報書增加各別流程控管階段的時間點並提供列印案件階段進度及統計excel下載
			if (Util.notEquals(meetingDate, "")) {
				L120S17A l120s17a = service1205.findL120s17aByMainId(mainId);
				if (l120s17a == null) {
					l120s17a = new L120S17A();
					l120s17a.setMainId(mainId);
					l120s17a.setCreateTime(CapDate.getCurrentTimestamp());
					l120s17a.setCreator(user.getUserId());
				}

				if (Util.equals(hqMeetFlag, "1")) {
					// 提授審會
					l120s17a.setDateF1(Util.parseDate(meetingDate));
				} else if (Util.equals(hqMeetFlag, "2")) {
					// 提催收會
					l120s17a.setDateF2(Util.parseDate(meetingDate));
				} else if (Util.equals(hqMeetFlag, UtilConstants.Casedoc.HqMeetFlag.審計委員會)) {
					// J-113-0337  配合本行將於第18屆董事會設置審計委員會替代監查人，新增「審計委員會」
					l120s17a.setDateF4(Util.parseDate(meetingDate));
				} else {
					// 提常董會
					l120s17a.setDateF3(Util.parseDate(meetingDate));
				}

				if (UtilConstants.unitType.授管處.equals(Util.trim(user
						.getUnitType()))) {

					l120s17a.setRoleG(user.getUserId());

				}

				l120s17a.setUpdater(user.getUserId());
				l120s17a.setUpdateTime(CapDate.getCurrentTimestamp());

				service1205.save(l120s17a);

				// J-110-0521_05097_B1001 Web e-Loan海外授信系統增加留存案件流程紀錄
				// J-109-0479_05097_B1001 Web
				// e-Loan簽報書增加各別流程控管階段的時間點並提供列印案件階段進度及統計excel下載
				lmsService.setL120s17aData(mainId);

			}

		}
		// // 印出執行成功訊息!
		// result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
		// RespMsgHelper.getMainMessage(this.getComponent(), "EFD0018"));
		return result;
	}

	// /**
	// * 查詢授審會會期
	// *
	// * @param params
	// * @param parent
	// * @return
	// * @throws CapException
	// */
	// @DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	// public IResult queryLogin1(PageParameters params)
	// throws CapException {
	// CapAjaxFormResult result = new CapAjaxFormResult();
	// CapAjaxFormResult formLms1200v62 = new CapAjaxFormResult();
	// formLms1200v62.set("rptTitle1a", "");
	// formLms1200v62.set("rptTitle1b", "");
	// formLms1200v62.set("rptTitle1c", "");
	// formLms1200v62.set("rptTitle1d", "");
	// String mainId = params.getString(EloanConstants.MAIN_ID);
	// L120M01A model = service1205.findL120m01aByMainId(mainId);
	// if (model != null) {
	// result.set(EloanConstants.OID, model.getOid());
	// String rptTitle1 = model.getRptTitle1();
	// if (!Util.isEmpty(rptTitle1)) {
	// formLms1200v62.set("rptTitle1a", rptTitle1.substring(0, 3));
	// formLms1200v62.set("rptTitle1b", rptTitle1.substring(4, 6));
	// formLms1200v62.set("rptTitle1c", rptTitle1.substring(7, 9));
	// String times = rptTitle1.substring(11);
	// formLms1200v62.set("rptTitle1d",
	// times.substring(0, times.indexOf("次", 0)));
	// }
	// }
	//
	// result.set("LMS1200V62Form1", formLms1200v62);
	// return result;
	// }
	//
	// /**
	// * 登錄授審會會期(含營運中心)
	// *
	// * @param params
	// * PageParameters
	// * @param parent
	// * Component
	// * @return CapAjaxFormResult
	// * @throws CapException
	// */
	// @DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	// public IResult login1(PageParameters params)
	// throws CapException {
	// CapAjaxFormResult result = new CapAjaxFormResult();
	// String LMS1200V62Form1 = params.getString("LMS1200V62Form1");
	// String caseName = params.getString("caseName");
	// boolean isArea = params.getBoolean("isArea");
	// JSONObject json = JSONObject.fromObject(LMS1200V62Form1);
	// StringBuilder strB = new StringBuilder();
	// strB.append(
	// Util.addZeroWithValue(
	// Util.trim((String) json.get("rptTitle1a")), 3))
	// .append("年")
	// .append(Util.addZeroWithValue(
	// Util.trim((String) json.get("rptTitle1b")), 2))
	// .append("月")
	// .append(Util.addZeroWithValue(
	// Util.trim((String) json.get("rptTitle1c")), 2))
	// .append("日第")
	// .append(Util.trim((String) json.get("rptTitle1d"))).append("次")
	// .append(Util.trim(caseName));
	// // 取得list中所有資料組成的字串
	// String listOid = params.getString("oids");
	// // 取得sign的資料
	// String strSign = ",";
	// String oid = params.getString(EloanConstants.OID);
	// List<L120M01A> list = new ArrayList<L120M01A>();
	// if (!Util.isEmpty(oid)) {
	// L120M01A model = service1205.findL120m01aByOid(oid);
	// if (model != null) {
	// if (isArea) {
	// // 營運中心授審會會期
	// model.setRptTitleArea1(strB.toString());
	// } else {
	// // 授管處授審會/催收會會期
	// model.setRptTitle1(strB.toString());
	// }
	// list.add(model);
	// }
	// } else {
	// // 將已取得的字串轉換成一陣列，分割辨識為sign內容
	// String[] oidArray = listOid.split(strSign);
	// if (oidArray.length > 0) {
	// for (String theOid : oidArray) {
	// L120M01A model = service1205.findL120m01aByOid(theOid);
	// if (model != null) {
	// if (isArea) {
	// // 營運中心授審會會期
	// model.setRptTitleArea1(strB.toString());
	// } else {
	// // 授管處授審會/催收會會期
	// model.setRptTitle1(strB.toString());
	// }
	// list.add(model);
	// }
	// }
	// }
	// }
	//
	// try {
	// service1205.saveL120m01as(list);
	// } catch (Exception e) {
	// logger.error("[login1] service1205.save EXCEPTION!!", e);
	// Map<String, String> param = new HashMap<String, String>();
	// param.put("colName", space);
	// throw new CapMessageException(RespMsgHelper.getMessage(parent,
	// "EFD0007", param), getClass());
	// }
	// // 印出執行成功訊息!
	// result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
	// RespMsgHelper.getMainMessage(this.getComponent(), "EFD0018"));
	// return result;
	// }

	/**
	 * 登錄催收會會期
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult login2(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String LMS1200V63Form1 = params.getString("LMS1200V63Form1");
		JSONObject json = JSONObject.fromObject(LMS1200V63Form1);
		StringBuilder strB = new StringBuilder();
		strB.append(
				Util.addZeroWithValue(
						Util.trim((String) json.get("rptTitle1a")), 3))
				.append("年")
				.append(Util.addZeroWithValue(
						Util.trim((String) json.get("rptTitle1b")), 2))
				.append("月")
				.append(Util.addZeroWithValue(
						Util.trim((String) json.get("rptTitle1c")), 2))
				.append("日第")
				.append(Util.trim((String) json.get("rptTitle1d"))).append("次")
				.append("逾期放款、催收款及呆帳審議委員會");
		// 取得list中所有資料組成的字串
		String listOid = params.getString("oids");
		// 取得sign的資料
		String strSign = ",";
		// 將已取得的字串轉換成一陣列，分割辨識為sign內容
		String[] oidArray = listOid.split(strSign);
		// String oid = params.getString(EloanConstants.OID);
		List<L120M01A> list = new ArrayList<L120M01A>();
		if (oidArray.length > 0) {
			for (String oid : oidArray) {
				L120M01A model = service1205.findL120m01aByOid(oid);
				if (model != null) {
					model.setRptTitle1(strB.toString());
					list.add(model);
				}
			}
		}
		try {
			service1205.saveL120m01as(list);
		} catch (Exception e) {
			logger.error("[login2] service1205.save EXCEPTION!!", e);
			Map<String, String> param = new HashMap<String, String>();
			param.put("colName", space);
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
		}
		// 印出執行成功訊息!
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0018"));
		return result;
	}

	/**
	 * 登錄常董會會期
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult login3(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String LMS1200V64Form1 = params.getString("LMS1200V64Form1");
		String caseName = params.getString("caseName");
		JSONObject json = JSONObject.fromObject(LMS1200V64Form1);
		StringBuilder strB = new StringBuilder();
		strB.append(
				Util.addZeroWithValue(
						Util.trim((String) json.get("rptTitle1a")), 3))
				.append("年")
				.append(Util.addZeroWithValue(
						Util.trim((String) json.get("rptTitle1b")), 2))
				.append("月")
				.append(Util.addZeroWithValue(
						Util.trim((String) json.get("rptTitle1c")), 2))
				.append("日第")
				.append(Util.trim((String) json.get("rptTitle1d")))
				.append("屆第")
				.append(Util.trim((String) json.get("rptTitle1e"))).append("次")
				.append(Util.trim(caseName));
		// 取得list中所有資料組成的字串
		String listOid = params.getString("oids");
		// 取得sign的資料
		String strSign = ",";
		// 將已取得的字串轉換成一陣列，分割辨識為sign內容
		String[] oidArray = listOid.split(strSign);
		// String oid = params.getString(EloanConstants.OID);
		List<L120M01A> list = new ArrayList<L120M01A>();
		if (oidArray.length > 0) {
			for (String oid : oidArray) {
				L120M01A model = service1205.findL120m01aByOid(oid);
				if (model != null) {
					model.setRptTitle2(strB.toString());
					list.add(model);
				}
			}
		}
		try {
			service1205.saveL120m01as(list);
		} catch (Exception e) {
			logger.error("[login3] service1205.save EXCEPTION!!", e);
			Map<String, String> param = new HashMap<String, String>();
			param.put("colName", space);
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
		}
		// 印出執行成功訊息!
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0018"));
		return result;
	}

	/**
	 * 儲存配偶欄位(同本案借保人)
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveCouple(PageParameters params)
			throws CapException {
		return super._LMSM01FormHandler_saveCouple(params);
	}

	public IResult prepareImportL120S01m(PageParameters params)
			throws CapException {
		return super._LMSM01FormHandler_prepareImportL120S01m(params);
	}

	public IResult queryL120S01m_date(PageParameters params)
			throws CapException {
		return super._LMSM01FormHandler_queryL120S01m_date(params);
	}
}