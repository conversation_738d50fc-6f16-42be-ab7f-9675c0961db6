$(function(){
	//==================
    var auth = (responseJSON ? responseJSON.Auth : {});
    if (auth.readOnly || responseJSON.mainDocStatus != "1") {
        $('body').lockDoc();
    }
    $("#allCheck").click(function(){
        $(":checkbox").prop("checked", true);
    });
    $("#clean").click(function(){
        $(":checkbox").prop("checked",false);
    });
    $.ajax({
        type: "POST",
        handler: "lms1825formhandler",
        data: {
            formAction: "queryBranch"
        }
        }).done(function(obj){
            var elmArr = [];
            var col_cnt = 1;
            if(obj.itemOrder.length>6){
            	col_cnt = 3;
            }
        	$.each(obj.itemOrder, function(idx, brNo) {
        		var brName = obj.item[brNo];
        		elmArr.push("<label style='letter-spacing:0px;cursor:pointer;font-weight:normal;'><input value='"+brNo+"' id='branchList' name='branchList' type='checkbox'>"+brNo +" "+ brName+"</label>");            		
			});
        	//===
        	//補empty col
        	var addcnt = (col_cnt - (elmArr.length % col_cnt)); 
        	if(addcnt==col_cnt){
        		addcnt = 0;
        	}
        	for(var i=0;i<addcnt;i++){
        		elmArr.push("&nbsp;");  
        	}
        	
        	var dyna = [];
        	dyna.push("<table width='100%' border='0' cellspacing='0' cellpadding='0'>");
        	dyna.push("<tr>");
        	for(var i=0;i<elmArr.length;i++){
        		dyna.push("<td>"+elmArr[i]+"</td>");
        		if( (i+1) % col_cnt==0){
        			dyna.push("</tr><tr>");		
        		}
        	}
        	dyna.push("</tr>");
        	dyna.push("</table>");
        	$("#order").append(dyna.join("\n"));
			initForm();
    });
 function initForm() {
    $.form.init({
        formHandler: "lms1825formhandler",
        formPostData: {
            formAction: "query"
        },
        loadSuccess: function(json){
            $('#L182M01AForm').injectData(json.L182M01AForm);
            $('#baseDate2').val(json.baseDate2);
			if (json.branchList) {
			    var banchlist = json.branchList.split(",");
			    for (var i = 0; i < banchlist.length; i++) {
			        $("input[name='branchList'][value='" + banchlist[i].trim() + "']").prop("checked", true);
			    }
			}
            if (responseJSON.mainDocStatus != "1") {
                $("input").prop("disabled", true);
            }
        }
    });
  }
    var btn = $("#buttonPanel");
    btn.find("#btnSave").click(function(){
        var branchList = new Array();
        if ($("[name='branchList']:checked").length == 0) {
            return CommonAPI.showMessage(i18n.lms1825m01["chooseOneBank"]);
        }
        $("[name='branchList']:checked").each(function(index){
            branchList[index] = $("[name='branchList']:checked").eq(index).val();
        });
        if ($("#L182M01AForm").valid()) {
            $.ajax({
                type: "POST",
                handler: "lms1825formhandler",
                data: {
                    formAction: "saveMain",
                    branchList: branchList,
                    baseDate: $("#baseDate2").val(),
                    oid: $("#oid").val()
                }
                }).done(function(obj){
                    $('#L182M01AForm').injectData(obj.L182M01AForm);
					//alert(obj.branchList);
					if (obj.branchList) {
					    var banchlist = obj.branchList.split(",");
					    for (var i = 0; i < banchlist.length; i++) {
					        $("input[name='branchList'][value='" + banchlist[i].trim() + "']").prop("checked", true);
					    }
					}
                    CommonAPI.triggerOpener("gridview", "reloadGrid");
            });
        }
    });
});
