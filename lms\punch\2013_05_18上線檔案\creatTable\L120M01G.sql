---------------------------------------------------------
-- LMS.L120M01G 整批貸款總額度資訊檔
---------------------------------------------------------

---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.L120M01G;
CREATE TABLE LMS.L120M01G (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)      not null,
	PARENTID      CHAR(10)     ,
	PARENTDUPNO   CHAR(1)      ,
	PROJECTNAME   VARCHAR(120) ,
	<PERSON><PERSON>LDNA<PERSON>     VARCHAR(120) ,
	ISSUEBRNO     CHAR(3)      ,
	PARENTYEAR    DECIMAL(4,0) ,
	PARENTCNTRNO  CHAR(12)     ,
	PACKNO        DECIMAL(2,0) ,
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_L120M01G PRIMARY KEY(OID)
) IN EL_DATA_4KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XL120M01G01;
CREATE UNIQUE INDEX LMS.XL120M01G01 ON LMS.L120M01G   (MAINID);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.L120M01G IS '整批貸款總額度資訊檔';
COMMENT ON LMS.L120M01G (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	PARENTID      IS '總戶統編', 
	PARENTDUPNO   IS '重複序號', 
	PROJECTNAME   IS '團貸總戶名稱(專案名)', 
	BUILDNAME     IS '團貸總戶名稱(建案名)', 
	ISSUEBRNO     IS '簽案分行', 
	PARENTYEAR    IS '總額度申請年度', 
	PARENTCNTRNO  IS '總戶序號', 
	PACKNO        IS '批　　號', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
