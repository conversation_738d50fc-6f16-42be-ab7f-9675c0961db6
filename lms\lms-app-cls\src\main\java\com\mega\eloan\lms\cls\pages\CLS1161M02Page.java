/* 
 * CLS1161M01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.cls.panels.CLS1161S21Panel;
import com.mega.eloan.lms.cls.panels.CLS1161S22Panel;
import com.mega.eloan.lms.model.C160M02A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.jcs.common.Util;

/**
 * <pre>
 * 中鋼整批評等檔
 * </pre>
 * 
 * @since 2015/07/17
 * <AUTHOR>
 * @version <ul>
 *          <li>2015/07/17,EL07623,new
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls1161m02/{page}")
public class CLS1161M02Page extends AbstractEloanForm {

	final String TAB_CTX = "_tabCtx";
	final String TAB_SIGN = "tab-";

	@Override
	public void execute(ModelMap model, PageParameters params) {
		MegaSSOUserDetails users = MegaSSOSecurityContext.getUserDetails();

		// hide tab
		// 912風控處資訊處可查看詳細資料
		boolean dtl_view = users.getSsoUnitNo().matches("900|912");
		dtl_view = true;
		model.addAttribute("_for912View", dtl_view);

		// i18n
		renderJsI18N(CLS1161M02Page.class);
		// tabs
		int page = params.getAsInteger(EloanConstants.PAGE, 1);
		String tabID = TAB_SIGN + Util.addZeroWithValue(page, 2); // 指定ID
		Panel panel = getPanel(page);
		panel.processPanelData(model, params);
		model.addAttribute("tabIdx", tabID);
	}// ;

	// 頁籤
	@SuppressWarnings("unused")
	public Panel getPanel(int index) {
		Panel panel = null;
		switch (index) {
		case 1:
			renderJsI18N(CLS1161S21Panel.class);
			panel = new CLS1161S21Panel(TAB_CTX, true);
			break;
		case 2:
			renderJsI18N(CLS1161S22Panel.class);
			panel = new CLS1161S22Panel(TAB_CTX, true);
			break;
		default:
			renderJsI18N(CLS1161S21Panel.class);
			panel = new CLS1161S21Panel(TAB_CTX, true);
			break;
		}
		if (panel == null) {
			panel = new Panel(TAB_CTX);
		}

		return panel;
	}

	// 驗證文件狀態Class
	public Class<? extends Meta> getDomainClass() {
		return C160M02A.class;
	}
}
