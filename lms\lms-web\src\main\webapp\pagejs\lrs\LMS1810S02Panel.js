initDfd.done(function(json){
	var grid_height = 270;
	var $grid_fcrdGrad = $("#grid_fcrdGrad").iGrid({
        handler: 'lms1810gridhandler',        
        height: grid_height,
        postData: {
        	fcrdType: $("#elfFcrdType").val(),
        	fcrdArea: $("#elfFcrdArea").val(),
        	fcrdPred: $("#elfFcrdPred").val(),
            formAction: "query_elfFcrdGrad"
        },
        needPager: false,        
        shrinkToFit: true,       
        colModel: [
          {//分行名稱
        	  colHeader: '評等等級', name: 'ratingGrad', sortable: false, align: "left" 
          }
        ],        
        ondblClickRow: function(rowid){
        	
        }        
    });
	
	var ChoiceReportGrid = $("#choiceReportGrid").iGrid({
        handler: "lms1810gridhandler",
        height: 235,
        width: 400,
		needPager: false,
		localFirst: true,
        postData: {
            formAction: "queryL120M01A"
        },
        rowNum: 10,
        rownumbers: true,
        sortname: 'endDate|caseDate|caseNo',
        sortorder: 'desc|desc|asc',
        colModel: [{
            colHeader: i18n.lms1810m01['L181M01B.custName'],//"主要借款人"
            name: 'custName',
            align: "left",
            width: 60
        }, {
            colHeader: i18n.lms1810m01['L181M01B.caseNo'],//案號	
            name: 'caseNo',
            align: "left",
            width: 100
        }, {
            colHeader: i18n.lms1810m01['L181M01B.signDate'],//"簽案日期"
            name: 'caseDate',
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d',
                newformat: 'Y-m-d'
            },
            align: "center",
            width: 60
        }, {
            colHeader: i18n.lms1810m01['L181M01B.approveDate'],//"核准日期"
            name: 'endDate',
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d',
                newformat: 'Y-m-d'
            },
            align: "center",
            width: 60
        }, {
            colHeader: "mainId",
            name: 'mainId',
            hidden: true //是否隱藏
        }]
    });
	
	
	$("#btn_fcrdGrad").click(function(){
		
		var fcrdType = $("#elfFcrdType").val();
		var fcrdArea = $("#elfFcrdArea").val()
		var fcrdPred = $("#elfFcrdPred").val();	
		if(fcrdType==""){
			CommonAPI.showErrorMessage(i18n.lms1810m01['L181M01B.elfFcrdType']+"不可空白");
			return;
		}
		if(fcrdArea==""){
			CommonAPI.showErrorMessage(i18n.lms1810m01['L181M01B.elfFcrdArea']+"不可空白");
			return;
		}
		if(fcrdPred==""){
			CommonAPI.showErrorMessage(i18n.lms1810m01['L181M01B.elfFcrdPred']+"不可空白");
			return;
		}
		if(fcrdType=="4" && fcrdArea=="1"){
			CommonAPI.showErrorMessage("中華信評-外部評等地區別錯誤(必須為本國)");
			return;
		}
		
		$grid_fcrdGrad.jqGrid("setGridParam", {
	    	postData : {
	    		'fcrdType': fcrdType,
	        	'fcrdArea': fcrdArea,
	        	'fcrdPred': fcrdPred,
	            formAction: "query_elfFcrdGrad"
	    	},
			search: true			
		}).trigger("reloadGrid");
		
		$("#_div_fcrdGrad").thickbox({
	        title: "",
	        width: 400,
            height: grid_height+140,
            align: "center",
            valign: "bottom",
            modal: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                	 var data = $grid_fcrdGrad.getSingleData();
                     if (data) {
                    	 $("#elfFcrdGrad").val( data.ratingGrad ); 
    	                 $.thickbox.close();
                     }      
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
	    });
	});
	
	$("#elfNCkdFlag").change(function(k, v){
		var nckdFlag = $(this).val();
		if(nckdFlag=="8"){//本次暫不覆審
			$("#tr_elfNextNwDt").show();
		}else{
			$("#tr_elfNextNwDt").hide();	
		}
	});
	//---
	$("#elfNCkdFlag").trigger('change');
	
	//elfNCkdFlag 會有 2 個 change
	if(json.elfNCkdFlag_1 != ""){
		$("#elfNCkdFlag").change(function(k, v){
			var nckdFlag = $(this).val();
			if(nckdFlag==""){
				API.confirmMessage("本案變更為要覆審，是否要將上次覆審日改為本日 ( 覆審週期計算基準日改為本日 )", function(result){
    	            if (result) {
    	            	$("#elfLRDate").val(CommonAPI.getToday());
    	        	}
    	    	});
			}
		});	
	}	
	//=============
	$("#elfFcrdType").change(function(k, v){
		clear_elfFcrdGrad();
	});
	$("#elfFcrdArea").change(function(k, v){
		clear_elfFcrdGrad();
	});
	$("#elfFcrdPred").change(function(k, v){
		clear_elfFcrdGrad();
	});
	//=============
	$('.yearMonth-picker').datepicker( {
        changeMonth: true,
        changeYear: true,
        showButtonPanel: true,
        dateFormat: 'yymm',
        yearRange : "1900:2100",
        onClose: function(dateText, inst) { 
//            var month = $("#ui-datepicker-div .ui-datepicker-month :selected").val();
//            var year = $("#ui-datepicker-div .ui-datepicker-year :selected").val();
//            $(this).datepicker('setDate', new Date(year, month, 1));
        }
    });
	
	//J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
	$("#btn_showAloanRealData").click(function(){
	    $.ajax({ handler: "lms1810m01formhandler", type: "POST", dataType: "json",
	        data: {
	            formAction: "getAloanRealData",
	            mainOid: responseJSON.mainOid
	        },
	        success: function(json){
				if(json.showMsg){
					API.showMessage(json.showMsg);	
				}
	        		            	        	
	        }
	    });
		 
		
	});

    // 2020/04 配合新冠肺炎紓困貸款專案，新增 J.純紓困貸款戶之首次覆審。
	$("#btn_showRescueCntrNo").click(function(){
        $.ajax({ handler: "lms1810m01formhandler", type: "POST", dataType: "json",
            data: {
                formAction: "getRescueCntrNo",
                mainOid: responseJSON.mainOid
            },
            success: function(json){
                if(json.showMsg){
                    API.showMessage(json.showMsg);
                }
            }
        });
    });
	
	function clear_elfFcrdGrad(){
		$("#elfFcrdGrad").val("");
	}
	
	//J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
	$("#btn_newRpt").click(function(){
		applyRptDoc('1');  //NEW RPTDOC
	});
	//J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
	$("#btn_oldRpt").click(function(){
		applyRptDoc('2');  //OLD RPTDOC
	});
	
	//J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
	function applyRptDoc(type){
		
        $("#choiceReportGrid").jqGrid("setGridParam", {
            postData: {
                mainId: responseJSON.mainId,
				oid: responseJSON.mainOid,
                formAction: "queryL120M01A"
            },
            search: true
        }).trigger("reloadGrid");

        var preFix = "";
        if(type == "1"){
			preFix = "New";
		}else{
			preFix = "Old";
		}

        $("#choiceReport").thickbox({
            //l230m01a.title18=請選擇1份已核准的案件簽報書
            title: i18n.lms1810m01['L181M01B.choiceReport'],
            width: 600,
            height: 440,
            modal: true,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var id = $("#choiceReportGrid").getGridParam('selrow');
                    if (!id) {
                        //action_005=請先選取一筆以上之資料列
                        return CommonAPI.showMessage(i18n.def["action_005"]);
                    }
                    var result = $("#choiceReportGrid").getRowData(id);
                    
					$("#elf"+preFix+"DocNo").val(result.caseNo);
					$("#elf"+preFix+"RptId").val(result.mainId);
					$("#elf"+preFix+"RptDt").val(result.endDate);
                    $.thickbox.close();
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
	}
		
});
