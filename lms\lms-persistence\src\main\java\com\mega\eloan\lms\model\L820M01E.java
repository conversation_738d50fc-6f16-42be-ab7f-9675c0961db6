/* 
 * L820M01E.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** RPA發查明家事公告細檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L820M01E", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId","custId","dupNo"}))
public class L820M01E extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 身分證統編 **/
	@Size(max=10)
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Size(max=1)
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** ETF、外國債劵、連動債 **/
	@Digits(integer=17, fraction=3, groups = Check.class)
	@Column(name="IN_TR_SC_E", columnDefinition="DECIMAL(17,3)")
	private BigDecimal in_tr_sc_e;

	/** 基金 **/
	@Digits(integer=17, fraction=3, groups = Check.class)
	@Column(name="IN_TR_FU", columnDefinition="DECIMAL(17,3)")
	private BigDecimal in_tr_fu;

	/** 理財AUM(A)+(B)+(C) **/
	@Digits(integer=17, fraction=3, groups = Check.class)
	@Column(name="IN_WM_AUM", columnDefinition="DECIMAL(17,3)")
	private BigDecimal in_wm_aum;

	/** (A)信託商品月底餘額 **/
	@Digits(integer=17, fraction=3, groups = Check.class)
	@Column(name="IN_WM_FD", columnDefinition="DECIMAL(17,3)")
	private BigDecimal in_wm_fd;

	/** (B)累積已繳保費/保單價值 **/
	@Digits(integer=17, fraction=3, groups = Check.class)
	@Column(name="IN_WM_IA", columnDefinition="DECIMAL(17,3)")
	private BigDecimal in_wm_ia;

	/** (C)優利投資月平均餘額 **/
	@Digits(integer=17, fraction=3, groups = Check.class)
	@Column(name="IN_WM_STD", columnDefinition="DECIMAL(17,3)")
	private BigDecimal in_wm_std;

	/** 累積下單金額 **/
	@Digits(integer=17, fraction=3, groups = Check.class)
	@Column(name="IN_WM_BAL", columnDefinition="DECIMAL(17,3)")
	private BigDecimal in_wm_bal;

	/** 累計手收金額 **/
	@Digits(integer=17, fraction=3, groups = Check.class)
	@Column(name="IN_WM_FEE", columnDefinition="DECIMAL(17,3)")
	private BigDecimal in_wm_fee;

	/** 
	 * 查詢結果<p/>
	 * Y:通過N:不通過
	 */
	@Size(max=1)
	@Column(name="DATASEARCHRESULT", length=1, columnDefinition="CHAR(1)")
	private String dataSearchResult;

	/** 查詢時間 **/
	@Column(name="QUERYTIME", columnDefinition="TIMESTAMP")
	private Timestamp queryTime;
	
	/** DW資料日期 **/
	@Column(name="DWQUERYDATE", columnDefinition="DATE")
	private Date dwQueryDate;

	/** 備註 **/
	@Size(max=300)
	@Column(name="MEMO", length=300, columnDefinition="VARCHAR(300)")
	private String memo;

	/** 文件建立者 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(06)")
	private String creator;

	/** 文件建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 資料修改人(行編) **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(06)")
	private String updater;

	/** 資料修改日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得身分證統編 **/
	public String getCustId() {
		return this.custId;
	}
	/** 設定身分證統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得身分證統編重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}
	/** 設定身分證統編重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得ETF、外國債劵、連動債 **/
	public BigDecimal getIn_tr_sc_e() {
		return this.in_tr_sc_e;
	}
	/** 設定ETF、外國債劵、連動債 **/
	public void setIn_tr_sc_e(BigDecimal value) {
		this.in_tr_sc_e = value;
	}

	/** 取得基金 **/
	public BigDecimal getIn_tr_fu() {
		return this.in_tr_fu;
	}
	/** 設定基金 **/
	public void setIn_tr_fu(BigDecimal value) {
		this.in_tr_fu = value;
	}

	/** 取得理財AUM(A)+(B)+(C) **/
	public BigDecimal getIn_wm_aum() {
		return this.in_wm_aum;
	}
	/** 設定理財AUM(A)+(B)+(C) **/
	public void setIn_wm_aum(BigDecimal value) {
		this.in_wm_aum = value;
	}

	/** 取得(A)信託商品月底餘額 **/
	public BigDecimal getIn_wm_fd() {
		return this.in_wm_fd;
	}
	/** 設定(A)信託商品月底餘額 **/
	public void setIn_wm_fd(BigDecimal value) {
		this.in_wm_fd = value;
	}

	/** 取得(B)累積已繳保費/保單價值 **/
	public BigDecimal getIn_wm_ia() {
		return this.in_wm_ia;
	}
	/** 設定(B)累積已繳保費/保單價值 **/
	public void setIn_wm_ia(BigDecimal value) {
		this.in_wm_ia = value;
	}

	/** 取得(C)優利投資月平均餘額 **/
	public BigDecimal getIn_wm_std() {
		return this.in_wm_std;
	}
	/** 設定(C)優利投資月平均餘額 **/
	public void setIn_wm_std(BigDecimal value) {
		this.in_wm_std = value;
	}

	/** 取得累積下單金額 **/
	public BigDecimal getIn_wm_bal() {
		return this.in_wm_bal;
	}
	/** 設定累積下單金額 **/
	public void setIn_wm_bal(BigDecimal value) {
		this.in_wm_bal = value;
	}

	/** 取得累計手收金額 **/
	public BigDecimal getIn_wm_fee() {
		return this.in_wm_fee;
	}
	/** 設定累計手收金額 **/
	public void setIn_wm_fee(BigDecimal value) {
		this.in_wm_fee = value;
	}

	/** 
	 * 取得查詢結果<p/>
	 * Y:通過N:不通過
	 */
	public String getDataSearchResult() {
		return this.dataSearchResult;
	}
	/**
	 *  設定查詢結果<p/>
	 *  Y:通過N:不通過
	 **/
	public void setDataSearchResult(String value) {
		this.dataSearchResult = value;
	}

	/** 取得查詢時間 **/
	public Timestamp getQueryTime() {
		return this.queryTime;
	}
	/** 設定查詢時間 **/
	public void setQueryTime(Timestamp value) {
		this.queryTime = value;
	}

	/** 取得備註 **/
	public String getMemo() {
		return this.memo;
	}
	/** 設定備註 **/
	public void setMemo(String value) {
		this.memo = value;
	}

	/** 取得文件建立者 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定文件建立者 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得文件建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定文件建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得資料修改人(行編) **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定資料修改人(行編) **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得資料修改日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定資料修改日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
	/** 設定DW資料日期 **/
	public void setDwQueryDate(Date dwQueryDate) {
		this.dwQueryDate = dwQueryDate;
	}
	/**  取得DW資料日期 **/
	public Date getDwQueryDate() {
		return dwQueryDate;
	}

}
