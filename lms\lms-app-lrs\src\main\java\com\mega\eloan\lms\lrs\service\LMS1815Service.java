/* 
 * LMS1815Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lrs.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.ICapService;

import com.mega.eloan.lms.model.L181M01A;
import com.mega.eloan.lms.model.L181M01B;

public interface LMS1815Service extends ICapService {

	// L180M01a 覆審名單主檔

	/**
	 * 多筆刪除L180M01A
	 * 
	 * @param oids
	 */
	void deleteL180m02a(String oid);

	L181M01A fingL180m02aByBranch(String branch, String CustId, String dupno,
			String ctlType);

	// L180M01B 覆審名單明細檔

	/**
	 * 刪除多筆L180M01B
	 * 
	 * @param mainId
	 */
	void deleteL180m02bList(String mainId);

	/**
	 * 利用mainId搜尋L180M01B
	 * 
	 * @param MainId
	 * @return
	 */
	List<L181M01B> findL180m02bByMainId(String MainId);

	/**
	 * 利用Oid做搜尋
	 * 
	 * @param <T>
	 * @param clazz
	 * @param oid
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	<T extends GenericBean> T findModelByOid(Class clazz, String oid);

	/**
	 * 搜尋
	 * 
	 * @param clazz
	 * @param search
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	Page<? extends GenericBean> findPage(Class clazz, ISearch search);

	/**
	 * 單筆儲存
	 * 
	 * @param entity
	 */
	void save(GenericBean... entity);

	/**
	 * 單筆刪除
	 * 
	 * @param clazz
	 * @param oid
	 */
	@SuppressWarnings("rawtypes")
	void delete(Class clazz, String oid);

	// // UploadFileInfo
	// List<UploadFileInfo> getUploadFileInfoList(ISearch search);
	//
	// int getUploadFileInfoCount(ISearch search);

	/**
	 * 起案所使用的flow
	 * 
	 * @param mainOid
	 */
	void startFlow(String mainOid);

	/**
	 * 其它到結案所用的flow
	 * 
	 * @param mainOid
	 * @param model
	 * @param setResult
	 * @param resultType
	 * @throws Throwable
	 */
	public void flowAction(String mainOid, GenericBean model,
			boolean setResult, boolean resultType) throws Throwable;

	public void saveNew(L181M01A l180m02a, List<L181M01B> l181m01bList);

	/**
	 * 計算下次覆審日
	 * 
	 * @param oid
	 * @return
	 */
	Date cauNextDate(String oid);

	/**
	 * 整批覆核
	 * 
	 * @param oids
	 * @return
	 */
	Map<String, Object> flowCases(String[] oids, boolean flowAction);

	/**
	 * 查詢戶況
	 * 
	 * @param custId
	 * @param dupNo
	 * @param branch
	 * @return
	 */
	String findCstate(L181M01A l181m01a);

	/**
	 * 計算下次覆審日
	 * 
	 * @param map
	 * @return
	 */
	Date cauNextDate(Map<String, Object> map);

}
