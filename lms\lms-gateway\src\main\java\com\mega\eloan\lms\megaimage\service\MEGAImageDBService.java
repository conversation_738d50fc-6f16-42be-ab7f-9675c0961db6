/* 
 * MEGAImageService.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.megaimage.service;

import tw.com.iisi.cap.exception.CapMessageException;

/**
 * <pre>
 * MEGAImageService文件數位化
 * </pre>
 * 
 * @since 2022/12/07
 * <AUTHOR>
 * @version <ul>
 *          <li>2022/12/07,009763,new
 *          </ul>
 */
@Deprecated
public interface MEGAImageDBService {

	/**
	 * 改派分行更新分行代碼
	 */
	public void changeBranchCodeByCaseNo(String caseNo,String branchCd)
			throws CapMessageException;

}
