/* 
 * L120S05D.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 借款人集團授信明細檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L120S05D", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId","custId","dupNo"}))
public class L120S05D extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 轄下公司統一編號 **/
	@Column(name="CUSTID", length=10, columnDefinition="CHAR(10)")
	private String custId;

	/** 重複序號 **/
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 公司名稱（戶名） **/
	@Column(name="CUSTNAME", length=120, columnDefinition="VARCHAR(120)")
	private String custName;

	/** 授信資料日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="LNDATE", columnDefinition="DATE")
	private Date lnDate;

	/** 
	 * 授信總額度<p/>
	 * 單位：TWD元<br/>
	 *  (totAmtA+totAmtB)
	 */
	@Column(name="TOTAMT", columnDefinition="DECIMAL(13,0)")
	private Long totAmt;

	/** 
	 * 授信總額度(國內)<p/>
	 * 100/09/28調整<br/>
	 *  單位：TWD元
	 */
	@Column(name="TOTAMTA", columnDefinition="DECIMAL(13,0)")
	private Long totAmtA;

	/** 
	 * 授信總額度(海外)<p/>
	 * 100/09/28調整<br/>
	 *  單位：TWD元
	 */
	@Column(name="TOTAMTB", columnDefinition="DECIMAL(13,0)")
	private Long totAmtB;

	/** 
	 * 無擔保授信總額度<p/>
	 * 單位：TWD元<br/>
	 *  (crdAmtA+crdAmtB)
	 */
	@Column(name="CRDAMT", columnDefinition="DECIMAL(13,0)")
	private Long crdAmt;

	/** 
	 * 無擔保授信總額度(國內)<p/>
	 * 100/09/28調整<br/>
	 *  單位：TWD元
	 */
	@Column(name="CRDAMTA", columnDefinition="DECIMAL(13,0)")
	private Long crdAmtA;

	/** 
	 * 無擔保授信總額度(海外)<p/>
	 * 100/09/28調整<br/>
	 *  單位：TWD元
	 */
	@Column(name="CRDAMTB", columnDefinition="DECIMAL(13,0)")
	private Long crdAmtB;

	/** 
	 * 扣除一～四項授信總額度<p/>
	 * 單位：TWD元
	 */
	@Column(name="EXCAMT", columnDefinition="DECIMAL(13,0)")
	private Long excAmt;

	/** 
	 * 扣除一～四項無擔保授信總額度<p/>
	 * 單位：TWD元
	 */
	@Column(name="EXCRDAMT", columnDefinition="DECIMAL(13,0)")
	private Long excrdAmt;

	/** 資料查詢日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="GPQDATE", columnDefinition="DATE")
	private Date gpQDate;

	/** 金融商品資料日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="GPCOMDATE", columnDefinition="DATE")
	private Date gpComDate;

	/** 授信與長投資料日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="GPRISKDATE", columnDefinition="DATE")
	private Date gpRiskDate;

	/** 
	 * 金融商品<p/>
	 * 單位：TWD元
	 */
	@Column(name="GPCOMAMT", columnDefinition="DECIMAL(13,0)")
	private Long gpComAmt;

	/** 
	 * 曝險總額<p/>
	 * 單位：TWD元
	 */
	@Column(name="GPRISKAMT", columnDefinition="DECIMAL(13,0)")
	private Long gpRiskAmt;

	/** 建立人員號碼 **/
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Date updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得轄下公司統一編號 **/
	public String getCustId() {
		return this.custId;
	}
	/** 設定轄下公司統一編號 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得重複序號 **/
	public String getDupNo() {
		return this.dupNo;
	}
	/** 設定重複序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得公司名稱（戶名） **/
	public String getCustName() {
		return this.custName;
	}
	/** 設定公司名稱（戶名） **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/** 取得授信資料日期 **/
	public Date getLnDate() {
		return this.lnDate;
	}
	/** 設定授信資料日期 **/
	public void setLnDate(Date value) {
		this.lnDate = value;
	}

	/** 
	 * 取得授信總額度<p/>
	 * 單位：TWD元<br/>
	 *  (totAmtA+totAmtB)
	 */
	public Long getTotAmt() {
		return this.totAmt;
	}
	/**
	 *  設定授信總額度<p/>
	 *  單位：TWD元<br/>
	 *  (totAmtA+totAmtB)
	 **/
	public void setTotAmt(Long value) {
		this.totAmt = value;
	}

	/** 
	 * 取得授信總額度(國內)<p/>
	 * 100/09/28調整<br/>
	 *  單位：TWD元
	 */
	public Long getTotAmtA() {
		return this.totAmtA;
	}
	/**
	 *  設定授信總額度(國內)<p/>
	 *  100/09/28調整<br/>
	 *  單位：TWD元
	 **/
	public void setTotAmtA(Long value) {
		this.totAmtA = value;
	}

	/** 
	 * 取得授信總額度(海外)<p/>
	 * 100/09/28調整<br/>
	 *  單位：TWD元
	 */
	public Long getTotAmtB() {
		return this.totAmtB;
	}
	/**
	 *  設定授信總額度(海外)<p/>
	 *  100/09/28調整<br/>
	 *  單位：TWD元
	 **/
	public void setTotAmtB(Long value) {
		this.totAmtB = value;
	}

	/** 
	 * 取得無擔保授信總額度<p/>
	 * 單位：TWD元<br/>
	 *  (crdAmtA+crdAmtB)
	 */
	public Long getCrdAmt() {
		return this.crdAmt;
	}
	/**
	 *  設定無擔保授信總額度<p/>
	 *  單位：TWD元<br/>
	 *  (crdAmtA+crdAmtB)
	 **/
	public void setCrdAmt(Long value) {
		this.crdAmt = value;
	}

	/** 
	 * 取得無擔保授信總額度(國內)<p/>
	 * 100/09/28調整<br/>
	 *  單位：TWD元
	 */
	public Long getCrdAmtA() {
		return this.crdAmtA;
	}
	/**
	 *  設定無擔保授信總額度(國內)<p/>
	 *  100/09/28調整<br/>
	 *  單位：TWD元
	 **/
	public void setCrdAmtA(Long value) {
		this.crdAmtA = value;
	}

	/** 
	 * 取得無擔保授信總額度(海外)<p/>
	 * 100/09/28調整<br/>
	 *  單位：TWD元
	 */
	public Long getCrdAmtB() {
		return this.crdAmtB;
	}
	/**
	 *  設定無擔保授信總額度(海外)<p/>
	 *  100/09/28調整<br/>
	 *  單位：TWD元
	 **/
	public void setCrdAmtB(Long value) {
		this.crdAmtB = value;
	}

	/** 
	 * 取得扣除一～四項授信總額度<p/>
	 * 單位：TWD元
	 */
	public Long getExcAmt() {
		return this.excAmt;
	}
	/**
	 *  設定扣除一～四項授信總額度<p/>
	 *  單位：TWD元
	 **/
	public void setExcAmt(Long value) {
		this.excAmt = value;
	}

	/** 
	 * 取得扣除一～四項無擔保授信總額度<p/>
	 * 單位：TWD元
	 */
	public Long getExcrdAmt() {
		return this.excrdAmt;
	}
	/**
	 *  設定扣除一～四項無擔保授信總額度<p/>
	 *  單位：TWD元
	 **/
	public void setExcrdAmt(Long value) {
		this.excrdAmt = value;
	}

	/** 取得資料查詢日期 **/
	public Date getGpQDate() {
		return this.gpQDate;
	}
	/** 設定資料查詢日期 **/
	public void setGpQDate(Date value) {
		this.gpQDate = value;
	}

	/** 取得金融商品資料日期 **/
	public Date getGpComDate() {
		return this.gpComDate;
	}
	/** 設定金融商品資料日期 **/
	public void setGpComDate(Date value) {
		this.gpComDate = value;
	}

	/** 取得授信與長投資料日期 **/
	public Date getGpRiskDate() {
		return this.gpRiskDate;
	}
	/** 設定授信與長投資料日期 **/
	public void setGpRiskDate(Date value) {
		this.gpRiskDate = value;
	}

	/** 
	 * 取得金融商品<p/>
	 * 單位：TWD元
	 */
	public Long getGpComAmt() {
		return this.gpComAmt;
	}
	/**
	 *  設定金融商品<p/>
	 *  單位：TWD元
	 **/
	public void setGpComAmt(Long value) {
		this.gpComAmt = value;
	}

	/** 
	 * 取得曝險總額<p/>
	 * 單位：TWD元
	 */
	public Long getGpRiskAmt() {
		return this.gpRiskAmt;
	}
	/**
	 *  設定曝險總額<p/>
	 *  單位：TWD元
	 **/
	public void setGpRiskAmt(Long value) {
		this.gpRiskAmt = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}
}
