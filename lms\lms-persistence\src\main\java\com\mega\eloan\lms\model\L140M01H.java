/* 
 * L140M01H.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;

/** 額度費率明細檔 **/
@Entity
@Table(name = "L140M01H", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "rateSeq" }))
public class L140M01H extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 序號
	 * <p/>
	 * L140M01F_rateSeq
	 */
	@Column(name = "RATESEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer rateSeq;

	/**
	 * 商業本票保證_費率種類
	 * <p/>
	 * 詳【註1】
	 */
	@Column(name = "CPTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String cpType;

	/**
	 * 年費率1
	 * <p/>
	 * 101/04/28調整<br/>
	 * DECIMAL(5,2) ( DECIMAL(7,4)
	 */
	@Column(name = "CP1RATE", columnDefinition = "DECIMAL(7,4)")
	private BigDecimal cp1Rate;

	/**
	 * 最低收費(幣別)
	 * <p/>
	 * 預設：TWD<br/>
	 * ※本欄未上傳
	 */
	@Column(name = "CP1CURR", length = 3, columnDefinition = "CHAR(3)")
	private String cp1Curr;

	/** 最低收費(金額) **/
	@Column(name = "CP1FEE", columnDefinition = "DECIMAL(5,0)")
	private Integer cp1Fee;

	/**
	 * 年費率2
	 * <p/>
	 * 101/04/28調整<br/>
	 * DECIMAL(5,2) ( DECIMAL(7,4)
	 */
	@Column(name = "CP2RATE1", columnDefinition = "DECIMAL(7,4)")
	private BigDecimal cp2Rate1;

	/**
	 * 若由本行承銷則年費率
	 * <p/>
	 * 101/04/28調整<br/>
	 * DECIMAL(5,2) ( DECIMAL(7,4)
	 */
	@Column(name = "CP2RATE2", columnDefinition = "DECIMAL(7,4)")
	private BigDecimal cp2Rate2;

	/**
	 * 商業本票文字敘述
	 * <p/>
	 * 組合文字或其他自行輸入<br/>
	 * (100/08/29調整)配合上傳主機(MIS與AS400)調整欄位長度內容均需轉換為全型字並額外保留0E0F長度。<br/>
	 * 如：0E+CHAR(80)+0F<br/>
	 * 80/2*3+2=122 <br/>
	 * 102.02.18 欄位擴大 122 -> 1050
	 */
	@Column(name = "CPDES", length = 1050, columnDefinition = "VARCHAR(1050)")
	private String cpDes;

	/**
	 * 開發保證函_費率種類
	 * <p/>
	 * 詳【註1】
	 */
	@Column(name = "CFTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String cfType;

	/**
	 * 年費率1
	 * <p/>
	 * 101/04/28調整<br/>
	 * DECIMAL(5,2) ( DECIMAL(7,4)
	 */
	@Column(name = "CF1RATE", columnDefinition = "DECIMAL(7,4)")
	private BigDecimal cf1Rate;

	/** 每X個月為一期按期計收 **/
	@Column(name = "CF1MON1", columnDefinition = "DECIMAL(2,0)")
	private Integer cf1Mon1;

	/**
	 * 計收方法
	 * <p/>
	 * 1. 於開發日一次收足<br/>
	 * 2. 採按季預收<br/>
	 * 3. 採按每半年預收<br/>
	 * 4. 採按逐年預收<br/>
	 * 5. 採按X月預收
	 */
	@Column(name = "CF1MD", length = 1, columnDefinition = "CHAR(1)")
	private String cf1MD;

	/** 採按X月預收 **/
	@Column(name = "CF1MON2", columnDefinition = "DECIMAL(2,0)")
	private Integer cf1Mon2;

	/**
	 * 年費率2
	 * <p/>
	 * 101/04/28調整<br/>
	 * DECIMAL(5,2) ( DECIMAL(7,4)
	 */
	@Column(name = "CF2RATE", columnDefinition = "DECIMAL(7,4)")
	private BigDecimal cf2Rate;

	/**
	 * 計收方法
	 * <p/>
	 * 1. 於開發日一次收足<br/>
	 * 2. 採按季預收<br/>
	 * 3. 採按每半年預收<br/>
	 * 4. 採按逐年預收<br/>
	 * 5. 採按X月預收
	 */
	@Column(name = "CF2MD", length = 1, columnDefinition = "CHAR(1)")
	private String cf2MD;

	/** 採按X月預收 **/
	@Column(name = "CF2MON", columnDefinition = "DECIMAL(2,0)")
	private Integer cf2Mon;

	/**
	 * 開發保證函文字敘述
	 * <p/>
	 * 組合文字或其他自行輸入<br/>
	 * (100/08/29調整)配合上傳主機(MIS與AS400)調整欄位長度內容均需轉換為全型字並額外保留0E0F長度。<br/>
	 * 如：0E+CHAR(80)+0F<br/>
	 * 80/2*3+2=122 <br/>
	 * 102.02.18 欄位擴大 122 -> 1050
	 */
	@Column(name = "CFDES", length = 1050, columnDefinition = "VARCHAR(1050)")
	private String cfDes;

	/**
	 * 公司債保證_費率種類
	 * <p/>
	 * 詳【註1】
	 */
	@Column(name = "CPYTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String cpyType;

	/**
	 * 年費率1
	 * <p/>
	 * 101/04/28調整<br/>
	 * DECIMAL(5,2) ( DECIMAL(7,4)
	 */
	@Column(name = "CPY1RATE", columnDefinition = "DECIMAL(7,4)")
	private BigDecimal cpy1Rate;

	/** 每X個月為一期按期計收 **/
	@Column(name = "CPY1MON1", columnDefinition = "DECIMAL(2,0)")
	private Integer cpy1Mon1;

	/**
	 * 計收方法
	 * <p/>
	 * 1. 於開發日一次收足<br/>
	 * 2. 採按季預收<br/>
	 * 3. 採按每半年預收<br/>
	 * 4. 採按逐年預收<br/>
	 * 5. 採按X月預收
	 */
	@Column(name = "CPY1MD", length = 1, columnDefinition = "CHAR(1)")
	private String cpy1MD;

	/** 採按X月預收 **/
	@Column(name = "CPY1MON2", columnDefinition = "DECIMAL(2,0)")
	private Integer cpy1Mon2;

	/**
	 * 年費率2
	 * <p/>
	 * 101/04/28調整<br/>
	 * DECIMAL(5,2) ( DECIMAL(7,4)
	 */
	@Column(name = "CPY2RATE", columnDefinition = "DECIMAL(7,4)")
	private BigDecimal cpy2Rate;

	/**
	 * 計收方法
	 * <p/>
	 * 1. 於開發日一次收足<br/>
	 * 2. 採按季預收<br/>
	 * 3. 採按每半年預收<br/>
	 * 4. 採按逐年預收<br/>
	 * 5. 採按X月預收
	 */
	@Column(name = "CPY2MD", length = 1, columnDefinition = "CHAR(1)")
	private String cpy2MD;

	/** 採按X月預收 **/
	@Column(name = "CPY2MON", columnDefinition = "DECIMAL(2,0)")
	private Integer cpy2Mon;

	/**
	 * 公司債保證文字敘述
	 * <p/>
	 * 組合文字或其他自行輸入<br/>
	 * (100/08/29調整)配合上傳主機(MIS與AS400)調整欄位長度內容均需轉換為全型字並額外保留0E0F長度。<br/>
	 * 如：0E+CHAR(80)+0F<br/>
	 * 80/2*3+2=122 <br/>
	 * 102.02.18 欄位擴大 122 -> 1050
	 */
	@Column(name = "CPYDES", length = 1050, columnDefinition = "VARCHAR(1050)")
	private String cpyDes;

	/**
	 * 承兌費率_費率種類
	 * <p/>
	 * 詳【註1】
	 */
	@Column(name = "PATYPE", length = 1, columnDefinition = "CHAR(1)")
	private String paType;

	/**
	 * 年費率1
	 * <p/>
	 * 101/04/28調整<br/>
	 * DECIMAL(5,2) ( DECIMAL(7,4)
	 */
	@Column(name = "PA1RATE", columnDefinition = "DECIMAL(7,4)")
	private BigDecimal pa1Rate;

	/**
	 * 計收方法
	 * <p/>
	 * 1. 於開發日一次收足<br/>
	 * 2. 採按季預收<br/>
	 * 3. 採按每半年預收<br/>
	 * 4. 採按逐年預收<br/>
	 * 5. 採按X月預收
	 */
	@Column(name = "PA1MD", length = 1, columnDefinition = "CHAR(1)")
	private String pa1MD;

	/** 採按X月預收 **/
	@Column(name = "PA1MON", columnDefinition = "DECIMAL(2,0)")
	private Integer pa1Mon;

	/**
	 * 年費率2
	 * <p/>
	 * 101/04/28調整<br/>
	 * DECIMAL(5,2) ( DECIMAL(7,4)
	 */
	@Column(name = "PA2RATE", columnDefinition = "DECIMAL(7,4)")
	private BigDecimal pa2Rate;

	/**
	 * 計收方法
	 * <p/>
	 * 1. 於開發日一次收足<br/>
	 * 2. 採按季預收<br/>
	 * 3. 採按每半年預收<br/>
	 * 4. 採按逐年預收<br/>
	 * 5. 採按X月預收
	 */
	@Column(name = "PA2MD", length = 1, columnDefinition = "CHAR(1)")
	private String pa2MD;

	/** 採按X月預收 **/
	@Column(name = "PA2MON", columnDefinition = "DECIMAL(2,0)")
	private Integer pa2Mon;

	/**
	 * 承兌費率文字敘述
	 * <p/>
	 * 組合文字或其他自行輸入<br/>
	 * (100/08/29調整)配合上傳主機(MIS與AS400)調整欄位長度內容均需轉換為全型字並額外保留0E0F長度。<br/>
	 * 如：0E+CHAR(80)+0F<br/>
	 * 80/2*3+2=122 <br/>
	 * 102.02.18 欄位擴大 122 -> 1050
	 */
	@Column(name = "PADES", length = 1050, columnDefinition = "VARCHAR(1050)")
	private String paDes;

	/**
	 * 其他
	 * <p/>
	 * ※本欄未上傳 <br/>
	 * 102.02.18 欄位擴大 122 -> 2010
	 */
	@Column(name = "OTHDES", length = 2010, columnDefinition = "VARCHAR(2010)")
	private String othDes;

	/**
	 * 組成說明字串
	 * <p/>
	 * 費率敘述<br/>
	 * 256個全型字 <br/>
	 * 102.02.18 欄位擴大 768 -> 4096
	 */
	@Column(name = "RATEDSCR", length = 4096, columnDefinition = "VARCHAR(4096)")
	private String rateDscr;

	/** 建立人員號碼 **/
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Date updateTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得序號
	 * <p/>
	 * L140M01F_rateSeq
	 */
	public Integer getRateSeq() {
		return this.rateSeq;
	}

	/**
	 * 設定序號
	 * <p/>
	 * L140M01F_rateSeq
	 **/
	public void setRateSeq(Integer value) {
		this.rateSeq = value;
	}

	/**
	 * 取得商業本票保證_費率種類
	 * <p/>
	 * 詳【註1】
	 */
	public String getCpType() {
		return this.cpType;
	}

	/**
	 * 設定商業本票保證_費率種類
	 * <p/>
	 * 詳【註1】
	 **/
	public void setCpType(String value) {
		this.cpType = value;
	}

	/**
	 * 取得年費率1
	 * <p/>
	 * 101/04/28調整<br/>
	 * DECIMAL(5,2) ( DECIMAL(7,4)
	 */
	public BigDecimal getCp1Rate() {
		return this.cp1Rate;
	}

	/**
	 * 設定年費率1
	 * <p/>
	 * 101/04/28調整<br/>
	 * DECIMAL(5,2) ( DECIMAL(7,4)
	 **/
	public void setCp1Rate(BigDecimal value) {
		this.cp1Rate = value;
	}

	/**
	 * 取得最低收費(幣別)
	 * <p/>
	 * 預設：TWD<br/>
	 * ※本欄未上傳
	 */
	public String getCp1Curr() {
		return this.cp1Curr;
	}

	/**
	 * 設定最低收費(幣別)
	 * <p/>
	 * 預設：TWD<br/>
	 * ※本欄未上傳
	 **/
	public void setCp1Curr(String value) {
		this.cp1Curr = value;
	}

	/** 取得最低收費(金額) **/
	public Integer getCp1Fee() {
		return this.cp1Fee;
	}

	/** 設定最低收費(金額) **/
	public void setCp1Fee(Integer value) {
		this.cp1Fee = value;
	}

	/**
	 * 取得年費率2
	 * <p/>
	 * 101/04/28調整<br/>
	 * DECIMAL(5,2) ( DECIMAL(7,4)
	 */
	public BigDecimal getCp2Rate1() {
		return this.cp2Rate1;
	}

	/**
	 * 設定年費率2
	 * <p/>
	 * 101/04/28調整<br/>
	 * DECIMAL(5,2) ( DECIMAL(7,4)
	 **/
	public void setCp2Rate1(BigDecimal value) {
		this.cp2Rate1 = value;
	}

	/**
	 * 取得若由本行承銷則年費率
	 * <p/>
	 * 101/04/28調整<br/>
	 * DECIMAL(5,2) ( DECIMAL(7,4)
	 */
	public BigDecimal getCp2Rate2() {
		return this.cp2Rate2;
	}

	/**
	 * 設定若由本行承銷則年費率
	 * <p/>
	 * 101/04/28調整<br/>
	 * DECIMAL(5,2) ( DECIMAL(7,4)
	 **/
	public void setCp2Rate2(BigDecimal value) {
		this.cp2Rate2 = value;
	}

	/**
	 * 取得商業本票文字敘述
	 * <p/>
	 * 組合文字或其他自行輸入<br/>
	 * (100/08/29調整)配合上傳主機(MIS與AS400)調整欄位長度內容均需轉換為全型字並額外保留0E0F長度。<br/>
	 * 如：0E+CHAR(80)+0F<br/>
	 * 80/2*3+2=122
	 */
	public String getCpDes() {
		String temp = this.cpDes;
		if (temp != null) {
			temp = temp.replaceAll("<br/>", "\r");
		}else{
			temp = "";
		}
		return temp;
	}

	/**
	 * 設定商業本票文字敘述
	 * <p/>
	 * 組合文字或其他自行輸入<br/>
	 * (100/08/29調整)配合上傳主機(MIS與AS400)調整欄位長度內容均需轉換為全型字並額外保留0E0F長度。<br/>
	 * 如：0E+CHAR(80)+0F<br/>
	 * 80/2*3+2=122
	 **/
	public void setCpDes(String value) {
		this.cpDes = value;
	}

	/**
	 * 取得開發保證函_費率種類
	 * <p/>
	 * 詳【註1】
	 */
	public String getCfType() {
		String temp = this.cfType;
		if (temp != null) {
			temp = temp.replaceAll("<br/>", "\r");
		}else{
			temp = "";
		}
		return temp;
	}

	/**
	 * 設定開發保證函_費率種類
	 * <p/>
	 * 詳【註1】
	 **/
	public void setCfType(String value) {
		this.cfType = value;
	}

	/**
	 * 取得年費率1
	 * <p/>
	 * 101/04/28調整<br/>
	 * DECIMAL(5,2) ( DECIMAL(7,4)
	 */
	public BigDecimal getCf1Rate() {
		return this.cf1Rate;

	}

	/**
	 * 設定年費率1
	 * <p/>
	 * 101/04/28調整<br/>
	 * DECIMAL(5,2) ( DECIMAL(7,4)
	 **/
	public void setCf1Rate(BigDecimal value) {
		this.cf1Rate = value;
	}

	/** 取得每X個月為一期按期計收 **/
	public Integer getCf1Mon1() {
		return this.cf1Mon1;
	}

	/** 設定每X個月為一期按期計收 **/
	public void setCf1Mon1(Integer value) {
		this.cf1Mon1 = value;
	}

	/**
	 * 取得計收方法
	 * <p/>
	 * 1. 於開發日一次收足<br/>
	 * 2. 採按季預收<br/>
	 * 3. 採按每半年預收<br/>
	 * 4. 採按逐年預收<br/>
	 * 5. 採按X月預收
	 */
	public String getCf1MD() {
		return this.cf1MD;
	}

	/**
	 * 設定計收方法
	 * <p/>
	 * 1. 於開發日一次收足<br/>
	 * 2. 採按季預收<br/>
	 * 3. 採按每半年預收<br/>
	 * 4. 採按逐年預收<br/>
	 * 5. 採按X月預收
	 **/
	public void setCf1MD(String value) {
		this.cf1MD = value;
	}

	/** 取得採按X月預收 **/
	public Integer getCf1Mon2() {
		return this.cf1Mon2;
	}

	/** 設定採按X月預收 **/
	public void setCf1Mon2(Integer value) {
		this.cf1Mon2 = value;
	}

	/**
	 * 取得年費率2
	 * <p/>
	 * 101/04/28調整<br/>
	 * DECIMAL(5,2) ( DECIMAL(7,4)
	 */
	public BigDecimal getCf2Rate() {
		return this.cf2Rate;
	}

	/**
	 * 設定年費率2
	 * <p/>
	 * 101/04/28調整<br/>
	 * DECIMAL(5,2) ( DECIMAL(7,4)
	 **/
	public void setCf2Rate(BigDecimal value) {
		this.cf2Rate = value;
	}

	/**
	 * 取得計收方法
	 * <p/>
	 * 1. 於開發日一次收足<br/>
	 * 2. 採按季預收<br/>
	 * 3. 採按每半年預收<br/>
	 * 4. 採按逐年預收<br/>
	 * 5. 採按X月預收
	 */
	public String getCf2MD() {
		return this.cf2MD;
	}

	/**
	 * 設定計收方法
	 * <p/>
	 * 1. 於開發日一次收足<br/>
	 * 2. 採按季預收<br/>
	 * 3. 採按每半年預收<br/>
	 * 4. 採按逐年預收<br/>
	 * 5. 採按X月預收
	 **/
	public void setCf2MD(String value) {
		this.cf2MD = value;
	}

	/** 取得採按X月預收 **/
	public Integer getCf2Mon() {
		return this.cf2Mon;
	}

	/** 設定採按X月預收 **/
	public void setCf2Mon(Integer value) {
		this.cf2Mon = value;
	}

	/**
	 * 取得開發保證函文字敘述
	 * <p/>
	 * 組合文字或其他自行輸入<br/>
	 * (100/08/29調整)配合上傳主機(MIS與AS400)調整欄位長度內容均需轉換為全型字並額外保留0E0F長度。<br/>
	 * 如：0E+CHAR(80)+0F<br/>
	 * 80/2*3+2=122
	 */
	public String getCfDes() {
		String temp =this.cfDes;
		if(temp != null){
			temp = temp.replaceAll("<br/>", "\r");
		}else{
			temp = "";
		}
		return temp;
	}

	/**
	 * 設定開發保證函文字敘述
	 * <p/>
	 * 組合文字或其他自行輸入<br/>
	 * (100/08/29調整)配合上傳主機(MIS與AS400)調整欄位長度內容均需轉換為全型字並額外保留0E0F長度。<br/>
	 * 如：0E+CHAR(80)+0F<br/>
	 * 80/2*3+2=122
	 **/
	public void setCfDes(String value) {
		this.cfDes = value;
	}

	/**
	 * 取得公司債保證_費率種類
	 * <p/>
	 * 詳【註1】
	 */
	public String getCpyType() {
		return this.cpyType;
	}

	/**
	 * 設定公司債保證_費率種類
	 * <p/>
	 * 詳【註1】
	 **/
	public void setCpyType(String value) {
		this.cpyType = value;
	}

	/**
	 * 取得年費率1
	 * <p/>
	 * 101/04/28調整<br/>
	 * DECIMAL(5,2) ( DECIMAL(7,4)
	 */
	public BigDecimal getCpy1Rate() {
		return this.cpy1Rate;
	}

	/**
	 * 設定年費率1
	 * <p/>
	 * 101/04/28調整<br/>
	 * DECIMAL(5,2) ( DECIMAL(7,4)
	 **/
	public void setCpy1Rate(BigDecimal value) {
		this.cpy1Rate = value;
	}

	/** 取得每X個月為一期按期計收 **/
	public Integer getCpy1Mon1() {
		return this.cpy1Mon1;
	}

	/** 設定每X個月為一期按期計收 **/
	public void setCpy1Mon1(Integer value) {
		this.cpy1Mon1 = value;
	}

	/**
	 * 取得計收方法
	 * <p/>
	 * 1. 於開發日一次收足<br/>
	 * 2. 採按季預收<br/>
	 * 3. 採按每半年預收<br/>
	 * 4. 採按逐年預收<br/>
	 * 5. 採按X月預收
	 */
	public String getCpy1MD() {
		return this.cpy1MD;
	}

	/**
	 * 設定計收方法
	 * <p/>
	 * 1. 於開發日一次收足<br/>
	 * 2. 採按季預收<br/>
	 * 3. 採按每半年預收<br/>
	 * 4. 採按逐年預收<br/>
	 * 5. 採按X月預收
	 **/
	public void setCpy1MD(String value) {
		this.cpy1MD = value;
	}

	/** 取得採按X月預收 **/
	public Integer getCpy1Mon2() {
		return this.cpy1Mon2;
	}

	/** 設定採按X月預收 **/
	public void setCpy1Mon2(Integer value) {
		this.cpy1Mon2 = value;
	}

	/**
	 * 取得年費率2
	 * <p/>
	 * 101/04/28調整<br/>
	 * DECIMAL(5,2) ( DECIMAL(7,4)
	 */
	public BigDecimal getCpy2Rate() {
		return this.cpy2Rate;
	}

	/**
	 * 設定年費率2
	 * <p/>
	 * 101/04/28調整<br/>
	 * DECIMAL(5,2) ( DECIMAL(7,4)
	 **/
	public void setCpy2Rate(BigDecimal value) {
		this.cpy2Rate = value;
	}

	/**
	 * 取得計收方法
	 * <p/>
	 * 1. 於開發日一次收足<br/>
	 * 2. 採按季預收<br/>
	 * 3. 採按每半年預收<br/>
	 * 4. 採按逐年預收<br/>
	 * 5. 採按X月預收
	 */
	public String getCpy2MD() {
		return this.cpy2MD;
	}

	/**
	 * 設定計收方法
	 * <p/>
	 * 1. 於開發日一次收足<br/>
	 * 2. 採按季預收<br/>
	 * 3. 採按每半年預收<br/>
	 * 4. 採按逐年預收<br/>
	 * 5. 採按X月預收
	 **/
	public void setCpy2MD(String value) {
		this.cpy2MD = value;
	}

	/** 取得採按X月預收 **/
	public Integer getCpy2Mon() {
		return this.cpy2Mon;
	}

	/** 設定採按X月預收 **/
	public void setCpy2Mon(Integer value) {
		this.cpy2Mon = value;
	}

	/**
	 * 取得公司債保證文字敘述
	 * <p/>
	 * 組合文字或其他自行輸入<br/>
	 * (100/08/29調整)配合上傳主機(MIS與AS400)調整欄位長度內容均需轉換為全型字並額外保留0E0F長度。<br/>
	 * 如：0E+CHAR(80)+0F<br/>
	 * 80/2*3+2=122
	 */
	public String getCpyDes() {
		String temp =this.cpyDes;
		if(temp != null){
			temp = temp.replaceAll("<br/>", "\r");
		}else{
			temp = "";
		}
		return temp;
	}

	/**
	 * 設定公司債保證文字敘述
	 * <p/>
	 * 組合文字或其他自行輸入<br/>
	 * (100/08/29調整)配合上傳主機(MIS與AS400)調整欄位長度內容均需轉換為全型字並額外保留0E0F長度。<br/>
	 * 如：0E+CHAR(80)+0F<br/>
	 * 80/2*3+2=122
	 **/
	public void setCpyDes(String value) {
		this.cpyDes = value;
	}

	/**
	 * 取得承兌費率_費率種類
	 * <p/>
	 * 詳【註1】
	 */
	public String getPaType() {
		return this.paType;
	}

	/**
	 * 設定承兌費率_費率種類
	 * <p/>
	 * 詳【註1】
	 **/
	public void setPaType(String value) {
		this.paType = value;
	}

	/**
	 * 取得年費率1
	 * <p/>
	 * 101/04/28調整<br/>
	 * DECIMAL(5,2) ( DECIMAL(7,4)
	 */
	public BigDecimal getPa1Rate() {
		return this.pa1Rate;
	}

	/**
	 * 設定年費率1
	 * <p/>
	 * 101/04/28調整<br/>
	 * DECIMAL(5,2) ( DECIMAL(7,4)
	 **/
	public void setPa1Rate(BigDecimal value) {
		this.pa1Rate = value;
	}

	/**
	 * 取得計收方法
	 * <p/>
	 * 1. 於開發日一次收足<br/>
	 * 2. 採按季預收<br/>
	 * 3. 採按每半年預收<br/>
	 * 4. 採按逐年預收<br/>
	 * 5. 採按X月預收
	 */
	public String getPa1MD() {
		return this.pa1MD;
	}

	/**
	 * 設定計收方法
	 * <p/>
	 * 1. 於開發日一次收足<br/>
	 * 2. 採按季預收<br/>
	 * 3. 採按每半年預收<br/>
	 * 4. 採按逐年預收<br/>
	 * 5. 採按X月預收
	 **/
	public void setPa1MD(String value) {
		this.pa1MD = value;
	}

	/** 取得採按X月預收 **/
	public Integer getPa1Mon() {
		return this.pa1Mon;
	}

	/** 設定採按X月預收 **/
	public void setPa1Mon(Integer value) {
		this.pa1Mon = value;
	}

	/**
	 * 取得年費率2
	 * <p/>
	 * 101/04/28調整<br/>
	 * DECIMAL(5,2) ( DECIMAL(7,4)
	 */
	public BigDecimal getPa2Rate() {
		return this.pa2Rate;
	}

	/**
	 * 設定年費率2
	 * <p/>
	 * 101/04/28調整<br/>
	 * DECIMAL(5,2) ( DECIMAL(7,4)
	 **/
	public void setPa2Rate(BigDecimal value) {
		this.pa2Rate = value;
	}

	/**
	 * 取得計收方法
	 * <p/>
	 * 1. 於開發日一次收足<br/>
	 * 2. 採按季預收<br/>
	 * 3. 採按每半年預收<br/>
	 * 4. 採按逐年預收<br/>
	 * 5. 採按X月預收
	 */
	public String getPa2MD() {
		return this.pa2MD;
	}

	/**
	 * 設定計收方法
	 * <p/>
	 * 1. 於開發日一次收足<br/>
	 * 2. 採按季預收<br/>
	 * 3. 採按每半年預收<br/>
	 * 4. 採按逐年預收<br/>
	 * 5. 採按X月預收
	 **/
	public void setPa2MD(String value) {
		this.pa2MD = value;
	}

	/** 取得採按X月預收 **/
	public Integer getPa2Mon() {
		return this.pa2Mon;
	}

	/** 設定採按X月預收 **/
	public void setPa2Mon(Integer value) {
		this.pa2Mon = value;
	}

	/**
	 * 取得承兌費率文字敘述
	 * <p/>
	 * 組合文字或其他自行輸入<br/>
	 * (100/08/29調整)配合上傳主機(MIS與AS400)調整欄位長度內容均需轉換為全型字並額外保留0E0F長度。<br/>
	 * 如：0E+CHAR(80)+0F<br/>
	 * 80/2*3+2=122
	 */
	public String getPaDes() {
		String temp =this.paDes;
		if(temp != null){
			temp = temp.replaceAll("<br/>", "\r");
		}else{
			temp = "";
		}
		return temp;
	}

	/**
	 * 設定承兌費率文字敘述
	 * <p/>
	 * 組合文字或其他自行輸入<br/>
	 * (100/08/29調整)配合上傳主機(MIS與AS400)調整欄位長度內容均需轉換為全型字並額外保留0E0F長度。<br/>
	 * 如：0E+CHAR(80)+0F<br/>
	 * 80/2*3+2=122
	 **/
	public void setPaDes(String value) {
		this.paDes = value;
	}

	/**
	 * 取得其他
	 * <p/>
	 * ※本欄未上傳
	 */
	public String getOthDes() {
		String temp =this.othDes;
		if(temp != null){
			temp = temp.replaceAll("<br/>", "\r");
		}else{
			temp = "";
		}
		return temp;
	}

	/**
	 * 設定其他
	 * <p/>
	 * ※本欄未上傳
	 **/
	public void setOthDes(String value) {
		this.othDes = value;
	}

	/**
	 * 取得組成說明字串
	 * <p/>
	 * 費率敘述<br/>
	 * 256個全型字
	 */
	public String getRateDscr() {
		String temp = this.rateDscr;
		if (temp != null) {
			temp = temp.replaceAll("<br/>", "\r");
		}else{
			temp = "";
		}
		
		return temp;
	}

	/**
	 * 設定組成說明字串
	 * <p/>
	 * 費率敘述<br/>
	 * 256個全型字
	 **/
	public void setRateDscr(String value) {
		this.rateDscr = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}
}
