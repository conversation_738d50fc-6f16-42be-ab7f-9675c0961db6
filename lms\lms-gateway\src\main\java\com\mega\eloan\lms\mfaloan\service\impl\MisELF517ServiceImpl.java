/* 
 *MisIquotappService.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.mfaloan.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.mfaloan.service.MisELF517Service;

/**
 * <pre>
 * 核准額度資料檔 IQUOTAPP(MIS.ELV38801)
 * </pre>
 * 
 * @since 2011/12/23
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/12/23,REX,new
 *          </ul>
 */
@Service
public class MisELF517ServiceImpl extends AbstractMFAloanJdbc implements
		MisELF517Service {

	@Override
	public Map<String, Object> findByCntrNo(String cntrNo) {
		return this.getJdbc().queryForMap("ELF517.selByCntrNo",
				new String[] { cntrNo });
	}
	
	
	@Override
	public void insert(String cntrNo, String remainLoanYN, String remainLoanClass,
			String remainLoanLocationCity, BigDecimal remainLoanSite2No, BigDecimal remainLoanSite3No, String remainLoanSite4No,
			BigDecimal firstLoanUnsoldHouseQuantity, BigDecimal currentUnsoldHouseQuantity,
			BigDecimal monthOfCreditPeriod, Date finalCreditPeriodEndDate, String documentNo) {
		this.getJdbc().update(
				"ELF517.insert",
				new Object[] { cntrNo, remainLoanYN, remainLoanClass, remainLoanLocationCity, remainLoanSite2No, remainLoanSite3No,
						remainLoanSite4No, firstLoanUnsoldHouseQuantity, currentUnsoldHouseQuantity, monthOfCreditPeriod, 
						finalCreditPeriodEndDate, documentNo });

	}

	@Override
	public void update(String cntrNo, String remainLoanYN, String remainLoanClass,
			String remainLoanLocationCity, BigDecimal remainLoanSite2No, BigDecimal remainLoanSite3No, String remainLoanSite4No,
			BigDecimal firstLoanUnsoldHouseQuantity, BigDecimal currentUnsoldHouseQuantity,
			BigDecimal monthOfCreditPeriod, Date finalCreditPeriodEndDate, String documentNo) {
		this.getJdbc().update(
				"ELF517.update",
				new Object[] { remainLoanYN, remainLoanClass, remainLoanLocationCity, remainLoanSite2No, remainLoanSite3No,
						remainLoanSite4No, firstLoanUnsoldHouseQuantity, currentUnsoldHouseQuantity, monthOfCreditPeriod, 
						finalCreditPeriodEndDate, documentNo, cntrNo });

	}

	/**
	 * J-110-0497 餘屋貸款貸後管理 - 新增
	 */
	@Override
	public void insertForFms(String cntrNo, String buildName, BigDecimal begForSell, BigDecimal soldNumber,
			 String proStatus, String behindDesc, String isSameCase) {
		this.getJdbc().update(
				"ELF517.insertForFms",
				new Object[] { cntrNo, buildName, begForSell, soldNumber,
						proStatus, behindDesc, isSameCase });

	}

	/**
	 * J-110-0497 餘屋貸款貸後管理 - 更新
	 */
	@Override
	public void updateForFms(String cntrNo, String buildName, BigDecimal begForSell, BigDecimal soldNumber,
			 String proStatus, String behindDesc, String isSameCase) {
		this.getJdbc().update(
				"ELF517.updateForFms",
				new Object[] { buildName, begForSell, soldNumber,
						proStatus, behindDesc, isSameCase,
						cntrNo });

	}
	
	@Override
	public Map<String, Object> findByCntrNoFullField(String cntrNo) {
		return this.getJdbc().queryForMap("ELF517.selAllByCntrNo",
				new String[] { cntrNo });
	}
	
	@Override
	public Map<String, Object> getByCntrNo(String cntrNo) {
		return this.getJdbc().queryForMap("ELF517.selAllByCntrNo", new String[] { cntrNo });
	}
}
