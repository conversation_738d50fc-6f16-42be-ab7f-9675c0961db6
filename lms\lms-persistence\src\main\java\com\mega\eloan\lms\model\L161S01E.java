/* 
 * L161S01E.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 動審表查詢名單明細檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L161S01E", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L161S01E extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * 文件產生方式<p/>
	 * 系統產生 | SYS<br/>
	 *  人工產生 | PEO
	 */
	@Size(max=3)
	@Column(name="CREATEBY", length=3, columnDefinition="CHAR(3)")
	private String createBY;

	/** 統一編號 **/
	@Size(max=10)
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 重覆序號 **/
	@Size(max=1)
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 中文戶名 **/
	@Size(max=120)
	@Column(name="CUSTNAME", length=120, columnDefinition="VARCHAR(120)")
	private String custName;

	/** 區部別 **/
	@Size(max=1)
	@Column(name="TYPCD", length=1, columnDefinition="CHAR(1)")
	private String typCd;

	/** 
	 * 與借款人關係<p/>
	 * (多選) 只能為數字<br/>
	 *  借戶 | 1<br/>
	 *  共同借款人 | 2<br/>
	 *  負責人 | 3<br/>
	 *  連保人 | 4<br/>
	 *  擔保品提供人 | 5<br/>
	 *  關係企業 | 6<br/>
	 *  實質受益人|7<br/>
	 *  一般保證人|8<br/>
	 *  應收帳款買方無追索|9<br/>
	 *  高階管理員|10<br/>
	 *  具控制權人|11<br/>
	 *  董監事|12
	 */
	@Size(max=20)
	@Column(name="CUSTRELATION", length=20, columnDefinition="VARCHAR(20)")
	private String custRelation;

	/** 
	 * 查詢類別<p/>
	 * (多選) 01公司登記事項卡(商工資料)<br/>
	 *  02稅籍登記資料公示查詢<br/>
	 *  03身份證遺失檢查<br/>
	 *  04受監護輔助宣告(家事公告)<br/>
	 *  05銀行法及金控法利害關係人查詢
	 */
	@Size(max=20)
	@Column(name="TYPE", length=20, columnDefinition="VARCHAR(20)")
	private String type;

	/** 
	 * 資料查詢日<p/>
	 * YYYY-MM-01
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="QUERYDATES", columnDefinition="DATE")
	private Date queryDateS;

	/** 輸入資料檢誤完成(Y/N) **/
	@Size(max=1)
	@Column(name="CHKYN", length=1, columnDefinition="CHAR(1)")
	private String chkYN;

	/** 備註(名單查詢結果) **/
	@Size(max=240)
	@Column(name="MEMO", length=240, columnDefinition="VARCHAR(240)")
	private String Memo;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 與借款人關係Grid 排序(非DB欄位) **/
	@Transient
	private String custRelationIndex;
	
	/** 
	 * 發證民國年<p/>
	 * J-109-0208
	 */
	@Size(max=3)
	@Column(name="IDDATEYEAR", length=3, columnDefinition="VARCHAR(3)")
	private String idDateYear;

	/** 
	 * 發證民國月<p/>
	 * J-109-0208
	 */
	@Size(max=2)
	@Column(name="IDDATEMONTH", length=2, columnDefinition="VARCHAR(2)")
	private String idDateMonth;

	/** 
	 * 發證民國日<p/>
	 * J-109-0208
	 */
	@Size(max=2)
	@Column(name="IDDATEDAY", length=2, columnDefinition="VARCHAR(2)")
	private String idDateDay;

	/** 
	 * 發證地點<p/>
	 * J-109-0208codetype=lms1601m01_IDSite
	 */
	@Size(max=5)
	@Column(name="IDSITE", length=5, columnDefinition="VARCHAR(5)")
	private String idSite;

	/** 
	 * 領補換別<p/>
	 * J-109-02081初發 2補發 3換發
	 */
	@Size(max=1)
	@Column(name="IDCHANGETYPE", length=1, columnDefinition="CHAR(1)")
	private String idChangeType;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得文件產生方式<p/>
	 * 系統產生 | SYS<br/>
	 *  人工產生 | PEO
	 */
	public String getCreateBY() {
		return this.createBY;
	}
	/**
	 *  設定文件產生方式<p/>
	 *  系統產生 | SYS<br/>
	 *  人工產生 | PEO
	 **/
	public void setCreateBY(String value) {
		this.createBY = value;
	}

	/** 取得統一編號 **/
	public String getCustId() {
		return this.custId;
	}
	/** 設定統一編號 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得重覆序號 **/
	public String getDupNo() {
		return this.dupNo;
	}
	/** 設定重覆序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得中文戶名 **/
	public String getCustName() {
		return this.custName;
	}
	/** 設定中文戶名 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/** 取得區部別 **/
	public String getTypCd() {
		return this.typCd;
	}
	/** 設定區部別 **/
	public void setTypCd(String value) {
		this.typCd = value;
	}

	/** 
	 * 取得與借款人關係<p/>
	 * (多選) 只能為數字<br/>
	 *  借戶 | 1<br/>
	 *  共同借款人 | 2<br/>
	 *  負責人 | 3<br/>
	 *  連保人 | 4<br/>
	 *  擔保品提供人 | 5<br/>
	 *  關係企業 | 6<br/>
	 *  實質受益人|7<br/>
	 *  一般保證人|8<br/>
	 *  應收帳款買方無追索|9<br/>
	 *  高階管理員|10<br/>
	 *  具控制權人|11<br/>
	 *  董監事|12
	 */
	public String getCustRelation() {
		return this.custRelation;
	}
	/**
	 *  設定與借款人關係<p/>
	 *  (多選) 只能為數字<br/>
	 *  借戶 | 1<br/>
	 *  共同借款人 | 2<br/>
	 *  負責人 | 3<br/>
	 *  連保人 | 4<br/>
	 *  擔保品提供人 | 5<br/>
	 *  關係企業 | 6<br/>
	 *  實質受益人|7<br/>
	 *  一般保證人|8<br/>
	 *  應收帳款買方無追索|9<br/>
	 *  高階管理員|10<br/>
	 *  具控制權人|11<br/>
	 *  董監事|12
	 **/
	public void setCustRelation(String value) {
		this.custRelation = value;
	}

	/** 
	 * 取得查詢類別<p/>
	 * (多選) 01公司登記事項卡(商工資料)<br/>
	 *  02稅籍登記資料公示查詢<br/>
	 *  03身份證遺失檢查<br/>
	 *  04受監護輔助宣告(家事公告)<br/>
	 *  05銀行法及金控法利害關係人查詢
	 */
	public String getType() {
		return this.type;
	}
	/**
	 *  設定查詢類別<p/>
	 *  (多選) 01公司登記事項卡(商工資料)<br/>
	 *  02稅籍登記資料公示查詢<br/>
	 *  03身份證遺失檢查<br/>
	 *  04受監護輔助宣告(家事公告)<br/>
	 *  05銀行法及金控法利害關係人查詢
	 **/
	public void setType(String value) {
		this.type = value;
	}

	/** 
	 * 取得資料查詢日<p/>
	 * YYYY-MM-01
	 */
	public Date getQueryDateS() {
		return this.queryDateS;
	}
	/**
	 *  設定資料查詢日<p/>
	 *  YYYY-MM-01
	 **/
	public void setQueryDateS(Date value) {
		this.queryDateS = value;
	}

	/** 取得輸入資料檢誤完成(Y/N) **/
	public String getChkYN() {
		return this.chkYN;
	}
	/** 設定輸入資料檢誤完成(Y/N) **/
	public void setChkYN(String value) {
		this.chkYN = value;
	}

	/** 取得備註(名單查詢結果) **/
	public String getMemo() {
		return this.Memo;
	}
	/** 設定備註(名單查詢結果) **/
	public void setMemo(String value) {
		this.Memo = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 
	 * 取得發證民國年<p/>
	 * J-109-0208
	 */
	public String getIdDateYear() {
		return this.idDateYear;
	}
	/**
	 *  設定發證民國年<p/>
	 *  J-109-0208
	 **/
	public void setIdDateYear(String value) {
		this.idDateYear = value;
	}

	/** 
	 * 取得發證民國月<p/>
	 * J-109-0208
	 */
	public String getIdDateMonth() {
		return this.idDateMonth;
	}
	/**
	 *  設定發證民國月<p/>
	 *  J-109-0208
	 **/
	public void setIdDateMonth(String value) {
		this.idDateMonth = value;
	}

	/** 
	 * 取得發證民國日<p/>
	 * J-109-0208
	 */
	public String getIdDateDay() {
		return this.idDateDay;
	}
	/**
	 *  設定發證民國日<p/>
	 *  J-109-0208
	 **/
	public void setIdDateDay(String value) {
		this.idDateDay = value;
	}

	/** 
	 * 取得發證地點<p/>
	 * J-109-0208codetype=lms1601m01_IDSite
	 */
	public String getIdSite() {
		return this.idSite;
	}
	/**
	 *  設定發證地點<p/>
	 *  J-109-0208codetype=lms1601m01_IDSite
	 **/
	public void setIdSite(String value) {
		this.idSite = value;
	}

	/** 
	 * 取得領補換別<p/>
	 * J-109-02081初發 2補發 3換發
	 */
	public String getIdChangeType() {
		return this.idChangeType;
	}
	/**
	 *  設定領補換別<p/>
	 *  J-109-02081初發 2補發 3換發
	 **/
	public void setIdChangeType(String value) {
		this.idChangeType = value;
	}
	public void setCustRelationIndex(String custRelationIndex) {
		this.custRelationIndex = custRelationIndex;
	}
	public String getCustRelationIndex() {
		return custRelationIndex;
	}
}
