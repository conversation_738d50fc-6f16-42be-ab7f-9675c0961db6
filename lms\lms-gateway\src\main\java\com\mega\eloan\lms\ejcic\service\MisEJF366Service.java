/* 
 * MisEJF366Service.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.ejcic.service;

import java.util.Map;


/**
 * <pre>
 * MIS.BAM206>>MIS.EJV36601>>MIS.EJF366
 * </pre>
 * 
 * @since 2012/03/23
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/03/23,TimChiang,new
 *          </ul>
 */
public interface MisEJF366Service {

	/**
	 * 查詢保證公司之票信及債信 (聯徵)
	 * @param id 客戶統一編號
	 * @param prodId 查詢產品別
	 * @return Map
	 */
	Map<String, Object> findTkLoanAndCrLoanStatus(String id, String prodId);
}
