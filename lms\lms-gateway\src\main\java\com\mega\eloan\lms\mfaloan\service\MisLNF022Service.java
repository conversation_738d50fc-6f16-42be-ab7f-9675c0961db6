package com.mega.eloan.lms.mfaloan.service;

import java.util.List;
import java.util.Map;

public interface MisLNF022Service {
	public List<?> findLNLNF022ForNetLoanNBal(String condition);

	public List<?> findLNLNF022ForNetLoanQta(String condition);

	public List<?> findLNLNF022ForNetLoanNQta(String condition);

	public List<?> findLNLNF022ForNetLoanSBal(String condition);

	/**
	 * <li>0-核准額度-原幣 <li>1-企業集團代碼 <li>2-開狀未到單金額-折台幣 <li>3-授信擔保餘額-折台幣 <li>
	 * 4-授信無擔保餘額-折台幣 <li>5-授信無擔保額度-折台幣 <li>6-有效總額度－折台幣金額 <li>7-有效無擔額度－折台幣 <li>
	 * 8-有效有擔額度－折台幣
	 */
	final String[] Lnf022Cols = { "LNF022V1_INDUS", "LNF022V1_FACT_AMT",
			"LNF022V1_OPEN_BAL", "LNF022V1_LOAN_BAL_S", "LNF022V1_LAON_BAL_N",
			"LNF022V1_FACT_AMT_N", "LNF022_AVLFAMT_T", "LNF022_AVLFAMT_TN",
			"LNF022_AVLFAMT_TS" };

	/**
	 * 以集團代碼查詢集團代號(總)授信明細
	 * 
	 * @param grpId
	 *            集團代碼
	 * 
	 * @return 集團代號(總)授信明細
	 */
	public Map<String, Object> findByGrpId(String grpId);

	/**
	 * 引入前一日授信額度與餘額資料
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆編號
	 * @return Map<String, Object> AMT decimal 前一日授信額度(元) BALS decimal
	 *         前一日有擔餘額(元) BALN decimal 前一日無擔餘額(元) BAL decimal 前一日餘額(元) DATE
	 *         String 前一日
	 */
	Map<String, Object> findByRelCompDeposit(String custId, String dupNo);

	/**
	 * 引進前一日授信明細
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @return List<Map<String,Object> CONT 額度序號 ACCT 科目 BAL 餘額 FACT_SWFT 額度幣別
	 *         QTA 額度 USED_RTEY 前一日年度動用率
	 */
	List<Map<String, Object>> findLoanMRate(String custId, String dupNo);

	/**
	 * 取得指定客戶的最新戶況
	 * 
	 * @param custId
	 *            String
	 * @param dupNo
	 *            String
	 * @return Map<String, Object>
	 */
	Map<String, Object> findCustLoanStatus(String custId, String dupNo);

	/**
	 * 引入主債務授信餘額
	 * 
	 * @param custId
	 * @param dupNo
	 * @return List<Map<String,Object>>
	 */
	List<Map<String, Object>> findMainLoanBalance(String custId, String dupNo);

	/**
	 * 引入共同(保證)債務授信餘額 <li>回傳兩筆Map<String, Object>
	 * <ol>
	 * <li>第一筆為共同債務授信餘額
	 * <dl>
	 * <li>若共同債務授信餘額無資料，則List第一筆Map為null
	 * </dl>
	 * <li>第二筆為保證債務授信餘額
	 * <dl>
	 * <li>若共同債務授信餘額無資料，則List第二筆Map為null
	 * </dl>
	 * </ol>
	 * 
	 * @param custId
	 *            統一編號
	 * @param dupNo
	 *            重覆序號
	 * @param brNo
	 *            分行代號多筆輸入
	 * @return List<Map<String,Object> LNF022_BR_NO 分行別 TOTAL_BAL 授信餘額 TWD仟元 FG
	 *         戶況 FG_CODE 戶況代碼 CNAME 姓名
	 */
	List<Map<String, Object>> findCommonLoanBalance(String custId,
			String dupNo, String brNo);

	/**
	 * 引入不在指定分行內的共同(保證)債務授信餘額
	 * 
	 * @param custId
	 *            統一編號
	 * @param dupNo
	 *            重覆序號
	 * @param brNo
	 *            分行代號多筆輸入
	 * @return List<Map<String,Object>
	 */
	List<Map<String, Object>> findCommonLoanBalanceNotInBrNo(String custId,
			String dupNo, String brNo);

	/**
	 * 額度明細表 引進帳務資料(購料放款案下已開狀未到單金額)LNF022
	 * 
	 * @param custId
	 *            統一編號 (一定要10碼不足補空白)
	 * @param dupNo
	 *            重覆序號
	 * @param cntrNo
	 *            額度序號
	 * @return <pre>
	 * FLAG 是否為開放清單  Y/N
	 * LCAMT 購料放款案下已開狀未到單金額
	 * </pre>
	 */
	List<Map<String, Object>> findByContractAndCustId(String custId,
			String dupNo, String cntrNo);

	/**
	 * 取得餘額日期(產案件明細表Word專用)
	 * 
	 * @param allCust
	 *            串好的統編+重複序號
	 * @param cntrNo
	 *            額度序號
	 * @return 餘額日期
	 */
	List<?> findLnf022_loan_date(String allCust, String cntrNo);

	/**
	 * LMS2305M01 確認額度是否已動用
	 * 
	 * @param custId
	 *            統一編號 (一定要10碼不足補空白)
	 * @param dupNo
	 *            重覆序號
	 * @param cntrNo
	 *            額度序號
	 * @return Flag Y | N
	 */
	String findDB2ChkALoanUse(String custId, String dupNo, String cntrNo);

	/**
	 * 查詢本行額度（千元）扣掉應收帳款餘額
	 * 
	 * @param brno
	 *            分行代碼
	 * @param allCustId
	 *            統編+重覆序號
	 * @return
	 */
	Map<String, Object> selFactAmt1(String brno, String allCustId);

	/**
	 * 查詢該戶在聯行額度（千元）扣掉應收帳款餘額
	 * 
	 * @param brno
	 *            分行代碼
	 * @param allCustId
	 *            統編+重覆序號
	 * @return
	 */
	Map<String, Object> selFactAmt2(String brno, String allCustId);

	/**
	 * 覆審抓取共管額度
	 * 
	 * @param custId
	 * @param brNo
	 * @return
	 */
	List<Map<String, Object>> getLNF022_CtlData(String custId, String dupNo,
			String brNo);

	/**
	 * 覆審報告表引進覆審資料
	 * 
	 * @param custId
	 * @param dupNo
	 * @param brNo
	 * @return
	 */
	List<Map<String, Object>> getLNF022_forCtl(String custId, String dupNo,
			String brNo);

	Map<String, Object> gfnCTL_Import_LNF022(String branch, String custId,
			String dupNo);

	// 2020/04 配合新冠肺炎紓困貸款專案，新增 J.純紓困貸款戶之首次覆審。
	List<Map<String, Object>> gfnCTL_Import_LNF022_selContract(String branch,
			String custId, String dupNo);

	Map<String, Object> gfnCTL_Get_Cust_Worst_Status(String branch,
			String custId, String dupNo);

	List<Map<String, Object>> gfnGenerateCTL_FLMS180R12(String brNo);

	/**
	 * 依額度序號找出動用率
	 */
	List<Map<String, Object>> getMrateByCntrno(String cntrNo, String date);

	public List<Map<String, Object>> findSumBycntrNo(String cntrNo);

	/**
	 * 依借款人統編查詢資料 J-105-0228-001 Web e-Loan企金授信簽報書新增私募基金相關建檔與報表。
	 */
	public List<Map<String, Object>> getByCustId(String custId, String dupNo);

	/**
	 * 依借款人統編查詢資料 J-105-0228-001 Web e-Loan企金授信簽報書新增私募基金相關建檔與報表。
	 */
	public List<Map<String, Object>> getByCntrNo(String cntrNo);

	/**
	 * J-105-0331-001 新增已核准授信額度辦理狀態通報彙總表
	 */
	public List<Map<String, Object>> getByCntrNoAndCustId(String cntrNo,
			String custId, String dupNo);

	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public Map<String, Object> findSumByCustId(String custId, String dupNo);

	public List<Map<String, Object>> SumAvlFamtByCntrNoAndSwft(String cntrNo);

	/**
	 * J-108-0107_05097_B1001 國內分行新核准往來企金客戶數統計表(按分行列表)
	 * 
	 * @param cntrNo
	 * @return
	 */
	public Map<String, Object> findSumBalGroupByCntrNo(String cntrNo);

	/**
	 * J-108-0107_05097_B1002 Web e-Loan企金授信新核准往來企金客戶數統計表新增動用資訊與調整效能
	 * 
	 * @param cntrNo
	 * @return
	 */
	public Map<String, Object> findByCntrNoFetchOne(String cntrNo);

	/**
	 * J-108-0288_05097_B1001 Web e-Loan授信系統新增合併關係企業額度彙總表
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */

	public Map<String, Object> findPureLoanByCustIdForRelatedCompany(
			String custId, String dupNo);

	/**
	 * J-108-0288_05097_B1001 Web e-Loan授信系統新增合併關係企業額度彙總表
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */

	public Map<String, Object> findImportLoanByCustIdForRelatedCompany(
			String custId, String dupNo);

	/**
	 * J-108-0288_05097_B1001 Web e-Loan授信系統新增合併關係企業額度彙總表
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */

	public Map<String, Object> findExportLoanByCustIdForRelatedCompany(
			String custId, String dupNo);

	/**
	 * J-108-0288_05097_B1001 Web e-Loan授信系統新增合併關係企業額度彙總表
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public Map<String, Object> findArSellerLoanByCustIdForRelatedCompany(
			String custId, String dupNo);

	/**
	 * J-109-0291_05097_B1001 簡化小規模營業人異常通報簽報流程。
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public List<Map<String, Object>> gfnCTL_Import_LNF022_selContract_without_branch(
			String custId, String dupNo);

	/**
	 * J-109-0456
	 * 授信有效額度新臺幣五百萬元(含)以下且經信保七成(含)以上之不循環動用案件，除新做、增貸案件應於撥貸後之半年內辦理覆審外，免再辦理覆審。
	 */
	public List<Map<String, Object>> findSumByCustIdAndGetIpfdRate(
			String custId, String dupNo);

	public List<String> getContractNoWithinValidCreditPeriod(
			String cntrNoString, String toDate);

	/**
	 * J-110-0234_05097_B1002 Web e-Loan國內企金簽報書小規模RPA修改
	 */
	public List<Map<String, Object>> findSmallBussCByCustId(String custId);

	/**
	 * J-110-0234_05097_B1002 Web e-Loan國內企金簽報書小規模RPA修改
	 */
	public List<Map<String, Object>> findSmallBussCByCustIdHasCancel(
			String custId);

	public List<Map<String, Object>> findStartUpReliefByCustid(String custId,
			String[] rescueItem, String[] rescueItemSub);

	/**
	 * 經濟部協助中小型事業疫後振興專案貸款 & 經濟部協助中小企業轉型發展專案貸款
	 * 
	 * @param custId
	 * @param rescueItem
	 * @return
	 */
	public List<Map<String, Object>> findByCustIdRescueItem(String custId,
			String rescueItem);
}
