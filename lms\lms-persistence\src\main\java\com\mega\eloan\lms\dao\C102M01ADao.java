/* 
 * C102M01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C102M01A;

/** 購置房屋擔保放款風險權數檢核表 **/
public interface C102M01ADao extends IGenericDao<C102M01A> {

	C102M01A findByOid(String oid);
	
	List<C102M01A> findByMainId(String mainId);
	List<C102M01A> findByMainIds(String []  mainIds);

	List<C102M01A> findByIndex01(String mainId, String custId, String dupNo, String cntrNo, String aLoanAC, Date aLoanDate, String aLoanPurpose, String rskFlag);

	C102M01A findByMainId2(String mainId);

	C102M01A findByIndex02(String custId, String dupNo, String cntrNo, String aLoanAC);
}