package com.mega.eloan.lms.base.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;
import org.springframework.stereotype.Service;

import com.mega.eloan.lms.base.common.ClsScoreUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.CLSScoreUpDWService;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.dao.C120S01EDao;
import com.mega.eloan.lms.dao.L140M02ADao;
import com.mega.eloan.lms.mfaloan.bean.DW_RKADJUST;
import com.mega.eloan.lms.mfaloan.bean.DW_RKAPPLICANT;
import com.mega.eloan.lms.mfaloan.bean.DW_RKAPPLICANT_N;
import com.mega.eloan.lms.mfaloan.bean.DW_RKCNTRNO;
import com.mega.eloan.lms.mfaloan.bean.DW_RKCREDIT;
import com.mega.eloan.lms.mfaloan.bean.DW_RKCREDIT_N;
import com.mega.eloan.lms.mfaloan.bean.DW_RKJCIC;
import com.mega.eloan.lms.mfaloan.bean.DW_RKPROJECT;
import com.mega.eloan.lms.mfaloan.bean.DW_RKSCORE;
import com.mega.eloan.lms.mfaloan.bean.DW_RKSCORE_N;
import com.mega.eloan.lms.model.C101S01G_N;
import com.mega.eloan.lms.model.C101S01Q_N;
import com.mega.eloan.lms.model.C101S01R_N;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.C120S01B;
import com.mega.eloan.lms.model.C120S01C;
import com.mega.eloan.lms.model.C120S01E;
import com.mega.eloan.lms.model.C120S01G;
import com.mega.eloan.lms.model.C120S01Q;
import com.mega.eloan.lms.model.C120S01R;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M02A;

import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapMath;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.Util;


@Service("CLSScoreUpDWService")
public class CLSScoreUpDWServiceImpl extends AbstractCapService implements CLSScoreUpDWService {
	
	@Resource
	CLSService clsService;
	
	@Resource
	C120S01EDao c120s01eDao;
	
	@Resource
	L140M02ADao l140m02aDao;
	
	private static final BigDecimal 單位_仟 = new BigDecimal("1000");
	
	/**
	 * DW_RKSCORE評分變量紀錄 List
	 * 
	 */
	@Override
	public List<DW_RKSCORE> upDW_RKSCORE(List<DW_RKSCORE> data,
			L120M01A l120m01a, L140M01A l140m01a, String ACCT_KEY,
			String modelKind, String LNGEFLAG, C120M01A c120m01a,
			C120S01G c120s01g, C120S01Q c120s01q, C120S01R c120s01r,
			String dDOCSTATUS) {

		String BR_CD = Util.trim(l140m01a.getOwnBrId());
		String NOTEID = Util.trim(l140m01a.getMainId());
		String CUSTID = Util.trim(c120m01a.getCustId());
		String DUPNO = Util.trim(c120m01a.getDupNo());

		Integer MOWVER1 = new Integer(0);
		Integer MOWVER2 = new Integer(0);

		// J-102-0196區分房貸(C120S01G)及非房貸(C120S01Q)信用模型
		String mowtype = "";
		Date jcicDate = null;

		BigDecimal R10_REVOL_RATE = null;
		BigDecimal R10_REVOL_RATE_SCORE = null;
		BigDecimal D07_LN_NOS_TAMT = null;
		BigDecimal D07_LN_NOS_TAMT_SCORE = null;
		BigDecimal D15_CC6_AVG_RC = null;
		BigDecimal D15_CC6_AVG_RC_SCORE = null;
		BigDecimal P19_CC12_PCODE_A_TIMES = null;
		String MARRIAGE = null;
		Integer CHILDREN = null;
		String OCCUPATION_1 = null;
		BigDecimal OCCUPATION_1_SCORE = null;
		String EDUCATION = null;
		Integer N06_INQ12_NAPP_BANK = null;
		BigDecimal N06_INQ12_NAPP_BANK_SCORE = null;
		Date RATING_DATE = null;
		BigDecimal R01_CC12_REVOL_RATE = null;
		BigDecimal R01_CC12_REVOL_RATE_SCORE = null;
		BigDecimal HINCOME_REG = null;
		BigDecimal HINCOME_REG_SCORE = null;
		BigDecimal P69_CC12_DELAY_RC_TIMES = null;
		BigDecimal P25_CC6_PCODE_A_TIMES = null;
		BigDecimal P25_CC6_PCODE_A_TIMES_SCORE = null;
		BigDecimal MARR_EDU_SCORE = null;
		BigDecimal P69_P19_SCORE = null;

		BigDecimal YPAY = null;
		BigDecimal YPAY_SCORE = null;
		BigDecimal SENIORITY = null;
		BigDecimal SENIORITY_SCORE = null;
		BigDecimal EDUCATION_N_SCORE = null;
		BigDecimal D63_LN_NOS_BANK = null;
		BigDecimal D63_SCORE = null;
		BigDecimal A21_CC6_RC_USE_MONTH = null;
		BigDecimal A21_SCORE = null;
		BigDecimal A11_CC6_RC_USE_BANK = null;
		BigDecimal A11_SCORE = null;
		BigDecimal D53_LN_6_TIMES_FLAG = null;
		BigDecimal D53_SCORE = null;
		BigDecimal PINCOME = null;
		BigDecimal PINCOME_SCORE = null;
		BigDecimal P68_CC6_DELAY_RC_TIMES = null;
		BigDecimal P68_P19_SCORE = null;
		BigDecimal N18_INQ12_BY30D = null;
		BigDecimal N18_SCORE = null;
		BigDecimal D07_DIV_PINCOME = null;
		BigDecimal D07_DIV_PINCOME_SCORE = null;
		BigDecimal Z03_ACC_DEBT_ENTERPRISE = null;
		BigDecimal Z03_SCORE = null;
		String c_flag = "";
		BigDecimal DRATE_SCORE = null;
		
		// 消金房貸2.1
		BigDecimal N22_INQ12_BY30D = null;
		BigDecimal N22_SCORE = null;
		
		// 消金房貸3.0
		BigDecimal DESIGNATION_SCORE = null;
		BigDecimal D42_SCORE = null;
		BigDecimal P68_SCORE = null;
		BigDecimal YRATE_SCORE = null;
		BigDecimal N01_SCORE = null;
		BigDecimal P19_CC12_PCODE_A_TIMES_SCORE = null;
		String DESIGNATION = "";
		BigDecimal D42_AVG_LIMIT = null;
		BigDecimal N01_INQ3_TOTAL = null;

		// 消金非房貸4.0
		BigDecimal P1_O_COUNTS = null;
		BigDecimal LOAN_BAL_S_BYID = null;
		BigDecimal R01_CC12_MAX_REVOL_RATE = null;
		BigDecimal P1_SCORE = null;
		BigDecimal LOAN_BAL_S_BYIDSCORE = null;
		BigDecimal R01_CC12_MAX_REVOL_RATE_SCORE = null;

		if (Util.equals(UtilConstants.DEFAULT.是, c120m01a.getModelTyp())
				|| Util.equals(UtilConstants.L140S02AModelKind.房貸, modelKind)) {
			// 新版模型上線前舊案 || 新模型分類為房貸
			if (!Util.isEmpty(c120s01g.getVarVer())) {
				String VarVer[] = c120s01g.getVarVer().split("\\.");
				MOWVER1 = Util.parseInt(VarVer[0]);
				MOWVER2 = Util.parseInt(VarVer[1]);
			}
			jcicDate = c120s01g.getJcicQDate();
			mowtype = "M";
			MARRIAGE = Util.trim(c120s01g.getMarry());
			CHILDREN = c120s01g.getChild();
			EDUCATION = Util.trim(c120s01g.getEdu());
			OCCUPATION_1 = Util.trim(c120s01g.getJobType1());
			SENIORITY = c120s01g.getSeniority();

			D07_LN_NOS_TAMT = c120s01g.getChkAmt01();

			P19_CC12_PCODE_A_TIMES = c120s01g.getChkNum3();

			RATING_DATE = c120s01g.getGrdCDate();
			if (c120s01g.getYFamAmt() != null) {
				HINCOME_REG = LMSUtil.getUploadYFamAmt(c120s01g.getYFamAmt());
			}
			HINCOME_REG_SCORE = c120s01g.getScrNum01();

			P69_CC12_DELAY_RC_TIMES = c120s01g.getChkNum2();

			P25_CC6_PCODE_A_TIMES = c120s01g.getChkNum1();
			P25_CC6_PCODE_A_TIMES_SCORE = c120s01g.getScrNum09();

			if (Util.equals(ClsScoreUtil.V1_3_HOUSE_LOAN, c120s01g.getVarVer())) {
				MARR_EDU_SCORE = c120s01g.getScrNum02();

				OCCUPATION_1_SCORE = c120s01g.getScrNum03();

				D07_LN_NOS_TAMT_SCORE = c120s01g.getScrNum04();

				D15_CC6_AVG_RC = c120s01g.getChkAmt02();
				D15_CC6_AVG_RC_SCORE = c120s01g.getScrNum05();

				N06_INQ12_NAPP_BANK = c120s01g.getInqQty();
				N06_INQ12_NAPP_BANK_SCORE = c120s01g.getScrNum06();

				R01_CC12_REVOL_RATE = c120s01g.getAvgRate01();
				R01_CC12_REVOL_RATE_SCORE = c120s01g.getScrNum07();

				R10_REVOL_RATE = c120s01g.getAvgRate02();
				R10_REVOL_RATE_SCORE = c120s01g.getScrNum08();

				P69_P19_SCORE = c120s01g.getScrNum10();
			} else if (Util.equals(ClsScoreUtil.V2_0_HOUSE_LOAN,c120s01g.getVarVer())
					|| Util.equals(ClsScoreUtil.V2_1_HOUSE_LOAN,c120s01g.getVarVer())) {
				SENIORITY_SCORE = c120s01g.getScrSeniority_G();

				// D07_DIV_PINCOME
				PINCOME = c120s01g.getPIncome();
				// 因子 D07 已在上面設定
				D07_DIV_PINCOME = c120s01g.getItemD07_DIV_PINCOME();
				D07_DIV_PINCOME_SCORE = c120s01g.getScrD07_DIV_PINCOME();

				if(Util.equals(ClsScoreUtil.V2_0_HOUSE_LOAN,c120s01g.getVarVer())){
					// N18
					N18_INQ12_BY30D = c120s01g.getItemN18();
					N18_SCORE = c120s01g.getScrN18();
				}else if(Util.equals(ClsScoreUtil.V2_1_HOUSE_LOAN,c120s01g.getVarVer())){
					// N22
					N22_INQ12_BY30D = c120s01g.getItemN22();
					N22_SCORE = c120s01g.getScrN22();
				}
				

				// P68_P19
				P68_CC6_DELAY_RC_TIMES = c120s01g.getItemP68();
				// 因子 P19 已在上面設定
				P68_P19_SCORE = c120s01g.getScrP68P19();

				// Z03
				Z03_ACC_DEBT_ENTERPRISE = c120s01g.getItemZ03();
				Z03_SCORE = c120s01g.getScrZ03();
			} else if (Util.equals(ClsScoreUtil.V3_0_HOUSE_LOAN,
					c120s01g.getVarVer())) {
				// N18 近12個月新業務申請查詢總次數，計算方式-天數別(30天內算1次)
				N22_INQ12_BY30D = c120s01g.getItemN22();
				N22_SCORE = c120s01g.getScrN22();

				// DESIGNATION 職稱
				DESIGNATION = c120s01g.getJobTitle();
				DESIGNATION_SCORE = c120s01g.getScrJobTitle();

				// D42 當月有效信用卡主卡平均信用額度(仟元)
				D42_AVG_LIMIT = c120s01g.getItemD42();
				D42_SCORE = c120s01g.getScrD42();

				// EDUCATION 學歷
				EDUCATION = c120s01g.getEdu();
				EDUCATION_N_SCORE = c120s01g.getScrEdu();

				// P68 近6個月信用卡繳款狀況出現不良繳款紀錄或使用循環信用的次數
				P68_CC6_DELAY_RC_TIMES = c120s01g.getItemP68();
				P68_SCORE = c120s01g.getScrP68();

				// YRATE 夫妻負債比
				// YRATE = c120s01g.getYRate(); //DW的YRATE，傳到DW_RKAPPLICANT
				YRATE_SCORE = c120s01g.getScrYRate();

				// N01 近3個月非Z類申請查詢總次數(近三個月本行查詢不列入計算)
				N01_INQ3_TOTAL = c120s01g.getItemN01();
				N01_SCORE = c120s01g.getScrN01();

				// P19 近12個月信用卡繳款狀況出現全額繳清無延遲次數(不含無須繳款)
				P19_CC12_PCODE_A_TIMES = c120s01g.getChkNum3();
				P19_CC12_PCODE_A_TIMES_SCORE = c120s01g.getScrP19();

				// R01 近12個月信用卡(每筆)循環信用平均使用率_(mean(單筆循環信用使用率))
				R01_CC12_REVOL_RATE = c120s01g.getAvgRate01();
				R01_CC12_REVOL_RATE_SCORE = c120s01g.getScrNum07();

			}
		} else if (Util.equals(UtilConstants.L140S02AModelKind.非房貸, modelKind)) {
			if (!Util.isEmpty(c120s01q.getVarVer())) {
				String VarVer[] = c120s01q.getVarVer().split("\\.");
				MOWVER1 = Util.parseInt(VarVer[0]);
				MOWVER2 = Util.parseInt(VarVer[1]);
			}
			jcicDate = c120s01q.getJcicQDate();
			mowtype = "N";
			c_flag = ClsScoreUtil.upDW_column_C_FLAG(c120s01q.getVarVer());

			RATING_DATE = c120s01q.getGrdCDate();

			if (Util.equals(ClsScoreUtil.V1_0_NOT_HOUSE_LOAN,
					c120s01q.getVarVer())) {
				YPAY = c120s01q.getYPay();
				YPAY_SCORE = c120s01q.getScrypay();
				SENIORITY = c120s01q.getSeniority();
				SENIORITY_SCORE = c120s01q.getScrseniority();
				EDUCATION = Util.trim(c120s01q.getEducation());
				EDUCATION_N_SCORE = c120s01q.getScreducation();
				D63_LN_NOS_BANK = c120s01q.getNochkItem01();
				D63_SCORE = c120s01q.getNoscrItem01();
				A21_CC6_RC_USE_MONTH = c120s01q.getNochkItem02();
				A21_SCORE = c120s01q.getNoscrItem02();
				A11_CC6_RC_USE_BANK = c120s01q.getNochkItem03();
				A11_SCORE = c120s01q.getNoscrItem03();
				D53_LN_6_TIMES_FLAG = c120s01q.getNochkItem04();
				D53_SCORE = c120s01q.getNoscrItem04();
			} else if (Util.equals(ClsScoreUtil.V2_0_NOT_HOUSE_LOAN,
					c120s01q.getVarVer())
					|| Util.equals(ClsScoreUtil.V2_1_NOT_HOUSE_LOAN,
							c120s01q.getVarVer())) {
				PINCOME = c120s01q.getPIncome();
				PINCOME_SCORE = c120s01q.getScrPIncome();
				SENIORITY = c120s01q.getSeniority();
				SENIORITY_SCORE = c120s01q.getScrseniority();
				D63_LN_NOS_BANK = c120s01q.getNochkItem01();
				D63_SCORE = c120s01q.getNoscrItem01();
				A11_CC6_RC_USE_BANK = c120s01q.getNochkItem03();
				A11_SCORE = c120s01q.getNoscrItem03();
				D07_LN_NOS_TAMT = c120s01q.getNochkItemD07();
				D07_LN_NOS_TAMT_SCORE = c120s01q.getNoscrItemD07();
				String str_N06 = null;
				if (c120s01q.getNochkItemN06() != null) {
					str_N06 = NumConverter.addComma(c120s01q.getNochkItemN06());
				}
				N06_INQ12_NAPP_BANK = clsService.parseIntColumn(str_N06);
				N06_INQ12_NAPP_BANK_SCORE = c120s01q.getNoscrItemN06();
				P68_CC6_DELAY_RC_TIMES = c120s01q.getNochkItemP68();
				P19_CC12_PCODE_A_TIMES = c120s01q.getNochkItemP19();
				P68_P19_SCORE = c120s01q.getNoscrItemP68P19();
			} else if (Util.equals(ClsScoreUtil.V3_0_NOT_HOUSE_LOAN,c120s01q.getVarVer()) 
					|| Util.equals(ClsScoreUtil.V3_1_NOT_HOUSE_LOAN,c120s01q.getVarVer())) {
				PINCOME = c120s01q.getPIncome();
				PINCOME_SCORE = c120s01q.getScrPIncome();
				DRATE_SCORE = c120s01q.getNoscrItemDrate();
				D07_LN_NOS_TAMT = c120s01q.getNochkItemD07();
				D07_LN_NOS_TAMT_SCORE = c120s01q.getNoscrItemD07();
				String str_N06 = null;
				if (c120s01q.getNochkItemN06() != null) {
					str_N06 = LMSUtil.pretty_numStr(c120s01q.getNochkItemN06());
				}
				N06_INQ12_NAPP_BANK = clsService.parseIntColumn(str_N06);
				N06_INQ12_NAPP_BANK_SCORE = c120s01q.getNoscrItemN06();

				P19_CC12_PCODE_A_TIMES = c120s01q.getNochkItemP19();
				P69_CC12_DELAY_RC_TIMES = c120s01q.getNochkItemP69();
				P69_P19_SCORE = c120s01q.getNoscrItemP69P19();

				R01_CC12_REVOL_RATE = c120s01q.getNochkItemR01();
				R01_CC12_REVOL_RATE_SCORE = c120s01q.getNoscrItemR01();

				P25_CC6_PCODE_A_TIMES = c120s01q.getNochkItemP25();
				P25_CC6_PCODE_A_TIMES_SCORE = c120s01q.getNoscrItemP25();
			} else if (Util.equals(ClsScoreUtil.V4_0_NOT_HOUSE_LOAN,
					c120s01q.getVarVer())) { // J-111-0373 消金非房貸4.0
				P1_O_COUNTS = c120s01q.getItemP01();
				P1_SCORE = c120s01q.getScrP01();

				LOAN_BAL_S_BYID = c120s01q.getLoanBalSByid();
				LOAN_BAL_S_BYIDSCORE = c120s01q.getScrLoanBalSByid();

				R01_CC12_MAX_REVOL_RATE = c120s01q.getItemMaxR01();
				R01_CC12_MAX_REVOL_RATE_SCORE = c120s01q.getScrMaxR01();

				EDUCATION = Util.trim(c120s01q.getEducation());
				EDUCATION_N_SCORE = c120s01q.getScreducation();

				DRATE_SCORE = c120s01q.getNoscrItemDrate();
			}
		} else if (Util.equals(UtilConstants.L140S02AModelKind.卡友貸, modelKind)) {
			if (!Util.isEmpty(c120s01r.getVarVer())) {
				String VarVer[] = c120s01r.getVarVer().split("\\.");
				MOWVER1 = Util.parseInt(VarVer[0]);
				MOWVER2 = Util.parseInt(VarVer[1]);
			}
			jcicDate = c120s01r.getJcicQDate();
			mowtype = ClsScoreUtil.upDW_column_MowType(c120s01r);
			c_flag = "Y";

			RATING_DATE = c120s01r.getGrdCDate();

			if (Util.equals(ClsScoreUtil.V2_1_CARD_LOAN, c120s01r.getVarVer())) {
				PINCOME = c120s01r.getPIncome();
				PINCOME_SCORE = c120s01r.getScrPIncome();
				SENIORITY = c120s01r.getSeniority();
				SENIORITY_SCORE = c120s01r.getScrseniority();
				D63_LN_NOS_BANK = c120s01r.getNochkItem01();
				D63_SCORE = c120s01r.getNoscrItem01();
				A11_CC6_RC_USE_BANK = c120s01r.getNochkItem03();
				A11_SCORE = c120s01r.getNoscrItem03();
				D07_LN_NOS_TAMT = c120s01r.getNochkItemD07();
				D07_LN_NOS_TAMT_SCORE = c120s01r.getNoscrItemD07();
				String str_N06 = null;
				if (c120s01r.getNochkItemN06() != null) {
					str_N06 = NumConverter.addComma(c120s01r.getNochkItemN06());
				}
				N06_INQ12_NAPP_BANK = clsService.parseIntColumn(str_N06);
				N06_INQ12_NAPP_BANK_SCORE = c120s01r.getNoscrItemN06();
				P68_CC6_DELAY_RC_TIMES = c120s01r.getNochkItemP68();
				P19_CC12_PCODE_A_TIMES = c120s01r.getNochkItemP19();
				P68_P19_SCORE = c120s01r.getNoscrItemP68P19();
			} else if (Util.equals(ClsScoreUtil.V3_0_CARD_LOAN,c120s01r.getVarVer()) || 
					Util.equals(ClsScoreUtil.V3_1_CARD_LOAN,c120s01r.getVarVer())) {
				PINCOME = c120s01r.getPIncome();
				PINCOME_SCORE = c120s01r.getScrPIncome();
				DRATE_SCORE = c120s01r.getNoscrItemDrate();
				D07_LN_NOS_TAMT = c120s01r.getNochkItemD07();
				D07_LN_NOS_TAMT_SCORE = c120s01r.getNoscrItemD07();
				String str_N06 = null;
				if (c120s01r.getNochkItemN06() != null) {
					str_N06 = LMSUtil.pretty_numStr(c120s01r.getNochkItemN06());
				}
				N06_INQ12_NAPP_BANK = clsService.parseIntColumn(str_N06);
				N06_INQ12_NAPP_BANK_SCORE = c120s01r.getNoscrItemN06();

				P19_CC12_PCODE_A_TIMES = c120s01r.getNochkItemP19();
				P69_CC12_DELAY_RC_TIMES = c120s01r.getNochkItemP69();
				P69_P19_SCORE = c120s01r.getNoscrItemP69P19();

				R01_CC12_REVOL_RATE = c120s01r.getNochkItemR01();
				R01_CC12_REVOL_RATE_SCORE = c120s01r.getNoscrItemR01();

				P25_CC6_PCODE_A_TIMES = c120s01r.getNochkItemP25();
				P25_CC6_PCODE_A_TIMES_SCORE = c120s01r.getNoscrItemP25();
			}else if (Util.equals(ClsScoreUtil.V4_0_CARD_LOAN,
					c120s01r.getVarVer())) { // J-111-0373 消金卡友貸4.0
				P1_O_COUNTS = c120s01r.getItemP01();
				P1_SCORE = c120s01r.getScrP01();

				LOAN_BAL_S_BYID = c120s01r.getLoanBalSByid();
				LOAN_BAL_S_BYIDSCORE = c120s01r.getScrLoanBalSByid();

				R01_CC12_MAX_REVOL_RATE = c120s01r.getItemMaxR01();
				R01_CC12_MAX_REVOL_RATE_SCORE = c120s01r.getScrMaxR01();

				EDUCATION = Util.trim(c120s01r.getEducation());
				EDUCATION_N_SCORE = c120s01r.getScreducation();

				DRATE_SCORE = c120s01r.getNoscrItemDrate();
			}
		} else {
			return data;
		}

		String CUST_KEY = Util.trim(l120m01a.getCustId());

		// dDOCSTATUS 為上傳DW時註記為刪除案件
		String DOCSTATUS = "";
		if ("".equals(dDOCSTATUS)) {
			DOCSTATUS = clsService._dwDOCSTATUS(Util.trim(l120m01a.getDocStatus()));
		} else {
			DOCSTATUS = dDOCSTATUS;
		}

		DW_RKSCORE dw_rkscore = new DW_RKSCORE();
		dw_rkscore.setBr_cd(BR_CD);
		dw_rkscore.setNoteid(NOTEID);
		dw_rkscore.setCustid(CUSTID);
		dw_rkscore.setDupno(DUPNO);
		dw_rkscore.setAcct_key(ACCT_KEY);
		dw_rkscore.setMowtype(mowtype);
		dw_rkscore.setMowver1(MOWVER1);
		dw_rkscore.setMowver2(MOWVER2);
		dw_rkscore.setJcic_date(jcicDate);
		dw_rkscore.setCust_key(CUST_KEY);
		dw_rkscore.setLngeflag(LNGEFLAG);
		// dw_rkscore.setCc_revol_permit_limit(CC_REVOL_PERMIT_LIMIT);
		dw_rkscore.setR10_revol_rate(R10_REVOL_RATE);
		dw_rkscore.setR10_revol_rate_score(R10_REVOL_RATE_SCORE);
		dw_rkscore.setD07_ln_nos_tamt(D07_LN_NOS_TAMT);
		dw_rkscore.setD07_ln_nos_tamt_score(D07_LN_NOS_TAMT_SCORE);
		dw_rkscore.setD15_cc6_avg_rc(D15_CC6_AVG_RC);
		dw_rkscore.setD15_cc6_avg_rc_score(D15_CC6_AVG_RC_SCORE);
		dw_rkscore.setMarriage(clsService.parseIntColumn(MARRIAGE));
		dw_rkscore.setChildren(CHILDREN);
		dw_rkscore.setOccupation_1(clsService.parseIntColumn(OCCUPATION_1));
		dw_rkscore.setOccupation_1_score(OCCUPATION_1_SCORE);
		dw_rkscore.setN06_inq12_napp_bank(N06_INQ12_NAPP_BANK);
		dw_rkscore.setN06_inq12_napp_bank_score(N06_INQ12_NAPP_BANK_SCORE);
		dw_rkscore.setRating_date(RATING_DATE);
		dw_rkscore.setDocstatus(DOCSTATUS);
		dw_rkscore.setData_src_dt(CapDate.getCurrentTimestamp());
		dw_rkscore.setHincome_reg(HINCOME_REG);
		dw_rkscore.setHincome_reg_score(HINCOME_REG_SCORE);
		dw_rkscore
				.setP69_cc12_delay_rc_times(clsService.parseIntColumn(P69_CC12_DELAY_RC_TIMES));
		dw_rkscore
				.setP25_cc6_pcode_a_times(clsService.parseIntColumn(P25_CC6_PCODE_A_TIMES));
		dw_rkscore.setP25_cc6_pcode_a_times_score(P25_CC6_PCODE_A_TIMES_SCORE);
		dw_rkscore.setMarr_edu_score(MARR_EDU_SCORE);
		dw_rkscore.setP69_p19_score(P69_P19_SCORE);
		dw_rkscore.setYpay(YPAY);
		dw_rkscore.setYpay_score(YPAY_SCORE);
		dw_rkscore.setSeniority(SENIORITY);
		dw_rkscore.setSeniority_score(SENIORITY_SCORE);
		dw_rkscore.setD63_ln_nos_bank(D63_LN_NOS_BANK);
		dw_rkscore.setD63_score(D63_SCORE);
		dw_rkscore.setA21_cc6_rc_use_month(A21_CC6_RC_USE_MONTH);
		dw_rkscore.setA21_score(A21_SCORE);
		dw_rkscore.setA11_cc6_rc_use_bank(A11_CC6_RC_USE_BANK);
		dw_rkscore.setA11_score(A11_SCORE);
		dw_rkscore.setD53_ln_6_times_flag(D53_LN_6_TIMES_FLAG);
		dw_rkscore.setD53_score(D53_SCORE);
		dw_rkscore.setPincome(PINCOME);
		dw_rkscore.setPincome_score(PINCOME_SCORE);
		dw_rkscore.setP68_p19_score(P68_P19_SCORE);
		dw_rkscore.setD07_div_pincome(D07_DIV_PINCOME);
		dw_rkscore.setD07_div_pincome_score(D07_DIV_PINCOME_SCORE);
		dw_rkscore.setZ03_acc_debt_enterprise(Z03_ACC_DEBT_ENTERPRISE);
		dw_rkscore.setZ03_score(Z03_SCORE);
		dw_rkscore.setC_flag(c_flag);
		dw_rkscore.setDrate_score(DRATE_SCORE);
		// ---------消金房貸2.1-------------
		dw_rkscore.setN22_inq12_by30d(N22_INQ12_BY30D);
		dw_rkscore.setN22_score(N22_SCORE);
		// ---------消金房貸3.0-------------(有些欄位2.0也有用)
		dw_rkscore.setN18_inq12_by30d(N18_INQ12_BY30D);
		dw_rkscore.setN18_score(N18_SCORE);
		dw_rkscore.setDesignation(DESIGNATION);
		dw_rkscore.setDesignation_score(DESIGNATION_SCORE);
		dw_rkscore.setD42_avg_limit(D42_AVG_LIMIT);
		dw_rkscore.setD42_score(D42_SCORE);
		dw_rkscore.setEducation(clsService.parseIntColumn(EDUCATION));
		dw_rkscore.setEducation_n_score(EDUCATION_N_SCORE);
		dw_rkscore.setP68_cc6_delay_rc_times(P68_CC6_DELAY_RC_TIMES);
		dw_rkscore.setP68_score(P68_SCORE);
		dw_rkscore.setYrate_score(YRATE_SCORE);
		dw_rkscore.setN01_inq3_total(N01_INQ3_TOTAL);
		dw_rkscore.setN01_score(N01_SCORE);
		dw_rkscore
				.setP19_cc12_pcode_a_times(clsService.parseIntColumn(P19_CC12_PCODE_A_TIMES));
		dw_rkscore
				.setP19_cc12_pcode_a_times_score(P19_CC12_PCODE_A_TIMES_SCORE);
		dw_rkscore.setR01_cc12_revol_rate(R01_CC12_REVOL_RATE);
		dw_rkscore.setR01_cc12_revol_rate_score(R01_CC12_REVOL_RATE_SCORE);
		
		// ---------消金非房貸4.0-------------
		dw_rkscore.setP1_o_counts(P1_O_COUNTS);
		dw_rkscore.setP1_score(P1_SCORE);
		dw_rkscore.setLoan_bal_s_byid(LOAN_BAL_S_BYID);
		dw_rkscore.setLoan_bal_s_byidscore(LOAN_BAL_S_BYIDSCORE);
		dw_rkscore.setR01_cc12_max_revol_rate(R01_CC12_MAX_REVOL_RATE);
		dw_rkscore.setR01_cc12_max_revol_rate_score(R01_CC12_MAX_REVOL_RATE_SCORE);
				
		data.add(dw_rkscore);
		return data;
	}
	

	/**
	 * DW_RKCREDIT個人信用評分評等紀錄 List
	 * 
	 */
	@Override
	public List<DW_RKCREDIT> upDW_RKCREDIT(List<DW_RKCREDIT> data,
			L120M01A l120m01a, L140M01A l140m01a, String ACCT_KEY,
			String modelKind, String LNGEFLAG, C120M01A c120m01a,
			C120S01G c120s01g, String custId, String dupNo,
			String L140CustIdDupNo, String reason, String reasonOth,
			C120S01Q c120s01q, C120S01R c120s01r, String dDOCSTATUS,
			C120S01E c120s01e, boolean isShortPeriodCase) {
		String BR_CD = Util.trim(l140m01a.getOwnBrId());
		String NOTEID = Util.trim(l140m01a.getMainId());
		String CUSTID = Util.trim(c120m01a.getCustId());
		String DUPNO = Util.trim(c120m01a.getDupNo());

		Integer MOWVER1 = new Integer(0);
		Integer MOWVER2 = new Integer(0);

		// J-102-0196區分房貸(C120S01G)及非房貸(C120S01Q)信用模型
		String mowtype = "";
		Date jcicDate = null;

		BigDecimal BASE_A = null;
		BigDecimal BASE_B = null;
		BigDecimal BASE_S = null;
		BigDecimal BASE_SCORE = null;
		BigDecimal TOTAL_SCORE = null;
		BigDecimal INITIAL_SCORE = null;
		BigDecimal PREDICT_BAD_RATE = null;
		Integer INITIAL_RATING = null;
		Integer ADJ_RATING = null;
		Integer FINAL_RATING = null;
		String JCIC_WARNING_FLAG = null;
		BigDecimal DR = null;
		BigDecimal DR_1YR = null;
		String c_flag = "";
		Integer spr = null;
		Integer j10_score = null;
		String j10_reason_code1 = "";
		String j10_reason_code2 = "";
		String j10_reason_code3 = "";
		String j10_reason_code4 = "";
		Integer jr_autodg = null;
		BigDecimal SLOPE = null;
		BigDecimal INTERCEPT = null;
		String PRODID = "";

		if (Util.equals(UtilConstants.DEFAULT.是, c120m01a.getModelTyp())
				|| Util.equals(UtilConstants.L140S02AModelKind.房貸, modelKind)) {
			// 新版模型上線前舊案 || 新模型分類為房貸
			if (!Util.isEmpty(c120s01g.getVarVer())) {
				String VarVer[] = c120s01g.getVarVer().split("\\.");
				MOWVER1 = Util.parseInt(VarVer[0]);
				MOWVER2 = Util.parseInt(VarVer[1]);
			}
			jcicDate = c120s01g.getJcicQDate();
			mowtype = "M";

			BASE_A = c120s01g.getVarA();
			BASE_B = c120s01g.getVarB();
			BASE_S = c120s01g.getVarC();
			BASE_SCORE = c120s01g.getScrNum12();
			TOTAL_SCORE = c120s01g.getScrNum11();
			INITIAL_SCORE = c120s01g.getScrNum13();
			PREDICT_BAD_RATE = c120s01g.getPd();
			INITIAL_RATING = Util.parseInt(c120s01g.getGrade1());
			FINAL_RATING = Util.parseInt(c120s01g.getGrade3());
			JCIC_WARNING_FLAG = "N";
			if (clsService.has_jcic_warning_flag((c120s01g))) {
				JCIC_WARNING_FLAG = "Y";
			}
			DR = c120s01g.getDr_3YR();
			DR_1YR = c120s01g.getDr_1YR();
			// -----消金房貸3.0-----
			SLOPE = c120s01g.getSlope();
			INTERCEPT = c120s01g.getInterCept();
			PRODID = c120s01e == null ? PRODID : c120s01e.getProdId();
			// --------------------

		} else if (Util.equals(UtilConstants.L140S02AModelKind.非房貸, modelKind)) {
			if (c120s01q == null) {
				// 刪除舊案,可能c120s01q不存在
				return data;
			}
			if (!Util.isEmpty(c120s01q.getVarVer())) {
				String VarVer[] = c120s01q.getVarVer().split("\\.");
				MOWVER1 = Util.parseInt(VarVer[0]);
				MOWVER2 = Util.parseInt(VarVer[1]);
			}
			jcicDate = c120s01q.getJcicQDate();
			mowtype = "N";
			c_flag = ClsScoreUtil.upDW_column_C_FLAG(c120s01q.getVarVer());

			BASE_A = c120s01q.getVarA();
			BASE_B = c120s01q.getVarB();
			BASE_S = c120s01q.getVarC();
			BASE_SCORE = c120s01q.getScrNum12();
			TOTAL_SCORE = c120s01q.getScrNum11();
			INITIAL_SCORE = c120s01q.getScrNum13();
			PREDICT_BAD_RATE = c120s01q.getPd();
			INITIAL_RATING = Util.parseInt(c120s01q.getGrade1());
			FINAL_RATING = Util.parseInt(c120s01q.getGrade3());
			JCIC_WARNING_FLAG = "N";
			if (clsService.has_jcic_warning_flag(c120s01q)) {
				JCIC_WARNING_FLAG = "Y";
			}

			DR = isShortPeriodCase ? c120s01q.getDr_2YR() : c120s01q
					.getDr_3YR();
			DR_1YR = isShortPeriodCase ? c120s01q.getDr_1YR_S() : c120s01q
					.getDr_1YR_L();
			// -----非消金房貸4.0-----
			SLOPE = c120s01q.getSlope();
			INTERCEPT = c120s01q.getInterCept();
			PRODID = c120s01e == null ? PRODID : c120s01e.getProdId();
			// ----------------------

		} else if (Util.equals(UtilConstants.L140S02AModelKind.卡友貸, modelKind)) {
			if (!Util.isEmpty(c120s01r.getVarVer())) {
				String VarVer[] = c120s01r.getVarVer().split("\\.");
				MOWVER1 = Util.parseInt(VarVer[0]);
				MOWVER2 = Util.parseInt(VarVer[1]);
			}
			jcicDate = c120s01r.getJcicQDate();
			mowtype = ClsScoreUtil.upDW_column_MowType(c120s01r);
			c_flag = "Y";

			BASE_A = c120s01r.getVarA();
			BASE_B = c120s01r.getVarB();
			BASE_S = c120s01r.getVarC();
			BASE_SCORE = c120s01r.getScrNum12();
			TOTAL_SCORE = c120s01r.getScrNum11();
			INITIAL_SCORE = c120s01r.getScrNum13();
			PREDICT_BAD_RATE = c120s01r.getPd();
			INITIAL_RATING = Util.parseInt(c120s01r.getGrade1());
			FINAL_RATING = Util.parseInt(c120s01r.getGrade3());
			JCIC_WARNING_FLAG = "N";
			if (clsService.has_jcic_warning_flag(c120s01r)) {
				JCIC_WARNING_FLAG = "Y";
			}

			DR = isShortPeriodCase ? c120s01r.getDr_2YR() : c120s01r
					.getDr_3YR();
			DR_1YR = isShortPeriodCase ? c120s01r.getDr_1YR_S() : c120s01r
					.getDr_1YR_L();
			
			SLOPE = c120s01r.getSlope();
			INTERCEPT = c120s01r.getInterCept();
			PRODID = c120s01e == null ? PRODID : c120s01e.getProdId();
			// === J10相關
			String sprRating = Util.trim(c120s01r.getSprtRating());
			spr = Util.isEmpty(sprRating) ? null : Util.parseInt(sprRating);
			j10_score = c120s01r.getJ10_score();
			j10_reason_code1 = Util.trim(c120s01r.getKcs003_reason_code1());
			j10_reason_code2 = Util.trim(c120s01r.getKcs003_reason_code2());
			j10_reason_code3 = Util.trim(c120s01r.getKcs003_reason_code3());
			j10_reason_code4 = Util.trim(c120s01r.getKcs003_reason_code4());
			jr_autodg = Util.parseInt(c120s01r.getAdj_j10());
		} else {
			return data;
		}

		ADJ_RATING = INITIAL_RATING - FINAL_RATING;
		if (Util.equals(UtilConstants.L140S02AModelKind.卡友貸, modelKind)) {
			ADJ_RATING = Util.parseInt(spr) - FINAL_RATING;
		}

		String CUST_KEY = Util.trim(l120m01a.getCustId());

		// dDOCSTATUS 為上傳DW時註記為刪除案件
		String DOCSTATUS = "";
		if ("".equals(dDOCSTATUS)) {
			DOCSTATUS = clsService._dwDOCSTATUS(Util.trim(l120m01a.getDocStatus()));
		} else {
			DOCSTATUS = dDOCSTATUS;
		}

		String FINAL_RATING_FLAG = "N";
		if (Util.equals(Util.trim(custId) + Util.trim(dupNo), L140CustIdDupNo)) {
			FINAL_RATING_FLAG = "Y";
		}

		DW_RKCREDIT dw_rkcredit = new DW_RKCREDIT();
		dw_rkcredit.setBr_cd(BR_CD);
		dw_rkcredit.setNoteid(NOTEID);
		dw_rkcredit.setCustid(CUSTID);
		dw_rkcredit.setDupno(DUPNO);
		dw_rkcredit.setAcct_key(ACCT_KEY);
		dw_rkcredit.setMowtype(mowtype);
		dw_rkcredit.setMowver1(MOWVER1);
		dw_rkcredit.setMowver2(MOWVER2);
		dw_rkcredit.setJcic_date(jcicDate);
		dw_rkcredit.setCust_key(CUST_KEY);
		dw_rkcredit.setLngeflag(LNGEFLAG);
		dw_rkcredit.setBase_a(BASE_A);
		dw_rkcredit.setBase_b(BASE_B);
		dw_rkcredit.setBase_s(BASE_S);
		dw_rkcredit.setBase_score(BASE_SCORE);
		dw_rkcredit.setTotal_score(TOTAL_SCORE);
		dw_rkcredit.setInitial_score(INITIAL_SCORE);
		dw_rkcredit.setPredict_bad_rate(PREDICT_BAD_RATE);
		dw_rkcredit.setInitial_rating(INITIAL_RATING);
		dw_rkcredit.setAdj_rating(ADJ_RATING);
		dw_rkcredit.setFinal_rating(FINAL_RATING);
		dw_rkcredit.setJcic_warning_flag(JCIC_WARNING_FLAG);
		dw_rkcredit.setFinal_rating_flag(FINAL_RATING_FLAG);
		dw_rkcredit.setDocstatus(DOCSTATUS);
		dw_rkcredit.setDelete_reason(Util.truncateString(reason, 2));
		dw_rkcredit.setReject_othereason_text(Util.trimSizeInOS390(reasonOth,
				200));
		dw_rkcredit.setData_src_dt(CapDate.getCurrentTimestamp());
		dw_rkcredit.setDr(DR);
		dw_rkcredit.setDr_1yr(DR_1YR);
		dw_rkcredit.setC_flag(c_flag);
		dw_rkcredit.setSpr(spr);
		dw_rkcredit.setJ10_score(j10_score);
		dw_rkcredit.setJ10_reason_code1(j10_reason_code1);
		dw_rkcredit.setJ10_reason_code2(j10_reason_code2);
		dw_rkcredit.setJ10_reason_code3(j10_reason_code3);
		dw_rkcredit.setJ10_reason_code4(j10_reason_code4);
		dw_rkcredit.setJr_autodg(jr_autodg);
		dw_rkcredit.setSlope(SLOPE);
		dw_rkcredit.setIntercept(INTERCEPT);
		dw_rkcredit.setProdId(PRODID);

		data.add(dw_rkcredit);
		return data;
	}
	
	/**
	 * DW_RKJCIC聯徵特殊負面資訊List
	 * 
	 */
	@Override
	public List<DW_RKJCIC> upDW_RKJCIC(List<DW_RKJCIC> data, L120M01A l120m01a,
			L140M01A l140m01a, String ACCT_KEY, String modelKind,
			String LNGEFLAG, C120M01A c120m01a, C120S01G c120s01g,
			C120S01Q c120s01q, C120S01R c120s01r, String dDOCSTATUS) {
		C120S01E c120s01e = c120s01eDao
				.findByUniqueKey(Util.trim(c120m01a.getMainId()),
						Util.trim(c120m01a.getCustId()),
						Util.trim(c120m01a.getDupNo()));
		String BR_CD = Util.trim(l140m01a.getOwnBrId());
		String NOTEID = Util.trim(l140m01a.getMainId());
		String CUSTID = Util.trim(c120m01a.getCustId());
		String DUPNO = Util.trim(c120m01a.getDupNo());
		Integer MOWVER1 = new Integer(0);
		Integer MOWVER2 = new Integer(0);

		// J-102-0196區分房貸(C120S01G)及非房貸(C120S01Q)信用模型
		String mowtype = "";
		Date jcicDate = null;

		Date CHECK_QDATE = null;
		String EVER_BAD_CHECK = null;
		String REJECT_YN = null;
		String CREDIT_FORCE_STOP = null;
		String BAD_DEBT = null;
		String LOAN_PASTDUE_YN = null;
		String NEGO_LAW = null;
		String NEGO_BANK = null;
		String OTHER_WARNING = null;
		String LN12_PAY_DELAY_TIMES = null;
		String CC12_REVOL_PAY_DELAY_TIMES = null;
		String CC12_MINPAY_DELAY_TIMES = null;
		String CC12_TOTPAY_DELAY_TIMES = null;
		String CC12_CASH_ADV_TIMES = null;
		String LN12_CASH_TIMES = null;
		String c_flag = "";

		if (Util.equals(UtilConstants.DEFAULT.是, c120m01a.getModelTyp())
				|| Util.equals(UtilConstants.L140S02AModelKind.房貸, modelKind)) {
			// 新版模型上線前舊案 || 新模型分類為房貸
			if (!Util.isEmpty(c120s01g.getVarVer())) {
				String VarVer[] = c120s01g.getVarVer().split("\\.");
				MOWVER1 = Util.parseInt(VarVer[0]);
				MOWVER2 = Util.parseInt(VarVer[1]);
			}
			jcicDate = c120s01g.getJcicQDate();
			mowtype = "M";

			CHECK_QDATE = c120s01g.getEtchQDate();
			EVER_BAD_CHECK = Util.trim(c120s01g.getChkItem1a());
			REJECT_YN = Util.trim(c120s01g.getChkItem1b());
			CREDIT_FORCE_STOP = Util.trim(c120s01g.getChkItem1c());
			BAD_DEBT = Util.trim(c120s01g.getChkItem1d());
			LOAN_PASTDUE_YN = Util.trim(c120s01g.getChkItem1e());
			NEGO_LAW = Util.trim(c120s01g.getChkItem2a());
			NEGO_BANK = Util.trim(c120s01g.getChkItem2b());
			OTHER_WARNING = Util.trim(c120s01g.getChkItem2c());
			LN12_PAY_DELAY_TIMES = Util.trim(c120s01g.getChkItem3());
			CC12_REVOL_PAY_DELAY_TIMES = Util.trim(c120s01g.getChkItem4());
			CC12_MINPAY_DELAY_TIMES = Util.trim(c120s01g.getChkItem5());
			CC12_TOTPAY_DELAY_TIMES = Util.trim(c120s01g.getChkItem6());
			CC12_CASH_ADV_TIMES = Util.trim(c120s01g.getChkItem7());
			LN12_CASH_TIMES = Util.trim(c120s01g.getChkItem8());

		} else if (Util.equals(UtilConstants.L140S02AModelKind.非房貸, modelKind)) {
			if (!Util.isEmpty(c120s01q.getVarVer())) {
				String VarVer[] = c120s01q.getVarVer().split("\\.");
				MOWVER1 = Util.parseInt(VarVer[0]);
				MOWVER2 = Util.parseInt(VarVer[1]);
			}
			jcicDate = c120s01q.getJcicQDate();
			mowtype = "N";
			c_flag = ClsScoreUtil.upDW_column_C_FLAG(c120s01q.getVarVer());

			CHECK_QDATE = c120s01q.getEtchQDate();
			EVER_BAD_CHECK = Util.trim(c120s01q.getChkItem1a());
			REJECT_YN = Util.trim(c120s01q.getChkItem1b());
			CREDIT_FORCE_STOP = Util.trim(c120s01q.getChkItem1c());
			BAD_DEBT = Util.trim(c120s01q.getChkItem1d());
			LOAN_PASTDUE_YN = Util.trim(c120s01q.getChkItem1e());
			NEGO_LAW = Util.trim(c120s01q.getChkItem2a());
			NEGO_BANK = Util.trim(c120s01q.getChkItem2b());
			OTHER_WARNING = Util.trim(c120s01q.getChkItem2c());
			LN12_PAY_DELAY_TIMES = Util.trim(c120s01q.getChkItem3());
			CC12_REVOL_PAY_DELAY_TIMES = Util.trim(c120s01q.getChkItem4());
			CC12_MINPAY_DELAY_TIMES = Util.trim(c120s01q.getChkItem5());
			CC12_TOTPAY_DELAY_TIMES = Util.trim(c120s01q.getChkItem6());
			CC12_CASH_ADV_TIMES = Util.trim(c120s01q.getChkItem7());
			LN12_CASH_TIMES = Util.trim(c120s01q.getChkItem8());
		} else if (Util.equals(UtilConstants.L140S02AModelKind.卡友貸, modelKind)) {
			if (!Util.isEmpty(c120s01r.getVarVer())) {
				String VarVer[] = c120s01r.getVarVer().split("\\.");
				MOWVER1 = Util.parseInt(VarVer[0]);
				MOWVER2 = Util.parseInt(VarVer[1]);
			}
			jcicDate = c120s01r.getJcicQDate();
			mowtype = ClsScoreUtil.upDW_column_MowType(c120s01r);
			c_flag = "Y";

			CHECK_QDATE = c120s01r.getEtchQDate();
			EVER_BAD_CHECK = Util.trim(c120s01r.getChkItem1a());
			REJECT_YN = Util.trim(c120s01r.getChkItem1b());
			CREDIT_FORCE_STOP = Util.trim(c120s01r.getChkItem1c());
			BAD_DEBT = Util.trim(c120s01r.getChkItem1d());
			LOAN_PASTDUE_YN = Util.trim(c120s01r.getChkItem1e());
			NEGO_LAW = Util.trim(c120s01r.getChkItem2a());
			NEGO_BANK = Util.trim(c120s01r.getChkItem2b());
			OTHER_WARNING = Util.trim(c120s01r.getChkItem2c());
			LN12_PAY_DELAY_TIMES = Util.trim(c120s01r.getChkItem3());
			CC12_REVOL_PAY_DELAY_TIMES = Util.trim(c120s01r.getChkItem4());
			CC12_MINPAY_DELAY_TIMES = Util.trim(c120s01r.getChkItem5());
			CC12_TOTPAY_DELAY_TIMES = Util.trim(c120s01r.getChkItem6());
			CC12_CASH_ADV_TIMES = Util.trim(c120s01r.getChkItem7());
			LN12_CASH_TIMES = Util.trim(c120s01r.getChkItem8());
		} else {
			return data;
		}

		String CUST_KEY = Util.trim(l120m01a.getCustId());

		Date END_DATE = new Date();
		if (!Util.isEmpty(c120s01e)) {
			END_DATE = c120s01e.getEChkDDate();
		}

		// dDOCSTATUS 為上傳DW時註記為刪除案件
		String DOCSTATUS = "";
		if ("".equals(dDOCSTATUS)) {
			DOCSTATUS = clsService._dwDOCSTATUS(Util.trim(l120m01a.getDocStatus()));
		} else {
			DOCSTATUS = dDOCSTATUS;
		}

		DW_RKJCIC dw_rkjcic = new DW_RKJCIC();
		dw_rkjcic.setBr_cd(BR_CD);
		dw_rkjcic.setNoteid(NOTEID);
		dw_rkjcic.setCustid(CUSTID);
		dw_rkjcic.setDupno(DUPNO);
		dw_rkjcic.setAcct_key(ACCT_KEY);
		dw_rkjcic.setMowtype(mowtype);
		dw_rkjcic.setMowver1(MOWVER1);
		dw_rkjcic.setMowver2(MOWVER2);
		dw_rkjcic.setJcic_date(jcicDate);
		dw_rkjcic.setCust_key(CUST_KEY);
		dw_rkjcic.setLngeflag(LNGEFLAG);

		dw_rkjcic.setCheck_qdate(CHECK_QDATE);
		dw_rkjcic.setEnd_date(END_DATE);

		dw_rkjcic.setLn12_pay_delay_times(LN12_PAY_DELAY_TIMES);
		dw_rkjcic.setCc12_revol_pay_delay_times(CC12_REVOL_PAY_DELAY_TIMES);
		dw_rkjcic.setCc12_minpay_delay_times(CC12_MINPAY_DELAY_TIMES);
		dw_rkjcic.setCc12_totpay_delay_times(CC12_TOTPAY_DELAY_TIMES);
		dw_rkjcic.setCc12_cash_adv_times(CC12_CASH_ADV_TIMES);
		dw_rkjcic.setLn12_cash_times(LN12_CASH_TIMES);
		dw_rkjcic.setDocstatus(DOCSTATUS);
		dw_rkjcic.setData_src_dt(CapDate.getCurrentTimestamp());

		dw_rkjcic.setEver_bad_check(EVER_BAD_CHECK);
		dw_rkjcic.setReject_yn(REJECT_YN);
		dw_rkjcic.setCredit_force_stop(CREDIT_FORCE_STOP);
		dw_rkjcic.setBad_debt(BAD_DEBT);
		dw_rkjcic.setLoan_pastdue_yn(LOAN_PASTDUE_YN);
		dw_rkjcic.setNego_law(NEGO_LAW);
		dw_rkjcic.setNego_bank(NEGO_BANK);
		dw_rkjcic.setOther_warning(OTHER_WARNING);
		dw_rkjcic.setC_flag(c_flag);

		data.add(dw_rkjcic);
		return data;
	}

	/**
	 * DW_RKPROJECT房貸敘做方案 List
	 * 
	 */
	@Override
	public List<DW_RKPROJECT> upDW_RKPROJECT(List<DW_RKPROJECT> data,
			L120M01A l120m01a, L140M01A l140m01a, String ACCT_KEY,
			String modelKind, String LNGEFLAG, C120M01A c120m01a,
			C120S01G c120s01g, C120S01Q c120s01q, C120S01R c120s01r,
			String dDOCSTATUS, String finalGrade) {
		L140M02A l140m02a = l140m02aDao.findByUniqueKey(l140m01a.getMainId());
		if (l140m02a == null) {
			l140m02a = new L140M02A();
		}
		String UPDATE_AMT_FLAG = "N";
		String UPDATE_RATE_FLAG = "N";
		String[] temps = Util.trim(l140m02a.getJsonData()).split(
				UtilConstants.Mark.SPILT_MARK);
		for (String name : temps) {
			if (UtilConstants.L140M02AName.現請額度.equals(name)) {
				UPDATE_AMT_FLAG = "Y";
			}
			if (UtilConstants.L140M02AName.利率條件.equals(name)) {
				UPDATE_RATE_FLAG = "Y";
			}
		}
		String BR_CD = Util.trim(l140m01a.getOwnBrId());
		String NOTEID = Util.trim(l140m01a.getMainId());
		String CUSTID = Util.trim(c120m01a.getCustId());
		String DUPNO = Util.trim(c120m01a.getDupNo());
		Integer MOWVER1 = new Integer(0);
		Integer MOWVER2 = new Integer(0);

		// J-102-0196區分房貸(C120S01G)及非房貸(C120S01Q)信用模型
		String mowtype = "";
		Date jcicDate = null;
		String c_flag = "";

		if (Util.equals(UtilConstants.DEFAULT.是, c120m01a.getModelTyp())
				|| Util.equals(UtilConstants.L140S02AModelKind.房貸, modelKind)) {
			// 新版模型上線前舊案 || 新模型分類為房貸
			if (!Util.isEmpty(c120s01g.getVarVer())) {
				String VarVer[] = c120s01g.getVarVer().split("\\.");
				MOWVER1 = Util.parseInt(VarVer[0]);
				MOWVER2 = Util.parseInt(VarVer[1]);
			}
			jcicDate = c120s01g.getJcicQDate();
			mowtype = "M";
		} else if (Util.equals(UtilConstants.L140S02AModelKind.非房貸, modelKind)) {
			if (!Util.isEmpty(c120s01q.getVarVer())) {
				String VarVer[] = c120s01q.getVarVer().split("\\.");
				MOWVER1 = Util.parseInt(VarVer[0]);
				MOWVER2 = Util.parseInt(VarVer[1]);
			}
			jcicDate = c120s01q.getJcicQDate();
			mowtype = "N";
			c_flag = ClsScoreUtil.upDW_column_C_FLAG(c120s01q.getVarVer());
		} else if (Util.equals(UtilConstants.L140S02AModelKind.卡友貸, modelKind)) {
			if (!Util.isEmpty(c120s01r.getVarVer())) {
				String VarVer[] = c120s01r.getVarVer().split("\\.");
				MOWVER1 = Util.parseInt(VarVer[0]);
				MOWVER2 = Util.parseInt(VarVer[1]);
			}
			jcicDate = c120s01r.getJcicQDate();
			mowtype = ClsScoreUtil.upDW_column_MowType(c120s01r);
			c_flag = "Y";
		} else {
			return data;
		}

		String CUST_KEY = Util.trim(l120m01a.getCustId());
		String CASE_LEVEL = Util.trim(l120m01a.getCaseLvl());
		String REASON_FLAG = Util.trim(l120m01a.getCaseLvlReason());
		if (Util.equals(REASON_FLAG, "7")) {
			REASON_FLAG = "6";
		}
		String AUTH_FLAG = (Util.equals(Util.trim(l120m01a.getDocRslt()), "1") ? "Y"
				: "N");
		String CONTINUE_CD = LMSUtil.getFinalGradeUpUse(modelKind, finalGrade);
		String REJECT_FLAG = Util.trim(l140m01a.getCesRjtCause());
		Date CHKDATE = l120m01a.getEndDate();

		// dDOCSTATUS 為上傳DW時註記為刪除案件
		String DOCSTATUS = "";
		if ("".equals(dDOCSTATUS)) {
			DOCSTATUS = clsService._dwDOCSTATUS(Util.trim(l120m01a.getDocStatus()));
		} else {
			DOCSTATUS = dDOCSTATUS;
		}

		DW_RKPROJECT dw_rkproject = new DW_RKPROJECT();
		dw_rkproject.setBr_cd(BR_CD);
		dw_rkproject.setNoteid(NOTEID);
		dw_rkproject.setCustid(CUSTID);
		dw_rkproject.setDupno(DUPNO);
		dw_rkproject.setAcct_key(ACCT_KEY);
		dw_rkproject.setMowtype(mowtype);
		dw_rkproject.setMowver1(MOWVER1);
		dw_rkproject.setMowver2(MOWVER2);
		dw_rkproject.setJcic_date(jcicDate);
		dw_rkproject.setCust_key(CUST_KEY);
		dw_rkproject.setLngeflag(LNGEFLAG);
		dw_rkproject.setContinue_cd(CONTINUE_CD);
		dw_rkproject.setCase_level(CASE_LEVEL);
		dw_rkproject.setReason_flag(REASON_FLAG);
		dw_rkproject.setAuth_flag(AUTH_FLAG);
		dw_rkproject.setReject_flag(REJECT_FLAG);
		dw_rkproject.setUpdate_amt_flag(UPDATE_AMT_FLAG);
		dw_rkproject.setUpdate_rate_flag(UPDATE_RATE_FLAG);
		dw_rkproject.setChkdate(CHKDATE);
		dw_rkproject.setDocstatus(DOCSTATUS);
		dw_rkproject.setData_src_dt(CapDate.getCurrentTimestamp());
		dw_rkproject.setC_flag(c_flag);

		data.add(dw_rkproject);
		return data;
	}

	/**
	 * DW_RKADJUST個人信用評等調整 List
	 * 
	 */
	@Override
	public List<DW_RKADJUST> upDW_RKADJUST(List<DW_RKADJUST> data,
			L120M01A l120m01a, L140M01A l140m01a, String ACCT_KEY,
			String modelKind, String LNGEFLAG, C120M01A c120m01a,
			C120S01G c120s01g, C120S01Q c120s01q, C120S01R c120s01r,
			String dDOCSTATUS) {
		String BR_CD = Util.trim(l140m01a.getOwnBrId());
		String NOTEID = Util.trim(l140m01a.getMainId());
		String CUSTID = Util.trim(c120m01a.getCustId());
		String DUPNO = Util.trim(c120m01a.getDupNo());

		Integer MOWVER1 = new Integer(0);
		Integer MOWVER2 = new Integer(0);

		// J-102-0196區分房貸(C120S01G)及非房貸(C120S01Q)信用模型
		String mowtype = "";
		Date jcicDate = null;
		String upgrade_reason_text = "";

		Integer upgrade_level = new Integer(0);
		String downgrade_reason_text = "";
		Integer downgrade_level = new Integer(0);

		String UPGRADE_REASON_FLAG = "";
		String UPGRADE_REASON_TEXT = "";
		String DOWNGRADE_REASON_TEXT = "";
		String c_flag = "";

		if (Util.equals(UtilConstants.DEFAULT.是, c120m01a.getModelTyp())
				|| Util.equals(UtilConstants.L140S02AModelKind.房貸, modelKind)) {
			// 新版模型上線前舊案 || 新模型分類為房貸
			if (!Util.isEmpty(c120s01g.getVarVer())) {
				String VarVer[] = c120s01g.getVarVer().split("\\.");
				MOWVER1 = Util.parseInt(VarVer[0]);
				MOWVER2 = Util.parseInt(VarVer[1]);
			}
			jcicDate = c120s01g.getJcicQDate();
			mowtype = "M";

			UPGRADE_REASON_FLAG = Util.trim(c120s01g.getAdjustFlag());
			UPGRADE_REASON_TEXT = Util.trim(c120s01g.getAdjustReason());
			DOWNGRADE_REASON_TEXT = Util.trim(c120s01g.getAdjustReason());

			if (Util.equals(c120s01g.getAdjustStatus(), "1")) {
				upgrade_reason_text = UPGRADE_REASON_TEXT;
				upgrade_level = Util.parseInt(c120s01g.getGrade2());
			} else if (Util.equals(c120s01g.getAdjustStatus(), "2")) {
				downgrade_reason_text = DOWNGRADE_REASON_TEXT;
				downgrade_level = Util.parseInt(c120s01g.getGrade2());
			} else {
				upgrade_level = 0;
				downgrade_level = 0;
			}

		} else if (Util.equals(UtilConstants.L140S02AModelKind.非房貸, modelKind)) {
			if (!Util.isEmpty(c120s01q.getVarVer())) {
				String VarVer[] = c120s01q.getVarVer().split("\\.");
				MOWVER1 = Util.parseInt(VarVer[0]);
				MOWVER2 = Util.parseInt(VarVer[1]);
			}
			jcicDate = c120s01q.getJcicQDate();
			mowtype = "N";
			c_flag = ClsScoreUtil.upDW_column_C_FLAG(c120s01q.getVarVer());

			UPGRADE_REASON_FLAG = Util.trim(c120s01q.getAdjustFlag());
			UPGRADE_REASON_TEXT = Util.trim(c120s01q.getAdjustReason());
			DOWNGRADE_REASON_TEXT = Util.trim(c120s01q.getAdjustReason());

			if (Util.equals(c120s01q.getAdjustStatus(), "1")) {
				upgrade_reason_text = UPGRADE_REASON_TEXT;
				upgrade_level = Util.parseInt(c120s01q.getGrade2());
			} else if (Util.equals(c120s01q.getAdjustStatus(), "2")) {
				downgrade_reason_text = DOWNGRADE_REASON_TEXT;
				downgrade_level = Util.parseInt(c120s01q.getGrade2());
			} else {
				upgrade_level = 0;
				downgrade_level = 0;
			}
		} else if (Util.equals(UtilConstants.L140S02AModelKind.卡友貸, modelKind)) {
			if (!Util.isEmpty(c120s01r.getVarVer())) {
				String VarVer[] = c120s01r.getVarVer().split("\\.");
				MOWVER1 = Util.parseInt(VarVer[0]);
				MOWVER2 = Util.parseInt(VarVer[1]);
			}
			jcicDate = c120s01r.getJcicQDate();
			mowtype = ClsScoreUtil.upDW_column_MowType(c120s01r);
			c_flag = "Y";

			UPGRADE_REASON_FLAG = Util.trim(c120s01r.getAdjustFlag());
			UPGRADE_REASON_TEXT = Util.trim(c120s01r.getAdjustReason());
			DOWNGRADE_REASON_TEXT = Util.trim(c120s01r.getAdjustReason());

			if (Util.equals(c120s01r.getAdjustStatus(), "1")) {
				upgrade_reason_text = UPGRADE_REASON_TEXT;
				upgrade_level = Util.parseInt(c120s01r.getGrade2());
			} else if (Util.equals(c120s01r.getAdjustStatus(), "2")) {
				downgrade_reason_text = DOWNGRADE_REASON_TEXT;
				downgrade_level = Util.parseInt(c120s01r.getGrade2());
			} else {
				upgrade_level = 0;
				downgrade_level = 0;
			}
		} else {
			return data;
		}
		String CUST_KEY = Util.trim(l120m01a.getCustId());

		// dDOCSTATUS 為上傳DW時註記為刪除案件
		String DOCSTATUS = "";
		if ("".equals(dDOCSTATUS)) {
			DOCSTATUS = clsService._dwDOCSTATUS(Util.trim(l120m01a.getDocStatus()));
		} else {
			DOCSTATUS = dDOCSTATUS;
		}

		DW_RKADJUST dw_rkadjust = new DW_RKADJUST();
		dw_rkadjust.setBr_cd(BR_CD);
		dw_rkadjust.setNoteid(NOTEID);
		dw_rkadjust.setCustid(CUSTID);
		dw_rkadjust.setDupno(DUPNO);
		dw_rkadjust.setAcct_key(ACCT_KEY);
		dw_rkadjust.setMowtype(mowtype);
		dw_rkadjust.setMowver1(MOWVER1);
		dw_rkadjust.setMowver2(MOWVER2);
		dw_rkadjust.setJcic_date(jcicDate);
		dw_rkadjust.setCust_key(CUST_KEY);
		dw_rkadjust.setLngeflag(LNGEFLAG);
		dw_rkadjust.setUpgrade_reason_flag(UPGRADE_REASON_FLAG);

		dw_rkadjust.setUpgrade_reason_text(upgrade_reason_text);
		dw_rkadjust.setUpgrade_level(upgrade_level);
		dw_rkadjust.setDowngrade_reason_text(downgrade_reason_text);
		dw_rkadjust.setDowngrade_level(downgrade_level);

		dw_rkadjust.setDocstatus(DOCSTATUS);
		dw_rkadjust.setData_src_dt(CapDate.getCurrentTimestamp());
		dw_rkadjust.setC_flag(c_flag);

		data.add(dw_rkadjust);
		return data;
	}

	/**
	 * DW_RKCNTRNO房貸額度序號檔List
	 * 
	 */
	@Override
	public List<DW_RKCNTRNO> upDW_RKCNTRNO(List<DW_RKCNTRNO> data,
			L120M01A l120m01a, L140M01A l140m01a, String ACCT_KEY,
			String modelKind, String LNGEFLAG, C120M01A c120m01a,
			C120S01G c120s01g, C120S01Q c120s01q, C120S01R c120s01r,
			String dDOCSTATUS, String loanCurr, BigDecimal loanAmt,
			String property, Integer lnYear, Integer lnMonth, String prodKind,
			String subjCode, String residential, String cntrNo) {

		String BR_CD = Util.trim(l140m01a.getOwnBrId());
		String NOTEID = Util.trim(l140m01a.getMainId());
		String CUSTID = Util.trim(c120m01a.getCustId());
		String DUPNO = Util.trim(c120m01a.getDupNo());
		Integer MOWVER1 = new Integer(0);
		Integer MOWVER2 = new Integer(0);

		// J-102-0196區分房貸(C120S01G)及非房貸(C120S01Q)信用模型
		String mowtype = "";
		Date jcicDate = null;
		String c_flag = "";

		if (Util.equals(UtilConstants.DEFAULT.是, c120m01a.getModelTyp())
				|| Util.equals(UtilConstants.L140S02AModelKind.房貸, modelKind)) {
			// 新版模型上線前舊案 || 新模型分類為房貸
			if (!Util.isEmpty(c120s01g.getVarVer())) {
				String VarVer[] = c120s01g.getVarVer().split("\\.");
				MOWVER1 = Util.parseInt(VarVer[0]);
				MOWVER2 = Util.parseInt(VarVer[1]);
			}
			jcicDate = c120s01g.getJcicQDate();
			mowtype = "M";
		} else if (Util.equals(UtilConstants.L140S02AModelKind.非房貸, modelKind)) {
			if (!Util.isEmpty(c120s01q.getVarVer())) {
				String VarVer[] = c120s01q.getVarVer().split("\\.");
				MOWVER1 = Util.parseInt(VarVer[0]);
				MOWVER2 = Util.parseInt(VarVer[1]);
			}
			jcicDate = c120s01q.getJcicQDate();
			mowtype = "N";
			c_flag = ClsScoreUtil.upDW_column_C_FLAG(c120s01q.getVarVer());
		} else if (Util.equals(UtilConstants.L140S02AModelKind.卡友貸, modelKind)) {
			if (!Util.isEmpty(c120s01r.getVarVer())) {
				String VarVer[] = c120s01r.getVarVer().split("\\.");
				MOWVER1 = Util.parseInt(VarVer[0]);
				MOWVER2 = Util.parseInt(VarVer[1]);
			}
			jcicDate = c120s01r.getJcicQDate();
			mowtype = ClsScoreUtil.upDW_column_MowType(c120s01r);
			c_flag = "Y";
		} else {
			return data;
		}

		String CUST_KEY = Util.trim(l120m01a.getCustId());

		// dDOCSTATUS 為上傳DW時註記為刪除案件
		String DOCSTATUS = "";
		if ("".equals(dDOCSTATUS)) {
			DOCSTATUS = clsService._dwDOCSTATUS(Util.trim(l120m01a.getDocStatus()));
		} else {
			DOCSTATUS = dDOCSTATUS;
		}

		DW_RKCNTRNO dw_rkcntrno = new DW_RKCNTRNO();
		dw_rkcntrno.setBr_cd(BR_CD);
		dw_rkcntrno.setNoteid(NOTEID);
		dw_rkcntrno.setCustid(CUSTID);
		dw_rkcntrno.setDupno(DUPNO);
		dw_rkcntrno.setAcct_key(ACCT_KEY);
		dw_rkcntrno.setMowtype(mowtype);
		dw_rkcntrno.setMowver1(Util.parseInt(MOWVER1));
		dw_rkcntrno.setMowver2(Util.parseInt(MOWVER2));
		dw_rkcntrno.setJcic_date(jcicDate);
		dw_rkcntrno.setCust_key(CUST_KEY);
		dw_rkcntrno.setLngeflag(LNGEFLAG);
		dw_rkcntrno.setCntrno(cntrNo);
		dw_rkcntrno.setDocstatus(DOCSTATUS);
		dw_rkcntrno.setData_src_dt(CapDate.getCurrentTimestamp());

		dw_rkcntrno.setLoancurr(loanCurr);
		dw_rkcntrno.setLoanamt(loanAmt);
		dw_rkcntrno.setProperty(property);
		dw_rkcntrno.setLnyear(lnYear);
		dw_rkcntrno.setLnmonth(lnMonth);
		dw_rkcntrno.setProdkind(prodKind);
		dw_rkcntrno.setSubjcode(subjCode);
		dw_rkcntrno.setResidential(residential);
		dw_rkcntrno.setC_flag(c_flag);
		dw_rkcntrno
				.setColl_house(Util.equals("Y", l140m01a.getHeadItem7()) ? "Y"
						: "N");
		//J-112-0169 續授審處	J-112-0118程式修改申請書，請協助留存e-Loan額度明細表中"(不動產)核貸成數"，存於DW中
		dw_rkcntrno.setApprovedPercent(l140m01a.getApprovedPercent());
		data.add(dw_rkcntrno);
		return data;
	}

	/**
	 * DW_RKAPPLICANT申請人基本資料List
	 * 
	 */
	@Override
	public List<DW_RKAPPLICANT> upDW_RKAPPLICANT(List<DW_RKAPPLICANT> data,
			L120M01A l120m01a, L140M01A l140m01a, String ACCT_KEY,
			String modelKind, String LNGEFLAG, C120M01A c120m01a,
			C120S01A c120s01a, C120S01B c120s01b, C120S01C c120s01c,
			C120S01G c120s01g, C120S01Q c120s01q, C120S01R c120s01r,
			String dDOCSTATUS) {
		String BR_CD = Util.trim(l140m01a.getOwnBrId());
		String NOTEID = Util.trim(l140m01a.getMainId());
		String CUSTID = Util.trim(c120m01a.getCustId());
		String DUPNO = Util.trim(c120m01a.getDupNo());

		Integer MOWVER1 = new Integer(0);
		Integer MOWVER2 = new Integer(0);

		// J-102-0196區分房貸(C120S01G)及非房貸(C120S01Q)信用模型
		String mowtype = "";
		Date jcicDate = null;
		String c_flag = "";

		if (Util.equals(UtilConstants.DEFAULT.是, c120m01a.getModelTyp())
				|| Util.equals(UtilConstants.L140S02AModelKind.房貸, modelKind)) {
			// 新版模型上線前舊案 || 新模型分類為房貸
			if (!Util.isEmpty(c120s01g.getVarVer())) {
				String VarVer[] = c120s01g.getVarVer().split("\\.");
				MOWVER1 = Util.parseInt(VarVer[0]);
				MOWVER2 = Util.parseInt(VarVer[1]);
			}
			jcicDate = c120s01g.getJcicQDate();
			mowtype = "M";
		} else if (Util.equals(UtilConstants.L140S02AModelKind.非房貸, modelKind)) {
			if (!Util.isEmpty(c120s01q.getVarVer())) {
				String VarVer[] = c120s01q.getVarVer().split("\\.");
				MOWVER1 = Util.parseInt(VarVer[0]);
				MOWVER2 = Util.parseInt(VarVer[1]);
			}
			jcicDate = c120s01q.getJcicQDate();
			mowtype = "N";
			c_flag = ClsScoreUtil.upDW_column_C_FLAG(c120s01q.getVarVer());
		} else if (Util.equals(UtilConstants.L140S02AModelKind.卡友貸, modelKind)) {
			if (!Util.isEmpty(c120s01r.getVarVer())) {
				String VarVer[] = c120s01r.getVarVer().split("\\.");
				MOWVER1 = Util.parseInt(VarVer[0]);
				MOWVER2 = Util.parseInt(VarVer[1]);
			}
			jcicDate = c120s01r.getJcicQDate();
			mowtype = ClsScoreUtil.upDW_column_MowType(c120s01r);
			c_flag = "Y";

		} else {
			return data;
		}

		String CUST_KEY = Util.trim(l120m01a.getCustId());
		Date DOB = c120s01a.getBirthday();

		String MARRIAGE = Util.trim(c120s01a.getMarry());
		Integer CHILDREN = c120s01a.getChild();
		BigDecimal SENIORITY = c120s01b.getSeniority();
		String POS = Util.trim(c120s01b.getJobType1())
				+ Util.trim(c120s01b.getJobType2())
				+ Util.trim(c120s01b.getJobTitle());
		BigDecimal YPAY = c120s01b.getPayAmt();
		String OMONEY = Util.trim(c120s01b.getOthType());
		BigDecimal OMONEY_AMT = c120s01b.getOthAmt();
		BigDecimal HINCOME = null;
		if (c120s01c.getYFamAmt() != null) {
			HINCOME = LMSUtil.getUploadYFamAmt(c120s01c.getYFamAmt());
		}
		BigDecimal FINCOME = c120s01c.getFincome();
		if (FINCOME != null) {
			String maxFINCOME = "9999999999";
			if (CapMath.compare(FINCOME, maxFINCOME) > 0) {
				FINCOME = CapMath.getBigDecimal(maxFINCOME);
			}
		}
		// 2013/07/10,Fantasy,個人負債比率和家庭負債比率 Interger改為BigDecimal
		// Integer DRATE = c120s01c.getDRate();
		// Integer YRATE = c120s01c.getYRate();
		BigDecimal DRATE = c120s01c.getDRate();
		BigDecimal YRATE = c120s01c.getYRate();
		BigDecimal FRATE = c120s01c.getFRate();
		String CERTIFICATE = Util.trim(c120s01b.getInDoc());
		String HCERTIFICATE = Util.trim(c120s01c.getYIncomeCert());
		String LAWADR_ZIP_CD = Util.trim(c120s01a.getFZip());
		String ADR_ZIP_CD = Util.trim(c120s01a.getCoZip());
		String DBCREDIT = Util.trim(c120s01c.getCredit());
		if (Util.equals(DBCREDIT, "A|B") || Util.equals(DBCREDIT, "AB")) {
			DBCREDIT = "C";
		}
		String ISPFund = Util.trim(c120s01c.getIsPeriodFund());
		String OBUSINESS = clsService._convertBusi(c120s01c.getBusi());

		BigDecimal INVMBAL = c120s01c.getInvMBalAmt();
		BigDecimal INVOBAL = c120s01c.getInvOBalAmt();
		BigDecimal ODEP = c120s01c.getBranAmt();
		String CMSSTATUS = Util.trim(c120s01a.getCmsStatus());
		// dDOCSTATUS 為上傳DW時註記為刪除案件
		String DOCSTATUS = "";
		if ("".equals(dDOCSTATUS)) {
			DOCSTATUS = clsService._dwDOCSTATUS(Util.trim(l120m01a.getDocStatus()));
		} else {
			DOCSTATUS = dDOCSTATUS;
		}

		String YPAY_SWFT = Util.trim(c120s01b.getPayCurr());
		String OMONEY_AMT_SWFT = Util.trim(c120s01b.getOthCurr());
		String HINCOME_SWFT = Util.trim(c120s01c.getYFamCurr());
		String INVMBAL_SWFT = Util.trim(c120s01c.getInvMBalCurr());
		String INVOBAL_SWFT = Util.trim(c120s01c.getInvOBalCurr());
		String ODEP_SWFT = Util.trim(c120s01c.getBranCurr());

		DW_RKAPPLICANT dw_rkapplicant = new DW_RKAPPLICANT();
		dw_rkapplicant.setBr_cd(BR_CD);
		dw_rkapplicant.setNoteid(NOTEID);
		dw_rkapplicant.setCustid(CUSTID);
		dw_rkapplicant.setDupno(DUPNO);
		dw_rkapplicant.setAcct_key(ACCT_KEY);
		dw_rkapplicant.setMowtype(mowtype);
		dw_rkapplicant.setMowver1(MOWVER1);
		dw_rkapplicant.setMowver2(MOWVER2);

		dw_rkapplicant.setJcic_date(jcicDate);
		dw_rkapplicant.setCust_key(CUST_KEY);
		dw_rkapplicant.setLngeflag(LNGEFLAG);
		dw_rkapplicant.setDob(DOB);
		dw_rkapplicant.setEducation(clsService.convert_dw_edu(c120s01a));
		dw_rkapplicant.setMarriage(Util.parseInt(MARRIAGE));
		dw_rkapplicant.setChildren(CHILDREN);
		dw_rkapplicant.setSeniority(SENIORITY);
		dw_rkapplicant.setPos(POS);
		dw_rkapplicant.setYpay(YPAY);
		dw_rkapplicant.setOmoney(OMONEY);
		dw_rkapplicant.setOmoney_amt(OMONEY_AMT);
		dw_rkapplicant.setHincome(HINCOME);
		// dw_rkapplicant.setDrate(Util.parseBigDecimal(DRATE));
		// dw_rkapplicant.setYrate(Util.parseBigDecimal(YRATE));
		dw_rkapplicant.setDrate(DRATE);
		dw_rkapplicant.setYrate(YRATE);
		dw_rkapplicant.setFrate(FRATE);
		dw_rkapplicant.setCertificate(CERTIFICATE);
		dw_rkapplicant.setHcertificate(HCERTIFICATE);
		dw_rkapplicant.setLawadr_zip_cd(LAWADR_ZIP_CD);
		dw_rkapplicant.setAdr_zip_cd(ADR_ZIP_CD);
		dw_rkapplicant.setDbcredit(DBCREDIT);
		dw_rkapplicant.setIspfund(ISPFund);
		dw_rkapplicant.setObusiness(OBUSINESS);
		dw_rkapplicant.setInvmbal(INVMBAL);
		dw_rkapplicant.setInvobal(INVOBAL);
		dw_rkapplicant.setOdep(ODEP);
		dw_rkapplicant.setCmsstatus(CMSSTATUS);
		dw_rkapplicant.setDocstatus(DOCSTATUS);
		dw_rkapplicant.setData_src_dt(CapDate.getCurrentTimestamp());

		dw_rkapplicant.setYpay_swft(YPAY_SWFT);
		dw_rkapplicant.setOmoney_amt_swft(OMONEY_AMT_SWFT);
		dw_rkapplicant.setHincome_swft(HINCOME_SWFT);
		dw_rkapplicant.setInvmbal_swft(INVMBAL_SWFT);
		dw_rkapplicant.setInvobal_swft(INVOBAL_SWFT);
		dw_rkapplicant.setOdep_swft(ODEP_SWFT);
		dw_rkapplicant.setPrimary_card(c120m01a.getPrimary_card());
		dw_rkapplicant.setAdditional_card(c120m01a.getAdditional_card());
		dw_rkapplicant.setBusiness_or_p_card(c120m01a.getBusiness_or_p_card());
		dw_rkapplicant.setCompany_id(Util.trim(c120s01b.getJuId()));
		dw_rkapplicant.setTotal_capital(c120s01b.getJuTotalCapital());
		dw_rkapplicant.setPaidup_capital(c120s01b.getJuPaidUpCapital());
		dw_rkapplicant.setCompany_type(Util.trim(c120s01b.getJuType()));
		dw_rkapplicant.setFincome(FINCOME);
		dw_rkapplicant.setFincome_swft(Util.trim(c120s01c.getFincomeCurr()));
		dw_rkapplicant.setC_flag(c_flag);

		data.add(dw_rkapplicant);
		return data;
	}

	/**
	 * DW_RKAPPLICANT申請人基本資料List(FOR 海外案件)
	 * 
	 */
	@Override
	public List<DW_RKAPPLICANT> upDW_RKAPPLICANTforOBU(
			List<DW_RKAPPLICANT> data, L120M01A l120m01a, L140M01A l140m01a,
			C120M01A c120m01a, C120S01A c120s01a, C120S01B c120s01b,
			C120S01C c120s01c, C120S01E c120s01e) {
		String BR_CD = Util.trim(l140m01a.getOwnBrId());
		String NOTEID = Util.trim(l140m01a.getMainId());
		String CUSTID = Util.trim(c120s01a.getCustId());
		String DUPNO = Util.trim(c120s01a.getDupNo());
		String ACCT_KEY = ("".equals(Util.trim(l140m01a.getCntrNo())) ? ""
				: Util.trim(l140m01a.getCntrNo()));

		Integer MOWVER1 = new Integer(0);
		Integer MOWVER2 = new Integer(0);

		String mowtype = "O";
		Date tmpDate = CapDate.parseDate("0001-01-01");
		Date jcicDate = ("".equals(Util.trim(c120s01e.getEJcicQDate())) ? tmpDate
				: c120s01e.getEJcicQDate());

		String CUST_KEY = Util.trim(l120m01a.getCustId());
		String LNGEFLAG = null;

		Date DOB = c120s01a.getBirthday();

		String MARRIAGE = Util.trim(c120s01a.getMarry());
		Integer CHILDREN = c120s01a.getChild();
		BigDecimal SENIORITY = c120s01b.getSeniority();
		String POS = Util.trim(c120s01b.getJobType1())
				+ Util.trim(c120s01b.getJobType2())
				+ Util.trim(c120s01b.getJobTitle());
		BigDecimal YPAY = Util.parseBigDecimal(c120s01b.getPayAmt());

		/*
		 * 其他收入/ 其他收入金額(幣別)/其他收入金額海外 用[C120S01B]OthType/OthCurr/OthAmt
		 * 用[C120S01C]OIncome/OMoneyCurr/OMoneyAmt
		 */
		String OMONEY = "";
		String OMONEY_AMT_SWFT = "";
		BigDecimal OMONEY_AMT = BigDecimal.ZERO;
		if (c120s01c.getOMoneyAmt() != null) {
			OMONEY = Util.trim(c120s01c.getOIncome());
			OMONEY_AMT_SWFT = Util.trim(c120s01c.getOMoneyCurr());
			OMONEY_AMT = Util.parseBigDecimal(c120s01c.getOMoneyAmt());
		} else if (c120s01b.getOthAmt() != null) {
			OMONEY = Util.trim(c120s01b.getOthType());
			OMONEY_AMT_SWFT = Util.trim(c120s01b.getOthCurr());
			OMONEY_AMT = Util.parseBigDecimal(c120s01b.getOthAmt());
		}

		BigDecimal HINCOME = Util.parseBigDecimal(c120s01c.getYFamAmt());
		if (HINCOME != null) {
			String maxHINCOME = "9999999999";
			if (CapMath.compare(HINCOME, maxHINCOME) > 0) {
				HINCOME = CapMath.getBigDecimal(maxHINCOME);
			}
		}
		BigDecimal FINCOME = c120s01c.getFincome();
		if (FINCOME != null) {
			String maxFINCOME = "9999999999";
			if (CapMath.compare(FINCOME, maxFINCOME) > 0) {
				FINCOME = CapMath.getBigDecimal(maxFINCOME);
			}
		}
		BigDecimal DRATE = Util.parseBigDecimal(c120s01c.getDRate());
		BigDecimal YRATE = Util.parseBigDecimal(c120s01c.getYRate());
		BigDecimal FRATE = Util.parseBigDecimal(c120s01c.getFRate());
		String CERTIFICATE = Util.trim(c120s01b.getInDoc());
		String HCERTIFICATE = Util.trim(c120s01c.getYIncomeCert());
		String LAWADR_ZIP_CD = null;
		String ADR_ZIP_CD = null;
		String DBCREDIT = Util.trim(c120s01c.getCredit());
		if (Util.equals(DBCREDIT, "A|B") || Util.equals(DBCREDIT, "AB")) {
			DBCREDIT = "C";
		}
		String ISPFund = Util.trim(c120s01c.getIsPeriodFund());
		String OBUSINESS = clsService._convertBusi(c120s01c.getBusi());

		BigDecimal INVMBAL = Util.parseBigDecimal(c120s01c.getInvMBalAmt());
		BigDecimal INVOBAL = Util.parseBigDecimal(c120s01c.getInvOBalAmt());
		BigDecimal ODEP = Util.parseBigDecimal(c120s01c.getBranAmt());
		String CMSSTATUS = Util.trim(c120s01a.getCmsStatus());
		String DOCSTATUS = clsService
				._dwDOCSTATUS(Util.trim(l120m01a.getDocStatus()));
		String YPAY_SWFT = Util.trim(c120s01b.getPayCurr());

		String HINCOME_SWFT = Util.trim(c120s01c.getYFamCurr());
		String INVMBAL_SWFT = Util.trim(c120s01c.getInvMBalCurr());
		String INVOBAL_SWFT = Util.trim(c120s01c.getInvOBalCurr());
		String ODEP_SWFT = Util.trim(c120s01c.getBranCurr());

		DW_RKAPPLICANT dw_rkapplicant = new DW_RKAPPLICANT();
		dw_rkapplicant.setBr_cd(BR_CD);
		dw_rkapplicant.setNoteid(NOTEID);
		dw_rkapplicant.setCustid(CUSTID);
		dw_rkapplicant.setDupno(DUPNO);
		dw_rkapplicant.setAcct_key(ACCT_KEY);
		dw_rkapplicant.setMowtype(mowtype);
		dw_rkapplicant.setMowver1(MOWVER1);
		dw_rkapplicant.setMowver2(MOWVER2);

		dw_rkapplicant.setJcic_date(jcicDate);
		dw_rkapplicant.setCust_key(CUST_KEY);
		dw_rkapplicant.setLngeflag(LNGEFLAG);
		dw_rkapplicant.setDob(DOB);
		dw_rkapplicant.setEducation(clsService.convert_dw_edu(c120s01a));
		dw_rkapplicant.setMarriage(Util.parseInt(MARRIAGE));
		dw_rkapplicant.setChildren(CHILDREN);
		dw_rkapplicant.setSeniority(SENIORITY);
		dw_rkapplicant.setPos(POS);
		dw_rkapplicant.setYpay(YPAY);
		dw_rkapplicant.setOmoney(OMONEY);
		dw_rkapplicant.setOmoney_amt(OMONEY_AMT);
		dw_rkapplicant.setHincome(HINCOME);
		// dw_rkapplicant.setDrate(Util.parseBigDecimal(DRATE));
		// dw_rkapplicant.setYrate(Util.parseBigDecimal(YRATE));
		dw_rkapplicant.setDrate(DRATE);
		dw_rkapplicant.setYrate(YRATE);
		dw_rkapplicant.setFrate(FRATE);
		dw_rkapplicant.setCertificate(CERTIFICATE);
		dw_rkapplicant.setHcertificate(HCERTIFICATE);
		dw_rkapplicant.setLawadr_zip_cd(LAWADR_ZIP_CD);
		dw_rkapplicant.setAdr_zip_cd(ADR_ZIP_CD);
		dw_rkapplicant.setDbcredit(DBCREDIT);
		dw_rkapplicant.setIspfund(ISPFund);
		dw_rkapplicant.setObusiness(OBUSINESS);
		dw_rkapplicant.setInvmbal(INVMBAL);
		dw_rkapplicant.setInvobal(INVOBAL);
		dw_rkapplicant.setOdep(ODEP);
		dw_rkapplicant.setCmsstatus(CMSSTATUS);
		dw_rkapplicant.setDocstatus(DOCSTATUS);
		dw_rkapplicant.setData_src_dt(CapDate.getCurrentTimestamp());

		dw_rkapplicant.setYpay_swft(YPAY_SWFT);
		dw_rkapplicant.setOmoney_amt_swft(OMONEY_AMT_SWFT);
		dw_rkapplicant.setHincome_swft(HINCOME_SWFT);
		dw_rkapplicant.setInvmbal_swft(INVMBAL_SWFT);
		dw_rkapplicant.setInvobal_swft(INVOBAL_SWFT);
		dw_rkapplicant.setOdep_swft(ODEP_SWFT);
		dw_rkapplicant.setPrimary_card(c120m01a.getPrimary_card());
		dw_rkapplicant.setAdditional_card(c120m01a.getAdditional_card());
		dw_rkapplicant.setBusiness_or_p_card(c120m01a.getBusiness_or_p_card());
		dw_rkapplicant.setCompany_id("");
		dw_rkapplicant.setTotal_capital(null);
		dw_rkapplicant.setPaidup_capital(null);
		dw_rkapplicant.setCompany_type("");
		dw_rkapplicant.setFincome(FINCOME);
		dw_rkapplicant.setFincome_swft(Util.trim(c120s01c.getFincomeCurr()));
		data.add(dw_rkapplicant);
		return data;
	}
	
	//--------------雙軌模型使用(DW_RKSCORE_N、DW_RKCREDIT_N、DW_RKAPPLICANT_N)--------------
	//--------------雙軌模型固定運算[房貸3.0]、[非房貸4.0]、專案信貸(非團體)4.0，只保留這些部份--------------
	public List<DW_RKSCORE_N> upDW_RKSCORE_N(List<DW_RKSCORE_N> data,
			L120M01A l120m01a, L140M01A l140m01a, String ACCT_KEY,
			String modelKind, String LNGEFLAG, C120M01A c120m01a,
			C101S01G_N c101s01g_n, C101S01Q_N c101s01q_n, C101S01R_N c101s01r_n,
			String dDOCSTATUS) {
		String BR_CD = Util.trim(l140m01a.getOwnBrId());
		String NOTEID = Util.trim(l140m01a.getMainId());
		String CUSTID = Util.trim(c120m01a.getCustId());
		String DUPNO = Util.trim(c120m01a.getDupNo());

		Integer MOWVER1 = new Integer(0);
		Integer MOWVER2 = new Integer(0);

		String mowtype = "";
		Date jcicDate = null;
		BigDecimal D07_LN_NOS_TAMT = null;
		BigDecimal P19_CC12_PCODE_A_TIMES = null;
		String MARRIAGE = null;
		Integer CHILDREN = null;
		String OCCUPATION_1 = null;
		String EDUCATION = null;
		Date RATING_DATE = null;
		BigDecimal R01_CC12_REVOL_RATE = null;
		BigDecimal R01_CC12_REVOL_RATE_SCORE = null;
		BigDecimal HINCOME_REG = null;
		BigDecimal HINCOME_REG_SCORE = null;
		BigDecimal P69_CC12_DELAY_RC_TIMES = null;
		BigDecimal P25_CC6_PCODE_A_TIMES = null;
		BigDecimal P25_CC6_PCODE_A_TIMES_SCORE = null;

		BigDecimal SENIORITY = null;
		BigDecimal EDUCATION_N_SCORE = null;
		BigDecimal P68_CC6_DELAY_RC_TIMES = null;
		String c_flag = "";
		BigDecimal DRATE_SCORE = null;
		
		// 消金房貸2.1
		BigDecimal N22_INQ12_BY30D = null;
		BigDecimal N22_SCORE = null;
		// 消金房貸3.0
		BigDecimal DESIGNATION_SCORE = null;
		BigDecimal D42_SCORE = null;
		BigDecimal P68_SCORE = null;
		BigDecimal YRATE_SCORE = null;
		BigDecimal N01_SCORE = null;
		BigDecimal P19_CC12_PCODE_A_TIMES_SCORE = null;
		String DESIGNATION = "";
		BigDecimal D42_AVG_LIMIT = null;
		BigDecimal N01_INQ3_TOTAL = null;
		// 消金非房貸4.0
		BigDecimal P1_O_COUNTS = null;
		BigDecimal LOAN_BAL_S_BYID = null;
		BigDecimal R01_CC12_MAX_REVOL_RATE = null;
		BigDecimal P1_SCORE = null;
		BigDecimal LOAN_BAL_S_BYIDSCORE = null;
		BigDecimal R01_CC12_MAX_REVOL_RATE_SCORE = null;

		if (Util.equals(UtilConstants.DEFAULT.是, c120m01a.getModelTyp())
				|| Util.equals(UtilConstants.L140S02AModelKind.房貸, modelKind)) {
			// 新版模型上線前舊案 || 新模型分類為房貸
			if (!Util.isEmpty(c101s01g_n.getVarVer())) {
				String VarVer[] = c101s01g_n.getVarVer().split("\\.");
				MOWVER1 = Util.parseInt(VarVer[0]);
				MOWVER2 = Util.parseInt(VarVer[1]);
			}
			jcicDate = c101s01g_n.getJcicQDate();
			mowtype = "M";
			MARRIAGE = Util.trim(c101s01g_n.getMarry());
			CHILDREN = c101s01g_n.getChild();
			EDUCATION = Util.trim(c101s01g_n.getEdu());
			OCCUPATION_1 = Util.trim(c101s01g_n.getJobType1());
			SENIORITY = c101s01g_n.getSeniority();
			D07_LN_NOS_TAMT = c101s01g_n.getChkAmt01();
			P19_CC12_PCODE_A_TIMES = c101s01g_n.getChkNum3();
			RATING_DATE = c101s01g_n.getGrdCDate();
			if (c101s01g_n.getYFamAmt() != null) {
				HINCOME_REG = LMSUtil.getUploadYFamAmt(c101s01g_n.getYFamAmt());
			}
			HINCOME_REG_SCORE = c101s01g_n.getScrNum01();
			P69_CC12_DELAY_RC_TIMES = c101s01g_n.getChkNum2();
			P25_CC6_PCODE_A_TIMES = c101s01g_n.getChkNum1();
			P25_CC6_PCODE_A_TIMES_SCORE = c101s01g_n.getScrNum09();
			if (Util.equals(ClsScoreUtil.V3_0_HOUSE_LOAN,
					c101s01g_n.getVarVer())) {
				// N18 近12個月新業務申請查詢總次數，計算方式-天數別(30天內算1次)
				N22_INQ12_BY30D = c101s01g_n.getItemN22();
				N22_SCORE = c101s01g_n.getScrN22();
				// DESIGNATION 職稱
				DESIGNATION = c101s01g_n.getJobTitle();
				DESIGNATION_SCORE = c101s01g_n.getScrJobTitle();
				// D42 當月有效信用卡主卡平均信用額度(仟元)
				D42_AVG_LIMIT = c101s01g_n.getItemD42();
				D42_SCORE = c101s01g_n.getScrD42();
				// EDUCATION 學歷
				EDUCATION = c101s01g_n.getEdu();
				EDUCATION_N_SCORE = c101s01g_n.getScrEdu();
				// P68 近6個月信用卡繳款狀況出現不良繳款紀錄或使用循環信用的次數
				P68_CC6_DELAY_RC_TIMES = c101s01g_n.getItemP68();
				P68_SCORE = c101s01g_n.getScrP68();
				// YRATE 夫妻負債比
				// YRATE = c120s01g.getYRate(); //DW的YRATE，傳到DW_RKAPPLICANT
				YRATE_SCORE = c101s01g_n.getScrYRate();
				// N01 近3個月非Z類申請查詢總次數(近三個月本行查詢不列入計算)
				N01_INQ3_TOTAL = c101s01g_n.getItemN01();
				N01_SCORE = c101s01g_n.getScrN01();
				// P19 近12個月信用卡繳款狀況出現全額繳清無延遲次數(不含無須繳款)
				P19_CC12_PCODE_A_TIMES = c101s01g_n.getChkNum3();
				P19_CC12_PCODE_A_TIMES_SCORE = c101s01g_n.getScrP19();
				// R01 近12個月信用卡(每筆)循環信用平均使用率_(mean(單筆循環信用使用率))
				R01_CC12_REVOL_RATE = c101s01g_n.getAvgRate01();
				R01_CC12_REVOL_RATE_SCORE = c101s01g_n.getScrNum07();
			}
		} else if (Util.equals(UtilConstants.L140S02AModelKind.非房貸, modelKind)) {
			if (!Util.isEmpty(c101s01q_n.getVarVer())) {
				String VarVer[] = c101s01q_n.getVarVer().split("\\.");
				MOWVER1 = Util.parseInt(VarVer[0]);
				MOWVER2 = Util.parseInt(VarVer[1]);
			}
			jcicDate = c101s01q_n.getJcicQDate();
			mowtype = "N";
			c_flag = ClsScoreUtil.upDW_column_C_FLAG(c101s01q_n.getVarVer());
			RATING_DATE = c101s01q_n.getGrdCDate();
			if (Util.equals(ClsScoreUtil.V4_0_NOT_HOUSE_LOAN,
					c101s01q_n.getVarVer())) { // J-111-0373 消金非房貸4.0
				P1_O_COUNTS = c101s01q_n.getItemP01();
				P1_SCORE = c101s01q_n.getScrP01();
				LOAN_BAL_S_BYID = c101s01q_n.getLoanBalSByid();
				LOAN_BAL_S_BYIDSCORE = c101s01q_n.getScrLoanBalSByid();
				R01_CC12_MAX_REVOL_RATE = c101s01q_n.getItemMaxR01();
				R01_CC12_MAX_REVOL_RATE_SCORE = c101s01q_n.getScrMaxR01();
				EDUCATION = Util.trim(c101s01q_n.getEducation());
				EDUCATION_N_SCORE = c101s01q_n.getScreducation();
				DRATE_SCORE = c101s01q_n.getNoscrItemDrate();
			}
		} else if (Util.equals(UtilConstants.L140S02AModelKind.卡友貸, modelKind)) {
			if (!Util.isEmpty(c101s01r_n.getVarVer())) {
				String VarVer[] = c101s01r_n.getVarVer().split("\\.");
				MOWVER1 = Util.parseInt(VarVer[0]);
				MOWVER2 = Util.parseInt(VarVer[1]);
			}
			jcicDate = c101s01r_n.getJcicQDate();
			mowtype = "N";
			c_flag = "Y";
			RATING_DATE = c101s01r_n.getGrdCDate();
			if (Util.equals(ClsScoreUtil.V4_0_CARD_LOAN,
					c101s01r_n.getVarVer())) { // J-111-0373 消金卡友貸4.0
				P1_O_COUNTS = c101s01r_n.getItemP01();
				P1_SCORE = c101s01r_n.getScrP01();
				LOAN_BAL_S_BYID = c101s01r_n.getLoanBalSByid();
				LOAN_BAL_S_BYIDSCORE = c101s01r_n.getScrLoanBalSByid();
				R01_CC12_MAX_REVOL_RATE = c101s01r_n.getItemMaxR01();
				R01_CC12_MAX_REVOL_RATE_SCORE = c101s01r_n.getScrMaxR01();
				EDUCATION = Util.trim(c101s01r_n.getEducation());
				EDUCATION_N_SCORE = c101s01r_n.getScreducation();
				DRATE_SCORE = c101s01r_n.getNoscrItemDrate();
			}
		} else {
			return data;
		}

		String CUST_KEY = Util.trim(l120m01a.getCustId());

		// dDOCSTATUS 為上傳DW時註記為刪除案件
		String DOCSTATUS = "";
		if ("".equals(dDOCSTATUS)) {
			DOCSTATUS = clsService._dwDOCSTATUS(Util.trim(l120m01a.getDocStatus()));
		} else {
			DOCSTATUS = dDOCSTATUS;
		}

		DW_RKSCORE_N dw_rkscore_n = new DW_RKSCORE_N();
		dw_rkscore_n.setBr_cd(BR_CD);
		dw_rkscore_n.setNoteid(NOTEID);
		dw_rkscore_n.setCustid(CUSTID);
		dw_rkscore_n.setDupno(DUPNO);
		dw_rkscore_n.setAcct_key(ACCT_KEY);
		dw_rkscore_n.setMowtype(mowtype);
		dw_rkscore_n.setMowver1(MOWVER1);
		dw_rkscore_n.setMowver2(MOWVER2);
		dw_rkscore_n.setJcic_date(jcicDate);
		dw_rkscore_n.setCust_key(CUST_KEY);
		dw_rkscore_n.setLngeflag(LNGEFLAG);
		dw_rkscore_n.setD07_ln_nos_tamt(D07_LN_NOS_TAMT);
		dw_rkscore_n.setMarriage(clsService.parseIntColumn(MARRIAGE));
		dw_rkscore_n.setChildren(CHILDREN);
		dw_rkscore_n.setOccupation_1(clsService.parseIntColumn(OCCUPATION_1));
		dw_rkscore_n.setRating_date(RATING_DATE);
		dw_rkscore_n.setDocstatus(DOCSTATUS);
		dw_rkscore_n.setData_src_dt(CapDate.getCurrentTimestamp());
		dw_rkscore_n.setHincome_reg(HINCOME_REG);
		dw_rkscore_n.setHincome_reg_score(HINCOME_REG_SCORE);
		dw_rkscore_n
				.setP69_cc12_delay_rc_times(clsService.parseIntColumn(P69_CC12_DELAY_RC_TIMES));
		dw_rkscore_n
				.setP25_cc6_pcode_a_times(clsService.parseIntColumn(P25_CC6_PCODE_A_TIMES));
		dw_rkscore_n.setP25_cc6_pcode_a_times_score(P25_CC6_PCODE_A_TIMES_SCORE);
		dw_rkscore_n.setSeniority(SENIORITY);
		dw_rkscore_n.setC_flag(c_flag);
		dw_rkscore_n.setDrate_score(DRATE_SCORE);
		// ---------消金房貸2.1-------------
		dw_rkscore_n.setN22_inq12_by30d(N22_INQ12_BY30D);
		dw_rkscore_n.setN22_score(N22_SCORE);
		// ---------消金房貸3.0-------------(有些欄位2.0也有用)
		dw_rkscore_n.setDesignation(DESIGNATION);
		dw_rkscore_n.setDesignation_score(DESIGNATION_SCORE);
		dw_rkscore_n.setD42_avg_limit(D42_AVG_LIMIT);
		dw_rkscore_n.setD42_score(D42_SCORE);
		dw_rkscore_n.setEducation(clsService.parseIntColumn(EDUCATION));
		dw_rkscore_n.setEducation_n_score(EDUCATION_N_SCORE);
		dw_rkscore_n.setP68_cc6_delay_rc_times(P68_CC6_DELAY_RC_TIMES);
		dw_rkscore_n.setP68_score(P68_SCORE);
		dw_rkscore_n.setYrate_score(YRATE_SCORE);
		dw_rkscore_n.setN01_inq3_total(N01_INQ3_TOTAL);
		dw_rkscore_n.setN01_score(N01_SCORE);
		dw_rkscore_n
				.setP19_cc12_pcode_a_times(clsService.parseIntColumn(P19_CC12_PCODE_A_TIMES));
		dw_rkscore_n
				.setP19_cc12_pcode_a_times_score(P19_CC12_PCODE_A_TIMES_SCORE);
		dw_rkscore_n.setR01_cc12_revol_rate(R01_CC12_REVOL_RATE);
		dw_rkscore_n.setR01_cc12_revol_rate_score(R01_CC12_REVOL_RATE_SCORE);
		
		// ---------消金非房貸4.0-------------
		dw_rkscore_n.setP1_o_counts(P1_O_COUNTS);
		dw_rkscore_n.setP1_score(P1_SCORE);
		dw_rkscore_n.setLoan_bal_s_byid(LOAN_BAL_S_BYID);
		dw_rkscore_n.setLoan_bal_s_byidscore(LOAN_BAL_S_BYIDSCORE);
		dw_rkscore_n.setR01_cc12_max_revol_rate(R01_CC12_MAX_REVOL_RATE);
		dw_rkscore_n.setR01_cc12_max_revol_rate_score(R01_CC12_MAX_REVOL_RATE_SCORE);
				
		data.add(dw_rkscore_n);
		return data;
	}
	
	
	/**
	 * DW_RKCREDIT個人信用評分評等紀錄 List
	 * 
	 */
	@Override
	public List<DW_RKCREDIT_N> upDW_RKCREDIT_N(List<DW_RKCREDIT_N> data,
			L120M01A l120m01a, L140M01A l140m01a, String ACCT_KEY,
			String modelKind, String LNGEFLAG, C120M01A c120m01a,
			C101S01G_N c101s01g_n, String custId, String dupNo,
			String L140CustIdDupNo, String reason, String reasonOth,
			C101S01Q_N c101s01q_n, C101S01R_N c101s01r_n, String dDOCSTATUS,
			C120S01E c120s01e, boolean isShortPeriodCase) {
		String BR_CD = Util.trim(l140m01a.getOwnBrId());
		String NOTEID = Util.trim(l140m01a.getMainId());
		String CUSTID = Util.trim(c120m01a.getCustId());
		String DUPNO = Util.trim(c120m01a.getDupNo());

		Integer MOWVER1 = new Integer(0);
		Integer MOWVER2 = new Integer(0);

		// J-102-0196區分房貸(C120S01G)及非房貸(C120S01Q)信用模型
		String mowtype = "";
		Date jcicDate = null;

		BigDecimal BASE_SCORE = null;
		BigDecimal TOTAL_SCORE = null;
		BigDecimal INITIAL_SCORE = null;
		BigDecimal PREDICT_BAD_RATE = null;
		Integer INITIAL_RATING = null;
		Integer ADJ_RATING = null;
		Integer FINAL_RATING = null;
		String JCIC_WARNING_FLAG = null;
		BigDecimal DR = null;
		BigDecimal DR_1YR = null;
		String c_flag = "";
		Integer spr = null;
		Integer j10_score = null;
		String j10_reason_code1 = "";
		String j10_reason_code2 = "";
		String j10_reason_code3 = "";
		String j10_reason_code4 = "";
		Integer jr_autodg = null;
		BigDecimal SLOPE = null;
		BigDecimal INTERCEPT = null;
		String PRODID = "";

		if (Util.equals(UtilConstants.DEFAULT.是, c120m01a.getModelTyp())
				|| Util.equals(UtilConstants.L140S02AModelKind.房貸, modelKind)) {
			// 新版模型上線前舊案 || 新模型分類為房貸
			if (!Util.isEmpty(c101s01g_n.getVarVer())) {
				String VarVer[] = c101s01g_n.getVarVer().split("\\.");
				MOWVER1 = Util.parseInt(VarVer[0]);
				MOWVER2 = Util.parseInt(VarVer[1]);
			}
			jcicDate = c101s01g_n.getJcicQDate();
			mowtype = "M";
			BASE_SCORE = c101s01g_n.getScrNum12();
			TOTAL_SCORE = c101s01g_n.getScrNum11();
			INITIAL_SCORE = c101s01g_n.getScrNum13();
			PREDICT_BAD_RATE = c101s01g_n.getPd();
			INITIAL_RATING = Util.parseInt(c101s01g_n.getGrade1());
			FINAL_RATING = Util.parseInt(c101s01g_n.getGrade3());
			JCIC_WARNING_FLAG = "N";
			if (this.has_jcic_warning_flag_n((c101s01g_n))) {
				JCIC_WARNING_FLAG = "Y";
			}
			DR = c101s01g_n.getDr_3YR();
			DR_1YR = c101s01g_n.getDr_1YR();
			// -----消金房貸3.0-----
			SLOPE = c101s01g_n.getSlope();
			INTERCEPT = c101s01g_n.getInterCept();
			PRODID = c120s01e == null ? PRODID : c120s01e.getProdId();
			// --------------------

		} else if (Util.equals(UtilConstants.L140S02AModelKind.非房貸, modelKind)) {
			if (c101s01q_n == null) {
				// 刪除舊案,可能c120s01q不存在
				return data;
			}
			if (!Util.isEmpty(c101s01q_n.getVarVer())) {
				String VarVer[] = c101s01q_n.getVarVer().split("\\.");
				MOWVER1 = Util.parseInt(VarVer[0]);
				MOWVER2 = Util.parseInt(VarVer[1]);
			}
			jcicDate = c101s01q_n.getJcicQDate();
			mowtype = "N";
			c_flag = ClsScoreUtil.upDW_column_C_FLAG(c101s01q_n.getVarVer());
			
			BASE_SCORE = c101s01q_n.getScrNum12();
			TOTAL_SCORE = c101s01q_n.getScrNum11();
			INITIAL_SCORE = c101s01q_n.getScrNum13();
			PREDICT_BAD_RATE = c101s01q_n.getPd();
			INITIAL_RATING = Util.parseInt(c101s01q_n.getGrade1());
			FINAL_RATING = Util.parseInt(c101s01q_n.getGrade3());
			JCIC_WARNING_FLAG = "N";
			if (this.has_jcic_warning_flag_n(c101s01q_n)) {
				JCIC_WARNING_FLAG = "Y";
			}

			DR = isShortPeriodCase ? c101s01q_n.getDr_2YR() : c101s01q_n
					.getDr_3YR();
			DR_1YR = isShortPeriodCase ? c101s01q_n.getDr_1YR_S() : c101s01q_n
					.getDr_1YR_L();
			// -----非消金房貸4.0-----
			SLOPE = c101s01q_n.getSlope();
			INTERCEPT = c101s01q_n.getInterCept();
			PRODID = c120s01e == null ? PRODID : c120s01e.getProdId();
			// ----------------------

		} else if (Util.equals(UtilConstants.L140S02AModelKind.卡友貸, modelKind)) {
			if (!Util.isEmpty(c101s01r_n.getVarVer())) {
				String VarVer[] = c101s01r_n.getVarVer().split("\\.");
				MOWVER1 = Util.parseInt(VarVer[0]);
				MOWVER2 = Util.parseInt(VarVer[1]);
			}
			jcicDate = c101s01r_n.getJcicQDate();
			mowtype = "N";
			c_flag = "Y";

			BASE_SCORE = c101s01r_n.getScrNum12();
			TOTAL_SCORE = c101s01r_n.getScrNum11();
			INITIAL_SCORE = c101s01r_n.getScrNum13();
			PREDICT_BAD_RATE = c101s01r_n.getPd();
			INITIAL_RATING = Util.parseInt(c101s01r_n.getGrade1());
			FINAL_RATING = Util.parseInt(c101s01r_n.getGrade3());
			JCIC_WARNING_FLAG = "N";
			if (this.has_jcic_warning_flag_n(c101s01r_n)) {
				JCIC_WARNING_FLAG = "Y";
			}

			DR = isShortPeriodCase ? c101s01r_n.getDr_2YR() : c101s01r_n
					.getDr_3YR();
			DR_1YR = isShortPeriodCase ? c101s01r_n.getDr_1YR_S() : c101s01r_n
					.getDr_1YR_L();

			SLOPE = c101s01r_n.getSlope();
			INTERCEPT = c101s01r_n.getInterCept();
			PRODID = c120s01e == null ? PRODID : c120s01e.getProdId();
			// === J10相關
			String sprRating = Util.trim(c101s01r_n.getSprtRating());
			spr = Util.isEmpty(sprRating) ? null : Util.parseInt(sprRating);
			j10_score = c101s01r_n.getJ10_score();
			j10_reason_code1 = Util.trim(c101s01r_n.getKcs003_reason_code1());
			j10_reason_code2 = Util.trim(c101s01r_n.getKcs003_reason_code2());
			j10_reason_code3 = Util.trim(c101s01r_n.getKcs003_reason_code3());
			j10_reason_code4 = Util.trim(c101s01r_n.getKcs003_reason_code4());
			jr_autodg = Util.parseInt(c101s01r_n.getAdj_j10());
		} else {
			return data;
		}

		ADJ_RATING = INITIAL_RATING - FINAL_RATING;
		if (Util.equals(UtilConstants.L140S02AModelKind.卡友貸, modelKind)) {
			ADJ_RATING = Util.parseInt(spr) - FINAL_RATING;
		}

		String CUST_KEY = Util.trim(l120m01a.getCustId());
		// dDOCSTATUS 為上傳DW時註記為刪除案件
		String DOCSTATUS = "";
		if ("".equals(dDOCSTATUS)) {
			DOCSTATUS = clsService._dwDOCSTATUS(Util.trim(l120m01a.getDocStatus()));
		} else {
			DOCSTATUS = dDOCSTATUS;
		}

		String FINAL_RATING_FLAG = "N";
		if (Util.equals(Util.trim(custId) + Util.trim(dupNo), L140CustIdDupNo)) {
			FINAL_RATING_FLAG = "Y";
		}

		DW_RKCREDIT_N dw_rkcredit_n = new DW_RKCREDIT_N();
		dw_rkcredit_n.setBr_cd(BR_CD);
		dw_rkcredit_n.setNoteid(NOTEID);
		dw_rkcredit_n.setCustid(CUSTID);
		dw_rkcredit_n.setDupno(DUPNO);
		dw_rkcredit_n.setAcct_key(ACCT_KEY);
		dw_rkcredit_n.setMowtype(mowtype);
		dw_rkcredit_n.setMowver1(MOWVER1);
		dw_rkcredit_n.setMowver2(MOWVER2);
		dw_rkcredit_n.setJcic_date(jcicDate);
		dw_rkcredit_n.setCust_key(CUST_KEY);
		dw_rkcredit_n.setLngeflag(LNGEFLAG);
		dw_rkcredit_n.setBase_score(BASE_SCORE);
		dw_rkcredit_n.setTotal_score(TOTAL_SCORE);
		dw_rkcredit_n.setInitial_score(INITIAL_SCORE);
		dw_rkcredit_n.setPredict_bad_rate(PREDICT_BAD_RATE);
		dw_rkcredit_n.setInitial_rating(INITIAL_RATING);
		dw_rkcredit_n.setAdj_rating(ADJ_RATING);
		dw_rkcredit_n.setFinal_rating(FINAL_RATING);
		dw_rkcredit_n.setJcic_warning_flag(JCIC_WARNING_FLAG);
		dw_rkcredit_n.setFinal_rating_flag(FINAL_RATING_FLAG);
		dw_rkcredit_n.setDocstatus(DOCSTATUS);
		dw_rkcredit_n.setDelete_reason(Util.truncateString(reason, 2));
		dw_rkcredit_n.setReject_othereason_text(Util.trimSizeInOS390(reasonOth,
				200));
		dw_rkcredit_n.setData_src_dt(CapDate.getCurrentTimestamp());
		dw_rkcredit_n.setDr(DR);
		dw_rkcredit_n.setDr_1yr(DR_1YR);
		dw_rkcredit_n.setC_flag(c_flag);
		dw_rkcredit_n.setSpr(spr);
		dw_rkcredit_n.setJ10_score(j10_score);
		dw_rkcredit_n.setJ10_reason_code1(j10_reason_code1);
		dw_rkcredit_n.setJ10_reason_code2(j10_reason_code2);
		dw_rkcredit_n.setJ10_reason_code3(j10_reason_code3);
		dw_rkcredit_n.setJ10_reason_code4(j10_reason_code4);
		dw_rkcredit_n.setJr_autodg(jr_autodg);
		dw_rkcredit_n.setSlope(SLOPE);
		dw_rkcredit_n.setIntercept(INTERCEPT);
		dw_rkcredit_n.setProdId(PRODID);

		data.add(dw_rkcredit_n);
		return data;
	}
	
	
	/**
	 * DW_RKAPPLICANT申請人基本資料List
	 * 
	 */
	@Override
	public List<DW_RKAPPLICANT_N> upDW_RKAPPLICANT_N(List<DW_RKAPPLICANT_N> data,
			L120M01A l120m01a, L140M01A l140m01a, String ACCT_KEY,
			String modelKind, String LNGEFLAG, C120M01A c120m01a,
			C120S01A c120s01a, C120S01B c120s01b, C120S01C c120s01c,
			C101S01G_N c101s01g_n, C101S01Q_N c101s01q_n, C101S01R_N c101s01r_n,
			String dDOCSTATUS) {
		String BR_CD = Util.trim(l140m01a.getOwnBrId());
		String NOTEID = Util.trim(l140m01a.getMainId());
		String CUSTID = Util.trim(c120m01a.getCustId());
		String DUPNO = Util.trim(c120m01a.getDupNo());

		Integer MOWVER1 = new Integer(0);
		Integer MOWVER2 = new Integer(0);

		// J-102-0196區分房貸(C120S01G)及非房貸(C120S01Q)信用模型
		String mowtype = "";
		Date jcicDate = null;
		String c_flag = "";

		if (Util.equals(UtilConstants.DEFAULT.是, c120m01a.getModelTyp())
				|| Util.equals(UtilConstants.L140S02AModelKind.房貸, modelKind)) {
			// 新版模型上線前舊案 || 新模型分類為房貸
			if (!Util.isEmpty(c101s01g_n.getVarVer())) {
				String VarVer[] = c101s01g_n.getVarVer().split("\\.");
				MOWVER1 = Util.parseInt(VarVer[0]);
				MOWVER2 = Util.parseInt(VarVer[1]);
			}
			jcicDate = c101s01g_n.getJcicQDate();
			mowtype = "M";
		} else if (Util.equals(UtilConstants.L140S02AModelKind.非房貸, modelKind)) {
			if (!Util.isEmpty(c101s01q_n.getVarVer())) {
				String VarVer[] = c101s01q_n.getVarVer().split("\\.");
				MOWVER1 = Util.parseInt(VarVer[0]);
				MOWVER2 = Util.parseInt(VarVer[1]);
			}
			jcicDate = c101s01q_n.getJcicQDate();
			mowtype = "N";
			c_flag = ClsScoreUtil.upDW_column_C_FLAG(c101s01q_n.getVarVer());
		} else if (Util.equals(UtilConstants.L140S02AModelKind.卡友貸, modelKind)) {
			if (!Util.isEmpty(c101s01r_n.getVarVer())) {
				String VarVer[] = c101s01r_n.getVarVer().split("\\.");
				MOWVER1 = Util.parseInt(VarVer[0]);
				MOWVER2 = Util.parseInt(VarVer[1]);
			}
			jcicDate = c101s01r_n.getJcicQDate();
			mowtype = "N";
			c_flag = "Y";

		} else {
			return data;
		}

		String CUST_KEY = Util.trim(l120m01a.getCustId());
		Date DOB = c120s01a.getBirthday();

		String MARRIAGE = Util.trim(c120s01a.getMarry());
		Integer CHILDREN = c120s01a.getChild();
		BigDecimal SENIORITY = c120s01b.getSeniority();
		String POS = Util.trim(c120s01b.getJobType1())
				+ Util.trim(c120s01b.getJobType2())
				+ Util.trim(c120s01b.getJobTitle());
		BigDecimal YPAY = c120s01b.getPayAmt();
		String OMONEY = Util.trim(c120s01b.getOthType());
		BigDecimal OMONEY_AMT = c120s01b.getOthAmt();
		BigDecimal HINCOME = null;
		if (c120s01c.getYFamAmt() != null) {
			HINCOME = LMSUtil.getUploadYFamAmt(c120s01c.getYFamAmt());
		}
		BigDecimal FINCOME = c120s01c.getFincome();
		if (FINCOME != null) {
			String maxFINCOME = "9999999999";
			if (CapMath.compare(FINCOME, maxFINCOME) > 0) {
				FINCOME = CapMath.getBigDecimal(maxFINCOME);
			}
		}
		BigDecimal DRATE = c120s01c.getDRate();
		BigDecimal YRATE = c120s01c.getYRate();
		BigDecimal FRATE = c120s01c.getFRate();
		String CERTIFICATE = Util.trim(c120s01b.getInDoc());
		String HCERTIFICATE = Util.trim(c120s01c.getYIncomeCert());
		String LAWADR_ZIP_CD = Util.trim(c120s01a.getFZip());
		String ADR_ZIP_CD = Util.trim(c120s01a.getCoZip());
		String DBCREDIT = Util.trim(c120s01c.getCredit());
		if (Util.equals(DBCREDIT, "A|B") || Util.equals(DBCREDIT, "AB")) {
			DBCREDIT = "C";
		}
		String ISPFund = Util.trim(c120s01c.getIsPeriodFund());
		String OBUSINESS = clsService._convertBusi(c120s01c.getBusi());

		BigDecimal INVMBAL = c120s01c.getInvMBalAmt();
		BigDecimal INVOBAL = c120s01c.getInvOBalAmt();
		BigDecimal ODEP = c120s01c.getBranAmt();
		String CMSSTATUS = Util.trim(c120s01a.getCmsStatus());
		// dDOCSTATUS 為上傳DW時註記為刪除案件
		String DOCSTATUS = "";
		if ("".equals(dDOCSTATUS)) {
			DOCSTATUS = clsService._dwDOCSTATUS(Util.trim(l120m01a.getDocStatus()));
		} else {
			DOCSTATUS = dDOCSTATUS;
		}

		String YPAY_SWFT = Util.trim(c120s01b.getPayCurr());
		String OMONEY_AMT_SWFT = Util.trim(c120s01b.getOthCurr());
		String HINCOME_SWFT = Util.trim(c120s01c.getYFamCurr());
		String INVMBAL_SWFT = Util.trim(c120s01c.getInvMBalCurr());
		String INVOBAL_SWFT = Util.trim(c120s01c.getInvOBalCurr());
		String ODEP_SWFT = Util.trim(c120s01c.getBranCurr());

		DW_RKAPPLICANT_N dw_rkapplicant_n = new DW_RKAPPLICANT_N();
		dw_rkapplicant_n.setBr_cd(BR_CD);
		dw_rkapplicant_n.setNoteid(NOTEID);
		dw_rkapplicant_n.setCustid(CUSTID);
		dw_rkapplicant_n.setDupno(DUPNO);
		dw_rkapplicant_n.setAcct_key(ACCT_KEY);
		dw_rkapplicant_n.setMowtype(mowtype);
		dw_rkapplicant_n.setMowver1(MOWVER1);
		dw_rkapplicant_n.setMowver2(MOWVER2);

		dw_rkapplicant_n.setJcic_date(jcicDate);
		dw_rkapplicant_n.setCust_key(CUST_KEY);
		dw_rkapplicant_n.setLngeflag(LNGEFLAG);
		dw_rkapplicant_n.setDob(DOB);
		dw_rkapplicant_n.setEducation(clsService.convert_dw_edu(c120s01a));
		dw_rkapplicant_n.setMarriage(Util.parseInt(MARRIAGE));
		dw_rkapplicant_n.setChildren(CHILDREN);
		dw_rkapplicant_n.setSeniority(SENIORITY);
		dw_rkapplicant_n.setPos(POS);
		dw_rkapplicant_n.setYpay(YPAY);
		dw_rkapplicant_n.setOmoney(OMONEY);
		dw_rkapplicant_n.setOmoney_amt(OMONEY_AMT);
		dw_rkapplicant_n.setHincome(HINCOME);
		dw_rkapplicant_n.setDrate(DRATE);
		dw_rkapplicant_n.setYrate(YRATE);
		dw_rkapplicant_n.setFrate(FRATE);
		dw_rkapplicant_n.setCertificate(CERTIFICATE);
		dw_rkapplicant_n.setHcertificate(HCERTIFICATE);
		dw_rkapplicant_n.setLawadr_zip_cd(LAWADR_ZIP_CD);
		dw_rkapplicant_n.setAdr_zip_cd(ADR_ZIP_CD);
		dw_rkapplicant_n.setDbcredit(DBCREDIT);
		dw_rkapplicant_n.setIspfund(ISPFund);
		dw_rkapplicant_n.setObusiness(OBUSINESS);
		dw_rkapplicant_n.setInvmbal(INVMBAL);
		dw_rkapplicant_n.setInvobal(INVOBAL);
		dw_rkapplicant_n.setOdep(ODEP);
		dw_rkapplicant_n.setCmsstatus(CMSSTATUS);
		dw_rkapplicant_n.setDocstatus(DOCSTATUS);
		dw_rkapplicant_n.setData_src_dt(CapDate.getCurrentTimestamp());

		dw_rkapplicant_n.setYpay_swft(YPAY_SWFT);
		dw_rkapplicant_n.setOmoney_amt_swft(OMONEY_AMT_SWFT);
		dw_rkapplicant_n.setHincome_swft(HINCOME_SWFT);
		dw_rkapplicant_n.setInvmbal_swft(INVMBAL_SWFT);
		dw_rkapplicant_n.setInvobal_swft(INVOBAL_SWFT);
		dw_rkapplicant_n.setOdep_swft(ODEP_SWFT);
		dw_rkapplicant_n.setPrimary_card(c120m01a.getPrimary_card());
		dw_rkapplicant_n.setAdditional_card(c120m01a.getAdditional_card());
		dw_rkapplicant_n.setBusiness_or_p_card(c120m01a.getBusiness_or_p_card());
		dw_rkapplicant_n.setCompany_id(Util.trim(c120s01b.getJuId()));
		dw_rkapplicant_n.setTotal_capital(c120s01b.getJuTotalCapital());
		dw_rkapplicant_n.setPaidup_capital(c120s01b.getJuPaidUpCapital());
		dw_rkapplicant_n.setCompany_type(Util.trim(c120s01b.getJuType()));
		dw_rkapplicant_n.setFincome(FINCOME);
		dw_rkapplicant_n.setFincome_swft(Util.trim(c120s01c.getFincomeCurr()));
		dw_rkapplicant_n.setC_flag(c_flag);

		data.add(dw_rkapplicant_n);
		return data;
	}
	


	private boolean has_jcic_warning_flag_n(C101S01G_N model) {
		String[] arr = { model.getChkItem1(), model.getChkItem2(),
				model.getChkItem3(), model.getChkItem4(), model.getChkItem5(),
				model.getChkItem6(), model.getChkItem7(), model.getChkItem8() };
		for (String chkItem : arr) {
			if (Util.equals("1", chkItem) || Util.equals("Y", chkItem)) {
				return true;
			}
		}
		return false;
	}

	private boolean has_jcic_warning_flag_n(C101S01Q_N model) {
		String[] arr = { model.getChkItem1(), model.getChkItem2(),
				model.getChkItem3(), model.getChkItem4(), model.getChkItem5(),
				model.getChkItem6(), model.getChkItem7(), model.getChkItem8() };
		for (String chkItem : arr) {
			if (Util.equals("1", chkItem) || Util.equals("Y", chkItem)) {
				return true;
			}
		}
		return false;
	}
	
	private boolean has_jcic_warning_flag_n(C101S01R_N model) {
		String[] arr = { model.getChkItem1(), model.getChkItem2(),
				model.getChkItem3(), model.getChkItem4(), model.getChkItem5(),
				model.getChkItem6(), model.getChkItem7(), model.getChkItem8() };
		for (String chkItem : arr) {
			if (Util.equals("1", chkItem) || Util.equals("Y", chkItem)) {
				return true;
			}
		}
		return false;
	}
	
	
	
	

}
