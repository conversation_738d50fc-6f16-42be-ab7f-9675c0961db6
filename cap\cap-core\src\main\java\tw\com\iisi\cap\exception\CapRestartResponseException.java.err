/*
 * CapRestartResponseException.java
 *
 * Copyright (c) 2009 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
 */
package tw.com.iisi.cap.exception;

import org.apache.wicket.IRequestTarget;
import org.apache.wicket.Page;
import org.apache.wicket.RestartResponseException;

/**
 * <p>
 * CapRestartResponseException.
 * </p>
 * 
 * <AUTHOR>
 * @version <ul>
 *          <li>2010/7/12,iristu,new
 *          </ul>
 */
@SuppressWarnings("serial")
public class CapRestartResponseException extends RestartResponseException {

	/**
	 * Instantiates a new cap restart response exception.
	 * 
	 * @param page
	 *            the page
	 * @param target
	 *            the target
	 */
	public CapRestartResponseException(Page page, IRequestTarget target) {
		super(page);
		page.getRequestCycle().setRequestTarget(target);
	}

}
