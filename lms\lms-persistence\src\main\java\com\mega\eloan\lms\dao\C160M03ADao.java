package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C160M03A;

/** 整批自動開戶 **/
public interface C160M03ADao extends IGenericDao<C160M03A> {

	public C160M03A findByOid(String oid);

	public C160M03A findByMainId(String mainId);
	
	public int findMaxPackNoByCntrNo(String cntrNo);
	
	public List<C160M03A> findByCntrNo(String cntrNo);
}