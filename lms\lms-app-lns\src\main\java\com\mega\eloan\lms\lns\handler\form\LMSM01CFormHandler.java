/* 
 * LMSM01CFormHandler.java 
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lns.handler.form;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.exception.GWException;
import com.mega.eloan.common.formatter.BranchDateTimeFormatter;
import com.mega.eloan.common.gwclient.EaiGwReqMessage;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.BeanValidator;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.eloan.lms.base.panels.LMSS20APanel;
import com.mega.eloan.lms.lms.pages.LMS1605M01Page;
import com.mega.eloan.lms.lns.pages.LMSS02BPage;
import com.mega.eloan.lms.lns.panels.LMSS02BPanel;
import com.mega.eloan.lms.lns.service.LMS1201Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01D;
import com.mega.eloan.lms.model.L120M01I;
import com.mega.eloan.lms.model.L120S01A;
import com.mega.eloan.lms.model.L120S01B;
import com.mega.eloan.lms.model.L120S01C;
import com.mega.eloan.lms.model.L120S01D;
import com.mega.eloan.lms.model.L120S01E;
import com.mega.eloan.lms.model.L120S01F;
import com.mega.eloan.lms.model.L120S01G;
import com.mega.eloan.lms.model.L120S01M;
import com.mega.eloan.lms.model.L120S01N;
import com.mega.eloan.lms.model.L120S01O;
import com.mega.eloan.lms.model.L120S01P;
import com.mega.eloan.lms.model.L120S01T;
import com.mega.eloan.lms.model.L130M01A;
import com.mega.eloan.lms.tej.service.TEJService;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.lms.validation.group.Check2;
import com.mega.eloan.lms.validation.group.Check3;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 國內授信簽報書企金(共用) FormHandler
 * </pre>
 * 
 * @since 2012/10/1
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/10/1,Miller,new
 *          </ul>
 */
@Scope("request")
@DomainClass(L120M01A.class)
public class LMSM01CFormHandler extends LMSM02FormHandler {
	/**
	 * 透過徵信取得借款人客戶資料
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@Resource
	CodeTypeService codeTypeService;
	@Resource
	MisdbBASEService misDbService;
	@Resource
	TEJService tejSrv;
	@Resource
	LMS1201Service service1201;
	@Resource
	UserInfoService userInfoService;

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getCustData2(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		// L120S01B model = service1201.findL120s01bByUniqueKey(mainId, custId,
		// dupNo);

		// Map<String, String> custData = service1201.getCustData(
		// user.getUnitNo(), mainId, mainId);

		// J-111-0443_05097_B1006 Web e-Loan企金授信開發授信BIS評估表
		// 修正因為分行前端網頁問題狀態錯誤，導致已核准簽報書借款人基本資料有顯示引進按鈕
		// 014 28883691
		L120M01A l120m01a = service1201.findL120m01aByMainId(mainId);

		String canEditMsg = lmsService.chkRptDocStatusCanEditBorrower(l120m01a);
		if (Util.isNotEmpty(canEditMsg)) {
			// 文件狀態非「編制中」或「待補件」，不得執行簽報書借款人基本資料引進或異動等相關功能
			throw new CapMessageException(canEditMsg, getClass());
		}

		CapAjaxFormResult formL120s01a = new CapAjaxFormResult();
		// if (model != null) {
		// // formL120s01a = DataParse
		// // .toResult(model, DataParse.Delete, new String[] {
		// // EloanConstants.MAIN_ID, EloanConstants.OID });
		// if (custData != null) {
		// // 總經理
		// formL120s01a.set("gManager", custData.get("gManager"));
		// // 負責人
		// formL120s01a.set("chairman", custData.get("chairman"));
		// // 國別代號(註冊地)
		// formL120s01a.set("ntCode", custData.get("ntCode"));
		// // 公司地址
		// formL120s01a.set("cmpAddr", custData.get("cmpAddr"));
		// // 工廠地址
		// formL120s01a.set("factoryAddr", custData.get("factoryAddr"));
		// // 登記資本額
		// formL120s01a.set("rgtAmt", custData.get("rgtAmt"));
		// // 登記資本額幣別
		// formL120s01a.set("rgtCurr", custData.get("rgtCurr"));
		// // 登記資本額金融單位
		// formL120s01a.set("rgtUnit", custData.get("rgtUnit"));
		// // 實收資本額
		// formL120s01a.set("cptlAmt", custData.get("cptlAmt"));
		// // 實收資本額幣別
		// formL120s01a.set("cptlCurr", custData.get("cptlCurr"));
		// // 實收資本額金額單位
		// formL120s01a.set("cptlUnit", custData.get("cptlUnit"));
		// // 主要股東
		// formL120s01a.set("stockHolder", custData.get("stockHolder"));
		// if (Util.isEmpty(custData.get("groupNo"))
		// || UtilConstants.Mark.SPACE.equals(custData
		// .get("groupNo"))) {
		// // 印出找不到集團資料訊息...
		// } else {
		// // 隸屬集團
		// formL120s01a.set("groupName", custData.get("groupName"));
		// // 隸屬集團代號
		// formL120s01a.set("groupNo", custData.get("groupNo"));
		// }
		// // 成立改組日期
		// formL120s01a.set("estDate", custData.get("estDate"));
		// // 有無赴大陸投資
		// formL120s01a.set("invMFlag", custData.get("invMFlag"));
		// } else {
		// if (params.getAsBoolean("showMsg", true)) {
		// // 印出找不到徵信資料訊息...
		// throw new CapMessageException(RespMsgHelper.getMessage(
		// parent, UtilConstants.AJAX_RSP_MSG.查無資料),
		// getClass());
		// }
		// return result;
		// }
		// }
		// 透過MIS JDBC取得負責人與公司地址
		List<Map<String, Object>> rows1 = this.misCustdataService
				.findCustdataForList(custId, dupNo);
		// J-105-0233-001 Web
		// e-Loan授信系統「註冊地址」及「聯絡地址」引進【0024】之「公司所在地」之建檔資料時可引進英文地址
		// 取得註冊地址
		List<?> rows2 = this.misCustdataService
				.findCustRegisteredAddressForList(custId, dupNo);

		for (Map<String, Object> map1 : rows1) {
			// 負責人統編
			if (Util.isNotEmpty(map1.get("SUP1ID"))) {
				formL120s01a.set("chairmanId", Util.trim(map1.get("SUP1ID")));
				formL120s01a.set("chairmanDupNo",
						Util.isEmpty(Util.trim(map1.get("SUP1DUPNO"))) ? "0"
								: Util.trim(map1.get("SUP1DUPNO")));
			}
			if (Util.isNotEmpty(Util.trim(map1.get("SUP1CNM")))) {
				formL120s01a.set("chairman", Util.trim(map1.get("SUP1CNM")));
			} else if (Util.isNotEmpty(Util.trim(map1.get("SUP3CNM")))) {
				formL120s01a.set("chairman", Util.trim(map1.get("SUP3CNM")));
			}

			// 申貸戶之通訊地址...
			/*
			 * StringBuilder sbAddr = new StringBuilder();
			 * sbAddr.append(Util.trim(map1.get("ADDRZIP"))) .append(" ")
			 * .append(Util.trim(map1.get("CITYR")))
			 * .append(Util.trim(map1.get("TOWNR")))
			 * .append(Util.trim(map1.get(("FALSE".equals(Util
			 * .toFullCharString(Util.trim(map1.get("LINR"))))) ? "LEER" :
			 * "LINR"))).append(Util.trim(map1.get("ADDRR"))); if
			 * (Util.isNotEmpty(Util.trim(sbAddr.toString()))) { // 公司地址
			 * formL120s01a.set("cmpAddr", Util.trim(sbAddr.toString())); }
			 */
			// J-105-0233-001 Web
			// e-Loan授信系統「註冊地址」及「聯絡地址」引進【0024】之「公司所在地」之建檔資料時可引進英文地址
			// 申貸戶之通訊地址..=> 申貸戶之註冊地址...
			Iterator<?> it2 = rows2.iterator();
			if (it2.hasNext()) {
				Map<?, ?> dataMap2 = (Map<?, ?>) it2.next();
				String cmpAddr = Util.trim(Util.nullToSpace(dataMap2
						.get("RGSTADDR")));
				if (!Util.isEmpty(Util.trim(cmpAddr))) {
					// 公司地址
					formL120s01a.set("cmpAddr", Util.trim(cmpAddr));
				}
			}

			// 國別註冊地
			// J-105-0233-001 Web
			// e-Loan授信系統「註冊地址」及「聯絡地址」引進【0024】之「公司所在地」之建檔資料時可引進英文地址
			// NATIONCODE->MOTHERNCODE
			if (Util.isNotEmpty(Util.trim(map1.get("MOTHERNCODE")))) {
				formL120s01a.set("ntCode", Util.trim(map1.get("MOTHERNCODE")));
			} else {
				if (Util.isNotEmpty(Util.trim(map1.get("NATIONCODE")))) {
					formL120s01a.set("ntCode",
							Util.trim(map1.get("NATIONCODE")));
				}
			}

			// 成立改組日期
			if (Util.isNotEmpty(Util.trim(map1.get("BIRTHDT")))) {
				formL120s01a.set("estDate", Util.trim(map1.get("BIRTHDT")));
			}
			break;
		}

		JSONObject jsonCustClass = new JSONObject();
		jsonCustClass = getCustBusCDAndClass(custId, dupNo);

		formL120s01a.set("busCode", jsonCustClass.getString("busCode"));
		formL120s01a.set("bussKind", jsonCustClass.getString("bussKind"));
		formL120s01a.set("ecoNm", jsonCustClass.getString("ecoNm"));
		formL120s01a.set("ecoNm07A", jsonCustClass.getString("ecoNm07A"));
		formL120s01a.set("custClass", jsonCustClass.getString("custClass"));
		formL120s01a.set("displayBusCd",
				jsonCustClass.getString("displayBusCd"));

		// J-108-0143_05097_B1001 Web e-Loan國內外企金額度明細表簽報性質新作時加註(新客戶往來原有客戶往來)
		String newCustFlag = lmsService.applyIsNewCust(custId, dupNo);
		formL120s01a.set("newCustFlag", newCustFlag);

		// J-110-0371 新版簽報書_個人
		if (LMSUtil.isBusCode_060000_130300(jsonCustClass.getString("busCode"))) {

			if (l120m01a != null) {
				String brNo = l120m01a.getOwnBrId();
				Map<String, Object> map = eloanDbBaseService.getCLSInfo(brNo,
						custId, dupNo, false);
				if (map != null && !map.isEmpty()) {
					String jobType1 = Util.trim(map.get("JOBTYPE1"));
					String jobType2 = Util.trim(map.get("JOBTYPE2"));
					Map<String, String> jobType1Map = codeTypeService
							.findByCodeType("jobType");
					Map<String, String> jobType2Map = codeTypeService
							.findByCodeType("jobType" + jobType1);
					String jobType1Str = jobType1;
					String jobType2Str = jobType2;
					if (jobType1Map != null) {
						jobType1Str = Util.trim(jobType1Map.get(jobType1));
					}
					if (jobType2Map != null) {
						jobType2Str = Util.trim(jobType2Map.get(jobType2));
					}
					formL120s01a
							.set("jobType", jobType1Str + "-" + jobType2Str);
					formL120s01a.set("payCurr", Util.trim(map.get("PAYCURR")));
					// 國內單位是萬元要換成元
					BigDecimal payAmt = LMSUtil.toBigDecimal(map.get("PAYAMT"));
					if (payAmt != null && Util.isNotEmpty(payAmt)) {
						payAmt = payAmt.multiply(new BigDecimal(10000));
					} else {
						payAmt = BigDecimal.ZERO;
					}
					formL120s01a.set("payAmt", payAmt);
					formL120s01a.set("eDueDate",
							Util.trim(map.get("ECHKDDATE")));
					formL120s01a.set("rtnChq", Util.trim(map.get("ISQDATA9")));
					formL120s01a.set("blackList",
							Util.trim(map.get("ISQDATA10")));
				}
			}
		}

		result.set("L120S01aForm", formL120s01a);
		if (params.getAsBoolean("showMsg", true)) {
			// 印出執行成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		}
		return result;
	}

	/**
	 * 取得股票資訊並設值到前端
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getStock(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String stockNum = Util.trim(params.getString("stockNum"));
		String errorMsg = "";
		boolean noMsg = params.getBoolean("noMsg");
		L120M01A l120m01a = service1201.findL120m01aByMainId(mainId);
		L120S01B model = service1201.findL120s01bByUniqueKey(mainId, custId,
				dupNo);
		if (model == null) {
			model = new L120S01B();
			model.setMainId(mainId);
			model.setCustId(custId);
			model.setDupNo(dupNo);
		}
		Map<String, Object> map = null;
		if (Util.isNotEmpty(stockNum)) {
			// 有輸入股票代號則用股票代號查詢
			map = service1201.getStock(stockNum, true);
		} else {
			// 無輸入股票代號則預設用借款人統編查詢
			map = service1201.getStock(custId, false);
		}
		if (!map.isEmpty()) {
			CapBeanUtil.map2Bean(map, model);
			service1201.save(model);
			CapAjaxFormResult formL120s01a = new CapAjaxFormResult(map);
			result.set("L120S01aForm", formL120s01a);
		}
		if (result.containsKey("L120S01aForm")) {
			if (!noMsg) {
				// 印出執行成功訊息!
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
						RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
			}
		} else {
			if (!noMsg) {
				throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.查無資料), getClass());
			}

			if (l120m01a != null) {
				// J-110-0CCC_05097_B1001 Web
				// e-Loan新增國發基金協助新創事業紓困融資加碼方案微型企業簽報書格式
				// J-112-0148 疫後振興
				if (lmsService.hidePanelbyCaseType(l120m01a)
						|| lmsService.hidePanelbyCaseType_003(l120m01a)
						|| lmsService.hidePanelbyCaseType_004(l120m01a)
						|| lmsService.hidePanelbyCaseTypeF(l120m01a)) {
					errorMsg = "";
				} else {
					errorMsg = "查無股票資訊！" + "<br/>";
				}

			} else {
				errorMsg = "查無股票資訊！" + "<br/>";
			}

		}
		result.set("errorMsg", errorMsg);
		return result;
	}

	/**
	 * 取得黑名單 ********配合J-106-0238-001 本功能作廢***********************************
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getBlack(PageParameters params) throws CapException {
		StringBuilder temp = new StringBuilder();
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);
		Properties pop2 = MessageBundleScriptCreator
				.getComponentResource(LMS1605M01Page.class);
		CapAjaxFormResult result = new CapAjaxFormResult();

		if (true) {
			// ************************************************************************************
			// J-106-0238-001
			// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
			// 此FUNC 取消，改由AML/CFT 黑名單查詢替代，列印時亦改從AML/CFT讀取資料
			CapAjaxFormResult formL120s01a = new CapAjaxFormResult();
			formL120s01a.set("blackDate", "");
			formL120s01a.set("blackName", "");
			result.set("L120S01aForm", formL120s01a);
			result.set("needInput", false);
			result.set("errorMsg", "");
			return result;
		}

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		boolean noMsg = params.getBoolean("noMsg");
		String errorMsg = "";
		String eName = null;
		String cName = null;
		// 是否為手動輸入英文名稱
		boolean inputEname = params.getBoolean("inputEname");

		L120S01A model = service1201.findL120s01aByUniqueKey(mainId, custId,
				dupNo);
		L120M01A meta = service1201.findL120m01aByMainId(mainId);

		if (!inputEname) {
			if (model != null) {
				Map<String, Object> custData = misCustdataService
						.findAllByByCustIdAndDupNo(custId.toUpperCase(),
								dupNo.toUpperCase());
				if (custData == null || custData.isEmpty()) {
					// EFD3009=ERROR|$\{custId\}客戶中文檔0024 無此借款人資料 ！！|
					HashMap<String, String> param = new HashMap<String, String>();
					param.put("custId",
							custId.toUpperCase() + " " + dupNo.toUpperCase());
					throw new CapMessageException(RespMsgHelper.getMessage("EFD3009", param), getClass());
				}
				eName = Util.trim(custData.get("ENAME"));
				cName = Util.trim(custData.get("CNAME"));
			}
		} else {
			eName = Util.trim(params.getString("eName"));
		}

		// Map<String, Object> map = service1201.getBlack("900",
		// "OSAMA BIN LADEN");
		// 若找到英文名稱才取得黑名單

		// eName = "OSAMA BIN LADEN";

		if (Util.isNotEmpty(eName)) {
			Map<String, Object> map = service1201.getBlack(
					Util.trim(meta.getOwnBrId()), Util.toSemiCharString(eName));
			if (!map.isEmpty()) {
				if ("YES".equals(Util.trim(map.get("SP_RETURN")))) {
					String outResult = Util.trim(map.get("SP_OUTPUT_AREA"));
					if (LMSUtil.checkSubStr(outResult, 1, 3)) {
						// 02:找到黑名單, 04:可能是黑名單
						// other.msg56=找到黑名單
						// other.msg57=可能是黑名單
						String resultCode = outResult.substring(1, 3);
						StringBuilder sb = new StringBuilder();
						sb.setLength(0);
						if ("02".equals(resultCode)) {
							sb.append(custId).append(" ").append(eName)
									.append(" [")
									.append(Util.trim(model.getCustName()))
									.append("] ")
									.append(pop.getProperty("other.msg56"));
						} else if ("04".equals(resultCode)) {
							sb.append(custId).append(" ").append(eName)
									.append(" [")
									.append(Util.trim(model.getCustName()))
									.append("] ")
									.append(pop.getProperty("other.msg57"));
						}
						if (sb.length() > 0) {
							model.setBlackName(sb.toString());
							model.setBlackDate(new Date());
							service1201.save(model);
							CapAjaxFormResult formL120s01a = new CapAjaxFormResult();
							formL120s01a.set("blackDate", CapDate.formatDate(
									model.getBlackDate(),
									UtilConstants.DateFormat.YYYY_MM_DD));
							formL120s01a.set("blackName", sb.toString());
							result.set("L120S01aForm", formL120s01a);
						}
					}
				} else {
					HashMap<String, String> msg = new HashMap<String, String>();
					// other.msg175=查詢中心黑名單失敗(lnsp0130)
					msg.put("msg",
							pop.getProperty("other.msg175")
									+ Util.trim(map.get("SP_ERROR_MSG")));
					if (!noMsg) {
						// 錯誤訊息
						throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, msg),
								getClass());
					}
					errorMsg = pop.getProperty("other.msg175")
							+ Util.trim(map.get("SP_ERROR_MSG")) + "<br/>";
				}
			}
		}
		if (result.containsKey("L120S01aForm")) {
			// 印出執行成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		} else {
			if (!inputEname) {
				// 若以自動查詢英文名稱找不到則傳回請求開啟手動視窗參數
				result.set("needInput", true);
				result.set("eName", eName);
			} else {
				// L160M01A.message39=未列於黑名單
				String msg = pop2.getProperty("L160M01A.message39");
				temp.append(temp.length() > 0 ? "\r" : "");
				temp.append(custId.toUpperCase()).append(" ");
				temp.append(eName);
				if (!Util.isEmpty(Util.trim(cName))) {
					temp.append("【").append(cName).append("】 ");
				}
				temp.append(" ").append(msg);
				model.setBlackName(temp.toString());
				model.setBlackDate(new Date());
				service1201.save(model);
				CapAjaxFormResult formL120s01a = new CapAjaxFormResult();
				formL120s01a.set("blackDate", CapDate.formatDate(
						model.getBlackDate(),
						UtilConstants.DateFormat.YYYY_MM_DD));
				formL120s01a.set("blackName", temp.toString());
				result.set("L120S01aForm", formL120s01a);
				// 手動輸入英文名稱仍查不到資料則顯示查無資料訊息
				// throw new
				// CapMessageException(RespMsgHelper.getMessage(parent,
				// UtilConstants.AJAX_RSP_MSG.查無資料), getClass());
			}
		}
		result.set("errorMsg", errorMsg);
		return result;
	}

	/**
	 * 取得 申貸戶 婉卻紀錄
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getReject(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		boolean noMsg = params.getBoolean("noMsg");
		L120S01A model = service1201.findL120s01aByUniqueKey(mainId, custId,
				dupNo);
		List<Map<String, Object>> listMap = service1201.findReject(custId,
				dupNo);
		if (!listMap.isEmpty()) {
			// 有婉卻紀錄
			for (Map<String, Object> map : listMap) {
				// 是否本行有婉卻紀錄
				model.setIsRejt(UtilConstants.DEFAULT.是);
				// 查詢日期
				model.setRejtReadDate(new Date());
				// 登錄分行
				model.setRejtBrNo(Util.trim(map.get("REGBR")));
				// 婉卻代碼
				model.setRejtCode(Util.parseInt(map.get("REFUSECD")));
				// 婉卻理由
				model.setRejtReason(Util.trim(map.get("REFUSEDS")));
				// 登錄時間
				model.setRejtDate(CapDate.convertStringToTimestamp(Util
						.trim(map.get("REGDT"))));
				// 婉卻控管種類(若查無資料則預設塞維持控管)
				model.setRejtCase((Util.isNotEmpty(Util.trim(map
						.get("STATUSCD")))) ? Util.trim(map.get("STATUSCD"))
						: UtilConstants.Casedoc.rejtCase.維持控管);

				service1201.save(model);
				String needCols[] = new String[] { "IsRejt", "rejtReadDate",
						"rejtBrNo", "rejtCode", "rejtReason", "rejtCase" };
				CapAjaxFormResult formL120s01a = DataParse.toResult(model,
						DataParse.Need, needCols);
				SimpleDateFormat formatter = new SimpleDateFormat(
						UtilConstants.DateFormat.YYYY_MM_DD_HH_MM_SS);
				formL120s01a.set("rejtBrNo",
						getRejtBrName(Util.trim(formL120s01a.get("rejtBrNo"))));
				formL120s01a
						.set("rejtCode", getRejtCodeName(Util.trim(formL120s01a
								.get("rejtCode"))));
				formL120s01a.set("rejtDate", Util.isNotEmpty(model
						.getRejtDate()) ? formatter.format(model.getRejtDate())
						: UtilConstants.Mark.SPACE);
				result.set("L120S01aForm", formL120s01a);
				break;
			}
		}
		if (result.containsKey("L120S01aForm")) {
			if (!noMsg) {
				// 印出執行成功訊息!
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
						RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
			}
		} else {
			// 是否本行有婉卻紀錄
			model.setIsRejt(UtilConstants.DEFAULT.否);
			// 查詢日期
			model.setRejtReadDate(new Date());
			// 登錄分行
			model.setRejtBrNo(null);
			// 婉卻代碼
			model.setRejtCode(null);
			// 婉卻理由
			model.setRejtReason(null);
			// 登錄時間
			model.setRejtDate(null);
			// 婉卻控管種類(若查無資料則預設塞維持控管)
			model.setRejtCase(UtilConstants.Mark.SPACE);

			service1201.save(model);
			CapAjaxFormResult formL120s01a = new CapAjaxFormResult();
			formL120s01a.set("IsRejt", Util.trim(model.getIsRejt()));
			formL120s01a.set("rejtReadDate", CapDate.formatDate(
					model.getRejtReadDate(),
					UtilConstants.DateFormat.YYYY_MM_DD));

			formL120s01a.set("rejtBrNo", UtilConstants.Mark.SPACE);
			formL120s01a.set("rejtCode", UtilConstants.Mark.SPACE);
			formL120s01a.set("rejtDate", UtilConstants.Mark.SPACE);
			formL120s01a.set("rejtCase", " ");

			result.set("L120S01aForm", formL120s01a);
			// throw new CapMessageException(RespMsgHelper.getMessage(parent,
			// UtilConstants.AJAX_RSP_MSG.查無資料), getClass());
		}
		return result;
	}

	/**
	 * 取得 負責人 婉卻紀錄
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getReject1(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String chairmanId = Util.trim(params.getString("chairmanId"));
		String chairmanDupNo = Util.trim(params.getString("chairmanDupNo"));
		boolean noMsg = params.getBoolean("noMsg");
		boolean chairmanVisible = params.getAsBoolean("chairmanVisible", true);
		boolean isPassed = false;
		if (Util.isEmpty(chairmanId)) {
			chairmanId = "";
			chairmanDupNo = "";
		} else {
			if (Util.isEmpty(chairmanDupNo)) {
				chairmanDupNo = "0";
			}
		}
		isPassed = true;
		if (chairmanVisible == false) {
			// 格式為[其他 ; 陳復案/陳述案], 企金戶在UI上的負責人欄位為hidden
			chairmanId = "";
			chairmanDupNo = "";
		}
		// ---
		L120S01A model = service1201.findL120s01aByUniqueKey(mainId, custId,
				dupNo);
		List<Map<String, Object>> listMap = new ArrayList<Map<String, Object>>();
		if (Util.isNotEmpty(chairmanId) && Util.isNotEmpty(chairmanDupNo)) {
			listMap.addAll(service1201.findReject(chairmanId, chairmanDupNo));

			if (!listMap.isEmpty()) {
				// 有婉卻紀錄
				for (Map<String, Object> map : listMap) {
					// 是否本行有婉卻紀錄
					model.setIsRejt1(UtilConstants.DEFAULT.是);
					// 查詢日期
					model.setRejtReadDate1(new Date());
					// 登錄分行
					model.setRejtBrNo1(Util.trim(map.get("REGBR")));
					// 婉卻代碼
					model.setRejtCode1(Util.parseInt(map.get("REFUSECD")));
					// 婉卻理由
					model.setRejtReason1(Util.trim(map.get("REFUSEDS")));
					// 登錄時間
					model.setRejtDate1(CapDate.convertStringToTimestamp(Util
							.trim(map.get("REGDT"))));
					// 婉卻控管種類(若查無資料則預設塞維持控管)
					model.setRejtCase1((Util.isNotEmpty(Util.trim(map
							.get("STATUSCD")))) ? Util.trim(map.get("STATUSCD"))
							: UtilConstants.Casedoc.rejtCase.維持控管);

					if (Util.equals(chairmanId, model.getRejtChairmanId(),
							false)
							&& Util.equals(chairmanDupNo,
									model.getRejtChairmanDupNo(), false)) {

					} else {
						model.setRejtCaseAdjMemo1(null);
						model.setRejtCaseBefore1(null);

						model.setRejtChairmanId(chairmanId);
						model.setRejtChairmanDupNo(chairmanDupNo);
					}
					service1201.save(model);
					String needCols[] = new String[] { "IsRejt1",
							"rejtReadDate1", "rejtBrNo1", "rejtCode1",
							"rejtReason1", "rejtCase1", "rejtChairmanId",
							"rejtChairmanDupNo" };
					CapAjaxFormResult formL120s01a = DataParse.toResult(model,
							DataParse.Need, needCols);
					SimpleDateFormat formatter = new SimpleDateFormat(
							UtilConstants.DateFormat.YYYY_MM_DD_HH_MM_SS);
					formL120s01a.set("rejtBrNo1", getRejtBrName(Util
							.trim(formL120s01a.get("rejtBrNo1"))));
					formL120s01a.set("rejtCode1", getRejtCodeName(Util
							.trim(formL120s01a.get("rejtCode1"))));
					formL120s01a.set(
							"rejtDate1",
							Util.isNotEmpty(model.getRejtDate1()) ? formatter
									.format(model.getRejtDate1())
									: UtilConstants.Mark.SPACE);
					result.set("L120S01aForm", formL120s01a);
					break;
				}
			}
		}

		if (result.containsKey("L120S01aForm")) {
			if (!noMsg) {
				// 印出執行成功訊息!
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
						.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
			}
		} else {
			if (Util.isNotEmpty(chairmanId) && Util.isNotEmpty(chairmanDupNo)) {
				// 是否本行有婉卻紀錄
				model.setIsRejt1(UtilConstants.DEFAULT.否);
				// 查詢日期
				model.setRejtReadDate1(new Date());

				if (Util.equals(chairmanId, model.getRejtChairmanId(), false)
						&& Util.equals(chairmanDupNo,
								model.getRejtChairmanDupNo(), false)) {

				} else {
					model.setRejtBrNo1(null);
					model.setRejtCode1(null);
					model.setRejtReason1(null);
					model.setRejtDate1(null);
					model.setRejtCase1(null);
					model.setRejtCaseAdjMemo1(null);
					model.setRejtCaseBefore1(null);

					model.setRejtChairmanId(chairmanId);
					model.setRejtChairmanDupNo(chairmanDupNo);
				}
				service1201.save(model);
				CapAjaxFormResult formL120s01a = new CapAjaxFormResult();
				formL120s01a.set("IsRejt1", Util.trim(model.getIsRejt1()));
				formL120s01a.set("rejtReadDate1", CapDate.formatDate(
						model.getRejtReadDate1(),
						UtilConstants.DateFormat.YYYY_MM_DD));
				formL120s01a.set("rejtChairmanId",
						Util.trim(model.getRejtChairmanId()));
				formL120s01a.set("rejtChairmanDupNo",
						Util.trim(model.getRejtChairmanDupNo()));
				result.set("L120S01aForm", formL120s01a);
			} else {
				model.setIsRejt1(null);
				model.setRejtReadDate1(null);
				model.setRejtBrNo1(null);
				model.setRejtCode1(null);
				model.setRejtReason1(null);
				model.setRejtDate1(null);
				model.setRejtCase1(null);
				model.setRejtCaseAdjMemo1(null);
				model.setRejtCaseBefore1(null);

				model.setRejtChairmanId(chairmanId);
				model.setRejtChairmanDupNo(chairmanDupNo);

				service1201.save(model);
				CapAjaxFormResult formL120s01a = new CapAjaxFormResult();
				formL120s01a.set("IsRejt1", Util.trim(model.getIsRejt1()));
				formL120s01a.set("rejtReadDate1", "");
				formL120s01a.set("rejtChairmanId",
						Util.trim(model.getRejtChairmanId()));
				formL120s01a.set("rejtChairmanDupNo",
						Util.trim(model.getRejtChairmanDupNo()));
				result.set("L120S01aForm", formL120s01a);
			}
		}
		result.set("isPassed", isPassed);
		return result;
	}

	/**
	 * 修改 申貸戶 婉卻控管種類
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult editReject(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		StringBuilder sb = new StringBuilder();
		sb.append(user.getUserCName())
				.append("(")
				.append(CapDate.getDateTimeFormat(CapDate.getCurrentTimestamp()))
				.append(")");
		CapAjaxFormResult result = new CapAjaxFormResult();
		CapAjaxFormResult L120S01aForm = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String _rejtCase = Util.trim(params.getString("_rejtCase"));
		String _rejtCaseBefore = Util.trim(params.getString("_rejtCaseBefore"));
		L120S01A model = service1201.findL120s01aByUniqueKey(mainId, custId,
				dupNo);
		if (model != null) {
			model.setRejtCase(_rejtCase);
			if (Util.isEmpty(Util.trim(model.getRejtCaseBefore()))) {
				model.setRejtCaseBefore(_rejtCaseBefore);
			}
			model.setRejtCaseAdjMemo(sb.toString());
			service1201.save(model);
		}
		L120S01aForm.set("rejtCase", _rejtCase);
		L120S01aForm
				.set("rejtCaseBefore", Util.trim(model.getRejtCaseBefore()));
		L120S01aForm.set("rejtCaseAdjMemo", sb.toString());
		result.set("L120S01aForm", L120S01aForm);
		// 印出儲存成功訊息!
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));
		return result;
	}

	/**
	 * 修改 負責人 婉卻控管種類
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult editReject1(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		StringBuilder sb = new StringBuilder();
		sb.append(user.getUserCName())
				.append("(")
				.append(CapDate.getDateTimeFormat(CapDate.getCurrentTimestamp()))
				.append(")");
		CapAjaxFormResult result = new CapAjaxFormResult();
		CapAjaxFormResult L120S01aForm = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String _rejtCase1 = Util.trim(params.getString("_rejtCase1"));
		String _rejtCaseBefore1 = Util.trim(params
				.getString("_rejtCaseBefore1"));
		L120S01A model = service1201.findL120s01aByUniqueKey(mainId, custId,
				dupNo);
		if (model != null) {
			model.setRejtCase1(_rejtCase1);
			if (Util.isEmpty(Util.trim(model.getRejtCaseBefore1()))) {
				model.setRejtCaseBefore1(_rejtCaseBefore1);
			}
			model.setRejtCaseAdjMemo1(sb.toString());
			service1201.save(model);
		}
		L120S01aForm.set("rejtCase1", _rejtCase1);
		L120S01aForm.set("rejtCaseBefore1",
				Util.trim(model.getRejtCaseBefore1()));
		L120S01aForm.set("rejtCaseAdjMemo1", sb.toString());
		result.set("L120S01aForm", L120S01aForm);
		// 印出儲存成功訊息!
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));
		return result;
	}

	/**
	 * 依照婉卻登錄分行代碼取得婉卻分行名稱
	 * 
	 * @param rejtBrNo
	 * @return
	 */
	private String getRejtBrName(String rejtBrNo) {
		StringBuilder sb = new StringBuilder();
		sb.setLength(0);
		if (Util.isNotEmpty(rejtBrNo)) {
			sb.append(rejtBrNo).append(" ")
					.append(branchSrv.getBranchName(rejtBrNo));
		}
		return sb.toString();
	}

	/**
	 * 依照婉卻代碼取得代碼說明
	 * 
	 * @param rejtCode
	 *            婉卻代碼
	 * @return 代碼說明
	 */
	private String getRejtCodeName(String rejtCode) {
		StringBuilder sb = new StringBuilder();
		sb.setLength(0);
		if (Util.isNotEmpty(rejtCode)) {
			sb.append(rejtCode)
					.append("-")
					.append(codeService.getDescOfCodeType("RejtCode", rejtCode));
		}
		return sb.toString();
	}

	/**
	 * 將使用者選擇集團資料代入至借款人隸屬集團
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getGrpData(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String selKey = Util.trim(params.getString("selKey"));
		String selVal = Util.trim(params.getString("selVal"));
		CapAjaxFormResult formL120s01a = new CapAjaxFormResult();
		// setItem 把Key 和Value設顛倒了，所以在這邊Key = value, Value = key
		formL120s01a.set("groupNo", selVal);
		formL120s01a.set("groupName",
				LMSUtil.checkSubStr(selKey, 5) ? UtilConstants.Mark.SPACE
						: selKey.substring(5));
		result.set("L120S01aForm", formL120s01a);
		return result;
	}

	/**
	 * <pre>
	 * 新增借款人主檔(企金)
	 * 
	 * @param params PageParameters
	 * @return IResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult addBorrowMain(PageParameters params) throws CapException {
		IBranch iBranch = branchSrv.getBranch(MegaSSOSecurityContext
				.getUnitNo());
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String docType = Util.trim(params.getString("docType"));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String custName = Util.trim(params.getString("custName"));
		String buscd = Util.trim(params.getString("buscd"));
		String renCd = Util.trim(params.getString("renCd"));
		String typCd = LMSUtil.isObuId(custId) ? UtilConstants.Casedoc.typCd.OBU
				: UtilConstants.Casedoc.typCd.DBU;
		boolean check = params.getBoolean("check");
		JSONObject addborrowJson = new JSONObject();
		addborrowJson.put("docType", docType);
		// addborrowJson.put("typCd", typCd);
		addborrowJson.put("custId", custId);
		addborrowJson.put("dupNo", dupNo);
		addborrowJson.put("custName", Util.truncateString(custName, 120));
		addborrowJson.put(EloanConstants.MAIN_ID, mainId);
		addborrowJson.put("creator", user.getUserId());
		addborrowJson
				.put("createTime", new BranchDateTimeFormatter(iBranch)
						.reformat(CapDate.parseToString(CapDate
								.getCurrentTimestamp())));

		JSONObject jsonCustClass = new JSONObject();
		jsonCustClass = getCustBusCDAndClass(custId, dupNo);
		if (true) {
			String busCode = jsonCustClass.optString("busCode");
			if (LMSUtil.isBusCode_060000_130300(busCode)) {
				String errMsg = clsService.check0024_23_LUV_DEPT_2(custId,
						dupNo);
				if (Util.isNotEmpty(errMsg)) {
					throw new CapMessageException(errMsg, getClass());
				}
			}
		}

		addborrowJson.put("busCode", jsonCustClass.getString("busCode"));
		addborrowJson.put("bussKind", jsonCustClass.getString("bussKind"));
		addborrowJson.put("ecoNm", jsonCustClass.getString("ecoNm"));
		addborrowJson.put("ecoNm07A", jsonCustClass.getString("ecoNm07A"));
		addborrowJson.put("custClass", jsonCustClass.getString("custClass"));
		addborrowJson.put("displayBusCd",
				jsonCustClass.getString("displayBusCd"));

		L120M01A l120m01a = service1201.findL120m01aByMainId(mainId);
		CapAjaxFormResult showBorrowData = new CapAjaxFormResult();
		if (check) {
			addborrowJson.put("keyMan", UtilConstants.DEFAULT.是);
			// 當是主要借款人且為第一次新增時
			l120m01a.setCustId(custId);
			l120m01a.setDupNo(dupNo);
			l120m01a.setCustName(Util.truncateString(custName, 120));
			l120m01a.setTypCd(typCd);
			// 小規模營業人

			// J-109-0KKK_05097_B1001 簡化青年創業及啟動金貸款簽報書簽案流程

			// if (Util.equals(l120m01a.getMiniFlag(), "Y")
			// && Util.equals(l120m01a.getCaseType(),
			// UtilConstants.Casedoc.caseType.小規模營業人)) {

			if (lmsService.hidePanelbyCaseType(l120m01a)
					|| lmsService.hidePanelbyCaseTypeF(l120m01a)) {
				// J-112-0148 疫後振興不用預設借款用途
				if (Util.notEquals(l120m01a.getCaseType(),
						UtilConstants.Casedoc.caseType.疫後振興F02)
						&& Util.notEquals(l120m01a.getCaseType(),
								UtilConstants.Casedoc.caseType.疫後振興F02_無利息補貼)) {
					if (Util.isEmpty(Util.trim(l120m01a.getPurpose()))) {
						l120m01a.setPurpose(UtilConstants.Casedoc.purpose.營運週轉金);
					}
				}
				if (Util.isEmpty(Util.trim(l120m01a.getResource()))) {
					l120m01a.setResource("1");
				}
			}

			// J-109-0KKK_05097_B1001 簡化青年創業及啟動金貸款簽報書簽案流程
			if (lmsService.hidePanelbyCaseType_lnType61(l120m01a)) {
				if (Util.isEmpty(Util.trim(l120m01a.getPurpose()))) {
					l120m01a.setPurpose(UtilConstants.Casedoc.purpose.其他);
					l120m01a.setPurposeOth("營運準備金及開辦費、週轉性支出與資本性支出(請依借款用途別填列)");
				}
				if (Util.isEmpty(Util.trim(l120m01a.getResource()))) {
					l120m01a.setResource("1");
				}
			}

			// J-110-0CCC_05097_B1001 Web e-Loan新增國發基金協助新創事業紓困融資加碼方案微型企業簽報書格式
			if (lmsService.hidePanelbyCaseType_003(l120m01a)
					|| lmsService.hidePanelbyCaseType_004(l120m01a)) {
				if (Util.isEmpty(Util.trim(l120m01a.getPurpose()))) {
					l120m01a.setPurpose(UtilConstants.Casedoc.purpose.其他);
					l120m01a.setPurposeOth("因應疫情需要及未來事業發展用。");
				}
				if (Util.isEmpty(Util.trim(l120m01a.getResource()))) {
					l120m01a.setResource("1");
				}
			}

			// J-110-0458 企金授權內其他 - 「簡易簽報」選項，適用方案「LIBOR退場變更利率條件簡易簽報」
			// 說明塞預設值
			if (lmsService.isLiborExitCase(l120m01a)) {
				Map<String, String> dscrMap = codeTypeService.findByCodeType(
						"liborExitDscr", LMSUtil.getLocale().toString());
				L120M01D l120m01d03 = service1201.findL120m01dByUniqueKey(
						mainId, UtilConstants.Casedoc.L120m01dItemType.其他);
				if (l120m01d03 != null) {
					// 如果是空的，就可以塞值
					if (Util.isEmpty(Util.nullToSpace(l120m01d03.getItemDscr()))) {
						// 依據授信審查處中華民國XX.XX.XX總授審字第XXXXXX函辦理。
						String dscr = Util.nullToSpace(dscrMap.get("dscr"));
						l120m01d03.setItemDscr(dscr);
						service1201.save(l120m01d03);
					}
				}
			}
			if (lmsService.isEuroyenTiborExitCase(l120m01a)) {
				Map<String, String> dscrMap = codeTypeService.findByCodeType(
						"EuroyenTiborExitDscr", LMSUtil.getLocale().toString());
				L120M01D l120m01d03 = service1201.findL120m01dByUniqueKey(
						mainId, UtilConstants.Casedoc.L120m01dItemType.其他);
				if (l120m01d03 != null) {
					if (Util.isEmpty(Util.nullToSpace(l120m01d03.getItemDscr()))) {
						String dscr = Util.nullToSpace(dscrMap.get("dscr"));
						l120m01d03.setItemDscr(dscr);
						service1201.save(l120m01d03);
					}
				}
			}

			showBorrowData.set("custId", custId);
			showBorrowData.set("dupNo", dupNo);
			showBorrowData.set("custName", Util.truncateString(custName, 120));
			showBorrowData.set("typCd", getMessage("typCd." + typCd));
			// showBorrowData.set("typCd", TypCdEnum.getEnum(typCd).name());
			// result.set("showBorrowData", showBorrowData);
		} else {
			addborrowJson.put("keyMan", UtilConstants.DEFAULT.否);
			showBorrowData.set("typCd",
					getMessage("typCd." + Util.trim(l120m01a.getTypCd())));
		}
		result.set("showBorrowData", showBorrowData);
		// 以下建立相關聯的資料表
		L120S01A l120s01a = new L120S01A();
		L120S01B l120s01b = new L120S01B();
		L120S01D l120s01d = new L120S01D();
		L120S01F l120s01f = new L120S01F();
		L120S01G l120s01g_1 = new L120S01G();
		L120S01G l120s01g_2 = new L120S01G();
		// 到此結束
		DataParse.toBean(addborrowJson, l120s01a); // 將addborrowJson data 置入
		l120s01a.setRenCd(Util.trim(renCd));
		l120s01a.setBusCode(Util.trim(buscd));
		l120s01a.setTypCd(typCd);
		// L120S01A
		DataParse.toBean(addborrowJson, l120s01b); // 將addborrowJson data 置入
													// L120S01B
		DataParse.toBean(addborrowJson, l120s01d); // 將addborrowJson data 置入
													// L120S01D
		DataParse.toBean(addborrowJson, l120s01f); // 將addborrowJson data 置入
													// L120S01F
		DataParse.toBean(addborrowJson, l120s01g_1); // 將addborrowJson data 置入
														// L120S01G_1
		DataParse.toBean(addborrowJson, l120s01g_2); // 將addborrowJson data 置入
														// L120S01G_2
		// 以下設定值給新建立的資料表
		l120s01g_1.setDataType(UtilConstants.Casedoc.L120s01gType.營運概況分析與評估);
		l120s01g_1.setDataDscr(UtilConstants.Mark.SPACE);
		l120s01g_2.setDataType(UtilConstants.Casedoc.L120s01gType.財務狀況分析與評估);
		l120s01g_2.setDataDscr(UtilConstants.Mark.SPACE);
		// 再來儲存資料
		L120M01A model = service1201.findL120m01aByMainId(mainId);

		// J-104-0222-001 Web e-Loan授信簽報系統中的借款人列印順序調整
		if (l120s01a != null) {
			Integer focusSeq = service1201
					.addL120S01ACustShowSeqNumToMax(l120s01a);
			if (focusSeq != null) {
				l120s01a.setCustShowSeqNum(focusSeq);
			}
		}

		// J-104-0222-001 Web e-Loan授信簽報系統中的借款人列印順序調整
		if (l120s01a != null) {
			Properties prop = MessageBundleScriptCreator
					.getComponentResource(LMSS02BPanel.class);

			boolean setFlag = service1201
					.resetL120S01AAllCustShowSeqNum(l120s01a.getMainId());
			if (setFlag == false) {
				// l120s02.alert33=重新設定借款人基本資料顯示順序錯誤(SEQNUM)
				Map<String, String> param = new HashMap<String, String>();
				param.put("noticeMsg", prop.getProperty("l120s02.alert33"));
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0015", param), getClass());
			}
		}

		service1201.save(model, l120s01a, l120s01b, l120s01d, l120s01f,
				l120s01g_1, l120s01g_2);
		result.set(EloanConstants.OID, l120s01a.getOid());
		CapAjaxFormResult tadd = DataParse.toResult(l120s01a, DataParse.Delete,
				new String[] { EloanConstants.MAIN_ID, EloanConstants.OID,
						"typCd" }); // 處理新增後客戶型態顯示
		CapAjaxFormResult tadd2 = new CapAjaxFormResult(); // 處理營運概況分析評估
		CapAjaxFormResult tadd3 = new CapAjaxFormResult(); // 處理財務概況分析評估
		CapAjaxFormResult tadd4 = new CapAjaxFormResult(jsonCustClass); // 行業對象別與客戶類別
		// 依據不同的客戶型態顯示相對應結果
		tadd.set("typCd", getMessage("typCd." + typCd));
		// tadd.set("typCd", TypCdEnum.getEnum(l120s01a.getTypCd()).name());
		tadd.set("invMDscr", UtilConstants.Mark.SPACE);
		tadd.set("_renCd", renCd);
		tadd.set("_buscd", buscd);
		tadd2.set("idDscr1", UtilConstants.Mark.HTMLSPACE);
		tadd3.set("idDscr2", UtilConstants.Mark.HTMLSPACE);

		tadd.add(tadd4);

		result.set("L120S01aForm", tadd); // 將model轉為回傳物件並置指定的formName
		result.set("formIdDscr1", tadd2); // 將model轉為回傳物件並置指定的formName
		result.set("formIdDscr2", tadd3); // 將model轉為回傳物件並置指定的formName

		// 增加取得營運概況的預設中文名稱
		CapAjaxFormResult resultl120s01g1 = new CapAjaxFormResult();
		JSONObject l120s01eKind1Data = service1201.getL120s01eKind1Data(mainId,
				custId, dupNo, ' ', ' ');
		resultl120s01g1.putAll(new CapAjaxFormResult(l120s01eKind1Data));
		result.set("L120S01gForm_1", resultl120s01g1);

		return result;
	}

	/**
	 * <pre>
	 * 刪除-(企金)(借款人資料)
	 * 
	 * @param params PageParameters
	 * @return IResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteBorrowMain(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		L120S01A l120s01a = service1201.findL120s01aByOid(oid);
		String custId = Util.trim(l120s01a.getCustId());
		String dupNo = Util.trim(l120s01a.getDupNo());
		L120S01B l120s01b = service1201.findL120s01bByUniqueKey(mainId, custId,
				dupNo);
		List<L120S01C> list = service1201.findL120s01cByMainId(mainId);
		List<L120S01C> listDel = new ArrayList<L120S01C>();
		for (L120S01C model : list) {
			if (custId.equals(Util.trim(model.getCustId()))
					&& dupNo.equals(Util.trim(model.getDupNo()))) {
				listDel.add(model);
			}
		}
		List<L120S01M> listL120S01M = service1201.findL120s01mByCustId(mainId,
				custId, dupNo);
		List<L120S01N> listL120S01N = service1201.findL120s01nByCustId(mainId,
				custId, dupNo);
		List<L120S01O> listL120S01O = service1201.findL120s01oByCustId(mainId,
				custId, dupNo);

		// J-106-0029-003 洗錢防制-新增實質受益人
		List<L120S01P> listL120s01p = amlRelateService
				.findL120s01pByMainIdAndCustIdWithoutRType(mainId, custId,
						dupNo);

		L120S01D l120s01d = service1201.findL120s01dByUniqueKey(mainId, custId,
				dupNo);
		L120S01G l120s01g_1 = service1201.findL120s01gByUniqueKey(mainId,
				custId, dupNo, UtilConstants.Casedoc.L120s01gType.營運概況分析與評估);
		L120S01G l120s01g_2 = service1201.findL120s01gByUniqueKey(mainId,
				custId, dupNo, UtilConstants.Casedoc.L120s01gType.財務狀況分析與評估);
		L120S01F l120s01f = service1201.findL120s01fByUniqueKey(mainId, custId,
				dupNo);
		CapAjaxFormResult showBorrowData = new CapAjaxFormResult();
		if (UtilConstants.DEFAULT.是.equals(l120s01a.getKeyMan())) {
			L120M01A l120m01a = service1201.findL120m01aByMainId(l120s01a
					.getMainId());
			l120m01a.setCustId(UtilConstants.Mark.SPACE);
			l120m01a.setDupNo(UtilConstants.Mark.SPACE);
			l120m01a.setCustName(UtilConstants.Mark.SPACE);
			l120m01a.setTypCd(UtilConstants.Mark.SPACE);
			showBorrowData.set("custId", UtilConstants.Mark.SPACE);
			showBorrowData.set("dupNo", UtilConstants.Mark.SPACE);
			showBorrowData.set("custName", UtilConstants.Mark.SPACE);
			showBorrowData.set("typCd", UtilConstants.Mark.SPACE);

			// J-106-0029-003 洗錢防制-新增實質受益人
			service1201.delBorrow(l120m01a, l120s01a, l120s01b, l120s01d,
					l120s01f, l120s01g_1, l120s01g_2, listDel, listL120S01M,
					listL120S01N, listL120S01O, listL120s01p);
		} else {
			// 刪除符合的資料
			try {
				// J-106-0029-003 洗錢防制-新增實質受益人
				service1201.delBorrow(null, l120s01a, l120s01b, l120s01d,
						l120s01f, l120s01g_1, l120s01g_2, listDel,
						listL120S01M, listL120S01N, listL120S01O, listL120s01p);
			} catch (Exception e) {
				logger.error(e.getMessage());
			}
		}

		// J-104-0222-001 Web e-Loan授信簽報系統中的借款人列印順序調整
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMSS02BPanel.class);

		boolean setFlag = service1201.resetL120S01AAllCustShowSeqNum(mainId);
		if (setFlag == false) {
			// l120s02.alert33=重新設定借款人基本資料顯示順序錯誤(SEQNUM)
			Map<String, String> param = new HashMap<String, String>();
			param.put("noticeMsg", prop.getProperty("l120s02.alert33"));
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0015", param), getClass());
		}

		result.set("showBorrowData", showBorrowData);
		// 印出刪除成功訊息!
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.刪除成功));
		return result;
	}// ;

	/**
	 * <pre>
	 * 查詢銀行法及金控法44 45條(企金)
	 * 
	 * @param params PageParameters
	 * @return IResult CapAjaxFormResult
	 * @throws CapException
	 * </pre>
	 */
	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getRlt(PageParameters params) throws CapException {
		Properties pop2 = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);

		/*
		 * 重要!!國內與海外不同**************************************************** noMsg
		 * false:顯示錯誤訊息(執行各別查詢按鈕) true:不顯示錯誤訊息(整批引進客戶資料)
		 */
		boolean noMsg = params.getBoolean("noMsg");

		String errorMsg = "";
		L120S01A model = service1201.findL120s01aByOid(oid);
		JSONArray jsons = new JSONArray();
		JSONObject jsonData = new JSONObject();
		jsonData.put("REID", Util.trim(model.getCustId()));
		jsonData.put("DUPNO", Util.trim(model.getDupNo()));
		jsonData.put("RelId", Util.trim(model.getCustId()));
		jsonData.put("LawNo", "44");
		jsons.add(jsonData);
		CapAjaxFormResult formL120s01a = new CapAjaxFormResult();

		formL120s01a.set("mhRlt", "");
		formL120s01a.set("mhRlt44", "");
		formL120s01a.set("mhRlt45", "");

		// 查詢44,45,銀行法有無資料
		// 2015-07-09 加速利害關係人查詢
		// 查詢44,45,銀行法有無資料可用底下其他隻替代(eaireq44、eaireq45 EAI1000
		// 代表N)，且重覆含misDbService.selRlt，故取消
		// EaiGwReqMessage req = new EaiGwReqMessage("MisElremainService",
		// "checkBankAndLaw44REByIdDup", UtilConstants.Mark.SPACE);

		// 查詢44 詳細資料
		EaiGwReqMessage eaireq44 = new EaiGwReqMessage("ELRPS",
				"EaiCommonService", "IPSCO01", IDGenerator.getUUID());

		// 查詢銀行法有無利害關係人
		List<Map<String, Object>> listMap = misDbService.selRlt(Util.trim(model
				.getCustId()));

		JSONObject jsonParam44 = new JSONObject();
		// jsonParam44.element("RelId", "A201088109");
		jsonParam44.put("RelId", Util.trim(model.getCustId()));
		jsonParam44.put("LawNo", "44");
		jsonParam44.put("compare", UtilConstants.Mark.SPACE);
		jsonParam44.put("check", "0");

		eaireq44.setQueryParam(jsonParam44);

		// 查詢45 詳細資料
		EaiGwReqMessage eaireq45 = new EaiGwReqMessage("ELRPS",
				"EaiCommonService", "IPSCO01", IDGenerator.getUUID());
		JSONObject jsonParam45 = new JSONObject();
		// jsonParam45.element("RelId", "A201088109");
		jsonParam45.put("RelId", Util.trim(model.getCustId()));
		jsonParam45.put("LawNo", "45");
		jsonParam45.put("compare", UtilConstants.Mark.SPACE);
		jsonParam45.put("check", "0");

		eaireq45.setQueryParam(jsonParam45);

		// req.setAryParamsJSONAry(jsons);
		JSONArray rltJsons = new JSONArray();
		JSONArray resp44 = new JSONArray();
		JSONArray resp45 = new JSONArray();
		// try {
		// rltJsons = eaiGwSrv.send(req);
		// } catch (GWException e) {
		// }
		try {
			resp44 = eaiGwSrv.send(eaireq44);
			JSONObject jo = resp44.getJSONObject(0);
			if (!jo.isEmpty() && "1".equals(jo.getString("checkData"))) {
				JSONArray rnData = jo.getJSONArray("rtnData");
				jsonParam44.put("EName",
						rnData.getJSONObject(0).get("R_RELNAME"));
				// {"userInfo":"資訊處@003966金至忠","appId":"ELRPS","serviceId":"EaiCommonService","queryId":"IPSCO01","queryParams":{"RelId":"03705903","EName":"(017)兆某某某某某某某","compare":"","check":"0","LawNo":"44"}}
				// {"appId":"ELRPS","serviceId":"EaiCommonService","queryId":"IPSCO01","userInfo":"忠孝分行@005001主管１","queryParams":{"RelId":"03705903","LawNo":"44","compare":"","check":"0"}}
				eaireq44.setQueryParam(jsonParam44);
				resp44 = eaiGwSrv.send(eaireq44);
			}
			formL120s01a.set("mhRlt44", "1");

		} catch (GWException e) {
			if (e.getMessage().startsWith("EAI 1000")) {
				// EAI 1000 代表非利害關係人(查無資料)
				formL120s01a.set("mhRlt44", "2");
			} else {
				// 非EAI 1000，代表查詢有問題，回傳錯誤訊息
				if (!noMsg) {
					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.查無資料),
							getClass());
				}
				errorMsg = errorMsg + "查詢失敗！！，金控連線錯誤，無法取得金控法44條資訊，請稍候再查詢！"
						+ "<br/>";

			}
		}
		try {
			resp45 = eaiGwSrv.send(eaireq45);
			JSONObject jo = resp45.getJSONObject(0);
			if (!jo.isEmpty() && "1".equals(jo.getString("checkData"))) {
				JSONArray rnData = jo.getJSONArray("rtnData");
				jsonParam45.put("EName",
						rnData.getJSONObject(0).get("R_RELNAME"));
				eaireq45.setQueryParam(jsonParam45);
				resp45 = eaiGwSrv.send(eaireq45);
			}
			formL120s01a.set("mhRlt45", "1");
		} catch (GWException e) {
			if (e.getMessage().startsWith("EAI 1000")) {
				// EAI 1000 代表非利害關係人(查無資料)
				formL120s01a.set("mhRlt45", "2");
			} else {
				// 非EAI 1000，代表查詢有問題，回傳錯誤訊息
				if (!noMsg) {
					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.查無資料),
							getClass());
				}
				errorMsg = errorMsg + "查詢失敗！！，金控連線錯誤，無法取得金控法45條資訊，請稍候再查詢！"
						+ "<br/>";

			}
		}

		// if (rltJsons.isEmpty()) {
		// if (!noMsg) {
		// throw new CapMessageException(RespMsgHelper.getMessage(parent,
		// UtilConstants.AJAX_RSP_MSG.查無資料), getClass());
		// }
		// errorMsg = "查無銀行法及金控法44、45條！" + "<br/>";
		// } else {
		// List<String> list = (List<String>) rltJsons.get(0);
		// if (list.size() >= 3) {
		// formL120s01a.set("mbRlt", getRltVal(list.get(0)));
		// formL120s01a.set("mhRlt44", getRltVal(list.get(1)));
		// formL120s01a.set("mhRlt45", getRltVal(list.get(2)));
		// } else {
		// // other.msg58=EAI資料不全
		// throw new CapMessageException(RespMsgHelper.getMessage(parent,
		// UtilConstants.AJAX_RSP_MSG.注意,
		// pop2.getProperty("other.msg58")), getClass());
		// }
		//

		formL120s01a.set("mbDate",
				CapDate.getCurrentDate(UtilConstants.DateFormat.YYYY_MM_DD));
		formL120s01a.set("mhDate",
				CapDate.getCurrentDate(UtilConstants.DateFormat.YYYY_MM_DD));
		if (!resp44.isEmpty()) {
			JSONObject json44 = JSONObject.fromObject(resp44.get(0));
			if (Util.isNotEmpty(json44.get("rtnData"))) {
				List<String> list44Data = (List<String>) json44.get("rtnData");
				// 44詳細資料內容
				JSONObject jsonDesc = JSONObject.fromObject(list44Data.get(0));
				if (jsonDesc != null && jsonDesc.containsKey("RELDESC")) {
					formL120s01a.set("mhRlt44Dscr",
							Util.trim(jsonDesc.getString("RELDESC")));
				} else {
					formL120s01a.set("mhRlt44Dscr", "");
				}
			}
		}
		if (!resp45.isEmpty()) {
			JSONObject json45 = JSONObject.fromObject(resp45.get(0));
			if (Util.isNotEmpty(json45.get("rtnData"))) {
				List<String> list45Data = (List<String>) json45.get("rtnData");
				// 45詳細資料內容
				JSONObject jsonDesc = JSONObject.fromObject(list45Data.get(0));
				if (jsonDesc != null && jsonDesc.containsKey("RELDESC")) {
					formL120s01a.set("mhRlt45Dscr",
							Util.trim(jsonDesc.getString("RELDESC")));
				} else {
					formL120s01a.set("mhRlt45Dscr", "");
				}
			}
		}

		// J-106-0117-001 增加金控法45條利害關係人比對範圍-實質關係人(授信以外交易)
		if (resp45.isEmpty()) {
			// 非金控法45 時，多比對是否為實質關係人(授信以外交易)
			Map<String, Object> map = misDbService.findRealRltByCustId(
					Util.trim(model.getCustId()), Util.trim(model.getDupNo()));
			if (map != null && !map.isEmpty()) {
				// 實質關係人(授信以外交易)
				Properties pop = MessageBundleScriptCreator
						.getComponentResource(LMSCommomPage.class);
				// l120s01d.effectiveRel=實質關係人(授信以外交易)
				formL120s01a.set("mhRlt45Dscr",
						pop.getProperty("l120s01d.effectiveRel"));
				formL120s01a.set("mhRlt45", "1");
			}
		}

		// 搜尋利害關係人檔
		formL120s01a.set("mbRlt", "2");

		// J-107-0337_05097_B1001 Web
		// e-Loan企金授信簽報書借款人基本資料，調整銀行法利害關係人可列出銀行所指派法人代表之單位、職稱及姓名且可顯示多筆
		String mbRlt = "2"; // 無
		List<String> rltList = new ArrayList<String>();

		if (listMap != null && !listMap.isEmpty()) {
			for (Map<String, Object> tempMap : listMap) {
				String ttp = Util.trim(tempMap.get("1"));
				// ttp 說明:
				// 1.本行銀行法利害關係人(含銀行實質關係人)
				// 2.本行銀行法有利害關係人
				// 3.本行銀行法有利害關係人企業
				if (!"1".equals(ttp) && !"2".equals(ttp) && !"3".equals(ttp)) {
					continue;
				}

				if ("1".equals(ttp)) {
					StringBuilder sb = new StringBuilder();
					sb.setLength(0);
					Map<String, Object> map = misDbService.selRlt(
							Util.trim(model.getCustId()),
							Util.trim(model.getDupNo()));
					if (map != null && !map.isEmpty()) {
						String tmp = Util.trim(map.get("RECTL"));
						if ("5".equals(tmp)) {
							// 本行轉投資事業
							// 持股比例須大於(含) 3%時,才為關係人
							if (LMSUtil.toBigDecimal(map.get("RESKRT"))
									.compareTo(new BigDecimal(3)) == -1) {
								continue;
							}
						}
						switch (Util.parseInt(tmp)) {
						case 1:
							// e-Loan企金授信簽報書借款人基本資料，調整銀行法利害關係人可列出銀行所指派法人代表之單位、職稱及姓名且可顯示多筆
							sb.append("借戶為本行負責人");
							break;
						case 2:
							sb.append("借戶為本行國內辦理授信人員");
							break;
						case 3:
							sb.append("借戶為本行國外辦理授信人員");
							break;
						case 4:
							sb.append("借戶為本行職員");
							break;
						case 5:
							sb.append("借戶為本行轉投資事業");
							break;
						case 6:
							sb.append("借戶為主要股東");
							break;
						case 7:
							sb.append("借戶為本行轉投資事業投資3％以下，且本行擔任該 投資事業之法人董監事及經理人");
							break;
						case 8:
							sb.append("借戶為實質關係人");
							break;
						}
						;
						tmp = Util.trim(map.get("RELCD"));

						// J-104-0210-001 Web e-Loan授信企金刪除引進利害關係人後顯示之授信影響等級欄位
						// if ("L".equals(tmp)) {
						// sb.append("，授信影響等級：所在分行");
						// } else if ("G".equals(tmp)) {
						// sb.append("，授信影響等級：全行");
						// }

					}
					if (sb.length() > 0) {
						formL120s01a.set("mbRltDscr", sb.toString());

						// J-107-0337_05097_B1001 Web
						// e-Loan企金授信簽報書借款人基本資料，調整銀行法利害關係人可列出銀行所指派法人代表之單位、職稱及姓名且可顯示多筆
						rltList.add(sb.toString());
					}
				}
				formL120s01a.set("mbRlt", "1");
				// J-107-0337_05097_B1001 Web
				// e-Loan企金授信簽報書借款人基本資料，調整銀行法利害關係人可列出銀行所指派法人代表之單位、職稱及姓名且可顯示多筆
				mbRlt = "1"; // 有
			}
		}

		// J-107-0337_05097_B1001 Web
		// e-Loan企金授信簽報書借款人基本資料，調整銀行法利害關係人可列出銀行所指派法人代表之單位、職稱及姓名且可顯示多筆
		// 加上第一層、第二層、第三層之說明
		if (Util.equals(mbRlt, "1")) {
			List<Map<String, Object>> mbRltLevel1s = misDbService.selRltLevel1(
					Util.trim(model.getCustId()), Util.trim(model.getDupNo()));

			if (mbRltLevel1s != null && !mbRltLevel1s.isEmpty()) {

				/*
				 * 常駐監察人 F12OOOOO80 劉OO 董事 1 常駐監察人 F12OOOOO80 劉OO 女 AAAAAAAAAA0
				 * 柔O公司 股東 2 常駐監察人 F12OOOOO80 劉OO 女 BBBBBBBBBB0 柔O公司 股東 2 常駐監察人
				 * F12OOOOO80 劉OO 子 CCCCCCCCCC0 柔O公司 股東 2 常駐監察人 F12OOOOO80 劉OO 子
				 * DDDDDDDDDD0 柔O公司 股東 2
				 */

				for (Map<String, Object> mbRltLevel1 : mbRltLevel1s) {
					String UNIT = MapUtils.getString(mbRltLevel1, "UNIT");
					String ID_2 = MapUtils.getString(mbRltLevel1, "ID_2");
					String NAME_2 = MapUtils.getString(mbRltLevel1, "NAME_2");
					String RECOCD = MapUtils.getString(mbRltLevel1, "RECOCD");
					String ID_3 = MapUtils.getString(mbRltLevel1, "ID_3");
					String NAME_3 = MapUtils.getString(mbRltLevel1, "NAME_3");
					String RELATIONSHIP = MapUtils.getString(mbRltLevel1,
							"RELATIONSHIP");
					String LEVEL = MapUtils.getString(mbRltLevel1, "LEVEL");

					// 常駐監察人 劉OO 為其董事
					// 常駐監察人 劉OO 之女 AAAAAAAAAA0 為其股東
					StringBuffer tRltBuf = new StringBuffer("");
					tRltBuf.append(
							Util.equals(tRltBuf.toString(), "") ? "" : "、")
							.append("本行").append(UNIT).append("")
							.append(NAME_2)
							.append((Util.equals(RECOCD, "") ? "" : "之"))
							.append(RECOCD).append(NAME_3).append("為其")
							.append(RELATIONSHIP);
					rltList.add(tRltBuf.toString());

				}

				if (rltList != null && !rltList.isEmpty()) {
					StringBuffer gRltBuff = new StringBuffer("");
					if (rltList.size() == 1) {
						for (String tRltStr : rltList) {
							gRltBuff.append(tRltStr);
						}
					} else {
						int i = 0;
						gRltBuff.append("　\n");
						for (String tRltStr : rltList) {

							i = i + 1;
							if (i == 1) {
								gRltBuff.append(i).append(".").append(tRltStr);
							} else {
								gRltBuff.append("\n").append(i).append(".")
										.append(tRltStr);
							}

						}
					}

					if (gRltBuff.length() > 0) {
						formL120s01a.set("mbRltDscr", gRltBuff.toString());
					}

				}
			}

		}

		// J-108-0178_05097_B1001 Web
		// e-Loan企金授信系統簽報書增列符合公司法第206條第三項「與本行董事具有控制從屬關係之公司」之董監事名單。
		// select DISTINCT REIDNM from MIS.ELF902 LEFT JOIN MIS.ELREMAIN ON
		// ELF902_CUSTID=REID AND ELF902_DUPNO=DUPNO where
		// ELF902_RCUSTID='12979827' AND ELF902_DUPNO1='0'

		// 後台管理->系統設定維護->LMS_J1080178_CA206_ON Y
		// J-108-0178企金授權外簽報書增列符合公司法第206條第三項「與本行董事具有控制從屬關係之公司」之董監事名單
		String showCaRlt206 = Util.trim(lmsService
				.getSysParamDataValue("LMS_J1080178_CA206_ON"));
		if (Util.equals(showCaRlt206, "")) {
			showCaRlt206 = "N";
		}

		if (Util.equals(showCaRlt206, "Y")) {
			List<Map<String, Object>> listElReMain = misDbService
					.findELREMAINByElf902RcustId(Util.trim(model.getCustId()),
							Util.trim(model.getDupNo()));
			StringBuffer reidnmBuff = new StringBuffer("");
			if (listElReMain != null && !listElReMain.isEmpty()) {
				for (Map<String, Object> elReMain : listElReMain) {
					String strElReMain = Util.trim(MapUtils.getString(elReMain,
							"REIDNM"));
					if (Util.notEquals(strElReMain, "")) {
						if (Util.equals(reidnmBuff.toString(), "")) {
							reidnmBuff.append(strElReMain);
						} else {
							reidnmBuff.append("、").append(strElReMain);
						}
					}
				}
			}

			if (Util.notEquals(reidnmBuff.toString(), "")) {
				formL120s01a.set("caRlt206", "Y");
				formL120s01a.set("caRlt206Date",
						CapDate.getCurrentDate("yyyy-MM-dd"));
				formL120s01a.set("caRlt206Dscr", reidnmBuff.toString());
			} else {
				formL120s01a.set("caRlt206", "N");
				formL120s01a.set("caRlt206Date",
						CapDate.getCurrentDate("yyyy-MM-dd"));
				formL120s01a.set("caRlt206Dscr", "");
			}
		} else {
			formL120s01a.set("caRlt206", "");
			formL120s01a.set("caRlt206Date", "");
			formL120s01a.set("caRlt206Dscr", "");
		}

		formL120s01a.set("showCaRlt206", showCaRlt206);

		// END******************************************************************************

		result.set("L120S01aForm", formL120s01a);

		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS02BPanel.class);
		StringBuilder noData = new StringBuilder();
		if (UtilConstants.Mark.ZEROISNODATA.equals(Util.trim(formL120s01a
				.get("mbRlt")))) {
			noData.append(pop.getProperty("l120s02.thickbox13"));
		}
		if (UtilConstants.Mark.ZEROISNODATA.equals(Util.trim(formL120s01a
				.get("mhRlt44")))) {
			(noData.length() == 0 ? noData.append(UtilConstants.Mark.SPACE)
					: noData.append(",")).append(pop
					.getProperty("l120s02.thickbox14"));
		}
		if (UtilConstants.Mark.ZEROISNODATA.equals(Util.trim(formL120s01a
				.get("mhRlt45")))) {
			(noData.length() == 0 ? noData.append(UtilConstants.Mark.SPACE)
					: noData.append(",")).append(pop
					.getProperty("l120s02.thickbox15"));
		}
		if (noData.length() > 0) {
			result.set("noData", noData.toString());
		} else {
			if (!noMsg) {
				// 印出執行成功訊息!
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
						RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
			}
			// return result;
		}
		// }
		result.set("errorMsg", errorMsg);
		return result;
	}

	/**
	 * 是否為內部信評
	 * 
	 * @param str
	 * @return
	 */
	private boolean isMow(String str) {
		if (UtilConstants.Casedoc.CrdType2.DBU大型企業.equals(str)
				|| UtilConstants.Casedoc.CrdType2.DBU中型企業.equals(str)
				|| UtilConstants.Casedoc.CrdType2.DBU中小型企業.equals(str)
				|| UtilConstants.Casedoc.CrdType2.DBU不動產有建案規劃.equals(str)
				|| UtilConstants.Casedoc.CrdType2.DBU專案融資.equals(str)
				|| UtilConstants.Casedoc.CrdType2.DBU本國證券公司.equals(str)
				|| "M7".equals(str)
				|| UtilConstants.Casedoc.CrdType2.DBU投資公司一般情況.equals(str)
				|| UtilConstants.Casedoc.CrdType2.DBU租賃公司.equals(str)
				|| UtilConstants.Casedoc.CrdType2.DBU一案建商.equals(str)
				|| UtilConstants.Casedoc.CrdType2.DBU非一案建商_擔保_土融.equals(str)
				|| UtilConstants.Casedoc.CrdType2.DBU非一案建商_無擔.equals(str)
				|| UtilConstants.Casedoc.CrdType2.投資公司情況一.equals(str)
				|| UtilConstants.Casedoc.CrdType2.投資公司情況二.equals(str)
				|| UtilConstants.Casedoc.CrdType2.OBU船舶_航空器模型.equals(str)
				|| UtilConstants.Casedoc.CrdType2.OBU境外貿易型_控股型.equals(str)
				|| UtilConstants.Casedoc.CrdType2.國金部實體營運企業.equals(str)
				|| UtilConstants.Casedoc.CrdType2.國金部金融租賃業.equals(str)
				|| UtilConstants.Casedoc.CrdType2.信託基金及其他金融工具.equals(str)
				|| UtilConstants.Casedoc.CrdType2.澳洲不動產租售業.equals(str)
				|| UtilConstants.Casedoc.CrdType2.境外中型中小型.equals(str)
				|| UtilConstants.Casedoc.CrdType2.日本一般企業.equals(str)
				|| UtilConstants.Casedoc.CrdType2.日本不動產租售業.equals(str)
				|| UtilConstants.Casedoc.CrdType2.亞太大型企業模型.equals(str)
				|| UtilConstants.Casedoc.CrdType2.亞太中型中小型企業模型.equals(str)
				|| UtilConstants.Casedoc.CrdType2.亞太租賃業評等表.equals(str)
				|| UtilConstants.Casedoc.CrdType2.亞太船舶航空器模型.equals(str)
				|| UtilConstants.Casedoc.CrdType2.亞太貿易型控股型企業模型.equals(str)
				|| UtilConstants.Casedoc.CrdType2.買入企業應收帳款.equals(str)
				|| UtilConstants.Casedoc.CrdType2.泰子行大型_中型企業.equals(str)
				|| UtilConstants.Casedoc.CrdType2.泰子行中小型企業.equals(str)
				|| UtilConstants.Casedoc.CrdType2.澳洲地區一般企業.equals(str)
				|| UtilConstants.Casedoc.CrdType2.歐洲地區一般企業.equals(str)
				|| UtilConstants.Casedoc.CrdType2.美國地區一般企業.equals(str)
				|| UtilConstants.Casedoc.CrdType2.DBU_大型企業.equals(str)
				|| UtilConstants.Casedoc.CrdType2.DBU_中型企業.equals(str)
				|| UtilConstants.Casedoc.CrdType2.DBU_中小型企業.equals(str)
				|| UtilConstants.Casedoc.CrdType2.DBU_微型企業.equals(str)
				|| UtilConstants.Casedoc.CrdType2.不動產開發商.equals(str)
				|| UtilConstants.Casedoc.CrdType2.不動產租售商.equals(str)
				|| UtilConstants.Casedoc.CrdType2.收益性不動產.equals(str)
				|| UtilConstants.Casedoc.CrdType2.高風險商用不動產.equals(str)) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * 企金借款人第一頁籤查詢
	 * 
	 * @param thisOid
	 *            Oid
	 * @param mainId
	 *            文件編號
	 * @param isOther
	 *            是否為其他綜合
	 * @return
	 * @throws CapException
	 */
	private IResult queryBorPage1(String thisOid, String mainId, boolean isOther) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		// 載入要用到的porperty檔
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS02BPanel.class);
		// 第一頁籤Query...
		L120S01A l120s01a = service1201.findL120s01aByOid(thisOid);
		if (l120s01a == null) {
			l120s01a = new L120S01A();
		}
		if (Util.isNotEmpty(thisOid)) {
			String custId = Util.trim(l120s01a.getCustId());
			String dupNo = Util.trim(l120s01a.getDupNo());
			L120S01B l120s01b = service1201.findL120s01bByUniqueKey(mainId,
					custId, dupNo);
			List<L120S01C> list = service1201.findL120s01cByMainId(mainId);
			L120S01D l120s01d = service1201.findL120s01dByUniqueKey(mainId,
					custId, dupNo);
			if (l120s01b == null) {
				l120s01b = new L120S01B();
			}
			if (l120s01d == null) {
				l120s01d = new L120S01D();
			}
			CapAjaxFormResult thisResult = DataParse.toResult(l120s01b,
					DataParse.Delete, new String[] { EloanConstants.MAIN_ID,
							EloanConstants.OID });
			L120M01A l120m01a = service1201.findL120m01aByMainId(mainId);
			thisResult.set("existDate", CapDate.formatDate(
					l120s01a.getExistDate(),
					UtilConstants.DateFormat.YYYY_MM_DD));
			thisResult.set("feeDate", CapDate.formatDate(l120s01a.getFeeDate(),
					UtilConstants.DateFormat.YYYY_MM_DD));
			thisResult.set("countryDate", CapDate.formatDate(
					l120s01a.getCountryDate(),
					UtilConstants.DateFormat.YYYY_MM_DD));
			thisResult.set("rgtUnit", Util.trim(l120s01b.getRgtUnit()));
			thisResult.set("cptlUnit", Util.trim(l120s01b.getCptlUnit()));

			// J-106-0232-001 Web e-Loan國內、海外企金授信衍生性金融商品額度明細表新增淨值與額外信用增強
			// 下面這行很重要，因為會把comma消掉變成 1000000，沒這行會NetAmtUnit會變成 1,000,000
			thisResult.set("netAmtUnit", Util.trim(l120s01b.getNetAmtUnit()));

			// 主要營業項目顯示設定控制
			if (UtilConstants.Casedoc.DocKind.授權外.equals(Util.trim(l120m01a
					.getDocKind()))) {
				// J-109-0370 相關評估改版
				// L120M01I l120m01i = service1201.findL120m01iByMainId(mainId);
				// boolean isOut = LMSUtil.isOutNewVer(l120m01a, l120m01i);
				// if(isOut) {
				thisResult.set("itemOfBusi", Util.trim(l120s01b.getBussItem()));
				// } else {
				// thisResult.set("itemOfBusi",
				// Util.trim(l120m01a.getItemOfBusi()));
				// }
			} else {
				// 授權內
				thisResult.set("itemOfBusi", Util.trim(l120s01b.getBussItem()));
			}
			thisResult.set("_renCd", Util.trim(l120s01a.getRenCd()));
			thisResult.set("_buscd", Util.trim(l120s01a.getBusCode()));
			// 依據不同的客戶型態顯示相對應結果
			thisResult.set("typCd", getMessage("typCd." + l120s01a.getTypCd()));
			// thisResult.set("typCd", TypCdEnum.getEnum(l120s01a.getTypCd())
			// .name());
			// 手動指定特定欄位塞特定的值...(因同一Tab由兩個Table組成而使用)
			thisResult.set("custId", custId);
			thisResult.set("custNo", Util.trim(l120s01a.getCustNo()));

			// J-108-0143_05097_B1001 Web e-Loan國內外企金額度明細表簽報性質新作時加註(新客戶往來原有客戶往來)
			thisResult.set("newCustFlag", Util.trim(l120s01a.getNewCustFlag()));

			thisResult.set("dupNo", dupNo);
			thisResult.set("custName", Util.trim(l120s01a.getCustName()));
			// 黑名單查詢
			thisResult.set("blackName", Util.trim(l120s01a.getBlackName()));
			thisResult.set("blackDate", CapDate.formatDate(
					l120s01a.getBlackDate(),
					UtilConstants.DateFormat.YYYY_MM_DD));
			// 婉卻紀錄-申貸戶
			thisResult.set("IsRejt", Util.trim(l120s01a.getIsRejt()));
			thisResult.set("rejtReadDate", CapDate.formatDate(
					l120s01a.getRejtReadDate(),
					UtilConstants.DateFormat.YYYY_MM_DD));
			thisResult.set("rejtBrNo",
					getRejtBrName(Util.trim(l120s01a.getRejtBrNo())));
			thisResult.set("rejtCode",
					getRejtCodeName(Util.trim(l120s01a.getRejtCode())));
			thisResult.set("rejtReason", Util.trim(l120s01a.getRejtReason()));
			SimpleDateFormat formatter = new SimpleDateFormat(
					UtilConstants.DateFormat.YYYY_MM_DD_HH_MM_SS);
			thisResult.set(
					"rejtDate",
					Util.isNotEmpty(l120s01a.getRejtDate()) ? formatter
							.format(l120s01a.getRejtDate())
							: UtilConstants.Mark.SPACE);
			thisResult.set("rejtCase", Util.trim(l120s01a.getRejtCase()));
			thisResult.set("rejtCaseAdjMemo",
					Util.trim(l120s01a.getRejtCaseAdjMemo()));
			thisResult.set("rejtCaseBefore",
					Util.trim(l120s01a.getRejtCaseBefore()));
			// 婉卻紀錄-負責人
			thisResult.set("IsRejt1", Util.trim(l120s01a.getIsRejt1()));
			thisResult.set("rejtReadDate1", CapDate.formatDate(
					l120s01a.getRejtReadDate1(),
					UtilConstants.DateFormat.YYYY_MM_DD));
			thisResult.set("rejtBrNo1",
					getRejtBrName(Util.trim(l120s01a.getRejtBrNo1())));
			thisResult.set("rejtCode1",
					getRejtCodeName(Util.trim(l120s01a.getRejtCode1())));
			thisResult.set("rejtReason1", Util.trim(l120s01a.getRejtReason1()));
			thisResult.set(
					"rejtDate1",
					Util.isNotEmpty(l120s01a.getRejtDate1()) ? formatter
							.format(l120s01a.getRejtDate1())
							: UtilConstants.Mark.SPACE);
			thisResult.set("rejtCase1", Util.trim(l120s01a.getRejtCase1()));
			thisResult.set("rejtCaseAdjMemo1",
					Util.trim(l120s01a.getRejtCaseAdjMemo1()));
			thisResult.set("rejtCaseBefore1",
					Util.trim(l120s01a.getRejtCaseBefore1()));
			// ---
			thisResult.set("rejtChairmanId",
					Util.trim(l120s01a.getRejtChairmanId()));
			thisResult.set("rejtChairmanDupNo",
					Util.trim(l120s01a.getRejtChairmanDupNo()));

			// J-105-0179-001 Web e-Loan企金授信建立「往來異常通報戶」紀錄查詢及於簽報書上顯示查詢結果功能
			// 異常通報紀錄-申貸戶
			thisResult.set("isAbnormal", Util.trim(l120s01a.getIsAbnormal()));
			thisResult.set("abnormalReadDate", CapDate.formatDate(
					l120s01a.getAbnormalReadDate(),
					UtilConstants.DateFormat.YYYY_MM_DD));
			thisResult.set("abnormalBrNo",
					getRejtBrName(Util.trim(l120s01a.getAbnormalBrNo())));
			thisResult.set("abnormalDate", CapDate.formatDate(
					l120s01a.getAbnormalDate(),
					UtilConstants.DateFormat.YYYY_MM_DD));
			thisResult.set("abnormalStatus",
					Util.trim(l120s01a.getAbnormalStatus()));
			thisResult.set("abnormalMainId",
					Util.trim(l120s01a.getAbnormalMainId()));

			// 異常通報紀錄-負責人
			thisResult.set("isAbnormal1", Util.trim(l120s01a.getIsAbnormal1()));
			thisResult.set("abnormalReadDate1", CapDate.formatDate(
					l120s01a.getAbnormalReadDate1(),
					UtilConstants.DateFormat.YYYY_MM_DD));
			thisResult.set("abnormalBrNo1",
					getRejtBrName(Util.trim(l120s01a.getAbnormalBrNo1())));
			thisResult.set("abnormalDate1", CapDate.formatDate(
					l120s01a.getAbnormalDate1(),
					UtilConstants.DateFormat.YYYY_MM_DD));
			thisResult.set("abnormalStatus1",
					Util.trim(l120s01a.getAbnormalStatus1()));
			thisResult.set("abnormalChairmanId",
					Util.trim(l120s01a.getAbnormalChairmanId()));
			thisResult.set("abnormalChairmanDupNo",
					Util.trim(l120s01a.getAbnormalChairmanDupNo()));
			thisResult.set("abnormalMainId1",
					Util.trim(l120s01a.getAbnormalMainId1()));

			// J-111-0535_05097_B1001 Web e-Loan企金授信配合「ESG綜效調查表 」建置，於簽報書增設相對應欄位
			// 因不符ESG而暫緩承作紀錄-申貸戶
			thisResult.set("isEsgRejt", Util.trim(l120s01a.getIsEsgRejt()));
			thisResult.set("esgRejtReadDate", CapDate.formatDate(
					l120s01a.getEsgRejtReadDate(),
					UtilConstants.DateFormat.YYYY_MM_DD));
			thisResult.set("esgRejtBrNo",
					getRejtBrName(Util.trim(l120s01a.getEsgRejtBrNo())));

			if (Util.notEquals(Util.nullToSpace(l120s01a.getEsgRejtEmpNo()), "")) {
				thisResult.set(
						"esgRejtEmpNo",
						Util.trim(l120s01a.getEsgRejtEmpNo())
								+ " "
								+ Util.nullToSpace(userInfoService
										.getUserName(Util.trim(l120s01a
												.getEsgRejtEmpNo()))));
			} else {
				thisResult.set("esgRejtEmpNo", "");
			}

			thisResult.set(
					"esgRejtDate",
					Util.isNotEmpty(l120s01a.getEsgRejtDate()) ? formatter
							.format(l120s01a.getEsgRejtDate())
							: UtilConstants.Mark.SPACE);

			// ---
			if (!Util.trim(l120s01a.getCustRlt()).contains("X")) {
				// 其他綜合關係
				isOther = true;
				String str = Util.trim(l120s01a.getCustRlt());
				if (Util.isNotEmpty(str)) {
					thisResult
							.set("custRlt1",
									!LMSUtil.checkSubStr(str, 0, 1) ? UtilConstants.Mark.SPACE
											: str.substring(0, 1));
					thisResult
							.set("custRlt2",
									!LMSUtil.checkSubStr(str, 1, 2) ? UtilConstants.Mark.SPACE
											: str.substring(1, 2));
				}
			}
			thisResult.set("custRlt", Util.trim(l120s01a.getCustRlt()));
			thisResult.set("custPos", Util.trim(l120s01a.getCustPos()));
			thisResult.set("keyMan", Util.trim(l120s01a.getKeyMan()));
			
			String tmpNewMowDesc = ""; // J-113-0044_12473_B1001 試行新模型資料敘述文字
			// 信用評等設定
			for (L120S01C model : list) {
				String trustGrade = Util.trim(model.getGrade());
				if (custId.equals(model.getCustId())
						&& dupNo.equals(model.getDupNo())) {
					String crdType = Util.trim(model.getCrdType());
					if (UtilConstants.Casedoc.CrdType.DBU大型企業.equals(crdType)
							|| UtilConstants.Casedoc.CrdType.DBU中小型企業
									.equals(crdType)
							|| UtilConstants.Casedoc.CrdType.海外.equals(crdType)
							|| UtilConstants.Casedoc.CrdType.澳洲大型企業
									.equals(crdType)
							|| UtilConstants.Casedoc.CrdType.澳洲中型企業
									.equals(crdType)
							|| UtilConstants.Casedoc.CrdType.澳洲中小型企業
									.equals(crdType)
							|| UtilConstants.Casedoc.CrdType.境外.equals(crdType)) {
						thisResult.set("borrGrade", trustGrade);
					} else if (UtilConstants.Casedoc.CrdType.未評等
							.equals(crdType)) {
						// 未評等(信用評等)
						thisResult.set("borrGrade",
								pop.getProperty("l120s01c.noGrade"));
						thisResult.set("showGrad", true);
					} else if (UtilConstants.Casedoc.CrdType2.免辦
							.equals(crdType)) {
						// 免辦(內部信評)
						thisResult.set("grade0",
								pop.getProperty("l120s01c.dont"));
					} else if (isMow(crdType)) {
						// MOW內部信評
						thisResult.set("grade0", trustGrade);
						thisResult.set("showGrad", true);
					} else if (UtilConstants.Casedoc.CrdType.泰國GroupA
							.equals(crdType)
							|| UtilConstants.Casedoc.CrdType.泰國GroupB
									.equals(crdType)
							|| UtilConstants.Casedoc.CrdType.自訂.equals(crdType)) {
						// 當地信評
						Map<String, String> map = new HashMap<String, String>();
						map.put(UtilConstants.Casedoc.CrdType.泰國GroupA, "4");
						map.put(UtilConstants.Casedoc.CrdType.泰國GroupB, "5");
						map.put(UtilConstants.Casedoc.CrdType.自訂, "6");

						String key = map.get(crdType);
						if (Util.isNotEmpty(key)) {
							thisResult
									.set("crdTYear" + key,
											CapDate.formatDate(
													model.getCrdTYear(),
													UtilConstants.DateFormat.YYYY_MM_DD));
							thisResult.set("grade" + key, trustGrade);
						}
					} else if (isTcriTrust(crdType)) {
						// J-109-0370_05097_B1001 配合企金一般簽報書格式修訂，新增、調整簽報作業相關內容
						// TCRU信評
						// J-109-0370_05097_B1001 配合企金一般簽報書格式修訂，新增、調整簽報作業相關內容
						Map<String, String> map = new HashMap<String, String>();
						map.put(UtilConstants.Casedoc.CrdType.TCRI評等, "Tcri");

						String key = map.get(crdType);
						if (Util.isNotEmpty(key)) {
							// myForm2Result.set("crdTYear" + key,
							// CapDate.formatDate(
							// model.getCrdTYear(),
							// UtilConstants.DateFormat.YYYY_MM_DD));
							thisResult
									.set("grade" + key,
											(Util.equals(
													Util.trim(model.getGrade()),
													"") ? ""
													: Util.trim(model
															.getGrade())
															+ pop.getProperty("l120s01c.tempGrade"))
													+ " "
													+ MapUtils.getString(
															service1201
																	.formatTCRIGrade(Util
																			.trim(model
																					.getGrade())),
															"t_grade1"));
						}

					} else {
						// 國際信評
						Map<String, String> map = new HashMap<String, String>();
						map.put(UtilConstants.Casedoc.CrdType.MOODY, "1");
						map.put(UtilConstants.Casedoc.CrdType.SAndP, "2");
						map.put(UtilConstants.Casedoc.CrdType.Fitch, "3");
						map.put(UtilConstants.Casedoc.CrdType.中華信評, "7");
						// J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等
						map.put(UtilConstants.Casedoc.CrdType.FitchTW, "8");
						map.put(UtilConstants.Casedoc.CrdType.KBRA, "9");

						String key = map.get(crdType);
						if (Util.isNotEmpty(key)) {
							thisResult
									.set("crdTYear" + key,
											CapDate.formatDate(
													model.getCrdTYear(),
													UtilConstants.DateFormat.YYYY_MM_DD));
							thisResult.set("grade" + key, trustGrade);
						}
					}
					// J-113-0044_12473_B1001 試行新模型資料敘述文字
					if("@".equals(Util.getLeftStr(crdType, 1))){
						String tmpNewMowcrdTypeDesc = codeService.getDescOfCodeType("CRDType", crdType.substring(1));
						tmpNewMowDesc = "<br>*" + pop.getProperty("l120s01c.tmpNewMow") + 
						"(" + pop.getProperty("l120s01c.forReference") + ")" + ":" + tmpNewMowcrdTypeDesc + 
						":" + trustGrade + pop.getProperty("l120s01c.tempGrade");
					}
					
				}
			}
			SimpleDateFormat bartDateFormat = new SimpleDateFormat(
					UtilConstants.DateFormat.YYYY_MM_DD);
			thisResult.set("isOther", isOther);
			thisResult.set("mbDate",
					checknull(bartDateFormat, l120s01d.getMbDate()));
			thisResult.set("mhDate",
					checknull(bartDateFormat, l120s01d.getMhDate()));
			thisResult.set("fctMhRltDscr",
					Util.truncateString(l120s01d.getFctMhRltDscr(), 192));
			thisResult.set("mbMhRltDscr",
					Util.truncateString(l120s01d.getMbMhRltDscr(), 192));
			thisResult.set("mbRlt", Util.trim(l120s01d.getMbRlt()));
			thisResult.set("mbRltDscr", Util.trim(l120s01d.getMbRltDscr()));
			thisResult.set("mhRlt44", Util.trim(l120s01d.getMhRlt44()));
			thisResult.set("mhRlt44Dscr", Util.trim(l120s01d.getMhRlt44Dscr()));
			thisResult.set("mhRlt45", Util.trim(l120s01d.getMhRlt45()));
			thisResult.set("mhRlt45Dscr", Util.trim(l120s01d.getMhRlt45Dscr()));
			thisResult.set("mbRlt33", Util.trim(l120s01d.getMbRlt33()));
			thisResult.set("mbRltDscr33", Util.trim(l120s01d.getMbRltDscr33()));
			thisResult.set("fctMbRlt", Util.trim(l120s01d.getFctMbRlt()));
			thisResult.set("fctMhRlt", Util.trim(l120s01d.getFctMhRlt()));
			thisResult.set("invMDscr", Util.trim(l120s01b.getInvMDscr()));
			// J-108-0178_05097_B1001 Web
			// e-Loan企金授信系統簽報書增列符合公司法第206條第三項「與本行董事具有控制從屬關係之公司」之董監事名單。
			thisResult.set("caRlt206", Util.trim(l120s01d.getCaRlt206()));
			thisResult.set("caRlt206Dscr",
					Util.trim(l120s01d.getCaRlt206Dscr()));
			thisResult.set("caRlt206Date",
					checknull(bartDateFormat, l120s01d.getCaRlt206Date()));

			// J-108-0178_05097_B1001 Web
			// e-Loan企金授權外簽報書增列符合公司法第206條第三項「與本行董事具有控制從屬關係之公司」之董監事名單。

			// 後台管理->系統設定維護->LMS_J1080178_CA206_ON Y
			// J-108-0178企金授權外簽報書增列符合公司法第206條第三項「與本行董事具有控制從屬關係之公司」之董監事名單
			String showCaRlt206 = Util.trim(lmsService
					.getSysParamDataValue("LMS_J1080178_CA206_ON"));
			if (Util.equals(showCaRlt206, "")) {
				showCaRlt206 = "N";
			}
			thisResult.set("showCaRlt206", showCaRlt206);

			thisResult.set(
					"borrGrade",
					Util.trim(thisResult.get("borrGrade"))
							+ (!(Util.isChineseChar(Util.trim(thisResult
									.get("borrGrade"))))
									&& Util.isNotEmpty(Util.trim(thisResult
											.get("borrGrade"))) ? "級" : ""));
			
			// J-113-0044_12473_B1001 信用風險內部評等新增顯示試行模型評等資訊
			String grade0Result = Util.trim(thisResult.get("grade0")) + ((Util.isNumeric(thisResult.get("grade0"))) ? "等" : "");
			if(Util.isNotEmpty(tmpNewMowDesc)){
				grade0Result = grade0Result + tmpNewMowDesc;
			}
			thisResult.set("grade0", grade0Result);
			String bussCode = Util.trim(Util.trim(l120s01b.getBusCode()));
			String ecoNm = Util.trim(l120s01b.getEcoNm());
			String bussKind = Util.trim(l120s01b.getBussKind());
			String ecoNm07A = Util.trim(l120s01b.getEcoNm07A());
			thisResult.set("displayBusCd",
					getDisplayBusCd(bussCode, ecoNm, bussKind, ecoNm07A));
			result.set("L120S01aForm", thisResult);
		}
		return result;
	}

	/**
	 * 企金借款人第二頁籤查詢
	 * 
	 * @param thisOid
	 * @param mainId
	 * @param isOther
	 * @return
	 * @throws CapException
	 */
	private IResult queryBorPage2(String thisOid, String mainId, L120S01A l120s01a) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		if (!CapString.isEmpty(thisOid)) {

			String custId = Util.trim(l120s01a.getCustId());
			String dupNo = Util.trim(l120s01a.getDupNo());

			L120S01B l120s01b = service1201.findL120s01bByUniqueKey(mainId,
					custId, dupNo);
			if (l120s01b == null) {
				l120s01b = new L120S01B();
			}

			L120S01G l120s01g1 = service1201
					.findL120s01gByUniqueKey(mainId, custId, dupNo,
							UtilConstants.Casedoc.L120s01gType.營運概況分析與評估);
			if (l120s01g1 == null) {
				l120s01g1 = new L120S01G();
			}

			CapAjaxFormResult resultl120s01g1 = DataParse.toResult(l120s01g1,
					DataParse.Delete, new String[] { EloanConstants.MAIN_ID,
							EloanConstants.OID });

			String gaapFlag = CapString.trimNull(l120s01b.getGaapFlag());
			String tradeType = CapString.trimNull(l120s01b.getTradeType());
			gaapFlag = "".equals(gaapFlag) ? " " : gaapFlag;
			tradeType = "".equals(tradeType) ? " " : tradeType;

			JSONObject l120s01eKind1Data = service1201.getL120s01eKind1Data(
					mainId, custId, dupNo, gaapFlag.charAt(0),
					tradeType.charAt(0));
			resultl120s01g1.putAll(new CapAjaxFormResult(l120s01eKind1Data));

			if (l120s01g1 != null) {
				CapAjaxFormResult formIdDscr1 = new CapAjaxFormResult();
				formIdDscr1.set("idDscr1", Util.trim(l120s01g1.getDataDscr()));
				resultl120s01g1.set("rcdFlag",
						(UtilConstants.DEFAULT.是.equals(Util.trim(l120s01b
								.getRcdFlag())) ? UtilConstants.DEFAULT.是
								: UtilConstants.DEFAULT.否));
				resultl120s01g1.set("runFlag",
						(UtilConstants.DEFAULT.是.equals(Util.trim(l120s01b
								.getRunFlag())) ? UtilConstants.DEFAULT.是
								: UtilConstants.DEFAULT.否));
				resultl120s01g1.set("runUnit", NumConverter.delCommaString(Util
						.trim(l120s01b.getRunUnit())));
				resultl120s01g1
						.set("runCurr", Util.trim(l120s01b.getRunCurr()));
				// 當有多個model要對應form時.. 依序指定
				result.set("formIdDscr1", formIdDscr1);
				result.set("L120S01gForm_1", resultl120s01g1);
			}
		}
		return result;
	}

	/**
	 * 企金借款人第三頁籤查詢
	 * 
	 * @param thisOid
	 * @param mainId
	 * @param isOther
	 * @return
	 * @throws CapException
	 */
	private IResult queryBorPage3(String thisOid, String mainId, L120S01A l120s01a) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		if (!CapString.isEmpty(thisOid)) {

			String custId = Util.trim(l120s01a.getCustId());
			String dupNo = Util.trim(l120s01a.getDupNo());

			L120S01G l120s01g_2 = service1201
					.findL120s01gByUniqueKey(mainId, custId, dupNo,
							UtilConstants.Casedoc.L120s01gType.財務狀況分析與評估);
			if (l120s01g_2 == null) {
				l120s01g_2 = new L120S01G();
			}

			L120S01B l120s01b = service1201.findL120s01bByUniqueKey(mainId,
					custId, dupNo);
			if (l120s01b == null) {
				l120s01b = new L120S01B();
			}

			CapAjaxFormResult resultL120s01g_2 = DataParse.toResult(l120s01g_2,
					DataParse.Delete, new String[] { EloanConstants.MAIN_ID,
							EloanConstants.OID });

			String gaapFlag = CapString.trimNull(l120s01b.getGaapFlag());
			String tradeType = CapString.trimNull(l120s01b.getTradeType());
			gaapFlag = "".equals(gaapFlag) ? " " : gaapFlag;
			tradeType = "".equals(tradeType) ? " " : tradeType;

			JSONObject l120s01eKind2Data = service1201.getL120s01eKind2Data(
					mainId, custId, dupNo, gaapFlag.charAt(0),
					tradeType.charAt(0));
			resultL120s01g_2.putAll(new CapAjaxFormResult(l120s01eKind2Data));

			if (l120s01g_2 != null) {

				CapAjaxFormResult formIdDscr2 = new CapAjaxFormResult();
				formIdDscr2.set("idDscr2", Util.trim(l120s01g_2.getDataDscr()));
				resultL120s01g_2.set("rcdFlag",
						(UtilConstants.DEFAULT.是.equals(Util.trim(l120s01b
								.getRcdFlag())) ? UtilConstants.DEFAULT.是
								: UtilConstants.DEFAULT.否));
				resultL120s01g_2.set("finFlag",
						(UtilConstants.DEFAULT.是.equals(Util.trim(l120s01b
								.getFinFlag())) ? UtilConstants.DEFAULT.是
								: UtilConstants.DEFAULT.否));
				// 當有多個model要對應form時.. 依序指定
				result.set("formIdDscr2", formIdDscr2);
				result.set("L120S01gForm_2", resultL120s01g_2);
			}
		}
		return result;
	}

	/**
	 * 企金借款人第四頁籤查詢
	 * 
	 * @param thisOid
	 * @param mainId
	 * @param isOther
	 * @return
	 * @throws CapException
	 */
	private IResult queryBorPage4(String thisOid, String mainId, L120S01A l120s01a) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		if (!CapString.isEmpty(thisOid)) {
			String custId = Util.trim(l120s01a.getCustId());
			String dupNo = Util.trim(l120s01a.getDupNo());
			L120S01F l120s01f = service1201.findL120s01fByUniqueKey(mainId,
					custId, dupNo);
			if (l120s01f == null) {
				l120s01f = new L120S01F();
			}
			CapAjaxFormResult L120S01fForm = DataParse.toResult(l120s01f,
					DataParse.Delete, new String[] { EloanConstants.MAIN_ID,
							EloanConstants.OID });
			String[] l120s01fKeys = new String[] { "dpAvgUnit", "fxUnit",
					"fx2Unit", "imUnit", "im2Unit", "exUnit", "ex2Unit",
					"cntrUnit", "nonLoanUnit", "fxYear", "imYear", "exYear" };
			for (String key : l120s01fKeys) {
				L120S01fForm
						.set(key,
								("fxYear".equals(key) || "imYear".equals(key) || "exYear"
										.equals(key)) ? trimZero(NumConverter
										.delCommaString(Util.trim(L120S01fForm
												.get(key)))) : NumConverter
										.delCommaString(Util.trim(L120S01fForm
												.get(key))));

			}

			// 2013-07-26因為徵信資信簡表平均存款可以打N.A.故配合調整
			if (l120s01f.getDpAvgAmt() == null
					&& !"".equals(Util.nullToSpace(l120s01f.getDpAvgCurr()))) {
				L120S01fForm.set("dpAvgAmt", "N.A.");
			}

			// SimpleDateFormat bartDateFormat = new SimpleDateFormat(
			// UtilConstants.DateFormat.YYYY_MM_DD);
			JSONObject jsonDate = new JSONObject();
			String[] keys = new String[] { "fxb", "fxe", "imb", "ime", "exb",
					"exe", "cntrb", "cntre" };
			jsonDate.put("fxb", CapDate.formatDate(l120s01f.getFxBDate(),
					UtilConstants.DateFormat.YYYY_MM_DD));
			jsonDate.put("fxe", CapDate.formatDate(l120s01f.getFxEDate(),
					UtilConstants.DateFormat.YYYY_MM_DD));
			jsonDate.put("imb", CapDate.formatDate(l120s01f.getImBDate(),
					UtilConstants.DateFormat.YYYY_MM_DD));
			jsonDate.put("ime", CapDate.formatDate(l120s01f.getImEDate(),
					UtilConstants.DateFormat.YYYY_MM_DD));
			jsonDate.put("exb", CapDate.formatDate(l120s01f.getExBDate(),
					UtilConstants.DateFormat.YYYY_MM_DD));
			jsonDate.put("exe", CapDate.formatDate(l120s01f.getExEDate(),
					UtilConstants.DateFormat.YYYY_MM_DD));
			jsonDate.put("cntrb", CapDate.formatDate(l120s01f.getCntrBDate(),
					UtilConstants.DateFormat.YYYY_MM_DD));
			jsonDate.put("cntre", CapDate.formatDate(l120s01f.getCntrEDate(),
					UtilConstants.DateFormat.YYYY_MM_DD));
			for (String key : keys) {
				translate(key, Util.trim(jsonDate.get(key)), L120S01fForm);
			}
			L120S01B l120s01b = service1201.findL120s01bByUniqueKey(mainId,
					custId, dupNo);
			L120S01fForm
					.set("rcdFlag",
							(UtilConstants.DEFAULT.是
									.equals((l120s01b == null) ? UtilConstants.Mark.SPACE
											: Util.trim(l120s01b.getRcdFlag())) ? UtilConstants.DEFAULT.是
									: UtilConstants.DEFAULT.否));
			result.set("L120S01fForm", L120S01fForm); // 當有多個model要對應form時..
														// 依序指定
		}
		return result;
	}

	/**
	 * <pre>
	 * 查詢-企金(借款人標籤列)
	 * 
	 * @param params PageParameters
	 * @return IResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryBorrow(PageParameters params) throws CapException {
		String thisOid = Util.trim(params.getString(EloanConstants.OID));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		boolean isOther = params.getBoolean("isOther");
		CapAjaxFormResult result = new CapAjaxFormResult();
		L120S01A l120s01a = service1201.findL120s01aByOid(thisOid);
		if (l120s01a == null) {
			l120s01a = new L120S01A();
		}
		// 第一頁籤Query...
		result.add(queryBorPage1(thisOid, mainId, isOther));

		// 第二頁籤Query...
		result.add(queryBorPage2(thisOid, mainId, l120s01a));

		// 第三頁籤Query...
		result.add(queryBorPage3(thisOid, mainId, l120s01a));

		// 第四頁籤Query...
		result.add(queryBorPage4(thisOid, mainId, l120s01a));

		L120M01A l120m01a = service1201.findL120m01aByMainId(mainId);
		result.set("custId", Util.trim(l120m01a.getCustId()));
		result.set("dupNo", Util.trim(l120m01a.getDupNo()));
		result.set("typCd",
				getMessage("typCd." + Util.trim(l120m01a.getTypCd())));
		result.set(EloanConstants.OID, thisOid);
		result.set("rejtCaseBefore", Util.trim(l120s01a.getRejtCaseBefore()));
		result.set("rejtCaseBefore1", Util.trim(l120s01a.getRejtCaseBefore1()));
		return result;
	}

	/**
	 * 引進Mow信用評等
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveMowTrust(PageParameters params) throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS02BPanel.class);
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		// String crdType = Util.trim(params.getString("crdType"));
		String _crdType = Util.trim(params.getString("_crdType"));
		// String crdTBR = Util.trim(params.getString("crdTBR"));
		String _crdTBR = Util.trim(params.getString("_crdTBR"));
		String crdTYear = Util.trim(params.getString("crdTYear"));
		String grade = Util.trim(params.getString("grade"));
		String finYear = Util.trim(params.getString("finYear"));
		String prospect = Util.trim(params.getString("prospect"));
		String prCustId = Util.trim(params.getString("prCustId"));
		String prDupNo = Util.trim(params.getString("prDupNo"));
		String prCNAME = Util.trim(params.getString("prCNAME"));
		String prFR = Util.trim(params.getString("prFR"));
		String prFinDate = Util.trim(params.getString("prFinDate"));
		String prMOWBr = Util.trim(params.getString("prMOWBr"));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));

		// J-110-0445_05097_B1001 於簽報書借款人基本資料之信用評等下新增欄位
		String noteId = Util.trim(params.getString("noteId"));
		String pr = Util.trim(params.getString("pr"));
		String sa = Util.trim(params.getString("sa"));
		String spr = Util.trim(params.getString("spr"));
		String fr = Util.trim(params.getString("fr"));
		String warn1 = Util.trim(params.getString("warn1"));
		String warn2 = Util.trim(params.getString("warn2"));
		String warn3 = Util.trim(params.getString("warn3"));

		boolean noMow = params.getAsBoolean("noMow", false);

		// 2024.12.19因應常常發生已核准案件又被異動評等為免辦
		// 但模擬不出來經辦操作的情境，所以直接在程式裡寫死判斷，在不該呼叫到的狀態也不會異動資料
		String LMS_CAN_NOT_CHANGE_TRUST = Util.trim(lmsService.getSysParamDataValue("LMS_CAN_NOT_CHANGE_TRUST"));
		if (Util.equals(LMS_CAN_NOT_CHANGE_TRUST, "Y")) {
			L120M01A l120m01a = service1201.findL120m01aByMainId(mainId);
			if (CreditDocStatusEnum.海外_編製中.getCode().equals(
					Util.trim(l120m01a.getDocStatus()))
					|| CreditDocStatusEnum.海外_待補件.getCode().equals(
							Util.trim(l120m01a.getDocStatus()))
							|| CreditDocStatusEnum.會簽後修改編製中.getCode().equals(
									Util.trim(l120m01a.getDocStatus()))) {
				// 是可以修改信評的文件狀態，就正常往下走
			}else{
				// 非可以修改信評的文件狀態，照理來說根本call不到這隻method才對
				// 直接回傳result
				return result;
			}
		}
		
		List<L120S01C> list = service1201.findL120s01cByMainId(mainId);
		// L120S01A l120s01a = service1205.findL120s01aByUniqueKey(mainId,
		// custId,
		// dupNo);
		// 刪除與內部評等相關的已存在資料
		List<L120S01C> delList = new ArrayList<L120S01C>();
		for (L120S01C model0 : list) {
			if (custId.equals(Util.trim(model0.getCustId()))
					&& dupNo.equals(Util.trim(model0.getDupNo()))) {
				String crdType = Util.trim(model0.getCrdType());
				if (isMow(crdType)) {
					// 內部評等
					delList.add(model0);
				} else if (isNormalTrust(crdType, true)
						|| UtilConstants.Casedoc.CrdType.未評等.equals(crdType)) {
					delList.add(model0);
				}
				
				//J-113-0044_12473_B1001 刪除@開頭試行新模型
				if("@".equals(crdType.substring(0, 1))){
					delList.add(model0);
				}
			}
		}
		if (!delList.isEmpty()) {
			service1201.deleteListL120s01c(delList);
		}
		CapAjaxFormResult myForm2Result = new CapAjaxFormResult();
		boolean hasTmpNewMow = false; //J-113-0044_12473_B1001 判斷有無試行新模型
		if (!noMow) {
			service1201.saveMow(mainId, _crdType, _crdTBR, crdTYear, grade,
					finYear, prospect, prCustId, prDupNo, prCNAME, prFR,
					prFinDate, prMOWBr, custId, dupNo);
			myForm2Result.set("grade0", grade);

			// J-110-0445_05097_B1001 於簽報書借款人基本資料之信用評等下新增欄位
			Map<String, Object> ces120Map = this.getMowGradeData(mainId,
					custId, dupNo, _crdType, noteId, pr, sa, spr, fr, warn1,
					warn2, warn3);
			myForm2Result.putAll(ces120Map);
			
			//J-113-0044_12473_B1001 儲存試行新模型資料
			String LMS_TMP_NEW_MOW = Util.trim(lmsService.getSysParamDataValue("LMS_TMP_NEW_MOW"));
			String[] tmpNewMow = new String[] {};
			if (Util.notEquals(	LMS_TMP_NEW_MOW, "")) {
				tmpNewMow = LMS_TMP_NEW_MOW.split(",");
			}
			if(tmpNewMow.length != 0){
				List<Map<String, Object>> m100m01aMapList = eloanDbBaseService.findTmpNewCesM100m01(custId, dupNo, tmpNewMow);
				if(m100m01aMapList.size() != 0){
					Map<String, Object> m100m01aMap = m100m01aMapList.get(0);
					service1201.saveTmpNewMow(m100m01aMap, mainId);
					hasTmpNewMow = true;
					myForm2Result.set("tmpNewMowcrdType", Util.trim(m100m01aMap.get("crdType")));
					myForm2Result.set("tmpNewMowgrade", Util.trim(m100m01aMap.get("fr")));
				}
			}
		} else {
			List<L120S01C> l120s01cs = new ArrayList<L120S01C>();
			service1201.getNewTrust(l120s01cs, mainId, custId, dupNo,
					UtilConstants.Casedoc.CrdType.未評等);
			service1201.getNewTrust(l120s01cs, mainId, custId, dupNo,
					UtilConstants.Casedoc.CrdType2.免辦);
			service1201.saveListL120s01c(l120s01cs);
			myForm2Result.set("grade0", pop.getProperty("l120s01c.dont"));
		}

		// 未評等(信用評等)
		myForm2Result.set("borrGrade", pop.getProperty("l120s01c.noGrade"));
		
		// J-113-0044_12473_B1001 信用風險內部評等新增顯示試行模型評等資訊
		String grade0Result = Util.trim(myForm2Result.get("grade0")) + ((Util.isNumeric(myForm2Result.get("grade0"))) ? "等" : "");
		if(hasTmpNewMow){
			String tmpNewMowcrdTypeDesc = codeService.getDescOfCodeType("CRDType", myForm2Result.get("tmpNewMowcrdType"));
			grade0Result = grade0Result + "<br>*" + pop.getProperty("l120s01c.tmpNewMow") +
			"(" + pop.getProperty("l120s01c.forReference") + ")" + ":" + tmpNewMowcrdTypeDesc +
				":" +  myForm2Result.get("tmpNewMowgrade") + pop.getProperty("l120s01c.tempGrade");
		}
		myForm2Result.set("grade0", grade0Result);
		result.set("L120S01aForm", myForm2Result);

		if (JSONObject.fromObject(myForm2Result.getResult()).isEmpty()) {
			if (params.getAsBoolean("showMsg", true)) {
				// 查無資料
				throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.查無資料), getClass());
			}
		} else {
			if (params.getAsBoolean("showMsg", true)) {
				// 印出執行成功訊息!
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
						RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
			}
		}
		return result;
	}// ;

	/**
	 * 是否為一般信評
	 * 
	 * @param crdType
	 * @param needNoDo
	 *            需要判斷免辦
	 * @return
	 */
	private boolean isNormalTrust(String crdType, boolean isNotDo) {
		if (UtilConstants.Casedoc.CrdType.DBU大型企業.equals(crdType)
				|| UtilConstants.Casedoc.CrdType.DBU中小型企業.equals(crdType)
				|| UtilConstants.Casedoc.CrdType.海外.equals(crdType)
				|| UtilConstants.Casedoc.CrdType.澳洲大型企業.equals(crdType)
				|| UtilConstants.Casedoc.CrdType.澳洲中型企業.equals(crdType)
				|| UtilConstants.Casedoc.CrdType.澳洲中小型企業.equals(crdType)
				|| UtilConstants.Casedoc.CrdType.境外.equals(crdType)
				|| UtilConstants.Casedoc.CrdType2.免辦.equals(crdType)) {
			if (UtilConstants.Casedoc.CrdType2.免辦.equals(crdType) && !isNotDo) {
				// 為免辦
				return false;
			}
			return true;
		} else {
			return false;
		}
	}

	/**
	 * 是否為國際信評
	 * 
	 * @param crdType
	 * @return
	 */
	private boolean isNationTrust(String crdType) {
		if (UtilConstants.Casedoc.CrdType.MOODY.equals(crdType)
				|| UtilConstants.Casedoc.CrdType.SAndP.equals(crdType)
				|| UtilConstants.Casedoc.CrdType.Fitch.equals(crdType)
				|| UtilConstants.Casedoc.CrdType.中華信評.equals(crdType)
				|| UtilConstants.Casedoc.CrdType.FitchTW.equals(crdType)
				|| UtilConstants.Casedoc.CrdType.KBRA.equals(crdType)) {
			// J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等
			return true;
		} else {
			return false;
		}
	}

	/**
	 * 是否為當地信評
	 * 
	 * @param crdType
	 * @return
	 */
	private boolean isLocalTrust(String crdType) {
		if (UtilConstants.Casedoc.CrdType.泰國GroupA.equals(crdType)
				|| UtilConstants.Casedoc.CrdType.泰國GroupB.equals(crdType)
				|| UtilConstants.Casedoc.CrdType.自訂.equals(crdType)) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * 是否為TCRI信評
	 * 
	 * J-109-0370_05097_B1001 配合企金一般簽報書格式修訂，新增、調整簽報作業相關內容
	 * 
	 * @param crdType
	 * @return
	 */
	private boolean isTcriTrust(String crdType) {
		if (UtilConstants.Casedoc.CrdType.TCRI評等.equals(crdType)) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * 是否為全部信評(Mow信評、當地信評除外)
	 * 
	 * @param crdType
	 * @return
	 */
	private boolean isAllTrust(String crdType) {
		if (UtilConstants.Casedoc.CrdType.DBU大型企業.equals(crdType)
				|| UtilConstants.Casedoc.CrdType.DBU中小型企業.equals(crdType)
				|| UtilConstants.Casedoc.CrdType.海外.equals(crdType)
				|| UtilConstants.Casedoc.CrdType.澳洲大型企業.equals(crdType)
				|| UtilConstants.Casedoc.CrdType.澳洲中型企業.equals(crdType)
				|| UtilConstants.Casedoc.CrdType.澳洲中小型企業.equals(crdType)
				|| UtilConstants.Casedoc.CrdType2.免辦.equals(crdType)
				|| UtilConstants.Casedoc.CrdType.未評等.equals(crdType)
				|| UtilConstants.Casedoc.CrdType.MOODY.equals(crdType)
				|| UtilConstants.Casedoc.CrdType.SAndP.equals(crdType)
				|| UtilConstants.Casedoc.CrdType.Fitch.equals(crdType)
				|| UtilConstants.Casedoc.CrdType.中華信評.equals(crdType)
				|| UtilConstants.Casedoc.CrdType.FitchTW.equals(crdType)
				|| UtilConstants.Casedoc.CrdType.KBRA.equals(crdType)) {
			// J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等
			return true;
		} else {
			return false;
		}
	}

	/**
	 * 是否為全部免辦之信評
	 * 
	 * @param crdType
	 * @return
	 */
	private boolean isNoDoTrust(String crdType) {
		if (UtilConstants.Casedoc.CrdType.DBU大型企業.equals(crdType)
				|| UtilConstants.Casedoc.CrdType.DBU中小型企業.equals(crdType)
				|| UtilConstants.Casedoc.CrdType.海外.equals(crdType)
				|| UtilConstants.Casedoc.CrdType.澳洲大型企業.equals(crdType)
				|| UtilConstants.Casedoc.CrdType.澳洲中型企業.equals(crdType)
				|| UtilConstants.Casedoc.CrdType.澳洲中小型企業.equals(crdType)
				|| UtilConstants.Casedoc.CrdType2.免辦.equals(crdType)
				|| UtilConstants.Casedoc.CrdType.未評等.equals(crdType)) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * 引進信評資料
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL120s01c(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String brno = Util.trim(params.getString("brno"));
		boolean showMsg = params.getBoolean("showMsg");
		int frag = params.getInt("frag");
		L120S01A l120s01a = service1201.findL120s01aByOid(oid);
		List<L120S01C> initList = service1201.findL120s01cByMainId(mainId);
		List<L120S01C> delList = new ArrayList<L120S01C>();
		for (L120S01C model0 : initList) {
			if (l120s01a.getCustId().equals(model0.getCustId())
					&& l120s01a.getDupNo().equals(model0.getDupNo())) {
				String crdType = Util.trim(model0.getCrdType());
				switch (frag) {
				case 1: // 信用評等
					if (isNormalTrust(crdType, true)
							|| UtilConstants.Casedoc.CrdType.未評等
									.equals(crdType)) {
						delList.add(model0);
					} else if (isMow(crdType)) {
						delList.add(model0);
					}
					break;
				case 3: // 國際信評
					if (isNationTrust(crdType)) {
						delList.add(model0);
					}
					break;
				case 5: // 當地信評
					if (isLocalTrust(crdType)) {
						delList.add(model0);
					}
					break;
				case 7: // TCRI信評
					// J-109-0370_05097_B1001 配合企金一般簽報書格式修訂，新增、調整簽報作業相關內容
					if (isTcriTrust(crdType)) {
						delList.add(model0);
					}
					break;
				case 999: // 全部查詢
					if (isAllTrust(crdType)) {
						delList.add(model0);
					}
					break;
				case 4: // 全部免辦
					delList.add(model0);
				}
				
				//J-113-0044_12473_B1001 刪除@開頭試行新模型
				if("@".equals(crdType.substring(0, 1))){
					delList.add(model0);
				}
			}
		}

		List<L120S01C> list = new ArrayList<L120S01C>();
		try {
			list = service1201.findL120s01c(mainId,
					Util.trim(l120s01a.getCustId()),
					Util.trim(l120s01a.getDupNo()), Util.trim(brno), delList,
					frag);
		} catch (Exception e) {
			logger.error(
					"[queryL120s01c] service1205.findL120s01c EXCEPTION!!", e);
			Map<String, String> param = new HashMap<String, String>();
			param.put("msg",
					"[queryL120s01c] service1205.findL120s01c EXCEPTION!!");
			if (params.getAsBoolean("showMsg", true)) {
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0067", param), getClass());
			}
			return result;
		}
		CapAjaxFormResult myForm2Result = setTrust(list, l120s01a);
		if (frag != 3) {
			// 不是國際信用評等則不需針國際信用評等設值
			myForm2Result.removeField("crdTYear1");
			myForm2Result.removeField("grade1");
			myForm2Result.removeField("crdTYear2");
			myForm2Result.removeField("grade2");
			myForm2Result.removeField("crdTYear3");
			myForm2Result.removeField("grade3");
			myForm2Result.removeField("crdTYear7");
			myForm2Result.removeField("grade7");
			// J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等
			myForm2Result.removeField("crdTYear8");
			myForm2Result.removeField("grade8");
			myForm2Result.removeField("crdTYear9");
			myForm2Result.removeField("grade9");
		}

		// J-110-0445_05097_B1001 於簽報書借款人基本資料之信用評等下新增欄位
		if (frag == 1 || frag == 4) {
			// 信用評等、全部免辦

			// 不是採用信用風險內部評等資料
			// 有無警訊
			myForm2Result.set("hasWarnGrade", "3"); // 不適用
			myForm2Result.set("warnGradeNote", "");
			// 有無調整評等
			myForm2Result.set("hasDowngradeGrade", "");// 空白
			myForm2Result.set("downgradeGradeNote", "");
			myForm2Result.set("downgradeGradeStatus", "");

		}

		result.set("L120S01aForm", myForm2Result);
		if (JSONObject.fromObject(myForm2Result.getResult()).isEmpty()) {
			if (showMsg) {
				// 查無資料
				throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.查無資料), getClass());
			}
		} else {
			if (showMsg) {
				// 印出執行成功訊息!
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
						RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
			}
		}
		return result;
	}

	/**
	 * 設定信用評等
	 * 
	 * @param list
	 * @param l120s01a
	 * @return
	 */
	private CapAjaxFormResult setTrust(List<L120S01C> list, L120S01A l120s01a) {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS02BPanel.class);
		CapAjaxFormResult myForm2Result = new CapAjaxFormResult();
		String custId = Util.trim(l120s01a.getCustId());
		String dupNo = Util.trim(l120s01a.getDupNo());
		for (L120S01C model : list) {
			if (custId.equals(Util.trim(model.getCustId()))
					&& dupNo.equals(Util.trim(model.getDupNo()))) {
				String crdType = Util.trim(model.getCrdType());
				if (isNormalTrust(crdType, false)) {
					// 信用評等
					myForm2Result.set("borrGrade", Util.trim(model.getGrade())
							+ "級");
				} else if (UtilConstants.Casedoc.CrdType.未評等.equals(crdType)) {
					// 未評等(信用評等)
					myForm2Result.set("borrGrade",
							pop.getProperty("l120s01c.noGrade"));
				} else if (UtilConstants.Casedoc.CrdType2.免辦.equals(crdType)) {
					// 免辦(內部信評)
					myForm2Result.set("grade0",
							pop.getProperty("l120s01c.dont"));
				} else if (isLocalTrust(crdType)) {
					// 當地信評
					Map<String, String> map = new HashMap<String, String>();
					map.put(UtilConstants.Casedoc.CrdType.泰國GroupA, "4");
					map.put(UtilConstants.Casedoc.CrdType.泰國GroupB, "5");
					map.put(UtilConstants.Casedoc.CrdType.自訂, "6");

					String key = map.get(crdType);
					if (Util.isNotEmpty(key)) {
						myForm2Result.set("crdTYear" + key, CapDate.formatDate(
								model.getCrdTYear(),
								UtilConstants.DateFormat.YYYY_MM_DD));
						myForm2Result.set("grade" + key,
								Util.trim(model.getGrade()));
					}

				} else if (isTcriTrust(crdType)) {
					// J-109-0370_05097_B1001 配合企金一般簽報書格式修訂，新增、調整簽報作業相關內容
					Map<String, String> map = new HashMap<String, String>();
					map.put(UtilConstants.Casedoc.CrdType.TCRI評等, "Tcri");

					String key = map.get(crdType);
					if (Util.isNotEmpty(key)) {
						// myForm2Result.set("crdTYear" + key,
						// CapDate.formatDate(
						// model.getCrdTYear(),
						// UtilConstants.DateFormat.YYYY_MM_DD));
						myForm2Result
								.set("grade" + key,
										(Util.equals(
												Util.trim(model.getGrade()), "") ? ""
												: Util.trim(model.getGrade())
														+ pop.getProperty("l120s01c.tempGrade"))
												+ " "
												+ MapUtils.getString(
														service1201
																.formatTCRIGrade(Util
																		.trim(model
																				.getGrade())),
														"t_grade1"));
					}

				} else {
					// 國際信評
					Map<String, String> map = new HashMap<String, String>();
					map.put(UtilConstants.Casedoc.CrdType.MOODY, "1");
					map.put(UtilConstants.Casedoc.CrdType.SAndP, "2");
					map.put(UtilConstants.Casedoc.CrdType.Fitch, "3");
					map.put(UtilConstants.Casedoc.CrdType.中華信評, "7");
					// J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等
					map.put(UtilConstants.Casedoc.CrdType.FitchTW, "8");
					map.put(UtilConstants.Casedoc.CrdType.KBRA, "9");

					String key = map.get(crdType);
					if (Util.isNotEmpty(key)) {
						myForm2Result.set("crdTYear" + key, CapDate.formatDate(
								model.getCrdTYear(),
								UtilConstants.DateFormat.YYYY_MM_DD));
						myForm2Result.set("grade" + key,
								Util.trim(model.getGrade()));
					}
				}
			}
		}
		return myForm2Result;
	}

	/**
	 * Set L120S01A
	 * 
	 * @param l120s01a
	 * @param formL120s01a
	 * @return
	 */
	private L120S01A setL120s01a(L120S01A l120s01a, String formL120s01a) {
		JSONObject json = JSONObject.fromObject(formL120s01a);
		// 移除不需儲存的欄位
		json.remove("rejtBrNo");
		json.remove("rejtCode");
		json.remove("rejtReason");
		json.remove("rejtDate");
		// ---
		json.remove("rejtBrNo1");
		json.remove("rejtCode1");
		json.remove("rejtReason1");
		json.remove("rejtDate1");
		// ---
		json.remove("blackName");
		json.remove("blackDate");
		String buscd = Util.trim(json.getString("_buscd"));
		String renCd = Util.trim(json.getString("_renCd"));

		// J-105-0179-001 Web e-Loan企金授信建立「往來異常通報戶」紀錄查詢及於簽報書上顯示查詢結果功能
		json.remove("abnormalBrNo");
		json.remove("abnormalDate");
		json.remove("abnormalBrNo1");
		json.remove("abnormalDate1");

		// J-111-0535_05097_B1001 Web e-Loan企金授信配合「ESG綜效調查表 」建置，於簽報書增設相對應欄位
		json.remove("esgRejtBrNo");
		json.remove("esgRejtEmpNo");
		json.remove("esgRejtDate");

		// 將L120S01aForm data 置入 L120S01A Model
		DataParse.toBean(json, l120s01a);
		if (Util.isNotEmpty(renCd)) {
			l120s01a.setRenCd(Util.trim(renCd));
		}
		if (Util.isNotEmpty(buscd)) {
			l120s01a.setBusCode(Util.trim(buscd));
		}
		return l120s01a;
	}

	/**
	 * Set L120S01B
	 * 
	 * @param l120s01a
	 * @param l120s01b
	 * @param formL120s01a
	 * @param rcdFlag
	 * @param runFlag
	 * @param finFlag
	 * @param invMDscr
	 * @param errorMsg
	 * @return
	 */
	private L120S01B setL120s01b(L120S01A l120s01a, L120S01B l120s01b,
			String formL120s01a, boolean rcdFlag, boolean runFlag,
			boolean finFlag, String invMDscr, StringBuilder errorMsg,
			boolean noSave2, boolean noSave3, String prodMkt, boolean isPrint) {
		L120M01A meta = service1201.findL120m01aByMainId(Util.trim(l120s01a
				.getMainId()));
		if (meta == null) {
			meta = new L120M01A();
		}
		if (l120s01b == null) {
			l120s01b = new L120S01B();
			l120s01b.setMainId(Util.trim(l120s01a.getMainId()));
			l120s01b.setCustId(Util.trim(l120s01a.getCustId()));
			l120s01b.setDupNo(Util.trim(l120s01a.getDupNo()));
		}
		// 將L120S01aForm data 置入L120S01B Model
		DataParse.toBean(formL120s01a, l120s01b);
		l120s01b.setAprCurr(UtilConstants.CURR.TWD);
		if (UtilConstants.DEFAULT.是.equals(Util.trim(l120s01a.getKeyMan()))) {
			// 如果為主要借款人
			// 有無大陸投資相關必填欄位檢核
			l120s01a.setChkYN(checkGotoChina(errorMsg, l120s01b));
			if (!BeanValidator.isValid(l120s01b, Check2.class)) {
				errorMsg.append(BeanValidator.getValidMsg(l120s01b,
						LMSS02BPage.class, Check2.class));
				l120s01a.setChkYN(UtilConstants.DEFAULT.否);
			}
		} else {
			// 如果不是主要借款人則檢查相關身份與主要借款人關係是否不為空
			// J-110-0458 企金授權內其他 -
			// 「簡易簽報」選項，適用方案「LIBOR退場變更利率條件簡易簽報」，可能毫無相關，故拿掉此檢核
			L120M01A l120m01a = service1201.findL120m01aByMainId(l120s01a
					.getMainId());
			if (l120m01a != null) {
				if (!(lmsService.isLiborExitCase(l120m01a) || lmsService.isEuroyenTiborExitCase(l120m01a))) {
					if (!BeanValidator.isValid(l120s01a, Check3.class)) {
						errorMsg.append(BeanValidator.getValidMsg(l120s01a,
								LMSS02BPage.class, Check3.class));
					}
					if (Util.isNotEmpty(l120s01a.getCustPos())
							&& Util.isNotEmpty(l120s01a.getCustRlt())) {
						// 有無大陸投資相關必填欄位檢核
						l120s01a.setChkYN(checkGotoChina(errorMsg, l120s01b));
					} else {
						l120s01a.setChkYN(UtilConstants.DEFAULT.否);
					}
				} else {
					// LIBOR退場 不檢查大陸投資跟與主要借款人關係
					l120s01a.setChkYN(UtilConstants.DEFAULT.是);
				}
			}
			if (!BeanValidator.isValid(l120s01b, Check2.class)) {
				errorMsg.append(BeanValidator.getValidMsg(l120s01b,
						LMSS02BPage.class, Check2.class));
				l120s01a.setChkYN(UtilConstants.DEFAULT.否);
			}
		}

		if (Util.isNotEmpty(l120s01a.getRejtChairmanId())) {
			String dupNo = l120s01b.getChairmanDupNo();
			if (Util.isEmpty(dupNo)) {
				dupNo = "0";
			}
			if (Util.equals(l120s01a.getRejtChairmanId(),
					l120s01b.getChairmanId(), false)
					&& Util.equals(l120s01a.getRejtChairmanDupNo(), dupNo,
							false)) {
				// 負責人婉卻紀錄的id==目前的負責人
			} else {
				// LMSS02D.html22=負責人婉卻紀錄之統一編號{0}與目前負責人{1}不一致，請重新引進
				Properties prop = MessageBundleScriptCreator
						.getComponentResource(LMSS02BPage.class);
				errorMsg.append(MessageFormat.format(
						prop.getProperty("LMSS02D.html22"),
						Util.trim(l120s01a.getRejtChairmanId()) + " "
								+ Util.trim(l120s01a.getRejtChairmanDupNo()),
						Util.trim(l120s01b.getChairmanId()) + " "
								+ Util.trim(l120s01b.getChairmanDupNo())));
				errorMsg.append("<br/>");
				l120s01a.setChkYN(UtilConstants.DEFAULT.否);
			}
		} else {
			if (Util.isNotEmpty(l120s01b.getChairmanId())) {
				if (meta != null
						&& UtilConstants.Casedoc.DocCode.一般.equals(meta
								.getDocCode())) {
					// LMSS02D.html23=負責人為{0}。尚未執行「{1}」
					// LMSS02D.html20=引進負責人婉卻紀錄
					Properties prop = MessageBundleScriptCreator
							.getComponentResource(LMSS02BPage.class);
					errorMsg.append(MessageFormat.format(
							prop.getProperty("LMSS02D.html23"),
							Util.trim(l120s01b.getChairmanId()),
							prop.getProperty("LMSS02D.html20")));
					errorMsg.append("<br/>");
					l120s01a.setChkYN(UtilConstants.DEFAULT.否);
				}
			}
		}

		// J-105-0179-001 Web e-Loan企金授信建立「往來異常通報戶」紀錄查詢及於簽報書上顯示查詢結果功能
		if (Util.isNotEmpty(l120s01a.getAbnormalChairmanId())) {
			String dupNo = l120s01b.getChairmanDupNo();
			if (Util.isEmpty(dupNo)) {
				dupNo = "0";
			}
			if (Util.equals(l120s01a.getAbnormalChairmanId(),
					l120s01b.getChairmanId(), false)
					&& Util.equals(l120s01a.getAbnormalChairmanDupNo(), dupNo,
							false)) {
				// 負責人婉卻紀錄的id==目前的負責人
			} else {
				// LMSS02D.html41=負責人異常通報紀錄之統一編號{0}與目前負責人{1}不一致，請重新引進
				Properties prop = MessageBundleScriptCreator
						.getComponentResource(LMSS02BPage.class);
				errorMsg.append(MessageFormat.format(
						prop.getProperty("LMSS02D.html41"),
						Util.trim(l120s01a.getAbnormalChairmanId())
								+ " "
								+ Util.trim(l120s01a.getAbnormalChairmanDupNo()),
						Util.trim(l120s01b.getChairmanId()) + " "
								+ Util.trim(l120s01b.getChairmanDupNo())));
				errorMsg.append("<br/>");
				l120s01a.setChkYN(UtilConstants.DEFAULT.否);
			}
		} else {
			if (Util.isNotEmpty(l120s01b.getChairmanId())) {
				if (meta != null
						&& UtilConstants.Casedoc.DocCode.一般.equals(meta
								.getDocCode())) {
					// LMSS02D.html23=負責人為{0}。尚未執行「{1}」
					// LMSS02D.html39=引進負責人異常通報紀錄
					Properties prop = MessageBundleScriptCreator
							.getComponentResource(LMSS02BPage.class);
					errorMsg.append(MessageFormat.format(
							prop.getProperty("LMSS02D.html23"),
							Util.trim(l120s01b.getChairmanId()),
							prop.getProperty("LMSS02D.html39")));
					errorMsg.append("<br/>");
					l120s01a.setChkYN(UtilConstants.DEFAULT.否);
				}
			}
		}

		// J-105-0228-001 Web e-Loan企金授信簽報書新增私募基金相關建檔與報表。
		if (Util.isEmpty(l120s01b.getPrivateEquityFg())
				|| Util.isEmpty(l120s01b.getBfPrivateEquityFg())) {
			Properties prop = MessageBundleScriptCreator
					.getComponentResource(LMSS02BPage.class);
			// l120s01b.error1=欄位「隸屬私募基金旗下事業」變更前/後註記不得為空白，請重新引進
			errorMsg.append(prop.getProperty("l120s01b.error1"));
			errorMsg.append("<br/>");
			l120s01a.setChkYN(UtilConstants.DEFAULT.否);
		}

		// J-105-0228-001 Web e-Loan企金授信簽報書新增私募基金相關建檔與報表。
		// J-112-0125 私募基金
		List<L120S01T> l120s01tYs = service1201.findL120s01tByCustIdFlag(
				l120s01b.getMainId(), Util.nullToSpace(l120s01b.getCustId()),
				Util.nullToSpace(l120s01b.getDupNo()), "Y");
		if (Util.equals(l120s01b.getPrivateEquityFg(), "Y")) {
			// if (Util.equals(l120s01b.getPrivateEquityNo(), "")) {
			if (l120s01tYs != null && !l120s01tYs.isEmpty()) {

			} else {
				Properties prop = MessageBundleScriptCreator
						.getComponentResource(LMSS02BPage.class);
				// l120s01b.error2=借戶有隸屬私募基金旗下事業，欄位「隸屬私募基金代碼」不得為空白
				errorMsg.append(prop.getProperty("l120s01b.error2"));
				errorMsg.append("<br/>");
				l120s01a.setChkYN(UtilConstants.DEFAULT.否);
			}
		} else {
			// J-112-0125 私募基金
			// 清除私募基金資料
			if (l120s01tYs != null && !l120s01tYs.isEmpty()) {
				service1201.deleteListL120s01t(l120s01tYs);
			}
		}

		// J-108-0145_05097_B1001 Web e-Loan 國內外企金授信私募基金案件調整實質受益人控管
		String beneficiaryChk = Util.trim(l120s01b.getBeneficiaryChk());

		if (Util.equals(beneficiaryChk, "")) {
			Properties prop = MessageBundleScriptCreator
					.getComponentResource(LMSS02BPage.class);
			// l120s02.alert38=欄位「{0}」不得空白!
			// l120s01b.beneficiaryChk=實質受益人辨識完成註記
			errorMsg.append(MessageFormat.format(
					prop.getProperty("l120s02.alert38"),
					prop.getProperty("l120s01b.beneficiaryChk")));
			errorMsg.append("<br/>");
			l120s01a.setChkYN(UtilConstants.DEFAULT.否);
		}

		if (Util.equals(beneficiaryChk, "N")) {
			if (Util.notEquals(Util.trim(l120s01b.getPrivateEquityFg()), "Y")) {
				// 只有私覆基金案件可以延後辦理實質受益人辨識

				Properties prop = MessageBundleScriptCreator
						.getComponentResource(LMSS02BPage.class);
				// l120s02.alert39=非私募基金案件，欄位「{0}」不得為「{1}」!
				// l120s01b.beneficiaryChk=實質受益人辨識完成註記
				Map<String, String> subjectMap = codeTypeService
						.findByCodeType("l120s01b_beneficiaryChk", LMSUtil
								.getLocale().toString());

				errorMsg.append(MessageFormat.format(
						prop.getProperty("l120s02.alert39"),
						prop.getProperty("l120s01b.beneficiaryChk"),
						Util.nullToSpace(subjectMap.get(Util.nullToSpace("N")))));
				errorMsg.append("<br/>");
				l120s01a.setChkYN(UtilConstants.DEFAULT.否);
			}
		}

		// J-106-0029-003 洗錢防制-新增實質受益人
		// J-107-0070-001 Web e-Loan 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
		// J-107-0176 代發機制
		if (Util.equals(beneficiaryChk, "Y")) {

			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			Map<String, Object> chkInsteadMap = amlRelateService
					.checkInstead(Util.trim(l120s01a.getMainId()));
			boolean instead = MapUtils.getBooleanValue(chkInsteadMap,
					"instead", false);
			String queryBrId = MapUtils.getString(chkInsteadMap, "queryBrId");
			String unitNo = (instead ? queryBrId : user.getUnitNo());
			String busCode = Util.trim(l120s01b.getBusCode());
			if (Util.notEquals(busCode, "")
					&& !LMSUtil.isBusCode_060000_130300(busCode)) {

				if (amlRelateService.needChkBeneficiaryBeforeSendBoss(unitNo)) {
					if (Util.equals(l120s01b.getBeneficiary(), "")) {

						Properties prop = MessageBundleScriptCreator
								.getComponentResource(LMSS20APanel.class);

						// AML.error005=欄位「{0}」不得空白
						// L120S09a.checkbox7=實質受益人
						errorMsg.append(MessageFormat.format(
								prop.getProperty("AML.error005"),
								prop.getProperty("L120S09a.checkbox7")));
						errorMsg.append("<br/>");
						l120s01a.setChkYN(UtilConstants.DEFAULT.否);
					}
				}

				// J-107-0070-001 Web e-Loan
				// 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
				if (amlRelateService.needChkSeniorMgrBeforeSendBoss(unitNo)) {
					if (Util.equals(l120s01b.getSeniorMgr(), "")) {

						Properties prop = MessageBundleScriptCreator
								.getComponentResource(LMSS20APanel.class);

						// AML.error005=欄位「{0}」不得空白
						// L120S09a.checkbox10=高階管理人員
						errorMsg.append(MessageFormat.format(
								prop.getProperty("AML.error005"),
								prop.getProperty("L120S09a.checkbox10")));
						errorMsg.append("<br/>");
						l120s01a.setChkYN(UtilConstants.DEFAULT.否);
					}
				}

				// J-108-0039_05097_B1001 Web e-Loan
				// 國內企金授信系統簽報、動審AML頁籤將借戶之「具控制權人」納入應查詢比對黑名單之對象。
				if (amlRelateService.needChkCtrlPeoBeforeSendBoss(unitNo)) {
					if (Util.equals(l120s01b.getCtrlPeo(), "")) {

						Properties prop = MessageBundleScriptCreator
								.getComponentResource(LMSS20APanel.class);

						// AML.error005=欄位「{0}」不得空白
						// L120S09a.checkbox11=具控制權人
						errorMsg.append(MessageFormat.format(
								prop.getProperty("AML.error005"),
								prop.getProperty("L120S09a.checkbox11")));
						errorMsg.append("<br/>");
						l120s01a.setChkYN(UtilConstants.DEFAULT.否);
					}
				}

			}
		}

		// J-108-0143_05097_B1001 Web e-Loan國內外企金額度明細表簽報性質新作時加註(新客戶往來原有客戶往來)
		if (Util.isEmpty(l120s01a.getNewCustFlag())) {
			Properties prop = MessageBundleScriptCreator
					.getComponentResource(LMSS02BPage.class);
			// l120s01a.newCustFlag=新客戶往來註記
			// l120s02.alert38=欄位「{0}」不得空白!
			errorMsg.append(MessageFormat.format(
					prop.getProperty("l120s02.alert38"),
					prop.getProperty("l120s01a.newCustFlag")));
			errorMsg.append("<br/>");
			l120s01a.setChkYN(UtilConstants.DEFAULT.否);
		}

		if (!noSave2) {
			// J-108-0243 微型企業 企金一般授權內
			if (Util.equals("Y", meta.getMiniFlag())
					&& UtilConstants.Casedoc.DocType.企金.equals(meta
							.getDocType())
					&& UtilConstants.Casedoc.DocKind.授權內.equals(meta
							.getDocKind())
					&& UtilConstants.Casedoc.DocCode.一般.equals(meta
							.getDocCode())) {
				l120s01b.setRcdFlag((rcdFlag ? UtilConstants.DEFAULT.是
						: UtilConstants.DEFAULT.否));
			} else {
				l120s01b.setRcdFlag((rcdFlag
						&& (UtilConstants.Casedoc.DocType.企金.equals(meta
								.getDocType())
								&& UtilConstants.Casedoc.DocKind.授權外
										.equals(meta.getDocKind()) && UtilConstants.Casedoc.DocCode.一般
								.equals(meta.getDocCode())) ? UtilConstants.DEFAULT.是
						: UtilConstants.DEFAULT.否));
			}
		}
		if (!noSave2) {
			if (Util.equals("Y", meta.getMiniFlag())
					&& UtilConstants.Casedoc.DocType.企金.equals(meta
							.getDocType())
					&& UtilConstants.Casedoc.DocKind.授權內.equals(meta
							.getDocKind())
					&& UtilConstants.Casedoc.DocCode.一般.equals(meta
							.getDocCode())) {
				l120s01b.setRunFlag((runFlag ? UtilConstants.DEFAULT.是
						: UtilConstants.DEFAULT.否));
			} else {
				l120s01b.setRunFlag((runFlag
						&& (UtilConstants.Casedoc.DocType.企金.equals(meta
								.getDocType())
								&& UtilConstants.Casedoc.DocKind.授權外
										.equals(meta.getDocKind()) && UtilConstants.Casedoc.DocCode.一般
								.equals(meta.getDocCode())) ? UtilConstants.DEFAULT.是
						: UtilConstants.DEFAULT.否));
			}
		}
		if (!noSave3) {
			if (Util.equals("Y", meta.getMiniFlag())
					&& UtilConstants.Casedoc.DocType.企金.equals(meta
							.getDocType())
					&& UtilConstants.Casedoc.DocKind.授權內.equals(meta
							.getDocKind())
					&& UtilConstants.Casedoc.DocCode.一般.equals(meta
							.getDocCode())) {
				l120s01b.setFinFlag((finFlag ? UtilConstants.DEFAULT.是
						: UtilConstants.DEFAULT.否));
			} else {
				l120s01b.setFinFlag((finFlag
						&& (UtilConstants.Casedoc.DocType.企金.equals(meta
								.getDocType())
								&& UtilConstants.Casedoc.DocKind.授權外
										.equals(meta.getDocKind()) && UtilConstants.Casedoc.DocCode.一般
								.equals(meta.getDocCode())) ? UtilConstants.DEFAULT.是
						: UtilConstants.DEFAULT.否));
			}
		}
		// l120s01b.setInvMDscr(Util.truncateString(Util.trim(invMDscr), 1536));
		l120s01b.setInvMDscr(Util.trim(invMDscr));
		// J-109-0370 相關評估改版
		l120s01b.setIsPrint(isPrint ? "Y" : "N");
		l120s01b.setProdMkt(Util.trim(prodMkt));
		if (UtilConstants.Casedoc.DocType.企金.equals(meta.getDocType())
				&& UtilConstants.Casedoc.DocKind.授權外.equals(meta.getDocKind())
				&& UtilConstants.Casedoc.DocCode.一般.equals(meta.getDocCode())) {
			L120M01I l120m01i = service1201.findL120m01iByMainId(meta
					.getMainId());
			boolean isOut = true;// LMSUtil.isOutNewVer(meta, l120m01i);
			if (isOut) {
				Properties prop = MessageBundleScriptCreator
						.getComponentResource(LMSS02BPage.class);
				List<L120S01C> l120s01cList = service1201.findL120s01cByCustId(
						Util.trim(l120s01a.getMainId()), l120s01a.getCustId(),
						l120s01a.getDupNo());
				boolean NA = false;
				boolean NO = false;
				for (L120S01C l120s01c : l120s01cList) {
					String crdType = Util.trim(l120s01c.getCrdType());
					if (UtilConstants.Casedoc.CrdType.未評等.equals(crdType)) {
						NA = true;
					} else if (UtilConstants.Casedoc.CrdType2.免辦
							.equals(crdType)) {
						NO = true;
					}
				}
				if (NA && NO
						&& Util.isEmpty(Util.trim(l120s01b.getNoneGrade()))) {
					errorMsg.append(MessageFormat.format(
							prop.getProperty("l120s02.alert38"),
							prop.getProperty("l120s01b.noneGrade")));
					errorMsg.append("<br/>");
					l120s01a.setChkYN(UtilConstants.DEFAULT.否);
				}

				// J-110-0445_05097_B1001 於簽報書借款人基本資料之信用評等下新增欄位
				if (Util.equals(Util.trim(l120s01b.getHasWarnGrade()), "")) {
					// l120s01b.hasWarnGrade=評等有無警示訊號
					errorMsg.append(MessageFormat.format(
							prop.getProperty("l120s02.alert38"),
							prop.getProperty("l120s01b.hasWarnGrade")));
					errorMsg.append("<br/>");
					l120s01a.setChkYN(UtilConstants.DEFAULT.否);
				} else {
					if (Util.equals(Util.trim(l120s01b.getHasWarnGrade()), "1")) {
						if (Util.equals(Util.trim(l120s01b.getWarnGradeNote()),
								"")) {

							// l120s01b.warnGradeNote=評等警示訊號說明
							errorMsg.append(MessageFormat.format(
									prop.getProperty("l120s02.alert38"),
									prop.getProperty("l120s01b.warnGradeNote")));
							errorMsg.append("<br/>");
							l120s01a.setChkYN(UtilConstants.DEFAULT.否);
						}
					}
				}

				if (Util.isEmpty(Util.nullToSpace(l120s01b.getUnfConFlag()))) {
					errorMsg.append(MessageFormat.format(
							prop.getProperty("l120s02.alert38"),
							prop.getProperty("l120s01b.unfConMemo")));
					errorMsg.append("<br/>");
					l120s01a.setChkYN(UtilConstants.DEFAULT.否);
				} else if (Util.equals(
						Util.nullToSpace(l120s01b.getUnfConFlag()), "Y")) {
					if (Util.isEmpty(Util.nullToSpace(l120s01b.getUnfConMemo()))) {
						errorMsg.append(MessageFormat.format(
								prop.getProperty("l120s02.alert38"),
								prop.getProperty("l120s01b.unfConMemo")));
						errorMsg.append("<br/>");
						l120s01a.setChkYN(UtilConstants.DEFAULT.否);
					}
				}
			}
		}

		// J-110-0371 新版簽報書_個人
		if (UtilConstants.Casedoc.DocType.企金.equals(meta.getDocType())) {
			String busCode = Util.trim(l120s01b.getBusCode());
			if (Util.notEquals(busCode, "")
					&& LMSUtil.isBusCode_060000_130300(busCode)) {
				Properties prop = MessageBundleScriptCreator
						.getComponentResource(LMSS02BPage.class);
				if (Util.isEmpty(Util.nullToSpace(l120s01b.getEstDate()))) {
					errorMsg.append(MessageFormat.format(
							prop.getProperty("l120s02.alert38"),
							prop.getProperty("l120s01b.estdate")));
					errorMsg.append("<br/>");
					l120s01a.setChkYN(UtilConstants.DEFAULT.否);
				}
				if (Util.isEmpty(Util.nullToSpace(l120s01b.getJobType()))) {
					errorMsg.append(MessageFormat.format(
							prop.getProperty("l120s02.alert38"),
							prop.getProperty("l120s01b.jobType")));
					errorMsg.append("<br/>");
					l120s01a.setChkYN(UtilConstants.DEFAULT.否);
				}
				if (Util.isEmpty(Util.nullToSpace(l120s01b.getPayCurr()))
						|| Util.isEmpty(Util.nullToSpace(l120s01b.getPayAmt()))) {
					errorMsg.append(MessageFormat.format(
							prop.getProperty("l120s02.alert38"),
							prop.getProperty("l120s01b.payAmt")));
					errorMsg.append("<br/>");
					l120s01a.setChkYN(UtilConstants.DEFAULT.否);
				}
				if (Util.isEmpty(Util.nullToSpace(l120s01b.getEDueDate()))) {
					errorMsg.append(MessageFormat.format(
							prop.getProperty("l120s02.alert38"),
							prop.getProperty("l120s01b.eDueDate")));
					errorMsg.append("<br/>");
					l120s01a.setChkYN(UtilConstants.DEFAULT.否);
				}
				if (Util.isEmpty(Util.nullToSpace(l120s01b.getRtnChq()))) {
					errorMsg.append(MessageFormat.format(
							prop.getProperty("l120s02.alert38"),
							prop.getProperty("l120s01b.rtnChq")));
					errorMsg.append("<br/>");
					l120s01a.setChkYN(UtilConstants.DEFAULT.否);
				}
				if (Util.isEmpty(Util.nullToSpace(l120s01b.getBlackList()))) {
					errorMsg.append(MessageFormat.format(
							prop.getProperty("l120s02.alert38"),
							prop.getProperty("l120s01b.blackList")));
					errorMsg.append("<br/>");
					l120s01a.setChkYN(UtilConstants.DEFAULT.否);
				}
			}
		}

		return l120s01b;
	}

	/**
	 * 有無大陸投資相關必填欄位檢核
	 * 
	 * @param errorMsg
	 * @param l120s01b
	 * @return
	 */
	private String checkGotoChina(StringBuilder errorMsg, L120S01B l120s01b) {
		String chkYn = null;
		if (UtilConstants.Casedoc.InvMFlag.有.equals(Util.trim(l120s01b
				.getInvMFlag()))) {
			// 若有赴大陸投資
			if (!BeanValidator.isValid(l120s01b, Check.class)) {
				errorMsg.append(BeanValidator.getValidMsg(l120s01b,
						LMSS02BPage.class, Check.class));
			}
			if (Util.isNotEmpty(l120s01b.getAprCurr())
					&& Util.isNotEmpty(l120s01b.getInvMAmt())
					&& Util.isNotEmpty(l120s01b.getInvMCurr())
					&& Util.isNotEmpty(l120s01b.getAprAmt())) {
				// 如果赴大陸投資金額和經濟部投審金額皆有輸入
				chkYn = UtilConstants.DEFAULT.是;
			} else {
				chkYn = UtilConstants.DEFAULT.否;
			}
		} else {
			// 若無赴大陸投資
			chkYn = UtilConstants.DEFAULT.是;
		}
		if (!BeanValidator.isValid(l120s01b, Check2.class)) {
			chkYn = UtilConstants.DEFAULT.否;
		}
		return Util.trim(chkYn);
	}

	/**
	 * Set L120S01D
	 * 
	 * @param l120s01a
	 * @param l120s01d
	 * @param formL120s01a
	 * @return
	 */
	private L120S01D setL120s01d(L120S01A l120s01a, L120S01D l120s01d, String formL120s01a) {
		if (l120s01d == null) {
			l120s01d = new L120S01D();
			l120s01d.setMainId(Util.trim(l120s01a.getMainId()));
			l120s01d.setCustId(Util.trim(l120s01a.getCustId()));
			l120s01d.setDupNo(Util.trim(l120s01a.getDupNo()));
		}
		// 將L120S01aForm data 置入L120S01D Model
		DataParse.toBean(formL120s01a, l120s01d);
		return l120s01d;
	}

	/**
	 * Set L120S01G
	 * 
	 * @param l120s01a
	 * @param l120s01g
	 * @param type
	 * @param form1L120s01g
	 * @return
	 */
	private L120S01G setL120s01g(L120S01A l120s01a, L120S01G l120s01g,
			String type, String form1L120s01g) {
		JSONObject jsontest = Util.isEmpty(form1L120s01g) ? new JSONObject()
				: JSONObject.fromObject(form1L120s01g);
		if (l120s01g == null) {
			l120s01g = new L120S01G();
			l120s01g.setMainId(Util.trim(l120s01a.getMainId()));
			l120s01g.setCustId(Util.trim(l120s01a.getCustId()));
			l120s01g.setDupNo(Util.trim(l120s01a.getDupNo()));
			l120s01g.setDataType(type);
		}
		// 設定使用者所輸入Ckeditor的資料
		l120s01g.setDataDscr(UtilConstants.Casedoc.L120s01gType.營運概況分析與評估
				.equals(type) ? Util.trim(jsontest.get("idDscr1"))
				: UtilConstants.Casedoc.L120s01gType.財務狀況分析與評估.equals(type) ? Util
						.trim(jsontest.get("idDscr2")) : null);
		return l120s01g;
	}

	/**
	 * Set L120S01F
	 * 
	 * @param l120s01a
	 * @param l120s01f
	 * @param formL120s01f
	 * @param params
	 * @return
	 * @throws CapMessageException
	 */
	private L120S01F setL120s01f(L120S01A l120s01a, L120S01F l120s01f,
			String formL120s01f, PageParameters params) throws CapMessageException {
		if (l120s01f == null) {
			l120s01f = new L120S01F();
			l120s01f.setMainId(Util.trim(l120s01a.getMainId()));
			l120s01f.setCustId(Util.trim(l120s01a.getCustId()));
			l120s01f.setDupNo(Util.trim(l120s01a.getDupNo()));
		}
		// 將L120S01fForm data 置入 L120S01F Model
		DataParse.toBean(formL120s01f, l120s01f);

		// 配合徵信資信簡表平均存款可以打N.A.
		String formLms120s01f = params.getString("L120S01fForm");
		JSONObject jsonObj = new JSONObject();
		if (!Util.isEmpty(formLms120s01f)) {
			jsonObj = JSONObject.fromObject(formLms120s01f);
		}

		if ("N.A.".equals(Util.nullToSpace(jsonObj.get("dpAvgAmt")))) {
			l120s01f.setDpAvgAmt(null);
		}

		if (Util.isNotEmpty(Util.trim(l120s01f.getAvgURate()))) {
			double avgURate = l120s01f.getAvgURate();
			if (avgURate <= 999.99 && avgURate >= 0) {
				l120s01f.setAvgURate(avgURate);
			} else {
				Properties pop = MessageBundleScriptCreator
						.getComponentResource(LMSS02BPanel.class);
				throw new CapMessageException(
						RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.注意, pop.getProperty("l120s02.alert12")),
						getClass());
			}
		}
		// 將自定義的欄位組合好，到資料表對應欄位裡
		l120s01f.setFxBDate(checkSpace(params.getString("fxbDateY"),
				params.getString("fxbDateM")));
		l120s01f.setFxEDate(checkSpace(params.getString("fxeDateY"),
				params.getString("fxeDateM")));
		l120s01f.setImBDate(checkSpace(params.getString("imbDateY"),
				params.getString("imbDateM")));
		l120s01f.setImEDate(checkSpace(params.getString("imeDateY"),
				params.getString("imeDateM")));
		l120s01f.setExBDate(checkSpace(params.getString("exbDateY"),
				params.getString("exbDateM")));
		l120s01f.setExEDate(checkSpace(params.getString("exeDateY"),
				params.getString("exeDateM")));
		l120s01f.setCntrBDate(checkSpace(params.getString("cntrbDateY"),
				params.getString("cntrbDateM")));
		l120s01f.setCntrEDate(checkSpace(params.getString("cntreDateY"),
				params.getString("cntreDateM")));
		return l120s01f;
	}

	/**
	 * 顯示儲存-企金後結果訊息
	 * 
	 * @param showBorrowData
	 * @param errorMsg
	 * @param needShowCust
	 * @return
	 */
	private IResult showMsg(IResult showBorrowData, StringBuilder errorMsg,	boolean needShowCust) {
		CapAjaxFormResult result = new CapAjaxFormResult();
		if (needShowCust) {
			result.set("showBorrowData", showBorrowData.getResult());
		}
		if (errorMsg.length() > 0) {
			errorMsg.insert(
					0,
					RespMsgHelper.getMainMessage(
							UtilConstants.AJAX_RSP_MSG.儲存成功)
							+ UtilConstants.Mark.HTMLBR
							+ UtilConstants.Mark.HTMLBR);
			result.set("errorMsg", errorMsg.toString());
		} else {
			// 印出儲存成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));
		}
		return result;
	}

	/**
	 * 儲存企金借款人所有相關Table
	 * 
	 * @param params
	 * @param l120m01a
	 * @param l120s01a
	 * @param l120s01b
	 * @param l120s01d
	 * @param l120s01g1
	 * @param l120s01g2
	 * @param l120s01f
	 * @throws CapMessageException
	 */
	private void toSaveBorrow(PageParameters params,
			L120M01A l120m01a, L120S01A l120s01a, L120S01B l120s01b,
			L120S01D l120s01d, L120S01G l120s01g1, L120S01G l120s01g2,
			L120S01F l120s01f) throws CapMessageException {
		try {
			if (UtilConstants.Casedoc.DocCode.一般.equals(Util.trim(l120m01a
					.getDocCode()))
					&& UtilConstants.Casedoc.DocType.企金.equals(Util
							.trim(l120m01a.getDocType()))) {

				// J-111-0535_05097_B1001 Web e-Loan企金授信配合「ESG綜效調查表
				// 」建置，於簽報書增設相對應欄位
				if (Util.isEmpty(Util.trim(l120s01b.getCmpAddr()))
						|| Util.isEmpty(Util.trim(l120s01a.getIsRejt()))
						|| Util.isEmpty(Util.trim(l120s01a.getIsEsgRejt()))) {
					// J-106-0238-001
					// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
					// || Util.isEmpty(Util.trim(l120s01a.getBlackName()))
					l120s01a.setChkYN(UtilConstants.DEFAULT.否);
				}
			}

			// J-104-0222-001 Web e-Loan授信簽報系統中的借款人列印順序調整
			if (l120s01a != null) {
				Integer focusSeq = service1201
						.addL120S01ACustShowSeqNumToMax(l120s01a);
				if (focusSeq != null) {
					l120s01a.setCustShowSeqNum(focusSeq);
				}
			}

			service1201.save(l120m01a, l120s01a, l120s01b, l120s01d, l120s01g1,
					l120s01g2, l120s01f);

			// J-104-0222-001 Web e-Loan授信簽報系統中的借款人列印順序調整
			if (l120s01a != null) {

				Properties prop = MessageBundleScriptCreator
						.getComponentResource(LMSS02BPanel.class);

				boolean setFlag = service1201
						.resetL120S01AAllCustShowSeqNum(l120s01a.getMainId());
				if (setFlag == false) {
					// l120s02.alert33=重新設定借款人基本資料顯示順序錯誤(SEQNUM)
					Map<String, String> param = new HashMap<String, String>();
					param.put("noticeMsg", prop.getProperty("l120s02.alert33"));
					throw new CapMessageException(RespMsgHelper.getMessage("EFD0015", param), getClass());
				}
			}

		} catch (Exception e) {
			logger.error("[saveBorrow] service1205.save EXCEPTION!!", e);
			Map<String, String> param = new HashMap<String, String>();
			param.put("colName", UtilConstants.Mark.HTMLSPACE);
			throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.輸入位數超過, param),
					getClass());
		}
	}

	/**
	 * <pre>
	 * 儲存-企金(全部借款人內容)
	 * 
	 * @param params PageParameters
	 * @return IResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveBorrow(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		CapAjaxFormResult showBorrowData = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);

		boolean rcdFlag = params.getBoolean("rcdFlag");
		boolean runFlag = params.getBoolean("runFlag");
		boolean finFlag = params.getBoolean("finFlag");
		boolean noSave2 = params.getBoolean("noSave2");
		boolean noSave3 = params.getBoolean("noSave3");
		boolean noSave4 = params.getBoolean("noSave4");
		String invMDscr = Util.trim(params.getString("invMDscr"));
		// J-109-0370 相關評估改版
		String prodMkt = Util.trim(params.getString("prodMkt"));
		boolean isPrint = params.getAsBoolean("isPrint", true);
		// 借款人主檔 及企金基本資料檔 的前端資料
		String formL120s01a = Util.trim(params.getString("L120S01aForm"));
		String keyMan = Util.trim(params.getString("keyMan"));
		L120S01A l120s01a = service1201.findL120s01aByOid(oid);
		String custId = Util.trim(l120s01a.getCustId());
		String dupNo = Util.trim(l120s01a.getDupNo());
		String mainId = Util.trim(l120s01a.getMainId());
		String custName = Util.trim(l120s01a.getCustName());
		String typCd = Util.trim(l120s01a.getTypCd());
		L120S01B l120s01b = service1201.findL120s01bByUniqueKey(mainId, custId,
				dupNo);
		L120S01D l120s01d = service1201.findL120s01dByUniqueKey(mainId, custId,
				dupNo);
		L120S01G l120s01g1 = service1201.findL120s01gByUniqueKey(mainId,
				custId, dupNo, UtilConstants.Casedoc.L120s01gType.營運概況分析與評估);
		L120S01G l120s01g2 = service1201.findL120s01gByUniqueKey(mainId,
				custId, dupNo, UtilConstants.Casedoc.L120s01gType.財務狀況分析與評估);
		L120S01F l120s01f = service1201
				.findL120s01fByUniqueKey(l120s01a.getMainId(),
						l120s01a.getCustId(), l120s01a.getDupNo());
		// 先抓出所有記錄主要借款人資料
		List<L120S01A> list = service1201.findL120s01aByMainId(mainId);
		L120M01A l120m01a = service1201.findL120m01aByMainId(mainId);
		String errMsgs[] = params.getStringArray("errMsg");
		StringBuilder errorMsg = new StringBuilder();
		errorMsg.setLength(0);
		// 串前端傳來的錯誤訊息
		if (errMsgs.length > 0) {
			for (String errMsg : errMsgs) {
				errorMsg.append(
						(errorMsg.length() > 0) ? "<br/>"
								: UtilConstants.Mark.SPACE).append(errMsg);
			}
		}
		
		//J-113-0075_12473_B1002 異常通報案件檢查 已重辦信用評等 及 未重辦信用評等原因
		if(UtilConstants.Casedoc.DocCode.異常通報.equals(l120m01a.getDocCode())){
			String LMS_CHECK_RERATING = Util.trim(lmsService.getSysParamDataValue("LMS_CHECK_RERATING"));
			if("Y".equals(LMS_CHECK_RERATING)){
				Properties pop = MessageBundleScriptCreator.getComponentResource(LMSS02BPanel.class);
				String reRatingFlag = Util.trim(params.getString("reRatingFlag"));
				String notReRatingRsn = Util.trim(params.getString("notReRatingRsn"));
				String otherRsnDesc = Util.trim(params.getString("otherRsnDesc"));
				if(reRatingFlag.isEmpty()){
					errorMsg.append(pop.getProperty("l120s01b.reRatingFlagAlert")).append("<br/>");
				}else if("N".equals(reRatingFlag)){
					if(notReRatingRsn.isEmpty()){
						errorMsg.append(pop.getProperty("l120s01b.notReRatingRsnAlert")).append("<br/>");
					}else if("6".equals(notReRatingRsn)){
						if(otherRsnDesc.isEmpty()){
							errorMsg.append(pop.getProperty("l120s01b.otherRsnDescAlert")).append("<br/>");
						}
					}
				}
			}
		}

		l120s01a = setL120s01a(l120s01a, formL120s01a);
		// ============================L120S01A資料到此就定位======================================
		l120s01b = setL120s01b(l120s01a, l120s01b, formL120s01a, rcdFlag,
				runFlag, finFlag, invMDscr, errorMsg, noSave2, noSave3,
				prodMkt, isPrint);
		// ============================L120S01B資料到此就定位======================================
		l120s01d = setL120s01d(l120s01a, l120s01d, formL120s01a);
		// ===================L120S01D資料到此就定位=======================
		// 企金分析與評估檔(part1) 的前端資料
		String form1L120s01g = Util.trim(params.getString("formIdDscr1"));
		l120s01g1 = (noSave2) ? null : setL120s01g(l120s01a, l120s01g1,
				UtilConstants.Casedoc.L120s01gType.營運概況分析與評估, form1L120s01g);
		// ====================L120S01G資料到此就定位======================
		// 企金分析與評估檔(part2) 的前端資料
		String form2L120s01g = Util.trim(params.getString("formIdDscr2"));
		l120s01g2 = (noSave3) ? null : setL120s01g(l120s01a, l120s01g2,
				UtilConstants.Casedoc.L120s01gType.財務狀況分析與評估, form2L120s01g);
		// ====================L120S01G資料到此就定位======================
		// 企金存放款外匯往來檔 的前端資料
		String formL120s01f = Util.trim(params.getString("L120S01fForm"));
		l120s01f = (noSave4) ? null : setL120s01f(l120s01a, l120s01f, formL120s01f, params);
		// ===================L120S01F資料到此就定位===========================
		// ===================以下開始驗證主要借款人===========================

		// J-104-0240-001 Web e-Loan授信簽報書與額度明細表增加列示借款戶所屬集團企業代號、名稱與註記。
		// 異常通報的集團改抓借款人基本資料，所以新報送的簽報書清掉原異常通報集團內容
		L130M01A l130m01a = service1201.findL130m01aByMainId(mainId);
		if (l130m01a != null) {
			if (Util.notEquals(Util.trim(l130m01a.getGrpId()), "")
					|| Util.notEquals(Util.trim(l130m01a.getGrpName()), "")) {
				l130m01a.setGrpId("");
				l130m01a.setGrpName("");
				service1201.save(l130m01a);
			}
		}

		// 主要營業項目設定控制
		// if (UtilConstants.Casedoc.DocKind.授權外.equals(Util.trim(l120m01a
		// .getDocKind()))) {
		// l120m01a.setItemOfBusi(JSONObject.fromObject(formL120s01a)
		// .getString("itemOfBusi"));
		// } else {
		// 授權內
		l120s01b.setBussItem(JSONObject.fromObject(formL120s01a).getString(
				"itemOfBusi"));
		// }

		// 當使用者按下儲存後(將簽報書刪除時間註記砍掉，代表此簽報書要保留。)
		l120m01a.setDeletedTime(null);
		MegaSSOUserDetails unit = MegaSSOSecurityContext.getUserDetails();
		// 若案號為空則進行給號
		if (Util.isEmpty(l120m01a.getCaseSeq())
				&& Util.isEmpty(l120m01a.getCaseNo())) {
			l120m01a.setCaseSeq(Integer.parseInt(number.getNumberWithMax(
					L120M01A.class, unit.getUnitNo(), null, 99999)));
			StringBuilder caseNum = new StringBuilder();
			IBranch ibranch = branchSrv.getBranch(unit.getUnitNo());
			// Properties pop = MessageBundleScriptCreator
			// .getComponentResource(LMS1205M01Page.class);
			caseNum.append(
					Util.toFullCharString(l120m01a.getCaseYear().toString()))
					.append(Util.trim(ibranch.getNameABBR()))
					.append(UtilConstants.Field.兆)
					.append(UtilConstants.Field.授字第)
					.append(Util.toFullCharString(Util.addZeroWithValue(
							Util.trim(l120m01a.getCaseSeq()), 5)))
					.append(UtilConstants.Field.號);
			l120m01a.setCaseNo(caseNum.toString());
		}

		String needToAddOid = UtilConstants.Mark.SPACE;
		int count = 0;
		// 取得使用者點選的借款人主表Oid
		String oidOid = l120s01a.getOid();
		for (L120S01A model : list) {
			if (UtilConstants.DEFAULT.是.equals(Util.trim(model.getKeyMan()))) {
				// 找出需要被覆蓋的主要借款人資料
				if (!(Util.trim(model.getOid()).equals(oidOid))) {
					count++;
					needToAddOid = model.getOid();
				}
			}
		}

		// 當是主要借款人且為第一次新增時
		if (UtilConstants.DEFAULT.是.equals(keyMan) && count == 0) {
			result.set("keyMan", UtilConstants.DEFAULT.是);
			l120m01a.setCustId(custId);
			l120m01a.setDupNo(dupNo);
			l120m01a.setCustName(Util.truncateString(custName, 120));
			l120m01a.setTypCd(typCd);
			showBorrowData.set("custId", custId);
			showBorrowData.set("dupNo", dupNo);
			showBorrowData.set("custName", Util.truncateString(custName, 120));
			showBorrowData.set("typCd", getMessage("typCd." + typCd));
			// showBorrowData.set("typCd", TypCdEnum.getEnum(typCd).name());
			if (errorMsg.length() > 0) {
				l120s01a.setChkYN(UtilConstants.DEFAULT.否);
			}
			// 進行儲存
			toSaveBorrow(params, l120m01a, l120s01a, l120s01b, l120s01d, l120s01g1, l120s01g2, l120s01f);

			// 顯示儲存-企金後結果訊息
			result.add(showMsg(showBorrowData, errorMsg, true));
		}
		// 當新增主要借款人且已有主要借款人時
		else if (UtilConstants.DEFAULT.是.equals(keyMan) && count == 1) {
			result.set("keyMan", UtilConstants.DEFAULT.是);
			// 將使用者勾選新主要借款人的Oid傳到前端以做進一步處理
			result.set("haveKeyManOid", l120s01a.getOid());
			// 將授信簽報書OID傳到前端以新增主要借款人
			result.set("haveKeyManDocNo", l120m01a.getOid());
			// 將被覆蓋(原主要借款人)的Oid傳到前端以做進一步處理
			result.set("needtoAddOid", needToAddOid);
			// 傳到前端判定為已存在主要借款人
			result.set("ExistKeyMan", true);
			L120S01A oldL120s01a = service1201.findL120s01aByOid(needToAddOid);
			// 原主要借款人名稱
			result.set("oldBorrower", Util.trim(oldL120s01a.getCustName()));
			if (errorMsg.length() > 0) {
				l120s01a.setChkYN(UtilConstants.DEFAULT.否);
			}
			// 先儲存進去之後 L120S01A Model再做處理
			toSaveBorrow(params, l120m01a, l120s01a, l120s01b, l120s01d, l120s01g1, l120s01g2, l120s01f);

			if (errorMsg.length() > 0) {
				result.set("errorMsg", errorMsg.toString());
			}
			return result;
		}
		// 使用者將原主要借款人取消時
		else if (UtilConstants.DEFAULT.否.equals(keyMan) && count == 0) {
			l120s01a.setKeyMan(UtilConstants.DEFAULT.否);
			result.set("keyMan", UtilConstants.DEFAULT.否);
			l120s01a.setChkYN(UtilConstants.DEFAULT.否);
			l120m01a.setCustId(UtilConstants.Mark.SPACE);
			l120m01a.setDupNo(UtilConstants.Mark.SPACE);
			l120m01a.setCustName(UtilConstants.Mark.SPACE);
			l120m01a.setTypCd(UtilConstants.Mark.SPACE);
			showBorrowData.set("custId", UtilConstants.Mark.SPACE);
			showBorrowData.set("dupNo", UtilConstants.Mark.SPACE);
			showBorrowData.set("custName", UtilConstants.Mark.SPACE);
			showBorrowData.set("typCd", UtilConstants.Mark.SPACE);
			result.set("ExistKeyMan", false);
			if (errorMsg.length() > 0) {
				l120s01a.setChkYN(UtilConstants.DEFAULT.否);
			}

			// 進行儲存
			toSaveBorrow(params, l120m01a, l120s01a, l120s01b, l120s01d, l120s01g1, l120s01g2, l120s01f);

			// 顯示儲存-企金後結果訊息
			result.add(showMsg(showBorrowData, errorMsg, true));
		} else {
			// 當使用者新增不是主要借款人資料時...
			l120s01a.setKeyMan(UtilConstants.DEFAULT.否);
			result.set("keyMan", UtilConstants.DEFAULT.否);
			if (errorMsg.length() > 0) {
				l120s01a.setChkYN(UtilConstants.DEFAULT.否);
			}
			// 進行儲存
			toSaveBorrow(params, l120m01a, l120s01a, l120s01b, l120s01d, l120s01g1, l120s01g2, l120s01f);

			// 顯示儲存-企金後結果訊息
			result.add(showMsg(showBorrowData, errorMsg, false));
		}

		return result;
	}

	/**
	 * 根據項目決定財務狀況欄位是否需要加%符號
	 * 
	 * @param finItem
	 *            項目
	 * @return true->需要加,false->不需加
	 */
	public boolean needPercent(String finItem) {
		String noPercent[] = UtilConstants.Casedoc.財務狀況不需加百分比欄位;
		for (String str : noPercent) {
			if (str.equals(finItem)) {
				return false;
			}
		}
		return true;
	}

	/**
	 * 營運概況修改欄位儲存
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult editBorrowPage02(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String thisOid = Util.trim(params.getString("thisOid"));
		L120S01A l120s01a = service1201.findL120s01aByOid(thisOid);
		String editFPanel2 = Util.trim(params.getString("editFPanel2"));
		if (l120s01a != null) {
			String mainId = Util.trim(l120s01a.getMainId());
			String custId = Util.trim(l120s01a.getCustId());
			String dupNo = Util.trim(l120s01a.getDupNo());
			L120S01B l120s01b = service1201.findL120s01bByUniqueKey(mainId,
					custId, dupNo);
			CapAjaxFormResult L120S01gForm_1 = new CapAjaxFormResult();
			if (Util.isNotEmpty(editFPanel2)) {

				String gaapFlag = CapString.trimNull(l120s01b.getGaapFlag());
				String tradeType = CapString.trimNull(l120s01b.getTradeType());
				gaapFlag = "".equals(gaapFlag) ? " " : gaapFlag;
				tradeType = "".equals(tradeType) ? " " : tradeType;

				String[] fssItemCode = service1201.getFssItemCode(
						gaapFlag.charAt(0), tradeType.charAt(0));
				String titleCode_kind_1 = fssItemCode[0];
				String[] finGroups = titleCode_kind_1.split("\\|");

				List<L120S01E> listL120s01e = service1201
						.findListL120s01eByUniqueKey(
								UtilConstants.Casedoc.L120s01eKind.營運概況,
								mainId, custId, dupNo); // 抓出所有符合條件的資料
				List<L120S01E> listToAdd = new ArrayList<L120S01E>();
				if (!listL120s01e.isEmpty()) {
					service1201.deleteListL120s01e(listL120s01e);
				}
				JSONObject json = JSONObject.fromObject(editFPanel2);
				if (l120s01b != null) {
					l120s01b.setRunCurr(Util.trim(json.getString("_runCurr")));
					l120s01b.setRunUnit(LMSUtil.toBigDecimal(Util.trim(json
							.getString("_runUnit"))));
				}
				String[] flag = new String[] { "D", "C", "B", "A" };
				for (int k = 0; k < flag.length; k++) {
					if (json.containsKey("_finYear_" + flag[k])
							&& json.getString("_finYear_" + flag[k]).length() == 10) {
						for (int i = 0; i < finGroups.length; i++) {

							String finGroup = finGroups[i];
							String[] finItems = finGroup.split("\\^");
							L120S01E model = new L120S01E();
							model.setMainId(mainId);
							model.setCustId(custId);
							model.setDupNo(dupNo);
							model.setFinKind(UtilConstants.Casedoc.L120s01eKind.營運概況);
							model.setFinYear(Util.parseDate(json
									.getString("_finYear_" + flag[k])));
							model.setFinItem("");
							for (int j = 0; j < finItems.length; j++) {

								if (j == 0) {
									String itemValue = "_finAmt" + flag[k]
											+ (i + 1);
									Long value = NumberUtils.isNumber(json
											.getString(itemValue)) ? new Long(
											json.getString(itemValue)) : null;
									model.setFinAmtCode(finItems[j]);
									model.setFinAmt(value);
									L120S01gForm_1.set("finAmt" + flag[k]
											+ (i + 1), value == null ? "N.A."
											: String.valueOf(value));

								} else if (j == 1) {
									String itemValue = "_finRatio" + flag[k]
											+ (i + 1);
									BigDecimal value = NumberUtils
											.isNumber(json.getString(itemValue)) ? new BigDecimal(
											json.getString(itemValue)) : null;
									model.setFinRatioCode(finItems[j]);
									model.setFinRatio(value);
									L120S01gForm_1.set("finRatio" + flag[k]
											+ (i + 1), value == null ? "N.A."
											: String.valueOf(value));

								}

							}
							listToAdd.add(model);
						}

					}
				}

				service1201.saveListL120s01e(listToAdd, l120s01b);

				JSONObject l120s01eKind1Data = service1201
						.getL120s01eKind1Data(mainId, custId, dupNo,
								gaapFlag.charAt(0), tradeType.charAt(0));
				L120S01gForm_1.putAll(new CapAjaxFormResult(l120s01eKind1Data));

				// 印出執行成功訊息!
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
						RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
				L120S01gForm_1.set("runCurr", Util.trim(l120s01b.getRunCurr()));
				L120S01gForm_1.set("runUnit", Util.trim(l120s01b.getRunUnit()));
				result.set("L120S01gForm_1", L120S01gForm_1);
			}
		}
		return result;
	}

	/**
	 * 財務狀況修改欄位儲存
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult editBorrowPage03(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String thisOid = Util.trim(params.getString("thisOid"));
		L120S01A l120s01a = service1201.findL120s01aByOid(thisOid);
		String editFPanel3 = Util.trim(params.getString("finRatioData"));
		if (l120s01a != null) {
			String[] flag = new String[] { "C", "B", "A" };
			String mainId = Util.trim(l120s01a.getMainId());
			String custId = Util.trim(l120s01a.getCustId());
			String dupNo = Util.trim(l120s01a.getDupNo());

			L120S01B l120s01b = service1201.findL120s01bByUniqueKey(mainId,
					custId, dupNo);

			String gaapFlag = CapString.trimNull(l120s01b.getGaapFlag());
			String tradeType = CapString.trimNull(l120s01b.getTradeType());
			gaapFlag = "".equals(gaapFlag) ? " " : gaapFlag;
			tradeType = "".equals(tradeType) ? " " : tradeType;

			String[] fssItemCode = service1201.getFssItemCode(
					gaapFlag.charAt(0), tradeType.charAt(0));
			String titleCode_kind_2 = fssItemCode[1];
			String[] finGroups = titleCode_kind_2.split("\\|");

			CapAjaxFormResult L120S01gForm_2 = new CapAjaxFormResult();
			if (Util.isNotEmpty(editFPanel3)) {
				List<L120S01E> listL120s01e = service1201
						.findListL120s01eByUniqueKey(
								UtilConstants.Casedoc.L120s01eKind.財務狀況,
								mainId, custId, dupNo); // 抓出所有符合條件的資料
				List<L120S01E> listToAdd = new ArrayList<L120S01E>();
				if (!listL120s01e.isEmpty()) {
					service1201.deleteListL120s01e(listL120s01e);
				}
				// 微型企業 finGroups 加上 權益總額 / 銀行借款
				String miniFlag = "";
				L120M01A l120m01a = service1201.findL120m01aByMainId(mainId);
				if (l120m01a != null) {
					miniFlag = Util.trim(l120m01a.getMiniFlag());
					if (Util.equals("Y", miniFlag)
							&& !lmsService.hidePanelbyCaseType_004(l120m01a)) {
						List<String> listFinItem = new ArrayList<String>(
								Arrays.asList(finGroups));

						if (Util.equals("0", gaapFlag.charAt(0))) { // GAAP
							if (!listFinItem.contains("a30000000")) { // 權益總額
								listFinItem.add("a30000000");
							}
							if (!listFinItem.contains("a20301000c")) { // 銀行借款
								listFinItem.add("a20301000c");
							}
						} else if (Util.equals("1", gaapFlag.charAt(0))) { // IFRS
							if (!listFinItem.contains("a30000")) { // 權益總額
								listFinItem.add("a30000");
							}
							if (!listFinItem.contains("a21010c")) { // 銀行借款
								listFinItem.add("a21010c");
							}
						} else if (Util.equals("2", gaapFlag.charAt(0))) { // EAS
							// J-109-0279_05097_B1001
							// e-Loan企金簽報書配合徵信IFRS改版與新增EAS會計準則相關修改
							if (!listFinItem.contains("a30000")) { // 權益總額
								listFinItem.add("a30000");
							}
							if (!listFinItem.contains("a21010c")) { // 銀行借款
								listFinItem.add("a21010c");
							}
						}
						finGroups = listFinItem.toArray(new String[0]);
					}
				}
				JSONObject json = JSONObject.fromObject(editFPanel3);
				for (int k = 0; k < flag.length; k++) {
					String finRatioYear = "_finRatioYear_" + flag[k];
					if (json.containsKey(finRatioYear)
							&& json.getString(finRatioYear).length() == 10) {
						for (int i = 0; i < finGroups.length; i++) {
							String key = finGroups[i];// r11,r12
							String finRatioKey = "_finRatio_" + flag[k] + "_"
									+ key;
							if (!json.containsKey(finRatioKey)) {
								continue;
							}
							L120S01E model = new L120S01E();
							model.setMainId(mainId);
							model.setCustId(custId);
							model.setDupNo(dupNo);
							model.setFinKind(UtilConstants.Casedoc.L120s01eKind.財務狀況);
							model.setFinYear(Util.parseDate(json
									.getString(finRatioYear)));
							String value = json.optString(finRatioKey);
							BigDecimal ratioValue = NumberUtils.isNumber(value) ? new BigDecimal(
									value).setScale(2, RoundingMode.DOWN)
									: null;
							model.setFinItem("");
							// 微型企業 finGroups 加上 權益總額 / 銀行借款
							// 值要塞在FINAMT，FINAMTCODE
							if (Util.equals(key, "a30000000")
									|| Util.equals(key, "a20301000c")
									|| Util.equals(key, "a30000")
									|| Util.equals(key, "a21010c")) {
								model.setFinAmt(NumberUtils.isNumber(value) ? Util
										.parseLong(value) : null);
								model.setFinAmtCode(key);
							} else {
								model.setFinRatio(ratioValue);
								model.setFinRatioCode(key);
							}
							model.setUpdater(user.getUserId());
							model.setUpdateTime(new Date());
							listToAdd.add(model);
						}
					}
				}
				/**
				 * String keys[] = Util.getMapKey(json); Map<String, String> map
				 * = new HashMap<String, String>(); String tempFinRatio = null;
				 * for (String key : keys) { if (key.contains("finYear")) { //
				 * 年份 if (LMSUtil.checkSubStr(key, 2)) {
				 * L120S01gForm_2.set(key.substring(2),
				 * Util.trim(json.getString(key))); } if
				 * (LMSUtil.checkSubStr(key, key.length() - 1)) {
				 * map.put("finYear_" + key.substring(key.length() - 1),
				 * Util.trim(json.getString(key))); } } else if
				 * (key.contains("Answer")) { // 比率 tempFinRatio =
				 * Util.trim(json.getString(key)); if (LMSUtil.checkSubStr(key,
				 * 2) && LMSUtil.checkSubStr(key, key.length() - 2)) {
				 * L120S01gForm_2 .set(key.substring(2), tempFinRatio +
				 * (needPercent(Util.trim(key.substring(key .length() - 2))) ?
				 * "%" : UtilConstants.Mark.SPACE)); map.put(key.substring(2),
				 * tempFinRatio); } } } if (!map.isEmpty()) { String mapKeys[] =
				 * Util.getMapKey(map); for (String mapKey : mapKeys) { if
				 * (mapKey.contains("Answer")) { // 比率 if (LMSUtil
				 * .checkSubStr(mapKey, mapKey.length() - 2) &&
				 * LMSUtil.checkSubStr(mapKey, mapKey.length() - 4,
				 * mapKey.length() - 3)) { String finItem = Util.trim(mapKey
				 * .substring(mapKey.length() - 2)); String yearKind =
				 * Util.trim(mapKey.substring( mapKey.length() - 4,
				 * mapKey.length() - 3)); String finYear = map.get("finYear_" +
				 * yearKind); String idKey = yearKind + "_" + finItem; if
				 * (Util.isNotEmpty(finYear) && Util.isNotEmpty(finItem)) {
				 * L120S01E model = new L120S01E(); model.setMainId(mainId);
				 * model.setCustId(custId); model.setDupNo(dupNo);
				 * model.setFinKind(UtilConstants.Casedoc.L120s01eKind.財務狀況);
				 * model.setFinYear(Util.parseDate(finYear));
				 * model.setFinItem(("N.A.").equals(finItem) ? null : finItem);
				 * model.setFinRatio(("N.A.").equals(map .get("Answer_" +
				 * idKey)) ? null : LMSUtil.toBigDecimal(map .get("Answer_" +
				 * idKey)));
				 * 
				 * listToAdd.add(model); } } } } }
				 */
				service1201.saveListL120s01e(listToAdd);

				JSONObject l120s01eKind2Data = service1201
						.getL120s01eKind2Data(mainId, custId, dupNo,
								gaapFlag.charAt(0), tradeType.charAt(0));
				L120S01gForm_2.putAll(new CapAjaxFormResult(l120s01eKind2Data));

				// 印出執行成功訊息!
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
						RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
				result.set("L120S01gForm_2", L120S01gForm_2);
			}
		}
		return result;
	}

	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult getBorrowDataFromCES(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String cesMainId = params.getString("cesMainId", "");
		String mainId = params.getString(EloanConstants.MAIN_ID);
		if (!StringUtils.isEmpty(cesMainId)) {
			// 徵信報告-一般概況
			Map<String, Object> overview = eloanDbBaseService
					.getC140M01A_Overview(cesMainId);
			JSONObject overviewJson = JSONObject.fromObject(MapUtils.getString(
					overview, "JSONOB", "{}"));

			result.set("chairman", overviewJson.optString("cpPeople1", ""));
			result.set("chairmanId", overviewJson.optString("cpPeoid1", ""));
			result.set("gManager", overviewJson.optString("cpPeople", ""));
			result.set("cmpAddr", overviewJson.optString("regAddr", ""));
			result.set("ntCode", overviewJson.optString("regPlace", ""));
			result.set("cmpAddr", overviewJson.optString("cpAddr", ""));
			result.set("factoryAddr", overviewJson.optString("facAddr", ""));
			result.set("stockStatus", overviewJson.optString("cpStock", ""));
			result.set("stockDate", overviewJson.optString("emDate", ""));

			// 徵信報告-資本變動情況
			Map<String, Object> changeInShareCapital = eloanDbBaseService
					.getC140M01A_ChangeInShareCapital(cesMainId);
			JSONObject changeInShareCapitalJson = JSONObject
					.fromObject(MapUtils.getString(changeInShareCapital,
							"JSONOB", "{}"));
			if (changeInShareCapitalJson != null
					&& changeInShareCapitalJson.size() > 0) {
				for (int i = 5; i >= 1; i--) {
					String capD = changeInShareCapitalJson.optString(
							"capD" + i, "");
					if (!StringUtils.isEmpty(capD)) {
						result.set("rgtAmt", changeInShareCapitalJson
								.optString("capR" + i, ""));
						result.set("cptlAmt", changeInShareCapitalJson
								.optString("capC" + i, ""));
						break;
					}
				}

				result.set("rgtCurr",
						changeInShareCapitalJson.optString("curr"));
				result.set("cptlCurr",
						changeInShareCapitalJson.optString("curr"));
				result.set("rgtUnit",
						changeInShareCapitalJson.optString("unit"));
				result.set("cptlUnit",
						changeInShareCapitalJson.optString("unit"));
			}

			Map<String, Object> history = eloanDbBaseService
					.getC140M01A_History(cesMainId);

			result.set("stockHolder", MapUtils.getString(history, "VAL", ""));

			Map<String, Object> estaDate = eloanDbBaseService
					.getC140C120M01A_EstaDate(cesMainId);

			result.set("estDate", MapUtils.getString(estaDate, "ESTADATE", ""));

			List<Map<String, Object>> itemOfBusi = eloanDbBaseService
					.findBusi(cesMainId);
			if (itemOfBusi != null && !itemOfBusi.isEmpty()) {
				result.set("itemOfBusi",
						MapUtils.getString(itemOfBusi.get(0), "VAL", ""));
			}

			Map<String, Object> ch6_LLand_inv = eloanDbBaseService
					.getC140M01A_Ch6_LLand_inv(cesMainId);
			result.set("invMFlag", MapUtils.getString(ch6_LLand_inv, "inv", ""));

			// 資料來源為徵信報告的CKEDITOR,要特別處理，才能就圖檔複製
			String invMDscr = docFileService.copyCKEditorImageFile(Util
					.trim(MapUtils.getString(ch6_LLand_inv, "invMDscr", "")),
					mainId, "CES");
			result.set("invMDscr", invMDscr);

			// 不符合中小企業定義(A01~A03)及貸款期間(A02:3年、A03:5年)，則系統(eLoan 與
			// aLoan)不能讓該授信案成為經濟部紓困案件(A01~A03)
			// 徵信報告-員工人數
			// Map<String, Object> totEmp = eloanDbBaseService
			// .getC140M01A_TotEmp(cesMainId);
			// JSONObject totEmpJson = JSONObject.fromObject(MapUtils.getString(
			// totEmp, "JSONOB", "{}"));
			// if (totEmpJson != null && totEmpJson.size() > 0) {
			// result.set("totEmp", totEmpJson.optString("totEmp"));
			// }

		}

		// J-110-0445_05097_B1001 於簽報書借款人基本資料之信用評等下新增欄位
		// Map<String, Object> ces120Map = this.getLastCes120Data(cesMainId,
		// params, parent);
		// CapAjaxFormResult mowGradeData = new CapAjaxFormResult();
		// mowGradeData.putAll(ces120Map);
		// result.set("mowGradeData", mowGradeData);

		return result;
	}

	/**
	 * 取得行業對象別與客戶類別並設值到前端
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getBusCdAndCustClass(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String htmlFormName = Util.trim(params.getString("htmlFormName"));
		String errorMsg = "";
		boolean noMsg = params.getBoolean("noMsg");

		JSONObject jsonData = new JSONObject();
		jsonData = getCustBusCDAndClass(custId, dupNo);

		CapAjaxFormResult formL120s01a = new CapAjaxFormResult();

		result.set("busCode", jsonData.getString("busCode"));
		result.set("bussKind", jsonData.getString("bussKind"));
		result.set("ecoNm", jsonData.getString("ecoNm"));
		result.set("ecoNm07A", jsonData.getString("ecoNm07A"));
		result.set("custClass", jsonData.getString("custClass"));
		result.set("displayBusCd", jsonData.getString("displayBusCd"));

		if (Util.notEquals(jsonData.getString("busCode"), "")) {
			if (!noMsg) {
				// 印出執行成功訊息!
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
						RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
			}
		} else {
			if (!noMsg) {
				throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.查無資料), getClass());
			}
			errorMsg = "查無行業對象別資訊！" + "<br/>";
		}
		result.set("errorMsg", errorMsg);
		return result;
	}

	/**
	 * 取得MIS客戶行業別與客戶類別
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重複序號
	 * @return
	 */
	public JSONObject getCustBusCDAndClass(String custId, String dupNo) {

		JSONObject jsonData = new JSONObject();
		Map<String, Object> map = misDbService.findCustBussDataByIdAndDup(
				custId, dupNo);

		String busCode = "";
		String cltype = "";
		String bussKind = "";
		String ecoNm = "";
		String ecoNm07A = "";
		String custClass = "";
		String displayBusCd = "";

		// J-111-0572_05097_B1001 Web e-Loan企金授信配合e-Loan LGD擔保品分配規則修改
		if (map != null && !map.isEmpty()) {

			busCode = Util.trim(map.get("BUSCD"));
			cltype = Util.trim(map.get("CLTYPE"));
			bussKind = Util.trim(map.get("BUSSKIND"));
			ecoNm = Util.trim(map.get("ECONM"));
			ecoNm07A = Util.trim(map.get("ecoNM07A"));

			if (Util.notEquals(busCode, "")) {
				custClass = getCustClass(busCode, cltype);
				displayBusCd = getDisplayBusCd(busCode, ecoNm, bussKind,
						ecoNm07A);
			}
		}

		jsonData.put("busCode", busCode);
		jsonData.put("bussKind", bussKind);
		jsonData.put("ecoNm", ecoNm);
		jsonData.put("ecoNm07A", ecoNm07A);
		jsonData.put("custClass", custClass);
		jsonData.put("displayBusCd", displayBusCd);

		return jsonData;
	}

	/**
	 * 取得客戶類別--邏輯同a-Loan新彙集檔
	 * 
	 * @param buscd
	 *            行業對象別代碼0024
	 * @param cltype
	 *            企業規模0024
	 * @return
	 */
	public String getCustClass(String buscd, String cltype) {

		String custClass = "";

		if (Util.equals(Util.getLeftStr(buscd, 2), "02")) {
			custClass = "5";
		} else if (Util.equals(Util.getLeftStr(buscd, 2), "03")) {
			custClass = "6";
		} else if (Util.equals(Util.getLeftStr(buscd, 2), "05")) {
			custClass = "7";
		} else if (Util.equals(Util.getLeftStr(buscd, 2), "06")) {
			custClass = "2";
		} else {
			if (Util.equals(cltype, "1")) {
				custClass = "1";
			} else if (Util.equals(cltype, "2")) {
				custClass = "8";
			} else {
				custClass = "4";
			}
		}

		if (Util.equals(buscd, "130300")) {
			custClass = "2";
		}

		return custClass;

	}

	/**
	 * 取得畫面顯示的行業對象別-次產業別
	 * 
	 * @param bussCode
	 *            行業對象別代碼
	 * @param ecoNm
	 *            行業對象別名稱
	 * @param bussKind
	 *            次產業別代碼
	 * @param ecoNm07A
	 *            次產業別名稱
	 * @return
	 */
	public String getDisplayBusCd(String bussCode, String ecoNm, String bussKind, String ecoNm07A) {

		String displayBusCd1 = Util.trim(bussCode);
		String displayBusCd2 = Util.trim(ecoNm);

		if (Util.notEquals(bussKind, "")) {

			displayBusCd1 = displayBusCd1 + "-" + Util.trim(bussKind);
			displayBusCd2 = displayBusCd2 + "-" + Util.trim(ecoNm07A);
		}

		if (Util.notEquals(displayBusCd2, "")) {
			return displayBusCd1 + "：" + displayBusCd2;
		} else {
			return displayBusCd1;
		}

	}

	/**
	 * 取得 申貸戶 異常通報戶紀錄 J-105-0179-001 Web e-Loan企金授信建立「往來異常通報戶」紀錄查詢及於簽報書上顯示查詢結果功能
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getAbnormal(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		boolean noMsg = params.getBoolean("noMsg");
		L120S01A model = service1201.findL120s01aByUniqueKey(mainId, custId,
				dupNo);
		// 查詢銀行法有無利害關係人
		List<Map<String, Object>> listMap = misDbService
				.findLnfe0854LastStatusByCustId(custId, dupNo);

		if (!listMap.isEmpty()) {
			// 有婉卻紀錄
			for (Map<String, Object> map : listMap) {
				// 是否本行有婉卻紀錄
				model.setIsAbnormal(UtilConstants.DEFAULT.是);
				// 查詢日期
				model.setAbnormalReadDate(new Date());
				// 通報分行
				model.setAbnormalBrNo(Util.trim(map.get("LNFE0854_MDBRNO")));
				// 通報時間
				model.setAbnormalDate(CapDate.parseDate(Util.trim(map
						.get("LNFE0854_SDATE"))));
				// 目前異常通報狀態
				model.setAbnormalStatus(Util.equals(
						Util.trim(map.get("LNFE0854_CLOSEFG")), "Y") ? UtilConstants.Casedoc.abnormalStatus.已解除
						: UtilConstants.Casedoc.abnormalStatus.未解除);
				// 異常通報簽報書MAINID
				model.setAbnormalMainId(Util.trim(map.get("LNFE0854_UNID")));

				service1201.save(model);
				String needCols[] = new String[] { "isAbnormal",
						"abnormalReadDate", "abnormalReadDate", "abnormalBrNo",
						"abnormalDate", "abnormalStatus", "abnormalMainId" };
				CapAjaxFormResult formL120s01a = DataParse.toResult(model,
						DataParse.Need, needCols);
				SimpleDateFormat formatter = new SimpleDateFormat(
						UtilConstants.DateFormat.YYYY_MM_DD);
				formL120s01a.set("abnormalBrNo", getRejtBrName(Util
						.trim(formL120s01a.get("abnormalBrNo"))));

				formL120s01a.set(
						"abnormalDate",
						Util.isNotEmpty(model.getAbnormalDate()) ? formatter
								.format(model.getAbnormalDate())
								: UtilConstants.Mark.SPACE);
				result.set("L120S01aForm", formL120s01a);
				break;
			}
		}
		if (result.containsKey("L120S01aForm")) {
			if (!noMsg) {
				// 印出執行成功訊息!
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
						RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
			}
		} else {
			// 是否本行有婉卻紀錄
			model.setIsAbnormal(UtilConstants.DEFAULT.否);
			// 查詢日期
			model.setAbnormalReadDate(new Date());
			// 通報分行
			model.setAbnormalBrNo(null);
			// 通報時間
			model.setAbnormalDate(null);
			// 目前異常通報狀態
			model.setAbnormalStatus(null);
			// 異常通報簽報書MAINID
			model.setAbnormalMainId(null);

			service1201.save(model);
			CapAjaxFormResult formL120s01a = new CapAjaxFormResult();
			formL120s01a.set("isAbnormal", Util.trim(model.getIsAbnormal()));
			formL120s01a.set("abnormalReadDate", CapDate.formatDate(
					model.getAbnormalReadDate(),
					UtilConstants.DateFormat.YYYY_MM_DD));

			formL120s01a.set("abnormalBrNo", UtilConstants.Mark.SPACE);
			formL120s01a.set("abnormalDate", UtilConstants.Mark.SPACE);
			formL120s01a.set("abnormalStatus", UtilConstants.Mark.SPACE);

			result.set("L120S01aForm", formL120s01a);
			// throw new CapMessageException(RespMsgHelper.getMessage(parent,
			// UtilConstants.AJAX_RSP_MSG.查無資料), getClass());
		}
		return result;
	}

	/**
	 * 取得 申貸戶 異常通報戶紀錄 J-105-0179-001 Web e-Loan企金授信建立「往來異常通報戶」紀錄查詢及於簽報書上顯示查詢結果功能
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getAbnormal1(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String chairmanId = Util.trim(params.getString("chairmanId"));
		String chairmanDupNo = Util.trim(params.getString("chairmanDupNo"));
		boolean noMsg = params.getBoolean("noMsg");
		boolean chairmanVisible = params.getAsBoolean("chairmanVisible", true);
		boolean isPassed = false;
		if (Util.isEmpty(chairmanId)) {
			chairmanId = "";
			chairmanDupNo = "";
		} else {
			if (Util.isEmpty(chairmanDupNo)) {
				chairmanDupNo = "0";
			}
		}
		isPassed = true;
		if (chairmanVisible == false) {
			// 格式為[其他 ; 陳復案/陳述案], 企金戶在UI上的負責人欄位為hidden
			chairmanId = "";
			chairmanDupNo = "";
		}
		// ---
		L120S01A model = service1201.findL120s01aByUniqueKey(mainId, custId,
				dupNo);
		List<Map<String, Object>> listMap = new ArrayList<Map<String, Object>>();
		if (Util.isNotEmpty(chairmanId) && Util.isNotEmpty(chairmanDupNo)) {
			listMap = misDbService.findLnfe0854LastStatusByCustId(chairmanId,
					chairmanDupNo);

			if (!listMap.isEmpty()) {
				// 有婉卻紀錄
				for (Map<String, Object> map : listMap) {
					// 是否本行有婉卻紀錄
					model.setIsAbnormal1(UtilConstants.DEFAULT.是);
					// 查詢日期
					model.setAbnormalReadDate1(new Date());
					// 通報分行
					model.setAbnormalBrNo1(Util.trim(map.get("LNFE0854_MDBRNO")));
					// 通報時間
					model.setAbnormalDate1(CapDate.parseDate(Util.trim(map
							.get("LNFE0854_SDATE"))));
					// 目前異常通報狀態
					model.setAbnormalStatus1(Util.equals(
							Util.trim(map.get("LNFE0854_CLOSEFG")), "Y") ? UtilConstants.Casedoc.abnormalStatus.已解除
							: UtilConstants.Casedoc.abnormalStatus.未解除);

					if (Util.equals(chairmanId, model.getAbnormalChairmanId(),
							false)
							&& Util.equals(chairmanDupNo,
									model.getAbnormalChairmanDupNo(), false)) {

					} else {

						model.setAbnormalChairmanId(chairmanId);
						model.setAbnormalChairmanDupNo(chairmanDupNo);
					}
					// 異常通報簽報書MAINID
					model.setAbnormalMainId1(Util.trim(map.get("LNFE0854_UNID")));

					service1201.save(model);
					String needCols[] = new String[] { "isAbnormal1",
							"abnormalReadDate1", "abnormalReadDate1",
							"abnormalBrNo1", "abnormalDate1",
							"abnormalStatus1", "abnormalChairmanId",
							"abnormalChairmanDupNo", "abnormalMainId1" };
					CapAjaxFormResult formL120s01a = DataParse.toResult(model,
							DataParse.Need, needCols);
					SimpleDateFormat formatter = new SimpleDateFormat(
							UtilConstants.DateFormat.YYYY_MM_DD);
					formL120s01a.set("abnormalBrNo1", getRejtBrName(Util
							.trim(formL120s01a.get("abnormalBrNo1"))));

					formL120s01a
							.set("abnormalDate1",
									Util.isNotEmpty(model.getAbnormalDate1()) ? formatter
											.format(model.getAbnormalDate1())
											: UtilConstants.Mark.SPACE);
					result.set("L120S01aForm", formL120s01a);
					break;
				}
			}
		}

		if (result.containsKey("L120S01aForm")) {
			if (!noMsg) {
				// 印出執行成功訊息!
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
						RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
			}
		} else {
			if (Util.isNotEmpty(chairmanId) && Util.isNotEmpty(chairmanDupNo)) {
				// 是否本行有婉卻紀錄
				model.setIsAbnormal1(UtilConstants.DEFAULT.否);
				// 查詢日期
				model.setAbnormalReadDate1(new Date());

				model.setAbnormalChairmanId(chairmanId);
				model.setAbnormalChairmanDupNo(chairmanDupNo);

				// 通報分行
				model.setAbnormalBrNo1(null);
				// 通報時間
				model.setAbnormalDate1(null);
				// 目前異常通報狀態
				model.setAbnormalStatus1(null);
				// 異常通報簽報書MAINID
				model.setAbnormalMainId1(null);

				// if (Util.equals(chairmanId, model.getAbnormalChairmanId(),
				// false)
				// && Util.equals(chairmanDupNo,
				// model.getAbnormalChairmanDupNo(), false)) {
				//
				// } else {
				//
				//
				// }
				service1201.save(model);
				CapAjaxFormResult formL120s01a = new CapAjaxFormResult();
				formL120s01a.set("isAbnormal1",
						Util.trim(model.getIsAbnormal1()));
				formL120s01a.set("abnormalReadDate1", CapDate.formatDate(
						model.getAbnormalReadDate1(),
						UtilConstants.DateFormat.YYYY_MM_DD));
				formL120s01a.set("abnormalChairmanId",
						Util.trim(model.getAbnormalChairmanId()));
				formL120s01a.set("abnormalChairmanDupNo",
						Util.trim(model.getAbnormalChairmanDupNo()));
				result.set("L120S01aForm", formL120s01a);
			} else {
				model.setIsAbnormal1(null);
				model.setAbnormalChairmanId(chairmanId);
				model.setAbnormalChairmanDupNo(chairmanDupNo);

				model.setAbnormalReadDate1(null);
				model.setAbnormalBrNo1(null);
				model.setAbnormalDate1(null);
				model.setAbnormalStatus1(null);

				// 異常通報簽報書MAINID
				model.setAbnormalMainId1(null);

				service1201.save(model);
				CapAjaxFormResult formL120s01a = new CapAjaxFormResult();
				formL120s01a.set("isAbnormal1",
						Util.trim(model.getIsAbnormal1()));
				formL120s01a.set("abnormalReadDate1", "");
				formL120s01a.set("abnormalChairmanId",
						Util.trim(model.getAbnormalChairmanId()));
				formL120s01a.set("abnormalChairmanDupNo",
						Util.trim(model.getAbnormalChairmanDupNo()));
				result.set("L120S01aForm", formL120s01a);
			}
		}
		result.set("isPassed", isPassed);
		return result;
	}

	/**
	 * 取得 申貸戶 異常通報戶紀錄 J-105-0179-001 Web e-Loan企金授信建立「往來異常通報戶」紀錄查詢及於簽報書上顯示查詢結果功能
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult openDocAbnormal(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString("abnormalMainId"));
		String custId = Util.trim(params.getString("openCustId"));
		String dupNo = Util.trim(params.getString("openDupNo"));
		boolean noMsg = params.getBoolean("noMsg");

		L120M01A model = service1201.findL120m01aByMainId(mainId);

		String needCols[] = new String[] { "oid", "typCd" };
		result = DataParse.toResult(model, DataParse.Need, needCols);

		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult getLatestMow(PageParameters params) throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS02BPanel.class);
		CapAjaxFormResult result = new CapAjaxFormResult();

		String thisOid = Util.nullToSpace(params.getString("thisOid"));
		L120S01A l120s01a = service1201.findL120s01aByOid(thisOid);
		String custId = "";
		String dupNo = "";
		String _crdType = ""; // *評等表類型
		String _crdTBR = ""; // *評等單位
		String crdTYear = ""; // *評等（公佈）日期
		String grade = ""; // *評等等級
		String finYear = ""; // 評等財報年度
		String prospect = ""; // 評等展望
		String prCustId = ""; // 保證企業統一編號
		String prDupNo = ""; // 保證企業重覆序號
		String prCNAME = ""; // 保證企業名稱
		String prFR = ""; // 保證企業最終評等
		String prFinDate = ""; // 保證企業所引用之財報期間
		String prMOWBr = ""; // 保證企業之評等單位

		// J-110-0445_05097_B1001 於簽報書借款人基本資料之信用評等下新增欄位
		String noteId = "";
		String pr = "";
		String sa = "";
		String spr = "";
		String fr = "";
		String warn1 = "";
		String warn2 = "";
		String warn3 = "";

		if (l120s01a != null) {
			custId = Util.trim(l120s01a.getCustId());
			dupNo = Util.trim(l120s01a.getDupNo());
		}
		List<Map<String, Object>> rows = misDbService.findMISMOWTBL1_selMow(
				custId, dupNo);
		boolean noMow = false;
		if (rows != null && !rows.isEmpty()) {
			Map<String, Object> data = rows.get(0);
			_crdType = Util.trim(data.get("MOWTYPE"));
			_crdTBR = Util.trim(Util.nullToSpace(data.get("MOWBR")));
			crdTYear = Util.trim(Util.nullToSpace(data.get("MOWYMD")));
			grade = Util.trim(Util.nullToSpace(data.get("FR")));
			finYear = Util.isEmpty(Util.trim(Util.nullToSpace(data
					.get("FINDATE")))) ? Util.trim(Util.nullToSpace(data
					.get("MOWYMD"))) : Util.trim(Util.nullToSpace(data
					.get("FINDATE")));
			prospect = Util.trim(Util.nullToSpace(data.get("SA")));
			prCustId = Util.trim(Util.nullToSpace(data.get("PRCUSTID")));
			prDupNo = Util.trim(Util.nullToSpace(data.get("PRDUPNO")));
			prCNAME = Util.trim(Util.nullToSpace(data.get("PRCNAME")));
			prFR = Util.trim(Util.nullToSpace(data.get("PRFR")));
			prFinDate = Util.trim(Util.nullToSpace(data.get("PRFINDATE")));
			prMOWBr = Util.trim(Util.nullToSpace(data.get("PRMOWBR")));

			// J-110-0445_05097_B1001 於簽報書借款人基本資料之信用評等下新增欄位
			noteId = Util.trim(Util.nullToSpace(data.get("NOTEID")));
			pr = Util.trim(Util.nullToSpace(data.get("PR")));
			sa = Util.trim(Util.nullToSpace(data.get("SA")));
			spr = Util.trim(Util.nullToSpace(data.get("SPR")));
			fr = Util.trim(Util.nullToSpace(data.get("FR")));
			warn1 = Util.trim(Util.nullToSpace(data.get("WARN1")));
			warn2 = Util.trim(Util.nullToSpace(data.get("WARN2")));
			warn3 = Util.trim(Util.nullToSpace(data.get("WARN3")));

		} else {
			// 沒選信評等同於按取消鈕
			noMow = true;
		}

		// 比照function saveMowTrust(
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		List<L120S01C> list = service1201.findL120s01cByMainId(mainId);
		List<L120S01C> delList = new ArrayList<L120S01C>();
		for (L120S01C model0 : list) {
			if (custId.equals(Util.trim(model0.getCustId()))
					&& dupNo.equals(Util.trim(model0.getDupNo()))) {
				String crdType = Util.trim(model0.getCrdType());
				if (isMow(crdType)) {
					// 內部評等
					delList.add(model0);
				} else if (isNormalTrust(crdType, true)
						|| UtilConstants.Casedoc.CrdType.未評等.equals(crdType)) {
					delList.add(model0);
				}
			}
		}
		if (!delList.isEmpty()) {
			service1201.deleteListL120s01c(delList);
		}
		CapAjaxFormResult myForm2Result = new CapAjaxFormResult();
		if (!noMow) {
			service1201.saveMow(mainId, _crdType, _crdTBR, crdTYear, grade,
					finYear, prospect, prCustId, prDupNo, prCNAME, prFR,
					prFinDate, prMOWBr, custId, dupNo);
			myForm2Result.set("grade0", grade);
		} else {
			List<L120S01C> l120s01cs = new ArrayList<L120S01C>();
			service1201.getNewTrust(l120s01cs, mainId, custId, dupNo,
					UtilConstants.Casedoc.CrdType.未評等);
			service1201.getNewTrust(l120s01cs, mainId, custId, dupNo,
					UtilConstants.Casedoc.CrdType2.免辦);
			service1201.saveListL120s01c(l120s01cs);
			myForm2Result.set("grade0", pop.getProperty("l120s01c.dont"));
		}

		myForm2Result.set("borrGrade", pop.getProperty("l120s01c.noGrade")); // 未評等(信用評等)
		myForm2Result.set("grade0", Util.trim(myForm2Result.get("grade0"))
				+ ((Util.isNumeric(myForm2Result.get("grade0"))) ? "等" : ""));

		// J-110-0445_05097_B1001 於簽報書借款人基本資料之信用評等下新增欄位
		Map<String, Object> ces120Map = this.getMowGradeData(mainId, custId,
				dupNo, _crdType, noteId, pr, sa, spr, fr, warn1, warn2, warn3);
		myForm2Result.putAll(ces120Map);

		result.set("L120S01aForm", myForm2Result);

		return result;
	}// ;

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult getLatestCes(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.nullToSpace(params.getString("mainId"));
		String thisOid = Util.nullToSpace(params.getString("thisOid"));
		L120M01A l120m01a = service1201.findL120m01aByMainId(mainId);
		L120S01A l120s01a = service1201.findL120s01aByOid(thisOid);
		String caseBrId = "";
		String custId = "";
		String dupNo = "";
		String cesMainId = "";
		if (l120s01a != null) {
			caseBrId = Util.trim(l120m01a.getCaseBrId());
			custId = Util.trim(l120s01a.getCustId());
			dupNo = Util.trim(l120s01a.getDupNo());
		}
		// ORDER BY B.CUSTID, B.DUPNO,B.CREATETIME DESC,B.CREATETIME DESC ,
		// B.APPROVETIME DESC
		List<Map<String, Object>> rows = eloanDbBaseService
				.findC120M01A_selMainId2(caseBrId, custId, dupNo);
		if (rows != null && !rows.isEmpty()) {
			Map<String, Object> data = rows.get(0);
			cesMainId = Util.trim(Util.nullToSpace(data.get("MAINID")));
		} else {
			// 沒資料直接執行 importCust();
			result.set("noCES", true);
		}

		// 比照function getBorrowDataFromCES(
		if (!StringUtils.isEmpty(cesMainId)) {
			// 徵信報告-一般概況
			Map<String, Object> overview = eloanDbBaseService
					.getC140M01A_Overview(cesMainId);
			JSONObject overviewJson = JSONObject.fromObject(MapUtils.getString(
					overview, "JSONOB", "{}"));

			result.set("chairman", overviewJson.optString("cpPeople1", ""));
			result.set("chairmanId", overviewJson.optString("cpPeoid1", ""));
			result.set("gManager", overviewJson.optString("cpPeople", ""));
			result.set("cmpAddr", overviewJson.optString("regAddr", ""));
			result.set("ntCode", overviewJson.optString("regPlace", ""));
			result.set("cmpAddr", overviewJson.optString("cpAddr", ""));
			result.set("factoryAddr", overviewJson.optString("facAddr", ""));
			result.set("stockStatus", overviewJson.optString("cpStock", ""));
			result.set("stockDate", overviewJson.optString("emDate", ""));

			// 徵信報告-資本變動情況
			Map<String, Object> changeInShareCapital = eloanDbBaseService
					.getC140M01A_ChangeInShareCapital(cesMainId);
			JSONObject changeInShareCapitalJson = JSONObject
					.fromObject(MapUtils.getString(changeInShareCapital,
							"JSONOB", "{}"));
			if (changeInShareCapitalJson != null
					&& changeInShareCapitalJson.size() > 0) {
				for (int i = 5; i >= 1; i--) {
					String capD = changeInShareCapitalJson.optString(
							"capD" + i, "");
					if (!StringUtils.isEmpty(capD)) {
						result.set("rgtAmt", changeInShareCapitalJson
								.optString("capR" + i, ""));
						result.set("cptlAmt", changeInShareCapitalJson
								.optString("capC" + i, ""));
						break;
					}
				}

				result.set("rgtCurr",
						changeInShareCapitalJson.optString("curr"));
				result.set("cptlCurr",
						changeInShareCapitalJson.optString("curr"));
				result.set("rgtUnit",
						changeInShareCapitalJson.optString("unit"));
				result.set("cptlUnit",
						changeInShareCapitalJson.optString("unit"));
			}

			// 徵信報告-沿革
			Map<String, Object> history = eloanDbBaseService
					.getC140M01A_History(cesMainId);
			result.set("stockHolder", MapUtils.getString(history, "VAL", ""));

			// 徵字資信簡表上的成立(改組)日期
			Map<String, Object> estaDate = eloanDbBaseService
					.getC140C120M01A_EstaDate(cesMainId);
			result.set("estDate", MapUtils.getString(estaDate, "ESTADATE", ""));

			// 徵信報告-主要營業項目
			List<Map<String, Object>> itemOfBusi = eloanDbBaseService
					.findBusi(cesMainId);
			if (itemOfBusi != null && !itemOfBusi.isEmpty()) {
				result.set("itemOfBusi",
						MapUtils.getString(itemOfBusi.get(0), "VAL", ""));
			}

			// 徵信報告-有無大陸投資
			Map<String, Object> ch6_LLand_inv = eloanDbBaseService
					.getC140M01A_Ch6_LLand_inv(cesMainId);
			result.set("invMFlag", MapUtils.getString(ch6_LLand_inv, "inv", ""));

			// 資料來源為徵信報告的CKEDITOR,要特別處理，才能就圖檔複製
			String invMDscr = docFileService.copyCKEditorImageFile(Util
					.trim(MapUtils.getString(ch6_LLand_inv, "invMDscr", "")),
					mainId, "CES");
			result.set("invMDscr", invMDscr);

			// 不符合中小企業定義(A01~A03)及貸款期間(A02:3年、A03:5年)，則系統(eLoan 與
			// aLoan)不能讓該授信案成為經濟部紓困案件(A01~A03)
			// 徵信報告-員工人數
			// Map<String, Object> totEmp = eloanDbBaseService
			// .getC140M01A_TotEmp(cesMainId);
			// JSONObject totEmpJson = JSONObject.fromObject(MapUtils.getString(
			// totEmp, "JSONOB", "{}"));
			// if (totEmpJson != null && totEmpJson.size() > 0) {
			// result.set("totEmp", totEmpJson.optString("totEmp"));
			// }

			// J-109-0KKK_05097_B1001 簡化青年創業及啟動金貸款簽報書簽案流程
			result.set("CESMAINID", cesMainId);

			// J-110-0445_05097_B1001 於簽報書借款人基本資料之信用評等下新增欄位
			// Map<String, Object> ces120Map = this.getLastCes120Data(cesMainId,
			// params, parent);
			// CapAjaxFormResult mowGradeData = new CapAjaxFormResult();
			// mowGradeData.putAll(ces120Map);
			// result.set("mowGradeData", mowGradeData);

		}
		return result;
	}// ;

	// /**
	// * J-107-0093-001 引進TCRI信用風險
	// *
	// * @return IResult
	// * @throws CapException
	// */
	// public IResult queryTCRI2(PageParameters params, Component parent)
	// throws CapException {
	// CapAjaxFormResult result = new CapAjaxFormResult();
	// Properties pop = MessageBundleScriptCreator
	// .getComponentResource(LMSS02BPanel.class);
	// String custId = params.getString("custId");
	// //custId = "73251209";
	// if (!CapString.isEmpty(custId)) {
	// String stockNo = service1201.getStockNoByCustId(custId);
	// List<Map<String, Object>> list = tejSrv
	// .findCrmtabByStockNo(stockNo);
	// if (list != null && !list.isEmpty()) {
	// String yymm_newest = "", yymm_last = "", t_period1 = "";
	// String t_grade2 = "", t_score2 = "", t_period2 = "";
	// Map<String, Object> newest = list.get(0);
	// yymm_newest = CapString.trimNull(newest.get("yymm"));
	// t_period1 = formatDateForTCRI(yymm_newest);
	// String t_score1 = CapString.trimNull(newest.get("tcri"));
	// if (CapString.isEmpty(t_score1) || "-".equals(t_score1)) {
	// // ces1200.0293=股票代號:{0}
	// // 查無TCRI評等結果。<br/>企業情報資料庫系統不評等條件如下:<br/>1.成立未滿4年且財簽未滿3年不評等<br/>2.金融業不評等
	// result.set("msg", MessageFormat.format(pop
	// .getProperty("ces1200.0293"), new Object[] { custId
	// .equals(stockNo) ? "無" : stockNo }));
	// } else {
	// String t_grade1 = "C".equals(t_score1) ? "未公佈財報" : "D"
	// .equals(t_score1) ? "違約" : pop.getProperty(Integer
	// .parseInt(t_score1) > 6 ? "ces1200.0290" : Integer
	// .parseInt(t_score1) > 4 ? "ces1200.0289"
	// : "ces1200.0288");
	// if (list.size() > 1) {
	// Map<String, Object> last = list.get(1);
	//
	// // J-107-0279,EL07623, 改為上一年度評等
	// for (int i = 1; i < list.size(); i++) {
	// Map<String, Object> l = list.get(i);
	// if (CapString.trimNull(l.get("yymm"))
	// .endsWith("12")) {
	// last = l;
	// break;
	// }
	// }
	//
	// yymm_last = CapString.trimNull(last.get("yymm"));
	// t_period2 = formatDateForTCRI(yymm_last);
	// t_score2 = CapString.trimNull(last.get("tcri"));
	// if (!CapString.isEmpty(t_score2)
	// && !"-".equals(t_score2)) {
	// t_grade2 = "C".equals(t_score2) ? "未公佈財報"
	// : "D".equals(t_score2) ? "違約"
	// : pop.getProperty(Integer
	// .parseInt(t_score2) > 6 ? "ces1200.0290"
	// : Integer
	// .parseInt(t_score2) > 4 ? "ces1200.0289"
	// : "ces1200.0288");
	// }
	// }
	// result.set("t_period1", t_period1);
	// result.set("t_period2", t_period2);
	// result.set("t_score1", t_score1);
	// result.set("t_score2", t_score2);
	// result.set("t_grade1", t_grade1);
	// result.set("t_grade2", t_grade2);
	// }
	// } else {
	// // ces1200.0293=股票代號:{0}
	// // 查無TCRI評等結果。<br/>企業情報資料庫系統不評等條件如下:<br/>1.成立未滿4年且財簽未滿3年不評等<br/>2.金融業不評等
	// result.set("msg",
	// MessageFormat.format(pop.getProperty("ces1200.0293"),
	// new Object[] { custId.equals(stockNo) ? "無"
	// : stockNo }));
	// }
	// }
	// return result;
	// }
	//
	// /**
	// * J-107-0093-001 引進TCRI信用風險
	// *
	// * @return IResult
	// * @throws CapException
	// */
	// @DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	// public IResult queryTCRI(PageParameters params, Component parent)
	// throws CapException {
	// CapAjaxFormResult result = new CapAjaxFormResult();
	// Properties pop = MessageBundleScriptCreator
	// .getComponentResource(LMSS02BPanel.class);
	// String custId = params.getString("custId");
	// if (!CapString.isEmpty(custId)) {
	// String stockNo = service1201.getStockNoByCustId(custId);
	// List<Map<String, Object>> list = tejSrv
	// .findCrmtabByStockNo(stockNo);
	// if (list != null && !list.isEmpty()) {
	// String yymm_newest = "", yymm_last = "", t_period1 = "";
	// String t_grade2 = "", t_score2 = "", t_period2 = "";
	// Map<String, Object> newest = list.get(0);
	// yymm_newest = CapString.trimNull(newest.get("yymm"));
	// t_period1 = formatDateForTCRI(yymm_newest);
	// String t_score1 = CapString.trimNull(newest.get("tcri"));
	// if (CapString.isEmpty(t_score1) || "-".equals(t_score1)) {
	// // ces1200.0293=股票代號:{0}
	// // 查無TCRI評等結果。<br/>企業情報資料庫系統不評等條件如下:<br/>1.成立未滿4年且財簽未滿3年不評等<br/>2.金融業不評等
	// result.set("msg", MessageFormat.format(pop
	// .getProperty("ces1200.0293"), new Object[] { custId
	// .equals(stockNo) ? "無" : stockNo }));
	// } else {
	// String t_grade1 = "C".equals(t_score1) ? "未公佈財報" : "D"
	// .equals(t_score1) ? "違約" : pop.getProperty(Integer
	// .parseInt(t_score1) > 6 ? "ces1200.0290" : Integer
	// .parseInt(t_score1) > 4 ? "ces1200.0289"
	// : "ces1200.0288");
	// if (list.size() > 1) {
	// Map<String, Object> last = list.get(1);
	//
	// // J-107-0279,EL07623, 改為上一年度評等
	// for (int i = 1; i < list.size(); i++) {
	// Map<String, Object> l = list.get(i);
	// if (CapString.trimNull(l.get("yymm"))
	// .endsWith("12")) {
	// last = l;
	// break;
	// }
	// }
	//
	// yymm_last = CapString.trimNull(last.get("yymm"));
	// t_period2 = formatDateForTCRI(yymm_last);
	// t_score2 = CapString.trimNull(last.get("tcri"));
	// if (!CapString.isEmpty(t_score2)
	// && !"-".equals(t_score2)) {
	// t_grade2 = "C".equals(t_score2) ? "未公佈財報"
	// : "D".equals(t_score2) ? "違約"
	// : pop.getProperty(Integer
	// .parseInt(t_score2) > 6 ? "ces1200.0290"
	// : Integer
	// .parseInt(t_score2) > 4 ? "ces1200.0289"
	// : "ces1200.0288");
	// }
	// }
	// result.set("t_period1", t_period1);
	// result.set("t_period2", t_period2);
	// result.set("t_score1", t_score1);
	// result.set("t_score2", t_score2);
	// result.set("t_grade1", t_grade1);
	// result.set("t_grade2", t_grade2);
	// }
	// } else {
	// // ces1200.0293=股票代號:{0}
	// // 查無TCRI評等結果。<br/>企業情報資料庫系統不評等條件如下:<br/>1.成立未滿4年且財簽未滿3年不評等<br/>2.金融業不評等
	// result.set("msg",
	// MessageFormat.format(pop.getProperty("ces1200.0293"),
	// new Object[] { custId.equals(stockNo) ? "無"
	// : stockNo }));
	// }
	// }
	// return result;
	// }
	//
	// private String formatDateForTCRI(String yyyyMM) {
	// if (CapString.isEmpty(yyyyMM)) {
	// return yyyyMM;
	// }
	// String yyyy = yyyyMM.substring(0, 4);
	// String startDate = yyyy + "-01-01";
	// String endDate = yyyy;
	// if (yyyyMM.endsWith("3")) {
	// endDate = yyyy + "-03-31";
	// } else if (yyyyMM.endsWith("6")) {
	// endDate = yyyy + "-06-30";
	// } else if (yyyyMM.endsWith("9")) {
	// endDate = yyyy + "-09-30";
	// } else {
	// endDate = yyyy + "-12-31";
	// }
	// return startDate + "~" + endDate;
	// }

	/**
	 * J-110-0445_05097_B1001 於簽報書借款人基本資料之信用評等下新增欄位
	 */
	public Map<String, Object> getLastCes120Data(String ces140MainId, PageParameters params) {
		Map<String, Object> subReportMowMap = new HashMap<String, Object>();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		L120M01A l120m01a = service1201.findL120m01aByMainId(mainId);

		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMSS02BPanel.class);

		String ces120MainId = "";
		// //取得資信簡表MAINID
		if (Util.notEquals(ces140MainId, "")) {
			// 有徵信報告MAINID，取得資信簡表MAINID
			List<Map<String, Object>> temp140List = eloanDbBaseService
					.C140M01A_selCustname2(ces140MainId);
			for (Map<String, Object> map : temp140List) {
				ces120MainId = Util.trim(map.get("C120M01A_MAINID"));
				break;
			}
		}
		if (Util.equals(ces120MainId, "")) {
			// 無徵信報告MAINID，取得最新一筆資信簡表MAINID
			List<?> rows = eloanDbBaseService.findC120M01A_selMainIda(
					l120m01a.getCaseBrId(), l120m01a.getMainId(),
					l120m01a.getMainId());
			Iterator<?> it = rows.iterator();
			int count = 0;
			while (it.hasNext()) {
				Map<?, ?> dataMap = (Map<?, ?>) it.next();
				ces120MainId = Util
						.trim(Util.nullToSpace(dataMap.get("MAINID")));
				break;
			}
		}

		if (Util.equals(ces120MainId, "")) {
			// 沒有資信簡表
			return subReportMowMap;
		}

		String SOURCERT = ""; // 評等來源
		List<Map<String, Object>> temp120List = eloanDbBaseService
				.C120M01A_selCustname(ces120MainId);
		if (temp120List != null && !temp120List.isEmpty()) {
			for (Map<String, Object> c120m01Map : temp120List) {
				SOURCERT = Util
						.trim(MapUtils.getString(c120m01Map, "SOURCERT"));
				break;
			}
		}

		if (Util.notEquals(SOURCERT, "5")) {
			// 資信簡表不是採用信用風險內部評等資料
			// 有無警訊
			subReportMowMap.put("hasWarnGrade", "3"); // 不適用
			subReportMowMap.put("warnGradeNote", "");
			// 有無調整評等
			subReportMowMap.put("hasDowngradeGrade", "");// 空白
			subReportMowMap.put("downgradeGradeNote", "");
			subReportMowMap.put("downgradeGradeStatus", "");
			return subReportMowMap;
		}

		List<Map<String, Object>> c120jsonList = eloanDbBaseService
				.findC120jsonByMainIdAndTab(ces120MainId, "02", "5");
		if (c120jsonList != null && !c120jsonList.isEmpty()) {
			for (Map<String, Object> c120jsonMap : c120jsonList) {
				String JSONOB = Util.trim(MapUtils.getString(c120jsonMap,
						"JSONOB"));
				if (Util.notEquals(JSONOB, "")) {
					JSONObject data = JSONObject.fromObject(JSONOB);

					// 有無警訊***************************************************************************************

					String mWarn1Name = CapString.trimNull(data.optString(
							"mWarn1Name", ""));
					String mWarn1 = CapString.trimNull(data.optString("mWarn1",
							""));
					String mWarn2Name = CapString.trimNull(data.optString(
							"mWarn2Name", ""));
					String mWarn2 = CapString.trimNull(data.optString("mWarn2",
							""));
					String mWarnNote = CapString.trimNull(data.optString(
							"mWarnNote", ""));

					subReportMowMap.put("mWarn1Name", mWarn1Name);
					subReportMowMap.put("mWarn1", mWarn1);
					subReportMowMap.put("mWarn2Name", mWarn2Name);
					subReportMowMap.put("mWarn2", mWarn2);
					subReportMowMap.put("mWarnNote", mWarnNote);

					// 一般警訊
					String LMS_BORROWER_MOW_GRADE_WARN1 = Util
							.trim(lmsService
									.getSysParamDataValue("LMS_BORROWER_MOW_GRADE_WARN1")); // 綠燈|绿灯|Green|0
					// 特殊警訊
					String LMS_BORROWER_MOW_GRADE_WARN2 = Util
							.trim(lmsService
									.getSysParamDataValue("LMS_BORROWER_MOW_GRADE_WARN2")); // 無特殊警訊發生|无特殊警讯发生|No
																							// Triggered|None|NoTriggered

					// 一般警訊
					boolean hasWarn1 = true;
					String[] tWarn1Arr = StringUtils.split(
							LMS_BORROWER_MOW_GRADE_WARN1, "|");
					for (String tWarn1 : tWarn1Arr) {
						if (Util.equals(Util.trim(mWarn1), tWarn1)) {
							hasWarn1 = false;
							break;
						}
					}

					// 特殊警訊
					boolean hasWarn2 = true;
					String[] tWarn2Arr = StringUtils.split(
							LMS_BORROWER_MOW_GRADE_WARN2, "|");
					for (String tWarn2 : tWarn2Arr) {
						if (Util.equals(Util.trim(mWarn2), tWarn2)) {
							hasWarn2 = false;
							break;
						}
					}

					if (hasWarn1 || hasWarn2) {
						subReportMowMap.put("hasWarnGrade", "1"); // 有
						subReportMowMap.put("warnGradeNote", mWarnNote);
					} else {
						subReportMowMap.put("hasWarnGrade", "2"); // 無
						subReportMowMap.put("warnGradeNote", "");
					}

					// 有無調整評等***********************************************************************************
					boolean hasDowngradeGrade = false;
					String mCrdType = CapString.trimNull(data
							.optString("mCrdType"));
					String mPr = CapString.trimNull(data.optString("mPr", "")); // 初始評等
					String mSa = CapString.trimNull(data.optString("mSa", "")); // 支援評等
					String mFr = CapString.trimNull(data.optString("mFr", "")); // 最終評等
					String mSpr = CapString
							.trimNull(data.optString("mSpr", "")); // 保證企業評等

					String upgradePattern = prop.getProperty("ces120.upgrade");
					String downgradePattern = prop
							.getProperty("ces120.downgrade");
					String none = prop.getProperty("ces120.none");

					try {

						// 初始評等:mPr
						// 最終評等:mFr
						if (!"".equals(mCrdType)) {

							if (StringUtils.isNotEmpty(mSpr)) {
								int _mPr = Integer.parseInt(mPr); // 初始評等:mPr
								int _mFr = Integer.parseInt(mFr); // 最終評等:mFr
								int dis = Math.abs(_mFr - _mPr);
								if (_mPr < _mFr) {
									// 調降
									hasDowngradeGrade = true;
									subReportMowMap.put("hasDowngradeGrade",
											"1");
									subReportMowMap.put("downgradeGradeStatus",
											MessageFormat.format(
													downgradePattern,
													String.valueOf(dis)));
								} else {
									// 無調降
									hasDowngradeGrade = false;
									subReportMowMap.put("hasDowngradeGrade",
											"2");
									subReportMowMap.put("downgradeGradeStatus",
											"");
								}
							}

							// gradeStatus
							// 主觀評等更新可能是從sa 升到 fr，或是 從 spr 升到 fr，所以要做2種判斷
							// if (StringUtils.isNotEmpty(mSpr)) {
							// if (mSpr.equals(mFr)) {
							// subReportMowMap.put("gradeStatus", none);
							// } else {
							// int _mSpr = Integer.parseInt(mSpr);
							// int _mFr = Integer.parseInt(mFr);
							// int dis = Math.abs(_mFr - _mSpr);
							// if (_mSpr > _mFr) {
							// subReportMowMap.put("gradeStatus",
							// MessageFormat.format(
							// upgradePattern,
							// String.valueOf(dis)));
							// hasAdjustGrade = true;
							// } else {
							// subReportMowMap.put("gradeStatus",
							// MessageFormat.format(
							// downgradePattern,
							// String.valueOf(dis)));
							// hasAdjustGrade = true;
							// }
							// }
							// } else {
							// if (mSa.equals(mFr)) {
							// // reportData.setField("gradeStatus",
							// // "無");
							// subReportMowMap.put("gradeStatus", none);
							// } else {
							// int _mSa = Integer.parseInt(mSa);
							// int _mFr = Integer.parseInt(mFr);
							// int dis = Math.abs(_mFr - _mSa);
							// if (_mSa > _mFr) {
							// // reportData.setField("gradeStatus",
							// // "調升" + dis + "等");
							// subReportMowMap.put("gradeStatus",
							// MessageFormat.format(
							// upgradePattern,
							// String.valueOf(dis)));
							// hasAdjustGrade = true;
							// } else {
							// // reportData.setField("gradeStatus",
							// // "調降" + dis + "等");
							// subReportMowMap.put("gradeStatus",
							// MessageFormat.format(
							// downgradePattern,
							// String.valueOf(dis)));
							// hasAdjustGrade = true;
							// }
							// }
							// }

						}
						break;

					} catch (NumberFormatException e) {

					}

				}

			}
		}

		return subReportMowMap;
	}

	/**
	 * J-110-0445_05097_B1001 於簽報書借款人基本資料之信用評等下新增欄位
	 */
	public Map<String, Object> getMowGradeData(String mainId, String custId,
			String dupNo, String crdType, String noteId, String pr, String sa,
			String spr, String fr, String warn1, String warn2, String warn3) {
		Map<String, Object> subReportMowMap = new HashMap<String, Object>();
		L120M01A l120m01a = service1201.findL120m01aByMainId(mainId);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMSS02BPanel.class);

		String SOURCERT = ""; // 評等來源

		if (Util.equals(noteId, "")) {
			// 不是採用信用風險內部評等資料
			// 有無警訊
			subReportMowMap.put("hasWarnGrade", "3"); // 不適用
			subReportMowMap.put("warnGradeNote", "");
			// 有無調整評等
			subReportMowMap.put("hasDowngradeGrade", "");// 空白
			subReportMowMap.put("downgradeGradeNote", "");
			subReportMowMap.put("downgradeGradeStatus", "");
			return subReportMowMap;
		}

		String mWarn1 = CapString.trimNull(warn1);
		String mWarn2 = CapString.trimNull(warn2);
		String mWarn3 = CapString.trimNull(warn3);

		// 一般警訊
		String LMS_BORROWER_MOW_GRADE_WARN1 = Util.trim(lmsService
				.getSysParamDataValue("LMS_BORROWER_MOW_GRADE_WARN1")); // G 0
																		// ''
		// 特殊警訊
		String LMS_BORROWER_MOW_GRADE_WARN2 = Util.trim(lmsService
				.getSysParamDataValue("LMS_BORROWER_MOW_GRADE_WARN2")); // N 0
																		// ''

		String LMS_BORROWER_MOW_GRADE_WARN3 = Util.trim(lmsService
				.getSysParamDataValue("LMS_BORROWER_MOW_GRADE_WARN3")); // 0 ''

		// 一般警訊
		boolean hasWarn1 = Util.equals(Util.trim(mWarn1), "") ? false : true;
		if (Util.notEquals(mWarn1, "")) {
			String[] tWarn1Arr = StringUtils.split(
					LMS_BORROWER_MOW_GRADE_WARN1, "|");
			for (String tWarn1 : tWarn1Arr) {
				if (Util.equals(Util.trim(mWarn1), tWarn1)) {
					hasWarn1 = false;
					break;
				}
			}
		}

		// 特殊警訊
		boolean hasWarn2 = Util.equals(Util.trim(mWarn2), "") ? false : true;
		if (Util.notEquals(mWarn2, "")) {
			String[] tWarn2Arr = StringUtils.split(
					LMS_BORROWER_MOW_GRADE_WARN2, "|");
			for (String tWarn2 : tWarn2Arr) {
				if (Util.equals(Util.trim(mWarn2), tWarn2)) {
					hasWarn2 = false;
					break;
				}
			}
		}

		// 特殊警訊
		boolean hasWarn3 = Util.equals(Util.trim(mWarn3), "") ? false : true;
		if (Util.notEquals(mWarn3, "")) {
			String[] tWarn3Arr = StringUtils.split(
					LMS_BORROWER_MOW_GRADE_WARN3, "|");
			for (String tWarn3 : tWarn3Arr) {
				if (Util.equals(Util.trim(mWarn3), tWarn3)) {
					hasWarn3 = false;
					break;
				}
			}
		}

		if (hasWarn1 || hasWarn2 || hasWarn3) {

			// 取得資簡評等欄位下方備註

			// noteId(MOW的OID) 要轉換為 MOW的MAINID
			String mowMainId = "";

			Map<String, Object> m100m01aMap = eloanDbBaseService
					.findCesM100m01aByOid(noteId);
			mowMainId = Util.trim(MapUtils.getString(m100m01aMap, "MAINID"));
			String mWarnNote = "";
			if (Util.notEquals(mowMainId, "")) {

				// List<Map<String, Object>> temp120List = eloanDbBaseService
				// .findC120M01A_selMainIdb(user.getUnitNo(), custId);

				List<Map<String, Object>> temp120List = eloanDbBaseService
						.findC120M01A_selMainId1(user.getUnitNo(), custId, "0");

				if (temp120List != null && !temp120List.isEmpty()) {
					int count = 0;
					boolean hasFound = false;
					for (Map<String, Object> c120m01Map : temp120List) {
						count = count + 1;
						SOURCERT = Util.trim(MapUtils.getString(c120m01Map,
								"SOURCERT"));
						String ces120MainId = Util.trim(MapUtils.getString(
								c120m01Map, "MAINID"));
						if (Util.equals(SOURCERT, "5")) {
							List<Map<String, Object>> c120jsonList = eloanDbBaseService
									.findC120jsonByMainIdAndTab(ces120MainId,
											"02", "5");
							if (c120jsonList != null && !c120jsonList.isEmpty()) {
								for (Map<String, Object> c120jsonMap : c120jsonList) {
									String JSONOB = Util.trim(MapUtils
											.getString(c120jsonMap, "JSONOB"));
									if (Util.notEquals(JSONOB, "")) {
										JSONObject data = JSONObject
												.fromObject(JSONOB);

										String mainId5 = CapString
												.trimNull(data.optString(
														"mainId5", "")); // 資信簡表與MOW連結
										// mWarnNote mPInote
										if (Util.equals(mowMainId, mainId5)) {
											// 資信簡表引用的MOW跟MOWTBL1引進的一樣
											String tmPInote = Util
													.trim(CapString.trimNull(data
															.optString(
																	"mPInote",
																	"")));
											String tmWarnNote = Util
													.trim(CapString.trimNull(data
															.optString(
																	"mWarnNote",
																	"")));

											mWarnNote = tmPInote;

											if (Util.notEquals(tmWarnNote, "")) {
												mWarnNote = mWarnNote
														+ (Util.equals(
																mWarnNote, "") ? ""
																: "\r")
														+ CapString
																.trimNull(data
																		.optString(
																				"mWarnNote",
																				""));
											}

											hasFound = true;
											break;
										}
									}
								}

							}
						}
						if (hasFound) {
							break;
						}
						if (count > 30) {
							// 最多掃描30筆
							break;
						}
					}
				}
			}

			subReportMowMap.put("hasWarnGrade", "1"); // 有
			subReportMowMap.put("warnGradeNote", mWarnNote);
		} else {
			subReportMowMap.put("hasWarnGrade", "2"); // 無
			subReportMowMap.put("warnGradeNote", "");
		}

		// 有無調整評等***********************************************************************************
		boolean hasDowngradeGrade = false;
		String mCrdType = crdType;
		String mPr = CapString.trimNull(pr); // 初始評等
		String mSa = CapString.trimNull(sa); // 支援評等
		String mFr = CapString.trimNull(fr); // 最終評等
		String mSpr = CapString.trimNull(spr); // 保證企業評等

		String upgradePattern = prop.getProperty("ces120.upgrade");
		String downgradePattern = prop.getProperty("ces120.downgrade");
		String none = prop.getProperty("ces120.none");

		try {

			// 初始評等:mPr
			// 最終評等:mFr
			if (!"".equals(mCrdType)) {

				if (StringUtils.isNotEmpty(mSpr)) {
					int _mPr = Integer.parseInt(mPr); // 初始評等:mPr
					int _mFr = Integer.parseInt(mFr); // 最終評等:mFr
					int dis = Math.abs(_mFr - _mPr);
					if (_mPr < _mFr) {
						// 調降
						hasDowngradeGrade = true;
						subReportMowMap.put("hasDowngradeGrade", "1");
						subReportMowMap.put(
								"downgradeGradeStatus",
								MessageFormat.format(downgradePattern,
										String.valueOf(dis)));
					} else {
						// 無調降
						hasDowngradeGrade = false;
						subReportMowMap.put("hasDowngradeGrade", "2");
						subReportMowMap.put("downgradeGradeStatus", "");
					}
				}

			}

		} catch (NumberFormatException e) {

		}

		return subReportMowMap;
	}

	/**
	 * 判斷簽報書文件狀態可否異動借款人基本資料 J-111-0443_05097_B1006 Web e-Loan企金授信開發授信BIS評估表
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult chkDocStatusCanEditBorrower(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));

		// J-111-0443_05097_B1006 Web e-Loan企金授信開發授信BIS評估表
		// 修正因為分行前端網頁問題狀態錯誤，導致已核准簽報書借款人基本資料有顯示引進按鈕
		// 014 28883691
		L120M01A l120m01a = service1201.findL120m01aByMainId(mainId);

		String canEditMsg = lmsService.chkRptDocStatusCanEditBorrower(l120m01a);

		if (Util.isNotEmpty(canEditMsg)) {
			// 文件狀態非「編制中」或「待補件」，不得執行簽報書借款人基本資料引進或異動等相關功能
			throw new CapMessageException(canEditMsg, getClass());
		}

		result.set("canEdit", Util.isEmpty(canEditMsg) ? "Y" : "N");

		return result;
	}

	/**
	 * 取得 申貸戶不符ESG而暫緩承作紀錄 J-111-0535_05097_B1001 Web e-Loan企金授信配合「ESG綜效調查表
	 * 」建置，於簽報書增設相對應欄位
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getEsgReject(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		boolean noMsg = params.getBoolean("noMsg");
		L120S01A model = service1201.findL120s01aByUniqueKey(mainId, custId,
				dupNo);
		List<Map<String, Object>> listMap = service1201.findEsgReject(custId,
				dupNo);
		if (!listMap.isEmpty()) {
			// 有婉卻紀錄
			for (Map<String, Object> map : listMap) {
				// 申貸戶是否有因不符ESG而暫緩承作
				model.setIsEsgRejt(UtilConstants.DEFAULT.是);
				// 查詢日期
				model.setEsgRejtReadDate(new Date());
				// 登錄分行
				model.setEsgRejtBrNo(Util.trim(map.get("OWNBRID")));
				// 不符ESG而暫緩承作登錄行員
				String empNo = Util.equals(Util.trim(map.get("UPDATER")), "") ? Util
						.trim(map.get("CREATOR")) : Util.trim(map
						.get("UPDATER"));
				model.setEsgRejtEmpNo(empNo);
				// 登錄時間
				model.setEsgRejtDate(CapDate.convertStringToTimestamp(Util
						.trim(map.get("APPROVETIME"))));

				service1201.save(model);
				String needCols[] = new String[] { "isEsgRejt",
						"esgRejtReadDate", "esgRejtBrNo", "esgRejtEmpNo",
						"esgRejtDate" };
				CapAjaxFormResult formL120s01a = DataParse.toResult(model,
						DataParse.Need, needCols);
				SimpleDateFormat formatter = new SimpleDateFormat(
						UtilConstants.DateFormat.YYYY_MM_DD_HH_MM_SS);
				formL120s01a.set("esgRejtBrNo", getRejtBrName(Util
						.trim(formL120s01a.get("esgRejtBrNo"))));

				if (Util.notEquals(Util.nullToSpace(Util.trim(formL120s01a
						.get("esgRejtEmpNo"))), "")) {
					formL120s01a.set(
							"esgRejtEmpNo",
							Util.nullToSpace(Util.trim(formL120s01a
									.get("esgRejtEmpNo")))
									+ " "
									+ Util.nullToSpace(userInfoService
											.getUserName(Util.trim(formL120s01a
													.get("esgRejtEmpNo")))));
				} else {
					formL120s01a.set("esgRejtEmpNo", "");
				}

				formL120s01a.set(
						"esgRejtDate",
						Util.isNotEmpty(model.getEsgRejtDate()) ? formatter
								.format(model.getEsgRejtDate())
								: UtilConstants.Mark.SPACE);
				result.set("L120S01aForm", formL120s01a);
				break;
			}
		}
		if (result.containsKey("L120S01aForm")) {
			if (!noMsg) {
				// 印出執行成功訊息!
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
						RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
			}
		} else {
			// 申貸戶是否有因不符ESG而暫緩承作
			model.setIsEsgRejt(UtilConstants.DEFAULT.否);
			// 查詢日期
			model.setEsgRejtReadDate(new Date());
			// 登錄分行
			model.setEsgRejtBrNo(null);
			// 不符ESG而暫緩承作登錄行員
			model.setEsgRejtEmpNo(null);
			// 登錄時間
			model.setEsgRejtDate(null);

			service1201.save(model);
			CapAjaxFormResult formL120s01a = new CapAjaxFormResult();
			formL120s01a.set("isEsgRejt", Util.trim(model.getIsEsgRejt()));
			formL120s01a.set("esgRejtReadDate", CapDate.formatDate(
					model.getEsgRejtReadDate(),
					UtilConstants.DateFormat.YYYY_MM_DD));
			formL120s01a.set("esgRejtBrNo", UtilConstants.Mark.SPACE);
			formL120s01a.set("esgRejtEmpNo", UtilConstants.Mark.SPACE);
			formL120s01a.set("esgRejtDate", UtilConstants.Mark.SPACE);

			result.set("L120S01aForm", formL120s01a);
			// throw new CapMessageException(RespMsgHelper.getMessage(parent,
			// UtilConstants.AJAX_RSP_MSG.查無資料), getClass());
		}
		return result;
	}

}
