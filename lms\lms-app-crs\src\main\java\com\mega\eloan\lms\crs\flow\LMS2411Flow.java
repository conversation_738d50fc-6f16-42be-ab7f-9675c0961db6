package com.mega.eloan.lms.crs.flow;

import java.sql.Timestamp;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;

import com.mega.eloan.common.flow.AbstractFlowHandler;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.crs.service.LMS2401Service;
import com.mega.eloan.lms.dao.C241A01ADao;
import com.mega.eloan.lms.dao.C241M01ADao;
import com.mega.eloan.lms.dao.C241M01EDao;
import com.mega.eloan.lms.model.C241M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

@Component
public class LMS2411Flow extends AbstractFlowHandler {
	
	public static final String LMS2411FLOW = "LMS2411Flow";
	
	/*
	 	此參數在 LMS2411Flow.jpdl.xml 裡用到
	 */
	public static final String ZINITPARAM = "zInitParam";
	
	@Resource
	RetrialService retrialService;
	
	@Resource
	LMS2401Service lms2401Service;
	
	@Resource
	C241M01EDao c241m01eDao;
	
	@Resource
	C241M01ADao c241m01aDao;
	
	@Resource
	C241A01ADao c241a01aDao;
	
	@Transition(node = "開始", value = "to_編製中_覆審組")
	public void init_flow(FlowInstance instance) {		
		String c241m01a_oid = Util.trim(instance.getId());
		
		C241M01A c241m01a = retrialService.findC241M01A_oid(c241m01a_oid);
		{
			c241m01a.setApprover(null);
			c241m01a.setApproveTime(null);	
		}		
		c241m01aDao.save(c241m01a);
	}
	
	@Transition(node = "decisionE", value = "to_編製中_分行端")
	public void at1_forwardBoss(FlowInstance instance) {		
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String instUserId = user.getUserId();
		
		String c241m01a_oid = Util.trim(instance.getId());
		Timestamp now = CapDate.getCurrentTimestamp();
		C241M01A c241m01a = retrialService.findC241M01A_oid(c241m01a_oid);
		{
			c241m01a.setApprover(instUserId);
			c241m01a.setApproveTime(now);
			c241m01a.setUpDate(now);
			// 首次放行時間
			if(c241m01a.getFirstAccTime() == null){
				c241m01a.setFirstAccTime(now);
			}
		}		
		c241m01aDao.save(c241m01a);
		//---
		lms2401Service.up_to_mis(c241m01a);	
				
	}
	
	@Transition(node = "decisionE", value = "to_已覆核已核定")
	public void toEnd_E(FlowInstance instance) {
		//當931進行 "防杜代辦覆審" ， 覆審主管核可後， 不需再送分行 => 結束
		toEnd(instance);
	}	
	
	@Transition(node = "decisionA", value = "to_已覆核已核定")
	public void toEnd_A(FlowInstance instance) {
		toEnd(instance);
	}	
	
	@Transition(node = "decisionB", value = "to_已覆核已核定")
	public void toEnd_B(FlowInstance instance) {
		toEnd(instance);
	}
	
	@Transition(node = "decisionC", value = "to_已覆核已核定")
	public void toEnd_C(FlowInstance instance) {
		toEnd(instance);
	}
	
	@Transition(node = "decisionD", value = "to_已覆核已核定")
	public void toEnd_D(FlowInstance instance) {
		toEnd(instance);
	}
	
	private void toEnd(FlowInstance instance){
		
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String assgn_userId = Util.trim(instance.getAttribute("assgn_userId")); 
		String instUserId = Util.isNotEmpty(assgn_userId)?assgn_userId:user.getUserId();
		
		String c241m01a_oid = Util.trim(instance.getId());
		Timestamp now = CapDate.getCurrentTimestamp();
		
		C241M01A c241m01a = retrialService.findC241M01A_oid(c241m01a_oid);
		{
			c241m01a.setApprover(instUserId);
			c241m01a.setApproveTime(now);
			c241m01a.setUpDate(now);

            // 首次放行時間
            if(c241m01a.getFirstAccTime() == null){
                c241m01a.setFirstAccTime(now);
            }
		}		
		c241m01aDao.save(c241m01a);
		//---
		lms2401Service.up_to_mis(c241m01a);	
				
	}	
	
	@Override
	public Class<? extends Meta> getDomainClass() {
		return C241M01A.class;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Class getDocStatusEnumClass() {
		return RetrialDocStatusEnum.class;
	}
}