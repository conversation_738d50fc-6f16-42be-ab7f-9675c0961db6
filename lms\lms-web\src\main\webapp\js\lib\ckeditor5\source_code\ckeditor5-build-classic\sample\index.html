<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<title>CKEditor 5 – classic editor build – development sample</title>
	<style>
		body {
			max-width: 800px;
			margin: 20px auto;
		}
	</style>
</head>
<body>

<h1>CKEditor 5 – classic editor build – development sample</h1>

<div id="editor">
	<h2>Sample</h2>

	<p>This is an instance of the <a href="https://ckeditor.com/docs/ckeditor5/latest/installation/getting-started/predefined-builds.html#classic-editor">classic editor build</a>.</p>

	<figure class="image">
		<img src="../tests/manual/sample.jpg" alt="Autumn fields" />
	</figure>

	<p>You can use this sample to validate whether your <a href="https://ckeditor.com/docs/ckeditor5/latest/installation/advanced/alternative-setups/custom-builds.html">custom build</a> works fine.</p>
</div>

<script src="../build/ckeditor.js"></script>
<script>
	ClassicEditor.create( document.querySelector( '#editor' ), {
			htmlSupport: {
				allow: [
					{
						name: /^(span)$/,
						attributes: true,
						classes: true,
						styles: true
					}
				]
			}
		} )
		.then( editor => {
			window.editor = editor;
		} )
		.catch( error => {
			console.error( 'There was a problem initializing the editor.', error );
		} );
</script>

</body>
</html>
