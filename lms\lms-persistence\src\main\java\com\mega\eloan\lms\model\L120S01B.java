/* 
 * L120S01B.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Basic;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.Lob;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import org.apache.bval.constraints.NotEmpty;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.lms.validation.group.Check2;

/** 企金基本資料檔 **/
@NamedEntityGraph(name = "L120S01B-entity-graph", attributeNodes = { @NamedAttributeNode("l120s01a") })
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L120S01B", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "custId", "dupNo" }))
public class L120S01B extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * JOIN條件 L120S01A．關聯檔
	 * 
	 */
	@OneToOne(cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	@JoinColumns({
			@JoinColumn(name = "mainId", referencedColumnName = "mainId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "custId", referencedColumnName = "custId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "dupNo", referencedColumnName = "dupNo", nullable = false, insertable = false, updatable = false) })
	private L120S01A l120s01a;

	public L120S01A getL120s01a() {
		return l120s01a;
	}

	public void setL120s01a(L120S01A l120s01a) {
		this.l120s01a = l120s01a;
	}

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 身分證統編 **/
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/** 成立(改組)日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "ESTDATE", columnDefinition = "DATE")
	private Date estDate;

	/** 國別註冊地 **/
	@Column(name = "NTCODE", length = 2, columnDefinition = "VARCHAR(2)")
	@NotNull(message = "{required.message}", groups = Check2.class)
	@NotEmpty(message = "{required.message}", groups = Check2.class)
	private String ntCode;

	/**
	 * 負責人欄類型
	 * <p/>
	 * 1董事長、2董事、3負責人、9其他（可修改）<br/>
	 * 預設：3負責人
	 */
	@Column(name = "POSTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String posType;

	/**
	 * 負責人欄描述
	 * <p/>
	 * 董事長、董事、負責人、其他（可修改）
	 */
	@Column(name = "POSDSCR", length = 60, columnDefinition = "VARCHAR(60)")
	private String posDscr;

	/** 負責人統編 **/
	@Column(name = "CHAIRMANID", length = 10, columnDefinition = "VARCHAR(10)")
	private String chairmanId;

	/** 負責人統編重複碼 **/
	@Column(name = "CHAIRMANDUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String chairmanDupNo;

	/** 負責人姓名 **/
	@Column(name = "CHAIRMAN", length = 60, columnDefinition = "VARCHAR(60)")
	private String chairman;

	/** 總經理欄描述 **/
	@Column(name = "GMANAGERDSCR", length = 60, columnDefinition = "VARCHAR(60)")
	private String gManagerDscr;

	/** 總經理 **/
	@Column(name = "GMANAGER", length = 120, columnDefinition = "VARCHAR(120)")
	private String gManager;

	/** 登記資本額（幣別） **/
	@Column(name = "RGTCURR", length = 3, columnDefinition = "CHAR(3)")
	private String rgtCurr;

	/** 登記資本額（金額） **/
	@Column(name = "RGTAMT", columnDefinition = "DECIMAL(13,0)")
	private Long rgtAmt;

	/**
	 * 登記資本額（單位）
	 * <p/>
	 * 元：1<br/>
	 * 千元：1000<br/>
	 * 萬元：10000<br/>
	 * 百萬元：1000000<br/>
	 * 仟萬元：10000000
	 */
	@Column(name = "RGTUNIT", columnDefinition = "DECIMAL(13,0)")
	private Integer rgtUnit;

	/**
	 * 實收資本額（幣別）
	 * <p/>
	 * 同登記資本額（幣別）
	 */
	@Column(name = "CPTLCURR", length = 3, columnDefinition = "CHAR(3)")
	private String cptlCurr;

	/** 實收資本額（金額） **/
	@Column(name = "CPTLAMT", columnDefinition = "DECIMAL(13,0)")
	private Long cptlAmt;

	/**
	 * 實收資本額（單位）
	 * <p/>
	 * 同登記資本額（單位）<br/>
	 * 元：1<br/>
	 * 千元：1000<br/>
	 * 萬元：10000<br/>
	 * 百萬元：1000000<br/>
	 * 仟萬元：10000000
	 */
	@Column(name = "CPTLUNIT", columnDefinition = "DECIMAL(13,0)")
	private Integer cptlUnit;

	/**
	 * 主要股東
	 * <p/>
	 * 512個全型字
	 */
	@Column(name = "STOCKHOLDER", length = 1536, columnDefinition = "VARCHAR(1536)")
	private String stockHolder;

	/**
	 * 股票上市上櫃情形
	 * <p/>
	 * 1.上市2.上櫃3.公開發行4.非公開發行5.興櫃
	 */
	@Column(name = "STOCKSTATUS", length = 1, columnDefinition = "VARCHAR(1)")
	private String stockStatus;

	/** 股票上市日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "STOCKDATE", columnDefinition = "DATE")
	private Date stockDate;

	/**
	 * 股票代碼
	 * <p/>
	 * 2012/10/23 Miller Add
	 */
	@Column(name = "STOCKNUM", length = 6, columnDefinition = "VARCHAR(6)")
	private String stockNum;

	/**
	 * 股價日期
	 * <p/>
	 * 2012/10/23 Miller Add
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "STOCKAMTDATE", columnDefinition = "DATE")
	private Date stockAmtDate;

	/**
	 * 股價
	 * <p/>
	 * 2012/10/23 Miller Add
	 */
	@Column(name = "STOCKAMT", columnDefinition = "DECIMAL(10,2)")
	private BigDecimal stockAmt;

	/**
	 * 公司所在地
	 * <p/>
	 * 300個全型字<br/>
	 * ※如OBU，則為註冊地址
	 */
	@Column(name = "CMPADDR", length = 900, columnDefinition = "VARCHAR(900)")
	private String cmpAddr;

	/**
	 * 工廠地址
	 * <p/>
	 * 300個全型字
	 */
	@Column(name = "FACTORYADDR", length = 900, columnDefinition = "VARCHAR(900)")
	private String factoryAddr;

	/** 隸屬集團代號 **/
	@Column(name = "GROUPNO", length = 4, columnDefinition = "VARCHAR(4)")
	private String groupNo;

	/**
	 * 隸屬集團
	 * <p/>
	 * 20個全型字
	 */
	@Column(name = "GROUPNAME", length = 60, columnDefinition = "VARCHAR(60)")
	private String groupName;

	/**
	 * 主要營業項目
	 * <p/>
	 * 900個全型字<br/>
	 * ※授權內案件才顯示此欄位供填寫<br/>
	 * 保留二行，可自行輸入或引自徵信報告中【貳、一般概況－六、主要營業項目】，引入後請自行修改調整內容成二行。
	 */
	@Column(name = "BUSSITEM", length = 4096, columnDefinition = "VARCHAR(4096)")
	private String bussItem;

	/**
	 * 借款人有無赴大陸投資
	 * <p/>
	 * 1.有、2.無、3.不適用
	 */
	@Column(name = "INVMFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String invMFlag;

	/** 赴大陸投資金額（幣別） **/
	@Column(name = "INVMCURR", length = 3, columnDefinition = "CHAR(3)")
	@NotNull(message = "{required.message}", groups = Check.class)
	@NotEmpty(message = "{required.message}", groups = Check.class)
	private String invMCurr;

	/**
	 * 赴大陸投資金額
	 * <p/>
	 * 單位：仟元
	 */
	@Column(name = "INVMAMT", columnDefinition = "DECIMAL(13,0)")
	@NotNull(message = "{required.message}", groups = Check.class)
	@NotEmpty(message = "{required.message}", groups = Check.class)
	private Long invMAmt;

	/**
	 * 經濟部投審會核准金額（幣別）
	 * <p/>
	 * TWD
	 */
	@Column(name = "APRCURR", length = 3, columnDefinition = "CHAR(3)")
	private String aprCurr;

	/**
	 * 經濟部投審會核准金額
	 * <p/>
	 * 單位：仟元
	 */
	@Column(name = "APRAMT", columnDefinition = "DECIMAL(13,0)")
	@NotNull(message = "{required.message}", groups = Check.class)
	@NotEmpty(message = "{required.message}", groups = Check.class)
	private Long aprAmt;

	/**
	 * 登錄營運概況、財務狀況、存放款及外匯往來情形
	 * <p/>
	 * Y/N
	 */
	@Column(name = "RCDFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String rcdFlag;

	/**
	 * 登錄營收獲利情形
	 * <p/>
	 * Y/N
	 */
	@Column(name = "RUNFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String runFlag;

	/**
	 * 登錄主要財務比率
	 * <p/>
	 * Y/N
	 */
	@Column(name = "FINFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String finFlag;

	/**
	 * 大陸投資概況說明
	 * <p/>
	 * 101/01/03修改<br/>
	 * CLOB ( VARCHAR(1536)<br/>
	 * 512個全型字
	 * 
	 * 102/2/18 由 VARCHAR(1536) 放大成 CLOB
	 */
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(name = "INVMDSCR", length = 1048576, columnDefinition = "CLOB")
	private String invMDscr;

	/** 建立人員號碼 **/
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Date updateTime;

	/**
	 * 營收獲利情形幣別
	 * <p/>
	 * 2012-08-21新增<br/>
	 * 資料來源：CES.C120M01C.currFin
	 */
	@Column(name = "RUNCURR", length = 3, columnDefinition = "CHAR(3)")
	private String runCurr;

	/**
	 * 營收獲利情形單位
	 * <p/>
	 * 2012-08-21新增<br/>
	 * 資料來源：CES.C120M01C.amtUnitFin 仟萬元：10000000
	 */
	@Column(name = "RUNUNIT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal runUnit;

	/**
	 * 引入資信簡表種類 0: gaap 1: ifrs
	 */
	@Column(name = "GAAPFLAG", columnDefinition = "CHAR(1)")
	private String gaapFlag;

	/**
	 * 引入資信簡表行業別
	 */
	@Column(name = "TRADETYPE", columnDefinition = "CHAR(1)")
	private String tradeType;

	/**
	 * 引入行業對象別
	 */
	@Column(name = "BUSCODE", columnDefinition = "CHAR(6)")
	private String busCode;

	/**
	 * 引入行業對象別名稱
	 */
	@Column(name = "ECONM", columnDefinition = "CHAR(78)")
	private String ecoNm;

	/**
	 * 引入次產業別
	 */
	@Column(name = "BUSSKIND", columnDefinition = "CHAR(6)")
	private String bussKind;

	/**
	 * 引入次產業別名稱
	 */
	@Column(name = "ECONM07A", columnDefinition = "CHAR(78)")
	private String ecoNm07A;

	/**
	 * 引入客戶類別
	 */
	@Column(name = "CUSTCLASS", columnDefinition = "CHAR(1)")
	private String custClass;

	/**
	 * 引入行業對象別補充說明
	 */
	@Column(name = "BUSMEMO", columnDefinition = "VARCHAR(150)")
	private String busMemo;

	/**
	 * 引入客戶類別補充說明
	 */
	@Column(name = "CCSMEMO", columnDefinition = "VARCHAR(150)")
	private String ccsMemo;

	/**
	 * J-104-0240-001 Web e-Loan授信簽報書與額度明細表增加列示借款戶所屬集團企業代號、名稱與註記。 集團列管註記
	 */
	@Column(name = "GROUPBADFLAG", columnDefinition = "CHAR(2)")
	private String groupBadFlag;

	/**
	 * J-105-0228-001 Web e-Loan企金授信簽報書新增私募基金相關建檔與報表。 申貸戶是否屬私募基金旗下事業
	 */
	@Column(name = "PRIVATEEQUITYFG", columnDefinition = "VARCHAR(1)")
	private String privateEquityFg;

	/**
	 * J-105-0228-001 Web e-Loan企金授信簽報書新增私募基金相關建檔與報表。 私募基金代碼
	 */
	@Column(name = "PRIVATEEQUITYNO", columnDefinition = "VARCHAR(4)")
	private String privateEquityNo;

	/**
	 * J-105-0228-001 Web e-Loan企金授信簽報書新增私募基金相關建檔與報表。 私募基金名稱
	 */
	@Column(name = "PRIVATEEQUITYNAME", columnDefinition = "VARCHAR(120)")
	private String privateEquityName;

	/**
	 * J-105-0228-001 Web e-Loan企金授信簽報書新增私募基金相關建檔與報表。 申貸戶是否屬私募基金旗下事業(變更前)
	 */
	@Column(name = "BFPRIVATEEQUITYFG", columnDefinition = "VARCHAR(1)")
	private String bfPrivateEquityFg;

	/**
	 * J-105-0228-001 Web e-Loan企金授信簽報書新增私募基金相關建檔與報表。 私募基金代碼(變更前)
	 */
	@Column(name = "BFPRIVATEEQUITYNO", columnDefinition = "VARCHAR(4)")
	private String bfPrivateEquityNo;

	/**
	 * J-105-0228-001 Web e-Loan企金授信簽報書新增私募基金相關建檔與報表。 私募基金名稱(變更前)
	 */
	@Column(name = "BFPRIVATEEQUITYNAME", columnDefinition = "VARCHAR(120)")
	private String bfPrivateEquityName;

	/**
	 * J-105-0228-001 Web e-Loan企金授信簽報書新增私募基金相關建檔與報表。 申貸戶統編是否為尚未確認之臨時性MEGA ID
	 */
	@Column(name = "TEMPMEGAIDFG", columnDefinition = "VARCHAR(1)")
	private String tempMegaIdFg;

	/**
	 * J-105-0228-001 Web e-Loan企金授信簽報書新增私募基金相關建檔與報表。 變更前臨時性客戶統編
	 */
	@Column(name = "BFTEMPCUSTID", columnDefinition = "VARCHAR(10)")
	private String bfTempCustId;

	/**
	 * J-105-0228-001 Web e-Loan企金授信簽報書新增私募基金相關建檔與報表。 變更前臨時性客戶重覆序號
	 */
	@Column(name = "BFTEMPDUPNO", columnDefinition = "VARCHAR(1)")
	private String bfTempDupNo;

	/**
	 * J-106-0029-003 洗錢防制-新增實質受益人 實質受益人
	 */
	@Column(name = "BENEFICIARY", columnDefinition = "VARCHAR(1800)")
	private String beneficiary;

	/**
	 * J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
	 */
	@Column(name = "REVIEWBRNO", columnDefinition = "VARCHAR(3)")
	private String reviewBrNo;

	/**
	 * J-106-0232-001 Web e-Loan國內、海外企金授信衍生性金融商品額度明細表新增淨值與額外信用增強
	 */
	@Column(name = "NETSWFT", columnDefinition = "VARCHAR(3)")
	private String netSwft;

	/**
	 * J-106-0232-001 Web e-Loan國內、海外企金授信衍生性金融商品額度明細表新增淨值與額外信用增強
	 */
	@Column(name = "NETAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal netAmt;

	/**
	 * J-106-0232-001 Web e-Loan國內、海外企金授信衍生性金融商品額度明細表新增淨值與額外信用增強
	 */
	@Column(name = "NETAMTUNIT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal netAmtUnit;

	/**
	 * J-107-0070-001 Web e-Loan 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
	 */
	@Column(name = "SENIORMGR", columnDefinition = "VARCHAR(1800)")
	private String seniorMgr;

	/**
	 * J-108-0039_05097_B1001 Web e-Loan
	 * 國內企金授信系統簽報、動審AML頁籤將借戶之「具控制權人」納入應查詢比對黑名單之對象。
	 */
	@Column(name = "CTRLPEO", columnDefinition = "VARCHAR(1800)")
	private String ctrlPeo;

	/**
	 * J-108-0145_05097_B1001 Web e-Loan 國內外企金授信私募基金案件調整實質受益人控管
	 */
	@Column(name = "BENEFICIARYCHK", columnDefinition = "CHAR(1)")
	private String beneficiaryChk;

	/**
	 * J-109-0370 相關評估改版 產銷方式 Production-marketing
	 */
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(name = "PRODMKT", columnDefinition = "CLOB")
	private String prodMkt;

	/**
	 * J-109-0370 相關評估改版 無本行信用評等之簡要說明
	 */
	@Column(name = "NONEGRADE", length = 900, columnDefinition = "VARCHAR(900)")
	private String noneGrade;

	/**
	 * 是否列印 企金授權外使用
	 **/
	@Size(max = 1)
	@Column(name = "ISPRINT", length = 1, columnDefinition = "VARCHAR(1)")
	private String isPrint;

	/**
	 * J-109-0370 相關評估改版 授信戶/集團是否有「已核准未完成簽約」
	 */
	@Size(max = 1)
	@Column(name = "UNFCONFLAG", length = 1, columnDefinition = "VARCHAR(1)")
	private String unfConFlag;

	/**
	 * J-109-0370 相關評估改版 已核准未完成簽約之備註
	 */
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(name = "UNFCONMEMO", columnDefinition = "CLOB")
	private String unfConMemo;

	/**
	 * J-110-0371 新版簽報書_個人 職業
	 */
	@Size(max = 60)
	@Column(name = "JOBTYPE", length = 60, columnDefinition = "VARCHAR(60)")
	private String jobType;

	/**
	 * J-110-0371 新版簽報書_個人 年所得(幣別)
	 */
	@Size(max = 3)
	@Column(name = "PAYCURR", length = 3, columnDefinition = "VARCHAR(3)")
	private String payCurr;

	/**
	 * J-110-0371 新版簽報書_個人 年所得
	 */
	@Digits(integer = 13, fraction = 0, groups = Check.class)
	@Column(name = "PAYAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal payAmt;

	/**
	 * J-110-0371 新版簽報書_個人 票交所查覆資料截止
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "EDUEDATE", columnDefinition = "DATE")
	private Date eDueDate;

	/**
	 * J-110-0371 新版簽報書_個人 退票紀錄
	 */
	@Size(max = 1)
	@Column(name = "RTNCHQ", length = 1, columnDefinition = "VARCHAR(1)")
	private String rtnChq;

	/**
	 * J-110-0371 新版簽報書_個人 拒往紀錄
	 */
	@Size(max = 1)
	@Column(name = "BLACKLIST", length = 1, columnDefinition = "VARCHAR(1)")
	private String blackList;

	/**
	 * 有無警示訊號
	 */
	@Column(name = "HASWARNGRADE", length = 1, columnDefinition = "CHAR(1)")
	private String hasWarnGrade;

	/**
	 * 警示訊號說明
	 */
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(name = "WARNGRADENOTE", columnDefinition = "CLOB")
	private String warnGradeNote;

	/**
	 * 有無主客觀評等調降
	 */
	@Column(name = "HASDOWNGRADEGRADE", length = 1, columnDefinition = "CHAR(1)")
	private String hasDowngradeGrade;

	/**
	 * 評等調降說明
	 */
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(name = "DOWNGRADEGRADENOTE", columnDefinition = "CLOB")
	private String downgradeGradeNote;

	/**
	 * 評等調降狀態
	 */
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(name = "DOWNGRADEGRADESTATUS", columnDefinition = "CLOB")
	private String downgradeGradeStatus;

	/**
	 * J-111-0132_05097_B1001 Web e-Loan企金授信管理系統新增集團企業列管理由 集團列管理由/說明
	 */
	@Size(max = 1500)
	@Column(name = "GROUPREASON", length = 1500, columnDefinition = "VARCHAR(1500)")
	private String groupReason;
	
	/**
	 * J-113-0075_12473_B1001 異常通報案件新增欄位
	 * 已重辦信用評等註記
	 */
	@Column(name = "RERATINGFLAG", length = 1, columnDefinition = "VARCHAR(1)")
	private String reRatingFlag;
	
	/**
	 * J-113-0075_12473_B1001 異常通報案件新增欄位
	 * 未重辦信用評等原因
	 */
	@Column(name = "NOTRERATINGRSN", length = 1, columnDefinition = "VARCHAR(2)")
	private String notReRatingRsn;
	
	/**
	 * J-113-0075_12473_B1001 異常通報案件新增欄位
	 * 未重辦信用評等原因其他敘述
	 */
	@Column(name = "OTHERRSNDESC", length = 1, columnDefinition = "VARCHAR(30)")
	private String otherRsnDesc;

    /** 股價幣別 **/
    @Column(name = "STOCKCURR", length = 3, columnDefinition = "VARCHAR(3)")
    private String stockCurr;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得身分證統編 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定身分證統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得身分證統編重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定身分證統編重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得成立(改組)日期 **/
	public Date getEstDate() {
		return this.estDate;
	}

	/** 設定成立(改組)日期 **/
	public void setEstDate(Date value) {
		this.estDate = value;
	}

	/** 取得國別註冊地 **/
	public String getNtCode() {
		return this.ntCode;
	}

	/** 設定國別註冊地 **/
	public void setNtCode(String value) {
		this.ntCode = value;
	}

	/**
	 * 取得負責人欄類型
	 * <p/>
	 * 1董事長、2董事、3負責人、9其他（可修改）<br/>
	 * 預設：3負責人
	 */
	public String getPosType() {
		return this.posType;
	}

	/**
	 * 設定負責人欄類型
	 * <p/>
	 * 1董事長、2董事、3負責人、9其他（可修改）<br/>
	 * 預設：3負責人
	 **/
	public void setPosType(String value) {
		this.posType = value;
	}

	/**
	 * 取得負責人欄描述
	 * <p/>
	 * 董事長、董事、負責人、其他（可修改）
	 */
	public String getPosDscr() {
		return this.posDscr;
	}

	/**
	 * 設定負責人欄描述
	 * <p/>
	 * 董事長、董事、負責人、其他（可修改）
	 **/
	public void setPosDscr(String value) {
		this.posDscr = value;
	}

	/** 取得負責人統編 **/
	public String getChairmanId() {
		return this.chairmanId;
	}

	/** 設定負責人統編 **/
	public void setChairmanId(String value) {
		this.chairmanId = value;
	}

	/** 取得負責人統編重複碼 **/
	public String getChairmanDupNo() {
		return this.chairmanDupNo;
	}

	/** 設定負責人統編重複碼 **/
	public void setChairmanDupNo(String value) {
		this.chairmanDupNo = value;
	}

	/** 取得負責人姓名 **/
	public String getChairman() {
		return this.chairman;
	}

	/** 設定負責人姓名 **/
	public void setChairman(String value) {
		this.chairman = value;
	}

	/** 取得總經理欄描述 **/
	public String getGManagerDscr() {
		return this.gManagerDscr;
	}

	/** 設定總經理欄描述 **/
	public void setGManagerDscr(String value) {
		this.gManagerDscr = value;
	}

	/** 取得總經理 **/
	public String getGManager() {
		return this.gManager;
	}

	/** 設定總經理 **/
	public void setGManager(String value) {
		this.gManager = value;
	}

	/** 取得登記資本額（幣別） **/
	public String getRgtCurr() {
		return this.rgtCurr;
	}

	/** 設定登記資本額（幣別） **/
	public void setRgtCurr(String value) {
		this.rgtCurr = value;
	}

	/** 取得登記資本額（金額） **/
	public Long getRgtAmt() {
		return this.rgtAmt;
	}

	/** 設定登記資本額（金額） **/
	public void setRgtAmt(Long value) {
		this.rgtAmt = value;
	}

	/**
	 * 取得登記資本額（單位）
	 * <p/>
	 * 元：1<br/>
	 * 千元：1000<br/>
	 * 萬元：10000<br/>
	 * 百萬元：1000000<br/>
	 * 仟萬元：10000000
	 */
	public Integer getRgtUnit() {
		return this.rgtUnit;
	}

	/**
	 * 設定登記資本額（單位）
	 * <p/>
	 * 元：1<br/>
	 * 千元：1000<br/>
	 * 萬元：10000<br/>
	 * 百萬元：1000000<br/>
	 * 仟萬元：10000000
	 **/
	public void setRgtUnit(Integer value) {
		this.rgtUnit = value;
	}

	/**
	 * 取得實收資本額（幣別）
	 * <p/>
	 * 同登記資本額（幣別）
	 */
	public String getCptlCurr() {
		return this.cptlCurr;
	}

	/**
	 * 設定實收資本額（幣別）
	 * <p/>
	 * 同登記資本額（幣別）
	 **/
	public void setCptlCurr(String value) {
		this.cptlCurr = value;
	}

	/** 取得實收資本額（金額） **/
	public Long getCptlAmt() {
		return this.cptlAmt;
	}

	/** 設定實收資本額（金額） **/
	public void setCptlAmt(Long value) {
		this.cptlAmt = value;
	}

	/**
	 * 取得實收資本額（單位）
	 * <p/>
	 * 同登記資本額（單位）<br/>
	 * 元：1<br/>
	 * 千元：1000<br/>
	 * 萬元：10000<br/>
	 * 百萬元：1000000<br/>
	 * 仟萬元：10000000
	 */
	public Integer getCptlUnit() {
		return this.cptlUnit;
	}

	/**
	 * 設定實收資本額（單位）
	 * <p/>
	 * 同登記資本額（單位）<br/>
	 * 元：1<br/>
	 * 千元：1000<br/>
	 * 萬元：10000<br/>
	 * 百萬元：1000000<br/>
	 * 仟萬元：10000000
	 **/
	public void setCptlUnit(Integer value) {
		this.cptlUnit = value;
	}

	/**
	 * 取得主要股東
	 * <p/>
	 * 128個全型字
	 */
	public String getStockHolder() {
		return this.stockHolder;
	}

	/**
	 * 設定主要股東
	 * <p/>
	 * 128個全型字
	 **/
	public void setStockHolder(String value) {
		this.stockHolder = value;
	}

	/**
	 * 取得股票上市上櫃情形
	 * <p/>
	 * 1.上市2.上櫃3.公開發行4.非公開發行5.興櫃
	 */
	public String getStockStatus() {
		return this.stockStatus;
	}

	/**
	 * 設定股票上市上櫃情形
	 * <p/>
	 * 1.上市2.上櫃3.公開發行4.非公開發行5.興櫃
	 **/
	public void setStockStatus(String value) {
		this.stockStatus = value;
	}

	/** 取得股票上市日期 **/
	public Date getStockDate() {
		return this.stockDate;
	}

	/** 設定股票上市日期 **/
	public void setStockDate(Date value) {
		this.stockDate = value;
	}

	/**
	 * 取得股票代碼
	 * <p/>
	 * 2012/10/23 Miller Add
	 */
	public String getStockNum() {
		return this.stockNum;
	}

	/**
	 * 設定股票代碼
	 * <p/>
	 * 2012/10/23 Miller Add
	 **/
	public void setStockNum(String value) {
		this.stockNum = value;
	}

	/**
	 * 取得股價日期
	 * <p/>
	 * 2012/10/23 Miller Add
	 */
	public Date getStockAmtDate() {
		return this.stockAmtDate;
	}

	/**
	 * 設定股價日期
	 * <p/>
	 * 2012/10/23 Miller Add
	 **/
	public void setStockAmtDate(Date value) {
		this.stockAmtDate = value;
	}

	/**
	 * 取得股價
	 * <p/>
	 * 2012/10/23 Miller Add
	 */
	public BigDecimal getStockAmt() {
		return this.stockAmt;
	}

	/**
	 * 設定股價
	 * <p/>
	 * 2012/10/23 Miller Add
	 **/
	public void setStockAmt(BigDecimal value) {
		this.stockAmt = value;
	}

	/**
	 * 取得公司所在地
	 * <p/>
	 * 64個全型字<br/>
	 * ※如OBU，則為註冊地址
	 */
	public String getCmpAddr() {
		return this.cmpAddr;
	}

	/**
	 * 設定公司所在地
	 * <p/>
	 * 64個全型字<br/>
	 * ※如OBU，則為註冊地址
	 **/
	public void setCmpAddr(String value) {
		this.cmpAddr = value;
	}

	/**
	 * 取得工廠地址
	 * <p/>
	 * 64個全型字
	 */
	public String getFactoryAddr() {
		return this.factoryAddr;
	}

	/**
	 * 設定工廠地址
	 * <p/>
	 * 64個全型字
	 **/
	public void setFactoryAddr(String value) {
		this.factoryAddr = value;
	}

	/** 取得隸屬集團代號 **/
	public String getGroupNo() {
		return this.groupNo;
	}

	/** 設定隸屬集團代號 **/
	public void setGroupNo(String value) {
		this.groupNo = value;
	}

	/**
	 * 取得隸屬集團
	 * <p/>
	 * 20個全型字
	 */
	public String getGroupName() {
		return this.groupName;
	}

	/**
	 * 設定隸屬集團
	 * <p/>
	 * 20個全型字
	 **/
	public void setGroupName(String value) {
		this.groupName = value;
	}

	/**
	 * 取得主要營業項目
	 * <p/>
	 * 128個全型字<br/>
	 * ※授權內案件才顯示此欄位供填寫<br/>
	 * 保留二行，可自行輸入或引自徵信報告中【貳、一般概況－六、主要營業項目】，引入後請自行修改調整內容成二行。
	 */
	public String getBussItem() {
		return this.bussItem;
	}

	/**
	 * 設定主要營業項目
	 * <p/>
	 * 128個全型字<br/>
	 * ※授權內案件才顯示此欄位供填寫<br/>
	 * 保留二行，可自行輸入或引自徵信報告中【貳、一般概況－六、主要營業項目】，引入後請自行修改調整內容成二行。
	 **/
	public void setBussItem(String value) {
		this.bussItem = value;
	}

	/**
	 * 取得借款人有無赴大陸投資
	 * <p/>
	 * 1.有、2.無、3.不適用
	 */
	public String getInvMFlag() {
		return this.invMFlag;
	}

	/**
	 * 設定借款人有無赴大陸投資
	 * <p/>
	 * 1.有、2.無、3.不適用
	 **/
	public void setInvMFlag(String value) {
		this.invMFlag = value;
	}

	/** 取得赴大陸投資金額（幣別） **/
	public String getInvMCurr() {
		return this.invMCurr;
	}

	/** 設定赴大陸投資金額（幣別） **/
	public void setInvMCurr(String value) {
		this.invMCurr = value;
	}

	/**
	 * 取得赴大陸投資金額
	 * <p/>
	 * 單位：仟元
	 */
	public Long getInvMAmt() {
		return this.invMAmt;
	}

	/**
	 * 設定赴大陸投資金額
	 * <p/>
	 * 單位：仟元
	 **/
	public void setInvMAmt(Long value) {
		this.invMAmt = value;
	}

	/**
	 * 取得經濟部投審會核准金額（幣別）
	 * <p/>
	 * TWD
	 */
	public String getAprCurr() {
		return this.aprCurr;
	}

	/**
	 * 設定經濟部投審會核准金額（幣別）
	 * <p/>
	 * TWD
	 **/
	public void setAprCurr(String value) {
		this.aprCurr = value;
	}

	/**
	 * 取得經濟部投審會核准金額
	 * <p/>
	 * 單位：仟元
	 */
	public Long getAprAmt() {
		return this.aprAmt;
	}

	/**
	 * 設定經濟部投審會核准金額
	 * <p/>
	 * 單位：仟元
	 **/
	public void setAprAmt(Long value) {
		this.aprAmt = value;
	}

	/**
	 * 取得登錄營運概況、財務狀況、存放款及外匯往來情形
	 * <p/>
	 * Y/N
	 */
	public String getRcdFlag() {
		return this.rcdFlag;
	}

	/**
	 * 設定登錄營運概況、財務狀況、存放款及外匯往來情形
	 * <p/>
	 * Y/N
	 **/
	public void setRcdFlag(String value) {
		this.rcdFlag = value;
	}

	/**
	 * 取得登錄營收獲利情形
	 * <p/>
	 * Y/N
	 */
	public String getRunFlag() {
		return this.runFlag;
	}

	/**
	 * 設定登錄營收獲利情形
	 * <p/>
	 * Y/N
	 **/
	public void setRunFlag(String value) {
		this.runFlag = value;
	}

	/**
	 * 取得登錄主要財務比率
	 * <p/>
	 * Y/N
	 */
	public String getFinFlag() {
		return this.finFlag;
	}

	/**
	 * 設定登錄主要財務比率
	 * <p/>
	 * Y/N
	 **/
	public void setFinFlag(String value) {
		this.finFlag = value;
	}

	/**
	 * 取得大陸投資概況說明
	 * <p/>
	 * 101/01/03修改<br/>
	 * CLOB ( VARCHAR(1536)<br/>
	 * 512個全型字
	 */
	public String getInvMDscr() {
		return this.invMDscr;
	}

	/**
	 * 設定大陸投資概況說明
	 * <p/>
	 * 101/01/03修改<br/>
	 * CLOB ( VARCHAR(1536)<br/>
	 * 512個全型字
	 **/
	public void setInvMDscr(String value) {
		this.invMDscr = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}

	/**
	 * 取得營收獲利情形幣別
	 * <p/>
	 * 2012-08-21新增<br/>
	 * 資料來源：CES.C120M01C.currFin
	 */
	public String getRunCurr() {
		return this.runCurr;
	}

	/**
	 * 設定營收獲利情形幣別
	 * <p/>
	 * 2012-08-21新增<br/>
	 * 資料來源：CES.C120M01C.currFin
	 **/
	public void setRunCurr(String value) {
		this.runCurr = value;
	}

	/**
	 * 取得營收獲利情形單位
	 * <p/>
	 * 2012-08-21新增<br/>
	 * 資料來源：CES.C120M01C.amtUnitFin
	 */
	public BigDecimal getRunUnit() {
		return this.runUnit;
	}

	/**
	 * 設定營收獲利情形單位
	 * <p/>
	 * 2012-08-21新增<br/>
	 * 資料來源：CES.C120M01C.amtUnitFin
	 **/
	public void setRunUnit(BigDecimal value) {
		this.runUnit = value;
	}

	/**
	 * 設定引入資信簡表種類
	 */
	public String getGaapFlag() {
		return gaapFlag;
	}

	/**
	 * 取得引入資信簡表種類
	 */
	public void setGaapFlag(String gaapFlag) {
		this.gaapFlag = gaapFlag;
	}

	/**
	 * 設定引入資信簡表行業別
	 */
	public String getTradeType() {
		return tradeType;
	}

	/**
	 * 取得引入資信簡表行業別
	 */
	public void setTradeType(String tradeType) {
		this.tradeType = tradeType;
	}

	/** 建構子 **/
	public L120S01B() {
	}

	/**
	 * 建構子
	 * 
	 * @param mainId
	 *            (文件編號)
	 * @param custId
	 *            (身分證統編)
	 * @param dupNo
	 *            (身分證統編重複碼)
	 **/
	public L120S01B(String mainId, String custId, String dupNo) {
		this.mainId = mainId;
		this.custId = custId;
		this.dupNo = dupNo;
	}

	/**
	 * 設定行業對象別
	 * 
	 * @param busCode
	 */
	public void setBusCode(String busCode) {
		this.busCode = busCode;
	}

	/**
	 * 取得行業對象別
	 * 
	 * @return
	 */
	public String getBusCode() {
		return busCode;
	}

	/**
	 * 設定行業對象別名稱
	 * 
	 * @param ecoNm
	 */
	public void setEcoNm(String ecoNm) {
		this.ecoNm = ecoNm;
	}

	/**
	 * 取得行業對象別名稱
	 * 
	 * @return
	 */
	public String getEcoNm() {
		return ecoNm;
	}

	/**
	 * 設定次產業別
	 * 
	 * @param bussKind
	 */
	public void setBussKind(String bussKind) {
		this.bussKind = bussKind;
	}

	/**
	 * 取得次產業別
	 * 
	 * @return
	 */
	public String getBussKind() {
		return bussKind;
	}

	/**
	 * 設定次產業別名稱
	 * 
	 * @param ecoNm07A
	 */
	public void setEcoNm07A(String ecoNm07A) {
		this.ecoNm07A = ecoNm07A;
	}

	/**
	 * 取得次產業別名稱
	 * 
	 * @return
	 */
	public String getEcoNm07A() {
		return ecoNm07A;
	}

	/**
	 * 設定客戶類別
	 * 
	 * @param custClass
	 */
	public void setCustClass(String custClass) {
		this.custClass = custClass;
	}

	/**
	 * 取得客戶類別
	 * 
	 * @return
	 */
	public String getCustClass() {
		return custClass;
	}

	/**
	 * 設定行業對象別補充說明
	 * 
	 * @param busMemo
	 */
	public void setBusMemo(String busMemo) {
		this.busMemo = busMemo;
	}

	/**
	 * 取得行業對象別補充說明
	 * 
	 * @return
	 */
	public String getBusMemo() {
		return busMemo;
	}

	/**
	 * 設定客戶類別補充說明
	 * 
	 * @param ccsMemo
	 */
	public void setCcsMemo(String ccsMemo) {
		this.ccsMemo = ccsMemo;
	}

	/**
	 * 取得客戶類別補充說明
	 * 
	 * @return
	 */
	public String getCcsMemo() {
		return ccsMemo;
	}

	/**
	 * 設定集團列管註記
	 */
	public void setGroupBadFlag(String groupBadFlag) {
		this.groupBadFlag = groupBadFlag;
	}

	/**
	 * 取得集團列管註記
	 */
	public String getGroupBadFlag() {
		return groupBadFlag;
	}

	public void setPrivateEquityFg(String privateEquityFg) {
		this.privateEquityFg = privateEquityFg;
	}

	public String getPrivateEquityFg() {
		return privateEquityFg;
	}

	public void setPrivateEquityNo(String privateEquityNo) {
		this.privateEquityNo = privateEquityNo;
	}

	public String getPrivateEquityNo() {
		return privateEquityNo;
	}

	public void setPrivateEquityName(String privateEquityName) {
		this.privateEquityName = privateEquityName;
	}

	public String getPrivateEquityName() {
		return privateEquityName;
	}

	public void setBfPrivateEquityFg(String bfPrivateEquityFg) {
		this.bfPrivateEquityFg = bfPrivateEquityFg;
	}

	public String getBfPrivateEquityFg() {
		return bfPrivateEquityFg;
	}

	public void setBfPrivateEquityNo(String bfPrivateEquityNo) {
		this.bfPrivateEquityNo = bfPrivateEquityNo;
	}

	public String getBfPrivateEquityNo() {
		return bfPrivateEquityNo;
	}

	public void setBfPrivateEquityName(String bfPrivateEquityName) {
		this.bfPrivateEquityName = bfPrivateEquityName;
	}

	public String getBfPrivateEquityName() {
		return bfPrivateEquityName;
	}

	public void setTempMegaIdFg(String tempMegaIdFg) {
		this.tempMegaIdFg = tempMegaIdFg;
	}

	public String getTempMegaIdFg() {
		return tempMegaIdFg;
	}

	public void setBfTempCustId(String bfTempCustId) {
		this.bfTempCustId = bfTempCustId;
	}

	public String getBfTempCustId() {
		return bfTempCustId;
	}

	public void setBfTempDupNo(String bfTempDupNo) {
		this.bfTempDupNo = bfTempDupNo;
	}

	public String getBfTempDupNo() {
		return bfTempDupNo;
	}

	/**
	 * 設定實質受益人
	 */
	public void setBeneficiary(String beneficiary) {
		this.beneficiary = beneficiary;
	}

	/**
	 * 取得實質受益人
	 */
	public String getBeneficiary() {
		return beneficiary;
	}

	/**
	 * 設定實地覆審分行
	 * 
	 * @param reviewBrNo
	 */
	public void setReviewBrNo(String reviewBrNo) {
		this.reviewBrNo = reviewBrNo;
	}

	/**
	 * 取得實地覆審分行
	 * 
	 * @return
	 */
	public String getReviewBrNo() {
		return reviewBrNo;
	}

	/**
	 * 設定淨值幣別
	 * 
	 * @return
	 */
	public void setNetSwft(String netSwft) {
		this.netSwft = netSwft;
	}

	/**
	 * 取得淨值幣別
	 * 
	 * @return
	 */
	public String getNetSwft() {
		return netSwft;
	}

	/**
	 * 設定淨值
	 * 
	 * @return
	 */
	public void setNetAmt(BigDecimal netAmt) {
		this.netAmt = netAmt;
	}

	/**
	 * 取得淨值
	 * 
	 * @return
	 */
	public BigDecimal getNetAmt() {
		return netAmt;
	}

	/**
	 * 設定淨值單位
	 * 
	 * @return
	 */
	public void setNetAmtUnit(BigDecimal netAmtUnit) {
		this.netAmtUnit = netAmtUnit;
	}

	/**
	 * 取得淨值單位
	 * 
	 * @return
	 */
	public BigDecimal getNetAmtUnit() {
		return netAmtUnit;
	}

	/**
	 * 設定高階管理人員
	 * 
	 * @return
	 */
	public void setSeniorMgr(String seniorMgr) {
		this.seniorMgr = seniorMgr;
	}

	/**
	 * 取得高階管理人員
	 * 
	 * @return
	 */
	public String getSeniorMgr() {
		return seniorMgr;
	}

	/**
	 * 設定具控制權人
	 * 
	 * @return
	 */
	public void setCtrlPeo(String ctrlPeo) {
		this.ctrlPeo = ctrlPeo;
	}

	/**
	 * 取得具控制權人
	 * 
	 * @return
	 */
	public String getCtrlPeo() {
		return ctrlPeo;
	}

	/**
	 * 設定實質受益人辨識完成註記
	 * 
	 * @return
	 */
	public void setBeneficiaryChk(String beneficiaryChk) {
		this.beneficiaryChk = beneficiaryChk;
	}

	/**
	 * 取得實質受益人辨識完成註記
	 * 
	 * @return
	 */
	public String getBeneficiaryChk() {
		return beneficiaryChk;
	}

	/**
	 * 設定產銷方式
	 * 
	 * @return
	 */
	public void setProdMkt(String prodMkt) {
		this.prodMkt = prodMkt;
	}

	/**
	 * 取得產銷方式
	 * 
	 * @return
	 */
	public String getProdMkt() {
		return prodMkt;
	}

	/**
	 * 取得無本行信用評等之簡要說明
	 */
	public String getNoneGrade() {
		return this.noneGrade;
	}

	/**
	 * 設定無本行信用評等之簡要說明
	 **/
	public void setNoneGrade(String value) {
		this.noneGrade = value;
	}

	/** 取得是否列印 **/
	public String getIsPrint() {
		return this.isPrint;
	}

	/** 設定是否列印 **/
	public void setIsPrint(String value) {
		this.isPrint = value;
	}

	/** 取得授信戶/集團是否有「已核准未完成簽約」 **/
	public String getUnfConFlag() {
		return this.unfConFlag;
	}

	/** 設定授信戶/集團是否有「已核准未完成簽約」 **/
	public void setUnfConFlag(String value) {
		this.unfConFlag = value;
	}

	/** 取得已核准未完成簽約之備註 **/
	public String getUnfConMemo() {
		return this.unfConMemo;
	}

	/** 設定已核准未完成簽約之備註 **/
	public void setUnfConMemo(String value) {
		this.unfConMemo = value;
	}

	/** 取得職業 **/
	public String getJobType() {
		return this.jobType;
	}

	/** 設定職業 **/
	public void setJobType(String value) {
		this.jobType = value;
	}

	/** 取得年所得(幣別) **/
	public String getPayCurr() {
		return this.payCurr;
	}

	/** 設定年所得(幣別) **/
	public void setPayCurr(String value) {
		this.payCurr = value;
	}

	/** 取得年所得 **/
	public BigDecimal getPayAmt() {
		return payAmt;
	}

	/** 設定年所得 **/
	public void setPayAmt(BigDecimal value) {
		this.payAmt = value;
	}

	/** 取得票交所查覆資料截止日期 **/
	public Date getEDueDate() {
		return this.eDueDate;
	}

	/** 設定票交所查覆資料截止日期 **/
	public void setEDueDate(Date value) {
		this.eDueDate = value;
	}

	/** 取得退票紀錄 **/
	public String getRtnChq() {
		return this.rtnChq;
	}

	/** 設定退票紀錄 **/
	public void setRtnChq(String value) {
		this.rtnChq = value;
	}

	/** 取得拒往紀錄 **/
	public String getBlackList() {
		return this.blackList;
	}

	/** 設定拒往紀錄 **/
	public void setBlackList(String value) {
		this.blackList = value;
	}

	/** 設定有無警示訊號 **/
	public void setHasWarnGrade(String hasWarnGrade) {
		this.hasWarnGrade = hasWarnGrade;
	}

	/** 取得有無警示訊號 **/
	public String getHasWarnGrade() {
		return hasWarnGrade;
	}

	/** 設定警示訊號說明 **/
	public void setWarnGradeNote(String warnGradeNote) {
		this.warnGradeNote = warnGradeNote;
	}

	/** 取得警示訊號說明 **/
	public String getWarnGradeNote() {
		return warnGradeNote;
	}

	/** 設定有無主客觀評等調降 **/
	public void setHasDowngradeGrade(String hasDowngradeGrade) {
		this.hasDowngradeGrade = hasDowngradeGrade;
	}

	/** 取得有無主客觀評等調降 **/
	public String getHasDowngradeGrade() {
		return hasDowngradeGrade;
	}

	/** 設定評等調降說明 **/
	public void setDowngradeGradeNote(String downgradeGradeNote) {
		this.downgradeGradeNote = downgradeGradeNote;
	}

	/** 取得評等調降說明 **/
	public String getDowngradeGradeNote() {
		return downgradeGradeNote;
	}

	/** 設定評等調降狀態 **/
	public void setDowngradeGradeStatus(String downgradeGradeStatus) {
		this.downgradeGradeStatus = downgradeGradeStatus;
	}

	/** 取得評等調降狀態 **/
	public String getDowngradeGradeStatus() {
		return downgradeGradeStatus;
	}

	/** 設定集團列管理由/說明 **/
	public void setGroupReason(String groupReason) {
		this.groupReason = groupReason;
	}

	/** 取得集團列管理由/說明 **/
	public String getGroupReason() {
		return groupReason;
	}

	public String getReRatingFlag() {
		return reRatingFlag;
	}

	public void setReRatingFlag(String reRatingFlag) {
		this.reRatingFlag = reRatingFlag;
	}

	public String getNotReRatingRsn() {
		return notReRatingRsn;
	}

	public void setNotReRatingRsn(String notReRatingRsn) {
		this.notReRatingRsn = notReRatingRsn;
	}

	public String getOtherRsnDesc() {
		return otherRsnDesc;
	}

	public void setOtherRsnDesc(String otherRsnDesc) {
		this.otherRsnDesc = otherRsnDesc;
	}

	/** 取得股價幣別 **/
	public String getStockCurr() {
		return this.stockCurr;
	}

	/** 設定股價幣別 **/
	public void setStockCurr(String value) {
		this.stockCurr = value;
	}
	
}
