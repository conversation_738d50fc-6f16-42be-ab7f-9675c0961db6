/* 
 * MicroEntService.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.service;

import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.service.ICapService;

import com.mega.eloan.lms.model.L120S01B;
import com.mega.eloan.lms.model.L120S04C;
import com.mega.eloan.lms.model.L120S10A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01S;
import com.mega.eloan.lms.model.L164S01A;

/**
 * <pre>
 * BY 專案共用Service
 * </pre>
 * 
 * @since 2019/09
 * <AUTHOR>
 * @version <ul>
 *          <li>2019/09,009301,new
 *          </ul>
 */
public interface MicroEntService extends ICapService {

	public <T extends GenericBean> T findModelByMainId(Class clazz,
			String mainId);

	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId);

	public L120S10A findL120s10aByOid(String oid);

	public List<L120S10A> findL120s10aByMainId(String mainId);

	public void deleteListL120s10a(List<L120S10A> list);

	public List<L140M01A> findL140m01aListByL120m01cMainId(String mainId,
			String caseType);

	public L120S01B findL120s01bByUniqueKey(String mainId, String custId,
			String dupNo);

	public void reSetL120S10A(String mainId, String custId, String dupNo,
			String custName, String relation);

	public List<L120S10A> findListL120s10aByCustId(String mainId,
			String custId, String dupNo);

	public List<L120S10A> findListL120s10aByCustName(String mainId,
			String custName);

	public L164S01A findL164s01aByUniqueKey(String mainId, String custId,
			String dupNo);

	public L120S10A importJ10(L120S10A l120s10a,
			TreeMap<String, String> J10_BREACH_MAP,
			TreeMap<String, String> J10_PERCENTILE_MAP,
			List<Map<String, Object>> cesJ10List);

	public void deleteL120s10as(String mainId);

	public void deleteL120s10b(String mainId);

	public List<L140M01S> findL140m01sByMainIdType(String mainId, String type);

	public List<L120S04C> findL120s04cByMainIdDocKind(String mainId,
			String[] docKind);

	public void chkListFull(String mainId) throws CapException;
}