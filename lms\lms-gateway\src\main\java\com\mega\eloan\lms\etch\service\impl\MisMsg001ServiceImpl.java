/* 
 * MisMsg001ServiceImpl.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.etch.service.impl;

import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.etch.service.MisMsg001Service;

/**
 * <pre>
 * MIS申貸戶退票、拒往紀錄查詢
 * </pre>
 * 
 * @since 2011年9月30日
 * <AUTHOR>
 * @version <ul>
 *          <li>2011年9月30日,tammyChen,new
 *          </ul>
 */
@Service
public class MisMsg001ServiceImpl extends AbstractETchJdbc implements
		MisMsg001Service {
 	
	/**
	 * 取得申貸戶退票、拒往紀錄
	 * 
	 * @param custId
	 *            統編
	 * @return Map<String, Object>
	 */
	@Override
	public Map<String, Object> findById(String custId) {
		return getJdbc().queryForMap("MSG001.findById",
				new String[] { custId });
	}//;
	
	/**
	 * 查詢申貸戶是否有退票/拒往資訊
	 * 
	 * @param custId
	 *            統編
	 * @return Map<String, Object>
	 */
	public Map<String, Object> findCntAmtById(String custId){
		return getJdbc().queryForMap("MSG001.findCntAmtById",
				new String[] { custId });
	}

	@Override
	public Map<String, Object> findDetailAndBanBygId(String gId) {
		return getJdbc().queryForMap("MSG001.findDetailAndBanBygId",
				new String[] { gId });
	}	
}
