package com.mega.eloan.lms.fms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;

@Controller@RequestMapping(path = "/fms/lms9101v00")
public class LMS9101V00Page extends AbstractEloanInnerView {

	public LMS9101V00Page() {
		super();
	}

	@Override
	public void  execute(ModelMap model, PageParameters params) {
		// UPGRADE: 後續沒有用到就可以刪掉
		// add(new Label("_buttonPanel"));
		renderJsI18N(LMS9101V00Page.class);
	}

	public String[] getJavascriptPath() {
		return new String[] { "pagejs/fms/LMS9101V00Page.js" };
	}
}
