package com.mega.eloan.lms.mfaloan.service;

import java.util.List;
import java.util.Map;

import com.mega.eloan.lms.mfaloan.bean.ELF486;

/**
 * <pre>
 * 價金履約保證覆審資料
 * </pre>
 * 
 * @since 2014/4/24
 * <AUTHOR>
 * @version <ul>
 *          <li>2014/4/24,EL08034,new
 *          </ul>
 */
public interface MisELF486Service {
	public ELF486 findByPk(String elf486_branch, String elf486_lc_no, String elf486_cntrno, String elf486_overdue);
	public List<Map<String, Object>> sel_already_retrial_cnt(String elf486_branch, String elf486_cntrno, String elf486_s_date_s, String elf486_s_date_e);
	
}
