
package com.mega.eloan.lms.lns.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import tw.com.jcs.auth.AuthType;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;


/**<pre>
 * 授信簽報書 海外_總行提會待登錄
 * 特殊分行提審計委員會
 * </pre>
 */
@Controller
@RequestMapping("/lms/lms1201v29")
public class LMS1201V29Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String unitNo = user.getUnitNo();
		//設定文件狀態(交易代碼)
		if(LMSUtil.isSpecialBranch(unitNo)){
			setGridViewStatus(CreditDocStatusEnum.特殊分行提審計委員會);//特殊分行提審計委員會
		}else{
			setGridViewStatus(CreditDocStatusEnum.海外_提會待登錄);
		}
		if (this.getAuth(AuthType.Modify)) {
			// 加上Button
			// 經辦權限時要顯示的按鈕...
//			add(new CreditButtonPanel("_buttonPanel", null,CreditButtonEnum.View));
			//,CreditButtonEnum.Create  授審正大科長通知先將「產生」功能拿掉, 等後續再提
			addToButtonPanel(model, LmsButtonEnum.View, LmsButtonEnum.Login4);
		} else {
			// 否則需要顯示的按鈕
			// 主管權限時要顯示的按鈕...
			addToButtonPanel(model, LmsButtonEnum.View);
		}		
		//套用哪個i18N檔案
		renderJsI18N(LMS1201V01Page.class);
		renderJsI18N(LMS1201V29Page.class);
		
		model.addAttribute("loadScript", "loadScript('pagejs/lns/LMS1201V01Page');");
	}// ;

}
