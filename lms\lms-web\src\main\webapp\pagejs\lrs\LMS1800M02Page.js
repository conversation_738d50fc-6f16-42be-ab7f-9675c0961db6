var _handler = "lms1800formhandler";
var grid_p = "grid_printL140M01A";
var grid_p_h = 150;
var print_url = "../../simple/FileProcessingService"; 
$(document).ready(function(){
	var tabForm = $("#tabForm");
	var btnPanel = $("#buttonPanel");
	
	$.form.init({
		formHandler:_handler, 
		formAction:'queryM02', 
		loadSuccess:function(json){
			
			// 控制頁面 Read/Write
			if(!$("#buttonPanel").find("#btnSave").is("button")) {
				tabForm.lockDoc();
			}else{
				if( $("input:visible").length>0 || $("textarea:visible").length>0){
					
				}else{
					btnPanel.find("#btnSave").addClass(" ui-state-disabled ").attr("disabled", "true");
				}
			}
			tabForm.injectData(json);	
			
			//J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
			if(json.ctlType == "B"){
				$(".showCtlTypeB").show();
				$(".showCtlTypeA").hide();
			}else{
				$(".showCtlTypeA").show();
				$(".showCtlTypeB").hide();
			}
			
	}});

	$("#"+grid_p).iGrid({
        handler: 'lms1800gridhandler',        
        height: grid_p_h,
        postData: {
        	parStrArr: '',
            formAction: "queryL140M01A"
        },
        needPager: false,        
        shrinkToFit: false,
        multiselect: true,        
        colModel: [
          {
            // "借款人統編",
            colHeader: i18n.lms1800m01['grid.custId'],name:'custId',align: "left",width: 85,sortable: false
          }, {
            // "借款人姓名",
            colHeader: i18n.lms1800m01['L180M01B.elfCName'],name: 'cName',align: "left",width: 130,sortable: false
          },{//"類別"
            colHeader: i18n.lms1800m01['grid.cntrNoType'], name:'rptNoDesc',align: "left",width: 85,sortable: false
          }, {//"額度序號",
            colHeader: i18n.lms1800m01['grid.cntrNo'], name:'cntrNo',align: "left",width: 85,sortable: false
          }, { name: 'rptNo', hidden: true}
          , { name: 'oid', hidden: true}  
          , { name: 'cntrCustid', hidden: true }
          , { name: 'cntrDupno', hidden: true }
        ] 
    });
	
	btnPanel.find("#btnSave").click(function(){
		var tabForm = $("#tabForm");
		if(tabForm.valid()){
			$.ajax({
	            type: "POST",
	            handler: _handler,
	            data:$.extend( {
	            	formAction: "saveM02",
	                mainOid: $("#mainOid").val()
	                }, 
	                tabForm.serializeData()
	            ),                
	            success: function(json){
	            	tabForm.injectData(json);
	            	//更新 opener 的 Grid
	                CommonAPI.triggerOpener("gridview", "reloadGrid");
	                API.showMessage(i18n.def.saveSuccess);
	            }
	        });
		}
	}).end().find("#btn_printLatestL140M01A").click(function(){
		printLatestL140M01A( $("#mainOid").val() );
    });    
	
});

function printLatestL140M01A(oids){
	$.ajax({ type: "POST", handler: _handler, data:{ formAction: "getPrintL140M01AParam", 'oids': oids},
        success: function(json){
        	var my_dfd = $.Deferred();    	
        	if(json.notProc){
        		API.showPopMessage("", json.notProc,function(){
        			my_dfd.resolve();
  				});
        	}else{
        		my_dfd.resolve();
        	}

        	my_dfd.done(function(){
        		if(json.parStr){
            		$("#"+grid_p).jqGrid("setGridParam", {
                        postData: {
                        	parStrArr:json.parStr
                        },
                        search: true
                    }).trigger("reloadGrid");
            		
            		$("#div_printL140M01A").thickbox({ // 使用選取的內容進行彈窗
         		       title: "", width: 550, height: grid_p_h+140, align: "center", valign: "bottom", modal: false, i18n: i18n.def,
         	           buttons: {
         	               "sure": function(){
         	            	  var rowId_arr = $("#"+grid_p).getGridParam('selarrrow');
         	            	  var oid_arr = [];
         	            	 	for (var i = 0; i < rowId_arr.length; i++) {
         	         			var data = $("#"+grid_p).getRowData(rowId_arr[i]);
         	         			oid_arr.push(data.rptNo+"^"+data.oid+"^"+data.cntrCustid+"^"+data.cntrDupno+"^"+data.cntrNo);    			
         	            	  }
         	            	  if(oid_arr.length==0){   	 		
    	                	 	API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
    	             	 		return;
    	             	 	  }	
         	            	 
         	            	  $.form.submit({
         	                     url: print_url,
         	                     target: "_blank",
         	                     data: {
         	                         'rptOid': oid_arr.join("|"),
         	                         'fileDownloadName': "cntrNo.pdf",
         	                         serviceName: "lms1201r01rptservice"            
         	                     }
         	            	  });  	                  	
         	               },
         	               "close": function(){
         	            	   $.thickbox.close();
         	               }
         	           }
            		});        		
            	}
        	});
        }
    });
}
