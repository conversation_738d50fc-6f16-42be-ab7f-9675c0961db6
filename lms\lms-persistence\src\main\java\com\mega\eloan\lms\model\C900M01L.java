/* 
 * C900M01L.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 外匯業務授權額度檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C900M01L", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class C900M01L extends GenericBean implements IDataObject {

	private static final long serialVersionUID = 1L;

	/** oid **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 
	 * 授權層級<p/>
	 * 參考lms1205m01_caseLvl<br/>
	 *  A.董事會 <br/>
	 *  1.常務董事會<br/>
	 *  6.總經理<br/>
	 *  7.副總經理<br/>
	 *  8.授信審查處處長<br/>
	 *  B.區域營運中心營運長<br/>
	 *  <br/>
	 *  caseLvl、brNo、brClass擇一有值
	 */
	@Size(max=2)
	@Column(name="CASELVL", length=2, columnDefinition="VARCHAR(2)")
	private String caseLvl;

	/** 
	 * 分行代號<p/>
	 * COM.BELSBRN.brNo<br/>
	 *  放特別指定的分行代號<br/>
	 *  007.國外部<br/>
	 *  201.金控總部分行<br/>
	 *  025.國際金融業務分行經理<br/>
	 *  <br/>
	 *  caseLvl、brNo、brClass擇一有值
	 */
	@Size(max=3)
	@Column(name="BRNO", length=3, columnDefinition="VARCHAR(3)")
	private String brNo;

	/** 
	 * 分行等級<p/>
	 * COM.BELSBRN.brClass<br/>
	 *  <br/>
	 *  caseLvl、brNo、brClass擇一有值
	 */
	@Size(max=1)
	@Column(name="BRCLASS", length=1, columnDefinition="VARCHAR(1)")
	private String brClass;

	/** 
	 * 項目<p/>
	 * 目前有A、B兩項<br/>
	 *  若此欄位為空，代表授權層級沒有分AB項
	 */
	@Size(max=1)
	@Column(name="TYPE", length=1, columnDefinition="VARCHAR(1)")
	private String type;

	/** 符合出口實績額度 **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="EXPERFYAMT", columnDefinition="DECIMAL(17,2)")
	private BigDecimal experfYAmt;

	/** 不符出口實績額度 **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="EXPERFNAMT", columnDefinition="DECIMAL(17,2)")
	private BigDecimal experfNAmt;

	/** 瑕疵單據押匯限額 **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="FLAWAMT", columnDefinition="DECIMAL(17,2)")
	private BigDecimal flawAmt;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String Creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 取得oid **/
	public String getOid() {
		return this.oid;
	}
	/** 設定oid **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 
	 * 取得授權層級<p/>
	 * 參考lms1205m01_caseLvl<br/>
	 *  A.董事會 <br/>
	 *  1.常務董事會<br/>
	 *  6.總經理<br/>
	 *  7.副總經理<br/>
	 *  8.授信審查處處長<br/>
	 *  B.區域營運中心營運長<br/>
	 *  <br/>
	 *  caseLvl、brNo、brClass擇一有值
	 */
	public String getCaseLvl() {
		return this.caseLvl;
	}
	/**
	 *  設定授權層級<p/>
	 *  參考lms1205m01_caseLvl<br/>
	 *  A.董事會 <br/>
	 *  1.常務董事會<br/>
	 *  6.總經理<br/>
	 *  7.副總經理<br/>
	 *  8.授信審查處處長<br/>
	 *  B.區域營運中心營運長<br/>
	 *  <br/>
	 *  caseLvl、brNo、brClass擇一有值
	 **/
	public void setCaseLvl(String value) {
		this.caseLvl = value;
	}

	/** 
	 * 取得分行代號<p/>
	 * COM.BELSBRN.brNo<br/>
	 *  放特別指定的分行代號<br/>
	 *  007.國外部<br/>
	 *  201.金控總部分行<br/>
	 *  025.國際金融業務分行經理<br/>
	 *  <br/>
	 *  caseLvl、brNo、brClass擇一有值
	 */
	public String getBrNo() {
		return this.brNo;
	}
	/**
	 *  設定分行代號<p/>
	 *  COM.BELSBRN.brNo<br/>
	 *  放特別指定的分行代號<br/>
	 *  007.國外部<br/>
	 *  201.金控總部分行<br/>
	 *  025.國際金融業務分行經理<br/>
	 *  <br/>
	 *  caseLvl、brNo、brClass擇一有值
	 **/
	public void setBrNo(String value) {
		this.brNo = value;
	}

	/** 
	 * 取得分行等級<p/>
	 * COM.BELSBRN.brClass<br/>
	 *  <br/>
	 *  caseLvl、brNo、brClass擇一有值
	 */
	public String getBrClass() {
		return this.brClass;
	}
	/**
	 *  設定分行等級<p/>
	 *  COM.BELSBRN.brClass<br/>
	 *  <br/>
	 *  caseLvl、brNo、brClass擇一有值
	 **/
	public void setBrClass(String value) {
		this.brClass = value;
	}

	/** 
	 * 取得項目<p/>
	 * 目前有A、B兩項<br/>
	 *  若此欄位為空，代表授權層級沒有分AB項
	 */
	public String getType() {
		return this.type;
	}
	/**
	 *  設定項目<p/>
	 *  目前有A、B兩項<br/>
	 *  若此欄位為空，代表授權層級沒有分AB項
	 **/
	public void setType(String value) {
		this.type = value;
	}

	/** 取得符合出口實績額度 **/
	public BigDecimal getExperfYAmt() {
		return this.experfYAmt;
	}
	/** 設定符合出口實績額度 **/
	public void setExperfYAmt(BigDecimal value) {
		this.experfYAmt = value;
	}

	/** 取得不符出口實績額度 **/
	public BigDecimal getExperfNAmt() {
		return this.experfNAmt;
	}
	/** 設定不符出口實績額度 **/
	public void setExperfNAmt(BigDecimal value) {
		this.experfNAmt = value;
	}

	/** 取得瑕疵單據押匯限額 **/
	public BigDecimal getFlawAmt() {
		return this.flawAmt;
	}
	/** 設定瑕疵單據押匯限額 **/
	public void setFlawAmt(BigDecimal value) {
		this.flawAmt = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.Creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.Creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
