/* 
 * LMS2415M01Formhandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.crs.handler.form;

import java.text.MessageFormat;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import com.iisigroup.cap.component.PageParameters;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.TypCdEnum;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.model.DocOpener;
import com.mega.eloan.common.model.DocOpener.OpenTypeCode;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.base.common.BranchRate;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.crs.pages.LMS2415M01Page;
import com.mega.eloan.lms.crs.service.LMS2415Service;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.mfaloan.service.MisELLNGTEEService;
import com.mega.eloan.lms.model.C241M01A;
import com.mega.eloan.lms.model.C241M01B;
import com.mega.eloan.lms.model.C241M01C;
import com.mega.eloan.lms.model.C241M01E;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * [個金]覆審報告表
 * </pre>
 * 
 * @since 2011/9/7
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/9/7,jessica,new
 *          </ul>
 */

@Scope("request")
@Controller("lms2415m01formhandler")
public class LMS2415M01Formhandler extends AbstractFormHandler {

	@Resource
	MisELLNGTEEService emisEllnService;
	
	@Resource
	LMSService lmsService;
	
	@Resource
	UserInfoService userInfoService;

	@Resource
	LMS2415Service service;

	@Resource
	DocCheckService docCheckService;

	@Resource
	CodeTypeService codetypeService;

	@Resource
	BranchService branch;
	
	@Resource
	MisCustdataService misCustdataService;
	
	/**
	 * 覆審項目 預設值 TODO: 需等能引進擔保品 才能完成整個功能計算
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult returnC241s03Val(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		// C2415S03Panel預設值
		// 先判斷L170M01A是否有資料(授信資料合計)
		C241M01A c241m01a = service.findModelByMainId(C241M01A.class, mainId);
		if(c241m01a == null){
			c241m01a = new C241M01A();
		}
		List<C241M01C> c241m01clist = service.findC241m01cByMainId(mainId);
		List<C241M01C> list = new ArrayList<C241M01C>();
		for (C241M01C c241m01c : c241m01clist) {
			if(Util.equals("", Util.nullToSpace(c241m01a.getRptId()))){
				int seq = c241m01c.getItemSeq();
				switch (seq) {
				case 1://A001
				case 2://A002
				case 3://A003
				case 6://B001
				case 7://B002
				case 8://B003
				case 9://B004
				case 10://B005
				case 13://B008
				case 17://B012
				case 18://B013
				case 19://B014
				case 20://B015
				case 21://C001
				case 22://C002
					c241m01c.setChkResult("Y");
					break;
				case 16://B011
				case 24://C004
					c241m01c.setChkResult("N");
					break;
				default://A004,A005,B006,B007,B009,B010,C003
					// K
					c241m01c.setChkResult("K");
					break;
				}
			} else if(Util.equals(CrsUtil.V_201809, Util.nullToSpace(c241m01a.getRptId()))){
				String itemType = c241m01c.getItemType();
				if("Z".equals(itemType)||"Y".equals(itemType)){
					continue;
				}
				String itemNo = c241m01c.getItemNo();
				String[] Y = new String[]{"A001","A002","A003","A005","B001","B002","B003",
						"B004","B005","B007","B009","B014","B015","B016","B017","C001","C002","C004"};
				String[] N = new String[]{"B013","C005"};
				String[] K = new String[]{"A004","A006","A007","B006","B008","B010","B011","B012","C003"};
				if (Arrays.asList(Y).contains(itemNo)){
					c241m01c.setChkResult("Y");
				} else if (Arrays.asList(N).contains(itemNo)){
					c241m01c.setChkResult("N");
				} else if (Arrays.asList(K).contains(itemNo)){
					c241m01c.setChkResult("K");
				}
			} else if(Util.equals(CrsUtil.V_202209, Util.nullToSpace(c241m01a.getRptId()))){
				String itemType = c241m01c.getItemType();
				if("Z".equals(itemType)||"Y".equals(itemType)){
					continue;
				}
				String itemNo = c241m01c.getItemNo();
				String[] Y = new String[]{"A001","A002","A003","A005","B001","B002","B003",
						"B004","B005","B007","B009","B014","B015","B016","B017","C001","C002","C004","C006"};
				String[] N = new String[]{"B013","C005"};
				String[] K = new String[]{"A004","A006","A007","B006","B008","B010","B011","B012","C003"};
				if (Arrays.asList(Y).contains(itemNo)){
					c241m01c.setChkResult("Y");
				} else if (Arrays.asList(N).contains(itemNo)){
					c241m01c.setChkResult("N");
				} else if (Arrays.asList(K).contains(itemNo)){
					c241m01c.setChkResult("K");
				}
			}
			list.add(c241m01c);
		}

		JSONArray ja = new JSONArray();
		// JSONObject typeJson = new JSONObject();
		for (C241M01C model : list) {
			JSONObject data = DataParse.toJSON(model);
			ja.add(data);
		}
		result.set("L170M01DArray", ja);
		result.set("rptId", Util.nullToSpace(c241m01a.getRptId()));
		return result;

	}

	/**
	 * 儲存[tempSave]
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult tempSave(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		result = this.saveData(params, "Y");

		return result;
	}

	/**
	 * <pre>
	 * 查詢 授信案件覆審報告表
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 * </pre>
	 */

	@SuppressWarnings("unused")
	@DomainAuth(value = AuthType.Query, CheckDocStatus = true)
	public IResult queryC2415m01a(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		// CapAjaxFormResult myFormResult = u。。ll;
		String mainId = params.getString(EloanConstants.MAIN_ID);
		int page = Util.parseInt(params.getString("page"));
		C241M01A c241m01a = service.findModelByMainId(C241M01A.class, mainId);
		BranchRate branchRate = lmsService.getBranchRate(Util.trim(c241m01a.getOwnBrId()));
		if(c241m01a == null){
			c241m01a = new C241M01A();
		}
		boolean currResult = false;
		if(c241m01a.getTotBalCurr() == null){
			c241m01a.setTotBalCurr(branch.getBranch(Util.trim(c241m01a.getOwnBrId())).getUseSWFT());
			currResult = true;
		}
		if(c241m01a.getTotQuotaCurr() == null){
			c241m01a.setTotQuotaCurr(branch.getBranch(Util.trim(c241m01a.getOwnBrId())).getUseSWFT());
			currResult = true;
		}
		if(currResult){
			if("010".equals(c241m01a.getDocStatus())){
				service.save(c241m01a);
			}else{
				service.saveNoChangUpdate(c241m01a);
			}
		}
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Map<String, String> yesNoMap = codetypeService
				.findByCodeType("Common_YesNo");
		if (yesNoMap == null)
			yesNoMap = new LinkedHashMap<String, String>();
		Map<String, String> specifyCycleMap = codetypeService
				.findByCodeType("c241m01a_specifyCycle");
		if (specifyCycleMap == null)
			specifyCycleMap = new LinkedHashMap<String, String>();
		Map<String, String> nckdFlagMap = codetypeService
		.findByCodeType("lms2405m01_NckdFlag");
		if (nckdFlagMap == null)
			nckdFlagMap = new LinkedHashMap<String, String>();
		String docStatus = Util.trim(c241m01a.getDocStatus());
		if(docStatus.equals(RetrialDocStatusEnum.已覆核未核定.toString()) && Util.isNotEmpty(c241m01a.getUpDate())){
			docStatus = RetrialDocStatusEnum.已覆核已核定.toString();
		}
		RetrialDocStatusEnum e = RetrialDocStatusEnum.getEnum(Util.trim(docStatus));
		if (c241m01a == null) {
			c241m01a = new C241M01A();
		}
		String kind = c241m01a.getTypCd();
		if (e != null) {
			docStatus = e.name();
		}
		result = DataParse.toResult(c241m01a);
		result.set("custIdm1", Util.nullToSpace(c241m01a.getCustId()));
		result.set("custNamem1", Util.nullToSpace(c241m01a.getCustName()));
		result.set("dupNom1", Util.nullToSpace(c241m01a.getDupNo()));
		result.set("branchName", Util.nullToSpace(branch.getBranchName(Util
				.nullToSpace(c241m01a.getOwnBrId()))));
		result.set("typCd",
				Util.nullToSpace(TypCdEnum.getEnum(kind).toString()));
		result.set("docStatus", Util.nullToSpace(docStatus));
		result.set("staff", yesNoMap.get(Util.nullToSpace(c241m01a.getStaff())));
		result.set("upDateCheck", TWNDate.toAD(c241m01a.getUpDate()));
		switch (page) {
		case 1:
			List<C241M01E> c241m01eList = service.findC241m01eByMainId(mainId);
			result.set(
					"creator",
					Util.trim(c241m01a.getCreator()) + " " + Util.trim(lmsService.getUserName(c241m01a.getCreator())) + 
					"("+TWNDate.toFullAD(c241m01a.getCreateTime())+")");
			result.set(
					"updater",
					Util.trim(c241m01a.getUpdater()) + " " + Util.trim(lmsService.getUserName(c241m01a.getUpdater())) + "("+TWNDate.toFullAD(c241m01a.getUpdateTime())+")");
			for (C241M01E c241m01e : c241m01eList) {
				result.set(
						c241m01e.getBranchType() + "staffJob"
								+ c241m01e.getStaffJob(), Util.trim(c241m01e.getStaffNo()) + Util
								.nullToSpace(userInfoService.getUserName(c241m01e.getStaffNo())));
			}
			break;
		case 2:
			result.set("custIdIn", Util.nullToSpace(c241m01a.getCustId()));
			result.set("dupNoIn", Util.nullToSpace(c241m01a.getDupNo()));
			result.set("custNameIn", Util.nullToSpace(c241m01a.getCustName()));
			
			result.set("show_shouldReviewDate", TWNDate.toAD(c241m01a.getShouldReviewDate()));
			result.set("show_retrialKind", Util.trim(c241m01a.getRetrialKind()));
			result.set("show_lastRetrialDate", TWNDate.toAD(c241m01a.getLastRetrialDate()));
			result.set("show_nCkdFlag", nckdFlagMap.get(Util
					.nullToSpace(c241m01a.getNCkdFlag())));
			result.set("show_nCkdMemo", Util.nullToSpace(c241m01a.getNCkdMemo()));
			if("A".equals(c241m01a.getSpecifyCycle())){
				c241m01a.setSpecifyCycle("12");
			}
			result.set("show_specifyCycle", specifyCycleMap.get(Util
					.nullToSpace("A".equals(c241m01a.getSpecifyCycle()) ? "12" : c241m01a.getSpecifyCycle())));
			result.set("show_lnDataDate",
					TWNDate.toAD(c241m01a.getLnDataDate()));
			result.set("show_totQuota",
					Util.trim(c241m01a.getTotQuotaCurr()) + " " + NumConverter.addComma(c241m01a.getTotQuota(),"#,##0.00"));
			result.set("show_totBal",
					Util.trim(c241m01a.getTotBalCurr()) + " " + NumConverter.addComma(c241m01a.getTotBal(),"#,##0.00"));
			result.set("docFmt", Util.trim(c241m01a.getDocFmt()));
			break;
		case 3:
			result.set("rptId", Util.nullToSpace(c241m01a.getRptId()));
			break;
		case 4:
			// myFormResult = DataParse.toResult(c241m01a);
			// result.set("c241m01d", myFormResult);
			result.set("conFlag", c241m01a.getConFlag());
			result.set("condition", c241m01a.getCondition());
			result.set("curRate", Util.nullToSpace(c241m01a.getCurRate()));
			result.set("sugRate", Util.nullToSpace(c241m01a.getSugRate()));
			break;
		default:

			break;
		}
		result.set(EloanConstants.MAIN_OID,
				CapString.trimNull(c241m01a.getOid()));
		if (yesNoMap != null)
			yesNoMap.clear();
		if (specifyCycleMap != null)
			specifyCycleMap.clear();
		return result;
	};
	
	/**重新引進主從債務人資料
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = true)
	public IResult reImportData(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		Properties prop = MessageBundleScriptCreator
		.getComponentResource(LMS2415M01Page.class);
		String oid = params.getString(EloanConstants.OID);
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String tKind = null;
		StringBuffer str01 = new StringBuffer();
		StringBuffer str02 = new StringBuffer();
		C241M01A c241m01a = service.findModelByMainId(C241M01A.class, mainId);
		if(c241m01a == null){
			c241m01a = new C241M01A();
		}
		C241M01B c241m01b = service.findModelByOid(C241M01B.class, oid);
		if(c241m01b == null){
			c241m01b = new C241M01B();
		}
		List<Map<String,Object>> list = emisEllnService.findMisellngteeForSM(Util.trim(c241m01a.getOwnBrId()), Util.trim(c241m01b.getCustId()), Util.trim(c241m01b.getDupNo()), Util.trim(c241m01b.getQuotaNo()));
		for(Map<String,Object> map : list){
			//連保
			if("G".equals(Util.trim(map.get("LNGEFLAG")))){
				tKind = prop.getProperty("C241M01b.coBorrower01");
				if(str01.length() > 0){
					str01.append("、");
				}
				str01.append(Util.trim(map.get("LNGENM"))).append(tKind);
			//一般
			}else if("N".equals(Util.trim(map.get("LNGEFLAG")))){
				tKind = prop.getProperty("C241M01b.coBorrower02");
				if(str01.length() > 0){
					str01.append("、");
				}
				str01.append(Util.trim(map.get("LNGENM"))).append(tKind);
			//連帶債務)
			}else if("L".equals(Util.trim(map.get("LNGEFLAG")))){
				tKind = prop.getProperty("C241M01b.coBorrower03");
				if(str01.length() > 0){
					str01.append("、");
				}
				str01.append(Util.trim(map.get("LNGENM"))).append(tKind);
			//票據債務
			}else if("E".equals(Util.trim(map.get("LNGEFLAG")))){
				tKind = prop.getProperty("C241M01b.coBorrower04");
				if(str01.length() > 0){
					str01.append("、");
				}
				str01.append(Util.trim(map.get("LNGENM"))).append(tKind);
			//擔保物提供
			}else if("S".equals(Util.trim(map.get("LNGEFLAG")))){
				tKind = prop.getProperty("C241M01b.coBorrower05");
				if(str01.length() > 0){
					str01.append("、");
				}
				str01.append(Util.trim(map.get("LNGENM"))).append(tKind);
			//
			}else if("C".equals(Util.trim(map.get("LNGEFLAG")))){
				if(str02.length() > 0){
					str02.append("、");
				}
				str02.append(Util.trim(map.get("LNGENM"))).append(tKind);
			}
		}
		if(str01.length() > 0){
			c241m01b.setCoBorrower(str01.toString());
		}
		if(str02.length() > 0){
			c241m01b.setGuarantor(str02.toString());
		}
		if(str01.length() > 0 && str02.length() > 0){
			service.save(c241m01b);
			// EFD0017=INFO|儲存成功|
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0017"));
		}else{
			// 查無資料
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0036"));
		}
		result.set("coBorrower", c241m01b.getCoBorrower());
		result.set("guarantor", c241m01b.getGuarantor());
		
		return result;
	}
	

	/**
	 * <pre>
	 * 查詢 授信案件覆審報告表
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 * </pre>
	 */

	@DomainAuth(value = AuthType.Query, CheckDocStatus = true)
	public IResult checkC2415Data(PageParameters params)
			throws CapException {
//		boolean checkResult = true;
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String checkPoint = params.getString("checkPoint");
//		String mainOid = params.getString(EloanConstants.MAIN_OID);
//		int page = Util.parseInt(params.getString("page"));
		C241M01A c241m01a = service.findModelByMainId(C241M01A.class, mainId);
		List<C241M01C> c241m01cList = service.findC241m01cByMainId(mainId);
		List<C241M01B> c241m01bList = service.findC241m01bByMainId(mainId);
		if (c241m01a == null) {
			c241m01a = new C241M01A();
		}
		HashMap<String, String> param = new HashMap<String, String>();
		Properties pop = MessageBundleScriptCreator
		.getComponentResource(LMS2415M01Page.class);
		if (!c241m01bList.isEmpty()) {
			for (C241M01B c241m01b : c241m01bList) {
				if(Util.isEmpty(c241m01b.getQuotaNo()) || 
					Util.isEmpty(c241m01b.getYnReview()) || 
					Util.isEmpty(c241m01b.getQuotaType())
				){
					result.set("check", false);
					// EFD0005=ERROR|$\{colName\}授信帳務明細「是否於本次覆審」、「額度性質」及「額度序號」不可為空白
					param.put("colName", pop.getProperty("C241M01b.checkSendData"));
					throw new CapMessageException(RespMsgHelper.getMessage("EFD0005", param), getClass());
				}
			}
		}else{
			result.set("check", false);
			// EFD0005=ERROR|$\{colName\}授信帳務明細至少要輸入一筆
			param.put("colName", pop.getProperty("C241M01b.inputOneData"));
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0005", param), getClass());
		}
		if (!c241m01cList.isEmpty()) {
			String cPR = Util.equals("", c241m01a.getRptId()) ? "B009" : "B011";
			String[] noChk = new String[] {"Y000", "Y100", "Y110", "Y120", "Y200", "Y210", "Y220", "Y222"};
			String[] arrayX = new String[] {"X000", "X100", "X200", "X300"};
			// 前次覆審項目檢核 chkPreReview
			for (C241M01C c241m01c : c241m01cList) {
				if (Util.equals(cPR, c241m01c.getItemNo())) {
					if("N".equals(c241m01c.getChkResult())){
						result.set("check", true);
					}else if("K".equals(c241m01c.getChkResult())){
						result.set("check", true);
					}else if ("Y".equals(c241m01c.getChkResult())) {
						result.set("check", false);
						// ERROR|請選擇第14項是否有改善，並且輸入說明|
						param.put("number", Util.trim(c241m01c.getItemSeq()));
						throw new CapMessageException(RespMsgHelper.getMessage("EFD3008", param), getClass());
					} else {
						if (Util.isEmpty(c241m01c.getChkPreReview()) || (Util.isNotEmpty(c241m01c.getChkPreReview()) && Util.isEmpty(c241m01c.getChkText()))) {
							result.set("check", false);
							// ERROR|請選擇第14項是否有改善，並且輸入說明|
							param.put("number", Util.trim(c241m01c.getItemSeq()));
							throw new CapMessageException(RespMsgHelper.getMessage("EFD3008", param), getClass());
						}else{
							result.set("check", true);
						}
					}
				}else if (CrsUtil.DOCFMT_一般.equals(c241m01a.getDocFmt()) && 
							("B007".equals(c241m01c.getItemNo()))
								|| (Arrays.asList(arrayX).contains(c241m01c.getItemNo()))) { 
					// 一般覆審報告表不須檢核B007,arrayX
				}else if (Util.isEmpty(c241m01c.getChkResult()) 
						&& !(Arrays.asList(noChk).contains(c241m01c.getItemNo()))){
					result.set("check", false);
					// EFD0005=ERROR|$\{colName\}覆審項目都要有填「有、無、不適用」
					param.put("colName", pop.getProperty("C241M01c.choiceChkResult"));
					throw new CapMessageException(RespMsgHelper.getMessage("EFD0005", param), getClass());
				}
			}
		} else {
			result.set("check", false);
			// EFD0005=ERROR|$\{colName\}覆審項目都要有填「有、無、不適用」
			param.put("colName", pop.getProperty("C241M01c.choiceChkResult"));
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0005", param), getClass());
		}
		if (Util.isEmpty(Util.trim(c241m01a.getConFlag()))) {
			result.set("check", false);
			// ERROR||
			throw new CapMessageException(RespMsgHelper.getMessage("EFD3029", param), getClass());
		}
		if (Util.isEmpty(c241m01a.getProjectNo())) {
			result.set("check", false);
			// EFD0005=ERROR|$\{colName\}此欄位不可空白|(覆審序號)
			param.put("colName", pop.getProperty("C241M01a.projectNo"));
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0005", param), getClass());
		}
		if (Util.isEmpty(c241m01a.getRetrialDate())) {
			result.set("check", false);
			// EFD0005=ERROR|$\{colName\}此欄位不可空白|(覆審日期)
			param.put("colName", pop.getProperty("C241M01a.retrialDate"));
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0005", param), getClass());
		}
		if("2".equals(c241m01a.getConFlag())){
			if (Util.isEmpty(c241m01a.getCondition())) {
				result.set("check", false);
				// EFD0005=ERROR|$\{colName\}此欄位不可空白|(異常情形，應改善或注意事項)
				param.put("colName", pop.getProperty("C241M01A.condition"));
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0005", param), getClass());
			}
		}
		if(!"N".equals(checkPoint)){
			if("".equals(c241m01a.getConFlag())){
				result.set("check", false);
				// EFD0005=ERROR|$\{colName\}此欄位不可空白|(覆審意見)
				param.put("colName", pop.getProperty("C241M01A.conFlag"));
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0005", param), getClass());
			}
		}
		result.set("retrialKind", Util.trim(c241m01a.getRetrialKind()));
		

		return result;
	};

	@DomainAuth(value = AuthType.Query, CheckDocStatus = true)
	public IResult queryC2415m01d(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		C241M01A c241m01a = service.findModelByMainId(C241M01A.class, mainId);

		if (c241m01a != null) {
			result = DataParse.toResult(c241m01a);
		}
		return result;
	};

	/**
	 * <pre>
	 * 查詢  一般/團貸覆審項目檔 (查詢覆審項目是否有變更)
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 * </pre>
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	@DomainAuth(value = AuthType.Query, CheckDocStatus = true)
	public IResult queryC2415m01c(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String custId = params.getString("custId");
		String dupNo = params.getString("dupNo");
		String deleteData = params.getString("deleteData");
		C241M01A c241m01a = service.findModelByMainId(C241M01A.class, mainId);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		IBranch branchtype = branch.getBranch(user.getUnitNo());
		Locale locale = LMSUtil.getLocale();

		if("Y".equals(deleteData)){
			service.deleteC241m01cByMainId(mainId);
			c241m01a.setRptId(service.getOVSLastC241M01ARptId());	//J-107-0128海外改格式
		}
		List<C241M01C> list = new ArrayList<C241M01C>();
		list = service.findC241m01cByMainId(mainId);
		// 如果沒有此mainId資料
		if (list.isEmpty()) {
			// 找CodeType裡的資料
			List<CodeType> codeTypeList = null;
			if("".equals(Util.nullToSpace(c241m01a.getRptId()))){
				codeTypeList = codetypeService.findByCodeTypeList("lms2415s03_reviewType");
			} else if(CrsUtil.V_201809.equals(Util.nullToSpace(c241m01a.getRptId()))){	//009301海外改格式  新增版本資訊
				if(Util.equals(locale, "en") 
						&& UtilConstants.Country.加拿大.equals(branchtype.getCountryType())){
					codeTypeList = codetypeService.findByCodeTypeList("lms2415s03_reviewTypeV2_CA");
				} else {
					codeTypeList = codetypeService.findByCodeTypeList("lms2415s03_reviewTypeV2");
				}
			} else if(CrsUtil.V_202209.equals(Util.nullToSpace(c241m01a.getRptId()))){	//J-111-0405,009763海外改格式
				if(Util.equals(locale, "en") 
						&& UtilConstants.Country.加拿大.equals(branchtype.getCountryType())){
					codeTypeList = codetypeService.findByCodeTypeList("lms2415s03_reviewTypeV3_CA");
				} else {
					codeTypeList = codetypeService.findByCodeTypeList("lms2415s03_reviewTypeV3");
				}
			}

			list = new ArrayList();
			for (CodeType codeType : codeTypeList) {
				C241M01C model = new C241M01C();
				model.setCreateTime(CapDate.getCurrentTimestamp());
				model.setCreator(user.getUserId());
				model.setUpdateTime(CapDate.getCurrentTimestamp());
				model.setUpdater(user.getUserId());
				model.setMainId(mainId);
				model.setCustId(custId);
				model.setDupNo(dupNo);
				model.setItemNo(Util.nullToSpace(codeType.getCodeValue()));
				model.setChkItem(Util.nullToSpace(codeType.getCodeDesc()));
				// 覆審類別 A,B...
				model.setItemType(Util.nullToSpace(codeType.getCodeDesc2()));
				model.setItemSeq(codeType.getCodeOrder());
				model.setChkResult(null);
				list.add(model);
			}
			service.save(list);
		}
		JSONArray ja = new JSONArray();
		JSONObject typeJson = new JSONObject();
		for (C241M01C model : list) {
			JSONObject data = DataParse.toJSON(model);
			// 覆審類別 A,B...
			String type = model.getItemType();
			int count = Util.parseInt((String) typeJson.get(type));
			typeJson.put(type, String.valueOf(++count));
			ja.add(data);
		}

		result.set("C241M01CArray", ja);
		result.set("typeJson", typeJson.toString());
		result.set("rptId", Util.nullToSpace(c241m01a.getRptId()));
		result.set("Country",branchtype.getCountryType());
		result.set("docFmt", Util.trim(c241m01a.getDocFmt()));
		return result;
	};

	/**
	 * <pre>
	 * 更新覆審控制檔
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 * </pre>
	 * @throws ParseException 
	 */

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult updateElf491(PageParameters params)
			throws CapException, ParseException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String dupNo = params.getString("dupNo");
		String custId = params.getString("custId");
		String specifyCycle = params.getString("specifyCycle");
		String doFix = params.getString("doFix");
		
		if (!Util.isEmpty(params.getString("detailOid", ""))) {
			C241M01A c241m01aIn = service.findModelByOid(C241M01A.class, params.getString("detailOid"));
			if (c241m01aIn != null) {
				mainId = c241m01aIn.getMainId();
				dupNo = c241m01aIn.getDupNo();
				custId = c241m01aIn.getCustId();
			}
		}
		
		C241M01A c241m01a = service.findModelByMainId(C241M01A.class, mainId);
		if (c241m01a == null) {
			c241m01a = new C241M01A();
			c241m01a.setCustId(custId);
			c241m01a.setDupNo(dupNo);
			c241m01a.setMainId(mainId);
		}
		String branch = Util.trim(c241m01a.getOwnBrId());
		if(Util.isEmpty(specifyCycle)){
			specifyCycle = Util.trim(c241m01a.getSpecifyCycle());
		}else{
			c241m01a.setSpecifyCycle(specifyCycle);
		}
		boolean st = service.updateElf491(mainId, custId, dupNo, branch,doFix,specifyCycle);

		if (st) {

			c241m01a.setUpDate(CapDate.getCurrentTimestamp());
			if("010".equals(c241m01a.getDocStatus())){
				service.save(c241m01a);
			}else{
				service.saveNoChangUpdate(c241m01a);
			}
			// 顯示在畫面上的日期格式(當日引進)
			result.set("upDate", TWNDate.toFullAD(c241m01a.getUpDate()));
			// LMS241M01Page.js (更新覆審控制檔按鈕)
			result.set("st", true);

			Map<String, String> specifyCycleMap = codetypeService
					.findByCodeType("c241m01a_specifyCycle");
			if (specifyCycleMap == null)
				specifyCycleMap = new LinkedHashMap<String, String>();
			result.set("show_specifyCycle", specifyCycleMap.get(Util
					.nullToSpace("A".equals(c241m01a.getSpecifyCycle()) ? "12" : c241m01a.getSpecifyCycle())));
			
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0018"));
		} else {
			// 沒有此筆資料
			result.set("st", false);
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0036"));
		}

		return result;
	}

	/**
	 * <pre>
	 * 查詢一般授信資料(單筆)thickbox內容
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = true)
	public IResult queryCredit(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
//		Map<String,String> lms2415m01_lnTypeMap = codetypeService.findByCodeType("lms2415m01_lnType");
//		if(lms2415m01_lnTypeMap == null){
//			lms2415m01_lnTypeMap = new LinkedHashMap<String,String>();
//		}
		// 查詢授信資料內容(C241M01B)
		String oid = params.getString(EloanConstants.OID);
		C241M01B c241m01b = service.findModelByOid(C241M01B.class, oid);
		Map<String, String> quotaTypeMap = codetypeService
				.findByCodeType("c241m01b_quotaType");
		if (quotaTypeMap == null)
			quotaTypeMap = new LinkedHashMap<String, String>();
		if (c241m01b != null) {
//			c241m01b.setLnType(lms2415m01_lnTypeMap.get(c241m01b.getLnType()));
			result.set("C2415M01bForm", DataParse.toResult(c241m01b));
			result.set("balAmt_show", NumConverter.addComma(c241m01b.getBalAmt(),"#,##0.00"));
			result.set("quotaAmt_show", NumConverter.addComma(c241m01b.getQuotaAmt(),"#,##0.00"));
		}
		result.set("c241m01b_quotaType", new CapAjaxFormResult(quotaTypeMap));
		return result;
	};

	/**
	 * <pre>
	 * 重新產生授信資料
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult addCredit(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		C241M01A c241m01a = service.findModelByMainId(C241M01A.class, mainId);
		if (c241m01a == null)
			c241m01a = new C241M01A();
		String brNo = Util.nullToSpace(c241m01a.getOwnBrId());
		String custId = c241m01a.getCustId();
		String dupNo = c241m01a.getDupNo();

		// 重新產生授信資料之前先刪除原先存在的資料(下SQL大量刪除)
		service.deleteC241m01bByMainId(mainId);

		// 再重新產生授信資料
		Map<String, String> map = null;
		if ((Boolean) service.saveC241m01bByCustIdData(brNo, custId, mainId,
				dupNo).get("success")) {
			List<C241M01B> c241m01bList = service.findC241M01bList(mainId);
			map = service.sumC241M01BAllAndSaveC241M01A(brNo, mainId, c241m01a,
					c241m01bList, "1");
			// 顯示在畫面上的格式
			result.set("show_lnDataDate",
					TWNDate.toAD(c241m01a.getLnDataDate()));
			result.set("show_totQuota",
					Util.trim(map.get("totQuotaCurr")) + " " + NumConverter.addComma(map.get("totQuota"),"#,##0.00"));
			result.set("show_totBal", Util.trim(map.get("totBalCurr")) + " " + NumConverter.addComma(map.get("totBal"),"#,##0.00"));
			result = this.handleRateMap(map, result);
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0018"));
			this.HandlerResultWarmMsg(result);
		} else {
			map = service.sumC241M01BAllAndSaveC241M01A(brNo, mainId, c241m01a,
					new LinkedList<C241M01B>(), "1");
			// 查無資料
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0036"));
			result.set("show_totQuota", Util.trim(map.get("totQuotaCurr")) + " 0.00");
			result.set("show_totBal", Util.trim(map.get("totBalCurr")) + " 0.00");
		}

		return result;
	}

	/**
	 * <pre>
	 * 儲存(單筆)一般授信資料
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(AuthType.Query)
	public IResult saveC241m01b(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String formC241m01b = params.getString("C2415M01bForm");
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String oid = params.getString(EloanConstants.OID);
		C241M01B c241m01b = service.findModelByOid(C241M01B.class, oid);
		C241M01A c241m01a = service.findModelByMainId(C241M01A.class,
				mainId);
		Map<String,String> subItemMap = null;
		subItemMap = codetypeService.findByCodeType("lms1405m01_SubItem");
		if(subItemMap == null){
			subItemMap = new LinkedHashMap<String,String>();
		}
		if (c241m01b == null) {
			c241m01b = new C241M01B();
			c241m01b.setMainId(mainId);
			c241m01b.setCustId(Util.trim(c241m01a.getCustId()));
			c241m01b.setDupNo(Util.trim(c241m01a.getDupNo()));
		}
		DataParse.toBean(formC241m01b, c241m01b);
		c241m01b.setLoanNo("");
		c241m01b.setSubjectName(Util.trim(subItemMap.get(Util.trim(c241m01b.getSubjectNo()))));
		service.save(c241m01b);
		List<C241M01B> c241m01bList = service.findC241M01bList(mainId);
		Map<String, String> map = service
				.sumC241M01BAllAndSaveC241M01A(c241m01a.getOwnBrId(),
						mainId, c241m01a, c241m01bList, null);
		result = this.handleRateMap(map, result);
		result.set("C241M01bForm", DataParse.toResult(c241m01b));

		result.set("show_oid",
				Util.trim(c241m01b.getOid()));
		result.set("show_lnDataDate",
				TWNDate.toAD(c241m01a.getLnDataDate()));
		result.set("show_totQuota",
				Util.trim(map.get("totQuotaCurr")) + " " + NumConverter.addComma(map.get("totQuota"),"#,##0.00"));
		result.set("show_totBal", Util.trim(map.get("totBalCurr")) + " " + NumConverter.addComma(map.get("totBal"),"#,##0.00"));
		if (params.getAsBoolean("showMsg", true)) {
			// EFD0017=INFO|儲存成功|
			result.set("SUCCESSCODE", "EFD0017");
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0017"));
			this.HandlerResultWarmMsg(result);
		}
		return result;
	}
	
	/**
	 * 刪除
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult delete(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String[] oids = params.getStringArray("oids");
		C241M01A c241m01a = service.findModelByMainId(C241M01A.class,
				mainId);
		service.deleteFromL241M01B(oids);
		List<C241M01B> c241m01bList = service.findC241M01bList(mainId);
		Map<String, String> map = service
				.sumC241M01BAllAndSaveC241M01A(c241m01a.getOwnBrId(),
						mainId, c241m01a, c241m01bList, null);
		result = this.handleRateMap(map, result);
		result.set("show_lnDataDate",
				TWNDate.toAD(c241m01a.getLnDataDate()));
		result.set("show_totQuota",
				Util.trim(map.get("totQuotaCurr")) + " " + NumConverter.addComma(map.get("totQuota"),"#,##0.00"));
		result.set("show_totBal", Util.trim(map.get("totBalCurr")) + " " + NumConverter.addComma(map.get("totBal"),"#,##0.00"));
		if (params.getAsBoolean("showMsg", true)) {
			// EFD0019=INFO|刪除成功|
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0019"));
		}
		return result;
	}
	
	/**
	 * 刪除所有授信資料
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult deleteCredit(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		C241M01A c241m01a = service.findModelByMainId(C241M01A.class,
				mainId);
		service.deleteCredit(mainId);
		List<C241M01B> c241m01bList = service.findC241M01bList(mainId);
		Map<String, String> map = service
				.sumC241M01BAllAndSaveC241M01A(c241m01a.getOwnBrId(),
						mainId, c241m01a, c241m01bList, null);
		result = this.handleRateMap(map, result);
		result.set("show_lnDataDate",
				TWNDate.toAD(c241m01a.getLnDataDate()));
		result.set("show_totQuota",
				Util.trim(map.get("totQuotaCurr")) + " " + NumConverter.addComma(map.get("totQuota"),"#,##0.00"));
		result.set("show_totBal", Util.trim(map.get("totBalCurr")) + " " + NumConverter.addComma(map.get("totBal"),"#,##0.00"));
		if (params.getAsBoolean("showMsg", true)) {
			// EFD0019=INFO|刪除成功|
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0019"));
		}
		return result;
	}

	/**
	 * <pre>
	 * 儲存受檢單位洽辦情形
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult save2415M5(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String form = params.getString("lms2415s05BOX");
		JSONObject json = JSONObject.fromObject(form);
		String strText = json.getString("lms2415s05BOXtext");
		String mainId = params.getString(EloanConstants.MAIN_ID);
		C241M01A c241m01a = service.findModelByMainId(C241M01A.class, mainId);
		c241m01a.setBranchComm(strText);
		if("010".equals(c241m01a.getDocStatus())){
			service.save(c241m01a);
		}else{
			service.saveNoChangUpdate(c241m01a);
		}
		result = DataParse.toResult(c241m01a);
		if (params.getAsBoolean("showMsg", true)) {
			// EFD0017=INFO|儲存成功|
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0017"));
		}
		result.set(EloanConstants.MAIN_OID, c241m01a.getOid());
		return result;

	};

	/**
	 * <pre>
	 * 儲存(全部標籤內容)
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(AuthType.Query)
	public IResult saveAll(PageParameters params)
			throws CapException {
		boolean showMsg = params.getBoolean("showMsg");
		CapAjaxFormResult result = new CapAjaxFormResult();
		result = this.saveData(params, "N");
		if (showMsg) {
			if (Util.isNotEmpty(Util.nullToSpace(result.get("IncompleteMsg")))){
				this.HandlerResultWarmMsg(result);
				result.set(
						CapConstants.AJAX_NOTIFY_MESSAGE,
						result.get(CapConstants.AJAX_NOTIFY_MESSAGE)
								+ "<BR/><BR/>"
								+ Util.nullToSpace(result.get("IncompleteMsg")));
			}
		} else {
			if (Util.isNotEmpty(Util.nullToSpace(result.get("IncompleteMsg")))){
				throw new CapMessageException(result.get("IncompleteMsg").toString(), getClass());
			}
		}
		return result;
	}
	
	private CapAjaxFormResult saveData(PageParameters params,String tempSave) throws CapMessageException{
		CapAjaxFormResult result = new CapAjaxFormResult();
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, tempSave);
		int page = Util.parseInt(params.getString("page"));
		String mainId = params.getString(EloanConstants.MAIN_ID);
		boolean c241m01aSaveResult = false;
		C241M01A c241m01a = service.findModelByMainId(C241M01A.class, mainId);
		if(c241m01a == null){
			c241m01a = new C241M01A();
		}
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		switch (page) {
		case 1:

			break;
		case 2:
			//C241M01aForm_s02
			String formC241m01a = params.getString("C241M01aForm_s02");
			JSONObject jsonS02 = JSONObject.fromObject(formC241m01a);
			String retrialDate = jsonS02.optString("retrialDate");
			c241m01a.setRetrialDate(Util.isEmpty(retrialDate) ? null : TWNDate.valueOf(retrialDate));
			c241m01a.setDocFmt(jsonS02.optString("docFmt"));
			if("010".equals(c241m01a.getDocStatus())){
				service.save(c241m01a);
			}else{
				service.saveNoChangUpdate(c241m01a);
			}
			c241m01aSaveResult = true;
			break;
		case 3:
			List<C241M01C> list = new ArrayList<C241M01C>();
			String custId = params.getString("custId");
			String dupNo = params.getString("dupNo");
			String formC241m01c = params.getString("C241M01cForm");
			JSONObject jobjectD = JSONObject.fromObject(formC241m01c);
			JSONArray itemNo = jobjectD.getJSONArray("itemNo");
			String chkPreReview = jobjectD.getString("chkPreReview");
			String[] chkResultSeq = new String[itemNo.size()];
			String[] chkTextSeq = new String[itemNo.size()];
			String rptid = jobjectD.getString("rptid");
			c241m01a.setRptId(rptid);		//J-107-0128海外改格式  更新版本
			if("010".equals(c241m01a.getDocStatus())){
				service.save(c241m01a);
			}else{
				service.saveNoChangUpdate(c241m01a);
			}
			
			if("".equals(rptid)){
				for (int i = 1; i < itemNo.size() + 1; i++) {
	
					String chkResult = jobjectD.getString("chkResult" + i);
					String chkText = jobjectD.getString("chkText" + i);
					if (chkResult.isEmpty()) {
						chkResult = "3";
					}
					chkResultSeq[i - 1] = chkResult;
					chkTextSeq[i - 1] = chkText;
				}
	
				for (int j = 1; j < itemNo.size() + 1; j++) {
	
					C241M01C c241m01c = service.findC241m01cByMainId(mainId,
							custId, dupNo, Util.parseInt(itemNo.getString(j - 1)));
					if (c241m01c == null) {
						c241m01c = new C241M01C();
						c241m01c.setMainId(mainId);
						c241m01c.setCustId(custId);
						c241m01c.setDupNo(dupNo);
						c241m01c.setChkResult(chkResultSeq[j - 1]);
						c241m01c.setChkText(chkTextSeq[j - 1]);
						if ("B009".equals(c241m01c.getItemNo())) {
							c241m01c.setChkPreReview(chkPreReview);
						}
					} else {
						c241m01c.setChkResult(chkResultSeq[j - 1]);
						c241m01c.setChkText(chkTextSeq[j - 1]);
						if ("B009".equals(c241m01c.getItemNo())) {
							c241m01c.setChkPreReview(chkPreReview);
						}
					}
					c241m01c.setUpdateTime(CapDate.getCurrentTimestamp());
					c241m01c.setUpdater(user.getUserId());
					list.add(c241m01c);
				}
			} else {
				for (int i = 0; i < itemNo.size(); i++) {
					C241M01C c241m01c = service.findC241m01cByMainId(mainId, custId, dupNo, 
							Util.trim(itemNo.getString(i)));
					String chkResult = Util.trim(jobjectD.get("chkResult" + itemNo.getString(i)));
					String chkText = Util.trim(jobjectD.get("chkText" + itemNo.getString(i)));
					c241m01c.setChkResult(Util.trim(chkResult));
					c241m01c.setChkText(chkText);
					if ("B011".equals(c241m01c.getItemNo())) {
						c241m01c.setChkPreReview(chkPreReview);
					}
					c241m01c.setUpdateTime(CapDate.getCurrentTimestamp());
					c241m01c.setUpdater(user.getUserId());
					list.add(c241m01c);
				}
			}
			
			if (list.size() > 0) {
				service.save(list);
				if(Util.notEquals("",Util.nullToSpace(c241m01a.getRptId()))){	//新版才檢查
					if (Util.equals("N",
							SimpleContextHolder.get(EloanConstants.TEMPSAVE_RUN))) {
						String msg = "";
						try {
							if(CrsUtil.V_201809.equals(Util.nullToSpace(c241m01a.getRptId()))) {
								msg = this.checkC241m01cData(list);
							} else if(CrsUtil.V_202209.equals(Util.nullToSpace(c241m01a.getRptId()))){	//J-111-0405,009763海外改格式
								msg = this.checkC241m01cDataV202209(list);
							}
							if (Util.isNotEmpty(msg)) {
								result.set("IncompleteMsg", msg);
							}
						} catch (CapException e) {
							// TODO Auto-generated catch block
							e.printStackTrace();
						}
					}
				}
			}
			break;
		case 4:
			String formC241m01d = params.getString("C241M01dForm");
			JSONObject json = JSONObject.fromObject(formC241m01d);

			if("2".equals(Util.trim(json.getString("conFlag")))){
				if (Util.isEmpty(Util.trim(json.getString("condition")))) {
					// EFD0005=ERROR|$\{colName\}此欄位不可空白|(受檢單位洽辦情形)
					Properties pop = MessageBundleScriptCreator
					.getComponentResource(LMS2415M01Page.class);
					HashMap<String,String> param = new LinkedHashMap<String,String>();
					param.put("colName", pop.getProperty("C241M01A.condition"));
					throw new CapMessageException(RespMsgHelper.getMessage("EFD0005", param), getClass());
				}
			}
			
			c241m01a.setConFlag(Util.trim(json.getString("conFlag")));
			c241m01a.setCondition(Util.trim(json.getString("condition")));
			c241m01a.setCurRate(Util.trim(json.getString("curRate")));
			c241m01a.setSugRate(Util.trim(json.getString("sugRate")));
			if("010".equals(c241m01a.getDocStatus())){
				service.save(c241m01a);
			}else{
				service.saveNoChangUpdate(c241m01a);
			}
			c241m01aSaveResult = true;
			break;
		default:
			break;
		}
		if(!c241m01aSaveResult){
			if("010".equals(c241m01a.getDocStatus())){
				service.save(c241m01a);
			}else{
				service.saveNoChangUpdate(c241m01a);
			}
		}

		if (params.getAsBoolean("showMsg", true)) {
			// EFD0017=INFO|儲存成功|
			if("N".equals(tempSave)){
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0017"));
			}
		}

		return result;
	}

	/**
	 * <pre>
	 * 呈主管覆核(呈主管 主管覆核 拆2個method)
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws Throwable
	 */
	@DomainAuth(AuthType.Modify + AuthType.Accept)
	public IResult flowAction(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// UPGRADE: 移除Wicket params print
		// Util.showParams(params);
		String oid = params.getString(EloanConstants.MAIN_OID, "");
		String mainId = params.getString(EloanConstants.MAIN_ID, "");
		String flowActionResult = params.getString("flowActionResult", "N");
		String flowSeq = params.getString("flowSeq", "");
		C241M01A c241m01a = service.findModelByMainId(C241M01A.class,mainId);

		HashMap<String, Object> data = new HashMap<String, Object>();
		data.put("mainId", mainId);
		data.put("mainOid", oid);

		//檢查經辦和主管是否為同一人
		boolean decition = params.getAsBoolean("flowAction", false);
		if(decition && user.getUserId().equals(c241m01a.getUpdater())){
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0053"), getClass());
		}
		
		if ("2".equals(flowSeq)) {
			data.put("result", "Y".equals(flowActionResult) ? "核定" : "退回");
		} else if ("4".equals(flowSeq)) {

		}
		service.flowAction(oid, user, data);

		return result;
	}// ;

	/**
	 * 處理若有無法轉換的幣別匯率 則回傳內容
	 * 
	 * @param map
	 * @return
	 */
	private CapAjaxFormResult handleRateMap(Map<String, String> map,
			CapAjaxFormResult result) {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS2415M01Page.class);
		if (map.get("noRateCurr") != null) {
			result.set(
					"WARMMSG",
					pop.getProperty("C241M01b.warmMsg01")
							+ map.get("noRateCurr"));
		}
		return result;
	}

	/**
	 * 處理從service回傳回來的訊息
	 * 
	 * @param result
	 * @param code
	 */
	private void HandlerResultWarmMsg(CapAjaxFormResult result) {
		if (result.containsKey("WARMMSG")) {
			if (result.containsKey("SUCCESSCODE")) {
				result.set(
						CapConstants.AJAX_NOTIFY_MESSAGE,
						result.get(CapConstants.AJAX_NOTIFY_MESSAGE)
								+ "<BR/><BR/>"
								+ Util.nullToSpace(result.get("WARMMSG")));
			} else {
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
						Util.nullToSpace(result.get("WARMMSG")));
			}

		}
	}
	
	public String checkC241m01cData(List<C241M01C> c241m01cList) throws CapException {
		Properties pop = MessageBundleScriptCreator.getComponentResource(LMS2415M01Page.class);
		List<String> lossList = new ArrayList<String>();
		List<String> errMsg = new ArrayList<String>();
		
		Map<String,C241M01C> map = new HashMap<String,C241M01C>();
		for (C241M01C i : c241m01cList) map.put(i.getItemNo(),i);
		
		for (C241M01C c241m01c : c241m01cList) {
			String[] AttachedTable = new String[] {"Z","Y"};
			if(Arrays.asList(AttachedTable).contains(c241m01c.getItemType())){
					continue;	//附表不檢查缺項
			}
			int itemSeq = c241m01c.getItemSeq();
			if(itemSeq >= 14 && itemSeq < 17) {
				itemSeq = itemSeq-1;
		    } else if (itemSeq >= 17) {
		    	itemSeq = itemSeq-2;
		    }
			if (Util.isEmpty(Util.trim(c241m01c.getChkResult()))) {	//檢查缺項
				lossList.add(Integer.toString(itemSeq));
			}

			if("B009".equals(c241m01c.getItemNo())){
				boolean loss = false;
				String[] arrayZ = new String[] {"Z000", "Z100"};
				for (String itemNo: arrayZ) {
					C241M01C itemData = map.get(itemNo);
					if(itemData != null	&& Util.isEmpty(Util.trim(itemData.getChkResult()))){
						loss = true;
					}
					if(Util.equals("Z000", itemNo) && Util.equals("N", itemData.getChkResult())){
						C241M01C B010Data = map.get("B010");
						if(B010Data != null && Util.notEquals("K", B010Data.getChkResult())){
							//覆審項目:{0}不應為{1}
							errMsg.add(MessageFormat.format(pop.getProperty("lms2415.ChkMsg05"),
									B010Data.getChkItem(), B010Data.getChkResult(), getClass()));
						}
						C241M01C Z100Data = map.get("Z100");
						if(Z100Data != null && Util.notEquals("K", Z100Data.getChkResult())){
							//覆審項目:{0}不應為{1}
							errMsg.add(MessageFormat.format(pop.getProperty("lms2415.ChkMsg05"),
									Z100Data.getChkItem(), Z100Data.getChkResult(), getClass())); 
						}
					} else if(Util.equals("Z100", itemNo)){
						if(itemData != null	&& Util.notEquals("K", itemData.getChkResult()) && 
								Util.isEmpty(Util.trim(itemData.getChkText()))){
							errMsg.add(MessageFormat.format(itemData.getChkItem()+
									pop.getProperty("C241M01a.info01"),	getClass()));
						}
					}
				}
				if (loss) {
					errMsg.add(MessageFormat.format(pop.getProperty("lms2415.ChkMsg04"),
							itemSeq, getClass()));	//覆審項目第{0}項之附表欄位不得空白
				}	
			}
			//前次覆審有無應行改善事項？
			if("B011".equals(c241m01c.getItemNo()) && "Y".equals(c241m01c.getChkResult()) 
					&& Util.isEmpty(c241m01c.getChkPreReview())){
				errMsg.add(MessageFormat.format(pop.getProperty("lms2415.ChkMsg01"),
						itemSeq,getClass()));
			}
			if("C001".equals(c241m01c.getItemNo())){
				boolean loss = false;
				String[] arrayY = new String[] {"Y111", "Y112", "Y121", "Y122",
						"Y123", "Y124", "Y125", "Y211", "Y212", "Y213"};
				for (String itemNo: arrayY) {
					C241M01C itemData = map.get(itemNo);
					if(itemData != null	&& Util.isEmpty(Util.trim(itemData.getChkResult()))){
						loss = true;
					}
				}
				if (loss) {
					errMsg.add(MessageFormat.format(pop.getProperty("lms2415.ChkMsg04"),
							itemSeq, getClass()));	//覆審項目第{0}項之附表欄位不得空白
				}
			}

		}
		if (CollectionUtils.isNotEmpty(lossList)) {
			errMsg.add(MessageFormat.format(pop.getProperty("lms2415.ChkMsg02"),
					StringUtils.join(lossList, "、"), getClass()));
		}
		if (errMsg.size() > 0) {
			return StringUtils.join(errMsg, "<br/>");
		} else {
			return "";
		}
	}
	
	public String checkC241m01cDataV202209(List<C241M01C> c241m01cList) throws CapException {
		Properties pop = MessageBundleScriptCreator.getComponentResource(LMS2415M01Page.class);
		List<String> lossList = new ArrayList<String>();
		List<String> errMsg = new ArrayList<String>();
		
		Map<String,C241M01C> map = new HashMap<String,C241M01C>();
		for (C241M01C i : c241m01cList) map.put(i.getItemNo(),i);
		
		for (C241M01C c241m01c : c241m01cList) {
			String[] AttachedTable = new String[] {"Z", "Y", "X"};
			if(Arrays.asList(AttachedTable).contains(c241m01c.getItemType())){
					continue;	//附表不檢查缺項
			}
			int itemSeq = c241m01c.getItemSeq();
			if(itemSeq >= 14 && itemSeq < 17) {
				itemSeq = itemSeq-1;
		    } else if (itemSeq >= 17) {
		    	itemSeq = itemSeq-2;
		    }
			if (Util.isEmpty(Util.trim(c241m01c.getChkResult()))) {	//檢查缺項
				lossList.add(Integer.toString(itemSeq));
			}
			if("B007".equals(c241m01c.getItemNo())){
				boolean loss = false;
				String[] arrayX = new String[] {"X000", "X100", "X200", "X300"};
				for (String itemNo: arrayX) {
					C241M01C itemData = map.get(itemNo);
					if(itemData != null	&& Util.isEmpty(Util.trim(itemData.getChkResult()))){
						loss = true;
					}
				}
				if (loss) {
					errMsg.add(MessageFormat.format(pop.getProperty("lms2415.ChkMsg04"),
							itemSeq, getClass()));	//覆審項目第{0}項之附表欄位不得空白
				}
			}
			if("B009".equals(c241m01c.getItemNo())){
				boolean loss = false;
				String[] arrayZ = new String[] {"Z000", "Z100"};
				for (String itemNo: arrayZ) {
					C241M01C itemData = map.get(itemNo);
					if(itemData != null	&& Util.isEmpty(Util.trim(itemData.getChkResult()))){
						loss = true;
					}
					if(Util.equals("Z000", itemNo) && Util.equals("N", itemData.getChkResult())){
						C241M01C B010Data = map.get("B010");
						if(B010Data != null && Util.notEquals("K", B010Data.getChkResult())){
							//覆審項目:{0}不應為{1}
							errMsg.add(MessageFormat.format(pop.getProperty("lms2415.ChkMsg05"),
									B010Data.getChkItem(), B010Data.getChkResult(), getClass()));
						}
						C241M01C Z100Data = map.get("Z100");
						if(Z100Data != null && Util.notEquals("K", Z100Data.getChkResult())){
							//覆審項目:{0}不應為{1}
							errMsg.add(MessageFormat.format(pop.getProperty("lms2415.ChkMsg05"),
									Z100Data.getChkItem(), Z100Data.getChkResult(), getClass())); 
						}
					} else if(Util.equals("Z100", itemNo)){
						if(itemData != null	&& Util.notEquals("K", itemData.getChkResult()) && 
								Util.isEmpty(Util.trim(itemData.getChkText()))){
							errMsg.add(MessageFormat.format(itemData.getChkItem()+
									pop.getProperty("C241M01a.info01"),	getClass()));
						}
					}
				}
				if (loss) {
					errMsg.add(MessageFormat.format(pop.getProperty("lms2415.ChkMsg04"),
							itemSeq, getClass()));	//覆審項目第{0}項之附表欄位不得空白
				}	
			}
			//前次覆審有無應行改善事項？
			if("B011".equals(c241m01c.getItemNo()) && "Y".equals(c241m01c.getChkResult()) 
					&& Util.isEmpty(c241m01c.getChkPreReview())){
				errMsg.add(MessageFormat.format(pop.getProperty("lms2415.ChkMsg01"),
						itemSeq,getClass()));
			}
			if("C001".equals(c241m01c.getItemNo())){
				boolean loss = false;
				String[] arrayY = new String[] {"Y111", "Y112", "Y121", "Y122",
						"Y123", "Y124", "Y125", "Y211", "Y212", "Y213"};
				for (String itemNo: arrayY) {
					C241M01C itemData = map.get(itemNo);
					if(itemData != null	&& Util.isEmpty(Util.trim(itemData.getChkResult()))){
						loss = true;
					}
				}
				if (loss) {
					errMsg.add(MessageFormat.format(pop.getProperty("lms2415.ChkMsg04"),
							itemSeq, getClass()));	//覆審項目第{0}項之附表欄位不得空白
				}
			}

		}
		if (CollectionUtils.isNotEmpty(lossList)) {
			List<String> listWithoutDuplicates = new ArrayList<String>(
				      new LinkedHashSet<String>(lossList));
			errMsg.add(MessageFormat.format(pop.getProperty("lms2415.ChkMsg02"),
					StringUtils.join(listWithoutDuplicates, "、"), getClass()));
		}
		if (errMsg.size() > 0) {
			return StringUtils.join(errMsg, "<br/>");
		} else {
			return "";
		}
	}
	
	/**
	 * 產生新的覆審報告表資料
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult produceNewC241M01A(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String custId = params.getString("custId");
		String dupNo = params.getString("dupNo");
		Map<String, Object> custDataMap = misCustdataService.findCNameByIdDupNo(custId, dupNo);
		String custName = CapString.trimNull(custDataMap.get("CNAME"));
		C241M01A c241m01a = new C241M01A();
		c241m01a = service.produceNewC241M01A(custId, dupNo, custName);

		result.set("C241M01AForm", DataParse.toResult(c241m01a));
		result.set(EloanConstants.OID, c241m01a.getOid());
		result.set(EloanConstants.MAIN_OID, c241m01a.getOid());
		result.set(EloanConstants.MAIN_ID, c241m01a.getMainId());

		return result;
	}
	
	/**
	 * 覆審報告表刪除註記
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult deleteMarkC241M01A(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oids = params.getString("oids");
		
		boolean edit = false;
		C241M01A c241m01a = service.findModelByOid(C241M01A.class, oids);
		List<DocOpener> docOpeners = docCheckService.findByMainId(c241m01a.getMainId());
		for(DocOpener docOpener : docOpeners){
			if(OpenTypeCode.Writing.getCode().equals(docOpener.getOpenType())){
				HashMap<String, String> hm = new HashMap<String, String>();
				hm.put("userId", docOpener.getOpener());
				hm.put("userName",
						userInfoService.getUserName(docOpener.getOpener()));
				edit = true;
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMessage("EFD0009", hm));
				break;
			}
		}
		if(!edit){
			service.deleteC241M01AMainMark(oids);
			// EFD0019=刪除成功
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0019"));
		}
		return result;
	}
	
	/**
	 * <pre>
	 * 儲存與工作底稿的連結
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(AuthType.Query)
	public IResult saveC240M01B(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String c240MainId = params.getString("c240MainId");
		String c241MainId = params.getString("c241MainId");
		service.saveLinkToC240M01B(c240MainId, c241MainId);
		return result;
	}
	
	/**
	 * <pre>
	 * 覆審報告查詢工作底稿
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(AuthType.Query)
	public IResult queryC240M01A(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);
		C241M01A c241m01a = service.findModelByOid(C241M01A.class, oid);
		if (c241m01a.getC240m01b() != null) {
			result.set("c240m01aMainIds", c241m01a.getC240m01b().getMainId());
		}
		return result;
	}
	
}
