/* 
 * IDataBean.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.report;

/**
 * <pre>
 * 所有DataBean的公用介面。
 * </pre>
 * 
 * @since 2011/6/20
 * <AUTHOR> Wang
 * @version <ul>
 *          <li>2011/6/20,Sunkist,new
 *          </ul>
 */
public interface IDataBean {

    /**
     * 欄。
     * 
     * @return String[]
     */
    String[] getColumns();

    /**
     * 值。
     * 
     * @return String[][]
     */
    Object[][] getData();
}
