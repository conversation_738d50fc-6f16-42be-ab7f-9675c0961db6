/* 
 * C900M01FDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C900M01F;

/** 個金授信科目限額檔 **/
public interface C900M01FDao extends IGenericDao<C900M01F> {

	C900M01F findByOid(String oid);
	
	List<C900M01F> findByMainId(String mainId);
	
	C900M01F findByUniqueKey(String mainId, String lmtType, Integer lmtSeq);

	List<C900M01F> findByIndex01(String mainId, String lmtType, Integer lmtSeq);
	
	List<C900M01F> findByIndex02(String mainId, String lmtType);

	List<C900M01F> findByMainIdAndlmtType(String mainId, String lmtType);

	List<C900M01F> findByOids(String[] oids);
	
	void deleteByMainId(String mainId);
}