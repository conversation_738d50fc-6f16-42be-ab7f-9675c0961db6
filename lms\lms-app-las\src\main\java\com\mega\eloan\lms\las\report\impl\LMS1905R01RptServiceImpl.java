package com.mega.eloan.lms.las.report.impl;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

import com.iisigroup.cap.component.PageParameters;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.ReportGenerator;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.AbstractReportService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.las.report.LMS1905R01RptService;
import com.mega.eloan.lms.las.service.LMS1905Service;
import com.mega.eloan.lms.model.L192M01A;
import com.mega.eloan.lms.model.L192M01B;
import com.mega.eloan.lms.model.L192M01D;
import com.mega.eloan.lms.model.L192S01A;
import com.mega.eloan.lms.model.L192S02A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * 房貸工作底稿基本內容列印
 * 
 * <AUTHOR>
 * 
 */
@Service("lms1905r01rptservice")
public class LMS1905R01RptServiceImpl extends AbstractReportService implements
		LMS1905R01RptService {

	@Resource
	LMS1905Service lms1905Service;

	@Resource
	BranchService branchService;

	@Resource
	LMSService lmsService;

	@Override
	public String getReportTemplateFileName() {
		Locale locale = LocaleContextHolder.getLocale();
		if (locale == null)
			locale = Locale.getDefault();
		return "report/las/LMS1905R01_" + locale.toString() + ".rpt";
	}



	@Override
	public void setReportData(ReportGenerator rptGenerator,
			PageParameters params) {
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		setReport001(rptGenerator, mainOid);
	}

	@Override
	public void setReport001(ReportGenerator rptGenerator, String mainOid) {
		String balDate = "";

		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		DecimalFormat df = new DecimalFormat("###,###,###,###,###,###,###,###");

		// String mainOid = params.getString(EloanConstants.MAIN_OID);
		L192M01A meta = lms1905Service.getL192M01A(mainOid);
		
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		String caseBrId = "";
		if(meta != null){
			 caseBrId = meta.getOwnBrId();
		}else{
			 caseBrId = user.getUnitNo();
		}

		String logoPath = lmsService.getLogoShowPath(UtilConstants.RPTPicType.兆豐LOGO,"00",caseBrId);
		
		
		
		// -----------------------------------第一區段資料開始，列印受檢單位，基準日，檢查日等基本資料------------------------------
		Map<String, String> values = reNewHashMapParams();
		List<Map<String, String>> list = new ArrayList<Map<String, String>>();

		values.put("ReportBean.column40", "section1");
		
		list.add(values);
		
		// -----------------------------------第一區段資料結束---------------------------------------------------------

		// -----------------------------------第二區段資料開始，列印借款人及連保人基本資料----------------------------------

		Set<L192M01B> l192m01bs = meta.getL192m01bs();// 取得借款人 保證人資料
		// 共同借款人
		List<L192M01B> main = new ArrayList<L192M01B>();
		// 連帶保證人
		List<L192M01B> notMain = new ArrayList<L192M01B>();

		for (L192M01B data : l192m01bs) {
			if ("1".equals(data.getCustType())) {
				// 共同借款人
				main.add(data);
			} else if ("2".equals(data.getCustType())) {
				// 連帶保證人
				notMain.add(data);
			}
		}
		
		for (int i = 0; i < main.size(); i++) {
			L192M01B tmp = main.get(i);
			if (Util.equals(tmp.getCustId(), meta.getCustId())
					&& Util.equals(tmp.getDupNo(), meta.getDupNo())) {
				main.remove(i);
				main.add(0, tmp);
				break;
			}
		}

		// 先判斷借款人 連保人 誰的筆數比較多，決定總共要列印幾列資料
		int biggestCount = main.size() >= notMain.size() ? main.size()
				: notMain.size();

		values = reNewHashMapParams();
		values.put("ReportBean.column39", "first");
		
		for (int i = 0; i < biggestCount; i++) {
			values = values == null ? reNewHashMapParams() : values;
			values.put("ReportBean.column40", "part1");
			if (i < main.size()) {
				L192M01B l192m01b = main.get(i);
				values.put("ReportBean.column01", l192m01b.getCustId());
				values.put("ReportBean.column02", l192m01b.getCustName());
				values.put("ReportBean.column03",
						l192m01b.getPosi() == null ? "" : l192m01b.getPosi());

				values.put("ReportBean.column04",
						l192m01b.getIncomeAmt() == null ? "" : l192m01b
								.getIncomeAmt().toString());
			}

			if (i < notMain.size()) {
				L192M01B l192m01b = notMain.get(i);
				values.put("ReportBean.column05", l192m01b.getCustId());
				values.put("ReportBean.column06", l192m01b.getCustName());
				values.put("ReportBean.column07",
						l192m01b.getPosi() == null ? "" : l192m01b.getPosi());

				values.put("ReportBean.column08",
						l192m01b.getIncomeAmt() == null ? "" : l192m01b
								.getIncomeAmt().toString());
			}
			
			
			list.add(values);
			values = null;
		}
		// -----------------------------------第二區段資料結束--------------------------------------------------------

		// -----------------------------------第三區段資料開始，列印主要借款人基本資料--------------------------------------------------------
		values = reNewHashMapParams();
		values.put("ReportBean.column40", "section2");
		
		list.add(values);
		// -----------------------------------第三區段資料結束--------------------------------------------------------

		// -----------------------------------第四區段資料開始，列印申請內容資料--------------------------------------------------------
		Set<L192S01A> l192s01as = meta.getL192s01as();// 取得申請內容

		values = reNewHashMapParams();
		values.put("ReportBean.column39", "first");

		int l192s01aCount = 0;
		for (L192S01A l192s01a : l192s01as) {

			balDate = l192s01a.getBalDate() == null ? "" : sdf.format(l192s01a
					.getBalDate());

			l192s01aCount++;
			values = values == null ? reNewHashMapParams() : values;
			values.put("ReportBean.column50", String.valueOf(l192s01aCount));
			values.put("ReportBean.column40", "part2");
			values.put("ReportBean.column11", l192s01a.getAccNo()); // 帳號
			values.put("ReportBean.column12", l192s01a.getQuotaCurr());// 額度幣別
			values.put(
					"ReportBean.column13",
					l192s01a.getQuotaAmt() == null ? "" : df.format(l192s01a
							.getQuotaAmt()));// 額度
			values.put("ReportBean.column14", l192s01a.getBalCurr());// 餘額幣別

			values.put("ReportBean.column15", l192s01a.getBalAmt() == null ? ""
					: df.format(l192s01a.getBalAmt()));// 餘額
			values.put(
					"ReportBean.column16",
					l192s01a.getAppDate() == null ? "" : sdf.format(l192s01a
							.getAppDate()));// 申請日
			values.put(
					"ReportBean.column17",
					l192s01a.getSignDate() == null ? "" : sdf.format(l192s01a
							.getSignDate()));// 簽辦日
			values.put(
					"ReportBean.column18",
					l192s01a.getUseDate() == null ? "" : sdf.format(l192s01a
							.getUseDate()));// 動用日
			values.put(
					"ReportBean.column19",
					l192s01a.getFromDate() == null ? "" : sdf.format(l192s01a
							.getFromDate()));// 動用起日
			values.put(
					"ReportBean.column20",
					l192s01a.getEndDate() == null ? "" : sdf.format(l192s01a
							.getEndDate()));// 動用迄日
			values.put("ReportBean.column21", l192s01a.getWay());// 進帳方式
			values.put("ReportBean.column22", l192s01a.getAppr());// 核准者
			
			list.add(values);
			values = null;
		}
		// ------------------------------------第四區段資料結束--------------------------------------------------------
		// ------------------------------------第五區段資料開始，列印擔保品內容-------------------------------------------------------
		Set<L192S02A> l192s02as = meta.getL192s02as();// 取得擔保品資料
		values = reNewHashMapParams();
		values.put("ReportBean.column39", "first");

		int l192s02aCount = 0;
		for (L192S02A l192s02a : l192s02as) {
			l192s02aCount++;
			values = values == null ? reNewHashMapParams() : values;
			values.put("ReportBean.column50", String.valueOf(l192s02aCount));
			values.put("ReportBean.column40", "part3");
			values.put("ReportBean.column30", l192s02a.getGteName());// 擔保品名稱
			values.put(
					"ReportBean.column31",
					l192s02a.getEstDate() == null ? "" : sdf.format(l192s02a
							.getEstDate()));// 鑑價日期
			values.put("ReportBean.column32", l192s02a.getLoanCurr());// 押值幣別
			values.put(
					"ReportBean.column33",
					l192s02a.getLoanAmt() == null ? "" : df.format(l192s02a
							.getLoanAmt()));// 押值
			values.put("ReportBean.column34", l192s02a.getSetCurr());// 設定金額幣別
			values.put("ReportBean.column35", l192s02a.getSetAmt() == null ? ""
					: df.format(l192s02a.getSetAmt()));// 設定金額
			values.put(
					"ReportBean.column36",
					l192s02a.getSetDate() == null ? "" : sdf.format(l192s02a
							.getSetDate()));// 設定日期
			values.put("ReportBean.column37", String.valueOf(l192s02a
					.getSetPosition() == null ? "" : l192s02a.getSetPosition()));// 設定順位
			values.put("ReportBean.column38", l192s02a.getInsurance());// 險種
			values.put("ReportBean.column41", l192s02a.getOwner());// 所有權人
			
			list.add(values);
			values = null;
		}
		// ------------------------------------第五區段資料結束--------------------------------------------------------

		// ------------------------------------第六區段資料開始，列印查核事項內容-------------------------------------------------------
		values = reNewHashMapParams();
		values.put("ReportBean.column40", "section3");
		
		list.add(values);
		// ------------------------------------第六區段資料結束--------------------------------------------------------

		// ------------------------------------第七區段資料開始，列印檢查事項內容-------------------------------------------------------
		values = reNewHashMapParams();
		values.put("ReportBean.column40", "section4");
		
		list.add(values);
		// ------------------------------------第七區段資料結束--------------------------------------------------------

		// ------------------------------------第八區段資料開始，列印處理意見內容-------------------------------------------------------
		values = reNewHashMapParams();
		values.put("ReportBean.column40", "section5");
		
		list.add(values);
		// ------------------------------------第八區段資料結束--------------------------------------------------------

		rptGenerator.setRowsData(list); // set report Bean data

		Map<String, String> prompts = new HashMap<String, String>();
		prompts.put("custName", meta.getCustName());
		prompts.put("ownBrId", meta.getOwnBrId());
		prompts.put("ownBrName", branchService.getBranchName(meta.getOwnBrId()));
		prompts.put(
				"checkBase",
				meta.getCheckBase() == null ? "" : sdf.format(meta
						.getCheckBase()));
		prompts.put(
				"checkDate",
				meta.getCheckDate() == null ? "" : sdf.format(meta
						.getCheckDate()));
		prompts.put("tNo", meta.getTNo());
		prompts.put("wpNo", meta.getWpNo());
		prompts.put("checkMan", meta.getCheckMan());
		prompts.put("leader", meta.getLeader());
		prompts.put("mtDoc", meta.getMtDoc());
		prompts.put("tAddr", meta.getTAddr());
		prompts.put("tTel", meta.getTTel());
		prompts.put("cdQ1", meta.getCdQ1());
		prompts.put("cdQ2", meta.getCdQ2());

		L192M01D l192m01d = meta.getL192m01d();
		prompts.put("ck1", l192m01d.getCk1());
		prompts.put("ck2", l192m01d.getCk2());
		prompts.put(
				"ck3Date",
				l192m01d.getCk3Date() == null ? "" : sdf.format(l192m01d
						.getCk3Date()));
		prompts.put("ck4", l192m01d.getCk4());
		prompts.put("ck5", l192m01d.getCk5());
		prompts.put("ck6", l192m01d.getCk6());

		prompts.put("userItem1", l192m01d.getUserItem1());
		prompts.put("userItem2", l192m01d.getUserItem2());
		prompts.put("userItem3", l192m01d.getUserItem3());

		prompts.put("userCk1", l192m01d.getUserCk1());
		prompts.put("userCk2", l192m01d.getUserCk2());
		prompts.put("userCk3", l192m01d.getUserCk3());

		prompts.put("processComm", meta.getProcessComm());
		prompts.put("gist", meta.getGist());
		prompts.put("randomCode", meta.getRandomCode());

		prompts.put("balDate", balDate);
		prompts.put("custId", meta.getCustId());

		prompts.put("LOGOSHOW", logoPath);
		
		rptGenerator.setVariableData(prompts);

		rptGenerator.setTestMethod(false);

	}

	/**
	 * 初始化map 資料，將所有的rportBean的資料初使化，避免少了而產生exception
	 * 
	 * @return
	 */
	private Map<String, String> reNewHashMapParams() {
		Map<String, String> values = new HashMap<String, String>();
		for (int i = 1; i <= 60; i++) {
			values.put("ReportBean.column" + String.format("%02d", i), "");
		}
		return values;
	}
}
