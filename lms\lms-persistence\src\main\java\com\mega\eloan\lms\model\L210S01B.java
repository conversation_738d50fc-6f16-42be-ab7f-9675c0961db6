/* 
 * L210S01B.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;

/** 同業聯貸攤貸比率檔 **/
@Entity
// @EntityListeners({ DocumentModifyListener.class })
@Table(name = "L210S01B", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "seq", "chgFlag" }))
public class L210S01B extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 序號 **/
	@Column(name = "SEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer seq;

	/**
	 * 變更前/變更後
	 * <p/>
	 * 1.變更前, 2.變更後
	 */
	@Column(name = "CHGFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String chgFlag;

	/**
	 * 參貸行庫種類
	 * <p/>
	 * 勾選項目：<br/>
	 * 01-本國銀行<br/>
	 * 02-外國銀行在台分行<br/>
	 * 03-信託投資公司<br/>
	 * 06-產物及人壽保險公司<br/>
	 * 12-國外銀行
	 */
	@Column(name = "SLBANKTYPE", length = 2, columnDefinition = "VARCHAR(02)")
	private String slBankType;

	/**
	 * 參貸行庫代碼
	 * <p/>
	 * ※「12-國外銀行」代碼為6碼<br/>
	 * 現行資料來源：<br/>
	 * 除「12-國外銀行」為DB2外，其餘於Notes
	 */
	@Column(name = "SLBANK", length = 6, columnDefinition = "VARCHAR(06)")
	private String slBank;

	/** 參貸行庫名稱 **/
	@Column(name = "SLBANKCN", length = 60, columnDefinition = "VARCHAR(60)")
	private String slBankCN;

	/**
	 * 參貸行庫分行代碼
	 * <p/>
	 * 為其他行庫或本行之營業單位
	 */
	@Column(name = "SLBRANCH", length = 7, columnDefinition = "VARCHAR(07)")
	private String slBranch;

	/** 參貸行庫分行名稱 **/
	@Column(name = "SLBRANCHCN", length = 60, columnDefinition = "VARCHAR(60)")
	private String slBranchCN;

	/**
	 * 是否為共同主辦行
	 * <p/>
	 * Y/N
	 */
	@Column(name = "SLMASTER", length = 1, columnDefinition = "VARCHAR(1)")
	private String slMaster;

	/** 同業帳號 **/
	@Column(name = "SLACCNO", length = 14, columnDefinition = "VARCHAR(14)")
	private String slAccNo;

	/** 參貸幣別 **/
	@Column(name = "SLCURR", length = 3, columnDefinition = "VARCHAR(3)")
	private String slCurr;

	/** 參貸金額 **/
	@Column(name = "SLAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal slAmt;

	/** 建立人員號碼 **/
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Date updateTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得序號 **/
	public Integer getSeq() {
		return this.seq;
	}

	/** 設定序號 **/
	public void setSeq(Integer value) {
		this.seq = value;
	}

	/**
	 * 取得變更前/變更後
	 * <p/>
	 * 1.變更前, 2.變更後
	 */
	public String getChgFlag() {
		return this.chgFlag;
	}

	/**
	 * 設定變更前/變更後
	 * <p/>
	 * 1.變更前, 2.變更後
	 **/
	public void setChgFlag(String value) {
		this.chgFlag = value;
	}

	/**
	 * 取得參貸行庫種類
	 * <p/>
	 * 勾選項目：<br/>
	 * 01-本國銀行<br/>
	 * 02-外國銀行在台分行<br/>
	 * 03-信託投資公司<br/>
	 * 06-產物及人壽保險公司<br/>
	 * 12-國外銀行
	 */
	public String getSlBankType() {
		return this.slBankType;
	}

	/**
	 * 設定參貸行庫種類
	 * <p/>
	 * 勾選項目：<br/>
	 * 01-本國銀行<br/>
	 * 02-外國銀行在台分行<br/>
	 * 03-信託投資公司<br/>
	 * 06-產物及人壽保險公司<br/>
	 * 12-國外銀行
	 **/
	public void setSlBankType(String value) {
		this.slBankType = value;
	}

	/**
	 * 取得參貸行庫代碼
	 * <p/>
	 * ※「12-國外銀行」代碼為6碼<br/>
	 * 現行資料來源：<br/>
	 * 除「12-國外銀行」為DB2外，其餘於Notes
	 */
	public String getSlBank() {
		return this.slBank;
	}

	/**
	 * 設定參貸行庫代碼
	 * <p/>
	 * ※「12-國外銀行」代碼為6碼<br/>
	 * 現行資料來源：<br/>
	 * 除「12-國外銀行」為DB2外，其餘於Notes
	 **/
	public void setSlBank(String value) {
		this.slBank = value;
	}

	/** 取得參貸行庫名稱 **/
	public String getSlBankCN() {
		return this.slBankCN;
	}

	/** 設定參貸行庫名稱 **/
	public void setSlBankCN(String value) {
		this.slBankCN = value;
	}

	/**
	 * 取得參貸行庫分行代碼
	 * <p/>
	 * 為其他行庫或本行之營業單位
	 */
	public String getSlBranch() {
		return this.slBranch;
	}

	/**
	 * 設定參貸行庫分行代碼
	 * <p/>
	 * 為其他行庫或本行之營業單位
	 **/
	public void setSlBranch(String value) {
		this.slBranch = value;
	}

	/** 取得參貸行庫分行名稱 **/
	public String getSlBranchCN() {
		return this.slBranchCN;
	}

	/** 設定參貸行庫分行名稱 **/
	public void setSlBranchCN(String value) {
		this.slBranchCN = value;
	}

	/**
	 * 取得是否為共同主辦行
	 * <p/>
	 * Y/N
	 */
	public String getSlMaster() {
		return this.slMaster;
	}

	/**
	 * 設定是否為共同主辦行
	 * <p/>
	 * Y/N
	 **/
	public void setSlMaster(String value) {
		this.slMaster = value;
	}

	/** 取得同業帳號 **/
	public String getSlAccNo() {
		return this.slAccNo;
	}

	/** 設定同業帳號 **/
	public void setSlAccNo(String value) {
		this.slAccNo = value;
	}

	/** 取得參貸幣別 **/
	public String getSlCurr() {
		return this.slCurr;
	}

	/** 設定參貸幣別 **/
	public void setSlCurr(String value) {
		this.slCurr = value;
	}

	/** 取得參貸金額 **/
	public BigDecimal getSlAmt() {
		return this.slAmt;
	}

	/** 設定參貸金額 **/
	public void setSlAmt(BigDecimal value) {
		this.slAmt = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}
}
