package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/** <pre>DW_LNCUSTBR檔
 * </pre>
 * 
 * @since 2019/02
 * <AUTHOR>
 * @version <ul>
 *          <li>2019/02, J-107-0129 , 在 SLMS-00074 將 DW 的資料抄寫到  ELOANDB
 *          </li>
 *          </ul>
 */
@Entity
@Table(name="C900S03D", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class C900S03D extends GenericBean implements IDataObject {

	private static final long serialVersionUID = 1L;
	
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(unique = true, nullable = false, length = 32, columnDefinition = "CHAR(32)")
	private String oid;
	
	@Temporal(TemporalType.DATE)
	@Column(name = "CYC_MN", columnDefinition = "DATE")
	private Date cyc_mn;               
	
	@Column(name="CUST_KEY", length=11, columnDefinition="CHAR(11)")
	private String cust_key;
	
	@Column(name="BRNO", length=3, columnDefinition="CHAR(3)")
	private String brNo;
	
	public String getOid() {
		return this.oid;
	}
	
	public void setOid(String value) {
		this.oid = value;
	}
	
	public Date getCyc_mn() {
		return cyc_mn;
	}

	public void setCyc_mn(Date cyc_mn) {
		this.cyc_mn = cyc_mn;
	}

	public String getCust_key() {
		return cust_key;
	}

	public void setCust_key(String cust_key) {
		this.cust_key = cust_key;
	}

	public String getBrNo() {
		return brNo;
	}

	public void setBrNo(String brNo) {
		this.brNo = brNo;
	}
	
}

