/* 
 * CLSS02CPanel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.panels;

import com.mega.eloan.common.panels.Panel;

/**<pre>
 * 個金借款人
 * </pre>
 * @since  2012/11/14
 * <AUTHOR>
 * @version <ul>
 *           <li>2012/11/14,<PERSON>,new
 *          </ul>
 */
public class CLSS02CPanel extends Panel {

	public CLSS02CPanel(String id) {
		super(id);
	}

	public CLSS02CPanel(String id, boolean updatePanelName) {
		super(id, updatePanelName);
	}

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

}
