package tw.com.jcs.common;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.nio.ByteBuffer;
import java.nio.CharBuffer;
import java.nio.charset.Charset;
import java.nio.charset.CharsetDecoder;
import java.nio.charset.CodingErrorAction;
import java.sql.Clob;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.DateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import javax.persistence.Column;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import net.sf.cglib.beans.BeanMap;

/**
 * <pre>
 * 這是一個公用程式，提供通用性的處理函數
 * </pre>
 * 
 * @since 2023年1月7日
 * <AUTHOR> Software Inc.
 * @version
 *          <ul>
 *          <li>2023年1月7日,JCS Software Inc.
 *          </ul>
 */
public class Util {

    private static final Logger logger = LoggerFactory.getLogger(Util.class);

    public static final int BUFFER_SIZE = 1024;

    /**
     * 去除前後的空白字元
     * 
     * @param str
     */
    public static String trim(Object value) {
        return value == null ? "" : trim(value.toString());
    }

    /**
     * 去除前後的空白字元
     * 
     * @param str
     */
    public static String trim(String str) {
        if (str == null)
            return "";
        return str.trim();
    }

    /**
     * 是否為空值
     * 
     * @param str
     * @return true:空值 false:不是空值
     */
    public static boolean isEmpty(String str) {
        return (null == str || str.trim().length() == 0);
    }

    /**
     * 是否為空值
     * 
     * @param str
     * @return true:空值 false:不是空值
     */
    public static boolean isEmpty(Object value) {
        return (null == value ? true : isEmpty(nullToSpace(value.toString())));
    }

    /**
     * 是否不為空值
     * 
     * @param str
     * @return true:空值 false:不是空值
     */
    public static boolean isNotEmpty(Object value) {
        return !isEmpty(value);
    }

    /**
     * NULL 轉為空白.
     * 
     * @param text
     *            要執行的字串.
     */
    public static String nullToSpace(String text) {
        return text == null || "null".equals(text) ? "" : trim(text);
    }

    /**
     * NULL 轉為空白.
     * 
     * @param Object
     */
    public static String nullToSpace(Object obj) {
        return (obj != null) ? trim(obj.toString()) : "";
    }

    /**
     * 將含有半形的字元轉為全形.
     * 
     * @param text
     *            String 欲轉換的字串
     * @return String 全形的字串
     */
    public static String toFullCharString(String text) {
        if (text == null)
            return "";
        StringBuffer sb = new StringBuffer();
        try {
            for (int i = 0; i < text.length(); i++) {
                if (isFullChar(text.substring(i, i + 1))) {
                    sb.append(text.substring(i, i + 1));
                } else {
                    sb.append(charSemitoFull(text.charAt(i)));
                }
            }

        } catch (Exception e) {
            return null;
        }
        return sb.toString();
    }

    /**
     * 將全形英數字轉為半形的字元.
     * 
     * @param text
     *            String 欲轉換的字串
     * @return String 半形的字串
     * @since 2008-05-08
     */
    public static String toSemiCharString(String text) {
        if (text == null)
            return "";
        StringBuffer sb = new StringBuffer();
        try {
            for (int i = 0; i < text.length(); i++) {
                if (isFullChar(text.substring(i, i + 1))) {
                    sb.append(charFulltoSemi(text.charAt(i)));
                } else {
                    sb.append(text.substring(i, i + 1));
                }
            }

        } catch (Exception e) {
            return null;
        }
        return sb.toString();
    }

    /**
     * 判斷此字元是否為中文字.
     * 
     * @param text
     *            String
     * @return boolean
     */
    public static boolean isChineseChar(String text) {
        try {
            char ch = text.charAt(0);
            String strHex = Integer.toHexString((int) ch);
            if (strHex.length() != 4) // 長度不為4
                return false;
            else if (("ff".equals(strHex.substring(0, 2)))) // 全形非國字
                return false;
            else
                return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 判斷此字元是否為全型字.
     * 
     * @param text
     *            String
     * @return boolean
     */
    private static boolean isFullChar(String text) {
        try {
            String strHex = Integer.toHexString((int) text.charAt(0));
            return (strHex.length() == 4) ? true : false;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 對單一字元進行全形轉半形.
     * 
     * @param ch
     *            char
     * @return char
     */
    private static char charFulltoSemi(char ch) {
        char chSemi = '0';

        try {
            int iChar = (int) ch;

            if ((iChar >= 65296 && iChar <= 65305)) {
                // 數字(Number)
                iChar = iChar - 65296 + 48;
                String after = String.valueOf(Integer.toHexString(iChar));
                chSemi = (char) Integer.parseInt(after, 16);

            } else if ((iChar >= 65345 && iChar <= 65370)) {
                // 小寫英文字母(Alphabet)
                iChar = iChar - 65345 + 97;
                String after = String.valueOf(Integer.toHexString(iChar));
                chSemi = (char) Integer.parseInt(after, 16);
            } else if ((iChar >= 65313 && iChar <= 65338)) {
                // 大寫英文字母(Alphabet)
                iChar = iChar - 65313 + 65;
                String after = String.valueOf(Integer.toHexString(iChar));
                chSemi = (char) Integer.parseInt(after, 16);
            } else {
                // 特殊符號
                switch (iChar) {
                case 65281:
                    chSemi = (char) 33;
                    break;
                case 8221: // 22 " A1A8 ” 201D
                    chSemi = (char) 34;
                    break;
                case 65283: // 23 # A1AD ＃ FF03
                    chSemi = (char) 35;
                    break;
                case 65284: // 24 $ A243 ＄ FF04
                    chSemi = (char) 36;
                    break;
                case 65285: // 25 % A248 ％ FF05
                    chSemi = (char) 37;
                    break;
                case 65286: // 26 & A1AE ＆ FF06
                    chSemi = (char) 38;
                    break;
                case 8217: // 27 ' A1A6 ’ 2019
                    chSemi = (char) 39;
                    break;
                case 65288: // 28 ( A15D （ FF08
                    chSemi = (char) 40;
                    break;
                case 65289: // 29 ) A15E ） FF09
                    chSemi = (char) 41;
                    break;
                case 65290: // 2A * A1AF ＊ FF0A
                    chSemi = (char) 42;
                    break;
                case 65291: // 2B + A1CF ＋ FF0B
                    chSemi = (char) 43;
                    break;
                case 65292: // 2C , A141 ， FF0C
                    chSemi = (char) 44;
                    break;
                case 65293: // 2D - A1D0 － FF0D
                    chSemi = (char) 45;
                    break;
                case 65294: // 2E . A144 ．FF0E
                    chSemi = (char) 46;
                    break;
                case 65295: // 2F / A1FE ∕ FF0F
                    chSemi = (char) 47;
                    break;
                case 65306: // 3A : A147 ： FF1A
                    chSemi = (char) 58;
                    break;
                case 65307: // 3B ; A146 ； FF1B
                    chSemi = (char) 59;
                    break;
                case 65308: // 3C < A1D5 ＜ FF1C
                    chSemi = (char) 60;
                    break;
                case 65309: // 3D = A1D7 ＝ FF1D
                    chSemi = (char) 61;
                    break;
                case 65310: // 3E > A1D6 ＞ FF1E
                    chSemi = (char) 62;
                    break;
                case 65311: // 3F ? A148 ？ FF1F
                    chSemi = (char) 63;
                    break;
                case 65312: // 40 @ A249 ＠ FF20
                    chSemi = (char) 64;
                    break;
                case 65340: // 5C \ A240 ﹨ FF3C
                    chSemi = (char) 92;
                    break;
                case 65343: // 5F _ A1C4 ＿ FF3F
                    chSemi = (char) 95;
                    break;
                case 65371: // 7B { A1A1 ﹛ FF5B
                    chSemi = (char) 123;
                    break;
                case 65372: // 7C | A155 ｜ FF5C
                    chSemi = (char) 124;
                    break;
                case 65373: // 7D } A1A2 ﹜ FF5D
                    chSemi = (char) 125;
                    break;
                case 65374: // 7E ~ A1E3 ? FF5E
                    chSemi = (char) 126;
                    break;
                case 12288: // 全型空白轉半型
                    chSemi = (char) 32;
                    break;
                default:
                    chSemi = ch;
                }
            }

        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        return chSemi;
    }

    /**
     * 對單一字元進行半形轉全形 .
     * 
     * @param ch
     *            char
     * @return char
     */
    private static char charSemitoFull(char ch) {
        char chFull = '0';
        try {
            int iChar = (int) ch;
            // 英文字母(Alphabet) && 數字(Number)
            if ((iChar >= 48 && iChar <= 57) || (iChar >= 65 && iChar <= 90) || (iChar >= 97 && iChar <= 122)) {
                iChar -= 32;
                String after = "ff" + String.valueOf(Integer.toHexString(iChar));
                chFull = (char) Integer.parseInt(after, 16);
            } else {
                // 特殊符號
                switch (iChar) {
                case 33: // 21 ! A149 ！ FF01
                    chFull = (char) Integer.parseInt("FF01", 16);
                    break;
                case 34: // 22 " A1A8 ” 201D
                    chFull = (char) Integer.parseInt("201D", 16);
                    break;
                case 35: // 23 # A1AD ＃ FF03
                    chFull = (char) Integer.parseInt("FF03", 16);
                    break;
                case 36: // 24 $ A243 ＄ FF04
                    chFull = (char) Integer.parseInt("FF04", 16);
                    break;
                case 37: // 25 % A248 ％ FF05
                    chFull = (char) Integer.parseInt("FF05", 16);
                    break;
                case 38: // 26 & A1AE ＆ FF06
                    chFull = (char) Integer.parseInt("FF06", 16);
                    break;
                case 39: // 27 ' A1A6 ’ 2019
                    chFull = (char) Integer.parseInt("2019", 16);
                    break;
                case 40: // 28 ( A15D （ FF08
                    chFull = (char) Integer.parseInt("FF08", 16);
                    break;
                case 41: // 29 ) A15E ） FF09
                    chFull = (char) Integer.parseInt("FF09", 16);
                    break;
                case 42: // 2A * A1AF ＊ FF0A
                    chFull = (char) Integer.parseInt("FF0A", 16);
                    break;
                case 43: // 2B + A1CF ＋ FF0B
                    chFull = (char) Integer.parseInt("FF0B", 16);
                    break;
                case 44: // 2C , A141 ， FF0C
                    chFull = (char) Integer.parseInt("FF0C", 16);
                    break;
                case 45: // 2D - A1D0 － FF0D
                    chFull = (char) Integer.parseInt("FF0D", 16);
                    break;
                case 46: // 2E . A144 ．FF0E
                    chFull = (char) Integer.parseInt("FF0E", 16);
                    break;
                case 47: // 2F / A1FE ∕ FF0F
                    chFull = (char) Integer.parseInt("FF0F", 16);
                    break;
                case 58: // 3A : A147 ： FF1A
                    chFull = (char) Integer.parseInt("FF1A", 16);
                    break;
                case 59: // 3B ; A146 ； FF1B
                    chFull = (char) Integer.parseInt("FF1B", 16);
                    break;
                case 60: // 3C < A1D5 ＜ FF1C
                    chFull = (char) Integer.parseInt("FF1C", 16);
                    break;
                case 61: // 3D = A1D7 ＝ FF1D
                    chFull = (char) Integer.parseInt("FF1D", 16);
                    break;
                case 62: // 3E > A1D6 ＞ FF1E
                    chFull = (char) Integer.parseInt("FF1E", 16);
                    break;
                case 63: // 3F ? A148 ？ FF1F
                    chFull = (char) Integer.parseInt("FF1F", 16);
                    break;
                case 64: // 40 @ A249 ＠ FF20
                    chFull = (char) Integer.parseInt("FF20", 16);
                    break;
                case 92: // 5C \ A240 ﹨ FF3C
                    chFull = (char) Integer.parseInt("FF3C", 16);
                    break;
                case 95: // 5F _ A1C4 ＿ FF3F
                    chFull = (char) Integer.parseInt("FF3F", 16);
                    break;
                case 123: // 7B { A1A1 ﹛ FF5B
                    chFull = (char) Integer.parseInt("FF5B", 16);
                    break;
                case 124: // 7C | A155 ｜ FF5C
                    chFull = (char) Integer.parseInt("FF5C", 16);
                    break;
                case 125: // 7D } A1A2 ﹜ FF5D
                    chFull = (char) Integer.parseInt("FF5D", 16);
                    break;
                case 126: // 7E ~ A1E3 ? FF5E
                    chFull = (char) Integer.parseInt("FF5E", 16);
                    break;
                case 91: // 5B [ FF3B No corresponding
                case 93: // 5D ] FF3D No corresponding
                case 94: // 5E ^ FF3E No corresponding
                case 96: // 60 ` FF40 No corresponding
                default: // No corresponding 塞 全型空白
                    chFull = (char) Integer.parseInt("3000", 16);
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return chFull;
    }

    /** 將byte[] 轉為整數 */
    public static int decoder(byte[] a) {
        int a0, a1, a2, a3, ret;
        a0 = a[0];
        a1 = a[1];
        a2 = a[2];
        a3 = a[3];
        // we have to 'positivise' intermediate negative bytes to deceive
        // Java implicit conversion behavior
        if (a3 < 0) {
            a3 = a3 << 24;
            a3 = a3 >>> 24;
        }
        if (a2 < 0) {
            a2 = a2 << 24;
            a2 = a2 >>> 24;
        }
        if (a1 < 0) {
            a1 = a1 << 24;
            a1 = a1 >>> 24;
        }
        // we can't do long shift for bytes because they are only 8 bit length
        ret = (a0 << 24 | a1 << 16 | a2 << 8 | a3);
        return ret;
    }

    /** 將整數轉為byte[] */
    public static byte[] converter(int n) {
        byte[] seq_num = new byte[4];
        seq_num[0] = (byte) ((n & 0xff000000) >>> 24);
        seq_num[1] = (byte) ((n & 0x00ff0000) >>> 16);
        seq_num[2] = (byte) ((n & 0x0000ff00) >>> 8);
        seq_num[3] = (byte) (n & 0x000000ff);
        return seq_num;
    }

    /**
     * 將字串截至符合 limit bytes數目的長度
     * 
     * @param src
     *            要截字數的字串
     * @param lim
     *            DB2的長度, -1 為 不設限制
     * @return 傳回截成符合長度的字串
     */
    public static String truncateString(String src, int lim) {
        if (null == src || src.length() == 0 || lim == 0)
            return "";
        if (lim < 0)
            return src;

        int bys = 0, cnt = -1;
        char[] chars = src.toCharArray();
        int len = chars.length;
        int i = 0;
        while (++cnt < len) {
            // modify by Vance, utf-8中文字每個是算三
            i = (chars[cnt] < 0x0100) ? 1 : 3; // test char is double byte
            bys += i;
            if (bys > lim)
                break;
        }

        return String.valueOf(chars, 0, cnt);
    } // end

    /**
     * 取得兩個數字的最大公因數(GCD)。
     * 
     * @param m
     * @param n
     * @return long
     * @throws ProjectException
     */
    public static long euclidGCD(long m, long n) {
        long r = m % n;
        while (r != 0) {
            m = n;
            n = r;
            r = m % n;
        }
        return n;
    }

    /**
     * 取得兩個分數相加(值會約分)。
     * 
     * @param a
     *            /b
     * @param c
     *            /d
     * @return String[0] 分子 String[1]分母
     * @throws ProjectException
     */
    public static String[] getaddhalf(long a, long b, long c, long d) {
        String[] half = new String[2];
        if (b == 0 && d != 0) {
            half[0] = String.valueOf(c);
            half[1] = String.valueOf(d);
        } else if (d == 0 && b != 0) {
            half[0] = String.valueOf(a);
            half[1] = String.valueOf(b);
        } else {
            long x = a * d + b * c;
            long y = b * d;
            long z = euclidGCD(x, y);
            if (z != 1) {
                x = x / z;
                y = y / z;
            }
            if (b == d && b > y && b % y == 0) {
                long t = b / y;
                x = t * x;
                y = b;
            }
            half[0] = String.valueOf(x);
            half[1] = String.valueOf(y);
        }
        return half;
    }

    /**
     * 取得兩個分數相減(值會約分)。
     * 
     * @param a
     *            /b
     * @param c
     *            /d
     * @return String[0] 分子 String[1]分母
     * @throws ProjectException
     */
    public static String[] getsubtracthalf(long a, long b, long c, long d) {
        String[] half = new String[2];
        if (c == 0) {
            half[0] = String.valueOf(a);
            half[1] = String.valueOf(b);
        } else {
            if ((a * d) >= (b * c)) {
                long x = a * d - b * c;
                long y = b * d;
                long z = euclidGCD(x, y);
                if (z != 1) {
                    x = x / z;
                    y = y / z;
                }
                half[0] = String.valueOf(x);
                half[1] = String.valueOf(y);
            }
        }
        return half;
    }

    /**
     * 讀檔案至字串.
     * 
     * @param filePath
     *            檔案名稱(含完整路徑)
     * @return 檔案內容
     * @throws java.io.IOException
     */
    public static String readFileAsString(String filePath) throws java.io.IOException {
        StringBuffer fileData = new StringBuffer(BUFFER_SIZE);
        BufferedReader reader = new BufferedReader(new FileReader(filePath));
        char[] buf = new char[BUFFER_SIZE];
        int numRead = 0;
        while ((numRead = reader.read(buf)) != -1) {
            fileData.append(buf, 0, numRead);
        }
        reader.close();
        return fileData.toString();
    }

    /**
     * 取得Bean的BeanMap，提供快速存取Bean欄位的能力
     * 
     * @param obj
     *            目的Bean
     * @return BeanMap for obj
     */
    public static BeanMap getBeanMap(Object obj) {
        return BeanMap.create(obj);
    }

    /**
     * 複製Bean
     * <p/>
     * 
     * 如果有指定要複製的欄位名，則只複製對應的欄位，否則就複製全部來源Bean的欄位
     * <p/>
     * P.S.不限定必須為相同類別的Bean<br/>
     * 只要有存在該欄位名，即進行複製，如不存在該欄位，則忽略
     * 
     * @param src
     *            來源Bean
     * @param dest
     *            目的Bean
     * @param names
     *            欄位名(可多個)
     * @param excludeNullValue
     *            是否排除null欄位
     */
    @SuppressWarnings("unchecked")
    public static void beanCopy(Object src, Object dest, String[] names, boolean excludeNullValue) {
        BeanMap srcMap = getBeanMap(src);
        BeanMap destMap = src.getClass() == dest.getClass() ? srcMap : getBeanMap(dest);

        if (names == null || names.length == 0) {
            Set<String> keys = (Set<String>) srcMap.keySet();
            names = keys.toArray(new String[keys.size()]);
        }

        for (String name : names) {
            Object value = srcMap.get(src, name);
            if (!(excludeNullValue && value == null)) {
                destMap.put(dest, name, value);
            }
        }
    }

    /**
     * 複製Bean
     * <p/>
     * 
     * 如果有指定要複製的欄位名，則只複製對應的欄位，否則就複製全部來源Bean的欄位
     * <p/>
     * P.S.不限定必須為相同類別的Bean<br/>
     * 只要有存在該欄位名，即進行複製，如不存在該欄位，則忽略
     * 
     * @param src
     *            來源Bean
     * @param dest
     *            目的Bean
     */
    public static void beanCopy(Object src, Object dest) {
        beanCopy(src, dest, null, false);
    }

    /**
     * 複製Bean
     * <p/>
     * 
     * 如果有指定要複製的欄位名，則只複製對應的欄位，否則就複製全部來源Bean的欄位
     * <p/>
     * P.S.不限定必須為相同類別的Bean<br/>
     * 只要有存在該欄位名，即進行複製，如不存在該欄位，則忽略
     * 
     * @param src
     *            來源Bean
     * @param dest
     *            目的Bean
     * @param excludeNullValue
     *            是否排除null欄位
     */
    public static void beanCopy(Object src, Object dest, boolean excludeNullValue) {
        beanCopy(src, dest, null, excludeNullValue);
    }

    /**
     * 對Bean進行設值
     * 
     * @param bean
     *            目的Bean
     * @param valueMap
     *            欄位名與值的Map
     */
    public static <T> T setBean(T bean, Map<String, ?> valueMap) {
        BeanMap map = getBeanMap(bean);
        map.putAll(valueMap);
        return bean;
    }

    /**
     * 對Bean進行設值
     * 
     * @param bean
     *            目的Bean
     * @param valueMap
     *            欄位名與值的Map
     */
    public static <T> T setBeanUpperCase(T bean, Map<String, ?> valueMap) {
        BeanMap map = getBeanMap(bean);
        for (Object obj : map.keySet()) {
            map.put(obj, valueMap.get(((String) obj).toUpperCase()));
        }
        return bean;
    }

    /**
     * 讀取ResultSet中的CLOB欄位.
     * 
     * @param rs
     *            - 含有 COLB 的 sql.ResultSet
     * @param column_name
     *            - 該 CLOB 欄位的名稱
     * @return String - CLOB 內的資料
     */
    public static String readClob(ResultSet rs, String column_name) {
        try {
            if (rs == null) {
                throw new SQLException("傳入的 ResultSet == null，請檢查程式！");
            }
            if (column_name == null || column_name.length() == 0) {
                throw new SQLException("傳入的 ResultSet column_name == null or column_name.length() == 0，請檢查程式！");
            }
            /* 獲取CLOB物件 */
            Clob clob = rs.getClob(column_name);
            if (clob == null) {
                return "";
            }

            /* 以字元形式輸出 */
            BufferedReader in = new BufferedReader(clob.getCharacterStream());
            StringBuffer sbRet = new StringBuffer((int) clob.length());
            int c;
            while ((c = in.read()) != -1) {
                sbRet.append((char) c);
            }
            in.close();
            return sbRet.toString();
        } catch (Exception e) {
            logger.error(e.getMessage());
            return "";
        }
    }

    /**
     * 產以逗點分隔的問號 ex: num=5 -> result: ?,?,?,?,?
     * 
     * @param num
     *            問號的數量
     */
    public static String createComma(int num) {
        StringBuilder buf = new StringBuilder();
        for (--num, buf.append('?'); num > 0; --num) {
            buf.append(",?");
        }
        return buf.toString();
    }

    /**
     * 取得相對於ClassPath路徑的資源
     * 
     * @param location
     *            ClassPath相對路徑
     */
    public static InputStream getClassPathResource(String location) {
        return Thread.currentThread().getContextClassLoader().getResourceAsStream(location);
    }

    /**
     * 複製檔案
     * 
     * @param src
     *            來源路徑
     * @param dest
     *            目的路徑
     * @return 處理結果
     */
    public static boolean copyFile(String src, String dest) {
        try {
            FileUtils.copyFile(new File(src), new File(dest));
        } catch (IOException e) {
            logger.error(e.getMessage());
            return false;
        }
        return true;
    }

    /**
     * 複製檔案
     * 
     * @param src
     *            來源檔案
     * @param dest
     *            目的檔案
     * @return 處理結果
     */
    public static boolean copyFile(File src, File dest) {
        try {
            FileUtils.copyFile(src, dest);
        } catch (IOException e) {
            logger.error(e.getMessage());
            return false;
        }
        return true;
    }

    /**
     * Ajax 中文亂碼轉換 <br/>
     * (for Ajax POST時傳中文問題,不用再用GET傳中文了) <br/>
     * 
     * @param input
     *            欲轉換之中文
     * <AUTHOR>
     */
    public static String parseChineseAjax(String input) {
        String s = null;

        if (input == null) {
            logger.warn("Warn:Chinese null founded!");
            return new String("");
        }

        try {
            // 將字串轉成 UTF8 格式
            s = new String(input.getBytes("ISO-8859-1"), "UTF8");
        } catch (UnsupportedEncodingException e) {
            logger.warn(e.toString());
        }

        return s;
    }

    /**
     * 轉換為日期格式
     * 
     * @param date
     *            日期字串，可接受的格式如下：<br/>
     *            "y/M/d" 或 "y-M-d h:m:s"<br/>
     * 
     * @see DateFormat
     * @return date
     */
    public static Date parseDate(Object value) {
        Date result = null;
        if (isNotEmpty(value)) {
            String temp = addZeroWithValue(trim(value), 7);
            try {
                result = TWNDate.valueOf(temp);
            } catch (Exception e) {
                // logger.error(e.getMessage());
            }
        }
        return result;
    }

    /**
     * 轉換為日期格式
     * 
     * @param date
     *            日期字串，可接受的格式如下：<br/>
     *            "y/M/d" 或 "y-M-d h:m:s"<br/>
     *            分各欄位字元定義，可參考DateFormat，分隔字元可為'/'或'-'或' '或':'
     * 
     * @see DateFormat
     * @return date
     */
    public static Date parseDate(String value) {
        Date result = null;
        if (!"".equals(nullToSpace(value))) {
            result = TWNDate.valueOf(value);
        }
        return result;
    }

    /**
     * 返回西元日期 <br>
     * type='-',格式為"yyyy-MM-dd"
     * 
     * @param date
     *            日期
     * @return
     */
    public static String toAD(Date date) {
        return date == null ? null : new TWNDate(date.getTime()).toAD('-');
    }

    /**
     * 判斷是否為數字 support Numeric format:<br>
     * "33" "+33" "033.30" "-.33" ".33" " 33." " 000.000 "
     * 
     * @param str
     *            String
     * @return boolean
     */
    public static boolean isNumeric(Object obj) {
        return isNumeric(trim(obj));
    }

    /**
     * 檢查字串是不是文數字，如果是就傳回true，如果不是就傳回false
     * 
     * @param number
     *            要檢查的數字字串
     */
    public static boolean isNumeric(String number) {
        if (number == null)
            return false;
        try {
            Double.parseDouble(number);
            return true;
        } catch (NumberFormatException nfe) {
            return false;
        }
    }

    /**
     * 判斷是否為整數 support Integer format:<br>
     * "33" "003300" "+33" " -0000 "
     * 
     * @param str
     *            String
     * @return boolean
     */
    public static boolean isInteger(String str) {
        int begin = 0;
        if (str == null || str.trim().equals("")) {
            return false;
        }
        str = str.trim();
        if (str.startsWith("+") || str.startsWith("-")) {
            if (str.length() == 1) {
                // "+" "-"
                return false;
            }
            begin = 1;
        }
        for (int i = begin; i < str.length(); i++) {
            if (!Character.isDigit(str.charAt(i))) {
                return false;
            }
        }
        return true;
    }

    /**
     * 判斷是否為數字 use Exception support Numeric format:<br>
     * "33" "+33" "033.30" "-.33" ".33" " 33." " 000.000 "
     * 
     * @param str
     *            String
     * @return boolean
     */
    public static boolean isNumericEx(String str) {
        try {
            Double.parseDouble(str);
            return true;
        } catch (NumberFormatException ex) {
            return false;
        }
    }

    /**
     * 判斷是否為整數 use Exception support less than 11 digits(<11)<br>
     * support Integer format:<br>
     * "33" "003300" "+33" " -0000 " "+ 000"
     * 
     * @param str
     *            String
     * @return boolean
     */
    public static boolean isIntegerEx(String str) {
        str = str.trim();
        try {
            Integer.parseInt(str);
            return true;
        } catch (NumberFormatException ex) {
            if (str.startsWith("+")) {
                return isIntegerEx(str.substring(1));
            }
            return false;
        }
    }

    /**
     * 文字轉整數
     * 
     * @param string
     *            要轉換的物件
     * @return int
     */
    public static int parseInt(Object value) {
        return value != null ? parseInt(value.toString()) : 0;
    }

    /**
     * 文字轉整數
     * 
     * @param string
     *            要轉換的字串
     * @return int
     */
    public static int parseInt(String value) {
        int result = 0;
        String temp = NumConverter.delCommaString(value);
        if (isInteger(temp)) {
            result = Integer.parseInt(temp);
            // try{
            // BigDecimal bd = new BigDecimal(value);
            // result = Integer.parseInt(bd.toBigInteger().toString());
            // }catch(NumberFormatException e){
            //
            // }
        }
        return result;
    }

    /**
     * 文字轉double
     * 
     * @param string
     *            要轉換的物件
     * @return int
     */
    public static Double parseDouble(Object value) {
        return value != null ? parseDouble(value.toString()) : 0.0;
    }

    /**
     * 文字轉double
     * 
     * @param string
     *            要轉換的字串
     * @return double
     */
    public static Double parseDouble(String value) {
        Double result = 0.0;
        String temp = NumConverter.delCommaString(value);
        if (isNumeric(temp)) {
            result = Double.parseDouble(temp);
        }
        return result;
    }

    /**
     * 文字轉Long
     * 
     * @param string
     *            要轉換的物件
     * @return Long
     */
    public static long parseLong(Object value) {
        return value != null ? parseLong(value.toString()) : 0;
    }

    /**
     * 文字轉Long
     * 
     * @param string
     *            要轉換的字串
     * @return Long
     */
    public static long parseLong(String value) {
        long result = 0;
        String temp = NumConverter.delCommaString(value);
        if (isNumeric(temp)) {
            try {
                BigDecimal bd = new BigDecimal(temp);
                result = Long.parseLong(bd.toBigInteger().toString());
            } catch (NumberFormatException e) {
                //
            }
        }
        return result;
    }

    /**
     * 字串前補0
     * 
     * @param str
     *            欲補零的字串 <br/>
     * @param num
     *            不足幾位補零 <br/>
     * @return 補0後之字串 <br/>
     * <AUTHOR> EX: addZeroWithValue("1",3) -> "001"
     */
    public static String addZeroWithValue(String str, int num) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < num - str.length(); i++) {
            sb.append("0");
        }
        sb.append(str);
        return sb.toString();
    }

    /**
     * 字串後補空白
     * 
     * @param str
     *            欲補零的字串 <br/>
     * @param num
     *            總位數 <br/>
     * @return 補空白後之字串 <br/>
     * <AUTHOR> EX: addSpaceWithValue("1",3) -> "1 "
     */
    public static String addSpaceWithValue(String str, int num) {
        StringBuffer sb = new StringBuffer(trim(str));
        for (int i = 0, total = num - sb.length(); i < total; i++) {
            sb.append(" ");
        }
        return sb.toString();
    }

    /**
     * 字串前補0
     * 
     * @param value
     *            欲補零的數字 <br/>
     * @param num
     *            不足幾位補零 <br/>
     * @return 補0後之字串 <br/>
     */
    public static String addZeroWithValue(int value, int num) {
        return addZeroWithValue(String.valueOf(value), num);
    }

    /**
     * 截掉頭部、尾部的全形空白 (因 DB char 會自動補空白 所以要 trimSpace)
     * 
     * @param str
     * @return str
     */
    public static String trimSpace(String str) {
        return trimSpace(str, true);
    } // end trimSpace

    /**
     * 截掉頭部、尾部的全形空白 (因 DB char 會自動補空白 所以要 trimSpace)
     * 
     * @param str
     * @return str
     */
    public static String trimSpace(String str, boolean al) {
        if (str == null || str.equals(""))
            return "";

        // trim 半型空白(頭、尾)
        str = str.trim();

        if (!al)
            return str;

        // trim 全型空白(頭)
        int len = str.length();
        int index = 0;
        while (index < len) {
            if (str.charAt(index) == '　')
                index++;
            else
                break;
        } // end while
        str = str.substring(index, len);

        // trim 全型空白(尾)
        len = str.length();
        index = len;
        while (index > 0) {
            if (str.charAt(index - 1) == '　')
                index--;
            else
                break;
        } // end while
        str = str.substring(0, index);

        return str;

    } // end trimSpace

    /**
     * 取得map的key值
     * 
     * @param map
     * @return
     */
    @SuppressWarnings("rawtypes")
    public static String[] getMapKey(Map map) {
        String[] keys = new String[map.size()];
        int count = 0;
        Iterator iter = map.entrySet().iterator();
        while (iter.hasNext()) {
            Map.Entry entry = (Map.Entry) iter.next();
            String key = Util.trim((String) entry.getKey());
            keys[count++] = key;
        }

        return keys;
    }

    /**
     * 
     * @param obj
     * @param encoding
     *            編碼
     */
    public static void trimStringLengthInBytes(Object obj, String encoding) {
        Class<?> cls = obj.getClass();
        try {
            while (cls != Object.class) {
                for (Field field : cls.getDeclaredFields()) {
                    if (field.getType() == String.class) {
                        Column col = field.getAnnotation(Column.class);
                        if (col == null)
                            continue;

                        field.setAccessible(true);
                        Object _value = field.get(obj);
                        if (_value != null) {
                            String value = _value.toString();
                            if (col.columnDefinition().toUpperCase().startsWith("GRAPHIC")) {
                                if (value.length() > col.length()) {
                                    /*
                                     * log.debug("欄位{}長度{}超過限制{}，進行Trim", new Object[]{ field.getName(), value.length(), col.length() }); field.set(obj, value.substring(0, col.length()));
                                     */
                                }
                            } else {
                                byte[] bytes = String.valueOf(value).getBytes("UTF-8");
                                if (bytes.length > col.length()) {
                                    /*
                                     * log.debug("欄位{}長度{}超過限制{}，進行Trim", new Object[]{ field.getName(), bytes.length, col.length() }); field.set(obj, new String(bytes, 0, col.length(), encoding));
                                     */
                                }
                            }
                        }
                    }
                }

                cls = cls.getSuperclass();
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
    }

    /**
     * 設定主要幣別 if 該欄位值為null && 欄位大小=3
     * 
     * @param obj
     *            model物件
     * @param mainCurr
     *            主要幣別
     */
    public static void setMainCurr(Object obj, String mainCurr) {
        Class<?> cls = obj.getClass();
        try {
            while (cls != Object.class) {
                for (Field field : cls.getDeclaredFields()) {
                    if (field.getType() == String.class) {
                        Column col = field.getAnnotation(Column.class);
                        if (col == null)
                            continue;
                        field.setAccessible(true);
                        if (field.getName().toUpperCase().endsWith("CURR")) {
                            if (field.get(obj) == null && col.length() == 3) {
                                field.set(obj, mainCurr);
                            }
                        }
                    }
                }
                cls = cls.getSuperclass();
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
    }

    /**
     * 查詢超出欄位大小的欄位
     * 
     * @param obj要檢查的model
     * @return map<欄位名,欄位大小>
     */
    public static Map<String, Integer> conuntStringLengthInBytes(Object obj) {
        Class<?> cls = obj.getClass();
        Map<String, Integer> tempMap = new HashMap<String, Integer>();
        try {
            while (cls != Object.class) {
                for (Field field : cls.getDeclaredFields()) {
                    if (field.getType() == String.class) {
                        Column col = field.getAnnotation(Column.class);
                        if (col == null)
                            continue;

                        field.setAccessible(true);
                        Object _value = field.get(obj);
                        if (_value != null) {
                            String value = _value.toString();
                            if (col.columnDefinition().toUpperCase().startsWith("GRAPHIC")) {
                                if (value.length() > col.length()) {
                                    tempMap.put(field.getName(), col.length());
                                }
                            } else {
                                byte[] bytes = String.valueOf(value).getBytes("UTF-8");
                                if (bytes.length > col.length()) {
                                    tempMap.put(field.getName(), col.length());
                                }
                            }
                        }
                    }
                }

                cls = cls.getSuperclass();
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        return tempMap;
    }

    /**
     * 驗證model是否超出欄位大小
     * 
     * @param obj
     *            model
     * @param prop
     *            語系檔
     * @return 訊息 (非空白則表示有欄位超出大小，反之則無)
     */
    public static String validateColumnSize(Object obj, Properties prop) {
        String table = obj.getClass().getSimpleName().toUpperCase();
        return validateColumnSize(obj, prop, table);
    }

    /**
     * 驗證model是否超出欄位大小
     * 
     * @param obj
     *            model
     * @param prop
     *            語系檔
     * @param schema
     *            語系檔Table schema
     * @return 訊息 (非空白則表示有欄位超出大小，反之則無)
     */
    public static String validateColumnSize(Object obj, Properties prop, String schema) {
        Map<String, Integer> tempMap = Util.conuntStringLengthInBytes(obj);
        if (!tempMap.isEmpty()) {
            StringBuffer temp = new StringBuffer();
            StringBuffer resultString = new StringBuffer();
            int count = 1;
            for (String colName : tempMap.keySet()) {
                count++;
                resultString.append(resultString.length() > 0 ? "、" : "");
                String colNameProp = prop.getProperty(temp.append(schema).append(".").append(colName).toString());
                temp.setLength(0);
                if (colNameProp == null) {
                    resultString.append(colName);
                } else {
                    resultString.append(colNameProp);
                }
                // 增加顯示欄位可輸入的全型字數
                resultString.append("(");
                resultString.append(tempMap.get(colName) / 3);
                resultString.append(")");
                if (count > 5) {
                    resultString.append("<br/>");
                    count = 1;
                }
            }

            return resultString.append("，").toString();
        }
        return null;
    }

    /**
     * 取得日期(XXXX-XX-XX)
     * 
     * @param date
     *            日期
     * @return 日期
     */
    public static String getDate(Date date) {
        String str = null;
        if (date == null) {
            str = "";
        } else {
            str = TWNDate.toAD(date);
        }
        return str;
    }

    /**
     * 取得日期(XXXX-XX-XX)
     * 
     * @param date
     *            日期
     * @return 日期
     */
    public static String getDate(String date) {
        String str = null;
        if (Util.isEmpty(date)) {
            str = "";
        } else {
            str = TWNDate.valueOf(date).toAD();
        }
        return str;
    }

    /**
     * 初始化map 資料，將所有的rportBean的資料初使化，避免少了而產生exception
     * 
     * @return Map<String, String> 多值MAP
     */
    public static Map<String, String> setColumnMap() {
        return setColumnMap(200);
    }

    /**
     * 初始化map 資料，將所有的rportBean的資料初使化，避免少了而產生exception
     * 
     * @return Map<String, String> 多值MAP
     */
    public static Map<String, String> setColumnMap(int szie) {
        Map<String, String> values = new LinkedHashMap<String, String>();
        for (int i = 1; i <= szie; i++) {
            values.put("ReportBean.column" + String.format("%02d", i), "");
        }
        return values;
    }

    /**
     * 取得複選欄位某選項的資料是否被選取.
     * 
     * ex: 1 + 4 + 32 + 512 = 549 = 1000100101 [二進位]<br/>
     * ==>valueOfMultiOption(549,2) = false <br/>
     * ==>valueOfMultiOption(549,3) = true <br/>
     * ==>valueOfMultiOption(549,6) = true
     * 
     * @param value
     *            由1.2.4.8.16.32.....等二進位數所加總後的數值
     * @param index
     *            由1開始.
     * @return booelan
     */
    public static boolean valueOfCheckBoxOption(int value, int index) {
        int i = 1 << (index - 1);
        return (value & i) > 0;
    }

    /**
     * 取得選取的值
     * 
     * @param value
     *            由1.2.4.8.16.32.....等二進位數所加總後的數值
     * @param total
     *            總共之選項
     * @return
     */
    public static ArrayList<String> valueOfCheckBoxOptionList(int value, int total) {
        ArrayList<String> list = new ArrayList<String>();
        for (int i = 0; i <= total; i++) {
            int j = 1 << i;
            if ((value & j) > 0) {
                list.add(String.valueOf(j));
            }
        }
        return list;
    }

    /**
     * 取得文字檔內容
     * 
     * @param fileName
     *            檔案名稱
     * @return 檔案內容
     * @throws IOException
     */
    public static String getFileContent(String fileName) throws IOException {
        // 儲存完整Word內容
        StringBuilder sbWord = new StringBuilder();
        InputStream inputStream = null;
        InputStreamReader inputStreamReader = null;
        BufferedReader reader = null;
        String line = null;
        // 開始讀取檔案
        try {
            // 讀檔
            inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(fileName);
            inputStreamReader = new InputStreamReader(inputStream);
            reader = new BufferedReader(inputStreamReader);
            while ((line = reader.readLine()) != null) {
                sbWord.append(line);
            }
        } catch (FileNotFoundException e) {
            logger.error(e.getMessage());
        } catch (IOException e) {
            logger.error(e.getMessage());
        } finally {
            if (reader != null) {
                reader.close();
                reader = null;
            }
            if (inputStreamReader != null) {
                inputStreamReader.close();
                inputStreamReader = null;
            }
            if (inputStream != null) {
                inputStream.close();
                inputStream = null;
            }
        }
        return sbWord.toString();
    }

    /**
     * 取代word特定欄位資料
     * 
     * @param content
     *            WORD原始內容
     * @param map
     *            (key,replaceContent)
     * @return content
     */
    public static String replaceWordContent(String content, Map<String, String> map) {
        for (String key : map.keySet()) {
            content = content.replace(key, Util.nullToSpace(map.get(key)));
        }
        return content;
    }

    /**
     * 取代word特定欄位資料(可自訂前綴字、後綴字)
     * 
     * @param content
     *            WORD原始內容
     * @param map
     *            (key,replaceContent)
     * @param arg1前綴字
     * @param arg2後綴字
     * @return
     */
    public static String replaceWordContent(String content, Map<String, String> map, String arg1, String arg2) {
        for (String key : map.keySet()) {
            content = content.replace(arg1 + key + arg2, Util.nullToSpace(map.get(key)));
        }
        return content;
    }

    /**
     * 取代word特定欄位資料(可自訂前綴字、後綴字與特殊條件)
     * 
     * @param contentWORD原始內容
     * @param map
     *            (key,replaceContent)
     * @param arg1前綴字
     * @param arg2後綴字
     * @param spectial特殊條件
     *            (符合此條件不需加上前綴字及後綴字)
     * @return
     */
    public static String replaceWordContent(String content, Map<String, String> map, String arg1, String arg2, String spectial) {
        for (String key : map.keySet()) {
            if (Util.trim(key).contains(spectial)) {
                content = content.replace(key, Util.nullToSpace(map.get(key)));
            } else {
                content = content.replace(arg1 + key + arg2, Util.nullToSpace(map.get(key)));
            }
        }
        return content;
    }

    /**
     * 將數值轉為BinaryList EX: 7 => [4,2,1]
     * 
     * @param int
     *            value
     * @return List<String>
     */
    @SuppressWarnings({ "unchecked", "rawtypes" })
    public static List<String> getBinaryList(int value) {
        String str = Integer.toBinaryString(value);
        char[] charArray = str.toCharArray();
        int len = charArray.length;
        List<String> list = new ArrayList();
        for (char c : charArray) {
            list.add(String.valueOf(parseInt(c) * (int) Math.pow(2, --len)));
        }
        return list;
    }

    /**
     * 取得排序條件
     * 
     * @param pageSetting
     * @return
     */
    public static String getOrderData(Map<String, Boolean> map) {
        StringBuffer str = new StringBuffer();
        boolean result = false;
        if (map != null) {
            for (String key : map.keySet()) {
                if (key.indexOf(",") != -1) {
                    result = true;
                }
                if (!result) {
                    if (str.length() == 0) {
                        str.append(" order by ");
                    } else {
                        str.append(" , ");
                    }
                    str.append(key);
                    if (map.get(key)) {
                        str.append(" DESC ");
                    }
                } else {
                    str.append(" order by ");
                    if (key.substring(key.length() - 2, key.length()).equals(", ")) {
                        str.append(key.substring(0, key.length() - 2));
                    } else {
                        str.append(key);
                    }
                }

            }
        }
        return str.toString();
    }

    /**
     * 為了WORD讀取CKEDIT可以取得圖片檔
     * 
     * @param data
     * @return
     */
    public static String getDocRealImgPath(String data) {
        return Util.trim(data.replace("<img src=\"file?id=", "<img src=\"" + PropUtil.getProperty("baseUrl") + "file?id="));
    }

    /**
     * 擷取右邊字串
     * 
     * @param str
     *            字串
     * @param len
     *            截取長度
     * @return ex: getRightStr("123456",4) ="3456"
     */
    public static String getRightStr(String str, int len) {
        if (str == null || len == 0) {
            return "";
        }
        return str.substring(str.length() - (str.length() < len ? str.length() : len), str.length());
    }

    /**
     * 擷取左邊字串
     * 
     * @param str
     *            字串
     * @param len
     *            截取長度
     * @return ex: getLeftStr("123456",4) ="1234"
     */
    public static String getLeftStr(String str, int len) {
        if (str == null || len == 0) {
            return "";
        }
        return str.substring(0, len > str.length() ? str.length() : len);
    }

    /**
     * 文字轉BigDecimal
     * 
     * @param s
     * @return
     */
    public static BigDecimal parseBigDecimal(Object s) {
        return parseToBigDecimal(s);
    }

    /**
     * 文字轉BigDecimal
     * 
     * @param s
     * @return
     */
    public static BigDecimal parseToBigDecimal(Object s) {
        String _s = trim(s);
        BigDecimal bd = BigDecimal.ZERO;
        if (Util.isNotEmpty(_s)) {
            try {
                bd = new BigDecimal(_s);
            } catch (Exception e) {
                // nothing to do............
            }
        }
        return bd;
    }

    /**
     * 檢查二者是否相同
     * 
     * @param Object
     * @param Object
     */
    public static boolean notEquals(Object a, Object b) {
        return !equals(a, b, true);
    }

    /**
     * 檢查二者是否相同
     * 
     * @param Object
     * @param Object
     * @param boolean
     *            不分大小寫
     */
    public static boolean notEquals(Object a, Object b, boolean c) {
        return !equals(a, b, c);
    }

    /**
     * 檢查二者是否相同
     * 
     * @param Object
     * @param Object
     */
    public static boolean equals(Object a, Object b) {
        return equals(a, b, true);
    }

    /**
     * 檢查二者是否相同
     * 
     * @param Object
     * @param Object
     * @param boolean
     *            不分大小寫
     */
    public static boolean equals(Object a, Object b, boolean c) {
        if (c) {
            return trim(a).toUpperCase().equals(trim(b).toUpperCase());
        } else {
            return trim(a).equals(trim(b));
        }
    }

    /**
     * 處理溢位(使溢位值轉為正確之顯示文字)
     * 
     * @param Object
     * @param String
     */
    private final static BigDecimal bigZero = new BigDecimal(0);

    /**
     * 小數進位處理
     * 
     * @param d
     * @param scale
     * @return
     */
    public static String Overflow(double d, int scale) {
        BigDecimal bd = new BigDecimal(d);
        return Overflow(bd, scale);
    }

    /**
     * 小數進位處理
     * 
     * @param obj
     * @param scale
     * @return
     */
    public static String Overflow(Object obj, int scale) {
        BigDecimal bd = new BigDecimal(parseDouble(obj));
        return Overflow(bd, scale);
    }

    /**
     * 小數進位處理
     * 
     * @param bigDecimal
     * @param scale
     * @return
     */
    public static String Overflow(BigDecimal bigDecimal, int scale) {
        if (bigDecimal.compareTo(bigZero) == 0) {
            return bigZero.toString();
        } else {
            return bigDecimal.setScale(scale, BigDecimal.ROUND_HALF_UP).toString();
        }
    }

    /**
     * 長度不足補空白(含中文處理)
     * 
     * @param str
     * @param num
     * @return
     */
    public static String addSpaceWithValueFixCht(String str, int num) {
        StringBuffer sb = new StringBuffer(trim(str));
        int total = num - strLength(sb.toString());
        for (int i = 0; i < total; i++) {
            sb.append(" ");
        }
        return sb.toString();
    }

    /**
     * 回傳字串長度
     * 
     * @param src
     *            欲處理字串
     * @return
     */
    public static int strLength(String src) {
        if (null == src || src.length() == 0)
            return 0;

        int len = 0;
        for (int i = 0; i < src.length(); i++) {
            len += (Integer.toHexString((int) src.charAt(i)).length() == 4) ? 2 : 1;
        }

        return len;
    }

    // 中文
    public static final String CODEPAGE_CP937 = "CP937";
    // CP037 --> 要置換為 CP937
    public static final String CODEPAGE_CP037_NODBCS = "CP037";

    /**
     * <pre>
     * 截字串 in OS390主機 (預設CODEPAGE=CP937)
     * 依主機加0x0E, 0x0F的規則，將字串截到指定長度
     * </pre>
     * 
     * @param str
     * @param size
     * @return
     * @throws Exception
     */
    public static String trimSizeInOS390(String str, int size) {
        return trimSizeInOS390(CODEPAGE_CP937, str, size);
    }

    /**
     * <pre>
     * 截字串 in OS390主機 (如果codepage為空白則預設為CP937)
     * 依主機加0x0E, 0x0F的規則，將字串截到指定長度
     * 
     * PS. 從ELSBRN資料表內取得CODEPAGE定義，若為CP037則置換成CP937
     * </pre>
     * 
     * @param codePage
     * @param str
     * @param size
     * @return
     * @throws Exception
     */
    public static String trimSizeInOS390(String codePage, String str, int size) {
        StringBuilder buf = new StringBuilder(size);

        if (StringUtils.isBlank(codePage)) {
            codePage = CODEPAGE_CP937;
        }

        // 參照 OBSDBJdbcContext.getCodepage
        // 從ELSBRN資料表內取得CODEPAGE定義，若為CP037則置換成CP937
        if (CODEPAGE_CP037_NODBCS.equalsIgnoreCase(codePage)) {
            codePage = CODEPAGE_CP937;
        }

        int mode = 1;
        int len = 0;
        if (Util.isNotEmpty(str)) {
            try {
                for (char ch : str.toCharArray()) {

                    byte[] b = String.valueOf(ch).getBytes(codePage);
                    int charLen = b.length;

                    int count = (charLen + 1) / 2;

                    if (CODEPAGE_CP937.equals(codePage)) {
                        // 因為CP937不需要經過getBytes的動作所以
                        // 如果該字元非ascii就直接視為圖型文字
                        if (ch < 0 || ch > 127)
                            count = 2;
                    }

                    if (count != mode) {
                        ++len;
                    }
                    mode = count;

                    if ((len + count) > size || (mode == 2 && (len + count) == size))
                        break;

                    len += count;
                    buf.append(ch);
                }
            } catch (Exception e) {
                throw new RuntimeException(e.getMessage());
            }
        }
        return buf.toString();
    }

    /**
     * 截字串 for e-Loan DB
     * 
     * @param s
     * @param maxBytes
     * @return
     */
    public static String truncateToFitUtf8ByteLength(String s, int maxBytes) {

        return truncateToFitByteLength(s, maxBytes, "UTF-8");
    }

    public static String truncateToFitByteLength(String s, int maxBytes, String charset) {
        if (s == null) {
            return null;
        }
        Charset cs = Charset.forName(charset);
        CharsetDecoder decoder = cs.newDecoder();
        byte[] sba = s.getBytes(cs);
        if (sba.length <= maxBytes) {
            return s;
        }
        // Ensure truncation by having byte buffer = maxBytes
        ByteBuffer bb = ByteBuffer.wrap(sba, 0, maxBytes);
        CharBuffer cb = CharBuffer.allocate(maxBytes);
        // Ignore an incomplete character
        decoder.onMalformedInput(CodingErrorAction.IGNORE);
        decoder.decode(bb, cb, true);
        decoder.flush(cb);
        String res = new String(cb.array(), 0, cb.position());

        // 為避免 cp937 這類, 在 new String 時自動補上 0x0f, 這裡做個檢查, 必要時切掉最後一個字
        if (res.length() > 0 && res.getBytes(cs).length > maxBytes)
            return res.substring(0, res.length() - 1);
        return res;
    }

    /**
     * 依傳入的參數長度流生對應的sql 參數個數
     *
     * @param args
     * @return 長度為1 產生? 長度為2 產生 ?,?
     */
    public static String genSqlParam(Object[] args) {
        if (args != null && args.length > 0) {
            String[] params = new String[args.length];
            Arrays.fill(params, "?");
            return StringUtils.join(params, ",");
        }
        return "";
    }

    /**
     * 依傳入的參數長度流生對應的sql 參數個數
     *
     * @param args
     * @return 長度為1 產生? 長度為2 產生 ?,?
     */
    public static String genSqlParam(List<?> args) {
        if (args != null && args.size() > 0) {
            String[] params = new String[args.size()];
            Arrays.fill(params, "?");
            return StringUtils.join(params, ",");
        }
        return "";
    }

    public static void main(String[] args) throws UnsupportedEncodingException {

        String s = "一二三四五";

        System.out.println("測試中文字串-[" + s + "]");
        testTruncateString(s, 8, "UTF-8");
        testTruncateString(s, 8, "CP937");
        testTruncateString(s, 8, "cp835");
    }

    private static void testTruncateString(String s, int maxLen, String charset) throws UnsupportedEncodingException {

        byte[] b1 = s.getBytes(charset);
        System.out.println(charset + " 資料長度 " + b1.length + " bytes");
        String s2 = truncateToFitByteLength(s, maxLen, charset);
        System.out.println(charset + " 以限制長度 " + maxLen + " 截斷字串, 結果為 " + s2 + ", 資料長度為 " + s2.getBytes(charset).length);
    }

    /**
     * 傳入陣例，回傳指定分割size的 陣列
     *
     * @param array
     *            陣列資料
     * @param splitSize
     *            分割大小
     * @param <T>
     *
     * @return input [5,4,3,2,1] size為2時，回傳List,內容為 [[5,4],[3,2],[1]]
     */
    public static <T extends Object> List<T[]> splitArray(T[] array, int splitSize) {

        int numberOfArrays = array.length / splitSize;
        int remainder = array.length % splitSize;

        int start = 0;
        int end = 0;

        List<T[]> list = new ArrayList<T[]>();
        for (int i = 0; i < numberOfArrays; i++) {
            end += splitSize;
            list.add(Arrays.copyOfRange(array, start, end));
            start = end;
        }

        if (remainder > 0) {
            list.add(Arrays.copyOfRange(array, start, (start + remainder)));
        }
        return list;
    }
}
