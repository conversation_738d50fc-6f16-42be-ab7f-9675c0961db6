/* 
 * LMS1405S02Panel05.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.OverSeaUtil;
import com.mega.eloan.lms.model.L120M01A;




/**<pre>
 * 額度明細表- 擔保品
 * </pre>
 * @since  2011/10/5
 * <AUTHOR>
 * @version <ul>
 *           <li>2011/10/5,REX,new
 *          </ul>
 */
public class LMS1405S02Panel05 extends Panel {

	private L120M01A l120m01a;

	public LMS1405S02Panel05(String id, L120M01A l120m01a) {
		super(id);
		this.l120m01a = l120m01a;

		// boolean showQualitativeFactor = false;
		// if(l120m01a!=null){
		// if(LMSUtil.isOverSea_CLS(l120m01a)){
		// if(OverSeaUtil.isCaseDoc_CLS_rawBorrowerPanel(l120m01a)){
		//
		// }else{
		// showQualitativeFactor = true;
		// }
		// }
		// }

		// add(LMSUtil.genHtmlComponent("_panelQualitativeFactor1",
		// showQualitativeFactor));
		// add(LMSUtil.genHtmlComponent("_panelQualitativeFactor2",
		// showQualitativeFactor));
		// if(true){
		// String panel_id = "_panelQualitativeFactor";
		// if(OverSeaUtil.isCaseDoc_CLS_JP_ON(l120m01a)){
		// add(new LMS1015S03PanelA(panel_id));
		// }else if(OverSeaUtil.isCaseDoc_CLS_AU_ON(l120m01a)){
		// add(new LMS1025S03PanelA(panel_id));
		// }else if(OverSeaUtil.isCaseDoc_CLS_TH_ON(l120m01a)){
		// add(new LMS1035S04PanelA(panel_id, true));
		// }else{
		// add(new Label(panel_id, "不明狀態"));
		// }
		// }
	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);

		boolean showQualitativeFactor = false;
		if (l120m01a != null) {
			if (LMSUtil.isOverSea_CLS(l120m01a)) {
				if (OverSeaUtil.isCaseDoc_CLS_rawBorrowerPanel(l120m01a)) {

				} else {
					showQualitativeFactor = true;
				}
			}
		}

		model.addAttribute("_panelQualitativeFactor1_visible", showQualitativeFactor);
		model.addAttribute("_panelQualitativeFactor2_visible", showQualitativeFactor);

		String panel_id = "_panelQualitativeFactor";
		if (OverSeaUtil.isCaseDoc_CLS_JP_ON(l120m01a)) {
			new LMS1015S03PanelA(panel_id).processPanelData(model, params);
		} else if (OverSeaUtil.isCaseDoc_CLS_AU_ON(l120m01a)) {
			new LMS1025S03PanelA(panel_id).processPanelData(model, params);
		} else if (OverSeaUtil.isCaseDoc_CLS_TH_ON(l120m01a)) {
			new LMS1035S04PanelA(panel_id, true).processPanelData(model, params);
		} else {
			// UPGRADE: 待確認畫面是否抓得到此屬性
			model.addAttribute(panel_id, "不明狀態");
		}
	}

	/**/
	private static final long serialVersionUID = 1L;

}
