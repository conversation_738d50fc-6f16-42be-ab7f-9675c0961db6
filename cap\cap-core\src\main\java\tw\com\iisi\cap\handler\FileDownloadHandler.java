/**
 * Copyright (c) 2009-2011 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
 */
package tw.com.iisi.cap.handler;

import com.iisigroup.cap.component.PageParameters;

import tw.com.iisi.cap.action.IAction;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.plugin.IFrameAjaxHandlerPlugin;
import tw.com.iisi.cap.response.IResult;

/**
 * <pre>
 * 定義下載相關行為
 * </pre>
 * 
 * @since 2010/11/24
 * <AUTHOR>
 * @version $Id$
 * @version
 *          <ul>
 *          <li>2010/11/24,iristu,new
 *          </ul>
 */
public abstract class FileDownloadHandler extends FormHandler implements IFrameAjaxHandlerPlugin {

    /*
     * 取得下載前會執行的動作
     * 
     * @see tw.com.iisi.cap.handler.FormHandler#getAction(java.lang.String)
     */
    @Override
    public IAction getAction(String formAction) {
        return new BeforeDownload();
    }

    /** do beforeDownload action */
    class BeforeDownload implements IAction {

        /*
         * 從Request裡取得下載前會執行的動作
         * 
         * @see tw.com.iisi.cap.action.IAction#doWork(com.iisigroup.cap.component.PageParameters)
         */
        @Override
        public IResult doWork(PageParameters params) throws CapException {
            return beforeDownload(params);
        }
    }// ;

    /**
     * 下載前的行為
     * 
     * @param params
     * @return
     * @throws CapException
     */
    public abstract IResult beforeDownload(PageParameters params) throws CapException;

}
