/* 
 * MisEldpfService.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.mfaloan.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <pre>
 * MIS存款檔
 * </pre>
 * 
 * @since 2011/9/23
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/9/23,rodeschen,new
 *          </ul>
 */
public interface MisEldpfService {
	/**
	 * 取得本行帳務系統近半年之存款資訊
	 * 
	 * @param custId 客戶統編
	 * @return 存款金額
	 */
	BigDecimal findAVGByCustId(String custId);
	
	/**
	 * 引入定期存款去年與今年各月餘額及今年平均餘額
	 * @param custId 客戶統編
	 * @return 今年各月餘額及今年平均餘額
	 */
	Map<String,Object> findCurrentYearAvgByCustId(String custId);
	
	/**
	 * 引入活期存款去年與今年各月餘額及今年平均餘額
	 * @param custId 客戶統編
	 * @return 今年各月餘額及今年平均餘額
	 */
	Map<String,Object> findCurrentYearAvgByCustId2(String custId);
	
	/**
	 * 查詢負責人/連保人之存款資料
	 * @param custId 客戶統編
	 * @return 所有存款別及台幣前日餘額 存款資料
	 */
	List<Map<String,Object>> findByCustId(String custId);

}
