/* 
 * L730M01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import org.apache.commons.lang3.builder.ToStringExclude;

import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 授信報案考核表主檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name = "L730M01A", uniqueConstraints = @UniqueConstraint(columnNames = { "mainId" }))
public class L730M01A extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * JOIN條件 L730A01A．關聯檔
	 * 
	 */
	@ToStringExclude
	@OneToMany(mappedBy = "l730m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private Set<L730A01A> l730a01a;

	public Set<L730A01A> getL730a01a() {
		return l730a01a;
	}

	public void setL730a01a(Set<L730A01A> l730a01a) {
		this.l730a01a = l730a01a;
	}

	/**
	 * 刪除註記
	 * <p/>
	 * 2011/11/08 新增：文件刪除時使用(非立即性刪除)
	 */
	@Column(name = "DELETEDTIME", columnDefinition = "TIMESTAMP")
	private Timestamp deletedTime;

	/**
	 * 受評單位
	 * <p/>
	 * L120M01A.caseBrId
	 */
	@Column(name = "BRANCHID", length = 3, columnDefinition = "CHAR(03)")
	private String branchId;

	/**
	 * 資料年月
	 * <p/>
	 * L120M01A.caseDate??<br/>
	 * L120M01A.endDate??<br/>
	 * ※上傳至DW改為YYYMM(民國年)
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "CHKYM", columnDefinition = "DATE")
	private Date chkYM;

	/**
	 * 企/個金案件
	 * <p/>
	 * 企金案件：LMS<br/>
	 * 個金案件：CLS
	 */
	@Column(name = "SYSTYPE", length = 3, columnDefinition = "CHAR(3)")
	private String sysType;

	/** 考評人員 **/
	@Column(name = "APPRID", length = 6, columnDefinition = "CHAR(6)")
	private String apprId;

	/** 覆核人員 **/
	@Column(name = "CHKID", length = 6, columnDefinition = "CHAR(6)")
	private String chkId;

	/**
	 * 考評單位
	 * <p/>
	 * 營運中心(營運中心制分行)或授管處(國外部、國金部、金控總部分行及海外分行)
	 */
	@Column(name = "CHKUNIT", length = 3, columnDefinition = "CHAR(3)")
	private String chkUnit;

	/**
	 * 簽報書案件號碼
	 * <p/>
	 * L120M01A.caseNo<br/>
	 * ※上傳至DW不可超過CHAR(50)
	 */
	@Column(name = "PROJNO", length = 62, columnDefinition = "VARCHAR(62)")
	private String projNo;

	/**
	 * 額度(幣別)
	 * <p/>
	 * ??
	 */
	@Column(name = "CURR", length = 3, columnDefinition = "CHAR(3)")
	private String curr;

	/**
	 * 額度(金額)
	 * <p/>
	 * ??
	 */
	@Column(name = "LOANAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal loanAmt;

	/**
	 * ??
	 * <p/>
	 * ??
	 */
	@Column(name = "CASEAMT", columnDefinition = "DECIMAL(7,0)")
	private BigDecimal caseAmt;

	/** 扣分總數 **/
	@Column(name = "TMGRADE", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal tmGrade;

	/**
	 * 取得刪除註記
	 * <p/>
	 * 2011/11/08 新增：文件刪除時使用(非立即性刪除)
	 */
	public Timestamp getDeletedTime() {
		return this.deletedTime;
	}

	/**
	 * 設定刪除註記
	 * <p/>
	 * 2011/11/08 新增：文件刪除時使用(非立即性刪除)
	 **/
	public void setDeletedTime(Timestamp value) {
		this.deletedTime = value;
	}

	/**
	 * 取得受評單位
	 * <p/>
	 * L120M01A.caseBrId
	 */
	public String getBranchId() {
		return this.branchId;
	}

	/**
	 * 設定受評單位
	 * <p/>
	 * L120M01A.caseBrId
	 **/
	public void setBranchId(String value) {
		this.branchId = value;
	}

	/**
	 * 取得資料年月
	 * <p/>
	 * L120M01A.caseDate??<br/>
	 * L120M01A.endDate??<br/>
	 * ※上傳至DW改為YYYMM(民國年)
	 */
	public Date getChkYM() {
		return this.chkYM;
	}

	/**
	 * 設定資料年月
	 * <p/>
	 * L120M01A.caseDate??<br/>
	 * L120M01A.endDate??<br/>
	 * ※上傳至DW改為YYYMM(民國年)
	 **/
	public void setChkYM(Date value) {
		this.chkYM = value;
	}

	/**
	 * 取得企/個金案件
	 * <p/>
	 * 企金案件：LMS<br/>
	 * 個金案件：CLS
	 */
	public String getSysType() {
		return this.sysType;
	}

	/**
	 * 設定企/個金案件
	 * <p/>
	 * 企金案件：LMS<br/>
	 * 個金案件：CLS
	 **/
	public void setSysType(String value) {
		this.sysType = value;
	}

	/** 取得考評人員 **/
	public String getApprId() {
		return this.apprId;
	}

	/** 設定考評人員 **/
	public void setApprId(String value) {
		this.apprId = value;
	}

	/** 取得覆核人員 **/
	public String getChkId() {
		return this.chkId;
	}

	/** 設定覆核人員 **/
	public void setChkId(String value) {
		this.chkId = value;
	}

	/**
	 * 取得考評單位
	 * <p/>
	 * 營運中心(營運中心制分行)或授管處(國外部、國金部、金控總部分行及海外分行)
	 */
	public String getChkUnit() {
		return this.chkUnit;
	}

	/**
	 * 設定考評單位
	 * <p/>
	 * 營運中心(營運中心制分行)或授管處(國外部、國金部、金控總部分行及海外分行)
	 **/
	public void setChkUnit(String value) {
		this.chkUnit = value;
	}

	/**
	 * 取得簽報書案件號碼
	 * <p/>
	 * L120M01A.caseNo<br/>
	 * ※上傳至DW不可超過CHAR(50)
	 */
	public String getProjNo() {
		return this.projNo;
	}

	/**
	 * 設定簽報書案件號碼
	 * <p/>
	 * L120M01A.caseNo<br/>
	 * ※上傳至DW不可超過CHAR(50)
	 **/
	public void setProjNo(String value) {
		this.projNo = value;
	}

	/**
	 * 取得額度(幣別)
	 * <p/>
	 * ??
	 */
	public String getCurr() {
		return this.curr;
	}

	/**
	 * 設定額度(幣別)
	 * <p/>
	 * ??
	 **/
	public void setCurr(String value) {
		this.curr = value;
	}

	/**
	 * 取得額度(金額)
	 * <p/>
	 * ??
	 */
	public BigDecimal getLoanAmt() {
		return this.loanAmt;
	}

	/**
	 * 設定額度(金額)
	 * <p/>
	 * ??
	 **/
	public void setLoanAmt(BigDecimal value) {
		this.loanAmt = value;
	}

	/**
	 * 取得??
	 * <p/>
	 * ??
	 */
	public BigDecimal getCaseAmt() {
		return this.caseAmt;
	}

	/**
	 * 設定??
	 * <p/>
	 * ??
	 **/
	public void setCaseAmt(BigDecimal value) {
		this.caseAmt = value;
	}

	/** 取得扣分總數 **/
	public BigDecimal getTmGrade() {
		return this.tmGrade;
	}

	/** 設定扣分總數 **/
	public void setTmGrade(BigDecimal value) {
		this.tmGrade = value;
	}
}
