/* 
 *MisLNF030ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.mfaloan.service.MISLN30Service;

/**
 * <pre>
 * MISLN30Service
 * </pre>
 * 
 * @since 2012/11/7
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/11/7,REX,new
 *          </ul>
 */
@Service
public class MISLN30ServiceImpl extends AbstractMFAloanJdbc implements
		MISLN30Service {

	@Override
	public List<Map<String, Object>> findByLNF030_CONTRACT(String cntrNo) {
		return this.getJdbc().queryForList("MIS.MISLN30_findByLNF030_CONTRACT",
				new Object[] { "cntrNo" });
	}

	@Override
	public List<Map<String, Object>> findByLNF030_PersonalData(String brNo){
		return this.getJdbc().queryForListWithMax("MIS.MISLN30_findByLNF030_PersonalData", 
			new String[] { (brNo+"%") });
	}
	
	@Override
	public List<Map<String, Object>> findByLNF030_CoporateData(String brNo){
		return this.getJdbc().queryForListWithMax("MIS.MISLN30_findByLNF030_CoporateData", 
			new String[] { (brNo+"%") });
	}
	
}
