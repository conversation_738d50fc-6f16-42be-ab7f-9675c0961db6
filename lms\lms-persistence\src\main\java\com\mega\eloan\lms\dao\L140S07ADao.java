/* 
 * L140S07ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140S07A;

/** 敘作條件異動資訊 **/
public interface L140S07ADao extends IGenericDao<L140S07A> {

	L140S07A findByOid(String oid);
	
	List<L140S07A> findByMainId(String mainId);

	L140S07A findMaxSeqNumByMainId(String mainId);
}