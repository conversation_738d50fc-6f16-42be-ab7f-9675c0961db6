<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:aop="http://www.springframework.org/schema/aop" xmlns:tx="http://www.springframework.org/schema/tx"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
           http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.0.xsd
           http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd">

	<bean id="lms4comEntityManagerFactory"
		class="org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean">
		<property name="persistenceUnitName" value="pu-lms4com" />
		<property name="dataSource" ref="lms4com-db" />
		<property name="persistenceXmlLocation" value="classpath:META-INF/persistence-lms4com.xml" />
		<property name="jpaVendorAdapter">
			<bean class="org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter">
				<property name="generateDdl" value="${lms.jpa.ddl}" />
			</bean>
		</property>
		<property name="jpaDialect">
			<bean class="org.springframework.orm.jpa.vendor.HibernateJpaDialect" />
		</property>
        <property name="jpaProperties">
            <bean class="org.springframework.beans.factory.config.PropertiesFactoryBean">
                <property name="location">
                    <value>classpath:/db/#{systemProperties['eloan.env']}hibernate.properties</value>
                </property>
            </bean>
        </property>
		<property name="jpaPropertyMap">
			<map>
                <entry key="hibernate.dialect" value="${lms4com.jpa.hibernate.dialect}" />
                <entry key="hibernate.default_schema" value="${lms4com.jpa.schema}" />
			</map>
		</property>
	</bean>

	<bean id="lms4comEntityManager" class="org.springframework.orm.jpa.support.SharedEntityManagerBean">
		<property name="entityManagerFactory" ref="lms4comEntityManagerFactory" />
	</bean>

	<bean id="lms4comTxManger" class="org.springframework.orm.jpa.JpaTransactionManager">
		<property name="entityManagerFactory" ref="lms4comEntityManagerFactory" />
	</bean>

	<tx:advice id="lms4comTxAdvice" transaction-manager="lms4comTxManger">
		<tx:attributes>
			<!-- all methods below are read-only -->
			<tx:method name="list*"  read-only="true" />
			<tx:method name="find*"  read-only="true" />
			<tx:method name="get*"  read-only="true" />

			<!-- other methods use the default transaction settings (see below) -->
			<tx:method name="*" timeout="45000" rollback-for="Throwable"
				propagation="REQUIRED" />
			<!-- timeout in seconds -->
		</tx:attributes>
	</tx:advice>

	<aop:config proxy-target-class="true">
		<aop:pointcut id="lms4comServiceOperation"
			expression="execution(* com.mega.eloan.common.log.*Service.*(..))" />

		<aop:advisor advice-ref="lms4comTxAdvice" pointcut-ref="lms4comServiceOperation" />
	</aop:config>
</beans>