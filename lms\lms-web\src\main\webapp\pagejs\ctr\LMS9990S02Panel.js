$(function(){
    /**  檔案上傳grid  */
    
    var grid = $("#gridfile").iGrid({
        handler: "lms9990gridhandler",
        height: 120,
        autowidth: true,
        postData: {
            formAction: "queryfile",
            mainId: $("#tabFormMainId").val()
        },
        rowNum: 15,
        multiselect: true,
        colModel: [{
            colHeader: i18n.def['uploadFile.srcFileName'],//檔案名稱,
            name: 'srcFileName',
            width: 120,
            align: "left",
            sortable: true,
            formatter: 'click',
            onclick: download
        }, {
            colHeader: i18n.def['uploadFile.uploadTime'],//上傳時間
            name: 'uploadTime',
            width: 140,
            align: "center",
            sortable: true
        },        /*{
         colHeader :"&nbsp;",
         formatter : 'fileDownload',
         onclick : download
         }, */
        {
            name: 'oid',
            hidden: true
        }]
    });
    
    /**  上傳檔案按鈕*/
    $("#uploadFile").click(function(){
        var limitFileSize = 3145728;
        MegaApi.uploadDialog({
            fieldId: "L999M01A",
            fieldIdHtml: "size='30'",
            fileDescId: "fileDesc",
            fileDescHtml: "size='30' maxlength='30'",
            subTitle: i18n.def('insertfileSize', {
                'fileSize': (limitFileSize / 1048576).toFixed(2)
            }),
            limitSize: limitFileSize,
            width: 320,
            height: 190,
            data: {
                mainId: $("#srcMainId").val(),
                sysId: "LMS"
            },
            success: function(){
                $("#gridfile").trigger("reloadGrid");
            }
        });
    });
    
    /**  刪除檔案按鈕 */
    $("#deleteFile").click(function(){
        var select = $("#gridfile").getGridParam('selarrrow');
        if (select == "") {
        
            // TMMDeleteError=請先選擇需修改(刪除)之資料列
            CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
            return;
        }
        
        // confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                var datas = [];
                for (var i in select) {
                    datas.push($("#gridfile").getRowData(select[i]).oid);
                }
                $.ajax({
                    handler: "lms9990m01formhandler",
                    data: {
                        formAction: "deleteUploadFile",
                        oids: datas
                    },
                }).done(function(obj){
					$("#gridfile").trigger("reloadGrid");					
				});
            } else {
                return;
            }
        });
    });
    
    
    /**  檔案下載  */
    function download(cellvalue, options, data){
        $.capFileDownload({
            handler: "simplefiledwnhandler",
            data: {
                fileOid: data.oid
            }
        });
    }
    
    
    
});
