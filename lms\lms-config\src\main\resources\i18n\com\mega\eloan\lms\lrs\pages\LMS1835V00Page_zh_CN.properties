#==================================================
# \u7522\u751f\u4f01\u91d1\u6236\u65b0\u589e/\u589e\u984d\u540d\u55ae-\u5916Grid
#==================================================
L1835M01a.dataDate=\u6570\u636e\u65e5\u671f
L1835M01a.branch=\u5206\u884c\u540d\u79f0
L1835M01a.reportDate=\u62a5\u8868\u4ea7\u751f\u65e5\u671f

#==================================================
# \u7522\u751f\u4f01\u91d1\u6236\u65b0\u589e/\u589e\u984d\u540d\u55ae--thickbox
#==================================================
L1835M01a.title1=\u4ea7\u751f\u4f01\u91d1\u6237\u65b0\u589e/\u589e\u989d\u540d\u5355
L1835M01a.title2=\u4ea7\u751f\u4f01\u91d1\u6237\u65b0\u589e/\u589e\u989d\u540d\u5355\u67e5\u8be2
L1835M01a.date=\u8bf7\u8f93\u5165\u9884\u8ba1\u8986\u5ba1\u4e4b\u5e74\u6708
L1835M01a.brNo=\u8bf7\u9009\u62e9\u6b32\u4ea7\u751f\u540d\u5355\u4e4b\u5206\u884c\u4ee3\u53f7
L1835M01a.dataDateForFilter=\u6570\u636e\u65e5\u671f
L1835M01a.dataDateForFilter2=\u6570\u636e\u65e5\u671f\u533a\u95f4
L1835M01a.searchNew=\u6700\u65b0\u8d44\u6599
L1835M01a.searchOld=\u5386\u53f2\u8d44\u6599
L1835v00.startDate=\u8d77\u59cb\u65e5\u671f
L1835v00.endDate=\u8fc4\u81f3\u65e5\u671f
#==================================================
# \u7522\u751f\u4f01\u91d1\u6236\u65b0\u589e/\u589e\u984d\u540d\u55ae--ERROR
#==================================================
L1835v00.error=\u8d77\u59cb\u65e5\u671f\u4e0d\u53ef\u5927\u4e8e\u8fc4\u81f3\u65e5\u671f
