package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 消金覆審控制維護主檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "C243M01A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class C243M01A extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 分行代碼 **/
	@Column(name="ELFBRANCH", length=3, columnDefinition="CHAR(3)")
	private String elfBranch;

	/** 應覆審日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELFCRDATE", columnDefinition="DATE")
	private Date elfCrDate;

	/** 調整後應覆審日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="CHGCRDATE", columnDefinition="DATE")
	private Date chgCrDate;

	/** 調整原因 **/
	@Column(name="CHGREASON", length=900, columnDefinition="VARCHAR(900)")
	private String chgReason;

	/** 調整後不覆審代碼 **/
	@Column(name="CHGNCKDFLAG", length=2, columnDefinition="CHAR(2)")
	private String chgNckdFlag;
	
	/** 上次覆審日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELFLRDATE", columnDefinition="DATE")
	private Date elfLrDate;

	/** 上次土建融實地覆審日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELFLASTREALDT", columnDefinition="DATE")
	private Date elfLastRealDt;

	/** 覆審規則 **/
	@Column(name="ELFREMOMO", length=30, columnDefinition="CHAR(30)")
	private String elfRemomo;

	/** 不覆審代碼 **/
	@Column(name="ELFNCKDFLAG", length=2, columnDefinition="CHAR(2)")
	private String elfNckdFlag;

	/** 不覆審日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELFNCKDDATE", columnDefinition="DATE")
	private Date elfNckdDate;

	/** 不覆審備註 **/
	@Column(name="ELFNCKDMEMO", length=202, columnDefinition="VARCHAR(202)")
	private String elfNckdMemo;

	/** 取得分行代碼 **/
	public String getElfBranch() {
		return this.elfBranch;
	}
	/** 設定分行代碼 **/
	public void setElfBranch(String value) {
		this.elfBranch = value;
	}

	/** 取得應覆審日 **/
	public Date getElfCrDate() {
		return this.elfCrDate;
	}
	/** 設定應覆審日 **/
	public void setElfCrDate(Date value) {
		this.elfCrDate = value;
	}

	/** 取得調整後應覆審日 **/
	public Date getChgCrDate() {
		return this.chgCrDate;
	}
	/** 設定調整後應覆審日 **/
	public void setChgCrDate(Date value) {
		this.chgCrDate = value;
	}

	/** 取得調整原因 **/
	public String getChgReason() {
		return this.chgReason;
	}
	/** 設定調整原因 **/
	public void setChgReason(String value) {
		this.chgReason = value;
	}

	/** 取得調整後不覆審代碼 **/
	public String getChgNckdFlag() {
		return this.chgNckdFlag;
	}
	/** 設定調整後不覆審代碼 **/
	public void setChgNckdFlag(String value) {
		this.chgNckdFlag = value;
	}
	
	/** 取得上次覆審日 **/
	public Date getElfLrDate() {
		return this.elfLrDate;
	}
	/** 設定上次覆審日 **/
	public void setElfLrDate(Date value) {
		this.elfLrDate = value;
	}

	/** 取得上次土建融實地覆審日 **/
	public Date getElfLastRealDt() {
		return this.elfLastRealDt;
	}
	/** 設定上次土建融實地覆審日 **/
	public void setElfLastRealDt(Date value) {
		this.elfLastRealDt = value;
	}

	/** 取得覆審規則 **/
	public String getElfRemomo() {
		return this.elfRemomo;
	}
	/** 設定覆審規則 **/
	public void setElfRemomo(String value) {
		this.elfRemomo = value;
	}

	/** 取得不覆審代碼 **/
	public String getElfNckdFlag() {
		return this.elfNckdFlag;
	}
	/** 設定不覆審代碼 **/
	public void setElfNckdFlag(String value) {
		this.elfNckdFlag = value;
	}

	/** 取得不覆審日期 **/
	public Date getElfNckdDate() {
		return this.elfNckdDate;
	}
	/** 設定不覆審日期 **/
	public void setElfNckdDate(Date value) {
		this.elfNckdDate = value;
	}

	/** 取得不覆審備註 **/
	public String getElfNckdMemo() {
		return this.elfNckdMemo;
	}
	/** 設定不覆審備註 **/
	public void setElfNckdMemo(String value) {
		this.elfNckdMemo = value;
	}
}
