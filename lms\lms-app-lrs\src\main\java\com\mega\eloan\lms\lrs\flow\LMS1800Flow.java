package com.mega.eloan.lms.lrs.flow;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;

import com.mega.eloan.common.flow.AbstractFlowHandler;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LrsUtil;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.dao.L180M01ADao;
import com.mega.eloan.lms.lrs.service.LMS1700Service;
import com.mega.eloan.lms.lrs.service.LMS1801Service;
import com.mega.eloan.lms.mfaloan.bean.ELF412;
import com.mega.eloan.lms.mfaloan.bean.ELF412B;
import com.mega.eloan.lms.mfaloan.bean.ELF412C;
import com.mega.eloan.lms.mfaloan.bean.ELF493;
import com.mega.eloan.lms.mfaloan.service.MisELF412BService;
import com.mega.eloan.lms.mfaloan.service.MisELF412CService;
import com.mega.eloan.lms.mfaloan.service.MisELF412Service;
import com.mega.eloan.lms.model.L180M01A;
import com.mega.eloan.lms.model.L180M01B;
import com.mega.eloan.lms.model.L186M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

@Component
public class LMS1800Flow extends AbstractFlowHandler {

	private static final int MAXLEN_ELF412_NCKDMEMO = StrUtils
			.getEntityFileldLegth(ELF412.class, "elf412_nckdMemo", 202);

	@Resource
	LMS1801Service lms1801Service;

	@Resource
	L180M01ADao l180m01aDao;

	@Resource
	RetrialService retrialService;

	@Resource
	LMS1700Service lms1700Service;

	@Autowired
	DocFileService fileService;

	@Resource
	MisELF412Service misELF412Service;

	@Resource
	MisELF412BService misELF412BService;

	@Resource
	MisELF412CService misELF412CService;

	@Transition(node = "區中心_編製中", value = "呈主管")
	public void send(FlowInstance instance) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		L180M01A meta = retrialService.findL180M01A_oid(Util.trim(instance
				.getId()));
		meta.setApprId(user.getUserId());
		l180m01aDao.save(meta);

		lms1801Service.gfnGenCTLListExcel(meta);
	}

	private void gfnDB2UpELF412_NCKD(List<ELF412> elf412_list, L180M01B model,
			ELF412 elf412, String userId, L180M01A l180m01a) {
		Date elf412_lrDate = elf412.getElf412_lrDate();
		String elf412_nckdFlag = elf412.getElf412_nckdFlag();
		Date elf412_nckdDate = elf412.getElf412_nckdDate();
		String elf412_nckdMemo = elf412.getElf412_nckdMemo();
		Date elf412_nextNwDt = elf412.getElf412_nextNwDt();
		Date elf412_nextLtDt = elf412.getElf412_nextLtDt();
		String elf412_newAdd = elf412.getElf412_newAdd();
		String elf412_newDate = elf412.getElf412_newDate();

		// J-108-0078_05097_B1001
		// 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。
		String elf412_isAllNew = Util.trim(elf412.getElf412_isAllNew());

        // 2020/04 配合新冠肺炎紓困貸款專案，新增 J.純紓困貸款戶之首次覆審。
        String elf412_isRescue = elf412.getElf412_isRescue();
		String elf412_guarFlag = elf412.getElf412_guarFlag();
		String elf412_newRescue = elf412.getElf412_newRescue();
        String elf412_newRescueYM = elf412.getElf412_newRescueYM();

        // J-110-0272 抽樣覆審
        String elf412_randomType = Util.trim(elf412.getElf412_randomType());

		if (Util.isNotEmpty(Util.trim(model.getElfNCkdFlag()))
				&& Util.isEmpty(Util.trim(model.getNewNCkdFlag()))) {
			// '不覆審 改成 要覆審
			if (CrsUtil.isNOT_null_and_NOTZeroDate(model.getNewLRDate())) {
				elf412_lrDate = model.getNewLRDate();
			} else {
				// the same
			}

			elf412_nckdFlag = "";
			elf412_nckdDate = CapDate.parseDate(CapDate.ZERO_DATE);
			elf412_nckdMemo = "";
			elf412_nextNwDt = CapDate.parseDate(CapDate.ZERO_DATE);
			elf412_nextLtDt = CapDate.parseDate(CapDate.ZERO_DATE);

		} else {
			// '要覆審 改成 不要覆審
			if (CrsUtil.isNull_or_ZeroDate(elf412.getElf412_lrDate())) {
				if (Util.equals(LrsUtil.NCKD_8_本次暫不覆審, model.getNewNCkdFlag())) {
					// the same
				} else {
					elf412_lrDate = new Date();
				}
			}
			elf412_nckdFlag = model.getNewNCkdFlag();
			elf412_nckdDate = new Date();
			elf412_nckdMemo = Util.truncateString(
					Util.trim(model.getNewNCkdMemo()), MAXLEN_ELF412_NCKDMEMO);
			elf412_nextNwDt = CrsUtil.isNOT_null_and_NOTZeroDate(model
					.getNewNextNwDt()) ? model.getNewNextNwDt() : CapDate
					.parseDate(CapDate.ZERO_DATE);
			elf412_nextLtDt = CapDate.parseDate(CapDate.ZERO_DATE);

			// 要覆審 → 不覆審 之後,在上傳ELF412時, 要把 ELF412_NEWADD 清空
			// 不然在 月初 執行批次後 gfnCTL_Caculate_ELF412 又會變為要覆審
			if (Util.notEquals(LrsUtil.NCKD_8_本次暫不覆審, model.getNewNCkdFlag())) {
				elf412_newAdd = "";
				elf412_newDate = "";
				// J-108-0078_05097_B1001
				// 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。
				elf412_isAllNew = "";
                // 2020/04 配合新冠肺炎紓困貸款專案，新增 J.純紓困貸款戶之首次覆審。
                elf412_isRescue = "";
				elf412_guarFlag = "";
				elf412_newRescue = "";
                elf412_newRescueYM = "";
                // J-110-0272 抽樣覆審
                // 未抽中的要壓不覆審代碼13，視為本次已覆審 ==> 做過覆審就可以清空 randomType
                elf412_randomType = "";
				// 未抽中的視為本次已覆審，ELF412上次覆審日 = 該名單預計覆審日
				if(Util.equals(model.getNewNCkdFlag(),
						LrsUtil.NCKD_13_有效額度NTD1000w信保七成以上或十足擔保之含有循環動用案件_未列於抽樣需覆審名單內)) {
					elf412_lrDate = l180m01a.getDefaultCTLDate();
				}
			}
		}

		Timestamp nowTS = CapDate.getCurrentTimestamp();
		elf412.setElf412_lrDate(elf412_lrDate);
		elf412.setElf412_newAdd(elf412_newAdd);
		elf412.setElf412_newDate(elf412_newDate);
		// J-108-0078_05097_B1001
		// 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。
		elf412.setElf412_isAllNew(elf412_isAllNew);
        // 2020/04 配合新冠肺炎紓困貸款專案，新增 J.純紓困貸款戶之首次覆審。
        elf412.setElf412_isRescue(elf412_isRescue);
		elf412.setElf412_guarFlag(elf412_guarFlag);
		elf412.setElf412_newRescue(elf412_newRescue);
        elf412.setElf412_newRescueYM(elf412_newRescueYM);
        // J-110-0272 抽樣覆審
        elf412.setElf412_randomType(elf412_randomType);

		elf412.setElf412_nckdFlag(elf412_nckdFlag);
		elf412.setElf412_nckdDate(elf412_nckdDate);
		elf412.setElf412_nckdMemo(elf412_nckdMemo);
		elf412.setElf412_nextNwDt(elf412_nextNwDt);
		elf412.setElf412_nextLtDt(elf412_nextLtDt);
		elf412.setElf412_tmestamp(nowTS);
		elf412.setElf412_upddate(nowTS);
		elf412.setElf412_updater(userId);
		// ---
		elf412_list.add(elf412);
	}

	@Transition(node = "確認", value = "核定")
	public void accept(FlowInstance instance) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String instanceId = Util.trim(instance.getId());
		L180M01A l180m01a = retrialService.findL180M01A_oid(instanceId);
		// 寫入文件核定者
		l180m01a.setApprover(user.getUserId());
		l180m01a.setApproveTime(CapDate.getCurrentTimestamp());
		// 上傳ELF493用
		l180m01a.setBossId(user.getUserId());
		l180m01aDao.save(l180m01a);
		// ---
		List<L180M01B> l180m01b_list = retrialService
				.findL180M01BDefaultOrder(l180m01a.getMainId());
		if (l180m01b_list != null && l180m01b_list.size() > 0) {
			List<ELF412> elf412_list = new ArrayList<ELF412>();
			List<ELF412B> elf412b_list = new ArrayList<ELF412B>();
			List<ELF412C> elf412c_list = new ArrayList<ELF412C>();

			for (L180M01B l180m01b : l180m01b_list) {
				if (Util.isNotEmpty(Util.trim(l180m01b.getNewNCkdFlag()))) {
					// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
					if (Util.equals(l180m01b.getCtlType(), LrsUtil.CTLTYPE_自辦覆審)) {
						ELF412B elf412b = misELF412BService.findByPk(
								l180m01a.getBranchId(), l180m01b.getCustId(),
								l180m01b.getDupNo());
						if (elf412b == null) {
							continue;
						}
						gfnDB2UpELF412B_NCKD(elf412b_list, l180m01b, elf412b,
								user.getUserId());
					} else if (Util.equals(l180m01b.getCtlType(),
							LrsUtil.CTLTYPE_價金履約)) {
						ELF412C elf412c = misELF412CService.findByPk(
								l180m01a.getBranchId(), l180m01b.getCustId(),
								l180m01b.getDupNo());
						if (elf412c == null) {
							continue;
						}
						gfnDB2UpELF412C_NCKD(elf412c_list, l180m01b, elf412c,
								user.getUserId());
					} else {
						ELF412 elf412 = misELF412Service.findByPk(
								l180m01a.getBranchId(), l180m01b.getCustId(),
								l180m01b.getDupNo());
						if (elf412 == null) {
							continue;
						}
						gfnDB2UpELF412_NCKD(elf412_list, l180m01b, elf412,
								user.getUserId(), l180m01a);

						// J-110-0272 抽樣覆審
						// 小規模案件回壓 L186M01A
						if(Util.equals(Util.trim(l180m01b.getIsSmallBuss()), "Y")){
							// && Util.equals(Util.trim(l180m01b.getElfRandomType()), LrsUtil.RANDOMTYPE_A_有效額度NTD1000w信保七成以上或十足擔保之含有循環動用案件)
							L186M01A l186m01a = retrialService.findL186M01AByUniqueKey(null, Util.trim(elf412.getElf412_branch()),
									Util.trim(elf412.getElf412_custId()), Util.trim(elf412.getElf412_dupNo()),
									LrsUtil.RANDOMTYPE_A_有效額度NTD1000w信保七成以上或十足擔保之含有循環動用案件);
							if (l186m01a != null) {
								l186m01a.setIsSmallBuss(Util.trim(l180m01b.getIsSmallBuss()));
								l186m01a.setUpdateTime(CapDate.getCurrentTimestamp());
								retrialService.save(l186m01a);
							}
						}
					}

				}
			}

			if (CollectionUtils.isNotEmpty(elf412_list)) {
				// retrialService.upELF412_DelThenInsert(elf412_list);
				// J-106-0145-006 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
				for (ELF412 elf412 : elf412_list) {
					misELF412Service
							.updateELF412NckdFlag(elf412.getElf412_lrDate(),
									elf412.getElf412_newAdd(),
									elf412.getElf412_newDate(),
									elf412.getElf412_nckdFlag(),
									elf412.getElf412_nckdDate(),
									elf412.getElf412_nckdMemo(),
									elf412.getElf412_nextNwDt(),
									elf412.getElf412_nextLtDt(),
									elf412.getElf412_tmestamp(),
									elf412.getElf412_upddate(),
									elf412.getElf412_updater(),
									elf412.getElf412_branch(),
									elf412.getElf412_custId(),
									elf412.getElf412_dupNo(),
									elf412.getElf412_isRescue(),
									elf412.getElf412_guarFlag(),
									elf412.getElf412_newRescue(),
									elf412.getElf412_newRescueYM(),
									elf412.getElf412_randomType());
				}

			}

			if (CollectionUtils.isNotEmpty(elf412b_list)) {
				// retrialService.upELF412B_DelThenInsert(elf412b_list);
				// J-106-0145-006 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
				for (ELF412B elf412b : elf412b_list) {
					misELF412BService.updateELF412BNckdFlag(
							elf412b.getElf412b_lrDate(),
							elf412b.getElf412b_newAdd(),
							elf412b.getElf412b_newDate(),
							elf412b.getElf412b_nckdFlag(),
							elf412b.getElf412b_nckdDate(),
							elf412b.getElf412b_nckdMemo(),
							elf412b.getElf412b_nextNwDt(),
							elf412b.getElf412b_nextLtDt(),
							elf412b.getElf412b_tmestamp(),
							elf412b.getElf412b_upddate(),
							elf412b.getElf412b_updater(),
							elf412b.getElf412b_branch(),
							elf412b.getElf412b_custId(),
							elf412b.getElf412b_dupNo());
				}
			}

			if (CollectionUtils.isNotEmpty(elf412c_list)) {
				for (ELF412C elf412c : elf412c_list) {
					misELF412CService.updateELF412CNckdFlag(
							elf412c.getElf412c_lrDate(),
							elf412c.getElf412c_newAdd(),
							elf412c.getElf412c_newDate(),
							elf412c.getElf412c_nckdFlag(),
							elf412c.getElf412c_nckdDate(),
							elf412c.getElf412c_nckdMemo(),
							elf412c.getElf412c_nextNwDt(),
							elf412c.getElf412c_nextLtDt(),
							elf412c.getElf412c_tmestamp(),
							elf412c.getElf412c_upddate(),
							elf412c.getElf412c_updater(),
							elf412c.getElf412c_branch(),
							elf412c.getElf412c_custId(),
							elf412c.getElf412c_dupNo());
				}
			}
		}
		if (true) {
			List<ELF493> elf493_list = lms1801Service.gfnDB2UpELF493(l180m01a,
					l180m01b_list, user.getUserId());
			// 先依 elf493_rptDocId 刪除
			retrialService.upELF493_Del_by_rptDocId(LrsUtil
					.elf493_rptDocId(l180m01a));
			// 再上傳
			retrialService.upELF493_DelThenInsert(elf493_list);
		}

		delExcel(l180m01a);
	}

	private void delExcel(L180M01A meta) {
		// '清空覆審名單EXCEL檔 99.8.27 授管處郭慧珠襄理提 經辦必須於名單已核准後再次產生正式發文用的覆審名單EXCEL
		String fieldId = LrsUtil.ATTCH_L180M01A_0;
		List<DocFile> docFiles = fileService.findByIDAndName(meta.getMainId(),
				fieldId, "");
		if (docFiles != null && !docFiles.isEmpty()) {
			for (DocFile file : docFiles) {
				fileService.clean(file.getOid());
			}
		}
	}

	@Transition(node = "確認", value = "退回")
	public void back(FlowInstance instance) {

		String instanceId = Util.trim(instance.getId());
		L180M01A meta = retrialService.findL180M01A_oid(instanceId);

		delExcel(meta);
	}

	@Transition(node = "流程控制", value = "退回")
	public void back_2(FlowInstance instance) {

	}

	@Transition(node = "流程控制", value = "完成結案")
	public void send_to_br(FlowInstance instance) {

		String instanceId = Util.trim(instance.getId());
		L180M01A l180m01a = retrialService.findL180M01A_oid(instanceId);

		// 傳送 Btt
		try {
			retrialService.sendBtt(l180m01a);
		} catch (Exception e) {

		}
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return L180M01A.class;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Class getDocStatusEnumClass() {
		return RetrialDocStatusEnum.class;
	}

	// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
	private void gfnDB2UpELF412B_NCKD(List<ELF412B> elf412b_list,
			L180M01B model, ELF412B elf412b, String userId) {
		Date elf412b_lrDate = elf412b.getElf412b_lrDate();
		String elf412b_nckdFlag = elf412b.getElf412b_nckdFlag();
		Date elf412b_nckdDate = elf412b.getElf412b_nckdDate();
		String elf412b_nckdMemo = elf412b.getElf412b_nckdMemo();
		Date elf412b_nextNwDt = elf412b.getElf412b_nextNwDt();
		Date elf412b_nextLtDt = elf412b.getElf412b_nextLtDt();
		String elf412b_newAdd = elf412b.getElf412b_newAdd();
		String elf412b_newDate = elf412b.getElf412b_newDate();

		if (Util.isNotEmpty(Util.trim(model.getElfNCkdFlag()))
				&& Util.isEmpty(Util.trim(model.getNewNCkdFlag()))) {
			// '不覆審 改成 要覆審
			if (CrsUtil.isNOT_null_and_NOTZeroDate(model.getNewLRDate())) {
				elf412b_lrDate = model.getNewLRDate();
			} else {
				// the same
			}

			elf412b_nckdFlag = "";
			elf412b_nckdDate = CapDate.parseDate(CapDate.ZERO_DATE);
			elf412b_nckdMemo = "";
			elf412b_nextNwDt = CapDate.parseDate(CapDate.ZERO_DATE);
			elf412b_nextLtDt = CapDate.parseDate(CapDate.ZERO_DATE);

		} else {
			// '要覆審 改成 不要覆審
			if (CrsUtil.isNull_or_ZeroDate(elf412b.getElf412b_lrDate())) {
				if (Util.equals(LrsUtil.NCKD_8_本次暫不覆審, model.getNewNCkdFlag())) {
					// the same
				} else {
					elf412b_lrDate = new Date();
				}
			}
			elf412b_nckdFlag = model.getNewNCkdFlag();
			elf412b_nckdDate = new Date();
			elf412b_nckdMemo = Util.truncateString(
					Util.trim(model.getNewNCkdMemo()), MAXLEN_ELF412_NCKDMEMO);
			elf412b_nextNwDt = CrsUtil.isNOT_null_and_NOTZeroDate(model
					.getNewNextNwDt()) ? model.getNewNextNwDt() : CapDate
					.parseDate(CapDate.ZERO_DATE);
			elf412b_nextLtDt = CapDate.parseDate(CapDate.ZERO_DATE);

			// 要覆審 → 不覆審 之後,在上傳ELF412時, 要把 ELF412_NEWADD 清空
			// 不然在 月初 執行批次後 gfnCTL_Caculate_ELF412 又會變為要覆審
			if (Util.notEquals(LrsUtil.NCKD_8_本次暫不覆審, model.getNewNCkdFlag())) {
				elf412b_newAdd = "";
				elf412b_newDate = "";
			}
		}

		Timestamp nowTS = CapDate.getCurrentTimestamp();
		elf412b.setElf412b_lrDate(elf412b_lrDate);
		elf412b.setElf412b_newAdd(elf412b_newAdd);
		elf412b.setElf412b_newDate(elf412b_newDate);
		elf412b.setElf412b_nckdFlag(elf412b_nckdFlag);
		elf412b.setElf412b_nckdDate(elf412b_nckdDate);
		elf412b.setElf412b_nckdMemo(elf412b_nckdMemo);
		elf412b.setElf412b_nextNwDt(elf412b_nextNwDt);
		elf412b.setElf412b_nextLtDt(elf412b_nextLtDt);
		elf412b.setElf412b_tmestamp(nowTS);
		elf412b.setElf412b_upddate(nowTS);
		elf412b.setElf412b_updater(userId);
		// ---
		elf412b_list.add(elf412b);
	}

	// J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
	private void gfnDB2UpELF412C_NCKD(List<ELF412C> elf412c_list,
			L180M01B model, ELF412C elf412c, String userId) {
		Date elf412c_lrDate = elf412c.getElf412c_lrDate();
		String elf412c_nckdFlag = elf412c.getElf412c_nckdFlag();
		Date elf412c_nckdDate = elf412c.getElf412c_nckdDate();
		String elf412c_nckdMemo = elf412c.getElf412c_nckdMemo();
		Date elf412c_nextNwDt = elf412c.getElf412c_nextNwDt();
		Date elf412c_nextLtDt = elf412c.getElf412c_nextLtDt();
		String elf412c_newAdd = elf412c.getElf412c_newAdd();
		String elf412c_newDate = elf412c.getElf412c_newDate();

		if (Util.isNotEmpty(Util.trim(model.getElfNCkdFlag()))
				&& Util.isEmpty(Util.trim(model.getNewNCkdFlag()))) {
			// '不覆審 改成 要覆審
			if (CrsUtil.isNOT_null_and_NOTZeroDate(model.getNewLRDate())) {
				elf412c_lrDate = model.getNewLRDate();
			} else {
				// the same
			}

			elf412c_nckdFlag = "";
			elf412c_nckdDate = CapDate.parseDate(CapDate.ZERO_DATE);
			elf412c_nckdMemo = "";
			elf412c_nextNwDt = CapDate.parseDate(CapDate.ZERO_DATE);
			elf412c_nextLtDt = CapDate.parseDate(CapDate.ZERO_DATE);

		} else {
			// '要覆審 改成 不要覆審
			if (CrsUtil.isNull_or_ZeroDate(elf412c.getElf412c_lrDate())) {
				if (Util.equals(LrsUtil.NCKD_8_本次暫不覆審, model.getNewNCkdFlag())) {
					// the same
				} else {
					elf412c_lrDate = new Date();
				}
			}
			elf412c_nckdFlag = model.getNewNCkdFlag();
			elf412c_nckdDate = new Date();
			elf412c_nckdMemo = Util.truncateString(
					Util.trim(model.getNewNCkdMemo()), MAXLEN_ELF412_NCKDMEMO);
			elf412c_nextNwDt = CrsUtil.isNOT_null_and_NOTZeroDate(model
					.getNewNextNwDt()) ? model.getNewNextNwDt() : CapDate
					.parseDate(CapDate.ZERO_DATE);
			elf412c_nextLtDt = CapDate.parseDate(CapDate.ZERO_DATE);

			// 要覆審 → 不覆審 之後,在上傳ELF412時, 要把 ELF412_NEWADD 清空
			// 不然在 月初 執行批次後 gfnCTL_Caculate_ELF412 又會變為要覆審
			if (Util.notEquals(LrsUtil.NCKD_8_本次暫不覆審, model.getNewNCkdFlag())) {
				elf412c_newAdd = "";
				elf412c_newDate = "";
			}
		}

		Timestamp nowTS = CapDate.getCurrentTimestamp();
		elf412c.setElf412c_lrDate(elf412c_lrDate);
		elf412c.setElf412c_newAdd(elf412c_newAdd);
		elf412c.setElf412c_newDate(elf412c_newDate);
		elf412c.setElf412c_nckdFlag(elf412c_nckdFlag);
		elf412c.setElf412c_nckdDate(elf412c_nckdDate);
		elf412c.setElf412c_nckdMemo(elf412c_nckdMemo);
		elf412c.setElf412c_nextNwDt(elf412c_nextNwDt);
		elf412c.setElf412c_nextLtDt(elf412c_nextLtDt);
		elf412c.setElf412c_tmestamp(nowTS);
		elf412c.setElf412c_upddate(nowTS);
		elf412c.setElf412c_updater(userId);
		// ---
		elf412c_list.add(elf412c);
	}

}