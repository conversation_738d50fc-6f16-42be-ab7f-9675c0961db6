/**
 * 產品種類js
 */
$(function(json){  
    // 讀取產品種類Grid
    gridS17();
    // 讀取產品種類Grid(ThickBox)
    _gridS17();
});

var initsS17 = {
    // kindType
    contractType: responseJSON.contractType,
    // handler name
    fhandle: "lms9990m06formhandler",
    ghandle: "lms9990gridhandler",
    
    // grid method
    gridQuery: "queryC999s01a",
    
    // button method
    modifyItemNo: "modifyItemNo",
    delItemNo: "delItemNo",
    canDelItemNo: "canDelItemNo",
    
    // sub method	
    saveSubAction: (responseJSON.contractType == 'A') ? "saveS17" : "saveS17A",
    querySubAction: (responseJSON.contractType == 'A') ? "queryS17" : "queryS17A",
    resetSubAction: (responseJSON.contractType == 'A') ? "resetS17" : "resetS17A",
	
    dataSaveS17: function(oid){
		return {
			oid: oid,
			mainId: responseJSON.mainId,			
			formTab02a: JSON.stringify($("#formTab02a").serializeData()),
			formTab03a: JSON.stringify($("#formTab03a").serializeData()),
			formTab04a: JSON.stringify($("#formTab04a").serializeData()),
			formTab05a: JSON.stringify($("#formTab05a").serializeData()),
			formTab06a: JSON.stringify($("#formTab06a").serializeData()),
			formTab07a: JSON.stringify($("#formTab07a").serializeData()),
			
			s20Radio: $("#formTab04a").find("[name='_20Ara']:radio:checked").val(),
			s21Radio: $("#formTab05a").find("[name='_21Ara']:radio:checked").val(),
			s22Radio: $("#formTab06a").find("[name='_22Ara']:radio:checked").val(),
			s23aRadio: $("#formTab07a").find("[name='_23Ara']:radio:checked").val(),
			s23bRadio: $("#formTab07a").find("[name='_23Arb']:radio:checked").val()
		}
    },
    dataSaveS17A: function(oid){
		return {
			oid: oid,
			mainId: responseJSON.mainId,
			formTab02b: JSON.stringify($("#formTab02a").serializeData()),
			formTab03b: JSON.stringify($("#formTab03b").serializeData()),
			formTab04b: JSON.stringify($("#formTab04b").serializeData()),
			formTab05b: JSON.stringify($("#formTab05b").serializeData()),
			formTab06b: JSON.stringify($("#formTab06b").serializeData()),
			formTab07b: JSON.stringify($("#formTab07b").serializeData()),
			formTab08b: JSON.stringify($("#formTab08b").serializeData())
		}
    },
    queryS17Rlt: function(responseData){
        var formTab04a = "#formTab04a";
        var formTab05a = "#formTab05a";
        var formTab06a = "#formTab06a";
        var formTab07a = "#formTab07a";
        
        $("#formTab02a").setData(responseData.formTab02a, false);
        $("#formTab03a").setData(responseData.formTab03a, false);
        $(formTab04a).setData(responseData.formTab04a, false);
        $(formTab05a).setData(responseData.formTab05a, false);
        $(formTab06a).setData(responseData.formTab06a, false);
        $(formTab07a).setData(responseData.formTab07a, false);
        
        // 進行Radio設定
        setRadio(formTab04a, "_20Ara", DOMPurify.sanitize(responseData.s20Radio), false);
        setRadio(formTab05a, "_21Ara", DOMPurify.sanitize(responseData.s21Radio), false);
        setRadio(formTab06a, "_22Ara", DOMPurify.sanitize(responseData.s22Radio), false);
        setRadio(formTab07a, "_23Ara", DOMPurify.sanitize(responseData.s23aRadio), false);
        setRadio(formTab07a, "_23Arb", DOMPurify.sanitize(responseData.s23bRadio), false);
    },
	resetS17Panel: function(responseData){
            var formTab03a = "#formTab03a";
            var formTab04a = "#formTab04a";
            var formTab05a = "#formTab05a";
            var formTab06a = "#formTab06a";
            var formTab07a = "#formTab07a";
			
            $("#formTab02a").setData(responseData.formTab02a, false);
            $(formTab03a).setData(responseData.formTab03a, false);
            $(formTab04a).setData(responseData.formTab04a, false);
            $(formTab05a).setData(responseData.formTab05a, false);
            $(formTab06a).setData(responseData.formTab06a, false);
            $(formTab07a).setData(responseData.formTab07a, false);

            // 清空checkBox
            clearChkBox(formTab03a);
            clearChkBox(formTab04a);
            clearChkBox(formTab05a);
            clearChkBox(formTab06a);
            clearChkBox(formTab07a);
            
            //clearChkBox("#tab-09a");
            
            // 清空Radio
            setRadio(formTab04a, "_20Ara", "", true);
            setRadio(formTab05a, "_21Ara", "", true);
            setRadio(formTab06a, "_22Ara", "", true);
            setRadio(formTab07a, "_23Ara", "", true);
            setRadio(formTab07a, "_23Arb", "", true);		
	},	
    queryS17ARlt: function(responseData){
        $("#formTab02b").setData(responseData.formTab02b, false);
        $("#formTab03b").setData(responseData.formTab03b, false);
        $("#formTab04b").setData(responseData.formTab04b, false);
        $("#formTab05b").setData(responseData.formTab05b, false);
        $("#formTab06b").setData(responseData.formTab06b, false);
        $("#formTab07b").setData(responseData.formTab07b, false);
        $("#formTab08b").setData(responseData.formTab08b, false);
    },
	checkValidS17: function(){
		if (initsS17.contractType == 'A') {
			return ($("#formTab02a").valid() && 
			$("#formTab03a").valid() && $("#formTab04a").valid() && 
			$("#formTab05a").valid() && $("#formTab06a").valid() && 
			$("#formTab07a").valid());
		}else{
			return ($("#formTab02b").valid() && 
			$("#formTab03b").valid() && $("#formTab04b").valid() && 
			$("#formTab05b").valid() && $("#formTab06b").valid() && 
			$("#formTab07b").valid() && $("#formTab08b").valid());			
		}		
	}
};

/**
 * 控制顯示/隱藏切換(顯示狀態則隱藏並清空值；隱藏狀態則顯示)
 */
function hideOrShow(id){
    if ($(id).is(":hidden")) {
        $(id).show();
    }
    else {
        $(id).hide();
        $(id).find("input").val("");
    }
}

/**
 * 產品種類Grid
 */
function gridS17(){
    var gridS17 = $("#gridS17").iGrid({
        handler: initsS17.ghandle,
        action: initsS17.gridQuery,
        height: 200,
        hiddengrid: false,
        rowNum: 25,
        sortname: 'itemNo',
        postData: {
            mainId: responseJSON.mainId,
            isThick: false
        },
        colModel: [{
            colHeader: i18n.lms9990m07['C999M01AM07.grid01'],// "項次",
            name: 'itemNo',
            width: 40,
            sortable: true,
            formatter: function(data){
                // 取得項次對應文字
                return getItemNo(data);
            }
        }, {
            colHeader: i18n.lms9990m07['C999M01AM07.grid02'],// "額度序號",
            formatter: 'click',
            onclick: openDoc,
            name: 'cntrNo',
            width: 120,
            sortable: true
        }, {
            colHeader: i18n.lms9990m07['C999M01AM07.grid03'],// "產品種類",
            name: 'prodKind',
            width: 70,
            sortable: true
        }, {
            colHeader: i18n.lms9990m07['C999M01AM07.grid04'],// "科目",
            name: 'subjCode',
            width: 70,
            sortable: true
        }, {
            colHeader: i18n.lms9990m07['C999M01AM07.grid05'],// "幣別",
            name: 'loanCurr',
            width: 70,
            sortable: true
        }, {
            colHeader: i18n.lms9990m07['C999M01AM07.grid06'],// "金額",
            name: 'loanAmt',
            align: "right",
            width: 70,
            sortable: true,
            formatter: function(data){
                if (data == null) {
                    return "";
                }
                else {
                    // 加入撇節符號
                    return util.addComma(data);
                }
            }
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }, {
            colHeader: "uid",
            name: 'uid',
            hidden: true
        }, {
            colHeader: "mainId",
            name: 'mainId',
            hidden: true
        }],
        ondblClickRow: function(rowid){ // 當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = gridS17.getRowData(rowid);
            openDoc(null, null, data);
        }
    });
}

/**
 * 開啓產品種類Grid分頁內容
 */
function openDoc(cellvalue, options, rowObject){
    if ($("#loadPanelS17").attr("openFlag") == "true") {
        $("#loadPanelS17").load("../../ctr/lms9990s17/01", function(){
            queryS17(rowObject.oid);
        });
        $("#loadPanelS17").attr("openFlag", false);
    }
    else {
        queryS17(rowObject.oid);
    }
}

/**
 * 查詢產品種類內容後開啓ThickBox
 */
function queryS17(oid){
    $.ajax({
        type: "POST",
        handler: initsS17.fhandle,
        action: initsS17.resetSubAction,
        data: {
            oid: oid
        },
    }).done(function(responseData){
		(initsS17.contractType == "A") ? initsS17.resetS17Panel(responseData) : initsS17.queryS17ARlt(responseData);
		 // 清空標題
		 $("#itemNo").html("");
		 $("#cntrNo").html("");
		 $("#prodKind").html("");

		 // 開始查詢產品種類	
		 $.ajax({
		     type: "POST",
		     handler: initsS17.fhandle,
		     action: initsS17.querySubAction,
		     data: {
		         oid: oid,
		         mainId: responseJSON.mainId
		     },
		 }).done(function(responseData){
			(initsS17.contractType == "A") ? initsS17.queryS17Rlt(responseData) : initsS17.queryS17ARlt(responseData);
			// 進行標題設定
			$("#itemNo").html(DOMPurify.sanitize(responseData.itemNo));
			$("#cntrNo").html(DOMPurify.sanitize(responseData.cntrNo));
			$("#prodKind").html(DOMPurify.sanitize(responseData.prodKind));


			// 開啟thickBox
			openDocGridS17(oid);			
		 });		
	});
}

/**
 * 清空Radio值並隱藏
 */
function clearChkBox(id){
    $(id).find(":checkbox").each(function(i){
        $(this).prop("checked", false);
    });
    $(id).find(".hide").hide();
}

/**
 * Radio設值到前端
 */
function setRadio(id, rName, val, isReset){
    $(id).find("[name='" + rName + "']").each(function(i){
        var $this = $(this);
        if (isReset) {
            $this.prop("checked", false);
        }
        else {
            if ($this.val() == val) {
                $this.prop("checked", true);
            }
        }
    });
}

/**
 * 產品種類Grid分頁ThickBox
 */
function openDocGridS17(oid){
    var contractType = responseJSON.contractType;
    if (contractType == "A") {
        $(".contractTypeA").show();
	    //每次開啟都是第一頁
	    $("#tabs-s17").tabs("option","active",0);
    }else if (contractType == "B") {
        $(".contractTypeB").show();
	    $("#tabs-s17").tabs("option","active",6);
    }
    $("#openDocGridS17").thickbox({
        // 使用選取的內容進行彈窗
        title: i18n.lms9990m07['C999M01AM07.title01'],
        width: 800,
        height: 500,
        modal: true,
        i18n: i18n.def,
        buttons: {
            "saveData": function(){
                if (initsS17.checkValidS17()) {					
                    $.ajax({
                        type: "POST",
                        handler: initsS17.fhandle,
                        action: initsS17.saveSubAction,					
                        data: (initsS17.contractType == "A") ? initsS17.dataSaveS17(oid) : initsS17.dataSaveS17A(oid),
                    }).done(function(responseData){
						// alert(JSON.stringify(responseData));
						 $("#gridS17").trigger("reloadGrid"); // 更新Grid內容
						 $.thickbox.close();
						 $.thickbox.close();
						 CommonAPI.showMessage(responseData.NOTIFY_MESSAGE);						
					});
                }
            },
            "close": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

/**
 * 產品種類Grid(ThickBox)
 */
function _gridS17(){
    var _gridS17 = $("#_gridS17").iGrid({
        handler: initsS17.ghandle,
        action: initsS17.gridQuery,
        height: 200,
        hiddengrid: false,
        rowNum: 25,
        sortname: 'cntrNo',
        /*
         multiselect : true,
         hideMultiselect : false,
         */
        postData: {
            mainId: responseJSON.mainId,
            isThick: true
        },
        colModel: [{
            colHeader: i18n.lms9990m07['C999M01AM07.grid01'],// "項次",
            name: 'itemNo',
            width: 40,
            sortable: true,
            formatter: function(cellvalue, options, rowObject, action){
                if (cellvalue == "99") {
                    // 若為'刪除'則直接顯示(純文字)，不需產下拉式選單
                    return selItem[cellvalue];
                }
                else {
                    // 將項次產成下拉式選單 
                    var result = '<select id="selItemNo'+options.rowId+'" >';
                    for (var key in selItem) {
                        result += '<option value="' + key + '" ' + (cellvalue == key ? ' selected ' : '') + '>' + selItem[key] + '</option>';
                    }
                    result += '</select>';
                    return result;
                }
            }
        }, {
            colHeader: i18n.lms9990m07['C999M01AM07.grid02'],// "額度序號",
            name: 'cntrNo',
            width: 120,
            sortable: true
        }, {
            colHeader: i18n.lms9990m07['C999M01AM07.grid03'],// "產品種類",
            name: 'prodKind',
            width: 70,
            sortable: true
        }, {
            colHeader: i18n.lms9990m07['C999M01AM07.grid04'],// "科目",
            name: 'subjCode',
            width: 70,
            sortable: true
        }, {
            colHeader: i18n.lms9990m07['C999M01AM07.grid05'],// "幣別",
            name: 'loanCurr',
            width: 70,
            sortable: true
        }, {
            colHeader: i18n.lms9990m07['C999M01AM07.grid06'],// "金額",
            name: 'loanAmt',
            align: "right",
            width: 70,
            sortable: true,
            formatter: function(data){
                if (data == null) {
                    return "";
                }
                else {
                    // 加入撇節符號
                    return util.addComma(data);
                }
            }
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }, {
            colHeader: "uid",
            name: 'uid',
            hidden: true
        }, {
            colHeader: "mainId",
            name: 'mainId',
            hidden: true
        }]
    });
}

/**
 * 更新產品種類Grid
 */
function ugridS17(){
    $("#gridS17").jqGrid("setGridParam", {
        postData: {
            formAction: initsS17.gridQuery,
            isThick: false
        },
        search: true
    }).trigger("reloadGrid");
}

/**
 * 更新產品種類(ThickBox)
 */
function _ugridS17(){
    $("#_gridS17").jqGrid("setGridParam", {
        postData: {
            formAction: initsS17.gridQuery,
            isThick: true
        },
        search: true
    }).trigger("reloadGrid");
}

/**
 * 指定項次ThickBox
 */
function thickModify(){
    _ugridS17();
    $("#thickModify").thickbox({
        // 使用選取的內容進行彈窗
        title: i18n.lms9990m07['C999M01AM07.setIndex'],
        width: 640,
        height: 400,
        modal: true,
        align: "center",
        valign: "bottom",
        i18n: i18n.def,
        buttons: {
            "sure": function(){
                var ids = new Array();
                ids = $("#_gridS17").jqGrid('getDataIDs');
                var listOid = "";
                var listItemNo = "";
                var sign = ",";
                var count = 0;
				var index = 1;
                for (var id in ids) {
                    var rows = $("#_gridS17").jqGrid('getRowData', ids[id]);
                    if (rows.oid != 'undefined' && rows.oid != null && rows.oid != "") {
                        listOid += ((listOid == "") ? "" : sign) + rows.oid;
                    }
                    if (rows.itemNo != 'undefined' && rows.itemNo != null && rows.itemNo != "") {					
                        listItemNo += ((listItemNo == "") ? "" : sign) + $("#selItemNo"+index).val();
                    }
                    count++;
					index++;
                }
                if (count == 0) {
                    // grid.emptyrecords=查無資料
                    CommonAPI.showMessage(i18n.def["grid.emptyrecords"]);
                    return;
                }
                else {
                    $.ajax({
                        type: "POST",
                        handler: initsS17.fhandle,
                        action: initsS17.modifyItemNo,
                        data: {
                            oids: listOid,
                            itemNos: listItemNo
                        },
                    }).done(function(responseData){
						$("#gridS17").trigger("reloadGrid"); // 更新Grid內容
						 $.thickbox.close();
						 $.thickbox.close();
						 CommonAPI.showMessage(responseData.NOTIFY_MESSAGE);
					});
                }
            },
            "cancel": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

/**
 * 刪除指定項次
 */
function delItemNo(){
    var row = $("#gridS17").getGridParam('selrow');
    var list = "";
    var data = $("#gridS17").getRowData(row);
    list = data.oid;
    if (list == "" || list == undefined) {
        // grid_selector=請選擇資料
        CommonAPI.showMessage(i18n.def["grid_selector"]);
        return;
    }
    else {
        // action_003=是否確定「刪除」此筆資料?
        CommonAPI.confirmMessage(i18n.def["action_003"], function(b){
            if (b) {
                $.ajax({
                    type: "POST",
                    handler: initsS17.fhandle,
                    action: initsS17.delItemNo,
                    data: {
                        oid: list
                    },
                }).done(function(responseData){
					$("#gridS17").trigger("reloadGrid"); // 更新Grid內容
					$.thickbox.close();
					$.thickbox.close();
					CommonAPI.showMessage(responseData.NOTIFY_MESSAGE);
				});
            }
        });
    }
}

/**
 * 取消刪除指定項次
 */
function canDelItemNo(){
    var row = $("#gridS17").getGridParam('selrow');
    var list = "";
    var data = $("#gridS17").getRowData(row);
    list = data.oid;
    if (list == "" || list == undefined) {
        // grid_selector=請選擇資料
        CommonAPI.showMessage(i18n.def["grid_selector"]);
        return;
    }
    else {
        // confirmRun=是否確定執行此功能?
        CommonAPI.confirmMessage(i18n.def["confirmRun"], function(b){
            if (b) {
                $.ajax({
                    type: "POST",
                    handler: initsS17.fhandle,
                    action: initsS17.canDelItemNo,
                    data: {
                        oid: list
                    },
                }).done(function(responseData){
					$("#gridS17").trigger("reloadGrid"); // 更新Grid內容
					$.thickbox.close();
					$.thickbox.close();
					CommonAPI.showMessage(responseData.NOTIFY_MESSAGE);					
				});
            }
        });
    }
}

/**
 * 項次對應表(目前內容暫定，之後做修改) -- Grid Select用
 */
var selItem = {
    "1": "甲",
    "2": "乙",
    "3": "丙",
    "4": "丁",
    "5": "戊",
    "6": "己",
    "7": "庚",
    "8": "辛",
    "9": "壬",
    "10": "癸",
    "11": "子",
    "12": "丑",
    "13": "寅",
    "14": "卯",
    "15": "辰",
    "16": "巳",
    "17": "午",
    "18": "未",
    "19": "申",
    "20": "酉",
    "21": "戌",
    "22": "亥"
}

/**
 * 項次對應表(目前內容暫定，之後做修改)
 */
function getItemNo(itemNo){
    var json = {};
    json["1"] = "甲";
    json["2"] = "乙";
    json["3"] = "丙";
    json["4"] = "丁";
    json["5"] = "戊";
    json["6"] = "己";
    json["7"] = "庚";
    json["8"] = "辛";
    json["9"] = "壬";
    json["10"] = "癸";
    json["11"] = "子";
    json["12"] = "丑";
    json["13"] = "寅";
    json["14"] = "卯";
    json["15"] = "辰";
    json["16"] = "巳";
    json["17"] = "午";
    json["18"] = "未";
    json["19"] = "申";
    json["20"] = "酉";
    json["21"] = "戌";
    json["22"] = "亥";
    json["99"] = "刪除";
    if (json[itemNo] == null || json[itemNo] == undefined || json[itemNo] == '') {
        return "";
    }
    else {
        return json[itemNo];
    }
}
