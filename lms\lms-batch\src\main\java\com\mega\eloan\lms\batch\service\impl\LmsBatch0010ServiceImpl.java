package com.mega.eloan.lms.batch.service.impl;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.constants.SysParamConstants;
import com.mega.eloan.common.gwclient.EmailClient;
import com.mega.eloan.common.gwclient.IdentificationCheckGwClient;
import com.mega.eloan.common.gwclient.IdentificationCheckGwReqMessage;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.ClsUtility;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.RPAProcessService;
import com.mega.eloan.lms.dao.L820M01ADao;
import com.mega.eloan.lms.dao.L820M01CDao;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.fms.service.LMS8200Service;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C101S01A;
import com.mega.eloan.lms.model.L820M01A;
import com.mega.eloan.lms.model.L820M01B;
import com.mega.eloan.lms.model.L820M01C;
import com.mega.eloan.lms.model.L820M01E;
import com.mega.eloan.lms.model.L820M01S;
import com.mega.eloan.lms.model.L820M01W;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.annotation.NonTransactional;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.core.FlowException;


/**
 * J-111-0525_11850_B1001 Web
 * e-Loan  為減少撥款前人工查詢作業，於E-LOAN系統建置「以房養老貸款撥款前查詢結果」，系統發查有身分證補換發資料及司法院受輔助監護宣告查詢，
 * 系統發查(撥款日前1日)結果正常或分行人工查詢，即照預定撥款日撥款，系統撥款前1個營業日未處理，則停止撥款。
 *
 */
@Service("lmsbatch0010serviceimpl")
public class LmsBatch0010ServiceImpl extends AbstractCapService implements
		WebBatchService {

	private Logger logger = LoggerFactory.getLogger(this.getClass());
	
	@Resource
	L820M01ADao l820m01aDao;
	@Resource
	L820M01CDao l820m01cDao;
	
	@Resource
	SysParameterService sysParamService;

	@Resource
	LMSService lmsService;
	
	@Resource
	CLSService clsService;
	
	@Resource
	MisdbBASEService misdbBASEService;
	
	@Resource
	MisCustdataService misCustdataService;
	
	@Resource
	DwdbBASEService dwdbBASEService;
	
	@Resource
	LMS8200Service lms8200Service;
	
	@Resource
	RPAProcessService rpaProcessService;
	
	@Resource
	IdentificationCheckGwClient identificationCheckGwClient;

	@Resource
	private EmailClient emailClient;
	@Resource
	private SysParameterService sysParameterService;
	
	@Override
	@NonTransactional
	public JSONObject execute(JSONObject json) {
		// @NonTransactional
		JSONObject result = new JSONObject();
		JSONObject request = json.getJSONObject("request");
		StringBuffer errMsg = new StringBuffer("");
		try {	
			String runType = Util.trim(request.getString("runType"));//1.發查 , 2.接收回傳資料
			
			if("1".equals(runType)){//執行rpa發查相關
				this.doPostRpaIdentBatch(request, result, errMsg);
			}else if("2".equals(runType)){//檢查已房養老案件(L820M01A)是否有尚未覆核之system自動發查資料，檢查身分證、RPA發查結果
				this.doAutoApproveL820M01A(request, result, errMsg);
			}else {
				result = WebBatchCode.RC_ERROR;
				result.element(WebBatchCode.P_RESPONSE, "LmsBatch0010ServiceImpl-doPostRpaIdentBatch執行失敗！==> runType 參數有誤");
			}
			
			if (Util.notEquals(errMsg.toString(), "")) {
				result = WebBatchCode.RC_ERROR;
				result.element(WebBatchCode.P_RESPONSE, "LmsBatch0010ServiceImpl runType: " + runType + "執行失敗！==>" + errMsg);
			} 
			else{	
				result = WebBatchCode.RC_SUCCESS;
				result.element(WebBatchCode.P_RESPONSE, "LmsBatch0010ServiceImpl runType: " + runType + "執行成功！");
			}
		}catch (Exception ex) {
			logger.error(StrUtils.getStackTrace(ex));
			result = WebBatchCode.RC_ERROR;
			String msg = ex.getMessage();
			result.element(WebBatchCode.P_RESPONSE, "clsBatchSuspectedAgentAppCaseServiceImpl 執行失敗！==>" + msg + " - " + ex.getLocalizedMessage());
		}
		return result;
	}

	/**
	 * 檢查已房養老案件(L820M01A)是否有尚未覆核之system自動發查資料，檢查身分證、RPA發查結果
	 * @param request
	 * @return
	 */
	@NonTransactional
	public String doAutoApproveL820M01A(JSONObject request, JSONObject result, StringBuffer errMsg) {
		//StringBuffer errMsg = new StringBuffer("");
		String datadate = Util.trim(request.getString("dataDate"));//有傳入日期則以此日為基準
		String nowdate = Util.isEmpty(datadate) ? CapDate.getCurrentDate("yyyy-MM-dd") : datadate;
		
		//檢查今日尚未處理完成的已房養老案件
		List<L820M01A> l820m01alist = l820m01aDao.findByDocStatus(CreditDocStatusEnum.海外_編製中.getCode(), nowdate);
		
		if (!Util.isEmpty(l820m01alist)) {
			for (L820M01A ll820m01a : l820m01alist) {
				//J-113-0392 新增財富管理發查WealthResult
				if(("system".equals(ll820m01a.getUpdater()) && "system".equals(ll820m01a.getUpdater())) 
						&& ("Y".equals(ll820m01a.getIdeSearchResult()) && "Y".equals(ll820m01a.getFaSearchResult()) && "Y".equals(ll820m01a.getWealthResult()))){
					ll820m01a.setUpdateTime(CapDate.getCurrentTimestamp());
					ll820m01a.setApprover("system");
					ll820m01a.setApproveTime(CapDate.getCurrentTimestamp());
					
					@SuppressWarnings("unchecked")
					List<L820M01B> models = (List<L820M01B>) lms8200Service.findListByMainId(L820M01B.class, ll820m01a.getMainId());
					if (!models.isEmpty()) {
						lms8200Service.deleteL820m01bs(models, false);
					}
					//送呈主管
					L820M01B apprL820m01b = new L820M01B();
					apprL820m01b.setCreator("system");
					apprL820m01b.setCreateTime(CapDate.getCurrentTimestamp());
					apprL820m01b.setMainId(ll820m01a.getMainId());
					apprL820m01b.setStaffJob(UtilConstants.STAFFJOB.經辦L1);
					apprL820m01b.setStaffNo("system");
					apprL820m01b.setBranchType("B");
					apprL820m01b.setBranchId(ll820m01a.getOwnBrId());
					lms8200Service.autosave(apprL820m01b);
					
					//主管覆核
					L820M01B l820m01b = new L820M01B();
					l820m01b = new L820M01B();
					l820m01b.setCreator("system");
					l820m01b.setCreateTime(CapDate.getCurrentTimestamp());
					l820m01b.setMainId(ll820m01a.getMainId());
					l820m01b.setStaffJob(UtilConstants.STAFFJOB.執行覆核主管L4);
					l820m01b.setStaffNo("system");
					l820m01b.setBranchType("B");
					l820m01b.setBranchId(ll820m01a.getOwnBrId());
					
					lms8200Service.autosave(l820m01b);
					lms8200Service.autosave(ll820m01a);
					
					try {
						//待覆核
						lms8200Service.flowAction(ll820m01a.getOid(), ll820m01a, true, true, false);//upMis 先填false
						//已覆核
						lms8200Service.flowAction(ll820m01a.getOid(), ll820m01a, true, true, false);//upMis 先填false
						// 放行後 同步更新LN242對應的已房養老案件
						// 撈取l820m01c 向下的額度序號
						List<L820M01C> l820m01cs = l820m01cDao.findByMainId(ll820m01a.getMainId());
						// 同步更新 LN.LNF242，一個額度序號只會有一筆 所以不看放款帳號
						for (L820M01C l820m01c : l820m01cs) {
							//放行後將L242  LNF242_IDCHG_FG, LNF242_WARD_FG, LNF242_WEALTH_FG 註記為N (N為可撥款，這邊跟L820相反)
							misdbBASEService.updateLNF242("N", "N", "N", ll820m01a.getCustId(), ll820m01a.getDupNo(), l820m01c.getCntrNo());
						}
					} catch (FlowException t1) {
						logger.error("[flowAction] doChangeL820M01ADocStatus.flowAction FlowException!!", t1);
						errMsg.append("已房養老案件(L820M01A) 尚未覆核之資料 自動覆核失敗 oid=" + ll820m01a.getOid());
					} catch (Throwable t1) {
						logger.error("[flowAction]  doChangeL820M01ADocStatus.flowAction EXCEPTION!!",t1);
						errMsg.append("已房養老案件(L820M01A) 尚未覆核之資料 自動覆核失敗 oid=" + ll820m01a.getOid());
					}
				}else if (("N".equals(ll820m01a.getIdeSearchResult()) || "N".equals(ll820m01a.getFaSearchResult()) || "N".equals(ll820m01a.getWealthResult()))){
					//20230512改發報表簽核系統，先註解
					//身分證換補or 家事查詢 or 財富管理 發查未通過，發notes提醒案件所屬分行T1
					//this.notifyByMail(ll820m01a);
					try {
						// 撈取l820m01c 向下的額度序號
						List<L820M01C> l820m01cs = l820m01cDao.findByMainId(ll820m01a.getMainId());
						// 同步更新 LN.LNF242，一個額度序號只會有一筆 所以不看放款帳號
						for (L820M01C l820m01c : l820m01cs) {
							//放行後將L242  LNF242_IDCHG_FG, LNF242_WARD_FG 註記為N (N為可撥款，這邊跟L820相反)
							misdbBASEService.updateLNF242(
									"Y".equals(ll820m01a.getIdeSearchResult()) ? "N" : "Y", 
									"Y".equals(ll820m01a.getFaSearchResult()) ? "N" : "Y", 
									"Y".equals(ll820m01a.getWealthResult()) ? "N" : "Y", 
									ll820m01a.getCustId(), 
									ll820m01a.getDupNo(), 
									l820m01c.getCntrNo()
							);
						}
					} catch (FlowException t1) {
						logger.error("[flowAction] doChangeL820M01ADocStatus.flowAction FlowException!!", t1);
						errMsg.append("已房養老案件(L820M01A) 尚未覆核之資料 自動覆核失敗 oid=" + ll820m01a.getOid());
					} catch (Throwable t1) {
						logger.error("[flowAction]  doChangeL820M01ADocStatus.flowAction EXCEPTION!!",t1);
						errMsg.append("已房養老案件(L820M01A) 尚未覆核之資料 自動覆核失敗 oid=" + ll820m01a.getOid());
					}
				}
			}

		}

		return errMsg.toString();
	}
	/**
	 * 執行rpa發查相關
	 * @param request
	 * @return
	 */
	@NonTransactional
	public String doPostRpaIdentBatch(JSONObject request, JSONObject result, StringBuffer errMsg) {
		//StringBuffer errMsg = new StringBuffer("");
		String datadate = Util.trim(request.getString("dataDate"));//有傳入日期則以此日為基準
		String nowdate = Util.isEmpty(datadate) ? CapDate.getCurrentDate("yyyy-MM-dd") : datadate;
		
		//取得營業日資訊
		Map<String, Object> lnf320 = misdbBASEService.get_LNF320(nowdate);
		
		if (MapUtils.isEmpty(lnf320)) {
			// (1):非營業日：不執行
		    result = JSONObject.fromObject(WebBatchCode.RC_SUCCESS);
		    result.element(WebBatchCode.P_RESPONSE, "LmsBatch0010ServiceImpl 執行成功！今日非營業日");
		}else{
			// (2):營業日
			//撥款日落在批次發動日~下次營業日之間的帳號來查詢，
			//假設排程的執行日是2021-02-09，就要查詢撥款日是落在 2021-02-10 ~2021-02-17 (因為10-16放假過年，所以10-16號的撥款也會延後到17號撥款)，
			//就要自動發查身分證換補發跟家事查詢
			//a. 取得
			//SQL=>SELECT LNF320_QUERY_DATE + 1 DAY AS BGNDATE,LNF320_NEXT_DATE as ENDDATE  FROM LN.LNF320 where LNF320_QUERY_DATE = '2021-02-09'

			String basedate = MapUtils.getString(lnf320, "LNF320_QUERY_DATE", "");
			String chkdate = CapDate.shiftDaysString(basedate, "yyyy-MM-dd", 1);//當天+1
			String nextdate = MapUtils.getString(lnf320, "LNF320_NEXT_DATE", "");
			//撈 aloan 以房養老控制檔 取得區間內的資料 寫進L820M01A
			List<Map<String, Object>> lnf242_contractas = null; 
			//新增排除條件, 依據 custid + loanno(帳號) 串到LNF030, 如已消戶則不發查
			if(!nextdate.equals(chkdate)){
				//跨假日, 區間內的一起查
				lnf242_contractas = misdbBASEService.get_LNF242ByNxtpdate(chkdate, nextdate);
			}else{
				//排程啟動當下撈取隔天預計要撥款的案件
				lnf242_contractas = misdbBASEService.get_LNF242ByNxtpdate(chkdate, chkdate);
			}
			

			if(lnf242_contractas != null){
				ArrayList<String> has_query_custid = new ArrayList<String>();
				for (Map<String, Object> lnf242 : lnf242_contractas) {
					String lnf242_custid = MapUtils.getString(lnf242, "LNF242_CUST_ID", "");
					String lnf242_contract = MapUtils.getString(lnf242, "LNF242_CONTRACT", "");
					String custId = lnf242_custid.substring(0, lnf242_custid.length()-1);
					String dupNo = lnf242_custid.substring(lnf242_custid.length()-1);
					//String createTime = String.valueOf(lnf242.get("LNF242_NXTDP_DATE"));
					//取得後先判斷該筆當天是否已經發動建立過l820m01a，如有就不再建立，僅重新發動身分證及家事調查
					L820M01A l820m01a = l820m01aDao.findByConst(custId, dupNo, basedate);
					//L820M01A l820m01a = l820m01aDao.findByConst(custId, dupNo, createTime);
					
					if (Util.isEmpty(l820m01a)){
						//未建立, 建立L820M01A 
						l820m01a = this.newL820M01A(lnf242, custId, dupNo, basedate, nextdate);
					}
					
					L820M01C l820m01c = l820m01cDao.findByUniqueKey(l820m01a.getMainId(), l820m01a.getCustId(), l820m01a.getDupNo(), lnf242_contract);
					if (Util.isEmpty(l820m01c)){
						//建立明細表	
						l820m01c = new L820M01C();
						l820m01c.setUid(null);
						l820m01c.setMainId(l820m01a.getMainId());
						l820m01c.setCustId(l820m01a.getCustId());
						l820m01c.setDupNo(l820m01a.getDupNo());
						l820m01c.setCntrNo(lnf242_contract);
						l820m01c.setLnf242_loan_no(MapUtils.getString(lnf242, "LNF242_LOAN_NO", ""));	
						l820m01c.setEstimateTime(new Timestamp(CapDate.parseDate(MapUtils.getString(lnf242, "LNF242_NXTDP_DATE", "")).getTime()));
						l820m01c.setActualTime(new Timestamp(CapDate.parseDate(nextdate).getTime()));
						lms8200Service.autosave(l820m01c);
					}

					//已建立，重新執行發查就好
					//同客戶僅需發查一次
					if(!has_query_custid.contains(custId)){
						try {
							this.do_keep_EJ_ST_query(l820m01a, CrsUtil.API_TXID_ID_CARD_CHECK, errMsg);//身分證換補
							this.do_keep_EJ_ST_query(l820m01a, CrsUtil.RPA_TXID_FA, errMsg);//家事
							this.do_DW_Wealth_query(l820m01a, chkdate, errMsg);//財富管理
							has_query_custid.add(custId);
						} catch (Exception e) {
							l820m01a.setIdeSearchResult("N");
							l820m01a.setFaSearchResult("N");
							logger.info("LmsBatch0010Service doPostRpaIdentBatch custId:" + custId + "發查失敗");
							errMsg.append("LmsBatch0010Service doPostRpaIdentBatch custId:" + custId + "發查失敗");
						}
					}
					//
					try {
						lms8200Service.autosave(l820m01a);
					} catch (Exception e) {
						logger.info("LmsBatch0010Service doPostRpaIdentBatch custId:" + custId + "l820m01a存取失敗");
						errMsg.append("LmsBatch0010Service doPostRpaIdentBatch custId:" + custId + "l820m01a存取失敗");
					}
				}
			}
		}
		return errMsg.toString();
	}
	
	private void notifyByMail(L820M01A ll820m01a) {

		String hostAddr = sysParameterService.getParamValue(SysParamConstants.MAIL_ADDRESS_HOST);
		String[] toAddrs = null;
		StringBuilder custStr = new StringBuilder();

		// 取得案號
		// 若為個人戶，需作資料隱碼 (身分證後三碼半形O，客戶名稱第二個字 全形Ｏ)
		custStr.append("客戶ID：");
		custStr.append((ll820m01a.getCustId().substring(0, 7) + "OOO "));
		custStr.append(ll820m01a.getDupNo() + " ");
		custStr.append(" 客戶名稱：");
		custStr.append((ll820m01a.getCustName().substring(0, 1) + "Ｏ" + ll820m01a.getCustName().substring(2)));
		custStr.append("\n");
		//custStr.append("尚未完成以房養老撥款前查詢作業，請至SSO >> e-LOAN系統 >> 授信管理系統 >> 建檔維護 >> 以房養老案件查詢結果 確認詳細資訊！");
		custStr.append("以房養老貸款「撥款日前系統發查」失敗，請授信經辦今日至SSO行員專用網站>>e-Loan系統>>授信管理系統>>建檔維護>>以房養老案件查詢結果>>編製中>>確認查詢結果，查詢結果無異常後，請呈主管覆核，並請主管於今日覆核完畢。");
		custStr.append("\n");
		custStr.append("請注意：今日未處理完畢者，系統無法於撥款日執行自動撥款，請授信經辦於撥款日執行臨櫃撥款(L010)。");
		custStr.append("\n");
		
		
		if (StringUtils.indexOf(hostAddr, "@notes.") >= 0) { // Production
			//次營業日未結之案件，notes通知所屬分行T1
			String ReceiverAddr = ll820m01a.getOwnBrId() + "T1" + hostAddr;
			toAddrs = ReceiverAddr.split(",");//先預留
		} else {
			// 測試用，暫時寄給冠霖
			String testReceiverAddr = "<EMAIL>";
			toAddrs = testReceiverAddr.split(",");
		}

		/**
		 * 傳送Text Email
		 * 
		 * @param subject
		 *            主旨
		 * @param body
		 *            訊息內容
		 * @param toAddr
		 *            接收者Email清單
		 */
		String subject = "以房養老貸款案件「撥款日前查詢結果通知」：下列借款人「撥款日前系統發查」失敗，請授信經辦盡速處理。";
		emailClient.send(toAddrs, subject, custStr.toString());
	}
	
	/**
	 * 接收RPA 司法院受輔助監護宣告查詢 結果
	 * @param request
	 * @param result
	 * @return
	 */
	@NonTransactional
	public String doResponseRpaIdentBatch(JSONObject request, JSONObject result) {
		StringBuffer errMsg = new StringBuffer("");
		
		return errMsg.toString();
	}
	
	/**
	 * J-111-0525_11850_B1001 Web
	 * 系統發查有身分證補換發資料及司法院受輔助監護宣告查詢，款日如果非營業日，ALOAN會順延到下一營業日才撥款
	 */
	@NonTransactional
	public L820M01A newL820M01A(Map<String, Object> lnf242, String custId, String dupNo, String basedate, String nextdate) {
		//
		String lnf242_ownbrid = MapUtils.getString(lnf242, "LNF242_CONTRACT", "").substring(0,3);
		Map<String, Object> custmap = misCustdataService.findCNameByIdDupNo(custId,dupNo);
		L820M01A l820m01a = new L820M01A();
		l820m01a.setDocStatus(CreditDocStatusEnum.海外_編製中.getCode());
		l820m01a.setOwnBrId(lnf242_ownbrid);
		l820m01a.setMainId(IDGenerator.getUUID());
		l820m01a.setTxCode("336112");
		// UPGRADE: 待確認，URL怎麼取得
		// l820m01a.setDocURL(LMS8200M01Page.class.getAnnotation(MountPath.class).path());
		l820m01a.setCustId(custId);
		l820m01a.setDupNo(dupNo);
		l820m01a.setCustName(MapUtils.getString(custmap, "CNAME", ""));
		l820m01a.setCreateTime(new Timestamp(CapDate.parseDate(basedate).getTime()));
		//l820m01a.setCreator("system");//自動產生放system
		//l820m01a.setUpdater("system");//自動產生放system
		//l820m01a.setDeletedTime(CapDate.getCurrentTimestamp());
		//lms8200Service.save(l820m01a);
		
		return l820m01a;
	}
	
	public void do_keep_EJ_ST_query(L820M01A l820m01a, String param_txId, StringBuffer errMsg) throws Exception{
		//L820M01A l820m01a = lms8200Service.findModelByOid(L820M01A.class, l820m01a_old.getOid());
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainId = Util.trim(l820m01a.getMainId());
		String custId = Util.trim(l820m01a.getCustId());
		String dupNo = Util.trim(l820m01a.getDupNo());
		//L820M01A l820m01a = l820m01aDao.findByMainId(mainId);
		//查詢===========================================================
		if (Util.equals(param_txId, CrsUtil.API_TXID_ID_CARD_CHECK)){
			//內政部國民身分證領換補資料
			//check 身分證領補換資料「發證日期 發證地點 領補換類別」是否有輸入
			//這邊根據取得的 custid , 額度序號, 產品種類 至 L140M01A額度明細(新做) => L120M01C關聯 => L120M01A簽報書=> C120M01A徵信資料 取得相關資訊
			//取得額度明細表
			//C120S01A c120s01a = lms8200Service.findC120m01aByConst(cntrNo, custId, dupNo);
			// 撈取l820m01c 向下的額度序號, 額度序號前三碼為分行
			List<L820M01C> l820m01cs = l820m01cDao.findByMainId(l820m01a.getMainId());
			String BRNO = null;
			for (L820M01C l820m01c : l820m01cs) {
				//取得分行資料
				BRNO = (Util.trim(l820m01c.getCntrNo()).length() > 0 ? l820m01c.getCntrNo().substring(0, 3) : "");
			}
			//取得該客戶最新的簽報書中引入的徵信資料
			//C120S01A c120s01a = lms8200Service.findC120m01aByConst(custId, dupNo);
			//2023.06.07 消金郭守明科長確認改抓"個金徵信"中最新的資料
			//check 身分證領補換資料「發證日期 發證地點 領補換類別」是否有輸入
			C101S01A c101s01a = lms8200Service.findC101s01aByConst(BRNO, custId, dupNo);
			if (c101s01a == null) {
				logger.info("無法取得身分證領補相關資料 custId:" + custId);
			}
			String dataStatus = "1"; //查詢結果 0成功，1失敗
			String[] _Z21_qkey1_dataArr = get_Z21_qkey1_dataArr(c101s01a);
			boolean validate_Z21 = validate_Z21_qkey1_dataArr(_Z21_qkey1_dataArr);
			if(validate_Z21){//檢合無缺資訊
				//------------------------------------------------------------
				String applyCode = convert_c120s01a_IdCardChgFlag_to_z21_applyReasonCode(c101s01a.getIdCardChgFlag());
				String applyYYYMMDD = CapDate.formatDate(c101s01a.getIdCardIssueDate(), "YYYMMDD");
				IdentificationCheckGwReqMessage req = new IdentificationCheckGwReqMessage(c101s01a.getCustId(), applyCode, applyYYYMMDD, 
																this.clsService.getSiteIdByApiInquiry(c101s01a.getIdCard_siteId()), user.getUserId());
				identificationCheckGwClient.send(req);
				
				Calendar currentDateTime = Calendar.getInstance();
				Timestamp nowTS = new Timestamp(currentDateTime.getTimeInMillis());
				byte[] reportData = this.lms8200Service.generateJSONObjectForIdCardCheck(req, currentDateTime);
				/*
				 	1=國民身分證資料與檔存資料相符。 
				 	2=身分證字號 XXXXXXXXXX 目前驗證資料錯誤次數已達 1 次，今日錯誤累積達 3 次後，此身分證字號將無法查詢。 
				 	3=身分證字號 XXXXXXXXXX 目前驗證資料錯誤次數已達 2 次，今日錯誤累積達 3 次後，此身分證字號將無法查詢。 
				 	4=身分證字號 XXXXXXXXXX 目前驗證資料錯誤次數已達 3 次，今日錯誤累積達 3 次後，此身分證字號將無法查詢。 
				 	5=身分證字號 XXXXXXXXXX 驗證資料錯誤次數已達 3 次。今 日無法查詢，請明日再查！！ 
				 	6=您所查詢的國民身分證字號 XXXXXXXXXX 已停止使用。 
				 	7=您所查詢的國民身分證 XXXXXXXXXX，業依當事人申請登 錄掛失。 
				 	8=單一使用者出現異常使用情形，暫停使用者權限。
				*/
				dataStatus = "1".equals(req.getCheckIdCardApply()) ? "0" : "1";
				L820M01S l820m1s = lms8200Service.modifyL820M01SForMixRecordData(mainId, c101s01a.getCustId(), c101s01a.getDupNo(), "5", dataStatus, reportData);
				if (l820m1s != null) {
					l820m1s.setCreateTime(nowTS);
					l820m1s.setUpdateTime(nowTS);
					l820m1s.setDataCreateTime(nowTS);
					lms8200Service.autosave(l820m1s);
				}
			}

			if (l820m01a != null) {
				if("0".equals(dataStatus)){//發查狀態  1=國民身分證資料與檔存資料相符，更新 L820M01A.IDESEARCHRESULT身分證發查成功寫Y否則寫N
					l820m01a.setIdeSearchResult("Y");
				}else{
					l820m01a.setIdeSearchResult("N");
				}
				//lms8200Service.save(l820m01a);
			}
		}else if(Util.equals(param_txId, CrsUtil.RPA_TXID_FA)){
			//司法院受監護/輔助宣告資料
			//this.rpaProcessService.deleteBeforeQueryL820(mainId, custId);
			//this.rpaProcessService.gotoRPAJobsForL820(new L820M01W(mainId, custId));
			//家事查詢新增retry機制
			this.doRpaJobs(mainId, custId, errMsg);
			
			if (l820m01a != null) {
				// * 家事查詢結果<p/> * 通過:Y * 未通過:N* 查詢中:I
				l820m01a.setFaSearchResult("I");
				//lms8200Service.save(l820m01a);
			}
		}else {
			errMsg.append("doLmsBatch0010 do_keep_EJ_ST_query 執行結果 : txId[" + param_txId + "] unknown。");
			logger.info("doLmsBatch0010 do_keep_EJ_ST_query 執行結果 : txId[" + param_txId + "] unknown。");
		}
		logger.info("doLmsBatch0010 do_keep_EJ_ST_query : txId[" + param_txId + "] 執行成功。");
	}
	/**
	 * X101010107▲1000210▲2▲1000110▲0▲68000000
	 * X123400025▲0950301▲1▲0300201▲1▲66000000 <br/>
	 * [0] 身分證號 char(10) <br/>
	 * [1] 領補換日期 char(7) yyy+mm+dd <br/>
	 * [2] 領補換代號 char(1) 1:初領,2:補領,3:換領 <br/>
	 * [3] 出生日期 char(7) yyy+mm+dd <br/>
	 * [4] 有無相片 char(1) 0:有,1:無 <br/>
	 * [5] 發證地點 char(8) 66000000:中市
	 */
	private String[] get_Z21_qkey1_dataArr(C101S01A c101s01a) {
		String[] arr = new String[6];
		for (int i = 0; i < arr.length; i++) {
			arr[i] = "";
		}
		if (c101s01a != null) {
			String char10 = "          ";
			// ~~~~~~~~~~
			arr[0] = Util.trim(c101s01a.getCustId());
			arr[1] = c101s01a.getIdCardIssueDate() == null ? Util.getLeftStr(char10, 7) : TWNDate.valueOf(c101s01a.getIdCardIssueDate()).toTW();
			arr[2] = convert_c120s01a_IdCardChgFlag_to_z21_applyReasonCode(c101s01a.getIdCardChgFlag());
			arr[3] = c101s01a.getBirthday() == null ? Util.getLeftStr(char10, 7) : TWNDate.valueOf(c101s01a.getBirthday()).toTW();
			arr[4] = convert_c120s01a_IdCardPhoto_to_z21_picCd(c101s01a.getIdCardPhoto());
			arr[5] = Util.trim(c101s01a.getIdCard_siteId());
		}
		return arr;
	}
	
	private String convert_c120s01a_IdCardChgFlag_to_z21_applyReasonCode(
			String idCardChgFlag) {
		return ClsUtility.convert_c101s01a_IdCardChgFlag_to_z21_applyReasonCode(idCardChgFlag);
	}
	
	private String convert_c120s01a_IdCardPhoto_to_z21_picCd(String idCardPhoto) {
		if (Util.equals("Y", idCardPhoto)) {
			return "0";
		} else if (Util.equals("N", idCardPhoto)) {
			return "1";
		} else if (Util.equals("", idCardPhoto)) {
			return " ";
		}
		return idCardPhoto;
	}
	
	private boolean validate_Z21_qkey1_dataArr(String[] dataArr){
		boolean validate_Z21 = true;
		if (dataArr != null && dataArr.length == 6) {
			if (Util.isEmpty(Util.trim(dataArr[3]))) {//未輸入生日
				validate_Z21 = false;
			} else if (dataArr[3].length() != 7) {//生日長度錯誤
				validate_Z21 = false;
			}
		
			if (Util.isEmpty(Util.trim(dataArr[1]))) {//未輸入領補換日期
				validate_Z21 = false;
			} else if (dataArr[1].length() != 7) {//領補換日期長度錯誤
				validate_Z21 = false;
			}
		
			if (Util.isEmpty(Util.trim(dataArr[5]))) {//發證地點未輸入
				validate_Z21 = false;
			} else if (dataArr[5].length() != 8) {//資料長度應為8
				validate_Z21 = false;
			}
		
			if (Util.isEmpty(Util.trim(dataArr[2]))) {//領補換代號 char(1) 1:初領,2:補領,3:換領
				validate_Z21 = false;
			} else if (dataArr[2].length() != 1) {//資料長度應為1
				validate_Z21 = false;
			}
		
			if (Util.isEmpty(Util.trim(dataArr[4]))) {//有無相片 char(1) 0:有,1:無
				validate_Z21 = false;
			} else if (dataArr[4].length() != 1) {//資料長度應為1
				validate_Z21 = false;
			}
	
		}
		return validate_Z21;
	}
	/**
	 * 家事查詢新增retry機制
	 * @param mainId
	 * @param custId
	 * @param errMsg
	 */
	private void doRpaJobs(String mainId, String custId, StringBuffer errMsg){
		int retrycount = 0;
		while(true){
			retrycount++;
			try{
				this.rpaProcessService.deleteBeforeQueryL820(mainId, custId);
				this.rpaProcessService.gotoRPAJobsForL820(new L820M01W(mainId, custId));
				return;
			} catch(Exception e){
				if(retrycount > 3){
					errMsg.append("RPA Job建立失敗，請稍後再試。");
				}
			}
		}
	}
	
	/**
	 * 財富管理查詢
	 * @param l820m01a
	 * @param errMsg
	 * @throws Exception
	 */
	public void do_DW_Wealth_query(L820M01A l820m01a, String checkDate, StringBuffer errMsg) throws Exception{
		//取得上個月資料
		// 上個月
		String toDayStr = checkDate;
		String toDayStr1 = toDayStr.replace("-", "");
		String newDateStr = CapDate.formatyyyyMMddToDateFormat(
				CapDate.addMonth(toDayStr1, -1),
				UtilConstants.DateFormat.YYYY_MM);
		// 上個月底最後一天
		String preMonLastDate = Util
				.toAD(CapDate.shiftDays(CapDate.addMonth(
						CapDate.parseDate(newDateStr + "-01"), 1), -1));
		// 上個月第一天
		String preMonFirstDate = Util.getLeftStr(preMonLastDate, 7) + "-01";
		// DW 資料日期
		Date dwQueryDate = null;//new Date();
		
		List<?> cupfmOtsRows = this.dwdbBASEService.findDW_MD_CUPFM_OTS_selCYC_MN(
				l820m01a.getCustId(), l820m01a.getDupNo(), preMonFirstDate, preMonLastDate);
		
		// 因倉儲會有資料時間差, 如月初尚未有新的資料進來, 則在往前抓衣個月的
		if(cupfmOtsRows.isEmpty() || cupfmOtsRows.size() == 0){
			newDateStr = CapDate.formatyyyyMMddToDateFormat(
					CapDate.addMonth(toDayStr1, -2),
					UtilConstants.DateFormat.YYYY_MM);
			preMonLastDate = Util
			.toAD(CapDate.shiftDays(CapDate.addMonth(
					CapDate.parseDate(newDateStr + "-01"), 1), -1));
			preMonFirstDate = Util.getLeftStr(preMonLastDate, 7) + "-01";
			//往前在推一個月
			cupfmOtsRows = this.dwdbBASEService.findDW_MD_CUPFM_OTS_selCYC_MN(
					l820m01a.getCustId(), l820m01a.getDupNo(), preMonFirstDate, preMonLastDate);
		}
		
		if (l820m01a != null) {
			L820M01E l820m01e = lms8200Service.genL820M01ERecordData(l820m01a.getMainId(), l820m01a.getCustId(), l820m01a.getDupNo());
			
			// * 財富管理查詢<p/> * 通過:Y * 未通過:N* 查詢中:I
			if(cupfmOtsRows.isEmpty()){
				//沒有資料 =>通過
				l820m01e.setDataSearchResult(UtilConstants.DEFAULT.是);//通過
				//l820m01e.setQueryTime(CapDate.convertStringToTimestamp(preMonLastDate+" 00:00:00"));//改放DW資料日//郭襄臨時要求補上資料日
				l820m01e.setQueryTime(CapDate.getCurrentTimestamp());
				l820m01a.setWealthResult(UtilConstants.DEFAULT.是);//通過
			}else{
				//確認相關項目
				BigDecimal in_tr_sc_e = new BigDecimal(0);//ETF、外國債劵、連動債	
				BigDecimal in_tr_fu = new BigDecimal(0);//基金
				BigDecimal in_wm_aum = new BigDecimal(0);//理財AUM(A)+(B)+(C)
				BigDecimal in_wm_fd = new BigDecimal(0);//(A)信託商品月底餘額
				BigDecimal in_wm_ia = new BigDecimal(0);//(B)累積已繳保費/保單價值
				BigDecimal in_wm_std = new BigDecimal(0);//(C)優利投資月平均餘額
				BigDecimal in_wm_bal = new BigDecimal(0);//累積下單金額
				BigDecimal in_wm_fee = new BigDecimal(0);//累計手收金額
				BigDecimal total_amt = new BigDecimal(0);//數字總額
				//1. 如果該戶無信託業務(ETF、國外債券、連動債、基金)及財富管理業務往來(不含資產AUM)，顯示通過，即照預定撥款日撥款。
				//2. 如果該戶有信託業務(ETF、國外債券、連動債、基金)及財富管理業務往來，顯示未通過，需請經辦人工判斷，填寫檢視結果後送呈主管覆核，主管覆核後顯示通過，即照預定撥款日撥款。未於撥款前1個營業日未處理完成，則停止撥款。
				Iterator<?> itcupfmOts = cupfmOtsRows.iterator();
				while (itcupfmOts.hasNext()) {
					Map<?, ?> dataMapCupfmOts = (Map<?, ?>) itcupfmOts.next();
					//ETF、外國債劵、連動債	
					in_tr_sc_e = in_tr_sc_e.add(new BigDecimal(Util.parseLong(Util
							.nullToSpace(dataMapCupfmOts.get("IN_TR_SC_E")))));
					//基金
					in_tr_fu = in_tr_fu.add(new BigDecimal(Util.parseLong(Util
							.nullToSpace(dataMapCupfmOts.get("IN_TR_FU")))));
					//(A)信託商品月底餘額
					in_wm_fd = in_wm_fd.add(new BigDecimal(Util.parseLong(Util
							.nullToSpace(dataMapCupfmOts.get("IN_WM_FD")))));
					//(B)累積已繳保費/保單價值
					in_wm_ia = in_wm_ia.add(new BigDecimal(Util.parseLong(Util
							.nullToSpace(dataMapCupfmOts.get("IN_WM_IA")))));
					//(C)優利投資月平均餘額
					in_wm_std = in_wm_std.add(new BigDecimal(Util.parseLong(Util
							.nullToSpace(dataMapCupfmOts.get("IN_WM_STD")))));
					//理財AUM(A)+(B)+(C)
					in_wm_aum = in_wm_aum
							.add(new BigDecimal(Util.parseLong(Util.nullToSpace(dataMapCupfmOts.get("IN_WM_FD")))))
							.add(new BigDecimal(Util.parseLong(Util.nullToSpace(dataMapCupfmOts.get("IN_WM_IA")))))
							.add(new BigDecimal(Util.parseLong(Util.nullToSpace(dataMapCupfmOts.get("IN_WM_STD")))));
					//累積下單金額
					in_wm_bal = in_wm_bal.add(new BigDecimal(Util.parseLong(Util
							.nullToSpace(dataMapCupfmOts.get("IN_WM_BAL")))));
					//累計手收金額
					in_wm_fee = in_wm_fee.add(new BigDecimal(Util.parseLong(Util
							.nullToSpace(dataMapCupfmOts.get("IN_WM_FEE")))));
					//DW資料日期
					dwQueryDate = Util.parseDate((Util.trim(dataMapCupfmOts.get("CYC_MN"))));
				}
				//數字總和
				total_amt = total_amt.add(in_tr_fu).add(in_tr_sc_e).add(in_wm_aum).add(in_wm_bal)
					.add(in_wm_fd).add(in_wm_fee).add(in_wm_ia).add(in_wm_std);
				
				l820m01e.setIn_tr_fu(in_tr_fu);
				l820m01e.setIn_tr_sc_e(in_tr_sc_e);
				l820m01e.setIn_wm_aum(in_wm_aum);
				l820m01e.setIn_wm_bal(in_wm_bal);
				l820m01e.setIn_wm_fd(in_wm_fd);
				l820m01e.setIn_wm_fee(in_wm_fee);
				l820m01e.setIn_wm_ia(in_wm_ia);
				l820m01e.setIn_wm_std(in_wm_std);
				//l820m01e.setQueryTime(CapDate.convertStringToTimestamp(preMonLastDate+" 00:00:00"));//改放DW資料日//郭襄臨時要求補上資料日
				l820m01e.setQueryTime(CapDate.getCurrentTimestamp());
				if(total_amt.compareTo(new BigDecimal(0)) > 0){//數字總和>0 表示有財富管理
					l820m01a.setWealthResult(UtilConstants.DEFAULT.否);//不通過
					l820m01e.setDataSearchResult(UtilConstants.DEFAULT.否);
				}else{
					l820m01a.setWealthResult(UtilConstants.DEFAULT.是);//通過
					l820m01e.setDataSearchResult(UtilConstants.DEFAULT.是);
				}
				l820m01e.setDwQueryDate(dwQueryDate);
				//l820m01a.setWealthResult(UtilConstants.DEFAULT.否);
			}
			lms8200Service.autosave(l820m01e);
		}
	
	}
}
