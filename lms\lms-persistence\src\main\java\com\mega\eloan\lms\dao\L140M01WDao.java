/*
 * L140M01WDao.java
 *
 * Copyright (c) 2011-2012 JC Software Services, Inc.
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 *
 * Licensed Materials - Property of JC Software Services, Inc.
 *
 * This software is confidential and proprietary information of
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140M01W;

/** 專案種類細項資訊檔 **/
public interface L140M01WDao extends IGenericDao<L140M01W> {

	L140M01W findByOid(String oid);

	List<L140M01W> findByMainId(String mainId);

	L140M01W findByUniqueKey(String mainId);

	List<L140M01W> findByIndex01(String mainId);
}