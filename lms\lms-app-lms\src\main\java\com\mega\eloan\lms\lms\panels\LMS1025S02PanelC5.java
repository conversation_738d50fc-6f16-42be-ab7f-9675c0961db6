package com.mega.eloan.lms.lms.panels;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.iisigroup.cap.utils.CapAppContext;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.model.C120M01A;

/**
 * <pre>
 * 相關查詢資料
 * </pre>
 * 
 * @since 2015/3/1
 * <AUTHOR>
 * @version <ul>
 *          <li>2015/3/1,EL08034,new
 *          </ul>
 */
public class LMS1025S02PanelC5 extends Panel {
	private static final long serialVersionUID = 1L;

	private CLSService clsService;

	public LMS1025S02PanelC5(String id) {
		super(id);
		this.clsService = CapAppContext.getBean("CLSService");
	}
	
	public LMS1025S02PanelC5(String id, boolean updatePanelName) {
		super(id, updatePanelName);
		this.clsService = CapAppContext.getBean("CLSService");
	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);

		String oid = params.getString(EloanConstants.OID);
		C120M01A meta = clsService.findC120M01A_oid(oid);	
		boolean showVedaReport = true;
		if (meta == null) {
			if(LMSUtil.get_FR_BRNO_SET().contains(params.getString("_branch")) 
					|| LMSUtil.get_CA_BRNO_SET().contains(params.getString("_branch"))){ //法國、加拿大
				showVedaReport = false;
			}
		} else {
			if(LMSUtil.get_FR_BRNO_SET().contains(meta.getOwnBrId()) 
					|| LMSUtil.get_CA_BRNO_SET().contains(meta.getOwnBrId())){ //法國、加拿大
				showVedaReport = false;
			}
		}
		model.addAttribute("showVedaReport", showVedaReport);
	}
}
