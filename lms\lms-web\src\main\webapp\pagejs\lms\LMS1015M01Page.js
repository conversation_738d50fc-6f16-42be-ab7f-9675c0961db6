var initDfd = initDfd || $.Deferred();
var _handler = "lms1015m01formhandler";
var __bef_c120m01a_oid = "bef_c120m01a_oid";

var printGrid_height = 200;

pageJsInit(function() {
	$(function() {
		var tabForm = $("#tabForm");
		var btnPanel = $("#buttonPanel");
		var initControl_lockDoc = false;
		$.form.init({
			formHandler: _handler,
			formAction: 'query',
			loadSuccess: function(json) {

				// 控制頁面 Read/Write
				if (!$("#buttonPanel").find("#btnSave").is("button") || json.lock) {
					tabForm.lockDoc();
					initControl_lockDoc = true;
					json['initControl_lockDoc'] = initControl_lockDoc;
				}
				tabForm.buildItem();
				if (json.page == "02") {
					$("#chooseC120M01ACustPosForm").buildItem();
				}
				tabForm.injectData(json);
				initDfd.resolve(json);
			}
		});

		btnPanel.find("#btnSave").click(function() {
			saveAction({ 'allowIncomplete': 'Y' }).done(function(json) {
				if (json.saveOkFlag) {
					if (json.IncompleteMsg) {
						API.showMessage(i18n.def.saveSuccess + "<br/>-------------------<br/>" + json.IncompleteMsg);
					} else {
						API.showMessage(i18n.def.saveSuccess);
					}
				}
			});
		}).end().find("#btnSend").click(function() {
			saveAction().done(function(json_saveAction) {
				if (json_saveAction.saveOkFlag) {
					sendBoss();
				}
			});
		}).end().find("#btnAccept").click(function() {
			var _id = "_div_btnAccept";
			var _form = _id + "_form";
			if ($("#" + _id).length == 0) {
				var dyna = [];
				dyna.push("<div id='" + _id + "' style='display:none;' >");
				dyna.push("<form id='" + _form + "'>");

				dyna.push("		<p><label><input type='radio' name='decisionExpr' value='1' class='required' />" + i18n.def['accept'] + "</label></p>");
				dyna.push("		<p><label><input type='radio' name='decisionExpr' value='2' class='required' />" + i18n.def['return'] + "</label></p>");

				dyna.push("</form>");

				dyna.push("</div>");

				$('body').append(dyna.join(""));
			}
			//clear data
			$("#" + _form).reset();

			$("#" + _id).thickbox({ // 使用選取的內容進行彈窗
				title: i18n.def["confirmApprove"],
				width: 380,
				height: 180,
				align: "center",
				valign: "bottom",
				modal: false,
				i18n: i18n.def,
				buttons: {
					"sure": function() {
						if (!$("#" + _form).valid()) {
							return;
						}
						var val = $("#" + _form).find("[name='decisionExpr']:checked").val();
						if (val == "1") {
							flowAction({ 'decisionExpr': '核定' });
						} else if (val == "2") {
							flowAction({ 'decisionExpr': '退回' });
						}
					},
					"cancel": function() {
						$.thickbox.close();
					}
				}
			});
		}).end().find("#btnReturnToCompiling").click(function() {
			API.confirmMessage(i18n.def.confirmReturn, function(result) {
				if (result) {
					flowAction({ 'decisionExpr': '退回' });
				}
			});
		}).end().find("#btnPrint").click(function() {
			if (initControl_lockDoc) {
				do_print();
			} else {
				saveAction({ 'allowIncomplete': 'Y' }).done(function(json) {
					if (json.saveOkFlag) {
						do_print();
					}
				});
			}
		});

		$("#printGrid").iGrid({
			handler: 'lms1015gridhandler',
			height: printGrid_height,
			action: 'queryPrint',
			postData: {
				formAction: "queryPrint",
				mainId: responseJSON.mainId
			},
			needPager: false,
			shrinkToFit: false,
			multiselect: true,
			colModel: [{ name: 'oid', hidden: true }
				, { name: 'mainId', hidden: true }
				, { name: 'type', hidden: true }
				, { colHeader: i18n.lms1015m01['print.rptName'], width: 150, name: 'desc1', sortable: false }
				, { colHeader: '&nbsp;', width: 300, name: 'desc2', sortable: false }
			]
		});
		var flowAction = function(opts) {
			return $.ajax({
				type: "POST",
				handler: _handler, action: "flowAction",
				data: $.extend({
					mainOid: $("#mainOid").val(),
					mainDocStatus: $("#mainDocStatus").val()
				}
					, (opts || {})
				),
				}).done(function(json) {
					API.triggerOpener();//gridview.reloadGrid 
					window.close();
			});
		}

		//呈主管覆核 選授信主管人數
		$("#numPerson").change(function() {
			var $newBossSpan = $("#newBossSpan");
			//清空原本的
			$newBossSpan.empty();
			var newdiv = "";
			var val = parseInt($(this).val(), 10);
			if (val > 1) {
				for (var i = 2, count = val + 1; i < count; i++) {
					newdiv += "<div>" + i18n.lms1015m01['c121m01e.no'] + i +
						i18n.lms1015m01['c121m01e.site'] +
						"&nbsp;" +
						i18n.lms1015m01['c121m01e.bossId'] +
						"&nbsp;&nbsp;&nbsp;<select id='boss" +
						i +
						"' name=boss" +
						i +
						" class='boss'/>&nbsp;" +
						"</div>"
				}
				$newBossSpan.append(newdiv);
				var copyOption = $("#mainBoss").html();
				$("[name^=boss]").html(copyOption);
			}
		});
	});
});

$.extend(window.tempSave,{
	handler: _handler, // handler 名稱
	action: "tempSave", // action Method
	beforeCheck:function(){ // return false or true
		if(responseJSON.page == "02"){
			var count=$("#c120m01agrid").jqGrid('getGridParam','records');
			if(count == 0){
				CommonAPI.showErrorMessage(i18n.lms1015m01('l120m01a.error24', {'colName': ''}));				
				return false;
			}
    	}
		return $("#tabForm").valid();
	},sendData:function(){ // 需上送之資料集合(Map<String,String>)
		var opts = {}
		if(responseJSON.page == "04"||responseJSON.page == "05"||responseJSON.page == "06"){
			opts[__bef_c120m01a_oid] = $('#c120_id_list').val();
		}
		return $.extend($("#tabForm").serializeData(), opts);
	}
});

function do_print(){	
	$("#printGrid").jqGrid("setGridParam", {
        postData: {
            formAction: "queryPrint",
            mainId: responseJSON.mainId
        },
        search: true
    }).trigger("reloadGrid");
	//==============
	var _id = "divPrint";
	$("#"+_id).thickbox({
        title: '', width: 520, height: (printGrid_height+150), align: "center", valign: "bottom",
        modal: false, i18n: i18n.def,
     buttons: {
         "print": function(){
        	 var printGrid = $("#printGrid");
        	 var rowId_arr = printGrid.getGridParam('selarrrow');
        	 var oid_arr = [];
        	 /**
         	 * 同時印多份: 
         	 * oid_arr.push(data.oid+"^"+data.mainId);
         	 * rptOid: oid_arr.join("|")
         	 */    
        	 for (var i = 0; i < rowId_arr.length; i++) {
     			var data = printGrid.getRowData(rowId_arr[i]);
     			
     			oid_arr.push(data.oid+"^"+data.type);
             }
        	 if (oid_arr.length == 0) {
                 CommonAPI.showMessage(i18n.def['grid.selrow']);
                 return
             }
        	 $.form.submit({
      	        url: "../../simple/FileProcessingService",
      	        target: "_blank",
      	        data: {
      	            'rptOid': oid_arr.join("|"),
      	            'fileDownloadName': "lms1015.pdf",
      	            serviceName: "lms1015rptservice"            
      	        }
      	     });	                    
         },
         "cancel": function(){
             $.thickbox.close();
         }
     }
    });
}

function saveAction(opts){
	var tabForm = $("#tabForm");
	debugger;
	if(tabForm.valid()){
		var optsPage = {};
		if(responseJSON.page == "04"||responseJSON.page == "05"||responseJSON.page == "06"){
			optsPage[__bef_c120m01a_oid] = $('#c120_id_list').val();
		}
		
		return $.ajax({
            type: "POST",
            handler: _handler,
            data:$.extend( {
            	formAction: "saveMain",
                page: responseJSON.page,
                mainOid: responseJSON.mainOid
                }, 
                tabForm.serializeData(),
                optsPage,
                ( opts||{} )
            ),                
            }).done(function(json){
            	tabForm.injectData(json);
            	
            	var varVer = json.varVer;	
        		if(varVer == "2.0"){ //日本模型2.0,不顯示評等調整頁簽。因儲存時不會reload，這邊要藏起來
        			$("#page06").hide();
        		}else{
        			$("#page06").show();
        		}
            	//更新 opener 的 Grid
            	debugger;
                CommonAPI.triggerOpener("gridview", "reloadGrid");
        });
	}else{
		return $.Deferred();
	}
}

function enable_c120_id_list(){
	$("#c120_id_list").prop("disabled", false);
}

function build_borrower_list(json){
	if(json.c120m01a_list){
		var dyna = []
		var size = json.c120m01a_list.key.length;
		if(size>1){
			dyna.push("<table border='0'>");
			dyna.push("<tr><td class='text-red rt'>"+i18n.lms1015m01['msg.006']+"</td></tr>");
			dyna.push("<tr><td>");	
		}
		dyna.push("<select id='c120_id_list' style='background-color:#C9E4CA'>");
		$.each(json.c120m01a_list.key, function(idx, jsonItem) {
			dyna.push("<option value='"+jsonItem.oid+"' data-idDup='"+(jsonItem.custId+jsonItem.dupNo)+"'>"+jsonItem.custId+"-"+jsonItem.dupNo+" "+jsonItem.custName+"</option>");
		});	
		dyna.push("</select>");		
		if(size>1){
			dyna.push("</td></tr>");
			dyna.push("</table>");
		}
		$("#borrower_list").html(dyna.join(""));
	}
}

function initAtPage456(json){
	build_borrower_list(json);
	//來自 build_borrower_list(...)
	if(true){
		if(json.c120m01a_oid_m){
			//default 顯示主借人的資料
			$("#c120_id_list").val( json.c120m01a_oid_m );//.trigger('change');
		}
		
		$("#c120_id_list").data(__bef_c120m01a_oid, $("#c120_id_list").val());
		
		//不用 trigger('change') 而用 loadC121M... 是為了配合 切換 ID時
		//要先save原資料，再load新資料
		loadC121M_items($("#c120_id_list").val());	
	}
		
	$("#c120_id_list").change(function(){
		var pre_oid = $(this).data(__bef_c120m01a_oid);
		var c120m01a_oid = $(this).val();
		$(this).data(__bef_c120m01a_oid, c120m01a_oid);
		
		saveC121M_items(pre_oid)
		.done(function(){
			if(true){
				loadC121M_items(c120m01a_oid);
		    }	
		})
		.fail(function(){
			//故意在 A 輸入升3等，於selectBox 選擇B
			$("#c120_id_list").val(pre_oid);
			$("#c120_id_list").data(__bef_c120m01a_oid, $("#c120_id_list").val());			
		});	    
	});
}
function saveC121M_items(pre_oid){
	if(!$("#buttonPanel").find("#btnSave").is("button")){
		ilog.debug("noNeedToSave[pre_oid]"+pre_oid);
		var my_dfd = $.Deferred();    	
    	my_dfd.resolve();  		
    	return my_dfd.promise();
		
	}else{
		ilog.debug("save[pre_oid]"+pre_oid);
		var opts = {};
		opts[__bef_c120m01a_oid] = pre_oid;
		
		return $.ajax({
	        type: "POST",
	        handler: _handler, action: "tempSave",
	        data:$.extend($("#tabForm").serializeData(), opts),                
	        }).done(function(json){
	    });	
	}
}
function loadC121M_items(c120m01a_oid){
	ilog.debug("loadC121M_items: "+c120m01a_oid);
	if(responseJSON.page=='04' || responseJSON.page=='05' || responseJSON.page=='06'){
		//若用 setValue 時，會把 selectBox 的目前 CustId 也一併清空
		//$("#tabForm").setValue();
		var cleanJSON = {};
		$.each($("#tabForm").serializeData(), function(colName, colVal){
			cleanJSON[colName] = '';
		});
		$("#tabForm").setValue(cleanJSON, false);
	}
	$.ajax({
        type: "POST",
        handler: _handler, action: "loadC121M_items",
        data:{
        	'c120m01a_oid': c120m01a_oid, 
        	'mainOid': responseJSON.mainOid,
        	page: responseJSON.page 
        },                
        }).done(function(json){            	
        	$("#tabForm").injectData(json);        	
        	if(responseJSON.page=='06'){
        		var debugMsg = "ajax:loadC121M_items:success";
        		var val = json.noAdj;
    			if(val=="1"){						
    				isS06_show(false, debugMsg);
    			}else if(val=="2"){
    				isS06_show(true, debugMsg);
    			}else{
    				isS06_show(true, debugMsg);
    			}
    			
    			var $frm = $("#tabForm");
    			if($frm.find("#attchFileOid2").val()){
    				$frm.find("#getOverrideFile").show();
    			}else{
    				$frm.find("#getOverrideFile").hide();
    			}
        	}
        	//即使在 readonly 狀態，也要可檢視不同身分的 custId
        	enable_c120_id_list();
    });		
}

function calc_C121_score(){
	$.ajax({
        type: "POST",
        handler: _handler, action: "calc_C121_score",
        data:{
        	'mainOid': responseJSON.mainOid,        	
        	page: responseJSON.page 
        },                
        }).done(function(json){            	
        	
    });		
}

function isS06_show(isShow, debugMsg){
	//ilog.debug("isS06_show("+isShow+")"+(debugMsg||''));
	if(isShow){
		$("tr.tr_adjustStatus12").show();
		
		if(true){
			/*
			 * 之前沒有加以下的程式
			 * [編製中]有正常運作
			 * 但[待覆核、已核准]有升等的case,未呈現[ ]淨資產[ ] 職業 [ ]其它 
			 */
			$('input[type=radio][name=adjustStatus]:checked').trigger('change');
			$('input[type=radio][name=adjustFlag]:checked').trigger('change');	
		}
		
	}else{
		$("tr.tr_adjustStatus12").hide();
	}
}


function alwaysConfirmAdjReason(cnt, obj){
	var my_dfd = $.Deferred();
	if(cnt=="0"){
		my_dfd.resolve();
	}else{	
		if(true){
			$("#adjustReasonAlwaysCfmMsg").html(obj.alwaysCfmStr);
		}
		$("#divAdjustReasonAlwaysCfmMsg").thickbox({
	        title: "", width: 550, height: 180,
	        align: "center", valign: "bottom", modal: false,
	        i18n: (obj || i18n.def),
	        buttons: {
	        	"alwaysCfmN": function(){
	                $.thickbox.close();
	                my_dfd.reject();
	            },
	            "alwaysCfmY": function(){
	            	//=============
	                $.thickbox.close();
	            	my_dfd.resolve();
	            }
	        }
	    });	
	}		
	return my_dfd.promise(); 
}
function procCfmMsg(obj){
	var my_dfd = $.Deferred();
	
	if((obj.cfmStr||"")==""){
		my_dfd.resolve();
	}else{		
		if(true){
			$("#adjustReasonCfmMsg").html(obj.cfmStr);
		}
		$("#divAdjustReasonCfmMsg").thickbox({
	        title: "", width: 600, height: 200,
            align: "center", valign: "bottom", modal: false,
            i18n: (obj || i18n.def),
            buttons: {
            	"cfmN": function(){
                    $.thickbox.close();
                    my_dfd.reject();
                },
                "cfmY": function(){
                	//=============
                    $.thickbox.close();
                	my_dfd.resolve();
                }
            }
	    });	
		
	}
	return my_dfd.promise(); 
}

/** 
 * 呈主管 -  編製中 
 **/
function sendBoss(){
    $("#numPerson").prop("disabled", false);
    $.ajax({
        handler: "lms1015m01formhandler",
        data: {
            formAction: "queryBoss"
        },
        }).done(function(json){
            $(".boss").setItems({
                item: json.bossList
            });
            CommonAPI.confirmMessage(i18n.lms1015m01['c121m01e.message01'], function(b){
                if (b) {
                    $("#selectBossForm").find("select").prop("disabled", false);
                    $("#selectBossBox").thickbox({ // 使用選取的內容進行彈窗
                        title: i18n.lms1015m01['c121m01e.message00'],
                        width: 500,
                        height: 300,
                        modal: true,
                        valign: "bottom",
                        align: "center",
                        readOnly: false,
                        i18n: i18n.def,
                        buttons: {
                            "sure": function(){
                                var $selectBossForm = $("#selectBossForm");
                                var accounting = $selectBossForm.find("select#accounting").val();
                                var selectBoss = $("select[name^=boss]", $selectBossForm).map(function(){
                                    return $(this).val();
                                }).toArray();
                                
                                var manager = $selectBossForm.find("select#sManager").val();
                                
                                //驗證主管是否都有選擇到
                                for (var id in selectBoss) {
                                    if (selectBoss[id] == "") {
                                        return CommonAPI.showErrorMessage(i18n.lms1015m01['c121m01e.error1'] + i18n.lms1015m01['c121m01e.bossId']);
                                    }
                                }
                                //驗證是否有重複的主管
                                if (checkArrayRepeat(selectBoss)) {
                                    return CommonAPI.showErrorMessage(i18n.lms1015m01['c121m01e.message02']);
                                }
                                //驗證單位授權主管是否有選擇到
                                if (!manager || manager == "") {
                                    return CommonAPI.showErrorMessage(i18n.lms1015m01['c121m01e.error1'] + i18n.lms1015m01['c121m01e.managerId']);
                                }
                                //建立簽章欄
                                $.ajax({
                                    type: "POST",
                                    handler: "lms1015m01formhandler",
                                    action: "saveC121m01e",
                                    data: {
                                        mainId: responseJSON.mainId,
                                        selectBoss: selectBoss,
                                        sManager: $("#sManager option:selected").val()
                                    },
                                    success: function(responseData){
                                    	flowAction({'decisionExpr':'呈主管'});
                                    }
                                });
                                $.thickbox.close();
                                
                            },
                            "cancel": function(){
                            	API.confirmMessage(i18n.def['flow.exit'], function(res){
                                    if (res) {
                                        $.thickbox.close();
                                    }
                                });
                            }
                        }
                    });
                }
            });
    });
}


function flowAction(opts){
	return $.ajax({
        type: "POST",
        handler: _handler, action: "flowAction",
        data:$.extend( {
        	mainOid: $("#mainOid").val(), 
        	mainDocStatus: $("#mainDocStatus").val() 
            }
            , ( opts||{} )
        ),                
        }).done(function(json){            	
        	API.triggerOpener();//gridview.reloadGrid 
        	window.close();            	
    });
}

//檢查陣列內容是否重複
function checkArrayRepeat(arrVal){
    var newArray = [];
    for (var i = arrVal.length; i--;) {
        var val = arrVal[i];
        if ($.inArray(val, newArray) == -1) {
            newArray.push(val);
        }
        else {
            return true;
        }
    }
    return false;
}
