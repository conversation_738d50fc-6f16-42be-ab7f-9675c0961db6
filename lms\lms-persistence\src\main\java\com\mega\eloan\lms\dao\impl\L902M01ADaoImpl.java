/* 
 * L902M01ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L902M01ADao;
import com.mega.eloan.lms.model.L902M01A;

/** 私募基金代碼主檔 **/
@Repository
public class L902M01ADaoImpl extends LMSJpaDao<L902M01A, String> implements
		L902M01ADao {

	@Override
	public L902M01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public L902M01A findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L902M01A> findByIndex01(String mainId) {
		ISearch search = createSearchTemplete();
		List<L902M01A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public L902M01A findByPeNo(String peNo) {

		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "peNo", peNo);
		return findUniqueOrNone(search);

	}
	
	@Override
	public List<L902M01A> findByPeNoList(String peNo) {

		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		search.addSearchModeParameters(SearchMode.EQUALS, "peNo", peNo);
		search.addOrderBy("peNo", false);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L902M01A> list = createQuery(search).getResultList();
		return list;

	}
	

	/**
	 * 取得所有私募基金資料 J-105-0228-001 Web e-Loan企金授信簽報書新增私募基金相關建檔與報表。
	 */
	@Override
	public List<L902M01A> findAllPeNo() {

		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		search.addOrderBy("peNo", false);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L902M01A> list = createQuery(search).getResultList();
		return list;
	}
	
	/**
	 * 取得所有私募基金最大號
	 * @return
	 */
	@Override
	public L902M01A findMaxPeNo() {

		ISearch search = createSearchTemplete();
		search.addOrderBy("peNo", true);
		return findUniqueOrNone(search);

	}
	
}