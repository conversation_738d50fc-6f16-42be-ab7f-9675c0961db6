var Action = {
    //引進為授權外時才要顯示總行經辦、總行覆核，非授權外請隱藏
    showMainApprIdTr: function(value){
        var $mainApprIdTr = $("#mainApprIdTr");
        if (value) {
            $mainApprIdTr.show();
        } else {
            $mainApprIdTr.hide();
        }
    }
}
initDfd.done(function(json){
    //引進為授權外時才要顯示總行經辦、總行覆核，非授權外請隱藏
    Action.showMainApprIdTr(json.mainApprId);
    //按鈕-選擇額度明細表
    $("#selectTable").click(function(){
        // 初始化
        $("[name=caseTable]:checked").removeAttr("checked");
        $("#openthetable").thickbox({
            title: i18n.lms1601m01["L160M01A.bt01"],// 選擇額度明細表
            width: 400,
            height: 100,
            modal: true,
            valign: "bottom",
            i18n: i18n.def,
            align: "center",
            buttons: {
                "sure": function(){
                    var checked = $("[name=caseTable]:checked").val();
                    
                    if (!checked) {
                    
                        // L160M01A.error2 請選擇
                        CommonAPI.showMessage(i18n.lms1601m01['L160M01A.error2']);
                    } else {
                        $.thickbox.close();
                        setCustId(checked);
                        //                        switch (checked) {
                        //                            case "1":
                        //                                setCustId(1);
                        //                                break;
                        //                            case "2":
                        //                                // 更新聯行額度明細表的grid
                        //                                $("#openthegrid2").jqGrid("setGridParam", {
                        //                                    postData: {
                        //                                        formAction: "queryL141m01a",
                        //                                        tabFormMainId: $("#tabFormMainId").val(),
                        //                                        lmtType: "1"
                        //                                    },
                        //                                    search: true
                        //                                }).trigger("reloadGrid");
                        //                                openthetable2();
                        //                                break;
                        //                        }//close switch					
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    });// close 選擇額度明細表
    // 自行簽報之額度明細表簽報書選擇Grid
    var $openthegrid = $("#openthegrid").iGrid({
        handler: inits.ghaddle,
        height: "250px",
        width: "100%",
        sortname: 'endDate|custId',
        sortorder: "desc|desc",
        rowNum: 10,
        autowidth: true,
        colModel: [{
            colHeader: i18n.lms1601m01['L160M01A.mainCust'],// 主要借款人
            name: 'custId',
            width: 150,
            sortable: true,
            align: "left"
        }, {
            colHeader: i18n.lms1601m01['L160M01A.caseNo'],// 案號
            name: 'caseNo',
            width: 120,
            sortable: true,
            align: "left"
        }, {
            colHeader: i18n.lms1601m01['L160M01A.caseDate'],// 簽案日期
            name: 'caseDate',
            width: 45,
            sortable: true,
            align: "center"
        }, {
            colHeader: i18n.lms1601m01['L160M01A.approveTime'],// 核准日期
            name: 'endDate',
            width: 45,
            sortable: true,
            align: "center",
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d',
                newformat: 'Y-m-d'
            }
        }, {
            colHeader: i18n.lms1601m01['L160M01A.apprId'],// 經辦
            name: 'updater',
            width: 60,
            sortable: true,
            align: "updater"
        }, {
            name: 'oid',
            hidden: true
        }, {
            name: 'mainId',
            hidden: true
        }, {
            name: 'authLvl',
            hidden: true
        }, {
            name: 'docKind',
            hidden: true
        }]
    });
    
    // 聯貸案簽報行傳來之額度明細表簽報書選擇Grid
    // TODO 聯行額度明細表
    var $openthegrid2 = $("#openthegrid2").iGrid({
        handler: inits.ghaddle,
        height: "230px",
        width: "100%",
        sortname: 'caseBrId',
        //		postData : {
        //			formAction : "queryL141m01a"
        //		},
        autowidth: true,
        colModel: [{
            colHeader: i18n.lms1601m01['L160M01A.ownbranch'],// 聯行
            name: 'caseBrId',
            width: 100,
            sortable: true,
            align: "center"
        }, {
            colHeader: i18n.lms1601m01['L160M01A.mainCust'],// 主要借款人
            name: 'custName',
            width: 150,
            sortable: true,
            align: "left"
        }, {
            colHeader: i18n.lms1601m01['L160M01A.caseNo'],// 案號
            name: 'caseNo',
            width: 120,
            sortable: true,
            align: "left"
        }, {
            colHeader: i18n.lms1601m01['L160M01A.caseDate'],// 簽案日期
            name: 'caseDate',
            width: 90,
            sortable: true,
            align: "center"
        }, {
            colHeader: i18n.lms1601m01['L160M01A.apprId'],// 經辦
            name: 'coAppraiser',
            width: 90,
            sortable: true,
            align: "center"
        }, {
            name: 'srcMainId',
            hidden: true
        }],
        ondblClickRow: function(rowid){
        
        }
    });
    // 額度明細表選擇Grid
    var openCaseGrid = $("#openCaseGrid").iGrid({
        handler: inits.ghaddle,
		needPager: false,
        height: "230px",
        width: "100%",
        sortname: 'useCntrNo',
		sortorder: 'asc',  
        multiselect: true,
        hideMultiselect: false,
        postData: {
            mainId: "1",
            itemType: "1"
        },
        autowidth: true,
		shrinkToFit: false,
        colModel: [{
            colHeader: i18n.lms1601m01['L160M01A.custId'],// 統一編號
            name: 'custId',
            width: 150,
            sortable: true,
            align: "left"
        }, {
            colHeader: i18n.lms1601m01['L160M01A.custName'],// 借款人名稱
            name: 'custName',
            width: 90,
            sortable: true,
            align: "center"
        }, {
            colHeader: i18n.lms1601m01['L160M01A.cntrNum'],// 額度序號
            name: 'useCntrNo',
            width: 90,
            sortable: true,
            align: "center"
        }, {
            colHeader: i18n.lms1601m01["L160M01A.commSno"],//"共用額度序號",
            name: 'commSno',
            width: 80,
            sortable: true
        }, {
            colHeader: "&nbsp;",
            name: 'currentApplyCurr',
            width: 30,
            sortable: true,
            align: "center"
        }, {
            colHeader: i18n.lms1601m01["L160M01A.moneyAmt"],//現請額度,
            name: 'currentApplyAmt',
            width: 100,
            sortable: true,
            align: "right",
            formatter: 'currency',
            formatoptions: {
                thousandsSeparator: ",",
				removeTrailingZero: true,
                decimalPlaces: 2//小數點到第幾位
            }
        }, {
            colHeader: i18n.lms1601m01["L160M01A.useAMT"],//攤貸額度,
            name: 'shareAmt',
            width: 100,
            sortable: true,
            align: "right",
            formatter: 'currency',
            formatoptions: {
                thousandsSeparator: ",",
				removeTrailingZero: true,
				defaultValue: '0',
                decimalPlaces: 2//小數點到第幾位
            }
        }, {
            colHeader: i18n.lms1601m01["L160M01A.type"],//"性質"
            name: 'proPerty',
            width: 70,
            sortable: true,
            align: "center",
            formatter: proPertyFormatter
        }, {
            colHeader: i18n.lms1601m01['L160M01A.branchName'],// 分行別
            name: 'useBrId',
            width: 60,
            sortable: true,
            align: "center"
        }, {
            name: 'oid',
            hidden: true
        }, {
            name: 'mainId',
            hidden: true
        }]
    });
    
    
    /**  登錄性質的格式化  */
    function proPertyFormatter(cellvalue, otions, rowObject){
        var itemName = '';
        var list = cellvalue.split("|");
        if (cellvalue) {
            itemName = i18n.lms1601m01["L140M01a.type" + list[0]];
            if (cellvalue.length > 1) {
                for (var i = 1; i < list.length; i++) {
                    var itemone = i18n.lms1601m01["L140M01a.type" + list[i]];
                    itemName = itemName + "、" + itemone;
                }//close for
            }//close if
        }
        return itemName;
    }
    
    /**
     * 輸入借款人Id
     * @param {Object} type 引進種類 1 簽報書 2 聯行額度明細表
     */
    function setCustId(type){
        var $form = $("#filterForm");
        $form.reset();
        $("#filterBox").thickbox({
            title: i18n.def["query"],
            width: 300,
            height: 190,
            modal: true,
            align: "center",
            valign: "bottom",
            readOnly: _openerLockDoc == "1",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if ($form.valid()) {
                        $.thickbox.close();
                        var custId = $form.find("#filter_custId").val();
                        var dupNo = $form.find("#filter_dupNo").val()
                        if (type == "1") {
                            $("#openthegrid").jqGrid("setGridParam", {
                                postData: {
                                    formAction: "queryL120m01a",
                                    custId: custId,
                                    dupNo: dupNo
                                },
                                search: true
                            }).trigger("reloadGrid");
                            
                            openthetable1();
                        } else {
                            $("#openthegrid2").jqGrid("setGridParam", {
                                postData: {
                                    formAction: "queryL141m01a",
                                    tabFormMainId: $("#tabFormMainId").val(),
                                    custId: custId,
                                    dupNo: dupNo,
                                    lmtType: "1"
                                },
                                search: true
                            }).trigger("reloadGrid");
                            openthetable2();
                        }
                        
                    }
                    
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
    
    // 自行簽報之額度明細表簽報書選擇
    function openthetable1(){
        $("#openthetable1").thickbox({
        
            // L160M01A.selfCntrCase=自行簽報之額度明細表簽報書選擇
            title: i18n.lms1601m01["L160M01A.selfCntrCase"],
            width: 800,
            height: 410,
            valign: "bottom",
            align: "center",
            modal: true,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var id = $openthegrid.getGridParam('selrow');
                    
                    if (!id) {
                    
                        // action_004=請先選擇需「調閱」之資料列
                        return CommonAPI.showMessage(i18n.def["action_004"]);
                    }
                    $.thickbox.close();
                    var selectMainId = $openthegrid.getRowData(id).mainId;
                    var authLvl = $openthegrid.getRowData(id).authLvl;
                    var docKind = $openthegrid.getRowData(id).docKind;
                    // L160M01A.message1=該簽書項下額度此次是否全部動用?若全部動用請按【確定】，否則請按，【取消】繼續選擇此次動用之額度明細表。
                    CommonAPI.confirmMessage(i18n.lms1601m01["L160M01A.message1"], function(b){
                        if (b) {
                            // 全部動用
                            $.ajax({
                                handler: inits.fhandle,
                                data: {
                                    formAction: "queryL140m01a",
                                    authLvl: authLvl || "",
                                    docKind: docKind || "",
                                    caseMainId: selectMainId,
                                    type: "all",
                                    dataSrc: $("[name=caseTable]:checked").val()
                                },
                                success: function(obj){
                                    CommonAPI.triggerOpener("gridview", "reloadGrid");
                                    responseJSON.oid = obj.oid;
                                    responseJSON.mainOid = obj.mainOid;
                                    responseJSON.mainId = obj.mainId;
                                    $("body").injectData(obj);
                                    // L160M01A.message3=簽報書項下額度明細表全部動用
                                    $("#cntrNo").html(i18n.lms1601m01["L160M01A.message3"]);
                                    //引進為授權外時才要顯示總行經辦、總行覆核，非授權外請隱藏
                                    Action.showMainApprIdTr(obj.mainApprId);
									
                                    //J-109-0KKK_05097_B1001 簡化青年創業及啟動金貸款簽報書簽案流程
									//用來判斷是否出現RPA查詢
//									if (obj.isSmallBuss == "Y") {
//				                        $("#tabs_6").show();
//				                    }
//				                    else {
//				                        $("#tabs_6").hide();
//				                    }
									
									obj.show_tabs_6 == "Y"  ? $("#tabs_6").show() : $("#tabs_6").hide();
									
                                }
                            });//close ajax
                        } else {
                            $("#openCaseGrid").jqGrid("setGridParam", {
                                postData: {
                                    formAction: "queryL140m01a",
                                    mainId: selectMainId,
                                    authLvl: authLvl || "",
                                    docKind: docKind || ""
                                },
                                search: true
                            }).trigger("reloadGrid");
                            
                            // 打開額度明細表thinckbox
                            openCaseBox(selectMainId);
                        }
                    });
                    
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
    
    // 聯貸案簽報行傳來之額度明細表簽報書選擇
    function openthetable2(){
        $("#openthetable2").thickbox({
            // L160M01A.contentCntrCase=聯貸案簽報行傳來之額度明細表
            title: i18n.lms1601m01["L160M01A.contentCntrCase"],
            modal: true,
            width: 800,
            height: 380,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var id = $openthegrid2.getGridParam('selrow');
                    
                    if (!id) {
                    
                        // action_004=請先選擇需「調閱」之資料列
                        return CommonAPI.showMessage(i18n.def["action_004"]);
                    }
                    $.thickbox.close();
                    var selectMainId = $openthegrid2.getRowData(id).srcMainId;
                    
                    openCaseGrid.jqGrid("setGridParam", {
                        postData: {
                            formAction: "queryL140m01a",
                            mainId: selectMainId,
                            itemType: "1",
                            dataSrc: $("[name=caseTable]:checked").val()
                        },
                        search: true
                    }).trigger("reloadGrid");
                    
                    openCaseBox(selectMainId);
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
    
    // 額度明細表選擇
    function openCaseBox(mainId){
        $("#openCaseBox").thickbox({
        
            // L160M01A.title09=額度明細表
            title: i18n.lms1601m01["L160M01A.title09"],
            width: 800,
            height: 400,
            modal: true,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var ids = openCaseGrid.getGridParam('selarrrow'), dataMainId = [], datacntrNo = [];
                    
                    if (ids == "") {
                    
                        // action_004=請先選擇需「調閱」之資料列
                        return CommonAPI.showMessage(i18n.def["action_004"]);
                    }
                    
                    for (var i in ids) {
                        dataMainId.push(openCaseGrid.getRowData(ids[i]).mainId);
                        datacntrNo.push(openCaseGrid.getRowData(ids[i]).useCntrNo);
                    }
                    
                    
                    
                    $.ajax({
                        handler: inits.fhandle,
                        data: {
                            formAction: "queryL140m01a",
                            type: "select",
                            caseMainId: mainId,
                            selectMainId: dataMainId,
                            selectCntrNo: datacntrNo,
                            dataSrc: $("[name=caseTable]:checked").val()
                        },
                        success: function(obj){
                            $("body").injectData(obj);
                            //將額度序號寫到畫面上
                            $("#cntrNo").html(datacntrNo.join());
                            responseJSON.oid = obj.oid;
                            responseJSON.mainOid = obj.mainOid;
                            responseJSON.mainId = obj.mainId;
                            CommonAPI.triggerOpener("gridview", "reloadGrid");
							
                            //J-109-0KKK_05097_B1001 簡化青年創業及啟動金貸款簽報書簽案流程
                            //用來判斷是否出現RPA查詢
//							if (obj.isSmallBuss == "Y") {
//		                        $("#tabs_6").show();
//		                    }
//		                    else {
//		                        $("#tabs_6").hide();
//		                    }
                            obj.show_tabs_6 == "Y"  ? $("#tabs_6").show() : $("#tabs_6").hide();
                            
							
                        }
                    });//close ajax
                    $.thickbox.close();
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
    
});
