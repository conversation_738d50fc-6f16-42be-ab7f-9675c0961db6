$(function(){
	
	var gridShowFinish = false;
	if( $("#gridShowFinish") && $("#gridShowFinish").length > 0  && $("#gridShowFinish").val()==="Y"){
		gridShowFinish = true;
	}
	
	var my_colModel = [];	
	my_colModel.push({ name: 'oid', hidden: true });
	my_colModel.push({ name: 'mainId', hidden: true });
	if(gridShowFinish){
		my_colModel.push({
			colHeader: i18n.lms2420v01['C242M01A.approveTime'],//已完成日期
			align: "left",width: 120, sortable: true,
            name: 'approveTime'
	    });
	}
	
	my_colModel.push({
        colHeader: i18n.lms2420v01["C242M01A.schTime"],//預約產生日期
        align: "left",width: 120, sortable: true,
        formatter: 'click',
        onclick: BOM,
        name: 'schTime'        	
    });
	my_colModel.push({
        colHeader: i18n.lms2420v01["C241M01a.exeBrNo"],//預約分行代號
        align: "left",width: 220, sortable: false,
        name: 'exeBrNo'
    });
	my_colModel.push({
        colHeader: i18n.lms2420v01["C242M01A.creator"], //建立人員
        align: "left",width: 100, sortable: true,
        name: 'creator'
    });
	my_colModel.push({
		colHeader: i18n.lms2420v01["C242M01A.updater"], //異動人員
        align: "left",width: 100, sortable: true,
        name: 'updater'
    });
	//---
    var grid = $("#gridview").iGrid({
        rownumbers: true,
        handler: 'lms2420gridhandler',
        height: 350, // 設定高度
        sortname: 'schTime', // 預設排序
        sortorder: "desc",
        shrinkToFit: false,
        rowNum: 15,
        postData: {
            formAction: "queryMain",
            docStatus: viewstatus
        },
        colModel: my_colModel,
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = $("#gridview").getRowData(rowid);
            BOM(null, null, data);
        }
    
    });
    
    
    $("#buttonPanel").find("#btnFilter").click(function(){
    	FilterAction.openBox();
    }).end().find("#btnAdd").click(function(){
    	$.ajax({
            handler: "lms2420m01formhandler",
            type: "POST",
            dataType: "json",
            data: {
                formAction: "newC242M01A"
            }}).done(function(obj_param){
            	$.form.submit({
                    url: '../crs/lms2420m01/01',
                    data: obj_param,
                    target: "_blank"
                });
            })
    }).end().find("#btnDelete").click(function(){
    	var rows = $("#gridview").getGridParam('selrow');
        var list = "";
        if (rows != 'undefined' && rows != null && rows != 0) {
            var data = $("#gridview").getRowData(rows);
            list = data.oid;
        }
        if (list == "") {
            CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
            return;
        }
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                $.ajax({
                    handler: "lms2420m01formhandler",
                    type: "POST",
                    dataType: "json",
                    data: {
                        formAction: "deleteMark",
                        list: list
                    }}).done(function(obj){
                        $("#gridview").trigger("reloadGrid");
                    })
            }
        });	
    });    
});

// ======================================================================================================
function BOM(cellvalue, options, rowObject){ // 連結XXXXM01.html視窗
	var postData = {
		'mainOid': rowObject.oid, 
		'mainId': rowObject.mainId,
		'mainDocStatus': viewstatus
	}
	$.form.submit({ url:'../crs/lms2420m01/01', data:postData, target:rowObject.oid});
}

//=====================================================================================

