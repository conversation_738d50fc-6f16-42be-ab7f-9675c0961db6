<html xmlns="http://www.w3.org/1999/xhtml"  xmlns:th="http://www.thymeleaf.org">
<body>
	<th:block th:fragment="panelFragmentBody">
		<form id="L120S01gForm_1">
                <div class="required max" maxlength="1" id="finKind" name="finKind" style="display: none;">
                    1
                </div>
                <div class="required max" maxlength="1" id="dataType" name="dataType" style="display: none;">
                    1
                </div>
                <div class="text-red">
                    <th:block th:utext="#{'l120s01a.other15'}">說明...</th:block>
                </div>
                <label>
                    <input class="cesCheck" id="rcdFlag" name="rcdFlag" type="checkbox" value="Y" onClick="showHide(this,'.hideThis');checkUncheck(this,'.cesCheck');" checked/><b><th:block th:text="#{'l120s01e.checkbox14'}">登錄營運概況、財務狀況、存放款及外匯往來情形</th:block></b>
                </label>
                <br/>
                <div class="hideThis" id="buttonces" style="display: none;">
                    <button type="button" onclick="setValue();">
                        <span class="text-only"><th:block th:text="#{'l120s01e.btn1'}">引進徵信相關資料</th:block></span>
                    </button>
                    <button type="button" onclick="editPanel2();">
                        <span class="text-only"><th:block th:text="#{'l120s01e.btn2'}">內容修正</th:block></span>
                    </button>
                </div>
                <div class="hideThis">
                    <label>
                        <input class="cesCheck" type="checkbox" id="runFlag" name="runFlag" value="Y" onClick="showHide(this,'tabs-2_show');" checked/><b><th:block th:text="#{'l120s01e.checkbox15'}">１、營收獲利情形</th:block>：</b>
                    </label>
                    <b><span class="ps1 text-red"><th:block th:text="#{'l120s01e.other1'}">(※若不選取此章節則資料行數會併入分析與評估中)</th:block></span></b>
                </div>
                <div class="hideThis" id="tabs-2_show">
                    <table class="tb2" width="100%" border="1">
                        <tr class="hd1" colspan="5">
                            <th:block th:text="#{'l120s01e.other2a'}">(金額單位：</th:block><select class="canEdit1" id="runCurr" name="runCurr" combokey="Common_Currcy" combotype="1" space="true" disabled="true" ></select><select class="canEdit1" id="runUnit" name="runUnit" combokey="lms1205s01_Unit" combotype="2" space="true" disabled="true" /><th:block th:text="#{'l120s01e.other2b'}">、%)</th:block>
                        </tr>
                        <tr class="hd1">
                            <td width="20%" align="center">
                                <th:block th:text="#{'l120s01e.other3'}">項目＼年度</th:block>
                            </td>
                            <td width="20%" align="center">
                                <span class="field canEdit1" id="finYear_D" name="finYear_D"></span>
                            </td>
                            <td width="20%" align="center">
                                <span class="field canEdit1" id="finYear_C" name="finYear_C"></span>
                            </td>
                            <td width="20%" align="center">
                                <span class="field canEdit1" id="finYear_B" name="finYear_B"></span>
                            </td>
                            <td width="20%" align="center">
                                <span class="field canEdit1" id="finYear_A" name="finYear_A"></span>
                            </td>
                        </tr>
                        <tr>
                            <td class="hd1" style="text-align: center">
                                <span name="finAmtName1" id="finAmtName1" class="ct canEdit1"></span>
                                <!--<th:block th:text="#{'l120s01e.other4'}">營收淨額</th:block>-->
                                <br/>
                                <span name="finRatioName1" id="finRatioName1" class="ct canEdit1"></span>
                                <!--<th:block th:text="#{'l120s01e.other5'}">(成長率%)</th:block>-->
                            </td>
                            <td align="right">
                                <span class="field numeric canEdit1" id="finAmtD1"></span>
                                <input type="hidden" name="finAmtCodeD1" id="finAmtCodeD1" class="canEdit1" />
                                <br/>
                                <span class="field canEdit1" id="finRatioD1"></span>
                                <input type="hidden" name="finRatioCodeD1" id="finRatioCodeD1" class="canEdit1" />
                            </td>
                            <td align="right">
                                <span class="field numeric canEdit1" id="finAmtC1"></span>
                                <input type="hidden" name="finAmtCodeC1" id="finAmtCodeC1" class="canEdit1" />
                                <br/>
                                <span class="field canEdit1" id="finRatioC1"></span>
                                <input type="hidden" name="finRatioCodeC1" id="finRatioCodeC1" class="canEdit1" />
                            </td>
                            <td align="right">
                                <span class="field numeric canEdit1" id="finAmtB1"></span>
                                <input type="hidden" name="finAmtCodeB1" id="finAmtCodeB1" class="canEdit1" />
                                <br/>
                                <span class="field canEdit1" id="finRatioB1"></span>
                                <input type="hidden" name="finRatioCodeB1" id="finRatioCodeB1" class="canEdit1" />
                            </td>
                            <td align="right">
                                <span class="field numeric canEdit1" id="finAmtA1"></span>
                                <input type="hidden" name="finAmtCodeA1" id="finAmtCodeA1" class="canEdit1" />
                                <br/>
                                <span class="field canEdit1" id="finRatioA1"></span>
                                <input type="hidden" name="finRatioCodeA1" id="finRatioCodeA1" class="canEdit1" />
                            </td>
                        </tr>
                        <tr>
                            <td class="hd1" style="text-align: center">
                                <span name="finAmtName2" id="finAmtName2" class="ct canEdit1"></span>
                                <!--<th:block th:text="#{'l120s01e.other6'}">營收利益</th:block>-->
                                <br/>
                                <span name="finRatioName2" id="finRatioName2" class="ct canEdit1"></span>
                                <!--<th:block th:text="#{'l120s01e.other7'}">(率％)</th:block>-->
                            </td>
                            <td align="right">
                                <span class="field numeric canEdit1" id="finAmtD2"></span>
                                <input type="hidden" name="finAmtCodeD2" id="finAmtCodeD2" class="canEdit1" />
                                <br/>
                                <span class="field canEdit1" id="finRatioD2"></span>
                                <input type="hidden" name="finRatioCodeD2" id="finRatioCodeD2" class="canEdit1" />
                            </td>
                            <td align="right">
                                <span class="field numeric canEdit1" id="finAmtC2"></span>
                                <input type="hidden" name="finAmtCodeC2" id="finAmtCodeC2" class="canEdit1" />
                                <br/>
                                <span class="field canEdit1" id="finRatioC2"></span>
                                <input type="hidden" name="finRatioCodeC2" id="finRatioCodeC2" class="canEdit1" />
                            </td>
                            <td align="right">
                                <span class="field numeric canEdit1" id="finAmtB2"></span>
                                <input type="hidden" name="finAmtCodeB2" id="finAmtCodeB2" class="canEdit1" />
                                <br/>
                                <span class="field canEdit1" id="finRatioB2"></span>
                                <input type="hidden" name="finRatioCodeB2" id="finRatioCodeB2" class="canEdit1" />
                            </td>
                            <td align="right">
                                <span class="field numeric canEdit1" id="finAmtA2"></span>
                                <input type="hidden" name="finAmtCodeA2" id="finAmtCodeA2" class="canEdit1" />
                                <br/>
                                <span class="field canEdit1" id="finRatioA2"></span>
                                <input type="hidden" name="finRatioCodeA2" id="finRatioCodeA2" class="canEdit1" />
                            </td>
                        </tr>
                        <tr>
                            <td class="hd1" style="text-align: center">
                                <span name="finAmtName3" id="finAmtName3" class="ct canEdit1"></span>
                                <!--<th:block th:text="#{'l120s01e.other8'}">財務費用</th:block>-->
                                <br/>
                                <span name="finRatioName3" id="finRatioName3" class="ct canEdit1"></span>
                                <!--<th:block th:text="#{'l120s01e.other7'}">(率％)</th:block>-->
                            </td>
                            <td align="right">
                                <span class="field numeric canEdit1" id="finAmtD3"></span>
                                <input type="hidden" name="finAmtCodeD3" id="finAmtCodeD3" class="canEdit1" />
                                <br/>
                                <span class="field canEdit1" id="finRatioD3"></span>
                                <input type="hidden" name="finRatioCodeD3" id="finRatioCodeD3" class="canEdit1" />
                            </td>
                            <td align="right">
                                <span class="field numeric canEdit1" id="finAmtC3"></span>
                                <input type="hidden" name="finAmtCodeC3" id="finAmtCodeC3" class="canEdit1"/>
                                <br/>
                                <span class="field canEdit1" id="finRatioC3"></span>
                                <input type="hidden" name="finRatioCodeC3" id="finRatioCodeC3" class="canEdit1"/>
                            </td>
                            <td align="right">
                                <span class="field numeric canEdit1" id="finAmtB3"></span>
                                <input type="hidden" name="finAmtCodeB3" id="finAmtCodeB3" class="canEdit1" />
                                <br/>
                                <span class="field canEdit1" id="finRatioB3"></span>
                                <input type="hidden" name="finRatioCodeB3" id="finRatioCodeB3" class="canEdit1"/>
                            </td>
                            <td align="right">
                                <span class="field numeric canEdit1" id="finAmtA3"></span>
                                <input type="hidden" name="finAmtCodeA3" id="finAmtCodeA3" class="canEdit1" />
                                <br/>
                                <span class="field canEdit1" id="finRatioA3"></span>
                                <input type="hidden" name="finRatioCodeA3" id="finRatioCodeA3" class="canEdit1" />
                            </td>
                        </tr>
                        <tr>
                            <td class="hd1" style="text-align: center">
                                <span name="finAmtName4" id="finAmtName4" class="ct canEdit1"></span>
                                <!--<th:block th:text="#{'l120s01e.other9'}">稅前淨利</th:block>-->
                                <br/>
                                <span name="finRatioName4" id="finRatioName4" class="ct canEdit1"></span>
                                <!--<th:block th:text="#{'l120s01e.other7'}">(率％)</th:block>-->
                            </td>
                            <td align="right">
                                <span class="field numeric canEdit1" id="finAmtD4"></span>
                                <input type="hidden" name="finAmtCodeD4" id="finAmtCodeD4" class="canEdit1" />
                                <br/>
                                <span class="field canEdit1" id="finRatioD4"></span>
                                <input type="hidden" name="finRatioCodeD4" id="finRatioCodeD4" class="canEdit1" />
                            </td>
                            <td align="right">
                                <span class="field numeric canEdit1" id="finAmtC4"></span>
                                <input type="hidden" name="finAmtCodeC4" id="finAmtCodeC4" class="canEdit1" />
                                <br/>
                                <span class="field canEdit1" id="finRatioC4"></span>
                                <input type="hidden" name="finRatioCodeC4" id="finRatioCodeC4" class="canEdit1" />
                            </td>
                            <td align="right">
                                <span class="field numeric canEdit1" id="finAmtB4"></span>
                                <input type="hidden" name="finAmtCodeB4" id="finAmtCodeB4" class="canEdit1" />
                                <br/>
                                <span class="field canEdit1" id="finRatioB4"></span>
                                <input type="hidden" name="finRatioCodeB4" id="finRatioCodeB4" class="canEdit1" />
                            </td>
                            <td align="right">
                                <span class="field numeric canEdit1" id="finAmtA4"></span>
                                <input type="hidden" name="finAmtCodeA4" id="finAmtCodeA4" class="canEdit1" />
                                <br/>
                                <span class="field canEdit1" id="finRatioA4"></span>
                                <input type="hidden" name="finRatioCodeA4" id="finRatioCodeA4" class="canEdit1" />
                            </td>
                        </tr>
                    </table>
                </div>
            </form>
			<div id="getCes" style="display: none">
                <fieldset>
                    <legend>
                        <th:block th:text="#{'l120s01a.subtitle2'}">資信簡表</th:block>
                    </legend>
                    <div id="cesGrid1" width="100%" style="margin-left: 10px; margin-right: 10px">
                    </div>
                </fieldset>
                <fieldset>
                    <legend>
                        <th:block th:text="#{'l120s01a.subtitle3'}">徵信報告</th:block>
                    </legend>
                    <div id="cesGrid2" width="100%" style="margin-left: 10px; margin-right: 10px">
                    </div>
                </fieldset>
            </div>
            <div id="setRatioValue" style="display: none">
                <table id="setRatioValueTable" border="1" cellpadding="0" cellspacing="0">
                </table>
            </div>
			
            <div id="setvalue" style="display: none">
                <table border="1" cellpadding="0" cellspacing="0">
                    <tr>
                        <td>
                            <label>
                                <input type="checkbox" id="finItem21" name="set" value="_21" /><th:block th:text="#{'l120s01e.checkbox1'}">流動比率</th:block>
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                                <input type="checkbox" id="finItem22" name="set" value="_22" /><th:block th:text="#{'l120s01e.checkbox2'}">速動比率</th:block>
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                                <input type="checkbox" id="finItem23" name="set" value="_23" /><th:block th:text="#{'l120s01e.checkbox3'}">負債比率</th:block>
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                                <input type="checkbox" id="finItem24" name="set" value="_24" /><th:block th:text="#{'l120s01e.checkbox4'}">扣除股東往來之負債比率</th:block>
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                                <input type="checkbox" id="finItem25" name="set" value="_25" /><th:block th:text="#{'l120s01e.checkbox5'}">現金流量比率</th:block>
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                                <input type="checkbox" id="finItem26" name="set" value="_26" /><th:block th:text="#{'l120s01e.checkbox6'}">固定長期適合率</th:block>
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                                <input type="checkbox" id="finItem27" name="set" value="_27" /><th:block th:text="#{'l120s01e.checkbox7'}">存貨週轉率(次)</th:block>
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                                <input type="checkbox" id="finItem28" name="set" value="_28" /><th:block th:text="#{'l120s01e.checkbox18'}">存貨週轉率(日)</th:block>
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                                <input type="checkbox" id="finItem29" name="set" value="_29" /><th:block th:text="#{'l120s01e.checkbox8'}">應收款項週轉率(次)</th:block>
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                                <input type="checkbox" id="finItem2a" name="set" value="_2a" /><th:block th:text="#{'l120s01e.checkbox19'}">應收款項週轉率(日)</th:block>
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                                <input type="checkbox" id="finItem2b" name="set" value="_2b" /><th:block th:text="#{'l120s01e.checkbox9'}">應付款項週轉率(次)</th:block>
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                                <input type="checkbox" id="finItem2c" name="set" value="_2c" /><th:block th:text="#{'l120s01e.checkbox20'}">應付款項週轉率(日)</th:block>
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                                <input type="checkbox" id="finItem2d" name="set" value="_2d" /><th:block th:text="#{'l120s01e.checkbox10'}">利息保障倍數</th:block>
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                                <input type="checkbox" id="finItem2e" name="set" value="_2e" /><th:block th:text="#{'l120s01e.checkbox11'}">總資產週轉率(次)</th:block>
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                                <input type="checkbox" id="finItem2f" name="set" value="_2f" /><th:block th:text="#{'l120s01e.checkbox17'}">總資產週轉率(日)</th:block>
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                                <input type="checkbox" id="finItem2g" name="set" value="_2g" /><th:block th:text="#{'l120s01e.checkbox12'}">逾放比率</th:block>
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                                <input type="checkbox" id="finItem2h" name="set" value="_2h" /><th:block th:text="#{'l120s01e.checkbox13'}">呆帳覆蓋率</th:block>
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                                <input type="checkbox" id="finItem2i" name="set" value="_2i" /><th:block th:text="#{'l120s01e.checkbox21'}">固定資產週轉率(次)</th:block>
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                                <input type="checkbox" id="finItem2j" name="set" value="_2j" /><th:block th:text="#{'l120s01e.checkbox22'}">固定資產週轉率(日)</th:block>
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                                <input type="checkbox" id="finItem2k" name="set" value="_2k" /><th:block th:text="#{'l120s01e.checkbox23'}">固定資產對淨值比率</th:block>
                            </label>
                        </td>
                    </tr>
                    <!--建霖說先不秀，這屬於資信簡表的營運情形
                    <tr>
                    <td>
                    <label><input type="checkbox" id="finItem2l" name="set" value="_2l" /><th:block th:text="#{'l120s01e.checkbox24'}">營業毛利率</th:block></label>
                    </td>
                    </tr>
                    -->
                    <tr>
                        <td>
                            <label>
                                <input type="checkbox" id="finItem2m" name="set" value="_2m" /><th:block th:text="#{'l120s01e.checkbox25'}">淨值獲利率</th:block>
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                                <input type="checkbox" id="finItem2n" name="set" value="_2n" /><th:block th:text="#{'l120s01e.checkbox26'}">資產獲利率</th:block>
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                                <input type="checkbox" id="finItem2o" name="set" value="_2o" /><th:block th:text="#{'l120s01e.checkbox27'}">營業利益率變動情形</th:block>
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                                <input type="checkbox" id="finItem2p" name="set" value="_2p" /><th:block th:text="#{'l120s01e.checkbox28'}">存放比率</th:block>
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                                <input type="checkbox" id="finItem2q" name="set" value="_2q" /><th:block th:text="#{'l120s01e.checkbox29'}">放款資產</th:block>
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                                <input type="checkbox" id="finItem2r" name="set" value="_2r" /><th:block th:text="#{'l120s01e.checkbox30'}">提存比率</th:block>
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                                <input type="checkbox" id="finItem2s" name="set" value="_2s" /><th:block th:text="#{'l120s01e.checkbox31'}">資本適足率</th:block>
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                                <input type="checkbox" id="finItem2t" name="set" value="_2t" /><th:block th:text="#{'l120s01e.checkbox32'}">第一類資本適足率</th:block>
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                                <input type="checkbox" id="finItem2u" name="set" value="_2u" /><th:block th:text="#{'l120s01e.checkbox33'}">第二類資本適足率</th:block>
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                                <input type="checkbox" id="finItem2v" name="set" value="_2v" /><th:block th:text="#{'l120s01e.checkbox34'}">資產報酬率</th:block>
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                                <input type="checkbox" id="finItem2w" name="set" value="_2w" /><th:block th:text="#{'l120s01e.checkbox35'}">股東權益報酬率</th:block>
                            </label>
                        </td>
                    </tr>
                </table>
            </div>
			<div id="editPanel2" style="display: none">
                <form id="editFPanel2">
                    <table class="tb2" width="100%" border="1">
                        <tr class="hd1" colspan="5">
                            <th:block th:text="#{'l120s01e.other2a'}">(金額單位：</th:block><select id="_runCurr" name="_runCurr" combokey="Common_Currcy" combotype="1" space="true" ></select><select id="_runUnit" name="_runUnit" combokey="lms1205s01_Unit" combotype="2" space="true" ></select><th:block th:text="#{'l120s01e.other2b'}">、%)</th:block>
                        </tr>
                        <tr class="hd1">
                            <td width="20%" align="center">
                                <th:block th:text="#{'l120s01e.other3'}">項目＼年度</th:block>
                            </td>
                            <td width="20%" align="center">
                                <input type="text" id="_finYear_D" name="_finYear_D" class="date" />
                            </td>
                            <td width="20%" align="center">
                                <input type="text" id="_finYear_C" name="_finYear_C" class="date" />
                            </td>
                            <td width="20%" align="center">
                                <input type="text" id="_finYear_B" name="_finYear_B" class="date" />
                            </td>
                            <td width="20%" align="center">
                                <input type="text" id="_finYear_A" name="_finYear_A" class="date" />
                            </td>
                        </tr>
                        <tr>
                            <td class="hd1" style="text-align: center">
                            	<span name="_finAmtName1" id="_finAmtName1" class="ct"></span>
                                <!--<th:block th:text="#{'l120s01e.other4'}">營收淨額</th:block>-->
                                <br/>
								<span name="_finRatioName1" id="_finRatioName1" class="ct"></span>
                                <!--<th:block th:text="#{'l120s01e.other5'}">(成長率%)</th:block>-->
                            </td>
                            <td align="right">
                                <input type="text" class="numeric" integer="13" id="_finAmtD1" name="_finAmtD1" size="13" maxlength="13" /><input type="hidden" id="_finAmtCodeD1" name="_finAmtCodeD1" size="13" maxlength="13" />
                                <br/>
                                <input type="text" id="_finRatioD1" name="_finRatioD1" size="6" class="numeric" positiveonly="false" integer="7" fraction="2" />%<input type="hidden" id="_finRatioCodeD1" name="_finRatioCodeD1" size="6" integer="7" fraction="2" />
                            </td>
                            <td align="right">
                                <input type="text" class="numeric" integer="13" id="_finAmtC1" name="_finAmtC1" size="13" maxlength="13" /><input type="hidden" id="_finAmtCodeC1" name="_finAmtCodeC1" size="13" maxlength="13" />
                                <br/>
                                <input type="text" id="_finRatioC1" name="_finRatioC1" size="6" class="numeric" positiveonly="false" integer="7" fraction="2" />%<input type="hidden" id="_finRatioCodeC1" name="_finRatioCodeC1" size="6" integer="7" fraction="2" />
                            </td>
                            <td align="right">
                                <input type="text" class="numeric" integer="13" id="_finAmtB1" name="_finAmtB1" size="13" maxlength="13" /><input type="hidden" id="_finAmtCodeB1" name="_finAmtCodeB1" size="13" maxlength="13" />
                                <br/>
                                <input type="text" id="_finRatioB1" name="_finRatioB1" size="6" class="numeric" positiveonly="false" integer="7" fraction="2" />%<input type="hidden" id="_finRatioCodeB1" name="_finRatioCodeB1" size="6" integer="7" fraction="2" />
                            </td>
                            <td align="right">
                                <input type="text" class="numeric" integer="13" id="_finAmtA1" name="_finAmtA1" size="13" maxlength="13" /><input type="hidden" id="_finAmtCodeA1" name="_finAmtCodeA1" size="13" maxlength="13" />
                                <br/>
                                <input type="text" id="_finRatioA1" name="_finRatioA1" size="6" class="numeric" positiveonly="false" integer="7" fraction="2" />%<input type="hidden" id="_finRatioCodeA1" name="_finRatioCodeA1" size="6" integer="7" fraction="2" />
                            </td>
                        </tr>
                        <tr>
                            <td class="hd1" style="text-align: center">
                                <span name="_finAmtName2" id="_finAmtName2" class="ct"></span>
                                <br/>
                                <span name="_finRatioName2" id="_finRatioName2" class="ct"></span>
                            </td>
                            <td align="right">
                                <input type="text" class="numeric" integer="13" id="_finAmtD2" name="_finAmtD2" size="13" maxlength="13" /><input type="hidden" id="_finAmtCodeD2" name="_finAmtCodeD2" size="13" maxlength="13" />
                                <br/>
                                <input type="text" id="_finRatioD2" name="_finRatioD2" size="6" class="numeric" positiveonly="false" integer="7" fraction="2" />%<input type="hidden" id="_finRatioCodeD2" name="_finRatioCodeD2" size="6" integer="7" fraction="2" />
                            </td>
                            <td align="right">
                                <input type="text" class="numeric" integer="13" id="_finAmtC2" name="_finAmtC2" size="13" maxlength="13" /><input type="hidden" id="_finAmtCodeC2" name="_finAmtCodeC2" size="13" maxlength="13" />
                                <br/>
                                <input type="text" id="_finRatioC2" name="_finRatioC2" size="6" class="numeric" positiveonly="false" integer="7" fraction="2" />%<input type="hidden" id="_finRatioCodeC2" name="_finRatioCodeC2" size="6" integer="7" fraction="2" />
                            </td>
                            <td align="right">
                                <input type="text" class="numeric" integer="13" id="_finAmtB2" name="_finAmtB2" size="13" maxlength="13" /><input type="hidden" id="_finAmtCodeB2" name="_finAmtCodeB2" size="13" maxlength="13" />
                                <br/>
                                <input type="text" id="_finRatioB2" name="_finRatioB2" size="6" class="numeric" positiveonly="false" integer="7" fraction="2" />%<input type="hidden" id="_finRatioCodeB2" name="_finRatioCodeB2" size="6" integer="7" fraction="2" />
                            </td>
                            <td align="right">
                                <input type="text" class="numeric" integer="13" id="_finAmtA2" name="_finAmtA2" size="13" maxlength="13" /><input type="hidden" id="_finAmtCodeA2" name="_finAmtCodeA2" size="13" maxlength="13" />
                                <br/>
                                <input type="text" id="_finRatioA2" name="_finRatioA2" size="6" class="numeric" positiveonly="false" integer="7" fraction="2" />%<input type="hidden" id="_finRatioCodeA2" name="_finRatioCodeA2" size="6" integer="7" fraction="2" />
                            </td>
                        </tr>
                        <tr>
                            <td class="hd1" style="text-align: center">
                                <span name="_finAmtName3" id="_finAmtName3" class="ct"></span>
                                <br/>
                                <span name="_finRatioName3" id="_finRatioName3" class="ct"></span>
                            </td>
                            <td align="right">
                                <input type="text" class="numeric" integer="13" id="_finAmtD3" name="_finAmtD3" size="13" maxlength="13" /><input type="hidden" id="_finAmtCodeD3" name="_finAmtCodeD3" size="13" maxlength="13" />
                                <br/>
                                <input type="text" id="_finRatioD3" name="_finRatioD3" size="6" class="numeric" positiveonly="false" integer="7" fraction="2" />%<input type="hidden" id="_finRatioCodeD3" name="_finRatioCodeD3" size="6" integer="7" fraction="2" />
                            </td>
                            <td align="right">
                                <input type="text" class="numeric" integer="13" id="_finAmtC3" name="_finAmtC3" size="13" maxlength="13" /><input type="hidden" id="_finAmtCodeC3" name="_finAmtCodeC3" size="13" maxlength="13" />
                                <br/>
                                <input type="text" id="_finRatioC3" name="_finRatioC3" size="6" class="numeric" positiveonly="false" integer="7" fraction="2" />%<input type="hidden" id="_finRatioCodeC3" name="_finRatioCodeC3" size="6" integer="7" fraction="2" />
                            </td>
                            <td align="right">
                                <input type="text" class="numeric" integer="13" id="_finAmtB3" name="_finAmtB3" size="13" maxlength="13" /><input type="hidden" id="_finAmtCodeB3" name="_finAmtCodeB3" size="13" maxlength="13" />
                                <br/>
                                <input type="text" id="_finRatioB3" name="_finRatioB3" size="6" class="numeric" positiveonly="false" integer="7" fraction="2" />%<input type="hidden" id="_finRatioCodeB3" name="_finRatioCodeB3" size="6" integer="7" fraction="2" />
                            </td>
                            <td align="right">
                                <input type="text" class="numeric" integer="13" id="_finAmtA3" name="_finAmtA3" size="13" maxlength="13" /><input type="hidden" id="_finAmtCodeA3" name="_finAmtCodeA3" size="13" maxlength="13" />
                                <br/>
                                <input type="text" id="_finRatioA3" name="_finRatioA3" size="6" class="numeric" positiveonly="false" integer="7" fraction="2" />%<input type="hidden" id="_finRatioCodeA3" name="_finRatioCodeA3" size="6" integer="7" fraction="2" />
                            </td>
                        </tr>
                        <tr>
                            <td class="hd1" style="text-align: center">
                                <span name="_finAmtName4" id="_finAmtName4" class="ct"></span>
                                <br/>
                                <span name="_finRatioName4" id="_finRatioName4" class="ct"></span>
                            </td>
                            <td align="right">
                                <input type="text" class="numeric" integer="13" id="_finAmtD4" name="_finAmtD4" size="13" maxlength="13" /><input type="hidden" id="_finAmtCodeD4" name="_finAmtCodeD4" size="13" maxlength="13" />
                                <br/>
                                <input type="text" id="_finRatioD4" name="_finRatioD4" size="6" class="numeric" positiveonly="false" integer="7" fraction="2" />%<input type="hidden" id="_finRatioCodeD4" name="_finRatioCodeD4" size="6" integer="7" fraction="2" />
                            </td>
                            <td align="right">
                                <input type="text" class="numeric" integer="13" id="_finAmtC4" name="_finAmtC4" size="13" maxlength="13" /><input type="hidden" id="_finAmtCodeC4" name="_finAmtCodeC4" size="13" maxlength="13" />
                                <br/>
                                <input type="text" id="_finRatioC4" name="_finRatioC4" size="6" class="numeric" positiveonly="false" integer="7" fraction="2" />%<input type="hidden" id="_finRatioCodeC4" name="_finRatioCodeC4" size="6" integer="7" fraction="2" />
                            </td>
                            <td align="right">
                                <input type="text" class="numeric" integer="13" id="_finAmtB4" name="_finAmtB4" size="13" maxlength="13" /><input type="hidden" id="_finAmtCodeB4" name="_finAmtCodeB4" size="13" maxlength="13" />
                                <br/>
                                <input type="text" id="_finRatioB4" name="_finRatioB4" size="6" class="numeric" positiveonly="false" integer="7" fraction="2" />%<input type="hidden" id="_finRatioCodeB4" name="_finRatioCodeB4" size="6" integer="7" fraction="2" />
                            </td>
                            <td align="right">
                                <input type="text" class="numeric" integer="13" id="_finAmtA4" name="_finAmtA4" size="13" maxlength="13" /><input type="hidden" id="_finAmtCodeA4" name="_finAmtCodeA4" size="13" maxlength="13" />
                                <br/>
                                <input type="text" id="_finRatioA4" name="_finRatioA4" size="6" class="numeric" positiveonly="false" integer="7" fraction="2" />%<input type="hidden" id="_finRatioCodeA4" name="_finRatioCodeA4" size="6" integer="7" fraction="2" />
                            </td>
                        </tr>
                    </table>
                </form>
            </div>
            
            <form id="formIdDscr1">
                <div class="hideThis">
                    <br/>
                    <b><th:block th:text="#{'l120s01e.other10'}">２、分析與評估</th:block>：<span class="ps1 text-red"><th:block th:text="#{'l120s01e.other11'}">(※含損益表中本業，業外收支及獲利重大異動之說明)</th:block></span></b>
                    <br/>
                    <textarea id="idDscr1" name="idDscr1" cols="500" rows="10%" class="tckeditor" showType="b" showNewLineMessage="Y" distanceWord="50" th:attr="displayMessage=#{'l120s01g.datadscr'}" preview="heigth:960;width:880">
                    </textarea>
                    <!--※資料請在系統所訂的行數範圍內登錄，超過部份可填寫於「其他」或「綜合評估及敘做理由」。-->
                </div>
            </form>
	</th:block>
</body>
</html>
