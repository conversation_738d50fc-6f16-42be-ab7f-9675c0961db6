/* 
 * C103M01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 訪談紀錄表資料檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "C103M01A", uniqueConstraints = @UniqueConstraint(columnNames = { "mainId" }))
public class C103M01A extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 訪談日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "INTERDATE", columnDefinition = "DATE")
	private Date interDate;

	/** 訪談地點 **/
	@Size(max = 210)
	@Column(name = "INTERPLACE", length = 210, columnDefinition = "VARCHAR(210)")
	private String interPlace;

	/** 分行洽談人 **/
	@Size(max = 90)
	@Column(name = "INTERPER", length = 90, columnDefinition = "VARCHAR(90)")
	private String interPer;

	/** 任職公司 **/
	@Size(max = 210)
	@Column(name = "COMPANY", length = 210, columnDefinition = "VARCHAR(210)")
	private String company;

	/** 地址:縣/市 **/
	@Size(max = 12)
	@Column(name = "CITY", length = 12, columnDefinition = "VARCHAR(12)")
	private String city;

	/** 地址:區/市/鄉/鎮 **/
	@Size(max = 3)
	@Column(name = "CITYAREA", length = 3, columnDefinition = "CHAR(3)")
	private String cityarea;

	/** 地址:路/街 **/
	@Size(max = 150)
	@Column(name = "ADDRESS", length = 150, columnDefinition = "VARCHAR(150)")
	private String address;

	/** 電子郵件 **/
	@Size(max = 75)
	@Column(name = "EMAIL", length = 75, columnDefinition = "VARCHAR(75)")
	private String email;

	/**
	 * 客戶類型
	 * <p/>
	 * N:新戶 O:舊戶
	 */
	@Size(max = 1)
	@Column(name = "CUSTTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String custType;

	/** 舊戶目前往來業務 **/
	@Size(max = 50)
	@Column(name = "OLDCUSTBUSINESS", length = 50, columnDefinition = "VARCHAR(50)")
	private String oldCustBusiness;

	/** 舊戶目前往來其他業務 **/
	@Size(max = 90)
	@Column(name = "OTHERBUSINESS", length = 50, columnDefinition = "VARCHAR(90)")
	private String otherBusiness;

	/**
	 * 本行潛在商機
	 * <p/>
	 * 1:房貸<br/>
	 * 2:信貸<br/>
	 * 3:理財<br/>
	 * 4:信用卡<br/>
	 * 5:信託<br/>
	 * 6:存款<br/>
	 * 7:其他
	 */
	@Size(max = 50)
	@Column(name = "POTENTIALMAIN", length = 50, columnDefinition = "VARCHAR(50)")
	private String potentialMain;

	/** 本行潛在商機其他說明 **/
	@Size(max = 50)
	@Column(name = "POTENTIALMAINOTHER", length = 50, columnDefinition = "VARCHAR(50)")
	private String potentialMainOther;

	/**
	 * 共銷潛在商機
	 * <p/>
	 * 1:產物保險<br/>
	 * 2:投信基金投資<br/>
	 * 3:開立證券戶、國內外股票買賣
	 */
	@Size(max = 50)
	@Column(name = "POTENTIALCOMMON", length = 50, columnDefinition = "VARCHAR(50)")
	private String potentialCommon;

	/**
	 * 共銷潛在商機-產險種類
	 * <p/>
	 * 1:住火險<br/>
	 * 2.車險<br/>
	 * 3:傷害險<br/>
	 * 4:健康險
	 */
	@Size(max = 50)
	@Column(name = "INSURANCE", length = 50, columnDefinition = "VARCHAR(50)")
	private String insurance;

	/**
	 * 是否轉介潛在商機予對應窗口
	 * <p/>
	 * Y:是<br/>
	 * N:否
	 */
	@Size(max = 1)
	@Column(name = "TRANSPOTENTIAL", length = 1, columnDefinition = "CHAR(1)")
	private String transPotential;

	/** 未轉介原因 **/
	@Size(max = 50)
	@Column(name = "NOTTRANSREASON", length = 50, columnDefinition = "VARCHAR(50)")
	private String notTransReason;

	/** 訪談紀要 **/
	@Size(max = 900)
	@Column(name = "INTERVIEWMEMO", length = 900, columnDefinition = "VARCHAR(900)")
	private String interviewMemo;

	/** 追蹤事項 **/
	@Size(max = 150)
	@Column(name = "TRACKINGITEM", length = 150, columnDefinition = "VARCHAR(150)")
	private String trackingItem;

	/** 主管意見 **/
	@Size(max = 150)
	@Column(name = "DIRECTOROPINION", length = 150, columnDefinition = "VARCHAR(150)")
	private String directorOpinion;

	/** 取得訪談日期 **/
	public Date getInterDate() {
		return this.interDate;
	}

	/** 設定訪談日期 **/
	public void setInterDate(Date value) {
		this.interDate = value;
	}

	/** 取得訪談地點 **/
	public String getInterPlace() {
		return this.interPlace;
	}

	/** 設定訪談地點 **/
	public void setInterPlace(String value) {
		this.interPlace = value;
	}

	/** 取得分行洽談人 **/
	public String getInterPer() {
		return this.interPer;
	}

	/** 設定分行洽談人 **/
	public void setInterPer(String value) {
		this.interPer = value;
	}

	/** 取得任職公司 **/
	public String getCompany() {
		return this.company;
	}

	/** 設定任職公司 **/
	public void setCompany(String value) {
		this.company = value;
	}

	/** 取得地址:縣/市 **/
	public String getCity() {
		return this.city;
	}

	/** 設定地址:縣/市 **/
	public void setCity(String value) {
		this.city = value;
	}

	/** 取得地址:區/市/鄉/鎮 **/
	public String getCityarea() {
		return this.cityarea;
	}

	/** 設定地址:區/市/鄉/鎮 **/
	public void setCityarea(String value) {
		this.cityarea = value;
	}

	/** 取得地址:路/街 **/
	public String getAddress() {
		return this.address;
	}

	/** 設定地址:路/街 **/
	public void setAddress(String value) {
		this.address = value;
	}

	/** 取得電子郵件 **/
	public String getEmail() {
		return this.email;
	}

	/** 設定電子郵件 **/
	public void setEmail(String value) {
		this.email = value;
	}

	/**
	 * 取得客戶類型
	 * <p/>
	 * N:新戶 O:舊戶
	 */
	public String getCustType() {
		return this.custType;
	}

	/**
	 * 設定客戶類型
	 * <p/>
	 * N:新戶 O:舊戶
	 **/
	public void setCustType(String value) {
		this.custType = value;
	}

	/** 取得舊戶目前往來業務 **/
	public String getOldCustBusiness() {
		return this.oldCustBusiness;
	}

	/** 設定舊戶目前往來業務 **/
	public void setOldCustBusiness(String value) {
		this.oldCustBusiness = value;
	}

	/** 取得舊戶目前往來其他業務 **/
	public String getOtherBusiness() {
		return this.otherBusiness;
	}

	/** 設定舊戶目前往來其他業務 **/
	public void setOtherBusiness(String value) {
		this.otherBusiness = value;
	}

	/**
	 * 取得本行潛在商機
	 * <p/>
	 * 1:房貸<br/>
	 * 2:信貸<br/>
	 * 3:理財<br/>
	 * 4:信用卡<br/>
	 * 5:信託<br/>
	 * 6:存款<br/>
	 * 7:其他
	 */
	public String getPotentialMain() {
		return this.potentialMain;
	}

	/**
	 * 設定本行潛在商機
	 * <p/>
	 * 1:房貸<br/>
	 * 2:信貸<br/>
	 * 3:理財<br/>
	 * 4:信用卡<br/>
	 * 5:信託<br/>
	 * 6:存款<br/>
	 * 7:其他
	 **/
	public void setPotentialMain(String value) {
		this.potentialMain = value;
	}

	/** 取得本行潛在商機其他說明 **/
	public String getPotentialMainOther() {
		return this.potentialMainOther;
	}

	/** 設定本行潛在商機其他說明 **/
	public void setPotentialMainOther(String value) {
		this.potentialMainOther = value;
	}

	/**
	 * 取得共銷潛在商機
	 * <p/>
	 * 1:產物保險<br/>
	 * 2:投信基金投資<br/>
	 * 3:開立證券戶、國內外股票買賣
	 */
	public String getPotentialCommon() {
		return this.potentialCommon;
	}

	/**
	 * 設定共銷潛在商機
	 * <p/>
	 * 1:產物保險<br/>
	 * 2:投信基金投資<br/>
	 * 3:開立證券戶、國內外股票買賣
	 **/
	public void setPotentialCommon(String value) {
		this.potentialCommon = value;
	}

	/**
	 * 取得共銷潛在商機-產險種類
	 * <p/>
	 * 1:住火險<br/>
	 * 2.車險<br/>
	 * 3:傷害險<br/>
	 * 4:健康險
	 */
	public String getInsurance() {
		return this.insurance;
	}

	/**
	 * 設定共銷潛在商機-產險種類
	 * <p/>
	 * 1:住火險<br/>
	 * 2.車險<br/>
	 * 3:傷害險<br/>
	 * 4:健康險
	 **/
	public void setInsurance(String value) {
		this.insurance = value;
	}

	/**
	 * 取得是否轉介潛在商機予對應窗口
	 * <p/>
	 * Y:是<br/>
	 * N:否
	 */
	public String getTransPotential() {
		return this.transPotential;
	}

	/**
	 * 設定是否轉介潛在商機予對應窗口
	 * <p/>
	 * Y:是<br/>
	 * N:否
	 **/
	public void setTransPotential(String value) {
		this.transPotential = value;
	}

	/** 取得未轉介原因 **/
	public String getNotTransReason() {
		return this.notTransReason;
	}

	/** 設定未轉介原因 **/
	public void setNotTransReason(String value) {
		this.notTransReason = value;
	}

	/** 取得訪談紀要 **/
	public String getInterviewMemo() {
		return this.interviewMemo;
	}

	/** 設定訪談紀要 **/
	public void setInterviewMemo(String value) {
		this.interviewMemo = value;
	}

	/** 取得追蹤事項 **/
	public String getTrackingItem() {
		return this.trackingItem;
	}

	/** 設定追蹤事項 **/
	public void setTrackingItem(String value) {
		this.trackingItem = value;
	}

	/** 取得主管意見 **/
	public String getDirectorOpinion() {
		return this.directorOpinion;
	}

	/** 設定主管意見 **/
	public void setDirectorOpinion(String value) {
		this.directorOpinion = value;
	}
}