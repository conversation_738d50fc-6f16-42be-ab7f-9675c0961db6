<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="panelFragmentBody">
            <script type="text/javascript">
				loadScript('pagejs/cls/CLS3401S071Panel');
			</script>
                <fieldset>
                    <legend>
                        <b><th:block th:text="#{'doc.baseInfo'}"><!--基本資訊--></th:block></b>
                    </legend>
                    <table class="tb2 alignTopTab " id="top_part" width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tbody>
						<tr>
							<td width="20%" class="hd2" align="right"><th:block th:text="#{'C340M01A.ownBrId'}">分行名稱</th:block>&nbsp;&nbsp;
							</td>
							<td width="30%" >
								<span id="ownBrId" class="field" />&nbsp;<span id="ownBrIdName" class="field" ></span>
							</td>
							<td width="18%" class="hd2" align="right"><th:block th:text="#{'C340M01A.docStatus'}">文件狀態</th:block>&nbsp;&nbsp;
							</td>
							<td width="32%" ><b class="text-red"><span id="docStatus" ></span>&nbsp;</b>
							</td>
						</tr>
						
						<tr>
							<td class="hd2" align="right"><th:block th:text="#{'C340M01A.custId'}">主要借款人</th:block>&nbsp;&nbsp;
							</td>
							<td><span id="custId" class="field" ></span>-<span id="dupNo" class="field" ></span>&nbsp;&nbsp;<span id="custName"></span>
							</td>
							<td class="hd2" align="right"><th:block th:text="#{'C340M01A.ploanCtrSignTimeM'}">線上對保借款人完成時間</th:block>&nbsp;
							</td>
							<td ><span id='ploanCtrSignTimeM' />&nbsp;
								<div>
									<span id='ploanBorrowerIPAddr' />&nbsp;
								</div>
							</td>							
						</tr>
						<tr>
							<td class="hd2" align="right"><span class="text-red">＊</span><th:block th:text="#{'C340M01A.ploanCtrNo'}">線上對保契約編號</th:block>&nbsp;
							</td>
							<td><span id='ploanCtrNo' ></span>&nbsp;
							</td>
							<td class="hd2" align="right"><th:block th:text="#{'C340M01A.caseNo'}">案號</th:block>&nbsp;&nbsp;</td>
							<td><span id="caseNo" ></span>&nbsp;
							</td>
						</tr>
						<tr>
							<td class="hd2" align="right"><span class="text-red">＊</span><th:block th:text="#{'label.ploanStakeholderQueryResult'}">利害關係人查詢結果</th:block>&nbsp;
							</td>
							<td colspan='3'><th:block th:text="#{'label.queryTimeStamp'}">查詢時間</th:block>：<span id='stkhQueryEJTs' ></span>&nbsp;
								<table border='1'>
									<tr>
										<td><th:block th:text="#{'label.C122M01A.stkhBank33'}">銀行法利害關係人</th:block>&nbsp;</td>
										<td><th:block th:text="#{'label.C122M01A.stkhFh44'}">金控法第44條利害關係人</th:block>&nbsp;</td>
										<td><th:block th:text="#{'label.C122M01A.stkhFh45'}">金控法第45條利害關係人</th:block>&nbsp;</td>
										<td><th:block th:text="#{'label.C122M01A.stkhRelFg'}">實質關係人(授信以外交易)</th:block>&nbsp;</td>
										<td><th:block th:text="#{'label.C122M01A.stkhCoFg'}">公司法與本行董事具有控制從屬關係公司</th:block>&nbsp;</td>
									</tr>
									<tr>
										<td><span id='stkhBank33' ></span>&nbsp;</td>
										<td><span id='stkhFh44' ></span>&nbsp;</td>
										<td><span id='stkhFh45' ></span>&nbsp;</td>
										<td><span id='stkhRelFg' ></span>&nbsp;</td>
										<td><span id='stkhCoFg' ></span>&nbsp;</td>
									</tr>
								</table>
							</td>
						</tr>
						<tr>
							<td class="ct hd2" colspan="4" style="background-color:rgb(186, 214, 255)">借款條件
							</td>
						</tr>
						<tr>
							<td class="hd2" align="right">借款金額
							</td>
							<td><input type="text" id="loanAmt" name="loanAmt" class="numeric" size="7">元
							</td>	
							<td class="hd2" align="right"><th:block th:text="#{'C340M01B.cntrNo'}">額度序號</th:block>&nbsp;
							</td>
							<td><span id="cntrNo" ></span>
							</td>						
						</tr>
						<tr>
							<td class="hd2" align="right">借款期閒(月)</td>
							<td><input type="text" id="loanPeriod" name="loanPeriod" class="numeric" readonly="readonly" size="2">月
							</td>
							<td class="hd2" align="right"><th:block th:text="#{'label.lnFromEndDate'}">授信起迄日</th:block>&nbsp;
							</td>
							<td><span id='ploanCtrBegDateToEndDate' ></span>&nbsp;
							</td>
						</tr>
						<tr>	
							<td class="hd2" align="right"><span id="rateTitle" name="rateTitle" class="field">利率</span></td>
							<td colspan="3">
								<span id='rateDesc'></span>&nbsp;
							</td>
						</tr>
						<tr>
							<td class="hd2" align="right"><th:block th:text="#{'label.ploanUploadGrid'}">線上對保檔案</th:block>&nbsp;
							</td>
							<td colspan='3'>
								<div id="test123" style="max-width:600px;" >
									<div id='ploanUploadGrid'></div>
								</div>
							</td>
						</tr>
						
						</tbody>
                    </table>
                </fieldset>
               
           
        </th:block>
    </body>
</html>
