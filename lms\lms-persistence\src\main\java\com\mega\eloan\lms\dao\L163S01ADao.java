/* 
 * L162S01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L163S01A;

/** 先行動用呈核及控制表檔 **/
public interface L163S01ADao extends IGenericDao<L163S01A> {

	L163S01A findByOid(String oid);

	List<L163S01A> findByMainId(String mainId);

	L163S01A findByUniqueKey(String mainId);

	List<L163S01A> findByIndex01(String mainId);
}