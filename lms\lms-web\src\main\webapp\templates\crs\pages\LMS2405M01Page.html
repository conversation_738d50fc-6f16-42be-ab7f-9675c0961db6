<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
	<body>
		<script type="text/javascript">
			loadScript('pagejs/crs/LMS2405M01Page');
		</script>
<!-- 		<script type="text/javascript"> -->
<!-- //  			require(['pagejs/crs/LMS2405M01Page']); -->
<!-- 		</script> -->
		<th:block th:fragment="innerPageBody">
		<div class="button-menu funcContainer" id="buttonPanel">
			<th:block th:if="${_btnDOC_EDITING_visible}">
				<button type="button" id="btnSave">
					<span class="ui-icon ui-icon-jcs-04"></span>
					<th:block th:text="#{'button.save'}">儲存</th:block>
				</button>
				<button type="button" id="btnSend">
					<span class="ui-icon ui-icon-jcs-02"></span>
					<th:block th:text="#{'button.send'}">呈主管覆核</th:block>
				</button>
			</th:block>
			<th:block th:if="${_btnWAIT_APPROVE_visible}">
				<button type="button" id="btnAccept">
					<span class="ui-icon ui-icon-check"></span>
					<th:block th:text="#{'button.acceptBack'}">核可</th:block>
				</button>
				<!-- 退回  -->
				<!--<button type="button" id="btnReturn">
					<span class="ui-icon ui-icon-closethick"></span>
					<th:block th:text="#{'button.return'}">退回</th:block>
				</button>-->
			</th:block>
			<th:block th:if="${_btnApply_visible}">
				<button type="button" id="btnSendRetrialReport">
			    	<span class="ui-icon ui-icon-jcs-216"></span>
					<th:block th:text="#{'button.SendRetrialReport'}">傳送分行覆審報告表</th:block>
				</button>
			</th:block>
			<button type="button" id="btnModifyDate">
				<span class="ui-icon ui-icon-jcs-15"></span>
				<th:block th:text="#{'button.ExceptRetrialDate'}">修改預計覆審日</th:block>
			</button>
			<button type="button" id="btnExit" class="forview">
				<span class="ui-icon ui-icon-jcs-01"></span>
				<th:block th:text="#{'button.exit'}">離開</th:block>
			</button>
		</div>
		<div class="tit2 color-black">
			<th:block th:text="#{'title'}"></th:block>
			<span class="color-blue" id="titInfo"></span>
		</div>
		<div class="tabs doc-tabs">
			<ul>
				<li><a href="#tab-01" goto="01"><b><th:block th:text="#{'doc.docinfo'}">文件資訊</th:block> </b> </a></li>
				<li><a href="#tab-02" goto="02"><b><th:block th:text="#{'tit02'}">案件資訊</th:block> </b> </a></li>
				<li><a href="#tab-03" goto="03"><b><th:block th:text="#{'tit03'}">附件檔案</th:block> </b> </a></li>
			</ul>
             <div class="tabCtx-warp">
                <div th:id="${tabID}" th:insert="~{${panelName} :: ${panelFragmentName}}"></div>
                    <!-- 	<form id="tabForm"> --><!-- <div wicket:id="_tabCtx" /> -->
                    <!-- </form> -->
             </div>
		</div>
		<div id="change" style="display: none">
			<form id="defaultForm">
			<table>
				<tr><td>
					<b><th:block th:text="#{'newDefaultDate'}">請輸入新預計覆審日：</th:block></b>
					&nbsp;<input type="text" size="8" class="required date" _requiredLength="10" id="defaultCTLDate" />
					<span class="text-red">(YYYY-MM-DD)</span>
				</td></tr>
			</table>
			</form>
		</div>
		<div id="acceptBack" style="display: none">
			<table>
				<tr><td>
					<label><input type="radio" name="apply" value=1 /><th:block th:text="#{'button.accept'}">核准</th:block></label>
					<label><input type="radio" name="apply" value=2 /><th:block th:text="#{'button.return'}">退回</th:block></label>
				</td></tr>
			</table>
		</div>
	</th:block>
</body>
</html>
