<!-- 授信異常通報共用元件 -->
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="panelFragmentBody">
        	<!--J-109-0291_05097_B1001 簡化小規模營業人異常通報簽報流程-->
			<MARQUEE bgColor=#ffcc00 behavior=alternate width="50%"><FONT color=red>異常通報注意事項</FONT></MARQUEE><BR>
			<DIV style="BORDER-TOP-STYLE: solid; BORDER-LEFT-STYLE: solid; BORDER-TOP-COLOR: red; BORDER-BOTTOM-STYLE: solid; BORDER-LEFT-COLOR: red; BORDER-BOTTOM-COLOR: red; BORDER-RIGHT-STYLE: solid; BORDER-RIGHT-COLOR: red">
			<FONT color=red>
			<p style="font-weight: bold; font-size: 125%; margin-bottom: 20px;">
			請注意! 異常通報簽報時，亦請一併評估是否徵提最新財報, 若有疑慮時應重新辦理徵信或評等!<BR>
			N.B.Please evaluate whether request for most recent financial statements is needed when under writing Abnormal Credit Report,Credit assessment or credit rating shall be updated when in doubt.
			</p>
			</FONT></DIV>
        	<form id="unNormalForm">
				<button type="button" id="btnGetUnNormalData" onclick="lmsM02Json.getUnNormalData()">
					<span class="text-only">引進帳務資料</span>
				</button>
				<span class="color-blue">異常通報案件</span>
	        	<table width="100%" class="tb2">
	        		<tr class="hPerson">
	        			<td width="25%" class="hd1">負責人&nbsp;&nbsp;</td>
						<td width="25%"><input type="text" id="chairman" name="chairman" class="max" maxlength="120" maxlengthC="40" /></td>
						<td width="25%" class="hd1">身份證統一編號&nbsp;&nbsp;</td>
						<td width="25%"><input type="text" id="chairmanId" name="chairmanId" class="max upText" maxlength="10" size="10" />&nbsp;重覆序號：<input type="text" id="chairmanDupNo" name="chairmanDupNo" class="max" maxlength="1" size="1" /></td>
	        		</tr>
					<tr>
						<td class="hd1" rowspan="3">異常狀況&nbsp;&nbsp;</td>
						<td rowspan="3"><textarea cols="20" rows="10" id="process" name="process" class="max txt_mult" maxlengthC="1024" style="padding:0px;margin:0px;width:270px;"></textarea></td>
						<td class="hd1">初次授信往來日期&nbsp;&nbsp;</td>
						<td><input type="text" class="date" id="firstDate" name="firstDate" /></td>
					</tr>
					<tr>
						<td class="hd1">最後一次續約日期&nbsp;&nbsp;</td>
						<td><input type="text" class="date" id="lastDate" name="lastDate" /></td>
					</tr>
					<tr>
						<td class="hd1">擔保品內容及押值&nbsp;&nbsp;</td>
						<td><textarea id="collStat" name="collStat" class="max txt_mult" cols="20" rows="5" maxlengthC="1024" style="width:270px;"></textarea></td>
					</tr>
	        		<tr>
	        			<td class="hd1">信保基金保證案件&nbsp;&nbsp;</td>
						<td><label><input type="radio" id="promiseCase" name="promiseCase" value="Y" onclick="$('#unNormalForm').find('.showPro').show();" />是</label>&nbsp;<label><input type="radio" id="promiseCase" name="promiseCase" value="N" onclick="$('#unNormalForm').find('.showPro').hide();"  />否</label></td>
						<td class="hd1"><span class="showPro hide">保證成數&nbsp;&nbsp;</span></td>
						<td><span class="showPro hide"><input type="text" id="promiseRatio" name="promiseRatio" class="max numText" maxlength="3" />成</span></td>
	        		</tr>
	
					<tr>
	        			<td class="hd1">額度（千元）&nbsp;&nbsp;</td>
						<td><textarea class="max txt_mult" cols="20" rows="5" id="amt" name="amt" maxlengthC="1024" style="width:270px;"></textarea></td>
						<td class="hd1">餘額（千元）&nbsp;&nbsp;</td>
						<td><textarea class="max txt_mult" cols="20" rows="5" id="remaind" name="remaind" maxlengthC="1024" style="width:270px;"></textarea></td>
	        		</tr>
	        		<tr>
	        			<td class="hd1">本行曝險金額總計（TWD千元）&nbsp;&nbsp;</td>
						<td><input type="text" class="numeric" integer="13" id="totRiskAmt" name="totRiskAmt" /></td>
						<td class="hd1">預估損失金額（TWD千元）&nbsp;&nbsp;</td>
						<td><input type="text" class="numeric" integer="13" id="lostAmt" name="lostAmt" /></td>
	        		</tr>
					<tr>
	        			<td class="hd1">該戶在聯行之額度（千元）&nbsp;&nbsp;</td>
						<td><textarea class="max txt_mult" cols="20" rows="5" id="bankAmt" name="bankAmt" maxlengthC="1024" style="width:270px;"></textarea></td>
						<td class="hd1">該戶在聯行之餘額（千元）&nbsp;&nbsp;</td>
						<td><textarea class="max txt_mult" cols="20" rows="5" id="bankRemaind" name="bankRemaind" maxlengthC="1024" style="width:270px;"></textarea></td>
	        		</tr>
	        		<tr class="showForNoneCaseTypeC"  style="display:none"> 
	        			<!--
	        			<td class="hd1">所屬企業集團&nbsp;&nbsp;</td>
						<td>
							<button type="button" onclick="lmsM02Json.getGrp()">
								<span class="text-only">引進</span>
							</button><br/>
							集團代碼：<input class="numText max" maxlength="4" type="text"
							id="grpId" name="grpId" size="4" /><br/>集團名稱：<input
							class="max" maxlength="60" type="text" id="grpName"
							name="grpName" maxlengthC="20" />							
						</td>
						-->
						<td class="hd1">該戶在同業之總餘額（千元）&nbsp;&nbsp;</td>
						<td><textarea class="max txt_mult" cols="20" rows="5" id="sameTotAmt" name="sameTotAmt" maxlengthC="1024" style="width:270px;"></textarea></td>
						<td class="hd1"></td>
						<td></td>
	        		</tr>
					<tr>
	        			<td class="hd1" rowspan="2">屬「本行重大偶發事件通報作業要點」應通報案件&nbsp;&nbsp;</td>
						<td rowspan="2">
							<label><input type="radio" id="isMajor" name="isMajor" value="Y" onclick="$('#unNormalForm').find('.showMajor').show();" />是</label>&nbsp;
						    <label><input type="radio" id="isMajor" name="isMajor" value="N" onclick="$('#unNormalForm').find('.showMajor').hide();"  />否</label>
						</td>
						<td class="hd1"><span class="showMajor hide">(1)屬該要點&nbsp;&nbsp;</span></td>
						<td>
							<div class="showMajor hide">
								<label style="letter-spacing:0px;cursor:pointer;">
									<input id="majorPt2" name="majorPt2" class="" type="checkbox" value="Y" >第二條之重大偶發事件。
								</label>
								<BR>
								<label style="letter-spacing:0px;cursor:pointer;">
									<input id="majorPt3" name="majorPt3" class="" type="checkbox" value="Y" >第三條之海外及大陸地區發生評估債權或投資損失金額達等值美元壹仟萬元以上之重大信用風險個案事件。
								</label>
				            </div>
						</td>
	        		</tr>
					<tr>
	        			<td class="hd1"><span class="showMajor hide">(2)辦理情形&nbsp;&nbsp;</span></td>
						<td>
							<div class="showMajor hide">
								(
									<label><input type="radio" id="majorPt4" name="majorPt4" value="Y"   />已辦理</label>&nbsp;
								    <label><input type="radio" id="majorPt4" name="majorPt4" value="N"   />未辦理</label>
								)<BR>
								依該要點第四條於發生當日立即以電話及書面通知稽核處及授信管理處相關單位，且應於三日內再詳細敘述事件發生原因、處理情形及改善措施陳報稽核處。
							</div>
						</td>
	        		</tr>
					<tr>
						<td colspan="4">
							<button type="button" onclick="lmsM02Json.thickClass()">
								<span class="text-only">選擇異常類別</span>
							</button>&nbsp;
							<button type="button" onclick="lmsM02Json.getLnfe0851()">
								<span class="text-only">查詢往來異常戶系統資料</span>
							</button>											
							異常類別：<span class="color-red" id="pMdClass" name="pMdClass"></span><input type="hidden" id="mdClass" name="mdClass" class="max" maxlength="3" />
						</td>
					</tr>
					<tr class="showForNoneCaseTypeC"  style="display:none">
						<td class="hd1">同業擬（已）採取之措施&nbsp;&nbsp;</td>
						<td colspan="3">
							<b class="star"><th:block th:text="#{'lms.ckeditRemark3'}">註1:|←建議換行</th:block></b><br/>
							　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　|←
							<textarea class="txt_mult max" cols="60" rows="6" maxlengthC="1024" id="sameIdea" name="sameIdea" style="padding:0px;margin:0px; width:770px;"></textarea><br/>
							　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　|←
							</td>
					</tr>
					<tr>
						<td class="hd1">擬（已）採取之措施&nbsp;&nbsp;</td>
						<td colspan="3">
							<span id="hideNotSub"><button type="button" onclick="lmsM02Json.thickOpenUnNormal(false)">
								<span class="text-only">登錄擬（已）辦事項</span>
							</button><br/></span>
							<span class="color-red field" id="willIdea" name="willIdea">
								<!--
								核定事項：<br/>
								◎同意另案簽報申請展期/協議清償。<br/>
								◎同意列報逾期放款或轉列催收款項。<br/>
								◎同意免列入追蹤控管。<br/>
								◎應查詞借保人財產。<br/>
								◎應儘速召開債權銀行團會議。-->
							</span>
						</td>
					</tr>
					<tr class="showForNoneCaseTypeC"  style="display:none">
						<td class="hd1">陳報及說明事項&nbsp;&nbsp;</td>
						<td colspan="3">
							<b class="star"><th:block th:text="#{'lms.ckeditRemark3'}">註1:|←建議換行</th:block></b>
							<button type="button" id="printRltvList" onclick="lmsM02Json.genLms1201r49('Y')">
								<span class="text-only">產生關係戶清單</span>
							</button>
							<span id="divPdfFile"></span><br>
							<span  class="color-red" >
								請注意授信戶實質上因(1)保證、(2)上下游及(3)集團關係 而存在與本案相關之關係戶，包括但不限於共同借款人、主要進銷貨廠商、投資績效或收入來源與授信戶營運密切相關者，
								亦或授信戶擔任另案之保證人、(4)共用擔保品等，如關係戶為聯行授信戶，應依本行「授信戶往來異常通報作業須知」第七條辦理，並於「陳報及說明事項」敘明關係戶通報情形(若無與本案相關之關係戶需通報，亦請簡要說明)。
							</span>
							　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　|←
							<textarea class="txt_mult max" id="reportDscr" name="reportDscr" cols="60" rows="6" maxlengthC="1024" style="padding:0px;margin:0px; width:770px;"></textarea>
                            <br/>
							　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　|←
						</td>
					</tr>
					<tr>
						<td  class="hd1">
							<span>
							是否有參貸行&nbsp;&nbsp;
							</span>
							
						</td>
						<td colspan="3" ><label><input type="radio" id="haseBrid" name="hasBrid" value="Y" onclick="$('#unNormalForm #showBrid').show();$('#unNormalForm #loginBran').show();$('#unNormalForm #showBrid :checkbox').attr('disabled',true);" />是</label>&nbsp;<label><input type="radio" id="haseBrid" name="hasBrid" value="N" onclick="$('#unNormalForm #showBrid').hide();$('#unNormalForm #loginBran').hide();" />否</label>
							<span id="loginBran" class="hide">
								<button type="button" onclick="lmsM02Json.thickBranch()">
									<span class="text-only">登錄參貸行</span>
								</button>								
							</span>
							<br>
							<span  class="color-red" >
							註：國內各營業單位於授信戶發生異常狀況時，如使用授信管理系統辦理授信異常通報，在各區域營運中心、國外部、金控總部分行或海外分行放行核轉授信管理處同時，該系統即自動將「授信戶異常通報表」副知風控處T1信箱及參貸行，營業單位毋須再人工通報。
							</span>
						</td>
					</tr>
					<tr id="showBrid" class="hide">
						<!-- 參貸行共用元件 -->
						<!--<div wicket:id="lmsm02a_panel" id="lmsm02a_panel"></div> -->
						<td colspan="4">
				            <table id="showAllBranch1" width="100%">	
							</table>        		
    	                </td>							
					</tr>
					<tr class="hide headShow">
						<td class="hd1">登錄人員&nbsp;&nbsp;</td>
						<td><span id="headUser" name="headUser"></span></td>
						<td class="hd1">放行日期&nbsp;&nbsp;</td>
						<td><span id="headApprove" name="headApprove"></span></td>
					</tr>
					<tr class="hide headShow">
						<td class="hd1">批覆&nbsp;&nbsp;</td>
						<td colspan="3"><span class="color-red" id="headSay" name="headSay"></span><br/><span class="color-red" id="headWillIdea" name="headWillIdea"></span></td>
					</tr>
	        	</table>
			</form>
			<!-- 異常通報參貸行ThickBox -->
			<div id="thickBranch" style="display:none">
				<form id="tBranchForm">
					<table width="100%" class="tb2">
						<tr>
							<!-- 參貸行共用元件 -->
							<!--<div wicket:id="lmsm02a_panel2" id="lmsm02a_panel2"></div>-->
							<td colspan="4">
					            <table id="showAllBranch2" width="100%">	
								</table>        		
        	                </td>					
						</tr>
					</table>					
				</form>
			</div>
			<!-- 異常通報類別ThickBox -->
			<div id="thickClass" style="display:none">
				<form id="tClassForm">
					<table width="100%" class="tb2">
						<tr>
							<td>
								<select id="sMdClass" name="sMdClass"></select>
							</td>
						</tr>												
					</table>					
				</form>
			</div>																								
        </th:block>
    </body>
</html>
