pageJsInit(function() {
	$(function() {
		/***執行輸入查詢ID視窗***/
		openQuery();

		var grid = $("#gridview").iGrid({
			handler: 'lms2301gridhandler',
			height: 350,
			width: 785,
			autowidth: false,
			rowNum: 15,
			sortname: 'createTime',
			sortorder: 'desc',
			colModel: [{
				colHeader: i18n.lms2301v01['approveTime'], //報送日期 == 核准日期
				align: "left",
				width: 100,
				sortable: true,
				formatter: 'date',
				formatoptions: {
					srcformat: 'Y-m-d H:i:s',
					newformat: 'Y-m-d'
				},
				name: 'approveTime'
			}, {
				colHeader: i18n.lms2301v01['custId'],//統一編號
				align: "left",
				width: 100,
				sortable: true,
				name: 'custId',
				formatter: 'click',
				onclick: openDoc
			}, {
				colHeader: i18n.lms2301v01['custName'], //客戶名稱
				align: "left",
				width: 100,
				sortable: true,
				name: 'custName'
			}, {
				colHeader: i18n.lms2301v01['caseNo'], //案件號碼
				align: "left",
				width: 150,
				sortable: true,
				name: 'caseNo'
			}, {
				colHeader: i18n.lms2301v01['reasion'], //未簽約、動用原因
				align: "left",
				width: 100,
				sortable: true,
				name: 'reasion'
			}, {
				colHeader: i18n.lms2301v01['appraiser'], //經辦姓名
				align: "left",
				width: 100,
				sortable: true,
				name: 'apprId'
			}, {
				colHeader: "oid",
				name: 'oid',//oid
				hidden: true //是否隱藏
			}, {
				name: 'docURL',
				hidden: true
			}],
			ondblClickRow: function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
				var data = $("#gridview").getRowData(rowid);
				openDoc(null, null, data);
			}
		});

		function openDoc(cellvalue, options, rowObject) {
			//開啟lms2305m01
			$.form.submit({
				url: '..' + rowObject.docURL + '/01',//'../lms/lms2301m01/01'
				data: {
					mainOid: rowObject.oid,
					mainId: rowObject.mainId,
					mainDocStatus: viewstatus
				},
				target: rowObject.oid//"_blank" // 給 thickbox oid
			});
		};

		$("#buttonPanel").find("#btnDelete").click(function() {
			var rows = $("#gridview").getGridParam('selarrrow');
			var data = [];

			if (rows == "") {// TMMDeleteError=請先選擇需修改(刪除)之資料列
				return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
			}

			//confirmDelete=是否確定刪除?
			CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b) {
				if (b) {
					for (var i in rows) {
						data.push($("#gridview").getRowData(rows[i]).oid);
					}

					$.ajax({
						handler: "lms1605m01formhandler",
						data: {
							formAction: "deleteL160m01a",
							oids: data
						},
					}).done(function(obj) {
						$("#gridview").trigger("reloadGrid");

					});
				}
			});


		}).end().find("#btnView").click(function() {
			var id = $("#gridview").getGridParam('selrow');

			if (!id) {

				// action_004=請先選擇需「調閱」之資料列
				return CommonAPI.showMessage(i18n.def["action_004"]);
			}
			if (id.length > 1) {
				CommonAPI.showMessage(i18n.lms1605m01["L160M01a.error1"]);
			} else {
				var result = $("#gridview").getRowData(id);
				openDoc(null, null, result);
			}
		}).end().find("#btnFilter").click(function() {
			openQuery();
		});
	});
});
/***輸入查詢ID視窗***/
function openQuery(){
    var $filterForm = $("#LMS2305V03Form");
    //初始化
    $filterForm.reset();
    $("[name=docDate][value=1]").prop("checked", true);
    $("#docFirstDate").val(CommonAPI.getToday())
    $("#docLastDate").val(CommonAPI.getToday());
    $("#openbox001").thickbox({ // 使用選取的內容進行彈窗
        //l230m01a.title12=簽約未動用授信案件報送作業
        title: i18n.lms2301v01['l230m01a.title12'],
        width: 470,
        height: 220,
        modal: true,
        valign: "bottom",
        align: "center",
        i18n: i18n.def,
        buttons: {
            "sure": function(){
                if (!$("[name='docDate']:checked").val()) {
                    //l230m01a.title02=請選擇選項
                    return CommonAPI.showMessage(i18n.lms2301v01["l230m01a.title02"]);
                }
                if (!$("#LMS2305V03Form").valid()) {
                    return;
                }
                
                if ($.trim($("#docLastDate").val()) != "" || $.trim($("#docFirstDate").val()) != "") {
                
                    var end = $("#docLastDate").val().split("-");
                    var from = $("#docFirstDate").val().split("-");
                    var endData = new Date(end[0], end[1], end[2]);
                    var fromData = new Date(from[0], from[1], from[2]);
                    var tempStartDate = $("#docFirstDate").val();
                    var tempEndDate = $("#docLastDate").val();
                    
                    //如果日期大小顛倒直接幫他調換過來
                    if (fromData > endData) {
                        tempStartDate = $("#docLastDate").val();
                        tempEndDate = $("#docFirstDate").val();
                        
                    }
                    
                };
                
                filterGrid({
                    endDate: tempEndDate,
                    fromDate: tempStartDate,
                    custid: $("#custId").val()
                });
                $.thickbox.close();
            },
            "cancel": function(){
                $("#LMS2305V03Form").reset();
                filterGrid({
                    type: "0"
                });
                $.thickbox.close();
            }
        }
    });
}



//grid資料篩選
function filterGrid(sendData){
    $("#gridview").jqGrid("setGridParam", {
        postData: $.extend({
            formAction: "queryByDateString",
            docStatus: viewstatus,
            type: $("[name=docDate]:checked").val()
        }, sendData || {}),
        search: true
    }).trigger("reloadGrid");
}
