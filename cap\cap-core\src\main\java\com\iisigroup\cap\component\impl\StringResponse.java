/* 
 * StringResponse.java
 * 
 * Copyright (c) 2009-2012 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.iisigroup.cap.component.impl;

import java.io.OutputStream;

import javax.servlet.ServletResponse;

import tw.com.iisi.cap.exception.CapException;

/**
 * <pre>
 * 設置Respone內容
 * </pre>
 * 
 * @since 2012/9/24
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2012/9/24,iristu,new
 *          </ul>
 */
public class StringResponse {

    /** the string for the response. */
    private final String string;

    /** content type for the string */
    private final String contentType;

    /** charset of the string */
    private final String encoding;

    /**
     * 建立 String Respone
     * 
     * @param contentType
     *            本次回傳的媒體類型
     * @param encoding
     *            編碼
     * @param str
     *            文字內容
     */
    public StringResponse(String contentType, String encoding, String str) {
        this.contentType = contentType;
        this.string = str;
        this.encoding = encoding;
    }

    /**
     * 輸出 respond
     * 
     * @param response
     *            回應
     * @throws CapException
     */
    public void respond(ServletResponse response) throws CapException {
        response.setContentType(contentType + ";charset=" + encoding);
        try {
            OutputStream out = response.getOutputStream();
            out.write(string.getBytes(encoding));
            out.flush();
        } catch (Exception e) {
            throw new CapException(e.getMessage(), e, getClass());
        }
    }
}
