package com.mega.eloan.lms.lns.flow;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.core.FlowException;

import com.mega.eloan.common.dao.CommonMetaDao;
import com.mega.eloan.common.enums.DocAuthTypeEnum;
import com.mega.eloan.common.flow.AbstractFlowHandler;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.FlowNameService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dao.C120M01ADao;
import com.mega.eloan.lms.dao.L120A01ADao;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dao.L130M01ADao;
import com.mega.eloan.lms.dao.L130S01BDao;
import com.mega.eloan.lms.dao.L140M01ADao;
import com.mega.eloan.lms.dao.L700M01ADao;
import com.mega.eloan.lms.lms.flow.LMS1205AreaFlow;
import com.mega.eloan.lms.lms.flow.LMS1205HeadFlow;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.L120A01A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L130M01A;
import com.mega.eloan.lms.model.L130S01B;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L700M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * <pre>
 * 案件簽報書 - (國內流程) 簡易分行和國內一般分行
 * </pre>
 * 
 * @since 2013/3/5
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/3/5,REX,new
 *          <li>2013/06/27,Rex,當營運中心areabrid為空 幫她帶預設值
 *          </ul>
 */
@Component
public class LMS1200Flow extends AbstractFlowHandler {
	public static final String FLOW_CODE = "LMS1200Flow";

	@Resource
	CommonMetaDao metaDao;

	@Resource
	L140M01ADao l140m01aDao;

	@Resource
	L700M01ADao l700m01aDao;

	@Resource
	L120A01ADao l120a01aDao;

	@Resource
	L130S01BDao l130s01bDao;

	@Resource
	BranchService branchService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	L120M01ADao l120m01aDao;

	@Resource
	C120M01ADao c120m01aDao;

	@Resource
	DocLogService docLogService;

	@Resource
	LMSService lmsService;

	@Resource
	CLSService clsService;

	@Resource
	L130M01ADao l130m01aDao;

	/**
	 * 這裡經由起案分行決定分行待覆核、或總行待覆核
	 * 
	 * @param instance
	 *            流程資料
	 */
	@Transition(node = "編製中", value = "to分行判斷")
	public void start(FlowInstance instance) {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = (L120M01A) metaDao.findByOid(getDomainClass(),
				instanceId);
		/** parentBrNo 取得所屬母行 **/
		String caseBrId = Util.trim(meta.getCaseBrId());
		IBranch branch = branchService.getBranch(caseBrId);
		String parentBrNo = Util.trim(branch.getParentBrNo());

		if (Util.isEmpty(parentBrNo)) {
			instance.setAttribute("result", "to母行待覆核");
		} else {
			instance.setAttribute("result", "to簡易行待覆核");
		}
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		if (user.getUnitNo().equals(caseBrId)) {
			lmsService.gfnDB2SetELF442_CNTRNO(meta,
					UtilConstants.Cntrdoc.ACTION.呈主管, null);
		}
		if (UtilConstants.Casedoc.DocKind.授權外.equals(meta.getDocKind())) {
			// 授權外額度明細表要把額度明細表的文件狀態改為待覆核
			lmsService.resetL140M01A(meta, FlowDocStatusEnum.待覆核.getCode());

		} else if (UtilConstants.Casedoc.DocKind.授權內.equals(meta.getDocKind())
				&& UtilConstants.Casedoc.AuthLvl.營運中心授權內.equals(meta
						.getAuthLvl())) {
			// 當是授權內 且為營運中心授權內 其文件 狀態要變更
			lmsService.resetL140M01A(meta, FlowDocStatusEnum.待覆核.getCode());
		}
	}

	/**
	 * 離開 待補件 這裡經由起案分行決定分行待覆核、或總行待覆核
	 * 
	 * @param instance
	 *            流程資料
	 */
	@Transition(node = "待補件", value = "to分行判斷")
	public void start2(FlowInstance instance) {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = (L120M01A) metaDao.findByOid(getDomainClass(),
				instanceId);
		meta.setReturnFromBH(null);
		/** parentBrNo 取得所屬母行 **/
		String parentBrNo = Util.trim(branchService.getBranch(
				meta.getCaseBrId()).getParentBrNo());
		if (Util.isEmpty(parentBrNo)) {
			instance.setAttribute("result", "to母行待覆核");
		} else {
			instance.setAttribute("result", "to簡易行待覆核");
		}

		if (UtilConstants.Casedoc.DocKind.授權外.equals(meta.getDocKind())) {
			// 授權外額度明細表要把額度明細表的文件狀態改為待覆核
			lmsService.resetL140M01A(meta, FlowDocStatusEnum.待覆核.getCode());
			// 授權外簽報書，營運中心退分行更正後，案件要顯示在待更正MENU內，
			// 但若分行修改送呈單位後(例如由呈營運中心改直接送授管處)，則原授權單位(營運中心)就要刪掉
			if (!UtilConstants.Casedoc.AreaChk.送審查.equals(meta.getAreaChk())) {
				if (FlowNameService.FlowbackUnitEnum.營運中心_退回分行.getCode()
						.equals(meta.getBackUnit())) {
					meta.setBackUnit(null);
				}
			}
		} else if (UtilConstants.Casedoc.DocKind.授權內.equals(meta.getDocKind())
				&& UtilConstants.Casedoc.AuthLvl.營運中心授權內.equals(meta
						.getAuthLvl())) {
			// 當是授權內 且為營運中心授權內 其文件 狀態要變更
			lmsService.resetL140M01A(meta, FlowDocStatusEnum.待覆核.getCode());
		}
	}

	/**
	 * 簡易行(判斷核定還是婉卻)
	 * 
	 * @param instance
	 *            流程資料
	 */
	@Transition(node = "簡易行待覆核", value = "to決策")
	public void checkByUnitTypeJ(FlowInstance instance) {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = l120m01aDao.findByOid(instanceId);
		String result = (String) instance.getAttribute("result");

		if ("to核定".equals(result)) {
			// 檢查主管與經辦是否為同一人
			lmsService.checkAppraiser(meta, UtilConstants.BRANCHTYPE.分行,
					UtilConstants.STAFFJOB.經辦L1);
			List<L140M01A> l140m01as = l140m01aDao
					.findL140m01aListByL120m01cMainId(meta.getMainId(),
							UtilConstants.Cntrdoc.ItemType.額度明細表,
							FlowDocStatusEnum.已核准.getCode());
			// 當無已核准額度明細表且 該案件非陳覆陳述案 為婉卻
			if (l140m01as.isEmpty()
					&& !UtilConstants.Casedoc.DocCode.陳復陳述案.equals(meta
							.getDocCode())) {
				instance.setAttribute("result", "to已婉卻");
			} else {
				instance.setAttribute("result", "to核定");
			}
		}

	}

	/**
	 * 一般分行( 判斷核定還是婉卻)
	 * 
	 * 
	 * @param instance
	 *            流程資料
	 */
	@Transition(node = "母行待覆核", value = "to決策")
	public void checkByUnitTypeB(FlowInstance instance) {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = l120m01aDao.findByOid(instanceId);
		List<L140M01A> l140m01as = l140m01aDao
				.findL140m01aListByL120m01cMainId(meta.getMainId(),
						UtilConstants.Cntrdoc.ItemType.額度明細表,
						FlowDocStatusEnum.已核准.getCode());

		String result = (String) instance.getAttribute("result");
		if ("to核定".equals(result)) {
			// 檢查主管與經辦是否為同一人
			lmsService.checkAppraiser(meta, UtilConstants.BRANCHTYPE.分行,
					UtilConstants.STAFFJOB.經辦L1);
			// 當無已核准額度明細表且 該案件非陳覆陳述案 為婉卻

			// J-GGG-XXXX
			if (l140m01as.isEmpty()
					&& !UtilConstants.Casedoc.DocCode.陳復陳述案.equals(meta
							.getDocCode())
			// && !UtilConstants.Casedoc.DocCode.異常通報.equals(meta
			// .getDocCode())
			) {
				instance.setAttribute("result", "to已婉卻");
			} else {
				instance.setAttribute("result", "to核定");
			}
		}
	}

	/**
	 * 
	 * 呈區域中心
	 * 
	 * @param instance
	 *            流程資料
	 */
	@Transition(node = "決策", value = "to呈區域中心")
	public void sendArea(FlowInstance instance) {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = (L120M01A) metaDao.findByOid(getDomainClass(),
				instanceId);
		// J-111-0488_05097_B1001 Web
		// e-Loan企金授信簽報系統增加強分行經理權限對於合併關係企業彙總額度之管控
		if (lmsService.isOverAuthSendHead(meta)) {
			meta.setAreaChk(UtilConstants.Casedoc.AreaChk.送審查);
			l120m01aDao.save(meta);
		}
		String nextOwnbrid = Util.trim(meta.getAreaBrId());
		// start 2013/06/27,Rex,當營運中心areabrid為空 幫她帶預設值
		if (Util.isEmpty(nextOwnbrid)) {
			IBranch branch = branchService.getBranch(meta.getCaseBrId());
			nextOwnbrid = branch.getBrnGroup();
			meta.setAreaBrId(nextOwnbrid);
		}
		// end 2013/06/27,Rex,當營運中心areabrid為空 幫她帶預設值
		meta.setOwnBrId(nextOwnbrid);
		L700M01A l700m01a = l700m01aDao.findByBranchId(nextOwnbrid,
				meta.getCaseBrId());
		if (l700m01a != null) {
			meta.setAreaAppraiser(l700m01a.getUserNo());
		}
		lmsService.checkAppraiser(meta, UtilConstants.BRANCHTYPE.分行,
				UtilConstants.STAFFJOB.經辦L1);
		// 設定分行放行時間
		lmsService.setSentTime(meta);
		// 新增簽章欄
		lmsService.saveL120M01F(meta, UtilConstants.BRANCHTYPE.分行,
				UtilConstants.STAFFJOB.執行覆核主管L4);
		// 先判斷是否已經有這筆授權檔
		lmsService.saveL12A01A(meta, nextOwnbrid, UtilConstants.AuthType.送審查);
		// 判斷退補件欄位
		lmsService.checkBackUnit(meta);
		// 要去除多餘的營運中心授權檔，不然在營運中心退過的案件再其營運中心會出現
		lmsService.checkL120A01A(meta);
		// 新增近期已收
		lmsService.saveL000M01A(meta, meta.getAreaBrId());
		lmsService.gfnInsertELF447N(meta, UtilConstants.Cntrdoc.ItemType.額度明細表,
				CreditDocStatusEnum.海外_呈營運中心.getCode(), nextOwnbrid);
		instance.setAttribute("flowCode", LMS1205AreaFlow.FLOW_CODE);
		// 新增簽章欄
		lmsService.saveL120M01F(meta, UtilConstants.BRANCHTYPE.分行,
				UtilConstants.STAFFJOB.執行覆核主管L4);

		// J-103-0027 核准時設定為需要傳送卡務中心
		String mainId = Util.trim(meta.getMainId());
		if (UtilConstants.Casedoc.DocCode.異常通報.equals(meta.getDocCode())) {
			// 為了時效性，營運中心制分行核准送營運中心sendArea或總處分行核准送授管處sendHead時就設定為要傳給卡務
			// 但結案註記要等授管處核准時才寫為Y
			L130M01A l130m01a = l130m01aDao.findByUniqueKey(mainId);
			if (l130m01a != null) {
				l130m01a.setNeedSend("Y");
				// l130m01a.setCloseFg(""); 結案註記要等授管處核准時才寫為Y
				// LMSServiceImpl.java \insertLnfe0854
				l130m01aDao.save(l130m01a);
			}
		}

		// 國內個金才會有上傳優惠房貸
		if (LMSUtil.isClsCase(meta)) {
			List<L140M01A> l140m01as = l140m01aDao
					.findL140m01aListByL120m01cMainId(meta.getMainId(),
							UtilConstants.Cntrdoc.ItemType.額度明細表,
							FlowDocStatusEnum.待覆核.getCode());
			for (L140M01A l140m01a : l140m01as) {
				lmsService.upELF431(meta, l140m01a);
			}

		}

	}

	/**
	 * 
	 * 呈授管處
	 * 
	 * @param instance
	 *            流程資料
	 */
	@Transition(node = "決策", value = "to呈授管處")
	public void sendHead(FlowInstance instance) {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = (L120M01A) metaDao.findByOid(getDomainClass(),
				instanceId);
		String nextOwnbrid = UtilConstants.BankNo.授管處;
		meta.setOwnBrId(nextOwnbrid);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		meta.setApprover(user.getUserId());
		meta.setApproveTime(CapDate.getCurrentTimestamp());
		L700M01A l700m01a = l700m01aDao.findByBranchId(nextOwnbrid,
				meta.getCaseBrId());
		if (l700m01a != null) {
			meta.setHqAppraiser(l700m01a.getUserNo());
		}
		lmsService.checkAppraiser(meta, UtilConstants.BRANCHTYPE.分行,
				UtilConstants.STAFFJOB.經辦L1);
		// 設定分行放行時間
		lmsService.setSentTime(meta);
		// 判斷退補件欄位
		lmsService.checkBackUnit(meta);
		// 新增簽章欄
		lmsService.saveL120M01F(meta, UtilConstants.BRANCHTYPE.分行,
				UtilConstants.STAFFJOB.執行覆核主管L4);
		// 新增授權檔
		lmsService.saveL12A01A(meta, nextOwnbrid,
				DocAuthTypeEnum.VIEW_TRANSFER.getCode());

		// J-111-0107_05097_B1001 Web e-Loan企金增加借戶ESG外部綜合評分資料相關資料。
		IBranch theBranch = branchService.getBranch(Util.trim(meta
				.getCaseBrId()));
		String brnGroup = Util.trim(theBranch.getBrnGroup());

		// 分行直送授審處
		// J-112-0076_05097_B1001 Web
		// e-Loan授信針對一般異常通報案件(非小規C方案)，報案流程變更為要經北區營運中心後，再送授信審查處
		boolean isDirectSend = lmsService.isAreaBranchDirectSendHead(meta);
		if (isDirectSend) {
			// 如果直送，要幫營運中心加上授權
			lmsService.saveL12A01A(meta, brnGroup,
					DocAuthTypeEnum.VIEW_TRANSFER.getCode());
		}

		// 新增近期已收
		lmsService.saveL000M01A(meta, UtilConstants.BankNo.授管處);
		meta.setHqMeetFlag(null);
		// 新增近期已收

		instance.setAttribute("flowCode", LMS1205HeadFlow.FLOW_CODE);

		// 異常通報Mail風控處與所選參貸行
		if (theBranch != null) {
			String mainId = Util.trim(meta.getMainId());
			List<L130S01B> listL130s01a = l130s01bDao.findByMainId(mainId);

			// J-110-0104_05097_B1001 Web
			// e-Loan企金授信配合組織再造，預計北一區、北二區合併為北區授管中心，北區營業單位之授管中心授權外案件直送授信審查處
			// J-112-0076_05097_B1001 Web
			// e-Loan授信針對一般異常通報案件(非小規C方案)，報案流程變更為要經北區營運中心後，再送授信審查處
			if ((UtilConstants.BankNo.中部區域授信中心.equals(brnGroup)
					|| UtilConstants.BankNo.北一區營運中心.equals(brnGroup)
					|| UtilConstants.BankNo.南部區域授信中心.equals(brnGroup)
					|| UtilConstants.BankNo.北二區營運中心.equals(brnGroup)
					|| UtilConstants.BankNo.桃竹苗區營運中心.equals(brnGroup)
					|| UtilConstants.BankNo.中區營運中心.equals(brnGroup) || UtilConstants.BankNo.南區營運中心
					.equals(brnGroup))
					&& !lmsService.isAreaBranchDirectSendHead(meta)) {
				// 營運中心制分行於營運中心覆核時傳送異常通報MAIL給風控處與參貸行
			} else {
				// 非營運中心制分行於分行覆核時就傳送異常通報MAIL給風控處與參貸行
				// 開始進行Mail動作
				if (UtilConstants.Casedoc.DocCode.異常通報
						.equals(meta.getDocCode())) {
					lmsService.sendEmail(mainId, meta, listL130s01a, "0");

					// J-103-0027 核准時設定為需要傳送卡務中心

					// 為了時效性，營運中心制分行核准送營運中心sendArea或總處分行核准送授管處sendHead時就設定為要傳給卡務
					// 但結案註記要等授管處核准時才寫為Y
					L130M01A l130m01a = l130m01aDao.findByUniqueKey(mainId);
					if (l130m01a != null) {
						l130m01a.setNeedSend("Y");

						// J-105-0065-001 Web e-Loan
						// 授信管理系統修改異常通報副知風控處說明文字及新增統計報表
						l130m01a.setSend912MailTime(CapDate
								.getCurrentTimestamp());

						// l130m01a.setCloseFg(""); 結案註記要等授管處核准時才寫為Y
						// LMSServiceImpl.java \insertLnfe0854
						l130m01aDao.save(l130m01a);
					}

				}

			}
		}
		lmsService.gfnInsertELF447N(meta, UtilConstants.Cntrdoc.ItemType.額度明細表,
				CreditDocStatusEnum.海外_呈授管處.getCode(), nextOwnbrid);
		String areack = meta.getAreaChk();
		if (!Util.isEmpty(areack)) {
			meta.setOwnBrId(nextOwnbrid);
			switch (Util.parseInt(areack)) {
			case 1:
				// 否 -直接送"授管處-待收件"
				// 依流程變更目前編制行
				meta.setAreaDocstatus(null);
				meta.setAreaUpdater(null);
				meta.setAreaApprover(null);
				meta.setAreaApprTime(null);
				break;
			case 2:
				lmsService.sendL121M01A(meta);
				break;
			case 3:
				// 送審查-送營運中心 並增加一筆資料到授權檔該營運中心案件
				meta.setAreaDocstatus(null);
				meta.setAreaUpdater(null);
				meta.setAreaApprover(null);
				meta.setAreaApprTime(null);

				// J-110-0104_05097_B1001 Web
				// e-Loan企金授信配合組織再造，預計北一區、北二區合併為北區授管中心，北區營業單位之授管中心授權外案件直送授信審查處
				// J-112-0076_05097_B1001 Web
				// e-Loan授信針對一般異常通報案件(非小規C方案)，報案流程變更為要經北區營運中心後，再送授信審查處
				if (!lmsService.isAreaBranchDirectSendHead(meta)) {
					meta.setOwnBrId(meta.getAreaBrId());
				}

				break;
			case 5:
				// (108)第 3230 號
				lmsService.sendL121M01A(meta);
				break;
			}
		}
		// 國內個金才會有上傳優惠房貸
		if (LMSUtil.isClsCase(meta)) {
			List<L140M01A> l140m01as = l140m01aDao
					.findL140m01aListByL120m01cMainId(meta.getMainId(),
							UtilConstants.Cntrdoc.ItemType.額度明細表,
							FlowDocStatusEnum.已核准.getCode());
			for (L140M01A l140m01a : l140m01as) {
				lmsService.upELF431(meta, l140m01a);
			}

		}
	}

	/**
	 * 當案件結束時做的動作
	 * 
	 * @param instance
	 *            flow 流程檔
	 * @param docstatus
	 *            文件狀態
	 * @param staffjob
	 *            職稱欄
	 * @throws CapException
	 * @throws FlowException
	 */
	private void toEndAction(FlowInstance instance, String docstatus,
			String staffjob) throws FlowException, CapException {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();

		L120M01A meta = (L120M01A) metaDao.findByOid(getDomainClass(),
				instanceId);
		// 為營運中心或授管處覆核為Y，分行覆核為N
		meta.setIsHeadCheck(UtilConstants.DEFAULT.否);
		String docRslt = "";
		if (CreditDocStatusEnum.海外_已核准.getCode().equals(docstatus)) {
			docRslt = UtilConstants.Casedoc.DocRslt.承做;
			//J-110-0330 確定承做，寫入AO帳號管理員
			lmsService.gfnLNF013(meta);
		} else {
			docRslt = UtilConstants.Casedoc.DocRslt.婉卻;
		}
		lmsService.uploadELLNSEEK(meta);
		meta.setDocRslt(docRslt);
		meta.setOwnBrId(meta.getCaseBrId());
		l120m01aDao.save(meta);
		// 要去除多餘的營運中心授權檔，不然在已核准或已婉卻會多出現
		lmsService.checkL120A01A(meta);
		// 設定分行放行時間
		lmsService.setSentTime(meta);
		// 新增簽章欄
		lmsService.saveL120M01F(meta, UtilConstants.BRANCHTYPE.分行, staffjob);

		if (UtilConstants.Casedoc.SimplifyFlag.快速審核信貸.equals(meta
				.getSimplifyFlag())
				&& UtilConstants.Casedoc.DocRslt.承做.equals(docRslt)) {
			lmsService.saveL120M01F(meta, UtilConstants.BRANCHTYPE.分行,
					UtilConstants.STAFFJOB.無紙化簽報最後覆核主管P7);
		}

		// 更新預約額度檔
		lmsService.gfnDB2SetELF442_CNTRNO(meta,
				UtilConstants.Cntrdoc.ACTION.覆核, docstatus);
		// 新增 核准額度資料檔 MIS.ELF447n
		lmsService.gfnInsertELF447N(meta, UtilConstants.Cntrdoc.ItemType.額度明細表,
				docstatus, meta.getCaseBrId());
		lmsService.upLoadMIS(meta);
		lmsService.upLnunid(meta);

		// J-105-0228-001 Web e-Loan企金授信簽報書新增私募基金相關建檔與報表。
		if (CreditDocStatusEnum.海外_已核准.getCode().equals(docstatus)) {
			if (!LMSUtil.isClsCase(meta)) {
				lmsService.uploadL902Data(meta, docstatus);
			}
		}

		// 當為核准、且為國內個金案件需更新團貸母戶編號 至L140M03A grpCntrNo 以利動審表取得團貸額度明細表
		if (UtilConstants.Casedoc.DocRslt.承做.equals(docRslt)
				&& LMSUtil.isClsCase(meta)
				&& UtilConstants.Casedoc.DocCode.一般.equals(meta.getDocCode())) {
			// 取得所有批附表mainId
			List<L140M01A> l140m01as = l140m01aDao
					.findL140m01aListByL120m01cMainId(meta.getMainId(),
							UtilConstants.Cntrdoc.ItemType.額度明細表, null);
			List<String> mainIds = new ArrayList<String>();
			for (L140M01A l140m01a : l140m01as) {
				mainIds.add(l140m01a.getMainId());
			}
			lmsService.setL140M03AGrpCntrNo(meta, mainIds);
		}
		// 國內個金才會有上傳優惠房貸
		if (LMSUtil.isClsCase(meta)) {
			// 國內個金簽報書上傳項目
			clsService.upMisByCls(meta);
			List<L140M01A> l140m01as = l140m01aDao
					.findL140m01aListByL120m01cMainId(meta.getMainId(),
							UtilConstants.Cntrdoc.ItemType.額度明細表,
							FlowDocStatusEnum.已核准.getCode());
			for (L140M01A l140m01a : l140m01as) {
				lmsService.upELF431(meta, l140m01a);
			}

		}

	}

	/**
	 * 婉卻
	 * 
	 * @param instance
	 *            流程資料
	 * @throws CapException
	 * @throws FlowException
	 */
	@Transition(node = "決策", value = "to已婉卻")
	public void reject(FlowInstance instance) throws FlowException,
			CapException {
		this.toEndAction(instance, CreditDocStatusEnum.海外_婉卻.getCode(),
				UtilConstants.STAFFJOB.執行覆核主管L4);

		// J-106-0246-002 Web e-Loan授信系統企金額度明細表產品種類為G1政府機構低利優惠放款時，覆核時通知授信行銷處。
		lmsService.notifyByMailForG1To940(instance, "分行已婉卻");
	}

	/**
	 * 核准
	 * 
	 * @param instance
	 *            流程資料
	 * @throws CapException
	 * @throws FlowException
	 */
	@Transition(node = "決策", value = "to核定")
	public void complete(FlowInstance instance) throws FlowException,
			CapException {
		this.toEndAction(instance, CreditDocStatusEnum.海外_已核准.getCode(),
				UtilConstants.STAFFJOB.執行覆核主管L4);

		// J-106-0246-002 Web e-Loan授信系統企金額度明細表產品種類為G1政府機構低利優惠放款時，覆核時通知授信行銷處。
		lmsService.notifyByMailForG1To940(instance, "分行已核准");

	}

	@SuppressWarnings("rawtypes")
	@Override
	public Class getDocStatusEnumClass() {
		return CreditDocStatusEnum.class;
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return L120M01A.class;
	}

	/**
	 * 退回編製中
	 * 
	 * @param instance
	 *            流程資料
	 */
	@Transition(node = "決策", value = "to退回編製中")
	public void back(FlowInstance instance) {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = (L120M01A) metaDao.findByOid(getDomainClass(),
				instanceId);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 如果執行退回的人不是 原本 起案的要刪除目前執行的人授權檔
		if (!user.getUnitNo().equals(meta.getCaseBrId())) {
			L120A01A l120a01a = l120a01aDao.findByAuthUnit(meta.getMainId(),
					user.getUnitNo());
			if (l120a01a != null) {
				l120a01aDao.delete(l120a01a);
			}
		}
		meta.setOwnBrId(meta.getCaseBrId());
		if (UtilConstants.Casedoc.DocType.個金.equals(meta.getDocType())) {
			for (C120M01A c120m01a : c120m01aDao.findByMainId(meta.getMainId())) {
				c120m01a.setModelTyp("");
				c120m01aDao.save(c120m01a);
			}
		}
		l120m01aDao.save(meta);
		// 要砍簽章欄
		lmsService.deleteL120M01F(meta.getMainId(), null, new String[] {
				UtilConstants.STAFFJOB.執行覆核主管L4,
				UtilConstants.STAFFJOB.提會登錄經辦L7,
				UtilConstants.STAFFJOB.提會放行主管L8 });
		// 更改額度明細表文件狀態
		lmsService.resetL140M01A(meta, FlowDocStatusEnum.編製中.getCode());

	}

	/**
	 * 分行待覆核 呈 總行待覆核
	 * 
	 * @param instance
	 *            流程資料
	 */
	@Transition(node = "決策", value = "to母行待覆核")
	public void send(FlowInstance instance) {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = (L120M01A) metaDao.findByOid(getDomainClass(),
				instanceId);
		IBranch branch = branchService.getBranch(meta.getCaseBrId());
		// 下一個編製單位
		String parentBrNo = Util.trim(branch.getParentBrNo());
		String nextOwnbrid = Util.isEmpty(parentBrNo) ? "" : parentBrNo;
		// EFD0053=WARN|覆核人員不可與“經辦人員或其它覆核人員”為同一人|
		lmsService.checkAppraiser(meta, UtilConstants.BRANCHTYPE.分行,
				UtilConstants.STAFFJOB.經辦L1);
		// 設定分行放行時間
		lmsService.setSentTime(meta);
		// 新增授權檔
		lmsService.saveL12A01A(meta, nextOwnbrid,
				DocAuthTypeEnum.VIEW_TRANSFER.getCode());

		// 新增一筆傳送紀錄以供近期已收案件用
		lmsService.saveL000M01A(meta, nextOwnbrid);
		// 新增簽章欄
		lmsService.saveL120M01F(meta, UtilConstants.BRANCHTYPE.分行,
				UtilConstants.STAFFJOB.執行覆核主管L4);
		lmsService.gfnInsertELF447N(meta, UtilConstants.Cntrdoc.ItemType.額度明細表,
				CreditDocStatusEnum.海外_總行待覆核.getCode(), nextOwnbrid);
	}

}