
package com.mega.eloan.lms.mfaloan.bean;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Digits;

import org.apache.wicket.markup.html.form.Check;

import tw.com.iisi.cap.model.GenericBean;

/** 消金簽案利率檔(在 LLDLN502 出表) **/
public class ELF504 extends GenericBean{

	private static final long serialVersionUID = 1L;

	/** 分行別 **/
	@Column(name="ELF504_BR_NO", length=3, columnDefinition="CHAR(3)", nullable=false,unique = true)
	private String elf504_br_no;

	/** 客戶統編 **/
	@Column(name="ELF504_CUST_ID", length=11, columnDefinition="CHAR(11)")
	private String elf504_cust_id;

	/** 額度序號 **/
	@Column(name="ELF504_CNTRNO", length=12, columnDefinition="CHAR(12)", nullable=false,unique = true)
	private String elf504_cntrno;

	/** 放款帳號 **/
	@Column(name="ELF504_LOAN_NO", length=14, columnDefinition="CHAR(14)", nullable=false,unique = true)
	private String elf504_loan_no;

	/** 起期 **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="ELF504_BEG_TERM", columnDefinition="DECIMAL(3,0)",unique = true)
	private Integer elf504_beg_term;

	/** 迄期 **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="ELF504_END_TERM", columnDefinition="DECIMAL(3,0)")
	private Integer elf504_end_term;

	/** 利率代碼 **/
	@Column(name="ELF504_INT_CODE", length=2, columnDefinition="CHAR(2)")
	private String elf504_int_code;

	/** 利率加減碼 **/
	@Digits(integer=2, fraction=6, groups = Check.class)
	@Column(name="ELF504_INT_SPRD", columnDefinition="DECIMAL(8,6)")
	private BigDecimal elf504_int_sprd;

	/** 利率方式 **/
	@Column(name="ELF504_INT_TYPE", length=1, columnDefinition="CHAR(1)")
	private String elf504_int_type;

	/** 利率變動方式 **/
	@Column(name="ELF504_INTCHG_TYPE", length=1, columnDefinition="CHAR(1)")
	private String elf504_intchg_type;

	/** 利率變動週期 **/
	@Column(name="ELF504_INTCHG_CYCL", length=1, columnDefinition="CHAR(1)")
	private String elf504_intchg_cycl;

	/** 資料來源 **/
	@Column(name="ELF504_SCTYPE", length=1, columnDefinition="CHAR(1)")
	private String elf504_sctype;

	/** 文件 ID **/
	@Column(name="ELF504_UNDOCID", length=32, columnDefinition="CHAR(32)")
	private String elf504_undocid;

	/** 產品種類 **/
	@Column(name="ELF504_LOAN_CLASS", length=2, columnDefinition="CHAR(2)")
	private String elf504_loan_class;

	/** 資料修改日期 **/
	@Column(name="ELF504_TMESTAMP", columnDefinition="TIMESTAMP")
	private Timestamp elf504_tmestamp;

	/** 省息遞減註記 **/
	@Column(name="ELF504_DEC_FLAG", length=1, columnDefinition="CHAR(1)")
	private String elf504_dec_flag;

	/** 遞減利率 **/
	@Digits(integer=2, fraction=6, groups = Check.class)
	@Column(name="ELF504_DEC_SPRD", columnDefinition="DECIMAL(8,6)")
	private BigDecimal elf504_dec_sprd;

	/** 自訂利率參考指標 **/
	@Column(name="ELF504_INT_01_PTR", length=3, columnDefinition="CHAR(3)")
	private String elf504_int_01_ptr;

	/** 動審表核准日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELF504_SDATE", columnDefinition="DATE")
	private Date elf504_sdate;

	/** 簽報書核准案號 **/
	@Column(name="ELF504_DOCUMENTNO", length=20, columnDefinition="CHAR(20)")
	private String elf504_documentno;

	/** 取得分行別 **/
	public String getElf504_br_no() {
		return this.elf504_br_no;
	}
	/** 設定分行別 **/
	public void setElf504_br_no(String value) {
		this.elf504_br_no = value;
	}

	/** 取得客戶統編 **/
	public String getElf504_cust_id() {
		return this.elf504_cust_id;
	}
	/** 設定客戶統編 **/
	public void setElf504_cust_id(String value) {
		this.elf504_cust_id = value;
	}

	/** 取得額度序號 **/
	public String getElf504_cntrno() {
		return this.elf504_cntrno;
	}
	/** 設定額度序號 **/
	public void setElf504_cntrno(String value) {
		this.elf504_cntrno = value;
	}

	/** 取得放款帳號 **/
	public String getElf504_loan_no() {
		return this.elf504_loan_no;
	}
	/** 設定放款帳號 **/
	public void setElf504_loan_no(String value) {
		this.elf504_loan_no = value;
	}

	/** 取得起期 **/
	public Integer getElf504_beg_term() {
		return this.elf504_beg_term;
	}
	/** 設定起期 **/
	public void setElf504_beg_term(Integer value) {
		this.elf504_beg_term = value;
	}

	/** 取得迄期 **/
	public Integer getElf504_end_term() {
		return this.elf504_end_term;
	}
	/** 設定迄期 **/
	public void setElf504_end_term(Integer value) {
		this.elf504_end_term = value;
	}

	/** 取得利率代碼 **/
	public String getElf504_int_code() {
		return this.elf504_int_code;
	}
	/** 設定利率代碼 **/
	public void setElf504_int_code(String value) {
		this.elf504_int_code = value;
	}

	/** 取得利率加減碼 **/
	public BigDecimal getElf504_int_sprd() {
		return this.elf504_int_sprd;
	}
	/** 設定利率加減碼 **/
	public void setElf504_int_sprd(BigDecimal value) {
		this.elf504_int_sprd = value;
	}

	/** 取得利率方式 **/
	public String getElf504_int_type() {
		return this.elf504_int_type;
	}
	/** 設定利率方式 **/
	public void setElf504_int_type(String value) {
		this.elf504_int_type = value;
	}

	/** 取得利率變動方式 **/
	public String getElf504_intchg_type() {
		return this.elf504_intchg_type;
	}
	/** 設定利率變動方式 **/
	public void setElf504_intchg_type(String value) {
		this.elf504_intchg_type = value;
	}

	/** 取得利率變動週期 **/
	public String getElf504_intchg_cycl() {
		return this.elf504_intchg_cycl;
	}
	/** 設定利率變動週期 **/
	public void setElf504_intchg_cycl(String value) {
		this.elf504_intchg_cycl = value;
	}

	/** 取得資料來源 **/
	public String getElf504_sctype() {
		return this.elf504_sctype;
	}
	/** 設定資料來源 **/
	public void setElf504_sctype(String value) {
		this.elf504_sctype = value;
	}

	/** 取得文件 ID **/
	public String getElf504_undocid() {
		return this.elf504_undocid;
	}
	/** 設定文件 ID **/
	public void setElf504_undocid(String value) {
		this.elf504_undocid = value;
	}

	/** 取得產品種類 **/
	public String getElf504_loan_class() {
		return this.elf504_loan_class;
	}
	/** 設定產品種類 **/
	public void setElf504_loan_class(String value) {
		this.elf504_loan_class = value;
	}

	/** 取得資料修改日期 **/
	public Timestamp getElf504_tmestamp() {
		return this.elf504_tmestamp;
	}
	/** 設定資料修改日期 **/
	public void setElf504_tmestamp(Timestamp value) {
		this.elf504_tmestamp = value;
	}

	/** 取得省息遞減註記 **/
	public String getElf504_dec_flag() {
		return this.elf504_dec_flag;
	}
	/** 設定省息遞減註記 **/
	public void setElf504_dec_flag(String value) {
		this.elf504_dec_flag = value;
	}

	/** 取得遞減利率 **/
	public BigDecimal getElf504_dec_sprd() {
		return this.elf504_dec_sprd;
	}
	/** 設定遞減利率 **/
	public void setElf504_dec_sprd(BigDecimal value) {
		this.elf504_dec_sprd = value;
	}

	/** 取得自訂利率參考指標 **/
	public String getElf504_int_01_ptr() {
		return this.elf504_int_01_ptr;
	}
	/** 設定自訂利率參考指標 **/
	public void setElf504_int_01_ptr(String value) {
		this.elf504_int_01_ptr = value;
	}

	/** 取得動審表核准日期 **/
	public Date getElf504_sdate() {
		return this.elf504_sdate;
	}
	/** 設定動審表核准日期 **/
	public void setElf504_sdate(Date value) {
		this.elf504_sdate = value;
	}

	/** 取得簽報書核准案號 **/
	public String getElf504_documentno() {
		return this.elf504_documentno;
	}
	/** 設定簽報書核准案號 **/
	public void setElf504_documentno(String value) {
		this.elf504_documentno = value;
	}
}
