/* 
 * lms9020FormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.handler.form;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
//import com.ibm.icu.text.Normalizer.Mode;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.ELRoleEnum;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.CustomerIdCheckUtil;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.NumberService;
import com.mega.eloan.lms.fms.pages.LMS9020M01Page;
import com.mega.eloan.lms.fms.service.LMS9020Service;
import com.mega.eloan.lms.mfaloan.service.MisELF506Service;
import com.mega.eloan.lms.mfaloan.service.MisELF511Service;
import com.mega.eloan.lms.model.L902M01A;
import com.mega.eloan.lms.model.L902S01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 私募基金解除維護FormHandler
 * </pre>
 * 
 * @since 2013/1/21
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/1/21,Miller,new
 *          </ul>
 */
@Scope("request")
@Controller("lms9020formhandler")
public class LMS9020FormHandler extends AbstractFormHandler {

	@Resource
	LMS9020Service service9020;

	@Resource
	BranchService branch;

	@Resource
	MisELF511Service misElf511Service;

	@Resource
	UserInfoService userinfoservice;

	@Resource
	MisELF506Service misELF506Service;

	@Resource
	LMSService lmsService;

	@Resource
	NumberService number;

	@Resource
	ICustomerService customerService;

	/**
	 * 查詢私募基金主要資料
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL902m01a(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		CapAjaxFormResult formStopDetail1 = new CapAjaxFormResult();
		CapAjaxFormResult lms140m01qform = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		L902M01A meta = service9020.findL902m01aByMainId(mainId);
		if (meta == null) {
			meta = new L902M01A();
		}

		formStopDetail1 = DataParse.toResult(meta, DataParse.Delete,
				new String[] { EloanConstants.MAIN_ID, EloanConstants.OID });

		result.set("formStopDetail1", formStopDetail1);

		return result;
	}

	/**
	 * 儲存私募基金明細畫面主要資料
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL902m01a(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String formStopDetail1 = params.getString("formStopDetail1");
		L902M01A model = service9020.findL902m01aByOid(oid);
		if (model == null) {
			Properties prop = MessageBundleScriptCreator
					.getComponentResource(LMS9020M01Page.class);

			// L902M01A.err002=主檔已刪除，無法儲存
			throw new CapMessageException(prop.getProperty("L902M01A.err002"),
					getClass());

		}

		DataParse.toBean(formStopDetail1, model);
		model.setMainId(mainId);

		service9020.save(model);
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(
						UtilConstants.AJAX_RSP_MSG.儲存成功));
		return result;
	}

	/**
	 * 查詢私募基金明細畫面主要資料
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL902s01a(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		CapAjaxFormResult formStopDetail2 = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		L902S01A model = service9020.findL902s01aByOid(oid);
		if (model == null) {
			model = new L902S01A();
		}
		formStopDetail2 = DataParse.toResult(model, DataParse.Delete, "oid",
				"mainId");

		result.set("formStopDetail2", formStopDetail2);
		return result;
	}

	/**
	 * 儲存私募基金明細畫面主要資料
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL902s01a(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String formStopDetail2 = params.getString("formStopDetail2");

		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS9020M01Page.class);

		L902M01A l902m01a = service9020.findL902m01aByMainId(mainId);
		if (l902m01a == null) {

			// L902M01A.err002=主檔已刪除，無法儲存
			throw new CapMessageException(prop.getProperty("L902M01A.err002"),
					getClass());
		}
		String peNo = l902m01a.getPeNo();
		boolean isNew = false;
		L902S01A model = service9020.findL902s01aByOid(oid);
		if (model == null) {
			model = new L902S01A();
			isNew = true;
		}
		DataParse.toBean(formStopDetail2, model);
		model.setMainId(mainId);
		model.setPeNo(peNo);

		// 判斷該ID是否已存在未刪除之集團

		String custId = model.getCustId();
		String dupNo = model.getDupNo();

		if (isNew) {
			List<L902S01A> l902s01as1 = service9020
					.findL902s01aByCustIdAndPeNo(custId, dupNo, peNo);
			if (l902s01as1 != null && !l902s01as1.isEmpty()) {
				// L902M01A.err003=此客戶統編已存在此私募基金，不得新增
				throw new CapMessageException(
						prop.getProperty("L902M01A.err003"), getClass());
			}
		}

		List<L902S01A> l902s01as2 = service9020
				.findL902s01aByCustIdWithoutDelete(custId, dupNo);
		if (l902s01as2 != null && !l902s01as2.isEmpty()) {
			for (L902S01A l902s01a : l902s01as2) {

				StringBuffer errMsg = new StringBuffer("");
				// L902M01A.err004=此客戶統編已存在其他私募基金，不得新增
				errMsg.append(prop.getProperty("L902M01A.err004"));
				errMsg.append("（");
				errMsg.append(prop.getProperty("html.index1"));
				errMsg.append(l902s01a.getPeNo());
				errMsg.append("）");
				throw new CapMessageException(errMsg.toString(), getClass());

			}

		}

		service9020.save(model);
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(
						UtilConstants.AJAX_RSP_MSG.儲存成功));
		return result;
	}

	/**
	 * 依照使用者選擇刪除私募基金所有相關資料
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult startDel(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		L902M01A l902m01a = service9020.findL902m01aByMainId(mainId);
		if (isAuthForDel(mainId)) {
			// 具有刪除權限
			// 進行刪除

			List<L902S01A> l902s01as = service9020
					.findL902s01aByMainIdContainDelete(mainId);
			if (l902s01as != null && !l902s01as.isEmpty()) {
				for (L902S01A l902s01a : l902s01as) {
					if (Util.isEmpty(l902s01a.getDeletedTime())) {
						l902s01a.setUpdater(user.getUserId());
						l902s01a.setUpdateTime(CapDate.getCurrentTimestamp());
						l902s01a.setDeletedTime(CapDate.getCurrentTimestamp());
						service9020.save(l902s01a);
					}

				}

				l902m01a.setUpdater(user.getUserId());
				l902m01a.setUpdateTime(CapDate.getCurrentTimestamp());
				l902m01a.setDeletedTime(CapDate.getCurrentTimestamp());
				service9020.save(l902m01a);

			} else {
				service9020.deleteL902m01a(l902m01a.getOid());
			}

		} else {
			// 不具有刪除權限
			Map<String, String> param = new HashMap<String, String>();
			param.put("txCode", UtilConstants.Mark.HTMLSPACE);
			param.put("methodName", UtilConstants.Mark.HTMLSPACE);
			param.put("authType", UtilConstants.Mark.HTMLSPACE);
			throw new CapMessageException(RespMsgHelper.getMessage(
					"EFD0004", param), getClass());
		}
		// 印出刪除成功訊息!
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(
						UtilConstants.AJAX_RSP_MSG.刪除成功));
		return result;
	}

	/**
	 * 判定是否具有刪除權限
	 * 
	 * @param oid
	 *            文件Oid
	 * @return true: 具有權限, false: 不具有權限
	 */
	private boolean isAuthForDel(String mainId) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 主管
		if (user.getRoles().containsKey(ELRoleEnum.主管.getCode())) {
			return true;
		} else {
			// 非主管
			L902M01A l902m01a = service9020.findL902m01aByMainId(mainId);
			if (user.getUserId().equals(l902m01a.getUpdater())) {
				return true;
			}
		}
		return false;
	}

	/**
	 * 刪除私募基金明細畫面主要資料
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult delL902s01a(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String listOid = Util.trim(params.getString("list"));
		List<L902S01A> list = new ArrayList<L902S01A>();
		if (Util.isNotEmpty(listOid)) {
			String oids[] = listOid.split(",");
			for (String oid : oids) {
				L902S01A model = service9020.findL902s01aByOid(oid);
				if (model != null) {
					if (Util.isNotEmpty(model.getDocumentNo())) {
						// 有分行簽報異動記錄的，不真正刪除
						model.setUpdater(user.getUserId());
						model.setUpdateTime(CapDate.getCurrentTimestamp());
						model.setDeletedTime(CapDate.getCurrentTimestamp());
						service9020.save(model);
					} else {
						// 沒有簽報過
						service9020.deleteL902s01a(oid);
					}
				}
			}
		}

		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(
						UtilConstants.AJAX_RSP_MSG.刪除成功));
		return result;
	}

	/**
	 * 取消刪除私募基金明細畫面主要資料
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult undoDelL902s01a(PageParameters params)
			throws CapException {
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS9020M01Page.class);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString("mainId", ""));
		if (Util.equals(mainId, "")) {
			// L902M01A.err002=主檔已刪除，無法儲存
			throw new CapMessageException(prop.getProperty("L902M01A.err002"),
					getClass());
		}
		String listOid = Util.trim(params.getString("list"));
		List<L902S01A> list = new ArrayList<L902S01A>();
		if (Util.isNotEmpty(listOid)) {
			String oids[] = listOid.split(",");
			for (String oid : oids) {
				L902S01A model = service9020.findL902s01aByOid(oid);

				String custId = model.getCustId();
				String dupNo = model.getDupNo();

				List<L902S01A> l902s01as2 = service9020
						.findL902s01aByCustIdWithoutDelete(custId, dupNo);
				if (l902s01as2 != null && !l902s01as2.isEmpty()) {
					for (L902S01A l902s01a : l902s01as2) {

						if (Util.notEquals(mainId, l902s01a.getMainId())) {

							StringBuffer errMsg = new StringBuffer("");
							// L902M01A.err004=此客戶統編已存在其他私募基金，不得新增
							errMsg.append(l902s01a.getCustId());
							errMsg.append(" ");
							errMsg.append(l902s01a.getCustName());
							errMsg.append(" ");
							errMsg.append(prop.getProperty("L902M01A.err004"));
							errMsg.append("（");
							errMsg.append(prop.getProperty("html.index1"));
							errMsg.append(l902s01a.getPeNo());
							errMsg.append("）");
							throw new CapMessageException(errMsg.toString(),
									getClass());
						}

					}

				}

				if (model != null) {
					model.setDeletedTime(null);
					model.setUpdater(user.getUserId());
					model.setUpdateTime(CapDate.getCurrentTimestamp());
					list.add(model);
				}
			}
		}
		service9020.saveList902s01a(list);
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(
						UtilConstants.AJAX_RSP_MSG.執行成功));
		return result;
	}

	/**
	 * 查詢私募基金明細檔資料並建立私募基金主檔
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult newDoc(PageParameters params)
			throws CapException {
		// 取得當前使用者資料
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS9020M01Page.class);

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String peNo = Util.trim(params.getString("peNo"));
		String peName = Util.trim(params.getString("peName"));
		String mainId = IDGenerator.getUUID();

		// 判斷peNo是否已存在
		L902M01A oldL902m01a = service9020.findL902m01aByPeNo(peNo);
		if (oldL902m01a != null) {
			// L902M01A.err001=私募基金代碼已存在
			throw new CapMessageException(prop.getProperty("L902M01A.err001"),
					getClass());
		}

		// 開始設定私募基金主檔
		L902M01A l902m01a = new L902M01A();
		l902m01a.setMainId(mainId);
		l902m01a.setPeNo(peNo);
		l902m01a.setPeName(peName);
		l902m01a.setCreator(user.getUserId());
		l902m01a.setCreateTime(CapDate.getCurrentTimestamp());

		service9020.save(l902m01a);

		// 開始設定要拋到前端的參數

		result.set(EloanConstants.MAIN_ID, mainId);
		result.set(EloanConstants.OID, Util.trim(l902m01a.getOid()));
		result.set("docURL", "/fms/lms9020m01");

		/*
		 * result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
		 * .getMainMessage(
		 * UtilConstants.AJAX_RSP_MSG.執行成功));
		 */
		return result;
	}

	/**
	 * 取得私募基金代碼最大號+1
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult getMaxNumber(PageParameters params)
			throws CapException {
		// 取得當前使用者資料
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS9020M01Page.class);

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		Integer maxNo = 0;

		// 判斷peNo是否已存在
		L902M01A oldL902m01a = service9020.findL902m01aMaxPeNo();
		if (oldL902m01a == null) {
			// L902M01A.err001=私募基金代碼已存在
			maxNo = 1;
		} else {
			maxNo = Util.parseInt(Util.trim(oldL902m01a.getPeNo()));
			maxNo = maxNo + 1;
		}

		DecimalFormat df = new DecimalFormat("0000");
		String maxNoStr = df.format(maxNo);

		result.set("maxNo", maxNoStr);

		return result;
	}

	/**
	 * 取得私募基金代碼最大號+1
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult applyCustName(PageParameters params)
			throws CapException {
		// 取得當前使用者資料
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS9020M01Page.class);

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String custName = "";
		String noData = "Y";
		Map<String, Object> cust = customerService.findByIdDupNo(custId, dupNo);
		if (cust != null && !cust.isEmpty()) {
			String cname = (String) cust.get("CNAME");
			cname = Util.toSemiCharString(cname);
			String ename = (String) cust.get("ENAME");
			custName = CustomerIdCheckUtil.getName(custId, cname, ename);
			noData = "N";
		}

		result.set("custName", custName);
		result.set("noData", noData);

		return result;
	}

}
