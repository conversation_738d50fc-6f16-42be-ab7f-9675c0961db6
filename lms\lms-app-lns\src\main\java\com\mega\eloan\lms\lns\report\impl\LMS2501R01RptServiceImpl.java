package com.mega.eloan.lms.lns.report.impl;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.inet.report.ReportException;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.base.service.LMS2501Service;
import com.mega.eloan.lms.dc.util.Util;
import com.mega.eloan.lms.lns.service.LMS1401Service;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01J;
import com.mega.eloan.lms.model.L250M01A;
import com.mega.eloan.lms.model.L250M01B;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.report.ReportGenerator;

/**
 * 列印企金模擬動審檢核表
 * 
 * <AUTHOR>
 * 
 */
@Service("lms2501r01rptservice")
public class LMS2501R01RptServiceImpl implements FileDownloadService {

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(LMS2501R01RptServiceImpl.class);

	@Resource
	LMS2501Service lms2501Service;

	@Resource
	UserInfoService userInfoService;

	@Resource
	LMS1401Service lms1401Service;;

	/**
	 * 建立PDF
	 * 
	 * @param params
	 *            params
	 * @return OutputStream OutputStream
	 * @throws Exception
	 */
	public OutputStream generateReport(PageParameters params) throws Exception {

		// 產生企金模擬動審檢核表
		OutputStream outputStream = this.genLMS2501R01(params);

		return outputStream;
	}

	@SuppressWarnings("unchecked")
	public OutputStream genLMS2501R01(PageParameters params) throws Exception {

		Locale locale = LMSUtil.getLocale();
		String oid = params.getString(EloanConstants.MAIN_OID);
		ReportGenerator generator = new ReportGenerator(
				"report/lns/LMS2501R01_" + locale.toString() + ".rpt");

		OutputStream outputStream = null;

		Map<String, String> values = reNewHashMapParams();
		List<Map<String, String>> columnList = new ArrayList<Map<String, String>>();

		L250M01A meta = lms2501Service.findModelByOid(L250M01A.class, oid);
		JSONArray checkList = lms2501Service.getSavedList(meta);

		for (int i = 0; i < checkList.size(); i++) {

			String column05 = "";

			JSONObject group = checkList.getJSONObject(i);
			String groupTitle = group.getString("groupTitle");

			values = reNewHashMapParams();
			values.put("ReportBean.column01", groupTitle);
			columnList.add(values);

			values = reNewHashMapParams();
			String showHead = group.getString("showHead");
			String yesTitle = group.getString("yesTitle");
			String noTitle = group.getString("noTitle");
			String naTitle = group.getString("naTitle");

			column05 = Util.trim(yesTitle + " " + noTitle + " " + naTitle);

			if ("Y".equals(showHead)) {
				values.put("ReportBean.column04", yesTitle + " " + noTitle
						+ " " + naTitle);
			} else {
				values.put("ReportBean.column04", "");
			}
			values.put("ReportBean.column05", column05);

			columnList.add(values);

			JSONArray subItems = group.getJSONArray("subItems");
			for (int j = 0; j < subItems.size(); j++) {
				values = reNewHashMapParams();

				if (j == 0) {

				} else {
					values.put("ReportBean.column05", column05);
				}

				JSONObject subItem = subItems.getJSONObject(j);
				String subTitle = subItem.getString("subTitle");
				String yes = subItem.optString("yes");
				String no = subItem.optString("no");
				String na = subItem.optString("na");
				String checkValue = subItem.optString("checkValue");

				StringBuffer sb = new StringBuffer();

				if ("Y".equals(yes)) {

					int spaceCount = 0;
					spaceCount = (yesTitle.length() * 2 - 2) / 2;

					if ("Y".equals(checkValue)) {
						// 依標題的長度來計算要加多少空白
						sb.append(StringUtils.repeat(" ", spaceCount) + "■"
								+ StringUtils.repeat(" ", spaceCount) + " ");
					} else {
						sb.append(StringUtils.repeat(" ", spaceCount) + "□"
								+ StringUtils.repeat(" ", spaceCount) + " ");
					}
				} else {
					int spaceCount = yesTitle.length() * 2;
					sb.append(StringUtils.repeat(" ", spaceCount) + " ");
				}

				if ("Y".equals(no)) {

					int spaceCount = 0;
					spaceCount = (noTitle.length() * 2 - 2) / 2;
					if ("N".equals(checkValue)) {
						sb.append(StringUtils.repeat(" ", spaceCount) + "■"
								+ StringUtils.repeat(" ", spaceCount) + " ");
					} else {
						sb.append(StringUtils.repeat(" ", spaceCount) + "□"
								+ StringUtils.repeat(" ", spaceCount) + " ");
					}
				} else {
					int spaceCount = noTitle.length() * 2;
					sb.append(StringUtils.repeat(" ", spaceCount) + " ");
				}

				if ("Y".equals(na)) {

					int spaceCount = 0;
					spaceCount = (naTitle.length() * 2 - 2) / 2;

					if ("NA".equals(checkValue)) {
						sb.append(StringUtils.repeat(" ", spaceCount) + "■"
								+ StringUtils.repeat(" ", spaceCount) + " ");
					} else {
						sb.append(StringUtils.repeat(" ", spaceCount) + "□"
								+ StringUtils.repeat(" ", spaceCount) + " ");
					}
				} else {
					int spaceCount = naTitle.length() * 2;
					sb.append(StringUtils.repeat(" ", spaceCount) + " ");
				}
				// sb.append(subTitle);
				if ("Y".equals(showHead)) {
					if (column05.length() < 8) {
						// 是 否 適用
						values.put("ReportBean.column08", sb.toString());
						values.put("ReportBean.column09", subTitle);
					} else {
						// 已完成 待辦理 不適用
						values.put("ReportBean.column02", sb.toString());
						values.put("ReportBean.column03", subTitle);
					}

				} else {
					// 無標題
					values.put("ReportBean.column06", sb.toString());
					values.put("ReportBean.column07", subTitle);
				}

				columnList.add(values);
			}

		}

		Map<String, String> prompts = new HashMap<String, String>();

		List<L250M01B> l250m01bs = (List<L250M01B>) lms2501Service
				.findListByMainId(L250M01B.class, meta.getMainId());

		if (CollectionUtils.isNotEmpty(l250m01bs)) {
			StringBuffer sb = new StringBuffer();
			if ("Y".equals(meta.getAllCanPay())) {
				sb.append("全部動用");
			} else {
				for (L250M01B l250m01b : l250m01bs) {
					String cntrNo = l250m01b.getCntrNo();
					sb.append(cntrNo + ",");
				}
				sb.delete(sb.length() - 1, sb.length());
			}
			prompts.put("cntrNos", sb.toString());

			Map<String, String> idMap = new LinkedHashMap<String, String>();
			for (L250M01B l250m01b : l250m01bs) {
				L140M01A l140m01a = lms1401Service
						.findL140m01aByMainId(l250m01b.getReMainId());
				String l140custIdDup = l140m01a.getCustId() + " "
						+ l140m01a.getDupNo();
				if (!idMap.containsKey(l140custIdDup)) {
					idMap.put(l140custIdDup, l140m01a.getCustName());
				}
				Set<L140M01J> l140m01js = l140m01a.getL140m01j();
				for (L140M01J l140m01j : l140m01js) {
					String custIdDup = l140m01j.getCustId() + " "
							+ l140m01j.getDupNo();
					if (!idMap.containsKey(custIdDup)) {
						idMap.put(custIdDup, l140m01j.getCustName());
					}
				}
			}
			if (idMap.size() > 0) {

				StringBuffer sbId = new StringBuffer();
				StringBuffer sbName = new StringBuffer();

				String mainId = "";
				String mainName = "";
				for (Entry<String, String> entry : idMap.entrySet()) {
					if (entry.getKey().equals(
							meta.getCustId() + " " + meta.getDupNo())) {
						mainId = entry.getKey();
						mainName = entry.getValue();
					} else {
						sbId.append(entry.getKey() + ",");
						sbName.append(entry.getValue() + ",");
					}
				}

				if (sbId.length() > 0) {
					sbId.delete(sbId.length() - 1, sbId.length());
					sbName.delete(sbName.length() - 1, sbName.length());
				}
				if (mainId.equals("")) {
					prompts.put("custId", sbId.toString());
					prompts.put("custName", sbName.toString());
				} else {
					prompts.put("custId", mainId
							+ (sbId.length() > 0 ? "," + sbId.toString() : ""));
					prompts.put("custName", mainName
							+ (sbName.length() > 0 ? "," + sbName.toString()
									: ""));
				}

			}
		}

		prompts.put("caseDate",
				CapDate.formatDate(meta.getCaseDate(), "yyyy-MM-dd"));
		prompts.put("ownBrId", meta.getOwnBrId());
		prompts.put("caseNo", meta.getCaseNo());
		prompts.put("managerId", this.getUserName(meta.getManagerId()));
		prompts.put("bossId", this.getUserName(meta.getBossId()));
		prompts.put("reCheckId", this.getUserName(meta.getReCheckId()));
		prompts.put("apprId", this.getUserName(meta.getApprId()));

		//J-108-0217_10702_B1002 企金界接IVR語音系統查詢及檢核
		String ivrString="錄音檔：<br>";
		boolean ivrFlag=false;
		for(L250M01B l250m01b:l250m01bs){
			if(!Util.isEmpty(l250m01b.getIVRFlag())){
				String reMainId=l250m01b.getReMainId();
				L140M01A l140m01a = lms1401Service
					.findL140m01aByMainId(reMainId);
				String custName = l140m01a.getCustName();
				ivrString += Util.trim(custName+" "+l250m01b.getIVRFlag())+"<br>";
				ivrFlag =true;
			}
		}
		if(ivrFlag){
			prompts.put("ivrFlag", ivrString);
		}
		
		generator.setVariableData(prompts);

		generator.setRowsData(columnList);

		outputStream = generator.generateReport();

		return outputStream;

	}

	private String getUserName(String userId) {
		if (Util.isEmpty(userId)) {
			return "";
		}

		String result = userInfoService.getUserName(userId);
		if (Util.isEmpty(result)) {
			return userId;
		} else {
			return result;
		}
	}

	@Override
	public byte[] getContent(PageParameters params) throws CapException,
			FileNotFoundException, ReportException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
			baos = (ByteArrayOutputStream) this.generateReport(params);
			return baos.toByteArray();
		} finally {
			if (baos != null) {
				baos.close();
			}

		}
	}

	/**
	 * 初始化map 資料，將所有的rportBean的資料初使化，避免少了而產生exception
	 * 
	 * @return
	 */
	private Map<String, String> reNewHashMapParams() {
		Map<String, String> values = new HashMap<String, String>();
		for (int i = 1; i <= 60; i++) {
			values.put("ReportBean.column" + String.format("%02d", i), "");
		}
		return values;
	}

}
