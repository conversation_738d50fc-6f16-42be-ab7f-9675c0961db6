
package com.mega.eloan.lms.cls.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;

/**
 * <pre>
 * 中小信保整批申請 - 編製中
 * </pre>
 */
@Controller
@RequestMapping("/cls/cls3501v01")
public class CLS3501V01Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		setGridViewStatus(CreditDocStatusEnum.海外_編製中);

		// 加上Button
		// 主管跟經辦都會出現的按鈕
		addToButtonPanel(model, LmsButtonEnum.View, LmsButtonEnum.Add,
				LmsButtonEnum.Delete, LmsButtonEnum.Filter);

		renderJsI18N(CLS3501V01Page.class);
		renderJsI18N(CLS3501M01Page.class);

	}

}
