<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:wicket="http://wicket.apache.org/">
<body>
<wicket:panel>
	<style type="text/css">
	</style>
	<script type="text/javascript" src="pagejs/cls/CLS1201S27Panel.js?ver=20220104"></script>
	<style>
		.content{
			margin:5px 0px;
		}
	</style>
	<form id="CLS1201S27Form">
		<table class="tb2" width='95%'>
			<tbody>
				<tr>
					<td class="hd2" colspan='4'><wicket:message key="brmp.001">決策系統審核結果</wicket:message></td>
				</tr>
				<tr>
					<td class="hd2" colspan="4"><wicket:message key="brmp.009">基本資料</wicket:message></td>
				</tr>
				<tr>
					<td class="hd1"><wicket:message key="brmp.048">貸款人姓名</wicket:message></td>
					<td><span wicket:id="applyCustName"></span></td>
					<td class="hd1"><wicket:message key="brmp.049">職業</wicket:message></td>
					<td><span wicket:id="compJobTitle"></span></td>
				</tr>
				<tr>
					<td class="hd1"><wicket:message key="brmp.050">身分證字號</wicket:message></td>
					<td><span wicket:id="applyCustId"></span></td>
					<td class="hd1"><wicket:message key="brmp.051">年資</wicket:message></td>
					<td><span wicket:id="seniority"></span></td>
				</tr>
				<tr>
					<td class="hd1"><wicket:message key="brmp.028">信用評等</wicket:message></td>
					<td><span wicket:id="modelKindGrade3"></span></td>
					<td class="hd1"><wicket:message key="brmp.052">平均月收入</wicket:message></td>
					<td><span wicket:id="avgMonthIncome"></span></td>
				</tr>
				<tr>
					<td class="hd1"><wicket:message key="brmp.053">申貸用途</wicket:message></td>
					<td><span wicket:id="creditLoanPurpose"></span></td>
					<td class="hd1"><wicket:message key="brmp.038">負債比</wicket:message></td>
					<td><span wicket:id="drate"></span></td>
				</tr>
				<tr>
					<td class="hd1"><wicket:message key="brmp.061">使用專案</wicket:message></td>
					<!--<td><select name="usePlan" id="usePlan" wicket:id="usePlan" disabled="disabled"/></td>-->
					<td><span wicket:id="usePlan"></span></td>
					<td class="hd1"></td>
					<td></td>
				</tr>
				<tr>
					<td class="hd2" colspan="4">
						<wicket:message key="brmp.creditCondition">授信條件</wicket:message>
					</td>
				</tr>
				<tr>
					<td rowspan="3" class="hd1">
						<wicket:message key="brmp.loanQuota">貸款額度</wicket:message>
						<br/>
						<button type="button" id="updateLaonQuota">
		                    <span class="text-only">修改</span>
		                </button>
						<button type="button" id="docLogLaonQuota" class="forview">
		                    <span class="text-only"><wicket:message key="button.docLogLaonQuota">異動歷程</wicket:message></span>
		                </button>
					</td>
					<td>
						<wicket:message key="brmp.applyQuota">申貸額度</wicket:message>：
						<span wicket:id="applyQuota"></span>
					</td>
					<td colspan="2">
						<wicket:message key="brmp.systemComputedQuota">系統計算額度</wicket:message>：
						<span wicket:id="systemComputedQuota"></span>
					</td>
				</tr>
				<tr>
					<td>
						<wicket:message key="brmp.aoModifyAmount">AO修改額度</wicket:message>：
						<span id="aoModifyAmount" wicket:id="aoModifyAmount"></span>
					</td>
					<td colspan="2">
						<wicket:message key="brmp.remark">備註</wicket:message>：
						<span id="aoUpdAmountRemark" wicket:id="aoUpdAmountRemark"></span>
					</td>
				</tr>
				<tr>
					<td>
						<wicket:message key="brmp.rvModifyAmount">審查修改額度</wicket:message>：
						<span id="rvModifyAmount" wicket:id="rvModifyAmount"></span>
					</td>
					<td colspan="2">
						<wicket:message key="brmp.remark">備註</wicket:message>：
						<span id="rvUpdAmountRemark" wicket:id="rvUpdAmountRemark"></span>
					</td>
				</tr>
				<tr>
					<td rowspan="3" class="hd1">
						<wicket:message key="brmp.loanPeriod">貸款期間</wicket:message>
						<br/>
						<button type="button" id="updateLoanPeriod">
		                    <span class="text-only">修改</span>
		                </button>
						<button type="button" id="docLogLoanPeriod" class="forview">
		                    <span class="text-only"><wicket:message key="button.docLogLoanPeriod">異動歷程</wicket:message></span>
		                </button>
					</td>
					<td>
						<wicket:message key="brmp.applyPeriod">申貸期間</wicket:message>：
						<span wicket:id="applyPeriod"></span>
					</td>
					<td colspan="2">
						<wicket:message key="brmp.systemComputedPeriod">系統計算期間</wicket:message>：
						<span wicket:id="systemComputedPeriod"></span>
					</td>
				</tr>
				<tr>
					<td>
						<wicket:message key="brmp.aoModifyPeriod">AO修改期間</wicket:message>：
						<span id="aoModifyPeriod" wicket:id="aoModifyPeriod"></span>
					</td>
					<td colspan="2">
						<wicket:message key="brmp.remark">備註</wicket:message>：
						<span id="aoUpdPeriodRemark" wicket:id="aoUpdPeriodRemark"></span>
					</td>
				</tr>
				<tr>
					<td>
						<wicket:message key="brmp.rvModifyPeriod">審查修改期間</wicket:message>：
						<span id="rvModifyPeriod" wicket:id="rvModifyPeriod"></span>
					</td>
					<td colspan="2">
						<wicket:message key="brmp.remark">備註</wicket:message>：
						<span id="rvUpdPeriodRemark" wicket:id="rvUpdPeriodRemark"></span>
					</td>
				</tr>
				<tr>
					<td rowspan="3" class="hd1">
						<wicket:message key="brmp.loanRate">貸款利率</wicket:message>
						<br/>
						<button type="button" id="updateLoanRate">
		                    <span class="text-only">修改</span>
		                </button>
						<button type="button" id="docLogLoanRate" class="forview">
		                    <span class="text-only"><wicket:message key="button.docLogLoanRate">異動歷程</wicket:message></span>
		                </button>
					</td>
					<td colspan="3">
						<wicket:message key="brmp.systemComputedRate">系統計算利率</wicket:message>：
						<span wicket:id="systemComputedRate"></span>
					</td>
				</tr>
				<tr>
					<td colspan="3">
						<p class="content">
							<wicket:message key="brmp.aoModifyRate">AO修改利率</wicket:message>：
							<span id="aoModifyRate" wicket:id="aoModifyRate"></span>
						</p>
						<p class="content">
							<wicket:message key="brmp.remark">備註</wicket:message>：
							<span id="aoUpdRateRemark" wicket:id="aoUpdRateRemark"></span>
						</p>
					</td>
				</tr>
				<tr>
					<td colspan="3">
						<p class="content">
							<wicket:message key="brmp.rvModifyRate">審查修改利率</wicket:message>：
							<span id="rvModifyRate" wicket:id="rvModifyRate"></span>
						</p>
						<p class="content">
							<wicket:message key="brmp.remark">備註</wicket:message>：
							<span id="rvUpdRateRemark" wicket:id="rvUpdRateRemark"></span>
						</p>
					</td>
				</tr>
				<tr>
					<td rowspan="3" class="hd1">
						<wicket:message key="brmp.loanFee">貸款費用</wicket:message>
						<br/>
						<button type="button" id="updateLoanFee">
		                    <span class="text-only">修改</span>
		                </button>
						<button type="button" id="docLogLoanFee" class="forview">
		                    <span class="text-only"><wicket:message key="button.docLogLoanFee">異動歷程</wicket:message></span>
		                </button>
					</td>
					<td colspan="3">
						<wicket:message key="brmp.systemComputedFee">系統計算費用</wicket:message>：
						<wicket:message key="brmp.015">開辦費</wicket:message>
						<span wicket:id="organizationCost"></span>
						/
						<wicket:message key="brmp.016">信用查詢費</wicket:message>
						<span wicket:id="creditCheckFee"></span>
					</td>
				</tr>
				<tr>
					<td colspan="3">
						<p class="content">
							<wicket:message key="brmp.aoModifyFee">AO修改費用</wicket:message>：
							<span id="aoModifyFee" wicket:id="aoModifyFee"></span>
						</p>
						<p class="content">
							<wicket:message key="brmp.remark">備註</wicket:message>：
							<span id="aoUpdFeeRemark" wicket:id="aoUpdFeeRemark"></span>
						</p>
					</td>
				</tr>
				<tr>
					<td colspan="3">
						<p class="content">
							<wicket:message key="brmp.rvModifyFee">審查修改費用</wicket:message>：
							<span id="rvModifyFee" wicket:id="rvModifyFee"></span>
						</p>
						<p class="content">
							<wicket:message key="brmp.remark">備註</wicket:message>：
							<span id="rvUpdFeeRemark" wicket:id="rvUpdFeeRemark"></span>
						</p>
					</td>
				</tr>
				<tr>
					<td class="hd1"><wicket:message key="brmp.017">提前清償違約金</wicket:message></td>
					<td><span wicket:id="advancedRedemptionDesc"></span></td>
					<td class="hd1"><wicket:message key="brmp.gracePeriod">寬限期</wicket:message></td>
					<td><span wicket:id="gracePeriod"></span>無</td>
				</tr>
			</tbody>
			<tbody>
				<tr>
					<td class="hd2" colspan="4"><wicket:message key="brmp.018">審核規則</wicket:message></td>
				</tr>
				<tr>
					<td class="hd2"><wicket:message key="brmp.019">規則代碼</wicket:message></td>
					<td class="hd2" colspan="2"><wicket:message key="brmp.020">規則內容(補充描述)</wicket:message></td>
					<td class="hd2"><wicket:message key="brmp.021">審核結果</wicket:message></td>
				</tr>
			</tbody>
			<tbody wicket:id="_grid_policyResult">
				<tr>
					<td><span wicket:id="policyCode"></span></td>
					<td colspan="2">
						<span wicket:id="policyDescription"></span>
						<br>
						<span class="color-red" wicket:id="subDescription"></span>
					</td>
					<td><span wicket:id="status"></span></td>
				</tr>
			</tbody>
			<tbody>
				<tr>
					<td colspan="4">
						<wicket:message key="brmp.060">備註說明 (檢核項目如有未符合請於此處說明) 限300字</wicket:message>
						<br>
						<textarea name="memoNote1" id="memoNote1" rows="5" cols="100" style="width:98%" maxlengthC="300"></textarea>
					</td>
				</tr>
			</tbody>
			<tbody>
				<tr>
					<td><wicket:message key="brmp.022">人頭戶強指標警示門檻</wicket:message></td>
					<td><span wicket:id="headAccountStrongFactor"></span></td>
					<td colspan="2" rowspan="3">
						<wicket:message key="brmp.023">態樣說明:</wicket:message>
						<br>
						<textarea wicket:id="indicatorReaon" rows="5" cols="40" style="width:96%" maxlengthC="300"></textarea>
					</td>
				</tr>
				<tr>
					<td><wicket:message key="brmp.024">人頭戶弱指標警示門檻</wicket:message></td>
					<td><span wicket:id="headAccountWeakFactor"></span></td>
				</tr>
				<tr>
					<td><wicket:message key="brmp.025">人頭戶改授權外門檻</wicket:message></td>
					<td><span wicket:id="headAuthorizeFactor"></span></td>
				</tr>
				<tr>
					<td colspan="4">
						<span wicket:id="ph_policyDesc"></span>
					</td>
				</tr>
			</tbody>

			<tbody>
				<tr>
					<td class="hd2" colspan="4"><wicket:message key="brmp.026">系統審核參數</wicket:message></td>
				</tr>
				<tr>
					<td class="hd2" colspan="4"><wicket:message key="brmp.056">利率加減碼判斷</wicket:message></td>
				</tr>
				<tr>
					<td class="hd1"><wicket:message key="brmp.027">本行薪轉戶</wicket:message></td>
					<td><span wicket:id="ptaFlag"></span></td>
					<td class="hd1">
						<span style="display:none;"> <wicket:message key="brmp.030">信用卡循環與預借現金餘額合計</wicket:message> </span>
					</td>
					<td>
						<span wicket:id="revolvingAmt" style="display:none;"></span>
					</td>
				</tr>
				<tr>
					<td class="hd1"><wicket:message key="brmp.029">既有房貸戶</wicket:message></td>
					<td><span wicket:id="mortgageUserFlag"></span></td>
					<td class="hd1"><wicket:message key="brmp.032">線上申請註記</wicket:message></td>
					<td><span wicket:id="applyOnlineFlag"></span></td>
				</tr>
				<tr>
					<td class="hd1"><wicket:message key="brmp.031">卡友滿一年</wicket:message></td>
					<td><span wicket:id="creditCardUserFlag"></span></td>
					<td class="hd1"><wicket:message key="brmp.034">利率分段數</wicket:message></td>
					<td><span wicket:id="stageCount"></span></td>
				</tr>
				<tr>
					<td class="hd1"><wicket:message key="brmp.033">近一年AUM >= 100萬</wicket:message></td>
					<td><span wicket:id="aumAmt"></span></td>
					<td class="hd1"><wicket:message key="brmp.036">是否綁約</wicket:message></td>
					<td><span wicket:id="advanceRedemptionFlag"></span></td>
				</tr>
				<tr>
					<td class="hd1"><wicket:message key="brmp.035">海悅聯名卡卡友</wicket:message></td>
					<td><span wicket:id="coBrandedCardUserType"></span></td>
					<td class="hd1"><wicket:message key="brmp.037">ESG分數</wicket:message></td>
					<td><span wicket:id="esgScore"></span></td>
				</tr>
				<tr>
					<td class="hd2" colspan="4"><wicket:message key="brmp.057">額度金額判斷 (上限計算未含本次申貸金額)</wicket:message></td>
				</tr>
				<tr>
					<td class="hd1"><wicket:message key="brmp.046">本行貸款總計</wicket:message></td>
					<td><span wicket:id="megaTotalLoanAmt"></span></td>
					<td class="hd1"><wicket:message key="brmp.047">全體金融機構貸款總計</wicket:message></td>
					<td><span wicket:id="allFinInsTotalLoanAmt"></span></td>
				</tr>
				<tr>
					<td class="hd1"><wicket:message key="brmp.039">DBR額度上限</wicket:message></td>
					<td><span wicket:id="dbrLimitAmt"></span></td>
					<td class="hd1"><wicket:message key="brmp.041">客群額度上限</wicket:message></td>
					<td><span wicket:id="termGroupLimitAmt"></span></td>
				</tr>
				<tr>
					
					
					<td style="display:none;" class="hd1"><wicket:message key="brmp.043">本行信用貸款上限</wicket:message></td>
					<td style="display:none;"><span wicket:id="megaUnsecuredLoanLimitAmt"></span></td>
				</tr>
				<tr>
					<td class="hd1"><wicket:message key="brmp.042">本行消費性貸款上限(營業單位授權)</wicket:message></td>
					<td><span wicket:id="megaConsumerLoanLimitAmt"></span></td>
					<td class="hd1"><wicket:message key="brmp.045">銀行法及金控法利害關係人下消貸額度上限</wicket:message></td>
					<td><span wicket:id="bankStakeholderLimitAmt"></span></td>
				</tr>
				<tr>
					<td class="hd1"><wicket:message key="brmp.058">代償金額</wicket:message></td>
					<td><span wicket:id="compensationAmt"></span></td>
					<td class="hd1"><wicket:message key="brmp.059">其他調整</wicket:message></td>
					<td><span wicket:id="otherAdjustAmt"></span></td>
				</tr>
				<tr>
					<td class="hd1"><wicket:message key="brmp.044">月付金額度上限(適用行員貸款)</wicket:message></td>
					<td><span wicket:id="monthlyPaymentLimitAmt"></span></td>
					<td class="hd1"><wicket:message key="brmp.040">負債比額度上限</wicket:message></td>
					<td><span wicket:id="drateLimitAmt"></span></td>
				</tr>
			</tbody>
			<tbody>
				<tr>
					<td class="hd2" colspan="4"><wicket:message key="brmp.002">系統狀態</wicket:message></td>
				</tr>
				<tr>
					<td class="hd1"  width="20%"><wicket:message key="brmp.003">結果狀態</wicket:message></td>
					<td width="30%"><span wicket:id="stat"></span></td>
					<td class="hd1"  width="20%"><wicket:message key="brmp.004">決策識別碼</wicket:message></td>
					<td width="30%"><span wicket:id="systemUuid"></span></td>
				</tr>
				<tr>
					<td class="hd1"><wicket:message key="brmp.005">鍵值</wicket:message></td>
					<td><span wicket:id="uuid"></span></td>
					<td class="hd1"><wicket:message key="brmp.006">版本資訊</wicket:message></td>
					<td><span wicket:id="packageVersion"></span></td>
				</tr>
				<tr>
					<td class="hd1"><wicket:message key="brmp.007">錯誤代碼</wicket:message></td>
					<td><span wicket:id="errorCode"></span></td>
					<td class="hd1"><wicket:message key="brmp.008">錯誤訊息</wicket:message></td>
					<td><span wicket:id="errorMsg"></span></td>
				</tr>
			</tbody>
		</table>
	</form>
	
	<div id="openBox_creidtConditionModifying" style="display: none;">
		<form id="creidtConditionModifyingForm">
			<table class="tb2">
				<tbody>
					<tr id="tr_quotaModifying" class="tr_quotaModifying">
						<td class="hd2" width="50%"><wicket:message key="quotaModifying">修改額度</wicket:message></td>
						<td width="50%"><input type="text" id="quotaModifying" name="quotaModifying" size="18" maxlength="19" integer="17" class="numeric required" /></td>
					</tr>
					<tr id="tr_termModifying" class="tr_termModifying">
						<td class="hd2"><wicket:message key="termModifying">修改期間</wicket:message></td>
						<td>
							<input type="text" id="yearModifying" name="yearModifying" class="max number required" maxlength="3" size="3" />&nbsp;年
							<input type="text" id="monthModifying" name="monthModifying" class="max number required" maxlength="2" size="2" />&nbsp;月
						</td>
					</tr>
					<tr id="tr_feeCode" class="tr_feeModifying">
						<td class="hd2" width="40%"><wicket:message key="feeCode">費用代碼</wicket:message></td>
						<td width="60%"><select id="feeCode" name="feeCode" combokey="paperless_feeNo" comboType="4" /></td>
					</tr>
					<tr id="tr_feeAmount" class="tr_feeModifying">
						<td class="hd2" width="40%"><wicket:message key="feeAmount">費用金額</wicket:message>&nbsp;TWD</td>
						<td width="60%"><input type="text" id="feeAmount" name="feeAmount" size="18" maxlength="19" integer="13" class="numeric required" />元</td>
					</tr>
					<tr>
						<td class="hd2"><wicket:message key="brmp.remark">備註</wicket:message></td>
						<td><textarea name="remarkModifying" id="remarkModifying" rows="8" cols="20" maxlengthC="100" maxlength="300" class="required"></textarea></td>
					</tr>
				</tbody>
			</table>
		</form>
	</div>
	<!--<script type="text/javascript" src="pagejs/cls/CLS1201S26Panel.js?ver=20210118"></script>-->
	
	<!---登錄利率 begien--->
	<div id="L140S02CBox" style="display:none;">
	    <form action="" id="L140S02CForm" name="L140S02CForm">
	        <table class="tb2" width="95%" border="0" cellspacing="0" cellpadding="0">
	            <tbody>
	                <div>
	                    <tr>
	                        <td class="hd2">
	                            <span><strong><wicket:message key="page5.050">注意事項：</wicket:message></strong>
	                                <br/>
	                                <span><strong><wicket:message key="page5.074">選擇利率基礎代碼後，系統會帶出其基準利率。登錄加減碼後，系統會自動計算</wicket:message><span><wicket:message key="page5.075">目前為</wicket:message>『』</span>%<wicket:message key="page5.076">之值</wicket:message>。
	                                        <br/>
	                                        <wicket:message key="page5.201">若計息方式為按月計息，僅能勾選第一段或其他，且無起迄期間</wicket:message>
	                                    </strong>
	                                </span>
	                            </span>
	                        </td>
	                    </tr>
	                </div>
	                <tr id="payNumOldTr">
	                    <td class="hd2">
	                        <wicket:message key="L140S02C.preDscr">前期利率說明</wicket:message>：(<wicket:message key="L140S02C.payNum">已還期數</wicket:message>
	                        <span id="payNum" class="field text-red"></span>
	                        <wicket:message key="page5.054">期</wicket:message>)
	                        <br/>
	                        <!--<span id="preDscr" name="preDscr" class="field"></span>-->	
							<!--由原本的前期利率內容不可修改調整成可編輯模式-->								
							<textarea id="preDscr" name="preDscr" style="width: 650px; height: 50px;" maxlengthc="1024" readonly="readonly" data-zmemo="在 double-click 已還期數N期 後,即可改成「前次簽案」的值"></textarea>									
	                    </td>
	                </tr>
	                <tr>
	                    <td class="hd2">
	                        <wicket:message key="L140S02C.submitRate">首段期間適用利率</wicket:message>
	                        <span id="submitRate" class="field"></span>％
	                        <br/>
	                        <!--<span class="text-red">1.如果第一段有值則由第一段的值直接帶入
	                        <br/>
	                        2.如果第一段無值而是用其他說明時則在按確定時要求輸入一個值存入。
	                        </span>-->
	                    </td>
	                </tr>
	                <!--計息方式-->
	                <tr>
	                    <td>
	                        <span>■ <wicket:message key="L140S02C.intWay">計息方式</wicket:message>:</span>
	                        <select id="intWay" name="intWay" itemType="L140S02C_intWay" class="required" />
	                    </td>
	                </tr>
	                <!--收息方式-->
	                <tr>
	                    <td>
	                        <span>■ <wicket:message key="L140S02C.rIntWay">收息方式</wicket:message>:</span>
	                        <select id="rIntWay" name="rIntWay" itemType="L140S02C_rIntWay" class="required" />
	                        <span id="taxRateSpan"><wicket:message key="page5.197">扣稅負擔值</wicket:message>
	                            <input type="text" id="taxRate" name="taxRate" maxlength="8" integer="1" fraction="3" class="required numeric" size="6"/>
	                        </span>
	                    </td>
	                </tr>
	                <tr>
	                    <td class="hd2">
	                        <wicket:message key="page5.055">第一段</wicket:message>：
	                        <input type="checkbox" name="isUseBox_1" id="isUseBox_1" value="Y" />
	                        <wicket:message key="page5.065">第</wicket:message>
	                        <input type="text" id="bgnNum_1" name="bgnNum_1" size="3" maxlength="3" class="numeric" readonly="readonly"/>
	                        <wicket:message key="page5.054">期</wicket:message>~<wicket:message key="page5.065">第</wicket:message>
	                        <input type="text" id="endNum_1" name="endNum_1" size="3" maxlength="3" class="numeric" readonly="readonly"/>
	                        <wicket:message key="page5.054">期</wicket:message>
	                        <span><wicket:message key="page5.208">(若為按月計息戶，期數內容不用登錄!)</wicket:message></span>
	                    </td>
	                </tr>
	                <tr class="isUseBox_1" style="display:none">
	                    <td>
	                        <div id="isTempTr_1" />
	                    </td>
	                </tr>
	                <tr>
	                    <td class="hd2">
	                        <wicket:message key="page5.056">第二段</wicket:message>：
	                        <input type="checkbox" name="isUseBox_2" id="isUseBox_2" value="Y" class="notIsOne"/>
	                        <wicket:message key="page5.065">第</wicket:message>
	                        <input type="text" id="bgnNum_2" name="bgnNum_2" size="3" maxlength="3" class="numeric notIsOneNum" readonly="readonly"/>
	                        <wicket:message key="page5.054">期</wicket:message>~<wicket:message key="page5.065">第</wicket:message>
	                        <input type="text" id="endNum_2" name="endNum_2" size="3" maxlength="3" class="numeric notIsOneNum" readonly="readonly"/>
	                        <wicket:message key="page5.054">期</wicket:message>
	                    </td>
	                </tr>
	                <tr class="isUseBox_2" style="display:none">
	                    <td>
	                        <div id="isTempTr_2" />
	                    </td>
	                </tr>
	                <tr>
	                    <td class="hd2">
	                        <wicket:message key="page5.057">第三段</wicket:message>：
	                        <input type="checkbox" name="isUseBox_3" id="isUseBox_3" value="Y" class="notIsOne"/>
	                        <wicket:message key="page5.065">第</wicket:message>
	                        <input type="text" id="bgnNum_3" name="bgnNum_3" size="3" maxlength="3" class="numeric notIsOneNum" readonly="readonly"/>
	                        <wicket:message key="page5.054">期</wicket:message>~<wicket:message key="page5.065">第</wicket:message>
	                        <input type="text" id="endNum_3" name="endNum_3" size="3" maxlength="3" class="numeric notIsOneNum" readonly="readonly"/>
	                        <wicket:message key="page5.054">期</wicket:message>
	                    </td>
	                </tr>
	                <tr class="isUseBox_3" style="display:none">
	                    <td>
	                        <div id="isTempTr_3" />
	                    </td>
	                </tr>
	                <tr>
	                    <td class="hd2">
	                        <wicket:message key="page5.058">第四段</wicket:message>：
	                        <input type="checkbox" name="isUseBox_4" id="isUseBox_4" value="Y" class="notIsOne"/>
	                        <wicket:message key="page5.065">第</wicket:message>
	                        <input type="text" id="bgnNum_4" name="bgnNum_4" size="3" maxlength="3" class="numeric notIsOneNum" readonly="readonly"/>
	                        <wicket:message key="page5.054">期</wicket:message>~<wicket:message key="page5.065">第</wicket:message>
	                        <input type="text" id="endNum_4" name="endNum_4" size="3" maxlength="3" class="numeric notIsOneNum" readonly="readonly"/>
	                        <wicket:message key="page5.054">期</wicket:message>
	                    </td>
	                </tr>
	                <tr class="isUseBox_4" style="display:none">
	                    <td>
	                        <div id="isTempTr_4" />
	                    </td>
	                </tr>
	                <tr>
	                    <td class="hd2">
	                        <wicket:message key="page5.059">第五段</wicket:message>：
	                        <input type="checkbox" name="isUseBox_5" id="isUseBox_5" value="Y" class="notIsOne"/>
	                        <wicket:message key="page5.065">第</wicket:message>
	                        <input type="text" id="bgnNum_5" name="bgnNum_5" size="3" maxlength="3" class="numeric notIsOneNum" readonly="readonly"/>
	                        <wicket:message key="page5.054">期</wicket:message>~<wicket:message key="page5.065">第</wicket:message>
	                        <input type="text" id="endNum_5" name="endNum_5" size="3" maxlength="3" class="numeric notIsOneNum" readonly="readonly"/>
	                        <wicket:message key="page5.054">期</wicket:message>
	                    </td>
	                </tr>
	                <tr class="isUseBox_5" style="display:none">
	                    <td>
	                        <div id="isTempTr_5" />
	                    </td>
	                </tr>
	                <tr>
	                    <td class="hd2">
	                        <wicket:message key="page5.060">第六段</wicket:message>：
	                        <input type="checkbox" name="isUseBox_6" id="isUseBox_6" value="Y" class="notIsOne"/>
	                        <wicket:message key="page5.065">第</wicket:message>
	                        <input type="text" id="bgnNum_6" name="bgnNum_6" size="3" maxlength="3" class="numeric notIsOneNum" readonly="readonly"/>
	                        <wicket:message key="page5.054">期</wicket:message>~<wicket:message key="page5.065">第</wicket:message>
	                        <input type="text" id="endNum_6" name="endNum_6" size="3" maxlength="3" class="numeric notIsOneNum" readonly="readonly"/>
	                        <wicket:message key="page5.054">期</wicket:message>
	                    </td>
	                </tr>
	                <tr class="isUseBox_6" style="display:none">
	                    <td>
	                        <div id="isTempTr_6" />
	                    </td>
	                </tr>
	                <tr>
	                    <td class="hd2">
	                        <wicket:message key="page5.061">第七段</wicket:message>：
	                        <input type="checkbox" name="isUseBox_7" id="isUseBox_7" value="Y" class="notIsOne"/>
	                        <wicket:message key="page5.065">第</wicket:message>
	                        <input type="text" id="bgnNum_7" name="bgnNum_7" size="3" maxlength="3" class="numeric notIsOneNum" readonly="readonly"/>
	                        <wicket:message key="page5.054">期</wicket:message>~<wicket:message key="page5.065">第</wicket:message>
	                        <input type="text" id="endNum_7" name="endNum_7" size="3" maxlength="3" class="numeric notIsOneNum" readonly="readonly"/>
	                        <wicket:message key="page5.054">期</wicket:message>
	                    </td>
	                </tr>
	                <tr class="isUseBox_7" style="display:none">
	                    <td>
	                        <div id="isTempTr_7" />
	                    </td>
	                </tr>
	                <tr>
	                    <td class="hd2">
	                        <wicket:message key="page5.062">第八段</wicket:message>：
	                        <input type="checkbox" name="isUseBox_8" id="isUseBox_8" value="Y" class="notIsOne"/>
	                        <wicket:message key="page5.065">第</wicket:message>
	                        <input type="text" id="bgnNum_8" name="bgnNum_8" size="3" maxlength="3" class="numeric notIsOneNum" readonly="readonly"/>
	                        <wicket:message key="page5.054">期</wicket:message>~<wicket:message key="page5.065">第</wicket:message>
	                        <input type="text" id="endNum_8" name="endNum_8" size="3" maxlength="3" class="numeric notIsOneNum" readonly="readonly"/>
	                        <wicket:message key="page5.054">期</wicket:message>
	                    </td>
	                </tr>
	                <tr class="isUseBox_8" style="display:none">
	                    <td>
	                        <div id="isTempTr_8" />
	                    </td>
	                </tr>
	                <tr>
	                    <td class="hd2">
	                        <wicket:message key="page5.063">第九段</wicket:message>：
	                        <input type="checkbox" name="isUseBox_9" id="isUseBox_9" value="Y" class="notIsOne"/>
	                        <wicket:message key="page5.065">第</wicket:message>
	                        <input type="text" id="bgnNum_9" name="bgnNum_9" size="3" maxlength="3" class="numeric notIsOneNum" readonly="readonly"/>
	                        <wicket:message key="page5.054">期</wicket:message>~<wicket:message key="page5.065">第</wicket:message>
	                        <input type="text" id="endNum_9" name="endNum_9" size="3" maxlength="3" class="numeric notIsOneNum" readonly="readonly"/>
	                        <wicket:message key="page5.054">期</wicket:message>
	                    </td>
	                </tr>
	                <tr class="isUseBox_9" style="display:none">
	                    <td>
	                        <div id="isTempTr_9" />
	                    </td>
	                </tr>
	                <tr>
	                    <td class="hd2">
	                        <wicket:message key="page5.064">第十段</wicket:message>：
	                        <input type="checkbox" name="isUseBox_10" id="isUseBox_10" value="Y" class="notIsOne"/>
	                        <wicket:message key="page5.065">第</wicket:message>
	                        <input type="text" id="bgnNum_10" name="bgnNum_10" size="3" maxlength="3" class="numeric notIsOneNum" readonly="readonly"/>
	                        <wicket:message key="page5.054">期</wicket:message>~<wicket:message key="page5.065">第</wicket:message>
	                        <input type="text" id="endNum_10" name="endNum_10" size="3" maxlength="3" class="numeric notIsOneNum" readonly="readonly"/>
	                        <wicket:message key="page5.054">期</wicket:message>
	                    </td>
	                </tr>
	                <tr class="isUseBox_10" style="display:none">
	                    <td>
	                        <div id="isTempTr_10" />
	                    </td>
	                </tr><!--省息遞減-->
	                <tr>
	                    <td>
	                        <span>■ <wicket:message key="L140S02C.decFlag">省息遞減</wicket:message>:</span>
	                        <select name="decFlag" id="decFlag" itemType="L140S02C_decFlag" class="required"/>
	                    </td>
	                </tr>
	                <!--其他-->
	                <tr>
	                    <td class="hd2">
	                        <input type="checkbox" name="isInputDesc" id="isInputDesc" value="Y"/>
	                        <span><wicket:message key="L140S02C.desc">其他</wicket:message>:</span>
	                        <span>(<wicket:message key="page5.066">此欄位不會上傳中心主機，若不為報請總額度之利率，請勿登錄此欄！</wicket:message>)</span>
	                        <br/>
	                        <textarea id="desc" name="desc" maxlengthC="1029" class="required" style="width: 650px; height: 83px;display:none"></textarea>
	                    </td>
	                </tr>
	                <tr>
	                    <td class="hd2">
	                        <span id="L140S02CDesc" class="field"></span>
	                    </td>
	                </tr>
					 <!--備註-->
	                <tr>
	                    <td class="hd2">
	                        <span><wicket:message key="L140S02A.remark">備註</wicket:message>:</span>
	                        <br/>
	                        <textarea class="required" id="rateRemark" name="rateRemark" maxlengthC="100" style="width: 650px; height: 83px;"></textarea>
	                    </td>
	                </tr>
	            </tbody>
	        </table>
	    </form>
	</div>
	<div id="submitRateBox" style="display: none;">
	    <form action="" id="L140S02CsubmitRate" name="L140S02CsubmitRate">
	        <table class="tb2" width="95%" border="0" cellspacing="0" cellpadding="0">
	            <tr>
	                <td class="hd2">
	                    <wicket:message key="L140S02C.submitRate">首段期間適用利率</wicket:message>
	                </td>
	                <td>
	                    <input type="text" id="tmpsubmitRateTemp" name="tmpsubmitRateTemp" class="hide" />
	                    <input type="text" id="tmpsubmitRate" name="tmpsubmitRate" class="numeric required" integer="2" fraction="4" maxlength="7" size="8" />
	                    ％
	                </td>
	            </tr>
	        </table>
	    </form>
	</div>
	<!---登錄利率 end--->
	
	
	<div id="cls_enterRateBox" style="display:none">
              <div id="cls_enterRateBoxDiv">
                  <form action="" id="cls_enterRateForm">
                      <wicket:message key="page5.084"><!--利率欄位目前之值為--></wicket:message>
                      <br/>
                      <input type="text" name="cls_enterRateInupt" id="cls_enterRateInupt" class="numeric required" positiveonly="false" integer="2" fraction="4" maxlength="7" size="8" />
                      <!--多放一個欄位以免在 按下enter 時送出 -->
                      <input type="text" name="cls_enterRateInupt2" id="cls_enterRateInupt2" style="display:none"/>
                      <wicket:message key="page5.085"><!--請輸入新值--></wicket:message>
                  </form>
              </div>
    </div>
	<!-- 利率用重覆的部分-->
	<div id="L140S02C_temp_Div" style="display:none">
	    <table class="tb2">
	        <tr>
	            <td>
	                <span>■ <wicket:message key="L140S02D.rateType">利率基礎</wicket:message>:</span>
	                <select id="rateType_{seq}" name="rateType_{seq}" class="rateType required haveEven" />
	                <div id="rateUserDiv_{seq}" style="display:none">
	                    01.<wicket:message key="L140S02D.rateUser">自訂利率</wicket:message>：
	                    <input type="text" id="rateUser_{seq}" name="rateUser_{seq}" maxlength="7" integer="2" fraction="4" class="required numeric"/>
	                    (<wicket:message key="page5.067">其他，請自行輸入</wicket:message>)
	                    <br/>
	                    <span class="text-red"><wicket:message key="L140S02D.rateUserType">自訂利率參考指標</wicket:message>：</span>
	                    <select id="rateUserType_{seq}" name="rateUserType_{seq}" class="rateUserType required"/>
	                </div>
	                <div id="rateTypeDiv_{seq}" style="display:none">
	                    (<wicket:message key="L140S02D.baseRate">指標利率</wicket:message>
	                    <span id="baseRate_{seq}" class="field"></span>％
	                    <button type="button" id="reloadBaseRate_{seq}">
	                        <span class="text-only"><wicket:message key="page5.068">重新引進利率</wicket:message></span>
	                    </button>
	                    <button type="button" id="modfixBaseRate_{seq}">
	                        <span class="text-only"><wicket:message key="button.modify">修改</wicket:message></span>
	                    </button>
	                    <wicket:message key="page5.069">顯示之利率資料基準日為前一營業日</wicket:message>)
	                    <br/>
	                    <div id="pmFlagDiv_{seq}" style="display:none">
	                        <select id="pmFlag_{seq}" name="pmFlag_{seq}" itemType="L140S02D_pmFlag" class="pmFlag haveEven" />
	                        <span id="pmRateSpan_{seq}" style="display:none"><wicket:message key="page5.070">年率</wicket:message>
	                            <input type="text" id="pmRate_{seq}" name="pmRate_{seq}" size="7" maxlength="7" class="numeric" integer="2" fraction="4"/>
	                            ％ 
	                        </span>
	                    </div>
	                    (<wicket:message key="page5.071">目前為</wicket:message>
	                    <span id="nowRate_{seq}" class="field"></span>％)
	                    <input type="hidden" id="baseDesc_{seq}" name="baseDesc_{seq}" />
	                </div>
	            </td>
	        </tr>
	        <tr>
	            <td>
	                <span>■ <wicket:message key="L140S02D.rateFlag">利率方式</wicket:message>：</span>
	                <select id="rateFlag_{seq}" name="rateFlag_{seq}" itemType="lms1405s0204_rateKind" class="required rateFlag haveEven"/>
	                <div id="rateFlagDiv_{seq}" style="display:none">
	                    <span>■ <wicket:message key="L140S02D.rateChgWay">利率變動方式</wicket:message>：</span>
	                    <select id="rateChgWay_{seq}" name="rateChgWay_{seq}" itemType="L140S02D_rateChgWay" class="required rateChgWay haveEven" />
	                    <div id="rateChgWay2Div_{seq}" style="display:none">
	                        <wicket:message key="page5.072">每</wicket:message>
	                        <select name="rateChgWay2_{seq}" id="rateChgWay2_{seq}" itemType="L140S02D_rateChgWay2" class="required"/>
	                        <wicket:message key="page5.073">調整一次</wicket:message>
	                    </div>
	                </div>
	            </td>
	        </tr>
	    </table>
	</div>
	<!-- 利率用重覆的部分 END-->
	
	<div id="cls_L120S19BGridBox" style="display:none">
		<div id="cls_L120S19BGrid">
        </div>
    </div>
</wicket:panel>
</body>
</html>