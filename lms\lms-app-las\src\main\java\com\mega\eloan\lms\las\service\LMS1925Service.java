package com.mega.eloan.lms.las.service;

import java.util.List;
import java.util.Map;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.ICapService;

import com.mega.eloan.lms.model.C120S01B;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L160M01A;
import com.mega.eloan.lms.model.L192M01A;
import com.mega.eloan.lms.model.L192M01B;
import com.mega.eloan.lms.model.L192M01C;
import com.mega.eloan.lms.model.L192S01A;
import com.mega.eloan.lms.model.L192S02A;

/**
 * 授信業務工作底稿基本介面
 * 
 * <AUTHOR>
 * 
 */
public interface LMS1925Service extends ICapService {
	/**
	 * 取得工作底稿 grid
	 * 
	 * @param search
	 *            ISearch
	 * @return Page<L192M01A>
	 */
	Page<L192M01A> get1925V01(ISearch search);

	/**
	 * 取得工作底稿主文件資料
	 * 
	 * @param oid
	 *            key
	 * @return L192M01A
	 */
	L192M01A getL192M01A(String oid);

	/**
	 * 儲存工作底稿
	 * 
	 * @param l192m01a
	 *            L192M01A
	 */
	void saveL192M01A(L192M01A l192m01a);

	List<L192S02A> getL192S02A(L192M01A l192m01a);

	/**
	 * 取得擔保品資料grid
	 * 
	 * @param search
	 *            ISearch
	 * @return Page<L192S02A>
	 */
	Page<L192S02A> getL192S02A(ISearch search);

	/**
	 * 取得借款人，連保人基本資料grid
	 * 
	 * @param search
	 *            ISearch
	 * @return Page<L192M01B>
	 */
	Page<L192M01B> getL192M01B(ISearch search);

	/**
	 * 儲存擔保品資料
	 * 
	 * @param l192s02a
	 *            L192S02A
	 */
	void saveL192S02A(L192S02A l192s02a);

	/**
	 * 儲存申請內容資料
	 * 
	 * @param l192s01as
	 *            List<L192S01A>
	 */
	void saveL192S01A(List<L192S01A> l192s01as);

	/**
	 * 刪除擔保品資料
	 * 
	 * @param l192s02a
	 *            L192S02A
	 */
	void deleteL192S02A(L192S02A l192s02a);

	/**
	 * 取得擔保品資料
	 * 
	 * @param oid
	 *            key
	 * @return L192S02A
	 */
	L192S02A getL192S02A(String oid);

	/**
	 * 取得申請內容資料
	 * 
	 * @param oid
	 *            key
	 * @return L192S01A
	 */
	L192S01A getL192S01A(String oid);

	/**
	 * 儲存新文件
	 * 
	 * @param meta
	 *            L192M01A
	 * @throws CapMessageException 
	 */
	void saveNewDocument(L192M01A meta) throws CapMessageException;

	/**
	 * 刪除工作底稿
	 * 
	 * @param mainOid
	 *            mainOid
	 */
	void deleteL192M01A(String mainOid);

	List<L160M01A> findL160M01A(ISearch search);

	Page<L160M01A> get1925V02(ISearch search);

	L120M01A getLatestCaseDateById(String custId, String dupNo);

	/**
	 * 取得申請內容grid
	 * 
	 * @param search
	 *            ISearch
	 * @return Page<L192S01A>
	 */
	Page<L192S01A> getL192S01A(ISearch search);

	/**
	 * 刪除申請內容資料
	 * 
	 * @param mainOid
	 *            mainOid
	 */
	void deleteL192S01A(String mainOid);

	/**
	 * 更新申請內容資料
	 * 
	 * @param mainOid
	 *            mainOid
	 * @param l192s01as
	 *            List<L192S01A>
	 */
	void reNewL192S01A(String mainOid, List<L192S01A> l192s01as);

	/**
	 * 刪除借款人，連保人基本資料
	 * 
	 * @param mainOid
	 *            mainOid
	 */
	void deleteL192M01B(String mainOid);

	/**
	 * 更新借款人，連保人資料
	 * 
	 * @param mainOid
	 *            mainOid
	 * @param l192m01bs
	 *            List<L192M01B>
	 */
	void reNewL192M01B(String mainOid, List<L192M01B> l192m01bs);

	/**
	 * 儲存借款人，連保人基本資料
	 * 
	 * @param l192m01bs
	 *            List<L192M01B>
	 */
	void saveL192M01B(List<L192M01B> l192m01bs);

	/**
	 * 流程控制
	 * 
	 * @param oid
	 *            key
	 * @param action
	 *            action
	 */
	void flowControl(String oid, String action);

	Page<Map<String, Object>> getBorrows(String ownBrId,String docStatus, ISearch search)
			throws CapException;

	/**
	 * 儲存並傳送資料
	 * 
	 * @param l192m01a
	 *            L192M01A
	 */
	void saveAndSendDocument(L192M01A l192m01a);

	/**
	 * 取得最近一份工作底稿內容
	 * 
	 * @param brNo
	 *            BranchNo
	 * @param shtType
	 *            工作底稿種類
	 * @param innerAudit
	 *            是否為內容查核
	 * @return L192M01A
	 */
	L192M01A getLatestL192M01byBrNoShtTypeInnerAudit(String brNo,
			String shtType, String innerAudit);

	/**
	 * 重新引進稽核工作底稿資料
	 * 
	 * @param l192m01a
	 *            L192M01A
	 * @param l192m01bs
	 *            List<L192M01B>
	 * @param l192m01c
	 *            L192M01C
	 * @param l192s01as
	 *            List<L192S01A>
	 * @param l192s02as
	 *            List<L192S02A>
	 */
	void includeData(L192M01A l192m01a, List<L192M01B> l192m01bs,
			L192M01C l192m01c, List<L192S01A> l192s01as,
			List<L192S02A> l192s02as);

	/**
	 * 取得消金戶最新一筆借款人資料
	 * 
	 * @param custId
	 *            customer Id
	 * @param dupNo
	 *            dupNo
	 * @return L120S01I
	 */
	C120S01B getLatestC120S01BByCustId(String custId, String dupNo);

	/**
	 * 註記是否列印對帳單
	 * 
	 * @param l192m01a
	 *            L192M01A
	 * @param printMark
	 *            String
	 */
	void printMark(L192M01A l192m01a, String printMark);
	
	/**
	 * 刪除擔保品資料
	 * @param mainOid
	 */
	void deleteL192S02A(String mainOid);

	void saveL192S02A(List<L192S02A> l192s02as);

	void reNewL192S02A(String mainOid, List<L192S02A> l192s02as);
}
