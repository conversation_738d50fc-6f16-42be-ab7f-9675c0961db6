/* 
 * CLS1161S02BPanel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.panels;

import com.mega.eloan.common.panels.Panel;

/**<pre>
 * 動審表-借保人Panel
 * </pre>
 * @since  2013/1/3
 * <AUTHOR>
 * @version <ul>
 *           <li>2013/1/3,Fantasy,new
 *          </ul>
 */
public class CLS1161S02BPanel extends Panel {

	private static final long serialVersionUID = 1L;

	/**
	 * @param id
	 */
	public CLS1161S02BPanel(String id) {
		super(id);
	}
}
