package com.mega.eloan.lms.cls.report.impl;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.enums.UnitTypeEnum;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.LmsExcelUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CLSDocStatusEnum;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.cls.report.CLS1220R13RptService;
import com.mega.eloan.lms.cls.service.CLS1220Service;
import com.mega.eloan.lms.dao.C120S01BDao;
import com.mega.eloan.lms.dao.C120S01ZDao;
import com.mega.eloan.lms.dao.C122M01ADao;
import com.mega.eloan.lms.dao.C160M01ADao;
import com.mega.eloan.lms.dao.C160S01CDao;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dao.L120M01CDao;
import com.mega.eloan.lms.dao.L140M01ADao;
import com.mega.eloan.lms.dao.L140M01YDao;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.mfaloan.bean.MISLN30;
import com.mega.eloan.lms.mfaloan.service.MisLNF030Service;
import com.mega.eloan.lms.model.C120S01B;
import com.mega.eloan.lms.model.C120S01Z;
import com.mega.eloan.lms.model.C122M01A;
import com.mega.eloan.lms.model.C122M01F;
import com.mega.eloan.lms.model.C160M01A;
import com.mega.eloan.lms.model.C160S01C;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01C;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01Y;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * 引介案件進度查詢XLS
 */
@Service("cls1220r13rptservice")
public class CLS1220R13RptServiceImpl implements FileDownloadService, CLS1220R13RptService {

	protected static final Logger LOGGER = LoggerFactory.getLogger(CLS1220R13RptServiceImpl.class);

	@Resource
	BranchService branchService;

	@Resource
	EloandbBASEService eloandbBASEService;
	
	@Resource
	CLS1220Service cls1220service;
	
	@Resource
	MisLNF030Service misLNF030Service;
	
	@Resource
	CodeTypeService codeTypeService;
	
	@Resource
	C122M01ADao c122m01adao;
	
	@Resource
	L120M01ADao l120m01adao;
	
	@Resource
	L120M01CDao l120m01cdao;
	
	@Resource
	L140M01ADao l140m01adao;
	
	@Resource
	L140M01YDao l140m01ydao;
	
	@Resource
	C160M01ADao c160m01adao;
	
	@Resource
	C160S01CDao c160s01cdao;
	
	@Resource
	C120S01BDao c120s01bdao;
	
	@Resource
	C120S01ZDao c120s01zdao;
	
	@Override
	public byte[] getContent(PageParameters params) throws FileNotFoundException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
			baos = (ByteArrayOutputStream) this.generateXls(params);
			return baos.toByteArray();
		} finally {
			if (baos != null) {
				baos.close();
			}

		}
	}

	private ByteArrayOutputStream generateXls(PageParameters params)
	throws IOException, Exception {

		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		genXls(outputStream, params);
		
		if (outputStream != null) {
			outputStream.flush();
		}
		return outputStream;
	}

	private void genXls(ByteArrayOutputStream outputStream, PageParameters params) throws IOException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		UnitTypeEnum unitType = UnitTypeEnum.convertToUnitType(branchService.getBranch(user.getUnitNo()));
		
		HSSFWorkbook workbook = null;
		HSSFSheet sheet1 = null;

		if (true) {
			
			String custId = Util.trim(params.getString("custId"));
			String brIdFilter = Util.trim(params.getString("brIdFilter"));
			String introduceMegaEmp = Util.trim(params.getString("introduceMegaEmp"));
			String ploanCaseId = Util.trim(params.getString("ploanCaseId"));
			String applyTS_beg = params.getString("applyTS_beg");
			String applyTS_end = params.getString("applyTS_end");
			
			ISearch search = c122m01adao.createSearchTemplete();
			search.setMaxResults(Integer.MAX_VALUE);
			search.addOrderBy("applyTS", true);
			search.addOrderBy("custId", false);
			//進建日期
			if (Util.isEmpty(applyTS_beg) || Util.isEmpty(applyTS_end)) {//沒輸入進件日期不回傳結果
				// 不然清空前端 grid 的資料列(用查到0筆的方式)
				search.addSearchModeParameters(SearchMode.EQUALS, "applyTS", "");
			}else {
				//search.addSearchModeParameters(SearchMode.GREATER_EQUALS, "applyTS", (applyTS +" 00:00:00"));
				//search.addSearchModeParameters(SearchMode.LESS_EQUALS, "applyTS", (applyTS +" 23:59:59"));
				
				SearchModeParameter se_applyTS_start = new SearchModeParameter(SearchMode.GREATER_EQUALS, "applyTS", Util.parseDate(applyTS_beg +" 00:00:00"));
				SearchModeParameter se_applyTS_end = new SearchModeParameter(SearchMode.LESS_EQUALS, "applyTS", Util.parseDate(applyTS_end +" 23:59:59"));
				SearchModeParameter se_applyTS_start_end = new SearchModeParameter(SearchMode.AND, 
						new SearchModeParameter(SearchMode.IS_NOT_NULL, "applyTS", ""), 
						new SearchModeParameter(SearchMode.AND, se_applyTS_start, se_applyTS_end));
				
				SearchModeParameter caseDate_start = new SearchModeParameter(SearchMode.GREATER_EQUALS, "applyTS", Util.parseDate(applyTS_beg +" 00:00:00"));
				SearchModeParameter caseDate_end = new SearchModeParameter(SearchMode.LESS_EQUALS, "applyTS", Util.parseDate(applyTS_end +" 23:59:59"));
				SearchModeParameter caseDate_start_end = new SearchModeParameter(SearchMode.AND, 
						new SearchModeParameter(SearchMode.IS_NULL, "applyTS", ""), 
						new SearchModeParameter(SearchMode.AND, caseDate_start, caseDate_end));

				search.addSearchModeParameters(SearchMode.OR, caseDate_start_end, se_applyTS_start_end);
				
				//僅抓線上案件
				search.addSearchModeParameters(SearchMode.EQUALS, "IncomType", "2");
			}
			//身分證統編
			if (!Util.isEmpty(custId)) {
				search.addSearchModeParameters(SearchMode.LIKE, "custId", custId + "%");
			}
			//引介分行
			if (!Util.isEmpty(brIdFilter)) {//篩選原始申貸行
				//search.addSearchModeParameters(SearchMode.IN, "ploanIntroduceBr1st", new String[] {"", brIdFilter});
				search.addSearchModeParameters(SearchMode.EQUALS, "orgBrId", brIdFilter);
			} else {
				if(unitType.isEquals(UnitTypeEnum.分行)){//分行使用者僅能查看所屬分行
					//search.addSearchModeParameters(SearchMode.IN, "ploanIntroduceBr1st", new String[] {"", user.getUnitNo()});
					search.addSearchModeParameters(SearchMode.EQUALS, "orgBrId", user.getUnitNo());
				}
			}
			//申請類別固定篩抓 C(信貸), P(卡友信貸) 
			String[] applyKindArray = new String[] {"P", "C"};
			search.addSearchModeParameters(SearchMode.IN, "applyKind", applyKindArray);
			//引介人員
			if (!Util.isEmpty(introduceMegaEmp)) {
				//search.addSearchModeParameters(SearchMode.EQUALS, "megaEmpNo", chgSignMegaEmp);
				search.addSearchModeParameters(SearchMode.EQUALS, "marketingStaff", introduceMegaEmp);
			}
			//案件編號
			if (!Util.isEmpty(ploanCaseId)) {
				search.addSearchModeParameters(SearchMode.EQUALS, "ploanCaseId", ploanCaseId);
			}
			
			if (true) {
				workbook = new HSSFWorkbook();
				sheet1 = workbook.createSheet("清單");
				// ===內容樣式===
	            HSSFFont headFont12 = workbook.createFont();
	            {
	                headFont12.setFontName("標楷體");
	                headFont12.setFontHeightInPoints((short)12);
	            }

	            HSSFCellStyle cellFormatL = workbook.createCellStyle();
	            {                
	                cellFormatL.setFont(headFont12);
	                cellFormatL.setAlignment(HorizontalAlignment.LEFT);
	                cellFormatL.setWrapText(true);
	            }
	            HSSFCellStyle cellFormatL_Border = workbook.createCellStyle();
	            {
	                cellFormatL_Border.cloneStyleFrom(cellFormatL);
	                cellFormatL_Border.setBorderTop(BorderStyle.THIN);
	                cellFormatL_Border.setBorderBottom(BorderStyle.THIN);
	                cellFormatL_Border.setBorderLeft(BorderStyle.THIN);
	                cellFormatL_Border.setBorderRight(BorderStyle.THIN);
	            }
				// ===標題樣式===
	            HSSFFont headFontTitle = workbook.createFont();
	            {
	                headFontTitle.setFontName("標楷體");
	                headFontTitle.setFontHeightInPoints((short)20);
	                headFontTitle.setBold(true);
	                headFontTitle.setItalic(false); 
	                headFontTitle.setUnderline(HSSFFont.U_NONE); 
	            }
	            
	            HSSFCellStyle cellFormatT = workbook.createCellStyle();
	            {
	                cellFormatT.setFont(headFontTitle);
	                cellFormatT.setAlignment(HorizontalAlignment.CENTER);
	                cellFormatT.setWrapText(true);
	            }
	            HSSFCellStyle cellFormatT_Border = workbook.createCellStyle();
		        {
		        	cellFormatT_Border.cloneStyleFrom(cellFormatL);
		        	cellFormatT_Border.setBorderTop(BorderStyle.THIN);
		        	cellFormatT_Border.setBorderBottom(BorderStyle.THIN);
		        	cellFormatT_Border.setBorderLeft(BorderStyle.THIN);
		        	cellFormatT_Border.setBorderRight(BorderStyle.THIN);
		        }
		        
		        HSSFCellStyle amtTWDFormat = workbook.createCellStyle();
		        {
		            amtTWDFormat.setFont(headFont12);
		            amtTWDFormat.setDataFormat(workbook.createDataFormat().getFormat("#,##0"));
		            amtTWDFormat.setBorderTop(BorderStyle.THIN);
		            amtTWDFormat.setBorderBottom(BorderStyle.THIN);
		            amtTWDFormat.setBorderLeft(BorderStyle.THIN);
		            amtTWDFormat.setBorderRight(BorderStyle.THIN);
		        }
				// =============
				Map<String, Integer> headerMap = new LinkedHashMap<String, Integer>();
				headerMap.put("分行別", 25);
				headerMap.put("分行名稱", 30);
				headerMap.put("客戶統編", 25);
				headerMap.put("客戶姓名", 30);
				headerMap.put("進件時間", 25);
				headerMap.put("申請額度", 30);
				headerMap.put("進件類型", 25);
				headerMap.put("核准日期", 25);
				headerMap.put("首撥日期", 25);
				headerMap.put("授信起日", 25);
				headerMap.put("授信迄日", 25);
				headerMap.put("撥款額度", 25);
				headerMap.put("狀態", 25);
				headerMap.put("公司名稱", 40);
				headerMap.put("引介分行", 25);
				headerMap.put("引介人員", 30);
				headerMap.put("案件價值", 30);
				headerMap.put("行銷方案", 30);
				
				// ---
				int totalColSize = headerMap.size();
				List<Object[]> list = new ArrayList<Object[]>();
				
				Map<String, String> cacheMap = new HashMap<String, String>();
				List<C122M01A> c122m01aList = cls1220service.getC122M01AList(search);
				
				//進件類型
				final Map<String, String> incomtypeMap = cls1220service.get_IncomTypeMap();
				//案件狀態
				final Map<String, String> docstatusMap = cls1220service.get_DocStatusNewDescMap();
				//行銷方案中文說明
				Map<String, String> ploanPlanCodeType = codeTypeService.findByCodeType("ploan_plan", "zh_TW");
				
				for (int i = 0; i < c122m01aList.size(); i++) {
					C122M01A c122m01a = c122m01aList.get(i);
					C122M01F c122m01f = c122m01a.getC122m01f();
					//L140M01Y l140m01y = l140m01ydao.findByRefmainId(c122m01a.getMainId());//額度特殊註記檔
					List<L140M01Y> l140m01ys = l140m01ydao.findByRef(UtilConstants.L140M01Y_refType_docCode1.ELF459_SRCFLAG_1, c122m01a.getMainId(), c122m01a.getOid());
					L140M01A l140m01a = null;
					L120M01C l120m01c = null;
					L120M01A l120m01a = null;
					//C160M01A c160m01a = null;
					C160S01C c160s01c = null;
					C120S01B c120s01b = null;
					C120S01Z c120s01z = null;
					//C120s01b s01b
					Date LNF030_LOAN_DATE = null;// 首撥日期
					BigDecimal lnAmt_1st = BigDecimal.ZERO;//撥款額度
					//if(l140m01y != null){
					if(l140m01ys != null && l140m01ys.size() > 0){
						for(L140M01Y l140m01y : l140m01ys){
							l140m01a = l140m01adao.findByMainId(l140m01y.getMainId());//額度明細表主檔
							if (l140m01a == null){//
								continue;
							}
							;
							if (!Arrays.asList(l140m01a.getProPerty().split("|")).contains("1")){//只抓新做的額度明細
								continue;
							}
							
							if (l140m01a != null && Util.isNotEmpty(l140m01a.getCntrNo())) {
								List<MISLN30> ln30Data = misLNF030Service.selBykeyWithoutCancelDate(l140m01a.getCustId(), l140m01a.getDupNo(), l140m01a.getCntrNo());
								for (MISLN30 misln30 : ln30Data) {
									LNF030_LOAN_DATE = misln30.getLnf030_loan_date();
									lnAmt_1st = CrsUtil.parseBigDecimal(misln30.getLnf030_1st_ln_amt());// 首撥金額
								}
							}
							if(l140m01a != null){
								l120m01c = l120m01cdao.findoneByRefMainId(l140m01a.getMainId());
							}
							if(l120m01c != null){
								l120m01a = l120m01adao.findByMainId(l120m01c.getMainId());
							}
							if(l120m01a != null){
								//left join (SELECT * FROM LMS.C160M01A WHERE docStatus IN ('03O','05O','04O')) C160M01A on C160M01A.srcMAINID=l120m01a.MAINID
								List<C160M01A> c160m01as = c160m01adao.findBySrcMainId(l120m01a.getMainId(), 
											new String[] {CLSDocStatusEnum.已核准.getCode(),CLSDocStatusEnum.先行動用_已覆核.getCode(),CLSDocStatusEnum.先行動用_待覆核.getCode()});
								if(c160m01as != null && c160m01as.size() > 0){
									for(C160M01A m160a : c160m01as){
										List<C160S01C> c160s01cs = c160s01cdao.findByMainId(m160a.getMainId());
										if(c160s01cs != null && c160s01cs.size() > 0){
											for(C160S01C s160c : c160s01cs){
												c160s01c = s160c;
												break;
											}
										}
									}
								}
								
								c120s01b = c120s01bdao.findByUniqueKey(l120m01a.getMainId(), l120m01a.getCustId(), l120m01a.getDupNo());
								
								c120s01z = c120s01zdao.findByUniqueKey(l120m01a.getMainId(), l120m01a.getCustId(), l120m01a.getDupNo());
							}
						}
					}
					
					//明細資料值，預設先空值
					String[] arr = new String[totalColSize];
					for (int i_col = 0; i_col < totalColSize; i_col++) {
						arr[i_col] = "";
					}
					arr[0] = Util.trim(c122m01a.getOwnBrId()); // 分行別
					arr[1] = getBrName(cacheMap, c122m01a.getOwnBrId()); // 分行名稱
					arr[2] = Util.trim(c122m01a.getCustId());//客戶統編
					arr[3] = Util.trim(c122m01a.getCustName());//客戶姓名
					arr[4] = Util.trim(TWNDate.toFullAD(c122m01a.getApplyTS())); //進件時間
					//CapDate.formatDate((Date) c122m01a.getApplyTS(), UtilConstants.DateFormat.YYYY_MM_DD);
					arr[5] = (l140m01a != null) ? Util.trim(l140m01a.getCurrentApplyAmt()) : Util.trim(c122m01a.getApplyAmt().multiply(new BigDecimal(10000)));//申請額度
					arr[6] = LMSUtil.getDesc(incomtypeMap, c122m01a.getIncomType());//進件類型
					arr[7] = (l120m01a != null) ? Util.trim(CapDate.formatDate((Date) l120m01a.getApproveTime(), UtilConstants.DateFormat.YYYY_MM_DD)) : "";//核准日期
					arr[8] = TWNDate.toAD(LNF030_LOAN_DATE);//首撥日期"
					arr[9] = (c160s01c != null) ? Util.trim(CapDate.formatDate((Date) c160s01c.getLnStartDate(), UtilConstants.DateFormat.YYYY_MM_DD)) : "";//授信起日"
					arr[10] = (c160s01c != null) ? Util.trim(CapDate.formatDate((Date) c160s01c.getLnEndDate(), UtilConstants.DateFormat.YYYY_MM_DD)) : "";//授信迄日"
					arr[11] = LMSUtil.pretty_numStr(lnAmt_1st);//撥款額度
					arr[12] = Util.trim(docstatusMap.get(c122m01a.getDocStatus())); // 案件狀態
					arr[13] = (c120s01b != null) ? c120s01b.getComName() : "";//公司名稱"
					arr[14] = Util.isEmpty(c122m01a.getPloanIntroduceBr1st()) ? c122m01a.getOrgBrId() : c122m01a.getPloanIntroduceBr1st();//引介分行
					//arr[15]= Util.trim(c122m01a.getMarketingStaff());//c122m01f.getMegaEmpNo();//引介人員員編
					arr[15] = ("".equals(Util.trim(c122m01a.getMarketingStaff()))) ? "" : (Util.isNumeric(c122m01a.getMarketingStaff()) ?
						String.format("%06d", Integer.parseInt(Util.trim(c122m01a.getMarketingStaff()))) : c122m01a.getMarketingStaff());//引介人員員編
					arr[16] = (c120s01z != null) ? Util.trim(c120s01z.getKycNpv()) : "0";//案件價值
					if (Util.isNotEmpty(ploanPlanCodeType) && Util.isNotEmpty(c122m01a.getPloanPlan())) {
						// J-112-0463 行銷代碼 (這先不用放)
						//arr[17] = Util.trim(MapUtils.getString(row_map, "ploanPlan"));
						// J-112-0463 行銷方案(中文說明)
						arr[17] = ploanPlanCodeType.get(Util.trim(c122m01a.getPloanPlan()));
					}
					
					list.add(arr);
					
				}
				
				// ==============================
				int rowIdx = 0;
			    HSSFRow titleRow = sheet1.createRow(rowIdx);  

				//警語
				
				HSSFFont warnnFont12 = workbook.createFont();
				{
				    warnnFont12.setFontName("標楷體");
				    warnnFont12.setFontHeightInPoints((short)12);
				    warnnFont12.setColor(HSSFFont.COLOR_RED);
				}
				HSSFCellStyle cellFormatL_Warnn = workbook.createCellStyle();
				{
				    cellFormatL_Warnn.setFont(warnnFont12);
				    cellFormatL_Warnn.setBorderTop(BorderStyle.THIN);
				    cellFormatL_Warnn.setBorderBottom(BorderStyle.THIN);
				    cellFormatL_Warnn.setBorderLeft(BorderStyle.THIN);
				    cellFormatL_Warnn.setBorderRight(BorderStyle.THIN);
				}
				sheet1.setColumnWidth(0, 30 * 256);
				LmsExcelUtil.addCell(titleRow,0, "此引介報表僅供參考，相關引介人員及引介分行計績，以消金處發函為主。", cellFormatL_Warnn);
				rowIdx++;
				if(true){
					int colIdx = 0;
					HSSFRow headerRow = sheet1.createRow(rowIdx);
					for (String h : headerMap.keySet()) {
						int colWidth = headerMap.get(h);
						sheet1.setColumnWidth(colIdx, colWidth * 256);
						LmsExcelUtil.addCell(headerRow,colIdx, h, cellFormatL_Border);
						// ---
						colIdx++;
					}
				}
				// ==============================
				rowIdx = 2;
				for (Object[] arr : list) {
					HSSFRow dataRow = sheet1.createRow(rowIdx);
					int colLen = arr.length;
					for (int i_col = 0; i_col < colLen; i_col++) {
						if (i_col == 5 || i_col == 11 || i_col == 16) { // 數字欄位,自0起算
							if (Util.isEmpty(Util.trim(arr[i_col]))) {
								// 空白, 就不去轉數字
								LmsExcelUtil.addCell(dataRow,i_col, Util.trim(arr[i_col]), cellFormatL_Border);
							} else {
								// 金額(至整數欄位)
								Cell numCell = dataRow.createCell(i_col);
								numCell.setCellValue(CrsUtil.parseBigDecimal(arr[i_col]).doubleValue());
				                numCell.setCellStyle(amtTWDFormat);
							}
						} else {
							LmsExcelUtil.addCell(dataRow, i_col, Util.trim(arr[i_col]), cellFormatL_Border);
						}
					}
					// ---
					rowIdx++;
				}
				
				workbook.write(outputStream);
				workbook.close();
			}
		}
	}

	private String getBrName(Map<String, String> cacheMap, String brNo){
		if(!cacheMap.containsKey(brNo)){
			IBranch obj = branchService.getBranch(brNo);
			if(obj!=null){
				cacheMap.put(brNo, Util.trim(obj.getBrName()));
			}
		}
		return Util.trim(cacheMap.get(brNo));
	}
	
}
