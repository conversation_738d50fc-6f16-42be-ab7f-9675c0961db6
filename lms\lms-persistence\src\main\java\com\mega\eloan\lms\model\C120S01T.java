/* 
 * C120S01T.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 個金簡化簽報書借款人主檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C120S01T", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId","ownBrId","custId","dupNo"}))
public class C120S01T extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * 編製單位代號<p/>
	 * 單位代碼
	 */
	@Size(max=3)
	@Column(name="OWNBRID", length=3, columnDefinition="CHAR(3)")
	private String ownBrId;

	/** 身分證統編 **/
	@Size(max=10)
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Size(max=1)
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 
	 * seqNo<p/>
	 * orderBy
	 */
	@Column(name="SEQNO", columnDefinition="INTEGER" )
	private Integer seqNo;

	/** 借款人姓名 for view **/
	@Transient
	private String custName;

	/** 
	 * 客戶型態
(區部別)<p/>
	 * 0.無、1.DBU、2.OBU、5.海外(海外同業, 海外客戶)
	 */
	@Size(max=1)
	@Column(name="TYPCD", length=1, columnDefinition="CHAR(1)")
	private String typCd;

	/** 備註 **/
	@Size(max=30)
	@Column(name="RMK", length=30, columnDefinition="VARCHAR(30)")
	private String rmk;

	/** 關係 **/
	@Size(max=120)
	@Column(name="RELATIONSHIP", length=120, columnDefinition="VARCHAR(120)")
	private String relationship;

	/** 職稱 **/
	@Size(max=120)
	@Column(name="OCCUPATION", length=120, columnDefinition="VARCHAR(120)")
	private String occupation;

	/** 
	 * 利害關係人<p/>
	 * Y/N
	 */
	@Size(max=1)
	@Column(name="ISQDATA", length=1, columnDefinition="CHAR(1)")
	private String isqdata;

	/** 利害關係人說明 **/
	@Size(max=150)
	@Column(name="ISQDATADESC", length=150, columnDefinition="VARCHAR(150)")
	private String isqdataDesc;

	/** 主債務額度/餘額 **/
	@Size(max=50)
	@Column(name="CLPAYNT", length=50, columnDefinition="VARCHAR(50)")
	private String clpayNt;
	
	/** 共同債務(本行) **/
	@Size(max=50)
	@Column(name="COMMONDEBT", length=50, columnDefinition="VARCHAR(50)")
	private String commonDebt;
	
	/** 從債務(本行) **/
	@Size(max=50)
	@Column(name="SLAVEDEBT", length=50, columnDefinition="VARCHAR(50)")
	private String slaveDebt;
	
	/** 信用卡使用及繳款狀況 **/
	@Size(max=120)
	@Column(name="CREDITSTATE", length=120, columnDefinition="VARCHAR(120)")
	private String creditState;
	
	/** 授信異常紀錄 **/
	@Size(max=150)
	@Column(name="LOANFAILRECORD", length=150, columnDefinition="VARCHAR(150)")
	private String loanFailRecord;
	
	/** J10資料日(YYYY-MM-dd) */
	@Temporal(TemporalType.DATE)
	@Column(name="J10DATE", columnDefinition="DATE")
	private Date j10Date;

	/** J10信用評分 **/
	@Size(max=5)
	@Column(name="J10SCORE", length=5, columnDefinition="VARCHAR(5)")
	private String j10Score;
	
	/** J10百分位點 **/
	@Size(max=25)
	@Column(name="J10PERCENTILE", length=25, columnDefinition="VARCHAR(25)")
	private String j10Percentile;
	
	/** J10違約率 **/
	@Size(max=15)
	@Column(name="J10BREACH", length=15, columnDefinition="VARCHAR(15)")
	private String j10Breach;
	
	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得編製單位代號<p/>
	 * 單位代碼
	 */
	public String getOwnBrId() {
		return this.ownBrId;
	}
	/**
	 *  設定編製單位代號<p/>
	 *  單位代碼
	 **/
	public void setOwnBrId(String value) {
		this.ownBrId = value;
	}

	/** 取得身分證統編 **/
	public String getCustId() {
		return this.custId;
	}
	/** 設定身分證統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得身分證統編重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}
	/** 設定身分證統編重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 
	 * 取得seqNo<p/>
	 * orderBy
	 */
	public Integer getSeqNo() {
		return this.seqNo;
	}
	/**
	 *  設定seqNo<p/>
	 *  orderBy
	 **/
	public void setSeqNo(Integer value) {
		this.seqNo = value;
	}

	/** 設定借款人姓名 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/** 
	 * 取得客戶型態
(區部別)<p/>
	 * 0.無、1.DBU、2.OBU、5.海外(海外同業, 海外客戶)
	 */
	public String getTypCd() {
		return this.typCd;
	}
	/**
	 *  設定客戶型態
(區部別)<p/>
	 *  0.無、1.DBU、2.OBU、5.海外(海外同業, 海外客戶)
	 **/
	public void setTypCd(String value) {
		this.typCd = value;
	}

	/** 取得備註 **/
	public String getRmk() {
		return this.rmk;
	}
	/** 設定備註 **/
	public void setRmk(String value) {
		this.rmk = value;
	}

	/** 取得關係 **/
	public String getRelationship() {
		return this.relationship;
	}
	/** 設定關係 **/
	public void setRelationship(String value) {
		this.relationship = value;
	}

	/** 取得職稱 **/
	public String getOccupation() {
		return this.occupation;
	}
	/** 設定職稱 **/
	public void setOccupation(String value) {
		this.occupation = value;
	}

	/** 
	 * 取得利害關係人<p/>
	 * Y/N
	 */
	public String getIsqdata() {
		return this.isqdata;
	}
	/**
	 *  設定利害關係人<p/>
	 *  Y/N
	 **/
	public void setIsqdata(String value) {
		this.isqdata = value;
	}

	/** 取得利害關係人說明 **/
	public String getIsqdataDesc() {
		return this.isqdataDesc;
	}
	/** 設定利害關係人說明 **/
	public void setIsqdataDesc(String value) {
		this.isqdataDesc = value;
	}
	public void setClpayNt(String clpayNt) {
		this.clpayNt = clpayNt;
	}
	public String getClpayNt() {
		return clpayNt;
	}
	public void setCommonDebt(String commonDebt) {
		this.commonDebt = commonDebt;
	}
	public String getCommonDebt() {
		return commonDebt;
	}
	public void setSlaveDebt(String slaveDebt) {
		this.slaveDebt = slaveDebt;
	}
	public String getSlaveDebt() {
		return slaveDebt;
	}
	public void setCreditState(String creditState) {
		this.creditState = creditState;
	}
	public String getCreditState() {
		return creditState;
	}
	public void setLoanFailRecord(String loanFailRecord) {
		this.loanFailRecord = loanFailRecord;
	}
	public String getLoanFailRecord() {
		return loanFailRecord;
	}
	public Date getJ10Date() {
		return j10Date;
	}
	public void setJ10Date(Date j10Date) {
		this.j10Date = j10Date;
	}
	public String getJ10Score() {
		return j10Score;
	}
	public void setJ10Score(String j10Score) {
		this.j10Score = j10Score;
	}
	public String getJ10Percentile() {
		return j10Percentile;
	}
	public void setJ10Percentile(String j10Percentile) {
		this.j10Percentile = j10Percentile;
	}
	public String getJ10Breach() {
		return j10Breach;
	}
	public void setJ10Breach(String j10Breach) {
		this.j10Breach = j10Breach;
	}

}
