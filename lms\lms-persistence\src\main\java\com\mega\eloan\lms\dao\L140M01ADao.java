/* 
 * L140M01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao;

import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.Page;

import com.mega.eloan.lms.model.L140M01A;

/** 額度明細表主檔 **/
public interface L140M01ADao extends IGenericDao<L140M01A> {

	L140M01A findByOid(String oid);

	L140M01A findByMainId(String mainId);

	/**
	 * 找出此案件簽報書底下該客戶的所有額度明細表
	 * 
	 * @param mainId
	 *            案件簽報書mainId
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @return 額度明細表
	 */
	List<L140M01A> findByMainIdAndCustId(String mainId, String custId,
			String dupNo);

	L140M01A findByUniqueKey(String mainId);

	L140M01A findOidAndId(String oid, String id);

	/**
	 * 找出這個簽報書底下的額度明細表是否有兩種幣別
	 * 
	 * @param caseMainId
	 * @param itemType
	 * @return
	 */
	List<Object[]> findCurrByMainId(String caseMainId, String itemType);

	/**
	 * 找出這個簽報書底下的額度明細表
	 * 
	 * @param caseMainId
	 *            案件簽報書的mainid
	 * @param itemType
	 *            文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
	 * @param docStatus
	 *            額度明細表文件狀態
	 * @return List<L140M01A>
	 */
	List<L140M01A> findL140m01aListByL120m01cMainId(String caseMainId,
			String itemType, String docStatus);

	/**
	 * 找出這個簽報書底下的額度明細表-給列印用的順序
	 * 
	 * @param caseMainId
	 *            案件簽報書的mainid
	 * @param itemType
	 *            文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
	 * @param docStatus
	 *            額度明細表文件狀態
	 * @return List<L140M01A>
	 */
	List<L140M01A> findL140m01aListByL120m01cMainIdForPrint(String caseMainId,
			String itemType, String docStatus);

	/**
	 * 找出這個簽報書底下的額度明細表 By custId 排序
	 * 
	 * @param caseMainId
	 *            案件簽報書的mainid
	 * @param itemType
	 *            文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
	 * @return List<L140M01A>
	 */
	List<L140M01A> findL140m01aListByL120m01cMainIdOrderByCust(
			String caseMainId, String itemType);

	/**
	 * 找出這個簽報書底下的額度明細表
	 * 
	 * @param caseMainId
	 *            案件簽報書的mainid
	 * @param itemType
	 *            String[] 文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
	 * @param docStatus
	 *            額度明細表文件狀態
	 * @return List<L140M01A>
	 */
	List<L140M01A> findL140m01aListByL120m01cMainId(String caseMainId,
			String[] itemType, String docStatus);

	/**
	 * 根據mainIds 抓出所有額度明細表
	 * 
	 * @param mainId
	 *            關聯編號
	 * @return List<L140M01A>
	 */
	List<L140M01A> findL140m01aListByMainIdList(String[] mainId);

	/**
	 * 根據oids 抓出所有額度明細表
	 * 
	 * @param oids
	 *            文件編號陣列
	 * @return List<L140M01A>
	 */
	List<L140M01A> findL140m01aListByOids(String[] oids);

	/**
	 * 根據額度序號 抓出所有額度明細表
	 * 
	 * @param cntrNo
	 *            額度序號
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @return 額度明細表
	 */
	List<L140M01A> findL140m01aListBycntrNo(String cntrNo, String custId,
			String dupNo);
	
	//依客戶ID 額度序號找 上一份明細表(依時間排序)
	public List<L140M01A> findL140m01aListBycntrNo_orderByCT(String cntrNo,
			String custId, String dupNo);

	List<L140M01A> findByCntrNo(String CntrNo);

	/**
	 * 找出這個簽報書底下的額度明細表
	 * 
	 * @param caseMainId
	 *            案件簽報書的mainid
	 * @return List<L140M01A>
	 */
	List<L140M01A> findL140m01aListByL120m01cMainId(String caseMainId);

	/**
	 * 找出這個簽報書底下的額度明細表
	 * 
	 * @param caseMainId
	 *            案件簽報書的mainid
	 * @param docstatus
	 *            文件狀態
	 * @return
	 */
	List<L140M01A> findL140m01aListByL120m01cMainIdAndDocstatus(
			String caseMainId, String[] docstatus);

	/**
	 * 
	 * @param mainId
	 * @param cntrNo
	 * @param itemType
	 * @return
	 */
	L140M01A findByL120m01cMainIdAndcntrNo(String mainId, String cntrNo,
			String itemType);

	/**
	 * 找出這個簽報書底下的額度明細表,依IsDerivatives和HasDerivatives排序
	 * 
	 * @param caseMainId
	 *            案件簽報書的mainid
	 * @param itemType
	 *            文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
	 * @param docStatus
	 *            額度明細表文件狀態
	 * @return List<L140M01A>
	 */
	List<L140M01A> findL140m01aListByL120m01cMainIdOrderByIsDerivativesAndHasDerivatives(
			String caseMainId, String itemType, String docStatus);

	public List<L140M01A> findL140m01aListByL141m01bMainIdForPrint(
			String caseMainId, String itemType, String docStatus);

	/**
	 * 
	 * @param cntrNo
	 *            透過額度序號找出最後異動的一筆資料
	 * @return
	 */
	public L140M01A findByCntrNoForNew(String cntrNo);

	/**
	 * 取得簽報書項下符合條件(統編、重複序號與額度序號)的額度明細表
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @return
	 */
	public List<L140M01A> findByMainIdCustIdCntrno(String mainId,
			String custId, String dupNo, String cntrNo);

	/**
	 * 取得簽報書項下符合條件(統編、重複序號與文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見)的額度明細表 J-104-0270-001
	 * Web e-Loan國內授信管理系統OBU戶檢核要有聯徵虛擬統編才能送呈主管
	 * 
	 * @param caseMainId
	 * @param custId
	 * @param dupNo
	 * @param itemType
	 * @return
	 */
	List<L140M01A> findByMainIdAndCustId(String caseMainId, String custId,
			String dupNo, String itemType);

	/**
	 * J-105-0179-001 Web e-Loan企金授信建立「往來異常通報戶」紀錄查詢及於簽報書上顯示查詢結果功能
	 * 取得額度性質非不變或解除之額度明細表
	 * 
	 * @param caseMainId
	 * @param custId
	 * @param dupNo
	 * @param itemType
	 * @return
	 */
	public List<L140M01A> findByMainIdAndCustIdWithoutProperty7Or8(
			String caseMainId, String custId, String dupNo, String itemType);

	/**
	 * 取得最近一次簽案日期的額度明細表 J-105-0228-001 Web e-Loan企金授信簽報書新增私募基金相關建檔與報表。
	 * 
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @param docStatus
	 * @return
	 */
	public L140M01A findByCustIdCntrnoDocStatus(String custId, String dupNo,
			String cntrNo, String docStatus);

	public Page<L140M01A> findL140m01aListByL140m01atmp1UserIdForSearch(
			ISearch search, String userId);

	// J-112-0449_05097_B1001 Web e-Loan企金額度明細表新增主要用途查詢條件
	public List<Object[]> findFullTextSearch(String fxUserId, String fxGroupId,
			String fxCaseDateName, String fxCaseDateS, String fxCaseDateE,
			String fxEndDateS, String fxEndDateE, String fxTypCd,
			String fxDocType, String fxDocKind, String fxDocCode,
			String fxUpdaterName, String fxUpdater, String fxCaseBrId,
			String fxCustId, String fxDocRslt, String fxDocStatus,
			String fxLnSubject, String fxRateText, String fxOtherCondition,
			String fxReportOther, String fxReportReason1,
			String fxReportReason2, String fxAreaOption, String fxCollateral,
			String fxCustName, String fxBusCode, String fxCurr,
			String fxLmtDays1, String fxLmtDays2, String fxRateText1,
			String fxGuarantor, String fxCntrNo, String fxCollateral1,
			String unitType, String fxCrGrade, String fxCrKind,
			String fxBldUse, String fxOnlyLand);

	// J-112-0449_05097_B1001 Web e-Loan企金額度明細表新增主要用途查詢條件
	public List<Object[]> findFullTextSearch_CLS(String fxUserId,
			String fxGroupId, String fxCaseDateName, String fxCaseDateS,
			String fxCaseDateE, String fxEndDateS, String fxEndDateE,
			String fxTypCd, String fxDocType, String fxDocKind,
			String fxDocCode, String fxUpdaterName, String fxUpdater,
			String fxCaseBrId, String fxCustId, String fxDocRslt,
			String fxDocStatus, String fxLnSubject, String fxRateText,
			String fxOtherCondition, String fxReportOther,
			String fxReportReason1, String fxReportReason2,
			String fxAreaOption, String fxCollateral, String fxCustName,
			String fxBusCode, String fxCurr, String fxLmtDays1,
			String fxLmtDays2, String fxRateText1, String fxGuarantor,
			String fxCntrNo, String fxCollateral1, String unitType,
			String fxIsCls, String fxProdKind, String fxLnSubjectCls,
			String fxRateTextCls, String fxLnMonth1, String fxLnMonth2,
			String fxBldUse, String fxOnlyLand);

	public L140M01A findLatestApprovedL140m01a(String cntrNo, String custId,
			String dupNo);

	L140M01A findByL120m01cMainIdCustIdAndcntrNo(String caseMainId,
			String custId, String dupNo, String cntrNo, String itemType);

	public List<L140M01A> findL140m01aListByL120m01cMainIdAndProperty(
			String caseMainId, String itemType, String property);

	public List<L140M01A> findL140m01aListByCustIdForSmallBussC(String custId,
			String dupNo);

	public L140M01A findLatestApprovedL140m01a(String cntrNo);

	public List<L140M01A> findL140m01aByCntrNo(String cntrNo);

	public List<L140M01A> findByMainIdAndCustIdOnlyContainProperty7(String caseMainId,
			String custId, String dupNo, String itemType);

    List<Object[]> findSMEAList(Date sDate, Date eDate);
	
	public List<L140M01A> findL140m01aByProjRefCntrNo(String cntrNo);

	public List<L140M01A> findL140m01aListByL120m01cMainIdExcludeCntrNo(String caseMainId, String itemType, String cntrNo);
}