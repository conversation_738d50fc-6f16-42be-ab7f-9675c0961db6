package com.mega.eloan.lms.cls.report.impl;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.base.common.ClsScoreUtil;
import com.mega.eloan.lms.base.common.ClsUtility;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.OverSeaUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.base.service.ScoreService;
import com.mega.eloan.lms.cls.common.ClsUtil;
import com.mega.eloan.lms.cls.constants.ClsConstants;
import com.mega.eloan.lms.cls.panels.CLS1131S01Panel;
import com.mega.eloan.lms.cls.service.CLS1131Service;
import com.mega.eloan.lms.cls.service.CLS1151Service;
import com.mega.eloan.lms.dao.C101S01G_NDao;
import com.mega.eloan.lms.dao.C101S01Q_NDao;
import com.mega.eloan.lms.dao.C101S01R_NDao;
import com.mega.eloan.lms.dao.C101S01YDao;
import com.mega.eloan.lms.dao.C120S01YDao;
import com.mega.eloan.lms.dao.L140M01ADao;
import com.mega.eloan.lms.dao.L140S02ADao;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.model.C101M01A;
import com.mega.eloan.lms.model.C101S01A;
import com.mega.eloan.lms.model.C101S01B;
import com.mega.eloan.lms.model.C101S01C;
import com.mega.eloan.lms.model.C101S01D;
import com.mega.eloan.lms.model.C101S01E;
import com.mega.eloan.lms.model.C101S01G;
import com.mega.eloan.lms.model.C101S01G_N;
import com.mega.eloan.lms.model.C101S01J;
import com.mega.eloan.lms.model.C101S01K;
import com.mega.eloan.lms.model.C101S01L;
import com.mega.eloan.lms.model.C101S01M;
import com.mega.eloan.lms.model.C101S01N;
import com.mega.eloan.lms.model.C101S01P;
import com.mega.eloan.lms.model.C101S01Q;
import com.mega.eloan.lms.model.C101S01Q_N;
import com.mega.eloan.lms.model.C101S01R;
import com.mega.eloan.lms.model.C101S01R_N;
import com.mega.eloan.lms.model.C101S02B;
import com.mega.eloan.lms.model.C101S02S;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.C120S01B;
import com.mega.eloan.lms.model.C120S01C;
import com.mega.eloan.lms.model.C120S01D;
import com.mega.eloan.lms.model.C120S01E;
import com.mega.eloan.lms.model.C120S01G;
import com.mega.eloan.lms.model.C120S01J;
import com.mega.eloan.lms.model.C120S01K;
import com.mega.eloan.lms.model.C120S01L;
import com.mega.eloan.lms.model.C120S01M;
import com.mega.eloan.lms.model.C120S01N;
import com.mega.eloan.lms.model.C120S01P;
import com.mega.eloan.lms.model.C120S01Q;
import com.mega.eloan.lms.model.C120S01R;
import com.mega.eloan.lms.model.C120S01Y;
import com.mega.eloan.lms.model.C120S02B;
import com.mega.eloan.lms.model.C120S02S;
import com.mega.eloan.lms.model.C900M01J;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01I;
import com.mega.eloan.lms.model.L120S09A;
import com.mega.eloan.lms.model.L120S09B;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140S02A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.ReportGenerator;
import tw.com.jcs.common.report.SubReportParam;

/**
 * <pre>
 * 借保人徵信資料
 * </pre>
 * 
 * @since 2012/11/12
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/11/12,Fantasy,new
 *          <li>2013/06/15,調整主借款人和配偶之利害關係人顯示排序問題
 *          <li>2013/07/11,Rex,修改顯示評等
 *          <li>2013/07/17,Rex,修改判斷非兆豐行庫改用判斷其帳戶為14碼的排除加總
 *          </ul>
 */
@Service("cls1131r01rptservice")
public class CLS1131R01RptServiceImpl implements FileDownloadService {

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(CLS1131R01RptServiceImpl.class);

	private Map<String, CapAjaxFormResult> container = new LinkedHashMap<String, CapAjaxFormResult>();

	private Map<String, String> keyMap = new LinkedHashMap<String, String>();


	private static final Class<?>[] C101_CLASS_RPT_1 = { C101S01D.class,
			C101M01A.class, C101S01A.class, C101S01B.class, C101S01C.class,
			C101S01E.class, C101S01G.class, C101S01J.class, C101S01Q.class, 
			C101S01R.class, C101S02B.class, C101S02S.class };

	private static final Class<?>[] C101_CLASS_RPT_2 = { C101S01K.class,
			C101S01L.class, C101S01N.class, C101S01M.class, C101S01P.class };

	private static final Class<?>[] C120_CLASS_RPT_1 = { C120S01D.class,
			C120M01A.class, C120S01A.class, C120S01B.class, C120S01C.class,
			C120S01E.class, C120S01G.class, C120S01J.class, C120S01Q.class, C120S01R.class,
			C120S02B.class, C120S02S.class};

	private static final Class<?>[] C120_CLASS_RPT_2 = { C120S01K.class,
			C120S01L.class, C120S01N.class, C120S01M.class, C120S01P.class };
	
	@Resource
	CodeTypeService codeTypeService;

	@Resource
	BranchService branchService;

	@Resource
	CLS1131Service service;

	@Resource
	CLS1151Service cls1151Service;
	@Resource
	L140M01ADao l140m01aDao;

	@Resource
	L140S02ADao l140s02aDao;
	
	@Resource
	MisCustdataService misCustdataService;

	@Resource
	CLSService clsService;
	
	@Resource
	C101S01YDao c101s01yDao;
	
	@Resource
	C120S01YDao c120s01yDao;
	
	@Resource
	SysParameterService sysparamService;
	
	@Resource
	ScoreService scoreService;
	
	@Resource
	C101S01G_NDao c101s01g_nDao;
	
	@Resource
	C101S01Q_NDao c101s01q_nDao;
	
	@Resource
	C101S01R_NDao c101s01r_nDao;
	
	
	
	/**
	 * 建立PDF
	 * 
	 * @param params
	 *            params
	 * @return OutputStream OutputStream
	 * @throws Exception
	 * @throws IOException
	 * @throws FileNotFoundException
	 */
	public OutputStream createReportData(PageParameters params)
			throws FileNotFoundException, IOException, Exception {
		init();
		Properties prop_cls1131 = MessageBundleScriptCreator.getComponentResource(CLS1131S01Panel.class);
		Properties prop_LMSCommomPage = MessageBundleScriptCreator.getComponentResource(LMSCommomPage.class);
		boolean simplePrint = params.getAsBoolean("simplePrint", false);
		if (simplePrint) {
			//簡易列印
			return genSimple(LMSUtil.getLocale(), params, prop_cls1131, prop_LMSCommomPage);
		} else {
			return gen(LMSUtil.getLocale(), params, prop_cls1131, prop_LMSCommomPage);
		}
	}

	/*
	 * (non-Javadoc) 呈現在頁面用的
	 * 
	 * @see
	 * com.mega.eloan.lms.base.service.FileDownloadService#getContent(org.apache
	 * .wicket.PageParameters)
	 */
	@Override
	public byte[] getContent(PageParameters params) throws Exception {
		ByteArrayOutputStream baos = null;
		try {
			baos = (ByteArrayOutputStream) this.createReportData(params);
		} finally {
			if (baos != null) {
				baos.close();
			}
		}
		return baos != null ? baos.toByteArray() : null;
	}

	/**
	 * 依分行別
	 * 
	 * @param locale
	 * @param params
	 * @return
	 * @throws FileNotFoundException
	 * @throws IOException
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public OutputStream gen(Locale locale, PageParameters params
		, Properties prop_cls1131, Properties prop_LMSCommomPage)
	throws FileNotFoundException, IOException, Exception {
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		List<Map<String, String>> rowsData = new LinkedList<Map<String, String>>();
		ReportGenerator generator = new ReportGenerator();
		OutputStream outputStream = null;
		Date oneMonthAfter = new Date();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		try {

			String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
			String custId = Util.trim(params.getString("custId"));
			String dupNo = Util.trim(params.getString("dupNo"));
			boolean isC120M01A = params.getBoolean("isC120M01A");
			if (isC120M01A) {
				L120M01A model = cls1151Service.findModelByMainId(
						L120M01A.class, mainId);
				if (model != null
						&& UtilConstants.Casedoc.DocCode.一般.equals(model
								.getDocCode())
						&& (CreditDocStatusEnum.海外_編製中.getCode().equals(
								model.getDocStatus()) || CreditDocStatusEnum.海外_待覆核
								.getCode().equals(model.getDocStatus()))) {
					/* XXX 為何在C101有勾選模型，但在 C120 的簽報書「編製中」列印時，未出現引用的模型
					 * 
					 * 沒有引用到的模型，就不印出 → 避免被干擾
					 * 
					 * 所以在「編製中」列印之前，會去判斷 L140S02A 是否有適用的模型
					 * ● 非主債務人(無 L140M01A)
					 * ● 非從債務人(無 L140S01A)
					 * ● 或 L140S02A 是免辦評等、或 性質[不變||取消]
					 * 則會把 C120M01A.markModel 回寫成空白，造成未印出模型
					 */ 
					cls1151Service.syncQuoteToC120M01A_markModel(
							model.getMainId(), true, null);
					
					clsService.syncL120M01I_bailout_flag(model);					
				}
			}
			Class<?> classes[] = (isC120M01A ? C120_CLASS_RPT_1 : C101_CLASS_RPT_1);

			HashMap<String, String> markModel_map = procMarkModel(classes,
					mainId, custId, dupNo, isC120M01A);
			boolean run_c101s01_G_Q_R = false;

			String memoC101M01A_custName = "";
			String memoC101M01A_ownBrId = "";
			String memoC101S01E_isQdata7 = "";
			String memoC101S01E_isQdata18 = "";
			String memoC101S01E_wm_data = "";
			//地政士
			String memoC101S01E_caseSrcFlag = "";
			String memoC101S01E_laaName = "";
			String memoC101S01E_laaYear = "";
			String memoC101S01E_laaWord = "";
			String memoC101S01E_laaNo = "";
			String memoC101S01E_laaOfficeId = "";
			String memoC101S01E_laaOffice = "";
			String memoC101S01E_caseSrcMemo = "";
			String memoC101S01E_agentPId = "";
			String memoC101S01E_agentPName = "";
			String memoC101S01E_laaDesc = "";
			String version = "";
			// J-113-0285 因應歡喜信貸審核需求，申請新增徵信及簽報書書面相關欄位
			boolean printClsJobType = false;
			
			Date birthday = null;
			
			for (Class<?> clazz : classes) {
				// 判斷TABLE不存在時是否產生NEW OBJECT
				// 預設舊案也有資料的TABLE=TRUE
				boolean isCreate = true;
				// 不一定有資料的TABLE=FALSE
				if (clazz == C101S01G.class || clazz == C120S01G.class
						|| clazz == C101S01Q.class || clazz == C120S01Q.class
						|| clazz == C101S01R.class || clazz == C120S01R.class
						|| clazz == C101S02B.class || clazz == C120S02B.class) {
					isCreate = false;
				}
				GenericBean bean = service.findModelByKey(clazz, mainId,
						custId, dupNo, isCreate);
				// change model by fantasy 2013/04/16
				GenericBean model = ClsUtil.changeModel(clazz, bean);

				if (model != null) {
					if(true){
						
						JSONObject json = DataParse.toJSON(model, true);
						
						if(clazz == C120S01E.class || clazz == C101S01E.class){
							version = json.getString("version");
						}
						
						boolean is1_0Version = false;
						if(clazz == C120S02S.class || clazz == C101S02S.class){
							is1_0Version = "1.0".equals(version) || StringUtils.isBlank(version);
						}

						Set<String> sets = (Set<String>) json.keySet();
						if (clazz == C101S01G.class || clazz == C101S01Q.class || clazz == C101S01R.class
							|| clazz == C120S01G.class || clazz == C120S01Q.class || clazz == C120S01R.class
							|| (clazz == C120S02S.class && is1_0Version) 
							|| (clazz == C101S02S.class && is1_0Version)) {
							
						} else {
							for (String key : sets) {
								String value = Util.trim(json.get(key));
								rptVariableMap.put(key, Util.trim(getValue(key, value)));
							}
						}
					}
					// 個金徵信借款人主檔
					if (model instanceof C101M01A) {
						C101M01A c101m01a = (C101M01A) model;

						memoC101M01A_custName = Util.trim(c101m01a.getCustName());
						memoC101M01A_ownBrId = Util.trim(c101m01a.getOwnBrId());
						if (Util.isEmpty(Util.trim(c101m01a.getBfRejCase()))){
							rptVariableMap.put("bfRejCase", "無");
						}
						rptVariableMap.put("rfRejBranchName", branchService
								.getBranchName(c101m01a.getBfRejBranch()));
						
						if (true){
							String abnormalInfoStr = "";
							if(c101m01a.getAbnormalReadDate()!=null){
								String abormalDate = Util.trim(TWNDate.toAD(c101m01a.getAbnormalDate()));
								String abnormalBrNo = Util.trim(c101m01a.getAbnormalBrNo());
								String abnormalStatus = Util.trim(c101m01a.getAbnormalStatus());
																
								String printName = "申貸戶";
								if(Util.isNotEmpty(abnormalBrNo)){
									//有
									String v = prop_cls1131.getProperty("rdo.have");
									
									String v_status = (Util.equals(UtilConstants.Casedoc.abnormalStatus.已解除, abnormalStatus)?prop_cls1131.getProperty("C101M01A.abnormalStatus.Y"):prop_cls1131.getProperty("C101M01A.abnormalStatus.N"));
									abnormalInfoStr = MessageFormat.format(prop_cls1131.getProperty("rpt.abnormalInfoStr_Y")
											, printName
											, v
											, abnormalBrNo+ Util.trim(branchService.getBranchName(abnormalBrNo))
											, abormalDate
											, v_status
										);
										
								}else{
									//無
									String v = prop_cls1131.getProperty("rdo.nohave");
									abnormalInfoStr = MessageFormat.format(prop_cls1131.getProperty("rpt.abnormalInfoStr_N")
											, printName
											, v
										);
								}										
							}	
							rptVariableMap.put("abnormalInfoStr", abnormalInfoStr);						
						}
						if(LMSUtil.isContainValue(c101m01a.getMarkModel(), "3")){//專案信貸有打勾
							printClsJobType = true;
						}
						
						//J-113-0341 新增「年輕族群客戶加強關懷提問單」
						rptVariableMap.put("youngCareResult1", this.getYesOrNo(Util.trim(c101m01a.getYoungCareResult1())));
						rptVariableMap.put("youngCareResult2_1", this.getYesOrNo(Util.trim(c101m01a.getYoungCareResult2_1())));
						rptVariableMap.put("youngCareResult2_2", this.getYesOrNo(Util.trim(c101m01a.getYoungCareResult2_2())));
						rptVariableMap.put("youngCareResult3_1", this.getYesOrNo(Util.trim(c101m01a.getYoungCareResult3_1())));
						rptVariableMap.put("youngCareResult3_2", this.getYesOrNo(Util.trim(c101m01a.getYoungCareResult3_2())));
						rptVariableMap.put("youngCareResult4_1", this.getYesOrNo(Util.trim(c101m01a.getYoungCareResult4_1())));
						rptVariableMap.put("youngCareResult4_2", this.getYesOrNo(Util.trim(c101m01a.getYoungCareResult4_2())));
						rptVariableMap.put("youngCareResult5", this.getYesOrNo(Util.trim(c101m01a.getYoungCareResult5())));
					}
					
					else if (model instanceof C101S01A) {
						C101S01A c101s01a = (C101S01A) model;
						birthday = c101s01a.getBirthday();
					}

					// 個金服務單位檔
					else if (model instanceof C101S01B) {
						// 設定職業別細項
						String value = null;
						String othTypes = "";
						int count = 0;
						C101S01B c101s01b = (C101S01B) model;
						Map<String, String> map = codeTypeService
								.findByCodeType("jobType"
										+ Util.trim(c101s01b.getJobType1()));
						Map<String, String> cls1131m01_othTypemap = codeTypeService
								.findByCodeType("cls1131m01_othType");
						if (map != null){
							value = map.get(Util.trim(c101s01b.getJobType2()));
						}
						if (cls1131m01_othTypemap != null) {
							for (String othtype : Util.trim(
									c101s01b.getOthType()).split(
									UtilConstants.Mark.SPILT_MARK)) {
								if (count == 0) {
									othTypes = cls1131m01_othTypemap
											.get(othtype);
									count++;
								} else {
									othTypes = othTypes
											+ "、"
											+ cls1131m01_othTypemap
													.get(othtype);
								}
							}
						}
						rptVariableMap.put("jobType2", Util.trim(value));
						rptVariableMap.put("othTypes", othTypes);
						rptVariableMap.put("workDate", CapDate.formatDate(c101s01b.getWorkDate(), "yyyy-MM-dd"));
						if(true){
							String seniority_desc = "";  //default{若空白}
							if(c101s01b.getSeniority()!=null){
								Integer[] seniorityYM_arr = ClsUtility.seniorityYM_decode(c101s01b.getSeniority());
								if(c101s01b.getSnrM()==null){
									seniority_desc = String.valueOf(seniorityYM_arr[0]);
								}else{
									seniority_desc = String.valueOf(seniorityYM_arr[0])+"年"+String.valueOf(seniorityYM_arr[1])+"月";
								}
							}							
							rptVariableMap.put("seniority", seniority_desc);
							rptVariableMap.put(ClsUtility.SNRDESC, seniority_desc);
						}
						if(true){
//							rptVariableMap.put("cm1_serve_company", Util.trim(c101s01b.getCm1_serve_company()));
							Map<String, String> _CM1_JOB_BUSINESS_CODE_map = codeTypeService.findByCodeType("CM1_JOB_BUSINESS_CODE");
							Map<String, String> _CM1_TITLE_CODE_map = codeTypeService.findByCodeType("CM1_TITLE_CODE");
							
							rptVariableMap.put("cm1_job_business_InfoStr", ClsUtil.getC101S01B_cm1_job_business_InfoStr(c101s01b, _CM1_JOB_BUSINESS_CODE_map));
							rptVariableMap.put("cm1_job_title_InfoStr", ClsUtil.getC101S01B_cm1_job_title_InfoStr(c101s01b, _CM1_TITLE_CODE_map));
							rptVariableMap.put("cm1_dataDt", CrsUtil.isNull_or_ZeroDate(c101s01b.getCm1_dataDt())?"":TWNDate.toAD(c101s01b.getCm1_dataDt()));
						}
						
						if(Util.equals("N", c101s01b.getYnJuId())){
//							rptVariableMap.put("juId", prop_cls1131.getProperty("rdo.nohave"));						
						}
						if(Util.equals("N", c101s01b.getYnJuTotalCapital())){
//							rptVariableMap.put("juTotalCapital", prop_cls1131.getProperty("rdo.nohave"));
						}else{
							String val = LMSUtil.convert_bigvalue(c101s01b.getJuTotalCapital());
							if(Util.isNotEmpty(val)){
								val = val+prop_cls1131.getProperty("money.unit.1");	
							}
							rptVariableMap.put("juTotalCapital", val);
						}
						if(Util.equals("N", c101s01b.getYnJuPaidUpCapital())){
//							rptVariableMap.put("juPaidUpCapital", prop_cls1131.getProperty("rdo.nohave"));
						}else{
							String val = LMSUtil.convert_bigvalue(c101s01b.getJuPaidUpCapital());
							if(Util.isNotEmpty(val)){
								val = val+prop_cls1131.getProperty("money.unit.1");
							}
							rptVariableMap.put("juPaidUpCapital", val);
						}
						
						if(true){
							String ptaFlag = Util.trim(c101s01b.getPtaFlag());
							String ptaInfoStr = "";
							if(Util.equals("", ptaFlag)){
								
							}else if(Util.equals("Y", ptaFlag)){
								String ptaGradeMsg = "";
								String ptaGrade = Util.trim(c101s01b.getPtaGrade());
								if(Util.isNotEmpty(ptaGrade)){
									ptaGradeMsg = prop_cls1131.getProperty("C101S01B.ptaGrade.descBf")+ptaGrade+prop_cls1131.getProperty("C101S01B.ptaGrade.descAf");
								}
								ptaInfoStr = prop_cls1131.getProperty("rdo.yes")+"   "
									+prop_cls1131.getProperty("C101S01B.ptaTaxNo.label")+"："+Util.trim(c101s01b.getPtaTaxNo())
									+ptaGradeMsg;
							}else if(Util.equals("N", ptaFlag)){
								ptaInfoStr = prop_cls1131.getProperty("rdo.no");
							}else{
								ptaInfoStr = ptaFlag;
							}
							rptVariableMap.put("ptaInfoStr", ptaInfoStr);
						}
						// J-113-0285 因應歡喜信貸審核需求，申請新增徵信及簽報書書面相關欄位，詳如附件
						String clsJobTitleDesc = ""; //歡喜信貸職業別
						String termGroupDesc = "";//歡喜信貸客群
						if(printClsJobType){
							CodeType clsJobTitle = codeTypeService
										.findByCodeTypeAndCodeValue("clsJobTitle",Util.trim(c101s01b.getClsJobTitle()));
							if(clsJobTitle != null &&
									Util.isNotEmpty(clsJobTitle)){
								clsJobTitleDesc = Util.trim(clsJobTitle.getCodeDesc());
							}
							
							Map<String,String> termGroupResultMap = 
								service.findTermGroup(mainId, custId, dupNo);
							if(termGroupResultMap!=null &&
									Util.isNotEmpty(termGroupResultMap)){
								if(Util.equals("Y", Util.trim(termGroupResultMap.get("haveResult")))
										&& Util.isEmpty(Util.trim(termGroupResultMap.get("errorMsg")))){
									CodeType termGroup = codeTypeService
										.findByCodeTypeAndCodeValue("L140S02A_termGroup",
												Util.trim(termGroupResultMap.get("termGroup")));
									if(termGroup!=null){
										termGroupDesc = Util.trim(termGroup.getCodeDesc());
									}else{
										if(Util.equals("X",
												Util.trim(termGroupResultMap.get("termGroup")))){
											//C101S01B.noTermGroup = 不可承做
											termGroupDesc = prop_cls1131.getProperty("C101S01B.noTermGroup");
										}
									}
									
									if(Util.equals("A", 
											Util.trim(termGroupResultMap.get("applyDBRType")))){
										//C101S01B.DBR15=(DBR上限15倍)
										termGroupDesc = termGroupDesc + 
												prop_cls1131.getProperty("C101S01B.DBR15");
									}
								}
							}
						}
						rptVariableMap.put("printClsJobType", printClsJobType ? "Y" : "N");
						if(Util.isNotEmpty(clsJobTitleDesc)){
							rptVariableMap.put("jobTitle", clsJobTitleDesc);
						}
						rptVariableMap.put("termGroupDesc", termGroupDesc);
					}
					// 票交所與聯徵特殊負面資訊
					else if (model instanceof C101S01G
							|| model instanceof C101S01Q
							|| model instanceof C101S01R) {
						if (run_c101s01_G_Q_R) {
							// 已跑過一次
						} else {
							run_c101s01_G_Q_R = true;
							// ---
							rptVariableMap.putAll(markModel_map);
						}
					}
					// 個金相關查詢資料檔
					else if (model instanceof C101S01E) {
						C101S01E c101s01e = (C101S01E) model;
						if (UtilConstants.haveNo.無.equals(c101s01e
								.getIsQdata3())
								&& UtilConstants.haveNo.無.equals(c101s01e
										.getIsQdata16())) {
							rptVariableMap.put("isQdata44", "無");
						}
						if(Util.isNotEmpty(c101s01e.getMbRlt33())){
							String mbRlt33 = Util.trim(c101s01e.getMbRlt33());
							
							if(Util.equals(UtilConstants.haveNo.有, mbRlt33)){
								mbRlt33 = prop_cls1131.getProperty("rdo.have");
							}else if(Util.equals(UtilConstants.haveNo.無, mbRlt33)){
								mbRlt33 = prop_cls1131.getProperty("rdo.nohave");
							}else if(Util.equals(UtilConstants.haveNo.NA, mbRlt33)){
								mbRlt33 = prop_cls1131.getProperty("rdo.notApplicable");
							}
							rptVariableMap.put("mbRlt33", mbRlt33);
						}
						memoC101S01E_isQdata7 = c101s01e.getIsQdata7();
						memoC101S01E_isQdata18 = c101s01e.getIsQdata18();
						if(c101s01e.getWm_qDate()!=null){
							String wm_data = ClsUtil.getC101S01E_wm_data(true, Util.trim(c101s01e.getWm_flag()), Util.trim(c101s01e.getWm_gra_name()));
							memoC101S01E_wm_data = wm_data+(Util.isEmpty(wm_data)?"":"。")+"    查詢日期："+TWNDate.toAD(c101s01e.getWm_qDate());	
						}
						
						memoC101S01E_caseSrcFlag = Util.trim(c101s01e.getCaseSrcFlag());
						memoC101S01E_laaName = Util.trim(c101s01e.getLaaName());
						memoC101S01E_laaYear = Util.trim(c101s01e.getLaaYear());
						memoC101S01E_laaWord = Util.trim(c101s01e.getLaaWord());
						memoC101S01E_laaNo = Util.trim(c101s01e.getLaaNo());
						memoC101S01E_laaOfficeId = Util.trim(c101s01e.getLaaOfficeId());
						memoC101S01E_laaOffice = Util.trim(c101s01e.getLaaOffice());
						memoC101S01E_caseSrcMemo = Util.trim(c101s01e.getCaseSrcMemo());
						memoC101S01E_laaDesc = Util.trim(c101s01e.getLaaDesc());
						if(true){
							//上面先已用 rptVariableMap.put(key, Util.trim(getValue(key, value)));
							
							Map<String, String> map = new HashMap<String, String>();
							map.put("L", prop_cls1131.getProperty("C101S01E.caseSrcFlag.L"));
							map.put("P", prop_cls1131.getProperty("C101S01E.caseSrcFlag.P"));
							map.put("O", prop_cls1131.getProperty("C101S01E.caseSrcFlag.O"));

							//選項增加 A買賣件經地政士辦理   B買賣件非經地政士辦理    C非買賣件
							//原L,P,O不再使用，但舊案還是要可以順利列印
							map.put("A", prop_cls1131.getProperty("C101S01E.caseSrcFlag.A"));
							map.put("B", prop_cls1131.getProperty("C101S01E.caseSrcFlag.B"));
							map.put("C", prop_cls1131.getProperty("C101S01E.caseSrcFlag.C"));
							// 判斷列印的標題是要印「進件來源」或是「地政士查詢」
							rptVariableMap.put("caseSrcFlagVer", memoC101S01E_caseSrcFlag.matches("[LPO]") ? "N" : "Y");

							rptVariableMap.put("caseSrcFlag", LMSUtil.getDesc(map, memoC101S01E_caseSrcFlag));
							
						}
						memoC101S01E_agentPId = Util.trim(c101s01e.getAgentPId());
						memoC101S01E_agentPName = Util.trim(c101s01e.getAgentPName());

                        Map<String, String> amlBadNewsCode = codeTypeService.findByCodeType("amlBadNewsCode",
                                locale.toString());
                        String amlBadNews = Util.trim(c101s01e.getAmlBadNews());
                        String creditBadNews = Util.trim(c101s01e.getCreditBadNews());
                        String[] array = amlBadNews.split("\\|");
                        StringBuilder sb = new StringBuilder();
                        for (String s : array) {
                            sb.append(Util.trim(amlBadNewsCode.get(s))).append("、");
                        }
                        if (sb.length() > 0) {
                            sb.deleteCharAt(sb.length() - 1);
                        }
                        rptVariableMap.put("amlBadNews", sb.toString());
                        rptVariableMap.put("creditBadNews", creditBadNews);

                    } else if (model instanceof C101S01J) {
						/* class 的順序,是先C101S01E, 再C101S01J */
						// if(UtilConstants.haveNo.有.equals(memoC101S01E_isQdata7)
						// && "04".equals(((C101S01J)model).getAns1())){
						// rptVariableMap.put("isQdata7", "疑似");
						// }

						C101S01J c101s01j = (C101S01J) model;
						String amlRefNo = Util.trim(c101s01j.getAmlRefNo());
						String amlRefOid = Util.trim(c101s01j.getAmlRefOid());
						String ans1 = Util.trim(c101s01j.getAns1());
						
						if(true){
							boolean use_old_fmt = true;
							L120S09B l120s09b = null;
							L120S09A l120s09a = null;
							if(clsService.active_SAS_AML(memoC101M01A_ownBrId)){
								l120s09b = clsService.findL120S09B_refNo_or_oid(amlRefNo, amlRefOid);
								l120s09a = clsService.findL120S09A_cls1131(l120s09b);
							}
							if(l120s09b!=null || l120s09a!=null){
								use_old_fmt = false;
							}
							
							if(use_old_fmt){
								if (UtilConstants.haveNo.有.equals(memoC101S01E_isQdata7)) {
									if (UtilConstants.Casedoc.L120s09aBlackListCode.是黑名單.equals(ans1)) {
										rptVariableMap.put("isQdata7", "存在於黑名單資料");
									} else if (UtilConstants.Casedoc.L120s09aBlackListCode.可能是黑名單.equals(ans1)) {
										rptVariableMap.put("isQdata7", "疑似黑名單");
									} else if ("14".equals(ans1)) {
										rptVariableMap.put("isQdata7", "疑似自行黑名單 請至0015-16 自行黑名單查詢");
									} else if ("61".equals(ans1)) {
										rptVariableMap.put("isQdata7", "疑似政治敏感人物名單 請至0015-61 政治敏感人物查詢");
									}
								}else if (UtilConstants.haveNo.NA.equals(memoC101S01E_isQdata7)) {
									//still show 'N.A'
								}
							}else{
								//J-109-0190_05097_B1001 為加速勞工紓困貸款之案件簽報，簡化e-Loan洗錢防制流程
								if (UtilConstants.haveNo.NA.equals(memoC101S01E_isQdata7)) {
									rptVariableMap.put("isQdata7", "尚未取得黑名單查詢結果");//AML
								}else{
									List<String> isQdata7_list = new ArrayList<String>();	
									//~~~~~~~~~~~~~~~~
									if(true){
										if(true){
											String ncResult = "";
											if(l120s09b!=null){
												ncResult = Util.trim(l120s09b.getNcResult());												
												if(Util.isNotEmpty(ncResult)){
													Map<String, String> map_ncResult = clsService.get_codeTypeWithOrder("SAS_NC_Result");
													if(map_ncResult.containsKey(ncResult)){
														ncResult = (ncResult+"-"+map_ncResult.get(ncResult));	
													}													
												}
											}	
											
											//L120S09B.ncResult=案件調查結果
											isQdata7_list.add(prop_cls1131.getProperty("L120S09B.ncResult")+"："+ncResult);
										}
										
										if(l120s09a!=null){
											String memo = Util.trim(l120s09a.getMemo());
											if(Util.isNotEmpty(memo)){
												//L120S09A.memo=命中代碼
												isQdata7_list.add(prop_cls1131.getProperty("L120S09A.memo")+"："+memo);
											}
										}																		
									}
									//~~~~~~~~~~~~~~~~
									rptVariableMap.put("isQdata7", StringUtils.join(isQdata7_list, "。"));
								}
								//J-113-0082 web e-Loan消金徵信新增告誡戶掃描功能
								String cmfwarnpResult = "";
								String cmfwarnpQueryResultInfo = "無";
								if(l120s09a!=null){
									cmfwarnpResult = Util.trim(l120s09a.getCmfwarnpResult());
									if (Util.equals(cmfwarnpResult, "1") && Util.isNotEmpty(l120s09a.getCmfwarnpQueryResultInfo())) {
										cmfwarnpQueryResultInfo = MessageFormat.format(prop_cls1131.getProperty("cmfwarnpQueryResultInfo")
												, l120s09a.getCustId()
												, l120s09a.getCustName()
												, Util.trim(l120s09a.getCmfwarnpQueryResultInfo())
										);
										//cmfwarnpQueryResultInfo = "借款人ID " + l120s09a.getCustId() + " " + l120s09a.getCustName() + "於" + Util.trim(l120s09a.getCmfwarnpQueryResultInfo()) + "被列為「洗錢防制法第十五條之二第六項帳戶帳號暫停限制功能或逕予關閉管理辦法」所稱「經裁處告誡者」，請於高風險欄位或綜合評估頁籤補充說明敘做理由。";
									} else if(Util.equals(cmfwarnpResult, "3")){
										cmfwarnpQueryResultInfo = prop_cls1131.getProperty("cmfwarnpQueryResultInfo.err");
									}
									rptVariableMap.put("cmfwarnpQueryResultInfo",cmfwarnpQueryResultInfo);
								}
							}
						}
						
						
						if (UtilConstants.haveNo.有.equals(memoC101S01E_isQdata18)) {
							rptVariableMap.put("isQdata18", ClsUtil.getC900M01EMsg(memoC101M01A_custName, c101s01j));
						}else if (UtilConstants.haveNo.無.equals(memoC101S01E_isQdata18)) {							
							rptVariableMap.put("isQdata18", Util.trim(getValue("isQdata18", memoC101S01E_isQdata18)));
						}
						
						if(true){
							rptVariableMap.put("c101s01e_wm_data", memoC101S01E_wm_data);
						}
					
						if(true){							
							List<String> msg = new ArrayList<String>();
							if (Util.equals("L", memoC101S01E_caseSrcFlag)) {
								
							}else if (Util.equals("P", memoC101S01E_caseSrcFlag)) {
								
							}else if (Util.equals("O", memoC101S01E_caseSrcFlag)
									|| Util.equals("B", memoC101S01E_caseSrcFlag)) {
								if(Util.isNotEmpty(memoC101S01E_caseSrcMemo)){
									msg.add( memoC101S01E_caseSrcMemo );
									msg.add("");
								}								
							}else{
								//若 caseSrcFlag 空白 , 表示是在 地政士黑名單 上線前的舊案
							}	
							
							if(true){
								// 地政士
								List<? extends GenericBean> so1ys = new ArrayList<GenericBean>();
								if (isC120M01A) {
									so1ys = c120s01yDao.findByList(mainId, custId, dupNo);
								} else {
									so1ys = c101s01yDao.findByList(mainId, custId, dupNo);
								}
								for (GenericBean s01y : so1ys) {
									memoC101S01E_laaName = CapString.trimNull(s01y.get("laaName"));
									memoC101S01E_laaYear = CapString.trimNull(s01y.get("laaYear"));
									memoC101S01E_laaWord = CapString.trimNull(s01y.get("laaWord"));
									memoC101S01E_laaNo = CapString.trimNull(s01y.get("laaNo"));
									memoC101S01E_laaOfficeId = CapString.trimNull(s01y.get("laaOfficeId"));
									memoC101S01E_laaOffice = CapString.trimNull(s01y.get("laaOffice"));
									memoC101S01E_laaDesc += CapString.trimNull(s01y.get("laaDesc"));
									if(Util.isNotEmpty(memoC101S01E_laaName)){
										msg.add(prop_cls1131.getProperty("C101S01E.laaName")+"："+memoC101S01E_laaName);	
									}
									if(CrsUtil.hasLaaCertData(memoC101S01E_laaYear, memoC101S01E_laaWord, memoC101S01E_laaNo)){
										msg.add(prop_cls1131.getProperty("C101S01E.label.laaCert")+"："
												+"("+memoC101S01E_laaYear+")"
												+memoC101S01E_laaWord+prop_cls1131.getProperty("C101S01E.label.laaWordPost")
												+memoC101S01E_laaNo+prop_cls1131.getProperty("C101S01E.label.laaNoPost"));
									}
									if(Util.isNotEmpty(memoC101S01E_laaOfficeId)){
										msg.add(prop_cls1131.getProperty("C101S01E.laaOfficeId")+"："+memoC101S01E_laaOfficeId);
									}
									if(CrsUtil.hasLaaOfficeData(memoC101S01E_laaOffice)){
										msg.add(prop_cls1131.getProperty("C101S01E.laaOffice")+"："+memoC101S01E_laaOffice);
									}
									if(true){
										String laaCtlFlag = CapString.trimNull(s01y.get("laaCtlFlag"));
										String laaMatchRuleFlag = CapString.trimNull(s01y.get("laaMatchRuleFlag"));
										if(Util.isNotEmpty(laaCtlFlag)){
											String cmpStr = "「地政士黑名單」檢核結果：";
											if(Util.equals(laaCtlFlag, "0")){											
												msg.add(cmpStr+"無");
											}else{
												C101S01J temp = new C101S01J();
												temp.setLaaCtlFlag(laaCtlFlag);
												temp.setLaaMatchRuleFlag(laaMatchRuleFlag);
												msg.add(cmpStr+clsService.msg_Laa_html(temp, prop_LMSCommomPage));								
												if(true){
													Map<String, String> descMap = clsService.get_codeTypeWithOrder(
															"cls260CtlFlagType",
															LocaleContextHolder.getLocale().toString());
													msg.add("控管原因："+LMSUtil.getDesc(descMap, laaCtlFlag));	
													//J-109-0140_10702_B1001 增加地政士黑名單控管邏輯
													if(Util.equals(clsService.getCtlFlagType(laaCtlFlag), LMSUtil.地政士黑名單警示名單)){
														msg.add("理由說明：" + memoC101S01E_laaDesc);
													}
												}
											}	
										}
									}
									msg.add("");
								}
								
								if(Util.isNotEmpty(memoC101S01E_agentPId)||Util.isNotEmpty(memoC101S01E_agentPName)){
									if(Util.isNotEmpty(memoC101S01E_agentPId)){
										String c900m01j_mainId = Util.trim(c101s01j.getAgentPIdCmp());
										C900M01J c900m01j = null;
										if(Util.isNotEmpty(c900m01j_mainId)){
											c900m01j = clsService.findC900M01J_mainId(c900m01j_mainId);
										}
										//=================
										msg.add(ClsUtil.msg_agentPIdCmp(memoC101S01E_agentPId, c900m01j, clsService.get_C900M01J_output_memo(c900m01j), prop_cls1131));										
									}
									if(Util.isNotEmpty(memoC101S01E_agentPName)){
										msg.add(prop_cls1131.getProperty("C101S01E.agentPName")+"："+memoC101S01E_agentPName);
									}
								}
							}							
							rptVariableMap.put("caseSrcFlagStr", StringUtils.join(msg, "<br/>"));
						}				
                    } else if (model instanceof C101S02B) {
                    	Map<String, String>  c101S02B_HML_map = codeTypeService
                    	.findByCodeType("C101S02B_HML");
                    	String[] cols = new String[] { "in_fraud_db",
                    			"phony_account_cluster", "agency_cluster",
                    			"reachable", "special_loan_cluster",
                    			"pawnshop_cluster", "debt_collect_cluster",
                    			"spec_career_cluster", "installment_cluster",
                    	"whitelist_cluster" };
                    	JSONObject json = DataParse.toJSON(model, true);
                    	for (String col : cols) {
                    		rptVariableMap.put(col, CapString.trimNull(c101S02B_HML_map.get(json.optString(col))));
                    	}
                    	Map<String, String>  c101S02B_LAA_map = codeTypeService.findByCodeType("C101S02B_LAA");
                    	rptVariableMap.put("suspicious_laa_cluster", CapString.trimNull(c101S02B_LAA_map.get(json.optString("suspicious_laa_cluster"))));
                    }//end if (model instanceof C101S...) {
				}
			}
			
			//J-113-0341 新增「年輕族群客戶加強關懷提問單」
			boolean J_113_0341_ON = clsService.is_function_on_codetype("J_113_0341_ON");
			if(J_113_0341_ON && birthday != null){
				int age = service.getAge(birthday);
				if(18 <= age && age < 22){
					rptVariableMap.put("showYoungCare", "Y");
				}
			}

			// 分行名稱
			String branchId = Util.trim(user.getUnitNo());
			String branchName = Util
					.trim(branchService.getBranchName(branchId));
			// rptVariableMap.put("BRANCHNAME", branchId + branchName);
			rptVariableMap.put("BRANCHNAME", branchName);
			rptVariableMap.put("QUERYEDATE", TWNDate.toAD(oneMonthAfter));
			rptVariableMap.put("PRINTDATE", TWNDate.toAD(new Date()));

			generator.setLang(locale);
			// set data rows
			generator.setRowsData(parseData(prop_cls1131, rptVariableMap, mainId, custId,
					dupNo, isC120M01A));
			generator.setVariableData(rptVariableMap);
			// ap
			generator.setReportFile("report/cls/CLS1131R01_"
					+ locale.toString() + ".rpt");

			outputStream = generator.generateReport();
		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
				rptVariableMap = null;
			}
			if (rowsData != null) {
				rowsData.clear();
				rowsData = null;
			}
		}

		return outputStream;
	}

	/**
	 * 解析為報表所使用之資料列
	 *
	 * @param prop_cls1131
	 * @param rptVariableMap
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @param isC120M01A
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	private List<Map<String, String>> parseData(Properties prop_cls1131, 
			Map<String, String> rptVariableMap, String mainId, String custId,
			String dupNo, boolean isC120M01A) throws CapException {

		int max = 50;
		long sumN = 0;
		BigDecimal total = BigDecimal.ZERO;
		boolean firstN = true; // 第一第歸戶資料
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		Map<String, String> rMap = new HashMap<String, String>();
		
		// 主借款人
		if (Util.isNotEmpty(custId)) {			
			rMap.put(custId, Util.trim(rptVariableMap.get("custName")));
		}
		// 配偶(B. 列於本欄)
		String mateFlag = Util.trim(rptVariableMap.get("mateFlag"));
		if ("B".equals(mateFlag)) {
			String mCustId = Util.trim(rptVariableMap.get("mCustId"));
			if (Util.isNotEmpty(mCustId)) {
				rMap.put(mCustId, Util.trim(rptVariableMap.get("mName")));
			}
		}else{
			Map<String, Object> custId_0024 = misCustdataService.findByIdDupNo(custId, dupNo);
			if (custId_0024 != null) {
				String mateid_0024 = Util.trim(custId_0024.get("MATEID"));
				if (Util.isNotEmpty(mateid_0024) && !custId.equals(mateid_0024)) {					
					String mateName = Util.trim(custId_0024.get("MATENM"));
					rMap.put(mateid_0024, mateName);
				}
			}
		}

		List<Map<String, String>> result = new LinkedList<Map<String, String>>();

		Class<?> classes[] = (isC120M01A ? C120_CLASS_RPT_2 : C101_CLASS_RPT_2);
		for (Class<?> clz : classes) {
			List<? extends GenericBean> list = service.findListByRelationKey(
					clz, mainId, custId, dupNo);
			Class<?> clazz = ClsUtil.changeClass(clz);
			String groupName = getGroupSign(clazz);

			if (clazz == C101S01L.class) {
				procC101S01L(result, "a1", list, custId, dupNo, groupName, rMap);
				procC101S01L(result, "a2", list, custId, dupNo, groupName, rMap);
				procC101S01L(result, "a3", list, custId, dupNo, groupName, rMap);
				continue;
			}

			if (list.isEmpty()) {
				Map<String, String> map = Util.setColumnMap();
				map.put("ReportBean.column01", groupName);
				// 主從債務人
				if (clazz == C101S01K.class) {
					map.put("ReportBean.column08", "無");
				}
				result.add(map);
			} else {
				for (GenericBean bean : list) {
					// change model by fantasy 2013/04/16
					GenericBean model = ClsUtil.changeModel(clazz, bean);
					JSONObject json = DataParse.toJSON(model, true);
					// int size = json.size() + 10;
					// Map<String, String> map = Util.setColumnMap(size);
					Map<String, String> map = Util.setColumnMap();
					map.put("ReportBean.column01", groupName);

					int count = 1;
					Set<String> sets = (Set<String>) json.keySet();
					for (String key : sets) {
						String value = Util.trim(json.get(key));

						map.put("ReportBean.column"
								+ Util.addZeroWithValue(
										String.valueOf(++count), 2),
								Util.trim(getValue(key, value)));
					}
					// 歸戶合計
					if (clazz == C101S01N.class) {
						// String bankNo = Util.trim(json.get("bankNo"));
						String bankName = "";
						if (firstN) {
							// bankName =
							// Util.trim(branchService.getBranchName(bankNo));
							map.put("ReportBean.column18",
									Util.trim(rptVariableMap.get("custName"))); // 客戶名稱
							firstN = false;
						} else {
							// bankName = getValue("BankCode01", bankNo);
							map.put("ReportBean.column04", ""); // 客戶ID
						}
						map.put("ReportBean.column19", bankName); // 銀行名稱
						//======================================================================
						/* 列印出的 PDF 頁面
						 	帳號 				額度					有擔保	 			無擔保				餘額(元)
						 						＊額度重複註記												(合計)
						 
							ReportBean.column07	ReportBean.column09	ReportBean.column11	ReportBean.column12	ReportBean.column13
							
						 ~~~ C101S01N begin ~~~
						 NAME          		COLTYPE   NULLS  LENGTH  SCALE   COLNO  REMARKS				***RPT***
						 ----------			-------	  -----  ------	 -----	 -----	---------------
						 OID				CHAR      N      32      0       0		oid      			column02                               
						 MAINID            	CHAR      N      32      0       1		文件編號                       	column03            
						 CUSTID      		VARCHAR   N      10      0       2		身分證統編                   	column04               
						 DUPNO             	CHAR      N      1       0       3		身分證統編重複碼    	column05                           
						 BANKNO          	CHAR      Y      3       0       4		分行別或銀行名稱             	column06                           
						 LOANNO           	VARCHAR   N      15      0       5		放款帳號                           	column07           
						 LOANTP           	VARCHAR   Y      8       0       6		(會計)科目                         	column08              
						 QUOTAPRV          	DECIMAL   Y      15      2       7		 額度                                      	column09  
						 QUOTADUP          	VARCHAR   Y      1       0       8		額度重複註記                       column10                       
						 BALS              	DECIMAL   Y      15      2       9		餘額(有擔保)      	column11 //抓查詢日(前一天日終)LNF150_LOAN_BAL                                       
						 BALN              	DECIMAL   Y      15      2       10		餘額(無擔保)        	column12 //依 lnap 是[有擔/無擔]                                  
						 LOANBAL          	DECIMAL   Y      15      2       11		餘額(合計)          	column13 //來填入[balS/balN]                                    
						 ~~~ C101S01N end ~~~																	 => 可能與 BTT L201 看到的 bal 不同	
						 	
						 */
						sumN += Util.parseLong(json.get("loanBal"));

						//======================================================================
						// 2013/07/17,Rex,修改判斷非兆豐行庫改用判斷其帳戶為14碼的排除加總
						String loanNo = Util.trim(map
								.get("ReportBean.column07"));
						// 變數 total 先加總 "非兆豐銀行" 金額
						// 之後 total 再於 isC120M01A 的判斷, 去累計 l140m01a .getLoanTotAmt()
						if (loanNo.length() != 14) {
							total = total.add(Util.parseBigDecimal(NumConverter
									.delComma(Util.trim(json.get("loanBal")))));
						}
					}
					if (clazz == C101S01P.class || clazz == C120S01P.class) {
						map.put("ReportBean.column21",
								Util.trim(model.get("comCustId")));
						map.put("ReportBean.column22",
								Util.trim(model.get("comName")));
						map.put("ReportBean.column23", NumConverter
								.addComma(Util.trim(model.get("fact_amt"))));
					}
					result.add(map);
				}
			}
		}
		rptVariableMap.put("isShowTotalAmt", "N");

		/*
		 * add by EL08034, 2013/09/12
		 * 
		 * 借款人加計本筆貸款後金融機構總授信金額=sum(他行)+sum(本行--->有14碼的loanNo)
		 * 
		 * 在[個金徵信]列印時, 只合計他行的數字(因本行的授信額度,隨本次的簽案來決定,尚不確定) 在[個金簽報書]列印時 當引入的額度序號,
		 * 狀態為 O, sum(本行)的數字仍為 0 當引入的額度序號, 狀態為 V(已執行過 計算授信額度合計, 由 O 變 V),
		 * sum(本行)的數字才有值
		 */
		if (isC120M01A) {
			// 歸戶合計
			List<L140M01A> l140m01as = l140m01aDao
					.findL140m01aListByL120m01cMainIdOrderByCust(mainId,
							UtilConstants.Cntrdoc.ItemType.額度明細表);
			for (L140M01A l140m01a : l140m01as) {
				if (custId.equals(l140m01a.getCustId())
						&& dupNo.equals(l140m01a.getDupNo())) {
					total = total.add(Util.parseBigDecimal(l140m01a
							.getLoanTotAmt()));

					rptVariableMap.put("isShowTotalAmt", "Y");
					break;
				}

			}
		}

		rptVariableMap.put("sumN", NumConverter.addComma(sumN));
		rptVariableMap.put("totalAmt", NumConverter.addComma(total));

		// 對同一自然人授信總餘額比率
		if (true) {
			Map<String, String> map = Util.setColumnMap(max);
			map.put("ReportBean.column01", "d");
			result.add(map);
		}
		//疑似偽造證件或財力證明
		if (Util.isNotEmpty(MapUtils.getString(rptVariableMap, "isQdata18"))) {
			Map<String, String> map = Util.setColumnMap(max);
			map.put("ReportBean.column01", "d2");
			result.add(map);
		}
		
		//J-107-0304 績優理財客戶
		if (Util.isNotEmpty(MapUtils.getString(rptVariableMap, "c101s01e_wm_data"))) {
			Map<String, String> map = Util.setColumnMap(max);
			map.put("ReportBean.column01", "d3");
			result.add(map);
		}
		
		//J-111-0146 大數據風險報告
		// 判斷查詢WITCHER_FIN是否為已開放分行，ALL代表全開放
        String branchs = sysparamService.getParamValue("WITCHER_FIN_BRANCHS");
        if ("ALL".equals(branchs) || Arrays.asList(branchs.split(",")).contains(user.getUnitNo())) {
        	Map<String, String> map = Util.setColumnMap(max);
        	map.put("ReportBean.column01", "d4");
        	result.add(map);
        }
		
		// 黑名單查詢
		if (true) {
			Map<String, String> map = Util.setColumnMap(max);
			map.put("ReportBean.column01", "g");
			result.add(map);
		}
		if (Util.isNotEmpty(MapUtils.getString(rptVariableMap, "mbRlt33"))) {
			Map<String, String> map = Util.setColumnMap(max);
			map.put("ReportBean.column01", "g1");			
			result.add(map);
		}
						
		if (Util.equals(prop_cls1131.getProperty("rdo.have"), MapUtils.getString(rptVariableMap, "mbRlt33"))) {
			Map<String, String> map = Util.setColumnMap(max);
			map.put("ReportBean.column01", "g2");			
			result.add(map);
		}else{
			Map<String, String> map = Util.setColumnMap(max);
			map.put("ReportBean.column01", "g2_none");			
			result.add(map);
		}
		
		if(true){
			if (Util.isNotEmpty(MapUtils.getString(rptVariableMap, "caseSrcFlag"))) {
				Map<String, String> map = Util.setColumnMap(max);
				map.put("ReportBean.column01", "g3");			
				result.add(map);
			}
			if (Util.isNotEmpty(MapUtils.getString(rptVariableMap, "caseSrcFlagStr"))) {
				Map<String, String> map = Util.setColumnMap(max);
				map.put("ReportBean.column01", "g3dtl");			
				result.add(map);
			}			
		}

        if(true){
            if (Util.isNotEmpty(MapUtils.getString(rptVariableMap, "amlBadNews"))) {
                Map<String, String> map = Util.setColumnMap(max);
                map.put("ReportBean.column01", "g4");
                result.add(map);
            }
        }

        if(true){
            if (Util.isNotEmpty(MapUtils.getString(rptVariableMap, "creditBadNews"))) {
                Map<String, String> map = Util.setColumnMap(max);
                map.put("ReportBean.column01", "g5");
                result.add(map);
            }
        }
		
		if(true){
			//免辦評等，但若有觸動聯徵負面資訊
			if(Util.isNotEmpty(rptVariableMap.get("alertMsg_0"))){
				Map<String, String> map = Util.setColumnMap(max);
				map.put("ReportBean.column01", "h0");
				result.add(map);				
			}
		}
		
		// 房貸模型, 非房貸模型
		if (Util.isNotEmpty(rptVariableMap.get("markModel_title"))
				&& Util.isNotEmpty(rptVariableMap.get("markModel_title_2"))) {
			Map<String, String> map = Util.setColumnMap(max);
			map.put("ReportBean.column01", "h2");
			result.add(map);
			
			_jcicFlag(result, rptVariableMap, max);
		} else if (Util.isNotEmpty(rptVariableMap.get("markModel_title"))
				|| Util.isNotEmpty(rptVariableMap.get("markModel_title_2"))) {
			Map<String, String> map = Util.setColumnMap(max);
			map.put("ReportBean.column01", "h1");
			result.add(map);
			
			_jcicFlag(result, rptVariableMap, max);
		}

		if(true){	
			//專案信貸(非團體)
			if (Util.isNotEmpty(rptVariableMap.get("markModel3_title"))){
				Map<String, String> map = Util.setColumnMap(max);
				map.put("ReportBean.column01", "hk"); //之前編到[hj1, hj2, hj3], 這裡用 j 後面的字母 k
				result.add(map);
			}
		}
		return result;
	}
	
	/** 報表裡有 [hj1, hj2, hj3] 的區段, 該借保人無信用卡及授信紀錄, 顯示不同時間點的訊息 
	 */
	private void _jcicFlag(List<Map<String, String>> result, Map<String, String> rptVariableMap, int max){
		String jcicFlg = MapUtils.getString(rptVariableMap, ClsUtility.C101M01A_JCICFLG);
		String jcicFlg_first2chars = StringUtils.substring(jcicFlg, 0, 2);
//		String jcicFlg_last2chars = StringUtils.substring(jcicFlg, 1, 3);
		//若是 NNN，應該優先適用判斷後2碼		
//		if(Util.equals("NN", jcicFlg_last2chars)){
//			Map<String, String> map = Util.setColumnMap(max);
//			map.put("ReportBean.column01", "hj2");
//			result.add(map);			
//		}else{ 
//			if(Util.equals("NN", jcicFlg_first2chars)){			
//				Map<String, String> map = Util.setColumnMap(max);
//				map.put("ReportBean.column01", "hj");
//				result.add(map);
//			}
//		}
		if(Util.equals("NN", jcicFlg_first2chars)){			
			Map<String, String> map = Util.setColumnMap(max);
			map.put("ReportBean.column01", "hj3");
			result.add(map);
		}
	
	}
	/**
	 * 配合報表的設定
	 * 
	 * @param type_in_rpt
	 *            為 利害關係人(銀行法,金控法44條,金控法45條){ "a1", "a2", "a3" };
	 */
	private void procC101S01L(List<Map<String, String>> result,
			String type_in_rpt, List<? extends GenericBean> list,
			String m01a_custId, String m01a_dupNo, String groupName, Map<String, String> rMap )
			throws CapException {

		LinkedHashMap<String, Map<String, String>> id_dataMap = new LinkedHashMap<String, Map<String, String>>();
		{			
			for (GenericBean bean : list) {
				GenericBean model = ClsUtil.changeModel(C101S01L.class, bean);				
				String xType = Util.trim(model.get("xType"));
				if (Util.notEquals(type_in_rpt, groupName + xType)) {
					continue;
				}
				if (Util.equals(_query_id(model), m01a_custId)) {				
					_add_S01L(id_dataMap, type_in_rpt, model, rMap);				
					break;
				}

			}
			// 第1筆顯示主借款人
			if (MapUtils.isEmpty(id_dataMap)) {				
				C101S01L model = new C101S01L();
				model.setCustId(m01a_custId);
				model.setXCustId(m01a_custId);
				model.setXCustName(LMSUtil.getDesc(rMap, m01a_custId));
				model.setQCustId(m01a_custId);
				model.setRelvl("0");//無
				_add_S01L(id_dataMap, type_in_rpt, model, rMap);
			}
		}

		for (GenericBean bean : list) {
			GenericBean model = ClsUtil.changeModel(C101S01L.class, bean);
			String xType = Util.trim(model.get("xType"));
			if (Util.notEquals(type_in_rpt, groupName + xType)) {
				continue;
			}

			_add_S01L(id_dataMap, type_in_rpt, model, rMap);			
		}
		if(MapUtils.isNotEmpty(id_dataMap)){
			result.addAll(id_dataMap.values());
		}
	}
	
	private String _query_id(GenericBean bean) throws CapException{
		String qCustId = Util.trim(bean.get("qCustId"));
		if(Util.isEmpty(qCustId)){
			qCustId = Util.trim(bean.get("custId"));
		}
		return qCustId;
	}

	private void _add_S01L(LinkedHashMap<String, Map<String, String>> id_dataMap
			, String type_in_rpt, GenericBean bean, Map<String, String> rMap) throws CapException{

		String qCustId = _query_id(bean);
		if (id_dataMap.containsKey(qCustId)) {
			return;
		}
		
		Map<String, String> map = Util.setColumnMap();
		map.put("ReportBean.column01", type_in_rpt);
		if(true){			
			map.put("ReportBean.column07", qCustId);
			map.put("ReportBean.column09", LMSUtil.getDesc(rMap, qCustId));
			if(true){
				String relvl = Util.trim(bean.get("relvl"));
				if(Util.equals(relvl, "0")){
					/*
					 會把 relvl 的代碼
					 用 select * from com.bcodetype where codetype='lms_relvl' 的中文值來替代 
					 而 lms_relvl 沒有 0 的代碼
					*/
					map.put("ReportBean.column10", "無");
					map.put("ReportBean.column11", "");
					map.put("ReportBean.column12", "");
					map.put("ReportBean.column13", "");
				}else{
					map.put("ReportBean.column10", getValue("relvl", relvl));
					map.put("ReportBean.column11", getValue("rectl", Util.trim(bean.get("rectl"))));
					map.put("ReportBean.column12", getValue("relcd", Util.trim(bean.get("relcd"))));
					map.put("ReportBean.column13", Util.trim(bean.get("xCustId")));
				}					
			}
		}
		id_dataMap.put(qCustId, map);
	}
	
	/**
	 * @param clazz
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	private String getGroupSign(Class clazz) {
		String result = "";
		// 個金相關查詢主從債務人檔
		if (C101S01K.class == clazz) {
			result = "e";
		}
		// 個金相關查詢利害關係人檔
		else if (C101S01L.class == clazz) {
			result = "a";
		}
		// 個金相關查詢聯徵非Z類檔
		else if (C101S01M.class == clazz) {
			result = "f";
		}
		// 個金相關查詢授信歸戶檔
		else if (C101S01N.class == clazz) {
			result = "c";
		}
		// 個金相關查詢所擔任負責人或董監事之企業是否於本行有授信額度達一億元以上名單
		else if (C101S01P.class == clazz || C120S01P.class == clazz) {
			result = "f2";
		}
		return result;
	}

	private void init() {
		if (keyMap == null)
			keyMap = new LinkedHashMap<String, String>();
		keyMap.clear();
		// set key

		keyMap.put("edu", "cls1131m01_edu"); // 學歷
		keyMap.put("marry", "marry"); // 婚姻狀況
		keyMap.put("jobType1", "jobType"); // 職業別
		// keyMap.put("jobType2", "jobType"); // 職業別 jobType
		keyMap.put("jobTitle", "lms1205s01_jobTitle"); // 職稱
		keyMap.put("inDoc", "lms1205s01_inDoc"); // 個人所得證明文件
		keyMap.put("oIncome", "cls1131m01_othType"); // 其他收入
		keyMap.put("yIncomeCert", "lms1205s01_inDoc"); // 家庭所得證明文件
		keyMap.put("isPeriodFund", "Common_YesNo"); // 是否於本行財富管理有定時定額扣款
		keyMap.put("busi", "Common_YesNo2"); // 與本行其他業務往
		keyMap.put("eChkFlag", "HaveNoNa"); // 有無票信退補記錄
		keyMap.put("eJcicFlag", "HaveNoNa"); // 有無聯徵逾催呆記錄
		keyMap.put("isFromOld", "Common_YesNo"); // 引進原舊案資料
		keyMap.put("bfRejCase", "lms_rejCase"); // 前婉卻狀態
		keyMap.put("rejectCase", "lms_rejCase"); // 現婉卻狀態
		keyMap.put("relvl", "lms_relvl"); // 關係層級
		keyMap.put("rectl", "lms_rectl"); // 銀行法控管對象別
		keyMap.put("relcd", "lms_relcd"); // 授信影響等級
		keyMap.put("cstRd", "Common_YesNo"); // 是否有強制停用記錄
		keyMap.put("isQdata12", "YesNoNa"); // 身分證補、換發紀錄
		keyMap.put("isQdata15", "YesNoNa"); // 成年監護制度查詢紀錄
		keyMap.put("ratio2", "cms1010_hateMark"); // 有無超過本行淨值百分之三
		keyMap.put("ratio1", "cms1010_hateMark"); // 有無超過本行淨值百分之一
		keyMap.put("othType", "cls1131m01_othType");
		keyMap.put("isQdata1", "HaveNoNa"); // 婉卻紀錄
		keyMap.put("isQdata2", "HaveNoNa"); // 本行利害關係人
		keyMap.put("isQdata3", "HaveNoNa"); // 金控利害關係人(44條)
		keyMap.put("isQdata16", "HaveNoNa"); // 金控利害關係人(45條)
		keyMap.put("isQdata6", "HaveNoNa"); // 主從債務人(不含本次資料)
		keyMap.put("isQdata5", "HaveNoNa"); // 對同一自然人授信總餘額比率
		keyMap.put("isQdata4", "HaveNoNa"); // 歸戶(本行餘額為a-Loan資料、他行餘額為聯徵資料)
		keyMap.put("isQdata14", "HaveNoNa"); // 近一年內不含查詢當日非Z類被聯行查詢紀錄明細
		keyMap.put("isQdata7", "HaveNoNa"); // 黑名單
		keyMap.put("isQdata8", "HaveNoNa"); // 證券暨期貨違約交割紀錄
		keyMap.put("isQdata9", "HaveNoNa"); // 退票紀錄
		keyMap.put("isQdata10", "HaveNoNa"); // 拒絕往來紀錄
		keyMap.put("isQdata11", "HaveNoNa"); // 主債務逾期、催收、呆帳紀錄
		keyMap.put("isQdata13", "HaveNoNa"); // 信用卡強停紀錄
		keyMap.put("isQdata17", "HaveNoNa"); // 擔任負責人或董監事之企業是否於本行有授信額度達一億元以上
		keyMap.put("isQdata18", "HaveNoNa"); // 疑似偽造證件或財力證明
		keyMap.put(ClsConstants.C101S01E.異常通報紀錄, "HaveNoNa");
		// keyMap.put("bankNo", "BankCode01");
		keyMap.put("BankCode01", "BankCode01");
		
		keyMap.put("juType", "C101S01B_juType");
		
		// get codeType key value
		if (container == null)
			container = new LinkedHashMap<String, CapAjaxFormResult>();
		container.clear();

		Collection<String> collection = keyMap.values();
		if (collection != null) {
			String[] keys = collection.toArray(new String[] {});
			container = codeTypeService.findByCodeType(keys);
		}
	}

	private String getValue(String key, String value) {
		Object result = null;
		if (key != null && keyMap != null) {
			String codeTypeKey = keyMap.get(key);
			if (codeTypeKey != null) {
				CapAjaxFormResult cafr = container.get(codeTypeKey);
				if (cafr != null)
					result = cafr.get(value);
			}
		}
		return Util.trim(result != null ? result : value);
	}

	private HashMap<String, String> procMarkModel(Class<?> classes[],
			String mainId, String custId, String dupNo, boolean isC120M01A) throws CapException {
		HashMap<String, String> r = new HashMap<String, String>();
		
		String markModel_alertMsg_0 = "";
		String markModel_grdCDate_0 = "";
		
		String markModel_varVer = "";
		String markModel_title = "";
		String markModel_grdCDate = "";
		String markModel_grade1 = "";
		String markModel_alertMsg = "";
		String markModel_adjustStatus = "";
		String markModel_adjustReason = "";
		String markModel_grade3 = "";

		String markModel_varVer_2 = "";
		String markModel_title_2 = "";
		String markModel_grdCDate_2 = "";
		String markModel_grade1_2 = "";
		String markModel_alertMsg_2 = "";
		String markModel_adjustStatus_2 = "";
		String markModel_adjustReason_2 = "";
		String markModel_grade3_2 = "";

		// 專案信貸(非團體) 比 非房貸 多顯示{聯徵J10個人信用評分、模型支援評等}
		String markModel3_varVer = "";
		String markModel3_title = "";
		String markModel3_grdCDate = "";
		String markModel3_grade1 = "";
		String markModel3_alertMsg = "";
		String markModel3_adjustStatus = "";
		String markModel3_adjustReason = "";
		String markModel3_grade3 = "";
		String markModel3_sprtRating = "";
		String markModel3_j10_score = "";
		String markModel3_j10_reason = "";
		
		//雙軌模型使用
		String markModel_varVer_N = "";
		String markModel_title_N = "";
		String markModel_notes_N = "";
		String markModel_grdCDate_N = "";
		String markModel_grade1_N = "";
		String markModel_alertMsg_N = "";
		String markModel_grade3_N = "";

		String markModel_varVer_2_N = "";
		String markModel_title_2_N = "";
		String markModel_notes_2_N = "";
		String markModel_grdCDate_2_N = "";
		String markModel_grade1_2_N = "";
		String markModel_alertMsg_2_N = "";
		String markModel_grade3_2_N = "";

		// 專案信貸(非團體) 比 非房貸 多顯示{聯徵J10個人信用評分、模型支援評等}
		String markModel3_varVer_N = "";
		String markModel3_title_N = "";
		String markModel3_notes_N = "";
		String markModel3_grdCDate_N = "";
		String markModel3_grade1_N = "";
		String markModel3_alertMsg_N = "";
		String markModel3_grade3_N = "";
		String markModel3_sprtRating_N = "";
		String markModel3_j10_score_N = "";
		String markModel3_j10_reason_N = "";
		
		String showSDT_Area1 = "N";
		String showSDT_Area2 = "N";
		String showSDT_Area3 = "N";
		
		C101M01A c101m01a = null;
		C101S01G c101s01g = null;
		C101S01Q c101s01q = null;
		C101S01R c101s01r = null;
		/*
		 * 分以下的狀況 1. 只有房貸模型上線, 無 C101S01Q 2. 只有房貸模型上線, 有 C101S01Q 藏在背後(列印時,不能用 Q
		 * 蓋掉 G 的值) 3. 房貸模型、及非房貸模型上線
		 */
		boolean oldCase = false;
		boolean scoreDoubleTrack = scoreService.scoreDoubleTrack(); //雙軌
		// ---		
		String bailout_alertMsg = "";
		String bailout_q6to10Msg = "";
		if(isC120M01A){
			L120M01I l120m01i = clsService.findL120M01I_mainId(mainId);
			bailout_alertMsg = OverSeaUtil.build_l120m01i_bailout_flag(l120m01i);
			//~~~~~~
			bailout_q6to10Msg = Util.trim(l120m01i!=null?l120m01i.getBailout_flag2():"");
		}
		// ---
		for (Class<?> clazz : classes) {
			if (clazz == C101M01A.class || clazz == C101S01G.class || clazz == C101S01Q.class || clazz == C101S01R.class 
				|| clazz == C120M01A.class || clazz == C120S01G.class || clazz == C120S01Q.class || clazz == C120S01R.class) {

				boolean findModelByKey_create = true;			
				//若無專案信貸(非團體)評等, 避免 service.findModelByKey(clazz, mainId, custId, dupNo, true); 最後1個參數, 抓到1個 new object
				if(    clazz == C101S01Q.class ||  clazz == C120S01Q.class
					|| clazz == C101S01R.class ||  clazz == C120S01R.class){
					findModelByKey_create =  false;
				}				
				
				GenericBean bean = service.findModelByKey(clazz, mainId,
						custId, dupNo, findModelByKey_create);
				if (clazz == C120M01A.class) {
					oldCase = "Y".equals(((C120M01A) bean).getModelTyp());
				}
				GenericBean model = ClsUtil.changeModel(clazz, bean);

				if (model != null) {
					if (clazz == C101M01A.class || clazz == C120M01A.class) {
						c101m01a = (C101M01A) model;
					} else if (clazz == C101S01G.class
							|| clazz == C120S01G.class) {
						c101s01g = (C101S01G) model;
					} else if (clazz == C101S01Q.class
							|| clazz == C120S01Q.class) {
						c101s01q = (C101S01Q) model;
					} else if (clazz == C101S01R.class
							|| clazz == C120S01R.class) {
						c101s01r = (C101S01R) model;
					}
				}
			}
		}
		

		if (Util.equals(UtilConstants.L140S02AModelKind.免辦,
				c101m01a.getMarkModel())) {
			if ( c101s01g != null) {
				String c101s01g_grdCDate = Util.trim(getValue(
						"grdCDate",
						Util.trim(DataParse.toJSON(c101s01g, true).get(
								"grdCDate"))));				
				
				markModel_grdCDate_0 = c101s01g_grdCDate;
				
				markModel_alertMsg_0 = firstNotEmptyStr(
						clsService.getClsGradeMessage(c101s01g,
								UtilConstants.L140S02AModelKind.房貸, OverSeaUtil.TYPE_RPT, bailout_alertMsg),
					"");
			}
		} else {
			boolean use_g = ClsUtil.isQuote(c101m01a,
					UtilConstants.L140S02AModelKind.房貸)
					&& c101s01g != null
					&& ClsUtil.showMarkModelInfoByQuote(c101s01g.getQuote());

			if (oldCase && c101s01g != null) {
				use_g = true;
			}
			boolean use_q = ClsUtil.isQuote(c101m01a,
					UtilConstants.L140S02AModelKind.非房貸)
					&& c101s01q != null
					&& ClsUtil.showMarkModelInfoByQuote(c101s01q.getQuote());
			
			boolean use_r = ClsUtil.isQuote(c101m01a,
					UtilConstants.L140S02AModelKind.卡友貸)
					&& c101s01r != null
					&& Util.isNotEmpty(Util.trim(c101s01r.getGrade3())) //缺J10時，即使有 C101S01R，但 grade3 一定是空的
					&& ClsUtil.showMarkModelInfoByQuote(c101s01r.getQuote());
			//========================
			String c101s01g_grdCDate = "";
			String c101s01q_grdCDate = "";
			String c101s01r_grdCDate = "";			
			
			
			if (use_g) {
				c101s01g_grdCDate = Util.trim(getValue(
						"grdCDate",
						Util.trim(DataParse.toJSON(c101s01g, true).get(
								"grdCDate"))));
			}
			if (use_q) {
				c101s01q_grdCDate = Util.trim(getValue(
						"grdCDate",
						Util.trim(DataParse.toJSON(c101s01q, true).get(
								"grdCDate"))));
			}
			if (use_r) {
				c101s01r_grdCDate = Util.trim(getValue(
						"grdCDate",
						Util.trim(DataParse.toJSON(c101s01r, true).get(
								"grdCDate"))));
			}
			
			if(use_r && Util.isNotEmpty(c101s01r_grdCDate)){
				markModel3_title = "非房屋貸款申請信用評等:專案信貸(非團體)資訊";
				markModel3_grdCDate = c101s01r_grdCDate;
				markModel3_varVer = c101s01r.getVarVer() + this.getModelSpan("R", c101s01r.getVarVer());
				markModel3_grade1 = ClsUtil.showGradeWithRatingDesc(
						UtilConstants.L140S02AModelKind.卡友貸,
						c101s01r.getGrade1());
				//J-111-0651 無聯徵警訊 >> 無信用不良限制及聯徵繳款資訊限制
				markModel3_alertMsg = firstNotEmptyStr(
						clsService.getClsGradeMessage(c101s01r,
						UtilConstants.L140S02AModelKind.卡友貸, OverSeaUtil.TYPE_RPT, bailout_alertMsg),
						"無信用不良限制及聯徵繳款資訊限制");
				markModel3_adjustStatus = firstNotEmptyStr(
						ClsUtil.getAdjustStatus(c101s01r), "無調整評等");
				markModel3_adjustReason = firstNotEmptyStr(
						c101s01r.getAdjustReason(), "無調整理由");
				markModel3_grade3 = ClsUtil.showGradeWithRatingDesc(
						UtilConstants.L140S02AModelKind.卡友貸,
						c101s01r.getGrade3());
				//增加專案信貸(非團體)提示資訊
				markModel3_grade3 = s01r_out_authority(markModel3_grade3, isC120M01A, mainId, custId, dupNo, bailout_q6to10Msg);
				markModel3_sprtRating = ClsUtil.showGradeWithRatingDesc(
						UtilConstants.L140S02AModelKind.卡友貸,
						c101s01r.getSprtRating());
				markModel3_j10_score = Util.trim(LMSUtil.getKCS003_J10(c101s01r.getJ10_score()));
				markModel3_j10_reason = Util.trim(LMSUtil.getKCS003Reason(c101s01r, "<br/>"));
				
				if(scoreDoubleTrack){ //雙軌運行中
					markModel3_title_N = "非房屋貸款申請信用評等:專案信貸(非團體)資訊";
					markModel3_notes_N = "專案信貸(非團體)模型4.0尚未正式啟用，僅供參考";
					C101S01R_N c101s01r_n = c101s01r_nDao.findByUniqueKey(c101s01r.getMainId(), c101s01r.getOwnBrId(), c101s01r.getCustId(), c101s01r.getDupNo());
					if(c101s01r_n != null){
						markModel3_varVer_N = c101s01r_n.getVarVer() + this.getModelSpan("R", c101s01r_n.getVarVer());
						markModel3_grdCDate_N = c101s01r_grdCDate;
						markModel3_grade1_N = ClsUtil.showGradeWithRatingDesc(
								UtilConstants.L140S02AModelKind.卡友貸,
								c101s01r_n.getGrade1());
						//J-111-0651 無聯徵警訊 >> 無信用不良限制及聯徵繳款資訊限制
						markModel3_alertMsg_N = firstNotEmptyStr(
								clsService.getClsGradeMessage(c101s01r_n,
								UtilConstants.L140S02AModelKind.卡友貸, OverSeaUtil.TYPE_RPT, bailout_alertMsg),
								"無信用不良限制及聯徵繳款資訊限制");
						markModel3_grade3_N = ClsUtil.showGradeWithRatingDesc(
								UtilConstants.L140S02AModelKind.卡友貸,
								c101s01r_n.getGrade3());
						markModel3_grade3_N = s01r_out_authority(markModel3_grade3_N, isC120M01A, mainId, custId, dupNo, bailout_q6to10Msg);
						markModel3_sprtRating_N = ClsUtil.showGradeWithRatingDesc(
								UtilConstants.L140S02AModelKind.卡友貸,
								c101s01r_n.getSprtRating());
						markModel3_j10_score_N = Util.trim(LMSUtil.getKCS003_J10(c101s01r_n.getJ10_score()));
						markModel3_j10_reason_N = Util.trim(LMSUtil.getKCS003Reason(c101s01r_n, "<br/>"));	
						showSDT_Area3 = "Y";
					}
					
				}
			}
			
			if (use_g == true && use_q == false) {
				markModel_title = "房屋貸款申請信用評等資訊";

				if (Util.isNotEmpty(c101s01g_grdCDate)) {
					markModel_varVer = c101s01g.getVarVer() + this.getModelSpan("G", c101s01g.getVarVer());
					markModel_grdCDate = c101s01g_grdCDate;
					markModel_grade1 = ClsUtil.showGradeWithRatingDesc(
							UtilConstants.L140S02AModelKind.房貸,
							c101s01g.getGrade1());
					//J-111-0651 無聯徵警訊 >> 無信用不良限制及聯徵繳款資訊限制
					markModel_alertMsg = firstNotEmptyStr(
							clsService.getClsGradeMessage(c101s01g,
									UtilConstants.L140S02AModelKind.房貸, OverSeaUtil.TYPE_RPT, bailout_alertMsg),
							"無信用不良限制及聯徵繳款資訊限制");
					markModel_adjustStatus = firstNotEmptyStr(
							ClsUtil.getAdjustStatus(c101s01g), "無調整評等");
					markModel_adjustReason = firstNotEmptyStr(
							c101s01g.getAdjustReason(), "無調整理由");
					markModel_grade3 = ClsUtil.showGradeWithRatingDesc(
							UtilConstants.L140S02AModelKind.房貸,
							c101s01g.getGrade3());
					
					if(scoreDoubleTrack){ //雙軌運行中
						markModel_title_N = "房屋貸款申請信用評等資訊";
						markModel_notes_N = "房貸模型3.0尚未正式啟用，僅供參考";
						C101S01G_N c101s01g_n = c101s01g_nDao.findByUniqueKey(c101s01g.getMainId(), c101s01g.getOwnBrId(), c101s01g.getCustId(), c101s01g.getDupNo());
						if(c101s01g_n != null){
							markModel_varVer_N = c101s01g_n.getVarVer() + this.getModelSpan("G", c101s01g_n.getVarVer());
							markModel_grdCDate_N = c101s01g_grdCDate;
							markModel_grade1_N = ClsUtil.showGradeWithRatingDesc(
									UtilConstants.L140S02AModelKind.房貸,
									c101s01g_n.getGrade1());
							//J-111-0651 無聯徵警訊 >> 無信用不良限制及聯徵繳款資訊限制
							markModel_alertMsg_N = firstNotEmptyStr(
									clsService.getClsGradeMessage(c101s01g_n,
											UtilConstants.L140S02AModelKind.房貸, OverSeaUtil.TYPE_RPT, bailout_alertMsg),
									"無信用不良限制及聯徵繳款資訊限制");
							markModel_grade3_N = ClsUtil.showGradeWithRatingDesc(
									UtilConstants.L140S02AModelKind.房貸,
									c101s01g_n.getGrade3());
							showSDT_Area1 = "Y";
						}
					}
				}
			} else if (use_g == false && use_q == true) {
				markModel_title = "非房屋貸款申請信用評等資訊";

				if (Util.isNotEmpty(c101s01q_grdCDate)) {
					markModel_varVer = c101s01q.getVarVer() + this.getModelSpan("Q", c101s01q.getVarVer());;
					markModel_grdCDate = c101s01q_grdCDate;
					markModel_grade1 = ClsUtil.showGradeWithRatingDesc(
							UtilConstants.L140S02AModelKind.非房貸,
							c101s01q.getGrade1());
					//J-111-0651 無聯徵警訊 >> 無信用不良限制及聯徵繳款資訊限制
					markModel_alertMsg = firstNotEmptyStr(
							clsService.getClsGradeMessage(c101s01q,
									UtilConstants.L140S02AModelKind.非房貸, OverSeaUtil.TYPE_RPT, bailout_alertMsg),
							"無信用不良限制及聯徵繳款資訊限制");
					markModel_adjustStatus = firstNotEmptyStr(
							ClsUtil.getAdjustStatus(c101s01q), "無調整評等");
					markModel_adjustReason = firstNotEmptyStr(
							c101s01q.getAdjustReason(), "無調整理由");
					markModel_grade3 = ClsUtil.showGradeWithRatingDesc(
							UtilConstants.L140S02AModelKind.非房貸,
							c101s01q.getGrade3());
					//增加非房貸6~10提示資訊
					markModel_grade3 = grade_6to10_appendAlertMsg(markModel_grade3, isC120M01A, mainId, custId, dupNo, bailout_q6to10Msg);
					
					if(scoreDoubleTrack){ //雙軌運行中
						markModel_title_N = "非房屋貸款申請信用評等資訊";
						markModel_notes_N = "非房貸模型4.0尚未正式啟用，僅供參考";
						C101S01Q_N c101s01q_n = c101s01q_nDao.findByUniqueKey(c101s01q.getMainId(), c101s01q.getOwnBrId(), c101s01q.getCustId(), c101s01q.getDupNo());
						if(c101s01q_n != null){
							markModel_varVer_N = c101s01q_n.getVarVer() + this.getModelSpan("Q", c101s01q_n.getVarVer());;
							markModel_grdCDate_N = c101s01q_grdCDate;
							markModel_grade1_N = ClsUtil.showGradeWithRatingDesc(
									UtilConstants.L140S02AModelKind.非房貸,
									c101s01q_n.getGrade1());
							//J-111-0651 無聯徵警訊 >> 無信用不良限制及聯徵繳款資訊限制
							markModel_alertMsg_N = firstNotEmptyStr(
									clsService.getClsGradeMessage(c101s01q_n,
											UtilConstants.L140S02AModelKind.非房貸, OverSeaUtil.TYPE_RPT, bailout_alertMsg),
									"無信用不良限制及聯徵繳款資訊限制");
							markModel_grade3_N = ClsUtil.showGradeWithRatingDesc(
									UtilConstants.L140S02AModelKind.非房貸,
									c101s01q_n.getGrade3());
							showSDT_Area1 = "Y";
						}
					}
				}
			} else if (use_g == true && use_q == true) {
				// 同時有房貸、非房貸
				markModel_title = "房屋貸款申請信用評等資訊";

				if (Util.isNotEmpty(c101s01g_grdCDate)) {

					markModel_varVer = c101s01g.getVarVer() + this.getModelSpan("G", c101s01g.getVarVer());;
					markModel_grdCDate = c101s01g_grdCDate;
					markModel_grade1 = ClsUtil.showGradeWithRatingDesc(
							UtilConstants.L140S02AModelKind.房貸,
							c101s01g.getGrade1());
					//J-111-0651 無聯徵警訊 >> 無信用不良限制及聯徵繳款資訊限制
					markModel_alertMsg = firstNotEmptyStr(
							clsService.getClsGradeMessage(c101s01g,
									UtilConstants.L140S02AModelKind.房貸, OverSeaUtil.TYPE_RPT, bailout_alertMsg),
							"無信用不良限制及聯徵繳款資訊限制");
					markModel_adjustStatus = firstNotEmptyStr(
							ClsUtil.getAdjustStatus(c101s01g), "無調整評等");
					markModel_adjustReason = firstNotEmptyStr(
							c101s01g.getAdjustReason(), "無調整理由");
					markModel_grade3 = ClsUtil.showGradeWithRatingDesc(
							UtilConstants.L140S02AModelKind.房貸,
							c101s01g.getGrade3());
					if(scoreDoubleTrack){ //雙軌運行中
						markModel_title_N = "房屋貸款申請信用評等資訊";
						markModel_notes_N = "房貸模型3.0尚未正式啟用，僅供參考";
						C101S01G_N c101s01g_n = c101s01g_nDao.findByUniqueKey(c101s01g.getMainId(), c101s01g.getOwnBrId(), c101s01g.getCustId(), c101s01g.getDupNo());
						if(c101s01g_n != null){
							markModel_varVer_N = c101s01g_n.getVarVer() + this.getModelSpan("G", c101s01g_n.getVarVer());;
							markModel_grdCDate_N = c101s01g_grdCDate;
							markModel_grade1_N = ClsUtil.showGradeWithRatingDesc(
									UtilConstants.L140S02AModelKind.房貸,
									c101s01g_n.getGrade1());
							//J-111-0651 無聯徵警訊 >> 無信用不良限制及聯徵繳款資訊限制
							markModel_alertMsg_N = firstNotEmptyStr(
									clsService.getClsGradeMessage(c101s01g_n,
											UtilConstants.L140S02AModelKind.房貸, OverSeaUtil.TYPE_RPT, bailout_alertMsg),
									"無信用不良限制及聯徵繳款資訊限制");
							markModel_grade3_N = ClsUtil.showGradeWithRatingDesc(
									UtilConstants.L140S02AModelKind.房貸,
									c101s01g_n.getGrade3());
							showSDT_Area2 = "Y";
						}
					}
				}
				// ---
				markModel_title_2 = "非房屋貸款申請信用評等資訊";

				if (Util.isNotEmpty(c101s01q_grdCDate)) {

					markModel_varVer_2 = c101s01q.getVarVer() + this.getModelSpan("Q", c101s01q.getVarVer());;
					markModel_grdCDate_2 = c101s01q_grdCDate;
					markModel_grade1_2 = ClsUtil.showGradeWithRatingDesc(
							UtilConstants.L140S02AModelKind.非房貸,
							c101s01q.getGrade1());
					//J-111-0651 無聯徵警訊 >> 無信用不良限制及聯徵繳款資訊限制
					markModel_alertMsg_2 = firstNotEmptyStr(
							clsService.getClsGradeMessage(c101s01q,
									UtilConstants.L140S02AModelKind.非房貸, OverSeaUtil.TYPE_RPT, bailout_alertMsg),
							"無信用不良限制及聯徵繳款資訊限制");
					markModel_adjustStatus_2 = firstNotEmptyStr(
							ClsUtil.getAdjustStatus(c101s01q), "無調整評等");
					markModel_adjustReason_2 = firstNotEmptyStr(
							c101s01q.getAdjustReason(), "無調整理由");
					markModel_grade3_2 = ClsUtil.showGradeWithRatingDesc(
							UtilConstants.L140S02AModelKind.非房貸,
							c101s01q.getGrade3());
					//增加非房貸6~10提示資訊
					markModel_grade3_2 = grade_6to10_appendAlertMsg(markModel_grade3_2, isC120M01A, mainId, custId, dupNo, bailout_q6to10Msg);
					
					if(scoreDoubleTrack){ //雙軌運行中
						markModel_title_2_N = "非房屋貸款申請信用評等資訊";
						markModel_notes_2_N = "非房貸模型4.0尚未正式啟用，僅供參考";
						C101S01Q_N c101s01q_n = c101s01q_nDao.findByUniqueKey(c101s01q.getMainId(), c101s01q.getOwnBrId(), c101s01q.getCustId(), c101s01q.getDupNo());
						if(c101s01q_n != null){
							markModel_varVer_2_N = c101s01q_n.getVarVer() + this.getModelSpan("Q", c101s01q_n.getVarVer());;
							markModel_grdCDate_2_N = c101s01q_grdCDate;
							markModel_grade1_2_N = ClsUtil.showGradeWithRatingDesc(
									UtilConstants.L140S02AModelKind.非房貸,
									c101s01q_n.getGrade1());
							//J-111-0651 無聯徵警訊 >> 無信用不良限制及聯徵繳款資訊限制
							markModel_alertMsg_2_N = firstNotEmptyStr(
									clsService.getClsGradeMessage(c101s01q_n,
											UtilConstants.L140S02AModelKind.非房貸, OverSeaUtil.TYPE_RPT, bailout_alertMsg),
									"無信用不良限制及聯徵繳款資訊限制");
							markModel_grade3_2_N = ClsUtil.showGradeWithRatingDesc(
									UtilConstants.L140S02AModelKind.非房貸,
									c101s01q_n.getGrade3());
							showSDT_Area2 = "Y";
						}
						
					}
				}

			}
		}

		String randomCode = "";
		if (c101s01g != null) {
			randomCode = Util.trim(c101s01g.getRandomCode());
		}
		// ---
		r.put("grdCDate_0", markModel_grdCDate_0);
		r.put("alertMsg_0", markModel_alertMsg_0);
		
		r.put("markModel_title", markModel_title);
		r.put("grdCDate", markModel_grdCDate);
		r.put("markModel_varVer", markModel_varVer);
		r.put("grade1", markModel_grade1);
		r.put("alertMsg", markModel_alertMsg);
		r.put("adjustStatus", markModel_adjustStatus);
		r.put("adjustReason", markModel_adjustReason);
		r.put("grade3", markModel_grade3);

		r.put("markModel_varVer_2", markModel_varVer_2);
		r.put("markModel_title_2", markModel_title_2);
		r.put("grdCDate_2", markModel_grdCDate_2);
		r.put("grade1_2", markModel_grade1_2);
		r.put("alertMsg_2", markModel_alertMsg_2);
		r.put("adjustStatus_2", markModel_adjustStatus_2);
		r.put("adjustReason_2", markModel_adjustReason_2);
		r.put("grade3_2", markModel_grade3_2);
		// ---
		r.put("randomCode", randomCode);

		r.put("markModel3_varVer", markModel3_varVer);
		r.put("markModel3_title", markModel3_title);
		r.put("markModel3_grdCDate", markModel3_grdCDate);
		r.put("markModel3_grade1", markModel3_grade1);
		r.put("markModel3_alertMsg", markModel3_alertMsg);
		r.put("markModel3_adjustStatus", markModel3_adjustStatus);
		r.put("markModel3_adjustReason", markModel3_adjustReason);
		r.put("markModel3_grade3", markModel3_grade3);
		r.put("markModel3_sprtRating", markModel3_sprtRating);
		r.put("markModel3_j10_score", markModel3_j10_score);
		r.put("markModel3_j10_reason", markModel3_j10_reason);
		
		//雙軌模型資料
		r.put("markModel_title_N", markModel_title_N);
		r.put("markModel_notes_N", markModel_notes_N);
		r.put("markModel_varVer_N", markModel_varVer_N);
		r.put("grdCDate_N", markModel_grdCDate_N);
		r.put("grade1_N", markModel_grade1_N);
		r.put("alertMsg_N", markModel_alertMsg_N);
		r.put("grade3_N", markModel_grade3_N);

		r.put("markModel_title_2_N", markModel_title_2_N);
		r.put("markModel_notes_2_N", markModel_notes_2_N);
		r.put("markModel_varVer_2_N", markModel_varVer_2_N);
		r.put("grdCDate_2_N", markModel_grdCDate_2_N);
		r.put("grade1_2_N", markModel_grade1_2_N);
		r.put("alertMsg_2_N", markModel_alertMsg_2_N);
		r.put("grade3_2_N", markModel_grade3_2_N);

		r.put("markModel3_title_N", markModel3_title_N);
		r.put("markModel3_notes_N", markModel3_notes_N);
		r.put("markModel3_varVer_N", markModel3_varVer_N);
		r.put("markModel3_grdCDate_N", markModel3_grdCDate_N);
		r.put("markModel3_grade1_N", markModel3_grade1_N);
		r.put("markModel3_alertMsg_N", markModel3_alertMsg_N);
		r.put("markModel3_grade3_N", markModel3_grade3_N);
		r.put("markModel3_sprtRating_N", markModel3_sprtRating_N);
		r.put("markModel3_j10_score_N", markModel3_j10_score_N);
		r.put("markModel3_j10_reason_N", markModel3_j10_reason_N);
		
		r.put("showSDT_Area1", showSDT_Area1);
		r.put("showSDT_Area2", showSDT_Area2);
		r.put("showSDT_Area3", showSDT_Area3);
		
		return r;
	}

	private String grade_6to10_appendAlertMsg(String src,  
			boolean isC120M01A, String mainId, String custId, String dupNo, String bailout_flag2){
		if(CrsUtil.get_s01q_out_authority_grade_6_to_10().contains(src)){	
			if(isC120M01A){
				if(isUseAtL140S02A(mainId, custId, dupNo)){
					//判斷 "最終評等" 是否有被採用
					return src+"<br/>※應呈報總處核准。"+(Util.equals("Y", bailout_flag2)?OverSeaUtil.get_bailout_msg():"");	
				}else{
					/*當 c120s01q.quote=Y 表示為 {主債務人 or 需辦理評等的從債務人}
						+ 若屬L140S02A被選取的ID ，直接出現「應呈報總處核准」
					 	+ 未被L140S02A被選取的ID ，出現提示訊息
					 */
					//主借人2等, 連保人7等  ⇒ 若選擇採用主借人的評等
					//於列印 連保人 的基本資料時
					return src+"<br/>※若採用本筆信用評等時，應依本行消金信用評等作業須知第五條，呈報總處核准。"+(Util.equals("Y", bailout_flag2)?OverSeaUtil.get_bailout_msg():"");		
				}				
			}else{
				return src+"<br/>※若採用本筆信用評等時，應依本行消金信用評等作業須知第五條，呈報總處核准。"; //個金徵信
			}			
		}else{
			return src;
		}
	}
	
	private boolean isUseAtL140S02A(String mainId, String custId, String dupNo){
		List<L140M01A> l140m01as = l140m01aDao
			.findL140m01aListByL120m01cMainIdOrderByCust(mainId,
					UtilConstants.Cntrdoc.ItemType.額度明細表);
		for (L140M01A l140m01a : l140m01as) {
			for(L140S02A l140s02a : l140s02aDao.findByMainId(l140m01a.getMainId())){
				if ((Util.equals(UtilConstants.L140S02AModelKind.非房貸, l140s02a.getModelKind()) 
					|| Util.equals(UtilConstants.L140S02AModelKind.卡友貸, l140s02a.getModelKind()))
						&& Util.equals(l140s02a.getCustId(), custId)
						&& Util.equals(l140s02a.getDupNo(), dupNo)) {
					return true;
				}
			}	
		}
		return false;
	}
	
	private String s01r_out_authority(String src,  
			boolean isC120M01A, String mainId, String custId, String dupNo, String bailout_flag2){
		String brNo = "";
		if(isC120M01A){
			L120M01A l120m01a = clsService.findL120M01A_mainId(mainId);
			if(l120m01a!=null){
				brNo = l120m01a.getCaseBrId();
			}
		}else{
			C101M01A c101m01a = clsService.findC101M01A_mainId(mainId);
			if(c101m01a!=null){
				brNo = c101m01a.getOwnBrId();                                           
			}
		}
		
		if(ClsUtility.get_s01r_out_authority_grade_6_to_10().contains(src)
				&& ClsUtility.get_s01r_out_authority_grade_prodKind71(brNo).contains(src)==false){
			//若專案信貸(非團體) 6~8等  => 在 6~10 等的範圍內，再去排除9~10等
			if(!isC120M01A){
				//個金徵信，因為{產品08}已停用，會用到 專案信貸(非團體) 的只有{產品71}
				return src;
			}
			
			if(isC120M01A && is_MarkModel3_atL140S02A_onlyProdKind71(mainId)){
				return src;
			}
		}		
		
		return grade_6to10_appendAlertMsg(src, isC120M01A,  mainId, custId, dupNo, bailout_flag2);
	}
	
	private boolean is_MarkModel3_atL140S02A_onlyProdKind71(String mainId){
		List<L140M01A> l140m01as = l140m01aDao
			.findL140m01aListByL120m01cMainIdOrderByCust(mainId,
					UtilConstants.Cntrdoc.ItemType.額度明細表);
		int cnt_prodKind71 = 0;
		int cnt_prodKindNot71 = 0;
		for (L140M01A l140m01a : l140m01as) {
			for(L140S02A l140s02a : l140s02aDao.findByMainId(l140m01a.getMainId())){
				if (Util.equals(UtilConstants.L140S02AModelKind.卡友貸, l140s02a.getModelKind())) {
					if(CrsUtil.is_71(l140s02a.getProdKind())){
						++cnt_prodKind71;
					}else{
						++cnt_prodKindNot71;
					}
				}
			}	
		}		
		return (cnt_prodKind71>0 && cnt_prodKindNot71==0);
	}
	

	
	private String firstNotEmptyStr(String s1, String s2) {
		return Util.isEmpty(s1) ? s2 : s1;
	}

	/**
	 *  消金處明澤要求簡易列印要增加下列內容
	 *  疑似偽造證件或財力證明
	 *  進件來源
	 *  房屋貸款申請信用評等資訊
	 *
	 * @param locale
	 * @param params
	 * @param prop_cls1131
	 * @param prop_LMSCommomPage
	 * @return
	 * @throws FileNotFoundException
	 * @throws IOException
	 * @throws Exception
	 */
	public OutputStream genSimple(Locale locale, PageParameters params, Properties prop_cls1131,
			Properties prop_LMSCommomPage) throws FileNotFoundException, IOException, Exception {

		ReportGenerator generator = new ReportGenerator();


		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));

		List<C120M01A> c120m01as = (List<C120M01A>) service.findListByMainId(C120M01A.class, mainId);

		List<C120M01A> c120m01asCopy = new ArrayList<C120M01A>(c120m01as);
		Collections.sort(c120m01asCopy, new Comparator<C120M01A>() {
			@Override
			public int compare(C120M01A o1, C120M01A o2) {
				if ("Y".equals(o1.getKeyMan())) {
					return -1;
				} else if ("Y".equals(o2.getKeyMan())) {
					return 1;
				} else if (o1.getCustShowSeqNum() != null && o2.getCustShowSeqNum() != null) {
					return o1.getCustShowSeqNum() - o2.getCustShowSeqNum();
				} else {
					return 0;
				}
			}
		});

		Class<?> classes[] = C120_CLASS_RPT_1;

		List<Map<String, String>> dataRows = new LinkedList<Map<String, String>>();
		for (C120M01A c120m01a : c120m01asCopy) {

			if (UtilConstants.lngeFlag.擔保品提供人.equals(c120m01a.getCustPos())) {
				continue;
			}
			// 因非自然人於個金簽案其並無法弄出徵信資料故於列印時不再列印非自然人的徵信資料表。
			if ("N".equals(c120m01a.getNaturalFlag())) {
				continue;
			}

			Map<String, String> rowData = new LinkedHashMap<String, String>();

			String custId = c120m01a.getCustId();
			String dupNo = c120m01a.getDupNo();


			HashMap<String, String> markModel_map = procMarkModel(classes, mainId, custId, dupNo, true);
			dataRows.add(rowData);

			String branchName = Util.trim(branchService.getBranchName(c120m01a.getOwnBrId()));
			rowData.put("ReportBean.column14", branchName);

			rowData.put("ReportBean.column07", MapUtils.getString(markModel_map, "markModel_title", ""));
			rowData.put("ReportBean.column08", MapUtils.getString(markModel_map, "grdCDate", ""));
			rowData.put("ReportBean.column09", MapUtils.getString(markModel_map, "grade1", ""));
			rowData.put("ReportBean.column10", MapUtils.getString(markModel_map, "alertMsg", ""));
			rowData.put("ReportBean.column11", MapUtils.getString(markModel_map, "adjustStatus", ""));
			rowData.put("ReportBean.column12", MapUtils.getString(markModel_map, "adjustReason", ""));
			rowData.put("ReportBean.column13", MapUtils.getString(markModel_map, "grade3", ""));


			rowData.put("ReportBean.column15", MapUtils.getString(markModel_map, "markModel_title_2", ""));
			rowData.put("ReportBean.column16", MapUtils.getString(markModel_map, "grdCDate_2", ""));
			rowData.put("ReportBean.column17", MapUtils.getString(markModel_map, "grade1_2", ""));
			rowData.put("ReportBean.column18", MapUtils.getString(markModel_map, "alertMsg_2", ""));
			rowData.put("ReportBean.column19", MapUtils.getString(markModel_map, "adjustStatus_2", ""));
			rowData.put("ReportBean.column20", MapUtils.getString(markModel_map, "adjustReason_2", ""));
			rowData.put("ReportBean.column21", MapUtils.getString(markModel_map, "grade3_2", ""));


			rowData.put("ReportBean.column22", MapUtils.getString(markModel_map, "markModel3_title", ""));
			rowData.put("ReportBean.column23", MapUtils.getString(markModel_map, "markModel3_grdCDate", ""));
			rowData.put("ReportBean.column24", MapUtils.getString(markModel_map, "markModel3_grade1", ""));
			rowData.put("ReportBean.column25", MapUtils.getString(markModel_map, "markModel3_alertMsg", ""));
			rowData.put("ReportBean.column26", MapUtils.getString(markModel_map, "markModel3_j10_score", ""));
			rowData.put("ReportBean.column27", MapUtils.getString(markModel_map, "markModel3_j10_reason", ""));
			rowData.put("ReportBean.column28", MapUtils.getString(markModel_map, "markModel3_sprtRating", ""));
			rowData.put("ReportBean.column29", MapUtils.getString(markModel_map, "markModel3_adjustStatus", ""));
			rowData.put("ReportBean.column30", MapUtils.getString(markModel_map, "markModel3_adjustReason", ""));
			rowData.put("ReportBean.column31", MapUtils.getString(markModel_map, "markModel3_grade3", ""));

			rowData.put("ReportBean.column98", ""); //控制 value in ("hk") 時，是否列印
			rowData.put("ReportBean.column99", ""); //控制 value in ("h1", "h2") 時，是否列印
			// 房貸模型, 非房貸模型
			if (Util.isNotEmpty(markModel_map.get("markModel_title"))
					&& Util.isNotEmpty(markModel_map.get("markModel_title_2"))) {
				rowData.put("ReportBean.column99", "h2");
			} else if (Util.isNotEmpty(markModel_map.get("markModel_title"))
					|| Util.isNotEmpty(markModel_map.get("markModel_title_2"))) {
				rowData.put("ReportBean.column99", "h1");
			}

			//專案信貸(非團體)
			if (Util.isNotEmpty(markModel_map.get("markModel3_title"))){
				rowData.put("ReportBean.column98", "hk");
			}

			String memoC101M01A_custName = "";
			String memoC101M01A_ownBrId = "";
			String memoC101S01E_isQdata18 = "";
			String memoC101S01E_caseSrcFlag = "";
			String memoC101S01E_laaName = "";
			String memoC101S01E_laaYear = "";
			String memoC101S01E_laaWord = "";
			String memoC101S01E_laaNo = "";
			String memoC101S01E_laaOfficeId = "";
			String memoC101S01E_laaOffice = "";
			String memoC101S01E_caseSrcMemo = "";
			String memoC101S01E_laaDesc = "";
			String memoC101S01E_agentPId = "";
			String memoC101S01E_agentPName = "";

			for (Class<?> clazz : classes) {

				GenericBean bean = service.findModelByKey(clazz, mainId, custId, dupNo, true);
				GenericBean model = ClsUtil.changeModel(clazz, bean);

				if (model != null) {

					// 個金徵信借款人主檔
					if (model instanceof C101M01A) {
						C101M01A c101m01a = (C101M01A) model;

						memoC101M01A_custName = Util.trim(c101m01a.getCustName());
						memoC101M01A_ownBrId = Util.trim(c101m01a.getOwnBrId());

						rowData.put("ReportBean.column01", c101m01a.getCustName());
						rowData.put("ReportBean.column02", c101m01a.getCustId());

					} else if (model instanceof C101S01A) {
						C101S01A c101s01a = (C101S01A) model;
						rowData.put("ReportBean.column03", CapDate.formatDate(c101s01a.getBirthday(), "yyyy-MM-dd"));
					}

					// 個金服務單位檔
					else if (model instanceof C101S01B) {
					}
					// 票交所與聯徵特殊負面資訊
					else if (model instanceof C101S01G || model instanceof C101S01Q || model instanceof C101S01R) {

					}
					// 個金相關查詢資料檔
					else if (model instanceof C101S01E) {
						C101S01E c101s01e = (C101S01E) model;
						memoC101S01E_isQdata18 = c101s01e.getIsQdata18();
						memoC101S01E_caseSrcFlag = Util.trim(c101s01e.getCaseSrcFlag());
						memoC101S01E_laaName = Util.trim(c101s01e.getLaaName());
						memoC101S01E_laaYear = Util.trim(c101s01e.getLaaYear());
						memoC101S01E_laaWord = Util.trim(c101s01e.getLaaWord());
						memoC101S01E_laaNo = Util.trim(c101s01e.getLaaNo());
						memoC101S01E_laaOfficeId = Util.trim(c101s01e.getLaaOfficeId());
						memoC101S01E_laaOffice = Util.trim(c101s01e.getLaaOffice());
						memoC101S01E_caseSrcMemo = Util.trim(c101s01e.getCaseSrcMemo());
						memoC101S01E_laaDesc = Util.trim(c101s01e.getLaaDesc());
						if (true) {
							//上面先已用 rptVariableMap.put(key, Util.trim(getValue(key, value)));

							Map<String, String> map = new HashMap<String, String>();
							map.put("L", prop_cls1131.getProperty("C101S01E.caseSrcFlag.L"));
							map.put("P", prop_cls1131.getProperty("C101S01E.caseSrcFlag.P"));
							map.put("O", prop_cls1131.getProperty("C101S01E.caseSrcFlag.O"));

							//選項增加 A買賣件經地政士辦理   B買賣件非經地政士辦理    C非買賣件
							//原L,P,O不再使用，但舊案還是要可以順利列印
							map.put("A", prop_cls1131.getProperty("C101S01E.caseSrcFlag.A"));
							map.put("B", prop_cls1131.getProperty("C101S01E.caseSrcFlag.B"));
							map.put("C", prop_cls1131.getProperty("C101S01E.caseSrcFlag.C"));
							// 判斷列印的標題是要印「進件來源」或是「地政士查詢」
							rowData.put("ReportBean.column32", memoC101S01E_caseSrcFlag.matches("[LPO]") ? "N" : "Y");

							rowData.put("ReportBean.column05", LMSUtil.getDesc(map, memoC101S01E_caseSrcFlag));

						}
						memoC101S01E_agentPId = Util.trim(c101s01e.getAgentPId());
						memoC101S01E_agentPName = Util.trim(c101s01e.getAgentPName());


					} else if (model instanceof C101S01J) {
						C101S01J c101s01j = (C101S01J) model;
						if (UtilConstants.haveNo.有.equals(memoC101S01E_isQdata18)) {
							rowData.put("ReportBean.column04", ClsUtil.getC900M01EMsg(memoC101M01A_custName,
									c101s01j));

						} else if (UtilConstants.haveNo.無.equals(memoC101S01E_isQdata18)) {
							rowData.put("ReportBean.column04",
									Util.trim(getValue("isQdata18", memoC101S01E_isQdata18)));
						}
						List<String> msg = new ArrayList<String>();
						if (Util.equals("L", memoC101S01E_caseSrcFlag)) {

						} else if (Util.equals("P", memoC101S01E_caseSrcFlag)) {

						} else if (Util.equals("O", memoC101S01E_caseSrcFlag)
								|| Util.equals("B", memoC101S01E_caseSrcFlag)) {
							if (Util.isNotEmpty(memoC101S01E_caseSrcMemo)) {
								msg.add(memoC101S01E_caseSrcMemo);
								msg.add("");
							}
						} else {
							//若 caseSrcFlag 空白 , 表示是在 地政士黑名單 上線前的舊案
						}

						// 地政士改為多筆
						List<C120S01Y> so1ys = c120s01yDao.findByList(mainId, custId, dupNo);
						for (C120S01Y s01y : so1ys) {
							memoC101S01E_laaName = CapString.trimNull(s01y.getLaaName());
							memoC101S01E_laaYear = CapString.trimNull(s01y.getLaaYear());
							memoC101S01E_laaWord = CapString.trimNull(s01y.getLaaWord());
							memoC101S01E_laaNo = CapString.trimNull(s01y.getLaaNo());
							memoC101S01E_laaOfficeId = CapString.trimNull(s01y.getLaaOfficeId());
							memoC101S01E_laaOffice = CapString.trimNull(s01y.getLaaOffice());
							memoC101S01E_laaDesc = CapString.trimNull(s01y.getLaaDesc());
							if (Util.isNotEmpty(memoC101S01E_laaName)) {
								msg.add(prop_cls1131.getProperty("C101S01E.laaName") + "：" + memoC101S01E_laaName);
							}
							if (CrsUtil.hasLaaCertData(memoC101S01E_laaYear, memoC101S01E_laaWord, memoC101S01E_laaNo)) {
								msg.add(prop_cls1131.getProperty(
								"C101S01E.label.laaCert") + "：" + "(" + memoC101S01E_laaYear + ")" + memoC101S01E_laaWord + prop_cls1131
								.getProperty(
								"C101S01E.label.laaWordPost") + memoC101S01E_laaNo + prop_cls1131.getProperty(
								"C101S01E.label.laaNoPost"));
							}
							if (Util.isNotEmpty(memoC101S01E_laaOfficeId)) {
								msg.add(prop_cls1131.getProperty("C101S01E.laaOfficeId") + "：" + memoC101S01E_laaOfficeId);
							}
							if (CrsUtil.hasLaaOfficeData(memoC101S01E_laaOffice)) {
								msg.add(prop_cls1131.getProperty("C101S01E.laaOffice") + "：" + memoC101S01E_laaOffice);
							}
							String laaCtlFlag = CapString.trimNull(s01y.get("laaCtlFlag"));
							String laaMatchRuleFlag = CapString.trimNull(s01y.get("laaMatchRuleFlag"));
							if(Util.isNotEmpty(laaCtlFlag)){
								String cmpStr = "「地政士黑名單」檢核結果：";
								if(Util.equals(laaCtlFlag, "0")){											
									msg.add(cmpStr+"無");
								}else{
									C101S01J temp = new C101S01J();
									temp.setLaaCtlFlag(laaCtlFlag);
									temp.setLaaMatchRuleFlag(laaMatchRuleFlag);
									msg.add(cmpStr+clsService.msg_Laa_html(temp, prop_LMSCommomPage));								
									Map<String, String> descMap = clsService.get_codeTypeWithOrder("cls260CtlFlagType",
											LocaleContextHolder.getLocale().toString());
									msg.add("控管原因："+LMSUtil.getDesc(descMap, laaCtlFlag));	
									//J-109-0140_10702_B1001 增加地政士黑名單控管邏輯
									if(Util.equals(clsService.getCtlFlagType(laaCtlFlag), LMSUtil.地政士黑名單警示名單)){
										msg.add("理由說明：" + memoC101S01E_laaDesc);
									}
								}	
							}
							msg.add("<br>");
						}

						if (Util.isNotEmpty(memoC101S01E_agentPId) || Util.isNotEmpty(memoC101S01E_agentPName)) {
							if (Util.isNotEmpty(memoC101S01E_agentPId)) {
								String c900m01j_mainId = Util.trim(c101s01j.getAgentPIdCmp());
								C900M01J c900m01j = null;
								if (Util.isNotEmpty(c900m01j_mainId)) {
									c900m01j = clsService.findC900M01J_mainId(c900m01j_mainId);
								}
								//=================
								msg.add(ClsUtil.msg_agentPIdCmp(memoC101S01E_agentPId, c900m01j,
										clsService.get_C900M01J_output_memo(c900m01j), prop_cls1131));
							}
							if (Util.isNotEmpty(memoC101S01E_agentPName)) {
								msg.add(prop_cls1131.getProperty(
										"C101S01E.agentPName") + "：" + memoC101S01E_agentPName);
							}
						}

						rowData.put("ReportBean.column06", StringUtils.join(msg, "<br/>"));

					}
				}

			}
		}
		SubReportParam subReportParam = new SubReportParam();
		subReportParam.setData(0, new HashMap<String, String>(), dataRows);
		generator.setSubReportParam(subReportParam);
		generator.setVariableData(new HashMap<String, String>());
		// ap
		generator.setReportFile("report/cls/CLS1131R09_" + locale.toString() + ".rpt");

		OutputStream outputStream = null;
		outputStream = generator.generateReport();

		return outputStream;

	}
	
	/**
	 * 依照欄位數值回傳是或否
	 * 
	 * @param checkFlag
	 *            欄位數值
	 * @return 是/否/原數值
	 */
	private String getYesOrNo(String checkFlag) {
		if (UtilConstants.DEFAULT.是.equals(checkFlag)) {
			return "是";
		} else if (UtilConstants.DEFAULT.否.equals(checkFlag)) {
			return "否";
		} else {
			return checkFlag;
		}
	}
	
	private String getModelSpan(String mowType, String varVer) {
		String Span = " (1~10等)";
		if(Util.equals(mowType, "G")){ //房貸
			if(Util.equals(varVer, ClsScoreUtil.V3_0_HOUSE_LOAN)){ //房貸3.0
				Span = " (1~15等)";
			}
		}else if(Util.equals(mowType, "Q")){ //非房貸
			if(Util.equals(varVer, ClsScoreUtil.V4_0_NOT_HOUSE_LOAN)){ //非房貸4.0
				Span = " (1~15等)";
			}
		}else if(Util.equals(mowType, "R")){ //非房貸
			if(Util.equals(varVer, ClsScoreUtil.V4_0_CARD_LOAN)){ //專案信貸(非團體)4.0
				Span = " (1~15等)";
			}
		}
		return Span;
	}
	
}
