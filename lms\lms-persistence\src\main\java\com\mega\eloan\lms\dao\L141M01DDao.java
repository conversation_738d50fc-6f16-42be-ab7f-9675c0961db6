/* 
 * L141M01DDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L141M01D;

/** 聯行額度明細表簽章欄檔 **/
public interface L141M01DDao extends IGenericDao<L141M01D> {

	L141M01D findByOid(String oid);

	List<L141M01D> findByMainId(String mainId);

	List<L141M01D> findByMainIdAndBranchType(String mainId, String branchType);

	L141M01D findByUniqueKey(String mainId, String branchType, String branchId,
			String staffNo, String staffJob);

	L141M01D findByUniqueKey(String mainId, String branchType, String staffJob);

	List<L141M01D> findByIndex01(String mainId, String branchType,
			String branchId, String staffNo, String staffJob);

}