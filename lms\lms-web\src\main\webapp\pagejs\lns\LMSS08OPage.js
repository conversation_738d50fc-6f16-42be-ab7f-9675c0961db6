var dfd1 = new $.Deferred();
var lastSel;
var initS08oJson = {
	handlerName : null,
	// 設定handler名稱
	setHandler : function(){
		if(responseJSON.docURL == "/lms/lms1201m01"){
			// 授權外企金
			this.handlerName = "lms1201formhandler";
		}else if(responseJSON.docURL == "/lms/lms1101m01"){
			// 授權內企金
			this.handlerName = "lms1101formhandler";
		}else if(responseJSON.docURL == "/lms/lms1211m01"){
			// 授權外個金
			this.handlerName = "lms1211formhandler";
		}else if(responseJSON.docURL == "/lms/lms1111m01"){
			this.handlerName = "lms1111formhandler";
		}else{
			this.handlerName = "lms1301formhandler";
		}		
	},
	init: function(){
		//初始化文件
//		$.ajax({
//	        handler: "lmscombineprintformhandler",
//	        action: "initData",
//	        async: true ,
//	        data: {
//	        	mainId: responseJSON.mainId
//	        },
//	        success: function(obj){
//	        	 
//	        }
//	    });	
		
		this.updateCombinePrintInfo();
	},
	updateCombinePrintInfo: function(){
		//初始化文件
		$.ajax({
	        handler: "lmscombineprintformhandler",
	        action: "updateCombinePrintInfo",
	        async: true ,
	        data: {
	        	mainId: responseJSON.mainId
	        },
	        success: function(obj){
	        	$("#combinePrint_time").val(obj.combinePrint_time);
	        	$("#combinePrint_unid").val(obj.combinePrint_unid);
	        	$("#combinePrint_status").val(obj.combinePrint_status);
	        	$("#combinePrint_errMsg").val(obj.combinePrint_errMsg);
	        	
	        	//J-112-0508_05097_B1001 Web e-Loan為提升授信簽報效率, 三大部及授審處可由eloan授信系統依據授審會及常董會提案稿所需文件之順序產生相關提案稿pdf
	        	if(obj.showLmss08o == "Y"){
	        		$(".show_lmss08o_panel").show();
 	        	}else{
 	        		$(".show_lmss08o_panel").hide();
 	        	}
	   
	        }
	    });		
	},
	// 設定附加檔案內容
	fileSet : function(upFileId, delFileId, fieldId, fileGridId){
		// 上傳檔案按鈕
		$("#" + upFileId).click(function(){
			
			var count = $("#"+fileGridId).jqGrid('getGridParam', 'records');
			if (count > 0) {
				// L120S08O.confirm2=請先刪除資料後再重新上傳
// CommonAPI.showErrorMessage(i18n.lmss08a["L120S08O.confirm2"]);
// return false;
			}
			
			var limitFileSize=9437103;
			MegaApi.uploadDialog({
				handler:"lmsfileuploadhandler",
				fieldId:fieldId,
	            fieldIdHtml:"size='30'",
	            fileDescId:"fileDesc",
	            fileDescHtml:"size='30' maxlength='30'",
	            // fileCheck: ['doc'],
				subTitle:i18n.def('insertfileSize',{'fileSize':(limitFileSize/1048576).toFixed(2)}),
				limitSize:limitFileSize,
				multiple : true,
	            width:320,
	            height:190,			
				data:{
					mainId:$("#mainId").val()				
				},
				success : function(obj) {
					$("#" + fileGridId).trigger("reloadGrid");
				}
		   });
		});
		
		// 刪除檔案按鈕
		$("#" + delFileId).click(function(){
			var select  = $("#" + fileGridId).getGridParam('selrow');		
			// confirmDelete=是否確定刪除?
			CommonAPI.confirmMessage(i18n.def["confirmDelete"],function(b){
				if(b){				 
					var select = $("#"+fileGridId).getGridParam('selarrrow');
			        if (select == "") {
			        	// TMMDeleteError=請先選擇需修改(刪除)之資料列
						CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
						return;
			        }
		
			        var data = [];
	                for (var i in select) {
	                    data.push($("#"+fileGridId).getRowData(select[i]).oid);
	                }
				 
					$.ajax({
						handler : "lmscommonformhandler",
						type : "POST",
						dataType : "json",
						data : {
							formAction : "deleteUploadFile",
							realTime : "Y",
	                        oids: data
						},
						success : function(obj) {
							$("#" + fileGridId).trigger("reloadGrid");
						}
					});
				}else{
					return ;
				}
			});
		});		
	},
	// 設定附加檔案Grid
	fileGrid : function(fileGridId, fieldId){
		// 檔案上傳grid
		$("#" + fileGridId).iGrid({
			handler : 'lms1201gridhandler',
			height : 300,
			postData : {
				formAction : "queryfile",
				fieldId:fieldId,
				mainId:responseJSON.mainId
			},
			// rowNum : 15,
			caption: "&nbsp;",
			hiddengrid : false,
			sortname: 'srcFileName|uploadTime',
	        sortorder: 'asc|asc',
			// expandOnLoad : true, //只對subgrid有用
			multiselect : true,
			colModel : [ {
				colHeader : i18n.lmss08a['L120S08O.srcFileName'],// 原始檔案名稱,
				name : 'srcFileName',
				width : 200,
				align: "left",
				sortable : false,
				formatter : 'click',
				onclick : openDoc
			}, {
				colHeader :  i18n.lmss08a['L120S08O.fileDesc'],// 檔案說明
				name : 'fileDesc',
				width : 200,
				sortable : false
			}, {
				colHeader : " ",// 上傳時間
				name : 'flag',
				width : 10,
				sortable : false
			}, {
				colHeader : i18n.lmss08a['L120S08O.uploadTime'],// 上傳時間
				name : 'uploadTime',
				width : 80,
				sortable : false
			}, {
				name : 'oid',
				hidden : true
			}],
			gridComplete : function () {
				initS08oJson.updateCombinePrintInfo(); 
			}
		});		
	},
	// 設定附加檔案Grid
	updateFileGrid : function(tfileGridId, tfieldId){
		// 檔案上傳grid
		$("#" + tfileGridId).iGrid({
			handler : 'lms1201gridhandler',
			height : 300,
			postData : {
				formAction : "queryfile",
				fieldId:tfieldId,
				mainId:responseJSON.mainId,
				flagOnlyNull:"Y"
			},
			hiddengrid : false,
			cellsubmit: 'clientArray',
            autowidth: true,
			sortname: 'srcFileName|uploadTime',
	        sortorder: 'asc|asc',
			// expandOnLoad : true, //只對subgrid有用
			colModel : [ {
				colHeader : i18n.lmss08a['L120S08O.srcFileName'],// 原始檔案名稱,
				name : 'srcFileName',
				width : 200,
				align: "left",
				sortable : false,
                editable: true
			}, {
				colHeader :  i18n.lmss08a['L120S08O.fileDesc'],// 檔案說明
				name : 'fileDesc',
				width : 200,
				sortable : false,
				editable: true
			}, {
				colHeader : i18n.lmss08a['L120S08O.uploadTime'],// 上傳時間
				name : 'uploadTime',
				width : 80,
				sortable : false	
			}, {
				name : 'oid',
				hidden : true
			}, {
				name : 'flag',
				hidden : true
			}],
            onSelectRow: function(id){
                if (id && id != lastSel) {
                	$("#" + tfileGridId).saveRow(lastSel, false, 'clientArray');
                	$("#" + tfileGridId).restoreRow(lastSel);
                    lastSel = id;
                }
                $("#" + tfileGridId).editRow(id, false);
            },
			gridComplete : function () {
				initS08oJson.updateCombinePrintInfo(); 
			}
		});		
	}
};


 

$(document).ready(function() {
	
	setCloseConfirm(true);
	// 設定handler名稱
	initS08oJson.setHandler();
	initS08oJson.init();
	
    var fileGridId = "lmss08o_gridfile";
    
	initS08oJson.fileSet("lmss08o_uploadFile", "lmss08o_deleteFile", "sendCreatDoc", fileGridId);
	 
	// 設定附加檔案Grid
	initS08oJson.fileGrid(fileGridId, "sendCreatDoc");
	initS08oJson.updateFileGrid("lmss08o_updateFileGrid", "sendCreatDoc") ;
	
	// 產生報表
	$("#lmss08o_generate").click(function(){
		lmss08o_generateReport(fileGridId);
	});
	
	// 合併列印
	$("#lmss08o_sendRpaCombine").click(function(){
		lmss08o_sendRpaCombine(fileGridId);
	});
	
	// 勾選(授審會)
	$("#lmss08o_choiceFile1").click(function(){
		lmss08o_choiceFile(fileGridId,"1");
	});
	
	// 勾選(常董會_個案)
	$("#lmss08o_choiceFile2").click(function(){
		lmss08o_choiceFile(fileGridId,"2");
	});
	
	// 勾選(常董會_彙總)
	$("#lmss08o_choiceFile3").click(function(){
		lmss08o_choiceFile(fileGridId,"3");
	});
	
	
	// 修改檔名
	$("#lmss08o_updateFileName").click(function(){
		lmss08o_updateFileName(fileGridId);
	});
	
	// 清除列印紀錄(管理用)
	$("#lmss08o_clearRpaCombine").click(function(){
		lmss08o_clearRpaCombine(fileGridId);
	});
	
	
	// 調閱舊案
	$("#lmss08o_viewOldCase").click(function(){
		lmss08o_viewOldCase();
	});
	
	/**
	 * 登錄分行代號
	 */
	$("#lmss08o_selectCopyBranchBt").click(function(){
	   CommonAPI.showAllBranch({
	       btnAction: function(a, b){
	           $("#lmss08o_selectFilterBrno").val(b.brNo);
	           $.thickbox.close();
	       }
	   });
	});
	
	
});

// 產生
function lmss08o_generateReport(fileGridId){
	var count = $("#"+fileGridId).jqGrid('getGridParam', 'records');
	if (count > 0) {
		// L120S08O.confirm1=執行前會刪除已存在之資料，是否確定執行？
		CommonAPI.confirmMessage(i18n.lmss08a["L120S08O.confirm1"], function(b){
            if (b) {
                // 是的function
            	var select = $("#"+fileGridId).jqGrid('getRowData');
            	var data = [];
                for (var i in select) {
                	if(select[i].flag=="S"){
                		//只刪除系統產生的，USER自己上傳的不會刪除
                		data.push( select[i].oid);
                	}
                }
                
                $.ajax({
                	handler : "lmscommonformhandler",
                    data: {
                        formAction: "deleteUploadFile",
                        realTime : "Y",
                        oids: data
                    },
                    success: function(obj){
                    	$("#"+fileGridId).trigger("reloadGrid");
                    	$.ajax({
                            handler: "lms1202docservice",
                            action: "saveCreatDoc",
                            data: {
                            	mainId: responseJSON.mainId,
                                fieldId: "sendCreatDoc"
                            },
                            success: function(obj){
                            	
//                            	//常董會->產生常董稿->新版簽報書
//                            	$.ajax({
//                                    type: "POST",
//                                    handler: "lms1205formhandler",
//                                    data: {
//                                        formAction: "checkIsOutNewVer",
//                                        oid: responseJSON.mainOid
//                                    },
//                                    success: function(responseData){
//                                    	
//                                    	var caseTypeCd = responseJSON.typCd;
//                                    	
//                                        $.form.submit({
//                                            url: "../../simple/FileProcessingService",
//                                            target: "_blank",
//                                            data: {
//                                                oid: responseJSON.mainOid,
//                                                mainId: responseJSON.mainId,
//                                                rptOid: "R01" + "^" + responseJSON.mainOid + "^" + "^" + "^" + "^" + "LMSDoc4",
//                                                fileDownloadName: (caseTypeCd == "5" ? "LMS1205R01.pdf" : "LMS1201R01.pdf"),
//                                                serviceName: (caseTypeCd == "5" ? "lms1205r01rptservice" : "lms1201r01rptservice")
//                                            }
//                                        });
//                                        $.thickbox.close();
//                                    }
//                                });
                            	 
                            	
                            	 $("#"+fileGridId).trigger("reloadGrid");
                            }
                        });	
                    }
                });
                
                
            }
        });
	} else{
		$("#"+fileGridId).trigger("reloadGrid");
        $.ajax({
            handler: "lms1202docservice",
            action: "saveCreatDoc",
            data: {
            	mainId: responseJSON.mainId,
                fieldId: "sendCreatDoc"
            },
            success: function(obj){
            	$("#"+fileGridId).trigger("reloadGrid");
            }
        });		
	}

}

// 1.勾選(授審會)
// 2.勾選(常董會_個案)
// 3.勾選(常董會_彙總)
function lmss08o_choiceFile(fileGridId,type){
	var i, count, $grid = $("#"+fileGridId);
	var rowArray = $grid.jqGrid('getRowData');
	$grid.jqGrid('resetSelection');
	$.ajax({
        handler: "lmscombineprintformhandler",
        data: {// 把資料轉成json
            formAction: "choiceFile",
            type: type
        },
        success: function(obj){
        	var selectItemList = obj.selectItemList;
        	if(selectItemList){
        		for (var i = 0, count = rowArray.length; i < count; i += 1) {
            		var srcFileName = rowArray[i].srcFileName.substring(0, 3);
            		if( selectItemList.includes(srcFileName) ){   
            			$grid.jqGrid('setSelection', i+1, true);
        			}else{
        				var srcFileName6 = rowArray[i].srcFileName.substring(0, 6);
                		if( selectItemList.includes(srcFileName6) ){   
                			$grid.jqGrid('setSelection', i+1, true);
            			} 
        			}
            		
            		
                }
        	}
        }// close success
    }); // close ajax

}


// 合併列印
function lmss08o_sendRpaCombine(fileGridId){
	var select = $("#"+fileGridId).getGridParam('selarrrow');
    if (select == "") {
    	// action_006=請先選擇需「列印」之資料列
		CommonAPI.showMessage(i18n.def["action_006"]);
		return;
    }
    
    var data = [];
    for (var i in select) {
        data.push($("#"+fileGridId).getRowData(select[i]).oid);
    }
   
	$("#"+fileGridId).trigger("reloadGrid");
	
	//檢查是否可以傳送
	$.ajax({
        handler: "lmscombineprintformhandler",
        action: "checkCanSend",
        data: {
        	mainId: responseJSON.mainId,
        	oids: data
        },
        success: function(obj){
        	$("#lmss08o_inputRpaReturnFileNameBox").thickbox({
                title: i18n.lmss08a["L120S08O.title.02"], //"請輸入自訂回傳檔案名稱"
                width: 500,
                height: 300,
                modal: true,
                // i18n: i18n.lms1405s02,
                buttons: API.createJSON([{
                    key: i18n.def['sure'],
                    value: function(){
                    	var rpaReturnFileName = $('#lmss08o_rpaReturnFileName').val();
                    	$.thickbox.close();
                    	$.ajax({
                	        handler: "lmscombineprintformhandler",
                	        action: "sendCreatDoc",
                	        data: {
                	        	mainId: responseJSON.mainId,
                	        	fieldId: "sendCreatDoc",
                	        	rpaReturnFileName: rpaReturnFileName,
                	        	oids: data
                	        },
                	        success: function(obj){
                	        	initS08oJson.updateCombinePrintInfo();  	
                	        	$("#"+fileGridId).trigger("reloadGrid");
                	        	
                	        }
                	    });		
                    }
                }, {
                    key: i18n.def['close'],
                    value: function(){
                        $.thickbox.close();
                    }
                }])
            });
        }
    });		
 
  
}


// 修改檔名
function lmss08o_updateFileName(fileGridId){
	 
    $("#lmss08o_updateFileGrid").trigger("reloadGrid");
    
    $("#lmss08o_updateSendCreatDocBox").thickbox({
        title: i18n.lmss08a["L120S08O.title.03"], //修改檔案資訊
        width: 900,
        height: 500,
        modal: true,
        // i18n: i18n.lms1405s02,
        buttons: API.createJSON([{
            key: i18n.def['sure'],
            value: function(){
                var $lmss08o_updateFileGrid = $("#lmss08o_updateFileGrid");
                // 寫回額度明細表
                $lmss08o_updateFileGrid.jqGrid('saveRow', lastSel, false, 'clientArray');
                var ids = $lmss08o_updateFileGrid.jqGrid('getDataIDs');
                // 用來放列印順序跟oid
                var json = {};
                
                for (var id in ids) {
                    var data = $lmss08o_updateFileGrid.jqGrid('getRowData', ids[id]);
                    var info = {};
                    info["srcFileName"] = data.srcFileName;
                    info["fileDesc"] = data.fileDesc;
                    json[data.oid] = info;
                }
                FormAction.open = true;
                $.ajax({
                    handler: "lmscombineprintformhandler",
                    action: "saveFileInfo",
                    data: {
                        mainId: $("#mainId").val(),
                        fieldId: "sendCreatDoc",
                        data: JSON.stringify(json)
                    },
                    success: function(obj){
                        $.thickbox.close();
                        $("#"+fileGridId).trigger("reloadGrid");
                    }
                });
            }
        }, {
            key: i18n.def['close'],
            value: function(){
                $.thickbox.close();
            }
        }])
    });

}

// 清除列印紀錄(管理用)
function lmss08o_clearRpaCombine(fileGridId){
	$.ajax({
        handler: "lmscombineprintformhandler",
        action: "clearRpaCombine",
        data: {
        	mainId: responseJSON.mainId
        },
        success: function(obj){
        	initS08oJson.updateCombinePrintInfo();
        	$("#"+fileGridId).trigger("reloadGrid");
        }
    });		
}


 

function openDoc(cellvalue, options, rowObject){
    $.capFileDownload({
        handler:"simplefiledwnhandler",
        data : {
            fileOid:rowObject.oid
        }
    });
}