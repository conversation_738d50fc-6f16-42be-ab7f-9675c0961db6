---------------------------------------------------------
-- LMS.L999S04A 中長期契約書檔
---------------------------------------------------------

---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.L999S04A;
CREATE TABLE LMS.L999S04A (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)     ,
	LOANREASO<PERSON>RC<PERSON>R(60)  ,
	TOTORIGINAL   DECIMAL(3,0) ,
	TOTCOPY       DECIMAL(3,0) ,
	AORIGINAL     DECIMAL(3,0) ,
	ACOPY         DECIMAL(3,0) ,
	BORIGINAL     DECIMAL(3,0) ,
	BCOPY         DECIMAL(3,0) ,
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_L999S04A PRIMARY KEY(OID)
) IN  EL_DATA_4KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.L999S04A IS '中長期契約書檔';
COMMENT ON LMS.L999S04A (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	LOANREASON    IS '借款原因', 
	TOTORIGINAL   IS '本約正本份數', 
	TOTCOPY       IS '本約副本份數', 
	AORIGINAL     IS '甲方存執正本份數', 
	ACOPY         IS '甲方存執副本份數', 
	BORIGINAL     IS '乙丙方存執正本份數', 
	BCOPY         IS '乙丙方存執副本份數', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
