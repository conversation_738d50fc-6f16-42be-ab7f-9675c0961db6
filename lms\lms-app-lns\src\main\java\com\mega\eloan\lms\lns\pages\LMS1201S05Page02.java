package com.mega.eloan.lms.lns.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;

/**
 * <pre>
 * 說明(企金授權外) - 營運概況
 * </pre>
 * 
 * @since 2012/1/19
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/19,<PERSON>,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms1201S05B")
public class LMS1201S05Page02 extends AbstractEloanForm {
//	public LMS1201S05Page02(PageParameters parameters) {
//		super(parameters);
//		add(new LMS1205S05Panel03("lms1205s05panel03"));
//		add(new LMS1205S05Panel05("lms1205s05panel05"));
//	}

	private static final long serialVersionUID = 1L;

	@Override
	public Class<? extends Meta> getDomainClass() {
		return null;
	}
	
	@Override
	public void execute(ModelMap model, PageParameters params) {
		
		renderJsI18N(LMS1201S05Page02.class);
	}	
	
    /*
     * (non-Javadoc)
     * 
     * @see com.mega.eloan.common.pages.AbstractEloanForm#getViewName()
     */
    @Override
    public String getViewName() {
        // 不要headerarea
        return "common/pages/None";
    }
}
