package com.mega.eloan.lms.lrs.flow;

import java.sql.Timestamp;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;

import com.mega.eloan.common.flow.AbstractFlowHandler;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.lrs.service.LMS1700Service;
import com.mega.eloan.lms.model.L170M01A;
import com.mega.eloan.lms.model.L170M01F;
import com.mega.eloan.lms.model.L186M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

@Component
public class LMS1700Flow extends AbstractFlowHandler {

	public static final String LMS1700FLOW = "LMS1700Flow";
	public static final String ZINITPARAM = "zInitParam";
	
	@Resource
	RetrialService retrialService;


	@Resource
	LMS1700Service lms1700Service;
	
	@Transition(node = "開始", value = "to_編製中_覆審組")
	public void init_flow(FlowInstance instance) {		
		
		
	}
	
	@Transition(node = "decisionE", value = "to_編製中_分行端")
	public void at1_forwardBoss(FlowInstance instance) throws CapException {		
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		String meta_oid = Util.trim(instance.getId());
		L170M01A meta = retrialService.findL170M01A_oid(meta_oid);
		
		_proc(meta, user.getUserId());
	}
	

	@Transition(node = "decisionA", value = "to_已覆核已核定")
	public void toEnd_A(FlowInstance instance) throws CapException {
		toEnd(instance);
	}	
	
	@Transition(node = "decisionB", value = "to_已覆核已核定")
	public void toEnd_B(FlowInstance instance) throws CapException  {
		toEnd(instance);
	}
	
	@Transition(node = "decisionC", value = "to_已覆核已核定")
	public void toEnd_C(FlowInstance instance)throws CapException  {
		toEnd(instance);
	}
	
	@Transition(node = "decisionD", value = "to_已覆核已核定")
	public void toEnd_D(FlowInstance instance) throws CapException {
		toEnd(instance);
	}
	
	private void toEnd(FlowInstance instance) throws CapException {		
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String assgn_userId = Util.trim(instance.getAttribute("assgn_userId")); 
		String instUserId = Util.isNotEmpty(assgn_userId)?assgn_userId:user.getUserId();
		
		String meta_oid = Util.trim(instance.getId());		
		L170M01A meta = retrialService.findL170M01A_oid(meta_oid);
		
		_proc(meta, instUserId);
	}	
	
	private void _proc(L170M01A meta, String userId) throws CapException{
		Timestamp now = CapDate.getCurrentTimestamp();
		
		if(true){
			meta.setApprover(userId);
			meta.setApproveTime(now);

			// 首次放行時間
			if(meta.getFirstAccTime() == null){
				meta.setFirstAccTime(now);
			}
			
			L170M01F l170m01f = meta.getL170m01f();
			l170m01f.setUpDate(now);
			//---
			retrialService.save(l170m01f);	
		}		
		retrialService.saveEndFlow(meta);
		//---
		lms1700Service.up_to_mis(meta);

		// J-110-0272 抽樣覆審
		// to_編製中_分行端 跟 to_已覆核已核定 都會進來
        L186M01A l186m01a = retrialService.findL186M01AByUniqueKey(null, Util.trim(meta.getOwnBrId()),
                Util.trim(meta.getCustId()), Util.trim(meta.getDupNo()), "");
        if (l186m01a != null) {
            l186m01a.setCaseMainId(meta.getMainId());
            l186m01a.setFinishTime(CapDate.getCurrentTimestamp());
            retrialService.save(l186m01a);
        }
	}
	
	@Override
	public Class<? extends Meta> getDomainClass() {
		return L170M01A.class;
	}
	
	@SuppressWarnings("rawtypes")
	@Override
	public Class getDocStatusEnumClass() {
		return RetrialDocStatusEnum.class;
	}
}