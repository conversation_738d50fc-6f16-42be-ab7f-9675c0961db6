/* 
 * LMS1205S06Panel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * <pre>
 * 其他(企金授權外)
 * </pre>
 * 
 * @since 2012/1/19
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/19,<PERSON>,new
 *          </ul>
 */
public class LMS1205S06Panel extends Panel {

	public LMS1205S06Panel(String id) {
		super(id);
		
		// UPGRADE: 前端須配合改Thymeleaf的樣式
		// MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// if("0C3".equals(user.getUnitNo())){
		// add(new Fragment("HKMAText_fragmentArea", "HKMAText_fragmentArea_show",
		// this));
		// }else{
		// add(new Label("HKMAText_fragmentArea", ""));
		// }
	}

	public LMS1205S06Panel(String id, boolean updatePanelName) {
		super(id, updatePanelName);
	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		if ("0C3".equals(user.getUnitNo())) {
			model.addAttribute("HKMAText_fragmentArea", "HKMAText_fragmentArea_show");
		} else {
			model.addAttribute("HKMAText_fragmentArea", "");
		}
	}

	/**/
	private static final long serialVersionUID = 1L;

}
