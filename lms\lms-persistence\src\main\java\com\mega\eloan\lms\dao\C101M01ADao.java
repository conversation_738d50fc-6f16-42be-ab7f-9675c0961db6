/* 
 * C101M01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C101M01A;

/** 個金徵信借款人主檔 **/
public interface C101M01ADao extends IGenericDao<C101M01A> {

	C101M01A findByOid(String oid);

	C101M01A findByMainIdone(String MainId);

	List<C101M01A> findByMainId(String mainId);

	C101M01A findByUniqueKey(String mainId, String ownBrId, String custId,
			String dupNo);

	List<C101M01A> findByIndex01(String mainId, String ownBrId, String custId,
			String dupNo);

	List<C101M01A> findByMainIds(String[] mainIds);

	int deleteByOid(String oid);
}