var PanelAction08 = {
    isInit: false,
    /**
     *頁面初始化的動作 只執行一次
     * */
    initAction: function(){
        this.afterSetFormDataAction();
        this.initGrid();
        this.initEvent();
    },
    /**
     *載入頁面後的動作 每次切換到此頁面都執行
     * */
    afterAction: function(){
        //PanelAction08.upLoadGrid.reload({
          //  mainId: _M.tabMainId
        //});
    },
    upLoadGrid: null,
    initGrid: function(){
        /**  檔案上傳grid  */
        this.upLoadGrid = $("#cls_gridfile").iGrid({
            handler: "lmscommongridhandler",
            height: 120,
            autowidth: true,
            postData: {
                formAction: "queryFile",
                fieldId: "clsCtrDoc",
                mainId: _M.tabMainId
            },
            rowNum: 15,
            multiselect: true,
            colModel: [{
                colHeader: i18n.def['uploadFile.srcFileName'],//檔案名稱,
                name: 'srcFileName',
                width: 120,
                align: "left",
                sortable: true,
                formatter: 'click',
                onclick: PanelAction08.download
            }, {
                colHeader: i18n.def['uploadFile.srcFileDesc'],//檔案說明
                name: 'fileDesc',
                width: 140,
                align: "center",
                sortable: true
            }, {
                colHeader: i18n.def['uploadFile.uploadTime'],//上傳時間
                name: 'uploadTime',
                width: 140,
                align: "center",
                sortable: true
            }, {
                name: 'oid',
                hidden: true
            }]
        });
    },
    initEvent: function(){
        /**  上傳檔案按鈕*/
        $("#cls_uploadFile").click(function(){
            var limitFileSize = 3145728;
            MegaApi.uploadDialog({
                fieldId: "clsCtrDoc",
                fieldIdHtml: "size='30'",
                fileDescId: "fileDesc",
                fileDescHtml: "size='30' maxlength='30'",
                subTitle: i18n.def('insertfileSize', {
                    'fileSize': (limitFileSize / 1048576).toFixed(2)
                }),
                limitSize: limitFileSize,
                width: 320,
                height: 190,
                data: {
                    mainId: _M.tabMainId,
                    sysId: "LMS"
                },
                success: function(){
                    PanelAction08.upLoadGrid.trigger("reloadGrid");
                }
            });
        });
        
        /**  刪除檔案按鈕 */
        $("#cls_deleteFile").click(function(){
            var $grid = PanelAction08.upLoadGrid;
            var select = $grid.getGridParam('selarrrow');
            if (select == "") {
            
                // TMMDeleteError=請先選擇需修改(刪除)之資料列
                return API.showMessage(i18n.def["TMMDeleteError"]);
            }
            
            // confirmDelete=是否確定刪除?
            API.confirmMessage(i18n.def["confirmDelete"], function(b){
                if (b) {
                    var data = [];
                    for (var i in select) {
                        data.push($grid.getRowData(select[i]).oid);
                    }
                    $.ajax({
                        handler: "lmscommonformhandler",
                        data: {
                            formAction: "deleteUploadFile",
                            oids: data
                        },
                        success: function(obj){
                            $grid.trigger("reloadGrid");
                        }
                    });
                }
            });
        });
        
        
        
    },
    
    /**  檔案下載  */
    download: function(cellvalue, options, data){
        $.capFileDownload({
            handler: "simplefiledwnhandler",
            data: {
                fileOid: data.oid
            }
        });
    },
    /**
     *載入頁面後的動作 每次切換到此頁面都執行
     * */
    afterSetFormDataAction: function(){
		if($('#pageNum8').val() == '0'){
			$('#itemDscr8').attr('distanceWord','44');
		}else{
			$('#itemDscr8').attr('distanceWord','53');
		}
		
		if (_M.AllFormData["04"]["snoKind"] && _M.AllFormData["04"]["snoKind"] == "20") {
			$("#page08isGutCut").show();
		}else{
			$("#page08isGutCut").hide();
		}
    }
};

var OtherTakingConditonTemplate = {
	
	editType_beforeSelect: '1',
	
	//在 CLS1151S01Page.js 的 setFormData()執行, 需等setFormData塞完資料, 在執行init
	init: function(){
		
		OtherTakingConditonTemplate.loadTypeGrid();
		OtherTakingConditonTemplate.loadItemContentGrid();
		OtherTakingConditonTemplate.loadContentGrid();
		
		var objs = API.loadCombos(["template_type"]);
        $("#bizCat").setItems({
            space: false,
            item: objs.template_type,
            format: "{value} - {key}"
        });
		
		OtherTakingConditonTemplate.switchSelectTemplateFormatButton($('#formatType').val());

		/**  選擇樣版格式 **/
	    $("#selectTmpFormatButton").click(function(){
	        OtherTakingConditonTemplate.openTemplateFormat();
	    });
		
		/**  選取簽報樣版 **/
	    $("#selectTemplateButton").click(function(){
			OtherTakingConditonTemplate.selectTemplateFormat();
	    });
		
		$("#bizCatItemAddButton").click(function(){
			OtherTakingConditonTemplate.createCustomItemAndContent('item');	
		});
		
		$("#bizCatContentAddButton").click(function(){
			OtherTakingConditonTemplate.createCustomItemAndContent('content');	
		});
		
		$(".previewTemplateFormat").click(function(){
			OtherTakingConditonTemplate.previewTemplateFormat();
		});
		
		$("#bizCatItemChgButton").click(function(){
			OtherTakingConditonTemplate.changeCutomItemName();	
		});
		
		$("#formatType").change(function(){
			OtherTakingConditonTemplate.changeEditFormatType($(this));	
		});
		
		$("#bizCatTypeDelButton").click(function(){
			OtherTakingConditonTemplate.deleteSigningTemplateData('type');	
		});
		
		$("#bizCatItemDelButton").click(function(){
			OtherTakingConditonTemplate.deleteSigningTemplateData('item');	
		});
		
		$("#bizCatContentDelButton").click(function(){
			OtherTakingConditonTemplate.deleteSigningTemplateData('content');	
		})
		
		$("#bizCatTypeMoveUpButton").click(function(){
			OtherTakingConditonTemplate.changeTemplateSequence("up", "type");
		});
		
		$("#bizCatTypeMoveDownButton").click(function(){
			OtherTakingConditonTemplate.changeTemplateSequence("down", "type");
		});
		
		$("#bizCatItemMoveUpButton").click(function(){
			OtherTakingConditonTemplate.changeTemplateSequence("up", "item");
		});
		
		$("#bizCatItemMoveDownButton").click(function(){
			OtherTakingConditonTemplate.changeTemplateSequence("down", "item");
		});
		
		$("#bizCatContentMoveUpButton").click(function(){
			OtherTakingConditonTemplate.changeTemplateSequence("up", "content");
		});
		
		$("#bizCatContentMoveDownButton").click(function(){
			OtherTakingConditonTemplate.changeTemplateSequence("down", "content");
		});
	},
	
	checkMoveUp: function(rowIds){
        if (rowIds == null || rowIds.length > 1 || rowIds.length == 0) {
            //此功能只能選擇單筆
            return i18n.cls1151s01['signingTemplate.errorMsg.onlyOneRowSelected'];
        }
		
		if(rowIds[0] == 1){
			//無法移動順序
			return i18n.cls1151s01['signingTemplate.errorMsg.sequenceCantbeChanged'];
		}
		
		return '';
	},
	
	checkMoveDown: function(rowIds, templateId){

        if (rowIds == null || rowIds.length > 1 || rowIds.length == 0) {
            //此功能只能選擇單筆
            return i18n.cls1151s01['signingTemplate.errorMsg.onlyOneRowSelected'];
        }
		
		var gridId = OtherTakingConditonTemplate.getSelectedGridId(templateId);
		var totalRows = $('#' + gridId).jqGrid("getGridParam", 'records');
		
		if(rowIds[0] == totalRows){
			//無法移動順序
			return i18n.cls1151s01['signingTemplate.errorMsg.sequenceCantbeChanged'];
		}
		
		return '';
	},
	
	checkDelete: function(rowIds){
		
        if (!rowIds || rowIds == "") {
			//請先選擇需修改(刪除)之資料列
            return i18n.def["TMMDeleteError"];
        }
		
		if (rowIds == null || rowIds.length > 1 || rowIds.length == 0) {
            //此功能只能選擇單筆
            return i18n.cls1151s01['signingTemplate.errorMsg.onlyOneRowSelected'];
        }
		
		return '';
	},
	
	checkUpdateItemName: function(rowIds){
        if (rowIds == null || rowIds.length > 1 || rowIds.length == 0) {
            //此功能只能選擇單筆
            return i18n.cls1151s01['signingTemplate.errorMsg.onlyOneRowSelected'];
        }
		
		var data = OtherTakingConditonTemplate.getSelectedRowData(rowIds, 'item');
		if(data.isDefaultItem == 'Y'){
			//非為自訂項目，不得更改！
			return i18n.cls1151s01['signingTemplate.errorMsg.onlyDefaultItemCanUpdate'];
		}
		
		return '';
	},
	
	//選擇樣版格式
	openTemplateFormat: function(){
		
		$("#bizCatTypeThickbox").thickbox({
            title: "",//i18n.lms1401s02["btn.L140S09A"],
            width: 800,
            height: 300,
            align: "center",
            valign: "bottom",
            buttons: {
	            "close": function(){
	                $.thickbox.close();
//	                if (!inits.toreadOnly) {
	                    $.ajax({
	                        handler: 'cls1151m01formhandler',
	                        action: "previewTemplateFormat",
	                        data: {
	                            tabFormMainId: $("#tabFormMainId").val()
	                        },
	                        success: function(obj){
	                            $("#CLS1151Form08").find("#itemDscr8").val(obj.drc);
	                        }
	                    });
//	                }
	            }
            }
        });

        $("#bizCatTypeGrid").trigger("reloadGrid");
	},
	
	selectTemplateFormat: function(){
		$("#newL140S09ABox").thickbox({
            title: "",
            width: 900,
            height: 200,
            align: "center",
            valign: "bottom",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var bizCat = $("#bizCat :selected").val();
					var msg = OtherTakingConditonTemplate.checkBizCatIsDuplicate();
					if (msg && msg != "") {
						return API.showErrorMessage(msg);
					}
					else{
						OtherTakingConditonTemplate.saveSigningTemplateData();
					}
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });

        // 初始化
        $("#bizCat").val("");
        $("#itemSpan_loanTPs").remove();
	},
	
	checkBizCatIsDuplicate: function(){
		var msg;
		var bizCat = $("#bizCat :selected").val();
		$.ajax({
            handler: 'cls1151m01formhandler',
			async:false,
            data: {
                formAction: "checkIsDuplicateOfSigningTemplateType",
                bizCat: bizCat
            },
            success: function(obj){
				msg = obj.msg;
			}
		});
		return msg;
	},
	
	saveSigningTemplateData: function(){
		
        $.ajax({
            handler: 'cls1151m01formhandler',
            data: {
                formAction: "createSigningTemplateData",
                bizCat: $("#bizCat :selected").val(),
                loanTPsName: $("#loanTPsName").val()
            },
            success: function(obj){
                $.thickbox.close();
                $("#bizCatTypeGrid").trigger("reloadGrid");
            }
        });
	},
	
	loadTypeGrid: function(){
		
		$("#bizCatTypeGrid").iGrid({
			handler: "cls1151gridhandler",
			rowNum: 15,
			height: 100,
			autowidth: true,
			postData: {
				formAction: "queryL140s10aBizCatMainGridData",
				tabFormMainId: $("#tabFormMainId").val()
			},
			loadComplete: function(){
				$('#bizCatTypeGrid a').click(function(e){
					// 避免<a href="#"> go to top
					e.preventDefault();
				});
			},
			colModel: [{
				colHeader: i18n.cls1151s01['L140S10A.signingTemplate.sequence'],//序號
				name: 'seqName',
				width: 20,
				sortable: false,
				align: "center"
			}, {
				colHeader: i18n.cls1151s01['L140S10A.signingTemplate.type'],//樣版類別
				name: 'bizCat',
				width: 20,
				sortable: false,
				align: "center"
			}, {
				colHeader: i18n.cls1151s01['L140S10A.signingTemplate.name'],//樣版名稱
				name: 'bizCatName',
				width: 100,
				align: "left",
				sortable: false,
				formatter: 'click',
				onclick: function(cellValue, options, rowObject){
					OtherTakingConditonTemplate.openItemContentBox(null, null, rowObject)
				} 
			}, {
				colHeader: i18n.cls1151s01['L140S10A.signingTemplate.loanTpsName'],//抬頭名稱
				name: 'loanTpsName',
				width: 100,
				sortable: false,
				align: "center"
			}, {
				name: 'seq',
                hidden: true
			}],
			ondblClickRow: function(rowid){
				var data = $("#bizCatTypeGrid").getRowData(rowid);
				OtherTakingConditonTemplate.openItemContentBox(null, null, data);
			}
		}).trigger("reloadGrid");
	},
	
	openItemContentBox: function(cellvalue, options, data){
        var title = data.bizCatName;
        if(data.bizCatName.length > 40){
            title = data.bizCatName.substring(0, 39);
        }
        $("#bizCatItemContentThickbox").thickbox({
            title: title,
            width: 700,
            height: 450,
            modal: false   // 會有X關閉視窗
        });
		
        $("#bizCatItemContentGrid").jqGrid("setGridParam", {
          postData: {
              formAction: "queryTemplateItemAndContentGridData",
              tabFormMainId: $("#tabFormMainId").val(),
              bizCat: data.bizCat
          },
          search: true
        }).trigger("reloadGrid");
		
		$("#selectedBizCat").val(data.bizCat);
    },
	
	loadItemContentGrid: function(){
		 $("#bizCatItemContentGrid").iGrid({
            handler: "cls1151gridhandler",
            rowNum: 15,
            height: 300,
            autowidth: true,
            multiselect: true,
            loadComplete: function(){
                $('#l140s09aGrid a').click(function(e){
                    // 避免<a href="#"> go to top
                    e.preventDefault();
                });
            },
            colModel: [{
				colHeader: i18n.cls1151s01['L140S10A.signingTemplate.item'],//項目
				name: 'bizItemDesc',
				width: 20,
				sortable: false,
				align: "center"
            },{
				colHeader: i18n.cls1151s01['L140S10A.signingTemplate.content'],//內容
				name: 'bizContent',
				width: 80,
				sortable: false,
				align: "left"
			},{
				name: 'mainId',
                hidden: true
			},{
				name: 'bizCat',
                hidden: true
			},{
				name: 'bizItem',
                hidden: true
			},{
				name: 'seq',
                hidden: true
			},{
				name: 'isDefaultItem',
                hidden: true
			}],
            ondblClickRow: function(rowid){
                var data = $("#bizCatItemContentGrid").getRowData(rowid);
                OtherTakingConditonTemplate.openContentBox(null, null, data);
            }
        }).trigger("reloadGrid");
	},
	
	openContentBox: function(cellvalue, options, data){

        var title = data.bizItemDesc;
        if(data.bizItemDesc.length > 50){
            title = data.bizItemDesc.substring(0, 49);
        }
        $("#detailContentThickBox").thickbox({
            title: title,
            width: 800,
            height: 350,
            modal: false,   // 會有X關閉視窗
            i18n: i18n.def
        });
        
		ilog.debug("bizCatContentGrid do setGridParam and reloadGrid");
		$("#bizCatContentGrid").jqGrid("setGridParam", {
            postData : {
                formAction: "queryTemplateContentGridData",
                mainId: data.mainId,
				bizCat: data.bizCat,
				bizItem: data.bizItem
            },
            search: true
        }).trigger("reloadGrid");
		
		$("#selectedBizCat").val(data.bizCat);
		$("#selectedBizItem").val(data.bizItem);
    },
	
	loadContentGrid: function(){

        $("#bizCatContentGrid").iGrid({
            handler: "cls1151gridhandler",
            height: 200,
            autowidth: true,
       		multiselect: true,    
            loadComplete: function(){
                $('#bizCatContentGrid a').click(function(e){
                    // 避免<a href="#"> go to top
                    e.preventDefault();
                });
            },
            colModel: [{
                colHeader: i18n.cls1151s01['L140S10A.signingTemplate.content'],//內容
                name: 'bizContent',
                width: 550,
                sortable : false,
                align: "left"
            },{
                name: 'seq',
                hidden: true
            },{
                name: 'oid',
                hidden: true
            },{
                name: 'bizCat',
                hidden: true
            }, {
                name: 'bizItem',
                hidden: true
            }],
            ondblClickRow: function(rowid){
                var data = $("#bizCatContentGrid").getRowData(rowid);
                OtherTakingConditonTemplate.openDetailContentBox(null, null, data);
            }
        }).trigger("reloadGrid");
    },
	
	openDetailContentBox: function(cellvalue, options, data){

        $.ajax({
            handler: 'cls1151m01formhandler',
            action: "queryL140s10cDetailContent",
            data: {
                oid: data.oid
            },
            success: function(obj){
                $('#l140s10bContentForm').injectData(obj);
            }
        }).done(function(){
            var buttons = {
                "saveData": function(){
                    $.ajax({
                        handler: 'cls1151m01formhandler',
                        action: "updateL140s10cContent",
						async:false,
                        data: {
                            detailForm: JSON.stringify($("#l140s10bContentForm").serializeData()),
                            oid: data.oid,
							content: $("#content").val()
                        },
                        success: function(obj){
							//saveSuccess=儲存成功
							$("#bizCatContentGrid").trigger("reloadGrid");
							$("#bizCatItemContentGrid").trigger("reloadGrid");
                            CommonAPI.confirmMessage(i18n.def["saveSuccess"], function(b){
                                if (b) {
                                    $.thickbox.close();
                                }
                            });
                            
                        }
                    });
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }

//            if (inits.toreadOnly) {
//                delete buttons.saveData;
//            }

            $("#l140s10bContentBox").thickbox({
                title: "",
                width: 700,
                height: 200,
                modal: false,   // 會有X關閉視窗
                i18n: i18n.def,
                align: "center",
                valign: "bottom",
                buttons: buttons
            });
        });
    },
	
	getSelectedGridId: function(templateId){
		
		if(templateId == 'type'){
			return 'bizCatTypeGrid';
		}
		
		if(templateId == 'item'){
			return 'bizCatItemContentGrid';
		}
		
		if(templateId == 'content'){
			return 'bizCatContentGrid';
		}
		
		return '';
	},
	
	getSelectedRowId: function(templateId){
		
		var gridId = OtherTakingConditonTemplate.getSelectedGridId(templateId);
		
		if(templateId == 'type'){
			return $('#' + gridId).jqGrid("getGridParam", 'selrow');
		}
		
		if(templateId == 'item' || templateId == 'content'){
			return $('#' + gridId).jqGrid("getGridParam", 'selarrrow');
		}
		
		return '';
	},
	
	getSelectedRowData: function(rowId, templateId){
		var gridId = OtherTakingConditonTemplate.getSelectedGridId(templateId);
		return $('#' + gridId).getRowData(rowId);
	},
	
	changeTemplateSequence: function(upDown, templateId){
		
		var rowId = OtherTakingConditonTemplate.getSelectedRowId(templateId);
		
		if(upDown == 'up'){
			msg = OtherTakingConditonTemplate.checkMoveUp(rowId);
		}
		else{
			msg = OtherTakingConditonTemplate.checkMoveDown(rowId, templateId);
		}
		
		if(msg != ''){
			return API.showMessage(msg);
		}
			
		var data = OtherTakingConditonTemplate.getSelectedRowData(rowId, templateId);

		$.ajax({
			type: "POST",
			handler: 'cls1151m01formhandler',
			data: {
				formAction: "changeSigningTemplateSequence",
				bizCat: data.bizCat,
				bizItem: data.bizItem,
				seq: data.seq,
				upOrDown: upDown,
				templateId: templateId,
				tabFormMainId: $("#tabFormMainId").val()
			},
			success: function(obj){
				
				if(templateId == 'type'){
					$("#bizCatTypeGrid").trigger("reloadGrid");
				}
				
				if(templateId == 'item'){
					$("#bizCatItemContentGrid").trigger("reloadGrid");
				}
				
				if(templateId == 'content'){
					$("#bizCatContentGrid").trigger("reloadGrid");
				}
				
				$("#bizCatItemContentGrid").trigger("reloadGrid");
			}
		});
	},
	
	deleteSigningTemplateData: function(templateId){
		
		var warningMsg;
		var rowId = OtherTakingConditonTemplate.getSelectedRowId(templateId)
		
		if(templateId == 'type'){
			//樣板項目及細項內容將會一併刪除，是否確定執行？
			warningMsg = i18n.cls1151s01["signingTemplate.warningMsg.isDeleteAllData"];
		}

		if(templateId == 'item'){
			//樣板項目及細項內容將會一併刪除，是否確定執行？
			warningMsg = i18n.cls1151s01["signingTemplate.warningMsg.isDeleteAllData"];
		}
		
		if(templateId == 'content'){
			//刪除樣板內容，是否確定執行？
			warningMsg = i18n.cls1151s01["signingTemplate.warningMsg.isDeleteTemplateContent"];
		}
		
		var msg = OtherTakingConditonTemplate.checkDelete(rowId);
		if(msg != ''){
			return API.showMessage(msg);
		}

        CommonAPI.confirmMessage(warningMsg, function(b){
            if (b) {
                var data = OtherTakingConditonTemplate.getSelectedRowData(rowId, templateId);
                $.ajax({
                    type: "POST",
                    handler: 'cls1151m01formhandler',
                    data: {
                        formAction: "deleteSigningTemplateData",
                        bizCat: data.bizCat,
						bizItem: data.bizItem,
						l140s10cOid: data.oid,
						templateId: templateId,
						isDefaultItem: data.isDefaultItem
                    },
                    success: function(responseData){
						if (templateId == 'type') {
							 $("#bizCatTypeGrid").trigger("reloadGrid");
						}
                        if (templateId == 'item') {
							 $("#bizCatItemContentGrid").trigger("reloadGrid");
						}
						if (templateId == 'content') {
							 $("#bizCatContentGrid").trigger("reloadGrid");
							 $("#bizCatItemContentGrid").trigger("reloadGrid");
						}
                    }
                });
            }
        });
	},
	
    createCustomItemAndContent: function(templateId){
		
		var width;
		if (templateId == 'item') {
			$("#itemDescTr").show();
			$("#contentTextTr").hide().val("");
			width = 300;
		}
		//content
		else{
			$("#contentTextTr").show();
			$("#itemDescTr").hide().val("");
			width = 700;
		}
		
		$("#modifyCustomItemAndContentBox").thickbox({
	        title: "",
	        modal: false,
	        width: width,
	        height: 200,
	        buttons: {
	            "sure": function(){

					var content;
					if(templateId == 'item'){
						content = $.trim($("#itemDesc").val());
					}
					
					if(templateId == 'content'){
						content = $.trim($("#contentText").val());
					}
					
					if(content == ''){
						return API.showMessage('請輸入資料')
					}
					
	                $.ajax({
	                    handler: 'cls1151m01formhandler',
	                    action: "createCustomItemAndContent",
	                    data: {
	                        bizCat: $("#selectedBizCat").val(),
							bizItem: $("#selectedBizItem").val(),
							content: content,
							templateId: templateId
	                    },
	                    success: function(obj){
							$.thickbox.close();
							
							if(templateId == 'content'){
								$("#bizCatContentGrid").trigger("reloadGrid");
							}
							
							$("#bizCatItemContentGrid").trigger("reloadGrid");
	                    }
	                });
	            },
	            "cancel": function(){
	                $.thickbox.close();
	            }
	        }
	    })
	},
	
	previewTemplateFormat: function(){
	    $.ajax({
	        handler: 'cls1151m01formhandler',
	        action: "previewTemplateFormat",
	        data: {},
	        success: function(obj){
	            $("#previewSpan").html(DOMPurify.sanitize(obj.drc));
	        }
	    }).done(function(){
	        $("#previewBox").thickbox({
	            title: "",
	            align: "center",
	            valign: "bottom",
	            width: 800,
	            height: 600,
	            buttons: {
	                "close": function(){
	                    $.thickbox.close();
	                }
	            }
	        });
	    });
	},
	
	changeCutomItemName: function(){
		
		$("#itemDescTr").show();
		$("#contentTextTr").hide();
		
		var rowId = OtherTakingConditonTemplate.getSelectedRowId('item');
		var data = OtherTakingConditonTemplate.getSelectedRowData(rowId, 'item');
		var msg = OtherTakingConditonTemplate.checkUpdateItemName(rowId);
		if(msg != ''){
			return API.showMessage(msg);
		}
		
		var data = OtherTakingConditonTemplate.getSelectedRowData(rowId, 'item');
		
		$("#modifyCustomItemAndContentBox").thickbox({
	        title: "",
	        modal: false,
	        width: 300,
	        height: 200,
	        buttons: {
	            "sure": function(){
					
	                $.ajax({
	                    handler: 'cls1151m01formhandler',
	                    action: "changeCutomItemName",
	                    data: {
	                        bizCat: data.bizCat,
							bizItem: data.bizItem,
							bizItemDesc: $('#itemDesc').val()
	                    },
	                    success: function(obj){
							$.thickbox.close();
							$("#bizCatItemContentGrid").trigger("reloadGrid");
	                    }
	                });
	            },
	            "cancel": function(){
	                $.thickbox.close();
	            }
	        }
	    });
	},
	
	switchSelectTemplateFormatButton: function(value){

		OtherTakingConditonTemplate.editType_beforeSelect = value;
		
		if(value == 2){
			$("#selectTmpFormatButton").show();
		}
		else{
			$("#selectTmpFormatButton").hide();
		}
	},
	
    changeEditFormatType: function(x){
		
        CommonAPI.confirmMessage(i18n.cls1151s01['signingTemplate.warningMsg.swithFormatType'], function(b){
            
			if (b) {
				var value = x.val();
				OtherTakingConditonTemplate.switchSelectTemplateFormatButton(value);
				OtherTakingConditonTemplate.editType_beforeSelect = value;
				
                $.ajax({
                    type: "POST",
                    handler: 'cls1151m01formhandler',
                    data: {
                        formAction: "deleteOtherTakingConditionDesc",
                        tabFormMainId: $("#tabFormMainId").val(),
                        formatType: value
                    },
                    success: function(responseData){
                        $("#itemDscr8").val("");
                    }
                });
            }
			else{
				$('#formatType').val(OtherTakingConditonTemplate.editType_beforeSelect)
			}
        });
	}
        
}

_M.pageInitAcion["08"] = PanelAction08;
