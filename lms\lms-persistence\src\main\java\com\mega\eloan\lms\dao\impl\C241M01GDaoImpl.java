package com.mega.eloan.lms.dao.impl;

import com.mega.eloan.lms.dao.C241M01GDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C241M01G;
import java.util.List;

import org.springframework.stereotype.Repository;
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;


/**
 * 消金覆審考核表dao
 */
@Repository
public class C241M01GDaoImpl extends LMSJpaDao<C241M01G, String> implements C241M01GDao {

	@Override
	public C241M01G findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C241M01G> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.setMaxResults(Integer.MAX_VALUE);
		List<C241M01G> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<C241M01G> findByIndex02(String mainId, String itemType, String itemName){
		ISearch search = createSearchTemplete();
		List<C241M01G> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (itemType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "itemType", itemType);
		if (itemName != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "itemName", itemName);
		search.setMaxResults(Integer.MAX_VALUE);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
}