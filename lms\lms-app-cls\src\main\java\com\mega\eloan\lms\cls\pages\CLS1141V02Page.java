/* 
 * CLS1141V02Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.pages;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.html.EloanPageFragment;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.eloan.lms.base.panels.GridViewFilterPanel01;
import com.mega.sso.context.MegaSSOSecurityContext;

import tw.com.jcs.auth.AuthService;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 授信簽報書 - 待覆核(個金)
 * </pre>
 * 
 * @since 2012/11/14
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/11/14,Miller,new
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls1141v02")
public class CLS1141V02Page extends AbstractEloanInnerView {

	@Autowired
	AuthService au;

	@Override
	public void execute(ModelMap model, PageParameters params) {
		setGridViewStatus(CreditDocStatusEnum.海外_待覆核);

		//========================
		Set<String> eloanRoles = MegaSSOSecurityContext.getEloanRoles();
		String pgmDept = MegaSSOSecurityContext.getPGMDept();
		int transactionCode = Util.parseInt(params
				.getString(EloanConstants.TRANSACTION_CODE));
		
		boolean _Accept = au.auth(pgmDept, eloanRoles, transactionCode,
				AuthType.Accept);		
		
		// 加上Button
		List<EloanPageFragment> list = new ArrayList<EloanPageFragment>();

		list.add(LmsButtonEnum.View);
		list.add(LmsButtonEnum.Filter);
		if(_Accept){
			list.add(LmsButtonEnum.BatchAprvProd69); // 2020/05/20 add 勞工紓困案整批覆核
		}
		addToButtonPanel(model, list);
		
		// 套用哪個i18N檔案
		renderJsI18N(CLS1141V01Page.class);
		renderJsI18N(LMSCommomPage.class);

		setupIPanel(new GridViewFilterPanel01(PANEL_ID), model, params);

		// UPGRADE: 待確認畫面是否正常
		model.addAttribute("hasHtml", false);
		model.addAttribute("loadScript",
				"loadScript('pagejs/cls/CLS1141V01Page');");
	}// ;

}
