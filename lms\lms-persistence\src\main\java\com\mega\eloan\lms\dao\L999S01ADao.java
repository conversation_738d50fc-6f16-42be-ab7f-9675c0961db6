/* 
 * L999S01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L999S01A;

/** 綜合授信契約書檔 **/
public interface L999S01ADao extends IGenericDao<L999S01A> {

	L999S01A findByOid(String oid);

	L999S01A findByMainId(String mainId);
}