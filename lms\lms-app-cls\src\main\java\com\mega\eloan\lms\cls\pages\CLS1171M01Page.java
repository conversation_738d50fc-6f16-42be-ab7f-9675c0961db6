/* 
 * CLS1171M01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.cls.panels.CLS1171S01Panel;
import com.mega.eloan.lms.cls.panels.CLS1171S02Panel;
import com.mega.eloan.lms.cls.panels.CLS1171S03Panel;
import com.mega.eloan.lms.model.L141M01A;

import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * [國內企金]聯行額度明細表
 * </pre>
 * 
 * @since 2012/11/29
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/11/29,REX,new
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls1171m01/{page}")
public class CLS1171M01Page extends AbstractEloanForm {

	final String TAB_CTX = "_tabCtx";
	final String TAB_SIGN = "tab-";

	@Override
	public void execute(ModelMap model, PageParameters params) {
		// 依權限設定button
		addAclLabel(model, new AclLabel("_btnDOC_EDITING", params,
				getDomainClass(), AuthType.Modify, CreditDocStatusEnum.海外_編製中));

		addAclLabel(model, new AclLabel("_btnWAIT_APPROVE", params,
				getDomainClass(), AuthType.Accept, CreditDocStatusEnum.海外_待覆核));
		renderJsI18N(CLS1141M01Page.class);
		renderJsI18N(CLS1171M01Page.class);
		// tabs
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		String tabID = TAB_SIGN + Util.addZeroWithValue(page, 2); // 指定ID
		Panel panel = getPanel(page);
		panel.processPanelData(model, params);
		model.addAttribute("tabID", tabID);
	}

	// 頁籤
	public Panel getPanel(int index) {
		Panel panel = null;
		switch (index) {
		case 1:
			panel = new CLS1171S01Panel(TAB_CTX, true);
			break;
		case 2:
			panel = new CLS1171S02Panel(TAB_CTX, true);
			renderJsI18N(CLS1151S01Page.class);
			break;
		case 3:
			panel = new CLS1171S03Panel(TAB_CTX, true);
			break;
		default:
			panel = new CLS1171S01Panel(TAB_CTX, true);
			break;
		}
		return panel;
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return L141M01A.class;
	}
}
