package com.mega.eloan.lms.dc.action;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.util.Arrays;
import java.util.List;

import org.apache.commons.io.IOUtils;

import com.mega.eloan.lms.dc.conf.ViewListConfig;
import com.mega.eloan.lms.dc.util.Util;

/**
 * <pre>
 * ChkDtlRcdNumber :檢查主檔紀錄之筆數與明細檔存在筆數是否相符
 * </pre>
 * 
 * @since 2012/12/20
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/20,Bang,new
 *          <li>2013/02/21,Bang :此程式暫時留存,等確定用不到時即可刪除,踢退清單已在GetItemValue產生
 *          </ul>
 */
public class ChkDtlRcdNumber extends BaseAction {
	private String lmsDxlDirRootPath = "";
	private String lmsLogsDirPath = "";
	
	/**
	 * 初始化必要資訊
	 */
	public void doCheck(String viewListName) {
		logger.info("正在初始化 ChkDtlRcdNumber 必要資訊...");
		long t1 = System.currentTimeMillis();
		this.lmsDxlDirRootPath = this.configData.getLmsDxlDirRootPath();// homePath\today\LMS
		this.lmsLogsDirPath = this.configData.getLmsLogsDirPath();// User當前工作目錄\log\logs\執行日期\LMS
		
		// 讀取viewList
		List<String> viewList = ViewListConfig.getInstance().getViewList(
				viewListName);
		for (int i = 0; i < viewList.size(); i++) {
			this.chkDtlRecords(viewList.get(i));
		}
		logger.info("ChkDtlRcdNumber :" + viewListName
				+ " 執行完畢 ...TOTAL TIME(微秒)===>"
				+ (System.currentTimeMillis() - t1));
	}

	/**
	 * 檢查主檔之筆數與明細檔筆數是否相符
	 * 
	 * @param nsfViewData
	 *            String :viewList中的資料
	 */
	private void chkDtlRecords(String nsfViewData) {
		// 取得各項路徑及分行名formName
		String[] str = nsfViewData.split(";");
		String strBrn = str[0].substring(2, 5);// 分行名稱,EX:201
		String viewName = str[1];// View Name ,EX:VLMSDB201B

		PrintWriter logsChk = null;// 輸出log
		long tt1 = System.currentTimeMillis();

		// 建立錯誤清單的檔案名稱: LMS dxl根目錄\Branch \DtlRcdError.lst
		String errFile = this.lmsDxlDirRootPath + File.separator + strBrn
				+ File.separator + "DtlRcdError.lst";

		BufferedWriter out;
		File errorList;
		try {
			// 建立各份行logs
			String brnLogPath = this.lmsLogsDirPath + File.separator
					+ "chkDtl_" + viewName + "_" + strBrn + ".log";
			logsChk = new PrintWriter(new BufferedWriter(
					new OutputStreamWriter(new FileOutputStream(new File(
							brnLogPath)))), true);
			logsChk.println(viewName + "_" + strBrn + "起始時間 :"
					+ Util.getNowTime());

			errorList = new File(errFile);
			if (errorList.exists()) {
				out = new BufferedWriter(new FileWriter(errorList, true));
			} else {
				out = new BufferedWriter(new FileWriter(errorList));
			}
			String dxlPath = this.lmsDxlDirRootPath + File.separator + strBrn
					+ File.separator + viewName;
			String[] dxllist = Util.getSameAttachFile(dxlPath, ".dxl");
			Arrays.sort(dxllist);
			int landNumber = 0;
			int bldNumber = 0;
			int mNumber = 0;
			int landCount = 0;
			int bldCount = 0;
			int mCount = 0;
			String wkUnid = "";
			String wkFName = "";

			for (String dxlName : dxllist) {
				String[] fName = dxlName.split(".dxl");// EX:{FLMS120M01_2E3761E1BB971A2B48257A7D00143D9C,''}
				String[] fParm = fName[0].split("_");// EX:{FLMS120M01,2E3761E1BB971A2B48257A7D00143D9C}
				String[] dtlNums;
				// System.out.println("fn-->" + fnDir + "/" + fn);
				int lth = fParm.length;
				if (lth == 2) { // 主檔資料
					/*
					 * if (bFirstRecord) { bFirstRecord = false; }
					 */
					// 目前沒有明細檔,這裡是擔保品的邏輯全部不能用 要再修改
					if (landNumber != landCount) {
						out.write(wkUnid + ":" + viewName + ":E0001土地明細資料筆數錯誤("
								+ wkFName + ")");
						out.newLine();
					}
					if (bldNumber != bldCount) {
						out.write(wkUnid + ":" + viewName + ":E0002建物明細資料筆數錯誤("
								+ wkFName + ")");
						out.newLine();
					}
					if (mNumber != mCount) {
						out.write(wkUnid + ":" + viewName
								+ ":E0003不動產以外明細資料筆數錯誤(" + wkFName + ")");
						out.newLine();
					}
					// ------------------------------------------------------------------------------------------------------
					// bang Test Used
					out.write(fName[0] + ":" + viewName + ":E0001土地明細資料筆數錯誤("
							+ dxlName + ")");
					out.newLine();
					// ------------------------------------------------------------------------------------------------------

					// 這裡要用fName[0];給完整的檔名,否則在DXLReject時會比對不到正確的檔名(因為企金是formName_unid擔保品是:unid_formName)
					wkUnid = fName[0];
					wkFName = dxlName;
					landNumber = 0;
					bldNumber = 0;
					mNumber = 0;
					landCount = 0;
					bldCount = 0;
					mCount = 0;
					dtlNums = getRcdNum(dxlPath + File.separator + dxlName,
							fParm[0]).split(";"); // fn:dxl
					// name
					// ;
					// fParm[1]
					// :
					// form
					// name
					if (dtlNums.length == 2) {
						landNumber = Integer.parseInt(dtlNums[0]); // 主檔記錄之土地明細筆數
						bldNumber = Integer.parseInt(dtlNums[1]); // 主檔記錄之建物明細筆數
					} else {
						mNumber = Integer.parseInt(dtlNums[0]); // 主檔記錄之明細筆數--不動產以外
					}
				} // end of if (lth == 2)

				if (lth == 3) { // 明細資料
					if ("FCMS101S01".equals(fParm[0])
							|| "FCMS101S03".equals(fParm[0])) {
						landCount++; // 土地明細筆數
					} else if ("FCMS101S02".equals(fParm[0])) {
						bldCount++; // 建物明細筆數
					} else {
						mCount++; // 不動產以外明細筆數
					}
				} // end of if (lth == 3)
			} // end of for(String fn : fnlist)
			IOUtils.closeQuietly(out);
			logsChk.println("結束時間 :" + Util.getNowTime());
			long cost = System.currentTimeMillis() - tt1;
			logsChk.println(strBrn + "分行 ChkDtlRcdNumber TOTAL TIME===>"
					+ Util.millis2minute(cost));

		} catch (Exception e) {
			e.printStackTrace();
		} finally {

			IOUtils.closeQuietly(logsChk);
		}
	} // end of chkDtlRecords()

	private String getRcdNum(String dxlFile, String formName) {
		try {
			String rcd = readFile(dxlFile);
			if ("FCMS101M01".equals(formName)) { // 不動產讀取土地及建物明細筆數
				String landNum = getCountValue(rcd, "land_count");
				if ("0".equals(landNum)) {
					landNum = getCountValue(rcd, "lr_count");
				}
				String bldNum = getCountValue(rcd, "bld_count");
				return landNum + ";" + bldNum;

			} else { // 不動產以外讀取明細筆數
				String mCount = getCountValue(rcd, "m_count");
				return mCount;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "";
	}

	/**
	 * 讀取.dxl並將之轉換為String型態
	 * 
	 * @param dxlFile
	 *            String :要被讀取的dxl檔(path/dxlName)
	 * @return sb StringBuffer:
	 * @throws IOException
	 */
	private String readFile(String dxlFile) throws IOException {
		BufferedReader in = new BufferedReader(new FileReader(dxlFile));
		StringBuffer str = new StringBuffer();
		while (in.ready()) {
			str.append(in.readLine()).append("\n");
		}
		return str.toString();
	}

	private String getCountValue(String rcd, String fldName) {
		String idxBgn = "<item name='" + fldName + "'><number>";
		String idxEnd = "</number>";
		String countValue = "0";
		int idxBgnLength = idxBgn.length();
		int idx = 0;
		int idx1 = 0;
		int idx2 = 0;

		idx1 = rcd.indexOf(idxBgn, idx);
		idx2 = rcd.indexOf(idxEnd, idx1 + idxBgnLength);
		if (idx1 > 0 && idx2 > 0) {
			countValue = rcd.substring(idx1 + idxBgnLength, idx2);
		}
		return countValue;
	}

}
