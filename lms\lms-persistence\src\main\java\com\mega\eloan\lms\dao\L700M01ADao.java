/* 
 * L700M01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L700M01A;

/** 案件分案對照表檔 **/
public interface L700M01ADao extends IGenericDao<L700M01A> {

	L700M01A findByOid(String oid);
	
	List<L700M01A> findByMainId(String mainId);
	
	L700M01A findByUniqueKey(String branchId, String subject);
	
	L700M01A findByBranchId(String ownBrId,String branchId);

	List<L700M01A> findByIndex01(String branchId, String subject);
}