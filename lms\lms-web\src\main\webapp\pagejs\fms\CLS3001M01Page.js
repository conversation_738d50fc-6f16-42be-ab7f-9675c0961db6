var _handler = "cls3001formhandler";

$(document).ready(function(){
	var tabForm = $("#tabForm");
	var btnPanel = $("#buttonPanel");
	$.form.init({
		formHandler:_handler, 
		formAction:'query', 
		loadSuccess:function(json){
			
			if(!$("#buttonPanel").find("#btnSave").is("button") || json.lock) {
				tabForm.lockDoc();
				initControl_lockDoc = true;
				json['initControl_lockDoc'] = initControl_lockDoc;
			}			
			tabForm.buildItem();
			

			tabForm.injectData(json);			
		}
	});
	
	btnPanel.find("#btnSave").click(function(){
		if(!tabForm.valid()){
			return;
		}
		
		saveAction({'allowIncomplete':'Y'}).done(function(json){
			if(json.saveOkFlag){
				var dyna = [];
				if(true){
					dyna.push(i18n.def.saveSuccess);
				}	
				
				if(json.IncompleteMsg){
					dyna.push(json.IncompleteMsg);
				}
				if(json.promptMsg){
					dyna.push(json.promptMsg);
				}
				
				API.showMessage(dyna.join("<br/>-------------------<br/>"));
			}
        });
	}).end().find("#btnSend").click(function(){ 
		
		if(!tabForm.valid()){
			return;
		}
		
		saveAction().done(function(json_saveAction){
    		if(json_saveAction.saveOkFlag){
    			API.confirmMessage(i18n.def.confirmApply, function(result){
    	            if (result) {
    	            	flowAction({'decisionExpr':'呈主管'});    	
    	        	}
    	    	});
    		}
    	});
	}).end().find("#btnAccept").click(function(){	

		var _id = "_div_btnAccept";
		var _form = _id+"_form";		
    	
		if ($("#"+_id).size() == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");
			dyna.push("<form id='"+_form+"'>");
			//dyna.push("	<table><tr><td>");
			dyna.push("		<p><label><input type='radio' name='decisionExpr' value='1' class='required' />核定</label></p>");
			dyna.push("		<p><label><input type='radio' name='decisionExpr' value='2' class='required' />退回</label></p>");
			//dyna.push(" </td></tr></table>");
			dyna.push("</form>");
			
			dyna.push("</div>");
			
		     $('body').append(dyna.join(""));
		}
		//clear data
		$("#"+_form).reset();
		
		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
	        title: i18n.def["confirmApprove"],
	        width: 380,
            height: 180,
            align: "center",
            valign: "bottom",
            modal: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if (!$("#"+_form).valid()) {
                        return;
                    }
                    var val = $("#"+_form).find("[name='decisionExpr']:checked").val();
                    if(val=="1"){
                    	
                    	flowAction({'decisionExpr':'核定'});
                    }else if(val=="2"){
                    	flowAction({'decisionExpr':'退回'});
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
	    });
	});

	$("#btnFilterCntrNo").click(function(){
		var queryId = $("#queryId").val();
		var queryCntrNo = $("#queryCntrNo").val();
		//=========================
		var grid_id = "grid_importCnrNoData";
		
		var my_post_data = {
			'formAction' : "importCnrNoData"		
			, 'queryId': queryId
			, 'queryCntrNo': queryCntrNo
		};
		
		
		$("#"+grid_id).jqGrid("setGridParam", {
			postData : my_post_data,
			search : true
		}).trigger("reloadGrid");	
	});
	
	$("#btnImport").click(function(){
		var grid_id = "grid_importCnrNoData";
		
		var my_post_data = {
			formAction : "importCnrNoData"
		};
		var grid_height = 280;
		if($("#"+grid_id+".ui-jqgrid-btable").length >0){
			$("#"+grid_id).jqGrid("setGridParam", {
				postData : my_post_data,
				search : true
			}).trigger("reloadGrid");	        		
		}else{
			
			$("#"+grid_id).iGrid({
				handler : 'cls3001gridhandler',
				height : grid_height,
				postData : my_post_data,		
				sortname: 'approveTime',
				sortorder: 'desc',
				colModel : [{ name : 'l140m01a_mainId', hidden : true}
				,{ name : 'caseBrId', hidden : true}
				,{colHeader : i18n.cls3001m01["C900S02D.custId"], width : 40, name : 'custId', sortable : false}
				,{colHeader : i18n.cls3001m01["C900S02D.dupNo"], width : 10, name : 'dupNo', sortable : false}
				,{colHeader : i18n.cls3001m01["C900S02D.custName"], width : 80, name : 'custName', sortable : false}
				,{colHeader : i18n.cls3001m01["C900S02D.cntrNo"], width : 40, name : 'cntrNo', sortable : false}
				,{colHeader : i18n.cls3001m01["C900S02D.document_no"], width :160, name : 'document_no', sortable : false}
				,{colHeader : i18n.cls3001m01["C900S02D.curr"], width : 30, name : 'curr', sortable : false}
				,{colHeader : i18n.cls3001m01["C900S02D.applyAmt"], width :60, name : 'applyAmt', align : 'right',sortable : false}
				]
			});
		}    
		
		$("#label_queryId").val(i18n.cls3001m01["C900S02D.custId"]+"：");
		$("#label_queryCntrNo").val(i18n.cls3001m01["C900S02D.cntrNo"]+"：");
	            
		$("#thickBoxImportCnrNoData").thickbox({
	        title: '',
	        width: 940, height: (grid_height+230), align: 'center', valign: 'bottom', modal: false,
	        buttons: {
	            "sure": function(){            	
	            	var $gridview = $("#"+grid_id);
	            	var row = $gridview.getGridParam('selrow');
	            	if(row){
	            		var data = $gridview.getRowData(row);
	            		tabForm.injectData(data);
	            		saveAction({'allowIncomplete':'Y'});
	            		$.thickbox.close();
	            	}else{
	            		API.showMessage(i18n.def.grid_selector);//請選擇資料
	            	}
	            },
	            "cancel": function(){
	               $.thickbox.close();
	            }
	        }
	    });
	})
	
	var saveAction = function(opts){		
		if(tabForm.valid()){			
			return $.ajax({
                type: "POST",
                handler: _handler,
                data:$.extend( {
                		formAction: "saveMain"
                    }, 
                    tabForm.serializeData(),
                    ( opts||{} )
                ),                
                success: function(json){
                	tabForm.injectData(json);
                	//更新 opener 的 Grid
                    CommonAPI.triggerOpener("gridview", "reloadGrid");
                }
            });
		}else{
			return $.Deferred();
		}
	}
	
	var flowAction = function(opts){
		return $.ajax({
            type: "POST",
            handler: _handler, action: "flowAction",
            data:$.extend( {
            	mainOid: $("#mainOid").val(), 
            	mainDocStatus: $("#mainDocStatus").val() 
                }
                , ( opts||{} )
            ),                
            success: function(json){            	
            	API.triggerOpener();//gridview.reloadGrid 
            	window.close();            	
            }
        });
	}
	
});

