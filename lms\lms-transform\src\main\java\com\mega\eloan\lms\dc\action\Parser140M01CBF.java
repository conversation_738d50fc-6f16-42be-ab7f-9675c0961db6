package com.mega.eloan.lms.dc.action;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.w3c.dom.Document;

import com.mega.eloan.lms.dc.base.DCException;
import com.mega.eloan.lms.dc.bean.L140M01CBFBean;
import com.mega.eloan.lms.dc.util.DXLUtil;
import com.mega.eloan.lms.dc.util.TextDefine;
import com.mega.eloan.lms.dc.util.Util;

/**
 * <pre>
 * Parser140M01CBF
 * </pre>
 * 
 * @since 2012/12/20
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/20,Bang,new
 *          <li>20123/03/07: LoanTP欄位1.空白時，就不要轉到DB，2.如果不為空白但是非代碼的(有中文的)，
 *          科目代碼(loanTP)寫入Z99，並將中文塞到科目補充說明(subjDscr)內
 *          </ul>
 */
public class Parser140M01CBF extends AbstractLMSCustParser {
	private static final boolean BYPASS_PRECHECK = false;

	// UFO@2013-01-21
	public static final int MAX_LOOP = 14;// 每一筆DXL應最多產生17筆資料

	private static final String BF_STR = "_BF";
	private static List<String[]> KEY_LIST = new ArrayList<String[]>();
	private static final String[] KEYS = { "LNSubject_", "LNSubjectMemo_",
			"PayDeadline_", "Oth_", };

	static {
		initKeyString();
	}

	/**
	 * @param pid
	 * @param doViewName
	 * @param formGroup
	 */
	public Parser140M01CBF(String pid, String doViewName, String formGroup) {
		super(pid, doViewName, formGroup);
	}

	/**
	 * 讀取,處理及轉換
	 * 
	 * @param dxlPath
	 *            String : .dxl檔存放路徑
	 * @param dxlName
	 *            :.dxl列表中的.dxl檔名
	 * @param strBrn
	 *            String:分行名稱
	 * @param domDoc
	 *            DOM Document:已轉為DOM Document的.dxl檔
	 */
	@SuppressWarnings("unused")
	protected void transferDXL(String dxlPath, String dxlName, String strBrn,
			Document domDoc, String dxlXml) {
		long t1 = System.currentTimeMillis();
		try {
			String[] k1 = dxlName.split(TextDefine.ATTACH_DXL);// EX:{FLMS120M01_2E3761E1BB971A2B48257A7D00143D9C,.dxl}
			String[] k2 = k1[0].split("_");// EX:{FLMS120M01,2E3761E1BB971A2B48257A7D00143D9C}
			String tmpMainId = "";
			if (k2.length == 2) {
				tmpMainId = k2[1];// 主檔
			} else {
				tmpMainId = k2[2];// 明細檔之UNID
			}

			// 20130417
			// Sandra若CNTRDOCID有值，以CNTRDOCID為140開頭所有的table的mainid；若無值，則取unid為mainid
			// 20130419 Sandra因CNTRDOCID仍會有重覆，建霖mail通知調整使用WEBELOANMAINID
			String cntrDocId = getItemValue(domDoc, "WEBELOANMAINID");
			tmpMainId = cntrDocId.isEmpty() ? tmpMainId : cntrDocId;

			char[] countAry = new char[] { 'A', 'B', 'C', 'D', 'E', 'F', 'G',
					'H', 'I', 'J', 'K', 'L', 'M', 'N' };
			for (int i = 0; i < countAry.length; i++) {
				char ctValue = countAry[i];
				if (!BYPASS_PRECHECK && isKeyEmpty(dxlXml, (ctValue - 'A'))) {
					continue;
				}

				if (DEBUG && logger.isDebugEnabled()) {
					logger.debug("@@@@@@@@ DO=>" + dxlName + " :: " + ctValue);
				}

				L140M01CBFBean L140cbf = new L140M01CBFBean();

				L140cbf.setOid(Util.getOID());
				L140cbf.setMainId(tmpMainId);
				L140cbf.setSubjSeq(String.valueOf(i + 1));

				// 科目代碼
				String loanTPValue = getItemValue(domDoc, "LNSubject_"
						+ ctValue + "_BF");
				// 2013-03-07 Modify by Bang
				String oldLoanTP = loanTPValue;
				boolean flag = false;
				if (StringUtils.isNotBlank(loanTPValue)) {
					flag = Util.isChineseChr(loanTPValue);
					if (flag) {
						loanTPValue = "Z99";
					}
				}
				L140cbf.setLoanTP(loanTPValue);
				// 科目順序
				L140cbf.setSubjSeq("XX");
				// 科目補充說明
				// 2013-03-07 Modify by Bang
				String subjDscrValue = getItemValue(domDoc, "LNSubjectMemo_"
						+ ctValue);
				if (flag) {
					subjDscrValue = oldLoanTP;
				}
				L140cbf.setSubjDscr(subjDscrValue);
				// 清償期限－天數 2013-03-15 Modify by Bang 非數字塞到科目補充說明
				String lmtDaysValue = getItemValue(domDoc, "PayDeadline_"
						+ ctValue);
				boolean lmtDaysflag = Util.isChineseChr(lmtDaysValue);
				if (lmtDaysflag) {
					L140cbf.setLmtDays(TextDefine.EMPTY_STRING);
					L140cbf.setSubjDscr(subjDscrValue + lmtDaysValue);
				} else {
					L140cbf.setLmtDays(lmtDaysValue);
				}
				// 清償期限－詳其他敘作條件
				String lmtOtherValue = getItemValue(domDoc, "Oth_" + ctValue);
				// 2013-01-25 若db2.lmtOther="詳"，則寫入1，其他寫入""
				String typeValue = DXLUtil.getLmtOther(lmtOtherValue);
				L140cbf.setLmtOther(typeValue);

				// 2013-03-07 Modify by Bang: 當loanTP欄位為空白時不需產生資料
				String loanTP = L140cbf.getLoanTP();
				if (!loanTP.trim().equals(TextDefine.EMPTY_STRING)) {
					this.txtWrite.println(L140cbf.toString());
					this.parserTotal++;
				}
			}// end of for(char ctValue : countAry)
		} catch (Exception e) {
			String errmsg = "【" + strBrn
					+ "】分行執行Parser140M01CBF 之transferDXL時產生錯誤,dxl檔名:" + dxlName
					+ ",dxlPath=" + dxlPath;
			throw new DCException(errmsg, e);
		} finally {
			if (DEBUG && logger.isDebugEnabled()) {
				logger.debug("@@@@@@@@ TOTAL_COST="
						+ (System.currentTimeMillis() - t1));
			}
		}
	}

	private boolean isKeyEmpty(String xml, int idx) {
		String[] keystr = KEY_LIST.get(idx);
		for (int i = 0, size = keystr.length; i < size; i++) {
			if (xml.indexOf(keystr[i]) == -1 && xml.indexOf(KEYS[i]) != -1) {
				return false;
			}
		}
		return true;
	}

	private static void initKeyString() {
		Logger logger = LoggerFactory.getLogger(Parser140M01CBF.class);

		for (int i = 0; i < MAX_LOOP; i++) {
			String[] keystr = new String[KEYS.length];
			for (int j = 0, jsize = KEYS.length; j < jsize; j++) {
				keystr[j] = "<item name='" + KEYS[j] + ((char) ('A' + i))
						+ ((j == 0) ? BF_STR : "") + "'><text/></item>";
			}
			KEY_LIST.add(keystr);

			if (logger.isDebugEnabled()) {
				logger.debug(ArrayUtils.toString(keystr));
			}
		}

	}

}
