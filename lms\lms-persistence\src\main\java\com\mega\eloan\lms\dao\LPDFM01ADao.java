/* 
 * LPDFM01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.LPDFM01A;

/** 授信 PDF 舊案主檔 **/
public interface LPDFM01ADao extends IGenericDao<LPDFM01A> {

	LPDFM01A findByOid(String oid);
	
	List<LPDFM01A> findByMainId(String mainId);

	List<LPDFM01A> findByIndex01(String mainId, String ownBrId, String custId, String dupNo);
}