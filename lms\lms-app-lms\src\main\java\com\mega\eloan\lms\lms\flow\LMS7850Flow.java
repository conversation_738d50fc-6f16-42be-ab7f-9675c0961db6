package com.mega.eloan.lms.lms.flow;

import java.util.Date;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONObject;
import org.springframework.stereotype.Component;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.exception.FlowMessageException;
import com.mega.eloan.common.flow.AbstractFlowHandler;
import com.mega.eloan.common.gwclient.EloanBatchClient;
import com.mega.eloan.common.gwclient.EloanServerBatReqMessage;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.NumberService;
import com.mega.eloan.lms.dao.L140M01ATMP1Dao;
import com.mega.eloan.lms.dao.L785M01ADao;
import com.mega.eloan.lms.dao.L999LOG01ADao;
import com.mega.eloan.lms.lms.pages.LMS7850M01Page;
import com.mega.eloan.lms.mfaloan.service.MisStoredProcService;
import com.mega.eloan.lms.model.L785M01A;
import com.mega.eloan.lms.model.L999LOG01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.core.FlowException;

/**
 * <pre>
 * 授管處解除停權 - 流程
 * </pre>
 * 
 * @since 2013/1/24
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/1/24,Miller,new
 *          </ul>
 */
@Component
public class LMS7850Flow extends AbstractFlowHandler {
	public static final String FLOW_CODE = "LMS7850Flow";

	@Resource
	L785M01ADao l785m01aDao;

	@Resource
	BranchService branch;
	@Resource
	NumberService number;
	@Resource
	MisStoredProcService misStoredProcService;

	@Resource
	BranchService branchService;

	@Resource
	EloanBatchClient eloanBatClient;

	@Resource
	L140M01ATMP1Dao l140m01atmp1Dao;

	@Resource
	L999LOG01ADao l999log01aDao;

	@Override
	public Class<? extends Meta> getDomainClass() {
		return L785M01A.class;
	}

	/**
	 * 編製中到待覆核
	 * 
	 * @param instance
	 *            流程資料
	 * @throws CapMessageException
	 * @throws NumberFormatException
	 */
	@Transition(node = "停權編製中", value = "to停權待覆核")
	public void start(FlowInstance instance) throws NumberFormatException,
			CapMessageException {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L785M01A meta = (L785M01A) l785m01aDao.findByOid(instanceId);
		MegaSSOUserDetails unit = MegaSSOSecurityContext.getUserDetails();
		// 若無案號則進行給號
		if (Util.isEmpty(Util.trim(meta.getCaseSeq()))
				&& Util.isEmpty(Util.trim(meta.getCaseNo()))) {
			meta.setCaseSeq(Integer.parseInt(number.getNumberWithMax(
					L785M01A.class, unit.getUnitNo(), null, 99999)));
			meta.setCaseYear(Util.parseInt(TWNDate.toAD(new Date()).substring(
					0, 4)));
			StringBuilder caseNum = new StringBuilder();
			IBranch ibranch = branch.getBranch(unit.getUnitNo());
			caseNum.append(Util.toFullCharString(Util.trim(meta.getCaseYear())))
					.append(Util.trim(ibranch.getNameABBR()))
					.append(UtilConstants.Field.兆)
					.append(UtilConstants.Field.授字第)
					.append(Util.toFullCharString(Util.addZeroWithValue(
							Util.trim(meta.getCaseSeq()), 5)))
					.append(UtilConstants.Field.號);
			meta.setCaseNo(caseNum.toString());

		}
		meta.setUpdater(unit.getUserId());
		meta.setUpdateTime(CapDate.getCurrentTimestamp());
		meta.setCaseDate(new Date());
		l785m01aDao.save(meta);
	}

	@Transition(node = "停權待覆核", value = "to決策")
	public void next(FlowInstance instance) {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L785M01A meta = (L785M01A) l785m01aDao.findByOid(instanceId);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String result = (String) instance.getAttribute("result");
		if ("to核定".equals(result)) {
			// 檢查主管與經辦是否為同一人
			if (Util.notEquals(meta.getOwnBrId(), UtilConstants.BankNo.授管處)) {
				if (user.getUserId().equals(Util.trim(meta.getUpdater()))) {
					// EFD0053=WARN|覆核人員不可與“經辦人員或其它覆核人員”為同一人|
					throw new FlowMessageException("EFD0053");
				}
			}

			instance.setAttribute("result", "to核定");
		} else {
			instance.setAttribute("result", "to退回停權編製中");
		}
	}

	/**
	 * 退回編製中
	 * 
	 * @param instance
	 *            流程資料
	 */
	@Transition(node = "決策", value = "to退回停權編製中")
	public void back(FlowInstance instance) {
		// String instanceId = instance.getParentInstanceId() != null ? instance
		// .getParentInstanceId().toString() : instance.getId().toString();
		// L120M01A meta = (L120M01A) metaDao.findByOid(getDomainClass(),
		// instanceId);
		// // 新增簽章欄
		// lmsService.deleteL120M01F(meta.getMainId(),
		// UtilConstants.BRANCHTYPE.分行,
		// new String[] { UtilConstants.STAFFJOB.執行覆核主管L4 });
		// // 變更額度明細表為編製中
		// lmsService.resetL140M01A(meta, FlowDocStatusEnum.編製中.getCode());
	}

	/**
	 * 核准
	 * 
	 * @param instance
	 *            流程資料
	 * @throws CapException
	 * @throws FlowException
	 */
	@Transition(node = "決策", value = "to核定")
	public void complete(FlowInstance instance) throws FlowException,
			CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS7850M01Page.class);

		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L785M01A meta = (L785M01A) l785m01aDao.findByOid(instanceId);

		String fxUserId = meta.getUpdater();

		int delCount = l140m01atmp1Dao.delByUid(meta.getMainId());

		L999LOG01A logDoc = l999log01aDao.findByMainId(meta.getMainId());
		String filterForm = meta.getItemDscr();

		// 刪除中文欄位SHOW，因為太長SEND 會ERROR
		JSONObject newFilter = JSONObject.fromObject(filterForm);
		String[] deleteShow = { "fxCurrShow", "fxLnSubjectShow",
				"fxRateText1Show", "fxCollateral1Show", "fxProdKindShow",
				"fxLnSubjectClsShow", "fxRateTextClsShow" };
		for (String delKey : deleteShow) {
			if (newFilter.containsKey(delKey)) {
				newFilter.remove(delKey);
			}
		}

		if (logDoc == null) {
			logDoc = new L999LOG01A();
			logDoc.setItemType("2");
			logDoc.setMainId(meta.getMainId());
		}

		// 因為資料很多，使用呼叫批次的方式執行
		try {
			EloanServerBatReqMessage req = new EloanServerBatReqMessage();
			req.setUserId(user.getUserId());
			req.setRunType(EloanServerBatReqMessage.RUN_TYPE_QUEUE);
			req.setSchId("SLMS-00047");
			JSONObject paraJson = new JSONObject();
			paraJson.put(EloanConstants.MAIN_OID, logDoc.getOid());
			paraJson.put("uid", meta.getMainId());
			paraJson.put("updater", fxUserId);
			paraJson.put("filterForm", newFilter.toString());
			paraJson.put("unitType", meta.getUnitType());
			paraJson.put("docStatus", meta.getDocStatus());

			paraJson.put("userId", fxUserId);
			paraJson.put("unitNo", meta.getOwnBrId());

			StringBuffer batchParams = new StringBuffer();
			batchParams.append("REQUEST=").append(paraJson.toString());

			req.setParams(batchParams.toString());

			req.setDupeId(StringUtils.left(logDoc.getMainId(), 30));

			eloanBatClient.send(req);

			// 新增一筆L999LOG01A

			logDoc.setResult("S"); // 排程中
			logDoc.setItemDscr(filterForm);
			logDoc.setCreateTime(CapDate.getCurrentTimestamp());
			logDoc.setCreator(fxUserId);
			logDoc.setUpdater("");
			logDoc.setUpdateTime(null);
			l999log01aDao.save(logDoc);

		} catch (Exception e) {
			// 新增一筆L999LOG01A

			// logDoc = new L999LOG01A();
			logDoc.setItemType("2");
			logDoc.setResult("N"); // 排程中
			logDoc.setItemDscr(filterForm);
			logDoc.setCreateTime(CapDate.getCurrentTimestamp());
			logDoc.setCreator(fxUserId);
			logDoc.setUpdater("");
			logDoc.setUpdateTime(null);
			// L784M01A.error2=排程失敗，請洽資訊處:
			logDoc.setExecMsg(prop.getProperty("L784M01A.error2")
					+ StrUtils.getStackTrace(e));
			l999log01aDao.save(logDoc);
			e.printStackTrace();
			throw new CapMessageException(prop.getProperty("L784M01A.error2")
					+ e.toString(), getClass());

		}

		// 設定覆核主管與覆核時間
		meta.setApprover(user.getUserId());
		meta.setApproveTime(CapDate.getCurrentTimestamp());

		l785m01aDao.save(meta);
	}

	/**
	 * 取得完整Id(統編加重覆序號)
	 * 
	 * @param custid
	 * @param dupNo
	 * @return
	 */
	private String getAllCust(String custid, String dupNo) {
		StringBuilder strb = new StringBuilder();
		// if ("0".equals(dupNo)) {
		// dupNo = "";
		// }
		return strb.append(CapString.fillString(custid, 10, false, ' '))
				.append(dupNo).toString();
	}

	/**
	 * 取得上傳案號-> 民國年 + 分行別+{LMS/CLS}+末五碼流水號
	 * 
	 * @param l785m01a
	 *            授管處解除停權主檔
	 * @return
	 */
	private static String getUploadCaseNo(L785M01A l785m01a) {
		String schema = "LMS";
		String custId = Util.trim(l785m01a.getCustId());
		// 統編：第一碼英文、第二~十數字(長度是10)--CLS
		if (custId.length() == 10) {
			char c = custId.charAt(0);
			if (c >= 'A' && c <= 'Z') {
				if (Util.isNumeric(custId.substring(1))) {
					schema = "CLS";
				}
			}
		}
		return StrUtils.concat(l785m01a.getCaseYear() - 1911,
				l785m01a.getOwnBrId(), schema,
				Util.addZeroWithValue(l785m01a.getCaseSeq(), 5));
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Class getDocStatusEnumClass() {
		return CreditDocStatusEnum.class;
	}
}