package com.mega.eloan.lms.lns.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.dao.DocFileDao;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.lms.base.common.BranchRate;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dao.C120S01GDao;
import com.mega.eloan.lms.dao.L140M01ADao;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.lms.report.impl.LMS1205R01RptServiceImpl;
import com.mega.eloan.lms.lms.service.LMS1405Service;
import com.mega.eloan.lms.lns.panels.LMSS07APanel;
import com.mega.eloan.lms.lns.report.impl.LMS1201R01RptServiceImpl;
import com.mega.eloan.lms.lns.service.LMS1201Service;
import com.mega.eloan.lms.lns.service.LMS1201XLSService;
import com.mega.eloan.lms.lns.service.LMS1401Service;
import com.mega.eloan.lms.mfaloan.service.MisGrpcmpService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C120S01G;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01C;
import com.mega.eloan.lms.model.L120S01A;
import com.mega.eloan.lms.model.L120S01C;
import com.mega.eloan.lms.model.L120S04A;
import com.mega.eloan.lms.model.L120S04B;
import com.mega.eloan.lms.model.L120S04D;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01B;
import com.mega.eloan.lms.model.L140M01E;
import com.mega.sso.service.BranchService;

import jxl.SheetSettings;
import jxl.Workbook;
import jxl.format.Alignment;
import jxl.format.Border;
import jxl.format.BorderLineStyle;
import jxl.format.PageOrientation;
import jxl.write.Label;
import jxl.write.NumberFormats;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import jxl.write.WriteException;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 主要關係戶與本行授信往來比較表產Excel
 * </pre>
 * 
 * @since 2012/12/28
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/28,Miller
 *          </ul>
 */
@Service("lms1201xlsservice")
public class LMS1201XLSServiceImpl implements LMS1201XLSService {

	@Resource
	LMS1201Service service1201;
	@Resource
	LMS1405Service service1405;
	@Resource
	C120S01GDao c120s01gDao;
	@Resource
	EloandbBASEService eloanDBService;
	@Resource
	CodeTypeService codetypeService;
	@Autowired
	DocFileService fileService;
	@Resource
	DocFileDao docFileDao;
	@Resource
	BranchService branch;
	@Resource
	LMSService lmsService;

	// J-107-0225_05097_B1001 Web e-Loan企金授信簽報書新增集團關係企業與本行授信往來條件比較表
	@Resource
	MisGrpcmpService misGrpcmpService;
	@Resource
	MisdbBASEService misDbService;
	@Resource
	DwdbBASEService dwdbService;
	@Resource
	L140M01ADao l140m01aDao;

	@Resource
	LMS1401Service lms1401Service;

	// 過濾所有以<開頭以>結尾的標籤
	private final static String regxpForHtml = "<([^>]*)>";

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(LMS1201XLSServiceImpl.class);

	@SuppressWarnings("unused")
	@Override
	public void getContent(PageParameters params) throws CapException,
			WriteException, IOException {

		String type = Util.trim(params.getString("type"));
		if (Util.equals(type, "") || Util.equals(type, "1")) {
			// 產生主要關係戶與本行授信往來比較表(Excel)
			creExcel(params);
		} else if (Util.equals(type, "2")) {
			// J-107-0225_05097_B1001 Web e-Loan企金授信簽報書新增集團關係企業與本行授信往來條件比較表
			// 產生集團／關係企業與本行授信往來條件比較表
			creExcel2(params);
		}

		// return null;
	}

	public void creExcel(PageParameters params) {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);
		// J-109-0370 相關評估改版
		String oidL120s04d = Util
				.trim(params.getString(EloanConstants.MAIN_ID));// oidL120s04d
		ByteArrayOutputStream baos = null;
		List<Map<String, Object>> listMap = new ArrayList<Map<String, Object>>();
		WritableFont font12 = null;
		WritableCellFormat format12Center = null;
		WritableCellFormat format12Left = null;
		WritableCellFormat format12Right = null;
		WritableCellFormat format12LeftNO = null;
		WritableCellFormat format12RightNO = null;
		L120S04D l120s04d = lmsService.findL120s04dByOid(oidL120s04d);
		String keyCustId = "";
		String keyDupNo = "";
		if (l120s04d != null) {
			keyCustId = Util.trim(l120s04d.getKeyCustId());
			keyDupNo = Util.trim(l120s04d.getKeyDupNo());
		}
		String mainId = l120s04d.getMainId();
		L120M01A meta = service1201.findL120m01aByMainId(mainId);
		// List<L120S04A> tempNew = service1201.findL120s04aByMainId(mainId);
		List<L120S04A> tempNew = lmsService.findL120s04aByMainIdKeyCustIdDupNo(
				mainId, keyCustId, keyDupNo);
		List<L120S04A> list = new ArrayList<L120S04A>(tempNew);

		if (list != null && !list.isEmpty()) {

			Collections.sort(list, new Comparator<L120S04A>() {

				@Override
				public int compare(L120S04A object1, L120S04A object2) {
					// TODO Auto-generated method stub
					int cr = 0;
					String[] resStr1 = Util.trim(object1.getCustRelation())
							.split(",");
					Arrays.sort(resStr1);
					String[] resStr2 = Util.trim(object2.getCustRelation())
							.split(",");
					Arrays.sort(resStr2);

					int a = resStr2[0].compareTo(resStr1[0]);

					String prtFlag1 = object1.getPrtFlag();
					String prtFlag2 = object2.getPrtFlag();
					int prtFlag = prtFlag2.compareTo(prtFlag1);

					if (prtFlag != 0) {
						cr = (prtFlag > 0) ? -1 : 5;
					} else if (a != 0) {
						cr = (a > 0) ? -2 : 4;
					} else {
						long b = (object2.getProfit() == null ? 0 : object2
								.getProfit())
								- (object1.getProfit() == null ? 0 : object1
										.getProfit());
						if (b != 0) {
							cr = (b > 0) ? 3 : -3;
						} else {
							int c = object2.getCustId().compareTo(
									object1.getCustId());
							if (c != 0) {
								cr = (c > 0) ? -4 : 2;
							} else {
								// String oid1 = object1.getOid();
								// String oid2 = object2.getOid();
								// int oidFlag = oid2.compareTo(oid2);
								// if(oidFlag != 0){
								// cr = (oidFlag > 0)? -5:1;
								// }
							}
						}
					}

					return cr;
				}
			});
		}

		String brno = null;
		String custId = null;
		String dupNo = null;
		String custName = null;
		String caseNo = null;

		// listName="LMSNoList"
		// String ranMainId = IDGenerator.getRandomCode();
		DocFile docFile = new DocFile();
		docFile.setBranchId((meta != null) ? Util.trim(meta.getCaseBrId()) : "");
		docFile.setContentType("application/msexcel");
		docFile.setMainId(oidL120s04d);
		docFile.setCrYear(CapDate.getCurrentDate("yyyy"));
		docFile.setFieldId("LMSNoList");
		// other.msg61=借戶暨關係戶與本行授信往來比較表
		// LMSNoList.xls請顯示為 主要關係戶與本行授信往來比較表.xls
		docFile.setSrcFileName("LMS1205R24A.xls");
		docFile.setUploadTime(CapDate.getCurrentTimestamp());
		docFile.setSysId(fileService.getSysId());
		docFile.setData(new byte[] {});
		fileService.save(docFile, false);
		File file = null;

		String filename = null;
		String xlsOid = null;

		xlsOid = docFile.getOid();
		filename = LMSUtil.getUploadFilePath(docFile.getBranchId(),
				oidL120s04d, "LMSNoList");
		file = new File(filename);
		file.mkdirs();

		try {
			file = new File(filename + "/" + xlsOid + ".xls");

			// baos = new ByteArrayOutputStream();
			// WritableWorkbook book = Workbook.createWorkbook(baos);
			WritableWorkbook book = Workbook.createWorkbook(file);
			WritableSheet sheet = book.createSheet("1", 0);

			/*
			 * 1.1方向 SheetSetting#setOrientation(PageOrientation po)； 參數：
			 * PageOrientation#LANDSCAPE 橫向打印 PageOrientation# PORTRAIT 縱向打印 (A)
			 * SheetSetting #setScaleFactor (int);百分比形式
			 */
			SheetSettings settings = sheet.getSettings();
			settings.setOrientation(PageOrientation.LANDSCAPE);
			// EXCEL 請設定預設為橫印、預設為1頁寬
			// // 縮放比例
			// settings.setScaleFactor(100);
			// 縮放比例頁寬
			settings.setFitWidth(1);
			// 縮放比例頁高
			settings.setFitHeight(5000);
			// 頁面距(英吋)
			// 上
			settings.setTopMargin((double) 0.394);
			// 下
			settings.setBottomMargin((double) 0.394);
			// 左
			settings.setLeftMargin((double) 0.197);
			// 右
			settings.setRightMargin((double) 0.197);
			// 頁首
			settings.setHeaderMargin((double) 0.5);
			// 頁尾
			settings.setFooterMargin((double) 0.5);

			// 設定字型與格式
			// other.msg60=新細明體
			font12 = new WritableFont(WritableFont.createFont(pop
					.getProperty("other.msg60")), 12, WritableFont.NO_BOLD);
			// 將某欄位強制轉成文字欄位顯示
			WritableCellFormat contentFromart = new WritableCellFormat(
					NumberFormats.TEXT);
			format12Center = LMSUtil.setCellFormat(format12Center, font12,
					Alignment.CENTRE);
			format12Left = LMSUtil.setCellFormat(contentFromart, font12,
					Alignment.LEFT);
			format12Right = LMSUtil.setCellFormat(format12Right, font12,
					Alignment.RIGHT);
			format12LeftNO = LMSUtil.setCellFormat(format12LeftNO, font12,
					Alignment.LEFT, false, false);
			format12RightNO = LMSUtil.setCellFormat(format12RightNO, font12,
					Alignment.RIGHT, false, false);
			sheet.mergeCells(0, 0, 12, 0);
			if (meta != null) {
				custId = Util.trim(meta.getCustId());
				dupNo = Util.trim(meta.getDupNo());
				brno = Util.trim(meta.getCaseBrId());
				custName = Util.trim(meta.getCustName());
			}
			if (l120s04d != null) {
				custId = Util.trim(l120s04d.getKeyCustId());
				dupNo = Util.trim(l120s04d.getKeyDupNo());
				// custName = Util.trim(l120s04d.getCustName());
				L120S01A l120s01a = service1201.findL120s01aByUniqueKey(mainId,
						keyCustId, keyDupNo);
				if (l120s01a != null) {
					custName = Util.trim(l120s01a.getCustName());
				}
			}
			StringBuilder sb = new StringBuilder();
			// other.msg61=借戶暨關係戶與本行授信往來比較表
			sb.append(Util.trim(custId)).append(" ")
					.append(Util.trim(custName)).append("借戶暨關係戶與本行授信往來比較表")
					.append(" ").append(TWNDate.toTW(new Date()));
			Label labelT1 = new Label(0, 0, sb.toString(), format12Left);
			sheet.addCell(labelT1);

			// other.msg62=公司名稱
			Label labelT2 = new Label(0, 1, pop.getProperty("other.msg62"),
					format12Center);
			sheet.addCell(labelT2);
			// other.msg136=分行
			Label labelT15 = new Label(1, 1, pop.getProperty("other.msg136"),
					format12Center);
			sheet.addCell(labelT15);
			// other.msg63=信用評等
			Label labelT3 = new Label(2, 1, pop.getProperty("other.msg63"),
					format12Center);
			sheet.addCell(labelT3);
			// other.msg64=關係
			Label labelT4 = new Label(3, 1, pop.getProperty("other.msg64"),
					format12Center);
			sheet.addCell(labelT4);
			// other.msg65=核准日期
			Label labelT5 = new Label(4, 1, pop.getProperty("other.msg65"),
					format12Center);
			sheet.addCell(labelT5);
			// other.msg137=額度序號
			Label labelT16 = new Label(5, 1, pop.getProperty("other.msg137"),
					format12Center);
			sheet.addCell(labelT16);
			// other.msg66=額度（仟元）
			Label labelT6 = new Label(6, 1, pop.getProperty("other.msg66"),
					format12Center);
			sheet.addCell(labelT6);
			// other.msg67=性質
			Label labelT7 = new Label(7, 1, pop.getProperty("other.msg67"),
					format12Center);
			sheet.addCell(labelT7);
			// other.msg68=科目
			Label labelT8 = new Label(8, 1, pop.getProperty("other.msg68"),
					format12Center);
			sheet.addCell(labelT8);
			// other.msg69=清償期限
			Label labelT9 = new Label(9, 1, pop.getProperty("other.msg69"),
					format12Center);
			sheet.addCell(labelT9);
			// other.msg70=保證人
			Label labelT10 = new Label(10, 1, pop.getProperty("other.msg70"),
					format12Center);
			sheet.addCell(labelT10);
			// other.msg71=擔保品
			Label labelT11 = new Label(11, 1, pop.getProperty("other.msg71"),
					format12Center);
			sheet.addCell(labelT11);
			// other.msg72=利率
			Label labelT12 = new Label(12, 1, pop.getProperty("other.msg72"),
					format12Center);
			sheet.addCell(labelT12);
			// other.msg73=其他敘做條件
			Label labelT13 = new Label(13, 1, pop.getProperty("other.msg73"),
					format12Center);
			sheet.addCell(labelT13);
			// other.msg74=備註
			Label labelT14 = new Label(14, 1, pop.getProperty("other.msg74"),
					format12Center);
			sheet.addCell(labelT14);
			// 設定行寬
			sheet.setColumnView(0, 15);
			sheet.setColumnView(1, 15);
			sheet.setColumnView(2, 15);
			sheet.setColumnView(3, 10);
			sheet.setColumnView(4, 15);
			sheet.setColumnView(5, 15);
			sheet.setColumnView(6, 15);
			sheet.setColumnView(7, 10);
			sheet.setColumnView(8, 15);
			sheet.setColumnView(9, 10);
			sheet.setColumnView(10, 10);
			sheet.setColumnView(11, 25);
			sheet.setColumnView(12, 25);
			sheet.setColumnView(13, 25);
			sheet.setColumnView(14, 25);

			for (L120S04A model : list) {
				// 集團合計、關係企業合計就不用顯示於EXCEL
				if (!UtilConstants.Casedoc.L120s04aCustRelation.集團企業合計
						.equals(Util.trim(model.getCustRelation()))
						&& !UtilConstants.Casedoc.L120s04aCustRelation.關係企業合計
								.equals(Util.trim(model.getCustRelation()))) {
					String tCustId = Util.trim(model.getCustId());
					String tDupNo = Util.trim(model.getDupNo());
					String tCustName = Util.trim(model.getCustName());

					// J-113-0069_05097_B1001 Web
					// e-Loan企金授信「主要關係戶與本行授信往來比較表」，借戶及其相關關係企業於本次有簽報者，引進本次簽報內容
					// 判斷該戶本次簽報書有沒有額度明細表

					// List<Map<String, Object>> tempListMap = eloanDBService
					// .selExcel(tCustId, tDupNo);

					List<Map<String, Object>> tempListMap = null;

					List<L140M01A> l140m01as = null;
					l140m01as = l140m01aDao.findByMainIdAndCustId(mainId,
							tCustId, tDupNo,
							UtilConstants.Cntrdoc.ItemType.額度明細表);
					if (l140m01as != null && !l140m01as.isEmpty()) {
						// 如果有，改以本次的額度明細表顯示
						tempListMap = eloanDBService.selExcel_forThisReport(
								tCustId, tDupNo, mainId,
								UtilConstants.Cntrdoc.ItemType.額度明細表);
					} else {
						// 如果沒有，以借款人已核准最新一筆顯示
						tempListMap = eloanDBService.selExcel(tCustId, tDupNo);
					}

					listMap.add(getFullContent(tempListMap, tCustId, tDupNo,
							tCustName, model));
				}
			}

			if (!listMap.isEmpty()) {
				for (int i = 0, k = 2; i < listMap.size(); i++, k++) {
					// 公司名稱
					Label labelCustName = new Label(0, k, listMap.get(i)
							.get("custName").toString(), format12Left);
					sheet.addCell(labelCustName);
					// 分行
					Label labelOwnBrId = new Label(1, k, listMap.get(i)
							.get("ownBrId").toString(), format12Left);
					sheet.addCell(labelOwnBrId);
					// 信用評等
					Label labelGrade = new Label(2, k, listMap.get(i)
							.get("grade").toString(), format12Left);
					sheet.addCell(labelGrade);
					// 關係
					Label labelCustRelation = new Label(3, k, listMap.get(i)
							.get("custRelation").toString(), format12Left);
					sheet.addCell(labelCustRelation);
					// 核准日期
					Label labelApproveTime = new Label(4, k, listMap.get(i)
							.get("approveTime").toString(), format12Center);
					sheet.addCell(labelApproveTime);
					// 額度序號
					Label labelCntrNo = new Label(5, k, listMap.get(i)
							.get("cntrNo").toString(), format12Left);
					sheet.addCell(labelCntrNo);
					// 額度(仟元)
					Label labelCurrentApplyAmt = new Label(6, k, listMap.get(i)
							.get("currentApplyAmt").toString(), format12Right);
					sheet.addCell(labelCurrentApplyAmt);
					// 性質
					Label labelProperty = new Label(7, k, listMap.get(i)
							.get("property").toString(), format12Left);
					sheet.addCell(labelProperty);
					// 科目
					Label labelLnSubject = new Label(8, k, listMap.get(i)
							.get("lnSubject").toString(), format12Left);
					sheet.addCell(labelLnSubject);
					// 清償期限
					Label labelPayDeadline = new Label(9, k, listMap.get(i)
							.get("payDeadline").toString(), format12Left);
					sheet.addCell(labelPayDeadline);
					// 保證人
					Label labelGuarantor = new Label(10, k, listMap.get(i)
							.get("guarantor").toString(), format12Left);
					sheet.addCell(labelGuarantor);
					// 擔保品
					Label labelDanbowDscr = new Label(11, k, listMap.get(i)
							.get("danbowDscr").toString(), format12Left);
					sheet.addCell(labelDanbowDscr);
					// 利率
					Label labelRateDscr = new Label(12, k, listMap.get(i)
							.get("rateDscr").toString(), format12Left);
					sheet.addCell(labelRateDscr);
					// 其他敘做條件
					Label labelOtherDscr = new Label(13, k, listMap.get(i)
							.get("otherDscr").toString(), format12Left);
					sheet.addCell(labelOtherDscr);
					// 備註
					Label labelMemoDscr = new Label(14, k, listMap.get(i)
							.get("memoDscr").toString(), format12Left);
					sheet.addCell(labelMemoDscr);
				}
			}

			book.write();
			book.close();
			// return baos.toByteArray();
			// return null;
		} catch (Exception ex) {
			LOGGER.error("[getContent] Exception!!", ex.getMessage());
		} finally {
			if (baos != null) {
				try {
					baos.close();
				} catch (IOException ex) {
					LOGGER.error("[getContent] Exception!!", ex.getMessage());
				}
			}
		}
	}

	/**
	 * 
	 * 基本功能：過濾所有以"<"開頭以">"結尾的標籤
	 * <p>
	 * 
	 * @param str
	 *            String
	 * @return String
	 */
	public static String filterHtml(String str) {
		Pattern pattern = Pattern.compile(regxpForHtml);
		Matcher matcher = pattern.matcher(str);
		StringBuffer sb = new StringBuffer();
		boolean result1 = matcher.find();
		while (result1) {
			matcher.appendReplacement(sb, "");
			result1 = matcher.find();
		}
		matcher.appendTail(sb);
		return sb.toString();
	}

	/**
	 * 取得完整資料(Excel所要塞的內容)
	 * 
	 * @param tempListMap
	 * @param custId
	 *            往來彙總明細統編
	 * @param dupNo
	 *            往來彙總明細重覆序號
	 * @param custName
	 *            公司名稱
	 * @return
	 */
	private Map<String, Object> getFullContent(
			List<Map<String, Object>> tempListMap, String custId, String dupNo,
			String custName, L120S04A model) {
		Map<String, String> proPertyMap = codetypeService.findByCodeType(
				"lms1405s02_proPerty", "zh_TW");
		Map<String, Object> map = new HashMap<String, Object>();
		/*
		 * 信用評等(用L120M01A.docType判斷企金/個金) 企金：L120S01C.crdType + L120S01C.grade
		 * 個金：C120S01G.grade3
		 */
		String grade = null;
		// 關係(L120S04A.custRelation)
		String custRelation = null;
		// 核准日期(L120M01A.approveTime)
		String approveTime = null;
		// 編製單位(L140M01A.ownBrId)
		String ownBrId = null;
		// 額度序號(L140M01A.cntrNo)
		String cntrNo = null;
		// 額度(仟元)(L140M01A.currentApplyCurr + L140M01A.currentApplyAmt)
		String currentApplyAmt = null;
		// 性質(L140M01A.property)
		String property = null;
		// 科目(L140M01A.lnSubject)
		String lnSubject = null;
		// 清償期限(L140M01A.payDeadline)
		String payDeadline = null;
		// 保證人(L140M01A.guarantorType=="2" + L140M01A.guarantor)
		String guarantor = null;
		// 擔保品(L140M01B.itemType=="3" + L140M01B.itemDscr)
		String danbowDscr = null;
		// 利率(L140M01B.itemType=="2" + L140M01B.itemDscr)
		String rateDscr = null;
		// 其他敘作條件(L140M01B.itemType=="4" + L140M01B.itemDscr)
		String otherDscr = null;
		// 備註
		String memoDscr = null;
		// 額度明細表文件編號
		String cntrMainId = null;
		// 簽報書文件編號
		String mainId = null;

		StringBuilder sb = new StringBuilder();
		sb.setLength(0);
		for (Map<String, Object> tempMap : tempListMap) {
			// 編製單位
			ownBrId = Util.trim(tempMap.get("OWNBRID"));
			// 額度序號
			cntrNo = Util.trim(tempMap.get("CNTRNO"));
			// 額度明細表文件編號
			cntrMainId = Util.trim(tempMap.get("CNTRMAINID"));
			sb.append(Util.trim(tempMap.get("CURRENTAPPLYCURR")))
					.append(NumConverter.addComma((LMSUtil.toBigDecimal(Util
							.trim(tempMap.get("currentApplyAmt"))) == null) ? LMSUtil
							.toBigDecimal(Util.trim(tempMap
									.get("currentApplyAmt"))) : LMSUtil
							.toBigDecimal(
									Util.trim(tempMap.get("currentApplyAmt")))
							.setScale(0)));
			// 額度(仟元)
			currentApplyAmt = sb.toString();
			// 性質
			property = this.getProPerty(Util.trim(tempMap.get("PROPERTY")),
					proPertyMap);
			// 科目
			lnSubject = Util.trim(tempMap.get("LNSUBJECT"));
			// 清償期限
			payDeadline = Util.trim(tempMap.get("PAYDEADLINE"));
			// 核准日期
			approveTime = TWNDate.toTW(
					CapDate.getDate(Util.trim(tempMap.get("APPROVETIME")),
							UtilConstants.DateFormat.YYYY_MM_DD)).replace("/",
					".");
			break;
		}
		L140M01A cntrModel = service1405.findL140m01aByMainId(cntrMainId);
		L140M01B danBowModel = service1405.findL140m01bUniqueKey(cntrMainId,
				UtilConstants.Cntrdoc.l140m01bItemType.擔保品);
		L140M01B rateModel = service1405.findL140m01bUniqueKey(cntrMainId,
				UtilConstants.Cntrdoc.l140m01bItemType.利費率);
		L140M01B otherModel = service1405.findL140m01bUniqueKey(cntrMainId,
				UtilConstants.Cntrdoc.l140m01bItemType.其他敘做條件);
		if (cntrModel != null) {
			L120M01C relateModel = cntrModel.getL120m01c();
			if ("2".equals(Util.trim(cntrModel.getGuarantorType()))) {
				// 保證人內容
				guarantor = Util.trim(cntrModel.getGuarantor());
			} else if ("1".equals(Util.trim(cntrModel.getGuarantorType()))) {
				// 連保人內容
				guarantor = Util.trim(cntrModel.getGuarantor());
			}
			if (relateModel != null) {
				// 簽報書文件編號
				mainId = Util.trim(relateModel.getMainId());
			}
		}
		if (danBowModel != null) {
			// 擔保品內容
			danbowDscr = filterHtml(Util.trim(danBowModel.getItemDscr()))
					.replace("&nbsp;", UtilConstants.Mark.SPACE);
		}
		if (rateModel != null) {
			// 利費率內容
			rateDscr = filterHtml(Util.trim(rateModel.getItemDscr())).replace(
					"&nbsp;", UtilConstants.Mark.SPACE);
		}
		if (otherModel != null) {
			// 其他敘做條件內容
			otherDscr = filterHtml(Util.trim(otherModel.getItemDscr()))
					.replace("&nbsp;", UtilConstants.Mark.SPACE);
		}
		// L120S04A custRelModel = service1201.findL120s04aByUniqueKey(mainId,
		// custId, dupNo, custName);
		if (model != null) {
			Properties pop = MessageBundleScriptCreator
					.getComponentResource(LMSS07APanel.class);
			StringBuilder relSb = new StringBuilder();
			relSb.setLength(0);
			String[] strs = Util.trim(model.getCustRelation()).split(",");
			// 對陣列進行排序
			Arrays.sort(strs);
			for (String s : strs) {
				if (relSb.length() > 0)
					relSb.append("/");
				relSb.append(Util.trim(pop.getProperty("L1205S07.checkbox" + s)));
			}
			// 關係
			custRelation = relSb.toString();
		}
		L120M01A meta = service1201.findL120m01aByMainId(mainId);
		if (meta != null) {
			String docType = Util.trim(meta.getDocType());
			if (UtilConstants.Casedoc.DocType.企金.equals(docType)) {
				// 企金信評設定
				StringBuilder trustSb = new StringBuilder();
				trustSb.setLength(0);
				List<L120S01C> busList = service1201.findL120s01cByCustId(
						mainId, custId, dupNo);
				// for (L120S01C busModel : busList) {
				// String crdType = Util.trim(busModel.getCrdType());
				// trustSb.append(
				// (trustSb.length() > 0) ? "\n"
				// : UtilConstants.Mark.SPACE)
				// .append(getTrustName(crdType))
				// .append(("NA".equals(crdType) || "NO"
				// .equals(crdType)) ? UtilConstants.Mark.SPACE
				// : "：")
				// .append(Util.trim(busModel.getGrade()));
				// }
				// grade = trustSb.toString();
				grade = getL120S01CData(busList, "1");
			} else if (UtilConstants.Casedoc.DocType.個金.equals(docType)) {
				// 個金信評設定
				C120S01G perModel = c120s01gDao.findByUniqueKey(mainId,
						Util.trim(meta.getOwnBrId()), custId, dupNo);
				if (perModel != null) {
					grade = Util.trim(perModel.getGrade3());
				}

			}
		}

		// 開始設定值
		map.put("ownBrId", Util.trim(ownBrId));
		map.put("cntrNo", Util.trim(cntrNo));
		map.put("custName", Util.trim(custName));
		map.put("grade", Util.trim(grade));
		map.put("custRelation", Util.trim(custRelation));
		map.put("approveTime", Util.trim(approveTime));
		map.put("currentApplyAmt", Util.trim(currentApplyAmt));
		map.put("property", Util.trim(property));
		map.put("lnSubject", Util.trim(lnSubject));
		map.put("payDeadline", Util.trim(payDeadline));
		map.put("guarantor", Util.trim(guarantor));
		map.put("danbowDscr", Util.trim(danbowDscr));
		map.put("rateDscr", Util.trim(rateDscr));
		map.put("otherDscr", Util.trim(otherDscr));
		map.put("memoDscr", Util.trim(memoDscr));

		return map;
	}

	/**
	 * 取得信評資料
	 * 
	 * @param l120s01a
	 *            L120S01A的資料
	 * @param l120s01cList
	 *            LIST<L120S01C>的資料
	 * @return 信評資料
	 */
	@SuppressWarnings("unused")
	private String getL120S01CData(List<L120S01C> l120s01cList, String type) {
		String resultData = "";
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1205R01RptServiceImpl.class);
		Map<String, String> crdTypeMap = codetypeService.findByCodeType(
				"CRDType", LMSUtil.getLocale().toString());
		StringBuffer str1 = new StringBuffer();
		StringBuffer str2 = new StringBuffer();
		StringBuffer str3 = new StringBuffer();
		StringBuffer str4 = new StringBuffer();
		// 免辦
		boolean noResult = false;
		boolean naResult = false;
		StringBuffer tempGrade = new StringBuffer();
		for (L120S01C l120s01c : l120s01cList) {
			// if (Util.nullToSpace(l120s01a.getCustId()).equals(
			// Util.nullToSpace(l120s01c.getCustId()))
			// && Util.nullToSpace(l120s01a.getDupNo()).equals(
			// Util.nullToSpace(l120s01c.getDupNo()))) {
			String crdType = Util.trim(l120s01c.getCrdType());
			String grade = Util.trim(l120s01c.getGrade());
			tempGrade.setLength(0);
			if ("NA".equals(crdType)) {
				naResult = true;
				// str.append(prop.getProperty("L120S01C.CRDTITLE01"))
				// .append(prop.getProperty("L120S05A.GRPGRRDN"))
				// .append("、");
			} else if ("DB".equals(crdType) || "DL".equals(crdType)
					|| "OU".equals(crdType)) {
				if (str3.length() != 0) {
					str3.append("、");
				}

				if (Util.notEquals(grade, "NA")) {

					str3.append(prop.getProperty("L120S01C.CRDTITLE01"))
							.append(grade)
							.append("【")
							.append(prop.getProperty("L120S01C.CRDTITLE02"))
							.append(Util.nullToSpace(TWNDate.toAD(l120s01c
									.getCrdTYear())))
							.append(" ")
							.append(prop.getProperty("L120S01C.CRDTITLE03"))
							.append(Util.nullToSpace(branch.getBranchName(Util
									.nullToSpace(l120s01c.getCrdTBR()))))
							.append("】");
				}
			} else if ("NO".equals(crdType)) {
				noResult = true;
				// str.append(prop.getProperty("L120S01C.CRDTITLE04"))
				// .append(prop.getProperty("L120S01C.NOCRD01"))
				// .append("、");
			} else if ("M".equals(Util.getLeftStr(crdType, 1))) {

				if (Util.isNumeric(grade)) {
					tempGrade.append(grade)
							.append(prop.getProperty("tempGrade")).append(" ");
				}

				// 取得MOW等級之說明
				tempGrade.append(lmsService.getMowGradeName(prop, crdType,
						grade));

				if (str2.length() != 0) {
					str2.append("、");
				}
				str2.append(Util.nullToSpace(crdTypeMap.get(crdType)))
						.append(" : ")
						.append(tempGrade.toString())
						.append("【")
						.append(prop.getProperty("L120S01C.CRDTITLE02"))
						.append(Util.nullToSpace(TWNDate.toAD(l120s01c
								.getCrdTYear())))
						.append(" ")
						.append(prop.getProperty("L120S01C.CRDTITLE03"))
						.append(Util.nullToSpace(branch.getBranchName(Util
								.nullToSpace(l120s01c.getCrdTBR()))))
						.append("】");
			} else if (UtilConstants.Casedoc.CrdType.MOODY.equals(crdType)
					|| UtilConstants.Casedoc.CrdType.SAndP.equals(crdType)
					|| UtilConstants.Casedoc.CrdType.Fitch.equals(crdType)
					|| UtilConstants.Casedoc.CrdType.FitchTW.equals(crdType)
					|| UtilConstants.Casedoc.CrdType.KBRA.equals(crdType)) {
				// J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等
				if (str1.length() != 0) {
					str1.append("、");
				}
				str1.append(grade)
						.append("【")
						.append(prop.getProperty("L120S01C.CRDTITLE02"))
						.append(Util.nullToSpace(TWNDate.toAD(l120s01c
								.getCrdTYear())))
						.append(" ")
						.append(prop.getProperty("L120S01C.CRDTITLE03"))
						.append(Util.nullToSpace(crdTypeMap.get(l120s01c
								.getCrdType()))).append("】");
			} else if (crdType.startsWith("C") && Util.notEquals(crdType, "CS")) {
				if (str4.length() != 0) {
					str4.append("、");
				}
				str4.append(Util.nullToSpace(crdTypeMap.get(crdType)))
						.append(" : ")
						.append(grade)
						.append("【")
						.append(prop.getProperty("L120S01C.CRDTITLE02"))
						.append(Util.nullToSpace(TWNDate.toAD(l120s01c
								.getCrdTYear())))
						.append(" ")
						.append(prop.getProperty("L120S01C.CRDTITLE03"))
						.append(" ")
						.append(l120s01c.getCrdTBR())
						.append(" ")
						.append(Util.nullToSpace(branch.getBranchName(Util
								.nullToSpace(l120s01c.getCrdTBR()))))
						.append("】");
			}
			// }
		}

		/*
		 * 狀況1:MX+NA 狀況2:DX+NO 狀況3:NA+NO 狀況4:空 最後在加外部NM,NS,NP
		 */
		// 外部平等一定要串
		boolean result = false;
		StringBuffer total = new StringBuffer();
		// L120S01C.CRDTITLE04=模型評等 :
		if (str2.length() > 0) {

			// MXXX+外部
			// rptVariableMap.put("L120S01C.CRD",str2.toString());
			if (Util.equals(type, "2")) {
				total.append(str2);
			} else {
				total.append(prop.getProperty("L120S01C.CRDTITLE04") + " " + str2);
			}
			result = true;
		}
		// L120S01C.CRDTITLE01=信用評等 :
		if (str3.length() > 0) {
			// DXXX+外部
			total.append(total.length() > 0 ? "<br/>" : "");
			total.append(str3.toString());
			// rptVariableMap.put("L120S01C.CRD",str3.toString() + " " +
			// prop.getProperty("L120S01C.CRDTITLE04"));
			result = true;
		}

		// L120S01C.CRDTITLE05=外部評等 :
		if (str1.length() > 0) {
			total.append(total.length() > 0 ? "<br/>" : "");
			total.append(prop.getProperty("L120S01C.CRDTITLE05")
					+ str1.toString());
		}
		if (total.length() == 0) {
			// rptVariableMap.put("L120S01C.CRD",prop.getProperty("L120S01C.NOCRD01"));
			if (Util.equals(type, "2")) {
				if (noResult || naResult) {
					total.append(prop.getProperty("L120S01C.NOCRD01"));
				}
			} else {
				total.append(prop.getProperty("L120S01C.NOCRD01"));
			}
			result = true;
		}
		// rptVariableMap.put("L120S01C.CRD",(!result ? "" :
		// (rptVariableMap.get("L120S01C.CRD") + "\n"))+crdtitle05 +
		// str1.toString());
		resultData = total.toString();
		return resultData;
	}

	// /**
	// * 取得信評名稱
	// *
	// * @param crdType
	// * 信評類型
	// * @return 信評名稱
	// */
	// private String getTrustName(String crdType) {
	// Properties pop = MessageBundleScriptCreator
	// .getComponentResource(LMSCommomPage.class);
	// if (UtilConstants.Casedoc.CrdType.泰國GroupA.equals(crdType)) {
	// // other.msg75=泰國GroupA
	// return pop.getProperty("other.msg75");
	// } else if (UtilConstants.Casedoc.CrdType.泰國GroupB.equals(crdType)) {
	// // other.msg76=泰國GroupB
	// return pop.getProperty("other.msg76");
	// } else if (UtilConstants.Casedoc.CrdType.自訂.equals(crdType)) {
	// // other.msg77=自訂
	// return pop.getProperty("other.msg77");
	// } else if (UtilConstants.Casedoc.CrdType.消金評等.equals(crdType)) {
	// // other.msg78=消金評等
	// return pop.getProperty("other.msg78");
	// } else if (UtilConstants.Casedoc.CrdType.MOODY.equals(crdType)) {
	// return "MOODY";
	// } else if (UtilConstants.Casedoc.CrdType.SAndP.equals(crdType)) {
	// return "S&P";
	// } else if (UtilConstants.Casedoc.CrdType.Fitch.equals(crdType)) {
	// return "Fitch";
	// } else if (UtilConstants.Casedoc.CrdType.中華信評.equals(crdType)) {
	// // other.msg79=中華信評
	// return pop.getProperty("other.msg79");
	// } else if ("NO".equals(crdType)) {
	// // other.msg80=免辦
	// return pop.getProperty("other.msg80");
	// } else {
	// return codetypeService.getDescOfCodeType("lms1705s01_crdType",
	// crdType);
	// }
	// }

	/**
	 * 取得property對應
	 * 
	 * @param proPerty
	 *            property
	 * @return property對應
	 */
	private String getProPerty(String proPerty, Map<String, String> proPertyMap) {
		StringBuffer str = new StringBuffer();
		String[] temp = proPerty.split("\\|");
		for (String perty : temp) {
			str.append(Util.nullToSpace(proPertyMap.get(perty))).append("、");
		}
		if (str.length() == 0) {
			str.append("、");
		}

		return str.toString().substring(0, str.length() - 1);
	}

	/**
	 * J-107-0225_05097_B1001 Web e-Loan企金授信簽報書新增集團關係企業與本行授信往來條件比較表
	 * 產生集團／關係企業與本行授信往來條件比較表
	 * 
	 * @param params
	 */
	public void creExcel2(PageParameters params) throws CapMessageException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);
		// J-109-0370 相關評估改版
		String oidL120s04d = Util
				.trim(params.getString(EloanConstants.MAIN_ID));// oidL120s04d
		L120S04D l120s04d = lmsService.findL120s04dByOid(oidL120s04d);
		String mainId = l120s04d.getMainId();
		ByteArrayOutputStream baos = null;
		List<Map<String, Object>> listMap = new ArrayList<Map<String, Object>>();
		WritableFont font12 = null;
		WritableCellFormat format12Center = null;
		WritableCellFormat format12Left = null;
		WritableCellFormat format12Right = null;
		WritableCellFormat format12LeftNO = null;
		WritableCellFormat format12RightNO = null;

		// J-108-0299_05097_B1001 Web
		// e-Loan企金授信增加並調整e-Loan「集團關係企業與本行授信往來條件比較表」欄位
		WritableCellFormat format12LeftNoBorder = null;

		// List<L120S04A> tempNew = service1201.findL120s04aByMainId(mainId);
		// List<L120S04A> list = new ArrayList<L120S04A>(tempNew);

		// 有集團以集團為主，若沒集團才以關係企業為主

		// ***********************************************************
		// 這邊LMS1201XLSServiceImpl、LMS1205XLSServiceImpl、LMS9535XLSServiceImpl不一樣
		// **********************************************************
		String mCustId = "";
		String mDupNo = "";
		String mCustName = "";
		String mBrNo = "";

		// J-113-0069_05097_B1001 Web
		// e-Loan企金授信「主要關係戶與本行授信往來比較表」，借戶及其相關關係企業於本次有簽報者，引進本次簽報內容
		L120M01A l120m01a = null;
		if (true) {
			l120m01a = service1201.findL120m01aByMainId(mainId);
			if (l120m01a != null) {
				mCustId = Util.trim(l120m01a.getCustId());
				mDupNo = Util.trim(l120m01a.getDupNo());
				mCustName = Util.trim(l120m01a.getCustName());
				mBrNo = Util.trim(l120m01a.getCaseBrId());
			} else {
				throw new CapMessageException("無主借款人資料，產生失敗！！", getClass());
			}
		}
		if (l120s04d != null) {
			mCustId = Util.trim(l120s04d.getKeyCustId());
			mDupNo = Util.trim(l120s04d.getKeyDupNo());
			// mCustName = Util.trim(l120s04d.getCustName());
			L120S01A l120s01a = service1201.findL120s01aByUniqueKey(mainId,
					mCustId, mDupNo);
			if (l120s01a != null) {
				mCustName = Util.trim(l120s01a.getCustName());
			}
		}

		// J-113-0069_05097_B1001 Web
		// e-Loan企金授信「主要關係戶與本行授信往來比較表」，借戶及其相關關係企業於本次有簽報者，引進本次簽報內容
		BranchRate branchRate = lmsService
				.getBranchRate(l120m01a.getCaseBrId());

		// ***********************************************************
		// 這邊LMS1201XLSServiceImpl、LMS1205XLSServiceImpl、LMS9535XLSServiceImpl不一樣
		// **********************************************************

		List<Map<String, Object>> listGrpMap = misGrpcmpService
				.findGrpcmpSelGrpdtl(mCustId, mDupNo);
		String tGPID = null;
		String tGPName = null;
		// J-108-0299_05097_B1001 Web
		// e-Loan企金授信增加並調整e-Loan「集團關係企業與本行授信往來條件比較表」欄位
		String BRANCH = null;
		String GRPYY = null;
		String GRPGRADE = null;
		String GRPSIZE = null;
		String GRPLEVEL = null;
		boolean findGroupProfitSum = false;
		BigDecimal profit = BigDecimal.ZERO;
		String dataBgnDateYYYYMM = "";
		String dataEndDateYYYYMM = "";

		for (Map<String, Object> map : listGrpMap) {
			// 隸屬集團代號
			tGPID = Util.trim(map.get("GRPID"));
			// 隸屬集團名稱
			tGPName = Util.trim(map.get("GRPNM"));

			// J-108-0299_05097_B1001 Web
			// e-Loan企金授信增加並調整e-Loan「集團關係企業與本行授信往來條件比較表」欄位
			BRANCH = Util.trim(map.get("BRANCH"));
			GRPYY = Util.trim(map.get("GRPYY"));
			GRPGRADE = Util.trim(map.get("GRPGRADE"));
			GRPSIZE = Util.trim(map.get("GRPSIZE"));
			GRPLEVEL = Util.trim(map.get("GRPLEVEL"));

			// 貢獻度
			// 經洽提案人企金處(07083)陳嫈潔 ，要與二維表一致

			// List<L120S04A> l120s04aList = service1201
			// .findL120s04aByMainId(mainId);
			List<L120S04A> l120s04aList = lmsService
					.findL120s04aByMainIdKeyCustIdDupNo(mainId, mCustId, mDupNo);
			if (l120s04aList != null && !l120s04aList.isEmpty()) {
				for (L120S04A l120s04a : l120s04aList) {
					if (LMSUtil.isContainValue(
							Util.trim(l120s04a.getCustRelation()), "3")) {
						// 集團合計
						findGroupProfitSum = true;
						profit = l120s04a.getProfit() == null ? BigDecimal.ZERO
								: Util.parseBigDecimal(l120s04a.getProfit());

						dataBgnDateYYYYMM = CapDate.formatDate(
								l120s04a.getQueryDateS(), "yyyy")
								+ "年"
								+ CapDate.formatDate(l120s04a.getQueryDateS(),
										"MM") + "月";
						dataEndDateYYYYMM = CapDate.formatDate(
								l120s04a.getQueryDateE(), "yyyy")
								+ "年"
								+ CapDate.formatDate(l120s04a.getQueryDateE(),
										"MM") + "月";
					}

				}
			}

			if (findGroupProfitSum == false) {
				throw new CapMessageException(
						"請先執行關係戶於本行各項業務往來彙總「計算集團/關係企業合計」功能後再執行本作業！！",
						getClass());
			}

			// *******************************************************************

			break;
		}

		List<Map<String, Object>> dbDataList = null;
		if (Util.notEquals(tGPID, "")) {
			// 集團企業
			dbDataList = misDbService.findLnf022ByGrpId(tGPID);
		} else {
			// 關係企業
			dbDataList = misDbService.findLnf022ByByElcrecomCustId(mCustId,
					mDupNo);
		}

		// String brno = null;
		// String custId = null;
		// String dupNo = null;
		// String custName = null;
		// String caseNo = null;

		// ***********************************************************
		// 這邊LMS1201XLSServiceImpl、LMS1205XLSServiceImpl、LMS9535XLSServiceImpl不一樣
		// **********************************************************
		// listName="LMSNoList"
		// String ranMainId = IDGenerator.getRandomCode();
		DocFile docFile = new DocFile();
		docFile.setBranchId(mBrNo);
		docFile.setContentType("application/msexcel");
		docFile.setMainId(oidL120s04d);
		docFile.setCrYear(CapDate.getCurrentDate("yyyy"));
		docFile.setFieldId("LMSNoList");
		// other.msg61=借戶暨關係戶與本行授信往來比較表
		// LMSNoList.xls請顯示為 主要關係戶與本行授信往來比較表.xls
		docFile.setSrcFileName("LMS1205R24A_B.xls");
		docFile.setUploadTime(CapDate.getCurrentTimestamp());
		docFile.setSysId(fileService.getSysId());
		docFile.setData(new byte[] {});
		fileService.save(docFile, false);
		File file = null;

		String filename = null;
		String xlsOid = null;
		// ***********************************************************
		// 這邊LMS1201XLSServiceImpl、LMS1205XLSServiceImpl、LMS9535XLSServiceImpl不一樣
		// **********************************************************
		xlsOid = docFile.getOid();
		filename = LMSUtil.getUploadFilePath(docFile.getBranchId(),
				oidL120s04d, "LMSNoList");
		file = new File(filename);
		file.mkdirs();

		try {
			file = new File(filename + "/" + xlsOid + ".xls");

			// baos = new ByteArrayOutputStream();
			// WritableWorkbook book = Workbook.createWorkbook(baos);
			WritableWorkbook book = Workbook.createWorkbook(file);
			WritableSheet sheet = book.createSheet("1", 0);

			/*
			 * 1.1方向 SheetSetting#setOrientation(PageOrientation po)； 參數：
			 * PageOrientation#LANDSCAPE 橫向打印 PageOrientation# PORTRAIT 縱向打印 (A)
			 * SheetSetting #setScaleFactor (int);百分比形式
			 */
			SheetSettings settings = sheet.getSettings();
			settings.setOrientation(PageOrientation.LANDSCAPE);
			// EXCEL 請設定預設為橫印、預設為1頁寬
			// // 縮放比例
			// settings.setScaleFactor(100);
			// 縮放比例頁寬
			settings.setFitWidth(1);
			// 縮放比例頁高
			settings.setFitHeight(5000);
			// 頁面距(英吋)
			// 上
			settings.setTopMargin((double) 0.394);
			// 下
			settings.setBottomMargin((double) 0.394);
			// 左
			settings.setLeftMargin((double) 0.197);
			// 右
			settings.setRightMargin((double) 0.197);
			// 頁首
			settings.setHeaderMargin((double) 0.5);
			// 頁尾
			settings.setFooterMargin((double) 0.5);

			// 設定字型與格式
			// other.msg60=新細明體
			font12 = new WritableFont(WritableFont.createFont(pop
					.getProperty("other.msg60")), 12, WritableFont.NO_BOLD);
			// 將某欄位強制轉成文字欄位顯示
			WritableCellFormat contentFromart = new WritableCellFormat(
					NumberFormats.TEXT);
			format12Center = LMSUtil.setCellFormat(format12Center, font12,
					Alignment.CENTRE);
			format12Left = LMSUtil.setCellFormat(contentFromart, font12,
					Alignment.LEFT);
			format12Right = LMSUtil.setCellFormat(format12Right, font12,
					Alignment.RIGHT);
			format12LeftNO = LMSUtil.setCellFormat(format12LeftNO, font12,
					Alignment.LEFT, false, false);
			format12RightNO = LMSUtil.setCellFormat(format12RightNO, font12,
					Alignment.RIGHT, false, false);

			// J-108-0299_05097_B1001 Web
			// e-Loan企金授信增加並調整e-Loan「集團關係企業與本行授信往來條件比較表」欄位
			format12LeftNoBorder = LMSUtil.setCellFormat(format12LeftNoBorder,
					font12, Alignment.LEFT);
			format12LeftNoBorder.setBorder(Border.ALL, BorderLineStyle.NONE);

			int y = 0;

			if (Util.notEquals(tGPID, "")) {
				// 屬集團企業才要印

				sheet.setRowView(y, 350); // 設定行高
				sheet.mergeCells(0, y, 9, y);
				StringBuilder sb = new StringBuilder();
				sb.append(Util.trim(tGPID))
						.append(" ")
						.append(Util.trim(tGPName))
						.append(" ")
						.append("維護窗口行：")
						.append(BRANCH)
						.append(Util.nullToSpace(branch.getBranchName(Util
								.nullToSpace(BRANCH))));
				Label labelT1 = new Label(0, y, sb.toString(),
						format12LeftNoBorder);
				sheet.addCell(labelT1);

				y = y + 1;
				sheet.setRowView(y, 350); // 設定行高
				sheet.mergeCells(0, y, 9, y);
				sb = new StringBuilder();

				Properties prop = MessageBundleScriptCreator
						.getComponentResource(LMS1201R01RptServiceImpl.class);
				String grpYYYY = Util.equals(Util.trim(GRPYY), "") ? "" : Util
						.trim(Util.parseInt(Util.trim(GRPYY)) + 1911);
				String showGrpGrade = showGrpGrrd(GRPGRADE, grpYYYY, GRPSIZE,
						GRPLEVEL, prop);

				// sb.append("集團評等：")
				// .append(Util.trim(GRPGRADE))
				// .append("級")
				// .append(" ")
				// .append(lmsService.getGrpSizeLvlShow(
				// Util.trim(GRPSIZE), Util.trim(GRPLEVEL)))
				// .append(" 集團評等年度： ")
				// .append(Util.equals(Util.trim(GRPYY), "") ? "" : Util
				// .trim(Util.parseInt(Util.trim(GRPYY)) + 1911));

				sb.append("集團評等：").append(Util.trim(showGrpGrade)).append(" ")
						.append(" 集團評等年度： ").append(grpYYYY);

				labelT1 = new Label(0, y, sb.toString(), format12LeftNoBorder);
				sheet.addCell(labelT1);

				y = y + 1;
				sheet.setRowView(y, 350); // 設定行高
				sheet.mergeCells(0, y, 9, y);
				sb = new StringBuilder();

				NumberFormat n = new DecimalFormat(
						"###,###,###,###,###,###,##0");
				String profitStr = n.format(profit);

				sb.append("集團").append(Util.trim(dataBgnDateYYYYMM))
						.append("～").append(Util.trim(dataEndDateYYYYMM))
						.append("貢獻度：TWD").append(profitStr).append("仟元");
				labelT1 = new Label(0, y, sb.toString(), format12LeftNoBorder);
				sheet.addCell(labelT1);

			}

			// J-108-0299_05097_B1001 Web
			// e-Loan企金授信增加並調整e-Loan「集團關係企業與本行授信往來條件比較表」欄位
			y = y + 1;
			sheet.mergeCells(0, y, 9, y);

			// J-108-0299_05097_B1001 Web
			// e-Loan企金授信增加並調整e-Loan「集團關係企業與本行授信往來條件比較表」欄位
			StringBuilder sb = new StringBuilder();
			// other.msg61=借戶暨關係戶與本行授信往來比較表
			sb.append(Util.trim(mCustId)).append(" ")
					.append(Util.trim(mCustName)).append("集團／關係企業與本行授信往來條件比較表")
					.append(" ").append(TWNDate.toTW(new Date()));
			Label labelT1 = new Label(0, y, sb.toString(), format12Left);
			sheet.addCell(labelT1);

			y = y + 1;
			// other.msg62=公司名稱
			Label labelT2 = new Label(0, y, pop.getProperty("other.msg62"),
					format12Center);
			sheet.addCell(labelT2);
			// other.msg136=分行
			// Label labelT15 = new Label(1, 1, pop.getProperty("other.msg136"),
			// format12Center);
			// sheet.addCell(labelT15);
			// J-111-0466 依據企金業務處940F11108-4號傳真函，調整"集團／關係企業與本行授信往來條件比較表"格式
			// other.msg63=信用評等
			Label labelT3 = new Label(1, y, pop.getProperty("other.msg63"),
					format12Center);
			sheet.addCell(labelT3);
			// other.msg67=性質
			Label labelT7 = new Label(2, y, pop.getProperty("other.msg67"),
					format12Center);
			sheet.addCell(labelT7);

			// other.msg65=核准日期
			// Label labelT5 = new Label(3, 1, pop.getProperty("other.msg65"),
			// format12Center);
			// sheet.addCell(labelT5);
			// other.msg137=額度序號
			// Label labelT16 = new Label(1, y, pop.getProperty("other.msg137"),
			// format12Center);
			// sheet.addCell(labelT16);
			// other.msg66=額度（仟元）
			Label labelT6 = new Label(3, y, "額度(仟元)", format12Center);
			sheet.addCell(labelT6);

			// other.msg64=餘額
			Label labelT4 = new Label(4, y, "餘額(仟元)", format12Center);
			sheet.addCell(labelT4);

			// 額度起迄日
			Label labelT5 = new Label(5, y, "授信期間", format12Center);
			sheet.addCell(labelT5);

			// other.msg67=性質
			// Label labelT7 = new Label(7, 1, pop.getProperty("other.msg67"),
			// format12Center);
			// sheet.addCell(labelT7);
			// other.msg68=科目
			// Label labelT8 = new Label(6, y, pop.getProperty("other.msg68"),
			// format12Center);
			// sheet.addCell(labelT8);
			// other.msg69=清償期限
			// Label labelT9 = new Label(9, 1, pop.getProperty("other.msg69"),
			// format12Center);
			// sheet.addCell(labelT9);
			// other.msg70=保證人
			Label labelT10 = new Label(6, y, pop.getProperty("other.msg70"),
					format12Center);
			sheet.addCell(labelT10);
			// other.msg71=擔保品
			Label labelT11 = new Label(7, y, pop.getProperty("other.msg71"),
					format12Center);
			sheet.addCell(labelT11);
			// other.msg72=利率
			Label labelT12 = new Label(8, y, "利(費)率", format12Center);
			sheet.addCell(labelT12);
			// other.msg73=其他敘做條件
			// Label labelT13 = new Label(9, y, pop.getProperty("other.msg73"),
			// format12Center);
			// sheet.addCell(labelT13);
			// other.msg74=備註
			// Label labelT14 = new Label(10, y, pop.getProperty("other.msg74"),
			// format12Center);
			// sheet.addCell(labelT14);
			Label labelT15 = new Label(9, y, "敘做分行", format12Center);
			sheet.addCell(labelT15);
			// 設定行寬
			sheet.setColumnView(0, 15);
			sheet.setColumnView(1, 15);
			sheet.setColumnView(2, 15);
			sheet.setColumnView(3, 15);
			sheet.setColumnView(4, 15);
			sheet.setColumnView(5, 15);
			sheet.setColumnView(6, 15);
			sheet.setColumnView(7, 40);
			sheet.setColumnView(8, 40);
			sheet.setColumnView(9, 15);
			// sheet.setColumnView(10, 10);
			// sheet.setColumnView(11, 25);
			// sheet.setColumnView(12, 25);
			// sheet.setColumnView(13, 25);
			// sheet.setColumnView(14, 25);

			String gFullCustId = "";
			String tFullCustId = "";

			String gCustName = "";
			String tCustName = "";

			BigDecimal factAmtTS = BigDecimal.ZERO;
			BigDecimal factAmtTN = BigDecimal.ZERO;
			BigDecimal factAmtT = BigDecimal.ZERO;
			BigDecimal loanBalTS = BigDecimal.ZERO;
			BigDecimal loanBalTN = BigDecimal.ZERO;
			BigDecimal loanBalT = BigDecimal.ZERO;

			Map<String, Object> dbData = null;

			// J-113-0069_05097_B1001 Web
			// e-Loan企金授信「主要關係戶與本行授信往來比較表」，借戶及其相關關係企業於本次有簽報者，引進本次簽報內容
			BranchRate twBranchRate = lmsService.getBranchRate("005");
			Map<String, String> useEloanLoanTot_custId = new HashMap<String, String>();
			Map<String, String> useEloanLoanTot_cntrNo = new HashMap<String, String>();
			String mainCurr = "TWD";

			for (Map<String, Object> tDbData : dbDataList) {
				dbData = tDbData;
				tFullCustId = Util.trim(MapUtils.getString(dbData, "CUST_ID"));
				tCustName = Util.trim(MapUtils.getString(dbData, "CNAME"));

				if (Util.notEquals(gFullCustId, "")
						&& Util.notEquals(gFullCustId, tFullCustId)) {
					// 該借款人MIS最後一筆後，再塞DW資料、再塞只有ELOAN的額度資料*******************
					String tCustId = Util
							.trim(Util.getLeftStr(gFullCustId, 10));
					String tDupNo = Util.trim(Util.getRightStr(gFullCustId, 1));

					// DW
					List<Map<String, Object>> dwDataList = dwdbService
							.findDW_LNCNTROVSByCustIdForCreExcel2(tCustId,
									tDupNo);
					for (Map<String, Object> dwData : dwDataList) {
						Map<String, Object> l140m01aMap = null;
						String tCntrNo = Util.trim(MapUtils.getString(dwData,
								"LNF022_CONTRACT"));
						if (Util.notEquals(tCntrNo, "")) {

							// J-113-0069_05097_B1001 Web
							// e-Loan企金授信「主要關係戶與本行授信往來比較表」，借戶及其相關關係企業於本次有簽報者，引進本次簽報內容
							// 抓ELOAN 資料
							// ********************************************
							// 如果該額度有在本次簽報書簽案，要用本次簽報書額度明細表資料代替
							// ********************************************

							useEloanLoanTot_cntrNo.put(tCntrNo, tCntrNo);

							// 簽報書有沒有這個借款人，如果有，則
							boolean thisCaseReportHasCust = false;
							List<L140M01A> custL140m01as = l140m01aDao
									.findByMainIdAndCustId(
											mainId,
											tCustId,
											tDupNo,
											UtilConstants.Cntrdoc.ItemType.額度明細表);
							if (custL140m01as != null
									&& !custL140m01as.isEmpty()) {
								thisCaseReportHasCust = true;
								useEloanLoanTot_custId.put(tCustId + "-"
										+ tDupNo, tCustId + "-" + tDupNo);
							}

							// 預設用最本次簽報的額度序號之額度明細表
							boolean useNewEloan = true;
							l140m01aMap = eloanDBService
									.findL140M01A_lastByCustIdAndCntrNo_forThisReport(
											tCustId,
											tDupNo,
											tCntrNo,
											mainId,
											UtilConstants.Cntrdoc.ItemType.額度明細表);

							if (l140m01aMap == null || l140m01aMap.isEmpty()) {
								// 沒有本次簽案，則改抓已核准最後一筆
								useNewEloan = false;
								l140m01aMap = eloanDBService
										.findL140M01A_lastByCustIdAndCntrNo(
												tCustId, tDupNo, tCntrNo);
							}

							listMap.add(getFullContent_ForCreExcel2(dwData,
									l140m01aMap, gCustName, null, useNewEloan));

							// 計算合計*************************************************
							// J-113-0069_05097_B1001 Web
							// e-Loan企金授信「主要關係戶與本行授信往來比較表」，借戶及其相關關係企業於本次有簽報者，引進本次簽報內容
							// 額度---------------------------------------------
							BigDecimal tFactAmtTS = BigDecimal.ZERO;
							BigDecimal tFactAmtTN = BigDecimal.ZERO;
							BigDecimal tFactAmtT = BigDecimal.ZERO;
							if (thisCaseReportHasCust) {
								// 額度部分改抓簽報書的借款人授信額度合計
								// 這邊就不要加總
								// 到最後面在整個簽報書處理
							} else {
								tFactAmtTS = Util.equals(Util.trim(MapUtils
										.getString(dwData, "AMT_TS")), "") ? BigDecimal.ZERO
										: LMSUtil.toBigDecimal(Util
												.trim(MapUtils.getString(
														dwData, "AMT_TS")));
								tFactAmtTN = Util.equals(Util.trim(MapUtils
										.getString(dwData, "AMT_TN")), "") ? BigDecimal.ZERO
										: LMSUtil.toBigDecimal(Util
												.trim(MapUtils.getString(
														dwData, "AMT_TN")));
								tFactAmtT = Util.equals(Util.trim(MapUtils
										.getString(dwData, "AMT_T")), "") ? BigDecimal.ZERO
										: LMSUtil.toBigDecimal(Util
												.trim(MapUtils.getString(
														dwData, "AMT_T")));
							}

							// 餘額---------------------------------------------
							BigDecimal tLoanBalTS = Util.equals(Util
									.trim(MapUtils.getString(dwData,
											"LOANBAL_TS")), "") ? BigDecimal.ZERO
									: LMSUtil.toBigDecimal(Util.trim(MapUtils
											.getString(dwData, "LOANBAL_TS")));
							BigDecimal tLoanBalTN = Util.equals(Util
									.trim(MapUtils.getString(dwData,
											"LOANBAL_TN")), "") ? BigDecimal.ZERO
									: LMSUtil.toBigDecimal(Util.trim(MapUtils
											.getString(dwData, "LOANBAL_TN")));
							BigDecimal tLoanBalT = Util.equals(Util
									.trim(MapUtils.getString(dwData,
											"LOANBAL_T")), "") ? BigDecimal.ZERO
									: LMSUtil.toBigDecimal(Util.trim(MapUtils
											.getString(dwData, "LOANBAL_T")));

							factAmtTS = factAmtTS.add(tFactAmtTS);
							factAmtTN = factAmtTN.add(tFactAmtTN);
							factAmtT = factAmtT.add(tFactAmtT);
							loanBalTS = loanBalTS.add(tLoanBalTS);
							loanBalTN = loanBalTN.add(tLoanBalTN);
							loanBalT = loanBalT.add(tLoanBalT);

						}
					}

					// J-113-0069_05097_B1001 Web
					// e-Loan企金授信「主要關係戶與本行授信往來比較表」，借戶及其相關關係企業於本次有簽報者，引進本次簽報內容
					// 補純ELOAN額度
					// BGN******************************************************
					// 只有出現在本次簽案的額度名序號(沒有出現再ALOAN與DW)，且額度性質非'7','8','13'

					List<L140M01A> custL140m01as = l140m01aDao
							.findByMainIdAndCustId(mainId, tCustId, tDupNo,
									UtilConstants.Cntrdoc.ItemType.額度明細表);
					if (custL140m01as != null && !custL140m01as.isEmpty()) {
						for (L140M01A custL140m01a : custL140m01as) {
							String xCntrNo = Util
									.trim(custL140m01a.getCntrNo());
							String xCustId = Util
									.trim(custL140m01a.getCustId());
							String xDupNo = Util.trim(custL140m01a.getDupNo());
							if (Util.notEquals(xCntrNo, "")) {
								if (!useEloanLoanTot_cntrNo
										.containsKey(xCntrNo)) {
									if (LMSUtil.isContainValue(Util
											.trim(custL140m01a.getProPerty()),
											UtilConstants.Cntrdoc.Property.取消)
											|| LMSUtil
													.isContainValue(
															Util.trim(custL140m01a
																	.getProPerty()),
															UtilConstants.Cntrdoc.Property.報價)) {
										// ELOAN額度序號沒有出現在ALOAN、DW的額度序號，不變、取消、報價不要補資料近來
									} else {

										// 補資料
										boolean useNewEloan = true;
										Map<String, Object> l140m01aMap = null;
										l140m01aMap = eloanDBService
												.findL140M01A_lastByCustIdAndCntrNo_forThisReport(
														xCustId,
														xDupNo,
														xCntrNo,
														mainId,
														UtilConstants.Cntrdoc.ItemType.額度明細表);

										if (l140m01aMap == null
												|| l140m01aMap.isEmpty()) {
											useNewEloan = false;
											continue;
										}

										listMap.add(getFullContent_ForCreExcel2(
												dbData, l140m01aMap, gCustName,
												twBranchRate, useNewEloan));

										// 計算合計*************************************************

										// 額度後面會補借款人授信額度合計---------------------------------------------
										BigDecimal tFactAmtTS = BigDecimal.ZERO;
										BigDecimal tFactAmtTN = BigDecimal.ZERO;
										BigDecimal tFactAmtT = BigDecimal.ZERO;
										// 只補餘額
										BigDecimal tLoanBalTS = BigDecimal.ZERO;
										BigDecimal tLoanBalTN = BigDecimal.ZERO;
										BigDecimal tLoanBalT = BigDecimal.ZERO;

										// 額度、餘額都不補，當做0，額度部分最後會抓借款人授信額度合計來補，但餘額額度明細表分不出有擔或無擔，所以沒辦法補，乾脆不補

										factAmtTS = factAmtTS.add(tFactAmtTS);
										factAmtTN = factAmtTN.add(tFactAmtTN);
										factAmtT = factAmtT.add(tFactAmtT);
										loanBalTS = loanBalTS.add(tLoanBalTS);
										loanBalTN = loanBalTN.add(tLoanBalTN);
										loanBalT = loanBalT.add(tLoanBalT);

									}
								}
							}

						}
					}
					// 補純ELOAN額度
					// END******************************************************

				}

				if (Util.notEquals(gFullCustId, tFullCustId)) {
					gFullCustId = tFullCustId;
					gCustName = tCustName;
				}

				String tCustId = Util.trim(Util.getLeftStr(gFullCustId, 10));
				String tDupNo = Util.trim(Util.getRightStr(gFullCustId, 1));
				String tCntrNo = Util.trim(MapUtils.getString(dbData,
						"LNF022_CONTRACT"));

				Map<String, Object> l140m01aMap = null;
				if (Util.notEquals(tCntrNo, "")) {
					// J-113-0069_05097_B1001 Web
					// e-Loan企金授信「主要關係戶與本行授信往來比較表」，借戶及其相關關係企業於本次有簽報者，引進本次簽報內容
					// 抓ELOAN 資料
					// ********************************************
					// 如果該額度有在本次簽報書簽案，要用本次簽報書額度明細表資料代替
					// ********************************************

					useEloanLoanTot_cntrNo.put(tCntrNo, tCntrNo);

					// 簽報書有沒有這個借款人，如果有，則
					boolean thisCaseReportHasCust = false;
					List<L140M01A> custL140m01as = l140m01aDao
							.findByMainIdAndCustId(mainId, tCustId, tDupNo,
									UtilConstants.Cntrdoc.ItemType.額度明細表);
					if (custL140m01as != null && !custL140m01as.isEmpty()) {
						thisCaseReportHasCust = true;
						useEloanLoanTot_custId.put(tCustId + "-" + tDupNo,
								tCustId + "-" + tDupNo);
					}

					// 預設用最本次簽報的額度序號之額度明細表
					boolean useNewEloan = true;
					l140m01aMap = eloanDBService
							.findL140M01A_lastByCustIdAndCntrNo_forThisReport(
									tCustId, tDupNo, tCntrNo, mainId,
									UtilConstants.Cntrdoc.ItemType.額度明細表);

					if (l140m01aMap == null || l140m01aMap.isEmpty()) {
						// 沒有本次簽案，則改抓已核准最後一筆
						useNewEloan = false;
						l140m01aMap = eloanDBService
								.findL140M01A_lastByCustIdAndCntrNo(tCustId,
										tDupNo, tCntrNo);
					}

					listMap.add(getFullContent_ForCreExcel2(dbData,
							l140m01aMap, gCustName, twBranchRate, useNewEloan));

					// 計算合計*************************************************
					// J-113-0069_05097_B1001 Web
					// e-Loan企金授信「主要關係戶與本行授信往來比較表」，借戶及其相關關係企業於本次有簽報者，引進本次簽報內容
					// 額度---------------------------------------------
					BigDecimal tFactAmtTS = BigDecimal.ZERO;
					BigDecimal tFactAmtTN = BigDecimal.ZERO;
					BigDecimal tFactAmtT = BigDecimal.ZERO;
					if (thisCaseReportHasCust) {
						// 額度部分改抓簽報書的借款人授信額度合計
						// 這邊就不要加總
						// 到最後面在整個簽報書處理
					} else {
						tFactAmtTS = Util.equals(Util.trim(MapUtils.getString(
								tDbData, "AMT_TS")), "") ? BigDecimal.ZERO
								: LMSUtil.toBigDecimal(Util.trim(MapUtils
										.getString(tDbData, "AMT_TS")));
						tFactAmtTN = Util.equals(Util.trim(MapUtils.getString(
								tDbData, "AMT_TN")), "") ? BigDecimal.ZERO
								: LMSUtil.toBigDecimal(Util.trim(MapUtils
										.getString(tDbData, "AMT_TN")));
						tFactAmtT = Util
								.equals(Util.trim(MapUtils.getString(tDbData,
										"AMT_T")), "") ? BigDecimal.ZERO
								: LMSUtil.toBigDecimal(Util.trim(MapUtils
										.getString(tDbData, "AMT_T")));
					}
					BigDecimal tLoanBalTS = Util.equals(Util.trim(MapUtils
							.getString(tDbData, "LOANBAL_TS")), "") ? BigDecimal.ZERO
							: LMSUtil.toBigDecimal(Util.trim(MapUtils
									.getString(tDbData, "LOANBAL_TS")));
					BigDecimal tLoanBalTN = Util.equals(Util.trim(MapUtils
							.getString(tDbData, "LOANBAL_TN")), "") ? BigDecimal.ZERO
							: LMSUtil.toBigDecimal(Util.trim(MapUtils
									.getString(tDbData, "LOANBAL_TN")));
					BigDecimal tLoanBalT = Util
							.equals(Util.trim(MapUtils.getString(tDbData,
									"LOANBAL_T")), "") ? BigDecimal.ZERO
							: LMSUtil.toBigDecimal(Util.trim(MapUtils
									.getString(tDbData, "LOANBAL_T")));

					factAmtTS = factAmtTS.add(tFactAmtTS);
					factAmtTN = factAmtTN.add(tFactAmtTN);
					factAmtT = factAmtT.add(tFactAmtT);
					loanBalTS = loanBalTS.add(tLoanBalTS);
					loanBalTN = loanBalTN.add(tLoanBalTN);
					loanBalT = loanBalT.add(tLoanBalT);

				}

			}

			// 最後一筆補DW資料
			if (Util.notEquals(gFullCustId, "")) {
				// 該借款人MIS最後一筆後，再塞DW資料*******************
				String tCustId = Util.trim(Util.getLeftStr(gFullCustId, 10));
				String tDupNo = Util.trim(Util.getRightStr(gFullCustId, 1));

				List<Map<String, Object>> dwDataList = dwdbService
						.findDW_LNCNTROVSByCustIdForCreExcel2(tCustId, tDupNo);
				for (Map<String, Object> dwData : dwDataList) {
					Map<String, Object> l140m01aMap = null;
					String tCntrNo = Util.trim(MapUtils.getString(dwData,
							"LNF022_CONTRACT"));
					if (Util.notEquals(tCntrNo, "")) {
						// J-113-0069_05097_B1001 Web
						// e-Loan企金授信「主要關係戶與本行授信往來比較表」，借戶及其相關關係企業於本次有簽報者，引進本次簽報內容
						// 抓ELOAN 資料

						useEloanLoanTot_cntrNo.put(tCntrNo, tCntrNo);

						// ********************************************
						// 如果該額度有在本次簽報書簽案，要用本次簽報書額度明細表資料代替
						// ********************************************

						// 簽報書有沒有這個借款人，如果有，則
						boolean thisCaseReportHasCust = false;
						List<L140M01A> custL140m01as = l140m01aDao
								.findByMainIdAndCustId(mainId, tCustId, tDupNo,
										UtilConstants.Cntrdoc.ItemType.額度明細表);
						if (custL140m01as != null && !custL140m01as.isEmpty()) {
							thisCaseReportHasCust = true;
							useEloanLoanTot_custId.put(tCustId + "-" + tDupNo,
									tCustId + "-" + tDupNo);
						}

						// 預設用最本次簽報的額度序號之額度明細表
						boolean useNewEloan = true;
						l140m01aMap = eloanDBService
								.findL140M01A_lastByCustIdAndCntrNo_forThisReport(
										tCustId, tDupNo, tCntrNo, mainId,
										UtilConstants.Cntrdoc.ItemType.額度明細表);

						if (l140m01aMap == null || l140m01aMap.isEmpty()) {
							// 沒有本次簽案，則改抓已核准最後一筆
							useNewEloan = false;
							l140m01aMap = eloanDBService
									.findL140M01A_lastByCustIdAndCntrNo(
											tCustId, tDupNo, tCntrNo);
						}

						listMap.add(getFullContent_ForCreExcel2(dwData,
								l140m01aMap, gCustName, null, useNewEloan));

						// 計算合計*************************************************
						// J-113-0069_05097_B1001 Web
						// e-Loan企金授信「主要關係戶與本行授信往來比較表」，借戶及其相關關係企業於本次有簽報者，引進本次簽報內容
						// 額度---------------------------------------------
						BigDecimal tFactAmtTS = BigDecimal.ZERO;
						BigDecimal tFactAmtTN = BigDecimal.ZERO;
						BigDecimal tFactAmtT = BigDecimal.ZERO;
						if (thisCaseReportHasCust) {
							// 額度部分改抓簽報書的借款人授信額度合計
							// 這邊就不要加總
							// 到最後面在整個簽報書處理
						} else {
							tFactAmtTS = Util.equals(Util.trim(MapUtils
									.getString(dwData, "AMT_TS")), "") ? BigDecimal.ZERO
									: LMSUtil.toBigDecimal(Util.trim(MapUtils
											.getString(dwData, "AMT_TS")));
							tFactAmtTN = Util.equals(Util.trim(MapUtils
									.getString(dwData, "AMT_TN")), "") ? BigDecimal.ZERO
									: LMSUtil.toBigDecimal(Util.trim(MapUtils
											.getString(dwData, "AMT_TN")));
							tFactAmtT = Util.equals(Util.trim(MapUtils
									.getString(dwData, "AMT_T")), "") ? BigDecimal.ZERO
									: LMSUtil.toBigDecimal(Util.trim(MapUtils
											.getString(dwData, "AMT_T")));
						}
						BigDecimal tLoanBalTS = Util.equals(Util.trim(MapUtils
								.getString(dwData, "LOANBAL_TS")), "") ? BigDecimal.ZERO
								: LMSUtil.toBigDecimal(Util.trim(MapUtils
										.getString(dwData, "LOANBAL_TS")));
						BigDecimal tLoanBalTN = Util.equals(Util.trim(MapUtils
								.getString(dwData, "LOANBAL_TN")), "") ? BigDecimal.ZERO
								: LMSUtil.toBigDecimal(Util.trim(MapUtils
										.getString(dwData, "LOANBAL_TN")));
						BigDecimal tLoanBalT = Util.equals(Util.trim(MapUtils
								.getString(dwData, "LOANBAL_T")), "") ? BigDecimal.ZERO
								: LMSUtil.toBigDecimal(Util.trim(MapUtils
										.getString(dwData, "LOANBAL_T")));

						factAmtTS = factAmtTS.add(tFactAmtTS);
						factAmtTN = factAmtTN.add(tFactAmtTN);
						factAmtT = factAmtT.add(tFactAmtT);
						loanBalTS = loanBalTS.add(tLoanBalTS);
						loanBalTN = loanBalTN.add(tLoanBalTN);
						loanBalT = loanBalT.add(tLoanBalT);

					}
				}

				// J-113-0069_05097_B1001 Web
				// e-Loan企金授信「主要關係戶與本行授信往來比較表」，借戶及其相關關係企業於本次有簽報者，引進本次簽報內容
				// 補純ELOAN額度
				// BGN******************************************************
				// 只有出現在本次簽案的額度名序號(沒有出現再ALOAN與DW)，且額度性質非'7','8','13'

				List<L140M01A> custL140m01as = l140m01aDao
						.findByMainIdAndCustId(mainId, tCustId, tDupNo,
								UtilConstants.Cntrdoc.ItemType.額度明細表);
				if (custL140m01as != null && !custL140m01as.isEmpty()) {
					for (L140M01A custL140m01a : custL140m01as) {
						String xCntrNo = Util.trim(custL140m01a.getCntrNo());
						String xCustId = Util.trim(custL140m01a.getCustId());
						String xDupNo = Util.trim(custL140m01a.getDupNo());
						if (Util.notEquals(xCntrNo, "")) {
							if (!useEloanLoanTot_cntrNo.containsKey(xCntrNo)) {
								if (LMSUtil.isContainValue(
										Util.trim(custL140m01a.getProPerty()),
										UtilConstants.Cntrdoc.Property.取消)
										|| LMSUtil
												.isContainValue(
														Util.trim(custL140m01a
																.getProPerty()),
														UtilConstants.Cntrdoc.Property.報價)) {
									// ELOAN額度序號沒有出現在ALOAN、DW的額度序號，不變、取消、報價不要補資料近來
								} else {

									// 補資料
									boolean useNewEloan = true;
									Map<String, Object> l140m01aMap = null;
									l140m01aMap = eloanDBService
											.findL140M01A_lastByCustIdAndCntrNo_forThisReport(
													xCustId,
													xDupNo,
													xCntrNo,
													mainId,
													UtilConstants.Cntrdoc.ItemType.額度明細表);

									if (l140m01aMap == null
											|| l140m01aMap.isEmpty()) {
										useNewEloan = false;
										continue;
									}

									listMap.add(getFullContent_ForCreExcel2(
											dbData, l140m01aMap, gCustName,
											twBranchRate, useNewEloan));

									// 計算合計*************************************************

									// 額度後面會補借款人授信額度合計---------------------------------------------
									BigDecimal tFactAmtTS = BigDecimal.ZERO;
									BigDecimal tFactAmtTN = BigDecimal.ZERO;
									BigDecimal tFactAmtT = BigDecimal.ZERO;
									// 只補餘額
									BigDecimal tLoanBalTS = BigDecimal.ZERO;
									BigDecimal tLoanBalTN = BigDecimal.ZERO;
									BigDecimal tLoanBalT = BigDecimal.ZERO;

									// 額度、餘額都不補，當做0，額度部分最後會抓借款人授信額度合計來補，但餘額額度明細表分不出有擔或無擔，所以沒辦法補，乾脆不補

									factAmtTS = factAmtTS.add(tFactAmtTS);
									factAmtTN = factAmtTN.add(tFactAmtTN);
									factAmtT = factAmtT.add(tFactAmtT);
									loanBalTS = loanBalTS.add(tLoanBalTS);
									loanBalTN = loanBalTN.add(tLoanBalTN);
									loanBalT = loanBalT.add(tLoanBalT);

								}
							}
						}

					}
				}
				// 補純ELOAN額度
				// END******************************************************

			}

			// J-113-0069_05097_B1001 Web
			// e-Loan企金授信「主要關係戶與本行授信往來比較表」，借戶及其相關關係企業於本次有簽報者，引進本次簽報內容
			// ******************************************************
			// 本次有簽案要補授信額度合計之借款人
			// ******************************************************
			if (useEloanLoanTot_custId != null
					&& !useEloanLoanTot_custId.isEmpty()) {

				for (String name : useEloanLoanTot_custId.keySet()) {
					String[] xx = useEloanLoanTot_custId.get(name).split("-");
					String tCustId = xx[0];
					String tDupNo = xx[1];

					List<L140M01A> custL140m01as = l140m01aDao
							.findByMainIdAndCustId(mainId, tCustId, tDupNo,
									UtilConstants.Cntrdoc.ItemType.額度明細表);
					if (custL140m01as != null && !custL140m01as.isEmpty()) {
						for (L140M01A custL140m01a : custL140m01as) {

							String loanTotCurr = custL140m01a.getLoanTotCurr();
							BigDecimal loanTotAmt = custL140m01a
									.getLoanTotAmt();
							// String assureTotCurr = custL140m01a
							// .getAssureTotCurr();
							BigDecimal assureTotAmt = custL140m01a
									.getAssureTotAmt();

							BigDecimal tFactAmtTS = BigDecimal.ZERO;
							BigDecimal tFactAmtTN = BigDecimal.ZERO;
							BigDecimal tFactAmtT = BigDecimal.ZERO;

							tFactAmtTS = assureTotAmt;
							tFactAmtTN = loanTotAmt.subtract(assureTotAmt);
							factAmtT = loanTotAmt;

							if (Util.notEquals(loanTotCurr, mainCurr)) {
								tFactAmtTS = branchRate.toOtherAmt(loanTotCurr,
										mainCurr, tFactAmtTS);
								tFactAmtTN = branchRate.toOtherAmt(loanTotCurr,
										mainCurr, tFactAmtTN);
								factAmtT = branchRate.toOtherAmt(loanTotCurr,
										mainCurr, factAmtT);
							}

							factAmtTS = factAmtTS.add(tFactAmtTS);
							factAmtTN = factAmtTN.add(tFactAmtTN);
							factAmtT = factAmtT.add(tFactAmtT);

							// 抓借款人第一筆額度明細表就好了
							break;
						}
					}
				}
			}

			if (!listMap.isEmpty()) {
				y = y + 1;
				// J-108-0299_05097_B1001 Web
				// e-Loan企金授信增加並調整e-Loan「集團關係企業與本行授信往來條件比較表」欄位
				for (int i = 0, k = y; i < listMap.size(); i++, k++) {
					// 公司名稱
					Label labelCustName = new Label(0, k, listMap.get(i)
							.get("custName").toString(), format12Left);
					sheet.addCell(labelCustName);
					// 分行
					// Label labelOwnBrId = new Label(1, k, listMap.get(i)
					// .get("ownBrId").toString(), format12Left);
					// sheet.addCell(labelOwnBrId);
					// 信用評等
					Label labelGrade = new Label(1, k, listMap.get(i)
							.get("grade").toString(), format12Left);
					sheet.addCell(labelGrade);

					// 性質(科目)
					Label labelLnSubject = new Label(2, k, listMap.get(i)
							.get("lnSubject").toString(), format12Left);
					sheet.addCell(labelLnSubject);

					// 核准日期
					// Label labelApproveTime = new Label(3, k, listMap.get(i)
					// .get("approveTime").toString(), format12Center);
					// sheet.addCell(labelApproveTime);
					// 額度序號
					// Label labelCntrNo = new Label(1, k, listMap.get(i)
					// .get("cntrNo").toString(), format12Center);
					// sheet.addCell(labelCntrNo);
					// 額度(仟元)
					Label labelCurrentApplyAmt = new Label(3, k, listMap.get(i)
							.get("currentApplyAmt").toString(), format12Right);
					sheet.addCell(labelCurrentApplyAmt);
					// 餘額(仟元)
					Label labelLoanBal = new Label(4, k, listMap.get(i)
							.get("loanBal").toString(), format12Right);
					sheet.addCell(labelLoanBal);

					// 額度起迄日
					Label labelContractDuring = new Label(5, k, listMap.get(i)
							.get("contractDuring").toString(), format12Left);
					sheet.addCell(labelContractDuring);

					// 性質
					// Label labelProperty = new Label(7, k, listMap.get(i)
					// .get("property").toString(), format12Left);
					// sheet.addCell(labelProperty);
					// 科目
					// Label labelLnSubject = new Label(5, k, listMap.get(i)
					// .get("lnSubject").toString(), format12Left);
					// sheet.addCell(labelLnSubject);
					// 清償期限
					// Label labelPayDeadline = new Label(9, k, listMap.get(i)
					// .get("payDeadline").toString(), format12Left);
					// sheet.addCell(labelPayDeadline);
					// 保證人
					Label labelGuarantor = new Label(6, k, listMap.get(i)
							.get("guarantor").toString(), format12Left);
					sheet.addCell(labelGuarantor);
					// 擔保品
					Label labelDanbowDscr = new Label(7, k, listMap.get(i)
							.get("danbowDscr").toString(), format12Left);
					sheet.addCell(labelDanbowDscr);
					// 利率
					Label labelRateDscr = new Label(8, k, listMap.get(i)
							.get("rateDscr").toString(), format12Left);
					sheet.addCell(labelRateDscr);
					// 其他敘做條件
					// Label labelOtherDscr = new Label(9, k, listMap.get(i)
					// .get("otherDscr").toString(), format12Left);
					// sheet.addCell(labelOtherDscr);
					// 備註
					// Label labelMemoDscr = new Label(10, k, listMap.get(i)
					// .get("memoDscr").toString(), format12Left);
					// sheet.addCell(labelMemoDscr);

					// 敘做分行
					String shareBrStr = listMap.get(i).get("shareBr")
							.toString();
					String ownBrIdStr = listMap.get(i).get("ownBrId")
							.toString();
					String brStr = Util.isEmpty(shareBrStr) ? branch
							.getBranchName(ownBrIdStr) : shareBrStr;
					Label shareBr = new Label(9, k, brStr, format12Center);
					sheet.addCell(shareBr);
				}
			}

			List<L120S04B> listL120s04b = lmsService
					.findL120s04bByMainIdKeyCustIdDupNoDocKind(mainId, mCustId,
							mDupNo, new String[] { "0" });
			L120S04B l120s04b = null;
			if (!listL120s04b.isEmpty()) {
				for (L120S04B model : listL120s04b) {
					l120s04b = model;
					break;
				}
			}
			StringBuffer grpRateSb = new StringBuffer("");
			if (l120s04b != null) {
				String grpGrrd = Util.nullToSpace(l120s04b.getGrpGrrd());
				if (Util.notEquals(grpGrrd, "") && Util.notEquals(grpGrrd, "0")) {
					grpRateSb
							.append("屬主要集團企業，最近一年(")
							.append(Util.nullToSpace(l120s04b.getMainGrpDateS()))
							.append("~")
							.append(Util.nullToSpace(l120s04b.getMainGrpDateE()))
							.append(")集團風險性資產平均餘額報酬率：")
							.append((Util.isEmpty(l120s04b.getMainGrpAvgRate())) ? "N.A."
									: Util.nullToSpace(l120s04b
											.getMainGrpAvgRate())).append("%。");
				} else {
					grpRateSb
							.append("最近一年(")
							.append(Util.nullToSpace(l120s04b
									.getMainGrpDateRS()))
							.append("~")
							.append(Util.nullToSpace(l120s04b
									.getMainGrpDateRE()))
							.append(")主借款人暨關係戶風險性資產平均餘額報酬率：")
							.append((Util.isEmpty(l120s04b.getMainGrpAvgRateR())) ? "N.A."
									: Util.nullToSpace(l120s04b
											.getMainGrpAvgRateR()))
							.append("%。");
				}
			}

			int mapEndLine = y + listMap.size();
			sheet.mergeCells(0, mapEndLine, 7, mapEndLine);
			Label labelGrpRate = new Label(0, mapEndLine, grpRateSb.toString(),
					format12Left);
			sheet.addCell(labelGrpRate);
			// 最後一行開始塞合計****************************************************************************

			BigDecimal factAmtTS1000 = BigDecimal.ZERO;
			BigDecimal factAmtTN1000 = BigDecimal.ZERO;
			BigDecimal loanBalTS1000 = BigDecimal.ZERO;
			BigDecimal loanBalTN1000 = BigDecimal.ZERO;

			// J-108-0299_05097_B1001 Web
			// e-Loan企金授信增加並調整e-Loan「集團關係企業與本行授信往來條件比較表」欄位
			int finalLine = mapEndLine + 1;

			sheet.mergeCells(0, finalLine, 3, finalLine);
			String factAmtTsStr = "";
			if (factAmtTS.compareTo(BigDecimal.ZERO) > 0) {
				factAmtTS1000 = factAmtTS.divide(new BigDecimal("1000"), 0,
						BigDecimal.ROUND_HALF_UP);
				factAmtTsStr = NumConverter.addComma(factAmtTS1000);
			} else {
				factAmtTsStr = "0";
			}
			Label labelFactAmtTS = new Label(0, finalLine, "集團/關係企業擔保額度合計：TWD"
					+ factAmtTsStr + "仟元", format12Left);
			sheet.addCell(labelFactAmtTS);

			sheet.mergeCells(4, finalLine, 7, finalLine);

			String loanBalTSStr = "";
			if (loanBalTS.compareTo(BigDecimal.ZERO) > 0) {
				loanBalTS1000 = loanBalTS.divide(new BigDecimal("1000"), 0,
						BigDecimal.ROUND_HALF_UP);
				loanBalTSStr = NumConverter.addComma(loanBalTS1000);
			} else {
				loanBalTSStr = "0";
			}

			Label labelLoanBalTS = new Label(4, finalLine, "集團/關係企業擔保餘額合計：TWD"
					+ loanBalTSStr + "仟元", format12Left);
			sheet.addCell(labelLoanBalTS);

			// **************************************************************************************************
			finalLine = finalLine + 1;

			sheet.mergeCells(0, finalLine, 3, finalLine);
			String factAmtTNStr = "";
			if (factAmtTN.compareTo(BigDecimal.ZERO) > 0) {
				factAmtTN1000 = factAmtTN.divide(new BigDecimal("1000"), 0,
						BigDecimal.ROUND_HALF_UP);
				factAmtTNStr = NumConverter.addComma(factAmtTN1000);
			} else {
				factAmtTNStr = "0";
			}
			Label labelFactAmtTN = new Label(0, finalLine, "集團/關係企業無擔保額度合計：TWD"
					+ factAmtTNStr + "仟元", format12Left);
			sheet.addCell(labelFactAmtTN);

			sheet.mergeCells(4, finalLine, 7, finalLine);
			String loanBalTNStr = "";
			if (loanBalTN.compareTo(BigDecimal.ZERO) > 0) {
				loanBalTN1000 = loanBalTN.divide(new BigDecimal("1000"), 0,
						BigDecimal.ROUND_HALF_UP);
				loanBalTNStr = NumConverter.addComma(loanBalTN1000);
			} else {
				loanBalTNStr = "0";
			}
			Label labelLoanBalTN = new Label(4, finalLine, "集團/關係企業無擔保餘額合計：TWD"
					+ loanBalTNStr + "仟元", format12Left);
			sheet.addCell(labelLoanBalTN);

			// **************************************************************************************************

			finalLine = finalLine + 1;

			sheet.mergeCells(0, finalLine, 3, finalLine);

			// 總合計改以有擔除以1000+無擔除以1000

			factAmtT = factAmtTN1000.add(factAmtTS1000);
			String factAmtTStr = "";
			if (factAmtT.compareTo(BigDecimal.ZERO) > 0) {
				factAmtTStr = NumConverter.addComma(factAmtT);
			} else {
				factAmtTStr = "0";
			}
			Label labelFactAmtT = new Label(0, finalLine, "集團/關係企業總額度合計：TWD"
					+ factAmtTStr + "仟元", format12Left);
			sheet.addCell(labelFactAmtT);

			sheet.mergeCells(4, finalLine, 7, finalLine);
			// 總合計改以有擔+無擔
			loanBalT = loanBalTN1000.add(loanBalTS1000);
			String loanBalTStr = "";
			if (loanBalT.compareTo(BigDecimal.ZERO) > 0) {
				loanBalTStr = NumConverter.addComma(loanBalT);
			} else {
				loanBalTStr = "0";
			}
			Label labelLoanBalT = new Label(4, finalLine, "集團/關係企業總餘額合計：TWD"
					+ loanBalTStr + "仟元", format12Left);
			sheet.addCell(labelLoanBalT);

			book.write();
			book.close();
			// return baos.toByteArray();
			// return null;
		} catch (Exception ex) {
			LOGGER.error("[getContent] Exception!!", ex.getMessage());
		} finally {
			if (baos != null) {
				try {
					baos.close();
				} catch (IOException ex) {
					LOGGER.error("[getContent] Exception!!", ex.getMessage());
				}
			}
		}
	}

	/**
	 * J-107-0225_05097_B1001 Web e-Loan企金授信簽報書新增集團關係企業與本行授信往來條件比較表
	 * 取得最新一筆ELOAN額度明細表
	 * 
	 * @param dbMap
	 * @param l140m01aMap
	 * @return
	 */
	private Map<String, Object> getFullContent_ForCreExcel2(
			Map<String, Object> dbMap, Map<String, Object> l140m01aMap,
			String custName, BranchRate twBranchRate, boolean useNewEloan) {
		Map<String, String> proPertyMap = codetypeService.findByCodeType(
				"lms1405s02_proPerty", "zh_TW");
		Map<String, Object> map = new HashMap<String, Object>();
		/*
		 * 信用評等(用L120M01A.docType判斷企金/個金) 企金：L120S01C.crdType + L120S01C.grade
		 * 個金：C120S01G.grade3
		 */
		String grade = null;
		// 餘額
		String loanBal = null;
		// 核准日期(L120M01A.approveTime)
		String approveTime = null;
		// 編製單位(L140M01A.ownBrId)
		String ownBrId = null;
		// 額度序號(L140M01A.cntrNo)
		String cntrNo = null;
		// 額度(仟元)(L140M01A.currentApplyCurr + L140M01A.currentApplyAmt)
		String currentApplyAmt = null;
		// 性質(L140M01A.property)
		String property = null;
		// 科目(L140M01A.lnSubject)
		String lnSubject = null;
		// 清償期限(L140M01A.payDeadline)
		String payDeadline = null;
		// 保證人(L140M01A.guarantorType=="2" + L140M01A.guarantor)
		String guarantor = null;
		// 擔保品(L140M01B.itemType=="3" + L140M01B.itemDscr)
		String danbowDscr = null;
		// 利率(L140M01B.itemType=="2" + L140M01B.itemDscr)
		String rateDscr = null;
		// 其他敘作條件(L140M01B.itemType=="4" + L140M01B.itemDscr)
		String otherDscr = null;
		// 備註
		String memoDscr = null;
		// 額度明細表文件編號
		String cntrMainId = null;
		// 簽報書文件編號
		String mainId = null;
		// 額度起迄日
		String contractDuring = null;

		DecimalFormat df = new DecimalFormat(
				"###,###,###,###,###,###,###,##0.##");

		StringBuilder sb = new StringBuilder();
		sb.setLength(0);

		StringBuilder sc = new StringBuilder();
		sc.setLength(0);

		L140M01A l140m01a = null;
		L120M01A l120m01a = null;

		if (l140m01aMap == null || l140m01aMap.isEmpty()) {
			cntrMainId = "";
		} else {
			cntrMainId = Util.trim(MapUtils
					.getString(l140m01aMap, "CNTRMAINID"));
			l140m01a = service1405.findL140m01aByMainId(cntrMainId);
			if (l140m01a != null) {

				L120M01C relateModel = l140m01a.getL120m01c();

				if (relateModel != null) {
					// 簽報書文件編號
					mainId = Util.trim(relateModel.getMainId());
					l120m01a = service1201.findL120m01aByMainId(mainId);
				}

			}

		}

		if (l140m01a == null) {
			l140m01a = new L140M01A();
		}
		if (l120m01a == null) {
			l120m01a = new L120M01A();
		}

		// 以MIS DB資料為主
		String fullCustId = Util.trim(MapUtils.getString(dbMap, "CUST_ID"));
		String custId = Util.trim(Util.getLeftStr(fullCustId, 10));
		String dupNo = "";
		if (StringUtils.length(fullCustId) > 10) {
			dupNo = Util.trim(Util.getRightStr(fullCustId, 1));
		} else {
			dupNo = "0";
		}

		// 額度序號
		cntrNo = Util.trim(MapUtils.getString(dbMap, "LNF022_CONTRACT"));

		// 額度(仟元)
		// J-113-0069_05097_B1001 Web
		// e-Loan企金授信「主要關係戶與本行授信往來比較表」，借戶及其相關關係企業於本次有簽報者，引進本次簽報內容
		BigDecimal amt = BigDecimal.ZERO;
		String amtCurr = "";
		if (useNewEloan) {
			// 用ELOAN額度明細表
			amt = l140m01a.getCurrentApplyAmt() == null ? BigDecimal.ZERO
					: l140m01a.getCurrentApplyAmt();
			amtCurr = Util.trim(l140m01a.getCurrentApplyCurr());
		} else {
			// 用帳務資料
			amt = Util.equals(Util.trim(MapUtils.getString(dbMap, "AMT")), "") ? BigDecimal.ZERO
					: LMSUtil.toBigDecimal(Util.trim(MapUtils.getString(dbMap,
							"AMT")));
			amtCurr = Util.equals(
					Util.trim(MapUtils.getString(dbMap, "LNF022_FACT_SWFT")),
					"") ? "" : Util.trim(MapUtils.getString(dbMap,
					"LNF022_FACT_SWFT"));
		}

		if (amt.compareTo(BigDecimal.ZERO) > 0) {
			sb.append(amtCurr).append(
					NumConverter.addComma(amt == null ? BigDecimal.ZERO : amt
							.divide(new BigDecimal("1000"), 0,
									BigDecimal.ROUND_HALF_UP)));
		} else {
			sb.append(amtCurr).append("0");
		}

		currentApplyAmt = sb.toString();

		if (twBranchRate == null) {
			// 海外分行有提供原幣餘額，故可以直接用
			String strLoanBal = Util.trim(MapUtils
					.getString(dbMap, "LOANBAL_O"));
			BigDecimal orgLoanBal = Util.equals(strLoanBal, "") ? BigDecimal.ZERO
					: LMSUtil.toBigDecimal(strLoanBal);
			if (orgLoanBal.compareTo(BigDecimal.ZERO) > 0) {
				loanBal = NumConverter.addComma((orgLoanBal.divide(
						new BigDecimal("1000"), 0, BigDecimal.ROUND_HALF_UP)));
			} else {
				loanBal = "0";
			}

		} else {
			// 國內分行從LNF022來，餘額已經是台幣，所以要轉回額度幣別
			// 餘額(台幣)
			String strLoanBal = Util.trim(MapUtils
					.getString(dbMap, "LOANBAL_T")); // LOANBAL
			BigDecimal twdLoanBal = Util.equals(strLoanBal, "") ? BigDecimal.ZERO
					: LMSUtil.toBigDecimal(strLoanBal);
			// 轉原幣
			BigDecimal orgLoanBal = twBranchRate.toOtherAmt("TWD", amtCurr,
					twdLoanBal);

			if (orgLoanBal.compareTo(BigDecimal.ZERO) > 0) {
				loanBal = NumConverter.addComma(orgLoanBal.divide(
						new BigDecimal("1000"), 0, BigDecimal.ROUND_HALF_UP));
			} else {
				loanBal = "0";
			}

		}

		sc.append(amtCurr).append(loanBal);
		loanBal = sc.toString();

		// 額度起日
		String bgnDate = null;
		// 額度迄日
		String endDate = null;
		bgnDate = Util
				.trim(MapUtils.getString(dbMap, "BGN_DATE", "0001-01-01"));
		endDate = Util
				.trim(MapUtils.getString(dbMap, "END_DATE", "0001-01-01"));

		if (Util.notEquals(bgnDate, "0001-01-01")
				&& Util.notEquals(endDate, "0001-01-01")) {
			contractDuring = bgnDate + "~" + endDate;
		} else {
			contractDuring = "";
		}

		// 以額度明細表為主*****************************************************************

		// 編製單位
		ownBrId = Util.trim(l140m01a.getOwnBrId());

		// 攤貸分行
		StringBuffer brnSb = new StringBuffer("");
		StringBuffer rateSb = new StringBuffer("");
		String shareBr = "";
		Set<L140M01E> l140m01es = l140m01a.getL140m01e();
		if (l140m01es != null && !l140m01es.isEmpty()) {
			for (L140M01E l140m01e : l140m01es) {
				String shareBrId = Util.equals(l140m01e.getShareBrId(), "") ? ""
						: l140m01e.getShareBrId();
				BigDecimal SHARERATE1 = l140m01e.getShareRate1();
				brnSb.append((brnSb.length() > 0) ? "：" : "").append(
						branch.getBranchName(shareBrId) == null ? shareBrId
								: branch.getBranchName(shareBrId));
				rateSb.append((rateSb.length() > 0) ? "：" : "").append(
						(SHARERATE1 == null ? "" : SHARERATE1.toString()));
			}
			if (l140m01es.size() > 1) {
				shareBr = brnSb.append("＝").append(rateSb).toString();
			} else {
				shareBr = brnSb.toString();
			}
		}

		// sb.append(Util.trim(l140m01a.getCurrentApplyCurr()))
		// .append(NumConverter
		// .addComma(l140m01a.getCurrentApplyAmt() == null ? BigDecimal.ZERO
		// : l140m01a.getCurrentApplyAmt().divide(
		// new BigDecimal("1000"), 2,
		// BigDecimal.ROUND_HALF_UP)));
		//
		// // 額度(仟元)
		// currentApplyAmt = sb.toString();

		// 性質
		property = this.getProPerty(Util.trim(l140m01a.getProPerty()),
				proPertyMap);
		// 科目
		lnSubject = Util.trim(l140m01a.getLnSubject());
		// 清償期限
		payDeadline = Util.trim(l140m01a.getPayDeadline());
		// 核准日期
		if (l120m01a.getEndDate() != null) {
			approveTime = TWNDate.toTW(l120m01a.getEndDate());
		} else {
			approveTime = "";
		}

		if ("2".equals(Util.trim(l140m01a.getGuarantorType()))) {
			// 保證人內容
			guarantor = Util.trim(l140m01a.getGuarantor());
		} else if ("1".equals(Util.trim(l140m01a.getGuarantorType()))) {
			// 連保人內容
			guarantor = Util.trim(l140m01a.getGuarantor());
		}

		L140M01B danBowModel = service1405.findL140m01bUniqueKey(cntrMainId,
				UtilConstants.Cntrdoc.l140m01bItemType.擔保品);
		L140M01B rateModel = service1405.findL140m01bUniqueKey(cntrMainId,
				UtilConstants.Cntrdoc.l140m01bItemType.利費率);
		String m01bRateDscr = "";
		try {
			m01bRateDscr = lms1401Service.saveL140m01bDscr2(cntrMainId, "", true);
		} catch (CapMessageException e) {

		}
		L140M01B otherModel = service1405.findL140m01bUniqueKey(cntrMainId,
				UtilConstants.Cntrdoc.l140m01bItemType.其他敘做條件);

		if (danBowModel != null) {
			// 擔保品內容
			danbowDscr = filterHtml(Util.trim(danBowModel.getItemDscr()))
					.replace("&nbsp;", UtilConstants.Mark.SPACE);
		}
		if (l120m01a.getTypCd() != null
				&& UtilConstants.Casedoc.typCd.海外.equals(Util.trim(l120m01a.getTypCd()))) {
			if (rateModel != null) {
				// 利費率內容
				rateDscr = filterHtml(Util.trim(rateModel.getItemDscr())).replace(
						"&nbsp;", UtilConstants.Mark.SPACE);
			}
		} else {
			rateDscr = filterHtml(Util.trim(m01bRateDscr)).replace(
					"&nbsp;", UtilConstants.Mark.SPACE);
		}
		if (otherModel != null) {
			// 其他敘做條件內容
			otherDscr = filterHtml(Util.trim(otherModel.getItemDscr()))
					.replace("&nbsp;", UtilConstants.Mark.SPACE);
		}

		if (l120m01a != null) {
			String docType = Util.trim(l120m01a.getDocType());
			if (UtilConstants.Casedoc.DocType.企金.equals(docType)) {
				// 企金信評設定
				StringBuilder trustSb = new StringBuilder();
				trustSb.setLength(0);
				// J-111-0466 依據企金業務處940F11108-4號傳真函，調整"集團／關係企業與本行授信往來條件比較表"格式
				// 抓最新 信用評等 / 信用風險內部評等
				List<L120S01C> busList = new ArrayList<L120S01C>();

				// 2023/07/21 [上午 09:11] 金至忠(授信審查處,襄理)
				// 集團比較表, 評等會有出現兩個的狀況, 能僅出現最新的嗎?
				// 信用風險內部評等
				Map<String, Object> map2 = lmsService.getLastedCrd(custId,
						dupNo, 2);
				if (map2 != null && !map2.isEmpty()) {
					L120S01C temp2 = new L120S01C();
					// 評等（公佈）日期
					temp2.setCrdTYear(CapDate.parseSQLDate(Util.trim(Util
							.nullToSpace(map2.get("crdTYear")))));
					// 評等單位
					temp2.setCrdTBR(Util.trim(Util.nullToSpace(map2
							.get("crdTBR"))));
					// 信評表類別
					temp2.setCrdType(Util.trim(Util.nullToSpace(map2
							.get("crdType"))));
					// 評等評分
					temp2.setScore(Util.parseInt(Util.nullToSpace(map2
							.get("score"))));
					// 評等等級
					temp2.setGrade(Util.trim(Util.nullToSpace(map2.get("grade"))));
					busList.add(temp2);
				} else {
					// 信用評等
					Map<String, Object> map1 = lmsService.getLastedCrd(custId,
							dupNo, 1);
					if (map1 != null && !map1.isEmpty()) {
						L120S01C temp1 = new L120S01C();
						// 評等（公佈）日期
						temp1.setCrdTYear(CapDate.parseSQLDate(Util.trim(Util
								.nullToSpace(map1.get("crdTYear")))));
						// 評等單位
						temp1.setCrdTBR(Util.trim(Util.nullToSpace(map1
								.get("crdTBR"))));
						// 信評表類別
						temp1.setCrdType(Util.trim(Util.nullToSpace(map1
								.get("crdType"))));
						// 評等評分
						temp1.setScore(Util.parseInt(Util.nullToSpace(map1
								.get("score"))));
						// 評等等級
						temp1.setGrade(Util.trim(Util.nullToSpace(map1
								.get("grade"))));
						busList.add(temp1);
					}
				}

				// List<L120S01C> busList = service1201.findL120s01cByCustId(
				// mainId, custId, dupNo);
				// for (L120S01C busModel : busList) {
				// String crdType = Util.trim(busModel.getCrdType());
				// trustSb.append(
				// (trustSb.length() > 0) ? "\n"
				// : UtilConstants.Mark.SPACE)
				// .append(getTrustName(crdType))
				// .append(("NA".equals(crdType) || "NO"
				// .equals(crdType)) ? UtilConstants.Mark.SPACE
				// : "：")
				// .append(Util.trim(busModel.getGrade()));
				// }
				// grade = trustSb.toString();
				grade = getL120S01CData(busList, "2");
			} else if (UtilConstants.Casedoc.DocType.個金.equals(docType)) {
				// 個金信評設定
				C120S01G perModel = c120s01gDao.findByUniqueKey(mainId,
						Util.trim(l120m01a.getOwnBrId()), custId, dupNo);
				if (perModel != null) {
					grade = Util.trim(perModel.getGrade3());
				}

			}
		}

		// 開始設定值
		map.put("ownBrId", Util.trim(ownBrId));
		map.put("cntrNo", Util.trim(cntrNo));
		map.put("custName", Util.trim(custName));
		map.put("grade", Util.trim(grade));
		map.put("contractDuring", Util.trim(contractDuring));
		map.put("loanBal", Util.trim(loanBal));
		map.put("approveTime", Util.trim(approveTime));
		map.put("currentApplyAmt", Util.trim(currentApplyAmt));
		map.put("property", Util.trim(property));
		map.put("lnSubject", Util.trim(lnSubject));
		map.put("payDeadline", Util.trim(payDeadline));
		map.put("guarantor", Util.trim(guarantor));
		map.put("danbowDscr", Util.trim(danbowDscr));
		map.put("rateDscr", Util.trim(rateDscr));
		map.put("otherDscr", Util.trim(otherDscr));
		map.put("memoDscr", Util.trim(memoDscr));
		map.put("shareBr", Util.trim(shareBr));

		return map;
	}

	/**
	 * 處理L120S05A.GRPGRRD中文字
	 * 
	 * @param grpGrrd
	 *            =未評等 ?=幾級
	 * @param prop
	 *            prop
	 * @return string
	 */
	private String showGrpGrrd(String grpGrade, String grpYYYY, String grpSize,
			String grpLevle, Properties prop) {
		StringBuffer str = new StringBuffer();

		// J-107-0087-001 Web
		// e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。
		String grpGrrd = Util.nullToSpace(grpGrade);
		String grpYear = Util.nullToSpace(grpYYYY);

		boolean newGrpGrade = lmsService.isNewGrpGrade(grpYear, true);
		String defultNoGrade = lmsService.getGrpNoGrade(newGrpGrade);

		if (newGrpGrade) {
			// 新集團評等
			if ("".equals(grpGrrd) || defultNoGrade.equals(grpGrrd)) {
				str.append(grpGrrd).append(" ")
						.append(prop.getProperty("L120S05A.GRPGRRDY"))
						.append("(")
						.append(prop.getProperty("L120S05A.GRPGRRDN_2017")) // L120S05A.GRPGRRDN_2017=新戶及未評等
						.append(")");
			} else {
				// 評等等級 + (大A)
				// l120s05a.grpYear=集團評等年度：
				str.append(grpGrrd)
						.append(" ")
						.append(prop.getProperty("L120S05A.GRPGRRDY"))
						.append(lmsService.getGrpSizeLvlShow(
								Util.trim(grpSize), Util.trim(grpLevle)));

				// J-107-0318_05097_B1001 Web e-Loan企金授信配合集團企業評等增加應予觀察註記相關程式修改
				if (Util.notEquals(Util.trim(grpYYYY), "")) {

					Properties commPop = MessageBundleScriptCreator
							.getComponentResource(LMSCommomPage.class);

					str.append("，")
							.append(commPop.getProperty("l120s05a.grpYear"))
							.append("").append(grpYYYY);
				}
			}
		} else {
			if ("".equals(grpGrrd) || defultNoGrade.equals(grpGrrd)) {
				str.append(grpGrrd).append(" ")
						.append(prop.getProperty("L120S05A.GRPGRRDY"))
						.append("(")
						.append(prop.getProperty("L120S05A.GRPGRRDN"))
						.append(")");
			} else if ("7".equals(grpGrrd)) {
				// L120S05A.GRPGRRDE=問題集團
				str.append(grpGrrd).append(" ")
						.append(prop.getProperty("L120S05A.GRPGRRDY"))
						.append("(")
						.append(prop.getProperty("L120S05A.GRPGRRDE"))
						.append(")");
			} else {
				str.append(grpGrrd).append(" ")
						.append(prop.getProperty("L120S05A.GRPGRRDY"));
			}
		}

		return str.toString();
	}

}
