/* 
 * F101M01ADaoImpl.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.util.CapString;

import com.mega.eloan.lms.dao.F101M01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.F101M01A;

/**
 * <pre>
 * F101M01A dao 實作。
 * </pre>
 * 
 * @since 2011/7/26
 * <AUTHOR> Wang
 * @version <ul>
 *          <li>2011/7/26,Sunkist Wang,new</li>
 *          </ul>
 */
@Repository
public class F101M01ADaoImpl extends LMSJpaDao<F101M01A, String> implements
		F101M01ADao {

//	/*
//	 * (non-Javadoc)
//	 * 
//	 * @see
//	 * com.mega.eloan.ces.fss.dao.F101M01ADao#findForOriginalMow(java.lang.String
//	 * , java.lang.String)
//	 */
//	@SuppressWarnings("unchecked")
//	public List<F101M01A> findForOriginalMow(String custId, String dupNo) {
//
//		ISearch search = createSearchTemplete();
////		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
////		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
////		search.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
////				CESDocStatusEnum.已確認.getCode());
//		search.addSearchModeParameters(SearchMode.EQUALS, "periodType",
//				PeriodTypeEnum.YEAR.getCode());
//		search.addSearchModeParameters(SearchMode.EQUALS, "type",
//				FssTypeEnum.GENERAL.getCode());
//		search.addSearchModeParameters(SearchMode.EQUALS, "conso",
//				CESConstant.S0);
//		search.addSearchModeParameters(SearchMode.IN, "source", new String[] {
//				FssSourceEnum.INQUIRY.getCode(),
//				FssSourceEnum.REVIEW.getCode(), FssSourceEnum.TAX.getCode(),
//				FssSourceEnum.SETTLEMENT.getCode() });
//		search.addSearchModeParameters(SearchMode.EQUALS, "publicFlag",
//				PublicFlagEnum.PUBLIC.getCode());
//		List<F101M01A> publicList = find(search);
//
//		String branch = MegaSSOSecurityContext.getUnitNo();
//		ISearch anoSearch = createSearchTemplete();
////		anoSearch.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
////		anoSearch.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
////		anoSearch.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
////				CESDocStatusEnum.已確認.getCode());
//		anoSearch.addSearchModeParameters(SearchMode.EQUALS, "periodType",
//				PeriodTypeEnum.YEAR.getCode());
//		anoSearch.addSearchModeParameters(SearchMode.EQUALS, "type",
//				FssTypeEnum.GENERAL.getCode());
//		anoSearch.addSearchModeParameters(SearchMode.EQUALS, "conso",
//				CESConstant.S0);
//		anoSearch.addSearchModeParameters(
//				SearchMode.IN,
//				"source",
//				new String[] { FssSourceEnum.INQUIRY.getCode(),
//						FssSourceEnum.REVIEW.getCode(),
//						FssSourceEnum.TAX.getCode(),
//						FssSourceEnum.SETTLEMENT.getCode() });
//		anoSearch.addSearchModeParameters(SearchMode.EQUALS,
//				"f101a01as.authUnit", branch);
//		anoSearch.addSearchModeParameters(SearchMode.EQUALS, "publicFlag",
//				PublicFlagEnum.PRIVATE.getCode());
//		List<F101M01A> privateList = find(anoSearch);
//
//		return (List<F101M01A>) CollectionUtils.union(publicList, privateList);
//	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.ces.fss.dao.F101M01ADao#getF101M01AbyMainId(java.lang.
	 * String)
	 */
	public F101M01A getF101M01AbyMainId(String mainId) {
		ISearch mySearch = createSearchTemplete();
		mySearch.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		return findUniqueOrNone(mySearch);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.ces.fss.dao.F101M01ADao#findDuplicateMeta(java.lang.String
	 * , java.lang.String, java.lang.String, java.lang.String, java.lang.String,
	 * java.lang.String, java.lang.String, java.lang.String, java.lang.String)
	 */
	public F101M01A findDuplicateMeta(String custId, String dupNo,
			String conso, String publicFlag, String year, String periodType,
			String mainId, String docstatus, String type) {
		ISearch search = createSearchTemplete();
		if (!CapString.isEmpty(custId)) {
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		}
		if (!CapString.isEmpty(dupNo)) {
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		}
		if (!CapString.isEmpty(mainId)) {
			search.addSearchModeParameters(SearchMode.NOT_EQUALS, "mainId",
					mainId);
		}
		if (!CapString.isEmpty(docstatus)) {
			search.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
					docstatus);
		}
		if (!CapString.isEmpty(year)) {
			search.addSearchModeParameters(SearchMode.EQUALS, "year", year);
		}
		if (!CapString.isEmpty(periodType)) {
			search.addSearchModeParameters(SearchMode.EQUALS, "periodType",
					periodType);
		}
		if (!CapString.isEmpty(conso)) {
			search.addSearchModeParameters(SearchMode.EQUALS, "conso", conso);
		}
		if (!CapString.isEmpty(publicFlag)) {
			search.addSearchModeParameters(SearchMode.EQUALS, "publicFlag",
					publicFlag);
		}
		if (!CapString.isEmpty(type)) {
			search.addSearchModeParameters(SearchMode.EQUALS, "type", type);
		}
		F101M01A entity = findUniqueOrNone(search);
		return entity;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.ces.fss.dao.F101M01ADao#copyDocument(java.lang.String,
	 * java.lang.String, java.lang.String, java.lang.String, java.lang.String,
	 * java.lang.String)
	 */
	public void copyDocument(String mainId, String docStatus,
			String randomCode, String branchNo, String userId, String newMianId) {
		// call ces.copyF101(?1,?2,?3,?4,?5?6)
		Query query = getEntityManager().createNamedQuery("f101m01a.copyF101");
		query.setParameter(1, mainId).setParameter(2, docStatus)
				.setParameter(3, randomCode).setParameter(4, branchNo)
				.setParameter(5, userId).setParameter(6, newMianId);
		query.executeUpdate();
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public List<String> getSortMainId(String[] mainIds) {
		Query query = getEntityManager().createNamedQuery(
				"f101m01a.getSortMainId");
		query.setParameter(1, mainIds[0]);
		query.setParameter(2, mainIds[1]);
		query.setParameter(3, mainIds[2]);
		query.setParameter(4, mainIds[3]);
		return (List<String>) query.getResultList();
	}	
}
