package com.mega.eloan.lms.base.report.impl;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.pages.LMS140MM01Page;
import com.mega.eloan.lms.base.service.AbstractReportService;
import com.mega.eloan.lms.base.service.LMS140MService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dao.impl.L140MM1ADaoImpl;
import com.mega.eloan.lms.dao.impl.L140MM1BDaoImpl;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.model.L140M01M;
import com.mega.eloan.lms.model.L140MM1A;
import com.mega.eloan.lms.model.L140MM1B;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.ReportGenerator;

/**
 * 產生央行註記異動PDF
 * 
 * @since 2014/08/28
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 * 
 */
@Service("lms140mr01rptservice")
public class LMS140MR01RptServiceImpl extends AbstractReportService {

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(LMS140MR01RptServiceImpl.class);
	@Resource
	EloandbBASEService eloanDbService;
	@Resource
	UserInfoService userInfoService;
	@Resource
	L140MM1ADaoImpl l140mm1adao;
	@Resource
	L140MM1BDaoImpl l140mm1bdao;
	@Resource
	BranchService branch;
	@Resource
	CodeTypeService codeTypeService;
	@Resource
	LMSService lmsService;
	@Resource
	LMS140MService lms140MService;

	@Override
	public String getReportTemplateFileName() {
		LOGGER.info("into getReportTemplateFileName");
		// zh_TW: 正體中文
		// zh_CN: 簡體中文
		// en_US: 英文
		Locale locale = LocaleContextHolder.getLocale();
		if (locale == null)
			locale = Locale.getDefault();
		// 測試用
		// return
		// "D:/work/src.mega/WebELoan45/lms/lms-config/src/main/resources/report/cls/CLS1021R01_zh_TW.rpt";
		return "report/cls/LMS140MR01_" + locale.toString() + ".rpt";
	}

	/*
	 * (non-Javadoc) 設定需要傳入RPT參數
	 * 
	 * @see
	 * com.mega.eloan.lms.base.service.AbstractReportService#setReportData(com
	 * .mega.eloan.lms.base.report.ReportGenerator,
	 * org.apache.wicket.PageParameters)
	 */
	@Override
	public void setReportData(ReportGenerator reportTools, PageParameters params) {
		// 測試用
		// reportTools.setTestMethod(true);
		LOGGER.info("into setReportData");
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS140MM01Page.class);
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		// List<Map<String, String>> titleRows = new LinkedList<Map<String,
		// String>>();
		String mainOid = Util.trim(params.getString(EloanConstants.MAIN_OID));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		// L140MM1A．購置房屋擔保放款風險權數檢核表主檔
		L140MM1A l140mm1a = null;
		// L140MM1B．購置房屋擔保放款風險權數檢核表簽章欄檔
		List<L140MM1B> l140mm1blist = null;

		L140M01M l140m01m = null;

		

		


		String branchName = null;
		// zh_TW: 正體中文
		// zh_CN: 簡體中文
		// en_US: 英文
		Locale locale = null;
		try {
			locale = LocaleContextHolder.getLocale();
			if (locale == null)
				locale = Locale.getDefault();
			l140mm1a = l140mm1adao.findByOid(mainOid);
			if (l140mm1a == null) {
				l140mm1a = new L140MM1A();
			}
			l140m01m = lmsService.findModelByMainId(L140M01M.class,
					l140mm1a.getMainId());

			Map<String, CapAjaxFormResult> L140M01MCodeMap = this.lmsService.getReportCodeMapOfRealEstateRuleVersion(l140m01m.getVersion());
			
			if (L140M01MCodeMap == null) {
				L140M01MCodeMap = new HashMap<String, CapAjaxFormResult>();
			}
			
			String apprid = "";
			String recheckid = "";
			String bossid = "";
			String managerid = "";
			l140mm1blist = l140mm1bdao.findByMainId(mainId);

			for (L140MM1B l140mm1b : l140mm1blist) {
				// 要加上人員代碼
				String type = Util.trim(l140mm1b.getStaffJob());
				String userId = Util.trim(l140mm1b.getStaffNo());
				String value = Util.trim(lmsService.getUserName(userId));
				if ("L1".equals(type)) {
					apprid = userId + " " + value;
				} else if ("L3".equals(type)) {
					bossid = bossid + userId + " " + value + "<br/>";
				} else if ("L4".equals(type)) {
					recheckid = userId + " " + value;
				} else if ("L5".equals(type)) {
					managerid = userId + " " + value;
				}
			}

			branchName = Util.nullToSpace(branch.getBranchName(Util
					.nullToSpace(l140mm1a.getOwnBrId())));
			String custId = Util.nullToSpace(l140mm1a.getCustId()) + " "
					+ Util.nullToSpace(l140mm1a.getDupNo());
			String custname = Util.nullToSpace(l140mm1a.getCustName());
			String cntrno = Util.nullToSpace(l140mm1a.getCntrNo());
			String tDate = "";

			if (!"".equals(Util.trim(l140mm1a.getApproveTime()))) {
				tDate = Util.trim(l140mm1a.getUpdateTime()) + "("
						+ Util.trim(l140mm1a.getApproveTime()) + ")";
			} else {
				tDate = Util.trim(l140mm1a.getUpdateTime());
			}

			// 2013-03-28_Rex Edit_新增央行購置訊息顯示
			String[] arr_1 = lms140MService.l140m01m_location(
					l140m01m.getCityId(), l140m01m.getAreaId(),
					l140m01m.getSit3No(), l140m01m.getSit4No());
			CapAjaxFormResult CityArea = new CapAjaxFormResult();
			CityArea.set("city", arr_1[0]);
			CityArea.set("area", arr_1[1]);
			CityArea.set("site3", arr_1[2]);
			L140M01MCodeMap.put("L140M01M_city", CityArea);

			if ("Y".equals(l140m01m.getLandBuildYN())) {
				CapAjaxFormResult LandBuildArea = new CapAjaxFormResult();
				if ("2".equals(l140m01m.getLocationDomestic())){
					//國外座落
					LandBuildArea.set("landbuidcity", "");
					LandBuildArea.set("landbuidarea", "");
					LandBuildArea.set("site3e", l140m01m.getLocationTarget());
				} else {
					//國內座落
					String[] arr_2 = lms140MService.l140m01m_location(
							l140m01m.getLocationCity(), l140m01m.getLocationCd(),
							l140m01m.getSite3No(), l140m01m.getSite4No());
					
					LandBuildArea.set("landbuidcity", arr_2[0]);
					LandBuildArea.set("landbuidarea", arr_2[1]);
					LandBuildArea.set("site3e", arr_2[2]);
				}
				L140M01MCodeMap.put("L140M01M_landbuidcity", LandBuildArea);
			}
			rptVariableMap.put("L140M01M.DESC",
					LMSUtil.L140M01MStr(l140m01m, L140M01MCodeMap));

			// 2013-09-14異動央行註記列印規則....
			// System.out.println("AAAAA["+prop.getProperty("yes")+"]["+prop.getProperty("no")+"]");
			if ("".equals(Util.trim(l140m01m.getCbcCase()))) {
				rptVariableMap.put("L140M01M.CBACASE_", "");
			} else if (!"4".equals(l140m01m.getCbcCase())) {
				rptVariableMap
						.put("L140M01M.CBACASE_", prop.getProperty("yes"));
			} else {
				rptVariableMap.put("L140M01M.CBACASE_", prop.getProperty("no"));
			}

			rptVariableMap.put("BRANCHNAME", branchName);
			rptVariableMap.put("L140MM1A.Mainid",
					Util.trim(l140m01m.getMainId()));
			rptVariableMap.put("L140MM1A.CUSTID", custId);

			rptVariableMap.put("L140MM1A.CUSTNAME", custname);

			rptVariableMap.put("L140MM1A.CNTRNO", cntrno);

			rptVariableMap.put("L140MM1B.APPRID", apprid);
			rptVariableMap.put("L140MM1B.RECHECKID", recheckid);
			rptVariableMap.put("L140MM1B.BOSSID", bossid);
			rptVariableMap.put("L140MM1B.MANAGERID", managerid);
			rptVariableMap.put("L140MM1B.TITLEMARK", "");
			rptVariableMap.put("L140MM1A.DATE", tDate);

			// this.generator.setLang(java.util.Locale.TAIWAN);
			reportTools.setLang(locale);
			reportTools.setVariableData(rptVariableMap);
			// reportTools.setRowsData(titleRows);
			// new ReportGenerator().checkVariableExist("C:/test.txt",
			// rptVariableMap);
		} finally {

		}
	}

}
