/* 
 * L120S01SDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S01S;

/** 高齡客戶關懷檢核表明細檔 **/
public interface L120S01SDao extends IGenericDao<L120S01S> {

	L120S01S findByOid(String oid);
	
	List<L120S01S> findByMainId(String mainId);

	List<L120S01S> findByIndex01(String mainId);
	
	List<L120S01S> findByRefOid(String refOid);
}