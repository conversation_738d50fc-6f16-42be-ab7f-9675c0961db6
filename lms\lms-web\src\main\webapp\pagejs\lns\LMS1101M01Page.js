
var initDfd = new $.Deferred(),
	initAll = new $.Deferred();
var l1101m01Json = {
	docType: responseJSON.docType,
	docCode: responseJSON.docCode,
	docKind: responseJSON.docKind,
	mainId: responseJSON.mainId,
	docStatus: responseJSON.mainDocStatus,
	areaDocstatus: responseJSON.areaDocstatus,
	unitNo: userInfo.unitNo,
	page: responseJSON.page,
	//依照不同系統控制顯示頁籤
	controlBook : function(){
		$.ajax({
			type : "POST",
			handler : "lms1101formhandler",
			data : 
			{
				formAction : "checkBookmark",
				mainId : responseJSON.mainId,
				docKind : responseJSON.docKind,
				docCode : responseJSON.docCode,
				docStatus : responseJSON.mainDocStatus
			},
			success:function(responseData){
				//alert(JSON.stringify(responseData));
				for(o in responseData.hideBook){
					$(DOMPurify.sanitize(responseData.hideBook[o])).hide();
				}
				$(".tabs-warp").scrollToTab();			
			}
		});		
	},
	// 儲存全部	
	saveAll : function(showMsg, needPrint, needSend){
		var $LMS1205S01Form = $("#LMS1205S01Form");
		var $L120M01BForm = $("#L120M01BForm");
		var $L120M01CForm = $("#L120M01CForm");    //J-105-0264-001  WEB-ELOAN授信案件簽報書增訂「新臺幣、美元利率定價合理性分析表」。
		var $L120M01IForm = $("#L120M01IForm");    //J-110-0155 修改e-loan授信管理系統簽報書之「利率定價合理性分析表」為「新臺幣、美元利率定價合理性及收益率分析表」
		var $LMS1205S04Form = $("#LMS1205S04Form");		
		var $LMS1205S05Form01 = $("#LMS1205S05Form01");
		var $LMS1205S05Form06 = $("#LMS1205S05Form06");
		var $LMS1205S05Form07 = $("#LMS1205S05Form07");
		var $CLS1205S05Form = $("#CLS1205S05Form");
		var $L120M01DForm03 = $("#L120M01DForm03");
		var $L120M01dForm04 = $("#L120M01dForm04");
		var $formL120m01e = $("#formL120m01e");
		var $L120M01DForm05 = $("#L120M01DForm05");
		var $LMS1205S07Form05 = $("#LMS1205S07Form05");
		
		//J-106-0085-001  Web e-Loan企金授信新增主要還款來源國等相關欄位
		var $LMS1205S21Form01 = $("#LMS1205S21Form01");
		
		// J-108-0243 微型企業
		var $LMS1205S23Form = $("#LMSS23Form");
				
		FormAction.open=true;
		if(responseJSON.page == "05"){
			if((responseJSON.docType == "2" && responseJSON.docCode == "1") || 
		   (responseJSON.docType == "1" && responseJSON.docCode == "1" 
			   && responseJSON.docKind == "1")){
			   	if(!$("#CLS1205S05Form").valid()){
					return;
				}
			}				
		}		
		$.ajax({
			type : "POST",
			handler : "lms1101formhandler",
			data : 
			{
				formAction : "saveAll",
				page : responseJSON.page,
				LMS1205S01Form : JSON.stringify($LMS1205S01Form.serializeData()),
				L120M01BForm : JSON.stringify($L120M01BForm.serializeData()),
				L120M01CForm : JSON.stringify($L120M01CForm.serializeData()),   //J-105-0264-001  WEB-ELOAN授信案件簽報書增訂「新臺幣、美元利率定價合理性分析表」。
				L120M01IForm : JSON.stringify($L120M01IForm.serializeData()),   //J-110-0155 修改e-loan授信管理系統簽報書之「利率定價合理性分析表」為「新臺幣、美元利率定價合理性及收益率分析表」
				LMS1205S04Form : JSON.stringify($LMS1205S04Form.serializeData()),				
				LMS1205S05Form01 : JSON.stringify($LMS1205S05Form01.serializeData()),
				LMS1205S05Form06 : JSON.stringify($LMS1205S05Form06.serializeData()),
				LMS1205S05Form07 : JSON.stringify($LMS1205S05Form07.serializeData()),
				CLS1205S05Form : JSON.stringify($CLS1205S05Form.serializeData()),
				L120M01DForm03 : JSON.stringify($L120M01DForm03.serializeData()),
				L120M01dForm04 : JSON.stringify($L120M01dForm04.serializeData()),
				formL120m01e : JSON.stringify($formL120m01e.serializeData()),
				L120M01DForm05 : JSON.stringify($L120M01DForm05.serializeData()),
				LMS1205S07Form05: JSON.stringify($LMS1205S07Form05.serializeData()),
				LMS1205S21Form01: JSON.stringify($LMS1205S21Form01.serializeData()),   //J-106-0085-001  Web e-Loan企金授信新增主要還款來源國等相關欄位
				LMS1205S23Form: JSON.stringify($LMS1205S23Form.serializeData()), // J-108-0243 微型企業
				itemDscr03 : getCkeditor("itemDscr03"),
				itemDscr05 : getCkeditor("itemDscr05"),
				itemDscr06 : $("#L120M01dForm06").find("#itemDscr06").val(),
				itemDscr01 : $("#LMS1205S05Form01").find("#itemDscr01").val(),//getCkeditor("itemDscr01"),
				itemDscr02 : $("#LMS1205S05Form04").find("#itemDscr02").val(),//getCkeditor("itemDscr02"),
				itemDscr07 : getCkeditor("itemDscr07"),
				itemDscr09 : getCkeditor("itemDscr09"),
				itemDscr0A : getCkeditor("itemDscr0A"),
				itemDscr0C : getCkeditor("itemDscr0C"),
				itemDscr0N : getCkeditor("itemDscr0N"), //J-105-0264-001  WEB-ELOAN授信案件簽報書增訂「新臺幣、美元利率定價合理性分析表」。
				itemDscrO : getCkeditor("itemDscrO"), //J-106-0085-001  Web e-Loan企金授信新增主要還款來源國等相關欄位
				longCaseFlag : $("#LMS1205S05Form04").find("input[name='longCaseFlag']:radio:checked").val(),
				longCaseDscr : $("#LMS1205S05Form04").find("#longCaseDscr option:selected").val(),
				ffbody : getCkeditor("ffbody"),								
				mainId : responseJSON.mainId,
				docCode : responseJSON.docCode,
				docType : responseJSON.docType,
				brnGroup : responseJSON.brnGroup,
				showMsg : showMsg
			},
			success:function(responseData){
				FormAction.open=false;
				ilog.debug(responseData);
				
				//控制文件類別顯示
	            // 所屬營運中心設定
				var $showBorrowData = $("#showBorrowData");
	            responseJSON["brnGroup"] = responseData.brnGroup;
	            
	            //J-GGG-XXXX
	            responseJSON["miniFlag"] = responseData.miniFlag;
	            responseJSON["caseType"] = responseData.caseType;
	            
	            //J-110-0336_05097_B1001 Web e-Loan授信異常通報增加通報類別與流程
	            responseJSON["caseTypeA"] = responseData.caseTypeA;
	            
	            //J-109-0459_05097_B1001 Web e-Loan簡化微型企業簽報書資僅為動用新台幣案件時得免執行制裁/管制名單掃描。
	            responseJSON["canPassAml"] = responseData.canPassAml;
	            
	            
	            if (responseData.areaTitle) {
	                $showBorrowData.find("#title1301").html(DOMPurify.sanitize(responseData.areaTitle));
	                if (lmsM01Json.docType == "1") {
	                    $showBorrowData.find("#title0a").show();
	                    $showBorrowData.find("#title0b").hide();
	                } else if (lmsM01Json.docType == "2") {
	                    $showBorrowData.find("#title0a").hide();
	                    $showBorrowData.find("#title0b").show();
	                }					
	                if (lmsM01Json.docCode == "3") {
	                    $showBorrowData.find("#title1y").show();
	                } else if (lmsM01Json.docCode == "4") {
	                    $showBorrowData.find("#title1x").show();
	                }
	            } else {
	                if (lmsM01Json.docType == "1") {
	                    $showBorrowData.find("#title0a").show();
	                    $showBorrowData.find("#title0b").hide();
	                } else if (lmsM01Json.docType == "2") {
	                    $showBorrowData.find("#title0a").hide();
	                    $showBorrowData.find("#title0b").show();
	                }
	                if (lmsM01Json.docKind == "2") {
	                    if (lmsM01Json.docCode == "1") {
	                        $showBorrowData.find("#title1").show();
	                        $showBorrowData.find("#title1a").hide();
	                        $showBorrowData.find("#title1b").hide();
	                    } else if (lmsM01Json.docCode == "2") {
	                        $showBorrowData.find("#title1a").show();
	                        $showBorrowData.find("#title1").hide();
	                        $showBorrowData.find("#title1b").hide();
	                    } else if (lmsM01Json.docCode == "3") {
	                        $showBorrowData.find("#title1b").show();
	                        $showBorrowData.find("#title1").hide();
	                        $showBorrowData.find("#title1a").hide();
	                        $showBorrowData.find("#title1y").show();
	                    } else if (lmsM01Json.docCode == "4") {
	                        // other.msg114=授權外案件簽報書(異常通報案件)
	                        $showBorrowData.find("#title1301").html(i18n.lmscommom["other.msg114"]);
	                        $showBorrowData.find("#title1x").show();
	                    }
	                } else if (lmsM01Json.docKind == "1") {
	                    $showBorrowData.find("#title1").hide();
	                    $showBorrowData.find("#title1c").hide();
	                    $showBorrowData.find("#title1d").hide();
	                    $showBorrowData.find("#title1e").hide();
	                    $showBorrowData.find("#title1f").hide();
	                    $showBorrowData.find("#title1g").hide();
	                    $showBorrowData.find("#title1h").hide();
	                    $showBorrowData.find("#title1i").hide();
	                    $showBorrowData.find("#title1j").hide();
	                    $showBorrowData.find("#title1k").hide();						
	                    if (responseJSON.authLvl == "2") {
	                        // 總行授權內
	                        if (lmsM01Json.docCode == "1") {
	                            $showBorrowData.find("#title1f").show();
	                            $showBorrowData.find("#title1g").hide();
	                            $showBorrowData.find("#title1h").hide();
	                        } else if (lmsM01Json.docCode == "2") {
	                            $showBorrowData.find("#title1g").show();
	                            $showBorrowData.find("#title1f").hide();
	                            $showBorrowData.find("#title1h").hide();
	                        } else if (lmsM01Json.docCode == "3") {
	                            $showBorrowData.find("#title1h").show();
	                            $showBorrowData.find("#title1f").hide();
	                            $showBorrowData.find("#title1g").hide();
	                            $showBorrowData.find("#title1y").show();
	                        } else if (lmsM01Json.docCode == "4") {
	                            // other.msg115=總行授權內案件簽報書(異常通報案件)
	                            $showBorrowData.find("#title1301").html(i18n.lmscommom["other.msg115"]);
	                            $showBorrowData.find("#title1x").show();
	                        }
	                    } else if (responseJSON.authLvl == "1") {
	                        // 分行授權內
	                        if (lmsM01Json.docCode == "1") {
	                            $showBorrowData.find("#title1c").show();
	                            $showBorrowData.find("#title1d").hide();
	                            $showBorrowData.find("#title1e").hide();
	                        } else if (lmsM01Json.docCode == "2") {
	                            $showBorrowData.find("#title1d").show();
	                            $showBorrowData.find("#title1c").hide();
	                            $showBorrowData.find("#title1e").hide();
	                        } else if (lmsM01Json.docCode == "3") {
	                            $showBorrowData.find("#title1e").show();
	                            $showBorrowData.find("#title1c").hide();
	                            $showBorrowData.find("#title1d").hide();
	                            $showBorrowData.find("#title1y").show();
	                        } else if (lmsM01Json.docCode == "4") {
	                            // other.msg116=分行授權內案件簽報書(異常通報案件)
	                            $showBorrowData.find("#title1301").html(i18n.lmscommom["other.msg116"]);
	                            $showBorrowData.find("#title1x").show();
	                        }
	                    } else if (responseJSON.authLvl == "3") {
	                        // 營運中心授權內
	                        if (responseJSON.docCode == "1") {
	                            $showBorrowData.find("#title1i").show();
	                            $showBorrowData.find("#title1j").hide();
	                            $showBorrowData.find("#title1k").hide();
	                        } else if (responseJSON.docCode == "2") {
	                            $showBorrowData.find("#title1j").show();
	                            $showBorrowData.find("#title1i").hide();
	                            $showBorrowData.find("#title1k").hide();
	                        } else if (responseJSON.docCode == "3") {
	                            $showBorrowData.find("#title1k").show();
	                            $showBorrowData.find("#title1j").hide();
	                            $showBorrowData.find("#title1i").hide();
	                            $showBorrowData.find("#title1y").show();
	                        } else if (lmsM01Json.docCode == "4") {
	                            // other.msg117=營運中心授權內案件簽報書(異常通報案件)
	                            $showBorrowData.find("#title1301").html(i18n.lmscommom["other.msg117"]);
	                            $showBorrowData.find("#title1x").show();
	                        }
	                    } else {
	                        // 預設顯示
	                        if (lmsM01Json.docCode == "1") {
	                            $showBorrowData.find("#title1c").show();
	                            $showBorrowData.find("#title1d").hide();
	                            $showBorrowData.find("#title1e").hide();
	                        } else if (lmsM01Json.docCode == "2") {
	                            $showBorrowData.find("#title1d").show();
	                            $showBorrowData.find("#title1c").hide();
	                            $showBorrowData.find("#title1e").hide();
	                        } else if (lmsM01Json.docCode == "3") {
	                            $showBorrowData.find("#title1e").show();
	                            $showBorrowData.find("#title1c").hide();
	                            $showBorrowData.find("#title1d").hide();
	                            $showBorrowData.find("#title1y").show();
	                        } else if (lmsM01Json.docCode == "4") {
	                            // other.msg116=分行授權內案件簽報書(異常通報案件)
	                            $showBorrowData.find("#title1301").html(i18n.lmscommom["other.msg116"]);
	                            $showBorrowData.find("#title1x").show();
	                        }
	                    }
	                }
	            }				
				//lmsM01Json.showHideTitle();
								
				if(responseJSON.page == "01"){
					// 儲存後的前端設定
					initS01aJson.afterSave(responseData);
					
					$LMS1205S01Form.find("#authLvl option").each(function(i){
						var $this = $(this);
						if($this.val() == responseData.LMS1205S01Form.authLvl){
							$this.attr("selected",true);
							if($this.val() == "3"){
								$LMS1205S01Form.find("#divArea3").show();
							}else{
								$LMS1205S01Form.find("#divArea3").hide();
							}
						}
					});
					$LMS1205S01Form.find("#areaBrId3 option").each(function(i){
						var $this = $(this);
						if($this.val() == responseData.LMS1205S01Form.areaBrId){
							$this.attr("selected",true);
						}
					});					
					setRequiredSave(false);
				}else{
					if ( lmsM01Json.hidePanelbyCaseType_miniFlag_passAml()  ) {
						$("#book20").hide();
					}else{
						$("#book20").show();
					}
				}
				
			
				
				//更新授信簽報書Grid內容
				CommonAPI.triggerOpener("gridview","reloadGrid");
				if(needPrint){
					// 如果saveAll檢核訊息有值，則不自動關閉儲存成功視窗
					if(responseData.hasSaveAllCheckMsg == false){
						$.thickbox.close();
					}
					printAction();	
				}
				if(needSend){
					$.thickbox.close();
					l1101m01Json.sendBoss();
				}				
			}
		});				
	},
	
	getRejtMsgAndSendBossNext : function(json, responseData){
		var rejtResult = getRejtMsgData();
		if (rejtResult != null) {
			CommonAPI.confirmMessage(rejtResult, function(b){
				if (b) {
					l1101m01Json.sendBossNext(json,responseData);
				}else{
					return;	
				}	
			})
		}else{
			l1101m01Json.sendBossNext(json,responseData);
		}
	},
	// 呈主管覆核 
	sendBoss : function(){
		$.ajax({
			handler : "lms1101formhandler",
			data : {
				formAction : "setBoss"
			},
			success : function(json){
				$(".boss").setItems({
					item:json.bossList,
					space: true
				});
				if(!json.noBossList2){
					$(".boss2").setItems({
						item:json.bossList2,
						space: true
					});						
				}				
				$("#sUnitManager option:eq(1)").attr("selected",true);
				$("#sUnitManager").attr("disabled",true);			
				//l120m01a.message01=是否呈主管覆核？
				CommonAPI.confirmMessage(i18n.lms1101m01["l120m01a.message01"],function(b){
					if(b){
					    // 檢核簽報書底下異常通報明細是否有被停權
					    $.ajax({
					        handler: "lms1101formhandler",
					        data: {
					            formAction: "checkIsStop",
								docKind : responseJSON.docKind,
								mainId : responseJSON.mainId,
								isNew : false
					        },
					        success: function(obj){
			            		//必要欄位檢核
			             		$.ajax({
			            			type : "POST",
			            			handler : "lms1101formhandler",
			            			data : 
			            			{
			            				formAction : "checkSend",
			            				mainId : responseJSON.mainId,
			            				page : responseJSON.page,
			            				docCode : responseJSON.docCode,
			            				docType : responseJSON.docType
			            			},
			            			success:function(responseData){
			            				// J-110-0493_11557_B1001
			            				// 檢查利害關係人授信額度合計是否達新台幣1億元以上
			            				// J-111-0488_05097_B1001 Web e-Loan企金授信簽報系統增加強分行經理權限對於合併關係企業彙總額度之管控
			            				if(responseData.haveRltOver && responseJSON.docKind == "1" && (responseData.unitType != "S" && responseData.unitType != "A" ) ){
			            					rtlOver100MillionBox(l1101m01Json.getRejtMsgAndSendBossNext, json, responseData, 'LMS1101', '');
			            				}else{
			            					l1101m01Json.getRejtMsgAndSendBossNext(json, responseData);
			            				}
			            				
										              				
			            			}
			            		});	
					        }
					    });					
					}
				});				
			}
		});				
	},
	
	//呈主管覆核 
	sendBossNext : function(json,responseData){
		var docStatus = responseData.docStatus;
			            				if(responseData.canSend){
			            					// 授管處和營運中心的呈主管不需額外挑主管與相關負責人員。
			            					if(responseData.unitType != "S" && responseData.unitType != "A"){
												// 國內加上查詢AO人員並設定程式碼
												if(json.AOVal != undefined && json.AOVal != null && json.AOVal != ""){
													CommonAPI.confirmMessage("系統搜尋到該客戶之帳戶管理員為"+json.AOName+"，是否需要變更？", function(b){
													if (b) {
															$("#bAOPerson").show();
															queryPrintSeq(l1101m01Json.selectBossbox);							
														}else{
															$("#AOPerson").setItems({
																item:json.bossListAO,
																space: false
															});
															$("#bAOPerson").hide();															
															queryPrintSeq(l1101m01Json.selectBossbox);
														}				
													});
												}else{
													queryPrintSeq(l1101m01Json.selectBossbox);													
												}
			            					}else{
												if(responseData.rejtMsgData != undefined && responseData.rejtMsgData != null && responseData.rejtMsgData != ''){
													CommonAPI.confirmMessage(responseData.rejtMsgData, function(b){
													if (b) {
						            						// 直接跑flow，簽章欄不用建，之後於"登錄說明及意見"時再建立
															if(docStatus == "03K"){
																// 會簽待覆核(04K)
																flowAction({flowAction:"sendHWait"});
															}else if(docStatus == "01K"){
																// 會簽後修改待覆核(02K)
																flowAction({flowAction:"sendAWait"});
															}else{
																flowAction({flowAction:"waitCheck"});
															}																	
														}				
													});
												}else{
				            						// 直接跑flow，簽章欄不用建，之後於"登錄說明及意見"時再建立
													if(docStatus == "03K"){
														// 會簽待覆核(04K)
														flowAction({flowAction:"sendHWait"});
													}else if(docStatus == "01K"){
														// 國內加上查詢AO人員並設定程式碼
														if(json.AOVal != undefined && json.AOVal != null && json.AOVal != ""){
															CommonAPI.confirmMessage("系統搜尋到該客戶之帳戶管理員為"+json.AOName+"，是否需要變更？", function(b){
															if (b) {
																	$("#bAOPerson").show();
																	queryPrintSeq(l1201m01Json.selectBossbox);							
																}else{
																	$("#AOPerson").setItems({
																		item:json.bossListAO,
																		space: false
																	});	
																	$("#bAOPerson").hide();																
																	queryPrintSeq(l1201m01Json.selectBossbox);
																}				
															});
														}else{
															queryPrintSeq(l1201m01Json.selectBossbox);
														}												
		/*
														// 會簽後修改待覆核(02K)
														flowAction({flowAction:"sendAWait"});
		*/
													}else{
														flowAction({flowAction:"waitCheck"});
													}
												}		
			            					}
				
			            				}else{
			            					//檢核不通過
			            					return CommonAPI.showErrorMessage(i18n.lms1101m01('l120m01a.error3',{'colName':responseData.tables}));
			            				}  
	},	
	
	//選擇主管box
	selectBossbox : function(){ 
		//檢核通過
		$("#selectBossBox").thickbox({     // 使用選取的內容進行彈窗
			//l120m01a.bt03=覆核
	       title : i18n.lms1101m01['l120m01a.bt03'],
	       width :500,
	       height : 300,
	       modal : true,
	       valign : "bottom",
			   align : "center",
			   i18n:i18n.def,
		   readOnly: false,
	       buttons: {
	                 "sure": function() {
	                		
	                	 var  selectBoss=  $("select[name^=boss]").map(function(){
	                         				return $(this).val();
	                     					}).toArray();
						
						// 驗證主管是否都有選擇到
	                    for (var i in selectBoss) {
	                        if (selectBoss[i] == "") {
	                            //l120m01a.error1=請選擇+ l120m01a.bossId=授信主管
	                            return CommonAPI.showErrorMessage(i18n.lms1101m01['l120m01a.error1'] + i18n.lms1101m01['l120m01a.bossId']);
	                        }
	                    }
						// 驗證單位授權主管是否有選擇到
						if($("#sManager option:selected").val() == ""){
							//l120m01a.error1=請選擇+ l120m01a.managerId=單位/授權主管
							return CommonAPI.showErrorMessage(i18n.lms1101m01['l120m01a.error1'] + i18n.lms1101m01['l120m01a.managerId']);
						}																        				                         					
	                     //驗證是否有重複的主管
	                    if(checkArrayRepeat(selectBoss)){
	                    	//主管人員名單重複請重新選擇
	                    	return CommonAPI.showErrorMessage(i18n.lms1101m01['l120m01a.message02']);
	                    }
						// 驗證單位主管是否有選擇到
						if($("#sUnitManager option:selected").val() == ""){
							//l120m01a.error1=請選擇+ l120m01a.managerId2=單位主管
							return CommonAPI.showErrorMessage(i18n.lms1101m01['l120m01a.error1'] + i18n.lms1101m01['l120m01a.managerId2']);
						}						
	                    //建立簽章欄
	             		$.ajax({
	            			type : "POST",
	            			handler : "lms1101formhandler",
	            			data : 
	            			{
	            				formAction : "saveL120m01f",
	            				mainId : responseJSON.mainId,
	            				selectBoss : selectBoss,
	            				manager:$("#sManager option:selected").val(),
	            				AOPerson:$("#AOPerson option:selected").val(),
								sUnitManager : $("#sUnitManager option:selected").val()
	            			},
	            			success:function(responseData){
	            				//alert(JSON.stringify(responseData));
								if (responseData.docStatus == "01K") {
									// 會簽後修改待覆核(02K)
									flowAction({flowAction:"sendAWait"});
								}else{
									flowAction({flowAction:"waitCheck"});
								}
	            			}
	        			});   				                        
	                    $.thickbox.close();  				                       
	                 },            
	                 
	                 "cancel": function() {
	                	 API.confirmMessage(i18n.def['flow.exit'], function(res){
	     					if(res){
	     						$.thickbox.close();
	     					}
	     		        });
	                 }
	               }
	        });	      
	}	
};
$(document).ready(function(){
	/*if(YAHOO.util.SWFDetect.getFlashVersion()=="0"){
		alert("無法載入Adobe Flash元件，請至e-Loan下載專區或Adobe官網下載安裝\nUnable to load Flash content. The YUI SWFStore Utility requires Flash Player 9.0.115 or higher\n\nhttp://www.adobe.com/go/getflash");
		window.close();
		return;
	}*/
	if(userInfo.userLocale != "zh_TW"){
		alert("國內分行案件僅能使用繁體中文語系開啟\nCases of domestic branches open only use Traditional Chinese language");
		window.close();
		return;
	}
	 
	var noOpenDoc = false;	
	if(responseJSON.ownBrId == userInfo.unitNo){
		noOpenDoc = true;
	}			
	var auth = (responseJSON ? responseJSON.Auth : {}); //權限
	
	setCloseConfirm(true);/*設定關閉此畫面時要詢問*/	
	
	//控制文件類別顯示
	//lmsM01Json.showHideTitle();

	//依照不同系統控制顯示頁籤
	//l1101m01Json.controlBook();

	//控制特殊分行(國外部、國金部、金控總部分行、財務部、財富管理處)按鈕顯示/隱藏 
	lmsM01Json.checkSpecitalBtn();
	
	//J-110-0336_05097_B1001 Web e-Loan授信異常通報增加通報類別與流程
	if (responseJSON.page == "01"){
    	lmsM01Json.loadCaseTypeItem(responseJSON.docCode); 
    }   

	//呈主管覆核 選授信主管人數
	$("#numPerson").change(function(){
		var $newBossSpan= $("#newBossSpan");
		
		//清空原本的
		$newBossSpan.empty();
	
		var newdiv = "";
		var val = parseInt($(this).val(),10);
		if(val > 1){
			for(var i=2,count=val+1; i<count ;i++){
				newdiv+="<div>"+i18n.lms1101m01['l120m01a.no']+i+
				i18n.lms1101m01['l120m01a.site']+"&nbsp;"+i18n.lms1101m01['l120m01a.bossId']+
				"&nbsp;&nbsp;&nbsp;<select id='boss" +
                i +
                "' name=boss" +
                i +
                " class='boss'/>&nbsp;" +
                "<span class='fg-buttonset'>" +
                "<span class='fg-child'>" +
                "<button b-id='boss" +
                i +
                "' class='ui-button ui-widget ui-state-default ui-corner-all ui-button-text-only fg-button' onclick='lmsM03Json.beforeOpen($(this).attr(\"b-id\"))' type='button' role='button' aria-disabled='false'>" +
                "<span class='ui-button-text'>" +
                "<span class='text-only'>常用主管</span>" +
                "</span></button></span></span></div>"
			}
			
			$newBossSpan.append(newdiv);
			var copyOption = $("#mainBoss").html();
			$("[name^=boss]").html(copyOption);
		}
	});
	$("#LMS1205S01Form").find("[name=areaChk]").click(function(i){
		var $LMS1205S01Form = $("#LMS1205S01Form");
		if($(this).val()=="2"){
			if (!lmsM01Json.isSpectialBank()) {
				$LMS1205S01Form.find("#divArea1").show();
			}
			$LMS1205S01Form.find("#divArea2").hide();
		}else if($(this).val()=="5"){
			//(108)第 3230 號
			if (!lmsM01Json.isSpectialBank()) {
				$LMS1205S01Form.find("#divArea1").show();
			}
			$LMS1205S01Form.find("#divArea2").hide();
			
			
			//(108)第 3230 號
			//把額度明細表的狀態變成編製中
			$.ajax({
    			type : "POST",
    			async: false,
    			handler : "lms1101formhandler",
    			data : 
    			{
    				formAction : "chgL140m01aNotApprove",
    				mainId : responseJSON.mainId 
    			},
    			success:function(responseData){
    				 $LMS1205S01Form.find("#divArea3").show();
    			},
    	        error: function(){
    	        	$LMS1205S01Form.find("[name=authLvl]").val(authLvlPreVal);
					$LMS1205S01Form.find("#divArea3").hide();
                }
			});   	 
			 
			
		}else if($(this).val()=="3"){
			$LMS1205S01Form.find("#divArea1").hide();
			$LMS1205S01Form.find("#divArea2").show();
		}else{
			$LMS1205S01Form.find("#divArea1").hide();
			$LMS1205S01Form.find("#divArea2").hide();
		}
	});
	var authLvlPreVal;
	$("#LMS1205S01Form").find("[name=authLvl]").focusin(function() {
	    authLvlPreVal = $(this).val(); 
	});
	$("#LMS1205S01Form").find("[name=authLvl]").change(function(i){
		var $LMS1205S01Form = $("#LMS1205S01Form");
		if($(this).val()=="3"){
			$LMS1205S01Form.find("#divArea3").show();
			//查詢是否有已核准額度明細表
     		$.ajax({
    			type : "POST",
    			async: false,
    			handler : "lms1101formhandler",
    			data : 
    			{
    				formAction : "chkL140m01aHasApprove",
    				mainId : responseJSON.mainId 
    			},
    			success:function(responseData){
    				var tmpAreaBrId3 = responseData.areaBrId3;
    				if(tmpAreaBrId3 != ""){
    					$LMS1205S01Form.find("#areaBrId3 option").each(function(i){
    						var $this = $(this);
    						if($this.val() == tmpAreaBrId3){
    							$this.attr("selected",true);
    						}
    					});		
    				}
    				
    				if (responseData.hasApprove == "Y") {
    					//other.msg201=調整為營運中心授權內，本案非編製中額度明細表會自動回復成編製中，是否繼續？
    					CommonAPI.confirmMessage(i18n.lmscommom["other.msg201"], function(b){
						    if (b) {
								  $.ajax({
						    			type : "POST",
						    			async: false,
						    			handler : "lms1101formhandler",
						    			data : 
						    			{
						    				formAction : "chgL140m01aNotApprove",
						    				mainId : responseJSON.mainId 
						    			},
						    			success:function(responseData){
						    				 $LMS1205S01Form.find("#divArea3").show();
						    			},
						    	        error: function(){
						    	        	$LMS1205S01Form.find("[name=authLvl]").val(authLvlPreVal);
											$LMS1205S01Form.find("#divArea3").hide();
						                }
									});   	
							}else{
								$LMS1205S01Form.find("[name=authLvl]").val(authLvlPreVal);
								$LMS1205S01Form.find("#divArea3").hide();
							}				
						});
					}else{
						 $LMS1205S01Form.find("#divArea3").show();
					}	
    			}	
			});   		
		}else{
			$LMS1205S01Form.find("#divArea3").hide();
		}
		$("#readDocLog").focus();
	});	
	
	//J-109-0077_05097_B1021 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
    $("#LMS1205S01Form").find("[name='miniFlag']").click(function(){
		
		var LMS1205S01Form = $("#LMS1205S01Form");
        var miniFlag = $(this).val();
        
		if (miniFlag == "Y") {
			LMS1205S01Form.find(".showCaseType").show();
        }else {
        	LMS1205S01Form.find(".showCaseType").hide();
        	LMS1205S01Form.find("#caseType").val('');
        }
		
    });
    
    
  //J-110-0336_05097_B1001 Web e-Loan授信異常通報增加通報類別與流程
    $("#LMS1205S01Form").find("[name='caseType']").change(function(){
    	setUI();
    });
    
    
	if($("#purpose3").attr("checked")){
		$("#chkboxA-2-3c1_edit").show();
	}else{
		$("#chkboxA-2-3c1_edit").hide();
		$("#purposeOth").val("");
	}	
	if($("#resource3").attr("checked")){
		$("#chkboxA-2-3c2_edit").show();
	}else{
		$("#chkboxA-2-3c2_edit").hide();
		$("#resourceOth").val("");
	}
	
	if (responseJSON.page != "01"){
		var obj = CommonAPI.loadCombos(["lms1205m01_caseLvl"]);
	    $("#formCaseLvl").find("#caseLvl").setItems({
	        item: obj.lms1205m01_caseLvl,
	        format: "{value} - {key}",
	        space: false
	    });
	}


	// 基本資訊、案由、補充說明、主管批示已元件化(不須重新讀取)
	if (responseJSON.page != "01" && responseJSON.page != "04" && responseJSON.page != "09" && responseJSON.page != "18") {		
		$.form.init({
	        formHandler: "lms1101formhandler",
	        formPostData:{
	        	formAction : "queryLms1205m01",
	        	oid : responseJSON.oid,
	        	page : responseJSON.page,
	        	mainId : responseJSON.mainId,
	        	docType : responseJSON.docType,
	        	docCode : responseJSON.docCode,
	        	docKind : responseJSON.docKind,
				docStatus : responseJSON.mainDocStatus,
	        	//noOpenDoc : noOpenDoc,
				itemDscr03 : "",
				itemDscr05 : "",
				ffbody : "",
				brnGroup : responseJSON.brnGroup
	        },
			loadSuccess:function(jsonInit){
				
				// Miller 2012-06-28 用來取代Grid前端隱藏欄位傳到後端的值
				responseJSON["mainDocStatus"] = jsonInit._mainDocStatus;
				responseJSON["mainId"] = jsonInit._mainId;
				responseJSON["mainOid"] = jsonInit._mainOid;
				responseJSON["docType"] = jsonInit._docType;
				responseJSON["docCode"] = jsonInit._docCode;
				responseJSON["docKind"] = jsonInit._docKind;
				responseJSON["docURL"] = jsonInit._docURL;
				responseJSON["ownBrId"] = jsonInit._ownBrId;
				responseJSON["authLvl"] = jsonInit._authLvl;
				responseJSON["areaDocstatus"] = jsonInit._areaDocstatus;
				responseJSON["areaBrId"] = jsonInit._areaBrId;
				responseJSON["oid"] = jsonInit._oid;
				responseJSON["hqMeetFlag"] = jsonInit._hqMeetFlag;
				responseJSON["typCd"] = jsonInit._typCd;
				responseJSON["miniFlag"] = jsonInit._miniFlag;
				//J-109-0077_05097_B1021 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
				responseJSON["caseType"] = jsonInit._caseType;
				//J-110-0336_05097_B1001 Web e-Loan授信異常通報增加通報類別與流程
				responseJSON["caseTypeA"] = jsonInit._caseTypeA;
				//J-109-0459_05097_B1001 Web e-Loan簡化微型企業簽報書資僅為動用新台幣案件時得免執行制裁/管制名單掃描。
				responseJSON["canPassAml"] = jsonInit._canPassAml;
				
				setTimeout(function(){
				for(o in jsonInit.hideBook){
					$(jsonInit.hideBook[o]).hide();
				}
				$(".tabs-warp").scrollToTab();					
				//控制文件類別顯示
	            // 所屬營運中心設定
				var $showBorrowData = $("#showBorrowData");
	            responseJSON["brnGroup"] = jsonInit.brnGroup;
	            if (jsonInit.areaTitle) {
	                $showBorrowData.find("#title1301").html(jsonInit.areaTitle);
	                if (lmsM01Json.docType == "1") {
	                    $showBorrowData.find("#title0a").show();
	                    $showBorrowData.find("#title0b").hide();
	                } else if (lmsM01Json.docType == "2") {
	                    $showBorrowData.find("#title0a").hide();
	                    $showBorrowData.find("#title0b").show();
	                }					
	                if (lmsM01Json.docCode == "3") {
	                    $showBorrowData.find("#title1y").show();
	                } else if (lmsM01Json.docCode == "4") {
	                    $showBorrowData.find("#title1x").show();
	                }
	            } else {
	                if (lmsM01Json.docType == "1") {
	                    $showBorrowData.find("#title0a").show();
	                    $showBorrowData.find("#title0b").hide();
	                } else if (lmsM01Json.docType == "2") {
	                    $showBorrowData.find("#title0a").hide();
	                    $showBorrowData.find("#title0b").show();
	                }
	                if (lmsM01Json.docKind == "2") {
	                    if (lmsM01Json.docCode == "1") {
	                        $showBorrowData.find("#title1").show();
	                        $showBorrowData.find("#title1a").hide();
	                        $showBorrowData.find("#title1b").hide();
	                    } else if (lmsM01Json.docCode == "2") {
	                        $showBorrowData.find("#title1a").show();
	                        $showBorrowData.find("#title1").hide();
	                        $showBorrowData.find("#title1b").hide();
	                    } else if (lmsM01Json.docCode == "3") {
	                        $showBorrowData.find("#title1b").show();
	                        $showBorrowData.find("#title1").hide();
	                        $showBorrowData.find("#title1a").hide();
	                        $showBorrowData.find("#title1y").show();
	                    } else if (lmsM01Json.docCode == "4") {
	                        // other.msg114=授權外案件簽報書(異常通報案件)
	                        $showBorrowData.find("#title1301").html(i18n.lmscommom["other.msg114"]);
	                        $showBorrowData.find("#title1x").show();
	                    }
	                } else if (lmsM01Json.docKind == "1") {
	                    $showBorrowData.find("#title1").hide();
	                    $showBorrowData.find("#title1c").hide();
	                    $showBorrowData.find("#title1d").hide();
	                    $showBorrowData.find("#title1e").hide();
	                    $showBorrowData.find("#title1f").hide();
	                    $showBorrowData.find("#title1g").hide();
	                    $showBorrowData.find("#title1h").hide();
	                    $showBorrowData.find("#title1i").hide();
	                    $showBorrowData.find("#title1j").hide();
	                    $showBorrowData.find("#title1k").hide();						
	                    if (responseJSON.authLvl == "2") {
	                        // 總行授權內
	                        if (lmsM01Json.docCode == "1") {
	                            $showBorrowData.find("#title1f").show();
	                            $showBorrowData.find("#title1g").hide();
	                            $showBorrowData.find("#title1h").hide();
	                        } else if (lmsM01Json.docCode == "2") {
	                            $showBorrowData.find("#title1g").show();
	                            $showBorrowData.find("#title1f").hide();
	                            $showBorrowData.find("#title1h").hide();
	                        } else if (lmsM01Json.docCode == "3") {
	                            $showBorrowData.find("#title1h").show();
	                            $showBorrowData.find("#title1f").hide();
	                            $showBorrowData.find("#title1g").hide();
	                            $showBorrowData.find("#title1y").show();
	                        } else if (lmsM01Json.docCode == "4") {
	                            // other.msg115=總行授權內案件簽報書(異常通報案件)
	                            $showBorrowData.find("#title1301").html(i18n.lmscommom["other.msg115"]);
	                            $showBorrowData.find("#title1x").show();
	                        }
	                    } else if (responseJSON.authLvl == "1") {
	                        // 分行授權內
	                        if (lmsM01Json.docCode == "1") {
	                            $showBorrowData.find("#title1c").show();
	                            $showBorrowData.find("#title1d").hide();
	                            $showBorrowData.find("#title1e").hide();
	                        } else if (lmsM01Json.docCode == "2") {
	                            $showBorrowData.find("#title1d").show();
	                            $showBorrowData.find("#title1c").hide();
	                            $showBorrowData.find("#title1e").hide();
	                        } else if (lmsM01Json.docCode == "3") {
	                            $showBorrowData.find("#title1e").show();
	                            $showBorrowData.find("#title1c").hide();
	                            $showBorrowData.find("#title1d").hide();
	                            $showBorrowData.find("#title1y").show();
	                        } else if (lmsM01Json.docCode == "4") {
	                            // other.msg116=分行授權內案件簽報書(異常通報案件)
	                            $showBorrowData.find("#title1301").html(i18n.lmscommom["other.msg116"]);
	                            $showBorrowData.find("#title1x").show();
	                        }
	                    } else if (responseJSON.authLvl == "3") {
	                        // 營運中心授權內
	                        if (responseJSON.docCode == "1") {
	                            $showBorrowData.find("#title1i").show();
	                            $showBorrowData.find("#title1j").hide();
	                            $showBorrowData.find("#title1k").hide();
	                        } else if (responseJSON.docCode == "2") {
	                            $showBorrowData.find("#title1j").show();
	                            $showBorrowData.find("#title1i").hide();
	                            $showBorrowData.find("#title1k").hide();
	                        } else if (responseJSON.docCode == "3") {
	                            $showBorrowData.find("#title1k").show();
	                            $showBorrowData.find("#title1j").hide();
	                            $showBorrowData.find("#title1i").hide();
	                            $showBorrowData.find("#title1y").show();
	                        } else if (lmsM01Json.docCode == "4") {
	                            // other.msg117=營運中心授權內案件簽報書(異常通報案件)
	                            $showBorrowData.find("#title1301").html(i18n.lmscommom["other.msg117"]);
	                            $showBorrowData.find("#title1x").show();
	                        }
	                    } else {
	                        // 預設顯示
	                        if (lmsM01Json.docCode == "1") {
	                            $showBorrowData.find("#title1c").show();
	                            $showBorrowData.find("#title1d").hide();
	                            $showBorrowData.find("#title1e").hide();
	                        } else if (lmsM01Json.docCode == "2") {
	                            $showBorrowData.find("#title1d").show();
	                            $showBorrowData.find("#title1c").hide();
	                            $showBorrowData.find("#title1e").hide();
	                        } else if (lmsM01Json.docCode == "3") {
	                            $showBorrowData.find("#title1e").show();
	                            $showBorrowData.find("#title1c").hide();
	                            $showBorrowData.find("#title1d").hide();
	                            $showBorrowData.find("#title1y").show();
	                        } else if (lmsM01Json.docCode == "4") {
	                            // other.msg116=分行授權內案件簽報書(異常通報案件)
	                            $showBorrowData.find("#title1301").html(i18n.lmscommom["other.msg116"]);
	                            $showBorrowData.find("#title1x").show();
	                        }
	                    }
	                }
	            }				
				//lmsM01Json.showHideTitle();

				//依照不同文件狀態控制唯讀
				lmsM01Json.setReadOnly(auth);

				//新增送審查時補充說明按鈕以及授管處補充說明Ckeditor隱藏
				if(jsonInit.showBorrowData._areaChk == "3"){
					//建霖說：簽報書授管處審查時，開啟補充意見登錄畫面一律顯示
					//$(".hareaChk").hide();				
				}else{
					//建霖說：簽報書授管處審查時，開啟補充意見登錄畫面一律顯示
					//$(".hareaChk").show();
				}			
                
				if(responseJSON.page == "03"){
					$("#L120M01BForm").setData(jsonInit.L120M01BForm,false);	
					//J-105-0264-001  WEB-ELOAN授信案件簽報書增訂「新臺幣、美元利率定價合理性分析表」。
					setCkeditor2("itemDscr0N",jsonInit.L120M01CForm.itemDscr0N);
					//J-110-0155 修改e-loan授信管理系統簽報書之「利率定價合理性分析表」為「新臺幣、美元利率定價合理性及收益率分析表」
					$("#L120M01IForm").setData(jsonInit.L120M01IForm,false);
				}else if(responseJSON.page == "05"){
					var $LMS1205S05Form01 = $("#LMS1205S05Form01");
					var $LMS1205S05Form02 = $("#LMS1205S05Form02");
					var $LMS1205S05Form03 = $("#LMS1205S05Form03");
					var $LMS1205S05Form05 = $("#LMS1205S05Form05");
					var $LMS1205S05Form06 = $("#LMS1205S05Form06");
					var $LMS1205S05Form07 = $("#LMS1205S05Form07");
					if(responseJSON.docType == "1" && responseJSON.docCode == "1" && responseJSON.docKind == "2"){
						$LMS1205S05Form01.reset();
						$LMS1205S05Form02.reset();
						$LMS1205S05Form03.reset();
						$LMS1205S05Form05.reset();
						$LMS1205S05Form06.reset();
						$LMS1205S05Form07.reset();
						$LMS1205S05Form01.setData(jsonInit.LMS1205S05Form01,false);
						$LMS1205S05Form02.setData(jsonInit.LMS1205S05Form02,false);
						$LMS1205S05Form03.setData(jsonInit.LMS1205S05Form03,false);
						$("#LMS1205S05Form04").setData(jsonInit.LMS1205S05Form04,false);
						$LMS1205S05Form05.setData(jsonInit.LMS1205S05Form05,false);
						$LMS1205S05Form02.readOnlyChilds(true);
						$LMS1205S05Form03.readOnlyChilds(true);
						$LMS1205S05Form05.readOnlyChilds(true);					
						$LMS1205S05Form06.setData(jsonInit.LMS1205S05Form06,false);
						if(jsonInit.LMS1205S05Form06.grpFlag == "Y"){
							$LMS1205S05Form06.find("input[name='grpFlag']:radio:eq(0)").attr("checked",true);
							$LMS1205S05Form06.find('#groupButton').show();
							$LMS1205S05Form06.find('#groupContext').show();
						}else{
							$LMS1205S05Form06.find("input[name='grpFlag']:radio:eq(0)").attr("checked",false);
							$LMS1205S05Form06.find('#groupButton').hide();
							$LMS1205S05Form06.find('#groupContext').hide();
						}
						$LMS1205S05Form07.setData(jsonInit.LMS1205S05Form07,false);
						if(jsonInit.LMS1205S05Form07.rltFlag == "Y"){
							$LMS1205S05Form07.find("input[name='rltFlag']:radio:eq(0)").attr("checked",true);
							$LMS1205S05Form07.find('#relButton').show();
							$LMS1205S05Form07.find('#relContext').show();
						}else{
							$LMS1205S05Form07.find("input[name='rltFlag']:radio:eq(0)").attr("checked",false);
							$LMS1205S05Form07.find('#relButton').hide();
							$LMS1205S05Form07.find('#relContext').hide();
						}
					}else if((responseJSON.docType == "2" && responseJSON.docCode == "1") || 
							(responseJSON.docType == "1" && responseJSON.docCode == "1" && responseJSON.docKind == "1")){
						var $CLS1205S05Form = $("#CLS1205S05Form");
						$CLS1205S05Form.setData(jsonInit);
						for(var o in jsonInit.CLS1205S05Form.purposes){
							$CLS1205S05Form.find("input[name='purpose']").each(function(i){
								var $this = $(this);
								if($this.val() == jsonInit.CLS1205S05Form.purposes[o]){
									$this.attr("checked",true);
									if($this.val()=="3"){
										$("#chkboxA-2-3c1_edit").show();
									}
								}
							});
						}
						for(var p in jsonInit.CLS1205S05Form.resources){
							$CLS1205S05Form.find("input[name='resource']").each(function(j){
								var $this = $(this);
								if($this.val() == jsonInit.CLS1205S05Form.resources[p]){
									$this.attr("checked",true);
									if($this.val()=="3"){
										$("#chkboxA-2-3c2_edit").show();
									}
								}
							});
						}
					}else if(responseJSON.docCode == "2" || responseJSON.docCode == "3"){
						setCkeditor2("itemDscr03",jsonInit.L120M01DForm03.itemDscr03);
					}
					if (responseJSON.readOnly != undefined &&
					responseJSON.readOnly != null &&
					responseJSON.readOnly != '') {
						if (responseJSON.readOnly.toString() == "true") {
							setReadOnly2();
						}					
					}					
				}else if(responseJSON.page == "06"){
					setCkeditor2("itemDscr03",jsonInit.L120M01DForm03.itemDscr03);
				}
				else if(responseJSON.page == "08"){
					var $formL120m01e = $("#formL120m01e");
					var $docDscr1 = $("#docDscr1");
					var $docDscr2 = $("#docDscr2");
					if(jsonInit.formL120m01e.cesCase == "Y"){
						$formL120m01e.find("#cesCase:checkbox").attr("checked",true);
					}else{
						$formL120m01e.find("#cesCase:checkbox").attr("checked",false);
					}
					$formL120m01e.find("#docDscr1").html(jsonInit.formL120m01e.docDscr1);
					if(jsonInit.formL120m01e.docDscr1 != "" 
					&& jsonInit.formL120m01e.docDscr1 != undefined 
					&& jsonInit.formL120m01e.docDscr1 != null){
						$("#docDscr1 a").attr({"href":"#"});	
					}else{
						$docDscr1.html("");
						$docDscr1.val("");
					}
					$formL120m01e.find("#docDscr2").html(jsonInit.formL120m01e.docDscr2);
					if(jsonInit.formL120m01e.docDscr2 != "" 
					&& jsonInit.formL120m01e.docDscr2 != undefined 
					&& jsonInit.formL120m01e.docDscr2 != null){
						$("#docDscr2 a").attr({"href":"#"});	
					}else{
						$docDscr2.html("");
						$docDscr2.val("");
					}
					initS08dJson.afterQuery(jsonInit);
					$formL120m01e.find("#docDscr3").html(jsonInit.formL120m01e.docDscr3);
					$formL120m01e.find("#docDscr4").html(jsonInit.formL120m01e.docDscr4);
					$formL120m01e.find("#docDscr5").html(jsonInit.formL120m01e.docDscr5);
					if(jsonInit.formL120m01e.docDscr5 != "" 
					&& jsonInit.formL120m01e.docDscr5 != undefined 
					&& jsonInit.formL120m01e.docDscr5 != null){
						$("#docDscr5 a").attr({"href":"#"});	
					}else{
						var $docDscr5 = $("#docDscr5");
						$docDscr5.html("");
						$docDscr5.val("");
					}
					$("#formL120m01e").find("#docDscr6").html(jsonInit.formL120m01e.docDscr6);
					if(jsonInit.formL120m01e.docDscr6 != "" 
					&& jsonInit.formL120m01e.docDscr6 != undefined 
					&& jsonInit.formL120m01e.docDscr6 != null){
						$("#docDscr6 a").attr({"href":"#"});	
					}else{
						var $docDscr6 = $("#docDscr6");
						$docDscr6.html("");
						$docDscr6.val("");
					}										
					$("#formL120m01e").find("#docDscr7").html(jsonInit.formL120m01e.docDscr7);
					if (responseJSON.readOnly != undefined &&
					responseJSON.readOnly != null &&
					responseJSON.readOnly != '') {
						if (responseJSON.readOnly.toString() == "true") {
							setReadOnly2();
						}					
					}					
				}else if(responseJSON.page == "10"){
					var $formfile = $("#formfile");
					if (responseJSON.readOnly != undefined &&
					responseJSON.readOnly != null &&
					responseJSON.readOnly != '') {
						if (responseJSON.readOnly.toString() == "true") {
							setReadOnly2();
						}					
					}				
					if(jsonInit.formfile.sfilehide1a){
						$formfile.find("#filehide1a").hide();
					}else{
						$formfile.find("#filehide1a").show();
					}
					if(jsonInit.formfile.sfilehide1b){
						$formfile.find("#filehide1b").hide();
					}else{
						$formfile.find("#filehide1b").show();
						if(auth.Modify){
							$formfile.find("#filehide1b button").show();	
						}					
					}
					if (jsonInit.formfile.sfilehide1a && jsonInit.formfile.sfilehide1b) {
						$formfile.find("#filehide1").hide();
					}				
					if(jsonInit.formfile.sfilehide2a){
						$formfile.find("#filehide2a").hide();
					}else{
						$formfile.find("#filehide2a").show();
					}
					if(jsonInit.formfile.sfilehide2b){
						$formfile.find("#filehide2b").hide();
					}else{
						$formfile.find("#filehide2b").show();
						if(auth.Modify){
							$formfile.find("#filehide2b button").show();	
						}					
					}
					if (jsonInit.formfile.sfilehide2a && jsonInit.formfile.sfilehide2b) {
						$formfile.find("#filehide2").hide();
					}				
				}else if(responseJSON.page == "14"){
					setCkeditor2("itemDscr09",jsonInit.L120M01aForm14.itemDscr09);
				}else if(responseJSON.page == "15"){
					setCkeditor2("itemDscr07", jsonInit.L120M01aForm15.itemDscr07);
					setCkeditor2("itemDscr08", jsonInit.L120M01aForm15.itemDscr08);
				}else if(responseJSON.page == "17"){
					var $L120M01aForm17 = $("#L120M01aForm17");
					if (jsonInit.L120M01aForm17.showBtn) {
						$L120M01aForm17.find("#showBtnA").show();
					}else{
						$L120M01aForm17.find("#showBtnA").hide();
					}
				}else if(responseJSON.page == "18"){
					if (responseJSON.readOnly != undefined &&
					responseJSON.readOnly != null &&
					responseJSON.readOnly != '') {
						if (responseJSON.readOnly.toString() == "true") {
							setReadOnly2();
						}					
					}					
				}else if(responseJSON.page == "20"){
					//J-106-0029-001 新增洗錢防制頁籤
					var $LMS1205S20Form01 = $("#LMS1205S20Form01"); 
					$LMS1205S20Form01.setData(jsonInit.LMS1205S20Form01,false);
				}else if(responseJSON.page == "21"){
					//J-106-0085-001 J-106-0085-001  Web e-Loan企金授信新增主要還款來源國等相關欄位
					var $LMS1205S21Form01 = $("#LMS1205S21Form01"); 
					$LMS1205S21Form01.setData(jsonInit.LMS1205S21Form01,false);	
				}else if(responseJSON.page == "23"){
					// J-108-0243 微型企業
					var $LMSS23Form = $("#LMSS23Form"); 
					$LMSS23Form.setData(jsonInit.LMSS23Form,false);	
				}
				if(responseJSON.page != "01"){
					var $showBorrowData = $("#showBorrowData");
					$showBorrowData.reset();
					$showBorrowData.setData(jsonInit.showBorrowData,false);	
				}
				initDfd.resolve(auth);
			  },100);			
			}
	    });
	}else{
		//控制文件類別顯示
		//lmsM01Json.showHideTitle();

		//依照不同文件狀態控制唯讀
		lmsM01Json.setReadOnly(auth);		
	}	
	var btn = $("#buttonPanel");
	btn.find("#btnSave").click(function(showMsg){
		l1101m01Json.saveAll(showMsg, false, false);
	}).end().find("#btnAccept").click(function(){
	}).end().find("#btnSendCase").click(function(){
		if(responseJSON.hqMeetFlag == "1"){
			$("#sendTo").find("#sendTo1").hide();
			$("#sendTo").find("#sendTo2").show();
			$("#sendTo").find("#sendTo3").show();
			//J-113-0337  配合本行將於第18屆董事會設置審計委員會替代監查人，新增「審計委員會」
			$("#sendTo").find("#sendTo4").show();
		}else if(responseJSON.hqMeetFlag == "2"){
			$("#sendTo").find("#sendTo1").show();
			$("#sendTo").find("#sendTo2").hide();
			$("#sendTo").find("#sendTo3").show();
			//J-113-0337  配合本行將於第18屆董事會設置審計委員會替代監查人，新增「審計委員會」
			$("#sendTo").find("#sendTo4").show();
		}else if(responseJSON.hqMeetFlag == "3"){
			$("#sendTo").find("#sendTo1").show();
			$("#sendTo").find("#sendTo2").show();
			$("#sendTo").find("#sendTo3").hide();
			//J-113-0337  配合本行將於第18屆董事會設置審計委員會替代監查人，新增「審計委員會」
			$("#sendTo").find("#sendTo4").show();
		}else if(responseJSON.hqMeetFlag == "4"){
			//J-113-0337  配合本行將於第18屆董事會設置審計委員會替代監查人，新增「審計委員會」
			$("#sendTo").find("#sendTo1").show();
			$("#sendTo").find("#sendTo2").show();
			$("#sendTo").find("#sendTo3").show();
			$("#sendTo").find("#sendTo4").hide();
		}
		$("#sendTo").thickbox({
			//l120m01a.btnSendTo=提會
	       title : i18n.lms1101m01['l120m01a.btnSendTo'],
	       width :250,
	       height : 150,
	       modal : true,
	       valign : "bottom",
		   align : "center",
		   i18n:i18n.def,
		   readOnly: false,
	       buttons: {
	                 "sure": function() {
	                	 if($("input[name='hqMeetFlag']:radio:checked").val() == undefined){
	                		 //l120m01a.error1=請選擇
	                		 return CommonAPI.showErrorMessage(i18n.lms1101m01['l120m01a.error1']); 
	                	 }else{
	 	              		$.ajax({
		            			type : "POST",
		            			handler : "lms1101formhandler",
		            			data : 
		            			{
		            				formAction : "sendTo",
		            				mainId : responseJSON.mainId,
		            				hqMeetFlag : $("input[name='hqMeetFlag']:radio:checked").val()
		            			},
		            			success:function(responseData){
		            				//alert(JSON.stringify(responseData));
		            				CommonAPI.triggerOpener("gridview","reloadGrid");
		            			}
		        			});
		                	$.thickbox.close();	                		 
	                	 }
	                 },            
	                 "cancel": function() {
	                	 API.confirmMessage(i18n.def['flow.exit'], function(res){
	     					if(res){
	     						$.thickbox.close();
	     					}
	     		        });
	                 }
	               }
	        });		
	}).end().find("#btnLogeIN").click(function(){
		if(responseJSON.areaDocstatus == "LWC"){
     		$.ajax({
    			type : "POST",
    			handler : "lms1101formhandler",
    			data : 
    			{
    				formAction : "querySignContent",
    				mainId : responseJSON.mainId
    			},
    			success:function(responseData){
    				//alert(JSON.stringify(responseData));
					setCkeditor2("tItemDscr09",responseData.L120M01aForm14.tItemDscr09);
    				//$("#tItemDscr09").val(responseData.L120M01aForm14.tItemDscr09);
    				signThickBox();
    			}
			});
		}else if ((responseJSON.mainDocStatus == "L1H" ||
                responseJSON.mainDocStatus == "L2H" ||
                responseJSON.mainDocStatus == "L3H" ||
                responseJSON.mainDocStatus == "L4H" ||
                responseJSON.mainDocStatus == "L5H") &&
                userInfo.unitNo == "918") {
                    $.ajax({
                        type: "POST",
                        handler: "lms1301formhandler",
                        data: {
                            formAction: "querySignContent2",
                            mainId: responseJSON.mainId
                        },
                        success: function(responseData){
                            //setCkeditor2("tItemDscr0A",responseData.L120M01aForm13.itemDscr0A);
                            //setCkeditor2("tItemDscr0B",responseData.L120M01aForm13.itemDscr0B);
                            $("#tItemDscr0A").val(responseData.L120M01aForm13.tItemDscr0A);
                            $("#tItemDscr0B").val(responseData.L120M01aForm13.tItemDscr0B);
                            $("#_tItemDscr0A").val(responseData.L120M01aForm13.tItemDscr0A);
                            $("#_tItemDscr0B").val(responseData.L120M01aForm13.tItemDscr0B);
                            $.ajax({
                                type: "POST",
                                handler: "lms1301formhandler",
                                data: {
                                    formAction: "querySignContent2a",
                                    mainId: responseJSON.mainId
                                },
                                success: function(responseData){
                                    //alert(JSON.stringify(responseData));
                                    $("#htItemDscr0C").val(responseData.L120M01aForm12.htItemDscr0C);
                                    $("#_htItemDscr0C").val(responseData.L120M01aForm12.htItemDscr0C);
                                    signThickBox2_1();
                                }
                            });
                        }
                    });
                 	
		}else if(responseJSON.mainDocStatus == "L1H"
			    || responseJSON.mainDocStatus == "L2H"
				|| responseJSON.mainDocStatus == "L3H"
				|| responseJSON.mainDocStatus == "L4H"
				|| responseJSON.mainDocStatus == "L5H"){
     		$.ajax({
    			type : "POST",
    			handler : "lms1101formhandler",
    			data : 
    			{
    				formAction : "querySignContent2",
    				mainId : responseJSON.mainId
    			},
    			success:function(responseData){
    				//alert(JSON.stringify(responseData));
					setCkeditor2("tItemDscr0A",responseData.L120M01aForm13.tItemDscr0A);
					setCkeditor2("tItemDscr0B",responseData.L120M01aForm13.tItemDscr0B);
    				//$("#tItemDscr0A").val(responseData.L120M01aForm13.tItemDscr0A);
    				//$("#tItemDscr0B").val(responseData.L120M01aForm13.tItemDscr0B);
    				signThickBox2();
    			}
			});			
		}else if(responseJSON.mainDocStatus == "L1C"  
		&& (userInfo.unitNo == "920" || userInfo.unitNo == "931" || userInfo.unitNo == "922"
		|| userInfo.unitNo == "932" || userInfo.unitNo == "933" || userInfo.unitNo == "934"
		|| userInfo.unitNo == "935")){
     		$.ajax({
    			type : "POST",
    			handler : "lms1101formhandler",
    			data : 
    			{
    				formAction : "querySignContent3",
    				mainId : responseJSON.mainId
    			},
    			success:function(responseData){
    				//alert(JSON.stringify(responseData));
					//setCkeditor2("tItemDscr07",responseData.L120M01aForm15.tItemDscr07);
					//setCkeditor2("tItemDscr08",responseData.L120M01aForm15.tItemDscr08);
    				$("#tItemDscr07").val(responseData.L120M01aForm15.tItemDscr07);
    				$("#tItemDscr08").val(responseData.L120M01aForm15.tItemDscr08);
    				signThickBox3();
    			}
			});			
		}
		//signContent2
	}).end().find("#btnCheckArea").click(function(){
		
		//區域中心覆核
		btnCheckActionArea();
	}).end().find("#btnCheck").click(function(){
		
		//覆核 :提供退回或呈下一關
		btnCheckAction();
	}).end().find("#btnCaseToChange").click(function(){
		// 案件改分派
		$.ajax({
			handler : "lms1101formhandler",
			data : {
				formAction : "setBoss",
				changePeople : true
			},
			success : function(json){
				$("#hqAppraiser").setItems({
					item:json.hqAppraiser,
					space: false
				});
				$("#selectHqAppraiser").thickbox({     // 使用選取的內容進行彈窗
		           title : i18n.lms1101m01['l120m01a.error1a'],
		           width :200,
		           height : 100,
		           modal : true,
		           valign : "bottom",
		   		   align : "center",
		   		   i18n:i18n.def,
				   readOnly: false,
		           buttons: {
		                     "sure": function() {
		                  		$.ajax({
			            			type : "POST",
			            			handler : "lms1101formhandler",
			            			data : 
			            			{
			            				formAction : "saveHqAppraiser",
			            				hqAppraiser : $("#hqAppraiser").val(),
			            				mainid : responseJSON.mainId
			            			},
			            			success:function(responseData){
			            				//alert(JSON.stringify(responseData));
			            				$("#LMS1205S01Form").find("#headAppraiser").val(responseData.headAppraiser);
			            				$("#LMS1205S01Form").find("#areaAppraiser").val(responseData.areaAppraiser);
										// 更新授信簽報書Grid內容
										CommonAPI.triggerOpener("gridview",
												"reloadGrid");
			            				$.thickbox.close();
			            				CommonAPI.showMessage(responseData.runSuccess);
			            			}
		            			}); 				                       
		                     },            		                     
		                     "cancel": function() {
		                    	 API.confirmMessage(i18n.def['flow.exit'], function(res){
		         					if(res){
		         						$.thickbox.close();
		         					}
		         		        });
		                     }
		                   }
		            });				
			}
		});
	}).end().find("#btnSend").click(function(showMsg){
		if ((responseJSON.mainDocStatus == "010" ||
		responseJSON.mainDocStatus == "01O" ||
		responseJSON.mainDocStatus == "07O|0EO" ||
		responseJSON.mainDocStatus == "07O") &&
		auth.Modify &&
		!thickboxOptions.readOnly) {
            //saveBeforeSend=執行將自動儲存資料，是否繼續此動作? 
            CommonAPI.confirmMessage(i18n.def["saveBeforeSend"], function(b){
                if (b) {					
					l1101m01Json.saveAll(showMsg, false, true);
				}				
			});			
		}else{
			l1101m01Json.sendBoss();
		}
	}).end().find("#btnBackUnit").click(function(){
		
		//退回分行更正:退分行待補件
		enterBackReason("6");
		//flowAction({flowAction:"waitCase"});
	}).end().find("#btnBackInHead").click(function(){
		
		//授管處_退回更正
		btnHeadBackAction();
	}).end().find("#btnCheckHead").click(function(){
		
		//授管處覆核
		btnChecActionkHead();
	}).end().find("#btnBackCase").click(function(){	
		
		//撤件/陳復
		btnBackCaseBox();
	}).end().find("#btnBackCase2").click(function(){	
		//J-109-0092_10702_B1001 Web e-Loan新增營運中心簽報書(審查中)加撤件功能
		//撤件
		API.confirmMessage(i18n.def['flow.confirmReturn'], function(res){
			if(res){
				signCaseCancelDate(function(){
					flowAction({
                        flowAction: "cancelCase"
                    });
					$.thickbox.close();
				});
			}
        });
		
	}).end().find("#btnPrint").click(function(showMsg){
		if ((responseJSON.mainDocStatus == "010" ||
		responseJSON.mainDocStatus == "01O" ||
		responseJSON.mainDocStatus == "07O|0EO" ||
		responseJSON.mainDocStatus == "07O") &&
		auth.Modify &&
		!thickboxOptions.readOnly) {
            //saveBeforePrint=執行列印將自動儲存資料，是否繼續此動作? 
            CommonAPI.confirmMessage(i18n.def["saveBeforePrint"], function(b){
                if (b) {
					// 儲存後列印
					l1101m01Json.saveAll(showMsg, true, false);	
                }
            });			
		}else{
			printAction();
		}
	}).end().find("#btnPrint").one("click",function(){
		var gridPrint = $("#printGrid").iGrid({
			needPager: false,
			localFirst: true,
		    handler: 'lms1201gridhandler',
		    height: 270,
			rownumbers:true,
			multiselect: true,
			hideMultiselect:false,
			caption: "&nbsp;",
			hiddengrid : false,
			sortname : 'rptNo',
		    postData: {
		        formAction: "",
				mainId: responseJSON.mainId
		    },
		    colModel: [{
		        colHeader: tempKey['print.custName'],// "借款人名稱",
		        name: 'custName',
		        width: 120,
		        sortable: false
		    }, {
		        colHeader: tempKey['print.rptNo'],// "報表編號",
		        name: 'rptNo',
		        align: "center",
		        width: 40,
		        sortable: false
		    }, {
		        colHeader: tempKey['print.rptName'],// "報表名稱",
		        name: 'rptName',
		        width: 70,
		        sortable: false
		    }, {
		        colHeader: tempKey['print.cntrNo'],// "額度序號",
		        name: 'cntrNo',
		        align: "center",
		        width: 50,
		        sortable: false
		    }, {
				colHeader : "oid",
				name : 'oid',
				hidden : true
			}, {
				colHeader : "rpt",
				name : 'rpt',
				hidden : true
			}, {
				colHeader : "custId",
				name : 'custId',
				hidden : true
			}, {
				colHeader : "dupNo",
				name : 'dupNo',
				hidden : true
			}, {
				colHeader : "refMainId",
				name : 'refMainId',
				hidden : true
			}]
		});	
		
		var gridPrint2 = $("#printGrid2").iGrid({
			localFirst: true,
		    handler: 'lms1201gridhandler',
		    height: 270,
			rownumbers:true,
			multiselect: false,
			hideMultiselect:false,
			caption: "&nbsp;",
			hiddengrid : false,
			sortname : 'rptNo',
		    postData: {
		        formAction: "",
				mainId: responseJSON.mainId
		    },
		    colModel: [{
		        colHeader: tempKey['print.custName'],// "借款人名稱",
		        name: 'custName',
		        width: 120,
		        sortable: false
		    }, {
		        colHeader: tempKey['print.rptNo'],// "報表編號",
		        name: 'rptNo',
		        align: "center",
		        width: 40,
		        sortable: false
		    }, {
		        colHeader: tempKey['print.rptName'],// "報表名稱",
		        name: 'rptName',
		        width: 100,
		        sortable: false
		    }, {
				colHeader : "rptHandle",					//組合方式
				name : 'rptHandle',
				hidden : true
			}, {
				colHeader : "cntrNo",
				name : 'cntrNo',
				hidden : true
			}, {
				colHeader : "oid",
				name : 'oid',
				hidden : true
			}, {
				colHeader : "rpt",
				name : 'rpt',
				hidden : true
			}, {
				colHeader : "custId",
				name : 'custId',
				hidden : true
			}, {
				colHeader : "dupNo",
				name : 'dupNo',
				hidden : true
			}, {
				colHeader : "refMainId",
				name : 'refMainId',
				hidden : true
			}, {
				colHeader : "rptHandle",
				name : 'rptHandle',
				hidden : true
			}, {
				colHeader : "meetingType",
				name : 'meetingType',
				hidden : true
			}, {
				colHeader : "branchType",
				name : 'branchType',
				hidden : true
			}]
		});		
	}).end().find("#btnOpenLmsCase").click(function(){
		if($("#lmss7305_panel").attr("open") == "true"){
			$("#lmss7305_panel").load("../../lms/lms7301m01",function(){
				queryL730m01a();
				$("#lmss7305_panel").attr("open",false);
			});			
		}else{
			queryL730m01a();
		}
	}).end().find("#btnSendWaitLogin").click(function(){
		//總行提會待登錄
		btnSendWaitLogin();
	}).end().find("#btnCheckWaitApprove").click(function(){
		//總行提會待覆核
		btnCheckWaitApprove();
	}).end().find("#btnExit").click(function(){	
		setCloseConfirm(false);
	}).end().find("#btnPrintL120S14A").click(function(showMsg){
		//J-109-0371_05097_B1002 簡化青年創業及啟動金貸款簽報書簽案流程
		$.ajax({
			type : "POST",
			handler : "lms1101formhandler",
			data : {
				formAction : "getContractType",
				mainId : responseJSON.mainId 
			},
			success : function(obj) {
				// a: LMSPrintContractThickBoxa(小規)      b: LMSPrintContractThickBoxb(青創)
                var qType = "";
                if(obj.isSamllBuss == "Y"){
                    qType = "a";
                }else{
                    qType = "b";
                }

                printContractListGrid.jqGrid("setGridParam", {
                    postData: {
                        qType: qType
                    },
                    search: true
                }).trigger("reloadGrid");

                $("#printContractView").thickbox({
                    title: "",
                    width: 400,
                    height: 450,
                    modal: true,
                    buttons: {
                        "sure": function(){
                            var data = printContractListGrid.getSingleData();
                            if (data) {
                                $.thickbox.close();

                                if(obj.isSamllBuss == "Y"){
                                    printContract(data.oid);
                                }else{
                                    printContract_lnType61(data.oid);
                                }
                            }
                        },
                        "close": function(){
                            $.thickbox.close();
                        }
                    }
                });
				
			}
		});
		
        
	}).end().find("#btnTestPrintContract").click(function(){	
		//J-109-0199_05097_B1002 央行C方案授信合約書一鍵產製     

		var isForTest= 'K';    //N 正式，Y 內容為固定寫死測試資料，K:MAINID 是固定寫死
		var	printItem= '1|2|3|4';  //|2|3|4      //1.央行C方案授信合約書   2.本票    3.本票授權書-公司   4.授權扣帳範例
		var	splitMark= '|';
		
		$.ajax({
			type : "POST",
			handler : "lms1101formhandler",
			data : {
				formAction : "queryDownloadFileName",
				mainId : responseJSON.mainId,
				type : "SM_C",
				'isForTest' : isForTest,
     			'printItem' : printItem,  //|2|3|4      //1.央行C方案授信合約書   2.本票    3.本票授權書-公司   4.授權扣帳範例
     			'splitMark' : splitMark
			},
			success : function(obj) {
				
				$.form.submit({
		        	url: __ajaxHandler,
		     		target : "_blank",
		     		data : { 
		     			'_pa' : 'lmsdownloadformhandler' ,
		     			'serviceName': "LmsContractDocService" ,
		     			'oid' : responseJSON.mainOid ,
		     			'mainId' : responseJSON.mainId ,
		     			'isForTest' : isForTest,
		     			'printItem' : printItem,  //|2|3|4      //1.央行C方案授信合約書   2.本票    3.本票授權書-公司   4.授權扣帳範例
		     			'splitMark' : splitMark,
		                'fileDownloadName': obj.fileDownloadName
		     		}
		     	});
				
			}
		});
	}).end().find("#btnApproveUnestablshExl").click(function(){	
		//J-111-0551_05097_B1006 Web e-Loan授信之信用風險管理遵循檢核表及借款人暨關係戶與本行授信往來情形及利潤貢獻度納入在途案件之額度
		approveUnestablshExl();
    });
	
	var tScore = 0;
	//計算授信報案考核表扣分與總扣分
	$("#formopenLmsCase").find(".mathit").blur(function(i){
		var mul1 = $(this).parent().parent().find("td").next().html();
		var mul2 = $(this).val();
		$(this).parent().next().html(mul1*mul2);
		
		//總扣分初始化
		tScore = 0;
		$("#formopenLmsCase").find(".totalit").each(function(j){
			if($(this).html().length == 0){
				tScore += 0;
			}else{
				tScore += parseInt($(this).html(),10);
			}			
		});
		//將計算後的總扣分結果設定到畫面上
		$("#formopenLmsCase").find("#tmGrade").html(tScore);
	});

	var printContractListGrid = $("#printContractListGrid").iGrid({
        handler: 'lms1201gridhandler',
        height: 270,
        width: 100,
        needPager: false,
        multiselect: false,
        postData: {
            formAction: "queryL140m01aByL120m01a",
            mainId: responseJSON.mainId
        },
        colModel: [{
            colHeader: tempKey['print.cntrNo'],
            name: 'cntrNo',
            align: "center",
            width: 100,
            sortable: false
        }, {
            colHeader : "oid",
            name : 'oid',
            hidden : true
        }]
    });
	
	
});
		
//登錄-Thickbox
function signThickBox(){
	$("#signThick").thickbox({     // 使用選取的內容進行彈窗
       title : i18n.lms1101m01['l120m01a.login'],
       width :250,
       height : 150,
       modal : true,
       valign : "bottom",
	   align : "center",
	   i18n:i18n.def,
	   readOnly: false,
       buttons: {
                 "sure": function() {
                  $.thickbox.close();
                  signContent();
                 },            
                 "cancel": function() {
                	 API.confirmMessage(i18n.def['flow.exit'], function(res){
     					if(res){
     						$.thickbox.close();
     					}
     		        });
                 }
               }
        });	
}

//登錄-Thickbox
function signThickBox2(){
	$("#signThick2").find("input[name='login']").readOnly(false);
	$("#signThick2").thickbox({     // 使用選取的內容進行彈窗
       title : i18n.lms1101m01['l120m01a.login'],
       width :250,
       height : 150,
       modal : true,
       valign : "bottom",
	   align : "center",
	   i18n:i18n.def,
	   readOnly: false,
       buttons: {
                 "sure": function() {
	                 var value =  $("input[name='login']:checked").val();
	                 $.thickbox.close();
	                 if(value == 1){
	                	 //案件審核層級
		           	      var obj= CommonAPI.loadCombos("lms1205m01_caseLvl");
		        	        //幣別
		        	      $("#formCaseLvl").find("#caseLvl").setItems({
		        	     		item:obj.lms1205m01_caseLvl,
		        	     		format : "{key}",
		        	     		space: false
		        	     	});
		                	 signCaseLvl();
	                 }else{
	                	 //補充意見及審查意見
	                	 signContent2(); 
	                 }
                 },            
                 "cancel": function() {
                	 API.confirmMessage(i18n.def['flow.exit'], function(res){
     					if(res){
     						$.thickbox.close();
     					}
     		        });
                 }
               }
        });	
}

//登錄授管處-Thickbox for 授權內
function signThickBox2_1(){
    $("#_tItemDscr0A").val($("#tItemDscr0A").val());
    $("#_tItemDscr0B").val($("#tItemDscr0B").val());
    $("#signThick2_1").find("input[name='login']").readOnly(false);
    if (responseJSON.hqMeetFlag == "1" ||
    responseJSON.hqMeetFlag == "2" ||
    responseJSON.hqMeetFlag == "3" || 
	responseJSON.hqMeetFlag == "4"
    ) {
        $("#signThick2_1").find(".hide").show();
    }
    else {
        $("#signThick2_1").find(".hide").hide();
    }
    lmsM01Json.checkSpectialHead();
    $("#signThick2_1").thickbox({ // 使用選取的內容進行彈窗
        title: i18n.lms1101m01['l120m01a.login'],
        width: 450,
        height: 250,
        modal: true,
        valign: "bottom",
        align: "center",
        i18n: i18n.def,
        readOnly: false,
        buttons: {
            "sure": function(){
                var value = $("input[name='login']:checked").val();
                $.thickbox.close();
                if (value == 1) {
                    //案件審核層級
                    var obj = CommonAPI.loadCombos(["lms1205m01_caseLvl"]);
                    $("#formCaseLvl").find("#caseLvl").setItems({
                        item: obj.lms1205m01_caseLvl,
                        format: "{value} - {key}",
                        space: false
                    });
                    signCaseLvl();
                }else if (value == 991) {
                	//J-109-0479_05097_B1001 Web e-Loan簽報書增加各別流程控管階段的時間點並提供列印案件階段進度及統計excel下載
                    //案件收件日期
                	signCaseReceivedDate();
                }else if (value == 2) {
                        //補充意見及審查意見
                        //signContent2();
                        $.ajax({
                            type: "POST",
                            handler: "lms1301formhandler",
                            data: {
                                formAction: "saveSignContent2",
                                itemDscr0A: $("#tItemDscr0A").val(),
                                itemDscr0B: $("#tItemDscr0B").val(),
                                sHeadLeader: $("#sHeadLeader").val(),
                                sHeadSubLeader: $("#sHeadSubLeader").val(),
                                sHeadReCheck: $("#sHeadReCheck").val(),
                                sHeadAppraiser: $("#sHeadAppraiser").val(),
                                mainid: responseJSON.mainId
                            },
                            success: function(responseData){
                                //alert(JSON.stringify(responseData));
                                //$("#LMS1205S01Form").setData(responseData.LMS1205S01Form, false);
                                setCkeditor2("itemDscr0A", responseData.L120M01aForm13.itemDscr0A);
                                setCkeditor2("itemDscr0B", responseData.L120M01aForm13.itemDscr0B);
                            //$("#L120M01aForm13").find("#itemDscr0A").val(responseData.L120M01aForm13.itemDscr0A);
                            //$("#L120M01aForm13").find("#itemDscr0B").val(responseData.L120M01aForm13.itemDscr0B);
                            }
                        });
                    }
                    else 
                        if (value == 998) {
                            // 授管處會簽意見(國內授信專用)
                            $.ajax({
                                type: "POST",
                                handler: "lms1301formhandler",
                                data: {
                                    formAction: "saveSignContent2a",
                                    htItemDscr0C: $("#htItemDscr0C").val(),
                                    sHeadLeader: $("#sHeadLeader").val(),
                                    sHeadSubLeader: $("#sHeadSubLeader").val(),
                                    sHeadReCheck: $("#sHeadReCheck").val(),
                                    sHeadAppraiser: $("#sHeadAppraiser").val(),
                                    mainid: responseJSON.mainId
                                },
                                success: function(responseData){
                                    //$("#LMS1205S01Form").setData(responseData.LMS1205S01Form, false);
                                    setCkeditor2("itemDscr0C", responseData.L120M01aForm12.itemDscr0C);
                                //$("#L120M01aForm13").find("#itemDscr0A").val(responseData.L120M01aForm13.itemDscr0A);
                                //$("#L120M01aForm13").find("#itemDscr0B").val(responseData.L120M01aForm13.itemDscr0B);
                                }
                            });
                        }
                        else 
                            if (value == 3) {
                                // 會議決議
                                $.ajax({
                                    type: "POST",
                                    handler: "lms1301formhandler",
                                    data: {
                                        formAction: "querySignContent0",
                                        mainId: responseJSON.mainId,
                                        txCode: responseJSON.txCode
                                    },
                                    success: function(responseData){
                                        //alert(JSON.stringify(responseData));
                                        $("#formL120m01h").reset();
                                        $("#formL120m01h").setData(responseData.formL120m01h, false);
                                        signContent0();
                                    }
                                });
                            }
                            else 
                                if (value == 4) {
                                    lmsM02Json.thickOpenUnNormal(false);
                                }
                                else 
                                    if (value == 5) {
                                        lmsM02Json.thickOpenDecide(false);
                                    }
                                    else {
                                        // 登錄簽章欄人員
                                        signContent2();
                                    }
            },
            "cancel": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

function login1(oid,isArea) {
	//$("#LMS1200V62Form1").reset();
	$("#LMS1200V62Form1").find("#rptTitle1e").html($("body").find(".system_info span").html());
	$("#LMS1200V62Thickbox1").thickbox({ // 使用選取的內容進行彈窗
			title : i18n.lms1101m01["l120v01.thickbox7"],
			width : 500,
			height : 200,
			modal : true,
			valign : "bottom",
			align : "center",
			i18n : i18n.def,
			readOnly: false,
			buttons : {
				"sure" : function() {
					var $LMS1200V62Form1 = $("#LMS1200V62Form1");
					if ($LMS1200V62Form1.valid()) {
						if ($LMS1200V62Form1.find("#rptTitle1b").val() < 1
								|| $LMS1200V62Form1.find("#rptTitle1b").val() > 12) {
							CommonAPI.showMessage(i18n.lms1101m01["l120v01.error3"]);
							return;
						} else if ($LMS1200V62Form1.find("#rptTitle1c").val() < 1
								|| $LMS1200V62Form1.find("#rptTitle1c").val() > 31) {
							CommonAPI.showMessage(i18n.lms1101m01["l120v01.error4"]);
							return;
						} else if ($LMS1200V62Form1.find(
								"#rptTitle1d").val() <= 0) {
							CommonAPI.showMessage(i18n.lms1101m01["l120v01.error6"]);
							return;
						} else if ($LMS1200V62Form1.find("#rptTitle1a").val() <= 0) {
							CommonAPI.showMessage(i18n.lms1101m01["l120v01.error8"]);
							return;
						} else {
							$.ajax({
								type : "POST",
								handler : "lms1101formhandler",
								data : {
									formAction : "login1",
									LMS1200V62Form1 : JSON.stringify($LMS1200V62Form1.serializeData()),
									oid : oid,
									isArea : isArea,
									idDelete : false,
									caseName : $LMS1200V62Form1.find("b").text()
								},
								success : function(responseData) {
								}
							});
						}
						$.thickbox.close();
					}
				},
				"cancel" : function() {
					 API.confirmMessage(i18n.def['flow.exit'], function(res){
							if(res){
								$.thickbox.close();
							}
				        });
				},
				"del" : function() {
					$.thickbox.close();
					var $LMS1200V62Form1 = $("#LMS1200V62Form1");
					 API.confirmMessage(i18n.def['confirmDelete'], function(res){
							if(res){
								$.ajax({
									type : "POST",
									handler : "lms1101formhandler",
									data : {
										formAction : "login1",
										LMS1200V62Form1 : JSON.stringify($LMS1200V62Form1.serializeData()),
										oid : oid,
										isArea : isArea,
										isDelete : true,
										caseName : $LMS1200V62Form1.find("b").text()
									},
									success : function(responseData) {
										$.thickbox.close();
									}
								});
								
							}
							
							$.thickbox.close();
				        });
				}
			}
		});		
}

//登錄營運中心-Thickbox
function signThickBox3(){
	$("#_tItemDscr07").val($("#tItemDscr07").val());
	$("#_tItemDscr08").val($("#tItemDscr08").val());
	$("#signThick3").find("input[name='login2']").readOnly(false);
	$("#signThick3").thickbox({     // 使用選取的內容進行彈窗
       title : i18n.lms1101m01['l120m01a.login'],
       width :450,
       height : 250,
       modal : true,
       valign : "bottom",
	   align : "center",
	   i18n:i18n.def,
	   readOnly: false,
       buttons: {
                 "sure": function() {
	                 var value =  $("input[name='login2']:checked").val();
	                 $.thickbox.close();
	                 if(value == 1){
	                	 //營運中心授審會會期
						$.ajax({
							type : "POST",
							handler : "lms1101formhandler",
							data : {
								formAction : "queryLogin1",
								isArea : true,
								mainId : responseJSON.mainId
							},
							success : function(responseData) {
								$("#LMS1200V62Form1").setData(responseData);
								login1(responseData.oid,true);
							}
						});						 
	                 }else if(value == 2){
	                	 //營運中心說明及意見
	                	 //signContent3();
	             		$.ajax({
		            			type : "POST",
		            			handler : "lms1101formhandler",
		            			data : 
		            			{
		            				formAction : "saveSignContent3",
		            				itemDscr07 : $("#tItemDscr07").val(),
		            				itemDscr08 : $("#tItemDscr08").val(),
									sAreaLeader	: $("#sAreaLeader").val(),
									sAreaSubLeader : $("#sAreaSubLeader").val(),
									sAreaManager : $("#sAreaManager").val(),
									sAreaAppraiser : $("#sAreaAppraiser").val(),
									itemTitle : $("#itemTitle").val(),
		            				mainid : responseJSON.mainId
		            			},
		            			success:function(responseData){
		            				//alert(JSON.stringify(responseData));
									//$("#LMS1205S01Form").setData(responseData.LMS1205S01Form, false);
									setCkeditor2("itemDscr07", responseData.L120M01aForm15.itemDscr07);
									setCkeditor2("itemDscr08", responseData.L120M01aForm15.itemDscr08);
		            				//$("#L120M01aForm15").find("#itemDscr07").val(responseData.L120M01aForm15.itemDscr07);
		            				//$("#L120M01aForm15").find("#itemDscr08").val(responseData.L120M01aForm15.itemDscr08);
		            			}
	            			});						  
	                 }else if(value == 3){
					 	// 會議決議
						$.ajax({
				    			type : "POST",
				    			handler : "lms1101formhandler",
				    			data : 
				    			{
				    				formAction : "querySignContent0",
									isArea : true,
				    				mainId : responseJSON.mainId,
									txCode : responseJSON.txCode
				    			},
				    			success:function(responseData){
				    				//alert(JSON.stringify(responseData));
									$("#formL120m01h").reset();
									$("#formL120m01h").setData(responseData.formL120m01h,false);
				    				signContent0();
				    			}
							});					 	
					 } else if (value == 991) {
                        	//#J-110-0374 Web e-Loan 為加強區域營運中心授信案件之審查效率, 增加企/消金授信簽報案件經區域營運中心流程進度控管表
                            //案件收件日期
                        	signCaseReceivedDate();   
					 } else{
					 	signContent3();
					 }
                 },            
                 "cancel": function() {
                	 API.confirmMessage(i18n.def['flow.exit'], function(res){
     					if(res){
     						$.thickbox.close();
     					}
     		        });
                 }
               }
        });	
}	

//查詢會簽意見內容(營運中心)-Thickbox
function rSignContent(){
	$("#signContent").thickbox({     // 使用選取的內容進行彈窗
       title : i18n.lms1101m01['l120m01a.looksay2'],
       width :640,
       height : 350,
       modal : true,
	   i18n:i18n.def,
	   readOnly: false,
       buttons: {            
                 "close": function() {
                	 API.confirmMessage(i18n.def['flow.exit'], function(res){
     					if(res){
     						$.thickbox.close();
     					}
     		        });
                 }
               }
        });
	$.ajax({
		type : "POST",
		handler : "lms1101formhandler",
		data : 
		{
			formAction : "querySignContent",
			mainId : responseJSON.mainId
		},
		success:function(responseData){
			$("#tItemDscr09").val(responseData.formSea.itemDscr09);
		}
	});			
}

//會議決議(產報表)
function printR20(){
	var $formL120m01h = $("#formL120m01h");
    CommonAPI.confirmMessage(i18n.def["saveBeforePrint"], function(b){
        if (b) {
			// 儲存後列印
     		$.ajax({
        			type : "POST",
        			handler : "lms1101formhandler",
        			data : $.extend({
        				formAction : "saveSignContent0",
						formL120m01h : JSON.stringify($formL120m01h.serializeData()),
        				mainid : responseJSON.mainId,
						txCode : responseJSON.txCode
        			},(userInfo.unitNo == "920"
					 || userInfo.unitNo == "922"
					 || userInfo.unitNo == "931"
					 || userInfo.unitNo == "932"
					 || userInfo.unitNo == "933"
					 || userInfo.unitNo == "934"
					 || userInfo.unitNo == "935")?{isArea : true}:{}),
        			success:function(responseData){
						var itemDscr08 = responseData.itemDscr08;
						var itemDscr0D = responseData.itemDscr0D;
						var page = responseJSON.page;
						if(itemDscr08 != undefined && itemDscr08 != null){
							if(page == "15"){
								setCkeditor2("itemDscr08",itemDscr08);
							}
						}
						if(itemDscr0D != undefined && itemDscr0D != null){
							if(page == "12"){
								setCkeditor2("itemDscr0D",itemDscr0D);
							}							
						}
						$.form.submit({
					        url: "../../simple/FileProcessingService",
					        target: "_blank",
					        data: {
					        	mainId : responseJSON.mainId,
					        	rptOid : "R20" + "^" + "",
								otherData : "3",
								fileDownloadName : "l120r01.pdf",
								serviceName : "lms1201r01rptservice"
					        }
					    });
        			}
    			});
        }
    });		
}

// 會議決議ThickBox畫面
function signContent0(){
	var $formL120m01h = $("#formL120m01h");
	$("#meetingNote").readOnly(false);
	$formL120m01h.find("#itemDscrC").readOnly(true);
	$("#signContent0").thickbox({     // 使用選取的內容進行彈窗
       title : i18n.lms1101m01['L120M01H.meetingNote'],
       width :900,
       height : 480,
       modal : true,
       valign : "bottom",
	   align : "center",
	   i18n:i18n.def,
	   readOnly: false,
       buttons: {
                 "sure": function() {
             		$.ajax({
	            			type : "POST",
	            			handler : "lms1101formhandler",
	            			data : $.extend({
	            				formAction : "saveSignContent0",
								formL120m01h : JSON.stringify($formL120m01h.serializeData()),
	            				mainid : responseJSON.mainId,
								txCode : responseJSON.txCode
	            			},(userInfo.unitNo == "920"
							 || userInfo.unitNo == "922"
							 || userInfo.unitNo == "931"
							 || userInfo.unitNo == "932"
							 || userInfo.unitNo == "933"
							 || userInfo.unitNo == "934"
							 || userInfo.unitNo == "935")?{isArea : true}:{}),
	            			success:function(responseData){
								var itemDscr08 = responseData.itemDscr08;
								var itemDscr0D = responseData.itemDscr0D;
								var page = responseJSON.page;
								if(itemDscr08 != undefined && itemDscr08 != null){
									if(page == "15"){
										setCkeditor2("itemDscr08",itemDscr08);
									}
								}
								if(itemDscr0D != undefined && itemDscr0D != null){
									if(page == "12"){
										setCkeditor2("itemDscr0D",itemDscr0D);
									}							
								}								
	            				//alert(JSON.stringify(responseData));
	            				//$("#L120M01aForm14").find("#itemDscr09").val(responseData.L120M01aForm14.itemDscr09);
	            			}
            			});
                  $.thickbox.close(); 
                 },            
                 "cancel": function() {
                	 API.confirmMessage(i18n.def['flow.exit'], function(res){
     					if(res){
     						$.thickbox.close();
     					}
     		        });
                 }
               }
        });	
}

//J-110-0374 Web e-Loan 為加強區域營運中心授信案件之審查效率, 增加企/消金授信簽報案件經區域營運中心流程進度控管表
//登錄案件收件日期
function signCaseReceivedDate(){
	
	$("#formCaseReceivedDate").find("#caseReceivedDate").val(CommonAPI.getToday());
    $("#signCaseReceivedDate").thickbox({ // 使用選取的內容進行彈窗
        title: i18n.lms1101m01['l120s17a.caseReceivedDate'],   //案件收件日期
        width: 400,
        height: 150,
        modal: true,
        valign: "bottom",
        align: "center",
        i18n: i18n.def,
        readOnly: false,
        buttons: {
            "sure": function(){
                $.ajax({
                    type: "POST",
                    handler: "lms1201formhandler",
                    data: {
                        formAction: "setCaseReceivedDate",
                        caseReceivedDate: $("#formCaseReceivedDate").find("#caseReceivedDate").val(),
                        mainid: responseJSON.mainId
                    },
                    success: function(responseData){
                        
                        $.thickbox.close();
                        $.thickbox.close();
                        CommonAPI.showMessage(responseData.NOTIFY_MESSAGE);
                    }
                });
            },
            "cancel": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

//登錄案件審核層級
function signCaseLvl(){
	$("#signCaseLvl").thickbox({     // 使用選取的內容進行彈窗
       title : i18n.lms1101m01['l120m01a.caselevel'],
       width :320,
       height : 200,
       modal : true,
       valign : "bottom",
	   align : "center",
	   i18n:i18n.def,
	   readOnly: false,
       buttons: {
                 "sure": function() {
                	$.thickbox.close();  
             		$.ajax({
	            			type : "POST",
	            			handler : "lms1101formhandler",
	            			data : 
	            			{
	            				formAction : "setCaseLvl",
	            				caseLvl : $("#caseLvl option:selected").val(),
	            				mainid : responseJSON.mainId
	            			},
	            			success:function(responseData){
	            				//alert(JSON.stringify(responseData));
								$("#caseLvl").val(responseData.caseLvl);
	            			}
            			}); 
                 },            
                 "cancel": function() {
                	 API.confirmMessage(i18n.def['flow.exit'], function(res){
     					if(res){
     						$.thickbox.close();
     					}
     		        });
                 }
               }
        });	
}

//合併多個serializeData JSON 成一個serializeData JSON
function mergeJSON(json, array){
	for ( var data in array) {
		json[data] = array[data];
	}	
}

//檢查陣列內容是否重複
function checkArrayRepeat(arrVal) {
	var newArray = [];
	for (var i = arrVal.length; i--; ) {
		var val = arrVal[i];
		if ($.inArray(val, newArray) == -1) {
			newArray.push(val);
		}else{
			return true;
		}
	}
	return false;
}

//J-110-0336_05097_B1001 Web e-Loan授信異常通報增加通報類別與流程
function setUI(){
	
	var LMS1205S01Form = $("#LMS1205S01Form");
    var caseType = LMS1205S01Form.find("#caseType").val();
     
    var miniFlag =  LMS1205S01Form.find("input[name='miniFlag']:radio:checked").val();
    
    if(lmsM01Json.showCaseTypeA(responseJSON.docType,responseJSON.docCode,miniFlag,caseType)){
    	//異常通報
    	LMS1205S01Form.find(".showCaseTypeA").show();
    }else{
    	LMS1205S01Form.find(".showCaseTypeA").hide();
    	LMS1205S01Form.find("#caseTypeA").val('');
    }
}

//畫面切換table 所需設定之資料 如無設定 則直接切換
$.extend(window.tempSave, {
    handler: "lms1101formhandler",
    action: "tempSave",
	beforeCheck: function(){
		if(responseJSON.page == "02"){
			var count=$("#l120s01agrid").jqGrid('getGridParam','records');
			if(count == 0){
				 CommonAPI.showErrorMessage(i18n.lms1101m01('l120m01a.error24', {
					          'colName': ''
					    }));				
				return false;
			}else{
				return true;
			}
    	}else if(responseJSON.page == "04"){
    		//案由
			if ($("#LMS1205S04Form").valid()) {
				return $("#LMS1205S04Form").valid();
			}else{
				return false;
			}
    	}else if(responseJSON.page == "05"){
    		//說明
			if((responseJSON.docType == "2" && responseJSON.docCode == "1") || 
					(responseJSON.docType == "1" && responseJSON.docCode == "1" && responseJSON.docKind == "1")){
				if($("#CLS1205S05Form").valid()){
					return $("#CLS1205S05Form").valid();	
				}else{
					return false;
				}
			}else if(responseJSON.docCode == "2" || responseJSON.docCode == "3"){
				return $("#L120M01dForm03").valid();
			}else{
				return true;
			}
    	}else{
			return true;
		}
    },	
    sendData: function(){
    	if(responseJSON.page == "01"){
    		//文件資訊
    		return $("#LMS1205S01Form").serializeData();
    	}else if(responseJSON.page == "03"){
    		//額度明細表
    		//J-105-0264-001  WEB-ELOAN授信案件簽報書增訂「新臺幣、美元利率定價合理性分析表」。
    		return $("#L120M01BForm,#L120M01CForm,#L120M01IForm").serializeData();
    	}else if(responseJSON.page == "04"){
    		//案由
			return $("#LMS1205S04Form").serializeData();
    	}else if(responseJSON.page == "05"){
    		//說明
			if(responseJSON.docType == "1" && responseJSON.docCode == "1" && responseJSON.docKind == "2"){
	    		var json = {};
	    		var array = {};
	    		array["LMS1205S05Form01"] = $("#LMS1205S05Form01").serializeData();
	    		array["LMS1205S05Form04"] = $("#LMS1205S05Form04").serializeData();
	    		array["form06Lms1205s05"] = $("#form06Lms1205s05").serializeData();
	    		array["form06Lms1205s06"] = $("#form06Lms1205s06").serializeData();			
	    		mergeJSON(json, array.LMS1205S05Form01);
	    		mergeJSON(json, array.LMS1205S05Form04);
	    		mergeJSON(json, array.LMS1205S05Form05);
	    		mergeJSON(json, array.LMS1205S05Form06);				
				return json;
			}else if((responseJSON.docType == "2" && responseJSON.docCode == "1") || 
					(responseJSON.docType == "1" && responseJSON.docCode == "1" && responseJSON.docKind == "1")){
				return $("#CLS1205S05Form").serializeData();
			}else if(responseJSON.docCode == "2" || responseJSON.docCode == "3"){
				return $("#L120M01dForm03,#LMS1205S07Form05").serializeData();
			}

    	}else if(responseJSON.page == "06"){
    		//其他
    		return $("#L120M01dForm03").serializeData();
    	}else if(responseJSON.page == "07"){
    		//綜合評估/往來彙總
    		return $("#L120M01dForm04,#LMS1205S07Form05").serializeData();
    	}else if(responseJSON.page == "08"){
			//相關文件
			return $("#formL120m01e").serializeData();
		}else if(responseJSON.page == "09"){
    		//補充說明
    		return $("#L120M01dForm05").serializeData();
    	}else if(responseJSON.page == "18"){
    		//主管批示
    		return $("#L120M01dForm06").serializeData();
    	}else if(responseJSON.page == "21"){
    		//還款來源國
    		//J-106-0085-001  Web e-Loan企金授信新增主要還款來源國等相關欄位
    		return $("#LMS1205S21Form01").serializeData();	
    	}else if(responseJSON.page == "23"){
    		// J-108-0243 微型企業
    		return $("#LMSS23Form").serializeData();	
    	}else{
			return {};
		}        
    }
	
	
});

// 取得婉卻控管種類確認訊息
function getRejtMsgData(){
    var result = null;
    $.ajax({
        type: "POST",
        handler: "lms1201formhandler",
        async: false,
        data: {
            formAction: "getRejtMsgData",
            mainid: responseJSON.mainId
        },
        success: function(responseData){
            var rejtMsgData = responseData.rejtMsgData;
            if (rejtMsgData != undefined && rejtMsgData != null && rejtMsgData != "") {
                result = rejtMsgData;
            }
        }
    });
    return result;
}

 