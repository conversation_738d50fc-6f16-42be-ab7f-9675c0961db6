/* 
 * L785M01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Basic;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.Lob;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

import tw.com.iisi.cap.model.IDataObject;

/** 全行額度明細表查詢主檔 **/
@NamedEntityGraph(name = "L785M01A-entity-graph", attributeNodes = { @NamedAttributeNode("l999log01a") })
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L785M01A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class L785M01A extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * JOIN條件 L230A01A．關聯檔
	 * 
	 */
	@OneToOne(cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	@JoinColumn(name = "MAINID", referencedColumnName = "MAINID", nullable = false, insertable = false, updatable = false)
	private L999LOG01A l999log01a;

	public L999LOG01A getL999log01a() {
		return l999log01a;
	}

	public void setL999log01a(L999LOG01A l999log01a) {
		this.l999log01a = l999log01a;
	}

	/**
	 * 案件號碼-年度
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	@Digits(integer = 4, fraction = 0, groups = Check.class)
	@Column(name = "CASEYEAR", columnDefinition = "DECIMAL(4,0)")
	private Integer caseYear;

	/**
	 * 案件號碼-分行
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	@Size(max = 3)
	@Column(name = "CASEBRID", length = 3, columnDefinition = "CHAR(3)")
	private String caseBrId;

	/**
	 * 案件號碼-流水號
	 * <p/>
	 * 100/09/27調整<br/>
	 * 資料來源：案件簽報書
	 */
	@Digits(integer = 5, fraction = 0, groups = Check.class)
	@Column(name = "CASESEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer caseSeq;

	/**
	 * 案件號碼
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	@Size(max = 62)
	@Column(name = "CASENO", length = 62, columnDefinition = "VARCHAR(62)")
	private String caseNo;

	/**
	 * 簽案日期
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "CASEDATE", columnDefinition = "DATE")
	private Date caseDate;

	/** 篩選條件 **/
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(name = "ITEMDSCR", columnDefinition = "CLOB")
	private String itemDscr;

	@Transient
	private String itemDscrShow = "N";

	/**
	 * 取得案件號碼-年度
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	public Integer getCaseYear() {
		return this.caseYear;
	}

	/**
	 * 設定案件號碼-年度
	 * <p/>
	 * 資料來源：案件簽報書
	 **/
	public void setCaseYear(Integer value) {
		this.caseYear = value;
	}

	/**
	 * 取得案件號碼-分行
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	public String getCaseBrId() {
		return this.caseBrId;
	}

	/**
	 * 設定案件號碼-分行
	 * <p/>
	 * 資料來源：案件簽報書
	 **/
	public void setCaseBrId(String value) {
		this.caseBrId = value;
	}

	/**
	 * 取得案件號碼-流水號
	 * <p/>
	 * 100/09/27調整<br/>
	 * 資料來源：案件簽報書
	 */
	public Integer getCaseSeq() {
		return this.caseSeq;
	}

	/**
	 * 設定案件號碼-流水號
	 * <p/>
	 * 100/09/27調整<br/>
	 * 資料來源：案件簽報書
	 **/
	public void setCaseSeq(Integer value) {
		this.caseSeq = value;
	}

	/**
	 * 取得案件號碼
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	public String getCaseNo() {
		return this.caseNo;
	}

	/**
	 * 設定案件號碼
	 * <p/>
	 * 資料來源：案件簽報書
	 **/
	public void setCaseNo(String value) {
		this.caseNo = value;
	}

	/**
	 * 取得簽案日期
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	public Date getCaseDate() {
		return this.caseDate;
	}

	/**
	 * 設定簽案日期
	 * <p/>
	 * 資料來源：案件簽報書
	 **/
	public void setCaseDate(Date value) {
		this.caseDate = value;
	}

	/** 取得篩選條件 **/
	public String getItemDscr() {
		return this.itemDscr;
	}

	/** 設定篩選條件 **/
	public void setItemDscr(String value) {
		this.itemDscr = value;
	}

	public void setItemDscrShow(String itemDscrShow) {
		this.itemDscrShow = itemDscrShow;
	}

	public String getItemDscrShow() {
		return itemDscrShow;
	}

}
