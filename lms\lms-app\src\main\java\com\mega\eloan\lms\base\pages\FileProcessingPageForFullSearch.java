package com.mega.eloan.lms.base.pages;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.response.CapByteArrayDownloadResult;
import tw.com.iisi.cap.response.IResult;

import tw.com.jcs.common.Util;

/**
 * 提供notes直接下載pdf
 * 
 * security.xml要增加設定
 * 
 * <AUTHOR>
 * 
 */
@Controller
@RequestMapping("/simple/FileProcessingPageForFullSearch")
public class FileProcessingPageForFullSearch extends AbstractFileDownloadPage {
	@Autowired
	L120M01ADao l120m01aDao;

	public FileProcessingPageForFullSearch() {

		super();

	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * tw.com.iisi.cap.base.pages.AbstractCapPage#execute(org.apache.wicket.
	 * PageParameters)
	 */
	@Override
	public void execute(ModelMap model, PageParameters params) throws Exception {
		this.fileDownloadName = params.getString("fileDownloadName");
		this.serviceName = params.getString("serviceName");

		// 原始呼叫完整URL
		// http://192.168.59.130:9081/lms-web/app/simple/FileProcessingPageForNotes?random=0.7614665330930178&fileDownloadName=LMS1205R01.pdf&serviceName=lms1201r01rptservice&rptOid=R01^2EF2A6BDBAC946FEA9E19CDD4669B973^^^^|R12^8A604F8012AA11EB9B9D046CC0A89955^73251209^0^005110900630^|R29^8A604F8012AA11EB9B9D046CC0A89955^73251209^0^005110900630^|R14^2EF2A6BDBAC946FEA9E19CDD4669B973^^^^|R32^^^^^|R33^^^^^|R36^^^^^|R39^^^^^&mainId=4a292a290b2c473889bb643562b80db0

		// FOR 全文檢索簡化後URL
		// http://192.168.59.130:9081/lms-web/app/simple/FileProcessingPageForFullSearch?random=0.7614665330930181&mainId=4a292a290b2c473889bb643562b80db0

		MegaSSOUserDetails users = MegaSSOSecurityContext.getUserDetails();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		L120M01A l120m01a = l120m01aDao.findByMainId(mainId);

		if (l120m01a != null) {

			
			
			
			
			
			
			if (!params.containsKey("rptOid")) {
				String rptOid = "R01^2EF2A6BDBAC946FEA9E19CDD4669B973^^^^|R12^8A604F8012AA11EB9B9D046CC0A89955^73251209^0^005110900630^|R29^8A604F8012AA11EB9B9D046CC0A89955^73251209^0^005110900630^|R14^2EF2A6BDBAC946FEA9E19CDD4669B973^^^^|R32^^^^^|R33^^^^^|R36^^^^^|R39^^^^^";
				params.add("rptOid", rptOid);
			}

			if (!params.containsKey("serviceName")) {
				String tserviceName = "";
				if (Util.equals(l120m01a.getDocType(),
						UtilConstants.Casedoc.DocType.企金)) {
					if (Util.equals(l120m01a.getTypCd(),
							UtilConstants.Casedoc.typCd.海外)) {
						tserviceName = "lms1205r01rptservice";
					} else {
						tserviceName = "lms1201r01rptservice";
					}
				} else {
					// 個金
				}

				this.serviceName = tserviceName;
				params.add("serviceName", tserviceName);
			}
		}

		if (!params.containsKey("fileDownloadName")) {
			String tfileDownloadName = "LMS1205R01.pdf";
			this.fileDownloadName = tfileDownloadName;
			params.add("fileDownloadName", tfileDownloadName);
		}

		IResult result = new CapByteArrayDownloadResult(getContent(params), getFileContentType(), fileDownloadName);
		result.respondResult(response);

	}

	@Override
	public String getDownloadFileName() {
		return this.fileDownloadName;
	}

	@Override
	public String getFileDownloadServiceName() {
		return this.serviceName;
	}

	@Override
	protected String getViewName() {
		// TODO Auto-generated method stub
		return null;
	}
}
