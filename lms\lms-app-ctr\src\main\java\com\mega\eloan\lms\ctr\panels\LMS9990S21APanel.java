/* 
 * LMS9990S21Panel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.ctr.panels;

import com.mega.eloan.common.panels.Panel;

/**<pre>
 * 個金約據書-撥款方式(分頁)
 * </pre>
 * @since  2012/7/24
 * <AUTHOR>
 * @version <ul>
 *           <li>2012/7/24,<PERSON>,new
 *          </ul>
 */
public class LMS9990S21APanel extends Panel {
	
	public LMS9990S21APanel(String id) {
		super(id);
	}

	public LMS9990S21APanel(String id, boolean updatePanelName) {
		super(id, updatePanelName);
	}

	/**/
	private static final long serialVersionUID = 1L;

}
