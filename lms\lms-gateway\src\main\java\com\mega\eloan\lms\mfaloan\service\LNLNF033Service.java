/* 
 *LNLNF033Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.mfaloan.service;

import com.mega.eloan.lms.mfaloan.bean.LNF033;

/**
 * <pre>
 * LN.LN033 期付金控制檔
 * </pre>
 * 
 * @since 2012/11/7
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/11/7,REX,new
 *          </ul>
 */
public interface LNLNF033Service {

	/**
	 * 查詢
	 * 
	 * @param loanNo
	 *            放款帳號
	 * @return
	 */
	public LNF033 findByKey(String loanNo);

}
