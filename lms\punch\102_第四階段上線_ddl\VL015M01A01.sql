-- LMS.VL015M01A01
--2013/02/04,<PERSON>,因應簽報書狀態不一樣所以要更換veiw的條件
;
DROP   VIEW LMS.VL015M01A01;
CREATE VIEW LMS.VL015M01A01 AS 
  SELECT 'L120M01A' AS type,'' AS typeShow,A1.CaseDate,A1.Doctype,A1.DocKind,A1.DocCode
    ,A1.OID,A1.CUSTID,A1.dupNo,A1.custName,A1.docStatus,A1.docURL,A1.txCode,A1.updater
    ,A1.APPROVER,A1.createTime,A1.mainId,A1.caseNo,A1.ownBrid,A1.ngFlag 
  FROM LMS.L120M01A A1 
  WHERE A1.deletedTime IS NULL AND A1.DOCSTATUS 
  IN ('01O','02O','07O', '0DO','L1H','L6H','L1C','L2C','01K',
	  '02K','03K','04K','05K','06K','07K')
  UNION
  SELECT 'L141M01A' AS type,'' AS typeShow,A1.CaseDate,'','',''
    ,A1.OID,A1.CUSTID,A1.dupNo,A1.custName,A1.docStatus,A1.docURL,A1.txCode,A1.updater
    ,A1.APPROVER,A1.createTime,A1.mainId,A1.caseNo,A1.ownBrid,'' as ngFlag
  FROM LMS.L141M01A A1 
  WHERE A1.deletedTime IS NULL AND A1.DOCSTATUS IN ('01O','02O')
  UNION
  SELECT 'L160M01A' AS type, '' AS typeShow,A1.CaseDate,'','',''
    ,A1.OID,A1.CUSTID,A1.dupNo,A1.custName,A1.docStatus,A1.docURL,A1.txCode,A1.updater
    ,A1.APPROVER,A1.createTime,A1.mainId,A1.caseNo,A1.ownBrid,'' as ngFlag 
  FROM LMS.L160M01A A1 
  WHERE A1.deletedTime IS NULL AND A1.DOCSTATUS IN ('01O','02O','04O')
;

COMMENT ON TABLE LMS.VL015M01A01 IS '待辦事項view';
COMMENT ON LMS.VL015M01A01 (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	custId        IS '統一編號', 
	dupNo         IS '重覆序號', 
	custName      IS '客戶名稱', 
	ownBrId       IS '編製單位代號', 
	docStatus     IS '目前文件狀態', 
	caseDate      IS '簽案日期',	
	docURL        IS '文件URL',
	txCode        IS '交易代碼', 
	createTime    IS '建立日期', 
	updater       IS '異動人員號碼', 
	approver      IS '核准人員號碼', 
	docKind       IS '授權別', 
	docCode       IS '案件別', 
	docType       IS '企/個金案件',
	caseNo        IS '案件號碼', 
	typeShow      IS '文件顯示的名稱', 
	type          IS '文件的種類',
	ngFlag        IS '三個月以上協議案註記(0:無,1:授管處三個月以內,2:債管處三個月以上)' 
);
