initDfd.done(function(json){
	ilog.debug("<EMAIL>" + "rptId=" + json.CLS1201S25Form.rptId); 
	
	var chgrptId = responseJSON.chgrptid;
	
	if(chgrptId=="1"){//重新引入表格的資料
		$.ajax({
			handler : 'cls1141m01formhandler',type : "POST", dataType : "json",
			data : {
				formAction : "cls1201s25_btn_import",
				mainId : responseJSON.mainid
			},
			success : function(json) {
				if(json.fetchForm){
					$("#CLS1201S25Form").injectData(json.fetchForm);
				}
			}
		});		
	}
	
	
	if(json.CLS1201S25Form){
//		var l120s15a_rptId = (json.CLS1201S25Form.rptId || "V2020");
		
//		if(l120s15a_rptId=="V202201"){
			$("#avgMincomeAmt").attr('disabled',true);
//		}
	}
	
	$("#cls1201s25_btn_import").click(function(){
		$.ajax({
			handler : 'cls1141m01formhandler',type : "POST", dataType : "json",
			data : {
				formAction : "cls1201s25_btn_import",
				mainId : responseJSON.mainid
			},
			success : function(respJson) {
				if(respJson.fetchForm){
					$("#CLS1201S25Form").injectData(respJson.fetchForm);
				}
				
				swithVersion(respJson.fetchForm.rptId);
			}
		});		
	});
	
	$("#cls1201s25_btn_del").click(function(){
		//action_003=是否確定「刪除」此筆資料?
		CommonAPI.confirmMessage(i18n.def["action_003"], function(b){
            if (b) {
            	$.ajax({
        			handler : 'cls1141m01formhandler',type : "POST", dataType : "json",
        			data : {
        				formAction : "cls1201s25_btn_del",
        				mainId : responseJSON.mainid
        			},
        			success : function(json) {
        				if(json.fetchForm){
        					$("#CLS1201S25Form").injectData(json.fetchForm);
        					$("#CLS1201S25Form").reset();
        				}
        			}
        		});		
            }
        });
	});

	swithVersion(json.CLS1201S25Form.rptId);
   
});

function swithVersion(rptId){
	
	$(".v202201").hide();
	$(".v202301").hide();

	if(rptId == 'V202201'){
		$(".v202201").show();
	}
	
	if(rptId == 'V202301'){
		$(".v202301").show();
	}

	// 動態塞入序號
	var i = 1;
	$('.serialNo').each(function(){

		var isVisible = $(this).closest('tr').is(':visible');
		if(isVisible){
			$(this).text(i++);
		}
   });
}

