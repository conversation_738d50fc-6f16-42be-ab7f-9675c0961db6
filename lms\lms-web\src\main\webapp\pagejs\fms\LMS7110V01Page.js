var pageAction = {
	handler : 'lms7110formhandler',
	grid : null,
	build : function(){
		pageAction.grid = $("#gridview").iGrid({
			//localFirst: true,
			handler : 'lms7110gridhandler',
			action :  "queryL918m01a",
	        width : 785,
			height: 350,
	        sortname: 'caseDate|caseNo',
	        sortorder: 'desc|asc',
	        shrinkToFit: true,
	        autowidth: false,       
	         postData: {
	            mainDocStatus: viewstatus,
	            rowNum: 15
	        },
       		rowNum: 15,
			//multiselect : true,
			colModel : [{
				colHeader : "oid",
				name : 'oid',
				hidden : true //是否隱藏
			},{
				colHeader : "mainId",
				hidden : true, //是否隱藏
				name : 'mainId'
			},{
				colHeader : "docURL",
				hidden : true, //是否隱藏
				name : 'docURL'
			},{
				colHeader : i18n.lms7110v01["mainGrid.index1"], //分行代號
				align : "left",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'caseBrId'
			},{
				colHeader : i18n.lms7110v01["mainGrid.index2"], //統一編號
	            align: "left",
	            width: 80,
	            sortable: true,
	            formatter: 'click',
	            onclick: pageAction.openBox,
	            name: 'custId'
			},{
				colHeader : i18n.lms7110v01["mainGrid.index3"], //經辦人員
	            align: "left",
	            width: 90,
	            sortable: true,
	            name: 'stopUpdater'
			},{
				colHeader : i18n.lms7110v01["mainGrid.index4"], //查詢日期
	            align: "center",
	            width: 70,
	            sortable: true,
	            name: 'caseDate'
			},{
				colHeader : i18n.lms7110v01["mainGrid.index5"], //案件號碼
	            align: "left",
	            width: 150,
	            sortable: true,
	            name: 'caseNo'
			},{
				colHeader : i18n.lms7110v01["mainGrid.index6"], //覆核人員
	            align: "left",
	            width: 90,
	            sortable: true,				
				name : 'stopApprover'
			},{
				colHeader : i18n.lms7110v01["mainGrid.index7"], //覆核日期
	            align: "left",
	            width: 90,
	            sortable: true,				
				name : 'stopApprTime'
			}
		],
			ondblClickRow: function(rowid){//同修改
				var data = pageAction.grid.getRowData(rowid);
				pageAction.openBox(null, null, data);
			}				
		});
		//build addThick selectes
		
		//build button 
		//查詢
		$("#buttonPanel").find("#btnSearch").click(function() {
			$.ajax({
				handler : pageAction.handler,
				action : 'setAllBranch',
				success:function(response){
					// 設定所有分行代碼Map
		             $("#queryStopForm").find("#caseBrId").setItems({
			             clear: false,
			             item: response.caseBrId,
			             format: "{value} - {key}",
			             space: true
		             });
					pageAction.queryStop();
				}
			});			
		}).end().find("#btnView").click(function(){
			//調閱	    
	        var row = pageAction.grid.getGridParam('selrow');
	        if (!row) {	        
	            // action_004=請先選擇需「調閱」之資料列
	            return CommonAPI.showMessage(i18n.def["action_004"]);	            
	        }else{
				var result = $("#gridview").getRowData(row);
				pageAction.openBox(null, null, result);
			}	        
	    }).end().find("#btnDelete").click(function(){
			//刪除
	        var row = pageAction.grid.getGridParam('selrow');
	        if (!row) {	        
	            // includeId.selData=請選擇一筆資料!!
	            return CommonAPI.showMessage(i18n.def["includeId.selData"]);	            
	        }else{
		        // confirmDelete=是否確定刪除?
				CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
					if (b) {
						var result = $("#gridview").getRowData(row);
						pageAction.startDel(null, null, result);
					}
				});
			}			
		});
	},
	/**
	 * 開啟查詢視窗
	 */	
	queryStop : function(){
		$("#queryStop").thickbox({
			title : i18n.def["query"],//'查詢',
			width : 500,
			height : 230,
			modal : true,
			align : 'center',
			valign: 'bottom',
			i18n: i18n.def,
			buttons : {
				'sure' : function(){
					var $queryStopForm = $("#queryStopForm");
					var caseBrId = $queryStopForm.find("#caseBrId").val();
					var custId = $queryStopForm.find("#custId").val();
					var dupNo = $queryStopForm.find("#dupNo").val();
					var custName = $queryStopForm.find("#custName").val();
					if ($queryStopForm.valid()){
						if((caseBrId == undefined || caseBrId == null || caseBrId == "") &&
							(custId == undefined || custId == null || custId == "")
						){
							// 請輸入分行代號或統一編號
							CommonAPI.showErrorMessage(i18n.lms7110v01["msg.001"]);
							return;
						}else{
							if((custName == undefined || custName == null || custName == "") &&
								(dupNo == undefined || dupNo == null || dupNo == "")){
								// msg.002=借款人名稱與重覆序號為空，可點選「引進」按鈕進行引進，是否仍要執行？
								CommonAPI.confirmMessage(i18n.lms7110v01["msg.002"], function(b){
								if (b) {
										$.thickbox.close();
										var data = {
											caseBrId : (caseBrId == undefined || caseBrId == null || caseBrId == "")? "" : caseBrId,
											custId : (custId == undefined || custId == null || custId == "") ? "" : custId,
											dupNo : (dupNo == undefined || dupNo == null || dupNo == "") ? "" : dupNo,
											custName : (custName == undefined || custName == null || custName == "") ? "" : custName
										};
										// 開始進行查詢
										pageAction.startQuery(data);
									}				
								});								
							}else{
								$.thickbox.close();
								var data = {
									caseBrId : (caseBrId == undefined || caseBrId == null || caseBrId == "")? "" : caseBrId,
									custId : (custId == undefined || custId == null || custId == "") ? "" : custId,
									dupNo : (dupNo == undefined || dupNo == null || dupNo == "") ? "" : dupNo,
									custName : (custName == undefined || custName == null || custName == "") ? "" : custName
								};
								// 開始進行查詢
								pageAction.startQuery(data);
							}							
						}
					}
				},
				'cancel' : function(){
                    API.confirmMessage(i18n.def['flow.exit'], function(res){
                        if (res) {
                            $.thickbox.close();
                        }
                    });
				}
			}
		});		
	},
	/**
	 * 開始刪除資料
	 */	
	startDel : function(cellvalue, options, rowObject){
		$.ajax({
			handler : pageAction.handler,
			action : 'startDel',
	        data: {
	            mainId: rowObject.mainId
	        },
			success:function(response){
				// 更新Grid
				pageAction.reloadGrid();
			}
		});
	},		
	/**
	 * 開始查詢資料(包含建立主檔與明細檔)
	 */
	startQuery : function(data){
		$.ajax({
			handler : pageAction.handler,
			action : 'startQuery',
			data : data,
			success:function(response){
				// 更新Grid
				pageAction.reloadGrid();
				// 查詢後開啟視窗
				pageAction.openBox(null,null,response);
			}
		});
	},	
	/**
	 * 開啟視窗
	 */
	openBox : function(cellvalue, options, rowObject){
		ilog.debug(rowObject);
	    var url = '..' + rowObject.docURL;
		$.form.submit({
	        url: url,
	        data: {
	            mainDocStatus: viewstatus,
	            mainId: rowObject.mainId,
	            mainOid: rowObject.oid,
	            docURL: rowObject.docURL,
	            oid: rowObject.oid
	        },
	        target: "_blank"
	    });
	},
	/**
	 * 取得資料表之選擇列
	 */
	getRowData : function(){
		var row = pageAction.grid.getGridParam('selrow');
		var data;
		if (row) {
			data = pageAction.grid.getRowData(row);
		}else{
			MegaApi.showPopMessage(i18n.def["confirmTitle"],i18n.def["grid.selrow"]);
		}
		return data;
	},
	/**
	 * 重整資料表
	 */
	reloadGrid : function(data){
		if (data){
			pageAction.grid.jqGrid("setGridParam", {
				posinputata : data,
				page : 1,
				search : true
			}).trigger("reloadGrid");
		}else{
			pageAction.grid.trigger('reloadGrid');
		}
	}
}

$(function() {
	pageAction.build();
    $("#getCustData").click(function(){
		var $queryStopForm = $("#queryStopForm");
		var $custId = $queryStopForm.find("#custId").val();
		if(($custId == null || $custId == undefined || $custId == '')){
			// 請輸入統一編號
			CommonAPI.showErrorMessage(i18n.lms7110v01["l120s02.alert27"]);
		}else{
		    var defaultOption = {};
			if($custId != null && $custId != undefined && $custId != ''){
				defaultOption = {
					defaultValue: $custId //預設值 
				};
			}			
			//綁入MegaID
			CommonAPI.openQueryBox(
				$.extend({
/*
	                defaultValue: $custId, //預設值 
	                defaultName : $custName,
*/
					doNewUser: false,
					defaultCustType : ($custId != null && $custId != undefined && $custId != '') ? "1" : "",
	                divId:"queryStopForm", //在哪個div 底下 
	                autoResponse: { // 是否自動回填資訊 
	                           id: "custId", // 統一編號欄位ID 
	                       dupno: "dupNo", // 重覆編號欄位ID 
	                      name: "custName" // 客戶名稱欄位ID 
	                },fn:function(obj){						
					}
				},defaultOption)
			);			
		}
    });	
});