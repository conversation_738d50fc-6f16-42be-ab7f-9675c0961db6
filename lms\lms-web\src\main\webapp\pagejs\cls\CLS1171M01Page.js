var initDfd = $.Deferred(), initAll = $.Deferred();
$(document).ready(function(){
    $.form.init({
        formHandler: "cls1171m01formhandler",
        formPostData: {//把form上貼上資料
            formAction: "queryL141m01a",
            oid: responseJSON.oid
        },
        loadSuccess: function(json){
            initDfd.resolve(json);
            if (responseJSON.page == "01") {
                $("#coReCheck").html(json.coReCheck);
                $("#mainBossShow").html(json.mainBossShow);
            }
            
        }
    });//close form init
    var btn = $("#buttonPanel");
	var item;
    btn.find("#btnSend").click(function(){
        sendBoss();
    }).end().find("#btnCheck").click(function(){
        openCheck();
    }).end().find("#btnPrint").click(function(){
        $("#cb_printGrid").removeAttr("disabled");
        gridPrint.trigger("reloadGrid");
        $("#printView").thickbox({ // 使用選取的內容進行彈窗
            title: i18n.def['print'],
            width: 700,
            height: 400,
            readOnly: false,
            modal: true,
            buttons: (function(){
                var btn = {};
                btn[i18n.def['print']] = function(){
                    var pdfName = "";
                    var count = 0;
                    var content = "";
                    var id = gridPrint.getGridParam('selarrrow');
                    for (var i = 0; i < id.length; i++) {
                        if (id[i] != "") {
                            var datas = gridPrint.getRowData(id[i]);
                            content = content + datas.rpt + "^" + datas.oid + "^" + datas.custId + "^" + datas.dupNo + "^" + datas.cntrNo + "^" + datas.refMainId + "|";
                            pdfName = datas.rptNo + ".pdf";
                            count++;
                        }
                    }
                    
                    if (content.length != 0) {
                        content = content.substring(0, content.length - 1);
                    }
                    if (count == 0) {
                        CommonAPI.showMessage(i18n.def['grid.selrow']);
                    } else {
                        if (count != 1) {
                            pdfName = "CLS1151R01.pdf";
                        }
                        $.form.submit({
                            url: "../../simple/FileProcessingService",
                            target: "_blank",
                            data: {
                                //srcMainId 原案簽報書mainId
                                mainId: $("#srcMainId").val(),
                                rptOid: content,
                                fileDownloadName: pdfName,
                                serviceName: "cls1141r01rptservice"
                            }
                        });
                    }
                };
                btn[i18n.def['close']] = function(){
                    $.thickbox.close();
                };
                return btn;
            })()
        });
        
    });
    
    
    
    var gridPrint = $("#printGrid").iGrid({
        handler: 'cls1171gridhandler',
        height: 270,
        rownumbers: true,
        multiselect: true,
        hideMultiselect: false,
        caption: "&nbsp;",
        hiddengrid: false,
        postData: {
            formAction: "queryPrint"
        },
        colModel: [{
            colHeader: i18n.cls1141m01['print.custName'],// "借款人名稱",
            name: 'custName',
            width: 120,
            sortable: true
        }, {
            colHeader: i18n.cls1141m01['print.rptNo'],// "報表編號",
            name: 'rptNo',
            align: "center",
            width: 40,
            sortable: true
        }, {
            colHeader: i18n.cls1141m01['print.rptName'],// "報表名稱",
            name: 'rptName',
            width: 70,
            sortable: true
        }, {
            colHeader: i18n.cls1141m01['print.cntrNo'],// "額度序號",
            name: 'cntrNo',
            align: "center",
            width: 50,
            sortable: true
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }, {
            colHeader: "rpt",
            name: 'rpt',
            hidden: true
        }, {
            colHeader: "custId",
            name: 'custId',
            hidden: true
        }, {
            colHeader: "dupNo",
            name: 'dupNo',
            hidden: true
        }, {
            colHeader: "refMainId",
            name: 'refMainId',
            hidden: true
        }]
    });
    //呈主管覆核 選授信主管人數
    $("#numPerson").change(function(){
         $('#bossItem').empty();
        var value = $(this).val();
        if (value) {
            var html = '';
            for (var i = 1; i <= value; i++) {
                var name = 'boss' + i;
                html += i + '. ' 
                html += '<select id="' + name + '" name="boss"' +
                '" class="required" CommonManager="kind:2;type:2" />';
                html += '<br/>';
            }
            $('#bossItem').append(html).find('select').each(function(){
                $(this).setItems({
                    item: item,
                    format: "{value} {key}"
                });
            });
        }
    });
    
    /** 呈主管 -  編製中 */
    function sendBoss(){
        $("#numPerson").removeAttr("disabled");
        $.ajax({
            handler: "cls1171m01formhandler",
            data: {
                formAction: "queryBoss"
            },
            success: function(json){
			   item = json.bossList;
               $('#managerItem').empty();
			   $('#bossItem').empty();
			   $('#accountingItem').empty();
			   
               var ahtml = '<select id="accounting" name="accounting" class="required" CommonManager="kind:2;type:1"/>';
               $('#accountingItem').append(ahtml).find('select').each(function(){
                   $(this).setItems({
                       item: item,
                       format: "{value} {key}"
                   });
               });
			   
			   var bhtml = '1. <select id="boss1" name="boss" class="required" CommonManager="kind:2;type:2"/>';
               $('#bossItem').append(bhtml).find('select').each(function(){
                   $(this).setItems({
                       item: item,
                       format: "{value} {key}"
                   });
               });
               var html = '<select id="manager" name="manager" class="required" CommonManager="kind:2;type:3" />';
               $('#managerItem').append(html).find('select').each(function(){
                   $(this).setItems({
                       item: item,
                       format: "{value} {key}"
                   });
               });
                //L141M01A.btn04=呈主管覆核
                CommonAPI.confirmMessage(i18n.cls1171m01["L141M01A.messag001"], function(b){
                    if (b) {
                        $("#selectBossForm").find("select").removeAttr("disabled");
                        $("#selectBossBox").thickbox({ // 使用選取的內容進行彈窗
                            //L141M01A.btn04=呈主管覆核
                            title: i18n.cls1171m01['L141M01A.btn04'],
                            width: 500,
                            height: 300,
                            modal: true,
                            valign: "bottom",
                            align: "center",
                            readOnly: false,
                            i18n: i18n.def,
                            buttons: {
                                "sure": function(){
                                    var $selectBossForm = $("#selectBossForm");
                                    var accounting = $selectBossForm.find("select#accounting").val();
                                    var selectBoss = $("select[name^=boss]", $selectBossForm).map(function(){
                                        return $(this).val();
                                    }).toArray();
                                    
                                    var manager = $selectBossForm.find("select#manager").val();
                                    
                                    for (var id in selectBoss) {
                                        if (selectBoss[id] == "") {
                                            //L141M01A.bossId=授信主管
                                            return CommonAPI.showErrorMessage(i18n.cls1171m01['L141M01A.error3'] + i18n.cls1171m01['L141M01A.bossId']);
                                        }
                                    }
                                    //驗證是否有重複的主管
                                    if (checkArrayRepeat(selectBoss)) {
                                        //L141M01A.error2 = 主管人員名單重複請重新選擇
                                        return CommonAPI.showErrorMessage(i18n.cls1171m01['L141M01A.error2']);
                                    }
                                    
                                    
                                    if (!manager || manager == "") {
                                        //L141M01A.title010=單位/授權主管
                                        return CommonAPI.showErrorMessage(i18n.cls1171m01['L141M01A.error3'] + i18n.cls1171m01['L141M01A.title010']);
                                    }
                                    
                                    flowAction({
                                        page: responseJSON.page,
                                        saveData: true,
                                        selectBoss: selectBoss,
                                        manager: manager,
                                        accounting: accounting
                                    });
                                    
                                    $.thickbox.close();
                                    
                                },
                                "cancel": function(){
                                    $.thickbox.close();
                                }
                            }
                        });
                    }
                });
            }
        });
    }
    
    
    /**待覆核  - 覆核  */
    function openCheck(){
        $("#openCheckBox").thickbox({ // 使用選取的內容進行彈窗
            //L141M01A.btn03=覆核
            title: i18n.cls1171m01['L141M01A.btn03'],
            width: 100,
            height: 100,
            modal: true,
            valign: "bottom",
            align: "center",
            
            i18n: i18n.def,
            readOnly: false,
            buttons: {
                "sure": function(){
                
                    var val = $("[name=checkRadio]:checked").val();
                    if (!val) {
                        //L141M01A.error3
                        return CommonAPI.showMessage(i18n.cls1171m01['L141M01A.error3']);
                    }
                    $.thickbox.close();
                    switch (val) {
                        case "1":
                            //一般退回到編製中010
                            //L141M01A.messag002=該案件是否退回經辦修改？要退回請按【確定】，不退回請按【取消】
                            CommonAPI.confirmMessage(i18n.cls1171m01['L141M01A.messag002'], function(b){
                                if (b) {
                                    flowAction({
                                        flowAction: "back"
                                    });
                                }
                            });
                            
                            break;
                        case "2":
                            //L141M01A.messag003=該案件是否確定執行核定
                            CommonAPI.confirmMessage(i18n.cls1171m01["L141M01A.messag003"], function(b){
                                if (b) {
                                    checkDate();
                                }
                            });
                            break;
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
    
    /**  輸入核定日期視窗  */
    function checkDate(){
        //帶入今天日期
        $("#forCheckDate").val(CommonAPI.getToday());
        $("#openChecDatekBox").thickbox({ // 使用選取的內容進行彈窗
            //L141M01A.messag005=請輸入核定日
            title: i18n.cls1171m01['L141M01A.messag005'],
            width: 100,
            height: 100,
            modal: true,
            valign: "bottom",
            align: "center",
            readOnly: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var forCheckDate = $("#forCheckDate").val();
                    if ($.trim(forCheckDate) == "") {
                        //L141M01A.messag005=請輸入核定日
                        return CommonAPI.showErrorMessage(i18n.cls1171m01['L141M01A.messag005']);
                    }
                    flowAction({
                        flowAction: "check",
                        checkDate: forCheckDate
                    });
                    $.thickbox.close();
                },
                
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
    
    
    /** 流程動作   */
    function flowAction(sendData){
        $.ajax({
            handler: "cls1171m01formhandler",
            data: $.extend({
                formAction: "flowAction",
                mainOid: $("#oid").val()
            }, (sendData || {})),
            success: function(){
                CommonAPI.triggerOpener("gridview", "reloadGrid");
                API.showPopMessage(i18n.def["runSuccess"], window.close);
            }
        });
    }
    
    /**  檢查陣列內容是否重複  */
    function checkArrayRepeat(arrVal){
        var newArray = [];
        for (var i = arrVal.length; i--;) {
            var val = arrVal[i];
            if ($.inArray(val, newArray) == -1) {
                newArray.push(val);
            } else {
                return true;
            }
        }
        return false;
    }
    
});



