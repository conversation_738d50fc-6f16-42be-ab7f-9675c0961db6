package com.mega.eloan.lms.cls.panels;

import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * 整批自動開戶 - 詳細資料
 * </pre>
 * 
 * @since 2017/03/05
 * <AUTHOR>
 * @version <ul>
 *          <li>2017/03/05,EL08034,new
 *          </ul>
 */
public class CLS1161S32Panel extends Panel {

	public CLS1161S32Panel(String id) {
		super(id);
	}

	public CLS1161S32Panel(String id, boolean updatePanelName) {
		super(id, updatePanelName);
	}

	/**/
	private static final long serialVersionUID = 1L;
}
