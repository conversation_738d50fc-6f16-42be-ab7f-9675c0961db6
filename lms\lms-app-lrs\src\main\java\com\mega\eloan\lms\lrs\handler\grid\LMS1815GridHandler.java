/* 
 * LMS1815GridHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lrs.handler.grid;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import com.iisigroup.cap.component.PageParameters;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.lrs.service.LMS1815Service;
import com.mega.eloan.lms.model.L181M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * <pre>
 * 覆審控制檔維護交易
 * </pre>
 * 
 * @since 2011/9/21
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/9/21,irene,new
 *          </ul>
 */
@Scope("request")
@Controller("lms1815gridhandler")
public class LMS1815GridHandler extends AbstractGridHandler {

	@Resource
	UserInfoService userservice;

	@Resource
	BranchService branch;

	@Resource
	LMS1815Service service;

	/**
	 * 查詢Grid 覆審名單檔 資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	public CapGridResult query(ISearch pageSetting, PageParameters params) throws CapException {

		// 建立主要Search 條件
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String docStatus = Util.nullToSpace(params
				.getString(EloanConstants.DOC_STATUS));
		String custId = Util.nullToSpace(params.getString("custId"));
		if (Util.isNotEmpty(custId)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId",
					custId);
		}
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.DOC_STATUS, docStatus);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l181a01a.authUnit", user.getUnitNo());
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				null);
		if (RetrialDocStatusEnum.已核准.getCode().equals(docStatus)) {
			String branch = Util.nullToSpace(params.getString("branch"));
			Date beforeDate = TWNDate.valueOf(Util.nullToSpace(params
					.getString("beforeDate", "0001-01-01")));
			Date afterDate = TWNDate.valueOf(params.getString("afterDate",
					TWNDate.toFullAD(new Date())));
			//新增日期轉型
			Date startTime = CapDate.parseDate(TWNDate.toAD(beforeDate) + " 00:00:00");
			Date endTime = CapDate.parseDate(TWNDate.toAD(afterDate) + " 23:59:59");
			Object[] reason = { startTime, endTime };
			
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "elfBranch",
					branch);
			pageSetting.addSearchModeParameters(SearchMode.BETWEEN,
					"approveTime", reason);
		}
		pageSetting.addOrderBy("elfBranch");
		pageSetting.addOrderBy("approveTime");
		pageSetting.addOrderBy("custId");
		pageSetting.addOrderBy("dupNo");
		Page page = service.findPage(L181M01A.class, pageSetting);
		List<L181M01A> list = page.getContent();
		for (L181M01A l181m01a : list) {

			IBranch ibranch = branch.getBranch(l181m01a.getElfBranch());
			if (ibranch != null) {
				l181m01a.setElfBranch(l181m01a.getElfBranch() + " "
						+ ibranch.getBrName());
			}
			l181m01a.setCustId(Util.nullToSpace(l181m01a.getCustId()) + " "
					+ Util.nullToSpace(l181m01a.getDupNo()));
			l181m01a.setUpdater(Util.trim(userservice.getUserName(l181m01a
					.getUpdater())));
		}

		return new CapGridResult(list, page.getTotalRow());
	}

}
