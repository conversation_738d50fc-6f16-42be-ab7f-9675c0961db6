/* 
 * CMSDocStatusEnum.java
 *
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.flow.enums;

import java.util.Properties;

import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.pages.AbstractEloanPage;

/**<pre>
 * 進件管理文件狀態
 * </pre>
 * @since  2012/12/26
 * <AUTHOR>
 * @version <ul>
 *           <li>2012/12/26,Fantasy,new
 *          </ul>
 */
public enum IncomDocStatusEnum {
	
	待派案("A00"),
	已派案("A01"),
	補件通知("A02"),
	待初審("A99"),
	系統徵信("B00"),
	<PERSON>Y<PERSON>("B01"),
	完成照會("B02"),
	簽案作業("C00"),
	簽案待覆核("C01"),
	簽案已覆核("C02"),
	契約作業("D00"),
	契約待覆核("D01"),
	契約已覆核("D02"),
	契約已簽定("D03"),
	定約通知("D04"),
	動用作業("E00"),
	動用待覆核("E01"),
	動用已覆核("E02"),
	已作廢("F00"),
	不承作("G00"),
	票債信異常("G01"),
	信評未通過("G02"),
	客戶撤件("G03"),
	估價作業("H00"),
	估價待覆核("H01"),
	估價已覆核("H02");


	private String code;
	private static Properties pop = MessageBundleScriptCreator
			.getComponentResource(AbstractEloanPage.class);

	IncomDocStatusEnum(String code) {
		this.code = code;
	}

	public String getCode() {
		return code;
	}

	public String getName() {
		return pop.getProperty("status." + this.code,
				EloanConstants.EMPTY_STRING);
	}

	public boolean isEquals(Object other) {
		if (other instanceof String) {
			return code.equals(other);
		} else {
			return super.equals(other);
		}
	}

	public static String getMessage(CLSDocStatusEnum status) {
		return getMessage(status.toString());
	}

	public static String getMessage(String status) {
		return pop.getProperty("CLSDocStatus." + status, EloanConstants.EMPTY_STRING);
	}

	public static CLSDocStatusEnum getEnum(String code) {
		for (CLSDocStatusEnum enums : CLSDocStatusEnum.values()) {
			if (enums.isEquals(code)) {
				return enums;
			}
		}
		return null;
	}

	@Override
	public String toString() {
		return code;
	}

}
