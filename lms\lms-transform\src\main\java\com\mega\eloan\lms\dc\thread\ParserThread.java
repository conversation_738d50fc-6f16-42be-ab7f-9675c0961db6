package com.mega.eloan.lms.dc.thread;

import com.mega.eloan.cls.dc.action.AbstractCLSCustParser;
import com.mega.eloan.cls.dc.action.ClsParser120M01F;
import com.mega.eloan.cls.dc.action.ClsParser140M01B;
import com.mega.eloan.cls.dc.action.ClsParserL140S02B;
import com.mega.eloan.lms.dc.action.AbstractLMSCustParser;
import com.mega.eloan.lms.dc.action.DXLParser;
import com.mega.eloan.lms.dc.action.Parser120M01F;
import com.mega.eloan.lms.dc.action.Parser140M01B;
import com.mega.eloan.lms.dc.action.Parser140M01C;
import com.mega.eloan.lms.dc.action.Parser140M01CBF;
import com.mega.eloan.lms.dc.action.Parser140M01D;
import com.mega.eloan.lms.dc.action.Parser140M01DBF;
import com.mega.eloan.lms.dc.action.Parser140M01F;
import com.mega.eloan.lms.dc.action.Parser140M01I;
import com.mega.eloan.lms.dc.action.Parser140M01K;
import com.mega.eloan.lms.dc.base.DCException;
import com.mega.eloan.lms.dc.conf.ConfigData;
import com.mega.eloan.lms.dc.util.TextDefine;

/**
 * <pre>
 * ParserThread
 * </pre>
 * 
 * @since 2012/12/20
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/20,Bang,new
 *          </ul>
 */
public class ParserThread extends Thread {

	private String viewname = "";
	private String schema = "";
	private ConfigData config = null;

	public ParserThread() {
		super();
	}

	/**
	 * Constructor
	 * 
	 * @param schema
	 *            String:目前執行的系統名稱
	 * @param vn
	 *            String :目前讀取的viewListName
	 * @param pps
	 *            :Properties 2013-01-28 Modify By Bang:加入個金判斷 (schema)
	 */
	public ParserThread(String schema, String vn) {
		this.schema = schema;
		this.viewname = vn;
	}

	public void run() {
		DXLParser dp = new DXLParser();
		dp.setConfigData(config);
		dp.doParser(this.schema, this.viewname);
		String firstView = "";
		if (TextDefine.SCHEMA_LMS.equalsIgnoreCase(this.schema)) {
			// 以下獨立轉出之Table不寫在DXLParser裡,為了活用性且看起來太雜亂
			
			String[] viewList  = dp.getConfigData().getLMSViewName().split(";");
			if(viewList!=null &&viewList.length>0){
				firstView = viewList[0];
			}else{
				throw new DCException("Config 中的LMS View List未設定！");
			}
			AbstractLMSCustParser pb = new Parser140M01B("L140M01B",
					TextDefine.LMS_PARSER14020, TextDefine.L140M01B_FORM);
			pb.setConfigData(config);
			pb.doParser(this.viewname);

			AbstractLMSCustParser pd = new Parser140M01D("L140M01D",
					TextDefine.LMS_PARSER14020, TextDefine.L140M01D_FORM);
			pd.setConfigData(config);
			pd.doParser(this.viewname);

			AbstractLMSCustParser pdbf = new Parser140M01DBF("L140M01D_BF",
					TextDefine.LMS_PARSER14020, TextDefine.L140M01D_BF_FORM);
			pdbf.setConfigData(config);
			pdbf.doParser(this.viewname);

			AbstractLMSCustParser pf = new Parser140M01F("L140M01F",
					TextDefine.LMS_PARSER14020, TextDefine.L140M01F_FORM);
			pf.setConfigData(config);
			pf.doParser(this.viewname);

			AbstractLMSCustParser pi = new Parser140M01I("L140M01I",
					TextDefine.LMS_PARSER14020, TextDefine.L140M01I_FORM);
			pi.setConfigData(config);
			pi.doParser(this.viewname);

			AbstractLMSCustParser pc = new Parser140M01C("L140M01C",
					TextDefine.LMS_PARSER14020, TextDefine.L140M01C_FORM);
			pc.setConfigData(config);
			pc.doParser(this.viewname);

			AbstractLMSCustParser pcbf = new Parser140M01CBF("L140M01C_BF",
					TextDefine.LMS_PARSER14020, TextDefine.L140M01C_BF_FORM);
			pcbf.setConfigData(config);
			pcbf.doParser(this.viewname);

			//將簽報書的view name以config檔中的為主使用
			/*AbstractLMSCustParser pF = new Parser120M01F("L120M01F",
					TextDefine.LMS_PARSERDB201B, TextDefine.L120M01F_FORM);*/
			AbstractLMSCustParser pF = new Parser120M01F("L120M01F",
					firstView, TextDefine.L120M01F_FORM);
			pF.setConfigData(config);
			pF.doParser(this.viewname);

			AbstractLMSCustParser pK = new Parser140M01K("L140M01K",
					TextDefine.LMS_PARSER14020, TextDefine.L140M01K_FORM);
			pK.setConfigData(config);
			pK.doParser(this.viewname);
		} else {
			// L140M01B
			String cls140M01BAllForm = TextDefine.CLS_L140M01B_FORM_11501
					+ TextDefine.SYMBOL_SEMICOLON
					+ TextDefine.CLS_L140M01B_FORM_71501;
			AbstractCLSCustParser cpb = new ClsParser140M01B("L140M01B",
					TextDefine.CLS_PARSER10130, cls140M01BAllForm);
			cpb.setConfigData(config);
			cpb.doParser(this.viewname);

			// L140S02B
			String cls140S02BAllForm = TextDefine.CLS_L140S02B_FORM_109M01
					+ TextDefine.SYMBOL_SEMICOLON
					+ TextDefine.CLS_L140S02B_FORM_109M02
					+ TextDefine.SYMBOL_SEMICOLON
					+ TextDefine.CLS_L140S02B_FORM_109M03;
			AbstractCLSCustParser cpsb = new ClsParserL140S02B("L140S02B",
					TextDefine.CLS_PARSER10105Z, cls140S02BAllForm);
			cpsb.setConfigData(config);
			cpsb.doParser(this.viewname);

			AbstractCLSCustParser cpF = new ClsParser120M01F("L120M01F",
					TextDefine.CLS_PARSER10105Z, TextDefine.CLS_L120M01F_FORM);
			cpF.setConfigData(config);
			cpF.doParser(this.viewname);

		}
		// 匯整所有.err檔成一份文字檔
		dp.combineErr();

	}

	public void parser(ConfigData config) {
		try {
			this.setConfig(config);
			this.run();
		} catch (DCException e) {
			throw new DCException("Parser 時產生錯誤...", e);
		}
	}

	/**
	 * set the config
	 * 
	 * @param config
	 *            the config to set
	 */
	public void setConfig(ConfigData config) {
		if (config != null) {
			this.config = config;
		}
	}

}
