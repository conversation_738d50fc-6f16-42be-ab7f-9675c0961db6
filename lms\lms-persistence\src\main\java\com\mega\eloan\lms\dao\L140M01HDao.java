/* 
 * L140M01HDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140M01H;

/** 額度費率明細檔 **/
public interface L140M01HDao extends IGenericDao<L140M01H> {

	L140M01H findByOid(String oid);
	
	List<L140M01H> findByMainId(String mainId);
	
	L140M01H findByUniqueKey(String mainId,Integer rateSeq);
	
	List <L140M01H> findByMainIdAndRateSeq(String mainId,Integer rateSeq);
}