/* 
 * L120M01EDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L120M01EDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L120M01E;

/** 相關文件資料檔 **/
@Repository
public class L120M01EDaoImpl extends LMSJpaDao<L120M01E, String> implements
		L120M01EDao {

	@Override
	public L120M01E findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120M01E> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L120M01E> list = createQuery(L120M01E.class, search)
				.getResultList();
		return list;
	}

	@Override
	public L120M01E findByUniqueKey(String mainId, String docType,
			String docURL, String docOid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "docType", docType);
		// search.addSearchModeParameters(SearchMode.EQUALS, "docURL", docURL);
		search.addSearchModeParameters(SearchMode.EQUALS, "docOid", docOid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120M01E> findByIndex01(String mainId, String docType,
			String docURL, String docOid) {
		ISearch search = createSearchTemplete();
		List<L120M01E> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (docType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "docType",
					docType);
		if (docURL != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "docURL", docURL);
		if (docOid != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "docOid", docOid);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(L120M01E.class, search).getResultList();
		}
		return list;
	}

	@Override
	public List<L120M01E> findByCustIdDupId(String custId, String DupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "docCustId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "docDupNo", DupNo);
		List<L120M01E> list = createQuery(L120M01E.class, search)
				.getResultList();
		return list;
	}

	@Override
	public List<L120M01E> findByMainIdAndDocType(String mainId, String docType) {
		ISearch search = createSearchTemplete();
		List<L120M01E> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (docType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "docType",
					docType);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(L120M01E.class, search).getResultList();
		}
		return list;
	}

	@Override
	public List<L120M01E> findByOids(String[] oids) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.IN, "oid", oids);
		List<L120M01E> list = createQuery(L120M01E.class, search)
				.getResultList();
		return list;
	}
	
	@Override
	public List<L120M01E> findByMainIdAndDocTypeDocCustIdDocDupNo(String mainId, String docType, String docCustId, String docDupNo){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "docType", docType);
		search.addSearchModeParameters(SearchMode.EQUALS, "docCustId", docCustId);
		search.addSearchModeParameters(SearchMode.EQUALS, "docDupNo", docDupNo);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L120M01E> list = createQuery(L120M01E.class, search).getResultList();
		return list;
	}
}