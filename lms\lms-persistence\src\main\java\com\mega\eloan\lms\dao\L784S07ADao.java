/* 
 * L784S07ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L784S07A;

/** 常董會及申報案件明細檔 **/
public interface L784S07ADao extends IGenericDao<L784S07A> {

	L784S07A findByOid(String oid);

	List<L784S07A> findByMainId(String mainId);

	List<L784S07A> findByMainIdBrNo(String mainId,String brNo);

	L784S07A findByUniqueKey(String mainId, String brNo, String apprYY,
			String apprMM, String caseDept);

	List<L784S07A> findByIndex01(String mainId, String brNo, String apprYY,
			String apprMM, String caseDept);
	
	List<L784S07A> findByMainIdApprYM(String mainId, String apprYY,
			String apprMM, String caseDept);
	
}