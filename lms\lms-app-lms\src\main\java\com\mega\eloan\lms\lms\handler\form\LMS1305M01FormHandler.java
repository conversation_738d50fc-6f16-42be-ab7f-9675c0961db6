/* 
 * LMS1305FormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.handler.form;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.formatter.BranchDateTimeFormatter;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.utils.BeanValidator;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.lms.pages.LMS1205M01Page;
import com.mega.eloan.lms.lms.pages.LMSS02Page;
import com.mega.eloan.lms.lms.panels.LMSS02Panel;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01D;
import com.mega.eloan.lms.model.L120M01F;
import com.mega.eloan.lms.model.L120S01A;
import com.mega.eloan.lms.model.L120S01B;
import com.mega.eloan.lms.model.L120S01D;
import com.mega.eloan.lms.model.L120S01E;
import com.mega.eloan.lms.model.L120S01F;
import com.mega.eloan.lms.model.L120S01G;
import com.mega.eloan.lms.model.L120S17A;
import com.mega.eloan.lms.model.L121M01B;
import com.mega.eloan.lms.model.L130M01A;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.lms.validation.group.Check2;
import com.mega.eloan.lms.validation.group.Check3;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 授信簽報書(陳復(述)案) FormHandler
 * </pre>
 * 
 * @since 2011/8/6
 * <AUTHOR> Lin
 * @version <ul>
 *          <li>2011/8/6,Miller Lin,new
 *          </ul>
 */
@Scope("request")
@Controller("lms1305formhandler")
@DomainClass(L120M01A.class)
public class LMS1305M01FormHandler extends LMSM01FormHandler {

	/**
	 * 將使用者選擇集團資料代入至借款人隸屬集團
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getGrpData(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String selKey = params.getString("selKey");
		String selVal = params.getString("selVal");
		CapAjaxFormResult formL120s01a = new CapAjaxFormResult();
		// setItem 把Key 和Value設顛倒了，所以在這邊Key = value, Value = key
		formL120s01a.set("groupNo", selVal);
		formL120s01a.set("groupName", selKey.substring(5));
		result.set("L120S01aForm", formL120s01a);
		return result;
	}

	/**
	 * <pre>
	 * 新增借款人主檔(企金)
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return IResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult addBorrowMain(PageParameters params)
			throws CapException {
		IBranch iBranch = branch.getBranch(MegaSSOSecurityContext.getUnitNo());
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String docType = Util.trim(params.getString("docType"));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String custName = Util.trim(params.getString("custName"));
		String buscd = Util.trim(params.getString("buscd"));
		String renCd = Util.trim(params.getString("renCd"));
		boolean check = params.getBoolean("check");
		JSONObject addborrowJson = new JSONObject();
		addborrowJson.put("docType", docType);
		addborrowJson.put("typCd", "5");
		addborrowJson.put("custId", custId);
		addborrowJson.put("dupNo", dupNo);
		addborrowJson.put("custName", Util.truncateString(custName, 120));
		addborrowJson.put(EloanConstants.MAIN_ID, mainId);
		addborrowJson.put("creator", user.getUserId());
		addborrowJson
				.put("createTime", new BranchDateTimeFormatter(iBranch)
						.reformat(CapDate.parseToString(CapDate
								.getCurrentTimestamp())));

		JSONObject jsonCustClass = new JSONObject();
		jsonCustClass = getCustBusCDAndClass(custId, dupNo);

		addborrowJson.put("busCode", jsonCustClass.getString("busCode"));
		addborrowJson.put("bussKind", jsonCustClass.getString("bussKind"));
		addborrowJson.put("ecoNm", jsonCustClass.getString("ecoNm"));
		addborrowJson.put("ecoNm07A", jsonCustClass.getString("ecoNm07A"));
		addborrowJson.put("custClass", jsonCustClass.getString("custClass"));
		addborrowJson.put("displayBusCd",
				jsonCustClass.getString("displayBusCd"));

		L120M01A l120m01a = service1205.findL120m01aByMainId(mainId);
		CapAjaxFormResult showBorrowData = new CapAjaxFormResult();
		if (check) {
			addborrowJson.put("keyMan", "Y");
			// 當是主要借款人且為第一次新增時
			l120m01a.setCustId(custId);
			l120m01a.setDupNo(dupNo);
			l120m01a.setCustName(Util.truncateString(custName, 120));
			String typCd = "5";
			l120m01a.setTypCd(typCd);
			showBorrowData.set("custId", custId);
			showBorrowData.set("dupNo", dupNo);
			showBorrowData.set("custName", Util.truncateString(custName, 120));
			showBorrowData.set("typCd", getMessage("typCd." + typCd));
			// showBorrowData.set("typCd", TypCdEnum.getEnum(typCd).name());

			// J-110-0458 企金授權內其他 - 「簡易簽報」選項，適用方案「LIBOR退場變更利率條件簡易簽報」
			// 說明塞預設值
			if (lmsService.isLiborExitCase(l120m01a)) {
				Map<String, String> dscrMap = codeService.findByCodeType(
						"liborExitDscr", LMSUtil.getLocale().toString());
				L120M01D l120m01d03 = service1205.findL120m01dByUniqueKey(
						mainId, UtilConstants.Casedoc.L120m01dItemType.其他);
				if (l120m01d03 != null) {
					// 如果是空的，就可以塞值
					if (Util.isEmpty(Util.nullToSpace(l120m01d03.getItemDscr()))) {
						// 依據授信審查處中華民國XX.XX.XX總授審字第XXXXXX函辦理。
						String dscr = Util.nullToSpace(dscrMap.get("dscr"));
						l120m01d03.setItemDscr(dscr);
						service1205.save(l120m01d03);
					}
				}
			}
			if (lmsService.isEuroyenTiborExitCase(l120m01a)) {
				Map<String, String> dscrMap = codeService.findByCodeType(
						"EuroyenTiborExitDscr", LMSUtil.getLocale().toString());
				L120M01D l120m01d03 = service1205.findL120m01dByUniqueKey(
						mainId, UtilConstants.Casedoc.L120m01dItemType.其他);
				if (l120m01d03 != null) {
					if (Util.isEmpty(Util.nullToSpace(l120m01d03.getItemDscr()))) {
						String dscr = Util.nullToSpace(dscrMap.get("dscr"));
						l120m01d03.setItemDscr(dscr);
						service1205.save(l120m01d03);
					}
				}
			}

			result.set("showBorrowData", showBorrowData);
		} else {
			addborrowJson.put("keyMan", "N");
		}
		// 以下建立相關聯的資料表
		L120S01A l120s01a = new L120S01A();
		L120S01B l120s01b = new L120S01B();
		L120S01D l120s01d = new L120S01D();
		L120S01F l120s01f = new L120S01F();
		L120S01G l120s01g_1 = new L120S01G();
		L120S01G l120s01g_2 = new L120S01G();
		// 到此結束
		DataParse.toBean(addborrowJson, l120s01a); // 將addborrowJson data 置入
		l120s01a.setRenCd(Util.trim(renCd));
		l120s01a.setBusCode(Util.trim(buscd));
		// L120S01A
		DataParse.toBean(addborrowJson, l120s01b); // 將addborrowJson data 置入
													// L120S01B
		DataParse.toBean(addborrowJson, l120s01d); // 將addborrowJson data 置入
													// L120S01D
		DataParse.toBean(addborrowJson, l120s01f); // 將addborrowJson data 置入
													// L120S01F
		DataParse.toBean(addborrowJson, l120s01g_1); // 將addborrowJson data 置入
														// L120S01G_1
		DataParse.toBean(addborrowJson, l120s01g_2); // 將addborrowJson data 置入
														// L120S01G_2
		// 以下設定值給新建立的資料表
		l120s01g_1.setDataType("1");
		l120s01g_1.setDataDscr("");
		l120s01g_2.setDataType("2");
		l120s01g_2.setDataDscr("");
		// 再來儲存資料
		L120M01A model = service1205.findL120m01aByMainId(mainId);
		service1205.save(model, l120s01a, l120s01b, l120s01d, l120s01f,
				l120s01g_1, l120s01g_2);
		result.set(EloanConstants.OID, l120s01a.getOid());
		CapAjaxFormResult tadd = DataParse.toResult(l120s01a); // 處理新增後客戶型態顯示
		CapAjaxFormResult tadd2 = new CapAjaxFormResult(); // 處理營運概況分析評估
		CapAjaxFormResult tadd3 = new CapAjaxFormResult(); // 處理財務概況分析評估
		CapAjaxFormResult tadd4 = new CapAjaxFormResult(jsonCustClass); // 行業對象別與客戶類別

		// 依據不同的客戶型態顯示相對應結果
		tadd.set("typCd", getMessage("typCd." + l120s01a.getTypCd()));
		// tadd.set("typCd", TypCdEnum.getEnum(l120s01a.getTypCd()).name());
		tadd.set("invMDscr", "");
		tadd.set("_renCd", renCd);
		tadd.set("_buscd", buscd);
		tadd2.set("idDscr1", space);
		tadd3.set("idDscr2", space);

		tadd.add(tadd4);
		result.set("L120S01aForm", tadd); // 將model轉為回傳物件並置指定的formName
		result.set("formIdDscr1", tadd2); // 將model轉為回傳物件並置指定的formName
		result.set("formIdDscr2", tadd3); // 將model轉為回傳物件並置指定的formName
		return result;
	}

	/**
	 * <pre>
	 * 查詢銀行法及金控法44 45條(企金)
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return IResult CapAjaxFormResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getRlt(PageParameters params)
			throws CapException {
		return super._LMSM01FormHandler_getRlt(params);
	}

	/**
	 * <pre>
	 * 查詢銀行法及金控法44 45條(個金)
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return IResult CapAjaxFormResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getRlt2(PageParameters params)
			throws CapException {
		return super._LMSM01FormHandler_getRlt2(params);
	}

	/**
	 * 查詢主從債務人是否有資料(個金)
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getSel(PageParameters params)
			throws CapException {
		return super._LMSM01FormHandler_getSel(params);
	}

	/**
	 * <pre>
	 * 新增借款人主檔(個金)
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return IResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult addBorrowMain2(PageParameters params)
			throws CapException {
		return super._LMSM01FormHandler_addBorrowMain2(params);
	}

	/**
	 * <pre>
	 * 刪除-(個金)(借款人資料)
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return IResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteBorrowMain2(PageParameters params)
			throws CapException {
		return super._LMSM01FormHandler_deleteBorrowMain2(params);
	}

	/**
	 * 引進借款人資料(個金)
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getCustData3(PageParameters params)
			throws CapException {
		return super._LMSM01FormHandler_getCustData3(params);
	}

	/**
	 * 引進借款人配偶資料(個金)
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getCustData3m(PageParameters params)
			throws CapException {
		return super._LMSM01FormHandler_getCustData3m(params);
	}

	/**
	 * 設定使用者所選擇的申貸戶資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult setCustData3(PageParameters params)
			throws CapException {
		return super._LMSM01FormHandler_setCustData3(params);
	}

	/**
	 * 引進徵信資信簡表取得營運概況與財務狀況及存放款外匯往來情形
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult getL120s01e(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String cesMainId1 = params.getString("cesMainId1");
		String cesMainId2 = params.getString("cesMainId2");
		String thisOid = params.getString("thisOid");
		String[] finItem = params.getStringArray("finItem");
		if (finItem.length > 0) {
			for (int i = 0; i < finItem.length; i++) {
				finItem[i] = finItem[i].substring(1);
			}
		}
		Map<String, List<String>> map = new HashMap<String, List<String>>();
		L120M01A l120m01a = service1205.findL120m01aByMainId(mainId);
		L120S01A l120s01a = service1205.findL120s01aByOid(thisOid);
		String custId = l120s01a.getCustId();
		String dupNo = l120s01a.getDupNo();
		L120S01B l120s01b = service1205.findL120s01bByUniqueKey(mainId, custId,
				dupNo);

		// 如果已有資料則刪除重新引進
		eloanDbBaseService.L120S01E_delByMainIdCustData(mainId, custId, dupNo);

		// List<L120S01E> oldList = service1205.findL120s01eByMainId(mainId);
		// List<L120S01E> delList = new ArrayList<L120S01E>();
		// if (!oldList.isEmpty()) {
		// for (L120S01E l120s01e : oldList) {
		// if (custId.equals(Util.trim(l120s01e.getCustId()))
		// && dupNo.equals(Util.trim(l120s01e.getDupNo()))) {
		// // 如果已有資料則刪除重新引進
		// delList.add(l120s01e);
		// }
		// }
		// }
		// // 開始進行刪除
		// service1205.deleteListL120s01e(delList);
		// 營運概況(資信簡表)
		List<L120S01E> list1 = service1205.findListL120s01e("1",
				Util.nullToSpace(mainId), Util.nullToSpace(custId),
				Util.nullToSpace(dupNo), finItem, map, cesMainId1);
		List<String> years1 = new ArrayList<String>();
		if (!map.isEmpty()) {
			years1 = map.get("year1");
			Collections.sort(years1);
		}
		CapAjaxFormResult resultl120s01g1 = new CapAjaxFormResult();
		CapAjaxFormResult formIdDscr1 = new CapAjaxFormResult();
		if (!list1.isEmpty()) {
			for (int i = 0; i < list1.size(); i++) {
				L120S01E model = list1.get(i);
				// if (!map.isEmpty()) {
				if (!years1.isEmpty()) {
					// if (!Util.isEmpty(Util.nullToSpace(years1
					// .get(0)))) {
					if (!checkSize(years1, 0)) {
						// 顯示完整日期而不僅僅顯示西元年 Miller Modified at 2012/08/20
						if (Util.trim(years1.get(0)).equals(
								TWNDate.toAD(model.getFinYear()))) {
							// if (Util.trim(years1.get(0)).equals(
							// TWNDate.toAD(model.getFinYear()).substring(0,
							// 4))) {
							resultl120s01g1.set("finYear_D", years1.get(0));
							resultl120s01g1
									.set("finAmt_D_" + model.getFinItem(),
											Util.isEmpty(Util.trim(Util
													.nullToSpace(model
															.getFinAmt()))) ? "0"
													: NumConverter.addComma(Util.trim(Util.nullToSpace(model
															.getFinAmt()))));
							resultl120s01g1
									.set("finRatio_D_" + model.getFinItem(),
											Util.isEmpty(Util.trim(Util
													.nullToSpace(model
															.getFinRatio()))) ? "0"
													: Util.trim(Util.nullToSpace(model
															.getFinRatio()))
															+ "%");
						}
					}
					// } else if
					// (!Util.isEmpty(Util.nullToSpace(years1.get(1)))) {
					if (!checkSize(years1, 1)) {
						// 顯示完整日期而不僅僅顯示西元年 Miller Modified at 2012/08/20
						if (Util.trim(years1.get(1)).equals(
								TWNDate.toAD(model.getFinYear()))) {
							// if(Util.trim(years1.get(1)).equals(
							// TWNDate.toAD(model.getFinYear()).substring(0,
							// 4))) {
							resultl120s01g1.set("finYear_C", years1.get(1));
							resultl120s01g1
									.set("finAmt_C_" + model.getFinItem(),
											Util.isEmpty(Util.trim(Util
													.nullToSpace(model
															.getFinAmt()))) ? "0"
													: NumConverter.addComma(Util.trim(Util.nullToSpace(model
															.getFinAmt()))));
							resultl120s01g1
									.set("finRatio_C_" + model.getFinItem(),
											Util.isEmpty(Util.trim(Util
													.nullToSpace(model
															.getFinRatio()))) ? "0"
													: Util.trim(Util.nullToSpace(model
															.getFinRatio()))
															+ "%");
						}
					}
					// } else if
					// (!Util.isEmpty(Util.nullToSpace(years1.get(2)))) {
					if (!checkSize(years1, 2)) {
						// 顯示完整日期而不僅僅顯示西元年 Miller Modified at 2012/08/20
						if (Util.trim(years1.get(2)).equals(
								TWNDate.toAD(model.getFinYear()))) {
							// if(Util.trim(years1.get(2)).equals(
							// TWNDate.toAD(model.getFinYear()).substring(0,
							// 4))) {
							resultl120s01g1.set("finYear_B", years1.get(2));
							resultl120s01g1
									.set("finAmt_B_" + model.getFinItem(),
											Util.isEmpty(Util.trim(Util
													.nullToSpace(model
															.getFinAmt()))) ? "0"
													: NumConverter.addComma(Util.trim(Util.nullToSpace(model
															.getFinAmt()))));
							resultl120s01g1
									.set("finRatio_B_" + model.getFinItem(),
											Util.isEmpty(Util.trim(Util
													.nullToSpace(model
															.getFinRatio()))) ? "0"
													: Util.trim(Util.nullToSpace(model
															.getFinRatio()))
															+ "%");
						}
					}
					// } else if
					// (!Util.isEmpty(Util.nullToSpace(years1.get(3)))) {
					if (!checkSize(years1, 3)) {
						// 顯示完整日期而不僅僅顯示西元年 Miller Modified at 2012/08/20
						if (Util.trim(years1.get(3)).equals(
								TWNDate.toAD(model.getFinYear()))) {
							// if(Util.trim(years1.get(3)).equals(
							// TWNDate.toAD(model.getFinYear()).substring(0,
							// 4))) {
							resultl120s01g1.set("finYear_A", years1.get(3));
							resultl120s01g1
									.set("finAmt_A_" + model.getFinItem(),
											Util.isEmpty(Util.trim(Util
													.nullToSpace(model
															.getFinAmt()))) ? "0"
													: NumConverter.addComma(Util.trim(Util.nullToSpace(model
															.getFinAmt()))));
							resultl120s01g1
									.set("finRatio_A_" + model.getFinItem(),
											Util.isEmpty(Util.trim(Util
													.nullToSpace(model
															.getFinRatio()))) ? "0"
													: Util.trim(Util.nullToSpace(model
															.getFinRatio()))
															+ "%");
						}
					}
					// }
				}
				// }
			}
		}
		// 設定營運概況分析與評估(徵信報告)
		L120S01G model1 = service1205.findL120s01gByUniqueKey(mainId, custId,
				dupNo, "1");
		if (model1 == null) {
			MegaSSOUserDetails unit = MegaSSOSecurityContext.getUserDetails();
			model1 = new L120S01G();
			model1.setMainId(mainId);
			model1.setDataType("1");
			model1.setCustId(custId);
			model1.setDupNo(dupNo);
			model1.setCreateTime(CapDate.getCurrentTimestamp());
			model1.setCreator(unit.getUserId());
		}
		if (!map.isEmpty()) {
			model1 = service1205.find120s01g(model1, cesMainId2);
		}
		formIdDscr1.set("idDscr1", model1.getDataDscr());
		// 財務狀況(資信簡表)
		List<L120S01E> list2 = service1205.findListL120s01e("2",
				Util.nullToSpace(mainId), Util.nullToSpace(custId),
				Util.nullToSpace(dupNo), finItem, map, cesMainId1);
		List<String> years2 = new ArrayList<String>();
		if (!map.isEmpty()) {
			years2 = map.get("year2");
			Collections.sort(years2);
		}
		CapAjaxFormResult resultL120s01g_2 = new CapAjaxFormResult();
		CapAjaxFormResult formIdDscr2 = new CapAjaxFormResult();
		if (!list2.isEmpty()) {
			for (int i = 0; i < list2.size(); i++) {
				L120S01E model = list2.get(i);
				// if (!map.isEmpty()) {
				if (!years2.isEmpty()) {
					// if (!Util.isEmpty(Util.nullToSpace(map.get("year2")
					// .get(0)))) {
					if (!checkSize(years2, 0)) {
						// 顯示完整日期而不僅僅顯示西元年 Miller Modified at 2012/08/20
						if (Util.trim(years2.get(0)).equals(
								TWNDate.toAD(model.getFinYear()))) {
							// if (Util.trim(years2.get(0)).equals(
							// TWNDate.toAD(model.getFinYear()).substring(0,
							// 4))) {
							resultL120s01g_2.set("finYear_C", years2.get(0));
							resultL120s01g_2
									.set("Answer_C_" + model.getFinItem(),
											Util.isEmpty(Util.trim(Util
													.nullToSpace(model
															.getFinRatio()))) ? "0"
													: Util.trim(Util.nullToSpace(model
															.getFinRatio()))
															+ (needPercent(Util.trim(model
																	.getFinItem())) ? "%"
																	: ""));
						}
					}
					// } else if (!Util.isEmpty(Util.nullToSpace(map.get(
					// "year2").get(1)))) {
					if (!checkSize(years2, 1)) {
						// 顯示完整日期而不僅僅顯示西元年 Miller Modified at 2012/08/20
						if (Util.trim(years2.get(1)).equals(
								TWNDate.toAD(model.getFinYear()))) {
							// if (Util.trim(years2.get(1)).equals(
							// TWNDate.toAD(model.getFinYear()).substring(0,
							// 4))) {
							resultL120s01g_2.set("finYear_B", years2.get(1));
							resultL120s01g_2
									.set("Answer_B_" + model.getFinItem(),
											Util.isEmpty(Util.trim(Util
													.nullToSpace(model
															.getFinRatio()))) ? "0"
													: Util.trim(Util.nullToSpace(model
															.getFinRatio()))
															+ (needPercent(Util.trim(model
																	.getFinItem())) ? "%"
																	: ""));
						}
					}
					// } else if (!Util.isEmpty(Util.nullToSpace(map.get(
					// "year2").get(2)))) {
					if (!checkSize(years2, 2)) {
						// 顯示完整日期而不僅僅顯示西元年 Miller Modified at 2012/08/20
						if (Util.trim(years2.get(2)).equals(
								TWNDate.toAD(model.getFinYear()))) {
							// if (Util.trim(years2.get(2)).equals(
							// TWNDate.toAD(model.getFinYear()).substring(0,
							// 4))) {
							resultL120s01g_2.set("finYear_A", years2.get(2));
							resultL120s01g_2
									.set("Answer_A_" + model.getFinItem(),
											Util.isEmpty(Util.trim(Util
													.nullToSpace(model
															.getFinRatio()))) ? "0"
													: Util.trim(Util.nullToSpace(model
															.getFinRatio()))
															+ (needPercent(Util.trim(model
																	.getFinItem())) ? "%"
																	: ""));
						}
					}
					// }
				}
				// }
			}
		}
		// 設定財務狀況分析與評估(徵信報告)
		L120S01G model2 = service1205.findL120s01gByUniqueKey(mainId, custId,
				dupNo, "2");
		if (model2 == null) {
			MegaSSOUserDetails unit = MegaSSOSecurityContext.getUserDetails();
			model2 = new L120S01G();
			model2.setMainId(mainId);
			model2.setDataType("2");
			model2.setCustId(custId);
			model2.setDupNo(dupNo);
			model2.setCreateTime(CapDate.getCurrentTimestamp());
			model2.setCreator(unit.getUserId());
		}
		model2 = service1205.find120s01g(model2, cesMainId2);
		formIdDscr2.set("idDscr2", model2.getDataDscr());

		// 存放款外匯往來情形(資信簡表)
		L120S01F model3 = service1205.findL120s01fByUniqueKey(mainId, custId,
				dupNo);
		model3 = service1205.findl120s01f(model3, cesMainId1);
		if (l120s01b != null) {
			l120s01b.setRcdFlag("Y");
			l120s01b.setRunFlag((list1.isEmpty()) ? "N" : "Y");
			l120s01b.setFinFlag((list2.isEmpty()) ? "N" : "Y");
		}
		CapAjaxFormResult L120S01fForm = DataParse.toResult(model3);
		L120S01fForm.set("dpAvgUnit", NumConverter.delCommaString(Util
				.nullToSpace(L120S01fForm.get("dpAvgUnit"))));
		L120S01fForm.set("fxUnit", NumConverter.delCommaString(Util
				.nullToSpace(L120S01fForm.get("fxUnit"))));
		L120S01fForm.set("fx2Unit", NumConverter.delCommaString(Util
				.nullToSpace(L120S01fForm.get("fx2Unit"))));
		L120S01fForm.set("imUnit", NumConverter.delCommaString(Util
				.nullToSpace(L120S01fForm.get("imUnit"))));
		L120S01fForm.set("im2Unit", NumConverter.delCommaString(Util
				.nullToSpace(L120S01fForm.get("im2Unit"))));
		L120S01fForm.set("exUnit", NumConverter.delCommaString(Util
				.nullToSpace(L120S01fForm.get("exUnit"))));
		L120S01fForm.set("ex2Unit", NumConverter.delCommaString(Util
				.nullToSpace(L120S01fForm.get("ex2Unit"))));
		L120S01fForm.set("cntrUnit", NumConverter.delCommaString(Util
				.nullToSpace(L120S01fForm.get("cntrUnit"))));
		L120S01fForm.set("nonLoanUnit", NumConverter.delCommaString(Util
				.nullToSpace(L120S01fForm.get("nonLoanUnit"))));
		L120S01fForm.set("fxYear", trimZero(NumConverter.delCommaString(Util
				.nullToSpace(model3.getFxYear()))));
		L120S01fForm.set("imYear", trimZero(NumConverter.delCommaString(Util
				.nullToSpace(model3.getImYear()))));
		L120S01fForm.set("exYear", trimZero(NumConverter.delCommaString(Util
				.nullToSpace(model3.getExYear()))));
		SimpleDateFormat bartDateFormat = new SimpleDateFormat(
				UtilConstants.DateFormat.YYYY_MM_DD);
		String fxb = checknull(bartDateFormat, model3.getFxBDate());
		String fxe = checknull(bartDateFormat, model3.getFxEDate());
		String imb = checknull(bartDateFormat, model3.getImBDate());
		String ime = checknull(bartDateFormat, model3.getImEDate());
		String exb = checknull(bartDateFormat, model3.getExBDate());
		String exe = checknull(bartDateFormat, model3.getExEDate());
		String cntrb = checknull(bartDateFormat, model3.getCntrBDate());
		String cntre = checknull(bartDateFormat, model3.getCntrEDate());
		translate("fxb", fxb, L120S01fForm); // 舊型用法(不活)
		translate("fxe", fxe, L120S01fForm);
		translate("imb", imb, L120S01fForm);
		translate("ime", ime, L120S01fForm);
		translate("exb", exb, L120S01fForm);
		translate("exe", exe, L120S01fForm);
		translate("cntrb", cntrb, L120S01fForm);
		translate("cntre", cntre, L120S01fForm);
		L120S01fForm.set("rcdFlag", ("Y".equals(l120s01b.getRcdFlag()) ? "Y"
				: "N"));
		// 進行儲存
		service1205.saveL120s01e(list1, list2, l120m01a, model1, model2,
				model3, l120s01b);
		if (params.getAsBoolean("showMsg", true)) {
			// 印出執行成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0018"));
		}
		CapAjaxFormResult showBorrowData = new CapAjaxFormResult();
		showBorrowData.set("custId", Util.trim(l120m01a.getCustId()));
		showBorrowData.set("dupNo", Util.trim(l120m01a.getDupNo()));
		resultl120s01g1.set("rcdFlag", Util.trim(l120s01b.getRcdFlag()));
		resultl120s01g1.set("runFlag", Util.trim(l120s01b.getRunFlag()));
		resultL120s01g_2.set("finFlag", Util.trim(l120s01b.getFinFlag()));
		result.set("L120S01gForm_1", resultl120s01g1);
		result.set("formIdDscr1", formIdDscr1);
		result.set("L120S01gForm_2", resultL120s01g_2);
		result.set("formIdDscr2", formIdDscr2);
		result.set("L120S01fForm", L120S01fForm); // 當有多個model要對應form時..
		result.set("showBorrowData", showBorrowData);
		// 依序指定
		return result;
	}// ;

	/**
	 * <pre>
	 * 查詢-個金(借款人標籤列)
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return IResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryBorrow2(PageParameters params)
			throws CapException {
		return super._LMSM01FormHandler_queryBorrow2(params);
	}

	/**
	 * <pre>
	 * 儲存-企金(全部借款人內容)
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return IResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveBorrow(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		CapAjaxFormResult showBorrowData = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);
		String chairman = params.getString("chairman");
		boolean rcdFlag = params.getBoolean("rcdFlag");
		boolean runFlag = params.getBoolean("runFlag");
		boolean finFlag = params.getBoolean("finFlag");
		String invMDscr = params.getString("invMDscr");
		// 借款人主檔 及企金基本資料檔 的前端資料
		String formL120s01a = params.getString("L120S01aForm");
		L120S01A l120s01a = service1205.findL120s01aByOid(oid);

		String errMsgs[] = params.getStringArray("errMsg");
		StringBuilder errorMsg = new StringBuilder();
		errorMsg.setLength(0);
		// 串前端傳來的錯誤訊息
		if (errMsgs.length > 0) {
			for (String errMsg : errMsgs) {
				errorMsg.append(
						(errorMsg.length() > 0) ? "<br/>"
								: UtilConstants.Mark.SPACE).append(errMsg);
			}
		}

		super._LMSM01FormHandler_setL120s01a(l120s01a, formL120s01a);
		// ============================L120S01A資料到此就定位======================================
		L120S01B l120s01b = service1205
				.findL120s01bByUniqueKey(l120s01a.getMainId(),
						l120s01a.getCustId(), l120s01a.getDupNo());
		if (l120s01b == null) {
			l120s01b = new L120S01B();
			l120s01b.setMainId(l120s01a.getMainId());
			l120s01b.setCustId(l120s01a.getCustId());
			l120s01b.setDupNo(l120s01a.getDupNo());
		}
		// 將L120S01aForm data 置入L120S01B Model
		DataParse.toBean(formL120s01a, l120s01b);
		l120s01b.setAprCurr("TWD");

		if ("Y".equals(l120s01a.getKeyMan())) {
			// 如果為主要借款人
			if ("1".equals(l120s01b.getInvMFlag())) {
				// 若有赴大陸投資
				if (!BeanValidator.isValid(l120s01b, Check.class)) {
					errorMsg.append(BeanValidator.getValidMsg(l120s01b,
							LMSS02Page.class, Check.class));
				}
				if (!Util.isEmpty(l120s01b.getAprCurr())
						&& !Util.isEmpty(l120s01b.getInvMAmt())
						&& !Util.isEmpty(l120s01b.getInvMCurr())
						&& !Util.isEmpty(l120s01b.getAprAmt())) {
					// 如果赴大陸投資金額和經濟部投審金額皆有輸入
					l120s01a.setChkYN("Y");
				} else {
					l120s01a.setChkYN("N");
				}
			} else {
				// 若無赴大陸投資
				l120s01a.setChkYN("Y");
			}
			if (!BeanValidator.isValid(l120s01b, Check2.class)) {
				errorMsg.append(BeanValidator.getValidMsg(l120s01b,
						LMSS02Page.class, Check2.class));
				l120s01a.setChkYN("N");
			}
		} else {
			// 如果不是主要借款人則檢查相關身份與主要借款人關係是否不為空
			// J-110-0458 企金授權內其他 -
			// 「簡易簽報」選項，適用方案「LIBOR退場變更利率條件簡易簽報」，可能毫無相關，故拿掉此檢核
			L120M01A l120m01a = service1205.findL120m01aByMainId(l120s01a
					.getMainId());
			if (l120m01a != null) {
				if (!(lmsService.isLiborExitCase(l120m01a) || lmsService.isEuroyenTiborExitCase(l120m01a))) {
					if (!BeanValidator.isValid(l120s01a, Check3.class)) {
						errorMsg.append(BeanValidator.getValidMsg(l120s01a,
								LMSS02Page.class, Check3.class));
					}

					if (!Util.isEmpty(l120s01a.getCustPos())
							&& !Util.isEmpty(l120s01a.getCustRlt())) {
						if ("1".equals(l120s01b.getInvMFlag())) {
							// 若有赴大陸投資
							if (!BeanValidator.isValid(l120s01b, Check.class)) {
								errorMsg.append(BeanValidator
										.getValidMsg(l120s01b,
												LMSS02Page.class, Check.class));
							}
							if (!Util.isEmpty(l120s01b.getAprCurr())
									&& !Util.isEmpty(l120s01b.getInvMAmt())
									&& !Util.isEmpty(l120s01b.getInvMCurr())
									&& !Util.isEmpty(l120s01b.getAprAmt())) {
								// 如果赴大陸投資金額和經濟部投審金額皆有輸入
								l120s01a.setChkYN("Y");
							} else {
								l120s01a.setChkYN("N");
							}
						} else {
							// 若無赴大陸投資
							l120s01a.setChkYN("Y");
						}
					} else {
						l120s01a.setChkYN("N");
					}
				} else {
					// LIBOR退場 不檢查大陸投資跟與主要借款人關係
					l120s01a.setChkYN(UtilConstants.DEFAULT.是);
				}
			}
			if (!BeanValidator.isValid(l120s01b, Check2.class)) {
				errorMsg.append(BeanValidator.getValidMsg(l120s01b,
						LMSS02Page.class, Check2.class));
				l120s01a.setChkYN("N");
			}
		}
		l120s01b.setRcdFlag((rcdFlag ? "Y" : "N"));
		l120s01b.setRunFlag((runFlag ? "Y" : "N"));
		l120s01b.setFinFlag((finFlag ? "Y" : "N"));
		l120s01b.setInvMDscr(Util.truncateString(Util.trim(invMDscr), 1536));
		l120s01b.setChairman(Util.trim(chairman));
		// J-109-0370 相關評估改版
		String prodMkt = Util.trim(params.getString("prodMkt"));
		boolean isPrint = params.getAsBoolean("isPrint", true);
		l120s01b.setIsPrint(isPrint ? "Y" : "N");
		l120s01b.setProdMkt(Util.trim(prodMkt));
		super._LMSM01FormHandler_setL120s01b_ChkYN(l120s01a, l120s01b, errorMsg);
		// ============================L120S01B資料到此就定位======================================
		L120S01D l120s01d = service1205
				.findL120s01dByUniqueKey(l120s01a.getMainId(),
						l120s01a.getCustId(), l120s01a.getDupNo());
		if (l120s01d == null) {
			l120s01d = new L120S01D();
			l120s01d.setMainId(l120s01a.getMainId());
			l120s01d.setCustId(l120s01a.getCustId());
			l120s01d.setDupNo(l120s01a.getDupNo());
		}
		// 將L120S01aForm data 置入L120S01D Model
		DataParse.toBean(formL120s01a, l120s01d);
		// ===================L120S01D資料到此就定位=======================
		// 企金分析與評估檔(part1) 的前端資料
		String form1L120s01g = params.getString("formIdDscr1");
		JSONObject jsontest = JSONObject.fromObject(form1L120s01g);
		L120S01G l120s01g1 = service1205.findL120s01gByUniqueKey(
				l120s01a.getMainId(), l120s01a.getCustId(),
				l120s01a.getDupNo(), "1");
		if (l120s01g1 == null) {
			l120s01g1 = new L120S01G();
			l120s01g1.setMainId(l120s01a.getMainId());
			l120s01g1.setCustId(l120s01a.getCustId());
			l120s01g1.setDupNo(l120s01a.getDupNo());
			l120s01g1.setDataType("1");
		}
		// 設定使用者所輸入Ckeditor的資料
		l120s01g1.setDataDscr(Util.nullToSpace(jsontest.get("idDscr1")));
		// l120s01g1.setDataDscr(params.getString("idDscr1"));
		// ====================L120S01G資料到此就定位======================
		// 企金分析與評估檔(part2) 的前端資料
		String form2L120s01g = params.getString("formIdDscr2");
		JSONObject jsontest2 = JSONObject.fromObject(form2L120s01g);
		L120S01G l120s01g2 = service1205.findL120s01gByUniqueKey(
				l120s01a.getMainId(), l120s01a.getCustId(),
				l120s01a.getDupNo(), "2");
		if (l120s01g2 == null) {
			l120s01g2 = new L120S01G();
			l120s01g2.setMainId(l120s01a.getMainId());
			l120s01g2.setCustId(l120s01a.getCustId());
			l120s01g2.setDupNo(l120s01a.getDupNo());
			l120s01g2.setDataType("2");
		}
		// 設定使用者所輸入Ckeditor的資料
		l120s01g2.setDataDscr(Util.nullToSpace(jsontest2.get("idDscr2")));
		// l120s01g2.setDataDscr(params.getString("idDscr2"));
		// ====================L120S01G資料到此就定位======================
		// 企金存放款外匯往來檔 的前端資料
		String formL120s01f = params.getString("L120S01fForm");
		L120S01F l120s01f = service1205
				.findL120s01fByUniqueKey(l120s01a.getMainId(),
						l120s01a.getCustId(), l120s01a.getDupNo());
		if (l120s01f == null) {
			l120s01f = new L120S01F();
			l120s01f.setMainId(l120s01a.getMainId());
			l120s01f.setCustId(l120s01a.getCustId());
			l120s01f.setDupNo(l120s01a.getDupNo());
		}
		// 將L120S01fForm data 置入 L120S01F Model
		DataParse.toBean(formL120s01f, l120s01f);
		if (!Util.isEmpty(Util.nullToSpace(l120s01f.getAvgURate()))) {
			if (l120s01f.getAvgURate() <= 999.99 && l120s01f.getAvgURate() >= 0) {
				l120s01f.setAvgURate(l120s01f.getAvgURate());
			} else {
				Properties pop = MessageBundleScriptCreator
						.getComponentResource(LMSS02Panel.class);
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0015", pop.getProperty("l120s02.alert12")),
						getClass());
			}
		}
		// 將自定義的欄位組合好塞到資料表對應欄位裡
		l120s01f.setFxBDate(checkSpace(params.getString("fxbDateY"),
				checkLength(params.getString("fxbDateM"))));
		l120s01f.setFxEDate(checkSpace(params.getString("fxeDateY"),
				checkLength(params.getString("fxeDateM"))));
		l120s01f.setImBDate(checkSpace(params.getString("imbDateY"),
				checkLength(params.getString("imbDateM"))));
		l120s01f.setImEDate(checkSpace(params.getString("imeDateY"),
				checkLength(params.getString("imeDateM"))));
		l120s01f.setExBDate(checkSpace(params.getString("exbDateY"),
				checkLength(params.getString("exbDateM"))));
		l120s01f.setExEDate(checkSpace(params.getString("exeDateY"),
				checkLength(params.getString("exeDateM"))));
		l120s01f.setCntrBDate(checkSpace(params.getString("cntrbDateY"),
				checkLength(params.getString("cntrbDateM"))));
		l120s01f.setCntrEDate(checkSpace(params.getString("cntreDateY"),
				checkLength(params.getString("cntreDateM"))));
		// ===================L120S01F資料到此就定位===========================

		if (errorMsg.length() > 0) {
			l120s01a.setChkYN(UtilConstants.DEFAULT.否);
		}

		// ===================以下開始驗證主要借款人===========================

		// J-104-0240-001 Web e-Loan授信簽報書與額度明細表增加列示借款戶所屬集團企業代號、名稱與註記。
		// 異常通報的集團改抓借款人基本資料，所以新報送的簽報書清掉原異常通報集團內容
		L130M01A l130m01a = service1205.findL130m01aByMainId(l120s01a
				.getMainId());
		if (l130m01a != null) {
			if (Util.notEquals(Util.trim(l130m01a.getGrpId()), "")
					|| Util.notEquals(Util.trim(l130m01a.getGrpName()), "")) {
				l130m01a.setGrpId("");
				l130m01a.setGrpName("");
				service1205.save(l130m01a);
			}
		}

		// 先抓出所有記錄主要借款人資料
		List<L120S01A> list = service1205.findL120s01aByMainId(l120s01a
				.getMainId());
		L120M01A l120m01a = service1205.findL120m01aByMainId(l120s01a
				.getMainId());
		// 主要營業項目設定控制
		if (UtilConstants.Casedoc.DocKind.授權外.equals(Util.trim(l120m01a
				.getDocKind()))) {
			l120m01a.setItemOfBusi(JSONObject.fromObject(formL120s01a)
					.getString("itemOfBusi"));
		} else {
			// 授權內
			l120s01b.setBussItem(JSONObject.fromObject(formL120s01a).getString(
					"itemOfBusi"));
		}

		// 當使用者按下儲存後(將簽報書刪除時間註記砍掉，代表此簽報書要保留。)
		l120m01a.setDeletedTime(null);
		MegaSSOUserDetails unit = MegaSSOSecurityContext.getUserDetails();
		if (Util.isEmpty(l120m01a.getCaseSeq())
				&& Util.isEmpty(l120m01a.getCaseNo())) {
			l120m01a.setCaseSeq(Integer.parseInt(number.getNumberWithMax(
					L120M01A.class, unit.getUnitNo(), null, 99999)));
			StringBuilder caseNum = new StringBuilder();
			IBranch ibranch = branch.getBranch(unit.getUnitNo());
			// Properties pop = MessageBundleScriptCreator
			// .getComponentResource(LMS1205M01Page.class);
			caseNum.append(
					Util.toFullCharString(l120m01a.getCaseYear().toString()))
					.append(Util.trim(ibranch.getNameABBR()))
					.append(UtilConstants.Field.兆)
					.append(UtilConstants.Field.授字第)
					.append(Util.toFullCharString(Util.addZeroWithValue(
							Util.trim(l120m01a.getCaseSeq()), 5)))
					.append(UtilConstants.Field.號);
			l120m01a.setCaseNo(caseNum.toString());
		}

		String needToAddOid = "";
		int count = 0;
		// 取得使用者點選的借款人主表Oid
		String oidOid = l120s01a.getOid();
		for (int i = 0; i < list.size(); i++) {
			L120S01A model = list.get(i);
			if ("Y".equals(Util.nullToSpace(model.getKeyMan()))) {
				// 找出需要被覆蓋的主要借款人資料
				if (!(Util.nullToSpace(model.getOid()).equals(oidOid))) {
					count++;
					needToAddOid = model.getOid();
				}
			}
		}

		// 當是主要借款人且為第一次新增時
		if ("Y".equals(Util.nullToSpace(params.getString("keyMan")))
				&& count == 0) {
			result.set("keyMan", "Y");
			String custId = l120s01a.getCustId();
			String dupNo = l120s01a.getDupNo();
			String custName = l120s01a.getCustName();
			String typCd = l120s01a.getTypCd();
			l120m01a.setCustId(custId);
			l120m01a.setDupNo(dupNo);
			l120m01a.setCustName(Util.truncateString(custName, 120));
			l120m01a.setTypCd("5");
			showBorrowData.set("custId", custId);
			showBorrowData.set("dupNo", dupNo);
			showBorrowData.set("custName", Util.truncateString(custName, 120));
			showBorrowData.set("typCd", getMessage("typCd." + typCd));
			// showBorrowData.set("typCd", TypCdEnum.getEnum(typCd).name());
			try {
				service1205.save(l120m01a, l120s01a, l120s01b, l120s01d,
						l120s01g1, l120s01g2, l120s01f);
			} catch (Exception e) {
				logger.error("[saveBorrow] service1205.save EXCEPTION!!", e);
				Map<String, String> param = new HashMap<String, String>();
				param.put("colName", space);
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
			}
			result.set("showBorrowData", showBorrowData);
			if (errorMsg.length() > 0) {
				errorMsg.insert(0, RespMsgHelper.getMainMessage("EFD0017") + "<br/><br/>");
				result.set("errorMsg", errorMsg.toString());
			} else {
				// 印出儲存成功訊息!
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0017"));
			}
		}
		// 當新增主要借款人且已有主要借款人時
		else if ("Y".equals(params.getString("keyMan")) && count == 1) {
			result.set("keyMan", "Y");
			// 將使用者勾選新主要借款人的Oid傳到前端以做進一步處理
			result.set("haveKeyManOid", l120s01a.getOid());
			// 將授信簽報書OID傳到前端以新增主要借款人
			result.set("haveKeyManDocNo", l120m01a.getOid());
			// 將被覆蓋(原主要借款人)的Oid傳到前端以做進一步處理
			result.set("needtoAddOid", needToAddOid);
			// 傳到前端判定為已存在主要借款人
			result.set("ExistKeyMan", true);
			L120S01A oldL120s01a = service1205.findL120s01aByOid(needToAddOid);
			// 原主要借款人名稱
			result.set("oldBorrower", oldL120s01a.getCustName());
			// 先儲存進去之後 L120S01A Model再做處理
			try {
				service1205.save(l120m01a, l120s01a, l120s01b, l120s01d,
						l120s01g1, l120s01g2, l120s01f);
			} catch (Exception e) {
				Map<String, String> param = new HashMap<String, String>();
				param.put("colName", space);
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
			}
			if (errorMsg.length() > 0) {
				result.set("errorMsg", errorMsg.toString());
			}
			return result;
		}
		// 使用者將原主要借款人取消時
		else if ("N".equals(params.getString("keyMan")) && count == 0) {
			l120s01a.setKeyMan("N");
			result.set("keyMan", "N");
			l120s01a.setChkYN("N");
			l120m01a.setCustId("");
			l120m01a.setDupNo("");
			l120m01a.setCustName("");
			l120m01a.setTypCd("");
			showBorrowData.set("custId", "");
			showBorrowData.set("dupNo", "");
			showBorrowData.set("custName", "");
			showBorrowData.set("typCd", "");
			result.set("ExistKeyMan", false);
			try {
				service1205.save(l120m01a, l120s01a, l120s01b, l120s01d,
						l120s01g1, l120s01g2, l120s01f);
			} catch (Exception e) {
				logger.error("[saveBorrow] service1205.save EXCEPTION!!", e);
				Map<String, String> param = new HashMap<String, String>();
				param.put("colName", space);
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
			}
			result.set("showBorrowData", showBorrowData);
			if (errorMsg.length() > 0) {
				errorMsg.insert(0, RespMsgHelper.getMainMessage("EFD0017") + "<br/><br/>");
				result.set("errorMsg", errorMsg.toString());
			} else {
				// 印出儲存成功訊息!
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0017"));
			}
		} else {
			// 當使用者新增不是主要借款人資料時...
			l120s01a.setKeyMan("N");
			result.set("keyMan", "N");
			try {
				service1205.save(l120m01a, l120s01a, l120s01b, l120s01d,
						l120s01g1, l120s01g2, l120s01f);
			} catch (Exception e) {
				logger.error("[saveBorrow] service1205.save EXCEPTION!!", e);
				Map<String, String> param = new HashMap<String, String>();
				param.put("colName", space);
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
			}
			if (errorMsg.length() > 0) {
				errorMsg.insert(0, RespMsgHelper.getMainMessage("EFD0017") + "<br/><br/>");
				result.set("errorMsg", errorMsg.toString());
			} else {
				// 印出儲存成功訊息!
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0017"));
			}
		}
		return result;
	}

	/**
	 * 營運概況修改欄位儲存
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult editBorrowPage02(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String thisOid = Util.trim(params.getString("thisOid"));
		L120S01A l120s01a = service1205.findL120s01aByOid(thisOid);
		String editFPanel2 = Util.trim(params.getString("editFPanel2"));
		if (l120s01a != null) {
			String mainId = Util.trim(l120s01a.getMainId());
			String custId = Util.trim(l120s01a.getCustId());
			String dupNo = Util.trim(l120s01a.getDupNo());
			L120S01B l120s01b = service1205.findL120s01bByUniqueKey(mainId,
					custId, dupNo);
			CapAjaxFormResult L120S01gForm_1 = new CapAjaxFormResult();
			if (Util.isNotEmpty(editFPanel2)) {
				List<L120S01E> listL120s01e = service1205
						.findListL120s01eByUniqueKey("1",
								Util.trim(l120s01a.getMainId()),
								Util.trim(l120s01a.getCustId()),
								Util.trim(l120s01a.getDupNo())); // 抓出所有符合條件的資料
				List<L120S01E> listToAdd = new ArrayList<L120S01E>();
				if (!listL120s01e.isEmpty()) {
					service1205.deleteListL120s01e(listL120s01e);
				}
				JSONObject json = JSONObject.fromObject(editFPanel2);
				if (l120s01b != null) {
					l120s01b.setRunCurr(Util.trim(json.getString("_runCurr")));
					l120s01b.setRunUnit(LMSUtil.toBigDecimal(Util.trim(json
							.getString("_runUnit"))));
				}
				String keys[] = Util.getMapKey(json);
				Map<String, String> map = new HashMap<String, String>();
				String tempFinAmt = null;
				String tempFinRatio = null;
				for (String key : keys) {
					if (key.contains("finYear")) {
						// 年份
						L120S01gForm_1.set(key.substring(1),
								Util.trim(json.getString(key)));
						map.put("finYear_" + key.substring(key.length() - 1),
								Util.trim(json.getString(key)));
					} else if (key.contains("finAmt")) {
						// 金額
						tempFinAmt = NumConverter.delCommaString(Util.trim(json
								.getString(key)));
						L120S01gForm_1.set(key.substring(1), tempFinAmt);
						map.put(key.substring(1), tempFinAmt);
					} else if (key.contains("finRatio")) {
						// 比率
						tempFinRatio = Util.trim(json.getString(key));
						L120S01gForm_1.set(key.substring(1), tempFinRatio);
						map.put(key.substring(1), tempFinRatio);
					}
				}
				if (!map.isEmpty()) {
					String mapKeys[] = Util.getMapKey(map);
					for (String mapKey : mapKeys) {
						if (mapKey.contains("finAmt")) {
							// 金額或比率
							String finItem = Util.trim(mapKey.substring(mapKey
									.length() - 2));
							String yearKind = Util.trim(mapKey.substring(
									mapKey.length() - 4, mapKey.length() - 3));
							String finYear = Util.trim(map.get("finYear_"
									+ yearKind));
							String idKey = yearKind + "_" + finItem;
							if (Util.isNotEmpty(finYear)
									&& Util.isNotEmpty(finItem)) {
								L120S01E model = new L120S01E();
								model.setMainId(mainId);
								model.setCustId(custId);
								model.setDupNo(dupNo);
								model.setFinKind("1");
								model.setFinYear(Util.parseDate(finYear));
								model.setFinItem(finItem);
								model.setFinAmt(Util.parseLong(map
										.get("finAmt_" + idKey)));
								model.setFinRatio(LMSUtil.toBigDecimal(map
										.get("finRatio_" + idKey)));

								listToAdd.add(model);
							}
						}
					}
				}
				service1205.saveListL120s01e(listToAdd, l120s01b);
				// 印出執行成功訊息!
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0018"));
				L120S01gForm_1.set("runCurr", Util.trim(l120s01b.getRunCurr()));
				L120S01gForm_1.set("runUnit", Util.trim(l120s01b.getRunUnit()));
				result.set("L120S01gForm_1", L120S01gForm_1);
			}
		}
		return result;
	}

	/**
	 * 財務狀況修改欄位儲存
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult editBorrowPage03(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String thisOid = Util.trim(params.getString("thisOid"));
		L120S01A l120s01a = service1205.findL120s01aByOid(thisOid);
		String editFPanel3 = Util.trim(params.getString("editFPanel3"));
		if (l120s01a != null) {
			String mainId = Util.trim(l120s01a.getMainId());
			String custId = Util.trim(l120s01a.getCustId());
			String dupNo = Util.trim(l120s01a.getDupNo());
			CapAjaxFormResult L120S01gForm_2 = new CapAjaxFormResult();
			if (Util.isNotEmpty(editFPanel3)) {
				List<L120S01E> listL120s01e = service1205
						.findListL120s01eByUniqueKey("2",
								Util.trim(l120s01a.getMainId()),
								Util.trim(l120s01a.getCustId()),
								Util.trim(l120s01a.getDupNo())); // 抓出所有符合條件的資料
				List<L120S01E> listToAdd = new ArrayList<L120S01E>();
				if (!listL120s01e.isEmpty()) {
					service1205.deleteListL120s01e(listL120s01e);
				}
				JSONObject json = JSONObject.fromObject(editFPanel3);
				String keys[] = Util.getMapKey(json);
				Map<String, String> map = new HashMap<String, String>();
				String tempFinRatio = null;
				for (String key : keys) {
					if (key.contains("finYear")) {
						// 年份
						L120S01gForm_2.set(key.substring(2),
								Util.trim(json.getString(key)));
						map.put("finYear_" + key.substring(key.length() - 1),
								Util.trim(json.getString(key)));
					} else if (key.contains("Answer")) {
						// 比率
						tempFinRatio = Util.trim(json.getString(key));
						L120S01gForm_2
								.set(key.substring(2),
										tempFinRatio
												+ (needPercent(Util.trim(key
														.substring(key.length() - 2))) ? "%"
														: ""));
						map.put(key.substring(2), tempFinRatio);
					}
				}
				if (!map.isEmpty()) {
					String mapKeys[] = Util.getMapKey(map);
					for (String mapKey : mapKeys) {
						if (mapKey.contains("Answer")) {
							// 比率
							String finItem = Util.trim(mapKey.substring(mapKey
									.length() - 2));
							String yearKind = Util.trim(mapKey.substring(
									mapKey.length() - 4, mapKey.length() - 3));
							String finYear = map.get("finYear_" + yearKind);
							String idKey = yearKind + "_" + finItem;
							if (Util.isNotEmpty(finYear)
									&& Util.isNotEmpty(finItem)) {
								L120S01E model = new L120S01E();
								model.setMainId(mainId);
								model.setCustId(custId);
								model.setDupNo(dupNo);
								model.setFinKind("2");
								model.setFinYear(Util.parseDate(finYear));
								model.setFinItem(finItem);
								model.setFinRatio(LMSUtil.toBigDecimal(map
										.get("Answer_" + idKey)));

								listToAdd.add(model);
							}
						}
					}
				}
				service1205.saveListL120s01e(listToAdd);
				// 印出執行成功訊息!
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0018"));
				result.set("L120S01gForm_2", L120S01gForm_2);
			}
		}
		return result;
	}

	/**
	 * <pre>
	 * 儲存-個金(全部借款人內容)
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return IResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveBorrow2(PageParameters params)
			throws CapException {
		return super._LMSM01FormHandler_saveBorrow2(params);
	}

	/**
	 * 儲存會簽內容(國金部)
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveSignContent(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		CapAjaxFormResult L120M01aForm14 = new CapAjaxFormResult();
		CapAjaxFormResult LMS1205S01Form = new CapAjaxFormResult();
		boolean queryArea = params.getBoolean("queryArea");
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String itemDscr09 = params.getString("itemDscr09");
		String sSeaManager = trimNull(params.getString("sSeaManager"));
		String sSeaBoss = trimNull(params.getString("sSeaBoss"));
		String sSeaAoName = trimNull(params.getString("sSeaAoName"));
		String sSeaAppraiserCN = trimNull(params.getString("sSeaAppraiserCN"));
		String sAreaLeader = trimNull(params.getString("sAreaLeader"));
		String sAreaSubLeader = trimNull(params.getString("sAreaSubLeader"));
		String sAreaManager = trimNull(params.getString("sAreaManager"));
		String sAreaAppraiser = trimNull(params.getString("sAreaAppraiser"));

		if (queryArea) {
			LMS1205S01Form
					.set("areaLeader", withIdName(Util.trim(sAreaLeader)));
			LMS1205S01Form.set("areaSubLeader",
					withIdName(Util.trim(sAreaSubLeader)));
			LMS1205S01Form.set("areaManager",
					withIdName(Util.trim(sAreaManager)));
			LMS1205S01Form.set("areaAppraiser",
					withIdName(Util.trim(sAreaAppraiser)));
		} else {
			LMS1205S01Form
					.set("seaManager", withIdName(Util.trim(sSeaManager)));
			LMS1205S01Form.set("seaBoss", withIdName(Util.trim(sSeaBoss)));
			LMS1205S01Form.set("seaAoName", withIdName(Util.trim(sSeaAoName)));
			LMS1205S01Form.set("seaAppraiserCN",
					withIdName(Util.trim(sSeaAppraiserCN)));
		}

		L120M01aForm14.set("itemDscr09", itemDscr09);
		L121M01B model = service1205.findL121m01bByUniqueKey(mainId, "9");
		// List<L120M01F> list = service1205.findL120m01fByMainId(mainId);
		if (model == null) {
			model = new L121M01B();
			model.setMainId(mainId);
			model.setItemType("9");
			model.setCreateTime(CapDate.getCurrentTimestamp());
			model.setCreator(user.getUserId());
		}
		List<L120M01F> saveList = new ArrayList<L120M01F>();

		if (!Util.isEmpty(Util.trim(itemDscr09))) {
			model.setItemDscr(itemDscr09);
		} else {
			model.setItemDscr("");
		}
		try {
			service1205.saveSea(model, saveList);
		} catch (Exception e) {
			logger.error("[saveSignContent] service1205.save EXCEPTION!!", e);
			Map<String, String> param = new HashMap<String, String>();
			param.put("colName", space);
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
		}
		if (params.getAsBoolean("showMsg", true)) {
			// 印出儲存成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0017"));
		}
		result.set("L120M01aForm14", L120M01aForm14);
		result.set("LMS1205S01Form", LMS1205S01Form);
		return result;
	}

	/**
	 * 儲存簽章欄(海外聯貸)
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveSignContentF1(PageParameters params)
			throws CapException {
		// MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		CapAjaxFormResult L120M01aForm14 = new CapAjaxFormResult();
		CapAjaxFormResult LMS1205S01Form = new CapAjaxFormResult();
		boolean queryArea = params.getBoolean("queryArea");
		String mainId = params.getString(EloanConstants.MAIN_ID);
		// String itemDscr09 = params.getString("itemDscr09");
		String sSeaManager = trimNull(params.getString("sSeaManager"));
		String sSeaBoss = trimNull(params.getString("sSeaBoss"));
		String sSeaAoName = trimNull(params.getString("sSeaAoName"));
		String sSeaAppraiserCN = trimNull(params.getString("sSeaAppraiserCN"));
		String sUnitManager1 = trimNull(params.getString("sUnitManager1"));
		String sAreaLeader = trimNull(params.getString("sAreaLeader"));
		String sAreaSubLeader = trimNull(params.getString("sAreaSubLeader"));
		String sAreaManager = trimNull(params.getString("sAreaManager"));
		String sAreaAppraiser = trimNull(params.getString("sAreaAppraiser"));
		String sUnitManager3 = trimNull(params.getString("sUnitManager3"));

		if (queryArea) {
			LMS1205S01Form
					.set("areaLeader", withIdName(Util.trim(sAreaLeader)));
			LMS1205S01Form.set("areaSubLeader",
					withIdName(Util.trim(sAreaSubLeader)));
			LMS1205S01Form.set("areaManager",
					withIdName(Util.trim(sAreaManager)));
			LMS1205S01Form.set("areaAppraiser",
					withIdName(Util.trim(sAreaAppraiser)));
		} else {
			LMS1205S01Form
					.set("seaManager", withIdName(Util.trim(sSeaManager)));
			LMS1205S01Form.set("seaBoss", withIdName(Util.trim(sSeaBoss)));
			LMS1205S01Form.set("seaAoName", withIdName(Util.trim(sSeaAoName)));
			LMS1205S01Form.set("seaAppraiserCN",
					withIdName(Util.trim(sSeaAppraiserCN)));
		}

		List<L120M01F> list = service1205.findL120m01fByMainId(mainId);
		List<L120M01F> saveList = new ArrayList<L120M01F>();
		if (!list.isEmpty()) {
			for (L120M01F l120m01f : list) {
				if ("6".equals(l120m01f.getBranchType())) {
					saveList.add(l120m01f);
				}
			}
		}
		if (!saveList.isEmpty()) {
			service1205.delListL120m01f(saveList);
			saveList.clear();
		}
		// 第一次新增營運簽章欄
		if (queryArea) {
			saveList.add(addSeaL120m01f(mainId, "L6", sAreaLeader));
			saveList.add(addSeaL120m01f(mainId, "L5", sAreaSubLeader));
			saveList.add(addSeaL120m01f(mainId, "L3", sAreaManager));
			saveList.add(addSeaL120m01f(mainId, "L1", sAreaAppraiser));
			saveList.add(addSeaL120m01f(mainId, "L9", sUnitManager1));
		} else {
			saveList.add(addSeaL120m01f(mainId, "L5", sSeaManager));
			saveList.add(addSeaL120m01f(mainId, "L3", sSeaBoss));
			if (!Util.isEmpty(sSeaAoName)) {
				saveList.add(addSeaL120m01f(mainId, "L2", sSeaAoName));
			}
			saveList.add(addSeaL120m01f(mainId, "L1", sSeaAppraiserCN));
			saveList.add(addSeaL120m01f(mainId, "L9", sUnitManager3));
		}

		try {
			service1205.saveSea(null, saveList);
		} catch (Exception e) {
			logger.error("[saveSignContent] service1205.save EXCEPTION!!", e);
			Map<String, String> param = new HashMap<String, String>();
			param.put("colName", space);
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
		}
		if (params.getAsBoolean("showMsg", true)) {
			// 印出儲存成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0017"));
		}
		result.set("L120M01aForm14", L120M01aForm14);
		result.set("LMS1205S01Form", LMS1205S01Form);
		return result;
	}

	/**
	 * 呈主管放行(海外聯貸)
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult sendBossSea(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		L120M01A l120m01a = service1205.findL120m01aByMainId(mainId);
		L121M01B l120m01b = service1205.findL121m01bByUniqueKey(mainId, "9");
		List<L120M01F> list = service1205.findL120m01fByMainId(mainId);
		StringBuilder errorMsg = new StringBuilder();
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1205M01Page.class);
		if (!Util.isEmpty(l120m01a.getAreaDocstatus())
				&& "025".equals(user.getUnitNo())) {
			// 國金部
			boolean haseData = checkPeo(list, UtilConstants.BRANCHTYPE.國金部_營運中心);
			if (!haseData) {
				// 拋出沒有登錄國金部主管訊息
				errorMsg.append(pop.getProperty("l120m01a.error35"))
						.append("、");
			}
		} else if (!Util.isEmpty(l120m01a.getAreaDocstatus())
				&& !"025".equals(user.getUnitNo())) {
			// 營運中心會簽
			boolean haseData = checkPeo(list, UtilConstants.BRANCHTYPE.國金部_營運中心);
			if (!haseData) {
				// 拋出沒有登錄營運中心主管訊息
				errorMsg.append(pop.getProperty("l120m01a.error34"))
						.append("、");
			}
		}

		if (errorMsg.length() > 0) {
			errorMsg.deleteCharAt(errorMsg.length() - 1).append(
					pop.getProperty("l120m01a.error16"));
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0015", errorMsg.toString()), getClass());
		}

		if (l120m01b != null) {
			if (!Util.isEmpty(l120m01b.getItemDscr())) {
				// 會簽單位放行主管
				l120m01a.setAreaApprover(user.getUserId());
				// 會簽單位經辦
				l120m01a.setAreaUpdater(l120m01b.getUpdater());
				// 會簽單位放行時間
				l120m01a.setAreaApprTime(CapDate.getCurrentTimestamp());
				// 會簽文件狀態
				l120m01a.setAreaDocstatus(CreditDocStatusEnum.營運中心_海外聯貸案_待放行
						.getCode());
			} else {
				// 尚未登錄海外聯貸案會簽意見，請登入後再執行本功能！
				throw new CapMessageException(
						pop.getProperty("l120m01a.error25"), getClass());
			}
		} else {
			// 尚未登錄海外聯貸案會簽意見，請登入後再執行本功能！
			throw new CapMessageException(pop.getProperty("l120m01a.error25"),
					getClass());
		}

		// 找出已存在放行人員準備刪除
		L120M01F delL120m01f = null;
		// 準備儲存的放行人員
		L120M01F addL120m01f = addSeaL120m01f(mainId, "L4", user.getUserId());
		if (!list.isEmpty()) {
			for (L120M01F l120m01f : list) {
				if ("6".equals(l120m01f.getBranchType())) {
					if ("L4".equals(Util.trim(l120m01f.getStaffJob()))) {
						delL120m01f = l120m01f;
						break;
					}
				}
			}
		}
		if (delL120m01f != null) {
			service1205.delete(delL120m01f);
		}
		service1205.saveSeaAndDel(l120m01a, l120m01b, addL120m01f);
		// service1205.save(l120m01a, l120m01b);
		// if (params.getAsBoolean("showMsg", true)) {
		// // 印出儲存成功訊息!
		// result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
		// .getMainMessage(this.getComponent(), "EFD0017"));
		// }
		return result;
	}

	/**
	 * 退回會簽意見
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult backSea(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		L120M01A l120m01a = service1205.findL120m01aByMainId(mainId);
		L121M01B l121m01b = service1205.findL121m01bByUniqueKey(mainId, "9");
		if (l121m01b == null) {
			Properties pop = MessageBundleScriptCreator
					.getComponentResource(LMS1205M01Page.class);
			// 尚未登錄海外聯貸案會簽意見，請登入後再執行本功能！
			throw new CapMessageException(pop.getProperty("l120m01a.error25"),
					getClass());
		}
		// 會簽文件狀態
		l120m01a.setAreaDocstatus(CreditDocStatusEnum.營運中心_海外聯貸案_會簽中.getCode());
		service1205.save(l120m01a);
		if (params.getAsBoolean("showMsg", true)) {
			// 印出執行成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0018"));
		}
		return result;
	}

	/**
	 * 退會簽中OR呈已會簽(海外聯貸案用)
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Accept, CheckDocStatus = false)
	public IResult returnGoBossSea(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String frag = params.getString("frag");
		L120M01A l120m01a = service1205.findL120m01aByMainId(mainId);
		L121M01B l120m01b = service1205.findL121m01bByUniqueKey(mainId, "9");
		if (l120m01b != null) {
			if (!Util.isEmpty(l120m01b.getItemDscr())) {
				// 會簽單位放行主管
				l120m01a.setAreaApprover(user.getUserId());
				// 會簽單位經辦
				l120m01a.setAreaUpdater(l120m01b.getUpdater());
				// 會簽單位放行時間
				l120m01a.setAreaApprTime(CapDate.getCurrentTimestamp());
				// 會簽文件狀態
				if ("1".equals(frag)) {
					// 退回經辦修改
					l120m01a.setAreaDocstatus(CreditDocStatusEnum.營運中心_海外聯貸案_會簽中
							.getCode());
				} else if ("8".equals(frag)) {
					// 確認
					l120m01a.setAreaDocstatus(CreditDocStatusEnum.營運中心_海外聯貸案_已會簽
							.getCode());
				}
			}
		} else {
			Properties pop = MessageBundleScriptCreator
					.getComponentResource(LMS1205M01Page.class);
			throw new CapMessageException(pop.getProperty("l120m01a.error25"),
					getClass());
		}
		service1205.save(l120m01a, l120m01b);
		// if (params.getAsBoolean("showMsg", true)) {
		// // 印出儲存成功訊息!
		// result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
		// .getMainMessage(this.getComponent(), "EFD0017"));
		// }
		return result;
	}

	/**
	 * 儲存會簽內容(授管處補充說明+審查意見)
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveSignContent2(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		CapAjaxFormResult L120M01aForm13 = new CapAjaxFormResult();
		CapAjaxFormResult LMS1205S01Form = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String itemDscr0A = params.getString("itemDscr0A");
		String itemDscr0B = params.getString("itemDscr0B");
		String sHeadLeader = trimNull(params.getString("sHeadLeader"));
		String sHeadSubLeader = trimNull(params.getString("sHeadSubLeader"));
		String sHeadReCheck = trimNull(params.getString("sHeadReCheck"));
		String sHeadAppraiser = trimNull(params.getString("sHeadAppraiser"));
		LMS1205S01Form.set("headLeader", withIdName(Util.trim(sHeadLeader)));
		LMS1205S01Form.set("headSubLeader",
				withIdName(Util.trim(sHeadSubLeader)));
		LMS1205S01Form.set("headReCheck", withIdName(Util.trim(sHeadReCheck)));
		LMS1205S01Form.set("headAppraiser",
				withIdName(Util.trim(sHeadAppraiser)));

		L120M01aForm13.set("itemDscr0A", itemDscr0A);
		L120M01aForm13.set("itemDscr0B", itemDscr0B);
		L120M01D model1 = service1205.findL120m01dByUniqueKey(mainId, "A");
		L120M01D model2 = service1205.findL120m01dByUniqueKey(mainId, "B");
		// List<L120M01F> list = service1205.findL120m01fByMainId(mainId);
		if (model1 == null) {
			model1 = new L120M01D();
			model1.setMainId(mainId);
			model1.setItemType("A");
			model1.setCreateTime(CapDate.getCurrentTimestamp());
			model1.setCreator(user.getUserId());
		}
		if (model2 == null) {
			model2 = new L120M01D();
			model2.setMainId(mainId);
			model2.setItemType("B");
			model2.setCreateTime(CapDate.getCurrentTimestamp());
			model2.setCreator(user.getUserId());
		}

		if (!Util.isEmpty(Util.trim(itemDscr0A))) {
			model1.setItemDscr(itemDscr0A);
		} else {
			model1.setItemDscr("");
		}
		if (!Util.isEmpty(Util.trim(itemDscr0B))) {
			model2.setItemDscr(itemDscr0B);
		} else {
			model2.setItemDscr("");
		}
		L120M01A l120m01a = service1205.findL120m01aByMainId(mainId);
		try {
			// service1205.saveArea(l120m01a, model1, model2, saveList);
			service1205.save(l120m01a, model1, model2);
		} catch (Exception e) {
			logger.error("[saveSignContent2] service1205.save EXCEPTION!!", e);
			Map<String, String> param = new HashMap<String, String>();
			param.put("colName", space);
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
		}
		if (params.getAsBoolean("showMsg", true)) {
			// 印出儲存成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0017"));
		}
		result.set("L120M01aForm13", L120M01aForm13);
		result.set("LMS1205S01Form", LMS1205S01Form);
		return result;
	}

	/**
	 * 儲存簽章欄(授管處)
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveSignContentF2(PageParameters params)
			throws CapException {
		// MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		CapAjaxFormResult L120M01aForm13 = new CapAjaxFormResult();
		CapAjaxFormResult LMS1205S01Form = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		// String itemDscr0A = params.getString("itemDscr0A");
		// String itemDscr0B = params.getString("itemDscr0B");
		String sHeadLeader = trimNull(params.getString("sHeadLeader"));
		String sHeadSubLeader = trimNull(params.getString("sHeadSubLeader"));
		String sHeadReCheck = trimNull(params.getString("sHeadReCheck"));
		String sHeadAppraiser = trimNull(params.getString("sHeadAppraiser"));
		String sUnitManager2 = trimNull(params.getString("sUnitManager2"));

		LMS1205S01Form.set("headLeader", withIdName(Util.trim(sHeadLeader)));
		LMS1205S01Form.set("headSubLeader",
				withIdName(Util.trim(sHeadSubLeader)));
		LMS1205S01Form.set("headReCheck", withIdName(Util.trim(sHeadReCheck)));
		LMS1205S01Form.set("headAppraiser",
				withIdName(Util.trim(sHeadAppraiser)));

		List<L120M01F> list = service1205.findL120m01fByMainId(mainId);
		List<L120M01F> saveList = new ArrayList<L120M01F>();
		if (!list.isEmpty()) {
			for (L120M01F model : list) {
				if ("4".equals(model.getBranchType())) {
					saveList.add(model);
				}
			}
		}
		if (!saveList.isEmpty()) {
			service1205.delListL120m01f(saveList);
			saveList.clear();
		}
		// 第一次新增營運簽章欄
		saveList.add(addHeadL120m01f(mainId, "L6", sHeadLeader));
		saveList.add(addHeadL120m01f(mainId, "L5", sHeadSubLeader));
		saveList.add(addHeadL120m01f(mainId, "L3", sHeadReCheck));
		saveList.add(addHeadL120m01f(mainId, "L1", sHeadAppraiser));
		saveList.add(addHeadL120m01f(mainId, "L9", sUnitManager2));
		L120M01A l120m01a = service1205.findL120m01aByMainId(mainId);
		try {
			// service1205.saveArea(l120m01a, model1, model2, saveList);
			service1205.saveArea(l120m01a, saveList);
		} catch (Exception e) {
			logger.error("[saveSignContent2] service1205.save EXCEPTION!!", e);
			Map<String, String> param = new HashMap<String, String>();
			param.put("colName", space);
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
		}
		if (params.getAsBoolean("showMsg", true)) {
			// 印出儲存成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0017"));
		}
		result.set("L120M01aForm13", L120M01aForm13);
		result.set("LMS1205S01Form", LMS1205S01Form);
		return result;
	}

	// /**
	// * 儲存會簽內容(營運中心說明及意見)
	// *
	// * @param params
	// * PageParameters
	// * @param parent
	// * Component
	// * @return CapAjaxFormResult
	// * @throws CapException
	// */
	// @DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	// public IResult saveSignContent3(PageParameters params)
	// throws CapException {
	// MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
	// CapAjaxFormResult result = new CapAjaxFormResult();
	// CapAjaxFormResult L120M01aForm15 = new CapAjaxFormResult();
	// CapAjaxFormResult LMS1205S01Form = new CapAjaxFormResult();
	// String mainId = params.getString(EloanConstants.MAIN_ID);
	// String itemDscr07 = params.getString("itemDscr07");
	// String itemDscr08 = params.getString("itemDscr08");
	// String sAreaLeader = trimNull(params.getString("sAreaLeader"));
	// String sAreaSubLeader = trimNull(params.getString("sAreaSubLeader"));
	// String sAreaManager = trimNull(params.getString("sAreaManager"));
	// String sAreaAppraiser = trimNull(params.getString("sAreaAppraiser"));
	// String itemTitle = params.getString("itemTitle");
	// String _itemTitle = params.getString("_itemTitle");
	//
	// if (!Util.isEmpty(sAreaLeader)) {
	// LMS1205S01Form
	// .set("areaLeader", withIdName(Util.trim(sAreaLeader)));
	// }
	// if (!Util.isEmpty(sAreaSubLeader)) {
	// LMS1205S01Form.set("areaSubLeader",
	// withIdName(Util.trim(sAreaSubLeader)));
	// }
	// if (!Util.isEmpty(sAreaManager)) {
	// LMS1205S01Form.set("areaManager",
	// withIdName(Util.trim(sAreaManager)));
	// }
	// if (!Util.isEmpty(sAreaAppraiser)) {
	// LMS1205S01Form.set("areaAppraiser",
	// withIdName(Util.trim(sAreaAppraiser)));
	// }
	//
	// L120M01aForm15.set("itemDscr07", itemDscr07);
	// L120M01aForm15.set("itemDscr08", itemDscr08);
	// L120M01D model1 = service1205.findL120m01dByUniqueKey(mainId, "7");
	// L120M01D model2 = service1205.findL120m01dByUniqueKey(mainId, "8");
	// // List<L120M01F> list = service1205.findL120m01fByMainId(mainId);
	// if (model1 == null) {
	// model1 = new L120M01D();
	// model1.setMainId(mainId);
	// model1.setItemType("7");
	// model1.setCreateTime(CapDate.getCurrentTimestamp());
	// model1.setCreator(user.getUserId());
	// }
	// if (model2 == null) {
	// model2 = new L120M01D();
	// model2.setMainId(mainId);
	// model2.setItemType("8");
	// model2.setCreateTime(CapDate.getCurrentTimestamp());
	// model2.setCreator(user.getUserId());
	// }
	//
	// if (!Util.isEmpty(Util.trim(itemDscr07))) {
	// model1.setItemDscr(itemDscr07);
	// } else {
	// model1.setItemDscr("");
	// }
	// if (!Util.isEmpty(Util.trim(itemDscr08))) {
	// model2.setItemDscr(itemDscr08);
	// } else {
	// model2.setItemDscr("");
	// }
	// if (!Util.isEmpty(Util.trim(itemTitle))) {
	// if ("1".equals(Util.trim(_itemTitle))) {
	// L120M01A l120m01a = service1205.findL120m01aByMainId(mainId);
	// model2.setItemTitle(Util.trim(l120m01a.getRptTitleArea1()));
	// } else {
	// model2.setItemTitle(Util.trim(itemTitle));
	// }
	// }
	// L120M01A l120m01a = service1205.findL120m01aByMainId(mainId);
	// try {
	// // service1205.saveArea(l120m01a, model1, model2, saveList);
	// service1205.save(l120m01a, model1, model2);
	// } catch (Exception e) {
	// logger.error("[saveSignContent3] service1205.save EXCEPTION!!", e);
	// Map<String, String> param = new HashMap<String, String>();
	// param.put("colName", space);
	// throw new CapMessageException(RespMsgHelper.getMessage(parent,
	// "EFD0007", param), getClass());
	// }
	// if (params.getAsBoolean("showMsg", true)) {
	// // 印出儲存成功訊息!
	// result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
	// .getMainMessage(this.getComponent(), "EFD0017"));
	// }
	// result.set("L120M01aForm15", L120M01aForm15);
	// result.set("LMS1205S01Form", LMS1205S01Form);
	// return result;
	// }

	// /**
	// * 儲存簽章欄(營運中心)
	// *
	// * @param params
	// * @param parent
	// * @return
	// * @throws CapException
	// */
	// @DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	// public IResult saveSignContentF3(PageParameters params)
	// throws CapException {
	// // MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
	// CapAjaxFormResult result = new CapAjaxFormResult();
	// CapAjaxFormResult L120M01aForm15 = new CapAjaxFormResult();
	// CapAjaxFormResult LMS1205S01Form = new CapAjaxFormResult();
	// String mainId = params.getString(EloanConstants.MAIN_ID);
	// // String itemDscr07 = params.getString("itemDscr07");
	// // String itemDscr08 = params.getString("itemDscr08");
	// String sAreaLeader = trimNull(params.getString("sAreaLeader"));
	// String sAreaSubLeader = trimNull(params.getString("sAreaSubLeader"));
	// String sAreaManager = trimNull(params.getString("sAreaManager"));
	// String sAreaAppraiser = trimNull(params.getString("sAreaAppraiser"));
	// String sUnitManager3 = trimNull(params.getString("sUnitManager3"));
	//
	// LMS1205S01Form.set("areaLeader", withIdName(Util.trim(sAreaLeader)));
	// LMS1205S01Form.set("areaSubLeader",
	// withIdName(Util.trim(sAreaSubLeader)));
	// LMS1205S01Form.set("areaManager", withIdName(Util.trim(sAreaManager)));
	// LMS1205S01Form.set("areaAppraiser",
	// withIdName(Util.trim(sAreaAppraiser)));
	//
	// List<L120M01F> list = service1205.findL120m01fByMainId(mainId);
	//
	// List<L120M01F> saveList = new ArrayList<L120M01F>();
	// if (!Util.isEmpty(sAreaLeader) && !Util.isEmpty(sAreaSubLeader)
	// && !Util.isEmpty(sAreaManager) && !Util.isEmpty(sAreaAppraiser)) {
	// if (!list.isEmpty()) {
	// for (L120M01F model : list) {
	// if ("3".equals(model.getBranchType())) {
	// saveList.add(model);
	// }
	// }
	// }
	// if (!saveList.isEmpty()) {
	// service1205.delListL120m01f(saveList);
	// saveList.clear();
	// }
	// // 第一次新增營運簽章欄
	// saveList.add(addAreaL120m01f(mainId, "L6", sAreaLeader));
	// saveList.add(addAreaL120m01f(mainId, "L5", sAreaSubLeader));
	// saveList.add(addAreaL120m01f(mainId, "L3", sAreaManager));
	// saveList.add(addAreaL120m01f(mainId, "L1", sAreaAppraiser));
	// saveList.add(addAreaL120m01f(mainId, "L9", sUnitManager3));
	// }
	//
	// L120M01A l120m01a = service1205.findL120m01aByMainId(mainId);
	// try {
	// // service1205.saveArea(l120m01a, model1, model2, saveList);
	// service1205.saveArea(l120m01a, saveList);
	// } catch (Exception e) {
	// logger.error("[saveSignContent3] service1205.save EXCEPTION!!", e);
	// Map<String, String> param = new HashMap<String, String>();
	// param.put("colName", space);
	// throw new CapMessageException(RespMsgHelper.getMessage(parent,
	// "EFD0007", param), getClass());
	// }
	// if (params.getAsBoolean("showMsg", true)) {
	// // 印出儲存成功訊息!
	// result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
	// .getMainMessage(this.getComponent(), "EFD0017"));
	// }
	// result.set("L120M01aForm15", L120M01aForm15);
	// result.set("LMS1205S01Form", LMS1205S01Form);
	// return result;
	// }

	/**
	 * 新建國金部簽章欄
	 * 
	 * @param mainId
	 * @param headJob
	 * @param headNo
	 * @return
	 */
	public L120M01F addSeaL120m01f(String mainId, String headJob, String headNo) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		L120M01F l120m01f = new L120M01F();
		l120m01f.setMainId(mainId);
		l120m01f.setBranchType("6");
		l120m01f.setBranchId(user.getUnitNo());
		l120m01f.setStaffJob(headJob);
		l120m01f.setStaffNo(headNo);
		l120m01f.setCreator(user.getUserId());
		l120m01f.setCreateTime(CapDate.getCurrentTimestamp());
		l120m01f.setUpdater(user.getUserId());
		l120m01f.setUpdateTime(CapDate.getCurrentTimestamp());
		return l120m01f;
	}

	/**
	 * 新建授管處簽章欄
	 * 
	 * @param mainId
	 * @param headJob
	 * @param headNo
	 * @return
	 */
	private L120M01F addHeadL120m01f(String mainId, String headJob,
			String headNo) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		L120M01F l120m01f = new L120M01F();
		l120m01f.setMainId(mainId);
		l120m01f.setBranchType("4");
		l120m01f.setBranchId(user.getUnitNo());
		l120m01f.setStaffJob(headJob);
		l120m01f.setStaffNo(headNo);
		l120m01f.setCreator(user.getUserId());
		l120m01f.setCreateTime(CapDate.getCurrentTimestamp());
		l120m01f.setUpdater(user.getUserId());
		l120m01f.setUpdateTime(CapDate.getCurrentTimestamp());
		return l120m01f;
	}

	// /**
	// * 新建營運中心簽章欄
	// *
	// * @param mainId
	// * @param areaJob
	// * @param areaNo
	// * @return
	// */
	// private L120M01F addAreaL120m01f(String mainId, String areaJob,
	// String areaNo) {
	// MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
	// L120M01F l120m01f = new L120M01F();
	// l120m01f.setMainId(mainId);
	// l120m01f.setBranchType("3");
	// l120m01f.setBranchId(user.getUnitNo());
	// l120m01f.setStaffJob(areaJob);
	// l120m01f.setStaffNo(areaNo);
	// l120m01f.setCreator(user.getUserId());
	// l120m01f.setCreateTime(CapDate.getCurrentTimestamp());
	// l120m01f.setUpdater(user.getUserId());
	// l120m01f.setUpdateTime(CapDate.getCurrentTimestamp());
	// return l120m01f;
	// }

	// /**
	// * 查詢會簽內容(營運中心)
	// *
	// * @param params
	// * PageParameters
	// * @param parent
	// * Component
	// * @return CapAjaxFormResult
	// * @throws CapException
	// */
	// @DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	// public IResult querySignContent(PageParameters params)
	// throws CapException {
	// CapAjaxFormResult result = new CapAjaxFormResult();
	// String mainId = params.getString(EloanConstants.MAIN_ID);
	// L121M01B model = service1205.findL121m01bByUniqueKey(mainId, "9");
	// if (model != null) {
	// CapAjaxFormResult formSea = DataParse.toResult(model);
	// formSea.set("itemDscr09", Util.trim(model.getItemDscr()));
	// result.set("formSea", formSea);
	// } else {
	// CapAjaxFormResult formSea = new CapAjaxFormResult();
	// formSea.set("itemDscr09", "");
	// result.set("formSea", formSea);
	// }
	// return result;
	// }

	/**
	 * 查詢會簽內容(授管處補充說明+審查意見)
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult querySignContent2(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		L120M01D l120m01d0A = service1205.findL120m01dByUniqueKey(mainId, "A");
		L120M01D l120m01d0B = service1205.findL120m01dByUniqueKey(mainId, "B");
		CapAjaxFormResult form0AL120m01d = new CapAjaxFormResult();
		CapAjaxFormResult form0BL120m01d = new CapAjaxFormResult();
		if (l120m01d0A != null) {
			form0AL120m01d = DataParse.toResult(l120m01d0A);
			form0AL120m01d.set("tItemDscr0A",
					Util.trim(l120m01d0A.getItemDscr()));
		} else {
			form0AL120m01d = new CapAjaxFormResult();
			form0AL120m01d.set("tItemDscr0A", "");
		}
		if (l120m01d0B != null) {
			form0BL120m01d = DataParse.toResult(l120m01d0B);
			form0BL120m01d.set("tItemDscr0B",
					Util.trim(l120m01d0B.getItemDscr()));
		} else {
			form0BL120m01d = new CapAjaxFormResult();
			form0BL120m01d.set("tItemDscr0B", "");
		}
		form0AL120m01d.add(form0BL120m01d);
		result.set("L120M01aForm13", form0AL120m01d);
		return result;
	}

	// /**
	// * 查詢會簽內容(營運中心說明及意見)
	// *
	// * @param params
	// * PageParameters
	// * @param parent
	// * Component
	// * @return CapAjaxFormResult
	// * @throws CapException
	// */
	// @DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	// public IResult querySignContent3(PageParameters params)
	// throws CapException {
	// CapAjaxFormResult result = new CapAjaxFormResult();
	// String mainId = params.getString(EloanConstants.MAIN_ID);
	// L120M01D l120m01d07 = service1205.findL120m01dByUniqueKey(mainId, "7");
	// L120M01D l120m01d08 = service1205.findL120m01dByUniqueKey(mainId, "8");
	// CapAjaxFormResult form07L120m01d = new CapAjaxFormResult();
	// CapAjaxFormResult form08L120m01d = new CapAjaxFormResult();
	// if (l120m01d07 != null) {
	// form07L120m01d = DataParse.toResult(l120m01d07);
	// form07L120m01d.set("tItemDscr07",
	// Util.trim(l120m01d07.getItemDscr()));
	// } else {
	// form07L120m01d = new CapAjaxFormResult();
	// form07L120m01d.set("tItemDscr07", "");
	// }
	// if (l120m01d08 != null) {
	// form08L120m01d = DataParse.toResult(l120m01d08);
	// form08L120m01d.set("tItemDscr08",
	// Util.trim(l120m01d08.getItemDscr()));
	// } else {
	// form08L120m01d = new CapAjaxFormResult();
	// form08L120m01d.set("tItemDscr08", "");
	// }
	// form07L120m01d.add(form08L120m01d);
	// result.set("L120M01aForm15", form07L120m01d);
	// return result;
	// }

	/**
	 * 提會(提授審、提催收、提常董)
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult sendTo(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String hqMeetFlag = params.getString("hqMeetFlag");
		// J-110-0521_05097_B1001 Web e-Loan海外授信系統增加留存案件流程紀錄
		String meetingDate = Util.trim(params.getString("meetingDate"));
		String mainId = params.getString(EloanConstants.MAIN_ID);
		L120M01A model = service1205.findL120m01aByMainId(mainId);
		if (model != null) {
			if (!"3".equals(hqMeetFlag) && !UtilConstants.Casedoc.HqMeetFlag.審計委員會.equals(hqMeetFlag)) {
				model.setMeetingType(hqMeetFlag);
			}
			model.setHqMeetFlag(hqMeetFlag);
			service1205.save(model);

			// J-110-0521_05097_B1001 Web e-Loan海外授信系統增加留存案件流程紀錄
			// J-109-0479_05097_B1001 Web
			// e-Loan簽報書增加各別流程控管階段的時間點並提供列印案件階段進度及統計excel下載
			if (Util.notEquals(meetingDate, "")) {
				L120S17A l120s17a = service1205.findL120s17aByMainId(mainId);
				if (l120s17a == null) {
					l120s17a = new L120S17A();
					l120s17a.setMainId(mainId);
					l120s17a.setCreateTime(CapDate.getCurrentTimestamp());
					l120s17a.setCreator(user.getUserId());
				}

				if (Util.equals(hqMeetFlag, "1")) {
					// 提授審會
					l120s17a.setDateF1(Util.parseDate(meetingDate));
				} else if (Util.equals(hqMeetFlag, "2")) {
					// 提催收會
					l120s17a.setDateF2(Util.parseDate(meetingDate));
				} else if (Util.equals(hqMeetFlag, UtilConstants.Casedoc.HqMeetFlag.審計委員會)) {
					// J-113-0337  配合本行將於第18屆董事會設置審計委員會替代監查人，新增「審計委員會」
					l120s17a.setDateF4(Util.parseDate(meetingDate));
				} else {
					// 提常董會
					l120s17a.setDateF3(Util.parseDate(meetingDate));
				}

				if (UtilConstants.unitType.授管處.equals(Util.trim(user
						.getUnitType()))) {

					l120s17a.setRoleG(user.getUserId());

				}

				l120s17a.setUpdater(user.getUserId());
				l120s17a.setUpdateTime(CapDate.getCurrentTimestamp());

				service1205.save(l120s17a);

				// J-110-0521_05097_B1001 Web e-Loan海外授信系統增加留存案件流程紀錄
				// J-109-0479_05097_B1001 Web
				// e-Loan簽報書增加各別流程控管階段的時間點並提供列印案件階段進度及統計excel下載
				lmsService.setL120s17aData(mainId);

			}
		}
		// // 印出執行成功訊息!
		// result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
		// RespMsgHelper.getMainMessage(this.getComponent(), "EFD0018"));
		return result;
	}

	// /**
	// * 查詢授審會會期
	// *
	// * @param params
	// * @param parent
	// * @return
	// * @throws CapException
	// */
	// @DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	// public IResult queryLogin1(PageParameters params)
	// throws CapException {
	// CapAjaxFormResult result = new CapAjaxFormResult();
	// CapAjaxFormResult formLms1200v62 = new CapAjaxFormResult();
	// formLms1200v62.set("rptTitle1a", "");
	// formLms1200v62.set("rptTitle1b", "");
	// formLms1200v62.set("rptTitle1c", "");
	// formLms1200v62.set("rptTitle1d", "");
	// String mainId = params.getString(EloanConstants.MAIN_ID);
	// L120M01A model = service1205.findL120m01aByMainId(mainId);
	// if (model != null) {
	// result.set(EloanConstants.OID, model.getOid());
	// String rptTitle1 = model.getRptTitle1();
	// if (!Util.isEmpty(rptTitle1)) {
	// formLms1200v62.set("rptTitle1a", rptTitle1.substring(0, 3));
	// formLms1200v62.set("rptTitle1b", rptTitle1.substring(4, 6));
	// formLms1200v62.set("rptTitle1c", rptTitle1.substring(7, 9));
	// String times = rptTitle1.substring(11);
	// formLms1200v62.set("rptTitle1d",
	// times.substring(0, times.indexOf("次", 0)));
	// }
	// }
	//
	// result.set("LMS1200V62Form1", formLms1200v62);
	// return result;
	// }
	//
	// /**
	// * 登錄授審會會期(含營運中心)
	// *
	// * @param params
	// * PageParameters
	// * @param parent
	// * Component
	// * @return CapAjaxFormResult
	// * @throws CapException
	// */
	// @DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	// public IResult login1(PageParameters params)
	// throws CapException {
	// CapAjaxFormResult result = new CapAjaxFormResult();
	// String LMS1200V62Form1 = params.getString("LMS1200V62Form1");
	// String caseName = params.getString("caseName");
	// boolean isArea = params.getBoolean("isArea");
	// JSONObject json = JSONObject.fromObject(LMS1200V62Form1);
	// StringBuilder strB = new StringBuilder();
	// strB.append(
	// Util.addZeroWithValue(
	// Util.trim((String) json.get("rptTitle1a")), 3))
	// .append("年")
	// .append(Util.addZeroWithValue(
	// Util.trim((String) json.get("rptTitle1b")), 2))
	// .append("月")
	// .append(Util.addZeroWithValue(
	// Util.trim((String) json.get("rptTitle1c")), 2))
	// .append("日第")
	// .append(Util.trim((String) json.get("rptTitle1d"))).append("次")
	// .append(Util.trim(caseName));
	// // 取得list中所有資料組成的字串
	// String listOid = params.getString("oids");
	// // 取得sign的資料
	// String strSign = ",";
	// String oid = params.getString(EloanConstants.OID);
	// List<L120M01A> list = new ArrayList<L120M01A>();
	// if (!Util.isEmpty(oid)) {
	// L120M01A model = service1205.findL120m01aByOid(oid);
	// if (model != null) {
	// if (isArea) {
	// // 營運中心授審會會期
	// model.setRptTitleArea1(strB.toString());
	// } else {
	// // 授管處授審會/催收會會期
	// model.setRptTitle1(strB.toString());
	// }
	// list.add(model);
	// }
	// } else {
	// // 將已取得的字串轉換成一陣列，分割辨識為sign內容
	// String[] oidArray = listOid.split(strSign);
	// if (oidArray.length > 0) {
	// for (String theOid : oidArray) {
	// L120M01A model = service1205.findL120m01aByOid(theOid);
	// if (model != null) {
	// if (isArea) {
	// // 營運中心授審會會期
	// model.setRptTitleArea1(strB.toString());
	// } else {
	// // 授管處授審會/催收會會期
	// model.setRptTitle1(strB.toString());
	// }
	// list.add(model);
	// }
	// }
	// }
	// }
	//
	// try {
	// service1205.saveL120m01as(list);
	// } catch (Exception e) {
	// logger.error("[login1] service1205.save EXCEPTION!!", e);
	// Map<String, String> param = new HashMap<String, String>();
	// param.put("colName", space);
	// throw new CapMessageException(RespMsgHelper.getMessage(parent,
	// "EFD0007", param), getClass());
	// }
	// // 印出執行成功訊息!
	// result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
	// RespMsgHelper.getMainMessage(this.getComponent(), "EFD0018"));
	// return result;
	// }

	/**
	 * 登錄催收會會期
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult login2(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String LMS1200V63Form1 = params.getString("LMS1200V63Form1");
		JSONObject json = JSONObject.fromObject(LMS1200V63Form1);
		StringBuilder strB = new StringBuilder();
		strB.append(
				Util.addZeroWithValue(
						Util.trim((String) json.get("rptTitle1a")), 3))
				.append("年")
				.append(Util.addZeroWithValue(
						Util.trim((String) json.get("rptTitle1b")), 2))
				.append("月")
				.append(Util.addZeroWithValue(
						Util.trim((String) json.get("rptTitle1c")), 2))
				.append("日第")
				.append(Util.trim((String) json.get("rptTitle1d"))).append("次")
				.append("逾期放款、催收款及呆帳審議委員會");
		// 取得list中所有資料組成的字串
		String listOid = params.getString("oids");
		// 取得sign的資料
		String strSign = ",";
		// 將已取得的字串轉換成一陣列，分割辨識為sign內容
		String[] oidArray = listOid.split(strSign);
		// String oid = params.getString(EloanConstants.OID);
		List<L120M01A> list = new ArrayList<L120M01A>();
		if (oidArray.length > 0) {
			for (String oid : oidArray) {
				L120M01A model = service1205.findL120m01aByOid(oid);
				if (model != null) {
					model.setRptTitle1(strB.toString());
					list.add(model);
				}
			}
		}
		try {
			service1205.saveL120m01as(list);
		} catch (Exception e) {
			logger.error("[login2] service1205.save EXCEPTION!!", e);
			Map<String, String> param = new HashMap<String, String>();
			param.put("colName", space);
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
		}
		// 印出執行成功訊息!
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0018"));
		return result;
	}

	/**
	 * 登錄常董會會期
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult login3(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String LMS1200V64Form1 = params.getString("LMS1200V64Form1");
		String caseName = params.getString("caseName");
		JSONObject json = JSONObject.fromObject(LMS1200V64Form1);
		StringBuilder strB = new StringBuilder();
		strB.append(
				Util.addZeroWithValue(
						Util.trim((String) json.get("rptTitle1a")), 3))
				.append("年")
				.append(Util.addZeroWithValue(
						Util.trim((String) json.get("rptTitle1b")), 2))
				.append("月")
				.append(Util.addZeroWithValue(
						Util.trim((String) json.get("rptTitle1c")), 2))
				.append("日第")
				.append(Util.trim((String) json.get("rptTitle1d")))
				.append("屆第")
				.append(Util.trim((String) json.get("rptTitle1e"))).append("次")
				.append(Util.trim(caseName));
		// 取得list中所有資料組成的字串
		String listOid = params.getString("oids");
		// 取得sign的資料
		String strSign = ",";
		// 將已取得的字串轉換成一陣列，分割辨識為sign內容
		String[] oidArray = listOid.split(strSign);
		// String oid = params.getString(EloanConstants.OID);
		List<L120M01A> list = new ArrayList<L120M01A>();
		if (oidArray.length > 0) {
			for (String oid : oidArray) {
				L120M01A model = service1205.findL120m01aByOid(oid);
				if (model != null) {
					model.setRptTitle2(strB.toString());
					list.add(model);
				}
			}
		}
		try {
			service1205.saveL120m01as(list);
		} catch (Exception e) {
			logger.error("[login3] service1205.save EXCEPTION!!", e);
			Map<String, String> param = new HashMap<String, String>();
			param.put("colName", space);
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
		}
		// 印出執行成功訊息!
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0018"));
		return result;
	}

	/**
	 * 儲存配偶欄位(同本案借保人)
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveCouple(PageParameters params)
			throws CapException {
		return super._LMSM01FormHandler_saveCouple(params);
	}
}