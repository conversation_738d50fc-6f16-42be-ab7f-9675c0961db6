package tw.com.jcs.auth;

import java.util.List;
import java.util.Set;

import tw.com.jcs.auth.model.CodeItem;

/**
 * <pre>
 * CodeItemService
 * </pre>
 * 
 * @since 2011/5/13
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2011/5/13,FantasyHwang,new
 *          <li>2012/02/12,UFO,findXXX增加pgmDept參數
 *          </ul>
 */
public interface CodeItemService {

    /**
     * 重新載入
     */
    public void reload();

    /**
     * findByStep
     * 
     * @since 2011/05/10
     * <AUTHOR>
     * @param pgmDept
     *            指定部門
     * @param roles
     *            角色
     * @param step
     *            階層
     * @return
     */
    List<CodeItem> findByStep(String pgmDept, Set<String> roles, int... step);

    /**
     * findByParent
     * 
     * @since 2011/05/10
     * <AUTHOR>
     * @param pgmDept
     *            指定部門
     * @param roles
     *            角色
     * @param parent
     * @return
     */
    List<CodeItem> findByParent(String pgmDept, Set<String> roles, int parent);

    /**
     * findByParentAndSteps
     * 
     * @since 2011/05/10
     * <AUTHOR>
     * @param pgmDept
     *            指定部門
     * @param roles
     *            角色
     * @param parent
     * @param steps
     *            階層
     * @return
     */
    List<CodeItem> findByParentAndSteps(String pgmDept, Set<String> roles, int parent, int... steps);

    /**
     * 取得系統代碼
     * 
     * @since 2011/05/19
     * <AUTHOR>
     */
    String getSystemType();

    /**
     * 依交易代碼取得URL路徑
     * 
     * @since 2011/05/31
     * <AUTHOR>
     * @param code
     *            代碼
     * @return
     */
    String getUrlPathByCode(int code);

    /**
     * 依交易代碼取得CodeItem
     * 
     * @since 2011/05/31
     * <AUTHOR>
     * @param code
     *            代碼
     * @return {@link tw.com.jcs.auth.model.CodeItem}
     */
    CodeItem getCodeItemByCode(int code);

    /**
     * 依交易代碼取得List<CodeItem>
     * 
     * @since 2011/05/31
     * <AUTHOR>
     * @param _codes
     *            代碼
     * @return
     */
    List<CodeItem> getCodeItemByCodes(int... _codes);

    /**
     * 依URL取得List<CodeItem>
     * 
     * @since 2011/05/31
     * <AUTHOR>
     * @param urls
     * @return
     */
    @Deprecated
    List<CodeItem> getCodeItemByURL(String... urls);

    /**
     * 依code取得程式名稱
     * 
     * @since 2011/07/18
     * <AUTHOR>
     * @param code
     *            代碼
     * @param url
     * @return
     */
    String getPgmNameByCode(int code, String url);

    /**
     * 依code取得指定階層的名稱
     * 
     * @since 2011/05/31
     * <AUTHOR>
     * @param code
     *            代碼
     * @return
     */
    String getParentNameByCode(int code);

    /**
     * 依code取得指定階層的名稱
     * 
     * @since 2011/05/31
     * <AUTHOR>
     * @param code
     *            代碼
     * @param steps
     *            階層
     * @return
     */
    String getParentNameByCode(int code, int... steps);

    /**
     * 依code取得上層代碼
     * 
     * @since 2012/12/26
     * @param code
     *            代碼
     * @return
     */
    String getParentByCode(int code);
}
