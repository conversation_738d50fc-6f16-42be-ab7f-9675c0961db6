package com.mega.eloan.lms.lms.service;

/* 
 * LMS1605Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
import java.util.HashMap;
import java.util.List;

import org.kordamp.json.JSONObject;

import com.mega.eloan.common.exception.GWException;
import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L160M01A;
import com.mega.eloan.lms.model.L160M01B;
import com.mega.eloan.lms.model.L160M01C;
import com.mega.eloan.lms.model.L160M01D;
import com.mega.eloan.lms.model.L161S01A;
import com.mega.eloan.lms.model.L162S01A;
import com.mega.eloan.lms.model.L163S01A;
import com.mega.eloan.lms.model.L164S01A;
import com.mega.eloan.lms.model.L901M01A;
import com.mega.eloan.lms.model.VLUSEDOC01;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.GenericBean;

/**
 * <pre>
 * 動用審核表
 * </pre>
 * 
 * @since 2011/10/5
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/10/5,REX,new
 *          </ul>
 */
public interface LMS1605Service extends AbstractService {

	/**
	 * 刪除動審表主檔資料 根據所選的oid
	 * 
	 * @param oids
	 *            文件編號陣列
	 * @return boolean
	 */
	boolean deleteL160m01as(String[] oids);

	/**
	 * 刪除檔案根據所傳的oid 和 class
	 * 
	 * @param clazz
	 *            model class
	 * @param oids
	 *            陣列
	 * @return boolean
	 */
	@SuppressWarnings("rawtypes")
	public boolean deleteListByOid(Class clazz, String[] oids);

	/**
	 * 刪除動審表額度序號資料
	 * 
	 * @param l160m01bs
	 *            動審表額度序號資料 List
	 */
	public void deleteL160m01bs(List<L160M01B> l160m01bs);

	/**
	 * 刪除主從債務人資料資料
	 * 
	 * @param l162m01as
	 *            主從債務人資料資料 List
	 */
	public void deleteL162m01as(List<L162S01A> l162m01as);

	/**
	 * 儲存動審表額度序號資料
	 * 
	 * @param l160m01bs
	 *            動審表額度序號資料 List
	 */
	public void saveL160m01bList(List<L160M01B> l160m01bs);

	/**
	 * 儲存主從債務人資料表檔
	 * 
	 * @param l162m01as
	 *            主從債務人資料資料 List
	 */
	public void saveL162m01aList(List<L162S01A> l162m01as);

	/**
	 * 查詢L901M01A 動用審核表稽核項目
	 * 
	 * @param itemType
	 *            項目總類
	 * @param branchId
	 *            分行代碼
	 * @return List<L901M01A>
	 */
	public List<L901M01A> findL901m01aByBranchIdAndItemType(String itemType,
			String branchId, String locale);

	/**
	 * 儲存動審表查核項目資料
	 * 
	 * @param list
	 *            List<L160M01C>
	 */
	public void saveL160m01cList(List<L160M01C> list);

	/**
	 * 刪除動審表查核項目資料
	 * 
	 * @param l160m01cs
	 *            List<L160M01C>
	 */
	public void deleteL160m01cs(List<L160M01C> l160m01cs);

	/**
	 * 刪除上傳檔案
	 * 
	 * @param oids
	 *            文件編號
	 */
	void deleteUploadFile(String[] oids);

	/**
	 * 查詢L161M01A 聯貸案參貸比率一覽表主檔
	 * 
	 * @param mainId
	 *            文件編號
	 * @return L161M01A
	 */
	public L161S01A findL161m01aByMainId(String mainId, String cntrNo);

	/**
	 * 查詢L163M01A 先行動用呈核及控制表檔
	 * 
	 * @param mainId
	 *            文件編號
	 * @return L163M01A
	 */
	public L163S01A findL163m01aByMainId(String mainId);

	/**
	 * 儲存L160M01D．案件簽章欄檔
	 * 
	 * @param list
	 *            List<L160M01D>
	 */
	public void saveL160m01dList(List<L160M01D> list);

	/**
	 * 查詢 L160M01D．案件簽章欄檔
	 * 
	 * @param mainId
	 *            案件編號
	 * 
	 * @param staffNo
	 *            員編
	 * @param staffjob
	 *            人員職稱
	 */
	public L160M01D findL160m01d(String mainId, String staffNo, String staffjob);

	/**
	 * 取得聯貸案最大seq
	 * 
	 * @param mainId
	 *            文件編號
	 * @return 最大seq+1
	 */
	int findL161m01bMaxSeqOLD(String mainId);

	/**
	 * 再重新引進時 刪除之前引進的資料
	 * 
	 * @param mainId
	 *            文件編號
	 */
	void deleteListReInclude(String mainId);

	/**
	 * 其它到結案所用的flow
	 * 
	 * @param mainOid
	 *            文件編號
	 * @param model
	 *            資料表
	 * @param setResult
	 *            boolean
	 * @param resultType
	 *            boolean
	 * @throws Throwable
	 */
	public void flowAction(String mainOid, GenericBean model,
			boolean setResult, boolean resultType, boolean upMis)
			throws Throwable;

	/**
	 * 刪除簽章欄 L160M01D
	 * 
	 * @param l160m01ds
	 *            List<L160M01D>
	 * @param isAll
	 *            是否刪除全部(false 除了L6 L7 其餘皆刪除)
	 */
	public void deleteL160m01ds(List<L160M01D> l160m01ds, boolean isAll);

	/**
	 * 當先行動用的再已覆核辦妥完成，讓狀態跑到先行動用已覆核040
	 * 
	 * @param mainOid
	 *            文件編號
	 */
	public void flowActionGo(String mainOid, L163S01A l163s01a)
			throws Throwable;

	/**
	 * 黑名單查詢 J-106-0029-003 Web e-Loan授信簽報書借款人基本資料與動審表黑名單查詢調整使用共用模組
	 * 
	 * @param name
	 *            查詢英文名
	 * @param mainId
	 *            文件編號
	 * @return JSONObject
	 * @throws GWException
	 * 
	 */
	public JSONObject findBlackPage(String name, String mainId)
			throws CapException;

	/**
	 * 上傳MisDb
	 * 
	 * @param L160M01A
	 *            動用審核表
	 */
	public void upLoadMIS(L160M01A L160M01A) throws CapException;

	/**
	 * 查詢先行動用的動審表
	 * 
	 * @param docStatus
	 *            文件狀態
	 * @param ownBrId
	 *            分行號碼
	 */
	public List<L160M01A> findFirstUse(String[] docStatus, String ownBrId);

	/**
	 * 儲存主檔
	 * 
	 * @param l160m01bs
	 *            動審表額度序號資料
	 * 
	 * @param l162s01as
	 *            主從債務人資料表檔
	 * 
	 * @param entity
	 *            model
	 */
	public void saveMain(List<L160M01B> l160m01bs, List<L162S01A> l162s01as,
			GenericBean... entity);

	/**
	 * 儲存動審表查核項目資料
	 * 
	 * @param l160m01cs
	 *            動審表查核項目資料
	 * 
	 * @param entity
	 *            model
	 */
	public void saveL160m01cs(List<L160M01C> l160m01cs, GenericBean... entity);

	/**
	 * 查詢動審表主檔
	 * 
	 * @param mainId
	 *            文件編號
	 * @return 動審表主檔
	 */
	public L160M01A findL160M01AByMaindId(String mainId);

	/**
	 * 查詢動審表主檔 by
	 * 
	 * @param oids
	 *            文件編號
	 * @return 動審表主檔 List
	 */
	public List<L160M01A> findL160M01AByOids(String[] oids);

	/**
	 * 取得借款人 國別資料
	 * 
	 * @param l120m01a
	 *            簽報書主檔
	 * @return Map<custid+dupno , 國別>
	 */
	public HashMap<String, String> getCustCounty(L120M01A l120m01a);

	/**
	 * 取得可動用的額度序號
	 * 
	 * @param caseMainId
	 *            簽報書mainId
	 * @param useBrId
	 *            登錄分行
	 * @param itemType
	 *            額度明細表種類 1額度明細表、2額度批覆表、3母行法人提案意見
	 * @return
	 */
	public List<VLUSEDOC01> getDoCntrNo(String caseMainId, String[] useBrId,
			String itemType);

	/**
	 * 取得可動用的額度序號
	 * 
	 * @param caseMainId
	 *            簽報書mainId
	 * @param cntrNos
	 *            額度序號
	 * @param useBrId
	 *            登錄分行
	 * @param itemType
	 *            額度明細表種類 1額度明細表、2額度批覆表、3母行法人提案意見
	 * @return
	 */
	public List<VLUSEDOC01> getDoCntrNo(String caseMainId, String[] cntrNos,
			String[] useBrId, String itemType);

	/**
	 * 取得聯貸案最大seq
	 * 
	 * @param mainId
	 *            文件編號
	 * @param pid
	 *            L161S01A 的UID
	 * @return 最大seq+1
	 */
	int findL161m01bMaxSeq(String mainId, String pid);

	/**
	 * 用MAINID + UID 找L161S01A
	 * 
	 * @param mainId
	 *            L160M01A 的MAINID
	 * @param pid
	 *            L161S01B的PID
	 * @return
	 */
	public L161S01A findL161m01aByMainIdUid(String mainId, String uid);

	/**
	 * 用MAINID + CNTRNO 找L161S01A
	 * 
	 * @param mainId
	 * @param cntrNo
	 * @return
	 */
	public L161S01A findL161m01aByMainIdCntrno(String mainId, String cntrNo);

	/**
	 * J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public L164S01A findL164s01aByMainIdCustId(String mainId, String custId,
			String dupNo);

	/**
	 * J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
	 * 
	 */
	public void deleteL164s01as(List<L164S01A> l164s01as);

	/**
	 * J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
	 * 
	 * @param list
	 */
	public void saveL164s01aList(List<L164S01A> list);

	/**
	 * J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
	 * 
	 * @param list
	 * @return
	 */
	public List<L162S01A> findL162s01aNeedPriority(List<L162S01A> list,
			String cntrNo);

	/**
	 * J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
	 */
	public List<L162S01A> findL162s01aByMainIdCntrno(String mainId,
			String cntrNo);

	/**
	 * J-110-0040_05097_B1001 Web e-Loan增加「本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)」註記
	 * 
	 * @param oids
	 * @return
	 */
	public List<L162S01A> findL162S01AByOids(String[] oids);

	/**
	 * J-111-0028_05097_B1001 Web e-Loan海外企金授信動用審核表其它事項增加「土建融案件維護表」檢核項目
	 * 
	 * @param l160m01c
	 * @return
	 */
	String isL160m01cHasInputItemField1(L160M01C l160m01c);

	/**
	 * 額度向下僅有該科目時不用上傳QUOTAPPR
	 * 
	 * 檢查是否為需要上傳科目 J-112-0129_05097_B1001 Web
	 * e-Loan系統對於符合單純僅有「交換票據抵用」會計科目之額度序號，不要帶入額度介面檔
	 * 
	 * @param item
	 *            授信科目
	 * @return boolean
	 */
	public boolean isNoUploadQuotapprItem(String item);

	/**
	 * 取得 MIS.ELLNGTEE(ELF401)，如額度明細引入之主從債務人 擔保限額Guarante Amount(GRTAMT)、當地客戶識別ID Local Id(LOCALID)未填，則帶MIS.ELLNGTEE為預設值
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @param newL162s01as
	 * @return
	 */
	public List<L162S01A> checkL162s01aLocalIdAndGrtAmt(String custId, String dupNo, String cntrNo, List<L162S01A> newL162s01as);

}