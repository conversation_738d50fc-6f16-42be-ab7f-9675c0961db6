package com.mega.eloan.lms.mfaloan.bean;

import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import tw.com.iisi.cap.model.GenericBean;

/** 消金抽樣覆審控制檔 **/
public class ELF491C extends GenericBean{

	private static final long serialVersionUID = 1L;

	/** 文件編號UNID */
	@Column(name = "ELF491C_UNID", length = 32, columnDefinition = "CHAR(32)", nullable=false,unique = true)
	private String elf491c_unid;

	/** 抽樣規則 => 一份覆審報告表可能含N個抽樣規則 (參考 LMS.C240M01C ) 
	 * <ul>
	 * <li>值域 [ DN_95 , LS_95 ] 是來自批次SLMS-00127 : J-109-0372 基準日R95-1每年6月底有效筆數 , 嗣後逐年按受檢追蹤對象計117筆之10%範圍內,以隨機方式產製名單 
	 * 	   <ul> 
	 *     <li>select * from mis.elf491c where elf491c_lrdate='2022-06-30' and elf491c_rule_no in ('DN_95', 'LS_95') order by elf491c_rule_no
	 *     </li>
	 *     <li>在 elf491c_lrdate='2022-06-30' 時，elf491c_rule_no='DN_95' 只會有1筆(辨別 批次 已跑完, DN 表示 DONE )
	 *     </li>
	 *     <li>在 elf491c_lrdate='2022-06-30' 時，elf491c_rule_no='LS_95' 會有N筆
	 *     </li>
	 *    </ul>
	 * </li>
	 * <li>值域 [ 95-1 ]
	 * 	   <ul> 
	 *     <li>在 營業中心 實際抽樣覆審之後，要上傳MIS，供 統計報表 去檢視「已抽樣件數」(以前在 ELF491_REMOMO 用前13碼、後13碼的方式不太好)
	 *     </li>
	 *    </ul>
	 * </li>
	 * <li>值域 [ 8-1 ]
	 * </li>
	 * <li>值域 [ 13 ]
	 * 	   <ul> 
	 *     <li>產品種類08,71（SELECT * FROM LN.LNF07A where LNF07A_KEY_1 = 'CLS REEXAMINATION' and LNF07A_KEY_2='RULE 13'）
	 *     </li>
	 *    </ul>
	 * </li>
	 * <li>值域 [ R1R2S ]
	 * 	   <ul> 
	 *     <li>在 營業中心 實際抽樣覆審之後，要上傳MIS，供 統計報表 去檢視「已抽樣件數」(以前在 ELF491_REMOMO 用前13碼、後13碼的方式不太好)
	 *     </li>
	 * 	   <ul> 
	 *     <li>一開始本來預計在 e-Loan 的覆審工作底稿針對{中長期額度、門檻以下}、{短期額度、門檻以下}有各自的 Rule。例如：1-1, 2-1 <br/>
	 *     		但後來是採用 1~2 這一個 Rule代號去同時代表{長期、短期}
	 *     </li>
	 *     <li>在 LMS.C241M01A.RETRIALKIND 是放 1~2 ；上傳到 ELF491C 時，改成放 R1R2S
	 *     </li>
	 *     <li>今年度的 目標件數 的資料來源：是在 LNBD9455 裡，把去年底的該類總件數寫到 ELF491_REMOMO的前13碼（where elf491_custId='AAAAAAAAAA'）
	 *     </li>
	 *    </ul>
	 * </li>
	 * </ul>
	 */
	@Column(name = "ELF491C_RULE_NO", length = 5, columnDefinition = "CHAR(5)", nullable=false,unique = true)
	private String elf491c_rule_no;
	
	/** 分行代號 */
	@Column(name = "ELF491C_BRANCH", length = 3, columnDefinition = "CHAR(3)", nullable=false)
	private String elf491c_branch;
	
	/** 借款人統一編號 */
	@Column(name = "ELF491C_CUSTID", length = 10, columnDefinition = "CHAR(10)", nullable=false)
	private String elf491c_custid;
	
	/** 重複序號 */
	@Column(name = "ELF491C_DUPNO", length = 1, columnDefinition = "CHAR(1)", nullable=false)
	private String elf491c_dupno;
		
	/** 覆審日期*/
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF491C_LRDATE", columnDefinition = "DATE")
	private Date elf491c_lrdate;
	
	/** 資料修改人  */
	@Column(name = "ELF491C_UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String elf491c_updater;
	
	/** 資料更新日 */
	@Column(name = "ELF491C_TMESTAMP", columnDefinition = "TIMESTAMP")
	private Timestamp elf491c_tmestamp;

	public String getElf491c_unid() {
		return elf491c_unid;
	}

	public void setElf491c_unid(String elf491c_unid) {
		this.elf491c_unid = elf491c_unid;
	}

	public String getElf491c_rule_no() {
		return elf491c_rule_no;
	}

	public void setElf491c_rule_no(String elf491c_rule_no) {
		this.elf491c_rule_no = elf491c_rule_no;
	}

	public String getElf491c_branch() {
		return elf491c_branch;
	}

	public void setElf491c_branch(String elf491c_branch) {
		this.elf491c_branch = elf491c_branch;
	}

	public String getElf491c_custid() {
		return elf491c_custid;
	}

	public void setElf491c_custid(String elf491c_custid) {
		this.elf491c_custid = elf491c_custid;
	}

	public String getElf491c_dupno() {
		return elf491c_dupno;
	}

	public void setElf491c_dupno(String elf491c_dupno) {
		this.elf491c_dupno = elf491c_dupno;
	}

	public Date getElf491c_lrdate() {
		return elf491c_lrdate;
	}

	public void setElf491c_lrdate(Date elf491c_lrdate) {
		this.elf491c_lrdate = elf491c_lrdate;
	}

	public String getElf491c_updater() {
		return elf491c_updater;
	}

	public void setElf491c_updater(String elf491c_updater) {
		this.elf491c_updater = elf491c_updater;
	}

	public Timestamp getElf491c_tmestamp() {
		return elf491c_tmestamp;
	}

	public void setElf491c_tmestamp(Timestamp elf491c_tmestamp) {
		this.elf491c_tmestamp = elf491c_tmestamp;
	}

}
