package com.mega.eloan.lms.rpt.service.impl;

import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import jxl.format.Alignment;
import jxl.format.Border;
import jxl.format.BorderLineStyle;
import jxl.format.VerticalAlignment;
import jxl.write.Label;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WriteException;
import jxl.write.biff.RowsExceededException;

import org.springframework.stereotype.Service;

import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;

import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.dao.L140M01ADao;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.eloandb.service.EloandbcmsBASEService;
import com.mega.eloan.lms.mfaloan.service.MisPTEAMAPPService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C101S01E;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140S01A;
import com.mega.eloan.lms.rpt.service.CLS180R57Service;
import com.mega.sso.service.BranchService;

/**
 * <pre>
 * 待售房屋(餘屋)去化落後追蹤表
 * </pre>
 * 
 * @since 2022
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Service
public class CLS180R57ServiceImpl extends AbstractCapService implements CLS180R57Service {

	@Resource
	EloandbBASEService eloandbBASEService;
	
	@Resource
	MisdbBASEService misdbBASEService;
	
	@Resource
	BranchService branchService;
	
	@Resource
	CodeTypeService codeTypeService;
	
	@Resource
	MisPTEAMAPPService misPTEAMAPPService;
	
	@Resource
	EloandbcmsBASEService eloandbcmsBASEService;
	
	@Resource
	L140M01ADao l140m01adao;
	
	@Resource
	CLSService clsService;

	@Override
	public Map<String, Integer> getTitleMap(){
		Map<String, Integer> titleMap = new LinkedHashMap<String, Integer>();
		titleMap.put("CLS180R57.midCycleAnnualReviewList", 10);//中期循環年度檢視表
		return titleMap;
	}

	@Override
	public Map<String, Integer> getHeader2Map(){
		Map<String, Integer> titleMap = new LinkedHashMap<String, Integer>();
		titleMap.put("CLS180R57.C101S01E.eChkFlag", 15); // 有無票信退補記錄
		titleMap.put("CLS180R57.C101S01E.eChkDDate", 15);// 票信資料截止日
		titleMap.put("CLS180R57.C101S01E.eChkQDate", 15);// 票信查詢日期
		titleMap.put("CLS180R57.C101S01E.isFromOld", 15);// 無法提供票信電子資料
		titleMap.put("CLS180R57.C101S01E.isQdata1", 15); // 婉卻紀錄
		titleMap.put("CLS180R57.C101S01E.isQdata29", 15);// 異常通報紀錄
		titleMap.put("CLS180R57.C101S01E.isQdata6", 15); // 主從債務人(E-LOAN查詢)
		titleMap.put("CLS180R57.C101S01E.isQdata8", 15); // 證券暨期貨違約交割紀錄
		titleMap.put("CLS180R57.C101S01E.isQdata9", 15); // 退票紀錄
		titleMap.put("CLS180R57.C101S01E.isQdata10", 15);// 拒絕往來紀錄
		titleMap.put("CLS180R57.C101S01E.isQdata11", 15);// 主債務逾期放款、催收款、呆帳紀錄
		titleMap.put("CLS180R57.C101S01E.isQdata13", 15);// 信用卡強停紀錄
		titleMap.put("CLS180R57.C101S01G.C101S01Q.C101S01R.chkItem2", 15);//消債條例信用註記、銀行公會債務協商註記及其他補充註記
		titleMap.put("CLS180R57.C101S01E.isQdata7", 15); // 黑名單(BTT 交易代號0015、功能別11)
		titleMap.put("CLS180R57.C101S04W.rpaData", 20); // 司法院受監護/輔助宣告資料
		return titleMap;
	}
	
	@Override
	public int setBodyContentOfDebtorAndGuarantorData(WritableSheet sheet, int colIndex, int rowIndex, Properties prop, 
														Map<String, C101S01E> debtorMap, String custPos, Map<String, String> otherDataMap,
														Map<String, String> haveNoNaMap, Map<String, String> yesNoMap) throws RowsExceededException, WriteException{
		
		WritableFont font = new WritableFont(WritableFont.createFont("標楷體"), 12);
		WritableCellFormat cellFormat = new WritableCellFormat(font);
		{
			cellFormat.setWrap(true);
			cellFormat.setAlignment(Alignment.CENTRE);
			cellFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
			cellFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
		}
		
		if(debtorMap.size() > 0){
			Map<String, String > custPosMap = new HashMap<String, String>();
			custPosMap.put("B", prop.getProperty("CLS180R57.borrower"));//借款人
			custPosMap.put("G", prop.getProperty("CLS180R57.guarantor"));//保證人
			custPosMap.put("C", prop.getProperty("CLS180R57.coBorrower"));//共同借款人
			this.mergeFieldAndSetWidth(sheet, colIndex, colIndex, rowIndex, rowIndex + debtorMap.size()-1, custPosMap.get(custPos), cellFormat, 10);
			colIndex++;
		}
		
		for(String custId : debtorMap.keySet()){
			C101S01E borrower = debtorMap.get(custId);
			
			if(borrower != null){
				sheet.setColumnView(colIndex, 15);
				sheet.addCell(new Label(colIndex++, rowIndex, otherDataMap.get(custId+"name"), cellFormat));//姓名
				sheet.addCell(new Label(colIndex++, rowIndex, custId, cellFormat));//ID
				sheet.addCell(new Label(colIndex++, rowIndex, haveNoNaMap.get(borrower.getEChkFlag()), cellFormat));//有無票信退補記錄
				sheet.addCell(new Label(colIndex++, rowIndex, CapDate.formatDate(borrower.getEChkDDate(), "yyyy-MM-dd"), cellFormat));//票信資料截止日
				sheet.addCell(new Label(colIndex++, rowIndex, CapDate.formatDate(borrower.getEChkQDate(), "yyyy-MM-dd"), cellFormat));//票信查詢日期
				sheet.addCell(new Label(colIndex++, rowIndex, yesNoMap.get(borrower.getIsFromOld()), cellFormat));//無法提供票信電子資料
				sheet.addCell(new Label(colIndex++, rowIndex, haveNoNaMap.get(borrower.getIsQdata1()), cellFormat));//婉卻紀錄
				sheet.addCell(new Label(colIndex++, rowIndex, haveNoNaMap.get(borrower.getIsQdata29()), cellFormat));//異常通報紀錄
				sheet.addCell(new Label(colIndex++, rowIndex, haveNoNaMap.get(borrower.getIsQdata6()), cellFormat));//主從債務人(E-LOAN查詢)
				sheet.addCell(new Label(colIndex++, rowIndex, haveNoNaMap.get(borrower.getIsQdata8()), cellFormat));//證券暨期貨違約交割紀錄
				sheet.addCell(new Label(colIndex++, rowIndex, haveNoNaMap.get(borrower.getIsQdata9()), cellFormat));//退票紀錄
				sheet.addCell(new Label(colIndex++, rowIndex, haveNoNaMap.get(borrower.getIsQdata10()), cellFormat));//拒絕往來紀錄
				sheet.addCell(new Label(colIndex++, rowIndex, haveNoNaMap.get(borrower.getIsQdata11()), cellFormat));//主債務逾期、催收、呆帳紀錄
				sheet.addCell(new Label(colIndex++, rowIndex, haveNoNaMap.get(borrower.getIsQdata13()), cellFormat));//信用卡強停紀錄
				sheet.addCell(new Label(colIndex++, rowIndex, haveNoNaMap.get(otherDataMap.get(custId+"chkItem2")), cellFormat));//消債條例信用註記、銀行公會債務協商註記及其他補充註記
				sheet.addCell(new Label(colIndex++, rowIndex, haveNoNaMap.get(borrower.getIsQdata7()), cellFormat));//黑名單(BTT 交易代號0015、功能別11)
				sheet.addCell(new Label(colIndex++, rowIndex, otherDataMap.get(custId+"c101s04w"), cellFormat));//司法院受監護/輔助宣告資料
				rowIndex++;
				colIndex = 1;
			}
		}
		
		return rowIndex;
	}
	
	@Override
	public void setTitleContent(WritableSheet sheet, Map<String, Integer> titleMap, Properties prop, int fromColIndex, int toColIndex, int fromRowIndex, int toRowIndex) throws WriteException{
		
		WritableFont font_Header = new WritableFont(WritableFont.createFont("標楷體"), 20);
		WritableCellFormat cellFormat = new WritableCellFormat(font_Header);
		{
			cellFormat.setWrap(true);
			cellFormat.setAlignment(Alignment.CENTRE);
			cellFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
			cellFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
			cellFormat.setLocked(false);
		}

		for(String title : titleMap.keySet()){
			this.mergeFieldWithAutoSize(sheet, fromColIndex, toColIndex, fromRowIndex, toRowIndex, prop.getProperty(title), cellFormat);
		}
	}
	
	@Override
	public void setHeader1Content(WritableSheet sheet, Properties prop, int colIndex, int rowIndex) throws WriteException{
		
		WritableFont font_Header = new WritableFont(WritableFont.createFont("標楷體"), 12);
		WritableCellFormat cellFormat = new WritableCellFormat(font_Header);
		{
			cellFormat.setWrap(true);
			cellFormat.setAlignment(Alignment.CENTRE);
			cellFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
			cellFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
		}
		WritableCellFormat formatHaveLeftBorder = new WritableCellFormat(font_Header);
		{
			formatHaveLeftBorder.setWrap(true);
			formatHaveLeftBorder.setAlignment(Alignment.LEFT);
			formatHaveLeftBorder.setVerticalAlignment(VerticalAlignment.CENTRE);
			formatHaveLeftBorder.setBorder(Border.LEFT, BorderLineStyle.THIN);
		}
		
		int toRowIndex = rowIndex+1;
		this.mergeFieldWithAutoSize(sheet, 0, 2, rowIndex, toRowIndex, "", formatHaveLeftBorder);//補一條左邊的線
		//聯微/票信/相關資料查詢
		this.mergeFieldWithAutoSize(sheet, colIndex, 5, rowIndex, toRowIndex, prop.getProperty("CLS180R57.ejcicAndEtchAndrelatedDataInquiry"), cellFormat);
		//本行資料庫
		this.mergeFieldWithAutoSize(sheet, 6, 8, rowIndex, toRowIndex, prop.getProperty("CLS180R57.insideBankDataBase"), cellFormat);
		//外來資料庫(聯徵、票交所、兆豐證…)
		this.mergeFieldWithAutoSize(sheet, 9, 17, rowIndex, toRowIndex, prop.getProperty("CLS180R57.outsideBankDataBase"), cellFormat);
	}
	
	@Override
	public void setHeader2Content(WritableSheet sheet, Map<String, Integer> headerMap, Properties prop, int colIndex, int rowIndex) throws WriteException{
		
		WritableFont font_Header = new WritableFont(WritableFont.createFont("標楷體"), 12);
		WritableCellFormat cellFormat = new WritableCellFormat(font_Header);
		{
			cellFormat.setWrap(true);
			cellFormat.setAlignment(Alignment.CENTRE);
			cellFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
			cellFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
		}
		WritableCellFormat formatHaveLeftBorder = new WritableCellFormat(font_Header);
		{
			formatHaveLeftBorder.setWrap(true);
			formatHaveLeftBorder.setAlignment(Alignment.LEFT);
			formatHaveLeftBorder.setVerticalAlignment(VerticalAlignment.CENTRE);
			formatHaveLeftBorder.setBorder(Border.LEFT, BorderLineStyle.THIN);
		}
		this.mergeFieldWithAutoSize(sheet, 0, 2, rowIndex, rowIndex+2, "", formatHaveLeftBorder);//補一條左邊的線

		for(String header : headerMap.keySet()){
			this.mergeFieldAndSetWidth(sheet, colIndex, colIndex++, rowIndex, rowIndex+2, prop.getProperty(header), cellFormat, headerMap.get(header));
		}
	}
	
	private void mergeFieldAndSetWidth(WritableSheet sheet, int fromColIndex, int toColIndex, int fromRowIndex, int toRowIndex, 
										String content, WritableCellFormat cellFormat, int width) throws RowsExceededException, WriteException{
		sheet.setColumnView(fromColIndex, width);
		sheet.mergeCells(fromColIndex, fromRowIndex, toColIndex, toRowIndex);
		sheet.addCell(new Label(fromColIndex, fromRowIndex, content, cellFormat));
	}
	
	private void mergeFieldWithAutoSize(WritableSheet sheet, int fromColIndex, int toColIndex, int fromRowIndex, int toRowIndex, 
										String content, WritableCellFormat cellFormat) throws RowsExceededException, WriteException{
		sheet.mergeCells(fromColIndex, fromRowIndex, toColIndex, toRowIndex);
		sheet.addCell(new Label(fromColIndex, fromRowIndex, content, cellFormat));
	}

	@Override
	public Map<String, Map<String, String>> getRelatedDebtorMap(String cntrno){
		
		Map<String, Map<String, String>> allPeopleMap = new HashMap<String, Map<String, String>>();
		L140M01A l140m01a = this.l140m01adao.findByCntrNoForNew(cntrno);
		
		if(l140m01a == null){
			return allPeopleMap;
		}
		
		List<L140S01A> guarantorList = this.clsService.findL140S01A(l140m01a);
		
		String branchNo = l140m01a.getOwnBrId();
		Map<String, String> borrowerMap = new HashMap<String, String>();
		borrowerMap.put("BRANCH_NO", branchNo);
		borrowerMap.put("DUP_NO", l140m01a.getDupNo());
		borrowerMap.put("CUST_POS", "B");
		allPeopleMap.put(l140m01a.getCustId(), borrowerMap);
		
		String[] codeArray = new String[]{"G", "N", "C"};
		for(L140S01A guarantor : guarantorList){
			
			if(Arrays.asList(codeArray).contains(guarantor.getCustPos())){
				Map<String, String> m = new HashMap<String, String>();
				m.put("BRANCH_NO", branchNo);
				m.put("DUP_NO", guarantor.getDupNo());
				m.put("CUST_POS", guarantor.getCustPos());
				allPeopleMap.put(guarantor.getCustId(), m);
			}
		}
		
		return allPeopleMap;
	}
	
	@Override
	public int setBodyContent2(WritableSheet sheet, int colIndex, int rowIndex, Properties prop) throws RowsExceededException, WriteException{
		
		WritableFont font = new WritableFont(WritableFont.createFont("標楷體"), 14);
		WritableCellFormat formatNoBorder = new WritableCellFormat(font);
		{
			formatNoBorder.setWrap(true);
			formatNoBorder.setAlignment(Alignment.LEFT);
			formatNoBorder.setVerticalAlignment(VerticalAlignment.CENTRE);
			formatNoBorder.setLocked(false);
		}
		
		WritableCellFormat formatHaveBorder = new WritableCellFormat(font);
		{
			formatHaveBorder.setWrap(true);
			formatHaveBorder.setAlignment(Alignment.LEFT);
			formatHaveBorder.setVerticalAlignment(VerticalAlignment.CENTRE);
			formatHaveBorder.setBorder(Border.ALL, BorderLineStyle.THIN);
			formatHaveBorder.setLocked(false);
		}
		
		WritableCellFormat formatLeftTopBorder = new WritableCellFormat(font);
		{
			formatLeftTopBorder.setWrap(true);
			formatLeftTopBorder.setAlignment(Alignment.LEFT);
			formatLeftTopBorder.setVerticalAlignment(VerticalAlignment.TOP);
			formatLeftTopBorder.setBorder(Border.ALL, BorderLineStyle.THIN);
			formatLeftTopBorder.setLocked(false);
		}
		
		WritableFont font12 = new WritableFont(WritableFont.createFont("標楷體"), 12);
		WritableCellFormat formatNoBorderFont12 = new WritableCellFormat(font12);
		{
			formatNoBorderFont12.setWrap(true);
			formatNoBorderFont12.setAlignment(Alignment.LEFT);
			formatNoBorderFont12.setVerticalAlignment(VerticalAlignment.TOP);
			formatNoBorderFont12.setLocked(false);
		}
		
		WritableCellFormat formatNoBorderFont12Locked = new WritableCellFormat(font12);
		{
			formatNoBorder.setWrap(true);
			formatNoBorder.setAlignment(Alignment.LEFT);
			formatNoBorder.setVerticalAlignment(VerticalAlignment.CENTRE);
		}
		
		int toRowIndex = rowIndex + 1;
		// J-113-0276 因中期循環規範修訂，調整中期循環檢視表 -start-
		this.mergeFieldWithAutoSize(sheet, colIndex, 10, rowIndex, toRowIndex++, prop.getProperty("CLS180R57.content5"), formatNoBorder);
		rowIndex = toRowIndex;
		toRowIndex = rowIndex + 1;
		this.mergeFieldWithAutoSize(sheet, colIndex, 10, rowIndex, toRowIndex++, prop.getProperty("CLS180R57.content6"), formatNoBorder);
		rowIndex = toRowIndex;
		toRowIndex = rowIndex + 1;
		this.mergeFieldWithAutoSize(sheet, colIndex, 8, rowIndex, toRowIndex++, prop.getProperty("CLS180R57.content7"), formatNoBorder);
		rowIndex = toRowIndex;
		toRowIndex = rowIndex + 7;
		this.mergeFieldWithAutoSize(sheet, colIndex, 6, rowIndex, toRowIndex++, prop.getProperty("CLS180R57.description"), formatLeftTopBorder);
		rowIndex = toRowIndex+1;
		toRowIndex = rowIndex;
		// J-113-0276 因中期循環規範修訂，調整中期循環檢視表 -end-
		this.mergeFieldWithAutoSize(sheet, colIndex, 10, rowIndex, toRowIndex++, prop.getProperty("CLS180R57.content1"), formatNoBorder);
		rowIndex = toRowIndex;
		toRowIndex = rowIndex + 1;
		this.mergeFieldWithAutoSize(sheet, colIndex, 8, rowIndex, toRowIndex++, prop.getProperty("CLS180R57.content2"), formatNoBorder);
		rowIndex = toRowIndex;
		toRowIndex = rowIndex + 7;
		this.mergeFieldWithAutoSize(sheet, colIndex, 6, rowIndex, toRowIndex++, "", formatLeftTopBorder);
		rowIndex = toRowIndex;
		toRowIndex = rowIndex + 7;
		this.mergeFieldWithAutoSize(sheet, colIndex, 6, rowIndex, toRowIndex++, prop.getProperty("CLS180R57.content3"), formatNoBorderFont12);
		rowIndex = toRowIndex + 1;
		toRowIndex = rowIndex + 1;
		this.mergeFieldWithAutoSize(sheet, colIndex, 6, rowIndex, toRowIndex++, prop.getProperty("CLS180R57.content4"), formatNoBorder);
		rowIndex = toRowIndex;
		toRowIndex = rowIndex + 5;
		this.mergeFieldWithAutoSize(sheet, colIndex, 6, rowIndex, toRowIndex++, prop.getProperty("CLS180R57.description"), formatLeftTopBorder);
		rowIndex = toRowIndex+1;
		toRowIndex = rowIndex;
		int toColIndex = colIndex + 1;
		this.mergeFieldWithAutoSize(sheet, colIndex, toColIndex++, rowIndex, toRowIndex, prop.getProperty("CLS180R57.clerk"), formatNoBorder);//經辦
		colIndex = toColIndex;
		toColIndex = toColIndex + 1;
		this.mergeFieldWithAutoSize(sheet, colIndex, toColIndex++, rowIndex, toRowIndex, prop.getProperty("CLS180R57.sectionChief"), formatNoBorder);//科長/襄理
		colIndex = toColIndex;
		toColIndex = toColIndex + 1;
		this.mergeFieldWithAutoSize(sheet, colIndex, toColIndex++, rowIndex, toRowIndex, prop.getProperty("CLS180R57.deputyManager"), formatNoBorder);//副理
		colIndex = toColIndex;
		toColIndex = toColIndex + 1;
		this.mergeFieldWithAutoSize(sheet, colIndex, toColIndex++, rowIndex, toRowIndex, prop.getProperty("CLS180R57.sectionSupervisor"), formatNoBorder);//單位主管
		// J-113-0276 因中期循環規範修訂，調整中期循環檢視表 新增版本 -start-
		rowIndex = toRowIndex+2;
		toRowIndex = rowIndex;
		colIndex = 0;
		this.mergeFieldWithAutoSize(sheet, colIndex, 1, rowIndex, toRowIndex++, prop.getProperty("CLS180R57.version"), formatNoBorderFont12Locked);
		// J-113-0276 因中期循環規範修訂，調整中期循環檢視表 新增版本 -end-
		rowIndex++;
		colIndex = 1;
		return rowIndex;
	}
}
