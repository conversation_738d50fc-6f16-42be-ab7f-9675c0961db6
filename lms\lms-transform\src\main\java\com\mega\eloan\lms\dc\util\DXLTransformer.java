package com.mega.eloan.lms.dc.util;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;

import javax.xml.transform.Result;
import javax.xml.transform.Source;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.stream.StreamResult;
import javax.xml.transform.stream.StreamSource;

public class DXLTransformer {

	// onLineDC Only
	public static final String OUTPUT_ENCODING = TextDefine.ENCODING_UTF8;

	private String xmlin = null;
	private String xslin = null;
	private Source xslSource = null;
	private Source xmlSource = null;
	private InputStream xmlInputStream = null;
	private InputStream xslInputStream = null;
	private OutputStream outStream = null;

	public DXLTransformer() {

	}

	public DXLTransformer(String xmlin, String xslin) throws Exception {
		this.setXmlin(xmlin);
		this.setXslin(xslin);
	}

	public void doTransform() throws Exception {
		Result result = new StreamResult(this.getOutStream());
		// System.out.println("a");
		// Create a Transformer factory
		// TransformerFactory tFactory = new TransformerFactory();
		TransformerFactory tFactory = TransformerFactory.newInstance();
		// System.out.println("a");

		// Generate Transformer from factory passing XSL source
		Transformer transformer = tFactory.newTransformer(this.getXslSource());
		// System.out.println("a");

		// Make it happen
		transformer.setOutputProperty("encoding", OUTPUT_ENCODING);
		transformer.transform(this.getXmlSource(), result);
		// System.out.println("a");
	}

	/**
	 * Returns the outStream.
	 * 
	 * @return OutputStream
	 */
	public OutputStream getOutStream() {
		return outStream;
	}

	/**
	 * Returns the xmlin.
	 * 
	 * @return String
	 */
	public String getXmlin() {
		return xmlin;
	}

	/**
	 * Returns the xmlSource.
	 * 
	 * @return Source
	 */
	public Source getXmlSource() {
		return xmlSource;
	}

	/**
	 * Returns the xslin.
	 * 
	 * @return String
	 */
	public String getXslin() {
		return xslin;
	}

	/**
	 * Returns the xslSource.
	 * 
	 * @return Source
	 */
	public Source getXslSource() {
		return xslSource;
	}

	/**
	 * Sets the outStream.
	 * 
	 * @param outStream
	 *            The outStream to set
	 */
	public void setOutStream(OutputStream outStream) {
		this.outStream = outStream;
	}

	/**
	 * Sets the xmlin.
	 * 
	 * @param xmlin
	 *            The xmlin to set
	 */
	public void setXmlin(String xmlin) {
		this.xmlin = xmlin;
		this.setXmlSource(new StreamSource(xmlin));

	}

	/**
	 * Sets the xmlSource.
	 * 
	 * @param xmlSource
	 *            The xmlSource to set
	 */
	public void setXmlSource(Source xmlSource) {
		this.xmlSource = xmlSource;
	}

	/**
	 * Sets the xslin.
	 * 
	 * @param xslin
	 *            The xslin to set
	 */
	public void setXslin(String xslin) {
		this.xslin = xslin;
		this.setXslSource(new StreamSource(xslin));
	}

	/**
	 * Sets the xslSource.
	 * 
	 * @param xslSource
	 *            The xslSource to set
	 */
	public void setXslSource(Source xslSource) {
		this.xslSource = xslSource;
	}

	/**
	 * Returns the xslInputStream.
	 * 
	 * @return InputStream
	 */
	public InputStream getXslInputStream() {
		return xslInputStream;
	}

	/**
	 * Sets the xslInputStream.
	 * 
	 * @param xslInputStream
	 *            The xslInputStream to set
	 */
	public void setXslInputStream(InputStream xslInputStream) {
		this.xslInputStream = xslInputStream;
		this.setXslSource(new StreamSource(xslInputStream));
	}

	/**
	 * Returns the xmlInputStream.
	 * 
	 * @return InputStream
	 */
	public InputStream getXmlInputStream() {
		return xmlInputStream;
	}

	/**
	 * Sets the xmlInputStream.
	 * 
	 * @param xmlInputStream
	 *            The xmlInputStream to set
	 */
	public void setXmlInputStream(InputStream xmlInputStream) {
		this.xmlInputStream = xmlInputStream;
		this.setXmlSource(new StreamSource(xmlInputStream));
	}

	/**
	 * Sets the output.
	 * 
	 * @param output
	 *            The output to set
	 */
	public void setOutput(String output) {
		try {
			FileOutputStream fos = new FileOutputStream(new File(output));
			this.setOutStream(fos);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void setOutput(ByteArrayOutputStream bao) {
		try {
			// FileOutputStream fos = new FileOutputStream(new File(output));
			this.setOutStream(bao);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

}
