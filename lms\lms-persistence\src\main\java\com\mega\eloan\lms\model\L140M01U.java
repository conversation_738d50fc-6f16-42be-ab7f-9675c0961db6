/* 
 * L140M01U.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 共同行銷資訊檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L140M01U", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L140M01U extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 額度序號 **/
	@Size(max=12)
	@Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)")
	private String cntrNo;

	/** 
	 * 類型<p/>
	 * A產險 B票券 C證券<br/>
	 *  MIS.SYNBANK (英文大小寫有差)<br/>
	 *  A(<br/>
	 *  B( FINKIND='06' FINCODE='060' BRNNO<>''<br/>
	 *  C( FINKIND='1A' FINCODE='700' BRNNO<>''
	 */
	@Size(max=1)
	@Column(name="TYPE", length=1, columnDefinition="VARCHAR(1)")
	private String type;

	/** 第一層-是否 **/
	@Size(max=1)
	@Column(name="CHECKYN", length=1, columnDefinition="VARCHAR(1)")
	private String checkYN;

	/** 
	 * 第二層-結果<p/>
	 * A1已洽兆豐產險<br/>
	 *  A2未洽兆豐產險<br/>
	 *  A3已於擔保品檔鍵入投保狀況<br/>
	 *  B1已洽財務處或兆豐票券<br/>
	 *  B2未洽財務處或兆豐票券<br/>
	 *  B3未與財務處或兆豐票券辦理簽證承銷<br/>
	 *  C1已洽兆豐證券<br/>
	 *  C2未洽兆豐證券<br/>
	 *  C3未與財兆豐證券辦理簽證承銷
	 */
	@Size(max=2)
	@Column(name="RESULT", length=2, columnDefinition="VARCHAR(2)")
	private String result;
	
	/** 
	 * 窗口使用<p/>
	 *  Ex. 兆豐票券金融公司高雄分公司(下拉FINKIND='06' FINCODE='060' BRNNO=’002’) 	=> 002
	 */
	@Size(max=5)
	@Column(name="CONTACT", length=5, columnDefinition="VARCHAR(5)")
	private String contact;

	/** 
	 * 說明<p/>
	 * 若為窗口使用(A1.B1.C1) <br/>
	 * 若為原因(textbox) 使用時機<br/>
	 * type=’B’ && checkYN=’N’.B3.  type=’C’ && checkYN=’N’.C3.<br/>
	 */
	@Size(max=300)
	@Column(name="MEMO", length=300, columnDefinition="VARCHAR(300)")
	private String memo;

	/** 建立人員 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立時間 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 更新人員 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 更新時間 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得額度序號 **/
	public String getCntrNo() {
		return this.cntrNo;
	}
	/** 設定額度序號 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/** 
	 * 取得類型<p/>
	 * A產險 B票券 C證券<br/>
	 *  MIS.SYNBANK (英文大小寫有差)<br/>
	 *  A(<br/>
	 *  B( FINKIND='06' FINCODE='060' BRNNO<>''<br/>
	 *  C( FINKIND='1A' FINCODE='700' BRNNO<>''
	 */
	public String getType() {
		return this.type;
	}
	/**
	 *  設定類型<p/>
	 *  A產險 B票券 C證券<br/>
	 *  MIS.SYNBANK (英文大小寫有差)<br/>
	 *  A(<br/>
	 *  B( FINKIND='06' FINCODE='060' BRNNO<>''<br/>
	 *  C( FINKIND='1A' FINCODE='700' BRNNO<>''
	 **/
	public void setType(String value) {
		this.type = value;
	}

	/** 取得第一層-是否 **/
	public String getCheckYN() {
		return this.checkYN;
	}
	/** 設定第一層-是否 **/
	public void setCheckYN(String value) {
		this.checkYN = value;
	}

	/** 
	 * 取得第二層-結果<p/>
	 * A1已洽兆豐產險<br/>
	 *  A2未洽兆豐產險<br/>
	 *  A3已於擔保品檔鍵入投保狀況<br/>
	 *  B1已洽財務處或兆豐票券<br/>
	 *  B2未洽財務處或兆豐票券<br/>
	 *  B3未與財務處或兆豐票券辦理簽證承銷<br/>
	 *  C1已洽兆豐證券<br/>
	 *  C2未洽兆豐證券<br/>
	 *  C3未與財兆豐證券辦理簽證承銷
	 */
	public String getResult() {
		return this.result;
	}
	/**
	 *  設定第二層-結果<p/>
	 *  A1已洽兆豐產險<br/>
	 *  A2未洽兆豐產險<br/>
	 *  A3已於擔保品檔鍵入投保狀況<br/>
	 *  B1已洽財務處或兆豐票券<br/>
	 *  B2未洽財務處或兆豐票券<br/>
	 *  B3未與財務處或兆豐票券辦理簽證承銷<br/>
	 *  C1已洽兆豐證券<br/>
	 *  C2未洽兆豐證券<br/>
	 *  C3未與財兆豐證券辦理簽證承銷
	 **/
	public void setResult(String value) {
		this.result = value;
	}
	
	/** 
	 * 取得窗口<p/>
	 */
	public String getContact() {
		return this.contact;
	}
	/**
	 *  設定窗口<p/>
	 **/
	public void setContact(String value) {
		this.contact = value;
	}

	/** 
	 * 取得說明<p/>
	 * 若為窗口使用(A1.B1.C1) <br/>
	 *  若為原因(textbox) 使用時機<br/>
	 *  type=’B’ && checkYN=’N’.B3.<br/>
	 *  type=’C’ && checkYN=’N’.C3.
	 */
	public String getMemo() {
		return this.memo;
	}
	/**
	 *  設定說明<p/>
	 *  若為窗口使用(A1.B1.C1) <br/>
	 *  若為原因(textbox) 使用時機<br/>
	 *  type=’B’ && checkYN=’N’.B3.<br/>
	 *  type=’C’ && checkYN=’N’.C3.
	 **/
	public void setMemo(String value) {
		this.memo = value;
	}

	/** 取得建立人員 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立時間 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立時間 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得更新人員 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定更新人員 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得更新時間 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定更新時間 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
