package com.mega.eloan.lms.crs.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;

import tw.com.jcs.common.Util;

import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.model.C241M01A;
import com.mega.eloan.lms.model.L170M01A;

public class LMS2411S02Panel extends Panel {
	
	private C241M01A meta;
	
	private static final long serialVersionUID = 1L;

	public LMS2411S02Panel(String id, C241M01A meta) {
		super(id);
		this.meta = meta;
	}
	
	public LMS2411S02Panel(String id, boolean updatePanelName, C241M01A meta) {
		super(id, updatePanelName);
		this.meta = meta;
	}
	
	
	
	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);
		if(true){
			model.addAttribute("show21ROW1_P", CrsUtil.isCaseP(meta));
			model.addAttribute("show21ROW1_NG", !CrsUtil.isCaseP(meta));
			
			model.addAttribute("showGrpInfo", CrsUtil.isCaseG_Parent(meta) );
		}
		
		//fieldset覆審訊息
		if(true){
			if(CrsUtil.isCaseP(meta)){
				model.addAttribute("showUnkdInfo", false);
			}else{
				model.addAttribute("showUnkdInfo", Util.isNotEmpty(meta.getMdFlag()));			
			}
			
			model.addAttribute("show22ROW1_P", CrsUtil.isCaseP(meta));
			model.addAttribute("show22ROW1_NG", !CrsUtil.isCaseP(meta));
			model.addAttribute("show22ROW4_P", CrsUtil.isCaseP(meta));
			model.addAttribute("show22ROW4_NG", !CrsUtil.isCaseP(meta));	
			
			if(true){				
				boolean is_R11 = CrsUtil.docKindN_since_R11(meta);
				model.addAttribute("docFmt_show_1", is_R11);
				model.addAttribute("docFmt_show_2", is_R11);
				model.addAttribute("docFmt_hidden", !is_R11);
			}			
			model.addAttribute("show_docKindS_pa_period", CrsUtil.isCaseS(meta) );
			model.addAttribute("showC241M01F", CrsUtil.isCaseS(meta) );
		}
		
		//fieldset帳務
		if(true){
			model.addAttribute("showLNPanelN", CrsUtil.isCaseN(meta)|| CrsUtil.isCaseG___N_Detail(meta) || CrsUtil.isCaseS(meta) || CrsUtil.isCaseH(meta)|| CrsUtil.isCaseG___H_Detail(meta) );
			model.addAttribute("showLNPanelP", CrsUtil.isCaseP(meta) );
			model.addAttribute("showLNPanelG", CrsUtil.isCaseG_Parent(meta) );	
		}
	}
}
