$.extend($cap._fn, {
    iGrid: {
        rule: "[role=presentation].ui-common-table",
        fn: {
            /**
             * 移至grid 指定位置
             * @param {string} selector
             */
            iGridScrollTo: function(selector){
                var $this = this.closest(".ui-jqgrid-bdiv");
                if ($this.scrollTo) {
                    $this.scrollTo(selector);
                }
                return this;
            },
            
            /**
             * 自動設定寬度
             * @param {integer} maxWidth 寬度
             * @param {boolean} fitColumn 是否自動調整欄寬
             */
            iGridFitSize: function(maxWidth, fitColumn){
                var self = this.closest(".ui-jqgrid"), $this = $(this);
                if (!$this.is(":visible")) 
                    return;
                if (typeof maxWidth == 'boolean') {
                    maxWidth = 0;
                    // fitColumn = maxWidth;
                }
                //fitColumn = !!fitColumn;
                $this.each(function(){
                    if (this.p.autowidth) {
                        maxWidth = maxWidth || self.parent().width();
                        maxWidth = (maxWidth > 960) ? 960 : maxWidth
                    }
                    else {
                        maxWidth = maxWidth || this.p.freezeWidth;
                    }
                    $this.setGridWidth((maxWidth * 98) / 100, fitColumn == undefined ? this.p.forceFit : fitColumn);// ? this.p.forceFit : !(self.width() > maxWidth)); //);
                });
            },
            
            /**
             * Grid hide (extend jQuery hide)
             * @param {integer} speed
             * @param {function} callback
             */
            iGridHide: function(speed, callback){
                $("#gbox_" + DOMPurify.sanitize($(this).attr("id"))).hide(speed, callback);
                return this;
            },
            /**
             * Gird show (extend jQuery show)
             * @param {integer} speed
             * @param {function} callback
             */
            iGridShow: function(speed, callback){
                $("#gbox_" + DOMPurify.sanitize($(this).attr("id"))).show(speed, callback);
                return this;
            },
            
            /**
             * 將GridData轉換為Array<JSON>值
             * @param {boolean} stringify
             */
            iGridSerialize: function(stringify){
                var data = [];
                if ($(this).attr('role') == 'presentation') {
                    var tGrid = $(this);
                    tGrid.find('tr[id]').each(function(){
                        data.push($.extend(tGrid.getRowData($(this).attr('id')), {
                            rowId: $(this).attr('id')
                        }));
                    });
                }
                return stringify ? JSON.stringify(data) : data;
            },
            
            /**
             * 將TreeGird全部關閉
             */
            iGridCollapse: function(){
                var $this = $(this);
                $.each($this[0].rows, function(i, e){
                    if ($(e).find('td[aria-describedby=gridview_isLeaf]').attr('title') != 'true') {
                    
                        $this.jqGrid('collapseRow', e);
                        $this.jqGrid('collapseNode', e);
                    }
                });
                $this = null;
            },
            
            /**
             * 將TreeGrid全部打開
             */
            iGridExpand: function(){
                var $this = $(this);
                $.each($this[0].rows, function(i, e){
                    if ($(e).find('td[aria-describedby=gridview_isLeaf]').attr('title') != 'true') {
                        $this.jqGrid('expandRow', e);
                        $this.jqGrid('expandNode', e);
                    }
                });
                $this = null;
            },
            
            /**
             * add data to grid by Array[json,json,....] or Array[array,array,....]
             * @param {Object} datas
             */
            addGridData: function(datas){
                var $this = $(this), ids = $this.getGridParam("colIds"), rowData, colKey = this[0].colKey;
                if (datas instanceof Array) {
                    for (var data in datas) {
                        var _data = (datas[data] instanceof Array) ? _convertJson(datas[data]) : datas[data], rowId = _data[$this[0].colKey];
                        var _new = !$this[0].rows.namedItem(rowId);
                        $this[_new ? "addRowData" : "setRowData"](rowId, _data);
                    }
                }
                return this;
                function _convertJson(d){
                    var td = {}, i = 0;
                    for (var i in ids) {
                        td[ids[i]] = d[i++];
                    }
                    return td;
                }
            },
            
            /**
             * hide header bar
             */
            hideHeader: function(){
                $(this).closest(".ui-jqgrid-bdiv").siblings(".ui-jqgrid-hdiv").hide();
            },
            
            /**
             * show header bar
             */
            showHeader: function(){
                $(this).closest(".ui-jqgrid-bdiv").siblings(".ui-jqgrid-hdiv").show();
            },
            /**
             * 取得目前所選取資料行資料
             */
            getSelRowDatas: function(){
                var tGrid = $(this);
                if (tGrid.jqGrid('getGridParam', 'multiselect')) {
                    var sels = tGrid.jqGrid('getGridParam', 'selarrrow');
                    var res = [];
                    for (var i = 0; i < sels.length; i++) {
                        res.push(tGrid.getRowData(sels[i]));
                    }
                    return res.length ? res : undefined;
                }
                else {
                    var selrow = tGrid.jqGrid('getGridParam', 'selrow');
                    return selrow ? tGrid.getRowData(selrow) : undefined;
                }
            },
			/**
			* 取得資料表之選擇列(單筆) add by Fantasy 2012/12/27
			*/
			getSingleData: function() {
				var tGrid = $(this);
				var row = tGrid.getGridParam('selrow');
				if (row) {
					return tGrid.getRowData(row);
				} else {
					MegaApi.showErrorMessage(i18n.def["confirmTitle"], i18n.def['grid.selrow']);
					return undefined;
				}
			},
			/**
         	 * 取得資料表之選擇列(多筆:回傳陣列) add by Fantasy 2012/12/27
         	 */
         	getSelectData : function(key){
         		var tGrid = $(this);
         		var datas = [];
         		var rows = tGrid.getGridParam('selarrrow');
       			for (var o in rows) {
       				  datas.push(tGrid.getRowData(rows[o]));
     				}
         		if (datas.length > 0){
         			if (key){
         				var result = [];
         				for (var o in datas){
         					var data = datas[o];
         					result.push(data[key]);
         				}
         				return result;
         			}else{
         				return datas;
         			}
         		}else{
         			MegaApi.showErrorMessage(i18n.def['confirmTitle'],i18n.def['action_005']);
         			return undefined;
         		}
         	},
			reload: function(data) {
				var tGrid = $(this);
				if (data) {
					tGrid.jqGrid("setGridParam", {
						postData: data,
						page: 1,
						search: true
					}).trigger("reloadGrid");
				} else {
					tGrid.trigger('reloadGrid');
				}
			}
        }
    }
});


require(['jqgrid', 'jqueryui'], function() {
// autoBuildGird index
$.ui.iGridIndex = $.ui.iGridIndex || 0;
jQuery.fn.extend({
    /**
     * autoBuildGrid by iqGrid
     * @param {JSON} settings
     */
    iGrid: function(settings){
        var obj = $(this);
        var gridHtml = '<table id="{id}" class="scroll" cellpadding="0" cellspacing="0"></table>';
        var pageHtml = '<div id="{id}" class="scroll" style="text-align:center;"></div>';
        $.ui.iGridIndex++;
        var gridId = settings.id || obj.attr("id") ||
        ("iGrid" + $.ui.iGridIndex);
        var pagerId = settings.pagerId ||
        ('iGridPage' + $.ui.iGridIndex);
        var sortBy = settings.sortBy || settings.handler && 'all' || 'page';
        var colIds = [];
        
        var _key = undefined, _colNames = (function(array){
            var newColNames = [];
            for (var col in array) {
                newColNames.push(array[col].colHeader || array[col].name);
                colIds.push(array[col].name);
                !_key && (_key = (array[col].key && array[col].name));
            }
            return newColNames;
        })(settings.colModel || []);
        var colNames = settings.colNames || _colNames;
        var needPager = settings.needPager === false ? false : true;
        settings = $.extend({
            ajaxGridOptions: { // ajax option
                global: false
            },
            datatype: (!settings.localFirst && (settings.url || settings.handler)) ? 'json' : 'local',
            forceFit: true,
            url: (!settings.localFirst && settings.handler) ? new newAjaxUrl(settings.handler) : '',
            // scroll: true,
            pager: pagerId,
            mtype: 'POST',
            prmNames:{page:"gridPage"},
            //needPager: true,
            rowNum: (settings.needPager == false) ? 1000 : Properties.Grid.rowNum,
            rowList: Properties.Grid.rowList,
            height: 140,
            multiselect: false,
            emptyRecordRow: false,
            hideMultiselect: false,
            hideHeader: false,
            viewrecords: true,
            pgbuttons: needPager,
            pginput: needPager,
            loadonce: false,
            autowidth: true,
            localFirst: false,
            gridview: true, // 設定成true以快速大量資料集之載入
            freezeWidth: settings.width, //設定width 最大寬度
            priorityAction: '', // 排序formAction name (有設定則出現 上下圖示)
            divWidth: settings.divWidth||$("#leftFrame").length && 780 || 0,
            //ExpandColumn: 'colunmid',
            onSortCol: function(index){
                //                $("#" + gridId).setGridParam({
                //                    datatype: 'page' === sortBy ? 'local' : 'json',
                //                    page: 1
                //                });
              this.p.sortorder.indexOf( ',' ) > -1?this.p.sortorder = 'asc':"";
                $.extend(this.p.postData, {
                    isGroup: (this.p.ExpandColumn == index)
                });
                
            },
            onPaging: function(parm){
                //  $("#" + gridId).setGridParam({
                //      datatype: 'json'
                //  });
                
                if ('records' == parm) {
                    $("#" + gridId).setGridParam({
                        page: 1
                    });
                }
            }
        }, settings, {
            postData: $.extend({
                priority: [],
                formAction: settings.action || '',
                _columnParam: JSON.stringify(settings.colModel, null),
                _pa: settings.handler
            }, settings.postData || {})
        }, {
            sortname: (settings.sortname && settings.sortname.indexOf( '|' ) > -1) ? settings.sortname.replaceAll('|', ',') : settings.sortname,
            sortorder: (settings.sortorder && settings.sortorder.indexOf( '|' ) > -1) ? settings.sortorder.replaceAll('|', ',') : settings.sortorder,
            colNames: colNames
        });
        var maxWidth;
        if(settings.divWidth){
          obj.css("width",settings.divWidth);
          maxWidth = settings.divWidth;
        }else{
          maxWidth = obj.parent().width();
        }
        var newgrid = null;
        if (settings.priorityAction) {
            var _loadComplete = settings.loadComplete;
            settings.loadComplete = function(){
                _loadComplete && _loadComplete.apply(this, arguments);
                newgrid && newgrid.jqGrid('setGridParam', {
                    postData: {
                        formAction: settings.action
                    }
                });
                $('#' + pagerId).find(".ui-icon-check").closest("td").remove();
            };
        }
        
        //保留原始已存在loadComplete不覆寫, 初始化後將id為norecs的tr加上"ui-state-disabled" 並隱藏
        var oriLoadComplete = settings.loadComplete;
        settings.loadComplete = function() {
          oriLoadComplete && oriLoadComplete.apply(this, arguments);
          if(settings.localFirst){
            $(this).find('#norecs').remove();            
          } else {
            $(this).find('#norecs').addClass('ui-state-disabled hide');            
          }
        };

        newgrid = obj.append($(gridHtml.replace("{id}", gridId))).append(pageHtml.replace("{id}", pagerId)).find("#" + gridId).jqGrid($.extend(settings, {}));
        settings.hideMultiselect && newgrid.hideCol('cb');
        newgrid.navGrid('#' + pagerId, {
            add: false,
            edit: false,
            del: false,
            search: false,
            refreshtitle: i18n.def["grid.refresh"]
        });
        
        var pager = $('#' + pagerId);
        if (settings.priorityAction) {
            // add up btn
            newgrid.jqGrid('navButtonAdd', '#' + pagerId, {
                caption: "",
                buttonicon: "ui-icon-arrowthick-1-n",
                onClickButton: function(){
                    changeRow(true);
                }
            });
            
            // add down btn
            newgrid.jqGrid('navButtonAdd', '#' + pagerId, {
                caption: "",
                buttonicon: "ui-icon-arrowthick-1-s",
                onClickButton: function(){
                    changeRow(false);
                }
            });
        }
        
        
        function changeRow(prev){
            var gsr = newgrid.jqGrid('getGridParam', 'selrow');
            if (gsr) {
                var tr = newgrid.find("tr[id=" + gsr + "]"), target = tr[prev ? "prev" : "next"](".jqgrow");
                if (target.length) {
                    var cAction = prev ? "insertBefore" : "insertAfter", hasAlt = target.is(".altClass");
                    tr[hasAlt ? "addClass" : "removeClass"]("altClass")[cAction](target[hasAlt ? "removeClass" : "addClass"]("altClass"));
                    addPriorityBtn();
                }
            }
            else {
                API.showMessage(i18n.def["grid.selrow"]);
            }
        }
        
        function addPriorityBtn(){
            if (!pager.find(".ui-icon-check").length) {
                newgrid.jqGrid('navButtonAdd', '#' + pagerId, {
                    caption: i18n.def['grid.check'],
                    buttonicon: "ui-icon-check",
                    onClickButton: function(){
                        newgrid.jqGrid('setGridParam', {
                            postData: {
                                'priority': newgrid.getDataIDs(),
                                formAction: settings.priorityAction
                            }
                        });
                        newgrid.trigger("reloadGrid");
                    }
                });
            }
        }
                
        //增加 Grid 功能
        newgrid = $.extend(newgrid, $cap._fn.iGrid.fn);
        newgrid.each(function(){
            this.p.id = gridId;
            this.p.parent = parent;
            this.p.colIds = colIds;
            this.p.$_this = newgrid;
            this.colKey = this.p.colKey = _key;
        }).iGridFitSize();
        
        settings.localFirst &&
        newgrid.setGridParam({
            datatype: (settings.url || settings.handler) ? 'json' : 'local',
            url: settings.handler ? new newAjaxUrl(settings.handler) : ''
        });
        settings.hideHeader && newgrid.hideHeader();
        obj.removeAttr("id");
        return newgrid;
    }
});

  $.extend($.jgrid.defaults, {
    altRows: true, /*是否允許行交替變色*/
    altclass: 'altClass', /*交替色的css*/
    recordtext: i18n.def['grid.recordtext'],
    emptyrecords: i18n.def['grid.emptyrecords'],
    pgtext: i18n.def['grid.pgtext'],
    loadtext: i18n.def['grid.loadtext']
  });
});
