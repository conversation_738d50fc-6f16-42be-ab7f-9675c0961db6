/* 
 * LMS1601ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lns.service.impl;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.TimeZone;
import java.util.TreeMap;
import java.util.regex.Pattern;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.constants.SysParamConstants;
import com.mega.eloan.common.dao.DocFileDao;
import com.mega.eloan.common.enums.DocAuthTypeEnum;
import com.mega.eloan.common.enums.DocLogEnum;
import com.mega.eloan.common.enums.TypCdEnum;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.gwclient.IVRGwClient;
import com.mega.eloan.common.gwclient.IVRGwReqMessage;
import com.mega.eloan.common.gwclient.OBSMqGwClient;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.common.service.RPAService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.LrsUtil;
import com.mega.eloan.lms.base.common.MISRows;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.AMLRelateService;
import com.mega.eloan.lms.base.service.CLS8011Service;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.FlowNameService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.dao.C120S01ADao;
import com.mega.eloan.lms.dao.C120S01EDao;
import com.mega.eloan.lms.dao.C160M01ADao;
import com.mega.eloan.lms.dao.L120A01ADao;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dao.L120M01BDao;
import com.mega.eloan.lms.dao.L120M01CDao;
import com.mega.eloan.lms.dao.L120M01FDao;
import com.mega.eloan.lms.dao.L120S01ADao;
import com.mega.eloan.lms.dao.L120S01BDao;
import com.mega.eloan.lms.dao.L120S01CDao;
import com.mega.eloan.lms.dao.L120S01DDao;
import com.mega.eloan.lms.dao.L120S04ADao;
import com.mega.eloan.lms.dao.L120S04BDao;
import com.mega.eloan.lms.dao.L120S04CDao;
import com.mega.eloan.lms.dao.L120S21BDao;
import com.mega.eloan.lms.dao.L120S21CDao;
import com.mega.eloan.lms.dao.L140M01ADao;
import com.mega.eloan.lms.dao.L140M01A_BFDao;
import com.mega.eloan.lms.dao.L140M01C_BFDao;
import com.mega.eloan.lms.dao.L140M01D_BFDao;
import com.mega.eloan.lms.dao.L140M01E_AFDao;
import com.mega.eloan.lms.dao.L140M01JDao;
import com.mega.eloan.lms.dao.L140M01MDao;
import com.mega.eloan.lms.dao.L140M01NDao;
import com.mega.eloan.lms.dao.L140M01ODao;
import com.mega.eloan.lms.dao.L140S12ADao;
import com.mega.eloan.lms.dao.L160A01ADao;
import com.mega.eloan.lms.dao.L160M01ADao;
import com.mega.eloan.lms.dao.L160M01BDao;
import com.mega.eloan.lms.dao.L160M01CDao;
import com.mega.eloan.lms.dao.L160M01DDao;
import com.mega.eloan.lms.dao.L161S01ADao;
import com.mega.eloan.lms.dao.L161S01BDao;
import com.mega.eloan.lms.dao.L161S01CDao;
import com.mega.eloan.lms.dao.L161S01DDao;
import com.mega.eloan.lms.dao.L161S01EDao;
import com.mega.eloan.lms.dao.L162S01ADao;
import com.mega.eloan.lms.dao.L163S01ADao;
import com.mega.eloan.lms.dao.L164S01ADao;
import com.mega.eloan.lms.dao.L901M01ADao;
import com.mega.eloan.lms.dao.VLUSEDOC01Dao;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.eai.service.EAIService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.lms.pages.LMS1605M01Page;
import com.mega.eloan.lms.lms.service.LMS1405Service;
import com.mega.eloan.lms.lns.pages.LMS1601M01Page;
import com.mega.eloan.lms.lns.service.LMS1201Service;
import com.mega.eloan.lms.lns.service.LMS1401Service;
import com.mega.eloan.lms.lns.service.LMS1601Service;
import com.mega.eloan.lms.mfaloan.bean.ELF412B;
import com.mega.eloan.lms.mfaloan.bean.ELF600;
import com.mega.eloan.lms.mfaloan.bean.ELF601;
import com.mega.eloan.lms.mfaloan.bean.ELF602;
import com.mega.eloan.lms.mfaloan.service.MISSEFService;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.mfaloan.service.MisEJF305Service;
import com.mega.eloan.lms.mfaloan.service.MisELCRTBLService;
import com.mega.eloan.lms.mfaloan.service.MisELF383NService;
import com.mega.eloan.lms.mfaloan.service.MisELF412BService;
import com.mega.eloan.lms.mfaloan.service.MisELF447nService;
import com.mega.eloan.lms.mfaloan.service.MisELF503Service;
import com.mega.eloan.lms.mfaloan.service.MisELF517Service;
import com.mega.eloan.lms.mfaloan.service.MisELF600Service;
import com.mega.eloan.lms.mfaloan.service.MisELF603Service;
import com.mega.eloan.lms.mfaloan.service.MisELLNGTEEService;
import com.mega.eloan.lms.mfaloan.service.MisElcrcoService;
import com.mega.eloan.lms.mfaloan.service.MisElf902Service;
import com.mega.eloan.lms.mfaloan.service.MisEllnseekservice;
import com.mega.eloan.lms.mfaloan.service.MisIquotappService;
import com.mega.eloan.lms.mfaloan.service.MisIquotgurService;
import com.mega.eloan.lms.mfaloan.service.MisIquotjonService;
import com.mega.eloan.lms.mfaloan.service.MisLNF164Service;
import com.mega.eloan.lms.mfaloan.service.MisQuotainfService;
import com.mega.eloan.lms.mfaloan.service.MisQuotapprService;
import com.mega.eloan.lms.mfaloan.service.MisQuotsubService;
import com.mega.eloan.lms.mfaloan.service.MisQuotunioService;
import com.mega.eloan.lms.mfaloan.service.MisStoredProcService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.C120S01E;
import com.mega.eloan.lms.model.C160M01A;
import com.mega.eloan.lms.model.C160M01D;
import com.mega.eloan.lms.model.C801M01A;
import com.mega.eloan.lms.model.C801M01B;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01B;
import com.mega.eloan.lms.model.L120M01C;
import com.mega.eloan.lms.model.L120M01F;
import com.mega.eloan.lms.model.L120S01A;
import com.mega.eloan.lms.model.L120S01B;
import com.mega.eloan.lms.model.L120S01C;
import com.mega.eloan.lms.model.L120S01D;
import com.mega.eloan.lms.model.L120S01P;
import com.mega.eloan.lms.model.L120S04A;
import com.mega.eloan.lms.model.L120S04B;
import com.mega.eloan.lms.model.L120S04C;
import com.mega.eloan.lms.model.L120S21B;
import com.mega.eloan.lms.model.L120S21C;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01A_BF;
import com.mega.eloan.lms.model.L140M01B;
import com.mega.eloan.lms.model.L140M01C;
import com.mega.eloan.lms.model.L140M01C_BF;
import com.mega.eloan.lms.model.L140M01D;
import com.mega.eloan.lms.model.L140M01D_BF;
import com.mega.eloan.lms.model.L140M01E_AF;
import com.mega.eloan.lms.model.L140M01F;
import com.mega.eloan.lms.model.L140M01G;
import com.mega.eloan.lms.model.L140M01H;
import com.mega.eloan.lms.model.L140M01I;
import com.mega.eloan.lms.model.L140M01M;
import com.mega.eloan.lms.model.L140M01N;
import com.mega.eloan.lms.model.L140M01O;
import com.mega.eloan.lms.model.L140S12A;
import com.mega.eloan.lms.model.L160A01A;
import com.mega.eloan.lms.model.L160M01A;
import com.mega.eloan.lms.model.L160M01B;
import com.mega.eloan.lms.model.L160M01C;
import com.mega.eloan.lms.model.L160M01D;
import com.mega.eloan.lms.model.L161S01A;
import com.mega.eloan.lms.model.L161S01B;
import com.mega.eloan.lms.model.L161S01C;
import com.mega.eloan.lms.model.L161S01D;
import com.mega.eloan.lms.model.L161S01E;
import com.mega.eloan.lms.model.L162S01A;
import com.mega.eloan.lms.model.L163S01A;
import com.mega.eloan.lms.model.L164S01A;
import com.mega.eloan.lms.model.L901M01A;
import com.mega.eloan.lms.model.VLUSEDOC01;
import com.mega.eloan.lms.obsdb.service.ObsdbELF164Service;
import com.mega.eloan.lms.obsdb.service.ObsdbELF383Service;
import com.mega.eloan.lms.obsdb.service.ObsdbELF384Service;
import com.mega.eloan.lms.obsdb.service.ObsdbELF385Service;
import com.mega.eloan.lms.obsdb.service.ObsdbELF388Service;
import com.mega.eloan.lms.obsdb.service.ObsdbELF401Service;
import com.mega.eloan.lms.obsdb.service.ObsdbELF422Service;
import com.mega.eloan.lms.obsdb.service.ObsdbELF476Service;
import com.mega.eloan.lms.obsdb.service.ObsdbELF601Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.PropUtil;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.core.FlowException;
import tw.com.jcs.flow.service.FlowService;

/**
 * <pre>
 * 動用審核表 LMS1601ServiceImpl
 * </pre>
 * 
 * @since 2011/10/21
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/10/21,REX,new
 *          <li>2013/01/15,GaryChang,新增uploadELF503上傳結構化利率檔至MIS.ELF503
 *          <li>2013/07/03,Rex,同業聯貸為Y才需上傳同業聯貸
 *          <li>2013/07/19,修改判斷為動審表的分行
 *          </ul>
 */
@Service
public class LMS1601ServiceImpl extends AbstractCapService implements
		LMS1601Service {
	private static Logger logger = LoggerFactory
			.getLogger(LMS1601ServiceImpl.class);
	@Resource
	SysParameterService sysparamService;
	@Resource
	FlowNameService flowNameService;
	@Resource
	RPAService rpaservice;
	@Resource
	TempDataService tempDataService;
	@Resource
	OBSMqGwClient obsMqGwClient;
	@Resource
	LMS1405Service lms1405Service;
	@Resource
	FlowService flowService;
	@Resource
	EAIService eaiSrv;
	@Resource
	MisElf902Service misElf902Service;

	@Resource
	MisElcrcoService misElcrcoService;
	@Resource
	MisEJF305Service misEJF305Service;

	@Resource
	L120A01ADao l120a01aDao;

	@Resource
	L120M01ADao l120m01aDao;

	@Resource
	L120M01BDao l120m01bDao;

	@Resource
	L120M01CDao l120m01cDao;

	@Resource
	L120M01FDao l120m01fDao;

	@Resource
	L120S01ADao l120s01aDao;

	@Resource
	L120S01BDao l120s01bDao;

	@Resource
	L120S01CDao l120s01cDao;
	
	@Resource
	C120S01ADao c120s01aDao;

	@Resource
	L120S01DDao l120s01dDao;
	@Resource
	C120S01EDao c120s01eDao;

	@Resource
	L140M01E_AFDao l140m01e_efDao;
	
	@Resource
	L140M01NDao l140m01nDao;
	@Resource
	L140M01ADao l140m01aDao;

	@Resource
	L140S12ADao l140s12aDao;
	
	@Resource
	L160A01ADao l160A01aDao;

	@Resource
	L160M01ADao l160m01aDao;

	@Resource
	L160M01BDao l160m01bDao;

	@Resource
	L160M01CDao l160m01cDao;

	@Resource
	L160M01DDao l160m01dDao;

	@Resource
	L161S01ADao l161m01aDao;

	@Resource
	L161S01BDao l161m01bDao;

	@Resource
	L161S01CDao l161s01cDao;

	@Resource
	L161S01DDao l161s01dDao;

	@Resource
	L161S01EDao l161s01eDao;

	@Resource
	L162S01ADao l162s01aDao;

	@Resource
	L163S01ADao l163s01aDao;

	@Resource
	L901M01ADao l901m01aDao;

	@Resource
	VLUSEDOC01Dao vusedoc01Dao;

	@Resource
	DocFileDao docFileDao;

	@Resource
	DocLogService docLogService;

	@Resource
	BranchService branchService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	MisdbBASEService misdbBASEService;

	@Resource
	MisELLNGTEEService misELLNGTEEService;

	@Resource
	MisIquotjonService misIquotjonService;

	@Resource
	MisIquotgurService misIquotgurService;

	@Resource
	MisIquotappService misIquotappService;

	@Resource
	MisQuotainfService misQuotainfService;

	@Resource
	MisQuotunioService misQuotunioService;

	@Resource
	MisQuotapprService misQuotapprService;

	@Resource
	MisQuotsubService misQuotsubService;

	@Resource
	MisLNF164Service misLNF164Service;
	@Resource
	MisELF503Service misELF503Service;
	@Resource
	MisELCRTBLService misELCRTBLService;
	@Resource
	ObsdbELF164Service obsdbELF164Service;

	@Resource
	ObsdbELF383Service obsdbELF383Service;
	@Resource
	ObsdbELF384Service obsdbELF384Service;
	@Resource
	ObsdbELF385Service obsdbELF385Service;
	@Resource
	ObsdbELF388Service obsdbELF388Service;
	@Resource
	ObsdbELF401Service obsdbELF401Service;
	@Resource
	ObsdbELF422Service obsdbELF422Service;
	@Resource
	ObsdbELF476Service obsdbELF476Service;
	@Resource
	ObsdbELF601Service obsdbELF601Service;
	
	@Resource
	MISSEFService missefService;
	@Resource
	LMSService lmsService;
	@Resource
	DocFileService docFileService;

	@Resource
	CodeTypeService codeTypeService;
	@Resource
	L140M01MDao l140m01mDao;
	@Resource
	L140M01A_BFDao l140m01a_bfDao;

	@Resource
	L140M01C_BFDao l140m01c_bfDao;
	@Resource
	L140M01D_BFDao l140m01d_bfDao;

	@Resource
	C160M01ADao c160m01aDao;
	@Resource
	LMS1401Service lms1401Service;

	@Resource
	LMS1201Service lms1201Service;

	@Resource
	L140M01JDao l140m01jDao;

	@Resource
	CLS8011Service cls8011Service;

	@Resource
	MisELF517Service misElf517Service;

	// J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
	@Resource
	L164S01ADao l164s01aDao;

	@Resource
	AMLRelateService amlRelateService;

	// J-106-0029-003 Web e-Loan授信簽報書借款人基本資料與動審表黑名單查詢調整使用共用模組
	@Resource
	ICustomerService customerSrv;

	// J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
	@Resource
	MisELF412BService misElf412BService;

	// J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
	@Resource
	RetrialService retrialService;

	@Resource
	L120S04ADao l120s04aDao;
	@Resource
	L120S04BDao l120s04bDao;
	@Resource
	L120S04CDao l120s04cDao;

	@Resource
	MisELF600Service misELF600Service;

	@Resource
	MisELF447nService misELF447nService;

	@Resource
	DwdbBASEService dwdbBaseService;

	@Resource
	MisEllnseekservice misEllnseekservice;

	@Resource
	SysParameterService sysParameterService;
	@Resource
	private IVRGwClient ivrGwClient;

	@Resource
	MisCustdataService misCustdataService;

	@Resource
	MisStoredProcService msps;

	@Resource
	EloandbBASEService eloanDbBaseService;

	@Resource
	L120S21BDao l120s21bDao;

	@Resource
	L120S21CDao l120s21cDao;

	@Resource
	L140M01ODao l140m01oDao;

	@Resource
	CLSService clsService;

	@Resource
	MisELF383NService misELF383NService;
	
	@Resource
	MisELF603Service misELF603Service;

	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		if (clazz == L160M01A.class) {
			return l160m01aDao.findByMainIds(mainId);
		} else if (clazz == L160M01B.class) {
			return l160m01bDao.findByMainId(mainId);
		} else if (clazz == L160M01C.class) {
			return l160m01cDao.findByMainId(mainId);
		} else if (clazz == L160M01D.class) {
			return l160m01dDao.findByMainId(mainId);
		} else if (clazz == L161S01A.class) {
			return l161m01aDao.findByMainId(mainId);
		} else if (clazz == L161S01B.class) {
			return l161m01bDao.findByMainId(mainId);
		} else if (clazz == L161S01C.class) {
			return l161s01cDao.findByMainId(mainId);
		} else if (clazz == L161S01D.class) {
			return l161s01dDao.findByMainId(mainId);
		} else if (clazz == L161S01E.class) {
			return l161s01eDao.findByMainId(mainId);
		} else if (clazz == L162S01A.class) {
			return l162s01aDao.findByMainId(mainId);
		} else if (clazz == L163S01A.class) {
			return l163s01aDao.findByMainId(mainId);
		} else if (clazz == L164S01A.class) {
			return l164s01aDao.findByMainId(mainId);
		} else if (clazz == L120S04A.class) {
			return l120s04aDao.findByMainId(mainId);
		} else if (clazz == L120S04B.class) {
			return l120s04bDao.findByMainId(mainId);
		} else if (clazz == L120S04C.class) {
			return l120s04cDao.findByMainId(mainId);
		} else if (clazz == L140M01E_AF.class) {
			return l140m01e_efDao.findByMainId(mainId);
		} else if (clazz == L140S12A.class) {
			return l140s12aDao.findByMainId(mainId);
		}
		return null;
	}

	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				// set updater and updateTime
				try {
					if (Util.isEmpty(model.get(EloanConstants.OID))) {
						model.set("creator", user.getUserId());
						model.set("createTime", CapDate.getCurrentTimestamp());
					}
					model.set("updater", user.getUserId());
					model.set("updateTime", CapDate.getCurrentTimestamp());
				} catch (CapException e) {
					logger.error("set set updater or creator error "
							+ e.getMessage());
				}
				if (model instanceof L160M01A) {
					if (Util.isEmpty(((L160M01A) model).getOid())) {
						l160m01aDao.save((L160M01A) model);
						flowService.start("LMS1605Flow",
								((L160M01A) model).getOid(), user.getUserId(),
								user.getUnitNo());
						// 新增授權檔
						L160A01A l160a01a = new L160A01A();
						l160a01a.setAuthTime(CapDate.getCurrentTimestamp());
						l160a01a.setAuthType(DocAuthTypeEnum.MODIFY.getCode());
						l160a01a.setAuthUnit(user.getUnitNo());
						l160a01a.setMainId(((L160M01A) model).getMainId());
						l160a01a.setOwner(user.getUserId());
						l160a01a.setOwnUnit(user.getUnitNo());
						l160A01aDao.save(l160a01a);

					} else {
						// 當文件狀態為編製中時文件亂碼才變更

						l160m01aDao.save((L160M01A) model);
						if (!"Y".equals(SimpleContextHolder
								.get(EloanConstants.TEMPSAVE_RUN))) {
							tempDataService.deleteByMainId(((L160M01A) model)
									.getMainId());
							docLogService.record(((L160M01A) model).getOid(),
									DocLogEnum.SAVE);
						}

					}

				} else if (model instanceof L160M01B) {
					l160m01bDao.save((L160M01B) model);
				} else if (model instanceof L161S01A) {
					l161m01aDao.save((L161S01A) model);

				} else if (model instanceof L161S01B) {
					l161m01bDao.save((L161S01B) model);
				} else if (model instanceof L161S01C) {
					l161s01cDao.save((L161S01C) model);
				} else if (model instanceof L161S01D) {
					if (user != null) {
						((L161S01D) model).setUpdater(user.getUserId());
						((L161S01D) model).setUpdateTime(CapDate
								.getCurrentTimestamp());
						if (((L161S01D) model).getCreator() == null) {
							((L161S01D) model).setCreator(user.getUserId());
							((L161S01D) model).setCreateTime(CapDate
									.getCurrentTimestamp());
						}
					}
					l161s01dDao.save((L161S01D) model);
				} else if (model instanceof L161S01E) {
					if (user != null) {
						((L161S01E) model).setUpdater(user.getUserId());
						((L161S01E) model).setUpdateTime(CapDate
								.getCurrentTimestamp());
						if (((L161S01E) model).getCreator() == null) {
							((L161S01E) model).setCreator(user.getUserId());
							((L161S01E) model).setCreateTime(CapDate
									.getCurrentTimestamp());
						}
					}
					l161s01eDao.save((L161S01E) model);
				} else if (model instanceof L162S01A) {
					l162s01aDao.save((L162S01A) model);
				} else if (model instanceof L163S01A) {
					l163s01aDao.save((L163S01A) model);
				} else if (model instanceof L160M01D) {
					l160m01dDao.save((L160M01D) model);
				} else if (model instanceof L160M01C) {
					l160m01cDao.save((L160M01C) model);
				} else if (model instanceof L164S01A) {
					// J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
					l164s01aDao.save((L164S01A) model);
				}
			}
		}
	}

	@SuppressWarnings("rawtypes")
	@Override
	public boolean deleteListByOid(Class clazz, String[] oids) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		boolean flag = false;
		if (clazz == L160M01A.class) {
			// 不做直接刪除
			List<L160M01A> l160m01as = l160m01aDao.findByOids(oids);
			for (L160M01A l160m01a : l160m01as) {
				l160m01a.setDeletedTime(CapDate.getCurrentTimestamp());
				l160m01a.setUpdater(user.getUserId());
				docLogService.record(l160m01a.getOid(), DocLogEnum.DELETE);
			}
			l160m01aDao.save(l160m01as);
			return true;
		} else if (clazz == L161S01B.class) {
			List<L161S01B> l161m01bs = l161m01bDao.findByOid(oids);
			l161m01bDao.delete(l161m01bs);
			return true;
		} else if (clazz == L162S01A.class) {
			List<L162S01A> l162m01as = l162s01aDao.findByOid(oids);
			l162s01aDao.delete(l162m01as);
			return true;
		} else if (clazz == L164S01A.class) {
			// J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
			List<L164S01A> l164s01as = l164s01aDao.findByOid(oids);
			l164s01aDao.delete(l164s01as);
			return true;
		} else if (clazz == L161S01E.class) {
			// J-110-0540_05097_B1001 Web e-Loan企金授信配合調整E-loan系統動用審核表部分內容
			List<L161S01E> l161s01es = l161s01eDao.findByOid(oids);
			l161s01eDao.delete(l161s01es);
			return true;
		}
		return flag;
	}

	@Override
	public void delete(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L160M01A) {
					((L160M01A) model).setDeletedTime(CapDate
							.getCurrentTimestamp());
					((L160M01A) model).setUpdater(user.getUserId());
					docLogService.record(((L160M01A) model).getOid(),
							DocLogEnum.DELETE);
					l160m01aDao.save((L160M01A) model);
				} else if (model instanceof L160M01B) {
					l160m01bDao.delete((L160M01B) model);
				} else if (model instanceof L161S01A) {
					l161m01aDao.delete((L161S01A) model);
				} else if (model instanceof L161S01B) {
					l161m01bDao.delete((L161S01B) model);
				} else if (model instanceof L161S01C) {
					l161s01cDao.delete((L161S01C) model);
				} else if (model instanceof L161S01D) {
					l161s01dDao.delete((L161S01D) model);
				} else if (model instanceof L161S01E) {
					l161s01eDao.delete((L161S01E) model);
				} else if (model instanceof L162S01A) {
					l162s01aDao.delete((L162S01A) model);
				} else if (model instanceof L163S01A) {
					l163s01aDao.delete((L163S01A) model);
				} else if (model instanceof L160M01D) {
					l160m01dDao.delete((L160M01D) model);
				} else if (model instanceof L164S01A) {
					// J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
					l164s01aDao.delete((L164S01A) model);
				}
			}

		}

	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == L160M01A.class) {
			return l160m01aDao.findPage(search);
		} else if (clazz == L160M01B.class) {
			return l160m01bDao.findPage(search);
		} else if (clazz == L160M01C.class) {
			return l160m01cDao.findPage(search);
		} else if (clazz == L161S01A.class) {
			return l161m01aDao.findPage(search);
		} else if (clazz == L161S01B.class) {
			return l161m01bDao.findPage(search);
		} else if (clazz == L161S01C.class) {
			return l161s01cDao.findPage(search);
		} else if (clazz == L162S01A.class) {
			return l162s01aDao.findPage(search);
		} else if (clazz == VLUSEDOC01.class) {
			return vusedoc01Dao.findPage(search);
		} else if (clazz == L164S01A.class) {
			// J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
			return l164s01aDao.findPage(search);
		} else if (clazz == L161S01D.class) {
			return l161s01dDao.findPage(search);
		} else if (clazz == L161S01E.class) {
			return l161s01eDao.findPage(search);
		} else if (clazz == L140M01E_AF.class) {
			return l140m01e_efDao.findPage(search);
		}
		return null;
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		if (clazz == L160M01A.class) {
			return (T) l160m01aDao.findByOid(oid);
		} else if (clazz == L160M01B.class) {
			return (T) l160m01bDao.findByOid(oid);
		} else if (clazz == L161S01A.class) {
			return (T) l161m01aDao.findByOid(oid);
		} else if (clazz == L161S01B.class) {
			return (T) l161m01bDao.findByOid(oid);
		} else if (clazz == L161S01C.class) {
			return (T) l161s01cDao.findByOid(oid);
		} else if (clazz == L161S01D.class) {
			return (T) l161s01dDao.findByOid(oid);
		} else if (clazz == L161S01E.class) {
			return (T) l161s01eDao.findByOid(oid);
		} else if (clazz == L162S01A.class) {
			return (T) l162s01aDao.findByOid(oid);
		} else if (clazz == L163S01A.class) {
			return (T) l163s01aDao.findByOid(oid);
		} else if (clazz == L164S01A.class) {
			// J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
			return (T) l164s01aDao.findByOid(oid);
		} else if (clazz == L140M01E_AF.class) {
			return (T) l140m01e_efDao.findByOid(oid);
		}
		return null;
	}

	@Override
	public boolean deleteL160m01as(String[] oids) {
		boolean flag = false;
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<L160M01A> l160m01as = new ArrayList<L160M01A>();
		for (int i = 0, size = oids.length; i < size; i++) {
			L160M01A l160m01a = (L160M01A) findModelByOid(L160M01A.class,
					oids[i]);
			// 設定刪除並非直接刪除 ，只是標記刪除時間
			l160m01a.setDeletedTime(CapDate.getCurrentTimestamp());
			l160m01a.setUpdater(user.getUserId());
			l160m01as.add(l160m01a);
			docLogService.record(l160m01a.getOid(), DocLogEnum.DELETE);
		}

		if (!l160m01as.isEmpty()) {
			l160m01aDao.save(l160m01as);
			flag = true;
		}

		return flag;
	}

	@Override
	public void deleteL160m01bs(List<L160M01B> l160m01bs) {
		l160m01bDao.delete(l160m01bs);
	}

	@Override
	public void deleteL160m01ds(List<L160M01D> l160m01ds, boolean isAll) {
		if (isAll) {
			l160m01dDao.delete(l160m01ds);
		} else {
			List<L160M01D> L160M01DsOld = new ArrayList<L160M01D>();
			for (L160M01D l160m01d : l160m01ds) {
				String staffJob = l160m01d.getStaffJob();
				if (!("L6".equals(staffJob) || "L7".equals(staffJob))) {
					L160M01DsOld.add(l160m01d);
				}
			}
			l160m01dDao.delete(L160M01DsOld);
		}

	}

	@Override
	public void saveL160m01bList(List<L160M01B> list) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (L160M01B l160m01b : list) {
			l160m01b.setUpdater(user.getUserId());
			l160m01b.setUpdateTime(CapDate.getCurrentTimestamp());
		}
		l160m01bDao.save(list);
	}

	@Override
	public void saveL162m01aList(List<L162S01A> list) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (L162S01A l162m01a : list) {
			l162m01a.setUpdater(user.getUserId());
			l162m01a.setUpdateTime(CapDate.getCurrentTimestamp());
		}
		l162s01aDao.save(list);
	}

	@Override
	public void saveL160m01dList(List<L160M01D> list) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (L160M01D l160m01d : list) {
			l160m01d.setUpdater(user.getUserId());
			l160m01d.setUpdateTime(CapDate.getCurrentTimestamp());
		}
		l160m01dDao.save(list);
	}

	@Override
	public void saveL160m01cList(List<L160M01C> list) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (L160M01C l160m01c : list) {
			l160m01c.setUpdater(user.getUserId());
			l160m01c.setUpdateTime(CapDate.getCurrentTimestamp());
		}
		l160m01cDao.save(list);
	}

	@Override
	public List<L901M01A> findL901m01aByBranchIdAndItemType(String itemType,
			String branchId, String locale) {
		return l901m01aDao
				.findByItemTypeAndbranchId(itemType, branchId, locale);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.lms.service.LMS1605Service#deleteL160m01cs(java.util
	 * .List)
	 */
	@Override
	public void deleteL160m01cs(List<L160M01C> l160m01cs) {
		l160m01cDao.delete(l160m01cs);

	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.lms.service.LMS1605Service#deleteUploadFile(java.lang
	 * .String[])
	 */
	@Override
	public void deleteUploadFile(String[] oids) {
		List<DocFile> docfiles = docFileDao.findAllByOid(oids);
		for (DocFile docfile : docfiles) {
			docfile.setDeletedTime(CapDate.getCurrentTimestamp());
		}
		docFileDao.save(docfiles);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.lms.service.LMS1605Service#findL163m01aByMainId(java
	 * .lang.String)
	 */
	@Override
	public L163S01A findL163m01aByMainId(String mainId) {
		return l163s01aDao.findByUniqueKey(mainId);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.lms.service.LMS1605Service#findL161m01aByMainId(java
	 * .lang.String)
	 */
	@Override
	public L161S01A findL161m01aByMainId(String mainId, String cntrNo) {
		return l161m01aDao.findByUniqueKey(mainId, cntrNo);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.lms.service.LMS1605Service#deleteL162m01as(java.util
	 * .List)
	 */
	@Override
	public void deleteL162m01as(List<L162S01A> l162m01as) {
		l162s01aDao.delete(l162m01as);

	}

	@Override
	public int findL161m01bMaxSeqOLD(String mainId) {
		List<L161S01B> l161m01bs = l161m01bDao.findByMainId(mainId);
		int count = 0;
		int seqval = 0;
		for (L161S01B l161m01b : l161m01bs) {// 取出這個mainID底下的最大Seq
			seqval = l161m01b.getSeq();
			if (seqval > count) {
				count = seqval;
			}
		}
		return ++count;
	}

	@Override
	public int findL161m01bMaxSeq(String mainId, String pid) {
		List<L161S01B> l161m01bs = l161m01bDao.findByMainIdUid(mainId, pid);
		int count = 0;
		int seqval = 0;
		for (L161S01B l161m01b : l161m01bs) {// 取出這個mainID底下的最大Seq
			seqval = l161m01b.getSeq();
			if (seqval > count) {
				count = seqval;
			}
		}
		return ++count;
	}

	@SuppressWarnings("unchecked")
	@Override
	public void deleteListReInclude(String mainId) {
		List<L160M01B> l160m01bs = (List<L160M01B>) findListByMainId(
				L160M01B.class, mainId);

		List<L161S01A> l161s01as = (List<L161S01A>) findListByMainId(
				L161S01A.class, mainId);

		List<L161S01B> l161s01bs = (List<L161S01B>) findListByMainId(
				L161S01B.class, mainId);

		List<L161S01C> l161s01cs = (List<L161S01C>) findListByMainId(
				L161S01C.class, mainId);

		List<L162S01A> l162m01as = (List<L162S01A>) findListByMainId(
				L162S01A.class, mainId);

		// J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
		List<L164S01A> l164s01as = (List<L164S01A>) this.findListByMainId(
				L164S01A.class, mainId);

		List<L120S01P> l120s01ps = (List<L120S01P>) amlRelateService
				.findListByMainId(L120S01P.class, mainId);

		// J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制
		for (L161S01A l161s01a : l161s01as) {
			List<L120S04A> l120s04as = (List<L120S04A>) this.findListByMainId(
					L120S04A.class, l161s01a.getUid());
			if (l120s04as != null && !l120s04as.isEmpty()) {
				l120s04aDao.delete(l120s04as);
			}

			List<L120S04B> l120s04bs = (List<L120S04B>) this.findListByMainId(
					L120S04B.class, l161s01a.getUid());
			if (l120s04bs != null && !l120s04bs.isEmpty()) {
				l120s04bDao.delete(l120s04bs);
			}

			List<L120S04C> l120s04cs = (List<L120S04C>) this.findListByMainId(
					L120S04C.class, l161s01a.getUid());
			if (l120s04cs != null && !l120s04cs.isEmpty()) {
				l120s04cDao.delete(l120s04cs);
			}

		}

		// 當該mainId已經存在的額度序號，要先刪除再引進新的。
		if (!l160m01bs.isEmpty()) {
			l160m01bDao.delete(l160m01bs);
		}

		// 當該mainId已經存在的額度序號，要先刪除再引進新的。
		if (!l161s01as.isEmpty()) {
			l161m01aDao.delete(l161s01as);
		}

		// 當該mainId已經存在的主從債務人資料，要先刪除再引進新的。
		if (!l161s01bs.isEmpty()) {
			l161m01bDao.delete(l161s01bs);
		}

		// 當該mainId已經存在的主從債務人資料，要先刪除再引進新的。
		if (!l161s01cs.isEmpty()) {
			l161s01cDao.delete(l161s01cs);
		}

		// 當該mainId已經存在的主從債務人資料，要先刪除再引進新的。
		if (!l162m01as.isEmpty()) {
			l162s01aDao.delete(l162m01as);
		}

		// J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
		if (l164s01as != null && !l164s01as.isEmpty()) {
			this.deleteL164s01as(l164s01as);
		}

		if (l120s01ps != null && !l120s01ps.isEmpty()) {
			amlRelateService.deleteListL120s01p(l120s01ps);
		}

	}

	@Override
	public void flowAction(String mainOid, GenericBean model,
			boolean setResult, boolean resultType, boolean upMis) throws Throwable {
		if (upMis) {

			Properties prop = MessageBundleScriptCreator
					.getComponentResource(LMS1601M01Page.class);

			List<L161S01A> l161s01as = (List<L161S01A>) findListByMainId(
					L161S01A.class, ((L160M01A) model).getMainId());

			if (l161s01as == null || l161s01as.isEmpty()) {
				// 無法取得額度動用資訊主檔(L161S01A)
				throw new CapMessageException(
						prop.getProperty("L160M01A.message79"), getClass());
			}

			// J-103-0202-005 Web e-Loan授信簽案衍生性金融商品遠匯與換匯科目，改以交易額度來簽案。
			StringBuffer errCntrNo1 = new StringBuffer("");
			for (L161S01A l161s01a : l161s01as) {
				// J-103-0202-005 Web e-Loan授信簽案衍生性金融商品遠匯與換匯科目，改以交易額度來簽案。
				// 非不變或01.不動用，僅修改聯貸參貸比率時，不用檢核(不用上傳名目額度)
				L140M01A l140m01a = lms1401Service
						.findL140m01aByMainId(l161s01a.getCntrMainId());

				if (l140m01a == null) {
					// 找不到額度明細表
					throw new CapMessageException(
							prop.getProperty("L160M01A.message66")
									+ l161s01a.getCntrNo(), getClass());
				}

				if (Util.equals(l161s01a.getIsDerivatives(), "")) {

					Boolean hasDerivateSubjectFlag = false;

					ArrayList<String> itemsAll = new ArrayList<String>();
					List<L140M01C> l140m01cs = lms1401Service
							.findL140m01cListByMainId(l140m01a.getMainId());

					if (l140m01cs != null && !l140m01cs.isEmpty()) {
						for (L140M01C l140m01c : l140m01cs) {
							itemsAll.add(l140m01c.getLoanTP());
						}

						hasDerivateSubjectFlag = lmsService
								.hasDerivateSubject(itemsAll
										.toArray(new String[itemsAll.size()]));

						if (hasDerivateSubjectFlag == true) {
							errCntrNo1.append(Util.equals(
									errCntrNo1.toString(), "") ? "" : "、");
							errCntrNo1.append(l140m01a.getCntrNo());
						}

					} else {
						// 找不到額度明細表
						throw new CapMessageException(
								prop.getProperty("L160M01A.message66")
										+ l161s01a.getCntrNo(), getClass());
					}

				} else if (Util.equals(l161s01a.getIsDerivatives(),
						UtilConstants.DEFAULT.是)) {
					if (Util.equals(l161s01a.getDervApplyAmtType(), "")) {
						errCntrNo1
								.append(Util.equals(errCntrNo1.toString(), "") ? ""
										: "、");
						errCntrNo1.append(l140m01a.getCntrNo());

					}
				}

			}
			if (Util.notEquals(errCntrNo1.toString(), "")) {
				throw new CapMessageException(
						prop.getProperty("L160M01A.cntrInfo")
								+ errCntrNo1.toString()
								+ prop.getProperty("L160M01A.message75") + "：「"
								+ prop.getProperty("L160M01A.dervApplyAmtType")
								+ "」", getClass());
			}

			this.upLoadMIS((L160M01A) model, false);
		}

		if (model instanceof L160M01A) {
			save((L160M01A) model);
		}
		try {
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			FlowInstance inst = flowService.createQuery().id(mainOid).query();
			if (inst == null) {
				inst = flowService.start("LMS1605Flow",
						((L160M01A) model).getOid(), user.getUserId(),
						user.getUnitNo());
			}
			if (setResult) {
				inst.setDeptId(user.getUnitNo());
				inst.setUserId(user.getUserId());
				// inst.setNextDept("200");
				// inst.setNextUser("nextTest");

				// resultType 控制前進還是後退
				// 當有先行動用的狀態 是到03O 非先行動用表示已完成 到05O

				L163S01A l163m01a = ((L160M01A) model).getL163S01A();
				// 先行動用_待覆核04O
				if (CreditDocStatusEnum.先行動用_待覆核.getCode().equals(
						((L160M01A) model).getDocStatus())) {
					inst.setAttribute("result", resultType ? "結案" : "退回");

					// 當在待覆核執行先行動用已辦妥要填入覆核日期 ，如果是退回要清空乙級主管
					if (resultType) {
						l163m01a.setBfReCheckDate(CapDate.getCurrentTimestamp());
						l163m01a.setBossId(user.getUserId());

						// J-110-0547 為控管先行動用之授信案件，增加先行動用呈核及控制表預定補全日期之通知功能。
						// for批次使用，系統實際核准時間(先行動用覆核)，讓晚上批次可以抓資料寫進ELF601
						if (model instanceof L160M01A) {
							L160M01A l160m01a = (L160M01A) model;
							l160m01a.setSysActApproveTime(CapDate
									.getCurrentTimestamp());
							save(l160m01a);
						}
					} else {
						l163m01a.setBossId(null);
					}
					save(l163m01a);
				} else {
					// 當核定動撥有兩種情形，先行動用的核定跟非先行動用
					String resultNext = (UtilConstants.DEFAULT.是
							.equals(((L160M01A) model).getUseType())) ? "先行動用"
							: "結案";
					inst.setAttribute("result", resultType ? resultNext : "退回");
				}

			}
			inst.next();

		} catch (FlowException e) {
			Throwable t1 = e;
			while (t1.getCause() != null) {
				t1 = t1.getCause();
			}
			throw t1;
		}
	}

	@Override
	public void flowActionGo(String mainOid, L163S01A l163s01a)	throws Throwable {

		try {
			save(l163s01a);
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			FlowInstance inst = flowService.createQuery().id(mainOid).query();
			inst.setDeptId(user.getUnitNo());
			inst.setUserId(user.getUserId());
			inst.next();

		} catch (FlowException e) {
			Throwable t1 = e;
			while (t1.getCause() != null) {
				t1 = t1.getCause();
			}
			throw t1;
		}
	}

	// J-106-0029-003 Web e-Loan授信簽報書借款人基本資料與動審表黑名單查詢調整使用共用模組
	@Override
	public JSONObject findBlackPage(String name, String mainId)	throws CapException {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		// ********************************************************************************
		// BGN J-106-0029-003 Web
		// e-Loan授信簽報書借款人基本資料與動審表黑名單查詢調整使用共用模組
		// OBSMqGwReqMessage obsmqReq = new OBSMqGwReqMessage("004", mainId);
		// obsmqReq.setDefaultRequestHeader(user.getUnitNo());
		// obsmqReq.addRequest("ENG-NAME", name);
		// // 測試資料
		// // obsmqReq.setDefaultRequestHeader("0A7");
		// // obsmqReq.addRequest("ENG-NAME", "BIN LADEN");
		// JSONObject resp = null;
		// resp = obsMqGwClient.send(obsmqReq);
		// logger.info("004==>respon" + resp.toString());
		// // 成功會收到回應訊息
		// //
		// {"HSEQNO":"A5097674-5243-4C0C-8B91-8C33CAB5D85F","HTXNID":"004","HBRNO":"0A7","HRET-CODE":"MCP0000","HTXN-PAGE":"01","HTXN-CNT":"000","HTXN-TOT":"0000","HTXN-TYPE":"S","HTXN-END":"Y","HRET-MSG":"","HFILLER":"","NEXT-KEY":"OFAC0000011378OBIN LADEN","RECORD":[]}
		// // 失敗會收到GWException
		// END J-106-0029-003 Web
		// ************************************************************************************

		String unitNo = user.getUnitNo();
		JSONObject resp = new JSONObject();
		try {
			List<String> blackList = customerSrv.findBlackList(unitNo, name,
					mainId);
			String blackListCode = blackList
					.get(ICustomerService.BlackList_ReturnCode);
			String memo = blackList.get(ICustomerService.BlackList_OFACName);

			JSONArray value = new JSONArray();
			if (Util.equals(blackListCode,
					UtilConstants.Casedoc.L120s09aBlackListCode.是黑名單)
					|| Util.equals(blackListCode,
							UtilConstants.Casedoc.L120s09aBlackListCode.可能是黑名單)) {
				value.add(blackListCode);
				value.add(memo);
			} else if (Util.equals(blackListCode,
					UtilConstants.Casedoc.L120s09aBlackListCode.未列於黑名單)) {
			} else {
				// ????
				value.add(blackListCode);
				value.add(memo);
			}
			resp.put("RECORD", value);

		} catch (CapException e) {
			throw e;
		}

		return resp;
	}

	@Override
	public void upLoadMIS(L160M01A l160m01a, boolean isTest) throws CapException {
		String brnId = l160m01a.getOwnBrId();
		long t1 = System.currentTimeMillis();

		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1601M01Page.class);

		if (Util.equals(Util.trim(l160m01a.getNewVersion()), "")
				|| Util.equals(Util.trim(l160m01a.getNewVersion()), "00")) {
			// 請至文件資訊頁籤重新執行「選擇額度明細表」
			throw new CapMessageException(
					prop.getProperty("L160M01A.message73"), getClass());
		}

		L120M01A l120m01a = l120m01aDao.findByMainId(l160m01a.getSrcMainId());
		List<L120S01C> l120s01cs = l120s01cDao.findByMainId(l120m01a
				.getMainId());
		L120M01B l120m01b = l120m01bDao.findByUniqueKey(l120m01a.getMainId());
		// 取得該額度明細表 對應 額度序號
		HashMap<String, String> mainIdmapingCntrno = new HashMap<String, String>();
		List<String> mainIds = new ArrayList<String>();
		// 將查詢結果放到List裡面
		List<String> cntrNosList = new ArrayList<String>();
		for (L160M01B l160m01b : l160m01a.getL160m01b()) {
			mainIds.add(l160m01b.getReMainId());
			String cntrNo = l160m01b.getCntrNo();
			if (isTest) {
				cntrNo = "X" + IDGenerator.getUUID().substring(0, 11);
			}
			mainIdmapingCntrno.put(l160m01b.getReMainId(), cntrNo);
			// cntrNosTemp.append(cntrNosTemp.length() > 0 ? "," : "");
			// cntrNosTemp.append("'").append(cntrNo).append("'");
			cntrNosList.add(cntrNo);

		}

		List<L120M01F> l120m01fs = l120m01fDao.findByMainId(l160m01a
				.getSrcMainId());
		// 取出所有的額度序號一次查詢

		List<L140M01A> l140m01as = l140m01aDao
				.findL140m01aListByMainIdList(mainIds
						.toArray(new String[mainIds.size()]));

		List<Map<String, Object>> cntrNoData = misIquotappService
				.findByCntrNo(cntrNosList.toArray(new String[0]));

		ArrayList<String> cntrNoList = new ArrayList<String>();
		for (Map<String, Object> map : cntrNoData) {
			cntrNoList.add((String) map.get("QUOTANO"));
		}

		// 目前系統時間
		String sDate = CapDate.formatDate(new Date(),
				UtilConstants.DateFormat.YYYY_MM_DD);
		// 目前系統時間(時分秒)
		String sDateTime = CapDate.convertTimestampToString(CapDate.getCurrentTimestamp(), UtilConstants.DateFormat.YYYY_MM_DD_HH_MM_SS);
		// 借款人資料檔
		List<L120S01A> l120s01as = l120s01aDao.findByMainId(l120m01a
				.getMainId());
		HashMap<String, HashMap<String, String>> custDatas = new HashMap<String, HashMap<String, String>>();
		for (L120S01A l120s01a : l120s01as) {
			HashMap<String, String> custData = new HashMap<String, String>();
			custData.put("existDate", CapDate.formatDate(
					l120s01a.getExistDate(),
					UtilConstants.DateFormat.YYYY_MM_DD));
			custData.put("feeDate", CapDate.formatDate(l120s01a.getFeeDate(),
					UtilConstants.DateFormat.YYYY_MM_DD));
			custData.put("countryDate", CapDate.formatDate(
					l120s01a.getCountryDate(),
					UtilConstants.DateFormat.YYYY_MM_DD));
			custDatas.put(
					StrUtils.concat(l120s01a.getCustId(), l120s01a.getDupNo()),
					custData);
		}
		Map<String, String> codeTypeMap = codeTypeService
				.findByCodeType(UtilConstants.CodeTypeItem.授信科目轉會計科目);
		// 資料修改人
		String updater = MegaSSOSecurityContext.getUserId();

		// 重新上傳MIS
		if (l160m01a.getDocStatus()
				.equals(CreditDocStatusEnum.海外_已核准.getCode())) {
			String newUpdater = l160m01a.getApprover();
			if (!Util.equals(newUpdater, "")) {
				updater = newUpdater;
			}

			String newSDate = CapDate.formatDate(l160m01a.getApproveTime(),
					UtilConstants.DateFormat.YYYY_MM_DD);
			if (!Util.equals(newSDate, "")) {
				sDate = newSDate;
			}

		}

		// 存放處理過的客戶
		List<String> tProcessCustId = new ArrayList<String>();
		// 存放已處理過的額度序號
		List<String> tProcessSno = new ArrayList<String>();

		// MIS 用
		List<Object[]> iquotgurList = new ArrayList<Object[]>();
		List<Object[]> iquotjonList = new ArrayList<Object[]>();
		List<Object[]> quotainfList = new ArrayList<Object[]>();
		List<Object[]> quotunioList = new ArrayList<Object[]>();
		List<Object[]> lnf164List = new ArrayList<Object[]>();
		List<ELF600> elf600List = new ArrayList<ELF600>();

		// AS400 用
		List<Object[]> elf164List = new ArrayList<Object[]>();
		// List<Object[]> elf383List = new ArrayList<Object[]>();
		// List<Object[]> elf384List = new ArrayList<Object[]>();
		List<Object[]> elf385List = new ArrayList<Object[]>();
		List<Object[]> elf422List = new ArrayList<Object[]>();
		List<Object[]> elf503List = new ArrayList<Object[]>();
		List<Object[]> elf503DeleteList = new ArrayList<Object[]>();
		Boolean upAs400 = false;
		if (UtilConstants.BrNoType.國外.equals(branchService.getBranch(brnId)
				.getBrNoFlag())) {
			upAs400 = true;
		}
		HashMap<String, String> checkIquotjon = new HashMap<String, String>();
		String cntrNo = "";
		for (L140M01A l140m01a : l140m01as) {
			L120S01B l120s01b = l120s01bDao.findByUniqueKey(
					l120m01a.getMainId(), l140m01a.getCustId(),
					l140m01a.getDupNo());
			L140M01M l140m01m = l140m01mDao.findByMainId(l140m01a.getMainId());
			cntrNo = mainIdmapingCntrno.get(l140m01a.getMainId());
			if (Util.isEmpty(cntrNo)) {
				cntrNo = l140m01a.getCntrNo();
			}
			// 若該額度序號已經處理過則直接跳過此份額度明細表
			if (Util.isEmpty(Util.trim(cntrNo)) || tProcessSno.contains(cntrNo)) {
				continue;
			}

			// 將處理完的額度序號存到到 tProcessSno 中
			tProcessSno.add(cntrNo);

			L161S01A l161s01a = this.findL161m01aByMainIdCntrno(
					l160m01a.getMainId(), cntrNo);

			if (l161s01a == null) {
				// 無法取得額度動用資訊主檔(L161S01A)
				throw new CapMessageException(
						prop.getProperty("L160M01A.message79"), getClass());
			}

			logger.info("{}=======>{}", "Start", "sendFtpToArServer|cntrNo==>"
					+ cntrNo);

			String snoKind = l161s01a.getSnoKind();
			if (FlowDocStatusEnum.已核准.getCode().equals(l140m01a.getDocStatus())) {
				// 測試FTP檔案到AR
				if (Util.equals(snoKind,
						UtilConstants.Cntrdoc.snoKind.應收帳款賣方一般戶)
						|| Util.equals(snoKind,
								UtilConstants.Cntrdoc.snoKind.應收帳款賣方聯貸母戶)) {

					lmsService.sendFtpToArServer(l140m01a.getMainId(),
							l140m01a.getCntrNo());
				}

			}

			logger.info("{}=======>{}", "Start", "uploadIquotapp|cntrNo==>"
					+ cntrNo);
			// 1.上傳 核准額度資料檔 IQUOTAPP
			this.uploadIquotapp(brnId, l120m01a, l140m01a, l120m01fs,
					cntrNoList, updater, upAs400, cntrNo);
			logger.info("{}=======>{}", "Start", "uploadIquotgur|cntrNo==>"
					+ cntrNo);
			// 2.上傳 保證人檔IQUOTGUR
			this.uploadIquotgur(l140m01a, updater, iquotgurList, cntrNo);
			logger.info("{}=======>{}", "Start", "uploadIquotjon|cntrNo==>"
					+ cntrNo);
			// 3.上傳 共同借款人檔Iquotjon
			this.uploadIquotjon(l140m01a, l120s01as, updater, iquotjonList,
					cntrNo, checkIquotjon, l120m01a);
			logger.info("{}=======>{}", "Start", "uploadQuotainf|cntrNo==>"
					+ cntrNo);
			// 6.上傳 額度資訊檔 QUOTAINF
			this.uploadQuotainf(brnId, l160m01a, l140m01a, l120m01a, updater,
					quotainfList, elf422List, upAs400, cntrNo, l120m01fs);

			// 非不變或01.不動用，僅修改聯貸參貸比率時要上傳QUOTUNIO
			if (!UtilConstants.Cntrdoc.Property.不變.equals(l140m01a
					.getProPerty())
					|| Util.equals(l161s01a.getUseSpecialReason(), "01")) {
				logger.info("{}=======>{}", "Start", "uploadQuotunio|cntrNo==>"
						+ cntrNo);
				// 7.上傳 聯貸案參貸比率-自行參貸 QUOTUNIO
				this.uploadQuotunio(brnId, l160m01a, l140m01a, l120m01a,
						updater, quotunioList, elf385List, upAs400, cntrNo,
						l161s01a);

			}
			logger.info("{}=======>{}", "Start", "uploadQuotappr|cntrNo==>"
					+ cntrNo);
			// 8.上傳 授信額度檔 QUOTAPPR
			this.uploadQuotappr(brnId, l160m01a, l140m01a, l120m01a, l120m01b,
					l120s01as, l120s01cs, custDatas, updater, sDate, upAs400,
					cntrNo, l120s01b, l140m01m, l161s01a);

			// 此額度性質為「取消」, 不上傳科(子)目及其限額
			if (LMSUtil.isContainValue(l140m01a.getProPerty(),
					UtilConstants.Cntrdoc.Property.取消)) {
				continue;
			}
			logger.info("{}=======>{}", "Start", "uploadQuotsub|cntrNo==>"
					+ cntrNo);
			// 9.上傳科(子)目及其限額檔Quotsub
			this.uploadQuotsub(brnId, l160m01a, l140m01a, l120m01a, updater,
					sDate, upAs400, codeTypeMap, cntrNo);
			logger.info("{}=======>{}", "Start", "uploadLNF164|cntrNo==>"
					+ cntrNo);
			// 10.-利率條件LN.LNF164
			this.uploadLNF164(brnId, l160m01a, l140m01a, l120m01a, updater,
					sDate, lnf164List, elf164List, upAs400, cntrNo);
			this.uploadLNF164ByL140M01N(brnId, l160m01a, l140m01a, l120m01a,
					updater, sDate, lnf164List, cntrNo);
			logger.info("{}=======>{}", "Start", "uploadELF503|cntrNo==>"
					+ cntrNo);
			// 11.-結構化利率檔MIS.ELF503
			lmsService.uploadELF503(brnId, l140m01a, l120m01a, updater, sDate,
					elf503List, elf503DeleteList, cntrNo,
					Util.trim(l140m01a.getCustId()),
					Util.trim(l140m01a.getDupNo()));

			// J-104-0097-001
			// 配合業報指示，有關建商餘屋貸款之餘額列入控管，申請於e-Loan管理系統授信簽報書之額度明細表增加管控註記
			// 12.-建案完工未出售房屋之融資檔MIS.ELF517
			this.uploadELF517(l160m01a, l140m01a, l120m01a, cntrNo);

			// J-105-0263-001 配合a-Loan新增利率比對報表，Web e-Loan企金核准時同步更新ELF500相同額度之資料
			this.updateELF500(l160m01a, l140m01a, l120m01a, cntrNo);

			this.lmsService.genELF600ObjectWhenNoRecord(elf600List, l140m01m,
					cntrNo, l140m01a.getCustId(), l140m01a.getDupNo());

			// J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制
			this.updateELF600(l160m01a, l140m01a, l120m01a, cntrNo, l161s01a,
					updater);
			// J-113-0168 E-LOAN完成動審上送額度介面資料至A-LOAN時，針對屬授信額度者(排除衍生性金融商品業務外)，立即通知0024為本行授信戶
			if (!tProcessCustId.contains(l140m01a.getCustId() + l140m01a.getDupNo())) {
				// (排除)衍生性金融商品科目961 962 963 964 、 純Z類的虛科目、應收帳款買方 fact_type = 60的不用通知
				// 已處理過的額度序號上面跳過了，by客戶傳處理過的客戶也要跳過
				this.callLNSP0130ForUpdateLnFlag(l140m01a, l160m01a, brnId);
				tProcessCustId.add(l140m01a.getCustId() + l140m01a.getDupNo());
			}
			
		}
		// G-113-0036 異動聯貸案參貸比率時(uploadQuotappr)，如有新增額度序號一併發聯行額度明細
		lmsService.L1601M01AsendToL141M01A(l120m01a, l140m01as, l160m01a);

		// 4.上傳主從債務人檔 保證人 ELLNGTEE
		// 5.上傳主從債務人檔 共同借款人 ELLNGTEE
		logger.info("{}=======>{}", "Start", "uploadEllngtee|");
		this.uploadEllngtee(l160m01a, l120s01as, updater, upAs400);
		logger.info("{}=======>{}", "Start", "uploadQuotunio2|");
		// 11.上傳 聯貸案參貸比率-同業聯貸參貸 QUOTUNIO
		this.uploadQuotunio2(brnId, l160m01a, l120m01a, updater, quotunioList,
				elf385List, upAs400);
		logger.info("{}=======>{}", "Start", "misIquotgurService.insert|");
		misIquotgurService.insert(iquotgurList);
		logger.info("{}=======>{}", "Start", "misIquotjonService.insert|");
		misIquotjonService.insert(iquotjonList);
		logger.info("{}=======>{}", "Start", "misQuotainfService.insert|");
		misQuotainfService.insert(quotainfList);
		logger.info("{}=======>{}", "Start", "misQuotunioService.insert|");
		misQuotunioService.insert(quotunioList);
		logger.info("{}=======>{}", "Start", "misLNF164Service.insert|");
		misLNF164Service.insert(lnf164List);
		logger.info("{}=======>{}", "Start", "misELF503Service.insert|");
		lmsService.showDataListValue(elf503List);
		misELF503Service.insert(elf503DeleteList, elf503List);

		// J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
		// J-108-0087_05097_B1002 Web e-Loan企金覆審逾期未覆審報表修改「備註說明」等欄位。
		logger.info("{}=======>{}", "Start", "misELF412Bervice.insert|");
		// 235反應，因為客戶變成利害關係人，所以雖然沒有新做或增額，但是會從非常董會授權變成常董會授權，所以動審表還是需要上傳ELF412B
		this.gfnUpdateReviewListData(l160m01a);

		logger.info(StrUtils.concat("\n mis upLoad total Time ===>",
				(System.currentTimeMillis() - t1), " ms"));

		for (ELF600 elf600 : elf600List) {
			logger.info("{}=======>{}", "Start", "misELF600Service.insert|");
			this.misELF600Service.insert(elf600);
		}
		// J-113-0035 貸後追蹤分項紀錄檔
		logger.info("{}=======>{}", "Start", "misELF603Service.insert|");
		this.updateELF603(l160m01a, l120m01a, l140m01as, updater, sDateTime);
		
		// 以下為AS400 測試
		long t2 = System.currentTimeMillis();
		// obsdbELF164Service.insert(brnId,
		// LMSUtil.covertAs400Time(elf164List));
		if (upAs400) {
			logger.info("{}=======>{}", "Start", "obsdbELF385Service.insert|");
			obsdbELF385Service.insert(brnId,
					LMSUtil.covertAs400Time(elf385List));
			logger.info("{}=======>{}", "Start", "obsdbELF422Service.insert|");
			obsdbELF422Service.insert(brnId,
					LMSUtil.covertAs400Time(elf422List));
			logger.info(StrUtils.concat("\n as400 upLoad total Time ===>",
					(System.currentTimeMillis() - t2), " ms"));
		}

		// 以下為測試rollback 用
		// Object ddd = null;
		//
		// logger.info(ddd.toString());

	}

	/**
	 * 上傳核准額度資料檔 IQUOTAPP(AS400-ELF388)
	 * 
	 * @param BRNID
	 *            AS400上傳分行代碼
	 * @param l120m01a
	 *            案件簽報書
	 * @param l140m01a
	 *            額度明細表
	 * @param l120m01f
	 *            借款人
	 * @param cntrNoList
	 *            額度序號list
	 * @param updater
	 *            更新者
	 * @param upAS400
	 *            是否上傳AS400
	 */
	private void uploadIquotapp(String BRNID, L120M01A l120m01a,
			L140M01A l140m01a, List<L120M01F> l120m01fs,
			ArrayList<String> cntrNoList, String updater, boolean upAS400,
			String cntrNo) {

		// 身分證/統編 (額度明細表)
		String custId = l140m01a.getCustId();

		// 重複序號 額度明細表
		String dupNo = Util.isEmpty(l140m01a.getDupNo()) ? "0" : l140m01a
				.getDupNo();
		// 初放主管姓名
		String omgrName = Util.isEmpty(userInfoService.getUserName(l120m01a
				.getApprover())) ? "" : userInfoService.getUserName(l120m01a
				.getApprover());
		// 敘作主管姓名
		String fmgrName = Util.isEmpty(userInfoService.getUserName(l120m01a
				.getApprover())) ? "" : userInfoService.getUserName(l120m01a
				.getApprover());
		// 授信經辦行員編號
		String icbcNo = "";
		for (L120M01F l120m01f : l120m01fs) {
			String staffJob = l120m01f.getStaffJob();
			String staffNo = l120m01f.getStaffNo();
			if (UtilConstants.BRANCHTYPE.分行.equals(l120m01f.getBranchType())) {
				if (UtilConstants.STAFFJOB.經辦L1.equals(staffJob)) {
					icbcNo = staffNo;
				}
				if (UtilConstants.STAFFJOB.單位授權主管L5.equals(staffJob)) {

					omgrName = Util.trimSizeInOS390(this.getUserName(staffNo),
							20);
					fmgrName = Util.trimSizeInOS390(this.getUserName(staffNo),
							20);
				}
			}
		}
		// 授信經辦姓名
		String cName = Util.isEmpty(userInfoService.getUserName(icbcNo)) ? ""
				: userInfoService.getUserName(icbcNo);
		cName = Util.trimSizeInOS390(cName, 20);

		// 授權等級
		String approLvl = "";

		if (UtilConstants.Casedoc.DocKind.授權內.equals(l120m01a.getDocKind())) {
			approLvl = UtilConstants.Casedoc.CaseLvl.其他;
			if (UtilConstants.Casedoc.AuthLvl.營運中心授權內.equals(l120m01a
					.getAuthLvl())) {
				approLvl = UtilConstants.Casedoc.CaseLvl.營運中心營運長;
			}

		} else if (UtilConstants.Casedoc.DocKind.授權外.equals(l120m01a
				.getDocKind())) {
			approLvl = Util.trim(l120m01a.getCaseLvl());
		} else {
			logger.info(StrUtils.concat("\n DocKind is NULL",
					l120m01a.getDocKind()));
		}
		if (Util.isEmpty(approLvl)) {
			approLvl = "";
		}
		// 當有此額度序號表示非初貸
		if (cntrNoList.contains(cntrNo)) {
			// ---- 非初次貸放時只傳敘作主管, 原初放主管不上傳 ---
			misIquotappService.update(custId, dupNo,
					LMSUtil.forFiveBossId(icbcNo), cName, fmgrName, approLvl,
					LMSUtil.forFiveBossId(updater), cntrNo);

		} else {
			// ---- 初次貸放時初放與敘作主管為同一位 ---
			misIquotappService.insert(cntrNo, custId, dupNo,
					LMSUtil.forFiveBossId(icbcNo), cName, omgrName, fmgrName,
					approLvl, LMSUtil.forFiveBossId(updater));

		}
		if (upAS400) {
			// 以下為as400上傳
			if (obsdbELF388Service.selByQuotano(BRNID, new Object[] { cntrNo })
					.isEmpty()) {
				obsdbELF388Service.insert(
						BRNID,
						LMSUtil.covertAs400Time(new Object[] { cntrNo, custId,
								dupNo, LMSUtil.forFiveBossId(icbcNo), cName,
								omgrName, fmgrName, approLvl, updater,
								CapDate.getCurrentTimestamp() }));
			} else {
				obsdbELF388Service.update(
						BRNID,
						LMSUtil.covertAs400Time(new Object[] { custId, dupNo,
								LMSUtil.forFiveBossId(icbcNo), cName, fmgrName,
								Util.trim(approLvl), updater,
								CapDate.getCurrentTimestamp(), cntrNo }));
			}
		}

	}

	/**
	 * 上傳 保證人檔 Iquotgur
	 * 
	 * @param l140m01a
	 *            額度明細表
	 * @param updater
	 *            資料更新者
	 * @param dataList
	 *            Sql資料儲存
	 */
	private void uploadIquotgur(L140M01A l140m01a, String updater,
			List<Object[]> dataList, String cntrNo) {
		// 刪除該額度序號存在的 保證人檔IQUOTGUR

		misIquotgurService.delByCntrNo(cntrNo);
		// 姓名長度限制在70
		for (L140M01I l140m01i : l140m01a.getL140m01i()) {
			if (Util.equals(Util.trim(l140m01i.getRType()),
					UtilConstants.lngeFlag.連帶保證人)) {
				// J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式

				dataList.add(new Object[] { cntrNo, l140m01i.getRId(),
						l140m01i.getRDupNo(),
						Util.trimSizeInOS390(l140m01i.getRName(), 70), updater });
			}
		}

		// misIquotgurService.insert(dataList);

	}

	/**
	 * 上傳 共同借款人檔 Iquotjon
	 * 
	 * @param l140m01a
	 *            額度明細表
	 * @param l120s01as
	 *            借款人資料檔
	 * @param updater
	 *            資料更新者
	 * @param dataList
	 *            Sql資料儲存
	 * @param dataList
	 *            batch 上傳
	 * @param cntrNo
	 *            額度序號
	 * @param checkIquotjon
	 *            排除重覆的借款人
	 */
	private void uploadIquotjon(L140M01A l140m01a, List<L120S01A> l120s01as,
			String updater, List<Object[]> dataList, String cntrNo,
			HashMap<String, String> checkIquotjon, L120M01A l120m01a) {

		// 刪除該額度序號存在的 共同借款人檔Iquotjon
		misIquotjonService.delByCntrNo(cntrNo);
		// 姓名長度限制在70
		String key = "";
		for (L120S01A l120s01a : l120s01as) {
			String custId = Util.trim(l120s01a.getCustId());
			String dupNo = Util.trim(l120s01a.getDupNo());
			// 排除主借款人
			if (custId.equals(l120m01a.getCustId())
					&& dupNo.equals(l120m01a.getDupNo())) {

			} else {

				key = cntrNo + custId + dupNo;
				if (!checkIquotjon.containsKey(key)) {
					dataList.add(new Object[] { cntrNo, custId, dupNo,
							Util.trimSizeInOS390(l120s01a.getCustName(), 42),
							updater });
					checkIquotjon.put(key, "");
				}
			}

		}
		// misIquotjonService.insert(dataList);

	}

	/**
	 * 主從債務人檔 ELLNGTEE
	 * 
	 * @param l160m01a
	 *            額度明細表
	 * @param l120s01as
	 *            借款人資料檔
	 * @param updater
	 *            資料更新者
	 * @param upAs400         
	 * 
	 * 
	 */
	private void uploadEllngtee(L160M01A l160m01a, List<L120S01A> l120s01as,
			String updater, boolean upAs400) {
		List<String> cntrNoList = new ArrayList<String>();
		List<Object[]> dataList = new ArrayList<Object[]>();
		// 董監事任期止日
		String releaseDT = "9999-12-31";
		String brNo = "";
		String custId = "";
		String dupNo = "";
		String cntrNo = "";
		String lngeFlag = "";
		// 從債務人統編
		String lngeId = "";
		// 從債務人統編重複序號
		String dupNo1 = "";
		// 從債務人姓名
		String lngeNm = "";
		// 國家別
		String ntCode = "";
		// 與主債務人關係
		String lnGere = "";
		// 更新日
		String upddt = CapDate.formatDate(new Date(),
				UtilConstants.DateFormat.YYYY_MM_DD);
		// 保證人負担保證責任比率
		BigDecimal guaPercent = BigDecimal.ZERO;

		// J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
		BigDecimal priority = null;

		// J-110-0040_05097_B1001 Web e-Loan增加「本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)」註記
		String guaNaExposure = "";
		//G-113-0036 主從債務人新增 擔保限額、當地客戶識別ID
		String localId = "";
		BigDecimal grtAmt = null; 
		String brnId = l160m01a.getOwnBrId();
		for (L162S01A l162m01a : l160m01a.getL162S01A()) {
			cntrNo = l162m01a.getCntrNo();
			brNo = l162m01a.getCntrNo().substring(0, 3);
			custId = l162m01a.getCustId();
			dupNo = l162m01a.getDupNo();
			lngeFlag = l162m01a.getRType();
			lngeId = l162m01a.getRId();
			dupNo1 = l162m01a.getRDupNo();
			ntCode = Util.trim(l162m01a.getRCountry());
			lngeNm = Util.trimSizeInOS390(l162m01a.getRName(), 40);
			if (l162m01a.getCustId().equals(l162m01a.getRId())
					&& l162m01a.getDupNo().equals(l162m01a.getRDupNo())) {
				// 判斷當其關係為空白是要帶X0本人
				lnGere = "X0";
			} else {
				lnGere = l162m01a.getRKindD();
			}
			// 不論共同借款人或是從債務人, 在此統編及額度序號下之所有資料皆刪除
			if (!cntrNoList.contains(cntrNo)) {
				// 先刪除此筆資料後再新增
				misELLNGTEEService.delEllngteeByUniqueKey(brNo, custId, dupNo,
						cntrNo);
				String controlflag = Util.trim(lmsService.getSysParamDataValue("RPS_GTE1000"));
				if (upAs400 &&Util.equals(UtilConstants.DEFAULT.是, controlflag)) {
					obsdbELF401Service.delEllngteeByUniqueKey(brnId, brNo, custId, dupNo,
							cntrNo);
				}
				cntrNoList.add(cntrNo);
			}

			if (!Util.isEmpty(l162m01a.getDueDate())) {
				releaseDT = CapDate.formatDate(l162m01a.getDueDate(),
						UtilConstants.DateFormat.YYYY_MM_DD);
			} else {
				releaseDT = "9999-12-31";
			}

			if (Util.isEmpty(l162m01a.getGuaPercent())) {
				if (Util.equals(l162m01a.getRType(), "S")) {
					guaPercent = BigDecimal.ZERO;
				} else {
					guaPercent = BigDecimal.valueOf(100);
				}

			} else {
				guaPercent = l162m01a.getGuaPercent();
			}

			// J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
			priority = l162m01a.getPriority() == null ? BigDecimal.ZERO
					: l162m01a.getPriority();

			// J-110-0040_05097_B1001 Web
			// e-Loan增加「本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)」註記
			if (Util.notEquals(l162m01a.getRType(), "C")
					&& Util.notEquals(l162m01a.getRType(), "S")) {
				guaNaExposure = Util.trim(l162m01a.getGuaNaExposure());
			} else {
				guaNaExposure = "";
			}

			//G-113-0036 主從債務人新增 擔保限額、當地客戶識別ID
			grtAmt = Util.isEmpty(l162m01a.getGrtAmt()) ? BigDecimal.ZERO
					: l162m01a.getGrtAmt();
			localId = Util.isEmpty(l162m01a.getLocalId()) ? "" : l162m01a.getLocalId();
			
			
			dataList.add(new Object[] { brNo, custId, dupNo, cntrNo, lngeFlag,
					lngeId, dupNo1, lngeNm, ntCode, lnGere, upddt, updater,
					releaseDT, guaPercent, priority, guaNaExposure, grtAmt, localId});
		}
		misELLNGTEEService.insert(dataList);
		String controlflag = Util.trim(lmsService.getSysParamDataValue("RPS_GTE1000"));
		if (upAs400 &&Util.equals(UtilConstants.DEFAULT.是, controlflag)) {
			obsdbELF401Service.insert(brnId, LMSUtil.covertAs400Time(dataList));
		}
	}

	/**
	 * 額度資訊檔 QUOTAINF(AS400 ELF422)
	 * 
	 * @param BRNID
	 *            AS400上傳分行代碼
	 * @param l160m01a
	 *            動審表
	 * @param l140m01a
	 *            額度明細表
	 * @param l120m01a
	 *            案件簽報書
	 * @param updater
	 *            資料更新者
	 * @param dataList
	 *            Sql資料儲存
	 * @param as400List
	 *            as400 Sql資料儲存
	 * @param upAS400
	 *            是否上傳as400
	 * @param cntrNo
	 *            額度序號
	 * 
	 */
	private void uploadQuotainf(String BRNID, L160M01A l160m01a,
			L140M01A l140m01a, L120M01A l120m01a, String updater,
			List<Object[]> dataList, List<Object[]> as400List, boolean upAS400,
			String cntrNo, List<L120M01F> l120m01fs) {
		// 先刪除此筆資料後再新增
		misQuotainfService.delByKey(l160m01a.getCustId(), l160m01a.getDupNo(),
				l160m01a.getOwnBrId(), cntrNo);
		if (upAS400) {
			obsdbELF422Service.delByKey(BRNID, l160m01a.getCustId(),
					l160m01a.getDupNo(), l160m01a.getOwnBrId(), cntrNo);
		}

		String newCase = LMSUtil.isContainValue(l140m01a.getProPerty(),
				UtilConstants.Cntrdoc.Property.新做) ? UtilConstants.DEFAULT.是
				: "";
		String bossId = "";
		for (L120M01F l120m01f : l120m01fs) {
			String staffJob = l120m01f.getStaffJob();
			String staffNo = l120m01f.getStaffNo();
			if (UtilConstants.STAFFJOB.授信主管L3.equals(staffJob)) {
				if (UtilConstants.BRANCHTYPE.分行
						.equals(l120m01f.getBranchType())) {
					bossId = staffNo;
				}
			}

		}
		dataList.add(new Object[] { l160m01a.getCustId(), l160m01a.getDupNo(),
				l160m01a.getOwnBrId(), cntrNo,
				TWNDate.toTW(l120m01a.getCaseDate()),
				TWNDate.toTW(l120m01a.getEndDate()),
				Util.trimSizeInOS390(l120m01a.getCustName(), 40),
				Util.trim(LMSUtil.forFiveBossId(bossId)),
				Util.trim(Util.trimSizeInOS390(this.getUserName(bossId), 20)),
				l120m01a.getMainId(), newCase, updater });
		if (upAS400) {
			as400List.add(new Object[] { l160m01a.getCustId(),
					l160m01a.getDupNo(), l160m01a.getOwnBrId(), cntrNo,
					CapDate.formatDate(l120m01a.getCaseDate(), "yyyyMMdd"),
					CapDate.formatDate(l120m01a.getEndDate(), "yyyyMMdd"),

					Util.trimSizeInOS390(l120m01a.getCustName(), 40),
					LMSUtil.forFiveBossId(bossId),
					Util.trimSizeInOS390(this.getUserName(bossId), 20),
					// 以後用上面那段取核准者姓名
					// Util.trimSizeInOS390("bossName", 20),
					l120m01a.getMainId(), newCase, updater,
					CapDate.getCurrentTimestamp() });
		}

	}

	/**
	 * 聯貸案參貸比率-自行參貸 QUOTUNIO(AS400-ELF385)
	 * 
	 * @param BRNID
	 *            AS400上傳分行代碼
	 * @param l160m01a
	 *            動審表
	 * @param l140m01a
	 *            額度明細表
	 * @param l120m01a
	 *            案件簽報書
	 * @param updater
	 *            資料更新者
	 * @param dataList
	 *            Sql資料儲存
	 * @param as400List
	 *            AS400 Sql資料儲存
	 * @param upAS400
	 *            是否上傳as400
	 * @param cntrno
	 *            額度序號
	 */
	private void uploadQuotunio(String BRNID, L160M01A l160m01a,
			L140M01A l140m01a, L120M01A l120m01a, String updater,
			List<Object[]> dataList, List<Object[]> as400List, boolean upAS400,
			String cntrno, L161S01A l161s01a) {
		// 先刪除該額度序號的檔案
		misQuotunioService.delByCntrNo(cntrno);
		if (upAS400) {
			obsdbELF385Service.delByCntrNoOnly(BRNID, cntrno);
		}

		StringBuffer branchCode = new StringBuffer();
		// List<Object[]> dataList = new ArrayList<Object[]>();
		for (L161S01B l161s01b : l161s01a.getL161s01b()) {

			if (Util.notEquals(l161s01b.getSlBank(), UtilConstants.兆豐銀行代碼)) {
				continue;
			}

			IBranch branchData = branchService
					.getBranch(l161s01b.getSlBranch());
			if (branchData == null) {
				continue;
			}
			// 當為海外分行其前三碼為999
			if (UtilConstants.BrNoType.國外.equals(branchData.getBrNoFlag())) {
				branchCode.append("999").append(
						misdbBASEService.findICBCBRByBrId(l161s01b
								.getSlBranch()));
			} else {
				branchCode.append(UtilConstants.兆豐銀行代碼).append(
						l161s01b.getSlBranch());
			}
			// 2012_07_19_MIS 只有主辦行 可以上,簽報書簽案行 與額度序號前三碼相同時 才可以上傳
			// 2013/07/19,Rex,修改判斷為動審表的分行
			if (l160m01a.getOwnBrId().equals(cntrno.substring(0, 3))) {
				dataList.add(new Object[] {
						UtilConstants.Usedoc.unioType.自行聯貸,
						cntrno,
						branchCode.toString(),
						l140m01a.getOwnBrId().equals(l161s01b.getSlBranch()) ? UtilConstants.DEFAULT.是
								: "",
						Util.parseBigDecimal(l161s01b.getSlAmt()), updater });
			}

			if (upAS400) {
				as400List
						.add(new Object[] {
								UtilConstants.Usedoc.unioType.自行聯貸,
								cntrno,
								branchCode.toString(),
								l140m01a.getOwnBrId().equals(
										l161s01b.getSlBranch()) ? UtilConstants.DEFAULT.是
										: "",
								Util.parseBigDecimal(l161s01b.getSlAmt()),
								updater, CapDate.getCurrentTimestamp() });
			}

			branchCode.setLength(0);
		}
		// misQuotunioService.insert(dataList);

		// // 更新上傳主辦行的聯貸額度上 db2
		// if (total != l140m01a.getCurrentApplyAmt().longValue()) {
		// // Long d = l140m01a.getCurrentApplyAmt().longValue() - total;
		// }

	}

	/**
	 * 聯貸案參貸比率-同業聯貸參貸 QUOTUNIO2(AS400-ELF385)
	 * 
	 * @param BRNID
	 *            AS400上傳分行代碼
	 * @param l160m01a
	 *            動審表
	 * @param l120m01a
	 *            案件簽報書
	 * @param updater
	 *            資料更新者
	 * @param dataList
	 *            Sql資料儲存
	 * @param as400List
	 *            AS400 Sql資料儲存
	 * @param upAS400
	 *            是否上傳as400
	 * 
	 */
	private void uploadQuotunio2(String BRNID, L160M01A l160m01a,
			L120M01A l120m01a, String updater, List<Object[]> dataList,
			List<Object[]> as400List, boolean upAS400) {
		// 2013/07/03,Rex,同業聯貸為Y才需上傳同業聯貸

		Set<L161S01A> l161m01as = l160m01a.getL161S01A();
		for (L161S01A l161m01a : l161m01as) {

			if ("Y".equals(l161m01a.getUnitCase())) {
				String cntrno = Util.trim(l161m01a.getCntrNo());
				if (Util.isNotEmpty(cntrno)) {
					// 先刪除該額度序號的檔案
					misQuotunioService.delByCntrNo(l161m01a.getCntrNo());
					if (upAS400) {
						obsdbELF385Service.delByCntrNoOnly(BRNID,
								l161m01a.getCntrNo());
					}
					String branchCode = "";
					// List<Object[]> dataList = new ArrayList<Object[]>();
					// Long total = Long.valueOf(0);

					Set<L161S01B> l161m01bs = l161m01a.getL161s01b();
					if (l161m01bs != null && !l161m01bs.isEmpty()) {
						for (L161S01B l161m01b : l161m01a.getL161s01b()) {

							if (Util.equals(l161m01b.getSlBank(),
									UtilConstants.兆豐銀行代碼)) {
								continue;
							}

							if (Util.isEmpty(cntrno)) {
								continue;
							}
							branchCode = "";
							if (UtilConstants.兆豐銀行代碼.equals(l161m01b
									.getSlBank())) {
								IBranch branchData = branchService
										.getBranch(l161m01b.getSlBranch());
								// 當為海外分行其前三碼為999
								if (UtilConstants.BrNoType.國外.equals(branchData
										.getBrNoFlag())) {

									branchCode = StrUtils.concat("999",
											misdbBASEService
													.findICBCBRByBrId(l161m01b
															.getSlBranch()));

								} else {
									branchCode = StrUtils.concat(
											UtilConstants.兆豐銀行代碼,
											l161m01b.getSlBranch());

								}
							} else {
								if (Util.isNotEmpty(Util.trim(l161m01b
										.getSlBranch()))) {
									branchCode = StrUtils.concat(l161m01b
											.getSlBank(), l161m01b
											.getSlBranch().substring(3, 6));
								} else {
									branchCode = l161m01b.getSlBank();
								}

							}

							// 12 -國外銀行
							if ("12".equals(l161m01b.getSlBankType())
									|| "99".equals(l161m01b.getSlBankType())) {
								branchCode = l161m01b.getSlBank();

							}

							if (!Util.isEmpty(branchCode)) {
								if (Util.trim(branchCode).length() == 3) {
									branchCode = StrUtils.concat(
											Util.trim(branchCode), "000");
								}
							}

							// 2012_07_19_MIS 只有主辦行 可以上,簽報書簽案行 與額度序號前三碼相同時 才可以上傳
							// 2013/07/19,Rex,修改判斷為動審表的分行
							if (l160m01a.getOwnBrId().equals(
									cntrno.substring(0, 3))) {
								dataList.add(new Object[] {
										UtilConstants.Usedoc.unioType.同業聯貸,
										cntrno,
										branchCode,
										l161m01b.getSlMaster(),
										this.gfnConvertZero(l161m01b.getSlAmt()),
										updater });
							}

							if (upAS400) {
								as400List
										.add(new Object[] {
												UtilConstants.Usedoc.unioType.同業聯貸,
												cntrno,
												branchCode,
												l161m01b.getSlMaster(),
												this.gfnConvertZero(l161m01b
														.getSlAmt()), updater,
												CapDate.getCurrentTimestamp() });
							}

						}
					}

					// misQuotunioService.insert(dataList);

					// // 更新上傳主辦行的聯貸額度上 db2
					// if (total != l140m01a.getCurrentApplyAmt().longValue()) {
					// // Long d = l140m01a.getCurrentApplyAmt().longValue() -
					// total;
					// }

				}
			}
		}

	}

	// /**
	// * 聯貸案參貸比率-自行參貸 QUOTUNIO(AS400-ELF385)
	// *
	// * @param BRNID
	// * AS400上傳分行代碼
	// * @param l160m01a
	// * 動審表
	// * @param l140m01a
	// * 額度明細表
	// * @param l120m01a
	// * 案件簽報書
	// * @param updater
	// * 資料更新者
	// * @param dataList
	// * Sql資料儲存
	// * @param as400List
	// * AS400 Sql資料儲存
	// * @param upAS400
	// * 是否上傳as400
	// * @param cntrno
	// * 額度序號
	// */
	// private void uploadQuotunio(String BRNID, L160M01A l160m01a,
	// L140M01A l140m01a, L120M01A l120m01a, String updater,
	// List<Object[]> dataList, List<Object[]> as400List, boolean upAS400,
	// String cntrno) {
	// // 先刪除該額度序號的檔案
	// misQuotunioService.delByCntrNo(cntrno);
	// if (upAS400) {
	// obsdbELF385Service.delByCntrNoOnly(BRNID, cntrno);
	// }
	//
	// StringBuffer branchCode = new StringBuffer();
	// // List<Object[]> dataList = new ArrayList<Object[]>();
	// for (L140M01E l140m01e : l140m01a.getL140m01e()) {
	// IBranch branchData = branchService.getBranch(l140m01e
	// .getShareBrId());
	// if (branchData == null) {
	// continue;
	// }
	// // 當為海外分行其前三碼為999
	// if (UtilConstants.BrNoType.國外.equals(branchData.getBrNoFlag())) {
	// branchCode.append("999").append(
	// misdbBASEService.findICBCBRByBrId(l140m01e
	// .getShareBrId()));
	// } else {
	// branchCode.append("017").append(l140m01e.getShareBrId());
	// }
	// // 2012_07_19_MIS 只有主辦行 可以上,簽報書簽案行 與額度序號前三碼相同時 才可以上傳
	// // 2013/07/19,Rex,修改判斷為動審表的分行
	// if (l160m01a.getOwnBrId().equals(cntrno.substring(0, 3))) {
	// dataList.add(new Object[] {
	// UtilConstants.Usedoc.unioType.自行聯貸,
	// cntrno,
	// branchCode.toString(),
	// l140m01a.getOwnBrId().equals(l140m01e.getShareBrId()) ?
	// UtilConstants.DEFAULT.是
	// : "",
	// Util.parseBigDecimal(l140m01e.getShareAmt()), updater });
	// }
	//
	// if (upAS400) {
	// as400List
	// .add(new Object[] {
	// UtilConstants.Usedoc.unioType.自行聯貸,
	// cntrno,
	// branchCode.toString(),
	// l140m01a.getOwnBrId().equals(
	// l140m01e.getShareBrId()) ? UtilConstants.DEFAULT.是
	// : "",
	// Util.parseBigDecimal(l140m01e.getShareAmt()),
	// updater, CapDate.getCurrentTimestamp() });
	// }
	//
	// branchCode.setLength(0);
	// }
	// // misQuotunioService.insert(dataList);
	//
	// // // 更新上傳主辦行的聯貸額度上 db2
	// // if (total != l140m01a.getCurrentApplyAmt().longValue()) {
	// // // Long d = l140m01a.getCurrentApplyAmt().longValue() - total;
	// // }
	//
	// }
	//
	// /**
	// * 聯貸案參貸比率-同業聯貸參貸 QUOTUNIO2(AS400-ELF385)
	// *
	// * @param BRNID
	// * AS400上傳分行代碼
	// * @param l160m01a
	// * 動審表
	// * @param l120m01a
	// * 案件簽報書
	// * @param updater
	// * 資料更新者
	// * @param dataList
	// * Sql資料儲存
	// * @param as400List
	// * AS400 Sql資料儲存
	// * @param upAS400
	// * 是否上傳as400
	// *
	// */
	// private void uploadQuotunio2(String BRNID, L160M01A l160m01a,
	// L120M01A l120m01a, String updater, List<Object[]> dataList,
	// List<Object[]> as400List, boolean upAS400) {
	// // 2013/07/03,Rex,同業聯貸為Y才需上傳同業聯貸
	//
	// Set<L161S01A> l161m01as = l160m01a.getL161S01A();
	// for (L161S01A l161m01a : l161m01as) {
	// if ("Y".equals(l161m01a.getUnitCase())) {
	// String cntrno = Util.trim(l161m01a.getCntrNo());
	// if (Util.isNotEmpty(cntrno)) {
	// // 先刪除該額度序號的檔案
	// misQuotunioService.delByCntrNo(l161m01a.getCntrNo());
	// if (upAS400) {
	// obsdbELF385Service.delByCntrNoOnly(BRNID,
	// l161m01a.getCntrNo());
	// }
	// String branchCode = "";
	// // List<Object[]> dataList = new ArrayList<Object[]>();
	// // Long total = Long.valueOf(0);
	//
	// Set<L161S01B> l161m01bs = l161m01a.getL161s01b();
	// if (l161m01bs != null && !l161m01bs.isEmpty()) {
	// for (L161S01B l161m01b : l161m01a.getL161s01b()) {
	// if (Util.isEmpty(cntrno)) {
	// continue;
	// }
	// branchCode = "";
	// if ("017".equals(l161m01b.getSlBank())) {
	// IBranch branchData = branchService
	// .getBranch(l161m01b.getSlBranch());
	// // 當為海外分行其前三碼為999
	// if (UtilConstants.BrNoType.國外.equals(branchData
	// .getBrNoFlag())) {
	//
	// branchCode = StrUtils.concat("999",
	// misdbBASEService
	// .findICBCBRByBrId(l161m01b
	// .getSlBranch()));
	//
	// } else {
	// branchCode = StrUtils.concat("017",
	// l161m01b.getSlBranch());
	//
	// }
	// } else {
	// if (Util.isNotEmpty(Util.trim(l161m01b
	// .getSlBranch()))) {
	// branchCode = StrUtils.concat(l161m01b
	// .getSlBank(), l161m01b
	// .getSlBranch().substring(3, 6));
	// } else {
	// branchCode = l161m01b.getSlBank();
	// }
	//
	// }
	//
	// // 12 -國外銀行
	// if ("12".equals(l161m01b.getSlBankType())
	// || "99".equals(l161m01b.getSlBankType())) {
	// branchCode = l161m01b.getSlBank();
	//
	// }
	//
	// // 2012_07_19_MIS 只有主辦行 可以上,簽報書簽案行 與額度序號前三碼相同時 才可以上傳
	// // 2013/07/19,Rex,修改判斷為動審表的分行
	// if (l160m01a.getOwnBrId().equals(
	// cntrno.substring(0, 3))) {
	// dataList.add(new Object[] {
	// UtilConstants.Usedoc.unioType.同業聯貸,
	// cntrno,
	// branchCode,
	// l161m01b.getSlMaster(),
	// this.gfnConvertZero(l161m01b.getSlAmt()),
	// updater });
	// }
	//
	// if (upAS400) {
	// as400List
	// .add(new Object[] {
	// UtilConstants.Usedoc.unioType.同業聯貸,
	// cntrno,
	// branchCode,
	// l161m01b.getSlMaster(),
	// this.gfnConvertZero(l161m01b
	// .getSlAmt()), updater,
	// CapDate.getCurrentTimestamp() });
	// }
	//
	// }
	// }
	//
	// // misQuotunioService.insert(dataList);
	//
	// // // 更新上傳主辦行的聯貸額度上 db2
	// // if (total != l140m01a.getCurrentApplyAmt().longValue()) {
	// // // Long d = l140m01a.getCurrentApplyAmt().longValue() -
	// // total;
	// // }
	//
	// }
	// }
	// }
	//
	// }

	/**
	 * 授信額度檔 QUOTAPPR(AS400-ELF383)
	 * 
	 * @param BRNID
	 *            AS400上傳分行代碼
	 * @param l160m01a
	 *            動審表
	 * 
	 * @param l140m01a
	 *            額度明細表
	 * @param l120m01a
	 *            案件簽報書
	 * @param l120m01b
	 *            額度種類檔
	 * @param l120s01as
	 *            借款人主檔
	 * @param l120s01cs
	 *            企金信用評等資料檔
	 * @param custDatas
	 *            暫存借款人國別資料
	 * @param updater
	 *            資料更新者
	 * @param sDate
	 *            系統時間
	 * @param upAS400
	 *            是否上傳AS400
	 * @param cntrNo
	 *            額度序號
	 * @param l120s01b
	 *            企金基本資料檔
	 * @param l140m01m
	 *            維護央行購屋/空地/建屋貸款註記資訊
	 */
	@SuppressWarnings("unused")
	private void uploadQuotappr(String BRNID, L160M01A l160m01a,
			L140M01A l140m01a, L120M01A l120m01a, L120M01B l120m01b,
			List<L120S01A> l120s01as, List<L120S01C> l120s01cs,
			HashMap<String, HashMap<String, String>> custDatas, String updater,
			String sDate, Boolean upAS400, String cntrNo, L120S01B l120s01b,
			L140M01M l140m01m, L161S01A l161s01a) throws CapException {

		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1601M01Page.class);

		// 信保案件塞回額度明細表
		// J-109-0077_05097_B1008 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
		if (Util.notEquals(Util.trim(l161s01a.getHeadItem1()), "")) {
			String headItem1 = Util.trim(l161s01a.getHeadItem1());
			BigDecimal gutPercent = l161s01a.getGutPercent();
			Date gutCutDate = l161s01a.getGutCutDate();
			BigDecimal cgfRate = l161s01a.getCgfRate();
			Date cgfDate = l161s01a.getCgfDate();
			String isGuaOldCase = l161s01a.getIsGuaOldCase();
			String byNewOld = l161s01a.getByNewOld();

			l140m01a.setHeadItem1(headItem1);
			l140m01a.setGutPercent(gutPercent);
			l140m01a.setGutCutDate(gutCutDate);
			l140m01a.setCgfRate(cgfRate);
			l140m01a.setCgfDate(cgfDate);
			l140m01a.setIsGuaOldCase(isGuaOldCase);
			l140m01a.setByNewOld(byNewOld);
			lms1401Service.save(l140m01a);
		}

		// J-112-0129_05097_B1001 Web
		// e-Loan系統對於符合單純僅有「交換票據抵用」會計科目之額度序號，不要帶入額度介面檔
		boolean isOnlyNoUploadItem = true;
		for (L140M01C l140m01c : l140m01a.getL140m01c()) {
			String item = l140m01c.getLoanTP();
			if (!this.isNoUploadQuotapprItem(item)) {
				isOnlyNoUploadItem = false;
				break;
			}
		}
		if (isOnlyNoUploadItem) {
			return;
		}

		String mainId = l140m01a.getMainId();
		String custId = l140m01a.getCustId();
		String dupNo = l140m01a.getDupNo();
		String custKey = StrUtils.concat(custId, dupNo);
		// 加入Bf 的判斷
		L140M01A_BF l140m01a_bf = l140m01a_bfDao.findByUniqueKey(mainId);
		L140M01A l140m01aOld = null;
		if (UtilConstants.Cntrdoc.DataSrc.條件續約變更產生
				.equals(l140m01a.getDataSrc())) {
			l140m01aOld = l140m01aDao.findByMainId(l140m01a.getMainIdSrc());
		}

		HashMap<String, String> newList = LMSUtil.getItemList(l140m01a);

		// 變更前項目
		HashMap<String, String> oldList = null;
		String LNSubject_BF = "";
		if (l140m01a_bf != null) {
			List<L140M01C_BF> l140m01c_bfs = l140m01c_bfDao
					.findByMainId(mainId);
			List<L140M01D_BF> l140m01d_bfs = l140m01d_bfDao
					.findByMainId(mainId);
			oldList = LMSUtil.getItemListByBf(l140m01a_bf, l140m01c_bfs,
					l140m01d_bfs);
		} else {
			oldList = LMSUtil.getItemList(l140m01aOld);

		}

		if (l140m01aOld == null) {
			l140m01aOld = new L140M01A();
		}
		// 性質是否變動
		String lnFlag = "N";
		String isSpCase = ""; // 若為續約、展期、提前續約者，此值為Y
		String isSQCase = ""; // 若為紓困者，此值為Y
		HashMap<String, String> tempMap = new HashMap<String, String>();
		tempMap.put(UtilConstants.Cntrdoc.Property.新做,
				UtilConstants.Usedoc.upType.新作);
		tempMap.put(UtilConstants.Cntrdoc.Property.增額,
				UtilConstants.Usedoc.upType.增額);
		tempMap.put(UtilConstants.Cntrdoc.Property.減額,
				UtilConstants.Usedoc.upType.減額);
		tempMap.put(UtilConstants.Cntrdoc.Property.取消,
				UtilConstants.Usedoc.upType.取消);
		String tProperty = "";
		int property_count = 0;

		for (String f : l140m01a.getProPerty().split(
				UtilConstants.Mark.SPILT_MARK)) {
			// 之後在判斷是否為純續約, 展期, 提前續約用
			property_count += 1;
			if (tempMap.containsKey(f)) {
				tProperty = tempMap.get(f);
				break;
			}
			// 2.續約 9.展期(不良授信案) 11.提前續約
			if (UtilConstants.Cntrdoc.Property.續約.equals(f)
					|| UtilConstants.Cntrdoc.Property.展期.equals(f)
					|| UtilConstants.Cntrdoc.Property.提前續約者.equals(f)) {
				isSpCase = UtilConstants.DEFAULT.是;
			}

			if (UtilConstants.Cntrdoc.Property.紓困.equals(f)) {
				isSQCase = UtilConstants.DEFAULT.是;
			}
		}
		// 若為紓困則要檢核其為增額或減額
		BigDecimal currentApplyAmt_bf = l140m01aOld.getCurrentApplyAmt();
		String currentApplyCurr_bf = l140m01aOld.getCurrentApplyCurr();
		String headItem1_bf = l140m01aOld.getHeadItem1();
		String reUse_bf = l140m01aOld.getReUse();
		BigDecimal gutPercent_bf = l140m01aOld.getGutPercent();
		// OLDAMT XXXXXXXXXXXXXXX
		if (l140m01a_bf != null) {
			currentApplyAmt_bf = l140m01a_bf.getCurrentApply_BF();
			currentApplyCurr_bf = l140m01a_bf.getAPLCurr_BF();
			gutPercent_bf = l140m01a_bf.getGutPercent_BF();
			headItem1_bf = l140m01a_bf.getHeadItem1_BF();
			reUse_bf = l140m01a_bf.getReUse_BF();
		}
		if (UtilConstants.DEFAULT.是.equals(isSQCase)) {

			// 幣別一樣才做比較
			if (Util.trim(currentApplyCurr_bf).equals(
					Util.trim(l161s01a.getCurrentApplyCurr()))) {

				if (this.gfnConvertZero(l161s01a.getCurrentApplyAmt())
						.compareTo(this.gfnConvertZero(currentApplyAmt_bf)) == 1) {

					tProperty = UtilConstants.Usedoc.upType.增額;
				} else if (this.gfnConvertZero(l161s01a.getCurrentApplyAmt())
						.compareTo(this.gfnConvertZero(currentApplyAmt_bf)) == -1) {
					tProperty = UtilConstants.Usedoc.upType.減額;
				}
			}

		}

		lnFlag = tProperty;
		if (LMSUtil.isContainValue(l140m01a.getProPerty(),
				UtilConstants.Cntrdoc.Property.不變)) {
			lnFlag = "X";
		}

		if (Util.equals(l161s01a.getUseSpecialReason(), "01")) {
			// 特殊修改-01.不動用，僅修改聯貸比率
			lnFlag = "U";
		}

		if (Util.equals(l161s01a.getUseSpecialReason(), "02")) {
			// 特殊修改-02.不動用，僅修改振興經濟非中小企業專案貸款
			lnFlag = "X";
		}

		// 是否為純續約、展期、提前續約, 是者為Y
		String ChangeFlag1 = UtilConstants.DEFAULT.否;
		// 送保/不送保是否有異動
		String ChangeFlag2 = UtilConstants.DEFAULT.否;
		// 循環/不循環是否有異動
		String ChangeFlag3 = UtilConstants.DEFAULT.否;
		// 是否總額度不變但限額改變
		String ChangeFlag4 = UtilConstants.DEFAULT.否;
		// 保證成數是否變更
		String ChangeFlag5 = UtilConstants.DEFAULT.否;
		// 授信期間終止日是否變更
		String ChangeFlag6 = UtilConstants.DEFAULT.否;
		// 動用期間終止日是否變更
		String ChangeFlag7 = UtilConstants.DEFAULT.否;
		int ItemChange = 0;
		// --- (1) 檢查是否為 純續約、展期、提前續約 案件, 若是需則上傳到期日 ---
		if (UtilConstants.DEFAULT.是.equals(isSpCase) && property_count == 1) {
			ChangeFlag1 = UtilConstants.DEFAULT.是;
		} else {
			ItemChange += 1;
		}
		// --- (2) 檢查送保/不送保是否有異動 , 若是需上傳 ---

		if (Util.isNotEmpty(headItem1_bf)) {
			if (!l140m01a.getHeadItem1().equals(headItem1_bf)) {
				ChangeFlag2 = UtilConstants.DEFAULT.是;
			} else {
				ItemChange += 1;
			}
		}
		// ' --- (3) 檢查循環/不循環是否有異動 , 若是需上傳 ---

		if (Util.isNotEmpty(reUse_bf)) {
			if (!l140m01a.getReUse().equals(reUse_bf)) {
				ChangeFlag3 = UtilConstants.DEFAULT.是;
			} else {
				ItemChange += 1;
			}
		}

		/**
		 * <pre>
		 * 
		 *   ' --- (4) 檢查是否總額度不變但限額改變 , 若是需上傳 ---  
		 *         If NRdoc.LNSubject_BF(0) = "" Then
		 *                 '--- 因在e-Loan可能會直接打續約或變更案件，但在XXX_BF中並沒有值(XX_BF沒有值表示該科目在e-Loan並沒有經過新增時的階段, 所有一律上傳)
		 *                 ChangeFlag(4) = ""
		 *                 ItemChange = ItemChange +1
		 *         Else
		 *                 If gfnCkSubQuotaChang(NRdoc,BFSnoList,NewSnoList) Then
		 *                         ChangeFlag(4) = "Y"  
		 *                         ItemChange = ItemChange +1
		 *                 End If
		 *         End If
		 * 
		 * 
		 * </pre>
		 */

		// --- (4) 檢查是否總額度不變但限額改變 , 若是需上傳
		if (oldList.isEmpty()) {
			if (!l140m01a.getReUse().equals(reUse_bf)) {
				ChangeFlag4 = "";
				ItemChange += 1;
			}
		} else {
			if (this.gfnCkSubQuotaChang(newList, oldList)) {
				ChangeFlag4 = UtilConstants.DEFAULT.是;
				ItemChange += 1;
			}

		}
		// --- (5) 檢查保證成數是否變更 , 若是需上傳 ---

		if (Util.isNotEmpty(headItem1_bf) || Util.isNotEmpty(gutPercent_bf)) {
			if (!l140m01a.getHeadItem1().equals(headItem1_bf)
					|| this.gfnConvertZero(l140m01a.getGutPercent()).compareTo(
							this.gfnConvertZero(gutPercent_bf)) != 0) {
				ChangeFlag5 = UtilConstants.DEFAULT.是;
				ItemChange += 1;
			}
		}

		// 循環/不循環是否變更
		String reclChg = "N"; // 新案2015.02改為 循環/不循環 ，A. 循環、B. 不循環
		// 送保註記是否變更
		String sguChg = "N";
		// 信保保證成數是否變更
		String gutFlag = "N";

		// OBU公司存續證明到期日
		String existdate = l140m01a.getTypCd().equals(TypCdEnum.OBU.getCode()) ? this
				.parseDate(custDatas.get(custKey).get("existDate"))
				: "0001-01-01";
		if (Util.equals(existdate, "")) {
			existdate = "0001-01-01";
		}

		// OBU公司繳交年費證明到期日
		String feedate = l140m01a.getTypCd().equals(TypCdEnum.OBU.getCode()) ? this
				.parseDate(custDatas.get(custKey).get("feeDate"))
				: "0001-01-01";
		if (Util.equals(feedate, "")) {
			feedate = "0001-01-01";
		}
		// OBU公司註冊國有效期
		String countrydt = l140m01a.getTypCd().equals(TypCdEnum.OBU.getCode()) ? this
				.parseDate(custDatas.get(custKey).get("countryDate"))
				: "0001-01-01";
		if (Util.equals(countrydt, "")) {
			countrydt = "0001-01-01";
		}

		// 本案是否有同業聯貸案額度
		String tmpUnitCase = "";
		String tmpMainBranch = "";
		String caseType = "";

		// Map<String, String> caseMap = lmsService.getCaseType("2",
		// l120m01b.getMainId(), l120m01b, l140m01a);
		// tmpUnitCase = caseMap.get("tmpUnitCase");
		// tmpMainBranch = caseMap.get("tmpMainBranch");
		// caseType = caseMap.get("caseType");

		Map<String, String> caseMap = lmsService.getCaseType("2", l161s01a,
				l140m01a);

		tmpUnitCase = l161s01a.getUnitCase();
		tmpMainBranch = l161s01a.getUCntBranch();
		caseType = l161s01a.getCaseType();

		// 加強檢核動審表同業參貸比例有沒有勾兆豐為主辦行，如果有且簽報書勾非主辦行，則改上傳QUOTAPPR時由參貸改為主辦
		// List<L161S01B> l161m01bs = l161m01bDao.findByMainId(l160m01a
		// .getMainId());

		Set<L161S01B> l161m01bs = l161s01a.getL161s01b();

		String hasMainFlag = "N";
		String slBankType = "";
		String slBank = "";
		String slMaster = "";
		for (L161S01B l161m01b : l161m01bs) {
			slBankType = l161m01b.getSlBankType();
			slBank = l161m01b.getSlBank();
			slMaster = l161m01b.getSlMaster();
			if (Util.equals(slBankType, "01")
					&& Util.equals(slBank, UtilConstants.兆豐銀行代碼)) {
				if (Util.equals(slMaster, "Y")) {
					hasMainFlag = "Y";
					break;
				}
			}
		}
		if (Util.equals(hasMainFlag, "Y")) {
			if (Util.equals(caseType, UtilConstants.Usedoc.caseType.同業聯貸參貸)) {
				caseType = UtilConstants.Usedoc.caseType.同業聯貸主辦;
			} else if (Util.equals(caseType,
					UtilConstants.Usedoc.caseType.同業聯貸參貸含自行聯貸)) {
				caseType = UtilConstants.Usedoc.caseType.同業聯貸主辦含自行聯貸;
			}
		}

		// 合作業務種類
		String coKind = "";
		// 合作業務母戶額度序號
		String cntrnom = "";
		// 合作業務子戶代收帳號
		String rclno = "";
		if (l161s01a != null && !"0".equals(l161s01a.getCoKind())
				&& !Util.isEmpty(l161s01a.getCoKind())) {
			if (UtilConstants.DEFAULT.是.equals(l161s01a.getMCntrt())) {

				// caseType = UtilConstants.Usedoc.caseType.合作業務母戶;
			} else if (UtilConstants.DEFAULT.是.equals(l161s01a.getSCntrt())) {
				// caseType = UtilConstants.Usedoc.caseType.合作業務子戶;
				cntrnom = l161s01a.getMScntrt();
				rclno = Util.trimSizeInOS390(l161s01a.getMSAcc(), 14);
			} else {
				cntrnom = "";
				rclno = "";
			}
			coKind = l161s01a.getCoKind();
		}
		// 原請額度
		BigDecimal oldAmt = this.gfnConvertZero(currentApplyAmt_bf);
		// 現請額度
		BigDecimal curAmt = this.gfnConvertZero(l161s01a.getCurrentApplyAmt());
		// 現請額度幣別
		String curCurr = l161s01a.getCurrentApplyCurr();
		String oldCurr = Util.trim(currentApplyCurr_bf);

		/** ---------判斷科(子)目限額是否變更 --------------------------- */
		// 科(子)目限額是否變更
		String lnqtFlag = ChangeFlag4;
		/** ---判斷循環/不循環是否有異動 ------- */
		String reclFlag = "";
		String sguFlag = "";

		if (UtilConstants.Usedoc.upType.新作.equals(tProperty)) {
			if (UtilConstants.Cntrdoc.ReUse.循環使用.equals(l140m01a.getReUse())) {
				// 新作循環額度
				reclFlag = "R";
			} else {
				// 新作不循環額度
				reclFlag = "N";
			}

		} else {
			reclChg = ChangeFlag3;
			if (UtilConstants.DEFAULT.是.equals(reclChg)) {
				String reuse = Util.trim(l140m01a.getReUse());
				String reuseBF = Util.trim(reUse_bf);
				if (UtilConstants.Cntrdoc.ReUse.循環使用.equals(reuse)
						&& UtilConstants.Cntrdoc.ReUse.不循環使用.equals(reuseBF)) {
					reclFlag = "C";
				}
				if (UtilConstants.Cntrdoc.ReUse.不循環使用.equals(reuse)
						&& UtilConstants.Cntrdoc.ReUse.循環使用.equals(reuseBF)) {
					reclFlag = "F";
				}
			}

		}

		// 2015-02-10 與曉曉討論循環/不循環是否變更改為直接帶循環/不循環就好
		if (Util.equals(Util.trim(l140m01a.getReUse()), "1")) {
			// 不循環
			reclChg = "B";
		} else if (Util.equals(Util.trim(l140m01a.getReUse()), "2")) {
			// 循環
			reclChg = "A";
		} else {
			// 維持舊案Y/N不變
		}

		/** -----判斷送保註記 --------------- */
		String headItem1 = Util.trim(l140m01a.getHeadItem1());
		String headItem1BF = Util.trim(headItem1_bf);

		// J-105-0203-002 配合a-Loan調整信保資料上傳判斷
		// 32 送保註記是否變更 SGUCHG CHAR(01) Y.是、N.否
		// =>改A.送保　B.不送保
		// 33 信保保證成數是否變更 GUTFLAG CHAR(01) Y.是、N.否
		// =>主機取消判斷
		// 34 信保保證成數 GUTPER DECIMAL(03) 一律寫入，不判斷是否有更改

		BigDecimal gutper = BigDecimal.ZERO;
		if (Util.equals(lmsService.getSysParamDataValue("LMS_J1050203002_ON"),
				"Y")) {
			if (UtilConstants.DEFAULT.是.equals(headItem1)) {
				// 送保
				sguChg = "A";
				gutFlag = "Y";
				gutper = this.gfnConvertZero(l140m01a.getGutPercent());
				if (UtilConstants.Usedoc.upType.新作.equals(tProperty)) {
					sguFlag = "G";
				} else {
					sguFlag = "";
				}
			} else {
				// 不送保
				sguChg = "B";
				gutFlag = "";
				gutper = BigDecimal.ZERO;
				if (UtilConstants.Usedoc.upType.新作.equals(tProperty)) {
					sguFlag = "N";
				} else {
					sguFlag = "R";
				}
			}
		} else {
			// 信保成數
			gutper = this.gfnConvertZero(l140m01a.getGutPercent());
			if (UtilConstants.Usedoc.upType.新作.equals(tProperty)) {
				if (UtilConstants.DEFAULT.是.equals(headItem1)) {
					sguFlag = "G";
				} else {
					sguFlag = "N";
				}
			} else {
				sguChg = ChangeFlag2;
				if (UtilConstants.DEFAULT.是.equals(sguChg)) {
					if (UtilConstants.DEFAULT.否.equals(headItem1)
							&& UtilConstants.DEFAULT.是.equals(headItem1BF)) {
						sguFlag = "R";
					}
				}
			}
			/** -------判斷信保成數是否變更 ------- */
			if (!UtilConstants.Usedoc.upType.新作.equals(tProperty)) {
				if (UtilConstants.DEFAULT.是.equals(headItem1)) {
					gutFlag = ChangeFlag5;
				}
			}
		}

		// J-112-0366_12473_B1001 批次保證時,保證成數固定100
		if ("3".equals(Util.trim(l140m01a.getGutType()))) {
			gutper = new BigDecimal(100);
		}

		// J-110-0540_05097_B1001 Web e-Loan企金授信配合調整E-loan系統動用審核表部分內容
		/** ------------------契約書型態------------------ */
		String New_LNRTYPE = "";
		/** ---------授信期間 ------------ */
		String llnNo = "";
		String llnfDate = "0001-01-01";
		String llneDate = "0001-01-01";
		int llnmon = 0;
		/** -------------動用期限------------- */
		String lnuseNo = "";
		String useFmDt = "0001-01-01";
		String useEnDt = "0001-01-01";
		int useFtMn = 0;

		if (Util.notEquals(Util.trim(l160m01a.getTType()), "3")) {

			New_LNRTYPE = Util.trim(l160m01a.getTType());
			llnNo = Util.trim(l160m01a.getLnSelect());
			lnuseNo = Util.trim(l160m01a.getUseSelect());
			/** ---------授信期間 ------------ */
			if (UtilConstants.Usedoc.TType.中長期.equals(New_LNRTYPE)) {
				llnNo = Util.trim(l160m01a.getLnSelect());
				if ("1".equals(llnNo)) {// 授信期間為起迄日期
					llnfDate = CapDate.formatDate(l160m01a.getLnFromDate(),
							UtilConstants.DateFormat.YYYY_MM_DD);
					llneDate = CapDate.formatDate(l160m01a.getLnEndDate(),
							UtilConstants.DateFormat.YYYY_MM_DD);
				} else if ("2".equals(llnNo)) {// 授信期間」自首動日起XX年XX月
					llnmon = l160m01a.getLnYear() * 12 + l160m01a.getLnMonth();
				} else {
					// 「授信期間」的其他 都帶預設值就好所以不做處理

				}
			} else {
				llnNo = "";
			}

			/** -------------動用期限------------- */
			if ("1".equals(lnuseNo)) {
				if (Util.isNotEmpty((l160m01a.getUseFromDate()))) {
					useFmDt = CapDate.formatDate(l160m01a.getUseFromDate(),
							UtilConstants.DateFormat.YYYY_MM_DD);
				}
				if (Util.isNotEmpty((l160m01a.getUseEndDate()))) {
					useEnDt = CapDate.formatDate(l160m01a.getUseEndDate(),
							UtilConstants.DateFormat.YYYY_MM_DD);
				}
			} else if ("2".equals(lnuseNo)) {
				useFtMn = l160m01a.getUseMonth();
			}
			// 若為取消則將動用終止日設為0001-01-01
			if (UtilConstants.Usedoc.upType.取消.equals(tProperty)) {
				useEnDt = "0001-01-01";
			}

		} else {
			New_LNRTYPE = Util.trim(l161s01a.getTType_s01a());
			llnNo = Util.trim(l161s01a.getLnSelect_s01a());
			lnuseNo = Util.trim(l161s01a.getUseSelect_s01a());
			/** ---------授信期間 ------------ */
			if (UtilConstants.Usedoc.TType.中長期.equals(New_LNRTYPE)) {
				llnNo = Util.trim(l161s01a.getLnSelect_s01a());
				if ("1".equals(llnNo)) {// 授信期間為起迄日期
					llnfDate = CapDate.formatDate(
							l161s01a.getLnFromDate_s01a(),
							UtilConstants.DateFormat.YYYY_MM_DD);
					llneDate = CapDate.formatDate(l161s01a.getLnEndDate_s01a(),
							UtilConstants.DateFormat.YYYY_MM_DD);
				} else if ("2".equals(llnNo)) {// 授信期間」自首動日起XX年XX月
					llnmon = l161s01a.getLnYear_s01a() * 12
							+ l161s01a.getLnMonth_s01a();
				} else {
					// 「授信期間」的其他 都帶預設值就好所以不做處理

				}
			} else {
				llnNo = "";
			}

			/** -------------動用期限------------- */
			if ("1".equals(lnuseNo)) {
				if (Util.isNotEmpty((l161s01a.getUseFromDate_s01a()))) {
					useFmDt = CapDate.formatDate(
							l161s01a.getUseFromDate_s01a(),
							UtilConstants.DateFormat.YYYY_MM_DD);
				}
				if (Util.isNotEmpty((l161s01a.getUseEndDate_s01a()))) {
					useEnDt = CapDate.formatDate(l161s01a.getUseEndDate_s01a(),
							UtilConstants.DateFormat.YYYY_MM_DD);
				}
			} else if ("2".equals(lnuseNo)) {
				useFtMn = l161s01a.getUseMonth_s01a();
			}
			// 若為取消則將動用終止日設為0001-01-01
			if (UtilConstants.Usedoc.upType.取消.equals(tProperty)) {
				useEnDt = "0001-01-01";
			}
		}

		// 授信期間－終止日期是否變更
		String llneFlag = "N";
		// 動用期限－終止日期是否變更
		String useeFlag = "N";

		// ' 授信契約書型態
		String BF_LRPTYPE = "";
		// ' 中長期授信期間代碼
		String BF_LLNNO = "";
		// 中長期授信期間（授信期間－終止日期）
		String BF_LLNEDATE = "";
		// ' 授信期間－月
		int BF_LLNMON = 0;
		// ' 動用期限代碼
		String BF_LNUSENO = "";
		// ' 動用期限（動用期限－終止日期）
		String BF_USEENDT = "";
		// 動用期限－自首次動用日起Ｘ個月
		int BF_USEFTMN = 0;
		// Map<String, Object> quotapp = misQuotapprService.findByKey(custId,
		// dupNo, cntrNo, sDate);
		// 2013-05-24_多加查詢條件 onlntime is not null or bthtime is not null
		Map<String, Object> quotapp = misQuotapprService.findByKey2(custId,
				dupNo, cntrNo);
		if (quotapp != null) {
			// 授信契約書型態
			BF_LRPTYPE = Util.trim(quotapp.get("LRPTYPE"));
			//
			BF_LLNNO = Util.trim(quotapp.get("LLNNO"));
			BF_LLNEDATE = Util.trim(quotapp.get("LLNEDATE"));
			BF_LLNMON = Util.parseInt(Util.trim(quotapp.get("LLNMON")));
			BF_LNUSENO = Util.trim(quotapp.get("LNUSENO"));
			BF_USEENDT = Util.trim(quotapp.get("USEENDT"));
			BF_USEFTMN = Util.parseInt(Util.trim(quotapp.get("USEFTMN")));

			// --- (6) 檢查授信期間終止日是否變更 , 若是需上傳 ---
			// 判斷授信期間是否變動
			if ("2".equals(New_LNRTYPE)) {
				// 授信契約書型態不符
				if (Util.notEquals(BF_LRPTYPE, New_LNRTYPE)) {
					// 型態不符當為中長期時才要寫這個Flag
					llneFlag = "Y";
				} else {
					// 中長期要判斷授信期間
					if (Util.notEquals(BF_LLNNO, llnNo)) {
						llneFlag = "Y";
					} else {
						// 授信期間代碼相符時，判斷終止日期 OR 月份是否相符
						if ("1".equals(llnNo)) {
							if (Util.notEquals(useEnDt, BF_USEENDT)) {
								llneFlag = "Y";
							}
						} else if ("2".equals(llnNo)) {
							if (llnmon != BF_LLNMON) {
								llneFlag = "Y";
							}
						}

					}
				}
			}
			// (7) 檢查動用期間終止日是否變更 , 若是需上傳
			// 判斷動用期間是否變動
			if (Util.notEquals(BF_LNUSENO, lnuseNo)) {
				useeFlag = "Y";
			} else {
				if ("1".equals(lnuseNo)) {
					if (Util.notEquals(BF_USEENDT, useEnDt)) {
						useeFlag = "Y";
					}
				} else if ("2".equals(lnuseNo)) {
					if (BF_USEFTMN != useFtMn) {
						useeFlag = "Y";
					}
				}
			}
		}

		// 其他敘作條件動撥提示用語
		String memo = "";
		// 承諾事項
		String promise = "";
		// J-113-0035 承諾事項區分新舊版(以簽案createdate比較)
		String chkDate = Util.trim(lmsService.getSysParamDataValue("J-113-0035_CHKDATE"));
		Date createdate = l140m01a.getCreateTime();
		Date checkdate = CapDate.getDate(chkDate, UtilConstants.DateFormat.YYYY_MM_DD);
		boolean isnewpromise = false;
		if(createdate.after(checkdate)){
			isnewpromise = true;
		}
		
		String reViewDateKind = null;
		String reViewDate = null;
		String reViewChgKind = null;
		BigDecimal reViewChg1 = null;

		reViewDateKind = "";
		reViewDate = "0001-01-01";
		reViewChgKind = "";
		reViewChg1 = BigDecimal.ZERO;

		//J-113-0035 承諾事項區分新舊版
		if(isnewpromise){//新
			List<L140S12A> l140s12as = (List<L140S12A>) findListByMainId(L140S12A.class, mainId);
			if(!l140s12as.isEmpty()){
				Map<String, String> l140s12a_esgType = codeTypeService.findByCodeType(
						"l140s12a_esgType", LMSUtil.getLocale().toString());
				StringBuilder newmemo = new StringBuilder();//Util.trimSizeInOS390(l140m01b.getToALoan(), 60);
				StringBuilder newpromise = new StringBuilder();
				for (L140S12A l140s12a : l140s12as) {
					newpromise.append(l140s12a.getContentText());
					
					if(Util.isNotEmpty(l140s12a.getEsgType())){
						String[] esgtype = l140s12a.getEsgType().split(UtilConstants.Mark.SPILT_MARK);// |
						for(String detal : esgtype){
							String detailmsg = MapUtils.getString(l140s12a_esgType, Util.trim(detal), "");
							if(newmemo.length() > 0){
								newmemo.append("、");
							}
							newmemo.append(detailmsg);
						}
					}
					
				}
				if(newmemo.length() > 60){
					//L140S12A.message02=有關注意事項內容因字數限制，請至貸後管理平台查明。
					memo = Util.trimSizeInOS390(prop.getProperty("L140S12A.message02"), 60);
				}else{
					memo = Util.trimSizeInOS390(newmemo.toString().replaceAll("\r\n", " ").replaceAll("\r", " ").replaceAll("\n", " "), 60);
				}
				if(newpromise.length() > 300){
					//L140S12A.message01=有關承諾事項內容因字數限制，請至貸後管理平台查明。
					promise = Util.trimSizeInOS390(prop.getProperty("L140S12A.message01"), 750);
				}else{
					//換行符號會讓DW那邊吃有問題，需replace
					promise = Util.trimSizeInOS390(newpromise.toString().replaceAll("\r\n", " ").replaceAll("\r", " ").replaceAll("\n", " "), 750);
				}
				
			}
		}else{//舊
			L161S01C l161s01c = l161s01cDao.findByMainIdPidItemType(
					l161s01a.getMainId(), l161s01a.getUid(),
					UtilConstants.Cntrdoc.l140m01bItemType.其他敘做條件_動撥提示用語);

			if (l161s01c == null) {
				for (L140M01B l140m01b : l140m01a.getL140m01b()) {
					if (UtilConstants.Cntrdoc.l140m01bItemType.其他敘做條件_動撥提示用語
							.equals(l140m01b.getItemType())) {
						memo = Util.trimSizeInOS390(l140m01b.getToALoan(), 60);
						promise = Util.trimSizeInOS390(l140m01b.getItemDscr(), 750);
						break;
					}
				}
			} else {

				reViewDateKind = l161s01a.getReViewDateKind();
				if (Util.equals(reViewDateKind, "01")) {
					reViewDate = CapDate.formatDate(l161s01a.getReViewDate(),
							UtilConstants.DateFormat.YYYY_MM_DD);
					reViewChgKind = l161s01a.getReViewChgKind();
					if (Util.equals(reViewChgKind, "01")) {
						reViewChg1 = l161s01a.getReViewChg1();
					}
				}

				memo = Util.trimSizeInOS390(l161s01c.getToALoan(), 60);
				promise = Util.trimSizeInOS390(l161s01c.getItemDscr(), 750);
			}
		}

		/** ------授權等級 ------------ */
		String grantNo = "";

		if (UtilConstants.Casedoc.DocKind.授權內.equals(l120m01a.getDocKind())) {
			String authlvl = Util.trim(l120m01a.getAuthLvl());
			if (UtilConstants.Casedoc.AuthLvl.分行授權內.equals(authlvl)
					|| UtilConstants.Casedoc.AuthLvl.總行授權內.equals(authlvl)) {
				grantNo = "9";
			} else {
				grantNo = "B";

			}

		} else if (UtilConstants.Casedoc.DocKind.授權外.equals(l120m01a
				.getDocKind())) {
			grantNo = Util.trim(l120m01a.getCaseLvl());
		} else {
			logger.info("缺少DocKind 欄位值目前值為======>" + l120m01a.getDocKind());
		}

		// String commborw = "";
		// if (l120s01as.size() > 1) {
		// commborw = UtilConstants.DEFAULT.是;
		// }
		//
		String commborw = "";
		for (L162S01A l162s01a : l160m01a.getL162S01A()) {
			// 檢查動審表主從債務人資料表，若有共同借款人、相同額度序號、不同關係人ID，則代表有共同借款人
			if (Util.equals(l162s01a.getRType(), UtilConstants.lngeFlag.共同借款人)
					&& Util.equals(cntrNo, l162s01a.getCntrNo())
					&& !(l162s01a.getCustId().equals(l162s01a.getRId()) && l162s01a
							.getDupNo().equals(l162s01a.getRDupNo()))

			) {
				commborw = UtilConstants.DEFAULT.是;
				break;
			}
		}

		String unichgFlag = "";
		/**
		 * <pre>
		 * static final String 一般貸款 = 6
		 * static final String 合作業務母戶 = 7
		 * static final String 合作業務子戶 =8
		 * </pre>
		 */
		if (!UtilConstants.Usedoc.caseType.一般貸款.equals(caseType)
				&& !UtilConstants.Usedoc.caseType.合作業務母戶.equals(caseType)
				&& !UtilConstants.Usedoc.caseType.合作業務子戶.equals(caseType)) {
			unichgFlag = UtilConstants.DEFAULT.是;
		} else {
			unichgFlag = UtilConstants.DEFAULT.否;
		}

		// 聯貸額度(包含同業) 改依案件性質來判斷
		BigDecimal unionAmt = BigDecimal.ZERO;
		// if (cntrNo.equals(l160m01a.getL161S01A().getCntrNo())) {
		// if (UtilConstants.DEFAULT.是.equals(l161s01a.())) {
		// unionAmt = this.gfnConvertZero(l161s01a.getQuotaAmt());
		// } else {
		// unionAmt = this.gfnConvertZero(l161s01a.getCurrentApplyAmt());
		// }
		// }

		/*
		 * [下午 01:54:06] 曉曉: * 若 CASETYPE='2'( 同業主辦含聯行參貸 ) 若 UNLNTYPE='1'
		 * 同業參貸明細中有本行參貸明細 2 筆以上時，則 下傳全部的同業參貸明細，若否，則排除同業參貸明細本行部份，
		 * 其他同業皆下傳，並下傳聯行參貸明細
		 */
		if (Util.equals(caseType, UtilConstants.Usedoc.caseType.同業聯貸主辦)
				|| Util.equals(caseType,
						UtilConstants.Usedoc.caseType.同業聯貸主辦含自行聯貸)) {
			unionAmt = this.gfnConvertZero(l161s01a.getQuotaAmt()).setScale(0,
					BigDecimal.ROUND_HALF_UP);
			curAmt = this.gfnConvertZero(l161s01a.getQuotaAmt());
			curCurr = l161s01a.getQuotaCurr();
		} else {
			unionAmt = this.gfnConvertZero(l161s01a.getCurrentApplyAmt())
					.setScale(0, BigDecimal.ROUND_HALF_UP);
		}

		// 2012_05_23_建霖說 CURAMT 欄位上傳為以下規則
		// If DBCURAMT < DBUNIONAMT Then
		// DBCURAMT=gfnConvertZero(tdoc1.LoanTotamt(0))
		// End If

		// if (l161s01a != null) {
		// // 當現請額度小於聯貸額度 ，且 額度序號 相同
		// if (curAmt.compareTo(unionAmt) == -1
		// && l161s01a.getCntrNo().equals(cntrNo)) {
		// curAmt = unionAmt;
		// curCurr = l161s01a.getQuotaCurr();
		// }
		// }

		// 本行額度=現請額度
		BigDecimal shareAmt = this
				.gfnConvertZero(l161s01a.getCurrentApplyAmt()).setScale(0,
						BigDecimal.ROUND_HALF_UP);

		// 報核方式
		String permitType = "";

		if (UtilConstants.BankNo.資訊處.equals(MegaSSOSecurityContext.getUnitNo())
				|| UtilConstants.BankNo.國金部.equals(MegaSSOSecurityContext
						.getUnitNo())) {
			if (l161s01a != null) {
				permitType = StrUtils.concat(Util.trim(l161s01a.getURP1()),
						Util.trim(l161s01a.getURP2()),
						Util.trim(l161s01a.getURP3()));
				if ("AAA".equals(permitType)) {
					permitType = "1";
				} else if ("AAB".equals(permitType)) {
					permitType = "2";
				} else if ("ABA".equals(permitType)) {
					permitType = "3";
				} else if ("ABB".equals(permitType)) {
					permitType = "4";
				} else if ("BAA".equals(permitType)) {
					permitType = "5";
				} else if ("BAB".equals(permitType)) {
					permitType = "6";
				} else if ("BBA".equals(permitType)) {
					permitType = "7";
				} else if ("BBB".equals(permitType)) {
					permitType = "8";
				} else {
					permitType = "";
				}

			}

		}
		String hideunion = "";
		String UArea = "";
		String unionRole = "";

		if (Util.equals(l161s01a.getUnitCase(), "Y")) {
			if (l161s01a != null) {
				hideunion = Util.trim(l161s01a.getUHideName());
				UArea = Util.trim(l161s01a.getUArea());
				if (UtilConstants.DEFAULT.是.equals(l161s01a.getUCMainBranch())) {
					unionRole = "Y";
				} else {
					unionRole = " ";
				}
				if (UtilConstants.DEFAULT.是.equals(l161s01a.getUCntBranch())) {
					unionRole += "L";
				} else {
					unionRole += " ";
				}
				if (UtilConstants.DEFAULT.是.equals(l161s01a.getUCMSBranch())) {
					unionRole += "C";
				} else {
					unionRole += " ";
				}
			}
		}

		String riskArea = Util.trim(l140m01a.getRiskArea());
		String setDate = null;
		if (Util.equals(l161s01a.getUnitCase(), "Y")) {
			setDate = CapDate.formatDate(l161s01a.getSignDate(),
					UtilConstants.DateFormat.YYYY_MM_DD);
		}

		if (Util.isEmpty(setDate)) {
			setDate = CapDate.formatDate(l120m01a.getEndDate(),
					UtilConstants.DateFormat.YYYY_MM_DD);
		}

		if (Util.isEmpty(setDate)) {
			setDate = CapDate.formatDate(l120m01a.getApproveTime(),
					UtilConstants.DateFormat.YYYY_MM_DD);
		}

		// 信用評等等級CHAR(2)
		String crdttbl = "";
		// 信用風險模型評等類別 CHAR(1)
		String mowType = "";
		// 信用風險模型評等等級 CHAR(2)
		String mowtbl1 = "";

		// 舊評等日期CHAR(07)
		String crdtYmd = "";
		// 舊評等日期 AS400 DECIMAL(8)
		BigDecimal forAS400crdtYmd = BigDecimal.ZERO;
		// 舊評等分行 CHAR(3)
		String crdtBr = "";
		// MOW 評等日期CHAR(07)
		String mowYmd = "";
		// MOW 評等日期AS400 DECIMAL(8)
		BigDecimal forAS400MowYmd = BigDecimal.ZERO;
		// MOW 評等分行
		String mowbr = "";
		// MOODY評等日期 DECIMAL(8)
		BigDecimal modyDate = BigDecimal.ZERO;
		// MOODY評等 CHAR(10)
		String moodyGrd = "";
		// SP評等日期 DECIMAL(8)
		BigDecimal spDate = BigDecimal.ZERO;
		// SP評等 CHAR(10)
		String spGrd = "";
		// FITCH評等日期 DECIMAL(8)
		BigDecimal fitchDate = BigDecimal.ZERO;
		// FITCH評等 CHAR(10)
		String fitchGrd = "";
		String crdType = "";
		String grade = "";

		String l120s01cCustKey = "";
		for (L120S01C l120s01c : l120s01cs) {
			l120s01cCustKey = l120s01c.getCustId() + l120s01c.getDupNo();
			if (custKey.equals(l120s01cCustKey)) {
				crdType = Util.trim(l120s01c.getCrdType());
				grade = Util.trimSizeInOS390(Util.trim(l120s01c.getGrade()), 2);
				if (crdType.startsWith("M")) {
					// 信用風險模型評等類別
					mowType = crdType.substring(1, 2);
					mowYmd = this.paresTOTWDate(l120s01c.getCrdTYear());
					// 信用風險模型評等等級
					mowtbl1 = grade;
					mowbr = Util.trimSizeInOS390(l120s01c.getCrdTBR(), 3);

				} else if (UtilConstants.Casedoc.CrdType.DBU大型企業
						.equals(crdType)
						|| UtilConstants.Casedoc.CrdType.DBU中小型企業
								.equals(crdType)
						|| UtilConstants.Casedoc.CrdType.海外.equals(crdType)) {

					if (UtilConstants.Casedoc.CrdType.DBU大型企業.equals(crdType)
							&& "NA".equals(grade)) {
						// 當為DB且為 分數為NA 此值上傳空白
						crdType = "";
						crdttbl = "";
						crdtBr = "";
						crdtYmd = "";
					} else {
						crdttbl = grade;
						crdtBr = Util.trimSizeInOS390(l120s01c.getCrdTBR(), 3);
						crdtYmd = this.paresTOTWDate(l120s01c.getCrdTYear());
					}

				}
				if (UtilConstants.Casedoc.CrdType.MOODY.equals(crdType)) {
					moodyGrd = grade;
					modyDate = new BigDecimal(CapDate.formatDate(
							l120s01c.getCrdTYear(), "yyyyMMdd"));
				}
				if (UtilConstants.Casedoc.CrdType.SAndP.equals(crdType)) {
					spGrd = grade;
					spDate = new BigDecimal(CapDate.formatDate(
							l120s01c.getCrdTYear(), "yyyyMMdd"));
				}
				if (UtilConstants.Casedoc.CrdType.Fitch.equals(crdType)) {
					fitchDate = new BigDecimal(CapDate.formatDate(
							l120s01c.getCrdTYear(), "yyyyMMdd"));
					fitchGrd = grade;
				}
			}
		}
		// 聯貸信保註記
		// J-112-0366_12473_B1001 送保方式為批次送保時, 聯貸信保註記=B
		String syndipfd = "";
		if ("3".equals(Util.trim(l140m01a.getGutType()))) {
			syndipfd = "B";
		} else {
			syndipfd = Util.trim(l140m01a.getSyndIPFD());
		}

		// 簽報書案號-> 民國年 + 分行別+LMS+末五碼流水號
		String documentNo = LMSUtil.getUploadCaseNo(l120m01a);

		// 額度控管種類
		String factType = Util.trim(l161s01a.getSnoKind());
		// 是否有共用額度序號
		String commsno = Util.trim(l140m01a.getCommSno());
		if (Util.isNotEmpty(commsno)) {
			commsno = UtilConstants.DEFAULT.是;
		} else {
			commsno = "";
		}

		// 銀行法,金控法44,45
		String liHaiBank = "";
		String liHai44 = "";
		String liHai45 = "";
		String caseMainId = l120m01a.getMainId();
		String docType = l120m01a.getDocType();
		if (UtilConstants.Casedoc.DocType.企金.equals(docType)) {
			// 企金
			L120S01D l120s01d = l120s01dDao.findByUniqueKey(caseMainId, custId,
					dupNo);
			if (l120s01d != null) {
				liHaiBank = Util.trim(l120s01d.getMbRlt());
				liHai44 = Util.trim(l120s01d.getMhRlt44());
				liHai45 = Util.trim(l120s01d.getMhRlt45());
			}
		} else if (UtilConstants.Casedoc.DocType.個金.equals(docType)) {
			// 個金
			C120S01E c120s01e = c120s01eDao.findByUniqueKey(caseMainId, custId,
					dupNo);
			if (c120s01e != null) {
				liHaiBank = Util.trim(c120s01e.getIsQdata2());
				liHai44 = Util.trim(c120s01e.getIsQdata3());
				liHai45 = Util.trim(c120s01e.getIsQdata16());
			}
		}

		// 是否為銀行法或金控法利害關係人
		// String reFlag = UtilConstants.DEFAULT.否;
		// J-104-0219-001
		// 修改e-Loan授信管理系統企金額度明細表申請內容之「額度性質」欄，有關為無擔保授信不計入限額註記項目之「說明」及其下拉選項。
		// String unsecureFlag = "";
		// if ("1".equals(liHaiBank) || "1".equals(liHai44) ||
		// "1".equals(liHai45)) {
		// reFlag = UtilConstants.DEFAULT.是;
		//
		// // J-104-0219-001
		// // 修改e-Loan授信管理系統企金額度明細表申請內容之「額度性質」欄，有關為無擔保授信不計入限額註記項目之「說明」及其下拉選項。
		// unsecureFlag = Util.trim(l140m01a.getUnsecureFlag());
		// } else {
		// reFlag = UtilConstants.DEFAULT.否;
		// }

		// J-105-0250-001 Web e-Loan 新增利害關係人檢核
		/*
		 * 補充說明如下 ELF500 新增２個欄位 ● elf500_unsecurefla => 比照 elf383 的欄位 ●
		 * elf500_notvalid => 當企金簽報書含個人戶的額度，為免 ELF383, ELF500 存在相同的額度, a-loan
		 * 不曉得哪一筆是最新的資料 故增加此欄位來判斷
		 * 
		 * 
		 * 銀行法 金控44 金控45 －－ －－－ －－－ －－－ 主借人 甲 乙 丙 共借人 X Y
		 * 
		 * elf383_reflag ,elf500_relate原本的值, 只有{Y,N} {Y,N}代表［主借人］，是否是［銀行法 or
		 * 金控44 or 金控45］關係人，判斷的範圍是上圖的［甲、乙、丙］
		 * 
		 * 此案上線後, 企金／消金動審表上傳的值, 將更改為{A, B} {A, B}代表［主借人、共借人］，是否是［銀行法 or 金控44
		 * ］關係人，判斷的範圍是上圖的［甲、乙、X、Y］ 之後中心引進「簽案時是否為利害關係人」時，會把”A”改”Y”,”B”改”N”
		 */

		String reFlag = "B"; // ELF383 REFLAG
		String unsecureFlag = ""; // ELF383 UNSECUREFLAG

		List<L120S01D> l120s01ds = lms1401Service
				.getCntrDocAllBorrowerL120S01D(l140m01a);

		for (L120S01D tl120s01d : l120s01ds) {
			if (LMSUtil.isUnsecureFlag(tl120s01d)) {

				reFlag = "A";

				if (lms1401Service.chkCntrDocHasUnSecureSubject(l140m01a,
						l140m01a.getSbjProperty(), new String[] { "321" })) {
					unsecureFlag = Util.trim(l140m01a.getUnsecureFlag());
				}

				break;

			}
		}

		// 不計入授信項目代號
		String lnnoflag = "";
		// J-104-0219-001
		// 修改e-Loan授信管理系統企金額度明細表申請內容之「額度性質」欄，有關為無擔保授信不計入限額註記項目之「說明」及其下拉選項。
		lnnoflag = Util.trim(l140m01a.getNoLoan());

		// 不是同業聯貸案，所以同業聯貸相關欄位都不應該有值
		// (UNIONAMT,SHAREAMT,UNICHGFLAG ,HIDEUNION ,SETDATE ,UNIONAREA
		// ,UNIONROLE)
		if (UtilConstants.Usedoc.caseType.一般貸款.equals(caseType)) {
			unionAmt = BigDecimal.ZERO;
			shareAmt = BigDecimal.ZERO;
			// unichgFlag = "";
			hideunion = "";
			setDate = "";
			UArea = "";
			unionRole = "";
		}

		// 國內新增上傳欄位

		// 借款人有無赴大陸投資 CHAR(01) Y/N
		String chinaivt = "";
		// 赴大陸投資金額幣別 CHAR(03)
		String chinacur = "";
		// DECIMAL(15,2) 仟元
		BigDecimal chinaamt = BigDecimal.ZERO;
		// 經濟部投審會核准金額(TWD) DECIMAL(15,2) TWD 仟元
		BigDecimal signamt = BigDecimal.ZERO;
		if (l120s01b != null) {
			chinaivt = this.changeL120S01BInvMFlag(l120s01b.getInvMFlag());
			chinacur = Util.trim(l120s01b.getInvMCurr());

			chinaamt = Util.parseBigDecimal(l120s01b.getInvMAmt());

			signamt = Util.parseBigDecimal(l120s01b.getAprAmt());

		}

		String noisurea = Util.trim(l140m01a.getNoInsuReason());
		// 未送信保-擔保率超過% DECIMAL(5,2) %
		BigDecimal noisuort = BigDecimal.ZERO;
		// 未送信保-其他 VCHAR(1024) 經辦輸入描述 國內新增上傳欄位
		String noisudesp = "";
		if ("4".equals(noisurea)) {
			noisuort = Util.parseBigDecimal(l140m01a.getNoInsuReasonOther());
		} else if ("7".equals(noisurea)) {
			noisudesp = l140m01a.getNoInsuReasonOther();
		}

		// 央行購住/空地/建屋貸款註記
		String controlcd = "";
		// 是否所有權取得日期在99/6/25以後或99/6/25後曾受央行管制客戶之增貸或轉貸案
		String duringFlag = "";
		// 貸款成數
		BigDecimal ltvRate = BigDecimal.ZERO;
		// 擔保品座落地區別
		String locationcd = "";
		// 本次聯徵查詢名下有其他房貸資料
		String jcicMark = "";
		// 屬央行控管對象者需要選擇一個理由
		String plusreason = "";

		// 個人:「住」或「住商」無營業註記
		// 法人: 建物謄本登記用途是否屬「住」類 (Y/N)
		String buildYN = "";
		// 擔保品鑑價
		BigDecimal nowAMT = BigDecimal.ZERO;
		// 擔保品估價
		BigDecimal valueAMT = BigDecimal.ZERO;
		// 有無與其他額度共用擔保品
		String commonYN = "";
		// 有無授權外核准得分批動用
		String shareCollYN = "";
		// 共用同一擔保品總額度金額
		BigDecimal shareCollAmt = BigDecimal.ZERO;
		// 是否為受限戶(Y/N)
		String isLimitCust = "";
		// 是否為高價住宅(Y/N)
		String isHighHouse = "";
		// 是否屬都市計畫劃定之區域(Y/N)
		String houseYN = "";
		// 都市計畫劃定之使用分區
		String houseType = "";
		// 借款用途
		String purposeType = "";
		// 擔保品性質別
		String cmsType = "";
		// 保留一成估值(Y/N)
		String keepYN = "";
		// 非屬央行填報對象理由
		String plusReasonMeMo = "";
		// 擔保品座落 - 段
		String sit3No = "";
		// 擔保品座落 - 村
		String sit4No = "";
		// 擔保品性質別3(其他)之說明
		String cmsOther = "";
		// 按轉讓發票金額 % 動用
		BigDecimal loanPer = BigDecimal.ZERO;
		// Web e-Loan 央行房貸驗證註記
		String cbHlChk = "";
		// 取得擔保品購價(台幣元)
		BigDecimal appAmt = BigDecimal.ZERO;

		String version20201208 = null;
		String realEstateLoanLimitReason = null;
		String hLoanLimit_2 = "";
		Date endDate = null;
		String isRenew = "";
		String isPayOldQuota = "";
		BigDecimal oldQuota = null;// J-111-0534：隨此欄位於畫面上被棄用，已不需放值於DB
		BigDecimal payOldAmt = BigDecimal.ZERO;
		String payOldAmtItem = "";
		String isMatchUnsoldHouseItem = "";
		String isSaleCase = "";
		Date lstDate = null;
		BigDecimal timeVal = BigDecimal.ZERO;

		if (l140m01m != null) {
			controlcd = Util.trim(l140m01m.getCbcCase());
			if (UtilConstants.L140M01MCbcCase.自然人.equals(controlcd)) {
				ltvRate = l140m01m.getPayPercent();
				locationcd = Util.trim(l140m01m.getAreaId());
				jcicMark = l140m01m.getCustYN();
				duringFlag = "Y";
			} else if (UtilConstants.L140M01MCbcCase.土地抵押貸款.equals(controlcd)
					|| UtilConstants.L140M01MCbcCase.公司法人.equals(controlcd)
					|| UtilConstants.L140M01MCbcCase.餘屋貸款.equals(controlcd)
					|| UtilConstants.L140M01MCbcCase.工業區閒置土地抵押貸款
							.equals(controlcd)) {
				ltvRate = l140m01m.getPayPercent();
				locationcd = Util.trim(l140m01m.getAreaId());
			} else if (UtilConstants.L140M01MCbcCase.非央行自然人.equals(controlcd)) {
				plusreason = Util.trim(l140m01m.getPlusReason());
			}
			locationcd = getUploadLocationCd(locationcd);
			buildYN = CapString.trimNull(l140m01m.getBuildYN());
			nowAMT = Util.parseBigDecimal(l140m01m.getNowAMT());
			valueAMT = Util.parseBigDecimal(l140m01m.getValueAMT());
			commonYN = CapString.trimNull(l140m01m.getCommonYN());
			shareCollYN = CapString.trimNull(l140m01m.getShareCollYN());

			// 如果shareCollYN & commonYN皆等於N時 ，但有金額時要將金額清掉
			if ("N".equals(CapString.trimNull(l140m01m.getCommonYN()))
					&& "N".equals(CapString.trimNull(l140m01m.getShareCollYN()))) {
				shareCollAmt = BigDecimal.ZERO;
			} else {
				shareCollAmt = Util.parseBigDecimal(l140m01m.getShareCollAmt());
			}

			isLimitCust = CapString.trimNull(l140m01m.getIsLimitCust());
			isHighHouse = CapString.trimNull(l140m01m.getIsHighHouse());
			houseYN = CapString.trimNull(l140m01m.getHouseYN());
			houseType = CapString.trimNull(l140m01m.getHouseType());
			purposeType = CapString.trimNull(l140m01m.getPurposeType());
			cmsType = CapString.trimNull(l140m01m.getCmsType());
			keepYN = CapString.trimNull(l140m01m.getKeepYN());
			plusReasonMeMo = Util.trimSizeInOS390(Util.toFullCharString(Util
					.trim(l140m01m.getPlusReasonMeMo())), 62);
			sit3No = l140m01m.getSit3No() == null ? "" : Util.addZeroWithValue(
					l140m01m.getSit3No().intValue(), 4);
			sit4No = CapString.trimNull(l140m01m.getSit4No());
			cmsOther = Util.trimSizeInOS390(Util.trim(l140m01m.getCmsOther()),
					22);
			cbHlChk = CapString.trimNull(l140m01m.getCheckYN());
			appAmt = l140m01m.getAppAmt() == null ? BigDecimal.ZERO : l140m01m
					.getAppAmt();

			// 央行 109-12-08 對辦理不動產抵押貸款業務規定
			version20201208 = l140m01m.getVersion() == null ? "" : l140m01m
					.getVersion();
			realEstateLoanLimitReason = l140m01m.getRealEstateLoanLimitReason() == null ? ""
					: l140m01m.getRealEstateLoanLimitReason();
			// 核准日期
			endDate = this.lmsService
					.getApprovalDateByLnf447nForIs3rdHighHouseRule(l140m01a
							.getCntrNo());

			isRenew = l140m01m.getIsRenew() == null ? "" : l140m01m
					.getIsRenew();
			isPayOldQuota = l140m01m.getIsPayOldQuota() == null ? "" : l140m01m
					.getIsPayOldQuota();
			oldQuota = null;// J-111-0534：隨此欄位於畫面上被棄用，已不需放值於DB
			payOldAmt = l140m01m.getPayOldAmt() == null ? payOldAmt : l140m01m
					.getPayOldAmt();
			payOldAmtItem = l140m01m.getPayOldAmtItem() == null ? "" : l140m01m
					.getPayOldAmtItem();
			isMatchUnsoldHouseItem = l140m01m.getIsMatchUnsoldHouseItem() == null ? ""
					: l140m01m.getIsMatchUnsoldHouseItem();
			isSaleCase = l140m01m.getIsSaleCase() == null ? "" : l140m01m
					.getIsSaleCase();

			lstDate = l140m01m.getCbControlLstDate();
			timeVal = l140m01m.getTimeVal() == null ? timeVal : l140m01m
					.getTimeVal();

			if (!"5".equals(plusreason)) {
				// 非屬央行控管對象者需要選擇一個理由, 當理由不是其它時，要將取得非央行控管種類原因 備註清除
				plusReasonMeMo = "";
			}

			// 將值清除，避免畫面上殘留的值塞到DB
			if ("2".equals(controlcd)) {
				// appAmt = BigDecimal.ZERO;
				nowAMT = BigDecimal.ZERO;
			} else if ("4".equals(controlcd)) {
				appAmt = BigDecimal.ZERO;
				nowAMT = BigDecimal.ZERO;
				valueAMT = BigDecimal.ZERO;
			}
		}

		// 額度控管種類為6開頭的(應收帳款相關)才需上傳 loanPer
		if (Util.equals(Util.getLeftStr(l161s01a.getSnoKind(), 1), "6")) {
			loanPer = Util.parseBigDecimal(l140m01a.getLoanPer());
		}

		String residential = CapString.trimNull(l140m01a.getResidential());

		setDate = this.parseDate(setDate);
		// 先刪除此筆資料後再新增

		if (l160m01a.getDocStatus()
				.equals(CreditDocStatusEnum.海外_已核准.getCode())) {
			// 重新上傳MIS
			misQuotapprService.delByUniqueKeyWithoutONLNTIME(custId, dupNo,
					cntrNo, sDate);
			// 202030728
			misELF383NService.delByUniqueKeyWithoutONLNTIME(custId, dupNo,
					cntrNo, sDate);
		} else {
			// misQuotapprService.delByUniqueKey(custId, dupNo, cntrNo, sDate);
			misQuotapprService.delByUniqueKeyWithoutONLNTIME(custId, dupNo,
					cntrNo, sDate);
			// J-112-0366_12473_B1001
			misELF383NService.delByUniqueKeyWithoutONLNTIME(custId, dupNo,
					cntrNo, sDate);
		}

		// 調整 USEEFLAG LLNEFLAG，因為不論與ELOAN比或是與ALOAN比都無法正確比出有無變更，故與小小討論後改以下列方式判斷
		if (!Util.equals(lnFlag, "N") && !Util.equals(lnFlag, "U")) { // N= 新作
			if ((!Util.equals(useFmDt, "0001-01-01") && !Util.equals(useEnDt,
					"0001-01-01")) || useFtMn > 0) {
				useeFlag = "Y";
			}
			if ((!Util.equals(llnfDate, "0001-01-01") && !Util.equals(llneDate,
					"0001-01-01")) || llnmon > 0) {
				llneFlag = "Y";
			}
		}

		// 青創申請日期
		String applyDate = this.parseDate(l140m01a.getApplyDate());

		// J-103-0202-005 遠匯換匯比照選擇權，以交易額度(名目本金*風險係數)來簽案
		// 風險係數 RISKFACTORS
		// 名目額度 RISKFACTAMT
		// BigDecimal shareAmt =
		// this.gfnConvertZero(l161s01a.getCurrentApplyAmt()).setScale(0,
		// BigDecimal.ROUND_HALF_UP);
		Boolean hasDerivateSubjectFlag = false;
		BigDecimal riskFactors = BigDecimal.ZERO; // 風險係數
		BigDecimal riskActAmt = BigDecimal.ZERO; // 名目額度
		String derivativesNumDscr = Util.trim(l140m01a.getDerivativesNumDscr());

		if (Util.equals(l161s01a.getIsDerivatives(), UtilConstants.DEFAULT.是)) {
			boolean fxTradeEffectFlag = lmsService.chkFxTradeEffect();

			BigDecimal maxFactors = BigDecimal.ZERO;
			BigDecimal tmpFactors = BigDecimal.ZERO;

			if (Util.notEquals(derivativesNumDscr, "")) {
				// 取得最大的風險係數
				String[] dervArr = derivativesNumDscr
						.split(UtilConstants.Mark.SPILT_MARK);
				for (String dervItem : dervArr) {
					tmpFactors = Util.parseBigDecimal(dervItem);
					if (maxFactors.compareTo(tmpFactors) < 0) {
						maxFactors = tmpFactors;
					}
				}

				if (Util.equals(l161s01a.getDervApplyAmtType(), "")) {
					// L160M01A.cntrInfo=額度動用資訊
					// L160M01A.message75=尚有必填欄位未填
					// L160M01A.dervApplyAmtType=衍生性商品現請額度種類

					throw new CapMessageException(
							prop.getProperty("L160M01A.cntrInfo")
									+ l161s01a.getCntrNo()
									+ prop.getProperty("L160M01A.message75")
									+ "：「"
									+ prop.getProperty("L160M01A.dervApplyAmtType")
									+ "」", getClass());
				}

				if (maxFactors.compareTo(BigDecimal.ZERO) > 0) {
					riskFactors = maxFactors;
					BigDecimal divdFactors = maxFactors.divide(
							BigDecimal.valueOf(100), 10,
							BigDecimal.ROUND_HALF_UP);
					if (Util.equals(l161s01a.getDervApplyAmtType(), "1")) {
						// 現請額度為
						// 授權額度/交易額度(已乘上信用轉換係數/風險係數)**************************
						if (fxTradeEffectFlag == true) {
							// 生效後 curAmt = 交易額度 riskActAmt = 名目額度
							// 名目額度 =現請額度(交易額度) / 風險係數
							riskActAmt = this.gfnConvertZero(
									l161s01a.getCurrentApplyAmt()).divide(
									divdFactors, 2, BigDecimal.ROUND_HALF_UP);
						} else {
							// 生效前 curAmt = 名目額度 riskActAmt = 交易額度
							curAmt = this.gfnConvertZero(
									l161s01a.getCurrentApplyAmt()).divide(
									divdFactors, 2, BigDecimal.ROUND_HALF_UP);
							riskFactors = BigDecimal.ZERO;
							riskActAmt = BigDecimal.ZERO;
						}
					} else {
						// 現請額度為名目額度*************************************************************
						if (fxTradeEffectFlag == true) {
							// 生效後 curAmt = 交易額度 riskActAmt = 名目額度
							// 現請額度改為交易額度 =>名目額度 * 風險係數
							// 遠匯沒有聯貸，所以現請額度不用考量可能為聯貸總額度之情形
							curAmt = this
									.gfnConvertZero(
											l161s01a.getCurrentApplyAmt())
									.multiply(divdFactors)
									.setScale(2, BigDecimal.ROUND_HALF_UP);
							// 名目額度 =現請額度
							riskActAmt = this.gfnConvertZero(
									l161s01a.getCurrentApplyAmt()).setScale(2,
									BigDecimal.ROUND_HALF_UP);
						} else {
							// 生效前 curAmt = 名目額度 riskActAmt = 交易額度
							riskFactors = BigDecimal.ZERO;
							riskActAmt = BigDecimal.ZERO;
						}

					}

				} else {
					// L160M01A.message89=額度明細表【選擇權、換匯換利交易、換利交易、遠匯、換匯】科目無對應之風險係數，不得動用!!
					if (!UtilConstants.Cntrdoc.Property.不變.equals(l140m01a
							.getProPerty())
							|| Util.equals(l161s01a.getUseSpecialReason(), "01")) {
						throw new CapMessageException(
								prop.getProperty("L160M01A.message89"),
								getClass());
					}

				}
			} else {
				// 沒有風險係數
				boolean isProPerty7 = LMSUtil.isContainValue(
						Util.trim(l140m01a.getProPerty()),
						UtilConstants.Cntrdoc.Property.不變);
				if (!UtilConstants.Cntrdoc.Property.不變.equals(l140m01a
						.getProPerty())
						|| Util.equals(l161s01a.getUseSpecialReason(), "01")) {
					throw new CapMessageException(
							prop.getProperty("L160M01A.message89"), getClass());
				}
			}

		}

		// J-104-0284-001 額度明細表檢核供應鏈融資賣放限週轉科目
		String isEfin = "";
		if (Util.notEquals(factType, "")) {
			if (Util.equals(Util.trim(l140m01a.getIsEfin()), "Y")) {
				if (Util.equals(factType.substring(0, 1), "6")) {
					isEfin = Util.trim(l140m01a.getIsEfin());
				}
			}
		}

		String prodKind = Util.trim(l140m01a.getLnType());
		if (Util.equals(prodKind, "00")) {
			prodKind = "";
		}

		// J-107-0357_05097_B1001 Web
		// e-Loan授信系統配合工業區及產業園區建廠優惠貸款專案，額度簽報新增「專案種類」與相關報表
		String projClass = Util.trim(l140m01a.getProjClass());
		if (Util.equals(projClass, "00")) {
			projClass = "";
		}

		// J-105-0135-001 Web e-Loan國內企金授信系統動審表，開放可修改振興經濟非中小企業專案貸款註記與金額。
		// 上傳用
		String isNonSMEProjLoan = "";
		BigDecimal nonSMEProjLoanAmt = BigDecimal.ZERO;

		// J-105-0155-001 Web e-Loan國內、海外企金額度明細表增加『約定融資額度註記』欄位與上傳a-Loan功能
		// _ 為N.A. 上傳時改為""
		String exceptFlag = Util.trim(l140m01a.getExceptFlag())
				.replace("_", "");

		// 動審表的
		String isNonSMEProjLoanNew = Util.trim(l161s01a.getIsNonSMEProjLoan());
		BigDecimal nonSMEProjLoanAmtNew = BigDecimal.ZERO;
		String cNonSMEProjMainId = Util.trim(l161s01a.getMainId());
		if (Util.equals(isNonSMEProjLoanNew, "Y")) {
			nonSMEProjLoanAmtNew = l161s01a.getNonSMEProjLoanAmt() == null ? BigDecimal.ZERO
					: l161s01a.getNonSMEProjLoanAmt();
		}

		// 額度明細表的
		String isNonSMEProjLoanOrg = Util.trim(l140m01a.getIsNonSMEProjLoan());
		BigDecimal nonSMEProjLoanAmtOrg = l140m01a.getNonSMEProjLoanAmt() == null ? BigDecimal.ZERO
				: l140m01a.getNonSMEProjLoanAmt();

		// 先判斷動審表有沒有輸入振興經濟非中小企業專案貸款註記與金額
		if (Util.notEquals(isNonSMEProjLoanNew, "")) {
			// 動審表有欄位
			// 上傳以動審表欄位為主
			isNonSMEProjLoan = isNonSMEProjLoanNew;
			nonSMEProjLoanAmt = nonSMEProjLoanAmtNew;

			// 將動審表內容寫回額度明細表 cIsNonSMEProjLoan cNonSMEProjLoanAmt

			// 判斷額度明細表有沒有這個欄位
			if (Util.notEquals(Util.trim(isNonSMEProjLoanOrg), "")) {
				// 有
				// 判斷動審表內容是否與額度明細表一致
				if (Util.notEquals(isNonSMEProjLoanOrg, isNonSMEProjLoanNew)
						|| nonSMEProjLoanAmtOrg.compareTo(nonSMEProjLoanAmtNew) != 0) {
					// 不同
					// 要寫到 cIsNonSMEProjLoan cNonSMEProjLoanAmt
					l140m01a.setCIsNonSMEProjLoan(isNonSMEProjLoanNew);
					l140m01a.setCNonSMEProjLoanAmt(nonSMEProjLoanAmtNew);
					l140m01a.setCNonSMEProjMainId(cNonSMEProjMainId);
					lms1401Service.save(l140m01a);
				} else {
					// 相同
					// 不寫到 cIsNonSMEProjLoan cNonSMEProjLoanAmt
				}
			} else {
				// 沒有
				// 額度明細表沒有，就直接塞值
				l140m01a.setCIsNonSMEProjLoan(isNonSMEProjLoanNew);
				l140m01a.setCNonSMEProjLoanAmt(nonSMEProjLoanAmtNew);
				l140m01a.setCNonSMEProjMainId(cNonSMEProjMainId);
				lms1401Service.save(l140m01a);
			}
		} else {
			// 動審表沒欄位
			// 上傳以額度明細表欄位為主
			isNonSMEProjLoan = isNonSMEProjLoanOrg;
			nonSMEProjLoanAmt = nonSMEProjLoanAmtOrg;
		}

		// J-109-0352_05097_B1001 (109) 第 2684 號
		// 國內企金授信額度明細表新增「109年9至12月提升中小企業放款方案」
		String headItem2 = Util.trim(l140m01a.getHeadItem2());
		String isEnhanceSmeLoan = Util.trim(l140m01a.getIsEnhanceSmeLoan());
		// J-110-0038_05097_B1001 Web e-Loan企金額度明細表新增「本案是否屬110年行銷名單來源客戶」並產生統計報表
		String isMarketingList110 = Util.trim(l140m01a.getIsMarketingList110());
		if (Util.equals(headItem2, "Y")) {

			if (Util.equals(isMarketingList110, "Y")) {
				isNonSMEProjLoan = "F";
			} else if (Util.equals(isEnhanceSmeLoan, "Y")) {
				// 建議不新增欄位
				//
				// 借 ELF383_NONSME CHAR(01) 振興經濟非中小企業專案註記 (現在有Y/N/空白)
				// 來放 "本案是否屬109年9至12月提升中小企業放款方案"
				// 值為 'E'
				//
				// 主機有4支程式會判斷ELF383_NONSME=Y和資金來源(0222)的合理性
				isNonSMEProjLoan = "E";
			}
		}

		// J-106-0082-001 Web e-Loan國內企金授信系統，額度明細表新增中小企業創新發展專案貸款
		// 上傳用
		String inSmeFg = "";
		BigDecimal inSmeToAmt = BigDecimal.ZERO;
		BigDecimal inSmeCaAmt = BigDecimal.ZERO;

		// 動審表的
		String inSmeFgNew = "";
		BigDecimal inSmeToAmtNew = BigDecimal.ZERO;
		BigDecimal inSmeCaAmtNew = BigDecimal.ZERO;

		inSmeFgNew = Util.trim(l161s01a.getInSmeFg());
		if (Util.equals(inSmeFgNew, "Y")) {
			inSmeToAmtNew = l161s01a.getInSmeToAmt() == null ? BigDecimal.ZERO
					: l161s01a.getInSmeToAmt();
			inSmeCaAmtNew = l161s01a.getInSmeCaAmt() == null ? BigDecimal.ZERO
					: l161s01a.getInSmeCaAmt();
		}

		// 額度明細表的
		String inSmeFgOrg = "";
		BigDecimal inSmeToAmtOrg = BigDecimal.ZERO;
		BigDecimal inSmeCaAmtOrg = BigDecimal.ZERO;

		inSmeFgOrg = Util.trim(l140m01a.getInSmeFg());
		if (Util.equals(inSmeFgOrg, "Y")) {
			inSmeToAmtOrg = l140m01a.getInSmeToAmt() == null ? BigDecimal.ZERO
					: l140m01a.getInSmeToAmt();
			inSmeCaAmtOrg = l140m01a.getInSmeCaAmt() == null ? BigDecimal.ZERO
					: l140m01a.getInSmeCaAmt();
		}

		// 先判斷動審表有沒有輸入振興經濟非中小企業專案貸款註記與金額
		if (Util.notEquals(inSmeFgNew, "")) {
			// 動審表有欄位
			// 上傳以動審表欄位為主
			inSmeFg = inSmeFgNew;
			inSmeToAmt = inSmeToAmtNew;
			inSmeCaAmt = inSmeCaAmtNew;

			// 判斷額度明細表有沒有inSmeFg這個欄位時，將動審表內容寫回額度明細表
			if (Util.equals(Util.trim(inSmeFgOrg), "")) {
				l140m01a.setInSmeFg(inSmeFgNew);
				l140m01a.setInSmeToAmt(inSmeToAmtNew);
				l140m01a.setInSmeCaAmt(inSmeCaAmtNew);
				lms1401Service.save(l140m01a);
			}
		} else {
			// 動審表沒欄位
			// 上傳以額度明細表欄位為主
			inSmeFg = inSmeFgOrg;
			inSmeToAmt = inSmeToAmtOrg;
			inSmeCaAmt = inSmeCaAmtOrg;
		}

		// J-109-0077_05097_B1001 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
		// J-109-0077_05097_B1003因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
		// 上傳用
		String isRescue = "";
		String rescueItem = "";
		BigDecimal rescueRate = BigDecimal.ZERO;
		BigDecimal rescueAmt = BigDecimal.ZERO;
		String rescueCurr = "";
		String isCbRefin = "";
		String rescueItemSub = "";
		Date rescueDate = null;
		String cbRefinDt = null;
		// J-110-0288_05097_B1001 Web
		// e-Loan配合辦理「行政院國家發展基金協助新創事業紓困融資加碼方案」，修改額度明細表欄位
		BigDecimal rescueNdfGutPercent = BigDecimal.ZERO;
		// J-110-0288_05097_B1002 Web e-Loan配合國發基金新創事業紓困加碼方案H01、A07專案修改
		String isTurnoverDecreased = ""; // 是否符合110年5~12月營業額減少達15%
		// J-110-0465_05097_B1001 Web e-Loan國內企金授信動審表新增額度編號
		String rescueSn = "";

		// 直接用動審表的
		String rescueIbDate = l161s01a.getRescueIbDate() == null ? null
				: CapDate.formatDate(l161s01a.getRescueIbDate(), "yyyy-MM-dd");

		// 動審表的
		String isRescueNew = "";
		String rescueItemNew = "";
		BigDecimal rescueRateNew = BigDecimal.ZERO;
		BigDecimal rescueAmtNew = BigDecimal.ZERO;
		String rescueCurrNew = "";
		String isCbRefinNew = "";
		String rescueItemSubNew = "";
		Date rescueDateNew = null;
		// J-110-0288_05097_B1001 Web
		// e-Loan配合辦理「行政院國家發展基金協助新創事業紓困融資加碼方案」，修改額度明細表欄位
		BigDecimal rescueNdfGutPercentNew = BigDecimal.ZERO;

		// J-110-0288_05097_B1002 Web e-Loan配合國發基金新創事業紓困加碼方案H01、A07專案修改
		// 是否符合110年5~12月營業額減少達15%
		if (lmsService.isResueItemNeedIsTurnoverDecreased(Util.trim(l161s01a
				.getRescueItem()))) {
			isTurnoverDecreased = Util.trim(l161s01a.getIsTurnoverDecreased());
			if (Util.equals(isTurnoverDecreased, "X")) {
				isTurnoverDecreased = "N";
			}
		}

		// J-110-0465_05097_B1001 Web e-Loan國內企金授信動審表新增額度編號
		// J-112-0148_05097_B1002 Web
		// e-Loan企金授信新增經濟部協助中小型事業疫後振興專案貸款暨經濟部協助中小企業轉型發展專案貸款
		if (this.isResueItemRescueSn(Util.trim(l161s01a.getRescueItem()))) {
			rescueSn = Util.trim(l161s01a.getRescueSn());
		}

		isRescueNew = Util.trim(l161s01a.getIsRescue());
		if (Util.notEquals(isRescueNew, "")) {
			rescueItemNew = Util.trim(l161s01a.getRescueItem());
			rescueRateNew = l161s01a.getRescueRate() == null ? BigDecimal.ZERO
					: l161s01a.getRescueRate();
			rescueAmtNew = l161s01a.getRescueAmt() == null ? BigDecimal.ZERO
					: l161s01a.getRescueAmt();
			rescueCurrNew = Util.trim(l161s01a.getRescueCurr());

			isCbRefinNew = Util.trim(l161s01a.getIsCbRefin());
			rescueItemSubNew = Util.trim(l161s01a.getRescueItemSub());

			rescueDateNew = l161s01a.getRescueDate();
			// J-110-0288_05097_B1001 Web
			// e-Loan配合辦理「行政院國家發展基金協助新創事業紓困融資加碼方案」，修改額度明細表欄位
			rescueNdfGutPercentNew = l161s01a.getRescueNdfGutPercent() == null ? BigDecimal.ZERO
					: l161s01a.getRescueNdfGutPercent();

		}

		// 額度明細表的
		String isRescueOrg = "";
		String rescueItemOrg = "";
		BigDecimal rescueRateOrg = BigDecimal.ZERO;
		BigDecimal rescueAmtOrg = BigDecimal.ZERO;
		String rescueCurrOrg = "";
		String isCbRefinOrg = "";
		String rescueItemSubOrg = "";
		Date rescueDateOrg = null;
		// J-110-0288_05097_B1001 Web
		// e-Loan配合辦理「行政院國家發展基金協助新創事業紓困融資加碼方案」，修改額度明細表欄位
		BigDecimal rescueNdfGutPercentOrg = BigDecimal.ZERO;

		isRescueOrg = Util.trim(l140m01a.getIsRescue());
		if (Util.notEquals(isRescueOrg, "")) {
			rescueItemOrg = Util.trim(l140m01a.getRescueItem());
			rescueRateOrg = l140m01a.getRescueRate() == null ? BigDecimal.ZERO
					: l140m01a.getRescueRate();
			rescueAmtOrg = l140m01a.getRescueAmt() == null ? BigDecimal.ZERO
					: l140m01a.getRescueAmt();
			rescueCurrOrg = Util.trim(l140m01a.getRescueCurr());
			isCbRefinOrg = Util.trim(l140m01a.getIsCbRefin());
			rescueItemSubOrg = Util.trim(l140m01a.getRescueItemSub());

			rescueDateOrg = l140m01a.getRescueDate();
			// J-110-0288_05097_B1001 Web
			// e-Loan配合辦理「行政院國家發展基金協助新創事業紓困融資加碼方案」，修改額度明細表欄位
			rescueNdfGutPercentOrg = l140m01a.getRescueNdfGutPercent() == null ? BigDecimal.ZERO
					: l140m01a.getRescueNdfGutPercent();
		}

		// 先判斷動審表有沒有輸入本案是否屬因應嚴重特殊傳染性肺炎影響事業資金紓困
		if (Util.notEquals(isRescueNew, "")) {
			// 動審表有欄位
			// 上傳以動審表欄位為主
			isRescue = isRescueNew;
			rescueItem = rescueItemNew;
			rescueRate = rescueRateNew;
			rescueAmt = rescueAmtNew;
			rescueCurr = rescueCurrNew;
			isCbRefin = isCbRefinNew;
			rescueItemSub = rescueItemSubNew;
			rescueDate = rescueDateNew;
			rescueNdfGutPercent = rescueNdfGutPercentNew;

			// 將動審表內容寫回額度明細表
			l140m01a.setIsRescue_Af(isRescueNew);
			l140m01a.setRescueItem_Af(rescueItemNew);

			if (lmsService.isResueItemOldCase(rescueItem)) {
				l140m01a.setRescueRate_Af(rescueRateNew);
			}

			l140m01a.setRescueAmt_Af(rescueAmtNew);
			l140m01a.setRescueCurr_Af(rescueCurrNew);
			l140m01a.setIsCbRefin_Af(isCbRefinNew);
			l140m01a.setRescueItemSub_Af(rescueItemSubNew);
			l140m01a.setRescueDate_Af(rescueDateNew);
			// J-110-0288_05097_B1001 Web
			// e-Loan配合辦理「行政院國家發展基金協助新創事業紓困融資加碼方案」，修改額度明細表欄位
			l140m01a.setRescueNdfGutPercent(rescueNdfGutPercentNew);
			lms1401Service.save(l140m01a);

		} else {
			// 動審表沒欄位
			// 上傳以額度明細表欄位為主
			isRescue = isRescueOrg;
			rescueItem = rescueItemOrg;
			rescueRate = rescueRateOrg;
			rescueAmt = rescueAmtOrg;
			rescueCurr = rescueCurrOrg;
			isCbRefin = isCbRefinOrg;
			rescueItemSub = rescueItemSubOrg;
			rescueDate = rescueDateOrg;
			// J-110-0288_05097_B1001 Web
			// e-Loan配合辦理「行政院國家發展基金協助新創事業紓困融資加碼方案」，修改額度明細表欄位
			rescueNdfGutPercent = rescueNdfGutPercentOrg;
		}

		Map<String, String> lms140_cbRefinDt = codeTypeService.findByCodeType(
				"lms140_cbRefinDt", LMSUtil.getLocale().toString());

		// J-109-0811_05097_B1001 配合「嚴重特殊傳染性肺炎防治及妤困振興特別條例」施行期間調整，動審表新增央行優惠利率融通期限
		if (Util.equals(isRescue, "Y") && Util.equals(isCbRefin, "Y")) {
			// 直接用動審表的
			String tCbRefinDt = Util.trim(l161s01a.getCbRefinDt()); // 01=>2021-03-27、02=>2021-06-30
			if (Util.notEquals(tCbRefinDt, "")) {
				// J-109-0811_05097_B1001
				// 配合「嚴重特殊傳染性肺炎防治及妤困振興特別條例」施行期間調整，動審表新增央行優惠利率融通期限
				cbRefinDt = MapUtils.getString(lms140_cbRefinDt,
						Util.trim(l161s01a.getCbRefinDt()), null);
			}
		}

		// 清除沒用到的欄位
		if (!lmsService.isResueItemRescueRate(rescueItem, rescueItemSub)) {
			// 有要減收利率或補貼利率的才要上傳，其他都設成NULL =>改成帶0
			// J-110-0429_05097_B1001 配合內政部紓困方案，增列內政部紓困方案代號及相關條件
			rescueRate = BigDecimal.ZERO; // 如果帶NULL會造成 call LN.LNSP0141(?, ?,
											// ?, ?)}]; SQL state [39004]; error
											// code [-470]; SQL CALL STATEMENT
											// SPECIFIED A NULL VALUE FOR INPUT
											// PARAMETER 4, BUT THE STORED
											// PROCEDURE DOES NOT SUPPORT NULL
											// VALUES. SQLCODE=-470,
											// SQLSTATE=39004, DRIVER=4.25.23;
											// nested exception is
											// com.ibm.db2.jcc.am.SqlException:
											// SQL CALL STATEMENT SPECIFIED A
											// NULL VALUE FOR INPUT PARAMETER 4,
											// BUT THE STORED PROCEDURE DOES NOT
											// SUPPORT NULL VALUES.
											// SQLCODE=-470, SQLSTATE=39004,
											// DRIVER=4.25.23
		}

		// 清除沒用到的欄位
		if (!lmsService.isResueItemOldCase(rescueItem) && !rescueItem.matches("J04|J05")) {
			// 舊案才需要輸入合意展延日
			rescueIbDate = null;
		}

		// J-107-0357_05097_B1001 Web
		// e-Loan授信系統配合工業區及產業園區建廠優惠貸款專案，額度簽報新增「專案種類」與相關報表
		// J-105-0308-001 Web e-Loan國內海外企金授信管理系統，額度明細表產品種類新增「新創重點產業」。
		// J-111-0129_05097_B1001 Web e-Loan企金授信額度明細表新增六大核心戰略產業及附屬細項
		// 新創重點產業
		String itwCode = "";
		if (Util.equals(Util.trim(l140m01a.getIsStartUp()), "N")) {
			if (Util.equals(Util.trim(projClass), "05")) {
				itwCode = Util.trim(l140m01a.getItwCode());
			}
		} else {
			if (Util.equals(Util.trim(projClass), "05")) {
				projClass = "";
			}
		}

		// J-111-0129_05097_B1001 Web e-Loan企金授信額度明細表新增六大核心戰略產業及附屬細項
		// 六大核心戰略產業
		String itwCodeCoreBuss = "";
		if (Util.equals(Util.trim(l140m01a.getIsCoreBuss()), "Y")) {
			itwCodeCoreBuss = "";
		} else if (Util.equals(Util.trim(l140m01a.getIsCoreBuss()), "N")) {
			itwCodeCoreBuss = Util.trim(l140m01a.getItwCodeCoreBuss());
			if (Util.equals(Util.trim(itwCodeCoreBuss), "00")) {
				itwCodeCoreBuss = "";
			}
		}

		// J-106-0232-001 Web e-Loan國內、海外企金授信衍生性金融商品額度明細表新增淨值與額外信用增強
		String isHedge = "";
		BigDecimal enhanceAmt = BigDecimal.ZERO;
		String netSwft = "";
		BigDecimal netAmt = BigDecimal.ZERO;
		BigDecimal netAmtUnit = BigDecimal.ZERO;

		if (UtilConstants.Casedoc.DocType.企金.equals(docType)) {
			// 企金
			isHedge = Util.trim(lmsService
					.getDerivateSubjectHedgeKinde(l140m01a));
			if (Util.equals(isHedge, "2")) {
				enhanceAmt = l140m01a.getEnhanceAmt() == null ? BigDecimal.ZERO
						: l140m01a.getEnhanceAmt();

				if (l120s01b != null) {
					netSwft = Util.trim(l120s01b.getNetSwft());
					netAmt = l120s01b.getNetAmt() == null ? BigDecimal.ZERO
							: l120s01b.getNetAmt();
					netAmtUnit = l120s01b.getNetAmtUnit() == null ? BigDecimal.ZERO
							: l120s01b.getNetAmtUnit();
				}
			}

		}

		// J-107-0137_05097_B1001 Web
		// e-Loan企金授信額度明細表，新增「信保基金保證書發文日期」與「信保基金核准之保證手續費率」
		BigDecimal cgfRate = BigDecimal.ZERO;
		String cgfDate = null;
		if (UtilConstants.Casedoc.DocType.企金.equals(docType)) {

			String newHeadItem = Util.trim(l140m01a.getHeadItem1());
			if (UtilConstants.DEFAULT.是.equals(newHeadItem)) {
				cgfRate = l140m01a.getCgfRate() == null ? BigDecimal.ZERO
						: l140m01a.getCgfRate();

				if (l140m01a.getCgfDate() != null) {
					cgfDate = CapDate.formatDate(l140m01a.getCgfDate(),
							UtilConstants.DateFormat.YYYY_MM_DD);
				}
			}

		}

		// J-108-0302 是否符合出口實績規範 942
		String experf_fg = "";
		String flaw_fg = ""; // 瑕疵額度控管方式
		BigDecimal flaw_amt = BigDecimal.ZERO; // 瑕疵額度限額
		String[] sysExperf = StringUtils.split(
				Util.trim(lmsService.getSysParamDataValue("EXPERF")), ",");
		for (String key : newList.keySet()) {
			if (Arrays.asList(sysExperf).contains(key)) {
				experf_fg = Util.trim(l140m01a.getExperf_fg());
				flaw_fg = Util.trim(l140m01a.getFlaw_fg());
				flaw_amt = Util.parseBigDecimal(l140m01a.getFlaw_amt());
				break;
			}
		}

		// J-109-0077_05097_B1008 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
		String rescueNo = Util.trim(l161s01a.getRescueNo());
		BigDecimal empCount = l161s01a.getEmpCount();

		// J-109-0077_05097_B1010 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
		// 本案是否屬小規模營業人簡易申貸方案
		String isSmallBuss = "";
		if (Util.equals(isCbRefin, "Y")) {
			isSmallBuss = Util.trim(l140m01a.getIsSmallBuss());
		}

		// J-109-0209_05097_B1001 e-Loan國內企金動審表增加借戶性質註記等進扣帳對象檢核項目
		String isSole = "";
		String soleType = "";
		String hasRegis = "";
		L164S01A l164s01a = this
				.findL164s01aByMainIdCustId(l160m01a.getMainId(),
						l140m01a.getCustId(), l140m01a.getDupNo());
		if (l164s01a != null) {
			isSole = Util.trim(l164s01a.getIsSole());
			if (Util.equals(isSole, "Y")) {
				soleType = Util.trim(l164s01a.getSoleType());
				hasRegis = Util.trim(l164s01a.getHasRegis());
			}
		}

		// J-109-0235_05097_B1001 Web e-loan國內企金授信新增兆元振興融資方案
		String isRevive = "";
		String revTarget = "";
		String revSubItem = "";
		String revPurpose = "";

		// J-110-0281_05097_B1001 Web
		// e-Loan授信配合「公股攜手，兆元振興」融資方案已結案，謹申請取消e-LOAN簽報書相關欄位之顯示
		if (lmsService.needShowIsRevive(l120m01a)) {
			isRevive = Util.trim(l140m01a.getIsRevive());
			if (Util.equals(isRevive, "Y")) {
				revTarget = Util.trim(l140m01a.getReviveTarget());
				if (Util.equals(revTarget, "01")) {
					revSubItem = Util.trim(l140m01a.getReviveCoreIndustry());
				} else if (Util.equals(revTarget, "02")) {
					revSubItem = Util.trim(l140m01a.getReviveChain());
				}

				revPurpose = Util.trim(l140m01a.getReviveLoanPurpose());
			}
		}

		// J-109-0309_05097_B1001 Web e-Loan企金授信觀光局利息補貼額度控管及新增營業所在地欄位
		String rescueIndustry = "";
		String rescueCity = "";
		if (Util.equals(isRescue, "Y")) {
			if (lmsService.isResueItemNeedTourism(rescueItem)) {
				rescueIndustry = Util.trim(l140m01a.getRescueIndustry());
				rescueCity = Util.trim(l140m01a.getRescueCity());
			}
		}

		// J-111-000A_05097_B1001 Web e-Loan企金授信新增綠色支出、永續績效連結授信ESG
		String esggFg = "";
		String esggType = "";
		String esgsFg = "";
		String esgsType = "";
		String esgsUnre = "";

		esggFg = Util.trim(l140m01a.getIsEsgGreenLoan());
		if (Util.equals(esggFg, "Y")) {
			esggType = Util.trim(l140m01a.getEsgGreenSpendType());
		}

		esgsFg = Util.trim(l140m01a.getEsgSustainLoan());
		if (Util.equals(esgsFg, "Y")) {
			esgsType = Util.trim(l140m01a.getEsgSustainLoanType());
			if (Util.notEquals(esgsType, "")) {
				esgsType = lmsService
						.convertEsgSustainLoanTypeToAloan(esgsType);
			}
			esgsUnre = Util.trim(l140m01a.getEsgSustainLoanUnReach());
		}

		// G-111-0168_05097_B1001 新增海外分(子)行「綠色授信」及「永續績效連結授信」等註記
		// 海外ESG要多傳
		BigDecimal esgGreenSpendType1 = BigDecimal.ZERO;
		BigDecimal esgGreenSpendType2 = BigDecimal.ZERO;
		BigDecimal esgGreenSpendType3 = BigDecimal.ZERO;

		if ((LMSUtil.isContainValue(Util.trim(l140m01a.getEsgGreenSpendType()),
				"A"))) {
			// L140M01a.esgGreenSpendType1=發電站
			esgGreenSpendType1 = l140m01a.getEsgGreenSpendType1() == null ? BigDecimal.ZERO
					: l140m01a.getEsgGreenSpendType1();
			// L140M01a.esgGreenSpendType2=裝置容量
			esgGreenSpendType2 = l140m01a.getEsgGreenSpendType2() == null ? BigDecimal.ZERO
					: l140m01a.getEsgGreenSpendType2();
			// L140M01a.esgGreenSpendType3=裝置發電量
			esgGreenSpendType3 = l140m01a.getEsgGreenSpendType3() == null ? BigDecimal.ZERO
					: l140m01a.getEsgGreenSpendType3();
		}
		
		//J-113-0329 企金授信新增社會責任授信
		String socialFlag = "";
		String socialKind = "";
		String socialTa = "";
		String socialResp = "";
		
		socialFlag = Util.trim(l140m01a.getSocialLoanFlag());
		if("Y".equals(socialFlag)){
			socialKind = Util.trim(l140m01a.getSocialKind());
			socialTa = Util.trim(l140m01a.getSocialTa());
			socialResp = Util.trim(l140m01a.getSocialResp());
		}

		// J-111-0197_05097_B1001 Web e-Loan國內企金系統於計算LGD時，若於擔保品系統建有存款或自建擔保品有存款者,
		// 增加上送徵提存款設質擔保註記供ALOAN L550交易引進
		// J-111-0208_05097_B1001 Web
		// e-Loan國內外企金授信簽報，各額度LGD隨核定內容上送ALOAN、AS400(ELF383)
		boolean showLgdEffect = lmsService.showPanelLms140s08(l120m01a,
				l120m01a.getCaseBrId());
		BigDecimal cntrLgd = null;
		String stdAuth = "";
		String depositFg = "";
		if (showLgdEffect) {
			L120S21B l120s21b = l120s21bDao.findByIndex02(l120m01a.getMainId(),
					cntrNo);
			if (l120s21b != null) {
				if (l120s21b.getExpectLgd() != null) {
					// J-111-0400_05097_B1001 Web e-Loan企金授信增修LGD及額度暴險估算規則
					if (l120s21b.getLgdVer1() != null
							&& l120s21b.getLgdVer1() > 0) {
						// 大版是0為LGD試算，不要上傳
						cntrLgd = l120s21b.getExpectLgd().min(
								new BigDecimal(100));
					}
				}
			}

			stdAuth = Util.trim(l140m01a.getIsStandAloneAuth());

			List<L120S21C> l120s21cs = l120s21cDao.findByIndex03(
					l120m01a.getMainId(), cntrNo, null);
			if (l120s21cs != null && !l120s21cs.isEmpty()) {
				depositFg = "N";
				for (L120S21C l120s21c : l120s21cs) {
					if (Util.equals(Util.trim(l120s21c.getColKind_s21c()),
							"030100")) {
						depositFg = "Y";
						break;
					}
				}
			}

			if (Util.notEquals(depositFg, "Y")) {
				// 額度明細表擔保品
				List<L140M01O> l140m01os = l140m01oDao.findByMainId(l140m01a
						.getMainId());
				if (l140m01os != null && !l140m01os.isEmpty()) {

					for (L140M01O l140m01o : l140m01os) {
						if (Util.equals(Util.getLeftStr(
								Util.trim(l140m01o.getCollNo()), 5), "03-01")) {
							depositFg = "Y";
							break;
						}
					}

				}

			}

		}

		// J-111-0214_05097_B1001 Web e-Loan國內企金動用審核表新增可適用新利率計算減免息相關功能

		String RESCUE_C_FG = "";
		Date RESCUE_C_SD = null;
		BigDecimal RESCUE_C_RT = null;
		Date RESCUE_C_ED = null;

		if (this.needRescueChgRateFg(rescueItem)) {

			String rescueChgRateFg = "";
			Date rescueChgRateSingDate = null;
			BigDecimal rescueChgRate = null;
			Date rescueChgRateEffectDate = null;

			rescueChgRateFg = Util.trim(l161s01a.getRescueChgRateFg());
			rescueChgRateSingDate = l161s01a.getRescueChgRateSingDate();
			rescueChgRate = l161s01a.getRescueChgRate();

			RESCUE_C_FG = rescueChgRateFg;

			if (Util.equals(rescueChgRateFg, "Y")) {

				Map<String, String> returnMap = this
						.cacuRescueChgRateEffectDate(CapDate.formatDate(
								rescueChgRateSingDate, "yyyy-MM-dd"), sDate);

				String isOk = MapUtils.getString(returnMap, "isOk");
				String errorMsg = MapUtils.getString(returnMap, "errorMsg");

				if (Util.equals(isOk, "Y")) {

					rescueChgRateEffectDate = Util.parseDate(MapUtils
							.getString(returnMap, "rescueChgRateEffectDate"));

					l161s01a.setRescueChgRateEffectDate(rescueChgRateEffectDate);
					this.save(l161s01a);

					RESCUE_C_SD = rescueChgRateSingDate;
					RESCUE_C_RT = rescueChgRate;
					RESCUE_C_ED = rescueChgRateEffectDate;

				} else {
					throw new CapMessageException("額度序號" + l140m01a.getCntrNo()
							+ errorMsg, getClass());
				}

			}

		}

		// J-112-0366_12473_B1001 批次保證相關欄位
		String gutType = Util.trim(l140m01a.getGutType());
		String batGutSeq = "";
		String batGutNo = "";

		if ("3".equals(gutType)) {
			batGutSeq = Util.trim(l140m01a.getBatchNo());
			batGutNo = Util.trim(l140m01a.getBatGutAmtApprCaseNo());
		}

		// J-109-0365_05097_B1001 Web
		// e-Loan國內企金授信額度明細表科目為遠期外匯、換匯交易時，新增是否徵提保證金等相關欄位
		String marginFlag = "";
		boolean isMarginFlag = lms1401Service.needMarginFlag(l140m01a);
		if (isMarginFlag) {
			marginFlag = Util.trim(l140m01a.getMarginFlag());
		}

		// 紓困資料轉換
		// BGN***********************************************************************************************************************

		// 上傳DB
		String rescue110 = "";
		String rescueItemN = "";
		BigDecimal rescueRateN = null;
		String rescueIbDateN = null; // 110 年合意展延日
		String rescueNoN = ""; // 110 年掛件文號

		// 原始ELOAN資訊BGN******************************************************
		String isRescue_el = isRescue; // 原始eloan簽案註記
		String rescueItem_el = Util.equals(isRescue, "Y") ? rescueItem : "";

		// 原始ELOAN資訊END******************************************************

		// J-110-0209_05097_B1001 Web e-Loan國內企金簽報書額度明細表中增列「創兆貸」專案，並產生統計報表案
		//
		// ELF383(QUOTAPPR)增加
		// ELF383_RESCUE_110 CHAR(01) NOT NULL WITH DEFAULT 110年紓困註記
		// ELF383_RESCUEITEM_N CHAR(03) NOT NULL WITH DEFAULT 110年舊有貸款紓困類別
		// ELF383_RESCUERATE_N DECIMAL(7,5) NULL

		if (true) {

			String isRescueOn = Util.trim(l140m01a.getIsRescueOn());
			String rescueItemOn = Util.trim(l140m01a.getRescueItemOn());
			String cntrNoOn = Util.trim(l140m01a.getCntrNoOn());

			if (Util.equals(isRescue, "Y") && Util.equals(isRescueOn, "Y")) {

				// J-111-0112_05097_B1002 Web e-Loan企金授信管理系統新增111年經濟部紓困方案
				// A04 A05 A06
				if ((lmsService.isResueItemSubSidy(rescueItem, "110") && lmsService
						.isResueItemSubSidy(rescueItemOn, "109"))
						|| (lmsService.isResueItemSubSidy(rescueItem, "111") && (lmsService
								.isResueItemSubSidy(rescueItemOn, "109") || lmsService
								.isResueItemSubSidy(rescueItemOn, "110")))) {

					rescueItemN = rescueItem; // 放新的110年度資料
					rescueRateN = rescueRate; // 放新的110年度資料
					rescueIbDateN = rescueIbDate;
					rescueNoN = rescueNo;

					// 找前次的額度序號與紓困代碼
					// ELF383 只會有A01-A03,不會有A04-A06
					// 怕ELOAN前端沒控制好,強迫轉一次
					// String xRescueItemOn = Util.trim(lmsService
					// .resueItemSubSidyMapping(rescueItemOn));
					List<Map<String, Object>> l161s01aList = eloanDbBaseService
							.findL161s01aLastRescueDataWithRescueItem(cntrNoOn,
									custId, dupNo, rescueItemOn);

					if (l161s01aList != null && !l161s01aList.isEmpty()) {
						for (Map<String, Object> l161s01aMap : l161s01aList) {

							String ISRESCUE_161 = Util.trim(MapUtils.getString(
									l161s01aMap, "ISRESCUE"));
							String RESCUEITEM_161 = Util.trim(MapUtils
									.getString(l161s01aMap, "RESCUEITEM"));

							String RESCUERATE_161 = Util.trim(MapUtils
									.getString(l161s01aMap, "RESCUERATE"));
							String RESCUEIBDATE_161 = Util.trim(MapUtils
									.getString(l161s01aMap, "RESCUEIBDATE"));
							String ISCBREFIN_161 = Util.trim(MapUtils
									.getString(l161s01aMap, "ISCBREFIN"));
							String RESCUEITEMSUB_161 = Util.trim(MapUtils
									.getString(l161s01aMap, "RESCUEITEMSUB"));
							String RESCUENO_161 = Util.trim(MapUtils.getString(
									l161s01aMap, "RESCUENO"));
							String EMPCOUNT_161 = Util.trim(MapUtils.getString(
									l161s01aMap, "EMPCOUNT"));
							String CBREFINDT_161 = Util.trim(MapUtils
									.getString(l161s01aMap, "CBREFINDT"));

							String ISTURNOVERDECREASED = Util.trim(MapUtils
									.getString(l161s01aMap,
											"ISTURNOVERDECREASED", ""));

							CBREFINDT_161 = MapUtils.getString(
									lms140_cbRefinDt, Util.trim(CBREFINDT_161),
									null);

							// // 放舊的109年度資料
							rescueItem = rescueItemOn;
							rescueRate = Util.equals(RESCUERATE_161, "") ? null
									: Util.parseBigDecimal(RESCUERATE_161);
							rescueIbDate = Util.equals(RESCUEIBDATE_161, "") ? null
									: RESCUEIBDATE_161;

							rescueNo = RESCUENO_161;

							isTurnoverDecreased = Util.notEquals(
									ISTURNOVERDECREASED, "") ? ISTURNOVERDECREASED
									: isTurnoverDecreased;

							// J-111-0634_05097_B1001
							// 加強控管e-loan之出口押匯額度簽案利率方式皆不得選取「固定利率」
							// 判斷有沒有增補合約*************************************************************************
							// 拿舊的出來看

							// 當有延續補貼時(ELF383 前、後都有時)，覆核時:
							// 1.判斷ELF383前面的紓困代碼，抓最新一筆已覆核動審表的增補合約
							// 2.如果有增補，判斷當天的覆核日與補合約生效日(調升起息日)，是否已生效
							// (1).調升起息日 <= 今天覆核日，代表生效，
							// A.要比大小，
							// B.若ELF383前面的紓困利率，比增補之調升利率還低時，以增補後利率取代，否則就不變動
							// (2).調升起息日 > 今天覆核日，代表還沒生效，
							// A.若本次覆核的動審表沒有增補合約時
							// B.前次的增補合約生效日(調升起息日) < 右邊新的紓困代碼的合意展延日RESCUEIBDT_N
							// C.則ELF383的RESCUE_C_FG, RESCUE_C_SD, RESCUE_C_RT,
							// RESCUE_C_ED改放前次的增補欄位資訊

							// 此時，rescueItem 、rescueRate
							// 已經是放舊的資料了(ELF383的前面)，所以直接用來判斷
							if (Util.notEquals(rescueItem, "")) {
								List<Map<String, Object>> chgL161s01aList = eloanDbBaseService
										.findL161s01aLastRescueDataWithRescueItem(
												cntrNoOn, custId, dupNo,
												rescueItem); // 依照核准日期降利排序

								if (chgL161s01aList != null
										&& !chgL161s01aList.isEmpty()) {
									for (Map<String, Object> chgL161s01aMap : chgL161s01aList) {
										String xRESCUECHGRATEFG = Util
												.trim(MapUtils.getString(
														chgL161s01aMap,
														"RESCUECHGRATEFG"));

										// 舊的紓困帶碼曾經有增補合約時才要判斷要不要執行下面的調整
										if (Util.equals(xRESCUECHGRATEFG, "Y")) {
											String xRESCUECHGRATESINGDATE = Util
													.trim(MapUtils
															.getString(
																	chgL161s01aMap,
																	"RESCUECHGRATESINGDATE")); // 調升簽約日
											String xRESCUECHGRATE = Util
													.trim(MapUtils.getString(
															chgL161s01aMap,
															"RESCUECHGRATE")); // 調整後利率
											String xRESCUECHGRATEEFFECTDATE = Util
													.trim(MapUtils
															.getString(
																	chgL161s01aMap,
																	"RESCUECHGRATEEFFECTDATE")); // 生效日

											BigDecimal xRescueChgRate = Util
													.equals(xRESCUECHGRATE, "") ? null
													: Util.parseBigDecimal(xRESCUECHGRATE); // 調整後利率

											if (xRescueChgRate != null
													&& Util.notEquals(
															xRESCUECHGRATEEFFECTDATE,
															"")) {
												// 判斷當天的覆核日與補合約生效日是否已生效
												if (Util.notEquals(
														xRESCUECHGRATEEFFECTDATE,
														"")) {
													String toDay = CapDate
															.getCurrentDate("yyyy-MM-dd");

													if (LMSUtil
															.cmpDate(
																	Util.parseDate(xRESCUECHGRATEEFFECTDATE),
																	"<=",
																	Util.parseDate(toDay))) {
														// 調升起息日 <=
														// 今天覆核日，代表生效，直接放增補後利率
														// 要比大小，比增補低時才要放
														if (xRescueChgRate
																.compareTo(rescueRate) > 0) {
															rescueRate = xRescueChgRate;
														}

													} else {
														// 調升起息日 > 今天覆核日，代表
														// 還沒生效，改放增補資訊
														if (this.needRescueChgRateFg(rescueItem)) {

															// 本次覆核的動審表沒有增補合約時才要放
															if (Util.notEquals(
																	RESCUE_C_FG,
																	"Y")) {

																// B.前次的增補合約生效日(調升起息日)
																// <
																// 右邊新的紓困代碼的合意展延日RESCUEIBDT_N

																if (LMSUtil
																		.cmpDate(
																				Util.parseDate(xRESCUECHGRATEEFFECTDATE),
																				"<",
																				Util.parseDate(rescueIbDateN))) {
																	RESCUE_C_FG = xRESCUECHGRATEFG;
																	RESCUE_C_SD = Util
																			.parseDate(xRESCUECHGRATESINGDATE);
																	RESCUE_C_RT = xRescueChgRate;
																	RESCUE_C_ED = Util
																			.parseDate(xRESCUECHGRATEEFFECTDATE);
																}

															}

														}

													}
												}

											}

											break;
										}

									}
								}

							}

							break;

						}
					}

					// List<Map<String, Object>> elf383List = misQuotapprService
					// .findByLastRescue(custId, dupNo, cntrNoOn, "Y",
					// rescueItemOn);
					//
					// if (elf383List != null && !elf383List.isEmpty()) {
					// for (Map<String, Object> elf383Map : elf383List) {
					//
					// String ISRESCUE_383 = Util.trim(MapUtils.getString(
					// elf383Map, "ISRESCUE"));
					// String RESCUEITEM_383 = Util.trim(MapUtils
					// .getString(elf383Map, "RESCUEITEM"));
					//
					// String RESCUERATE_383 = Util.trim(MapUtils
					// .getString(elf383Map, "RESCUERATE"));
					// String RESCUEIBDT_383 = Util.trim(MapUtils
					// .getString(elf383Map, "RESCUEIBDT"));
					// String ISCBREFIN_383 = Util.trim(MapUtils
					// .getString(elf383Map, "ISCBREFIN"));
					// String RESCUEITEM_SUB_383 = Util.trim(MapUtils
					// .getString(elf383Map, "RESCUEITEM_SUB"));
					// String RESCUENO_383 = Util.trim(MapUtils.getString(
					// elf383Map, "RESCUENO"));
					// String EMPCOUNT_383 = Util.trim(MapUtils.getString(
					// elf383Map, "EMPCOUNT"));
					// String ISSMALLBUS_383 = Util.trim(MapUtils
					// .getString(elf383Map, "ISSMALLBUS"));
					// String CBREFINDT_383 = Util.trim(MapUtils
					// .getString(elf383Map, "CBREFINDT"));
					// String RESCUEINDS_383 = Util.trim(MapUtils
					// .getString(elf383Map, "RESCUEINDS"));
					// String RESCUECITY_383 = Util.trim(MapUtils
					// .getString(elf383Map, "RESCUECITY"));
					//
					// rescueItem = rescueItemOn;// 放舊的109年度資料
					// rescueRate = Util.equals(RESCUERATE_383, "") ? null
					// : Util.parseBigDecimal(RESCUERATE_383); // 放舊的109年度資料
					//
					// break;
					//
					// }
					// }

				}
			}

			if (Util.equals(isRescue, "Y")) {
				// ELOAN 用A04 A05 A06 但是ALOAN 用 A01 A02 A03 + rescue110 = 'Y'
				// 來代表110年度新紓困方案

				// ELOAN 轉 ALOAN 紓困代碼
				// ELOAN 用A04 A05 A06 但是ALOAN 用 A01 A02 A03 + rescue110 = 'Y'
				// 來代表110年度新紓困方案
				// 1.先用原來的紓困代碼判斷是不是110年
				if (lmsService.isResueItemSubSidy(rescueItem, "110")) {
					rescue110 = "Y";
				}

				// J-111-0112_05097_B1002 Web e-Loan企金授信管理系統新增111年經濟部紓困方案
				if (lmsService.isResueItemSubSidy(rescueItem, "111")) {
					rescue110 = "Y";
				}

				// 2.執行轉換,把A04轉成A01,A05->A02,A06->A03
				// rescueItem = Util.trim(lmsService
				// .resueItemSubSidyMapping(rescueItem));
				// rescueItemN = Util.trim(lmsService
				// .resueItemSubSidyMapping(rescueItemN));
			}

		}

		// J-110-0182_05097_B1001 Web e-Loan國內企金授信配合經濟部紓困貸款更改為非紓困案仍需持續補貼，修改相關程式。
		// (110) 第 1224
		// 號經濟部紓困貸款(舊案展延、營運資金貸款、振興資金貸款)，如案件續約且已更改為非紓困案，原先之紓困貸款仍依持續補貼至原紓困之到期日。
		// 判斷ELF383該額度是否有紓困案，且有紓困案的最後一筆是 A01，且本次

		String LMS_RESCUEITEM_A01_QUOTAPPR_ON = Util.trim(lmsService
				.getSysParamDataValue("LMS_RESCUEITEM_A01_QUOTAPPR_ON"));

		if (Util.equals(LMS_RESCUEITEM_A01_QUOTAPPR_ON, "Y")) {

			if (Util.equals(isRescue, "N")) {

				// 這次簽案是N
				// 判斷ELF383

				// ELF383曾經為Y且為A01或A04

				// 補ALOAN最後一段補貼

				List<Map<String, Object>> l161s01aList = eloanDbBaseService
						.findL161s01aLastRescueDataWithoutRescueItem(cntrNo,
								custId, dupNo);

				if (l161s01aList != null && !l161s01aList.isEmpty()) {
					for (Map<String, Object> l161s01aMap : l161s01aList) {
						// 判斷倒數最後一筆是紓困的代碼

						String ISRESCUE_161 = Util.trim(MapUtils.getString(
								l161s01aMap, "ISRESCUE"));
						String RESCUEITEM_161 = Util.trim(MapUtils.getString(
								l161s01aMap, "RESCUEITEM"));

						String RESCUERATE_161 = Util.trim(MapUtils.getString(
								l161s01aMap, "RESCUERATE"));
						String RESCUEIBDATE_161 = Util.trim(MapUtils.getString(
								l161s01aMap, "RESCUEIBDATE"));
						String ISCBREFIN_161 = Util.trim(MapUtils.getString(
								l161s01aMap, "ISCBREFIN"));
						String RESCUEITEMSUB_161 = Util.trim(MapUtils
								.getString(l161s01aMap, "RESCUEITEMSUB"));
						String RESCUENO_161 = Util.trim(MapUtils.getString(
								l161s01aMap, "RESCUENO"));
						String EMPCOUNT_161 = Util.trim(MapUtils.getString(
								l161s01aMap, "EMPCOUNT"));
						String CBREFINDT_161 = Util.trim(MapUtils.getString(
								l161s01aMap, "CBREFINDT"));
						String ISSMALLBUSS_161 = Util.trim(MapUtils.getString(
								l161s01aMap, "ISSMALLBUSS"));
						String RESCUEINDUSTRY_161 = Util.trim(MapUtils
								.getString(l161s01aMap, "RESCUEINDUSTRY"));
						String RESCUECITY_161 = Util.trim(MapUtils.getString(
								l161s01aMap, "RESCUECITY"));

						// J-111-0112_05097_B1002 Web
						// e-Loan企金授信管理系統新增111年經濟部紓困方案
						// 如果最後一筆是A01 或 A04
						if (Util.equals(ISRESCUE_161, "Y")
								&& (Util.equals(RESCUEITEM_161, "A01")
										|| Util.equals(RESCUEITEM_161, "A04") || Util
										.equals(RESCUEITEM_161, "A08"))) {
							// 改以上次的ELF383資料上傳本次ELF383
							isRescue = ISRESCUE_161;
							rescueItem = RESCUEITEM_161;
							rescueRate = Util.equals(RESCUERATE_161, "") ? null
									: Util.parseBigDecimal(RESCUERATE_161);
							rescueIbDate = Util.equals(RESCUEIBDATE_161, "") ? null
									: RESCUEIBDATE_161;
							isCbRefin = ISCBREFIN_161;
							rescueItemSub = RESCUEITEMSUB_161;
							rescueNo = RESCUENO_161;
							empCount = Util.equals(EMPCOUNT_161, "") ? null
									: Util.parseBigDecimal(EMPCOUNT_161);
							isSmallBuss = ISSMALLBUSS_161;

							cbRefinDt = Util.equals(CBREFINDT_161, "") ? null
									: MapUtils.getString(lms140_cbRefinDt,
											Util.trim(CBREFINDT_161), null);

							rescueIndustry = RESCUEINDUSTRY_161;
							rescueCity = RESCUECITY_161;

							if (lmsService
									.isResueItemSubSidy(rescueItem, "110")) {
								rescue110 = "Y";
							}

							// J-111-0112_05097_B1002 Web
							// e-Loan企金授信管理系統新增111年經濟部紓困方案
							if (lmsService
									.isResueItemSubSidy(rescueItem, "111")) {
								rescue110 = "Y";
							}

							rescueItemN = "";
							rescueRateN = null;

						}

						break;

					}
				}

				// List<Map<String, Object>> elf383List = misQuotapprService
				// .findByLastRescue(custId, dupNo, cntrNo, "Y", "A01");
				// if (elf383List == null || elf383List.isEmpty()) {
				// elf383List = misQuotapprService.findByLastRescue(custId,
				// dupNo, cntrNo, "Y", "A04");
				// }
				//
				// if (elf383List != null && !elf383List.isEmpty()) {
				// for (Map<String, Object> elf383Map : elf383List) {
				//
				// String ISRESCUE_383 = Util.trim(MapUtils.getString(
				// elf383Map, "ISRESCUE"));
				// String RESCUEITEM_383 = Util.trim(MapUtils.getString(
				// elf383Map, "RESCUEITEM"));
				//
				// String RESCUERATE_383 = Util.trim(MapUtils.getString(
				// elf383Map, "RESCUERATE"));
				// String RESCUEIBDT_383 = Util.trim(MapUtils.getString(
				// elf383Map, "RESCUEIBDT"));
				// String ISCBREFIN_383 = Util.trim(MapUtils.getString(
				// elf383Map, "ISCBREFIN"));
				// String RESCUEITEM_SUB_383 = Util.trim(MapUtils
				// .getString(elf383Map, "RESCUEITEM_SUB"));
				// String RESCUENO_383 = Util.trim(MapUtils.getString(
				// elf383Map, "RESCUENO"));
				// String EMPCOUNT_383 = Util.trim(MapUtils.getString(
				// elf383Map, "EMPCOUNT"));
				// String ISSMALLBUS_383 = Util.trim(MapUtils.getString(
				// elf383Map, "ISSMALLBUS"));
				// String CBREFINDT_383 = Util.trim(MapUtils.getString(
				// elf383Map, "CBREFINDT"));
				// String RESCUEINDS_383 = Util.trim(MapUtils.getString(
				// elf383Map, "RESCUEINDS"));
				// String RESCUECITY_383 = Util.trim(MapUtils.getString(
				// elf383Map, "RESCUECITY"));
				//
				// String RESCUE_110_383 = Util.trim(MapUtils.getString(
				// elf383Map, "RESCUE_110"));
				// String RESCUEITEM_N_383 = Util.trim(MapUtils.getString(
				// elf383Map, "RESCUEITEM_N"));
				// String RESCUERATE_N_383 = Util.trim(MapUtils.getString(
				// elf383Map, "RESCUERATE_N"));
				//
				// // 改以上次的ELF383資料上傳本次ELF383
				// isRescue = ISRESCUE_383;
				// rescueItem = RESCUEITEM_383;
				// rescueRate = Util.equals(RESCUERATE_383, "") ? null
				// : Util.parseBigDecimal(RESCUERATE_383);
				// rescueIbDate = Util.equals(RESCUEIBDT_383, "") ? null
				// : RESCUEIBDT_383;
				// isCbRefin = ISCBREFIN_383;
				// rescueItemSub = RESCUEITEM_SUB_383;
				// rescueNo = RESCUENO_383;
				// empCount = Util.equals(EMPCOUNT_383, "") ? null : Util
				// .parseBigDecimal(EMPCOUNT_383);
				// isSmallBuss = ISSMALLBUS_383;
				// cbRefinDt = Util.equals(CBREFINDT_383, "") ? null
				// : CBREFINDT_383;
				// rescueIndustry = RESCUEINDS_383;
				// rescueCity = RESCUECITY_383;
				//
				// rescue110 = RESCUE_110_383;
				// rescueItemN = RESCUEITEM_N_383;
				// rescueRateN = Util.equals(RESCUERATE_N_383, "") ? null
				// : Util.parseBigDecimal(RESCUERATE_N_383);
				//
				// break;
				//
				// }
				//
				// }

			}
		}

		// 紓困資料轉換
		// END***********************************************************************************************************************

		logger.info(
				"custId={},dupNo={},cntrNo={},sDate={},caseType={},lnFlag={},oldAmt={},curAmt={},oldCurr={},curCurr={},lnqtFlag={},reclFlag={},sguFlag={},New_LNRTYPE={},llnNo={},llnfDate={},llneDate={},llnmon={},lnuseNo={},useFmDt={},useEnDt={},useFtMn={},memo={},grantNo={},commborw={},updater={},reclChg={},sguChg={},gutFlag={},gutper={},llneFlag={},useeFlag={},lnnoflag={},unichgFlag={},reFlag={},unionAmt={},shareAmt={},permitType={},hideunion={},setDate={},UArea={},unionRole={},riskArea={},existdate={},feedate={},countrydt={},crdttbl={},mowType={},mowtbl1={},syndipfd={},coKind={},cntrnom={},rclno={},documentNo={},crdtYmd={},crdtBr={},mowYmd={},mowbr={},controlcd={},duringFlag={},ltvRate={},locationcd={},jcicMark={},promise={},factType={},commsno={},chinaivt={},chinacur={},chinaamt={},signamt={},noisurea={},noisuort={},noisudesp={},plusreaso={},residential={},buildYN={},nowAMT={},valueAMT={},commonYN={},shareCollYN={},shareCollAmt={},isLimitCust={},isHighHouse={},houseYN={},houseType={},purposeType={},cmsType={},keepYN={},plusReasonMeMo={},sit3No={},sit4No={},cmsOther={},loanPer={},cbHlChk={},appAmt={},applyDate={},unsecureFlag={},isEfin={},prodKind={},isNonSMEProjLoan={},nonSMEProjLoanAmt={},exceptFlag={},itwCode={},inSmeFg={},inSmeToAmt={},inSmeCaAmt={},isHedge={},enhanceAmt={},netSwft={},netAmt={},netAmtUnit={},cgfRate={},cgfDate={},projClass={},experf_fg={},flaw_fg={},flaw_amt={},isRescue={},rescueItem={},rescueRate={},rescueIbDate={},isCbRefin={},rescueItemSub={},rescueNo={},empCount={},isSmallBuss={},isSole={},soleType={},hasRegis={},isRevive={},revTarget={},revSubItem={},revPurpose={},cbRefinDt={},rescueIndustry={},rescueCity={},version20201208={},realEstateLoanLimitReason={},isRescue_el={},rescue110={},rescueItemN={},rescueRateN={},rescueItem_el={},rescueIbDateN={},rescueNoN={},rescueNdfGutPercent={},isTurnoverDecreased={},rescueSn={},esggFg={},esggType={},esgsFg={},esgsType={},esgsUnre={},hLoanLimit_2={},endDate={},itwCodeCoreBuss={},cntrLgd={},stdAuth={},depositFg={},RESCUE_C_FG={},RESCUE_C_SD={},RESCUE_C_RT={},RESCUE_C_ED={}, isRenew={}, isPayOldQuota={}, oldQuota={}, payOldAmt={}, payOldAmtItem={}, isMatchUnsoldHouseItem={}, isSaleCase={}, cbControlLstDate={}, timeVal={}, gutType={}, batGutSeq={}, batGutNo={},socialFlag={}, socialKind={}, socialTa={}, socialResp={}",
				new Object[] { custId, dupNo, cntrNo, sDate, caseType, lnFlag,
						oldAmt, curAmt, oldCurr, curCurr, lnqtFlag, reclFlag,
						sguFlag, New_LNRTYPE, llnNo, llnfDate, llneDate,
						llnmon, lnuseNo, useFmDt, useEnDt, useFtMn, memo,
						grantNo, commborw, updater, reclChg, sguChg, gutFlag,
						gutper, llneFlag, useeFlag, lnnoflag, unichgFlag,
						reFlag, unionAmt, shareAmt, permitType, hideunion,
						setDate, UArea, unionRole, riskArea, existdate,
						feedate, countrydt, crdttbl, mowType, mowtbl1,
						syndipfd, coKind, cntrnom, rclno, documentNo, crdtYmd,
						crdtBr, mowYmd, mowbr, controlcd, duringFlag, ltvRate,
						locationcd, jcicMark, promise, factType, commsno,
						chinaivt, chinacur, chinaamt, signamt, noisurea,
						noisuort, noisudesp, plusreason, residential, buildYN,
						nowAMT, valueAMT, commonYN, shareCollYN, shareCollAmt,
						isLimitCust, isHighHouse, houseYN, houseType,
						purposeType, cmsType, keepYN, plusReasonMeMo, sit3No,
						sit4No, cmsOther, loanPer, cbHlChk, appAmt, applyDate,
						riskFactors, riskActAmt, reViewDate, reViewChg1,
						unsecureFlag, isEfin, prodKind, isNonSMEProjLoan,
						nonSMEProjLoanAmt, exceptFlag, itwCode, inSmeFg,
						inSmeToAmt, inSmeCaAmt, isHedge, enhanceAmt, netSwft,
						netAmt, netAmtUnit, cgfRate, cgfDate, projClass,
						experf_fg, flaw_fg, flaw_amt, isRescue, rescueItem,
						rescueRate, rescueIbDate, isCbRefin, rescueItemSub,
						Util.trimSizeInOS390(Util.trim(rescueNo), 40),
						empCount, isSmallBuss, isSole, soleType, hasRegis,
						isRevive, revTarget, revSubItem, revPurpose, cbRefinDt,
						rescueIndustry, rescueCity, version20201208,
						realEstateLoanLimitReason, isRescue_el, rescue110,
						rescueItemN, rescueRateN, rescueItem_el, rescueIbDateN,
						rescueNoN, rescueNdfGutPercent, isTurnoverDecreased,
						rescueSn, esggFg, esggType, esgsFg, esgsType, esgsUnre,
						hLoanLimit_2, endDate, itwCodeCoreBuss, cntrLgd,
						stdAuth, depositFg, RESCUE_C_FG, RESCUE_C_SD,
						RESCUE_C_RT, RESCUE_C_ED, isRenew, isPayOldQuota,
						oldQuota, payOldAmt, payOldAmtItem,
						isMatchUnsoldHouseItem, isSaleCase, lstDate, timeVal,
						gutType, batGutSeq, batGutNo,
						socialFlag, socialKind, socialTa, socialResp});
		misQuotapprService.insertForInside(custId, dupNo, cntrNo, sDate,
				caseType, lnFlag, oldAmt, curAmt, oldCurr, curCurr, lnqtFlag,
				reclFlag, sguFlag, New_LNRTYPE, llnNo, llnfDate, llneDate,
				llnmon, lnuseNo, useFmDt, useEnDt, useFtMn, memo, grantNo,
				commborw, updater, reclChg, sguChg, gutFlag, gutper, llneFlag,
				useeFlag, lnnoflag, unichgFlag, reFlag, unionAmt, shareAmt,
				permitType, hideunion, setDate, UArea, unionRole, riskArea,
				existdate, feedate, countrydt, crdttbl, mowType, mowtbl1,
				syndipfd, coKind, cntrnom, rclno, documentNo, crdtYmd, crdtBr,
				mowYmd, mowbr, controlcd, duringFlag, ltvRate, locationcd,
				jcicMark, promise, factType, commsno, chinaivt, chinacur,
				chinaamt, signamt, noisurea, noisuort, noisudesp, plusreason,
				residential, buildYN, nowAMT, valueAMT, commonYN, shareCollYN,
				shareCollAmt, isLimitCust, isHighHouse, houseYN, houseType,
				purposeType, cmsType, keepYN, plusReasonMeMo, sit3No, sit4No,
				cmsOther, loanPer, cbHlChk, appAmt, applyDate, riskFactors,
				riskActAmt, reViewDate, reViewChg1, unsecureFlag, isEfin,
				prodKind, isNonSMEProjLoan, nonSMEProjLoanAmt, exceptFlag,
				itwCode, inSmeFg, inSmeToAmt, inSmeCaAmt, isHedge, enhanceAmt,
				netSwft, netAmt, netAmtUnit, cgfRate, cgfDate, projClass,
				experf_fg, flaw_fg, flaw_amt, isRescue, rescueItem, rescueRate,
				rescueIbDate, isCbRefin, rescueItemSub, rescueNo, empCount,
				isSmallBuss, isSole, soleType, hasRegis, isRevive, revTarget,
				revSubItem, revPurpose, cbRefinDt, rescueIndustry, rescueCity,
				version20201208, realEstateLoanLimitReason, isRescue_el,
				rescue110, rescueItemN, rescueRateN, rescueItem_el,
				rescueIbDateN, rescueNoN, rescueNdfGutPercent,
				isTurnoverDecreased, rescueSn, esggFg, esggType, esgsFg,
				esgsType, esgsUnre, hLoanLimit_2, endDate, itwCodeCoreBuss,
				cntrLgd, stdAuth, depositFg, RESCUE_C_FG, RESCUE_C_SD,
				RESCUE_C_RT, RESCUE_C_ED, isRenew, isPayOldQuota, oldQuota,
				payOldAmt, payOldAmtItem, isMatchUnsoldHouseItem, isSaleCase,
				lstDate, timeVal, 
				socialFlag, socialKind, socialTa, socialResp);

		// J-112-0366_12473_B1001
		// J-109-0365_05097_B1001 Web
		// e-Loan國內企金授信額度明細表科目為遠期外匯、換匯交易時，新增是否徵提保證金等相關欄位
		misELF383NService.insertForInside(custId, dupNo, cntrNo, sDate,
				gutType, batGutSeq, batGutNo, marginFlag);
		if (upAS400) {
			// AS400無下列三欄 promise factType commsno
			obsdbELF383Service.deleteByKey(BRNID, custId, dupNo, cntrNo,
					sDate.replaceAll("\\D", ""));

			// J-111-0461_05097_B1002 授信額度合計新增瑕疵押匯額度、與合計總授信額度(授信額度+出口瑕疵額度)
			// G-111-0168_05097_B1001 新增海外分(子)行「綠色授信」及「永續績效連結授信」等註記
			// J-111-0461_05097_B1002 授信額度合計新增瑕疵押匯額度、與合計總授信額度(授信額度+出口瑕疵額度)
			// J-113-0329 企金授信新增社會責任授信
			obsdbELF383Service.insert(
					BRNID,
					LMSUtil.covertAs400Time(new Object[] { custId, dupNo,
							cntrNo, sDate, caseType, lnFlag, oldAmt, curAmt,
							oldCurr, curCurr, lnqtFlag, reclFlag, sguFlag,
							New_LNRTYPE,

							llnNo, llnfDate, llneDate, llnmon, lnuseNo,
							useFmDt, useEnDt, useFtMn,
							Util.trimSizeInOS390(memo, 40), grantNo, commborw,
							updater, CapDate.getCurrentTimestamp(), reclChg,
							sguChg, gutFlag, gutper,

							llneFlag, useeFlag, lnnoflag, unichgFlag, reFlag,
							unionAmt, shareAmt, permitType, hideunion, setDate,
							UArea, unionRole, riskArea, existdate, feedate,

							countrydt, crdttbl, mowType, mowtbl1, syndipfd,
							coKind, cntrnom, rclno, documentNo,
							forAS400crdtYmd, crdtBr, forAS400MowYmd, mowbr,
							modyDate, moodyGrd, spDate, spGrd, fitchDate,
							fitchGrd, controlcd, duringFlag, ltvRate,
							locationcd, jcicMark, riskFactors, riskActAmt,
							unsecureFlag, exceptFlag, isHedge, enhanceAmt,
							netSwft, netAmt, netAmtUnit, isRevive, revTarget,
							revSubItem, revPurpose, esggFg, esggType, esgsFg,
							esgsType, esgsUnre, esgGreenSpendType1,
							esgGreenSpendType2, esgGreenSpendType3,
							(cntrLgd == null ? -999.99 : cntrLgd), stdAuth,
							experf_fg, flaw_fg, flaw_amt,
							socialFlag, socialKind, socialTa, socialResp}));
		}

		// J-110-0182_05097_B1002 Web
		// e-Loan國內企金授信配合經濟部紓困貸款更改為非紓困案仍需持續補貼，修改相關程式。

		if ((Util.notEquals(Util.trim(lnFlag), "C") && Util.notEquals(
				Util.trim(lnFlag), "U"))
				&& Util.equals(Util.trim(isRescue), "Y")
				&& Util.notEquals(Util.getLeftStr(Util.trim(rescueItem), 1),
						"Z")
				&& (Util.notEquals(Util.trim(rescueItem), "") || (rescueRate != null && rescueRate
						.compareTo(BigDecimal.ZERO) != 0))) {

			// WHEN (N.ELF383_LNFLAG NOT IN('C','U') AND
			// N.ELF383_ISRESCUE = 'Y' AND
			// SUBSTR(N.ELF383_RESCUEITEM,1,1) <> 'Z' AND
			// (N.ELF383_RESCUEITEM <> ' ' OR
			// N.ELF383_RESCUERATE <> 0 ))

			String LMS_CALL_LN_LNSP0141_ON = Util.trim(lmsService
					.getSysParamDataValue("LMS_CALL_LN_LNSP0141_ON"));

			if (Util.equals(LMS_CALL_LN_LNSP0141_ON, "Y")) {

				// CALL LN.LNSP0141(ELF383_CUSTID || ELF383_DUPNO,
				// ELF383_CNTRNO,
				// ELF383_RESCUEITEM, VALUE(ELF383_RESCUERATE,0));

				Map<String, Object> lnsp0141Map = msps.callLNSP0141(custId,
						dupNo, cntrNo, rescueItem, rescueRate);

				// 沒有回傳值*************************************

				// if ("YES".equals(lnsp0141Map.get("SP_RETURN"))) {
				//
				// } else {
				// throw new CapMessageException("callLNSP0141 ERROR："
				// + lnsp0141Map.toString(), getClass());
				// }

			}
		}

		// J-109-0077_05097_B1001 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業

		// 保證 信保首次動用有效期限 是否可借新還舊 要補上傳到ELLNSEEK

		if (UtilConstants.DEFAULT.是.equals(l140m01a.getHeadItem1())) {

			String gutcDate = null;
			String projNO = "";
			String property = "";

			if (Util.isNotEmpty(l140m01a.getGutCutDate())) {
				// 檢查 信保案件之信保動用截止日
				String tproperty = "999";
				Boolean notCheck = false;
				// 如果當性質為不變或者是取消
				for (String x : l140m01a.getProPerty().split(
						UtilConstants.Mark.SPILT_MARK)) {
					if (UtilConstants.Cntrdoc.Property.不變.equals(x)) {
						notCheck = true;
						tproperty = UtilConstants.Cntrdoc.Property.不變;
						break;
					} else if (UtilConstants.Cntrdoc.Property.取消.equals(x)) {
						notCheck = true;
						tproperty = UtilConstants.Cntrdoc.Property.取消;
						break;
					}

					if (Integer.valueOf(x) < Integer.valueOf(tproperty)) {
						tproperty = x;
					}
				}

				// if (!notCheck) {
				gutcDate = CapDate.formatDate(l140m01a.getGutCutDate(),
						UtilConstants.DateFormat.YYYY_MM_DD);
				property = tproperty;
				projNO = LMSUtil.getUploadCaseNo(l120m01a);
				// }
			} else {
				gutcDate = null;
			}

			String isGuaOldCase = Util.trim(l140m01a.getIsGuaOldCase());
			String byNewOld = Util.equals(isGuaOldCase, "Y") ? Util
					.trim(l140m01a.getByNewOld()) : "";

			List<Map<String, Object>> ellnseeks = misEllnseekservice.findByKey(
					custId, dupNo, cntrNo);
			if (ellnseeks != null && !ellnseeks.isEmpty()) {
				misEllnseekservice.updateGutData(custId, dupNo, cntrNo,
						gutcDate, byNewOld, property, projNO);
			}

		}

		// J-111-0506_05097_B1001 Web e-Loan企金授信動審表增加授信作業手續費之欄位
		// 上傳是否收取企金授信作業手續費
		String isOperationFee = Util.trim(l161s01a.getIsOperationFee());
		String operationFeeCurr = Util.trim(l161s01a.getOperationFeeCurr());
		BigDecimal operationFeeAmt = l161s01a.getOperationFeeAmt() != null ? l161s01a
				.getOperationFeeAmt() : BigDecimal.ZERO;
		String operationFeeDueDate = l161s01a.getOperationFeeDueDate() != null ? CapDate
				.formatDate(l161s01a.getOperationFeeDueDate(),
						UtilConstants.DateFormat.YYYY_MM_DD) : "0001-01-01";

		if (Util.notEquals(isOperationFee, "")) {
			if (Util.notEquals(isOperationFee, "Y")) {
				operationFeeCurr = "";
				operationFeeAmt = BigDecimal.ZERO;
				operationFeeDueDate = "0001-01-01";
			}

			List<Map<String, Object>> ellnseeks = misEllnseekservice.findByKey(
					custId, dupNo, cntrNo);
			if (ellnseeks != null && !ellnseeks.isEmpty()) {
				misEllnseekservice.updateOperationFee(custId, dupNo, cntrNo,
						isOperationFee, operationFeeCurr, operationFeeAmt,
						operationFeeDueDate);
			}
		}

		// 不變不上傳
		if (!LMSUtil.isContainValue(l140m01a.getProPerty(),
				UtilConstants.Cntrdoc.Property.不變)) {
			Map<String, Object> elf447nMap = misELF447nService
					.findByUnidAndCntrNo(l120m01a.getMainId(),
							l140m01a.getCntrNo());
			if (elf447nMap != null && !elf447nMap.isEmpty()) {
				misELF447nService.updateRescueByUnidAndContract(isRescue,
						rescueItem, rescueRate, l120m01a.getMainId(),
						l140m01a.getCntrNo());
			} else {
				String countryno = l140m01a.getRiskArea();
				if (Util.isEmpty(countryno)) {
					countryno = "TW";
				}
				String areaNo = "";
				// 區域代碼
				List<Map<String, Object>> areaMap = this.dwdbBaseService
						.findDW_CNTRYAREAByCountryCode(countryno);
				if (!areaMap.isEmpty()) {
					for (Map<String, Object> data : areaMap) {
						areaNo = Util.trim((String) data.get("AREA_NO"));
					}
				}

				// 先INSERT
				// J-112-0456_05097_B1001 Web e-Loan授信系統增加簽報中屬不變的額度序號之簽報明細上送DW
				lmsService.gfnDB2ELF447N(l120m01a, l140m01a, areaNo,
						l120m01a.getDocStatus(), l120m01a.getCaseBrId(), true,
						false);

				// 再UPDATE
				misELF447nService.updateRescueByUnidAndContract(isRescue,
						rescueItem, rescueRate, l120m01a.getMainId(),
						l140m01a.getCntrNo());
			}
		}

	}

	/**
	 * 有無赴大陸投資轉換代碼
	 * 
	 * @param invMFlag
	 *            1.有、2.無、3.不適用
	 * @return Y ,N ,Null
	 */
	private String changeL120S01BInvMFlag(String invMFlag) {
		String result = "";
		if ("1".equals(invMFlag)) {
			result = UtilConstants.DEFAULT.是;
		} else if ("2".equals(invMFlag)) {
			result = UtilConstants.DEFAULT.否;
		}
		return result;
	}

	/**
	 * 上傳科(子)目及其限額檔QUOTSUB(AS400-ELF384)
	 * 
	 * @param BRNID
	 *            目前上傳分行
	 * @param l160m01a
	 *            動審表
	 * @param l140m01a
	 *            額度明細表
	 * @param l120m01a
	 *            案件簽報書
	 * @param updater
	 *            上傳者
	 * @param sDate
	 *            系統時間
	 * @param upAS400
	 *            是否上傳AS400
	 * @param codeTypeMap
	 *            授信科目代碼轉會計科目
	 * @param cntrNo
	 *            額度序號
	 */

	private void uploadQuotsub(String BRNID, L160M01A l160m01a,
			L140M01A l140m01a, L120M01A l120m01a, String updater, String sDate,
			Boolean upAS400, Map<String, String> codeTypeMap, String cntrNo) {
		lmsService.gfnQUOTSUBProcess(l140m01a, sDate, cntrNo, upAS400, updater,
				BRNID);
		L140M01A l140m01aOld = null;
		// BF 為轉入舊案直接給動審表引用
		String mainId = l140m01a.getMainId();
		L140M01A_BF l140m01a_bf = l140m01a_bfDao.findByUniqueKey(mainId);
		if (UtilConstants.Cntrdoc.DataSrc.條件續約變更產生
				.equals(l140m01a.getDataSrc())) {
			l140m01aOld = l140m01aDao.findByMainId(l140m01a.getMainIdSrc());
		}
		HashMap<String, String> newList = LMSUtil.getItemList(l140m01a);

		// 變更前項目
		HashMap<String, String> oldList = null;
		// 針對舊案轉檔處理
		if (l140m01a_bf != null) {
			List<L140M01C_BF> l140m01c_bfs = l140m01c_bfDao
					.findByMainId(mainId);
			List<L140M01D_BF> l140m01d_bfs = l140m01d_bfDao
					.findByMainId(mainId);
			oldList = LMSUtil.getItemListByBf(l140m01a_bf, l140m01c_bfs,
					l140m01d_bfs);
		} else {
			oldList = LMSUtil.getItemList(l140m01aOld);
		}

		// J-111-0499 為改善常見覆審建檔缺失, 增加有關額度建檔及利率建檔缺漏之檢核及訊息提示
		HashMap<String, String> lnapFlagMap = this.getIsLnapFlag(l140m01a);

		// (1)上傳授信科目
		for (L140M01C l140m01c : l140m01a.getL140m01c()) {
			String item = l140m01c.getLoanTP();
			String as400item = l140m01c.getLoanTP();

			BigDecimal lmtDays = Util.parseBigDecimal(l140m01c.getLmtDays());

			String lmtOther = Util.trim(l140m01c.getLmtOther());

			if (Util.equals(lmtOther, "")) {
				if (lmtDays.compareTo(BigDecimal.ZERO) == 0) {
					lmtDays = BigDecimal.valueOf(999);
				}

				if (lmtDays.compareTo(BigDecimal.valueOf(999)) > 0) {
					lmtDays = BigDecimal.valueOf(999);
				}

			} else {
				lmtDays = BigDecimal.valueOf(999);
			}

			if (this.isNoUploadItem(item)) {
				continue;
			}
			item = item.substring(0, 3);

			// AS400上傳授信科目的部分要轉為會計科目 沒有科目的要帶8個0
			if (codeTypeMap.containsKey(item)) {
				as400item = codeTypeMap.get(item);
			} else {
				as400item = "00000000";
			}

			/**
			 * <pre>
			 *                               'Msgbox "(1) 申請科目 =" & Condition9
			 *                         T_OLDCURR =""     ' 原限額幣別
			 *                         T_OLDQUOTA = 0   ' 原限額
			 *                         T_NEWCURR = ""   ' 新限額幣別
			 *                         T_NEWQUOTA = 0  ' 新限額
			 *                         
			 *                               '--- 讀取新、舊限額幣別及金額 ---
			 *                         If Iselement(BFSnoList(X))  Then
			 *                               If Trim(BFSnoList(X))<>"" Then
			 *                                     T_OLDCURR = Left(BFSnoList(X),3)
			 *                                     T_OLDQUOTA= Cdbl(Mid(BFSnoList(X),4,Len(BFSnoList(X))))
			 *                               End If
			 *                         End If
			 *                         If Iselement(NewSnoList(X))  Then
			 *                               If Trim(NewSnoList(X))<>"" Then
			 *                                     T_NEWCURR = Left(NewSnoList(X),3)
			 *                                     T_NEWQUOTA= Cdbl(Mid(NewSnoList(X),4,Len(NewSnoList(X))))
			 *                               End If
			 *                         End If
			 * </pre>
			 */
			// 原限額幣別
			String T_OLDCURR = "";
			// 原限額
			BigDecimal T_OLDQUOTA = BigDecimal.ZERO;

			if (oldList.containsKey(item)) {
				String value = Util.trim(oldList.get(item));
				T_OLDCURR = Util.getLeftStr(value, 3);
				T_OLDQUOTA = Util.parseBigDecimal(value.substring(3,
						value.length()));
			}
			String T_NEWCURR = "";
			// 新限額
			BigDecimal T_NEWQUOTA = BigDecimal.ZERO;
			if (newList.containsKey(item)) {
				String value = Util.trim(newList.get(item));
				T_NEWCURR = Util.getLeftStr(value, 3);
				T_NEWQUOTA = Util.parseBigDecimal(value.substring(3,
						value.length()));
			}

			/**
			 * <pre>
			 *                                 ' --- 判斷科(子)目變更註記 ---
			 *                         If tRdoc.Property(0) = "1" Or (Not Iselement(BFSnoList(X))) Then
			 *                               tProperty = "1"        '--- 新增 : 性質為新增以及無變更前資料者都視為新增---
			 *                         Else
			 *                               If  T_NEWQUOTA > T_OLDQUOTA Then
			 *                                     tProperty = "3"   '--- 增額---
			 *                               Else
			 *                                     If  T_NEWQUOTA < T_OLDQUOTA Then
			 *                                           tProperty = "4"   '--- 減額---
			 *                                     Else
			 *                                           tProperty = "0"         '---  0.額度不變 ---
			 *                                     End If
			 *                               End If
			 *                         End If
			 * </pre>
			 */
			String tProperty = "0";
			if (LMSUtil.isContainValue(l140m01a.getProPerty(),
					UtilConstants.Cntrdoc.Property.新做)
					|| !oldList.containsKey(item)) {
				// '--- 新增 : 性質為新增以及無變更前資料者都視為新增---
				tProperty = "1";
			} else {
				switch (T_NEWQUOTA.compareTo(T_OLDQUOTA)) {
				case 1:
					// '--- 增額---
					tProperty = "3";
					break;
				case -1:
					// '--- 減額---
					tProperty = "4";
					break;
				case 0:
					// '--- 0.額度不變 ---
					tProperty = "0";
					break;
				}
			}

			if ("941".equals(item)) {
				item = "944";
			} else if ("912".equals(item)) {
				item = "947";
			} else if ("913".equals(item)) {
				item = "949";
			}

			// J-111-0499 為改善常見覆審建檔缺失, 增加有關額度建檔及利率建檔缺漏之檢核及訊息提示
			String lnapFlag = "N";
			if (lnapFlagMap != null) {
				lnapFlag = Util.nullToSpace(lnapFlagMap.get(Util
						.nullToSpace(l140m01c.getLoanTP())));
				if (Util.isEmpty(lnapFlag)) {
					lnapFlag = "N";
				}
			}

			if (misQuotsubService.selByUniqueKey(l140m01a.getCustId(),
					l140m01a.getDupNo(), cntrNo, sDate, item).isEmpty()) {
				misQuotsubService.insert(l140m01a.getCustId(),
						l140m01a.getDupNo(), cntrNo, sDate, item, tProperty,
						T_OLDCURR, T_OLDQUOTA.doubleValue(), T_NEWCURR,
						T_NEWQUOTA.doubleValue(), l140m01a.getSbjProperty(),
						updater, lmtDays, lnapFlag);
				if (upAS400) {
					obsdbELF384Service.insert(
							BRNID,
							LMSUtil.covertAs400Time(new Object[] {
									l140m01a.getCustId(), l140m01a.getDupNo(),
									cntrNo, sDate.replaceAll("\\D", ""),
									as400item, tProperty, T_OLDCURR,
									T_OLDQUOTA.doubleValue(), T_NEWCURR,
									T_NEWQUOTA.doubleValue(),
									l140m01a.getSbjProperty(), updater,
									CapDate.getCurrentTimestamp() }));
				}
			} else {
				misQuotsubService.update(tProperty, T_OLDCURR,
						T_OLDQUOTA.doubleValue(), T_NEWCURR,
						T_NEWQUOTA.doubleValue(), l140m01a.getSbjProperty(),
						updater, l140m01a.getCustId(), l140m01a.getDupNo(),
						cntrNo, sDate, item, lmtDays, lnapFlag);
				if (upAS400) {
					obsdbELF384Service.update(
							BRNID,
							LMSUtil.covertAs400Time(new Object[] { tProperty,
									T_OLDCURR, T_OLDQUOTA.doubleValue(),
									T_NEWCURR, T_NEWQUOTA.doubleValue(),
									l140m01a.getSbjProperty(), updater,
									CapDate.getCurrentTimestamp(),
									l140m01a.getCustId(), l140m01a.getDupNo(),
									cntrNo, sDate.replaceAll("\\D", ""),
									as400item }));
				}
			}

		}

		// (2)上傳科目限額

		for (L140M01D l140m01d : l140m01a.getL140m01d()) {
			// 科子目合併限額不上傳

			BigDecimal lmtDays = BigDecimal.valueOf(999);

			if (!"1".equals(l140m01d.getLmtType())) {
				continue;
			}

			String item = l140m01d.getSubject();
			String as400item = l140m01d.getSubject();
			// StringBuilder loanTp = new StringBuilder(0);
			// StringBuilder loanTpFor400 = new StringBuilder(0);

			if (this.isNoUploadItem(item)) {
				continue;
			}
			// AS400上傳授信科目的部分要轉為會計科目 沒有科目的要帶8個0
			if (codeTypeMap.containsKey(as400item)) {
				as400item = codeTypeMap.get(item);
			} else {
				as400item = "00000000";
			}

			item = item.substring(0, 3);

			/**
			 * <pre>
			 *                               'Msgbox "(1) 申請科目 =" & Condition9
			 *                         T_OLDCURR =""     ' 原限額幣別
			 *                         T_OLDQUOTA = 0   ' 原限額
			 *                         T_NEWCURR = ""   ' 新限額幣別
			 *                         T_NEWQUOTA = 0  ' 新限額
			 *                         
			 *                               '--- 讀取新、舊限額幣別及金額 ---
			 *                         If Iselement(BFSnoList(X))  Then
			 *                               If Trim(BFSnoList(X))<>"" Then
			 *                                     T_OLDCURR = Left(BFSnoList(X),3)
			 *                                     T_OLDQUOTA= Cdbl(Mid(BFSnoList(X),4,Len(BFSnoList(X))))
			 *                               End If
			 *                         End If
			 *                         If Iselement(NewSnoList(X))  Then
			 *                               If Trim(NewSnoList(X))<>"" Then
			 *                                     T_NEWCURR = Left(NewSnoList(X),3)
			 *                                     T_NEWQUOTA= Cdbl(Mid(NewSnoList(X),4,Len(NewSnoList(X))))
			 *                               End If
			 *                         End If
			 * </pre>
			 */
			// 原限額幣別
			String T_OLDCURR = "";
			// 原限額
			BigDecimal T_OLDQUOTA = BigDecimal.ZERO;

			if (oldList.containsKey(item)) {
				String value = Util.trim(oldList.get(item));
				T_OLDCURR = Util.getLeftStr(value, 3);
				T_OLDQUOTA = Util.parseBigDecimal(value.substring(3,
						value.length()));
			}
			String T_NEWCURR = "";
			// 新限額
			BigDecimal T_NEWQUOTA = BigDecimal.ZERO;
			if (newList.containsKey(item)) {
				String value = Util.trim(newList.get(item));
				T_NEWCURR = Util.getLeftStr(value, 3);
				T_NEWQUOTA = Util.parseBigDecimal(value.substring(3,
						value.length()));
			}

			/**
			 * <pre>
			 *                                 ' --- 判斷科(子)目變更註記 ---
			 *                         If tRdoc.Property(0) = "1" Or (Not Iselement(BFSnoList(X))) Then
			 *                               tProperty = "1"        '--- 新增 : 性質為新增以及無變更前資料者都視為新增---
			 *                         Else
			 *                               If  T_NEWQUOTA > T_OLDQUOTA Then
			 *                                     tProperty = "3"   '--- 增額---
			 *                               Else
			 *                                     If  T_NEWQUOTA < T_OLDQUOTA Then
			 *                                           tProperty = "4"   '--- 減額---
			 *                                     Else
			 *                                           tProperty = "0"         '---  0.額度不變 ---
			 *                                     End If
			 *                               End If
			 *                         End If
			 * </pre>
			 */
			String tProperty = "0";
			if (LMSUtil.isContainValue(l140m01a.getProPerty(),
					UtilConstants.Cntrdoc.Property.新做)
					|| !oldList.containsKey(item)) {
				// '--- 新增 : 性質為新增以及無變更前資料者都視為新增---
				tProperty = "1";
			} else {
				switch (T_NEWQUOTA.compareTo(T_OLDQUOTA)) {
				case 1:
					// '--- 增額---
					tProperty = "3";
					break;
				case -1:
					// '--- 減額---
					tProperty = "4";
					break;
				case 0:
					// '--- 0.額度不變 ---
					tProperty = "0";
					break;
				}
			}

			// If Y = "941" Or Y = "944" Then
			// Condition9 = Condition9 +
			// " (LOANTP = '941' OR LOANTP = '944'  ) "
			// Elseif Y = "912" Or Y = "947" Then
			// Condition9 = Condition9 +
			// " (LOANTP = '912' OR LOANTP = '947'  ) "
			// Elseif Y = "913" Or Y = "949" Then
			// Condition9 = Condition9 +
			// " (LOANTP = '913' OR LOANTP = '949'  ) "
			// Else
			// Condition9 = Condition9 + " LOANTP = '" + Y + "'"
			// End If

			if ("941".equals(item)) {
				item = "944";
			} else if ("912".equals(item)) {
				item = "947";
			} else if ("913".equals(item)) {
				item = "949";
			}

			// J-111-0499 為改善常見覆審建檔缺失, 增加有關額度建檔及利率建檔缺漏之檢核及訊息提示
			// 因為此段為 科目限額 l140m01d.getLmtType = '1' 是單選科目，所以 l140m01d.getSubject
			// 一定是單科目，直接拿來用
			String lnapFlag = "N";
			if (lnapFlagMap != null) {
				lnapFlag = Util.nullToSpace(lnapFlagMap.get(Util
						.nullToSpace(l140m01d.getSubject())));
				if (Util.isEmpty(lnapFlag)) {
					lnapFlag = "N";
				}
			}

			if (Util.notEquals(item, "")) {
				// AS400上傳要轉會計科目
				if (codeTypeMap.containsKey(as400item)) {
					as400item = codeTypeMap.get(item);
				}
				// loanTp.append("'").append(item).append("'");
				// loanTpFor400.append("'").append(as400item).append("'");
			}

			if (misQuotsubService.selByUniqueKey(l140m01a.getCustId(),
					l140m01a.getDupNo(), cntrNo, sDate, item).isEmpty()) {
				misQuotsubService.insert(l140m01a.getCustId(),
						l140m01a.getDupNo(), cntrNo, sDate, item, tProperty,
						T_OLDCURR, T_OLDQUOTA.doubleValue(), T_NEWCURR,
						T_NEWQUOTA.doubleValue(), l140m01a.getSbjProperty(),
						updater, lmtDays, lnapFlag);

			} else {
				misQuotsubService.update(tProperty, T_OLDCURR,
						T_OLDQUOTA.doubleValue(), T_NEWCURR,
						T_NEWQUOTA.doubleValue(), l140m01a.getSbjProperty(),
						updater, l140m01a.getCustId(), l140m01a.getDupNo(),
						cntrNo, sDate, item, null, lnapFlag);

			}
			if (upAS400) {
				if (obsdbELF384Service.selByUniqueKey(
						BRNID,
						LMSUtil.covertAs400Time(new Object[] {
								l140m01a.getCustId(), l140m01a.getDupNo(),
								cntrNo, sDate, as400item })).isEmpty()) {

					obsdbELF384Service.insert(
							BRNID,
							LMSUtil.covertAs400Time(new Object[] {
									l140m01a.getCustId(), l140m01a.getDupNo(),
									cntrNo, sDate.replaceAll("\\D", ""),
									as400item, tProperty, T_OLDCURR,
									T_OLDQUOTA.doubleValue(), T_NEWCURR,
									T_NEWQUOTA.doubleValue(),
									l140m01a.getSbjProperty(), updater,
									CapDate.getCurrentTimestamp() }));

				} else {
					obsdbELF384Service.update(
							BRNID,
							LMSUtil.covertAs400Time(new Object[] { tProperty,
									T_OLDCURR, T_OLDQUOTA.doubleValue(),
									T_NEWCURR, T_NEWQUOTA.doubleValue(),
									l140m01a.getSbjProperty(), updater,
									CapDate.getCurrentTimestamp(),
									l140m01a.getCustId(), l140m01a.getDupNo(),
									cntrNo, sDate.replaceAll("\\D", ""),
									as400item }));
				}
			}

		}

	}

	/**
	 * 處理上傳日期
	 * 
	 * @param date
	 * @return
	 */
	private String parseDate(Date date) {
		if (Util.isNotEmpty(date)) {
			return CapDate
					.formatDate(date, UtilConstants.DateFormat.YYYY_MM_DD);
		}
		return "0001-01-01";
	}

	/**
	 * 處理上傳日期
	 * 
	 * @param date
	 * @return
	 */
	private String parseDate(String date) {
		if (Util.isNotEmpty(date)) {
			return date;
		}
		return "0001-01-01";
	}

	/**
	 * 利率條件LN.LNF164
	 * 
	 * @param BRNID
	 *            AS400上傳分行
	 * @param l160m01a
	 *            動審表
	 * @param l140m01a
	 *            額度明細表
	 * @param l120m01a
	 *            簽報書
	 * @param updater
	 *            上傳者
	 * @param sDate
	 *            目前上傳時前
	 * @param dataList
	 *            MIS OBJECT[]
	 * @param as400List
	 *            as400 OBJECT[]
	 * @param upAS400
	 *            是否上傳as400
	 * @param cntrNo
	 *            額度序號
	 */
	private void uploadLNF164(String BRNID, L160M01A l160m01a,
			L140M01A l140m01a, L120M01A l120m01a, String updater, String sDate,
			List<Object[]> dataList, List<Object[]> as400List, boolean upAS400,
			String cntrNo) {
		HashMap<String, String> moneyMap = new HashMap<String, String>();
		moneyMap.put("1", "TWD");
		moneyMap.put("2", "USD");
		moneyMap.put("3", "JPY");
		moneyMap.put("4", "EUR");
		moneyMap.put("5", "");
		moneyMap.put("7", "THB");
		StringBuilder custid = new StringBuilder(0);
		custid.append(Util.trim(l140m01a.getCustId()));
		String intchgType = "";
		String intchgCycl = "";
		if (custid.length() == 8) {
			custid.append("  ").append(l140m01a.getDupNo());
		} else {
			custid.append(l140m01a.getDupNo());
		}

		for (L140M01F l140m01f : l140m01a.getL140m01f()) {

			if (Util.isEmpty(l140m01f.getLoanTPList())) {
				continue;
			}
			for (String item : l140m01f.getLoanTPList().split(
					UtilConstants.Mark.SPILT_MARK)) {
				for (L140M01G l140m01g : l140m01f.getL140m01g()) {
					switch (Util.parseInt(l140m01g.getRateChg1())) {
					case 1:
						intchgType = "W";
						intchgCycl = "1";
						break;
					case 2:
						intchgType = "M";
						intchgCycl = "1";
						break;

					case 3:
						intchgType = "M";
						intchgCycl = "3";
						break;
					case 4:
						intchgType = "M";
						intchgCycl = "6";
						break;
					case 5:
						intchgType = "M";
						intchgCycl = "9";
						break;
					case 6:
						intchgType = "Y";
						intchgCycl = "1";
						break;
					}
					String int_kind = "N";
					dataList.add(new Object[] {
							l140m01a.getOwnBrId(),
							cntrNo,
							custid.toString(),
							l160m01a.getTType(),
							moneyMap.get(l140m01g.getRateType()),
							Util.trimSizeInOS390(item, 3),
							l140m01g.getRateGetInt(),
							int_kind,
							Util.trimSizeInOS390(Util.toFullCharString(l140m01g
									.getRateInput()), 100),
							this.coverNumber(l140m01g.getRateValue()),
							l140m01g.getRateKind(),
							intchgType,
							intchgCycl,
							Util.trimSizeInOS390(Util.toFullCharString(l140m01g
									.getRateDscr()), 200) });
					if (upAS400) {
						obsdbELF164Service.insert(BRNID, l140m01a.getOwnBrId(),
								cntrNo, custid.toString(), l160m01a.getTType(),
								moneyMap.get(l140m01g.getRateType()), Util
										.trimSizeInOS390(item, 3), l140m01g
										.getRateGetInt(), int_kind, Util
										.trimSizeInOS390(Util
												.toFullCharString(l140m01g
														.getRateInput()), 70),
								this.coverNumber(l140m01g.getRateValue()),
								l140m01g.getRateKind(), intchgType, Util
										.trimSizeInOS390(
												l140m01g.getRateDscr(), 140));
					}

				}
				if (!Util.isEmpty(l140m01f.getL140m01h())) {
					// 11.-企金簽案費率檔 ELCRTBL
					this.uploadElcrtbl(BRNID, l140m01a, l140m01f.getL140m01h(),
							Util.trimSizeInOS390(item, 3), updater, sDate,
							upAS400, cntrNo);
				}

			}

		}
		// misLNF164Service.insert(dataList);
		custid.setLength(0);
	}

	/**
	 * 利率條件LN.LNF164 根據 利率結構化 上傳
	 * 
	 * @param BRNID
	 *            AS400上傳分行
	 * @param l160m01a
	 *            動審表
	 * @param l140m01a
	 *            額度明細表
	 * @param l120m01a
	 *            簽報書
	 * @param updater
	 *            上傳者
	 * @param sDate
	 *            目前上傳時前
	 * @param dataList
	 *            MIS OBJECT[]
	 * @param as400List
	 *            as400 OBJECT[]
	 * @param upAS400
	 *            是否上傳as400
	 * @param cntrNo
	 *            額度序號
	 */
	private void uploadLNF164ByL140M01N(String BRNID, L160M01A l160m01a,
			L140M01A l140m01a, L120M01A l120m01a, String updater, String sDate,
			List<Object[]> dataList, String cntrNo) {
		// 1新台幣、2美金、3日幣、4歐元、5人民幣、6澳幣、7港幣、Z雜幣
		HashMap<String, String> moneyMap = new HashMap<String, String>();
		moneyMap.put("1", "TWD");
		moneyMap.put("2", "USD");
		moneyMap.put("3", "JPY");
		moneyMap.put("4", "EUR");
		moneyMap.put("5", "CNY");
		moneyMap.put("6", "AUD");
		moneyMap.put("7", "HKD");
		moneyMap.put("Z", "OTH");
		StringBuilder custid = new StringBuilder(0);
		custid.append(Util.trim(l140m01a.getCustId()));
		String intchgType = "";
		String intchgCycl = "";
		if (custid.length() == 8) {
			custid.append("  ").append(l140m01a.getDupNo());
		} else {
			custid.append(l140m01a.getDupNo());
		}

		for (L140M01F l140m01f : l140m01a.getL140m01f()) {

			if (Util.isEmpty(l140m01f.getLoanTPList())) {
				continue;
			}
			for (String item : l140m01f.getLoanTPList().split(
					UtilConstants.Mark.SPILT_MARK)) {
				List<L140M01N> l140m01ns = l140m01nDao.findByMainIdAndRateSeq(
						l140m01f.getMainId(), l140m01f.getRateSeq());

				for (L140M01N l140m01n : l140m01ns) {
					String int_kind = "N";

					if ("Z".equals(l140m01n.getRateType())
							&& "@2".equals(l140m01n.getRateBase())) {
						int_kind = "Y";
					}
					dataList.add(new Object[] {
							l140m01a.getOwnBrId(),
							cntrNo,
							custid.toString(),
							l160m01a.getTType(),
							moneyMap.get(l140m01n.getRateType()),
							Util.trimSizeInOS390(item, 3),
							l140m01n.getRateGetInt(),
							int_kind,
							Util.trimSizeInOS390(Util.toFullCharString(l140m01n
									.getRateDscr()), 100),
							BigDecimal.ZERO,
							l140m01n.getRateKind(),
							intchgType,
							intchgCycl,
							Util.trimSizeInOS390(Util.toFullCharString(l140m01n
									.getRateDscr()), 200) });
				}
			}

		}
		// misLNF164Service.insert(dataList);
		custid.setLength(0);
	}

	/**
	 * 企金簽案費率檔 ELCRTBL(AS400-ELF476)
	 * 
	 * @param BRNID
	 *            as400上傳分行
	 * @param l140m01a
	 *            額度明細表主檔
	 * @param l140m01h
	 *            額度費率明細檔
	 * @param item
	 *            授信科目
	 * @param updater
	 *            更新者
	 * @param sDate
	 *            系統時間
	 * @param upAS400
	 *            是否上傳 as400
	 * @param cntrno
	 *            　額度序號 　　　　　　　
	 */
	private void uploadElcrtbl(String BRNID, L140M01A l140m01a,
			L140M01H l140m01h, String item, String updater, String sDate,
			boolean upAS400, String cntrno) {

		List<Object[]> elf476List = new ArrayList<Object[]>();
		List<Object[]> dataList = new ArrayList<Object[]>();

		if (!Util.isEmpty(l140m01h.getCpType())) {
			misELCRTBLService.delByKey(cntrno, item, sDate, "1");
			dataList.add(new Object[] {
					cntrno,
					item,
					sDate,
					"1",
					l140m01h.getCpType(),
					this.coverNumber(l140m01h.getCp1Rate()),
					this.coverNumber(l140m01h.getCp1Fee()),
					this.coverNumber(l140m01h.getCp2Rate1()),
					this.coverNumber(l140m01h.getCp2Rate2()),
					Util.trimSizeInOS390(
							Util.toFullCharString(l140m01h.getCpDes()), 80),
					"", 0.0, 0, "", 0, 0.0, "", 0, "", "", 0.0, 0, "", 0, 0.0,
					"", 0, "", "", 0.0, "", 0, 0.0, "", 0,

					"", updater });
			if (upAS400) {
				obsdbELF476Service.delByKey(BRNID, cntrno, item, CapDate
						.formatDate(CapDate.getDate(sDate,
								UtilConstants.DateFormat.YYYY_MM_DD),
								"yyyyMMdd"), "1");
				elf476List.add(new Object[] {
						cntrno,
						item,
						CapDate.getDate(sDate,
								UtilConstants.DateFormat.YYYY_MM_DD), "1",
						l140m01h.getCpType(),
						this.coverNumber(l140m01h.getCp1Rate()),
						this.coverNumber(l140m01h.getCp1Fee()),
						this.coverNumber(l140m01h.getCp2Rate1()),
						this.coverNumber(l140m01h.getCp2Rate2()),
						Util.trimSizeInOS390(l140m01h.getCpDes(), 60), "", 0,
						0, "", 0, 0, "", 0, "", "", 0, 0, "", 0, 0, "", 0, "",
						"", 0, "", 0, 0, "", 0, "", updater,
						CapDate.getCurrentTimestamp() });
			}

		}

		if (!Util.isEmpty(l140m01h.getCfType())) {
			misELCRTBLService.delByKey(cntrno, item, sDate, "2");
			dataList.add(new Object[] {
					cntrno,
					item,
					sDate,
					"2",
					"",
					0.0,
					0,
					0.0,
					0.0,
					"",
					Util.trim(l140m01h.getCfType()),
					this.coverNumber(l140m01h.getCf1Rate()),
					this.coverNumber(l140m01h.getCf1Mon1()),
					Util.trim(l140m01h.getCf1MD()),
					this.coverNumber(l140m01h.getCf1Mon2()),
					this.coverNumber(l140m01h.getCf2Rate()),
					Util.trim(l140m01h.getCf2MD()),
					this.coverNumber(l140m01h.getCf2Mon()),
					Util.trimSizeInOS390(
							Util.toFullCharString(l140m01h.getCfDes()), 80),
					"", 0.0, 0, "", 0, 0.0, "", 0, "", "", 0.0, "", 0, 0.0, "",
					0, "", updater });
			if (upAS400) {
				obsdbELF476Service.delByKey(BRNID, cntrno, item, CapDate
						.formatDate(CapDate.getDate(sDate,
								UtilConstants.DateFormat.YYYY_MM_DD),
								"yyyyMMdd"), "2");
				elf476List.add(new Object[] {
						cntrno,
						item,
						CapDate.getDate(sDate,
								UtilConstants.DateFormat.YYYY_MM_DD), "2", "",
						0, 0, 0, 0, "", l140m01h.getCfType(),
						coverNumber(l140m01h.getCf1Rate()),
						this.coverNumber(l140m01h.getCf1Mon1()),
						Util.trim(l140m01h.getCf1MD()),
						this.coverNumber(l140m01h.getCf1Mon2()),
						this.coverNumber(l140m01h.getCf2Rate()),
						Util.trim(l140m01h.getCf2MD()),
						this.coverNumber(l140m01h.getCf2Mon()),
						Util.trimSizeInOS390(l140m01h.getCfDes(), 60), "", 0,
						0, "", 0, 0, "", 0, "", "", 0, "", 0, 0, "", 0, "",
						updater, CapDate.getCurrentTimestamp() });

			}
		}

		if (!Util.isEmpty(l140m01h.getCpyType())) {
			misELCRTBLService.delByKey(cntrno, item, sDate, "3");
			dataList.add(new Object[] {
					cntrno,
					item,
					sDate,
					"3",
					"",
					0.0,
					0,
					0.0,
					0.0,
					"",
					"",
					0.0,
					0,
					"",
					0,
					0.0,
					"",
					0,
					"",
					l140m01h.getCpyType(),
					this.coverNumber(l140m01h.getCpy1Rate()),
					this.coverNumber(l140m01h.getCpy1Mon1()),
					Util.trim(l140m01h.getCpy1MD()),
					this.coverNumber(l140m01h.getCpy1Mon2()),
					this.coverNumber(l140m01h.getCpy2Rate()),
					Util.trim(l140m01h.getCpy2MD()),
					this.coverNumber(l140m01h.getCpy2Mon()),
					Util.trimSizeInOS390(
							Util.toFullCharString(l140m01h.getCpyDes()), 80),
					"", 0.0, "", 0, 0.0, "", 0, "", updater });
			if (upAS400) {
				obsdbELF476Service.delByKey(BRNID, cntrno, item, CapDate
						.formatDate(CapDate.getDate(sDate,
								UtilConstants.DateFormat.YYYY_MM_DD),
								"yyyyMMdd"), "3");
				elf476List.add(new Object[] {
						cntrno,
						item,
						CapDate.getDate(sDate,
								UtilConstants.DateFormat.YYYY_MM_DD), "3", "",
						0, 0, 0, 0, "", "", 0, 0, "", 0, 0, "", 0, "",
						l140m01h.getCpyType(),
						coverNumber(l140m01h.getCpy1Rate()),
						this.coverNumber(l140m01h.getCpy1Mon1()),
						Util.trim(l140m01h.getCpy1MD()),
						this.coverNumber(l140m01h.getCpy1Mon2()),
						this.coverNumber(l140m01h.getCpy2Rate()),
						Util.trim(l140m01h.getCpy2MD()),
						this.coverNumber(l140m01h.getCpy2Mon()),
						Util.trimSizeInOS390(l140m01h.getCpyDes(), 60), "", 0,
						"", 0, 0, "", 0, "", updater,
						CapDate.getCurrentTimestamp() });
			}

		}
		if (!Util.isEmpty(l140m01h.getPaType())) {
			misELCRTBLService.delByKey(cntrno, item, sDate, "4");
			dataList.add(new Object[] {
					cntrno,
					item,
					sDate,
					"4",
					"",
					0.0,
					0,
					0.0,
					0.0,
					"",
					"",
					0.0,
					0,
					"",
					0,
					0.0,
					"",
					0,
					"",
					"",
					0.0,
					0,
					"",
					0,
					0.0,
					"",
					0,
					"",
					Util.trim(l140m01h.getPaType()),
					this.coverNumber(l140m01h.getPa1Rate()),

					Util.trim(l140m01h.getPa1MD()),
					this.coverNumber(l140m01h.getPa1Mon()),
					this.coverNumber(l140m01h.getPa2Rate()),
					Util.trim(l140m01h.getPa2MD()),
					this.coverNumber(l140m01h.getPa2Mon()),
					Util.trimSizeInOS390(
							Util.toFullCharString(l140m01h.getPaDes()), 80),
					updater });
			if (upAS400) {
				obsdbELF476Service.delByKey(BRNID, cntrno, item, CapDate
						.formatDate(CapDate.getDate(sDate,
								UtilConstants.DateFormat.YYYY_MM_DD),
								"yyyyMMdd"), "4");
				elf476List.add(new Object[] {
						cntrno,
						item,
						CapDate.getDate(sDate,
								UtilConstants.DateFormat.YYYY_MM_DD), "4", "",
						0, 0, 0, 0, "", "", 0, 0, "", 0, 0, "", 0, "", "", 0,
						0, "", 0, 0, "", 0, "", l140m01h.getPaType(),
						coverNumber(l140m01h.getPa1Rate()),
						Util.trim(l140m01h.getPa1MD()),
						this.coverNumber(l140m01h.getPa1Mon()),
						this.coverNumber(l140m01h.getPa2Rate()),
						Util.trim(l140m01h.getPa2MD()),
						this.coverNumber(l140m01h.getPa2Mon()),
						Util.trimSizeInOS390(l140m01h.getPaDes(), 60), updater,
						CapDate.getCurrentTimestamp() });

			}
		}
		misELCRTBLService.insert(dataList);
		if (upAS400) {
			obsdbELF476Service.insert(BRNID,
					LMSUtil.covertAs400Time(elf476List));
		}
	}

	/**
	 * 轉換數值
	 * 
	 * @param value
	 *            Double
	 * @return Double
	 */
	private Double coverNumber(BigDecimal value) {
		if (value == null) {
			return BigDecimal.ZERO.doubleValue();
		}
		return value.doubleValue();
	}

	/**
	 * 轉換數值
	 * 
	 * @param value
	 *            Double
	 * @return Double
	 */
	private Double coverNumber(Double value) {

		return Util.isEmpty(value) ? 0.0 : value;
	}

	/**
	 * 轉換數值
	 * 
	 * @param value
	 *            Integer
	 * @return Integer
	 */
	private Integer coverNumber(Integer value) {

		return Util.isEmpty(value) ? 0 : value;
	}

	/**
	 * 檢查是否為需要上傳科目
	 * 
	 * @param item
	 *            授信科目
	 * @return boolean
	 */
	private boolean isNoUploadItem(String item) {
		// (1)額度明細表申請科目 ####
		// 科目代號只取前3碼，(第4碼為性質相同但名稱不同的科目)，
		// 第1碼為Z開頭的及代碼為7031者皆為虛擬科目，不上傳。

		// J-105-0340-001 Web e-Loan 交換票據抵用科目調整並上傳a-Loan
		if (Util.equals(lmsService.getSysParamDataValue("LMS_J1050340001_ON"),
				"Y")) {
			if (Util.isEmpty(Util.trim(item))
					|| ("Z".equals(item.substring(0, 1)) && !"Z15".equals(item))
					|| "7031".equals(item)) {
				return true;
			}
		} else {
			if (Util.isEmpty(Util.trim(item))
					|| "Z".equals(item.substring(0, 1)) || "7031".equals(item)) {
				return true;
			}
		}

		return false;
	}

	// /**
	// * 檢核 是否為續約、展期、提前續約者
	// *
	// * @param value
	// * 性質
	// * @return boolean
	// */
	// private boolean isSQCase(String value) {
	//
	// if (UtilConstants.Cntrdoc.Property.續約.equals(value)
	// || UtilConstants.Cntrdoc.Property.展期.equals(value)
	// || UtilConstants.Cntrdoc.Property.提前續約者.equals(value)) {
	// return true;
	// }
	// return false;
	// }

	@Override
	public List<L160M01A> findFirstUse(String[] docStatus, String ownBrId) {
		return l160m01aDao.findByFitstUse(docStatus, ownBrId);
	}

	@Override
	public void saveMain(List<L160M01B> l160m01bs, List<L162S01A> l162s01as,
			GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		save(entity);
		for (L160M01B l160m01b : l160m01bs) {
			l160m01b.setUpdateTime(CapDate.getCurrentTimestamp());
			l160m01b.setUpdater(user.getUserId());
		}
		for (L162S01A l162s01a : l162s01as) {
			l162s01a.setUpdateTime(CapDate.getCurrentTimestamp());
			l162s01a.setUpdater(user.getUserId());
		}
		l160m01bDao.save(l160m01bs);
		l162s01aDao.save(l162s01as);

	}

	@Override
	public void saveL160m01cs(List<L160M01C> l160m01cs, GenericBean... entity) {
		save(entity);
		l160m01cDao.save(l160m01cs);
	}

	@Override
	public HashMap<String, String> getCustCounty(L120M01A l120m01a) {

		StringBuilder custIdKey = new StringBuilder("");
		HashMap<String, String> custCounty = new HashMap<String, String>();
		// 1企金 2個金 、取得借款人國別
		if (UtilConstants.Casedoc.DocType.企金.equals(l120m01a.getDocType())) {
			List<L120S01B> l120s01bs = l120s01bDao.findByMainId(l120m01a
					.getMainId());

			for (L120S01B l120s01b : l120s01bs) {
				custIdKey.append(l120s01b.getCustId()).append(
						l120s01b.getDupNo());
				custCounty.put(custIdKey.toString(), l120s01b.getNtCode());
				custIdKey.setLength(0);
			}
		} else if (UtilConstants.Casedoc.DocType.個金.equals(l120m01a
				.getDocType())) {
			List<C120S01A> c120s01as = c120s01aDao.findByMainId(l120m01a
					.getMainId());
			for (C120S01A c120s01a : c120s01as) {
				custIdKey.append(c120s01a.getCustId()).append(
						c120s01a.getDupNo());
				custCounty.put(custIdKey.toString(), c120s01a.getNtCode());
				custIdKey.setLength(0);
			}

		}
		return custCounty;

	}

	/**
	 * 取得使用者姓名
	 * 
	 * @param userId
	 *            員編
	 * @return 姓名
	 */
	private String getUserName(String userId) {
		if (userId == null) {
			return "";
		}
		String result = userInfoService.getUserName(userId);
		if (Util.isEmpty(result)) {
			return userId;
		} else {
			return result;
		}
	}

	@Override
	public L160M01A findL160M01AByMaindId(String mainId) {
		return l160m01aDao.findByMainId(mainId);
	}

	@Override
	public List<L160M01A> findL160M01AByOids(String[] oids) {
		return l160m01aDao.findByOids(oids);
	}

	@Override
	public L160M01D findL160m01d(String mainId, String staffNo, String staffjob) {
		return l160m01dDao.findByUniqueKey(mainId, staffNo, staffjob);
	}

	@Override
	public List<VLUSEDOC01> getDoCntrNo(String caseMainId, String[] useBrId,
			String itemType) {
		return vusedoc01Dao.findUseCntrNo(caseMainId, useBrId, itemType);
	}

	@Override
	public List<VLUSEDOC01> getDoCntrNo(String caseMainId,
			String[] selectCntrNos, String[] useBrId, String itemType) {
		return vusedoc01Dao.findUseCntrNo(caseMainId, selectCntrNos, useBrId,
				itemType);
	}

	private BigDecimal gfnConvertZero(BigDecimal num) {
		if (num == null) {
			return BigDecimal.ZERO;
		}
		return num;
	}

	@Override
	public L160M01A getJoinMarketingSTR(String mainId) {

		List<L160M01B> l160m01bs = l160m01bDao.findByMainId(mainId);

		ArrayList<String> mainIds = new ArrayList<String>();
		for (L160M01B l160m01b : l160m01bs) {
			mainIds.add(l160m01b.getReMainId());
		}

		List<L140M01A> l140m01as = l140m01aDao
				.findL140m01aListByMainIdList(mainIds
						.toArray(new String[mainIds.size()]));
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1605M01Page.class);
		StringBuffer temp = new StringBuffer();
		StringBuffer totalStr = new StringBuffer();
		HashMap<String, String> custIdMap = new HashMap<String, String>();
		String key = "";
		Map<String, Object> row = null;
		for (L140M01A l140m01a : l140m01as) {
			key = l140m01a.getCustId().toUpperCase()
					+ l140m01a.getDupNo().toUpperCase();
			// 判斷是否已經處理過這借款人資料
			if (custIdMap.containsKey(key)) {
				continue;
			}
			custIdMap.put(key, "");
			row = missefService.getAll(l140m01a.getCustId(),
					l140m01a.getDupNo());
			temp.setLength(0);
			temp.append(l140m01a.getCustId());
			temp.append(" ");
			temp.append(l140m01a.getDupNo());
			temp.append(" ");
			temp.append(l140m01a.getCustName());
			temp.append(" ");
			if (row != null) {
				if (UtilConstants.DEFAULT.是.equals(Util.trim(row
						.get("SE_BAS_DATA")))) {
					// L160M01A.message55=【同意】共用基本資料
					temp.append(pop.getProperty("L160M01A.message55")).append(
							"，");
				} else {
					// L160M01A.message56=【不同意】共用基本資料
					temp.append(pop.getProperty("L160M01A.message56")).append(
							"，");
				}
				if (UtilConstants.DEFAULT.是.equals(Util.trim(row
						.get("SE_ACC_DATA")))) {
					// L160M01A.message57=【同意】共用帳務資料
					temp.append(pop.getProperty("L160M01A.message57")).append(
							"。");
				} else {
					// L160M01A.message58=【不同意】共用帳務資料
					temp.append(pop.getProperty("L160M01A.message58")).append(
							"。");
				}
			} else {
				// L160M01A.message59=0024交易無此客戶共同行銷維護資訊
				temp.append(pop.getProperty("L160M01A.message59")).append("。");
			}
			totalStr.append(totalStr.length() > 0 ? "\r" : "");
			totalStr.append(temp.toString());
		}

		L160M01A l160m01a = l160m01aDao.findByMainId(mainId);
		l160m01a.setJoinMarketing(totalStr.toString());
		l160m01a.setJoinMarketingDate(CapDate.getCurrentTimestamp());
		l160m01aDao.save(l160m01a);
		return l160m01a;
	}

	/**
	 * 判斷額度限額是否有變更
	 * 
	 * @param newList
	 *            現請科目限額
	 * @param oldList
	 *            變更前科目限額
	 * @return
	 */
	private boolean gfnCkSubQuotaChang(HashMap<String, String> newList,
			HashMap<String, String> oldList) {

		/**
		 * <pre>
		 * gfnCkSubQuotaChang = False
		 *        
		 *         Forall X In NewSnoList
		 *                 If Not Iselement(BFSnoList(X)) Then
		 *                         gfnCkSubQuotaChang = True
		 *                         Exit Forall
		 *                 Else
		 *                         If NewSnoList(X) <> BFSnoList(X) Then
		 *                                 gfnCkSubQuotaChang = True
		 *                                 Exit Forall
		 *                         End If
		 *                 End If
		 *         End Forall
		 * </pre>
		 */
		boolean result = false;
		for (String key : newList.keySet()) {
			if (!oldList.containsKey(key)) {
				result = true;
				break;
			} else {
				if (Util.notEquals(Util.trim(oldList.get(key)),
						Util.trim(newList.get(key)))) {
					result = true;
					break;
				}

			}
		}

		return result;
	}

	@Override
	public void upLoadMIS2(L160M01A l160m01a) {
		// String brnId = l160m01a.getOwnBrId();
		// long t1 = System.currentTimeMillis();
		//
		// L120M01A l120m01a =
		// l120m01aDao.findByMainId(l160m01a.getSrcMainId());
		// // 取得該額度明細表 對應 額度序號
		// HashMap<String, String> mainIdmapingCntrno = new HashMap<String,
		// String>();
		// List<String> mainIds = new ArrayList<String>();
		// // 將查詢結果放到List裡面
		// StringBuilder cntrNosTemp = new StringBuilder();
		// for (L160M01B l160m01b : l160m01a.getL160m01b()) {
		// mainIds.add(l160m01b.getReMainId());
		// mainIdmapingCntrno
		// .put(l160m01b.getReMainId(), l160m01b.getCntrNo());
		// cntrNosTemp.append(cntrNosTemp.length() > 0 ? "," : "");
		// cntrNosTemp.append("'");
		// cntrNosTemp.append(l160m01b.getCntrNo());
		// cntrNosTemp.append("'");
		// }
		//
		// List<L120M01F> l120m01fs = l120m01fDao.findByMainId(l160m01a
		// .getSrcMainId());
		// // 取出所有的額度序號一次查詢
		//
		// List<L140M01A> l140m01as = l140m01aDao
		// .findL140m01aListByMainIdList(mainIds
		// .toArray(new String[mainIds.size()]));
		//
		// List<Map<String, Object>> cntrNoData = misIquotappService
		// .findByCntrNo(cntrNosTemp.toString());
		//
		// ArrayList<String> cntrNoList = new ArrayList<String>();
		// for (Map<String, Object> map : cntrNoData) {
		// cntrNoList.add((String) map.get("QUOTANO"));
		// }
		//
		// // 目前系統時間
		// String sDate = CapDate.formatDate(new Date(),
		// UtilConstants.DateFormat.YYYY_MM_DD);
		// // 借款人資料檔
		// List<L120S01A> l120s01as = l120s01aDao.findByMainId(l120m01a
		// .getMainId());
		// HashMap<String, HashMap<String, String>> custDatas = new
		// HashMap<String, HashMap<String, String>>();
		// for (L120S01A l120s01a : l120s01as) {
		// HashMap<String, String> custData = new HashMap<String, String>();
		// custData.put("existDate", CapDate.formatDate(
		// l120s01a.getExistDate(),
		// UtilConstants.DateFormat.YYYY_MM_DD));
		// custData.put("feeDate", CapDate.formatDate(l120s01a.getFeeDate(),
		// UtilConstants.DateFormat.YYYY_MM_DD));
		// custData.put("countryDate", CapDate.formatDate(
		// l120s01a.getCountryDate(),
		// UtilConstants.DateFormat.YYYY_MM_DD));
		// custDatas.put(
		// StrUtils.concat(l120s01a.getCustId(), l120s01a.getDupNo()),
		// custData);
		// }
		// Map<String, String> codeTypeMap = codeTypeService
		// .findByCodeType(UtilConstants.CodeTypeItem.授信科目轉會計科目);
		// // 資料修改人
		// String updater = MegaSSOSecurityContext.getUserId();
		//
		// // 存放已處理過的額度序號
		// List<String> tProcessSno = new ArrayList<String>();
		//
		// // MIS 用
		// List<Object[]> iquotgurList = new ArrayList<Object[]>();
		// List<Object[]> iquotjonList = new ArrayList<Object[]>();
		//
		// Boolean upAs400 = false;
		// if (UtilConstants.BrNoType.國外.equals(branchService.getBranch(brnId)
		// .getBrNoFlag())) {
		// upAs400 = true;
		// }
		// HashMap<String, String> checkIquotjon = new HashMap<String,
		// String>();
		// String cntrNo = "";
		// for (L140M01A l140m01a : l140m01as) {
		// cntrNo = mainIdmapingCntrno.get(l140m01a.getMainId());
		// if (Util.isEmpty(cntrNo)) {
		// cntrNo = l140m01a.getCntrNo();
		// }
		// // 若該額度序號已經處理過則直接跳過此份額度明細表
		// if (Util.isEmpty(Util.trim(cntrNo)) || tProcessSno.contains(cntrNo))
		// {
		// continue;
		// }
		//
		// // 將處理完的額度序號存到到 tProcessSno 中
		// tProcessSno.add(cntrNo);
		//
		// logger.info("{}=======>{}", "Start", "uploadIquotapp|cntrNo==>"
		// + cntrNo);
		// // 2.上傳 保證人檔IQUOTGUR
		// this.uploadIquotgur(l140m01a, updater, iquotgurList, cntrNo);
		// logger.info("{}=======>{}", "Start", "uploadIquotjon|cntrNo==>"
		// + cntrNo);
		// // 3.上傳 共同借款人檔Iquotjon
		// this.uploadIquotjon(l140m01a, l120s01as, updater, iquotjonList,
		// cntrNo, checkIquotjon);
		// logger.info("{}=======>{}", "Start", "uploadQuotainf|cntrNo==>"
		// + cntrNo);
		//
		// // 此額度性質為「取消」, 不上傳科(子)目及其限額
		// if (LMSUtil.isContainValue(l140m01a.getProPerty(),
		// UtilConstants.Cntrdoc.Property.取消)) {
		// continue;
		// }
		//
		// }
		// logger.info("{}=======>{}", "Start", "misIquotgurService.insert|");
		// // misIquotgurService.insert(iquotgurList);
		// misIquotgurService.insertAllTest(cntrNosTemp.toString(),
		// iquotgurList,
		// iquotjonList);
		// // 以下為測試rollback 用
		// // Object ddd = null;
		//
		// // logger.info(ddd.toString());
		// logger.info("{}=======>{}", "Start", "misIquotjonService.insert|");
		// // misIquotjonService.insert(iquotjonList);
		// logger.info("{}=======>{}", "Start", "misQuotainfService.insert|");
		//
		// logger.info(StrUtils.concat("\n mis upLoad total Time ===>",
		// (System.currentTimeMillis() - t1), " ms"));
		//
		// // 以下為AS400 測試
		// long t2 = System.currentTimeMillis();

	}

	@Override
	public void saveByGenericBeans(List<GenericBean> beans) {
		for (GenericBean bean : beans) {
			this.save(bean);
		}
	}

	@Override
	public List<L160M01A> findClsFirstUse(String[] docStatus, String ownBrId) {
		List<L160M01A> result = new ArrayList<L160M01A>();
		List<C160M01A> list = c160m01aDao.findByFitstUse(docStatus, ownBrId);
		for (C160M01A c160m01a : list) {
			L160M01A l160m01a = new L160M01A();
			try {
				DataParse.copy(c160m01a, l160m01a);

				L163S01A l163s01a = new L163S01A();
				C160M01D c160m01d = c160m01a.getC160m01d();
				if (c160m01d != null)
					DataParse.copy(c160m01d, l163s01a);
				l160m01a.setL163S01A(l163s01a);

			} catch (CapException e) {
				logger.error("findClsFirstUse error: " + e.getMessage());
			}
			result.add(l160m01a);
		}
		return result;
	}

	/**
	 * 將西元改民國年
	 * 
	 * @param _obj
	 * @return
	 */
	private String paresTOTWDate(Date _obj) {
		String result = "";
		if (_obj != null) {
			TWNDate d = TWNDate.valueOf((Date) _obj);

			// 上傳mis如日期大於最大2910/12/31 (999/12/31) 一律上傳 2910/12/31
			if (d.compareTo(TWNDate.valueOf("2910-12-31")) > 0) {
				d = TWNDate.valueOf("2910-12-31");
			}
			result = d.toTW();

			// 日期如果小於 1911-01-01 則轉空白 (因為西元扣掉1911後會變成負的)
			if (d.compareTo(TWNDate.valueOf("1911-01-01")) < 0) {
				result = "";
			}

		} else {
			result = "";
		}

		return result;
	}

	/**
	 * 上傳MIS五都轉換
	 * 
	 * @param locationCd
	 * @return
	 */
	private String getUploadLocationCd(String locationCd) {
		Integer No = Util.parseInt(Util.getRightStr(locationCd, 2));
		String En = Util.getLeftStr(locationCd, 1);
		if (No > 50) {
			if (Util.equals(En, "B")) {
				En = "L";
			} else if (Util.equals(En, "D")) {
				En = "R";
			} else if (Util.equals(En, "E")) {
				En = "S";
			}
			No = No - 50;
			locationCd = En + Util.addZeroWithValue(No.toString(), 2);
		}
		return locationCd;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.lms.service.LMS1605Service#findL161m01aByMainIdUid
	 * (java .lang.String)
	 */
	@Override
	public L161S01A findL161m01aByMainIdUid(String mainId, String uid) {
		return l161m01aDao.findByMainIdUid(mainId, uid);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.lms.service.LMS1605Service#findL161m01aByMainIdUid
	 * (java .lang.String)
	 */
	@Override
	public L161S01A findL161m01aByMainIdCntrno(String mainId, String cntrNo) {
		return l161m01aDao.findByMainIdCntrno(mainId, cntrNo);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.lms.service.LMS1605Service#findL161m01aByMainIdUid
	 * (java .lang.String)
	 */
	@Override
	public L161S01C findL161s01cByMainIdPidItemType(String mainId, String pid,
			String itemType) {
		return l161s01cDao.findByMainIdPidItemType(mainId, pid, itemType);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.lms.service.LMS1605Service#findL161m01aByMainIdUid
	 * (java .lang.String)
	 */
	@Override
	public L161S01C findL161s01cByMainIdCntrnoItemType(String mainId,
			String cntrNo, String itemType) {
		return l161s01cDao.findByMainIdCntrnoItemType(mainId, cntrNo, itemType);
	}

	@Override
	public void reNewC801M01A(String[] cntrNos, String mainId)
			throws CapException {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		for (String CNTRNO : cntrNos) {

			String COMMA = "、";
			String tCoName = "";
			String tGuName = "";
			String tType = "";

			L161S01A l161s01a = l161m01aDao.findByMainIdCntrno(mainId, CNTRNO);
			List<L162S01A> l162s01as = l162s01aDao.findByMainIdCntrNo(mainId,
					CNTRNO);

			for (L162S01A l162s01a : l162s01as) {
				tType = l162s01a.getRType();
				if ("C".equals(tType)) {
					if (Util.notEquals(l162s01a.getCustId(), l162s01a.getRId())) {
						tCoName = tCoName + Util.trim(l162s01a.getRName())
								+ "(" + tType + ")" + COMMA;
					}

				} else if ("G".equals(tType)) {
					tGuName = tGuName + Util.trim(l162s01a.getRName()) + "("
							+ tType + ")" + COMMA;
				} else if ("N".equals(tType)) {
					tGuName = tGuName + Util.trim(l162s01a.getRName()) + "("
							+ tType + ")" + COMMA;
				} else if ("S".equals(tType)) {
					tGuName = tGuName + Util.trim(l162s01a.getRName()) + "("
							+ tType + ")" + COMMA;
				} else if ("D".equals(tType)) {
					tGuName = tGuName + Util.trim(l162s01a.getRName()) + "("
							+ tType + ")" + COMMA;
				} else if ("E".equals(tType)) {
					tGuName = tGuName + Util.trim(l162s01a.getRName()) + "("
							+ tType + ")" + COMMA;
				} else if ("L".equals(tType)) {
					tGuName = tGuName + Util.trim(l162s01a.getRName()) + "("
							+ tType + ")" + COMMA;
				}
			}
			if (!CapString.isEmpty(tCoName)) {
				tCoName = tCoName.substring(0,
						tCoName.length() - COMMA.length());
			}
			if (!CapString.isEmpty(tGuName)) {
				tGuName = tGuName.substring(0,
						tGuName.length() - COMMA.length());
			}

			C801M01A c801m01a = cls8011Service.findC801M01A_cntrNo_ownBrId(
					CNTRNO, user.getUnitNo());
			String custId = l161s01a.getCustId();
			String dupNo = l161s01a.getDupNo();
			String cntrMainId = l161s01a.getCntrMainId();
			L140M01A l140m01a = lms1401Service.findL140m01aByMainId(cntrMainId);

			String custName = null;
			List<C801M01B> newC801m01bs = null;

			if (l140m01a != null) {
				custName = Util.trim(l140m01a.getCustName());
			}

			if (c801m01a == null) {
				c801m01a = cls8011Service.initModel(custId, dupNo, custName,
						CNTRNO);
				newC801m01bs = cls8011Service.genLatest(c801m01a.getMainId());
			} else {
				// cls8011Service.deleteC801M01B(c801m01a.getMainId());
				List<C801M01B> orgC801m01bs = cls8011Service
						.findC801M01B_mainId(c801m01a.getMainId());
				if (orgC801m01bs == null || orgC801m01bs.isEmpty()) {
					newC801m01bs = cls8011Service.genLatest(c801m01a
							.getMainId());
				}
			}

			if (Util.notEquals(custName, "")) {
				c801m01a.setCustName(custName);
			}
			c801m01a.setCoCustName(tCoName);
			c801m01a.setGuarantor(tGuName);
			c801m01a.setDocType("1"); // 企金

			L120M01C l120m01c = l120m01cDao.findoneByRefMainId(cntrMainId);
			if (l120m01c != null) {
				L120M01A l120m01a = l120m01aDao.findByMainId(l120m01c
						.getMainId());
				if (l120m01a != null) {
					L120S01B l120s01b = lms1201Service.findL120s01bByUniqueKey(
							l120m01a.getMainId(), custId, dupNo);
					if (l120s01b != null) {
						if (Util.notEquals(l120s01b.getChairman(), "")) {
							if (c801m01a != null) {
								c801m01a.setChairmanName(Util.trim(l120s01b
										.getChairman()));
							}

						}
					}
				}
			}

			// ---
			cls8011Service.setStatusEdit(c801m01a);
			// ---
			if (newC801m01bs != null && !newC801m01bs.isEmpty()) {
				cls8011Service.saveData(c801m01a, newC801m01bs);
			} else {
				cls8011Service.save(c801m01a);
			}

		}

	}

	private void uploadELF517(L160M01A l160m01a, L140M01A l140m01a,
			L120M01A l120m01a, String cntrNo) throws CapException {

		boolean hasElf517 = false;
		Map<String, Object> cntrNoData = misElf517Service.findByCntrNo(cntrNo);

		if (cntrNoData != null && !cntrNoData.isEmpty()) {
			BigDecimal count = LMSUtil.toBigDecimal(cntrNoData.get("COUNT"));
			if (count.compareTo(BigDecimal.ZERO) > 0) {
				hasElf517 = true;
			}
		}

		L140M01M l140m01m = lmsService.findModelByMainId(L140M01M.class,
				l140m01a.getMainId());
		if (l140m01m == null) {
			return;
		}

		String documentNo = LMSUtil.getUploadCaseNo(l120m01a);

		this.lmsService.insertOrUpdateELF517ForUnsoldHouseFinanceingData(
				documentNo, l140m01m, hasElf517, cntrNo);
	}

	/**
	 * J-105-0263-001 配合a-Loan新增利率比對報表，Web e-Loan企金核准時同步更新ELF500相同額度之資料
	 * 
	 * @param l160m01a
	 * @param l140m01a
	 * @param l120m01a
	 * @param cntrNo
	 * @throws CapException
	 */
	private void updateELF500(L160M01A l160m01a, L140M01A l140m01a,
			L120M01A l120m01a, String cntrNo) throws CapException {

		misdbBASEService.updateELf500_NOTVALID_ByCntrNo(cntrNo,
				l140m01a.getCustId(), l140m01a.getDupNo());

	}

	/**
	 * J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	@Override
	public L164S01A findL164s01aByMainIdCustId(String mainId, String custId,
			String dupNo) {
		return l164s01aDao.findByUniqueKey(mainId, custId, dupNo);
	}

	/**
	 * J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
	 * 
	 */
	@Override
	public void deleteL164s01as(List<L164S01A> l164s01as) {
		l164s01aDao.delete(l164s01as);
	}

	/**
	 * J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
	 * 
	 * @param list
	 */
	@Override
	public void saveL164s01aList(List<L164S01A> list) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (L164S01A l164s01a : list) {
			l164s01a.setUpdater(user.getUserId());
			l164s01a.setUpdateTime(CapDate.getCurrentTimestamp());
		}
		l164s01aDao.save(list);
	}

	/**
	 * J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
	 * 
	 * @param meta
	 * @throws CapException
	 */
	private void gfnUpdateReviewListData(L160M01A meta) throws CapException {

		List<ELF412B> elf412b_list = gfnDB2UpELF412B(meta);

		if (CollectionUtils.isNotEmpty(elf412b_list)) {
			MISRows<ELF412B> mis_elf412b = new MISRows<ELF412B>(ELF412B.class);
			mis_elf412b.setValues(elf412b_list);
			up_DelThenInsert(mis_elf412b, "MIS");
		}

	}

	/**
	 * J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
	 * 
	 * @param meta
	 * @return
	 */
	private List<ELF412B> gfnDB2UpELF412B(L160M01A meta) {
		String creditType = "";
		String creditGrade = "";

		String mowType = "";
		String mowGrade = "";

		String fcrdType = "";
		String fcrdArea = "";
		String fcrdPred = "";
		String fcrdGrad = "";

		List<ELF412B> elf412_list = new ArrayList<ELF412B>();

		HashMap<String, String> processedCustIdMap = new HashMap<String, String>();

		L120M01A l120m01a = l120m01aDao.findByMainId(meta.getSrcMainId());

		String caseLvl = Util.trim(l120m01a.getCaseLvl());

		List<L160M01B> l160m01bs = (List<L160M01B>) findListByMainId(
				L160M01B.class, meta.getMainId());
		for (L160M01B l160m01b : l160m01bs) {

			String tabFormMainId = l160m01b.getReMainId();
			L140M01A l140m01a = l140m01aDao.findByMainId(tabFormMainId);

			String cntrNo = Util.trim(l160m01b.getCntrNo());
			String cntrBranch = Util.getLeftStr(cntrNo, 3);
			String custId = l140m01a.getCustId();
			String dupNo = l140m01a.getDupNo();
			String fullCustId = custId + dupNo;

			if (processedCustIdMap.containsKey(fullCustId)) {
				continue;
			}

			L161S01A l161s01a = this.findL161m01aByMainIdCntrno(
					meta.getMainId(), cntrNo);

			// 額度明細表為取消、不變，或動審表本次為僅修改資料(LNFLAG 為 X) 不處理
			boolean notChkReviewBrNoFlag = false;

			if ((LMSUtil.isContainValue(Util.trim(l140m01a.getProPerty()),
					UtilConstants.Cntrdoc.Property.不變))
					|| LMSUtil.isContainValue(
							Util.trim(l140m01a.getProPerty()),
							UtilConstants.Cntrdoc.Property.取消)) {
				notChkReviewBrNoFlag = true;
			}

			if (Util.notEquals(Util.trim(l161s01a.getUseSpecialReason()), "")
					&& Util.notEquals(
							Util.trim(l161s01a.getUseSpecialReason()), "00")) {
				// 特殊修改-01.不動用，僅修改聯貸比率
				// 特殊修改-02.不動用，僅修改振興經濟非中小企業專案貸款
				notChkReviewBrNoFlag = true;
			}

			if (notChkReviewBrNoFlag) {
				continue;
			}

			// 借款人基本資料
			L120S01B l120s01b = lms1201Service.findL120s01bByUniqueKey(
					l120m01a.getMainId(), custId, dupNo);
			String reviewBrNo = Util.trim(l120s01b.getReviewBrNo());
			if (Util.equals(reviewBrNo, "")) {
				reviewBrNo = cntrBranch; // meta.getOwnBrId()
			}

			// 借款人為個人戶不要寫到ELF412B
			if (l120s01b != null) {
				String busCode = Util.trim(l120s01b.getBusCode());
				if (Util.equals(busCode, "130300")
						|| Util.equals(busCode, "060000")) {
					continue;
				}
			}

			// 實地覆審主辦行才寫到ELF412B控制檔
			// if (Util.notEquals(cntrBranch, reviewBrNo)) {
			// continue;
			// }

			/*
			 * 免辦理實地覆審: 1.參貸同業主辦之聯合授信案件 2.國內營業單位辦理之境外公司授信案件（含對大陸地區授信）及
			 * 3.國外營業單位單獨承做之跨國(非當地國 )授信案件，
			 * 除經首長或主管單位指定辦理實地覆審之案件外，得免辦理實地覆審，由主辦單位依據本要點第六條及本條規定辦理一般覆審。
			 */
			// OBU戶不實地覆審
			if (!retrialService.chkNeedRealReview(custId, dupNo, caseLvl)) {
				continue;
			}

			// 參貸同業主辦之聯合授信案件不用覆審
			String caseType = l161s01a.getCaseType();
			if (Util.equals(caseType, UtilConstants.Usedoc.caseType.同業聯貸參貸含自行聯貸)
					|| Util.equals(caseType,
							UtilConstants.Usedoc.caseType.同業聯貸參貸)) {
				continue;
			}

			// 因常董會案件不見得都是從ELF411進來(例如因為借款人變成利害關係人，所以簽報書變成需常董會權限核准，雖可能該案件雖然沒有新案或增額額度(沒有ELF411，如變更條件)，但該借款人還是應該要變成需要常董會實地覆審)
			// 所以透過動審表時補這種CASE
			ELF412B elf412b = misElf412BService.findByPk(cntrBranch, custId,
					dupNo);

			if (elf412b != null) {
				// ELF412B已經存在

				if (retrialService.isElf412B_NckdFlagHasNoReviewFlag(Util
						.trim(elf412b.getElf412b_nckdFlag()))) {
					// 目前有不覆審
					if (Util.notEquals(elf412b.getElf412b_oldRptId(), "")
							&& Util.equals(l120m01a.getMainId(),
									elf412b.getElf412b_oldRptId())) {
						// 重覆的簽報書就不寫了
						continue;
					}
				} else {
					// 目前要覆審
					if (Util.notEquals(Util.trim(elf412b.getElf412b_newAdd()),
							"")) {
						// 已經有新案註記，不更新後面的新案
						continue;
					} else {

						if (Util.notEquals(elf412b.getElf412b_oldRptId(), "")
								&& Util.equals(l120m01a.getMainId(),
										elf412b.getElf412b_oldRptId())) {
							// 重覆的簽報書就不寫了
							continue;
						} else {
							// 已經持續有覆審了，就不用再補名單進去了(042
							// 松南分行反映，2020/02剛覆審完，2020/03動審表做變更條件，結果 23475561
							// 亞旭電腦又變成新案增額要覆審)
							continue;
						}

					}
				}

			} else {
				// ELF412B不存在，沒有實地覆審過，要補
				elf412b = new ELF412B();
				// ---
				elf412b.setElf412b_branch(reviewBrNo);
				elf412b.setElf412b_custId(custId);
				elf412b.setElf412b_dupNo(dupNo);
				// ---
				elf412b.setElf412b_rckdLine("A");
			}

			// 新增時共同要寫的資料

			elf412b.setElf412b_newAdd("N");

			// data_ym 的格式: 009912
			elf412b.setElf412b_newDate(LrsUtil.elf412_rocDateStr_from_Date(meta
					.getApproveTime()));

			if (Util.equals(LrsUtil.NCKD_7_銷戶, elf412b.getElf412b_nckdFlag())
					&& CrsUtil.isNOT_null_and_NOTZeroDate(elf412b
							.getElf412b_cancelDt())) {
				elf412b.setElf412b_llrDate(elf412b.getElf412b_lrDate());
				elf412b.setElf412b_lrDate(CapDate.parseDate(CapDate.ZERO_DATE));
			}

			// 本來不覆審，本次變成要覆審，要視為全新案件，清掉上次覆審日，要不然逾期未覆審報表會馬上跳出來
			// 例如:不覆審註記為4，上次覆審日2018-07，本次動審上傳ELF412B為2019-11，理論上下次次審日應該為2020-11，但是因為上次覆審日沒有清掉，
			// 所以變成用2018-07加上一年，最遲應覆審日變成2019-07，則2019-11本筆新增後，隔月馬上跳出逾期未覆審
			if (!LrsUtil.isNckdFlag_EMPTY_8(elf412b.getElf412b_nckdFlag())) {
				// 目前不覆審，要清上次覆審日
				elf412b.setElf412b_lrDate(CapDate.parseDate(CapDate.ZERO_DATE));
			}

			if (Util.notEquals(LrsUtil.NCKD_8_本次暫不覆審,
					elf412b.getElf412b_nckdFlag())) {
				elf412b.setElf412b_nckdFlag("");
				elf412b.setElf412b_nckdDate(CapDate
						.parseDate(CapDate.ZERO_DATE));
				elf412b.setElf412b_nckdMemo("");
				elf412b.setElf412b_nextNwDt(CapDate
						.parseDate(CapDate.ZERO_DATE));
				elf412b.setElf412b_nextLtDt(CapDate
						.parseDate(CapDate.ZERO_DATE));
			}

			elf412b.setElf412b_newRptId(l120m01a.getMainId());
			elf412b.setElf412b_newRptDt(l120m01a.getEndDate());
			elf412b.setElf412b_newDraId(meta.getMainId());
			elf412b.setElf412b_newDraDt(meta.getApproveTime());

			if (Util.equals(Util.trim(elf412b.getElf412b_oldRptId()), "")) {
				elf412b.setElf412b_oldRptId(l120m01a.getMainId());
				elf412b.setElf412b_oldRptDt(l120m01a.getEndDate());
			}
			if (Util.equals(Util.trim(elf412b.getElf412b_oldDraId()), "")) {
				elf412b.setElf412b_oldDraId(meta.getMainId());
				elf412b.setElf412b_oldDraDt(meta.getApproveTime());
			}

			elf412b.setElf412b_tmestamp(CapDate.getCurrentTimestamp());

			// J-106-0145-004 Web e-Loan
			// 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
			retrialService.gfnCTL_Caculate_ELF412B(elf412b);

			if (elf412b.getElf412b_llrDate() == null) {
				elf412b.setElf412b_llrDate(CapDate.parseDate(CapDate.ZERO_DATE));
			}
			if (elf412b.getElf412b_lrDate() == null) {
				elf412b.setElf412b_lrDate(CapDate.parseDate(CapDate.ZERO_DATE));
			}

			if (elf412b.getElf412b_nckdDate() == null) {
				elf412b.setElf412b_nckdDate(CapDate
						.parseDate(CapDate.ZERO_DATE));
			}
			if (elf412b.getElf412b_cancelDt() == null) {
				elf412b.setElf412b_cancelDt(CapDate
						.parseDate(CapDate.ZERO_DATE));
			}
			if (elf412b.getElf412b_uckdDt() == null) {
				elf412b.setElf412b_uckdDt(CapDate.parseDate(CapDate.ZERO_DATE));
			}
			if (elf412b.getElf412b_nextNwDt() == null) {
				elf412b.setElf412b_nextNwDt(CapDate
						.parseDate(CapDate.ZERO_DATE));
			}
			if (elf412b.getElf412b_nextLtDt() == null) {
				elf412b.setElf412b_nextLtDt(CapDate
						.parseDate(CapDate.ZERO_DATE));
			}

			if (!processedCustIdMap.containsKey(fullCustId)) {
				processedCustIdMap.put(fullCustId, fullCustId);
			}

			elf412_list.add(elf412b);

		}

		return elf412_list;
	}

	/**
	 * J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
	 * 
	 * @param <T>
	 * @param misRows
	 * @param schemaName
	 */
	private <T> void up_DelThenInsert(MISRows<T> misRows, String schemaName) {
		if (Util.isNotEmpty(misRows.getKeyValues())) {
			int DelCount = misdbBASEService.delete(
					misRows.getKeyMsgFmtParam(schemaName),
					misRows.getKeyValues());
			logger.info("{}=======>{}", misRows.getTableNm(), "Delete:"
					+ DelCount);
			misdbBASEService.insert(misRows.getMsgFmtParam(schemaName),
					misRows.getTypes(), misRows.getValues());
			logger.info("{}=======>{}", misRows.getTableNm(), "Insert");
		}
	}

	/**
	 * J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制
	 * 
	 * @param l161s01a
	 */
	@Override
	public void copyRoaL120s04List(L140M01M l140m01m, L161S01A l161s01a)
			throws CapException {
		List<L120S04A> l120s04as = (List<L120S04A>) this.findListByMainId(
				L120S04A.class, l140m01m.getMainId());
		if (l120s04as != null && !l120s04as.isEmpty()) {
			for (L120S04A l120s04a : l120s04as) {
				L120S04A newL120s04a = new L120S04A();
				DataParse.copy(l120s04a, newL120s04a);
				newL120s04a.setMainId(l161s01a.getUid());
				l120s04aDao.save(newL120s04a);
			}
		}

		List<L120S04B> l120s04bs = (List<L120S04B>) this.findListByMainId(
				L120S04B.class, l140m01m.getMainId());
		if (l120s04bs != null && !l120s04bs.isEmpty()) {
			for (L120S04B l120s04b : l120s04bs) {
				L120S04B newL120s04b = new L120S04B();
				DataParse.copy(l120s04b, newL120s04b);
				newL120s04b.setMainId(l161s01a.getUid());
				l120s04bDao.save(newL120s04b);
			}
		}

		List<L120S04C> l120s04cs = (List<L120S04C>) this.findListByMainId(
				L120S04C.class, l140m01m.getMainId());
		if (l120s04cs != null && !l120s04cs.isEmpty()) {
			for (L120S04C l120s04c : l120s04cs) {
				L120S04C newL120s04c = new L120S04C();
				DataParse.copy(l120s04c, newL120s04c);
				newL120s04c.setMainId(l161s01a.getUid());
				l120s04cDao.save(newL120s04c);
			}
		}
	}

	/**
	 * J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制
	 * 
	 * @param l160m01a
	 * @param l140m01a
	 * @param l120m01a
	 * @param cntrNo
	 * @param l161s01a
	 * @throws CapException
	 */
	private void updateELF600(L160M01A l160m01a, L140M01A l140m01a,
			L120M01A l120m01a, String cntrNo, L161S01A l161s01a, String updater)
			throws CapException {

		String l140Custid = Util.trim(l140m01a.getCustId());
		String l140Dupno = Util.trim(l140m01a.getDupNo());
		String l140CntrNo = Util.trim(l140m01a.getCntrNo());

		if (l161s01a != null) {
			if (Util.equals(Util.trim(l161s01a.getIsClearLand()), "Y")) {
				if (Util.equals(Util.trim(l161s01a.getIsChgStDate()), "Y")
						|| Util.equals(Util.trim(l161s01a.getIsChgRate()), "Y")
						|| l161s01a.getActStartDate() != null) {
					ELF600 elf600 = misELF600Service.findByContract(l140CntrNo);

					if (elf600 != null && Util.isNotEmpty(elf600)) {
						ELF600 newElf600 = this.convertL161s01aToELF600(
								l120m01a, l160m01a, l161s01a, elf600);
						newElf600.setElf600_updfrom("DMS");
						newElf600.setElf600_updater(updater);
						newElf600.setElf600_tmestamp(CapDate
								.getCurrentTimestamp());
						misELF600Service.delete(l140CntrNo);
						misELF600Service.insert(newElf600);
					} else {

						throw new CapMessageException("額度序號"
								+ l140m01a.getCntrNo()
								+ "屬撥貸逾一年以上未動工興建之空地貸款控管對象，但控制擋ELF600查無資料",
								getClass());

					}
				}
			}

		}

	}

	/**
	 * J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制
	 * 
	 * @param l120m01a
	 * @param l140m01m
	 * @param newElf600
	 * @return
	 */

	private ELF600 convertL161s01aToELF600(L120M01A l120m01a,
			L160M01A l160m01a, L161S01A l161s01a, ELF600 newElf600) {

		// 12. 最新核定(動審)預計動工日(授審處維護/ELOAN系統維護)
		// 14. 變更預計動工日簽報書狀態1.簽報核准 2.完成動審(ELOAN系統維護)
		// 15. 變更預計動工日最新簽報日期(ELOAN系統維護)
		// 16. 採行措施1.縮減額度 2.是否分期收回借款 3.利率調整(ELOAN系統維護)
		// 17. 利率再加碼幅度(ELOAN系統維護)
		// 23. 是否符合本行規定(ELOAN系統維護/授審更改)

		newElf600.setElf600_elflag("2"); // 變更預計動工日簽報書狀態1.簽報核准 2.完成動審(ELOAN系統維護)
											// 動審表

		newElf600.setElf600_documentno(LMSUtil.getUploadCaseNo(l120m01a));

		newElf600.setElf600_enddate(l120m01a.getEndDate());
		if (Util.equals(Util.trim(l161s01a.getIsChgStDate()), "Y")) {
			newElf600.setElf600_cstdate(l161s01a.getCstDate());
			newElf600.setElf600_cstreason(Util.trim(l161s01a.getCstReason()));
			newElf600.setElf600_adoptfg(Util.trim(l161s01a.getAdoptFg()));
			newElf600.setElf600_lstdate(l161s01a.getCstDate());
		}
		if (Util.trim(l161s01a.getAdoptFg()).contains("3")
				|| Util.equals(Util.trim(l161s01a.getIsChgRate()), "Y")) {
			newElf600
					.setElf600_rateadd(l161s01a.getRateAdd() == null ? BigDecimal.ZERO
							: l161s01a.getRateAdd());
			newElf600
					.setElf600_custroa(l161s01a.getCustRoa() == null ? BigDecimal.ZERO
							: l161s01a.getCustRoa());
			newElf600
					.setElf600_relroa(l161s01a.getRelRoa() == null ? BigDecimal.ZERO
							: l161s01a.getRelRoa());
		}
		if (Util.equals(Util.trim(l161s01a.getIsChgStDate()), "Y")
				|| Util.equals(Util.trim(l161s01a.getIsChgRate()), "Y")) {
			newElf600.setElf600_islegal(Util.trim(l161s01a.getIsLegal()));
		}

		// 金爺說如果ELF600沒有初次核定預計動工日或最新核定(動審)預計動工日，要讓分行維護上傳(僅限有變更預計動工日或調整利率才上傳)
		if (CrsUtil.isNull_or_ZeroDate(newElf600.getElf600_fstdate())) {
			if (CrsUtil.isNOT_null_and_NOTZeroDate(l161s01a.getFstDate())) {
				newElf600.setElf600_fstdate(l161s01a.getFstDate());
			}
		}

		// 金爺說如果ELF600沒有初次核定預計動工日或最新核定(動審)預計動工日，要讓分行維護上傳(僅限有變更預計動工日或調整利率才上傳)
		if (CrsUtil.isNull_or_ZeroDate(newElf600.getElf600_lstdate())) {
			if (CrsUtil.isNOT_null_and_NOTZeroDate(l161s01a.getLstDate())) {
				newElf600.setElf600_lstdate(l161s01a.getLstDate());
			}
		}

		newElf600.setElf600_universal_id(l160m01a.getMainId());
		newElf600.setElf600_act_st_date(l161s01a.getActStartDate());

		return newElf600;
	}

	@SuppressWarnings("unchecked")
	@Override
	public void importQueryList(String oid) throws CapMessageException {
		L160M01A l160m01a = this.findModelByOid(L160M01A.class, oid);

		// 先Delete再Insert
		List<L161S01E> l161s01es = (List<L161S01E>) this.findListByMainId(
				L161S01E.class, l160m01a.getMainId());
		for (L161S01E l161s01e : l161s01es) {
			l161s01eDao.delete(l161s01e);
		}
		l161s01eDao.flush();

		// 開始引進名單開始********************************************************************************************************************************

		this.importQueryListForL160m01a(l160m01a.getMainId());

		// 引進名單結束************************************************************************************************************************************

	}

	/**
	 * 重新引進查詢名單
	 * 
	 * @param mainId
	 *            動審表mainId
	 */
	@SuppressWarnings("unchecked")
	private void importQueryListForL160m01a(String mainId) {
		// 動審表主要資料來源為MIS TABLE
		L160M01A l160m01a = amlRelateService.findModelByMainId(L160M01A.class,
				mainId);

		// 有ID*9***********************************

		List<L162S01A> l162s01as = (List<L162S01A>) amlRelateService
				.findListByMainId(L162S01A.class, l160m01a.getMainId());
		L120M01A l120M01A = amlRelateService.findL120m01aByL160m01a(l160m01a);
		String rptMainId = l120M01A.getMainId();

		if (l162s01as != null && !l162s01as.isEmpty()) {
			Map<String, String> cntrCustIdMap = new HashMap<String, String>();
			Map<String, String> cntrAllCustIdMap = new HashMap<String, String>();
			for (L162S01A l162s01a : l162s01as) {

				if (Util.equals(l162s01a.getRType(),
						UtilConstants.lngeFlag.共同借款人)) {
					// 額度明細表借款人
					String bId = l162s01a.getRId();
					String bNo = l162s01a.getRDupNo();
					String bName = Util.toSemiCharString(Util.trim(l162s01a
							.getRName()));
					String bCountry = "";
					L120S01B bL120s01b = amlRelateService
							.findL120s01bByUniqueKey(rptMainId, bId, bNo);
					if (bL120s01b != null) {
						bCountry = bL120s01b.getNtCode();
					}

					String borrKind = "";
					if (Util.equals(bId, l162s01a.getCustId())
							&& Util.equals(bNo, l162s01a.getDupNo())) {
						borrKind = UtilConstants.RPA.custRelationListCtlTarget.借戶;
					} else {
						borrKind = UtilConstants.RPA.custRelationListCtlTarget.共同借款人;
					}

					// 借戶/共同借款人
					// 同一ID如果為借戶與共同借款人，則與本案關係都要勾起來，所以都要丟到reSetL161S01D去判斷
					this.reSetL161S01E(mainId, bId, bNo, bName, borrKind);

					if (!cntrCustIdMap.containsKey(bId + bNo)) {

						cntrCustIdMap.put(bId + bNo, bName);

						// 相同ID的借戶/共同借款人，負責人、實質受益人、關係企業應該都相同
						// 負責人
						L164S01A l164s01a = amlRelateService
								.findL164s01aByUniqueKey(mainId, bId, bNo);
						if (l164s01a != null) {
							String chairmanId = Util.trim(l164s01a
									.getChairmanId());
							String chairmanDupNo = Util.trim(l164s01a
									.getChairmanDupNo());
							String chairman = Util.toSemiCharString(Util
									.trim(l164s01a.getChairman()));
							String chairmanCountry = "";

							if (Util.notEquals(chairmanId, "")
									|| Util.notEquals(chairman, "")) {
								this.reSetL161S01E(
										mainId,
										chairmanId,
										chairmanDupNo,
										chairman,
										UtilConstants.RPA.custRelationListCtlTarget.負責人);
							}

						}

						// J-110-0540_05097_B1001 Web
						// e-Loan企金授信配合調整E-loan系統動用審核表部分內容
						// 董監事
						List<Map<String, Object>> list = misEJF305Service
								.getByCustId(bId);
						for (Map<String, Object> map : list) {
							String comCustId = Util.trim(map.get("EJF305_IDN"));
							// if (Util.equals(comCustId, "")) {
							// // 沒有ID的，戶名會顯示（缺額待補），故此筆不要進來
							// continue;
							// }
							String comCustName = Util.trim(map
									.get("EJF305_NAME"));

							this.reSetL161S01E(
									mainId,
									comCustId,
									"0",
									comCustName,
									UtilConstants.RPA.custRelationListCtlTarget.董監事);

						}

						// 借款人10%以上大股東*****************************************************
						// 缺

						// 實質受益人
						// J-110-0540_05097_B1001 Web
						// e-Loan企金授信配合調整E-loan系統動用審核表部分內容
						// List<L120S01P> listL120s01p = amlRelateService
						// .findL120s01pByMainIdAndCustIdWithRType(
						// mainId,
						// bId,
						// bNo,
						// UtilConstants.RPA.custRelationListCtlTarget.實質受益人);
						// if (listL120s01p != null && !listL120s01p.isEmpty())
						// {
						// for (L120S01P l120s01p : listL120s01p) {
						// String rId = Util.trim(l120s01p.getRId());
						// String rDupNo = Util.trim(l120s01p.getRDupNo());
						// String rName = Util.toSemiCharString(Util
						// .trim(l120s01p.getRName()));
						// String rCountry = Util.equals(
						// Util.trim(l120s01p.getNation()), "") ? ""
						// : Util.trim(l120s01p.getNation());
						// // String rCountry = "";
						//
						// if (Util.notEquals(rId, "")
						// || Util.notEquals(rName, "")) {
						// this.reSetL161S01E(
						// mainId,
						// rId,
						// rDupNo,
						// rName,
						// UtilConstants.RPA.custRelationListCtlTarget.實質受益人);
						// }
						//
						// }
						//
						// }

						// J-107-0070-001 Web e-Loan
						// 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
						// 高階管理人員
						// J-110-0540_05097_B1001 Web
						// e-Loan企金授信配合調整E-loan系統動用審核表部分內容
						// List<L120S01P> listL120s01p_smgr = amlRelateService
						// .findL120s01pByMainIdAndCustIdWithRType(
						// mainId,
						// bId,
						// bNo,
						// UtilConstants.RPA.custRelationListCtlTarget.高階管理人員);
						// if (listL120s01p_smgr != null
						// && !listL120s01p_smgr.isEmpty()) {
						// for (L120S01P l120s01p : listL120s01p_smgr) {
						// String rId = Util.trim(l120s01p.getRId());
						// String rDupNo = Util.trim(l120s01p.getRDupNo());
						// String rName = Util.toSemiCharString(Util
						// .trim(l120s01p.getRName()));
						// String rCountry = Util.equals(
						// Util.trim(l120s01p.getNation()), "") ? ""
						// : Util.trim(l120s01p.getNation());
						// // String rCountry = "";
						// if (Util.notEquals(rId, "")
						// || Util.notEquals(rName, "")) {
						// this.reSetL161S01E(
						// mainId,
						// rId,
						// rDupNo,
						// rName,
						// UtilConstants.RPA.custRelationListCtlTarget.高階管理人員);
						// }
						//
						// }
						//
						// }

						// J-108-0039_05097_B1001 Web e-Loan
						// 國內企金授信系統簽報、動審AML頁籤將借戶之「具控制權人」納入應查詢比對黑名單之對象。
						// 具控制權人
						// J-110-0540_05097_B1001 Web
						// e-Loan企金授信配合調整E-loan系統動用審核表部分內容
						// List<L120S01P> listL120s01p_ctrlPeo =
						// amlRelateService
						// .findL120s01pByMainIdAndCustIdWithRType(
						// mainId,
						// bId,
						// bNo,
						// UtilConstants.RPA.custRelationListCtlTarget.具控制權人);
						// if (listL120s01p_ctrlPeo != null
						// && !listL120s01p_ctrlPeo.isEmpty()) {
						// for (L120S01P l120s01p : listL120s01p_ctrlPeo) {
						// String rId = Util.trim(l120s01p.getRId());
						// String rDupNo = Util.trim(l120s01p.getRDupNo());
						// String rName = Util.toSemiCharString(Util
						// .trim(l120s01p.getRName()));
						// String rCountry = Util.equals(
						// Util.trim(l120s01p.getNation()), "") ? ""
						// : Util.trim(l120s01p.getNation());
						// // String rCountry = "";
						// if (Util.notEquals(rId, "")
						// || Util.notEquals(rName, "")) {
						// this.reSetL161S01E(
						// mainId,
						// rId,
						// rDupNo,
						// rName,
						// UtilConstants.RPA.custRelationListCtlTarget.具控制權人);
						// }
						//
						// }
						//
						// }

						if (Util.equals(borrKind,
								UtilConstants.RPA.custRelationListCtlTarget.借戶)) {

							// 再查關係企業
							// J-110-0540_05097_B1001 Web
							// e-Loan企金授信配合調整E-loan系統動用審核表部分內容
							// List<?> rows5 = misElcrcoService
							// .findElcrecomByIdDupno2(bId, bNo);
							// if (rows5 != null && !rows5.isEmpty()) {
							// Iterator<?> it5 = rows5.iterator();
							// while (it5.hasNext()) {
							// Map<?, ?> dataMap5 = (Map<?, ?>) it5.next();
							//
							// String s04aId = Util.trim(String
							// .valueOf(dataMap5.get("BAN")));
							// String s04aNo = Util.trim(String
							// .valueOf(dataMap5.get("DUPNO")));
							// String s04aName = Util
							// .toSemiCharString(Util.trim(String
							// .valueOf(dataMap5
							// .get("CNAME"))));
							// String s04aEName = Util
							// .toSemiCharString(Util.trim(String
							// .valueOf(dataMap5
							// .get("ENAME"))));
							// String sCountry = "";
							// if (Util.notEquals(s04aId, "")) {
							// this.reSetL161S01E(
							// mainId,
							// s04aId,
							// s04aNo,
							// s04aName,
							// UtilConstants.RPA.custRelationListCtlTarget.關係企業);
							// }
							//
							// }
							// }

						}
					}

				}

				if (Util.equals(l162s01a.getRType(),
						UtilConstants.lngeFlag.連帶保證人)) {
					String gId = l162s01a.getRId();
					String gNo = l162s01a.getRDupNo();
					String gName = Util.toSemiCharString(Util.trim(l162s01a
							.getRName()));
					String gCountry = Util.trim(l162s01a.getRCountry());

					this.reSetL161S01E(mainId, gId, gNo, gName,
							UtilConstants.RPA.custRelationListCtlTarget.連保人);
				}

				if (Util.equals(l162s01a.getRType(),
						UtilConstants.lngeFlag.ㄧ般保證人)) {
					String gId = l162s01a.getRId();
					String gNo = l162s01a.getRDupNo();
					String gName = Util.toSemiCharString(Util.trim(l162s01a
							.getRName()));
					String gCountry = Util.trim(l162s01a.getRCountry());

					this.reSetL161S01E(mainId, gId, gNo, gName,
							UtilConstants.RPA.custRelationListCtlTarget.一般保證人);
				}

				if (Util.equals(l162s01a.getRType(),
						UtilConstants.lngeFlag.擔保品提供人)
						|| Util.equals(l162s01a.getRType(),
								UtilConstants.lngeFlag.連帶借款人)) {
					String gId = l162s01a.getRId();
					String gNo = l162s01a.getRDupNo();
					String gName = Util.toSemiCharString(Util.trim(l162s01a
							.getRName()));
					String gCountry = Util.trim(l162s01a.getRCountry());

					this.reSetL161S01E(mainId, gId, gNo, gName,
							UtilConstants.RPA.custRelationListCtlTarget.擔保品提供人);
				}

			}
		}

		if (l160m01a != null) {
			Map<String, String> cntrCustIdMap = new HashMap<String, String>();
			Set<L160M01B> l160m01bs = l160m01a.getL160m01b();
			if (l160m01bs != null && !l160m01bs.isEmpty()) {
				for (L160M01B l160m01b : l160m01bs) {
					L140M01A l140m01a = amlRelateService.findModelByMainId(
							L140M01A.class, l160m01b.getReMainId());
					if (l140m01a != null) {
						String mId = l140m01a.getCustId();
						String mNo = l140m01a.getDupNo();
						String mName = Util.toSemiCharString(Util.trim(l140m01a
								.getCustName()));

						String mCountry = "";
						L120S01B mL120s01b = amlRelateService
								.findL120s01bByUniqueKey(rptMainId, mId, mNo);
						if (mL120s01b != null) {
							mCountry = mL120s01b.getNtCode();
						}

						this.reSetL161S01E(mainId, mId, mNo, mName,
								UtilConstants.RPA.custRelationListCtlTarget.借戶);

						// if (!cntrCustIdMap.containsKey(mId + mNo)) {
						//
						// cntrCustIdMap.put(mId + mNo, mName);
						//
						// // 查詢董監事 SELECT EJF305_IDN,EJF305_ID FROM mis.ejf305
						// // WHERE EJF305_ID =
						// // '53917662'
						// if (l160m01a != null) {
						// List<Map<String, Object>> list = misEJF305Service
						// .getByCustId(mId);
						// for (Map<String, Object> map : list) {
						// String comCustId = Util.trim(map
						// .get("EJF305_IDN"));
						// String comCustName = Util.trim(map
						// .get("EJF305_NAME"));
						//
						// this.reSetL161S01E(
						// mainId,
						// comCustId,
						// "0",
						// comCustName,
						// UtilConstants.RPA.custRelationListCtlTarget.董監事);
						//
						// }
						// }
						//
						// }

						// J-107-0164_05097_B1001 Web
						// e-Loan國內企金授信額度明細表新增應收帳款簽案資訊
						// 應收帳款無追索權買方
						// J-110-0540_05097_B1001 Web
						// e-Loan企金授信配合調整E-loan系統動用審核表部分內容
						// List<L140M01S> listL140m01s = amlRelateService
						// .findL140m01sByMainIdType(l140m01a.getMainId(),
						// UtilConstants.L140m01sType.本案應收帳款買方額度資訊);
						// if (listL140m01s != null && !listL140m01s.isEmpty())
						// {
						// for (L140M01S l140m01s : listL140m01s) {
						// if (l140m01s.getItemSeq() != 99999) {
						// String sId = l140m01s.getCustId();
						// String sNo = l140m01s.getDupNo();
						// String sName = Util.toSemiCharString(Util
						// .trim(l140m01s.getCustName()));
						// String sCountry = Util.trim(l140m01s
						// .getCountry());
						// this.reSetL161S01E(
						// mainId,
						// sId,
						// sNo,
						// sName,
						// UtilConstants.RPA.custRelationListCtlTarget.應收帳款買方無追索);
						// }
						//
						// }
						// }

					}
				}
			}
		}

	}

	@Override
	public void reSetL161S01E(String mainId, String custId, String dupNo,
			String custName, String newRelation) {

		List<L161S01E> tl161s01es = null;
		custName = Util.toSemiCharString(Util.trim(custName));

		if (Util.equals(Util.trim(custId), "")
				&& Util.equals(Util.trim(dupNo), "")
				&& Util.equals(Util.trim(custName), "")) {
			return;
		}

		if (Util.notEquals(custId, "") && Util.notEquals(dupNo, "")) {
			// 已存在
			tl161s01es = l161s01eDao.findByIndex02(mainId, custId, dupNo);
		}

		if (tl161s01es != null && !tl161s01es.isEmpty()) {
			for (L161S01E l161s01e : tl161s01es) {
				String custRelation = l161s01e.getCustRelation();

				String[] item = custRelation.split(",");
				List<String> asList = Arrays.asList(item);

				String[] newRelationItem = newRelation.split(",");
				for (int n = newRelationItem.length - 1; n >= 0; n = n - 1) {
					String relation = newRelationItem[n];

					if (asList.contains(relation)) {
						continue;
					} else {

						if (Util.equals(custRelation, "")) {
							custRelation = relation;
						} else {
							custRelation = custRelation + "," + relation;
						}
					}

					String[] newItem = custRelation.split(",");

					int i, j;
					String tmp;
					for (i = newItem.length - 1; i >= 0; i = i - 1) {
						for (j = 0; j < i; j = j + 1) {
							// if (newItem[j] > newItem[i])// 換（"小於"是由大到小）
							if (Util.parseInt((String) newItem[j]) > Util
									.parseInt((String) newItem[i]))// 換（"小於"是由大到小）
							{
								tmp = newItem[j];
								newItem[j] = newItem[i];
								newItem[i] = tmp;
							}
						}
					}

					StringBuffer itemBuf = new StringBuffer("");
					for (String tItem : newItem) {
						itemBuf.append(Util.equals(itemBuf, "") ? "" : ",");
						itemBuf.append(tItem);
					}

					l161s01e.setCustRelation(itemBuf.toString());

					if (Util.equals(l161s01e.getCustId(), "")
							&& Util.equals(l161s01e.getDupNo(), "")) {
						if (Util.notEquals(custId, "")
								&& Util.notEquals(dupNo, "")) {
							l161s01e.setCustId(Util.trim(custId));
							l161s01e.setDupNo(Util.trim(dupNo));
						}
					}

					StringBuffer typeBuf = new StringBuffer("");
					// 預設查詢項目
					for (String tItem : newItem) {
						if (tItem
								.equals(UtilConstants.RPA.custRelationListCtlTarget.借戶)
								|| tItem.equals(UtilConstants.RPA.custRelationListCtlTarget.共同借款人)) {
							// 借戶、共同借款人
							// 個人戶才預設要查
							if (Pattern.matches("^[A-Z]\\d{9}$", custId)) {
								typeBuf.append(Util.equals(typeBuf, "") ? ""
										: ",");
								typeBuf.append(UtilConstants.RPA.TYPE.公司登記事項卡);

								typeBuf.append(Util.equals(typeBuf, "") ? ""
										: ",");
								typeBuf.append(UtilConstants.RPA.TYPE.稅籍登記資料公示查詢);
							} else {
								typeBuf.append(Util.equals(typeBuf, "") ? ""
										: ",");
								typeBuf.append(UtilConstants.RPA.TYPE.身份證換補查詢);
								typeBuf.append(Util.equals(typeBuf, "") ? ""
										: ",");
								typeBuf.append(UtilConstants.RPA.TYPE.受監護輔助宣告查詢);
							}

							typeBuf.append(Util.equals(typeBuf, "") ? "" : ",");
							typeBuf.append(UtilConstants.RPA.TYPE.銀行法及金控法利害關係人查詢);
						} else if (tItem
								.equals(UtilConstants.RPA.custRelationListCtlTarget.負責人)) {
							// 負責人
							typeBuf.append(Util.equals(typeBuf, "") ? "" : ",");
							typeBuf.append(UtilConstants.RPA.TYPE.身份證換補查詢);

							typeBuf.append(Util.equals(typeBuf, "") ? "" : ",");
							typeBuf.append(UtilConstants.RPA.TYPE.受監護輔助宣告查詢);

							typeBuf.append(Util.equals(typeBuf, "") ? "" : ",");
							typeBuf.append(UtilConstants.RPA.TYPE.銀行法及金控法利害關係人查詢);
						} else if (tItem
								.equals(UtilConstants.RPA.custRelationListCtlTarget.連保人)) {
							// 連保人
							// 個人戶才預設要查
							if (Pattern.matches("^[A-Z]\\d{9}$", custId)) {
								typeBuf.append(Util.equals(typeBuf, "") ? ""
										: ",");
								typeBuf.append(UtilConstants.RPA.TYPE.身份證換補查詢);

								typeBuf.append(Util.equals(typeBuf, "") ? ""
										: ",");
								typeBuf.append(UtilConstants.RPA.TYPE.受監護輔助宣告查詢);
							}

						} else if (tItem
								.equals(UtilConstants.RPA.custRelationListCtlTarget.董監事)) {
							// 董監事
							typeBuf.append(Util.equals(typeBuf, "") ? "" : ",");
							typeBuf.append(UtilConstants.RPA.TYPE.銀行法及金控法利害關係人查詢);
						}
					}

					// 去重複
					StringBuffer type = new StringBuffer("");
					for (String tItem : typeBuf.toString().split(",")) {
						if (type.toString().indexOf(tItem) == -1) {
							type.append(Util.equals(type, "") ? "" : ",");
							type.append(tItem);
						}
					}

					l161s01e.setType(type.toString());
					if (typeBuf.toString().indexOf(
							UtilConstants.RPA.TYPE.身份證換補查詢) > -1) {
						l161s01e.setChkYN(UtilConstants.DEFAULT.否);
					} else {
						l161s01e.setChkYN(UtilConstants.DEFAULT.是);
					}

					// J-110-0540_05097_B1001 Web
					// e-Loan企金授信配合調整E-loan系統動用審核表部分內容
					if (Util.equals(Util.trim(l161s01e.getCustId()), "")) {
						l161s01e.setChkYN(UtilConstants.DEFAULT.否);
					}
					// J-110-0540_05097_B1001 Web
					// e-Loan企金授信配合調整E-loan系統動用審核表部分內容
					if (Util.notEquals(typeBuf.toString(), "")) {
						// 預設引進時，至少要有一項查詢項目時，才新增名單
						this.save(l161s01e);
					}

				}

			}
		} else {
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			L161S01E l161s01e = new L161S01E();
			l161s01e.setMainId(mainId);
			l161s01e.setCustId(custId);
			l161s01e.setDupNo(dupNo);
			l161s01e.setCustName(custName); // 已經轉半形
			l161s01e.setCustRelation(newRelation);

			l161s01e.setMemo("");
			l161s01e.setCreateTime(CapDate.getCurrentTimestamp());
			l161s01e.setCreator(user.getUserId());
			l161s01e.setCreateBY(UtilConstants.Casedoc.L120s04aCreateBY.系統產生);

			// 預設查詢項目
			StringBuffer typeBuf = new StringBuffer("");
			for (String tItem : newRelation.split(",")) {
				if (tItem
						.equals(UtilConstants.RPA.custRelationListCtlTarget.借戶)
						|| tItem.equals(UtilConstants.RPA.custRelationListCtlTarget.共同借款人)) {
					// 借戶、共同借款人
					typeBuf.append(Util.equals(typeBuf, "") ? "" : ",");
					typeBuf.append(UtilConstants.RPA.TYPE.公司登記事項卡);

					typeBuf.append(Util.equals(typeBuf, "") ? "" : ",");
					typeBuf.append(UtilConstants.RPA.TYPE.稅籍登記資料公示查詢);

					typeBuf.append(Util.equals(typeBuf, "") ? "" : ",");
					typeBuf.append(UtilConstants.RPA.TYPE.銀行法及金控法利害關係人查詢);
				} else if (tItem
						.equals(UtilConstants.RPA.custRelationListCtlTarget.負責人)) {
					// 負責人
					typeBuf.append(Util.equals(typeBuf, "") ? "" : ",");
					typeBuf.append(UtilConstants.RPA.TYPE.身份證換補查詢);

					typeBuf.append(Util.equals(typeBuf, "") ? "" : ",");
					typeBuf.append(UtilConstants.RPA.TYPE.受監護輔助宣告查詢);

					typeBuf.append(Util.equals(typeBuf, "") ? "" : ",");
					typeBuf.append(UtilConstants.RPA.TYPE.銀行法及金控法利害關係人查詢);
				} else if (tItem
						.equals(UtilConstants.RPA.custRelationListCtlTarget.連保人)) {
					// 連保人
					// 個人戶才預設要查
					if (Pattern.matches("^[A-Z]\\d{9}$", custId)) {
						typeBuf.append(Util.equals(typeBuf, "") ? "" : ",");
						typeBuf.append(UtilConstants.RPA.TYPE.身份證換補查詢);

						typeBuf.append(Util.equals(typeBuf, "") ? "" : ",");
						typeBuf.append(UtilConstants.RPA.TYPE.受監護輔助宣告查詢);
					}
				} else if (tItem
						.equals(UtilConstants.RPA.custRelationListCtlTarget.董監事)) {
					// 董監事
					typeBuf.append(Util.equals(typeBuf, "") ? "" : ",");
					typeBuf.append(UtilConstants.RPA.TYPE.銀行法及金控法利害關係人查詢);
				}
			}

			// 去重複
			StringBuffer type = new StringBuffer("");
			for (String tItem : typeBuf.toString().split(",")) {
				if (type.toString().indexOf(tItem) == -1) {
					type.append(Util.equals(type, "") ? "" : ",");
					type.append(tItem);
				}
			}

			l161s01e.setType(type.toString());

			if (typeBuf.toString().indexOf(UtilConstants.RPA.TYPE.身份證換補查詢) > -1) {
				l161s01e.setChkYN(UtilConstants.DEFAULT.否);
			} else {
				l161s01e.setChkYN(UtilConstants.DEFAULT.是);
			}

			// J-110-0540_05097_B1001 Web
			// e-Loan企金授信配合調整E-loan系統動用審核表部分內容
			if (Util.equals(Util.trim(l161s01e.getCustId()), "")) {
				l161s01e.setChkYN(UtilConstants.DEFAULT.否);
			}

			// J-110-0540_05097_B1001 Web e-Loan企金授信配合調整E-loan系統動用審核表部分內容
			if (Util.notEquals(typeBuf.toString(), "")) {
				// 預設引進時，至少要有一項查詢項目時，才新增名單
				this.save(l161s01e);
			}

		}

	}

	@SuppressWarnings("unchecked")
	@Override
	public void queryRpaQuery(String oid) throws CapMessageException {
		String token = "";
		Map<String, Object> objResult = new LinkedHashMap<String, Object>();
		L160M01A l160m01a = this.findModelByOid(L160M01A.class, oid);

		// 先Delete再Insert
		List<L161S01D> l161s01ds = (List<L161S01D>) this.findListByMainId(
				L161S01D.class, l160m01a.getMainId());
		for (L161S01D l161s01d : l161s01ds) {

			if (!CapString.isEmpty(l161s01d.getDocfileoid())) {
				// 先刪除附件(setDeletedTime)
				docFileService.clean(l161s01d.getDocfileoid());
				// DocFile oldDocFile = docFileService.findByOidAndSysId(
				// l161s01d.getDocfileoid(), "LMS");
				// File file = docFileService.getRealFile(oldDocFile);
				// if (file.exists()) {
				// FileUtils.deleteQuietly(file);
				// }
			}
			l161s01dDao.delete(l161s01d);
		}
		l161s01dDao.flush();

		try {
			// Step 1 取得 token
			JSONObject resultJson = null;
			try {
				resultJson = rpaservice.getRPAAccessToken(objResult);
				token = resultJson != null ? resultJson.getString("result")
						: "";
				token = "Bearer " + token;
				if (CapString.isEmpty(token)) {
					throw new CapMessageException("getRPAAccessToken失敗，請稍後再試。",
							this.getClass());
				}
			} catch (Exception e) {
				throw new CapMessageException("取得RPA Token失敗，請稍後再試。",
						this.getClass());
			}

			List<L161S01E> l161s01es = (List<L161S01E>) this.findListByMainId(
					L161S01E.class, l160m01a.getMainId());

			for (L161S01E l161s01e : l161s01es) {

				// RPA 每筆需分開查
				if (l161s01e.getType().indexOf(UtilConstants.RPA.TYPE.公司登記事項卡) > -1) {
					this.executeRPAJobs_01(l161s01e, token);
				}
				if (l161s01e.getType().indexOf(
						UtilConstants.RPA.TYPE.稅籍登記資料公示查詢) > -1) {
					this.executeRPAJobs_02(l161s01e, token);
				}
				if (l161s01e.getType().indexOf(UtilConstants.RPA.TYPE.身份證換補查詢) > -1) {
					this.executeRPAJobs_03(l161s01e, token);
				}
				if (l161s01e.getType()
						.indexOf(UtilConstants.RPA.TYPE.受監護輔助宣告查詢) > -1) {
					this.executeRPAJobs_04(l161s01e, token);
				}
			}

			// 只有利害關係人查詢一起查
			this.executeRPAJobs_05(l160m01a);
			l161s01eDao.flush();

			// 更新查詢日
			l161s01es = (List<L161S01E>) this.findListByMainId(L161S01E.class,
					l160m01a.getMainId());

			for (L161S01E l161s01e : l161s01es) {
				l161s01e.setQueryDateS(CapDate.getCurrentTimestamp());
			}
			l161s01eDao.save(l161s01es);

		} catch (CapException e) {
			throw new CapMessageException(e.getMessage(), this.getClass());
		}
	}

	@SuppressWarnings("unchecked")
	@Override
	public void queryRpaRetry(String oid) throws CapMessageException {
		String token = "", processKey = "";
		;
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Map<String, Object> objResult = new LinkedHashMap<String, Object>();
		L161S01D l161s01d = this.findModelByOid(L161S01D.class, oid);
		L160M01A l160m01a = this.findL160M01AByMaindId(l161s01d.getMainId());

		if (!CapString.isEmpty(l161s01d.getDocfileoid())) {
			// 先刪除附件(setDeletedTime)
			docFileService.clean(l161s01d.getDocfileoid());
			// DocFile oldDocFile = docFileService.findByOidAndSysId(
			// l161s01d.getDocfileoid(), "LMS");
			// File file = docFileService.getRealFile(oldDocFile);
			// if (file.exists()) {
			// FileUtils.deleteQuietly(file);
			// }
		}

		if (l161s01d.getType().equals(UtilConstants.RPA.TYPE.銀行法及金控法利害關係人查詢)) {
			l161s01dDao.delete(l161s01d);
		} else {
			l161s01d.setStatus(UtilConstants.RPA.STATUS.查詢中); // 查詢中
			l161s01d.setQueryTime(CapDate.getCurrentTimestamp());
			l161s01d.setReason("");
			save(l161s01d);
		}
		l161s01dDao.flush();

		try {
			// Step 1 取得 token
			JSONObject resultJson = null;
			try {
				resultJson = rpaservice.getRPAAccessToken(objResult);
				token = resultJson != null ? resultJson.getString("result")
						: "";
				token = "Bearer " + token;
				if (CapString.isEmpty(token)) {
					throw new CapMessageException("getRPAAccessToken失敗，請稍後再試。",
							this.getClass());
				}
			} catch (Exception e) {
				throw new CapMessageException("取得RPA Token失敗，請稍後再試。",
						this.getClass());
			}

			// Step 2啟動JOB
			try {
				objResult = new LinkedHashMap<String, Object>();
				if (l161s01d.getType().equals(UtilConstants.RPA.TYPE.公司登記事項卡)) {
					// 設定要傳遞的參數 LinkedHashMap 才可以按順序取出
					objResult
							.put("responseURL",
									sysparamService
											.getParamValue(SysParamConstants.RPA_GW_RESPONSE_URL_LMS)); // 回傳位址
																										// SIT
																										// :
																										// "http://192.168.53.85:9081/lms-web/app/schedulerRPA"
					objResult.put("system", "eloan");
					// J-109-0208_05097_B1002 Web e-Loan企金動審表一鍵查詢與列印
					objResult.put("uniqueID", l161s01d.getMainId() + "|"
							+ l161s01d.getRpaQueryReason1());
					objResult.put("branchNo", user.getUnitNo());
					objResult.put(
							"empNo",
							CapString.isNumeric(user.getUserId()) ? String
									.format("%06d",
											Integer.valueOf(user.getUserId()))
									: user.getUserId());

					// 查詢條件
					objResult.put("data_ID", l161s01d.getRpaQueryReason1());
					logger.info("傳入參數==>[{}]", objResult.toString());
					resultJson = rpaservice.StartRPAJobForLMS(objResult, token,
							processKey, SysParamConstants.RPA_公司登記事項卡);
				} else if (l161s01d.getType().equals(
						UtilConstants.RPA.TYPE.稅籍登記資料公示查詢)) {
					// 設定要傳遞的參數 LinkedHashMap 才可以按順序取出
					objResult
							.put("responseURL",
									sysparamService
											.getParamValue(SysParamConstants.RPA_GW_RESPONSE_URL_LMS)); // 回傳位址
																										// SIT
																										// :
																										// "http://192.168.53.85:9081/lms-web/app/schedulerRPA"
					objResult.put("system", "eloan");
					// J-109-0208_05097_B1002 Web e-Loan企金動審表一鍵查詢與列印
					objResult.put("uniqueID", l161s01d.getMainId() + "|"
							+ l161s01d.getRpaQueryReason1());
					objResult.put("branchNo", user.getUnitNo());
					objResult.put(
							"empNo",
							CapString.isNumeric(user.getUserId()) ? String
									.format("%06d",
											Integer.valueOf(user.getUserId()))
									: user.getUserId());

					// 查詢條件
					objResult.put("data_ID", l161s01d.getRpaQueryReason1());

					logger.info("傳入參數==>[{}]", objResult.toString());

					resultJson = rpaservice.StartRPAJobForLMS(objResult, token,
							processKey, SysParamConstants.RPA_稅籍登記資料公示查詢);

				} else if (l161s01d.getType().equals(
						UtilConstants.RPA.TYPE.身份證換補查詢)) {
					// 設定要傳遞的參數 LinkedHashMap 才可以按順序取出
					objResult
							.put("responseURL",
									sysparamService
											.getParamValue(SysParamConstants.RPA_GW_RESPONSE_URL_LMS)); // 回傳位址
																										// SIT
																										// :
																										// "http://192.168.53.85:9081/lms-web/app/schedulerRPA"
					objResult.put("system", "eloan");
					// J-109-0208_05097_B1002 Web e-Loan企金動審表一鍵查詢與列印
					objResult.put("uniqueID", l161s01d.getMainId() + "|"
							+ l161s01d.getRpaQueryReason1());
					objResult.put("branchNo", user.getUnitNo());
					objResult.put(
							"empNo",
							CapString.isNumeric(user.getUserId()) ? String
									.format("%06d",
											Integer.valueOf(user.getUserId()))
									: user.getUserId());

					// 查詢條件
					objResult.put("data_ID", l161s01d.getRpaQueryReason1());
					objResult.put("data_Year", l161s01d.getRpaQueryReason2());
					objResult.put("data_Month", l161s01d.getRpaQueryReason3());
					objResult.put("data_Day", l161s01d.getRpaQueryReason4());
					objResult.put("data_location",
							l161s01d.getRpaQueryReason5());
					objResult.put("data_type", l161s01d.getRpaQueryReason6());

					logger.info("傳入參數==>[{}]", objResult.toString());

					resultJson = rpaservice.StartRPAJobForLMS(objResult, token,
							processKey, SysParamConstants.RPA_身份證遺失檢查);

				} else if (l161s01d.getType().equals(
						UtilConstants.RPA.TYPE.受監護輔助宣告查詢)) {
					// 設定要傳遞的參數 LinkedHashMap 才可以按順序取出
					objResult
							.put("responseURL",
									sysparamService
											.getParamValue(SysParamConstants.RPA_GW_RESPONSE_URL_LMS)); // 回傳位址
																										// SIT
																										// :
																										// "http://192.168.53.85:9081/lms-web/app/schedulerRPA"
					objResult.put("system", "eloan");
					// J-109-0208_05097_B1002 Web e-Loan企金動審表一鍵查詢與列印
					objResult.put("uniqueID", l161s01d.getMainId() + "|"
							+ l161s01d.getRpaQueryReason1());
					objResult.put("branchNo", user.getUnitNo());
					objResult.put(
							"empNo",
							CapString.isNumeric(user.getUserId()) ? String
									.format("%06d",
											Integer.valueOf(user.getUserId()))
									: user.getUserId());

					// 查詢條件
					objResult.put("data_CustomerNo",
							l161s01d.getRpaQueryReason1()); // 負責人ID

					logger.info("傳入參數==>[{}]", objResult.toString());

					resultJson = rpaservice.StartRPAJobForLMS(objResult, token,
							processKey, SysParamConstants.RPA_受監護輔助宣告);

				} else if (l161s01d.getType().equals(
						UtilConstants.RPA.TYPE.銀行法及金控法利害關係人查詢)) {
					this.executeRPAJobs_05(l160m01a);
				}
			} catch (Exception e) {
				throw new CapMessageException("RPA Job建立失敗，請稍後再試。",
						this.getClass());
			}

		} catch (CapException e) {
			throw new CapMessageException(e.getMessage(), this.getClass());
		}
	}

	/**
	 * 查詢公司登記事項卡
	 * 
	 * @param l160m01a
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public String executeRPAJobs_01(L161S01E l161s01e, String token)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String errorMsg = "", processKey = "";
		Map<String, Object> objResult = new LinkedHashMap<String, Object>();
		JSONObject resultJson = null;

		try {
			// STEP2 啟動JOB-公司登記事項卡查詢
			objResult = new LinkedHashMap<String, Object>();
			try {
				logger.info("啟動JOB-公司登記事項卡查詢 開始========================");

				String cntrCustid = l161s01e.getCustId();

				L161S01D l161s01d = new L161S01D();
				l161s01d.setMainId(l161s01e.getMainId());
				l161s01d.setType(UtilConstants.RPA.TYPE.公司登記事項卡);
				l161s01d.setStatus(UtilConstants.RPA.STATUS.查詢中); // 查詢中
				l161s01d.setQueryTime(CapDate.getCurrentTimestamp());
				// 查詢條件一
				l161s01d.setRpaQueryReason1(cntrCustid);
				save(l161s01d);
				l161s01dDao.flush();

				// 設定要傳遞的參數 LinkedHashMap 才可以按順序取出
				objResult
						.put("responseURL",
								sysparamService
										.getParamValue(SysParamConstants.RPA_GW_RESPONSE_URL_LMS)); // 回傳位址
																									// SIT
																									// :
																									// "http://192.168.53.85:9081/lms-web/app/schedulerRPA"
				objResult.put("system", "eloan");
				// J-109-0208_05097_B1002 Web e-Loan企金動審表一鍵查詢與列印
				objResult.put(
						"uniqueID",
						l161s01d.getMainId() + "|"
								+ l161s01d.getRpaQueryReason1());
				objResult.put("branchNo", user.getUnitNo());
				objResult.put(
						"empNo",
						CapString.isNumeric(user.getUserId()) ? String.format(
								"%06d", Integer.valueOf(user.getUserId()))
								: user.getUserId());

				// 查詢條件
				objResult.put("data_ID", l161s01d.getRpaQueryReason1());

				logger.info("傳入參數==>[{}]", objResult.toString());

				resultJson = rpaservice.StartRPAJobForLMS(objResult, token,
						processKey, SysParamConstants.RPA_公司登記事項卡);

				logger.info("啟動JOB-公司登記事項卡查詢 結束========================");
			} catch (Exception e) {
				errorMsg = "RPA Job建立失敗，請稍後再試。";
				throw new CapMessageException("RPA Job建立失敗，請稍後再試。",
						this.getClass());
			}
		} catch (Exception e) {
			throw new CapMessageException(errorMsg, this.getClass());
		}

		return errorMsg;
	}

	/**
	 * 稅籍登記資料公示查詢
	 * 
	 * @param l160m01a
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public String executeRPAJobs_02(L161S01E l161s01e, String token)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String errorMsg = "", processKey = "";
		Map<String, Object> objResult = new LinkedHashMap<String, Object>();
		JSONObject resultJson = null;

		try {
			// STEP2 啟動JOB-稅籍登記資料公示查詢
			objResult = new LinkedHashMap<String, Object>();
			try {
				logger.info("啟動JOB-稅籍登記資料公示查詢 開始========================");

				String cntrCustid = l161s01e.getCustId();

				L161S01D l161s01d = new L161S01D();
				l161s01d.setMainId(l161s01e.getMainId());
				l161s01d.setType(UtilConstants.RPA.TYPE.稅籍登記資料公示查詢);
				l161s01d.setStatus(UtilConstants.RPA.STATUS.查詢中); // 查詢中
				l161s01d.setQueryTime(CapDate.getCurrentTimestamp());
				// 查詢條件一
				l161s01d.setRpaQueryReason1(cntrCustid);
				save(l161s01d);
				l161s01dDao.flush();

				// 設定要傳遞的參數 LinkedHashMap 才可以按順序取出
				objResult
						.put("responseURL",
								sysparamService
										.getParamValue(SysParamConstants.RPA_GW_RESPONSE_URL_LMS)); // 回傳位址
																									// SIT
																									// :
																									// "http://192.168.53.85:9081/lms-web/app/schedulerRPA"
				objResult.put("system", "eloan");
				// J-109-0208_05097_B1002 Web e-Loan企金動審表一鍵查詢與列印
				objResult.put(
						"uniqueID",
						l161s01d.getMainId() + "|"
								+ l161s01d.getRpaQueryReason1());
				objResult.put("branchNo", user.getUnitNo());
				objResult.put(
						"empNo",
						CapString.isNumeric(user.getUserId()) ? String.format(
								"%06d", Integer.valueOf(user.getUserId()))
								: user.getUserId());

				// 查詢條件
				objResult.put("data_ID", l161s01d.getRpaQueryReason1());

				logger.info("傳入參數==>[{}]", objResult.toString());

				resultJson = rpaservice.StartRPAJobForLMS(objResult, token,
						processKey, SysParamConstants.RPA_稅籍登記資料公示查詢);

				logger.info("啟動JOB-稅籍登記資料公示查詢 結束========================");
			} catch (Exception e) {
				errorMsg = "RPA Job建立失敗，請稍後再試。";
				throw new CapMessageException("RPA Job建立失敗，請稍後再試。",
						this.getClass());
			}
		} catch (Exception e) {
			throw new CapMessageException(errorMsg, this.getClass());
		}

		return errorMsg;
	}

	/**
	 * 身份證換補查詢
	 * 
	 * @param l160m01a
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public String executeRPAJobs_03(L161S01E l161s01e, String token)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String errorMsg = "", processKey = "";
		Map<String, Object> objResult = new LinkedHashMap<String, Object>();
		JSONObject resultJson = null;
		Map<String, String> codeTypeMapIDSite = codeTypeService
				.findByCodeType("lms1601m01_IDSite");

		try {
			// STEP2 啟動JOB-身份證換補查詢
			objResult = new LinkedHashMap<String, Object>();
			try {
				logger.info("啟動JOB-身份證換補查詢 開始========================");

				L161S01D l161s01d = new L161S01D();
				l161s01d.setMainId(l161s01e.getMainId());
				l161s01d.setType(UtilConstants.RPA.TYPE.身份證換補查詢);
				l161s01d.setStatus(UtilConstants.RPA.STATUS.查詢中); // 查詢中
				l161s01d.setQueryTime(CapDate.getCurrentTimestamp());
				// 查詢條件
				l161s01d.setRpaQueryReason1(l161s01e.getCustId()); // 從債務人統編
				l161s01d.setRpaQueryReason2(l161s01e.getIdDateYear()); // 發證民國年
				l161s01d.setRpaQueryReason3(l161s01e.getIdDateMonth()); // 月
				l161s01d.setRpaQueryReason4(l161s01e.getIdDateDay()); // 日
				l161s01d.setRpaQueryReason5(codeTypeMapIDSite.get(l161s01e
						.getIdSite())); // 發證地點
				l161s01d.setRpaQueryReason6(l161s01e.getIdChangeType().equals(
						"1") ? "初發"
						: (l161s01e.getIdChangeType().equals("2") ? "補發" : "換發")); // 領補換別
																					// 1初發
																					// 2補發
																					// 3換發

				save(l161s01d);
				l161s01dDao.flush();

				// 設定要傳遞的參數 LinkedHashMap 才可以按順序取出
				objResult
						.put("responseURL",
								sysparamService
										.getParamValue(SysParamConstants.RPA_GW_RESPONSE_URL_LMS)); // 回傳位址
																									// SIT
																									// :
																									// "http://192.168.53.85:9081/lms-web/app/schedulerRPA"
				objResult.put("system", "eloan");
				// J-109-0208_05097_B1002 Web e-Loan企金動審表一鍵查詢與列印
				objResult.put(
						"uniqueID",
						l161s01d.getMainId() + "|"
								+ l161s01d.getRpaQueryReason1());
				objResult.put("branchNo", user.getUnitNo());
				objResult.put(
						"empNo",
						CapString.isNumeric(user.getUserId()) ? String.format(
								"%06d", Integer.valueOf(user.getUserId()))
								: user.getUserId());

				// 查詢條件
				objResult.put("data_ID", l161s01d.getRpaQueryReason1());
				objResult.put("data_Year", l161s01d.getRpaQueryReason2());
				objResult.put("data_Month", l161s01d.getRpaQueryReason3());
				objResult.put("data_Day", l161s01d.getRpaQueryReason4());
				objResult.put("data_location", l161s01d.getRpaQueryReason5());
				objResult.put("data_type", l161s01d.getRpaQueryReason6());

				logger.info("傳入參數==>[{}]", objResult.toString());

				resultJson = rpaservice.StartRPAJobForLMS(objResult, token,
						processKey, SysParamConstants.RPA_身份證遺失檢查);

				logger.info("啟動JOB-身份證換補查詢 結束========================");
			} catch (Exception e) {
				errorMsg = "RPA Job建立失敗，請稍後再試。";
				throw new CapMessageException("RPA Job建立失敗，請稍後再試。",
						this.getClass());
			}
		} catch (Exception e) {
			throw new CapMessageException(errorMsg, this.getClass());
		}

		return errorMsg;
	}

	/**
	 * 受監護輔助宣告查詢
	 * 
	 * @param l160m01a
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public String executeRPAJobs_04(L161S01E l161s01e, String token)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String errorMsg = "", processKey = "";
		Map<String, Object> objResult = new LinkedHashMap<String, Object>();
		JSONObject resultJson = null;

		try {
			// STEP2 啟動JOB-受監護輔助宣告查詢
			objResult = new LinkedHashMap<String, Object>();
			try {
				logger.info("啟動JOB-受監護輔助宣告查詢 開始========================");

				L161S01D l161s01d = new L161S01D();
				l161s01d.setMainId(l161s01e.getMainId());
				l161s01d.setType(UtilConstants.RPA.TYPE.受監護輔助宣告查詢);
				l161s01d.setStatus(UtilConstants.RPA.STATUS.查詢中); // 查詢中
				l161s01d.setQueryTime(CapDate.getCurrentTimestamp());

				// 查詢條件一
				l161s01d.setRpaQueryReason1(l161s01e.getCustId());
				save(l161s01d);
				l161s01dDao.flush();

				// 設定要傳遞的參數 LinkedHashMap 才可以按順序取出
				objResult
						.put("responseURL",
								sysparamService
										.getParamValue(SysParamConstants.RPA_GW_RESPONSE_URL_LMS)); // 回傳位址
																									// SIT
																									// :
																									// "http://192.168.53.85:9081/lms-web/app/schedulerRPA"
				objResult.put("system", "eloan");
				// J-109-0208_05097_B1002 Web e-Loan企金動審表一鍵查詢與列印
				objResult.put(
						"uniqueID",
						l161s01d.getMainId() + "|"
								+ l161s01d.getRpaQueryReason1());
				objResult.put("branchNo", user.getUnitNo());
				objResult.put(
						"empNo",
						CapString.isNumeric(user.getUserId()) ? String.format(
								"%06d", Integer.valueOf(user.getUserId()))
								: user.getUserId());

				// 查詢條件
				objResult.put("data_CustomerNo", l161s01d.getRpaQueryReason1()); // 負責人ID

				logger.info("傳入參數==>[{}]", objResult.toString());

				resultJson = rpaservice.StartRPAJobForLMS(objResult, token,
						processKey, SysParamConstants.RPA_受監護輔助宣告);

				logger.info("啟動JOB-稅籍登記資料公示查詢 結束========================");
			} catch (Exception e) {
				errorMsg = "RPA Job建立失敗，請稍後再試。";
				throw new CapMessageException("RPA Job建立失敗，請稍後再試。",
						this.getClass());
			}
		} catch (Exception e) {
			throw new CapMessageException(errorMsg, this.getClass());
		}

		return errorMsg;
	}

	/**
	 * 利害關係人查詢
	 * 
	 * @param l160m01a
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public String executeRPAJobs_05(L160M01A l160m01a) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1601M01Page.class);
		List<String> gridDataHtml1s = new ArrayList<String>();
		String saveDataStatue = "";
		String errorMsg = "";
		String dataStatus = "";

		List<Map<String, String>> QueryCustObj = new ArrayList<Map<String, String>>();

		List<L161S01E> l161s01es = (List<L161S01E>) findListByMainId(
				L161S01E.class, l160m01a.getMainId());
		for (L161S01E l161s01e : l161s01es) {
			if (l161s01e.getType().indexOf(
					UtilConstants.RPA.TYPE.銀行法及金控法利害關係人查詢) > -1) {
				Map<String, String> addMainCustId = new HashMap<String, String>();
				addMainCustId.put("custId", l161s01e.getCustId());
				addMainCustId.put("dupNo", l161s01e.getDupNo());
				addMainCustId.put("custName", l161s01e.getCustName());
				QueryCustObj.add(addMainCustId);
			}
		}

		for (Map<String, String> custObj : QueryCustObj) {
			String custId = custObj.get("custId");
			String dupNo = custObj.get("dupNo");
			String custName = custObj.get("custName");

			JSONArray law44Data = null;
			Map<Integer, String> map = new TreeMap<Integer, String>();
			map.put(0, "1");
			map.put(1, custId);
			map.put(2, dupNo);
			map.put(3, custName);

			law44Data = eaiSrv.findLawRpsById(l160m01a.getMainId(), custId,
					dupNo);
			if (law44Data != null && !law44Data.isEmpty()) {
				for (int i = 0; i < 4; i++) {
					dataStatus += "0";
					if ("Y".equals(law44Data.getString(i))) {
						map.put(4 + i, "是");
					} else if ("EAI 1000:查無資料".equals(law44Data.getString(i))) {
						map.put(4 + i, "否");
					} else {
						map.put(4 + i, "否");
					}
				}
			} else {
				for (int i = 0; i < 4; i++) {
					dataStatus += "1";
					map.put(4 + i, "");
				}
			}

			// J-108-0178 新增公司法董事控制從屬關係公司查詢
			Properties properties = MessageBundleScriptCreator
					.getComponentResource(AbstractEloanPage.class);
			String director = properties.getProperty("no");
			String directorStatus = "N";

			if (!custId.isEmpty()) {

				Map<String, Object> directorMap = misElf902Service
						.findCnameByRcustIdAndDupNo1(custId, dupNo);
				dataStatus += "0";
				String mapKey = !directorMap.keySet().isEmpty() ? new ArrayList<String>(
						directorMap.keySet()).get(0) : "";
				String directorValue = StringUtils.removeEnd(
						CapString.trimNull(directorMap.get(mapKey)), "、");
				if (directorValue.length() > 0) {
					director = properties.getProperty("yes") + "("
							+ directorValue + ")";
					directorStatus = "Y";
					map.put(8, director);
				} else {
					map.put(8, "否");
				}
			}

			// STEP2:組裝DATA HTML
			String gridDataHtmlCode = pop.getProperty("ces1200.1202.1");
			Object[] arguments = new Object[map.keySet().size()];
			for (Map.Entry<Integer, String> entry : map.entrySet()) {
				arguments[entry.getKey()] = entry.getValue();
			}
			String gridDataHtml1 = MessageFormat.format(gridDataHtmlCode,
					arguments);
			gridDataHtml1s.add(gridDataHtml1);

			// 判斷資料狀況
			if (saveDataStatue.isEmpty()) {
				saveDataStatue = dataStatus;
			}
			if (dataStatus.contains("1")) {
				String[] save = saveDataStatue.split("");
				String[] statusArr = dataStatus.split("");
				for (int i = 0; i < statusArr.length; i++) {
					if ("1".equals(statusArr[i])) {
						save[i] = statusArr[i];
					}
				}
				StringBuilder sb = new StringBuilder();
				for (String s : save) {
					sb.append(s);
				}
				saveDataStatue = sb.toString();
			}
		}

		// =======================
		// 沒有產生資料
		if (gridDataHtml1s.isEmpty()) {
			return null;
		}
		// STEP3: 組裝全部HTML
		String htmlCode = pop.getProperty("ces1200.1202");
		Object[] arguments = new Object[3];
		arguments[0] = user.getUserName();
		arguments[1] = CapDate
				.getCurrentDate(UtilConstants.DateFormat.YYYY_MM_DD);
		StringBuilder sb1 = new StringBuilder();
		for (String data : gridDataHtml1s) {
			sb1.append(data);
		}
		arguments[2] = sb1.toString();
		String html = MessageFormat.format(htmlCode, arguments);
		String css = pop.getProperty("ces1200.1200.css");
		html = css + html;

		// STEP6.存檔
		String saveCustId = "";
		String saveDupNo = "";
		// 一鍵查詢以申貸戶ID為代表

		saveCustId = l160m01a.getCustId();
		saveDupNo = l160m01a.getDupNo();

		this.keep_url_htmloutput_to_elnfs("rpa_lms", "05", saveDataStatue,
				l160m01a, saveCustId, saveDupNo, html, null);

		return errorMsg;
	}

	/**
	 * 將查詢HTML結果存到ELNFS
	 * 
	 * @param fieldId
	 * @param dataType
	 * @param dataStatus
	 * @param C120M01A
	 * @param custId
	 * @param dupNo
	 * @param htmlByte
	 * @param encode
	 * @return C120S01I
	 */
	private void keep_url_htmloutput_to_elnfs(String fieldId, String dataType,
			String dataStatus, L160M01A meta, String custId, String dupNo,
			String html, String encode) {
		byte[] htmlByte = html.getBytes();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		DocFile docFile = new DocFile();
		docFile.setSysId("LMS");
		docFile.setBranchId(user.getUnitNo());
		docFile.setContentType("text/html");
		docFile.setMainId(meta.getMainId());
		docFile.setPid(meta.getUid());
		docFile.setCrYear(CapDate.getCurrentDate("yyyy"));
		docFile.setFieldId(fieldId);
		docFile.setSrcFileName(dataType + ".html");
		docFile.setDeletedTime(null);
		if (CapString.isEmpty(encode)) {
			docFile.setData(htmlByte);
			docFileService.save(docFile, true);
		} else {
			File toFile = null;
			try {
				docFileService.save(docFile, false);
				toFile = docFileService.getRealFile(docFile);
				FileUtils.forceMkdir(toFile.getParentFile());
				FileUtils.writeStringToFile(toFile, new String(htmlByte),
						encode);
			} catch (IOException ioe) {
				logger.error("write file fail,realPath={}",
						toFile.getAbsolutePath());
				logger.error(StrUtils.getStackTrace(ioe));
			}
		}

		L161S01D l161s01d = new L161S01D();
		l161s01d.setMainId(meta.getMainId());
		l161s01d.setType(UtilConstants.RPA.TYPE.銀行法及金控法利害關係人查詢);
		l161s01d.setQueryTime(CapDate.getCurrentTimestamp());
		l161s01d.setRpaQueryReason1(custId);
		l161s01d.setStatus(UtilConstants.RPA.STATUS.查詢完成); // 查詢完成
		l161s01d.setReason("查詢成功");
		l161s01d.setData(html);
		l161s01d.setDocfileoid(docFile.getOid());

		/**
		 * 資料狀態 全部為0代表正常, 任一碼為1代表資料異常
		 * <p/>
		 * 當dataType=05時, 00000, 第1碼-是否為銀行法利害關係人,
		 * 第2碼-是否為金控法第44條利害關係人,第3碼-是否為金控法第45條利害關係人, 第4碼-是否為實質關係人(授信以外交易),
		 * 第5碼-公司法與本行董事具有控制從屬關係公司 當dataType=3時, 00, 第1碼-本行有無婉卻記錄, 第2碼-金控有無婉卻記錄
		 * 
		 */
		String memo = "";
		if (dataStatus.contains("1")) {
			memo = "資料異常";
		} else {
			memo = "資料正常，「非」銀行法及金控法44條利害關係人";
		}
		l161s01d.setMemo(memo);

		save(l161s01d);
		l161s01dDao.flush();
	}

	@Override
	public void importRpaDetail(String mainId, String oid) throws CapException {
		List<L161S01D> l161s01ds = l161s01dDao.findByMainId(mainId);

		// Step 明細檔發查RPA
		for (L161S01D l161s01d : l161s01ds) {
			if (!l161s01d.getStatus().equals(UtilConstants.RPA.STATUS.查詢完成)) {
				continue;
			}
			if (l161s01d.getType().equals(UtilConstants.RPA.TYPE.公司登記事項卡)) {

				// 匯入相關欄位data
				JSONObject req = JSONObject.fromObject(l161s01d.getData());
				String data_searchResult = req.optString("data_searchResult",
						""); // data_searchResult String 查詢結果 0:有資料 1:無資料"
				String data_LastChangeDate = req.optString(
						"data_LastChangeDate", ""); // data_LastChangeDate
													// String 最後核准變更日期 民國年/月/日

				if (data_searchResult.equals("0")) {
					// TODO 如果要引的話
					// 抓 getL160M01C set s1 8.公司登記事項卡 = V
				}
			} else if (l161s01d.getType().equals(
					UtilConstants.RPA.TYPE.稅籍登記資料公示查詢)) {

			} else if (l161s01d.getType()
					.equals(UtilConstants.RPA.TYPE.身份證換補查詢)) {

			} else if (l161s01d.getType().equals(
					UtilConstants.RPA.TYPE.受監護輔助宣告查詢)) {

			}
		}
	}

	@Override
	public List<L161S01E> findListL161S01EByCustId(String mainId,
			String custId, String dupNo) throws CapException {
		return l161s01eDao.findByIndex02(mainId, custId, dupNo);
	}

	// J-109-0150_10702_B1001 Web e-Loan IVR頁籤由模擬動審移至動審表
	public boolean getProjClassFromL160M01A(String oid) {
		boolean checkprojClass = false;

		if (!Util.isEmpty(oid)) {
			L160M01A meta = findModelByOid(L160M01A.class, oid);
			List<L160M01B> l160m01bs = (List<L160M01B>) findListByMainId(
					L160M01B.class, meta.getMainId());

			for (L160M01B l160m01b : l160m01bs) {
				String reMainId = l160m01b.getReMainId();
				L140M01A l140m01a = l140m01aDao.findByMainId(reMainId);
				if (Util.isNotEmpty(l140m01a)) {
					String projClass = l140m01a.getProjClass();
					if (Util.equals(projClass, LMSUtil.特定金錢信託受益權自行設質擔保授信)) {
						checkprojClass = true;
					}

					// J-110-0283_10702_B1001 Web e-Loan
					// IVR動審表引入月份放寬，比對與前次額度明細表幣別不同
					// if(LMSUtil.isContainValue(Util.trim(l140m01a.getProPerty()),
					// UtilConstants.Cntrdoc.Property.變更條件)
					// ||
					// LMSUtil.isContainValue(Util.trim(l140m01a.getProPerty()),
					// UtilConstants.Cntrdoc.Property.增額)){
					// Map<String, Object> map = eloanDbBaseService
					// .findLastTimeCaseByCutIdAndCntrNoOrdByUpdateTime(
					// l140m01a.getCustId(), l140m01a.getDupNo(),
					// Util.trim(l140m01a.getCntrNo()),
					// UtilConstants.Casedoc.DocType.企金,
					// Util.trim(l140m01a.getMainId()));
					// L140M01A l140m01a_old = null;
					// String oldL140M01AmainId = "";
					// if (map != null) {
					// oldL140M01AmainId = Util.trim(map.get("MAINID"));
					// l140m01a_old = lms1401Service
					// .findL140m01aByMainId(oldL140M01AmainId);
					// }
					// if(Util.isNotEmpty(l140m01a_old)){
					// if(!Util.equals(l140m01a.getCurrentApplyCurr(),l140m01a_old.getCurrentApplyCurr())){
					// checkprojClass = true;
					// }
					// }
					// }
				}

			}
		}
		return checkprojClass;
	}

	public void saveIVRFlag(String oid, List<String> addIVRList)
			throws CapException {
		try {
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			for (String data : addIVRList) {
				String[] eachdata = data.split(";");
				int cnt = (eachdata == null ? 0 : eachdata.length);
				if (cnt == 2) {
					String newCustId = cnt > 0 ? eachdata[0] : "";
					String newIVRFlag = cnt > 1 ? eachdata[1] : "";
					L160M01A meta = findModelByOid(L160M01A.class, oid);
					List<L160M01B> l160m01bs = (List<L160M01B>) findListByMainId(
							L160M01B.class, meta.getMainId());
					for (L160M01B l160m01b : l160m01bs) {
						String reMainId = l160m01b.getReMainId();
						L140M01A l140m01a = l140m01aDao.findByMainId(reMainId);
						String custId = l140m01a.getCustId();
						String projClass = l140m01a.getProjClass();

						// custId="A123456789";

						if (newCustId.equals(custId)
								&& Util.equals(projClass,
										LMSUtil.特定金錢信託受益權自行設質擔保授信)) {
							String ivrFlag = l160m01b.getIVRFlag();
							if (ivrFlag == null) {
								l160m01b.setIVRFlag(newIVRFlag);
							} else if (ivrFlag != null
									&& !ivrFlag.contains(newIVRFlag)) {
								l160m01b.setIVRFlag(Util.trim(ivrFlag) + ","
										+ newIVRFlag);
							}
							l160m01b.setUpdater(user.getUserId());
							l160m01b.setUpdateTime(CapDate
									.getCurrentTimestamp());
							this.save(l160m01b);
						}
					}
				}
			}
		} catch (Throwable t1) {
			throw new CapMessageException(t1.getMessage(), getClass());
		}
	}

	public void deleteIVRFlag(String oid, String deleteCustId, String fileName)
			throws CapException {
		try {
			if (!Util.isEmpty(fileName)) {
				MegaSSOUserDetails user = MegaSSOSecurityContext
						.getUserDetails();
				L160M01A meta = findModelByOid(L160M01A.class, oid);
				List<L160M01B> l160m01bs = (List<L160M01B>) findListByMainId(
						L160M01B.class, meta.getMainId());
				for (L160M01B l160m01b : l160m01bs) {
					String reMainId = l160m01b.getReMainId();
					L140M01A l140m01a = l140m01aDao.findByMainId(reMainId);
					String projClass = l140m01a.getProjClass();
					String custId = l140m01a.getCustId();
					String ivrFlag = l160m01b.getIVRFlag();
					if (Util.equals(projClass, LMSUtil.特定金錢信託受益權自行設質擔保授信)
							&& custId.equals(deleteCustId) && ivrFlag != null
							&& ivrFlag.contains(fileName)) {
						if (ivrFlag.contains(",")) {
							l160m01b.setIVRFlag(ivrFlag.replace(fileName + ",",
									"").replace("," + fileName, ""));
						} else {
							l160m01b.setIVRFlag(null);
						}
						l160m01b.setUpdater(user.getUserId());
						l160m01b.setUpdateTime(CapDate.getCurrentTimestamp());
						this.save(l160m01b);
					}
				}
			}
		} catch (Throwable t1) {
			throw new CapMessageException(t1.getMessage(), getClass());
		}
	}

	public List<Map<String, Object>> getIVRgrid(String oid) throws CapException {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		try {
			L160M01A meta = findModelByOid(L160M01A.class, oid);
			List<L160M01B> l160m01bs = (List<L160M01B>) findListByMainId(
					L160M01B.class, meta.getMainId());

			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			String lightId = user.getLightId();
			String userId = user.getUserId();
			String url = sysParameterService
					.getParamValue(SysParamConstants.IVR_REC_GW_URL);

			for (L160M01B l160m01b : l160m01bs) {
				String reMainId = l160m01b.getReMainId();
				L140M01A l140m01a = l140m01aDao.findByMainId(reMainId);
				if (!Util.isEmpty(l160m01b.getIVRFlag())) {
					String[] IVRFlag = l160m01b.getIVRFlag() != null ? l160m01b
							.getIVRFlag().split(",") : null;
					for (String ivrFlag : IVRFlag) {
						Map<String, Object> row = new HashMap<String, Object>();
						row.put("custId", Util.trim(l140m01a.getCustId()));
						row.put("custName", Util.trim(l140m01a.getCustName()));
						row.put("record_FileName", Util.trim(ivrFlag));
						row.put("record_LightId", lightId);
						row.put("record_UserID", userId);
						row.put("record_Url", url);
						row.put("record_FileName2",
								Util.trim(ivrFlag).replace(".wav", ""));
						list.add(row);
					}
				}
			}
		} catch (Throwable t1) {
			throw new CapMessageException(t1.getMessage(), getClass());
		}

		return list;
	}

	public List<Map<String, Object>> getIVRFiltergrid(String oid)
			throws CapException {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		try {
			L160M01A meta = findModelByOid(L160M01A.class, oid);
			String SRCMAINID = meta.getSrcMainId();

			List<L160M01B> l160m01bs = (List<L160M01B>) findListByMainId(
					L160M01B.class, meta.getMainId());

			String branCode = user.getUnitNo();
			Date sDate = new Date();
			Calendar rightNow = Calendar.getInstance();
			rightNow.setTime(sDate);
			rightNow.add(Calendar.MONTH, NumberUtils.toInt(sysParameterService
					.getParamValue(SysParamConstants.IVR_Query_StartMonth)));
			sDate = rightNow.getTime();
			Date dDate = new Date();
			SimpleDateFormat ft = new SimpleDateFormat("yyyyMMdd");
			String startDate = ft.format(sDate);
			String endDate = ft.format(dDate);
			String lightId = user.getLightId();
			String userId = user.getUserId();

			for (L160M01B l160m01b : l160m01bs) {
				String reMainId = l160m01b.getReMainId();
				L140M01A l140m01a = l140m01aDao.findByMainId(reMainId);
				String custId = l140m01a.getCustId();
				String projClass = l140m01a.getProjClass();

				// branCode="";
				// startDate="20190601";
				// endDate="20200527";
				// custId="A123456789";

				IVRGwReqMessage req = new IVRGwReqMessage(custId, startDate,
						endDate, lightId, userId, branCode);
				if (Util.notEquals(custId, "") && Util.notEquals(startDate, "")
						&& Util.notEquals(endDate, "")
						&& Util.equals(projClass, LMSUtil.特定金錢信託受益權自行設質擔保授信)) {
					List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
					lists = this.find(req);
					for (Map<String, Object> each : lists) {
						list.add(each);
					}
				}
			}
		} catch (Throwable t1) {
			throw new CapMessageException(t1.getMessage(), getClass());
		}
		return list;
	}

	public List<Map<String, Object>> find(IVRGwReqMessage req) {
		/*
		 * http://192.168.211.127/megabank/web/admin/midi_rec_edit.php?lightID=
		 * ISEhfDAwODAzNHxFTHwwODZBOTQzNDRBRjk2QTY2NTA2NzJFQThCMTJBODdBQXwxNTIwNDE5ODAwMDR3
		 * &&UserID=008034&RECORD_FILENAME=201906050090039684320
		 */

		// ivrGwClient.init();
		List<Map<String, Object>> list = ivrGwClient.send(req);

		return list;
	}

	public L140M01A findByMainId(String reMainId) {
		return l140m01aDao.findByMainId(reMainId);
	}

	/**
	 * J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
	 * 
	 * @param list
	 * @return
	 */
	public List<L162S01A> findL162s01aNeedPriority(List<L162S01A> list,
			String cntrNo) {

		List<L162S01A> newList = new ArrayList<L162S01A>();

		for (L162S01A l162m01a : list) {

			String tCustId = Util.trim(l162m01a.getRId());
			String tDupNo = Util.trim(l162m01a.getRDupNo());
			String tCntrNo = Util.trim(l162m01a.getCntrNo());

			// 要相同額度序號
			if (Util.notEquals(tCntrNo, cntrNo)) {
				continue;
			}

			Map<String, Object> custData = misCustdataService
					.findAllByByCustIdAndDupNo(tCustId, tDupNo);

			if (custData != null && !custData.isEmpty()) {
				// 保證人是個人戶就不要
				if (Util.equals(custData.get("BUSCD"), "130300")
						|| Util.equals(custData.get("BUSCD"), "060000")) {
					continue;
				}
			}

			String rType = Util.trim(l162m01a.getRType());
			if (Util.equals(rType, UtilConstants.lngeFlag.連帶保證人)
					|| Util.equals(rType, UtilConstants.lngeFlag.ㄧ般保證人)) {
				newList.add(l162m01a);
			}

		}

		return newList;
	}

	/**
	 * J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
	 */
	@Override
	public List<L162S01A> findL162s01aByMainIdCntrno(String mainId,
			String cntrNo) {
		return l162s01aDao.findByMainIdCntrNo(mainId, cntrNo);
	}

	/**
	 * J-110-0040_05097_B1001 Web e-Loan增加「本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)」註記
	 * 
	 * @param oids
	 * @return
	 */
	@Override
	public List<L162S01A> findL162S01AByOids(String[] oids) {
		return l162s01aDao.findByOids(oids);
	}

	/**
	 * 信保保證書
	 * 
	 * 
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public String executeRPAJobs_07(L161S01E l161s01e, String token)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String errorMsg = "", processKey = "";
		Map<String, Object> objResult = new LinkedHashMap<String, Object>();
		JSONObject resultJson = null;
		boolean isTestEmail = "true"
				.equals(PropUtil.getProperty("isTestEmail")) ? true : false; // 是否為測試信件
		try {
			// STEP2 啟動JOB-公司登記事項卡查詢
			objResult = new LinkedHashMap<String, Object>();
			try {
				logger.info("啟動JOB-信保保證書查詢 開始========================");

				String cntrCustid = l161s01e.getCustId();

				L161S01D l161s01d = new L161S01D();
				l161s01d.setMainId(l161s01e.getMainId());
				l161s01d.setType(UtilConstants.RPA.TYPE.信保保證書);
				l161s01d.setStatus(UtilConstants.RPA.STATUS.查詢中); // 查詢中

				// 初始化回傳結果
				l161s01d.setRpaReturnData1("");
				l161s01d.setRpaReturnData2("");
				l161s01d.setRpaReturnData3("");
				l161s01d.setRpaReturnData4("");
				l161s01d.setRpaReturnData5("");
				l161s01d.setRpaReturnData6("");
				l161s01d.setRpaReturnData7("");
				l161s01d.setRpaReturnData8("");
				l161s01d.setRpaReturnData9("");

				l161s01d.setQueryTime(CapDate.getCurrentTimestamp());
				// 查詢條件一
				l161s01d.setRpaQueryReason1(cntrCustid);

				L120M01A l120m01a = l120m01aDao.findByMainId(l161s01e
						.getMainId());

				// J-110-0CCC_05097_B1001 Web
				// e-Loan新增國發基金協助新創事業紓困融資加碼方案微型企業簽報書格式
				// 信保保證書種類：1是小規 2是 新創
				if (l120m01a == null) {
					l120m01a = new L120M01A();
				}

				String rpaCesDate = "";

				// 正式環境
				if (l120m01a != null) {
					if (Util.notEquals(Util.trim(l120m01a.getCesMainId()), "")) {

						Map<String, Object> cesC120m01aMap = eloanDbBaseService
								.findCesC120m01aByMainId(Util.trim(l120m01a
										.getCesMainId()));
						if (cesC120m01aMap != null && !cesC120m01aMap.isEmpty()) {
							String COMPLETEDATE = Util.trim(MapUtils.getString(
									cesC120m01aMap, "COMPLETEDATE"));
							String UPDATETIME = Util.trim(MapUtils.getString(
									cesC120m01aMap, "UPDATETIME"));
							String APPROVETIME = Util.trim(MapUtils.getString(
									cesC120m01aMap, "APPROVETIME"));
							if (Util.notEquals(APPROVETIME, "")) {
								rpaCesDate = CapDate
										.formatyyyyMMddToDateFormat(CapDate
												.formatDateFormatToyyyyMMdd(
														APPROVETIME,
														"yyyy-MM-dd"),
												"yyyy-MM-dd");

							} else if (Util.notEquals(COMPLETEDATE, "")) {
								rpaCesDate = COMPLETEDATE;
							} else if (Util.notEquals(UPDATETIME, "")) {
								rpaCesDate = CapDate
										.formatyyyyMMddToDateFormat(CapDate
												.formatDateFormatToyyyyMMdd(
														UPDATETIME,
														"yyyy-MM-dd"),
												"yyyy-MM-dd");

							}

						}

					}
				}

				if (isTestEmail) {
					// 測試環境
					rpaCesDate = "2021-03-08";
				}

				l161s01d.setRpaQueryReason2(rpaCesDate); // 徵信核准日期

				save(l161s01d);
				l161s01dDao.flush();

				// 設定要傳遞的參數 LinkedHashMap 才可以按順序取出
				objResult
						.put("responseURL",
								sysparamService
										.getParamValue(SysParamConstants.RPA_GW_RESPONSE_URL_LMS)); // 回傳位址
																									// SIT
																									// :
																									// "http://192.168.53.85:9081/lms-web/app/schedulerRPA"
				objResult.put("system", "eloan");
				// J-109-0208_05097_B1002 Web e-Loan企金動審表一鍵查詢與列印
				objResult.put(
						"uniqueID",
						l161s01d.getMainId() + "|"
								+ l161s01d.getRpaQueryReason1());

				// objResult.put("DeferDate", StringUtils.replace(CapDate
				// .getCurrentTimestamp().toString(), " ", ""));

				String firstPartOfPattern = "yyyy-MM-dd";
				String secondPartOfPattern = "HH:mm:ss";

				String RPACORPORATELOANGUAR_DEFERDATE = Util
						.trim(lmsService
								.getSysParamDataValue("RPACORPORATELOANGUAR_DEFERDATE"));

				long deferDateMin = ((60 * 60)) * 1000; // 加一小時
				if (Util.notEquals(RPACORPORATELOANGUAR_DEFERDATE, "")) {
					deferDateMin = Util
							.parseLong(RPACORPORATELOANGUAR_DEFERDATE);
				}

				Timestamp deferDate = CapDate.getCurrentTimestamp();
				// 正式環境且為USER ID為RPA，要多一小時
				boolean isRpaUser = userInfoService.isRPAUser(user.getUserId());
				if (isRpaUser) {
					deferDate.setTime(CapDate.getCurrentTimestamp().getTime()
							+ deferDateMin); // 加一小時
				}

				if (isTestEmail) {
					// TESTING****************************************************************
					deferDate = CapDate.getCurrentTimestamp();
				}

				SimpleDateFormat sdf1 = new SimpleDateFormat(firstPartOfPattern);
				sdf1.setTimeZone(TimeZone.getTimeZone("GMT"));
				SimpleDateFormat sdf2 = new SimpleDateFormat(
						secondPartOfPattern);
				sdf2.setTimeZone(TimeZone.getTimeZone("GMT"));
				String formattedDate = sdf1.format(deferDate) + "T"
						+ sdf2.format(deferDate) + ".4407392Z";

				objResult.put("DeferDate", formattedDate);

				objResult.put("data_BranchNo", user.getUnitNo());
				objResult.put(
						"empNo",
						CapString.isNumeric(user.getUserId()) ? String.format(
								"%06d", Integer.valueOf(user.getUserId()))
								: user.getUserId());

				// 查詢條件
				objResult.put("data_Id", l161s01d.getRpaQueryReason1());

				// 徵信報告核准日期
				String cesDate = Util.equals(
						Util.trim(l161s01d.getRpaQueryReason2()), "") ? ""
						: CapDate.formatDate(Util.parseDate(Util.trim(l161s01d
								.getRpaQueryReason2())), "yyyy-MM-dd");

				objResult.put("data_CesDate", cesDate);

				// J-110-0CCC_05097_B1001 Web
				// e-Loan新增國發基金協助新創事業紓困融資加碼方案微型企業簽報書格式
				// 信保保證書種類：1是小規 2是 新創
				if (lmsService.isL120m01aRescueSmallBussHasCaseC(l120m01a)) {
					objResult.put("data_caseType", "1"); // 小規
				} else if (lmsService
						.isL120m01aHasStartUpReliefCntrDoc(l120m01a)) {
					objResult.put("data_caseType", "2"); // 國發基金協助新創事業紓困融資加碼方案
				} else {
					objResult.put("data_caseType", "1"); // 預設小規
				}

				logger.info("傳入參數==>[{}]", objResult.toString());

				resultJson = rpaservice.StartRPAJobForLMS(objResult, token,
						processKey, SysParamConstants.RPA_信保保證書);

				logger.info("啟動JOB-信保保證書查詢 結束========================");
			} catch (Exception e) {
				errorMsg = "RPA Job建立失敗，請稍後再試。";
				throw new CapMessageException("RPA Job建立失敗，請稍後再試。",
						this.getClass());
			}
		} catch (Exception e) {
			throw new CapMessageException(errorMsg, this.getClass());
		}

		return errorMsg;
	}

	/**
	 * J-111-0214_05097_B1001 Web e-Loan國內企金動用審核表新增可適用新利率計算減免息相關功能
	 * 
	 * 判斷是否需要顯示客戶是否申請調整補貼利率
	 * 
	 * @param rescueItem
	 * @return
	 */
	@Override
	public boolean needRescueChgRateFg(String rescueItem) {
		boolean isNeed = false;

		// LMS_RESCUEITEM_CHG_RATE A01,A04,A03,A06,A07 可調整並適用新利率計算減免息
		String showRescueChgRateFg = Util.trim(lmsService
				.getSysParamDataValue("LMS_RESCUEITEM_CHG_RATE"));

		if (Util.notEquals(showRescueChgRateFg, "")) {
			for (String xx : showRescueChgRateFg.split(",")) {
				if (Util.equals(xx, rescueItem)) {
					isNeed = true;
					break;
				}
			}
		}

		return isNeed;
	}

	/**
	 * J-111-0214_05097_B1001 Web e-Loan國內企金動用審核表新增可適用新利率計算減免息相關功能
	 * 
	 * @param rescueChgRateSingDate
	 * @param approveDateStr
	 * @return
	 */
	@Override
	public Map<String, String> cacuRescueChgRateEffectDate(
			String rescueChgRateSingDate, String approveDateStr) {
		HashMap<String, String> returnMap = new HashMap<String, String>();
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1601M01Page.class);
		Date rescueChgRateEffectDate = null;
		Date aloanCacuRateDate = null;

		if (Util.isEmpty(rescueChgRateSingDate) || Util.isEmpty(approveDateStr)) {
			returnMap.put("isOk", "N");
			returnMap.put("errorMsg",
					"欄位「" + prop.getProperty("L161S01A.rescueChgRateSingDate")
							+ "」或「核准日期 」不得空白。");
			return returnMap;
		}

		Date signDate = Util.parseDate(rescueChgRateSingDate);
		Date approveDate = Util.parseDate(approveDateStr);
		Date baseDate = approveDate.compareTo(signDate) >= 0 ? approveDate
				: signDate;

		Date baseDate21 = Util.parseDate(Util.getLeftStr(
				CapDate.formatDate(baseDate, "yyyy-MM-dd"), 7)
				+ "-21");

		if (baseDate.compareTo(baseDate21) < 0) {
			rescueChgRateEffectDate = baseDate21;

		} else {
			rescueChgRateEffectDate = CapDate.addMonth(baseDate21, 1);
		}

		aloanCacuRateDate = Util.parseDate(Util.getLeftStr(CapDate.formatDate(
				CapDate.addMonth(rescueChgRateEffectDate, 1), "yyyy-MM-dd"), 7)
				+ "-20");

		returnMap.put("isOk", "Y");
		returnMap.put("rescueChgRateEffectDate",
				CapDate.formatDate(rescueChgRateEffectDate, "yyyy-MM-dd"));
		returnMap.put("aloanCacuRateDate",
				CapDate.formatDate(aloanCacuRateDate, "yyyy-MM-dd"));

		return returnMap;
	}

	@Override
	public boolean isAddAllocateFundsCheckList(List<L160M01B> l160m01bs,
			boolean isCheckEjcicB29B33) {

		if (l160m01bs != null) {
			for (L160M01B l160m01b : l160m01bs) {

				L140M01A l140m01a = amlRelateService.findModelByMainId(
						L140M01A.class, l160m01b.getReMainId());
				if (l140m01a != null) {

					L140M01M l140m01m = this.l140m01mDao.findByMainId(l140m01a
							.getMainId());
					if (l140m01m != null) {
						if (this.clsService
								.isAddAllocateFundsCheckListAndQueryEjcicB29B33(
										l140m01a.getCntrNo(), l140m01m,
										isCheckEjcicB29B33)) {
							return true;
						}
					}
				}
			}
		}

		return false;
	}

	@Override
	public String check31ItemIsChecked(String mainId, String itemType,
			Integer itemSeq) {

		L160M01C l160m01c = this.getL160M01CByUniqueKey(mainId, itemType,
				itemSeq);
		if (null != l160m01c && !"1".equals(l160m01c.getItemCheck())) {
			return this.clsService.getCheckAllocateFundsCheckListItemMessage();
		}

		return "";
	}

	@Override
	public L160M01C getL160M01CByUniqueKey(String mainId, String itemType,
			Integer itemSeq) {
		return this.l160m01cDao.findByUniqueKey(mainId, itemType, itemSeq);
	}

	/**
	 * J-111-0499 為改善常見覆審建檔缺失, 增加有關額度建檔及利率建檔缺漏之檢核及訊息提示 1. eloan上送合併科目限額之註記
	 */
	@Override
	public HashMap<String, String> getIsLnapFlag(L140M01A l140m01a) {
		HashMap<String, String> resultMap = new HashMap<String, String>();

		if (l140m01a.getL140m01c() != null) {
			for (L140M01C l140m01c : l140m01a.getL140m01c()) {
				boolean result = false;
				if (l140m01c != null) {
					String m01cLoanTP = Util.nullToSpace(l140m01c.getLoanTP());
					for (L140M01D l140m01d : l140m01a.getL140m01d()) {
						if (!result) {
							if (l140m01d != null) {
								if (Util.notEquals(l140m01d.getLmtType(),
										UtilConstants.Cntrdoc.LMTTYPE.科子目合併限額)) {
									continue;
								}

								String[] subject = l140m01d.getSubject().split(
										UtilConstants.Mark.SPILT_MARK);
								for (String item : subject) {
									if (Util.isNotEmpty(Util.nullToSpace(item))) {
										if (Util.equals(m01cLoanTP,
												Util.nullToSpace(item))) {
											result = true;
											break;
										}
									}
								}
							}
						}
					}
					resultMap.put(m01cLoanTP, (result ? "Y" : "N"));
				}
			}
		}

		return resultMap;
	}

	@Override
	public void checkIsRemoveAllocateFundsCheckListItem(
			List<L160M01B> newL160m01bs, List<L160M01C> l160m01cs) {

		if (!this.isAddAllocateFundsCheckList(newL160m01bs, false)) {
			l160m01cs.remove(30);
		} else {
			l160m01cs.get(30).setItemCheck("1");
		}
	}

	/**
	 * J-111-0207 國內企金，動用檢核表增加一項目:「信保案件負責人客戶基本資料檔是否已建有配偶資料」 取得檢核結果
	 */
	@Override
	public Map<String, String> findSmeMateInfoCheckResult(String mainId) {
		Map<String, String> checkResult = new HashMap<String, String>();
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1601M01Page.class);
		StringBuilder noMateBorrower = new StringBuilder();
		Map<String, String> custIdMap = new HashMap<String, String>();
		String key = "";
		String smeMateInfo = "";
		int mateCheckPassCount = 0;
		int mateCheckNaCount = 0;
		int mateCheckNgCount = 0;

		L160M01A l160m01a = this.findL160M01AByMaindId(mainId);
		if (l160m01a != null) {
			Set<L161S01A> l161s01as = l160m01a.getL161S01A();
			if (Util.isNotEmpty(l161s01as)) {
				for (L161S01A l161s01a : l161s01as) {
					if (Util.isEmpty(l161s01a.getSmeMateInfo())) {// 功能上線前，該欄位不會有值
						continue;
					}
					key = Util.trim(l161s01a.getCustId()).toUpperCase()
							+ Util.trim(l161s01a.getDupNo()).toUpperCase();

					if (Util.equals("0", l161s01a.getSmeMateInfo())) {// 0:NA
						mateCheckNaCount++;
					}
					if (Util.equals("1", l161s01a.getSmeMateInfo())) {// 1:有(有建配偶檔)
						mateCheckPassCount++;
					}
					if (Util.equals("2", l161s01a.getSmeMateInfo())) {// 2:無(未建配偶檔)
						mateCheckNgCount++;
						if (!custIdMap.containsKey(key)) {
							custIdMap.put(key, "");
							if (Util.isNotEmpty(l161s01a
									.getSmeChkMatePrincipal())) {
								if (Util.isNotEmpty(noMateBorrower)) {
									noMateBorrower.append("、");
								}
								noMateBorrower.append(l161s01a
										.getSmeChkMatePrincipal());
							}
						}
					}
				}
			}
			if (mateCheckNgCount > 0) {// 有負責人配偶未建檔的信保額度
				smeMateInfo = "2";// 2:無(未建配偶檔)
				if (Util.isNotEmpty(noMateBorrower.toString())) {
					checkResult.put("noMateBorrower",
							pop.getProperty("L160M01A.message115") + "："
									+ noMateBorrower.toString());
				} else {
					checkResult.put("noMateBorrower",
							pop.getProperty("L160M01A.message115"));
				}
			} else {
				if (mateCheckPassCount > 0) {
					// 有信保額度且該額度負責人配偶有建檔
					smeMateInfo = "1";// 1:有(有建配偶檔)
				} else if (mateCheckNaCount > 0) {
					// 沒有信保額度
					smeMateInfo = "0";// 0:NA
				}
			}
		}
		checkResult.put("smeMateInfo", smeMateInfo);

		return checkResult;
	}

	/**
	 * J-112-0148_05097_B1002 Web
	 * e-Loan企金授信新增經濟部協助中小型事業疫後振興專案貸款暨經濟部協助中小企業轉型發展專案貸款
	 * 
	 * 額度編號
	 * 
	 * @param rescueItem
	 * @return
	 */
	@Override
	public boolean isResueItemRescueSn(String rescueItem) {
		boolean isNeed = false;

		if (Util.equals(Util.trim(rescueItem), "")) {
			return isNeed;
		}

		String showSubSidyRateItem = Util.trim(lmsService
				.getSysParamDataValue("LMS_RESCUEITEM_RESCUESN"));

		if (Util.notEquals(showSubSidyRateItem, "")) {
			for (String xx : showSubSidyRateItem.split(",")) {
				if (Util.equals(xx, rescueItem)) {
					isNeed = true;
					break;
				}
			}
		}

		return isNeed;
	}

	/**
	 * 掛件文號改成動態顯示名稱
	 */
	@Override
	public String reloadRescueItemData(String rescueItem) {

		String rescueNoName = "";
		if (Util.isEmpty(Util.trim(rescueItem))) {
			return rescueNoName;
		}

		// 額度佔用編號
		// 預設值 LMS_RESCUEITEM_RESCUENO_DF 掛件文號
		String rescueNoNameDf = Util.trim(lmsService
				.getSysParamDataValue("LMS_RESCUEITEM_RESCUENO_DF"));
		if (Util.notEquals(rescueNoNameDf, "")) {
			rescueNoName = rescueNoNameDf;
		}

		// LMS_RESCUEITEM_RESCUENO_CHG
		// "F01":"額度佔用編號","F02":"額度佔用編號","F03":"額度佔用編號","F04":"額度佔用編號","F05":"額度佔用編號"
		String LMS_RESCUEITEM_RESCUENO_CHG = Util.trim(lmsService
				.getSysParamDataValue("LMS_RESCUEITEM_RESCUENO_CHG"));

		if (Util.notEquals(LMS_RESCUEITEM_RESCUENO_CHG, "")) {
			JSONObject jsonRescueNoName = JSONObject.fromObject("{"
					+ LMS_RESCUEITEM_RESCUENO_CHG + "}");
			if (jsonRescueNoName != null) {
				String tRescueNoName = Util.trim(jsonRescueNoName.optString(
						rescueItem, ""));
				if (Util.notEquals(tRescueNoName, "")) {
					rescueNoName = tRescueNoName;
				}
			}
		}

		return rescueNoName;

	}

	/**
	 * 額度向下僅有該科目時不用上傳QUOTAPPR
	 * 
	 * 檢查是否為需要上傳科目 J-112-0129_05097_B1001 Web
	 * e-Loan系統對於符合單純僅有「交換票據抵用」會計科目之額度序號，不要帶入額度介面檔
	 * 
	 * @param item
	 *            授信科目
	 * @return boolean
	 */
	@Override
	public boolean isNoUploadQuotapprItem(String item) {
		boolean isNoNeed = false;
		String LMS_NO_UPLOAD_QUOTAPPR_ITEM = Util.trim(lmsService
				.getSysParamDataValue("LMS_NO_UPLOAD_QUOTAPPR_ITEM"));

		if (Util.notEquals(LMS_NO_UPLOAD_QUOTAPPR_ITEM, "")) {
			for (String xx : LMS_NO_UPLOAD_QUOTAPPR_ITEM.split(",")) {
				if (Util.equals(xx, item)) {
					isNoNeed = true;
					break;
				}
			}
		}

		return isNoNeed;
	}

	/**
	 * G-113-0036_11850_B1001 Web e-Loan動審表主從債務人
	 */
	@Override
	public L162S01A findS01AByCustIdCntrNo(String custId, String dupNo, String cntrNo) {
		return l162s01aDao.findS01AByCustIdCntrNo(custId, dupNo, cntrNo);
	}
	
	@Override
	public List<L162S01A> checkL162s01aLocalIdAndGrtAmt(String custId, String dupNo, String cntrNo, List<L162S01A> newL162s01as) {
		//G-113-0036 主從債務人
		//取得 MIS.ELLNGTEE(ELF401)，如額度明細引入之主從債務人 擔保限額Guarante Amount(GRTAMT)、當地客戶識別ID Local Id(LOCALID)未填，則帶MIS.ELLNGTEE為預設值
		List<Map<String, Object>> ellngteeDatas = misELLNGTEEService.getAllDataByCustIdCntrNo(custId, dupNo, cntrNo);
		//
		for (L162S01A finall162s01a : newL162s01as) {
			for (Map<String, Object> ellngteeData : ellngteeDatas) {
				String cntrno = (String) (ellngteeData.get("CNTRNO") == null ? "" : ellngteeData.get("CNTRNO"));
				String custid = (String) (ellngteeData.get("CUSTID") == null ? "" : ellngteeData.get("CUSTID"));
				String dupno = (String) (ellngteeData.get("DUPNO") == null ? "" : ellngteeData.get("DUPNO"));
				String rtype = (String) (ellngteeData.get("LNGEFLAG") == null ? "" : ellngteeData.get("LNGEFLAG"));
				String rid = (String) (ellngteeData.get("LNGEID") == null ? "" : ellngteeData.get("LNGEID"));
				String rdupno = (String) (ellngteeData.get("DUPNO1") == null ? "" : ellngteeData.get("DUPNO1"));
				BigDecimal grtamt = Util.parseBigDecimal(ellngteeData.get("GRTAMT"));
				String localid = (String) (ellngteeData.get("LOCALID") == null ? "" : ellngteeData.get("LOCALID"));

				if(Util.isEmpty(finall162s01a.getGrtAmt()) || Util.isEmpty(finall162s01a.getLocalId())){
					//PK相符則帶入預設資訊
					if (Util.equals(cntrno, finall162s01a.getCntrNo()) && Util.equals(custid, finall162s01a.getCustId())
							&& Util.equals(dupno, finall162s01a.getDupNo()) && Util.equals(rtype, finall162s01a.getRType())
							&& Util.equals(rid, finall162s01a.getRId()) && Util.equals(rdupno, finall162s01a.getRDupNo())) {
						if(Util.isEmpty(finall162s01a.getGrtAmt())){
							finall162s01a.setGrtAmt(grtamt);
						}
						if(Util.isEmpty(finall162s01a.getLocalId())){
							finall162s01a.setLocalId(localid);
						}
					}
				}
			}
		}		
		
		return newL162s01as;
	}
	
	/**
	 * J-113-0035 貸後追蹤分項紀錄檔
	 * @param l160m01a
	 * @param l120m01a
	 * @param l140m01as
	 * @param l140m01a
	 * @param updater
	 * @param sDate
	 * @throws CapException
	 */
	private void updateELF603(L160M01A l160m01a, L120M01A l120m01a, List<L140M01A> l140m01as, String updater, String sDateTime)
			throws CapException {
		String casemainid = l120m01a.getMainId();
		//String mainid = l140m01a.getMainId();
		//List<L140S12A> l140s12as = lmsService.findL140s12aListByCaseMainId(casemainid);
		
		if(!l140m01as.isEmpty()){
			String apptime =  CapDate.convertTimestampToString(l120m01a.getApproveTime(), UtilConstants.DateFormat.YYYY_MM_DD_HH_MM_SS);
			//CapDate.formatDate(l120m01a.getApproveTime(), UtilConstants.DateFormat.YYYY_MM_DD_HH_MM_SS);	
			for (L140M01A l140m01a : l140m01as) {
				//603刪除
				misELF603Service.delByUidIdAppDateCntrNo(casemainid, apptime, l140m01a.getCntrNo());
				//額度明細如為不變、取消 則不送
				if ((LMSUtil.isContainValue(Util.trim(l140m01a.getProPerty()),
						UtilConstants.Cntrdoc.Property.不變))
						|| LMSUtil.isContainValue(
								Util.trim(l140m01a.getProPerty()),
								UtilConstants.Cntrdoc.Property.取消)) {
					continue;
				}
				String mainid = l140m01a.getMainId();
				List<L140S12A> l140s12as = lmsService.findL140s12aListByMainId(mainid);
				if(!l140s12as.isEmpty()){
					for (L140S12A l140s12a : l140s12as) {
						String uid = casemainid;//簽案文件編號//l140s12a.getMainId();
						int seqno = l140s12a.getSeqNum();
						String cntrNo = l140s12a.getCntrNo();
						String esgtype = l140s12a.getEsgType();
						String esgmodel = l140s12a.getEsgModel();
						String tracond = l140s12a.getTraceCondition();
						String traprofik = l140s12a.getTraceProfiling();
						int tramonth = l140s12a.getTraceProfilingMonth(); 
						String content = l140s12a.getContentText();
						//ELF603上傳
						misELF603Service.insertForInside(uid, apptime, cntrNo, seqno, esgtype, esgmodel, tracond, traprofik, tramonth, content, updater, sDateTime);
						//追蹤項目>追蹤週期為其他時，要多寫601
						if(Util.equals("3", l140s12a.getTraceCondition())){
							this.updateELF601(l160m01a, l140s12a, updater, sDateTime, l160m01a.getOwnBrId(), uid, l120m01a.getApproveTime(), seqno);	
						}

					}
				}else{//不追蹤 寫一筆空的讓ALOAN判斷此額度無資料
					String uid = casemainid;//簽案文件編號//l140s12a.getMainId();
					int seqno = 1;
					String cntrNo = l140m01a.getCntrNo();
					String esgtype = "X";
					String esgmodel = "";
					String tracond = "";
					String traprofik = "";
					int tramonth = 0; 
					String content = "";
					//ELF603上傳
					misELF603Service.insertForInside(uid, apptime, cntrNo, seqno, esgtype, esgmodel, tracond, traprofik, tramonth, content, updater, sDateTime);
				}
				
			}
		}

	}
	
	/**
	 * J-113-0035 其他續做條件追蹤分項，起始追蹤日為其他時寫入ELF601
	 * @param l140s12a
	 * sDateTime
	 */
	private void updateELF601(L160M01A l160m01a, L140S12A l140s12a, String updater, String sDateTime, String brNo, String elf603_uid, Timestamp elf603_apptime, int elf603_seqno){
    	// -------------L140S12A 起始追蹤日(traceCondition)=3.其他，寫ELF601 start-------------
		if(l140s12a != null) {
			Properties prop = MessageBundleScriptCreator.getComponentResource(LMS1601M01Page.class);
			String oid = l140s12a.getOid();
			//	==> L160M01A.OID + _BATCH_   + Timestamp(到秒) 塞 ELF601_UNID ( KEY )
			String actApproveTimeString = sDateTime.replace("-", "").replace(" ", "").replace(":", "").replace(" ", "");
			String unid = oid + "_L140S12A_"+ actApproveTimeString;// UNID有_L140S12A_則一定是這支產生的ELF601，以及相關的ELF602
			String unidForLike = oid + "_L140S12A_" + actApproveTimeString;// OID+_L140S12A_則一定是這支產生的ELF601，以及相關的ELF602
			boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchService.getBranch(brNo).getBrNoFlag());
			
			// 寫入ELF601的資料 start
			ELF601 elf601Insert = new ELF601();
			elf601Insert.setElf601_unid(unid);// 序號(UNID)
			
			String cntrNo = Util.trim(l140s12a.getCntrNo());
			String cntrnoStringForContent = Util.trim(cntrNo + prop.getProperty("L140S12A.message03"));// L140S12A.message03=額度明細表：參閱 應注意/承諾/待追蹤/ESG連結條款明細 內容。

			elf601Insert.setElf601_cntrno(cntrNo);
			elf601Insert.setElf601_loan_no("");// 放款帳號，放空白
			elf601Insert.setElf601_loan_kind("LN");// 業務別
			
			String esgtype = Util.trim(l140s12a.getEsgType());
			elf601Insert.setElf601_fo_kind(esgtype);// 類別
			elf601Insert.setElf601_fo_content(cntrnoStringForContent);// 追蹤事項通知內容
			//elf601Insert.setElf601_fo_way("1");// 追蹤方式
			//取得追蹤週期<p/>
			//1.一次  2.週期月  3.核准日後6個月內  4.核准日後6個月第一次，其後每12個月一次
			//基準日先以動審核准隔天算 
			String traceProfiling = Util.trim(l140s12a.getTraceProfiling());
			if(Util.equals("2", traceProfiling)){//2.週期月
				elf601Insert.setElf601_fo_way("2");// 追蹤方式: 2-循環週期
				elf601Insert.setElf601_fo_cycle(BigDecimal.valueOf(l140s12a.getTraceProfilingMonth()));// 循環追蹤週期（月）
			}else {
				elf601Insert.setElf601_fo_way("1");// 追蹤方式: 1-特定日期
				elf601Insert.setElf601_fo_cycle(BigDecimal.valueOf(0));// 循環追蹤週期（月）
			}
			// 下次追蹤日: 基準日先以動審核准隔天算 ，待金襄確認
			Date baseDate = CapDate.getDate(sDateTime, UtilConstants.DateFormat.YYYY_MM_DD);
			Date nextDate = CapDate.shiftDays(baseDate, 1);

			elf601Insert.setElf601_fo_next_date(new java.sql.Date(nextDate.getTime()));// 下次追蹤日
			elf601Insert.setElf601_staff("02");// 應辦理追蹤對象  02-OA人員
			
			String apprId = Util.trim(l160m01a.getApprId());
			String reCheckId = Util.trim(l160m01a.getReCheckId());
			elf601Insert.setElf601_fo_staffNo("");// 應辦理追蹤對象行編-授信人員
			elf601Insert.setElf601_ao_staffNo(apprId);// 應辦理追蹤對象行編-AO人員
			elf601Insert.setElf601_status("N");// 狀態  N-新增
			elf601Insert.setElf601_cre_date(CapDate.getCurrentTimestamp());// 建檔日期
			elf601Insert.setElf601_cre_teller(apprId);// 建檔經辦  from動審表
			elf601Insert.setElf601_cre_supvno(reCheckId);// 建檔主管  from動審表
			elf601Insert.setElf601_upd_date(CapDate.getCurrentTimestamp());// 建檔日期
			elf601Insert.setElf601_upd_teller(apprId);// 建檔經辦  from動審表
			elf601Insert.setElf601_upd_supvno(reCheckId);// 建檔主管  from動審表
			elf601Insert.setElf601_full_content(Util.trimSizeInOS390(
					Util.toFullCharString(elf601Insert.getElf601_fo_content()), 402));// 追蹤事項通知內容（全形）
			String custId = Util.trim(l160m01a.getCustId());
			elf601Insert.setElf601_custid(custId);
			String dupNo = Util.trim(l160m01a.getDupNo());
			elf601Insert.setElf601_dupno(dupNo);
			elf601Insert.setElf601_br_no(brNo);
			elf601Insert.setElf601_case_mark("");// 設定案件註記，要給空字串，不然海外DB會ERROR
			
			elf601Insert.setElf601_suid(elf603_uid);
			elf601Insert.setElf601_sapptime(elf603_apptime);
			elf601Insert.setElf601_sseqno(new BigDecimal(elf603_seqno));
			// 此筆先行動用核准，要寫入ELF601的資料 end
			
			// 刪除同動審表oid相關的ELF601、ELF602，並新增ELF601 start
			if(isOverSea){
				// 同一筆動審表且同一核准時間產生的ELF601，照理講不會有才對
				ELF601 elf601Delete = obsdbELF601Service.getElf601ByUnid(brNo, unid);
				if (elf601Delete != null && Util.isNotEmpty(elf601Delete)) {
					logger.info("updateELF601 deleteELF601 ByUnid，brNo: " + brNo + ", unid: " + unid);
					obsdbELF601Service.deleteELF601(brNo, unid);
				}
				// 同一筆動審表且同一核准時間產生的ELF602，照理講不會有才對
				List<ELF602> elf602DeleteList = obsdbELF601Service.getElf602ByDatasrc(brNo, unid);
				for(ELF602 elf602Delete : elf602DeleteList){
					if (elf602Delete != null && Util.isNotEmpty(elf602Delete)) {
						logger.info("updateELF601 deleteELF602 ByDatasrc，brNo: " + brNo + ", datasrc: " + unid);
						obsdbELF601Service.deleteELF602(brNo, elf602Delete.getElf602_unid());
					}
				}
				
				// 同一筆動審表且不同核准時間產生的ELF601，可能是動審表重複核准(來來回回)產生的舊資料，應當刪除避免虛增
				List<ELF601> elf601LikeDeleteList = obsdbELF601Service.getElf601ByUnidLike(brNo, unidForLike + "%");
				for(ELF601 elf601LikeDelete : elf601LikeDeleteList){
					if (elf601LikeDelete != null && Util.isNotEmpty(elf601LikeDelete) && elf601LikeDelete.getElf601_unid().contains(unidForLike)) {
						logger.info("updateELF601 deleteELF601 ByUnidLike，brNo: " + brNo + ", unidForLike: " + unidForLike);
						obsdbELF601Service.deleteELF601(brNo, elf601LikeDelete.getElf601_unid());
					}
				}
				// 同一筆動審表且不同核准時間產生的ELF602，可能是動審表重複核准(來來回回)產生的舊資料，應當刪除避免虛增
				List<ELF602> elf602LikeDeleteList = obsdbELF601Service.getElf602ByDatasrcLike(brNo, unidForLike + "%");
				for(ELF602 elf602LikeDelete : elf602LikeDeleteList){
					if (elf602LikeDelete != null && Util.isNotEmpty(elf602LikeDelete) && elf602LikeDelete.getElf602_datasrc().contains(unidForLike)) {
						logger.info("updateELF601 deleteELF602 ByDatasrcLike，brNo: " + brNo + ", datasrcLike: " + unidForLike);
						obsdbELF601Service.deleteELF602(brNo, elf602LikeDelete.getElf602_unid());
					}
				}
				
				logger.info("updateELF601 insertELF601，brNo: " + brNo + ", unid: " + elf601Insert.getElf601_unid());
				obsdbELF601Service.insertELF601(brNo, elf601Insert);
			} else {
				// 同一筆動審表且同一核准時間產生的ELF601，照理講不會有才對
				ELF601 elf601Delete = misdbBASEService.getElf601ByUnid(unid);
				if(elf601Delete != null && Util.isNotEmpty(elf601Delete)){
					logger.info("updateELF601 deleteELF601 ByUnid， unid: " + unid);
					misdbBASEService.deleteELF601(unid);
				}
				
				// 同一筆動審表且同一核准時間產生的ELF602，照理講不會有才對
				List<ELF602> elf602DeleteList = misdbBASEService.getElf602ByDatasrc(unid);
				for(ELF602 elf602Delete : elf602DeleteList){
					if (elf602Delete != null && Util.isNotEmpty(elf602Delete)) {
						logger.info("updateELF601 deleteELF602 ByDatasrc，datasrc: " + unid);
						misdbBASEService.deleteELF602(elf602Delete.getElf602_unid());
					}
				}
				
				// 同一筆動審表且不同核准時間產生的ELF601，可能是動審表重複核准(來來回回)產生的舊資料，應當刪除避免虛增
				List<ELF601> elf601LikeDeleteList = misdbBASEService.getElf601ByUnidLike(unidForLike + "%");
				for(ELF601 elf601LikeDelete : elf601LikeDeleteList){
					if (elf601LikeDelete != null && Util.isNotEmpty(elf601LikeDelete) && elf601LikeDelete.getElf601_unid().contains(unidForLike)) {
						logger.info("updateELF601 deleteELF601 ByUnidLike，unidForLike: " + unidForLike);
						misdbBASEService.deleteELF601(elf601LikeDelete.getElf601_unid());
					}
				}
				// 同一筆動審表且不同核准時間產生的ELF602，可能是動審表重複核准(來來回回)產生的舊資料，應當刪除避免虛增
				List<ELF602> elf602LikeDeleteList = misdbBASEService.getElf602ByDatasrcLike(unidForLike + "%");
				for(ELF602 elf602LikeDelete : elf602LikeDeleteList){
					if (elf602LikeDelete != null && Util.isNotEmpty(elf602LikeDelete) && elf602LikeDelete.getElf602_datasrc().contains(unidForLike)) {
						logger.info("doLmsBatch0051 deleteELF602 ByDatasrcLike，datasrcLike: " + unidForLike);
						misdbBASEService.deleteELF602(elf602LikeDelete.getElf602_unid());
					}
				}
				
				logger.info("updateELF601 insertELF601，unid: " + elf601Insert.getElf601_unid());
				misdbBASEService.insertELF601(elf601Insert);
			}
			// 刪除同本次動審表oid L140S12A相關的ELF601、ELF602，並新增ELF601 end
		}
	}

	/**
	 * J-113-0168 E-LOAN完成動審上送額度介面資料至A-LOAN時，針對屬授信額度者(排除衍生性金融商品業務外)，立即通知0024為本行授信戶
	 * @param l140m01a
	 * @param l160m01a
	 * @param brNo
	 */
	private void callLNSP0130ForUpdateLnFlag(L140M01A l140m01a, L160M01A l160m01a, String brNo){
		String controlFlag = Util.trim(lmsService.getSysParamDataValue("J-113-0168_FLAG"));
		if(Util.equals(UtilConstants.DEFAULT.是, controlFlag)){
			boolean uploadFlag = false;
			// 以下條件不送
			//1. 衍生性金融商品科目961 962 963 964 、 純Z類的虛科目
			//2. 應收帳款買方 fact_type = 60的不用通知
			String ruleOutSubItemString = Util.trim(lmsService.getSysParamDataValue("J-113-0168_RULEOUTSUBITEM"));
			String[] subItems = ruleOutSubItemString.split(UtilConstants.Mark.SPILT_MARK);
			List<String> subItemsList = Arrays.asList(subItems);
			//抓動審當下L161S01A設定的
			L161S01A ll61s01a = this.findL161m01aByMainIdCntrno(l160m01a.getMainId(), l140m01a.getCntrNo());
			List<L140M01C> l140m01cs = lms1401Service.findL140m01cListByMainId(l140m01a.getMainId());
			String snoKind = "";
			if(ll61s01a != null){
				snoKind = Util.trim(ll61s01a.getSnoKind());
			}
			for(L140M01C l140m01c : l140m01cs){
				if(Util.isNotEmpty(l140m01c.getLoanTP()) 
						&& !subItemsList.contains(l140m01c.getLoanTP())
						&& Util.notEquals(snoKind, UtilConstants.Cntrdoc.snoKind.應收帳款買方)
				){
					uploadFlag = true;//只要有一個科目不是就送
					break;
				}
			}
			//通知0024為本行授信戶
			if(uploadFlag){
				String updater = MegaSSOSecurityContext.getUserId();
				Map<String, Object> lnsp0130Map = msps.callLNSP0130ForUpLnFlag(l160m01a.getCustId(), l160m01a.getDupNo(), updater, brNo);
			}
		}

	}
}
