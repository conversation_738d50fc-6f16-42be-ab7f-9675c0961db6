package com.mega.eloan.lms.mfaloan.service;

import java.util.List;

import com.mega.eloan.lms.mfaloan.bean.ELF515;

/**
 * <pre>
 * 72 - 2
 * </pre>
 * 
 * @since 2018/9/13
 * <AUTHOR>
 * @version <ul>
 *          <li>2018/9/13,007625,new
 *          </ul>
 */
public interface MisELF515Service {

	/**
	 * 依額度序號和類別找72-2 註記資料
	 * 
	 * @param cntrNo
	 * @param type
	 * @return
	 */
	public ELF515 find(String cntrNo, String type);

 

	/**
	 * 依都更危老母戶額度序號找預約資料
	 * 
	 * @param mCntrNo
	 * @return
	 */
	ELF515 findMcntrNo(String mCntrNo);

	/**
	 * 依額度序號取得目前所有72-2資料(都更危老資料要去串母戶額度序號資料，sql有處理)
	 * 
	 * @param cntrNo
	 * @return
	 */
	List<ELF515> getCurrentDataByCntrNo(String cntrNo);

	/**
	 * 依額度序號取得目前所有72-2資料
	 * 
	 * @param cntrNo
	 * @return
	 */
	List<ELF515> find(String cntrNo);

	/**
	 * 刪除72-2資料
	 * 
	 * @param cntrNo
	 */
	void delete(String cntrNo);

	/**
	 * 新增資料
	 * 
	 * @param elf515
	 */
	void insert(ELF515 elf515);



	/**
	 * 批次資料insert，將ELF506的資料塞到ELF515
	 */
	void do72_2BatchFrom515();

}
