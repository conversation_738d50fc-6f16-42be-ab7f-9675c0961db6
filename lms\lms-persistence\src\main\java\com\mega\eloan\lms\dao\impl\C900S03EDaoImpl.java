package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.C900S03EDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C900S03E;

/** MIS_ELF488新承做檔  **/
@Repository
public class C900S03EDaoImpl extends LMSJpaDao<C900S03E, String>
	implements C900S03EDao {

	@Override
	public C900S03E findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}
	
	@Override
	public List<C900S03E> findByCYC_MN(String cyc_mn){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "cyc_mn", cyc_mn);
		search.setMaxResults(Integer.MAX_VALUE);
		List<C900S03E> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public int countByCYC_MN(String CYC_MN){
		Query query = getEntityManager().createNamedQuery("C900S03E.countByCYC_MN");
		// 設置參數
		query.setParameter(1, CYC_MN);
		Integer count = (Integer) query.getSingleResult();
		return count.intValue();
	}
}