/* 
 * L120S19C.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 無紙化簽報貸款費用檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L120S19C", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L120S19C extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 
	 * 文件編號
	 * 簽報書MAINID
	 */
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 費用代碼 **/
	@Size(max=2)
	@Column(name="FEENO", length=2, columnDefinition="CHAR(2)")
	private String feeNo;

	/** 費用幣別 **/
	@Size(max=3)
	@Column(name="FEECURR", length=3, columnDefinition="CHAR(3)")
	private String feeCurr;

	/** 費用金額 **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="FEEAMT", columnDefinition="DECIMAL(17,2)")
	private BigDecimal feeAmt;

	/** 備註 **/
	@Size(max=300)
	@Column(name="REMARK", length=300, columnDefinition="VARCHAR(300)")
	private String remark;

	/** 
	 * 修改角色
	 * AO(AO修改), RV(審查修改)
	 */
	@Size(max=2)
	@Column(name="ROLE", length=2, columnDefinition="CHAR(2)")
	private String role;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 
	 * 取得oid
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 
	 * 取得文件編號
	 * 簽報書MAINID
	 */
	public String getMainId() {
		return this.mainId;
	}
	/**
	 *  設定文件編號
	 *  簽報書MAINID
	 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得費用代碼 **/
	public String getFeeNo() {
		return this.feeNo;
	}
	/** 設定費用代碼 **/
	public void setFeeNo(String value) {
		this.feeNo = value;
	}

	/** 取得費用幣別 **/
	public String getFeeCurr() {
		return this.feeCurr;
	}
	/** 設定費用幣別 **/
	public void setFeeCurr(String value) {
		this.feeCurr = value;
	}

	/** 取得費用金額 **/
	public BigDecimal getFeeAmt() {
		return this.feeAmt;
	}
	/** 設定費用金額 **/
	public void setFeeAmt(BigDecimal value) {
		this.feeAmt = value;
	}

	/** 取得備註 **/
	public String getRemark() {
		return this.remark;
	}
	/** 設定備註 **/
	public void setRemark(String value) {
		this.remark = value;
	}

	/** 
	 * 取得修改角色
	 * AO(AO修改), RV(審查修改)
	 */
	public String getRole() {
		return this.role;
	}
	/**
	 *  設定修改角色
	 *  AO(AO修改), RV(審查修改)
	 **/
	public void setRole(String value) {
		this.role = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}
}
