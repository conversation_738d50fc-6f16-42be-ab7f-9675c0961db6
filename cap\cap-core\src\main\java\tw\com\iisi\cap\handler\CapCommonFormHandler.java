/*_
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */

package tw.com.iisi.cap.handler;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;

/**
 * <pre>
 * Common Handler
 * 防止IE在檔案作業完成後關閉status bar
 * 定義預設Operation
 * </pre>
 * 
 * @since 2011/3/22
 * <AUTHOR>
 * @version $Id$
 * @version
 *          <ul>
 *          <li>2011/3/22,RodesChen,new
 *          </ul>
 */
@Scope("request")
@Controller("commonformhandler")
public class CapCommonFormHandler extends MFormHandler {

    /**
     * <pre>
     * 檔案作業完成後以防IE status bar 不停止
     * </pre>
     * 
     * @param params
     *            RequestParameters
     * @param parent
     *            Component
     * @return IResult
     * @throws CapException
     */
    public IResult fileSuccess(PageParameters params) throws CapException {
        return new CapAjaxFormResult();
    }// ;

    /**
     * <pre>
     * DEFAULT_OPERATION
     * 預設
     * </pre>
     * 
     * @return String
     */
    public String getOperationName() {
        return "simpleOperation";
    }

}
