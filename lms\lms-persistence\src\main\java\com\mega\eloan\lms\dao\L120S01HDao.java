/* 
 * L120S01HDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S01H;


/** 個金基本資料檔 **/
public interface L120S01HDao extends IGenericDao<L120S01H> {

	L120S01H findByOid(String oid);
	
	List<L120S01H> findByMainId(String mainId);
	
	L120S01H findByUniqueKey(String mainId, String custId, String dupNo);

	List<L120S01H> findByIndex01(String mainId, String custId, String dupNo);
	
	List<L120S01H> findByCustIdDupId(String custId,String DupNo);
	
	List<L120S01H> findAll();
}