---------------------------------------------------------
-- LMS.LPDFM01A 授信 PDF 舊案主檔
---------------------------------------------------------
---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.LPDFM01A;
CREATE TABLE LMS.LPDFM01A (
	OID           CHAR(32)      not null,
	UID           CHAR(32)     ,
	MAIN<PERSON>        CHAR(32)     ,
	TYP<PERSON>         CHAR(1)      ,
	CUS<PERSON><PERSON>        VARCHAR(10)  ,
	<PERSON><PERSON><PERSON><PERSON>         CHAR(1)      ,
	<PERSON><PERSON><PERSON>NAME      VARCHAR(120) ,
	UNITTYP<PERSON>      CHAR(1)      ,
	OWNBRID       CHAR(3)      ,
	<PERSON>OC<PERSON><PERSON><PERSON>     VARCHAR(3)   ,
	<PERSON>NDOMCODE    CHAR(32)     ,
	DOCURL        VARCHAR(40)  ,
	TXCODE        CHAR(6)      ,
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATERNM     VARCHAR(30)  ,
	UPDATETIME    TIMESTAMP    ,
	APPROVER      CHAR(6)      ,
	APPROVERNM    VARCHAR(30)  ,
	APPROVETIME   TIMESTAMP    ,
	ISCLOSED      CHAR(1)      ,
	DELETEDTIME   TIMESTAMP    ,
	FORMTYPE      CHAR(1)      ,
	FORMNAME      VARCHAR(10)  ,
	FORMTEXT      VARCHAR(120) ,
	CASEDATE      DATE         ,
	ENDDATE       DATE         ,
	DOCTYPE       CHAR(1)      ,
	DOCKIND       CHAR(1)      ,
	DOCCODE       CHAR(1)      ,
	CASEYEAR      DECIMAL(4,0) ,
	CASEBRID      CHAR(3)      ,
	CASESEQ       DECIMAL(5,0) ,
	CASENO        VARCHAR(62)  ,
	AUTHLVL       VARCHAR(1)   ,
	CASELVL       CHAR(2)      ,
	JSONDATA      CLOB         ,

	constraint P_LPDFM01A PRIMARY KEY(OID)
) IN EL_DATA_4KTS
  INDEX IN EL_INDEX_4KTS;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XLPDFM01A01;
CREATE INDEX LMS.XLPDFM01A01 ON LMS.LPDFM01A   (MAINID, OWNBRID, CUSTID, DUPNO);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.LPDFM01A IS '授信 PDF 舊案主檔';
COMMENT ON LMS.LPDFM01A (
	OID           IS 'oid', 
	UID           IS 'uid', 
	MAINID        IS '文件編號', 
	TYPCD         IS '區部別', 
	CUSTID        IS '統一編號', 
	DUPNO         IS '重覆序號', 
	CUSTNAME      IS '客戶名稱', 
	UNITTYPE      IS '辦理單位類別', 
	OWNBRID       IS '編製單位代號', 
	DOCSTATUS     IS '目前文件狀態', 
	RANDOMCODE    IS '文件亂碼', 
	DOCURL        IS '文件URL', 
	TXCODE        IS '交易代碼', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATERNM     IS '異動人員名稱', 
	UPDATETIME    IS '異動日期', 
	APPROVER      IS '核准人員號碼', 
	APPROVERNM    IS '核准人員名稱', 
	APPROVETIME   IS '核准日期', 
	ISCLOSED      IS '是否結案', 
	DELETEDTIME   IS '刪除註記', 
	FORMTYPE      IS '報表類型', 
	FORMNAME      IS '報表名稱(來源套表名稱)', 
	FORMTEXT      IS '報表描述', 
	CASEDATE      IS '日期', 
	ENDDATE       IS '核准(婉卻)日期', 
	DOCTYPE       IS '企/個金案件', 
	DOCKIND       IS '授權別', 
	DOCCODE       IS '案件別', 
	CASEYEAR      IS '案件號碼-年度', 
	CASEBRID      IS '案件號碼-分行', 
	CASESEQ       IS '案件號碼-流水號', 
	CASENO        IS '案件號碼', 
	AUTHLVL       IS '授權等級', 
	CASELVL       IS '案件審核層級', 
	JSONDATA      IS '內容描述'
);
