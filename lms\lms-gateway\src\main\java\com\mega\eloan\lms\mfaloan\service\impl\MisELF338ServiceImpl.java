package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;

import tw.com.jcs.common.Util;

import com.mega.eloan.lms.mfaloan.service.MisELF338Service;

@Service
public class MisELF338ServiceImpl extends AbstractMFAloanJdbc implements
		MisELF338Service {
	public Map<String, Object> findELF338N_CRDT(String custId, String dupNo) {
		return this.getJdbc().queryForMap("MISELF338N.selL120s01c1",
				new String[] { custId, dupNo });
	}

	public List<?> findELF338L120s01c1(String custId, String dupNo,
			String countryType) {
		if (Util.equals(countryType, "AU")) {
			return this.getJdbc().queryForList("MISELF338N.selL120s01c1_AU",
					new Object[] { custId, dupNo }, 0, 1);
		} else {
			return this.getJdbc().queryForList("MISELF338N.selL120s01c1",
					new Object[] { custId, dupNo }, 0, 1);
		}

	}

	public List<?> findELF338L120s01c2(String custId, String dupNo) {
		// ELF338N_CRDTYPE ='NM' OR ELF338N_CRDTYPE ='NS' OR ELF338N_CRDTYPE
		// ='NF
		List<Map<String, Object>> result = new LinkedList<Map<String, Object>>();
		Map<String, Object> map = new HashMap<String, Object>();
		// J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等
		String[] crdType = { "NM", "NS", "NF", "NC", "NT", "NK" };
		for (String tCrdType : crdType) {
			map = this.getJdbc().queryForMap("MISELF338N.selL120s01c2",
					new Object[] { custId, dupNo, tCrdType });

			if (map != null && !map.isEmpty()) {
				// 如果GRAD是空的就代表最新一筆資信簡表沒有維護這個外部評等
				String ELF338N_GRADE = Util.trim(MapUtils.getString(map,
						"ELF338N_GRADE"));
				if (!Util.isEmpty(ELF338N_GRADE)) {
					result.add(map);
				}
			}
		}
		return result;
	}

	public List<?> findELF338L120s01c3(String custId, String dupNo, String brno) {
		// (ELF338N_CRDTYPE ='CA' OR ELF338N_CRDTYPE ='CB' OR ELF338N_CRDTYPE
		// ='CK')
		List<Map<String, Object>> result = new LinkedList<Map<String, Object>>();
		Map<String, Object> map = new HashMap<String, Object>();

		if (Util.isNotEmpty(Util.trim(brno))) {
			// 分行
			map = this.getJdbc().queryForMap("MISELF338N.selL120s01c3a",
					new Object[] { custId, dupNo, brno });
		} else {
			// 全行
			map = this.getJdbc().queryForMap("MISELF338N.selL120s01c3b",
					new Object[] { custId, dupNo });
		}
		if (map != null && !map.isEmpty()) {
			result.add(map);
		}
		/*
		 * 
		 * String [] crdType = {"CA","CB","CK"};
		 * 
		 * for(String tCrdType : crdType){ if(Util.isNotEmpty(Util.trim(brno))){
		 * // 分行 map = this.getJdbc().queryForMap("MISELF338N.selL120s01c3a",
		 * new Object[] { custId, dupNo, tCrdType, brno }); }else{ // 全行 map =
		 * this.getJdbc().queryForMap("MISELF338N.selL120s01c3b", new Object[] {
		 * custId, dupNo, tCrdType }); }
		 * 
		 * if(map != null && !map.isEmpty()){ result.add(map); } }
		 */
		return result;
	}
}
