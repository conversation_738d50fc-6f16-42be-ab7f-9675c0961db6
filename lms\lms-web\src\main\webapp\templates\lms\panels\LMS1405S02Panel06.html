<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
<body>
	<th:block th:fragment="LMS1405S02Panel06">
		<span id="page08isGutCut" style="display:none">
			<font style='font-size:16px; font-weight: bold;' color="red">
				<th:block th:text="#{'L140M01b.page08isGutCut'}">※信保機構保證書所囑事項，應詳實列入額度明細表中</th:block>
			</font>
		</span>
		<table class="tb2" width="100%" border="0" cellpadding="0" cellspacing="0">
			<tr class="hd1" style="text-align:left">
				<td colspan="2">
					<span style="display:none" class="caseSpan">
						<label>
							<input id="tab06" type="checkbox" class="caseBox"></input>
							<th:block th:text="#{'button.modify'}"><!--修改--></th:block>
						</label>
					</span>
					<select id="pageNum4" name="pageNum4" class="nodisabled">
						<option value="0" selected="selected">
							<th:block th:text="#{'L140M01b.printMain'}"><!--印於主表--></th:block>
						</option>
						<option value="1">
							<th:block th:text="#{'L140M01b.print01'}"><!--印於附表(一)--></th:block>
						</option>
						<option value="2">
							<th:block th:text="#{'L140M01b.print02'}"><!--印於附表(二)--></th:block>
						</option>
						<option value="3">
							<th:block th:text="#{'L140M01b.print03'}"><!--印於附表(三)--></th:block>
						</option>
					</select>
				</td>
			</tr>
			<tr>
				<td colspan="2">
					<button type="button" id="btn_ESG" class="forview AS400_ON">
						<th:block th:text="#{'L140S12A.memoEsgTerms'}"><!--登錄應注意/承諾/待追蹤/ESG連結條款--></th:block>
					</button>
					<textarea cols="100" rows="10%" id="itemDscr4" name="itemDscr4" class="tckeditor" showType="b" wicket:message="displayMessage:L140S02Tab.6" preview="width:800;heigth:300"></textarea>
				</td>
			</tr>
			<tr class="J-113-0035_check">
				<td class="hd1">
					<span class="text-red">
						<th:block th:text="#{'L140M01b.toALoan'}"><!--動撥提醒事項--></th:block>&nbsp;&nbsp;
					</span>
				</td>
				<td>
					<th:block th:text="#{'L140M01b.toALoan1'}"><!--注意事項--></th:block>：
					<input type="text" id="toALoan1" name="toALoan1" maxlength="30" maxlengthC="30" size="80" class="nodisabled fullText"></input><br>
					<th:block th:text="#{'L140M01b.toALoan2'}"><!--承諾事項--></th:block>：<br>
					<textarea id="toALoan2" name="toALoan2" maxlengthC="390" rows="5" cols="70" class="nodisabled fullText"></textarea>
				</td>
			</tr>
			<tr>
				<td class="hd1">
					<th:block th:text="#{'l1405s02p06.001'}"><!--附加檔案--></th:block>&nbsp;&nbsp;
				</td>
				<td>
					<button type="button" id="uploadFile" class="noHideBt">
						<span class="text-only">
							<th:block th:text="#{'l1405s02p06.002'}"><!--選擇附加檔案--></th:block>
						</span>
					</button>
					<button type="button" id="deleteFile" class="noHideBt">
						<span class="text-only">
							<th:block th:text="#{'button.delete'}"><!--刪除--></th:block>
						</span>
					</button><br>
					<div id="gridfile"></div>
				</td>
			</tr>
		</table>
		<div id="divESG" style="display:none;">
			<button type="button" id="btnESGNew" class="forview">
				<th:block th:text="#{'btn.add'}">新增</th:block>
			</button>
			<button type="button" id="btnESGMod" class="forview">
				<th:block th:text="#{'btn.mod'}">修改</th:block>
			</button>
			<button type="button" id="btnESGDel" class="forview">
				<th:block th:text="#{'btn.delete'}">刪除</th:block>
			</button>
			<table width="100%" border="0" cellpadding="0" cellspacing="0">
				<tr>
					<td>
						<div id="l140s12aGrid"></div>
					</td>
				</tr>
			</table>
		</div>
		<div id="divESGDetail" style="display:none;">
			<!--<form id="l140s12aFormDetail">-->
				<table id="l140s12aFormDetail" class="tb2" width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td class="hd1">
							<th:block th:text="#{'L140S12A.type'}">類別</th:block>
						</td>
						<td colspan="4">
							<table width="100%" border="0" cellspacing="0" cellpadding="0">
								<tbody>
									<tr>
										<td style="border:none;padding:0px;margin:0px;">
											<label style="letter-spacing:0px;cursor:pointer;">
												<input type="checkbox" id="esgType" name="esgType"></input>
											</label>
										</td>
									</tr>
								</tbody>
							</table>
						</td>
					</tr>
					<!--ESG 模板選項-->
					<tr class="tr_esgMsg_type">
						<td class="hd1 esgMsgSample" rowspan="4">
							<th:block th:text="#{'L140S12A.esgModel'}">ESG模板</th:block>
						</td>
						<td id="td_esgMsgE" class="td_esgMsg" colspan="4">
							<table id="table_esgMsgE_type2" width="100%" border="0" cellspacing="0" cellpadding="0">
								<tr>
									<td style="border:none;padding:0px;margin:0px;">
										<th:block th:text="#{'L140S12A.esgModelE'}">E環境: </th:block>
									</td>
								</tr>
								<tr class="">
									<td style="border:none;padding:0px;margin:0px;">
										<label style="letter-spacing:0px;cursor:pointer;">
											<input type="checkbox" id="esgMsgE" name="esgMsgE" class="checkbox_esgMsgAll"></input>
										</label>
									</td>
								</tr>
							</table>
						</td>
					</tr>
					<tr class="tr_esgMsg_type">
						<!--<td class="hd1"></td>上面rowspan-->
						<td id="td_esgMsgS" class="td_esgMsg" colspan="4">
							<table id="table_esgMsgS_type3" width="100%" border="0" cellspacing="0" cellpadding="0">
								<tr>
									<td style="border:none;padding:0px;margin:0px;">
										<th:block th:text="#{'L140S12A.esgModelS'}">S社會責任: </th:block>
									</td>
								</tr>
								<!--永續績效連結授信條件(跟利率計價或手續費減免等優惠措施有關)-->
								<tr class="">
									<td style="border:none;padding:0px;margin:0px;">
										<label style="letter-spacing:0px;cursor:pointer;">
											<input type="checkbox" id="esgMsgS" name="esgMsgS" class="checkbox_esgMsgAll"></input>
										</label>
									</td>
								</tr>
							</table>
						</td>
					</tr>
					<tr class="tr_esgMsg_type">
						<!--<td class="hd1"></td>上面rowspan-->
						<td id="td_esgMsgG" class="td_esgMsg" colspan="4">
							<table id="table_esgMsgG_type3" width="100%" border="0" cellspacing="0" cellpadding="0">
								<tr>
									<td style="border:none;padding:0px;margin:0px;">
										<th:block th:text="#{'L140S12A.esgModelG'}">G公司治理: </th:block>
									</td>
								</tr>
								<!--永續績效連結授信條件(跟利率計價或手續費減免等優惠措施有關)-->
								<tr class="">
									<td style="border:none;padding:0px;margin:0px;">
										<label style="letter-spacing:0px;cursor:pointer;">
											<input type="checkbox" id="esgMsgG" name="esgMsgG" class="checkbox_esgMsgAll"></input>
										</label>
									</td>
								</tr>
							</table>
						</td>
					</tr>
					<tr class="tr_esgMsgImporBtn">
						<!--<td class="hd1"></td>-->
						<td id="td_esgMsgBtn" class="td_esgMsg" colspan="4">
							<button type="button" id="btmImportEsgMsg">
								<span class="text-only">
									<th:block th:text="#{'btn.importText'}">勾選項目引入內容</th:block>
								</span>
							</button>
						</td>
					</tr>
					<tr>
						<td class="hd1">
							<th:block th:text="#{'L140S12A.traceConditionTitle'}">設定追蹤條件</th:block>
						</td>
						<td class="hd1">
							<span class="text-red">＊</span>
							<th:block th:text="#{'L140S12A.traceCondition'}">起始追蹤日</th:block>
						</td>
						<td style="width:100px">
							<label>
								<input name="traceCondition" type="radio" value="1"></input>
								<th:block th:text="#{'L140S12A.traceCondition_1'}">首次撥款</th:block>
							</label><br>
							<label>
								<input name="traceCondition" type="radio" value="2"></input>
								<th:block th:text="#{'L140S12A.traceCondition_2'}">每次撥款</th:block>
							</label><br>
							<label>
								<input name="traceCondition" type="radio" value="3"></input>
								<th:block th:text="#{'L140S12A.traceCondition_3'}">其他</th:block>
							</label>
						</td>
						<td class="hd1 td_traceProfiling">
							<th:block th:text="#{'L140S12A.traceProfiling'}">追蹤週期</th:block>
						</td>
						<td class="td_traceProfiling">
							<label>
								<input name="traceProfiling" type="radio" value="1"></input>
								<th:block th:text="#{'L140S12A.traceProfiling_1'}">一次</th:block>
							</label><br>
							<label>
								<input name="traceProfiling" type="radio" value="2"></input>
								<th:block th:text="#{'L140S12A.traceProfiling_2'}">週期: </th:block>
								<input type="text" name="traceProfilingMonth" id="traceProfilingMonth" class="numeric" positiveonly="false" integer="3" maxlength="3" size="3"></input>
								<th:block th:text="#{'L140S12A.traceProfiling_2_1'}">月</th:block>
							</label><br>
							<label>
								<input name="traceProfiling" type="radio" value="3"></input>
								<th:block th:text="#{'L140S12A.traceProfiling_3'}">核准日後6個月內</th:block>
							</label><br>
							<label>
								<input name="traceProfiling" type="radio" value="4"></input>
								<th:block th:text="#{'L140S12A.traceProfiling_4'}">核准日後6個月內第一次，其後每12個月一次(中長期適用)</th:block>
							</label>
						</td>
					</tr>
					<tr>
						<td colspan="5">
							<span class="color-red">
								<b>
									<span id="showDocStatus">
										<th:block th:text="#{'L140S12A.promptMsg_1'}">[請注意將同一次要追蹤的所有工作應寫在一同一追蹤內容。若簽報有拆分表述需要時，可於轉入主表後，再依需要重新編輯。]</th:block>
									</span>
								</b>
							</span>
						</td>
					</tr>
					<tr>
						<td class="hd1">
							<th:block th:text="#{'L140S12A.contentText'}">內容</th:block>
						</td>
						<td colspan="4">
							<textarea id="contentText" name="contentText" maxlengthc="500" rows="10" cols="90" class="nodisabled fullText"></textarea>
						</td>
					</tr>
				</table>
			<!--</form>-->
		</div>
	</th:block>
</body>
</html>
