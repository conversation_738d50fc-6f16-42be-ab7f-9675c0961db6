package com.mega.eloan.lms.fms.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.model.L140MM6A;
import com.mega.eloan.lms.model.L140MM6B;
import com.mega.eloan.lms.model.L140MM6C;
import com.mega.eloan.lms.model.L180R46A;

import tw.com.iisi.cap.exception.CapException;

/**
 * <pre>
 * 共同行銷維護作業
 * </pre>
 * 
 * @since 2019
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
public interface LMS7800Service extends AbstractService {

	boolean deleteL140mm6as(String[] oids);
	
	List<L140MM6C> findL140mm6csByMainId(String mainId);
	
	public Map<String, String> getData(L140MM6A l140mm6a, boolean newData) throws CapException;
	
	public L180R46A findL180r46aByCntrNoType(String cntrNo, String type);
	
	public L140MM6C findL140mm6c(String mainId, String type);
	
	public void deleteL140mm6bs(List<L140MM6B> l140mm6bs, boolean isAll);
	
	public void saveL140mm6bList(List<L140MM6B> list);
	
	public L140MM6B findL140mm6b(String mainId, String branchType, String branchId,
			String staffNo, String staffJob);
	
	public void flowAction(String mainOid, L140MM6A model,
			boolean setResult, boolean resultType, boolean upMis)
			throws Throwable;
	
	public String changeDateToString(Date datetime);
}
