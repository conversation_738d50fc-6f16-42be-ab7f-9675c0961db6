package com.mega.eloan.lms.fms.handler.grid;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.cxf.common.util.StringUtils;
import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.formatter.BranchNameFormatter;
import com.mega.eloan.common.formatter.UserNameFormatter;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.fms.pages.CLS2901V01Page;
import com.mega.eloan.lms.fms.service.CLS2901Service;
import com.mega.eloan.lms.model.C900M01J;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.formatter.IBeanFormatter;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

@Scope("request")
@Controller("cls2901gridhandler")
public class CLS2901GridHandler extends AbstractGridHandler {


	@Resource
	CLSService service;

	@Resource
	UserInfoService userInfoService;
	
	@Resource
	CodeTypeService codeTypeService;
	
	@Resource
	BranchService branchService;
	
	@Resource
	CLS2901Service cls2901Service;

	Properties prop = MessageBundleScriptCreator
			.getComponentResource(CLS2901V01Page.class);

	/**
	 * 查詢Grid 資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	public CapGridResult queryMain(ISearch pageSetting,
			PageParameters params) throws CapException {
		Map<String, String> lnflag_map = service.get_codeTypeWithOrder("lnFlag_extend_C250M01A_C900M01H");
		String docStatus = Util.nullToSpace(params.getString(EloanConstants.DOC_STATUS));
		String filetData = Util.trim(params.getString("filetData"));
		if (Util.isNotEmpty(filetData)) {
			//Filet查詢條件
			JSONObject jsoniletData = JSONObject.fromObject(filetData);
			String custId = Util.trim(jsoniletData.getString("custId"));
			String category = Util.trim(jsoniletData.getString("category"));
			
			if(Util.isNotEmpty(custId)){
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
			}
			if(Util.isNotEmpty(category)){
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "category", category);
			}
		}
		//========================
		FlowDocStatusEnum docStatusEnum = FlowDocStatusEnum.getEnum(docStatus);
		if (docStatusEnum != null) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, EloanConstants.DOC_STATUS, docStatus);
			String ownBrId = Util.trim(params.getString("ownBrId"));
			if(!CapString.isEmpty(ownBrId)){
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", ownBrId);
			}
		} else {
			//全行黑名單查詢(已核准+待解除)
			String[] _docStatus = docStatus
					.split(UtilConstants.Mark.SPILT_MARK);
			pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
					_docStatus);
		}
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");

		Page<? extends GenericBean> page =service.findPage(
				C900M01J.class, pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());

		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		if(true){
			dataReformatter.put("ownBrId", new BranchNameFormatter(branchService,
					BranchNameFormatter.ShowTypeEnum.ID_Name)); // 分行名稱格式化
			UserNameFormatter userNameFormatter = new UserNameFormatter(userInfoService, UserNameFormatter.ShowTypeEnum.Name);
			dataReformatter.put("updater", userNameFormatter); // 使用者名稱格式化	
			dataReformatter.put("dcUpdater", userNameFormatter); // 使用者名稱格式化
			dataReformatter.put("dcApprover", userNameFormatter); // 使用者名稱格式化
			
			dataReformatter.put("category", new C900M01J_category_formatter());
//			dataReformatter.put("lnflag", new CodeTypeFormatter(codeTypeService, "C250M01A_lnFlag"));
			//因CHAR(2), 要比對 trim 後的值
			dataReformatter.put("lnflag", new C900M01J_lnflag_formatter(lnflag_map));
		}
		result.setDataReformatter(dataReformatter);
		
		return result;
	}
	
	private class C900M01J_category_formatter implements IBeanFormatter {
		private static final long serialVersionUID = 1L;

		@SuppressWarnings("unchecked")
		public String reformat(Object in) {
			C900M01J meta = (C900M01J) in;

			String val = meta.getCategory();
			if(Util.equals("P", val)){				
				return prop.getProperty("C900M01J.category.P");
			}else if(Util.equals("B", val)){
				return prop.getProperty("C900M01J.category.B");
			}else if(Util.equals("I", val)){
				return prop.getProperty("C900M01J.category.I");			
			}else if(Util.equals("", val)){
				return EloanConstants.EMPTY_STRING;	
			}else{
				return val;
			}
			
		}
	}
	private class C900M01J_lnflag_formatter implements IBeanFormatter {
		private static final long serialVersionUID = 1L;
		private Map<String, String> kvmap;
		
		private C900M01J_lnflag_formatter(Map<String, String> pass_kvmap){
			this.kvmap = pass_kvmap;
		}
		
		@SuppressWarnings("unchecked")
		public String reformat(Object in) {
			C900M01J meta = (C900M01J) in;

			String val = Util.trim(meta.getLnflag());
			
			return LMSUtil.getDesc(kvmap, val);			
		}
	}

	@DomainAuth(value = AuthType.Query , CheckDocStatus = false)
	public CapMapGridResult importRealEstateAgentInfo(ISearch pageSetting, PageParameters params) throws CapException {
		
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		if(!StringUtils.isEmpty(custId) && !StringUtils.isEmpty(dupNo)){
			list = this.cls2901Service.getRealEstateAgentInfo(custId, dupNo);
		}
		
		Page<Map<String, Object>> page = LMSUtil.getMapGirdDataRow(list, pageSetting);
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}
}
