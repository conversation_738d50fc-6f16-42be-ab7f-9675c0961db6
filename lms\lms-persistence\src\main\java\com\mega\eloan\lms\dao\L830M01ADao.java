/* 
 * L830M01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L830M01A;

/** 帳戶管理員維護 **/
public interface L830M01ADao extends IGenericDao<L830M01A> {

	L830M01A findByOid(String oid);
	
	public List<L830M01A> findByBrno(String brNo, String creator);
	
	List<L830M01A> findByMainId(String mainId);
	
}