package com.mega.eloan.lms.fms.pages;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.html.EloanPageFragment;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;


/**
 * <pre>
 * 資料上傳作業 - 待覆核
 * </pre>
 * 
 * @since 2019
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Controller@RequestMapping(path = "/fms/lms8500v02")
public class LMS8500V02Page extends AbstractEloanInnerView {

	public LMS8500V02Page() {
		super();
	}

	@Override
	public void  execute(ModelMap model, PageParameters params) {
		setGridViewStatus(CreditDocStatusEnum.海外_待覆核);

		// 加上Button
		List<EloanPageFragment> btns = new ArrayList<>();
		// 主管跟經辦都會出現的按鈕
		btns.add(LmsButtonEnum.Filter);
		btns.add(LmsButtonEnum.View);
		
		addToButtonPanel(model, btns);
		// 加上Button
		
		renderJsI18N(LMS8500V01Page.class);
	}

	public String[] getJavascriptPath() {
		return new String[] { "pagejs/fms/LMS8500V02Page.js" };
	}
}
