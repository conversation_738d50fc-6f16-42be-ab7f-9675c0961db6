package com.mega.eloan.lms.crs.handler.form;

import java.sql.Timestamp;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.TreeMap;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import com.iisigroup.cap.component.PageParameters;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.DocLogEnum;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.crs.pages.LMS2401M01Page;
import com.mega.eloan.lms.crs.pages.LMS2430M01Page;
import com.mega.eloan.lms.mfaloan.bean.ELF491;
import com.mega.eloan.lms.mfaloan.service.MisELF491Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C241M01Z;
import com.mega.eloan.lms.model.C243M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

@Scope("request")
@Controller("lms2430m01formhandler")
@DomainClass(C243M01A.class)
public class LMS2430M01Formhandler extends AbstractFormHandler {
	private static final Logger logger = LoggerFactory.getLogger(LMS2430M01Formhandler.class);
	private static final int MAXLEN_C243M01A_CHGREASON = StrUtils.getEntityFileldLegth(C243M01A.class, "chgReason", 900);
		
	@Resource
	UserInfoService userInfoService;
	
	@Resource
	BranchService branchService;
		
	@Resource
	DocCheckService docCheckService;
	
	@Resource
	DocLogService docLogService;
	
	@Resource
	ICustomerService iCustomerService;
	
	@Resource
	TempDataService tempDataService;
	
	@Resource
	CLSService clsService;
	
	@Resource
	RetrialService retrialService;
	
	@Resource
	MisELF491Service misELF491Service;
	
	@Resource
	MisdbBASEService misdbBASEService;
	
	Properties prop_lms2430m01 = MessageBundleScriptCreator.getComponentResource(LMS2430M01Page.class);
	Properties prop_lms2401m01 = MessageBundleScriptCreator.getComponentResource(LMS2401M01Page.class);
	Properties prop_abstractEloanPage = MessageBundleScriptCreator.getComponentResource(AbstractEloanPage.class);
	
	@DomainAuth(value = AuthType.Query)
	public IResult query(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C243M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = retrialService.findC243M01A_oid(mainOid);
			
			if (meta != null) {
				String page = params.getString(EloanConstants.PAGE);
				
				if ("01".equals(page)) {
					// 設定基本欄位
					LMSUtil.addMetaToResult(result, meta, new String[] {
							"custId", "dupNo", "custName",
							"elfBranch", 
							"elfCrDate", "chgCrDate", "chgReason",
							"elfLrDate", "elfLastRealDt", "elfRemomo",
							"randomCode" });					
					
					// 處理分行名稱
					String elfBranchName = "";
					if (StringUtils.isNotBlank(meta.getElfBranch())) {
						IBranch ibranch = branchService.getBranch(meta.getElfBranch());
						if (ibranch != null) {
							elfBranchName = ibranch.getBrName();
						}
					}
					
					// 處理文件狀態名稱
					String docStatusName = "";
					if (meta.getDocStatus() != null) {
						docStatusName = prop_abstractEloanPage.getProperty("docStatus." + meta.getDocStatus());
					}
					
					/*
					 * 在 ELF491 的 nckdflag 是 CHAR(2), 但前端 select 可能只有CHAR(1)
					 */
					result.set("chgNckdFlag", Util.trim(meta.getChgNckdFlag()));
					
					result.set("elfBranch", Util.trim(meta.getElfBranch()));
					result.set("elfBranchName", elfBranchName);
					result.set("docStatusName", docStatusName);
					
					result.set("creator", _id_name(meta.getCreator()));
					result.set("createTime",
							meta.getCreateTime() != null ? Util.trim(TWNDate.valueOf(meta.getCreateTime())) : "");
					result.set("updater", _id_name(meta.getUpdater()));
					result.set("updateTime",
							meta.getUpdateTime() != null ? Util.trim(TWNDate.valueOf(meta.getUpdateTime())) : "");
					
					result.set("apprId", _id_name(meta.getUpdater()));
					result.set("approvercn", _id_name(meta.getApprover()));
					
				}
			} else {
				throw new CapMessageException("查詢不到指定的個金覆審控制檔資料", getClass());
			}
		} else {
			// mainOid 為空時，表示可能是新增或空白頁面，允許繼續處理
			logger.info("mainOid 參數為空，建立空白表單");
			// 不拋出異常，允許建立空白表單
		}

		return defaultResult(params, meta, result);
	}
	
	private CapAjaxFormResult defaultResult(PageParameters params, C243M01A meta,
			CapAjaxFormResult result) throws CapException {
		
		// required information
		String page = Util.trim(params.getString(EloanConstants.PAGE));
		if (Util.isEmpty(page)) {
			page = "01"; // 預設頁面
		}
		result.set(EloanConstants.PAGE, page);
		
		logger.info("defaultResult設定頁面參數: page={}", page);
		
		if (meta != null) {
			result.set(EloanConstants.MAIN_OID, Util.trim(meta.getOid()));
			result.set(EloanConstants.MAIN_DOC_STATUS, Util.trim(meta.getDocStatus()));
			result.set(EloanConstants.MAIN_ID, Util.trim(meta.getMainId()));		
			result.set("custInfo",
					Util.trim(meta.getCustId()) + "-" + Util.trim(meta.getDupNo())
							+ " " + Util.trim(meta.getCustName()));
			
			result.set("custId", Util.trim(meta.getCustId()));
			result.set("dupNo", Util.trim(meta.getDupNo()));
			result.set("custName", Util.trim(meta.getCustName()));
			
			logger.info("defaultResult設定文件資訊: mainOid={}, docStatus={}, custInfo={}", 
					meta.getOid(), meta.getDocStatus(), 
					Util.trim(meta.getCustId()) + "-" + Util.trim(meta.getDupNo()) + " " + Util.trim(meta.getCustName()));
		} else {
			// 當meta為null時，設定預設值
			result.set(EloanConstants.MAIN_OID, "");
			result.set(EloanConstants.MAIN_DOC_STATUS, "");
			result.set(EloanConstants.MAIN_ID, "");
			result.set("custInfo", "");
			result.set("custId", "");
			result.set("dupNo", "");
			result.set("custName", "");
			
			logger.info("defaultResult: meta為空，設定空白預設值");
		}
		
		return result;
	}
	
	/**
	 * 儲存
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(AuthType.Modify)
	public IResult saveMain(PageParameters params)
			throws CapException {
		return _saveAction(params, "N");
	}
	
	private CapAjaxFormResult _saveAction(PageParameters params,String tempSave)
	throws CapException{
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, tempSave);
		boolean allowIncomplete = Util.equals("Y", params.getString("allowIncomplete"));
		//===
		String KEY = "saveOkFlag";
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set(KEY, false);
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String chgReason = Util.trim(params.getString("chgReason"));
		C243M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			try{
				meta = retrialService.findC243M01A_oid(mainOid);
				
				String page = params.getString(EloanConstants.PAGE);
				if ("01".equals(page)) {
					CapBeanUtil.map2Bean(params, meta, new String[] {
							"chgCrDate", "chgNckdFlag"});
					meta.setChgReason(Util.truncateString(chgReason, MAXLEN_C243M01A_CHGREASON));
				}
				//---
				retrialService.save(meta);				
				// ===
				if (Util.notEquals("Y",
						SimpleContextHolder.get(EloanConstants.TEMPSAVE_RUN))) {
					// 在tempSave<>Y,若有未填欄位,丟 CapMessageException, 讓
					// saveOkFlag==false

					String msg = checkIncompleteMsg(meta, chgReason);
					if (Util.isNotEmpty(msg)) {
						if (allowIncomplete) {
							result.set("IncompleteMsg", msg);
						} else {
							throw new CapMessageException(msg, getClass());
						}
					}
				}
				result.set(KEY, true);	
			}catch(Exception e){
				logger.error(StrUtils.getStackTrace(e));
				throw new CapException(e, getClass());
			}		
		}
		
		result.add(query(params));
		
		return result;
	}
	
	private String checkIncompleteMsg(C243M01A c243m01a, String chgReason){
		if(true){
			if (Util.isEmpty(chgReason)) {
				return "請輸入"+prop_lms2430m01.getProperty("C243M01A.chgReason");
			}else{
				if (Util.notEquals(c243m01a.getChgReason(), chgReason)) {
					return prop_lms2430m01.getProperty("C243M01A.chgReason")+"長度超過限制";
				}				
			}
		}
		
		Date orgCrDate = c243m01a.getElfCrDate();
		Date crDate = c243m01a.getChgCrDate();
		Date sysDate = CapDate.getCurrentTimestamp();
		if(CrsUtil.isNull_or_ZeroDate(crDate)){
			if(Util.isEmpty(Util.trim(c243m01a.getChgNckdFlag()))){
				return "請輸入"+prop_lms2430m01.getProperty("C243M01A.chgNckdFlag");	
			}
			
			if(CrsUtil.notAllowedNckdFlag_R98(c243m01a.getChgNckdFlag()) && CrsUtil.parseRule(c243m01a.getElfRemomo()).keySet().contains(CrsUtil.R98)){
				List<Map<String, Object>> lnf0854Map = misdbBASEService.findLnfe0854UnClosedByCustIdAndBrNo(
						c243m01a.getCustId(), c243m01a.getDupNo(), c243m01a.getElfBranch());
				if (lnf0854Map != null && !lnf0854Map.isEmpty()) {
					// J-107-0192 異常通報案件，不可註記為不覆審
					return MessageFormat.format(prop_lms2401m01.getProperty("ui_lms2401.msg12"), c243m01a.getCustId()+ " " + Util.trim(c243m01a.getCustName()));
				}
			}
		}else{
			//目前的 crDate 有值
			
			//檢核與 「不覆審代碼」的關聯
			if(Util.isNotEmpty(Util.trim(c243m01a.getChgNckdFlag()))){
				return prop_lms2430m01.getProperty("C243M01A.chgCrDate")+"("+TWNDate.toAD(crDate)+")"
					+"請勿輸入"+prop_lms2430m01.getProperty("C243M01A.chgNckdFlag");	
			}
			
			if(CrsUtil.isNOT_null_and_NOTZeroDate(orgCrDate) && LMSUtil.cmpDate(crDate,"==", orgCrDate)){
				return prop_lms2430m01.getProperty("C243M01A.chgCrDate")+"("+TWNDate.toAD(crDate)+")未異動";
			}
			
			if(LMSUtil.cmpDate(crDate, "<", sysDate)){
				return prop_lms2430m01.getProperty("C243M01A.chgCrDate")+"("+TWNDate.toAD(crDate)+")"
					+" 不應小於系統日("+TWNDate.toAD(sysDate)+")";	
			}else{
				if(CrsUtil.isNull_or_ZeroDate(orgCrDate)){
					//原本不覆審，自行指定覆審日 --> ok
				}else{
					//不可超過6個月
					String deadLine = StringUtils.substring(TWNDate.toAD(CapDate.addMonth(orgCrDate, 6))
							, 0, 7)+"-01";
					if(LMSUtil.cmpDate(crDate, ">=", CapDate.parseDate(deadLine))){
						return prop_lms2430m01.getProperty("C243M01A.chgCrDate")+"("+TWNDate.toAD(crDate)+")"
							+"需在" + CapDate.formatDate(CapDate.shiftDays(CapDate.parseDate(deadLine), -1), "yyyy-MM") 
							+"以前";
					} 
				}
			}
		}
		return "";
	}
	
	private String check_c243m01a_elf491(C243M01A c243m01a, ELF491 elf491){
		String branch = c243m01a.getElfBranch();
		String custId = c243m01a.getCustId();
		String dupNo = c243m01a.getDupNo();
		
		if(elf491==null){
			return "中心覆審控制檔"+branch+"-"+ custId+"-"+dupNo+"不存在";
		}
		String date_a  = CrsUtil.isNull_or_ZeroDate(elf491.getElf491_crdate())?"":TWNDate.toAD(elf491.getElf491_crdate());
		String date_b  = CrsUtil.isNull_or_ZeroDate(c243m01a.getElfCrDate())?"":TWNDate.toAD(c243m01a.getElfCrDate());
		if(Util.notEquals(date_a, date_b)){
			return "編製時，中心覆審控制檔"+branch+"-"+ custId+"-"+dupNo
				+"之「應覆審日」為 "+date_b+"，但已被異動為 "+date_a;
		}		
		return "";
	}
	
	@DomainAuth(AuthType.Modify)
	public IResult newC243M01A(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Timestamp nowTS = CapDate.getCurrentTimestamp();
		
		String branch = Util.trim(params.getString("branch"));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		
		// 驗證輸入參數
		if(Util.isEmpty(branch)){
			throw new CapMessageException("分行代碼不可為空", getClass());
		}
		if(Util.isEmpty(custId)){
			throw new CapMessageException("客戶統編不可為空", getClass());
		}
		if(Util.isEmpty(dupNo)){
			dupNo = "0"; // 預設值
		}
		
		ELF491 elf491 = null;
		try {
			elf491 = misELF491Service.findByPk(branch, custId, dupNo);
		} catch (Exception e) {
			
			// 根據錯誤類型提供不同的錯誤訊息
			String errorMsg = e.getMessage();
			if (errorMsg != null && errorMsg.contains("Connection reset")) {
				throw new CapMessageException("與外部系統(MFALOAN)連線中斷，請稍後再試", getClass());
			} else if (errorMsg != null && errorMsg.contains("SQLSTATE=08001")) {
				throw new CapMessageException("資料庫連線異常，請聯絡系統管理員", getClass());
			} else {
				throw new CapMessageException("查詢中心覆審控制檔時發生系統錯誤，請稍後再試", getClass());
			}
		}
		
		if(elf491==null){
			throw new CapMessageException("中心覆審控制檔"+branch+"-"+ custId+"-"+dupNo+"不存在", getClass());
		}
		
		String custName = "";
		Map<String, Object> latest0024Data = iCustomerService.findByIdDupNo(custId, dupNo);
		if(latest0024Data != null) {
			custName = Util.trim(MapUtils.getString(latest0024Data, "CNAME"));
			if(Util.isEmpty(custName)){
				throw new CapMessageException("客戶姓名資料不完整，無法建立覆審記錄", getClass());
			}
		} else {
			throw new CapMessageException("客戶資料不存在，無法建立覆審記錄", getClass());
		}

		try {
			logger.info("開始建立C243M01A記錄");
			C243M01A c243m01a = new C243M01A();
			c243m01a.setMainId(IDGenerator.getUUID());
			c243m01a.setCustId(custId);
			c243m01a.setDupNo(dupNo);
			c243m01a.setCustName(custName);
			c243m01a.setUnitType(user.getUnitType());
			c243m01a.setOwnBrId(user.getUnitNo());		
			c243m01a.setDocStatus(RetrialDocStatusEnum.編製中.getCode());		
			c243m01a.setCreator(user.getUserId());
			c243m01a.setCreateTime(nowTS);
			c243m01a.setUpdater(user.getUserId());
			c243m01a.setUpdateTime(nowTS);
			c243m01a.setElfBranch(branch);
			c243m01a.setElfCrDate(elf491.getElf491_crdate());
			c243m01a.setChgCrDate(elf491.getElf491_crdate());
			c243m01a.setChgReason("");
			c243m01a.setChgNckdFlag("");
			c243m01a.setElfLrDate(elf491.getElf491_lrdate());
			c243m01a.setElfLastRealDt(elf491.getElf491_lastRealDt());
			c243m01a.setElfRemomo(Util.trim(elf491.getElf491_remomo()));
			c243m01a.setElfNckdFlag(Util.trim(elf491.getElf491_nckdflag()));
			c243m01a.setElfNckdDate(elf491.getElf491_nckddate());
			c243m01a.setElfNckdMemo(Util.trim(elf491.getElf491_nckdmemo()));
			
			CapAjaxFormResult successResult = defaultResult(params, c243m01a, result);
			
			// 新增狀態標示，讓前端知道客戶姓名是否查詢成功
			successResult.set("custNameStatus", Util.isEmpty(custName) ? "empty" : "ok");
			successResult.set("custName", custName);
			
			return successResult;
		} catch (Exception e) {
			throw new CapMessageException("儲存資料時發生系統錯誤，請稍後再試", getClass());
		}
	}
	
	@DomainAuth(AuthType.Modify)
	public IResult delC243M01A(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Timestamp nowTS = CapDate.getCurrentTimestamp();
		
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C243M01A c243m01a = retrialService.findC243M01A_oid(mainOid);
		if(c243m01a!=null){
			c243m01a.setUpdater(user.getUserId());
			c243m01a.setDeletedTime(nowTS);
			retrialService.save(c243m01a);
		}		
		return result;
	}

	/**
	 * 呈主管覆核
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(AuthType.Modify + AuthType.Accept)
	public IResult flowAction(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");

		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String decisionExpr = Util.trim(params.getString("decisionExpr"));
		
		C243M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = retrialService.findC243M01A_oid(mainOid);
			
			String errMsg = "";

			String docStatus = Util.trim(meta.getDocStatus());
			String branch = meta.getElfBranch();
			String custId = meta.getCustId();
			String dupNo = meta.getDupNo();
			ELF491 elf491 = misELF491Service.findByPk(branch , custId, dupNo);
			
			String nextStatus = "";
			DocLogEnum _DocLogEnum = null;
			if(Util.equals(RetrialDocStatusEnum.編製中.getCode(), docStatus)){
				if(Util.isEmpty(errMsg)){
					errMsg = check_c243m01a_elf491(meta, elf491);
				}
				
				nextStatus = RetrialDocStatusEnum.待覆核.getCode();
				_DocLogEnum = DocLogEnum.FORWARD;
			}else if(Util.equals(RetrialDocStatusEnum.待覆核.getCode(), docStatus)){
				//核定、退回
				if(Util.equals("核定", decisionExpr)){	
					if(Util.isEmpty(errMsg)){
						errMsg = check_c243m01a_elf491(meta, elf491);
					}
					//===============
					if(Util.isEmpty(errMsg)){
						if(Util.equals(user.getUserId(), meta.getUpdater())){
							errMsg = RespMsgHelper.getMessage("EFD0053");
						}else{
							nextStatus = RetrialDocStatusEnum.已核准.getCode();
							_DocLogEnum = DocLogEnum.ACCEPT;	
						}
					}
					
				}else if(Util.equals("退回", decisionExpr)){
					nextStatus = RetrialDocStatusEnum.編製中.getCode();
					_DocLogEnum = DocLogEnum.BACK;
				}
			}else if(Util.equals(RetrialDocStatusEnum.已核准.getCode(), docStatus)){	

			}
			
						
			if(Util.isNotEmpty(errMsg)){				
				throw new CapMessageException(errMsg, getClass());
			}else{
				if(Util.isEmpty(nextStatus)){
					throw new CapMessageException("流程異常["+docStatus+"]", getClass());
				}	
			}
			if(true){
				
				List<ELF491> elf491_list = new ArrayList<ELF491>();
				List<C241M01Z> c241m01z_list = new ArrayList<C241M01Z>();
				
				if(Util.equals(nextStatus, RetrialDocStatusEnum.已核准.getCode())){
					retrialService.up491_at_c243m01aFinish(elf491_list, c241m01z_list, elf491, meta, user.getUserId());
				}else if(Util.equals(nextStatus, RetrialDocStatusEnum.編製中.getCode())){
					meta.setApprover(null);
					meta.setApproveTime(null);
				}else if(Util.equals(nextStatus, RetrialDocStatusEnum.待覆核.getCode())){
					meta.setApprover(null);
					meta.setApproveTime(null);
				}
				meta.setDocStatus(nextStatus);
				//用 daoSave 避免把 approver 寫到 updater
				clsService.daoSave(meta);
				
				retrialService.upELF491_DelThenInsert(elf491_list);
				retrialService.saveC241M01Z(c241m01z_list);

				if(_DocLogEnum!=null){
					docLogService.record(meta.getOid(), _DocLogEnum);	
				}				
			}			
			tempDataService.deleteByMainId(meta.getMainId());
			docCheckService.unlockDocByMainIdUser(meta.getMainId(), user.getUserId());
		}
		return defaultResult( params, meta, result);
	}
	
	private String _id_name(String raw_id) {
		String id = Util.trim(raw_id);
		return Util.trim(id + " "
					+ Util.trim(userInfoService.getUserName(id)));
	}
	
	public IResult queryBranch(PageParameters params)
	throws CapException {
	
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		TreeMap<String, String> tm = retrialService.getBranch(user.getUnitNo());
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set("item", new CapAjaxFormResult(tm));
		result.set("itemOrder", new ArrayList<String>(tm.keySet()));
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult load_nckdFlag(PageParameters params)
	throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();		
		
		Map<String, String> tm = retrialService.get_crs_NckdFlagMapActive();
		
		result.set("item", new CapAjaxFormResult(tm));
		result.set("itemOrder", new ArrayList<String>(tm.keySet()));
		return result;
	}
}