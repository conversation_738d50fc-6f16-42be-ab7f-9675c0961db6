$(function(){
	$(".forC2415Use").empty();
    $("#gridview99").iGrid({
        handler: 'lms2405gridhandler',
        height: 350,
        sortname: 'retrialYN|projectNo|custId',
        sortorder : 'desc|asc|asc',
        postData: {
            mainId: responseJSON.mainId,
            formAction: "queryList"
        },
        multiselect: true,
        colModel: [{
            colHeader:i18n.lms2405m01['C241M01A.projectNo'],//"序號",
            name: 'projectNo',
            width: 30,
            sortable: true,
            align: "center"
        }, {
            colHeader: i18n.lms2405m01['C241M01A.retrialYN'],//"需覆審",
            name: 'retrialYN',
            width: 40,
            sortable: true,
            align: "center"
        }, {
            colHeader: i18n.lms2405m01['C241M01A.custId'],//"客戶統編",
            name: 'custId',
            align: "center",
            width: 90,
            sortable: true,
            formatter: 'click',
            onclick: openDocDe
        }, {
            colHeader: i18n.lms2405m01['C241M01A.custName'],//"客戶姓名",
            name: 'custName',
            align: "center",
            width: 80
        }, {
            colHeader: i18n.lms2405m01['C241M01A.quotaAmt'],//"額度",
            name: 'totQuota',
            formatter : 'currency', 
			formatoptions : {
				thousandsSeparator: ",",
				decimalPlaces: 2,//小數點到第幾位
				prefix: "", //前綴字
				suffix:"" //後綴字
			},
            align: "right",
            width: 180
        }, {
            colHeader: i18n.lms2405m01['C241M01A.lastRetrialDate'],//"上次覆審日期",
            name: 'lastRetrialDate',
            align: "center",
            width: 120
        }, {
            colHeader: i18n.lms2405m01['C241M01A.shouldReviewDate'],//"最終應覆審日期",
            name: 'shouldReviewDate',
            align: "center",
            width: 120
        }, {
            colHeader: i18n.lms2405m01['C241M01A.retrialKind'],//"規則",
            name: 'retrialKind',
            align: "center",
            width: 50
        }, {
            colHeader: i18n.lms2405m01['C241M01A.docStatus'],//"覆審流程點",
            name: 'docStatus',
            align: "center",
            width: 125
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }, {
            colHeader: "mainId",
            name: 'mainId',
            hidden: true
        }, {
            colHeader: "dupNo",
            name: 'dupNo',
            hidden: true
        }],
        ondblClickRow: function(rowid){
        	openDocDe(null, null, $("#gridview99").getRowData(rowid));
        }
    });
    function openDocDe(cellvalue, options, rowObject){
        ilog.debug(rowObject);
        $("#C2415M01bGrid").jqGrid("setGridParam", {
    		postData : {
    			formAction: "queryC241m01b",
    			mainId: rowObject.mainId
    		},
			page : 1,
			//gridPage : 1,
    		search: true
    	}).trigger("reloadGrid");
        // 設定查詢的覆審報告MAINID
        $("#lms2415s05gridMainId").val(rowObject.mainId);
        // 重新查詢
        $("#lms2415s05grid").jqGrid("setGridParam", {
    		postData : {
    			formAction: "queryDocFile",
    			mainId: rowObject.mainId
    		},
			page : 1,
			//gridPage : 1,
    		search: true
    	}).trigger("reloadGrid");
        detailBox(rowObject);
    };
    
    if ($("#NCKDFLAG")) {
        var obj = CommonAPI.loadCombos("lms2405m01_NckdFlag");
        var NCKDFLAG = $("#NCKDFLAG");
        NCKDFLAG.setItems({
            item: obj.lms2405m01_NckdFlag,
            format: "{value} {key}",
            size: 1
        });
    }
    
//    $("#detail").find("input").attr("disabled", true);
//    $("#detail").find("select").attr("disabled", true);
    $("#newdetail").click(function(){
        $("#newCust").thickbox({
            //新增客戶
            title: i18n.lms2405m01['C241M01A.custId'],
            width: 250,
            height: 150,
            align: "center",
            valign: "bottom",
            modal: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if ($("#newCustId").valid()) {
                        $.ajax({
                            type: "POST",
                            handler: "lms2405m01formhandler",
                            data: {
                                formAction: "produceNew",
                                oid: responseJSON.mainOid,
                                custId: $("#newCustId").find("#custId").val(),
                                dupNo : $("#newCustId").find("#dupNo").val()
                            }
                            }).done(function(json){
								$("#reQuantity").val(json.reQuantity);
                                $("#gridview99").trigger("reloadGrid");
                                CommonAPI.triggerOpener("gridview", "reloadGrid");
                        });
                        $.thickbox.close();
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    });
    //刪除(註記)不覆審客戶
    $("#noCTL").click(function(){
        var rows = $("#gridview99").getGridParam('selarrrow');
        var list = new Array();
        for (var i = 0; i < rows.length; i++) {
            if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0) {
                var data = $("#gridview99").getRowData(rows[i]);
                if (data.retrialYN == "N") {
                    CommonAPI.showMessage(i18n.lms2405m01["retrial"]);
                    return;
                }
                list[i] = data.oid;
            }
        }
        if (list == "") {
            CommonAPI.showMessage(i18n.def["action_002"]);
            return;
        }
        $("#nckdflagForm").reset();
        $("#openbox3").thickbox({
            //不覆審選項
            title: i18n.lms2405m01['chooseNckdflag'],
            width: 550,
            height: 400,
            align: "center",
            valign: "bottom",
            modal: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if (($("[name=NCKDFLAG]:checked").val() ? false : true)) {
                        CommonAPI.showMessage(i18n.lms2405m01["chooseNckdflag"]);
                        return;
                    }
                    $.ajax({
                        type: "POST",
                        handler: "lms2405m01formhandler",
                        data: {
                            formAction: "saveNoCTL",
                            oid: list,
                            reason: $("[name=NCKDFLAG]:checked").parent().html()
                        }
                        }).done(function(json){
							$("#reQuantity").val(json.reQuantity);
                            $("#gridview99").trigger("reloadGrid");
                            CommonAPI.triggerOpener("gridview", "reloadGrid");
                    });
                    $.thickbox.close();
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    });
    $("#reCTL").click(function(){
        //恢復覆審
        var rows = $("#gridview99").getGridParam('selarrrow');
        var list = new Array();
        for (var i = 0; i < rows.length; i++) {
            if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0) {
                var data = $("#gridview99").getRowData(rows[i]);
                if (data.retrialYN == "Y") {
                    CommonAPI.showMessage(i18n.lms2405m01["noRetrial"]);
                    return;
                }
                list[i] = data.oid;
            }        	
        }
        if (list == "") {
            CommonAPI.showMessage(i18n.def["action_005"]);
            return;
        }
        $.ajax({
            type: "POST",
            handler: "lms2405m01formhandler",
            data: {
                formAction: "saveReCTL",
                oid: list
            }
            }).done(function(json){
            	$("#reQuantity").val(json.reQuantity);
                $("#gridview99").trigger("reloadGrid");
                CommonAPI.triggerOpener("gridview", "reloadGrid");
        });
    });
    
    
    $("#number").click(function(){
        //給號
        $.ajax({
            type: "POST",
            handler: "lms2405m01formhandler",
            data: {
                formAction: "number",
                oid: responseJSON.mainOid
            }
            }).done(function(){
                CommonAPI.triggerOpener("gridview", "reloadGrid");
                $("#gridview99").trigger("reloadGrid");
        });
    });
    //產生資料
    $("#produceInfo").click(function(){
        if ($("#C240M01AForm").valid()) {
            $.ajax({
                type: "POST",
                handler: "lms2405m01formhandler",
                data: {
                    formAction: "produceList",
                    mainOid: responseJSON.mainOid,
                    thisSamplingRate : $("#thisSamplingRate").val()
                }
                }).done(function(responseData){
                    $("#C240M01AForm").injectData(responseData);
                    CommonAPI.triggerOpener("gridview", "reloadGrid");
                    $("#gridview99").trigger("reloadGrid");
            });
        }
    });
    
    $("#excelFile").click(function(){
        $.capFileDownload({
            //handler: "simplefiledwnhandler",
            data: {
                fileOid: $("#excelFile").val()
            }
        });
    })
    
    function download(mod){
        var name = "";
        switch (mod) {
            case '0':
                name = "excelFile";
                break;
            case '1':
                name = "branchFile";
                break;
            case '2':
                name = "chkExcelFile";
                break;
                
        }
        $.capFileDownload({
            //handler: "simplefiledwnhandler",
            data: {
                fileOid: "$(#" + name + ")".val()
            }
        });
    }
    
    //列印
    $("#printS02").click(function(){
        var count = 0;
        var content = "";
		var rp = "R01";
        var id = $("#gridview99").getGridParam('selarrrow');
		var mainDataId = "";
        for (var i = 0; i < id.length; i++) {
            if (id[i] != "") {
                var datas = $("#gridview99").getRowData(id[i]);
            	if(content.length > 0){
            		content = content + "|";
            	}
                content = content + rp + "^" + datas.oid + "^" + datas.mainId;
                count++;
            }//if
        }
        if (count == 0) {
            CommonAPI.showMessage(i18n.def['grid.selrow']);
        }else{
        	$.form.submit({
                url: "../../simple/FileProcessingService",
                target: "_blank",
                data: {
                    rptOid: content,
                    fileDownloadName: "lms2405r01.pdf",
                    serviceName: "lms2405r01rptservice"
                }//data
            });//form
        }
    });
});
function detailBox(rowObject){
	$("#detail").find(".reset").html('');
    $.ajax({
        type: "POST",
        handler: "lms2405m01formhandler",
        data: {
            formAction: "queryL241m01a",
            oid: rowObject.oid
        }
        }).done(function(responseData){
            $("#L241M01aForm").injectData(responseData);
            $("#detailOid").val(responseData.oid);
            $("#detailMainId").val(responseData.mainId);
            $("#detailCustId").val(responseData.custId);
            $("#detailDupNo").val(responseData.dupNo);
            $("#detailTab1").trigger("click");
            $("#detail").thickbox({
                //明細
                title: i18n.lms2405m01['reportInfo'],
                width: 950,
                height: 500,
                modal: true,
                i18n: i18n.lms2405m01,
                buttons: {
//                    "button.Update491": function(){
//                        // 更新覆審控制檔 
//                        $.ajax({
//                            handler: "lms2415m01formhandler",
//                            type: "POST",
//                            dataType: "json",
//                            data: {
//                                formAction: "updateElf491",
//                                detailOid: $("#detailOid").val()
//                            },
//                            success: function(responseData){
//                                if (responseData.st) {
//                                    CommonAPI.showMessage(i18n.def["runSuccess"]);
//                                }
//                                else {
//                                    CommonAPI.showErrorMessage(i18n.lms2405m01["err.update491"]);
//                                }
//                            }
//                        });
//                    },
                    "close": function(){
                    	thickboxOptions.readOnly = thickBoxRead;
                    	$("#gridview99").trigger("reloadGrid");
                    	CommonAPI.triggerOpener("gridview", "reloadGrid");
                        $.thickbox.close();
                    }
                }
            });
            thickboxOptions.readOnly = false;
    });
}
var thickBoxRead = thickboxOptions.readOnly;