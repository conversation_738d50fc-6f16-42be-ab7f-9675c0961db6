package com.mega.eloan.lms.cls.handler.form;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.RPAProcessService;
import com.mega.eloan.lms.base.service.impl.RPAProcessServiceImpl;
import com.mega.eloan.lms.cls.pages.CLS3701M01Page;
import com.mega.eloan.lms.cls.service.CLS3701Service;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.model.C126M01A;
import com.mega.eloan.lms.model.C126M01B;
import com.mega.eloan.lms.model.C126S01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapString;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.iisi.cap.utils.CapWebUtil;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 房仲引薦案件
 * </pre>
 *
 */
@Scope("request")
@Controller("cls3701m01formhandler")
public class CLS3701M01FormHandler extends AbstractFormHandler {

    @Resource
    BranchService branchService;

    @Resource
    UserInfoService userInfoService;

    @Resource
    LMSService lmsService;

    @Resource
    CLSService clsService;

    @Resource
	CodeTypeService codetypeservice;
    
    @Resource
    EloandbBASEService eloandbService;

    @Resource
    CLS3701Service cls3701Service;
    
    @Resource
    RPAProcessService rpaProcessService;

    MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
    Properties prop = MessageBundleScriptCreator.getComponentResource(CLS3701M01Page.class);

	public IResult echo_custId(PageParameters params) throws CapException {
		// 儲存and檢核
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		result.set("custId", Util.trim(params.getString("custId")));
		result.set("dupNo", Util.trim(params.getString("dupNo")));
		result.set("custName", Util.trim(params.getString("custName")));
		return result;
	}
    
    @DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult newC126M01A(PageParameters params) throws CapException {
    	String custId=Util.trim(params.getString("custId", null));
    	if(Util.isNotEmpty(custId)){
			C126M01A c126m01a = new C126M01A();
	        c126m01a.setDocStatus(CreditDocStatusEnum.海外_編製中.getCode());
	        c126m01a.setOwnBrId(user.getUnitNo());
	        c126m01a.setMainId(IDGenerator.getUUID());
			c126m01a.setDocURL(CapWebUtil.getDocUrl(CLS3701M01Page.class));
	        String txCode = Util.trim(params.getString(EloanConstants.TRANSACTION_CODE));
	        c126m01a.setTxCode(txCode);
	        c126m01a.setCustId(Util.trim(params.getString("custId", null)));
	        c126m01a.setDupNo(Util.trim(params.getString("dupNo", null)));
	        c126m01a.setCustName(Util.trim(params.getString("custName", null)));
	        c126m01a.setIsFinished(UtilConstants.DEFAULT.否);
	        c126m01a.setIsClosed(UtilConstants.DEFAULT.否);
	
	        cls3701Service.save(c126m01a);
	
	        CapAjaxFormResult result = new CapAjaxFormResult();
	        result.set(EloanConstants.OID, c126m01a.getOid());
	        result.set(EloanConstants.MAIN_ID, c126m01a.getMainId());
	        result.set(EloanConstants.MAIN_OID, c126m01a.getOid());
	        result.set(EloanConstants.MAIN_DOC_STATUS, c126m01a.getDocStatus());
	        return result;
    	}
       return null;
    }
    
    @DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteC126M01A(PageParameters params) throws CapException {
        CapAjaxFormResult result = new CapAjaxFormResult();
        String[] oids = params.getStringArray("oids");
        StringBuffer sb = new StringBuffer();
        if (oids.length > 0) {
        	for (int i = 0, size = oids.length; i < size; i++) {
    			C126M01A c126m01a = (C126M01A) cls3701Service.findModelByOid(C126M01A.class, oids[i]);
    			List<C126M01B> c126m01bs=(List<C126M01B>)cls3701Service.findListByMainId(C126M01B.class, c126m01a.getMainId());
    			if(c126m01bs!=null && c126m01bs.size()>0){
    				sb.append(c126m01a.getCustId()+",");
    			}
    		}
        	String errMsg = sb.toString();
			if(Util.isEmpty(errMsg)){
				if (cls3701Service.deleteC126m01as(oids)) {
	                result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
							.getMainMessage(UtilConstants.AJAX_RSP_MSG.刪除成功));
	            }
			}
			else{
				throw new CapMessageException(errMsg.substring(0, errMsg.length()-1) + "等人已引進簽報書，不可刪除", getClass());
			}
        }
        return result;
    }
    
    @DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult tempSave(PageParameters params) throws CapException {
		return _saveAction(params, "Y");
    }

    @DomainAuth(AuthType.Modify)
	public IResult saveMain(PageParameters params) throws CapException {
		return _saveAction(params, "N");
    }

	private CapAjaxFormResult _saveAction(PageParameters params,
			String tempSave) throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, tempSave);
		Boolean showMsg = params.getAsBoolean("showMsg", false);
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C126M01A c126m01a =cls3701Service.findModelByOid(C126M01A.class, mainOid);
		if (Util.isNotEmpty(mainOid)) {
		    try{
		    	//J-109-0304_10702_B1004 Web e-Loan消金檢核地號是否符合整批貸款基地地號
		    	StringBuffer alertMsg = new StringBuffer();
		        Date receiveDate=Util.parseDate(params.getString("receiveDate"));
	        	Date contractDate=Util.parseDate(params.getString("contractDate"));
	        	BigDecimal ttlAmt=Util.parseToBigDecimal(params.getString("ttlAmt"));
	        	BigDecimal preloanAmt=Util.parseToBigDecimal(params.getString("preloanAmt"));
	        	
	        	String agntNo=params.getString("agntNo");
	        	String agntChain=params.getString("agntChain");
	        	String contractNo=params.getString("contractNo");
	        	
	        	//檢核收件日期不能比合約日期早
		        if(contractDate.compareTo(receiveDate) > 0){
		        	alertMsg.append(prop.getProperty("C126M01A.msg01"));
	        		if(Util.isNotEmpty(alertMsg)){
	        			alertMsg.append("<br>");
	        		}
	        	}
		        //檢核預貸金額不可比成交金額高
	        	if(preloanAmt.compareTo(ttlAmt)>0){
	        		alertMsg.append(prop.getProperty("C126M01A.msg02"));
	        		if(Util.isNotEmpty(alertMsg)){
	        			alertMsg.append("<br>");
	        		}
	        	}
	        	//檢核同一分行不可建同一房仲案件
	        	List<C126M01A> duplicateAgntCase=cls3701Service.findDuplicateAgntCase(c126m01a.getMainId(), c126m01a.getCustId(),c126m01a.getDupNo(),agntNo,agntChain,contractNo,c126m01a.getOwnBrId());
	        	if(duplicateAgntCase!=null && duplicateAgntCase.size()>0){
	        		alertMsg.append(prop.getProperty("C126M01A.msg04"));
	        		if(Util.isNotEmpty(alertMsg)){
	        			alertMsg.append("<br>");
	        		}
	        	}

		        if (Util.notEquals("Y",
		                SimpleContextHolder.get(EloanConstants.TEMPSAVE_RUN))) {
		        	if (Util.isEmpty(alertMsg)) {
				        CapBeanUtil.map2Bean(params, c126m01a);
				
				        String tips = this.checkData(c126m01a);
				        //檢查其餘非必填欄位，填寫完畢壓Flag
						if(Util.isEmpty(tips)){
							c126m01a.setIsFinished(UtilConstants.DEFAULT.是);
				        }
						else{
							tips = "尚有以下欄位待輸入：<br>" + tips + "<br>";
							c126m01a.setIsFinished(UtilConstants.DEFAULT.否);
						}
						//每次儲存，清掉覆核人，需重新覆核
						if(Util.isNotEmpty(c126m01a.getApprover())){
							c126m01a.setApprover(null);
						}
						if(Util.isNotEmpty(c126m01a.getApproveTime())){
							c126m01a.setApproveTime(null);
						}
						c126m01a.setDocStatus(CreditDocStatusEnum.海外_編製中.getCode());
						
						//檢核不同分行是否有重複鍵檔，有則跳出提醒
						List<C126M01A> duplicateAgntCase2=cls3701Service.findDuplicateAgntCase(c126m01a.getMainId(), c126m01a.getCustId(),c126m01a.getDupNo(),c126m01a.getAgntNo(),c126m01a.getAgntChain(),c126m01a.getContractNo(),"");
			        	if(duplicateAgntCase2!=null && duplicateAgntCase2.size()>0){
			        		String ownBrName = branchService.getBranchName(duplicateAgntCase2.get(0).getOwnBrId());
			        		tips += MessageFormat.format(
			        				prop.getProperty("C126M01A.msg03"),
									Util.trim(ownBrName));
			        	}
			        	
		        		cls3701Service.save(c126m01a);
		        		result = DataParse.toResult(c126m01a);
	                	result.set("tip", tips);
						result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
								RespMsgHelper.getMainMessage(
										UtilConstants.AJAX_RSP_MSG.儲存成功));
	                }else{
	                    //throw new CapMessageException(alertMsg.toString(), getClass());
	                	result.set("msg", alertMsg.toString());
	                }
		        }
		    }catch(Exception e){
		        logger.error(StrUtils.getStackTrace(e));
		        throw new CapException(e, getClass());
		    }
		}
		return defaultResult( params, c126m01a, result);
	}
    private String checkData(C126M01A c126m01a){
    	StringBuffer sb = new StringBuffer();
		Locale locale = LocaleContextHolder.getLocale();
		if (locale == null)
			locale = Locale.getDefault();
    	
        if(c126m01a!=null){
        	Map<String, CapAjaxFormResult> codeTypes = codetypeservice
			.findByCodeType(new String[] {"HaveNo","YesNo"});
        	CodeType counties = codetypeservice.findByCodeTypeAndCodeValue(
					"counties", Util.trim(c126m01a.getCity()));
			CodeType counties2 = codetypeservice.findByCodeTypeAndCodeValue(
					"counties" + Util.trim(c126m01a.getCity()),
					Util.trim(c126m01a.getDist()));
        	String address = (counties == null ? "" : counties.getCodeDesc()) +
			(counties2 == null ? "" : counties2.getCodeDesc()) +
			Util.trim(c126m01a.getVillageName()) +
			(Util.isEmpty(Util.trim(c126m01a.getVillageName())) ? "" :
					this.showPic(Util.trim(c126m01a.getVillage()), "5", codeTypes, locale)) +
			Util.trim(c126m01a.getNeighborhood()) +
			(Util.isEmpty(Util.trim(c126m01a.getNeighborhood())) ? "" : "鄰") +
			Util.trim(c126m01a.getRoadName()) + this.showPic(
					Util.trim(c126m01a.getRoad()), "6", codeTypes, locale) +
			Util.trim(c126m01a.getSec()) +
			(Util.isEmpty(Util.trim(c126m01a.getSec())) ? "" : "段") +
			Util.trim(c126m01a.getLane()) +
			(Util.isEmpty(Util.trim(c126m01a.getLane())) ? "" : "巷") +
			Util.trim(c126m01a.getAlley()) +
			(Util.isEmpty(Util.trim(c126m01a.getAlley())) ? "" : "弄") +
			Util.trim(c126m01a.getNo1()) +
			((Util.isEmpty(Util.trim(c126m01a.getNo1())) &&
					Util.isEmpty(Util.trim(c126m01a.getNo2()))) ? "" :
					(Util.isEmpty(Util.trim(c126m01a.getNo2())) ? "號" : "之")) +
            Util.trim(c126m01a.getNo2()) +
			(Util.isEmpty(Util.trim(c126m01a.getNo2())) ? "" : "號") +
			Util.trim(c126m01a.getFloor1()) +
			(Util.isEmpty(Util.trim(c126m01a.getFloor1())) ? "" : "樓") +
			(Util.isEmpty(Util.trim(c126m01a.getFloor2())) ? "" : "之") +
			Util.trim(c126m01a.getFloor2()) +
			(Util.isEmpty(Util.trim(c126m01a.getRoom())) ? "" : "(") +
			Util.trim(c126m01a.getRoom()) +
			(Util.isEmpty(Util.trim(c126m01a.getRoom())) ? "" : "室)");
        	
        	String agntNo=c126m01a.getAgntNo();
        	String agntChain=c126m01a.getAgntChain();
        	Date receiveDate=c126m01a.getReceiveDate();
        	Date contractDate=c126m01a.getContractDate();
        	String contractNo=c126m01a.getContractNo();
        	BigDecimal ttlAmt=c126m01a.getTtlAmt();
        	BigDecimal preloanAmt=c126m01a.getPreloanAmt();
        	BigDecimal applyAmt=c126m01a.getApplyAmt();
        	BigDecimal lnAmt=c126m01a.getLnAmt();
        	Date estDate=c126m01a.getEstDate();
        	Date creditReviewDate=c126m01a.getCreditReviewDate();
        	Date lnDate=c126m01a.getLnDate();
        	String essayName=c126m01a.getEssayName();
        	String statFlag=c126m01a.getStatFlag();
        	String rateKind=c126m01a.getRateKind();
        	BigDecimal rebateRatio=c126m01a.getRebateRatio();
        	BigDecimal rebate=c126m01a.getRebate();
        	if(Util.isEmpty(address)){
        		sb.append(prop.getProperty("C126M01A.address"));
        		if(Util.isNotEmpty(sb)){
        			sb.append("<br>");
        		}
        	}
        	if(Util.isEmpty(agntNo)){
        		sb.append(prop.getProperty("C126M01A.agntNo"));
        		if(Util.isNotEmpty(sb)){
        			sb.append("<br>");
        		}
        	}
        	if(Util.isEmpty(agntChain)){
        		sb.append(prop.getProperty("C126M01A.agntChain"));
        		if(Util.isNotEmpty(sb)){
        			sb.append("<br>");
        		}
        	}
        	if(Util.isEmpty(receiveDate)){
        		sb.append(prop.getProperty("C126M01A.receiveDate"));
        		if(Util.isNotEmpty(sb)){
        			sb.append("<br>");
        		}
        	}
        	if(Util.isEmpty(contractDate)){
        		sb.append(prop.getProperty("C126M01A.contractDate"));
        		if(Util.isNotEmpty(sb)){
        			sb.append("<br>");
        		}
        	}
        	if(Util.isEmpty(contractNo)){
        		sb.append(prop.getProperty("C126M01A.contractNo"));
        		if(Util.isNotEmpty(sb)){
        			sb.append("<br>");
        		}
        	}
        	if(Util.isEmpty(ttlAmt)){
        		sb.append(prop.getProperty("C126M01A.ttlAmt"));
        		if(Util.isNotEmpty(sb)){
        			sb.append("<br>");
        		}
        	}
        	if(Util.isEmpty(preloanAmt)){
        		sb.append(prop.getProperty("C126M01A.preloanAmt"));
        		if(Util.isNotEmpty(sb)){
        			sb.append("<br>");
        		}
        	}
        	if(Util.isEmpty(applyAmt)){
        		sb.append(prop.getProperty("C126M01A.applyAmt"));
        		if(Util.isNotEmpty(sb)){
        			sb.append("<br>");
        		}
        	}
        	if(Util.isEmpty(lnAmt)){
        		sb.append(prop.getProperty("C126M01A.lnAmt"));
        		if(Util.isNotEmpty(sb)){
        			sb.append("<br>");
        		}
        	}
        	if(Util.isEmpty(estDate)){
        		sb.append(prop.getProperty("C126M01A.estDate"));
        		if(Util.isNotEmpty(sb)){
        			sb.append("<br>");
        		}
        	}
        	if(Util.isEmpty(creditReviewDate)){
        		sb.append(prop.getProperty("C126M01A.creditReviewDate"));
        		if(Util.isNotEmpty(sb)){
        			sb.append("<br>");
        		}
        	}
        	if(Util.isEmpty(lnDate)){
        		sb.append(prop.getProperty("C126M01A.lnDate"));
        		if(Util.isNotEmpty(sb)){
        			sb.append("<br>");
        		}
        	}
        	if(Util.isEmpty(essayName)){
        		sb.append(prop.getProperty("C126M01A.essayName"));
        		if(Util.isNotEmpty(sb)){
        			sb.append("<br>");
        		}
        	}
        	if(Util.isEmpty(statFlag)){
        		sb.append(prop.getProperty("C126M01A.statFlag"));
        		if(Util.isNotEmpty(sb)){
        			sb.append("<br>");
        		}
        	}
        	if(Util.isEmpty(rateKind)){
        		sb.append(prop.getProperty("C126M01A.rateKind"));
        		if(Util.isNotEmpty(sb)){
        			sb.append("<br>");
        		}
        	}
        	if(Util.isEmpty(rebateRatio)){
        		sb.append(prop.getProperty("C126M01A.rebateRatio"));
        		if(Util.isNotEmpty(sb)){
        			sb.append("<br>");
        		}
        	}
        	if(Util.isEmpty(rebate)){
        		sb.append(prop.getProperty("C126M01A.rebate"));
        		if(Util.isNotEmpty(sb)){
        			sb.append("<br>");
        		}
        	}
        }
    	
    	return sb.toString();
    }
    
    @DomainAuth(value = AuthType.Modify)
	public IResult queryC126M01A(PageParameters params) throws CapException {
        int page = Util.parseInt(params.getString(EloanConstants.PAGE));
        CapAjaxFormResult result = new CapAjaxFormResult();
        String mainOid = params.getString(EloanConstants.MAIN_OID);
        C126M01A c126m01a = cls3701Service.findModelByOid(C126M01A.class, mainOid);
        if(c126m01a == null){
            if (!Util.isEmpty(mainOid)) {
                // 第一次啟案自動塞入前案資訊
            } else {
                // 開啟新案帶入起案的分行和目前文件狀態
                result.set("docStatus", this.getMessage("docStatus." + CreditDocStatusEnum.海外_編製中.getCode()));
                result.set("ownBrId", user.getUnitNo());
                result.set("ownBrName", StrUtils.concat(" ", branchService.getBranchName(user.getUnitNo())));
                result.set("docStatusVal", CreditDocStatusEnum.海外_編製中.getCode());
            }
        } else {
            if (!Util.isEmpty(mainOid)) {
            	result = DataParse.toResult(c126m01a);
            	
            	if(Util.isEmpty(c126m01a.getUpdater())){
            		result.set("isNew", true);
            	}
            }
        }

        return defaultResult( params, c126m01a, result);
    }
    private CapAjaxFormResult defaultResult(PageParameters params, C126M01A c126m01a,
            CapAjaxFormResult result) throws CapException {
		// required information
    	//Grid Page傳來的value用來控制CLS3701M01.js的按鈕顯示
		result.set("docStatusVal", params.getString(EloanConstants.MAIN_DOC_STATUS));
		result.set(EloanConstants.MAIN_OID,	CapString.trimNull(c126m01a.getOid()));
		result.set(EloanConstants.MAIN_DOC_STATUS, CapString.trimNull(c126m01a.getDocStatus()));
		result.set("showCustId", StrUtils.concat(CapString.trimNull(c126m01a.getCustId()), " ",
		CapString.trimNull(c126m01a.getDupNo()), " ", CapString.trimNull(c126m01a.getCustName())));
		result.set(EloanConstants.PAGE, Util.trim(params.getString(EloanConstants.PAGE)));
		result.set(EloanConstants.MAIN_ID, Util.trim(c126m01a.getMainId()));
		return result;
	}

    @DomainAuth(AuthType.Accept)
	public IResult batchAppvoeCase(PageParameters params) throws CapException {
        CapAjaxFormResult result = new CapAjaxFormResult();
        String[] oids = params.getStringArray("oids");
        StringBuffer sb = new StringBuffer();
        if (oids.length > 0) {
        	try{
        		for (int i = 0, size = oids.length; i < size; i++) {
        			C126M01A c126m01a = (C126M01A) cls3701Service.findModelByOid(C126M01A.class, oids[i]);
        			if(c126m01a!=null){
    		        	if(user.getUserId().equals(c126m01a.getUpdater())){
    		        		throw new CapMessageException(prop.getProperty("cls3701.err2"), getClass());
    		        	}
    		        	else{
    		        		c126m01a.setDocStatus(CreditDocStatusEnum.先行動用_已覆核.getCode());
    				        cls3701Service.save(c126m01a);
    		        	}
    		        }
        		}
//        		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
//                        .getMainMessage(this.getComponent(),
//                                UtilConstants.AJAX_RSP_MSG.執行成功));
        	}catch(Exception e){
		        logger.error(StrUtils.getStackTrace(e));
		        throw new CapException(e, getClass());
		    }
    	}
		return result;
    }
    
    @DomainAuth(AuthType.Accept)
	public IResult appvoeCase(PageParameters params) throws CapException {
    	SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
    	CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString("oids");
		C126M01A c126m01a = null;
		if (Util.isNotEmpty(mainOid)) {
		    try{
		    	c126m01a = cls3701Service.findModelByOid(C126M01A.class, mainOid);
		        CapBeanUtil.map2Bean(params, c126m01a);
		        if(c126m01a!=null){
		        	if(user.getUserId().equals(c126m01a.getUpdater())){
		        		throw new CapMessageException(prop.getProperty("cls3701.err2"), getClass());
		        	}
		        	else{
		        		c126m01a.setDocStatus(CreditDocStatusEnum.先行動用_已覆核.getCode());
				        cls3701Service.save(c126m01a);
		        	}
		        }
				
		    }catch(Exception e){
		        logger.error(StrUtils.getStackTrace(e));
		        throw new CapException(e, getClass());
		    }
		}
		return defaultResult( params, c126m01a, result);
    }
    
    @DomainAuth(AuthType.Accept)
    public IResult backApproveC126M01A(PageParameters params)
            throws CapException {
    	SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
    	CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString("oids");
		C126M01A c126m01a = null;
		if (Util.isNotEmpty(mainOid)) {
		    try{
		    	c126m01a = cls3701Service.findModelByOid(C126M01A.class, mainOid);
		        CapBeanUtil.map2Bean(params, c126m01a);
		        if(c126m01a!=null){
		        	if(Util.isNotEmpty(c126m01a.getApprover())){
						c126m01a.setApprover(null);
					}
					if(Util.isNotEmpty(c126m01a.getApproveTime())){
						c126m01a.setApproveTime(null);
					}
		        	c126m01a.setIsClosed(UtilConstants.DEFAULT.否);
		        	c126m01a.setDocStatus(CreditDocStatusEnum.海外_編製中.getCode());
			        cls3701Service.backApproveSave(c126m01a);
		        }
				
		    }catch(Exception e){
		        logger.error(StrUtils.getStackTrace(e));
		        throw new CapException(e, getClass());
		    }
		}
		return defaultResult( params, c126m01a, result);
    }

    /**
	 * kind=1, 寫死 "是 or 否"
	 * kind=2, 寫死 "有 or 無"
	 * kind=3, 1:是. 2:否
	 * kind=4, 1:有. 2:無
	 * kind=5, 1:里. 2:村
	 * kind=6, 1:"". 2:路. 3:街
	 **/
	private String showPic(String value, String kind,
						   Map<String, CapAjaxFormResult> codeTypes, Locale locale) {
		Map<String, String> yesNoMap = null;
		StringBuffer str = new StringBuffer();
		try {
			String code = "";
			if(Util.equals(kind, "1") || Util.equals(kind, "3")){
				code = "YesNo";
			} else if(Util.equals(kind, "2") || Util.equals(kind, "4")){
				code = "HaveNo";
			}

			if(Util.equals(kind, "1") || Util.equals(kind, "2")){
				str.append(codeTypes.get(code).get(value));
			} else if(Util.equals(kind, "3") || Util.equals(kind, "4")){
				str.append((Util.equals(value, "1") ? "■" : "□"));
				str.append(codeTypes.get(code).get("1"));
				str.append((Util.equals(value, "2") ? "■" : "□"));
				str.append(codeTypes.get(code).get("2"));
			} else if(Util.equals(kind, "5")){
				Map<String, String> k4Map = new HashMap<String, String>();
				k4Map.put("1", "里");
				k4Map.put("2", "村");
				str.append(k4Map.get(value));
			} else if(Util.equals(kind, "6")){
				Map<String, String> k5Map = new HashMap<String, String>();
				k5Map.put("1", "");
				k5Map.put("2", "路");
				k5Map.put("3", "街");
				str.append(k5Map.get(value));
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		} finally {
			if (yesNoMap != null) {
				yesNoMap.clear();
			}
		}
		return str.toString();
	}

	public IResult getSpecialBank(PageParameters params) throws CapException {
		// 儲存and檢核
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		result.set("isSpecialBank", cls3701Service.checkSpecialBank(user.getUnitNo()));
		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult batchEditRebate(PageParameters params) throws CapException {
        CapAjaxFormResult result = new CapAjaxFormResult();
        String[] datas = params.getStringArray("datas");
        StringBuffer sb = new StringBuffer();
        List<Map<String, Object>> list=new ArrayList<Map<String, Object>>();
        if (datas.length > 0) {
        	for(String data : datas){
        		String[] temp=data.split(";");
        		if(Util.isNotEmpty(Util.trim(temp[0])) && Util.isNotEmpty(Util.trim(temp[2]))){
        			Map<String, Object> map = new HashMap<String, Object>();
        			map.put("elf604_loan_no", Util.trim(temp[0]));
        			map.put("elf604_cntrno_no", Util.trim(temp[1]));
        	        map.put("elf604_1st_amt", Util.trim(temp[2]));
        	        map.put("elf604_upd_amt", Util.trim(temp[2]));
        	        if(!list.contains(map)){
        	        	list.add(map);
        	        }
        		}
        	}
        	if(list.size()>0){
        		cls3701Service.saveELF604(list);
        	}
        }
        return result;
    }
	
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryAgentLicenseInfo(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String agntName = Util.trim(params.getString("agntName"));
		C126S01A c126s01a = new C126S01A(mainId, agntName);
		this.rpaProcessService.deleteBeforeQueryRealEstateAgentCertNo(mainId);
		this.rpaProcessService.gotoRPAJobsForGettingRealEstateAgentCertNo(c126s01a);
		c126s01a = this.rpaProcessService.getC126S01ABy(mainId);
		result.set("statusName", c126s01a == null ? "" : RPAProcessServiceImpl.statusNameMap.get(c126s01a.getStatus()));
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
				RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		return result;
	}
	
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult updateAgentLicenseInfoStatus(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		C126S01A c126s01a = this.rpaProcessService.getC126S01ABy(mainId);
		
		String statusName = c126s01a == null ? "" : RPAProcessServiceImpl.statusNameMap.get(c126s01a.getStatus());
		String remark = c126s01a != null && StringUtils.isNotBlank(c126s01a.getResponseMsg()) ? c126s01a.getResponseMsg() : "" ;
		result.set("statusName", statusName);
		result.set("remark", remark);
		return result;
	}
}
