<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
	<body>
		<th:block th:fragment="innerPageBody">
		
				<script type="text/javascript">
				loadScript('pagejs/cls/CLS3101M01Page');
				</script>

			<div class="button-menu funcContainer" id="buttonPanel">
				
			<!--編製中 -->
			<th:block th:if="${_btnDOC_EDITING_visible}">
	        		<button id="btnSave"> 
	        			<span class="ui-icon ui-icon-jcs-04" ></span>
	        			<th:block th:text="#{'button.save'}"><!--儲存--></th:block>
	        		</button>
					<button id="btnSend" >
	        			<span class="ui-icon ui-icon-jcs-02" ></span>
	        			<th:block th:text="#{'button.send'}" ><!--呈主管覆核--></th:block>
	        		</button>
		       </th:block>	
				
				<!--待覆核 -->
				<th:block th:if="${_btnWAIT_APPROVE_visible}">
	        		<button id="btnAccept" >
	        			<span class="ui-icon ui-icon-jcs-106" ></span>
	        			<th:block th:text="#{'button.check'}" ><!--覆核--></th:block>
	        		</button>
		        </th:block>			
		        
                <button id="btnPrint" class="forview">
                	<span class="ui-icon ui-icon-jcs-03"></span>
					<th:block th:text="#{'button.print'}"><!--列印--></th:block>
				</button>
                <button id="btnExit"  class="forview">
                	<span class="ui-icon ui-icon-jcs-01"></span>
					<th:block th:text="#{'button.exit'}"><!--離開--></th:block>
				</button>
				
            </div>
			<div class="tit2 color-black">
				<th:block th:text="#{'page.title'}">同一通訊處註記</th:block>：<span id="custInfo" class="color-blue" ></span>
			</div>
						
			<form id="tabForm">
			<fieldset>
                    <legend>
                        <b><th:block th:text="#{'page.title'}">同一通訊處註記</th:block></b>
                    </legend>
					
					<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
						<tbody>
							<tr>
							<td class="hd2" align="right"><th:block th:text="#{'C310M01A.ownBrId'}">分行名稱</th:block>&nbsp;&nbsp;</td>
							<td >
								<span id="ownBrId" class="field" ></span>&nbsp;<span id="ownBrIdName" class="field" ></span>
							</td>
							<td class="hd2" align="right"><th:block th:text="#{'C310M01A.docStatus'}">文件狀態</th:block>&nbsp;&nbsp;</td>
							<td><b class="text-red"><span id="docStatus" ></span>&nbsp;</b></td>
						</tr>
						<tr>
							<td class="hd2" align="right"><th:block th:text="#{'C310M01A.custId'}">借款人統編</th:block>&nbsp;&nbsp;</td>
							<td><span id="custId" class="field" ></span>-<span id="dupNo" class="field" ></span></td>
							<td class="hd2" align="right"><th:block th:text="#{'C310M01A.custName'}">借款人姓名</th:block>&nbsp;&nbsp;</td>
							<td ><span id="custName"></span></td>
						</tr>
						<tr>
							<td class="hd2" align="right"><th:block th:text="#{'C900S02E.cyc_mn'}">資料年月</th:block>&nbsp;&nbsp;</td>
							<td colspan="3" ><span id="cyc_mn"></span></td>
						</tr>
						<tr>
							<td class="hd2" align="right"><th:block th:text="#{'C900S02E.text'}">通訊處</th:block>&nbsp;&nbsp;</td>
							<td colspan="3" ><span id="text"></span></td>
						</tr>
						<tr style='vertical-align:top;'>
							<td class="hd2" align="right"><th:block th:text="#{'label.c900s02f'}">相同借戶明細</th:block>&nbsp;&nbsp;</td>
							<td colspan="3" >
									<div id="grid_C900S02F">
									</div>
							</td>
						</tr>
						<tr>
							<td class="hd2" align="right"><th:block th:text="#{'C310M01A.chk_result'}">查證結果</th:block>&nbsp;&nbsp;</td>
							<td colspan="3" >
								<label><input type="radio" id="chk_result" name="chk_result" value="Y" class="required" /><th:block th:text="#{'C310M01A.chk_result.Y'}">正常</th:block>
								</label>
								<label><input type="radio" id="chk_result" name="chk_result" value="N" class="required" /><th:block th:text="#{'C310M01A.chk_result.N'}">異常</th:block>
								</label>
							</td>
						</tr>	
						<tr>
							<td class="hd2" align="right"><th:block th:text="#{'C310M01A.chk_memo'}">理由及後續處理方式</th:block>&nbsp;&nbsp;</td>
							<td colspan="3" >
								<textarea cols="100" rows="3" id="chk_memo" name="chk_memo" maxlength="300" maxlengthC="100" class="required" ></textarea>
							</td>
						</tr>	
						</tbody>
					</table>
					
			</fieldset>		
			
			    <fieldset>
                    <legend>
                        <b><th:block th:text="#{'doc.docUpdateLog'}"><!-- 文件異動紀錄 --></th:block></b>
                    </legend>
                    <div class="funcContainer">
                        <div class="funcContainer"><!-- 文件異動紀錄--> <div th:include="common/panels/DocLogPanel :: DocLogPanel"></div>

                    </div>
                    <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tbody>
                            <tr>
                                <td width="35%" class="hd1">
                                    <th:block th:text="#{'doc.creator'}">
                                        <!--  文件建立者-->
                                    </th:block>&nbsp;&nbsp;
                                </td>
                                <td width="15%">
                                    <span id='creator'></span>(<span id='createTime'></span>)
                                </td>
                                <td width="30%" class="hd1">
                                    <th:block th:text="#{'doc.lastUpdater'}">
                                        <!--  最後異動者-->
                                    </th:block>&nbsp;&nbsp;
                                </td>
                                <td width="20%">
                                    <span id='updater'></span>(<span id='updateTime'></span>)
                                </td>
                            </tr>
                            <tr>
                                <td class="hd1">
                                </td>
                                <td>
                                </td>
                                <td class="hd1">
                                    <th:block th:text="#{'doc.docCode'}">
                                        <!--文件亂碼-->
                                    </th:block>&nbsp;&nbsp;
                                </td>
                                <td>
                                    <span id="randomCode" ></span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </fieldset>
				
				<fieldset>
					
	                <div id="tabs-appr" class="tabs" style='width:99%;'>
	                    <ul>
	                        <li>
	                            <a href="#tabs-appr01"><b>
	                                    <th:block th:text="#{'C310M01E.title01'}">
	                                        
	                                    </th:block>
	                                </b></a>
	                        </li>
	                    </ul>
						<div class="tabCtx-warp">
	                        <div id="tabs-appr01" class="content">
 							<table width="100%">
                                <tr>
                                    <td class="rt" style='width:60px;'>
                                        <b class="text-red">
                                            <th:block th:text="#{'C310M01E.apprId'}">
                                                <!--  經辦-->
                                            </th:block>：
                                        </b>
                                    </td>
                                    <td width="12%" class="lt">
                                        <span id="showApprId"></span>
                                    </td>
                                    <td class="rt" style='width:120px;'>
                                        <b class="text-red">
                                            <th:block th:text="#{'C310M01E.reCheckId'}">
                                                <!--覆核主管-->
                                            </th:block>：
                                        </b>
                                    </td>
                                    <td width="18%" class="lt">
                                        <span id="reCheckId"></span>
                                    </td>
                                    <td class="rt" style='width:120px;'>
                                        <b class="text-red">
										    <th:block th:text="#{'C310M01E.bossId'}">
										      <!-- 授信主管 -->
										    </th:block>：
                                        </b>
                                    </td>
                                    <td width="24%" class="lt">
                                        <span id="bossId" ></span>
                                    </td>
									 <td width="12%">&nbsp;
									 </td>
                                	<!--
                                    <td width="12%" class="rt">
                                        <b class="text-red">
                                            <th:block th:text="#{'C310M01E.managerId'}">經副襄理</th:block>：
                                        </b>
                                    </td>									
                                    <td width="12%" class="lt">
                                        <span id="managerId" />
                                    </td>
									-->
                                </tr>
                            </table>
							</div>
	                    </div>

	               </div>				   
	            </fieldset>
			</form>	
			<!--===============================================-->	
			  <div id="selectBossBox"  style="display:none;">
			  <form id="selectBossForm">
	         	<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
	                 <tr>
	            		<td class="hd1" width="60%"><th:block th:text="#{'C310M01E.selectBoss'}"><!--  授信主管人數--></th:block>&nbsp;&nbsp;</td>
	                    <td width="40%"><select id="numPerson" name="numPerson">
	                    		<option value="1">1</option>
	                    		<option value="2">2</option>
	                            <option value="3">3</option>
								<option value="4">4</option>
	                    		<option value="5">5</option>
	                            <option value="6">6</option>
								<option value="7">7</option>
	                    		<option value="8">8</option>
	                            <option value="9">9</option>
								<option value="10">10</option>
	                    	</select>
							</td>
	                 </tr>
	                 <tr >
	                 	<td class="hd1" ><th:block th:text="#{'C310M01E.bossId'}"><!--  授信主管--></th:block>&nbsp;&nbsp;</td>
	            		<td >
	            			<div id="bossItem"></div>
	                 	</td>
	                 </tr>
					 <!--
	                 <tr >
	            		<td class="hd1"><th:block th:text="#{'C310M01E.managerId'}">經副襄理</th:block>&nbsp;&nbsp;</td>
	                    <td><div id="managerItem"></div></td>
	                 </tr>
					 -->
	           	 </table>
				</form>
  			</div>
		</th:block>
    </body>
</html>
