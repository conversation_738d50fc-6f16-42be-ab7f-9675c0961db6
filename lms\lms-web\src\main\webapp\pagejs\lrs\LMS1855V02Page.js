$(document).ready(function(){
    $("#buttonPanel").before('<div class=" tit2 color-black" id="searchActionName" name="searchActionName"></div>');
    $("#gridview").remove();
    show("searchAll");
    
    function padLeft(str, lenght){
    	str = "" + str;
        if (str.length >= lenght) 
            return str;
        else             
            return padLeft("0" + str, lenght);
    }
    
    function show(queryAction){
        brank();
        var showHeight = 235;
        if (queryAction == 'searchAll') {
            $("#report").show();
        } else if (queryAction == 'searchReport') {
            $("#report").hide();
        }
        $("#startDate").val(CommonAPI.getToday().split("-")[0] + "-" + padLeft(parseInt(CommonAPI.getToday().split("-")[1], 10) - 1, 2));
        $("#endDate").val(CommonAPI.getToday().split("-")[0] + "-" + padLeft(parseInt(CommonAPI.getToday().split("-")[1], 10) - 1, 2));
        
        $("#lms1855new").thickbox({
            title: i18n.lms1855v02['newInfo'],
            width: 500,
            height: showHeight,
            modal: false,
            align: 'center',
            valign: 'bottom',
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var action = $('select#searchAction option:selected').val();
                    refactorGrid();
                    //YYYY-MM 格式驗證
                    var startDate = $("#startDate").val();
                    if (startDate == "") {
                        //val.ineldate=請輸入年月
                        return CommonAPI.showMessage(i18n.def["val.ineldate"]);
                    }
                    
                    if (!startDate.match(/^\d{4}\-(0?[1-9]|1[0-2])$/)) {
                        //val.date2=日期格式錯誤(YYYY-MM)
                        return CommonAPI.showMessage(i18n.def["val.date2"]);
                    }
                    var endDate = $("#endDate").val();
                    if (endDate == "") {
                        //val.ineldate=請輸入年月
                        return CommonAPI.showMessage(i18n.def["val.ineldate"]);
                    }
                    
                    if (!endDate.match(/^\d{4}\-(0?[1-9]|1[0-2])$/)) {
                        //val.date2=日期格式錯誤(YYYY-MM)
                        return CommonAPI.showMessage(i18n.def["val.date2"]);
                    }
                    if (parseInt(endDate.replace("-", ""), 10) < parseInt(startDate.replace("-", ""), 10)) {
                        return CommonAPI.showMessage(i18n.lms1855v02["L1855v02.startMonth"] + i18n.lms1855v02["L1855v02.error"]);
                    }
                    
                    var msg = "";
                    switch (action) {
                        case '1':
                            msg = type1();
                            break;
                        case '2':
                            msg = type2();
                            break;
                        case '3':
                            msg = type3();
                            break;
                        case '4':
                            msg = type4();
                            break;
                    }
                    if (msg == '' || !msg) {
                        $("#searchActionName").val($('select#searchAction option:selected').text() + "(" + startDate + "~" + endDate + ")");
                        $.thickbox.close();
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
    //==============================================
    function refactorGrid(){
        $("#gridview").remove();
        $("#gridDiv").html('<div id="gridview" />');
    }
    // ================================================================================================================
    
    function brank(){
        $.ajax({
            type: "POST",
            handler: "lms1855m01formhandler",
            data: {
                formAction: "queryBranch"
            },
            success: function(obj){
                //alert(JSON.stringify(obj));
                var data = {
                    border: "none",
                    size: 3,
                    item: obj.item
                };
                $("#order1").setItems(data);
                //                $("#order1").setItems({
                //                    // i18n : i18n.samplehome,
                //                    item: obj.item,
                //                    format: "{value} {key}", // ,
                //                    space: false
                //                    // value :
                //                });
            }
        });
    }
    // ================================================================================================================
    $("#buttonPanel").find("#btnView").click(function(){
        var id = $("#gridview").getGridParam('selrow');
        if (!id) {
            // action_004=請先選擇需「調閱」之資料列
            return CommonAPI.showMessage(i18n.def["action_004"]);
        }
        var result = $("#gridview").getRowData(id);
        openDoc(null, null, result);
    }).end().find("#btnFilter").click(function(){
        show("searchReport");
    });
});//最外層
function getBrNos(idName){
    var choiceBrNos = "";
    $('input:checkbox:checked[id="' + idName + '"]').each(function(i){
        if (choiceBrNos != '') {
            choiceBrNos = choiceBrNos + "^";
        }
        choiceBrNos = choiceBrNos + this.value;
    });
    return choiceBrNos;
}

// ======================================================================================================
function type1(){
    var choiceBrNos = getBrNos("order1");
    if (choiceBrNos == "") {
        //val.inelbranch=請選分行
        return CommonAPI.showMessage(i18n.def["val.inelbranch"]);
    }
    var grid = $("#gridview").iGrid({
        rownumbers: true,
        handler: 'lms1855gridhandler',
        height: 350, // 設定高度
        width: 785,
        autowidth: false,
        // multiselect : true,
        postData: {
            formAction: "queryL185M01aH",
            searchAction: $("#searchAction").val(),
            startDate: $("#startDate").val(),
            endDate: $("#endDate").val(),
            brNos: choiceBrNos
        },
        colModel: [{
            name: 'oid',
            hidden: true
        }, {
            name: 'uid',
            hidden: true
        }, {
            name: 'mainId',
            hidden: true
        }, {
            name: 'listName',
            hidden: true
        }, {
            name: 'reportFile',
            hidden: true
        }, {
            colHeader: i18n.lms1855v02["L185M01a.ownBrId"], // 單位別
            align: "center",
            width: 100,
            sortable: true,
            name: 'ownBrId'
        }, {
            colHeader: i18n.lms1855v02["L185M01a.dataDate"], // 資料基準日
            align: "center",
            width: 100,
            sortable: true,
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d',
                newformat: 'Y-m'
            },
            name: 'dataDate'
        }, {
            colHeader: i18n.lms1855v02["L185M01a.conut"], // 資料筆數
            align: "right",
            width: 100,
            sortable: true,
            name: 'recCount',
            formatter: GridFormatter.number['addComma']
        }, {
            colHeader: i18n.lms1855v02["L185M01a.createTime"], // 產生日期
            align: "center",
            width: 100,
            sortable: true,
            name: 'createTime'
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = grid.getRowData(rowid);
            download(null, null, data);
        }
        
    });
}

// ======================================================================================================
function type2(){
    var choiceBrNos = getBrNos("order1");
    if (choiceBrNos == "") {
        //val.inelbranch=請選分行
        return CommonAPI.showMessage(i18n.def["val.inelbranch"]);
    }
    var grid = $("#gridview").iGrid({
        rownumbers: true,
        handler: 'lms1855gridhandler',
        height: 350, // 設定高度
        width: 785,
        autowidth: false,
        postData: {
            formAction: "queryL185M01aH",
            searchAction: $("#searchAction").val(),
            startDate: $("#startDate").val(),
            endDate: $("#endDate").val(),
            brNos: choiceBrNos
        },
        colModel: [{
            name: 'oid',
            hidden: true //是否隱藏
        }, {
            name: 'uid', //col.id,
            hidden: true //是否隱藏
        }, {
            name: 'mainId', //col.id
            hidden: true //是否隱藏
        }, {
            name: 'reportFile', // col.id
            hidden: true
            // 是否隱藏
        }, {
            colHeader: i18n.lms1855v02["L185M01a.ownBrId"], // 單位別
            align: "left",
            width: 100, //設定寬度
            sortable: true, //是否允許排序
            //formatter : 'click',
            //onclick : function,
            name: 'ownBrId' //col.id
        }, {
            colHeader: i18n.lms1855v02["L185M01a.dataDate"], //資料基準日
            align: "center",
            width: 100, //設定寬度
            sortable: true, //是否允許排序
            name: 'dataDate' //col.id
        }, {
            colHeader: i18n.lms1855v02["L185M01a.acctCount"], //戶數
            align: "right",
            width: 100, //設定寬度
            sortable: true, //是否允許排序
            //formatter : 'click',
            //onclick : function,
            name: 'acctCount' //col.id
        }, {
            colHeader: i18n.lms1855v02["L185M01a.uLoanYCount"], //聯貸筆數
            align: "right",
            width: 100, //設定寬度
            sortable: true, //是否允許排序
            //formatter : 'click',
            //onclick : function,
            name: 'uLoanYCount' //col.id
        }, {
            colHeader: i18n.lms1855v02["L185M01a.uLoanNCount"], //非聯貸筆數
            align: "right",
            width: 100, //設定寬度
            sortable: true, //是否允許排序
            //formatter : 'click',
            //onclick : function,
            name: 'uLoanNCount' //col.id
        }, {
            colHeader: i18n.lms1855v02["L185M01a.createTime"], // 產生日期
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'createTime' // col.id
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = grid.getRowData(rowid);
            download(null, null, data);
        }
        
    });
}

//======================================================================================================
function type3(){
    var choiceBrNos = getBrNos("order1");
    if (choiceBrNos == "") {
        //val.inelbranch=請選分行
        return CommonAPI.showMessage(i18n.def["val.inelbranch"]);
    }
    var grid = $("#gridview").iGrid({
        rownumbers: true,
        handler: 'lms1855gridhandler',
        height: 350, // 設定高度
        width: 785,
        autowidth: false,
        postData: {
            formAction: "queryL185M01aH",
            searchAction: $("#searchAction").val(),
            startDate: $("#startDate").val(),
            endDate: $("#endDate").val(),
            brNos: choiceBrNos
        },
        colModel: [{
            name: 'oid',
            hidden: true //是否隱藏
        }, {
            name: 'uid', //col.id,
            hidden: true //是否隱藏
        }, {
            name: 'mainId', //col.id
            hidden: true //是否隱藏
        }, {
            name: 'rptType', //col.id,
            hidden: true //是否隱藏
        }, {
            name: 'reportFile', // col.id
            hidden: true
            // 是否隱藏
        }, {
            colHeader: i18n.lms1855v02["L185M01a.ownBrId"], // 單位別
            align: "left",
            width: 100, //設定寬度
            sortable: true, //是否允許排序
            name: 'branchId' //col.id
        }, {
            colHeader: i18n.lms1855v02["L185M01a.sDate"], //資料基準日(起)
            align: "center",
            width: 100, //設定寬度
            sortable: true, //是否允許排序
            name: 'dataDate' //col.id
        }, {
            colHeader: i18n.lms1855v02["L185M01a.eDate"], //資料基準日(迄)
            align: "center",
            width: 100, //設定寬度
            sortable: true, //是否允許排序
            name: 'dataEDate' //col.id
        }, {
            colHeader: i18n.lms1855v02["L185M01a.delayCount"], //逾期筆數
            align: "center",
            width: 100, //設定寬度
            sortable: true, //是否允許排序
            //formatter : 'click',
            //onclick : function,
            name: 'delayCount' //col.id
        }, {
            colHeader: i18n.lms1855v02["L185M01a.acctCount"], //戶數合計
            align: "center",
            width: 100, //設定寬度
            sortable: true, //是否允許排序
            //formatter : 'click',
            //onclick : function,
            name: 'acctCount' //col.id
        }, {
            colHeader: i18n.lms1855v02["L185M01a.createTime"], // 產生日期
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'createTime' // col.id
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = grid.getRowData(rowid);
            download(null, null, data);
        }
    });
}

//======================================================================================================
function type4(){
    var choiceBrNos = getBrNos("order1");
    if (choiceBrNos == "") {
        //val.inelbranch=請選分行
        return CommonAPI.showMessage(i18n.def["val.inelbranch"]);
    }
    var grid = $("#gridview").iGrid({
        rownumbers: true,
        handler: 'lms1855gridhandler',
        height: 350, // 設定高度
        width: 785,
        autowidth: false,
        postData: {
            formAction: "queryL185M01aH",
            searchAction: $("#searchAction").val(),
            startDate: $("#startDate").val(),
            endDate: $("#endDate").val(),
            brNos: choiceBrNos
            //docStatus : viewstatus
        },
        colModel: [{
            name: 'oid',
            hidden: true //是否隱藏
        }, {
            name: 'uid', //col.id,
            hidden: true //是否隱藏
        }, {
            name: 'mainId', //col.id
            hidden: true //是否隱藏
        }, {
            name: 'reportFile', // col.id
            hidden: true
            // 是否隱藏
        }, {
            colHeader: i18n.lms1855v02["L185M01a.ownBrId"], // 單位別
            align: "left",
            width: 100, //設定寬度
            sortable: true, //是否允許排序
            name: 'branchId' //col.id
        }, {
            colHeader: i18n.lms1855v02["L185M01a.delayCount"], //逾期筆數
            align: "right",
            width: 100, //設定寬度
            sortable: true, //是否允許排序
            name: 'delayCount' //col.id
        }, {
            colHeader: i18n.lms1855v02["L185M01a.conut"], // 資料筆數
            align: "right",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'recCount' // col.id
        }, {
            colHeader: i18n.lms1855v02["L185M01a.createTime"], // 產生日期
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'createTime' // col.id
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = grid.getRowData(rowid);
            download(null, null, data);
        }
    });
}

//======================================================================================================
//下載EXCEL
function download(cellvalue, options, data){
    //alert(data.rptType);
    var rptTypeOid = data.reportFile;
    
    $.capFileDownload({
        //  handler: "simplefiledwnhandler",
        data: {
            fileOid: rptTypeOid
        }
    });
    
}

function padLeft(str, lenght){
	str = "" + str;
    if (str.length >= lenght) 
        return str;
    else         
        return padLeft("0" + str, lenght);
}

function changeReport(){
    $("#searchDataDate").val(CommonAPI.getToday().split("-")[0] + "-" + padLeft(parseInt(CommonAPI.getToday().split("-")[1], 10) - 1, 2));
}
