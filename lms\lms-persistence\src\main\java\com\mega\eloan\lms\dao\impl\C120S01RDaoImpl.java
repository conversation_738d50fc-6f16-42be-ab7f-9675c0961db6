package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.C120S01RDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C120S01R;

/** 個金卡友貸信用評等表 **/
@Repository
public class C120S01RDaoImpl extends LMSJpaDao<C120S01R, String> implements
		C120S01RDao {

	@Override
	public C120S01R findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C120S01R> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<C120S01R> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public C120S01R findByUniqueKey(String mainId, String ownBrId,
			String custId, String dupNo) {
		ISearch search = createSearchTemplete();
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (ownBrId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
					ownBrId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<C120S01R> findByIndex01(String mainId, String ownBrId,
			String custId, String dupNo) {
		ISearch search = createSearchTemplete();
		List<C120S01R> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (ownBrId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
					ownBrId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<C120S01R> findByCustIdDupId(String custId, String dupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		List<C120S01R> list = createQuery(C120S01R.class, search)
				.getResultList();
		return list;
	}

	@Override
	public int deleteByOid(String oid) {
		Query query = entityManager.createNamedQuery("C120S01R.deleteOid");
		query.setParameter("OID", oid);
		return query.executeUpdate();
	}
}