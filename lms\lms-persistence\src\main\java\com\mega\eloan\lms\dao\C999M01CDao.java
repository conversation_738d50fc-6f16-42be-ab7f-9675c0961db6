/* 
 * C999M01CDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C999M01C;

/** 個金約據書連保人(保證人)檔 **/
public interface C999M01CDao extends IGenericDao<C999M01C> {

	C999M01C findByOid(String oid);

	List<C999M01C> findByMainId(String mainId);

	List<C999M01C> findByIndex01(String oid);

	List<C999M01C> findByCustIdDupId(String custId, String DupNo);
}