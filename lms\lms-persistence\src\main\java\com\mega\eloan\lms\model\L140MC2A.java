/* 
 * L140MC2A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 比對相同資料檢核明細紀錄檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L140MC2A", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId","custId","dupNo"}))
public class L140MC2A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** mainId **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 額度序號 **/
	@Size(max=12)
	@Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)")
	private String cntrNo;

	/** 身分證統編 **/
	@Size(max=10)
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Size(max=1)
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 借款人姓名 **/
	@Size(max=120)
	@Column(name="CUSTNAME", length=120, columnDefinition="VARCHAR(120)")
	private String custName;

	/** 通訊地址(標的) **/
	@Size(max=300)
	@Column(name="ADDRESS", length=300, columnDefinition="VARCHAR(300)")
	private String address;

	/** 行動電話 **/
	@Size(max=150)
	@Column(name="CELLPHONE", length=150, columnDefinition="VARCHAR(150)")
	private String cellphone;

	/** 通訊電話 **/
	@Size(max=150)
	@Column(name="TELPHONE", length=150, columnDefinition="VARCHAR(150)")
	private String telphone;

	/** email **/
	@Size(max=120)
	@Column(name="EMAIL", length=120, columnDefinition="VARCHAR(120)")
	private String email;

	/** 版本 **/
	@Size(max=3)
	@Column(name="VERSION", length=3, columnDefinition="CHAR(3)")
	private String version;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得mainId **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定mainId **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得額度序號 **/
	public String getCntrNo() {
		return this.cntrNo;
	}
	/** 設定額度序號 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/** 取得身分證統編 **/
	public String getCustId() {
		return this.custId;
	}
	/** 設定身分證統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得身分證統編重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}
	/** 設定身分證統編重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得借款人姓名 **/
	public String getCustName() {
		return this.custName;
	}
	/** 設定借款人姓名 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/** 取得通訊地址(標的) **/
	public String getAddress() {
		return this.address;
	}
	/** 設定通訊地址(標的) **/
	public void setAddress(String value) {
		this.address = value;
	}

	/** 取得行動電話 **/
	public String getCellphone() {
		return this.cellphone;
	}
	/** 設定行動電話 **/
	public void setCellphone(String value) {
		this.cellphone = value;
	}

	/** 取得通訊電話 **/
	public String getTelphone() {
		return this.telphone;
	}
	/** 設定通訊電話 **/
	public void setTelphone(String value) {
		this.telphone = value;
	}

	/** 取得email **/
	public String getEmail() {
		return this.email;
	}
	/** 設定email **/
	public void setEmail(String value) {
		this.email = value;
	}

	/** 取得版本 **/
	public String getVersion() {
		return this.version;
	}
	/** 設定版本 **/
	public void setVersion(String value) {
		this.version = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
