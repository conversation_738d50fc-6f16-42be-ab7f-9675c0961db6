/* 
 * L140S02DDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140S02D;

/** 分段利率明細檔 **/
public interface L140S02DDao extends IGenericDao<L140S02D> {

	L140S02D findByOid(String oid);

	List<L140S02D> findByMainId(String mainId);

	L140S02D findByUniqueKey(String mainId, Integer seq, Integer phase);

	List<L140S02D> findByIndex01(String mainId, Integer seq, Integer phase);

	List<L140S02D> findByMainidSeq(String mainId, Integer seq
			);

	List<L140S02D> findByMainidSeqIsUseBox(String mainId, Integer seq,
			String IsUseBox);

	List<L140S02D> findByMainIdOrderbyPhase(String mainId);
	
	List<L140S02D> findByMainidSeqIsUseBoxOrderbyPhase(String mainId, Integer seq, String isUseBox);
}