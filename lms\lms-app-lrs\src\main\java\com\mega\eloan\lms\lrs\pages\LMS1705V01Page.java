/* 
 * LMS1705V01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lrs.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;

/**
 * <pre>
 * [企金]覆審報告表(編製中)
 * </pre>
 * 
 * @since 2012/2/15
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/2/15,jessica,new
 *          </ul>
 */
@Controller
@RequestMapping("/lrs/lms1705v01")
public class LMS1705V01Page extends AbstractEloanInnerView {

	public LMS1705V01Page() {
		super();
	}

	@Override
	public void execute(ModelMap model, PageParameters params) throws Exception {

		setGridViewStatus(RetrialDocStatusEnum.編製中);
		addToButtonPanel(model, LmsButtonEnum.Add, LmsButtonEnum.Filter,
				LmsButtonEnum.Delete, LmsButtonEnum.View);
		renderJsI18N(LMS1705V01Page.class);
		model.addAttribute("loadScript", "loadScript('pagejs/lrs/LMS1705V01Page');");
	}

}
