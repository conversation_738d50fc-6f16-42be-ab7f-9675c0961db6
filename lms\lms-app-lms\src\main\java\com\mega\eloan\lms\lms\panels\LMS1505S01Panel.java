/* 
 * LMS1505S01Panel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.panels;

import java.util.LinkedHashSet;

import org.springframework.ui.ModelMap;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.DocLogPanel;
import com.mega.eloan.common.panels.Panel;



/**<pre>
 * 小放會會議記錄- 文件資訊
 * </pre>
 * @since  2011/10/5
 * <AUTHOR>
 * @version <ul>
 *           <li>2011/10/5,REX,new
 *          </ul>
 */
public class LMS1505S01Panel extends Panel {

	public LMS1505S01Panel(String id) {
		super(id);
	}

	public LMS1505S01Panel(String id, boolean updatePanelName) {
		super(id, updatePanelName);
	}

	/**/
	private static final long serialVersionUID = 1L;
	
	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);
		
		//UPGRADE
		LinkedHashSet<String> keys = new LinkedHashSet<>();
		keys.add("AbstractEloan");
		keys.add("LMS1505M01");
		RequestContextHolder.getRequestAttributes().setAttribute("basenameKey", keys, RequestAttributes.SCOPE_REQUEST);

		model.addAttribute("showHeader", false);
		
		new DocLogPanel("_docLog").processPanelData(model, params);//頁面要作用地方的wicket ID
	}

}
