var pageAction = {
	handler : 'cls9021formhandler',
	grid : null,
	build : function(){
		$("#searchThickBox").thickbox({
			title : i18n.cls9021v00["searchTitle"],//查詢優惠房貸額度
			width : 400,
			height : 200,
			modal : true,
			align : 'center',
			valign: 'bottom',
			i18n: i18n.def,
			buttons : {
				'sure' : function(){
					$.thickbox.close();
					$.ajax({
						handler : pageAction.handler,
						action : 'obtainAmount',
						data : {
							type:$("input[name=type]:checked").val()
						},
						success : function(response){
							//把資料餵進text裡
							var title = $("span#title");
							switch(response["kindNo"]){
							case '1':title.html(i18n.cls9021v00["cls9021v00.mortgage8ke"]);//八千億優惠房貸
								break;
							case '2':title.html(i18n.cls9021v00["cls9021v00.mortgage3ke"]);//三千億優惠房貸
								break;
							case '3':title.html(i18n.cls9021v00["cls9021v00.mortgage2ke"]);//九十七年度二千億優惠房貸
								break;
							case '4':title.html(i18n.cls9021v00["cls9021v00.teenager"]);//青年安心成家優惠貸款-購置住宅
							}
							$("#kindNo").val(response["kindNo"]);
							$("#totapp").val(response["totapp"]);
							$("#favloan").val(response["favloan"]);
							$("#rfavloan").val(response["rfavloan"]);
							var height = 280;
							var button={
								'close' : function(){	
									$.thickbox.close();
								}
							};
							if(response["canEdit"]){
								$("#editRow").show();
								button = {
									'uploadFile.button' : function(){
										if($('#resultForm').valid()){
											$.thickbox.close();
											$.ajax({
												handler : pageAction.handler,
												action : 'saveTotapp',
												form : "resultForm",
												data : {},
												success : function(response){
													
												}
											});
										}
									},
									'close' : function(){	
										$.thickbox.close();
									}
								};
							}
							else{
								height=240;
								$("#editRow").hide();
							}
							//開視窗
							$("#resultThickBox").thickbox({
								title : i18n.cls9021v00["resultTitle"],//查詢結果
								width : 450,
								height : height,
								modal : true,
								align : 'left',
								valign: 'top',
								i18n: i18n.def,
								buttons : button
							});
						}
					});
				},
				'close' : function(){	
					$.thickbox.close();
				}
			}
		});
	}
}

$(function() {
	pageAction.build();
});