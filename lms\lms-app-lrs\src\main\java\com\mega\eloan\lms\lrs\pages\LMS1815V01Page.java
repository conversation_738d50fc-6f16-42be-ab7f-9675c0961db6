/* 
 * LMS1815V01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lrs.pages;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.html.EloanPageFragment;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;

import tw.com.jcs.auth.AuthType;

/**<pre>
 * 覆審控制檔-編制中
 * </pre>
 * @since  2011/9/20
 * <AUTHOR>
 * @version <ul>
 *           <li>2011/9/20,irene,new
 *          </ul>
 */
@Controller
@RequestMapping("/lrs/lms1815v01")
public class LMS1815V01Page extends AbstractEloanInnerView {

	public LMS1815V01Page() {
		super();
	}

	@Override
	public void execute(ModelMap model, PageParameters params) {
		setGridViewStatus(RetrialDocStatusEnum.編製中);
		// 加上Button
		List<EloanPageFragment> list = new ArrayList<>();
		if (this.getAuth(AuthType.Modify)) {
			list.add(LmsButtonEnum.Add);
		}
		list.add(LmsButtonEnum.Delete);
		addToButtonPanel(model, list);
		renderJsI18N(LMS1815V01Page.class);
		model.addAttribute("hasHtml", false);
		model.addAttribute("loadScript",
				"loadScript('pagejs/lrs/LMS1815V01Page');");
	}

//	public String[] getJavascriptPath() {
//		return new String[] { "pagejs/lrs/LMS1815V01Page.js" };
//	}
}
