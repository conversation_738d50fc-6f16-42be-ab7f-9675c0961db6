/* 
 * C101S01NDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.C101S01NDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C101S01N;


/** 個金相關查詢授信歸戶檔 **/
@Repository
public class C101S01NDaoImpl extends LMSJpaDao<C101S01N, String>
	implements C101S01NDao {

	@Override
	public C101S01N findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C101S01N> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<C101S01N> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public C101S01N findByUniqueKey(String mainId, String custId, String dupNo, String loanNo){
		ISearch search = createSearchTemplete();
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (loanNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "loanNo", loanNo);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			return findUniqueOrNone(search);
		}
		return null;
	}
	@Override
	public List<C101S01N> findByCustIdDupId(String custId,String DupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", DupNo);
		List<C101S01N> list = createQuery(C101S01N.class,search).getResultList();
		return list;
	}
	@Override
	public List<C101S01N> findByIndex01(String mainId, String custId, String dupNo, String loanNo){
		ISearch search = createSearchTemplete();
		List<C101S01N> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (loanNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "loanNo", loanNo);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			search.setFirstResult(0).setMaxResults(Integer.MAX_VALUE);
			search.addOrderBy("bankNo");
			search.addOrderBy("quotaDup");
			list = createQuery(search).getResultList();
		}
		return list;
	}
	
	@Override
	public int deleteByOid(String oid) {
		Query query = entityManager.createNamedQuery("C101S01N.deleteOid");
		query.setParameter("OID", oid);
		return query.executeUpdate();
	}
}