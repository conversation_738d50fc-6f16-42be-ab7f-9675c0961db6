/* 
 * L210S01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L210S01A;

/** 自行聯貸攤貸比率檔 **/
public interface L210S01ADao extends IGenericDao<L210S01A> {

	L210S01A findByOid(String oid);

	List<L210S01A> findByMainId(String mainId);

	List<L210S01A> findByOids(String[] oids);

	L210S01A findByUniqueKey(String mainId, String flag, String shareBrId,
			String chgFlag);

	List<L210S01A> findByIndex01(String mainId, String flag, String shareBrId,
			String chgFlag);

	List<L210S01A> findByMainIdAndChgFlag(String mainId, String chgFlag);

	List<L210S01A> findByCntrNo(String CntrNo);

	List<L210S01A> findByCustIdDupId(String custId, String DupNo);

	/**
	 * 自行聯貸
	 * 
	 * @param mainId
	 *            文件編號
	 * @param shareBrId
	 *            分行
	 * @param chgFlag
	 * @return
	 */
	L210S01A findByMainIdAndChgFlag(String mainId, String shareBrId,
			String chgFlag);
}