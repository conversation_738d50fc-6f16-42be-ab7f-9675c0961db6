package com.mega.eloan.lms.base.constants;

public interface ClsScoreConstants {
	/** 房貸評等因子(S01X vs ) **/
	interface ratingFactor_G_V1_3 {
		// 個金基本資料檔
		interface C101S01A {
			String 學歷 = "edu";
			String 婚姻狀況 = "marry";
			String 子女數 = "child";
		}

		// 個金服務單位檔
		interface C101S01B {
			String 學歷 = "edu";
			String 職業別大類 = "jobType1";
			String 職業別細項 = "jobType2";
			String 職稱 = "jobTitle";
			String 年薪 = "payAmt";
			String 其他收入金額 = "othAmt";
		}

		// 個金償債能力檔
		interface C101S01C {
			String 夫妻年收入 = "yFamAmt";
			String 個人負債比率 = "dRate";
			String 家庭負債比率 = "yRate";
		}
	}

	interface ratingFactor_G_V2_0 { // latest_G
		// 個金服務單位檔
		interface C101S01B {
			String 職稱 = "jobTitle"; //值[g,h]在 N018 使用
			String 年薪 = "payAmt";
			String 其他收入金額 = "othAmt";
			String 年資 = "seniority";
		}

		// 個金償債能力檔
		interface C101S01C {
			String 夫妻年收入 = "yFamAmt";
			String 個人負債比率 = "dRate";
			String 家庭負債比率 = "yRate";
		}
	}
	//==================================================
	interface ratingFactor_Q_V1_0 { // 非房貸
		// 個金基本資料檔
		interface C101S01A {
			String 非房貸學歷 = "edu";
		}

		// 個金服務單位檔
		interface C101S01B {
			String 個人年收入 = "payAmt";
			String 年資 = "seniority";
		}
	}
	
	interface ratingFactor_Q_V2_0 {
		// 個金服務單位檔
		interface C101S01B {
			String 個人年收入 = "payAmt";
			String 其他收入 = "othAmt";
			String 年資 = "seniority";
		}
	}
	
	interface ratingFactor_Q_V2_1 {
		// 個金服務單位檔
		interface C101S01B {
			String 個人年收入 = "payAmt";
			String 其他收入 = "othAmt";
			String 年資 = "seniority";
		}
	}
	
	interface ratingFactor_Q_V3_0 {
		// 個金服務單位檔
		interface C101S01B {
			String 職稱 = "jobTitle"; //值[g,h]在 N06 使用
			String 個人年收入 = "payAmt";
			String 其他收入 = "othAmt";
		}
		// 個金償債能力檔
		interface C101S01C {
			String 個人負債比率 = "dRate";
		}
	}
	
	interface ratingFactor_Q_V4_0 {
		// 個金服務單位檔
		interface C101S01A {
			String 非房貸學歷 = "edu";
		}
		// 個金償債能力檔
		interface C101S01C {
			String 個人負債比率 = "dRate";
			String 本行有擔餘額 = "loanBalSByid";
		}
	}
	//==================================================
	interface ratingFactor_R_V2_1 { // 卡友貸
		// 個金服務單位檔
		interface C101S01B {
			String 個人年收入 = "payAmt";
			String 其他收入 = "othAmt";
			String 年資 = "seniority";
		}
	}
	
	interface ratingFactor_R_V3_0 {
		// 個金服務單位檔
		interface C101S01B {
			String 職稱 = "jobTitle"; //值[g,h]在 N06 使用
			String 個人年收入 = "payAmt";
			String 其他收入 = "othAmt";
		}
		// 個金償債能力檔
		interface C101S01C {
			String 個人負債比率 = "dRate";
		}
	}
	
	interface ratingFactor_R_V4_0 {
		// 個金服務單位檔
		interface C101S01A {
			String 非房貸學歷 = "edu";
		}
		// 個金償債能力檔
		interface C101S01C {
			String 個人負債比率 = "dRate";
			String 本行有擔餘額 = "loanBalSByid";
		}
	}

}
