/* 
 * LMS2405S02Panel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.crs.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;

import tw.com.jcs.auth.AuthType;

import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.lms.model.C240M01A;

/**
 * <pre>
 * 覆審名單 - 明細資料
 * </pre>
 * 
 * @since 2011/8
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/9/6,irene
 *          </ul>
 */
public class LMS2405S02Panel extends Panel {

	public LMS2405S02Panel(String id, PageParameters params) {
		super(id);
	}
	
	public LMS2405S02Panel(String id,boolean updatePanelName) {
		super(id);
	}
	
	public LMS2405S02Panel(String id, PageParameters params,boolean updatePanelName) {
		super(id);
	}
	
	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);
		addAclLabel(model, new AclLabel("button", AuthType.Modify, params, true));
		addAclLabel(model, new AclLabel("produce", params, C240M01A.class,
				AuthType.Modify, FlowDocStatusEnum.編製中));
		new LMS2415S01Panel("tabsa-01").processPanelData(model, params);
		new LMS2415S02Panel("tabsa-02").processPanelData(model, params);
		new LMS2415S05Panel("tabsa-03").processPanelData(model, params);
	}

	private static final long serialVersionUID = 1L;

}
