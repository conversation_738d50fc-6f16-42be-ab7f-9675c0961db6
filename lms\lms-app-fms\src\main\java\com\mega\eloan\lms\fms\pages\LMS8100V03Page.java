package com.mega.eloan.lms.fms.pages;

import java.util.ArrayList;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.html.EloanPageFragment;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.jcs.common.Util;

/**
 * <pre>
 * 		覆審考核表作業 - 已核准
 * </pre>
 * 
 * @since 2022
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Controller@RequestMapping(path = "/fms/lms8100v03")
public class LMS8100V03Page extends AbstractEloanInnerView {

	public LMS8100V03Page() {
		super();
	}

	@Override
	public void  execute(ModelMap model, PageParameters params) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		setGridViewStatus(CreditDocStatusEnum.海外_已核准);
		
		// 加上Button
		ArrayList<EloanPageFragment> btns = new ArrayList<>();
		// 主管跟經辦都會出現的按鈕
		btns.add(LmsButtonEnum.Filter);
		btns.add(LmsButtonEnum.View);
		
		if(Util.notEquals(user.getUnitNo(), UtilConstants.BankNo.授管處)){
			btns.add(LmsButtonEnum.ReturnToCompiling);
			btns.add(LmsButtonEnum.ProduceRankingBoard);
		} else {
			btns.add(LmsButtonEnum.Print);
		}
		addToButtonPanel(model, btns);

		renderJsI18N(AbstractEloanPage.class);
		renderJsI18N(LMS8100V01Page.class);
	}
	
	public String[] getJavascriptPath() {
		return new String[] { "pagejs/fms/LMS8100V01Page.js" };
	}
}