/* 
 * AbstractEjcicJdbc.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.ejcic.service.impl;

import javax.annotation.Resource;
import javax.sql.DataSource;

import tw.com.iisi.cap.context.CapParameter;

import com.mega.eloan.common.exception.GWException;
import com.mega.eloan.common.jdbc.EloanJdbcTemplate;
import com.mega.eloan.common.service.EjcicLogService;

/**
 * <pre>
 * 聯徵資料
 * </pre>
 * 
 * @since 2012/10/23
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/10/23,Fantasy,new
 *          </ul>
 */
public class AbstractEjcicJdbc {

	public final String DEFAULT_CHARSET = "MS950";

	EloanJdbcTemplate jdbc;

	@Resource(name = "ejcicSqlStatement")
	CapParameter sqlp;

	@Resource
	EjcicLogService ejcicLogService;

	@Resource(name = "ejcic-db")
	public void setDataSource(DataSource dataSource) {
		jdbc = new EloanJdbcTemplate(dataSource, GWException.GWTYPE_EJCIC,
				ejcicLogService);
		jdbc.setSqlProvider(sqlp);
		jdbc.setCauseClass(this.getClass());
	}

	/**
	 * getJdbc
	 * 
	 * @return EloanJdbcTemplate
	 */
	public EloanJdbcTemplate getJdbc() {
		return jdbc;
	}

	/**
	 * 取得Sql
	 * 
	 * @param sqlId
	 *            sqlId
	 * @return sqlString
	 */
	public String getSqlBySqlId(String sqlId) {
		return sqlp.getValue(sqlId);
	}

}
