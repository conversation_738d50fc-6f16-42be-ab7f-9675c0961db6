/* 
 * LMS9541Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.service;

import java.util.Map;

public interface LMS9541V04Service {
	final int BASE = 10000;// 單位

	final String[] COL = { "brno", "custId", "dupNo", "unDocId", "areaNo",
			"cName", "appDate", "appMoney", "favloan", "noHouse", "apprDate",
			"favcntno", "orcntno1", "orcntno2", "updater", "tmestamp",
			"kindNo", "loanDate", "cntrno", "sellerId" };

	// use MIS-RDB
	public Map<String, Object> findMisData(String custId, String kindNo);

	public boolean save(String[] data, String custId, String kindNo);
}
