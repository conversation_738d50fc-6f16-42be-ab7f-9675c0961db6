/* 
 * C900M01HDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.lms.dao.C900M01HDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C900M01H;

/** 地政士黑名單 **/
@Repository
public class C900M01HDaoImpl extends LMSJpaDao<C900M01H, String>
	implements C900M01HDao {

	@Override
	public C900M01H findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C900M01H> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<C900M01H> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public List<C900M01H> findActiveByCertNo(String year, String word, String no){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "agentCertYear", year);
		search.addSearchModeParameters(SearchMode.EQUALS, "agentCertWord", word);
		search.addSearchModeParameters(SearchMode.EQUALS, "agentCertNo", no);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime","");
		search.addSearchModeParameters(SearchMode.IN, EloanConstants.DOC_STATUS
					, new String[]{FlowDocStatusEnum.已核准.getCode(), 
						FlowDocStatusEnum.待解除.getCode()});
		if(true){
			Map<String, Boolean> map = new LinkedHashMap<String, Boolean>();
			map.put("approveTime", true);
			search.setOrderBy(map);	
		}
		search.setMaxResults(Integer.MAX_VALUE);
		
		List<C900M01H> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public C900M01H findActiveMajorByCertNo(String year, String word, String no){
		List<C900M01H> list = findActiveByCertNo(year, word, no);
		if(list.size()==0){
			return null;
		}
		
		//依「重要性」回傳
		
		//先回傳4
		for(C900M01H model: list){
			if(Util.equals(model.getCtlFlag(), "4")){
				return model;
			}
		}

		//再回傳1
		for(C900M01H model: list){
			if(Util.equals(model.getCtlFlag(), "1")){
				return model;
			}
		}
		return list.get(0);
	}
}