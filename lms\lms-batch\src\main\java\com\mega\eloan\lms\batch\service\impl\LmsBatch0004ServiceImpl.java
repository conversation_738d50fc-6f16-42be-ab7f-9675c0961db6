package com.mega.eloan.lms.batch.service.impl;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.TimeZone;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.constants.SysParamConstants;
import com.mega.eloan.common.gwclient.EmailClient;
import com.mega.eloan.common.service.RPAService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.dao.L180M01ADao;
import com.mega.eloan.lms.dao.impl.C240M01ADaoImpl;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.mfaloan.service.MisELF506Service;
import com.mega.eloan.lms.mfaloan.service.MisELF515Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C240M01A;
import com.mega.eloan.lms.model.L180M01A;
import com.mega.eloan.lms.obsdb.service.ObsdbELF515Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.annotation.NonTransactional;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.PropUtil;
import tw.com.jcs.common.Util;

@Service("lmsbatch0004serviceimpl")
public class LmsBatch0004ServiceImpl extends AbstractCapService implements
		WebBatchService {

	private static Logger LOGGER = LoggerFactory
			.getLogger(LmsBatch0004ServiceImpl.class);

	@Resource
	DwdbBASEService dwdbBASEService;

	@Resource
	MisELF506Service misELF506Service;

	@Resource
	MisELF515Service misELF515Service;

	@Resource
	ObsdbELF515Service obsdbELF515Service;

	@Resource
	BranchService branchService;

	@Resource
	RPAService rpaservice;

	@Resource
	MisdbBASEService misDBService;

	@Resource
	L180M01ADao l180m01aDao;

	@Resource
	LMSService lmsService;

	@Resource
	RetrialService retrialService;

	@Resource
	C240M01ADaoImpl c240m01aDao;
	@Resource
	SysParameterService sysparamService;

	@Resource
	EmailClient emailClient;

	private static final String TogetherQ = "000";

	@Override
	@NonTransactional
	public JSONObject execute(JSONObject json) {

		JSONObject result = new JSONObject();
		String errorMsg = "";
		StringBuffer rpaExeOk = new StringBuffer("");
		StringBuffer rpaExeErr = new StringBuffer("");

		try {
			LOGGER.info("-------------------lmsbatch0004serviceimpl start -----------------------------------");

			// URL=http://127.0.0.1:9081/lms-web/app/scheduler,TIMEOUT=9999,SERVICEID=lmsbatch0004serviceimpl,REQUEST={exDate:"2021-10-07",putQueMode:"S",exBrNo:"005,006",reExe:"Y"}

			errorMsg = this.queryRpaQuery(json, rpaExeOk, rpaExeErr);

			if (Util.notEquals(Util.trim(errorMsg), "")) {
				result = WebBatchCode.RC_ERROR;
				String returnErrMsg = "lmsbatch0004serviceimpl執行失敗！==>"
						+ "，成功資料==>"
						+ StringUtils.replace(rpaExeOk.toString(), "\"", "")
						+ "，失敗資料==>"
						+ StringUtils.replace(rpaExeErr.toString(), "\"", "")
						+ "，錯誤訊息==>" + StringUtils.replace(errorMsg, "\"", "");
				result.element(WebBatchCode.P_RESPONSE, returnErrMsg);

				sendEmail(false, returnErrMsg);
			} else {
				result = WebBatchCode.RC_SUCCESS;
				String returnErrMsg = "lmsbatch0004serviceimpl執行成功！==>"
						+ StringUtils.replace(rpaExeOk.toString(), "\"", "");
				result.element(WebBatchCode.P_RESPONSE, returnErrMsg);
				sendEmail(true, returnErrMsg);
			}

		} catch (Exception ex) {
			ex.printStackTrace();
			result = WebBatchCode.RC_ERROR;
			String returnErrMsg = "lmsbatch0004serviceimpl執行失敗！==>" + "成功資料==>"
					+ rpaExeOk.toString() + "，失敗資料==>"
					+ StringUtils.replace(rpaExeErr.toString(), "\"", "")
					+ "，錯誤訊息==>" + ex.getLocalizedMessage();
			result.element(WebBatchCode.P_RESPONSE, returnErrMsg);
			sendEmail(false, returnErrMsg);

		}

		return result;
	}

	private void sendEmail(boolean isOk, String errorMsg) {
		String hostAddr = sysparamService
				.getParamValue(SysParamConstants.MAIL_ADDRESS_HOST);
		boolean isTestEmail = true; // 是否為測試信件

		if (Util.equals(Util.trim(hostAddr), "")) {
			isTestEmail = "true".equals(PropUtil.getProperty("isTestEmail")) ? true
					: false; // 是否為測試信件
		} else {
			if (StringUtils.indexOf(hostAddr, "@notes.") >= 0) { // Production
				isTestEmail = false;
			} else {
				isTestEmail = true;
			}
		}

		List<String> recv = new ArrayList<String>();
		recv.clear();
		if (isTestEmail) {
			recv.add("<EMAIL>");
			// return;
		} else {
			String mailOth = "";
			// INSERT INTO com.BSYSPARAM(OID, PARAM, PARAMVALUE, PARAMDESC,
			// LASTMODIFYBY, LASTMODIFYTIME) VALUES
			// (GET_OID(),'LMS_RPA_L224_ERROR_MAIL','05097,03966,11295','e-Loan排程執行錯誤通知：SLMS-00138
			// 啟動覆審RPA L224','005097',current timestamp);
			mailOth = Util.trim(lmsService
					.getSysParamDataValue((isOk ? "LMS_RPA_L224_OK_MAIL"
							: "LMS_RPA_L224_ERROR_MAIL")));
			if (Util.notEquals(mailOth, "") && Util.notEquals(mailOth, "X")) {
				for (String xx : mailOth.split(",")) {
					recv.add(xx + "@notes.megabank.com.tw");
				}
			}
		}
		int recvSize = recv.size();
		String[] aRecv = new String[recvSize];
		Object[] oRecv = recv.toArray();
		// 將傳送者Email型別Object[]改為String[]
		for (int j = 0; j < recvSize; j++) {
			aRecv[j] = Util.trim(oRecv[j]);
		}
		// 無附加檔案的Email寄送

		StringBuffer conetent = new StringBuffer();
		// other.msg28=】異常通報表報送通知
		conetent.append(
				"e-Loan排程執行【" + (isOk ? "成功" : "失敗") + "】通知：SLMS-00138【")
				.append("啟動覆審RPA L224").append("】");

		LOGGER.info("lmsbatch0004serviceimpl 執行結果：" + conetent.toString());

		emailClient.send(true, null, aRecv, conetent.toString(),
				conetent.toString() + "\r\n" + "\r\n" + errorMsg);

	}

	public String queryRpaQuery(JSONObject json, StringBuffer rpaExeOk,
			StringBuffer rpaExeErr) throws CapMessageException {
		String token = "";
		String errorMsg = "";
		Map<String, Object> objResult = new LinkedHashMap<String, Object>();

		try {
			// Step 1 取得 token
			JSONObject resultJson = null;
			try {
				resultJson = rpaservice.getRPAAccessToken(objResult);
				token = resultJson != null ? resultJson.getString("result")
						: "";
				token = "Bearer " + token;
				if (CapString.isEmpty(token)) {
					errorMsg = "queryRpaQuery\\getRPAAccessToken失敗，請稍後再試。";
					LOGGER.info(errorMsg);
					return errorMsg;
				}
			} catch (Exception e) {
				errorMsg = "queryRpaQuery\\取得RPA Token失敗，請稍後再試。";
				LOGGER.info(errorMsg);
				return errorMsg;
			}

			errorMsg = this.executeRPAJobs(token, json, rpaExeOk, rpaExeErr);

		} catch (CapException e) {
			// throw new CapMessageException(e.getMessage(), this.getClass());
			errorMsg = "queryRpaQuery失敗\\" + e.getMessage();
			LOGGER.info(errorMsg);
			return errorMsg;
		}

		return errorMsg;
	}

	/**
	 * 啟動RPA覆審 PUT Q
	 * 
	 * 
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public String executeRPAJobs(String token, JSONObject json,
			StringBuffer rpaExeOk, StringBuffer rpaExeErr) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String errorMsg = "", processKey = "";
		Map<String, Object> objResult = new LinkedHashMap<String, Object>();
		JSONObject resultJson = null;
		boolean isTestEmail = "true"
				.equals(PropUtil.getProperty("isTestEmail")) ? true : false; // 是否為測試信件

		// URL=http://127.0.0.1:9081/lms-web/app/scheduler,TIMEOUT=9999,SERVICEID=lmsbatch0004serviceimpl,REQUEST={exDate:"2021-10-07",putQueMode:"S",exBrNo:"005,006",reExe:"Y"}

		JSONObject request = json.getJSONObject("request");
		String exBgnDate = request.getString("exBgnDate");
		String exEndDate = request.getString("exEndDate");
		String putQueMode = request.getString("putQueMode"); // S:PUT Q
																// 一間分行單獨分別PUT
																// Q，A:所有分行一起PUT
																// 再一個Q
		String exBrNo = request.getString("exBrNo"); // 需要特別執行的分行，空白代表全部執行
		String reExe = request.getString("reExe"); // Y
													// 代表STATUS已經是A02已經完成的也要重做
		String defaultBgnDay = request.getString("defaultBgnDay"); // 7天
																	// 2021-11-29
		String defaultDurDay = request.getString("defaultDurDay"); // 6天
																	// 2021-12-05

		String putQTogether = request.getString("putQTogether"); // Q是否集中一個，N:BY營運中心分別PUT
																	// Q，Y:集中一個Q

		LOGGER.info(
				"exBgnDate:{},exEndDate:{},putQueMode:{},exBrNo:{},reExe:{},defaultBgnDay:{},defaultDurDay:{} ",
				new Object[] { exBgnDate, exEndDate, putQueMode, exBrNo, reExe,
						defaultBgnDay, defaultDurDay });

		String bgnDate = "";
		String endDate = "";
		if (Util.notEquals(exBgnDate, "") && Util.notEquals(exEndDate, "")) {
			bgnDate = exBgnDate;
			endDate = exEndDate;
		} else {
			// 今天加上兩天
			int tBgnDay = Util.parseInt(defaultBgnDay);
			int tDurDay = Util.parseInt(defaultDurDay);
			Date toDate = Util.parseDate(CapDate.getCurrentDate("yyyy-MM-dd"));
			Date calcDateBgn = CapDate.shiftDays(toDate, tBgnDay);
			Date calcDateEnd = CapDate.shiftDays(toDate, tBgnDay + tDurDay);
			bgnDate = CapDate.formatDate(calcDateBgn, "yyyy-MM-dd"); // 2021-11-29
			endDate = CapDate.formatDate(calcDateEnd, "yyyy-MM-dd"); // 2021-12-05
		}

		List<String> exBrNolist = null;
		if (Util.notEquals(exBrNo, "")) {
			String[] exBrNos = exBrNo.split(",");
			exBrNolist = Arrays.asList(exBrNos);
		}

		// List<Map<String, Object>> listMap = misDBService
		// .queryLnf09gByTxnCodeAndKey2("L224", bgnDate, endDate);
		Map<String, Map<String, String>> allMap = new HashMap<String, Map<String, String>>();

		// 企金覆審名單
		List<L180M01A> listl180m01a = l180m01aDao.findByDefaultCTLDate("0A0",
				bgnDate, endDate);

		if (listl180m01a != null && !listl180m01a.isEmpty()) {
			for (L180M01A l180m01a : listl180m01a) {
				String branchId = l180m01a.getBranchId();
				String areaName = branchService.getBranch(branchId)
						.getBrnGroup();
				String defaultCTLDate = CapDate.formatDate(
						l180m01a.getDefaultCTLDate(), "yyyy-MM-dd");
				String mainId = l180m01a.getMainId();

				if (Util.equals(areaName, "")) {
					areaName = branchId;
				}
				if (!this.chkRpaExeOwnerBrId(l180m01a.getOwnBrId())) {
					continue;
				}

				// 集中一個Q
				if (Util.equals(putQTogether, "Y")) {
					areaName = TogetherQ;
				}

				boolean isOk = false;
				if (l180m01a != null) {
					// 企金覆審
					// 分行、預計覆審日、mainid 右邊八碼
					if (Util.equals(reExe, "Y")) {
						// 不管有沒有執行過，都要重新執行
						isOk = true;
					} else if (Util.notEquals(l180m01a.getStatus(), "A02")) {
						// 沒有完成的才要重做
						isOk = true;
					}

				}

				// 判斷是否屬於允許執行之分行
				if (isOk && Util.notEquals(exBrNo, "")) {
					if (!exBrNolist.contains(branchId)) {
						// 如果有限制執行分行'若不屬於執行分行則改成不執行
						isOk = false;
					}
				}

				if (isOk) {

					// 重新上傳L224
					retrialService.sendBtt(l180m01a);

					String key = branchId + ":" + defaultCTLDate; // 分行、預計覆審日

					if (allMap.containsKey(areaName)) {
						Map<String, String> map = allMap.get(areaName);
						if (map.containsKey(key)) {
							String rpaKey = Util.trim(MapUtils.getString(map,
									key));
							if (Util.notEquals(rpaKey, "")) {
								rpaKey = rpaKey + ","
										+ Util.getRightStr(mainId, 8);
							} else {
								rpaKey = Util.getRightStr(mainId, 8);
							}
							map.put(key, rpaKey);
						} else {
							String rpaKey = Util.getRightStr(mainId, 8);
							map.put(key, rpaKey);
						}
						allMap.put(areaName, map);

					} else {
						Map<String, String> map = new HashMap<String, String>();
						String rpaKey = Util.getRightStr(mainId, 8);
						map.put(key, rpaKey);
						allMap.put(areaName, map);
					}

				}

			}
		}

		// 個金複審名單
		List<C240M01A> listc240m01a = c240m01aDao.findByDefaultCTLDate("0A0",
				bgnDate, endDate);

		if (listc240m01a != null && !listc240m01a.isEmpty()) {
			for (C240M01A c240m01a : listc240m01a) {
				String branchId = c240m01a.getBranchId();
				String areaName = branchService.getBranch(branchId)
						.getBrnGroup();
				String defaultCTLDate = CapDate.formatDate(
						c240m01a.getExpectedRetrialDate(), "yyyy-MM-dd");
				String mainId = c240m01a.getMainId();

				if (Util.equals(areaName, "")) {
					areaName = branchId;
				}

				if (!this.chkRpaExeOwnerBrId(c240m01a.getOwnBrId())) {
					continue;
				}

				// 集中一個Q
				if (Util.equals(putQTogether, "Y")) {
					areaName = TogetherQ;
				}

				boolean isOk = false;
				if (c240m01a != null) {
					// 企金覆審
					// 分行、預計覆審日、mainid 右邊八碼
					if (Util.equals(reExe, "Y")) {
						// 不管有沒有執行過，都要重新執行
						isOk = true;
					} else if (Util.notEquals(c240m01a.getStatus(), "A02")) {
						// 沒有完成的才要重做
						isOk = true;
					}

				}

				// 判斷是否屬於允許執行之分行
				if (isOk && Util.notEquals(exBrNo, "")) {
					if (!exBrNolist.contains(branchId)) {
						// 如果有限制執行分行'若不屬於執行分行則改成不執行
						isOk = false;
					}
				}

				if (isOk) {

					// 重新上傳L224
					retrialService.sendBtt(c240m01a);

					String key = branchId + ":" + defaultCTLDate; // 分行、預計覆審日

					if (allMap.containsKey(areaName)) {
						Map<String, String> map = allMap.get(areaName);
						if (map.containsKey(key)) {
							String rpaKey = Util.trim(MapUtils.getString(map,
									key));
							if (Util.notEquals(rpaKey, "")) {
								rpaKey = rpaKey + ","
										+ Util.getRightStr(mainId, 8);
							} else {
								rpaKey = Util.getRightStr(mainId, 8);
							}
							map.put(key, rpaKey);
						} else {
							String rpaKey = Util.getRightStr(mainId, 8);
							map.put(key, rpaKey);
						}
						allMap.put(areaName, map);

					} else {
						Map<String, String> map = new HashMap<String, String>();
						String rpaKey = Util.getRightStr(mainId, 8);
						map.put(key, rpaKey);
						allMap.put(areaName, map);
					}

				}

			}
		}

		LOGGER.info("啟動JOB-啟動覆審 開始========================");
		boolean rpaError = false;
		StringBuffer rpaErrMsg = new StringBuffer("");
		if (allMap != null && !allMap.isEmpty()) {

			if (Util.equals(putQueMode, "S")) {
				// BY 分行 PUT Q
				// 不同分行會有不同的營運中心的Q
				// key = LNF09G_KEY_1 + ":" + LNF09G_KEY_2; //分行、預計覆審日

				for (String areaKey : allMap.keySet()) {
					LOGGER.info("啟動RPA開始=>" + SysParamConstants.RPA_啟動覆審 + "_"
							+ areaKey + "========================");

					Map<String, String> map = allMap.get(areaKey);

					// 排序MAP***********************************************************

					// 已覆審日由早到晚排序
					Map<String, String> sortedMapAsc = sortByComparator(map);

					// *****************************************************************

					if (sortedMapAsc != null && !sortedMapAsc.isEmpty()) {
						for (String key : sortedMapAsc.keySet()) {

							JSONArray jsonArrayAllVal = new JSONArray();
							String jsonStr = "";

							String brNo = key.split(":")[0];
							String retrialDate = key.split(":")[1];
							String rpaKey = MapUtils.getString(sortedMapAsc,
									key);

							JSONObject jsonVal = new JSONObject();
							jsonVal.put("brNo", brNo);
							jsonVal.put("retrialDate", retrialDate);
							jsonVal.put("rpaKey", rpaKey);
							jsonStr = jsonVal.toString();
							jsonArrayAllVal.add(jsonVal);
							jsonStr = jsonArrayAllVal.toString();

							// 測試OK***********************************************
							jsonStr = StringUtils.replace(jsonStr, "[", "(");
							jsonStr = StringUtils.replace(jsonStr, "]", ")");
							// *******************************************************
							errorMsg = this.rpaPutQ(token, processKey, areaKey,
									jsonStr, rpaExeOk, rpaExeErr);
							if (Util.notEquals(Util.trim(errorMsg), "")) {
								rpaError = true;
								rpaErrMsg
										.append((Util.equals(
												rpaErrMsg.toString(), "") ? ""
												: "、")).append(errorMsg);
							}
						}
					}

					LOGGER.info("啟動RPA結束=>" + SysParamConstants.RPA_啟動覆審 + "_"
							+ areaKey + "========================");
				}

			} else {
				// 當天所有分行BY營運中心PUT Q
				// 一個營運中心的Q，有很多不同分行，一次發送
				for (String areaKey : allMap.keySet()) {
					LOGGER.info("啟動RPA開始=>" + SysParamConstants.RPA_啟動覆審 + "_"
							+ areaKey + "========================");
					String jsonStr = "";
					JSONArray jsonArrayAllVal = new JSONArray();

					Map<String, String> map = allMap.get(areaKey);

					if (map != null && !map.isEmpty()) {
						for (String key : map.keySet()) {
							String brNo = key.split(":")[0];
							String retrialDate = key.split(":")[1];
							String rpaKey = MapUtils.getString(map, key);

							JSONObject jsonVal = new JSONObject();
							jsonVal.put("brNo", brNo);
							jsonVal.put("retrialDate", retrialDate);
							jsonVal.put("rpaKey", rpaKey);
							jsonArrayAllVal.add(jsonVal);
						}

						jsonStr = jsonArrayAllVal.toString();

						// 2021-01-21

						// jsonStr = "\"" + "\"" + jsonStr + "\"" + "\"";

						// jsonStr = "\"" + jsonStr + "\"" ;

						// jsonStr = StringUtils.replace(jsonStr, "[",
						// "\""+"\"");
						// jsonStr = StringUtils.replace(jsonStr, "]",
						// "\""+"\"");

						// 測試OK***********************************************
						jsonStr = StringUtils.replace(jsonStr, "[", "(");
						jsonStr = StringUtils.replace(jsonStr, "]", ")");
						// *******************************************************
					}

					errorMsg = this.rpaPutQ(token, processKey, areaKey,
							jsonStr, rpaExeOk, rpaExeErr);

					if (Util.notEquals(Util.trim(errorMsg), "")) {
						rpaError = true;
						rpaErrMsg.append(
								(Util.equals(rpaErrMsg.toString(), "") ? ""
										: "\r")).append(errorMsg);
					}

					LOGGER.info("啟動RPA結束=>" + SysParamConstants.RPA_啟動覆審 + "_"
							+ areaKey + "========================");
				}
			}

		}
		LOGGER.info("啟動JOB-啟動覆審 結束========================");

		return rpaErrMsg.toString();
	}

	public String rpaPutQ(String token, String processKey, String areaKey,
			String jsonStr, StringBuffer rpaExeOk, StringBuffer rpaExeErr) {
		String errorMsg = "";

		Map<String, Object> objResult = new LinkedHashMap<String, Object>();
		JSONObject resultJson = null;
		boolean isTestEmail = "true"
				.equals(PropUtil.getProperty("isTestEmail")) ? true : false; // 是否為測試信件

		String qName = Util.equals(areaKey, TogetherQ) ? "" : ("_" + areaKey);
		String areaName = Util.equals(areaKey, TogetherQ) ? "" : areaKey;

		try {
			// STEP2 啟動JOB-啟動覆審
			objResult = new LinkedHashMap<String, Object>();
			try {
				LOGGER.info("啟動RPA開始=>" + SysParamConstants.RPA_啟動覆審 + qName
						+ ":" + jsonStr);

				// 設定要傳遞的參數 LinkedHashMap 才可以按順序取出
				objResult
						.put("responseURL",
								sysparamService
										.getParamValue(SysParamConstants.RPA_GW_RESPONSE_URL_LMS)); // 回傳位址
																									// SIT
																									// :
																									// "http://192.168.53.85:9081/lms-web/app/schedulerRPA"
				objResult.put("system", "eloan");
				objResult.put("uniqueID", IDGenerator.getUUID());
				// objResult.put("DeferDate",
				// StringUtils.replace(CapDate
				// .getCurrentTimestamp().toString(), " ", ""));

				String firstPartOfPattern = "yyyy-MM-dd";
				String secondPartOfPattern = "HH:mm:ss";

				String RPACORPORATELOANGUAR_DEFERDATE = Util.trim(lmsService
						.getSysParamDataValue("RPA_START_RETRIAL_DEFERDATE"));

				long deferDateMin = 0; // 延遲時間
				if (Util.notEquals(RPACORPORATELOANGUAR_DEFERDATE, "")) {
					deferDateMin = Util
							.parseLong(RPACORPORATELOANGUAR_DEFERDATE);
				}

				Timestamp deferDate = CapDate.getCurrentTimestamp();

				deferDate.setTime(CapDate.getCurrentTimestamp().getTime()
						+ deferDateMin); // 加一小時

				if (isTestEmail) {
					// TESTING****************************************************************
					deferDate = CapDate.getCurrentTimestamp();
				}

				SimpleDateFormat sdf1 = new SimpleDateFormat(firstPartOfPattern);
				sdf1.setTimeZone(TimeZone.getTimeZone("GMT"));
				SimpleDateFormat sdf2 = new SimpleDateFormat(
						secondPartOfPattern);
				sdf2.setTimeZone(TimeZone.getTimeZone("GMT"));
				String formattedDate = sdf1.format(deferDate) + "T"
						+ sdf2.format(deferDate) + ".4407392Z";

				objResult.put("DeferDate", formattedDate);

				// objResult.put("data_BranchNo", user.getUnitNo());
				// objResult.put(
				// "empNo",
				// CapString.isNumeric(user.getUserId()) ?
				// String.format(
				// "%06d", Integer.valueOf(user.getUserId()))
				// : user.getUserId());

				// 查詢條件
				objResult.put("data_Retrial", jsonStr);

				LOGGER.info("傳入參數==>[{}]", objResult.toString());

				resultJson = rpaservice.StartRPAJobForLMS(objResult, token,
						processKey, SysParamConstants.RPA_啟動覆審 + qName);

				LOGGER.info("執行啟動RPA結果=>[{}]", resultJson.toString());
				// {"message":"Queue_API_Start_Eloan_Retrial_934 does not exist.","errorCode":1002,"resourceIds":null}
				if (resultJson.containsKey("errorCode")
						&& Util.notEquals(
								resultJson.optString("errorCode", ""), "")) {

					// if (true) {

					errorMsg = "啟動RPA失敗=>" + (areaName + ":" + jsonStr) + "->"
							+ resultJson.toString();
					LOGGER.info(errorMsg);

					rpaExeErr.append(
							(Util.equals(rpaExeErr.toString(), "") ? "" : "、"))
							.append(areaName + ":" + jsonStr);
				} else {
					rpaExeOk.append(
							(Util.equals(rpaExeOk.toString(), "") ? "" : "、"))
							.append(areaName + ":" + jsonStr);

				}

				LOGGER.info("啟動RPA結束=>" + SysParamConstants.RPA_啟動覆審 + qName
						+ ":" + jsonStr);
			} catch (Exception e) {
				errorMsg = "RPA Job建立失敗，請稍後再試。" + e.toString();
				LOGGER.info(errorMsg);
			}
		} catch (Exception e) {
			errorMsg = "RPA Job建立失敗，請稍後再試。" + e.toString();
			LOGGER.info(errorMsg);
		}
		return errorMsg;
	}

	boolean chkRpaExeOwnerBrId(String exeUnit) {
		boolean canDo = false;

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		// LMS_RPA_RETRIAL_EXE_OWNER 931,932,933,934,935,007,201
		// 覆審名單OWNERBRID可以執行RPA
		String LMS_RPA_RETRIAL_EXE_OWNER = Util.trim(lmsService
				.getSysParamDataValue("LMS_RPA_RETRIAL_EXE_OWNER"));

		String countryType = Util.trim(branchService.getBranch(exeUnit)
				.getCountryType());

		if (Util.notEquals(LMS_RPA_RETRIAL_EXE_OWNER, "")) {

			for (String xx : LMS_RPA_RETRIAL_EXE_OWNER.split(",")) {
				if (Util.equals(xx, exeUnit) || Util.equals(xx, countryType)) {
					canDo = true;
					break;
				}
			}

		}

		return canDo;

	}

	private static Map<String, String> sortByComparator(
			Map<String, String> unsortMap) {

		List<Entry<String, String>> list = new LinkedList<Entry<String, String>>(
				unsortMap.entrySet());

		// Sorting the list based on values
		Collections.sort(list, new Comparator<Entry<String, String>>() {
			public int compare(Entry<String, String> o1,
					Entry<String, String> o2) {
				String brNo_o1 = o1.getKey().split(":")[0];
				String retrialDate_o1 = o1.getKey().split(":")[1];

				String brNo_o2 = o2.getKey().split(":")[0];
				String retrialDate_o2 = o2.getKey().split(":")[1];

				// 由早到晚排序
				boolean result = LMSUtil.cmpDate(
						Util.parseDate(retrialDate_o1), "<",
						Util.parseDate(retrialDate_o2));
				// return o1.getValue().compareTo(o2.getValue());
				return result ? -1 : 1;

			}
		});

		// Maintaining insertion order with the help of LinkedList
		Map<String, String> sortedMap = new LinkedHashMap<String, String>();
		for (Entry<String, String> entry : list) {
			sortedMap.put(entry.getKey(), entry.getValue());
		}

		return sortedMap;
	}

}
