package com.mega.eloan.lms.las.report.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import tw.com.jcs.common.report.ReportGenerator;

import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.AbstractReportService;
import com.mega.eloan.lms.las.report.AbstractLasReportMergeService;
import com.mega.eloan.lms.las.report.LMS1905R01RptService;
import com.mega.eloan.lms.las.report.LMS1915R01RptService;
import com.mega.eloan.lms.las.report.LMS1925R01RptService;
import com.mega.eloan.lms.las.report.LMS1935R01RptService;
import com.mega.eloan.lms.las.service.LMS1905Service;
import com.mega.eloan.lms.model.L192M01A;

/**
 * 合併列印工作底稿基本內容
 * 
 * <AUTHOR>
 * 
 */
@Service("lms1935r01rptservice")
public class LMS1935R01RptServiceImpl extends AbstractLasReportMergeService
		implements LMS1935R01RptService {

	@Resource
	LMS1905R01RptService lms1905r01RptService;

	@Resource
	LMS1925R01RptService lms1925r01RptService;
	
	@Resource
	LMS1915R01RptService lms1915r01RptService;

	@Resource
	LMS1905Service lms1905Service;

	@Override
	public boolean showPaginate() {
		return false;
	}

	@Override
	public void setLasReportData(ReportGenerator rptGenerator, String mainOid,
			String shtType) {

		if (UtilConstants.ShtType.房貸業務工作底稿.equals(shtType)) {
			rptGenerator.setReportFile(((AbstractReportService)lms1905r01RptService).getReportTemplateFileName());
			lms1905r01RptService.setReport001(rptGenerator, mainOid);

		} else if (UtilConstants.ShtType.授信業務工作底稿.equals(shtType)) {
			rptGenerator.setReportFile(((AbstractReportService)lms1925r01RptService).getReportTemplateFileName());
			lms1925r01RptService.setReport001(rptGenerator, mainOid);

		} else if(UtilConstants.ShtType.團體消貸工作底稿.equals(shtType)) {
			rptGenerator.setReportFile(((AbstractReportService)lms1915r01RptService).getReportTemplateFileName());
			lms1915r01RptService.setReport001(rptGenerator, mainOid);
		}
	}

	@Override
	public L192M01A getLasData(String mainOid) {
		return lms1905Service.getL192M01A(mainOid);
	}

}
