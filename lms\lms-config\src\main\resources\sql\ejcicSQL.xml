<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:util="http://www.springframework.org/schema/util"
	xsi:schemaLocation="
http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.0.xsd
http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-2.0.xsd">

	<util:map id="ejcicSql" map-class="java.util.HashMap" key-type="java.lang.String" >
		<!-- MIS.DATADATE 資料日期 -->
		<entry key="MIS.DATADATE.getData">
			<value>
				select QDATE,DATADATE from MIS.Datadate where ID = ? AND PRODID = ? and itemname='BAI001'
				order by QDATE desc,DATADATE desc
			</value>
		</entry>
		<!-- MIS.DATADATE 資料日期
		<entry key="MIS.DATADATE.getDataDate">
			<value>
				select datadate from MIS.Datadate where ID = ? AND PRODID = 'P7'
				and itemname='BAI001'
				order by datadate desc
			</value>
		</entry> 在 2019-10清查，未使用   -->
		<!-- MIS.AAS003 資料查詢日期 -->
		<entry key="MIS.AAS003.getQDate">
			<value>
				select QDATE from MIS.AAS003 where ID = ? AND PRODID = ?
				order by qdate desc
			</value>
		</entry>
		
		<!-- MIS.BAM095資料 用途別-->
		<entry key="MIS.BAM095.PURPOSE_CODE">
			<value>
				select count(ID) as Counts from MIS.BAM095 
				where ID = ? AND QDATE=? and PRODID = ? and PURPOSE_CODE = '1'
			</value>
		</entry>
		<!-- 
		聯徵查詢月份前一月(或兩個月)之無擔保授信餘額(仟元)	d07_ln_nos_tamt 
		-->
		<entry key="MIS.BAM095.D07_G_V_1_3">
			<value>
				select sum(LOAN_AMT + PASS_DUE_AMT) as LoanAmt from MIS.BAM095 
				where  ID = ? AND QDATE=? AND  prodid = ?
	  			and account_code2 != 'S' and account_code != 'Z'
			</value>
		</entry>
		<!-- D07因子 -->
		<entry key="MIS.BAM095.D07_G_V_2_0">
			<value>
				WITH t1 AS (select ID ,MAX(QDATE) AS QDATE from MIS.Datadate 
				where ID =? AND QDATE=?  AND PRODID = ? and itemname='BAI001' GROUP BY ID)
				,t2 as (
					select b.id
       				,b.qdate
       				,sum(case  
                			when ACCOUNT_CODE2 = 'S' then 0 
                			when account_code = 'Z' then 0 
                			when not( ACCOUNT_CODE2 != 'S' and account_code != 'Z' ) 
            			then 0 
                		else (LOAN_AMT+PASS_DUE_AMT)  end) as d07_ln_nos_tamt
					from   t1    as b
					left join  MIS.bam095 as a on  a.id = b.id and    a.qdate = b.qdate  and    prodid=?
 					group by b.id, b.qdate 
				)
				select t1.id
       			,t1.qdate 
       			,d07_ln_nos_tamt AS LoanAmt
				from   t1 left join  t2  on t1.id= t2.id and t1.qdate= t2.qdate
			</value>
		</entry>
		
		<!-- BAM095 -->
		<entry key="MIS.BAM095.getData">
			<value><![CDATA[
				select * from MIS.BAM095 where ID = ? and PRODID = ? AND QDATE=? ]]>
			</value>
		</entry>
		
		<!-- BAM095 擔保品類別-->
		<entry key="MIS.BAM095.IS_KIND">
			<value><![CDATA[
				select IS_KIND from MIS.BAM095 where ID = ? and PRODID = ? AND QDATE=? ]]>
			</value>
		</entry>
		
		<!-- MIS.AAS003 查詢日之相關日期 
		<entry key="MIS.AAS003.getDate">
			<value>
				select char(tdate) as tdate, char(tdate - 1 months) as t1,
				  char(tdate - 2 months) as t2, char(tdate - 3 months) as t3,
				  char(tdate - 6 months) as t6, char(tdate - 7 months) as t7,
				  char(tdate - 11 months) as t11, char(tdate - 12 months) as t12
				from ( 
				  select date(char(rtrim(char(int(left(MAX(QDATE),3))+1911))  
				    || '-' || substr(MAX(QDATE),5,2)  
				    || '-' || (right(MAX(QDATE),2)))) as tdate
				  from MIS.AAS003 where ID = ? and PRODID = 'P7' 
				) as tab1
			</value>
		</entry> 在 2019-10清查，未使用   -->
		<!-- KRM040 建檔期間資料 -->
		<entry key="MIS.KRM040.getData">
			<value><![CDATA[
				select * from MIS.KRM040 where ID = ? and PRODID = ? 
	  			and BILL_DATE between ? and ? ]]>
			</value>
		</entry>
		<!-- KRM040 近6個月平均的月信用卡循環信用 -->
		<entry key="MIS.KRM040.getAvgRev_G_V_1_3">
			<value>
				select avg(revol_bal) as RevolBal from ( 
				  select CAST(LEFT(BILL_DATE,5) AS CHAR(5)) as BILL_MONTH, revol_bal 
				  from MIS.KRM040 
				  where ID = ? and PRODID = ? AND QDATE=?  
				    and ISSUE != 'TOT' and revol_bal >= 0  
				    and BILL_DATE between ? and ?
				) as T1
			</value>
		</entry>
		<!-- 聯徵查詢月份and前兩月之信用卡循環信用(最新兩期) -->
		<entry key="MIS.KRM040.getRevolBalByDebtRate">
			<value><![CDATA[
				with tmp_01 as (
				   select ROW_NUMBER() OVER (PARTITION BY IDN_BAN, ISSUE, BILL_MARK ORDER BY BILL_DATE DESC ) AS SEQ , *
				   from MIS.KRM040 as a
				   where a.ID = ?  and a.qdate = ?
				   and a.prodid = ?
				   and issue != 'TOT'
				   and SUBSTR(replace(char(DATE(rtrim(char(int(SUBSTR(A.QDATE,1,3))+1911))||'-'||SUBSTR(A.QDATE,5,2)||'-'||SUBSTR(A.QDATE,8,2))-2 MONTH),'-',''),1,6)   
						<=  rtrim(Char(INT(SUBSTR(A.BILL_DATE,1,3))+1911))|| SUBSTR(A.BILL_DATE,4,2) 
				)
			    select NVL(SUM(CASE WHEN ISSUE = '017' then revol_bal end), 0) AS revol_bal_017
					   ,NVL(SUM(CASE WHEN ISSUE <> '017' then revol_bal end), 0) AS revol_bal_other
					   ,NVL(SUM(PRE_OWED), 0) AS pre_owed
			    from tmp_01 a
				where SEQ = 1
			]]></value>
		</entry>
		<!-- (avg 有問題)  
		<entry key="MIS.KRM040.getAvgRev2">
			<value>
				select sum(revol_bal)/count(1) as RevolBal from ( 
				  select CAST(LEFT(BILL_DATE,5) AS CHAR(5)) as BILL_MONTH, revol_bal 
				  from MIS.KRM040 
				  where ID = ? and PRODID = 'P7'  
				    and ISSUE != 'TOT' and revol_bal >= 0  
				    and BILL_DATE between ? and ?
				) as T1
			</value>
		</entry> 在 2019-10清查，未使用  -->
		<!-- 12個月新業務申請查詢總家數 
		<entry key="MIS.STM022.getBankData">
			<value><![CDATA[
				select distinct id, qdate, substr(bank_code,1,3) as bank_code, inq_purpose_1 from MIS.STM022  
	  			where id = ? and qdate = ? and prodid = 'P7' and inq_purpose_1 = '1'
	    		and (QUERY_DATE < ? or (QUERY_DATE >= ? and substr(bank_code,1,3) != '017'))
				and QUERY_DATE between ? and ?
			]]></value>
		</entry> 在 2019-10清查，未使用  -->
		<!-- 12個月新業務申請查詢總家數 -->
		<entry key="MIS.STM022.getBankData2">
			<value><![CDATA[
				
				select sum(CNT) AS N06 from (
					select substr(bank_code,1,3),INQ_PURPOSE_1 ,(case when inq_purpose_1 = '1' then 1 else 0 end) as CNT 
					from mis.stm022
					where id  = ?
					and qdate = ?
					and prodid = ?					
					and (QUERY_DATE<? OR (QUERY_DATE>=? AND substr(bank_code,1,3) <> '017'))
					group by substr(bank_code,1,3),INQ_PURPOSE_1
				)t 
			]]></value>
		</entry>
		<!-- N06因子 -->
		<entry key="MIS.STM022.N06_Q_V_3_0">
			<!--
			QUERY_DATE = ' 991207' 可能第1碼是空白 , 所以在轉成數字之前, 有加上 trim
			QDATE = '108/10/16', 但在912 的環境, 是以西元年月日呈現 。在和 912 核對資料時, 要調整SQL的 substr 的位置
				【912】, ((INT(substr(tmp.qdate, 1,4))*12+INT(substr(tmp.qdate, 6,2)))-(INT(trim(substr(query_date, 1,3))+1911)*12+ INT(substr(query_date,4 ,2)))) as diff_month
			在計算  diff_month 時，用 (年*12+月) 的邏輯，再去把兩個日期相減			  
			-->
			<value><![CDATA[				
				with  t1 AS (select ID ,MAX(QDATE) AS QDATE from MIS.Datadate 
					where ID =? AND QDATE=?  AND PRODID = ? and itemname='BAI001' GROUP BY ID
				)
				,t2 as (
 					select  id, qdate, substr(bank_code,1,3) bank ,INQ_PURPOSE_1 
			   		,(case when inq_purpose_1 in ('1','3','5','7') and (diff_month> 3 or (diff_month<=3 AND substr(bank_code,1,3) <> '017')) then 1 else 0 end) as CNT  from 
			   		(
			      		select t1.id, t1.qdate, tmp.bank_code, tmp.INQ_PURPOSE_1    
			        , ((INT(substr(tmp.qdate, 1,3))*12+INT(substr(tmp.qdate, 5,2)))-(INT(trim(substr(query_date, 1,3)))*12+ INT(substr(query_date, 4,2)))) as diff_month
			      	from mis.stm022 tmp ,t1
			      	where   tmp.ID = t1.id and tmp.qdate =t1.qdate and  tmp.prodid = ?
			      	) tmp2 
				)
				,t3 as (
					select id, qdate , cnt,bank
					, ROW_NUMBER() OVER (PARTITION BY id, qdate, bank ORDER BY cnt  DESC ) as bankrank 
				 	from t2 		
				)  
				select t1.id, t1.qdate , sum(cnt) as n06 
				from t1
				left join  t3  on t1.id= t3.id and t1.qdate= t3.qdate and bankrank =1 
				group by t1.id, t1.qdate  
			]]></value>
		</entry>
		<!-- N18因子 -->
		<entry key="MIS.STM022.get_N18_data">
			<value><![CDATA[				
				select * 
				from mis.stm022
				where id  = ? and qdate = ?
				and prodid = ?
			]]></value>
		</entry>
		
		<!-- 近12個月信用卡(每筆)循環信用平均使用率
		<entry key="MIS.KRM040.getRevolRate">
			<value>
				select IDN_BAN, QDATE , 
					avg(case when (REVOL_BAL=0 ) then 0 
	    				when (REVOL_BAL>0 and PERM_LIMIT>0 ) then REVOL_BAL/(PERM_LIMIT*1000) 
	    				when (REVOL_BAL  is null ) then null 
	    				when (PERM_LIMIT is null ) then null else 99 end
					) as RevolRate
				from MIS.KRM040 
				where ID = ? and QDATE = ? and PRODID = ? and ISSUE != 'TOT' 
				group by IDN_BAN, QDATE
			</value>
		</entry> 在 2019-10清查，未使用 -->
		<!-- R01因子 -->
		<entry key="MIS.KRM040.getRevolRate2_G_1_3"> <!-- avg 有問題 -->
			<value>
				select IDN_BAN, QDATE , 
					sum(DOUBLE(case when (REVOL_BAL=0 ) then 0 
	    				when (REVOL_BAL>0 and PERM_LIMIT>0 ) then REVOL_BAL/(PERM_LIMIT*1000) 
	    				when (REVOL_BAL  is null ) then 0 
	    				when (PERM_LIMIT is null ) then 0 else 99 end
					))/count(1) as RevolRate
				from MIS.KRM040 
				where ID = ? and QDATE = ? and PRODID = ? and ISSUE != 'TOT' 
				group by IDN_BAN, QDATE
			</value>
		</entry>
		<!-- R01因子
			在2020-07-03 風控處反應, 以下的 noteId 非房貸模型3.0的 R01 因子異常
				b63150e6dd4d4b89b916f9fcda3a349d	.25		-182.319(eloan_scr)	should -21.7477	=> 原始數值 0.25004597 
				aa59a7d88ab340968a6b993d7054f32a 	.05		-21.7477(eloan_scr)	should 6.6279	=> 原始數值 0.05001786
				46816d1f704442a09eb7e616dcf9b77f	.05		-21.7477(eloan_scr)	should 6.6279
 			切分的門檻為 0.05 與 0.25 => 原始數值可能是 0.25004597 或 0.24995500, 但因 四捨五入之後, 上傳到 DW 只有看到 0.25 而已
			為避免比對異常，由 DB 取值，就先進行 round => 再以 round 後的值，去比對門檻
		-->
		<entry key="MIS.KRM040.R01_Q_V_3_0">
			<value><![CDATA[
				select IDN_BAN, QDATE , 
					decimal(round( avg(case when (REVOL_BAL=0 ) then 0 
                   			when (REVOL_BAL>0 and PERM_LIMIT>0 ) and REVOL_BAL/(PERM_LIMIT*1000)<=1  then REVOL_BAL/(PERM_LIMIT*1000) 
                   			when (REVOL_BAL>0 and PERM_LIMIT>0 ) and REVOL_BAL/(PERM_LIMIT*1000)> 1 then 1
							when (REVOL_BAL  is null ) then null
							when (PERM_LIMIT is null ) then null else 1 end),  4), 10, 4)   as RevolRate
				from MIS.KRM040 
				where ID = ? and PRODID =? and QDATE = ? and ISSUE != 'TOT' 
				group by IDN_BAN, QDATE
			]]></value>
		</entry>
		<!-- 聯徵查詢月份前一月之信用卡循環信用使用率
		<entry key="MIS.KRM040.getRevolRatio">
			<value>
				select 
				  case when BAL = 0 then 0 
				  	when BAL >0 and QTA >0 then BAL/QTA/1000 
				  	when BAL is NULL then NULL 
				  	when QTA is NULL then NULL ELSE 99 end as tdata,
				  bal, qta
				from ( 
				  select double(sum(REVOL_BAL)) as bal, 
				  	double(sum(case when REVOL_BAL > 0 then PERM_LIMIT else 0 end)) AS qta 
				  from MIS.KRM040 
				  where ID = ? and PRODID = 'P7' and ISSUE != 'TOT' 
				  and substr(BILL_DATE,1,5) >= ? 
				) as t1
			</value>
		</entry> 在 2019-10清查，未使用  -->
		<!-- 聯徵查詢月份前一月之信用卡循環信用使用率2 -->
		<entry key="MIS.KRM040.getRevolRatio2_G_V_1_3">
			<value><![CDATA[
				with tmp_02 as (
				   select a.idn_ban,a.qdate,
				          sum(case when revol_bal >= 0  then revol_bal else null end)       as revol_bal_1 ,  /*分子 */
				          sum(case when REVOL_BAL >  0  then PERM_LIMIT*1000 else null end) as perm_limit_1  /* 分母 */
				   from MIS.KRM040 as a
				   where a.ID = ? and a.qdate = ?
				   and a.prodid = ?
				   and issue != 'TOT'
				   and SUBSTR(replace(char(DATE(rtrim(char(int(SUBSTR(A.QDATE,1,3))+1911))||'-'||SUBSTR(A.QDATE,5,2)||'-'||SUBSTR(A.QDATE,8,2))-1 MONTH),'-',''),1,6) <=    
					rtrim(Char(INT(SUBSTR(A.BILL_DATE,1,3))+1911))||  
					SUBSTR(A.BILL_DATE,4,2) 
				   group by a.idn_ban,a.qdate
				)
			    select a.idn_ban, a.qdate, a.revol_bal_1, a.perm_limit_1,
		          decimal(round((case when revol_bal_1 = 0 then 0
		                        when revol_bal_1 > 0 and perm_limit_1 > 0 then double(revol_bal_1)/perm_limit_1
		                        when revol_bal_1  is null then null
		                        when perm_limit_1 is null then null 
		                   else 99 end),4),10,4) as r10_cc_revol_ratio
			     from tmp_02 as a
			]]></value>
		</entry>
		<!-- 近6個月信用卡繳款狀況出現全額繳清無延遲次數
		<entry key="MIS.KRM040.getSix_PcodeAtimes">
			<value>
				select count(ID) as Counts,max(ID) as id from MIS.KRM040 
				where ID = ? and PRODID = 'P7'
				and pay_stat = '1' and pay_code = 'N' 
				and BILL_DATE between ? and ?
			</value>
		</entry> 在 2019-10清查，未使用  -->
		<!-- 近6個月信用卡繳款狀況出現全額繳清無延遲次數
		<entry key="MIS.KRM040.getSix_PcodeAtimes2">
			<value><![CDATA[
				SELECT A.IDN_BAN, A.QDATE, (SUM(CASE WHEN PAY_STAT='1' AND PAY_CODE='N' THEN 1 ELSE 0 END)) AS Counts FROM MIS.KRM040 AS A 
				WHERE A.IDN_BAN = ? AND PRODID = 'P7' AND ISSUE != 'TOT' AND SUBSTR(replace(char(DATE(rtrim(char(int(SUBSTR(A.QDATE,1,3))+1911))||'-'||SUBSTR(A.QDATE,5,2)||'-'||SUBSTR(A.QDATE,8,2))-6 MONTH),'-',''),1,6) <= rtrim(Char(INT(SUBSTR(A.BILL_DATE,1,3))+1911)) || SUBSTR(A.BILL_DATE,4,2)
				GROUP BY  A.IDN_BAN, A.QDATE
			]]></value>
		</entry> 在 2019-10清查，未使用  -->
		<!-- 近6個月信用卡繳款狀況出現全額繳清無延遲次數 -->
		<entry key="MIS.KRM040.getP25_G_V_1_3">
			<value><![CDATA[
				WITH tmp_02 as (
   select a.idn_ban,
          a.qdate,
          (sum(case when PAY_STAT='1' and PAY_CODE='N' then 1 else 0 end)) as p25
   from   MIS.krm040      as a
   where  a.idn_ban = ?
   and    a.qdate   = ?
   and    prodid = ?
   and    issue <> 'TOT'
   and      SUBSTR(replace(char(DATE(rtrim(char(int(SUBSTR(A.QDATE,1,3))+1911))||'-'||SUBSTR(A.QDATE,5,2)||'-'||SUBSTR(A.QDATE,8,2))-6 MONTH),'-',''),1,6) <=    
  rtrim(Char(INT(SUBSTR(A.BILL_DATE,1,3))+1911))||  
  SUBSTR(A.BILL_DATE,4,2)
   group by 
          a.idn_ban,
          a.qdate
), 
tmp_03 as (
  select idn_ban, qdate, p25,
         (case when (p25 < 0.5 and p25 <= 2.5 ) then  -8.3019
               when (p25 < 2.5 and p25 >  5.5 ) then   1.5218
               when (p25 > 5.5 ) then 14.0523
               when (p25 is null) then -9.2947 else 999999 end) as p25_score
  from   tmp_02
),
tmp_04 as (
  select distinct idn_ban,qdate,0 as p25_t
     from   MIS.KRM040      as a
   where  a.idn_ban = ?
   and    a.qdate   = ?
   and    a.prodid = ?
  )
  select  a.idn_ban
         ,a.qdate
         ,(case when b.p25 is null then p25_t else b.p25 end) as p25
         ,(case when b.p25 is null then -14.1623 else b.p25_score end) as p25_score
  from tmp_04 a left join tmp_03 b on 1=1
			]]></value>
		</entry>
		<!-- P25因子 -->
		<entry key="MIS.KRM040.getP25_G_V_2_0">
			<value><![CDATA[
				WITH TMP_01 AS
				(select ID AS S_ID,MAX(QDATE) AS S_DATE from MIS.Datadate 
				  where ID = ? AND QDATE=? AND PRODID = ? and itemname='BAI001' GROUP BY ID),
				TMP_02 AS 
				(
				   SELECT A.IDN_BAN,
				          A.QDATE,
				          (SUM(CASE WHEN PAY_STAT='1' and PAY_CODE='N' 
						  			and	SUBSTR(replace(char(DATE(rtrim(char(int(SUBSTR(A.QDATE,1,3))+1911))||'-'||SUBSTR(A.QDATE,5,2)||'-'||SUBSTR(A.QDATE,8,2))-6 MONTH),'-',''),1,6) <=    
  										rtrim(Char(INT(SUBSTR(A.BILL_DATE,1,3))+1911))|| SUBSTR(A.BILL_DATE,4,2)
									  THEN 1							
				                    ELSE 0 END))     AS P25
				   FROM   TMP_01   AS B
				   LEFT JOIN MIS.KRM040 AS A
				   ON a.idn_ban = b.s_id
   						and    a.qdate   = b.s_date
						and    prodid = ?
   						and    issue <> 'TOT'
   					group by a.idn_ban, a.qdate
				)
				SELECT S_ID,S_DATE, P25				     
				FROM   TMP_01 A LEFT JOIN TMP_02 B ON S_ID = B.IDN_BAN AND S_DATE = B.QDATE
			]]></value>
		</entry>
		
		<!-- 近12個月信用卡繳款狀況出現不良繳款紀錄或使用循環信用的次數
		<entry key="MIS.KRM040.getNot_PcodeAtimes">
			<value>
				select count(ID) as Counts,max(ID) as id from MIS.KRM040 
				where ID = ? and PRODID = 'P7' 
	  			and (pay_stat = '2' or pay_stat = '3' or pay_stat = '4') 
	  			and BILL_DATE between ? and ?
			</value>
		</entry> 在 2019-10清查，未使用  -->
		<!-- 近12個月信用卡繳款狀況出現全額繳清無延遲次數
		<entry key="MIS.KRM040.getPcodeAtimes">
			<value>
				select count(ID) as Counts,max(ID) as id from MIS.KRM040 
				where ID = ? and PRODID = 'P7'
	  			and pay_stat ='1' and pay_code ='N' 
	  			and BILL_DATE between ? and ?
			</value>
		</entry> 在 2019-10清查，未使用  -->
		<!-- 近12個月信用卡繳款狀況出現不良繳款紀錄或使用循環信用的次數 & 近12個月信用卡繳款狀況出現全額繳清無延遲次數 -->
		<entry key="MIS.KRM040.getP69P19">
			<value>
				select a.idn_ban,a.qdate,
		          (sum(case when PAY_STAT='1' and PAY_CODE='N' then 1 else 0 end)) as p19,
		          (sum(case when PAY_STAT in ('2','3','4') then 1 else 0 end))     as p69
				from MIS.KRM040 as a
				where a.idn_ban = ? and a.qdate = ?
				  and prodid = ? and issue != 'TOT'
				group by a.idn_ban,a.qdate
		  </value>
		</entry>
		
		<!-- MIS.KRM001 強停註記 -->
		<entry key="MIS.KRM001.getStopCreditCard">
			<value>
				select * from MIS.KRM001 
				where ID = ? and PRODID = ? AND QDATE=?
				and STOPCODE ='3'
			</value>
		</entry>
		<!-- MIS.BAM101 授信額度-->
		<entry key="MIS.BAM101.getCollectionLog">
			<value>
				select * from MIS.BAM101 
				where ID = ? and PRODID = ?  AND QDATE=?
				and DOUBLE(COLLECTION_AMT) + DOUBLE(BAD_DEBT) >0
			</value>
		</entry>
		<!-- MIS.BAM101 授信額度，查聯徵有無[逾期金額PASS_DUE_AMT]-->
		<entry key="MIS.BAM101.getPassDueAmt">
			<value>
				select * from MIS.BAM101 
				where ID = ? and PRODID = ?  AND QDATE=?
				and DOUBLE(PASS_DUE_AMT) >0
			</value>
		</entry>
		<!-- MIS.VAM106 消債條例信用註記資訊 -->
		<entry key="MIS.VAM106.getData">
			<value>
				select * from MIS.VAM106 
				where ID = ? and PRODID = ?
	  			<!--and MAINCODE IN ('A','B','C','7','8')-->
				and MAINCODE NOT IN ('D')
				and QDATE = ?
			</value>
		</entry>
		<!-- MIS.VAM107 銀行公會消金案件債務協商補充註記 -->
		<entry key="MIS.VAM107.getData">
			<value>
				select * from MIS.VAM107 
				where ID = ? and PRODID = ?
	  			<!--and MAINCODE IN ('A','B','C','7','8')-->
				and MAINCODE NOT IN ('D')
				and QDATE = ?
			</value>
		</entry>
		<!-- MIS.VAM108 非屬消債條例及銀行公會債務協商之註記資訊 -->
		<entry key="MIS.VAM108.getData">
			<value>
				select * from MIS.VAM108 
				where ID = ? and PRODID = ?
	  			<!--and MAINCODE IN ('A','B','C','7','8')-->
				and MAINCODE NOT IN ('D')
				and QDATE = ?
			</value>
		</entry>
		<!-- MIS.BAM087 近12個月授信帳戶有遲延還款紀錄
		<entry key="MIS.BAM087.getDelayPayLoan">
			<value>
				select min(PCODE) as PCODE, count(*) as Counts from ( 
				  select CASE WHEN substr(PAY_CODE_12, 1,1) > '1' THEN 'C'  ELSE 'D' END AS PCODE 
				  from MIS.BAM087 where id = ? and prodid = 'P7' and substr(PAY_CODE_12, 1,1) IN ('A','B','1','2','3','4','5','6') 
				  union all 
				  select CASE WHEN substr(PAY_CODE_12, 2,1) > '1' THEN 'C'  ELSE 'D' END AS PCODE 
				  from MIS.BAM087 where id = ? and prodid = 'P7' and substr(PAY_CODE_12, 2,1) IN ('A','B','1','2','3','4','5','6')
				  union all 
				  select CASE WHEN substr(PAY_CODE_12, 3,1) > '1' THEN 'C'  ELSE 'D' END AS PCODE 
				  from MIS.BAM087 where id = ? and prodid = 'P7' and substr(PAY_CODE_12, 3,1) IN ('A','B','1','2','3','4','5','6')
				  union all 
				  select CASE WHEN substr(PAY_CODE_12, 4,1) > '1' THEN 'C'  ELSE 'D' END AS PCODE 
				  from MIS.BAM087 where id = ? and prodid = 'P7' and substr(PAY_CODE_12, 4,1) IN ('A','B','1','2','3','4','5','6')
				  union all 
				  select CASE WHEN substr(PAY_CODE_12, 5,1) > '1' THEN 'C'  ELSE 'D' END AS PCODE 
				  from MIS.BAM087 where id = ? and prodid = 'P7' and substr(PAY_CODE_12, 5,1) IN ('A','B','1','2','3','4','5','6')
				  union all 
				  select CASE WHEN substr(PAY_CODE_12, 6,1) > '1' THEN 'C'  ELSE 'D' END AS PCODE 
				  from MIS.BAM087 where id = ? and prodid = 'P7' and substr(PAY_CODE_12, 6,1) IN ('A','B','1','2','3','4','5','6')
				  union all 
				  select CASE WHEN substr(PAY_CODE_12, 7,1) > '1' THEN 'C'  ELSE 'D' END AS PCODE 
				  from MIS.BAM087 where id = ? and prodid = 'P7' and substr(PAY_CODE_12, 7,1) IN ('A','B','1','2','3','4','5','6')
				  union all 
				  select CASE WHEN substr(PAY_CODE_12, 8,1) > '1' THEN 'C'  ELSE 'D' END AS PCODE 
				  from MIS.BAM087 where id = ? and prodid = 'P7' and substr(PAY_CODE_12, 8,1) IN ('A','B','1','2','3','4','5','6')
				  union all 
				  select CASE WHEN substr(PAY_CODE_12, 9,1) > '1' THEN 'C'  ELSE 'D' END AS PCODE 
				  from MIS.BAM087 where id = ? and prodid = 'P7' and substr(PAY_CODE_12, 9,1) IN ('A','B','1','2','3','4','5','6')
				  union all 
				  select CASE WHEN substr(PAY_CODE_12,10,1) > '1' THEN 'C'  ELSE 'D' END AS PCODE 
				  from MIS.BAM087 where id = ? and prodid = 'P7' and substr(PAY_CODE_12,10,1) IN ('A','B','1','2','3','4','5','6')
				  union all 
				  select CASE WHEN substr(PAY_CODE_12,11,1) > '1' THEN 'C'  ELSE 'D' END AS PCODE 
				  from MIS.BAM087 where id = ? and prodid = 'P7' and substr(PAY_CODE_12,11,1) IN ('A','B','1','2','3','4','5','6')
				  union all 
				  select CASE WHEN substr(PAY_CODE_12,12,1) > '1' THEN 'C'  ELSE 'D' END AS PCODE 
				  from MIS.BAM087 where id = ? and prodid = 'P7' and substr(PAY_CODE_12,12,1) IN ('A','B','1','2','3','4','5','6') 
				) AS T
			</value>
		</entry> 在 2019-10清查，未使用  -->
		<!-- MIS.BAM087 近12個月現金卡有動用紀錄-->
		<entry key="MIS.BAM087.getDelayPayLoan2">
			<value>
				select PAY_CODE_12 from MIS.BAM087 where id = ? and  prodid = ? AND QDATE=?
			</value>
		</entry>
		<!-- MIS.KRM040 pStat :2  近12個月信用卡繳款狀況出現(循環信用有延遲)2次(含)以上 -->
		<entry key="MIS.KRM040.getCardPayCode2">
			<value>
				select ID from MIS.KRM040 
				where ID = ? and PRODID = ?  AND QDATE=?
	  			and PAY_STAT = '2' 
	  			and PAY_CODE >= '1' and PAY_CODE != 'N' and PAY_CODE != 'X' 
	  			and BILL_DATE between ? and ? 
				group by ID HAVING COUNT(*) > 1
			</value>
		</entry>
		<!-- MIS.KRM040 pStat :3  近12個月信用卡繳款狀況出現(未繳足最低金額)2次(含)以上 -->
		<entry key="MIS.KRM040.getCardPayCode3">
			<value>
				select ID from MIS.KRM040 
				where ID = ? and PRODID =?  AND QDATE=?
				  and PAY_STAT = '3'
				  and BILL_DATE  between ? and ?
				group by ID HAVING COUNT(*) > 1
			</value>
		</entry>
		<!-- MIS.KRM040 pStat :4  近12個月信用卡繳款狀況出現(全額逾期未繳)2次(含)以上 -->
		<entry key="MIS.KRM040.getCardPayCode4">
			<value>
				select DISTINCT substr(bill_date,1,5)||'01' AS BILL_DATE1 
				from MIS.KRM040 
				where ID = ? and PRODID =?  AND QDATE=?
				  and PAY_STAT = '4' 
				  and BILL_DATE  between ? and ? 
				order by BILL_DATE1
			</value>
		</entry>
		<!-- MIS.KRM040 近12個月信用卡有預借現金餘額家數2家含 -->
		<entry key="MIS.KRM040.getCashAdvance">
			<value>
				select count(issue) as Counts from MIS.KRM040 
				where ID = ? and PRODID =? AND QDATE=?
				and CASH_LENT > 0 and ISSUE != 'TOT'
			</value>
		</entry>
		<!-- MIS.KRM040 近12個月不同銀行之信用卡繳款狀況出現(全額逾期未繳)次數 -->
		<entry key="MIS.KRM040.getCardPayDelayCountsByBank">
			<value>
				select count(ISSUE) as counts,ISSUE 
				from MIS.KRM040 
				where ID = ? and PRODID =?  AND QDATE=?
				  and PAY_STAT = '4' 
				  and BILL_DATE  between ? and ? 
				group by ISSUE
			</value>
		</entry>
		<!-- MIS.KRM040 近12個月指定銀行之信用卡全額逾期未繳資料 -->
		<entry key="MIS.KRM040.getCardPayDelayDataByBank">
			<value>
				select substr(bill_date,1,5)||'01' AS BILL_DATE1 
				from MIS.KRM040 
				where ID = ? and PRODID =?  AND QDATE=?
				  and PAY_STAT = '4' 
				  and BILL_DATE  between ? and ? 
				  and ISSUE = ?
				order by BILL_DATE1
			</value>
		</entry>
		
		<!-- MIS.KRM040 不同銀行之最近一期信用卡繳款日期 -->
		<entry key="MIS.KRM040.getCardPayLatestDateByBank">
			<value>
				select MAX(BILL_DATE) as LATEST_BILL_DATE, ISSUE
				from mis.KRM040 
				where ID = ? and PRODID = ? and QDATE = ? 
				  and ISSUE !='TOT'
				group by ISSUE
			</value>
		</entry>
		
		<!-- MIS.KRM040 指定銀行及信用卡繳款日期之資料有動用循環信用(REVOL_BAL>0)     //循環比率>0.5 and DOUBLE(REVOL_RATE) > 0.5 -->
		<entry key="MIS.KRM040.getCardPayLatestDataUseRevolByBank">
			<value>
				select *
				from mis.KRM040 
				where ID = ? and PRODID = ? and QDATE = ? 
				  and BILL_DATE =?
				  and ISSUE =?
				  and DOUBLE(REVOL_BAL) > 0
			</value>
		</entry>
		
		<!-- MIS.BAM087 近12個月現金卡有動用紀錄
		<entry key="MIS.BAM087.getCashCard">
			<value>
				select PCODE from ( 
				  select CASE WHEN substr(PAY_CODE_12, 1,1) is null THEN 'C'  ELSE 'D' END AS PCODE 
				  from MIS.BAM087 where id = ? and prodid = 'P7'
				  	and account_code = 'Y' and substr(PAY_CODE_12, 1,1) not IN ('X','')  
				  union
				  select CASE WHEN substr(PAY_CODE_12, 2,1) is null THEN 'C'  ELSE 'D' END AS PCODE 
				  from MIS.BAM087 where id = ? and prodid = 'P7'
				  	and account_code = 'Y' and substr(PAY_CODE_12, 2,1) not IN ('X','')
				  union
				  select CASE WHEN substr(PAY_CODE_12, 3,1) is null THEN 'C'  ELSE 'D' END AS PCODE 
				  from MIS.BAM087 where id = ? and prodid = 'P7'
				  	and account_code = 'Y' and substr(PAY_CODE_12, 3,1) not IN ('X','')
				  union
				  select CASE WHEN substr(PAY_CODE_12, 4,1) is null THEN 'C'  ELSE 'D' END AS PCODE 
				  from MIS.BAM087 where id = ? and prodid = 'P7'
				  	and account_code = 'Y' and substr(PAY_CODE_12, 4,1) not IN ('X','')
				  union
				  select CASE WHEN substr(PAY_CODE_12, 5,1) is null THEN 'C'  ELSE 'D' END AS PCODE 
				  from MIS.BAM087 where id = ? and prodid = 'P7'
				  	and account_code = 'Y' and substr(PAY_CODE_12, 5,1) not IN ('X','')
				  union
				  select CASE WHEN substr(PAY_CODE_12, 6,1) is null THEN 'C'  ELSE 'D' END AS PCODE 
				  from MIS.BAM087 where id = ? and prodid = 'P7'
				  	and account_code = 'Y' and substr(PAY_CODE_12, 6,1) not IN ('X','')
				  union
				  select CASE WHEN substr(PAY_CODE_12, 7,1) is null THEN 'C'  ELSE 'D' END AS PCODE 
				  from MIS.BAM087 where id = ? and prodid = 'P7'
				  	and account_code = 'Y' and substr(PAY_CODE_12, 7,1) not IN ('X','')
				  union
				  select CASE WHEN substr(PAY_CODE_12, 8,1) is null THEN 'C'  ELSE 'D' END AS PCODE 
				  from MIS.BAM087 where id = ? and prodid = 'P7'
				  	and account_code = 'Y' and substr(PAY_CODE_12, 8,1) not IN ('X','')
				  union
				  select CASE WHEN substr(PAY_CODE_12, 9,1) is null THEN 'C'  ELSE 'D' END AS PCODE 
				  from MIS.BAM087 where id = ? and prodid = 'P7'
				  	and account_code = 'Y' and substr(PAY_CODE_12, 9,1) not IN ('X','')
				  union
				  select CASE WHEN substr(PAY_CODE_12,10,1) is null THEN 'C'  ELSE 'D' END AS PCODE 
				  from MIS.BAM087 where id = ? and prodid = 'P7'
				  	and account_code = 'Y' and substr(PAY_CODE_12,10,1) not IN ('X','')
				  union
				  select CASE WHEN substr(PAY_CODE_12,11,1) is null THEN 'C'  ELSE 'D' END AS PCODE 
				  from MIS.BAM087 where id = ? and prodid = 'P7'
				  	and account_code = 'Y' and substr(PAY_CODE_12,11,1) not IN ('X','')
				  union
				  select CASE WHEN substr(PAY_CODE_12,12,1) is null THEN 'C'  ELSE 'D' END AS PCODE 
				  from MIS.BAM087 where id = ? and prodid = 'P7' 
				  	and account_code = 'Y' and substr(PAY_CODE_12,12,1) not IN ('X','')
				) AS T where PCODE ='D'
			</value>
		</entry> 在 2019-10清查，未使用  -->
		<!-- MIS.BAM087 近12個月現金卡有動用紀錄-->
		<entry key="MIS.BAM087.getCashCard2">
			<value>
				select PAY_CODE_12 from MIS.BAM087 where id = ? and prodid = ? AND QDATE=? and account_code = 'Y'
			</value>
		</entry>
		<!-- MIS.BAM087 個人主債務資料
		若查 BAM095(EJV41902)會多出欄位 CYCLE_FLAG －－－－－－－－－－  LMS.C101S01N 
		
		例子：同一 CONTRACT_CODE , CONTRACT_CODE1 
	                         但會因 ACCOUNT_CODE 不同，而有不同的 CYCLE_FLAG
					   
		ID,QDATE,ACCOUNT_CODE,CONTRACT_AMT1,CONTRACT_AMT, LOAN_AMT, CONTRACT_CODE       , CONTRACT_CODE1      , CYCLE_FLAG
						C           11000.       11000.        7351. F1249348210110100003 F1249348210110100003  Y         
           				I           11000.       11000.        2764. F1249348210110100003 F1249348210110100003  N         

		
		《105年度信用資訊產品講習》附-20 授信科目代號表
		-->
		<entry key="MIS.BAM087.getData">
			<value>
				select SUBSTR(T3.BANK_CODE,1,3) AS BC
				  ,T3.ACCOUNT_CODE AS ACCTCODE,T3.ACCOUNT_CODE2 AS ACCTCODE2
				  ,SUM(LOAN_AMT) AS TOT_LOAN,SUM(T3.CONTRACT_AMT1) AS TOT_CONT 
				from ( 
				  select * from ( 
				    select id,BANK_CODE,ACCOUNT_CODE,ACCOUNT_CODE2
				      ,MAX(PASS_DUE_AMT) as PASS_DUE_AMT,SUM(LOAN_AMT) AS LOAN_AMT
				      ,MAX(CONTRACT_AMT1) AS CONTRACT_AMT1 
				    from MIS.BAM087 
				    where CONTRACT_CODE != '99999999999999999999999999999999999999999999999999' 
				      and ID = ? and QDATE = ? and PRODID=? 
				      and SUBSTR(BANK_CODE,1,3)!='017' 
				      and ACCOUNT_CODE !='J' and ACCOUNT_CODE !='P' and ACCOUNT_CODE !='Q' 
				   group by id,BANK_CODE,ACCOUNT_CODE,ACCOUNT_CODE2,CONTRACT_CODE1
				  ) AS T1 
				  UNION ALL 
				  select * from (
				    select id,BANK_CODE,ACCOUNT_CODE,ACCOUNT_CODE2
				      ,SUM(PASS_DUE_AMT) as PASS_DUE_AMT,SUM(LOAN_AMT) AS LOAN_AMT
				      ,SUM(CONTRACT_AMT1) AS CONTRACT_AMT1 
				    from MIS.BAM087 
				    where CONTRACT_CODE = '99999999999999999999999999999999999999999999999999' 
				      and ID = ? and  QDATE = ? and PRODID =? 
				      and SUBSTR(BANK_CODE,1,3) != '017' 
				      and ACCOUNT_CODE !='J' and ACCOUNT_CODE !='P' and ACCOUNT_CODE !='Q' 
				    group by id,BANK_CODE,ACCOUNT_CODE,ACCOUNT_CODE2,CONTRACT_CODE 
				  )  AS T2 
				) AS T3 
				group by BANK_CODE,ACCOUNT_CODE,ACCOUNT_CODE2,CONTRACT_AMT1 
				order by TOT_LOAN desc
			</value>
		</entry>
		<!-- MIS.BAM087 個人主債務資料(改)xxxx
		<entry key="MIS.BAM087.getData2">
			<value>
				select * from ( 
				    select id,BANK_CODE,ACCOUNT_CODE,ACCOUNT_CODE2
				      ,MAX(PASS_DUE_AMT) as PASS_DUE_AMT,SUM(LOAN_AMT) AS LOAN_AMT
				      ,MAX(CONTRACT_AMT1) AS CONTRACT_AMT1 
				    from MIS.BAM087 
				    where ID = ? and QDATE = ? and PRODID='P7' 
				      and SUBSTR(BANK_CODE,1,3)!='017' 
				      and ACCOUNT_CODE !='J' and ACCOUNT_CODE !='P' and ACCOUNT_CODE !='Q' 
				   group by id,BANK_CODE,ACCOUNT_CODE,ACCOUNT_CODE2,CONTRACT_CODE1
				  ) AS T1
			</value>
		</entry> 在 2019-10清查，未使用  -->
		<!-- MIS.STM022 近一年內不含查詢當日非Z類被聯行查詢紀錄明細-->
		<entry key="MIS.STM022.getData">
			<value>
				select * from MIS.STM022 
				where ID = ?  and PRODID =? AND QDATE=? and substr(BANK_CODE,1,3) = '017'
			</value>
		</entry>
		<!-- MIS.BAM087  查詢申貸戶 所有逾期呆帳紀錄筆數-->
		<entry key="MIS.BAM087.getCollectionInfo1">
			<value>
				select SUM(PASS_DUE_AMT) as TOT_PASS,
				  SUM(T3.CONTRACT_AMT1) AS TOT_CONT ,SUM(LOAN_AMT) AS TOT_LOAN 
				from ( 
				  select * from (
				    select id,MAX(PASS_DUE_AMT) as PASS_DUE_AMT,
				      SUM(LOAN_AMT) AS LOAN_AMT,SUM(CONTRACT_AMT1) AS CONTRACT_AMT1 
				    from MIS.BAM087 s
				    where CONTRACT_CODE != '99999999999999999999999999999999999999999999999999' 
				      and ID = ? and PRODID =? AND QDATE=? and PASS_DUE_AMT > 0 
				    group by ID
				  ) AS T1 
				  union 
				  select * from ( 
				    select id,SUM(PASS_DUE_AMT) as PASS_DUE_AMT,
				      SUM(LOAN_AMT) AS LOAN_AMT,SUM(CONTRACT_AMT1) AS CONTRACT_AMT1 
				    from MIS.BAM087 
				    where CONTRACT_CODE = '99999999999999999999999999999999999999999999999999' 
				      and ID = ? and PRODID =? AND QDATE=? and PASS_DUE_AMT > 0 
				    group by ID
				  )  AS T2 
				) AS T3
			</value>
		</entry>
		<!-- MIS.BAM303 查詢所有董監事的從債務 逾期呆帳紀錄筆數
		<entry key="MIS.BAM303.getCollectionInfo1">
			<value>
				select ID,SUM(PASS_DUE_AMT) AS TOT_PASS from MIS.BAM303 
				where ID = ? and PRODID = 'P7' and PASS_DUE_AMT > 0 
				group by ID
			</value>
		</entry> 在 2019-10清查，未使用  -->
		<!-- MIS.KRM001 信用卡被強停查詢
		<entry key="MIS.KRM001.getCreditData2">
			<value>
				select ID,STOPDATE,STOPCODE,
					case when STOPDATE = '' then '0' else STOPDATE end as STOPDATE2 
				from MIS.KRM001 
				where ID = ? AND PRODID = 'P7' and STOPCODE = '3' 
				order by int(replace(STOPDATE2,'/','')) desc
			</value>
		</entry> 在 2019-10清查，未使用  -->
		<!-- MIS.KRM001 信用卡被強停查詢(改)
		<entry key="MIS.KRM001.getCreditData3">
			<value>
				select ID,STOPDATE,STOPCODE from MIS.KRM001 
				where ID = ? AND PRODID = 'P7' and STOPCODE = '3' 
				order by STOPDATE desc
			</value>
		</entry>在 2019-10清查，未使用  -->
		
		<!-- mis.CPXQueryLog 查詢結果HTML -->
		<entry key="mis.CPXQueryLog.getHtml">
			<value>
				select * from mis.CPXQueryLog
				where ID = ? and QDATE = ? and PRODID = ?
				order by TXID asc
			</value>
		</entry>
		
		<!-- 國內授信沿用徵信相關聯徵查詢start -->
		<entry key="DATADATE.findMaxDataDaㄒㄩteByIdAndPIdAndItNm">
			<value><![CDATA[SELECT DATADATE,PRODID,QDATE FROM MIS.DATADATE WHERE ID=:ID]]></value>
		</entry>
		<!-- 在【MisEJF419ServiceImpl】使用 -->
		<entry key="BAM087.findHeaderDatasBygId">
			<value><![CDATA[SELECT SUM(P) AS P,SUM(L) AS L,SUM(C) AS C FROM (SELECT * FROM 
			  (SELECT ID,SUBSTR(BANK_CODE,1,3) AS BC,CONTRACT_CODE1,SUM(PASS_DUE_AMT) AS P,SUM(LOAN_AMT) AS L,MAX(CONTRACT_AMT1) AS C 
			   FROM MIS.BAM087 WHERE CONTRACT_CODE1 <> '99999999999999999999999999999999999999999999999999' AND ID=? AND PRODID = 'P5' GROUP BY ID, SUBSTR(BANK_CODE,1,3), CONTRACT_CODE1) AS T1 
			UNION 
			 SELECT * FROM 
			  (SELECT ID,SUBSTR(BANK_CODE,1,3) AS BC,CONTRACT_CODE1,SUM(PASS_DUE_AMT) AS P,SUM(LOAN_AMT) AS L,SUM(CONTRACT_AMT1) AS C 
			   FROM MIS.BAM087 WHERE CONTRACT_CODE1 = '99999999999999999999999999999999999999999999999999' AND ID=? AND PRODID = 'P5' GROUP BY ID, SUBSTR(BANK_CODE,1,3), CONTRACT_CODE1) AS T2) AS T3 
			WHERE ID=?]]></value>
		</entry>
		<entry key="AAS103.findByBan">
			<value><![CDATA[SELECT BAN, PNAME FROM MIS.AAS103 WHERE BAN = ?]]></value>
		</entry>
		<entry key="BAM087.findCreditInfoI">
			<value><![CDATA[WITH T1 AS (Select ID,SUBSTR(BANK_CODE,1,3) AS BC,CONTRACT_CODE1,PASS_DUE_AMT,LOAN_AMT,CONTRACT_AMT1 
	FROM MIS.BAM087 WHERE ID=? AND (PRODID = 'P1' OR PRODID = 'P8') )
	SELECT BC, SUM(P) AS P, SUM(L) AS L, SUM(C) AS C
	from (select ID,BC,CONTRACT_CODE1,SUM(PASS_DUE_AMT) AS P, SUM(LOAN_AMT) AS L, MAX(CONTRACT_AMT1) AS C from T1 where CONTRACT_CODE1 <> '99999999999999999999999999999999999999999999999999' group by ID,BC,CONTRACT_CODE1
		union
		  select ID,BC,CONTRACT_CODE1,SUM(PASS_DUE_AMT) AS P, SUM(LOAN_AMT) AS L, SUM(CONTRACT_AMT1) AS C from T1 where CONTRACT_CODE1 = '99999999999999999999999999999999999999999999999999' group by ID,BC,CONTRACT_CODE1
	) T2 group by BC]]></value>
		</entry>
		<entry key="BAM087.findCreditInfoII">
			<value><![CDATA[WITH T1 AS (Select ID,SUBSTR(BANK_CODE,1,3) AS BC,CONTRACT_CODE1,PASS_DUE_AMT,LOAN_AMT,CONTRACT_AMT1 
	FROM MIS.BAM087 WHERE ID=? AND PRODID = 'P5' )
	SELECT BC, SUM(P) AS P, SUM(L) AS L, SUM(C) AS C
	from (select ID,BC,CONTRACT_CODE1,SUM(PASS_DUE_AMT) AS P, SUM(LOAN_AMT) AS L, MAX(CONTRACT_AMT1) AS C from T1 where CONTRACT_CODE1 <> '99999999999999999999999999999999999999999999999999' group by ID,BC,CONTRACT_CODE1
		union
		  select ID,BC,CONTRACT_CODE1,SUM(PASS_DUE_AMT) AS P, SUM(LOAN_AMT) AS L, SUM(CONTRACT_AMT1) AS C from T1 where  CONTRACT_CODE1 = '99999999999999999999999999999999999999999999999999' group by ID,BC,CONTRACT_CODE1
	) T2 group by BC]]></value>
		</entry>
		<entry key="BAM087.findCreditInfoIII">
			<value><![CDATA[WITH T1 AS (Select ID,SUBSTR(BANK_CODE,1,3) AS BC,CONTRACT_CODE1,PASS_DUE_AMT,LOAN_AMT,CONTRACT_AMT1 
	FROM MIS.BAM087 WHERE ID=? AND (PRODID = 'P1' OR PRODID = 'P8') AND (ACCOUNT_CODE = 'J' OR ACCOUNT_CODE = 'P' OR ACCOUNT_CODE = 'Q') )
	SELECT BC, SUM(P) AS JPQP, SUM(L) AS JPQL, SUM(C) AS JPQC
	from (select ID,BC,CONTRACT_CODE1,SUM(PASS_DUE_AMT) AS P, SUM(LOAN_AMT) AS L, MAX(CONTRACT_AMT1) AS C from T1 where CONTRACT_CODE1 <> '99999999999999999999999999999999999999999999999999' group by ID,BC,CONTRACT_CODE1
		union
		  select ID,BC,CONTRACT_CODE1,SUM(PASS_DUE_AMT) AS P, SUM(LOAN_AMT) AS L, SUM(CONTRACT_AMT1) AS C from T1 where  CONTRACT_CODE1 = '99999999999999999999999999999999999999999999999999' group by ID,BC,CONTRACT_CODE1
	) T2 group by BC]]></value>
		</entry>
		<entry key="BAM087.findCreditInfoIV">
			<value><![CDATA[WITH T1 AS (Select ID,SUBSTR(BANK_CODE,1,3) AS BC,CONTRACT_CODE1,PASS_DUE_AMT,LOAN_AMT,CONTRACT_AMT1 
	FROM MIS.BAM087 WHERE ID=? AND PRODID = 'P5' AND (ACCOUNT_CODE = 'J' OR ACCOUNT_CODE = 'P' OR ACCOUNT_CODE = 'Q') )
	SELECT BC, SUM(P) AS JPQP, SUM(L) AS JPQL, SUM(C) AS JPQC
	from (select ID,BC,CONTRACT_CODE1,SUM(PASS_DUE_AMT) AS P, SUM(LOAN_AMT) AS L, MAX(CONTRACT_AMT1) AS C from T1 where CONTRACT_CODE1 <> '99999999999999999999999999999999999999999999999999' group by ID,BC,CONTRACT_CODE1
		union
		  select ID,BC,CONTRACT_CODE1,SUM(PASS_DUE_AMT) AS P, SUM(LOAN_AMT) AS L, SUM(CONTRACT_AMT1) AS C from T1 where  CONTRACT_CODE1 = '99999999999999999999999999999999999999999999999999' group by ID,BC,CONTRACT_CODE1
	) T2 group by BC]]></value>
		</entry>
		<entry key="AAS103.findByBans">
			<value><![CDATA[SELECT BAN, PNAME FROM MIS.AAS103 WHERE BAN in (:BANs)]]></value>
		</entry>
		<entry key="DATADATE.findMaxDataDateByIdAndPIdAndItNm">
			<value><![CDATA[SELECT DATADATE,PRODID,QDATE FROM MIS.DATADATE WHERE ID=:ID]]></value>
		</entry>	
		<entry key="KRS001.findCreditCardDisbRecord">
			<value><![CDATA[SELECT ID, DEAD_CNT, AB_CODE, STOPDATE FROM MIS.KRS001 WHERE ID = ? AND PRODID = 'P2' ORDER BY STOPDATE DESC]]></value>
		</entry>	
		<entry key="PROFILE.findCreditCardDisbKindCNm">
			<value><![CDATA[SELECT CODENO, DESC FROM MIS.PROFILE WHERE CODETYPE = 'AB_CODE' AND CODENO = ?]]></value>
		</entry>	
		<entry key="KRM040.findLstYrCreditCardPaymntDelayCount">
			<value><![CDATA[SELECT COUNT(*) AS TCOUNT FROM MIS.KRM040 
			WHERE ID = ?  AND PAY_CODE NOT IN  ('X','N','0',' ') AND ISSUE <> 'TOT' AND QDATE = ? AND PRODID = ? ]]></value>
		</entry>
		<entry key="KRM040.findLstYrCreditCardPaymntDelayTime">
			<value><![CDATA[SELECT MAX(PAY_CODE) as MAX_PAY_CODE FROM MIS.KRM040 
			WHERE ID = ? AND  PAY_CODE NOT IN  ('X','N','0',' ') AND ISSUE <> 'TOT' AND QDATE = ? AND PRODID = ?]]></value>
		</entry>
		<entry key="KRM040.findUseCreditCardCycleAmtCount">
			<value><![CDATA[SELECT COUNT(*) AS TCOUNT  FROM MIS.KRM040 
			WHERE ID = ? AND (PAY_STAT <> 'X' AND  PAY_STAT <> '1' AND  PAY_STAT <> ' ') AND ISSUE <> 'TOT' AND QDATE = ? AND PRODID = ?]]></value>
		</entry>
		<entry key="DATADATE.findMaxQDateByIdAndPIdAndItNm">
			<value><![CDATA[SELECT QDATE FROM MIS.DATADATE WHERE ID=:ID]]></value>
		</entry>
		<entry key="BAM206.findTkLoanAndCrLoanStatus">
			<value><![CDATA[SELECT COUNT(*) FROM MIS.BAM206 WHERE ID = ? AND PRODID = ? ]]></value>
		</entry>
		<entry key="ACM009.findGuarantorBusiness">
			<value><![CDATA[SELECT DISTINCT MIS.ACM009.ID,MIS.ACM009.CNAME,MIS.ACM009.BAN,MIS.ACM009.STOCK,MIS.PROFILE.DESC,MIS.ACS015.REAL_CAPITAL 
			FROM MIS.ACM009 LEFT OUTER JOIN MIS.ACS015 ON MIS.ACM009.CNAME = MIS.ACS015.C_NAME, MIS.PROFILE 
			WHERE MIS.ACM009.ID = ? AND MIS.ACM009.PRODID = ? AND MIS.ACM009.STATUS_CODE = ? AND MIS.PROFILE.CODETYPE = ? AND MIS.PROFILE.CODENO = MIS.ACM009.POS_CODE]]></value>
		</entry>	
		<entry key="BAM303.findGuaranteeDebt">
			<value><![CDATA[SELECT ID, M_ID, M_ID_NAME, SUM(LOAN_AMT) AS BAL , SUM(PASS_DUE_AMT) AS PASS 
			FROM MIS.BAM303 WHERE ID = ? AND (PRODID = ? OR PRODID='P9')  GROUP BY ID, M_ID, M_ID_NAME ORDER BY BAL DESC]]></value>
		</entry>
		<entry key="BAM302.findLoanAmountAndBalance">
			<value><![CDATA[SELECT ID, SUM(LOAN_AMT) AS TOT_LOAN, SUM(PASS_DUE_AMT) AS TOT_PASS
			 FROM MIS.BAM302 
			 WHERE ID IN ({0}) AND PRODID IN ('''P1''','''P3''','''P4''','''P8''') AND QDATE = ?
			 GROUP BY ID]]></value>
		</entry>
		<entry key="BAM302.findLoanAmountAndBalanceInPrIds">
			<value><![CDATA[SELECT ID, SUM(LOAN_AMT) AS TOT_LOAN, SUM(PASS_DUE_AMT) AS TOT_PASS
			 FROM MIS.BAM302 
			 WHERE ID IN ({0}) AND PRODID IN ({1}) AND QDATE = ?
			 GROUP BY ID]]></value>
		</entry>
		<!-- 在【MisEJF419ServiceImpl】使用 -->
		<entry key="BAM087.findUseCashCardCycleAmtCount">
			<value><![CDATA[SELECT COUNT(*) AS TCOUNT FROM MIS.BAM087 
			WHERE ID = ? AND QDATE = ? AND PRODID = ? AND ACCOUNT_CODE = 'Y' AND LOAN_AMT <> 0]]></value>
		</entry>					
		<entry key="BAM087.findBorrowerInfo">
			<value><![CDATA[SELECT BAM.BC,SUM(BAM.LOAN_AMT) AS TOT_LOAN,SUM(BAM.PASS_AMT) AS TOT_PASS 
			FROM (SELECT SUBSTR(BANK_CODE,1,3) AS BC,LOAN_AMT AS LOAN_AMT,PASS_DUE_AMT AS PASS_AMT 
			FROM MIS.BAM087 WHERE ID = ? AND (PRODID = ? OR PRODID = 'P9')) AS BAM  GROUP BY BAM.BC ORDER BY TOT_LOAN DESC]]></value>
		</entry>

    	<!-- 聯徵查詢月份當時無擔保授信往來家數(排除學生助學貸款(Z)、本行貸款)	d63_ln_nos_bank QDATE的日期格式為'101/01/01'-->
    	<entry key="BAM095.D63LnNosBank">
        	<value>
            <![CDATA[
				WITH TMP_01 AS
				(select ID AS S_ID,MAX(QDATE) AS S_DATE from MIS.Datadate 
				  where ID = ? AND QDATE=? AND PRODID = ? and itemname='BAI001' GROUP BY ID),
                TMP_02 AS
                (
				  SELECT A.S_ID,A.S_DATE,COUNT(DISTINCT(BANK)) AS BANK_COUNT,SUM(BANK_1) AS BANK_2
				  FROM TMP_01 A 
				  LEFT JOIN 
				      (SELECT DISTINCT S_ID,S_DATE,SUBSTR(BANK_CODE,1,3)AS BANK
				         FROM TMP_01 A LEFT JOIN MIS.BAM095 B ON A.S_ID=B.ID AND A.S_DATE=B.QDATE
				          AND   SUBSTR(BANK_CODE,1,3) <> '017'
                 AND   ACCOUNT_CODE2 <> 'S'
                 AND   ACCOUNT_CODE  <> 'Z'
                 AND   CONTRACT_AMT  >=0
                 AND  (LOAN_AMT+PASS_DUE_AMT) >0 
				          AND   PRODID =? )  B ON A.S_ID =B.S_ID AND A.S_DATE =B.S_DATE
				  LEFT JOIN 
				      (SELECT S_ID,S_DATE,(CASE WHEN BANK_CODE IS NULL THEN NULL ELSE 0 END) AS BANK_1
				        FROM TMP_01 A LEFT JOIN MIS.BAM095 B ON A.S_ID =B.ID AND A.S_DATE =B.QDATE
				       ) C ON A.S_ID =C.S_ID AND A.S_DATE=C.S_DATE
                 GROUP BY A.S_ID,A.S_DATE
				 ),				
				 TMP_03 AS 
				(
				  SELECT S_ID,S_DATE,
				      (CASE WHEN BANK_2 IS NULL THEN NULL
				       ELSE BANK_COUNT END )AS D63
				  FROM TMP_02 
                )
                SELECT S_ID,S_DATE,D63 
                FROM TMP_03
			]]>
			</value>
		</entry>
	
		<!-- 近6個月信用卡使用循環信用月份數(含查詢當月之畸零月)	a21_cc6_rc_use_month QDATE的日期格式為'101/01/01' -->
    	<entry key="KRM040.A21Cc6RcUseMonth_Q_V_1_0">
        	<value>
            <![CDATA[
				WITH TMP_01 AS
				(select ID AS S_ID,MAX(QDATE) AS S_DATE from MIS.Datadate 
				  where ID = ? AND QDATE=? AND PRODID =? and itemname='BAI001' GROUP BY ID),
				TMP_02 AS 
				(
				  SELECT DISTINCT A.S_ID,A.S_DATE,BILL_DATE,TYPE_1,TYPE_2
				  FROM TMP_01 A 
				  LEFT JOIN 
				       (SELECT A.S_ID,A.S_DATE,SUBSTR(CHAR(INT(SUBSTR(B.BILL_DATE,1,3))+1911),1,4) || '-' ||
         			SUBSTR(B.BILL_DATE,4,2) AS BILL_DATE,
				        (CASE  WHEN REVOL_BAL=0 THEN  0 
				               WHEN REVOL_BAL >2000 AND PERM_LIMIT >0 AND REVOL_BAL/(PERM_LIMIT*1000) >0.1 THEN 1
				               WHEN REVOL_BAL >2000 AND PERM_LIMIT =0 THEN 1
				               WHEN REVOL_BAL >2000 AND PERM_LIMIT IS NULL THEN 1
				          ELSE 0 END )AS TYPE_1
				         FROM TMP_01 A  LEFT JOIN MIS.KRM040 B ON A.S_ID =B.IDN_BAN AND A.S_DATE =B.QDATE
      				AND SUBSTR(replace(char(DATE(rtrim(char(int(SUBSTR(B.QDATE,1,3))+1911))||'-'||SUBSTR(B.QDATE,5,2)||'-'||SUBSTR(B.QDATE,8,2))-6 MONTH),'-',''),1,6) <=    
				　　　　				rtrim(Char(INT(SUBSTR(B.BILL_DATE,1,3))+1911))||  
									SUBSTR(B.BILL_DATE,4,2)
				          AND PRODID =?
				        ) B ON A.S_ID=B.S_ID AND A.S_DATE =B.S_DATE
				  LEFT JOIN 
				       (SELECT A.S_ID,A.S_DATE,
				         (CASE WHEN ISSUE IS NULL THEN NULL ELSE 0 END) AS TYPE_2
				         FROM TMP_01 A LEFT JOIN MIS.KRM040 B ON A.S_ID =B.IDN_BAN 
				          AND A.S_DATE =B.QDATE) C ON A.S_ID=C.S_ID AND A.S_DATE =C.S_DATE
				)
				,
				TMP_03 AS 
				( SELECT S_ID,S_DATE,
				         SUM(CASE WHEN TYPE_2 IS NULL THEN NULL
				                  WHEN TYPE_2 IS NOT NULL THEN (TYPE_1+TYPE_2)
				             ELSE 99 END )AS A21
            		FROM TMP_02 
				   GROUP BY S_ID,S_DATE
				)
				SELECT *
    					FROM TMP_03 
			]]>
			</value>
		</entry>
	
		<!-- 近6個月信用卡使用循環信用家數(含查詢當月之畸零月)	a11_cc6_rc_use_bank QDATE的日期格式為'101/01/01' -->
    	<entry key="KRM040.AllCc6RcUseBank_Q_V_1_0">
        	<value>
            <![CDATA[
				WITH TMP_01 AS
				(select ID AS S_ID,MAX(QDATE) AS S_DATE from MIS.Datadate 
				  where ID = ? AND QDATE=? AND PRODID =? and itemname='BAI001' GROUP BY ID),
				TMP_02 AS
				(
				  SELECT DISTINCT A.S_ID,A.S_DATE,ISSUE,TYPE_1,TYPE_2
				  FROM TMP_01 A 
				  LEFT JOIN 
				       (SELECT A.S_ID,A.S_DATE,ISSUE ,
				        (CASE  WHEN REVOL_BAL=0 THEN  0 
				               WHEN REVOL_BAL >2000 AND PERM_LIMIT >0 AND REVOL_BAL/(PERM_LIMIT*1000) >0.1 THEN 1
				               WHEN REVOL_BAL >2000 AND PERM_LIMIT =0 THEN 1
				               WHEN REVOL_BAL >2000 AND PERM_LIMIT IS NULL THEN 1
				          ELSE 0 END )AS TYPE_1
				         FROM TMP_01 A  LEFT JOIN MIS.KRM040 B ON A.S_ID =B.IDN_BAN AND A.S_DATE =B.QDATE
      						AND SUBSTR(replace(char(DATE(rtrim(char(int(SUBSTR(B.QDATE,1,3))+1911))||'-'||SUBSTR(B.QDATE,5,2)||'-'||SUBSTR(B.QDATE,8,2))-6 MONTH),'-',''),1,6) <=    
				　　　　				rtrim(Char(INT(SUBSTR(B.BILL_DATE,1,3))+1911))||  
							SUBSTR(B.BILL_DATE,4,2)
     						AND PRODID =?
				        ) B ON A.S_ID=B.S_ID AND A.S_DATE =B.S_DATE
				  LEFT JOIN 
				       (SELECT A.S_ID,A.S_DATE,
				         (CASE WHEN ISSUE IS NULL THEN NULL ELSE 0 END) AS TYPE_2
				         FROM TMP_01 A LEFT JOIN MIS.KRM040 B ON A.S_ID =B.IDN_BAN 
				          AND A.S_DATE =B.QDATE) C ON A.S_ID=C.S_ID AND A.S_DATE =C.S_DATE
				)
				,
				TMP_03 AS 
				( SELECT S_ID,S_DATE,
				         SUM(CASE WHEN TYPE_2 IS NULL THEN NULL
				                  WHEN TYPE_2 IS NOT NULL THEN (TYPE_1+TYPE_2)
				             ELSE 99 END )AS A11
            			FROM TMP_02 
				   GROUP BY S_ID,S_DATE
				)
				SELECT *
    					FROM TMP_03 
			]]>
			</value>
		</entry>
		
		
		<!--
		大部份的寫法  WHEN (QDATE - 6 MONTH <= BILL_DATE) THEN 1 ELSE 0 END
		這裡反向, 用 > 先排除超出6個月的資料
		-->
		<entry key="KRM040.a11_cc6_rc_use_bank_V2_0">
        	<value>
            <![CDATA[
				WITH TMP_01 AS
				(select ID AS S_ID,MAX(QDATE) AS S_DATE from MIS.Datadate 
				  where ID = ? AND QDATE=? AND PRODID =? and itemname='BAI001' GROUP BY ID),
				TMP_02 AS 
				(
				   SELECT A.S_ID,A.S_DATE,SUM(A11_COUNT) AS A11
				     FROM TMP_01 A LEFT JOIN 
				     (  
				        SELECT DISTINCT A.S_ID,A.S_DATE,ISSUE ,
				           (CASE WHEN SUBSTR(replace(char(DATE(rtrim(char(int(SUBSTR(B.QDATE,1,3))+1911))||'-'||SUBSTR(B.QDATE,5,2)||'-'||SUBSTR(B.QDATE,8,2))-6 MONTH),'-',''),1,6) > 
				                      rtrim(Char(INT(SUBSTR(B.BILL_DATE,1,3))+1911))||SUBSTR(B.BILL_DATE,4,2) 
									  THEN 0
									   
				                 WHEN REVOL_BAL=0 THEN  0 
				                 WHEN REVOL_BAL >20000 THEN 1
				                 WHEN REVOL_BAL >0 AND PERM_LIMIT >0 AND (REVOL_BAL/(PERM_LIMIT*1000) >0.1) THEN 1
				                 WHEN REVOL_BAL >0 AND REVOL_BAL <=20000 AND PERM_LIMIT >0 AND (REVOL_BAL/(PERM_LIMIT*1000) <= 0.1)  THEN 0
				                 WHEN REVOL_BAL >0 AND PERM_LIMIT =0 THEN 1         
				            ELSE NULL END )AS A11_COUNT
				           FROM TMP_01 A  LEFT JOIN MIS.KRM040 B ON A.S_ID =B.IDN_BAN AND A.S_DATE =B.QDATE
				           WHERE PRODID =?
				            AND ISSUE <> 'TOT'
				     )B ON A.S_ID = B.S_ID AND A.S_DATE = B.S_DATE
				   GROUP BY A.S_ID,A.S_DATE
				)     
				SELECT *
    					FROM TMP_02 
			]]>
			</value>
		</entry>
		
		<!--
		大部份的寫法  WHEN (QDATE - 6 MONTH <= BILL_DATE) THEN 1 ELSE 0 END
		這裡反向, 用 > 先排除超出6個月的資料
		=====
		因子 P19 在[房貸2.0, 非房貸2.0] 針對 '10' 的判斷不同
		-->	
		<entry key="KRM040.p68_p19_G_V_2_0">
        	<value>
            <![CDATA[
				WITH TMP_01 AS
				(select ID AS S_ID,MAX(QDATE) AS S_DATE from MIS.Datadate 
				  where ID = ? AND QDATE=? AND PRODID =? and itemname='BAI001' GROUP BY ID),
				TMP_02 AS 
				(
				   SELECT A.IDN_BAN,
				          A.QDATE,
				          (SUM(CASE WHEN SUBSTR(replace(char(DATE(rtrim(char(int(SUBSTR(A.QDATE,1,3))+1911))||'-'||SUBSTR(A.QDATE,5,2)||'-'||SUBSTR(A.QDATE,8,2))-6 MONTH),'-',''),1,6) > 
				                      rtrim(Char(INT(SUBSTR(A.BILL_DATE,1,3))+1911))||SUBSTR(A.BILL_DATE,4,2) 
									  THEN 0							
				                    WHEN PAY_STAT ='2' THEN 1 
				                    WHEN PAY_STAT ='3' THEN 1
				                    WHEN PAY_STAT ='4' THEN 1
				                    ELSE 0 END))     AS P68
				   FROM   MIS.KRM040   AS A,
				          TMP_01         AS B
				   WHERE  A.IDN_BAN = B.S_ID
				     AND  A.QDATE   = B.S_DATE
				     AND  PRODID = ?
				     AND  ISSUE <> 'TOT'
				    GROUP BY 
				          A.IDN_BAN,
				          A.QDATE
				),
				TMP_03 AS 
				(
				  SELECT A.IDN_BAN,
				          A.QDATE,
				          (SUM(CASE WHEN PAY_STAT ='1' AND PAY_CODE ='N' THEN 1 
				                    WHEN PAY_STAT ='1' AND PAY_CODE ='0' THEN 0
				                    ELSE 0 END)) AS P19
				   FROM   MIS.KRM040   AS A,
				          TMP_01         AS B
				   WHERE  A.IDN_BAN = B.S_ID
				   AND    A.QDATE   = B.S_DATE
				   AND    PRODID = ?
				   AND    ISSUE <> 'TOT'
				   GROUP BY 
				          A.IDN_BAN,
				          A.QDATE
				)
				SELECT S_ID,S_DATE, P68, P19				     
				FROM   TMP_01 A LEFT JOIN TMP_02 B ON S_ID = B.IDN_BAN AND S_DATE = B.QDATE
				                LEFT JOIN TMP_03 C ON S_ID = C.IDN_BAN AND S_DATE = C.QDATE
			]]>
			</value>
		</entry>
			
		<!--
		大部份的寫法  WHEN (QDATE - 6 MONTH <= BILL_DATE) THEN 1 ELSE 0 END
		這裡反向, 用 > 先排除超出6個月的資料
		=====
		因子 P19 在[房貸2.0, 非房貸2.0] 針對 '10' 的判斷不同
		-->	
		<entry key="KRM040.p68_p19_Q_V_2_0">
        	<value>
            <![CDATA[
				WITH TMP_01 AS
				(select ID AS S_ID,MAX(QDATE) AS S_DATE from MIS.Datadate 
				  where ID = ? AND QDATE=? AND PRODID =? and itemname='BAI001' GROUP BY ID),
				TMP_02 AS 
				(
				   SELECT A.IDN_BAN,
				          A.QDATE,
				          (SUM(CASE WHEN SUBSTR(replace(char(DATE(rtrim(char(int(SUBSTR(A.QDATE,1,3))+1911))||'-'||SUBSTR(A.QDATE,5,2)||'-'||SUBSTR(A.QDATE,8,2))-6 MONTH),'-',''),1,6) > 
				                      rtrim(Char(INT(SUBSTR(A.BILL_DATE,1,3))+1911))||SUBSTR(A.BILL_DATE,4,2) 
									  THEN 0							
				                    WHEN PAY_STAT ='2' THEN 1 
				                    WHEN PAY_STAT ='3' THEN 1
				                    WHEN PAY_STAT ='4' THEN 1
				                    ELSE 0 END))     AS P68
				   FROM   MIS.KRM040   AS A,
				          TMP_01         AS B
				   WHERE  A.IDN_BAN = B.S_ID
				     AND  A.QDATE   = B.S_DATE
				     AND  PRODID = ?
				     AND  ISSUE <> 'TOT'
				    GROUP BY 
				          A.IDN_BAN,
				          A.QDATE
				),
				TMP_03 AS 
				(
				  SELECT A.IDN_BAN,
				          A.QDATE,
				          (SUM(CASE WHEN PAY_STAT ='1' AND PAY_CODE ='N' THEN 1 
				                    WHEN PAY_STAT ='1' AND PAY_CODE ='0' THEN 1
				                    ELSE 0 END)) AS P19
				   FROM   MIS.KRM040   AS A,
				          TMP_01         AS B
				   WHERE  A.IDN_BAN = B.S_ID
				   AND    A.QDATE   = B.S_DATE
				   AND    PRODID = ?
				   AND    ISSUE <> 'TOT'
				   GROUP BY 
				          A.IDN_BAN,
				          A.QDATE
				)
				SELECT S_ID,S_DATE, P68, P19				     
				FROM   TMP_01 A LEFT JOIN TMP_02 B ON S_ID = B.IDN_BAN AND S_DATE = B.QDATE
				                LEFT JOIN TMP_03 C ON S_ID = C.IDN_BAN AND S_DATE = C.QDATE
			]]>
			</value>
		</entry>
		<!-- revol_cnt -->
		<entry key="KRM040.get_revol_cnt">
        	<value>
            <![CDATA[
				select ID,QDATE,count(*) as CNT 
				from mis.krm040 
				where ID = ? AND QDATE=? AND PRODID = ? and revol_bal > 0 and ISSUE != 'TOT'
				GROUP BY ID,QDATE
			]]>
			</value>
		</entry>


		<!-- 聯徵查詢月份當月授信繳款記錄小於等於6次旗標(不含本行)	d53_ln_6_times_flag QDATE的日期格式為'101/01/01'-->
    	<entry key="BAM095.D53Ln6TimesFlag">
        	<value>
            <![CDATA[
				WITH TMP_01 AS
				(select ID AS S_ID,MAX(QDATE) AS S_DATE from MIS.Datadate 
				  where ID = ? AND QDATE=? AND PRODID =? and itemname='BAI001' GROUP BY ID), 
				TMP_02 AS 
				(
				  SELECT DISTINCT A.S_ID,A.S_DATE,MIN_PAYCODE_TIMES,ISSUE2
				  FROM TMP_01 A 
				  LEFT JOIN 
				       (SELECT A.S_ID,A.S_DATE,MIN(LENGTH(RTRIM(B.PAY_CODE_12))) AS MIN_PAYCODE_TIMES
   					FROM  TMP_01 A LEFT JOIN MIS.BAM095 B ON B.ID = A.S_ID
   					AND   B.QDATE =A.S_DATE
   					AND   SUBSTR(B.BANK_CODE,1,3) <> '017'
   					AND   B.PRODID =?   
				        GROUP BY A.S_ID,A.S_DATE) B ON A.S_ID=B.S_ID AND A.S_DATE=B.S_DATE
				  LEFT JOIN 
				       (SELECT A.S_ID,A.S_DATE,(CASE WHEN BANK_CODE IS NULL THEN NULL ELSE 0 END) AS ISSUE2  
				        FROM  TMP_01 A LEFT JOIN MIS.BAM095 B ON A.S_ID =B.ID AND A.S_DATE =B.QDATE) C ON A.S_ID=C.S_ID AND A.S_DATE =C.S_DATE
				),
				TMP_03 AS 
				(
  					SELECT S_ID,S_DATE,MIN_PAYCODE_TIMES,
				       (CASE WHEN MIN_PAYCODE_TIMES <=6 THEN 1
				             WHEN MIN_PAYCODE_TIMES IS NULL AND ISSUE2=0 THEN 0
             		        WHEN MIN_PAYCODE_TIMES >6      THEN 0  
				             WHEN MIN_PAYCODE_TIMES IS NULL AND ISSUE2 IS NULL  THEN NULL
				        ELSE 99999 END ) AS D53
  					FROM   TMP_02 
				)
				SELECT * FROM TMP_03 
			]]>
			</value>
		</entry>
		<!-- 針對C101M01A_JcicFlag -->
		<entry key="get_C101M01A_JcicFlag">
        	<value>
        	<!--
			有 krs001, krm046 但無 krm040 的原因
			(1)附卡
			(2)雖有正卡，但開卡原因 是 B-新卡未開卡
			
			select * from mis.krs001 where id='B...' {LIVE_CNT:3,  STOP_CNT:1,  DEAD_CNT=0 }
			
				● LIVE_CNT 及 STOP_CNT 看 KRM046{有3筆 STOP_DATE null, 1筆 STOP_DATE 有值}
				select * from mis.krm046 where id='B...' or IDN_BAN='B...' 
			
				● 強停 看 KRM001  的 STOPCODE='3'
				select * from mis.krs001 where id='B...'
				
			以KRS008(持有信用卡紀錄)之「是否持有信用卡(HAS_CREDIT_CARD:YN)」為準
			-->
            <![CDATA[
			select A.* 
			,(select count(*) from MIS.KRM040 K where K.IDN_BAN=A.S_ID AND K.PRODID=A.S_PRODID AND K.QDATE=A.S_DATE) as KRM040_CNT
			,(select count(*) from MIS.BAM095 B where B.ID=A.S_ID AND B.PRODID=A.S_PRODID AND B.QDATE=A.S_DATE) as BAM095_CNT
			,(select HAS_CARD from MIS.KRS008 C where C.ID=A.S_ID AND C.PRODID=A.S_PRODID AND C.QDATE=A.S_DATE) as KRS008_HAS_CARD
			,(select count(*) from MIS.KRM046 K where K.ID=A.S_ID AND K.PRODID=A.S_PRODID AND K.QDATE=A.S_DATE AND K.DISP_GROUP='1' AND K.STOP_DATE='') as PRIMARY_CARD
			,(select count(*) from MIS.KRM046 K where K.ID=A.S_ID AND K.PRODID=A.S_PRODID AND K.QDATE=A.S_DATE AND K.DISP_GROUP='2' AND K.STOP_DATE='') as ADDITIONAL_CARD
			,(select count(*) from MIS.KRM046 K where K.ID=A.S_ID AND K.PRODID=A.S_PRODID AND K.QDATE=A.S_DATE AND K.DISP_GROUP='3' AND K.STOP_DATE='') as BUSINESS_OR_P_CARD
			FROM
			( 
			  select ID AS S_ID,PRODID AS S_PRODID,MAX(QDATE) AS S_DATE from MIS.Datadate 
			  where ID = ? AND QDATE=? AND PRODID =? and itemname='BAI001' GROUP BY ID,PRODID
			) A 
			]]>
			</value>
		</entry>
		<!-- Z03因子 -->
		<entry key="BAM306.get_Z03">
        	<value>
            <![CDATA[
				WITH t1 AS
				(select ID AS ID,MAX(QDATE) AS QDATE from MIS.Datadate 
				  where ID = ? AND QDATE=? AND PRODID =? and itemname='BAI001' GROUP BY ID), 
				t2 as (
					select a.id, a.qdate, loan_amt, pass_due_amt, rel_code
					,case when (substr(rel_code,1,1) <>'X'  and substr(rel_code,2,1) ='X'  and rel_code <> '99' ) then loan_amt + pass_due_amt 
      					when rel_code <>' ' then 0
 						else null end as z03
					from t1 a
					left join mis.bam306 b 
					on  a.id=b.id and a.qdate = b.qdate and b.PRODID=?  
				)
				select  id, qdate, sum(z03) as Z03
				from t2
				group by id, qdate 
			]]>
			</value>
		</entry>
		
		<!-- KCS003 -->
		<entry key="MIS.KCS003.getData_ordByQdateDesc">
			<value>
			<![CDATA[
				select * from MIS.KCS003 where ID = ? ORDER BY QDATE DESC 
			]]>
			</value>
		</entry>
		<!-- 判斷 Q135, Q128 ,Q116 (這個可能沒有)  的RETCODE 若不是 0000，就是失敗的DATA -->
		<entry key="LOGFILE.getClsRecord">
        	<value>
            <![CDATA[
				select * from mis.logfile where qkey1=? and prodid=? and tojcic='Y' and txid like 'Q%' order by qdate desc,CLITTIME desc 
			]]>
			</value>
		</entry>

		<entry key="LOGFILE.get_latest_by_TXID">
        	<value>
            <![CDATA[
				select * from mis.logfile where qkey1=? and txid=? and tojcic='Y' and RETCODE='0000' 
				order by qdate desc,CLITTIME desc  
			]]>
			</value>
		</entry>
		
		<entry key="MIS.DAI001.get_data">
			<value>
				select * from MIS.DAI001 where ID = ? and prodid=? and qdate=?
			</value>
		</entry>
		<entry key="MIS.DAS001.get_data">
			<value>
				select * from MIS.DAS001 where ID = ? and prodid=? and qdate=?
			</value>
		</entry>
		<entry key="MIS.DAS002.get_data">
			<value>
				select * from MIS.DAS002 where ID = ? and prodid=? and qdate=?
			</value>
		</entry>
		
		<entry key="MIS.DAI002.get_latest">
			<value>
				select * from MIS.DAI002 where ID = ? 
				order by qdate desc
			</value>
		</entry>		
		<entry key="MIS.DAM001.get_data">
			<value>
				select * from MIS.DAM001 where ID = ? and prodid=? and qdate=?
			</value>
		</entry>
		<entry key="MIS.DAM003.get_data">
			<value>
				select * from MIS.DAM003 where ID = ? and prodid=? and qdate=?
			</value>
		</entry>	
		<!-- 國內授信沿用徵信相關聯徵查詢end -->
		
		<!-- 取得最新一筆查詢資料 (查詢借款人or 保證人授信資訊) -->
		<entry key="MIS.BAI001.LOGFILE.getLatestQueryRecordOfCreditInfoById">
        	<value>
            	<![CDATA[
					select bai.PRODID, l.REQID, bai.QBRANCH, bai.QDATE
					from mis.bai001           bai
					left outer join mis.LOGFILE l on bai.id = l.qkey1 and bai.prodid = l.prodid and bai.qdate = l.qdate and bai.qempname = l.qempname and bai.qbranch = l.qbranch
					where bai.id = ? and l.tojcic='Y' and l.retcode = '0000'
					order by bai.qDate desc, l.CLITTIME desc, bai.PRODID asc
				]]>
			</value>
		</entry>
		<entry key="MIS.BAI001.getLatestQueryRecordOfCreditInfoByIdInP7P9">
        	<value>
            	<![CDATA[
					select bai.PRODID, l.REQID, bai.QBRANCH, bai.QDATE
					from mis.bai001           bai
					left outer join mis.LOGFILE l on bai.id = l.qkey1 and bai.prodid = l.prodid and bai.qdate = l.qdate and bai.qempname = l.qempname and bai.qbranch = l.qbranch
					where bai.id = ? and l.tojcic='Y' and l.retcode = '0000'
					and bai.PRODID in ('P7','P9')
					order by bai.qDate desc, l.CLITTIME desc
				]]>
			</value>
		</entry>
		<entry key="MIS.BAM095.getHousingLoanCountByBAM095">
			<!--
			I ：長期放款
			S ：有十足擔保
			2A：土地及建物（商業用）
			-->
        	<value>
            	<![CDATA[
					select COUNT(*) as TOTAL_COUNT
					from mis.bam095 
					where id = ? and prodid = ? and qempcode = ? and QBRANCH = ? and qdate = ? 
						and ACCOUNT_CODE = 'I' AND ACCOUNT_CODE2 = 'S' and IS_KIND = '2A' AND LOAN_AMT > 5000
				]]>
			</value>
		</entry>
		<entry key="MIS.STM022.getEjcicDataQueryTimesMoreThan2ByOtherBank">
        	<value>
            	<![CDATA[
					With t0 as(
					    SELECT count(*) as TOTAL_COUNT
					    From mis.STM022 
					    Where ID = ? AND PRODID = ? and INQ_PURPOSE_1 = '1' and substr(BANK_CODE, 1, 3) != '017' and QUERY_DATE >= ?
					    group by substr(BANK_CODE, 1, 3)
					)
					Select count(*) as TOTAL_COUNT from t0
				]]>
			</value>
		</entry>
		<!-- BAM095 -->
		<entry key="MIS.BAM095.getBAM095Data">
			<value><![CDATA[
				select * from MIS.BAM095 where ID = ? and PRODID = ? AND QDATE=? and qempcode = ? and qbranch = ? order by ACCOUNT_CODE , ACCOUNT_CODE2]]>
			</value>
		</entry>
		<entry key="MIS.BAM305.getBAM305Data">
			<value><![CDATA[
				select * from MIS.BAM305 where ID = ? and PRODID = ? AND QDATE=? and qempcode = ? and qbranch = ? order by ACCOUNT_CODE , ACCOUNT_CODE2]]>
			</value>
		</entry>
		<entry key="MIS.KRM046.getEarliestIssueDateOfUnsuspensionCreditCard">
			<value>
			<![CDATA[
				select min(START_DATE) as START_DATE from mis.krm046 where id = ? and stop_date  = ''
			]]>
			</value>
		</entry>
		<entry key="MIS.KRM046.getKRM046Data_getNotStopMegaCard_Min_StartDate">
			<value>
			<![CDATA[
				select * from mis.krm046 where id = ? and stop_date = '' and ISSUE in ('017') order by START_DATE asc
			]]>
			</value>
		</entry>
		<entry key="MIS.RAM020.getLabourInsuredCount">
			<value>
			<![CDATA[
				select count(*) as TOTAL_COUNT from mis.RAM020 where id = ? and wage > 0 and qDate = (select max(qdate) from  mis.RAM020  where id = ?)
			]]>
			</value>
		</entry>
		<entry key="MIS.RAM020.getLabourInsuredAmountMoreThan23800Count">
			<value>
			<![CDATA[
				select count(*) as TOTAL_COUNT from  mis.RAM020 where id = ? and wage >=23800 and qDate = (select max(qdate) from  mis.RAM020  where id = ?)
			]]>
			</value>
		</entry>
		<entry key="MIS.KRS001.getStopCardDataOfCreditCard">
			<value>
			<![CDATA[
				select * from MIS.KRS001 where id = ? and prodId = ? and qDate = ?
			]]>
			</value>
		</entry>
		<entry key="MIS.VAM020.getIdChangedRecord">
			<value>
			<![CDATA[
				SELECT * FROM MIS.VAM020 where id = ? and PRODID = ? and QDATE = ?
			]]>
			</value>
		</entry>
		<entry key="MIS.VAM108.getOtherSupplementaryNoteInfo">
			<value>
			<![CDATA[
				SELECT * FROM MIS.VAM108 where ID = ? AND PRODID = ? AND QDATE = ?
			]]>
			</value>
		</entry>
		<entry key="MIS.VAM201.getCaseNotifiedRecord">
			<value>
			<![CDATA[
				select * from mis.vam201 where ID = ? and PRODID = ? and QDATE = ?
			]]>
			</value>
		</entry>
		<entry key="MIS.KRM046.getForcedStopCardDataByStopCode">
			<value>
			<![CDATA[
				SELECT * FROM MIS.KRM046 where ID = ? and PRODID = ? and QDATE = ? AND STOP_CODE = '3'
			]]>
			</value>
		</entry>
		<entry key="MIS.KRM040.getPaymentSituationOfBillInLatestMonthByBankCode">
			<value>
			<![CDATA[
				with temp as (
				    select *, Row_Number() OVER (partition by ISSUE ORDER BY BILL_DATE desc) rank
				    from mis.KRM040 
				    where ID = ? and PRODID = ? and QDATE = ?
				)
				SELECT ISSUE, CONCAT(PAY_STAT, PAY_CODE) as STATUS FROM temp where rank  = 1
			]]>
			</value>
		</entry>
		<entry key="MIS.RAS020.getAnnualIncomeIsLessThan50WanIn108Year">
			<value>
			<![CDATA[
				select * from mis.RAS020 where ID = ? order by QDATE desc
			]]>
			</value>
		</entry>
		<entry key="MIS.BAS020.getLatePaymentRecordOfCreditOver30Days">
			<value>
			<![CDATA[
				select * from mis.BAS020 WHERE ID = ? and QDATE >= ?
			]]>
			</value>
		</entry>
		<entry key="MIS.BAM210.getOverdueCollectionAndBadDebtData">
			<value>
			<![CDATA[
				select * from mis.BAM210 WHERE ID = ? and PRODID = ? and QDATE = ?
			]]>
			</value>
		</entry>
		<entry key="MIS.BAS001.getAbnormalCreditRecord">
			<value>
			<![CDATA[
				select * from MIS.BAS001 where id = ? and PRODID = ? and QDATE = ?
			]]>
			</value>
		</entry>
		<!-- MIS.VAM106 消債條例信用註記資訊, 排除 D-莫拉克颱風受災案件 -->
		<entry key="MIS.VAM106.getAllDataExceptMainCodeD">
			<value>
				select * from MIS.VAM106 
				where ID = ? and PRODID = ? and QDATE = ? and MAINCODE != 'D'
			</value>
		</entry>
		<!-- MIS.VAM107 銀行公會消金案件債務協商補充註記, 排除 D-莫拉克颱風受災案件 -->
		<entry key="MIS.VAM107.getAllDataExceptMainCodeD">
			<value>
				select * from MIS.VAM107 
				where ID = ? and PRODID = ? and QDATE = ? and MAINCODE != 'D'
			</value>
		</entry>
		<!-- J-110-0314 利率方案 24-整批房貸400億成長專案中，篩選案件增加檢查 -->
		<!-- 主債務:BAM095 篩選主債務授信科目別有S且擔保品類別25、27、2A、2B者 -->
		<entry key="MIS.BAM095.checkAccountCode2HaveS">
			<value>
				select * from MIS.BAM095 
				where ID = ? and PRODID = ? AND QDATE=? AND ACCOUNT_CODE2 = 'S' AND IS_KIND IN ('25','27','2A','2B')
			</value>
		</entry>
		<!-- 共用債務:BAM305 從債務授信科目別S者 -->
		<entry key="MIS.BAM305.checkAccountCode2HaveS">
			<value>
				select * from MIS.BAM305 
				where ID = ? and PRODID = ? AND QDATE=? AND ACCOUNT_CODE2 = 'S'
			</value>
		</entry>
		<!-- 從務人:BAM306 從債務授信科目別S者 -->
		<entry key="MIS.BAM306.checkAccountCode2HaveS">
			<value>
				select * from MIS.BAM306 
				where ID = ? and PRODID = ? AND QDATE=? AND ACCOUNT_CODE2 = 'S'
			</value>
		</entry>
		
		<!-- 查詢最近一次產品組合查詢紀錄且查詢理由是本人同意 -->
		<!-- P1 企金 611   P7 消金    128 、135 -->
		<entry key="MIS.ZAM003R.getQueryRecord">
			<value>
				SELECT * FROM mis.ZAM003R WHERE LEFT(INQ_PURPOSE,1) = 'A' AND KEY = ? AND QUERY_DATE BETWEEN ? AND ? AND QUERY_ITEM = ?
			</value>
		</entry>
		<!-- 依央行110-09-24對辦理不動產抵押貸款業務規定，有無以房屋含基地為抵押之擔保放款，且用途代號為「 1 」購置不動產 -->
		<entry key="MIS.BAM095.getBuyingRealEstateAndCollateralInSpecificType">
			<value><![CDATA[
				select count(*) as TOTAL_COUNT from MIS.BAM095 where ID = ? and PRODID = ? AND QDATE=?
						and PURPOSE_CODE = '1' and IS_KIND in ('25', '26', '27', '28', '29', '2A', '2B', '2X') ]]>
			</value>
		</entry>
		<!-- 聯徵查詢月份and前一月之預借現金循環信用(最新一期) -->
		<entry key="MIS.KRM040.getCreditCardCashLent">
			<value><![CDATA[
				with tmp_01 as (
				   select ROW_NUMBER() OVER (PARTITION BY IDN_BAN, ISSUE, BILL_MARK ORDER BY BILL_DATE DESC ) AS SEQ , *
				   from MIS.KRM040 as a
				   where a.ID = ?  and a.qdate = ?
				   and a.prodid = ?
				   and issue != 'TOT'
				   and SUBSTR(replace(char(DATE(rtrim(char(int(SUBSTR(A.QDATE,1,3))+1911))||'-'||SUBSTR(A.QDATE,5,2)||'-'||SUBSTR(A.QDATE,8,2))-1 MONTH),'-',''),1,6)
						<=  rtrim(Char(INT(SUBSTR(A.BILL_DATE,1,3))+1911))|| SUBSTR(A.BILL_DATE,4,2)
				)
			    select NVL(SUM(CASE WHEN ISSUE = '017' then CASH_LENT end), 0) AS cash_lent_017
					   ,NVL(SUM(CASE WHEN ISSUE <> '017' then CASH_LENT end), 0) AS cash_lent_other
			    	   ,NVL(SUM(CASH_LENT), 0) AS cash_lent
			    from tmp_01 a
				where SEQ = 1
			]]></value>
		</entry>
		<entry key="MIS.BAM087.getBAM087CashCardLoan">
			<value>
				select SUM(LOAN_AMT) AS loan_amt from MIS.BAM087 where id = ? AND QDATE=? and prodid = ? and account_code = 'Y'
			</value>
		</entry>
		
		<entry key="MIS.KRM046.getData">
			<value>
			<![CDATA[
				select * from mis.krm046 where id = ?
			]]>
			</value>
		</entry>
		
		<entry key="MIS.LOGFILE.getS11Data">
			<value>
			<![CDATA[
				select * from mis.logfile WHERE reqid = ? AND QBRANCH = ? AND QKEY1 = ? and txid = 'HS11' AND PRODID = 'ST'
			]]>
			</value>
		</entry>
		
		<!-- BAM029 - 取得他行新增核准額度 -->
		<entry key="MIS.BAM029.getNewApprovedQuotaByOtherBank">
			<value><![CDATA[
				select * from MIS.BAM029 where ID = ? and PRODID = ? AND QDATE = ? AND XACT_CODE = 'L' AND SUBSTR(BANK_CODE, 1, 3) != '017' ]]>
			</value>
		</entry>
		
		<entry key="LOGFILE.get_latest_by_TXID_QDate">
        	<value>
            <![CDATA[
				select * from mis.logfile where qkey1=? and txid=? and qDate = ? and tojcic='Y' and RETCODE='0000' order by qdate desc,CLITTIME desc
			]]>
			</value>
		</entry>
		
		<entry key="LOGFILE.get_latest_by_qKey1_txId_toJcic">
        	<value>
            <![CDATA[
				select * from mis.logfile where qkey1=? and txid=? and tojcic='Y'
				order by qdate desc,CLITTIME desc  
			]]>
			</value>
		</entry>
		<!-- MIS.STM017 他行查詢聯徵紀錄 -->
		<entry key="MIS.STM017.getEjcicQueryRecoredByOtherBank">
        	<value>
            <![CDATA[
				select * from mis.STM017 WHERE ID = ? and PRODID = ? and QDATE = ? AND SUBSTR(BANK_CODE, 1, 3) != '017'
			]]>
			</value>
		</entry>
		
		<!-- MIS.BAS006 查詢新增額度/撥款清償提示資訊 -->
		<entry key="MIS.BAS006.getNewQuotaOrRepaymentInfo">
        	<value>
            <![CDATA[
				SELECT * FROM MIS.BAS006 WHERE  ID = ? and PRODID = ? and QDATE = ?
			]]>
			</value>
		</entry>
		
		<entry key="LOGFILE.get_latest_by_qKey1_prodId">
        	<value>
            <![CDATA[
				select * from mis.logfile where qkey1=? and prodid=? and tojcic='Y' and RETCODE='0000' order by qdate desc,CLITTIME desc
			]]>
			</value>
		</entry>
		
		<!-- D42 BAM046 - 當月有效信用卡主卡平均信用額度(仟元) 消金房貸3.0 NEW -->
		<entry key="KRM046.d42_G_V_3_0">
        	<value>
            <![CDATA[
				with dd as
				(
					select replace((char(int(SUBSTR(qdate,1,3))+1911))||'-'||SUBSTR(qdate,5,2)||'-'||SUBSTR(qdate,8,2),' ','')qdate,limit,
					(CASE WHEN stop_date<>'' THEN replace((char(int(SUBSTR(stop_date,1,3))+1911))||'-'||SUBSTR(stop_date,4,2)||'-'||SUBSTR(stop_date,6,2),' ','') ELSE '' END)stop_date,
					replace((char(int(SUBSTR(start_date,1,3))+1911))||'-'||SUBSTR(start_date,4,2)||'-'||SUBSTR(start_date,6,2),' ','')start_date,
					replace((char(int(SUBSTR(qdate,1,3))+1911))||'-'||SUBSTR(qdate,5,2)||'-01' ,' ','')new_qdate
 				from MIS.KRM046
 				where id = ?
				and qdate = ?
				and disp_group='1'
				and prodid = ? 
				)
				select  avg(limit)D42 from dd
				where (dd.stop_date='' or dd.qdate<dd.stop_date)
				and new_qdate > start_date
			]]>
			</value>
		</entry>
		
		<!-- N01 STM022 - 近3個月非Z類申請查詢總次數(近三個月本行查詢不列入計算) 消金房貸3.0 NEW -->
		<entry key="STM022.n01_G_V_3_0">
        	<value>
            <![CDATA[
				with  t1 AS (select ID ,MAX(QDATE) AS QDATE from MIS.Datadate 
					where ID =? AND QDATE=?  AND PRODID = ? and itemname='BAI001' GROUP BY ID
				)
				, t2 as ( /* raw data */
 					 select a.id, a.qdate, bank_code, bank_name, inq_purpose_1, inq_purpose
  					, (INTEGER(SUBSTR(query_date,1,3))+1911) ||'-'|| SUBSTR(query_date,4,2) ||'-'|| SUBSTR(query_date,6,2) AS query_date ,PRODID
  					from t1 a 
 					left join  mis.stm022 b on a.id = b.id and a.qdate= b.qdate and b.prodid = ? 
				)
				,TMP_03 AS (
    				SELECT distinct t1.ID , t1.QDATE , SUBSTR(BANK_CODE,1,3) AS BANK_CODE 
           			,CASE WHEN (              
             		SUBSTR(BANK_CODE,1,3) <> '017'
             		AND ((SUBSTR(t1.QDATE,1,3)+1911)*12)+SUBSTR(t1.QDATE,5,2)-(YEAR(QUERY_DATE)*12 + MONTH(QUERY_DATE))<=3    ) THEN 1  WHEN BANK_CODE IS NOT NULL THEN 0 ELSE NULL END AS NN 
           			,query_date
    				FROM  t1  LEFT JOIN  T2 B ON 1 = 1
    				AND  PRODID = ? 
    				AND   b.ID = t1.ID
    				AND   b.QDATE = t1.QDATE
				)
				select id, qdate, SUM(NN)N01 from tmp_03
				GROUP BY ID, QDATE  
			]]>
			</value>
		</entry>
		
		<!-- R01 KRM040 - 近12個月信用卡(每筆)循環信用平均使用率_(mean(單筆循環信用使用率)) 消金房貸3.0 NEW -->
		<entry key="MIS.KRM040.R01_G_V_3_0">
			<value><![CDATA[
				select IDN_BAN, QDATE , 
					decimal(round( avg(case when (REVOL_BAL=0 ) then 0 
                   			when (REVOL_BAL>0 and PERM_LIMIT>0 ) and REVOL_BAL/(PERM_LIMIT*1000)<=1  then REVOL_BAL/(PERM_LIMIT*1000) 
                   			when (REVOL_BAL>0 and PERM_LIMIT>0 ) and REVOL_BAL/(PERM_LIMIT*1000)> 1 then REVOL_BAL/(PERM_LIMIT*1000) 
							when (REVOL_BAL  is null ) then null
							when (PERM_LIMIT is null ) then null else 1 end),  4), 10, 4)   as RevolRate
				from MIS.KRM040 
				where ID = ? and PRODID =? and QDATE = ? and ISSUE != 'TOT' 
				group by IDN_BAN, QDATE
			]]></value>
		</entry>
		
		<!-- R01 KRM040 - 近12個月信用卡(每筆)循環信用平均使用率_(mean(單筆循環信用使用率)) 消金房貸3.0 NEW -->
		<entry key="MIS.KRM040.MAX_QDATE">
			<value><![CDATA[
				with t1 as (
					SELECT MAX(QDATE)QDATE FROM MIS.KRM040 WHERE ID = ? 
				)
				SELECT ((SUBSTR(t1.QDATE,1,3)+1911) || '-' || SUBSTR(t1.QDATE,5,2) || '-' || SUBSTR(t1.QDATE,8,2))QDATE FROM t1
			]]></value>
		</entry>
		
		
		<!-- P01 BAM095 - 近 12 個月授信帳戶繳款狀況出現 0 的總次數，不含本行 消金非房貸4.0 NEW -->
		<entry key="MIS.BAM095.P01_Q_V_4_0">
			<value><![CDATA[
				with t1 as (
					SELECT  ID,QDATE, PRODID, substr(BANK_CODE,1,3) bank, PAY_CODE_12,
					length(trim(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(PAY_CODE_12,'X',''),'1',''),'2',''),'3',''),'4',''),'5',''),'6',''),'A',''),'B',''))) P  
					FROM MIS.BAM095
					WHERE ID = ? 
					AND PRODID= ? 
					AND QDATE= ? 
					AND SUBSTR(BANK_CODE,1,3) <>'017'
				)
				select ID,QDATE, PRODID, sum(P) P01 from t1 
				group by ID,QDATE, PRODID
			]]></value>
		</entry>
		
		<!-- R01 KRM040 - 近 12 個月授信帳戶繳款狀況出現 0 的總次數，不含本行 消金非房貸4.0 NEW -->
		<entry key="MIS.KRM040.R01_Q_V_4_0">
			<value><![CDATA[
				select IDN_BAN, QDATE ,
                    decimal(round( max(case when (REVOL_BAL=0 ) then 0
                               when (REVOL_BAL>0 and PERM_LIMIT>0 ) and REVOL_BAL/(PERM_LIMIT*1000)<=1  then REVOL_BAL/(PERM_LIMIT*1000)
                               when (REVOL_BAL>0 and PERM_LIMIT>0 ) and REVOL_BAL/(PERM_LIMIT*1000)> 1 then REVOL_BAL/(PERM_LIMIT*1000)
                            when (REVOL_BAL  is null ) then null
                            when (PERM_LIMIT is null ) then null else 99 end),  4), 10, 4) as R01
                from MIS.KRM040
                where ID = ? and PRODID = ? and QDATE = ? and ISSUE != 'TOT'
                group by IDN_BAN, QDATE
			]]></value>
		</entry>
		
		<entry key="MISDATA.getClsRecord">
        	<value>
            <![CDATA[
				select PRODID from mis.datadate where id = ? and itemName='BAI001' and prodid in ('P7','P9') order by qdate desc
			]]>
			</value>
		</entry>

		<entry key="MIS.KCS003.getDataBy_Id_ProdId_Qdate">
			<value>
			<![CDATA[
				select * from MIS.KCS003 where ID = ? and PRODID = ? AND QDATE = ?
			]]>
			</value>
		</entry>
		
		<!-- 歡喜信貸徵審初審判斷邏輯 MIS.VAM106 消債條例信用註記資訊 -->
		<entry key="MIS.VAM106.getByMainCodeNotIn_2_9_D_F">
			<value>
				select * from MIS.VAM106 
				where ID = ? and PRODID = ?
				and MAINCODE NOT IN ('2', '9', 'D', 'F')
				and QDATE = ?
			</value>
		</entry>
		
		<!-- 歡喜信貸徵審初審判斷邏輯 MIS.VAM107 行公會消金案件債務協商補充註記 -->
		<entry key="MIS.VAM107.getByMainCodeNotIn_2_9_D_F">
			<value>
				select * from MIS.VAM107 
				where ID = ? and PRODID = ?
				and MAINCODE NOT IN ('2', '9', 'D', 'F')
				and QDATE = ?
			</value>
		</entry>
		
		<!-- 歡喜信貸徵審初審判斷邏輯 MIS.VAM108 其他補充註記 -->
		<entry key="MIS.VAM108.getByMainCodeNotIn_2_9_D_F">
			<value>
				select * from MIS.VAM108 
				where ID = ? and PRODID = ?
				and MAINCODE NOT IN ('2', '9', 'D', 'F')
				and QDATE = ?
			</value>
		</entry>

		<!-- J-112-0304 MIS.BAM095 個人主債務資料-全行 -->
		<entry key="MIS.BAM095.getAllBankAmtData">
			<value>
				select ID
				  ,SUM(LOAN_AMT) AS TOT_LOAN 
				  ,SUM(T3.CONTRACT_AMT1) AS TOT_CONT 
				  ,SUM(PASS_DUE_AMT) AS TOT_PASS_DUE
				from ( 
				  select * from ( 
				    select id,BANK_CODE,ACCOUNT_CODE,ACCOUNT_CODE2
				      ,MAX(PASS_DUE_AMT) as PASS_DUE_AMT 
					  ,SUM(LOAN_AMT) AS LOAN_AMT 
				      ,MAX(CONTRACT_AMT1) AS CONTRACT_AMT1 
				    from MIS.BAM095 
				    where CONTRACT_CODE != '99999999999999999999999999999999999999999999999999' 
				      and ID = ? and QDATE = ? and PRODID=? 
				      and ACCOUNT_CODE !='J' and ACCOUNT_CODE !='P' and ACCOUNT_CODE !='Q' 
				   group by id,BANK_CODE,ACCOUNT_CODE,ACCOUNT_CODE2,CONTRACT_CODE1
				  ) AS T1 
				  UNION ALL 
				  select * from (
				    select id,BANK_CODE,ACCOUNT_CODE,ACCOUNT_CODE2
				      ,SUM(PASS_DUE_AMT) as PASS_DUE_AMT 
					  ,SUM(LOAN_AMT) AS LOAN_AMT
				      ,SUM(CONTRACT_AMT1) AS CONTRACT_AMT1 
				    from MIS.BAM095 
				    where CONTRACT_CODE = '99999999999999999999999999999999999999999999999999' 
				      and ID = ? and  QDATE = ? and PRODID =? 
				      and ACCOUNT_CODE !='J' and ACCOUNT_CODE !='P' and ACCOUNT_CODE !='Q' 
				    group by id,BANK_CODE,ACCOUNT_CODE,ACCOUNT_CODE2,CONTRACT_CODE 
				  )  AS T2 
				) AS T3 
				group by ID
			</value>
		</entry>
		
		<!-- J-112-0304 MIS.BAM095 個人主債務資料-本行 -->
		<entry key="MIS.BAM095.getBank017AmtData">
			<value>
				select ID
				  ,SUM(LOAN_AMT) AS TOT_LOAN 
				  ,SUM(T3.CONTRACT_AMT1) AS TOT_CONT 
				  ,SUM(PASS_DUE_AMT) AS TOT_PASS_DUE
				from ( 
				  select * from ( 
				    select id,BANK_CODE,ACCOUNT_CODE,ACCOUNT_CODE2
				      ,MAX(PASS_DUE_AMT) as PASS_DUE_AMT 
					  ,SUM(LOAN_AMT) AS LOAN_AMT 
				      ,MAX(CONTRACT_AMT1) AS CONTRACT_AMT1 
				    from MIS.BAM095 
				    where CONTRACT_CODE != '99999999999999999999999999999999999999999999999999' 
				      and ID = ? and QDATE = ? and PRODID=? 
				      and SUBSTR(BANK_CODE,1,3)='017' 
				      and ACCOUNT_CODE !='J' and ACCOUNT_CODE !='P' and ACCOUNT_CODE !='Q' 
				   group by id,BANK_CODE,ACCOUNT_CODE,ACCOUNT_CODE2,CONTRACT_CODE1
				  ) AS T1 
				  UNION ALL 
				  select * from (
				    select id,BANK_CODE,ACCOUNT_CODE,ACCOUNT_CODE2
				      ,SUM(PASS_DUE_AMT) as PASS_DUE_AMT 
					  ,SUM(LOAN_AMT) AS LOAN_AMT
				      ,SUM(CONTRACT_AMT1) AS CONTRACT_AMT1 
				    from MIS.BAM095 
				    where CONTRACT_CODE = '99999999999999999999999999999999999999999999999999' 
				      and ID = ? and  QDATE = ? and PRODID =? 
				      and SUBSTR(BANK_CODE,1,3) = '017' 
				      and ACCOUNT_CODE !='J' and ACCOUNT_CODE !='P' and ACCOUNT_CODE !='Q' 
				    group by id,BANK_CODE,ACCOUNT_CODE,ACCOUNT_CODE2,CONTRACT_CODE 
				  )  AS T2 
				) AS T3 
				group by ID
			</value>
		</entry>
		
		<!-- J-112-0304 共同債務 各銀行授信未逾期、逾期未還金額-->
		<entry key="MIS.BAM305.getLoanAmtPassDueAmtByBank">
			<value>
				select SUBSTR(BANK_CODE,1,3) AS BANKCODE, SUM(LOAN_AMT) AS TOT_LOAN, SUM(PASS_DUE_AMT) AS TOT_PASS 
				from MIS.BAM305 where ID = ? and PRODID = ? AND QDATE=?  GROUP BY SUBSTR(BANK_CODE,1,3)
			</value>
		</entry>
		
		<!-- J-112-0304 從債務 各銀行授信未逾期、逾期未還金額-->
		<entry key="MIS.BAM306.getLoanAmtPassDueAmtByBank">
			<value>
				select SUBSTR(BANK_CODE,1,3) AS BANKCODE, SUM(LOAN_AMT) AS TOT_LOAN, SUM(PASS_DUE_AMT) AS TOT_PASS 
				from MIS.BAM306 where ID = ? and PRODID = ? AND QDATE=?  GROUP BY SUBSTR(BANK_CODE,1,3)
			</value>
		</entry>
		
		<!-- J-112-0304 MIS.KRM040 指定區間內繳款異常資料筆數 -->
		<entry key="MIS.KRM040.getCardPayAbnormalCount">
			<value>
				select count(*) as Counts
				from mis.KRM040 
				where ID = ? and PRODID = ? and QDATE = ? 
				  and ISSUE !='TOT' 
				  and PAY_STAT||PAY_CODE not in ('XX','1N')
			</value>
		</entry>
		
		<!-- J-112-0304 共同債務逾期未還金額大於0之筆數-->
		<entry key="MIS.BAM305.getPassDueAmtOverZeroCount">
			<value>
				select count(*) AS Counts from MIS.BAM305 
				where ID = ? and PRODID = ? AND QDATE=? AND PASS_DUE_AMT > 0
			</value>
		</entry>
		
		<!-- H-111-0199 查詢T50-T54結果 -->
		<entry key="MIS.TAS500.findTAS500">
			<value>
				SELECT * FROM MIS.TAS500 WHERE IDN=? AND QUERY_ITEM=? AND QDATE >= ? AND QBRANCH=? AND RC='00' ORDER BY QDATE DESC
			</value>
		</entry>
		<!-- H-111-0199 查詢T50-T54結果 HTML  -->
		<entry key="MIS.BT3FILE.findByTxIdIdQDate">
			<value>
				SELECT * FROM MIS.BT3FILE WHERE TXID=? AND ID=? AND QDATE=? AND QBRANCH=? AND TIMES='2' AND QFLAG='Y' AND RTCODE='00000'
			</value>
		</entry>
		<entry key="MIS.EJF369.findAll">
			<value>
				select * from MIS.EJF369
			</value>
		</entry>
		
		<entry key="MIS.EJF369.findVDEPTID">
			<value>
				select * from MIS.EJF369 WHERE EJF369_DEPTID = ?
			</value>
		</entry>
		
		<!-- J-112-0534 查詢T70結果(只收A3=處理成功) -->
		<entry key="MIS.TAS700.findTAS700ById">
			<value>
				SELECT * FROM MIS.TAS700 WHERE IDN_BAN=? AND QUERY_ITEM='T70' AND R_STATUS='A3' ORDER BY QUERY_DATE DESC
			</value>
		</entry>
		<!-- J-112-0534 查詢T70 HTML  -->
		<entry key="MIS.BT2FILE.findByTxIdIdQDate">
			<value>
				SELECT * FROM MIS.BT2FILE WHERE TXID=? AND ID=? AND QDATE=? AND TIMES='2' AND QFLAG='Y' AND RTCODE='00000'
			</value>
		</entry>

		<!-- 聯徵查詢月份and前兩月之信用卡循環信用(最新兩期) -->
		<entry key="MIS.KRM040.getKRM040getRevolBalByDebtRateWithoutSum">
			<value><![CDATA[
				with tmp_01 as (
				   select ROW_NUMBER() OVER (PARTITION BY IDN_BAN, ISSUE, BILL_MARK ORDER BY BILL_DATE DESC ) AS SEQ , *
				   from MIS.KRM040 as a
				   where a.ID = ?  and a.qdate = ?
				   and a.prodid = ?
				   and issue != 'TOT'
				   and SUBSTR(replace(char(DATE(rtrim(char(int(SUBSTR(A.QDATE,1,3))+1911))||'-'||SUBSTR(A.QDATE,5,2)||'-'||SUBSTR(A.QDATE,8,2))-2 MONTH),'-',''),1,6)
						<=  rtrim(Char(INT(SUBSTR(A.BILL_DATE,1,3))+1911))|| SUBSTR(A.BILL_DATE,4,2)
				)
			    select ID,PRODID,QDATE,ISSUE,ISSUE_NAME,REVOL_BAL,PRE_OWED
			    from tmp_01 a
				where SEQ = 1
			]]></value>
		</entry>
		
		<!-- 取得信用卡 上期循環信用及永久額度 資料 -->
		<entry key="MIS.KRM040.getPermanentQuotaAndRevolvingCreditData">
			<value><![CDATA[
				SELECT SUM(CASH_LENT) AS CASH_LENT,
                   SUM(REVOL_BAL) AS REVOL_BAL,
                   SUM(PAYABLE) AS PAYABLE,
                   SUM(PRE_OWED) AS PRE_OWED,
                   NVL(SUM(CASE WHEN ISSUE = '017' THEN REVOL_BAL END), 0) AS REVOL_BAL_017,
                   NVL(SUM(CASE WHEN REVOL_BAL > 0 THEN PERM_LIMIT END), 0) AS TOTALCARDREVOLVINGQUOTA
	            FROM  (
	                SELECT ROW_NUMBER() OVER (PARTITION BY IDN_BAN ,ISSUE,BILL_MARK ORDER BY BILL_DATE DESC) AS SEQ
	                , *
	                FROM (
	                    SELECT QKEY1, QDATE, PRODID
	                    FROM MIS.LOGFILE
	                    WHERE TXID in ('QK33','Q128','Q810','Q146')
	                        AND TOJCIC='Y'
	                        AND QKEY1= ?
	                        AND RETCODE='0000'
	                    ORDER BY QDATE DESC
	                        ,CLITTIME DESC
	                    FETCH FIRST 1 ROW ONLY
	                ) ta
	                INNER JOIN MIS.KRM040 AS tb
	                ON ta.QKEY1=tb.ID
	                    AND ta.QDATE=tb.QDATE
	                    AND ta.PRODID=tb.PRODID
	                WHERE ISSUE != 'TOT'
	                    AND SUBSTR(REPLACE(CHAR(DATE(RTRIM(CHAR(INT(SUBSTR(tb.QDATE,1,3))+1911))||'-'||SUBSTR(tb.QDATE,5,2)||'-'||SUBSTR(tb.QDATE,8,2))-1 MONTH),'-',''),1,6)
					<= RTRIM(CHAR(INT(SUBSTR(tb.BILL_DATE,1,3))+1911))|| SUBSTR(tb.BILL_DATE,4,2)
	            )
	            WHERE SEQ = 1
			]]></value>
		</entry>
		
		<!-- 取得證券暨期貨違約交割記錄T70 結果 -->
		<entry key="MIS.TAS700.getDataBy_Id_ProdId">
			<value><![CDATA[
				select * from MIS.TAS700 where ID = ? and PRODID = ? Order by QDATE desc
			]]></value>
		</entry>
		
		<!-- 取得聯徵查詢T70 Html 結果 -->
		<entry key="MIS.BT2FILE.getSuccessHtmlData">
			<value><![CDATA[
				select * from MIS.BT2FILE Where qkey1 = ? and txId = ? and qDate = ? and qempCode = ? and qBranch = ? and qFlag='Y' and times='2' order by qdate desc, qtime desc
			]]></value>
		</entry>
		
	</util:map>
</beans>
