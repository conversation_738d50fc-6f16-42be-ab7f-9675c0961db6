log4j.rootLogger=DEBUG, A1, A2

# A1 is set to be a ConsoleAppender.
log4j.appender.A1=org.apache.log4j.ConsoleAppender
log4j.appender.A1.layout=org.apache.log4j.PatternLayout
log4j.appender.A1.layout.ConversionPattern=[%d][%5p][%t][%-15.15c{1}-%M] %m%n

      
# A2 is set to be a file
log4j.appender.A2=org.apache.log4j.DailyRollingFileAppender
log4j.appender.A2.layout=org.apache.log4j.PatternLayout
log4j.appender.A2.layout.ConversionPattern=[%d][%5p][%t][%-15.15c{1}-%M] %m%n
log4j.appender.A2.File=./log/logs/${dc.log.dcdate}/${dc.log.sysid}/${dc.log.pgid}_log4j.log

log4j.logger.com.mega.eloan.lms.dc.conf=INFO
log4j.logger.org.apache.commons.configuration=INFO
log4j.logger.org.apache.commons.beanutils=INFO