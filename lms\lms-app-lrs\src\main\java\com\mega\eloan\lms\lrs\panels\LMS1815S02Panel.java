/* 
 * LMS1815S02Panel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lrs.panels;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;
import org.springframework.ui.ModelMap;

import tw.com.jcs.auth.AuthType;

import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.model.L181M01A;

/**
 * <pre>
 * 覆審名單 - 文件資訊
 * </pre>
 * 
 * @since 2011/8
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/9/6,irene
 *          </ul>
 */
public class LMS1815S02Panel extends Panel {

	public LMS1815S02Panel(String id) {
		super(id);
	}
	
	public LMS1815S02Panel(String id, boolean updatePanelName) {
		super(id, updatePanelName);
	}
	
	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);
		addAclLabel(model, new AclLabel("btnRCkdLine", params, L181M01A.class,
				AuthType.Modify, RetrialDocStatusEnum.編製中));
		addAclLabel(model, new AclLabel("btnMDFlag", params, L181M01A.class,
				AuthType.Modify, RetrialDocStatusEnum.編製中));
		addAclLabel(model, new AclLabel("btnNCkdFlag", params, L181M01A.class,
				AuthType.Modify, RetrialDocStatusEnum.編製中));
	}

	private static final long serialVersionUID = 1L;

}
