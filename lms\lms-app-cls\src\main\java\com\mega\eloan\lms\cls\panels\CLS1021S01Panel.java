/* 
 * LMS1601S01Panel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.DocLogPanel;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.model.C102M01A;

import tw.com.jcs.common.Util;

/**
 * <pre>
 * 購置房屋擔保放款風險權數檢核表(自用住宅貸款檢核表)  - 文件資訊
 * </pre>
 * 
 * @since 2012/12/28
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/28,<PERSON><PERSON><PERSON>,new</li>
 *          <li>2022/06,EL08034,J-111-0096 Web e-Loan消金因應不動產暴險以貸放比率(LTV)決定適用之風險權數，報表名稱改為「自用住宅貸款檢核表」</li>
 *          </ul>
 */
public class CLS1021S01Panel extends Panel {
	
	/**/
	private static final long serialVersionUID = 1L;

	private C102M01A c102m01a;

	public CLS1021S01Panel(String id, C102M01A c102m01a) {
		super(id);
	}

	public CLS1021S01Panel(String id, boolean updatePanelName,
			C102M01A c102m01a) {
		super(id, updatePanelName);
		this.c102m01a = c102m01a;
	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);
		new DocLogPanel("_docLog").processPanelData(model, params);
		
		boolean showRptId_V20171231 = false;
		boolean showRptId_default = false;

		String rptId = "";
		if (c102m01a != null) {
			rptId = c102m01a.getRptId();
		}

		if (Util.equals(rptId, LMSUtil.C102M01A_RPTID_V202208)) {

		} else if (Util.equals(rptId, LMSUtil.C102M01A_RPTID_V20171231)) {
			showRptId_V20171231 = true;
		} else {
			showRptId_default = true;
		}

		model.addAttribute("showRptId_V20171231", showRptId_V20171231);
		model.addAttribute("showRptId_default", showRptId_default);
	}

}
