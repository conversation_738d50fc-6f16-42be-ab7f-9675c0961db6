/* 
 * L120M01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.util.Date;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import org.apache.commons.lang3.builder.ToStringExclude;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

import tw.com.iisi.cap.model.IDataObject;
import tw.com.jcs.common.Util;

/** 授信簽報書主檔 **/
@NamedEntityGraph(name = "L120M01A-entity-graph", attributeNodes = { @NamedAttributeNode("l120m01i") })
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L120M01A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class L120M01A extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * JOIN條件 L120A01A．關聯檔
	 * 
	 */
	@ToStringExclude
	@OneToMany(mappedBy = "l120m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private Set<L120A01A> l120a01a;

	public Set<L120A01A> getL120a01a() {
		return l120a01a;
	}

	public void setL120a01a(Set<L120A01A> l120a01a) {
		this.l120a01a = l120a01a;
	}

	/**
	 * JOIN條件 L120M01E．關聯檔
	 * 
	 */
	@ToStringExclude
	@OneToMany(mappedBy = "l120m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private Set<L120M01E> l120m01e;

	public Set<L120M01E> getL120m01e() {
		return l120m01e;
	}

	public void setL120m01e(Set<L120M01E> l120m01e) {
		this.l120m01e = l120m01e;
	}

	/**
	 * JOIN條件 L120M01ATMP1 全文檢索結果檔
	 * 
	 */
	@ToStringExclude
	@OneToMany(mappedBy = "l120m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private Set<L120M01ATMP1> l120m01atmp1;

	public Set<L120M01ATMP1> getL120m01atmp1() {
		return l120m01atmp1;
	}

	public void setL120m01atmp1(Set<L120M01ATMP1> l120m01atmp1) {
		this.l120m01atmp1 = l120m01atmp1;
	}

	/**
	 * JOIN條件與關聯檔By案件簽報書
	 * 
	 */
	@OneToOne(cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	@JoinColumn(name = "MAINID", referencedColumnName = "MAINID", nullable = false, insertable = false, updatable = false)
	private L120M01I l120m01i;

	public L120M01I getL120m01i() {
		return l120m01i;
	}

	public void setL120m01i(L120M01I l120m01i) {
		this.l120m01i = l120m01i;
	}

	// /**
	// * 刪除註記
	// * <p/>
	// * 2011/11/08 新增：文件刪除時使用(非立即性刪除)
	// */
	// @Column(name = "DELETEDTIME", columnDefinition = "TIMESTAMP")
	// private Timestamp deletedTime;

	/**
	 * 企/個金案件
	 * <p/>
	 * 1企金<br/>
	 * 2個金
	 */
	@Column(name = "DOCTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String docType;

	/**
	 * 授權別
	 * <p/>
	 * 1授權內<br/>
	 * 2授權外
	 */
	@Column(name = "DOCKIND", length = 1, columnDefinition = "CHAR(1)")
	private String docKind;

	/**
	 * 案件別
	 * <p/>
	 * 1一般<br/>
	 * 2其他<br/>
	 * 3陳復/陳述案<br/>
	 * 4異常通報<br/>
	 * 5團貸案件 (※國內個金案件)
	 */
	@Column(name = "DOCCODE", length = 1, columnDefinition = "CHAR(1)")
	private String docCode;

	/**
	 * 案件號碼-年度
	 * <p/>
	 * YYYY
	 */
	@Column(name = "CASEYEAR", columnDefinition = "DECIMAL(4,0)")
	private Integer caseYear;

	/** 案件號碼-分行 **/
	@Column(name = "CASEBRID", length = 3, columnDefinition = "CHAR(3)")
	private String caseBrId;

	/**
	 * 案件號碼-流水號
	 * <p/>
	 * 100/09/27調整<br/>
	 * 格式：00001
	 */
	@Column(name = "CASESEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer caseSeq;

	/**
	 * 案件號碼
	 * <p/>
	 * 格式與編碼規則為：<br/>
	 * 【國內DBU/OBU】：<br/>
	 * 年度(YYY)+分行簡稱+(兆)+授字第99999號，例：099蘭雅(兆)授字第00100號。<br/>
	 * 【海外分行】：<br/>
	 * 年度(YYYY)+海外分行簡稱+(兆)+授字第99999號，例：2011芝加哥(兆)授字第00001號、2011阿姆斯特丹(兆)授字第00100號
	 * 。<br/>
	 * 流水號由系統於簽報書儲存時自動賦予，不可修改<br/>
	 * 海外分行之授權內與授權外企金/個(消)金案件的流水號合併編製，且已刪除之流水號不重覆使用。<br/>
	 * (100/08/29調整)配合上傳主機(MIS與AS400)調整欄位長度且「年度」與「流水號」均需轉換為全型字並額外保留0E0F長度。<br/>
	 * 如：0E+CHAR(40)+0F<br/>
	 * 40/2*3+2=62<br/>
	 * eg.２０１１曼谷（兆）授字第００００１號
	 */
	@Column(name = "CASENO", length = 62, columnDefinition = "VARCHAR(62)")
	private String caseNo;

	/** 簽案日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "CASEDATE", columnDefinition = "DATE")
	private Date caseDate;

	/**
	 * 授權等級
	 * <p/>
	 * ※原Notes「Grant」<br/>
	 * 1 - 分行授權內 2 - 區域中心授權內<br/>
	 * 3 - 分行授權外 4 - 區域中心授權外<br/>
	 * ※docKind=授權內<br/>
	 * 101/02/01調整<br/>
	 * 1分行授權內<br/>
	 * 2總行授權內<br/>
	 * 3營運中心授權內<br/>
	 * 4母行授權內<br/>
	 * 5分行授權外<br/>
	 * 6營運中心授權外
	 */
	@Column(name = "AUTHLVL", length = 1, columnDefinition = "VARCHAR(1)")
	private String authLvl;

	/**
	 * 是否加送會審單位
	 * <p/>
	 * ※docKind=授權外<br/>
	 * 1無2送會簽3送審查4會簽審查5初審6初審審查
	 */
	@Column(name = "AREACHK", length = 1, columnDefinition = "VARCHAR(1)")
	private String areaChk;

	/**
	 * 會審單位代碼
	 * <p/>
	 * ※authLvl = 3營運中心授權內<br/>
	 * ※areaChk=2送會簽3送審查
	 */
	@Column(name = "AREABRID", length = 3, columnDefinition = "VARCHAR(3)")
	private String areaBrId;

	/**
	 * 會簽文件狀態
	 * <p/>
	 * 101/04/19新增<br/>
	 * 010.會簽中<br/>
	 * 020.待放行<br/>
	 * 030.已會簽
	 */
	@Column(name = "AREADOCSTATUS", length = 3, columnDefinition = "VARCHAR(3)")
	private String areaDocstatus;

	/**
	 * 會簽單位經辦
	 * <p/>
	 * 101/04/19新增
	 */
	@Column(name = "AREAUPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String areaUpdater;

	/**
	 * 會簽單位放行主管
	 * <p/>
	 * 101/04/19新增
	 */
	@Column(name = "AREAAPPROVER", length = 6, columnDefinition = "CHAR(6)")
	private String areaApprover;

	/**
	 * 會簽單位放行時間
	 * <p/>
	 * 101/04/19新增
	 */
	@Column(name = "AREAAPPRTIME", columnDefinition = "TIMESTAMP")
	private Date areaApprTime;

	/**
	 * 案件審核層級
	 * <p/>
	 * ※原Notes「CaseLevel_No」<br/>
	 * 100/11/25項目編訂<br/>
	 * 1常董會權限<br/>
	 * 2常董會權限簽奉總經理核批<br/>
	 * 3常董會權限簽准由副總經理核批<br/>
	 * 4利費率變更案件由總處經理核定<br/>
	 * 5屬常董會授權總經理逕核案件<br/>
	 * 6總經理權限內<br/>
	 * 7副總經理權限<br/>
	 * 8處長權限<br/>
	 * 9其他(經理)<br/>
	 * A董事會權限<br/>
	 * B營運中心營運長/副營運長權限<br/>
	 * C利費率變更案件由董事長核定<br/>
	 * D個金處經理權限
	 */
	@Column(name = "CASELVL", length = 2, columnDefinition = "CHAR(2)")
	private String caseLvl;

	/**
	 * 是否整批貸款
	 * <p/>
	 * 101/02/16新增<br/>
	 * ※國內個金才需填寫
	 */
	@Column(name = "PACKLOAN", length = 1, columnDefinition = "CHAR(1)")
	private String packLoan;

	/**
	 * 報送授權外審核之主要理由
	 * <p/>
	 * 101/02/16新增<br/>
	 * ※國內個金才需填寫<br/>
	 * 1.利率<br/>
	 * 2.額度<br/>
	 * 3.成數<br/>
	 * 4.年限<br/>
	 * 5.連保人<br/>
	 * 6.聯徵負面資訊<br/>
	 * 7.其他
	 */
	@Column(name = "CASELVLREASON", length = 1, columnDefinition = "CHAR(1)")
	private String caseLvlReason;

	/**
	 * 報送授權外審核之主要理由(其他)
	 * <p/>
	 * 101/02/16新增<br/>
	 * ※國內個金才需填寫<br/>
	 * 20個全型字
	 */
	@Column(name = "REASONDESC", length = 60, columnDefinition = "VARCHAR(60)")
	private String reasonDesc;

	/**
	 * 本案最後批示結果
	 * <p/>
	 * 1承作、2婉卻
	 */
	@Column(name = "DOCRSLT", length = 1, columnDefinition = "CHAR(1)")
	private String docRslt;

	/**
	 * 本案最後批示日期
	 * <p/>
	 * ※原Notes「FinalDate」
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ENDDATE", columnDefinition = "DATE")
	private Date endDate;

	/**
	 * 案由
	 * <p/>
	 * 256個全型字
	 */
	@Column(name = "GIST", length = 4096, columnDefinition = "VARCHAR(4096)")
	private String gist;

	/**
	 * 借款用途
	 * <p/>
	 * 100/10/06新增<br/>
	 * (多選) ( 1|2|3<br/>
	 * 1.購料週轉金<br/>
	 * 2.營運週轉金<br/>
	 * 3.其他<br/>
	 * <br/>
	 * 101/02/16新增<br/>
	 * A.購置住宅<br/>
	 * B.修繕房屋<br/>
	 * C.週轉(含行家理財中長期)<br/>
	 * D.購置汽車<br/>
	 * E.購置停車位貸款<br/>
	 * F.出國留學<br/>
	 * G.投資理財<br/>
	 * H.購置自用耐久性消費品<br/>
	 * I.出國旅遊<br/>
	 * J.子女教育<br/>
	 * K.繳稅<br/>
	 * L.青年創業貸款<br/>
	 * 3.其他
	 */
	@Column(name = "PURPOSE", length = 60, columnDefinition = "VARCHAR(60)")
	private String purpose;

	/**
	 * 借款用途(其他)
	 * <p/>
	 * 150個全型字
	 */
	@Column(name = "PURPOSEOTH", length = 450, columnDefinition = "VARCHAR(450)")
	private String purposeOth;

	/**
	 * 還款財源
	 * <p/>
	 * 100/10/06新增<br/>
	 * (多選) ( 1|2|3<br/>
	 * 1.營業收入<br/>
	 * 2.盈餘及折舊<br/>
	 * 3.其他<br/>
	 * <br/>
	 * 101/02/16新增<br/>
	 * A.薪資收入<br/>
	 * B.營利收入<br/>
	 * C.投資收入<br/>
	 * D.租金收入<br/>
	 * E.利息收入<br/>
	 * 3.其他
	 */
	@Column(name = "RESOURCE", length = 30, columnDefinition = "VARCHAR(30)")
	private String resource;

	/**
	 * 還款財源(其他)
	 * <p/>
	 * 150個全型字
	 */
	@Column(name = "RESOURCEOTH", length = 450, columnDefinition = "VARCHAR(450)")
	private String resourceOth;

	/**
	 * 主要營業項目
	 * <p/>
	 * 1365個全型字
	 */
	@Column(name = "ITEMOFBUSI", length = 4096, columnDefinition = "VARCHAR(4096)")
	private String itemOfBusi;

	/**
	 * 營運概況借款人統編
	 * <p/>
	 * 【引進借款人基本資料建檔內容】時寫入
	 */
	@Column(name = "CESCUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String cesCustId;

	/**
	 * 營運概況借款人重覆碼
	 * <p/>
	 * 【引進借款人基本資料建檔內容】時寫入
	 */
	@Column(name = "CESDUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String cesDupNo;

	/**
	 * 是否為中長期案件
	 * <p/>
	 * Y/N [個金]是否已徵提保證人同意書及宣告書 Y|N
	 */
	@Column(name = "LONGCASEFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String longCaseFlag;

	/**
	 * 中長期案件描述
	 * <p/>
	 * 1詳授信期間財務預估及產業概況表<br/>
	 * 2詳其他 [個金]是否已至資料建檔建置同一關係人表Y|N
	 */
	@Column(name = "LONGCASEDSCR", length = 1, columnDefinition = "CHAR(1)")
	private String longCaseDscr;

	/**
	 * 本案為免徵信案件
	 * <p/>
	 * Y/N
	 */
	@Column(name = "CESCASE", length = 1, columnDefinition = "CHAR(1)")
	private String cesCase;

	/**
	 * 隸屬集團代碼
	 * <p/>
	 * 集團名稱請參考MIS.ELF328(GRPDTL)
	 */
	@Column(name = "GRPID", length = 4, columnDefinition = "VARCHAR(4)")
	private String grpId;

	/**
	 * 授審會/催收會
	 * <p/>
	 * 授審會|1<br/>
	 * 催收會|2
	 */
	@Column(name = "MEETINGTYPE", length = 1, columnDefinition = "VARCHAR(1)")
	private String meetingType;

	/**
	 * 授審會/催收會 會期
	 * <p/>
	 * 100/11/24調整欄位長度
	 */
	@Column(name = "RPTTITLE1", length = 120, columnDefinition = "VARCHAR(120)")
	private String rptTitle1;

	/**
	 * 常董會會期
	 * <p/>
	 * 100/11/24調整欄位長度
	 */
	@Column(name = "RPTTITLE2", length = 120, columnDefinition = "VARCHAR(120)")
	private String rptTitle2;
	
	/**
	 * 審計委員會會期
	 * <p/>
	 */
	@Column(name = "RPTTITLE3", length = 120, columnDefinition = "VARCHAR(120)")
	private String rptTitle3;

	/**
	 * 營運中心負責經辦
	 * <p/>
	 * 100/12/21新增
	 */
	@Column(name = "AREAAPPRAISER", length = 6, columnDefinition = "CHAR(6)")
	private String areaAppraiser;

	/**
	 * 區中心放行時間
	 * <p/>
	 * 100/10/06調整<br/>
	 * VARCHAR(4) ( TIMESTAMP<br/>
	 * 區中心傳送至授管處的人員及時間
	 */
	@Column(name = "AREASENDINFO", columnDefinition = "TIMESTAMP")
	private Timestamp areaSendInfo;

	/**
	 * 營運中心授審會會期
	 * <p/>
	 * 100/11/24調整欄位長度
	 */
	@Column(name = "RPTTITLEAREA1", length = 120, columnDefinition = "VARCHAR(120)")
	private String rptTitleArea1;

	/** 核准文號 **/
	@Column(name = "SIGNNO", length = 60, columnDefinition = "VARCHAR(60)")
	private String signNo;

	/**
	 * 分行首次送件人員
	 * <p/>
	 * 100/08/12新增
	 */
	@Column(name = "SENDFIRST", length = 6, columnDefinition = "CHAR(6)")
	private String sendFirst;

	/**
	 * 分行首次送件日
	 * <p/>
	 * 100/08/12新增
	 */
	@Column(name = "SENDFIRSTTIME", columnDefinition = "TIMESTAMP")
	private Timestamp sendFirstTime;

	/**
	 * 分行最後送件人員
	 * <p/>
	 * 100/08/12新增
	 */
	@Column(name = "SENDLAST", length = 6, columnDefinition = "CHAR(6)")
	private String sendLast;

	/**
	 * 分行最後送件日
	 * <p/>
	 * 100/08/12新增
	 */
	@Column(name = "SENDLASTTIME", columnDefinition = "TIMESTAMP")
	private Timestamp sendLastTime;

	/**
	 * 授管處收件日期
	 * <p/>
	 * 100/11/17新增<br/>
	 * ※授管處「待收件」Grid判斷為空值<br/>
	 * ※授管處「已收件」Grid判斷不為空值<br/>
	 * ※授管處「審核中」Grid判斷不為空值+(審核中)
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "HQRECEIVEDATE", columnDefinition = "DATE")
	private Date hqReceiveDate;

	/**
	 * 授管處負責經辦
	 * <p/>
	 * 100/12/21新增
	 */
	@Column(name = "HQAPPRAISER", length = 6, columnDefinition = "CHAR(6)")
	private String hqAppraiser;

	/**
	 * 授管處提會註記
	 * <p/>
	 * 100/11/17新增<br/>
	 * 1.授審會, 2.催收會, 3.常董會　(預設值：0)<br/>
	 * ※授管處「審 核 中」Grid判斷0+(審核中)<br/>
	 * ※授管處「提授審會」Grid判斷1+(審核中)<br/>
	 * ※授管處「提催收會」Grid判斷2+(審核中)<br/>
	 * ※授管處「提常董會」Grid判斷3+(審核中)
	 */
	@Column(name = "HQMEETFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String hqMeetFlag;

	/**
	 * 退補件日期
	 * <p/>
	 * 100/11/21新增<br/>
	 * 營運中心：退回分行更正<br/>
	 * 授 管 處：退回分行更正、退回營運中心更正
	 */
	@Column(name = "RETURNBHDATE", columnDefinition = "TIMESTAMP")
	private Date returnBHDate;

	/**
	 * 退補件原因
	 * <p/>
	 * 100/11/21新增
	 */
	@Column(name = "BACKREASON", length = 1536, columnDefinition = "VARCHAR(1536)")
	private String backReason;

	/**
	 * 退補件單位
	 * <p/>
	 * 100/11/21新增<br/>
	 * A：營運中心執行【退回分行更正】<br/>
	 * S：授 管 處執行【退回分行更正】<br/>
	 * C：授 管 處執行【退回營運中心更正】<br/>
	 * ※分　　行「待補件」Grid判斷(A,S)+(待補件)<br/>
	 * 分行放行時，若backUnit=A則清除<br/>
	 * 分行放行時，若backUnit=S則backUnit=C<br/>
	 * ※營運中心「審核中」Grid判斷(C)+(審核中)<br/>
	 * ※營運中心「待更正」Grid判斷(A,S)<br/>
	 * 營運中心放行時，若backUnit=C則清除<br/>
	 * ※授 管 處「待更正」Grid判斷(S,C)<br/>
	 * 授管處放行時，則backUnit清除
	 */
	@Column(name = "BACKUNIT", length = 1, columnDefinition = "CHAR(1)")
	private String backUnit;

	/**
	 * 分行撤件註記
	 * <p/>
	 * 100/11/21新增<br/>
	 * 授管處執行【撤件/陳復】<br/>
	 * 1.撤件2.陳復<br/>
	 * ※分　　行「撤件」Grid判斷1+(待補件)<br/>
	 * 分行放行時，清除<br/>
	 * ※營運中心「分行撤件」Grid判斷1<br/>
	 * ※授 管 處「分行撤件」Grid判斷1<br/>
	 * ※授 管 處「待陳復」Grid判斷2<br/>
	 * 授管處放行時，清除
	 */
	@Column(name = "RETURNFROMBH", length = 1, columnDefinition = "CHAR(1)")
	private String returnFromBH;

	/**
	 * 動審表異動聯貸案資料修改註記
	 * <p/>
	 * 101/04/18新增<br/>
	 * Y<br/>
	 * 於已核准的動審表執行【資料修改】時註記為Y，並產生L210M01A異動聯貸案參貸比率檔。<br/>
	 * 於L210M01A異動聯貸案參貸比率檔核准上傳後清除註記。
	 */
	@Column(name = "REESTFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String reEstFlag;

	/**
	 * RPTID
	 * <p/>
	 * 電子表單列印套版版本ID
	 */
	@Column(name = "RPTID", length = 32, columnDefinition = "VARCHAR(32)")
	private String rptId;
	/**
	 * 核准文號seq
	 */
	@Column(name = "SIGNSEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer signSeq;

	public Integer getSignSeq() {
		return signSeq;
	}

	public void setSignSeq(Integer signSeq) {
		this.signSeq = signSeq;
	}

	/**
	 * 是否為協議案(0:無,1:授管處三個月以內,2:債管處三個月以上)
	 * **/
	@Column(name = "NGFLAG", length = 1, columnDefinition = "VARCHAR(1)")
	private String ngFlag;

	/**
	 * NOTES 轉檔版本
	 */
	@Column(name = "NOTESUP", columnDefinition = "VARCHAR(6)")
	private String notesUp;

	/**
	 * 採用模型註記
	 */
	@Column(name = "RATINGFLAG", columnDefinition = "VARCHAR(2)")
	private String ratingFlag;

	/**
	 * 首次主管核准日
	 * 
	 * J-108-0042_05097_B1001 Web e-Loan企金授信修改海外分行「已敘做案件清單」之擷取規則
	 * 後續退回修改此欄位不會異動，只記錄首次主管按下去核准之日期
	 */
	@Column(name = "ORGAPPROVETIME", columnDefinition = "TIMESTAMP")
	private Date orgApproveTime;

	/** 微型企業註記 */
	@Column(name = "MINIFLAG", length = 1, columnDefinition = "VARCHAR(1)")
	private String miniFlag;

	/**
	 * 消金簡化簽報註記(A:一般消金, B:卡友信貸, C:勞工紓困, D:歡喜信貸)select * from com.bcodetype where
	 * codetype='L120M01A_simplifyFlag'
	 */
	@Column(name = "SIMPLIFYFLAG", length = 1, columnDefinition = "VARCHAR(1)")
	private String simplifyFlag;

	/**
	 * 已覆核案件退回日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "APPROVEBACKDATE", columnDefinition = "DATE")
	private Date approveBackDate;

	/**
	 * 已覆核案件退回人員
	 * <p/>
	 */
	@Column(name = "APPROVEBACKUSER", length = 6, columnDefinition = "CHAR(6)")
	private String approveBackUser;

	/**
	 * 已覆核案件原始文件狀態
	 * <p/>
	 */
	@Column(name = "APPROVEBACKDOCSTATUS", length = 3, columnDefinition = "CHAR(3)")
	private String approveBackDocStatus;

	/**
	 * 適用方案
	 * <p/>
	 */
	@Column(name = "CASETYPE", length = 3, columnDefinition = "CHAR(3)")
	private String caseType;

	/**
	 * 申請資料核對表註記
	 * <p/>
	 */
	@Column(name = "CHECKLISTFLAG", length = 3, columnDefinition = "VARCHAR(1)")
	private String checkListFlag;

	/**
	 * 徵信MAINID 小規模簽報書RPA傳進 LMSM02FormHandler.java \ smallBussCRpa
	 * <p/>
	 */
	@Column(name = "CESMAINID", length = 32, columnDefinition = "VARCHAR(32)")
	private String cesMainId;

	/**
	 * 案件處理狀態
	 * <p/>
	 */
	@Column(name = "APPLYSTATUS", length = 3, columnDefinition = "CHAR(3)")
	private String applyStatus;

	/**
	 * 適用方案子類
	 * <p/>
	 */
	@Column(name = "CASETYPEA", length = 3, columnDefinition = "CHAR(3)")
	private String caseTypeA;

	/**
	 * 信貸資料被上傳至DW狀態 Y-已被上傳至DW；N-應被上傳至DW；D-DW上之對應資料應被刪除；null-新建未被賦值之資料
	 */
	@Size(max = 1)
	@Column(name = "IS_UPLOADED_DW_HPCL", columnDefinition = "CHAR(1)")
	private String is_uploaded_dw_hpcl;

	/** 授權計算模式 **/
	@Size(max = 2)
	@Column(name = "SUGGESTMODE", length = 2, columnDefinition = "VARCHAR(2)")
	private String suggestMode;

	/**
	 * 取得 是否為協議案 Y/N
	 * 
	 * @return
	 */
	public String getNgFlag() {
		return ngFlag;
	}

	/**
	 * 設定 是否為協議案 Y/N
	 * 
	 * @param ngFlag
	 */
	public void setNgFlag(String ngFlag) {
		this.ngFlag = ngFlag;
	}

	// /**
	// * 取得刪除註記
	// * <p/>
	// * 2011/11/08 新增：文件刪除時使用(非立即性刪除)
	// */
	// public Timestamp getDeletedTime() {
	// return this.deletedTime;
	// }
	//
	// /**
	// * 設定刪除註記
	// * <p/>
	// * 2011/11/08 新增：文件刪除時使用(非立即性刪除)
	// **/
	// public void setDeletedTime(Timestamp value) {
	// this.deletedTime = value;
	// }

	/**
	 * 取得企/個金案件
	 * <p/>
	 * 1企金<br/>
	 * 2個金
	 */
	public String getDocType() {
		return this.docType;
	}

	/**
	 * 設定企/個金案件
	 * <p/>
	 * 1企金<br/>
	 * 2個金
	 **/
	public void setDocType(String value) {
		this.docType = value;
	}

	/**
	 * 取得授權別
	 * <p/>
	 * 1授權內<br/>
	 * 2授權外
	 */
	public String getDocKind() {
		return this.docKind;
	}

	/**
	 * 設定授權別
	 * <p/>
	 * 1授權內<br/>
	 * 2授權外
	 **/
	public void setDocKind(String value) {
		this.docKind = value;
	}

	/**
	 * 取得案件別
	 * <p/>
	 * 1一般<br/>
	 * 2其他<br/>
	 * 3陳復/陳述案<br/>
	 * 4異常通報<br/>
	 * 5團貸案件 (※國內個金案件)
	 */
	public String getDocCode() {
		return this.docCode;
	}

	/**
	 * 設定案件別
	 * <p/>
	 * 1一般<br/>
	 * 2其他<br/>
	 * 3陳復/陳述案<br/>
	 * 4異常通報<br/>
	 * 5團貸案件 (※國內個金案件)
	 **/
	public void setDocCode(String value) {
		this.docCode = value;
	}

	/**
	 * 取得案件號碼-年度
	 * <p/>
	 * YYYY
	 */
	public Integer getCaseYear() {
		return this.caseYear;
	}

	/**
	 * 設定案件號碼-年度
	 * <p/>
	 * YYYY
	 **/
	public void setCaseYear(Integer value) {
		this.caseYear = value;
	}

	/** 取得案件號碼-分行 **/
	public String getCaseBrId() {
		return this.caseBrId;
	}

	/** 設定案件號碼-分行 **/
	public void setCaseBrId(String value) {
		this.caseBrId = value;
	}

	/**
	 * 取得案件號碼-流水號
	 * <p/>
	 * 100/09/27調整<br/>
	 * 格式：00001
	 */
	public Integer getCaseSeq() {
		return this.caseSeq;
	}

	/**
	 * 設定案件號碼-流水號
	 * <p/>
	 * 100/09/27調整<br/>
	 * 格式：00001
	 **/
	public void setCaseSeq(Integer value) {
		this.caseSeq = value;
	}

	/**
	 * 取得案件號碼
	 * <p/>
	 * 格式與編碼規則為：<br/>
	 * 【國內DBU/OBU】：<br/>
	 * 年度(YYY)+分行簡稱+(兆)+授字第99999號，例：099蘭雅(兆)授字第00100號。<br/>
	 * 【海外分行】：<br/>
	 * 年度(YYYY)+海外分行簡稱+(兆)+授字第99999號，例：2011芝加哥(兆)授字第00001號、2011阿姆斯特丹(兆)授字第00100號
	 * 。<br/>
	 * 流水號由系統於簽報書儲存時自動賦予，不可修改<br/>
	 * 海外分行之授權內與授權外企金/個(消)金案件的流水號合併編製，且已刪除之流水號不重覆使用。<br/>
	 * (100/08/29調整)配合上傳主機(MIS與AS400)調整欄位長度且「年度」與「流水號」均需轉換為全型字並額外保留0E0F長度。<br/>
	 * 如：0E+CHAR(40)+0F<br/>
	 * 40/2*3+2=62<br/>
	 * eg.２０１１曼谷（兆）授字第００００１號
	 */
	public String getCaseNo() {
		return this.caseNo;
	}

	/**
	 * 設定案件號碼
	 * <p/>
	 * 格式與編碼規則為：<br/>
	 * 【國內DBU/OBU】：<br/>
	 * 年度(YYY)+分行簡稱+(兆)+授字第99999號，例：099蘭雅(兆)授字第00100號。<br/>
	 * 【海外分行】：<br/>
	 * 年度(YYYY)+海外分行簡稱+(兆)+授字第99999號，例：2011芝加哥(兆)授字第00001號、2011阿姆斯特丹(兆)授字第00100號
	 * 。<br/>
	 * 流水號由系統於簽報書儲存時自動賦予，不可修改<br/>
	 * 海外分行之授權內與授權外企金/個(消)金案件的流水號合併編製，且已刪除之流水號不重覆使用。<br/>
	 * (100/08/29調整)配合上傳主機(MIS與AS400)調整欄位長度且「年度」與「流水號」均需轉換為全型字並額外保留0E0F長度。<br/>
	 * 如：0E+CHAR(40)+0F<br/>
	 * 40/2*3+2=62<br/>
	 * eg.２０１１曼谷（兆）授字第００００１號
	 **/
	public void setCaseNo(String value) {
		this.caseNo = value;
	}

	/** 取得簽案日期 **/
	public Date getCaseDate() {
		return this.caseDate;
	}

	/** 設定簽案日期 **/
	public void setCaseDate(Date value) {
		this.caseDate = value;
	}

	/**
	 * 取得授權等級 ※原Notes「Grant」<br/>
	 * 1 - 分行授權內 2 - 區域中心授權內<br/>
	 * 3 - 分行授權外 4 - 區域中心授權外<br/>
	 * ※docKind=授權內<br/>
	 * 101/02/01調整<br/>
	 * 1分行授權內<br/>
	 * 2總行授權內<br/>
	 * 3營運中心授權內<br/>
	 * 4母行授權內<br/>
	 * 5分行授權外<br/>
	 * 6營運中心授權外
	 */
	public String getAuthLvl() {
		return this.authLvl;
	}

	/**
	 * 設定授權等級
	 * <p/>
	 * ※原Notes「Grant」<br/>
	 * 1 - 分行授權內 2 - 區域中心授權內<br/>
	 * 3 - 分行授權外 4 - 區域中心授權外<br/>
	 * ※docKind=授權內<br/>
	 * 101/02/01調整<br/>
	 * 1分行授權內<br/>
	 * 2總行授權內<br/>
	 * 3營運中心授權內<br/>
	 * 4母行授權內<br/>
	 * 5分行授權外<br/>
	 * 6營運中心授權外
	 */
	public void setAuthLvl(String value) {
		this.authLvl = value;
	}

	/**
	 * 取得是否加送會審單位
	 * <p/>
	 * ※docKind=授權外<br/>
	 * 1無2送會簽3送審查
	 */
	public String getAreaChk() {
		return this.areaChk;
	}

	/**
	 * 設定是否加送會審單位
	 * <p/>
	 * ※docKind=授權外<br/>
	 * 1無2送會簽3送審查
	 **/
	public void setAreaChk(String value) {
		this.areaChk = value;
	}

	/**
	 * 取得會審單位代碼
	 * <p/>
	 * ※authLvl = 3營運中心授權內<br/>
	 * ※areaChk=2送會簽3送審查
	 */
	public String getAreaBrId() {
		return this.areaBrId;
	}

	/**
	 * 設定會審單位代碼
	 * <p/>
	 * ※authLvl = 3營運中心授權內<br/>
	 * ※areaChk=2送會簽3送審查
	 **/
	public void setAreaBrId(String value) {
		this.areaBrId = value;
	}

	/**
	 * 取得會簽文件狀態
	 * <p/>
	 * 101/04/19新增<br/>
	 * 010.會簽中<br/>
	 * 020.待放行<br/>
	 * 030.已會簽
	 */
	public String getAreaDocstatus() {
		return this.areaDocstatus;
	}

	/**
	 * 設定會簽文件狀態
	 * <p/>
	 * 101/04/19新增<br/>
	 * 010.會簽中<br/>
	 * 020.待放行<br/>
	 * 030.已會簽
	 **/
	public void setAreaDocstatus(String value) {
		this.areaDocstatus = value;
	}

	/**
	 * 取得會簽單位經辦
	 * <p/>
	 * 101/04/19新增
	 */
	public String getAreaUpdater() {
		return this.areaUpdater;
	}

	/**
	 * 設定會簽單位經辦
	 * <p/>
	 * 101/04/19新增
	 **/
	public void setAreaUpdater(String value) {
		this.areaUpdater = value;
	}

	/**
	 * 取得會簽單位放行主管
	 * <p/>
	 * 101/04/19新增
	 */
	public String getAreaApprover() {
		return this.areaApprover;
	}

	/**
	 * 設定會簽單位放行主管
	 * <p/>
	 * 101/04/19新增
	 **/
	public void setAreaApprover(String value) {
		this.areaApprover = value;
	}

	/**
	 * 取得會簽單位放行時間
	 * <p/>
	 * 101/04/19新增
	 */
	public Date getAreaApprTime() {
		return this.areaApprTime;
	}

	/**
	 * 設定會簽單位放行時間
	 * <p/>
	 * 101/04/19新增
	 **/
	public void setAreaApprTime(Date value) {
		this.areaApprTime = value;
	}

	/**
	 * 取得案件審核層級
	 * <p/>
	 * ※原Notes「CaseLevel_No」<br/>
	 * 100/11/25項目編訂<br/>
	 * 1常董會權限<br/>
	 * 2常董會權限簽奉總經理核批<br/>
	 * 3常董會權限簽准由副總經理核批<br/>
	 * 4利費率變更案件由總處經理核定<br/>
	 * 5屬常董會授權總經理逕核案件<br/>
	 * 6總經理權限內<br/>
	 * 7副總經理權限<br/>
	 * 8處長權限<br/>
	 * 9其他(經理)<br/>
	 * A董事會權限<br/>
	 * B營運中心營運長/副營運長權限<br/>
	 * C利費率變更案件由董事長核定<br/>
	 * D個金處經理權限
	 */
	public String getCaseLvl() {
		return this.caseLvl;
	}

	/**
	 * 設定案件審核層級
	 * <p/>
	 * ※原Notes「CaseLevel_No」<br/>
	 * 100/11/25項目編訂<br/>
	 * 1常董會權限<br/>
	 * 2常董會權限簽奉總經理核批<br/>
	 * 3常董會權限簽准由副總經理核批<br/>
	 * 4利費率變更案件由總處經理核定<br/>
	 * 5屬常董會授權總經理逕核案件<br/>
	 * 6總經理權限內<br/>
	 * 7副總經理權限<br/>
	 * 8處長權限<br/>
	 * 9其他(經理)<br/>
	 * A董事會權限<br/>
	 * B營運中心營運長/副營運長權限<br/>
	 * C利費率變更案件由董事長核定<br/>
	 * D個金處經理權限
	 **/
	public void setCaseLvl(String value) {
		this.caseLvl = value;
	}

	/**
	 * 取得是否整批貸款
	 * <p/>
	 * 101/02/16新增<br/>
	 * ※國內個金才需填寫
	 */
	public String getPackLoan() {
		return this.packLoan;
	}

	/**
	 * 設定是否整批貸款
	 * <p/>
	 * 101/02/16新增<br/>
	 * ※國內個金才需填寫
	 **/
	public void setPackLoan(String value) {
		this.packLoan = value;
	}

	/**
	 * 取得報送授權外審核之主要理由
	 * <p/>
	 * 101/02/16新增<br/>
	 * ※國內個金才需填寫<br/>
	 * 1.利率<br/>
	 * 2.額度<br/>
	 * 3.成數<br/>
	 * 4.年限<br/>
	 * 5.連保人<br/>
	 * 6.聯徵負面資訊<br/>
	 * 7.其他
	 */
	public String getCaseLvlReason() {
		return this.caseLvlReason;
	}

	/**
	 * 設定報送授權外審核之主要理由
	 * <p/>
	 * 101/02/16新增<br/>
	 * ※國內個金才需填寫<br/>
	 * 1.利率<br/>
	 * 2.額度<br/>
	 * 3.成數<br/>
	 * 4.年限<br/>
	 * 5.連保人<br/>
	 * 6.聯徵負面資訊<br/>
	 * 7.其他
	 **/
	public void setCaseLvlReason(String value) {
		this.caseLvlReason = value;
	}

	/**
	 * 取得報送授權外審核之主要理由(其他)
	 * <p/>
	 * 101/02/16新增<br/>
	 * ※國內個金才需填寫<br/>
	 * 20個全型字
	 */
	public String getReasonDesc() {
		return this.reasonDesc;
	}

	/**
	 * 設定報送授權外審核之主要理由(其他)
	 * <p/>
	 * 101/02/16新增<br/>
	 * ※國內個金才需填寫<br/>
	 * 20個全型字
	 **/
	public void setReasonDesc(String value) {
		this.reasonDesc = value;
	}

	/**
	 * 取得本案最後批示結果
	 * <p/>
	 * 1承作、2婉卻
	 */
	public String getDocRslt() {
		return this.docRslt;
	}

	/**
	 * 設定本案最後批示結果
	 * <p/>
	 * 1承作、2婉卻
	 **/
	public void setDocRslt(String value) {
		this.docRslt = value;
	}

	/** 取得本案最後批示日期 **/
	public Date getEndDate() {
		return this.endDate;
	}

	/** 設定本案最後批示日期 **/
	public void setEndDate(Date value) {
		this.endDate = value;
	}

	/**
	 * 取得案由
	 * <p/>
	 * 256個全型字
	 */
	public String getGist() {
		return this.gist;
	}

	/**
	 * 設定案由
	 * <p/>
	 * 256個全型字
	 **/
	public void setGist(String value) {
		this.gist = value;
	}

	/**
	 * 取得借款用途 100/10/06新增<br/>
	 * (多選) ( 1|2|3<br/>
	 * 1.購料週轉金<br/>
	 * 2.營運週轉金<br/>
	 * 3.其他<br/>
	 * <br/>
	 * 101/02/16新增<br/>
	 * A.購置住宅<br/>
	 * B.修繕房屋<br/>
	 * C.週轉(含行家理財中長期)<br/>
	 * D.購置汽車<br/>
	 * E.購置停車位貸款<br/>
	 * F.出國留學<br/>
	 * G.投資理財<br/>
	 * H.購置自用耐久性消費品<br/>
	 * I.出國旅遊<br/>
	 * J.子女教育<br/>
	 * K.繳稅<br/>
	 * L.青年創業貸款<br/>
	 * 3.其他
	 */
	public String getPurpose() {
		return this.purpose;
	}

	/**
	 * 設定借款用途 100/10/06新增<br/>
	 * (多選) ( 1|2|3<br/>
	 * 1.購料週轉金<br/>
	 * 2.營運週轉金<br/>
	 * 3.其他<br/>
	 * <br/>
	 * 101/02/16新增<br/>
	 * A.購置住宅<br/>
	 * B.修繕房屋<br/>
	 * C.週轉(含行家理財中長期)<br/>
	 * D.購置汽車<br/>
	 * E.購置停車位貸款<br/>
	 * F.出國留學<br/>
	 * G.投資理財<br/>
	 * H.購置自用耐久性消費品<br/>
	 * I.出國旅遊<br/>
	 * J.子女教育<br/>
	 * K.繳稅<br/>
	 * L.青年創業貸款<br/>
	 * 3.其他
	 **/
	public void setPurpose(String value) {
		this.purpose = value;
	}

	/**
	 * 取得借款用途(其他)
	 * <p/>
	 * 20個全型字
	 */
	public String getPurposeOth() {
		return this.purposeOth;
	}

	/**
	 * 設定借款用途(其他)
	 * <p/>
	 * 20個全型字
	 **/
	public void setPurposeOth(String value) {
		this.purposeOth = value;
	}

	/**
	 * 取得還款財源 100/10/06新增<br/>
	 * (多選) ( 1|2|3<br/>
	 * 1.營業收入<br/>
	 * 2.盈餘及折舊<br/>
	 * 3.其他<br/>
	 * <br/>
	 * 101/02/16新增<br/>
	 * A.薪資收入<br/>
	 * B.營利收入<br/>
	 * C.投資收入<br/>
	 * D.租金收入<br/>
	 * E.利息收入<br/>
	 * 3.其他
	 */
	public String getResource() {
		return this.resource;
	}

	/**
	 * 設定還款財源
	 * <p/>
	 * 100/10/06新增<br/>
	 * (多選) ( 1|2|3<br/>
	 * 1.營業收入<br/>
	 * 2.盈餘及折舊<br/>
	 * 3.其他<br/>
	 * <br/>
	 * 101/02/16新增<br/>
	 * A.薪資收入<br/>
	 * B.營利收入<br/>
	 * C.投資收入<br/>
	 * D.租金收入<br/>
	 * E.利息收入<br/>
	 * 3.其他
	 **/
	public void setResource(String value) {
		this.resource = value;
	}

	/**
	 * 取得還款財源(其他)
	 * <p/>
	 * 20個全型字
	 */
	public String getResourceOth() {
		return this.resourceOth;
	}

	/**
	 * 設定還款財源(其他)
	 * <p/>
	 * 20個全型字
	 **/
	public void setResourceOth(String value) {
		this.resourceOth = value;
	}

	/**
	 * 取得主要營業項目
	 * <p/>
	 * 128個全型字
	 */
	public String getItemOfBusi() {
		return this.itemOfBusi;
	}

	/**
	 * 設定主要營業項目
	 * <p/>
	 * 128個全型字
	 **/
	public void setItemOfBusi(String value) {
		this.itemOfBusi = value;
	}

	/**
	 * 取得營運概況借款人統編
	 * <p/>
	 * 【引進借款人基本資料建檔內容】時寫入
	 */
	public String getCesCustId() {
		return this.cesCustId;
	}

	/**
	 * 設定營運概況借款人統編
	 * <p/>
	 * 【引進借款人基本資料建檔內容】時寫入
	 **/
	public void setCesCustId(String value) {
		this.cesCustId = value;
	}

	/**
	 * 取得營運概況借款人重覆碼
	 * <p/>
	 * 【引進借款人基本資料建檔內容】時寫入
	 */
	public String getCesDupNo() {
		return this.cesDupNo;
	}

	/**
	 * 設定營運概況借款人重覆碼
	 * <p/>
	 * 【引進借款人基本資料建檔內容】時寫入
	 **/
	public void setCesDupNo(String value) {
		this.cesDupNo = value;
	}

	/**
	 * 取得是否為中長期案件
	 * <p/>
	 * Y/N [個金]是否已徵提保證人同意書及宣告書 Y|N
	 * 
	 */
	public String getLongCaseFlag() {
		return this.longCaseFlag;
	}

	/**
	 * 設定是否為中長期案件
	 * <p/>
	 * Y/N [個金]是否已徵提保證人同意書及宣告書 Y|N
	 * 
	 **/
	public void setLongCaseFlag(String value) {
		this.longCaseFlag = value;
	}

	/**
	 * 取得中長期案件描述
	 * <p/>
	 * 1詳授信期間財務預估及產業概況表<br/>
	 * 2詳其他
	 * 
	 * [個金]是否已至資料建檔建置同一關係人表Y|N
	 */
	public String getLongCaseDscr() {
		return this.longCaseDscr;
	}

	/**
	 * 設定中長期案件描述
	 * <p/>
	 * 1詳授信期間財務預估及產業概況表<br/>
	 * 2詳其他 [個金]是否已至資料建檔建置同一關係人表Y|N
	 **/
	public void setLongCaseDscr(String value) {
		this.longCaseDscr = value;
	}

	/**
	 * 取得本案為免徵信案件
	 * <p/>
	 * Y/N
	 */
	public String getCesCase() {
		return this.cesCase;
	}

	/**
	 * 設定本案為免徵信案件
	 * <p/>
	 * Y/N
	 **/
	public void setCesCase(String value) {
		this.cesCase = value;
	}

	/**
	 * 取得隸屬集團代碼
	 * <p/>
	 * 集團名稱請參考MIS.ELF328(GRPDTL)
	 */
	public String getGrpId() {
		return this.grpId;
	}

	/**
	 * 設定隸屬集團代碼
	 * <p/>
	 * 集團名稱請參考MIS.ELF328(GRPDTL)
	 **/
	public void setGrpId(String value) {
		this.grpId = value;
	}

	/**
	 * 取得授審會/催收會
	 * <p/>
	 * 授審會|1<br/>
	 * 催收會|2
	 */
	public String getMeetingType() {
		return this.meetingType;
	}

	/**
	 * 設定授審會/催收會
	 * <p/>
	 * 授審會|1<br/>
	 * 催收會|2
	 **/
	public void setMeetingType(String value) {
		this.meetingType = value;
	}

	/**
	 * 取得授審會/催收會 會期
	 * <p/>
	 * 100/11/24調整欄位長度
	 */
	public String getRptTitle1() {
		return this.rptTitle1;
	}

	/**
	 * 設定授審會/催收會 會期
	 * <p/>
	 * 100/11/24調整欄位長度
	 **/
	public void setRptTitle1(String value) {
		this.rptTitle1 = value;
	}

	/**
	 * 取得常董會會期
	 * <p/>
	 * 100/11/24調整欄位長度
	 */
	public String getRptTitle2() {
		return this.rptTitle2;
	}

	/**
	 * 設定審計委員會會期
	 * <p/>
	 **/
	public void setRptTitle3(String value) {
		this.rptTitle3 = value;
	}
	
	/**
	 * 取得審計委員會會期
	 * <p/>
	 */
	public String getRptTitle3() {
		return this.rptTitle3;
	}

	/**
	 * 設定常董會會期
	 * <p/>
	 * 100/11/24調整欄位長度
	 **/
	public void setRptTitle2(String value) {
		this.rptTitle2 = value;
	}

	/**
	 * 取得營運中心負責經辦
	 * <p/>
	 * 100/12/21新增
	 */
	public String getAreaAppraiser() {
		return this.areaAppraiser;
	}

	/**
	 * 設定營運中心負責經辦
	 * <p/>
	 * 100/12/21新增
	 **/
	public void setAreaAppraiser(String value) {
		this.areaAppraiser = value;
	}

	/**
	 * 取得區中心放行時間
	 * <p/>
	 * 100/10/06調整<br/>
	 * VARCHAR(4) ( TIMESTAMP<br/>
	 * 區中心傳送至授管處的人員及時間
	 */
	public Timestamp getAreaSendInfo() {
		return this.areaSendInfo;
	}

	/**
	 * 設定區中心放行時間
	 * <p/>
	 * 100/10/06調整<br/>
	 * VARCHAR(4) ( TIMESTAMP<br/>
	 * 區中心傳送至授管處的人員及時間
	 **/
	public void setAreaSendInfo(Timestamp value) {
		this.areaSendInfo = value;
	}

	/**
	 * 取得區中心授審會會期
	 * <p/>
	 * 100/11/24調整欄位長度
	 */
	public String getRptTitleArea1() {
		return this.rptTitleArea1;
	}

	/**
	 * 設定區中心授審會會期
	 * <p/>
	 * 100/11/24調整欄位長度
	 **/
	public void setRptTitleArea1(String value) {
		this.rptTitleArea1 = value;
	}

	/** 取得核准文號 **/
	public String getSignNo() {
		return this.signNo;
	}

	/** 設定核准文號 **/
	public void setSignNo(String value) {
		this.signNo = value;
	}

	/**
	 * 取得分行首次送件人員
	 * <p/>
	 * 100/08/12新增
	 */
	public String getSendFirst() {
		return this.sendFirst;
	}

	/**
	 * 設定分行首次送件人員
	 * <p/>
	 * 100/08/12新增
	 **/
	public void setSendFirst(String value) {
		this.sendFirst = value;
	}

	/**
	 * 取得分行首次送件日
	 * <p/>
	 * 100/08/12新增
	 */
	public Timestamp getSendFirstTime() {
		return this.sendFirstTime;
	}

	/**
	 * 設定分行首次送件日
	 * <p/>
	 * 100/08/12新增
	 **/
	public void setSendFirstTime(Timestamp value) {
		this.sendFirstTime = value;
	}

	/**
	 * 取得分行最後送件人員
	 * <p/>
	 * 100/08/12新增
	 */
	public String getSendLast() {
		return this.sendLast;
	}

	/**
	 * 設定分行最後送件人員
	 * <p/>
	 * 100/08/12新增
	 **/
	public void setSendLast(String value) {
		this.sendLast = value;
	}

	/**
	 * 取得分行最後送件日
	 * <p/>
	 * 100/08/12新增
	 */
	public Timestamp getSendLastTime() {
		return this.sendLastTime;
	}

	/**
	 * 設定分行最後送件日
	 * <p/>
	 * 100/08/12新增
	 **/
	public void setSendLastTime(Timestamp value) {
		this.sendLastTime = value;
	}

	/**
	 * 取得授管處收件日期
	 * <p/>
	 * 100/11/17新增<br/>
	 * ※授管處「待收件」Grid判斷為空值<br/>
	 * ※授管處「已收件」Grid判斷不為空值<br/>
	 * ※授管處「審核中」Grid判斷不為空值+(審核中)
	 */
	public Date getHqReceiveDate() {
		return this.hqReceiveDate;
	}

	/**
	 * 設定授管處收件日期
	 * <p/>
	 * 100/11/17新增<br/>
	 * ※授管處「待收件」Grid判斷為空值<br/>
	 * ※授管處「已收件」Grid判斷不為空值<br/>
	 * ※授管處「審核中」Grid判斷不為空值+(審核中)
	 **/
	public void setHqReceiveDate(Date value) {
		this.hqReceiveDate = value;
	}

	/**
	 * 取得授管處負責經辦
	 * <p/>
	 * 100/12/21新增
	 */
	public String getHqAppraiser() {
		return this.hqAppraiser;
	}

	/**
	 * 設定授管處負責經辦
	 * <p/>
	 * 100/12/21新增
	 **/
	public void setHqAppraiser(String value) {
		this.hqAppraiser = value;
	}

	/**
	 * 取得授管處提會註記
	 * <p/>
	 * 100/11/17新增<br/>
	 * 1.授審會, 2.催收會, 3.常董會　(預設值：0)<br/>
	 * ※授管處「審 核 中」Grid判斷0+(審核中)<br/>
	 * ※授管處「提授審會」Grid判斷1+(審核中)<br/>
	 * ※授管處「提催收會」Grid判斷2+(審核中)<br/>
	 * ※授管處「提常董會」Grid判斷3+(審核中)
	 */
	public String getHqMeetFlag() {
		return this.hqMeetFlag;
	}

	/**
	 * 設定授管處提會註記
	 * <p/>
	 * 100/11/17新增<br/>
	 * 1.授審會, 2.催收會, 3.常董會　(預設值：0)<br/>
	 * ※授管處「審 核 中」Grid判斷0+(審核中)<br/>
	 * ※授管處「提授審會」Grid判斷1+(審核中)<br/>
	 * ※授管處「提催收會」Grid判斷2+(審核中)<br/>
	 * ※授管處「提常董會」Grid判斷3+(審核中)
	 **/
	public void setHqMeetFlag(String value) {
		this.hqMeetFlag = value;
	}

	/**
	 * 取得退補件日期
	 * <p/>
	 * 100/11/21新增<br/>
	 * 營運中心：退回分行更正<br/>
	 * 授 管 處：退回分行更正、退回營運中心更正
	 */
	public Date getReturnBHDate() {
		return this.returnBHDate;
	}

	/**
	 * 設定退補件日期
	 * <p/>
	 * 100/11/21新增<br/>
	 * 營運中心：退回分行更正<br/>
	 * 授 管 處：退回分行更正、退回營運中心更正
	 **/
	public void setReturnBHDate(Date value) {
		this.returnBHDate = value;
	}

	/**
	 * 取得退補件原因
	 * <p/>
	 * 100/11/21新增
	 */
	public String getBackReason() {
		return this.backReason;
	}

	/**
	 * 設定退補件原因
	 * <p/>
	 * 100/11/21新增
	 **/
	public void setBackReason(String value) {
		this.backReason = value;
	}

	/**
	 * 取得退補件單位
	 * <p/>
	 * 100/11/21新增<br/>
	 * A：營運中心執行【退回分行更正】<br/>
	 * S：授 管 處執行【退回分行更正】<br/>
	 * C：授 管 處執行【退回營運中心更正】<br/>
	 * ※分　　行「待補件」Grid判斷(A,S)+(待補件)<br/>
	 * 分行放行時，若backUnit=A則清除<br/>
	 * 分行放行時，若backUnit=S則backUnit=C<br/>
	 * ※營運中心「審核中」Grid判斷(C)+(審核中)<br/>
	 * ※營運中心「待更正」Grid判斷(A,S)<br/>
	 * 營運中心放行時，若backUnit=C則清除<br/>
	 * ※授 管 處「待更正」Grid判斷(S,C)<br/>
	 * 授管處放行時，則backUnit清除
	 */
	public String getBackUnit() {
		return this.backUnit;
	}

	/**
	 * 設定退補件單位
	 * <p/>
	 * 100/11/21新增<br/>
	 * A：營運中心執行【退回分行更正】<br/>
	 * S：授 管 處執行【退回分行更正】<br/>
	 * C：授 管 處執行【退回營運中心更正】<br/>
	 * ※分　　行「待補件」Grid判斷(A,S)+(待補件)<br/>
	 * 分行放行時，若backUnit=A則清除<br/>
	 * 分行放行時，若backUnit=S則backUnit=C<br/>
	 * ※營運中心「審核中」Grid判斷(C)+(審核中)<br/>
	 * ※營運中心「待更正」Grid判斷(A,S)<br/>
	 * 營運中心放行時，若backUnit=C則清除<br/>
	 * ※授 管 處「待更正」Grid判斷(S,C)<br/>
	 * 授管處放行時，則backUnit清除
	 **/
	public void setBackUnit(String value) {
		this.backUnit = value;
	}

	/**
	 * 取得分行撤件註記
	 * <p/>
	 * 100/11/21新增<br/>
	 * 授管處執行【撤件/陳復】<br/>
	 * 1.撤件2.陳復<br/>
	 * ※分　　行「撤件」Grid判斷1+(待補件)<br/>
	 * 分行放行時，清除<br/>
	 * ※營運中心「分行撤件」Grid判斷1<br/>
	 * ※授 管 處「分行撤件」Grid判斷1<br/>
	 * ※授 管 處「待陳復」Grid判斷2<br/>
	 * 授管處放行時，清除
	 */
	public String getReturnFromBH() {
		return this.returnFromBH;
	}

	/**
	 * 設定分行撤件註記
	 * <p/>
	 * 100/11/21新增<br/>
	 * 授管處執行【撤件/陳復】<br/>
	 * 1.撤件2.陳復<br/>
	 * ※分　　行「撤件」Grid判斷1+(待補件)<br/>
	 * 分行放行時，清除<br/>
	 * ※營運中心「分行撤件」Grid判斷1<br/>
	 * ※授 管 處「分行撤件」Grid判斷1<br/>
	 * ※授 管 處「待陳復」Grid判斷2<br/>
	 * 授管處放行時，清除
	 **/
	public void setReturnFromBH(String value) {
		this.returnFromBH = value;
	}

	/**
	 * 取得動審表異動聯貸案資料修改註記
	 * <p/>
	 * 101/04/18新增<br/>
	 * Y<br/>
	 * 於已核准的動審表執行【資料修改】時註記為Y，並產生L210M01A異動聯貸案參貸比率檔。<br/>
	 * 於L210M01A異動聯貸案參貸比率檔核准上傳後清除註記。
	 */
	public String getReEstFlag() {
		return this.reEstFlag;
	}

	/**
	 * 設定動審表異動聯貸案資料修改註記
	 * <p/>
	 * 101/04/18新增<br/>
	 * Y<br/>
	 * 於已核准的動審表執行【資料修改】時註記為Y，並產生L210M01A異動聯貸案參貸比率檔。<br/>
	 * 於L210M01A異動聯貸案參貸比率檔核准上傳後清除註記。
	 **/
	public void setReEstFlag(String value) {
		this.reEstFlag = value;
	}

	/**
	 * 取得RPTID
	 * <p/>
	 * 電子表單列印套版版本ID
	 */
	public String getRptId() {
		return this.rptId;
	}

	/**
	 * 設定RPTID
	 * <p/>
	 * 電子表單列印套版版本ID
	 **/
	public void setRptId(String value) {
		this.rptId = value;
	}

	/** 建構子 **/
	public L120M01A() {
	}

	/** 建構子 **/
	public L120M01A(String caseBrId, Integer caseSeq) {
		this.caseBrId = caseBrId;
		this.caseSeq = caseSeq;
	}

	public void setNotesUp(String notesUp) {
		this.notesUp = notesUp;
	}

	public String getNotesUp() {
		return notesUp;
	}

	/**
	 * 是否為營運中心或授管處覆核 <br/>
	 * 102/04/22新增 <br/>
	 * 當總處分行覆核時有可能為審案或會簽用以判斷總處分行案件是要抓額度明細表還是額度批覆表。 Y/N
	 */
	@Column(name = "ISHEADCHECK", length = 1, columnDefinition = "VARCHAR(1)")
	private String isHeadCheck;

	/**
	 * 是否為營運中心或授管處覆核 <br/>
	 * 102/04/22新增 <br/>
	 * 當總處分行覆核時有可能為審案或會簽用以判斷總處分行案件是要抓額度明細表還是額度批覆表。
	 * 
	 * @return Y/N
	 */
	public String getIsHeadCheck() {
		return isHeadCheck;
	}

	/**
	 * 是否為營運中心或授管處覆核 <br/>
	 * 102/04/22新增 <br/>
	 * 當總處分行覆核時有可能為審案或會簽用以判斷總處分行案件是要抓額度明細表還是額度批覆表。
	 * 
	 * @param isHeadCheck
	 *            Y/N
	 */
	public void setIsHeadCheck(String isHeadCheck) {
		this.isHeadCheck = isHeadCheck;
	}

	public String getRatingFlag() {
		return ratingFlag;
	}

	public void setRatingFlag(String ratingFlag) {
		this.ratingFlag = ratingFlag;
	}

	/**
	 * 設定首次主管核准日
	 * 
	 * @param orgApproveTime
	 */
	public void setOrgApproveTime(Date orgApproveTime) {
		this.orgApproveTime = orgApproveTime;
	}

	/**
	 * 取得首次主管核准日
	 * 
	 * @param orgApproveTime
	 */
	public Date getOrgApproveTime() {
		return orgApproveTime;
	}

	/**
	 * 取得微型企業註記
	 * 
	 * @param miniFlag
	 */
	public String getMiniFlag() {
		return miniFlag;
	}

	/**
	 * 設定微型企業註記
	 * 
	 * @param miniFlag
	 */
	public void setMiniFlag(String miniFlag) {
		this.miniFlag = miniFlag;
	}

	/** 取得消金簡化簽報註記(A:一般消金, B:卡友信貸) */
	public String getSimplifyFlag() {
		return simplifyFlag;
	}

	/** 設定消金簡化簽報註記(A:一般消金, B:卡友信貸) */
	public void setSimplifyFlag(String simplifyFlag) {
		this.simplifyFlag = simplifyFlag;
	}

	/**
	 * 設定已覆核案件退回日期
	 * 
	 * @param approveBackDate
	 */
	public void setApproveBackDate(Date approveBackDate) {
		this.approveBackDate = approveBackDate;
	}

	/**
	 * 取得已覆核案件退回日期
	 * 
	 * @param approveBackDate
	 */
	public Date getApproveBackDate() {
		return approveBackDate;
	}

	/**
	 * 設定已覆核案件退回人員
	 * 
	 * @param approveBackUser
	 */
	public void setApproveBackUser(String approveBackUser) {
		this.approveBackUser = approveBackUser;
	}

	/**
	 * 取得已覆核案件退回人員
	 * 
	 * @return
	 */
	public String getApproveBackUser() {
		return approveBackUser;
	}

	/**
	 * 設定已覆核案件原始文件狀態
	 * 
	 * @param approveBackUser
	 */
	public void setApproveBackDocStatus(String approveBackDocStatus) {
		this.approveBackDocStatus = approveBackDocStatus;
	}

	/**
	 * 取得已覆核案件原始文件狀態
	 * 
	 * @return
	 */
	public String getApproveBackDocStatus() {
		return approveBackDocStatus;
	}

	/**
	 * J-107-0390_05097_B1001 分行權限之授信案件若於覆核後欲修改,得授權主管得退回至編製中 取得原始文件狀態
	 */
	public String getDocStatus() {
		if (Util.equals(super.getDocStatus(), "BKL")) {
			return approveBackDocStatus;
		} else {
			return super.getDocStatus();
		}

	}

	/**
	 * 顯示用欄位 For View 顯示是否為紓困
	 * 
	 * @param isOnlyProd69
	 */
	@Transient
	private boolean isOnlyProd69;

	public void setIsOnlyProd69(boolean isOnlyProd69) {
		this.isOnlyProd69 = isOnlyProd69;
	}

	public boolean isOnlyProd69() {
		return isOnlyProd69;
	}

	public void setCaseType(String caseType) {
		this.caseType = caseType;
	}

	public String getCaseType() {
		return caseType;
	}

	public void setCheckListFlag(String checkListFlag) {
		this.checkListFlag = checkListFlag;
	}

	public String getCheckListFlag() {
		return checkListFlag;
	}

	public void setCesMainId(String cesMainId) {
		this.cesMainId = cesMainId;
	}

	public String getCesMainId() {
		return cesMainId;
	}

	public void setApplyStatus(String applyStatus) {
		this.applyStatus = applyStatus;
	}

	public String getApplyStatus() {
		return applyStatus;
	}

	/**
	 * 設定適用方案子類
	 * 
	 * @param approveBackUser
	 */
	public void setCaseTypeA(String caseTypeA) {
		this.caseTypeA = caseTypeA;
	}

	/**
	 * 取得適用方案子類
	 * 
	 * @return
	 */
	public String getCaseTypeA() {
		return caseTypeA;
	}

	public String getIs_uploaded_dw_hpcl() {
		return is_uploaded_dw_hpcl;
	}

	public void setIs_uploaded_dw_hpcl(String is_uploaded_dw_hpcl) {
		this.is_uploaded_dw_hpcl = is_uploaded_dw_hpcl;
	}

	/** 取得授權計算模式 **/
	public String getSuggestMode() {
		return this.suggestMode;
	}

	/** 設定授權計算模式 **/
	public void setSuggestMode(String suggestMode) {
		this.suggestMode = suggestMode;
	}
}
