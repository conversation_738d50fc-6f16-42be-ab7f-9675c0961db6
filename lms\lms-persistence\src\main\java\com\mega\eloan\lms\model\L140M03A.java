/* 
 * L140M03A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 個金額度明細表主檔補充資料檔［L140M03A.grpCntrNo 與 L120M01G.parentCntrNo］ **/
@NamedEntityGraph(name = "L140M03A-entity-graph", attributeNodes = { @NamedAttributeNode("l140m01a") })
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L140M03A", uniqueConstraints = @UniqueConstraint(columnNames = { "mainId" }))
public class L140M03A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** mainId **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 不計入同一關係戶代號
	 * <p/>
	 * 不計入同一關係戶代號 <br/>
	 * 1: 配合政府政策經財政部或央行核准之專案授信 <br/>
	 * 2: 對政府機關之授信 <br/>
	 * 3: 以公債國庫券央行儲券可轉讓存單本行存單或債券 <br/>
	 * 4: 推動小額放款業務要點之台幣一百萬元以下之授信 <br/>
	 * 5: 配合政府不列同一人限額但需列入同一關係人企業
	 */
	@Size(max = 1)
	@Column(name = "IDENTITYNO", length = 1, columnDefinition = "VARCHAR(1)")
	private String identityNo;

	/**
	 * 單據寄發方式
	 * <p/>
	 * 1:郵寄通訊地址<br/>
	 * 9:不印授信對帳單
	 */
	@Size(max = 1)
	@Column(name = "NOTICETYPE", length = 1, columnDefinition = "VARCHAR(1)")
	private String noticeType;

	/**
	 * 是否屬於青創
	 * <p/>
	 * Y/N
	 */
	@Size(max = 1)
	@Column(name = "YOUNCREATYN", length = 1, columnDefinition = "VARCHAR(1)")
	private String younCreatYN;

	/** 登記出資額 **/
	@Digits(integer = 13, fraction = 0, groups = Check.class)
	@Column(name = "CAPITALAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal capitalAMT;

	/** 設立登記日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "REGISTERDATE", columnDefinition = "DATE")
	private Date registerDate;
	
	/** 申請日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "APPLICATIONDATE", columnDefinition = "DATE")
	private Date applicationDate;

	/**
	 * 當younCreatYN=Y時，{1:屬優予核貸對象, 產品60青年築夢創業啟動金貸款, 2:為育成中心輔導, 產品58青年創業貸款, 3:產品61青年創業及啟動金貸款}
	 */
	@Size(max = 1)
	@Column(name = "ASSISTYPE", length = 1, columnDefinition = "VARCHAR(1)")
	private String assisType;

	/**
	 * 是否屬優予核貸對象/是否為育成中心輔導是否
	 * <p/>
	 * Y/N
	 */
	@Size(max = 1)
	@Column(name = "ASSISTYPEYN", length = 1, columnDefinition = "VARCHAR(1)")
	private String assisTypeYN;

	/**
	 * 企業所在縣市
	 * <p/>
	 * ※是否屬優予核貸對象有值時才顯示<br/>
	 * codetype=counties codedesc3 2013-02-26 VARCHAR(2) ->VARCHA(1)
	 * 
	 * 
	 */
	@Size(max = 1)
	@Column(name = "COMPANYCITY", length = 1, columnDefinition = "VARCHAR(1)")
	private String companyCity;

	/**
	 * 企業所在鄉鎮市區
	 * <p/>
	 * ※是否屬優予核貸對象有值時才顯示<br/>
	 * Codetype=counties+ L140M03A. companyCity
	 */
	@Size(max = 3)
	@Column(name = "COMPANYAREA", length = 3, columnDefinition = "VARCHAR(3)")
	private String companyArea;

	/**
	 * 同一事業體資訊(ID)
	 * <p/>
	 * 限企業戶統編
	 */
	@Size(max = 10)
	@Column(name = "COMPANYID", length = 10, columnDefinition = "VARCHAR(10)")
	private String companyId;

	/** 同一事業體資訊(dupNo) **/
	@Size(max = 1)
	@Column(name = "COMPANYDUPNO", length = 1, columnDefinition = "VARCHAR(1)")
	private String companyDupNo;

	/** 同一事業體資訊(名稱) **/
	@Size(max = 120)
	@Column(name = "COMPANYNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String companyName;
	
	/** 同一事業體資訊(行業代碼) **/
	@Size(max = 6)
	@Column(name = "COMPANYINDUSTRYCODE", length = 6, columnDefinition = "CHAR(6)")
	private String companyIndustryCode;
	
	/** 同一事業體資訊(行業代碼名稱) **/
	@Size(max = 52)
	@Column(name = "COMPANYINDUSTRYCODENAME", length = 52, columnDefinition = "VARCHAR(52)")
	private String companyIndustryCodeName;

	/**
	 * 是否動用<br/>
	 * 由動審表上傳時寫入<br/>
	 * Y|已動用<br/>
	 * 空白|未動用 <br/>
	 * **/
	@Size(max = 1)
	@Column(name = "ISUSE", length = 1, columnDefinition = "CHAR(1)")
	private String isUse;

	/**
	 * 取得是否動用<br/>
	 * 由動審表上傳時寫入<br/>
	 * Y|已動用<br/>
	 * 空白|未動用 <br/>
	 * **/
	public String getIsUse() {
		return isUse;
	}

	/**
	 * 設定是否動用<br/>
	 * 由動審表上傳時寫入<br/>
	 * Y|已動用<br/>
	 * 空白|未動用 <br/>
	 * **/
	public void setIsUse(String isUse) {
		this.isUse = isUse;
	}

	/**
	 * 動用日期 <br/>
	 * ※2013-04-24 新增欄位 由動審表上傳時寫入
	 * 
	 * */
	@Temporal(TemporalType.DATE)
	@Column(name = "isUseDate", columnDefinition = "DATE")
	private Date isUseDate;

	/**
	 * 動用日期 <br/>
	 * ※2013-04-24 新增欄位 由動審表上傳時寫入
	 * 
	 * */
	public Date getIsUseDate() {
		return isUseDate;
	}

	/**
	 * 動用日期 <br/>
	 * ※2013-04-24 新增欄位 由動審表上傳時寫入
	 * 
	 * */
	public void setIsUseDate(Date isUseDate) {
		this.isUseDate = isUseDate;
	}

	/**
	 * 
	 * 團貸編號<br/>
	 * ※2013-01-24 新增欄位<br/>
	 * 若簽報書為整批貸款<br/>
	 * 需於核准時填入團貸編號<br/>
	 * L120M01G.parentCntrNo <br/>
	 * 
	 * **/
	@Size(max = 12)
	@Column(name = "GRPCNTRNO", length = 12, columnDefinition = "CHAR(12)")
	private String grpCntrNo;

	/**
	 * 
	 * 取得團貸編號<br/>
	 * ※2013-01-24 新增欄位<br/>
	 * 若簽報書為整批貸款<br/>
	 * 需於核准時填入團貸編號<br/>
	 * L120M01G.parentCntrNo <br/>
	 * 
	 * **/

	public String getGrpCntrNo() {
		return grpCntrNo;
	}

	/**
	 * 
	 * 設定團貸編號<br/>
	 * ※2013-01-24 新增欄位<br/>
	 * 若簽報書為整批貸款<br/>
	 * 需於核准時填入團貸編號<br/>
	 * L120M01G.parentCntrNo <br/>
	 * 
	 * **/
	public void setGrpCntrNo(String grpCntrNo) {
		this.grpCntrNo = grpCntrNo;
	}

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/** 以基金贖回款為還款來源之授信 **/
	@Size(max = 1)
	@Column(name = "REPAYFUND", length = 1, columnDefinition = "CHAR(1)")
	private String repayFund;
	
	/** 扣薪福委會代號 **/
	@Size(max = 3)
	@Column(name = "WELFARE_CMTE", length = 3, columnDefinition = "CHAR(3)")
	private String welfare_cmte;
	
	/**
	 * 青創貸款用途(企金在L140M01A, 消金在L140M03A)
	 */
	@Column(name = "YOPURPOSE", length = 1, columnDefinition = "CHAR(1)")
	private String yoPurpose;

	/**
	 * 補貼單位(企金在L140M01A, 消金在L140M03A)
	 */
	@Column(name = "SUBSIDYUT", length = 2, columnDefinition = "VARCHAR(2)")
	private String subSidyut;

	/**
	 * J-112-0206 歡喜信貸員工認股貸款適用條款
	 * <p/>
	 * Y/N
	 * N:無
	 * Y:有，離職後視為全部到期，應請客戶還款
	 */
	@Size(max = 1)
	@Column(name = "STAFFRULEYN", length = 1, columnDefinition = "VARCHAR(1)")
	private String staffRuleYN;
	
	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得mainId **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定mainId **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得不計入同一關係戶代號
	 * <p/>
	 * 不計入同一關係戶代號 <br/>
	 * 1: 配合政府政策經財政部或央行核准之專案授信 <br/>
	 * 2: 對政府機關之授信 <br/>
	 * 3: 以公債國庫券央行儲券可轉讓存單本行存單或債券 <br/>
	 * 4: 推動小額放款業務要點之台幣一百萬元以下之授信 <br/>
	 * 5: 配合政府不列同一人限額但需列入同一關係人企業
	 */
	public String getIdentityNo() {
		return this.identityNo;
	}

	/**
	 * 設定不計入同一關係戶代號
	 * <p/>
	 * 不計入同一關係戶代號 <br/>
	 * 1: 配合政府政策經財政部或央行核准之專案授信 <br/>
	 * 2: 對政府機關之授信 <br/>
	 * 3: 以公債國庫券央行儲券可轉讓存單本行存單或債券 <br/>
	 * 4: 推動小額放款業務要點之台幣一百萬元以下之授信 <br/>
	 * 5: 配合政府不列同一人限額但需列入同一關係人企業
	 **/
	public void setIdentityNo(String value) {
		this.identityNo = value;
	}

	/**
	 * 取得單據寄發方式
	 * <p/>
	 * 1:郵寄通訊地址<br/>
	 * 9:不印授信對帳單
	 */
	public String getNoticeType() {
		return this.noticeType;
	}

	/**
	 * 設定單據寄發方式
	 * <p/>
	 * 1:郵寄通訊地址<br/>
	 * 9:不印授信對帳單
	 **/
	public void setNoticeType(String value) {
		this.noticeType = value;
	}

	/**
	 * 取得是否屬於青創
	 * <p/>
	 * Y/N
	 */
	public String getYounCreatYN() {
		return this.younCreatYN;
	}

	/**
	 * 設定是否屬於青創
	 * <p/>
	 * Y/N
	 **/
	public void setYounCreatYN(String value) {
		this.younCreatYN = value;
	}

	/** 取得登記出資額 **/
	public BigDecimal getCapitalAMT() {
		return this.capitalAMT;
	}

	/** 設定登記出資額 **/
	public void setCapitalAMT(BigDecimal value) {
		this.capitalAMT = value;
	}

	/** 取得設立登記日期 **/
	public Date getRegisterDate() {
		return this.registerDate;
	}

	/** 設定設立登記日期 **/
	public void setRegisterDate(Date value) {
		this.registerDate = value;
	}

	/**
	 * 取得當younCreatYN=Y時，{1:屬優予核貸對象, 產品60青年築夢創業啟動金貸款, 2:為育成中心輔導, 產品58青年創業貸款, 3:產品61青年創業及啟動金貸款}
	 */
	public String getAssisType() {
		return this.assisType;
	}

	/**
	 * 設定當younCreatYN=Y時，{1:屬優予核貸對象, 產品60青年築夢創業啟動金貸款, 2:為育成中心輔導, 產品58青年創業貸款, 3:產品61青年創業及啟動金貸款}
	 **/
	public void setAssisType(String value) {
		this.assisType = value;
	}

	/**
	 * 取得是否屬優予核貸對象/是否為育成中心輔導是否
	 * <p/>
	 * Y/N
	 */
	public String getAssisTypeYN() {
		return this.assisTypeYN;
	}

	/**
	 * 設定是否屬優予核貸對象/是否為育成中心輔導是否
	 * <p/>
	 * Y/N
	 **/
	public void setAssisTypeYN(String value) {
		this.assisTypeYN = value;
	}

	/**
	 * 取得企業所在縣市
	 * <p/>
	 * ※是否屬優予核貸對象有值時才顯示<br/>
	 * codetype=counties
	 */
	public String getCompanyCity() {
		return this.companyCity;
	}

	/**
	 * 設定企業所在縣市
	 * <p/>
	 * ※是否屬優予核貸對象有值時才顯示<br/>
	 * codetype=counties
	 **/
	public void setCompanyCity(String value) {
		this.companyCity = value;
	}

	/**
	 * 取得企業所在鄉鎮市區
	 * <p/>
	 * ※是否屬優予核貸對象有值時才顯示<br/>
	 * Codetype=counties+ L140M03A. companyCity
	 */
	public String getCompanyArea() {
		return this.companyArea;
	}

	/**
	 * 設定企業所在鄉鎮市區
	 * <p/>
	 * ※是否屬優予核貸對象有值時才顯示<br/>
	 * Codetype=counties+ L140M03A. companyCity
	 **/
	public void setCompanyArea(String value) {
		this.companyArea = value;
	}

	/**
	 * 取得同一事業體資訊(ID)
	 * <p/>
	 * 限企業戶統編
	 */
	public String getCompanyId() {
		return this.companyId;
	}

	/**
	 * 設定同一事業體資訊(ID)
	 * <p/>
	 * 限企業戶統編
	 **/
	public void setCompanyId(String value) {
		this.companyId = value;
	}

	/** 取得同一事業體資訊(dupNo) **/
	public String getCompanyDupNo() {
		return this.companyDupNo;
	}

	/** 設定同一事業體資訊(dupNo) **/
	public void setCompanyDupNo(String value) {
		this.companyDupNo = value;
	}

	/** 取得同一事業體資訊(名稱) **/
	public String getCompanyName() {
		return this.companyName;
	}

	/** 設定同一事業體資訊(名稱) **/
	public void setCompanyName(String value) {
		this.companyName = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/**
	 * JOIN條件
	 * 
	 */
	@OneToOne(cascade = CascadeType.PERSIST, fetch = FetchType.EAGER)
	@JoinColumns({ @JoinColumn(name = "mainId", referencedColumnName = "mainId", nullable = true, insertable=false, updatable=true) })
	private L140M01A l140m01a;

	public L140M01A getL140m01a() {
		return l140m01a;
	}

	public void setL140m01a(L140M01A l140m01a) {
		this.l140m01a = l140m01a;
	}

	/** 設定申請日期 **/
	public void setApplicationDate(Date value) {
		this.applicationDate = value;
	}
	/** 取得申請日期 **/
	public Date getApplicationDate() {
		return this.applicationDate;
	}
	
	/** 取得基金為還款來源之個人短期無擔保貸款 **/
	public String getRepayFund() {
		return this.repayFund;
	}

	/** 設定基金為還款來源之個人短期無擔保貸款 **/
	public void setRepayFund(String value) {
		this.repayFund = value;
	}

	public String getWelfare_cmte() {
		return welfare_cmte;
	}

	public void setWelfare_cmte(String welfare_cmte) {
		this.welfare_cmte = welfare_cmte;
	}
	
	/**
	 * 設定 青創貸款用途
	 * 
	 * @param yoPurpose
	 */
	public void setYoPurpose(String yoPurpose) {
		this.yoPurpose = yoPurpose;
	}

	/**
	 * 取得青創貸款用途
	 * 
	 * @return
	 */
	public String getYoPurpose() {
		return yoPurpose;
	}

	/**
	 * 設定 補貼單位
	 * 
	 * @param subSidyut
	 */
	public void setSubSidyut(String subSidyut) {
		this.subSidyut = subSidyut;
	}

	/**
	 * 取得補貼單位
	 * 
	 * @return
	 */
	public String getSubSidyut() {
		return subSidyut;
	}

	public String getCompanyIndustryCode() {
		return companyIndustryCode;
	}

	public void setCompanyIndustryCode(String companyIndustryCode) {
		this.companyIndustryCode = companyIndustryCode;
	}

	public String getCompanyIndustryCodeName() {
		return companyIndustryCodeName;
	}

	public void setCompanyIndustryCodeName(String companyIndustryCodeName) {
		this.companyIndustryCodeName = companyIndustryCodeName;
	}

	/**
	 * 取得歡喜信貸員工認股貸款適用條款
	 * 
	 * @return
	 */
	public String getStaffRuleYN() {
		return staffRuleYN;
	}

	/**
	 * 設定 歡喜信貸員工認股貸款適用條款
	 * 
	 * @param staffRuleYN
	 */
	public void setStaffRuleYN(String staffRuleYN) {
		this.staffRuleYN = staffRuleYN;
	}
	
}
