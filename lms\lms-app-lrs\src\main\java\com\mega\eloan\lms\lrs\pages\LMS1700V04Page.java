package com.mega.eloan.lms.lrs.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.base.panels.RetrialPtMgrIdPanel;
import com.mega.eloan.lms.lrs.panels.LMS1700FilterPanel;

/**
 * 覆審單位-編製中(當週)
 */
@Controller
@RequestMapping("/lrs/lms1700v04")
public class LMS1700V04Page extends AbstractEloanInnerView {

	public LMS1700V04Page() {
		super();
	}

	@Override
	public void execute(ModelMap model, PageParameters params) {

		setGridViewStatus(RetrialDocStatusEnum.區中心_編製中);
		addToButtonPanel(model, LmsButtonEnum.Filter, LmsButtonEnum.BatchTask,
				LmsButtonEnum.Add, LmsButtonEnum.Delete,
				LmsButtonEnum.Maintain);
		renderJsI18N(LMS1700V01Page.class);
		renderJsI18N(LMS1700M01Page.class);
		new RetrialPtMgrIdPanel("divRetrialPtMgrIdPanel").processPanelData(model, params);
		new LMS1700FilterPanel("filterBox", true).processPanelData(model, params);
		model.addAttribute("loadScript", "require(['pagejs/lrs/LMS1700FilterPanel'],function(){loadScript('pagejs/lrs/LMS1700V01Page');});");
		setupIPanel(new LMS1700FilterPanel(PANEL_ID, true), model, params);
	}

	protected void addPanel(ModelMap model, PageParameters params, String panelId) {
		new LMS1700FilterPanel(panelId, true).processPanelData(model, params);
	}
}
