/* 
 * LMS1805FormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lrs.handler.form;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.TreeMap;

import javax.annotation.Resource;

import org.apache.commons.lang.StringEscapeUtils;
import com.iisigroup.cap.component.PageParameters;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.BranchTypeEnum;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.Bstbl;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.model.DocOpener;
import com.mega.eloan.common.model.DocOpener.OpenTypeCode;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.BstblService;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.base.common.LrsUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.base.service.NumberService;
import com.mega.eloan.lms.lrs.pages.LMS1805M01Page;
import com.mega.eloan.lms.lrs.pages.LMS1815M01Page;
import com.mega.eloan.lms.lrs.service.LMS1805Service;
import com.mega.eloan.lms.lrs.service.LMS1835Service;
import com.mega.eloan.lms.model.L180M01A;
import com.mega.eloan.lms.model.L180M01B;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 覆審交易
 * </pre>
 * 
 * @since 2011/8
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/9/6,irene
 *          </ul>
 **/
@Scope("request")
@Controller("lms1805formhandler")
@DomainClass(L180M01A.class)
public class LMS1805M01FormHandler extends AbstractFormHandler {

	private static Logger logger = LoggerFactory
			.getLogger(LMS1805M01FormHandler.class);

	@Resource
	DocCheckService docCheckService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	LMS1835Service service1835;

	@Resource
	LMS1805Service service;

	@Resource
	BranchService branch;

	@Resource
	NumberService numberService;

	@Resource
	TempDataService tempDataService;

	@Autowired
	DocFileService fileService;

	@Resource
	CodeTypeService codeTypeService;

	@Resource
	BstblService bstblService;

	Properties lms1805m01 = MessageBundleScriptCreator
			.getComponentResource(LMS1805M01Page.class);
	Properties lms1815m01 = MessageBundleScriptCreator
			.getComponentResource(LMS1815M01Page.class);
	Properties abstractEloan = MessageBundleScriptCreator
			.getComponentResource(AbstractEloanPage.class);

	/**
	 * 查詢
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 **/
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult query(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.MAIN_OID);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		L180M01A l180m01a = service.findModelByOid(L180M01A.class, oid);
		switch (page) {
		case 1:
			if (!CapString.isEmpty(oid)) {
				if (l180m01a != null) {
					l180m01a.setBranchId(l180m01a.getBranchId() + " "
							+ branch.getBranchName(l180m01a.getBranchId()));
					result = DataParse.toResult(l180m01a);
					if (l180m01a.getDataDate() != null) {
						result.set("DataDate", CapDate.formatDateFromF1ToF2(
								Util.addZeroWithValue(
										TWNDate.toAD(l180m01a.getDataDate()),
										10),
								UtilConstants.DateFormat.YYYY_MM_DD,
								UtilConstants.DateFormat.YYYY_MM));
					}
					result.set(EloanConstants.MAIN_ID, l180m01a.getMainId());
					result.set("appraiser", user.getLightId());
					result.set(
							"status",
							abstractEloan.getProperty("docStatus."
									+ Util.trim(l180m01a.getDocStatus())));
					result.set("Creator",
							userInfoService.getUserName(l180m01a.getCreator()));
					result.set("randomCode", l180m01a.getRandomCode());
					if (!Util.isEmpty(l180m01a.getBatchNO())) {
						result.set("batchNO",
								Util.addZeroWithValue(l180m01a.getBatchNO(), 3));
					}
					String creator = userInfoService.getUserName(l180m01a
							.getCreator());
					String updater = userInfoService.getUserName(l180m01a
							.getUpdater());
					if (!Util.isEmpty(creator)) {
					} else {
						creator = l180m01a.getCreator();
					}
					String updaterC = "";
					if (!Util.isEmpty(updater)) {
						updaterC = updater;
					} else {
						updater = l180m01a.getUpdater();
					}
					if (!Util.isEmpty(l180m01a.getApprover())) {
						String approver = userInfoService.getUserName(l180m01a
								.getApprover());
						result.set("approvercn",
								Util.isEmpty(approver) ? l180m01a.getApprover()
										: l180m01a.getApprover() + approver);
					}
					result.set(
							"Creator",
							Util.nullToSpace(l180m01a.getCreator())
									+ " "
									+ creator
									+ "("
									+ Util.nullToSpace(TWNDate
											.toFullAD(l180m01a.getCreateTime()))
									+ ")");
					result.set(
							"Updater",
							Util.nullToSpace(l180m01a.getUpdater())
									+ " "
									+ updater
									+ "("
									+ Util.nullToSpace(TWNDate
											.toFullAD(l180m01a.getUpdateTime()))
									+ ")");
					result.set("apprId",
							Util.nullToSpace(l180m01a.getUpdater()) + " "
									+ updaterC);
					if (!Util.isEmpty(l180m01a.getBossId())) {
						String bossC = userInfoService.getUserName(l180m01a
								.getBossId());
						result.set("approvercn",
								Util.nullToSpace(l180m01a.getBossId()) + " "
										+ bossC);
					}
				}
			}
			break;
		case 2:
			if (!CapString.isEmpty(oid)) {
				if (l180m01a != null) {
					result = DataParse.toResult(l180m01a);
					result.set(EloanConstants.MAIN_ID, l180m01a.getMainId());
					result.set(
							"status",
							abstractEloan.getProperty("docStatus."
									+ Util.trim(l180m01a.getDocStatus())));
				}
			}
			break;
		default:
			break;
		}

		result.set(EloanConstants.MAIN_OID, CapString.trimNull(oid));
		result.set(EloanConstants.PAGE, page);
		return result;
	}

	/**
	 * 明細搜尋
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryList(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.MAIN_OID);
		L180M01B l180m01b = service.findModelByOid(L180M01B.class, oid);
		if (!CapString.isEmpty(oid)) {
			if (l180m01b != null) {
				result = DataParse.toResult(l180m01b, DataParse.Delete,
						EloanConstants.OID);
				// 不覆審代碼
				Map<String, String> mapNckd = codeTypeService
						.findByCodeType("lms1815m01_elfNCkdFlag");
				if (!Util.isEmpty(Util.trim(l180m01b.getElfNCkdFlag()))) {
					if (!Util.isEmpty(mapNckd)) {
						result.set("elfNCkdFlag", mapNckd.get(Util
								.trim(l180m01b.getElfNCkdFlag())));
					}
				}
				if (!Util.isEmpty(Util.trim(l180m01b.getNewNCkdFlag()))) {
					if (!Util.isEmpty(mapNckd)) {
						result.set("newNCkdFlag", mapNckd.get(Util
								.trim(l180m01b.getNewNCkdFlag())));
						result.set("nCkdFlag",
								Util.trim(l180m01b.getNewNCkdFlag()));
					}
				}
				// 覆審週期
				if (!Util.isEmpty(Util.trim(l180m01b.getElfRCkdLine()))) {
					Map<String, String> map = codeTypeService
							.findByCodeType("lms1815m01_elfRCkdLine");
					if (!Util.isEmpty(map)) {
						result.set("elfRCkdLine",
								map.get(Util.trim(l180m01b.getElfRCkdLine())));
					}
				}
				// 異動代碼
				if (!Util.isEmpty(Util.trim(l180m01b.getElfMDFlag()))) {
					Map<String, String> map = codeTypeService
							.findByCodeType("lms1815m01_elfMDFlag");
					if (!Util.isEmpty(map)) {
						result.set("elfMDFlag",
								map.get(Util.trim(l180m01b.getElfMDFlag())));
					}
				}
				// 行業別
				Bstbl bstbl = bstblService.findByEcocd(l180m01b.getECoNm());
				if (bstbl != null) {
					result.set("eCoNm", bstbl.getEconm());
				}
				// 戶況
				if (!Util.isEmpty(Util.trim(l180m01b.getElfCState()))) {
					result.set("elfCState",
							lms1815m01.get("cstate" + l180m01b.getElfCState())
									.toString());
				}
				// 新作/增額/逾期轉正
				if (!Util.isEmpty(l180m01b.getElfNewAdd())) {
					Map<String, String> map = codeTypeService
							.findByCodeType("lms1805_newAdd");
					if (!Util.isEmpty(map)) {
						result.set("elfNewAdd",
								map.get(Util.trim(l180m01b.getElfNewAdd())));
					}
				}
				// 異動人員
				if (!Util.isEmpty(l180m01b.getUpdater())) {
					result.set(
							"updater",
							(Util.isEmpty(userInfoService.getUserName(l180m01b
									.getUpdater())) ? l180m01b.getUpdater()
									: l180m01b.getUpdater()
											+ " "
											+ userInfoService
													.getUserName(l180m01b
															.getUpdater())));
				}
				// 異動日期
				if (!Util.isEmpty(l180m01b.getUpdateTime())) {
					result.set("updateTime",
							TWNDate.toFullAD(l180m01b.getUpdateTime()));
				}
				// 資信評等類別
				if (!Util.isEmpty(Util.trim(l180m01b.getElfCrdType()))) {
					Map<String, String> map = codeTypeService
							.findByCodeType("lms1705s01_crdType");
					if (map != null) {
						String elfCrdType = "";
						if ("O".equals(Util.trim(l180m01b.getElfCrdType()))) {
							elfCrdType = Util.trim(l180m01b.getElfCrdType())
									+ "U";
						} else {
							elfCrdType = "D"
									+ Util.trim(l180m01b.getElfCrdType());
						}
						result.set("elfCrdType", map.get(elfCrdType));
					}
				}
				// 信用模型評等類別
				if (!Util.isEmpty(l180m01b.getElfMowType())) {
					Map<String, String> map = codeTypeService
							.findByCodeType("lms1705s01_crdType2");
					if (map != null) {
						result.set("elfMowType",
								map.get(Util.trim(l180m01b.getElfMowType())));
					}
				}
				// 主要授信戶
				if (!Util.isEmpty(l180m01b.getElfMainCust())) {
					if (UtilConstants.DEFAULT.是.equals(l180m01b
							.getElfMainCust())) {
						result.set("elfMainCust",
								abstractEloan.getProperty("yes"));
					} else if (UtilConstants.DEFAULT.否.equals(l180m01b
							.getElfMainCust())) {
						result.set("elfMainCust",
								abstractEloan.getProperty("no"));
					}
				}
				// 新作年月
				if (!Util.isEmpty(l180m01b.getElfNewDate())
						&& l180m01b.getElfNewDate().length() > 4) {
					result.set("elfNewDate", l180m01b.getElfNewDate()
							.substring(0, 4)
							+ "-"
							+ l180m01b.getElfNewDate().substring(4));
				}
				// 主管機關指定覆審案件
				if (!Util.isEmpty(l180m01b.getElfUCkdLINE())) {
					if (UtilConstants.DEFAULT.是.equals(Util.trim(l180m01b
							.getElfUCkdLINE()))) {
						result.set("elfUCkdLINE",
								abstractEloan.getProperty("yes"));
					} else if (UtilConstants.DEFAULT.否.equals(Util
							.trim(l180m01b.getElfUCkdLINE()))) {
						result.set("elfUCkdLINE",
								abstractEloan.getProperty("no"));
					}
				}
				// DBUOBU
				if (!Util.isEmpty(l180m01b.getElfDBUOBU())) {
					if (UtilConstants.DEFAULT.是.equals(Util.trim(l180m01b
							.getElfDBUOBU()))) {
						result.set("elfDBUOBU",
								abstractEloan.getProperty("yes"));
					} else if (UtilConstants.DEFAULT.否.equals(Util
							.trim(l180m01b.getElfDBUOBU()))) {
						result.set("elfDBUOBU", "");
					}
				}
				String Listcreate = null;
				if ("SYS".equals(l180m01b.getCreateBY())) {
					Listcreate = lms1805m01.getProperty("systemProduce");
				} else if ("PEO".equals(l180m01b.getCreateBY())) {
					Listcreate = lms1805m01.getProperty("peopleProduce");
				}
				result.set("Listcreate", Listcreate);

				String ListStatus = null;
				if ("1".equals(l180m01b.getDocStatus1())) {
					ListStatus = lms1805m01.getProperty("needRetrial");
				} else if ("2".equals(l180m01b.getDocStatus1())) {
					ListStatus = lms1805m01.getProperty("noNeedRetrial");
				}
				result.set("ListStatus", ListStatus);

				String ListtypCd = "";
				if ("5".equals(l180m01b.getTypCd())) {
					ListtypCd = lms1805m01.getProperty("typCd");
				}
				result.set("ListtypCd", ListtypCd);
			}
		}

		return result;
	}

	/**
	 * 儲存
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(AuthType.Modify)
	public IResult save(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		int page = Util.parseInt(params.getString("page"));
		String oid = params.getString(EloanConstants.MAIN_OID);
		String mainId = params.getString(EloanConstants.MAIN_ID);

		L180M01A l180m01a = service.findModelByOid(L180M01A.class, oid);
		switch (page) {
		case 1:
			String formL180m01a = params.getString("L180M01AForm");
			JSONObject jsonL1805m01a = JSONObject.fromObject(formL180m01a);
			jsonL1805m01a.remove("createTime");
			DataParse.toBean(jsonL1805m01a, l180m01a);
			// String rCode = IDGenerator.getRandomCode();
			// l180m01a.setRandomCode(rCode);
			SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
					params.getString("tempSave", "N"));

			service.save(l180m01a);
			L180M01A l180m01a2 = service.findL180m01aByMainId(mainId);
			result.set("oid", oid);
			result.set(EloanConstants.MAIN_ID, l180m01a.getMainId());
			result.set("randomCode", l180m01a2.getRandomCode());
			result.set(
					"Creator",
					!Util.isEmpty(userInfoService.getUserName(l180m01a
							.getCreator())) ? userInfoService
							.getUserName(l180m01a.getCreator()) : l180m01a
							.getCreator()
							+ "("
							+ CapDate.parseToString(l180m01a.getCreateTime())
							+ ")");
			result.set(
					"Updater",
					!Util.isEmpty(userInfoService.getUserName(l180m01a
							.getUpdater())) ? userInfoService
							.getUserName(l180m01a.getUpdater()) : l180m01a
							.getUpdater()
							+ "("
							+ CapDate.parseToString(l180m01a.getUpdateTime())
							+ ")");
			result.set(
					"apprId",
					!Util.isEmpty(userInfoService.getUserName(l180m01a
							.getUpdater())) ? userInfoService
							.getUserName(l180m01a.getUpdater()) : l180m01a
							.getUpdater()
							+ "("
							+ CapDate.parseToString(l180m01a.getUpdateTime())
							+ ")");
			break;
		}
		if (!"Y".equals(SimpleContextHolder.get(EloanConstants.TEMPSAVE_RUN))) {
			tempDataService.deleteByMainId(l180m01a.getMainId());
		}
		if (params.getAsBoolean("showMsg", true)) {
			// EFD0017=儲存成功
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0017"));
		}
		return result;
	}

	// @DomainAuth(AuthType.Modify)
	public IResult tempSave(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		GenericBean model = collectionData(params);
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", "Y"));

		try {
			service.save(model);
		} catch (Exception e) {
			Properties pop2 = MessageBundleScriptCreator
					.getComponentResource(LMS1805M01Page.class);
			throw new CapMessageException(RespMsgHelper.getMessage(
					"EFD0025", pop2.getProperty("L1805G.error1")), getClass());
		}

		return result;
	}

	private GenericBean collectionData(PageParameters params)
			throws CapException {

		int page = Util.parseInt(params.getString("page"));
		String oid = params.getString(EloanConstants.MAIN_OID);
		L180M01A l180m01a = service.findModelByOid(L180M01A.class, oid);

		if (page == 1) {
			String formL180m01a = params.getString("L180M01AForm");
			JSONObject jsonL1805m01a = JSONObject.fromObject(formL180m01a);
			DataParse.toBean(jsonL1805m01a, l180m01a);
		}

		return l180m01a;
	}

	/**
	 * 明細儲存
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveList(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.MAIN_OID);
		L180M01B l180m01b = service.findModelByOid(L180M01B.class, oid);

		String L180M01BForm = params.getString("L180M01BForm");
		JSONObject lms1805s0201Json = JSONObject.fromObject(L180M01BForm);
		DataParse.toBean(lms1805s0201Json, l180m01b);

		if ("8".equals(Util.trim(l180m01b.getNewNCkdFlag()))) {
			Date newNextNwDt = l180m01b.getNewNextNwDt();
			if (Util.isEmpty(newNextNwDt)) {
				throw new CapMessageException(
						lms1805m01.getProperty("noNextDate"), getClass());
			} else if (CapDate.parseDate(CapDate.getCurrentDate("yyyy-MM-dd"))
					.after(newNextNwDt)) {
				throw new CapMessageException(
						lms1805m01.getProperty("err.hisDate"), getClass());
			} else {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("NCKDFLAG", l180m01b.getNewNCkdFlag());
				map.put("LRDATE", l180m01b.getElfLRDate());
				map.put("MAINCUST", l180m01b.getElfMainCust());
				map.put("MOWTBL1", l180m01b.getElfMowTbl1());
				map.put("MOWTYPE", l180m01b.getElfMowType());
				map.put("CRDTTBL", l180m01b.getElfCrdTTbl());
				map.put("NEWDATE", l180m01b.getElfNewDate());
				map.put("MDFLAG", l180m01b.getElfMDFlag());
				map.put("RCKDLINE", l180m01b.getElfRCkdLine());
				map.put("MDDT", l180m01b.getElfMDDt());
				map.put("UCKDLINE", l180m01b.getElfUCkdLINE());
				map.put("UCKDDT", l180m01b.getElfUCkdDt());
				String[] value = service.cauculateDate(null, map, 3);
				if ((CapDate.parseDate(value[0])).before(newNextNwDt)) {
					l180m01b.setNewNextNwDt(newNextNwDt);
					service.save(l180m01b);
					throw new CapMessageException(
							lms1805m01.getProperty("cancelFail")
									+ TWNDate.toAD(Util.parseDate(value[0])),
							getClass());
				}
			}
		}

		service.save(l180m01b);

		if (params.getAsBoolean("showMsg", true)) {
			// EFD0017=儲存成功
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0017"));
		}
		return result;
	}

	/**
	 * 刪除
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(AuthType.Modify)
	public IResult deleteList(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] list = params.getStringArray("list");
		boolean edit = false;
		for (String oid : list) {
			L180M01A l180m01a = service.findModelByOid(L180M01A.class, oid);
			List<DocOpener> docOpeners = docCheckService.findByMainId(l180m01a
					.getMainId());
			for (DocOpener docOpener : docOpeners) {
				if (OpenTypeCode.Writing.getCode().equals(
						docOpener.getOpenType())) {
					HashMap<String, String> hm = new HashMap<String, String>();
					hm.put("userId", docOpener.getOpener());
					hm.put("userName",
							userInfoService.getUserName(docOpener.getOpener()));
					edit = true;
					result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMessage("EFD0009", hm));
					break;
				}
			}
			if (edit) {
				break;
			}
		}
		if (!edit) {
			service.deleteL180m01aList(list);
			// EFD0019=刪除成功
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0019"));
		}
		return result;
	}

	/**
	 * 搜尋分行
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 **/
	public IResult queryBranch(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		Map<String, String> m = new TreeMap<String, String>();
		List<IBranch> bank = branch.getBranchOfGroup(user.getUnitNo());
		bank.add(branch.getBranch(user.getUnitNo()));
		for (IBranch b : bank) {
			String brName = Util.trim(b.getBrName());
			String brCode = b.getBrNo();
			m.put(brCode, brName);
		}

		CapAjaxFormResult bankList = new CapAjaxFormResult(m);
		result.set("item", bankList);
		return result;
	}

	/**
	 * 修改預計覆審日
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 **/
	public IResult changeDefaultDate(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] list = params.getStringArray("list");
		String defaultCTLDate = Util.nullToSpace(params
				.getString("defaultCTLDate"));
		service.saveL180m01aDate(list, Util.parseDate(defaultCTLDate));

		// EFD0017=儲存成功
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0017"));
		return result;
	}

	/**
	 * <pre>
	 * 取消覆審
	 * </pre>
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult cancel(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		StringBuilder error = new StringBuilder();
		String[] oids = params.getStringArray("list");
		String nckdflag = Util.nullToSpace(params.getString("elf412Nckdflag"));
		Date newNextNwDt = Util.parseDate(params.getString("newNextNwDt", ""));
		if (!Util.isEmpty(newNextNwDt)
				&& CapDate.parseDate(CapDate.getCurrentDate("yyyy-MM-dd"))
						.after(newNextNwDt)) {
			throw new CapMessageException(
					lms1805m01.getProperty("err.hisDate"), getClass());
		}

		for (String list : oids) {
			L180M01B l180m01b = service.findModelByOid(L180M01B.class, list);

			Map<String, Object> map = new HashMap<String, Object>();
			map.put("NCKDFLAG", l180m01b.getNewNCkdFlag());
			map.put("LRDATE", l180m01b.getElfLRDate());
			map.put("MAINCUST", l180m01b.getElfMainCust());
			map.put("MOWTBL1", l180m01b.getElfMowTbl1());
			map.put("MOWTYPE", l180m01b.getElfMowType());
			map.put("CRDTTBL", l180m01b.getElfCrdTTbl());
			map.put("NEWDATE", l180m01b.getElfNewDate());
			map.put("MDFLAG", l180m01b.getElfMDFlag());
			map.put("RCKDLINE", l180m01b.getElfRCkdLine());
			map.put("MDDT", l180m01b.getElfMDDt());
			map.put("UCKDLINE", l180m01b.getElfUCkdLINE());
			map.put("UCKDDT", l180m01b.getElfUCkdDt());
			if ("8".equals(nckdflag)) {
				String[] value = service.cauculateDate(null, map, 3);
				Date date = newNextNwDt;
				// CapDate.parseDate(CapDate.addMonth(TWNDate.toAD().replace("-",
				// ""), -1))
				date = CapDate.parseDate(TWNDate.toAD(date).substring(0, 7)
						+ "-01");
				// System.out.println(TWNDate.toAD(date));
				if ((CapDate.parseDate(value[0])).before(date)) {
					error.append(Util.isEmpty(error.toString()) ? "" : "<BR/>");
					error.append("[" + Util.trim(l180m01b.getCustId()) + "]"
							+ lms1805m01.getProperty("cancelFail")
							+ TWNDate.toAD(Util.parseDate(value[0])));
					continue;
				}
			}
			service.saveL180m01bCancel(list, nckdflag, newNextNwDt);
		}
		if (Util.isEmpty(error.toString())) {
			// EFD0017=儲存成功
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0017"));
		} else {
			// 儲存失敗
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, error.toString());
		}

		return result;
	}

	/**
	 * 恢復取消覆審
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(AuthType.Modify)
	public IResult recancel(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] list = params.getStringArray("list");
		Date newLRDate = Util.isEmpty(params.getString("newLRDate")) ? null
				: CapDate.parseDate(params.getString("newLRDate"));

		service.saveL180m01bRecancel(list, newLRDate);

		// EFD0017=儲存成功
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0017"));
		return result;
	}

	/**
	 * 產生名單 (單筆)
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(AuthType.Modify)
	public IResult produce(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Date basedate = null;
		if (!params.getString("basedate").isEmpty()) {
			basedate = CapDate.parseDate(params.getString("basedate") + "-01");
		}
		String check = params.getString("check");
		String bank = Util.nullToSpace(params.getString("list"));
		// 整批產生需回傳失敗或成功分行
		StringBuilder failList = new StringBuilder();

		// 先檢查L180M01A是否有資料
		Map<String, Object> ch = service
				.findByBranchAnddataDate(bank, basedate);
		boolean st = (Boolean) ch.get("check");
		if (!"Y".equals(check) && !st) {
			// (提示"同一資料同一間分行未覆核名單已經有一筆名單，系統會刪除後再重新產生，是否執行")
			if (!Util.isEmpty(ch.get("mag"))) {
				result.set("mag", (String) ch.get("mag"));
			}
			result.set("check", "N");
			return result;
		}

		try {
			// 檢查是否更新完畢
			boolean sucess = service.checkOneUpdate(bank);
			if (!sucess) {
				failList.append(bank + lms1805m01.getProperty("updateFail"));
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
						failList.toString());
			} else {
				// 更新異常通報
				// TODO 500
				sucess = service.updateUnusual(bank);
				if (!sucess) {
					failList.append(bank
							+ lms1805m01.getProperty("unusualFail"));
					result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
							failList.toString());
				} else {
					// 產生名單
					// if ("Y".equals(check)) {
					// basedate =
					// CapDate.parseDate(CapDate.addMonth(TWNDate.toAD(basedate).replace("-",
					// ""), 1));
					sucess = service.produceList(basedate, bank, "2",
							user.getUnitNo());
					if (!sucess) {
						failList.append(bank
								+ lms1805m01.getProperty("produceFail"));
						result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
								failList.toString());
					} else {
						result.set(
								CapConstants.AJAX_NOTIFY_MESSAGE,
								RespMsgHelper.getMainMessage("EFD0018"));
					}
					// } else {
					// // 先檢查L180M01A是否有資料
					// Map<String, Object> ch = service
					// .findByBranchAnddataDate(bank, basedate);
					// boolean st = (Boolean) ch.get("check");
					// if (st) {
					// sucess = service.produceList(basedate, bank, "2",
					// user.getUnitNo());
					// if (!sucess) {
					// failList.append(bank
					// + lms1805m01.getProperty("produceFail"));
					// result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
					// failList.toString());
					// } else {
					// result.set(
					// CapConstants.AJAX_NOTIFY_MESSAGE,
					// RespMsgHelper.getMainMessage(
					// this.getComponent(), "EFD0018"));
					// }
					// } else {
					// // not True
					// // (提示"同一資料同一間分行未覆核名單已經有一筆名單，系統會刪除後再重新產生，是否執行")
					// result.set("check", "N");
					// if (!Util.isEmpty(ch.get("mag"))) {
					// result.set("mag", (String) ch.get("mag"));
					// }
					// }
				}
			}
			// }
		} catch (Exception e) {
			logger.error("LMS1805M01FormHandler produce EXCEPTION!!", e);
		}

		return result;
	}

	/**
	 * 產生名單-整批產生
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult produceMany(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String bankList = params.getString("list");
		JSONObject jsonBank = JSONObject.fromObject(bankList);
		JSONArray banks = jsonBank.names();
		for (int i = 0, size = banks.size(); i < size; i++) {
			if (!service.checkOneUpdate((String) banks.get(i))) {
				throw new CapMessageException(banks.get(i)
						+ lms1805m01.getProperty("updateFail"), getClass());
			}
		}

		StringBuilder mag = new StringBuilder();
		for (int i = 0, size = banks.size(); i < size; i++) {
			try {
				boolean success = service.updateUnusual((String) banks.get(i));
				if (success) {
					success = service.produceList(
							CapDate.parseDate(((String) jsonBank.get(banks
									.get(i)) + "-01")), (String) banks.get(i),
							"2", user.getUnitNo());
					if (!success) {
						mag.append((String) banks.get(i)
								+ lms1805m01.getProperty("produceFail"));
					}
				} else {
					mag.append((String) banks.get(i)
							+ lms1805m01.getProperty("unusualFail"));
				}
			} catch (Exception e) {
				mag.append((String) banks.get(i)
						+ lms1805m01.getProperty("produceFail"));
				logger.error("LMS1805M01FormHandler produceMany EXCEPTION!!", e);
			}
		}
		if (!Util.isEmpty(mag.toString())) {
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, mag.toString());
		}

		return result;
	}

	/**
	 * 新增覆審名單
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(AuthType.Modify)
	public IResult newList(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String MainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		String id = Util.nullToSpace(params.getString("id"));
		// 轉大寫
		id = id.toUpperCase();
		String dupno = Util.nullToSpace(params.getString("dupno"));
		String dataDate = Util.nullToSpace(params.getString("dataDate"));
		String ctlType = Util.equals(
				Util.nullToSpace(params.getString("ctlType")), "") ? LrsUtil.CTLTYPE_主辦覆審
				: Util.nullToSpace(params.getString("ctlType"));
		// 從主檔找銀行代碼
		L180M01A l180m01a = service.findL180m01aByMainId(MainId);
		String branchId = l180m01a.getBranchId();

		List<L180M01B> model = service.fingL180m01bByCustId(MainId, id, dupno,
				ctlType);
		if (!Util.isEmpty(model) && model.size() > 0) {
			throw new CapMessageException(
			// listAlreadyHave2=已有此筆名單。
					lms1805m01.getProperty("listAlreadyHave2"), getClass());
		}

		boolean sucess = service.produceNew(MainId, dupno, id,
				Util.parseDate(dataDate), branchId);
		if (!sucess) {
			throw new CapMessageException(
			// listAlreadyHave=覆審控制檔查無資料，請先至覆審控制檔完成建檔。
					lms1805m01.getProperty("listAlreadyHave"), getClass());
		}
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0035"));
		return result;
	}

	/**
	 * 更新ELF412
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(AuthType.Modify)
	public IResult updateElf412(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<IBranch> bank = branch.getBranchByUnitType(
				BranchTypeEnum.海外分行.getCode(),
				BranchTypeEnum.海外分行當地有總行.getCode(),
				BranchTypeEnum.海外總行泰國.getCode(),
				BranchTypeEnum.海外總行澳洲加拿大.getCode());
		bank.add(branch.getBranch(user.getUnitNo()));

		List<String> banks = new ArrayList<String>();
		for (int i = 0, size = bank.size(); i < size; i++) {
			banks.add(bank.get(i).getBrNo());
		}

		Date dataDate = CapDate.parseDate(CapDate
				.getCurrentDate(UtilConstants.DateFormat.YYYY_MM) + "-01");

		boolean updateOrNot = params.getBoolean("updateOrNot");
		if (!updateOrNot) {
			service.deleteL180M01Z(banks, dataDate);
		}
		StringBuilder failBanks = new StringBuilder();
		for (int i = 0, size = banks.size(); i < size; i++) {
			try {
				String failbank = service.updateResoure(banks.get(i), dataDate);
				if (failbank != null && !failbank.isEmpty()) {
					failBanks.append(failbank + ",");
				}
			} catch (Exception e) {
				failBanks.append(banks.get(i) + ",");
				logger.error("LMS1805M01FormHandler updateElf412 EXCEPTION!!",
						e);
			}
		}

		if (!Util.isEmpty(failBanks.toString())) {
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, failBanks.toString()
					+ "<BR/>" + lms1805m01.getProperty("updateFail"));
		} else {
			// 執行成功
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0018"));
		}

		return result;
	}

	/**
	 * 取號
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(AuthType.Modify)
	public IResult getNumber(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.MAIN_OID, "");
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", "N"));
		service.getNumber(oid);

		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0018"));
		return result;
	}

	/**
	 * 檢查是否更新完畢
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult checkUpdate(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<IBranch> banks = new ArrayList<IBranch>();
		if ("900".equals(user.getUnitNo())) {
			banks = branch.getBranchByUnitType(BranchTypeEnum.海外分行.getCode(),
					BranchTypeEnum.海外分行當地有總行.getCode(),
					BranchTypeEnum.海外總行泰國.getCode(),
					BranchTypeEnum.海外總行澳洲加拿大.getCode());
		} else {
			banks = branch.getBranchOfGroup(user.getUnitNo());
		}
		banks.add(branch.getBranch(user.getUnitNo()));
		StringBuilder returnVal = new StringBuilder();
		for (IBranch bank : banks) {
			if (!service.checkOneUpdate(bank.getBrNo())) {
				returnVal.append(bank.getBrNo() + bank.getBrName());
			}
		}
		if (Util.isEmpty(returnVal.toString())) {
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
					lms1805m01.getProperty("updateOK"));
		} else {
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, returnVal.toString()
					+ lms1805m01.getProperty("updateFail"));
		}

		return result;
	}

	/**
	 * 產生Excel
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult produceExcel(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.nullToSpace(params.getString(EloanConstants.OID));
		L180M01A l180m01a = service.findModelByOid(L180M01A.class, oid);
		try {
			boolean success = false;
			success = service.produceExcel(oid);
			if (success) {
				String xlsOid = l180m01a.getExcelFile();
				result.set("xlsOid", !Util.isEmpty(xlsOid) ? xlsOid : "");
				// 執行成功
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0018"));
			} else {
				Map<String, String> map = new HashMap<String, String>();
				map.put("msg", "[lms1805Service]");
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0025", map), getClass());
			}
		} catch (Exception e) {
			logger.error("LMS1805M01FormHandler produceExcel EXCEPTION!!", e);
			Map<String, String> map = new HashMap<String, String>();
			map.put("msg", "[lms1805m01formhandler]" + e.getMessage());
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0025", map), getClass());
		}

		return result;
	}

	/**
	 * 產生驗證Excel
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws Exception
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult produceChkExcel(PageParameters params)
			throws Exception {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.nullToSpace(params.getString(EloanConstants.OID));
		try {
			Map<String, Object> msg;
			msg = service.produceChkExcel(oid);
			if ((Boolean) msg.get("success")) {

				// 執行成功
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0018"));
			} else {
				throw new CapMessageException((String) msg.get("errorMag"),
						getClass());
			}
		} finally {

		}
		return result;
	}

	/**
	 * 依前次已傳送覆審名單改列本次為暫不覆審
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult reportCTL(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String exl180m01aOid = Util.nullToSpace(params.getString("exOid"));
		String l180m01aOid = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_OID));

		service.saveNckdFlag(exl180m01aOid, l180m01aOid);

		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0018"));
		return result;
	}

	/**
	 * 整批覆核
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(AuthType.Accept)
	public IResult flowCases(PageParameters params)
			throws CapException {

		CapAjaxFormResult r = new CapAjaxFormResult();
		// 儲存and檢核
		String[] oids = params.getStringArray("list");
		String defaultCTLDate = Util.nullToSpace(params
				.getString("defaultCTLDate"));
		Map<String, Object> map = service.flowCases(oids,
				Util.parseDate(defaultCTLDate), params.getBoolean("result"));
		if ((Boolean) map.get("success")) {
			r.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0018"));
		} else {
			Map<String, String> mapVal = new HashMap<String, String>();
			mapVal.put("msg", (String) map.get("msg"));
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0025", mapVal), getClass());
		}

		return r;
	}

	/**
	 * 呈主管覆核
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(AuthType.Modify + AuthType.Accept)
	public IResult flowAction(PageParameters params)
			throws CapException {
		CapAjaxFormResult r = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 儲存and檢核
		String mainId = params.getString(EloanConstants.MAIN_ID);
		L180M01A l180m01a = service.findL180m01aByMainId(mainId);

		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", "N"));

		if (RetrialDocStatusEnum.編製中.getCode().equals(l180m01a.getDocStatus())) {
			int page = Util.parseInt(params.getString(EloanConstants.PAGE));
			switch (page) {
			case 1:
				String formL180m01a = params.getString("L180M01AForm");
				JSONObject jsonL1805m01a = JSONObject.fromObject(formL180m01a);
				DataParse.toBean(jsonL1805m01a, l180m01a);
				l180m01a.setUpdater(user.getUserId());
				l180m01a.setUpdateTime(CapDate.getCurrentTimestamp());
				break;
			default:
				break;
			}
			this.service.getNumber(l180m01a.getOid());
		}
		if (!"Y".equals(SimpleContextHolder.get(EloanConstants.TEMPSAVE_RUN))) {
			tempDataService.deleteByMainId(l180m01a.getMainId());
		}

		// 檢查經辦和主管是否為同一人
		boolean decition = params.getAsBoolean("flowAction", false);
		if (decition && user.getUserId().equals(l180m01a.getUpdater())) {
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0053"), getClass());
		}

		if (l180m01a.getDefaultCTLDate() == null) {
			throw new CapMessageException(
					lms1805m01.getProperty("unWriteDate"), getClass());
		} else {
			try {
				logger.info("LMS1805Flow=======>Start");
				service.flowAction(StringEscapeUtils.escapeSql(l180m01a.getOid()), l180m01a,
						params.containsKey("flowAction"),
						params.getAsBoolean("flowAction", false));
				logger.info("LMS1805Flow=======>Finish");
			} catch (Throwable t1) {
				throw new CapMessageException(getMessage(t1.getMessage()),
						getClass());
			}
		}
		return r;
	}

	/**
	 * 找尋下載檔案路徑
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult downloadPath(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		int mode = params.getInt("mode");
		String oid = params.getString(EloanConstants.OID);
		L180M01A l180m01a = service.findModelByOid(L180M01A.class, oid);
		String mainId = l180m01a.getMainId();
		String fieldId = "";

		if (mode == 1) {
			if (RetrialDocStatusEnum.編製中.getCode().equals(
					l180m01a.getDocStatus())) {
				fieldId = "listSearch";
				// fileName.append("listSearch");
			} else if (RetrialDocStatusEnum.待覆核.getCode().equals(
					l180m01a.getDocStatus())) {
				fieldId = "listWait";
				// fileName.append("listWait");
			} else {
				fieldId = "listExcel";
				// fileName.append("listExcel");
			}
		} else if (mode == 2) {
			fieldId = "listChk";
			// fileName.append("listChk");
		}

		List<DocFile> docFiles = service1835.findDocFile(mainId, fieldId);
		String fileOid = (String) docFiles.get(0).getOid();
		result.set("fileOid", fileOid);

		return result;
	}

	/**
	 * 傳送報告表
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify + AuthType.Accept)
	public IResult sendReport(PageParameters params)
			throws CapException {
		CapAjaxFormResult r = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.MAIN_ID);
		String ctlDate = params.getString("ctlDate", "");
		boolean accountYN = params.getBoolean("accountYN");
		L180M01A l180m01a = service.findModelByOid(L180M01A.class, oid);

		if (l180m01a.getDefaultCTLDate() == null && Util.isEmpty(ctlDate)) {
			throw new CapMessageException(
					lms1805m01.getProperty("unWriteDate"), getClass());
		} else {
			service.produceReport(CapDate.parseDate(ctlDate), oid,
					accountYN);
		}

		return r;
	}
}
