package com.mega.eloan.lms.mfaloan.service.impl;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.mfaloan.service.MisLnunIdService;

@Service
public class MisLnunIdServiceImpl extends AbstractMFAloanJdbc implements
		MisLnunIdService {
	public List<Map<String, Object>> findLnunIdByCustId(String custId,
			String dupNo) {
		return this.getJdbc().queryForList("LNUNID.findByIdDupNo",
				new Object[] { custId, dupNo });
	}

	public List<Map<String, Object>> findLnunId02ByCustId(String custId,
			String dupNo) {
		return this.getJdbc().queryForList("LnunId02.queryByCustIdAndDupNo",
				new Object[] { custId, dupNo });
	}

	public Map<String, Object> findByIdDup(String custId, String dupNo) {
		return getJdbc().queryForMap("LNUNID.findByIdDupNo",
				"ORDER BY REGDT DESC", new Object[] { custId, dupNo });
	}

	@Override
	public List<Map<String, Object>> findReject(String custId, String dupNo) {
		return this.getJdbc().queryForList("LnunId.getReject",
				new Object[] { custId, dupNo });
	}

	@Override
	public List<Map<String, Object>> findLnunIdByCustIdCount(String custId) {
		return this.getJdbc().queryForList("LnunId.queryByCustId",
				new Object[] { custId });
	}

	@Override
	public void insertLnunId(String custId, String dupNo, Timestamp REGDT,
			String REGBR, String REGTELLER, int REFUSECD, String REFUSEDS,
			String RFSAUTH, String UPDATER, Timestamp TMESTAMP, String CLSCASE,
			String CARDREJ, String CUSTNM, String STATUSCD, String OID) {
		this.getJdbc().update(
				"LNUNID.INSERT",
				new Object[] { custId, dupNo, REGDT, REGBR, REGTELLER,
						REFUSECD, REFUSEDS, RFSAUTH, UPDATER, TMESTAMP,
						CLSCASE, CARDREJ, CUSTNM, STATUSCD, OID });

	}

	@Override
	public void updateLunId(String custId, String dupNo, String rejtCase, String caseNo) {
		this.getJdbc().update(
				"LnunId.update",
				new Object[] { rejtCase, caseNo, custId, dupNo });

	}	
	
	@Override
	public Map<String, Object> queryReject(String custId, String dupNo) {
		return this.getJdbc().queryForMap("LnunId.getReject",
				new Object[] { custId, dupNo });
	}
	
	@Override
	public List<Map<String, Object>> findRefusedRecordOfFinancialHolding(String custId, String dupNo) {
		return this.getJdbc().queryForList("LnunId02.queryByCustIdAndDupNoAndBuno", new Object[] { custId, dupNo });
	}
}
