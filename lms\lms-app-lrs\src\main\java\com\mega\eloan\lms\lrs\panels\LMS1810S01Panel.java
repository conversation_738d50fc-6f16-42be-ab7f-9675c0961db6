
package com.mega.eloan.lms.lrs.panels;

import com.mega.eloan.common.panels.Panel;
import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.DocLogPanel;

/**
 * <pre>
 * 覆審名單 - 文件資訊
 * </pre>
 */
public class LMS1810S01Panel extends Panel {

	public LMS1810S01Panel(String id) {
		super(id);
	}
	
	public LMS1810S01Panel(String id, boolean updatePanelName) {
		super(id, updatePanelName);
	}
	
	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);
		new DocLogPanel("_docLog").processPanelData(model, params);
	}

	private static final long serialVersionUID = 1L;


}
