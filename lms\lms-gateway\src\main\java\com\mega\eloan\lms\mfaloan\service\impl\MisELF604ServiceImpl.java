package com.mega.eloan.lms.mfaloan.service.impl;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import tw.com.jcs.common.Util;

import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.mfaloan.bean.ELF457;
import com.mega.eloan.lms.mfaloan.bean.ELF604;
import com.mega.eloan.lms.mfaloan.service.MisELF604Service;

@Service
public class MisELF604ServiceImpl extends AbstractMFAloanJdbc implements
	MisELF604Service {

	@Override
	public List<Map<String, Object>> getByBrNoAndLoanDate(String brNo,String agntNo,String loanStartDate,String loanEndDate) {
		List<Map<String, Object>> datas=this.getJdbc().queryForList(
				"elf604.getByBrNoAndLoanDate",	new Object[] { loanStartDate,loanEndDate,brNo
						,loanStartDate,loanEndDate,brNo
						,loanStartDate,loanEndDate });
		List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
		if(datas!=null && Util.isNotEmpty(agntNo)){
			for(Map<String, Object> data:datas){
				if(Util.equals(agntNo, data.get("AGNTNO"))){
					result.add(data);
				}
			}
		}
		else{
			result=datas;
		}
		
		return result;
	}
	
	@Override
	public ELF604 findByLoanNo(String ELF604_LOAN_NO) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForListByCustParam("select"
				, new Object[] { "ELF604", "ELF604_LOAN_NO=?  " }
				, new Object[] {ELF604_LOAN_NO});		
		List<ELF604> list = toELF604(rowData);
		
		if(list.size()==1){			
			return list.get(0);
		}else{
			return null;
		}
	}
	
	private List<ELF604> toELF604(List<Map<String, Object>> rowData){
		List<ELF604> list = new ArrayList<ELF604>();
		for (Map<String, Object> row : rowData) {
			ELF604 model = new ELF604();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}
	
	@Override
	public Map<String, Object> getByLoanNo(String loanNo) {
		return this.getJdbc().queryForMap("elf604.getByLoanNo",
				new String[] { loanNo });
	}
	
	@Override
	public List<Map<String, Object>> getAll() {
		return this.getJdbc().queryForListWithMax("elf604.findAll", new String[] {});
	}
	
	@Override
	public List<Map<String, Object>> getBycntrNo(String brNo,String cntrNo) {
		List<Map<String, Object>> datas=this.getJdbc().queryForList(
				"agent.getByCntrno",	new Object[] { brNo,cntrNo });

		
		return datas;
	}
}
