/* 
 * MisElremainService.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.mfaloan.service;

import java.util.List;
import java.util.Map;

/**
 * <pre>
 * 利害關係人
 * </pre>
 * 
 * @since 2012/11/8
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/11/8,Fantasy,new
 *          </ul>
 */
public interface MisElremainService {

	/**
	 * 本行利害關係人
	 * 
	 * @param reid
	 * @return
	 */
	public List<Map<String, Object>> findElremainByReidAndDupno(String reid,
			String dupno);

	/**
	 * 本行有利害關係人
	 * 
	 * @param cmpid
	 * @return
	 */
	public List<Map<String, Object>> findElresecdByCmpid(String cmpid);

	/**
	 * 本行有利害關係企業
	 * 
	 * @param cmpid
	 * @return
	 */
	public List<Map<String, Object>> findElrescomByCmpid(String cmpid);

	/**
	 * 金控法利害關係人檔
	 * 
	 * @param relId
	 * @param mageLawno
	 * @return
	 */
	public List<Map<String, Object>> findElrex45ByRelidAndLawno(String relId,
			String mageLawno);
	
	public List<Map<String, Object>> selForCLS(String custId, String dupNo); 
}
