package com.mega.eloan.lms.las.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import com.iisigroup.cap.component.PageParameters;

import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.lms.base.flow.enums.LasDocStatusEnum;
import com.mega.eloan.lms.las.panels.LMS1925S01Panel;
import com.mega.eloan.lms.las.panels.LMS1925S02Panel;
import com.mega.eloan.lms.las.panels.LMS1925S03Panel;
import com.mega.eloan.lms.las.panels.LMS1925S04Panel;
import com.mega.eloan.lms.las.panels.LMS1925S05Panel;
import com.mega.eloan.lms.las.panels.LMS1925S06Panel;
import com.mega.eloan.lms.model.L192M01A;
import com.mega.eloan.common.panels.Panel;

/**
 * 授信業務工作底稿
 * 
 * <AUTHOR>
 * 
 */
@Controller
@RequestMapping("/las/lms1925m01/{page}")
public class LMS1925M01Page extends AbstractEloanForm {

	final String TAB_CTX = "_tabCtx";
	final String TAB_SIGN = "tab-";

	public LMS1925M01Page() {
		super();
	}

	@SuppressWarnings("deprecation")
	@Override
	public void execute(ModelMap model, PageParameters params) {
		// 依權限設定button

		addAclLabel(model, new AclLabel("_btnDOC_EDITING_G_TO_B", params, getDomainClass(),
                AuthType.Modify, LasDocStatusEnum.稽核室_分行_編製中));
        addAclLabel(model, new AclLabel("_btnDOC_EDITING", params, getDomainClass(),
                AuthType.Modify, LasDocStatusEnum.分行_編製中));
        addAclLabel(model, new AclLabel("_btnWAIT_APPROVE", params, getDomainClass(),
                AuthType.Accept, LasDocStatusEnum.分行_待覆核));
        addAclLabel(model, new AclLabel("_btnDOC_EDITING_G", params, getDomainClass(),
                AuthType.Modify, LasDocStatusEnum.稽核室_編製中));
        addAclLabel(model, new AclLabel("_btnWAIT_APPROVE_G", params, getDomainClass(),
                AuthType.Accept, LasDocStatusEnum.稽核室_待覆核));

		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		String tabID = TAB_SIGN + Util.addZeroWithValue(page, 2); // 指定ID
		model.addAttribute("tabIdx", tabID);
		
		Panel panel = getPanel(page);
		//UPGRADE(Scott)	
//		panel.add(new AttributeModifier("id", new Model<String>(tabID)));
//		add(panel);
		panel.processPanelData(model, params);
		
		renderJsI18N(LMS1925M01Page.class);
	}

	public Panel getPanel(int index) {
		Panel panel = null;
		switch (index) {
		case 1:
			panel = new LMS1925S01Panel(TAB_CTX, true);
			break;
		case 2:
			panel = new LMS1925S02Panel(TAB_CTX, true);
			break;
		case 3:
			panel = new LMS1925S03Panel(TAB_CTX, true);
			break;
		case 4:
			panel = new LMS1925S04Panel(TAB_CTX, true);
			break;
		case 5:
			panel = new LMS1925S05Panel(TAB_CTX, true);
			break;
		case 6:
			panel = new LMS1925S06Panel(TAB_CTX, true);
			break;
		default:
			panel = new LMS1925S01Panel(TAB_CTX, true);
			break;
		}
		return panel;
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		// TODO Auto-generated method stub
		return L192M01A.class;
	}
}
