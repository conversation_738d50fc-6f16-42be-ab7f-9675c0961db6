<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
	<body>
        <th:block th:fragment="innerPageBody">
            <script type="text/javascript">loadScript('pagejs/cls/CLS3701M01Page');</script>
            <div class="button-menu funcContainer" id="buttonPanel">
                <!--編製中 -->
                <th:block th:if="${_btnDOC_EDITING_visible}">
                    <button id="btnSave">
                        <span class="ui-icon ui-icon-jcs-04" ></span>
                        <th:block th:text="#{'button.save'}"><!--儲存--></th:block>
                    </button>
                </th:block>

                <!--待覆核 -->
                <th:block th:if="${_btnWAIT_APPROVE_visible}">
                    <button id="btnCheck" >
                        <span class="ui-icon ui-icon-jcs-106" ></span>
                        <th:block th:text="#{'button.check'}"><!--覆核--></th:block>
                    </button>
                </th:block>

                <button id="btnExit" class="forview">
                    <span class="ui-icon ui-icon-jcs-01"></span>
                    <th:block th:text="#{'button.exit'}">離開</th:block>
                </button>
            </div>
            <div class="tit2 color-black">
                <span id="showCustId" class="color-blue" ></span>
            </div>
        	<form id="mainPanel">
                <span id="mainId" style="display:none"></span>
                <span id="custId" style="display:none"></span>
                <span id="dupNo" style="display:none"></span>
				<span id="approver" style="display:none"></span>
				<span id="ownBrId" style="display:none"></span>
				<input type="hidden" id="licenseWord" name="licenseWord" value="年登字">
                <table class='tb2' width='100%'>
                    <tr>
                        <td class="hd1">
                        	<span class="text-red">＊</span><th:block th:text="#{'C126M01A.label.agnt'}">引介房仲設定</th:block>
        	 			</td>
        	 			<td width="60%">
        	 				<table>
        	 					<tr>
	                	 			<td class="hd1" nowrap>
	                	 				<th:block th:text="#{'C126M01A.agntNo'}">引介房仲代號</th:block>
	                	 			</td>
                	 				<td width="20%"><select id="agntNo" name="agntNo" itemType="L140M01A_agntNo" class="required isNew"></select>
                	 				</td>
								</tr>
								<tr>
	                	 			<td class="hd1" ><th:block th:text="#{'C126M01A.agntChain'}">引介房仲連鎖店類型</th:block>
	                	 			</td>
                	 				<td><select id="agntChain" name="agntChain" itemType="L140M01A_agntChain"  class="required isNew"></select>
                	 				</td>
								</tr>
								<tr>
	                	 			<td class="hd1" ><th:block th:text="#{'C126M01A.agntName'}">引介房仲姓名</th:block>
	                	 			</td>
                	 				<td>
										<input type="text" id="agntName" name="agntName" size="20" class="required isNew"/>
                	 				</td>
								</tr>
								<tr>
	                	 			<td class="hd1" >
	                	 				<th:block th:text="#{'C126M01A.agentCertNo'}">引介房仲證書(明)字號</th:block>
										<br/>
										<button type="button" id="openButton"><th:block th:text="#{'C126S01A.open'}">開啟</th:block></button>
	                	 			</td>
                	 				<td>
                	 					<input type="text" id="licenseYear" name="licenseYear"  size="3" maxlength="3" class="required isNew"/>
										<th:block th:text="#{'C126M01A.licenseWord'}">年登字</th:block>
										<input type="text" id="licenseNumber" name="licenseNumber" size="6" maxlength="6" class="required isNew"/>
										<th:block th:text="#{'C126M01A.licenseNumber'}">號</th:block>
                	 				</td>
								</tr>
								<tr>
	                	 			<td class="hd1" ><th:block th:text="#{'C126M01A.agentCertNoExpiration'}">證書(明)有效期限</th:block>
	                	 			</td>
                	 				<td>
                	 					<input type="text" id="licenseExp" name="licenseExp" size="10" class="date isNew"/>
                	 				</td>
								</tr>
								<tr>
	                	 			<td class="hd1" ><th:block th:text="#{'C126M01A.agentCompanyName'}">任職經紀業名稱</th:block>
	                	 			</td>
                	 				<td>
										<input type="text" id="agntCompanyName" name="agntCompanyName" size="20" class="isNew"/>
                	 				</td>
								</tr>
								<tr>
	                	 			<td class="hd1" ><th:block th:text="#{'C126M01A.agentBranchName'}">任職營業處所名稱</th:block>
	                	 			</td>
                	 				<td>
										<input type="text" id="agntBranchName" name="agntBranchName" size="20" class="isNew"/>
                	 				</td>
								</tr>
        	 				</table>
        	 			</td>			
						<td width="25%" class="hd1">
                            <span class="text-red">＊</span><th:block th:text="#{'C126M01A.contractNo'}">買賣合約書編號</th:block>
                        </td>
                        <td width="15%" class="notUse">
                            <label>
								<input type="text" id="contractNo" name="contractNo"  size="21" maxlength="20" class="required isNew"/>
							</label>
                        </td>
                    </tr>
                    <tr>
                        <td width="18%" class="hd1">
                            <span class="text-red">＊</span><th:block th:text="#{'C126M01A.address'}">物件門牌</th:block>
                        </td>
                        <td colspan='3' width="32%">
                            <!--<span id='zip'></span>-->
                            <input type="text" id="zip" name="zip" size="5" maxlength="5" class="digits required" style="display:none;"/>
                            <select id="city" name="city" class="required"></select><select id="dist" name="dist" class="required"></select>
                            <br>
                            <input type="text" id="villageName" name="villageName" size="20" maxlength="50" class=""/>
                            <select id="village" name="village" class="">
                                <option value=""><th:block th:text="#{'checkSelect'}">請選擇</th:block></option>
                                <option value="1"><th:block th:text="#{'village_1'}">里</th:block></option>
                                <option value="2"><th:block th:text="#{'village_2'}">村</th:block></option>
                            </select>，
                            <input type="text" id="neighborhood" name="neighborhood" size="4" maxlength="4" class="digits"/>
                            鄰
                            <br>
                            <input type="text" id="roadName" name="roadName" size="20" maxlength="20" class="required"/>
                            <select id="road" name="road" class="">
                                <option value="1"></option>
                                <option value="2"><th:block th:text="#{'road_2'}">路</th:block></option>
                                <option value="3"><th:block th:text="#{'road_3'}">街</th:block></option>
                            </select>，
                            <input type="text" id="sec" name="sec" size="4" maxlength="4" class=""/>段，
                            <input type="text" id="lane" name="lane" size="8" maxlength="8" class=""/>巷，
                            <input type="text" id="alley" name="alley" size="8" maxlength="8" class=""/>弄，
                            <input type="text" id="no1" name="no1" size="4" maxlength="4" class="required"/>之
                            <input type="text" id="no2" name="no2" size="4" maxlength="4" class=""/>號，
                            <br>
                            <input type="text" id="floor1" name="floor1" size="3" maxlength="3" class=""/>樓之
                            <input type="text" id="floor2" name="floor2" size="3" maxlength="3" class=""/>(
                            <input type="text" id="room" name="room" size="3" maxlength="3" class=""/>室)
                            <br>
                            <span class="color-red">註：XX市．．．XX弄 A之B號，倘「B」欄位無資料，門號請填在「A」欄位</span>
                        </td>
                    </tr>
                    <tr>
                    	<td width="18%" class="hd1">
                            <span class="text-red">＊</span><th:block th:text="#{'C126M01A.receiveDate'}">收件日期</th:block>
                        </td>
                        <td width="32%" class="notUse">
                            <label>
								<input type="text" id="receiveDate" name="receiveDate" class="date required" size="11" maxlength="10"/>
							</label>
                        </td>
                        <td width="18%" class="hd1">
                           <span class="text-red">＊</span> <th:block th:text="#{'C126M01A.contractDate'}">買賣合約書簽約日期</th:block>
                        </td>
                        <td width="32%" class="notUse">
                            <label><input type="text" id="contractDate" name="contractDate" class="date required" size="11" maxlength="10"/></label>
                        </td>
                    </tr>
					<tr>
						<td width="18%" class="hd1">
                            <span class="text-red">＊</span><th:block th:text="#{'C126M01A.ttlAmt'}">成交金額</th:block>
                        </td>
                        <td width="32%" class="notUse">
                            <label>
								<input type="text" id="ttlAmt" name="ttlAmt" size="14" maxlength="14" integer="14" fraction="0" class="numeric required" />元
							</label>
                        </td>
                        <td width="18%" class="hd1">
                            <span class="text-red">＊</span><th:block th:text="#{'C126M01A.preloanAmt'}">預貸金額</th:block>
                        </td>
                        <td width="32%" class="notUse">
                            <label>
								<input type="text" id="preloanAmt" name="preloanAmt" size="14" maxlength="14" integer="14" fraction="0" class="numeric required" />元
							</label>
                        </td>
                    </tr>
					<tr>
						<td width="18%" class="hd1">
                            <th:block th:text="#{'C126M01A.applyAmt'}">可貸金額</th:block>
                        </td>
                        <td width="32%" class="notUse">
                            <label>
								<input type="text" id="applyAmt" name="applyAmt" size="14" maxlength="14" integer="14" fraction="0" class="numeric" />元
							</label>
                        </td>
                        <td width="18%" class="hd1">
                            <th:block th:text="#{'C126M01A.lnAmt'}">回饋金=</th:block>
                        </td>
                        <td width="32%" class="notUse">
                            <label>
								<input type="text" id="lnAmt" name="lnAmt" size="14" maxlength="14" integer="14" fraction="0" class="numeric" />元
							</label>
                        </td>
                    </tr>
					<tr>
						<td width="18%" class="hd1">
                            <th:block th:text="#{'C126M01A.estDate'}">鑑價完成日期</th:block>
                        </td>
                        <td width="32%" class="notUse">
                            <label>
								<input type="text" id="estDate" name="estDate"  class="date" size="11" maxlength="10"/>
							</label>
                        </td>
                        <td width="18%" class="hd1">
                            <th:block th:text="#{'C126M01A.creditReviewDate'}">徵審完成日期</th:block>
                        </td>
                        <td width="32%" class="notUse">
                            <label>
								<input type="text" id="creditReviewDate" name="creditReviewDate"  class="date" size="11" maxlength="10"/>
							</label>
                        </td>
                    </tr>
					<tr>
                        <td width="18%" class="hd1">
                            <th:block th:text="#{'C126M01A.lnDate'}">撥款日期</th:block>
                        </td>
                        <td width="32%" class="notUse">
                            <label>
								<input type="text" id="lnDate" name="lnDate"  class="date" size="11" maxlength="10"/>
							</label>
                        </td>
                        <td width="18%" class="hd1">
                            <th:block th:text="#{'C126M01A.statFlag'}">承作情形</th:block>
                        </td>
                        <td width="32%" class="notUse">
                            <label>
								<select id="statFlag" name="statFlag" itemType="C126M01A_statFlag" ></select>
							</label>
                        </td>
                    </tr>
					<tr>
                        <td width="18%" class="hd1">
                            <th:block th:text="#{'C126M01A.rateKind'}">利率方案</th:block>
                        </td>
                        <td width="32%" class="notUse">
                            <label>
								<input type="text" id="rateKind" name="rateKind" />
							</label>
                        </td>
						<td width="18%" class="hd1">
                            <th:block th:text="#{'C126M01A.essayName'}">送件代書</th:block>
                        </td>
                        <td width="32%" class="notUse">
                            <label>
								<input type="text" id="essayName" name="essayName" />
							</label>
                        </td>
                        
                    </tr>
					<tr>
						<td width="18%" class="hd1">
                            <th:block th:text="#{'C126M01A.rebateRatio'}">回饋金比率(%)</th:block>
                        </td>
                        <td width="32%" class="notUse">
                            <label>
								<input type="text" id="rebateRatio" name="rebateRatio" />%
							</label>
                        </td>
                        <td width="18%" class="hd1">
                            <th:block th:text="#{'C126M01A.rebate'}">回饋金</th:block>
                        </td>
                        <td width="32%" class="notUse">
                            <label>
								<input type="text" id="rebate" name="rebate" size="14" maxlength="14" integer="14" fraction="0" class="numeric" />元
							</label>
                        </td>
                    </tr>
                </table>
            </form>

            <div id="docPanel">
                <fieldset>
                    <legend>
                        <b><th:block th:text="#{'doc.docUpdateLog'}"><!-- 文件異動紀錄 --></th:block></b>
                    </legend>
                    <div class="funcContainer">
                        <div class="funcContainer"><!-- 文件異動紀錄--> <div th:include="common/panels/DocLogPanel :: DocLogPanel"></div></div>
                    </div>
                    <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tbody>
                        <tr>
                            <td width="35%" class="hd1">
                                <th:block th:text="#{'doc.creator'}"><!--  文件建立者--></th:block>&nbsp;&nbsp;
                            </td>
                            <td width="15%">
                                <span id='creator'></span>(<span id='createTime'></span>)
                            </td>
                            <td width="30%" class="hd1">
                                <th:block th:text="#{'doc.lastUpdater'}"><!--  最後異動者--></th:block>&nbsp;&nbsp;
                            </td>
                            <td width="20%">
                                <span id='updater'></span>(<span id='updateTime'></span>)
                            </td>
                        </tr>
                        <tr>
                            <td class="hd1">
                            </td>
                            <td>
                            </td>
                            <td class="hd1">
                                <th:block th:text="#{'doc.docCode'}"><!--文件亂碼--></th:block>&nbsp;&nbsp;
                            </td>
                            <td>
                                <span id="randomCode" ></span>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </fieldset>

            </div>

            <div id="openCheckBox" style="display:none">
                <div>
				<span id="check1" style="display:none">
				 	<label><input name="checkRadio" type="radio" value="3"><th:block th:text="#{'accept'}"><!--  核准--></th:block></label><br/>
					<label><input name="checkRadio" type="radio" value="1"><th:block th:text="#{'back'}"><!--  退回經辦修改--></th:block></label>
				</span>
                </div>
            </div>
            <div id="selectBossBox"  style="display:none;">
                <form id="selectBossForm">
                    <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td class="hd1" width="60%"><th:block th:text="#{'C126M01A.selectBoss'}"><!--  授信主管人數--></th:block>&nbsp;&nbsp;</td>
                            <td width="40%"><select id="numPerson" name="numPerson">
                                <option value="1">1</option>
                                <option value="2">2</option>
                                <option value="3">3</option>
                                <option value="4">4</option>
                                <option value="5">5</option>
                                <option value="6">6</option>
                                <option value="7">7</option>
                                <option value="8">8</option>
                                <option value="9">9</option>
                                <option value="10">10</option>
                            </select>
                            </td>
                        </tr>
                        <tr >
                            <td class="hd1" ><th:block th:text="#{'C126M01A.bossId'}"><!--  授信主管--></th:block>&nbsp;&nbsp;</td>
                            <td >
                                <div id="bossItem"></div>
                            </td>
                        </tr>
                        <tr >
                            <td class="hd1"><th:block th:text="#{'C126M01A.managerId'}"><!--經副襄理--></th:block>&nbsp;&nbsp;</td>
                            <td><div id="managerItem"></div></td>
                        </tr>
                    </table>
					<div id="openBox_agentLicenseInfo">
						<button type="button" id="queryLicenseNoButton"><th:block th:text="#{'C126M01A.queryLicenseNo'}">查詢</th:block></button>
						<button type="button" id="updateInquiryDateButton"><th:block th:text="#{'C126S01A.updateInquiryDate'}">更新查詢資料</th:block></button>
						<span>&nbsp;&nbsp;&nbsp;</span>
						<span><th:block th:text="#{'C126S01A.name'}">姓名</th:block>:</span><input type="text" id="agentNameThickBox" name="agentNameThickBox" readonly size="5"/>
						<span><th:block th:text="#{'C126S01A.status'}">狀態</th:block>:</span><input type="text" id="status" name="status" readonly size="5"/>
						<span>&nbsp;&nbsp</span>
						<span><th:block th:text="#{'C126S01A.remark'}">備註</th:block>:</span><input type="text" id="remark" name="remark" readonly size="10"/>
						<div id="agentLicenseInfoGrid"></div>
					</div>
                </form>
            </div>
        </th:block>
    </body>
</html>
