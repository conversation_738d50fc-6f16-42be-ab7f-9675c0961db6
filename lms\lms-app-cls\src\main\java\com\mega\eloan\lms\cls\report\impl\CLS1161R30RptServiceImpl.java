package com.mega.eloan.lms.cls.report.impl;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.inet.report.ReportException;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.cls.report.CLS1161R30RptService;
import com.mega.eloan.lms.cls.service.CLS1161Service;
import com.mega.eloan.lms.model.C160M03A;
import com.mega.eloan.lms.model.C160S03A;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.ReportGenerator;

/**
 * <pre>
 * 整批自動開戶
 * </pre>
 * 
 * @since 2017/04/14
 * <AUTHOR>
 * @version <ul>
 *          <li>2017/04/14, EL08034, 整批自動開戶
 *          </ul>
 */
@Service("cls1161r30rptservice")
public class CLS1161R30RptServiceImpl implements FileDownloadService, CLS1161R30RptService {

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(CLS1161R30RptServiceImpl.class);

	@Resource
	BranchService branchService;
	
	@Resource
	CLS1161Service cls1161Service;
	
	@Override
	public byte[] getContent(PageParameters params) throws CapException,
			FileNotFoundException, ReportException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
			baos = (ByteArrayOutputStream) this.createReportData(params);
		} finally {
			if (baos != null) {
				baos.close();
			}
		}
		return baos != null ? baos.toByteArray() : null;
	}
	
	private OutputStream createReportData(PageParameters params)
	throws FileNotFoundException, IOException, Exception {
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		List<Map<String, String>> rowsData = new LinkedList<Map<String, String>>();
		ReportGenerator generator = new ReportGenerator();
		OutputStream outputStream = null;
		
		//MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		try {

			String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
			C160M03A meta = cls1161Service.findModelByMainId(C160M03A.class, mainId);
			List<C160S03A> c160s03a_list = cls1161Service.findC160S03AByMainIdOrderBySeqNo(mainId);
			//==========
			Locale locale = LMSUtil.getLocale();
			rptVariableMap.put("BRANCHNAME", branchService.getBranchName(meta.getOwnBrId()));		
			
			rptVariableMap.put("titleInfo", 
					"客戶名稱　："+Util.trim(meta.getCustId())+" "+Util.trim(meta.getCustName())+"<br/>"
					+"額度、批號："+Util.trim(meta.getCntrNo())+"("+Util.trim(meta.getPackNo())+")"+"<br/>" +
					"列印日期　："+TWNDate.toAD(new Date()) );
			
			rptVariableMap.put("sumInfo", 
					"合計資料筆數："+c160s03a_list.size()+"筆<br/>"
					+"合計進帳金額："+sumInfo(c160s03a_list)+"元<br/>"
					+"文件亂碼："+Util.trim(meta.getRandomCode()));
			generator.setLang(locale);

			generator.setRowsData(parseData(c160s03a_list));
			generator.setVariableData(rptVariableMap);

			generator.setReportFile("report/cls/CLS1161R30_"+ locale.toString() + ".rpt");

			
			outputStream = generator.generateReport();
		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
				rptVariableMap = null;
			}
			if (rowsData != null) {
				rowsData.clear();
				rowsData = null;
			}
		}

		return outputStream;
	}
	
	private String sumInfo(List<C160S03A> c160s03a_list){
		Map<String, BigDecimal> r = new HashMap<String, BigDecimal>();
		for (C160S03A c160s03a : c160s03a_list) {
			String curr = c160s03a.getSwft();
			BigDecimal amt = c160s03a.getRctAmt();
			if(amt==null){
				amt = BigDecimal.ZERO;
			}
			
			if(!r.containsKey(curr)){
				r.put(curr, BigDecimal.ZERO);
			}
			
			r.put(curr, amt.add(r.get(curr)));
		}
		//=========
		List<String> str = new ArrayList<String>();
		for(String curr: r.keySet()){
			str.add(curr+" "+NumConverter.addComma(LMSUtil.pretty_numStr(r.get(curr))));
		}
		return StringUtils.join(str, " 、 ");
	}
	
	private List<Map<String, String>> parseData(List<C160S03A> c160s03a_list) throws CapException {
		
		List<Map<String, String>> result = new LinkedList<Map<String, String>>();
		int uiSeq = 0;
		for (C160S03A c160s03a : c160s03a_list) {
			
			Map<String, String> map = Util.setColumnMap(15);
			map.put("ReportBean.column01", Util.trim(TWNDate.toAD(c160s03a.getRctDate())));
			map.put("ReportBean.column02", Util.trim(c160s03a.getAccNo()));
			map.put("ReportBean.column03", Util.trim(c160s03a.getSwft()));
			map.put("ReportBean.column04", NumConverter.addComma(LMSUtil.pretty_numStr(c160s03a.getRctAmt())));
			map.put("ReportBean.column05", Util.getLeftStr(c160s03a.getCustId_s()+"          ", 10)+"-"+Util.trim(c160s03a.getDupNo_s()));
			map.put("ReportBean.column06", Util.trim(TWNDate.toAD(c160s03a.getNt_rt_dt_s())));
			map.put("ReportBean.column07", Util.trim(c160s03a.getAtPayNo_s()));
			map.put("ReportBean.column08", Util.trim(c160s03a.getMemo_s()));
			map.put("ReportBean.column09", Util.trim(String.valueOf(++uiSeq)));
			map.put("ReportBean.column10", allow_str(c160s03a));
			//=========
			result.add(map);
		}
		return result;
	}
	private String allow_str(C160S03A c160s03a){
		String beg = allow_str(c160s03a.getAllow_beg());
		String end = allow_str(c160s03a.getAllow_end());
		if(Util.isEmpty(Util.trim(beg)) && Util.isEmpty(Util.trim(end))){
			//for 之前的舊案
			return "";
		}
		return beg+"~"+end;
	}
	private String allow_str(Integer a){
		if(a==null){
			return  "   ";
		}else{
			return Util.getRightStr("000"+Util.trim(a), 3);
		}		
	}
}
