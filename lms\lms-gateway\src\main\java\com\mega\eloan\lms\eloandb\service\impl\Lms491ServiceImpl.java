package com.mega.eloan.lms.eloandb.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import tw.com.jcs.common.TWNDate;

import com.mega.eloan.lms.eloandb.service.Lms491Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

@Service
public class Lms491ServiceImpl extends AbstractEloandbJdbc implements
		Lms491Service {

	@Override
	public List<Map<String, Object>> LMS491selByBranch(String branch) {
		return this.getJdbc().queryForList("LMS.LMS491selByBranch",
				new Object[] { branch });
	}

	@Override
	public List<?> findSelAll(String branch, Date dateYM) {
		String date = TWNDate.toAD(dateYM).substring(0, 4)
				+ TWNDate.toAD(dateYM).substring(5, 7);
		return this.getJdbc().queryForList("LMS491.selAll",
				new Object[] { branch, date });
	}

	@Override
	public Map<String, Object> findSelRemomo(String branch) {
		return this.getJdbc().queryForMap("LMS491.selRemomo",
				new Object[] { branch });
	}

	@Override
	public Map<String, Object> findSelCrdate(String branchId, String custId,
			String dupno) {
		return this.getJdbc().queryForMap("LMS491.selCrdate",
				new Object[] { branchId, custId, dupno });
	}

	@Override
	public void saveInsertNew(String branch, String custId, String dupno,
			Date crDate, String updater, String rePortKind, String reMemo,
			String newFlag, Date agntDt, BigDecimal twdRt, String locRt) {
//		this.getJdbc().update(
//				"LMS491.insertNew",
//				new Object[] { branch, custId, dupno,
//						Util.addZeroWithValue(TWNDate.toAD(crDate), 10),
//						updater, rePortKind, reMemo, newFlag,
//						TWNDate.toAD(agntDt), twdRt, locRt });
		this.getJdbc().update("LMS491.insert", new Object[] { branch, custId, dupno, null,
				 null, null, TWNDate.toAD(crDate), null, null, 
				 null, null, updater, null, null, rePortKind, reMemo,
				 newFlag, null, null, null, TWNDate.toAD(agntDt), twdRt,
				 locRt });
	}
	
	@Override
	public void deleteLMS491(String branch, String custid, String dupno){
		this.getJdbc().update("LMS491.deleteSame", new Object[] { branch,custid,dupno });
	}
	
	@Override
	public void insertLMS491(String branch, String custid, String dupno, String maincust,
			Date llrdate, Date lrdate, Date crdate, String nckdflag, Date nckddate, String nckdmemo, Date canceldt,
			String updater, String uckdline, Date uckddt, String reportkind, String remomo,
			String newflag, String flag_8_1, String flagO8_1, String flagN8_1, Date agntDt, BigDecimal agntTwdRt,
			String agntLoc_Rt){
		this.getJdbc().update("LMS491.insert", new Object[] { branch, custid, dupno, maincust,
				 llrdate, lrdate, crdate, nckdflag, nckddate, nckdmemo, canceldt, updater, uckdline, uckddt, reportkind, remomo,
				 newflag, flag_8_1, flagO8_1, flagN8_1, agntDt, agntTwdRt,
				 agntLoc_Rt });
	}

	@Override
	public void saveUpdateNew(String remomo, Date crdate, String updater,
			Date agntDt, BigDecimal twdRt, String locRt, String branch,
			String custId, String dupNo) {
		this.getJdbc().update(
				"LMS491.updateNew",
				new Object[] { remomo,
						TWNDate.toAD(crdate),
						updater, TWNDate.toAD(agntDt), twdRt, locRt, branch,
						custId, dupNo });
	}

	@Override
	public List<?> findSelMaincust(String branch, String custId, String dupNo) {
		return this.getJdbc().queryForList("LMS491.selMaincust",
				new Object[] { branch, custId, dupNo });
	}

	@Override
	public void saveUpdateOld(String remomo, Date crdate, String updater,
			Date agntDt, BigDecimal twdRt, String locRt, String branch,
			String custId, String dupNo) {
		this.getJdbc().update(
				"LMS491.updateOld",
				new Object[] { remomo,
						TWNDate.toAD(crdate),
						updater, TWNDate.toAD(agntDt), twdRt, locRt, branch,
						custId, dupNo });
	}

	@Override
	public Map<String, Object> findNewSelAll(String branch, String custId,
			String dupNo) {
		return this.getJdbc().queryForMap("LMS491.selAllForNew",
				new Object[] { branch, custId, dupNo });
	}

	@Override
	public List<?> find491BycustIdData(String custId, String dupNo,
			String branch) {

		return this.getJdbc().queryForList("LMS491.selcrDateAndFlag",
				new Object[] { custId, dupNo, branch });
	}

	@Override
	public List<?> find491BycustIdDataNoBranch(String custId, String dupNo) {

		return this.getJdbc().queryForList("LMS491.selcrDateAndFlagNoBranch",
				new Object[] { custId, dupNo});
	}

	@Override
	public List<?> find491DistinctBycustIdData(String custId, String dupNo,
			String branch) {

		return this.getJdbc().queryForList("LMS491.DistinctselcrDateAndFlag",
				new Object[] { custId, dupNo, branch });
	}

	@Override
	public void insertNew491Data(String branch) {

		this.getJdbc().queryForList("LMS491.insertNewData",
				new Object[] { branch });
	}

	@Override
	public void insertNew491(String custId, String dupNo, String branch,
			Date lastRetrialDate, Date shouldReviewDate, String nCkdMemo,
			String nCkdFlag, String retrialYN, Date nCkdDate, String updater) {

		this.getJdbc()
				.update("LMS491.insertNew491",
						new Object[] { branch, custId, dupNo, "2011-01-01",
						"2011-01-01", nCkdMemo, nCkdFlag,"2011-01-01",
								updater});
	}

	@Override
	public int update491ByRetrialKind99(String specifyCycle,
			Date lastRetrialDate, Date retrialDate, String reportkind,
			String retrialKind, Date shouldReviewDate, String tFlag1,
			String tFlag2, String updater, String custId, String dupNo,
			String branch) {

		return this.getJdbc().update(
				"LMS491.update491ByRetrialKind99",
				new Object[] { specifyCycle, lastRetrialDate, retrialDate,
						reportkind, retrialKind, shouldReviewDate, tFlag1,
						tFlag2, updater, custId, dupNo, branch });
	}

	@Override
	public int update491ByRetrialKind81(String tuCount, String updater,
			String branch) {

		return this.getJdbc().update("LMS491.update491ByRetrialKind81",
				new Object[] { tuCount, updater, branch });
	}
	
	@Override
	public void markUpdate491(String nckdFlag, 
			Date nckdDate, String nckdMemo, String branch, String custid, String dupno){
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		this.getJdbc().update("markNoCTL.update491", new Object[]{ nckdFlag, 
				nckdDate, nckdMemo, user.getUserId(), branch, custid, dupno });
	}
}
