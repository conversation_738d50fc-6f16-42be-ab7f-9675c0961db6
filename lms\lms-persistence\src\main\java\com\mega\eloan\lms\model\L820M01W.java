/* 
 * L820M01W.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** RPA發查明家事公告細檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L820M01W", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId","branchNo","empNo","dataCustomerNo"}))
public class L820M01W extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	public L820M01W(){
		
	}
		
	public L820M01W(String mainId, String dataCustomerNo){
		this.dataCustomerNo = dataCustomerNo;
		this.mainId = mainId;
	}
	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 結果回傳URL **/
	@Size(max=200)
	@Column(name="RESPONSEURL", length=200, columnDefinition="VARCHAR(200)")
	private String responseURL;

	/** 系統別 **/
	@Size(max=10)
	@Column(name="SYSTEM", length=10, columnDefinition="VARCHAR(10)")
	private String system;

	/** 識別碼 **/
	@Size(max=300)
	@Column(name="UNIQUEID", length=300, columnDefinition="VARCHAR(300)")
	private String uniqueID;

	/** 發查分行別 **/
	@Size(max=3)
	@Column(name="BRANCHNO", length=3, columnDefinition="CHAR(3)")
	private String branchNo;

	/** 發查員工編號 **/
	@Size(max=6)
	@Column(name="EMPNO", length=6, columnDefinition="CHAR(6)")
	private String empNo;

	/** 身分證字號/護照號碼 **/
	@Size(max=20)
	@Column(name="DATACUSTOMERNO", length=20, columnDefinition="VARCHAR(20)")
	private String dataCustomerNo;

	/** 法院別 **/
	@Size(max=30)
	@Column(name="DATACOURTTYPE", length=30, columnDefinition="VARCHAR(30)")
	private String dataCourtType;

	/** 查詢類別 **/
	@Size(max=2)
	@Column(name="DATAQUERYTYPE", length=2, columnDefinition="CHAR(2)")
	private String dataQueryType;

	/** 
	 * 公告日期起日<p/>
	 * YYYMMDD(民國年)
	 */
	@Size(max=7)
	@Column(name="DATASTARTDATE", length=7, columnDefinition="CHAR(7)")
	private String dataStartDate;

	/** 
	 * 公告日期迄日<p/>
	 * YYYMMDD(民國年)
	 */
	@Size(max=7)
	@Column(name="DATAENDDATE", length=7, columnDefinition="CHAR(7)")
	private String dataEndDate;

	/** 
	 * 流程執行結果代碼<p/>
	 * 0:有案件<br/>
	 *  1:無案件
	 */
	@Size(max=1)
	@Column(name="RESPONSECODE", length=1, columnDefinition="CHAR(1)")
	private String responseCode;

	/** 結果訊息 **/
	@Size(max=300)
	@Column(name="RESPONSEMSG", length=300, columnDefinition="VARCHAR(300)")
	private String responseMsg;

	/** 
	 * 查詢結果<p/>
	 * 0:有案件<br/>
	 *  1:無案件
	 */
	@Size(max=1)
	@Column(name="DATASEARCHRESULT", length=1, columnDefinition="CHAR(1)")
	private String dataSearchResult;

	/** 查詢時間 **/
	@Column(name="QUERYTIME", columnDefinition="TIMESTAMP")
	private Timestamp queryTime;

	/** 備註 **/
	@Size(max=300)
	@Column(name="MEMO", length=300, columnDefinition="VARCHAR(300)")
	private String memo;

	/** 文件建立者 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(06)")
	private String creator;

	/** 文件建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 資料修改人(行編) **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(06)")
	private String updater;

	/** 資料修改日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 回傳附檔1<p/>
	 * Ref BdocFile.oid
	 */
	@Size(max=32)
	@Column(name="DOCFILEOID", length=32, columnDefinition="CHAR(32)")
	private String docfileoid;

	/** 
	 * 回傳結果內容<p/>
	 * Json格式
	 */
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(name="RETURNDATA", columnDefinition="CLOB")
	private String returnData;

	/** 
	 * 處理狀態<p/>
	 * A01:查詢中<br/>
	 *  A02:查詢完成<br/>
	 *  A03:查詢失敗
	 */
	@Size(max=3)
	@Column(name="STATUS", length=3, columnDefinition="CHAR(3)")
	private String status;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得結果回傳URL **/
	public String getResponseURL() {
		return this.responseURL;
	}
	/** 設定結果回傳URL **/
	public void setResponseURL(String value) {
		this.responseURL = value;
	}

	/** 取得系統別 **/
	public String getSystem() {
		return this.system;
	}
	/** 設定系統別 **/
	public void setSystem(String value) {
		this.system = value;
	}

	/** 取得識別碼 **/
	public String getUniqueID() {
		return this.uniqueID;
	}
	/** 設定識別碼 **/
	public void setUniqueID(String value) {
		this.uniqueID = value;
	}

	/** 取得發查分行別 **/
	public String getBranchNo() {
		return this.branchNo;
	}
	/** 設定發查分行別 **/
	public void setBranchNo(String value) {
		this.branchNo = value;
	}

	/** 取得發查員工編號 **/
	public String getEmpNo() {
		return this.empNo;
	}
	/** 設定發查員工編號 **/
	public void setEmpNo(String value) {
		this.empNo = value;
	}

	/** 取得身分證字號/護照號碼 **/
	public String getDataCustomerNo() {
		return this.dataCustomerNo;
	}
	/** 設定身分證字號/護照號碼 **/
	public void setDataCustomerNo(String value) {
		this.dataCustomerNo = value;
	}

	/** 取得法院別 **/
	public String getDataCourtType() {
		return this.dataCourtType;
	}
	/** 設定法院別 **/
	public void setDataCourtType(String value) {
		this.dataCourtType = value;
	}

	/** 取得查詢類別 **/
	public String getDataQueryType() {
		return this.dataQueryType;
	}
	/** 設定查詢類別 **/
	public void setDataQueryType(String value) {
		this.dataQueryType = value;
	}

	/** 
	 * 取得公告日期起日<p/>
	 * YYYMMDD(民國年)
	 */
	public String getDataStartDate() {
		return this.dataStartDate;
	}
	/**
	 *  設定公告日期起日<p/>
	 *  YYYMMDD(民國年)
	 **/
	public void setDataStartDate(String value) {
		this.dataStartDate = value;
	}

	/** 
	 * 取得公告日期迄日<p/>
	 * YYYMMDD(民國年)
	 */
	public String getDataEndDate() {
		return this.dataEndDate;
	}
	/**
	 *  設定公告日期迄日<p/>
	 *  YYYMMDD(民國年)
	 **/
	public void setDataEndDate(String value) {
		this.dataEndDate = value;
	}

	/** 
	 * 取得流程執行結果代碼<p/>
	 * 0:有案件<br/>
	 *  1:無案件
	 */
	public String getResponseCode() {
		return this.responseCode;
	}
	/**
	 *  設定流程執行結果代碼<p/>
	 *  0:有案件<br/>
	 *  1:無案件
	 **/
	public void setResponseCode(String value) {
		this.responseCode = value;
	}

	/** 取得結果訊息 **/
	public String getResponseMsg() {
		return this.responseMsg;
	}
	/** 設定結果訊息 **/
	public void setResponseMsg(String value) {
		this.responseMsg = value;
	}

	/** 
	 * 取得查詢結果<p/>
	 * 0:有案件<br/>
	 *  1:無案件
	 */
	public String getDataSearchResult() {
		return this.dataSearchResult;
	}
	/**
	 *  設定查詢結果<p/>
	 *  0:有案件<br/>
	 *  1:無案件
	 **/
	public void setDataSearchResult(String value) {
		this.dataSearchResult = value;
	}

	/** 取得查詢時間 **/
	public Timestamp getQueryTime() {
		return this.queryTime;
	}
	/** 設定查詢時間 **/
	public void setQueryTime(Timestamp value) {
		this.queryTime = value;
	}

	/** 取得備註 **/
	public String getMemo() {
		return this.memo;
	}
	/** 設定備註 **/
	public void setMemo(String value) {
		this.memo = value;
	}

	/** 取得文件建立者 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定文件建立者 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得文件建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定文件建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得資料修改人(行編) **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定資料修改人(行編) **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得資料修改日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定資料修改日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 
	 * 取得回傳附檔1<p/>
	 * Ref BdocFile.oid
	 */
	public String getDocfileoid() {
		return this.docfileoid;
	}
	/**
	 *  設定回傳附檔1<p/>
	 *  Ref BdocFile.oid
	 **/
	public void setDocfileoid(String value) {
		this.docfileoid = value;
	}

	/** 
	 * 取得回傳結果內容<p/>
	 * Json格式
	 */
	public String getReturnData() {
		return this.returnData;
	}
	/**
	 *  設定回傳結果內容<p/>
	 *  Json格式
	 **/
	public void setReturnData(String value) {
		this.returnData = value;
	}

	/** 
	 * 取得處理狀態<p/>
	 * A01:查詢中<br/>
	 *  A02:查詢完成<br/>
	 *  A03:查詢失敗
	 */
	public String getStatus() {
		return this.status;
	}
	/**
	 *  設定處理狀態<p/>
	 *  A01:查詢中<br/>
	 *  A02:查詢完成<br/>
	 *  A03:查詢失敗
	 **/
	public void setStatus(String value) {
		this.status = value;
	}
}
