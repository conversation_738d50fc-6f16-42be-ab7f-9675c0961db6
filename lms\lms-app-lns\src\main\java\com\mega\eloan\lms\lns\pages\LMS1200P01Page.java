package com.mega.eloan.lms.lns.pages;

import java.io.UnsupportedEncodingException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.pages.AbstractOutputPage;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.ejcic.service.EjcicService;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.jcs.common.Util;

/**
 * 查詢聯徵列印
 */
@Controller
@RequestMapping("/lms/lms1200p01")
public class LMS1200P01Page extends AbstractOutputPage {
	private static final String PAGE_BREAK_DIV = "<div class=\"pageBreak\">&nbsp;</div>";
	protected final Logger logger = LoggerFactory.getLogger(getClass());
	final String EMPTY_HTML = "<h1>查無資料</h1>";

	@Autowired
	DocFileService docFileService;

	@Autowired
	EjcicService ejcicService;

	@Override
	public String getOutputString(ModelMap model, PageParameters params) {
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String fileOid = Util.trim(params.getString("fileOid"));
		String dataType = Util.trim(params.getString("dataType"));
		
		return printOne(mainId, fileOid, dataType);

	}

	private String printOne(String mainId, String fileOid, String dataType) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		StringBuilder sb = new StringBuilder();

		DocFile docFile = docFileService.findByOidAndSysId(fileOid,	"LMS");
		if (docFile == null) {
			sb.append(EMPTY_HTML);
		} else {
			try {
				 if (Util.equals(dataType, "T70")) {
					// 標準查詢
					String html = new String(docFile.getData(), "BIG5");
					html = html.replaceFirst("<BODY>",
							"<BODY><p style=\"text-align:center\">" + "聯徵標準查詢T70" + "LMS1200P01Page_INSERT_TITLE" + "</p>");
					// 無大寫BODY時用小寫取代
					if (!html.contains("LMS1200P01Page_INSERT_TITLE")) {
						html = html.replaceFirst("<body>",
								"<body><p style=\"text-align:center\">" + "聯徵標準查詢T70" + "</p>");
					} else {
						// 判斷用字串刪除
						html = html.replace("LMS1200P01Page_INSERT_TITLE", "");
					}
					if (html.contains("<meta http-equiv=\"X-UA-Compatible\" content=\"IE=EmulateIE7\" />")) {
						sb.append(html
								.replaceFirst("<meta http-equiv=\"X-UA-Compatible\" content=\"IE=EmulateIE7\" />",
								"<META http-equiv=\"X-UA-Compatible\" content=\"IE=11\" />"));
					} else {
						sb.append(html
								.replaceFirst("</head>",
										"<META http-equiv=\"X-UA-Compatible\" content=\"IE=11\" /></head>"));
					}
				} else {
					sb.append(new String(docFile.getData()));
				}
			} catch (UnsupportedEncodingException e) {
				logger.error(StrUtils.getStackTrace(e));
			}
		}
		if (sb.length() > 0 && Util.equals(dataType, "T70")) {
			setNeedHtml(true);
			// 執行浮水印
			String wm_msg = UtilConstants.兆豐銀行代碼 + ejcicService.findEJF369VDEPTID(user.getUnitNo())
			+ " " + user.getUserId() + " " + user.getLoginIP();
			sb.append("<script>window.onload = function(){");
			sb.append("watermark('").append(wm_msg).append("');");
			sb.append("};</script>");
		}
		return sb.toString();
	}

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.iisi.cap.base.pages.AbstractCapPage#getViewName()
     */
    @Override
    protected String getViewName() {
        return "lns/pages/lms1200p01";
    }
}
