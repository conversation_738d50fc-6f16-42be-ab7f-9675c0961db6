/* 
 * DW_RKAPPLICANT.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.mfaloan.bean;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import tw.com.iisi.cap.model.GenericBean;


/** 申請人基本資料 **/
public class DW_RKAPPLICANT_N extends GenericBean {

	private static final long serialVersionUID = 1L;

	/** 分行別 **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="BR_CD", length=3, columnDefinition="CHAR(3)", nullable=false,unique = true)
	private String br_cd;

	/** NOTES文件編號 **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="NOTEID", length=32, columnDefinition="CHAR(32)", nullable=false,unique = true)
	private String noteid;

	/** 客戶統一編號 **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="CUSTID", length=10, columnDefinition="CHAR(10)", nullable=false,unique = true)
	private String custid;

	/** 重複序號 **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)", nullable=false,unique = true)
	private String dupno;

	/** 評等模型類別 **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="MOWTYPE", length=1, columnDefinition="CHAR(1)", nullable=false,unique = true)
	private String mowtype;

	/** 模型版本-大版 **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="MOWVER1", columnDefinition="DECIMAL(5,0)", nullable=false,unique = true)
	private Integer mowver1;

	/** 模型版本-小版 **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="MOWVER2", columnDefinition="DECIMAL(5,0)", nullable=false,unique = true)
	private Integer mowver2;

	/** JCIC查詢日期 YYYY-MM-DD **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Temporal(TemporalType.DATE)
	@Column(name="JCIC_DATE", columnDefinition="DATE", nullable=false,unique = true)
	private Date jcic_date;

	/**  **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="ACCT_KEY", length=14, columnDefinition="CHAR(14)", nullable=false,unique = true)
	private String acct_key;
	
	/** 主借款人統一編號(CUSTKEY) **/
	@Column(name="CUST_KEY", length=10, columnDefinition="CHAR(10)")
	private String cust_key;

	/** 
	 * 相關身分 ( LNGEFLAG)<p/>
	 * M: 主借款人  C: 共同借款人  G: 連帶保證人  N:一般保證人
	 */
	@Column(name="LNGEFLAG", length=1, columnDefinition="CHAR(1)")
	private String lngeflag;

	/** 出生年月日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="DOB", columnDefinition="DATE")
	private Date dob;

	/** 學歷 **/
	@Column(name="EDUCATION", columnDefinition="DECIMAL(2,0)")
	private Integer education;

	/** 婚姻 **/
	@Column(name="MARRIAGE", columnDefinition="DECIMAL(2,0)")
	private Integer marriage;

	/** 扶養子女數 **/
	@Column(name="CHILDREN", columnDefinition="DECIMAL(2,0)")
	private Integer children;

	/** 
	 * 工作年資<p/>
	 * 年
	 */
	@Column(name="SENIORITY", columnDefinition="DECIMAL(4,2)")
	private BigDecimal seniority;

	/** 
	 * 職業<p/>
	 * 大類(2碼)+細項(1碼)+職稱(1碼)
	 */
	@Column(name="POS", length=4, columnDefinition="CHAR(4)")
	private String pos;

	/** 
	 * 年薪<p/>
	 * 萬元
	 */
	@Column(name="YPAY", columnDefinition="DECIMAL(10,0)")
	private BigDecimal ypay;

	/** 
	 * 其他收入<p/>
	 * 1.薪資收入2.營利收入3.投資收入4.租金收入5.利息收入 <br/>
	 * c120s01b.othType  ==>  select * from com.bcodetype where codetype='cls1131m01_othType' and locale='zh_TW'
	 */
	@Column(name="OMONEY", length=40, columnDefinition="VARCHAR(40)")
	private String omoney;

	/** 
	 * 其他收入金額<p/>
	 * 萬元
	 */
	@Column(name="OMONEY_AMT", columnDefinition="DECIMAL(10,0)")
	private BigDecimal omoney_amt;

	/** 
	 * 夫妻年收入(之前命名為 家庭所得)<p/>
	 * 萬元
	 */
	@Column(name="HINCOME", columnDefinition="DECIMAL(10,0)")
	private BigDecimal hincome;

	/** 
	 * 個人負債比率<p/>
	 * %
	 */
	@Column(name="DRATE", columnDefinition="DECIMAL(7,0)")
	private BigDecimal drate;

	/** 
	 * 夫妻負債比率(之前命名為 家庭負債比率)<p/>
	 * %
	 */
	@Column(name="YRATE", columnDefinition="DECIMAL(7,0)")
	private BigDecimal yrate;
	
	/** 
	 * 家庭負債比率<p/>
	 * %
	 */
	@Column(name="FRATE", columnDefinition="DECIMAL(7,0)")
	private BigDecimal frate;

	/** 
	 * 個人所得證明文件<p/>
	 * 1.個人綜合所得申報資料 2.扣繳憑單 3.薪資轉帳存摺 4.勞保薪資 5.租賃契約 6.其他收入證明
	 */
	@Column(name="CERTIFICATE", length=1, columnDefinition="CHAR(1)")
	private String certificate;

	/** 
	 * 夫妻所得證明文件(之前命名為 家庭所得證明文件)<p/>
	 * 1.個人綜合所得申報資料 2.扣繳憑單 3.薪資轉帳存摺 4.勞保薪資 5.租賃契約 6.其他收入證明
	 */
	@Column(name="HCERTIFICATE", length=1, columnDefinition="CHAR(1)")
	private String hcertificate;

	/** 戶籍地郵遞區號 **/
	@Column(name="LAWADR_ZIP_CD", length=5, columnDefinition="CHAR(5)")
	private String lawadr_zip_cd;

	/** 居住地郵遞區號 **/
	@Column(name="ADR_ZIP_CD", length=5, columnDefinition="CHAR(5)")
	private String adr_zip_cd;

	/** 
	 * 使用信用卡循環信用或現金卡情形<p/>
	 * A是信用卡 B是現金卡  C是ALL,空白是無
	 */
	@Column(name="DBCREDIT", length=1, columnDefinition="CHAR(1)")
	private String dbcredit;

	/** 
	 * 是否於本行財富管理有定時定額扣款<p/>
	 * Y/N
	 */
	@Column(name="ISPFUND", length=1, columnDefinition="CHAR(1)")
	private String ispfund;

	/** 
	 * 與本行其他業務往來(財富管理業務如基金保險信用卡等)<p/>
	 * Y/N
	 */
	@Column(name="OBUSINESS", length=1, columnDefinition="CHAR(1)")
	private String obusiness;

	/** 
	 * 與本行財富管理三個月平均總資產<p/>
	 * 萬元
	 */
	@Column(name="INVMBAL", columnDefinition="DECIMAL(10,0)")
	private BigDecimal invmbal;

	/** 
	 * 與他行財富管理三個月平均總資產<p/>
	 * 萬元
	 */
	@Column(name="INVOBAL", columnDefinition="DECIMAL(10,0)")
	private BigDecimal invobal;

	/** 
	 * 與金融機構存款往來情形(近六個月平均餘額)<p/>
	 * 萬元
	 */
	@Column(name="ODEP", columnDefinition="DECIMAL(10,0)")
	private BigDecimal odep;

	/** 
	 * 不動產狀況<p/>
	 * ‘1’/‘2’/’3’
	 */
	@Column(name="CMSSTATUS", length=1, columnDefinition="CHAR(1)")
	private String cmsstatus;

	/** 
	 * 文件狀態<p/>
	 * 編製中|1待覆核|2待母行覆核 | 2C核准|3婉卻|4呈區域授信中心|5呈總行法金處/授管處|6待補件 | 7提放審會|H1提常董會|H2審核中|A已會簽|B會簽中|C會簽待覆核|2A
	 */
	@Column(name="DOCSTATUS", length=2, columnDefinition="CHAR(2)")
	private String docstatus;

	/** 上傳資料日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="DATA_SRC_DT", columnDefinition="DATE")
	private Date data_src_dt;
	
	/** 
	 * 年薪(幣別)<p/>
	 */
	@Column(name="YPAY_SWFT", length=3, columnDefinition="CHAR(3)")
	private String ypay_swft;
	
	/** 
	 * 其他收入(幣別)<p/>
	 * C120S01B> othAmt
	 */
	@Column(name="OMONEY_AMT_SWFT", length=3, columnDefinition="CHAR(3)")
	private String omoney_amt_swft;
	 
	/** 
	 * 夫妻年收入幣別(之前命名為 家庭所得幣別)<p/>
	 */
	@Column(name="HINCOME_SWFT", length=3, columnDefinition="CHAR(3)")
	private String hincome_swft;
	
	/** 
	 * 與本行財富管理三個月平均總資產(幣別)<p/>
	 */
	@Column(name="INVMBAL_SWFT", length=3, columnDefinition="CHAR(3)")
	private String invmbal_swft;
	
	/** 
	 * 與他行財富管理三個月平均總資產(幣別)<p/>
	 */
	@Column(name="INVOBAL_SWFT", length=3, columnDefinition="CHAR(3)")
	private String invobal_swft;
	
	/** 
	 * 與金融機構存款往來情形(近六個月平均餘額)(幣別)<p/>
	 */
	@Column(name="ODEP_SWFT", length=3, columnDefinition="CHAR(3)")
	private String odep_swft;	

	/** 信用卡正卡張數(不含停用) **/
	@Column(name = "PRIMARY_CARD", columnDefinition = "DEC(3,0)")
	private BigDecimal primary_card;
	
	/** 信用卡附卡張數(不含停用) **/
	@Column(name = "ADDITIONAL_CARD", columnDefinition = "DEC(3,0)")
	private BigDecimal additional_card;
	
	/** 信用卡商務/採購卡張數(不含停用) **/
	@Column(name = "BUSINESS_OR_P_CARD", columnDefinition = "DEC(3,0)")
	private BigDecimal business_or_p_card;
	
	/** 服務單位統一編號 **/
	@Column(name="COMPANY_ID", length=11, columnDefinition="CHAR(11)")
	private String company_id;
	
	/** 服務單位資本總額(新台幣元) **/
	@Column(name = "TOTAL_CAPITAL", columnDefinition = "DEC(19,0)")
	private BigDecimal total_capital;
	
	/** 服務單位實收資本額(新台幣元) **/
	@Column(name = "PAIDUP_CAPITAL", columnDefinition = "DEC(19,0)")
	private BigDecimal paidup_capital;
	
	/** 服務單位組織類型{1:獨資,2:合夥,3:無限公司,4:有限公司,5:兩合公司,6:股份有限公司,7:其他,0:N/A} **/
	@Column(name="COMPANY_TYPE", length=1, columnDefinition="CHAR(1)")
	private String company_type;	
	
	/** 家庭年收入 **/
	@Column(name="FINCOME", columnDefinition="DEC(10,0)")
	private BigDecimal fincome;

	/** 家庭年收入幣別 **/
	@Column(name="FINCOME_SWFT", length=3, columnDefinition="CHAR(3)")
	private String fincome_swft;
	
	/** 卡友貸旗標 */
	@Column(name="C_FLAG", length=1, columnDefinition="CHAR(1)")
	private String c_flag;
	
	/** 取得分行別 **/
	public String getBr_cd() {
		return this.br_cd;
	}
	/** 設定分行別 **/
	public void setBr_cd(String value) {
		this.br_cd = value;
	}

	/** 取得NOTES文件編號 **/
	public String getNoteid() {
		return this.noteid;
	}
	/** 設定NOTES文件編號 **/
	public void setNoteid(String value) {
		this.noteid = value;
	}

	/** 取得客戶統一編號 **/
	public String getCustid() {
		return this.custid;
	}
	/** 設定客戶統一編號 **/
	public void setCustid(String value) {
		this.custid = value;
	}

	/** 取得重複序號 **/
	public String getDupno() {
		return this.dupno;
	}
	/** 設定重複序號 **/
	public void setDupno(String value) {
		this.dupno = value;
	}

	/** 取得評等模型類別 **/
	public String getMowtype() {
		return this.mowtype;
	}
	/** 設定評等模型類別 **/
	public void setMowtype(String value) {
		this.mowtype = value;
	}

	/** 取得模型版本-大版 **/
	public Integer getMowver1() {
		return this.mowver1;
	}
	/** 設定模型版本-大版 **/
	public void setMowver1(Integer value) {
		this.mowver1 = value;
	}

	/** 取得模型版本-小版 **/
	public Integer getMowver2() {
		return this.mowver2;
	}
	/** 設定模型版本-小版 **/
	public void setMowver2(Integer value) {
		this.mowver2 = value;
	}

	/** 取得JCIC查詢日期 YYYY-MM-DD **/
	public Date getJcic_date() {
		return this.jcic_date;
	}
	/** 設定JCIC查詢日期 YYYY-MM-DD **/
	public void setJcic_date(Date value) {
		this.jcic_date = value;
	}

	/** 取得主借款人統一編號(CUSTKEY) **/
	public String getCust_key() {
		return this.cust_key;
	}
	/** 設定主借款人統一編號(CUSTKEY) **/
	public void setCust_key(String value) {
		this.cust_key = value;
	}

	/** 
	 * 取得相關身分 ( LNGEFLAG)<p/>
	 * M: 主借款人  C: 共同借款人  G: 連帶保證人  N:一般保證人
	 */
	public String getLngeflag() {
		return this.lngeflag;
	}
	/**
	 *  設定相關身分 ( LNGEFLAG)<p/>
	 *  M: 主借款人  C: 共同借款人  G: 連帶保證人  N:一般保證人
	 **/
	public void setLngeflag(String value) {
		this.lngeflag = value;
	}

	/** 取得出生年月日 **/
	public Date getDob() {
		return this.dob;
	}
	/** 設定出生年月日 **/
	public void setDob(Date value) {
		this.dob = value;
	}

	/** 取得學歷 **/
	public Integer getEducation() {
		return this.education;
	}
	/** 設定學歷 **/
	public void setEducation(Integer value) {
		this.education = value;
	}

	/** 取得婚姻 **/
	public Integer getMarriage() {
		return this.marriage;
	}
	/** 設定婚姻 **/
	public void setMarriage(Integer value) {
		this.marriage = value;
	}

	/** 取得扶養子女數 **/
	public Integer getChildren() {
		return this.children;
	}
	/** 設定扶養子女數 **/
	public void setChildren(Integer value) {
		this.children = value;
	}

	/** 
	 * 取得工作年資<p/>
	 * 年
	 */
	public BigDecimal getSeniority() {
		return this.seniority;
	}
	/**
	 *  設定工作年資<p/>
	 *  年
	 **/
	public void setSeniority(BigDecimal value) {
		this.seniority = value;
	}

	/** 
	 * 取得職業<p/>
	 * 大類(2碼)+細項(1碼)+職稱(1碼)
	 */
	public String getPos() {
		return this.pos;
	}
	/**
	 *  設定職業<p/>
	 *  大類(2碼)+細項(1碼)+職稱(1碼)
	 **/
	public void setPos(String value) {
		this.pos = value;
	}

	/** 
	 * 取得年薪<p/>
	 * 萬元
	 */
	public BigDecimal getYpay() {
		return this.ypay;
	}
	/**
	 *  設定年薪<p/>
	 *  萬元
	 **/
	public void setYpay(BigDecimal value) {
		this.ypay = value;
	}

	/** 
	 * 取得其他收入
	 */
	public String getOmoney() {
		return this.omoney;
	}
	/**
	 *  設定其他收入
	 **/
	public void setOmoney(String value) {
		this.omoney = value;
	}

	/** 
	 * 取得其他收入金額<p/>
	 * 萬元
	 */
	public BigDecimal getOmoney_amt() {
		return this.omoney_amt;
	}
	/**
	 *  設定其他收入金額<p/>
	 *  萬元
	 **/
	public void setOmoney_amt(BigDecimal value) {
		this.omoney_amt = value;
	}

	/** 
	 * 取得夫妻年收入(之前命名為 家庭所得)<p/>
	 * 萬元
	 */
	public BigDecimal getHincome() {
		return this.hincome;
	}
	/**
	 *  設定夫妻年收入(之前命名為 家庭所得)<p/>
	 *  萬元
	 **/
	public void setHincome(BigDecimal value) {
		this.hincome = value;
	}

	/** 
	 * 取得個人負債比率<p/>
	 * %
	 */
	public BigDecimal getDrate() {
		return this.drate;
	}
	/**
	 *  設定個人負債比率<p/>
	 *  %
	 **/
	public void setDrate(BigDecimal value) {
		this.drate = value;
	}

	/** 
	 * 取得夫妻負債比率(之前命名為 家庭負債比率)<p/>
	 * %
	 */
	public BigDecimal getYrate() {
		return this.yrate;
	}
	/**
	 *  設定夫妻負債比率(之前命名為 家庭負債比率)<p/>
	 *  %
	 **/
	public void setYrate(BigDecimal value) {
		this.yrate = value;
	}
	
	/** 
	 * 取得家庭負債比率<p/>
	 * %
	 */
	public BigDecimal getFrate() {
		return this.frate;
	}
	/**
	 *  取得家庭負債比率<p/>
	 *  %
	 **/
	public void setFrate(BigDecimal value) {
		this.frate = value;
	}

	/** 
	 * 取得個人所得證明文件<p/>
	 * 1.個人綜合所得申報資料 2.扣繳憑單 3.薪資轉帳存摺 4.勞保薪資 5.租賃契約 6.其他收入證明
	 */
	public String getCertificate() {
		return this.certificate;
	}
	/**
	 *  設定個人所得證明文件<p/>
	 *  1.個人綜合所得申報資料 2.扣繳憑單 3.薪資轉帳存摺 4.勞保薪資 5.租賃契約 6.其他收入證明
	 **/
	public void setCertificate(String value) {
		this.certificate = value;
	}

	/** 
	 * 取得夫妻所得證明文件(之前命名為 家庭所得證明文件)<p/>
	 * 1.個人綜合所得申報資料 2.扣繳憑單 3.薪資轉帳存摺 4.勞保薪資 5.租賃契約 6.其他收入證明
	 */
	public String getHcertificate() {
		return this.hcertificate;
	}
	/**
	 *  設定夫妻所得證明文件(之前命名為 家庭所得證明文件)<p/>
	 *  1.個人綜合所得申報資料 2.扣繳憑單 3.薪資轉帳存摺 4.勞保薪資 5.租賃契約 6.其他收入證明
	 **/
	public void setHcertificate(String value) {
		this.hcertificate = value;
	}

	/** 取得戶籍地郵遞區號 **/
	public String getLawadr_zip_cd() {
		return this.lawadr_zip_cd;
	}
	/** 設定戶籍地郵遞區號 **/
	public void setLawadr_zip_cd(String value) {
		this.lawadr_zip_cd = value;
	}

	/** 取得居住地郵遞區號 **/
	public String getAdr_zip_cd() {
		return this.adr_zip_cd;
	}
	/** 設定居住地郵遞區號 **/
	public void setAdr_zip_cd(String value) {
		this.adr_zip_cd = value;
	}

	/** 
	 * 取得使用信用卡循環信用或現金卡情形<p/>
	 * A是信用卡 B是現金卡  C是ALL,空白是無
	 */
	public String getDbcredit() {
		return this.dbcredit;
	}
	/**
	 *  設定使用信用卡循環信用或現金卡情形<p/>
	 *  A是信用卡 B是現金卡  C是ALL,空白是無
	 **/
	public void setDbcredit(String value) {
		this.dbcredit = value;
	}

	/** 
	 * 取得是否於本行財富管理有定時定額扣款<p/>
	 * Y/N
	 */
	public String getIspfund() {
		return this.ispfund;
	}
	
	/**
	 *  設定是否於本行財富管理有定時定額扣款<p/>
	 *  Y/N
	 **/
	public void setIspfund(String value) {
		this.ispfund = value;
	}

	/** 
	 * 取得與本行其他業務往來(財富管理業務如基金保險信用卡等)<p/>
	 * Y/N
	 */
	public String getObusiness() {
		return this.obusiness;
	}
	/**
	 *  設定與本行其他業務往來(財富管理業務如基金保險信用卡等)<p/>
	 *  Y/N
	 **/
	public void setObusiness(String value) {
		this.obusiness = value;
	}

	/** 
	 * 取得與本行財富管理三個月平均總資產<p/>
	 * 萬元
	 */
	public BigDecimal getInvmbal() {
		return this.invmbal;
	}
	/**
	 *  設定與本行財富管理三個月平均總資產<p/>
	 *  萬元
	 **/
	public void setInvmbal(BigDecimal value) {
		this.invmbal = value;
	}

	/** 
	 * 取得與他行財富管理三個月平均總資產<p/>
	 * 萬元
	 */
	public BigDecimal getInvobal() {
		return this.invobal;
	}
	/**
	 *  設定與他行財富管理三個月平均總資產<p/>
	 *  萬元
	 **/
	public void setInvobal(BigDecimal value) {
		this.invobal = value;
	}

	/** 
	 * 取得與金融機構存款往來情形(近六個月平均餘額)<p/>
	 * 萬元
	 */
	public BigDecimal getOdep() {
		return this.odep;
	}
	/**
	 *  設定與金融機構存款往來情形(近六個月平均餘額)<p/>
	 *  萬元
	 **/
	public void setOdep(BigDecimal value) {
		this.odep = value;
	}

	/** 
	 * 取得不動產狀況<p/>
	 * ‘1’/‘2’/’3’
	 */
	public String getCmsstatus() {
		return this.cmsstatus;
	}
	/**
	 *  設定不動產狀況<p/>
	 *  ‘1’/‘2’/’3’
	 **/
	public void setCmsstatus(String value) {
		this.cmsstatus = value;
	}

	/** 
	 * 取得文件狀態<p/>
	 * 編製中|1待覆核|2待母行覆核 | 2C核准|3婉卻|4呈區域授信中心|5呈總行法金處/授管處|6待補件 | 7提放審會|H1提常董會|H2審核中|A已會簽|B會簽中|C會簽待覆核|2A
	 */
	public String getDocstatus() {
		return this.docstatus;
	}
	/**
	 *  設定文件狀態<p/>
	 *  編製中|1待覆核|2待母行覆核 | 2C核准|3婉卻|4呈區域授信中心|5呈總行法金處/授管處|6待補件 | 7提放審會|H1提常董會|H2審核中|A已會簽|B會簽中|C會簽待覆核|2A
	 **/
	public void setDocstatus(String value) {
		this.docstatus = value;
	}

	/** 取得上傳資料日期 **/
	public Date getData_src_dt() {
		return this.data_src_dt;
	}
	/** 設定上傳資料日期 **/
	public void setData_src_dt(Date value) {
		this.data_src_dt = value;
	}
	

	/** 取得**/
	public String getAcct_key() {
		return this.acct_key;
	}
	/** 設定 **/
	public void setAcct_key(String value) {
		this.acct_key = value;
	}
	
	/** 
	 * 設定年薪(幣別)<p/>
	 */
	public void setYpay_swft(String value) {
		this.ypay_swft = value;
	}
	
	/** 
	 * 取得年薪(幣別)<p/>
	 */
	public String getYpay_swft() {
		return ypay_swft;
	}
	
	/** 
	 * 設定其他收入(幣別)<p/>
	 * C120S01B> othAmt
	 */
	public void setOmoney_amt_swft(String value) {
		this.omoney_amt_swft = value;
	}
	
	/** 
	 * 取得其他收入(幣別)<p/>
	 * C120S01B> othAmt
	 */
	public String getOmoney_amt_swft() {
		return omoney_amt_swft;
	}
	
	/** 
	 * 設定夫妻年收入幣別(之前命名為 家庭所得幣別)<p/>
	 */
	public void setHincome_swft(String value) {
		this.hincome_swft = value;
	}
	
	/** 
	 * 取得夫妻年收入幣別(之前命名為 家庭所得幣別)<p/>
	 */
	public String getHincome_swft() {
		return hincome_swft;
	}
	
	/** 
	 * 設定與本行財富管理三個月平均總資產(幣別)<p/>
	 */
	public void setInvmbal_swft(String value) {
		this.invmbal_swft = value;
	}
	
	/** 
	 * 取得與本行財富管理三個月平均總資產(幣別)<p/>
	 */
	public String getInvmbal_swft() {
		return invmbal_swft;
	}
	
	/** 
	 * 設定與他行財富管理三個月平均總資產(幣別)<p/>
	 */
	public void setInvobal_swft(String value) {
		this.invobal_swft = value;
	}
	
	/** 
	 * 取得與他行財富管理三個月平均總資產(幣別)<p/>
	 */
	public String getInvobal_swft() {
		return invobal_swft;
	}
	
	/** 
	 * 設定與金融機構存款往來情形(近六個月平均餘額)(幣別)<p/>
	 */
	public void setOdep_swft(String value) {
		this.odep_swft = value;
	}
	
	/** 
	 * 取得與金融機構存款往來情形(近六個月平均餘額)(幣別)<p/>
	 */
	public String getOdep_swft() {
		return odep_swft;
	}

	/** 取得信用卡正卡張數(不含停用) **/
	public BigDecimal getPrimary_card() {
		return primary_card;
	}
	/** 設定信用卡正卡張數(不含停用) **/
	public void setPrimary_card(BigDecimal primary_card) {
		this.primary_card = primary_card;
	}
	
	/** 取得信用卡附卡張數(不含停用) **/
	public BigDecimal getAdditional_card() {
		return additional_card;
	}
	/** 設定信用卡附卡張數(不含停用) **/
	public void setAdditional_card(BigDecimal additional_card) {
		this.additional_card = additional_card;
	}

	/** 取得信用卡商務/採購卡張數(不含停用) **/
	public BigDecimal getBusiness_or_p_card() {
		return business_or_p_card;
	}
	/** 設定信用卡商務/採購卡張數(不含停用) **/
	public void setBusiness_or_p_card(BigDecimal business_or_p_card) {
		this.business_or_p_card = business_or_p_card;
	}
	/** 取得服務單位統一編號 **/
	public String getCompany_id() {
		return company_id;
	}
	/** 設定服務單位統一編號 **/
	public void setCompany_id(String company_id) {
		this.company_id = company_id;
	}
	/** 取得服務單位資本總額(新台幣元) **/
	public BigDecimal getTotal_capital() {
		return total_capital;
	}
	/** 設定服務單位資本總額(新台幣元) **/
	public void setTotal_capital(BigDecimal total_capital) {
		this.total_capital = total_capital;
	}
	/** 取得服務單位實收資本額(新台幣元) **/
	public BigDecimal getPaidup_capital() {
		return paidup_capital;
	}
	/** 設定服務單位實收資本額(新台幣元) **/
	public void setPaidup_capital(BigDecimal paidup_capital) {
		this.paidup_capital = paidup_capital;
	}
	/** 取得服務單位組織類型{1:獨資,2:合夥,3:無限公司,4:有限公司,5:兩合公司,6:股份有限公司,7:其他,0:N/A} **/
	public String getCompany_type() {
		return company_type;
	}
	/** 設定服務單位組織類型{1:獨資,2:合夥,3:無限公司,4:有限公司,5:兩合公司,6:股份有限公司,7:其他,0:N/A} **/
	public void setCompany_type(String company_type) {
		this.company_type = company_type;
	}
	/** 取得家庭年收入 **/
	public BigDecimal getFincome() {
		return fincome;
	}
	/** 設定家庭年收入 **/
	public void setFincome(BigDecimal fincome) {
		this.fincome = fincome;
	}
	/** 取得家庭年收入幣別 **/
	public String getFincome_swft() {
		return fincome_swft;
	}
	/** 設定家庭年收入幣別 **/
	public void setFincome_swft(String fincome_swft) {
		this.fincome_swft = fincome_swft;
	}

	/** 取得卡友貸旗標 */
	public String getC_flag() {
		return c_flag;
	}
	/** 設定卡友貸旗標 */
	public void setC_flag(String c_flag) {
		this.c_flag = c_flag;
	}
}
