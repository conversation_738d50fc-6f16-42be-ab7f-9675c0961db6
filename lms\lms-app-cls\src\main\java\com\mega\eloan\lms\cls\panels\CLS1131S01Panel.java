package com.mega.eloan.lms.cls.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.iisigroup.cap.utils.CapAppContext;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.cls.service.CLS1141Service;
import com.mega.eloan.lms.dc.util.Util;
import com.mega.eloan.lms.model.L120M01A;



/**
 * <pre>
 * 個金徵信作業
 * </pre>
 * 
 * @since 2012/10/11
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/10/11,Fantasy,new
 *          </ul>
 */
public class CLS1131S01Panel extends Panel {

	
	CLS1141Service service1141;
	
	CLSService clsService;
	
	private static final long serialVersionUID = 1L;

	private String mainId;

	/**
	 * @param id
	 */
	public CLS1131S01Panel(String id, String mainId) {
		super(id);
		this.mainId = mainId;
		this.clsService = CapAppContext.getBean("CLSService");
		this.service1141 = CapAppContext.getBean("CLS1141ServiceImpl");
	}
	
	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);

		/*
		像簽報書, 有在  CLS1141M01Page.java 裡, 去呼叫 clsService.active_SAS_AML(...)
		以及 clsService.is_aml_lockEdit(...)
		決定在 Panel 傳入的參數值, 控制 AML 相關的 button, field 是否隱藏/呈現
		
		但為何未在  CLS1131S01EPanel 做類似的判斷？
		=> 兩者的架構不同
		*/
//		~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		/*
		AbstractCapPage --- AbstractEloanPage --- AbstractEloanForm  --- CLS1141M01Page 
														public void execute(PageParameters params) {...}
												
                                              --- AbstractOutputPage --- CLS1131P01Page 
														public String getOutputString(PageParameters params) {...}
			   
		比較 AbstractEloanForm 及 AbstractOutputPage 的差異
		
		(一)
		在 svn 20364 中, AbstractOutputPage 原本 extends AbstractCapPage (更上面一層)
		在 svn 21125 中, AbstractOutputPage 改成 extends AbstractEloanPage
		===> 在 AbstractEloanPage 中, 填入了前端使用的 userInfo 物件
		
		
		(二)
		ELoanLoginPage.html 有包含 jawr 
		但無 AbstractOutputPage.html
		
		
		(三)
		AbstractEloanForm 內
			有 public JSONObject authToJSON(PageParameters params) {...}
				有去呼叫 getDomainClass() 去判斷 docStatus 				
			在 public void afterExecute(PageParameters parameters) {...}
				有去 add EloanHeaderPanel［登錄行員、登入時間、HelpDesk系統］
		
		AbstractOutputPage 內
			已經有實作了 public void execute(PageParameters params) {...}
			並呼叫 getOutputString(params)			
		*/
		new CLS1131S01MPanel("CLS1131S01_M").processPanelData(model, params);
		new CLS1131S01APanel("CLS1131S01_A").processPanelData(model, params);
		new CLS1131S01BPanel("CLS1131S01_B").processPanelData(model, params);
		new CLS1131S01CPanel("CLS1131S01_C").processPanelData(model, params);
		new CLS1131S01DPanel("CLS1131S01_D").processPanelData(model, params);
		new CLS1131S01EPanel("CLS1131S01_E").processPanelData(model, params);
		new CLS1131S01FPanel("CLS1131S01_F").processPanelData(model, params);
		new CLS1131S01XPanel("CLS1131S01_X").processPanelData(model, params);
		new CLS1131S02CPanel("CLS1131S02_C").processPanelData(model, params);// 系統初評
		new CLS1131S01ZPanel("CLS1131S01_Z").processPanelData(model, params);// 信貸集中徵信
		
		//J-109-0273_10702_B1001 Web e-Loan 調整申貸資料核對表內容，增加版本
		//徵信作業>基本資料，一律秀新版本
		if(Util.isEmpty(mainId)){
			new CLS1131S01VPanel02("CLS1131S01_V").processPanelData(model,params);
			model.addAttribute("panelPath", "cls/panels/CLS1131S01VPanel02");
		}
		else{
			L120M01A l120m01a = service1141.findL120m01aByMainId(mainId);
			String checkListVersion = "";
			if(l120m01a != null){
				checkListVersion=clsService.findCheckListVersion(service1141.findL120m01aByMainId(mainId));
			}
			
			if(Util.isEmpty(checkListVersion)){
				new CLS1131S01VPanel("CLS1131S01_V").processPanelData(model,params);
				model.addAttribute("panelPath", "cls/panels/CLS1131S01VPanel");
			}
			else{
				new CLS1131S01VPanel02("CLS1131S01_V").processPanelData(model,params);
				model.addAttribute("panelPath", "cls/panels/CLS1131S01VPanel02");
			}
		}
		
	}
}
