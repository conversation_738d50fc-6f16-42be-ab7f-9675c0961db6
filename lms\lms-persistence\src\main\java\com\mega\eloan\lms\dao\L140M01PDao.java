/* 
 * L140M01PDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140M01P;

/** 敘做條件異動比較表 **/
public interface L140M01PDao extends IGenericDao<L140M01P> {

	L140M01P findByOid(String oid);

	List<L140M01P> findByMainId(String mainId);

	L140M01P findByUniqueKey(String mainId);

	List<L140M01P> findByIndex01(String mainId);
	
	L140M01P findByMainidSeq(String mainId, Integer Seq);
}