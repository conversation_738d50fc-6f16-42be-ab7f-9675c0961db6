package com.mega.eloan.lms.lms.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * 基本資料
 * ⇒ 原始參照 LMS1115S02PanelC1
 * </pre>
 * 
 * @since 2017/2/1
 * <AUTHOR>
 * @version <ul>
 *          <li>2017/2/1,EL08034,new
 *          </ul>
 */
public class LMS1035S02PanelC1 extends Panel {
	private static final long serialVersionUID = 1L;

	private boolean isBasicData;

	public LMS1035S02PanelC1(String id) {
		super(id);
	}

	public LMS1035S02PanelC1(String id, boolean updatePanelName, boolean isBasicData) {
		super(id, updatePanelName);
		this.isBasicData = isBasicData;
	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);

		model.addAttribute("hs_baseData_Y", isBasicData);
		model.addAttribute("hs_baseData_N", !isBasicData);
		
		model.addAttribute("hs_baseData_Y1", isBasicData);
		model.addAttribute("hs_baseData_N1", !isBasicData);
	}
	
	@Override
	protected String getViewName() {
		return getEloanPagePathByClass(getClass());
	}
}
