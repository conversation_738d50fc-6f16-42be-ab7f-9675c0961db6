//驗證readOnly狀態
var lastSel;
var LMS2305Action = {
    fhandle: "lms2305m01formhandler",
    ghandle: "lms2305gridhandler",
    /**檢查目前文件狀態*/
    checkReadonly: function(){
        var auth = (responseJSON ? responseJSON.Auth : {}); //權限
        if (auth.readOnly || responseJSON.mainDocStatus != "01O") {
            return true;
        }
        return false;
    },
    /**儲存的動作*/
    saveAction: function(){
        var $form = $("#L230M01AForm");
        
        if ($form.length > 0 && !$form.valid()) {
            return;
        }
        $.ajax({
            type: "POST",
            handler: LMS2305Action.fhandle,
            action: "saveL230m01a",
            data: {
                showMsg: true,
				L230M01AForm: JSON.stringify($form.serializeData())
				}
            }).done(function(responseData){
                $("#L230M01AForm").injectData(responseData);
                if ($("#mainOid").val()) {
                    setRequiredSave(false);
                    responseJSON.mainOid = $("#mainOid").val();
                } else {
                    setRequiredSave(true);
                }
                CommonAPI.triggerOpener("gridview", "reloadGrid");
        	});
    },
    /**呈主管 -  編製中*/
    sendBoss: function(){
        $.ajax({
            handler: LMS2305Action.fhandle,
            data: {
                formAction: "checkData",
				L230M01AForm: JSON.stringify($("#L230M01AForm").serializeData())
				}
            }).done(function(json){
                $(".boss").setItems({
                    item: json.bossList
                });
                //confirmApply=是否呈主管覆核？
                CommonAPI.confirmMessage(i18n.def["confirmApply"], function(b){
                    if (b) {
                        $("#selectBossBox").thickbox({ // 使用選取的內容進行彈窗
                            //l230m01a.title20=呈主管覆核
                            title: i18n.lms2305m01['l230m01a.title20'],
                            width: 500,
                            height: 180,
                            modal: true,
                            valign: "bottom",
                            align: "center",
                            readOnly: false,
                            i18n: i18n.def,
                            buttons: {
                                "sure": function(){
                                    var account = $("#accountPerson").val();
                                    var manager = $("#manager").val();
                                    if (account == "") {
                                        //l230m01a.title11=授信主管
                                        return CommonAPI.showErrorMessage(i18n.lms2305m01['l230m01a.title21'] + i18n.lms2305m01['l230m01a.title11']);
                                    }
                                    if (manager == "") {
                                        //l230m01a.title10=經副襄理
                                        return CommonAPI.showErrorMessage(i18n.lms2305m01['l230m01a.title21'] + i18n.lms2305m01['l230m01a.title10']);
                                    }
                                    LMS2305Action.flowAction({
                                        page: responseJSON.page,
                                        account: account,
                                        manager: manager
                                    });
                                    $.thickbox.close();
                                },
                                
                                "cancel": function(){
                                    $.thickbox.close();
                                }
                            }
                        });
                    }
                });
        	});
    },
    /**待覆核  - 覆核*/
    openCheck: function(){
        $("#openCheckBox").thickbox({ // 使用選取的內容進行彈窗
            //l230m01a.title22=覆核
            title: i18n.lms2305m01['l230m01a.title22'],
            width: 100,
            height: 150,
            modal: true,
            readOnly: false,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                
                    var val = $("[name=checkRadio]:checked").val();
                    if (!val) {
                        //l230m01a.title21=請選擇
                        return CommonAPI.showMessage(i18n.lms2305m01['l230m01a.title21']);
                    }
                    $.thickbox.close();
                    switch (val) {
                        case "1":
                            //一般退回到編製中01O
                            //l230m01a.title23=該案件是否退回經辦修改？要退回請按【確定】，不退回請按【取消】
                            CommonAPI.confirmMessage(i18n.lms2305m01['l230m01a.title23'], function(b){
                                if (b) {
                                    LMS2305Action.flowAction({
                                        flowAction: "back"
                                    });
                                }
                            });
                            break;
                        case "2":
                            //核定
                            //l230m01a.title24=該案件是否核准？確定請按【確定】，否則請按【取消】離開
                            CommonAPI.confirmMessage(i18n.lms2305m01["l230m01a.title24"], function(b){
                                if (b) {
                                    LMS2305Action.checkDate();
                                }
                            });
                            break;
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    /**輸入核定日期視窗*/
    checkDate: function(){
        $("#forCheckDate").val(CommonAPI.getToday());
        $("#openChecDatekBox").thickbox({ // 使用選取的內容進行彈窗
            //l230m01a.title25= 請輸入核定日
            title: i18n.lms2305m01['l230m01a.title25'],
            width: 100,
            height: 100,
            modal: true,
            readOnly: false,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var forCheckDate = $("#forCheckDate").val();
                    if ($.trim(forCheckDate) == "") {
                        //l230m01a.title25= 請輸入核定日
                        return CommonAPI.showErrorMessage(i18n.lms2305m01['l230m01a.title25']);
                    }
                    LMS2305Action.flowAction({
                        flowAction: "ok",
                        checkDate: forCheckDate
                    });
                    $.thickbox.close();
                },
                
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    
    flowAction: function(sendData){
        $.ajax({
            type: "POST",
            handler: LMS2305Action.fhandle,
            data: $.extend({
                formAction: "flowAction",
                mainOid: responseJSON.mainOid,
                txCode: responseJSON.txCode
            }, (sendData || {}))
			}).done(function(){
                CommonAPI.triggerOpener("gridview", "reloadGrid");
                API.showPopMessage(i18n.def["runSuccess"], window.close);
        	});
    },
    /**自簽報書引進-由舊資料複製到新資料上*/
    create_NewDoc_Form_OldDoc: function(cellvalue, options, rowObject){
        $.form.init({
            formHandler: LMS2305Action.fhandle,
            formPostData: {
                formAction: "getNewL120M0a",
                caseMainId: rowObject.mainId,
                mainOid: $("#mainOid").val()
            },
            loadSuccess: function(json){
            
            }
        });
    },
    /***自簽報書引進--請輸入借款人統一編號(不含重複序號)--簽報書選擇Grid***/
    SignReportGridReload: function(){
        $("#signReportGrid").jqGrid("setGridParam", {
            postData: {
                custId: $("#custId").html(),
                formAction: "queryL120M01A"
            },
            search: true
        }).trigger("reloadGrid");
    }
    
};


$(function(){
    if (LMS2305Action.checkReadonly()) {
        $("form").lockDoc();
    }
    $.form.init({
        formHandler: LMS2305Action.fhandle,
        formPostData: {
            formAction: "queryL230m01a",
            mainOid: responseJSON.mainOid,//rowObject.oid,
            mainDocStatus: responseJSON.mainDocStatus//viewstatus
        },
        loadSuccess: function(json){
        
        }
    });
    
    var btn = $("#buttonPanel");
    btn.find("#btnSave").click(function(){
        LMS2305Action.saveAction();
    }).end().find("#btnSend").click(function(){
        LMS2305Action.sendBoss();
    }).end().find("#btnCheck").click(function(){
        LMS2305Action.openCheck()
    });
    
});

$.extend(window.tempSave, {
    handler: LMS2305Action.fhandle,
    action: "tempSave",
    beforeCheck: function(){
        var $form = $("#L230M01AForm");
        var result = false;
        switch (responseJSON.page + "") {
            case "01":
            case "02":
                result = $form.valid();
                break;
            default:
                result = true;
                break;
        }
        
        return result;
    },
    sendData: function(){
		return { L230M01AForm: JSON.stringify($("#L230M01AForm").serializeData()) }
    }
});

