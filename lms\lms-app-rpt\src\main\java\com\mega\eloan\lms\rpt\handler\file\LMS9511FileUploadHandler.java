/* 
 * LMS9530FileUploadHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.handler.file;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;

import javax.annotation.Resource;

import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.multipart.MultipartFile;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.dao.DocFileDao;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.DocFileService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.handler.FileUploadHandler;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;

/**
 * <pre>
 * 最新管理報表
 * </pre>
 * 
 * @since 2013/1/10
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/1/10,Vector,new
 *          </ul>
 */
@Scope("request")
@Controller("lms9511fileuploadhandler")
public class LMS9511FileUploadHandler extends FileUploadHandler {
	@Resource
	DocFileDao fileDao;
	
	@Autowired
	DocFileService fileService;

	@Override
	public IResult afterUploaded(PageParameters params) throws CapException {
		MultipartFile uFile = params.getFile(params.getString("fieldId"));
		String oid = params.getString(EloanConstants.OID);
		FileOutputStream stream = null;
		InputStream is = null;
		try {
			is = uFile.getInputStream();
			byte[] data = IOUtils.toByteArray(is);
			DocFile docFile = new DocFile();
			docFile.setOid(oid);
			docFile = fileDao.find(docFile);
			docFile.setData(data);
			docFile.setSrcFileName(params.getString("fileName"));
			docFile.setContentType(uFile.getContentType());
			docFile.setUploadTime(CapDate.getCurrentTimestamp());
			docFile.setFileDesc(params.getString("fileDesc"));
			fileService.save(docFile);
			File realFile = fileService.getRealFile(docFile);
			String path = realFile.getPath();
			realFile.deleteOnExit();
			stream = new FileOutputStream(new File(path), false);
			stream.write(data);
			stream.close();

		} catch (IOException e) {
			logger.error(e.getMessage(), e);
			throw new CapMessageException("file IO ERROR", getClass());
		} finally {
			if (is != null) {
				try {
					is.close();
				} catch (IOException e) {
					logger.debug("inputStream close Error", getClass());
				}
			}
			if (stream != null) {
				try {
					stream.close();
				} catch (IOException e) {
					logger.debug("inputStream close Error", getClass());
				}
			}
		}

		return new CapAjaxFormResult();
	}

	@Override
	public String getOperationName() {
		return "fileUploadOperation";
	}

}
