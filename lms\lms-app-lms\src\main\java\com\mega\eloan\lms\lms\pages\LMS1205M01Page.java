/*
 * LMS1205M01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.pages;

import java.util.Map;
import java.util.Properties;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.eloan.lms.base.panels.LMSL140M01MPanel;
import com.mega.eloan.lms.base.panels.LMSM04Panel;
import com.mega.eloan.lms.base.panels.LMSM05Panel;
import com.mega.eloan.lms.base.panels.LMSM06Panel;
import com.mega.eloan.lms.base.panels.LMSS20APanel;
import com.mega.eloan.lms.base.panels.LMSS21APanel;
import com.mega.eloan.lms.base.panels.LMSS22APanel;
import com.mega.eloan.lms.base.panels.LMSS24APanel;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.lms.panels.LMS1205S05Panel;
import com.mega.eloan.lms.lms.panels.LMS1205S06Panel;
import com.mega.eloan.lms.lms.panels.LMS1205S11Panel;
import com.mega.eloan.lms.lms.panels.LMS1205S12Panel;
import com.mega.eloan.lms.lms.panels.LMS1205S13Panel;
import com.mega.eloan.lms.lms.panels.LMS1205S14Panel;
import com.mega.eloan.lms.lms.panels.LMS1205S15Panel;
import com.mega.eloan.lms.lms.panels.LMS1205S16Panel;
import com.mega.eloan.lms.lms.panels.LMS1205S17Panel;
import com.mega.eloan.lms.lms.panels.LMS1405S01Panel;
import com.mega.eloan.lms.lms.panels.LMS1405S02Panel;
import com.mega.eloan.lms.lms.panels.LMS1405S02Panel04;
import com.mega.eloan.lms.lms.panels.LMS1405S04Panel;
import com.mega.eloan.lms.lms.panels.LMS1405S06Panel;
import com.mega.eloan.lms.lms.panels.LMS1405S07Panel;
import com.mega.eloan.lms.lms.panels.LMS1405S08Panel;
import com.mega.eloan.lms.lms.panels.LMS1405S09Panel;
import com.mega.eloan.lms.lms.panels.LMS1405S10Panel;
import com.mega.eloan.lms.lms.panels.LMS1405S11Panel;
import com.mega.eloan.lms.lms.panels.LMS7305M01Panel;
import com.mega.eloan.lms.lms.panels.LMSM01Panel;
import com.mega.eloan.lms.lms.panels.LMSS01Panel;
import com.mega.eloan.lms.lms.panels.LMSS02Panel;
import com.mega.eloan.lms.lms.panels.LMSS03Panel;
import com.mega.eloan.lms.lms.panels.LMSS04Panel;
import com.mega.eloan.lms.lms.panels.LMSS07Panel;
import com.mega.eloan.lms.lms.panels.LMSS08Panel;
import com.mega.eloan.lms.lms.panels.LMSS09Panel;
import com.mega.eloan.lms.lms.panels.LMSS10Panel;
import com.mega.eloan.lms.lms.service.LMS1205Service;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.auth.CodeItemService;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 簽報書明細頁籤與按鈕控制(企金授權外)
 * </pre>
 * 
 * @since 2011/5/3
 * <AUTHOR> Lin
 * @version <ul>
 *          <li>2011/5/3,Miller Lin,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms1205m01/{page}")
public class LMS1205M01Page extends AbstractEloanForm {

	@Autowired
	CodeItemService cis;
	@Autowired
	LMS1205Service service1205;
	@Autowired
	LMSService lmsService;
	@Autowired
	BranchService branch;

	final String TAB_CTX = "_tabCtx";
	final String TAB_SIGN = "tab-";

	@Override
	public void execute(ModelMap model, PageParameters params) {
		// 依權限設定button
		// 案件考核表分頁
		model.addAttribute("_lmss7305_panel_visible", true);
		new LMS7305M01Panel("lmss7305_panel").processPanelData(model, params);
		model.addAttribute("_lmsm04_panel_visible", true);
		new LMSM04Panel("lmsm04_panel").processPanelData(model, params);
		model.addAttribute("_lmsm05_panel_visible", true);
		new LMSM05Panel("lmsm05_panel").processPanelData(model, params);

		// 可排除利害關係人授信限制原因共用元件_放在登入審核層級thickbox裡
		model.addAttribute("_lmsm06_panel_for_caseLvl_visible", true);
		new LMSM06Panel("lmsm06_panel_for_caseLvl").processPanelData(model, params);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 如果存在海外聯貸案文件狀態則新增此狀態的按鈕
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L120M01A l120m01a = service1205.findL120m01aByOid(mainOid);
		if (CreditDocStatusEnum.營運中心_海外聯貸案_會簽中.getCode().equals(
				l120m01a.getAreaDocstatus()) && user.getUnitNo().equals(l120m01a.getAreaBrId())) {
			hdefaultBtn(params, model);

			addAclLabel(model, new AclLabel("_btnDOC_SEA_SIGNING", AuthType.Modify, params, true));
			addAclLabel(model, new AclLabel("_btnDOC_SEA_SIGNING2", AuthType.Accept, params, true));
			addAclLabel(model, new AclLabel("_btnDOC_SEA_SIGNING3", AuthType.Query, params, true));
			addAclLabel(model, new AclLabel("_btnDOC_SEA_CWAITGO", AuthType.Modify, params, false));
			addAclLabel(model, new AclLabel("_btnDOC_SEA_CWAITGO2", AuthType.Accept, params, false));
			addAclLabel(model, new AclLabel("_btnDOC_SEA_CWAITGO3", AuthType.Query, params, false));
			addAclLabel(model, new AclLabel("_btnDOC_SEA_ALREADYSIGN", AuthType.Modify, params, false));
			addAclLabel(model, new AclLabel("_btnDOC_SEA_ALREADYSIGN2", AuthType.Accept, params, false));
			addAclLabel(model, new AclLabel("_btnDOC_SEA_ALREADYSIGN3", AuthType.Query, params, false));
		} else if (CreditDocStatusEnum.營運中心_海外聯貸案_待放行.getCode().equals(
				l120m01a.getAreaDocstatus()) && user.getUnitNo().equals(l120m01a.getAreaBrId())) {
			hdefaultBtn(params, model);

			addAclLabel(model, new AclLabel("_btnDOC_SEA_CWAITGO", AuthType.Modify, params, true));
			addAclLabel(model, new AclLabel("_btnDOC_SEA_CWAITGO2", AuthType.Accept, params, true));
			addAclLabel(model, new AclLabel("_btnDOC_SEA_CWAITGO3", AuthType.Query, params, true));
			addAclLabel(model, new AclLabel("_btnDOC_SEA_SIGNING", AuthType.Modify, params, false));
			addAclLabel(model, new AclLabel("_btnDOC_SEA_SIGNING2", AuthType.Accept, params, false));
			addAclLabel(model, new AclLabel("_btnDOC_SEA_SIGNING3", AuthType.Query, params, false));
			addAclLabel(model, new AclLabel("_btnDOC_SEA_ALREADYSIGN", AuthType.Modify, params, false));
			addAclLabel(model, new AclLabel("_btnDOC_SEA_ALREADYSIGN2", AuthType.Accept, params, false));
			addAclLabel(model, new AclLabel("_btnDOC_SEA_ALREADYSIGN3", AuthType.Query, params, false));
		} else if (CreditDocStatusEnum.營運中心_海外聯貸案_已會簽.getCode().equals(
				l120m01a.getAreaDocstatus()) && user.getUnitNo().equals(l120m01a.getAreaBrId())) {
			hdefaultBtn(params, model);
			if ("S".equals(user.getUnitType())
					&& CreditDocStatusEnum.營運中心_海外聯貸案_已會簽.getCode().equals(
							l120m01a.getAreaDocstatus())) {
				// UPGRADE: 待確認addOrReplace的功能
				// addOrReplace(new AclLabel("_btnDOC_HEDITING0", params, getDomainClass(),
				// AuthType.Modify, CreditDocStatusEnum.授管處_審查中));
				addAclLabel(model, new AclLabel("_btnDOC_HEDITING0", params, getDomainClass(), AuthType.Modify,
						CreditDocStatusEnum.授管處_審查中));
			}

			addAclLabel(model, new AclLabel("_btnDOC_SEA_ALREADYSIGN", AuthType.Modify, params, true));
			addAclLabel(model, new AclLabel("_btnDOC_SEA_ALREADYSIGN2", AuthType.Accept, params, true));
			addAclLabel(model, new AclLabel("_btnDOC_SEA_ALREADYSIGN3", AuthType.Query, params, true));
			addAclLabel(model, new AclLabel("_btnDOC_SEA_SIGNING", AuthType.Modify, params, false));
			addAclLabel(model, new AclLabel("_btnDOC_SEA_SIGNING2", AuthType.Accept, params, false));
			addAclLabel(model, new AclLabel("_btnDOC_SEA_SIGNING3", AuthType.Query, params, false));
			addAclLabel(model, new AclLabel("_btnDOC_SEA_CWAITGO", AuthType.Modify, params, false));
			addAclLabel(model, new AclLabel("_btnDOC_SEA_CWAITGO2", AuthType.Accept, params, false));
			addAclLabel(model, new AclLabel("_btnDOC_SEA_CWAITGO3", AuthType.Query, params, false));
		} else {
			if (user.getUnitNo().equals(Util.trim(l120m01a.getOwnBrId()))) {
				defaultBtn(params, model);
			} else {
				if (CreditDocStatusEnum.海外_已核准.getCode().equals(
						l120m01a.getDocStatus())
						|| CreditDocStatusEnum.海外_婉卻.getCode().equals(
								l120m01a.getDocStatus())
						||
						// CreditDocStatusEnum.海外_免批覆.getCode().equals(docStatus)
						// ||
						CreditDocStatusEnum.泰國_提會待登錄.getCode().equals(
								l120m01a.getDocStatus())
						|| CreditDocStatusEnum.泰國_提會待覆核.getCode().equals(
								l120m01a.getDocStatus())) {
					defaultBtn(params, model);
				} else {
					hdefaultBtn(params, model);
				}
			}

			addAclLabel(model, new AclLabel("_btnDOC_SEA_SIGNING", AuthType.Modify, params, false));
			addAclLabel(model, new AclLabel("_btnDOC_SEA_SIGNING2", AuthType.Accept, params, false));
			addAclLabel(model, new AclLabel("_btnDOC_SEA_SIGNING3", AuthType.Query, params, false));
			addAclLabel(model, new AclLabel("_btnDOC_SEA_CWAITGO", AuthType.Modify, params, false));
			addAclLabel(model, new AclLabel("_btnDOC_SEA_CWAITGO2", AuthType.Accept, params, false));
			addAclLabel(model, new AclLabel("_btnDOC_SEA_CWAITGO3", AuthType.Query, params, false));
			addAclLabel(model, new AclLabel("_btnDOC_SEA_ALREADYSIGN", AuthType.Modify, params, false));
			addAclLabel(model, new AclLabel("_btnDOC_SEA_ALREADYSIGN2", AuthType.Accept, params, false));
			addAclLabel(model, new AclLabel("_btnDOC_SEA_ALREADYSIGN3", AuthType.Query, params, false));
		}

		renderJsI18N(LMS1205M01Page.class);
		renderJsI18N(LMSM01Panel.class);
		renderJsI18N(LMSM05Panel.class);
		renderRespMsgJsI18N(UtilConstants.AJAX_RSP_MSG.ERR_MSG); // 多render一個msgi18n
		// renderJsI18N(LMS1205V01Page.class);
		// System.out.println("123");
		// tabs
		int page = Util.parseInt(params.getString("page"));
		String tabID = TAB_SIGN + Util.addZeroWithValue(page, 2); // 指定ID
		Panel panel = getPanel(page, l120m01a, model);
		panel.processPanelData(model, params);
		model.addAttribute("tabIdx", tabID);
	}// ;

	private void defaultBtn(PageParameters params, ModelMap model) {
		// 海外
		// add(new FlowButton("_FlowButton", "LMS1205M01Flow", params,
		// getDomainClass(), CreditDocStatusEnum.class, false));
		addAclLabel(model,
				new AclLabel("_btnDOC_EDITING", params, getDomainClass(), AuthType.Modify, CreditDocStatusEnum.海外_編製中));
		addAclLabel(model, new AclLabel("_btnDOC_EDITING2", params, getDomainClass(), AuthType.Accept,
				CreditDocStatusEnum.海外_編製中));
		addAclLabel(model,
				new AclLabel("_btnDOC_EDITING3", params, getDomainClass(), AuthType.Query, CreditDocStatusEnum.海外_編製中));

		addAclLabel(model, new AclLabel("_btnWAIT_APPROVE", params, getDomainClass(), AuthType.Modify,
				CreditDocStatusEnum.海外_待覆核, CreditDocStatusEnum.海外_總行待覆核));
		addAclLabel(model, new AclLabel("_btnWAIT_APPROVE2", params, getDomainClass(), AuthType.Accept,
				CreditDocStatusEnum.海外_待覆核, CreditDocStatusEnum.海外_總行待覆核));
		addAclLabel(model, new AclLabel("_btnWAIT_APPROVE3", params, getDomainClass(), AuthType.Query,
				CreditDocStatusEnum.海外_待覆核, CreditDocStatusEnum.海外_總行待覆核));

		addAclLabel(model, new AclLabel("_btnWORK_LOOKING", params, getDomainClass(), AuthType.Modify,
				CreditDocStatusEnum.海外_呈授管處, CreditDocStatusEnum.海外_呈營運中心, CreditDocStatusEnum.海外_呈總行));
		addAclLabel(model, new AclLabel("_btnWORK_LOOKING2", params, getDomainClass(), AuthType.Accept,
				CreditDocStatusEnum.海外_呈授管處, CreditDocStatusEnum.海外_呈營運中心, CreditDocStatusEnum.海外_呈總行));
		addAclLabel(model, new AclLabel("_btnWORK_LOOKING3", params, getDomainClass(), AuthType.Query,
				CreditDocStatusEnum.海外_呈授管處, CreditDocStatusEnum.海外_呈營運中心, CreditDocStatusEnum.海外_呈總行));

		addAclLabel(model, new AclLabel("_btnSEND_WAITLOGIN", params, getDomainClass(), AuthType.Modify,
				CreditDocStatusEnum.海外_提會待登錄, CreditDocStatusEnum.海外_總行提會待登錄, CreditDocStatusEnum.泰國_提會待登錄));
		addAclLabel(model, new AclLabel("_btnSEND_WAITLOGIN2", params, getDomainClass(), AuthType.Accept,
				CreditDocStatusEnum.海外_提會待登錄, CreditDocStatusEnum.海外_總行提會待登錄, CreditDocStatusEnum.泰國_提會待登錄));
		addAclLabel(model, new AclLabel("_btnSEND_WAITLOGIN3", params, getDomainClass(), AuthType.Query,
				CreditDocStatusEnum.海外_提會待登錄, CreditDocStatusEnum.海外_總行提會待登錄, CreditDocStatusEnum.泰國_提會待登錄));

		addAclLabel(model, new AclLabel("_btnSEND_WAITAPPROVE", params, getDomainClass(), AuthType.Modify,
				CreditDocStatusEnum.海外_提會待覆核, CreditDocStatusEnum.海外_總行提會待覆核, CreditDocStatusEnum.泰國_提會待覆核));
		addAclLabel(model, new AclLabel("_btnSEND_WAITAPPROVE2", params, getDomainClass(), AuthType.Accept,
				CreditDocStatusEnum.海外_提會待覆核, CreditDocStatusEnum.海外_總行提會待覆核, CreditDocStatusEnum.泰國_提會待覆核));
		addAclLabel(model, new AclLabel("_btnSEND_WAITAPPROVE3", params, getDomainClass(), AuthType.Query,
				CreditDocStatusEnum.海外_提會待覆核, CreditDocStatusEnum.海外_總行提會待覆核, CreditDocStatusEnum.泰國_提會待覆核));

		addAclLabel(model, new AclLabel("_btnWAIT_GETORBACK", params, getDomainClass(), AuthType.Modify,
				CreditDocStatusEnum.海外_待補件, CreditDocStatusEnum.海外_待撤件));
		addAclLabel(model, new AclLabel("_btnWAIT_GETORBACK2", params, getDomainClass(), AuthType.Accept,
				CreditDocStatusEnum.海外_待補件, CreditDocStatusEnum.海外_待撤件));
		addAclLabel(model, new AclLabel("_btnWAIT_GETORBACK3", params, getDomainClass(), AuthType.Query,
				CreditDocStatusEnum.海外_待補件, CreditDocStatusEnum.海外_待撤件));

		addAclLabel(model,
				new AclLabel("_btnALREADY_OK", params, getDomainClass(), AuthType.Modify, CreditDocStatusEnum.海外_已核准));
		addAclLabel(model,
				new AclLabel("_btnALREADY_OK2", params, getDomainClass(), AuthType.Accept, CreditDocStatusEnum.海外_已核准));
		addAclLabel(model,
				new AclLabel("_btnALREADY_OK3", params, getDomainClass(), AuthType.Query, CreditDocStatusEnum.海外_已核准));

		addAclLabel(model, new AclLabel("_btnALREADY_REJECT", params, getDomainClass(), AuthType.Modify,
				CreditDocStatusEnum.海外_婉卻));
		addAclLabel(model, new AclLabel("_btnALREADY_REJECT2", params, getDomainClass(), AuthType.Accept,
				CreditDocStatusEnum.海外_婉卻));
		addAclLabel(model, new AclLabel("_btnALREADY_REJECT3", params, getDomainClass(), AuthType.Query,
				CreditDocStatusEnum.海外_婉卻));

		addAclLabel(model, new AclLabel("_btnSAY_CASE", params, getDomainClass(), AuthType.Modify,
				CreditDocStatusEnum.海外_陳復案_陳述案));
		addAclLabel(model, new AclLabel("_btnSAY_CASE2", params, getDomainClass(), AuthType.Accept,
				CreditDocStatusEnum.海外_陳復案_陳述案));
		addAclLabel(model, new AclLabel("_btnSAY_CASE3", params, getDomainClass(), AuthType.Query,
				CreditDocStatusEnum.海外_陳復案_陳述案));

		// 授管處
		addAclLabel(model, new AclLabel("_btnDOC_HEDITING0", AuthType.Modify, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_HEDITING", params, getDomainClass(), AuthType.Modify,
				CreditDocStatusEnum.授管處_審查中));
		addAclLabel(model, new AclLabel("_btnDOC_HEDITING2", params, getDomainClass(), AuthType.Accept,
				CreditDocStatusEnum.授管處_審查中));
		addAclLabel(model, new AclLabel("_btnDOC_HEDITING3", params, getDomainClass(), AuthType.Query,
				CreditDocStatusEnum.授管處_審查中));

		addAclLabel(model, new AclLabel("_btnDOC_HALREADY", params, getDomainClass(), AuthType.Modify,
				CreditDocStatusEnum.授管處_已會簽));
		addAclLabel(model, new AclLabel("_btnDOC_HALREADY2", params, getDomainClass(), AuthType.Accept,
				CreditDocStatusEnum.授管處_已會簽));
		addAclLabel(model, new AclLabel("_btnDOC_HALREADY3", params, getDomainClass(), AuthType.Query,
				CreditDocStatusEnum.授管處_已會簽));

		addAclLabel(model, new AclLabel("_btnDOC_HSEND1", params, getDomainClass(), AuthType.Modify,
				CreditDocStatusEnum.授管處_提授審會));
		addAclLabel(model, new AclLabel("_btnDOC_HSEND1a", params, getDomainClass(), AuthType.Accept,
				CreditDocStatusEnum.授管處_提授審會));
		addAclLabel(model, new AclLabel("_btnDOC_HSEND1b", params, getDomainClass(), AuthType.Query,
				CreditDocStatusEnum.授管處_提授審會));

		addAclLabel(model, new AclLabel("_btnDOC_HSEND2", params, getDomainClass(), AuthType.Modify,
				CreditDocStatusEnum.授管處_提催收會));
		addAclLabel(model, new AclLabel("_btnDOC_HSEND2a", params, getDomainClass(), AuthType.Accept,
				CreditDocStatusEnum.授管處_提催收會));
		addAclLabel(model, new AclLabel("_btnDOC_HSEND2b", params, getDomainClass(), AuthType.Query,
				CreditDocStatusEnum.授管處_提催收會));

		addAclLabel(model, new AclLabel("_btnDOC_HSEND3", params, getDomainClass(), AuthType.Modify,
				CreditDocStatusEnum.授管處_提常董會));
		addAclLabel(model, new AclLabel("_btnDOC_HSEND3a", params, getDomainClass(), AuthType.Accept,
				CreditDocStatusEnum.授管處_提常董會));
		addAclLabel(model, new AclLabel("_btnDOC_HSEND3b", params, getDomainClass(), AuthType.Query,
				CreditDocStatusEnum.授管處_提常董會));

		addAclLabel(model, new AclLabel("_btnDOC_HWAITCHECK", params, getDomainClass(), AuthType.Modify,
				CreditDocStatusEnum.授管處_待放行, CreditDocStatusEnum.授管處_待核定));
		addAclLabel(model, new AclLabel("_btnDOC_HWAITCHECK2", params, getDomainClass(), AuthType.Accept,
				CreditDocStatusEnum.授管處_待放行, CreditDocStatusEnum.授管處_待核定));
		addAclLabel(model, new AclLabel("_btnDOC_HWAITCHECK3", params, getDomainClass(), AuthType.Query,
				CreditDocStatusEnum.授管處_待放行, CreditDocStatusEnum.授管處_待核定));

		addAclLabel(model, new AclLabel("_btnDOC_HWAITCHANGE", params, getDomainClass(), AuthType.Modify,
				CreditDocStatusEnum.授管處_待更正, CreditDocStatusEnum.授管處_待分行撤件));
		addAclLabel(model, new AclLabel("_btnDOC_HWAITCHANGE2", params, getDomainClass(), AuthType.Accept,
				CreditDocStatusEnum.授管處_待更正, CreditDocStatusEnum.授管處_待分行撤件));
		addAclLabel(model, new AclLabel("_btnDOC_HWAITCHANGE3", params, getDomainClass(), AuthType.Query,
				CreditDocStatusEnum.授管處_待更正, CreditDocStatusEnum.授管處_待分行撤件));

		addAclLabel(model, new AclLabel("_btnDOC_HALREADYOK", params, getDomainClass(), AuthType.Modify,
				CreditDocStatusEnum.授管處_已核准));
		addAclLabel(model, new AclLabel("_btnDOC_HALREADYOK2", params, getDomainClass(), AuthType.Accept,
				CreditDocStatusEnum.授管處_已核准));
		addAclLabel(model, new AclLabel("_btnDOC_HALREADYOK3", params, getDomainClass(), AuthType.Query,
				CreditDocStatusEnum.授管處_已核准));

		addAclLabel(model, new AclLabel("_btnDOC_HALREADYREJECT", params, getDomainClass(),
		 AuthType.Modify, CreditDocStatusEnum.授管處_已婉卻, CreditDocStatusEnum.授管處_待陳復));
		addAclLabel(model, new AclLabel("_btnDOC_HALREADYREJECT2", params, getDomainClass(),
		 AuthType.Accept, CreditDocStatusEnum.授管處_已婉卻, CreditDocStatusEnum.授管處_待陳復));
		addAclLabel(model, new AclLabel("_btnDOC_HALREADYREJECT3", params, getDomainClass(),
		 AuthType.Query, CreditDocStatusEnum.授管處_已婉卻, CreditDocStatusEnum.授管處_待陳復));

		// add(new AclLabel("_btnDOC_HNOLOOK", params, getDomainClass(),
		// AuthType.Modify, CreditDocStatusEnum.授管處_免批覆案件));

		// 營運中心
		addAclLabel(model, new AclLabel("_btnDOC_CEDITING", params, getDomainClass(), AuthType.Modify,
				CreditDocStatusEnum.營運中心_審查中));
		addAclLabel(model, new AclLabel("_btnDOC_CEDITING2", params, getDomainClass(), AuthType.Accept,
				CreditDocStatusEnum.營運中心_審查中));
		addAclLabel(model, new AclLabel("_btnDOC_CEDITING3", params, getDomainClass(), AuthType.Query,
				CreditDocStatusEnum.營運中心_審查中));

		addAclLabel(model, new AclLabel("_btnDOC_CWAITCHECK", params, getDomainClass(), AuthType.Modify,
				CreditDocStatusEnum.營運中心_待放行, CreditDocStatusEnum.營運中心_待核定));
		addAclLabel(model, new AclLabel("_btnDOC_CWAITCHECK2", params, getDomainClass(), AuthType.Accept,
				CreditDocStatusEnum.營運中心_待放行, CreditDocStatusEnum.營運中心_待核定));
		addAclLabel(model, new AclLabel("_btnDOC_CWAITCHECK3", params, getDomainClass(), AuthType.Query,
				CreditDocStatusEnum.營運中心_待放行, CreditDocStatusEnum.營運中心_待核定));

		addAclLabel(model, new AclLabel("_btnDOC_CTOADMIN", params, getDomainClass(), AuthType.Modify,
				CreditDocStatusEnum.營運中心_呈總處));
		addAclLabel(model, new AclLabel("_btnDOC_CTOADMIN2", params, getDomainClass(), AuthType.Accept,
				CreditDocStatusEnum.營運中心_呈總處));
		addAclLabel(model, new AclLabel("_btnDOC_CTOADMIN3", params, getDomainClass(), AuthType.Query,
				CreditDocStatusEnum.營運中心_呈總處));

		addAclLabel(model, new AclLabel("_btnDOC_CWAITCHANGE", params, getDomainClass(), AuthType.Modify,
				CreditDocStatusEnum.營運中心_待更正, CreditDocStatusEnum.營運中心_待分行撤件));
		addAclLabel(model, new AclLabel("_btnDOC_CWAITCHANGE2", params, getDomainClass(), AuthType.Accept,
				CreditDocStatusEnum.營運中心_待更正, CreditDocStatusEnum.營運中心_待分行撤件));
		addAclLabel(model, new AclLabel("_btnDOC_CWAITCHANGE3", params, getDomainClass(), AuthType.Query,
				CreditDocStatusEnum.營運中心_待更正, CreditDocStatusEnum.營運中心_待分行撤件));

		addAclLabel(model, new AclLabel("_btnDOC_CALREADYOK", params, getDomainClass(), AuthType.Modify,
				CreditDocStatusEnum.營運中心_已核准));
		addAclLabel(model, new AclLabel("_btnDOC_CALREADYOK2", params, getDomainClass(), AuthType.Accept,
				CreditDocStatusEnum.營運中心_已核准));
		addAclLabel(model, new AclLabel("_btnDOC_CALREADYOK3", params, getDomainClass(), AuthType.Query,
				CreditDocStatusEnum.營運中心_已核准));

		addAclLabel(model, new AclLabel("_btnDOC_CALREADYREJECT", params, getDomainClass(), AuthType.Modify,
				CreditDocStatusEnum.營運中心_已婉卻, CreditDocStatusEnum.營運中心_待陳復));
		addAclLabel(model, new AclLabel("_btnDOC_CALREADYREJECT2", params, getDomainClass(), AuthType.Accept,
				CreditDocStatusEnum.營運中心_已婉卻, CreditDocStatusEnum.營運中心_待陳復));
		addAclLabel(model, new AclLabel("_btnDOC_CALREADYREJECT3", params, getDomainClass(), AuthType.Query,
				CreditDocStatusEnum.營運中心_已婉卻, CreditDocStatusEnum.營運中心_待陳復));

		addAclLabel(model, new AclLabel("_btnDOC_CALLSEND", params, getDomainClass(), AuthType.Modify,
				CreditDocStatusEnum.營運中心_所有提會案件));
		addAclLabel(model, new AclLabel("_btnDOC_CALLSEND2", params, getDomainClass(), AuthType.Accept,
				CreditDocStatusEnum.營運中心_所有提會案件));
		addAclLabel(model, new AclLabel("_btnDOC_CALLSEND3", params, getDomainClass(), AuthType.Query,
				CreditDocStatusEnum.營運中心_所有提會案件));

		addAclLabel(model, new AclLabel("_btnDOC_CSAY_CASE", params, getDomainClass(),
		 AuthType.Modify, CreditDocStatusEnum.營運中心_陳復案_陳述案));
		addAclLabel(model, new AclLabel("_btnDOC_CSAY_CASE2", params, getDomainClass(),
		 AuthType.Accept, CreditDocStatusEnum.營運中心_陳復案_陳述案));
		addAclLabel(model, new AclLabel("_btnDOC_CSAY_CASE3", params, getDomainClass(),
		 AuthType.Query, CreditDocStatusEnum.營運中心_陳復案_陳述案));
	}

	private void hdefaultBtn(PageParameters params, ModelMap model) {
		// 海外
		// add(new AclLabel("_btnDOC_SEA_ALREADYSIGN3",AuthType.Query, params,
		// false));
		addAclLabel(model, new AclLabel("_btnDOC_EDITING", AuthType.Modify, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_EDITING2", AuthType.Accept, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_EDITING3", AuthType.Query, params, false));

		addAclLabel(model, new AclLabel("_btnWAIT_APPROVE", AuthType.Modify, params, false));
		addAclLabel(model, new AclLabel("_btnWAIT_APPROVE2", AuthType.Accept, params, false));
		addAclLabel(model, new AclLabel("_btnWAIT_APPROVE3", AuthType.Query, params, false));

		addAclLabel(model, new AclLabel("_btnWORK_LOOKING", AuthType.Modify, params, false));
		addAclLabel(model, new AclLabel("_btnWORK_LOOKING2", AuthType.Accept, params, false));
		addAclLabel(model, new AclLabel("_btnWORK_LOOKING3", AuthType.Query, params, false));

		addAclLabel(model, new AclLabel("_btnSEND_WAITLOGIN", AuthType.Modify, params, false));
		addAclLabel(model, new AclLabel("_btnSEND_WAITLOGIN2", AuthType.Accept, params, false));
		addAclLabel(model, new AclLabel("_btnSEND_WAITLOGIN3", AuthType.Query, params, false));

		addAclLabel(model, new AclLabel("_btnSEND_WAITAPPROVE", AuthType.Modify, params, false));
		addAclLabel(model, new AclLabel("_btnSEND_WAITAPPROVE2", AuthType.Accept, params, false));
		addAclLabel(model, new AclLabel("_btnSEND_WAITAPPROVE3", AuthType.Query, params, false));

		addAclLabel(model, new AclLabel("_btnWAIT_GETORBACK", AuthType.Modify, params, false));
		addAclLabel(model, new AclLabel("_btnWAIT_GETORBACK2", AuthType.Accept, params, false));
		addAclLabel(model, new AclLabel("_btnWAIT_GETORBACK3", AuthType.Query, params, false));

		addAclLabel(model, new AclLabel("_btnALREADY_OK", AuthType.Modify, params, false));
		addAclLabel(model, new AclLabel("_btnALREADY_OK2", AuthType.Accept, params, false));
		addAclLabel(model, new AclLabel("_btnALREADY_OK3", AuthType.Query, params, false));

		addAclLabel(model, new AclLabel("_btnALREADY_REJECT", AuthType.Modify, params, false));
		addAclLabel(model, new AclLabel("_btnALREADY_REJECT2", AuthType.Accept, params, false));
		addAclLabel(model, new AclLabel("_btnALREADY_REJECT3", AuthType.Query, params, false));

		addAclLabel(model, new AclLabel("_btnSAY_CASE", AuthType.Modify, params, false));
		addAclLabel(model, new AclLabel("_btnSAY_CASE2", AuthType.Accept, params, false));
		addAclLabel(model, new AclLabel("_btnSAY_CASE3", AuthType.Query, params, false));

		// 授管處
		addAclLabel(model, new AclLabel("_btnDOC_HEDITING0", AuthType.Modify, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_HEDITING", AuthType.Modify, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_HEDITING2", AuthType.Accept, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_HEDITING3", AuthType.Query, params, false));

		addAclLabel(model, new AclLabel("_btnDOC_HALREADY", AuthType.Modify, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_HALREADY2", AuthType.Accept, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_HALREADY3", AuthType.Query, params, false));

		addAclLabel(model, new AclLabel("_btnDOC_HSEND1", AuthType.Modify, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_HSEND1a", AuthType.Accept, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_HSEND1b", AuthType.Query, params, false));

		addAclLabel(model, new AclLabel("_btnDOC_HSEND2", AuthType.Modify, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_HSEND2a", AuthType.Accept, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_HSEND2b", AuthType.Query, params, false));

		addAclLabel(model, new AclLabel("_btnDOC_HSEND3", AuthType.Modify, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_HSEND3a", AuthType.Accept, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_HSEND3b", AuthType.Query, params, false));

		addAclLabel(model, new AclLabel("_btnDOC_HWAITCHECK", AuthType.Modify, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_HWAITCHECK2", AuthType.Accept, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_HWAITCHECK3", AuthType.Query, params, false));

		addAclLabel(model, new AclLabel("_btnDOC_HWAITCHANGE", AuthType.Modify, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_HWAITCHANGE2", AuthType.Accept, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_HWAITCHANGE3", AuthType.Query, params, false));

		addAclLabel(model, new AclLabel("_btnDOC_HALREADYOK", AuthType.Modify, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_HALREADYOK2", AuthType.Accept, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_HALREADYOK3", AuthType.Query, params, false));

		addAclLabel(model, new AclLabel("_btnDOC_HALREADYREJECT", AuthType.Modify, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_HALREADYREJECT2", AuthType.Accept, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_HALREADYREJECT3", AuthType.Query, params, false));

		// add(new AclLabel("_btnDOC_HNOLOOK", params, getDomainClass(),
		// AuthType.Modify, CreditDocStatusEnum.授管處_免批覆案件));

		// 營運中心
		addAclLabel(model, new AclLabel("_btnDOC_CEDITING", AuthType.Modify, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_CEDITING2", AuthType.Accept, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_CEDITING3", AuthType.Query, params, false));

		addAclLabel(model, new AclLabel("_btnDOC_CWAITCHECK", AuthType.Modify, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_CWAITCHECK2", AuthType.Accept, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_CWAITCHECK3", AuthType.Query, params, false));

		addAclLabel(model, new AclLabel("_btnDOC_CTOADMIN", AuthType.Modify, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_CTOADMIN2", AuthType.Accept, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_CTOADMIN3", AuthType.Query, params, false));

		addAclLabel(model, new AclLabel("_btnDOC_CWAITCHANGE", AuthType.Modify, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_CWAITCHANGE2", AuthType.Accept, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_CWAITCHANGE3", AuthType.Query, params, false));

		addAclLabel(model, new AclLabel("_btnDOC_CALREADYOK", AuthType.Modify, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_CALREADYOK2", AuthType.Accept, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_CALREADYOK3", AuthType.Query, params, false));

		addAclLabel(model, new AclLabel("_btnDOC_CALREADYREJECT", AuthType.Modify, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_CALREADYREJECT2", AuthType.Accept, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_CALREADYREJECT3", AuthType.Query, params, false));

		addAclLabel(model, new AclLabel("_btnDOC_CALLSEND", AuthType.Modify, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_CALLSEND2", AuthType.Accept, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_CALLSEND3", AuthType.Query, params, false));

		addAclLabel(model, new AclLabel("_btnDOC_CSAY_CASE", AuthType.Modify, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_CSAY_CASE2", AuthType.Accept, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_CSAY_CASE3", AuthType.Query, params, false));
	}

	// 頁籤
	@SuppressWarnings("unused")
	public Panel getPanel(int index, L120M01A l120m01a, ModelMap model) {
		Panel panel = null;
		Map<String, String> msgs = null;

		// J-109-0363_05097_B1001 Web e-Loan授信修改日本地區分行大阪分行授權外案件經由東京分行放行後送呈授信審查處
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		IBranch ibranch = branch.getBranch(user.getUnitNo());

		// UPGRADE: 待確認畫面是否抓得到此屬性
		Properties lms1205Pro = MessageBundleScriptCreator.getComponentResource(LMS1205M01Page.class);
		Properties eloanPro = MessageBundleScriptCreator.getComponentResource(AbstractEloanPage.class);
		String suffix = ("JP".equals(ibranch.getCountryType())) ? "_JP" : "";
		String queryBtnType = lms1205Pro.getProperty("l120m01a.queryButtonType4" + suffix);
		String l3m = eloanPro.getProperty("docStatus.L3M" + suffix);
		String l4m = eloanPro.getProperty("docStatus.L4M" + suffix);
		// 呈總行->呈管理行
		model.addAttribute("tmp_queryButtonType4", queryBtnType);
		// add(new Label("tmp_queryButtonType4",
		// MessageBundleScriptCreator.getComponentResource(
		// LMS1205M01Page.class)
		// .getProperty(
		// "l120m01a.queryButtonType4"
		// + (("JP".equals(ibranch
		// .getCountryType())) ? "_JP"
		// : ""))));
		// 提會待登錄->管理行待登錄
		model.addAttribute("tmp_docStatus.L3M", l3m);
		// add(new Label("tmp_docStatus.L3M",
		// MessageBundleScriptCreator.getComponentResource(
		// AbstractEloanPage.class)
		// .getProperty(
		// "docStatus.L3M"
		// + (("JP".equals(ibranch
		// .getCountryType())) ? "_JP"
		// : ""))));
		// 提會待覆核->管理行待覆核
		model.addAttribute("tmp_docStatus.L4M", l4m);
		// add(new Label("tmp_docStatus.L4M",
		// MessageBundleScriptCreator.getComponentResource(
		// AbstractEloanPage.class)
		// .getProperty(
		// "docStatus.L4M"
		// + (("JP".equals(ibranch
		// .getCountryType())) ? "_JP"
		// : ""))));

		switch (index) {
		case 1:
			panel = new LMSS01Panel(TAB_CTX, true);
			break;
		case 2:
			renderJsI18N(LMSS02Panel.class);
			// other.msg127=修改婉卻控管種類
			renderJsI18N(LMSCommomPage.class, new String[] { "other.msg127" });
			panel = new LMSS02Panel(TAB_CTX, true);
			break;
		case 3:
			msgs = lmsService.getAllDervPeriod();
			renderJsI18NWithMsgName("dervPeriodCodeType", msgs);
			// J-106-0085-001 Web e-Loan企金授信新增主要還款來源國等相關欄位
			msgs = lmsService.getCodeType("lms120_noFactCountry");
			renderJsI18NWithMsgName("lms120_noFactCountry", msgs);
			msgs = lmsService.getCodeType("lms120_freezeFactCountry");
			renderJsI18NWithMsgName("lms120_freezeFactCountry", msgs);
			//G-111-0168_05097_B1001 新增海外分(子)行「綠色授信」及「永續績效連結授信」等註記
			msgs = lmsService.getCodeType("lms140_esgGreenSpendType");
			renderJsI18NWithMsgName("lms140_esgGreenSpendType", msgs);  
			//G-111-0168_05097_B1001 新增海外分(子)行「綠色授信」及「永續績效連結授信」等註記
			msgs = lmsService.getCodeType("lms140_esgSustainLoanType");
			renderJsI18NWithMsgName("lms140_esgSustainLoanType", msgs);
			//J-113-0377 海外分(子)行企金授信新增社會責任授信
			msgs = lmsService.getCodeType("lms140_socialKind");
			renderJsI18NWithMsgName("lms140_socialKind", msgs);
			msgs = lmsService.getCodeType("lms140_socialTa");
			renderJsI18NWithMsgName("lms140_socialTa", msgs);
			msgs = lmsService.getCodeType("lms140_socialResp");
			renderJsI18NWithMsgName("lms140_socialResp", msgs);
			renderJsI18N(LMSS03Panel.class);
			renderJsI18N(LMS1405S01Panel.class);
			renderJsI18N(LMS1405S02Panel.class);
			//G-113-0145 授信新做額度於eloan簽報核准後，自動傳送AS400執行3X02，以利央行RDT報表傳送。
			msgs = lmsService.getCodeType("lms_140_loanAndContType");
			renderJsI18NWithMsgName("lms_140_loanAndContType", msgs);  
			renderJsI18N(LMS1405S02Panel04.class);
			// J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊
			renderJsI18N(LMS1405S04Panel.class);
			// J-108-0288_05097_B1001 Web e-Loan授信系統新增合併關係企業額度彙總表
			renderJsI18N(LMS1405S06Panel.class);
			renderJsI18N(LMS1405S07Panel.class);
			// J-110-0382 Web e-Loan國內與海外企金授信新增「BIS信用風險標準法/內評法」相關欄位
			renderJsI18N(LMS1405S08Panel.class);
			// J-111-0397 RWA
			renderJsI18N(LMS1405S09Panel.class);
			// J-111-0454 風控風險權數
			renderJsI18N(LMS1405S10Panel.class);
			// BIS
            renderJsI18N(LMS1405S11Panel.class);
			panel = new LMSS03Panel(TAB_CTX, true);
			break;
		case 4:
			renderJsI18N(LMSS04Panel.class);
			panel = new LMSS04Panel(TAB_CTX, true);
			break;
		case 5:
			// renderJsI18N(AbstractEloanPage.class, new String[]{"EFD0007"});
			renderJsI18N(LMSS07Panel.class);
			renderJsI18N(LMSS02Panel.class);
			renderJsI18N(LMS1205S05Panel.class);
			panel = new LMS1205S05Panel(TAB_CTX, true);
			break;
		case 6:
			renderJsI18N(LMS1205S06Panel.class);
			panel = new LMS1205S06Panel(TAB_CTX, true);
			break;
		case 7:
			// renderJsI18N(AbstractEloanPage.class, new String[]{"EFD0002"});
			// renderJsI18N(LMSM01Panel.class);
			renderJsI18N(LMSCommomPage.class, new String[] { "other.msg152" });
			renderJsI18N(LMSS07Panel.class);
			renderJsI18N(LMS1405S02Panel.class);
			panel = new LMSS07Panel(TAB_CTX, true);
			break;
		case 8:
			renderJsI18N(LMSS08Panel.class);
			renderJsI18N(AbstractEloanPage.class,
					new String[] { "docStatus.230" });
			// renderJsI18N(AbstractEloanPage.class, new
			// String[]{"EFD2056","EFD2039","EFD2047","EFD2057","EFD0038","docStatus.230"});
			renderRespMsgJsI18N(new String[] { "EFD2058", "EFD2059", "EFD2060" }); // 多render一個msgi18n
			panel = new LMSS08Panel(TAB_CTX, true);
			break;
		case 9:
			renderJsI18N(LMSS09Panel.class);
			panel = new LMSS09Panel(TAB_CTX, true);
			break;
		case 10:
			renderJsI18N(LMSS10Panel.class);
			panel = new LMSS10Panel(TAB_CTX, true);
			break;
		case 11:
			msgs = lmsService.getAllDervPeriod();
			renderJsI18NWithMsgName("dervPeriodCodeType", msgs);
			// J-106-0085-001 Web e-Loan企金授信新增主要還款來源國等相關欄位
			msgs = lmsService.getCodeType("lms120_noFactCountry");
			renderJsI18NWithMsgName("lms120_noFactCountry", msgs);
			msgs = lmsService.getCodeType("lms120_freezeFactCountry");
			renderJsI18NWithMsgName("lms120_freezeFactCountry", msgs);
			//G-111-0168_05097_B1001 新增海外分(子)行「綠色授信」及「永續績效連結授信」等註記
			msgs = lmsService.getCodeType("lms140_esgGreenSpendType");
			renderJsI18NWithMsgName("lms140_esgGreenSpendType", msgs);  
			//G-111-0168_05097_B1001 新增海外分(子)行「綠色授信」及「永續績效連結授信」等註記
			msgs = lmsService.getCodeType("lms140_esgSustainLoanType");
			renderJsI18NWithMsgName("lms140_esgSustainLoanType", msgs);
			//J-113-0329 企金授信新增社會責任授信
			msgs = lmsService.getCodeType("lms140_socialKind");
			renderJsI18NWithMsgName("lms140_socialKind", msgs);
			msgs = lmsService.getCodeType("lms140_socialTa");
			renderJsI18NWithMsgName("lms140_socialTa", msgs);
			msgs = lmsService.getCodeType("lms140_socialResp");
			renderJsI18NWithMsgName("lms140_socialResp", msgs);
			renderJsI18N(LMS1405S02Panel.class);
			renderJsI18N(LMS1405S02Panel04.class);
			renderJsI18N(LMSL140M01MPanel.class);
			panel = new LMS1205S11Panel(TAB_CTX, true, l120m01a);
			break;
		case 12:
			renderJsI18N(LMSS10Panel.class);
			panel = new LMS1205S12Panel(TAB_CTX, true);
			break;
		case 13:
			renderJsI18N(LMSS10Panel.class);
			panel = new LMS1205S13Panel(TAB_CTX, true);
			break;
		case 14:
			renderJsI18N(LMSS10Panel.class);
			panel = new LMS1205S14Panel(TAB_CTX, true);
			break;
		case 15:
			renderJsI18N(LMSS10Panel.class);
			panel = new LMS1205S15Panel(TAB_CTX, true);
			break;
		case 16:
			msgs = lmsService.getAllDervPeriod();
			renderJsI18NWithMsgName("dervPeriodCodeType", msgs);
			// J-106-0085-001 Web e-Loan企金授信新增主要還款來源國等相關欄位
			msgs = lmsService.getCodeType("lms120_noFactCountry");
			renderJsI18NWithMsgName("lms120_noFactCountry", msgs);
			msgs = lmsService.getCodeType("lms120_freezeFactCountry");
			renderJsI18NWithMsgName("lms120_freezeFactCountry", msgs);
			renderJsI18N(LMS1405S02Panel.class);
			renderJsI18N(LMS1405S02Panel04.class);
			panel = new LMS1205S16Panel(TAB_CTX, true, l120m01a);
			break;
		case 17:
			panel = new LMS1205S17Panel(TAB_CTX, true);
			renderJsI18N(LMS1205S17Panel.class);
			break;
		case 20:
			// J-106-0029-001 新增洗錢防制頁籤
			renderJsI18N(LMSS20APanel.class);
			panel = new LMSS20APanel(TAB_CTX, true);
			break;
		case 21:
			// J-106-0085-001 Web e-Loan企金授信新增主要還款來源國等相關欄位
			renderJsI18N(LMSS21APanel.class);
			panel = new LMSS21APanel(TAB_CTX, true);
			break;
		case 22:
			// J-106-0087-001 Web e-Loan 海外授信美國地區新增不符合授信政策案件資訊
			renderJsI18N(LMSS22APanel.class);
			panel = new LMSS22APanel(TAB_CTX, true);
			break;
		case 24:
			// J-106-0085-001 Web e-Loan企金授信新增主要還款來源國等相關欄位
			renderJsI18N(LMSS24APanel.class);
			panel = new LMSS24APanel(TAB_CTX, true);
			break;
		default:
			panel = new LMSS01Panel(TAB_CTX, true);
			break;
		}
		if (panel == null) {
			panel = new Panel(TAB_CTX, true);
		}

		return panel;
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return L120M01A.class;
	}

}// ~
