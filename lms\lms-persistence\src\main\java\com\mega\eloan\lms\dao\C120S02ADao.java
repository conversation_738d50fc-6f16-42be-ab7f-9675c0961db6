package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C120S02A;

/** 簽報書聯徵查詢結果 **/
public interface C120S02ADao extends IGenericDao<C120S02A> {

	C120S02A findByOid(String oid);

    List<C120S02A> findByList(String mainId, String custId, String dupNo);

    C120S02A findByItem(String mainId, String custId, String dupNo, String item);

	List<C120S02A> findByCustIdDupId(String custId, String DupNo);

	List<C120S02A> findByMainId(String mainId);

	public int deleteByOid(String oid);
}