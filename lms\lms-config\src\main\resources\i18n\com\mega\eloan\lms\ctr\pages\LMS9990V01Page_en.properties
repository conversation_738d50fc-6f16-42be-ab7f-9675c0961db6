#==================================================
# \u4f01\u91d1\u7d04\u64da\u66f8 Grid
#==================================================
L120M01A.caseDate=Report Date
L120M01A.caseNo=Case Report No.
L120M01A.approveTime=Approval date
L120M01A.custName=Principal Borrower
L120M01A.custId=Principal Bo<PERSON>er's UBN
L120M01A.dupNo=Repeat Serial No.

#==================================================
# \u4f01\u91d1\u7d04\u64da\u66f8 title
#==================================================
L999M01A.showTitle01=Principal Borrower

#==================================================
# \u4f01\u91d1\u7d04\u64da\u66f8 \u67e5\u8a62\u756b\u9762
#==================================================
ThickBox.showTitle=Loan Agreement Inquiry
ThickBox.showDupNoTitle=Repeated Serials Inquiry
ThickBox.title=Please input records to inquire
L999M01A.mainCustId=Principal Borrower's UBN
L999M01A.mainDupNo=Repeat Serial No.
L999M01A.typCd=DBU/OBU
L999M01A.obu=DBU
L999M01A.dbu=OBU


#==================================================
# \u4f01\u91d1\u7d04\u64da\u66f8 \u932f\u8aa4\u8a0a\u606f
#==================================================
Alert.message01=[Attention] Please input Principal Borrower's UBN
Alert.message02=[Attention] Error in Principal Borrower's UBN length
Alert.message03=[Attention] Error in the length of repeated serial No.
Alert.message04=[Attention] Principal Borrower not found
Alert.message05=[Attention] Error in the retrieve of random no.
