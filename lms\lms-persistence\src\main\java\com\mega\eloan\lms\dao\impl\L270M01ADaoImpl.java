/* 
 * L270M01ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L270M01ADao;
import com.mega.eloan.lms.model.L270M01A;

/** 企金信保央行C申請書 **/
@Repository
public class L270M01ADaoImpl extends LMSJpaDao<L270M01A, String>
	implements L270M01ADao {

	@Override
	public L270M01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public L270M01A findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		return findUniqueOrNone(search);
	}
			
	@Override
	public List<L270M01A> findByDocStatus(String docStatus){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "docStatus", docStatus);
		List<L270M01A> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public L270M01A findByUniqueKey(String approveNo){
		ISearch search = createSearchTemplete();
		if (approveNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "approveNo", approveNo);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<L270M01A> findByIndex01(String mainId){
		ISearch search = createSearchTemplete();
		List<L270M01A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L270M01A> findByIndex02(String ownBrId, String custId, String dupNo){
		ISearch search = createSearchTemplete();
		List<L270M01A> list = null;
		if (ownBrId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", ownBrId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
	
	@Override
	public List<L270M01A> findByApprNoCustId(String approveNo,String custId){
		ISearch search = createSearchTemplete();
		List<L270M01A> list = null;
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (approveNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "approveNo", approveNo);
        search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		search.setMaxResults(Integer.MAX_VALUE);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
}