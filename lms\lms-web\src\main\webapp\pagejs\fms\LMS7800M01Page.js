
var initDfd = $.Deferred(), inits = {
    fhandle: "lms7800m01formhandler",
    ghandle: "lms7800gridhandler"
};

//select source
var result_s = CommonAPI.loadCombos(["lms7800_resultA","lms7800_resultB","lms7800_resultC"]);

var Action = {
	_isLoad: false,
	initUser : function(){
	    $.ajax({
	        handler: "codetypehandler",
	        action: "userByBranch",
	        async: false,
	        success: function(json){
	        	$("#infoAppraiser").setItems({
	    	        item: json,
	    	        format: "{value} - {key}"
	    	    });
	        }
	    });
	},
	_getItem: function(){
		$.ajax({
			handler:"lms7800m01formhandler", 
			action:'querySynBankOfCsTypes',	//'querySynBank',
			async: false,
			success:function(json){
				$("[name='select_A']").setItems({
			        item: json.select_AList,
			        format: "{key}"
			    });
			    $("[name='select_B']").setItems({
			        item: json.select_BList,
			        format: "{key}"
			    });
			    $("[name='select_B']").append($('<option>', {
				    value: 'X11',
				    text: '財務處'
				}));
			    $("[name='select_C']").setItems({
			        item: json.select_CList,
			        format: "{key}"
			    }); 
			}
		});
	},
	_initItem: function(){
	    $("[name='result_A']").setItems({
	        item: result_s.lms7800_resultA,
	        format: "{key}"
	    });
	    $("[name='result_B']").setItems({
	        item: result_s.lms7800_resultB,
	        format: "{key}"
	    });
	    $("[name='result_C']").setItems({
	        item: result_s.lms7800_resultC,
	        format: "{key}"
	    });
	},
	_initEvent: function(){
		$("[name^='checkYN_']").change(function(){
			var type = DOMPurify.sanitize($(this).attr('class'));
			var value = $(this).val();
			if(value == "Y"){
				$("#result_"+type).val('').show();
				$("#select_"+type).val('').hide();
				$("#memo_"+type).val('').hide();
				$("#mtitle_"+type).text('');
			} else {
				$("#result_"+type).val('').hide();
				$("#select_"+type).val('').hide();
				$("#memo_"+type).val('').hide();
				$("#mtitle_"+type).text('');
				if(type != "A"){
					$("#memo_"+type).show();
					$("#mtitle_"+type).text(i18n.lms7800m01['reason']);
				}
			}
		});
		$("[name^='result_']").change(function(){
			var value = $(this).val();
			var name = DOMPurify.sanitize($(this).attr('name'));
			if(name.length >= 1){
				name = name.split("_").pop();//name.substring(name.length-1);
			}
			if (name == "A"){
				if (value == "A1") {
	                $("#select_A").val('').show();
	                $("#memo_A").val('').show();
	                $("#mtitle_A").text(i18n.lms7800m01['person']);
	            } else {
	            	$("#select_A").val('').hide();
	            	$("#memo_A").val('').hide();
	            	$("#mtitle_A").text('');
				}
			} else if (name == "B" || name == "C"){
				if (value == "B1" || value == "C1") {
	                $("#select_"+name).val('').show();
	                $("#memo_"+name).val('').show();
	                $("#mtitle_"+name).text(i18n.lms7800m01['person']);
	            } else if(value == "B2" || value == "C2") {
	            	$("#select_"+name).val('').hide();
	            	$("#memo_"+name).val('').hide();
	            	$("#mtitle_"+name).text('');
	            } else if(value == "B3" || value == "C3") {
	            	$("#select_"+name).val('').hide();
	            	$("#memo_"+name).val('').show();
	            	$("#mtitle_"+name).text(i18n.lms7800m01['reason']);
				}
			}
		});
	},
	_initForm: function(){
		$.form.init({
			formHandler:"lms7800m01formhandler", 
			formAction:'queryL140mm6a',	//'query',
			loadSuccess:function(json){
				$('body').injectData(json);
				
				$("[name=checkYN_A][value=" + json.checkYN_A + "]").attr("checked", true);
				$("[name=checkYN_B][value=" + json.checkYN_B + "]").attr("checked", true);
				$("[name=checkYN_C][value=" + json.checkYN_C + "]").attr("checked", true);
				
				if(json.A == ""){	//無資料
					$("#div_A").hide();
					$("#none_A").show();
				} else {
					$("#div_A").show();
					if(json.checkYN_A == "N"){
						
					} else {
						$("#result_A").show();
						if(json.result_A == "A1"){ //窗口
							$("#select_A").show();
							$("#memo_A").show();
							$("#mtitle_A").text(i18n.lms7800m01['person']);
						} else if(json.result_A == "A2"){
							
						} else if(json.result_A == "A3"){
							
						}
					}
				}
				
				if(json.B == ""){	//無資料
					$("#div_B").hide();
					$("#none_B").show();
				} else {
					$("#div_B").show();
					$("#memo_B").show();
					if(json.checkYN_B == "N"){
						$("#mtitle_B").text(i18n.lms7800m01['reason']);
					} else {
						$("#result_B").show();
						if(json.result_B == "B1"){ //窗口
							$("#select_B").show();
							$("#mtitle_B").text(i18n.lms7800m01['person']);
						} else if(json.result_B == "B2"){
							$("#memo_B").hide();
						} else if(json.result_B == "B3"){
							$("#mtitle_B").text(i18n.lms7800m01['reason']);
						}
					}
				}
				
				if(json.C == ""){	//無資料
					$("#div_C").hide();
					$("#none_C").show();
				} else {
					$("#div_C").show();
					$("#memo_C").show();
					if(json.checkYN_C == "N"){
						$("#mtitle_C").text(i18n.lms7800m01['reason']);
					} else {
						$("#result_C").show();
						if(json.result_C == "C1"){ //窗口
							$("#select_C").show();
							$("#mtitle_C").text(i18n.lms7800m01['person']);
						} else if(json.result_C == "C2"){
							$("#memo_C").hide();
						} else if(json.result_C == "C3"){
							$("#mtitle_C").text(i18n.lms7800m01['reason']);
						}
					}
				}

				Action._initEvent();
			}
		});
	},
	_init: function(){
        if (!this._isLoad) {
        	this.initUser();
        	this._getItem();
            this._initItem();
            this._initForm();
            this._isLoad = true;            
        } else {
            this._reloadGrid();
        }
    }
}

// 驗證readOnly狀態
function checkReadonly(){
    var auth = (responseJSON ? responseJSON.Auth : {}); // 權限
    if (auth.readOnly || responseJSON.mainDocStatus != "01O") {
        return true;
    }
    return false;
}

$(document).ready(function(){
	var tabForm = $("#mainPanel");
	Action._init();
	$("#check1").show();

    if (checkReadonly()) {
        $(".readOnlyhide").hide();
        $("form").lockDoc();
		_openerLockDoc="1";
    }

	// 呈主管覆核 選授信主管人數
    $("#numPerson").change(function(){
        $('#bossItem').empty();
        var value = $(this).val();
        if (value) {
            var html = '';
            for (var i = 1; i <= value; i++) {
                var name = 'boss' + i;
                html += i + '. '
                // || '授信主管'
                html += '<select id="' + name + '" name="boss"' +
                '" class="required" CommonManager="kind:2;type:2" />';
                html += '<br/>';
            }
            $('#bossItem').append(html).find('select').each(function(){
                $(this).setItems({
                    item: item,
                    format: "{value} {key}"
                });
            });
        }     
    });
	
	var btn = $("#buttonPanel");
    btn.find("#btnSave").click(function(showMsg){
        saveData(true);
    }).end().find("#btnSend").click(function(){
        saveData(false, sendBoss);	
    }).end().find("#btnCheck").click(function(){
        openCheck();
    }).end().find("#btnPrint").click(function(){
        if (checkReadonly()) {
            printAction();
        }
        else {
            // saveBeforePrint=執行列印將自動儲存資料，是否繼續此動作?
            CommonAPI.confirmMessage(i18n.def["saveBeforePrint"], function(b){
                if (b) {
                    saveData(false, printAction);
                }
            });
        }
    });
	
	// 儲存的動作
    function saveData(showMsg, tofn){
		
		// 為檢查UI的值是否皆無異常
		if ($("#mainPanel").valid() == false) {
			return;
		}

		var checkInput = false;
		$("#mainPanel").find('select:enabled:visible, input:enabled:visible').each(function(v, k){
			var value = $.trim($(this).val());
			if(value.length == 0){
				checkInput = true;
			}
		});

		if(checkInput){
			return CommonAPI.showErrorMessage(i18n.lms7800m01['dataMiss']);
		}
		
		FormAction.open = true;
		$.ajax({
			handler: inits.fhandle,
			data: {// 把資料轉成json
				formAction: "saveL140mm6a",
				oid: responseJSON.oid,
				page: responseJSON.page,
				txCode: responseJSON.txCode,
				showMsg: showMsg
			},
			success: function(obj){
				if (responseJSON.page == "01") {
					$('body').injectData(obj);
				}
				
				Action._initForm();
				
				CommonAPI.triggerOpener("gridview", "reloadGrid");
				if ($("#mainOid").val()) {
					setRequiredSave(false);
				}
				else {
					setRequiredSave(true);
				}
				
				// 執行列印
				if (!showMsg && tofn) {
					tofn();
				}
			}
		});
    }
	
	var item;
	// 呈主管 - 編製中
    function sendBoss(){
        $.ajax({
            handler: inits.fhandle,
            action: "checkData",
            data: {},
            success: function(json){
                $('#managerItem').empty();
                $('#bossItem').empty();
                item = json.bossList;
                var bhtml = '1. <select id="boss1" name="boss" class="required" CommonManager="kind:2;type:2"/>';
                $('#bossItem').append(bhtml).find('select').each(function(){
                    $(this).setItems({
                        item: item,
                        format: "{value} {key}"
                    });
                });
                var html = '<select id="manager" name="manager" class="required" CommonManager="kind:2;type:2" />';
                $('#managerItem').append(html).find('select').each(function(){
                    $(this).setItems({
                        item: item,
                        format: "{value} {key}"
                    });
                });
                
                // 是否呈主管覆核？
                CommonAPI.confirmMessage(i18n.lms7800m01["L140MM6B.message01"], function(b){
                    if (b) {
                        $("#selectBossBox").thickbox({
                            // 覆核
                            title: i18n.lms7800m01['approve'],
                            width: 500,
                            height: 300,
                            modal: true,
                            readOnly: false,
                            valign: "bottom",
                            align: "center",
                            i18n: i18n.def,
                            buttons: {
                                "sure": function(){
                                
                                    var selectBoss = $("select[name^=boss]").map(function(){
                                        return $(this).val();
                                    }).toArray();
                                    
                                    for (var i in selectBoss) {
                                        if (selectBoss[i] == "") {
                                            // 請選擇授信主管
                                            return CommonAPI.showErrorMessage(i18n.lms7800m01['checkSelect'] +
                                            i18n.lms7800m01['L140MM6B.bossId']);
                                        }
                                    }
                                    if ($("#manager").val() == "") {
                                        // 請選擇經副襄理
                                        return CommonAPI.showErrorMessage(i18n.lms7800m01['checkSelect'] +
                                        i18n.lms7800m01['L140MM6B.managerId']);
                                    }
                                    // 驗證是否有重複的主管
                                    if (checkArrayRepeat(selectBoss)) {
                                        // 主管人員名單重複請重新選擇
                                        return CommonAPI.showErrorMessage(i18n.lms7800m01['L140MM6B.message02']);
                                    }
                                    
                                    flowAction({
                                        page: responseJSON.page,
                                        saveData: true,
                                        selectBoss: selectBoss,
                                        manager: $("#manager").val()
                                    });
                                    $.thickbox.close();
                                    
                                },
                                
                                "cancel": function(){
                                    $.thickbox.close();
                                }
                            }
                        });
                    }
                });
            }
        });
    }
	
	// 待覆核 - 覆核
    function openCheck(){
        $("#openCheckBox").thickbox({ // 使用選取的內容進行彈窗
            title: i18n.lms7800m01['approve'],
            width: 100,
            height: 100,
            modal: true,
            readOnly: false,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var val = $("[name=checkRadio]:checked").val();
                    if (!val) {
                        return CommonAPI.showMessage(i18n.lms7800m01['checkSelect']);
                    }
                    $.thickbox.close();
                    switch (val) {
                        case "1":
                            // 一般退回到編製中01O
                            // 該案件是否退回經辦修改？要退回請按【確定】，不退回請按【取消】
                            CommonAPI.confirmMessage(i18n.lms7800m01['L140MM6B.message03'], function(b){
                                if (b) {
                                    flowAction({
                                        flowAction: false
                                    });
                                }
                            }); 
                            break;
                        case "3":
                            // 該案件是否確定執行核定作業
                            CommonAPI.confirmMessage(i18n.lms7800m01['L140MM6B.message04'], function(b){
                                if (b) {
				                    flowAction({
				                        flowAction: true,
				                        checkDate: CommonAPI.getToday()//forCheckDate
				                    });
                                }
                            });
                            break;
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
	
	function flowAction(sendData){
        $.ajax({
            handler: inits.fhandle,
            data: $.extend({
                formAction: "flowAction",
                mainOid: $("#mainOid").val()
            }, (sendData || {})),
            success: function(){
                CommonAPI.triggerOpener("gridview", "reloadGrid");
				window.close();
            }
        });
    }

	// 列印動作
    function printAction(){
        $.form.submit({
            url: "../../simple/FileProcessingService",
            target: "_blank",
            data: {
                mainId: responseJSON.mainId,
                mainOid: responseJSON.oid,
                fileDownloadName: "lms7800r01.pdf",
                serviceName: "lms7800r01rptservice"
            }
        });
    }
	
	// 檢查陣列內容是否重複
    function checkArrayRepeat(arrVal){
        var newArray = [];
        for (var i = arrVal.length; i--;) {
            var val = arrVal[i];
            if ($.inArray(val, newArray) == -1) {
                newArray.push(val);
            }
            else {
                return true;
            }
        }
        return false;
    }
});