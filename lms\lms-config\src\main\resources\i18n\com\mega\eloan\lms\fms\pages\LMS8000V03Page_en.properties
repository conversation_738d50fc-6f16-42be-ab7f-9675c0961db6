#==================================================
#  Grid
#==================================================
#Z
L260M01A.custId=\u5ba2\u6236\u7d71\u4e00\u7de8\u865f
L260M01A.custName=Customer Name
#L260M01A.followDate=\u8ffd\u8e64\u65e5
L260M01A.cntrNo=Credit limit No.
L260M01A.loanNo=Loan account
L260M01A.loanNoOvs=REF No.
L260M01A.followKind=Category
L260M01A.updater=\u7d93\u8fa6
L260M01A.createTime=\u5efa\u7acb\u65e5\u671f
#L260M01A.dataFrom=\u4f86\u6e90
L260M01A.approver=\u8986\u6838
L260M01A.approveTime=\u6838\u51c6\u65e5\u671f
L260M01A.error01=\u6b64\u529f\u80fd\u4e0d\u80fd\u591a\u9078
L260M01A.error02=\u4e0d\u53ef\u522a\u9664\u4f86\u6e90\u70ba\u7cfb\u7d71\u7522\u751f\u4e4b\u540d\u55ae
L260M01A.error03=\u7121\u76f8\u95dc\u984d\u5ea6\u5e8f\u865f
L260M01A.followUpType=Follow-up type
L260M01A.followUpProject=Follow-up project
L260M01A.followUpRecords=Follow-up records
L260M01A.custIdMsg=If there are multiple entries, please separate them with a comma.
L260M01A.followUpMsg=Select up to 4 types
checkSelect=Please select.

#==================================================
#  Filter
#==================================================
LMS8000V01.title=\u8acb\u8f38\u5165\u6b32\u67e5\u8a62\u9805\u76ee\uff1a

#==================================================
#  New Case
#==================================================
LMS8000V01.newCase=\u8d77\u6848\u65b9\u5f0f