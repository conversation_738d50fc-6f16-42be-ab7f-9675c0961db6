initDfd.done(function(json){


    var wrapFormId = "#formL1140m01r";
    var wrapForm = "<form id='formL1140m01r'></form>";
    
    $("#delL140M01RBt").click(function(){
        var selrow = grid.getGridParam('selrow');
        if (selrow) {
            var ret = grid.getRowData(selrow);
            API.flowConfirmAction({
                message: i18n.def["action_003"],
                handler: "cls1141m01formhandler",
                action: "deleteL140M01R",
                data: {
                    deleteMainOid: ret.oid,
                    deleteMainId: ret.mainId
                },
                success: function(){
                    CommonAPI.showErrorMessage(i18n.def["confirmDeleteSuccess"]);
                    grid.trigger("reloadGrid");                    
                }
            });
        }
        else {
            CommonAPI.showErrorMessage(i18n.def["action_002"]);
        }
    });
    
    function prompL140M01R_WmCust(){
    	var my_dfd = $.Deferred();
    	$.ajax({
            handler: "cls1141m01formhandler",
            data: $.extend($("#charge").serializeData(), {
                formAction: "prompL140M01R_WmCust"
            }),
            success: function(resp){				
               if(resp.cmfMsg){
            	   var msg_sure = i18n.def.sure;
            	   var msg_cancel = i18n.def.cancel;
            	   i18n.def.sure = "繼續執行收取";
            	   i18n.def.cancel = "取消收取";
            	   CommonAPI.confirmMessage(resp.cmfMsg, function(b){
       	            	if (b) {
       	            		my_dfd.resolve();
       	            	}else{
       	            		//關掉 parent 的 thickbox
       	            		//避免 event 錯亂  $.thickbox.close();
       	            	}
       	            	i18n.def.sure = msg_sure;
                 	    i18n.def.cancel = msg_cancel;
       	        	});
               }else{
            	   my_dfd.resolve();
               }               
            }
        });
    	return my_dfd.promise(); 
    }
    
    $("#addL140M01RBt").click(function(){
        $("#fee").thickbox({
            title: i18n.cls1141s05['L140M01R.006'],//'新增費用',
            width: 740,
            height: 300,
            align: 'left',
            valign: 'top',
			modal : false,
            modal: false,
            open: function(){
				$("#formL1140m01r").reset();
                $("#charge").wrap(wrapForm);
                $(wrapFormId).reset();
				$("#feeSwft").val("TWD");
            },            
            buttons: API.createJSON([{
                key: i18n.def['sure'],
                value: function(){
                    if ($(wrapFormId).valid()) {
                    	prompL140M01R_WmCust().done(function(){
                	    
                        $.ajax({
                            handler: "cls1141m01formhandler",
                            data: $.extend($("#charge").serializeData(), {
                                formAction: "addL140M01R"
                            }),
                            success: function(responseData){
								$("#charge").unwrap(wrapForm);
                                $.thickbox.close();
                                grid.trigger("reloadGrid");
                               if(responseData.promptMsg){
                            	   API.showMessage(responseData.promptMsg);
                               }
                            }
                        });
                    	}); //-- end prompL140M01R_WmCust()
                    }
                }
            }, {
                key: i18n.def['cancel'],
                value: function(){
					$("#charge").unwrap(wrapForm);
                    $.thickbox.close();
                }
            }])
        });
    });
    
    var grid = $("#L140M01RGrid").iGrid({
        height: 90,
        width: 200,
        autowidth: true,
        handler: "cls1141gridhandler",
        action: "queryL140M01R",
        rownumbers: true,
        colModel: [{
            colHeader: i18n.cls1141s05['L140M01R.002'],//"費用代碼",
            name: "feeNo",
            align: "center",
            formatter: 'click',
            onclick: json.editable == "N" ? openDocNoEdit : openDoc
//        }, {
//            colHeader: i18n.cls1141s05['L140M01R.003'],//"收取範圍",
//            name: "feeSphere",
//            align: "center"
        }, {
            colHeader: i18n.cls1141s05['L140M01R.015'],//"幣別",
            width: 20, // 設定寬度
            name: "feeSwft",
            align: "center"
        }, {
            colHeader: i18n.cls1141s05['L140M01R.004'],//"費用金額",
            name: "feeAmt",
            align: "right",
            formatter: 'currency',
            formatoptions: {
                decimalSeparator: ",",
                thousandsSeparator: ",",
                decimalPlaces: 0,
                defaultValue: ""
            }
//        },  {
//            colHeader: i18n.cls1141s05['L140M01R.009'],//"客戶編號",
//            name: "custId",
//            align: "center"
//        }, {
//            colHeader: i18n.cls1141s05['L140M01R.010'],//"額度序號",
//            name: "cntrno",
//            align: "center"
//        }, {
//            colHeader: i18n.cls1141s05['L140M01R.011'],//"帳號",
//            name: "loanNo",
//            align: "center"
        }, {
            colHeader: i18n.cls1141s05['L140M01R.005'],//"備註",
            name: "feeMemo",
            align: "center"
        }, {
            name: "oid",
            hidden: true
        }, {
            name: "mainId",
            hidden: true
        }],
        ondblClickRow: function(rowid){
            var data = grid.getRowData(rowid);
            json.editable == "N" ? openDocNoEdit(null, null, data) : openDoc(null, null, data);
        }
    });
    
    var gridC120M01A = $("#gridC120M01A").iGrid({
        height: 70,
        width: 80,
        autowidth: true,
        handler: "cls1141gridhandler",
        action: "queryC120M01A",
        rownumbers: true,
        colModel: [{
            colHeader: i18n.cls1141s05['L140M01R.009'],//'客戶編號',
            name: "custId",
            align: "center"
        }, {
            colHeader: i18n.cls1141s05['L140M01R.014'],//'客戶姓名',
            name: "custName",
            align: "center"
        }]
    
    });
    
    var gridL140M01A = $("#gridL140M01A").iGrid({
        height: 150,
        width: 80,
        autowidth: true,
        handler: "cls1141gridhandler",
        action: "queryL140M01A",
        rownumbers: true,
        colModel: [{
            colHeader: i18n.cls1141s05['L140M01R.009'],//'客戶編號',
            name: "custId",
            align: "center"
        }, {
            colHeader: i18n.cls1141s05['L140M01R.014'],//'客戶姓名',
            name: "custName",
            align: "center"
        }, {
            colHeader: i18n.cls1141s05['L140M01R.010'],//'額度序號',
            name: "cntrNo",
            align: "center"
        }]
    
    });
    
    function openDoc(cellvalue, options, rowObject){
        $("#fee").thickbox({
            title: i18n.cls1141s05['L140M01R.007'],//'修改費用',
            width: 600,
            height: 300,
            align: 'left',
            valign: 'top',
            open: function(){
                $("#charge").wrap(wrapForm);
                $(wrapFormId).reset();
                $.ajax({
                    handler: "cls1141m01formhandler",
                    data: {
                        l140m01roid: rowObject.oid,
                        formAction: "getL140M01R"
                    },
                    success: function(responseData){
                        $('#charge').injectData(responseData);
                    }
                });
            },         
            buttons: API.createJSON([{
                key: i18n.def['sure'],
                value: function(){
                    if ($(wrapFormId).valid()) {
                        $.ajax({
                            handler: "cls1141m01formhandler",
                            data: $.extend($("#charge").serializeData(), {
                                formAction: "updateL140M01R"
                            }),
                            success: function(responseData){
								$("#charge").unwrap(wrapForm);
                                $.thickbox.close();
                                grid.trigger("reloadGrid");
                            }
                        });
                    }
                }
            }, {
                key: i18n.def['cancel'],
                value: function(){
					$("#charge").unwrap(wrapForm);
                    $.thickbox.close();
                }
            }])
        });
    }
    
    function openDocNoEdit(cellvalue, options, rowObject){
        $("#fee").thickbox({
            title: i18n.cls1141s05['L140M01R.008'],//'檢視費用',
            width: 640,
            height: 500,
            align: 'left',
            valign: 'top',
            open: function(){
                $("#charge").wrap(wrapForm);
                $(wrapFormId).reset();
                $.ajax({
                    handler: "cls1141m01formhandler",
                    data: {
                        l140m01roid: rowObject.oid,
                        formAction: "getL140M01R"
                    },
                    success: function(responseData){
                        $('#charge').injectData(responseData);
                    }
                });
            },
            close: function(){
                $("#charge").unwrap(wrapForm);
            },
            buttons: API.createJSON([{
                key: i18n.def['sure'],
                value: function(){
                    $.thickbox.close();
                }
            }])
        });
    }
    
    $("input[type='radio'][name='feeSphere']").change(function(){
        var value = $(this).filter(":checked").val();
        $(".clearFee").val("");
        
        $("input[type='text'][class*='group'],button[class*='group'],span[class*='group']").hide();
        alert(DOMPurify.sanitize(value));
        $("input[type='text'][class*='group" + DOMPurify.sanitize(value) + "'],button[class*='group" + DOMPurify.sanitize(value) + "'],span[class*='group" + DOMPurify.sanitize(value) + "']").show();
    });
    
    $("input[type='radio'][name='feeSphere']:checked").trigger("change");
    
    $("#includeC120M01A").click(function(){
    
        $('#C120M01AThickBox').thickbox({
            title: i18n.cls1141s05['L140M01R.012'],//'本簽報書>借款人基本資料中借保人',
            width: 400,
            height: 300,
            align: 'center',
            valign: 'bottom',
            i18n: i18n.def,
            buttons: {
                'sure': function(){
                    var data = gridC120M01A.getSingleData();
                    var data1 = data.custId.substring(0, 11);
                    var data2 = data.custId.substring(11, 12);
                    $("#custId").val(data1);
                    $("#dupNo").val(data2);
                    $.thickbox.close();
                },
                'close': function(){
                    $.thickbox.close();
                }
            }
        });
    });
    
    $("#includeL140M01A").click(function(){
    
        $('#L140M01AThickBox').thickbox({
            title: i18n.cls1141s05['L140M01R.013'],//'本簽報書>額度明細表中額度序號',
            width: 800,
            height: 400,
            align: 'center',
            valign: 'bottom',
            i18n: i18n.def,
            buttons: {
                'sure': function(){
                    var data = gridL140M01A.getSingleData();
                    $("#cntrno").val(data.cntrNo);                    
//                    alert(data.cntrNo);                    
                    $.thickbox.close();
                },
                'close': function(){
                    $.thickbox.close();
                }
            }
        });
    });
    
    
});
