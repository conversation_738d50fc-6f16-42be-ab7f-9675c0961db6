<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:th="http://www.thymeleaf.org">
<body>
	<th:block th:fragment="panelFragmentBody">
		<form id="C160S01FForm" name="C160S01FForm">
			<table class="tb2" width="100%">
				<tr>
					<td width="20%" class="hd2" align="right"><span class="text-red">＊</span><th:block th:text="#{'C160S01F.bankNo'}">轉出之原金融機構代碼(如選不到行庫者，可自行登打...)</th:block></td>
					<td colspan="3" >
						<input type="text" id="branchNo" name="branchNo" size="5" maxlength="7" class="required alphanum"/>
						<input type="text" id="branchName" name="branchName"  maxlength="60" maxlengthC="20" size="30" class="required" />&nbsp;
						<button type="button" id="bankNoLogin" >
							<span class="text-only"><th:block th:text="#{'button.login'}">登錄</th:block></span>
						</button>
						<input type="hidden" id="bankNo" name="bankNo" />
						<input type="hidden" id="bankName" name="bankName" />&nbsp;
					</td>
				</tr>
				<tr>
					<td width="20%" class="hd2" align="right"><span class='subACNoTr'><th:block th:text="#{'C160S01F.subACNo'}">代償本行帳號</th:block></span>&nbsp;</td>
					<td width="30%" >
						<span class='subACNoTr'>
							<input id="subACNo" name="subACNo" />&nbsp;
							<button type="button" id="subACNoPullin" >
								<span class="text-only"><th:block th:text="#{'button.pullin'}">引進</th:block></span>
							</button>
						</span>
					</td>
					<td width="20%" class="hd2" align="right"><th:block th:text="#{'C160S01F.subAmt'}">代償金額</th:block>&nbsp;</td>
					<td width="30%" ><input type="text" name="subAmt" id="subAmt" commonItem="numeric" class="required"/></td>
				</tr>
				<tr>
					<td class="hd2" align="right"><th:block th:text="#{'C160S01F.oLNAppDate'}">原貸放日期</th:block>&nbsp;</td>
					<td ><input type="text" id="oLNAppDate" name="oLNAppDate" class="date required"  /></td>
					<td class="hd2" align="right"><th:block th:text="#{'C160S01F.oLNEndDate'}">原貸款到期日</th:block>&nbsp;</td>
					<td ><input type="text" id="oLNEndDate" name="oLNEndDate" class="date"  /></td>
				</tr>
				<tr>
					<td class="hd2" align="right"><th:block th:text="#{'C160S01F.oLNRemYear'}">剩餘貸款年限(年)</th:block>&nbsp;</td>
					<td ><input type="text" id="oLNRemYear" name="oLNRemYear" class="max number" maxlength="2" size="1"/></td>
					<td class="hd2" align="right"><th:block th:text="#{'C160S01F.taxNo'}">設定抵押房屋稅籍編號</th:block>&nbsp;</td>
					<td ><input type="text" id="taxNo" name="taxNo" class="max" maxlength="60" maxlengthC="20" /></td>
				</tr>
				<tr>
					<td class="hd2" align="right"><span class="text-red">＊</span><th:block th:text="#{'C160S01F.subReason'}">代償同業原因</th:block>&nbsp;</td>
					<td colspan="3" >
						<input type="radio" id="subReason" name="subReason" class="required" codeType="L140S02H_subReason" />
						<input type="text" id="subReaOth" name="subReaOth" class="max required" maxlength="30" maxlengthC="10" />
						<br/>
						<th:block th:text="#{'C160S01F.msg1'}">(若原因為"其它"者，請說明原因)(10個字以內)</th:block>
					</td>
				</tr>
				<tr>
					<td class="hd2" align="right"><th:block th:text="#{'C160S01F.repaymentProduct'}">代償產品</th:block>&nbsp;</td>
					<td ><input type="radio" id="repaymentProduct" name="repaymentProduct"  codeType="repaymentProduct" class="max" maxlength="2" size="1"/></td>
					<td class="hd2" align="right"><th:block th:text="#{'C160S01F.custName'}">代償戶名</th:block>&nbsp;</td>
					<td ><input type="text" id="custName" name="custName" class="max" maxlength="60" maxlengthC="20" /></td>
				</tr>
				<tr>
					<td class="hd2" align="right"><th:block th:text="#{'C160S01F.accNo'}">代償帳號</th:block>&nbsp;</td>
					<td >
						<input type="text" id="accNo" name="accNo" class="max" maxlength="15" size="15"/>
						<input type="hidden" id="seq" name="seq" />
					</td>
				</tr>
			</table>
        </form>
	</th:block>
</body>
</html>
