/*_
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */

package tw.com.iisi.cap.base.pages;

import com.iisigroup.cap.component.PageParameters;

/**
 * <pre>
 * Abstract產生PDF報表.
 * </pre>
 * 
 * @since 2010/12/2
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2010/12/2,iristu,new
 *          </ul>
 */
public abstract class AbstractCapPdfPage extends AbstractCapPage {

    public AbstractCapPdfPage(PageParameters parameters) {
        super(parameters);
    }

    // /** The allow copy. */
    // private boolean allowCopy = true;
    //
    // /** The allow print. */
    // private boolean allowPrint = true;
    //
    // /**
    // * <p>
    // * 產生報表
    // * </p>
    // */
    // private class ReportOutputStream extends AbstractResourceStreamWriter {
    //
    // /** The Constant serialVersionUID. */
    // private static final long serialVersionUID = 1L;
    //
    // /*
    // * (non-Javadoc)
    // *
    // * @see
    // * org.apache.wicket.util.resource.IResourceStreamWriter#write(java.
    // * io.OutputStream)
    // */
    // public void write(OutputStream output) {
    //
    // try {
    // // 取得靜態參數
    // Map<String, Object> para = getReportParameter();
    //
    // // 取得動態報表資料集
    // JRDataSource ds = getReportDataSource();
    //
    // JasperPrint jasperPrint = JasperFillManager.fillReport(
    // getClass().getResourceAsStream(getReportDefinition()),
    // para, ds);
    //
    // JRPdfExporter exporter = new JRPdfExporter();
    // exporter.setParameter(JRExporterParameter.JASPER_PRINT,
    // jasperPrint);
    // exporter.setParameter(JRExporterParameter.OUTPUT_STREAM, output);
    //
    // setDefaultPdfParameter(exporter);
    // setPdfPermission(exporter);
    //
    // exporter.exportReport();
    //
    // } catch (Throwable e) {
    // logger.error(e.getMessage(), e);
    // }
    // }
    //
    // /*
    // * (non-Javadoc)
    // *
    // * @see org.apache.wicket.util.resource.IResourceStream#getContentType()
    // */
    // public String getContentType() {
    // return "application/pdf";
    // }
    //
    // }
    //
    // /**
    // * 取得圖檔的inputStream
    // *
    // * @param path
    // * 檔案名稱
    // * @return InputStream
    // */
    // protected InputStream getInputStream(String fileName) {
    // return getClass().getResourceAsStream("/reports/" + fileName);
    // }
    //
    // /**
    // * 取得報表
    // *
    // * @param reportName
    // * 報表名稱
    // * @return JasperReport
    // */
    // protected JasperReport getReport(String reportName) {
    // try {
    // return (JasperReport) JRLoader.loadObject(getClass()
    // .getResourceAsStream("/reports/" + reportName));
    // } catch (JRException e) {
    // return null;
    // }
    // }
    //
    // /**
    // * Sets the default pdf parameter.
    // *
    // * @param exporter
    // * the new default pdf parameter
    // */
    // private void setDefaultPdfParameter(JRPdfExporter exporter) {
    // exporter.setParameter(JRPdfExporterParameter.IS_ENCRYPTED, Boolean.TRUE);
    // exporter.setParameter(JRPdfExporterParameter.IS_128_BIT_KEY,
    // Boolean.TRUE);
    // exporter.setParameter(JRPdfExporterParameter.PDF_VERSION,
    // JRPdfExporterParameter.PDF_VERSION_1_7);
    // }
    //
    // /**
    // * Sets the pdf permission.
    // *
    // * @param exporter
    // * the new pdf permission
    // */
    // public void setPdfPermission(JRPdfExporter exporter) {
    // int opt = 0;
    // if (allowCopy) {
    // opt = opt | PdfWriter.ALLOW_COPY;
    // }
    //
    // if (allowPrint) {
    // opt = opt | PdfWriter.ALLOW_PRINTING;
    // }
    // exporter.setParameter(JRPdfExporterParameter.PERMISSIONS,
    // Integer.valueOf(opt));
    // }
    //
    // @Override
    // public void execute(PageParameters params) {
    // getRequestCycle().setRequestTarget(
    // new ResourceStreamRequestTarget(new ReportOutputStream()));
    // }
    //
    // /**
    // * Gets the report parameter.
    // *
    // * @return the report parameter
    // */
    // public abstract Map<String, Object> getReportParameter();
    //
    // /**
    // * Gets the report data source.
    // *
    // * @return the report data source
    // */
    // public abstract JRDataSource getReportDataSource();
    //
    // /**
    // * Gets the report definition.
    // *
    // * @return the report definition
    // */
    // public abstract String getReportDefinition();

}
