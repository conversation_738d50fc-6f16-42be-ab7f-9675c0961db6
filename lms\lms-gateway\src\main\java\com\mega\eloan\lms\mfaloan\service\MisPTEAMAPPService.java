/* 
 *MisPTEAMAPPService.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.mfaloan.service;

import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.mega.eloan.lms.mfaloan.bean.PTEAMAPP;

/**
 * <pre>
 * PTEAMAPP(ELF409)團貸年度總額度檔
 * </pre>
 * 
 * @since 2012/12/24
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/24,REX,new
 *          </ul>
 */
public interface MisPTEAMAPPService {

	/**
	 * 個金額度明細表 查詢總戶案號
	 * 
	 * @param custId
	 *            統一編號
	 * @param dupNo
	 *            重覆序號
	 * @return
	 */
	List<Map<String, Object>> getPTEAMAPPData(String custId, String dupNo);

	/**
	 * 個金簽報書 查詢總戶資訊 其總額度有效迄日大於今天 最大批號
	 * 
	 * @param custId
	 *            統一編號
	 * @param dupNo
	 *            重覆序號
	 * @return
	 */
	List<Map<String, Object>> getPTEAMAPPDataByEFFEND(String custId,
			String dupNo);

	/**
	 * 個金額度明細表 查詢總戶案號是否存在
	 * 
	 * @param custId
	 *            統一編號
	 * @param dupNo
	 *            重覆序號
	 * @param grpcntrNo
	 *            團貸總戶額度序號
	 * @return
	 */
	Map<String, Object> getPTEAMAPPData(String custId, String dupNo,
			String grpcntrNo);

	/**
	 * 取得團貸批號
	 * 
	 * @param custId
	 * @param dupNo
	 * @param branch
	 * @return
	 */
	String getMaxAmtappno(String custId, String dupNo, String branch);

	/**
	 * 取得團貸資訊
	 * 
	 * @param custId
	 * @param dupNo
	 * @param year
	 * @return
	 */
	Map<String, Object> findByIdNoYear(String custId, String dupNo, String year);

	/**
	 * 取得總戶資訊
	 * 
	 * @param custId
	 * @param dupNo
	 * @param year
	 * @return
	 */
	List<Map<String, Object>> findListByIdNo(String custId, String dupNo);

	/**
	 * 取得團貸資訊
	 * 
	 * @param GRPCNTRNO
	 * @param SUBCOMPID
	 * @return
	 */
	Map<String, Object> findByMgrnoSubid(String MGRPCNTRNO, String SUBCOMPID);

	/**
	 * 新增團貸資訊
	 * 
	 * @param GRPCNTRNO
	 */
	void insertByGrpno(String GRPCNTRNO, String SUBCOMPID, String SUBCOMPNM,
			String MGRPCNTRNO);

	List<Map<String, Object>> getDataByCustId(String custId);

	/**
	 * 查詢條件 WHERE  GRPCNTRNO = ? AND (EFFEND> CURRENT DATE) AND (CANCEL_DATE IS NULL) 			
	 * @param grpCntrNo
	 * @return
	 */
	Map<String, Object> getDataByGrpCntrNo(String grpCntrNo);

	/**
	 * PTEAMAPP 查詢團貸資訊 多筆
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @param effEnd
	 *            有效期限
	 * @return
	 */
	public List<PTEAMAPP> getDataBykey(String custId, String dupNo,
			String effEnd);

	/**
	 * 單筆
	 * 
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @param effEnd
	 * @return
	 */
	public PTEAMAPP getDataBykey(String custId, String dupNo, String cntrNo,
			String effEnd);

	/**
	 * 個金額度明細表 查詢總戶案號
	 * 
	 * @param custId
	 *            統一編號
	 * @param dupNo
	 *            重覆序號
	 * @return
	 */
	List<PTEAMAPP> getPTEAMAPPDataByBean(String custId, String dupNo);

	/**
	 * 個金覆審 查詢團貸到期
	 * 
	 * @param brNo
	 *            分行代號
	 * @param effend
	 *            團貸到期日
	 * @return
	 */
	public List<Map<String, Object>> selCrsGroupData(String brNo, Date effend);

	/**
	 * 個金覆審 查詢團貸總戶額度序號下的明細
	 * 
	 * @param brNo
	 *            分行代號
	 * @param grpCntrNo
	 *            團貸總戶額度序號
	 * @return
	 */
	public List<Map<String, Object>> selCrsGroupDetail_orderByLoanBalDesc(
			String brNo, String grpCntrNo);

	/**
	 * 取得團貸總戶多筆custId資料
	 * 
	 * @param custIdSet
	 * @return
	 */
	public HashMap<String, HashSet<String>> findDISTINCTCntrNoByCustId(
			HashSet<String> custIdSet);

	/**
	 * 查詢團貸資料by額度序號
	 * 
	 * @param grpCntrNos
	 * @return
	 */
	public List<PTEAMAPP> getDataByGrpCntrNo(HashSet<String> grpCntrNos);

	/**
	 * 取得該筆最大批號
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @param year
	 *            年度 3碼
	 * @param brno
	 *            分行代號
	 * @return
	 */
	Map<String, Object> getMaxAmtappno(String custId, String dupNo,
			String year, String brno);
	
	/**
	 * 取得團貸批號
	 * 
	 * @param custId
	 * @param dupNo
	 * @param branch
	 * @return
	 */
	public String getMaxAmtappno2(String loan);
	
	/**
	 * 原本是為了某些分行要抓出舊案，才加上
	 * OR (EFFEND <= ? and GRPCNTRNO in (select LNF020_GRP_CNTRNO from LN.LNF020 where LNF020_CANCEL_DATE is null))
	 * 
	 * 但現在已把全部(含銷戶的 grpCntrNo) 供經辦選擇
	 */
	@Deprecated
	public List<PTEAMAPP> getGrpDataBykey(String custId, String dupNo,String effEnd);
	
	/**
	 * 刪除團貸資料by額度序號
	 * 
	 * @param grpCntrNos
	 * @return
	 */
	public int delDataByGrpCntrNo(Set<String> grpCntrNos);
	
	/**
	 * 單筆
	 * @param cntrNo
	 * @return
	 */
	public PTEAMAPP getDataByLnf020GrpCntrNo(String cntrNo);
	
	public PTEAMAPP getGroupLoanBuildCaseMasterAccount(String groupLoanMasterCntrNo);
}
