/* 
 * L140S02HDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L140S02HDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L140S02H;

/** 代償轉貸借新還舊明細檔 **/
@Repository
public class L140S02HDaoImpl extends LMSJpaDao<L140S02H, String> implements
		L140S02HDao {

	@Override
	public L140S02H findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L140S02H> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L140S02H> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L140S02H> findByMainIdSeq(String mainId, int seq) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "seq", seq);
		List<L140S02H> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public L140S02H findByUniqueKey(String mainId, Integer seq, String bankNo,
			String branchNo, String subACNo) {
		ISearch search = createSearchTemplete();
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (seq != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "seq", seq);
		if (bankNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "bankNo", bankNo);
		if (branchNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "branchNo",
					branchNo);
		if (subACNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "subACNo",
					subACNo);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<L140S02H> findByIndex01(String mainId, Integer seq,
			String bankNo, String branchNo, String subACNo) {
		ISearch search = createSearchTemplete();
		List<L140S02H> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (seq != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "seq", seq);
		if (bankNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "bankNo", bankNo);
		if (branchNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "branchNo",
					branchNo);
		if (subACNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "subACNo",
					subACNo);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L140S02H> findByOids(String[] oids) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.IN, "oid", oids);
		List<L140S02H> list = createQuery(search).getResultList();
		return list;
	}
}