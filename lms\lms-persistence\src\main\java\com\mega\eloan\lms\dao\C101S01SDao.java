/* 
 * C101S01SDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C101S01S;

/** 個金客戶貸款信用資訊檔 **/
public interface C101S01SDao extends IGenericDao<C101S01S> {

	C101S01S findByOid(String oid);
	
	List<C101S01S> findByMainId(String mainId);
	
	public C101S01S findByUniqueKey(String mainId, String custId, String dupNo, String dataType, String fileSeq);
	
	public List<C101S01S> findByList(String mainId, String custId, String dupNo, String dataType);
	
	public List<C101S01S> findByIdDupDataType(String mainId, String custId, String dupNo, String dataType);
	
	public int deleteByOid(String oid);
}