package com.mega.eloan.lms.las.pages;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import com.iisigroup.cap.component.PageParameters;

import tw.com.jcs.auth.AuthType;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.SimpleButtonEnum;
import com.mega.eloan.common.html.EloanPageFragment;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.LasDocStatusEnum;
import com.mega.eloan.lms.las.panels.LASCOMMON02Panel;

/**
 * 稽核室 稽核工作底稿 編製中(歷史)
 * 
 * <AUTHOR>
 * 
 */
@Controller
@RequestMapping("/las/lms1935v02")
public class LMS1935V02Page extends AbstractEloanInnerView {

	public LMS1935V02Page() {
		super();
	}

	@Override
	public void execute(ModelMap model, PageParameters params) {
		setGridViewStatus(LasDocStatusEnum.稽核室_編製中);

		// 假如主管權限的話，如果沒有編輯權限，那麼打開文件就不要lock，這個還要配合lms1935v02.js一起處理
		boolean authModify = this.getAuth(AuthType.Modify);

		setJavaScriptVar("noOpenDoc", authModify ? "N" : "Y");
		
		List<EloanPageFragment> list = new ArrayList<>();
		
		list.add(LmsButtonEnum.Filter);
		if(authModify) {
			list.add(LmsButtonEnum.Delete);
			list.add(LmsButtonEnum.ToReviewAll);
		}
		
		list.add(LmsButtonEnum.PrintAllAudit);
		list.add(LmsButtonEnum.PrintAllBill);
		list.add(LmsButtonEnum.ApprCreditAndCase);
		
//		UPGRADE：
//		TRANSACTION_CODE在已註解的原LasButtonPanel中，雖引入但並未影響邏輯
//		下列model.put()僅留著以免日後需要TRANSACTION_CODE做邏輯調整用
//		model.put("transactionCode", params.getString(EloanConstants.TRANSACTION_CODE));
		addToButtonPanel(model, list);

		renderJsI18N(LMS1935V01Page.class);
		//前端發動checkPrint後，會使用到1945的內容在前端做訊息組合。
		renderJsI18N(LMS1945V01Page.class);
		
		setupIPanel(new LASCOMMON02Panel(PANEL_ID), model, params);	

	}
	//UPGRADE(Scott)
//	@Override
//	public String[] getJavascriptPath() {
//		return new String[] { "pagejs/las/LMS1935V02.js",
//				"pagejs/las/LASCOMMON.js" };
//	}

}
