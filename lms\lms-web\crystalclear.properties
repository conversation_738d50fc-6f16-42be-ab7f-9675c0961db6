#Sat Apr 30 22:30:34 CST 2022
#xls.celltruncate=false
license.systeminfo.domain=ELOAN
# Development mode settings - bypass license check
license.check.disabled=true
development.mode=true
#pdf.tagged=false
#fontmapping.pdf.sansserifFontList=["DFKai-SB","MingLi<PERSON>","Arial Unicode MS"]
#rowLimit=0
#sign.key.name=
#log.maxsize=100
#fontmapping.pdf.serifFontList=["MingLiU","DFKai-SB","Arial Unicode MS"]
#sign.key.password=009E1ACFE28419029B74745D4DE4EB58539E1A0FDCB51EB5B3E65C450450FF7F9F
#xls.celldistribution=staticlayout
#permission.allowunknowndatasource=true
#sign.pdf.enabled=false
#FontPath=/eloan/fonts
#CompressedPDF=true
#coreFile=/eloan/logs/inet
#log.engine=true
#QueryTimeout=300
#hasGroupTree=true
#fontmapping.pdf.monospacedFontList=[]
#MaxConnections=5
#sign.keystore.file=
#fontmapping.xls.sansserif=Arial
#fontmapping.xls.monospaced=Courier New
#log.file=/eloan/logs/inet/CCReport-cms.log
#log.driver=false
#crosstabCellLimit=0
#FontAutoScaling=false
license.systeminfo.processors=12
#metaDataCache.timeout=10
#ConnectionPoolTimeout=10
#UseNativeFonts=true
#log.maxnumber=10
#log.dateformat=M/d H\:mm\:ss
#TolerateErrors=true
#fontmapping.pdf.replaceNotEmbeddedFonts=true
# Original license key (expired) - commented out for development
#licensekey=Hb7FEZYxYoee3+l7e8yv3G/5d9ac2+MNzoeqflQVvnBodx1gogUU9CLtLl+tCfMItsy0ltrgzAJLWm/PJulQruq33S/xN0F4pwufzVwE4565ESqiKLY8wwcoPKnP+phLDCVaqmBmo2CJH5RWEDHSP+HkvtbWDyDK8V8jRl9cxuIGPwMNStYvgpCtq8A4GG5iLVANNIOEi249qCbRRkzlf4bdRH3cn3huvoI6NU6tFe37O9uTRdl0yOR4S9qzD2OJIHKn5IQ1ob/3sww/E470v21j2XVwkGwT4RZXRoyb8M1PiJO6DhboTtfqg1NTw9hd3wqjAZoRE1l3JrLrRoMCTw==
# Development license key (if available) or use development mode
# Try to use a development/evaluation license key
licensekey=DEVELOPMENT
#MaximumErrors=0
#logData=false
#pdf.replacemissingchar=true
#fontmapping.xls.serif=Times New Roman
#mapToAdobeFonts=true
#log.levels={"Help"\:1,"IndexSearchEngine"\:1,"Config"\:4,"JobManager"\:1,"Reporting"\:4,"FacturX"\:1,"Statistics"\:4,"Task Planner"\:4,"Persistence"\:1,"Remotegui"\:4,"Designer"\:4,"Authentication"\:1,"Users And Groups"\:1,"Cache"\:4}
#compress.font.viewer=false
PromptBehavior=NEVER_PROMPT
# Additional development settings
license.validation.disabled=true
license.expiry.check=false
#test.mode=true
# Try to disable license checking entirely
license.required=false
license.enforce=false
#evaluation.mode=true
#sign.keystore.type=JKS
#PdfAsianFontEncoding=950
#metaDataCache.enabled=false
#LoginTimeout=30
#compatibilityLevel=**********
#OnError_ExecuteSQL_WithoutSF=true
license.systeminfo.ips=127.0.0.1,localhost,***********,***********,************
#sign.keystore.password=00450C85DDD1ADD88D55BECA6FC041D759450CD5C4F692A6FA91AC4CA5460C38B4
#stopAfterPage=0
