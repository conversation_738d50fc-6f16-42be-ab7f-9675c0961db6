var PanelAction03 = {
    isInit: false,
    L140M01O_Oid: "",
    /**
     *頁面初始化的動作(第一次切到這頁籤執行)
     * */
    initAction: function(){
        _M.initItem("03");
        //第一次開啟box
        //產生下拉選單
        var $div = $(_M.boxContextId).find("#cls_CMSBoxDiv").find("[itemType]");
        var allKey = [];
        $div.each(function(){
            allKey.push($(this).attr("itemType"));
        });
        _M.codetypeItem = API.loadCombos(allKey);
        $div.each(function(){
            var $obj = $(this);
            var itemType = $obj.attr("itemType");
            if (itemType) {
                var format = $obj.attr("itemFormat") || "{value} - {key}";
                $obj.setItems({
                    space: $obj.attr("space") || true,
                    item: _M.codetypeItem[itemType],
                    format: format,
                    sort: $obj.attr("itemSort") || "asc",
                    size: $obj.attr("itemSize")
                });
            }
        });
        this.afterSetFormDataAction();
        this._initGrid();
        this._initEvent();
        PanelAction03.setheadItem7(_M.AllFormData["03"]["headItem7"] || "");        
        PanelAction03.setheadItem8(_M.AllFormData["03"]["headItem8"] || "");
        PanelAction03.setInqJcic(_M.AllFormData["03"]["hasCMS01"] || "");
    },
    /**
     *載入頁面後的動作(每次切到這頁籤執行)
     * */
    afterAction: function(){
        /**
         *<pre>
         PanelAction03.reloadGrid({
         tabFormMainId: _M.tabMainId
         }, PanelAction03.grid);
         PanelAction03.setInqJcic(_M.AllFormData["03"]["hasCMS01"] || "");
         </pre>
         */
    },
    _initEvent: function(){
        var $boxContext = $(_M.boxContextId);
        /**
         *是否為開放型基金股票
         */
        var $headItem5Span = $boxContext.find("#headItem5Span");
        $boxContext.find("[name=headItem5]:radio").change(function(){
            $headItem5Span.hide();
            if ($("[name=headItem5]:radio:checked").val() == "Y") {
                $headItem5Span.show();
            }
        });
        
        
        var $mRate = $boxContext.find("#mRate");
        /**
         *擔保維持率
         */
        $boxContext.find("#mRateType").change(function(){
            $mRate.hide();
            if ($(this).val() == "0") {
                $mRate.val("140");
            } else if ($(this).val() == "1") {
                $mRate.show();
            }
        });
        /**
         登錄分行代號
         */
        $boxContext.find("#cls_selectCMSBranchBt").click(function(){
            API.showAllBranch({
                btnAction: function(a, b){
                    $("#selectFilterCMDBrno").val(b.brNo);
                    $.thickbox.close();
                }
            });
        });
        /**
         * 查詢引進擔保品
         */
        $boxContext.find("#cls_queryCMSData").click(function(){
        	_M.verifyCntrNoAction().done(function(json){
        		PanelAction03.queryCMSData();
        	});
        });
        /**
         * 引進擔保品
         */
        $boxContext.find("#cls_inculdeCMS").click(function(){
        	_M.verifyCntrNoAction().done(function(json){
        		PanelAction03.inculdeCMS();
            });
        });
        /**
         * 刪除擔保品
         */
        $boxContext.find("#cls_deleteCMS").click(function(){
            PanelAction03.deleteCMS();
        });
        /**
         * 重新引進文字
         */
        $boxContext.find("#cls_reloadCMS").click(function(){
            PanelAction03.reloadCMS();
        });
        
        /**
         * 更新grid
         */
        $boxContext.find("#cls_lms140Tab05").click(function(){
            //組字串
        	//J-112-0586_05097_B1002 依據簽會-2023-2192「Web eLoan-Checkmarx弱點改善會議」按季追蹤弱點修正進度
            var headItem1 = $("input[name=headItem1]:checked").val(), gutPercent = DOMPurify.sanitize($("#gutPercent").val());
            if ("Y" == headItem1 && gutPercent != "0") {
                $("#toALoan3").html(i18n.cls1151s01["L140M01a.message67"] + gutPercent + "%");
            } else {
                $("#toALoan3").html("");
            }
            PanelAction03.reloadGrid({
                tabFormMainId: _M.tabMainId
            }, PanelAction03.grid);
            PanelAction03.reloadGrid({}, PanelAction03.srcGrid);
        });
        
        //擔保品係
        $boxContext.find("#term1").change(function(){
        	var term1Ver = $("select#term1Ver option:selected").val();
        	
            var term1Str = "";
            switch ($(this).val()) {
                case "1":
                	if(term1Ver=="1"){
                		//page3.026V1=，應由借款人出具「同意書」予本行。	                	
                		term1Str = i18n.cls1151s01["page3.026V1"];
                	}else{
	                    //page3.026N=，應由擔保品提供人出具「同意書」及借款人出具「自住切結書」予本行。
                		// J-113-0149 E-LOAN簽報書額度明細表擔保品明細切結同意事項修改
                		// page3.026N.V2=，應由擔保品提供人出具「不動產使用狀況暨擔保借款抵押權設定種類聲明書」
                        // 及借款人出具「自住切結書」或申請日於民國113年4月1日後，
                        // 得改出具「消費金融專用借款申請書暨個人資料表之房屋貸款擔保品用途聲明切結事項」予本行。
	                    term1Str = i18n.cls1151s01["page3.026N.V2"];
                	}
                    break;
                case "2":
                case "4":
                    if(term1Ver=="1"){
                		//page3.027V1=，應由承租人出具「同意書」予本行【應徵提租賃合約書】。
                    	term1Str = i18n.cls1151s01["page3.027V1"];
                	}else{
                        //page3.027N=，應由擔保品提供人出具「同意書」，放款值如未扣除租賃押金，另應由承租人出具「承諾書」予本行【應徵提租賃合約書】。                    	
                		term1Str = i18n.cls1151s01["page3.027N"];
                	}
                	break;
                case "3":
                    if(term1Ver=="1"){
                		//page3.028V1=，應由借款人出具「同意書」予本行。
                    	term1Str = i18n.cls1151s01["page3.028V1"];
                	}else{
                    	//page3.028N=，應由擔保品提供人出具「同意書」予本行。                    	
                		term1Str = i18n.cls1151s01["page3.028N"];
                	}
                	break;
                default:
                    break;
            }
            $("#term1Str").html(term1Str);
        });
        
        //擔保品  加建增建未辦保存登記之建物
        var $term2ByValue1Span = $("#term2ByValue1Span");
        $boxContext.find("#term2").change(function(){
            if ($(this).val() == "1") {
                $term2ByValue1Span.show();
            } else {
                $term2ByValue1Span.hide().find("input").val("");
            }
        });
        
        var $cls_L140M01OForm = $("#cls_L140M01OForm");
        
        //借款金額                                 
        var $inAppMoney = $cls_L140M01OForm.find("#inAppMoney");
        //土地放款值
        var $assTot = $cls_L140M01OForm.find("#assTot");
        
        //火險金額計算方式為
        $cls_L140M01OForm.find("[name=fireIns][value=1]").click(function(){
            var $this = $(this);
            if ($this.is(":checked")) {
                $inAppMoney.addClass("required");
                $assTot.addClass("required");
            } else {
                $inAppMoney.removeClass("required");
                $assTot.removeClass("required");
            }
        });
        
        
        /**
         * 查詢重置成本計算公式
         */
        $cls_L140M01OForm.find("#queryL140M01OrebuildCost").click(function(){
            _M.doAjax({
                action: "queryL140M01OrebuildCost",
                data: {
                    oid: PanelAction03.L140M01O_Oid
                },
                success: function(obj){
                    if (obj.msg) {
                        API.showMessage(obj.msg);
                    } else {
                        //grid.emptyrecords=查無資料
                        API.showMessage(i18n.def("grid.emptyrecords"));
                    }
                    
                }
            });
        });
        $cls_L140M01OForm.find("#queryL140M01OrebuildCostSince1090101").click(function(){
            _M.doAjax({
                action: "queryL140M01OrebuildCost",
                data: {
                    oid: PanelAction03.L140M01O_Oid,
                    'rebuild_cost_since_1090101' : 'Y'
                },
                success: function(obj){
                    if (obj.msg) {
                        API.showMessage(obj.msg);
                    } else {
                        //grid.emptyrecords=查無資料
                        API.showMessage(i18n.def("grid.emptyrecords"));
                    }
                    
                }
            });
        });
		
//		$cls_L140M01OForm.find('#href_HLoanPercentSummaryTable').click(function(){
//            $.form.submit({
//                url: webroot + '/app/simple/FileProcessingService',
//                target: "_blank",
//                data: {
//                	markModel: "HLoanPercentSummaryTable",
//                    fileDownloadName: "HLoanPercentSummaryTable_11008.pdf",
//                    serviceName: "cls1131s01pdfservice"
//                }
//            });
//        });
    },
    grid: null,
    srcGrid: null,
    formId: "#cls_queryCMSDataForm",
    _initGrid: function(){
        var $boxContext = $(_M.boxContextId);
        this.grid = $boxContext.find("#cls_gridviewCollateral").iGrid({
            handler: _M.ghandle,
            action: "queryL140m01o",
            height: 200,
            rownumbers: true,
            autowidth: true,
            postData: {
                tabFormMainId: _M.tabMainId
            },
            multiselect: true,
            colModel: [{
                colHeader: i18n.cls1151s01['l1405s02c01.015'],//擔保品分行
                name: 'branch',
                align: "left",
                width: 110,
                sortable: true,
                formatter: 'click',
                onclick: PanelAction03.openCMSBox
            }, {
                colHeader: i18n.cls1151s01['l1405s02c01.016'],//擔保品類別
                name: 'collTyp1',
                align: "left",
                width: 110,
                sortable: true
            }, {
                colHeader: i18n.cls1151s01['L140S02Tab.5_01'],//擔保品內容
                name: 'cmsDesc',
                width: 160,
                sortable: false
            }, {
                colHeader: i18n.cls1151s01['l1405s02c01.022'],//核貸成數
                name: 'payPercent',
                width: 60,
                align: "right",
                sortable: true,
                formatter: 'currency',
                formatoptions: {
                    thousandsSeparator: ",",
                    decimalPlaces: 2,
                    suffix: "%"
                }
            }, {
                name: 'oid',
                hidden: true
            }],
            ondblClickRow: function(rowid){
                var data = PanelAction03.grid.getRowData(rowid);
                PanelAction03.openCMSBox(null, null, data);
            }
        });
        
        this.srcGrid = $boxContext.find("#cls_gridviewSrcColl").iGrid({
            handler: _M.ghandle,
            height: 200,
            action: "queryCMS",
            rownumbers: true,
            autowidth: true,
            multiselect: true,
            colModel: [{
                colHeader: i18n.cls1151s01['l1405s02c01.018'],//鑑估日期
                name: 'estDate',
                align: "left",
                width: 80,
                sortable: true
            }, {
                colHeader: i18n.cls1151s01['L140M01A.custName'],//借款人名稱
                name: 'custName',
                align: "left",
                width: 160,
                sortable: true
            }, {
                colHeader: i18n.cls1151s01['l140s02p05.007'],//擔保品種類
                name: 'collTyp1',
                width: 100,
                sortable: true
            }, {
                colHeader: i18n.cls1151s01['l1405s02c01.016'],//擔保品小類
                name: 'collTyp2',
                width: 100,
                sortable: true
            }, {
                colHeader: i18n.cls1151s01['L140M01O.collNo'],//擔保品編號
                name: 'collNo',
                width: 100,
                sortable: true
            }, {
                colHeader: i18n.cls1151s01['L140M01A.docStatus'],//文件狀態
                name: 'docStatus',
                width: 100,
                sortable: true
            }, {
                colHeader: i18n.cls1151s01['L140M01A.docDscr'],//文件說明
                name: 'docDscr',
                width: 90,
                sortable: true
            }, {
                colHeader: "&nbsp",//幣別
                name: 'currCd',
                align: "center",
                width: 50,
                sortable: true
            }, {
                colHeader: i18n.cls1151s01['l1405s02c01.017'],//放款值
                name: 'loanAmt',
                align: "right",
                width: 120,
                sortable: true,
                formatter: 'currency',
                formatoptions: {
                    thousandsSeparator: ",",
                    decimalPlaces: 0
                }
            },{
                colHeader: i18n.cls1151s01['l1405s02c01.025'],//估價人員
                name: 'appraiserName',
                align: "left",
                width: 80,
                sortable: false
            }, {
                name: 'appraiser',
                hidden: true
            }, {
                name: 'oid',
                hidden: true
            }, {
                name: 'mainId',
                hidden: true
            }]
        });
    },
    /**
     * 設定是否為股票開放型基金欄位
     * @param {String } value Y|N
     */
    setheadItem5: function(value){
        $(_M.boxContextId).find("[name=headItem5][value=" + value + "]:radio").attr("checked", "checked").trigger("change");
    },
    setheadItem7: function(value){
    	$(_M.boxContextId).find("[name=headItem7]").attr('checked', false).filter("[value='"+value+"']").trigger('click').attr("checked", true).trigger("change");;        
    },
    setheadItem8: function(value){
    	$(_M.boxContextId).find("[name=headItem8]").attr('checked', false).filter("[value='"+value+"']").trigger('click').attr("checked", true).trigger("change");;        
    },
    /**
     * 設定是否顯示已查詢聯徵中心不動產鑑價資訊
     * @param {boolean } value Y | N
     */
    setInqJcic: function(value){
        _M.AllFormData["03"]["hasCMS01"] = value;
        var $inqJcicTr = $(_M.boxContextId).find("#inqJcicTr");
        $("#CLS1151Form03").find("#hasCMS01").val(value);
        if (value == "Y") {
            $inqJcicTr.show();
        } else {
            $inqJcicTr.hide().find("[name=inqJcic]").removeAttr("checked");
        }
    },
    /**
     * 設定描述值
     * @param {Object} drc 描述
     */
    setDrc: function(drc){
        $("#itemDscr3").val(drc);
    },
    /**
     *依查詢條件重新查詢grid
     */
    queryCMSData: function(){
        var $form = $(PanelAction03.formId);
        if (!$form.valid()) {
            return false;
        }
        var queryData = $.extend({
            "query": true
        }, $form.serializeData())
        PanelAction03.reloadGrid(queryData, PanelAction03.srcGrid);
    },
    /**
     * 設定初始值
     */
    setDefault: function(){
        var $CMSBoxDiv = $("#cls_CMSBoxDiv");
        var data = _M.AllFormData["01"];
        $CMSBoxDiv.find("#selectFilterCMDBrno").val(data["ownBrId"]);
        $CMSBoxDiv.find("#cmsCustId").val(data["custId"]);
        $CMSBoxDiv.find("#cmsDupNo").val(data["dupNo"]);
        if (_M.AllFormData["04"]["snoKind"] && _M.AllFormData["04"]["snoKind"] == "20") {
        	$CMSBoxDiv.find("#cmsType").val("05"); //信保
        }else{
        	$CMSBoxDiv.find("#cmsType").val("01");
        }	
    },
    /**
     *引進擔保品
     */
    inculdeCMS: function(){
        var queryData = {
            "query": false
        };
        PanelAction03.reloadGrid(queryData, PanelAction03.srcGrid);
        PanelAction03.setDefault();
        $("#cls_CMSBox").thickbox({
            title: i18n.cls1151s01["btn.cms"],
            width: 980,
            height: 610,
            modal: true,
            align: "center",
            valign: "bottom",
            readOnly: _openerLockDoc == "1",
            i18n: i18n.def,
            open: function(){
                $("#CMSBoxDiv").wrap("<form id='cls_queryCMSDataForm'></form>");
            },
            close: function(){
                $("#CMSBoxDiv").unwrap();
            },
            buttons: {
                "sure": function(){
                    var gridIDs = PanelAction03.srcGrid.getGridParam('selarrrow');
                    if (gridIDs == "") {
                        //action_005=請先選取一筆以上之資料列
                        API.showMessage(i18n.def["action_005"]);
                        return false;
                    }
                    var gridsOid = [];
                    for (var i = 0, total = gridIDs.length; i < total; i++) {
                        gridsOid[i] = PanelAction03.srcGrid.getRowData(gridIDs[i]).oid;
						
						//J-109-0298 檢核引入之估價人員，如若有與授信經辦為同一人時，由系統發訊息警示。
						var appraiser = PanelAction03.srcGrid.getRowData(gridIDs[i]).appraiser;
						if (appraiser == userInfo.userId) {
							//C100M01.Error1=估價人員不可和授信經辦為同一人!!
                        	API.showErrorMessage(i18n.cls1151s01["C100M01.Error1"]);
                        	return false;
						}
                    }
                    _M.doAjax({
                        action: "inculdeL140M01O",
                        data: {
                            oids: gridsOid
                        },
                        success: function(obj){
                            PanelAction03.setDrc(obj.drc);
                            PanelAction03.reloadGrid({}, PanelAction03.grid);
                            PanelAction03.setheadItem5(obj.headItem5 || "");
                            PanelAction03.setheadItem7(obj.headItem7 || "");
                            PanelAction03.setheadItem8(obj.headItem8 || "");
                            PanelAction03.setInqJcic(obj.hasCMS01 || "");
                            $.thickbox.close();
                        }
                    });
                    
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    deleteCMS: function(){
        var gridIDs = PanelAction03.grid.getGridParam('selarrrow');
        if (gridIDs == "") {
            //TMMDeleteError=請先選擇需修改(刪除)之資料列
            API.showMessage(i18n.def["TMMDeleteError"]);
            return false;
        }
        
        //confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                var gridsOid = [];
                for (var i = 0, total = gridIDs.length; i < total; i++) {
                    gridsOid[i] = PanelAction03.grid.getRowData(gridIDs[i]).oid;
                }
                _M.doAjax({
                    action: "deleteL140M01O",
                    data: {
                        oids: gridsOid
                    },
                    success: function(obj){
                        PanelAction03.setDrc(obj.drc);
                        PanelAction03.reloadGrid({}, PanelAction03.grid);
                        PanelAction03.setheadItem5(obj.headItem5 || "");
                        PanelAction03.setInqJcic(obj.hasCMS01 || "");
                        if (obj.tips) {
                            API.showMessage(obj.tips);
                        }
                    }
                });
            }
        });
    },
    /**
     * 重新引進
     */
    reloadCMS: function(){
        _M.doAjax({
            action: "reloadCMSDesc",
            data: {
                inqJcic: $("[name=inqJcic]:checked").val()
            },
            success: function(obj){
                PanelAction03.setDrc(obj.drc);
            }
        });
    },
    /**
     * 重新整理Grid
     * @param {Object} data 設定資料
     * @param {Object} gridObject
     */
    reloadGrid: function(data, gridObject){
        gridObject.jqGrid("setGridParam", {
            sortname: "collTyp1",
            sortorder: "asc",
            postData: $.extend({
                tabFormMainId: _M.tabMainId,
                selectFilterCMDBrno: "",
                cmsCustId: "",
                cmsDupNo: "",
                cmsType: ""
            }, data || {}),
            page: 1,
            search: true
        }).trigger("reloadGrid");
    },
    
    isInitCMSBox: false,
    /**
     * 開啟修改鑑估值
     * @param {Object} cellvalue
     * @param {Object} type
     * @param {Object} data
     */
    openCMSBox: function(cellvalue, type, data){
        var $form = $("#cls_L140M01OForm");
        //借款金額                                 
        var $inAppMoney = $form.find("#inAppMoney");
        //土地放款值
        var $assTot = $form.find("#assTot");
        $inAppMoney.removeClass("required");
        $assTot.removeClass("required");
        if (!this.isInitCMSBox) {
            //初始化下拉選單
            var allKey = [];
            var $tagets = $form.find("[itemType]");
            $tagets.each(function(){
                allKey.push($(this).attr("itemType"));
            });
            var item = API.loadCombos(allKey);
            
            var disabledBankCode06Arr = [];
            if(item['BankCode06'] && _M.isReadOnly==false){
            	_M.doAjax({
                    action: "queryNoneLifeInsurance",
                    async:false,
                    data: {
                    },
                    success: function(resp){
                    	$.each(item['BankCode06'], function(key,value){		                	
                    		if($.inArray(key, resp.flag_1_list)> -1 ){
                    			disabledBankCode06Arr.push(key);                    			
                    		}else if($.inArray(key, resp.flag_2_list)> -1 ){
                    		}else{
                    			delete item['BankCode06'][key];
                    		}
		                });                    	
                    }
                });	
            }

            $tagets.each(function(){
                var $obj = $(this);
                var itemType = $obj.attr("itemType");
                if (itemType) {
                    var format = $obj.attr("itemFormat") || "{key}";
                    $obj.setItems({
                        space: $obj.attr("space") || true,
                        item: item[itemType],
                        format: format,
                        sort: $obj.attr("itemSort") || "asc",
                        size: $obj.attr("itemSize")
                    });
                }
            });
            
            if(disabledBankCode06Arr.length>0){
            	$.each($("#insId option"), function(idx,obj){
					var key = obj.value;
					var value = obj.text;
					
					if($.inArray(key, disabledBankCode06Arr)> -1 ){
						$(obj).attr( "disabled","disabled" );	
					}
				});	
            	
            }
            this.isInitCMSBox = true;
        }
        $form.reset();
        _M.doAjax({
            action: "queryL140M01O",
            data: {
                oid: data.oid
            },
            success: function(obj){
                PanelAction03.editBox1(data.oid, obj.isCollTyp101);
                PanelAction03.L140M01O_Oid = data.oid;
                $form.injectData(obj);
				
				$(".showMan").hide();
				$(".showSen").hide();
				if(obj.unitChg=="Y"){
					$(".showSen").show();	
				}else{
					$(".showMan").show();
				}
                if (obj.fireIns) {
                    var fireInsData = obj.fireIns.split("|");
                    $form.find("[name=fireIns]").val(fireInsData);
                    
                    for (var value in fireInsData) {
                        if (value == "1") {
                            $inAppMoney.addClass("required");
                            $assTot.addClass("required");
                        }
                    }
                    
                } else {
                
                }

				var isPayPercentReadonly = obj.collTyp1 == '01' ? true : false;
				$("#L140M01O_payPercent").attr("readonly", isPayPercentReadonly);
				
                $form.find("select").trigger("change");
            }
        });
    },
    /** 不動產用
     *
     * @param {Object} oid 文件編號
     * @param {boolean} isCollTyp101 是否為不動產
     */
    editBox1: function(oid, isCollTyp101){
        var $table = $("#forCollTyp101Show");
        $table.hide();
        if (isCollTyp101) {
            $table.show();
        }
        $("#cls_L140M01OBox").thickbox({
            title: i18n.cls1151s01["btn.cms"],
            width: isCollTyp101 ? 900 : 450,
            height: isCollTyp101 ? 530 : 230,
            modal: true,
            readOnly: _openerLockDoc == "1" || _M.isReadOnly,
            i18n: i18n.def,
            buttons: {
                "saveData": function(){
                    var $form = $("#cls_L140M01OForm");
                    if ($form.valid()) {
                        var fireIns = [];
                        $form.find("[name=fireIns]:checked").each(function(){
                            fireIns.push($(this).val());
                        });
                        //page3.058=是否重組擔保品描述?
                        API.confirmMessage(i18n.cls1151s01["page3.058"], function(b){
                            if (b) {
                                _M.doAjax({
                                    action: "saveL140M01O",
                                    formId: "cls_L140M01OForm",
                                    data: {
                                        oid: oid,
                                        fireInsVal: fireIns.join("|"),
                                        payPercent: $form.find("#L140M01O_payPercent").val(),
                                        resetDrc: true
                                    },
                                    success: function(obj){
                                        PanelAction03.setDrc(obj.drc);
                                        PanelAction03.reloadGrid({}, PanelAction03.grid);
										
										if (obj.tips) {
											API.showMessage(obj.tips);
										}
										else{
											$.thickbox.close();
										}
                                    }
                                });
                            } else {
                                _M.doAjax({
                                    action: "saveL140M01O",
                                    formId: "cls_L140M01OForm",
                                    data: {
                                        oid: oid,
                                        fireInsVal: fireIns.join("|"),
                                        payPercent: $form.find("#L140M01O_payPercent").val()
                                    },
                                    success: function(obj){
                                        PanelAction03.reloadGrid({}, PanelAction03.grid);
                                        $.thickbox.close();
                                    }
                                });
                            }
                        });
                        
                    }
                },
                "close": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    /**
     *載入頁面後的動作 每次切換到此頁面都執行
     * */
    afterSetFormDataAction: function(){
        if ($('#pageNum3').val() == '0') {
            $('#itemDscr3').attr('distanceWord', '48');
        } else {
            $('#itemDscr3').attr('distanceWord', '58');
        }
    }
};


_M.pageInitAcion["03"] = PanelAction03;
