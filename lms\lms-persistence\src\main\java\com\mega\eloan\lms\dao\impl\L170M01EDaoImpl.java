package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.annotation.Resource;
import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.dao.CodeTypeDao;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.lms.dao.L170M01EDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L170M01E;

/** 信用評等資料檔 **/
@Repository
public class L170M01EDaoImpl extends LMSJpaDao<L170M01E, String> implements
		L170M01EDao {

	@Resource
	CodeTypeDao codeTypeDao;
	/*
	 * DBU大型企業("DB"), DBU中小型企業("DL"), 海外("OU"), 境外("OB"), 泰國GroupA("CA"),
	 * 泰國GroupB("CB"), 納閩大企業及聯貸案("CC"), 納閩自貸案("CD"),納閩金融機構("CE"),
	 * 岷行大企業("CF"),岷行中小企業("CG"), 消金評等("CS"), Moody("NM"), SP("NS"), Fitch("NF"),
	 * 中華信評("NC");
	 */

	@Override
	public L170M01E findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L170M01E> findByMainId(String mainId, String timeFlag) {
		ISearch search = createSearchTemplete();

		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "timeFlag",
				(Util.equals("", timeFlag) ? "T" : timeFlag));
		return find(search);
	}

	@Override
	public List<L170M01E> findByUniqueKey(String mainId, String custId,
			String dupNo, String timeFlag) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "timeFlag",
				(Util.equals("", timeFlag) ? "T" : timeFlag));
		return find(search);
	}

	@Override
	public L170M01E findByCustIdAndDupNo(String custId, String dupNo,
			String timeFlag) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "timeFlag",
				(Util.equals("", timeFlag) ? "T" : timeFlag));
		return findUniqueOrNone(search);
	}

	@Override
	public void deleteL170m01eList(String mainId) {
		// Orm.xml
		Query query = getEntityManager().createNamedQuery(
				"L170M01E.deleteByMainId");
		// 設置參數
		query.setParameter("MAINID", mainId);
		query.executeUpdate();

	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> findByUniqueKey3(String mainId, String custId,
			String dupNo, String timeFlag) {

		Query query = getEntityManager()
				.createNamedQuery("L170M01E.selCrdType");
		query.setParameter("MAINID", mainId); // 設置參數
		query.setParameter("CUSTID", custId); // 設置參數
		query.setParameter("DUPNO", dupNo); // 設置參數
		query.setParameter("TIMEFLAG", (Util.equals("", timeFlag) ? "T"
				: timeFlag));
		return query.getResultList();
	}

	@Override
	public List<L170M01E> findByUniqueKey2(String mainId, String custId,
			String dupNo, String timeFlag) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.IN, "crdType", new Object[] {
				"DB", "DL", "OU", "NA" });
		search.addSearchModeParameters(SearchMode.EQUALS, "timeFlag",
				(Util.equals("", timeFlag) ? "T" : timeFlag));
		List<L170M01E> list = createQuery(L170M01E.class, search)
				.getResultList();
		return list;
	}

	@Override
	public List<L170M01E> findByUniqueKey6(String mainId, String custId,
			String dupNo, String timeFlag) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "timeFlag",
				(Util.equals("", timeFlag) ? "T" : timeFlag));
		List<L170M01E> list = createQuery(L170M01E.class, search)
				.getResultList();
		return list;
	}

	@Override
	public List<L170M01E> findByUniqueKey4(String mainId, String custId,
			String dupNo, String timeFlag) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.IN, "crdType", new Object[] {
				"CA", "CB", "CB", "CD", "CE", "CF", "CG", "CK", "CS", "NM",
				"NF", "NS", "NC" });
		search.addSearchModeParameters(SearchMode.EQUALS, "timeFlag",
				(Util.equals("", timeFlag) ? "T" : timeFlag));
		List<L170M01E> list = createQuery(L170M01E.class, search)
				.getResultList();
		return list;
	}

	@Override
	public List<L170M01E> findByUniqueKey5(String mainId, String custId,
			String dupNo, String timeFlag) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		List<CodeType> crdTypeList = codeTypeDao.findByCodeType("lms1705s01_crdType2", "zh_TW");
		Object[] crdTypeArray = new Object[crdTypeList.size()];
		for(int i =0;i<crdTypeList.size();i++){
			crdTypeArray[i] = crdTypeList.get(i).getCodeValue();
		}
		// J-108-0128_05097_B1001 Web e-Loan企金授信覆審系統修改覆審報告表內容。
        search.addSearchModeParameters(SearchMode.IN, "crdType", crdTypeArray);
		search.addSearchModeParameters(SearchMode.EQUALS, "timeFlag",
				(Util.equals("", timeFlag) ? "T" : timeFlag));
		List<L170M01E> list = createQuery(L170M01E.class, search)
				.getResultList();
		return list;
	}

	@Override
	public List<L170M01E> findByCntrNo(String CntrNo, String timeFlag) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", CntrNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "timeFlag",
				(Util.equals("", timeFlag) ? "T" : timeFlag));
		search.addOrderBy("cntrNo");
		List<L170M01E> list = createQuery(L170M01E.class, search)
				.getResultList();

		return list;
	}

	@Override
	public List<L170M01E> findByCustIdDupId(String custId, String DupNo,
			String timeFlag) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", DupNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "timeFlag",
				(Util.equals("", timeFlag) ? "T" : timeFlag));
		List<L170M01E> list = createQuery(L170M01E.class, search)
				.getResultList();
		return list;
	}
}