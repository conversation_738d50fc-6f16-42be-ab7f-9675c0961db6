/* 
 * MicroEntServiceImpl.java 
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;

import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.exception.GWException;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.lms.base.common.BranchRate;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.eloan.lms.base.service.LMSBisService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dao.L120S25ADao;
import com.mega.eloan.lms.dao.L120S25BDao;
import com.mega.eloan.lms.dao.L120S25CDao;
import com.mega.eloan.lms.dao.L140M01ADao;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120S04A;
import com.mega.eloan.lms.model.L120S25A;
import com.mega.eloan.lms.model.L120S25B;
import com.mega.eloan.lms.model.L120S25C;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.obsdb.service.MisELF001Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * J-110-0986_05097_B1001 於簽報書新增LGD欄位
 * </pre>
 * 
 * @since 2019/09
 * <AUTHOR>
 * @version <ul>
 *          <li>2019/09,009301,new
 *          </ul>
 */
@Service("LMSBisService")
public class LMSBisServiceImpl extends AbstractCapService implements
		LMSBisService {

	protected final Logger logger = LoggerFactory.getLogger(getClass());

	@Resource
	L120S25ADao l120s25aDao;
	@Resource
	L120S25BDao l120s25bDao;
	@Resource
	L120S25CDao l120s25cDao;
	@Resource
	L140M01ADao l140m01aDao;
	@Resource
	LMSService lmsService;

	@Resource
	DwdbBASEService dwdbService;

	@Resource
	MisELF001Service misELF003Service;

	@Override
	public void save(GenericBean... entity) {
		// 進行無限多筆儲存
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L120S25A) {
					((L120S25A) model).setUpdater(user.getUserId());
					((L120S25A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l120s25aDao.save((L120S25A) model);
				} else if (model instanceof L120S25B) {
					l120s25bDao.save((L120S25B) model);
				} else if (model instanceof L120S25C) {
					((L120S25C) model).setUpdater(user.getUserId());
					((L120S25C) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l120s25cDao.save((L120S25C) model);
				}

			}
		}
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == L120S25A.class) {
			return l120s25aDao.findPage(search);
		} else if (clazz == L120S25B.class) {
			return l120s25bDao.findPage(search);
		} else if (clazz == L120S25C.class) {
			return l120s25cDao.findPage(search);
		}
		return null;
	}

	@Override
	public void delete(GenericBean... entity) {
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L120S25A) {
					l120s25aDao.delete((L120S25A) model);
				} else if (model instanceof L120S25B) {
					l120s25bDao.delete((L120S25B) model);
				} else if (model instanceof L120S25C) {
					l120s25cDao.delete((L120S25C) model);
				}
			}
		}
	}

	@SuppressWarnings({ "rawtypes" })
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		if (clazz == L120S25A.class) {
			return l120s25aDao.findByMainId(mainId);
		}
		return null;
	}

	@Override
	public List<L120S25C> findL120s25cByParamTypeKeyDate(String paramType,
			String paramKey, String paramDate) {
		return l120s25cDao.findByParamTypeKeyDate(paramType, paramKey,
				paramDate);
	}

	@Override
	public void deleteListL120s25c(List<L120S25C> list) {
		List<String> listOid = new ArrayList<String>();
		for (L120S25C model : list) {
			listOid.add(model.getOid());
		}
		l120s25cDao.delete(list);
	}

	/**
	 * J-111-0443_05097_B1002 Web e-Loan企金授信開發授信BIS評估表
	 */
	@SuppressWarnings({ "unused", "rawtypes" })
	// @Override
	public void impBisEstimatedReturn(String kind,
			String mainId, String queryDateE, JSONObject jsonData,
			BranchRate branchRate, String qCustId, String qDupNo,
			Boolean isKindA) throws CapException {

		L120M01A meta = lmsService.findModelByMainId(L120M01A.class, mainId);

		List<L120S04A> listL120s04a = lmsService
				.findL120s04aByMainIdKeyCustIdDupNo(mainId, qCustId, qDupNo);

		JSONObject json = new JSONObject();
		JSONObject jsonM = new JSONObject();
		JSONObject jsonG = new JSONObject();
		JSONObject jsonR = new JSONObject();

		// 查詢起日-往前推半年
		Date dQueryDate6S = CapDate.addMonth(Util.parseDate(queryDateE), -5);
		// 查詢起日-往前推一年
		Date dQueryDate12S = CapDate.addMonth(Util.parseDate(queryDateE), -11);
		// 查詢迄日
		Date dQueryDateE = Util.parseDate(queryDateE);

		String MAX_CYC_MN = null;
		String MIN_CYC_MN = null;
		String mCustId = Util.trim(meta.getCustId());
		String mDupNo = Util.trim(meta.getDupNo());
		String _mDupNo = "0".equals(Util.trim(meta.getDupNo())) ? "" : Util
				.trim(meta.getDupNo());
		String tGPID = null;
		String tGPName = null;
		String tID = null;
		String tDUPNO = null;
		String tCUSTNAME = null;
		String tGRADE = null;
		String end3Mon = null;

		long depTime = 0; // 活期存款
		long depFixed = 0; // 定期存款
		long loanQuota = 0; // 額度
		long loanAvgBal = 0; // 平均餘額
		BigDecimal loanAvgRate = new BigDecimal("0"); // 平均動用率
		long exchgImpRec = 0; // 進口筆數
		long exchgImpAmt = 0; // 進口金額
		long exchgExpRec = 0; // 出口筆數
		long exchgExpAmt = 0; // 出口金額
		long exchgOutRec = 0; // 匯出筆數
		long exchgOutAmt = 0; // 匯出金額
		long exchgInRec = 0; // 匯入筆數
		long exchgInAmt = 0; // 匯入金額
		long derOption = 0; // 選擇權
		long derRateExchg = 0; // 利率交換
		long derCCS = 0; // 換匯換利
		String derDraft = "0"; // 遠匯
		long derSWAP = 0; // 遠匯(含SWAP)
		long trustBond = 0; // 國內外基金債券
		long trustFund = 0; // 基金保管
		long trustSetAcct = 0; // 集管
		String trustSecurities = "0"; // 有價證券信託
		String trustREITs = "0"; // 不動產信託
		String trustWelDep = "0"; // 福儲信託
		long trustOther = 0; // 其他信託
		long wealthTrust = 0; // 信託
		long wealthInsCom = 0; // 保險佣金
		long wealthInvest = 0; // 雙元投資
		long salaryRec = 0; // 薪轉戶數
		long salaryFixed = 0; // 定期定額戶數
		long salaryMortgage = 0;// 房貸戶數
		long salaryConsumption = 0; // 消貸戶數
		long salaryCard = 0; // 信用卡持卡人數
		long salaryNetwork = 0; // 個人網銀戶數
		long cardCommercial = 0;// 商務卡
		long cardNoneCommercial = 0; // 非商務卡
		long GEBTWDRec = 0; // 台幣交易筆數
		long GEBOTHRec = 0; // 外幣交易筆數
		long GEBLCRec = 0; // 信用狀交易筆數
		String profit = "0"; // 利潤貢獻度
		String depTime1 = "0"; // 活期存款
		String depFixed1 = "0"; // 定期存款
		String loanQuota1 = "0";// 額度
		String loanAvgBal1 = "0"; // 平均餘額
		String loanAvgRate1 = "0"; // 平均動用率
		String exchgImpRec1 = "0"; // 進口筆數
		String exchgImpAmt1 = "0"; // 進口金額
		String exchgExpRec1 = "0"; // 出口筆數
		String exchgExpAmt1 = "0"; // 出口金額
		String exchgOutRec1 = "0"; // 匯出筆數
		String exchgOutAmt1 = "0"; // 匯出金額
		String exchgInRec1 = "0"; // 匯入筆數
		String exchgInAmt1 = "0"; // 匯入金額
		String derOption1 = "0"; // 選擇權
		String derRateExchg1 = "0"; // 利率交換
		String derCCS1 = "0"; // 換匯換利
		String derDraft1 = "0"; // 遠匯
		String derSWAP1 = "0"; // 遠匯(含SWAP)
		String trustBond1 = "0";// 國內外基金債券
		String trustFund1 = "0";// 基金保管
		String trustSetAcct1 = "0"; // 集管
		String trustSecurities1 = "0"; // 有價證券信託
		String trustREITs1 = "0"; // 不動產信託
		String trustWelDep1 = "0"; // 福儲信託
		String trustOther1 = "0"; // 其他信託
		String wealthTrust1 = "0"; // 信託
		String wealthInsCom1 = "0"; // 保險佣金
		String wealthInvest1 = "0"; // 雙元投資
		String salaryRec1 = "0"; // 薪轉戶數
		String salaryFixed1 = "0"; // 定期定額戶數
		String salaryMortgage1 = "0"; // 房貸戶數
		String salaryConsumption1 = "0"; // 消貸戶數
		String salaryCard1 = "0"; // 信用卡持卡人數
		String salaryNetwork1 = "0"; // 個人網銀戶數
		String cardCommercial1 = "0"; // 商務卡
		String cardNoneCommercial1 = "0"; // 非商務卡
		String cardCoBranded1 = "N"; // 聯名卡
		String GEBTWDRec1 = "0"; // 台幣交易筆數
		String GEBOTHRec1 = "0"; // 外幣交易筆數
		String GEBLCRec1 = "0"; // 信用狀交易筆數
		String profit1 = "0"; // 利潤貢獻度
		String profitSalary1 = "0"; // 薪轉戶貢獻度

		String IN_CC_CC_ACT = "";
		String BR_CD = "";

		// M-104-0172-001 二維表收信新增AR買方額度餘額
		String IN_LN_FA_B = "0";
		String IN_LN_FA_S = "0";
		long in_ln_fa_b = 0;
		long in_ln_fa_s = 0;
		String IN_LN_FACT_AMT_FA_S = "0";
		String IN_LN_FACT_AMT_FA_B = "0";
		long in_ln_fact_amt_fa_b = 0;
		long in_ln_fact_amt_fa_s = 0;

		double bal = 0;

		BigDecimal eloanFactAmtIncrease = BigDecimal.ZERO; // 新作增額額度

		// J-112-0281_05097_B1001 Web
		// e-Loan簽報書BIS評估表調整海外分行存款貢獻度之年化處理及非授信貢獻度為負時應顯示之警語
		Map<String, Object> negativeMap = new HashMap<String, Object>();

		if (listL120s04a.isEmpty()) {
			// L120S25A.errMsg03= 必須先執行【引進各關係戶往來彙總】才能執行本功能！
			Properties pop = MessageBundleScriptCreator
					.getComponentResource(LMSCommomPage.class);
			throw new CapMessageException(RespMsgHelper.getMessage(
					"EFD0015", pop.getProperty("L1205S07.error14")), getClass());
		} else {

			// kind 說明:
			// 1:借款人
			// 2:關係企業 ==> J-112-0044 配合kind A 停用
			// 3.關係企業+借款人 ==> J-112-0044 配合kind A 2023/02/10 改為 "借款人+集團企業"
			// A.個體+集團　　2023/01/18 風控處改個體(借款人)；集團(借款人+集團企業)

			// 組成查詢名單
			if (Util.equals(kind, "1")) {
				// 查詢主借戶時要以合理性分細表的借戶統編來查，非以簽報書主借款人統編
				String tFullId = Util.trim(qCustId) + "-" + Util.trim(qDupNo);
				json.put(tFullId, tFullId);

			} else {
				for (L120S04A model : listL120s04a) {
					String custRel = Util.trim(model.getCustRelation());
					String custId = Util.trim(model.getCustId());
					String dupNo = Util.trim(model.getDupNo());
					StringBuilder sbFullId = new StringBuilder();

					// ***************************************************************************
					// 風控處反映[上午11:36]
					// 楊梅(風險控管處,專員)，集團B.近一年平均授信餘額(TWD仟元)，因為集團內含了借款人，所以再加上個體會有重複計算個體的問題
					// 11529802 會因為重複序號 0 與空白，JSON當成兩個KEY，所以算了兩次
					// {"11529802 ":"春11529802","A114677270 ":"A114677270","A115995286 ":"A115995286","A117201901 ":"A117201901","A117250913 ":"A117250913","A117664480 ":"A117664480","11175358 ":"永11529802","12136930 ":"春11529802","12292592 ":"東12292592","16088255 ":"春11529802","16638126 ":"16638126","16747834 ":"16747834","23447209 ":"七23447209","34008635 ":"新11529802","115298020":"115298020"}
					// ***************************************************************************

					// sbFullId.append(Util.trim(model.getCustId())).append(
					// Util.trim(dupNo).equals("0") ? " " : Util
					// .trim(dupNo));

					sbFullId.append(custId).append("-").append(dupNo);

					if (!custRel.contains("3") && !custRel.contains("4")) {
						if (Util.equals(kind, "2") || Util.equals(kind, "3")) {
							// 2:關係企業 ==> 停用
							// 3.關係企業+借款人 ==> 2023/02/10 改為 "借款人+集團企業"
							// custRel 從 6 改 5
							if (custRel.contains("5")) {
								json.put(sbFullId.toString(),
										Util.trim(model.getCustName()));
							}
						}
					}
				}
			}

			if (Util.equals(kind, "3")) {
				// ***************************************************************************
				// 風控處反映[上午11:36]
				// 楊梅(風險控管處,專員)，集團B.近一年平均授信餘額(TWD仟元)，因為集團內含了借款人，所以再加上個體會有重複計算個體的問題
				// 11529802 會因為重複序號 0 與空白，JSON當成兩個KEY，所以算了兩次
				// {"11529802 ":"春11529802","A114677270 ":"A114677270","A115995286 ":"A115995286","A117201901 ":"A117201901","A117250913 ":"A117250913","A117664480 ":"A117664480","11175358 ":"永11529802","12136930 ":"春11529802","12292592 ":"東12292592","16088255 ":"春11529802","16638126 ":"16638126","16747834 ":"16747834","23447209 ":"七23447209","34008635 ":"新11529802","115298020":"115298020"}
				// ***************************************************************************
				// [上午11:36] 楊梅(風險控管處,專員)
				// 黃高專您好~
				// 我發現陳宜群專員了抓的貢獻度數字~就萬海航運一間
				// 跟業務往來會總表的差異為716仟元~剛好是信用卡貢獻度的數字~
				// 所以想請問您~最終採用的貢獻度數字，除了CUBCPCM~還有什麼呢? 謝謝
				// 信用卡貢獻度-萬海.pdf

				// 理論上該筆ID已於處理集團時包含在json了，這邊只是保險
				String tFullId = Util.trim(qCustId) + "-" + Util.trim(qDupNo);
				json.put(tFullId, tFullId);
			}

			// 初始合計--所有查詢人的合計******************************
			DecimalFormat df = new DecimalFormat(
					"###,###,###,###,###,###,###,##0");
			BigDecimal totalNoneLoanProfit = BigDecimal.ZERO; // 一年內非授信

			BigDecimal totalAttrOTSBal = BigDecimal.ZERO; // 貢獻度-國內
			BigDecimal totalAttrOVSBal = BigDecimal.ZERO; // 貢獻度-海外
			BigDecimal totalLoanBal = BigDecimal.ZERO; // 授信餘額
			BigDecimal totalDepTime = BigDecimal.ZERO; // 存款-活期存款
			BigDecimal totalDepFixed = BigDecimal.ZERO; // 存款-定期存款
			// 外匯-進口
			BigDecimal totalExchgImpRec = BigDecimal.ZERO;
			BigDecimal totalExchgImpAmt = BigDecimal.ZERO;
			// 外匯-出口
			BigDecimal totalExchgExpRec = BigDecimal.ZERO;
			BigDecimal totalExchgExpAmt = BigDecimal.ZERO;
			// 外匯-匯出
			BigDecimal totalExchgOutRec = BigDecimal.ZERO;
			BigDecimal totalExchgOutAmt = BigDecimal.ZERO;
			// 外匯-匯入
			BigDecimal totalExchgInRec = BigDecimal.ZERO;
			BigDecimal totalExchgInAmt = BigDecimal.ZERO;

			BigDecimal totalWealthTrust = BigDecimal.ZERO; // 財富管理-信託
			BigDecimal totalWealthInsCom = BigDecimal.ZERO; // 財富管理-保險佣金
			BigDecimal totalWealthInvest = BigDecimal.ZERO; // 財富管理-雙元投資

			// a Method
			if (!json.isEmpty()) {
				for (Object jsonKey : json.keySet()) {
					// 初始合計--各別查詢人員的資料合計 BY 人
					// INITIAL 資料
					loanQuota = 0;
					trustFund = 0;
					trustSetAcct = 0;
					trustBond = 0;
					trustOther = 0;
					salaryRec = 0;
					salaryFixed = 0;
					salaryMortgage = 0;
					salaryConsumption = 0;
					salaryCard = 0;
					salaryNetwork = 0;
					loanAvgRate = new BigDecimal("0");
					depTime = 0;
					depFixed = 0;
					loanAvgBal = 0;
					exchgImpAmt = 0;
					exchgExpAmt = 0;
					exchgImpRec = 0;
					exchgExpRec = 0;
					exchgInAmt = 0;
					exchgOutAmt = 0;
					exchgOutRec = 0;
					exchgInRec = 0;
					derOption = 0;
					derRateExchg = 0;
					derCCS = 0;
					derSWAP = 0;
					wealthTrust = 0;
					wealthInsCom = 0;
					wealthInvest = 0;
					cardCommercial = 0;
					cardNoneCommercial = 0;
					GEBTWDRec = 0;
					GEBOTHRec = 0;
					GEBLCRec = 0;

					// M-104-0172-001 二維表收信新增AR買方額度餘額
					in_ln_fa_b = 0;
					in_ln_fa_s = 0;
					in_ln_fact_amt_fa_b = 0;
					in_ln_fact_amt_fa_s = 0;

					String sJsonKey = (String) jsonKey;
					String jsonVal = Util.trim(json.get(sJsonKey));

					// / 風控處反映[上午11:36]
					// 楊梅(風險控管處,專員)，集團B.近一年平均授信餘額(TWD仟元)，因為集團內含了借款人，所以再加上個體會有重複計算個體的問題

					// 擷取 "+tID+" "+ xr + " 與本行往來實績資料
					// tID = (Util.isEmpty(sJsonKey)) ? null :
					// sJsonKey.substring(
					// 0, sJsonKey.length() - 1);
					// tDUPNO = (Util.isEmpty(sJsonKey)) ? null : sJsonKey
					// .substring(sJsonKey.length() - 1);

					tID = sJsonKey.split("-")[0];
					tDUPNO = sJsonKey.split("-")[1];

					// if (Util.equals(Util.trim(tID), "11529802")) {
					// System.out.println(tID + "-" + tDUPNO);
					// }

					// 實績貢獻
					// BGN*****************************************************************************************
					// 一年內非授信 loan attrOTSBal

					// ********************國內*********************************************************

					List<Map<String, Object>> noneLoanTw = this.dwdbService
							.findDW_DM_CUBCPCM_TOTAL_ATTRIBUTE_None_Loan(
									tID,
									tDUPNO,
									CapDate.formatDate(dQueryDate12S,
											UtilConstants.DateFormat.YYYY_MM_DD),
									CapDate.formatDate(dQueryDateE,
											UtilConstants.DateFormat.YYYY_MM_DD));

					Map<String, String> cycMn_tw = new HashMap<String, String>();
					BigDecimal noneLoanProfitTw = BigDecimal.ZERO;
					for (Map<String, Object> noneLoan : noneLoanTw) {

						String CYN_NN = Util.trim(MapUtils.getString(noneLoan,
								"CYC_MN"));
						if (!cycMn_tw.containsKey(CYN_NN)) {
							cycMn_tw.put(CYN_NN, CYN_NN);
						}

						bal = Util.parseDouble(Util.trim(noneLoan
								.get("TOTAL_ATTRIBUTE")));

						noneLoanProfitTw = noneLoanProfitTw.add(BigDecimal
								.valueOf(bal));
					}

					if (cycMn_tw.size() > 0 && cycMn_tw.size() < 12) {
						// 未滿一年要年化
						noneLoanProfitTw = noneLoanProfitTw.divide(
								new BigDecimal(cycMn_tw.size()), 2,
								RoundingMode.HALF_UP).multiply(
								new BigDecimal(12));
					}

					// ********************海外*********************************************************
					// 一年內授信 loan rows8
					List<Map<String, Object>> noneLoanOv = this.dwdbService
							.findDW_DM_CUBCPCMOVS_TOTAL_ATTRIBUTE_None_Loan(
									tID,
									tDUPNO,
									CapDate.formatDate(dQueryDate12S,
											UtilConstants.DateFormat.YYYY_MM_DD),
									CapDate.formatDate(dQueryDateE,
											UtilConstants.DateFormat.YYYY_MM_DD));

					Map<String, String> cycMn_ov = new HashMap<String, String>();
					BigDecimal noneLoanProfitOv = BigDecimal.ZERO;
					for (Map<String, Object> noneLoan : noneLoanOv) {

						String CYN_NN = Util.trim(MapUtils.getString(noneLoan,
								"CYC_MN"));
						if (!cycMn_ov.containsKey(CYN_NN)) {
							cycMn_ov.put(CYN_NN, CYN_NN);
						}

						double seaBal = Util.parseDouble(Util.trim(noneLoan
								.get("TOTAL_ATTRIBUTE")));

						String curr = Util.trim(noneLoan.get("CURR"));
						if (seaBal != 0) {
							seaBal = branchRate.toTWDAmt(
									(Util.isEmpty(curr)) ? "TWD" : curr,
									LMSUtil.toBigDecimal(seaBal)).doubleValue();
						}

						noneLoanProfitOv = noneLoanProfitOv.add(BigDecimal
								.valueOf(seaBal));

					}

					if (cycMn_ov.size() > 0 && cycMn_ov.size() < 12) {
						// 未滿一年要年化
						noneLoanProfitOv = noneLoanProfitOv.divide(
								new BigDecimal(cycMn_ov.size()), 2,
								RoundingMode.HALF_UP).multiply(
								new BigDecimal(12));
					}

					// ***************************非授信國內+海外
					// *******************************
					totalNoneLoanProfit = totalNoneLoanProfit.add(
							noneLoanProfitTw).add(noneLoanProfitOv);

					// 海外貢獻度(存款)*************************************************************
					Map mapAs400 = null;
					if (meta != null) {
						String typCd = Util.trim(meta.getTypCd());
						if (UtilConstants.Casedoc.typCd.海外.equals(typCd)) {
							try {
								String elf003SDate = CapDate.formatDate(
										dQueryDate12S, "yyyyMM");
								String elf003EDate = CapDate.formatDate(
										dQueryDateE, "yyyyMM");
								mapAs400 = misELF003Service
										.findELF003ProfitContributeByIdDate(
												Util.trim(tID),
												Util.trim(tDUPNO),
												Util.trim(meta.getCaseBrId()),
												elf003SDate, elf003EDate);
							} catch (GWException gw) {
								Properties pop = MessageBundleScriptCreator
										.getComponentResource(LMSCommomPage.class);
								throw new CapMessageException(
										RespMsgHelper.getMessage("EFD0015",
												MessageFormat.format(
														pop.getProperty("L1205S07.error24"),
														tID, tDUPNO)),
										getClass());
							} catch (Exception e) {
								Properties pop = MessageBundleScriptCreator
										.getComponentResource(LMSCommomPage.class);
								throw new CapMessageException(
										RespMsgHelper.getMessage("EFD0015",
												MessageFormat.format(
														pop.getProperty("L1205S07.error24"),
														tID, tDUPNO)),
										getClass());
							}
						}
					}

					BigDecimal exchangeRate = null;
					if (mapAs400 != null && !mapAs400.isEmpty()) {

						String tmpStr = MapUtils.getString(mapAs400,
								"ELF003_LOC_CURR");

						BigDecimal ELF003_YYMM_COUNT = Util
								.parseBigDecimal(MapUtils.getString(mapAs400,
										"ELF003_YYMM_COUNT"));

						if (!CapString.isEmpty(tmpStr)) {
							exchangeRate = branchRate.toTWDRate(tmpStr);
						} else {
							exchangeRate = BigDecimal.ONE;
						}
						tmpStr = MapUtils
								.getString(mapAs400, "ELF003_AMT", "0");
						BigDecimal tmpBD = new BigDecimal(tmpStr);

						if (ELF003_YYMM_COUNT.compareTo(BigDecimal.ZERO) > 0
								&& ELF003_YYMM_COUNT.compareTo(new BigDecimal(
										12)) < 0) {
							// 未滿一年要年化
							tmpBD = tmpBD.divide(ELF003_YYMM_COUNT, 2,
									RoundingMode.HALF_UP).multiply(
									new BigDecimal(12));
						}

						// J-112-0281_05097_B1001 Web
						// e-Loan簽報書BIS評估表調整海外分行存款貢獻度之年化處理及非授信貢獻度為負時應顯示之警語
						String negativeStr = MapUtils.getString(mapAs400,
								"ELF003_LAST_HAS_NEGATIVE");
						if (Util.equals(negativeStr, "Y")) {
							String negativeKey = Util.trim(tID)
									+ "-"
									+ (Util.equals(Util.trim(tDUPNO), "") ? "0"
											: tDUPNO);
							negativeMap.put(negativeKey, negativeKey);
						}

						totalNoneLoanProfit = totalNoneLoanProfit.add(tmpBD
								.multiply(exchangeRate));

					}

					// 貢獻度END*****************************************************************************************

					// 往來明細BGN*****************************************************************************************
					List<?> rows6 = this.dwdbService
							.findDW_MD_CUPFM_OTS_selCYC_MN(tID, tDUPNO, CapDate
									.formatDate(dQueryDate12S, "yyyy-MM-dd"),
									CapDate.formatDate(dQueryDateE,
											"yyyy-MM-dd"));
					Iterator<?> it6 = rows6.iterator();

					// 聯名卡
					String cardCoBranded = "N";
					while (it6.hasNext()) {
						Map<?, ?> dataMap6 = (Map<?, ?>) it6.next();
						Date CYC_MN = CapDate.parseDate(String.valueOf(dataMap6
								.get("CYC_MN")));

						String date = CapDate.formatDate(CYC_MN, "yyyy-MM-dd");
						dataMap6.get("CUST_KEY");
						BR_CD = Util.nullToSpace(dataMap6.get("BR_CD")); // 999代表全行

						depTime1 = Util.nullToSpace(dataMap6.get("IN_DP"));
						depFixed1 = Util.nullToSpace(dataMap6.get("IN_CT"));
						dataMap6.get("IN_DP_G");

						loanAvgBal1 = Util.nullToSpace(dataMap6
								.get("IN_LN_USE"));
						loanAvgRate1 = Util.nullToSpace(dataMap6
								.get("IN_LN_AVGRT"));
						loanQuota1 = Util.nullToSpace(dataMap6
								.get("IN_LN_FACT_AMT"));
						exchgImpAmt1 = Util.nullToSpace(dataMap6.get("IN_IM"));
						exchgExpAmt1 = Util.nullToSpace(dataMap6
								.get("IN_EX_BP"));
						exchgImpRec1 = Util.nullToSpace(dataMap6
								.get("IN_IM_TXN"));
						exchgExpRec1 = Util.nullToSpace(dataMap6
								.get("IN_EX_TXN"));
						exchgOutAmt1 = Util.nullToSpace(dataMap6.get("IN_OR"));
						exchgInAmt1 = Util.nullToSpace(dataMap6.get("IN_IR"));
						exchgOutRec1 = Util.nullToSpace(dataMap6
								.get("IN_OR_TXN"));
						exchgInRec1 = Util.nullToSpace(dataMap6
								.get("IN_IR_TXN"));
						derOption1 = Util.nullToSpace(dataMap6.get("IN_DV_OP"));
						derRateExchg1 = Util.nullToSpace(dataMap6
								.get("IN_DV_RE"));
						derCCS1 = Util.nullToSpace(dataMap6.get("IN_DV_ER"));
						derSWAP1 = Util.nullToSpace(dataMap6.get("IN_DV_FR"));
						wealthTrust1 = Util.nullToSpace(dataMap6
								.get("IN_WM_F_FEE"));
						wealthInsCom1 = Util.nullToSpace(dataMap6
								.get("IN_WM_I_FEE"));
						wealthInvest1 = Util.nullToSpace(dataMap6
								.get("IN_WM_S_FEE"));
						trustFund1 = Util.nullToSpace(dataMap6.get("IN_TR_FU"));
						trustSetAcct1 = Util.nullToSpace(dataMap6
								.get("IN_TR_CF"));
						trustBond1 = Util.nullToSpace(dataMap6.get("IN_TR_SC"));
						trustOther1 = Util.nullToSpace(dataMap6
								.get("IN_TR_OTS"));
						cardCommercial1 = Util.nullToSpace(dataMap6
								.get("IN_CC_CC"));
						cardNoneCommercial1 = Util.nullToSpace(dataMap6
								.get("IN_CC_IV"));
						IN_CC_CC_ACT = Util.nullToSpace(dataMap6
								.get("IN_CC_CC_ACT"));
						cardCoBranded1 = Util.nullToSpace(dataMap6
								.get("IN_CC_JC_ACT"));
						salaryRec1 = Util.nullToSpace(dataMap6.get("IN_ST"));
						salaryFixed1 = Util.nullToSpace(dataMap6
								.get("IN_ST_FD"));
						salaryMortgage1 = Util.nullToSpace(dataMap6
								.get("IN_ST_LN_1"));
						salaryConsumption1 = Util.nullToSpace(dataMap6
								.get("IN_ST_LN_2"));
						salaryCard1 = Util
								.nullToSpace(dataMap6.get("IN_ST_CC"));
						salaryNetwork1 = Util.nullToSpace(dataMap6
								.get("IN_ST_NB"));
						GEBTWDRec1 = Util.nullToSpace(dataMap6
								.get("IN_GEB_NTD_TXN"));
						GEBOTHRec1 = Util.nullToSpace(dataMap6
								.get("IN_GEB_NTD_N_TXN"));
						GEBLCRec1 = Util.nullToSpace(dataMap6
								.get("IN_GEB_LC_TXN"));

						// M-104-0172-001 二維表收信新增AR買方額度餘額
						// 應收帳款無追索買方承購平均餘額(IN_LN_FA_B)
						IN_LN_FA_B = Util.nullToSpace(dataMap6
								.get("IN_LN_FA_B"));
						// 應收帳款無追索權賣方融資平均餘額(IN_LN_FA_S)
						IN_LN_FA_S = Util.nullToSpace(dataMap6
								.get("IN_LN_FA_S"));
						// 授信-應收帳款買方有效額度(等值台幣)(資料來源LNF02P)
						IN_LN_FACT_AMT_FA_B = Util.nullToSpace(dataMap6
								.get("IN_LN_FACT_AMT_FA_B"));
						// 授信-應收帳款賣方有效額度(等值台幣)(資料來源LNF02P)
						IN_LN_FACT_AMT_FA_S = Util.nullToSpace(dataMap6
								.get("IN_LN_FACT_AMT_FA_S"));

						if (date.equals(queryDateE)) {
							loanQuota += Util.parseLong(loanQuota1);
							trustFund += Util.parseLong(trustFund1);
							trustSetAcct += Util.parseLong(trustSetAcct1);
							trustBond += Util.parseLong(trustBond1);
							trustOther += Util.parseLong(trustOther1);

							salaryRec += Util.parseLong(salaryRec1);
							salaryFixed += Util.parseLong(salaryFixed1);
							salaryMortgage += Util.parseLong(salaryMortgage1);
							salaryConsumption += Util
									.parseLong(salaryConsumption1);
							salaryCard += Util.parseLong(salaryCard1);
							salaryNetwork += Util.parseLong(salaryNetwork1);

							if ("Y".equals(cardCoBranded1)) {
								cardCoBranded = "Y";
							} else {
								cardCoBranded = "N";
							}

							// M-104-0172-001 二維表收信新增AR買方額度餘額
							// 授信-應收帳款買方有效額度(等值台幣)(資料來源LNF02P)
							in_ln_fact_amt_fa_b += Util
									.parseLong(IN_LN_FACT_AMT_FA_B);
							// 授信-應收帳款賣方有效額度(等值台幣)(資料來源LNF02P)
							in_ln_fact_amt_fa_s += Util
									.parseLong(IN_LN_FACT_AMT_FA_S);
						}
						if ("999".equals(BR_CD)) {
							// 全行、用來計算平均動用率
							loanAvgRate = loanAvgRate.add(new BigDecimal(
									loanAvgRate1));
						}
						depTime += Util.parseLong(depTime1);
						depFixed += Util.parseLong(depFixed1);
						loanAvgBal += Util.parseLong(loanAvgBal1);
						exchgImpAmt += Util.parseLong(exchgImpAmt1);
						exchgExpAmt += Util.parseLong(exchgExpAmt1);
						exchgImpRec += Util.parseLong(exchgImpRec1);
						exchgExpRec += Util.parseLong(exchgExpRec1);
						exchgOutAmt += Util.parseLong(exchgOutAmt1);
						exchgInAmt += Util.parseLong(exchgInAmt1);
						exchgOutRec += Util.parseLong(exchgOutRec1);
						exchgInRec += Util.parseLong(exchgInRec1);
						derOption += Util.parseLong(derOption1);
						derRateExchg += Util.parseLong(derRateExchg1);
						derCCS += Util.parseLong(derCCS1);
						derSWAP += Util.parseLong(derSWAP1);
						wealthTrust += Util.parseLong(wealthTrust1);
						wealthInsCom += Util.parseLong(wealthInsCom1);
						wealthInvest += Util.parseLong(wealthInvest1);
						cardCommercial += Util.parseLong(cardCommercial1);
						cardNoneCommercial += Util
								.parseLong(cardNoneCommercial1);
						GEBTWDRec += Util.parseLong(GEBTWDRec1);
						GEBOTHRec += Util.parseLong(GEBOTHRec1);
						GEBLCRec += Util.parseLong(GEBLCRec1);
						// M-104-0172-001 二維表收信新增AR買方額度餘額
						// 應收帳款無追索買方承購平均餘額(IN_LN_FA_B)
						in_ln_fa_b += Util.parseLong(IN_LN_FA_B);
						// 應收帳款無追索權賣方融資平均餘額(IN_LN_FA_S)
						in_ln_fa_s += Util.parseLong(IN_LN_FA_S);
					}

					// 算月平均
					long avgDeptTime = (long) Math
							.round((depTime / (double) 6 / 1000));
					long avgDeptFixed = (long) Math.round((depFixed
							/ (double) 6 / 1000));

					// 授信月平均
					long avgLoanAvgBal = (long) Math
							.round((loanAvgBal / (double) 12));

					// 存款-活期存款
					totalDepTime = totalDepTime
							.add(new BigDecimal(avgDeptTime));
					// 存款-定期存款
					totalDepFixed = totalDepFixed.add(new BigDecimal(
							avgDeptFixed));

					// 授信月平均
					totalLoanBal = totalLoanBal.add(new BigDecimal(
							avgLoanAvgBal));

					// 外匯-進口
					totalExchgImpRec = totalExchgImpRec.add(new BigDecimal(
							exchgImpRec));
					totalExchgImpAmt = totalExchgImpAmt.add(new BigDecimal(
							(long) Math.round((double) exchgImpAmt / 1000)));
					// 外匯-出口
					totalExchgExpRec = totalExchgExpRec.add(new BigDecimal(
							exchgExpRec));
					totalExchgExpAmt = totalExchgExpAmt.add(new BigDecimal(
							(long) Math.round((double) exchgExpAmt / 1000)));
					// 外匯-匯出
					totalExchgOutRec = totalExchgOutRec.add(new BigDecimal(
							exchgOutRec));
					totalExchgOutAmt = totalExchgOutAmt.add(new BigDecimal(
							(long) Math.round((double) exchgOutAmt / 1000)));
					// 外匯-匯入
					totalExchgInRec = totalExchgInRec.add(new BigDecimal(
							exchgInRec));
					totalExchgInAmt = totalExchgInAmt.add(new BigDecimal(
							(long) Math.round((double) exchgInAmt / 1000)));

					// 財富管理-信託
					totalWealthTrust = totalWealthTrust.add(new BigDecimal(
							(long) Math.round((double) wealthTrust / 1000)));
					// 財富管理-保險佣金
					totalWealthInsCom = totalWealthInsCom.add(new BigDecimal(
							(long) Math.round((double) wealthInsCom / 1000)));
					// 財富管理-雙元投資
					totalWealthInvest = totalWealthInvest.add(new BigDecimal(
							(long) Math.round((double) wealthInvest / 1000)));

					// 往來明細END*****************************************************************************************

					// ELOAN新作增額額度BGN*****************************************************************************************

					List<L140M01A> listL140m01a = l140m01aDao
							.findByMainIdAndCustId(mainId, tID, tDUPNO,
									UtilConstants.Cntrdoc.ItemType.額度明細表);
					for (L140M01A l140m01a : listL140m01a) {
						if (Util.equals(l140m01a.getChkYN(), "Y")) {
							String incApplyTotCurr = l140m01a
									.getIncApplyTotCurr(); // 新作、增額合計幣別
							BigDecimal incApplyTotAmt = l140m01a
									.getIncApplyTotAmt(); // 新作、增額合計金額
							if (Util.notEquals(incApplyTotCurr, "")
									&& Util.notEquals(incApplyTotCurr, "TWD")) {
								incApplyTotAmt = branchRate.toTWDAmt(
										incApplyTotCurr, incApplyTotAmt);
							}

							eloanFactAmtIncrease = eloanFactAmtIncrease
									.add(incApplyTotAmt);

						} else {
							Properties pop = MessageBundleScriptCreator
									.getComponentResource(LMSCommomPage.class);
							// L1205S07.error28 = 借款人{0}額度明細表請先完成授信額度合計
							throw new CapMessageException(
									RespMsgHelper.getMessage("EFD0015",
											MessageFormat.format(
													pop.getProperty("L1205S07.error28"),
													tID)), getClass());
						}
						break;
					}

					// ELOAN新作增額額度END*****************************************************************************************

				} // for (Object jsonKey : json.keySet()) {

				if (isKindA) {
					if (Util.equals(kind, "1")) {
						// 近一年非授信貢獻度
						jsonData.put("bisNoneLoanProfit", totalNoneLoanProfit
								.divide(BigDecimal.valueOf(1000), 0,
										RoundingMode.HALF_UP));
						// 近一年授信餘額
						jsonData.put("bisLoanBal", totalLoanBal.divide(
								BigDecimal.valueOf(1000), 0,
								RoundingMode.HALF_UP));

						// 新作增額額度
						jsonData.put("bisFactAmtIncrease", eloanFactAmtIncrease
								.divide(BigDecimal.valueOf(1000), 0,
										RoundingMode.HALF_UP));
					} else if (Util.equals(kind, "3")) {
						// 近一年非授信貢獻度
						jsonData.put("bisNoneLoanProfit_1", totalNoneLoanProfit
								.divide(BigDecimal.valueOf(1000), 0,
										RoundingMode.HALF_UP));
						// 近一年授信餘額
						jsonData.put("bisLoanBal_1", totalLoanBal.divide(
								BigDecimal.valueOf(1000), 0,
								RoundingMode.HALF_UP));

						// 新作增額額度
						jsonData.put("bisFactAmtIncrease_1",
								eloanFactAmtIncrease.divide(
										BigDecimal.valueOf(1000), 0,
										RoundingMode.HALF_UP));
					}
				} else {
					// 近一年非授信貢獻度
					jsonData.put("bisNoneLoanProfit", totalNoneLoanProfit
							.divide(BigDecimal.valueOf(1000), 0,
									RoundingMode.HALF_UP));
					// 近一年授信餘額
					jsonData.put("bisLoanBal", totalLoanBal.divide(
							BigDecimal.valueOf(1000), 0, RoundingMode.HALF_UP));

					// 新作增額額度
					jsonData.put("bisFactAmtIncrease", eloanFactAmtIncrease
							.divide(BigDecimal.valueOf(1000), 0,
									RoundingMode.HALF_UP));
					// 近一年非授信貢獻度_集團
					jsonData.put("bisNoneLoanProfit_1", BigDecimal.ZERO);
					// 近一年授信餘額_集團
					jsonData.put("bisLoanBal_1", BigDecimal.ZERO);
					// 新作增額額度_集團
					jsonData.put("bisFactAmtIncrease_1", BigDecimal.ZERO);
				}

				jsonData.put("dQueryDate6S",
						CapDate.formatDate(dQueryDate6S, "yyyy/MM"));
				jsonData.put("dQueryDate12S",
						CapDate.formatDate(dQueryDate12S, "yyyy/MM"));
				jsonData.put("dQueryDateE",
						CapDate.formatDate(dQueryDateE, "yyyy/MM"));

				BigDecimal mainGroupTotalAttr = totalAttrOTSBal
						.add(totalAttrOVSBal); // 國內+海外
				jsonData.put("totalAttr", df.format(mainGroupTotalAttr.divide(
						BigDecimal.valueOf(1000), 0, RoundingMode.HALF_UP)));

				jsonData.put("totalDepTime", df.format(totalDepTime));
				jsonData.put("totalDepFixed", df.format(totalDepFixed));
				jsonData.put("totalDep",
						df.format(totalDepTime.add(totalDepFixed)));

				jsonData.put("totalExchgImpRec", df.format(totalExchgImpRec));
				jsonData.put("totalExchgImpAmt", df.format(totalExchgImpAmt));
				jsonData.put("totalExchgExpRec", df.format(totalExchgExpRec));
				jsonData.put("totalExchgExpAmt", df.format(totalExchgExpAmt));
				jsonData.put("totalExchgOutRec", df.format(totalExchgOutRec));
				jsonData.put("totalExchgOutAmt", df.format(totalExchgOutAmt));
				jsonData.put("totalExchgInRec", df.format(totalExchgInRec));
				jsonData.put("totalExchgInAmt", df.format(totalExchgInAmt));

				// J-112-0281_05097_B1001 Web
				// e-Loan簽報書BIS評估表調整海外分行存款貢獻度之年化處理及非授信貢獻度為負時應顯示之警語
				StringBuffer negativeAttrbute = new StringBuffer("");
				if (negativeMap != null && !negativeMap.isEmpty()) {
					for (String nKey : negativeMap.keySet()) {
						negativeAttrbute
								.append((Util.notEquals(
										negativeAttrbute.toString(), "") ? "、"
										: "")).append(nKey);
					}
				}
				jsonData.put("negativeAttrbute", negativeAttrbute.toString());

			} else {
				// L120S08A.error15=關係戶於本行各項業務往來(二維表)無符合之查詢對象
				Properties pop = MessageBundleScriptCreator
						.getComponentResource(LMSCommomPage.class);
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0015", pop.getProperty("L120S08A.error15")),
						getClass());
			}// if (!json.isEmpty()) {
		} // if (listL120s04a.isEmpty()) {
			// return result;
	}

	/**
	 * J-112-0389_05097_B1001 Web e-Loan調整企金e-Loan之RORWA風險成本計算方式
	 * 
	 * 判斷BIS版本
	 * 
	 * @param l120s25a
	 * @param sign
	 * @param baseVersion
	 * @return
	 */
	@Override
	public boolean compareBisVersion(L120S25A l120s25a, String sign,
			BigDecimal baseVersion) {

		// 若沒有
		if (l120s25a.getBisVer1() == null || l120s25a.getBisVer2() == null) {
			return false;
		}

		String bisVer1 = l120s25a.getBisVer1().toString();
		String bisVer2 = l120s25a.getBisVer2().toString();

		BigDecimal bisVer = Util.parseBigDecimal(bisVer1 + "." + bisVer2);

		BigDecimal a = bisVer;
		BigDecimal b = baseVersion;

		if (sign.equals("<")) {
			return (a.compareTo(b) < 0);
		} else if (sign.equals("<=")) {
			return (a.compareTo(b) <= 0);
		} else if (sign.equals("==")) {
			return (a.compareTo(b) == 0);
		} else if (sign.equals(">=")) {
			return (a.compareTo(b) >= 0);
		} else if (sign.equals(">")) {
			return (a.compareTo(b) > 0);
		}
		return false;
	}

	/**
	 * J-111-0443_05097_B1005 Web e-Loan企金授信開發授信BIS評估表
	 * 
	 * @param listL120s25a
	 * @return
	 */
	@Override
	public boolean showBisTotalNotNa(List<L120S25A> listL120s25a) {
		boolean showBisTotalNotNa = false;

		if (listL120s25a != null && !listL120s25a.isEmpty()) {
			for (L120S25A l120s25a : listL120s25a) {

				// J-111-0443_05097_B1005 Web e-Loan企金授信開發授信BIS評估表
				BigDecimal rItemD = (l120s25a.getBisRItemD() == null ? BigDecimal.ZERO
						: l120s25a.getBisRItemD()); // %
				if (rItemD.compareTo(BigDecimal.ZERO) != 0) {
					// 若抵減後風險權數不納入
					showBisTotalNotNa = true;
				}
			}
		} else {
			showBisTotalNotNa = true;
		}

		return showBisTotalNotNa;
	}

}
