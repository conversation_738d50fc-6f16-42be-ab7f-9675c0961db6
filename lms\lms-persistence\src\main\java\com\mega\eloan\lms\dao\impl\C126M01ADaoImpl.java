/* 
 * C126M01ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.C126M01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C126M01A;

/** 房仲引介資料檔 **/
@Repository
public class C126M01ADaoImpl extends LMSJpaDao<C126M01A, String> 
	implements C126M01ADao {

	@Override
	public C126M01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C126M01A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		List<C126M01A> list = null;
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}
	
	@Override
	public C126M01A findUniqueBy(String custId, String dupNo, String ownBrid, String agntNo, String agntChain, String contractNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", ownBrid);
		search.addSearchModeParameters(SearchMode.EQUALS, "agntNo", agntNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "agntChain", agntChain);
		search.addSearchModeParameters(SearchMode.EQUALS, "contractNo", contractNo);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		return findUniqueOrNone(search);
	}
	
	@Override
	public List<C126M01A> findByCustIdAndDupNo(String custId, String dupNo) {
		
		ISearch search = createSearchTemplete();
		List<C126M01A> list = null;
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		
		return list;
	}
}