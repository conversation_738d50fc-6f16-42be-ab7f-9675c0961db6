/* 
 * C140M01ADao.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;
import tw.com.iisi.cap.model.GenericBean;

import com.mega.eloan.lms.model.C140SFFF;

/**
 * <pre>
 * 徵信調查報告書自由格式
 * </pre>
 * 
 * @since 2011/9/23
 * <AUTHOR>
 * @version <ul>
 *          <li>new
 *          </ul>
 */
public interface C140SFFFDao extends IGenericDao<C140SFFF> {
	C140SFFF findC140SFFF(String mainId,String pid,String fieldId,String tab);
	List<C140SFFF> findC140SFFF(String mainId,String pid,String tab);
	/**
	 * 刪除所有資料
	 * 
	 * @param meta GenericBean
	 * @return int
	 */
	int deleteByMeta(GenericBean meta);
}
