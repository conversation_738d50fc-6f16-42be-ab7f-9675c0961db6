package com.mega.eloan.lms.lrs.flow;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.flow.FlowInstance;

import com.mega.eloan.common.dao.CommonMetaDao;
import com.mega.eloan.common.flow.AbstractFlowHandler;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.model.L181M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**<pre>
 * 維護覆審控制檔-流程
 * </pre>
 * @since  2011/9/23
 * <AUTHOR>
 * @version <ul>
 *           <li>2011/9/23,irene,new
 *          </ul>
 */
@Component
public class LMS1815Flow extends AbstractFlowHandler {

	@Resource
	CommonMetaDao metaDao;
	
	@Transition(node="確認", value="核定")
	public void accept(FlowInstance instance) {
		String instanceId = instance.getId().toString();
		Meta meta = metaDao.findByOid(getDomainClass(), instanceId);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		meta.setApprover(user.getUserId());
		meta.setApproveTime(CapDate.getCurrentTimestamp());
		metaDao.save(meta);
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return L181M01A.class;
	}
	
	@SuppressWarnings("rawtypes")
	@Override
	public Class getDocStatusEnumClass() {
		return RetrialDocStatusEnum.class;
	}
}