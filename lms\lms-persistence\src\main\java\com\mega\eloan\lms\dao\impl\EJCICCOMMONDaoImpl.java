/* 
 * EJCICCOMMONDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.EJCICCOMMONDao;
import com.mega.eloan.lms.model.EJCICCOMMON;

/** 個金歡喜信貸徵審初審及iLoan EJ查詢檔 **/
@Repository
public class EJCICCOMMONDaoImpl extends LMSJpaDao<EJCICCOMMON, String>
	implements EJCICCOMMONDao {

	@Override
	public EJCICCOMMON findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<EJCICCOMMON> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<EJCICCOMMON> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public EJCICCOMMON findBy(String custId, String dupNo, String txid, String prodid, String qBranch, String qDate, String qEmpCode, String qFuncSrc){
		ISearch search = createSearchTemplete();
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (txid != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "txid", txid);
		if (prodid != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "prodid", prodid);
		if (qBranch != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "qBranch", qBranch);
		if (qDate != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "qDate", qDate);
		if (qEmpCode != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "qEmpCode", qEmpCode);
		if (qFuncSrc != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "qFuncSrc", qFuncSrc);
		
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			return findUniqueOrNone(search);
		}
		
		return null;
	}
	
	@Override
	public List<EJCICCOMMON> findBy(String mainId, String custId, String dupNo, String prodId, String txId){
		ISearch search = createSearchTemplete();
		List<EJCICCOMMON> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (prodId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "prodid", prodId);
		if (txId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "txid", txId);
		
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
	
	
	@Override
	public List<EJCICCOMMON> findByInquiryRecord(String custId, String prodId, String txId, String qDate, String qFuncSrc){
		
		ISearch search = createSearchTemplete();
		List<EJCICCOMMON> list = null;
		
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (prodId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "prodid", prodId);
		if (txId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "txid", txId);
		if (qDate != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "qDate", qDate);
		if (qFuncSrc != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "qFuncSrc", qFuncSrc);
		
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}

	
	@Override
	public List<EJCICCOMMON> findByJcicKey(String custId, String prodId, String txId, String qdate){
		ISearch search = createSearchTemplete();
		List<EJCICCOMMON> list = null;
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (prodId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "prodid", prodId);
		if (txId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "txid", txId);
		if (qdate != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "qDate", qdate);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
}