/* 
 * C126M01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 房仲引介資料檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C126M01A", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId"}))
public class C126M01A extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 申請人_郵遞區號 **/
    @Size(max=5)
    @Column(name="ZIP", length=5, columnDefinition="VARCHAR(5)")
    private String zip;

	/** 申請人_縣市 **/
    @Size(max=2)
    @Column(name="CITY", length=2, columnDefinition="VARCHAR(2)")
    private String city;

	/** 申請人_鄉鎮市區 **/
    @Size(max=3)
    @Column(name="DIST", length=3, columnDefinition="VARCHAR(3)")
    private String dist;

	/** 
	 * 申請人_里村名<p/>
	 * 前端只能輸入50個字(不管全半型)
	 */
	@Size(max=150)
	@Column(name="VILLAGENAME", length=150, columnDefinition="VARCHAR(150)")
	private String villageName;

	/** 
	 * 申請人_里村<p/>
	 * 1,里<br/>
	 *  2,村
	 */
	@Digits(integer=1, fraction=0, groups = Check.class)
	@Column(name="VILLAGE", columnDefinition="DECIMAL(1,0)")
	private Integer village;

	/** 申請人_鄰 **/
	@Digits(integer=4, fraction=0, groups = Check.class)
	@Column(name="NEIGHBORHOOD", columnDefinition="DECIMAL(4,0)")
	private Integer neighborhood;

	/** 
	 * 申請人_路/街名<p/>
	 * 前端只能輸入20個字(不管全半型)
	 */
	@Size(max=60)
	@Column(name="ROADNAME", length=60, columnDefinition="VARCHAR(60)")
	private String roadName;

	/** 
	 * 申請人_路<p/>
	 * 1,(空白)<br/>
	 *  2,路<br/>
	 *  3,街
	 */
	@Digits(integer=1, fraction=0, groups = Check.class)
	@Column(name="ROAD", columnDefinition="DECIMAL(1,0)")
	private Integer road;

	/** 
	 * 申請人_段<p/>
	 * 前端只能輸入4個字(不管全半型)
	 */
	@Size(max=12)
	@Column(name="SEC", length=12, columnDefinition="VARCHAR(12)")
	private String sec;

	/** 
	 * 申請人_巷<p/>
	 * 前端只能輸入8個字(不管全半型)
	 */
	@Size(max=24)
	@Column(name="LANE", length=24, columnDefinition="VARCHAR(24)")
	private String lane;

	/** 
	 * 申請人_弄<p/>
	 * 前端只能輸入8個字(不管全半型)
	 */
	@Size(max=24)
	@Column(name="ALLEY", length=24, columnDefinition="VARCHAR(24)")
	private String alley;

	/** 
	 * 申請人_號<p/>
	 * 前端只能輸入4個字(不管全半型)
	 */
	@Size(max=12)
	@Column(name="NO1", length=12, columnDefinition="VARCHAR(12)")
	private String no1;

	/** 
	 * 申請人_之號<p/>
	 * 前端只能輸入4個字(不管全半型)
	 */
	@Size(max=12)
	@Column(name="NO2", length=12, columnDefinition="VARCHAR(12)")
	private String no2;

	/** 
	 * 申請人_樓<p/>
	 * 前端只能輸入3個字(不管全半型)
	 */
	@Size(max=9)
	@Column(name="FLOOR1", length=9, columnDefinition="VARCHAR(9)")
	private String floor1;

	/** 
	 * 申請人_之樓<p/>
	 * 前端只能輸入3個字(不管全半型)
	 */
	@Size(max=9)
	@Column(name="FLOOR2", length=9, columnDefinition="VARCHAR(9)")
	private String floor2;

	/** 
	 * 申請人_室<p/>
	 * 前端只能輸入3個字(不管全半型)
	 */
	@Size(max=9)
	@Column(name="ROOM", length=9, columnDefinition="VARCHAR(9)")
	private String room;

	/** 房仲代號 **/
	@Size(max=5)
	@Column(name="AGNTNO", length=5, columnDefinition="VARCHAR(5)")
	private String agntNo;

	/** 房仲連鎖店類型 **/
	@Size(max=1)
	@Column(name="AGNTCHAIN", length=1, columnDefinition="CHAR(1)")
	private String agntChain;

	/** 收件日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="RECEIVEDATE", columnDefinition="DATE")
	private Date receiveDate;

	/** 買賣合約書簽約日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="CONTRACTDATE", columnDefinition="DATE")
	private Date contractDate;

	/** 買賣合約書編號 **/
	@Size(max=20)
	@Column(name="CONTRACTNO", length=20, columnDefinition="CHAR (20)")
	private String contractNo;

	/** 成交金額 **/
	@Digits(integer=16, fraction=2, groups = Check.class)
	@Column(name="TTLAMT", columnDefinition="DECIMAL(16,2)")
	private BigDecimal ttlAmt;

	/** 預貸金額 **/
	@Digits(integer=16, fraction=2, groups = Check.class)
	@Column(name="PRELOANAMT", columnDefinition="DECIMAL(16,2)")
	private BigDecimal preloanAmt;

	/** 申請金額 **/
	@Digits(integer=16, fraction=2, groups = Check.class)
	@Column(name="APPLYAMT", columnDefinition="DECIMAL(16,2)")
	private BigDecimal applyAmt;

	/** 撥款金額 **/
	@Digits(integer=16, fraction=2, groups = Check.class)
	@Column(name="LNAMT", columnDefinition="DECIMAL(16,2)")
	private BigDecimal lnAmt;

	/** 鑑價完成日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ESTDATE", columnDefinition="DATE")
	private Date estDate;

	/** 徵審完成日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="CREDITREVIEWDATE", columnDefinition="DATE")
	private Date creditReviewDate;

	/** 撥款日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="LNDATE", columnDefinition="DATE")
	private Date lnDate;

	/** 送件代書 **/
	@Size(max=20)
	@Column(name="ESSAYNAME", length=20, columnDefinition="CHAR(20)")
	private String essayName;

	/** 承作情形 **/
	@Size(max=1)
	@Column(name="STATFLAG", length=1, columnDefinition="CHAR(1)")
	private String statFlag;

	/** 是否填寫完畢 **/
	@Size(max=1)
	@Column(name="ISFINISHED", length=1, columnDefinition="CHAR(1)")
	private String isFinished;

	/** 利率方案 **/
	@Size(max=50)
	@Column(name="RATEKIND", length=50, columnDefinition="VARCHAR(50)")
	private String rateKind;

	/** 回饋金比率(%) **/
	@Digits(integer=7, fraction=5, groups = Check.class)
	@Column(name="REBATERATIO", columnDefinition="DECIMAL(7,5)")
	private BigDecimal rebateRatio;

	/** 回饋金 **/
	@Digits(integer=16, fraction=2, groups = Check.class)
	@Column(name="REBATE", columnDefinition="DECIMAL(16,2)")
	private BigDecimal rebate;
	
	/** 文件編號 */
	@Column(name="L140M01A_MAINID",length = 32, columnDefinition = "CHAR(32)")
	private String l140m01a_MainId;
	
	/** 引介房仲姓名 **/
    @Size(max=90)
    @Column(name="AGNTNAME", length=90, columnDefinition="VARCHAR(90)")
    private String agntName;
	
    /** 房仲證書(明)字號-年 **/
    @Size(max=3)
    @Column(name="LICENSEYEAR", length=3, columnDefinition="VARCHAR(3)")
    private String licenseYear;
    
    /** 房仲證書(明)字號-年登字 **/
    @Size(max=15)
    @Column(name="LICENSEWORD", length=15, columnDefinition="VARCHAR(15)")
    private String licenseWord;
    
	/** 房仲證書(明)字號-編號 **/
	@Size(max=6)
	@Column(name="LICENSENUMBER", length=6, columnDefinition="CHAR(6)")
	private String licenseNumber;
	
	/** 房仲證書(明)字號有效期限 **/
	@Temporal(TemporalType.DATE)
	@Column(name="LICENSEEXP", columnDefinition="DATE")
	private Date licenseExp;
    
	/** 任職經紀業名稱 **/
    @Size(max=90)
    @Column(name="AGNTCOMPANYNAME", length=2, columnDefinition="VARCHAR(90)")
    private String agntCompanyName;
    
    /** 任職營業處所名稱 **/
    @Size(max=90)
    @Column(name="AGNTBRANCHNAME", length=2, columnDefinition="VARCHAR(90)")
    private String agntBranchName;
	
	
	/** 取得申請人_郵遞區號 **/
    public String getZip() {
        return this.zip;
    }
	/** 設定申請人_郵遞區號 **/
    public void setZip(String value) {
        this.zip = value;
    }

	/** 取得申請人_縣市 **/
    public String getCity() {
        return this.city;
    }
	/** 設定申請人_縣市 **/
    public void setCity(String value) {
        this.city = value;
    }

	/** 取得申請人_鄉鎮市區 **/
    public String getDist() {
        return this.dist;
    }
	/** 設定申請人_鄉鎮市區 **/
    public void setDist(String value) {
        this.dist = value;
    }

	/** 
	 * 取得申請人_里村名<p/>
	 * 前端只能輸入50個字(不管全半型)
	 */
	public String getVillageName() {
		return this.villageName;
	}
	/**
	 *  設定申請人_里村名<p/>
	 *  前端只能輸入50個字(不管全半型)
	 **/
	public void setVillageName(String value) {
		this.villageName = value;
	}

	/** 
	 * 取得申請人_里村<p/>
	 * 1,里<br/>
	 *  2,村
	 */
	public Integer getVillage() {
		return this.village;
	}
	/**
	 *  設定申請人_里村<p/>
	 *  1,里<br/>
	 *  2,村
	 **/
	public void setVillage(Integer value) {
		this.village = value;
	}

	/** 取得申請人_鄰 **/
	public Integer getNeighborhood() {
		return this.neighborhood;
	}
	/** 設定申請人_鄰 **/
	public void setNeighborhood(Integer value) {
		this.neighborhood = value;
	}

	/** 
	 * 取得申請人_路/街名<p/>
	 * 前端只能輸入20個字(不管全半型)
	 */
	public String getRoadName() {
		return this.roadName;
	}
	/**
	 *  設定申請人_路/街名<p/>
	 *  前端只能輸入20個字(不管全半型)
	 **/
	public void setRoadName(String value) {
		this.roadName = value;
	}

	/** 
	 * 取得申請人_路<p/>
	 * 1,(空白)<br/>
	 *  2,路<br/>
	 *  3,街
	 */
	public Integer getRoad() {
		return this.road;
	}
	/**
	 *  設定申請人_路<p/>
	 *  1,(空白)<br/>
	 *  2,路<br/>
	 *  3,街
	 **/
	public void setRoad(Integer value) {
		this.road = value;
	}

	/** 
	 * 取得申請人_段<p/>
	 * 前端只能輸入4個字(不管全半型)
	 */
	public String getSec() {
		return this.sec;
	}
	/**
	 *  設定申請人_段<p/>
	 *  前端只能輸入4個字(不管全半型)
	 **/
	public void setSec(String value) {
		this.sec = value;
	}

	/** 
	 * 取得申請人_巷<p/>
	 * 前端只能輸入8個字(不管全半型)
	 */
	public String getLane() {
		return this.lane;
	}
	/**
	 *  設定申請人_巷<p/>
	 *  前端只能輸入8個字(不管全半型)
	 **/
	public void setLane(String value) {
		this.lane = value;
	}

	/** 
	 * 取得申請人_弄<p/>
	 * 前端只能輸入8個字(不管全半型)
	 */
	public String getAlley() {
		return this.alley;
	}
	/**
	 *  設定申請人_弄<p/>
	 *  前端只能輸入8個字(不管全半型)
	 **/
	public void setAlley(String value) {
		this.alley = value;
	}

	/** 
	 * 取得申請人_號<p/>
	 * 前端只能輸入4個字(不管全半型)
	 */
	public String getNo1() {
		return this.no1;
	}
	/**
	 *  設定申請人_號<p/>
	 *  前端只能輸入4個字(不管全半型)
	 **/
	public void setNo1(String value) {
		this.no1 = value;
	}

	/** 
	 * 取得申請人_之號<p/>
	 * 前端只能輸入4個字(不管全半型)
	 */
	public String getNo2() {
		return this.no2;
	}
	/**
	 *  設定申請人_之號<p/>
	 *  前端只能輸入4個字(不管全半型)
	 **/
	public void setNo2(String value) {
		this.no2 = value;
	}

	/** 
	 * 取得申請人_樓<p/>
	 * 前端只能輸入3個字(不管全半型)
	 */
	public String getFloor1() {
		return this.floor1;
	}
	/**
	 *  設定申請人_樓<p/>
	 *  前端只能輸入3個字(不管全半型)
	 **/
	public void setFloor1(String value) {
		this.floor1 = value;
	}

	/** 
	 * 取得申請人_之樓<p/>
	 * 前端只能輸入3個字(不管全半型)
	 */
	public String getFloor2() {
		return this.floor2;
	}
	/**
	 *  設定申請人_之樓<p/>
	 *  前端只能輸入3個字(不管全半型)
	 **/
	public void setFloor2(String value) {
		this.floor2 = value;
	}

	/** 
	 * 取得申請人_室<p/>
	 * 前端只能輸入3個字(不管全半型)
	 */
	public String getRoom() {
		return this.room;
	}
	/**
	 *  設定申請人_室<p/>
	 *  前端只能輸入3個字(不管全半型)
	 **/
	public void setRoom(String value) {
		this.room = value;
	}

	/** 取得房仲代號 **/
	public String getAgntNo() {
		return this.agntNo;
	}
	/** 設定房仲代號 **/
	public void setAgntNo(String value) {
		this.agntNo = value;
	}

	/** 取得房仲連鎖店類型 **/
	public String getAgntChain() {
		return this.agntChain;
	}
	/** 設定房仲連鎖店類型 **/
	public void setAgntChain(String value) {
		this.agntChain = value;
	}

	/** 取得收件日期 **/
	public Date getReceiveDate() {
		return this.receiveDate;
	}
	/** 設定收件日期 **/
	public void setReceiveDate(Date value) {
		this.receiveDate = value;
	}

	/** 取得買賣合約書簽約日期 **/
	public Date getContractDate() {
		return this.contractDate;
	}
	/** 設定買賣合約書簽約日期 **/
	public void setContractDate(Date value) {
		this.contractDate = value;
	}

	/** 取得買賣合約書編號 **/
	public String getContractNo() {
		return this.contractNo;
	}
	/** 設定買賣合約書編號 **/
	public void setContractNo(String value) {
		this.contractNo = value;
	}

	/** 取得成交金額 **/
	public BigDecimal getTtlAmt() {
		return this.ttlAmt;
	}
	/** 設定成交金額 **/
	public void setTtlAmt(BigDecimal value) {
		this.ttlAmt = value;
	}

	/** 取得預貸金額 **/
	public BigDecimal getPreloanAmt() {
		return this.preloanAmt;
	}
	/** 設定預貸金額 **/
	public void setPreloanAmt(BigDecimal value) {
		this.preloanAmt = value;
	}

	/** 取得申請金額 **/
	public BigDecimal getApplyAmt() {
		return this.applyAmt;
	}
	/** 設定申請金額 **/
	public void setApplyAmt(BigDecimal value) {
		this.applyAmt = value;
	}

	/** 取得撥款金額 **/
	public BigDecimal getLnAmt() {
		return this.lnAmt;
	}
	/** 設定撥款金額 **/
	public void setLnAmt(BigDecimal value) {
		this.lnAmt = value;
	}

	/** 取得鑑價完成日期 **/
	public Date getEstDate() {
		return this.estDate;
	}
	/** 設定鑑價完成日期 **/
	public void setEstDate(Date value) {
		this.estDate = value;
	}

	/** 取得徵審完成日期 **/
	public Date getCreditReviewDate() {
		return this.creditReviewDate;
	}
	/** 設定徵審完成日期 **/
	public void setCreditReviewDate(Date value) {
		this.creditReviewDate = value;
	}

	/** 取得撥款日期 **/
	public Date getLnDate() {
		return this.lnDate;
	}
	/** 設定撥款日期 **/
	public void setLnDate(Date value) {
		this.lnDate = value;
	}

	/** 取得送件代書 **/
	public String getEssayName() {
		return this.essayName;
	}
	/** 設定送件代書 **/
	public void setEssayName(String value) {
		this.essayName = value;
	}

	/** 取得承作情形 **/
	public String getStatFlag() {
		return this.statFlag;
	}
	/** 設定承作情形 **/
	public void setStatFlag(String value) {
		this.statFlag = value;
	}

	/** 取得是否填寫完畢 **/
	public String getIsFinished() {
		return this.isFinished;
	}
	/** 設定是否填寫完畢 **/
	public void setIsFinished(String value) {
		this.isFinished = value;
	}

	/** 取得利率方案 **/
	public String getRateKind() {
		return this.rateKind;
	}
	/** 設定利率方案 **/
	public void setRateKind(String value) {
		this.rateKind = value;
	}

	/** 取得回饋金比率(%) **/
	public BigDecimal getRebateRatio() {
		return this.rebateRatio;
	}
	/** 設定回饋金比率(%) **/
	public void setRebateRatio(BigDecimal value) {
		this.rebateRatio = value;
	}

	/** 取得回饋金 **/
	public BigDecimal getRebate() {
		return this.rebate;
	}
	/** 設定回饋金 **/
	public void setRebate(BigDecimal value) {
		this.rebate = value;
	}

	public String getL140M01A_MainId() {
		return this.l140m01a_MainId;
	}
	public void setL140M01A_MainId(String value) {
		this.l140m01a_MainId = value;
	}
	public String getAgntName() {
		return agntName;
	}
	public void setAgntName(String agntName) {
		this.agntName = agntName;
	}
	public String getLicenseYear() {
		return licenseYear;
	}
	public void setLicenseYear(String licenseYear) {
		this.licenseYear = licenseYear;
	}
	public String getLicenseWord() {
		return licenseWord;
	}
	public void setLicenseWord(String licenseWord) {
		this.licenseWord = licenseWord;
	}
	public String getLicenseNumber() {
		return licenseNumber;
	}
	public void setLicenseNumber(String licenseNumber) {
		this.licenseNumber = licenseNumber;
	}
	public Date getLicenseExp() {
		return licenseExp;
	}
	public void setLicenseExp(Date licenseExp) {
		this.licenseExp = licenseExp;
	}
	public String getAgntCompanyName() {
		return agntCompanyName;
	}
	public void setAgntCompanyName(String agntCompanyName) {
		this.agntCompanyName = agntCompanyName;
	}
	public String getAgntBranchName() {
		return agntBranchName;
	}
	public void setAgntBranchName(String agntBranchName) {
		this.agntBranchName = agntBranchName;
	}
}
