package com.mega.eloan.lms.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;
/** 覆審名單共用客戶資料檔 **/
@NamedEntityGraph(name = "L180M01D-entity-graph", attributeNodes = { @NamedAttributeNode("l180m01b") })
@Entity
@Table(name = "L180M01D", uniqueConstraints = @UniqueConstraint(columnNames = {
		"pid", "dbuObuId", "dbuObuDupNo" }))
public class L180M01D extends GenericBean implements IDataObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = -8789880439002452074L;

	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** L180M01B.oid **/
	@Column(name = "PID", length = 32, columnDefinition = "CHAR(32)")
	private String pid;

	/** 0為DBU，1為OBU **/
	@Column(name = "DBUOBUTYPE", length = 32, columnDefinition = "CHAR(1)")
	private String dbuObuType;

	@Column(name = "DBUOBUID", length = 10, columnDefinition = "VARCHAR(10)")
	private String dbuObuId;

	@Column(name = "DBUOBUDUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dbuObuDupNo;

	@Column(name = "DBUOBUNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String dbuObuName;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumns({ @JoinColumn(name = "PID", referencedColumnName = "OID", nullable = false, insertable = false, updatable = false) })
	private L180M01B l180m01b;

	public String getOid() {
		return oid;
	}

	public void setOid(String oid) {
		this.oid = oid;
	}

	public String getPid() {
		return pid;
	}

	public void setPid(String pid) {
		this.pid = pid;
	}

	public String getDbuObuType() {
		return dbuObuType;
	}

	public void setDbuObuType(String dbuObuType) {
		this.dbuObuType = dbuObuType;
	}

	public String getDbuObuId() {
		return dbuObuId;
	}

	public void setDbuObuId(String dbuObuId) {
		this.dbuObuId = dbuObuId;
	}

	public String getDbuObuDupNo() {
		return dbuObuDupNo;
	}

	public void setDbuObuDupNo(String dbuObuDupNo) {
		this.dbuObuDupNo = dbuObuDupNo;
	}

	public String getDbuObuName() {
		return dbuObuName;
	}

	public void setDbuObuName(String dbuObuName) {
		this.dbuObuName = dbuObuName;
	}

	public L180M01B getL180m01b() {
		return l180m01b;
	}

	public void setL180m01b(L180M01B l180m01b) {
		this.l180m01b = l180m01b;
	}

}
