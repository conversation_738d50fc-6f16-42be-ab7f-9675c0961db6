package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L782M01A;

/** 額度特殊案件登記表 **/
public interface L782M01ADao extends IGenericDao<L782M01A> {

	L782M01A findByOid(String oid);

	List<L782M01A> findByMainId(String mainId);

	L782M01A findByUniqueKey(String mainId, String loanTP);

	List<L782M01A> findByIndex01(String mainId, String subject);

	/**
	 * 找出所有的特殊登錄案件，不包含有刪除時間的
	 * 
	 * @return 特殊案件登記表
	 */
	List<L782M01A> findByAll(String ownBrId,String releaseDateS,String releaseDateE);
}