---------------------------------------------------------
-- LMS.L140S01A 個金借保人檔
---------------------------------------------------------


---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.L140S01A;
CREATE TABLE LMS.L140S01A (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)      not null,
	CUSTID        VARCHAR(10)   not null,
	<PERSON><PERSON><PERSON><PERSON>         CHAR(1)       not null,
	CUSTNAME      VARCHAR(120) ,
	CUSTPOS       CHAR(1)      ,
	TYPE	      CHAR(1)      ,
	CREATOR       CHAR(6)      ,
	CREATE<PERSON>ME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_L140S01A PRIMARY KEY(OID)
) IN EL_DATA_4KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XL140S01A01;
CREATE UNIQUE INDEX LMS.XL140S01A01 ON LMS.L140S01A   (MAINID, CUSTID, DUPNO);
--DROP INDEX LMS.XL140S01A02;
CREATE INDEX LMS.XL140S01A02 ON LMS.L140S01A   (MAINID, CUSTPOS, CUSTID, DUPNO);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.L140S01A IS '個金借保人檔';
COMMENT ON LMS.L140S01A (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	CUSTID        IS '客戶統編', 
	DUPNO         IS '重複序號', 
	CUSTNAME      IS '借款人姓名', 
	CUSTPOS       IS '性質(相關身份)',
	TYPE          IS '新增來源',
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
