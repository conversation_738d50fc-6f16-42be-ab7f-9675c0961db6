package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.mfaloan.service.MisELF346Service;

@Service
public class MisELF346ServiceImpl extends AbstractMFAloanJdbc implements
		MisELF346Service {

	@Override
	public List<Map<String, Object>> getELF346DPChk(String custId, String dupNo) {
		return getJdbc().queryForList("ELF346.getDPChk", new String[] { custId, dupNo });
	}
}
