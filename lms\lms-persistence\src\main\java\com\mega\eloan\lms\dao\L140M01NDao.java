/* 
 * L140M01NDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140M01N;

/**
 * <pre>
 * 額度利率結構利率化明細檔
 * </pre>
 * 
 * @since 2012/10/16
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/10/16,REX,new
 *          </ul>
 */
public interface L140M01NDao extends IGenericDao<L140M01N> {

	L140M01N findByOid(String oid);

	List<L140M01N> findByOids(String[] oids);

	List<L140M01N> findByMainId(String mainId);

	L140M01N findByUniqueKey(String mainId, Integer rateSeq, String rateType);

	List<L140M01N> findByIndex01(String mainId, Integer rateSeq, String rateType);

	List<L140M01N> findByMainIdAndRateSeq(String mainId, Integer rateSeq);

	/**
	 * 查詢利率檔 (排序條件:類別 ASC, 段數 ASC, secNoOp ASC, 組成說明字串 ASC)
	 * 
	 * @param mainId
	 *            額度明細表mainId
	 * @param rateSeq
	 *            L140M01F.seq
	 * @return
	 */
	List<L140M01N> findByMainIdAndRateSeqOrderByWithGrid(String mainId,
			Integer rateSeq);
}