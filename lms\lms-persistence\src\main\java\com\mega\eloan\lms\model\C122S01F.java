/* 
 * C122S01F.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 進件狀態明細檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C122S01F", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId"}))
public class C122S01F extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** oid **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** mainId **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * uid<p/>
	 * not null (先放mainid，未來有複製功能的時候在改成複製的mainid)
	 */
	@Size(max=32)
	@Column(name="UID", length=32, columnDefinition="CHAR(32)")
	private String uid;

	/** 編製單位代號 **/
	@Size(max=3)
	@Column(name="OWNBRID", length=3, columnDefinition="CHAR(3)")
	private String ownBrId;
	
	/** 文件狀態 **/
	@Size(max=3)
	@Column(name="DOCSTATUS", length=3, columnDefinition="VARCHAR(3)")
	private String docStatus;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;
	
	/** 備註說明 **/
	@Size(max=1200)
	@Column(name="MEMO", length=1200, columnDefinition="VARCHAR(1200)")
	private String memo;

	/** 
	 * 改派分行對應<p/>
	 * 對應C122M01C-oid
	 */
	@Size(max=32)
	@Column(name="REFOID", length=32, columnDefinition="CHAR(32)")
	private String refoid;
	

	/** 取得oid **/
	public String getOid() {
		return this.oid;
	}
	/** 設定oid **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得mainId **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定mainId **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得uid<p/>
	 * not null (先放mainid，未來有複製功能的時候在改成複製的mainid)
	 */
	public String getUid() {
		return this.uid;
	}
	/**
	 *  設定uid<p/>
	 *  not null (先放mainid，未來有複製功能的時候在改成複製的mainid)
	 **/
	public void setUid(String value) {
		this.uid = value;
	}
	
	/** 取得編製單位代號 **/
	public String getOwnBrId() {
		return this.ownBrId;
	}
	/** 設定編製單位代號 **/
	public void setOwnBrId(String value) {
		this.ownBrId = value;
	}

	/** 取得文件狀態 **/
	public String getDocStatus() {
		return this.docStatus;
	}
	/** 設定文件狀態 **/
	public void setDocStatus(String value) {
		this.docStatus = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}
	
	/** 取得備註說明 **/
	public String getMemo() {
		return this.memo;
	}
	/** 設定備註說明 **/
	public void setMemo(String value) {
		this.memo = value;
	}

	/** 
	 * 取得改派分行對應<p/>
	 * 對應C122M01C-oid
	 */
	public String getRefoid() {
		return this.refoid;
	}
	/**
	 *  設定改派分行對應<p/>
	 *  對應C122M01C-oid
	 **/
	public void setRefoid(String value) {
		this.refoid = value;
	}
}
