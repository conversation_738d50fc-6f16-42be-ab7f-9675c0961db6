package com.mega.eloan.lms.batch.service.impl;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.TreeMap;

import javax.annotation.Resource;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.constants.SysParamConstants;
import com.mega.eloan.common.gwclient.RPQSFTPClient;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.RPQSPageMeta;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.dao.C122M01ADao;
import com.mega.eloan.lms.model.C122M01A;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.Util;

@Service("rpqsBatchServiceImpl")
public class RPQSBatchServiceImpl extends AbstractCapService implements
		WebBatchService {

	private static Logger logger = LoggerFactory
			.getLogger(RPQSBatchServiceImpl.class);
		
	private final static String OUTPUT_ENCODE = "CP950";
	private final static String RPQS_TEMP_DIR = "/LMS/RPQS/";
	private final static String NEWLINE = System.getProperty("line.separator");
	private final static String LINE_PAD = "   ";
	private final static int MAX_WIDTH = 164;
	
	@Resource
	BranchService branchService;
	
	@Resource
	CLSService clsService;
	
	@Resource
	RPQSFTPClient rpqsFtpClient;

	@Resource
	SysParameterService sysParamService;
	
	@Resource
	C122M01ADao c122m01aDao;
	
	@Override
	public JSONObject execute(JSONObject json) {
		boolean isSuccess = false;
		JSONObject result = null;
		JSONObject rq = json.getJSONObject("request");
		//復原TFS J-111-0636_05097_B100X	
		String act = rq.optString("act");
		String reportId = rq.optString("reportId");
		String msg = "";
		try {
			if(Util.equals("procRpqs", act)){
				String rptContent = "";
				if(Util.equals(reportId, "LLWEL001") || Util.equals(reportId, "LLMEL017")){
					boolean skipNoData = rq.optBoolean("skipNoData");
					rptContent = this.getRPQSContent_LLWEL001(skipNoData, reportId);

				}else if(Util.equals(reportId, "LLWEL002") || Util.equals(reportId, "LLMEL018")){
					boolean skipNoData = rq.optBoolean("skipNoData");
					rptContent = this.getRPQSContent_LLWEL002(skipNoData, reportId);
				}
				
				//LLWEL003 全行線上申辦貸款業務統計表for 授管處,原以TXT後,改以PDF送至RPQS
				
				if(Util.isNotEmpty(rptContent)){
					//產生報表檔				
					int year = Calendar.getInstance().get(Calendar.YEAR) - 1911;
					String fullFileName = this.getRootPath() + StrUtils.concat(reportId, ".", year,
							new SimpleDateFormat("MMdd").format(new Date()));
					FileUtils.deleteQuietly(new File(fullFileName));
					FileUtils.writeStringToFile(new File(fullFileName), rptContent,
							OUTPUT_ENCODE);
										
					String fileName = FilenameUtils.getName(fullFileName);
					String msgId = IDGenerator.getUUID();

					// 送FTP
					logger.info("[execute] FTP Client : {}", rpqsFtpClient.toString());
					rpqsFtpClient.send(msgId, fullFileName,
							rpqsFtpClient.getServerDir(), fileName, false, true, false);
					logger.info("[execute] FTP上傳結果： {} ", ToStringBuilder
							.reflectionToString(rpqsFtpClient.list(msgId,
									rpqsFtpClient.getServerDir())));
				}				
			}else{
				throw new CapException("未知的method【"+act+"】", getClass());
			}
			isSuccess = true;
		} catch (Throwable e) {	
			msg = e.getMessage();
			logger.error(msg, e);
			isSuccess = false;
		}

		if (isSuccess) {
			result = WebBatchCode.RC_SUCCESS;//此json object 內已包含 SUCCESS
			result.element(WebBatchCode.P_RESPONSE, result.get(WebBatchCode.P_RC_MSG));
		} else {
			result = WebBatchCode.RC_ERROR;
			result.element(WebBatchCode.P_RC_MSG, msg);
			result.element(WebBatchCode.P_RESPONSE, msg);
		}
		return result;
	}

	/**
	 * 取得報表檔目錄，若不存在則建立之
	 * 
	 * @return 報表檔目錄
	 * @throws IOException
	 */
	private String getRootPath() throws IOException {
		String docFileRoot = this.sysParamService
				.getParamValue(SysParamConstants.SYS_DOCFILE_ROOT_DIR);

		docFileRoot += (RPQS_TEMP_DIR + new SimpleDateFormat("yyyyMM")
				.format(new Date()));
		File rootPath = new File(docFileRoot);
		if (!rootPath.exists()) {
			FileUtils.forceMkdir(new File(docFileRoot));
		}
		return docFileRoot + "/";

	}
	private BigDecimal sumC122M01A_applyAmt(List<C122M01A> data_list, String withStatus){
		BigDecimal sumApplyAmt = BigDecimal.ZERO;
		for(C122M01A c122m01a: data_list){
			boolean match = false;
			if(Util.isEmpty(withStatus)){
				match = true;
			}else{
				if(Util.equals(c122m01a.getApplyStatus(), withStatus)){
					match = true;
				}	
			}
			if(match){
				sumApplyAmt = sumApplyAmt.add(c122m01a.getApplyAmt());	
			}						
		}
		return sumApplyAmt;
	}
	private int cntC122M01A_applyStatus(List<C122M01A> data_list, String withStatus){
		int r = 0;
		for(C122M01A c122m01a: data_list){
			boolean match = false;
			if(Util.isEmpty(withStatus)){
				match = true;
			}else{
				if(Util.equals(c122m01a.getApplyStatus(), withStatus)){
					match = true;
				}	
			}
			if(match){
				r++;
			}
		}
		return r;
	}
	
	private List<C122M01A> filter_in_applyKind(List<C122M01A> src_list, String applyKind){
		List<C122M01A> result = new ArrayList<C122M01A>();
		for(C122M01A c122m01a :src_list){
			if(Util.equals(c122m01a.getApplyKind(), applyKind)){
				result.add(c122m01a);
			}else{
				
			}
		}
		return result;
	}
	
	private String getRPQSContent_LLWEL001(boolean skipNoData, String reportId) {
		
		StringBuffer contents = new StringBuffer();
		
		RPQSPageMeta page = new RPQSPageMeta();
		page.setReportId(reportId);

		final int PAGING_SIZE = 50;

		TreeMap<String, String> brMap = getBranch();		
		for(String brNo : brMap.keySet()){
			String brName = brMap.get(brNo);
			page.setBrNo(brNo);
			page.setBrName(brName);
			//~~~
			//Title 由 線上增貸 RenameTo 線上申辦貸款  ⇒  同時含[房貸增貸、信貸] 
			List<C122M01A> raw_data_list = c122m01aDao.findInProgressC122M01A(page.getBrNo(), new String[]{UtilConstants.C122_ApplyKind.H, UtilConstants.C122_ApplyKind.C});			
			int TOTAL_COUNT = raw_data_list.size();			
			if (TOTAL_COUNT > 0) {
				int pageCount = TOTAL_COUNT / PAGING_SIZE;
				int modCount = TOTAL_COUNT % PAGING_SIZE;
				if (modCount > 0) {
					pageCount += 1;
				}			
				List<C122M01A> data_H_list = filter_in_applyKind(raw_data_list, UtilConstants.C122_ApplyKind.H);
				List<C122M01A> data_C_list = filter_in_applyKind(raw_data_list, UtilConstants.C122_ApplyKind.C);
				List<C122M01A> data_list = new ArrayList<C122M01A>();
				int data_H_size = data_H_list.size();
				int data_C_size = data_C_list.size();
				if(data_H_size>0){
					data_list.addAll(data_H_list);	
				}
				if(data_C_size>0){
					data_list.addAll(data_C_list);	
				}
				
				if(data_H_size>0 && data_C_size>0){
					/*
					 	先呈現H-房貸增貸, 再呈現C-信貸
					 */
					int start = 0;
					int end = 0;
					for (int i = 0; i < pageCount; i++) {
						start = (PAGING_SIZE * i);
						end = (Math.min(PAGING_SIZE * (i + 1), TOTAL_COUNT)-1);
						
						if(true){
							if (i>0) {
								contents.append("\f").append(NEWLINE); //換頁符號
							}
							page = this.getRPQSRptTitle_LLWEL001(page, i+1);
							contents.append(page.getContent());
							page.resetContent();
						}
						
						for(int idx=start; idx<=end; idx++){						
							C122M01A c122m01a = data_list.get(idx);						
							// 取得頁面內容
							page = this.getRPQSRptBody_LLWEL001(c122m01a, page);
							
							contents.append(page.getContent());
							
							if(idx==data_H_size-1){
								contents.append(StringUtils.repeat("-", MAX_WIDTH)).append(NEWLINE);
								appendLLWEL001Bottom(contents, data_H_list);
								contents.append(StringUtils.repeat(" ", MAX_WIDTH)).append(NEWLINE);
							}
							if(idx==(data_H_size+data_C_size-1)){
								contents.append(StringUtils.repeat("-", MAX_WIDTH)).append(NEWLINE);
								appendLLWEL001Bottom(contents, data_C_list);		
							}
						}
						
						if(i==pageCount-1){
							appendLLWEL001Bottom_end(contents);
						}
					}		
					
				}else{
					int start = 0;
					int end = 0;
					for (int i = 0; i < pageCount; i++) {
						start = (PAGING_SIZE * i);
						end = (Math.min(PAGING_SIZE * (i + 1), TOTAL_COUNT)-1);
						
						if(true){
							if (i>0) {
								contents.append("\f").append(NEWLINE); //換頁符號
							}
							page = this.getRPQSRptTitle_LLWEL001(page, i+1);
							contents.append(page.getContent());
							page.resetContent();
						}
						
						for(int idx=start; idx<=end; idx++){						
							C122M01A c122m01a = data_list.get(idx);						
							// 取得頁面內容
							page = this.getRPQSRptBody_LLWEL001(c122m01a, page);
							
							contents.append(page.getContent());						
						}
						contents.append(StringUtils.repeat("-", MAX_WIDTH)).append(NEWLINE);
						
						if(i==pageCount-1){
							appendLLWEL001Bottom(contents, data_list);
							appendLLWEL001Bottom_end(contents);
						}
					}		
				}
			} else {
				if(skipNoData){
					continue;
				}
				// 無資料
				page = this.getRPQSRptTitle_LLWEL001(page, 1);
				contents.append(page.getContent());
				contents.append(StringUtils.repeat("-", MAX_WIDTH)).append(NEWLINE);
				appendLLWEL001Bottom(contents, new ArrayList<C122M01A>()); //無資料
				appendLLWEL001Bottom_end(contents);
			}
		}

		return contents.toString();
	}

	private void appendLLWEL001Bottom(StringBuffer contents, List<C122M01A> data_list){
		int TOTAL_COUNT = data_list.size();
		int TOTAL_0A0 = cntC122M01A_applyStatus(data_list, UtilConstants.C122_ApplyStatus.受理中);
		int TOTAL_0B0 = cntC122M01A_applyStatus(data_list, UtilConstants.C122_ApplyStatus.審核中);
		BigDecimal sumApplyAmt = sumC122M01A_applyAmt(data_list, "");
		contents.append(LINE_PAD).append(
				"受理中："+TOTAL_0A0+"件。"+
				"審核中："+TOTAL_0B0+"件。"+
				"合計："+TOTAL_COUNT+"件。"+
				"合計金額："+NumConverter.addComma(sumApplyAmt)+"萬。").append(NEWLINE);	
		
	}
	
	private void appendLLWEL001Bottom_end(StringBuffer contents){
		contents.append(LINE_PAD).append("*** 報表結束 ***").append(NEWLINE);
	}
	
	private void appendLLWEL002Bottom(StringBuffer contents
			, int _cnt0A0, BigDecimal _sum0A0 
			, int _cnt0B0, BigDecimal _sum0B0
			, int _cntBr, BigDecimal _sumBr
			, int[] colWidthArr){
		int idx = 0; 
		contents.append(
				 rightPad("", colWidthArr[idx++])
				+leftPad("總計", colWidthArr[idx++])
				
				+leftPad(Util.trim(_cnt0A0), colWidthArr[idx++])
				+leftPad(NumConverter.addComma(_sum0A0)+"萬", colWidthArr[idx++])
				
				+leftPad(Util.trim(_cnt0B0), colWidthArr[idx++])
				+leftPad(NumConverter.addComma(_sum0B0)+"萬", colWidthArr[idx++])
				
				
				+leftPad(Util.trim(_cntBr), colWidthArr[idx++])
				+leftPad(NumConverter.addComma(_sumBr)+"萬", colWidthArr[idx++])
				
				).append(NEWLINE);	
		contents.append(LINE_PAD).append("*** 報表結束 ***").append(NEWLINE);
	}
	
	private RPQSPageMeta getRPQSRptTitle_LLWEL001(RPQSPageMeta page, int pageCnt) {
		String DEPT_NAMI = page.getBrName();
		
		StringBuffer rptstr = new StringBuffer();
		

		// 第1列
		rptstr.append(LINE_PAD).append("BRN:").append(page.getBrNo()).append(LINE_PAD).append("FORM:").append(page.getReportId());
		rptstr.append(StringUtils.repeat(" ", 70 - rptstr.length()));
		rptstr.append("兆豐商業銀行").append(LINE_PAD).append(DEPT_NAMI);
		rptstr.append(NEWLINE);
		
		// 第2列
//		rptstr.append(center("線上增貸未結案報表", MAX_WIDTH));
		rptstr.append(center("線上申辦貸款未結案報表", MAX_WIDTH));
		rptstr.append(NEWLINE);
		
		// 第3列
		rptstr.append(rightPad("報表用途：追蹤未結案", 40));
		rptstr.append(rightPad("使用單位：分行", 40));
		rptstr.append(rightPad("保管單位：分行", 40));
		rptstr.append(rightPad("保管期限：使用完畢", 25));
		rptstr.append("頁次：").append(pageCnt);
		rptstr.append(NEWLINE);
		
		// 第4列
		rptstr.append(rightPad("", 40));
		rptstr.append(rightPad("", 40));
		rptstr.append(rightPad("", 40));
		rptstr.append("製表日期：").append(getCurrentTWDate());
		rptstr.append(NEWLINE);
		
		// 第5列
		rptstr.append(StringUtils.repeat("-", MAX_WIDTH));
		rptstr.append(NEWLINE);		

		// 第6列
		if(true){
			if(true){
				rptstr.append(rightPad("統一編號", 11));
				rptstr.append(rightPad(" ", 3));
				rptstr.append(rightPad("客戶名稱", 70));
				rptstr.append(rightPad("申請金額", 17));//13+4
				rptstr.append(rightPad("", 2));
				rptstr.append(rightPad("申請時間", 22));
				rptstr.append(rightPad("申請狀態", 15));
				rptstr.append(rightPad("申請類型", 15));
			}
			rptstr.append(NEWLINE);				
		}
				
		// 第7列
		rptstr.append(StringUtils.repeat("-", MAX_WIDTH));
		rptstr.append(NEWLINE);
				
		
		page.setContent(rptstr.toString());
		return page;
	}

	private RPQSPageMeta getRPQSRptTitle_LLWEL002(RPQSPageMeta page, int pageCnt, String unitDesc, int[] colWidthArr) {
		String DEPT_NAMI = page.getBrName();
		
		StringBuffer rptstr = new StringBuffer();
		
		// 第1列
		rptstr.append(LINE_PAD).append("BRN:").append(page.getBrNo()).append(LINE_PAD).append("FORM:").append(page.getReportId());
		rptstr.append(StringUtils.repeat(" ", 70 - rptstr.length()));
		rptstr.append("兆豐商業銀行").append(LINE_PAD).append(DEPT_NAMI);
		rptstr.append(NEWLINE);
		
		// 第2列
		rptstr.append(center("線上增貸未結案統計表", MAX_WIDTH));
		rptstr.append(NEWLINE);
		
		// 第3列
		rptstr.append(rightPad("報表用途：追蹤未結案", 40));
		rptstr.append(rightPad("使用單位："+unitDesc, 40));
		rptstr.append(rightPad("保管單位："+unitDesc, 40));
		rptstr.append(rightPad("保管期限：使用完畢", 25));
		rptstr.append("頁次：").append(pageCnt);
		rptstr.append(NEWLINE);
		
		// 第4列
		rptstr.append(rightPad("", 40));
		rptstr.append(rightPad("", 40));
		rptstr.append(rightPad("", 40));
		rptstr.append("製表日期：").append(getCurrentTWDate());
		rptstr.append(NEWLINE);
		
		// 第5列
		rptstr.append(StringUtils.repeat("-", MAX_WIDTH));
		rptstr.append(NEWLINE);		

		// 第6列
		if(true){
			if(true){
				int idx = 0;
				rptstr.append(rightPad("分行", colWidthArr[idx++]));
				rptstr.append(rightPad("分行名稱", colWidthArr[idx++]));
				rptstr.append(rightPad("受理中件數", colWidthArr[idx++]));
				rptstr.append(rightPad("受理中金額", colWidthArr[idx++]));
				rptstr.append(rightPad("審核中件數", colWidthArr[idx++]));
				rptstr.append(rightPad("審核中金額", colWidthArr[idx++]));
				rptstr.append(rightPad("合計件數", colWidthArr[idx++]));
				rptstr.append(rightPad("合計金額", colWidthArr[idx++]));
			}
			rptstr.append(NEWLINE);				
		}
				
		// 第7列
		rptstr.append(StringUtils.repeat("-", MAX_WIDTH));
		rptstr.append(NEWLINE);
				
		
		page.setContent(rptstr.toString());
		return page;
	}
	
	private RPQSPageMeta getRPQSRptBody_LLWEL001(C122M01A c122m01a, RPQSPageMeta page) {
		StringBuffer rptstr = new StringBuffer();

		rptstr.append(rightPad(c122m01a.getCustId(), 11));
		rptstr.append(rightPad(c122m01a.getDupNo(), 3));
		rptstr.append(rightPad(c122m01a.getCustName(), 70));
		rptstr.append(rightPad("", 4)); //幣別
		rptstr.append(leftPad(NumConverter.addComma(c122m01a.getApplyAmt())+"萬", 13));
		rptstr.append(rightPad("", 2));
		String applyTS = c122m01a.getApplyTS()==null?"":CapDate.convertTimestampToString(c122m01a.getApplyTS(), UtilConstants.DateFormat.YYYY_MM_DD_HH_MM_SS);
		rptstr.append(rightPad(applyTS, 22));
		String applyStatus = c122m01a.getApplyStatus();
		if(Util.equals(UtilConstants.C122_ApplyStatus.受理中, applyStatus)){
			applyStatus = "受理中";
		}else if(Util.equals(UtilConstants.C122_ApplyStatus.審核中, applyStatus)){
			applyStatus = "審核中";
		}
		rptstr.append(rightPad(applyStatus, 15));
		String applyKind = Util.equals(c122m01a.getApplyKind(), UtilConstants.C122_ApplyKind.C)?"信貸":"房貸增貸";
		rptstr.append(rightPad(applyKind, 15));
		rptstr.append(NEWLINE);
		// 設定回傳的內容
		page.setContent(rptstr.toString());
		
		return page;
	}

	private RPQSPageMeta getRPQSRptBody_LLWEL002(String brNo, String brName, RPQSPageMeta page
			, Integer cnt0A0, BigDecimal sum0A0
			, Integer cnt0B0, BigDecimal sum0B0
			, Integer cntBr, BigDecimal sumBr, int[] colWidthArr) {
		StringBuffer rptstr = new StringBuffer();
		int idx = 0;
		rptstr.append(rightPad(Util.trim(brNo), colWidthArr[idx++]));
		rptstr.append(rightPad(Util.trim(brName), colWidthArr[idx++]));
		
		rptstr.append(leftPad(Util.trim(cnt0A0), colWidthArr[idx++]));
		rptstr.append(leftPad(NumConverter.addComma(sum0A0)+"萬", colWidthArr[idx++]));
		rptstr.append(leftPad(Util.trim(cnt0B0), colWidthArr[idx++]));
		rptstr.append(leftPad(NumConverter.addComma(sum0B0)+"萬", colWidthArr[idx++]));
		rptstr.append(leftPad(Util.trim(cntBr), colWidthArr[idx++]));
		rptstr.append(leftPad(NumConverter.addComma(sumBr)+"萬", colWidthArr[idx++]));
		rptstr.append(NEWLINE);
		// 設定回傳的內容
		page.setContent(rptstr.toString());
		
		return page;
	}
	private String getRPQSContent_LLWEL002(boolean skipNoData, String reportId) {
		
		StringBuffer contents = new StringBuffer();
		
		RPQSPageMeta page = new RPQSPageMeta();
		page.setReportId(reportId);

		final int PAGING_SIZE = 50;

		TreeMap<String, String> brMap = getBranch();
		LinkedHashMap<String, List<Object>> brDataMap = new LinkedHashMap<String, List<Object>>();
		for(String brNo : brMap.keySet()){
			List<C122M01A> data_list = filter_in_applyKind(c122m01aDao.findInProgressC122M01A(brNo, new String[]{UtilConstants.C122_ApplyKind.H})
					, UtilConstants.C122_ApplyKind.H); //另外篩選 applyKind==H , Title仍維持：增貸未結案
			if(skipNoData && data_list.size()==0){
				continue;
			}
			List<Object> dataList = new ArrayList<Object>();
			//受理中：0件。審核中：1件。合計：1件。合計金額：1,000,000萬。
			Integer TOTAL_COUNT = data_list.size();
			Integer TOTAL_0A0 = cntC122M01A_applyStatus(data_list, UtilConstants.C122_ApplyStatus.受理中);
			Integer TOTAL_0B0 = cntC122M01A_applyStatus(data_list, UtilConstants.C122_ApplyStatus.審核中);
			
			BigDecimal sumApplyAmt = sumC122M01A_applyAmt(data_list, "");
			BigDecimal sumApplyAmt_0A0 = sumC122M01A_applyAmt(data_list, UtilConstants.C122_ApplyStatus.受理中);
			BigDecimal sumApplyAmt_0B0 = sumC122M01A_applyAmt(data_list, UtilConstants.C122_ApplyStatus.審核中);
			
			dataList.add(TOTAL_0A0); //受理中
			dataList.add(sumApplyAmt_0A0);
			
			dataList.add(TOTAL_0B0); //審核中
			dataList.add(sumApplyAmt_0B0);
			
			dataList.add(TOTAL_COUNT); //合計		
			dataList.add(sumApplyAmt); //合計金額
			
			brDataMap.put(brNo, dataList);
		}
		if(true){
			String br9XX = UtilConstants.BankNo.授信行銷處;
			if(clsService.is_function_on_codetype("J-107-0113")){
				br9XX = UtilConstants.BankNo.消金業務處;
			}
			//==============
			IBranch ibranch9XX = branchService.getBranch(br9XX);
			String br9XXName = Util.trim(ibranch9XX==null?"":ibranch9XX.getBrName());
			String unitDesc = Util.trim(ibranch9XX==null?"":ibranch9XX.getNameABBR());
			page.setBrNo(br9XX);
			page.setBrName(br9XXName);
			//~~~
			
			int TOTAL_COUNT = brDataMap.size();			
			int _cnt0A0 = 0;
			BigDecimal _sum0A0 = BigDecimal.ZERO;
			int _cnt0B0 = 0;
			BigDecimal _sum0B0 = BigDecimal.ZERO;
			int _cntBr = 0;
			BigDecimal _sumBr = BigDecimal.ZERO;
			
			int[] colWidthArr = {11, 35, 11, 20, 11, 20, 11, 25};
			if (TOTAL_COUNT > 0) {
				int pageCount = TOTAL_COUNT / PAGING_SIZE;
				int modCount = TOTAL_COUNT % PAGING_SIZE;
				if (modCount > 0) {
					pageCount += 1;
				}				
				int start = 0;
				int end = 0;
				List<String> keyList = new ArrayList<String>(brDataMap.keySet());
				for (int i = 0; i < pageCount; i++) {
					start = (PAGING_SIZE * i);
					end = (Math.min(PAGING_SIZE * (i + 1), TOTAL_COUNT)-1);
					
					if(true){
						if (i>0) {
							contents.append("\f").append(NEWLINE);
						}
						page = this.getRPQSRptTitle_LLWEL002(page, i+1, unitDesc, colWidthArr);
						contents.append(page.getContent());
						page.resetContent();
					}
					
					for(int idx=start; idx<=end; idx++){						
						String brNo = keyList.get(idx);			
						List<Object> dataList = brDataMap.get(brNo);
						Integer cnt0A0 = (Integer)dataList.get(0);
						BigDecimal sum0A0 = (BigDecimal)dataList.get(1);
						Integer cnt0B0 = (Integer)dataList.get(2); 
						BigDecimal sum0B0 = (BigDecimal)dataList.get(3);
						Integer cntBr = (Integer)dataList.get(4);
						BigDecimal sumBr = (BigDecimal)dataList.get(5);
						if(true){
							_cnt0A0 += cnt0A0;
							_sum0A0 = _sum0A0.add(sum0A0);
							_cnt0B0 += cnt0B0;
							_sum0B0 = _sum0B0.add(sum0B0);
							_cntBr += cntBr;
							_sumBr = _sumBr.add(sumBr);
						}
						// 取得頁面內容
						page = this.getRPQSRptBody_LLWEL002(brNo, LMSUtil.getDesc(brMap, brNo),  page
								,  cnt0A0, sum0A0, cnt0B0, sum0B0, cntBr, sumBr, colWidthArr);
						
						contents.append(page.getContent());						
					}
					contents.append(StringUtils.repeat("-", MAX_WIDTH)).append(NEWLINE);
					
					if(i==pageCount-1){
						appendLLWEL002Bottom(contents, _cnt0A0, _sum0A0 , _cnt0B0 , _sum0B0, _cntBr, _sumBr, colWidthArr);		
					}
				}				
			} else {				
				// 無資料
				page = this.getRPQSRptTitle_LLWEL002(page, 1, unitDesc, colWidthArr);
				contents.append(page.getContent());
				contents.append(StringUtils.repeat("-", MAX_WIDTH)).append(NEWLINE);
				appendLLWEL002Bottom(contents, _cnt0A0, _sum0A0 , _cnt0B0 , _sum0B0, _cntBr, _sumBr, colWidthArr);
			}
		}

		return contents.toString();
	}

	private TreeMap<String, String> getBranch(){
		List<IBranch> bankList = new ArrayList<IBranch>();
		TreeMap<String, String> tm = new TreeMap<String, String>();
		String[] headquarters = new String[]{
				  UtilConstants.BankNo.北一區營運中心	, UtilConstants.BankNo.北二區營運中心
				, UtilConstants.BankNo.桃竹苗區營運中心	, UtilConstants.BankNo.中區營運中心
				, UtilConstants.BankNo.南區營運中心
				};
		for(String s: headquarters){
			_addBankAndSub(bankList, s);	
		}
		_addBankAndSub(bankList, UtilConstants.BankNo.國外部);
		_addBankAndSub(bankList, UtilConstants.BankNo.金控總部分行);
		_addBankAndSub(bankList, UtilConstants.BankNo.國金部);
		_addBankAndSub(bankList, UtilConstants.BankNo.私銀處作業組);
		
		for (IBranch b : bankList) {
			tm.put(Util.trim(b.getBrNo()), Util.trim(b.getBrName()));
		}	
		
		for(String rk: headquarters){
			tm.remove(rk);	
		}
		tm.remove("900");
		return tm;
	}
	private void _addBankAndSub(List<IBranch> bankList, String brNo){
		bankList.addAll(branchService.getBranchOfGroup(brNo));
		bankList.add(branchService.getBranch(brNo));
	}
		
	/**
	 * 取得目前民國日期
	 * 
	 * @return 民國日期
	 */
	private String getCurrentTWDate() {
		int year = Calendar.getInstance().get(Calendar.YEAR) - 1911;
		return StrUtils.concat("中華民國 ", year, " 年 ", new SimpleDateFormat(
				"MM 月 dd 日").format(new Date()));
	}

	
	/**
	 * 右方補足空白至指定長度
	 * 
	 * @param str
	 *            原始字串
	 * @param len
	 *            長度
	 * @return 補足長度後的字串
	 */
	private String rightPad(String str, int len) {
		str = (str == null ? "" : str);
		if (str.getBytes().length != str.length()) {
			// 有中文
			return StringUtils.rightPad(str, len - getDiffUTF8Size(str));
		} else {
			// 無中文
			return StringUtils.rightPad(str, len);
		}
	}
	
	private String leftPad(String str, int len) {
		str = (str == null ? "" : str);
		if (str.getBytes().length != str.length()) {
			// 有中文
			return StringUtils.leftPad(str, len - getDiffUTF8Size(str));
		} else {
			// 無中文
			return StringUtils.leftPad(str, len);
		}
	}

	/**
	 * 將指定字串置中
	 * 
	 * @param str
	 *            原始字串
	 * @param len
	 *            指定長度
	 * @return 置中後字串
	 */
	private String center(String str, int len) {
		str = (str == null ? "" : str);
		if (str.getBytes().length != str.length()) {
			// 有中文
			return StringUtils.center(str, len - getDiffUTF8Size(str));
		} else {
			// 無中文
			return StringUtils.center(str, len);
		}
	}
	
	/**
	 * 計算UTF-8與BIG5之差異長度
	 * 
	 * @param src
	 *            原始字串
	 * @return 差異長度
	 */
	private int getDiffUTF8Size(String src) {
		int len = (src == null) ? 0 : src.length();
		int total = 0;
		for (int i = 0; i < len; i++) {
			if (src.charAt(i) > 0x07FF) {
				total += 1;
			}
		}
		return total;
	}
}
      