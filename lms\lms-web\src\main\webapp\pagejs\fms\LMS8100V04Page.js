var inits = {
    fhandle: "lms8100m01formhandler",
    ghandle: "lms8100gridhandler"
};

$(document).ready(function(){

	init_grid();
	
    $("#buttonPanel").find("#btnDelete").click(function(){
    	var row = $("#gridview").getGridParam('selrow');
        if (!row) {
            // grid.selrow=請先選擇一筆資料。
            return CommonAPI.showMessage(i18n.def["grid.selrow"]);
        }
        
        // confirmDelete=是否確定刪除?
		CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
			if (b) {
				var rowData = $("#gridview").getRowData(row);
				$.ajax({
		            handler: inits.fhandle,
		            data: {
		                formAction: "deleteL300m01c",
		                oid: rowData.oid
		            },
		            success: function(obj){
		            	$("#gridview").trigger("reloadGrid");
		            }
		        });
			}
		});
    }).end().find("#btnSendToCtrlDept").click(function(){
    	var row = $("#gridview").getGridParam('selrow');
        if (!row) {
            // grid.selrow=請先選擇一筆資料。
            return CommonAPI.showMessage(i18n.def["grid.selrow"]);
        }
        
        var rowData = $("#gridview").getRowData(row);
        $.ajax({
            handler: inits.fhandle,
            data: {
                formAction: "sendL300m01c",
                oid: rowData.oid
            },
            success: function(obj){
            	$("#gridview").trigger("reloadGrid");
            }
        });
    }).end().find("#btnReturn").click(function(){
    	var row = $("#gridview").getGridParam('selrow');
        if (!row) {
            // grid.selrow=請先選擇一筆資料。
            return CommonAPI.showMessage(i18n.def["grid.selrow"]);
        }
        
        var rowData = $("#gridview").getRowData(row);
        $.ajax({
            handler: inits.fhandle,
            data: {
                formAction: "returnL300m01c",
                oid: rowData.oid
            },
            success: function(obj){
            	$("#gridview").trigger("reloadGrid");
            }
        });
    });
    
    function init_grid(){
    	var grid = $("#gridview").iGrid({
            handler: inits.ghandle,//'lms8100gridhandler',
            height: 350,
            width: 785,
            autowidth: false,
            action: "queryGridL300M01C",
            postData: {
                searchType: "init"
            },
            rowNum: 15,
            sortname: "createTime|bgnDate",
            sortorder: "desc|desc",
            multiselect: false,
            colModel: [{
            	colHeader: i18n.abstracteloan['doc.lastUpdater'],// "最後異動人員",
            	align: "center", width: 125, sortable: true, name: 'updater'
            }, {
            	colHeader: i18n.lms8100v01['L300M01A.dataDate'],
            	align: "center", width: 200, sortable: false, name: 'dataDate'
            }, {
            	colHeader: i18n.lms8100v01['L300M01A.createTime'],
            	align: "center", width: 200, sortable: true, name: 'createTime',
            	formatter: 'date',
            	formatoptions: {
                    srcformat: 'Y-m-d H:i:s',
                    newformat: 'Y-m-d'
                }
            }, {
            	colHeader: i18n.lms8100v01['L300M01A.sendTime'],
            	align: "center", width: 200, sortable: true, name: 'sendTime'
            }, {
                name: 'oid',
                hidden: true
            }, {
                name: 'mainId',
                hidden: true
            }, {
                name: 'reportFile',
                hidden: true
            }],
            ondblClickRow: function(rowid){ // 當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
				 var data = $("#gridview").getRowData(rowid);
				 download(null, null, data);
            }
        });
    }
    
    function download(cellvalue, options, data){
        $.capFileDownload({
             handler: "simplefiledwnhandler",
            data: {
                fileOid: data.reportFile
            }
        });        
    }
});

