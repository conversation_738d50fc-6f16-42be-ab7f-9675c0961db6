package com.mega.eloan.lms.fms.handler.form;

import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.DocOpener;
import com.mega.eloan.common.model.DocOpener.OpenTypeCode;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.FlowSimplifyService;
import com.mega.eloan.lms.fms.flow.CLS9061Flow;
import com.mega.eloan.lms.fms.pages.CLS9061M01Page;
import com.mega.eloan.lms.fms.service.CLS9061Service;
import com.mega.eloan.lms.model.C900M01E;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;


@Scope("request")
@Controller("cls9061formhandler")
public class CLS9061FormHandler extends AbstractFormHandler {

	@Resource
	CLS9061Service service;

	@Resource
	ICustomerService iCustomerService;
	
	@Resource
	UserInfoService userInfoService;

	@Resource 
	FlowSimplifyService flowSimplifyService;
	
	@Resource
	TempDataService tempDataService;
	
	@Resource
	DocCheckService docCheckService;
	
	Properties prop = MessageBundleScriptCreator
		.getComponentResource(CLS9061M01Page.class);

	Properties prop_AbstractEloanPage = MessageBundleScriptCreator
	.getComponentResource(AbstractEloanPage.class);
	
	@DomainAuth(AuthType.Modify)
	public IResult deleteMark(PageParameters params)
			throws CapException {
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Timestamp nowTS = CapDate.getCurrentTimestamp();
		String KEY = "saveOkFlag";
		
		String list = params.getString("list");
		result.set(KEY, false);
		
		C900M01E meta = null;
		if (Util.isNotEmpty(list)) {
			meta = service.findC900M01E_oid(list);	
			
			if(meta!=null){
				
				List<DocOpener> docOpeners = docCheckService.findByMainId(meta.getMainId());
				for(DocOpener docOpener : docOpeners){
					if(OpenTypeCode.Writing.getCode().equals(docOpener.getOpenType())){
						HashMap<String, String> hm = new HashMap<String, String>();
						hm.put("userId", docOpener.getOpener());
						hm.put("userName",
								userInfoService.getUserName(docOpener.getOpener()));
						throw new CapMessageException(RespMsgHelper.getMessage("EFD0009", hm), getClass());
					}
				}
				
				meta.setDeletedTime(nowTS);
				meta.setUpdater(user.getUserId());
				meta.setUpdateTime(nowTS);
				//---
				service.save(meta);
				flowSimplifyService.flowCancel(meta.getOid());
				
				result.set(KEY, true);
			}			
		}
		return defaultResult(params, meta, result);
	}
	
	@DomainAuth(AuthType.Modify)
	public IResult newC900M01E(PageParameters params)
			throws CapException {
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Timestamp nowTS = CapDate.getCurrentTimestamp();
		
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String custName = Util.trim(params.getString("custName"));
	
		C900M01E meta = new C900M01E();
		meta.setMainId(IDGenerator.getUUID());
		meta.setCustId(custId);
		meta.setDupNo(dupNo);
		meta.setCustName(custName);
		meta.setOwnBrId(user.getUnitNo());
		meta.setUnitType(user.getUnitType());		
		meta.setCreator(user.getUserId());
		meta.setCreateTime(nowTS);
		meta.setUpdater(user.getUserId());
		meta.setUpdateTime(nowTS);
		meta.setSourceNo("");
		meta.setDataSrc("0");
		meta.setIsClosed(false);
		//---
		service.save(meta);
		flowSimplifyService.flowStart(CLS9061Flow.FLOW_CODE, meta.getOid(), user.getUserId(), user.getUnitNo());
		
		return defaultResult(params, meta, result);
	}

	private CapAjaxFormResult defaultResult(PageParameters params, C900M01E meta,
			CapAjaxFormResult result) throws CapException {		
		// required information
		result.set(EloanConstants.PAGE, Util.trim(params.getString(EloanConstants.PAGE)));
		result.set(EloanConstants.MAIN_OID, Util.trim(meta.getOid()));
		result.set(EloanConstants.MAIN_DOC_STATUS, Util.trim(meta.getDocStatus()));
		result.set(EloanConstants.MAIN_ID, Util.trim(meta.getMainId()));		
		return result;
	}
	
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult query(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C900M01E meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = service.findC900M01E_oid(mainOid);	
			
			LMSUtil.addMetaToResult(result, meta, new String[]{"custId", "dupNo", "custName"
					, "comId", "comName", "comCity", "comZip", "comAddr", "comTarget"
					, "sourceNo", "dataSrc", "isClosed"
					, "createTime", "updateTime"});
			result.set("creator", Util.trim(userInfoService.getUserName(meta.getCreator())));
			result.set("updater", Util.trim(userInfoService.getUserName(meta.getUpdater())));
			
			String docStatus = meta.getDocStatus();			
			if(Util.equals(FlowDocStatusEnum.編製中.getCode(), docStatus)){
				docStatus = prop_AbstractEloanPage.getProperty("docStatus.010");
			}else if(Util.equals(FlowDocStatusEnum.待覆核.getCode(), docStatus)){
				docStatus = prop_AbstractEloanPage.getProperty("docStatus.020");
			}else if(Util.equals(FlowDocStatusEnum.已核准.getCode(), docStatus)){
				docStatus = prop_AbstractEloanPage.getProperty("docStatus.030");
			}
			result.set("docStatus", docStatus);
		}

		return defaultResult(params, meta, result);
	}
	
	@DomainAuth(AuthType.Modify)
	public IResult impCustName(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C900M01E meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = service.findC900M01E_oid(mainOid);	
			
			if(meta!=null){
				Map<String, Object> latestData = iCustomerService.findByIdDupNo(meta.getCustId(), meta.getDupNo());
				if(latestData!=null){								
					String cName = Util.trim(latestData.get("CNAME"));
					
					if(Util.isNotEmpty(cName)){					
						meta.setCustName(cName);
						service.save(meta);
					}			
				}
				result.set("custName", meta.getCustName());		
			}			
		}
		
		return defaultResult(params, meta, result);
	}
	@DomainAuth(AuthType.Modify)
	public IResult impComName(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String comId = Util.trim(params.getString("comId"));
		if(Util.isEmpty(comId)){
			HashMap<String, String> msg = new HashMap<String, String>();
			msg.put("colName", prop.getProperty("C900M01E.comId"));

			throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.欄位不得為空, msg), getClass());		
		}
		
		Map<String, Object> latestData = iCustomerService.findByIdDupNo(comId, "0");
		String comName = "";
		if(latestData!=null){								
			comName = Util.trim(latestData.get("CNAME"));
					
		}
		result.set("comName", comName);
		
		return result;
	}
	
	/**
	 * 儲存
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(AuthType.Modify)
	public IResult saveMain(PageParameters params)
			throws CapException {
		return _saveAction(params, "N");
	}
	
	private CapAjaxFormResult _saveAction(PageParameters params,String tempSave)
	throws CapException{
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, tempSave);
		//===
		String KEY = "saveOkFlag";
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set(KEY, false);
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C900M01E meta = null;
		if (Util.isNotEmpty(mainOid)) {
			try{
				meta = service.findC900M01E_oid(mainOid);
				//---
				CapBeanUtil.map2Bean(params, meta, new String[] {"comId", "comName"
						, "comCity", "comZip", "comAddr", "comTarget"
						, "sourceNo", "dataSrc", "isClosed"
				});
				//---
				service.save(meta);				
				result.set(KEY, true);	
			}catch(Exception e){
				logger.error(StrUtils.getStackTrace(e));
				throw new CapException(e, getClass());
			}		
		}
		
		result.add(query(params));
		
		return result;
	}
	
	@DomainAuth(AuthType.Modify + AuthType.Accept)
	public IResult flowAction(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String decisionExpr = Util.trim(params.getString("decisionExpr"));
		
		C900M01E meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = service.findC900M01E_oid(mainOid);
			
			String errMsg = "";
			if(Util.equals("核定", decisionExpr)){
				//檢查經辦和主管是否為同一人
				if(Util.equals(user.getUserId(), meta.getUpdater())){
					errMsg = RespMsgHelper.getMessage("EFD0053");	
				}			
			}
			if(Util.isNotEmpty(errMsg)){
				throw new CapMessageException(errMsg, getClass());
			}
			
			flowSimplifyService.flowNext(meta.getOid(), decisionExpr);
			
			tempDataService.deleteByMainId(meta.getMainId());
			docCheckService.unlockDocByMainIdUser(meta.getMainId(), user.getUserId());
		}
		return defaultResult( params, meta, result);
	}

	@DomainAuth(AuthType.Modify)
	public IResult flowRestart(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Timestamp nowTS = CapDate.getCurrentTimestamp();
		
		String mainOid = params.getString(EloanConstants.MAIN_OID);
				
		C900M01E meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = service.findC900M01E_oid(mainOid);
			if(Util.equals(meta.getDocStatus(), FlowDocStatusEnum.已核准.getCode())){
				meta.setUpdater(user.getUserId());
				meta.setUpdateTime(nowTS);
				flowSimplifyService.flowStart(CLS9061Flow.FLOW_CODE, meta.getOid(), user.getUserId(), user.getUnitNo());
			}
		}
		return defaultResult( params, meta, result);
	}
}
