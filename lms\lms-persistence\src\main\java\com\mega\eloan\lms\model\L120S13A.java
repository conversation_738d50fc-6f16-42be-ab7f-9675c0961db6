package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 個金勞工紓困審核表 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L120S13A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class L120S13A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 額度序號 **/
	@Column(name = "CNTRNO", length = 12, columnDefinition = "CHAR(12)")
	private String cntrNo;

	/** 額度明細表來源mainId **/
	@Column(name = "REFMAINID", length = 32, columnDefinition = "CHAR(32)")
	private String refMainId;
	
	/** 產品種類seq **/
	@Column(name = "REFSEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer refSeq;
	
	/** 職業(可能原本帶出 ○○實業股份有限公司監察人上市或上櫃公司之董監事及主管級人員，太長了，讓經辦可編輯) **/
	@Column(name = "OCCUPATION", length = 120, columnDefinition = "VARCHAR(120)")
	private String occupation;
	
	/** 幣別 **/
	@Column(name = "CURR", length = 3, columnDefinition = "CHAR(3)")
	private String curr;
	
	/** 申貸金額 **/
	@Column(name = "APPLYAMT", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal applyAmt;

	/** 最終評等 **/
	@Column(name = "GRADE1", length = 2, columnDefinition = "CHAR(2)")
	private String grade1;
	
	/** 項目1 **/
	@Column(name = "ITEM010", length = 1, columnDefinition = "CHAR(1)")
	private String item010;
	
	/** 項目2 **/
	@Column(name = "ITEM020", length = 1, columnDefinition = "CHAR(1)")
	private String item020;
	
	/** 項目3 **/
	@Column(name = "ITEM030", length = 1, columnDefinition = "CHAR(1)")
	private String item030;
	
	/** 項目4 **/
	@Column(name = "ITEM040", length = 1, columnDefinition = "CHAR(1)")
	private String item040;
	
	/** 項目5 **/
	@Column(name = "ITEM050", length = 1, columnDefinition = "CHAR(1)")
	private String item050;
	
	/** 項目6 **/
	@Column(name = "ITEM060", length = 1, columnDefinition = "CHAR(1)")
	private String item060;
	
	/** 項目7 **/
	@Column(name = "ITEM070", length = 1, columnDefinition = "CHAR(1)")
	private String item070;
	
	/** 項目8 **/
	@Column(name = "ITEM080", length = 1, columnDefinition = "CHAR(1)")
	private String item080;
	
	/** 項目9 **/
	@Column(name = "ITEM090", length = 1, columnDefinition = "CHAR(1)")
	private String item090;
	
	/** 項目10 **/
	@Column(name = "ITEM100", length = 1, columnDefinition = "CHAR(1)")
	private String item100;
	
	/** 備註說明 **/
	@Column(name = "MEMO", length = 3000, columnDefinition = "VARCHAR(3000)")
	private String memo;
	
	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;
	
	/** 文件版本 **/
	@Column(name = "RPTID", length = 32, columnDefinition = "VARCHAR(32)")
	private String rptId;

	/** 項目11**/
	@Column(name = "ITEM110", length = 1, columnDefinition = "CHAR(1)")
	private String item110;
	
	/** 項目12**/
	@Column(name = "ITEM120", length = 1, columnDefinition = "CHAR(1)")
	private String item120;
	

	public String getOid() {
		return oid;
	}

	public void setOid(String oid) {
		this.oid = oid;
	}

	public String getMainId() {
		return mainId;
	}

	public void setMainId(String mainId) {
		this.mainId = mainId;
	}

	public String getCntrNo() {
		return cntrNo;
	}

	public void setCntrNo(String cntrNo) {
		this.cntrNo = cntrNo;
	}

	public String getRefMainId() {
		return refMainId;
	}

	public void setRefMainId(String refMainId) {
		this.refMainId = refMainId;
	}

	public Integer getRefSeq() {
		return refSeq;
	}

	public void setRefSeq(Integer refSeq) {
		this.refSeq = refSeq;
	}

	public String getOccupation() {
		return occupation;
	}

	public void setOccupation(String occupation) {
		this.occupation = occupation;
	}

	public String getCurr() {
		return curr;
	}

	public void setCurr(String curr) {
		this.curr = curr;
	}

	public BigDecimal getApplyAmt() {
		return applyAmt;
	}

	public void setApplyAmt(BigDecimal applyAmt) {
		this.applyAmt = applyAmt;
	}

	public String getGrade1() {
		return grade1;
	}

	public void setGrade1(String grade1) {
		this.grade1 = grade1;
	}

	public String getItem010() {
		return item010;
	}

	public void setItem010(String item010) {
		this.item010 = item010;
	}

	public String getItem020() {
		return item020;
	}

	public void setItem020(String item020) {
		this.item020 = item020;
	}

	public String getItem030() {
		return item030;
	}

	public void setItem030(String item030) {
		this.item030 = item030;
	}

	public String getItem040() {
		return item040;
	}

	public void setItem040(String item040) {
		this.item040 = item040;
	}

	public String getItem050() {
		return item050;
	}

	public void setItem050(String item050) {
		this.item050 = item050;
	}

	public String getItem060() {
		return item060;
	}

	public void setItem060(String item060) {
		this.item060 = item060;
	}

	public String getItem070() {
		return item070;
	}

	public void setItem070(String item070) {
		this.item070 = item070;
	}

	public String getItem080() {
		return item080;
	}

	public void setItem080(String item080) {
		this.item080 = item080;
	}

	public String getItem090() {
		return item090;
	}

	public void setItem090(String item090) {
		this.item090 = item090;
	}

	public String getItem100() {
		return item100;
	}

	public void setItem100(String item100) {
		this.item100 = item100;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public Timestamp getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Timestamp createTime) {
		this.createTime = createTime;
	}

	public String getUpdater() {
		return updater;
	}

	public void setUpdater(String updater) {
		this.updater = updater;
	}

	public Timestamp getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Timestamp updateTime) {
		this.updateTime = updateTime;
	}

	public String getRptId() {
		return rptId;
	}

	public void setRptId(String rptId) {
		this.rptId = rptId;
	}


	public String getItem110() {
		return item110;
	}

	public void setItem110(String item110) {
		this.item110 = item110;
	}

	public String getItem120() {
		return item120;
	}

	public void setItem120(String item120) {
		this.item120 = item120;
	}
}
