/* 
 *ObsdbELF404ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.obsdb.service.impl;

import java.math.BigDecimal;

import org.springframework.stereotype.Service;

import com.mega.eloan.common.jdbc.AbstractOBSDBJdbcFactory;
import com.mega.eloan.lms.obsdb.service.ObsdbELF404Service;

/**
 * <pre>
 * ELF404
 * </pre>
 * 
 * @since 2012/1/16
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/16,<PERSON>,new
 *          </ul>
 */
@Service
public class ObsdbELF404ServiceImpl extends AbstractOBSDBJdbcFactory implements
		ObsdbELF404Service {
	@Override
	public void insertElf404(String brno, String appryy, String apprmm,
			String ctype, String caseDept, String caseNo, String cntrNo,
			Integer citem1, Integer citem2, Integer citem3, Integer citem4,
			Integer citem5, BigDecimal appramt, BigDecimal time) {
		this.getJdbc(brno).update(
				"ELF404.insert",
				new Object[] { brno, appryy, apprmm, ctype, caseDept, caseNo,
						cntrNo, citem1, citem2, citem3, citem4, citem5,
						appramt, time });
	}

	@Override
	public void updateElf404(String brno, String appryy, String apprmm,
			String ctype, String caseNo, String cntrNo, Integer citem1,
			Integer citem2, Integer citem3, Integer citem4, Integer citem5,
			BigDecimal appramt, String updater, String caseDept, BigDecimal time) {
		this.getJdbc(brno).update(
				"ELF404.update",
				new Object[] { brno, appryy, apprmm, ctype, caseNo, cntrNo,
						citem1, citem2, citem3, citem4, citem5, appramt,
						updater, caseDept, time, brno, appryy, apprmm, ctype,
						caseDept, caseNo, cntrNo });

	}

	@Override
	public boolean selectElf404(String brno, String appryy, String apprmm,
			String ctype, String caseDept, String caseNo, String cntrNo) {
		return this
				.getJdbc(brno)
				.queryForList(
						"ELF404.select",
						new Object[] { brno, appryy, apprmm, ctype, caseDept,
								caseNo, cntrNo }).isEmpty();
	}

	@Override
	public void insertElf4042(String brno, String appryy, String apprmm,
			String ctype, String caseNo, String cntrNo, int citem6, int citem7,
			int citem8, int citem9, int citem10, double appramt,
			String updater, String casedept, BigDecimal time) {
		this.getJdbc(brno).update(
				"ELF404.insert2",
				new Object[] { brno, appryy, apprmm, ctype, caseNo, cntrNo,
						citem6, citem7, citem8, citem9, citem10, appramt,
						updater, casedept, time });

	}

	@Override
	public void updateElf4042(String brno, String appryy, String apprmm,
			String ctype, String caseNo, String cntrNo, int citem6, int citem7,
			int citem8, int citem9, int citem10, double appramt,
			String updater, String caseDept, BigDecimal time) {
		this.getJdbc(brno).update(
				"ELF404.update2",
				new Object[] { brno, appryy, apprmm, ctype, caseNo, cntrNo,
						citem6, citem7, citem8, citem9, citem10, appramt,
						updater, caseDept, time, brno, appryy, apprmm, ctype,
						caseNo, cntrNo });
	}

	@Override
	public boolean selectElf4042(String brno, String appryy, String apprmm,
			String ctype, String caseNo, String cntrNo) {
		return this
				.getJdbc(brno)
				.queryForList(
						"ELF404.select2",
						new Object[] { brno, appryy, apprmm, ctype, caseNo,
								cntrNo }).isEmpty();
	}
}
