$(document).ready(function(){

    var grid = $("#gridview").iGrid({
        handler: 'lms140mgridhandler',
        height: 350,
        width: 785,
        autowidth: false,
        action: "queryL140mm1a",
        postData: {
            docStatus: viewstatus,
			editType: 'U'
        },
        rowNum: 15,
        sortname: "custId",
        sortorder: "desc|desc",
        multiselect: true,
        colModel: [{
            colHeader: i18n.lms140mv01['L140M01M.custId'],//"借款人統編",
            name: 'custId',
            width: 80,
            align: "left",
            sortable: true,
            formatter: 'click',
            onclick: openDoc
        }, {
            colHeader: i18n.lms140mv01['L140M01M.custName'],//"借款人",
            name: 'custName',
            width: 80,
            sortable: true
        }, {
            colHeader: i18n.lms140mv01['L140M01M.cntrNo'],//"額度序號",
            name: 'cntrNo',
            width: 100,
            sortable: true
        }, {
            colHeader: i18n.lms140mv01['L140M01M.creator'],//"分行經辦",
            name: 'creator',
            width: 80,
            sortable: true,
            align: "center"
        }, {
                colHeader: i18n.lms140mv01["L140M01M.createTime"], // 建立日期
                align: "left",
                width: 80, // 設定寬度
                sortable: true, // 是否允許排序
                name: 'createTime',
                formatter: 'date',
                formatoptions: {
                    srcformat: 'Y-m-d H:i:s',
                    newformat: 'Y-m-d H:i'
                }
        }, {
            name: 'oid',
            hidden: true
        }, {
            name: 'mainId',
            hidden: true
        }, {
            name: 'docURL',
            hidden: true
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = $("#gridview").getRowData(rowid);
            openDoc(null, null, data);
        }
    });
	
	var CntrnoGrid = $('#CntrnoGrid').iGrid({
            handler: 'lms140mgridhandler', //設定handler
            height: 400, //設定高度
            action: 'queryGetCntrno', //執行的Method
            postData: {
	            
	        },
            needPager: false,
            rownumbers: true,
            colModel: [{
                colHeader: i18n.lms140mm01["L140M01M.cntrNo"], // 額度序號
                align: "center",
                width: 100, //設定寬度
                sortable: true, //是否允許排序
                name: 'cntrNo'
            }, {
            	name: 'sDate',
            	hidden: true
        	}, {
            	name: 'version',
            	hidden: true
        	}],
            loadComplete: function () {
            	
            	if( CntrnoGrid.getGridParam("records")>0){
            		
            	}else{
					var custId = CntrnoGrid.getGridParam("postData")['custId'];
					if(custId && custId.length>1){
						API.showErrorMessage(i18n.lms140mm01["L140M01M.error6"]);	
					}            		
            	}
            }  
     });
	
    function openDoc(cellvalue, options, rowObject){
        $.form.submit({			
            url: '..' + rowObject.docURL + '/01',
            data: {
                formAction: "queryL140mm1a",
                oid: rowObject.oid,
                mainId: rowObject.mainId,
                mainOid: rowObject.oid,
                mainDocStatus: viewstatus,
                txCode: txCode
            },
            target: rowObject.oid
        });
    }
	
	
    $("#buttonPanel").find("#btnDelete").click(function(){
        var rows = $("#gridview").getGridParam('selarrrow');
        var data = [];
        
        if (rows == "") {// TMMDeleteError=請先選擇需修改(刪除)之資料列
            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
        }
        //confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                for (var i in rows) {
                    data.push($("#gridview").getRowData(rows[i]).oid);
                }
                
                $.ajax({
                    handler: "lms140mm01formhandler",
                    data: {
                        formAction: "deleteL140mm1a",
                        oids: data
                    },
                    success: function(obj){
                        $("#gridview").trigger("reloadGrid");
                    }
                });
            }
        });
    }).end().find("#btnAdd").click(function(){
		chose_custId().done(function(resultFrom_chose_custId){
   	 		chose_cntrNo(resultFrom_chose_custId).done(function(resultFrom_chose_cntrNo){
				chose_version(resultFrom_chose_cntrNo).done(function(resultFrom_chose_version){

					$.ajax({
	                    handler: "lms140mm01formhandler",
	                    action : 'newl140mm1a',
						data : {
							custId:resultFrom_chose_cntrNo.custId,
							dupNo:resultFrom_chose_cntrNo.dupNo,
							custName:resultFrom_chose_cntrNo.custName,
							cntrNo:resultFrom_chose_cntrNo.cntrNo,
							sDate:resultFrom_chose_cntrNo.sDate,
							selectVersion:resultFrom_chose_version.selectVersion,
							editType:"U"
	   	 				},
	                    success: function(obj){
		        			$.form.submit({
	                    		url: '../cls/lms140mm01/01',
	                    		data: {
	                        		formAction: "queryL140mm1a",
	                        		oid: obj.oid,
	                        		mainOid: obj.oid,
	                        		mainDocStatus: viewstatus,
	                        		txCode: txCode
	                    		},
	                    		target: obj.oid
	               	 		});
						}
		    		});
				});
	    	});
	    });
    }).end().find("#btnView").click(function(){
        var id = $("#gridview").getGridParam('selrow');
        if (!id) {
            // action_004=請先選擇需「調閱」之資料列
            return CommonAPI.showMessage(i18n.def["action_004"]);
        }
        if (id.length > 1) {
			// L140M01M.error1=此功能不能多選
            CommonAPI.showMessage(i18n.lms140mm01["L140M01M.error1"]);
        }
        else {
            var result = $("#gridview").getRowData(id);
            openDoc(null, null, result);
        }
    }).end().find("#btnFilter").click(function(){
        openFilterBox();
    }).end().find("#btnUseFirstTable").click(function(){
        openUseFirstTable();
    }).end().find("#btnLogeIN").click(function(){
        openLogeIN();
    }).end().find("#btnDataFix").click(function(){
        var id = $("#gridview").getGridParam('selrow');
        if (!id) {
            // action_004=請先選擇需「調閱」之資料列
            return CommonAPI.showMessage(i18n.def["action_004"]);
        }
        var result = $("#gridview").getRowData(id);
        openCntrCaseBox(result.oid);
    });
	
	
	function chose_custId(){	
		var my_dfd = $.Deferred();
		AddCustAction.open({
	    		handler: 'lms140mm01formhandler',
				action : 'echo_custId',
				data : {
	            },
				callback : function(json){					
	            	// 關掉 AddCustAction 的 
	            	$.thickbox.close();					
					my_dfd.resolve( json );					
				}
			});
		return my_dfd.promise();
	}
	function chose_cntrNo(resultFrom_chose_custId){
		var my_dfd = $.Deferred();
		
		CntrnoGrid.jqGrid("setGridParam", {
            postData: {
                'custId': resultFrom_chose_custId.custId
				,'dupNo': resultFrom_chose_custId.dupNo
            },
            search: true
        }).trigger("reloadGrid");

		$("#CntrnoThickBox").thickbox({
	       title: i18n.lms140mm01["L140M01M.title02"], width: 400,height: 450,align: "center",valign: "bottom",
           modal: false, i18n: i18n.def,
		   buttons: {
                "sure": function(){
					 var data = CntrnoGrid.getSingleData();
                     if (data) {
						 $.thickbox.close();
						 //---
                    	 var cntrNo = data.cntrNo;
						 var sDate = data.sDate;
						 var version = data.version;
        				 my_dfd.resolve($.extend(resultFrom_chose_custId, {'cntrNo':cntrNo, 'sDate':sDate, 'version': version} ));
                     }     	
                },
                "cancel": function(){
                	$.thickbox.close();
                }
            }	
		});	
		
		return my_dfd.promise();
	}
	
	function chose_version(resultFrom_chose_cntrNo){
		
		var my_dfd = $.Deferred();
		
		var version = resultFrom_chose_cntrNo.version;
		var l140mm1aLatestVersion = $("#l140mm1aLatestVersion").val();
		ilog.debug("chose_version @ LMS140MV01Page.js @ 目前版本: " + version + " 最新版本: " + l140mm1aLatestVersion);
		
		if(version == l140mm1aLatestVersion){
			my_dfd.resolve($.extend(resultFrom_chose_cntrNo, {'selectVersion':version} ));
			return my_dfd.promise();
		}
		
		$("#currentVersionDesc").val(i18n.lms140mm01["L140M01M.version.current"] + ':' + i18n.lms140mm01["L140M01M.version." + version]);
		$("#latestVersionDesc").val(i18n.lms140mm01["L140M01M.version.latest"] + ':' + i18n.lms140mm01["L140M01M.version." + l140mm1aLatestVersion]);
		$("#currentVersion").val(version);
		$("#latestVersion").val(l140mm1aLatestVersion);
		
		$("#versionThickBox").thickbox({
	       title: i18n.lms140mm01["L140M01M.text.title.pleaseSelectVersion"], width: 300,height: 150,align: "center",valign: "bottom",
           modal: false, i18n: i18n.def,
		   buttons: {
                "sure": function(){
					var selectVersion = $("[name=selectVersion]:checked").val();
					$.thickbox.close();
        			my_dfd.resolve($.extend(resultFrom_chose_cntrNo, {'selectVersion':selectVersion} ));
                },
                "cancel": function(){
                	$.thickbox.close();
                }
            }	
		});	
		
		return my_dfd.promise();
	}
	
});

