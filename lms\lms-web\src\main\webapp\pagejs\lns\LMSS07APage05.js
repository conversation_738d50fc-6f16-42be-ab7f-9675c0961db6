var oldOid = "";
var _handler = "";
var combos = CommonAPI.loadCombos(["LMSS07_equator01","LMSS07_D1_02","LMSS07_D1_03","LMSS07_D2_02","LMSS07_D3_02","LMSS07_badFaithItem","envAndSociRiskLvl"]);
$(function() {
	
	// J-108-0166 社會與環境風險評估改版
	$("#item1_D1").setItems({
        item: combos.LMSS07_equator01,
		size: "1",
		clear : true
    });
	$("#item1_D2").setItems({
        item: combos.LMSS07_equator01,
		size: "1",
		clear : true
    });
	$("#item1_D3").setItems({
        item: combos.LMSS07_equator01,
		size: "1",
		clear : true
    });
	$("#item2_D1").setItems({
        item: combos.LMSS07_D1_02,
		size: "1",
		clear : true
    });
	$("#item3_D1").setItems({
        item: combos.LMSS07_D1_03,
		size: "1",
		clear : true
    });
	$("#item2_D2").setItems({
        item: combos.LMSS07_D2_02,
		size: "1",
		clear : true
    });
	$("#item2_D3").setItems({
        item: combos.LMSS07_D3_02,
		size: "1",
		clear : true
    });
    $("#badFaithItem").setItems({
        item: combos.LMSS07_badFaithItem,
        size: "1",
        clear : true
    });
    
    //J-113-0442 企金簽報書「社會責任與環境風險評估」新增赤道原則相關欄位
    $("#envAndSociRiskLvl").setItems({
        item: combos.envAndSociRiskLvl,
		size: "1",
		clear : true
    });
			
//	$("#hasD1,#hasD2,#hasD3").change(function(){
	$("[name='hasD1'],[name='hasD2'],[name='hasD3']").change(function(){
		var value = $(this).val();  //取值
		var name = $(this).attr('name');
		name = name.substring(name.length-1);

		if(value == "Y"){
			$("#LMS1205S07Form05" ).find("#jsonD"+name).show();
		}else{
			$("#LMS1205S07Form05" ).find("#jsonD"+name).hide();
		}		
	});
	$("[name='hasESG']").change(function(){
        var value = $(this).val();  //取值

        if(value == "Y"){
            $("#LMS1205S07Form05" ).find(".jsonESG").show();
        }else{
            $("#LMS1205S07Form05" ).find(".jsonESG").hide();
            $("#LMS1205S07Form05" ).find("#esgAgency, #esgGrade").val("");
        }
    });
    $("[name='hasCnLim']").change(function(){
        var value = $(this).val();  //取值

        if(value == "Y"){
            $("#LMS1205S07Form05" ).find("#jsonCnLim").show();
        }else{
            $("#LMS1205S07Form05" ).find("#jsonCnLim").hide();
            $("#LMS1205S07Form05" ).find("#cnLimMemo").val("");
        }
    });
	$("[name='isSustain']").change(function(){
        var value = $(this).val();  //取值

        if(value == "Y"){
            $("#LMS1205S07Form05").find("#sustainLabel").show();
            if(thickboxOptions.readOnly){
                $("#LMS1205S07Form05").find("#sustainMemoPhraseDiv").hide();
            } else {
                $("#LMS1205S07Form05").find("#sustainMemoPhraseDiv").show();
            }
        } else {
            $("#LMS1205S07Form05").find("#sustainLabel").hide();
            $("#LMS1205S07Form05").find("#sustainMemo").val("");
        }
    });

	$("#itemSpan_item1_D1,#itemSpan_item1_D2,#itemSpan_item1_D3").change(function(){
		if($("[name='item1_D1'][value='2']").is(":checked")){
			$("#item1other_D1").show();
		} else {
			$("#item1other_D1").hide();
		}
		if($("[name='item1_D2'][value='2']").is(":checked")){
			$("#item1other_D2").show();
		} else {
			$("#item1other_D2").hide();
		}
		if($("[name='item1_D3'][value='2']").is(":checked")){
			$("#item1other_D3").show();
		} else {
			$("#item1other_D3").hide();
		}
	});
	
	$("#itemSpan_item2_D1,#itemSpan_item2_D2,#itemSpan_item2_D3,#itemSpan_badFaithItem").change(function(){
	    var ver = $("#LMS1205S07Form05").find("#ver").val();
	    if(ver == "04" || ver == "05" || ver == "06" || ver == "07" || ver == "08"){
	        var D1_7_checked = $("[name='item2_D1'][value='7']").is(":checked");
	        if(!thickboxOptions.readOnly){
                $("[name='item2_D1']").each(function(){
                    var value = $(this).val();
                    if(D1_7_checked){
                        if(value != '7'){
                            $(this).removeAttr("checked").attr("disabled", "disabled");
                        }
                    } else {
                        $(this).attr("disabled", false);
                    }
                });
            }
            if(D1_7_checked){
                $("#LMS1205S07Form05").find("[name='hasD1'][value='Y']:radio").removeAttr("checked");
                $("#LMS1205S07Form05").find("[name='hasD1'][value='N']:radio").attr("checked", "checked");
            } else {
                $("#LMS1205S07Form05").find("[name='hasD1'][value='Y']:radio").attr("checked", "checked");
                $("#LMS1205S07Form05").find("[name='hasD1'][value='N']:radio").removeAttr("checked");
            }
        }
		if($("[name='item2_D1'][value='6']").is(":checked")){
			$("#item2other_D1").show();
		} else {
			$("#item2other_D1").hide();
		}

        if(ver == "04" || ver == "05" || ver == "06" || ver == "07" || ver == "08"){
            var D2_8_checked = $("[name='item2_D2'][value='8']").is(":checked");
            if(!thickboxOptions.readOnly){
                $("[name='item2_D2']").each(function(){
                    var value = $(this).val();
                    if(D2_8_checked){
                        if(value != '8'){
                            $(this).removeAttr("checked").attr("disabled", "disabled");
                        }
                    } else {
                        $(this).attr("disabled", false);
                    }
                });
            }
            if(D2_8_checked){
                $("#LMS1205S07Form05").find("[name='hasD2'][value='Y']:radio").removeAttr("checked");
                $("#LMS1205S07Form05").find("[name='hasD2'][value='N']:radio").attr("checked", "checked");
            } else {
                $("#LMS1205S07Form05").find("[name='hasD2'][value='Y']:radio").attr("checked", "checked");
                $("#LMS1205S07Form05").find("[name='hasD2'][value='N']:radio").removeAttr("checked");
            }
        }
		if($("[name='item2_D2'][value='6']").is(":checked")){
			$("#item2other_D2").show();
		} else {
			$("#item2other_D2").hide();
		}

        if(ver == "04" || ver == "05" || ver == "06" || ver == "07" || ver == "08"){
            var D3_6_checked = $("[name='item2_D3'][value='6']").is(":checked");
            if(!thickboxOptions.readOnly){
                $("[name='item2_D3']").each(function(){
                    var value = $(this).val();
                    if(D3_6_checked){
                        if(value != '6'){
                            $(this).removeAttr("checked").attr("disabled", "disabled");
                        }
                    } else {
                        $(this).attr("disabled", false);
                    }
                });
            }
            if(D3_6_checked){
                $("#LMS1205S07Form05").find("[name='hasD3'][value='Y']:radio").removeAttr("checked");
                $("#LMS1205S07Form05").find("[name='hasD3'][value='N']:radio").attr("checked", "checked");
            } else {
                $("#LMS1205S07Form05").find("[name='hasD3'][value='Y']:radio").attr("checked", "checked");
                $("#LMS1205S07Form05").find("[name='hasD3'][value='N']:radio").removeAttr("checked");
            }
        }
		if($("[name='item2_D3'][value='5']").is(":checked")){
			$("#item2other_D3").show();
		} else {
			$("#item2other_D3").hide();
		}

		if(ver == "04" || ver == "05" || ver == "06" || ver == "07" || ver == "08"){
            var badFaithItem_6_checked = $("[name='badFaithItem'][value='6']").is(":checked");
            if(!thickboxOptions.readOnly){
                $("[name='badFaithItem']").each(function(){
                    var value = $(this).val();
                    if(badFaithItem_6_checked){
                        if(value != '6'){
                            $(this).removeAttr("checked").attr("disabled", "disabled");
                        }
                    } else {
                        $(this).attr("disabled", false);
                    }
                });
            }
            if(badFaithItem_6_checked){
                $("#LMS1205S07Form05").find("[name='hasBadFaith'][value='Y']:radio").removeAttr("checked");
                $("#LMS1205S07Form05").find("[name='hasBadFaith'][value='N']:radio").attr("checked", "checked");
            } else {
                $("#LMS1205S07Form05").find("[name='hasBadFaith'][value='Y']:radio").attr("checked", "checked");
                $("#LMS1205S07Form05").find("[name='hasBadFaith'][value='N']:radio").removeAttr("checked");
            }
            if($("[name='badFaithItem'][value='5']").is(":checked")){
                $("#LMS1205S07Form05").find("#badFaithMemo").show();
            } else {
                // $("#badFaithOther").hide();
                $("#LMS1205S07Form05").find("#badFaithMemo").hide();
            }
        }
	});
	
	//自動帶入姓名
    $("#itemId_D1,#itemDupNo_D1").blur(function(){
        var custId = $("#itemId_D1").val();
        var dupNo = $("#itemDupNo_D1").val();
        if ($.trim(custId).length > 0 && $.trim(dupNo).length > 0) {
            $.ajax({
                handler: "lms1401m01formhandler",//inits.fhandle,
                action: "getMisCustData",
                data: {
                    custId: custId,
                    dupNo: dupNo
                },
                success: function(obj){
                    if (!$.isEmptyObject(obj)) {
                        $("#itemName_D1").val(obj.custName);
                    }
                }
            });
        }
    });
    $("#itemId_D2,#itemDupNo_D2").blur(function(){
        var custId = $("#itemId_D2").val();
        var dupNo = $("#itemDupNo_D2").val();
        if ($.trim(custId).length > 0 && $.trim(dupNo).length > 0) {
            $.ajax({
                handler: "lms1401m01formhandler",
                action: "getMisCustData",
                data: {
                    custId: custId,
                    dupNo: dupNo
                },
                success: function(obj){
                    if (!$.isEmptyObject(obj)) {
                        $("#itemName_D2").val(obj.custName);
                    }
                }
            });
        }
    });
    $("#itemId_D3,#itemDupNo_D3").blur(function(){
        var custId = $("#itemId_D3").val();
        var dupNo = $("#itemDupNo_D3").val();
        if ($.trim(custId).length > 0 && $.trim(dupNo).length > 0) {
            $.ajax({
                handler: "lms1401m01formhandler",
                action: "getMisCustData",
                data: {
                    custId: custId,
                    dupNo: dupNo
                },
                success: function(obj){
                    if (!$.isEmptyObject(obj)) {
                        $("#itemName_D3").val(obj.custName);
                    }
                }
            });
        }
    });
	
	$(".hasDeeds").change(function(){		
	   
		var hasDeeds = $("#LMS1205S07Form05" ).find("[name='hasDeeds']:radio:checked").val();  //取值
	  
		if(hasDeeds == "Y"){
			$("#LMS1205S07Form05" ).find("#deedsMemo").show();
		}else{
			$("#LMS1205S07Form05" ).find("#deedsMemo").hide();
		}	
		 
	});	
	
	$(".hasBadFaith").change(function(){		
		   
		var hasBadFaith = $("#LMS1205S07Form05" ).find("[name='hasBadFaith']:radio:checked").val();  //取值
	  
		if(hasBadFaith == "Y"){
			$("#LMS1205S07Form05" ).find("#badFaithMemo").show();
		}else{
			$("#LMS1205S07Form05" ).find("#badFaithMemo").hide();
		}	
		 
	});	
	
	

	
});

//J-109-0077_05097_B1021 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
//1.社會與環境風險(赤道原則)評估、企業誠信經營評估
function setDefaultPrinciples(){
	
	
	
	$("#LMS1205S07Form05" ).find("[name='hasD1'][value='N']:radio").attr( "checked" , "checked" );
	$("#LMS1205S07Form05" ).find("[name='hasD2'][value='N']:radio").attr( "checked" , "checked" );
	$("#LMS1205S07Form05" ).find("[name='hasD3'][value='N']:radio").attr( "checked" , "checked" );
	$("#LMS1205S07Form05" ).find("[name='hasESG'][value='N']:radio").attr( "checked" , "checked" );
	$("#LMS1205S07Form05" ).find("[name='hasLegality'][value='Y']:radio").attr( "checked" , "checked" );
	$("#LMS1205S07Form05" ).find("[name='hasBadFaith'][value='N']:radio").attr( "checked" , "checked" );
	$("#LMS1205S07Form05" ).find("[name='hasCnLim'][value='N']:radio").attr( "checked" , "checked" );
	
	
	$("#LMS1205S07Form05").find(':checkbox, :radio').trigger('change');
	
	//J-111-0107_05097_B1001 Web e-Loan企金增加借戶ESG外部綜合評分資料相關資料。
	applyEsg();   //會變更hasESG

	// J-112-0337 配合授審處，在簽報書及常董會提案稿，社會責任與環境風險評估大項中，增加本行ESG風險評級結果
	applyEsgFa();  // 變更finalAssessment

	applyCesEsg();  // 變更高環境高碳排
	
}

function pullinL120S01Q(obj){
    $.ajax({
        handler: responseJSON["handler"],
        type: "POST",
        action : "deleteL120s01qs",
        dataType: "json",
        data:{
            mainId: responseJSON.mainId
        },
        success : function(json) {
            importL120S01Q();
        }
    });
    /*
    $.ajax({
        handler: responseJSON["handler"],
        action: "chkHasL120s01qs",
        data: {
            mainId: responseJSON.mainId
        },
        success: function(obj){
            if(obj.hasData == "Y"){
                //confirmBeforeDeleteAll=執行時會刪除已存在之資料，是否確定執行？
                CommonAPI.confirmMessage(i18n.def["confirmBeforeDeleteAll"], function(b){
                    if (b) {
                        $.ajax({
                            handler: responseJSON["handler"],
                            type: "POST",
                            action : "deleteL120s01qs",
                            dataType: "json",
                            data:{
                                mainId: responseJSON.mainId
                            },
                            success : function(json) {
                                importL120S01Q();
                            }
                        });
                    }
                });
            } else {
                importL120S01Q();
            }
        }
    })
    */
}

function importL120S01Q(){
    $.ajax({
        handler: responseJSON["handler"],
        type: "POST",
        action : "importL120s01q",
        dataType: "json",
        data:{
            mainId: responseJSON.mainId
        },
        success : function(json) {
            L120s01qGrid.trigger("reloadGrid");
        }
    });
}

var L120s01qGrid = $("#l120s01qGrid").iGrid({
    handler: 'lms1201gridhandler',
    height: 350,
    postData: {
        formAction: "queryL120s01qList"
    },
    rownumbers: true,
//    rowNum: 10,
    needPager: false,
    // multiselect : true,
    colModel: [{
        colHeader: i18n.lmss07a["L120S01Q.custId"],
        align: "left",
        width: 100, // 設定寬度
        sortable: true, // 是否允許排序
        formatter: 'click',
        onclick: openS01qDoc,
        name: 'custId'
    }, {
        colHeader: i18n.lmss07a["L120S01Q.dupNo"],
        align: "left",
        width: 10, // 設定寬度
        name: 'dupNo'
    }, {
        colHeader: i18n.lmss07a["L120S01Q.custName"],
        align: "left",
        width: 100, // 設定寬度
        name: 'custName'
    }, {
        colHeader: " ",
        name: 'checkYN',
        align: 'center',
        width: 5,
        formatter: function(value){
            if(value == "Y"){
                return "O";
            }else if(value == "N"){
                return "X";
            }else{
                return "X"; //value;
            }
        }
    }, {
        colHeader: "oid",
        name: 'oid',
        hidden: true
    }],
    ondblClickRow: function(rowid){ // 當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
        var data = $("#l120s01qGrid").getRowData(rowid);
        openS01qDoc(null, null, data);
    }
}).trigger("reloadGrid");

function openS01qDoc(cellvalue, options, data){
    $.ajax({
        handler: responseJSON["handler"],
        action: "queryL120s01q",
        data: {
            mainId: responseJSON.mainId,
            oid: data.oid
        },
        success: function(obj){
            setLMS1205S07Form05(obj);
//                $("#LMS1205S07Form05").injectData(obj);
        }
    }).done(function(){
        $("#form05detailDiv").thickbox({
            title: "",
            width: 1000,   //J-112-0063_05097_B1001 Web e-Loan配合集保結算所網站更新，增加moody ESG評分
            height: 500,
            modal : true,
            buttons: {
                "saveData": function() {
                    if(!$("#LMS1205S07Form05").valid()){
                        return false;
                    }
                    $.ajax({
                        handler: responseJSON["handler"],//"lms1201formhandler",
                        type: "POST",
                        action : "saveL120s01q",
                        async : false,
                        dataType: "json",
                        data:{
                            mainId: responseJSON.mainId,
                            oid: data.oid,
                            LMS1205S07Form05: JSON.stringify($("#LMS1205S07Form05").serializeData())
                        },
                        success : function(json) {
                        	//J-113-0442 新增赤道原則相關欄位檢核
                        	if(json.epsCheckMsg){
                        		API.showErrorMessage(json.epsCheckMsg);
                        	}else{
                        		API.showMessage(i18n.def['saveSuccess']);
                        	}
                            L120s01qGrid.trigger("reloadGrid");
                        }
                    });
                },
                "close": function() {
                    $.thickbox.close();
                }
            }
        });
    });
}

function setLMS1205S07Form05(obj){
    var LMS1205S07Form05 = $("#LMS1205S07Form05");
    LMS1205S07Form05.setData(obj.LMS1205S07Form05);
    LMS1205S07Form05.find(".hasDeeds").trigger("change");
    LMS1205S07Form05.find(".hasBadFaith").trigger("change");
    if(obj.LMS1205S07Form05.ver == "01"){
        $("#LMS1205S07Form05").find("#ver1").show();
        $("#LMS1205S07Form05").find("#ver2").hide();
        $("#LMS1205S07Form05").find(".ver3").hide();
        $("#form05detailDiv").find(".ver4Hide").hide();
        $("#form05detailDiv").find(".ver4Show").hide();
        $("#form05detailDiv").find(".ver5Show").hide();
        $("#form05detailDiv").find(".ver6Show").hide();
        $("#form05detailDiv").find(".ver7Show").hide();
        if(!thickboxOptions.readOnly){
            $("#LMS1205S07Form05").find("[name='hasD1']").attr("disabled", false);
            $("#LMS1205S07Form05").find("[name='hasD2']").attr("disabled", false);
            $("#LMS1205S07Form05").find("[name='hasD3']").attr("disabled", false);
            $("#LMS1205S07Form05").find("[name='hasBadFaith']").attr("disabled", false);
        }
        $("#LMS1205S07Form05").find(".jsonESG").hide();
        $("#LMS1205S07Form05").find("#jsonCnLim").hide();
        $("#LMS1205S07Form05").find("#sustainLabel").hide();
        $("#LMS1205S07Form05").find("#sustainMemo").val("");
        $("#LMS1205S07Form05").find("#sustainMemoPhraseDiv").hide();
    } else {
        var item1_D1 = obj.LMS1205S07Form05.item1_D1.split("|");
        for (var i = 0; i < item1_D1.length; i++) {
            var val = item1_D1[i];
            $("[name='item1_D1'][value=" + val + "]").attr("checked", true);
        }
        var item1_D2 = obj.LMS1205S07Form05.item1_D2.split("|");
        for (var i = 0; i < item1_D2.length; i++) {
            var val = item1_D2[i];
            $("[name='item1_D2'][value=" + val + "]").attr("checked", true);
        }
        var item1_D3 = obj.LMS1205S07Form05.item1_D3.split("|");
        for (var i = 0; i < item1_D3.length; i++) {
            var val = item1_D3[i];
            $("[name='item1_D3'][value=" + val + "]").attr("checked", true);
        }
        var item2_D1 = obj.LMS1205S07Form05.item2_D1.split("|");
        for (var i = 0; i < item2_D1.length; i++) {
            var val = item2_D1[i];
            $("[name='item2_D1'][value=" + val + "]").attr("checked", true);
        }
        var item2_D2 = obj.LMS1205S07Form05.item2_D2.split("|");
        for (var i = 0; i < item2_D2.length; i++) {
            var val = item2_D2[i];
            $("[name='item2_D2'][value=" + val + "]").attr("checked", true);
        }
        var item2_D3 = obj.LMS1205S07Form05.item2_D3.split("|");
        for (var i = 0; i < item2_D3.length; i++) {
            var val = item2_D3[i];
            $("[name='item2_D3'][value=" + val + "]").attr("checked", true);
        }

        $("#LMS1205S07Form05").find("#itemSpan_item1_D1,#itemSpan_item1_D2,#itemSpan_item1_D3").trigger("change");
        $("#LMS1205S07Form05").find("#itemSpan_item2_D1,#itemSpan_item2_D2,#itemSpan_item2_D3").trigger("change");

        if(obj.LMS1205S07Form05.ver == "04" || obj.LMS1205S07Form05.ver == "05" || obj.LMS1205S07Form05.ver == "06" || obj.LMS1205S07Form05.ver == "07" || obj.LMS1205S07Form05.ver == "08"){
            var badFaithItem = obj.LMS1205S07Form05.badFaithItem.split("|");
            for (var i = 0; i < badFaithItem.length; i++) {
                var val = badFaithItem[i];
                $("[name='badFaithItem'][value=" + val + "]").attr("checked", true);
            }
            $("#LMS1205S07Form05").find("#itemSpan_badFaithItem").trigger("change");
            $("#LMS1205S07Form05").find("#jsonD1").show();
            $("#LMS1205S07Form05").find("#jsonD2").show();
            $("#LMS1205S07Form05").find("#jsonD3").show();
            if(!thickboxOptions.readOnly){
                $("#LMS1205S07Form05").find("input[name='item2_D1'][value='7']").attr("disabled", false);
                $("#LMS1205S07Form05").find("input[name='item2_D2'][value='8']").attr("disabled", false);
                $("#LMS1205S07Form05").find("input[name='item2_D3'][value='6']").attr("disabled", false);
            }
        } else {
            if(!thickboxOptions.readOnly){
                $("#LMS1205S07Form05").find("input[name='item2_D1'][value='7']").attr("disabled", true);
                $("#LMS1205S07Form05").find("input[name='item2_D2'][value='8']").attr("disabled", true);
                $("#LMS1205S07Form05").find("input[name='item2_D3'][value='6']").attr("disabled", true);
            }

            if(obj.LMS1205S07Form05.hasD1 == "Y"){
                $("#LMS1205S07Form05").find("#jsonD1").show();
            } else {
                $("#LMS1205S07Form05").find("#jsonD1").hide();
            }
            if(obj.LMS1205S07Form05.hasD2 == "Y"){
                $("#LMS1205S07Form05").find("#jsonD2").show();
            } else {
                $("#LMS1205S07Form05").find("#jsonD2").hide();
            }
            if(obj.LMS1205S07Form05.hasD3 == "Y"){
                $("#LMS1205S07Form05").find("#jsonD3").show();
            } else {
                $("#LMS1205S07Form05").find("#jsonD3").hide();
            }
        }

        $("#LMS1205S07Form05").find("#ver2").show();
        $("#LMS1205S07Form05").find("#ver1").hide();

        if(obj.LMS1205S07Form05.ver == "03"){
//            $("#LMS1205S07Form05").find("#ver3").show();
            $("#LMS1205S07Form05").find(".ver3").show();
            $("#form05detailDiv").find(".ver4Hide").show();
            $("#form05detailDiv").find(".ver4Show").hide();
            $("#form05detailDiv").find(".ver5Show").hide();
            $("#form05detailDiv").find(".ver6Show").hide();
            $("#form05detailDiv").find(".ver7Show").hide();
            if(!thickboxOptions.readOnly){
                $("#LMS1205S07Form05").find("[name='hasD1']").attr("disabled", false);
                $("#LMS1205S07Form05").find("[name='hasD2']").attr("disabled", false);
                $("#LMS1205S07Form05").find("[name='hasD3']").attr("disabled", false);
                $("#LMS1205S07Form05").find("[name='hasBadFaith']").attr("disabled", false);
            }
            if(obj.LMS1205S07Form05.hasESG == "Y"){
                $("#LMS1205S07Form05").find(".jsonESG").show();
            } else {
                $("#LMS1205S07Form05").find(".jsonESG").hide();
                $("#LMS1205S07Form05").find("#esgAgency, #esgGrade").val("");
            }
            if(obj.LMS1205S07Form05.hasCnLim == "Y"){
                $("#LMS1205S07Form05").find("#jsonCnLim").show();
            } else {
                $("#LMS1205S07Form05").find("#jsonCnLim").hide();
                $("#LMS1205S07Form05").find("#cnLimMemo").val("");
            }
            $("#LMS1205S07Form05").find("#sustainLabel").hide();
            $("#LMS1205S07Form05").find("#sustainMemo").val("");
            $("#LMS1205S07Form05").find("#sustainMemoPhraseDiv").hide();
        } else if(obj.LMS1205S07Form05.ver == "04" || obj.LMS1205S07Form05.ver == "05" || obj.LMS1205S07Form05.ver == "06" || obj.LMS1205S07Form05.ver == "07" || obj.LMS1205S07Form05.ver == "08"){
            $("#LMS1205S07Form05").find(".ver3").show();
            $("#form05detailDiv").find(".ver4Hide").hide();
            $("#form05detailDiv").find(".ver4Show").show();
            $("#form05detailDiv").find(".ver5Show").hide();
            $("#form05detailDiv").find(".ver6Show").hide();
            $("#form05detailDiv").find(".ver7Show").hide();
            if(obj.LMS1205S07Form05.ver == "05" || obj.LMS1205S07Form05.ver == "06" || obj.LMS1205S07Form05.ver == "07" || obj.LMS1205S07Form05.ver == "08"){
                $("#form05detailDiv").find(".ver5Show").show();
                if(obj.LMS1205S07Form05.ver == "06" || obj.LMS1205S07Form05.ver == "07" || obj.LMS1205S07Form05.ver == "08"){
                    $("#form05detailDiv").find(".ver6Show").show();
                    if(obj.LMS1205S07Form05.ver == "07" || obj.LMS1205S07Form05.ver == "08"){
                        $("#form05detailDiv").find(".ver7Show").show();
                        if (obj.LMS1205S07Form05.hasSustainEval == "Y") {
                            $("#form05detailDiv").find("#seDiv_Y").show();
                            $("#form05detailDiv").find("#seDiv_N").hide();
                        } else {
                            $("#form05detailDiv").find("#seDiv_Y").hide();
                            $("#form05detailDiv").find("#seDiv_N").show();
                        }
                        
                        //J-113-0442 企金簽報書「社會責任與環境風險評估」新增赤道原則相關欄位
                        if(obj.LMS1205S07Form05.ver == "08"){
                        	$("#form05detailDiv").find(".ver8Show").show();
                        	var epsFlag = $('input:radio:checked[name="epsFlag"]').val();
                        	if(epsFlag == "Y"){
                        		$("#form05detailDiv").find(".epsFlag_Y_Show").show();
                        		var industryNo = obj.LMS1205S07Form05.industryNo;
                        		if(industryNo == "05"){
                        			$("#form05detailDiv").find(".isIndustryNo_05_show").show();
                        			$("#form05detailDiv").find(".isIndustryNo_06_show").hide();
                        		}else if(industryNo == "06"){
                        			$("#form05detailDiv").find(".isIndustryNo_05_show").hide();
                        			$("#form05detailDiv").find(".isIndustryNo_06_show").show();
                        		}
                        	}else{
                        		$("#form05detailDiv").find(".epsFlag_Y_Show").hide();
                        		$("#form05detailDiv").find(".isIndustryNo_05_show").hide();
                    			$("#form05detailDiv").find(".isIndustryNo_06_show").hide();
                        	}
                        }
                    }
                }
            }
            if(!thickboxOptions.readOnly){
                $("#LMS1205S07Form05").find("[name='hasD1']").attr("disabled", true);
                $("#LMS1205S07Form05").find("[name='hasD2']").attr("disabled", true);
                $("#LMS1205S07Form05").find("[name='hasD3']").attr("disabled", true);
                $("#LMS1205S07Form05").find("[name='hasBadFaith']").attr("disabled", true);
                //$("#LMS1205S07Form05").find(".ver4Lock").attr("disabled", true);
                if(obj.LMS1205S07Form05.ver == "05" || obj.LMS1205S07Form05.ver == "06" || obj.LMS1205S07Form05.ver == "07" || obj.LMS1205S07Form05.ver == "08"){
                    $("#LMS1205S07Form05").find("[name='isHighEnv']").attr("disabled", true);
                    $("#LMS1205S07Form05").find("[name='isHighCarbonEms']").attr("disabled", true);
                    $("#LMS1205S07Form05").find("[name='isDeCarbonEms']").attr("disabled", true);
                    $("#LMS1205S07Form05").find("[name='isSustain']").attr("disabled", true);
                }
                //J-113-0442 企金簽報書「社會責任與環境風險評估」新增赤道原則相關欄位
                $("#LMS1205S07Form05").find("[name='epsFlag']").attr("disabled", true);
                $("#LMS1205S07Form05").find("[name='isIndustryNo_05']").attr("disabled", true);
                $("#LMS1205S07Form05").find("[name='isIndustryNo_06']").attr("disabled", true);
            }
            if(obj.LMS1205S07Form05.hasESG == "Y"){
                $("#LMS1205S07Form05").find(".jsonESG").show();
            } else {
                $("#LMS1205S07Form05").find(".jsonESG").hide();
                $("#LMS1205S07Form05").find("#esgAgency, #esgGrade").val("");
            }
            if(obj.LMS1205S07Form05.hasCnLim == "Y"){
                $("#LMS1205S07Form05").find("#jsonCnLim").show();
            } else {
                $("#LMS1205S07Form05").find("#jsonCnLim").hide();
                $("#LMS1205S07Form05").find("#cnLimMemo").val("");
            }
            if(obj.LMS1205S07Form05.isSustain == "Y"){
                $("#LMS1205S07Form05").find("#sustainLabel").show();
                if(thickboxOptions.readOnly){
                    $("#LMS1205S07Form05").find("#sustainMemoPhraseDiv").hide();
                } else {
                    $("#LMS1205S07Form05").find("#sustainMemoPhraseDiv").show();
                }
            } else {
                $("#LMS1205S07Form05").find("#sustainLabel").hide();
                $("#LMS1205S07Form05").find("#sustainMemo").val("");
                $("#LMS1205S07Form05").find("#sustainMemoPhraseDiv").hide();
            }
        } else {
            $("#LMS1205S07Form05").find(".ver3").hide();
            $("#form05detailDiv").find(".ver4Hide").show();
            $("#form05detailDiv").find(".ver4Show").hide();
            $("#form05detailDiv").find(".ver5Show").hide();
            $("#form05detailDiv").find(".ver6Show").hide();
            $("#form05detailDiv").find(".ver7Show").hide();
            if(!thickboxOptions.readOnly){
                $("#LMS1205S07Form05").find("[name='hasD1']").attr("disabled", false);
                $("#LMS1205S07Form05").find("[name='hasD2']").attr("disabled", false);
                $("#LMS1205S07Form05").find("[name='hasD3']").attr("disabled", false);
                $("#LMS1205S07Form05").find("[name='hasBadFaith']").attr("disabled", false);
            }
            $("#LMS1205S07Form05").find(".jsonESG").hide();
            $("#LMS1205S07Form05").find("#esgAgency, #esgGrade").val("");
            $("#LMS1205S07Form05").find("#jsonCnLim").hide();
            $("#LMS1205S07Form05").find("#cnLimMemo").val("");
            $("#LMS1205S07Form05").find("#sustainLabel").hide();
            $("#LMS1205S07Form05").find("#sustainMemo").val("");
            $("#LMS1205S07Form05").find("#sustainMemoPhraseDiv").hide();
        }
    }
}

// J-112-0337 配合授審處，在簽報書及常董會提案稿，社會責任與環境風險評估大項中，增加本行ESG風險評級結果
function applyEsgFa(){
    $.ajax({
        handler: responseJSON["handler"],
        type: "POST",
        action : "applyEsgFa",
        async: false ,  //非同步
        dataType: "json",
        data:{
            mainId: responseJSON.mainId,
            custId: $("#LMS1205S07Form05").find("#custId_l120s01q").val(),
            dupNo:  $("#LMS1205S07Form05").find("#dupNo_l120s01q").val()
        },
        success : function(json) {
        	$("#LMS1205S07Form05").injectData(json);
        }
    });
}

function applyCesEsg(){
    $.ajax({
        handler: responseJSON["handler"],
        type: "POST",
        action : "applyCesEsg",
        async: false ,  //非同步
        dataType: "json",
        data:{
            mainId: responseJSON.mainId,
            custId: $("#LMS1205S07Form05").find("#custId_l120s01q").val(),
            dupNo:  $("#LMS1205S07Form05").find("#dupNo_l120s01q").val()
        },
        success : function(json) {
        	$("#LMS1205S07Form05").injectData(json);
        	if(json.isSustain == "Y"){
                $("#LMS1205S07Form05").find("#sustainLabel").show();
            } else {
                $("#LMS1205S07Form05").find("#sustainLabel").hide();
                $("#LMS1205S07Form05").find("#sustainMemo").val("");
            }
        	if(json.msg){
                return CommonAPI.showMessage(json.msg);
            } else {
                return CommonAPI.showMessage(i18n.def["runSuccess"]);
            }
        }
    });
}

function applyEsgSbti(){
     $.ajax({
         handler: responseJSON["handler"],
         type: "POST",
         action : "applyEsgSbti",
         async: false ,  //非同步
         dataType: "json",
         data:{
             mainId: responseJSON.mainId,
             custId: $("#LMS1205S07Form05").find("#custId_l120s01q").val(),
             dupNo:  $("#LMS1205S07Form05").find("#dupNo_l120s01q").val()
         },
         success : function(json) {
             $("#LMS1205S07Form05").injectData(json);
         }
     });
 }

 function applySustainEval(){
    $.ajax({
        handler: responseJSON["handler"],
        type: "POST",
        action : "applySustainEval",
        async: false ,  //非同步
        dataType: "json",
        data:{
            mainId: responseJSON.mainId,
            custId: $("#LMS1205S07Form05").find("#custId_l120s01q").val(),
            dupNo:  $("#LMS1205S07Form05").find("#dupNo_l120s01q").val()
        },
        success : function(json) {
            $("#LMS1205S07Form05").injectData(json);
            if (json.hasSustainEval == "Y") {
                $("#seDiv_Y").show();
                $("#seDiv_N").hide();
            } else {
                $("#seDiv_Y").hide();
                $("#seDiv_N").show();
            }
        }
    });
}

//J-111-0107_05097_B1001 Web e-Loan企金增加借戶ESG外部綜合評分資料相關資料。
function applyEsg(){
    $.ajax({
        handler: responseJSON["handler"],
        type: "POST",
        action : "applyEsg",
        async: false ,  //非同步
        dataType: "json",
        data:{
            mainId: responseJSON.mainId,
            custId: $("#LMS1205S07Form05").find("#custId_l120s01q").val(),
            dupNo:  $("#LMS1205S07Form05").find("#dupNo_l120s01q").val()
        },
        success : function(json) {
        	$("#LMS1205S07Form05").injectData(json);
        }
    });
}
