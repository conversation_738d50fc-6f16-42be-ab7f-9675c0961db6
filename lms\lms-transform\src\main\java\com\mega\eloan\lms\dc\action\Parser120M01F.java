package com.mega.eloan.lms.dc.action;

import org.apache.commons.lang.StringUtils;
import org.w3c.dom.Document;

import com.mega.eloan.lms.dc.base.DCException;
import com.mega.eloan.lms.dc.bean.L120M01FBean;
import com.mega.eloan.lms.dc.util.Util;

/**
 * <pre>
 * Parser120M01F
 * </pre>
 * 
 * @since 2013/04/12
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/04/12,<PERSON>,new
 *          </ul>
 */
public class Parser120M01F extends AbstractLMSCustParser {

	interface BRANCHTYPE {
		String 分行 = "1";
		String 母行 = "2";
		String 營運中心 = "3";
		String 授管處 = "4";
		String 徵信 = "5";
	}

	interface STAFFJOB {
		String L1 = "L1";
		String L2 = "L2";
		String L3 = "L3";
		String L4 = "L4";
		String L5 = "L5";
		String L9 = "L9";

		String C1 = "C1";
		String C2 = "C2";
		String C4 = "C4";
		String C5 = "C5";
	}

	public static final String SPLIT = "、";

	/**
	 * @param pid
	 * @param doViewName
	 * @param formGroup
	 */
	public Parser120M01F(String pid, String doViewName, String formGroup) {
		super(pid, doViewName, formGroup);
	}

	/**
	 * 讀取,處理及轉換
	 * 
	 * @param dxlPath
	 *            String : .dxl檔存放路徑
	 * @param dxlName
	 *            :.dxl列表中的.dxl檔名
	 * @param strBrn
	 *            String:分行名稱
	 * @param domDoc
	 *            DOM Document:已轉為DOM Document的.dxl檔
	 */
	@SuppressWarnings("unused")
	protected void transferDXL(String dxlPath, String dxlName, String strBrn,
			Document domDoc, String dxlXml) {
		long t1 = System.currentTimeMillis();
		try {

			String mainId = getItemValue(domDoc, "UnidDocID");

			String grant = getItemValue(domDoc, "grant");
			String branch_id = getItemValue(domDoc, "branch_id");
			String gMothBch = getItemValue(domDoc, "gMothBch");
			String AreaBch = getItemValue(domDoc, "AreaBch");
			String CES_Branch_ID = getItemValue(domDoc, "CES_Branch_ID");
			String CES_framing_date = getItemValue(domDoc, "CES_framing_date");

			// branchType = 1 ...............................................
			String[] job1 = { STAFFJOB.L1, STAFFJOB.L2, STAFFJOB.L3,
					STAFFJOB.L4, STAFFJOB.L5, STAFFJOB.L9 };
			String[] id1 = { "ApprID", "AO_id", "BossID", "ReCheckID",
					"ManagerID", "UNIT_MANAGERID" };
			String[] name1 = { "appraiser", "AO", "Boss", "ReCheck", "Manager",
					"UNIT_MANAGER" };
			parseStaff(domDoc, mainId, BRANCHTYPE.分行, branch_id, "", job1, id1,
					name1);

			// branchType = 2 ...............................................
			String[] job2 = { STAFFJOB.L4, STAFFJOB.L5, STAFFJOB.L9 };
			String[] id2 = { "MO_ReCheckID", "MO_ReCheckID_1",
					"MO_UNIT_MANAGERID" };
			String[] name2 = { "MO_ReCheck", "MO_ReCheck_1", "MO_UNIT_MANAGER" };
			parseStaff(domDoc, mainId, BRANCHTYPE.母行, gMothBch, "", job2, id2,
					name2);

			// branchType = 3 ...............................................
			String[] job3 = { STAFFJOB.L1, STAFFJOB.L4 };
			String[] id3 = { "ApprID_A", "ReCheckID_A" };
			String[] name3 = { "Appraiser_A", "ReCheck_A" };
			parseStaff(domDoc, mainId, BRANCHTYPE.營運中心, AreaBch, "", job3, id3,
					name3);

			// branchType = 4 ...............................................
			String[] job4 = { STAFFJOB.L9 };
			String[] id4 = { "UNIT_MANAGERID_S" };
			String[] name4 = { "UNIT_MANAGER_S" };
			parseStaff(domDoc, mainId, BRANCHTYPE.營運中心, "918", "", job4, id4,
					name4);

			// branchType = 5 ...............................................
			String[] job5 = { STAFFJOB.C1, STAFFJOB.C2, STAFFJOB.C4,
					STAFFJOB.C4, STAFFJOB.C5 };
			String[] id5 = { "CES_ApprID", "CES_RecheckID", "CES_BossID",
					"CES_Boss_KID", "CES_UNIT_MANAGERID" };
			String[] name5 = { "CES_appraiser", "CES_ReCheck", "CES_Boss",
					"CES_Boss_K", "CES_UNIT_MANAGER" };
			parseStaff(domDoc, mainId, BRANCHTYPE.徵信, CES_Branch_ID,
					CES_framing_date, job5, id5, name5);

			// 特別處理 ...................................................
			if ("2".equals(grant)) {
				// 3.營運中心(grant =2 營運中心授權內)
				String[] job6 = { STAFFJOB.L1, STAFFJOB.L4, STAFFJOB.L9 };
				String[] id6 = { "HQ_ApprID", "HQ_ReCheckID",
						"UNIT_MANAGERID_A" };
				String[] name6 = { "HQ_Appraiser", "HQ_ReCheck",
						"UNIT_MANAGER_A" };
				parseStaff(domDoc, mainId, BRANCHTYPE.營運中心, AreaBch, "", job6,
						id6, name6);
			} else if ("3".equals(grant) || "4".equals(grant)) {
				String[] job8 = { STAFFJOB.L1, STAFFJOB.L4 };
				String[] id8 = { "HQ_ApprID", "HQ_ReCheckID" };
				String[] name8 = { "HQ_Appraiser", "HQ_ReCheck" };
				parseStaff(domDoc, mainId, BRANCHTYPE.授管處, "918", "", job8,
						id8, name8);
				if ("4".equals(grant)) {
					parseStaff(domDoc, mainId, BRANCHTYPE.營運中心, AreaBch, "",
							new String[] { STAFFJOB.L9 },
							new String[] { "UNIT_MANAGER_A" },
							new String[] { "UNIT_MANAGER_A" });
				}
			}

		} catch (Exception e) {
			String errmsg = "【" + strBrn
					+ "】分行執行Parser120M01F 之transferDXL時產生錯誤,dxl檔名:" + dxlName
					+ ",dxlPath=" + dxlPath;
			throw new DCException(errmsg, e);
		} finally {
			if (DEBUG && logger.isDebugEnabled()) {
				logger.debug("@@@@@@@@ TOTAL_COST="
						+ (System.currentTimeMillis() - t1));
			}
		}
	}

	/**
	 * 解析簽章檔
	 * 
	 * @param domDoc
	 * @param mainId
	 * @param branchType
	 * @param branchId
	 * @param cesDate
	 * @param job
	 * @param idTag
	 * @param nameTag
	 * @throws Exception
	 */
	private void parseStaff(Document domDoc, String mainId, String branchType,
			String branchId, String cesDate, String[] job, String[] idTag,
			String[] nameTag) throws Exception {
		for (int i = 0; i < job.length; i++) {
			String id = this.getItemValue(domDoc, idTag[i]);
			String name = this.getItemValue(domDoc, nameTag[i]);

			// 已下幾個欄位會有多欄位值，需特別處理
			if ("Boss;CES_ReCheck;CES_Boss;CES_Boss_K".toUpperCase().contains(
					nameTag[i].toUpperCase())) {
				String[] ids = Util.split(id, SPLIT);
				String[] names = Util.split(name, SPLIT);
				for (int j = 0; j < names.length; j++) {
					print(mainId, branchType, branchId, job[i],
							getData(ids, j), getData(names, j), cesDate);
				}
			} else {
				print(mainId, branchType, branchId, job[i], id, name, cesDate);
			}

		}
	}

	/**
	 * 產生文字檔
	 * 
	 * @param mainId
	 * @param branchType
	 * @param branchId
	 * @param staffJob
	 * @param staffNo
	 * @param staffName
	 * @param cesDate
	 */
	private void print(String mainId, String branchType, String branchId,
			String staffJob, String staffNo, String staffName, String cesDate) {
		L120M01FBean bean = new L120M01FBean();
		bean.setOid(Util.getOID());
		bean.setMainId(mainId);

		bean.setBranchType(branchType);
		bean.setBranchId(branchId);
		bean.setStaffJob(staffJob);
		bean.setStaffNo(staffNo);
		bean.setStaffName(staffName);
		bean.setCesDate(cesDate);

		// staffNo不是英數字則直接轉成空白
		if (!Util.isEnOrNum(bean.getStaffNo())) {
			bean.setStaffNo("");
		}

		// staffNo 或 staffName 只要有一個不是空白則轉入資料庫
		if (StringUtils.isNotBlank(bean.getStaffNo())
				|| StringUtils.isNotBlank(bean.getStaffName())) {
			this.txtWrite.println(bean.toString());
			this.parserTotal++;
		}
	}

	/**
	 * 取得資料
	 * 
	 * @param arys
	 * @param num
	 * @return
	 */
	private String getData(String[] arys, int num) {
		try {
			String value = arys[num];
			return value;
		} catch (Exception e) {
			return "";
		}
	}
}