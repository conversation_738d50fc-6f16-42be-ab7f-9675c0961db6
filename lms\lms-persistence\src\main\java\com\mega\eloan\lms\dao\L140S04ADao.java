/* 
 * L140S04ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140S04A;

/** 額度明細表明細檔 **/
public interface L140S04ADao extends IGenericDao<L140S04A> {

	L140S04A findByOid(String oid);
	
	List<L140S04A> findByMainId(String mainId);

	List<L140S04A> findByMainIdWithItemType(String mainId, String itemType);
}