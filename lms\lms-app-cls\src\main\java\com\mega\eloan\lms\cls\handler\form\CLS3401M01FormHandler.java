package com.mega.eloan.lms.cls.handler.form;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.DocLogEnum;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.ClsUtility;
import com.mega.eloan.lms.base.common.ContractDocUtil;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.ContractDocConstants;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.ContractDocService;
import com.mega.eloan.lms.cls.pages.CLS3401M01Page;
import com.mega.eloan.lms.cls.pages.CLS3401M03Page;
import com.mega.eloan.lms.cls.pages.CLS3401M04Page;
import com.mega.eloan.lms.cls.service.CLS1141Service;
import com.mega.eloan.lms.cls.service.CLS1151Service;
import com.mega.eloan.lms.cls.service.CLS3401Service;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.model.C340M01A;
import com.mega.eloan.lms.model.C340M01B;
import com.mega.eloan.lms.model.C340M01C;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140S01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 消金契約書 => 對應 ContractDocConstants.C340M01A_CtrType.Type_1 或 Type_2 或 Type_3
 * </pre>
 * 
 * @since 2020/02/14
 * <AUTHOR>
 * @version <ul>
 *          <li>2020/02/14,EL08034,new
 *          </ul>
 */
@Scope("request")
@Controller("cls3401m01formhandler")
@DomainClass(C340M01A.class)
public class CLS3401M01FormHandler extends AbstractFormHandler {
	
	@Resource
	BranchService branchService;
	
	@Resource
	ContractDocService contractDocService;
	
	@Resource
	CLSService clsService;
	
	@Resource
	DocCheckService docCheckService;
	
	@Resource
	DocLogService docLogService;
	
	@Resource
	ICustomerService iCustomerService;
	
	@Resource
	TempDataService tempDataService;
	
	@Resource
	UserInfoService userInfoService;
	
	@Resource
	EloandbBASEService eloandbBASEService;

	@Resource
	CLS1151Service cls1151Service;

	@Resource
	CLS1141Service cls1141Service;
	
	@Resource
	CLS3401Service cls3401Service;
	
	Properties prop_cls3401m01 = MessageBundleScriptCreator.getComponentResource(CLS3401M01Page.class);
	Properties prop_cls3401m03 = MessageBundleScriptCreator.getComponentResource(CLS3401M03Page.class);
	Properties prop_cls3401m04 = MessageBundleScriptCreator.getComponentResource(CLS3401M04Page.class);
	Properties prop_abstractEloanPage = MessageBundleScriptCreator.getComponentResource(AbstractEloanPage.class);
	
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult newC340M01A(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();

		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String ctrType = Util.trim(params.getString("ctrType"));
		String caseMainId = Util.trim(params.getString("caseMainId"));
		String tabMainId = Util.trim(params.getString("tabMainId"));
		L120M01A l120m01a = clsService.findL120M01A_mainId(caseMainId);
		if(Util.isEmpty(ctrType)){
			throw new CapMessageException(MessageFormat.format(prop_cls3401m01.getProperty("msg.unkeyin"), prop_cls3401m01.getProperty("C340M01A.ctrType")), getClass());
		}else{
			if (Util.equals(ContractDocConstants.C340M01A_CtrType.Type_1, ctrType)){
			
			}else if (Util.equals(ContractDocConstants.C340M01A_CtrType.Type_2, ctrType)){
				
			}else if (Util.equals(ContractDocConstants.C340M01A_CtrType.Type_3, ctrType)){
				
			}else{
				throw new CapMessageException(prop_cls3401m01.getProperty("C340M01A.ctrType")+"["+ctrType+"]不合理", getClass());
			}
		}
		
		C340M01A c340m01a = new C340M01A();
		c340m01a.setMainId(IDGenerator.getUUID());		
		c340m01a.setTypCd(UtilConstants.Casedoc.typCd.DBU);
		c340m01a.setOwnBrId(user.getUnitNo());
		c340m01a.setDocStatus(CreditDocStatusEnum.海外_編製中.getCode());

		String txCode = Util.trim(params.getString(EloanConstants.TRANSACTION_CODE));
		c340m01a.setTxCode(txCode);
		if (Util.equals(ContractDocConstants.C340M01A_CtrType.Type_1, ctrType)){
			// UPGRADE: 待確認，URL是否正確
			c340m01a.setDocURL(params.getString("docUrl"));
		}else if (Util.equals(ContractDocConstants.C340M01A_CtrType.Type_2, ctrType)){
			// UPGRADE: 待確認，URL是否正確
			c340m01a.setDocURL(params.getString("docUrl"));
		}else if (Util.equals(ContractDocConstants.C340M01A_CtrType.Type_3, ctrType)){
			// UPGRADE: 待確認，URL是否正確
			c340m01a.setDocURL(params.getString("docUrl"));
		}
		
		if(true){
			c340m01a.setDeletedTime(CapDate.getCurrentTimestamp());
		}
		c340m01a.setCustId(custId);
		c340m01a.setDupNo(dupNo);	
		c340m01a.setCreator(user.getUserId());
		c340m01a.setCreateTime(CapDate.getCurrentTimestamp());
		c340m01a.setCtrType(ctrType);
		c340m01a.setRptId(get_latest_rptId(ctrType));
		c340m01a.setContrNumber("");
		c340m01a.setContrPartyNm("");
		if(l120m01a!=null){
			c340m01a.setCaseMainId(caseMainId);
			c340m01a.setCaseNo(Util.toSemiCharString(l120m01a.getCaseNo()));
		}
		contractDocService.init_C340Relate(c340m01a, tabMainId);
		
		return defaultResult(params, c340m01a, result);
	}
	
	private String get_latest_rptId(String ctrType){
		if(Util.equals(ctrType, ContractDocConstants.C340M01A_CtrType.Type_1)){
			if(clsService.is_function_on_codetype("CtrType1_V202401")){
				return ContractDocConstants.C340M01A_RptId.V202401;  // 購屋貸款契約書 之 version
			}
			else if(LMSUtil.cmpDate(CapDate.getCurrentTimestamp(), ">=", CapDate.parseDate("2023-04-06"))){
		    	return ContractDocConstants.C340M01A_RptId.V202304;  // 購屋貸款契約書 之 version
		    }
			else if(LMSUtil.cmpDate(CapDate.getCurrentTimestamp(), ">=", CapDate.parseDate("2022-06-23"))){
				return ContractDocConstants.C340M01A_RptId.V202212;  // 購屋貸款契約書 之 version
			}
			else if(LMSUtil.cmpDate(CapDate.getCurrentTimestamp(), ">=", CapDate.parseDate("2020-08-20"))){
				return ContractDocConstants.C340M01A_RptId.V202008;  // 購屋貸款契約書 之 version
			}
			else{
				return ContractDocConstants.C340M01A_RptId.V202003;  // 購屋貸款契約書 之 version
			}
		}else if(Util.equals(ctrType, ContractDocConstants.C340M01A_CtrType.Type_2)){
			if(clsService.is_function_on_codetype("CtrType2_V202409")){
				return ContractDocConstants.C340M01A_RptId.CtrType2_V202409;  // 信用貸款契約書 之 version
			}
			else if(clsService.is_function_on_codetype("CtrType2_V202401")){
				return ContractDocConstants.C340M01A_RptId.CtrType2_V202401;  // 信用貸款契約書 之 version
			}
			else{
				return ContractDocConstants.C340M01A_RptId.CtrType2_V202304;  // 信用貸款契約書 之 version
			}
		}else if(Util.equals(ctrType, ContractDocConstants.C340M01A_CtrType.Type_3)){
			if(clsService.is_function_on_codetype("CtrType3_V202401")){
				return ContractDocConstants.C340M01A_RptId.CtrType3_V202401;  // 其它貸款契約書 之 version
			}
			else{
				return ContractDocConstants.C340M01A_RptId.CtrType3_V202304;  // 其它貸款契約書 之 version
			}
		}
		return "";		
	}
	
	
	/*
$.ajax({ handler: "cls3401m01formhandler", action: "dump_json_prop", data: {'mainOid':responseJSON.mainOid
    	, 'key':['eloan_pa_deliv_C_cb','eloan_pa_deliv_C_t1','eloan_pa_deliv_D_cb','eloan_pa_deliv_D_t1','eloan_pa_deliv_D_t2','eloan_pa_deliv_D1_t1'
			,'eloan_pa_deliv_D1_t1A','eloan_pa_deliv_D1_t1B','eloan_pa_deliv_D1_t2','eloan_pa_deliv_D1_t3','eloan_pa_deliv_D1_t4','eloan_pa_deliv_D1_t5'
			,'eloan_pa_deliv_D1_t6','eloan_pa_deliv_D1_t7','eloan_pa_deliv_D2_1cb','eloan_pa_deliv_D2_2cb','eloan_pa_deliv_D3_t1'] 
    },success: function(json){  console.dir(json);   }
});
	 */
	@DomainAuth(value = AuthType.Query)
	public IResult dump_json_prop(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		CapAjaxFormResult resultJSON = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C340M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = clsService.findC340M01A_oid(mainOid);
			List<C340M01C> c340m01c_list = clsService.findC340M01C(meta.getMainId());
			
			String[] json_column_read = params.getStringArray("key");
			if(json_column_read!=null && json_column_read.length >0){
				for(C340M01C c340m01c: c340m01c_list){
					String jsonData = Util.trim(c340m01c.getJsonData());
					JSONObject jsonObject = DataParse.toJSON(jsonData);
					if(jsonObject!=null){
						LMSUtil.addJsonToResult(resultJSON, jsonObject, json_column_read);
					}
				}	
			}
			result.set("mainId", meta.getMainId());
			result.set("resultJSON", resultJSON);
		}
		return result;
	}
	
	@DomainAuth(value = AuthType.Query)
	public IResult inject_json_prop(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();		
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String itemType = Util.trim(params.getString("itemType"));
		String newk = Util.trim(params.getString("newk"));
		String newv = Util.trim(params.getString("newv"));
		C340M01A meta = null;
		if (Util.isNotEmpty(mainOid) && Util.isNotEmpty(itemType) && Util.isNotEmpty(newk)) {
			meta = clsService.findC340M01A_oid(mainOid);
			C340M01C c340m01c = clsService.findC340M01C(meta.getMainId(), itemType);
			if(c340m01c!=null){
				String jsonData = Util.trim(c340m01c.getJsonData());
				JSONObject jsonObject = DataParse.toJSON(jsonData);
				if(jsonObject!=null){
					jsonObject.put(newk, newv);
					c340m01c.setJsonData(jsonObject.toString());
					clsService.daoSave(c340m01c);
					result.set("newk", newk);
					result.set("newv", newv);
				}
			}
			
			result.set("mainId", meta.getMainId());
			result.set("itemType", itemType);
		}
		return result;
	}
	
	@DomainAuth(value = AuthType.Query)
	public IResult queryC340M01A(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C340M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = clsService.findC340M01A_oid(mainOid);
			List<C340M01C> c340m01c_list = clsService.findC340M01C(meta.getMainId()); 
						
			String page = params.getString(EloanConstants.PAGE);
			/*
			 * 依不同的 ctrType , rptId 去將 jsonColumn帶到前端UI
			 */
			Set<String> json_column_read = _json_column_read(meta.getCtrType(), meta.getRptId(), page);
			if ("01".equals(page)) {
				if (true) {
					LMSUtil.addMetaToResult(result, meta, new String[] {
							"custId", "dupNo", "custName"
							, "caseNo", "rptId", "contrNumber", "contrPartyNm"});					
				}
				String ownBrId = meta.getOwnBrId();
				result.set("ownBrId", StrUtils.concat(ownBrId, " ",branchService.getBranchName(ownBrId)));
				result.set("docStatus", getMessage("docStatus." + meta.getDocStatus()));
				result.set("typCd", getMessage("typCd." + meta.getTypCd()));
				result.set("ctrTypeMapDesc", get_ctrTyype_desc(meta.getCtrType()));
				result.set("creator", _id_name(meta.getCreator()));
				result.set("createTime", Util.trim(TWNDate.valueOf(meta.getCreateTime())));
				result.set("updater", _id_name(meta.getUpdater()));
				result.set("updateTime", Util.trim(TWNDate.valueOf(meta.getUpdateTime())));
				result.set("rptId_desc", rptId_desc(meta));
				List<C340M01B> c340m01b_list = clsService.findC340M01B(meta.getMainId());
				result.set("cntrNo", get_c340m01b_cntrNo(c340m01b_list));
				result.set("randomCode", meta.getRandomCode());
				
			}else if ("02".equals(page)) {
				inject_accountQuery_idDupList(result, meta);
			}else if ("03".equals(page)) {
			}else if ("04".equals(page)) {
			}else if ("08".equals(page)) {
				inject_accountQuery_idDupList(result, meta);
			}
			
			
			if(json_column_read.size()>0){
				for(C340M01C c340m01c: c340m01c_list){
					String jsonData = Util.trim(c340m01c.getJsonData());
					JSONObject jsonObject = DataParse.toJSON(jsonData);
					if(jsonObject!=null){
						LMSUtil.addJsonToResult(result, jsonObject, json_column_read);
					}
				}	
			}
		}

		return defaultResult(params, meta, result);
	}
	
	private String get_c340m01b_cntrNo(List<C340M01B> c340m01b_list){
		List<String> cntrNo_list = new ArrayList<String>();
		for(C340M01B c340m01b : c340m01b_list){
			cntrNo_list.add(c340m01b.getCntrNo());
		}
		return StringUtils.join(cntrNo_list, "、");
	}
	private String rptId_desc(C340M01A meta) {
		String ctrType = Util.trim(meta.getCtrType());
		String rptId = Util.trim(meta.getRptId());
		if(Util.equals(ctrType, ContractDocConstants.C340M01A_CtrType.Type_1)){
			return rptId_desc_ctrType1(meta);
		}else if(Util.equals(ctrType, ContractDocConstants.C340M01A_CtrType.Type_2)){
			return rptId_desc_ctrType2(meta);
		}else if(Util.equals(ctrType, ContractDocConstants.C340M01A_CtrType.Type_3)){
			return rptId_desc_ctrType3(meta);
		}
		return rptId;
	}
	private String rptId_desc_ctrType1(C340M01A meta) {
		String ctrType = Util.trim(meta.getCtrType());
		String rptId = Util.trim(meta.getRptId());
		if(Util.equals(ctrType, ContractDocConstants.C340M01A_CtrType.Type_1)){
			if(Util.equals(ContractDocConstants.C340M01A_RptId.V202003, rptId)){
				return "109.03版";
			}
			if(Util.equals(ContractDocConstants.C340M01A_RptId.V202008, rptId)){
				return "109.08版";
			}
			if(Util.equals(ContractDocConstants.C340M01A_RptId.V202206, rptId)){
				return "111.06版";
			}
			if(Util.equals(ContractDocConstants.C340M01A_RptId.V202212, rptId)){
				return "111.12版";
			}
			if(Util.equals(ContractDocConstants.C340M01A_RptId.V202304, rptId)){
				return "112.04版";
			}
			if(Util.equals(ContractDocConstants.C340M01A_RptId.V202401, rptId)){
				return "113.01版";
			}
		}
		return rptId;
	}
	private String rptId_desc_ctrType2(C340M01A meta) {
		String ctrType = Util.trim(meta.getCtrType());
		String rptId = Util.trim(meta.getRptId());
		if(Util.equals(ctrType, ContractDocConstants.C340M01A_CtrType.Type_2)){
			if(Util.equals(ContractDocConstants.C340M01A_RptId.CtrType2_V202010, rptId)){
				return "109.10版";
			}else if(Util.equals(ContractDocConstants.C340M01A_RptId.CtrType2_V202009, rptId)){
				return "109.09版";
			}else if(Util.equals(ContractDocConstants.C340M01A_RptId.CtrType2_V202206, rptId)){
				return "111.06版";
			}
			else if(Util.equals(ContractDocConstants.C340M01A_RptId.CtrType2_V202212, rptId)){
				return "111.12版";
			}
			else if(Util.equals(ContractDocConstants.C340M01A_RptId.CtrType2_V202304, rptId)){
				return "112.04版";
			}
			else if(Util.equals(ContractDocConstants.C340M01A_RptId.CtrType2_V202401, rptId)){
				return "113.01版";
			}
			else if(Util.equals(ContractDocConstants.C340M01A_RptId.CtrType2_V202409, rptId)){
				return "113.09版";
			}
		}
		return rptId;
	}
	private String rptId_desc_ctrType3(C340M01A meta) {
		String ctrType = Util.trim(meta.getCtrType());
		String rptId = Util.trim(meta.getRptId());
		if(Util.equals(ctrType, ContractDocConstants.C340M01A_CtrType.Type_3)){
			if(Util.equals(ContractDocConstants.C340M01A_RptId.CtrType3_V202008, rptId)){
				return "109.08版";
			}else if(Util.equals(ContractDocConstants.C340M01A_RptId.CtrType3_V202206, rptId)){
				return "111.06版";
			}else if(Util.equals(ContractDocConstants.C340M01A_RptId.CtrType3_V202210, rptId)){
				return "111.10版";
			}
			else if(Util.equals(ContractDocConstants.C340M01A_RptId.CtrType3_V202212, rptId)){
				return "111.12版";
			}
			else if(Util.equals(ContractDocConstants.C340M01A_RptId.CtrType3_V202304, rptId)){
				return "112.04版";
			}
			else if(Util.equals(ContractDocConstants.C340M01A_RptId.CtrType3_V202401, rptId)){
				return "113.01版";
			}
		}
		return rptId;
	}
	private String _id_name(String raw_id) {
		String id = Util.trim(raw_id);
		return Util.trim(id + " " + Util.trim(userInfoService.getUserName(id)));
	}
	
	private Set<String> _json_column_read(String ctrType, String rptId, String page){
		Set<String> r = new LinkedHashSet<String>();
		if (true) {
			r.addAll(_json_column_write(ctrType, rptId, page));
		}
		return r;
	}
	private Set<String> _json_column_write(String ctrType, String rptId, String page){
		if(Util.equals(ctrType, ContractDocConstants.C340M01A_CtrType.Type_1)){
			return _json_column_write_ctrType1(ctrType, rptId, page);
		}else if(Util.equals(ctrType, ContractDocConstants.C340M01A_CtrType.Type_2)){
			return _json_column_write_ctrType2(ctrType, rptId, page);
		}else if(Util.equals(ctrType, ContractDocConstants.C340M01A_CtrType.Type_3)){
			return _json_column_write_ctrType3(ctrType, rptId, page);
		} 
		return new LinkedHashSet<String>();
	}
	private Set<String> _json_column_write_ctrType1(String ctrType, String rptId, String page){
		Set<String> r = new LinkedHashSet<String>();
		if ("02".equals(page)) {
			r.add(ContractDocConstants.CtrType1.ELOAN_P1_CONTR_NAME_M);
			r.add(ContractDocConstants.CtrType1.ELOAN_P1_CONTR_NAME_N);
			r.add(ContractDocConstants.CtrType1.ELOAN_P1_CONTR_NAME_G);
			r.add(ContractDocConstants.CtrType1.ELOAN_P1_CONTR_AMT);
			//~~~~~~
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_DELIV_A_CB);
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_DELIV_A_T1);
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_DELIV_A_T2);
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_DELIV_B_CB);
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_DELIV_B_T1);
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_DELIV_B_T2);
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_DELIV_B_T3);
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_DELIV_C_CB);
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_DELIV_D_CB);
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_DELIV_D_T1);
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_DELIV_D_T2);

			r.add(ContractDocConstants.CtrType1.ELOAN_PA_DELIV_D1_T1);
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_DELIV_D1_T2);
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_DELIV_D1_T3);
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_DELIV_D1_T4);
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_DELIV_D1_T5);
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_DELIV_D1_T6);
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_DELIV_D1_T7);

			r.add(ContractDocConstants.CtrType1.ELOAN_PA_DELIV_D2_1CB);
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_DELIV_D2_2CB);
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_DELIV_D3_T1);
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_DELIV_E_CB);
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_DELIV_E_T1);
			//~~~~~~
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_USE_A_CB);
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_USE_A_T1);
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_USE_A_T2);
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_USE_B_CB);
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_USE_B_T1);
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_USE_Y);
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_USE_M);
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_USE_BEGY);
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_USE_BEGM);
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_USE_BEGD);
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_USE_ENDY);
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_USE_ENDM);
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_USE_ENDD);
			//~~~~~~
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_REPAY_A_CB);
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_REPAY_B_CB);
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_REPAY_C_CB);
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_REPAY_C_T1);
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_REPAY_C_T2);
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_REPAY_C_T3);
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_REPAY_C_T4);
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_REPAY_D_CB);
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_REPAY_D_T1);
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_REPAY_D_T2);
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_REPAY_D_T3);
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_REPAY_D_T4);
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_REPAY_E_CB);
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_REPAY_E_T1);
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_REPAY_E_T2);
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_REPAY_F_CB);
			r.add(ContractDocConstants.CtrType1.ELOAN_PA_REPAY_F_T1);
			if(true){
				r.add(ContractDocConstants.CtrType1.ELOAN_PA_REPAY_ACTNO);
				r.add(ContractDocConstants.CtrType1.ELOAN_PA_REPAY_FEENO01);
				r.add(ContractDocConstants.CtrType1.ELOAN_PA_REPAY_FEENO02);
				r.add(ContractDocConstants.CtrType1.ELOAN_PA_REPAY_FEENO03);
				r.add(ContractDocConstants.CtrType1.ELOAN_PA_REPAY_FEENO04);
				r.add(ContractDocConstants.CtrType1.ELOAN_PA_REPAY_FEENO06);
				r.add(ContractDocConstants.CtrType1.ELOAN_PA_REPAY_FEENO07);
			}			
			//~~~~~~
			if(true){
				r.add(ContractDocConstants.CtrType1.ELOAN_PA_INTR_NOPPP_CB);
				r.add(ContractDocConstants.CtrType1.ELOAN_PA_INTR_NOPPP_BASERATE);
				r.add(ContractDocConstants.CtrType1.ELOAN_PA_INTR_NOPPP_1T1);
				r.add(ContractDocConstants.CtrType1.ELOAN_PA_INTR_NOPPP_1T2);
				r.add(ContractDocConstants.CtrType1.ELOAN_PA_INTR_NOPPP_2T1);
				r.add(ContractDocConstants.CtrType1.ELOAN_PA_INTR_NOPPP_3T1);
				//~~~
				r.add(ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_CB);
				r.add(ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_BASERATE);
				r.add(ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_1X1);
				r.add(ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_1X2);
				r.add(ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_1X3);
				r.add(ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_1Y1);
				r.add(ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_1Y2);
				r.add(ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_1Y3);
				r.add(ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_1Y4);
				r.add(ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_1Y5);
				r.add(ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_1Y6);
				r.add(ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_1Y7);
				r.add(ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_1Y8);		
				r.add(ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_2T1);
				//~~~
				r.add(ContractDocConstants.CtrType1.ELOAN_PA_INTR_OTHER_CB);
				r.add(ContractDocConstants.CtrType1.ELOAN_PA_INTR_OTHER_T1);
			}
		}else if ("03".equals(page)) {
			r.add(ContractDocConstants.CtrType1.ELOAN_PB_GNTEEG_CB);
			r.add(ContractDocConstants.CtrType1.ELOAN_PB_GNTEEG_B_CB);
			r.add(ContractDocConstants.CtrType1.ELOAN_PB_GNTEEG_B_T1);
			r.add(ContractDocConstants.CtrType1.ELOAN_PB_GNTEEG_C_CB);
			r.add(ContractDocConstants.CtrType1.ELOAN_PB_GNTEEG_C_T1);
			
			r.add(ContractDocConstants.CtrType1.ELOAN_PB_GNTEEN_CB);
			r.add(ContractDocConstants.CtrType1.ELOAN_PB_GNTEEN_A_CB);
			r.add(ContractDocConstants.CtrType1.ELOAN_PB_GNTEEN_B_CB);
			r.add(ContractDocConstants.CtrType1.ELOAN_PB_GNTEEN_B_T1);
			r.add(ContractDocConstants.CtrType1.ELOAN_PB_GNTEEN_C_CB);
			r.add(ContractDocConstants.CtrType1.ELOAN_PB_GNTEEN_C_T1);	
		}else if ("04".equals(page)) {
			if(true){ // 服務管道
				r.add(ContractDocConstants.CtrType1.ELOAN_PC_SERV_TEL_T1);
				r.add(ContractDocConstants.CtrType1.ELOAN_PC_SERV_FAX_T1);
				r.add(ContractDocConstants.CtrType1.ELOAN_PC_SERV_MAIL_T1);
				r.add(ContractDocConstants.CtrType1.ELOAN_PC_SERV_OTHER_T1);
			}
			r.add(ContractDocConstants.CtrType1.ELOAN_PC_COURT_LOC);
			r.add(ContractDocConstants.CtrType1.ELOAN_PC_COPY_CNT);
			r.add(ContractDocConstants.CtrType1.ELOAN_PC_SPTERM_YRATE);
            r.add(ContractDocConstants.CtrType1.ELOAN_PC_SPTERM_NOTE);
			r.add(ContractDocConstants.CtrType1.ELOAN_PC_GNTEE_CB);
			if(true){ // 甲方
				r.add(ContractDocConstants.CtrType1.ELOAN_P9_SIG_M_NAME);
				r.add(ContractDocConstants.CtrType1.ELOAN_P9_SIG_M_CUSTID);
				r.add(ContractDocConstants.CtrType1.ELOAN_P9_SIG_M_ADDR);
				if(true){ // 從債務人1
					r.add(ContractDocConstants.CtrType1.ELOAN_P9_SIG_1N_CB);
					r.add(ContractDocConstants.CtrType1.ELOAN_P9_SIG_1N_NAME);
					r.add(ContractDocConstants.CtrType1.ELOAN_P9_SIG_1G_CB);
					r.add(ContractDocConstants.CtrType1.ELOAN_P9_SIG_1G_NAME);
					r.add(ContractDocConstants.CtrType1.ELOAN_P9_SIG_1_CUSTID);
					r.add(ContractDocConstants.CtrType1.ELOAN_P9_SIG_1_ADDR);
				}
				if(true){ // 從債務人2
					r.add(ContractDocConstants.CtrType1.ELOAN_P9_SIG_2N_CB);
					r.add(ContractDocConstants.CtrType1.ELOAN_P9_SIG_2N_NAME);
					r.add(ContractDocConstants.CtrType1.ELOAN_P9_SIG_2G_CB);
					r.add(ContractDocConstants.CtrType1.ELOAN_P9_SIG_2G_NAME);
					r.add(ContractDocConstants.CtrType1.ELOAN_P9_SIG_2_CUSTID);
					r.add(ContractDocConstants.CtrType1.ELOAN_P9_SIG_2_ADDR);
				}
			}
			if(true){ // 乙方
				r.add(ContractDocConstants.CtrType1.ELOAN_P9_SIG_PARTYB_AGENT);
				r.add(ContractDocConstants.CtrType1.ELOAN_P9_SIG_PARTYB_ADDR);
			}
		}
		return r;
	}
	

	private Set<String> _json_column_write_ctrType2(String ctrType, String rptId, String page){
		Set<String> r = new LinkedHashSet<String>();
		if ("02".equals(page)) {
			r.add(ContractDocConstants.CtrType2.ELOAN_P1_CONTR_NAME_M);
			r.add(ContractDocConstants.CtrType2.ELOAN_P1_CONTR_NAME_N);
			r.add(ContractDocConstants.CtrType2.ELOAN_P1_CONTR_NAME_G);
			r.add(ContractDocConstants.CtrType2.ELOAN_P1_CONTR_AMT);
			//~~~~~~
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_DELIV_A_CB);
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_DELIV_A_T1);
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_DELIV_A_T2);
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_DELIV_B_CB);
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_DELIV_C_CB);
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_DELIV_C_T1);
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_DELIV_D_CB);
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_DELIV_D_T1);
			//~~~~~~
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_PURPOSE_WAY);
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_PURPOSE_E_T1);
			//~~~~~~
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_USE_A_T1);
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_USE_A_T2);
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_USE_A_T3);
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_USE_A_T4);
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_USE_A_T5);
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_USE_A_T6);
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_USE_B_T1);
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_USE_B_T2);
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_USE_B_T3);
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_USE_B_T4);
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_USE_B_T5);
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_USE_B_T6);
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_USE_B_T7);
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_USE_B_T8);
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_USE_C_T1);
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_USE_C_T2);
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_USE_C_T3);
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_USE_C_T4);
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_USE_C_T5);
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_USE_C_T6);
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_USE_C_T7);
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_USE_C_T8);
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_USE_C_T9);
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_USE_D_T1);
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_USE_D_T2);
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_USE_D_T3);
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_USE_D_T4);
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_USE_D_T5);
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_USE_D_T6);
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_USE_D_T7);
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_USE_D_T8);
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_USE_D_T9);
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_USE_E_T1);
			//~~~~~~
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_REPAY_WAY);
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_REPAY_A_T1);
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_REPAY_A_T2);
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_REPAY_A_T3);
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_REPAY_C_T1);
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_REPAY_C_T2);
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_REPAY_C_T3);
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_REPAY_C_T4);
			r.add(ContractDocConstants.CtrType2.ELOAN_PA_REPAY_D_T1);
			if(true){
				r.add(ContractDocConstants.CtrType2.ELOAN_PA_REPAY_WITHPPP_TERM);
				r.add(ContractDocConstants.CtrType2.ELOAN_PA_REPAY_ACTNO);
				r.add(ContractDocConstants.CtrType2.ELOAN_PA_REPAY_FEENO01);
				r.add(ContractDocConstants.CtrType2.ELOAN_PA_REPAY_FEENO02);
				r.add(ContractDocConstants.CtrType2.ELOAN_PA_REPAY_FEENO03);
				r.add(ContractDocConstants.CtrType2.ELOAN_PA_REPAY_FEENO04);
				r.add(ContractDocConstants.CtrType2.ELOAN_PA_REPAY_FEENO06);
			}			
			//~~~~~~
			if(true){
				r.add(ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_CB);
				r.add(ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_BASERATE);
				r.add(ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1X1);
				r.add(ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1X2);
				r.add(ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1X3);
				r.add(ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1Y1);
				r.add(ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1Y2);
				r.add(ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1Y3);
				// r.add(ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1Y4);
				r.add(ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1Y5);
				r.add(ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1Y6);
				r.add(ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1Y7);
				// r.add(ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1Y8);		
				r.add(ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1Y9);
				r.add(ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1Y10);
				r.add(ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1Y11);
				// r.add(ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1Y12);	
				r.add(ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_2T1);
				//~~~
				r.add(ContractDocConstants.CtrType2.ELOAN_PA_INTR_NOPPP_CB);
				r.add(ContractDocConstants.CtrType2.ELOAN_PA_INTR_NOPPP_BASERATE);
				r.add(ContractDocConstants.CtrType2.ELOAN_PA_INTR_NOPPP_1T1);
				r.add(ContractDocConstants.CtrType2.ELOAN_PA_INTR_NOPPP_2T1);
				r.add(ContractDocConstants.CtrType2.ELOAN_PA_INTR_NOPPP_3T1);
				r.add(ContractDocConstants.CtrType2.ELOAN_PA_INTR_NOPPP_4T1);
				r.add(ContractDocConstants.CtrType2.ELOAN_PA_INTR_NOPPP_4T2);
				r.add(ContractDocConstants.CtrType2.ELOAN_PA_INTR_NOPPP_5T1);
				//~~~
				r.add(ContractDocConstants.CtrType2.ELOAN_PA_INTR_OTHER_CB);
				r.add(ContractDocConstants.CtrType2.ELOAN_PA_INTR_OTHER_T1);
			}
		}else if ("03".equals(page)) {
			r.add(ContractDocConstants.CtrType2.ELOAN_PB_GNTEEG_CB);
			r.add(ContractDocConstants.CtrType2.ELOAN_PB_GNTEEG_E_CB);
			r.add(ContractDocConstants.CtrType2.ELOAN_PB_GNTEEG_E_T1);
			r.add(ContractDocConstants.CtrType2.ELOAN_PB_GNTEEG_D_CB);
			r.add(ContractDocConstants.CtrType2.ELOAN_PB_GNTEEG_D_T1);
			
			r.add(ContractDocConstants.CtrType2.ELOAN_PB_GNTEEN_CB);
			r.add(ContractDocConstants.CtrType2.ELOAN_PB_GNTEEN_F_CB);
			r.add(ContractDocConstants.CtrType2.ELOAN_PB_GNTEEN_F_T1);
			r.add(ContractDocConstants.CtrType2.ELOAN_PB_GNTEEN_G_CB);
			r.add(ContractDocConstants.CtrType2.ELOAN_PB_GNTEEN_G_T1);	
		}else if ("04".equals(page)) {
			if(true){ // 服務管道
				r.add(ContractDocConstants.CtrType2.ELOAN_PC_SERV_TEL_T1);
				r.add(ContractDocConstants.CtrType2.ELOAN_PC_SERV_FAX_T1);
				r.add(ContractDocConstants.CtrType2.ELOAN_PC_SERV_MAIL_T1);
				r.add(ContractDocConstants.CtrType2.ELOAN_PC_SERV_OTHER_T1);
			}
			r.add(ContractDocConstants.CtrType2.ELOAN_PC_COURT_LOC);
			r.add(ContractDocConstants.CtrType2.ELOAN_PC_COPY_CNT);
			r.add(ContractDocConstants.CtrType2.ELOAN_PC_SPTERM_YRATE);
            r.add(ContractDocConstants.CtrType2.ELOAN_PC_SPTERM_NOTE);
			r.add(ContractDocConstants.CtrType2.ELOAN_PC_GNTEE_CB);
			if(true){ // 甲方
				r.add(ContractDocConstants.CtrType2.ELOAN_P9_SIG_M_NAME);
				r.add(ContractDocConstants.CtrType2.ELOAN_P9_SIG_M_CUSTID);
				r.add(ContractDocConstants.CtrType2.ELOAN_P9_SIG_M_ADDR);
				if(true){ // 從債務人1
					r.add(ContractDocConstants.CtrType2.ELOAN_P9_SIG_1N_CB);
					r.add(ContractDocConstants.CtrType2.ELOAN_P9_SIG_1N_NAME);
					r.add(ContractDocConstants.CtrType2.ELOAN_P9_SIG_1G_CB);
					r.add(ContractDocConstants.CtrType2.ELOAN_P9_SIG_1G_NAME);
					r.add(ContractDocConstants.CtrType2.ELOAN_P9_SIG_1_CUSTID);
					r.add(ContractDocConstants.CtrType2.ELOAN_P9_SIG_1_ADDR);
				}
				if(true){ // 從債務人2
					r.add(ContractDocConstants.CtrType2.ELOAN_P9_SIG_2N_CB);
					r.add(ContractDocConstants.CtrType2.ELOAN_P9_SIG_2N_NAME);
					r.add(ContractDocConstants.CtrType2.ELOAN_P9_SIG_2G_CB);
					r.add(ContractDocConstants.CtrType2.ELOAN_P9_SIG_2G_NAME);
					r.add(ContractDocConstants.CtrType2.ELOAN_P9_SIG_2_CUSTID);
					r.add(ContractDocConstants.CtrType2.ELOAN_P9_SIG_2_ADDR);
				}
			}
			if(true){ // 乙方
				r.add(ContractDocConstants.CtrType2.ELOAN_P9_SIG_PARTYB_AGENT);
				r.add(ContractDocConstants.CtrType2.ELOAN_P9_SIG_PARTYB_ADDR);
			}
		}
		return r;
	}
	
	private Set<String> _json_column_write_ctrType3(String ctrType, String rptId, String page){
		Set<String> r = new LinkedHashSet<String>();
		if ("02".equals(page)) {
			r.add(ContractDocConstants.CtrType3.ELOAN_P1_CONTR_NAME_M);
			r.add(ContractDocConstants.CtrType3.ELOAN_P1_CONTR_NAME_N);
			r.add(ContractDocConstants.CtrType3.ELOAN_P1_CONTR_NAME_G);
			r.add(ContractDocConstants.CtrType3.ELOAN_P1_CONTR_AMT);
			//~~~~~~
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_DELIV_A_CB);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_DELIV_A_T1);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_DELIV_A_T2);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_DELIV_B_CB);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_DELIV_C_CB);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_DELIV_C_T1);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_DELIV_D_CB);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_DELIV_D_T1);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_DELIV_D_T2);
			
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_DELIV_D1_T1);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_DELIV_D1_T2);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_DELIV_D1_T3);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_DELIV_D1_T4);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_DELIV_D1_T5);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_DELIV_D1_T6);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_DELIV_D1_T7);
			
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_DELIV_D2_1CB);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_DELIV_D2_2CB);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_DELIV_D3_T1);
			
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_DELIV_E_CB);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_DELIV_E_T1);
			//~~~~~~
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_PURPOSE_WAY);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_PURPOSE_G_T1);
			//~~~~~~
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_USE_A_T1);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_USE_A_T2);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_USE_A_T3);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_USE_A_T4);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_USE_A_T5);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_USE_A_T6);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_USE_A_T7);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_USE_A_T8);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_USE_B_T1);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_USE_B_T2);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_USE_B_T3);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_USE_B_T4);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_USE_B_T5);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_USE_B_T6);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_USE_B_T7);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_USE_B_T8);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_USE_B_T9);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_USE_C_T1);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_USE_C_T2);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_USE_C_T3);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_USE_C_T4);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_USE_C_T5);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_USE_C_T6);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_USE_C_T7);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_USE_C_T8);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_USE_C_T9);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_USE_D_T1);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_USE_D_T2);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_USE_D_T3);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_USE_D_T4);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_USE_D_T5);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_USE_D_T6);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_USE_E_T1);
			//~~~~~~
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_REPAY_WAY);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_REPAY_D_T1);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_REPAY_D_T2);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_REPAY_D_T3);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_REPAY_D_T4);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_REPAY_E_T1);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_REPAY_E_T2);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_REPAY_E_T3);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_REPAY_E_T4);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_REPAY_F_T1);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_REPAY_F_T2);
			r.add(ContractDocConstants.CtrType3.ELOAN_PA_REPAY_G_T1);
			if(true){
				r.add(ContractDocConstants.CtrType3.ELOAN_PA_REPAY_WITHPPP_TERM);
				r.add(ContractDocConstants.CtrType3.ELOAN_PA_REPAY_ACTNO);
				r.add(ContractDocConstants.CtrType3.ELOAN_PA_REPAY_FEENO01);
				r.add(ContractDocConstants.CtrType3.ELOAN_PA_REPAY_FEENO02);
				r.add(ContractDocConstants.CtrType3.ELOAN_PA_REPAY_FEENO03);
				r.add(ContractDocConstants.CtrType3.ELOAN_PA_REPAY_FEENO04);
				r.add(ContractDocConstants.CtrType3.ELOAN_PA_REPAY_FEENO06);
				r.add(ContractDocConstants.CtrType3.ELOAN_PA_REPAY_FEENO07);
			}			
			//~~~~~~
			if(true){
				r.add(ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_CB);
				r.add(ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_BASERATE);
				r.add(ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_1X1);
				r.add(ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_1X2);
				r.add(ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_1X3);
				r.add(ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_1Y1);
				r.add(ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_1Y2);
				r.add(ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_1Y3);
				// r.add(ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_1Y4);
				r.add(ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_1Y5);
				r.add(ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_1Y6);
				r.add(ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_1Y7);
				// r.add(ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_1Y8);		
				r.add(ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_2T1);
				//~~~
				r.add(ContractDocConstants.CtrType3.ELOAN_PA_INTR_NOPPP_CB);
				r.add(ContractDocConstants.CtrType3.ELOAN_PA_INTR_NOPPP_BASERATE);
				r.add(ContractDocConstants.CtrType3.ELOAN_PA_INTR_NOPPP_1T1);
				r.add(ContractDocConstants.CtrType3.ELOAN_PA_INTR_NOPPP_2T1);
				r.add(ContractDocConstants.CtrType3.ELOAN_PA_INTR_NOPPP_3T1);
				r.add(ContractDocConstants.CtrType3.ELOAN_PA_INTR_NOPPP_4T1);
				//~~~
				r.add(ContractDocConstants.CtrType3.ELOAN_PA_INTR_OTHER_CB);
				r.add(ContractDocConstants.CtrType3.ELOAN_PA_INTR_OTHER_T1);
			}
		}else if ("03".equals(page)) {
			r.add(ContractDocConstants.CtrType3.ELOAN_PB_GNTEEG_CB);
			r.add(ContractDocConstants.CtrType3.ELOAN_PB_GNTEEG_B_CB);
			r.add(ContractDocConstants.CtrType3.ELOAN_PB_GNTEEG_B_T1);
			r.add(ContractDocConstants.CtrType3.ELOAN_PB_GNTEEG_C_CB);
			r.add(ContractDocConstants.CtrType3.ELOAN_PB_GNTEEG_C_T1);
			r.add(ContractDocConstants.CtrType3.ELOAN_PB_GNTEEG_D_CB);
			
			r.add(ContractDocConstants.CtrType3.ELOAN_PB_GNTEEN_CB);
			r.add(ContractDocConstants.CtrType3.ELOAN_PB_GNTEEN_B_CB);
			r.add(ContractDocConstants.CtrType3.ELOAN_PB_GNTEEN_B_T1);
			r.add(ContractDocConstants.CtrType3.ELOAN_PB_GNTEEN_C_CB);
			r.add(ContractDocConstants.CtrType3.ELOAN_PB_GNTEEN_C_T1);
			r.add(ContractDocConstants.CtrType3.ELOAN_PB_GNTEEN_D_CB);
		}else if ("04".equals(page)) {
			if(true){ // 服務管道
				r.add(ContractDocConstants.CtrType3.ELOAN_PC_SERV_TEL_T1);
				r.add(ContractDocConstants.CtrType3.ELOAN_PC_SERV_FAX_T1);
				r.add(ContractDocConstants.CtrType3.ELOAN_PC_SERV_MAIL_T1);
				r.add(ContractDocConstants.CtrType3.ELOAN_PC_SERV_OTHER_T1);
			}
			r.add(ContractDocConstants.CtrType3.ELOAN_PC_COURT_LOC);
			r.add(ContractDocConstants.CtrType3.ELOAN_PC_COPY_CNT);
			r.add(ContractDocConstants.CtrType3.ELOAN_PC_SPTERM_YRATE);
            r.add(ContractDocConstants.CtrType3.ELOAN_PC_SPTERM_NOTE);
			r.add(ContractDocConstants.CtrType3.ELOAN_PC_GNTEE_CB);
			if(true){ // 甲方
				r.add(ContractDocConstants.CtrType3.ELOAN_P9_SIG_M_NAME);
				r.add(ContractDocConstants.CtrType3.ELOAN_P9_SIG_M_CUSTID);
				r.add(ContractDocConstants.CtrType3.ELOAN_P9_SIG_M_ADDR);
				if(true){ // 從債務人1
					r.add(ContractDocConstants.CtrType3.ELOAN_P9_SIG_1N_CB);
					r.add(ContractDocConstants.CtrType3.ELOAN_P9_SIG_1N_NAME);
					r.add(ContractDocConstants.CtrType3.ELOAN_P9_SIG_1G_CB);
					r.add(ContractDocConstants.CtrType3.ELOAN_P9_SIG_1G_NAME);
					r.add(ContractDocConstants.CtrType3.ELOAN_P9_SIG_1_CUSTID);
					r.add(ContractDocConstants.CtrType3.ELOAN_P9_SIG_1_ADDR);
				}
				if(true){ // 從債務人2
					r.add(ContractDocConstants.CtrType3.ELOAN_P9_SIG_2N_CB);
					r.add(ContractDocConstants.CtrType3.ELOAN_P9_SIG_2N_NAME);
					r.add(ContractDocConstants.CtrType3.ELOAN_P9_SIG_2G_CB);
					r.add(ContractDocConstants.CtrType3.ELOAN_P9_SIG_2G_NAME);
					r.add(ContractDocConstants.CtrType3.ELOAN_P9_SIG_2_CUSTID);
					r.add(ContractDocConstants.CtrType3.ELOAN_P9_SIG_2_ADDR);
				}
			}
			if(true){ // 乙方
				r.add(ContractDocConstants.CtrType3.ELOAN_P9_SIG_PARTYB_AGENT);
				r.add(ContractDocConstants.CtrType3.ELOAN_P9_SIG_PARTYB_ADDR);
			}
		}
		return r;
	}
	
	/**
	 * 儲存
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(AuthType.Modify)
	public IResult saveMain(PageParameters params) throws CapException {
		return _saveAction(params, "N");
	}
	
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult tempSave(PageParameters params) throws CapException {
		return _saveAction(params, "Y");
	}
	
	private CapAjaxFormResult _saveAction(PageParameters params,
			String tempSave) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, tempSave);
		boolean allowIncomplete = Util.equals("Y", params.getString("allowIncomplete"));
		//===
		String KEY = "saveOkFlag";
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set(KEY, false);
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C340M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			try{
				meta = clsService.findC340M01A_oid(mainOid);
				List<C340M01C> c340m01c_list = clsService.findC340M01C(meta.getMainId());
				LinkedHashMap<String, C340M01C> c340m01c_map = ContractDocUtil.convert_in_map_format(c340m01c_list);
				Set<String> changed_c340m01c_oid = new HashSet<String>();
				//~~~~~~
				String page = params.getString(EloanConstants.PAGE);				
				Set<String> json_column_write = _json_column_write(meta.getCtrType(), meta.getRptId(), page);
				
				if ("01".equals(page)) {
					CapBeanUtil.map2Bean(params, meta, new String[] {"contrNumber", "contrPartyNm"
					});
				}else if ("02".equals(page)) {
				}else if ("03".equals(page)) {
				}else if ("04".equals(page)) {
					
				}
				if(json_column_write.size()>0){
					for(String c340m01c_oid : c340m01c_map.keySet()){
						C340M01C c340m01c = c340m01c_map.get(c340m01c_oid);
						String jsonData = Util.trim(c340m01c.getJsonData());
						JSONObject jsonObject = DataParse.toJSON(jsonData);
						
						if(jsonObject==null || jsonObject.size()==0 ){
							continue;
						}						
						int write_cnt = LMSUtil.writeParamsToJson(params, jsonObject, json_column_write);						
						if(write_cnt>0){
							c340m01c.setJsonData(jsonObject.toString());
							//~~~~~~
							changed_c340m01c_oid.add(c340m01c_oid);
						}
					}	
				}
				
				for(String c340m01c_oid : c340m01c_map.keySet()){
					C340M01C c340m01c = c340m01c_map.get(c340m01c_oid);
					String jsonData = Util.trim(c340m01c.getJsonData());
					JSONObject jsonObject = DataParse.toJSON(jsonData);
					if(jsonObject==null || jsonObject.size()==0 ){
						continue;
					}
					//增加核貸額度與契約書借款額度不一致-跳出提醒訊息, 切換至其他頁簽時(會抓不到貸款金額), 在此塞入貸款金額值
					String chineseLoanAmt = jsonObject.getString("eloan_p1_contr_amt");
					if(!"02".equals(page) && chineseLoanAmt != null){
						params.put("eloan_p1_contr_amt", chineseLoanAmt);
					}
				}
				
				meta.setDeletedTime(null);
				meta.setUpdater(user.getUserId());
				meta.setUpdateTime(CapDate.getCurrentTimestamp());
				meta.setRandomCode(IDGenerator.getRandomCode());
				//---
				for(String c340m01c_oid : changed_c340m01c_oid){
					C340M01C c340m01c = c340m01c_map.get(c340m01c_oid);					
					c340m01c.setUpdater(meta.getUpdater());
					c340m01c.setUpdateTime(meta.getUpdateTime());
					clsService.save(c340m01c);
				}
				clsService.save(meta);
				// ===
				String tipsMsg = "";
				if (Util.notEquals("Y",
						SimpleContextHolder.get(EloanConstants.TEMPSAVE_RUN))) {
					// 在tempSave<>Y,若有未填欄位,丟 CapMessageException, 讓
					// saveOkFlag==false

					String err_msg = check_data_relation(meta);
					String unKeyIn_column = unKeyIn_column(meta);
					String prodKind_column = prodKind_column(meta);
					if(allowIncomplete==false && Util.isNotEmpty(err_msg)){
						throw new CapMessageException(err_msg, getClass());
					}
					
					if(allowIncomplete && (Util.isNotEmpty(err_msg)|| Util.isNotEmpty(unKeyIn_column) || Util.isNotEmpty(prodKind_column))){
						
						tipsMsg += err_msg
							+((Util.isNotEmpty(err_msg)&&Util.isNotEmpty(unKeyIn_column))?"<br/>":"")+unKeyIn_column
							+(((Util.isNotEmpty(err_msg)|| Util.isNotEmpty(unKeyIn_column))&&Util.isNotEmpty(prodKind_column))?"<br/>":"")+prodKind_column;
					}
				}
				
				String msg = this.cls3401Service.getTipsOfComparingLoanAmountAndApprovedQuota(meta.getMainId(), CapString.trimNull(params.getString("eloan_p1_contr_amt")));
				if(!"".equals(msg)){
					tipsMsg += "<br/>" + msg;
				}
				
				String errMsg_chk_rate_gt_16percent = errMsg_chk_rate_gt_16percent(meta);
				if(Util.isNotEmpty(errMsg_chk_rate_gt_16percent)){
					tipsMsg += "<br/>" + errMsg_chk_rate_gt_16percent;
				}
				result.set("randomCode", meta.getRandomCode());
				result.set("IncompleteMsg", tipsMsg);
				
				result.set(KEY, true);	
			}catch(Exception e){
				logger.error(StrUtils.getStackTrace(e));
				throw new CapException(e, getClass());
			}		
		}
		return defaultResult( params, meta, result);
	}
		
	/**
	 * 呈主管覆核
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(AuthType.Modify + AuthType.Accept)
	public IResult flowAction(PageParameters params) throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");

		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String decisionExpr = Util.trim(params.getString("decisionExpr"));
		
		C340M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = clsService.findC340M01A_oid(mainOid);
			
			String errMsg = "";
			String docStatus = Util.trim(meta.getDocStatus());			
			
			String nextStatus = "";
			DocLogEnum _DocLogEnum = null;
			if(Util.equals(CreditDocStatusEnum.海外_編製中.getCode(), docStatus)){
				
				if(Util.isEmpty(errMsg)){
					
					nextStatus = CreditDocStatusEnum.海外_待覆核.getCode();
					_DocLogEnum = DocLogEnum.FORWARD;
				}
			}else if(Util.equals(CreditDocStatusEnum.海外_待覆核.getCode(), docStatus)){
				//核定、退回
				if(Util.equals("核定", decisionExpr)){
					if(Util.isEmpty(errMsg)){
						if(Util.equals(user.getUserId(), meta.getUpdater())){
							// select * from com.berrorcode where code='EFD0053' 覆核人員不可與「經辦人員或其它覆核人員」為同一人
							errMsg = RespMsgHelper.getMessage("EFD0053");
						}else{
							nextStatus = CreditDocStatusEnum.海外_已核准.getCode();
							_DocLogEnum = DocLogEnum.ACCEPT;	
						}
					}
					
				}else if(Util.equals("退回", decisionExpr)){
					nextStatus = CreditDocStatusEnum.海外_編製中.getCode();
					_DocLogEnum = DocLogEnum.BACK;
				}
			}else if(Util.equals(CreditDocStatusEnum.海外_已核准.getCode(), docStatus)){	

			}
				
			if(Util.isNotEmpty(errMsg)){				
				throw new CapMessageException(errMsg, getClass());
			}else{
				if(Util.isEmpty(nextStatus)){
					throw new CapMessageException("流程異常["+docStatus+"]", getClass());
				}	
			}
			//=============================================
			if(true){				
				if(Util.equals(nextStatus, CreditDocStatusEnum.海外_已核准.getCode())){
					Timestamp nowTS = CapDate.getCurrentTimestamp();
					meta.setApprover(user.getUserId());
					meta.setApproveTime(nowTS);
					
				}else if(Util.equals(nextStatus, CreditDocStatusEnum.海外_編製中.getCode())){
					meta.setApprover(null);
					meta.setApproveTime(null);
					
				}else if(Util.equals(nextStatus, CreditDocStatusEnum.海外_待覆核.getCode())){
					meta.setApprover(null);
					meta.setApproveTime(null);
				}
				meta.setDocStatus(nextStatus);
				//用 daoSave 避免把 approver 寫到 updater
				clsService.daoSave(meta);
				
				if(_DocLogEnum!=null){
					docLogService.record(meta.getOid(), _DocLogEnum);	
				}				
			}			
			tempDataService.deleteByMainId(meta.getMainId());
			docCheckService.unlockDocByMainIdUser(meta.getMainId(), user.getUserId());
		}
		return defaultResult( params, meta, result);
	}
	
	@DomainAuth(AuthType.Modify)
	public IResult delC340M01A(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
				
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C340M01A c340m01a = clsService.findC340M01A_oid(mainOid);
		if(c340m01a!=null){
			c340m01a.setDeletedTime(CapDate.getCurrentTimestamp());
			clsService.save(c340m01a);
		}		
		return result;
	}
	
	private String check_data_relation(C340M01A c340m01a){
		//===========================
		// 若簽報書核准後，有被退回修改
		if(c340m01a==null){
			return "查無 契約書資料";
		}
		L120M01A l120m01a = clsService.findL120M01A_mainId(c340m01a.getCaseMainId());
		if(l120m01a==null){
			return "查無 簽報書資料"+"("+c340m01a.getCaseMainId()+")。"+"若退回且修改已核准簽報書，請重新產製契約書。";
		}
		for(C340M01B c340m01b : clsService.findC340M01B(c340m01a.getMainId())){
			L140M01A l140m01a = clsService.findL140M01A_mainId(c340m01b.getTabMainId());
			if(l140m01a==null){
				return "查無 額度明細表資料("+c340m01b.getTabMainId()+")。"+"若退回且修改已核准簽報書，請重新產製契約書。";
			}
		}
		return "";
	}
	
	@DomainAuth(AuthType.Modify)
	public IResult check_C340(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
				
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C340M01A meta = clsService.findC340M01A_oid(mainOid);
		if(true){
			String err_msg = check_data_relation(meta);
			if(Util.isNotEmpty(err_msg)){
				throw new CapMessageException(err_msg, getClass());
			}
		}
		if(true){			
			String msg = "";
			String prodKind_column = prodKind_column(meta);
			String unKeyIn_column = unKeyIn_column(meta);
			if(Util.isNotEmpty(prodKind_column)){
				msg = prodKind_column;
			}
			if(Util.isNotEmpty(unKeyIn_column)){
				if(Util.isNotEmpty(msg)){
					msg = msg + "<br/>";
				}
				msg = msg + unKeyIn_column;
			}

			//J-110-0212_10702_B1001 Web e-Loan 調整消金契約書購屋貸款契約書邏輯，新增利率、開辦費比對檢核
			List<C340M01C> c340m01c_list = clsService.findC340M01C(meta.getMainId());
			for(C340M01C c340m01c : c340m01c_list){
				if(Util.isNotEmpty(c340m01c.getInitialJsonData()))
				{
					String jsonData = Util.trim(c340m01c.getJsonData());
					String initialJsonData = Util.trim(c340m01c.getInitialJsonData());
					JSONObject jsonObject = DataParse.toJSON(jsonData);
					JSONObject initialJsonObject = DataParse.toJSON(initialJsonData);

					BigDecimal feeNo01 = Util.parseBigDecimal(jsonObject.optString("eloan_pa_repay_feeNo01"));
					BigDecimal feeNo02 = Util.parseBigDecimal(jsonObject.optString("eloan_pa_repay_feeNo02"));
					String noPPP_1t1 = jsonObject.optString("eloan_pa_intr_noPPP_1t1");
					String noPPP_1t2 = jsonObject.optString("eloan_pa_intr_noPPP_1t2");
					String noPPP_2t1 = jsonObject.optString("eloan_pa_intr_noPPP_2t1");
					String noPPP_3t1 = jsonObject.optString("eloan_pa_intr_noPPP_3t1");
					String noPPP_4t1 = jsonObject.optString("eloan_pa_intr_noPPP_4t1");
					String noPPP_baseRate = jsonObject.optString("eloan_pa_intr_noPPP_baseRate");
					String withPPP_1x3 = jsonObject.optString("eloan_pa_intr_withPPP_1x3");
					String withPPP_1y3 = jsonObject.optString("eloan_pa_intr_withPPP_1y3");
					String withPPP_1y4 = jsonObject.optString("eloan_pa_intr_withPPP_1y4");
					String withPPP_1y7 = jsonObject.optString("eloan_pa_intr_withPPP_1y7");
					String withPPP_1y8 = jsonObject.optString("eloan_pa_intr_withPPP_1y8");
					String withPPP_baseRate = jsonObject.optString("eloan_pa_intr_withPPP_baseRate");

					BigDecimal initialFeeNo01 = Util.parseBigDecimal(initialJsonObject.optString("eloan_pa_repay_feeNo01"));
					BigDecimal initialFeeNo02 = Util.parseBigDecimal(initialJsonObject.optString("eloan_pa_repay_feeNo02"));
					String initial_noPPP_1t1 = initialJsonObject.optString("eloan_pa_intr_noPPP_1t1");
					String initial_noPPP_1t2 = initialJsonObject.optString("eloan_pa_intr_noPPP_1t2");
					String initial_noPPP_2t1 = initialJsonObject.optString("eloan_pa_intr_noPPP_2t1");
					String initial_noPPP_3t1 = initialJsonObject.optString("eloan_pa_intr_noPPP_3t1");
					String initial_noPPP_4t1 = initialJsonObject.optString("eloan_pa_intr_noPPP_4t1");
					String initial_noPPP_baseRate = initialJsonObject.optString("eloan_pa_intr_noPPP_baseRate");
					String initial_withPPP_1x3 = initialJsonObject.optString("eloan_pa_intr_withPPP_1x3");
					String initial_withPPP_1y3 = initialJsonObject.optString("eloan_pa_intr_withPPP_1y3");
					String initial_withPPP_1y4 = initialJsonObject.optString("eloan_pa_intr_withPPP_1y4");
					String initial_withPPP_1y7 = initialJsonObject.optString("eloan_pa_intr_withPPP_1y7");
					String initial_withPPP_1y8 = initialJsonObject.optString("eloan_pa_intr_withPPP_1y8");
					String initial_withPPP_baseRate = initialJsonObject.optString("eloan_pa_intr_withPPP_baseRate");

					if(	!Util.equals(initialFeeNo01,feeNo01)
							|| !Util.equals(initialFeeNo02,feeNo02)
							|| !Util.equals(initial_noPPP_1t1,noPPP_1t1)
							|| !Util.equals(initial_noPPP_1t2,noPPP_1t2)
							|| !Util.equals(initial_noPPP_2t1,noPPP_2t1)
							|| !Util.equals(initial_noPPP_3t1,noPPP_3t1)
							|| !Util.equals(initial_noPPP_4t1,noPPP_4t1)
							|| !Util.equals(initial_noPPP_baseRate,noPPP_baseRate)
							|| !Util.equals(initial_withPPP_1x3,withPPP_1x3)
							|| !Util.equals(initial_withPPP_1y3,withPPP_1y3)
							|| !Util.equals(initial_withPPP_1y4,withPPP_1y4)
							|| !Util.equals(initial_withPPP_1y7,withPPP_1y7)
							|| !Util.equals(initial_withPPP_1y8,withPPP_1y8)
							|| !Util.equals(initial_withPPP_baseRate,withPPP_baseRate))
					{
						//cls3401m01.alertRateMsg=本案敘做條件與核定條件不符，請確認是否違反公平待客原則！
						if(!msg.contains(prop_cls3401m01.getProperty("cls3401m01.alertRateMsg"))){
							if(Util.isNotEmpty(msg)){
								msg = msg + "<br/>";
							}
							msg = msg + prop_cls3401m01.getProperty("cls3401m01.alertRateMsg");
							result.set("model_c340m01cDiff", "Y");
						}
					}
				}
			}
			
			if(Util.isNotEmpty(msg)){
				result.set("msg", msg);
			}
		}
		
		
		if (true){			
			String errMsg_chk_rate_gt_16percent = errMsg_chk_rate_gt_16percent(meta);
			if(Util.isNotEmpty(errMsg_chk_rate_gt_16percent)){
				throw new CapMessageException(errMsg_chk_rate_gt_16percent, getClass());
			}
		}
		
		result.set("model_custId", Util.trim(meta.getCustId()));
		result.set("model_custName", Util.trim(meta.getCustName()));
		result.set("model_caseNo", Util.trim(meta.getCaseNo()));
		result.set("model_ctrTypeDesc", get_ctrTyype_desc(meta.getCtrType()));	
		return result;
	}
	
	private String errMsg_chk_rate_gt_16percent(C340M01A c340m01a)
	throws CapException {
		if (clsService.is_function_on_codetype("chk_rate_gt_16percent_C340_M01") 
				&& chk_rate_gt_16percent(c340m01a)){			
			//J-111-0340 e-Loan 簽報書及對保契約書檢核利率大於16%時無法送出覆核
			return "依程修(111)第(2063)號檢核規則：利率大於16%時無法送出覆核";		
		}
		return "";
	}
	
	private boolean chk_rate_gt_16percent(C340M01A c340m01a) 
	throws CapException {
		Map<String, Object> map = new HashMap<String, Object>();
		List<C340M01C> c340m01c_list = clsService.findC340M01C(c340m01a.getMainId());
		for (C340M01C c340m01c : c340m01c_list) {
			String jsonData = Util.trim(c340m01c.getJsonData());
			JSONObject jsonObject = DataParse.toJSON(jsonData);

			LMSUtil.addJsonToMap(map, jsonObject);
		}
		if(Util.equals(ContractDocConstants.C340M01A_CtrType.Type_1, c340m01a.getCtrType())){ //購屋契約 => 內容順序：無限制清償、限制提前清償
			//註：購屋契約裡的{限制、無限制}的順序，和【純信用】、【其它類】這2種契約不同
			if(Util.equals("Y", MapUtils.getString(map, ContractDocConstants.CtrType1.ELOAN_PA_INTR_NOPPP_CB, ""))){ //無限制清償期間
				String str_baseRate = MapUtils.getString(map, ContractDocConstants.CtrType1.ELOAN_PA_INTR_NOPPP_BASERATE, "");
				if(Util.isNumeric(str_baseRate)){
					String[] plusRate_arr = new String[]{
							MapUtils.getString(map, ContractDocConstants.CtrType2.ELOAN_PA_INTR_NOPPP_1T1, "")
						, MapUtils.getString(map, ContractDocConstants.CtrType2.ELOAN_PA_INTR_NOPPP_2T1, "")
					};
					if(_rate_gt_16percent_baseRate_plus_plusRate(str_baseRate , plusRate_arr)){
						return true;	
					}
				}	
				//~~~~~~
				if(_rate_gt_16percent_sumResultRate(MapUtils.getString(map, ContractDocConstants.CtrType1.ELOAN_PA_INTR_NOPPP_1T2, ""))){
					return true;
				}
			}
			
			if(Util.equals("Y", MapUtils.getString(map, ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_CB, ""))){ //限制提前清償
				//第[ ]個月至第[ ]個月按[ ]%固定計息。 
				if(_rate_gt_16percent_fixRate(MapUtils.getString(map, ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_1X3, ""))){
					return true;
				}
				
				String str_baseRate = MapUtils.getString(map, ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_BASERATE, "");
				if(Util.isNumeric(str_baseRate)){ 
					String[] plusRate_arr = new String[]{
							MapUtils.getString(map, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1Y3, "")
						, MapUtils.getString(map, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1Y7, "")
					};
					if(_rate_gt_16percent_baseRate_plus_plusRate(str_baseRate , plusRate_arr)){
						return true;	
					}	
					
				}	
				//~~~~~~
				//按乙方撥款當日公告之消費金融放款指標利率加年利率[ ]％ （目前合計年利率[ ]％）
				if(_rate_gt_16percent_sumResultRate(MapUtils.getString(map, ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_1Y4, ""))){
					return true;
				}
				if(_rate_gt_16percent_sumResultRate(MapUtils.getString(map, ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_1Y8, ""))){
					return true;
				}
			}
		}else if(Util.equals(ContractDocConstants.C340M01A_CtrType.Type_2, c340m01a.getCtrType())){ //純信用無擔保(DBR22倍) => 內容順序：限制提前清償、無限制清償
			if(Util.equals("Y", MapUtils.getString(map, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_CB, ""))){ //限制提前清償
				//第[ ]個月至第[ ]個月按[ ]%固定計息。 
				if(_rate_gt_16percent_fixRate(MapUtils.getString(map, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1X3, ""))){
					return true;
				}
				
				String str_baseRate = MapUtils.getString(map, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_BASERATE, "");
				if(Util.isNumeric(str_baseRate)){ 
					String[] plusRate_arr = new String[]{
							MapUtils.getString(map, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1Y3, "")
						, MapUtils.getString(map, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1Y7, "")
						, MapUtils.getString(map, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1Y11, "")
					};
					if(_rate_gt_16percent_baseRate_plus_plusRate(str_baseRate , plusRate_arr)){
						return true;	
					}
				}		
			}
			if(Util.equals("Y", MapUtils.getString(map, ContractDocConstants.CtrType2.ELOAN_PA_INTR_NOPPP_CB, ""))){ //無限制清償期間
				String str_baseRate = MapUtils.getString(map, ContractDocConstants.CtrType2.ELOAN_PA_INTR_NOPPP_BASERATE, "");
				if(Util.isNumeric(str_baseRate)){ 
					String[] plusRate_arr = new String[]{
							MapUtils.getString(map, ContractDocConstants.CtrType2.ELOAN_PA_INTR_NOPPP_1T1, "")
						, MapUtils.getString(map, ContractDocConstants.CtrType2.ELOAN_PA_INTR_NOPPP_2T1, "")
						, MapUtils.getString(map, ContractDocConstants.CtrType2.ELOAN_PA_INTR_NOPPP_3T1, "")
					};
					if(_rate_gt_16percent_baseRate_plus_plusRate(str_baseRate , plusRate_arr)){
						return true;	
					}					
				}	
				
				//按乙方公告之行員消貸利率計算(目前[ ]為%)
				if(_rate_gt_16percent_N2Rate(MapUtils.getString(map, ContractDocConstants.CtrType2.ELOAN_PA_INTR_NOPPP_4T1, ""))){
					return true;
				}
			}
		}else if(Util.equals(ContractDocConstants.C340M01A_CtrType.Type_3, c340m01a.getCtrType())){ //其他類契約 => 內容順序：限制提前清償、無限制清償
			if(Util.equals("Y", MapUtils.getString(map, ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_CB, ""))){ //限制提前清償
				//第[ ]個月至第[ ]個月按[ ]%固定計息。 
				if(_rate_gt_16percent_fixRate(MapUtils.getString(map, ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_1X3, ""))){
					return true;
				}
				
				String str_baseRate = MapUtils.getString(map, ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_BASERATE, "");
				if(Util.isNumeric(str_baseRate)){ 
					String[] plusRate_arr = new String[]{
							MapUtils.getString(map, ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_1Y3, "")
						, MapUtils.getString(map, ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_1Y7, "")
					};
					if(_rate_gt_16percent_baseRate_plus_plusRate(str_baseRate , plusRate_arr)){
						return true;	
					}
				}				
			}
			if(Util.equals("Y", MapUtils.getString(map, ContractDocConstants.CtrType3.ELOAN_PA_INTR_NOPPP_CB, ""))){ //無限制清償期間
				String str_baseRate = MapUtils.getString(map, ContractDocConstants.CtrType3.ELOAN_PA_INTR_NOPPP_BASERATE, "");
				if(Util.isNumeric(str_baseRate)){ 
					String[] plusRate_arr = new String[]{
							MapUtils.getString(map, ContractDocConstants.CtrType3.ELOAN_PA_INTR_NOPPP_1T1, "")
						, MapUtils.getString(map, ContractDocConstants.CtrType3.ELOAN_PA_INTR_NOPPP_2T1, "")
						, MapUtils.getString(map, ContractDocConstants.CtrType3.ELOAN_PA_INTR_NOPPP_3T1, "")
					};
					if(_rate_gt_16percent_baseRate_plus_plusRate(str_baseRate , plusRate_arr)){
						return true;	
					}					
				}			
			}
		}
		return false;
	}
	
	private boolean _rate_gt_16percent_fixRate(String _rate){
		if(ClsUtility.is_rate_gt_16percent(CrsUtil.parseBigDecimal(_rate))){
			return true;
		}
		return false;
	}
	private boolean _rate_gt_16percent_sumResultRate(String _rate){
		if(ClsUtility.is_rate_gt_16percent(CrsUtil.parseBigDecimal(_rate))){
			return true;
		}
		return false;
	}
	private boolean _rate_gt_16percent_N2Rate(String _rate){
		if(ClsUtility.is_rate_gt_16percent(CrsUtil.parseBigDecimal(_rate))){
			return true;
		}
		return false;
	}
	private boolean _rate_gt_16percent_baseRate_plus_plusRate(String baseRate, String[] plusRate_arr){
		for(String plusRate : plusRate_arr){
			BigDecimal nowRate = CrsUtil.parseBigDecimal(baseRate).add(CrsUtil.parseBigDecimal(plusRate));
			if(ClsUtility.is_rate_gt_16percent(nowRate)){
				return true;
			}	
		}		
		return false;
	}

	private String prodKind_column(C340M01A c340m01a)
	throws CapException{
		String mainid = c340m01a.getMainId();
		List<Map<String, Object>> L140s02aSum = eloandbBASEService.findL140s02aByc340m01b(mainid);
		if(L140s02aSum.size() > 1){ //資料筆數大於1筆，表示該額度明細表下有多種產品種類
			return prop_cls3401m01.getProperty("msg.prodkind");
		}
		return "";
	}
	
	private String unKeyIn_column(C340M01A c340m01a)
	throws CapException{
		String ctrType = Util.trim(c340m01a.getCtrType());
		if(Util.equals(ctrType, ContractDocConstants.C340M01A_CtrType.Type_1)){
			return unKeyIn_column_ctrType1(c340m01a);
		}else if(Util.equals(ctrType, ContractDocConstants.C340M01A_CtrType.Type_2)){
			return unKeyIn_column_ctrType2(c340m01a);
		}else if(Util.equals(ctrType, ContractDocConstants.C340M01A_CtrType.Type_3)){
			return unKeyIn_column_ctrType3(c340m01a);
		}
		return "";
	}
	private String unKeyIn_column_ctrType1(C340M01A c340m01a)
	throws CapException{	
		Map<String, Object> map = new HashMap<String, Object>();
		List<C340M01C> c340m01c_list = clsService.findC340M01C(c340m01a.getMainId()); 
		for(C340M01C c340m01c : c340m01c_list){
			String jsonData = Util.trim(c340m01c.getJsonData());
			JSONObject jsonObject = DataParse.toJSON(jsonData); 
			
			LMSUtil.addJsonToMap(map, jsonObject);
		}
		
		List<String> list = new ArrayList<String>();
		if(true){
			String[] cols_deliv = new String[]{ContractDocConstants.CtrType1.ELOAN_PA_DELIV_A_CB
					, ContractDocConstants.CtrType1.ELOAN_PA_DELIV_B_CB
					, ContractDocConstants.CtrType1.ELOAN_PA_DELIV_C_CB
					, ContractDocConstants.CtrType1.ELOAN_PA_DELIV_D_CB
					, ContractDocConstants.CtrType1.ELOAN_PA_DELIV_E_CB};
			if(!have_keyin_data(map, cols_deliv)){
				list.add(MessageFormat.format(prop_cls3401m01.getProperty("msg.unkeyin"), prop_cls3401m01.getProperty("column.deliv")));
			}			
		}
		if(true){
			String[] cols_deliv = new String[]{ContractDocConstants.CtrType1.ELOAN_PA_USE_A_CB
					, ContractDocConstants.CtrType1.ELOAN_PA_USE_B_CB};
			if(!have_keyin_data(map, cols_deliv)){
				list.add(MessageFormat.format(prop_cls3401m01.getProperty("msg.unkeyin"), prop_cls3401m01.getProperty("column.use")));
			}			
		}
		if(true){
			String[] cols_deliv = new String[]{ContractDocConstants.CtrType1.ELOAN_PA_REPAY_A_CB
					, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_B_CB
					, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_C_CB
					, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_D_CB
					, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_E_CB
					, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_F_CB};
			if(!have_keyin_data(map, cols_deliv)){
				list.add(MessageFormat.format(prop_cls3401m01.getProperty("msg.unkeyin"), prop_cls3401m01.getProperty("column.repay")));
			}			
		}
		if(true){
			String[] cols_deliv = new String[]{ContractDocConstants.CtrType1.ELOAN_PA_INTR_NOPPP_CB
					, ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_CB
					, ContractDocConstants.CtrType1.ELOAN_PA_INTR_OTHER_CB};
			if(!have_keyin_data(map, cols_deliv)){
				list.add(MessageFormat.format(prop_cls3401m01.getProperty("msg.unkeyin"), prop_cls3401m01.getProperty("column.intr")));
			}			
		}
		return StringUtils.join(list, "<br/>");
	}
	
	private String unKeyIn_column_ctrType2(C340M01A c340m01a)
	throws CapException{	
		Map<String, Object> map = new HashMap<String, Object>();
		List<C340M01C> c340m01c_list = clsService.findC340M01C(c340m01a.getMainId()); 
		for(C340M01C c340m01c : c340m01c_list){
			String jsonData = Util.trim(c340m01c.getJsonData());
			JSONObject jsonObject = DataParse.toJSON(jsonData); 
			
			LMSUtil.addJsonToMap(map, jsonObject);
		}
		
		List<String> list = new ArrayList<String>();	
		if(true){
			String[] cols_deliv = new String[]{ContractDocConstants.CtrType2.ELOAN_PA_PURPOSE_WAY};
			if(!have_keyin_data(map, cols_deliv)){
				list.add(MessageFormat.format(prop_cls3401m04.getProperty("msg.unkeyin"), prop_cls3401m04.getProperty("column.purpose")));
			}			
		}
		if(true){
			String[] cols_deliv = new String[]{ContractDocConstants.CtrType2.ELOAN_PA_USE_A_T1
					, ContractDocConstants.CtrType2.ELOAN_PA_USE_A_T2		
					, ContractDocConstants.CtrType2.ELOAN_PA_USE_A_T3
					, ContractDocConstants.CtrType2.ELOAN_PA_USE_A_T4
					, ContractDocConstants.CtrType2.ELOAN_PA_USE_A_T5
					, ContractDocConstants.CtrType2.ELOAN_PA_USE_A_T6
					, ContractDocConstants.CtrType2.ELOAN_PA_USE_B_T1
					, ContractDocConstants.CtrType2.ELOAN_PA_USE_B_T2
					, ContractDocConstants.CtrType2.ELOAN_PA_USE_B_T3
					, ContractDocConstants.CtrType2.ELOAN_PA_USE_B_T4
					, ContractDocConstants.CtrType2.ELOAN_PA_USE_B_T5
					, ContractDocConstants.CtrType2.ELOAN_PA_USE_B_T6
					, ContractDocConstants.CtrType2.ELOAN_PA_USE_B_T7
					, ContractDocConstants.CtrType2.ELOAN_PA_USE_B_T8
					, ContractDocConstants.CtrType2.ELOAN_PA_USE_C_T1
					, ContractDocConstants.CtrType2.ELOAN_PA_USE_C_T2
					, ContractDocConstants.CtrType2.ELOAN_PA_USE_C_T3
					, ContractDocConstants.CtrType2.ELOAN_PA_USE_C_T4
					, ContractDocConstants.CtrType2.ELOAN_PA_USE_C_T5
					, ContractDocConstants.CtrType2.ELOAN_PA_USE_C_T6
					, ContractDocConstants.CtrType2.ELOAN_PA_USE_C_T7
					, ContractDocConstants.CtrType2.ELOAN_PA_USE_C_T8
					, ContractDocConstants.CtrType2.ELOAN_PA_USE_C_T9
					, ContractDocConstants.CtrType2.ELOAN_PA_USE_D_T1
					, ContractDocConstants.CtrType2.ELOAN_PA_USE_D_T2
					, ContractDocConstants.CtrType2.ELOAN_PA_USE_D_T3
					, ContractDocConstants.CtrType2.ELOAN_PA_USE_D_T4
					, ContractDocConstants.CtrType2.ELOAN_PA_USE_D_T5
					, ContractDocConstants.CtrType2.ELOAN_PA_USE_D_T6
					, ContractDocConstants.CtrType2.ELOAN_PA_USE_D_T7
					, ContractDocConstants.CtrType2.ELOAN_PA_USE_D_T8
					, ContractDocConstants.CtrType2.ELOAN_PA_USE_D_T9
					, ContractDocConstants.CtrType2.ELOAN_PA_USE_E_T1
					};
			if(!have_keyin_data(map, cols_deliv)){
				list.add(MessageFormat.format(prop_cls3401m04.getProperty("msg.unkeyin"), prop_cls3401m04.getProperty("column.use")));
			}			
		}
		if(true){
			String[] cols_deliv = new String[]{ContractDocConstants.CtrType2.ELOAN_PA_REPAY_WAY};
			if(!have_keyin_data(map, cols_deliv)){
				list.add(MessageFormat.format(prop_cls3401m04.getProperty("msg.unkeyin"), prop_cls3401m04.getProperty("column.repay")));
			}			
		}
		if(true){
			String[] cols_deliv = new String[]{ContractDocConstants.CtrType2.ELOAN_PA_INTR_NOPPP_CB
					, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_CB
					, ContractDocConstants.CtrType2.ELOAN_PA_INTR_OTHER_CB};
			if(!have_keyin_data(map, cols_deliv)){
				list.add(MessageFormat.format(prop_cls3401m04.getProperty("msg.unkeyin"), prop_cls3401m04.getProperty("column.intr")));
			}			
		}
		return StringUtils.join(list, "<br/>");
	}
	
	private String unKeyIn_column_ctrType3(C340M01A c340m01a)
	throws CapException{	
		Map<String, Object> map = new HashMap<String, Object>();
		List<C340M01C> c340m01c_list = clsService.findC340M01C(c340m01a.getMainId()); 
		for(C340M01C c340m01c : c340m01c_list){
			String jsonData = Util.trim(c340m01c.getJsonData());
			JSONObject jsonObject = DataParse.toJSON(jsonData); 
			
			LMSUtil.addJsonToMap(map, jsonObject);
		}
		
		List<String> list = new ArrayList<String>();	
		if(true){
			String[] cols_deliv = new String[]{ContractDocConstants.CtrType3.ELOAN_PA_PURPOSE_WAY};
			if(!have_keyin_data(map, cols_deliv)){
				list.add(MessageFormat.format(prop_cls3401m03.getProperty("msg.unkeyin"), prop_cls3401m03.getProperty("column.purpose")));
			}			
		}
		if(true){
			String[] cols_deliv = new String[]{ContractDocConstants.CtrType3.ELOAN_PA_USE_A_T1
					, ContractDocConstants.CtrType3.ELOAN_PA_USE_A_T2		
					, ContractDocConstants.CtrType3.ELOAN_PA_USE_A_T3
					, ContractDocConstants.CtrType3.ELOAN_PA_USE_A_T4
					, ContractDocConstants.CtrType3.ELOAN_PA_USE_A_T5		
					, ContractDocConstants.CtrType3.ELOAN_PA_USE_A_T6
					, ContractDocConstants.CtrType3.ELOAN_PA_USE_A_T7
					, ContractDocConstants.CtrType3.ELOAN_PA_USE_A_T8
					, ContractDocConstants.CtrType3.ELOAN_PA_USE_B_T1
					, ContractDocConstants.CtrType3.ELOAN_PA_USE_B_T2
					, ContractDocConstants.CtrType3.ELOAN_PA_USE_B_T3
					, ContractDocConstants.CtrType3.ELOAN_PA_USE_B_T4
					, ContractDocConstants.CtrType3.ELOAN_PA_USE_B_T5
					, ContractDocConstants.CtrType3.ELOAN_PA_USE_B_T6
					, ContractDocConstants.CtrType3.ELOAN_PA_USE_B_T7
					, ContractDocConstants.CtrType3.ELOAN_PA_USE_B_T8
					, ContractDocConstants.CtrType3.ELOAN_PA_USE_B_T9
					, ContractDocConstants.CtrType3.ELOAN_PA_USE_C_T1
					, ContractDocConstants.CtrType3.ELOAN_PA_USE_C_T2
					, ContractDocConstants.CtrType3.ELOAN_PA_USE_C_T3
					, ContractDocConstants.CtrType3.ELOAN_PA_USE_C_T4
					, ContractDocConstants.CtrType3.ELOAN_PA_USE_C_T5
					, ContractDocConstants.CtrType3.ELOAN_PA_USE_C_T6
					, ContractDocConstants.CtrType3.ELOAN_PA_USE_C_T7
					, ContractDocConstants.CtrType3.ELOAN_PA_USE_C_T8
					, ContractDocConstants.CtrType3.ELOAN_PA_USE_C_T9
					, ContractDocConstants.CtrType3.ELOAN_PA_USE_D_T1
					, ContractDocConstants.CtrType3.ELOAN_PA_USE_D_T2
					, ContractDocConstants.CtrType3.ELOAN_PA_USE_D_T3
					, ContractDocConstants.CtrType3.ELOAN_PA_USE_D_T4
					, ContractDocConstants.CtrType3.ELOAN_PA_USE_D_T5
					, ContractDocConstants.CtrType3.ELOAN_PA_USE_D_T6
					, ContractDocConstants.CtrType3.ELOAN_PA_USE_E_T1
					};
			if(!have_keyin_data(map, cols_deliv)){
				list.add(MessageFormat.format(prop_cls3401m03.getProperty("msg.unkeyin"), prop_cls3401m03.getProperty("column.use")));
			}			
		}
		if(true){
			String[] cols_deliv = new String[]{ContractDocConstants.CtrType3.ELOAN_PA_REPAY_WAY};
			if(!have_keyin_data(map, cols_deliv)){
				list.add(MessageFormat.format(prop_cls3401m03.getProperty("msg.unkeyin"), prop_cls3401m03.getProperty("column.repay")));
			}			
		}
		if(true){
			String[] cols_deliv = new String[]{ContractDocConstants.CtrType3.ELOAN_PA_INTR_NOPPP_CB
					, ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_CB
					, ContractDocConstants.CtrType3.ELOAN_PA_INTR_OTHER_CB};
			if(!have_keyin_data(map, cols_deliv)){
				list.add(MessageFormat.format(prop_cls3401m03.getProperty("msg.unkeyin"), prop_cls3401m03.getProperty("column.intr")));
			}			
		}
		return StringUtils.join(list, "<br/>");
	}
	
	private boolean have_keyin_data(Map<String, Object> map, String[] arr){
		if(arr!=null && arr.length>0){
			for(String key: arr){
				if(!map.containsKey(key)){
					continue;
				}
				Object obj = map.get(key);
				if(obj!=null && Util.isNotEmpty(Util.trim(obj))){
					return true;
				}
			}
		}
		return false;
	}
	
	private String get_ctrTyype_desc(String ctrType){
		if(Util.equals(ctrType, ContractDocConstants.C340M01A_CtrType.Type_1)){
			return prop_cls3401m01.getProperty("C340M01A.ctrType.1");
		}else if(Util.equals(ctrType, ContractDocConstants.C340M01A_CtrType.Type_2)){
			return prop_cls3401m01.getProperty("C340M01A.ctrType.2");
		}else if(Util.equals(ctrType, ContractDocConstants.C340M01A_CtrType.Type_3)){
			return prop_cls3401m01.getProperty("C340M01A.ctrType.3");
		}
		return "";		
	}
	
	private void inject_accountQuery_idDupList(CapAjaxFormResult result, C340M01A meta){
		LinkedHashSet<String> accountQuery_idDupList = new LinkedHashSet<String>();
		accountQuery_idDupList.add(meta.getCustId()+"-"+meta.getDupNo());	
		for(C340M01B c340m01b : clsService.findC340M01B(meta.getMainId())){
			L140M01A l140m01a  = clsService.findL140M01A_mainId(c340m01b.getTabMainId());
			for(L140S01A l140s01a : clsService.findL140S01A(l140m01a)){
				if(Util.equals(UtilConstants.lngeFlag.共同借款人, l140s01a.getCustPos())){
					accountQuery_idDupList.add(l140s01a.getCustId()+"-"+l140s01a.getDupNo());
				}
			}
			
		}	
		result.set("accountQuery_idDupList", StringUtils.join(accountQuery_idDupList, "|"));
		
	} 
	private CapAjaxFormResult defaultResult(PageParameters params, C340M01A meta,
			CapAjaxFormResult result) throws CapException {		
		// required information
		result.set(EloanConstants.PAGE, Util.trim(params.getString(EloanConstants.PAGE)));
		result.set(EloanConstants.MAIN_OID, Util.trim(meta.getOid()));
		result.set(EloanConstants.MAIN_DOC_STATUS, Util.trim(meta.getDocStatus()));
		result.set(EloanConstants.MAIN_ID, Util.trim(meta.getMainId()));
		result.set("custId", meta.getCustId());
		result.set("ctrType", meta.getCtrType());
		result.set("custInfo", Util.trim(meta.getCustId()) + " " + Util.trim(meta.getDupNo())+ " " + Util.trim(meta.getCustName()));
		result.set("ctrTypeHeaderDesc", get_ctrTyype_desc(meta.getCtrType()));
		return result;
	}
	
	/* 連線/FireWall是否開通
	   $.ajax({handler: "cls3401m01formhandler",action: "conn_ploan", data: {'ploanCtrNo':'ZZZZ_TEST_YYMMDD_HHmm'},success: function(json){}});	
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult conn_ploan(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String ploanCtrNo = params.getString("ploanCtrNo");
		C340M01A c340m01a = new C340M01A();
		c340m01a.setPloanCtrNo(ploanCtrNo);
		contractDocService.ploan_discardContract(c340m01a);
		return result;
	}
	/**
	 * 判斷登入者是否僅有EL電銷權限
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult check_only_expermission(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		result.set("only_ex_permission", user.isEXAuth());//是否僅有電銷權限	
		return result;
	}
}
