/* 
 * DW_RKCREDIT.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.mfaloan.bean;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

import tw.com.iisi.cap.model.GenericBean;


/** 個人信用評分評等紀錄 **/
public class DW_RKCREDIT extends GenericBean{

	private static final long serialVersionUID = 1L;

	/** 分行別 **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="BR_CD", length=3, columnDefinition="CHAR(3)", nullable=false,unique = true)
	private String br_cd;

	/** NOTES文件編號 **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="NOTEID", length=32, columnDefinition="CHAR(32)", nullable=false,unique = true)
	private String noteid;

	/** 客戶統一編號 **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="CUSTID", length=10, columnDefinition="CHAR(10)", nullable=false,unique = true)
	private String custid;

	/** 重複序號 **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)", nullable=false,unique = true)
	private String dupno;

	/** 評等模型類別 **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="MOWTYPE", length=1, columnDefinition="CHAR(1)", nullable=false,unique = true)
	private String mowtype;

	/** 模型版本-大版 **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="MOWVER1", columnDefinition="DECIMAL(5,0)", nullable=false,unique = true)
	private Integer mowver1;

	/** 模型版本-小版 **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="MOWVER2", columnDefinition="DECIMAL(5)", nullable=false,unique = true)
	private Integer mowver2;

	/** JCIC查詢日期 YYYY-MM-DD **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Temporal(TemporalType.DATE)
	@Column(name="JCIC_DATE", columnDefinition="DATE", nullable=false,unique = true)
	private Date jcic_date;

	/**  **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="ACCT_KEY", length=14, columnDefinition="CHAR(14)", nullable=false,unique = true)
	private String acct_key;
	
	/** 主借款人統一編號(CUSTKEY) **/
	@Column(name="CUST_KEY", length=10, columnDefinition="CHAR(10)")
	private String cust_key;

	/** 
	 * 相關身分 ( LNGEFLAG)<p/>
	 * M: 主借款人  C: 共同借款人  G: 連帶保證人
	 */
	@Column(name="LNGEFLAG", length=1, columnDefinition="CHAR(1)")
	private String lngeflag;

	/** 
	 * A<p/>
	 * 500
	 */
	@Column(name="BASE_A", columnDefinition="DECIMAL(4,0)")
	private BigDecimal base_a;

	/** 
	 * B<p/>
	 * 61.6414
	 */
	@Column(name="BASE_B", columnDefinition="DECIMAL(8,4)")
	private BigDecimal base_b;

	/** 
	 * 常數項<p/>
	 * 3.4192426
	 */
	@Column(name="BASE_S", columnDefinition="DECIMAL(12,8)")
	private BigDecimal base_s;

	/** 
	 * 基準底分<p/>
	 * A+ B*常數項
	 */
	@Column(name="BASE_SCORE", columnDefinition="DECIMAL(14,8)")
	private BigDecimal base_score;

	/** 
	 * 加總各變量得分<p/>
	 * 上表14項得分
	 */
	@Column(name="TOTAL_SCORE", columnDefinition="DECIMAL(14,8)")
	private BigDecimal total_score;

	/** 
	 * 初始評分<p/>
	 * "=基準底分+Σ各變量得分"
	 */
	@Column(name="INITIAL_SCORE", columnDefinition="DECIMAL(14,8)")
	private BigDecimal initial_score;

	/** 
	 * 預測壞率<p/>
	 * "=1/1+EXP((初始評分-A)/B)"
	 */
	@Column(name="PREDICT_BAD_RATE", columnDefinition="DECIMAL(14,8)")
	private BigDecimal predict_bad_rate;

	/** 
	 * 初始評等<p/>
	 * 初始評分分數區間對應之評等等級
	 */
	@Column(name="INITIAL_RATING", columnDefinition="DECIMAL(2,0)")
	private Integer initial_rating;

	/** 
	 * 調整評等<p/>
	 * 升降等數(+  /-  ){卡友貸：Spr- Final_rating, 房貸、非房貸：Initial_rating - Final_rating}
	 */
	@Column(name="ADJ_RATING", columnDefinition="DECIMAL(2,0)")
	private Integer adj_rating;

	/** 
	 * 最終評等<p/>
	 * 初始評等經升降等後結果
	 */
	@Column(name="FINAL_RATING", columnDefinition="DECIMAL(2,0)")
	private Integer final_rating;

	/** 
	 * 出現聯徵特殊負面資訊<p/>
	 * Y / N
	 */
	@Column(name="JCIC_WARNING_FLAG", length=1, columnDefinition="CHAR(1)")
	private String jcic_warning_flag;

	/** 
	 * 本案為最終採用之關係人評等<p/>
	 * Y / N
	 */
	@Column(name="FINAL_RATING_FLAG", length=1, columnDefinition="CHAR(1)")
	private String final_rating_flag;

	/** 
	 * 刪除本筆紀錄原因<p/>
	 * A1~A4
	 */
	@Column(name="DELETE_REASON", length=2, columnDefinition="CHAR(2)")
	private String delete_reason;

	/** 
	 * 刪除原因為其他時之理由文字<p/>
	 * 刪除本筆紀錄原因為A4其他時須詳述理由
	 */
	@Column(name="REJECT_OTHEREASON_TEXT", length=200, columnDefinition="CHAR(200)")
	private String reject_othereason_text;

	/** 
	 * 文件狀態<p/>
	 * 編製中|1待覆核|2待母行覆核 | 2C核准|3婉卻|4呈區域授信中心|5呈總行法金處/授管處|6待補件 | 7提放審會|H1提常董會|H2審核中|A已會簽|B會簽中|C會簽待覆核|2A
	 */
	@Column(name="DOCSTATUS", length=2, columnDefinition="CHAR(2)")
	private String docstatus;

	/** 上傳資料日期。J-108-0278 將 DATA_SRC_DT 納入PK 欄位之一 **/
	@Temporal(TemporalType.DATE)
	@Column(name="DATA_SRC_DT", columnDefinition="DATE", nullable=false,unique = true)
	private Date data_src_dt;
	
	/** 違約機率  **/
	@Column(name="DR", columnDefinition="DECIMAL(8,5)")
	private BigDecimal dr;
	
	/** 違約機率(預估1年期) **/
	@Column(name="DR_1YR", columnDefinition="DECIMAL(8,5)")
	private BigDecimal dr_1yr;
	
	/** 卡友貸旗標 */
	@Column(name="C_FLAG", length=1, columnDefinition="CHAR(1)")
	private String c_flag;

	/** 支援評等 **/
	@Column(name="SPR", columnDefinition="DEC(2,0)")
	private Integer spr;
	
	/** J10信用評分 **/
	@Column(name="J10_SCORE", columnDefinition="DEC(4,0)")
	private Integer j10_score;

	/** 理由代碼一 */
	@Column(name="J10_REASON_CODE1", length=3, columnDefinition="CHAR(3)")
	private String j10_reason_code1;

	/** 理由代碼二 */
	@Column(name="J10_REASON_CODE2", length=3, columnDefinition="CHAR(3)")
	private String j10_reason_code2;

	/** 理由代碼三 */
	@Column(name="J10_REASON_CODE3", length=3, columnDefinition="CHAR(3)")
	private String j10_reason_code3;

	/** 理由代碼四 */
	@Column(name="J10_REASON_CODE4", length=3, columnDefinition="CHAR(3)")
	private String j10_reason_code4;
	
	/** 外部J10評等升降等數 **/
	@Column(name="JR_AUTODG", columnDefinition="DEC(2,0)")
	private Integer jr_autodg;
	
	/** ----------消金房貸3.0 Start---------- **/
	/*SLOPE斜率* 
	 * 0.132 <p/>
	 */
	@Column(name="SLOPE", columnDefinition="DECIMAL(6,4)")
	private BigDecimal slope;
	
	/*INTERCEPT 截距* 
	 * -6.143 <p/>
	 */
	@Column(name="INTERCEPT", columnDefinition="DECIMAL(6,4)")
	private BigDecimal intercept;
	
	/** 查詢組合        註：未直接使用 setProdId(String prodId)而是用 json 去塞值，可查找  (1)ClsConstants.C101S01E.查詢組合    (2)ClsScoreUtil.PRODID_KEY */
    @Column(name = "PRODID", length = 2, columnDefinition = "VARCHAR(2)")
	private String prodId;
	
	/** ----------消金房貸3.0 End---------- **/
	
	/** 取得分行別 **/
	public String getBr_cd() {
		return this.br_cd;
	}
	/** 設定分行別 **/
	public void setBr_cd(String value) {
		this.br_cd = value;
	}

	/** 取得NOTES文件編號 **/
	public String getNoteid() {
		return this.noteid;
	}
	/** 設定NOTES文件編號 **/
	public void setNoteid(String value) {
		this.noteid = value;
	}

	/** 取得客戶統一編號 **/
	public String getCustid() {
		return this.custid;
	}
	/** 設定客戶統一編號 **/
	public void setCustid(String value) {
		this.custid = value;
	}

	/** 取得重複序號 **/
	public String getDupno() {
		return this.dupno;
	}
	/** 設定重複序號 **/
	public void setDupno(String value) {
		this.dupno = value;
	}

	/** 取得評等模型類別 **/
	public String getMowtype() {
		return this.mowtype;
	}
	/** 設定評等模型類別 **/
	public void setMowtype(String value) {
		this.mowtype = value;
	}

	/** 取得模型版本-大版 **/
	public Integer getMowver1() {
		return this.mowver1;
	}
	/** 設定模型版本-大版 **/
	public void setMowver1(Integer value) {
		this.mowver1 = value;
	}

	/** 取得模型版本-小版 **/
	public Integer getMowver2() {
		return this.mowver2;
	}
	/** 設定模型版本-小版 **/
	public void setMowver2(Integer value) {
		this.mowver2 = value;
	}

	/** 取得JCIC查詢日期 YYYY-MM-DD **/
	public Date getJcic_date() {
		return this.jcic_date;
	}
	/** 設定JCIC查詢日期 YYYY-MM-DD **/
	public void setJcic_date(Date value) {
		this.jcic_date = value;
	}

	/** 取得主借款人統一編號(CUSTKEY) **/
	public String getCust_key() {
		return this.cust_key;
	}
	/** 設定主借款人統一編號(CUSTKEY) **/
	public void setCust_key(String value) {
		this.cust_key = value;
	}

	/** 
	 * 取得相關身分 ( LNGEFLAG)<p/>
	 * M: 主借款人  C: 共同借款人  G: 連帶保證人
	 */
	public String getLngeflag() {
		return this.lngeflag;
	}
	/**
	 *  設定相關身分 ( LNGEFLAG)<p/>
	 *  M: 主借款人  C: 共同借款人  G: 連帶保證人
	 **/
	public void setLngeflag(String value) {
		this.lngeflag = value;
	}

	/** 
	 * 取得A<p/>
	 * 500
	 */
	public BigDecimal getBase_a() {
		return this.base_a;
	}
	/**
	 *  設定A<p/>
	 *  500
	 **/
	public void setBase_a(BigDecimal value) {
		this.base_a = value;
	}

	/** 
	 * 取得B<p/>
	 * 61.6414
	 */
	public BigDecimal getBase_b() {
		return this.base_b;
	}
	/**
	 *  設定B<p/>
	 *  61.6414
	 **/
	public void setBase_b(BigDecimal value) {
		this.base_b = value;
	}

	/** 
	 * 取得常數項<p/>
	 * 3.4192426
	 */
	public BigDecimal getBase_s() {
		return this.base_s;
	}
	/**
	 *  設定常數項<p/>
	 *  3.4192426
	 **/
	public void setBase_s(BigDecimal value) {
		this.base_s = value;
	}

	/** 
	 * 取得基準底分<p/>
	 * A+ B*常數項
	 */
	public BigDecimal getBase_score() {
		return this.base_score;
	}
	/**
	 *  設定基準底分<p/>
	 *  A+ B*常數項
	 **/
	public void setBase_score(BigDecimal value) {
		this.base_score = value;
	}

	/** 
	 * 取得加總各變量得分<p/>
	 * 上表14項得分
	 */
	public BigDecimal getTotal_score() {
		return this.total_score;
	}
	/**
	 *  設定加總各變量得分<p/>
	 *  上表14項得分
	 **/
	public void setTotal_score(BigDecimal value) {
		this.total_score = value;
	}

	/** 
	 * 取得初始評分<p/>
	 * "=基準底分+Σ各變量得分"
	 */
	public BigDecimal getInitial_score() {
		return this.initial_score;
	}
	/**
	 *  設定初始評分<p/>
	 *  "=基準底分+Σ各變量得分"
	 **/
	public void setInitial_score(BigDecimal value) {
		this.initial_score = value;
	}

	/** 
	 * 取得預測壞率<p/>
	 * "=1/1+EXP((初始評分-A)/B)"
	 */
	public BigDecimal getPredict_bad_rate() {
		return this.predict_bad_rate;
	}
	/**
	 *  設定預測壞率<p/>
	 *  "=1/1+EXP((初始評分-A)/B)"
	 **/
	public void setPredict_bad_rate(BigDecimal value) {
		this.predict_bad_rate = value;
	}

	/** 
	 * 取得初始評等<p/>
	 * 初始評分分數區間對應之評等等級
	 */
	public Integer getInitial_rating() {
		return this.initial_rating;
	}
	/**
	 *  設定初始評等<p/>
	 *  初始評分分數區間對應之評等等級
	 **/
	public void setInitial_rating(Integer value) {
		this.initial_rating = value;
	}

	/** 
	 * 取得調整評等<p/>
	 * 升降等數(+  /-  )
	 */
	public Integer getAdj_rating() {
		return this.adj_rating;
	}
	/**
	 *  設定調整評等<p/>
	 *  升降等數(+  /-  )
	 **/
	public void setAdj_rating(Integer value) {
		this.adj_rating = value;
	}

	/** 
	 * 取得最終評等<p/>
	 * 初始評等經升降等後結果
	 */
	public Integer getFinal_rating() {
		return this.final_rating;
	}
	/**
	 *  設定最終評等<p/>
	 *  初始評等經升降等後結果
	 **/
	public void setFinal_rating(Integer value) {
		this.final_rating = value;
	}

	/** 
	 * 取得出現聯徵特殊負面資訊<p/>
	 * Y / N
	 */
	public String getJcic_warning_flag() {
		return this.jcic_warning_flag;
	}
	/**
	 *  設定出現聯徵特殊負面資訊<p/>
	 *  Y / N
	 **/
	public void setJcic_warning_flag(String value) {
		this.jcic_warning_flag = value;
	}

	/** 
	 * 取得本案為最終採用之關係人評等<p/>
	 * Y / N
	 */
	public String getFinal_rating_flag() {
		return this.final_rating_flag;
	}
	/**
	 *  設定本案為最終採用之關係人評等<p/>
	 *  Y / N
	 **/
	public void setFinal_rating_flag(String value) {
		this.final_rating_flag = value;
	}

	/** 
	 * 取得刪除本筆紀錄原因<p/>
	 * A1~A4
	 */
	public String getDelete_reason() {
		return this.delete_reason;
	}
	/**
	 *  設定刪除本筆紀錄原因<p/>
	 *  A1~A4
	 **/
	public void setDelete_reason(String value) {
		this.delete_reason = value;
	}

	/** 
	 * 取得刪除原因為其他時之理由文字<p/>
	 * 刪除本筆紀錄原因為A4其他時須詳述理由
	 */
	public String getReject_othereason_text() {
		return this.reject_othereason_text;
	}
	/**
	 *  設定刪除原因為其他時之理由文字<p/>
	 *  刪除本筆紀錄原因為A4其他時須詳述理由
	 **/
	public void setReject_othereason_text(String value) {
		this.reject_othereason_text = value;
	}

	/** 
	 * 取得文件狀態<p/>
	 * 編製中|1待覆核|2待母行覆核 | 2C核准|3婉卻|4呈區域授信中心|5呈總行法金處/授管處|6待補件 | 7提放審會|H1提常董會|H2審核中|A已會簽|B會簽中|C會簽待覆核|2A
	 */
	public String getDocstatus() {
		return this.docstatus;
	}
	/**
	 *  設定文件狀態<p/>
	 *  編製中|1待覆核|2待母行覆核 | 2C核准|3婉卻|4呈區域授信中心|5呈總行法金處/授管處|6待補件 | 7提放審會|H1提常董會|H2審核中|A已會簽|B會簽中|C會簽待覆核|2A
	 **/
	public void setDocstatus(String value) {
		this.docstatus = value;
	}

	/** 取得上傳資料日期 **/
	public Date getData_src_dt() {
		return this.data_src_dt;
	}
	/** 設定上傳資料日期 **/
	public void setData_src_dt(Date value) {
		this.data_src_dt = value;
	}
	

	/** 取得**/
	public String getAcct_key() {
		return this.acct_key;
	}
	/** 設定 **/
	public void setAcct_key(String value) {
		this.acct_key = value;
	}
	
	/** 設定違約機率 **/
	public void setDr(BigDecimal dr) {
		this.dr = dr;
	}
	/** 取得違約機率 **/
	public BigDecimal getDr() {
		return dr;
	}
	/** 設定違約機率(預估1年期) **/
	public void setDr_1yr(BigDecimal dr_1yr) {
		this.dr_1yr = dr_1yr;
	}
	/** 取得違約機率(預估1年期) **/
	public BigDecimal getDr_1yr() {
		return dr_1yr;
	}
	
	/** 取得卡友貸旗標 */
	public String getC_flag() {
		return c_flag;
	}
	/** 設定卡友貸旗標 */
	public void setC_flag(String c_flag) {
		this.c_flag = c_flag;
	}

	/** 取得支援評等 **/
	public Integer getSpr() {
		return this.spr;
	}
	/** 設定支援評等 **/
	public void setSpr(Integer value) {
		this.spr = value;
	}
	
	/** 取得J10信用評分 **/
	public Integer getJ10_score() {
		return this.j10_score;
	}
	/** 設定J10信用評分 **/
	public void setJ10_score(Integer value) {
		this.j10_score = value;
	}
	
	/** 取得理由代碼一 **/
	public String getJ10_reason_code1() {
		return j10_reason_code1;
	}
	/** 設定理由代碼一 **/
	public void setJ10_reason_code1(String j10_reason_code1) {
		this.j10_reason_code1 = j10_reason_code1;
	}
	/** 取得理由代碼二 **/
	public String getJ10_reason_code2() {
		return j10_reason_code2;
	}
	/** 設定理由代碼二 **/
	public void setJ10_reason_code2(String j10_reason_code2) {
		this.j10_reason_code2 = j10_reason_code2;
	}
	/** 取得理由代碼三 **/
	public String getJ10_reason_code3() {
		return j10_reason_code3;
	}
	/** 設定理由代碼三 **/
	public void setJ10_reason_code3(String j10_reason_code3) {
		this.j10_reason_code3 = j10_reason_code3;
	}
	/** 取得理由代碼四 **/
	public String getJ10_reason_code4() {
		return j10_reason_code4;
	}
	/** 設定理由代碼四 **/
	public void setJ10_reason_code4(String j10_reason_code4) {
		this.j10_reason_code4 = j10_reason_code4;
	}
	
	/** 取得外部J10評等升降等數 */
	public Integer getJr_autodg() {
		return this.jr_autodg;
	}
	/** 設定外部J10評等升降等數 */
	public void setJr_autodg(Integer value) {
		this.jr_autodg = value;
	}
	
	/** 取得斜率<p/> */
	public BigDecimal getSlope() {
		return this.slope;
	}
	/** 設定斜率<p/> **/
	public void setSlope(BigDecimal value) {
		this.slope = value;
	}
	
	/** 取得截距<p/> */
	public BigDecimal getIntercept() {
		return this.intercept;
	}
	/** 設定截距<p/> **/
	public void setIntercept(BigDecimal value) {
		this.intercept = value;
	}
	/** 取得查詢組合 **/
	public String getProdId() {
		return prodId;
	}
	/** 設定查詢組合 **/
	public void setProdId(String prodId) {
		this.prodId = prodId;
	}
	
}
