/* 
 * L918M01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 授管處停權主檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L712M01A", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L712M01A extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

//	/** 
//	 * 刪除註記<p/>
//	 * 2011/11/08 新增：文件刪除時使用(非立即性刪除)
//	 */
//	@Column(name="DELETEDTIME", columnDefinition="TIMESTAMP")
//	private Date deletedTime;

	/** 
	 * 系統代碼<p/>
	 * 企金 LMS/消金 CLS
	 */
	@Column(name="CLASSNO", length=3, columnDefinition="CHAR(3)")
	private String classNo;

	/** 
	 * 案件號碼-年度<p/>
	 * YYYY
	 */
	@Column(name="CASEYEAR", columnDefinition="DECIMAL(4,0)")
	private Integer caseYear;

	/** 案件號碼-分行 **/
	@Column(name="CASEBRID", length=3, columnDefinition="CHAR(3)")
	private String caseBrId;

	/** 
	 * 案件號碼-流水號<p/>
	 * 100/09/27調整<br/>
	 *  格式：00001
	 */
	@Column(name="CASESEQ", columnDefinition="DECIMAL(5,0)")
	private Integer caseSeq;

	/** 
	 * 案件號碼<p/>
	 * 格式與編碼規則為：<br/>
	 *  【國內DBU/OBU】：<br/>
	 *  年度(YYY)+分行簡稱+(兆)+授字第99999號，例：099蘭雅(兆)授字第00100號。<br/>
	 *  【海外分行】：<br/>
	 *  年度(YYYY)+海外分行簡稱+(兆)+授字第99999號，例：2011芝加哥(兆)授字第00001號、2011阿姆斯特丹(兆)授字第00100號。<br/>
	 *  流水號由系統於簽報書儲存時自動賦予，不可修改<br/>
	 *  海外分行之授權內與授權外企金/個(消)金案件的流水號合併編製，且已刪除之流水號不重覆使用。<br/>
	 *  (100/08/29調整)配合上傳主機(MIS與AS400)調整欄位長度且「年度」與「流水號」均需轉換為全型字並額外保留0E0F長度。<br/>
	 *  如：0E+CHAR(40)+0F<br/>
	 *  40/2*3+2=62<br/>
	 *  eg.２０１１曼谷（兆）授字第００００１號
	 */
	@Column(name="CASENO", length=62, columnDefinition="VARCHAR(62)")
	private String caseNo;

	/** 停權單位經辦 **/
	@Column(name="STOPUPDATER", length=6, columnDefinition="CHAR(6)")
	private String stopUpdater ;

	/** 停權單位經辦建立時間 **/
	@Column(name="CASEDATE", columnDefinition="TIMESTAMP")
	private Date caseDate;

	/** 停權單位覆核主管 **/
	@Column(name="STOPAPPROVER", length=6, columnDefinition="CHAR(6)")
	private String stopApprover;

	/** 停權單位覆核時間 **/
	@Column(name="STOPAPPRTIME", columnDefinition="TIMESTAMP")
	private Date stopApprTime;
	
	/** 額度序號 **/
	@Column(name="CNTRNO", columnDefinition="CHAR(12)")
	private String cntrNo;
	
	/** 最後一次異動日期 **/
	@Column(name="ALNMODIFYTIME", columnDefinition="TIMESTAMP")
	private Timestamp alnModifyTime;
	
	/** 最後一次異動單位 **/
	@Column(name="ALNMODIFYUNIT", columnDefinition="CHAR(5)")
	private String alnModifyUnit;
	
	/** 最後一次異動文號 **/
	@Column(name="ALNDOCUMENTNO", columnDefinition="VARCHAR(20)")
	private String alnDocumentNo;
	
	

//	/** 
//	 * 取得刪除註記<p/>
//	 * 2011/11/08 新增：文件刪除時使用(非立即性刪除)
//	 */
//	public Date getDeletedTime() {
//		return this.deletedTime;
//	}
//	/**
//	 *  設定刪除註記<p/>
//	 *  2011/11/08 新增：文件刪除時使用(非立即性刪除)
//	 **/
//	public void setDeletedTime(Date value) {
//		this.deletedTime = value;
//	}

	/** 
	 * 取得系統代碼<p/>
	 * 企金 LMS/消金 CLS
	 */
	public String getClassNo() {
		return this.classNo;
	}
	/**
	 *  設定系統代碼<p/>
	 *  企金 LMS/消金 CLS
	 **/
	public void setClassNo(String value) {
		this.classNo = value;
	}

	/** 
	 * 取得案件號碼-年度<p/>
	 * YYYY
	 */
	public Integer getCaseYear() {
		return this.caseYear;
	}
	/**
	 *  設定案件號碼-年度<p/>
	 *  YYYY
	 **/
	public void setCaseYear(Integer value) {
		this.caseYear = value;
	}

	/** 取得案件號碼-分行 **/
	public String getCaseBrId() {
		return this.caseBrId;
	}
	/** 設定案件號碼-分行 **/
	public void setCaseBrId(String value) {
		this.caseBrId = value;
	}

	/** 
	 * 取得案件號碼-流水號<p/>
	 * 100/09/27調整<br/>
	 *  格式：00001
	 */
	public Integer getCaseSeq() {
		return this.caseSeq;
	}
	/**
	 *  設定案件號碼-流水號<p/>
	 *  100/09/27調整<br/>
	 *  格式：00001
	 **/
	public void setCaseSeq(Integer value) {
		this.caseSeq = value;
	}

	/** 
	 * 取得案件號碼<p/>
	 * 格式與編碼規則為：<br/>
	 *  【國內DBU/OBU】：<br/>
	 *  年度(YYY)+分行簡稱+(兆)+授字第99999號，例：099蘭雅(兆)授字第00100號。<br/>
	 *  【海外分行】：<br/>
	 *  年度(YYYY)+海外分行簡稱+(兆)+授字第99999號，例：2011芝加哥(兆)授字第00001號、2011阿姆斯特丹(兆)授字第00100號。<br/>
	 *  流水號由系統於簽報書儲存時自動賦予，不可修改<br/>
	 *  海外分行之授權內與授權外企金/個(消)金案件的流水號合併編製，且已刪除之流水號不重覆使用。<br/>
	 *  (100/08/29調整)配合上傳主機(MIS與AS400)調整欄位長度且「年度」與「流水號」均需轉換為全型字並額外保留0E0F長度。<br/>
	 *  如：0E+CHAR(40)+0F<br/>
	 *  40/2*3+2=62<br/>
	 *  eg.２０１１曼谷（兆）授字第００００１號
	 */
	public String getCaseNo() {
		return this.caseNo;
	}
	/**
	 *  設定案件號碼<p/>
	 *  格式與編碼規則為：<br/>
	 *  【國內DBU/OBU】：<br/>
	 *  年度(YYY)+分行簡稱+(兆)+授字第99999號，例：099蘭雅(兆)授字第00100號。<br/>
	 *  【海外分行】：<br/>
	 *  年度(YYYY)+海外分行簡稱+(兆)+授字第99999號，例：2011芝加哥(兆)授字第00001號、2011阿姆斯特丹(兆)授字第00100號。<br/>
	 *  流水號由系統於簽報書儲存時自動賦予，不可修改<br/>
	 *  海外分行之授權內與授權外企金/個(消)金案件的流水號合併編製，且已刪除之流水號不重覆使用。<br/>
	 *  (100/08/29調整)配合上傳主機(MIS與AS400)調整欄位長度且「年度」與「流水號」均需轉換為全型字並額外保留0E0F長度。<br/>
	 *  如：0E+CHAR(40)+0F<br/>
	 *  40/2*3+2=62<br/>
	 *  eg.２０１１曼谷（兆）授字第００００１號
	 **/
	public void setCaseNo(String value) {
		this.caseNo = value;
	}

	/** 取得停權單位經辦 **/
	public String getStopUpdater () {
		return this.stopUpdater ;
	}
	/** 設定停權單位經辦 **/
	public void setStopUpdater (String value) {
		this.stopUpdater  = value;
	}

	/** 取得停權單位經辦建立時間 **/
	public Date getCaseDate() {
		return this.caseDate;
	}
	/** 設定停權單位經辦建立時間 **/
	public void setCaseDate(Date value) {
		this.caseDate = value;
	}

	/** 取得停權單位覆核主管 **/
	public String getStopApprover() {
		return this.stopApprover;
	}
	/** 設定停權單位覆核主管 **/
	public void setStopApprover(String value) {
		this.stopApprover = value;
	}

	/** 取得停權單位覆核時間 **/
	public Date getStopApprTime() {
		return this.stopApprTime;
	}
	/** 設定停權單位覆核時間 **/
	public void setStopApprTime(Date value) {
		this.stopApprTime = value;
	}
	
	/**
	 * 額度序號
	 * @param cntrNo
	 */
	public void setCntrNo(String cntrNo) {
		this.cntrNo = cntrNo;
	}
	
	/**
	 * 
	 * @return
	 */
	public String getCntrNo() {
		return cntrNo;
	}
	
	/**
	 * 最後一次異動日期
	 * @param alnModifyTime
	 */
	public void setAlnModifyTime(Timestamp alnModifyTime) {
		this.alnModifyTime = alnModifyTime;
	}
	
	/**
	 * 
	 * @return
	 */
	public Timestamp getAlnModifyTime() {
		return alnModifyTime;
	}
	
	/**
	 * 最後一次異動單位
	 * @param alnModifyUnit
	 */
	public void setAlnModifyUnit(String alnModifyUnit) {
		this.alnModifyUnit = alnModifyUnit;
	}
	
	/**
	 * 
	 * @return
	 */
	public String getAlnModifyUnit() {
		return alnModifyUnit;
	}
	
	/**
	 * 最後一次異動文號
	 * @param alnDocumentNo
	 */
	public void setAlnDocumentNo(String alnDocumentNo) {
		this.alnDocumentNo = alnDocumentNo;
	}
	
	/**
	 * 
	 * @return
	 */
	public String getAlnDocumentNo() {
		return alnDocumentNo;
	}
}
