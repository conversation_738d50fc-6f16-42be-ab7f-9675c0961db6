package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.L120M01ATMP1Dao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L120M01ATMP1;

/** 授信簽報書主檔 **/
@Repository
public class L120M01ATMP1DaoImpl extends LMSJpaDao<L120M01ATMP1, String>
		implements L120M01ATMP1Dao {

	@Override
	public List<L120M01ATMP1> findByUserId(String userId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "notesUp", userId);
		List<L120M01ATMP1> list = createQuery(L120M01ATMP1.class, search)
				.getResultList();
		return list;
	}

	public int delByUserId(String userId) {
		Query query = entityManager
				.createNamedQuery("L120M01ATMP1.deleteByUserId");

		query.setParameter("NOTESUP", userId);
		return query.executeUpdate();
	}
}