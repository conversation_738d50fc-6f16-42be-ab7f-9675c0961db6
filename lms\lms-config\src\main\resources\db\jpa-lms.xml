<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:aop="http://www.springframework.org/schema/aop" xmlns:tx="http://www.springframework.org/schema/tx"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
           http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.0.xsd
           http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd">

	<bean id="lmsEntityManagerFactory"
		class="org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean">
		<property name="persistenceUnitName" value="pu-lms" />
		<property name="dataSource" ref="lms-db" />
		<property name="persistenceXmlLocation" value="classpath:META-INF/persistence-lms.xml" />
		<!-- <property name="loadTimeWeaver">
			<bean class="org.springframework.instrument.classloading.InstrumentationLoadTimeWeaver" 
			/>
			</property> -->
		<!-- <property name="loadTimeWeaver">
			<bean class="org.springframework.instrument.classloading.ReflectiveLoadTimeWeaver" 
			/>
			</property> -->
		<property name="jpaVendorAdapter">
			<bean class="org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter">
				<property name="generateDdl" value="${lms.jpa.ddl}" />
			</bean>
		</property>
		<property name="jpaDialect">
			<bean class="org.springframework.orm.jpa.vendor.HibernateJpaDialect" />
		</property>
        <property name="jpaProperties">
            <bean class="org.springframework.beans.factory.config.PropertiesFactoryBean">
                <property name="location">
                    <value>classpath:/db/#{systemProperties['eloan.env']}hibernate.properties</value>
                </property>
            </bean>
        </property>
		<property name="jpaPropertyMap">
			<map>
                <entry key="hibernate.dialect" value="${lms.jpa.hibernate.dialect}" />
                <entry key="hibernate.default_schema" value="${lms.jpa.schema}" />
			</map>
		</property>
	</bean>

	<bean id="lmsEntityManager" class="org.springframework.orm.jpa.support.SharedEntityManagerBean">
		<property name="entityManagerFactory" ref="lmsEntityManagerFactory" />
	</bean>

	<bean id="lmsTxManger" class="org.springframework.orm.jpa.JpaTransactionManager">
		<property name="entityManagerFactory" ref="lmsEntityManagerFactory" />
	</bean>

	<tx:advice id="lmsTxAdvice" transaction-manager="lmsTxManger">
		<tx:attributes>
			<!-- all methods below are read-only -->
			<tx:method name="list*"  read-only="true" />
			<tx:method name="find*"  read-only="true" />
			<tx:method name="get*"  read-only="true" />

			<!-- other methods use the default transaction settings (see below) -->
			<tx:method name="*" timeout="45000" rollback-for="Throwable"
				propagation="REQUIRED" />
			<!-- timeout in seconds -->
		</tx:attributes>
	</tx:advice>


	<aop:config proxy-target-class="true">
		<aop:pointcut id="lmsServiceOperation"
			expression="execution(* com.mega.eloan.lms..service.*.*(..)) and !@annotation(tw.com.iisi.cap.annotation.NonTransactional) " />
		<aop:advisor advice-ref="lmsTxAdvice" pointcut-ref="lmsServiceOperation" />

	</aop:config>

</beans>