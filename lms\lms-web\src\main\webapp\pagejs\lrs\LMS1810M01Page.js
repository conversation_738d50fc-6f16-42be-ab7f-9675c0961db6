var initDfd = $.Deferred();
var _handler = "lms1810m01formhandler";
$(function(){
	var tabForm = $("#tabForm");
	var btnPanel = $("#buttonPanel");
	
	$.form.init({
		formHandler:_handler, 
		formAction:'query', 
		loadSuccess:function(json){			
			
			if(json.isCustIdSaved=="Y"){
				btnPanel.find("#btnSearchCustId").addClass(" ui-state-disabled ").prop("disabled", true);
			}else{
				var disableArr = ["btnSave", "btnSend", "btnAccept", "btnCauculate", "btnState"];
				$.each( disableArr, function(idx,btnId){
					btnPanel.find("#"+btnId).addClass(" ui-state-disabled ").prop("disabled", true);
            	});
				
				$.each(json.br_itemOrder, function(idx, brNo) {
            		var currobj = {};
            		var brName = json.br_item[brNo];
            		currobj[brNo] = brName;
            		
            		//select
            		$("#custInfoForm").find("#branch").setItems({ item: currobj, format: "{value} {key}", clear:false, space: false });
				});
			}			
			
			if(json.page=="02"){
				build_selItem(json.selItem, json.selItemOrder, ".", true).done(function(){
		
					if(json.ctlType == "B"){
						$.each($("#elfRCkdLine option"), function(){
							//J-106-0278-001 修改常董會或董事會權限授信實地覆審案件覆審週期計算
							//107年1月1之後新作「常董會（或董事會）權限」授信案件，應於撥貸後半年內辦理首次實地覆審，之後並每年覆審一次。  
				            if($(this).val()!="A"  ){
								$("#elfRCkdLine option[value='"+DOMPurify.sanitize($(this).val())+"']").prop('disabled', true);
							}
				        });
					}
					
					//J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
					if(json.ctlType == "C"){
						$.each($("#elfRCkdLine option"), function(){
							//C.價金履約保證額度覆審每年覆審一次。  
				            if($(this).val()!="A"  ){
								$("#elfRCkdLine option[value='"+DOMPurify.sanitize($(this).val())+"']").prop('disabled', true);
							}
				        });
					}
					
					if (json.ctlType != "B") {
					   $.each($("#elfNCkdFlag option"), function(){
				            if($(this).val()=="A" || $(this).val()=="B" || $(this).val()=="C" || $(this).val()=="D" || $(this).val()=="E"){
								$("#elfNCkdFlag option[value='"+DOMPurify.sanitize($(this).val())+"']").prop('disabled', true);
							}
				        });
					}else{
						//J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
						var dynaB = [];
						dynaB.push("-------以下為董事會(或常董會)權限案件實地覆審專用--------<br>");
						$("#elfNCkdFlag option[value='A']").before("<option value='?#?'>------------※以下為【覆審種類B.董事會(或常董會)權限案件實地覆審】專用-------------</option>" );
						$("#elfNCkdFlag option[value='?#?']").prop('disabled', true);
					}

					// J-110-0272 抽樣覆審，不覆審代碼13 僅供系統用，不可經由人工更改
					// 控制檔維護，如原本是13還是要可以選13，所以拿掉disabled
                    // $("#elfNCkdFlag option[value='13']").attr('disabled', true);
				});
				
				//J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
				if(json.ctlType == "B"){
					$(".showCtlTypeB").show();
					$(".showCtlTypeA").hide();
				}else if(json.ctlType == "C"){
					$(".showCtlTypeB").hide();
					$(".showCtlTypeA").hide();	
				}else{
					$(".showCtlTypeA").show();
					$(".showCtlTypeB").hide();
				}
				
			}
			
			tabForm.injectData(json);
			
			if(!$("#buttonPanel").find("#btnSave").is("button") || json.lock) { 
				tabForm.lockDoc(); 
				initControl_lockDoc = true; 
				json['initControl_lockDoc'] = initControl_lockDoc; 
			} 

			
			//---
			initDfd.resolve(json);
	}});
	
	btnPanel.find("#btnSave").click(function(){		
		saveAction().done(function(json_saveAction){
			if(json_saveAction.saveOkFlag){
				API.showMessage(i18n.def.saveSuccess);	
			}
        });
	}).end().find("#btnSend").click(function(){
		//經辦-呈主管
		saveAction().done(function(json_saveAction1){
    		if(json_saveAction1.saveOkFlag){
    			calcNdDateAction().done(function(){
    				//有 ndDate 之後,再跑一次存檔時的檢核
    				saveAction().done(function(json_saveAction2){
    		    		if(json_saveAction2.saveOkFlag){
    		    			$.ajax({
        	                    type: "POST",
        	                    handler: _handler,
        	                    data: $.extend( {formAction: "check_befSendBoss"}
        	                    	, {'mainOid':responseJSON.mainOid})
        	                    }).done(function(json_check_before_sendBoss){
        	                    	if(json_check_before_sendBoss.checkFlag){
        	                    		//===
        	                    		API.confirmMessage(i18n.def.confirmApply, function(result){
        	    	        	            if (result) {
        	    	        	            	flowAction({'decisionExpr':'呈主管'});    	    	
        	    	        	        	}
        	    	        	    	});
        	                    }
        	                });
    		    		}
    				});	
    			});
    		}
    	});		
	}).end().find("#btnAccept").click(function(){
		//主管-覆核
		var _id = "_div_btnAccept";
		var _form = _id+"_form";
		if ($("#"+_id).length == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");
			dyna.push("<form id='"+_form+"'>");
			dyna.push("		<p><label><input type='radio' name='decisionExpr' value='1' class='required' />核准</label></p>");
			dyna.push("		<p><label><input type='radio' name='decisionExpr' value='2' class='required' />退回</label></p>");
			dyna.push("</form>");
			
			dyna.push("</div>");
			
		     $('body').append(dyna.join(""));
		}
		//clear data
		$("#"+_form).reset();
		
		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
	        title: i18n.def["confirmApprove"],
	        width: 380,
            height: 180,
            align: "center",
            valign: "bottom",
            modal: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if (!$("#"+_form).valid()) {
                        return;
                    }
                    var val = $("#"+_form).find("[name='decisionExpr']:checked").val();
                    if(val=="1"){
                    	flowAction({'decisionExpr':'核定'});
                    }else if(val=="2"){
                    	flowAction({'decisionExpr':'退回'});
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
	    });
	}).end().find("#btnSearchCustId").click(function(){
		//J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		//確認新增覆審名單種類
		$.ajax({
			type: "POST",
			handler: "lms1800formhandler",
			data: { 'formAction': 'getCtlTypeByBrNo' }
		}).done(function(responseData){
			 var ctlType = responseData.ctlType;
			 $("#custInfoForm").find("#_choiceCtlType").show();
			 //J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
			 build_selItem(responseData.selItem, responseData.selItemOrder, ".", true).done(function(){

				 $("#divSearchCustId").thickbox({
						title: i18n.lms1810m01["searchTitle"],
						width: 550,
						height: 270,
						modal: false,
						align: "center",
						valign: "bottom",
						i18n: i18n.def,
						buttons: {
							"sure": function(){
								if ($("#custInfoForm").valid()) {

									if(ctlType == "Z"){
										//ctlType = $("input[name='_ctlType']:radio:checked").val();
										ctlType = $("#_ctlType").val();
									}
									var _par = {
										mainOid : responseJSON.mainOid,
										branchId: $("#custInfoForm").find("#branch").val(),
										custId: $("#custInfoForm").find("#custid").val(),
										dupNo : $("#custInfoForm").find("#dupNo").val(),
										ctlType : ctlType
									};

									valid_custId_data(_par).done(function(){
										$.ajax({
											type: "POST",
											handler: _handler,
											data: $.extend({ formAction: "doCustIdSaved" }, _par)
										}).done(function(json_doCustIdSaved){
											CommonAPI.triggerOpener("gridview", "reloadGrid");
											API.showMessage(i18n.def.runSuccess);

											$.form.submit({ url: responseJSON.page , data: json_doCustIdSaved });
										});
										$.thickbox.close();	
									});
								}
							},
							"cancel": function(){
								$.thickbox.close();
							}
						}
					});
			 });
		});

		 
	}).end().find("#btnCauculate").click(function(){
		special_saveAction().done(function(json){
   			calcNdDateAction(json);
    	});
	})		.end().find("#btnState").click(function(){
			//J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
			special_saveAction().done(function(json){
				$.ajax({
					type: "POST",
					handler: _handler,
					data: $.extend({ formAction: "query_LNF022" }, {
						'mainOid': responseJSON.mainOid,
						'runSave': json.runSave,
						'ctlType': $('#ctlType').val()
					})
				}).done(function(json_query_LNF022){
					//更新UI上的欄位
					$("#cStateDesc").val(json_query_LNF022.cStateDesc);
					$("#elfCancelDt").val(json_query_LNF022.elfCancelDt);
					if (json.runSave == 'N') {
						if (json_query_LNF022.isCancel == "N") {
							API.showMessage("中心帳務檔未銷戶，戶況【" + json_query_LNF022.cStateDesc + "】");
						} else if (json_query_LNF022.isCancel == "Y") {
							API.showMessage("中心帳務檔已銷戶");
						}
					} else {
						if (json_query_LNF022.isCancel == "N") {
							API.showMessage("中心帳務檔未銷戶，戶況【" + json_query_LNF022.cStateDesc + "】");
						} else if (json_query_LNF022.isCancel == "Y") {
							CommonAPI.confirmMessage("中心帳務檔已銷戶，是否要將本文件之異動欄位更新為銷戶預設值?",
								function(b){
									if (b) {
										$.ajax({
											type: "POST",
											handler: _handler,
											data: $.extend({ formAction: "afItemWithCancelVal" }, {
												'mainOid': responseJSON.mainOid
											})
										}).done(function(json_afItemWithCancelVal){
											var page = responseJSON.page;
											var tData = {
												'mainDocStatus': $("#mainDocStatus").val(),
												'mainId': $("#mainId").val(),
												'mainOid': $("#mainOid").val()
											};
											if (page == "02") {
												$.form.submit({ url: page, data: tData });
											}
										});
									}
								}
							);
						}
					}
				});
			});
		});

	
	
	function valid_custId_data(param){
		var my_dfd = $.Deferred();
		
		 $.ajax({
             type: "POST",
             handler: _handler,
             data: $.extend( {formAction: "queryELF412"}, param)
             }).done(function(json_queryELF412){
             	if(json_queryELF412.elf412Flag=="Y"){
             		my_dfd.resolve();
             	}else if(json_queryELF412.elf412Flag=="N"){
             		CommonAPI.confirmMessage(i18n.lms1810m01["msg.no_ELF412"], function(b){
                        if (b) {
                        	my_dfd.resolve();    	
                        }else{
                        	my_dfd.reject();    	
                        }
                    });             		
             	}else{
             		my_dfd.reject();
             	}             	
         });
		
		return my_dfd.promise();
	}
	
	var special_saveAction = function(){
		var my_dfd = $.Deferred();
		//在待覆核，試算下次覆審日，不應寫入 updater
		if($("#buttonPanel").find("#btnSave").is("button")) {
			saveAction().done(function(json_saveAction){
	    		if(json_saveAction.saveOkFlag){
	    			my_dfd.resolve({'runSave':'Y'});
	    		}else{
	    			my_dfd.reject();
	    		}
	    	});	
		}else{
			my_dfd.resolve({'runSave':'N'});
		}		
		return my_dfd.promise();	
	}
	
	var saveAction = function(opts){
		
		if($("#elfNextNwDt").is(":visible") ){
			
		}else{
			$("#elfNextNwDt").val("");
		}
		
		if(tabForm.valid()){
			return $.ajax({
                type: "POST",
                handler: _handler,
                data:$.extend( {
                	formAction: "saveMain",
                    page: responseJSON.page,
                    mainOid: responseJSON.mainOid
                    }, 
                    tabForm.serializeData(),
                    ( opts||{} )
                )                
                }).done(function(json){
                	tabForm.injectData(json);
                	//更新 opener 的 Grid
                    CommonAPI.triggerOpener("gridview", "reloadGrid");
            });
		}else{
			return $.Deferred();
		}
	}
	
	var calcNdDateAction = function(opts){
		var my_dfd = $.Deferred();
		$.ajax({
            type: "POST",
            handler: _handler,
            data: $.extend( {formAction: "fnGetNewChkDate"}, {'mainOid':responseJSON.mainOid}, opts||{})
            }).done(function(json_fnGetNewChkDate){
            	if(json_fnGetNewChkDate.calcFlag){
            		$("#ndDate").val(json_fnGetNewChkDate.ndDate);
            		//---
            		if(json_fnGetNewChkDate.msg!=""){
            			API.showPopMessage("", json_fnGetNewChkDate.msg,function(){
                			my_dfd.resolve();
          				});
            		}else{
            			my_dfd.resolve(); 
            		}
            	}else{
            		my_dfd.reject();
            	}
        });
		return my_dfd.promise();
	}

	var flowAction = function(opts){
		return $.ajax({
            type: "POST",
            handler: _handler, action: "flowAction",
            data:$.extend( {
            	mainOid: $("#mainOid").val(), 
            	mainDocStatus: $("#mainDocStatus").val() 
                }
                , ( opts||{} )
            )                
            }).done(function(json){
            	API.triggerOpener(); window.close();
        });
	}
	
});

function build_selItem(json_selItem, json_selItemOrder, sep, doCopy){

    //J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
	var my_dfd = $.Deferred();
	$.each(json_selItem, function(itemName, kvMap) {
		var chooseItem = $("#"+itemName);
		var _addSpace = false;
		if(chooseItem.attr("space")=="true"){
			_addSpace = true;
		}
		
		var _fmt = "{key}";
		if(chooseItem.attr("myShowKey")==="Y"){
			_fmt = "{value}"+sep+"{key}";
		}
		$.each(json_selItemOrder[itemName], function(idx, kVal) {
			var currobj = {};
    		currobj[kVal] = kvMap[kVal];
    		
    		chooseItem.setItems({ item: currobj, format: _fmt, clear:false, space: (_addSpace?(idx==0):false) });	    		
		});			
//		$("#"+itemName).setOptions(kvMap, false);
		//---
		//copy html element
		if(doCopy){
			$("#"+itemName+"_1").html( chooseItem.html() );	
		}

	});
	
	//J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
	my_dfd.resolve();
	return my_dfd.promise();
	
		
}

$.extend(window.tempSave, {
    handler: _handler,
    action: "tempSave",
    beforeCheck : function(){
    	if(responseJSON.page == "01"){
    		if($("#custId").val() == "" || $("#dupNo").val() == ""){
    			CommonAPI.showMessage(i18n.lms1810m01["enterCustId"]);
    			return false;
    		}
    	}
    	return true;
    },
    sendData: function(){
    	return $("#tabForm").serializeData();
    }
});

