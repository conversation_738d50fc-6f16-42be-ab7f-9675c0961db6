package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/**
 * 授信業務查核事項檔  
 */
@Entity
@Table(uniqueConstraints = @UniqueConstraint(columnNames = { "mainId" }))
@EntityListeners({ DocumentModifyListener.class })
public class L192M01C extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 6635194902289434386L;

	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(unique = true, nullable = false, length = 32, columnDefinition = "CHAR(32)")
	private String oid;

	@Column(length = 32, nullable = false, columnDefinition = "CHAR(32)")
	private String mainId;
	
	/**有無對保或驗印*/
	@Column(length = 1, columnDefinition = "VARCHAR(1)")
	private String ck1;

	/**核貸條件是否履行*/
	@Column(length = 1, columnDefinition = "VARCHAR(1)")
	private String ck2;

	/**應收客票是否正常*/
	@Column(length = 1, columnDefinition = "VARCHAR(1)")
	private String ck3;

	/**徵信作業-資料更新*/
	@Temporal(TemporalType.DATE)
	private Date ck4Date;

	/**徵信作業-所得資料(企業戶)*/
	@Temporal(TemporalType.DATE)
	private Date ck5Date;

	/**徵信作業-所得資料(個人戶)*/
	@Column(columnDefinition = "DECIMAL(13,0)")
	private BigDecimal ck5Amt;

	/**徵信作業-融資簽證*/
	@Temporal(TemporalType.DATE)
	private Date ck6Date;

	/**徵信作業-退票查詢*/
	@Temporal(TemporalType.DATE)
	private Date ck7Date;

	/**關係人交易查詢*/
	@Temporal(TemporalType.DATE)
	private Date ck8Date;

	/**董事會決議錄(非法人得免)*/
	@Column(length = 1, columnDefinition = "VARCHAR(1)")
	private String ck9;

	/**股東債權居次同意書*/
	@Column(length = 1, columnDefinition = "VARCHAR(1)")
	private String ck10;
	
	/**個人及公司授信資料查詢*/
	@Temporal(TemporalType.DATE)
	private Date ck11Date;

	/**同意書*/
	@Column(length = 1, columnDefinition = "VARCHAR(1)")
	private String ck12;

	/**自訂欄位1*/
	@Column(length = 60, columnDefinition = "VARCHAR(60)")
	private String userItem1;
	
	/**自訂欄位查核1*/
	@Column(length = 1, columnDefinition = "VARCHAR(1)")
	private String userCk1;

	/**建立人員號碼*/
	@Column(length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/**建立日期*/
	@Temporal(TemporalType.TIMESTAMP)
	private Date createTime;

	/**異動人員號碼*/
	@Column(length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/**異動日期*/
	@Temporal(TemporalType.TIMESTAMP)
	private Date updateTime;

	public String getOid() {
		return oid;
	}

	public void setOid(String oid) {
		this.oid = oid;
	}

	public String getMainId() {
		return mainId;
	}

	public void setMainId(String mainId) {
		this.mainId = mainId;
	}

	/** 有無對保或驗印 */
	public String getCk1() {
		return ck1;
	}

	/** 有無對保或驗印 */
	public void setCk1(String ck1) {
		this.ck1 = ck1;
	}

	/** 核貸條件是否履行 */
	public String getCk2() {
		return ck2;
	}

	/** 核貸條件是否履行 */
	public void setCk2(String ck2) {
		this.ck2 = ck2;
	}

	/** 應收客票是否正常 */
	public String getCk3() {
		return ck3;
	}

	/** 應收客票是否正常 */
	public void setCk3(String ck3) {
		this.ck3 = ck3;
	}

	/** 徵信作業-資料更新 */
	public Date getCk4Date() {
		return ck4Date;
	}

	/** 徵信作業-資料更新 */
	public void setCk4Date(Date ck4Date) {
		this.ck4Date = ck4Date;
	}

	/** 徵信作業-所得資料(企業戶) */
	public Date getCk5Date() {
		return ck5Date;
	}

	/** 徵信作業-所得資料(企業戶) */
	public void setCk5Date(Date ck5Date) {
		this.ck5Date = ck5Date;
	}

	/** 徵信作業-所得資料(個人戶) */
	public BigDecimal getCk5Amt() {
		return ck5Amt;
	}

	/** 徵信作業-所得資料(個人戶) */
	public void setCk5Amt(BigDecimal ck5Amt) {
		this.ck5Amt = ck5Amt;
	}

	/** 徵信作業-融資簽證 */
	public Date getCk6Date() {
		return ck6Date;
	}

	/** 徵信作業-融資簽證 */
	public void setCk6Date(Date ck6Date) {
		this.ck6Date = ck6Date;
	}

	/** 徵信作業-退票查詢 */
	public Date getCk7Date() {
		return ck7Date;
	}

	/** 徵信作業-退票查詢 */
	public void setCk7Date(Date ck7Date) {
		this.ck7Date = ck7Date;
	}

	/** 關係人交易查詢 */
	public Date getCk8Date() {
		return ck8Date;
	}

	/** 關係人交易查詢 */
	public void setCk8Date(Date ck8Date) {
		this.ck8Date = ck8Date;
	}

	/** 董事會決議錄(非法人得免) */
	public String getCk9() {
		return ck9;
	}

	/** 董事會決議錄(非法人得免) */
	public void setCk9(String ck9) {
		this.ck9 = ck9;
	}

	/** 股東債權居次同意書 */
	public String getCk10() {
		return ck10;
	}

	/** 股東債權居次同意書 */
	public void setCk10(String ck10) {
		this.ck10 = ck10;
	}

	/** 個人及公司授信資料查詢 */
	public Date getCk11Date() {
		return ck11Date;
	}

	/** 個人及公司授信資料查詢 */
	public void setCk11Date(Date ck11Date) {
		this.ck11Date = ck11Date;
	}

	/** 同意書 */
	public String getCk12() {
		return ck12;
	}

	/** 同意書 */
	public void setCk12(String ck12) {
		this.ck12 = ck12;
	}

	/** 自訂欄位1 */
	public String getUserItem1() {
		return userItem1;
	}

	/** 自訂欄位1 */
	public void setUserItem1(String userItem1) {
		this.userItem1 = userItem1;
	}

	/** 自訂欄位查核1 */
	public String getUserCk1() {
		return userCk1;
	}

	/** 自訂欄位查核1 */
	public void setUserCk1(String userCk1) {
		this.userCk1 = userCk1;
	}

	/** 建立人員號碼 */
	public String getCreator() {
		return creator;
	}

	/** 建立人員號碼 */
	public void setCreator(String creator) {
		this.creator = creator;
	}

	/** 建立日期 */
	public Date getCreateTime() {
		return createTime;
	}

	/** 建立日期 */
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	/** 異動人員號碼 */
	public String getUpdater() {
		return updater;
	}

	/** 異動人員號碼 */
	public void setUpdater(String updater) {
		this.updater = updater;
	}

	/** 異動日期 */
	public Date getUpdateTime() {
		return updateTime;
	}

	/** 異動日期 */
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

}
