$(document).ready(function(){
	$("#btn_calc").click(function(){
		Cal_result();
	});
	$("#btn_clear").click(function(){
		Clear();
	});
});

function setFocus(){
	$("form#CalForm").find("#Money").focus();
}

function Clear(){
	$("form#CalForm").reset();
}

function Set_value(){
	var $frm = $("form#CalForm");
	if (! $frm.valid()) {
        return;
    }
	
    if ($frm.find("#Month").val().length == 0) {
        alert("請輸入貸款期間！");
        return false;
    }
    else {
    	//J-112-0586_05097_B1002 依據簽會-2023-2192「Web eLoan-Checkmarx弱點改善會議」按季追蹤弱點修正進度
        var month = DOMPurify.sanitize($frm.find("#Month").val());
        inputStr = month;
        for (var k = 0; k < inputStr.length; k++) {
            var oneChar = inputStr.substring(k, k + 1)
            if (!parseFloat(oneChar)) {
                if (oneChar != "0") {
                    alert("貸款期間資料須為正整數的數值資料！");
                    return false;
                }
            }
        }
        if (month == 0) {
            alert("貸款期間不得為零！");
            return false;
        }
    }
    
    if (month <= 36) {
        if (month == 12) {
        	$frm.find("#Grace").val("0");
        	$frm.find("#Month_1").val("6");
        	$frm.find("#Month_2").val("6");
        	$frm.find("#Month_3").val("0");
        	$frm.find("#Month_4").val("0");
        	$frm.find("#Month_5").val("0");
        	$frm.find("#Rate_3").val("0");
        	$frm.find("#Rate_4").val("0");
        	$frm.find("#Rate_5").val("0");
        }
        if (month == 24) {
        	$frm.find("#Grace").val("0");
        	$frm.find("#Month_1").val("6");
        	$frm.find("#Month_2").val("6");
        	$frm.find("#Month_3").val("12");
        	$frm.find("#Month_4").val("0");
        	$frm.find("#Month_5").val("0");        	
        	$frm.find("#Rate_4").val("0");
        	$frm.find("#Rate_5").val("0");
        }
        if (month == 36) {
        	$frm.find("#Grace").val("0");
        	$frm.find("#Month_1").val("6");
        	$frm.find("#Month_2").val("6");
        	$frm.find("#Month_3").val("12");
        	$frm.find("#Month_4").val("12");
        	$frm.find("#Month_5").val("0");
        	$frm.find("#Rate_5").val("0");
        }
    }
    else {
    	$frm.find("#Month_1").val("6");
    	$frm.find("#Month_2").val("6");
    	$frm.find("#Month_3").val("12");
    	$frm.find("#Month_4").val("12");
    	$frm.find("#Month_5").val( parseInt(month - 36) );
    }
}

function Cal_result(){
	var $frm = $("form#CalForm");
	if (! $frm.valid()) {
        return;
    }

    if ($frm.find("#Money").val().length == 0) {
        alert("請輸入貸款總金額！");
        return false;
    }
    else {
        var money = $frm.find("#Money").val();
        inputStr = money;
        for (var k = 0; k < inputStr.length; k++) {
            var oneChar = inputStr.substring(k, k + 1)
            if (!parseFloat(oneChar)) {
                if (oneChar != "0") {
                    alert("貸款總金額資料須為正整數的數值資料！");
                    return false;
                }
            }
        }
    }
    
    if ($frm.find("#Month").val().length == 0) {
        alert("請輸入貸款期間！");
        return false;
    }
    else {
    	//J-112-0586_05097_B1002 依據簽會-2023-2192「Web eLoan-Checkmarx弱點改善會議」按季追蹤弱點修正進度
        var month = DOMPurify.sanitize($frm.find("#Month").val());
        inputStr = month;
        for (var k = 0; k < inputStr.length; k++) {
            var oneChar = inputStr.substring(k, k + 1)
            if (!parseFloat(oneChar)) {
                if (oneChar != "0") {
                    alert("貸款期間資料須為正整數的數值資料！");
                    return false;
                }
            }
        }
    }
    
    if ($frm.find("#Grace").val().length == 0) {
    	$frm.find("#Grace").val("0");
        var grace = 0;
    }
    else {
    	//J-112-0586_05097_B1002 依據簽會-2023-2192「Web eLoan-Checkmarx弱點改善會議」按季追蹤弱點修正進度
        var grace = DOMPurify.sanitize($frm.find("#Grace").val());
        inputStr = grace;
        for (var k = 0; k < inputStr.length; k++) {
            var oneChar = inputStr.substring(k, k + 1)
            if (!parseFloat(oneChar)) {
                if (oneChar != "0") {
                    alert("寬限期資料須為正整數的數值資料！");
                    return false;
                }
            }
        }
        if (grace > 36) {
            alert("寬限期範圍為0～36個月");
            return false;
        }
    }
    
    if ($frm.find("#Rate_1").val().length == 0) {
        alert("請輸入第一段利率！");
        return false;
    }
    else {
        var rate_1 = $frm.find("#Rate_1").val();
        inputStr = rate_1;
        for (var k = 0; k < inputStr.length; k++) {
            var oneChar = inputStr.substring(k, k + 1)
            if (!parseFloat(oneChar)) {
                if ((oneChar != "0") && (oneChar != ".")) {
                    alert("第一段利率資料須為數值資料！");
                    return false;
                }
            }
        }
    }
    
    if ($frm.find("#Month_1").val().length == 0) {
        alert("請輸入第一段期數！");
        return false;
    }
    else {
    	//J-112-0586_05097_B1002 依據簽會-2023-2192「Web eLoan-Checkmarx弱點改善會議」按季追蹤弱點修正進度
        var month_1 = DOMPurify.sanitize($frm.find("#Month_1").val());
        inputStr = month_1;
        for (var k = 0; k < inputStr.length; k++) {
            var oneChar = inputStr.substring(k, k + 1)
            if (!parseFloat(oneChar)) {
                if (oneChar != "0") {
                    alert("第一段期數資料須為正整數的數值資料！");
                    return false;
                }
            }
        }
    }
    
    if ($frm.find("#Rate_2").val().length == 0) {
        alert("請輸入第二段利率！");
        return false;
    }
    else {
        var rate_2 = $frm.find("#Rate_2").val();
        inputStr = rate_2;
        for (var k = 0; k < inputStr.length; k++) {
            var oneChar = inputStr.substring(k, k + 1)
            if (!parseFloat(oneChar)) {
                if ((oneChar != "0") && (oneChar != ".")) {
                    alert("第二段利率資料須為數值資料！");
                    return false;
                }
            }
        }
    }
    
    if ($frm.find("#Month_2").val().length == 0) {
        alert("請輸入第二段期數！");
        return false;
    }
    else {
    	//J-112-0586_05097_B1002 依據簽會-2023-2192「Web eLoan-Checkmarx弱點改善會議」按季追蹤弱點修正進度
        var month_2 = DOMPurify.sanitize($frm.find("#Month_2").val());
        inputStr = month_2;
        for (var k = 0; k < inputStr.length; k++) {
            var oneChar = inputStr.substring(k, k + 1)
            if (!parseFloat(oneChar)) {
                if (oneChar != "0") {
                    alert("第二段期數資料須為正整數的數值資料！");
                    return false;
                }
            }
        }
    }
    
    if ($frm.find("#Rate_3").val().length == 0) {
        alert("請輸入第三段利率！");
        return false;
    }
    else {
        var rate_3 = $frm.find("#Rate_3").val();
        inputStr = rate_3;
        for (var k = 0; k < inputStr.length; k++) {
            var oneChar = inputStr.substring(k, k + 1)
            if (!parseFloat(oneChar)) {
                if ((oneChar != "0") && (oneChar != ".")) {
                    alert("第三段利率資料須為數值資料！");
                    return false;
                }
            }
        }
    }
    
    if ($frm.find("#Month_3").val().length == 0) {
        alert("請輸入第三段期數！");
        return false;
    }
    else {
    	//J-112-0586_05097_B1002 依據簽會-2023-2192「Web eLoan-Checkmarx弱點改善會議」按季追蹤弱點修正進度
        var month_3 = DOMPurify.sanitize($frm.find("#Month_3").val());
        inputStr = month_3;
        for (var k = 0; k < inputStr.length; k++) {
            var oneChar = inputStr.substring(k, k + 1)
            if (!parseFloat(oneChar)) {
                if (oneChar != "0") {
                    alert("第三段期數資料須為正整數的數值資料！");
                    return false;
                }
            }
        }
    }
    
    if ($frm.find("#Rate_4").val().length == 0) {
        alert("請輸入第四段利率！");
        return false;
    }
    else {
        var rate_4 = $frm.find("#Rate_4").val();
        inputStr = rate_4;
        for (var k = 0; k < inputStr.length; k++) {
            var oneChar = inputStr.substring(k, k + 1)
            if (!parseFloat(oneChar)) {
                if ((oneChar != "0") && (oneChar != ".")) {
                    alert("第四段利率資料須為數值資料！");
                    return false;
                }
            }
        }
    }
    
    if ($frm.find("#Month_4").val().length == 0) {
        alert("請輸入第四段期數！");
        return false;
    }
    else {
    	//J-112-0586_05097_B1002 依據簽會-2023-2192「Web eLoan-Checkmarx弱點改善會議」按季追蹤弱點修正進度
        var month_4 = DOMPurify.sanitize($frm.find("#Month_4").val());
        inputStr = month_4;
        for (var k = 0; k < inputStr.length; k++) {
            var oneChar = inputStr.substring(k, k + 1)
            if (!parseFloat(oneChar)) {
                if (oneChar != "0") {
                    alert("第四段期數資料須為正整數的數值資料！");
                    return false;
                }
            }
        }
    }
    
    if ($frm.find("#Rate_5").val().length == 0) {
        alert("請輸入第五段以上利率！");
        return false;
    }
    else {
        var rate_5 = $frm.find("#Rate_5").val();
        inputStr = rate_5;
        for (var k = 0; k < inputStr.length; k++) {
            var oneChar = inputStr.substring(k, k + 1)
            if (!parseFloat(oneChar)) {
                if ((oneChar != "0") && (oneChar != ".")) {
                    alert("第五段以上利率資料須為數值資料！");
                    return false;
                }
            }
        }
    }
    
    if ($frm.find("#Month_5").val().length == 0) {
        alert("請輸入第五段期數！");
        return false;
    }
    else {
    	//J-112-0586_05097_B1002 依據簽會-2023-2192「Web eLoan-Checkmarx弱點改善會議」按季追蹤弱點修正進度
        var month_5 = DOMPurify.sanitize($frm.find("#Month_5").val());
        inputStr = month_5;
        for (var k = 0; k < inputStr.length; k++) {
            var oneChar = inputStr.substring(k, k + 1)
            if (!parseFloat(oneChar)) {
                if (oneChar != "0") {
                    alert("第五段期數資料須為正整數的數值資料！");
                    return false;
                }
            }
        }
    }
    
    if ($frm.find("#Mmoney").val().length == 0) {
    	$frm.find("#Mmoney").val("0");    	
        var mmoney = 0;
    }
    else {
        var mmoney = $frm.find("#Mmoney").val();
        inputStr = mmoney;
        for (var k = 0; k < inputStr.length; k++) {
            var oneChar = inputStr.substring(k, k + 1)
            if (!parseFloat(oneChar)) {
                if (oneChar != "0") {
                    mmoney = 0;
                    $frm.find("#Mmoney").val("0");
                }
            }
        }
    }
    
    if ($frm.find("#Tmoney").val().length == 0) {
    	$frm.find("#Tmoney").val("0");
        var tmoney = 0;
    }
    else {
        var tmoney = $frm.find("#Tmoney").val();
        inputStr = tmoney;
        for (var k = 0; k < inputStr.length; k++) {
            var oneChar = inputStr.substring(k, k + 1)
            if (!parseFloat(oneChar)) {
                if (oneChar != "0") {
                    tmoney = 0;
                    $frm.find("#Tmoney").val("0");
                }
            }
        }
    }
    
    if ($frm.find("#Trate").val().length == 0) {
    	$frm.find("#Trate").val("0");
        var trate = 0;
    }
    else {
        var trate = $frm.find("#Trate").val();
        inputStr = trate;
        for (var k = 0; k < inputStr.length; k++) {
            var oneChar = inputStr.substring(k, k + 1)
            if (!parseFloat(oneChar)) {
                if ((oneChar != "0") && (oneChar != ".")) {
                    trate = 0;
                    $frm.find("#Trate").val("0");
                }
            }
        }
    }
    
    var pmt = 0, pmt_1 = 0, pmt_2 = 0, pmt_3 = 0, pmt_4 = 0, pmt_5 = 0;
    var money_1 = 0, money_2 = 0, money_3 = 0, money_4 = 0, money_5 = 0;
    var month_temp;
    var result = 20;
    var total = Math.round(money - mmoney - tmoney - (money * trate / 100));
    var flag = -1;
    var sub_total;
    var i;
    var j;
    var rate_tmp;
    var sub;
    var result_true;
    
    month_temp = parseInt(month_1) + parseInt(month_2) + parseInt(month_3) + parseInt(month_4) + parseInt(month_5);
    
    if (month != month_temp) {
        alert("貸款期間資料與各段期數加總不符！");
        return false;
    }
    
    if (parseInt(grace) > parseInt(month)) {
        alert("寬限期須小於貸款期數！");
        return false;
    }
    
    var submonth_1 = parseInt(month_1);
    var submonth_2 = parseInt(month_1) + parseInt(month_2);
    var submonth_3 = parseInt(month_1) + parseInt(month_2) + parseInt(month_3);
    var submonth_4 = parseInt(month_1) + parseInt(month_2) + parseInt(month_3) + parseInt(month_4);
    
    if ((grace != 0) && (grace != submonth_1) && (grace != submonth_2) && (grace != submonth_3) && (grace != submonth_4)) {
        alert("寬限期資料有誤！");
        return false;
    }
    
    if (grace >= submonth_1) {
        var money_1 = money;
        pmt_1 = Math.round(money_1 * (rate_1 / 1200));
    }
    else {
        var money_1 = money;
        pmt_1 = Math.round(money_1 * (rate_1 / 1200) / (1 - (1 / Math.pow(1 + rate_1 / 1200, month))));
        var interest = 0;
        for (var k = 1; k <= month_1; k++) {
            var interest_temp = Math.round(money_1 * (rate_1 / 1200));
            interest = interest + interest_temp;
            money_1 = money_1 - (pmt_1 - interest_temp);
        }
    }
    
    if (grace >= submonth_2) {
        var money_2 = money;
        pmt_2 = Math.round(money_2 * (rate_2 / 1200));
    }
    else {
        var money_2 = money_1;
        pmt_2 = Math.round(money_2 * (rate_2 / 1200) / (1 - (1 / Math.pow(1 + rate_2 / 1200, month - month_1))));
        var interest = 0;
        for (var k = 1; k <= month_2; k++) {
            var interest_temp = 0;
            interest_temp = Math.round(money_2 * (rate_2 / 1200));
            interest = interest + interest_temp;
            money_2 = money_2 - (pmt_2 - interest_temp);
        }
    }
    
    if (grace >= submonth_3) {
        var money_3 = money;
        pmt_3 = Math.round(money_3 * (rate_3 / 1200));
    }
    else {
        var money_3 = money_2;
        pmt_3 = Math.round(money_3 * (rate_3 / 1200) / (1 - (1 / Math.pow(1 + rate_3 / 1200, month - month_1 - month_2))));
        var interest = 0;
        for (var k = 1; k <= month_3; k++) {
            var interest_temp = 0;
            interest_temp = Math.round(money_3 * (rate_3 / 1200));
            interest = interest + interest_temp;
            money_3 = money_3 - (pmt_3 - interest_temp);
        }
    }
    
    if (grace >= submonth_4) {
        var money_4 = money;
        pmt_4 = Math.round(money_4 * (rate_4 / 1200));
    }
    else {
        var money_4 = money_3;
        pmt_4 = Math.round(money_4 * (rate_4 / 1200) / (1 - (1 / Math.pow(1 + rate_4 / 1200, month - month_1 - month_2 - month_3))));
        var interest = 0;
        for (var k = 1; k <= month_4; k++) {
            var interest_temp = 0;
            interest_temp = Math.round(money_4 * (rate_4 / 1200));
            interest = interest + interest_temp;
            money_4 = money_4 - (pmt_4 - interest_temp);
        }
    }
    
    
    var money_5 = money_4;
    pmt_5 = Math.round(money_5 * (rate_5 / 1200) / (1 - (1 / Math.pow(1 + rate_5 / 1200, month - month_1 - month_2 - month_3 - month_4))));
    var interest = 0;
    for (var k = 1; k <= month_5; k++) {
        var interest_temp = 0;
        interest_temp = Math.round(money_5 * (rate_5 / 1200));
        interest = interest + interest_temp;
        money_5 = money_5 - (pmt_5 - interest_temp);
    }
    
    while (flag <= 0) {
        sub_total = 0;
        i = 1;
        result_true = result;
        for (j = 1; j <= month; j++) {
            rate_tmp = Math.pow(1 + result / 1200, -i);
            
            if (j <= submonth_1) {
                pmt = pmt_1;
            }
            else {
                if (j <= submonth_2) {
                    pmt = pmt_2;
                }
                else {
                    if (j <= submonth_3) {
                        pmt = pmt_3;
                    }
                    else {
                        if (j <= submonth_4) {
                            pmt = pmt_4;
                        }
                        else {
                            pmt = pmt_5;
                        }
                    }
                }
            }
            sub = pmt * rate_tmp;
            sub_total = sub_total + sub;
            i++;
        }
        flag = sub_total - total;
        result = result - 0.001;
    }
    result = result + 0.001;
    result_true_string = result_true.toString();
    var pos = result_true_string.indexOf(".");
    $frm.find("#Result").val( result_true_string.substring(0, pos + 4) );
    
    if (pmt_1.toString() == "NaN") {
    	$frm.find("#Pmt_1").val("0");
    }
    else {
    	$frm.find("#Pmt_1").val(pmt_1);
    }
    
    if (pmt_2.toString() == "NaN") {
    	$frm.find("#Pmt_2").val("0");
    }
    else {
    	$frm.find("#Pmt_2").val(pmt_2);
    }
    
    if (pmt_3.toString() == "NaN") {
    	$frm.find("#Pmt_3").val("0");
    }
    else {
    	$frm.find("#Pmt_3").val(pmt_3);
    }
    
    if (pmt_4.toString() == "NaN") {
    	$frm.find("#Pmt_4").val("0");
    }
    else {
    	$frm.find("#Pmt_4").val(pmt_4);
    }
    
    if (pmt_5.toString() == "NaN") {
    	$frm.find("#Pmt_5").val("0");
    }
    else {
    	$frm.find("#Pmt_5").val(pmt_5);
    }
}
 