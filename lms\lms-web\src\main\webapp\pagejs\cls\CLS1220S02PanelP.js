initDfd.done(function(json){
	var incomType = json.incomType;
	var docStatus = responseJSON.docStatus;
	if(incomType == "2" || docStatus == "03O"){
		$("input[type=text]").readOnly();
		$("input[type=radio]").readOnly();
		$("select").readOnly();
		$("button[type=button]").hide();
	}
	
	$("#btfAddr").click(function(){
        AddrAction.open({
            formId: 'CLS1220S02Form',
            signify: 'f'
        });
    });
	
	$("#bcoAddr").click(function(){
        AddrAction.open({
            formId: 'CLS1220S02Form',
            signify: 'co'
        });
    });
	
	$("#btSameAddr").click(function(){
        $('#coCity').val($('#fCity').val());
        $('#coZip').val($('#fZip').val());
        $('#coAddr').val($('#fAddr').val());
        $('#coTarget').val($('#fTarget').val());
    });
	
	/**
	 * 地址
	 */
	var AddrAction = {
	    formId: '',
	    signify: '',
	    open: function(options){
	        AddrAction.formId = options.formId;
	        AddrAction.signify = options.signify;
	        
	        var $form = $('#' + AddrAction.formId);
	        var cityCode = $('#' + AddrAction.signify + 'City').val();
	        var combos = CommonAPI.loadCombos(['counties', 'counties' + cityCode]);
	        
	        var $addrForm = $('#AddrForm');
	        $addrForm.setValue(); // $addrForm reset
	        // 縣市
	        $addrForm.find('#AddrCity').setItems({
	            item: combos['counties'],
	            format: '{key}',
	            value: cityCode,
	            fn: function(){
	                var $addrForm = $('#AddrForm');
	                var cityCode = $addrForm.find('#AddrCity').val();
	                var combos = CommonAPI.loadCombos('counties' + cityCode);
	                $addrForm.find('#AddrZip').setItems({
	                    item: combos['counties' + cityCode],
	                    format: '{key}'
	                });
	            }
	        });
	        // 鄉鎮市區
	        $addrForm.find('#AddrZip').setItems({
	            item: combos['counties' + cityCode],
	            format: '{key}',
	            value: $('#' + AddrAction.signify + 'Zip').val()
	        });
	        // 地址
	        $addrForm.find('#AddrAddr').val($('#' + AddrAction.signify + 'Addr').val() || '');
	        
	        $('#AddrThickBox').thickbox({
	            title: '', // i18n.cms1400v01["title"],
	            width: 500,
	            height: 200,
	            align: 'center',
	            valign: 'bottom',
	            buttons: {
	                'sure': function(){
	                    var $addrForm = $('#AddrForm');
	                    if ($addrForm.valid()) {
	                        var $form = $('#' + AddrAction.formId);
	                        $('#' + AddrAction.signify + 'City').val($addrForm.find('#AddrCity').val());
	                        $('#' + AddrAction.signify + 'Zip').val($addrForm.find('#AddrZip').val());
	                        $('#' + AddrAction.signify + 'Addr').val($addrForm.find('#AddrAddr').val());
	                        $('#' + AddrAction.signify + 'Target').val($addrForm.find('#AddrCity :selected').text() +
	    	                        $addrForm.find('#AddrZip :selected').text() +
	    	                        $addrForm.find('#AddrAddr').val());
	                        $.thickbox.close();
	                    }
	                },
	                'close': function(){
	                    $.thickbox.close();
	                }
	            }
	        });
	    }
	};
	
	
});
