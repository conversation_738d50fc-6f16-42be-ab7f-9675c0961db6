/* 
 * MisEJF327Service.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.ejcic.service;

import java.util.Map;


/**
 * <pre>
 * MIS.PROFILE>>MIS.EJV32701>>MIS.EJF327代號對照表
 * </pre>
 * 
 * @since 2012/03/23
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/03/23,TimChiang,new
 *          </ul>
 */
public interface MisEJF327Service {

	/**
	 * 查詢信用卡停用種類中文名稱</br>
	 * 若有信用卡強制停用資料 (有強制停用日期) ,查詢信用卡停用種類之中文名稱
	 * @param codeNo 代號
	 * @return Map
	 */
	Map<String, Object> findCreditCardDisbKindCNm(String codeNo);
}
