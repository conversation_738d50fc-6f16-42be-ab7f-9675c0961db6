/* 
 * L120S21CDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S21C;

/** LGD額度擔保品檔 **/
public interface L120S21CDao extends IGenericDao<L120S21C> {

	L120S21C findByOid(String oid);

	List<L120S21C> findByMainId(String mainId);

	List<L120S21C> findByIndex01(String mainId);

	List<L120S21C> findByIndex02(String mainId, String cntrNo_s21c);

	List<L120S21C> findByIndex03(String mainId, String cntrNo_s21c,
			String collType_s21c);

	List<L120S21C> findByColKind(String mainId, String cntrNo_s21c,
			String colKind_s21c);
}