/* 
 * C999S02ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C999S02A;

/** 個金約據書增補條款內容檔 **/
public interface C999S02ADao extends IGenericDao<C999S02A> {

	C999S02A findByOid(String oid);
	
	List<C999S02A> findByMainId(String mainId);
	
	C999S02A findByUniqueKey(String mainId, Integer seq, String type);

	List<C999S02A> findByIndex01(String mainId, Integer seq, String type);
}