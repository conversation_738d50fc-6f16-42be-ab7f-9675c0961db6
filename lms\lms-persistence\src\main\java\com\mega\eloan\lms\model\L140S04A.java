/* 
 * L140S04A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 額度明細表明細檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L140S04A", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L140S04A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * 文件產生方式<p/>
	 * 系統產生 | SYS<br/>
	 *  人工產生 | PEO
	 */
	@Size(max=3)
	@Column(name="CREATEBY", length=3, columnDefinition="CHAR(3)")
	private String createBY;

	/** 
	 * 類型<p/>
	 * 明細種類<br/>
	 *  1: 不符合授信政策
	 */
	@Size(max=1)
	@Column(name="ITEMTYPE", length=1, columnDefinition="CHAR(1)")
	private String itemType;

	/** 項目代號 **/
	@Size(max=3)
	@Column(name="SEQNO", length=3, columnDefinition="CHAR(3)")
	private String seqNo;

	/** 
	 * 項目名稱<p/>
	 * 128個全型字
	 */
	@Size(max=384)
	@Column(name="SEQNAME", length=384, columnDefinition="VARCHAR(384)")
	private String seqName;

	/** 事項顯示順序 **/
	@Size(max=3)
	@Column(name="SEQSHOW", length=3, columnDefinition="CHAR(3)")
	private String seqShow;

	/** 事項大類 **/
	@Size(max=3)
	@Column(name="BIGKIND", length=3, columnDefinition="CHAR(3)")
	private String bigKind;

	/** 
	 * 說明<p/>
	 * 最多兩百個中文/全形字
	 */
	@Size(max=600)
	@Column(name="DOCDSCR", length=600, columnDefinition="VARCHAR(600)")
	private String docDscr;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String Creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得文件產生方式<p/>
	 * 系統產生 | SYS<br/>
	 *  人工產生 | PEO
	 */
	public String getCreateBY() {
		return this.createBY;
	}
	/**
	 *  設定文件產生方式<p/>
	 *  系統產生 | SYS<br/>
	 *  人工產生 | PEO
	 **/
	public void setCreateBY(String value) {
		this.createBY = value;
	}

	/** 
	 * 取得類型<p/>
	 * 明細種類<br/>
	 *  1: 不符合授信政策
	 */
	public String getItemType() {
		return this.itemType;
	}
	/**
	 *  設定類型<p/>
	 *  明細種類<br/>
	 *  1: 不符合授信政策
	 **/
	public void setItemType(String value) {
		this.itemType = value;
	}

	/** 取得項目代號 **/
	public String getSeqNo() {
		return this.seqNo;
	}
	/** 設定項目代號 **/
	public void setSeqNo(String value) {
		this.seqNo = value;
	}

	/** 
	 * 取得項目名稱<p/>
	 * 128個全型字
	 */
	public String getSeqName() {
		return this.seqName;
	}
	/**
	 *  設定項目名稱<p/>
	 *  128個全型字
	 **/
	public void setSeqName(String value) {
		this.seqName = value;
	}

	/** 取得事項顯示順序 **/
	public String getSeqShow() {
		return this.seqShow;
	}
	/** 設定事項顯示順序 **/
	public void setSeqShow(String value) {
		this.seqShow = value;
	}

	/** 取得事項大類 **/
	public String getBigKind() {
		return this.bigKind;
	}
	/** 設定事項大類 **/
	public void setBigKind(String value) {
		this.bigKind = value;
	}

	/** 
	 * 取得說明<p/>
	 * 最多兩百個中文/全形字
	 */
	public String getDocDscr() {
		return this.docDscr;
	}
	/**
	 *  設定說明<p/>
	 *  最多兩百個中文/全形字
	 **/
	public void setDocDscr(String value) {
		this.docDscr = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.Creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.Creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
