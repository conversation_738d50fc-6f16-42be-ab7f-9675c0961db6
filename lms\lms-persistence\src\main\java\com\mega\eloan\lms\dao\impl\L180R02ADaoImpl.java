/* 
 * L180R02ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L180R02ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L180R02A;

/** 營運中心授權內外已核准已婉卻授信案件 **/
@Repository
public class L180R02ADaoImpl extends LMSJpaDao<L180R02A, String> implements
		L180R02ADao {

	@Override
	public L180R02A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L180R02A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.setMaxResults(Integer.MAX_VALUE);
		search.addOrderBy("caseDate");
		search.addOrderBy("approver");
		search.addOrderBy("docKind");
		search.addOrderBy("docType");
		search.addOrderBy("brno");
		List<L180R02A> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L180R02A> findByIndex01(String mainId,String docType,String docKind, String approver,
			String[] brnos) {
		ISearch search = createSearchTemplete();
		List<L180R02A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (docType != null && !"3".equals(docType)){
			search.addSearchModeParameters(SearchMode.EQUALS, "docType",
					docType);
		}
		if (docKind != null && !"3".equals(docKind)){
			search.addSearchModeParameters(SearchMode.EQUALS, "docKind",
					docKind);
		}
		if (approver != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "approver",
					approver);
		if (brnos != null)
			search.addSearchModeParameters(SearchMode.IN, "brno", brnos);
		search.setMaxResults(Integer.MAX_VALUE);
		search.addOrderBy("caseDate");
		search.addOrderBy("approver");
		search.addOrderBy("docKind");
		search.addOrderBy("docType");
		search.addOrderBy("brno");
		search.addOrderBy("caseNo");
		search.addOrderBy("custId");
		search.addOrderBy("cntrNo");
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L180R02A> findByIndex02(String mainId, String areaBranchId,
			String audit) {
		ISearch search = createSearchTemplete();
		List<L180R02A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (areaBranchId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "areaBranchId",
					areaBranchId);
		if (audit != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "audit", audit);
		search.setMaxResults(Integer.MAX_VALUE);
		search.addOrderBy("caseDate");
		search.addOrderBy("approver");
		search.addOrderBy("docKind");
		search.addOrderBy("docType");
		search.addOrderBy("brno");
		search.addOrderBy("caseNo");
		search.addOrderBy("custId");
		search.addOrderBy("cntrNo");
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L180R02A> findByIndex03(String[] oids, String mainId,String docType,String docKind) {
		ISearch search = createSearchTemplete();
		List<L180R02A> list = null;
		if (oids != null)
			search.addSearchModeParameters(SearchMode.IN, "oid", oids);
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (docType != null && !"3".equals(docType)){
			search.addSearchModeParameters(SearchMode.EQUALS, "docType",
					docType);
		}
		if (docKind != null && !"3".equals(docKind)){
			search.addSearchModeParameters(SearchMode.EQUALS, "docKind",
					docKind);
		}
		search.setMaxResults(Integer.MAX_VALUE);
		search.addOrderBy("caseDate");
		search.addOrderBy("approver");
		search.addOrderBy("docKind");
		search.addOrderBy("docType");
		search.addOrderBy("brno");
		search.addOrderBy("caseNo");
		search.addOrderBy("custId");
		search.addOrderBy("cntrNo");
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}
}