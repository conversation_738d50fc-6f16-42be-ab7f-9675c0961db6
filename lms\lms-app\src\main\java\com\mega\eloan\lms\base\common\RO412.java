package com.mega.eloan.lms.base.common;

import java.util.Date;

import tw.com.jcs.common.Util;

import com.mega.eloan.lms.mfaloan.bean.ELF412;
import com.mega.eloan.lms.mfaloan.bean.ELF412B;
import com.mega.eloan.lms.mfaloan.bean.ELF412C;
import com.mega.eloan.lms.model.L180M01B;
import com.mega.eloan.lms.model.L181M01A;
import com.mega.eloan.lms.model.L181M01B;

public class RO412 {
	private String brNo = "";
	private String custId = "";
	private String dupNo = "";
	/*
	 * 在 gfnGetNEW_DBUOBU_MINCHKDATE, 以下欄位抓 ELF412
	 */
	private String uckdLine = "";
	private Date uckdDt = null;
	private Date lrDate = null;
	private Date newDate = null;
	// 這裡的 mdFlag 比照ELF412,無leadingZero
	private String mdFlag = "";
	private Date mdDt = null;
	private String nckdFlag = "";
	/*
	 * 在 gfnGenCTLList_PROCESS_SHARE_GRADE, 以下欄位會被異動
	 */
	private String rckdLine = "";
	private String mainCust = "";
	private String crdtTbl = "";
	private String mowType = "";
	private String mowTbl1 = "";
	private String fcrdType = "";
	private String fcrdArea = "";
	private String fcrdPred = "";
	private String fcrdGrad = "";

	private String realCkFg = "";
	private Date realDt = null;

	private String nckdMemo = "";
	private String memo = "";
	private String ctlType = "";

	// J-106-0278-001 修改常董會或董事會權限授信實地覆審案件覆審週期計算
	private String newAdd = "";

	// J-108-0078_05097_B1001
	// 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。
	private String isAllNew = "";
    // 2020/04 配合新冠肺炎紓困貸款專案，新增 J.純紓困貸款戶之首次覆審
    private String isRescue = "";
    private String guarFlag = "";
	private String newRescue = "";
    private Date newRescueYM = null;
    // J-110-0272 抽樣覆審
    private String randomType = "";

	public RO412(String brNo, String custId, String dupNo, L181M01B l181m01b) {
		this.brNo = brNo;
		this.custId = custId;
		this.dupNo = dupNo;
		this.uckdLine = Util.trim(l181m01b.getElfUCkdLINE());
		this.uckdDt = l181m01b.getElfUCkdDt();
		this.lrDate = l181m01b.getElfLRDate();
		this.newDate = LrsUtil.model_elfNewDate_to_Date(l181m01b
				.getElfNewDate());
		// J-106-0278-001 修改常董會或董事會權限授信實地覆審案件覆審週期計算
		this.newAdd = l181m01b.getElfNewAdd();
		this.mdFlag = CrsUtil.mdFlag_trim_leadingZero(Util.trim(l181m01b
				.getElfMDFlag()));
		this.mdDt = l181m01b.getElfMDDt();
		this.nckdFlag = Util.trim(l181m01b.getElfNCkdFlag());

		this.rckdLine = Util.trim(l181m01b.getElfRCkdLine());
		this.mainCust = Util.trim(l181m01b.getElfMainCust());
		this.crdtTbl = Util.trim(l181m01b.getElfCrdTTbl());
		this.mowType = Util.trim(l181m01b.getElfMowType());
		this.mowTbl1 = Util.trim(l181m01b.getElfMowTbl1());
		this.fcrdType = Util.trim(l181m01b.getElfFcrdType());
		this.fcrdArea = Util.trim(l181m01b.getElfFcrdArea());
		this.fcrdPred = Util.trim(l181m01b.getElfFcrdPred());
		this.fcrdGrad = Util.trim(l181m01b.getElfFcrdGrad());

		this.realCkFg = Util.trim(l181m01b.getElfRealCkFg());
		this.realDt = l181m01b.getElfRealDt();

		L181M01A l181m01a = l181m01b.getL181m01a();

		if (l181m01a != null) {
			this.ctlType = Util.equals(Util.trim(l181m01a.getCtlType()), "") ? LrsUtil.CTLTYPE_主辦覆審
					: Util.trim(l181m01a.getCtlType());
		} else {
			this.ctlType = LrsUtil.CTLTYPE_主辦覆審;
		}

		// J-108-0078_05097_B1001
		// 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。
		this.isAllNew = Util.trim(l181m01b.getElfIsAllNew());
        // 2020/04 配合新冠肺炎紓困貸款專案，新增 J.純紓困貸款戶之首次覆審。
        this.isRescue = Util.trim(l181m01b.getElfIsRescue());
        this.guarFlag = Util.trim(l181m01b.getElfGuarFlag());
		this.newRescue = Util.trim(l181m01b.getElfNewRescue());
        this.newRescueYM = LrsUtil.model_elfNewDate_to_Date(l181m01b.getElfNewRescueYM());
        // J-110-0272 抽樣覆審
        this.randomType = Util.trim(l181m01b.getElfRandomType());
	}

	public RO412(ELF412 elf412) {
		this.brNo = elf412.getElf412_branch();
		this.custId = elf412.getElf412_custId();
		this.dupNo = elf412.getElf412_dupNo();
		this.uckdLine = Util.trim(elf412.getElf412_uckdLine());
		this.uckdDt = elf412.getElf412_uckdDt();
		this.lrDate = elf412.getElf412_lrDate();
		this.newDate = LrsUtil.elf412_rocDateStr_to_Date(elf412
				.getElf412_newDate());
		// J-106-0278-001 修改常董會或董事會權限授信實地覆審案件覆審週期計算
		this.newAdd = Util.trim(elf412.getElf412_newAdd());

		this.mdFlag = CrsUtil.mdFlag_trim_leadingZero(Util.trim(elf412
				.getElf412_mdFlag()));
		this.mdDt = elf412.getElf412_mdDt();
		this.nckdFlag = Util.trim(elf412.getElf412_nckdFlag());

		this.rckdLine = Util.trim(elf412.getElf412_rckdLine());
		this.mainCust = Util.trim(elf412.getElf412_mainCust());
		this.crdtTbl = Util.trim(elf412.getElf412_crdtTbl());
		this.mowType = Util.trim(elf412.getElf412_mowType());
		this.mowTbl1 = Util.trim(elf412.getElf412_mowTbl1());
		this.fcrdType = Util.trim(elf412.getElf412_fcrdType());
		this.fcrdArea = Util.trim(elf412.getElf412_fcrdArea());
		this.fcrdPred = Util.trim(elf412.getElf412_fcrdPred());
		this.fcrdGrad = Util.trim(elf412.getElf412_fcrdGrad());

		this.realCkFg = Util.trim(elf412.getElf412_realCkFg());
		this.realDt = elf412.getElf412_realDt();
		this.nckdMemo = elf412.getElf412_nckdMemo();
		this.memo = elf412.getElf412_memo();
		this.ctlType = LrsUtil.CTLTYPE_主辦覆審;

		// J-108-0078_05097_B1001
		// 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。
		this.isAllNew = Util.trim(elf412.getElf412_isAllNew());
        // 2020/04 配合新冠肺炎紓困貸款專案，新增 J.純紓困貸款戶之首次覆審。
        this.isRescue = Util.trim(elf412.getElf412_isRescue());
        this.guarFlag = Util.trim(elf412.getElf412_guarFlag());
		this.newRescue = Util.trim(elf412.getElf412_newRescue());
        this.newRescueYM = LrsUtil.elf412_rocDateStr_to_Date(elf412.getElf412_newRescueYM());
        // J-110-0272 抽樣覆審
        this.randomType = Util.trim(elf412.getElf412_randomType());
	}

	public RO412(ELF412B elf412b) {
		this.brNo = elf412b.getElf412b_branch();
		this.custId = elf412b.getElf412b_custId();
		this.dupNo = elf412b.getElf412b_dupNo();
		this.uckdLine = Util.trim(elf412b.getElf412b_uckdLine());
		this.uckdDt = elf412b.getElf412b_uckdDt();
		this.lrDate = elf412b.getElf412b_lrDate();
		this.newDate = LrsUtil.elf412_rocDateStr_to_Date(elf412b
				.getElf412b_newDate());
		// J-106-0278-001 修改常董會或董事會權限授信實地覆審案件覆審週期計算
		this.newAdd = Util.trim(elf412b.getElf412b_newAdd());

		this.nckdFlag = Util.trim(elf412b.getElf412b_nckdFlag());

		this.rckdLine = Util.trim(elf412b.getElf412b_rckdLine());
		this.mainCust = Util.trim(elf412b.getElf412b_mainCust());
		this.crdtTbl = Util.trim(elf412b.getElf412b_crdtTbl());
		this.mowType = Util.trim(elf412b.getElf412b_mowType());
		this.mowTbl1 = Util.trim(elf412b.getElf412b_mowTbl1());
		this.fcrdType = Util.trim(elf412b.getElf412b_fcrdType());
		this.fcrdArea = Util.trim(elf412b.getElf412b_fcrdArea());
		this.fcrdPred = Util.trim(elf412b.getElf412b_fcrdPred());
		this.fcrdGrad = Util.trim(elf412b.getElf412b_fcrdGrad());

		this.nckdMemo = elf412b.getElf412b_nckdMemo();
		this.memo = elf412b.getElf412b_memo();
		this.ctlType = LrsUtil.CTLTYPE_自辦覆審;

	}

	// J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
	public RO412(ELF412C elf412c) {
		this.brNo = elf412c.getElf412c_branch();
		this.custId = elf412c.getElf412c_custId();
		this.dupNo = elf412c.getElf412c_dupNo();
		this.uckdLine = Util.trim(elf412c.getElf412c_uckdLine());
		this.uckdDt = elf412c.getElf412c_uckdDt();
		this.lrDate = elf412c.getElf412c_lrDate();
		this.newDate = LrsUtil.elf412_rocDateStr_to_Date(elf412c
				.getElf412c_newDate());
		// J-106-0278-001 修改常董會或董事會權限授信實地覆審案件覆審週期計算
		this.newAdd = Util.trim(elf412c.getElf412c_newAdd());

		this.nckdFlag = Util.trim(elf412c.getElf412c_nckdFlag());

		this.rckdLine = Util.trim(elf412c.getElf412c_rckdLine());
		this.mainCust = Util.trim(elf412c.getElf412c_mainCust());
		this.crdtTbl = Util.trim(elf412c.getElf412c_crdtTbl());
		this.mowType = Util.trim(elf412c.getElf412c_mowType());
		this.mowTbl1 = Util.trim(elf412c.getElf412c_mowTbl1());
		this.fcrdType = Util.trim(elf412c.getElf412c_fcrdType());
		this.fcrdArea = Util.trim(elf412c.getElf412c_fcrdArea());
		this.fcrdPred = Util.trim(elf412c.getElf412c_fcrdPred());
		this.fcrdGrad = Util.trim(elf412c.getElf412c_fcrdGrad());

		this.nckdMemo = elf412c.getElf412c_nckdMemo();
		this.memo = elf412c.getElf412c_memo();
		this.ctlType = LrsUtil.CTLTYPE_價金履約;

	}

	public RO412(RO412 roObj, ELF412 elf412) {
		this.brNo = roObj.get_brNo();
		this.custId = roObj.get_custId();
		this.dupNo = roObj.get_dupNo();
		this.uckdLine = Util.trim(elf412.getElf412_uckdLine());
		this.uckdDt = elf412.getElf412_uckdDt();
		this.lrDate = elf412.getElf412_lrDate();
		this.newDate = LrsUtil.elf412_rocDateStr_to_Date(elf412
				.getElf412_newDate());
		// J-106-0278-001 修改常董會或董事會權限授信實地覆審案件覆審週期計算
		this.newAdd = Util.trim(elf412.getElf412_newAdd());
		this.mdFlag = CrsUtil.mdFlag_trim_leadingZero(Util.trim(elf412
				.getElf412_mdFlag()));
		this.mdDt = elf412.getElf412_mdDt();
		this.nckdFlag = Util.trim(elf412.getElf412_nckdFlag());

		this.rckdLine = Util.trim(elf412.getElf412_rckdLine());
		this.mainCust = roObj.get_mainCust();
		this.crdtTbl = roObj.get_crdtTbl();
		this.mowType = roObj.get_mowType();
		this.mowTbl1 = roObj.get_mowTbl1();
		this.fcrdType = roObj.get_fcrdType();
		this.fcrdArea = roObj.get_fcrdArea();
		this.fcrdPred = roObj.get_fcrdPred();
		this.fcrdGrad = roObj.get_fcrdGrad();

		this.realCkFg = Util.trim(elf412.getElf412_realCkFg());
		this.realDt = elf412.getElf412_realDt();
		this.ctlType = LrsUtil.CTLTYPE_主辦覆審;

		// J-108-0078_05097_B1001
		// 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。
		this.isAllNew = Util.trim(roObj.get_isAllNew());
        // 2020/04 配合新冠肺炎紓困貸款專案，新增 J.純紓困貸款戶之首次覆審。
        this.isRescue = Util.trim(roObj.get_isRescue());
        this.guarFlag = Util.trim(roObj.get_guarFlag());
		this.newRescue = Util.trim(elf412.getElf412_newRescue());
        this.newRescueYM = LrsUtil.elf412_rocDateStr_to_Date(elf412.getElf412_newRescueYM());
        // J-110-0272 抽樣覆審
        this.randomType = Util.trim(roObj.get_randomType());
	}

	public RO412(RO412 roObj, String newELF412_RCKDLINE,
			String newELF412_MAINCUST, String newELF412_CRDTTBL,
			String newELF412_MOWTYPE, String newELF412_MOWTBL1,
			String newELF412_FCRDTYPE, String newELF412_FCRDAREA,
			String newELF412_FCRDPRED, String newELF412_FCRDGRAD) {
		this.brNo = roObj.get_brNo();
		this.custId = roObj.get_custId();
		this.dupNo = roObj.get_dupNo();
		this.uckdLine = roObj.get_uckdLine();
		this.uckdDt = roObj.get_uckdDt();
		this.lrDate = roObj.get_lrDate();
		this.newDate = roObj.get_newDate();
		// J-106-0278-001 修改常董會或董事會權限授信實地覆審案件覆審週期計算
		this.newAdd = roObj.get_newAdd();

		this.mdFlag = roObj.get_mdFlag();
		this.mdDt = roObj.get_mdDt();
		this.nckdFlag = roObj.get_nckdFlag();

		this.rckdLine = newELF412_RCKDLINE;
		this.mainCust = newELF412_MAINCUST;
		this.crdtTbl = newELF412_CRDTTBL;
		this.mowType = newELF412_MOWTYPE;
		this.mowTbl1 = newELF412_MOWTBL1;
		this.fcrdType = newELF412_FCRDTYPE;
		this.fcrdArea = newELF412_FCRDAREA;
		this.fcrdPred = newELF412_FCRDPRED;
		this.fcrdGrad = newELF412_FCRDGRAD;

		this.realCkFg = roObj.get_realCkFg();
		this.realDt = roObj.get_realDt();
		this.ctlType = Util.equals(Util.trim(roObj.get_ctlType()), "") ? LrsUtil.CTLTYPE_主辦覆審
				: Util.trim(roObj.get_ctlType());

		// J-108-0078_05097_B1001
		// 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。
		this.isAllNew = Util.trim(roObj.get_isAllNew());
        // 2020/04 配合新冠肺炎紓困貸款專案，新增 J.純紓困貸款戶之首次覆審。
        this.isRescue = Util.trim(roObj.get_isRescue());
        this.guarFlag = Util.trim(roObj.get_guarFlag());
		this.newRescue = Util.trim(roObj.get_newRescue());
		this.newRescueYM = roObj.get_newRescueYM();
        // J-110-0272 抽樣覆審
        this.randomType = Util.trim(roObj.get_randomType());
	}

	/**
	 * 目前看到的, 都是用 ElfNckdFlag 去算, 沒有用 NewNckdFlag 去算
	 */
	public RO412(String brNo, L180M01B l180m01b) {
		this.brNo = brNo;
		this.custId = l180m01b.getCustId();
		this.dupNo = l180m01b.getDupNo();
		this.uckdLine = Util.trim(l180m01b.getElfUCkdLINE());
		this.uckdDt = l180m01b.getElfUCkdDt();
		this.lrDate = l180m01b.getElfLRDate();
		this.newDate = LrsUtil.model_elfNewDate_to_Date(l180m01b
				.getElfNewDate());
		// J-106-0278-001 修改常董會或董事會權限授信實地覆審案件覆審週期計算
		this.newAdd = Util.trim(l180m01b.getElfNewAdd());
		this.mdFlag = CrsUtil.mdFlag_trim_leadingZero(Util.trim(l180m01b
				.getElfMDFlag()));
		this.mdDt = l180m01b.getElfMDDt();
		this.nckdFlag = Util.trim(l180m01b.getElfNCkdFlag());

		this.rckdLine = Util.trim(l180m01b.getElfRCkdLine());
		this.mainCust = Util.trim(l180m01b.getElfMainCust());
		this.crdtTbl = Util.trim(l180m01b.getElfCrdTTbl());
		this.mowType = Util.trim(l180m01b.getElfMowType());
		this.mowTbl1 = Util.trim(l180m01b.getElfMowTbl1());
		this.fcrdType = Util.trim(l180m01b.getElfFcrdType());
		this.fcrdArea = Util.trim(l180m01b.getElfFcrdArea());
		this.fcrdPred = Util.trim(l180m01b.getElfFcrdPred());
		this.fcrdGrad = Util.trim(l180m01b.getElfFcrdGrad());

		this.realCkFg = Util.trim(l180m01b.getElfRealCkFg());
		this.realDt = l180m01b.getElfRealDt();
		this.ctlType = Util.equals(Util.trim(l180m01b.getCtlType()), "") ? LrsUtil.CTLTYPE_主辦覆審
				: Util.trim(l180m01b.getCtlType());

		// J-108-0078_05097_B1001
		// 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。
		this.isAllNew = Util.trim(l180m01b.getElfIsAllNew());
        // 2020/04 配合新冠肺炎紓困貸款專案，新增 J.純紓困貸款戶之首次覆審。
        this.isRescue = Util.trim(l180m01b.getElfIsRescue());
        this.guarFlag = Util.trim(l180m01b.getElfGuarFlag());
		this.newRescue = Util.trim(l180m01b.getElfNewRescue());
		this.newRescueYM = LrsUtil.model_elfNewDate_to_Date(l180m01b.getElfNewRescueYM());
        // J-110-0272 抽樣覆審
        this.randomType = Util.trim(l180m01b.getElfRandomType());
	}

	public String get_custInfo() {
		return this.brNo + "-" + this.custId + "-" + this.dupNo;
	}

	public String get_brNo() {
		return this.brNo;
	}

	public String get_custId() {
		return this.custId;
	}

	public String get_dupNo() {
		return this.dupNo;
	}

	public String get_mainCust() {
		return this.mainCust;
	}

	public String get_crdtTbl() {
		return this.crdtTbl;
	}

	public String get_mowType() {
		return this.mowType;
	}

	public String get_mowTbl1() {
		return this.mowTbl1;
	}

	public Date get_lrDate() {
		return this.lrDate;
	}

	public String get_rckdLine() {
		return this.rckdLine;
	}

	public String get_mdFlag() {
		return this.mdFlag;
	}

	public Date get_mdDt() {
		return this.mdDt;
	}

	public Date get_newDate() {
		return this.newDate;
	}

	public String get_nckdFlag() {
		return this.nckdFlag;
	}

	public String get_uckdLine() {
		return this.uckdLine;
	}

	public Date get_uckdDt() {
		return this.uckdDt;
	}

	public String get_fcrdType() {
		return this.fcrdType;
	}

	public String get_fcrdArea() {
		return this.fcrdArea;
	}

	public String get_fcrdPred() {
		return this.fcrdPred;
	}

	public String get_fcrdGrad() {
		return this.fcrdGrad;
	}

	public String get_realCkFg() {
		return this.realCkFg;
	}

	public Date get_realDt() {
		return this.realDt;
	}

	public String get_nckdMemo() {
		return this.nckdMemo;
	}

	public String get_memo() {
		return this.memo;
	}

	public String get_newAdd() {
		return this.newAdd;
	}

	public String get_ctlType() {
		return Util.equals(Util.trim(this.ctlType), "") ? LrsUtil.CTLTYPE_主辦覆審
				: Util.trim(this.ctlType);

	}

	public String get_isAllNew() {
		return this.isAllNew;
	}

    // 2020/04 配合新冠肺炎紓困貸款專案，新增 J.純紓困貸款戶之首次覆審。
    public String get_isRescue() {
        return this.isRescue;
    }
    public String get_guarFlag() {
        return this.guarFlag;
    }
	public String get_newRescue() {
		return this.newRescue;
	}
    public Date get_newRescueYM() {
        return this.newRescueYM;
    }
    // J-110-0272 抽樣覆審
    public String get_randomType() {
        return this.randomType;
    }
}
