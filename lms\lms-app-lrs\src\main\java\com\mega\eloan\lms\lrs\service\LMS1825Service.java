/* 
 * LMS1825Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lrs.service;

import java.util.List;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.ICapService;

import com.mega.eloan.lms.model.L182M01A;

public interface LMS1825Service extends ICapService {

	/**
	 * 利用Oid做搜尋
	 * 
	 * @param <T>
	 * @param clazz
	 * @param oid
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	<T extends GenericBean> T findModelByOid(Class clazz, String oid);

	/**
	 * 搜尋
	 * 
	 * @param clazz
	 * @param search
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	Page<? extends GenericBean> findPage(Class clazz, ISearch search);

	/**
	 * 單筆儲存
	 * 
	 * @param entity
	 */
	void save(GenericBean... entity);

	/**
	 * 單筆刪除
	 * 
	 * @param clazz
	 * @param oid
	 */
	@SuppressWarnings("rawtypes")
	void delete(Class clazz, String oid);

	/**
	 * 流程
	 * @param mainOid
	 * @param model
	 * @param setResult
	 * @param resultType
	 * @throws Throwable
	 */
	void flowAction(String mainOid, GenericBean model, boolean setResult,
			boolean resultType) throws Throwable;

	/**
	 * 起流程
	 * @param mainOid
	 */
	void startFlow(String mainOid);

	/**
	 * 刪除主檔
	 * @param oid
	 */
	void deleteMain(String oid);

	/**
	 * 搜尋多筆
	 * @param docStatus
	 * @param genDate
	 * @param baseDate
	 * @param branchList
	 * @return
	 */
	List<L182M01A> findUnProcessedTypCd5();

}
