/* 
 * L120S01CDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S01C;


/** 企金信用評等資料檔 **/
public interface L120S01CDao extends IGenericDao<L120S01C> {

	L120S01C findByOid(String oid);

	List<L120S01C> findByMainId(String mainId);

	List<L120S01C> findByCustId(String mainId, String custId, String dupNo);

	List<L120S01C> findByCustIdOrderByCrdTYear(String mainId, String custId,
			String dupNo);
			
	L120S01C findByUniqueKey(String mainId, String custId, String dupNo,
			Date crdTYear, String crdTBR, String crdType, String finYear,
			String cntrNo);

	List<L120S01C> findByIndex01(String mainId, String custId, String dupNo,
			Date crdTYear, String crdTBR, String crdType, String finYear,
			String cntrNo);

	int delModel(String mainId);

	List<L120S01C> findByCntrNo(String CntrNo);
	
	List<L120S01C> findByCustIdDupId(String custId,String DupNo);
}