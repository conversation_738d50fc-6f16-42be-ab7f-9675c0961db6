/* 
 *MisELLNGTEEServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.mfaloan.service.impl;

import java.math.BigDecimal;
import java.sql.Types;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import tw.com.jcs.common.Util;

import com.mega.eloan.lms.mfaloan.service.MisELLNGTEEService;

/**
 * <pre>
 * 主從債務人檔 ELLNGTEE(MIS.ELV40101)
 * </pre>
 * 
 * @since 2011/12/23
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/12/23,REX,new
 *          </ul>
 */
@Service
public class MisELLNGTEEServiceImpl extends AbstractMFAloanJdbc implements
		MisELLNGTEEService {

	@Override
	public List<Map<String, Object>> getByCustIdCntrNo(String custId,
			String dupNo, String cntrNo) {
		return getJdbc().queryForListWithMax("ELLNGTEE.getByCustIdCntrNo",
				new String[] { custId, dupNo, cntrNo });
	}

	// @SuppressWarnings("rawtypes")
	// @Override
	// public Map findMisEllngtee(String custId, String dupNo, String brNo) {
	//
	// return getJdbc().queryForMap("ELLNGTEE.getByCustIdCntrNo",
	// new String[] { custId, dupNo, brNo });
	// }

	@Override
	public void delEllngteeByUniqueKey(String brNo, String custId,
			String dupNo, String cntrNo) {
		// brNo => 不要含BRNO，因為資料建檔那邊的BRNO是維護分行，如果007維護 005的額度序號，那BRNO會變成007
		this.getJdbc().update("ELLNGTEE.delByUniqueKey",
				new Object[] { custId, dupNo, cntrNo });

	}

	@Override
	public void insert(List<Object[]> dataList) {
		this.getJdbc().batchUpdate(
				"ELLNGTEE.insert",
				new int[] { Types.CHAR, Types.CHAR, Types.CHAR, Types.CHAR,
						Types.CHAR, Types.CHAR, Types.CHAR, Types.CHAR,
						Types.CHAR, Types.CHAR, Types.DATE, Types.CHAR,
						Types.DATE, Types.DECIMAL, Types.DECIMAL, Types.CHAR, Types.DECIMAL, Types.CHAR},
				dataList);

	}

	// @Override
	// public List<Map<String, Object>> getRltGuarantorByCustIdCntrNo(
	// String custId, String dupNo, String cntrNo) {
	//
	// return
	// getJdbc().queryForListWithMax("ELLNGTEE.getRltGuarantorByCustIdCntrNo",
	// new String[] { custId, dupNo, cntrNo });
	// }

	@Override
	public List<Map<String, Object>> findMisellngteeSelSell(String id,
			String dupno) {
		return this.getJdbc().queryForListWithMax("MISELLNGTEE.selSell",
				new Object[] { id, dupno });
	}

	@Override
	public List<Map<String, Object>> findMisellngteeForSM(String brNo,
			String id, String dupno, String cntrNo) {
		return this.getJdbc().queryForListWithMax(
				"MISELLNGTEE.findMisellngteeForSM",
				new Object[] { brNo, id, dupno, cntrNo });
	}

	@Override
	public List<Map<String, Object>> findByCntrNo(String cntrNo) {
		return this.getJdbc().queryForListWithMax("MIS.findELLNGTEEByCntrNo",
				new Object[] { cntrNo });
	}

	@Override
	public List<Map<String, Object>> findByLgneId(String lgneId, String dupNo,
			String cancelDate) {
		return this.getJdbc().queryForListWithMax("MIS.ELLNGTEE.FindByLgneid",
				new Object[] { lgneId, dupNo, cancelDate });
	}

	// @Override
	// public List<Map<String, Object>> findByCustId(String custId, String
	// dupNo,
	// String cancelDate) {
	// return this.getJdbc()
	// .queryForListWithMax(
	// "LN.LNF030.FindByCustId",
	// new Object[] { Util.trim(custId) + Util.trim(dupNo),
	// cancelDate });
	// }

	// @Override
	// public List<Map<String, Object>> findByCntrno(String cntrNo) {
	// return this.getJdbc().queryForListWithMax("MIS.ELLNGTEE.findByCntrno",
	// new Object[] { cntrNo });
	// }

	@Override
	public Set<String> findByLgneIdSet(String lgneId, String dupNo,
			String cancelDate) {
		Set<String> result = new HashSet<String>();
		excludeLgneId(result, lgneId, dupNo, cancelDate);
		return result;
	}

	private void excludeLgneId(Set<String> set, String lgneId, String dupNo,
			String cancelDate) {
		if (!set.contains(lgneId + dupNo)) {
			set.add(lgneId + dupNo);
			List<Map<String, Object>> list = findByLgneId(lgneId, dupNo,
					cancelDate);
			for (Map<String, Object> map : list)
				excludeLgneId(set, Util.trim(map.get("CUSTID")),
						Util.trim(map.get("DUPNO")), cancelDate);
		}
	}

	@Override
	public List<Map<String, Object>> findByCustId2(String id, String cancelDate) {
		return this.getJdbc().queryForList("LN.LNF030.FindByCustId2",
				new Object[] { id, cancelDate });
	}

	@Override
	public List<Map<String, Object>> findMisellngteeNotCancelAllLngeid(
			String id, String dupno) {
		return this.getJdbc()
				.queryForListWithMax("MISELLNGTEE.selNotCancelAllLngeid",
						new Object[] { id, dupno });
	}

	@Override
	public List<Map<String, Object>> findByCrs(String id, String dupNo,
			String cntrNo) {
		return this.getJdbc().queryForListWithMax("MISELLNGTEE.selByCrs",
				new Object[] { id, dupNo, cntrNo });
	}

	@Override
	public List<Map<String, Object>> findByLrs(String id, String dupNo,
			String cntrNo) {
		return this.getJdbc().queryForListWithMax("MISELLNGTEE.selByLrs",
				new Object[] { id, dupNo, cntrNo });
	}

	@Override
	public String get_lrs_Rlt_Borrower(String branch, String custId,
			String dupNo) {
		LinkedHashMap<String, String> m = _idNameMap(this.getJdbc()
				.queryForList("MIS.ELLNGTEE.get_lrs_Rlt_Borrower",
						new String[] { custId, dupNo, branch }));
		if (m.size() == 0) {
			return "無";
		} else {
			return StringUtils.join(m.values(), "、");
		}
	}

	@Override
	public String get_lrs_Rlt_Guarantor(String branch, String custId,
			String dupNo) {
		LinkedHashMap<String, String> m_G = _idNameMap(this.getJdbc()
				.queryForList("MIS.ELLNGTEE.get_lrs_Rlt_Guarantor",
						new String[] { custId, dupNo, branch, "G" }));// 參考
																		// UtilConstants.lngeFlag.連帶保證人
		LinkedHashMap<String, String> m_I = _idNameMap(this.getJdbc()
				.queryForList("MIS.ELLNGTEE.get_lrs_Rlt_Guarantor",
						new String[] { custId, dupNo, branch, "N" }));// 參考
																		// UtilConstants.lngeFlag.ㄧ般保證人

		String tRlt_name1 = StringUtils.join(m_G.values(), "、");
		String tRlt_name2 = StringUtils.join(m_I.values(), "、");
		String tRlt_name = "";
		if (Util.isNotEmpty(tRlt_name1)) {
			tRlt_name += ("(連帶)" + tRlt_name1);
		}
		if (Util.isNotEmpty(tRlt_name2)) {
			tRlt_name += ("(一般)" + tRlt_name2);
		}

		if (Util.isNotEmpty(tRlt_name)) {
			return tRlt_name;
		}
		return "無";
	}

	private LinkedHashMap<String, String> _idNameMap(
			List<Map<String, Object>> list) {
		LinkedHashMap<String, String> m = new LinkedHashMap<String, String>();
		for (Map<String, Object> db : list) {
			String id = Util.trim(db.get("LNGEID"));
			String name = Util.trim(db.get("LNGENM"));
			m.put(id, name);
		}
		return m;
	}

	@Override
	public void clearPriorfg(String brNo, String custId, String dupNo,
			String cntrNo) {
		this.getJdbc().update("ELLNGTEE.clearPriorfg",
				new Object[] { brNo, custId, dupNo, cntrNo });

	}

	@Override
	public void updatePriorfg(String brNo, String custId, String dupNo,
			String cntrNo, String lnGeId, String dupNo1, String lngeFlag,
			BigDecimal priorFg) {
		this.getJdbc().update(
				"ELLNGTEE.updatePriorfg",
				new Object[] { priorFg, brNo, custId, dupNo, cntrNo, lnGeId,
						dupNo1, lngeFlag });

	}

	/**
	 * J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
	 * 
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @return
	 */
	@Override
	public List<Map<String, Object>> getAllByCustIdCntrNo(String custId,
			String dupNo, String cntrNo) {
		return getJdbc().queryForListWithMax("ELLNGTEE.getAllByCustIdCntrNo",
				new String[] { custId, dupNo, cntrNo });
	}

	/**
	 * RPAQueryEllngteeServiceImpl 使用
	 */
	@Override
	public List<Map<String, Object>> findMisEllngteeNotCancelAllLngeidForRpa(
			String id, String dupno) {
		return this.getJdbc().queryForListWithMax(
				"MISELLNGTEE.selNotCancelAllLngeidForRpa",
				new Object[] { id, dupno });
	}
	
	@Override
	public List<Map<String, Object>> getAllDataByCustIdCntrNo(String custId,
			String dupNo, String cntrNo) {
		return getJdbc().queryForListWithMax("ELLNGTEE.getAllDataByCustIdCntrNo",
				new String[] { custId, dupNo, cntrNo });
	}

}
