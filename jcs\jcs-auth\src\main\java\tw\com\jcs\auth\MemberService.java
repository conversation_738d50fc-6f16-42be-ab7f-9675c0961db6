package tw.com.jcs.auth;

import java.util.List;
import java.util.Map;
import java.util.Set;

import tw.com.jcs.auth.model.Branch;
import tw.com.jcs.auth.model.Department;
import tw.com.jcs.auth.model.User;

/**
 * <pre>
 * MemberService
 * </pre>
 * 
 * @since 2022年12月22日
 * <AUTHOR> @version
 *          <ul>
 *          <li>2022年12月22日
 *          </ul>
 */
public interface MemberService {

    /**
     * 已棄用
     * 
     * @param userId
     * @return
     */
    @Deprecated
    User getUser(String userId);

    /**
     * 已棄用
     * 
     * @param deptId
     * @return
     */
    @Deprecated
    Department getDept(String deptId);

    /**
     * 已棄用
     * 
     * @param type
     * @return
     */
    @Deprecated
    List<Department> getDeptByType(String... type);

    /**
     * 已棄用
     * 
     * @param userId
     * @return
     */
    @Deprecated
    Set<String> getRolesByUser(String userId);

    /**
     * 依照角色取得權限
     * 
     * @param roleId
     *            角色
     * @return
     */
    Map<Integer, Integer> getAuthesByRole(String roleId);

    /**
     * 依照角色取得權限
     * 
     * @param roleId
     *            角色
     * @return
     */
    // add by fantasy 2012/04/13
    Map<String, Integer> getAuthesByRoleAndDocid(String roleId);

    /**
     * 已棄用
     * 
     * @param roleId
     * @return
     */
    @Deprecated
    List<User> getUsersByRole(String... roleId);

    /**
     * 已棄用
     * 
     * @param deptId
     * @return
     */
    @Deprecated
    List<User> getUsersByDept(String deptId);

    /**
     * 已棄用
     * 
     * @param authCode
     * @return
     */
    @Deprecated
    List<User> getUsersByAuth(int... authCode);

    /**
     * 已棄用
     * 
     * @param deptId
     * @param authCode
     * @return
     */
    @Deprecated
    List<User> getUsersByDeptAndAuth(String deptId, int... authCode);

    /**
     * 已棄用
     * 
     * @param deptId
     * @param roleId
     * @return
     */
    @Deprecated
    List<User> getUsersByDeptAndRole(String deptId, String... roleId);

    /**
     * 已棄用
     * 
     * @param userId
     * @param auth
     * @return
     */
    @Deprecated
    int getAuthTypeByUserAndAuth(String userId, int auth);

    /**
     * 依照角色取得權限
     * 
     * @param roles
     *            角色
     * @param auth
     *            權限
     * @return
     */
    // add by fantasy 2011/05/10
    int getAuthTypeByRoles(Set<String> roles, int auth);

    /**
     * 依照角色取得權限
     * 
     * @param pgmDept
     *            指定部門
     * @param roles
     *            角色
     * @param pgmcode
     *            功能代碼
     * @return
     */
    // add by UFO 2012/02/12
    int getAuthTypeByRoles(String pgmDept, Set<String> roles, int pgmcode);

    /**
     * 依照角色取得權限
     * 
     * @param pgmDept
     *            指定部門
     * @param roles
     *            角色
     * @param pgmcode
     *            功能代碼
     * @param docid
     *            文件代碼
     * @return
     */
    // add by fantasy 2012/04/13
    int getAuthTypeByRoles(String pgmDept, Set<String> roles, int pgmcode, String docid);

    /**
     * 轉換為eloan roles
     * 
     * @param mapRolse
     * @param branch
     *            分行
     * @return
     */
    Set<String> megaSSORotesToEloanRotes(Map<String, String> mapRolse, Branch branch);

    /**
     * 判斷有無傳入的角色
     * 
     * @param role
     *            角色
     * @return
     */
    boolean hasRole(String role);

    /**
     * 重新載入
     */
    public void reload();

}
