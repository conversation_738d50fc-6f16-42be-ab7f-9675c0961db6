/* 
 * C140SDSCDao.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;
import tw.com.iisi.cap.model.GenericBean;

import com.mega.eloan.lms.model.C140SDSC;

/**
 * <pre>
 * 徵信調查報告書副檔 註記說明
 * </pre>
 * 
 * @since 2011/9/21
 * <AUTHOR>
 * @version <ul>
 *          <li>new
 *          </ul>
 */
public interface C140SDSCDao extends IGenericDao<C140SDSC> {

	/**
	 * 取得註記說明內容
	 * 
	 * @param uid
	 *            String
	 * @param mainId
	 *            String
	 * @return C140SDSC
	 */
	public List<C140SDSC> findByMainPid(String uid, String mainId);
	
	/**
	 * 取得註記說明內容
	 * 
	 * @param uid
	 *            String
	 * @param mainId
	 *            String
	 * @param tab
	 *            String
	 * @return C140SDSC
	 */
	public List<C140SDSC> findByMainPidTab(String uid, String mainId, String tab);

	/**
	 * 取得註記說明內容
	 * 
	 * @param uid
	 *            String
	 * @param mainId
	 *            String
	 * @param tab
	 *            String
	 * @param fieldId
	 *            欄位ID
	 * @return C140SDSC
	 */
	public C140SDSC findByMainPidTab(String uid, String mainId, String tab,
			String fieldId);
	
	/**
	 * 刪除所有資料
	 * 
	 * @param meta GenericBean
	 * @return int
	 */
	int deleteByMeta(GenericBean meta);

}
