package com.mega.eloan.lms.base.common;

import java.util.ArrayList;
import java.util.List;

public class ClsNoAgreeQueryEjVerOnlineCntrNo {
	public static List<String> upd_ELF459_SRCFLAG_prodKind69_noAgreeQueryEjVer_2020_part1(){ //noAgreeQueryEjVer_2020_part1 共 5000筆
		List<String> r = new ArrayList<String>();
		r.add("002110900354");
		r.add("002110900399");
		r.add("002110900418");
		r.add("002110900471");
		r.add("002110900498");
		r.add("002110900502");
		r.add("002110900509");
		r.add("002110900532");
		r.add("002110900535");
		r.add("002110900544");
		r.add("002110900564");
		r.add("002110900568");
		r.add("002110900599");
		r.add("002110900600");
		r.add("002110900605");
		r.add("002110900616");
		r.add("002110900622");
		r.add("002110900649");
		r.add("002110900660");
		r.add("002110900675");
		r.add("002110900681");
		r.add("002110900686");
		r.add("002110900707");
		r.add("002110900717");
		r.add("002110900719");
		r.add("002110900749");
		r.add("002110900754");
		r.add("002110900756");
		r.add("002110900771");
		r.add("002110900798");
		r.add("002110900803");
		r.add("002110900805");
		r.add("002110900810");
		r.add("002110900811");
		r.add("002110900815");
		r.add("002110900821");
		r.add("002110900824");
		r.add("002110900826");
		r.add("002110900827");
		r.add("002110900830");
		r.add("002110900831");
		r.add("002110900853");
		r.add("002110900854");
		r.add("002110900863");
		r.add("002110900868");
		r.add("002110900892");
		r.add("002110900894");
		r.add("002110900903");
		r.add("002110900919");
		r.add("002110900938");
		r.add("002110900961");
		r.add("002110900963");
		r.add("002110900974");
		r.add("002110901008");
		r.add("003110900154");
		r.add("003110900173");
		r.add("003110900259");
		r.add("003110900276");
		r.add("003110900277");
		r.add("003110900286");
		r.add("003110900295");
		r.add("003110900359");
		r.add("003110900361");
		r.add("003110900362");
		r.add("003110900363");
		r.add("003110900364");
		r.add("003110900371");
		r.add("003110900374");
		r.add("003110900376");
		r.add("003110900377");
		r.add("003110900380");
		r.add("003110900381");
		r.add("003110900382");
		r.add("003110900384");
		r.add("003110900386");
		r.add("003110900388");
		r.add("003110900390");
		r.add("003110900391");
		r.add("003110900392");
		r.add("003110900396");
		r.add("003110900397");
		r.add("003110900398");
		r.add("003110900399");
		r.add("003110900400");
		r.add("003110900401");
		r.add("003110900402");
		r.add("003110900403");
		r.add("003110900405");
		r.add("003110900406");
		r.add("003110900407");
		r.add("003110900408");
		r.add("003110900409");
		r.add("003110900410");
		r.add("003110900411");
		r.add("003110900412");
		r.add("003110900413");
		r.add("003110900414");
		r.add("003110900415");
		r.add("003110900416");
		r.add("003110900418");
		r.add("003110900419");
		r.add("003110900420");
		r.add("003110900421");
		r.add("003110900422");
		r.add("003110900423");
		r.add("003110900424");
		r.add("003110900425");
		r.add("003110900426");
		r.add("003110900427");
		r.add("003110900428");
		r.add("003110900429");
		r.add("003110900430");
		r.add("003110900431");
		r.add("003110900432");
		r.add("003110900433");
		r.add("003110900435");
		r.add("003110900437");
		r.add("003110900439");
		r.add("003110900440");
		r.add("003110900441");
		r.add("003110900442");
		r.add("003110900443");
		r.add("003110900444");
		r.add("003110900445");
		r.add("003110900446");
		r.add("003110900447");
		r.add("003110900448");
		r.add("003110900449");
		r.add("003110900451");
		r.add("003110900452");
		r.add("003110900453");
		r.add("003110900454");
		r.add("003110900455");
		r.add("003110900457");
		r.add("003110900458");
		r.add("003110900459");
		r.add("003110900460");
		r.add("003110900461");
		r.add("003110900463");
		r.add("003110900464");
		r.add("003110900465");
		r.add("003110900466");
		r.add("003110900467");
		r.add("003110900469");
		r.add("003110900470");
		r.add("003110900471");
		r.add("003110900472");
		r.add("003110900473");
		r.add("003110900474");
		r.add("003110900475");
		r.add("003110900476");
		r.add("003110900477");
		r.add("003110900479");
		r.add("003110900480");
		r.add("003110900481");
		r.add("003110900483");
		r.add("003110900488");
		r.add("003110900489");
		r.add("003110900492");
		r.add("003110900493");
		r.add("003110900494");
		r.add("003110900495");
		r.add("003110900496");
		r.add("003110900497");
		r.add("003110900498");
		r.add("003110900499");
		r.add("003110900501");
		r.add("003110900502");
		r.add("003110900503");
		r.add("003110900504");
		r.add("003110900505");
		r.add("003110900506");
		r.add("003110900507");
		r.add("003110900508");
		r.add("003110900509");
		r.add("003110900510");
		r.add("003110900511");
		r.add("003110900513");
		r.add("003110900514");
		r.add("003110900515");
		r.add("003110900516");
		r.add("003110900517");
		r.add("003110900518");
		r.add("003110900519");
		r.add("003110900520");
		r.add("003110900522");
		r.add("003110900523");
		r.add("003110900524");
		r.add("003110900525");
		r.add("003110900526");
		r.add("003110900527");
		r.add("003110900528");
		r.add("003110900529");
		r.add("003110900530");
		r.add("003110900531");
		r.add("003110900532");
		r.add("003110900533");
		r.add("003110900534");
		r.add("003110900535");
		r.add("003110900536");
		r.add("003110900537");
		r.add("003110900538");
		r.add("003110900539");
		r.add("003110900540");
		r.add("003110900541");
		r.add("003110900542");
		r.add("003110900544");
		r.add("003110900545");
		r.add("003110900546");
		r.add("003110900547");
		r.add("003110900548");
		r.add("003110900549");
		r.add("003110900550");
		r.add("003110900551");
		r.add("003110900552");
		r.add("003110900554");
		r.add("003110900555");
		r.add("003110900556");
		r.add("003110900557");
		r.add("003110900569");
		r.add("003110900581");
		r.add("003110900584");
		r.add("003110900586");
		r.add("003110900587");
		r.add("003110900589");
		r.add("003110900590");
		r.add("003110900591");
		r.add("003110900592");
		r.add("003110900593");
		r.add("003110900594");
		r.add("003110900595");
		r.add("003110900596");
		r.add("003110900597");
		r.add("003110900598");
		r.add("003110900599");
		r.add("003110900600");
		r.add("003110900601");
		r.add("003110900602");
		r.add("003110900603");
		r.add("003110900604");
		r.add("003110900605");
		r.add("003110900606");
		r.add("003110900607");
		r.add("003110900609");
		r.add("003110900611");
		r.add("003110900616");
		r.add("003110900617");
		r.add("003110900618");
		r.add("003110900619");
		r.add("003110900620");
		r.add("003110900622");
		r.add("003110900623");
		r.add("003110900624");
		r.add("003110900625");
		r.add("003110900626");
		r.add("003110900628");
		r.add("003110900629");
		r.add("003110900630");
		r.add("003110900631");
		r.add("003110900632");
		r.add("003110900633");
		r.add("003110900634");
		r.add("003110900635");
		r.add("003110900638");
		r.add("003110900639");
		r.add("003110900640");
		r.add("003110900641");
		r.add("003110900642");
		r.add("003110900643");
		r.add("003110900644");
		r.add("003110900645");
		r.add("003110900646");
		r.add("003110900647");
		r.add("003110900648");
		r.add("003110900649");
		r.add("003110900650");
		r.add("003110900651");
		r.add("003110900652");
		r.add("003110900653");
		r.add("003110900654");
		r.add("003110900655");
		r.add("003110900656");
		r.add("003110900657");
		r.add("003110900658");
		r.add("003110900659");
		r.add("003110900660");
		r.add("003110900661");
		r.add("003110900662");
		r.add("003110900663");
		r.add("003110900664");
		r.add("003110900665");
		r.add("003110900666");
		r.add("003110900667");
		r.add("003110900668");
		r.add("003110900669");
		r.add("003110900670");
		r.add("003110900671");
		r.add("003110900672");
		r.add("003110900673");
		r.add("003110900674");
		r.add("003110900675");
		r.add("003110900676");
		r.add("003110900677");
		r.add("003110900678");
		r.add("003110900679");
		r.add("003110900680");
		r.add("003110900681");
		r.add("003110900682");
		r.add("003110900683");
		r.add("003110900684");
		r.add("003110900685");
		r.add("003110900686");
		r.add("003110900688");
		r.add("003110900689");
		r.add("003110900690");
		r.add("003110900691");
		r.add("003110900692");
		r.add("003110900693");
		r.add("003110900694");
		r.add("003110900695");
		r.add("003110900696");
		r.add("003110900697");
		r.add("003110900698");
		r.add("003110900699");
		r.add("003110900700");
		r.add("003110900701");
		r.add("003110900703");
		r.add("003110900704");
		r.add("003110900706");
		r.add("003110900707");
		r.add("003110900708");
		r.add("003110900709");
		r.add("003110900710");
		r.add("003110900711");
		r.add("003110900712");
		r.add("003110900713");
		r.add("003110900714");
		r.add("003110900715");
		r.add("003110900716");
		r.add("003110900717");
		r.add("003110900718");
		r.add("003110900719");
		r.add("003110900720");
		r.add("003110900721");
		r.add("003110900722");
		r.add("003110900723");
		r.add("003110900724");
		r.add("003110900729");
		r.add("003110900730");
		r.add("003110900731");
		r.add("003110900732");
		r.add("003110900733");
		r.add("003110900734");
		r.add("003110900735");
		r.add("003110900736");
		r.add("003110900737");
		r.add("003110900738");
		r.add("003110900739");
		r.add("003110900740");
		r.add("003110900741");
		r.add("003110900742");
		r.add("003110900743");
		r.add("003110900744");
		r.add("003110900745");
		r.add("003110900746");
		r.add("003110900748");
		r.add("003110900749");
		r.add("003110900750");
		r.add("003110900751");
		r.add("003110900752");
		r.add("003110900753");
		r.add("003110900754");
		r.add("003110900755");
		r.add("003110900756");
		r.add("003110900757");
		r.add("003110900758");
		r.add("003110900759");
		r.add("003110900760");
		r.add("003110900761");
		r.add("003110900762");
		r.add("003110900763");
		r.add("003110900764");
		r.add("003110900765");
		r.add("003110900766");
		r.add("003110900767");
		r.add("003110900769");
		r.add("003110900770");
		r.add("003110900771");
		r.add("003110900772");
		r.add("003110900774");
		r.add("003110900776");
		r.add("003110900777");
		r.add("003110900778");
		r.add("003110900779");
		r.add("003110900780");
		r.add("003110900781");
		r.add("003110900782");
		r.add("003110900783");
		r.add("003110900784");
		r.add("003110900785");
		r.add("003110900786");
		r.add("003110900788");
		r.add("003110900790");
		r.add("003110900791");
		r.add("003110900792");
		r.add("003110900793");
		r.add("003110900794");
		r.add("003110900795");
		r.add("003110900796");
		r.add("003110900797");
		r.add("003110900798");
		r.add("003110900799");
		r.add("003110900800");
		r.add("003110900801");
		r.add("003110900802");
		r.add("003110900803");
		r.add("003110900804");
		r.add("003110900805");
		r.add("003110900806");
		r.add("003110900807");
		r.add("003110900808");
		r.add("003110900809");
		r.add("003110900810");
		r.add("003110900811");
		r.add("003110900812");
		r.add("003110900813");
		r.add("003110900814");
		r.add("003110900815");
		r.add("003110900816");
		r.add("003110900817");
		r.add("003110900818");
		r.add("003110900819");
		r.add("003110900820");
		r.add("003110900821");
		r.add("003110900822");
		r.add("003110900823");
		r.add("003110900824");
		r.add("003110900825");
		r.add("003110900826");
		r.add("003110900827");
		r.add("003110900828");
		r.add("003110900829");
		r.add("003110900830");
		r.add("003110900831");
		r.add("003110900832");
		r.add("003110900833");
		r.add("003110900834");
		r.add("003110900835");
		r.add("003110900836");
		r.add("003110900837");
		r.add("003110900838");
		r.add("003110900839");
		r.add("003110900840");
		r.add("003110900841");
		r.add("003110900842");
		r.add("003110900843");
		r.add("003110900844");
		r.add("003110900845");
		r.add("003110900847");
		r.add("003110900848");
		r.add("003110900849");
		r.add("003110900850");
		r.add("003110900851");
		r.add("003110900852");
		r.add("003110900853");
		r.add("003110900854");
		r.add("003110900856");
		r.add("003110900857");
		r.add("003110900858");
		r.add("003110900859");
		r.add("003110900860");
		r.add("003110900861");
		r.add("003110900862");
		r.add("003110900863");
		r.add("003110900864");
		r.add("003110900865");
		r.add("003110900866");
		r.add("003110900867");
		r.add("003110900868");
		r.add("003110900869");
		r.add("003110900870");
		r.add("003110900871");
		r.add("003110900872");
		r.add("003110900873");
		r.add("003110900874");
		r.add("003110900875");
		r.add("003110900876");
		r.add("003110900877");
		r.add("003110900878");
		r.add("003110900879");
		r.add("003110900880");
		r.add("003110900881");
		r.add("003110900882");
		r.add("003110900883");
		r.add("003110900885");
		r.add("003110900886");
		r.add("003110900887");
		r.add("003110900888");
		r.add("003110900889");
		r.add("003110900890");
		r.add("003110900891");
		r.add("003110900892");
		r.add("003110900893");
		r.add("003110900894");
		r.add("003110900895");
		r.add("003110900896");
		r.add("003110900897");
		r.add("003110900898");
		r.add("003110900899");
		r.add("003110900900");
		r.add("003110900901");
		r.add("003110900902");
		r.add("003110900903");
		r.add("003110900904");
		r.add("003110900905");
		r.add("003110900906");
		r.add("003110900907");
		r.add("003110900908");
		r.add("003110900909");
		r.add("003110900910");
		r.add("003110900911");
		r.add("003110900912");
		r.add("003110900913");
		r.add("003110900914");
		r.add("003110900915");
		r.add("003110900916");
		r.add("003110900917");
		r.add("003110900918");
		r.add("003110900919");
		r.add("003110900920");
		r.add("003110900921");
		r.add("003110900922");
		r.add("003110900923");
		r.add("003110900924");
		r.add("003110900925");
		r.add("003110900926");
		r.add("003110900927");
		r.add("003110900928");
		r.add("003110900929");
		r.add("003110900930");
		r.add("003110900931");
		r.add("003110900932");
		r.add("003110900933");
		r.add("003110900934");
		r.add("003110900935");
		r.add("003110900936");
		r.add("003110900937");
		r.add("003110900938");
		r.add("003110900939");
		r.add("003110900940");
		r.add("003110900941");
		r.add("003110900942");
		r.add("003110900944");
		r.add("003110900945");
		r.add("003110900946");
		r.add("003110900947");
		r.add("003110900948");
		r.add("003110900949");
		r.add("003110900950");
		r.add("003110900951");
		r.add("003110900952");
		r.add("003110900953");
		r.add("003110900954");
		r.add("003110900955");
		r.add("003110900956");
		r.add("003110900957");
		r.add("003110900958");
		r.add("003110900959");
		r.add("003110900960");
		r.add("003110900961");
		r.add("003110900962");
		r.add("003110900963");
		r.add("003110900964");
		r.add("003110900965");
		r.add("003110900966");
		r.add("003110900967");
		r.add("003110900968");
		r.add("003110900969");
		r.add("003110900970");
		r.add("003110900971");
		r.add("003110900972");
		r.add("003110900973");
		r.add("003110900974");
		r.add("003110900975");
		r.add("003110900976");
		r.add("003110900977");
		r.add("003110900978");
		r.add("003110900979");
		r.add("003110900980");
		r.add("003110900981");
		r.add("003110900982");
		r.add("003110900983");
		r.add("003110900984");
		r.add("003110900985");
		r.add("003110900986");
		r.add("003110900987");
		r.add("003110900988");
		r.add("003110900989");
		r.add("003110900990");
		r.add("003110900991");
		r.add("003110900992");
		r.add("003110900993");
		r.add("003110900994");
		r.add("003110900995");
		r.add("003110900996");
		r.add("003110900997");
		r.add("003110900998");
		r.add("003110900999");
		r.add("003110901000");
		r.add("003110901001");
		r.add("003110901002");
		r.add("003110901003");
		r.add("003110901004");
		r.add("003110901005");
		r.add("003110901007");
		r.add("003110901008");
		r.add("003110901011");
		r.add("003110901012");
		r.add("003110901013");
		r.add("003110901014");
		r.add("003110901015");
		r.add("003110901016");
		r.add("003110901017");
		r.add("003110901018");
		r.add("003110901019");
		r.add("003110901020");
		r.add("003110901021");
		r.add("003110901022");
		r.add("003110901025");
		r.add("003110901026");
		r.add("003110901027");
		r.add("003110901028");
		r.add("003110901030");
		r.add("003110901031");
		r.add("003110901032");
		r.add("003110901033");
		r.add("003110901034");
		r.add("003110901035");
		r.add("003110901036");
		r.add("003110901037");
		r.add("003110901038");
		r.add("003110901039");
		r.add("003110901040");
		r.add("003110901041");
		r.add("003110901042");
		r.add("003110901043");
		r.add("003110901045");
		r.add("003110901046");
		r.add("003110901047");
		r.add("003110901048");
		r.add("003110901049");
		r.add("003110901050");
		r.add("003110901051");
		r.add("003110901052");
		r.add("003110901053");
		r.add("003110901054");
		r.add("003110901055");
		r.add("003110901056");
		r.add("003110901057");
		r.add("003110901058");
		r.add("003110901060");
		r.add("003110901061");
		r.add("003110901062");
		r.add("003110901063");
		r.add("003110901064");
		r.add("003110901065");
		r.add("003110901066");
		r.add("003110901067");
		r.add("003110901068");
		r.add("003110901069");
		r.add("003110901070");
		r.add("003110901072");
		r.add("003110901073");
		r.add("003110901074");
		r.add("003110901075");
		r.add("003110901076");
		r.add("003110901077");
		r.add("003110901078");
		r.add("003110901079");
		r.add("003110901080");
		r.add("003110901081");
		r.add("003110901082");
		r.add("003110901083");
		r.add("003110901084");
		r.add("003110901085");
		r.add("003110901086");
		r.add("003110901087");
		r.add("003110901088");
		r.add("003110901089");
		r.add("003110901090");
		r.add("003110901091");
		r.add("003110901092");
		r.add("003110901093");
		r.add("003110901094");
		r.add("003110901095");
		r.add("003110901096");
		r.add("003110901097");
		r.add("003110901098");
		r.add("003110901099");
		r.add("003110901100");
		r.add("003110901101");
		r.add("003110901102");
		r.add("003110901103");
		r.add("003110901104");
		r.add("003110901105");
		r.add("003110901106");
		r.add("003110901107");
		r.add("003110901108");
		r.add("003110901110");
		r.add("003110901111");
		r.add("003110901112");
		r.add("003110901113");
		r.add("003110901114");
		r.add("003110901115");
		r.add("003110901116");
		r.add("003110901117");
		r.add("003110901118");
		r.add("003110901119");
		r.add("003110901120");
		r.add("003110901122");
		r.add("003110901123");
		r.add("003110901124");
		r.add("003110901125");
		r.add("003110901126");
		r.add("003110901127");
		r.add("003110901128");
		r.add("003110901129");
		r.add("003110901130");
		r.add("003110901131");
		r.add("003110901132");
		r.add("003110901133");
		r.add("003110901134");
		r.add("003110901135");
		r.add("003110901136");
		r.add("003110901138");
		r.add("003110901139");
		r.add("003110901140");
		r.add("003110901141");
		r.add("003110901142");
		r.add("003110901143");
		r.add("003110901144");
		r.add("003110901145");
		r.add("003110901146");
		r.add("003110901147");
		r.add("003110901149");
		r.add("003110901150");
		r.add("003110901151");
		r.add("003110901152");
		r.add("003110901153");
		r.add("003110901154");
		r.add("003110901155");
		r.add("003110901156");
		r.add("003110901157");
		r.add("003110901158");
		r.add("003110901159");
		r.add("003110901160");
		r.add("003110901161");
		r.add("003110901162");
		r.add("003110901163");
		r.add("003110901164");
		r.add("003110901165");
		r.add("003110901166");
		r.add("003110901167");
		r.add("003110901168");
		r.add("003110901169");
		r.add("003110901171");
		r.add("003110901172");
		r.add("003110901173");
		r.add("003110901174");
		r.add("003110901175");
		r.add("003110901176");
		r.add("003110901177");
		r.add("003110901178");
		r.add("003110901179");
		r.add("003110901180");
		r.add("003110901181");
		r.add("003110901182");
		r.add("003110901183");
		r.add("003110901184");
		r.add("003110901185");
		r.add("003110901186");
		r.add("003110901187");
		r.add("003110901188");
		r.add("003110901189");
		r.add("003110901190");
		r.add("003110901191");
		r.add("003110901192");
		r.add("003110901193");
		r.add("003110901194");
		r.add("003110901195");
		r.add("003110901197");
		r.add("003110901201");
		r.add("003110901204");
		r.add("003110901206");
		r.add("003110901208");
		r.add("003110901209");
		r.add("003110901212");
		r.add("003110901219");
		r.add("003110901221");
		r.add("003110901222");
		r.add("003110901223");
		r.add("003110901224");
		r.add("003110901226");
		r.add("003110901227");
		r.add("003110901228");
		r.add("003110901230");
		r.add("003110901234");
		r.add("003110901235");
		r.add("003110901238");
		r.add("003110901239");
		r.add("003110901244");
		r.add("003110901245");
		r.add("003110901247");
		r.add("003110901248");
		r.add("003110901250");
		r.add("003110901251");
		r.add("003110901253");
		r.add("003110901254");
		r.add("003110901255");
		r.add("003110901259");
		r.add("003110901260");
		r.add("003110901261");
		r.add("003110901262");
		r.add("003110901263");
		r.add("003110901264");
		r.add("003110901266");
		r.add("003110901268");
		r.add("003110901269");
		r.add("003110901271");
		r.add("003110901274");
		r.add("003110901276");
		r.add("003110901284");
		r.add("003110901285");
		r.add("003110901286");
		r.add("003110901287");
		r.add("003110901289");
		r.add("003110901290");
		r.add("003110901291");
		r.add("003110901294");
		r.add("003110901295");
		r.add("003110901296");
		r.add("003110901297");
		r.add("003110901299");
		r.add("003110901301");
		r.add("003110901303");
		r.add("003110901305");
		r.add("003110901306");
		r.add("003110901308");
		r.add("003110901309");
		r.add("003110901312");
		r.add("003110901316");
		r.add("003110901318");
		r.add("003110901319");
		r.add("003110901320");
		r.add("003110901321");
		r.add("003110901322");
		r.add("003110901323");
		r.add("003110901324");
		r.add("003110901326");
		r.add("003110901328");
		r.add("003110901330");
		r.add("003110901331");
		r.add("003110901332");
		r.add("003110901334");
		r.add("003110901336");
		r.add("003110901337");
		r.add("003110901338");
		r.add("003110901340");
		r.add("003110901341");
		r.add("003110901342");
		r.add("003110901344");
		r.add("003110901345");
		r.add("003110901347");
		r.add("003110901350");
		r.add("003110901353");
		r.add("003110901355");
		r.add("003110901359");
		r.add("003110901362");
		r.add("003110901363");
		r.add("003110901364");
		r.add("003110901367");
		r.add("003110901369");
		r.add("003110901370");
		r.add("003110901371");
		r.add("003110901372");
		r.add("003110901374");
		r.add("003110901378");
		r.add("003110901380");
		r.add("003110901391");
		r.add("003110901393");
		r.add("003110901396");
		r.add("003110901397");
		r.add("003110901398");
		r.add("003110901399");
		r.add("003110901403");
		r.add("003110901405");
		r.add("003110901407");
		r.add("003110901408");
		r.add("003110901414");
		r.add("003110901418");
		r.add("003110901423");
		r.add("003110901424");
		r.add("003110901427");
		r.add("003110901430");
		r.add("003110901433");
		r.add("003110901435");
		r.add("003110901436");
		r.add("003110901440");
		r.add("003110901442");
		r.add("003110901448");
		r.add("003110901647");
		r.add("003110901648");
		r.add("003110901649");
		r.add("003110901650");
		r.add("003110901651");
		r.add("003110901652");
		r.add("003110901655");
		r.add("003110901656");
		r.add("003110901657");
		r.add("003110901659");
		r.add("003110901660");
		r.add("003110901662");
		r.add("003110901663");
		r.add("003110901802");
		r.add("003110901826");
		r.add("003110901834");
		r.add("003110901836");
		r.add("003110901839");
		r.add("003110901843");
		r.add("003110901846");
		r.add("003110901847");
		r.add("003110901877");
		r.add("003110901887");
		r.add("003110901889");
		r.add("003110901891");
		r.add("003110901892");
		r.add("003110901898");
		r.add("003110901900");
		r.add("003110901969");
		r.add("003110901970");
		r.add("003110901971");
		r.add("003110901978");
		r.add("003110901980");
		r.add("003110901984");
		r.add("003110901992");
		r.add("003110901994");
		r.add("003110901999");
		r.add("003110902005");
		r.add("003110902009");
		r.add("003110902010");
		r.add("003110902011");
		r.add("003110902012");
		r.add("003110902013");
		r.add("003110902014");
		r.add("003110902015");
		r.add("003110902042");
		r.add("003110902049");
		r.add("003110902056");
		r.add("003110902058");
		r.add("003110902063");
		r.add("003110902064");
		r.add("003110902065");
		r.add("003110902067");
		r.add("004110900325");
		r.add("004110900358");
		r.add("004110900407");
		r.add("004110900413");
		r.add("004110900416");
		r.add("004110900422");
		r.add("004110900428");
		r.add("004110900435");
		r.add("004110900446");
		r.add("004110900450");
		r.add("004110900458");
		r.add("004110900461");
		r.add("004110900472");
		r.add("004110900483");
		r.add("004110900493");
		r.add("004110900498");
		r.add("004110900513");
		r.add("004110900518");
		r.add("004110900519");
		r.add("004110900521");
		r.add("004110900524");
		r.add("004110900526");
		r.add("004110900529");
		r.add("004110900531");
		r.add("004110900532");
		r.add("004110900538");
		r.add("005110900119");
		r.add("005110900141");
		r.add("005110900156");
		r.add("005110900184");
		r.add("005110900185");
		r.add("005110900189");
		r.add("005110900192");
		r.add("005110900195");
		r.add("005110900200");
		r.add("005110900203");
		r.add("005110900204");
		r.add("005110900206");
		r.add("005110900208");
		r.add("005110900229");
		r.add("005110900234");
		r.add("005110900236");
		r.add("005110900247");
		r.add("005110900250");
		r.add("005110900251");
		r.add("005110900255");
		r.add("005110900256");
		r.add("005110900261");
		r.add("005110900263");
		r.add("005110900266");
		r.add("005110900273");
		r.add("005110900276");
		r.add("005110900278");
		r.add("005110900279");
		r.add("005110900280");
		r.add("005110900281");
		r.add("005110900282");
		r.add("005110900283");
		r.add("005110900285");
		r.add("005110900286");
		r.add("005110900287");
		r.add("005110900288");
		r.add("005110900289");
		r.add("005110900291");
		r.add("005110900292");
		r.add("005110900293");
		r.add("005110900295");
		r.add("005110900297");
		r.add("005110900298");
		r.add("005110900299");
		r.add("005110900300");
		r.add("005110900302");
		r.add("005110900304");
		r.add("005110900305");
		r.add("005110900306");
		r.add("005110900307");
		r.add("005110900309");
		r.add("005110900310");
		r.add("005110900313");
		r.add("005110900314");
		r.add("005110900322");
		r.add("005110900324");
		r.add("005110900325");
		r.add("005110900326");
		r.add("005110900327");
		r.add("005110900334");
		r.add("005110900335");
		r.add("005110900336");
		r.add("005110900337");
		r.add("005110900338");
		r.add("005110900339");
		r.add("005110900347");
		r.add("005110900348");
		r.add("005110900349");
		r.add("005110900364");
		r.add("005110900369");
		r.add("005110900375");
		r.add("005110900376");
		r.add("005110900379");
		r.add("005110900395");
		r.add("005110900410");
		r.add("005110900414");
		r.add("006110900166");
		r.add("006110900202");
		r.add("006110900218");
		r.add("006110900221");
		r.add("006110900223");
		r.add("006110900228");
		r.add("006110900230");
		r.add("006110900231");
		r.add("006110900232");
		r.add("006110900237");
		r.add("006110900238");
		r.add("006110900239");
		r.add("006110900249");
		r.add("006110900254");
		r.add("006110900266");
		r.add("006110900270");
		r.add("006110900271");
		r.add("006110900272");
		r.add("006110900273");
		r.add("006110900274");
		r.add("006110900275");
		r.add("006110900283");
		r.add("006110900286");
		r.add("006110900288");
		r.add("006110900289");
		r.add("006110900291");
		r.add("006110900294");
		r.add("006110900298");
		r.add("006110900299");
		r.add("006110900300");
		r.add("006110900302");
		r.add("006110900303");
		r.add("006110900308");
		r.add("006110900309");
		r.add("006110900310");
		r.add("006110900312");
		r.add("006110900313");
		r.add("006110900314");
		r.add("006110900315");
		r.add("006110900316");
		r.add("006110900317");
		r.add("006110900318");
		r.add("006110900319");
		r.add("006110900351");
		r.add("006110900352");
		r.add("006110900354");
		r.add("006110900355");
		r.add("006110900356");
		r.add("006110900361");
		r.add("006110900362");
		r.add("006110900369");
		r.add("006110900372");
		r.add("006110900374");
		r.add("006110900393");
		r.add("006110900394");
		r.add("006110900466");
		r.add("006110900521");
		r.add("007110900440");
		r.add("007110900608");
		r.add("007110900613");
		r.add("008110900153");
		r.add("008110900223");
		r.add("008110900224");
		r.add("008110900261");
		r.add("008110900265");
		r.add("008110900279");
		r.add("008110900280");
		r.add("008110900284");
		r.add("008110900289");
		r.add("008110900290");
		r.add("008110900291");
		r.add("008110900293");
		r.add("008110900295");
		r.add("008110900296");
		r.add("008110900298");
		r.add("008110900302");
		r.add("008110900303");
		r.add("008110900304");
		r.add("008110900305");
		r.add("008110900306");
		r.add("008110900308");
		r.add("008110900311");
		r.add("008110900316");
		r.add("008110900317");
		r.add("008110900318");
		r.add("008110900319");
		r.add("008110900320");
		r.add("008110900321");
		r.add("008110900322");
		r.add("008110900327");
		r.add("008110900329");
		r.add("008110900339");
		r.add("008110900341");
		r.add("008110900342");
		r.add("008110900349");
		r.add("008110900352");
		r.add("008110900355");
		r.add("008110900367");
		r.add("008110900373");
		r.add("008110900376");
		r.add("008110900405");
		r.add("008110900406");
		r.add("008110900407");
		r.add("008110900408");
		r.add("008110900409");
		r.add("008110900410");
		r.add("008110900411");
		r.add("008110900419");
		r.add("008110900420");
		r.add("008110900421");
		r.add("008110900422");
		r.add("008110900425");
		r.add("008110900426");
		r.add("008110900427");
		r.add("008110900431");
		r.add("008110900432");
		r.add("008110900433");
		r.add("008110900442");
		r.add("008110900443");
		r.add("008110900445");
		r.add("008110900453");
		r.add("008110900454");
		r.add("008110900455");
		r.add("008110900457");
		r.add("008110900458");
		r.add("008110900462");
		r.add("008110900463");
		r.add("008110900464");
		r.add("008110900465");
		r.add("008110900475");
		r.add("008110900478");
		r.add("008110900479");
		r.add("008110900481");
		r.add("008110900484");
		r.add("008110900487");
		r.add("008110900491");
		r.add("008110900492");
		r.add("008110900497");
		r.add("008110900498");
		r.add("008110900500");
		r.add("008110900502");
		r.add("008110900506");
		r.add("008110900507");
		r.add("008110900508");
		r.add("008110900509");
		r.add("008110900510");
		r.add("008110900514");
		r.add("008110900515");
		r.add("008110900516");
		r.add("008110900524");
		r.add("008110900525");
		r.add("008110900539");
		r.add("008110900544");
		r.add("008110900549");
		r.add("008110900599");
		r.add("008110900600");
		r.add("008110900607");
		r.add("008110900619");
		r.add("010110900250");
		r.add("010110900252");
		r.add("010110900255");
		r.add("010110900261");
		r.add("010110900267");
		r.add("010110900268");
		r.add("010110900269");
		r.add("010110900270");
		r.add("010110900272");
		r.add("010110900273");
		r.add("010110900276");
		r.add("010110900278");
		r.add("010110900280");
		r.add("010110900281");
		r.add("010110900282");
		r.add("010110900283");
		r.add("010110900284");
		r.add("010110900286");
		r.add("010110900287");
		r.add("010110900288");
		r.add("010110900290");
		r.add("010110900291");
		r.add("010110900298");
		r.add("010110900299");
		r.add("010110900303");
		r.add("010110900305");
		r.add("010110900307");
		r.add("010110900308");
		r.add("010110900310");
		r.add("010110900311");
		r.add("010110900312");
		r.add("010110900314");
		r.add("010110900315");
		r.add("010110900317");
		r.add("010110900318");
		r.add("010110900321");
		r.add("010110900328");
		r.add("010110900332");
		r.add("010110900336");
		r.add("010110900337");
		r.add("010110900338");
		r.add("010110900339");
		r.add("010110900340");
		r.add("010110900341");
		r.add("010110900344");
		r.add("010110900345");
		r.add("010110900348");
		r.add("010110900350");
		r.add("010110900351");
		r.add("010110900352");
		r.add("010110900354");
		r.add("010110900355");
		r.add("010110900356");
		r.add("010110900358");
		r.add("010110900361");
		r.add("010110900363");
		r.add("010110900364");
		r.add("010110900365");
		r.add("010110900368");
		r.add("010110900370");
		r.add("010110900372");
		r.add("010110900373");
		r.add("010110900374");
		r.add("010110900375");
		r.add("010110900376");
		r.add("010110900377");
		r.add("010110900378");
		r.add("010110900382");
		r.add("010110900388");
		r.add("010110900390");
		r.add("010110900394");
		r.add("010110900395");
		r.add("010110900400");
		r.add("010110900408");
		r.add("010110900410");
		r.add("010110900411");
		r.add("010110900415");
		r.add("010110900424");
		r.add("010110900443");
		r.add("010110900453");
		r.add("010110900461");
		r.add("010110900477");
		r.add("010110900483");
		r.add("010110900509");
		r.add("010110900510");
		r.add("010110900517");
		r.add("012110900189");
		r.add("012110900199");
		r.add("012110900207");
		r.add("012110900261");
		r.add("012110900267");
		r.add("012110900285");
		r.add("012110900289");
		r.add("012110900291");
		r.add("012110900306");
		r.add("012110900314");
		r.add("012110900323");
		r.add("012110900327");
		r.add("012110900331");
		r.add("012110900332");
		r.add("012110900338");
		r.add("012110900343");
		r.add("012110900344");
		r.add("012110900348");
		r.add("012110900350");
		r.add("012110900354");
		r.add("012110900355");
		r.add("012110900357");
		r.add("012110900358");
		r.add("012110900363");
		r.add("012110900364");
		r.add("012110900365");
		r.add("012110900372");
		r.add("012110900374");
		r.add("012110900386");
		r.add("012110900402");
		r.add("012110900430");
		r.add("012110900446");
		r.add("012110900469");
		r.add("013110900095");
		r.add("013110900142");
		r.add("013110900143");
		r.add("013110900151");
		r.add("013110900152");
		r.add("013110900160");
		r.add("013110900164");
		r.add("013110900178");
		r.add("013110900185");
		r.add("013110900186");
		r.add("013110900187");
		r.add("013110900188");
		r.add("013110900190");
		r.add("013110900192");
		r.add("013110900194");
		r.add("013110900200");
		r.add("013110900203");
		r.add("013110900206");
		r.add("013110900209");
		r.add("013110900210");
		r.add("013110900211");
		r.add("013110900214");
		r.add("013110900218");
		r.add("013110900220");
		r.add("013110900221");
		r.add("013110900222");
		r.add("013110900225");
		r.add("013110900227");
		r.add("013110900228");
		r.add("013110900231");
		r.add("013110900232");
		r.add("013110900238");
		r.add("013110900243");
		r.add("013110900244");
		r.add("013110900245");
		r.add("013110900253");
		r.add("013110900263");
		r.add("013110900270");
		r.add("013110900277");
		r.add("013110900279");
		r.add("013110900289");
		r.add("013110900299");
		r.add("014110900196");
		r.add("014110900219");
		r.add("014110900234");
		r.add("014110900251");
		r.add("014110900256");
		r.add("014110900277");
		r.add("014110900283");
		r.add("014110900285");
		r.add("014110900303");
		r.add("014110900310");
		r.add("014110900311");
		r.add("014110900314");
		r.add("014110900315");
		r.add("014110900316");
		r.add("014110900320");
		r.add("014110900321");
		r.add("014110900322");
		r.add("014110900323");
		r.add("014110900324");
		r.add("014110900325");
		r.add("014110900327");
		r.add("014110900328");
		r.add("014110900330");
		r.add("014110900331");
		r.add("014110900332");
		r.add("014110900333");
		r.add("014110900334");
		r.add("014110900335");
		r.add("014110900336");
		r.add("014110900337");
		r.add("014110900338");
		r.add("014110900341");
		r.add("014110900344");
		r.add("014110900345");
		r.add("014110900347");
		r.add("014110900349");
		r.add("014110900350");
		r.add("014110900351");
		r.add("014110900352");
		r.add("014110900353");
		r.add("014110900355");
		r.add("014110900356");
		r.add("014110900359");
		r.add("014110900360");
		r.add("014110900361");
		r.add("014110900362");
		r.add("014110900364");
		r.add("014110900366");
		r.add("014110900369");
		r.add("014110900370");
		r.add("014110900371");
		r.add("014110900372");
		r.add("014110900374");
		r.add("014110900376");
		r.add("014110900377");
		r.add("014110900379");
		r.add("014110900380");
		r.add("014110900381");
		r.add("014110900382");
		r.add("014110900383");
		r.add("014110900384");
		r.add("014110900385");
		r.add("014110900386");
		r.add("014110900388");
		r.add("014110900389");
		r.add("014110900390");
		r.add("014110900392");
		r.add("014110900393");
		r.add("014110900394");
		r.add("014110900395");
		r.add("014110900396");
		r.add("014110900397");
		r.add("014110900398");
		r.add("014110900400");
		r.add("014110900405");
		r.add("014110900406");
		r.add("014110900407");
		r.add("014110900408");
		r.add("014110900409");
		r.add("014110900410");
		r.add("014110900411");
		r.add("014110900412");
		r.add("014110900415");
		r.add("014110900416");
		r.add("014110900417");
		r.add("014110900418");
		r.add("014110900419");
		r.add("014110900421");
		r.add("014110900422");
		r.add("014110900423");
		r.add("014110900424");
		r.add("014110900425");
		r.add("014110900426");
		r.add("014110900427");
		r.add("014110900428");
		r.add("014110900429");
		r.add("014110900430");
		r.add("014110900431");
		r.add("014110900433");
		r.add("014110900435");
		r.add("014110900436");
		r.add("014110900437");
		r.add("014110900438");
		r.add("014110900439");
		r.add("014110900440");
		r.add("014110900442");
		r.add("014110900443");
		r.add("014110900444");
		r.add("014110900445");
		r.add("014110900446");
		r.add("014110900447");
		r.add("014110900450");
		r.add("014110900451");
		r.add("014110900453");
		r.add("014110900454");
		r.add("014110900455");
		r.add("014110900456");
		r.add("014110900457");
		r.add("014110900458");
		r.add("014110900460");
		r.add("014110900463");
		r.add("014110900464");
		r.add("014110900465");
		r.add("014110900467");
		r.add("014110900468");
		r.add("014110900481");
		r.add("014110900482");
		r.add("014110900483");
		r.add("014110900485");
		r.add("014110900486");
		r.add("014110900489");
		r.add("014110900490");
		r.add("014110900491");
		r.add("014110900493");
		r.add("014110900495");
		r.add("014110900510");
		r.add("014110900515");
		r.add("014110900517");
		r.add("014110900519");
		r.add("014110900522");
		r.add("014110900525");
		r.add("014110900527");
		r.add("014110900532");
		r.add("014110900547");
		r.add("014110900560");
		r.add("014110900570");
		r.add("014110900576");
		r.add("014110900578");
		r.add("014110900602");
		r.add("014110900632");
		r.add("014110900634");
		r.add("014110900637");
		r.add("014110900638");
		r.add("014110900642");
		r.add("014110900657");
		r.add("014110900671");
		r.add("014110900690");
		r.add("014110900691");
		r.add("014110900694");
		r.add("014110900702");
		r.add("015110900194");
		r.add("015110900231");
		r.add("015110900249");
		r.add("015110900279");
		r.add("015110900289");
		r.add("015110900304");
		r.add("015110900307");
		r.add("015110900309");
		r.add("015110900310");
		r.add("015110900313");
		r.add("015110900315");
		r.add("015110900316");
		r.add("015110900317");
		r.add("015110900318");
		r.add("015110900320");
		r.add("015110900321");
		r.add("015110900331");
		r.add("015110900333");
		r.add("015110900334");
		r.add("015110900344");
		r.add("015110900346");
		r.add("015110900352");
		r.add("015110900357");
		r.add("015110900362");
		r.add("015110900363");
		r.add("015110900366");
		r.add("015110900367");
		r.add("015110900375");
		r.add("015110900376");
		r.add("015110900378");
		r.add("015110900382");
		r.add("015110900395");
		r.add("015110900400");
		r.add("015110900406");
		r.add("015110900418");
		r.add("015110900458");
		r.add("015110900461");
		r.add("015110900470");
		r.add("015110900472");
		r.add("015110900487");
		r.add("016110900141");
		r.add("016110900142");
		r.add("016110900162");
		r.add("016110900163");
		r.add("016110900169");
		r.add("016110900174");
		r.add("016110900176");
		r.add("016110900178");
		r.add("016110900179");
		r.add("016110900185");
		r.add("016110900198");
		r.add("016110900199");
		r.add("016110900213");
		r.add("016110900223");
		r.add("016110900224");
		r.add("016110900226");
		r.add("016110900244");
		r.add("016110900247");
		r.add("016110900248");
		r.add("016110900258");
		r.add("016110900262");
		r.add("016110900265");
		r.add("016110900267");
		r.add("016110900268");
		r.add("016110900271");
		r.add("016110900274");
		r.add("016110900276");
		r.add("016110900278");
		r.add("016110900279");
		r.add("016110900280");
		r.add("016110900283");
		r.add("016110900284");
		r.add("016110900285");
		r.add("016110900289");
		r.add("016110900291");
		r.add("016110900293");
		r.add("016110900295");
		r.add("016110900296");
		r.add("016110900297");
		r.add("016110900298");
		r.add("016110900299");
		r.add("016110900301");
		r.add("016110900302");
		r.add("016110900305");
		r.add("016110900307");
		r.add("016110900309");
		r.add("016110900314");
		r.add("016110900315");
		r.add("016110900316");
		r.add("016110900317");
		r.add("016110900319");
		r.add("016110900322");
		r.add("016110900323");
		r.add("016110900324");
		r.add("016110900325");
		r.add("016110900327");
		r.add("016110900329");
		r.add("016110900330");
		r.add("016110900332");
		r.add("016110900334");
		r.add("016110900335");
		r.add("016110900336");
		r.add("016110900338");
		r.add("016110900339");
		r.add("016110900342");
		r.add("016110900345");
		r.add("016110900347");
		r.add("016110900348");
		r.add("016110900354");
		r.add("016110900363");
		r.add("016110900364");
		r.add("016110900365");
		r.add("016110900367");
		r.add("016110900368");
		r.add("016110900369");
		r.add("016110900372");
		r.add("016110900373");
		r.add("016110900375");
		r.add("016110900376");
		r.add("016110900377");
		r.add("016110900380");
		r.add("016110900382");
		r.add("016110900383");
		r.add("016110900385");
		r.add("016110900391");
		r.add("016110900393");
		r.add("016110900396");
		r.add("016110900399");
		r.add("016110900401");
		r.add("016110900402");
		r.add("016110900403");
		r.add("016110900404");
		r.add("016110900406");
		r.add("016110900408");
		r.add("016110900409");
		r.add("016110900410");
		r.add("016110900411");
		r.add("016110900412");
		r.add("016110900415");
		r.add("016110900416");
		r.add("016110900417");
		r.add("016110900420");
		r.add("016110900421");
		r.add("016110900423");
		r.add("016110900424");
		r.add("016110900426");
		r.add("016110900428");
		r.add("016110900430");
		r.add("016110900432");
		r.add("016110900433");
		r.add("016110900435");
		r.add("016110900436");
		r.add("016110900437");
		r.add("016110900439");
		r.add("016110900440");
		r.add("016110900444");
		r.add("016110900445");
		r.add("016110900446");
		r.add("016110900447");
		r.add("016110900448");
		r.add("016110900452");
		r.add("016110900455");
		r.add("016110900456");
		r.add("016110900459");
		r.add("016110900460");
		r.add("016110900462");
		r.add("016110900463");
		r.add("016110900464");
		r.add("016110900466");
		r.add("016110900467");
		r.add("016110900468");
		r.add("016110900475");
		r.add("016110900476");
		r.add("016110900553");
		r.add("016110900563");
		r.add("016110900575");
		r.add("016110900586");
		r.add("017110900078");
		r.add("017110900092");
		r.add("017110900103");
		r.add("017110900104");
		r.add("017110900106");
		r.add("017110900108");
		r.add("017110900112");
		r.add("017110900113");
		r.add("017110900114");
		r.add("017110900115");
		r.add("017110900116");
		r.add("017110900119");
		r.add("017110900122");
		r.add("017110900125");
		r.add("017110900126");
		r.add("017110900132");
		r.add("017110900133");
		r.add("017110900134");
		r.add("017110900136");
		r.add("017110900137");
		r.add("017110900138");
		r.add("017110900139");
		r.add("017110900140");
		r.add("017110900141");
		r.add("017110900145");
		r.add("017110900146");
		r.add("017110900148");
		r.add("017110900154");
		r.add("017110900155");
		r.add("017110900156");
		r.add("017110900157");
		r.add("017110900158");
		r.add("017110900163");
		r.add("017110900168");
		r.add("017110900169");
		r.add("017110900170");
		r.add("017110900173");
		r.add("017110900174");
		r.add("017110900175");
		r.add("017110900176");
		r.add("017110900177");
		r.add("017110900178");
		r.add("017110900179");
		r.add("017110900181");
		r.add("017110900183");
		r.add("017110900185");
		r.add("017110900186");
		r.add("017110900187");
		r.add("017110900188");
		r.add("017110900189");
		r.add("017110900190");
		r.add("017110900191");
		r.add("017110900192");
		r.add("017110900194");
		r.add("017110900195");
		r.add("017110900196");
		r.add("017110900197");
		r.add("017110900198");
		r.add("017110900199");
		r.add("017110900200");
		r.add("017110900201");
		r.add("017110900203");
		r.add("017110900204");
		r.add("017110900205");
		r.add("017110900206");
		r.add("017110900207");
		r.add("017110900209");
		r.add("017110900210");
		r.add("017110900211");
		r.add("017110900216");
		r.add("017110900217");
		r.add("017110900222");
		r.add("017110900225");
		r.add("017110900228");
		r.add("017110900232");
		r.add("017110900258");
		r.add("017110900260");
		r.add("017110900276");
		r.add("017110900285");
		r.add("018110900154");
		r.add("018110900190");
		r.add("018110900197");
		r.add("018110900211");
		r.add("018110900213");
		r.add("018110900230");
		r.add("018110900239");
		r.add("018110900243");
		r.add("018110900245");
		r.add("018110900248");
		r.add("018110900249");
		r.add("018110900256");
		r.add("018110900261");
		r.add("018110900266");
		r.add("018110900267");
		r.add("018110900275");
		r.add("018110900279");
		r.add("018110900292");
		r.add("018110900293");
		r.add("018110900296");
		r.add("018110900298");
		r.add("018110900305");
		r.add("018110900306");
		r.add("018110900311");
		r.add("018110900315");
		r.add("018110900322");
		r.add("018110900327");
		r.add("018110900334");
		r.add("018110900335");
		r.add("018110900340");
		r.add("018110900344");
		r.add("018110900346");
		r.add("018110900350");
		r.add("018110900359");
		r.add("018110900361");
		r.add("018110900362");
		r.add("018110900378");
		r.add("018110900412");
		r.add("018110900439");
		r.add("019110900159");
		r.add("019110900161");
		r.add("019110900163");
		r.add("019110900164");
		r.add("019110900165");
		r.add("019110900166");
		r.add("019110900169");
		r.add("019110900191");
		r.add("019110900198");
		r.add("019110900201");
		r.add("019110900202");
		r.add("019110900205");
		r.add("019110900207");
		r.add("019110900209");
		r.add("019110900223");
		r.add("019110900226");
		r.add("019110900227");
		r.add("019110900229");
		r.add("019110900230");
		r.add("019110900233");
		r.add("019110900234");
		r.add("019110900236");
		r.add("019110900239");
		r.add("019110900240");
		r.add("019110900241");
		r.add("019110900242");
		r.add("019110900243");
		r.add("019110900245");
		r.add("019110900246");
		r.add("019110900247");
		r.add("019110900248");
		r.add("019110900249");
		r.add("019110900252");
		r.add("019110900253");
		r.add("019110900254");
		r.add("019110900258");
		r.add("019110900259");
		r.add("019110900260");
		r.add("019110900262");
		r.add("019110900263");
		r.add("019110900264");
		r.add("019110900265");
		r.add("019110900267");
		r.add("019110900271");
		r.add("019110900273");
		r.add("019110900276");
		r.add("019110900286");
		r.add("019110900289");
		r.add("019110900290");
		r.add("019110900317");
		r.add("019110900320");
		r.add("019110900329");
		r.add("019110900331");
		r.add("020110900166");
		r.add("020110900178");
		r.add("020110900201");
		r.add("020110900202");
		r.add("020110900214");
		r.add("020110900221");
		r.add("020110900226");
		r.add("020110900264");
		r.add("020110900265");
		r.add("020110900271");
		r.add("020110900286");
		r.add("020110900288");
		r.add("020110900289");
		r.add("020110900290");
		r.add("020110900291");
		r.add("020110900294");
		r.add("020110900298");
		r.add("020110900299");
		r.add("020110900304");
		r.add("020110900306");
		r.add("020110900307");
		r.add("020110900309");
		r.add("020110900313");
		r.add("020110900314");
		r.add("020110900315");
		r.add("020110900316");
		r.add("020110900317");
		r.add("020110900322");
		r.add("020110900327");
		r.add("020110900329");
		r.add("020110900332");
		r.add("020110900333");
		r.add("020110900334");
		r.add("020110900335");
		r.add("020110900340");
		r.add("020110900341");
		r.add("020110900342");
		r.add("020110900343");
		r.add("020110900345");
		r.add("020110900346");
		r.add("020110900347");
		r.add("020110900348");
		r.add("020110900349");
		r.add("020110900354");
		r.add("020110900355");
		r.add("020110900357");
		r.add("020110900361");
		r.add("020110900362");
		r.add("020110900363");
		r.add("020110900365");
		r.add("020110900366");
		r.add("020110900373");
		r.add("020110900375");
		r.add("020110900377");
		r.add("020110900378");
		r.add("020110900380");
		r.add("020110900381");
		r.add("020110900383");
		r.add("020110900387");
		r.add("020110900389");
		r.add("020110900390");
		r.add("020110900391");
		r.add("020110900392");
		r.add("020110900396");
		r.add("020110900397");
		r.add("020110900398");
		r.add("020110900402");
		r.add("020110900407");
		r.add("020110900410");
		r.add("020110900411");
		r.add("020110900415");
		r.add("020110900416");
		r.add("020110900425");
		r.add("020110900426");
		r.add("020110900428");
		r.add("020110900429");
		r.add("020110900439");
		r.add("020110900440");
		r.add("020110900443");
		r.add("020110900444");
		r.add("020110900446");
		r.add("020110900447");
		r.add("020110900448");
		r.add("020110900449");
		r.add("020110900453");
		r.add("020110900454");
		r.add("020110900456");
		r.add("020110900457");
		r.add("020110900459");
		r.add("020110900461");
		r.add("020110900462");
		r.add("020110900463");
		r.add("020110900464");
		r.add("020110900467");
		r.add("020110900472");
		r.add("020110900473");
		r.add("020110900475");
		r.add("020110900482");
		r.add("020110900483");
		r.add("020110900511");
		r.add("020110900512");
		r.add("020110900513");
		r.add("020110900530");
		r.add("020110900536");
		r.add("020110900537");
		r.add("020110900567");
		r.add("020110900610");
		r.add("020110900647");
		r.add("020110900679");
		r.add("021110900097");
		r.add("021110900122");
		r.add("021110900124");
		r.add("021110900130");
		r.add("021110900141");
		r.add("021110900142");
		r.add("021110900150");
		r.add("021110900151");
		r.add("021110900152");
		r.add("021110900154");
		r.add("021110900157");
		r.add("021110900159");
		r.add("021110900160");
		r.add("021110900163");
		r.add("021110900166");
		r.add("021110900168");
		r.add("021110900175");
		r.add("021110900177");
		r.add("021110900179");
		r.add("021110900181");
		r.add("021110900184");
		r.add("021110900200");
		r.add("021110900201");
		r.add("021110900208");
		r.add("021110900212");
		r.add("021110900219");
		r.add("021110900221");
		r.add("021110900228");
		r.add("021110900229");
		r.add("021110900231");
		r.add("021110900248");
		r.add("021110900290");
		r.add("021110900291");
		r.add("022110900128");
		r.add("022110900130");
		r.add("022110900143");
		r.add("022110900158");
		r.add("022110900166");
		r.add("022110900167");
		r.add("022110900168");
		r.add("022110900171");
		r.add("022110900176");
		r.add("022110900178");
		r.add("022110900184");
		r.add("022110900188");
		r.add("022110900189");
		r.add("022110900190");
		r.add("022110900199");
		r.add("022110900201");
		r.add("022110900204");
		r.add("022110900205");
		r.add("022110900207");
		r.add("022110900209");
		r.add("022110900215");
		r.add("022110900217");
		r.add("022110900218");
		r.add("022110900219");
		r.add("022110900221");
		r.add("022110900223");
		r.add("022110900224");
		r.add("022110900225");
		r.add("022110900226");
		r.add("022110900227");
		r.add("022110900228");
		r.add("022110900231");
		r.add("022110900233");
		r.add("022110900234");
		r.add("022110900235");
		r.add("022110900237");
		r.add("022110900239");
		r.add("022110900240");
		r.add("022110900241");
		r.add("022110900242");
		r.add("022110900244");
		r.add("022110900245");
		r.add("022110900249");
		r.add("022110900251");
		r.add("022110900252");
		r.add("022110900253");
		r.add("022110900254");
		r.add("022110900255");
		r.add("022110900256");
		r.add("022110900257");
		r.add("022110900259");
		r.add("022110900260");
		r.add("022110900261");
		r.add("022110900262");
		r.add("022110900263");
		r.add("022110900264");
		r.add("022110900267");
		r.add("022110900268");
		r.add("022110900269");
		r.add("022110900270");
		r.add("022110900271");
		r.add("022110900272");
		r.add("022110900275");
		r.add("022110900276");
		r.add("022110900279");
		r.add("022110900280");
		r.add("022110900281");
		r.add("022110900283");
		r.add("022110900287");
		r.add("022110900288");
		r.add("022110900294");
		r.add("022110900302");
		r.add("022110900303");
		r.add("022110900304");
		r.add("022110900307");
		r.add("022110900309");
		r.add("022110900310");
		r.add("022110900311");
		r.add("022110900312");
		r.add("022110900317");
		r.add("022110900329");
		r.add("022110900330");
		r.add("022110900332");
		r.add("022110900339");
		r.add("022110900341");
		r.add("022110900356");
		r.add("022110900373");
		r.add("022110900436");
		r.add("023110900182");
		r.add("023110900186");
		r.add("023110900189");
		r.add("023110900207");
		r.add("023110900229");
		r.add("023110900231");
		r.add("023110900243");
		r.add("023110900244");
		r.add("023110900260");
		r.add("023110900261");
		r.add("023110900269");
		r.add("023110900270");
		r.add("023110900271");
		r.add("023110900294");
		r.add("026110900176");
		r.add("026110900212");
		r.add("026110900229");
		r.add("026110900239");
		r.add("026110900249");
		r.add("026110900250");
		r.add("026110900251");
		r.add("026110900252");
		r.add("026110900255");
		r.add("026110900256");
		r.add("026110900267");
		r.add("026110900268");
		r.add("026110900288");
		r.add("026110900292");
		r.add("026110900320");
		r.add("026110900323");
		r.add("026110900324");
		r.add("026110900327");
		r.add("026110900328");
		r.add("026110900329");
		r.add("026110900337");
		r.add("026110900361");
		r.add("026110900370");
		r.add("026110900371");
		r.add("026110900373");
		r.add("026110900375");
		r.add("026110900377");
		r.add("026110900380");
		r.add("026110900382");
		r.add("026110900385");
		r.add("026110900388");
		r.add("026110900401");
		r.add("026110900405");
		r.add("026110900411");
		r.add("026110900412");
		r.add("026110900417");
		r.add("026110900418");
		r.add("026110900419");
		r.add("026110900420");
		r.add("026110900428");
		r.add("026110900432");
		r.add("026110900433");
		r.add("026110900446");
		r.add("026110900448");
		r.add("026110900449");
		r.add("026110900451");
		r.add("026110900456");
		r.add("026110900458");
		r.add("026110900460");
		r.add("026110900462");
		r.add("026110900474");
		r.add("026110900476");
		r.add("026110900477");
		r.add("026110900479");
		r.add("026110900481");
		r.add("026110900488");
		r.add("026110900489");
		r.add("026110900495");
		r.add("026110900498");
		r.add("026110900499");
		r.add("026110900501");
		r.add("026110900502");
		r.add("026110900510");
		r.add("026110900517");
		r.add("026110900521");
		r.add("026110900525");
		r.add("026110900534");
		r.add("026110900535");
		r.add("026110900537");
		r.add("026110900556");
		r.add("026110900557");
		r.add("026110900558");
		r.add("026110900560");
		r.add("026110900561");
		r.add("026110900562");
		r.add("026110900564");
		r.add("026110900595");
		r.add("026110900598");
		r.add("026110900599");
		r.add("026110900616");
		r.add("026110900634");
		r.add("026110900639");
		r.add("026110900642");
		r.add("027110900168");
		r.add("027110900169");
		r.add("027110900176");
		r.add("027110900180");
		r.add("027110900188");
		r.add("027110900192");
		r.add("027110900195");
		r.add("027110900203");
		r.add("027110900213");
		r.add("027110900214");
		r.add("027110900215");
		r.add("027110900216");
		r.add("027110900218");
		r.add("027110900219");
		r.add("027110900220");
		r.add("027110900223");
		r.add("027110900227");
		r.add("027110900229");
		r.add("027110900238");
		r.add("027110900249");
		r.add("027110900251");
		r.add("027110900255");
		r.add("027110900256");
		r.add("027110900258");
		r.add("027110900260");
		r.add("027110900261");
		r.add("027110900262");
		r.add("027110900263");
		r.add("027110900267");
		r.add("027110900268");
		r.add("027110900269");
		r.add("027110900270");
		r.add("027110900271");
		r.add("027110900272");
		r.add("027110900274");
		r.add("027110900275");
		r.add("027110900277");
		r.add("027110900279");
		r.add("027110900280");
		r.add("027110900281");
		r.add("027110900282");
		r.add("027110900283");
		r.add("027110900284");
		r.add("027110900286");
		r.add("027110900290");
		r.add("027110900294");
		r.add("027110900297");
		r.add("027110900298");
		r.add("027110900299");
		r.add("027110900301");
		r.add("027110900303");
		r.add("027110900304");
		r.add("027110900307");
		r.add("027110900308");
		r.add("027110900309");
		r.add("027110900310");
		r.add("027110900311");
		r.add("027110900315");
		r.add("027110900316");
		r.add("027110900317");
		r.add("027110900318");
		r.add("027110900324");
		r.add("027110900325");
		r.add("027110900326");
		r.add("027110900339");
		r.add("027110900346");
		r.add("027110900347");
		r.add("027110900354");
		r.add("027110900359");
		r.add("027110900361");
		r.add("027110900391");
		r.add("027110900419");
		r.add("027110900425");
		r.add("027110900430");
		r.add("027110900465");
		r.add("028110900138");
		r.add("028110900206");
		r.add("028110900207");
		r.add("028110900208");
		r.add("028110900210");
		r.add("028110900213");
		r.add("028110900215");
		r.add("028110900217");
		r.add("028110900219");
		r.add("028110900221");
		r.add("028110900223");
		r.add("028110900225");
		r.add("028110900226");
		r.add("028110900234");
		r.add("028110900235");
		r.add("028110900236");
		r.add("028110900238");
		r.add("028110900239");
		r.add("028110900240");
		r.add("028110900242");
		r.add("028110900243");
		r.add("028110900244");
		r.add("028110900245");
		r.add("028110900246");
		r.add("028110900247");
		r.add("028110900248");
		r.add("028110900250");
		r.add("028110900251");
		r.add("028110900253");
		r.add("028110900255");
		r.add("028110900256");
		r.add("028110900257");
		r.add("028110900258");
		r.add("028110900259");
		r.add("028110900263");
		r.add("028110900270");
		r.add("028110900271");
		r.add("028110900272");
		r.add("028110900276");
		r.add("028110900277");
		r.add("028110900278");
		r.add("028110900285");
		r.add("028110900287");
		r.add("028110900292");
		r.add("028110900293");
		r.add("028110900294");
		r.add("028110900295");
		r.add("028110900297");
		r.add("028110900300");
		r.add("028110900335");
		r.add("028110900337");
		r.add("028110900339");
		r.add("028110900340");
		r.add("028110900342");
		r.add("028110900343");
		r.add("028110900344");
		r.add("028110900345");
		r.add("028110900356");
		r.add("028110900357");
		r.add("028110900358");
		r.add("028110900360");
		r.add("028110900366");
		r.add("028110900368");
		r.add("028110900371");
		r.add("028110900374");
		r.add("028110900376");
		r.add("028110900378");
		r.add("028110900381");
		r.add("028110900383");
		r.add("028110900386");
		r.add("028110900387");
		r.add("028110900388");
		r.add("028110900389");
		r.add("028110900390");
		r.add("028110900392");
		r.add("028110900393");
		r.add("028110900394");
		r.add("028110900395");
		r.add("028110900396");
		r.add("028110900397");
		r.add("028110900400");
		r.add("028110900401");
		r.add("028110900416");
		r.add("028110900418");
		r.add("028110900420");
		r.add("028110900422");
		r.add("028110900424");
		r.add("028110900426");
		r.add("028110900427");
		r.add("028110900429");
		r.add("028110900430");
		r.add("028110900431");
		r.add("028110900432");
		r.add("028110900433");
		r.add("028110900434");
		r.add("028110900435");
		r.add("028110900436");
		r.add("028110900439");
		r.add("028110900450");
		r.add("028110900451");
		r.add("028110900452");
		r.add("028110900453");
		r.add("028110900454");
		r.add("028110900457");
		r.add("028110900458");
		r.add("028110900459");
		r.add("028110900461");
		r.add("028110900462");
		r.add("028110900465");
		r.add("028110900477");
		r.add("028110900479");
		r.add("028110900480");
		r.add("028110900487");
		r.add("028110900491");
		r.add("028110900502");
		r.add("028110900519");
		r.add("028110900520");
		r.add("028110900521");
		r.add("028110900522");
		r.add("028110900523");
		r.add("028110900524");
		r.add("028110900525");
		r.add("028110900526");
		r.add("028110900527");
		r.add("028110900528");
		r.add("028110900530");
		r.add("028110900532");
		r.add("028110900549");
		r.add("028110900584");
		r.add("028110900592");
		r.add("028110900593");
		r.add("028110900605");
		r.add("028110900606");
		r.add("028110900616");
		r.add("028110900629");
		r.add("028110900671");
		r.add("028110900695");
		r.add("028110900699");
		r.add("028110900703");
		r.add("028110900709");
		r.add("028110900710");
		r.add("028110900739");
		r.add("028110900741");
		r.add("028110900743");
		r.add("028110900747");
		r.add("028110900749");
		r.add("028110900752");
		r.add("028110900759");
		r.add("029110900168");
		r.add("029110900214");
		r.add("029110900346");
		r.add("029110900368");
		r.add("029110900395");
		r.add("029110900418");
		r.add("029110900432");
		r.add("029110900436");
		r.add("029110900482");
		r.add("029110900490");
		r.add("029110900547");
		r.add("029110900553");
		r.add("029110900565");
		r.add("029110900569");
		r.add("029110900577");
		r.add("029110900578");
		r.add("029110900580");
		r.add("029110900582");
		r.add("029110900586");
		r.add("029110900595");
		r.add("029110900596");
		r.add("029110900597");
		r.add("029110900615");
		r.add("029110900616");
		r.add("029110900620");
		r.add("029110900622");
		r.add("029110900625");
		r.add("029110900628");
		r.add("029110900630");
		r.add("029110900637");
		r.add("029110900638");
		r.add("029110900647");
		r.add("029110900648");
		r.add("029110900650");
		r.add("029110900654");
		r.add("029110900656");
		r.add("029110900660");
		r.add("029110900664");
		r.add("029110900670");
		r.add("029110900672");
		r.add("029110900673");
		r.add("029110900674");
		r.add("029110900675");
		r.add("029110900677");
		r.add("029110900679");
		r.add("029110900680");
		r.add("029110900685");
		r.add("029110900686");
		r.add("029110900689");
		r.add("029110900695");
		r.add("029110900696");
		r.add("029110900697");
		r.add("029110900701");
		r.add("029110900705");
		r.add("029110900708");
		r.add("029110900709");
		r.add("029110900715");
		r.add("029110900716");
		r.add("029110900718");
		r.add("029110900720");
		r.add("029110900721");
		r.add("029110900724");
		r.add("029110900727");
		r.add("029110900731");
		r.add("029110900733");
		r.add("029110900736");
		r.add("029110900737");
		r.add("029110900739");
		r.add("029110900745");
		r.add("029110900750");
		r.add("029110900754");
		r.add("029110900767");
		r.add("029110900774");
		r.add("029110900775");
		r.add("029110900776");
		r.add("029110900783");
		r.add("029110900784");
		r.add("029110900791");
		r.add("029110900792");
		r.add("029110900793");
		r.add("029110900795");
		r.add("029110900798");
		r.add("029110900804");
		r.add("029110900806");
		r.add("029110900810");
		r.add("029110900814");
		r.add("029110900815");
		r.add("029110900821");
		r.add("029110900822");
		r.add("029110900827");
		r.add("029110900830");
		r.add("029110900833");
		r.add("029110900834");
		r.add("029110900837");
		r.add("029110900839");
		r.add("029110900840");
		r.add("029110900850");
		r.add("029110900852");
		r.add("029110900854");
		r.add("029110900855");
		r.add("029110900856");
		r.add("029110900857");
		r.add("029110900859");
		r.add("029110900863");
		r.add("029110900865");
		r.add("029110900867");
		r.add("029110900870");
		r.add("029110900871");
		r.add("029110900872");
		r.add("029110900873");
		r.add("029110900874");
		r.add("029110900875");
		r.add("029110900878");
		r.add("029110900879");
		r.add("029110900880");
		r.add("029110900881");
		r.add("029110900883");
		r.add("029110900885");
		r.add("029110900886");
		r.add("029110900889");
		r.add("029110900890");
		r.add("029110900892");
		r.add("029110900893");
		r.add("029110900895");
		r.add("029110900898");
		r.add("029110900899");
		r.add("029110900900");
		r.add("029110900908");
		r.add("029110900909");
		r.add("029110900910");
		r.add("029110900912");
		r.add("029110900913");
		r.add("029110900915");
		r.add("029110900918");
		r.add("029110900924");
		r.add("029110900928");
		r.add("029110900937");
		r.add("029110900939");
		r.add("029110900950");
		r.add("029110900951");
		r.add("029110900952");
		r.add("029110900953");
		r.add("029110900967");
		r.add("029110900969");
		r.add("029110900974");
		r.add("029110900975");
		r.add("029110900976");
		r.add("029110900980");
		r.add("029110900984");
		r.add("029110900989");
		r.add("029110900990");
		r.add("029110900991");
		r.add("029110900992");
		r.add("029110900995");
		r.add("029110901009");
		r.add("029110901016");
		r.add("029110901017");
		r.add("029110901018");
		r.add("029110901029");
		r.add("029110901030");
		r.add("029110901036");
		r.add("029110901044");
		r.add("029110901046");
		r.add("029110901053");
		r.add("029110901057");
		r.add("029110901060");
		r.add("029110901063");
		r.add("029110901085");
		r.add("029110901091");
		r.add("029110901115");
		r.add("029110901262");
		r.add("029110901267");
		r.add("029110901273");
		r.add("029110901310");
		r.add("029110901337");
		r.add("029110901362");
		r.add("029110901378");
		r.add("029110901379");
		r.add("029110901393");
		r.add("030110900312");
		r.add("030110900313");
		r.add("030110900317");
		r.add("030110900319");
		r.add("030110900338");
		r.add("030110900350");
		r.add("030110900366");
		r.add("030110900379");
		r.add("030110900380");
		r.add("030110900395");
		r.add("030110900414");
		r.add("030110900420");
		r.add("030110900435");
		r.add("030110900436");
		r.add("030110900439");
		r.add("030110900441");
		r.add("030110900443");
		r.add("030110900446");
		r.add("030110900458");
		r.add("030110900475");
		r.add("030110900476");
		r.add("030110900481");
		r.add("030110900484");
		r.add("030110900489");
		r.add("030110900494");
		r.add("030110900498");
		r.add("030110900545");
		r.add("030110900601");
		r.add("030110900604");
		r.add("030110900605");
		r.add("030110900668");
		r.add("030110900683");
		r.add("031110900138");
		r.add("031110900141");
		r.add("031110900165");
		r.add("031110900174");
		r.add("031110900179");
		r.add("031110900180");
		r.add("031110900190");
		r.add("031110900194");
		r.add("031110900197");
		r.add("031110900209");
		r.add("031110900212");
		r.add("031110900214");
		r.add("031110900224");
		r.add("031110900227");
		r.add("031110900228");
		r.add("031110900229");
		r.add("031110900236");
		r.add("031110900243");
		r.add("031110900244");
		r.add("031110900245");
		r.add("031110900250");
		r.add("031110900253");
		r.add("031110900260");
		r.add("031110900262");
		r.add("031110900279");
		r.add("031110900281");
		r.add("031110900283");
		r.add("031110900288");
		r.add("031110900310");
		r.add("032110900196");
		r.add("032110900197");
		r.add("032110900198");
		r.add("032110900199");
		r.add("032110900200");
		r.add("032110900201");
		r.add("032110900202");
		r.add("032110900203");
		r.add("032110900204");
		r.add("032110900205");
		r.add("032110900211");
		r.add("032110900212");
		r.add("032110900214");
		r.add("032110900215");
		r.add("032110900216");
		r.add("032110900217");
		r.add("032110900218");
		r.add("032110900225");
		r.add("032110900226");
		r.add("032110900227");
		r.add("032110900228");
		r.add("032110900230");
		r.add("032110900232");
		r.add("032110900233");
		r.add("032110900234");
		r.add("032110900235");
		r.add("032110900236");
		r.add("032110900237");
		r.add("032110900238");
		r.add("032110900239");
		r.add("032110900240");
		r.add("032110900242");
		r.add("032110900244");
		r.add("032110900245");
		r.add("032110900247");
		r.add("032110900248");
		r.add("032110900249");
		r.add("032110900250");
		r.add("032110900251");
		r.add("032110900252");
		r.add("032110900253");
		r.add("032110900254");
		r.add("032110900255");
		r.add("032110900256");
		r.add("032110900257");
		r.add("032110900258");
		r.add("032110900261");
		r.add("032110900262");
		r.add("032110900263");
		r.add("032110900264");
		r.add("032110900266");
		r.add("032110900268");
		r.add("032110900269");
		r.add("032110900271");
		r.add("032110900278");
		r.add("032110900279");
		r.add("032110900280");
		r.add("032110900281");
		r.add("032110900282");
		r.add("032110900283");
		r.add("032110900284");
		r.add("032110900285");
		r.add("032110900286");
		r.add("032110900288");
		r.add("032110900289");
		r.add("032110900291");
		r.add("032110900292");
		r.add("032110900293");
		r.add("032110900294");
		r.add("032110900295");
		r.add("032110900296");
		r.add("032110900298");
		r.add("032110900299");
		r.add("032110900300");
		r.add("032110900301");
		r.add("032110900302");
		r.add("032110900303");
		r.add("032110900304");
		r.add("032110900305");
		r.add("032110900306");
		r.add("032110900307");
		r.add("032110900308");
		r.add("032110900309");
		r.add("032110900310");
		r.add("032110900311");
		r.add("032110900312");
		r.add("032110900313");
		r.add("032110900314");
		r.add("032110900315");
		r.add("032110900316");
		r.add("032110900317");
		r.add("032110900318");
		r.add("032110900319");
		r.add("032110900320");
		r.add("032110900321");
		r.add("032110900322");
		r.add("032110900323");
		r.add("032110900324");
		r.add("032110900325");
		r.add("032110900326");
		r.add("032110900328");
		r.add("032110900329");
		r.add("032110900332");
		r.add("032110900333");
		r.add("032110900334");
		r.add("032110900335");
		r.add("032110900336");
		r.add("032110900337");
		r.add("032110900340");
		r.add("032110900341");
		r.add("032110900342");
		r.add("032110900343");
		r.add("032110900344");
		r.add("032110900345");
		r.add("032110900346");
		r.add("032110900347");
		r.add("032110900348");
		r.add("032110900349");
		r.add("032110900350");
		r.add("032110900351");
		r.add("032110900352");
		r.add("032110900353");
		r.add("032110900354");
		r.add("032110900355");
		r.add("032110900356");
		r.add("032110900357");
		r.add("032110900358");
		r.add("032110900360");
		r.add("032110900361");
		r.add("032110900363");
		r.add("032110900364");
		r.add("032110900365");
		r.add("032110900366");
		r.add("032110900367");
		r.add("032110900368");
		r.add("032110900369");
		r.add("032110900370");
		r.add("032110900371");
		r.add("032110900372");
		r.add("032110900373");
		r.add("032110900374");
		r.add("032110900375");
		r.add("032110900376");
		r.add("032110900377");
		r.add("032110900378");
		r.add("032110900379");
		r.add("032110900380");
		r.add("032110900381");
		r.add("032110900382");
		r.add("032110900383");
		r.add("032110900384");
		r.add("032110900385");
		r.add("032110900386");
		r.add("032110900387");
		r.add("032110900388");
		r.add("032110900389");
		r.add("032110900392");
		r.add("032110900393");
		r.add("032110900394");
		r.add("032110900395");
		r.add("032110900396");
		r.add("032110900397");
		r.add("032110900398");
		r.add("032110900399");
		r.add("032110900400");
		r.add("032110900401");
		r.add("032110900402");
		r.add("032110900403");
		r.add("032110900404");
		r.add("032110900405");
		r.add("032110900407");
		r.add("032110900409");
		r.add("032110900410");
		r.add("032110900411");
		r.add("032110900412");
		r.add("032110900413");
		r.add("032110900414");
		r.add("032110900415");
		r.add("032110900417");
		r.add("032110900418");
		r.add("032110900419");
		r.add("032110900420");
		r.add("032110900421");
		r.add("032110900422");
		r.add("032110900423");
		r.add("032110900424");
		r.add("032110900425");
		r.add("032110900426");
		r.add("032110900427");
		r.add("032110900428");
		r.add("032110900429");
		r.add("032110900430");
		r.add("032110900431");
		r.add("032110900432");
		r.add("032110900433");
		r.add("032110900434");
		r.add("032110900435");
		r.add("032110900436");
		r.add("032110900438");
		r.add("032110900439");
		r.add("032110900440");
		r.add("032110900441");
		r.add("032110900442");
		r.add("032110900443");
		r.add("032110900444");
		r.add("032110900445");
		r.add("032110900446");
		r.add("032110900447");
		r.add("032110900448");
		r.add("032110900449");
		r.add("032110900450");
		r.add("032110900451");
		r.add("032110900452");
		r.add("032110900453");
		r.add("032110900454");
		r.add("032110900455");
		r.add("032110900456");
		r.add("032110900457");
		r.add("032110900459");
		r.add("032110900460");
		r.add("032110900463");
		r.add("032110900468");
		r.add("032110900470");
		r.add("032110900471");
		r.add("032110900472");
		r.add("032110900473");
		r.add("032110900474");
		r.add("032110900475");
		r.add("032110900476");
		r.add("032110900477");
		r.add("032110900478");
		r.add("032110900479");
		r.add("032110900480");
		r.add("032110900481");
		r.add("032110900482");
		r.add("032110900483");
		r.add("032110900484");
		r.add("032110900485");
		r.add("032110900487");
		r.add("032110900489");
		r.add("032110900493");
		r.add("032110900494");
		r.add("032110900495");
		r.add("032110900498");
		r.add("032110900500");
		r.add("032110900501");
		r.add("032110900503");
		r.add("032110900504");
		r.add("032110900509");
		r.add("032110900512");
		r.add("032110900514");
		r.add("032110900515");
		r.add("032110900517");
		r.add("032110900525");
		r.add("032110900532");
		r.add("032110900536");
		r.add("032110900543");
		r.add("032110900545");
		r.add("032110900554");
		r.add("032110900556");
		r.add("032110900558");
		r.add("032110900560");
		r.add("032110900567");
		r.add("032110900568");
		r.add("032110900573");
		r.add("032110900575");
		r.add("032110900589");
		r.add("032110900594");
		r.add("032110900604");
		r.add("032110900610");
		r.add("032110900614");
		r.add("032110900618");
		r.add("032110900624");
		r.add("032110900628");
		r.add("032110900634");
		r.add("034110900111");
		r.add("034110900116");
		r.add("034110900129");
		r.add("034110900138");
		r.add("034110900143");
		r.add("034110900144");
		r.add("034110900145");
		r.add("034110900146");
		r.add("034110900149");
		r.add("034110900150");
		r.add("034110900152");
		r.add("034110900153");
		r.add("034110900155");
		r.add("034110900157");
		r.add("034110900161");
		r.add("034110900162");
		r.add("034110900175");
		r.add("034110900176");
		r.add("034110900177");
		r.add("034110900178");
		r.add("034110900182");
		r.add("034110900191");
		r.add("034110900192");
		r.add("034110900194");
		r.add("034110900196");
		r.add("034110900199");
		r.add("034110900200");
		r.add("034110900204");
		r.add("034110900223");
		r.add("034110900231");
		r.add("034110900234");
		r.add("035110900203");
		r.add("035110900216");
		r.add("035110900229");
		r.add("035110900244");
		r.add("035110900252");
		r.add("035110900254");
		r.add("035110900255");
		r.add("035110900267");
		r.add("035110900289");
		r.add("035110900326");
		r.add("035110900339");
		r.add("035110900341");
		r.add("035110900345");
		r.add("035110900349");
		r.add("035110900350");
		r.add("035110900351");
		r.add("035110900353");
		r.add("035110900357");
		r.add("035110900363");
		r.add("035110900365");
		r.add("035110900368");
		r.add("035110900371");
		r.add("035110900375");
		r.add("035110900377");
		r.add("035110900384");
		r.add("035110900387");
		r.add("035110900388");
		r.add("035110900391");
		r.add("035110900392");
		r.add("035110900393");
		r.add("035110900395");
		r.add("035110900396");
		r.add("035110900397");
		r.add("035110900400");
		r.add("035110900403");
		r.add("035110900404");
		r.add("035110900409");
		r.add("035110900410");
		r.add("035110900412");
		r.add("035110900414");
		r.add("035110900415");
		r.add("035110900417");
		r.add("035110900419");
		r.add("035110900421");
		r.add("035110900423");
		r.add("035110900424");
		r.add("035110900425");
		r.add("035110900426");
		r.add("035110900428");
		r.add("035110900430");
		r.add("035110900446");
		r.add("035110900485");
		r.add("035110900487");
		r.add("035110900488");
		r.add("035110900489");
		r.add("035110900490");
		r.add("035110900491");
		r.add("035110900492");
		r.add("035110900495");
		r.add("035110900496");
		r.add("035110900498");
		r.add("035110900499");
		r.add("035110900502");
		r.add("035110900504");
		r.add("035110900506");
		r.add("035110900510");
		r.add("035110900514");
		r.add("035110900516");
		r.add("035110900517");
		r.add("035110900519");
		r.add("035110900521");
		r.add("035110900523");
		r.add("035110900525");
		r.add("035110900526");
		r.add("035110900527");
		r.add("035110900528");
		r.add("035110900529");
		r.add("035110900531");
		r.add("035110900535");
		r.add("035110900536");
		r.add("035110900537");
		r.add("035110900538");
		r.add("035110900540");
		r.add("035110900541");
		r.add("035110900542");
		r.add("035110900543");
		r.add("035110900545");
		r.add("035110900546");
		r.add("035110900547");
		r.add("035110900551");
		r.add("035110900552");
		r.add("035110900556");
		r.add("035110900558");
		r.add("035110900563");
		r.add("035110900564");
		r.add("035110900568");
		r.add("035110900571");
		r.add("035110900572");
		r.add("035110900574");
		r.add("035110900575");
		r.add("035110900576");
		r.add("035110900577");
		r.add("035110900580");
		r.add("035110900582");
		r.add("035110900583");
		r.add("035110900584");
		r.add("035110900585");
		r.add("035110900586");
		r.add("035110900588");
		r.add("035110900590");
		r.add("035110900591");
		r.add("035110900592");
		r.add("035110900593");
		r.add("035110900594");
		r.add("035110900595");
		r.add("035110900597");
		r.add("035110900599");
		r.add("035110900600");
		r.add("035110900604");
		r.add("035110900605");
		r.add("035110900606");
		r.add("035110900608");
		r.add("035110900609");
		r.add("035110900610");
		r.add("035110900617");
		r.add("035110900619");
		r.add("035110900634");
		r.add("035110900635");
		r.add("035110900638");
		r.add("035110900641");
		r.add("035110900643");
		r.add("035110900645");
		r.add("035110900649");
		r.add("035110900651");
		r.add("035110900652");
		r.add("035110900653");
		r.add("035110900654");
		r.add("035110900655");
		r.add("035110900656");
		r.add("035110900659");
		r.add("035110900661");
		r.add("035110900662");
		r.add("035110900669");
		r.add("035110900670");
		r.add("035110900671");
		r.add("035110900672");
		r.add("035110900673");
		r.add("035110900674");
		r.add("035110900675");
		r.add("035110900676");
		r.add("035110900677");
		r.add("035110900690");
		r.add("035110900691");
		r.add("035110900693");
		r.add("035110900695");
		r.add("035110900696");
		r.add("035110900699");
		r.add("035110900700");
		r.add("035110900702");
		r.add("035110900704");
		r.add("035110900712");
		r.add("035110900713");
		r.add("035110900718");
		r.add("035110900721");
		r.add("035110900725");
		r.add("035110900727");
		r.add("035110900730");
		r.add("035110900732");
		r.add("035110900735");
		r.add("035110900740");
		r.add("035110900741");
		r.add("035110900746");
		r.add("035110900756");
		r.add("035110900757");
		r.add("035110900761");
		r.add("035110900784");
		r.add("035110900788");
		r.add("035110900802");
		r.add("035110900804");
		r.add("035110900805");
		r.add("035110900823");
		r.add("035110900825");
		r.add("035110900829");
		r.add("035110900852");
		r.add("035110900859");
		r.add("035110900870");
		r.add("035110900877");
		r.add("035110900878");
		r.add("035110900879");
		r.add("035110900883");
		r.add("035110900894");
		r.add("035110900898");
		r.add("035110900899");
		r.add("035110900901");
		r.add("035110900908");
		r.add("035110900912");
		r.add("035110901020");
		r.add("036110900105");
		r.add("036110900133");
		r.add("036110900134");
		r.add("036110900159");
		r.add("036110900164");
		r.add("036110900167");
		r.add("036110900189");
		r.add("036110900204");
		r.add("036110900211");
		r.add("036110900212");
		r.add("036110900218");
		r.add("036110900238");
		r.add("036110900251");
		r.add("037110900191");
		r.add("037110900192");
		r.add("037110900193");
		r.add("037110900194");
		r.add("037110900204");
		r.add("037110900209");
		r.add("037110900210");
		r.add("037110900211");
		r.add("037110900213");
		r.add("037110900218");
		r.add("037110900219");
		r.add("037110900263");
		r.add("037110900264");
		r.add("037110900300");
		r.add("037110900301");
		r.add("037110900302");
		r.add("037110900303");
		r.add("037110900304");
		r.add("037110900305");
		r.add("037110900310");
		r.add("037110900311");
		r.add("037110900315");
		r.add("037110900317");
		r.add("037110900319");
		r.add("037110900321");
		r.add("037110900324");
		r.add("037110900326");
		r.add("037110900327");
		r.add("037110900328");
		r.add("037110900335");
		r.add("037110900339");
		r.add("037110900342");
		r.add("037110900355");
		r.add("037110900358");
		r.add("037110900361");
		r.add("037110900362");
		r.add("037110900365");
		r.add("037110900366");
		r.add("037110900367");
		r.add("037110900368");
		r.add("037110900370");
		r.add("037110900373");
		r.add("037110900377");
		r.add("037110900379");
		r.add("037110900387");
		r.add("037110900388");
		r.add("037110900389");
		r.add("037110900391");
		r.add("037110900392");
		r.add("037110900393");
		r.add("037110900398");
		r.add("037110900399");
		r.add("037110900400");
		r.add("037110900401");
		r.add("037110900402");
		r.add("037110900403");
		r.add("037110900404");
		r.add("037110900405");
		r.add("037110900409");
		r.add("037110900410");
		r.add("037110900411");
		r.add("037110900412");
		r.add("037110900413");
		r.add("037110900415");
		r.add("037110900416");
		r.add("037110900417");
		r.add("037110900418");
		r.add("037110900419");
		r.add("037110900421");
		r.add("037110900424");
		r.add("037110900425");
		r.add("037110900426");
		r.add("037110900428");
		r.add("037110900429");
		r.add("037110900430");
		r.add("037110900431");
		r.add("037110900432");
		r.add("037110900433");
		r.add("037110900434");
		r.add("037110900436");
		r.add("037110900438");
		r.add("037110900440");
		r.add("037110900441");
		r.add("037110900442");
		r.add("037110900444");
		r.add("037110900445");
		r.add("037110900446");
		r.add("037110900447");
		r.add("037110900448");
		r.add("037110900452");
		r.add("037110900455");
		r.add("037110900457");
		r.add("037110900460");
		r.add("037110900463");
		r.add("037110900467");
		r.add("037110900468");
		r.add("037110900476");
		r.add("037110900478");
		r.add("037110900479");
		r.add("037110900487");
		r.add("037110900489");
		r.add("037110900490");
		r.add("037110900491");
		r.add("037110900492");
		r.add("037110900493");
		r.add("037110900496");
		r.add("037110900497");
		r.add("037110900499");
		r.add("037110900500");
		r.add("037110900504");
		r.add("037110900509");
		r.add("037110900512");
		r.add("037110900513");
		r.add("037110900519");
		r.add("037110900521");
		r.add("037110900523");
		r.add("037110900525");
		r.add("037110900534");
		r.add("037110900536");
		r.add("037110900538");
		r.add("037110900539");
		r.add("037110900541");
		r.add("037110900542");
		r.add("037110900543");
		r.add("037110900547");
		r.add("037110900548");
		r.add("037110900552");
		r.add("037110900555");
		r.add("037110900559");
		r.add("037110900562");
		r.add("037110900567");
		r.add("037110900573");
		r.add("037110900575");
		r.add("037110900576");
		r.add("037110900577");
		r.add("037110900579");
		r.add("037110900580");
		r.add("037110900581");
		r.add("037110900582");
		r.add("037110900583");
		r.add("037110900593");
		r.add("037110900621");
		r.add("037110900623");
		r.add("037110900624");
		r.add("037110900625");
		r.add("037110900626");
		r.add("037110900628");
		r.add("037110900631");
		r.add("037110900632");
		r.add("037110900634");
		r.add("037110900635");
		r.add("037110900636");
		r.add("037110900640");
		r.add("037110900641");
		r.add("037110900642");
		r.add("037110900643");
		r.add("037110900644");
		r.add("037110900645");
		r.add("037110900647");
		r.add("037110900648");
		r.add("037110900651");
		r.add("037110900653");
		r.add("037110900656");
		r.add("037110900657");
		r.add("037110900658");
		r.add("037110900666");
		r.add("037110900677");
		r.add("037110900679");
		r.add("037110900694");
		r.add("037110900696");
		r.add("037110900698");
		r.add("037110900699");
		r.add("037110900702");
		r.add("037110900704");
		r.add("037110900705");
		r.add("037110900707");
		r.add("037110900711");
		r.add("037110900751");
		r.add("037110900752");
		r.add("037110900754");
		r.add("037110900755");
		r.add("037110900756");
		r.add("037110900764");
		r.add("037110900785");
		r.add("037110900819");
		r.add("037110900850");
		r.add("037110900851");
		r.add("037110900858");
		r.add("037110900859");
		r.add("037110900860");
		r.add("037110900866");
		r.add("037110900871");
		r.add("037110900873");
		r.add("037110900875");
		r.add("037110900879");
		r.add("037110900884");
		r.add("037110900894");
		r.add("037110900898");
		r.add("037110900901");
		r.add("037110900911");
		r.add("037110900913");
		r.add("037110900914");
		r.add("037110900915");
		r.add("037110900939");
		r.add("038110900101");
		r.add("038110900102");
		r.add("038110900128");
		r.add("038110900129");
		r.add("038110900131");
		r.add("038110900141");
		r.add("038110900144");
		r.add("038110900152");
		r.add("038110900153");
		r.add("038110900155");
		r.add("038110900157");
		r.add("038110900159");
		r.add("038110900164");
		r.add("038110900165");
		r.add("038110900166");
		r.add("038110900167");
		r.add("038110900173");
		r.add("038110900176");
		r.add("038110900180");
		r.add("038110900183");
		r.add("038110900185");
		r.add("038110900186");
		r.add("038110900187");
		r.add("038110900188");
		r.add("038110900189");
		r.add("038110900190");
		r.add("038110900191");
		r.add("038110900192");
		r.add("038110900193");
		r.add("038110900194");
		r.add("038110900195");
		r.add("038110900196");
		r.add("038110900197");
		r.add("038110900198");
		r.add("038110900199");
		r.add("038110900200");
		r.add("038110900201");
		r.add("038110900202");
		r.add("038110900203");
		r.add("038110900204");
		r.add("038110900205");
		r.add("038110900206");
		r.add("038110900207");
		r.add("038110900212");
		r.add("038110900213");
		r.add("038110900214");
		r.add("038110900215");
		r.add("038110900216");
		r.add("038110900217");
		r.add("038110900219");
		r.add("038110900220");
		r.add("038110900221");
		r.add("038110900223");
		r.add("038110900224");
		r.add("038110900225");
		r.add("038110900226");
		r.add("038110900227");
		r.add("038110900228");
		r.add("038110900229");
		r.add("038110900230");
		r.add("038110900231");
		r.add("038110900232");
		r.add("038110900233");
		r.add("038110900234");
		r.add("038110900235");
		r.add("038110900236");
		r.add("038110900237");
		r.add("038110900238");
		r.add("038110900239");
		r.add("038110900240");
		r.add("038110900241");
		r.add("038110900242");
		r.add("038110900243");
		r.add("038110900244");
		r.add("038110900245");
		r.add("038110900246");
		r.add("038110900247");
		r.add("038110900248");
		r.add("038110900249");
		r.add("038110900250");
		r.add("038110900252");
		r.add("038110900253");
		r.add("038110900254");
		r.add("038110900255");
		r.add("038110900257");
		r.add("038110900259");
		r.add("038110900260");
		r.add("038110900261");
		r.add("038110900262");
		r.add("038110900263");
		r.add("038110900264");
		r.add("038110900265");
		r.add("038110900270");
		r.add("038110900279");
		r.add("038110900280");
		r.add("038110900299");
		r.add("038110900325");
		r.add("038110900331");
		r.add("038110900363");
		r.add("039110900155");
		r.add("039110900161");
		r.add("039110900162");
		r.add("039110900166");
		r.add("039110900167");
		r.add("039110900168");
		r.add("039110900169");
		r.add("039110900170");
		r.add("039110900174");
		r.add("039110900176");
		r.add("039110900178");
		r.add("039110900179");
		r.add("039110900181");
		r.add("039110900182");
		r.add("039110900184");
		r.add("039110900187");
		r.add("039110900189");
		r.add("039110900190");
		r.add("039110900191");
		r.add("039110900195");
		r.add("039110900196");
		r.add("039110900201");
		r.add("039110900203");
		r.add("039110900205");
		r.add("039110900206");
		r.add("039110900207");
		r.add("039110900208");
		r.add("039110900212");
		r.add("039110900215");
		r.add("039110900219");
		r.add("039110900221");
		r.add("039110900222");
		r.add("039110900230");
		r.add("039110900231");
		r.add("039110900232");
		r.add("039110900233");
		r.add("039110900234");
		r.add("039110900235");
		r.add("039110900236");
		r.add("039110900237");
		r.add("039110900238");
		r.add("039110900239");
		r.add("039110900240");
		r.add("039110900241");
		r.add("039110900243");
		r.add("039110900244");
		r.add("039110900245");
		r.add("039110900246");
		r.add("039110900247");
		r.add("039110900248");
		r.add("039110900249");
		r.add("039110900251");
		r.add("039110900254");
		r.add("039110900255");
		r.add("039110900256");
		r.add("039110900257");
		r.add("039110900258");
		r.add("039110900259");
		r.add("039110900260");
		r.add("039110900261");
		r.add("039110900262");
		r.add("039110900263");
		r.add("039110900264");
		r.add("039110900266");
		r.add("039110900267");
		r.add("039110900269");
		r.add("039110900270");
		r.add("039110900271");
		r.add("039110900272");
		r.add("039110900273");
		r.add("039110900274");
		r.add("039110900275");
		r.add("039110900276");
		r.add("039110900277");
		r.add("039110900278");
		r.add("039110900279");
		r.add("039110900280");
		r.add("039110900281");
		r.add("039110900282");
		r.add("039110900283");
		r.add("039110900284");
		r.add("039110900285");
		r.add("039110900288");
		r.add("039110900289");
		r.add("039110900290");
		r.add("039110900291");
		r.add("039110900292");
		r.add("039110900293");
		r.add("039110900294");
		r.add("039110900295");
		r.add("039110900297");
		r.add("039110900298");
		r.add("039110900299");
		r.add("039110900300");
		r.add("039110900301");
		r.add("039110900302");
		r.add("039110900303");
		r.add("039110900304");
		r.add("039110900305");
		r.add("039110900306");
		r.add("039110900307");
		r.add("039110900312");
		r.add("039110900313");
		r.add("039110900314");
		r.add("039110900315");
		r.add("039110900316");
		r.add("039110900317");
		r.add("039110900318");
		r.add("039110900319");
		r.add("039110900320");
		r.add("039110900321");
		r.add("039110900322");
		r.add("039110900323");
		r.add("039110900324");
		r.add("039110900325");
		r.add("039110900326");
		r.add("039110900327");
		r.add("039110900328");
		r.add("039110900329");
		r.add("039110900331");
		r.add("039110900332");
		r.add("039110900333");
		r.add("039110900334");
		r.add("039110900335");
		r.add("039110900336");
		r.add("039110900338");
		r.add("039110900341");
		r.add("039110900342");
		r.add("039110900343");
		r.add("039110900346");
		r.add("039110900347");
		r.add("039110900348");
		r.add("039110900349");
		r.add("039110900350");
		r.add("039110900351");
		r.add("039110900352");
		r.add("039110900354");
		r.add("039110900355");
		r.add("039110900357");
		r.add("039110900358");
		r.add("039110900359");
		r.add("039110900360");
		r.add("039110900362");
		r.add("039110900363");
		r.add("039110900364");
		r.add("039110900365");
		r.add("039110900367");
		r.add("039110900368");
		r.add("039110900369");
		r.add("039110900371");
		r.add("039110900372");
		r.add("039110900373");
		r.add("039110900374");
		r.add("039110900381");
		r.add("039110900382");
		r.add("039110900383");
		r.add("039110900384");
		r.add("039110900385");
		r.add("039110900386");
		r.add("039110900387");
		r.add("039110900388");
		r.add("039110900389");
		r.add("039110900390");
		r.add("039110900394");
		r.add("039110900397");
		r.add("039110900400");
		r.add("039110900411");
		r.add("039110900422");
		r.add("039110900423");
		r.add("039110900449");
		r.add("039110900452");
		r.add("039110900488");
		r.add("039110900501");
		r.add("039110900502");
		r.add("039110900522");
		r.add("039110900526");
		r.add("039110900547");
		r.add("039110900598");
		r.add("039110900618");
		r.add("040110900121");
		r.add("040110900130");
		r.add("040110900150");
		r.add("040110900159");
		r.add("040110900162");
		r.add("040110900164");
		r.add("040110900165");
		r.add("040110900166");
		r.add("040110900172");
		r.add("040110900176");
		r.add("040110900190");
		r.add("040110900191");
		r.add("040110900192");
		r.add("040110900200");
		r.add("040110900202");
		r.add("040110900209");
		r.add("040110900211");
		r.add("040110900213");
		r.add("040110900214");
		r.add("040110900219");
		r.add("040110900222");
		r.add("040110900226");
		r.add("040110900227");
		r.add("040110900234");
		r.add("040110900236");
		r.add("040110900238");
		r.add("040110900239");
		r.add("040110900240");
		r.add("040110900241");
		r.add("040110900242");
		r.add("040110900243");
		r.add("040110900244");
		r.add("040110900247");
		r.add("040110900249");
		r.add("040110900250");
		r.add("040110900251");
		r.add("040110900254");
		r.add("040110900255");
		r.add("040110900256");
		r.add("040110900257");
		r.add("040110900258");
		r.add("040110900260");
		r.add("040110900265");
		r.add("040110900268");
		r.add("040110900272");
		r.add("040110900273");
		r.add("040110900274");
		r.add("040110900280");
		r.add("040110900286");
		r.add("040110900287");
		r.add("040110900289");
		r.add("040110900290");
		r.add("040110900295");
		r.add("040110900298");
		r.add("040110900299");
		r.add("040110900305");
		r.add("040110900308");
		r.add("040110900309");
		r.add("040110900310");
		r.add("040110900312");
		r.add("040110900318");
		r.add("040110900352");
		r.add("040110900398");
		r.add("040110900403");
		r.add("040110900416");
		r.add("041110900144");
		r.add("041110900148");
		r.add("041110900193");
		r.add("041110900212");
		r.add("041110900220");
		r.add("041110900227");
		r.add("041110900228");
		r.add("041110900234");
		r.add("041110900235");
		r.add("041110900237");
		r.add("041110900239");
		r.add("041110900241");
		r.add("041110900244");
		r.add("041110900256");
		r.add("041110900260");
		r.add("041110900265");
		r.add("041110900268");
		r.add("041110900273");
		r.add("041110900274");
		r.add("041110900275");
		r.add("041110900276");
		r.add("041110900282");
		r.add("041110900285");
		r.add("041110900287");
		r.add("041110900288");
		r.add("041110900289");
		r.add("041110900292");
		r.add("041110900293");
		r.add("041110900296");
		r.add("041110900297");
		r.add("041110900303");
		r.add("041110900304");
		r.add("041110900306");
		r.add("041110900308");
		r.add("041110900311");
		r.add("041110900318");
		r.add("041110900320");
		r.add("041110900321");
		r.add("041110900322");
		r.add("041110900324");
		r.add("041110900325");
		r.add("041110900337");
		r.add("041110900342");
		r.add("041110900344");
		r.add("041110900345");
		r.add("041110900347");
		r.add("041110900348");
		r.add("041110900355");
		r.add("041110900357");
		r.add("041110900364");
		r.add("041110900396");
		r.add("042110900230");
		r.add("042110900286");
		r.add("042110900307");
		r.add("042110900364");
		r.add("042110900367");
		r.add("042110900368");
		r.add("042110900369");
		r.add("042110900370");
		r.add("042110900372");
		r.add("042110900374");
		r.add("042110900377");
		r.add("042110900378");
		r.add("042110900380");
		r.add("042110900386");
		r.add("042110900391");
		r.add("042110900392");
		r.add("042110900393");
		r.add("042110900395");
		r.add("042110900396");
		r.add("042110900398");
		r.add("042110900404");
		r.add("042110900405");
		r.add("042110900406");
		r.add("042110900407");
		r.add("042110900412");
		r.add("042110900413");
		r.add("042110900414");
		r.add("042110900416");
		r.add("042110900436");
		r.add("043110900131");
		r.add("043110900139");
		r.add("043110900143");
		r.add("043110900152");
		r.add("043110900154");
		r.add("043110900160");
		r.add("043110900165");
		r.add("043110900169");
		r.add("043110900174");
		r.add("043110900175");
		r.add("043110900176");
		r.add("043110900177");
		r.add("043110900178");
		r.add("043110900179");
		r.add("043110900187");
		r.add("043110900188");
		r.add("043110900189");
		r.add("043110900194");
		r.add("043110900198");
		r.add("043110900201");
		r.add("043110900202");
		r.add("043110900205");
		r.add("043110900207");
		r.add("043110900208");
		r.add("043110900209");
		r.add("043110900210");
		r.add("043110900215");
		r.add("043110900216");
		r.add("043110900217");
		r.add("043110900220");
		r.add("043110900221");
		r.add("043110900222");
		r.add("043110900223");
		r.add("043110900224");
		r.add("043110900225");
		r.add("043110900232");
		r.add("043110900235");
		r.add("043110900236");
		r.add("043110900244");
		r.add("043110900252");
		r.add("043110900264");
		r.add("043110900285");
		r.add("043110900286");
		r.add("043110900287");
		r.add("043110900288");
		r.add("043110900289");
		r.add("043110900317");
		r.add("044110900160");
		r.add("044110900162");
		r.add("044110900165");
		r.add("044110900168");
		r.add("044110900171");
		r.add("044110900173");
		r.add("044110900175");
		r.add("044110900176");
		r.add("044110900180");
		r.add("044110900182");
		r.add("044110900183");
		r.add("044110900187");
		r.add("044110900195");
		r.add("044110900196");
		r.add("044110900198");
		r.add("044110900199");
		r.add("044110900200");
		r.add("044110900203");
		r.add("044110900204");
		r.add("044110900205");
		r.add("044110900206");
		r.add("044110900207");
		r.add("044110900213");
		r.add("044110900217");
		r.add("044110900226");
		r.add("044110900229");
		r.add("044110900235");
		r.add("044110900238");
		r.add("044110900239");
		r.add("044110900242");
		r.add("044110900243");
		r.add("044110900244");
		r.add("044110900246");
		r.add("044110900247");
		r.add("044110900248");
		r.add("044110900249");
		r.add("044110900263");
		r.add("044110900264");
		r.add("044110900265");
		r.add("044110900266");
		r.add("044110900268");
		r.add("044110900269");
		r.add("044110900270");
		r.add("044110900271");
		r.add("044110900272");
		r.add("044110900273");
		r.add("044110900274");
		r.add("044110900276");
		r.add("044110900279");
		r.add("044110900280");
		r.add("044110900285");
		r.add("044110900286");
		r.add("044110900287");
		r.add("044110900288");
		r.add("044110900290");
		r.add("044110900291");
		r.add("044110900292");
		r.add("044110900293");
		r.add("044110900299");
		r.add("044110900300");
		r.add("044110900305");
		r.add("044110900308");
		r.add("044110900310");
		r.add("044110900315");
		r.add("044110900317");
		r.add("044110900320");
		r.add("044110900321");
		r.add("044110900325");
		r.add("044110900329");
		r.add("044110900330");
		r.add("044110900333");
		r.add("044110900334");
		r.add("044110900335");
		r.add("044110900337");
		r.add("044110900338");
		r.add("044110900340");
		r.add("044110900341");
		r.add("044110900342");
		r.add("044110900347");
		r.add("044110900348");
		r.add("044110900350");
		r.add("044110900351");
		r.add("044110900352");
		r.add("044110900362");
		r.add("044110900364");
		r.add("044110900367");
		r.add("044110900368");
		r.add("044110900371");
		r.add("044110900374");
		r.add("044110900375");
		r.add("044110900393");
		r.add("044110900394");
		r.add("044110900395");
		r.add("044110900398");
		r.add("044110900417");
		r.add("044110900420");
		r.add("044110900426");
		r.add("044110900483");
		r.add("045110900147");
		r.add("045110900150");
		r.add("045110900155");
		r.add("045110900200");
		r.add("045110900201");
		r.add("045110900204");
		r.add("045110900205");
		r.add("045110900215");
		r.add("045110900217");
		r.add("045110900218");
		r.add("045110900219");
		r.add("045110900220");
		r.add("045110900225");
		r.add("045110900227");
		r.add("045110900228");
		r.add("045110900233");
		r.add("045110900234");
		r.add("045110900236");
		r.add("045110900237");
		r.add("045110900240");
		r.add("045110900241");
		r.add("045110900242");
		r.add("045110900244");
		r.add("045110900246");
		r.add("045110900247");
		r.add("045110900249");
		r.add("045110900252");
		r.add("045110900254");
		r.add("045110900256");
		r.add("045110900258");
		r.add("045110900266");
		r.add("045110900269");
		r.add("045110900273");
		r.add("045110900274");
		r.add("045110900275");
		r.add("045110900276");
		r.add("045110900277");
		r.add("045110900279");
		r.add("045110900281");
		r.add("045110900288");
		r.add("045110900289");
		r.add("045110900290");
		r.add("045110900291");
		r.add("045110900296");
		r.add("045110900297");
		r.add("045110900298");
		r.add("045110900300");
		r.add("045110900303");
		r.add("045110900304");
		r.add("045110900305");
		r.add("045110900306");
		r.add("045110900309");
		r.add("045110900319");
		r.add("045110900322");
		r.add("045110900324");
		r.add("045110900325");
		r.add("045110900327");
		r.add("045110900329");
		r.add("045110900330");
		r.add("045110900332");
		r.add("045110900335");
		r.add("045110900336");
		r.add("045110900337");
		r.add("045110900340");
		r.add("045110900344");
		r.add("045110900346");
		r.add("045110900348");
		r.add("045110900351");
		r.add("045110900356");
		r.add("045110900357");
		r.add("045110900359");
		r.add("045110900361");
		r.add("045110900363");
		r.add("045110900365");
		r.add("045110900368");
		r.add("045110900369");
		r.add("045110900371");
		r.add("045110900372");
		r.add("045110900375");
		r.add("045110900376");
		r.add("045110900377");
		r.add("045110900378");
		r.add("045110900384");
		r.add("045110900385");
		r.add("045110900393");
		r.add("045110900397");
		r.add("045110900398");
		r.add("045110900400");
		r.add("045110900402");
		r.add("045110900420");
		r.add("045110900433");
		r.add("045110900438");
		r.add("045110900439");
		r.add("045110900451");
		r.add("045110900464");
		r.add("045110900474");
		r.add("045110900484");
		r.add("045110900516");
		r.add("045110900548");
		r.add("045110900553");
		r.add("045110900555");
		r.add("045110900558");
		r.add("045110900566");
		r.add("045110900569");
		r.add("045110900579");
		r.add("045110900586");
		r.add("045110900587");
		r.add("045110900609");
		r.add("045110900638");
		r.add("045110900667");
		r.add("045110900670");
		r.add("045110900671");
		r.add("045110900675");
		r.add("045110900681");
		r.add("045110900692");
		r.add("045110900705");
		r.add("045110900709");
		r.add("046110900174");
		r.add("046110900175");
		r.add("046110900176");
		r.add("046110900195");
		r.add("046110900202");
		r.add("046110900211");
		r.add("046110900213");
		r.add("046110900215");
		r.add("046110900219");
		r.add("046110900223");
		r.add("046110900225");
		r.add("046110900242");
		r.add("046110900243");
		r.add("046110900244");
		r.add("046110900248");
		r.add("046110900249");
		r.add("046110900250");
		r.add("046110900251");
		r.add("046110900252");
		r.add("046110900253");
		r.add("046110900254");
		r.add("046110900260");
		r.add("046110900261");
		r.add("046110900263");
		r.add("046110900265");
		r.add("046110900269");
		r.add("046110900286");
		r.add("046110900290");
		r.add("046110900292");
		r.add("046110900294");
		r.add("046110900295");
		r.add("046110900297");
		r.add("046110900298");
		r.add("046110900299");
		r.add("046110900301");
		r.add("046110900304");
		r.add("046110900310");
		r.add("046110900311");
		r.add("046110900313");
		r.add("046110900316");
		r.add("046110900320");
		r.add("046110900321");
		r.add("046110900323");
		r.add("046110900325");
		r.add("046110900327");
		r.add("046110900334");
		r.add("046110900337");
		r.add("046110900339");
		r.add("046110900348");
		r.add("046110900349");
		r.add("046110900375");
		r.add("046110900376");
		r.add("046110900377");
		r.add("046110900381");
		r.add("046110900391");
		r.add("046110900392");
		r.add("046110900406");
		r.add("046110900409");
		r.add("046110900413");
		r.add("046110900424");
		r.add("046110900433");
		r.add("046110900443");
		r.add("046110900448");
		r.add("046110900456");
		r.add("046110900471");
		r.add("046110900472");
		r.add("046110900475");
		r.add("046110900478");
		r.add("046110900491");
		r.add("046110900492");
		r.add("046110900495");
		r.add("046110900498");
		r.add("046110900514");
		r.add("046110900517");
		r.add("046110900518");
		r.add("046110900545");
		r.add("047110900088");
		r.add("047110900097");
		r.add("047110900099");
		r.add("047110900121");
		r.add("047110900128");
		r.add("047110900135");
		r.add("047110900137");
		r.add("047110900153");
		r.add("047110900158");
		r.add("047110900165");
		r.add("047110900179");
		r.add("047110900181");
		r.add("047110900189");
		r.add("047110900193");
		r.add("047110900194");
		r.add("047110900210");
		r.add("047110900212");
		r.add("047110900217");
		r.add("047110900218");
		r.add("047110900220");
		r.add("047110900223");
		r.add("047110900225");
		r.add("047110900228");
		r.add("047110900231");
		r.add("047110900233");
		r.add("047110900236");
		r.add("047110900238");
		r.add("047110900240");
		r.add("047110900241");
		r.add("047110900242");
		r.add("047110900243");
		r.add("047110900244");
		r.add("047110900245");
		r.add("047110900246");
		r.add("047110900248");
		r.add("047110900250");
		r.add("047110900251");
		r.add("047110900252");
		r.add("047110900253");
		r.add("047110900254");
		r.add("047110900255");
		r.add("047110900256");
		r.add("047110900257");
		r.add("047110900258");
		r.add("047110900259");
		r.add("047110900260");
		r.add("047110900261");
		r.add("047110900262");
		r.add("047110900263");
		r.add("047110900264");
		r.add("047110900265");
		r.add("047110900266");
		r.add("047110900267");
		r.add("047110900268");
		r.add("047110900269");
		r.add("047110900270");
		r.add("047110900272");
		r.add("047110900273");
		r.add("047110900274");
		r.add("047110900275");
		r.add("047110900282");
		r.add("047110900283");
		r.add("047110900284");
		r.add("047110900285");
		r.add("047110900286");
		r.add("047110900287");
		r.add("047110900288");
		r.add("047110900289");
		r.add("047110900290");
		r.add("047110900291");
		r.add("047110900292");
		r.add("047110900294");
		r.add("047110900295");
		r.add("047110900296");
		r.add("047110900298");
		r.add("047110900299");
		r.add("047110900300");
		r.add("047110900303");
		r.add("047110900305");
		r.add("047110900306");
		r.add("047110900307");
		r.add("047110900309");
		r.add("047110900310");
		r.add("047110900311");
		r.add("047110900313");
		r.add("047110900314");
		r.add("047110900315");
		r.add("047110900316");
		r.add("047110900317");
		r.add("047110900319");
		r.add("047110900320");
		r.add("047110900321");
		r.add("047110900323");
		r.add("047110900324");
		r.add("047110900325");
		r.add("047110900326");
		r.add("047110900327");
		r.add("047110900329");
		r.add("047110900330");
		r.add("047110900332");
		r.add("047110900335");
		r.add("047110900336");
		r.add("047110900337");
		r.add("047110900338");
		r.add("047110900339");
		r.add("047110900340");
		r.add("047110900341");
		r.add("047110900342");
		r.add("047110900343");
		r.add("047110900344");
		r.add("047110900345");
		r.add("047110900346");
		r.add("047110900348");
		r.add("047110900350");
		r.add("047110900351");
		r.add("047110900352");
		r.add("047110900353");
		r.add("047110900354");
		r.add("047110900355");
		r.add("047110900356");
		r.add("047110900357");
		r.add("047110900358");
		r.add("047110900359");
		r.add("047110900360");
		r.add("047110900362");
		r.add("047110900364");
		r.add("047110900366");
		r.add("047110900371");
		r.add("047110900376");
		r.add("047110900380");
		r.add("047110900382");
		r.add("047110900383");
		r.add("047110900385");
		r.add("047110900386");
		r.add("047110900387");
		r.add("047110900393");
		r.add("047110900394");
		r.add("047110900395");
		r.add("047110900397");
		r.add("047110900401");
		r.add("047110900402");
		r.add("047110900403");
		r.add("047110900405");
		r.add("047110900407");
		r.add("047110900408");
		r.add("047110900409");
		r.add("047110900411");
		r.add("047110900414");
		r.add("047110900419");
		r.add("047110900422");
		r.add("047110900423");
		r.add("047110900427");
		r.add("047110900431");
		r.add("047110900432");
		r.add("047110900434");
		r.add("047110900443");
		r.add("047110900448");
		r.add("047110900469");
		r.add("047110900474");
		r.add("048110900105");
		r.add("048110900107");
		r.add("048110900109");
		r.add("048110900114");
		r.add("048110900115");
		r.add("048110900119");
		r.add("048110900120");
		r.add("048110900124");
		r.add("048110900125");
		r.add("048110900127");
		r.add("048110900129");
		r.add("048110900130");
		r.add("048110900132");
		r.add("048110900134");
		r.add("048110900135");
		r.add("048110900136");
		r.add("048110900138");
		r.add("048110900140");
		r.add("048110900141");
		r.add("048110900142");
		r.add("048110900143");
		r.add("048110900146");
		r.add("048110900152");
		r.add("048110900153");
		r.add("048110900155");
		r.add("048110900161");
		r.add("048110900166");
		r.add("048110900167");
		r.add("048110900172");
		r.add("048110900174");
		r.add("048110900213");
		r.add("049110900102");
		r.add("049110900113");
		r.add("049110900114");
		r.add("049110900115");
		r.add("049110900116");
		r.add("049110900118");
		r.add("049110900122");
		r.add("049110900124");
		r.add("049110900125");
		r.add("049110900127");
		r.add("049110900128");
		r.add("049110900129");
		r.add("049110900130");
		r.add("049110900131");
		r.add("049110900149");
		r.add("049110900150");
		r.add("049110900151");
		r.add("049110900152");
		r.add("049110900153");
		r.add("049110900154");
		r.add("049110900155");
		r.add("049110900156");
		r.add("049110900157");
		r.add("049110900158");
		r.add("049110900159");
		r.add("049110900160");
		r.add("049110900161");
		r.add("049110900167");
		r.add("049110900174");
		r.add("049110900175");
		r.add("049110900176");
		r.add("049110900177");
		r.add("049110900178");
		r.add("049110900179");
		r.add("049110900183");
		r.add("049110900184");
		r.add("049110900185");
		r.add("049110900187");
		r.add("049110900189");
		r.add("049110900190");
		r.add("049110900191");
		r.add("049110900192");
		r.add("049110900193");
		r.add("049110900194");
		r.add("049110900195");
		r.add("049110900198");
		r.add("049110900203");
		r.add("049110900204");
		r.add("049110900207");
		r.add("049110900208");
		r.add("049110900209");
		r.add("049110900214");
		r.add("049110900215");
		r.add("049110900216");
		r.add("049110900217");
		r.add("049110900219");
		r.add("049110900223");
		r.add("049110900224");
		r.add("049110900225");
		r.add("049110900228");
		r.add("049110900234");
		r.add("049110900235");
		r.add("049110900237");
		r.add("049110900238");
		r.add("049110900241");
		r.add("049110900278");
		r.add("049110900283");
		r.add("050110900105");
		r.add("050110900109");
		r.add("050110900110");
		r.add("050110900146");
		r.add("050110900148");
		r.add("050110900156");
		r.add("050110900162");
		r.add("050110900165");
		r.add("050110900166");
		r.add("050110900167");
		r.add("050110900168");
		r.add("050110900169");
		r.add("050110900170");
		r.add("050110900171");
		r.add("050110900172");
		r.add("050110900174");
		r.add("050110900176");
		r.add("050110900177");
		r.add("050110900179");
		r.add("050110900188");
		r.add("050110900196");
		r.add("050110900198");
		r.add("050110900200");
		r.add("050110900211");
		r.add("050110900212");
		r.add("050110900213");
		r.add("050110900215");
		r.add("050110900216");
		r.add("050110900217");
		r.add("050110900218");
		r.add("050110900219");
		r.add("050110900220");
		r.add("050110900222");
		r.add("050110900223");
		r.add("050110900224");
		r.add("050110900225");
		r.add("050110900226");
		r.add("050110900228");
		r.add("050110900230");
		r.add("050110900231");
		r.add("050110900232");
		r.add("050110900234");
		r.add("050110900236");
		r.add("050110900238");
		r.add("050110900239");
		r.add("050110900240");
		r.add("050110900241");
		r.add("050110900245");
		r.add("050110900265");
		r.add("050110900266");
		r.add("050110900268");
		r.add("050110900269");
		r.add("050110900270");
		r.add("050110900271");
		r.add("050110900272");
		r.add("050110900275");
		r.add("050110900276");
		r.add("050110900277");
		r.add("050110900278");
		r.add("050110900279");
		r.add("050110900280");
		r.add("050110900281");
		r.add("050110900282");
		r.add("050110900283");
		r.add("050110900284");
		r.add("050110900285");
		r.add("050110900286");
		r.add("050110900287");
		r.add("050110900288");
		r.add("050110900289");
		r.add("050110900290");
		r.add("050110900291");
		r.add("050110900292");
		r.add("050110900295");
		r.add("050110900296");
		r.add("050110900297");
		r.add("050110900299");
		r.add("050110900300");
		r.add("050110900301");
		r.add("050110900302");
		r.add("050110900303");
		r.add("050110900305");
		r.add("050110900306");
		r.add("050110900307");
		r.add("050110900316");
		r.add("050110900317");
		r.add("050110900318");
		r.add("050110900321");
		r.add("050110900322");
		r.add("050110900323");
		r.add("050110900325");
		r.add("050110900326");
		r.add("050110900327");
		r.add("050110900328");
		r.add("050110900329");
		r.add("050110900330");
		r.add("050110900332");
		r.add("050110900333");
		r.add("050110900336");
		r.add("050110900337");
		r.add("050110900338");
		r.add("050110900339");
		r.add("050110900340");
		r.add("050110900341");
		r.add("050110900342");
		r.add("050110900343");
		r.add("050110900344");
		r.add("050110900346");
		r.add("050110900347");
		r.add("050110900349");
		r.add("050110900350");
		r.add("050110900351");
		r.add("050110900352");
		r.add("050110900353");
		r.add("050110900355");
		r.add("050110900356");
		r.add("050110900357");
		r.add("050110900358");
		r.add("050110900359");
		r.add("050110900360");
		r.add("050110900361");
		r.add("050110900362");
		r.add("050110900363");
		r.add("050110900364");
		r.add("050110900368");
		r.add("050110900369");
		r.add("050110900371");
		r.add("050110900372");
		r.add("050110900373");
		r.add("050110900374");
		r.add("050110900375");
		r.add("050110900376");
		r.add("050110900377");
		r.add("050110900378");
		r.add("050110900379");
		r.add("050110900381");
		r.add("050110900383");
		r.add("050110900384");
		r.add("050110900385");
		r.add("050110900387");
		r.add("050110900388");
		r.add("050110900389");
		r.add("050110900390");
		r.add("050110900392");
		r.add("050110900393");
		r.add("050110900394");
		r.add("050110900396");
		r.add("050110900397");
		r.add("050110900399");
		r.add("050110900400");
		r.add("050110900401");
		r.add("050110900402");
		r.add("050110900404");
		r.add("050110900405");
		r.add("050110900406");
		r.add("050110900407");
		r.add("050110900408");
		r.add("050110900409");
		r.add("050110900410");
		r.add("050110900411");
		r.add("050110900413");
		r.add("050110900414");
		r.add("050110900415");
		r.add("050110900416");
		r.add("050110900417");
		r.add("050110900418");
		r.add("050110900419");
		r.add("050110900420");
		r.add("050110900422");
		r.add("050110900423");
		r.add("050110900424");
		r.add("050110900425");
		r.add("050110900426");
		r.add("050110900427");
		r.add("050110900428");
		r.add("050110900430");
		r.add("050110900432");
		r.add("050110900434");
		r.add("050110900435");
		r.add("050110900436");
		r.add("050110900437");
		r.add("050110900438");
		r.add("050110900439");
		r.add("050110900440");
		r.add("050110900441");
		r.add("050110900442");
		r.add("050110900444");
		r.add("050110900445");
		r.add("050110900446");
		r.add("050110900448");
		r.add("050110900449");
		r.add("050110900454");
		r.add("050110900456");
		r.add("050110900457");
		r.add("050110900458");
		r.add("050110900459");
		r.add("050110900460");
		r.add("050110900461");
		r.add("050110900462");
		r.add("050110900463");
		r.add("050110900464");
		r.add("050110900465");
		r.add("050110900467");
		r.add("050110900468");
		r.add("050110900469");
		r.add("050110900470");
		r.add("050110900471");
		r.add("050110900472");
		r.add("050110900473");
		r.add("050110900474");
		r.add("050110900476");
		r.add("050110900477");
		r.add("050110900478");
		r.add("050110900479");
		r.add("050110900480");
		r.add("050110900481");
		r.add("050110900482");
		r.add("050110900483");
		r.add("050110900484");
		r.add("050110900485");
		r.add("050110900486");
		r.add("050110900487");
		r.add("050110900488");
		r.add("050110900489");
		r.add("050110900490");
		r.add("050110900491");
		r.add("050110900492");
		r.add("050110900493");
		r.add("050110900494");
		r.add("050110900495");
		r.add("050110900497");
		r.add("050110900498");
		r.add("050110900499");
		r.add("050110900500");
		r.add("050110900501");
		r.add("050110900502");
		r.add("050110900503");
		r.add("050110900504");
		r.add("050110900505");
		r.add("050110900506");
		r.add("050110900508");
		r.add("050110900511");
		r.add("050110900513");
		r.add("050110900517");
		r.add("050110900518");
		r.add("050110900526");
		r.add("050110900529");
		r.add("050110900530");
		r.add("050110900534");
		r.add("050110900535");
		r.add("050110900548");
		r.add("050110900552");
		r.add("050110900556");
		r.add("050110900558");
		r.add("050110900567");
		r.add("050110900569");
		r.add("050110900570");
		r.add("050110900620");
		r.add("050110900630");
		r.add("050110900653");
		r.add("050110900654");
		r.add("050110900656");
		r.add("050110900658");
		r.add("050110900662");
		r.add("050110900664");
		r.add("050110900666");
		r.add("050110900667");
		r.add("051110900187");
		r.add("051110900240");
		r.add("051110900245");
		r.add("051110900247");
		r.add("051110900250");
		r.add("051110900252");
		r.add("051110900262");
		r.add("051110900263");
		r.add("051110900265");
		r.add("051110900267");
		r.add("051110900270");
		r.add("051110900271");
		r.add("051110900272");
		r.add("051110900273");
		r.add("051110900276");
		r.add("051110900277");
		r.add("051110900279");
		r.add("051110900282");
		r.add("051110900283");
		r.add("051110900285");
		r.add("051110900287");
		r.add("051110900292");
		r.add("051110900298");
		r.add("051110900299");
		r.add("051110900300");
		r.add("051110900304");
		r.add("051110900305");
		r.add("051110900307");
		r.add("051110900309");
		r.add("051110900318");
		r.add("051110900319");
		r.add("051110900320");
		r.add("051110900321");
		r.add("051110900323");
		r.add("051110900331");
		r.add("051110900339");
		r.add("051110900340");
		r.add("051110900342");
		r.add("051110900343");
		r.add("051110900344");
		r.add("051110900345");
		r.add("051110900348");
		r.add("051110900349");
		r.add("051110900350");
		r.add("051110900351");
		r.add("051110900353");
		r.add("051110900354");
		r.add("051110900355");
		r.add("051110900356");
		r.add("051110900357");
		r.add("051110900358");
		r.add("051110900359");
		r.add("051110900360");
		r.add("051110900361");
		r.add("051110900362");
		r.add("051110900372");
		r.add("051110900373");
		r.add("051110900374");
		r.add("051110900375");
		r.add("051110900376");
		r.add("051110900378");
		r.add("051110900383");
		r.add("051110900384");
		r.add("051110900386");
		r.add("051110900390");
		r.add("051110900393");
		r.add("051110900411");
		r.add("051110900416");
		r.add("051110900418");
		r.add("051110900419");
		r.add("051110900423");
		r.add("051110900427");
		r.add("051110900430");
		r.add("051110900431");
		r.add("051110900432");
		r.add("051110900455");
		r.add("051110900457");
		r.add("051110900460");
		r.add("051110900468");
		r.add("051110900479");
		r.add("052110900089");
		r.add("052110900105");
		r.add("052110900124");
		r.add("052110900188");
		r.add("052110900196");
		r.add("052110900201");
		r.add("052110900268");
		r.add("052110900279");
		r.add("052110900291");
		r.add("052110900299");
		r.add("052110900313");
		r.add("052110900315");
		r.add("052110900322");
		r.add("052110900323");
		r.add("052110900326");
		r.add("052110900327");
		r.add("052110900349");
		r.add("052110900358");
		r.add("052110900364");
		r.add("052110900365");
		r.add("052110900384");
		r.add("052110900390");
		r.add("052110900396");
		r.add("052110900401");
		r.add("052110900407");
		r.add("052110900409");
		r.add("052110900411");
		r.add("052110900417");
		r.add("052110900418");
		r.add("052110900422");
		r.add("052110900423");
		r.add("052110900424");
		r.add("052110900426");
		r.add("052110900432");
		r.add("052110900433");
		r.add("052110900434");
		r.add("052110900435");
		r.add("052110900440");
		r.add("052110900442");
		r.add("052110900445");
		r.add("052110900453");
		r.add("052110900454");
		r.add("052110900459");
		r.add("052110900465");
		r.add("052110900469");
		r.add("052110900472");
		r.add("052110900475");
		r.add("052110900481");
		r.add("052110900483");
		r.add("052110900484");
		r.add("052110900489");
		r.add("052110900495");
		r.add("052110900496");
		r.add("052110900499");
		r.add("052110900501");
		r.add("052110900503");
		r.add("052110900507");
		r.add("052110900510");
		r.add("052110900512");
		r.add("052110900513");
		r.add("052110900515");
		r.add("052110900520");
		r.add("052110900521");
		r.add("052110900524");
		r.add("052110900525");
		r.add("052110900526");
		r.add("052110900528");
		r.add("052110900531");
		r.add("052110900532");
		r.add("052110900534");
		r.add("052110900535");
		r.add("052110900539");
		r.add("052110900542");
		r.add("052110900543");
		r.add("052110900545");
		r.add("052110900546");
		r.add("052110900548");
		r.add("052110900553");
		r.add("052110900568");
		r.add("052110900574");
		r.add("052110900575");
		r.add("052110900576");
		r.add("052110900594");
		r.add("052110900598");
		r.add("052110900613");
		r.add("052110900625");
		r.add("052110900664");
		r.add("052110900667");
		r.add("052110900669");
		r.add("052110900674");
		r.add("052110900687");
		r.add("052110900710");
		r.add("052110900724");
		r.add("052110900733");
		r.add("053110900146");
		r.add("053110900173");
		r.add("053110900177");
		r.add("053110900178");
		r.add("053110900179");
		r.add("053110900180");
		r.add("053110900183");
		r.add("053110900184");
		r.add("053110900188");
		r.add("053110900189");
		r.add("053110900190");
		r.add("053110900191");
		r.add("053110900192");
		r.add("053110900195");
		r.add("053110900196");
		r.add("053110900202");
		r.add("053110900210");
		r.add("053110900212");
		r.add("053110900214");
		r.add("053110900215");
		r.add("053110900219");
		r.add("053110900224");
		r.add("053110900228");
		r.add("053110900229");
		r.add("053110900231");
		r.add("053110900232");
		r.add("053110900233");
		r.add("053110900234");
		r.add("053110900235");
		r.add("053110900236");
		r.add("053110900237");
		r.add("053110900239");
		r.add("053110900240");
		r.add("053110900243");
		r.add("053110900244");
		r.add("053110900246");
		r.add("053110900247");
		r.add("053110900248");
		r.add("053110900249");
		r.add("053110900250");
		r.add("053110900251");
		r.add("053110900252");
		r.add("053110900254");
		r.add("053110900255");
		r.add("053110900257");
		r.add("053110900261");
		r.add("053110900262");
		r.add("053110900264");
		r.add("053110900267");
		r.add("053110900268");
		r.add("053110900272");
		r.add("053110900274");
		r.add("053110900275");
		r.add("053110900276");
		r.add("053110900277");
		r.add("053110900278");
		r.add("053110900279");
		r.add("053110900280");
		r.add("053110900282");
		r.add("053110900283");
		r.add("053110900284");
		r.add("053110900285");
		r.add("053110900286");
		r.add("053110900287");
		r.add("053110900288");
		r.add("053110900289");
		r.add("053110900291");
		r.add("053110900292");
		r.add("053110900293");
		r.add("053110900294");
		r.add("053110900295");
		r.add("053110900296");
		r.add("053110900297");
		r.add("053110900298");
		r.add("053110900299");
		r.add("053110900300");
		r.add("053110900301");
		r.add("053110900302");
		r.add("053110900303");
		r.add("053110900305");
		r.add("053110900306");
		r.add("053110900307");
		r.add("053110900308");
		r.add("053110900309");
		r.add("053110900310");
		r.add("053110900311");
		r.add("053110900312");
		r.add("053110900313");
		r.add("053110900314");
		r.add("053110900315");
		r.add("053110900316");
		r.add("053110900317");
		r.add("053110900318");
		r.add("053110900320");
		r.add("053110900321");
		r.add("053110900322");
		r.add("053110900324");
		r.add("053110900325");
		r.add("053110900326");
		r.add("053110900327");
		r.add("053110900328");
		r.add("053110900329");
		r.add("053110900330");
		r.add("053110900331");
		r.add("053110900332");
		r.add("053110900333");
		r.add("053110900334");
		r.add("053110900335");
		r.add("053110900336");
		r.add("053110900337");
		r.add("053110900338");
		r.add("053110900339");
		r.add("053110900340");
		r.add("053110900341");
		r.add("053110900342");
		r.add("053110900344");
		r.add("053110900345");
		r.add("053110900346");
		r.add("053110900347");
		r.add("053110900349");
		r.add("053110900350");
		r.add("053110900351");
		r.add("053110900352");
		r.add("053110900353");
		r.add("053110900354");
		r.add("053110900356");
		r.add("053110900357");
		r.add("053110900359");
		r.add("053110900360");
		r.add("053110900362");
		r.add("053110900363");
		r.add("053110900364");
		r.add("053110900365");
		r.add("053110900367");
		r.add("053110900370");
		r.add("053110900372");
		r.add("053110900381");
		r.add("053110900383");
		r.add("053110900397");

		return r;
	}
	public static List<String> upd_ELF459_SRCFLAG_prodKind69_noAgreeQueryEjVer_2020_part2(){ //noAgreeQueryEjVer_2020_part2 共 3971筆
		List<String> r = new ArrayList<String>();
		r.add("053110900401");
		r.add("053110900403");
		r.add("053110900410");
		r.add("053110900417");
		r.add("053110900418");
		r.add("053110900420");
		r.add("053110900428");
		r.add("053110900442");
		r.add("053110900443");
		r.add("053110900451");
		r.add("053110900468");
		r.add("053110900476");
		r.add("053110900481");
		r.add("053110900501");
		r.add("053110900502");
		r.add("053110900508");
		r.add("053110900516");
		r.add("053110900518");
		r.add("053110900524");
		r.add("053110900525");
		r.add("053110900526");
		r.add("055110900299");
		r.add("055110900314");
		r.add("055110900330");
		r.add("055110900342");
		r.add("055110900349");
		r.add("055110900351");
		r.add("055110900371");
		r.add("055110900375");
		r.add("055110900377");
		r.add("055110900379");
		r.add("055110900387");
		r.add("055110900396");
		r.add("055110900397");
		r.add("055110900445");
		r.add("055110900449");
		r.add("055110900456");
		r.add("056110900068");
		r.add("056110900108");
		r.add("056110900109");
		r.add("056110900110");
		r.add("056110900115");
		r.add("056110900123");
		r.add("056110900135");
		r.add("056110900136");
		r.add("056110900138");
		r.add("056110900139");
		r.add("056110900141");
		r.add("056110900142");
		r.add("056110900145");
		r.add("056110900148");
		r.add("056110900151");
		r.add("056110900152");
		r.add("056110900153");
		r.add("056110900159");
		r.add("056110900164");
		r.add("056110900165");
		r.add("056110900166");
		r.add("056110900180");
		r.add("056110900187");
		r.add("056110900203");
		r.add("056110900205");
		r.add("056110900212");
		r.add("056110900213");
		r.add("056110900215");
		r.add("056110900216");
		r.add("056110900220");
		r.add("057110900114");
		r.add("057110900144");
		r.add("057110900147");
		r.add("057110900151");
		r.add("057110900166");
		r.add("057110900170");
		r.add("057110900171");
		r.add("057110900196");
		r.add("057110900197");
		r.add("057110900199");
		r.add("057110900200");
		r.add("057110900201");
		r.add("057110900213");
		r.add("057110900222");
		r.add("057110900231");
		r.add("057110900233");
		r.add("057110900236");
		r.add("057110900239");
		r.add("057110900241");
		r.add("057110900248");
		r.add("057110900257");
		r.add("057110900262");
		r.add("057110900269");
		r.add("058110900110");
		r.add("058110900123");
		r.add("058110900125");
		r.add("058110900138");
		r.add("058110900145");
		r.add("058110900153");
		r.add("058110900162");
		r.add("058110900172");
		r.add("058110900173");
		r.add("058110900182");
		r.add("058110900185");
		r.add("058110900234");
		r.add("058110900238");
		r.add("058110900246");
		r.add("058110900250");
		r.add("058110900251");
		r.add("058110900252");
		r.add("058110900257");
		r.add("058110900258");
		r.add("058110900279");
		r.add("058110900302");
		r.add("058110900303");
		r.add("058110900305");
		r.add("058110900310");
		r.add("058110900314");
		r.add("058110900315");
		r.add("058110900323");
		r.add("058110900327");
		r.add("058110900329");
		r.add("058110900336");
		r.add("058110900339");
		r.add("058110900348");
		r.add("058110900355");
		r.add("058110900365");
		r.add("058110900368");
		r.add("058110900370");
		r.add("058110900372");
		r.add("058110900375");
		r.add("058110900383");
		r.add("058110900386");
		r.add("058110900387");
		r.add("058110900391");
		r.add("058110900396");
		r.add("058110900397");
		r.add("058110900401");
		r.add("058110900402");
		r.add("058110900406");
		r.add("058110900411");
		r.add("058110900414");
		r.add("058110900420");
		r.add("058110900421");
		r.add("058110900433");
		r.add("058110900436");
		r.add("058110900450");
		r.add("058110900454");
		r.add("058110900455");
		r.add("058110900456");
		r.add("058110900463");
		r.add("058110900466");
		r.add("058110900467");
		r.add("058110900484");
		r.add("058110900491");
		r.add("058110900510");
		r.add("058110900512");
		r.add("058110900513");
		r.add("058110900516");
		r.add("058110900517");
		r.add("058110900521");
		r.add("058110900523");
		r.add("058110900542");
		r.add("058110900545");
		r.add("058110900546");
		r.add("058110900551");
		r.add("058110900552");
		r.add("058110900554");
		r.add("058110900555");
		r.add("058110900557");
		r.add("058110900562");
		r.add("058110900563");
		r.add("058110900567");
		r.add("058110900569");
		r.add("058110900571");
		r.add("058110900602");
		r.add("058110900608");
		r.add("058110900621");
		r.add("058110900630");
		r.add("058110900643");
		r.add("058110900673");
		r.add("058110900679");
		r.add("058110900681");
		r.add("058110900688");
		r.add("058110900708");
		r.add("058110900710");
		r.add("058110900719");
		r.add("058110900765");
		r.add("058110900767");
		r.add("058110900773");
		r.add("059110900200");
		r.add("059110900203");
		r.add("059110900207");
		r.add("059110900280");
		r.add("059110900281");
		r.add("059110900288");
		r.add("059110900295");
		r.add("059110900298");
		r.add("059110900299");
		r.add("059110900303");
		r.add("059110900304");
		r.add("059110900306");
		r.add("059110900308");
		r.add("059110900310");
		r.add("059110900312");
		r.add("059110900313");
		r.add("059110900316");
		r.add("059110900317");
		r.add("059110900318");
		r.add("059110900322");
		r.add("059110900324");
		r.add("059110900328");
		r.add("059110900333");
		r.add("059110900339");
		r.add("059110900340");
		r.add("059110900341");
		r.add("059110900342");
		r.add("059110900343");
		r.add("059110900347");
		r.add("059110900348");
		r.add("059110900351");
		r.add("059110900354");
		r.add("059110900363");
		r.add("059110900365");
		r.add("059110900366");
		r.add("059110900367");
		r.add("059110900370");
		r.add("059110900371");
		r.add("059110900417");
		r.add("059110900431");
		r.add("060110900110");
		r.add("060110900126");
		r.add("060110900201");
		r.add("060110900211");
		r.add("060110900223");
		r.add("060110900230");
		r.add("060110900232");
		r.add("060110900240");
		r.add("060110900256");
		r.add("060110900257");
		r.add("060110900300");
		r.add("060110900301");
		r.add("060110900302");
		r.add("060110900307");
		r.add("060110900309");
		r.add("060110900310");
		r.add("060110900321");
		r.add("060110900325");
		r.add("060110900329");
		r.add("060110900330");
		r.add("060110900337");
		r.add("060110900343");
		r.add("060110900347");
		r.add("060110900355");
		r.add("060110900370");
		r.add("060110900372");
		r.add("060110900390");
		r.add("060110900393");
		r.add("060110900395");
		r.add("060110900396");
		r.add("060110900404");
		r.add("060110900406");
		r.add("060110900408");
		r.add("060110900409");
		r.add("060110900411");
		r.add("060110900412");
		r.add("060110900415");
		r.add("060110900416");
		r.add("060110900418");
		r.add("060110900419");
		r.add("060110900426");
		r.add("060110900442");
		r.add("060110900445");
		r.add("060110900451");
		r.add("060110900459");
		r.add("060110900460");
		r.add("060110900466");
		r.add("060110900472");
		r.add("060110900475");
		r.add("060110900476");
		r.add("060110900477");
		r.add("060110900478");
		r.add("060110900482");
		r.add("060110900488");
		r.add("060110900490");
		r.add("060110900492");
		r.add("060110900495");
		r.add("060110900496");
		r.add("060110900497");
		r.add("060110900498");
		r.add("060110900499");
		r.add("060110900502");
		r.add("060110900504");
		r.add("060110900507");
		r.add("060110900509");
		r.add("060110900511");
		r.add("060110900516");
		r.add("060110900533");
		r.add("060110900544");
		r.add("060110900547");
		r.add("060110900552");
		r.add("060110900556");
		r.add("060110900557");
		r.add("060110900558");
		r.add("060110900562");
		r.add("060110900565");
		r.add("060110900566");
		r.add("060110900575");
		r.add("060110900583");
		r.add("060110900597");
		r.add("060110900603");
		r.add("060110900606");
		r.add("060110900658");
		r.add("060110900670");
		r.add("060110900684");
		r.add("060110900702");
		r.add("061110900140");
		r.add("061110900141");
		r.add("061110900180");
		r.add("061110900182");
		r.add("061110900187");
		r.add("061110900206");
		r.add("061110900210");
		r.add("061110900212");
		r.add("061110900216");
		r.add("061110900218");
		r.add("061110900219");
		r.add("061110900222");
		r.add("061110900224");
		r.add("061110900226");
		r.add("061110900227");
		r.add("061110900231");
		r.add("061110900236");
		r.add("061110900237");
		r.add("061110900239");
		r.add("061110900240");
		r.add("061110900243");
		r.add("061110900244");
		r.add("061110900248");
		r.add("061110900251");
		r.add("061110900252");
		r.add("061110900257");
		r.add("061110900258");
		r.add("061110900264");
		r.add("061110900267");
		r.add("061110900269");
		r.add("061110900276");
		r.add("061110900277");
		r.add("061110900278");
		r.add("061110900283");
		r.add("061110900288");
		r.add("061110900290");
		r.add("061110900316");
		r.add("061110900317");
		r.add("061110900323");
		r.add("061110900327");
		r.add("061110900330");
		r.add("061110900343");
		r.add("061110900359");
		r.add("061110900378");
		r.add("061110900405");
		r.add("061110900407");
		r.add("061110900423");
		r.add("061110900424");
		r.add("061110900425");
		r.add("061110900429");
		r.add("061110900440");
		r.add("062110900158");
		r.add("062110900175");
		r.add("062110900207");
		r.add("062110900209");
		r.add("062110900210");
		r.add("062110900212");
		r.add("062110900217");
		r.add("062110900221");
		r.add("062110900222");
		r.add("062110900223");
		r.add("062110900243");
		r.add("062110900245");
		r.add("062110900246");
		r.add("062110900250");
		r.add("062110900252");
		r.add("062110900257");
		r.add("062110900262");
		r.add("062110900269");
		r.add("062110900270");
		r.add("062110900271");
		r.add("062110900278");
		r.add("062110900284");
		r.add("062110900292");
		r.add("062110900293");
		r.add("062110900309");
		r.add("062110900312");
		r.add("062110900315");
		r.add("062110900319");
		r.add("062110900323");
		r.add("062110900325");
		r.add("062110900333");
		r.add("062110900349");
		r.add("062110900354");
		r.add("062110900361");
		r.add("062110900365");
		r.add("063110900104");
		r.add("063110900108");
		r.add("063110900135");
		r.add("063110900145");
		r.add("063110900155");
		r.add("063110900156");
		r.add("063110900157");
		r.add("063110900158");
		r.add("063110900163");
		r.add("063110900164");
		r.add("063110900165");
		r.add("063110900167");
		r.add("063110900168");
		r.add("063110900169");
		r.add("063110900170");
		r.add("063110900180");
		r.add("063110900181");
		r.add("063110900190");
		r.add("063110900198");
		r.add("063110900199");
		r.add("063110900204");
		r.add("063110900205");
		r.add("063110900206");
		r.add("063110900214");
		r.add("063110900216");
		r.add("063110900217");
		r.add("063110900219");
		r.add("063110900222");
		r.add("063110900225");
		r.add("063110900229");
		r.add("063110900230");
		r.add("063110900231");
		r.add("063110900236");
		r.add("063110900239");
		r.add("063110900241");
		r.add("063110900243");
		r.add("063110900244");
		r.add("063110900254");
		r.add("063110900255");
		r.add("063110900257");
		r.add("063110900263");
		r.add("063110900358");
		r.add("063110900366");
		r.add("063110900369");
		r.add("064110900064");
		r.add("064110900088");
		r.add("064110900141");
		r.add("064110900143");
		r.add("064110900144");
		r.add("064110900145");
		r.add("064110900147");
		r.add("064110900150");
		r.add("064110900152");
		r.add("064110900154");
		r.add("064110900155");
		r.add("064110900157");
		r.add("064110900158");
		r.add("064110900160");
		r.add("064110900164");
		r.add("064110900165");
		r.add("064110900167");
		r.add("064110900169");
		r.add("064110900170");
		r.add("064110900171");
		r.add("064110900172");
		r.add("064110900173");
		r.add("064110900176");
		r.add("064110900179");
		r.add("064110900180");
		r.add("064110900182");
		r.add("064110900184");
		r.add("064110900193");
		r.add("064110900194");
		r.add("064110900214");
		r.add("064110900215");
		r.add("064110900216");
		r.add("064110900219");
		r.add("064110900221");
		r.add("064110900223");
		r.add("064110900225");
		r.add("064110900233");
		r.add("064110900234");
		r.add("064110900236");
		r.add("064110900239");
		r.add("064110900240");
		r.add("064110900242");
		r.add("064110900244");
		r.add("064110900247");
		r.add("064110900249");
		r.add("064110900251");
		r.add("064110900252");
		r.add("064110900254");
		r.add("064110900257");
		r.add("064110900258");
		r.add("064110900261");
		r.add("064110900262");
		r.add("064110900263");
		r.add("064110900264");
		r.add("064110900268");
		r.add("064110900270");
		r.add("064110900273");
		r.add("064110900274");
		r.add("064110900275");
		r.add("064110900279");
		r.add("064110900280");
		r.add("064110900282");
		r.add("064110900283");
		r.add("064110900286");
		r.add("064110900288");
		r.add("064110900292");
		r.add("064110900293");
		r.add("064110900294");
		r.add("064110900295");
		r.add("064110900296");
		r.add("064110900298");
		r.add("064110900302");
		r.add("064110900303");
		r.add("064110900304");
		r.add("064110900306");
		r.add("064110900307");
		r.add("064110900308");
		r.add("064110900309");
		r.add("064110900310");
		r.add("064110900311");
		r.add("064110900312");
		r.add("064110900313");
		r.add("064110900315");
		r.add("064110900317");
		r.add("064110900318");
		r.add("064110900319");
		r.add("064110900320");
		r.add("064110900321");
		r.add("064110900322");
		r.add("064110900323");
		r.add("064110900324");
		r.add("064110900325");
		r.add("064110900326");
		r.add("064110900327");
		r.add("064110900328");
		r.add("064110900329");
		r.add("064110900331");
		r.add("064110900332");
		r.add("064110900333");
		r.add("064110900334");
		r.add("064110900335");
		r.add("064110900336");
		r.add("064110900337");
		r.add("064110900338");
		r.add("064110900339");
		r.add("064110900340");
		r.add("064110900341");
		r.add("064110900342");
		r.add("064110900343");
		r.add("064110900344");
		r.add("064110900345");
		r.add("064110900346");
		r.add("064110900347");
		r.add("064110900348");
		r.add("064110900349");
		r.add("064110900350");
		r.add("064110900351");
		r.add("064110900352");
		r.add("064110900353");
		r.add("064110900354");
		r.add("064110900355");
		r.add("064110900356");
		r.add("064110900357");
		r.add("064110900362");
		r.add("064110900363");
		r.add("064110900364");
		r.add("064110900366");
		r.add("064110900367");
		r.add("064110900368");
		r.add("064110900369");
		r.add("064110900370");
		r.add("064110900371");
		r.add("064110900372");
		r.add("064110900373");
		r.add("064110900374");
		r.add("064110900375");
		r.add("064110900376");
		r.add("064110900377");
		r.add("064110900378");
		r.add("064110900379");
		r.add("064110900380");
		r.add("064110900381");
		r.add("064110900382");
		r.add("064110900383");
		r.add("064110900384");
		r.add("064110900386");
		r.add("064110900387");
		r.add("064110900388");
		r.add("064110900389");
		r.add("064110900390");
		r.add("064110900396");
		r.add("064110900397");
		r.add("064110900398");
		r.add("064110900399");
		r.add("064110900400");
		r.add("064110900401");
		r.add("064110900402");
		r.add("064110900403");
		r.add("064110900404");
		r.add("064110900405");
		r.add("064110900406");
		r.add("064110900407");
		r.add("064110900408");
		r.add("064110900409");
		r.add("064110900410");
		r.add("064110900414");
		r.add("064110900415");
		r.add("064110900416");
		r.add("064110900417");
		r.add("064110900422");
		r.add("064110900430");
		r.add("064110900431");
		r.add("064110900433");
		r.add("064110900434");
		r.add("064110900435");
		r.add("064110900436");
		r.add("064110900439");
		r.add("064110900451");
		r.add("064110900453");
		r.add("064110900454");
		r.add("064110900470");
		r.add("064110900478");
		r.add("064110900501");
		r.add("064110900510");
		r.add("064110900511");
		r.add("064110900513");
		r.add("064110900514");
		r.add("064110900515");
		r.add("064110900518");
		r.add("064110900520");
		r.add("064110900521");
		r.add("064110900522");
		r.add("064110900525");
		r.add("064110900526");
		r.add("064110900529");
		r.add("064110900530");
		r.add("064110900534");
		r.add("064110900535");
		r.add("064110900539");
		r.add("065110900167");
		r.add("065110900272");
		r.add("065110900275");
		r.add("065110900279");
		r.add("065110900283");
		r.add("065110900290");
		r.add("065110900298");
		r.add("065110900301");
		r.add("065110900309");
		r.add("065110900311");
		r.add("065110900313");
		r.add("065110900314");
		r.add("065110900319");
		r.add("065110900321");
		r.add("065110900332");
		r.add("065110900337");
		r.add("065110900348");
		r.add("065110900350");
		r.add("065110900360");
		r.add("065110900361");
		r.add("065110900365");
		r.add("065110900366");
		r.add("065110900368");
		r.add("065110900369");
		r.add("065110900372");
		r.add("065110900378");
		r.add("065110900379");
		r.add("065110900380");
		r.add("065110900381");
		r.add("065110900383");
		r.add("065110900384");
		r.add("065110900386");
		r.add("065110900396");
		r.add("065110900406");
		r.add("065110900416");
		r.add("065110900419");
		r.add("065110900420");
		r.add("065110900421");
		r.add("065110900425");
		r.add("065110900429");
		r.add("065110900432");
		r.add("065110900433");
		r.add("065110900435");
		r.add("065110900438");
		r.add("065110900439");
		r.add("065110900441");
		r.add("065110900449");
		r.add("065110900467");
		r.add("065110900480");
		r.add("065110900487");
		r.add("065110900496");
		r.add("065110900498");
		r.add("065110900503");
		r.add("065110900505");
		r.add("065110900515");
		r.add("065110900516");
		r.add("065110900519");
		r.add("065110900528");
		r.add("065110900585");
		r.add("065110900597");
		r.add("065110900604");
		r.add("065110900616");
		r.add("065110900635");
		r.add("065110900645");
		r.add("066110900075");
		r.add("066110900077");
		r.add("066110900083");
		r.add("066110900091");
		r.add("066110900107");
		r.add("066110900134");
		r.add("066110900135");
		r.add("066110900150");
		r.add("066110900153");
		r.add("066110900154");
		r.add("066110900156");
		r.add("066110900164");
		r.add("066110900166");
		r.add("066110900169");
		r.add("066110900174");
		r.add("066110900185");
		r.add("066110900186");
		r.add("066110900188");
		r.add("066110900190");
		r.add("066110900191");
		r.add("066110900192");
		r.add("066110900193");
		r.add("066110900198");
		r.add("066110900200");
		r.add("066110900201");
		r.add("066110900202");
		r.add("066110900211");
		r.add("066110900212");
		r.add("066110900216");
		r.add("066110900226");
		r.add("066110900228");
		r.add("066110900229");
		r.add("066110900230");
		r.add("066110900231");
		r.add("066110900233");
		r.add("066110900235");
		r.add("066110900238");
		r.add("066110900240");
		r.add("066110900241");
		r.add("066110900243");
		r.add("066110900244");
		r.add("066110900246");
		r.add("066110900250");
		r.add("066110900251");
		r.add("066110900252");
		r.add("066110900253");
		r.add("066110900254");
		r.add("066110900255");
		r.add("066110900256");
		r.add("066110900259");
		r.add("066110900260");
		r.add("066110900266");
		r.add("066110900269");
		r.add("066110900271");
		r.add("066110900274");
		r.add("066110900275");
		r.add("066110900277");
		r.add("066110900278");
		r.add("066110900280");
		r.add("066110900282");
		r.add("066110900285");
		r.add("066110900289");
		r.add("066110900291");
		r.add("066110900294");
		r.add("066110900298");
		r.add("066110900299");
		r.add("066110900300");
		r.add("066110900303");
		r.add("066110900312");
		r.add("066110900325");
		r.add("066110900331");
		r.add("066110900354");
		r.add("067110900103");
		r.add("067110900129");
		r.add("067110900130");
		r.add("067110900131");
		r.add("067110900132");
		r.add("067110900133");
		r.add("067110900139");
		r.add("067110900146");
		r.add("067110900154");
		r.add("067110900157");
		r.add("067110900158");
		r.add("067110900159");
		r.add("067110900162");
		r.add("067110900165");
		r.add("067110900166");
		r.add("067110900179");
		r.add("067110900184");
		r.add("067110900185");
		r.add("067110900187");
		r.add("067110900189");
		r.add("067110900190");
		r.add("067110900191");
		r.add("067110900192");
		r.add("067110900193");
		r.add("067110900194");
		r.add("067110900195");
		r.add("067110900196");
		r.add("067110900197");
		r.add("067110900198");
		r.add("067110900202");
		r.add("067110900203");
		r.add("067110900204");
		r.add("067110900205");
		r.add("067110900206");
		r.add("067110900207");
		r.add("067110900208");
		r.add("067110900209");
		r.add("067110900210");
		r.add("067110900216");
		r.add("067110900222");
		r.add("067110900227");
		r.add("067110900235");
		r.add("067110900240");
		r.add("067110900241");
		r.add("067110900243");
		r.add("067110900253");
		r.add("067110900255");
		r.add("067110900262");
		r.add("067110900284");
		r.add("067110900302");
		r.add("067110900303");
		r.add("068110900106");
		r.add("068110900132");
		r.add("068110900155");
		r.add("068110900178");
		r.add("068110900181");
		r.add("068110900184");
		r.add("068110900186");
		r.add("068110900187");
		r.add("068110900191");
		r.add("068110900192");
		r.add("068110900193");
		r.add("068110900197");
		r.add("068110900198");
		r.add("068110900209");
		r.add("068110900211");
		r.add("068110900212");
		r.add("068110900216");
		r.add("068110900220");
		r.add("068110900222");
		r.add("068110900224");
		r.add("068110900225");
		r.add("068110900227");
		r.add("068110900228");
		r.add("068110900231");
		r.add("068110900232");
		r.add("068110900235");
		r.add("068110900236");
		r.add("068110900237");
		r.add("068110900239");
		r.add("068110900241");
		r.add("068110900242");
		r.add("068110900243");
		r.add("068110900248");
		r.add("068110900250");
		r.add("068110900255");
		r.add("068110900256");
		r.add("068110900258");
		r.add("068110900262");
		r.add("068110900265");
		r.add("068110900266");
		r.add("068110900267");
		r.add("068110900270");
		r.add("068110900271");
		r.add("068110900272");
		r.add("068110900273");
		r.add("068110900274");
		r.add("068110900275");
		r.add("068110900276");
		r.add("068110900277");
		r.add("068110900278");
		r.add("068110900279");
		r.add("068110900280");
		r.add("068110900281");
		r.add("068110900283");
		r.add("068110900284");
		r.add("068110900285");
		r.add("068110900286");
		r.add("068110900287");
		r.add("068110900288");
		r.add("068110900289");
		r.add("068110900290");
		r.add("068110900291");
		r.add("068110900294");
		r.add("068110900295");
		r.add("068110900296");
		r.add("068110900297");
		r.add("068110900298");
		r.add("068110900300");
		r.add("068110900302");
		r.add("068110900304");
		r.add("068110900305");
		r.add("068110900306");
		r.add("068110900307");
		r.add("068110900308");
		r.add("068110900309");
		r.add("068110900310");
		r.add("068110900311");
		r.add("068110900312");
		r.add("068110900313");
		r.add("068110900314");
		r.add("068110900315");
		r.add("068110900317");
		r.add("068110900318");
		r.add("068110900319");
		r.add("068110900320");
		r.add("068110900321");
		r.add("068110900322");
		r.add("068110900323");
		r.add("068110900324");
		r.add("068110900325");
		r.add("068110900326");
		r.add("068110900327");
		r.add("068110900328");
		r.add("068110900329");
		r.add("068110900330");
		r.add("068110900331");
		r.add("068110900332");
		r.add("068110900333");
		r.add("068110900334");
		r.add("068110900335");
		r.add("068110900336");
		r.add("068110900339");
		r.add("068110900340");
		r.add("068110900341");
		r.add("068110900344");
		r.add("068110900346");
		r.add("068110900347");
		r.add("068110900348");
		r.add("068110900349");
		r.add("068110900351");
		r.add("068110900352");
		r.add("068110900353");
		r.add("068110900354");
		r.add("068110900356");
		r.add("068110900357");
		r.add("068110900358");
		r.add("068110900361");
		r.add("068110900362");
		r.add("068110900363");
		r.add("068110900364");
		r.add("068110900365");
		r.add("068110900366");
		r.add("068110900368");
		r.add("068110900369");
		r.add("068110900370");
		r.add("068110900371");
		r.add("068110900372");
		r.add("068110900373");
		r.add("068110900374");
		r.add("068110900375");
		r.add("068110900376");
		r.add("068110900378");
		r.add("068110900379");
		r.add("068110900380");
		r.add("068110900385");
		r.add("068110900386");
		r.add("068110900387");
		r.add("068110900388");
		r.add("068110900389");
		r.add("068110900390");
		r.add("068110900391");
		r.add("068110900392");
		r.add("068110900393");
		r.add("068110900394");
		r.add("068110900395");
		r.add("068110900396");
		r.add("068110900397");
		r.add("068110900398");
		r.add("068110900399");
		r.add("068110900400");
		r.add("068110900402");
		r.add("068110900403");
		r.add("068110900416");
		r.add("068110900417");
		r.add("068110900441");
		r.add("068110900445");
		r.add("068110900452");
		r.add("068110900492");
		r.add("068110900510");
		r.add("068110900525");
		r.add("069110900365");
		r.add("069110900415");
		r.add("069110900417");
		r.add("069110900423");
		r.add("069110900472");
		r.add("069110900483");
		r.add("069110900490");
		r.add("069110900494");
		r.add("069110900499");
		r.add("069110900502");
		r.add("069110900506");
		r.add("069110900521");
		r.add("069110900526");
		r.add("069110900536");
		r.add("069110900563");
		r.add("069110900572");
		r.add("069110900577");
		r.add("069110900586");
		r.add("069110900587");
		r.add("069110900588");
		r.add("069110900590");
		r.add("069110900606");
		r.add("069110900610");
		r.add("069110900611");
		r.add("069110900612");
		r.add("069110900620");
		r.add("069110900621");
		r.add("069110900624");
		r.add("069110900625");
		r.add("069110900633");
		r.add("069110900635");
		r.add("069110900647");
		r.add("069110900650");
		r.add("069110900651");
		r.add("069110900653");
		r.add("069110900654");
		r.add("069110900657");
		r.add("069110900668");
		r.add("069110900679");
		r.add("069110900680");
		r.add("069110900681");
		r.add("069110900682");
		r.add("069110900683");
		r.add("069110900697");
		r.add("069110900698");
		r.add("069110900716");
		r.add("069110900727");
		r.add("069110900732");
		r.add("069110900734");
		r.add("069110900779");
		r.add("069110900780");
		r.add("069110900783");
		r.add("069110900794");
		r.add("069110900795");
		r.add("069110900819");
		r.add("069110900833");
		r.add("069110900834");
		r.add("069110900838");
		r.add("0701109T0101");
		r.add("0701109T0133");
		r.add("0701109T0140");
		r.add("0701109T0149");
		r.add("0701109T0171");
		r.add("0701109T0175");
		r.add("0701109T0185");
		r.add("0701109T0195");
		r.add("0701109T0203");
		r.add("0701109T0210");
		r.add("0701109T0219");
		r.add("0701109T0220");
		r.add("0701109T0224");
		r.add("0701109T0361");
		r.add("0701109T0390");
		r.add("0701109T0445");
		r.add("0701109T0574");
		r.add("0701109T0576");
		r.add("0701109T0578");
		r.add("0701109T0584");
		r.add("0701109T0599");
		r.add("0701109T0601");
		r.add("0701109T0603");
		r.add("0701109T0604");
		r.add("0701109T0606");
		r.add("0701109T0607");
		r.add("0701109T0609");
		r.add("0701109T0619");
		r.add("0701109T0629");
		r.add("0701109T0630");
		r.add("0701109T0632");
		r.add("0701109T0640");
		r.add("0701109T0642");
		r.add("0701109T0705");
		r.add("0701109U0231");
		r.add("0701109U0249");
		r.add("0701109U0250");
		r.add("0701109U0275");
		r.add("0701109U0285");
		r.add("0701109U0286");
		r.add("0701109U0289");
		r.add("0701109U0294");
		r.add("0701109U0310");
		r.add("0701109U0329");
		r.add("0701109U0334");
		r.add("070110900107");
		r.add("070110900111");
		r.add("070110900135");
		r.add("070110900136");
		r.add("070110900140");
		r.add("070110900141");
		r.add("070110900175");
		r.add("070110900176");
		r.add("070110900179");
		r.add("070110900189");
		r.add("070110900190");
		r.add("070110900192");
		r.add("070110900193");
		r.add("070110900194");
		r.add("070110900202");
		r.add("070110900203");
		r.add("070110900205");
		r.add("070110900210");
		r.add("070110900212");
		r.add("070110900215");
		r.add("070110900227");
		r.add("070110900228");
		r.add("070110900230");
		r.add("070110900231");
		r.add("070110900233");
		r.add("070110900235");
		r.add("070110900236");
		r.add("070110900238");
		r.add("070110900242");
		r.add("070110900243");
		r.add("070110900245");
		r.add("070110900246");
		r.add("070110900247");
		r.add("070110900250");
		r.add("070110900256");
		r.add("070110900257");
		r.add("070110900267");
		r.add("070110900298");
		r.add("070110900313");
		r.add("070110900314");
		r.add("070110900324");
		r.add("071110900254");
		r.add("071110900256");
		r.add("071110900257");
		r.add("071110900258");
		r.add("071110900270");
		r.add("071110900271");
		r.add("071110900272");
		r.add("071110900273");
		r.add("071110900274");
		r.add("071110900275");
		r.add("071110900276");
		r.add("071110900278");
		r.add("071110900279");
		r.add("071110900282");
		r.add("071110900289");
		r.add("071110900295");
		r.add("071110900296");
		r.add("071110900297");
		r.add("071110900298");
		r.add("071110900299");
		r.add("071110900300");
		r.add("071110900301");
		r.add("071110900302");
		r.add("071110900303");
		r.add("071110900305");
		r.add("071110900306");
		r.add("071110900307");
		r.add("071110900308");
		r.add("071110900309");
		r.add("071110900311");
		r.add("071110900312");
		r.add("071110900313");
		r.add("071110900314");
		r.add("071110900315");
		r.add("071110900316");
		r.add("071110900317");
		r.add("071110900318");
		r.add("071110900319");
		r.add("071110900320");
		r.add("071110900322");
		r.add("071110900323");
		r.add("071110900324");
		r.add("071110900325");
		r.add("071110900327");
		r.add("071110900328");
		r.add("071110900329");
		r.add("071110900330");
		r.add("071110900331");
		r.add("071110900335");
		r.add("071110900336");
		r.add("071110900338");
		r.add("071110900339");
		r.add("071110900340");
		r.add("071110900341");
		r.add("071110900342");
		r.add("071110900343");
		r.add("071110900344");
		r.add("071110900346");
		r.add("071110900347");
		r.add("071110900350");
		r.add("071110900351");
		r.add("071110900352");
		r.add("071110900353");
		r.add("071110900354");
		r.add("071110900355");
		r.add("071110900356");
		r.add("071110900357");
		r.add("071110900359");
		r.add("071110900360");
		r.add("071110900361");
		r.add("071110900362");
		r.add("071110900363");
		r.add("071110900364");
		r.add("071110900365");
		r.add("071110900366");
		r.add("071110900368");
		r.add("071110900369");
		r.add("071110900370");
		r.add("071110900371");
		r.add("071110900372");
		r.add("071110900373");
		r.add("071110900374");
		r.add("071110900375");
		r.add("071110900377");
		r.add("071110900378");
		r.add("071110900379");
		r.add("071110900382");
		r.add("071110900383");
		r.add("071110900384");
		r.add("071110900385");
		r.add("071110900386");
		r.add("071110900387");
		r.add("071110900390");
		r.add("071110900391");
		r.add("071110900392");
		r.add("071110900394");
		r.add("071110900395");
		r.add("071110900396");
		r.add("071110900397");
		r.add("071110900398");
		r.add("071110900399");
		r.add("071110900400");
		r.add("071110900401");
		r.add("071110900402");
		r.add("071110900403");
		r.add("071110900404");
		r.add("071110900405");
		r.add("071110900407");
		r.add("071110900408");
		r.add("071110900409");
		r.add("071110900410");
		r.add("071110900411");
		r.add("071110900413");
		r.add("071110900414");
		r.add("071110900415");
		r.add("071110900416");
		r.add("071110900417");
		r.add("071110900418");
		r.add("071110900419");
		r.add("071110900420");
		r.add("071110900421");
		r.add("071110900422");
		r.add("071110900424");
		r.add("071110900425");
		r.add("071110900426");
		r.add("071110900428");
		r.add("071110900429");
		r.add("071110900430");
		r.add("071110900431");
		r.add("071110900434");
		r.add("071110900436");
		r.add("071110900437");
		r.add("071110900439");
		r.add("071110900440");
		r.add("071110900442");
		r.add("071110900445");
		r.add("071110900446");
		r.add("071110900449");
		r.add("071110900454");
		r.add("071110900456");
		r.add("071110900461");
		r.add("071110900462");
		r.add("071110900463");
		r.add("071110900471");
		r.add("071110900500");
		r.add("071110900524");
		r.add("071110900562");
		r.add("072110900097");
		r.add("072110900098");
		r.add("072110900099");
		r.add("072110900100");
		r.add("072110900101");
		r.add("072110900102");
		r.add("072110900103");
		r.add("072110900105");
		r.add("072110900106");
		r.add("072110900107");
		r.add("072110900108");
		r.add("072110900109");
		r.add("072110900110");
		r.add("072110900111");
		r.add("072110900112");
		r.add("072110900113");
		r.add("072110900114");
		r.add("072110900115");
		r.add("072110900117");
		r.add("072110900118");
		r.add("072110900123");
		r.add("072110900124");
		r.add("072110900125");
		r.add("072110900126");
		r.add("072110900127");
		r.add("072110900128");
		r.add("072110900129");
		r.add("072110900130");
		r.add("072110900131");
		r.add("072110900134");
		r.add("072110900135");
		r.add("072110900137");
		r.add("072110900138");
		r.add("072110900140");
		r.add("072110900141");
		r.add("072110900142");
		r.add("072110900145");
		r.add("072110900147");
		r.add("072110900148");
		r.add("072110900150");
		r.add("072110900151");
		r.add("072110900152");
		r.add("072110900153");
		r.add("072110900154");
		r.add("072110900155");
		r.add("072110900156");
		r.add("072110900157");
		r.add("072110900158");
		r.add("072110900159");
		r.add("072110900160");
		r.add("072110900161");
		r.add("072110900162");
		r.add("072110900163");
		r.add("072110900164");
		r.add("072110900165");
		r.add("072110900166");
		r.add("072110900167");
		r.add("072110900168");
		r.add("072110900169");
		r.add("072110900170");
		r.add("072110900171");
		r.add("072110900172");
		r.add("072110900173");
		r.add("072110900174");
		r.add("072110900176");
		r.add("072110900178");
		r.add("072110900179");
		r.add("072110900180");
		r.add("072110900181");
		r.add("072110900182");
		r.add("072110900183");
		r.add("072110900184");
		r.add("072110900185");
		r.add("072110900186");
		r.add("072110900187");
		r.add("072110900188");
		r.add("072110900189");
		r.add("072110900190");
		r.add("072110900191");
		r.add("072110900192");
		r.add("072110900194");
		r.add("072110900195");
		r.add("072110900197");
		r.add("072110900198");
		r.add("072110900199");
		r.add("072110900200");
		r.add("072110900202");
		r.add("072110900203");
		r.add("072110900204");
		r.add("072110900206");
		r.add("072110900207");
		r.add("072110900208");
		r.add("072110900209");
		r.add("072110900211");
		r.add("072110900214");
		r.add("072110900215");
		r.add("072110900216");
		r.add("072110900218");
		r.add("072110900221");
		r.add("072110900228");
		r.add("072110900230");
		r.add("072110900233");
		r.add("072110900234");
		r.add("072110900235");
		r.add("072110900237");
		r.add("072110900238");
		r.add("072110900240");
		r.add("072110900241");
		r.add("072110900250");
		r.add("072110900261");
		r.add("072110900270");
		r.add("072110900275");
		r.add("072110900278");
		r.add("074110900115");
		r.add("074110900126");
		r.add("074110900143");
		r.add("074110900149");
		r.add("074110900157");
		r.add("074110900161");
		r.add("074110900167");
		r.add("074110900170");
		r.add("074110900207");
		r.add("074110900211");
		r.add("074110900225");
		r.add("074110900227");
		r.add("074110900241");
		r.add("074110900249");
		r.add("074110900250");
		r.add("074110900255");
		r.add("074110900264");
		r.add("074110900270");
		r.add("074110900274");
		r.add("074110900280");
		r.add("074110900281");
		r.add("074110900284");
		r.add("074110900288");
		r.add("074110900289");
		r.add("074110900290");
		r.add("074110900300");
		r.add("074110900301");
		r.add("074110900305");
		r.add("074110900307");
		r.add("074110900309");
		r.add("074110900313");
		r.add("074110900319");
		r.add("074110900334");
		r.add("074110900353");
		r.add("074110900358");
		r.add("074110900362");
		r.add("074110900377");
		r.add("074110900389");
		r.add("074110900394");
		r.add("074110900403");
		r.add("074110900438");
		r.add("075110900072");
		r.add("075110900093");
		r.add("075110900152");
		r.add("075110900164");
		r.add("075110900171");
		r.add("075110900175");
		r.add("075110900177");
		r.add("075110900179");
		r.add("075110900180");
		r.add("075110900183");
		r.add("075110900186");
		r.add("075110900188");
		r.add("075110900191");
		r.add("075110900194");
		r.add("075110900196");
		r.add("075110900199");
		r.add("075110900200");
		r.add("075110900201");
		r.add("075110900202");
		r.add("075110900203");
		r.add("075110900207");
		r.add("075110900208");
		r.add("075110900209");
		r.add("075110900211");
		r.add("075110900213");
		r.add("075110900214");
		r.add("075110900215");
		r.add("075110900216");
		r.add("075110900218");
		r.add("075110900219");
		r.add("075110900221");
		r.add("075110900223");
		r.add("075110900224");
		r.add("075110900225");
		r.add("075110900226");
		r.add("075110900227");
		r.add("075110900228");
		r.add("075110900229");
		r.add("075110900231");
		r.add("075110900236");
		r.add("075110900237");
		r.add("075110900239");
		r.add("075110900240");
		r.add("075110900242");
		r.add("075110900243");
		r.add("075110900255");
		r.add("075110900277");
		r.add("075110900283");
		r.add("075110900286");
		r.add("075110900293");
		r.add("075110900304");
		r.add("075110900308");
		r.add("075110900316");
		r.add("075110900318");
		r.add("075110900341");
		r.add("075110900342");
		r.add("075110900344");
		r.add("075110900350");
		r.add("076110900080");
		r.add("076110900102");
		r.add("076110900103");
		r.add("076110900104");
		r.add("076110900106");
		r.add("076110900107");
		r.add("076110900109");
		r.add("076110900110");
		r.add("076110900111");
		r.add("076110900113");
		r.add("076110900116");
		r.add("076110900119");
		r.add("076110900120");
		r.add("076110900122");
		r.add("076110900123");
		r.add("076110900124");
		r.add("076110900125");
		r.add("076110900136");
		r.add("076110900138");
		r.add("076110900142");
		r.add("076110900146");
		r.add("076110900147");
		r.add("076110900148");
		r.add("076110900149");
		r.add("076110900152");
		r.add("076110900155");
		r.add("076110900157");
		r.add("076110900160");
		r.add("076110900166");
		r.add("076110900170");
		r.add("076110900171");
		r.add("076110900172");
		r.add("076110900173");
		r.add("076110900176");
		r.add("076110900177");
		r.add("076110900178");
		r.add("076110900179");
		r.add("076110900181");
		r.add("076110900182");
		r.add("076110900183");
		r.add("076110900185");
		r.add("076110900188");
		r.add("076110900189");
		r.add("076110900190");
		r.add("076110900191");
		r.add("076110900192");
		r.add("076110900196");
		r.add("076110900197");
		r.add("076110900198");
		r.add("076110900201");
		r.add("076110900202");
		r.add("076110900203");
		r.add("076110900206");
		r.add("076110900207");
		r.add("076110900208");
		r.add("076110900209");
		r.add("076110900210");
		r.add("076110900211");
		r.add("076110900212");
		r.add("076110900213");
		r.add("076110900220");
		r.add("076110900228");
		r.add("076110900232");
		r.add("076110900237");
		r.add("076110900238");
		r.add("076110900239");
		r.add("076110900242");
		r.add("076110900243");
		r.add("076110900244");
		r.add("076110900247");
		r.add("076110900250");
		r.add("076110900257");
		r.add("076110900270");
		r.add("076110900275");
		r.add("076110900280");
		r.add("076110900292");
		r.add("077110900093");
		r.add("077110900106");
		r.add("077110900113");
		r.add("077110900117");
		r.add("077110900120");
		r.add("077110900121");
		r.add("077110900122");
		r.add("077110900124");
		r.add("077110900125");
		r.add("077110900126");
		r.add("077110900127");
		r.add("077110900128");
		r.add("077110900129");
		r.add("077110900130");
		r.add("077110900132");
		r.add("077110900133");
		r.add("077110900134");
		r.add("077110900135");
		r.add("077110900136");
		r.add("077110900137");
		r.add("077110900138");
		r.add("077110900143");
		r.add("077110900144");
		r.add("077110900147");
		r.add("077110900149");
		r.add("077110900151");
		r.add("077110900158");
		r.add("077110900161");
		r.add("077110900162");
		r.add("077110900163");
		r.add("077110900164");
		r.add("077110900169");
		r.add("077110900170");
		r.add("077110900181");
		r.add("077110900185");
		r.add("077110900188");
		r.add("077110900191");
		r.add("077110900193");
		r.add("077110900197");
		r.add("077110900198");
		r.add("077110900199");
		r.add("077110900200");
		r.add("077110900201");
		r.add("077110900202");
		r.add("077110900203");
		r.add("077110900204");
		r.add("077110900205");
		r.add("077110900206");
		r.add("077110900207");
		r.add("077110900208");
		r.add("077110900209");
		r.add("077110900210");
		r.add("077110900211");
		r.add("077110900214");
		r.add("077110900215");
		r.add("077110900216");
		r.add("077110900217");
		r.add("077110900218");
		r.add("077110900221");
		r.add("077110900222");
		r.add("077110900231");
		r.add("077110900234");
		r.add("077110900237");
		r.add("077110900242");
		r.add("077110900244");
		r.add("077110900259");
		r.add("077110900262");
		r.add("077110900264");
		r.add("079110900083");
		r.add("079110900113");
		r.add("079110900124");
		r.add("079110900130");
		r.add("079110900140");
		r.add("079110900144");
		r.add("079110900150");
		r.add("079110900151");
		r.add("079110900156");
		r.add("079110900172");
		r.add("079110900177");
		r.add("079110900181");
		r.add("079110900184");
		r.add("079110900187");
		r.add("079110900188");
		r.add("079110900189");
		r.add("079110900191");
		r.add("079110900194");
		r.add("079110900209");
		r.add("079110900216");
		r.add("079110900218");
		r.add("079110900219");
		r.add("079110900220");
		r.add("079110900222");
		r.add("079110900223");
		r.add("079110900225");
		r.add("079110900226");
		r.add("079110900227");
		r.add("079110900228");
		r.add("079110900238");
		r.add("079110900241");
		r.add("079110900255");
		r.add("079110900260");
		r.add("079110900262");
		r.add("079110900263");
		r.add("079110900269");
		r.add("079110900271");
		r.add("079110900277");
		r.add("079110900288");
		r.add("080110900142");
		r.add("080110900168");
		r.add("080110900185");
		r.add("080110900210");
		r.add("080110900213");
		r.add("080110900214");
		r.add("080110900217");
		r.add("080110900218");
		r.add("080110900219");
		r.add("080110900221");
		r.add("080110900222");
		r.add("080110900223");
		r.add("080110900226");
		r.add("080110900227");
		r.add("080110900229");
		r.add("080110900230");
		r.add("080110900231");
		r.add("080110900232");
		r.add("080110900233");
		r.add("080110900235");
		r.add("080110900236");
		r.add("080110900237");
		r.add("080110900239");
		r.add("080110900247");
		r.add("080110900249");
		r.add("080110900250");
		r.add("080110900252");
		r.add("080110900253");
		r.add("080110900255");
		r.add("080110900256");
		r.add("080110900257");
		r.add("080110900258");
		r.add("080110900259");
		r.add("080110900260");
		r.add("080110900263");
		r.add("080110900264");
		r.add("080110900266");
		r.add("080110900267");
		r.add("080110900271");
		r.add("080110900273");
		r.add("080110900275");
		r.add("080110900276");
		r.add("080110900277");
		r.add("080110900280");
		r.add("080110900283");
		r.add("080110900285");
		r.add("080110900286");
		r.add("080110900287");
		r.add("080110900291");
		r.add("080110900295");
		r.add("080110900297");
		r.add("080110900298");
		r.add("080110900302");
		r.add("080110900303");
		r.add("080110900304");
		r.add("080110900305");
		r.add("080110900306");
		r.add("080110900308");
		r.add("080110900309");
		r.add("080110900310");
		r.add("080110900311");
		r.add("080110900312");
		r.add("080110900313");
		r.add("080110900314");
		r.add("080110900321");
		r.add("080110900322");
		r.add("080110900325");
		r.add("080110900327");
		r.add("080110900328");
		r.add("080110900330");
		r.add("080110900331");
		r.add("080110900332");
		r.add("080110900333");
		r.add("080110900334");
		r.add("080110900335");
		r.add("080110900336");
		r.add("080110900339");
		r.add("080110900340");
		r.add("080110900341");
		r.add("080110900342");
		r.add("080110900343");
		r.add("080110900344");
		r.add("080110900345");
		r.add("080110900346");
		r.add("080110900347");
		r.add("080110900348");
		r.add("080110900349");
		r.add("080110900350");
		r.add("080110900351");
		r.add("080110900352");
		r.add("080110900353");
		r.add("080110900354");
		r.add("080110900355");
		r.add("080110900356");
		r.add("080110900359");
		r.add("080110900360");
		r.add("080110900361");
		r.add("080110900362");
		r.add("080110900365");
		r.add("080110900366");
		r.add("080110900367");
		r.add("080110900368");
		r.add("080110900369");
		r.add("080110900370");
		r.add("080110900371");
		r.add("080110900372");
		r.add("080110900373");
		r.add("080110900374");
		r.add("080110900375");
		r.add("080110900377");
		r.add("080110900378");
		r.add("080110900379");
		r.add("080110900380");
		r.add("080110900381");
		r.add("080110900382");
		r.add("080110900384");
		r.add("080110900385");
		r.add("080110900386");
		r.add("080110900387");
		r.add("080110900388");
		r.add("080110900389");
		r.add("080110900390");
		r.add("080110900391");
		r.add("080110900392");
		r.add("080110900393");
		r.add("080110900394");
		r.add("080110900395");
		r.add("080110900396");
		r.add("080110900397");
		r.add("080110900398");
		r.add("080110900401");
		r.add("080110900402");
		r.add("080110900403");
		r.add("080110900404");
		r.add("080110900405");
		r.add("080110900406");
		r.add("080110900407");
		r.add("080110900408");
		r.add("080110900409");
		r.add("080110900410");
		r.add("080110900411");
		r.add("080110900412");
		r.add("080110900414");
		r.add("080110900416");
		r.add("080110900417");
		r.add("080110900418");
		r.add("080110900419");
		r.add("080110900420");
		r.add("080110900422");
		r.add("080110900423");
		r.add("080110900424");
		r.add("080110900425");
		r.add("080110900426");
		r.add("080110900427");
		r.add("080110900428");
		r.add("080110900429");
		r.add("080110900430");
		r.add("080110900431");
		r.add("080110900433");
		r.add("080110900434");
		r.add("080110900435");
		r.add("080110900436");
		r.add("080110900437");
		r.add("080110900438");
		r.add("080110900439");
		r.add("080110900440");
		r.add("080110900444");
		r.add("080110900447");
		r.add("080110900448");
		r.add("080110900450");
		r.add("080110900452");
		r.add("080110900455");
		r.add("080110900456");
		r.add("080110900458");
		r.add("080110900459");
		r.add("080110900464");
		r.add("080110900467");
		r.add("080110900471");
		r.add("080110900473");
		r.add("080110900476");
		r.add("080110900477");
		r.add("080110900483");
		r.add("080110900489");
		r.add("080110900492");
		r.add("080110900495");
		r.add("080110900512");
		r.add("080110900534");
		r.add("080110900549");
		r.add("080110900553");
		r.add("080110900571");
		r.add("080110900572");
		r.add("080110900596");
		r.add("080110900624");
		r.add("080110900629");
		r.add("080110900655");
		r.add("080110900659");
		r.add("080110900728");
		r.add("083110900081");
		r.add("083110900102");
		r.add("083110900103");
		r.add("083110900104");
		r.add("083110900106");
		r.add("083110900114");
		r.add("083110900117");
		r.add("083110900119");
		r.add("083110900125");
		r.add("083110900128");
		r.add("083110900132");
		r.add("083110900141");
		r.add("083110900144");
		r.add("083110900145");
		r.add("083110900146");
		r.add("083110900147");
		r.add("083110900148");
		r.add("083110900149");
		r.add("083110900150");
		r.add("083110900152");
		r.add("083110900153");
		r.add("083110900157");
		r.add("083110900158");
		r.add("083110900159");
		r.add("083110900164");
		r.add("083110900167");
		r.add("083110900171");
		r.add("083110900173");
		r.add("083110900176");
		r.add("083110900186");
		r.add("083110900207");
		r.add("103110900144");
		r.add("103110900186");
		r.add("103110900211");
		r.add("103110900212");
		r.add("103110900219");
		r.add("103110900221");
		r.add("103110900233");
		r.add("103110900247");
		r.add("103110900250");
		r.add("103110900251");
		r.add("103110900259");
		r.add("103110900266");
		r.add("103110900270");
		r.add("103110900276");
		r.add("103110900278");
		r.add("103110900279");
		r.add("103110900280");
		r.add("103110900281");
		r.add("103110900288");
		r.add("103110900293");
		r.add("103110900295");
		r.add("103110900298");
		r.add("103110900299");
		r.add("103110900304");
		r.add("103110900308");
		r.add("103110900309");
		r.add("103110900311");
		r.add("103110900312");
		r.add("103110900313");
		r.add("103110900317");
		r.add("103110900320");
		r.add("103110900321");
		r.add("103110900322");
		r.add("103110900323");
		r.add("103110900330");
		r.add("103110900337");
		r.add("103110900339");
		r.add("103110900340");
		r.add("103110900341");
		r.add("103110900343");
		r.add("103110900344");
		r.add("103110900345");
		r.add("103110900346");
		r.add("103110900348");
		r.add("103110900349");
		r.add("103110900350");
		r.add("103110900355");
		r.add("103110900356");
		r.add("103110900361");
		r.add("103110900364");
		r.add("103110900371");
		r.add("103110900374");
		r.add("103110900375");
		r.add("103110900378");
		r.add("103110900380");
		r.add("103110900384");
		r.add("103110900387");
		r.add("103110900394");
		r.add("103110900397");
		r.add("103110900398");
		r.add("103110900401");
		r.add("103110900461");
		r.add("103110900462");
		r.add("103110900465");
		r.add("103110900467");
		r.add("103110900471");
		r.add("103110900472");
		r.add("103110900476");
		r.add("103110900477");
		r.add("103110900478");
		r.add("103110900479");
		r.add("103110900480");
		r.add("103110900494");
		r.add("201110900224");
		r.add("201110900243");
		r.add("201110900244");
		r.add("201110900247");
		r.add("201110900248");
		r.add("201110900250");
		r.add("201110900252");
		r.add("201110900265");
		r.add("201110900268");
		r.add("201110900272");
		r.add("201110900273");
		r.add("201110900278");
		r.add("201110900279");
		r.add("201110900288");
		r.add("201110900297");
		r.add("201110900298");
		r.add("201110900301");
		r.add("201110900302");
		r.add("201110900304");
		r.add("201110900305");
		r.add("201110900306");
		r.add("201110900310");
		r.add("201110900312");
		r.add("201110900313");
		r.add("201110900315");
		r.add("201110900319");
		r.add("201110900320");
		r.add("201110900321");
		r.add("201110900322");
		r.add("201110900323");
		r.add("201110900325");
		r.add("201110900326");
		r.add("201110900330");
		r.add("201110900331");
		r.add("201110900337");
		r.add("201110900338");
		r.add("201110900342");
		r.add("201110900344");
		r.add("201110900347");
		r.add("201110900348");
		r.add("201110900356");
		r.add("201110900361");
		r.add("201110900362");
		r.add("201110900364");
		r.add("201110900376");
		r.add("201110900412");
		r.add("202110900131");
		r.add("202110900133");
		r.add("202110900139");
		r.add("202110900174");
		r.add("202110900176");
		r.add("202110900177");
		r.add("202110900184");
		r.add("202110900185");
		r.add("202110900186");
		r.add("202110900187");
		r.add("202110900188");
		r.add("202110900189");
		r.add("202110900200");
		r.add("202110900202");
		r.add("202110900204");
		r.add("202110900205");
		r.add("202110900217");
		r.add("202110900230");
		r.add("202110900231");
		r.add("202110900232");
		r.add("202110900233");
		r.add("202110900246");
		r.add("202110900250");
		r.add("202110900285");
		r.add("202110900286");
		r.add("202110900288");
		r.add("202110900292");
		r.add("202110900294");
		r.add("202110900299");
		r.add("202110900303");
		r.add("202110900306");
		r.add("202110900308");
		r.add("202110900309");
		r.add("202110900310");
		r.add("202110900311");
		r.add("202110900316");
		r.add("202110900318");
		r.add("202110900322");
		r.add("202110900328");
		r.add("202110900331");
		r.add("202110900332");
		r.add("202110900343");
		r.add("202110900356");
		r.add("202110900364");
		r.add("202110900396");
		r.add("203110900139");
		r.add("203110900159");
		r.add("203110900167");
		r.add("203110900169");
		r.add("203110900171");
		r.add("203110900174");
		r.add("203110900175");
		r.add("203110900176");
		r.add("203110900177");
		r.add("203110900179");
		r.add("203110900180");
		r.add("203110900182");
		r.add("203110900185");
		r.add("203110900186");
		r.add("203110900188");
		r.add("203110900189");
		r.add("203110900190");
		r.add("203110900191");
		r.add("203110900192");
		r.add("203110900193");
		r.add("203110900196");
		r.add("203110900197");
		r.add("203110900199");
		r.add("203110900201");
		r.add("203110900202");
		r.add("203110900208");
		r.add("203110900209");
		r.add("203110900210");
		r.add("203110900215");
		r.add("203110900216");
		r.add("203110900217");
		r.add("203110900219");
		r.add("203110900221");
		r.add("203110900223");
		r.add("203110900225");
		r.add("203110900227");
		r.add("203110900228");
		r.add("203110900229");
		r.add("203110900230");
		r.add("203110900231");
		r.add("203110900232");
		r.add("203110900233");
		r.add("203110900234");
		r.add("203110900235");
		r.add("203110900236");
		r.add("203110900237");
		r.add("203110900238");
		r.add("203110900245");
		r.add("203110900247");
		r.add("203110900248");
		r.add("203110900249");
		r.add("203110900250");
		r.add("203110900251");
		r.add("203110900252");
		r.add("203110900256");
		r.add("203110900258");
		r.add("203110900268");
		r.add("203110900269");
		r.add("203110900270");
		r.add("203110900274");
		r.add("203110900329");
		r.add("203110900367");
		r.add("203110900390");
		r.add("204110900172");
		r.add("204110900175");
		r.add("204110900178");
		r.add("204110900179");
		r.add("204110900204");
		r.add("204110900220");
		r.add("204110900233");
		r.add("204110900249");
		r.add("204110900250");
		r.add("204110900263");
		r.add("204110900264");
		r.add("204110900270");
		r.add("204110900273");
		r.add("204110900276");
		r.add("204110900278");
		r.add("204110900284");
		r.add("204110900286");
		r.add("204110900290");
		r.add("204110900301");
		r.add("204110900303");
		r.add("204110900304");
		r.add("204110900305");
		r.add("204110900319");
		r.add("204110900324");
		r.add("204110900339");
		r.add("204110900341");
		r.add("204110900366");
		r.add("204110900370");
		r.add("204110900371");
		r.add("204110900374");
		r.add("204110900380");
		r.add("204110900381");
		r.add("204110900382");
		r.add("204110900393");
		r.add("204110900394");
		r.add("204110900404");
		r.add("204110900405");
		r.add("205110900151");
		r.add("205110900161");
		r.add("205110900168");
		r.add("205110900174");
		r.add("205110900175");
		r.add("205110900179");
		r.add("205110900185");
		r.add("205110900227");
		r.add("205110900230");
		r.add("205110900231");
		r.add("205110900232");
		r.add("205110900233");
		r.add("205110900235");
		r.add("205110900237");
		r.add("205110900238");
		r.add("205110900241");
		r.add("205110900243");
		r.add("205110900244");
		r.add("205110900245");
		r.add("205110900248");
		r.add("205110900249");
		r.add("205110900253");
		r.add("205110900254");
		r.add("205110900261");
		r.add("205110900262");
		r.add("205110900264");
		r.add("205110900265");
		r.add("205110900266");
		r.add("205110900267");
		r.add("205110900268");
		r.add("205110900269");
		r.add("205110900299");
		r.add("205110900306");
		r.add("205110900318");
		r.add("205110900320");
		r.add("205110900322");
		r.add("205110900323");
		r.add("205110900325");
		r.add("205110900326");
		r.add("205110900327");
		r.add("205110900329");
		r.add("205110900331");
		r.add("205110900332");
		r.add("205110900335");
		r.add("205110900338");
		r.add("205110900341");
		r.add("205110900342");
		r.add("205110900343");
		r.add("205110900371");
		r.add("205110900372");
		r.add("205110900378");
		r.add("205110900405");
		r.add("205110900420");
		r.add("206110900157");
		r.add("206110900195");
		r.add("206110900198");
		r.add("206110900199");
		r.add("206110900204");
		r.add("206110900218");
		r.add("206110900221");
		r.add("206110900222");
		r.add("206110900226");
		r.add("206110900228");
		r.add("206110900233");
		r.add("206110900236");
		r.add("206110900237");
		r.add("206110900240");
		r.add("206110900241");
		r.add("206110900245");
		r.add("206110900248");
		r.add("206110900254");
		r.add("206110900256");
		r.add("206110900257");
		r.add("206110900267");
		r.add("206110900270");
		r.add("206110900274");
		r.add("206110900275");
		r.add("206110900276");
		r.add("206110900277");
		r.add("206110900282");
		r.add("206110900288");
		r.add("206110900289");
		r.add("206110900291");
		r.add("206110900293");
		r.add("206110900295");
		r.add("206110900301");
		r.add("206110900306");
		r.add("206110900311");
		r.add("206110900313");
		r.add("206110900315");
		r.add("206110900319");
		r.add("206110900320");
		r.add("206110900324");
		r.add("206110900325");
		r.add("206110900327");
		r.add("206110900339");
		r.add("206110900346");
		r.add("206110900352");
		r.add("206110900353");
		r.add("206110900359");
		r.add("206110900397");
		r.add("206110900452");
		r.add("206110900457");
		r.add("207110900364");
		r.add("207110900416");
		r.add("207110900420");
		r.add("207110900427");
		r.add("207110900428");
		r.add("207110900435");
		r.add("207110900436");
		r.add("207110900438");
		r.add("207110900445");
		r.add("207110900446");
		r.add("207110900456");
		r.add("207110900458");
		r.add("207110900459");
		r.add("207110900460");
		r.add("207110900488");
		r.add("207110900489");
		r.add("207110900490");
		r.add("207110900492");
		r.add("207110900493");
		r.add("207110900494");
		r.add("207110900515");
		r.add("207110900516");
		r.add("207110900524");
		r.add("207110900525");
		r.add("207110900535");
		r.add("207110900538");
		r.add("207110900546");
		r.add("207110900548");
		r.add("207110900552");
		r.add("207110900554");
		r.add("207110900555");
		r.add("207110900556");
		r.add("207110900558");
		r.add("207110900559");
		r.add("207110900560");
		r.add("207110900562");
		r.add("207110900563");
		r.add("207110900566");
		r.add("207110900567");
		r.add("207110900581");
		r.add("207110900584");
		r.add("207110900588");
		r.add("207110900594");
		r.add("207110900597");
		r.add("207110900611");
		r.add("207110900612");
		r.add("207110900626");
		r.add("207110900638");
		r.add("207110900651");
		r.add("207110900666");
		r.add("207110900674");
		r.add("207110900676");
		r.add("207110900800");
		r.add("208110900100");
		r.add("208110900120");
		r.add("208110900121");
		r.add("208110900130");
		r.add("208110900133");
		r.add("208110900134");
		r.add("208110900173");
		r.add("208110900178");
		r.add("208110900185");
		r.add("208110900189");
		r.add("208110900193");
		r.add("208110900197");
		r.add("208110900204");
		r.add("208110900209");
		r.add("208110900210");
		r.add("208110900211");
		r.add("208110900212");
		r.add("208110900218");
		r.add("208110900221");
		r.add("208110900222");
		r.add("208110900224");
		r.add("208110900225");
		r.add("208110900227");
		r.add("208110900235");
		r.add("208110900236");
		r.add("208110900237");
		r.add("208110900239");
		r.add("208110900241");
		r.add("208110900242");
		r.add("208110900244");
		r.add("208110900246");
		r.add("208110900248");
		r.add("208110900249");
		r.add("208110900250");
		r.add("208110900252");
		r.add("208110900253");
		r.add("208110900254");
		r.add("208110900256");
		r.add("208110900257");
		r.add("208110900258");
		r.add("208110900262");
		r.add("208110900263");
		r.add("208110900264");
		r.add("208110900265");
		r.add("208110900267");
		r.add("208110900271");
		r.add("208110900272");
		r.add("208110900273");
		r.add("208110900274");
		r.add("208110900275");
		r.add("208110900276");
		r.add("208110900277");
		r.add("208110900279");
		r.add("208110900283");
		r.add("208110900286");
		r.add("208110900287");
		r.add("208110900289");
		r.add("208110900290");
		r.add("208110900291");
		r.add("208110900294");
		r.add("208110900295");
		r.add("208110900296");
		r.add("208110900299");
		r.add("208110900306");
		r.add("208110900307");
		r.add("208110900309");
		r.add("208110900310");
		r.add("208110900311");
		r.add("208110900314");
		r.add("208110900315");
		r.add("208110900316");
		r.add("208110900317");
		r.add("208110900323");
		r.add("208110900325");
		r.add("208110900329");
		r.add("208110900343");
		r.add("208110900348");
		r.add("208110900350");
		r.add("208110900354");
		r.add("208110900361");
		r.add("208110900362");
		r.add("208110900370");
		r.add("208110900385");
		r.add("208110900386");
		r.add("208110900409");
		r.add("208110900411");
		r.add("208110900415");
		r.add("208110900451");
		r.add("208110900502");
		r.add("208110900547");
		r.add("208110900548");
		r.add("210110900102");
		r.add("210110900103");
		r.add("210110900112");
		r.add("210110900119");
		r.add("210110900126");
		r.add("210110900130");
		r.add("210110900135");
		r.add("210110900137");
		r.add("210110900148");
		r.add("210110900150");
		r.add("210110900152");
		r.add("210110900154");
		r.add("210110900157");
		r.add("210110900158");
		r.add("210110900159");
		r.add("210110900165");
		r.add("210110900169");
		r.add("210110900171");
		r.add("210110900173");
		r.add("210110900174");
		r.add("210110900175");
		r.add("210110900177");
		r.add("210110900178");
		r.add("210110900182");
		r.add("210110900185");
		r.add("210110900186");
		r.add("210110900187");
		r.add("210110900206");
		r.add("210110900211");
		r.add("210110900212");
		r.add("210110900213");
		r.add("210110900215");
		r.add("210110900217");
		r.add("210110900218");
		r.add("210110900219");
		r.add("210110900250");
		r.add("210110900270");
		r.add("210110900272");
		r.add("212110900082");
		r.add("212110900099");
		r.add("212110900100");
		r.add("212110900101");
		r.add("212110900102");
		r.add("212110900104");
		r.add("212110900109");
		r.add("212110900110");
		r.add("212110900113");
		r.add("212110900114");
		r.add("212110900116");
		r.add("212110900122");
		r.add("212110900124");
		r.add("212110900126");
		r.add("212110900139");
		r.add("212110900149");
		r.add("212110900156");
		r.add("212110900158");
		r.add("212110900159");
		r.add("212110900160");
		r.add("212110900163");
		r.add("212110900176");
		r.add("212110900177");
		r.add("212110900178");
		r.add("212110900179");
		r.add("212110900181");
		r.add("212110900182");
		r.add("212110900186");
		r.add("212110900187");
		r.add("212110900192");
		r.add("212110900193");
		r.add("212110900194");
		r.add("212110900197");
		r.add("212110900198");
		r.add("212110900201");
		r.add("212110900202");
		r.add("212110900204");
		r.add("212110900205");
		r.add("212110900207");
		r.add("212110900210");
		r.add("212110900211");
		r.add("212110900212");
		r.add("212110900213");
		r.add("212110900214");
		r.add("212110900215");
		r.add("212110900216");
		r.add("212110900217");
		r.add("212110900218");
		r.add("212110900221");
		r.add("212110900222");
		r.add("212110900223");
		r.add("212110900224");
		r.add("212110900225");
		r.add("212110900226");
		r.add("212110900227");
		r.add("212110900228");
		r.add("212110900229");
		r.add("212110900230");
		r.add("212110900231");
		r.add("212110900232");
		r.add("212110900233");
		r.add("212110900234");
		r.add("212110900235");
		r.add("212110900236");
		r.add("212110900237");
		r.add("212110900238");
		r.add("212110900239");
		r.add("212110900240");
		r.add("212110900241");
		r.add("212110900242");
		r.add("212110900243");
		r.add("212110900244");
		r.add("212110900245");
		r.add("212110900246");
		r.add("212110900247");
		r.add("212110900251");
		r.add("212110900252");
		r.add("212110900253");
		r.add("212110900254");
		r.add("212110900255");
		r.add("212110900256");
		r.add("212110900257");
		r.add("212110900258");
		r.add("212110900259");
		r.add("212110900260");
		r.add("212110900261");
		r.add("212110900262");
		r.add("212110900263");
		r.add("212110900264");
		r.add("212110900265");
		r.add("212110900266");
		r.add("212110900268");
		r.add("212110900271");
		r.add("212110900275");
		r.add("212110900278");
		r.add("212110900279");
		r.add("212110900283");
		r.add("212110900290");
		r.add("212110900291");
		r.add("212110900294");
		r.add("212110900296");
		r.add("212110900310");
		r.add("212110900311");
		r.add("212110900312");
		r.add("212110900313");
		r.add("212110900330");
		r.add("212110900331");
		r.add("212110900340");
		r.add("212110900342");
		r.add("212110900343");
		r.add("212110900345");
		r.add("212110900346");
		r.add("212110900347");
		r.add("213110900178");
		r.add("213110900244");
		r.add("213110900317");
		r.add("213110900371");
		r.add("213110900491");
		r.add("213110900494");
		r.add("213110900579");
		r.add("213110900582");
		r.add("213110900588");
		r.add("213110900589");
		r.add("213110900595");
		r.add("213110900598");
		r.add("213110900605");
		r.add("213110900608");
		r.add("213110900609");
		r.add("213110900617");
		r.add("213110900620");
		r.add("213110900621");
		r.add("213110900631");
		r.add("213110900634");
		r.add("213110900642");
		r.add("213110900644");
		r.add("213110900659");
		r.add("213110900666");
		r.add("213110900667");
		r.add("213110900668");
		r.add("213110900672");
		r.add("213110900678");
		r.add("213110900685");
		r.add("213110900688");
		r.add("213110900691");
		r.add("213110900693");
		r.add("213110900705");
		r.add("213110900725");
		r.add("213110900733");
		r.add("213110900763");
		r.add("213110900767");
		r.add("213110900768");
		r.add("213110900771");
		r.add("213110900778");
		r.add("213110900780");
		r.add("213110900788");
		r.add("213110900790");
		r.add("213110900800");
		r.add("213110900809");
		r.add("213110900810");
		r.add("213110900813");
		r.add("213110900814");
		r.add("213110900816");
		r.add("213110900861");
		r.add("213110900870");
		r.add("213110900881");
		r.add("213110900899");
		r.add("214110900135");
		r.add("214110900136");
		r.add("214110900146");
		r.add("214110900152");
		r.add("214110900157");
		r.add("214110900168");
		r.add("214110900185");
		r.add("214110900205");
		r.add("214110900206");
		r.add("214110900209");
		r.add("214110900211");
		r.add("214110900220");
		r.add("214110900230");
		r.add("214110900231");
		r.add("214110900234");
		r.add("214110900235");
		r.add("214110900237");
		r.add("214110900239");
		r.add("214110900240");
		r.add("214110900242");
		r.add("214110900243");
		r.add("214110900244");
		r.add("214110900245");
		r.add("214110900246");
		r.add("214110900247");
		r.add("214110900248");
		r.add("214110900249");
		r.add("214110900250");
		r.add("214110900251");
		r.add("214110900252");
		r.add("214110900255");
		r.add("214110900256");
		r.add("214110900257");
		r.add("214110900258");
		r.add("214110900259");
		r.add("214110900260");
		r.add("214110900264");
		r.add("214110900265");
		r.add("214110900268");
		r.add("214110900276");
		r.add("214110900279");
		r.add("214110900284");
		r.add("214110900286");
		r.add("214110900287");
		r.add("214110900288");
		r.add("214110900290");
		r.add("214110900292");
		r.add("214110900293");
		r.add("214110900294");
		r.add("214110900295");
		r.add("214110900300");
		r.add("214110900301");
		r.add("214110900302");
		r.add("214110900305");
		r.add("214110900306");
		r.add("214110900308");
		r.add("214110900309");
		r.add("214110900311");
		r.add("214110900312");
		r.add("214110900313");
		r.add("214110900314");
		r.add("214110900318");
		r.add("214110900319");
		r.add("214110900320");
		r.add("214110900321");
		r.add("214110900322");
		r.add("214110900323");
		r.add("214110900324");
		r.add("214110900325");
		r.add("214110900326");
		r.add("214110900327");
		r.add("214110900328");
		r.add("214110900334");
		r.add("214110900339");
		r.add("214110900347");
		r.add("214110900351");
		r.add("214110900353");
		r.add("214110900354");
		r.add("214110900371");
		r.add("214110900376");
		r.add("214110900392");
		r.add("214110900416");
		r.add("214110900420");
		r.add("214110900432");
		r.add("214110900448");
		r.add("214110900449");
		r.add("215110900287");
		r.add("215110900291");
		r.add("215110900293");
		r.add("215110900306");
		r.add("215110900307");
		r.add("215110900316");
		r.add("215110900318");
		r.add("215110900319");
		r.add("215110900323");
		r.add("215110900324");
		r.add("215110900325");
		r.add("215110900327");
		r.add("215110900332");
		r.add("215110900341");
		r.add("215110900343");
		r.add("215110900345");
		r.add("215110900346");
		r.add("215110900360");
		r.add("215110900361");
		r.add("215110900363");
		r.add("215110900364");
		r.add("215110900365");
		r.add("215110900367");
		r.add("215110900369");
		r.add("215110900375");
		r.add("215110900378");
		r.add("215110900379");
		r.add("215110900382");
		r.add("215110900385");
		r.add("215110900387");
		r.add("215110900389");
		r.add("215110900397");
		r.add("215110900398");
		r.add("215110900399");
		r.add("215110900400");
		r.add("215110900401");
		r.add("215110900404");
		r.add("215110900405");
		r.add("215110900406");
		r.add("215110900408");
		r.add("215110900409");
		r.add("215110900410");
		r.add("215110900411");
		r.add("215110900412");
		r.add("215110900414");
		r.add("215110900417");
		r.add("215110900420");
		r.add("215110900421");
		r.add("215110900422");
		r.add("215110900423");
		r.add("215110900425");
		r.add("215110900426");
		r.add("215110900431");
		r.add("215110900432");
		r.add("215110900436");
		r.add("215110900439");
		r.add("215110900444");
		r.add("215110900455");
		r.add("215110900457");
		r.add("215110900458");
		r.add("215110900460");
		r.add("215110900488");
		r.add("215110900505");
		r.add("215110900508");
		r.add("215110900509");
		r.add("215110900513");
		r.add("215110900516");
		r.add("215110900521");
		r.add("215110900535");
		r.add("215110900553");
		r.add("215110900566");
		r.add("215110900573");
		r.add("215110900588");
		r.add("215110900591");
		r.add("215110900595");
		r.add("215110900613");
		r.add("216110900107");
		r.add("216110900124");
		r.add("216110900128");
		r.add("216110900129");
		r.add("216110900138");
		r.add("216110900144");
		r.add("216110900148");
		r.add("216110900149");
		r.add("216110900150");
		r.add("216110900152");
		r.add("216110900153");
		r.add("216110900154");
		r.add("216110900155");
		r.add("216110900160");
		r.add("216110900164");
		r.add("216110900165");
		r.add("216110900170");
		r.add("216110900172");
		r.add("216110900178");
		r.add("216110900180");
		r.add("216110900181");
		r.add("216110900200");
		r.add("219110900124");
		r.add("219110900154");
		r.add("219110900178");
		r.add("219110900184");
		r.add("219110900195");
		r.add("219110900196");
		r.add("219110900199");
		r.add("219110900200");
		r.add("219110900203");
		r.add("219110900208");
		r.add("219110900212");
		r.add("219110900213");
		r.add("219110900218");
		r.add("219110900220");
		r.add("219110900225");
		r.add("219110900227");
		r.add("219110900231");
		r.add("219110900242");
		r.add("219110900257");
		r.add("219110900259");
		r.add("219110900264");
		r.add("219110900278");
		r.add("219110900282");
		r.add("219110900308");
		r.add("219110900311");
		r.add("219110900317");
		r.add("219110900318");
		r.add("219110900320");
		r.add("219110900325");
		r.add("219110900348");
		r.add("219110900350");
		r.add("219110900356");
		r.add("219110900365");
		r.add("219110900369");
		r.add("220110900205");
		r.add("220110900206");
		r.add("220110900207");
		r.add("220110900209");
		r.add("220110900224");
		r.add("220110900245");
		r.add("220110900249");
		r.add("220110900250");
		r.add("220110900256");
		r.add("220110900259");
		r.add("220110900269");
		r.add("220110900270");
		r.add("220110900271");
		r.add("220110900272");
		r.add("220110900277");
		r.add("220110900286");
		r.add("220110900289");
		r.add("220110900297");
		r.add("220110900311");
		r.add("220110900336");
		r.add("220110900356");
		r.add("226110900142");
		r.add("226110900167");
		r.add("226110900175");
		r.add("226110900179");
		r.add("226110900241");
		r.add("226110900264");
		r.add("226110900271");
		r.add("226110900272");
		r.add("226110900273");
		r.add("226110900280");
		r.add("226110900281");
		r.add("226110900286");
		r.add("226110900289");
		r.add("226110900290");
		r.add("226110900296");
		r.add("226110900298");
		r.add("226110900308");
		r.add("226110900309");
		r.add("226110900333");
		r.add("226110900339");
		r.add("226110900341");
		r.add("226110900347");
		r.add("226110900359");
		r.add("226110900378");
		r.add("226110900388");
		r.add("226110900402");
		r.add("227110900098");
		r.add("227110900103");
		r.add("227110900104");
		r.add("227110900108");
		r.add("227110900114");
		r.add("227110900116");
		r.add("227110900117");
		r.add("227110900120");
		r.add("227110900130");
		r.add("227110900131");
		r.add("227110900136");
		r.add("227110900139");
		r.add("227110900140");
		r.add("227110900141");
		r.add("227110900142");
		r.add("227110900143");
		r.add("227110900148");
		r.add("227110900153");
		r.add("227110900154");
		r.add("227110900155");
		r.add("227110900156");
		r.add("227110900158");
		r.add("227110900161");
		r.add("227110900162");
		r.add("227110900163");
		r.add("227110900164");
		r.add("227110900165");
		r.add("227110900166");
		r.add("227110900167");
		r.add("227110900168");
		r.add("227110900169");
		r.add("227110900170");
		r.add("227110900171");
		r.add("227110900172");
		r.add("227110900173");
		r.add("227110900174");
		r.add("227110900175");
		r.add("227110900176");
		r.add("227110900177");
		r.add("227110900178");
		r.add("227110900179");
		r.add("227110900180");
		r.add("227110900181");
		r.add("227110900186");
		r.add("227110900187");
		r.add("227110900189");
		r.add("227110900190");
		r.add("227110900191");
		r.add("227110900192");
		r.add("227110900193");
		r.add("227110900194");
		r.add("227110900195");
		r.add("227110900197");
		r.add("227110900198");
		r.add("227110900206");
		r.add("227110900220");
		r.add("227110900227");
		r.add("227110900229");
		r.add("227110900232");
		r.add("227110900238");
		r.add("228110900116");
		r.add("228110900123");
		r.add("228110900127");
		r.add("228110900144");
		r.add("228110900160");
		r.add("228110900171");
		r.add("228110900210");
		r.add("228110900224");
		r.add("228110900234");
		r.add("228110900237");
		r.add("228110900246");
		r.add("228110900257");
		r.add("228110900258");
		r.add("228110900266");
		r.add("228110900269");
		r.add("228110900271");
		r.add("228110900275");
		r.add("228110900280");
		r.add("228110900291");
		r.add("228110900295");
		r.add("228110900299");
		r.add("228110900300");
		r.add("228110900303");
		r.add("228110900304");
		r.add("228110900305");
		r.add("228110900309");
		r.add("228110900310");
		r.add("228110900311");
		r.add("228110900316");
		r.add("228110900317");
		r.add("228110900319");
		r.add("228110900321");
		r.add("228110900322");
		r.add("228110900323");
		r.add("228110900324");
		r.add("228110900329");
		r.add("228110900330");
		r.add("228110900331");
		r.add("228110900332");
		r.add("228110900333");
		r.add("228110900334");
		r.add("228110900335");
		r.add("228110900336");
		r.add("228110900337");
		r.add("228110900340");
		r.add("228110900341");
		r.add("228110900342");
		r.add("228110900343");
		r.add("228110900344");
		r.add("228110900345");
		r.add("228110900346");
		r.add("228110900347");
		r.add("228110900351");
		r.add("228110900352");
		r.add("228110900354");
		r.add("228110900355");
		r.add("228110900356");
		r.add("228110900358");
		r.add("228110900359");
		r.add("228110900371");
		r.add("228110900379");
		r.add("228110900381");
		r.add("228110900383");
		r.add("228110900390");
		r.add("228110900392");
		r.add("228110900402");
		r.add("228110900421");
		r.add("228110900422");
		r.add("229110900534");
		r.add("229110900566");
		r.add("229110900569");
		r.add("229110900572");
		r.add("229110900601");
		r.add("229110900604");
		r.add("229110900622");
		r.add("229110900644");
		r.add("229110900659");
		r.add("229110900669");
		r.add("229110900685");
		r.add("229110900687");
		r.add("229110900689");
		r.add("229110900690");
		r.add("229110900691");
		r.add("229110900692");
		r.add("229110900701");
		r.add("229110900732");
		r.add("229110900752");
		r.add("229110900765");
		r.add("229110900766");
		r.add("229110900769");
		r.add("229110900810");
		r.add("229110900824");
		r.add("229110900885");
		r.add("229110900916");
		r.add("229110900935");
		r.add("231110900108");
		r.add("231110900205");
		r.add("231110900230");
		r.add("231110900253");
		r.add("231110900263");
		r.add("231110900308");
		r.add("232110900080");
		r.add("232110900105");
		r.add("232110900122");
		r.add("232110900127");
		r.add("232110900132");
		r.add("232110900138");
		r.add("232110900142");
		r.add("232110900144");
		r.add("232110900145");
		r.add("232110900149");
		r.add("232110900153");
		r.add("232110900157");
		r.add("232110900160");
		r.add("232110900161");
		r.add("232110900165");
		r.add("232110900167");
		r.add("232110900170");
		r.add("232110900175");
		r.add("232110900178");
		r.add("232110900184");
		r.add("232110900186");
		r.add("232110900187");
		r.add("232110900188");
		r.add("232110900189");
		r.add("232110900195");
		r.add("232110900201");
		r.add("232110900202");
		r.add("232110900203");
		r.add("232110900204");
		r.add("232110900212");
		r.add("232110900216");
		r.add("232110900219");
		r.add("232110900223");
		r.add("232110900224");
		r.add("232110900229");
		r.add("232110900230");
		r.add("232110900232");
		r.add("232110900233");
		r.add("232110900234");
		r.add("232110900239");
		r.add("232110900242");
		r.add("232110900244");
		r.add("232110900247");
		r.add("232110900260");
		r.add("232110900289");
		r.add("232110900293");
		r.add("232110900301");
		r.add("232110900355");
		r.add("232110900358");
		r.add("232110900360");
		r.add("232110900366");
		r.add("232110900377");
		r.add("232110900378");
		r.add("232110900381");
		r.add("232110900417");
		r.add("232110900426");
		r.add("232110900449");
		r.add("232110900455");
		r.add("233110900092");
		r.add("233110900093");
		r.add("233110900095");
		r.add("233110900096");
		r.add("233110900097");
		r.add("233110900098");
		r.add("233110900099");
		r.add("233110900100");
		r.add("233110900101");
		r.add("233110900102");
		r.add("233110900103");
		r.add("233110900104");
		r.add("233110900108");
		r.add("233110900109");
		r.add("233110900110");
		r.add("233110900111");
		r.add("233110900112");
		r.add("233110900113");
		r.add("233110900116");
		r.add("233110900120");
		r.add("233110900121");
		r.add("233110900122");
		r.add("233110900123");
		r.add("233110900124");
		r.add("233110900125");
		r.add("233110900126");
		r.add("233110900127");
		r.add("233110900128");
		r.add("233110900129");
		r.add("233110900130");
		r.add("233110900131");
		r.add("233110900132");
		r.add("233110900133");
		r.add("233110900134");
		r.add("233110900135");
		r.add("233110900136");
		r.add("233110900137");
		r.add("233110900138");
		r.add("233110900139");
		r.add("233110900140");
		r.add("233110900141");
		r.add("233110900142");
		r.add("233110900143");
		r.add("233110900144");
		r.add("233110900145");
		r.add("233110900146");
		r.add("233110900148");
		r.add("233110900149");
		r.add("233110900150");
		r.add("233110900151");
		r.add("233110900152");
		r.add("233110900153");
		r.add("233110900154");
		r.add("233110900155");
		r.add("233110900156");
		r.add("233110900157");
		r.add("233110900158");
		r.add("233110900160");
		r.add("233110900161");
		r.add("233110900162");
		r.add("233110900163");
		r.add("233110900164");
		r.add("233110900165");
		r.add("233110900166");
		r.add("233110900167");
		r.add("233110900168");
		r.add("233110900169");
		r.add("233110900170");
		r.add("233110900171");
		r.add("233110900172");
		r.add("233110900173");
		r.add("233110900174");
		r.add("233110900175");
		r.add("233110900176");
		r.add("233110900177");
		r.add("233110900178");
		r.add("233110900179");
		r.add("233110900180");
		r.add("233110900181");
		r.add("233110900182");
		r.add("233110900183");
		r.add("233110900184");
		r.add("233110900185");
		r.add("233110900186");
		r.add("233110900187");
		r.add("233110900188");
		r.add("233110900189");
		r.add("233110900190");
		r.add("233110900191");
		r.add("233110900192");
		r.add("233110900193");
		r.add("233110900194");
		r.add("233110900195");
		r.add("233110900196");
		r.add("233110900197");
		r.add("233110900198");
		r.add("233110900199");
		r.add("233110900200");
		r.add("233110900201");
		r.add("233110900202");
		r.add("233110900203");
		r.add("233110900204");
		r.add("233110900205");
		r.add("233110900206");
		r.add("233110900208");
		r.add("233110900210");
		r.add("233110900211");
		r.add("233110900212");
		r.add("233110900213");
		r.add("233110900214");
		r.add("233110900215");
		r.add("233110900216");
		r.add("233110900217");
		r.add("233110900218");
		r.add("233110900219");
		r.add("233110900220");
		r.add("233110900221");
		r.add("233110900222");
		r.add("233110900223");
		r.add("233110900224");
		r.add("233110900225");
		r.add("233110900235");
		r.add("233110900236");
		r.add("233110900237");
		r.add("233110900239");
		r.add("233110900240");
		r.add("233110900241");
		r.add("233110900242");
		r.add("233110900243");
		r.add("233110900244");
		r.add("233110900245");
		r.add("233110900246");
		r.add("233110900248");
		r.add("233110900249");
		r.add("233110900250");
		r.add("233110900251");
		r.add("233110900252");
		r.add("233110900253");
		r.add("233110900254");
		r.add("233110900255");
		r.add("233110900256");
		r.add("233110900257");
		r.add("233110900258");
		r.add("233110900259");
		r.add("233110900261");
		r.add("233110900262");
		r.add("233110900263");
		r.add("233110900264");
		r.add("233110900265");
		r.add("233110900266");
		r.add("233110900267");
		r.add("233110900269");
		r.add("233110900270");
		r.add("233110900271");
		r.add("233110900273");
		r.add("233110900274");
		r.add("233110900275");
		r.add("233110900276");
		r.add("233110900277");
		r.add("233110900278");
		r.add("233110900279");
		r.add("233110900280");
		r.add("233110900281");
		r.add("233110900282");
		r.add("233110900283");
		r.add("233110900284");
		r.add("233110900285");
		r.add("233110900287");
		r.add("233110900288");
		r.add("233110900289");
		r.add("233110900291");
		r.add("233110900294");
		r.add("233110900295");
		r.add("233110900298");
		r.add("233110900299");
		r.add("233110900303");
		r.add("233110900310");
		r.add("233110900311");
		r.add("233110900315");
		r.add("233110900320");
		r.add("233110900321");
		r.add("233110900325");
		r.add("233110900335");
		r.add("233110900337");
		r.add("233110900338");
		r.add("233110900344");
		r.add("233110900359");
		r.add("234110900142");
		r.add("234110900144");
		r.add("234110900145");
		r.add("234110900151");
		r.add("234110900152");
		r.add("234110900153");
		r.add("234110900172");
		r.add("234110900174");
		r.add("234110900179");
		r.add("234110900180");
		r.add("234110900181");
		r.add("234110900183");
		r.add("234110900186");
		r.add("234110900187");
		r.add("234110900192");
		r.add("234110900193");
		r.add("234110900198");
		r.add("234110900199");
		r.add("234110900200");
		r.add("234110900202");
		r.add("234110900204");
		r.add("234110900205");
		r.add("234110900206");
		r.add("234110900207");
		r.add("234110900211");
		r.add("234110900212");
		r.add("234110900214");
		r.add("234110900216");
		r.add("234110900217");
		r.add("234110900219");
		r.add("234110900220");
		r.add("234110900221");
		r.add("234110900226");
		r.add("234110900227");
		r.add("234110900228");
		r.add("234110900229");
		r.add("234110900237");
		r.add("234110900240");
		r.add("234110900241");
		r.add("234110900243");
		r.add("234110900264");
		r.add("234110900280");
		r.add("235110900264");
		r.add("235110900308");
		r.add("235110900310");
		r.add("235110900318");
		r.add("235110900325");
		r.add("235110900328");
		r.add("235110900332");
		r.add("235110900379");
		r.add("235110900380");
		r.add("235110900381");
		r.add("235110900403");
		r.add("235110900413");
		r.add("235110900416");
		r.add("235110900417");
		r.add("235110900420");
		r.add("235110900422");
		r.add("235110900423");
		r.add("235110900424");
		r.add("235110900425");
		r.add("235110900426");
		r.add("235110900428");
		r.add("235110900431");
		r.add("235110900432");
		r.add("235110900434");
		r.add("235110900438");
		r.add("235110900439");
		r.add("235110900444");
		r.add("235110900445");
		r.add("235110900446");
		r.add("235110900447");
		r.add("235110900452");
		r.add("235110900454");
		r.add("235110900455");
		r.add("235110900456");
		r.add("235110900457");
		r.add("235110900461");
		r.add("235110900462");
		r.add("235110900466");
		r.add("235110900470");
		r.add("235110900471");
		r.add("235110900472");
		r.add("235110900483");
		r.add("235110900484");
		r.add("235110900488");
		r.add("235110900501");
		r.add("235110900536");
		r.add("235110900541");
		r.add("235110900556");
		r.add("235110900557");
		r.add("235110900560");
		r.add("235110900561");
		r.add("235110900564");
		r.add("235110900565");
		r.add("235110900566");
		r.add("235110900570");
		r.add("235110900578");
		r.add("235110900580");
		r.add("235110900581");
		r.add("235110900587");
		r.add("235110900588");
		r.add("235110900597");
		r.add("235110900598");
		r.add("236110900147");
		r.add("236110900148");
		r.add("236110900155");
		r.add("236110900178");
		r.add("236110900179");
		r.add("236110900184");
		r.add("236110900186");
		r.add("236110900190");
		r.add("236110900199");
		r.add("236110900209");
		r.add("236110900215");
		r.add("236110900217");
		r.add("236110900218");
		r.add("236110900222");
		r.add("236110900226");
		r.add("236110900227");
		r.add("236110900229");
		r.add("236110900232");
		r.add("236110900242");
		r.add("236110900246");
		r.add("236110900247");
		r.add("236110900249");
		r.add("236110900252");
		r.add("236110900253");
		r.add("236110900254");
		r.add("236110900256");
		r.add("236110900257");
		r.add("236110900258");
		r.add("236110900259");
		r.add("236110900260");
		r.add("236110900261");
		r.add("236110900262");
		r.add("236110900263");
		r.add("236110900265");
		r.add("236110900266");
		r.add("236110900267");
		r.add("236110900268");
		r.add("236110900270");
		r.add("236110900271");
		r.add("236110900272");
		r.add("236110900273");
		r.add("236110900274");
		r.add("236110900275");
		r.add("236110900276");
		r.add("236110900277");
		r.add("236110900278");
		r.add("236110900279");
		r.add("236110900280");
		r.add("236110900281");
		r.add("236110900282");
		r.add("236110900283");
		r.add("236110900284");
		r.add("236110900285");
		r.add("236110900286");
		r.add("236110900287");
		r.add("236110900288");
		r.add("236110900289");
		r.add("236110900290");
		r.add("236110900291");
		r.add("236110900293");
		r.add("236110900295");
		r.add("236110900296");
		r.add("236110900297");
		r.add("236110900298");
		r.add("236110900300");
		r.add("236110900301");
		r.add("236110900303");
		r.add("236110900304");
		r.add("236110900305");
		r.add("236110900306");
		r.add("236110900307");
		r.add("236110900308");
		r.add("236110900309");
		r.add("236110900310");
		r.add("236110900311");
		r.add("236110900312");
		r.add("236110900313");
		r.add("236110900314");
		r.add("236110900315");
		r.add("236110900316");
		r.add("236110900317");
		r.add("236110900318");
		r.add("236110900321");
		r.add("236110900323");
		r.add("236110900324");
		r.add("236110900326");
		r.add("236110900327");
		r.add("236110900328");
		r.add("236110900333");
		r.add("236110900334");
		r.add("236110900341");
		r.add("236110900343");
		r.add("236110900344");
		r.add("236110900345");
		r.add("236110900347");
		r.add("236110900348");
		r.add("236110900350");
		r.add("236110900351");
		r.add("236110900353");
		r.add("236110900354");
		r.add("236110900355");
		r.add("236110900356");
		r.add("236110900357");
		r.add("236110900359");
		r.add("236110900360");
		r.add("236110900362");
		r.add("236110900363");
		r.add("236110900364");
		r.add("236110900365");
		r.add("236110900367");
		r.add("236110900368");
		r.add("236110900369");
		r.add("236110900378");
		r.add("236110900379");
		r.add("236110900384");
		r.add("236110900386");
		r.add("236110900388");
		r.add("236110900393");
		r.add("236110900394");
		r.add("236110900396");
		r.add("236110900400");
		r.add("236110900404");
		r.add("236110900414");
		r.add("236110900421");
		r.add("236110900422");
		r.add("236110900423");
		r.add("236110900427");
		r.add("236110900438");
		r.add("236110900454");
		r.add("236110900457");
		r.add("236110900478");
		r.add("236110900494");
		r.add("237110900195");
		r.add("237110900201");
		r.add("237110900202");
		r.add("237110900203");
		r.add("237110900205");
		r.add("237110900209");
		r.add("237110900217");
		r.add("237110900220");
		r.add("237110900226");
		r.add("237110900236");
		r.add("237110900237");
		r.add("237110900239");
		r.add("237110900240");
		r.add("237110900242");
		r.add("237110900247");
		r.add("237110900248");
		r.add("237110900250");
		r.add("237110900253");
		r.add("237110900254");
		r.add("237110900255");
		r.add("237110900256");
		r.add("237110900264");
		r.add("237110900266");
		r.add("237110900267");
		r.add("237110900272");
		r.add("237110900273");
		r.add("237110900276");
		r.add("237110900279");
		r.add("237110900281");
		r.add("237110900290");
		r.add("237110900294");
		r.add("237110900295");
		r.add("237110900296");
		r.add("237110900297");
		r.add("237110900298");
		r.add("237110900300");
		r.add("237110900302");
		r.add("237110900305");
		r.add("237110900306");
		r.add("237110900307");
		r.add("237110900315");
		r.add("237110900317");
		r.add("237110900319");
		r.add("237110900320");
		r.add("237110900321");
		r.add("237110900335");
		r.add("237110900339");
		r.add("237110900349");
		r.add("237110900365");
		r.add("238110900096");
		r.add("238110900118");
		r.add("238110900119");
		r.add("238110900120");
		r.add("238110900123");
		r.add("238110900124");
		r.add("238110900125");
		r.add("238110900126");
		r.add("238110900127");
		r.add("238110900128");
		r.add("238110900130");
		r.add("238110900132");
		r.add("238110900133");
		r.add("238110900134");
		r.add("238110900135");
		r.add("238110900139");
		r.add("238110900140");
		r.add("238110900142");
		r.add("238110900143");
		r.add("238110900145");
		r.add("238110900150");
		r.add("238110900151");
		r.add("238110900152");
		r.add("238110900154");
		r.add("238110900155");
		r.add("238110900156");
		r.add("238110900157");
		r.add("238110900158");
		r.add("238110900167");
		r.add("238110900168");
		r.add("238110900170");
		r.add("238110900175");
		r.add("238110900176");
		r.add("238110900180");
		r.add("238110900181");
		r.add("238110900182");
		r.add("238110900183");
		r.add("238110900184");
		r.add("238110900185");
		r.add("238110900191");
		r.add("238110900194");
		r.add("238110900197");
		r.add("238110900198");
		r.add("238110900199");
		r.add("238110900205");
		r.add("238110900214");
		r.add("238110900215");
		r.add("238110900225");
		r.add("238110900233");
		r.add("238110900242");
		r.add("238110900246");
		r.add("240110900069");
		r.add("240110900098");
		r.add("240110900102");
		r.add("240110900105");
		r.add("240110900107");
		r.add("240110900109");
		r.add("240110900111");
		r.add("240110900116");
		r.add("240110900127");
		r.add("240110900131");
		r.add("240110900132");
		r.add("240110900136");
		r.add("240110900137");
		r.add("240110900139");
		r.add("240110900150");
		r.add("240110900154");
		r.add("240110900159");
		r.add("240110900164");
		r.add("240110900165");
		r.add("240110900171");
		r.add("240110900178");
		r.add("240110900179");
		r.add("240110900181");
		r.add("240110900182");
		r.add("240110900188");
		r.add("240110900192");
		r.add("240110900201");
		r.add("240110900225");
		r.add("240110900248");
		r.add("241110900154");
		r.add("241110900207");
		r.add("241110900233");
		r.add("241110900253");
		r.add("241110900258");
		r.add("241110900261");
		r.add("241110900262");
		r.add("241110900263");
		r.add("241110900264");
		r.add("241110900266");
		r.add("241110900267");
		r.add("241110900269");
		r.add("241110900278");
		r.add("241110900280");
		r.add("241110900282");
		r.add("241110900283");
		r.add("241110900285");
		r.add("241110900286");
		r.add("241110900287");
		r.add("241110900288");
		r.add("241110900289");
		r.add("241110900292");
		r.add("241110900293");
		r.add("241110900294");
		r.add("241110900298");
		r.add("241110900300");
		r.add("241110900302");
		r.add("241110900303");
		r.add("241110900304");
		r.add("241110900306");
		r.add("241110900307");
		r.add("241110900308");
		r.add("241110900309");
		r.add("241110900310");
		r.add("241110900311");
		r.add("241110900312");
		r.add("241110900315");
		r.add("241110900317");
		r.add("241110900318");
		r.add("241110900319");
		r.add("241110900320");
		r.add("241110900321");
		r.add("241110900323");
		r.add("241110900324");
		r.add("241110900325");
		r.add("241110900327");
		r.add("241110900328");
		r.add("241110900330");
		r.add("241110900331");
		r.add("241110900332");
		r.add("241110900336");
		r.add("241110900337");
		r.add("241110900341");
		r.add("241110900343");
		r.add("241110900345");
		r.add("241110900346");
		r.add("241110900348");
		r.add("241110900349");
		r.add("241110900352");
		r.add("241110900353");
		r.add("241110900354");
		r.add("241110900355");
		r.add("241110900356");
		r.add("241110900357");
		r.add("241110900358");
		r.add("241110900362");
		r.add("241110900364");
		r.add("241110900366");
		r.add("241110900371");
		r.add("241110900372");
		r.add("241110900376");
		r.add("241110900377");
		r.add("241110900379");
		r.add("241110900380");
		r.add("241110900381");
		r.add("241110900382");
		r.add("241110900383");
		r.add("241110900384");
		r.add("241110900385");
		r.add("241110900388");
		r.add("241110900389");
		r.add("241110900390");
		r.add("241110900392");
		r.add("241110900393");
		r.add("241110900395");
		r.add("241110900402");
		r.add("241110900403");
		r.add("241110900404");
		r.add("241110900406");
		r.add("241110900407");
		r.add("241110900411");
		r.add("241110900414");
		r.add("241110900415");
		r.add("241110900420");
		r.add("241110900421");
		r.add("242110900105");
		r.add("242110900106");
		r.add("242110900128");
		r.add("242110900138");
		r.add("242110900140");
		r.add("242110900144");
		r.add("242110900150");
		r.add("242110900170");
		r.add("242110900171");
		r.add("242110900182");
		r.add("242110900187");
		r.add("242110900188");
		r.add("242110900189");
		r.add("242110900191");
		r.add("242110900192");
		r.add("242110900193");
		r.add("242110900197");
		r.add("242110900199");
		r.add("242110900202");
		r.add("242110900203");
		r.add("242110900204");
		r.add("242110900205");
		r.add("242110900206");
		r.add("242110900207");
		r.add("242110900208");
		r.add("242110900209");
		r.add("242110900213");
		r.add("242110900214");
		r.add("242110900215");
		r.add("242110900219");
		r.add("242110900220");
		r.add("242110900221");
		r.add("242110900223");
		r.add("242110900224");
		r.add("242110900225");
		r.add("242110900226");
		r.add("242110900227");
		r.add("242110900230");
		r.add("242110900233");
		r.add("242110900234");
		r.add("242110900235");
		r.add("242110900238");
		r.add("242110900240");
		r.add("242110900241");
		r.add("242110900242");
		r.add("242110900243");
		r.add("242110900244");
		r.add("242110900245");
		r.add("242110900246");
		r.add("242110900247");
		r.add("242110900248");
		r.add("242110900249");
		r.add("242110900250");
		r.add("242110900252");
		r.add("242110900253");
		r.add("242110900254");
		r.add("242110900256");
		r.add("242110900257");
		r.add("242110900259");
		r.add("242110900260");
		r.add("242110900261");
		r.add("242110900263");
		r.add("242110900265");
		r.add("242110900266");
		r.add("242110900267");
		r.add("242110900268");
		r.add("242110900270");
		r.add("242110900271");
		r.add("242110900273");
		r.add("242110900274");
		r.add("242110900276");
		r.add("242110900281");
		r.add("242110900282");
		r.add("242110900283");
		r.add("242110900284");
		r.add("242110900285");
		r.add("242110900286");
		r.add("242110900289");
		r.add("242110900290");
		r.add("242110900291");
		r.add("242110900292");
		r.add("242110900293");
		r.add("242110900294");
		r.add("242110900295");
		r.add("242110900296");
		r.add("242110900297");
		r.add("242110900298");
		r.add("242110900299");
		r.add("242110900300");
		r.add("242110900301");
		r.add("242110900302");
		r.add("242110900303");
		r.add("242110900305");
		r.add("242110900306");
		r.add("242110900307");
		r.add("242110900308");
		r.add("242110900309");
		r.add("242110900310");
		r.add("242110900312");
		r.add("242110900314");
		r.add("242110900315");
		r.add("242110900317");
		r.add("242110900319");
		r.add("242110900322");
		r.add("242110900324");
		r.add("242110900325");
		r.add("242110900327");
		r.add("242110900328");
		r.add("242110900329");
		r.add("242110900331");
		r.add("242110900334");
		r.add("242110900337");
		r.add("242110900338");
		r.add("242110900339");
		r.add("242110900340");
		r.add("242110900341");
		r.add("242110900344");
		r.add("242110900345");
		r.add("242110900347");
		r.add("242110900348");
		r.add("242110900349");
		r.add("242110900351");
		r.add("242110900352");
		r.add("242110900354");
		r.add("242110900355");
		r.add("242110900356");
		r.add("242110900357");
		r.add("242110900365");
		r.add("242110900369");
		r.add("242110900371");
		r.add("242110900373");
		r.add("242110900374");
		r.add("242110900375");
		r.add("242110900376");
		r.add("242110900377");
		r.add("242110900378");
		r.add("242110900382");
		r.add("242110900387");
		r.add("242110900388");
		r.add("242110900389");
		r.add("242110900392");
		r.add("242110900396");
		r.add("242110900398");
		r.add("242110900399");
		r.add("242110900402");
		r.add("242110900403");
		r.add("242110900405");
		r.add("242110900406");
		r.add("242110900409");
		r.add("242110900410");
		r.add("242110900412");
		r.add("242110900413");
		r.add("242110900414");
		r.add("242110900415");
		r.add("242110900416");
		r.add("242110900417");
		r.add("242110900418");
		r.add("242110900419");
		r.add("242110900420");
		r.add("242110900424");
		r.add("242110900427");
		r.add("242110900428");
		r.add("242110900429");
		r.add("242110900430");
		r.add("242110900431");
		r.add("242110900432");
		r.add("242110900439");
		r.add("242110900443");
		r.add("242110900444");
		r.add("242110900445");
		r.add("242110900446");
		r.add("242110900447");
		r.add("242110900449");
		r.add("242110900451");
		r.add("242110900452");
		r.add("242110900453");
		r.add("242110900455");
		r.add("242110900456");
		r.add("242110900457");
		r.add("242110900458");
		r.add("242110900459");
		r.add("242110900461");
		r.add("242110900462");
		r.add("242110900463");
		r.add("242110900464");
		r.add("242110900465");
		r.add("242110900466");
		r.add("242110900468");
		r.add("242110900470");
		r.add("242110900471");
		r.add("242110900474");
		r.add("242110900475");
		r.add("242110900478");
		r.add("242110900480");
		r.add("242110900484");
		r.add("242110900486");
		r.add("242110900487");
		r.add("242110900490");
		r.add("242110900491");
		r.add("242110900492");
		r.add("242110900497");
		r.add("242110900498");
		r.add("242110900500");
		r.add("242110900502");
		r.add("242110900503");
		r.add("242110900504");
		r.add("242110900506");
		r.add("242110900509");
		r.add("242110900516");
		r.add("242110900519");
		r.add("242110900521");
		r.add("242110900522");
		r.add("242110900523");
		r.add("242110900524");
		r.add("242110900527");
		r.add("242110900532");
		r.add("242110900541");
		r.add("242110900569");
		r.add("242110900570");
		r.add("242110900580");
		r.add("242110900588");
		r.add("220110900278");
		return r;
	}
	public static List<String> upd_ELF459_SRCFLAG_prodKind69_noAgreeQueryEjVer_2020_part3(){ //noAgreeQueryEjVer_2020_part3 共 1筆
		List<String> r = new ArrayList<String>();
		r.add("014110900391"); //客戶更改ID , select custid as newId, orgCustId from lms.c900m01i where custid in (select custid from lms.l140m01a where cntrno='014110900391')
		return r;
	}
}
