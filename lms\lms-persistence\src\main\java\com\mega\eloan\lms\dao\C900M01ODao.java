/* 
 * C900M01ODao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;
import tw.com.iisi.cap.dao.IGenericDao;
import com.mega.eloan.lms.model.C900M01O;

/** 歡喜信貸自動派案維護-被指派分行清單檔 **/
public interface C900M01ODao extends IGenericDao<C900M01O> {

	C900M01O findByOid(String oid);

	List<C900M01O> findListByOid(String oid);

	List<C900M01O> findByAssigneeBrchId(String assigneeBrchId);

	List<C900M01O> findAll();
}