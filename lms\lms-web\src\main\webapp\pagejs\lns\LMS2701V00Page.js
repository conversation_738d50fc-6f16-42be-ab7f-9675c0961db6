pageJsInit(function() {
	$(function() {
		openQuery();

		$("#buttonPanel").find("#btnView").click(function() {
			openQuery();
		});

		var grid = $("#gridview").iGrid({
			handler: 'lms2701gridhandler',
			height: 350,
			width: 785,
			autowidth: false,
			postData: {
				formAction: "queryView",
				init: true
			},
			rowNum: 15,
			sortname: "createTime|applyLoanDay|custId",
			sortorder: "desc|asc|desc",//desc
			multiselect: true,
			colModel: [{
				colHeader: i18n.lms2701v00["custId"],
				align: "left", width: 90, sortable: true, name: 'custId',
				formatter: 'click', onclick: openDoc
			}, {
				colHeader: i18n.lms2701v00["dupNo"],
				align: "left", width: 10, sortable: true, name: 'dupNo'
			}, {
				colHeader: i18n.lms2701v00["custName"],
				align: "left", width: 120, sortable: true, name: 'custName'
			}, {
				colHeader: i18n.lms2701v00["applyLoanDay"],
				align: "center", width: 60, sortable: true, name: 'applyLoanDay'
			}, {
				colHeader: i18n.lms2701v00["grntPaper"], //保證案號
				align: "left",
				width: 80, // 設定寬
				name: 'grntPaper',
				hidden: true
			}, {
				colHeader: i18n.lms2701v00["dataStatus"], //回覆結果
				align: "left",
				width: 60, // 設定寬
				name: 'dataStatus'
				//            ,
				//            hidden: hidden05O
			}, {
				colHeader: i18n.lms2701v00["description"], //處理說明
				align: "left",
				width: 100, // 設定寬
				name: 'description'
				//            ,
				//            hidden: hidden05O
			}, {
				name: 'oid',
				hidden: true
			}, {
				name: 'mainId',
				hidden: true
			}],
			ondblClickRow: function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
				var data = $("#gridview").getRowData(rowid);
				openDoc(null, null, data);
			}
		});
	});
});

function openQuery(){
    $("#filterForm").find("#custId").val('');
    $("#filterForm").find("#dupNo").val('');
    $("#filterBox").thickbox({ // 使用選取的內容進行彈窗
        title: i18n.lms2701v00['L270M01A.title01'],
        width: 400,
        height: 100,
        valign: "bottom",
        align: "center",
        i18n: i18n.def,
        buttons: {
            sure: function(){
                var custId = $("#filterForm").find("#custId").val();
                var dupNo = $("#filterForm").find("#dupNo").val();

                if ($.trim(custId) == "" || $.trim(dupNo) == "") {
                    return CommonAPI.showErrorMessage(i18n.def["val.checkID"]);
                }
                if (/[\W]/g.test(custId) || /[\W]/g.test(dupNo)) {//!/\d/.test(dupNo)) {
                    return CommonAPI.showErrorMessage(i18n.def["val.alphanum"]);
                }

                $("#gridview").jqGrid("setGridParam", {
                    postData: {
                        init: false,
                        custId: custId,
                        dupNo: dupNo
                    },
                    search: true
                }).trigger("reloadGrid");
                $.thickbox.close();
            },
            cancel: function(){
                $.thickbox.close();
            }
        }
    });
}

function openDoc(cellvalue, options, rowObject){
    $("#l270m01aBox").thickbox({ // 使用選取的內容進行彈窗
        title: i18n.lms2701v00['L270M01A.title02'],
        width: 800,
        height: 600,
        valign: "top",
        align: "left",
        i18n: i18n.def,
        open: function(){
            $('#l270m01aForm').reset();
            $.ajax({
                handler: "lms2701m01formhandler",
                data: {
                    mainOid: rowObject.oid,
                    formAction: "getL270M01A"
                },
            }).done(function(responseData){
                    $('#l270m01aForm').injectData(responseData);
                
            });
        },
        buttons: {
            "close": function(){
                $.thickbox.close();
            }
        }
    });
}