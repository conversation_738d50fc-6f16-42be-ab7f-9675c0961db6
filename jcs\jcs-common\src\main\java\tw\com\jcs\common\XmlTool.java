/**
 *JC Software.
 * copyright @jcs.com.tw 2003~2006. all right reserved.
 */
package tw.com.jcs.common;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.Reader;
import java.io.StringReader;
import java.io.StringWriter;
import java.text.DecimalFormat;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import javax.xml.XMLConstants;
import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import javax.xml.bind.Unmarshaller;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Source;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerConfigurationException;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import javax.xml.transform.stream.StreamSource;
import javax.xml.validation.Schema;
import javax.xml.validation.SchemaFactory;
import javax.xml.validation.Validator;
import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathConstants;
import javax.xml.xpath.XPathExpressionException;
import javax.xml.xpath.XPathFactory;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.w3c.dom.Text;
import org.xml.sax.ContentHandler;
import org.xml.sax.InputSource;
import org.xml.sax.SAXException;

/**
 * XML Utility Tool
 * 
 * <AUTHOR> Software Inc.
 * @version 1.0
 */
public class XmlTool {

    private static final Logger logger = LoggerFactory.getLogger(XmlTool.class);

    private static final XPathFactory xpathFactory = XPathFactory.newInstance();

    private static final String DEFAULT_ENCODING = "utf-8";

    private static Transformer transformer;

    /**
     * 取得transformer
     * 
     * @return
     */
    private static Transformer getTransformer() {
        if (transformer == null) {
            TransformerFactory tFactory = TransformerFactory.newInstance();
            try {
                transformer = tFactory.newTransformer();
            } catch (TransformerConfigurationException e) {
                logger.error(e.getMessage(), e);
            }
        }
        return transformer;
    }

    /**
     * 將節點Node依預設的編碼轉換為XML
     * 
     * @see XmlTool#DEFAULT_ENCODING
     * @param node
     * @return
     */
    public static String toXML(Node node) {
        return toXML(node, DEFAULT_ENCODING);
    }

    /**
     * 將節點Node依指定的編碼轉換為XML
     * 
     * @param node
     *            節點
     * @param encoding
     *            編碼
     * @return
     */
    public static String toXML(Node node, String encoding) {
        StringWriter out = new StringWriter(1024);
        try {
            Transformer tran = getTransformer();
            tran.setOutputProperty(OutputKeys.ENCODING, encoding);
            tran.transform(new DOMSource(node), new StreamResult(out));
            return out.toString();
        } catch (TransformerException e) {
            logger.error("無法將Node {}轉換為XML：{}", node.getNodeName(), e.getMessage());
        }
        return "";
    }

    /**
     * 使用自訂的handler，將JAXB標記物件轉換為XML
     * 
     * @param obj
     *            JAXB Element
     * @param handler
     *            ContentHandler
     */
    public static void toXML(Object obj, ContentHandler handler) {
        try {
            JAXBContext ctx = JAXBContext.newInstance(obj.getClass());
            Marshaller mr = ctx.createMarshaller();
            mr.marshal(obj, handler);
        } catch (JAXBException e) {
            // fix checkmarx Privacy Violation 20221104 path 1, 2
            logger.error("無法將{}轉換為XML：{}", obj.getClass(), e.getMessage());
        }
    }

    /**
     * 將JAXB標記物件轉換為XML
     * 
     * @param obj
     *            JAXB Element
     * @return XML string
     */
    public static String toXML(Object obj) {
        StringWriter writer = new StringWriter(1024);
        try {
            JAXBContext ctx = JAXBContext.newInstance(obj.getClass());
            Marshaller mr = ctx.createMarshaller();
            mr.marshal(obj, writer);
        } catch (JAXBException e) {
            logger.error("無法將{}轉換為XML：{}", obj, e.getMessage());
        }
        return writer.toString();
    }

    /**
     * 將XML轉換為指定的JAXB標記類別
     * 
     * @param xml
     *            XML string
     * @param xmlClass
     *            JAXB Element Class
     * @return JAXB Element Object
     */
    @SuppressWarnings("unchecked")
    public static <T> T fromXML(String xml, Class<T> xmlClass) {
        try {
            JAXBContext ctx = JAXBContext.newInstance(xmlClass);
            Unmarshaller umr = ctx.createUnmarshaller();
            StringReader reader = new StringReader(xml);
            return (T) umr.unmarshal(reader);
        } catch (JAXBException e) {
            logger.error("無法將XML轉換為{}：{}", xmlClass, e.getMessage());
        }
        return null;
    }

    /**
     * 讀取符合XPath所指定的第一個節點中的值 .
     * 
     * @param sXML
     *            XML格式的字串
     * @param xpath
     *            XPath 路徑
     */
    public static String selectSingleNodeValue(String sXML, String xpath) {
        Document document = null;
        try {
            document = XmlTool.newDocument(sXML);
        } catch (IOException e) {
            logger.debug("XmlTool", e);
        }
        return selectSingleNodeValue(document, xpath);
    }

    /**
     * 讀取Dom物件中符合XPath所指定的第一個節點中的值 .
     * 
     * 
     * Note:呼叫此Mthod時傳入Document 會比傳入Node時使用較少的計憶體[memory resource].
     * 
     * @param node
     * @param xpath
     *            XPath 路徑
     */
    public static Node selectSingleNode(Node node, String xpath) {
        if (node == null) {
            logger.debug("Warning->參數document =null。");
            return null;
        }

        XPath xp = xpathFactory.newXPath();
        NodeList list = null;
        try {
            list = (NodeList) xp.evaluate(encodeForXPath(xpath), node, XPathConstants.NODESET);
            if (list.getLength() > 0) {
                return list.item(0);
            }
        } catch (XPathExpressionException e) {
            logger.error("讀取XML發生錯誤：{} [xpath={}]", e.getMessage(), xpath);
        }

        logger.debug("Warning->找不到[xpath=" + xpath + "]所指定的節點。");
        return null;
    }

    /**
     * 讀取Dom物件中符合XPath所指定的第一個節點中的值 .
     * 
     * 
     * Note:呼叫此Mthod時傳入Document 會比傳入Node時使用較少的計憶體[memory resource].
     * 
     * @param document
     * @param xpath
     *            XPath 路徑
     */
    public static String selectSingleNodeValue(Node node, String xpath) {
        Node targetNode = ".".equals(xpath) ? node : selectSingleNode(node, xpath);
        return (targetNode == null ? "" : (targetNode.getFirstChild() == null) ? "" : (targetNode.getFirstChild().getNodeValue() == null) ? "" : targetNode.getFirstChild().getNodeValue().trim());
    }

    /**
     * 設定符合XPath所指定的第一個節點中的值 .
     * 
     * @param node
     * @param xpath
     *            XPath 路徑
     */
    public static void setSingleNodeValue(Node node, String xpath, String value) throws Exception {
        Node nodeResult = selectSingleNode(node, xpath);
        if (nodeResult != null) {
            if (nodeResult.getFirstChild() == null) {
                nodeResult.appendChild(nodeResult.getOwnerDocument().createTextNode(value));
            } else {
                nodeResult.getFirstChild().setNodeValue(value);
            }
        }
    }

    /**
     * 設定所有符合XPath所指定的節點中的值 .
     * 
     * @param node
     * @param xpath
     *            XPath 路徑
     */
    public static void setNodesValue(Node node, String xpath, String value) throws Exception {
        NodeList nodeList = selectNodes(node, xpath);

        int i = 0;
        for (; i < nodeList.getLength(); ++i) {
            Node nodeResult = nodeList.item(i);
            if (nodeResult.getFirstChild() == null) {
                if (node instanceof Document) {
                    nodeResult.appendChild(((Document) node).createTextNode(value));
                } else {
                    nodeResult.appendChild(node.getOwnerDocument().createTextNode(value));
                }
            } else {
                nodeResult.getFirstChild().setNodeValue(value);
            }
        }

        if (i == 0)
            throw new Exception("找不到" + xpath + "所指定的節點。");
    }

    /**
     * 將所有符合XPath所指定的節點中的值轉為指定的格式.
     * 
     * 94/04/29 added.
     * 
     * @param nodeInput
     *            要轉換的節點
     * @param xpath
     *            XPath 路徑
     * @param assignedPattern
     *            指定的格式
     */
    public static void convertToFormat(Node nodeInput, String xpath, String assignedPattern) throws Exception {
        NodeList nodeList = selectNodes(nodeInput, xpath);

        DecimalFormat assignedFormat = new DecimalFormat(assignedPattern);
        for (int i = 0; i < nodeList.getLength(); ++i) {
            Node node = nodeList.item(i);
            if (node.getFirstChild() != null && node.getFirstChild().getNodeValue().trim().length() > 0) {
                double d = Double.parseDouble(node.getFirstChild().getNodeValue());
                node.getFirstChild().setNodeValue(assignedFormat.format(d));
            }
        }

    }

    /**
     * 讀取符合XPath所指定的第一個節點中的值 .
     * 
     * 94/04/29 added.
     * 
     * @param node
     * @param xpath
     *            XPath 路徑
     */
    public static NodeList selectNodes(Node node, String xpath) {
        XPath xp = xpathFactory.newXPath();
        try {
            return (NodeList) xp.evaluate(encodeForXPath(xpath), node, XPathConstants.NODESET);
        } catch (XPathExpressionException e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     * 將XML 檔( InputStream ) parse 為 Document.
     * 
     * 註:使用預設encoding:Big5
     * 
     * @param fis
     *            (String).
     * @return Document.
     * @throws IOException
     */
    public static Document newDocument(InputStream fis) throws IOException {
        return newDocument(fis, "UTF-8");
    }

    /**
     * 將XML 檔( InputStream ) parse 為 Document.
     * 
     * @param fis
     *            (String).
     * @return Document.
     * @throws IOException
     */
    public static Document newDocument(InputStream fis, String encoding) throws IOException {
        InputSource in = new InputSource(fis);
        return newDocument(in, encoding);
    }

    /**
     * 將XML 格式的字串 parse 為 Document.
     * 
     * 註:使用預設encoding:Big5
     * 
     * @param sXML
     *            (String).
     * @return Document.
     * @throws IOException
     */
    public static Document newDocument(String sXML) throws IOException {
        return newDocument(sXML, "UTF-8");
    }

    /**
     * 將XML 格式的字串 parse 為 Document.
     * 
     * @param sXML
     *            (String).
     * @return Document.
     * @throws IOException
     */
    public static Document newDocument(String sXML, String encoding) throws IOException {
        StringReader reader = new StringReader(sXML);
        InputSource in = new InputSource(reader);
        return newDocument(in, encoding);
    }

    /**
     * 將InputSource parse 為 Document.(預設UTF-8)
     * 
     * @param in
     * @return
     * @throws IOException
     */
    public static Document newDocument(InputSource in) throws IOException {
        return newDocument(in, "UTF-8");
    }

    /**
     * 把InputSource Parse 為Document 物件.
     * 
     * @param in
     *            來源
     * @param encoding
     *            編碼
     * @return Document
     * @throws IOException
     */
    public static Document newDocument(InputSource in, String encoding) throws IOException {
        Document document = null;
        try {
            DocumentBuilderFactory dfactory = DocumentBuilderFactory.newInstance();
            dfactory.setNamespaceAware(true);
            // 2008-07-21 added:For Support Big5 encoding......................
            dfactory.setAttribute("http://apache.org/xml/features/allow-java-encodings", true);
            dfactory.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
            dfactory.setFeature("http://xml.org/sax/features/external-general-entities", false);
            dfactory.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
            DocumentBuilder domBuilder = dfactory.newDocumentBuilder();
            in.setEncoding(encoding);
            document = domBuilder.parse(in);
        } catch (SAXException e) {
            logger.debug("XmlTool", "In newDocument().Caught a SAXException .訊息=." + e.getMessage());
            logger.debug("XmlTool", e);
        } catch (ParserConfigurationException e) {
            logger.debug("XmlTool", "In newDocument().Caught a ParserConfigurationException .訊息=." + e.getMessage());
            logger.debug("XmlTool", e);
        }
        return document;
    }

    /**
     * 產生空的 Document 物件.
     */
    public static Document newDocument() {
        Document document = null;
        DocumentBuilderFactory docBuildFactory = DocumentBuilderFactory.newInstance();
        try {
            DocumentBuilder docBuilder = docBuildFactory.newDocumentBuilder();
            document = docBuilder.newDocument();
        } catch (ParserConfigurationException e) {
            logger.debug("XmlTool", "In newDocument().Caught a ParserConfigurationException e .訊息=." + e.getMessage());
            logger.debug("XmlTool", e);
        }
        return document;
    }

    /**
     * 開啟指定的XML檔
     */
    public static Document openDocument(String filename) {
        Document doc = null;
        try {
            doc = newDocument(new FileInputStream(filename));
        } catch (FileNotFoundException e) {
            logger.debug("XmlTool", "In openDocument(File file).Caught a FileNotFound Exception e .訊息=." + e.getMessage());
            logger.debug("XmlTool", e);
            return null;
        } catch (IOException e) {
            logger.debug("XmlTool", "In openDocument(File file).Caught a IOException e .訊息=." + e.getMessage());
            logger.debug("XmlTool", e);
            return null;
        }
        return doc;
    }

    /**
     * 關啟FILE物件所代表的XML檔.
     * 
     * 註:使用預設encoding:Big5
     * 
     * @param file
     * @return Document
     */
    public static Document openDocument(File file) {
        return openDocument(file, "UTF-8");
    }

    /**
     * 關啟FILE物件所代表的XML檔.
     * 
     * @param file
     * @return Document
     */
    public static Document openDocument(File file, String encoding) {
        Document doc = null;
        try {
            doc = newDocument(new FileInputStream(file), encoding);
        } catch (FileNotFoundException e) {
            logger.debug("XmlTool", "In openDocument(File file).Caught a FileNotFound Exception e .訊息=." + e.getMessage());
            logger.debug("XmlTool", e);
            return null;
        } catch (IOException e) {
            logger.debug("XmlTool", "In openDocument(File file).Caught a IOException e .訊息=." + e.getMessage());
            logger.debug("XmlTool", e);
            return null;
        }
        return doc;
    }

    /**
     * 將XML DOM物件儲存在指定的檔案.
     * 
     * @param fileName
     *            XML file name
     * @param doc
     *            XML document to save
     * @return <B>true</B> if method success <B>false</B> otherwise
     */
    public static boolean saveDocument(String fileName, Document doc) {

        OutputStream fos;
        Transformer transformer;

        try {
            fos = new FileOutputStream(fileName);
            // Use a Transformer for output
            TransformerFactory transformerFactory = TransformerFactory.newInstance();
            transformerFactory.setFeature(XMLConstants.FEATURE_SECURE_PROCESSING, true);
            transformerFactory.setAttribute(XMLConstants.ACCESS_EXTERNAL_DTD, "");
            transformerFactory.setAttribute(XMLConstants.ACCESS_EXTERNAL_STYLESHEET, "");
            transformer = transformerFactory.newTransformer();
            transformer.setOutputProperty("encoding", "UTF-8");
            transformer.setOutputProperty(OutputKeys.INDENT, "yes");
            transformer.setOutputProperty("{http://xml.apache.org/xalan}indent-amount", "2");
            DOMSource source = new DOMSource(doc);
            StreamResult result = new StreamResult(fos);
            // transform source into result will do save
            transformer.transform(source, result);
            fos.flush();
            fos.close();
        } catch (TransformerException e) {
            logger.debug("XmlTool", "In saveDocument().Caught a TransformerException e .訊息=." + e.getMessage());
            logger.debug("XmlTool", e);
            return false;
        } catch (FileNotFoundException e) {
            logger.debug("XmlTool", "In saveDocument().Caught a FileNotFoundException e .訊息=." + e.getMessage());
            logger.debug("XmlTool", e);
            return false;
        } catch (IOException e) {
            logger.debug("XmlTool", "In saveDocument().Caught a IOException e .訊息=." + e.getMessage());
            logger.debug("XmlTool", e);
            return false;
        }
        return true;
    }

    /**
     * 將XML 格式的字串 , 套用指定的XSL file.
     * 
     * @param sXML
     *            (String).格式為XML.
     * @param xslFileName
     *            (File). XSL 樣式檔.
     * @return String. 為套用XSL後所產生的結果.
     * @throws IOException
     */
    public static String transform(String sXML, File xslFileName) throws IOException {
        return transform(newDocument(sXML), xslFileName);
    }

    /**
     * 將Document , 套用指定的XSL file.
     * 
     * @param dom
     *            (org.w3s.dom.Document)
     * @param xslFileName
     *            (File). XSL 樣式檔.
     * @return String. 為套用XSL後所產生的結果.
     * @throws IOException
     * @since 2006.07.29...(reason:因WAS會轉不出來..可能是class loader順序問題)
     */
    public static String transform(Node dom, File xslFileName) throws IOException {
        Transformer transformer = null;
        StringWriter writer = new StringWriter();
        try {
            transformer = TransformerFactory.newInstance().newTransformer(new StreamSource(xslFileName));
            transformer.setOutputProperty(OutputKeys.OMIT_XML_DECLARATION, "yes");
            transformer.setOutputProperty(OutputKeys.ENCODING, "UTF-8");
            transformer.transform(new DOMSource(dom), new StreamResult(writer));
        } catch (TransformerException e) {
            logger.debug("XmlTool", "In transform().Caught a TransformerException .訊息=." + e.getMessage());
            logger.debug("XmlTool", e);
        }
        String result = writer.toString();
        writer.flush();
        return result;
    }

    /**
     * 取代XML 的保留字to中文大寫(ex:'&'->'＆' , '<' ->'＜' ...).
     *
     * @param text
     *            要檢查的字串
     * @return String
     */
    public static String replaceXMLReservedWord(String text) {
        return replaceXMLReservedWord(text, true);
    }

    /**
     * 取代XML 的保留字to中文大寫(ex:'&'->'＆' , '<' ->'＜' ...).
     *
     * history:<br/>
     * ======= 94/08/23 modify by Rex ============ 增加isTrim參數,是否要保留空白, 不影響原先的使用方式 ===========================================
     *
     * @param text
     *            要檢查的字串
     * @param isTrim
     *            是否要做trim動作
     * @return String
     */
    public static String replaceXMLReservedWord(String text, boolean isTrim) {
        if (text == null || text.length() == 0)
            return text;

        String ret = null;
        ret = text.replace('&', '＆');
        ret = text.replace('<', '＜');
        if (isTrim)
            ret = ret.replaceAll("", ""); // 0x0B
        return ret;
    }

    /**
     * 去除字串中的0x00,0xe字元.
     * 
     * @param text
     * @return 去除後的字串
     */
    public static String replaceSpecialChar(String text) {
        String ret = null;
        byte[] c = new byte[1];

        c[0] = 0x0;
        ret = text.replaceAll(new String(c), " ");
        c[0] = 0xe;
        ret = ret.replaceAll(new String(c), " ");

        return ret;
    }

    /**
     * 傳回下一個ELEMENT NODE
     * 
     * @param node
     * @return 找到的ELEMENT NODE，若無則回傳NULL
     */
    public static Element getNextElement(Node node) {
        Node nextNode = node;
        while ((nextNode = nextNode.getNextSibling()) != null) {
            if (nextNode.getNodeType() == Node.ELEMENT_NODE) {
                return (Element) nextNode;
            }
        }
        return null;
    }

    /**
     * 傳回DOM架構中parent node 中第一個NodeType 是Element Type的CHILD NODE
     * 
     * @param parentNode
     * @return 找到的第一個ELEMENT NODE 若無則回傳NULL
     */
    public static Element getFirstElement(Node parentNode) {
        Node childNode = parentNode.getFirstChild();
        if (childNode.getNodeType() == Node.ELEMENT_NODE) {
            return (Element) childNode;
        }
        return getNextElement(childNode);
    }

    /**
     * 取回所有符合XPATH所指定的NODE
     * 
     * @param xpath
     *            XPath 路徑
     * @param node
     * @return 以NODE ARRAY的方式回傳符合的NODE
     */
    public static Node[] getNodesArray(String xpath, Node node) {
        NodeList nodeList = getNodes(xpath, node);
        Node[] nodeArray = new Node[nodeList.getLength()];
        for (int i = 0; i < nodeArray.length; ++i) {
            nodeArray[i] = nodeList.item(i);
        }
        return nodeArray;
    }

    /**
     * 取得XPATH所指定的所有NODE
     * 
     * @param xpath
     *            XPath 路徑
     * @param node
     * @return 以XPathResult的方式回傳符合的NODE
     */
    public static NodeList getNodes(String xpath, Node node) {
        return selectNodes(node, xpath);
    }

    /**
     * 驗證Node是否符合所指定的XML Schema.
     * 
     * <br/>
     * <b>Note:</b> <br/>
     * 1.使用JAXP 1.3 feature.( xalan version 2.7) <br/>
     * 2.目前只用最簡單的單一 Schema檔驗證.
     * 
     * @param schemaFile
     *            XML Schema 檔
     * @param node
     *            要驗證的Node
     * @throws Exception
     * @since V1.2 2006.03.08
     */
    public static void validate(File schemaFile, Node node) throws Exception {
        validate(schemaFile, new DOMSource(node));
    }

    /**
     * 驗證XML檔,是否符合所指定的XML Schema.
     * 
     * <br/>
     * <b>Note:</b> <br/>
     * 1.使用JAXP 1.3 feature.( xalan version 2.7) <br/>
     * 2.目前只用最簡單的單一 Schema檔驗證.
     * 
     * @param schemaFile
     *            XML Schema 檔
     * @param source
     *            要驗證的XML檔
     * @throws Exception
     * @since V1.2 2006.03.08
     */
    public static void validate(File schemaFile, File source) throws Exception {
        validate(schemaFile, new StreamSource(source));
    }

    /**
     * 驗證XML檔,是否符合所指定的XML Schema.
     * 
     * @param schemaFile
     *            指定的XML Schema
     * @param source
     *            XML Source
     * @throws Exception
     */
    private static void validate(File schemaFile, Source source) throws Exception {
        SchemaFactory schemaFactory = SchemaFactory.newInstance(XMLConstants.W3C_XML_SCHEMA_NS_URI);
        try {
            // create JAXP transformation sources to specify
            // schema sources you want to use
            StreamSource po = new StreamSource(new FileInputStream(schemaFile));

            // build in-memory representation for po.xsd and ipo.xsd
            Schema schemas = schemaFactory.newSchema(new Source[] { po });

            // create a validator that will be able to validate
            Validator validator = schemas.newValidator();

            // configure this validator
            // validator.setErrorHandler(myErrorHandler);

            // validate the source
            validator.validate(source, null);
        } catch (IOException e) {
            logger.debug("XmlTool", e);
        } catch (SAXException e) {
            logger.debug("XmlTool", e);
            throw new Exception("驗證失敗! 錯誤原因:" + e.getMessage());
        }
    }

    /**
     * 驗證資料是否符合schemaReader中的定義.
     * 
     * @param schemaSources
     *            指定的XML Schema
     * @param docSource
     *            要驗證的資料
     * @throws Exception
     */
    private static void validate(Source[] schemaSources, Source docSource) throws Exception {
        SchemaFactory schemaFactory = SchemaFactory.newInstance(XMLConstants.W3C_XML_SCHEMA_NS_URI);
        try {
            Schema schemas = schemaFactory.newSchema(schemaSources);
            Validator validator = schemas.newValidator();
            validator.validate(docSource, null);
        } catch (IOException e) {
            logger.debug("XmlTool", e);
        } catch (SAXException e) {
            logger.debug("XmlTool", e);
            throw new Exception("驗證失敗！ 錯誤原因：" + e.getMessage());
        }
    }

    /**
     * 驗證Node的資料是否符合schemaReader中的定義.
     * 
     * @param schemaReader
     *            schema來源
     * @param node
     *            要驗證的資料
     * @throws Exception
     */
    public static void validate(Reader schemaReader, Node node) throws Exception {
        StreamSource po = new StreamSource(schemaReader);
        Source docSource = new DOMSource(node);
        validate(new Source[] { po }, docSource);
    }

    /**
     * 取得節點值.
     * 
     * @param node
     * @return Author:ingram Date:Jan 22, 2007
     */
    public static String getNodeValue(Node node) {
        Node cNode = node.getFirstChild();
        while (cNode != null) {
            if (cNode.getNodeType() == Element.TEXT_NODE) {
                return ((Text) cNode).getNodeValue();
            }
            cNode = cNode.getNextSibling();
        }
        cNode = null;
        return "";
    }

    private static final char[] IMMUNE_XPATH = { ',', '.', '-', '_', ' ', '/', '@' };
    private static final Map<Character, String> characterToEntityMap = mkCharacterToEntityMap();

    /**
     * 對傳入字串進行編碼
     * 
     * @param xpath
     *            XPath 路徑
     * @return
     */
    public static String encodeForXPath(String xpath) {
        if (xpath == null) {
            return null;
        }
        return encode(IMMUNE_XPATH, xpath);
    }

    /**
     * 對傳入字串進行編碼
     * 
     * @param immune
     *            檢核用字元陣列
     * @param input
     * @return
     */
    public static String encode(char[] immune, String input) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < input.length(); i++) {
            char c = input.charAt(i);
            sb.append(encodeCharacter(immune, Character.valueOf(c)));
        }
        return sb.toString();
    }

    /**
     * 對傳入字元進行編碼
     * 
     * @param immune
     *            檢核用字元陣列
     * @param c
     * @return
     */
    public static String encodeCharacter(char[] immune, Character c) {
        if (containsCharacter(c.charValue(), immune)) {
            return "" + c;
        }
        String hex = getHexForNonAlphanumeric(c.charValue());
        if (hex == null) {
            return "" + c;
        }
        if (((c.charValue() <= '\037') && (c.charValue() != '\t') && (c.charValue() != '\n') && (c.charValue() != '\r')) || ((c.charValue() >= '') && (c.charValue() <= ''))) {
            hex = "fffd";
            c = Character.valueOf((char) 65533);
        }
        String entityName = (String) characterToEntityMap.get(c);
        if (entityName != null) {
            return "&" + entityName + ";";
        }
        return "&#x" + hex + ";";
    }

    /**
     * 判斷字元c是否包含在字元陣列array中
     * 
     * @param c
     * @param array
     * @return
     */
    public static boolean containsCharacter(char c, char[] array) {
        for (char ch : array) {
            if (c == ch) {
                return true;
            }
        }
        return false;
    }

    private static final String[] hex = new String[256];

    static {
        for (char c = '\000'; c < 'ÿ'; c = (char) (c + '\001')) {
            if (((c >= '0') && (c <= '9')) || ((c >= 'A') && (c <= 'Z')) || ((c >= 'a') && (c <= 'z'))) {
                hex[c] = null;
            } else {
                hex[c] = toHex(c).intern();
            }
        }
    }

    /**
     * 取得非字母數字的十六進制
     * 
     * @param c
     * @return
     */
    public static String getHexForNonAlphanumeric(char c) {
        if (c < 'ÿ') {
            return hex[c];
        }
        return toHex(c);
    }

    /**
     * 十六進制
     * 
     * @param c
     * @return
     */
    public static String toHex(char c) {
        return Integer.toHexString(c);
    }

    /**
     * 特殊字元轉換用的map
     * 
     * @return
     */
    private static synchronized Map<Character, String> mkCharacterToEntityMap() {
        Map<Character, String> map = new HashMap<Character, String>(252);

        map.put(Character.valueOf('"'), "quot");
        map.put(Character.valueOf('&'), "amp");
        map.put(Character.valueOf('<'), "lt");
        map.put(Character.valueOf('>'), "gt");
        map.put(Character.valueOf(' '), "nbsp");
        map.put(Character.valueOf('¡'), "iexcl");
        map.put(Character.valueOf('¢'), "cent");
        map.put(Character.valueOf('£'), "pound");
        map.put(Character.valueOf('¤'), "curren");
        map.put(Character.valueOf('¥'), "yen");
        map.put(Character.valueOf('¦'), "brvbar");
        map.put(Character.valueOf('§'), "sect");
        map.put(Character.valueOf('¨'), "uml");
        map.put(Character.valueOf('©'), "copy");
        map.put(Character.valueOf('ª'), "ordf");
        map.put(Character.valueOf('«'), "laquo");
        map.put(Character.valueOf('¬'), "not");
        map.put(Character.valueOf('­'), "shy");
        map.put(Character.valueOf('®'), "reg");
        map.put(Character.valueOf('¯'), "macr");
        map.put(Character.valueOf('°'), "deg");
        map.put(Character.valueOf('±'), "plusmn");
        map.put(Character.valueOf('²'), "sup2");
        map.put(Character.valueOf('³'), "sup3");
        map.put(Character.valueOf('´'), "acute");
        map.put(Character.valueOf('µ'), "micro");
        map.put(Character.valueOf('¶'), "para");
        map.put(Character.valueOf('·'), "middot");
        map.put(Character.valueOf('¸'), "cedil");
        map.put(Character.valueOf('¹'), "sup1");
        map.put(Character.valueOf('º'), "ordm");
        map.put(Character.valueOf('»'), "raquo");
        map.put(Character.valueOf('¼'), "frac14");
        map.put(Character.valueOf('½'), "frac12");
        map.put(Character.valueOf('¾'), "frac34");
        map.put(Character.valueOf('¿'), "iquest");
        map.put(Character.valueOf('À'), "Agrave");
        map.put(Character.valueOf('Á'), "Aacute");
        map.put(Character.valueOf('Â'), "Acirc");
        map.put(Character.valueOf('Ã'), "Atilde");
        map.put(Character.valueOf('Ä'), "Auml");
        map.put(Character.valueOf('Å'), "Aring");
        map.put(Character.valueOf('Æ'), "AElig");
        map.put(Character.valueOf('Ç'), "Ccedil");
        map.put(Character.valueOf('È'), "Egrave");
        map.put(Character.valueOf('É'), "Eacute");
        map.put(Character.valueOf('Ê'), "Ecirc");
        map.put(Character.valueOf('Ë'), "Euml");
        map.put(Character.valueOf('Ì'), "Igrave");
        map.put(Character.valueOf('Í'), "Iacute");
        map.put(Character.valueOf('Î'), "Icirc");
        map.put(Character.valueOf('Ï'), "Iuml");
        map.put(Character.valueOf('Ð'), "ETH");
        map.put(Character.valueOf('Ñ'), "Ntilde");
        map.put(Character.valueOf('Ò'), "Ograve");
        map.put(Character.valueOf('Ó'), "Oacute");
        map.put(Character.valueOf('Ô'), "Ocirc");
        map.put(Character.valueOf('Õ'), "Otilde");
        map.put(Character.valueOf('Ö'), "Ouml");
        map.put(Character.valueOf('×'), "times");
        map.put(Character.valueOf('Ø'), "Oslash");
        map.put(Character.valueOf('Ù'), "Ugrave");
        map.put(Character.valueOf('Ú'), "Uacute");
        map.put(Character.valueOf('Û'), "Ucirc");
        map.put(Character.valueOf('Ü'), "Uuml");
        map.put(Character.valueOf('Ý'), "Yacute");
        map.put(Character.valueOf('Þ'), "THORN");
        map.put(Character.valueOf('ß'), "szlig");
        map.put(Character.valueOf('à'), "agrave");
        map.put(Character.valueOf('á'), "aacute");
        map.put(Character.valueOf('â'), "acirc");
        map.put(Character.valueOf('ã'), "atilde");
        map.put(Character.valueOf('ä'), "auml");
        map.put(Character.valueOf('å'), "aring");
        map.put(Character.valueOf('æ'), "aelig");
        map.put(Character.valueOf('ç'), "ccedil");
        map.put(Character.valueOf('è'), "egrave");
        map.put(Character.valueOf('é'), "eacute");
        map.put(Character.valueOf('ê'), "ecirc");
        map.put(Character.valueOf('ë'), "euml");
        map.put(Character.valueOf('ì'), "igrave");
        map.put(Character.valueOf('í'), "iacute");
        map.put(Character.valueOf('î'), "icirc");
        map.put(Character.valueOf('ï'), "iuml");
        map.put(Character.valueOf('ð'), "eth");
        map.put(Character.valueOf('ñ'), "ntilde");
        map.put(Character.valueOf('ò'), "ograve");
        map.put(Character.valueOf('ó'), "oacute");
        map.put(Character.valueOf('ô'), "ocirc");
        map.put(Character.valueOf('õ'), "otilde");
        map.put(Character.valueOf('ö'), "ouml");
        map.put(Character.valueOf('÷'), "divide");
        map.put(Character.valueOf('ø'), "oslash");
        map.put(Character.valueOf('ù'), "ugrave");
        map.put(Character.valueOf('ú'), "uacute");
        map.put(Character.valueOf('û'), "ucirc");
        map.put(Character.valueOf('ü'), "uuml");
        map.put(Character.valueOf('ý'), "yacute");
        map.put(Character.valueOf('þ'), "thorn");
        map.put(Character.valueOf('ÿ'), "yuml");
        map.put(Character.valueOf('Œ'), "OElig");
        map.put(Character.valueOf('œ'), "oelig");
        map.put(Character.valueOf('Š'), "Scaron");
        map.put(Character.valueOf('š'), "scaron");
        map.put(Character.valueOf('Ÿ'), "Yuml");
        map.put(Character.valueOf('ƒ'), "fnof");
        map.put(Character.valueOf('ˆ'), "circ");
        map.put(Character.valueOf('˜'), "tilde");
        map.put(Character.valueOf('Α'), "Alpha");
        map.put(Character.valueOf('Β'), "Beta");
        map.put(Character.valueOf('Γ'), "Gamma");
        map.put(Character.valueOf('Δ'), "Delta");
        map.put(Character.valueOf('Ε'), "Epsilon");
        map.put(Character.valueOf('Ζ'), "Zeta");
        map.put(Character.valueOf('Η'), "Eta");
        map.put(Character.valueOf('Θ'), "Theta");
        map.put(Character.valueOf('Ι'), "Iota");
        map.put(Character.valueOf('Κ'), "Kappa");
        map.put(Character.valueOf('Λ'), "Lambda");
        map.put(Character.valueOf('Μ'), "Mu");
        map.put(Character.valueOf('Ν'), "Nu");
        map.put(Character.valueOf('Ξ'), "Xi");
        map.put(Character.valueOf('Ο'), "Omicron");
        map.put(Character.valueOf('Π'), "Pi");
        map.put(Character.valueOf('Ρ'), "Rho");
        map.put(Character.valueOf('Σ'), "Sigma");
        map.put(Character.valueOf('Τ'), "Tau");
        map.put(Character.valueOf('Υ'), "Upsilon");
        map.put(Character.valueOf('Φ'), "Phi");
        map.put(Character.valueOf('Χ'), "Chi");
        map.put(Character.valueOf('Ψ'), "Psi");
        map.put(Character.valueOf('Ω'), "Omega");
        map.put(Character.valueOf('α'), "alpha");
        map.put(Character.valueOf('β'), "beta");
        map.put(Character.valueOf('γ'), "gamma");
        map.put(Character.valueOf('δ'), "delta");
        map.put(Character.valueOf('ε'), "epsilon");
        map.put(Character.valueOf('ζ'), "zeta");
        map.put(Character.valueOf('η'), "eta");
        map.put(Character.valueOf('θ'), "theta");
        map.put(Character.valueOf('ι'), "iota");
        map.put(Character.valueOf('κ'), "kappa");
        map.put(Character.valueOf('λ'), "lambda");
        map.put(Character.valueOf('μ'), "mu");
        map.put(Character.valueOf('ν'), "nu");
        map.put(Character.valueOf('ξ'), "xi");
        map.put(Character.valueOf('ο'), "omicron");
        map.put(Character.valueOf('π'), "pi");
        map.put(Character.valueOf('ρ'), "rho");
        map.put(Character.valueOf('ς'), "sigmaf");
        map.put(Character.valueOf('σ'), "sigma");
        map.put(Character.valueOf('τ'), "tau");
        map.put(Character.valueOf('υ'), "upsilon");
        map.put(Character.valueOf('φ'), "phi");
        map.put(Character.valueOf('χ'), "chi");
        map.put(Character.valueOf('ψ'), "psi");
        map.put(Character.valueOf('ω'), "omega");
        map.put(Character.valueOf('ϑ'), "thetasym");
        map.put(Character.valueOf('ϒ'), "upsih");
        map.put(Character.valueOf('ϖ'), "piv");
        map.put(Character.valueOf(' '), "ensp");
        map.put(Character.valueOf(' '), "emsp");
        map.put(Character.valueOf(' '), "thinsp");
        map.put(Character.valueOf('‌'), "zwnj");
        map.put(Character.valueOf('‍'), "zwj");
        map.put(Character.valueOf('‎'), "lrm");
        map.put(Character.valueOf('‏'), "rlm");
        map.put(Character.valueOf('–'), "ndash");
        map.put(Character.valueOf('—'), "mdash");
        map.put(Character.valueOf('‘'), "lsquo");
        map.put(Character.valueOf('’'), "rsquo");
        map.put(Character.valueOf('‚'), "sbquo");
        map.put(Character.valueOf('“'), "ldquo");
        map.put(Character.valueOf('”'), "rdquo");
        map.put(Character.valueOf('„'), "bdquo");
        map.put(Character.valueOf('†'), "dagger");
        map.put(Character.valueOf('‡'), "Dagger");
        map.put(Character.valueOf('•'), "bull");
        map.put(Character.valueOf('…'), "hellip");
        map.put(Character.valueOf('‰'), "permil");
        map.put(Character.valueOf('′'), "prime");
        map.put(Character.valueOf('″'), "Prime");
        map.put(Character.valueOf('‹'), "lsaquo");
        map.put(Character.valueOf('›'), "rsaquo");
        map.put(Character.valueOf('‾'), "oline");
        map.put(Character.valueOf('⁄'), "frasl");
        map.put(Character.valueOf('€'), "euro");
        map.put(Character.valueOf('ℑ'), "image");
        map.put(Character.valueOf('℘'), "weierp");
        map.put(Character.valueOf('ℜ'), "real");
        map.put(Character.valueOf('™'), "trade");
        map.put(Character.valueOf('ℵ'), "alefsym");
        map.put(Character.valueOf('←'), "larr");
        map.put(Character.valueOf('↑'), "uarr");
        map.put(Character.valueOf('→'), "rarr");
        map.put(Character.valueOf('↓'), "darr");
        map.put(Character.valueOf('↔'), "harr");
        map.put(Character.valueOf('↵'), "crarr");
        map.put(Character.valueOf('⇐'), "lArr");
        map.put(Character.valueOf('⇑'), "uArr");
        map.put(Character.valueOf('⇒'), "rArr");
        map.put(Character.valueOf('⇓'), "dArr");
        map.put(Character.valueOf('⇔'), "hArr");
        map.put(Character.valueOf('∀'), "forall");
        map.put(Character.valueOf('∂'), "part");
        map.put(Character.valueOf('∃'), "exist");
        map.put(Character.valueOf('∅'), "empty");
        map.put(Character.valueOf('∇'), "nabla");
        map.put(Character.valueOf('∈'), "isin");
        map.put(Character.valueOf('∉'), "notin");
        map.put(Character.valueOf('∋'), "ni");
        map.put(Character.valueOf('∏'), "prod");
        map.put(Character.valueOf('∑'), "sum");
        map.put(Character.valueOf('−'), "minus");
        map.put(Character.valueOf('∗'), "lowast");
        map.put(Character.valueOf('√'), "radic");
        map.put(Character.valueOf('∝'), "prop");
        map.put(Character.valueOf('∞'), "infin");
        map.put(Character.valueOf('∠'), "ang");
        map.put(Character.valueOf('∧'), "and");
        map.put(Character.valueOf('∨'), "or");
        map.put(Character.valueOf('∩'), "cap");
        map.put(Character.valueOf('∪'), "cup");
        map.put(Character.valueOf('∫'), "int");
        map.put(Character.valueOf('∴'), "there4");
        map.put(Character.valueOf('∼'), "sim");
        map.put(Character.valueOf('≅'), "cong");
        map.put(Character.valueOf('≈'), "asymp");
        map.put(Character.valueOf('≠'), "ne");
        map.put(Character.valueOf('≡'), "equiv");
        map.put(Character.valueOf('≤'), "le");
        map.put(Character.valueOf('≥'), "ge");
        map.put(Character.valueOf('⊂'), "sub");
        map.put(Character.valueOf('⊃'), "sup");
        map.put(Character.valueOf('⊄'), "nsub");
        map.put(Character.valueOf('⊆'), "sube");
        map.put(Character.valueOf('⊇'), "supe");
        map.put(Character.valueOf('⊕'), "oplus");
        map.put(Character.valueOf('⊗'), "otimes");
        map.put(Character.valueOf('⊥'), "perp");
        map.put(Character.valueOf('⋅'), "sdot");
        map.put(Character.valueOf('⌈'), "lceil");
        map.put(Character.valueOf('⌉'), "rceil");
        map.put(Character.valueOf('⌊'), "lfloor");
        map.put(Character.valueOf('⌋'), "rfloor");
        map.put(Character.valueOf('〈'), "lang");
        map.put(Character.valueOf('〉'), "rang");
        map.put(Character.valueOf('◊'), "loz");
        map.put(Character.valueOf('♠'), "spades");
        map.put(Character.valueOf('♣'), "clubs");
        map.put(Character.valueOf('♥'), "hearts");
        map.put(Character.valueOf('♦'), "diams");

        return Collections.unmodifiableMap(map);
    }
}
