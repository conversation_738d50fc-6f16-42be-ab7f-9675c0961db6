package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 地政士黑名單 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C900M01H", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId"}))
public class C900M01H extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * 證書-年<p/>
	 * 3碼，前不補0
	 */
	@Size(max=3)
	@Column(name="AGENTCERTYEAR", length=3, columnDefinition="VARCHAR(3)")
	private String agentCertYear;

	/** 
	 * 證書-字號<p/>
	 * 預設出現「台內地登」，經辦可自行修改
	 */
	@Size(max=15)
	@Column(name="AGENTCERTWORD", length=15, columnDefinition="VARCHAR(15)")
	private String agentCertWord;

	/** 
	 * 證書-流水號<p/>
	 * 前補0
	 */
	@Size(max=6)
	@Column(name="AGENTCERTNO", length=6, columnDefinition="CHAR(6)")
	private String agentCertNo;

	/** 
	 * 控制狀態［Block:1,4］［Prompt:2,3］(ref C900M01J) <br/>
	 * select codetype, codeValue, codeDesc from com.bcodetype where locale='zh_TW' and codetype in('cls260CtlFlagType', 'lnFlag_extend_C250M01A_C900M01H') order by codetype, codeOrder   <br/>
	 *  1.經稽核處查核確定或疑似人頭戶案件<br/>
	 *  2.經分行查詢實價登錄價格與買賣契約價格不符之案件<br/>
	 *  3.經分行發現疑似製作假文件之案件<br/>
	 *  4.同業發生人頭戶房貸案經媒體揭露或受金管會裁罰案例<br/>
	 *  <br/>
	 *  註1.同一個地政士，可能Ａ分行登錄的ctlFlag=2，後來B分行登錄的ctlFlag=1<br/>
	 *  註2.只有授管處918，可登錄ctlFlag=4
	 */
	@Size(max=1)
	@Column(name="CTLFLAG", length=1, columnDefinition="CHAR(1)")
	private String ctlFlag;

	/** 備註 **/
	@Size(max=300)
	@Column(name="MEMO", length=300, columnDefinition="VARCHAR(300)")
	private String memo;

	/** 申請解除人員號碼 **/
	@Size(max=6)
	@Column(name="DCUPDATER", length=6, columnDefinition="CHAR(6)")
	private String dcUpdater;

	/** 申請解除日期 **/
	@Column(name="DCUPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp dcUpdateTime;

	/** 核准解除人員號碼 **/
	@Size(max=6)
	@Column(name="DCAPPROVER", length=6, columnDefinition="CHAR(6)")
	private String dcApprover;

	/** 核准解除日期 **/
	@Column(name="DCAPPROVETIME", columnDefinition="TIMESTAMP")
	private Timestamp dcApproveTime;
	
	/** 核准解除日期 **/
	@Column(name="ESTATEAGENTFLAG", columnDefinition="VARCHAR(1)")
	private String estateAgentFlag;
	
	/** 核准解除日期 **/
	@Column(name="RECORD17FLAG", columnDefinition="VARCHAR(1)")
	private String record17Flag;

	/** 
	 * 取得證書-年<p/>
	 * 3碼，前不補0
	 */
	public String getAgentCertYear() {
		return this.agentCertYear;
	}
	/**
	 *  設定證書-年<p/>
	 *  3碼，前不補0
	 **/
	public void setAgentCertYear(String value) {
		this.agentCertYear = value;
	}

	/** 
	 * 取得證書-字號<p/>
	 * 預設出現「台內地登」，經辦可自行修改
	 */
	public String getAgentCertWord() {
		return this.agentCertWord;
	}
	/**
	 *  設定證書-字號<p/>
	 *  預設出現「台內地登」，經辦可自行修改
	 **/
	public void setAgentCertWord(String value) {
		this.agentCertWord = value;
	}

	/** 
	 * 取得證書-流水號<p/>
	 * 前補0
	 */
	public String getAgentCertNo() {
		return this.agentCertNo;
	}
	/**
	 *  設定證書-流水號<p/>
	 *  前補0
	 **/
	public void setAgentCertNo(String value) {
		this.agentCertNo = value;
	}

	/** 取得控制狀態［Block:1,4］［Prompt:2,3］ */
	public String getCtlFlag() {
		return this.ctlFlag;
	}
	/** 設定控制狀態［Block:1,4］［Prompt:2,3］  */
	public void setCtlFlag(String value) {
		this.ctlFlag = value;
	}

	/** 取得備註 **/
	public String getMemo() {
		return this.memo;
	}
	/** 設定備註 **/
	public void setMemo(String value) {
		this.memo = value;
	}

	/** 取得申請解除人員號碼 **/
	public String getDcUpdater() {
		return this.dcUpdater;
	}
	/** 設定申請解除人員號碼 **/
	public void setDcUpdater(String value) {
		this.dcUpdater = value;
	}

	/** 取得申請解除日期 **/
	public Timestamp getDcUpdateTime() {
		return this.dcUpdateTime;
	}
	/** 設定申請解除日期 **/
	public void setDcUpdateTime(Timestamp value) {
		this.dcUpdateTime = value;
	}

	/** 取得核准解除人員號碼 **/
	public String getDcApprover() {
		return this.dcApprover;
	}
	/** 設定核准解除人員號碼 **/
	public void setDcApprover(String value) {
		this.dcApprover = value;
	}

	/** 取得核准解除日期 **/
	public Timestamp getDcApproveTime() {
		return this.dcApproveTime;
	}
	/** 設定核准解除日期 **/
	public void setDcApproveTime(Timestamp value) {
		this.dcApproveTime = value;
	}
	
	/** 取得永慶房屋直營店或信義房屋名義仲介成交案件註記 **/
	public String getEstateAgentFlag() {
		return this.estateAgentFlag;
	}
	/** 設定永慶房屋直營店或信義房屋名義仲介成交案件註記 **/
	public void setEstateAgentFlag(String value) {
		this.estateAgentFlag = value;
	}
	
	/** 取得懲戒紀錄是否屬地政士法第17條註記 **/
	public String getRecord17Flag() {
		return this.record17Flag;
	}
	/** 設定懲戒紀錄是否屬地政士法第17條註記 **/
	public void setRecord17Flag(String value) {
		this.record17Flag = value;
	}

	/**  顯示用欄位*/
	@Transient
	private String agentCert;

	public String isAgentCert() {
		return agentCert;
	}
	public void setAgentCert(String agentCert) {
		this.agentCert = agentCert;
	}	
}
