/* 
 * L120M01B.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 簽報書額度種類資料檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L120M01B", uniqueConstraints = @UniqueConstraint(columnNames = { "mainId" }))
public class L120M01B extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 本案是否有同業聯貸案額度
	 * <p/>
	 * Y/N（是/否）
	 */
	@Column(name = "UNITCASE", length = 1, columnDefinition = "VARCHAR(1)")
	private String unitCase;

	/**
	 * 本分行是否為管理行
	 * <p/>
	 * Y/N（是/否）
	 */
	@Column(name = "UCMAINBRANCH", length = 1, columnDefinition = "VARCHAR(1)")
	private String uCMainBranch;

	/**
	 * 本案是否有同行(本行)聯貸案額度行
	 * <p/>
	 * Y/N（是/否）
	 */
	@Column(name = "UNITMEGA", length = 1, columnDefinition = "VARCHAR(1)")
	private String unitMega;

	/**
	 * 本分行是否為額度管理行
	 * <p/>
	 * Y/N（是/否）
	 */
	@Column(name = "UCNTBRANCH", length = 1, columnDefinition = "VARCHAR(1)")
	private String uCntBranch;

	/**
	 * 本分行是否為擔保品管理行
	 * <p/>
	 * Y/N（是/否）
	 */
	@Column(name = "UCMSBRANCH", length = 1, columnDefinition = "VARCHAR(1)")
	private String uCMSBranch;

	/**
	 * 本案是否為隱名參貸
	 * <p/>
	 * Y/N（是/否）
	 */
	@Column(name = "UHIDENAME", length = 1, columnDefinition = "VARCHAR(1)")
	private String uHideName;

	/**
	 * 本案為國內聯貸/國際聯貸
	 * <p/>
	 * 國內聯貸 | A<br/>
	 * 國際聯貸 | B
	 */
	@Column(name = "UAREA", length = 1, columnDefinition = "VARCHAR(1)")
	private String uArea;

	/**
	 * 報核方式（國金部報核、分行報核）
	 * <p/>
	 * 國金部報核 | A<br/>
	 * 分行報核 | B
	 */
	@Column(name = "URP1", length = 1, columnDefinition = "VARCHAR(1)")
	private String uRP1;

	/**
	 * 報核方式（國金部對外、分行對外）
	 * <p/>
	 * 國金部對外| A<br/>
	 * 分行對外 | B
	 */
	@Column(name = "URP2", length = 1, columnDefinition = "VARCHAR(1)")
	private String uRP2;

	/**
	 * 報核方式（國金部掛帳、分行掛帳）
	 * <p/>
	 * 國金部掛帳 | A<br/>
	 * 分行掛帳 | B
	 */
	@Column(name = "URP3", length = 1, columnDefinition = "VARCHAR(1)")
	private String uRP3;

	/**
	 * 合作業務種類
	 * <p/>
	 * 非合作業務 | 0<br/>
	 * 價金履約保證 | 1<br/>
	 * 合作外匯 | 2<br/>
	 * 其他合作業務 | Z
	 */
	@Column(name = "COKIND", length = 1, columnDefinition = "VARCHAR(1)")
	private String coKind;

	/**
	 * 其他合作業務母戶
	 * <p/>
	 * Y/N（是/否）
	 */
	@Column(name = "MCNTRT", length = 1, columnDefinition = "VARCHAR(1)")
	private String mCntrt;

	/**
	 * 其他合作業務子戶
	 * <p/>
	 * Y/N（是/否）
	 */
	@Column(name = "SCNTRT", length = 1, columnDefinition = "VARCHAR(1)")
	private String sCntrt;

	/** 其他合作業務母戶之額度序號 **/
	@Column(name = "MSCNTRT", length = 12, columnDefinition = "VARCHAR(12)")
	private String mScntrt;

	/** 其他合作業務子戶之代收帳號 **/
	@Column(name = "MSACC", length = 14, columnDefinition = "VARCHAR(14)")
	private String mSAcc;

	/**
	 * 輸入資料檢誤完成(Y/N)
	 * <p/>
	 * 100/12/05新增<br/>
	 * Y/N<br/>
	 * 預先檢核資料是否已登錄完整，供執行【呈主管覆核】時，可快速檢核資料是否完備用。
	 */
	@Column(name = "CHKYN", length = 1, columnDefinition = "CHAR(1)")
	private String chkYN;

	/** 建立人員號碼 **/
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Date updateTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得本案是否有同業聯貸案額度
	 * <p/>
	 * Y/N（是/否）
	 */
	public String getUnitCase() {
		return this.unitCase;
	}

	/**
	 * 設定本案是否有同業聯貸案額度
	 * <p/>
	 * Y/N（是/否）
	 **/
	public void setUnitCase(String value) {
		this.unitCase = value;
	}

	/**
	 * 取得本分行是否為管理行
	 * <p/>
	 * Y/N（是/否）
	 */
	public String getUCMainBranch() {
		return this.uCMainBranch;
	}

	/**
	 * 設定本分行是否為管理行
	 * <p/>
	 * Y/N（是/否）
	 **/
	public void setUCMainBranch(String value) {
		this.uCMainBranch = value;
	}

	/**
	 * 取得本案是否有同行(本行)聯貸案額度行
	 * <p/>
	 * Y/N（是/否）
	 */
	public String getUnitMega() {
		return this.unitMega;
	}

	/**
	 * 設定本案是否有同行(本行)聯貸案額度行
	 * <p/>
	 * Y/N（是/否）
	 **/
	public void setUnitMega(String value) {
		this.unitMega = value;
	}

	/**
	 * 取得本分行是否為額度管理行
	 * <p/>
	 * Y/N（是/否）
	 */
	public String getUCntBranch() {
		return this.uCntBranch;
	}

	/**
	 * 設定本分行是否為額度管理行
	 * <p/>
	 * Y/N（是/否）
	 **/
	public void setUCntBranch(String value) {
		this.uCntBranch = value;
	}

	/**
	 * 取得本分行是否為擔保品管理行
	 * <p/>
	 * Y/N（是/否）
	 */
	public String getUCMSBranch() {
		return this.uCMSBranch;
	}

	/**
	 * 設定本分行是否為擔保品管理行
	 * <p/>
	 * Y/N（是/否）
	 **/
	public void setUCMSBranch(String value) {
		this.uCMSBranch = value;
	}

	/**
	 * 取得本案是否為隱名參貸
	 * <p/>
	 * Y/N（是/否）
	 */
	public String getUHideName() {
		return this.uHideName;
	}

	/**
	 * 設定本案是否為隱名參貸
	 * <p/>
	 * Y/N（是/否）
	 **/
	public void setUHideName(String value) {
		this.uHideName = value;
	}

	/**
	 * 取得本案為國內聯貸/國際聯貸
	 * <p/>
	 * 國內聯貸 | A<br/>
	 * 國際聯貸 | B
	 */
	public String getUArea() {
		return this.uArea;
	}

	/**
	 * 設定本案為國內聯貸/國際聯貸
	 * <p/>
	 * 國內聯貸 | A<br/>
	 * 國際聯貸 | B
	 **/
	public void setUArea(String value) {
		this.uArea = value;
	}

	/**
	 * 取得報核方式（國金部報核、分行報核）
	 * <p/>
	 * 國金部報核 | A<br/>
	 * 分行報核 | B
	 */
	public String getURP1() {
		return this.uRP1;
	}

	/**
	 * 設定報核方式（國金部報核、分行報核）
	 * <p/>
	 * 國金部報核 | A<br/>
	 * 分行報核 | B
	 **/
	public void setURP1(String value) {
		this.uRP1 = value;
	}

	/**
	 * 取得報核方式（國金部對外、分行對外）
	 * <p/>
	 * 國金部對外| A<br/>
	 * 分行對外 | B
	 */
	public String getURP2() {
		return this.uRP2;
	}

	/**
	 * 設定報核方式（國金部對外、分行對外）
	 * <p/>
	 * 國金部對外| A<br/>
	 * 分行對外 | B
	 **/
	public void setURP2(String value) {
		this.uRP2 = value;
	}

	/**
	 * 取得報核方式（國金部掛帳、分行掛帳）
	 * <p/>
	 * 國金部掛帳 | A<br/>
	 * 分行掛帳 | B
	 */
	public String getURP3() {
		return this.uRP3;
	}

	/**
	 * 設定報核方式（國金部掛帳、分行掛帳）
	 * <p/>
	 * 國金部掛帳 | A<br/>
	 * 分行掛帳 | B
	 **/
	public void setURP3(String value) {
		this.uRP3 = value;
	}

	/**
	 * 取得合作業務種類
	 * <p/>
	 * 非合作業務 | 0<br/>
	 * 價金履約保證 | 1<br/>
	 * 合作外匯 | 2<br/>
	 * 其他合作業務 | Z
	 */
	public String getCoKind() {
		return this.coKind;
	}

	/**
	 * 設定合作業務種類
	 * <p/>
	 * 非合作業務 | 0<br/>
	 * 價金履約保證 | 1<br/>
	 * 合作外匯 | 2<br/>
	 * 其他合作業務 | Z
	 **/
	public void setCoKind(String value) {
		this.coKind = value;
	}

	/**
	 * 取得其他合作業務母戶
	 * <p/>
	 * Y/N（是/否）
	 */
	public String getMCntrt() {
		return this.mCntrt;
	}

	/**
	 * 設定其他合作業務母戶
	 * <p/>
	 * Y/N（是/否）
	 **/
	public void setMCntrt(String value) {
		this.mCntrt = value;
	}

	/**
	 * 取得其他合作業務子戶
	 * <p/>
	 * Y/N（是/否）
	 */
	public String getSCntrt() {
		return this.sCntrt;
	}

	/**
	 * 設定其他合作業務子戶
	 * <p/>
	 * Y/N（是/否）
	 **/
	public void setSCntrt(String value) {
		this.sCntrt = value;
	}

	/** 取得其他合作業務母戶之額度序號 **/
	public String getMScntrt() {
		return this.mScntrt;
	}

	/** 設定其他合作業務母戶之額度序號 **/
	public void setMScntrt(String value) {
		this.mScntrt = value;
	}

	/** 取得其他合作業務子戶之代收帳號 **/
	public String getMSAcc() {
		return this.mSAcc;
	}

	/** 設定其他合作業務子戶之代收帳號 **/
	public void setMSAcc(String value) {
		this.mSAcc = value;
	}

	/**
	 * 取得輸入資料檢誤完成(Y/N)
	 * <p/>
	 * 100/12/05新增<br/>
	 * Y/N<br/>
	 * 預先檢核資料是否已登錄完整，供執行【呈主管覆核】時，可快速檢核資料是否完備用。
	 */
	public String getChkYN() {
		return this.chkYN;
	}

	/**
	 * 設定輸入資料檢誤完成(Y/N)
	 * <p/>
	 * 100/12/05新增<br/>
	 * Y/N<br/>
	 * 預先檢核資料是否已登錄完整，供執行【呈主管覆核】時，可快速檢核資料是否完備用。
	 **/
	public void setChkYN(String value) {
		this.chkYN = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}
}
