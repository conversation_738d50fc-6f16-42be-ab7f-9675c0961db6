<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="ctrs22panel">
			<form id="formTab06a">
	            <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
	                <tbody>
	                    <tr>
	                        <td width="35%" class="hd1">
	                          <th:block th:text="#{'C999M01AM07.title06'}"><!--  償還辦法--></th:block>&nbsp;&nbsp;
	                        </td>
	                        <td width="65%">
						              <table width="99%">
						                <tr>
						                  <td><input type="radio" name="_22Ara" value="1" />（一）</td>
						                  <td>
						                    自實際借用日起，按月付息一次，到期還清本金。
						                  </td>
						                </tr>
						                <tr>
						                  <td><input type="radio" name="_22Ara" value="2" />（二）</td>
						                  <td>
						                    自實際借用日起，按年金法，按月攤付本息。
						                  </td>
						                </tr>
						                <tr>
						                  <td><input type="radio" name="_22Ara" value="3" />（三）</td>
						                  <td>
						                  	<!-- E1 ~ E2-->
						                    自實際借用日起，前<input type="text" class="color-red max numText" id="_jsonDataE1" name="_jsonDataE1" size="3" maxlength="3" />年（個月）按月付息，自第<input type="text" class="color-red max numText" id="_jsonDataE2" name="_jsonDataE2" size="3" maxlength="3" />年(個月)起，再按月攤付本息。
						                  </td>
						                </tr>
						                <tr>
						                  <td><input type="radio" name="_22Ara" value="4" />（四）</td>
						                  <td>
						                  	<!-- E3 ~ E4-->
						                    自實際借用日起，以每十四天為一期，分<input type="text" class="color-red numText" id="_jsonDataE3" name="_jsonDataE3" />期償還。(每期償還之本息金額為：以<input type="text" class="color-red max numText" id="_jsonDataE4" name="_jsonDataE4" size="3" maxlength="3" />年為期，依年金法按月攤還本息計算所得每月應攤付金額之二分之一)。
						                  </td>
						                </tr>
						                <tr>
						                  <td><input type="checkbox" id="_22Aca" name="_22Aca" value="5" onclick="hideOrShow('#other22A')"/>（五）</td>
						                  <td>
						                    （由甲方與乙方個別約定）：<br />
						                    	<span id="other22A" name="other22A" class="hide"><textarea class="color-red" id="_jsonDataE" name="_jsonDataE" style="width: 450px; height: 83px"></textarea></span>
						                  </td>
						                </tr>
						              </table>                        	
							  <!--<textarea id="jsonDataE" name="jsonDataE" class="txt_mult" maxlengthC="256" cols="90" rows="10" readonly="readonly"
											style="line-height: 20px;"></textarea>-->
	                        </td>
	                    </tr>
	                </tbody>
	            </table>
			</form>
		</th:block>
    </body>
</html>
