package com.mega.eloan.lms.mfaloan.bean;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 天然及重大災害住宅補貼控制檔 **/
public class ELF508 extends GenericBean{

	private static final long serialVersionUID = 1L;

	/** 分行別 **/
	@Size(max=3)
	@Column(name="ELF508_BRNO", length=3, columnDefinition="CHAR(03)")
	private String elf508_brno;

	/** 借款戶ID **/
	@Size(max=11)
	@Column(name="ELF508_CUST_ID", length=11, columnDefinition="CHAR(11)",unique = true)
	private String elf508_cust_id;

	/** 
	 * 專案融資種類<p/>
	 * (01:0206震災)
	 */
	@Size(max=2)
	@Column(name="ELF508_DISAS_TYPE", length=2, columnDefinition="CHAR(02)",unique = true)
	private String elf508_disas_type;

	/** 毀損住宅所有權人戶籍號碼 **/
	@Size(max=8)
	@Column(name="ELF508_HOLD_NO", length=8, columnDefinition="CHAR(08)",unique = true)
	private String elf508_hold_no;

	/** 毀損住宅所有權人身分證字號 **/
	@Size(max=11)
	@Column(name="ELF508_OWNER_ID", length=11, columnDefinition="CHAR(11)",unique = true)
	private String elf508_owner_id;

	/** 額度序號 **/
	@Size(max=12)
	@Column(name="ELF508_CNTRNO", length=12, columnDefinition="CHAR(12)")
	private String elf508_cntrno;

	/** 
	 * 貸款種類<p/>
	 * 1：購屋 2：重建 3：修繕
	 */
	@Size(max=1)
	@Column(name="ELF508_LOAN_TYPE", length=1, columnDefinition="CHAR(1)")
	private String elf508_loan_type;

	/** 
	 * 申請日期 
	 * 借款人填寫貸款申請書之日期
	 * **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELF508_APP_DATE", columnDefinition="DATE")
	private Date elf508_app_date;

	/** 申請金額 **/
	@Digits(integer=15, fraction=2)
	@Column(name="ELF508_APP_AMT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal elf508_app_amt;

	/** 毀損住宅所在地址 **/
	@Size(max=102)
	@Column(name="ELF508_HOUSE_ADR", length=102, columnDefinition="CHAR(102)")
	private String elf508_house_adr;

	/** 毀損住宅所有權人姓名 **/
	@Size(max=32)
	@Column(name="ELF508_OWNER_NM", length=32, columnDefinition="CHAR(32)")
	private String elf508_owner_nm;

	/** 毀損住宅所有權人配偶身分證字號 **/
	@Size(max=11)
	@Column(name="ELF508_OWNSP_ID", length=11, columnDefinition="CHAR(11)")
	private String elf508_ownsp_id;

	/** 毀損住宅所有權人配偶姓名 **/
	@Size(max=32)
	@Column(name="ELF508_OWNSP_NM", length=32, columnDefinition="CHAR(32)")
	private String elf508_ownsp_nm;

	/** 借款人配偶身分證字號 **/
	@Size(max=11)
	@Column(name="ELF508_SPOUSE_ID", length=11, columnDefinition="CHAR(11)")
	private String elf508_spouse_id;

	/** 借款人配偶姓名 **/
	@Size(max=32)
	@Column(name="ELF508_SPOUSE_NM", length=32, columnDefinition="CHAR(32)")
	private String elf508_spouse_nm;

	/** 重建(購)、修繕貸款擔保品地號 **/
	@Size(max=8)
	@Column(name="ELF508_COLL_LN", length=8, columnDefinition="CHAR(08)")
	private String elf508_coll_ln;

	/** 重建(購)、修繕貸款擔保品建號 **/
	@Size(max=8)
	@Column(name="ELF508_COLL_BN", length=8, columnDefinition="CHAR(08)")
	private String elf508_coll_bn;

	/** 重建(購)、修繕貸款擔保品地址 **/
	@Size(max=102)
	@Column(name="ELF508_COLL_ADDR", length=102, columnDefinition="CHAR(102)")
	private String elf508_coll_addr;

	/** 
	 * 建物所有權人是否設籍於受毀損住宅<p/>
	 * Y:是 N:否
	 */
	@Size(max=1)
	@Column(name="ELF508_SET_HOLD", length=1, columnDefinition="CHAR(01)")
	private String elf508_set_hold;

	/** 銷戶日期(由ALOAN月批程式維護) **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELF508_CANCEL_DATE", columnDefinition="DATE")
	private Date elf508_cancel_date;

	/** ELOAN資料維護日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELF508_ELOAN_DATE", columnDefinition="DATE")
	private Date elf508_eloan_date;

	/** ALOAN資料維護日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELF508_ALOAN_DATE", columnDefinition="DATE")
	private Date elf508_aloan_date;

	/** 取得分行別 **/
	public String getElf508_brno() {
		return this.elf508_brno;
	}
	/** 設定分行別 **/
	public void setElf508_brno(String value) {
		this.elf508_brno = value;
	}

	/** 取得借款戶ID **/
	public String getElf508_cust_id() {
		return this.elf508_cust_id;
	}
	/** 設定借款戶ID **/
	public void setElf508_cust_id(String value) {
		this.elf508_cust_id = value;
	}

	/** 
	 * 取得專案融資種類<p/>
	 * (01:0206震災)
	 */
	public String getElf508_disas_type() {
		return this.elf508_disas_type;
	}
	/**
	 *  設定專案融資種類<p/>
	 *  (01:0206震災)
	 **/
	public void setElf508_disas_type(String value) {
		this.elf508_disas_type = value;
	}

	/** 取得毀損住宅所有權人戶籍號碼 **/
	public String getElf508_hold_no() {
		return this.elf508_hold_no;
	}
	/** 設定毀損住宅所有權人戶籍號碼 **/
	public void setElf508_hold_no(String value) {
		this.elf508_hold_no = value;
	}

	/** 取得毀損住宅所有權人身分證字號 **/
	public String getElf508_owner_id() {
		return this.elf508_owner_id;
	}
	/** 設定毀損住宅所有權人身分證字號 **/
	public void setElf508_owner_id(String value) {
		this.elf508_owner_id = value;
	}

	/** 取得額度序號 **/
	public String getElf508_cntrno() {
		return this.elf508_cntrno;
	}
	/** 設定額度序號 **/
	public void setElf508_cntrno(String value) {
		this.elf508_cntrno = value;
	}

	/** 
	 * 取得貸款種類<p/>
	 * 1：購屋 2：重建 3：修繕
	 */
	public String getElf508_loan_type() {
		return this.elf508_loan_type;
	}
	/**
	 *  設定貸款種類<p/>
	 *  1：購屋 2：重建 3：修繕
	 **/
	public void setElf508_loan_type(String value) {
		this.elf508_loan_type = value;
	}

	/** 取得申請日期 **/
	public Date getElf508_app_date() {
		return this.elf508_app_date;
	}
	/** 設定申請日期 **/
	public void setElf508_app_date(Date value) {
		this.elf508_app_date = value;
	}

	/** 取得申請金額 **/
	public BigDecimal getElf508_app_amt() {
		return this.elf508_app_amt;
	}
	/** 設定申請金額 **/
	public void setElf508_app_amt(BigDecimal value) {
		this.elf508_app_amt = value;
	}

	/** 取得毀損住宅所在地址 **/
	public String getElf508_house_adr() {
		return this.elf508_house_adr;
	}
	/** 設定毀損住宅所在地址 **/
	public void setElf508_house_adr(String value) {
		this.elf508_house_adr = value;
	}

	/** 取得毀損住宅所有權人姓名 **/
	public String getElf508_owner_nm() {
		return this.elf508_owner_nm;
	}
	/** 設定毀損住宅所有權人姓名 **/
	public void setElf508_owner_nm(String value) {
		this.elf508_owner_nm = value;
	}

	/** 取得毀損住宅所有權人配偶身分證字號 **/
	public String getElf508_ownsp_id() {
		return this.elf508_ownsp_id;
	}
	/** 設定毀損住宅所有權人配偶身分證字號 **/
	public void setElf508_ownsp_id(String value) {
		this.elf508_ownsp_id = value;
	}

	/** 取得毀損住宅所有權人配偶姓名 **/
	public String getElf508_ownsp_nm() {
		return this.elf508_ownsp_nm;
	}
	/** 設定毀損住宅所有權人配偶姓名 **/
	public void setElf508_ownsp_nm(String value) {
		this.elf508_ownsp_nm = value;
	}

	/** 取得借款人配偶身分證字號 **/
	public String getElf508_spouse_id() {
		return this.elf508_spouse_id;
	}
	/** 設定借款人配偶身分證字號 **/
	public void setElf508_spouse_id(String value) {
		this.elf508_spouse_id = value;
	}

	/** 取得借款人配偶姓名 **/
	public String getElf508_spouse_nm() {
		return this.elf508_spouse_nm;
	}
	/** 設定借款人配偶姓名 **/
	public void setElf508_spouse_nm(String value) {
		this.elf508_spouse_nm = value;
	}

	/** 取得重建(購)、修繕貸款擔保品地號 **/
	public String getElf508_coll_ln() {
		return this.elf508_coll_ln;
	}
	/** 設定重建(購)、修繕貸款擔保品地號 **/
	public void setElf508_coll_ln(String value) {
		this.elf508_coll_ln = value;
	}

	/** 取得重建(購)、修繕貸款擔保品建號 **/
	public String getElf508_coll_bn() {
		return this.elf508_coll_bn;
	}
	/** 設定重建(購)、修繕貸款擔保品建號 **/
	public void setElf508_coll_bn(String value) {
		this.elf508_coll_bn = value;
	}

	/** 取得重建(購)、修繕貸款擔保品地址 **/
	public String getElf508_coll_addr() {
		return this.elf508_coll_addr;
	}
	/** 設定重建(購)、修繕貸款擔保品地址 **/
	public void setElf508_coll_addr(String value) {
		this.elf508_coll_addr = value;
	}

	/** 
	 * 取得建物所有權人是否設籍於受毀損住宅<p/>
	 * Y:是 N:否
	 */
	public String getElf508_set_hold() {
		return this.elf508_set_hold;
	}
	/**
	 *  設定建物所有權人是否設籍於受毀損住宅<p/>
	 *  Y:是 N:否
	 **/
	public void setElf508_set_hold(String value) {
		this.elf508_set_hold = value;
	}

	/** 取得銷戶日期(由ALOAN月批程式維護) **/
	public Date getElf508_cancel_date() {
		return this.elf508_cancel_date;
	}
	/** 設定銷戶日期(由ALOAN月批程式維護) **/
	public void setElf508_cancel_date(Date value) {
		this.elf508_cancel_date = value;
	}

	/** 取得ELOAN資料維護日期 **/
	public Date getElf508_eloan_date() {
		return this.elf508_eloan_date;
	}
	/** 設定ELOAN資料維護日期 **/
	public void setElf508_eloan_date(Date value) {
		this.elf508_eloan_date = value;
	}

	/** 取得ALOAN資料維護日期 **/
	public Date getElf508_aloan_date() {
		return this.elf508_aloan_date;
	}
	/** 設定ALOAN資料維護日期 **/
	public void setElf508_aloan_date(Date value) {
		this.elf508_aloan_date = value;
	}
}
