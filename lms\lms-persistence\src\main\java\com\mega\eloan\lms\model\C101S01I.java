/* 
 * C101S01I.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Lob;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 個金票信訊息紀錄表(1個人可能會有2筆 C101S01I) **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "C101S01I", uniqueConstraints = @UniqueConstraint(columnNames = {"oid" }))
public class C101S01I extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 身分證統編 **/
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/** 交易流水號 **/
	@Size(max = 6)
	@Column(name = "SEQID", length = 6, columnDefinition = "VARCHAR(06)")
	private String seqId;

	/** 查詢日期 **/
	@Size(max = 8)
	@Column(name = "QDATE", length = 8, columnDefinition = "VARCHAR(08)")
	private String qDate;

	/** 交易代號 **/
	@Size(max = 4)
	@Column(name = "TXID", length = 4, columnDefinition = "VARCHAR(04)")
	private String txid;

	/** TCH編訂之訊息流水號 **/
	@Size(max = 3)
	@Column(name = "SNDRREF", length = 3, columnDefinition = "VARCHAR(03)")
	private String sndrRef;

	/**
	 * TCH回覆之html訊息
	 * <p/>
	 * LONG VARCHAR
	 */
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(name = "HTMLRESULT", columnDefinition = "CLOB")
	private String htmlResult;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得身分證統編 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定身分證統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得身分證統編重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定身分證統編重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得交易流水號 **/
	public String getSeqId() {
		return this.seqId;
	}

	/** 設定交易流水號 **/
	public void setSeqId(String value) {
		this.seqId = value;
	}

	/** 取得查詢日期 **/
	public String getQDate() {
		return this.qDate;
	}

	/** 設定查詢日期 **/
	public void setQDate(String value) {
		this.qDate = value;
	}

	/** 取得交易代號 **/
	public String getTxid() {
		return this.txid;
	}

	/** 設定交易代號 **/
	public void setTxid(String value) {
		this.txid = value;
	}

	/** 取得TCH編訂之訊息流水號 **/
	public String getSndrRef() {
		return this.sndrRef;
	}

	/** 設定TCH編訂之訊息流水號 **/
	public void setSndrRef(String value) {
		this.sndrRef = value;
	}

	/**
	 * 取得TCH回覆之html訊息
	 * <p/>
	 * LONG VARCHAR
	 */
	public String getHtmlResult() {
		return this.htmlResult;
	}

	/**
	 * 設定TCH回覆之html訊息
	 * <p/>
	 * LONG VARCHAR
	 **/
	public void setHtmlResult(String value) {
		this.htmlResult = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
