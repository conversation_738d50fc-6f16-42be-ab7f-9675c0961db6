/* 
 * L120S21ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.Query;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L120S21ADao;
import com.mega.eloan.lms.model.L120S21A;

/**
 * LGD額度共用檔 J-110-0986_05097_B1001 於簽報書新增LGD欄位
 **/
@Repository
public class L120S21ADaoImpl extends LMSJpaDao<L120S21A, String> implements
		L120S21ADao {

	@Override
	public L120S21A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120S21A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addOrderBy("cntrNoCo_s21a");
		search.addOrderBy("cntrNo_s21a");
		search.addOrderBy("oid");
		search.setMaxResults(Integer.MAX_VALUE);
		List<L120S21A> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L120S21A> findByIndex01(String mainId) {
		ISearch search = createSearchTemplete();
		List<L120S21A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addOrderBy("cntrNoCo_s21a");
		search.addOrderBy("cntrNo_s21a");
		search.addOrderBy("oid");
		search.setMaxResults(Integer.MAX_VALUE);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L120S21A> findByIndex02(String mainId, String cntrNoCo_s21a) {
		ISearch search = createSearchTemplete();
		List<L120S21A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (cntrNoCo_s21a != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "cntrNoCo_s21a",
					cntrNoCo_s21a);
		search.addOrderBy("cntrNo_s21a");
		search.addOrderBy("oid");
		search.setMaxResults(Integer.MAX_VALUE);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L120S21A> findByIndex03(String mainId, String cntrNoCo_s21a,
			String cntrNo_s21a) {
		ISearch search = createSearchTemplete();
		List<L120S21A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (cntrNoCo_s21a != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "cntrNoCo_s21a",
					cntrNoCo_s21a);
		if (cntrNo_s21a != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo_s21a",
					cntrNo_s21a);
		search.addOrderBy("oid");
		search.setMaxResults(Integer.MAX_VALUE);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<Object[]> findMinAllocate(String mainId) {
		String sql = "SELECT CNTRNO_S21A,MIN_ALLOCATE2 FROM (SELECT CNTRNO_S21A,MIN(ALLOCATE2) AS MIN_ALLOCATE2 FROM LMS.L120S21A WHERE MAINID = ?1 AND CNTRNO_S21A NOT IN (SELECT CNTRNO_S21B FROM LMS.L120S21B WHERE MAINID = ?2 AND CNTREAD_S21B IS NOT NULL) GROUP BY CNTRNO_S21A HAVING COUNT(*) > 1) AS T1 ORDER BY MIN_ALLOCATE2 DESC";
		Query query = entityManager.createNativeQuery(sql); // 排除掉最後面的AND

		if (!StringUtils.isBlank(mainId)) {
			query.setParameter(1, mainId);
			query.setParameter(2, mainId);
		}

		List<Object[]> resultList = query.getResultList();
		return resultList;
	}

	@Override
	public List<L120S21A> findByMainIdAndCntrNo(String mainId, String cntrNo) {
		ISearch search = createSearchTemplete();
		List<L120S21A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (cntrNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo_s21a",
					cntrNo);
		search.addOrderBy("cntrNoCo_s21a");
		search.addOrderBy("oid");
		search.setMaxResults(Integer.MAX_VALUE);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}
}