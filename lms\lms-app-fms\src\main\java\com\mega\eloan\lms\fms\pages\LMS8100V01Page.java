package com.mega.eloan.lms.fms.pages;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.html.EloanPageFragment;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;

import tw.com.jcs.auth.AuthType;


/**
 * <pre>
 *     覆審考核表 - 編製中
 * </pre>
 * 
 * @since 2022
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Controller@RequestMapping(path = "/fms/lms8100v01")
public class LMS8100V01Page extends AbstractEloanInnerView {

	public LMS8100V01Page() {
		super();
	}

	@Override
	public void  execute(ModelMap model, PageParameters params) {
		setGridViewStatus(CreditDocStatusEnum.海外_編製中);

		// 加上Button
		List<EloanPageFragment> btns = new ArrayList<>();

		// 只有經辦出現的按鈕
		if (this.getAuth(AuthType.Modify)) {
			btns.add(LmsButtonEnum.Filter);
			btns.add(LmsButtonEnum.Add);
			btns.add(LmsButtonEnum.Delete);
		}
		addToButtonPanel(model,	btns);

		renderJsI18N(LMS8100V01Page.class);
		renderJsI18N(AbstractEloanPage.class);
	}
	

	public String[] getJavascriptPath() {
		return new String[] { "pagejs/fms/LMS8100V01Page.js" };
	}
}