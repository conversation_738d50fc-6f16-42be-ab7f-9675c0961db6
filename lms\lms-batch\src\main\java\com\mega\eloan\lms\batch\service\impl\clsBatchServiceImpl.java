package com.mega.eloan.lms.batch.service.impl;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Properties;
import java.util.Set;

import javax.annotation.Resource;

import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.ClsScoreUtil;
import com.mega.eloan.lms.base.constants.Score;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.ScoreService;
import com.mega.eloan.lms.dao.C101M01ADao;
import com.mega.eloan.lms.dao.C101S01ADao;
import com.mega.eloan.lms.dao.C101S01BDao;
import com.mega.eloan.lms.dao.C101S01CDao;
import com.mega.eloan.lms.dao.C101S01DDao;
import com.mega.eloan.lms.dao.C101S01EDao;
import com.mega.eloan.lms.dao.C101S01FDao;
import com.mega.eloan.lms.dao.C101S01GDao;
import com.mega.eloan.lms.dao.C101S01HDao;
import com.mega.eloan.lms.dao.C101S01IDao;
import com.mega.eloan.lms.dao.C101S01JDao;
import com.mega.eloan.lms.dao.C101S01KDao;
import com.mega.eloan.lms.dao.C101S01LDao;
import com.mega.eloan.lms.dao.C101S01MDao;
import com.mega.eloan.lms.dao.C101S01NDao;
import com.mega.eloan.lms.dw.service.DWRKCNTRNOService;
import com.mega.eloan.lms.ejcic.service.EjcicService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.etch.service.EtchService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C101M01A;
import com.mega.eloan.lms.model.C101S01A;
import com.mega.eloan.lms.model.C101S01B;
import com.mega.eloan.lms.model.C101S01C;
import com.mega.eloan.lms.model.C101S01D;
import com.mega.eloan.lms.model.C101S01E;
import com.mega.eloan.lms.model.C101S01F;
import com.mega.eloan.lms.model.C101S01G;
import com.mega.eloan.lms.model.C101S01H;
import com.mega.eloan.lms.model.C101S01I;
import com.mega.eloan.lms.model.C101S01J;
import com.mega.eloan.lms.model.C101S01K;
import com.mega.eloan.lms.model.C101S01L;
import com.mega.eloan.lms.model.C101S01M;
import com.mega.eloan.lms.model.C101S01N;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import jxl.Cell;
import jxl.Sheet;
import jxl.Workbook;
import jxl.read.biff.BiffException;
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

@Service("clsBatchServiceImpl")
public class clsBatchServiceImpl extends AbstractCapService implements
		WebBatchService {

	private static Logger LOGGER = LoggerFactory
			.getLogger(clsBatchServiceImpl.class);

	private static final long serialVersionUID = 1L;

	@Resource
	C101M01ADao c101m01aDao;

	@Resource
	C101S01ADao c101s01aDao;

	@Resource
	C101S01BDao c101s01bDao;

	@Resource
	C101S01CDao c101s01cDao;

	@Resource
	C101S01DDao c101s01dDao;

	@Resource
	C101S01EDao c101s01eDao;

	@Resource
	C101S01FDao c101s01fDao;

	@Resource
	C101S01GDao c101s01gDao;

	@Resource
	C101S01HDao c101s01hDao;

	@Resource
	C101S01IDao c101s01iDao;

	@Resource
	C101S01JDao c101s01jDao;

	@Resource
	C101S01KDao c101s01kDao;

	@Resource
	C101S01LDao c101s01lDao;

	@Resource
	C101S01MDao c101s01mDao;

	@Resource
	C101S01NDao c101s01nDao;

	@Resource
	CodeTypeService codeTypeService;

	@Resource
	EloandbBASEService eloandbbaseservice;

	@Resource
	MisdbBASEService misBaseService;

	@Resource
	EjcicService ejcicService;

	@Resource
	EtchService etchService;

	@Resource
	ScoreService scoreService;

	@Resource
	LMSService lmsService;

	@Resource
	DWRKCNTRNOService dwRkcntrnoService;

	Properties prop;

	public static final Class<?>[] C101CopyClass = { C101M01A.class,
			C101S01A.class, C101S01B.class, C101S01C.class, C101S01D.class };

	public static final Class<?>[] C101AllClass = { C101M01A.class,
			C101S01A.class, C101S01B.class, C101S01C.class, C101S01D.class,
			C101S01E.class, C101S01F.class, C101S01G.class, C101S01H.class,
			C101S01I.class, C101S01J.class, C101S01K.class, C101S01L.class,
			C101S01M.class, C101S01N.class };

	@Override
	public JSONObject execute(JSONObject json) {
		int count = 0;
		StringBuilder sb = new StringBuilder();
		// 可以從json內取得參數
		long t1 = System.currentTimeMillis();
		LOGGER.info("傳入參數==>[{}]", json.toString());
		System.out.println("start");
		// JSONObject request = json.getJSONObject("request");
		JSONObject result = new JSONObject();
		String path = Util.trim(json.get("path"));
		String setScore = Util.trim(json.get("score"));

		try {
			if (Util.isEmpty(setScore)){
				String ownBrId = "005";
				String dupNo = "0";
				String dataId = "A100063900";
				// get base data
				JSONObject baseData = getBaseData(dataId, dupNo);
				LOGGER.info("baseData=" + baseData.toString());
				
				for (String custId : read(path)) {
					System.out.println("custId=" + custId);
					if (!dataId.equals(custId)) {
						LOGGER.info("custId = " + custId);
						JSONObject data = new JSONObject();
						data.putAll(baseData);
	
						// check have data
						String mainId = null;
						C101M01A c101m01a = c101m01aDao.findByUniqueKey(mainId,
								ownBrId, custId, dupNo);
						// get mainId
						if (c101m01a == null) {
							mainId = IDGenerator.getUUID();
						} else {
							LOGGER.info("continue[" + custId + " " + dupNo + "]");
							mainId = Util.trim(c101m01a.getMainId());
							//continue;
						}
						data.put(EloanConstants.MAIN_ID, mainId);
						data.put("custId", custId);
						data.put("custName", "批次" + custId);
						data.put("dupNo", dupNo);
						LOGGER.info("data=" + data.toString());
						// 取得聯徵和票信資料
						data.putAll(scoreService.getData(custId, dupNo
								, ClsScoreUtil.V2_0_HOUSE_LOAN, ClsScoreUtil.V2_1_NOT_HOUSE_LOAN, ClsScoreUtil.V2_1_CARD_LOAN
								, data));
						// 檢核有無聯徵&票信資料
						if (Util.isNotEmpty(data.get(Score.column.聯徵資料日期))
								&& Util.isNotEmpty(data.get(Score.column.票信資料日期))) {
							JSONObject score = scoreService.score(Score.type.基本,
									data, false);
							LOGGER.info("score=" + score.toString());
							data.putAll(score); // 評等
							// 儲存
							save(data, mainId, custId, dupNo);
							LOGGER.info("success [" + custId + "]");
							System.out.println("success [" + custId + "]");
							sb.append("[").append(custId).append("]");
							count++;
						}
					}
				}
			}else{
				ISearch search = c101s01gDao.createSearchTemplete();
				//search.addSearchModeParameters(SearchMode.EQUALS, "creator", "STEL01");
				search.addSearchModeParameters(SearchMode.EQUALS, "grdCDate", "2013-03-28");
				search.setFirstResult(0).setMaxResults(Integer.MAX_VALUE);
				List<C101S01G> list = c101s01gDao.find(search);
				for (C101S01G model : list){
					count++;
					String custId = Util.trim(model.getCustId());
					sb.append(custId).append(",");
					System.out.println("custid = "+ custId+ "  " + String.valueOf(count));
					LOGGER.info("custId [" + custId + "] updpate score");
					updateScore(model);
				}
			}

		} catch (Exception ex) {
			result.put("success", false);
			LOGGER.error("[execute] Exception!!", ex);
		} finally {
			result.put("custId", sb.toString());
			result.put("count", count);
			result.put("success", true);
			LOGGER.info("RESULT={}", result.toString());
			LOGGER.info(StrUtils.concat("TOTAL_COST= ",
					(System.currentTimeMillis() - t1), " ms"));
		}
		System.out.println("end");
		return result;
	}

	/**
	 * read excel
	 * 
	 * @return
	 */
	private Set<String> read(String path) {
		Set<String> result = new HashSet<String>();

		InputStream is = null;
		Workbook rwb = null;
		Sheet sheet = null;
		try {
			// is = new FileInputStream(
			// "D:\\MegaWorkSpace\\lms2\\lms\\lms-config\\src\\main\\resources\\cls\\EJCIC測試用ID-1.xls");

			if (Util.isEmpty(path))
				path = "cls/EJCIC_ID.xls";

			LOGGER.info("read excel path:" + path);
			
			is = Thread.currentThread().getContextClassLoader()
					.getResourceAsStream(path);
			// is = new FileInputStream("cls/EJCIC測試用ID-1.xls");

			if (is != null) {
				rwb = Workbook.getWorkbook(is);
				sheet = rwb.getSheet(0); // 取得資料表
				// int rsColumns = sheet.getColumns(); // 獲取Sheet表中所包含的總列數
				int rsRows = sheet.getRows() - 1; // 獲取Sheet表中所包含的總行數
				//rsRows = 40;
				System.out.println("rsRows = " + rsRows);
				LOGGER.info("rsRows = " + rsRows);
				// rsRows = 6;
				// System.out.println("rsRows = "+rsRows);
				for (int y = 30; y <= rsRows; y++) {
					Cell cell = sheet.getCell(0, y);
					String id = Util.trim(cell.getContents());
					if (Util.isNotEmpty(id) && !result.contains(id)) {
						result.add(id);
					}
				}
			} else {
				LOGGER.info("excel is not found!");
			}

		} catch (FileNotFoundException e) {
			LOGGER.error("FileNotFoundException:" + e.toString());
			LOGGER.error(e.getMessage());
		} catch (BiffException e) {
			LOGGER.error("BiffException:" + e.toString());
			LOGGER.error(e.getMessage());
		} catch (IOException e) {
			LOGGER.error("IOException:" + e.toString());
			LOGGER.error(e.getMessage());
		} finally {
			sheet = null;
			if (rwb != null) {
				rwb.close();
				rwb = null;
			}
			try {
				if (is != null) {
					is.close();
					is = null;
				}
			} catch (IOException e) {
				LOGGER.error("IOException:" + e.toString());
				LOGGER.error(e.getMessage());
			}
		}

		return result;
	}

	/**
	 * get base data
	 */
	private JSONObject getBaseData(String custId, String dupNo)
			throws CapException {
		JSONObject result = new JSONObject();
		Class<?>[] Classes = { C101M01A.class, C101S01A.class, C101S01B.class,
				C101S01C.class };
		for (Class<?> clazz : Classes) {
			GenericBean bean = findModelByKey(clazz, null, custId, dupNo);
			if (bean != null) {
				result.putAll(DataParse.toJSON(bean));
			}
		}

		return result;
	}

	private void save(JSONObject data, String mainId, String custId,
			String dupNo) throws CapException {
		List<GenericBean> list = new ArrayList<GenericBean>();
		Class<?>[] Classes = { C101M01A.class, C101S01A.class, C101S01B.class,
				C101S01C.class, C101S01G.class };
		for (Class<?> clazz : Classes) {
			GenericBean bean = findModelByKey(clazz, mainId, custId, dupNo,
					true);
			DataParse.toBean(data, bean);
			list.add(bean);

			// update mis
			// if (clazz == C101S01G.class)
			// updateMis((C101S01G) bean);
		}
		save(list);

//		for (GenericBean bean : list) {
//			if (bean instanceof C101S01G)
//				updateMis((C101S01G) bean);
//		}
	}

	private void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				// set updater and updateTime
				try {
					if (Util.isEmpty(model.get(EloanConstants.OID))) {
						model.set("creator", user.getUserId());
						model.set("createTime", CapDate.getCurrentTimestamp());
					}
					model.set("updater", user.getUserId());
					model.set("updateTime", CapDate.getCurrentTimestamp());
				} catch (CapException e) {
					LOGGER.error(e.getMessage());
				}

				if (model instanceof C101M01A) {
					c101m01aDao.save(((C101M01A) model));
				} else if (model instanceof C101S01A) {
					c101s01aDao.save(((C101S01A) model));
				} else if (model instanceof C101S01B) {
					c101s01bDao.save(((C101S01B) model));
				} else if (model instanceof C101S01C) {
					c101s01cDao.save(((C101S01C) model));
				} else if (model instanceof C101S01D) {
					c101s01dDao.save(((C101S01D) model));
				} else if (model instanceof C101S01E) {
					c101s01eDao.save(((C101S01E) model));
				} else if (model instanceof C101S01F) {
					c101s01fDao.save(((C101S01F) model));
				} else if (model instanceof C101S01G) {
					c101s01gDao.save(((C101S01G) model));
				} else if (model instanceof C101S01H) {
					c101s01hDao.save(((C101S01H) model));
				} else if (model instanceof C101S01I) {
					c101s01iDao.save(((C101S01I) model));
				} else if (model instanceof C101S01J) {
					c101s01jDao.save(((C101S01J) model));
				} else if (model instanceof C101S01K) {
					c101s01kDao.save(((C101S01K) model));
				} else if (model instanceof C101S01L) {
					c101s01lDao.save(((C101S01L) model));
				} else if (model instanceof C101S01M) {
					c101s01mDao.save(((C101S01M) model));
				} else if (model instanceof C101S01N) {
					c101s01nDao.save(((C101S01N) model));
				}
			}
		}
	}

	private void save(List<GenericBean> list) {
		for (GenericBean model : list) {
			save(model);
		}
	}

	@SuppressWarnings("unchecked")
	private <T extends GenericBean> T findModelByKey(Class<?> clazz,
			String mainId, String custId, String dupNo) {
		return (T)findModelByKey(clazz, mainId, custId, dupNo, false);
	}

	@SuppressWarnings("unchecked")
	private <T extends GenericBean> T findModelByKey(Class<?> clazz,
			String mainId, String custId, String dupNo, boolean create) {
		if (clazz == C101M01A.class) {
			C101M01A model = c101m01aDao.findByUniqueKey(mainId, null, custId,
					dupNo);
			return (T) (model == null && create ? new C101M01A() : model);
		} else if (clazz == C101S01A.class) {
			C101S01A model = c101s01aDao.findByUniqueKey(mainId, custId, dupNo);
			return (T) (model == null && create ? new C101S01A() : model);
		} else if (clazz == C101S01B.class) {
			C101S01B model = c101s01bDao.findByUniqueKey(mainId, custId, dupNo);
			return (T) (model == null && create ? new C101S01B() : model);
		} else if (clazz == C101S01C.class) {
			C101S01C model = c101s01cDao.findByUniqueKey(mainId, custId, dupNo);
			return (T) (model == null && create ? new C101S01C() : model);
		} else if (clazz == C101S01D.class) {
			C101S01D model = c101s01dDao.findByUniqueKey(mainId, custId, dupNo);
			return (T) (model == null && create ? new C101S01D() : model);
		} else if (clazz == C101S01E.class) {
			C101S01E model = c101s01eDao.findByUniqueKey(mainId, custId, dupNo);
			return (T) (model == null && create ? new C101S01E() : model);
		} else if (clazz == C101S01F.class) {
			C101S01F model = c101s01fDao.findByUniqueKey(mainId, custId, dupNo);
			return (T) (model == null && create ? new C101S01F() : model);
		} else if (clazz == C101S01G.class) {
			C101S01G model = c101s01gDao.findByUniqueKey(mainId, null, custId,
					dupNo);
			return (T) (model == null && create ? new C101S01G() : model);
		} else if (clazz == C101S01H.class) {
			C101S01H model = c101s01hDao.findByUniqueKey(mainId, custId, dupNo);
			return (T) (model == null && create ? new C101S01H() : model);
		} else if (clazz == C101S01I.class) {
			C101S01I model = c101s01iDao.findByUniqueKey(mainId, custId, dupNo);
			return (T) (model == null && create ? new C101S01I() : model);
		} else if (clazz == C101S01J.class) {
			C101S01J model = c101s01jDao.findByUniqueKey(mainId, custId, dupNo);
			return (T) (model == null && create ? new C101S01J() : model);
		} else if (clazz == C101S01K.class) {
			C101S01K model = c101s01kDao.findByUniqueKey(mainId, custId, dupNo,
					null, null);
			return (T) (model == null && create ? new C101S01K() : model);
		} else if (clazz == C101S01L.class) {
			C101S01L model = c101s01lDao.findByUniqueKey(mainId, custId, dupNo,
					null, null, null);
			return (T) (model == null && create ? new C101S01L() : model);
		} else if (clazz == C101S01M.class) {
			C101S01M model = c101s01mDao.findByUniqueKey(mainId, custId, dupNo,
					null);
			return (T) (model == null && create ? new C101S01M() : model);
		} else if (clazz == C101S01N.class) {
			C101S01N model = c101s01nDao.findByUniqueKey(mainId, custId, dupNo,
					null);
			return (T) (model == null && create ? new C101S01N() : model);
		}
		return null;
	}

	private void updateMis(C101S01G model) throws CapException {

		JSONObject source = DataParse.toJSON(model);
		JSONObject data = new JSONObject();

		data.put("BR_CD", source.get("ownBrId")); // 分行
		data.put("NOTEID", source.get("oid")); // NOTES文件編號
		data.put("CUSTID", source.get("custId")); // 客戶統一編號
		data.put("DUPNO", source.get("dupNo")); // 重複序號
		data.put("MOWTYPE", "1"); // 評等模型類別
		data.put("MOWVER1", 0); // 模型版本-大版
		data.put("MOWVER2", 0); // 模型版本-小版
		data.put("JCIC_DATE", source.get("jcicQDate")); // JCIC查詢日期 YYYY-MM-DD
		data.put("CUST_KEY", source.get("custId")); // 主借款人統一編號( CUSTKEY)
		data.put("LNGEFLAG", ""); // 相關身分 ( LNGEFLAG)
		data.put("BASE_A", source.get("varA")); // A
		data.put("BASE_B", source.get("varB")); // B
		data.put("BASE_S", source.get("varC")); // 常數項
		data.put("BASE_SCORE", source.get("scrNum12")); // 基準底分
		data.put("TOTAL_SCORE", source.get("scrNum11")); // 加總各變量得分
		data.put("INITIAL_SCORE", source.get("scrNum13")); // 初始評分
		data.put("PREDICT_BAD_RATE", source.get("pd")); // 預測壞率
		data.put("INITIAL_RATING", source.get("grade1")); // 初始評等
		data.put("ADJ_RATING", Util.isEmpty(source.get("grade2")) ? null
				: source.get("grade2")); // 調整評等
		data.put("FINAL_RATING", source.get("grade3")); // 最終評等
		data.put("JCIC_WARNING_FLAG", ""); // 出現聯徵特殊負面資訊
		data.put("FINAL_RATING_FLAG", ""); // 本案為最終採用之關係人評等
		data.put("DELETE_REASON", ""); // 刪除本筆紀錄原因
		data.put("REJECT_OTHEREASON_TEXT", ""); // 刪除原因為其他時之理由文字
		data.put("DOCSTATUS", "B"); // 文件狀態
		data.put("DATA_SRC_DT", TWNDate.toAD(new Date())); // 上傳資料日期
		System.out.println("data= " + data.toString());
		if (dwRkcntrnoService.update(data) <= 0) {
			dwRkcntrnoService.insert(data);
		}
	}
	
	private void updateScore(C101S01G model) throws CapException {

		JSONObject source = DataParse.toJSON(model);
		JSONObject data = new JSONObject();
		data.put("BR_CD", source.get("ownBrId")); // 分行
		data.put("NOTEID", source.get("oid")); //NOTES文件編號
		data.put("CUSTID", source.get("custId")); //客戶統一編號
		data.put("DUPNO", source.get("dupNo")); //重複序號
		data.put("MOWTYPE", "M"); //評等模型類別
		data.put("MOWVER1", 1); //模型版本-大版
		data.put("MOWVER2", 3); //模型版本-小版
		data.put("JCIC_DATE", source.get("jcicQDate")); //JCIC查詢日期 YYYY-MM-DD
		data.put("CUST_KEY", source.get("custId")); //主借款人統一編號(  CUSTKEY)  
		data.put("LNGEFLAG", ""); //相關身分 ( LNGEFLAG)
		
		//data.put("CC12_REVOL_BAL", 0); //近12個月循環信用餘額(ΣREVOL_BAL)
		//data.put("CC12_REVOL_LIMIT", 0); //近12個月有動用循環信用之信用卡額度(ΣPERM_LIMIT)
		
		//data.put("R01_CC12_MAX_REVOL_RATE", source.get("avgRate01")); //近12個月循環信用使用率(REVOL_RATE) 
		//data.put("R01_CC12_MAX_REVOL_RATE_SCORE", source.get("scrNum07")); //R_01得分
		
		data.put("CC_REVOL_BAL", 0); //當月循環信用餘額(ΣREVOL_BAL)
		data.put("CC_REVOL_PERMIT_LIMIT", 0); //當月有動用循環信用之信用卡額度(ΣPERM_LIMIT)
		
		data.put("R10_REVOL_RATE", source.get("avgRate02")); //當月循環信用使用率(REVOL_RATE) 
		data.put("R10_REVOL_RATE_SCORE", source.get("scrNum08")); //R_10得分
		
		data.put("D07_LN_NOS_TAMT", source.get("chkAmt01")); //當月無擔保授信餘額(仟元)
		data.put("D07_LN_NOS_TAMT_SCORE", source.get("scrNum04")); //D_07得分
		
		//data.put("D12_CC12_MAX_AVGLIMIT", source.get("avgRate01")); //近12個月有繳款紀錄之信用卡的最大信用額度平均值(元)
		//data.put("D12_CC13_MAX_AVGLIMIT", source.get("scrNum07")); //SCORE	D_12得分
		
		data.put("D15_CC6_AVG_RC", source.get("chkAmt02")); //近6個月平均的月信用卡循環信用(元)
		data.put("D15_CC6_AVG_RC_SCORE", source.get("scrNum05")); //D_15得分
		
		data.put("P19_CC12_PCODE_A_TIMES", source.get("chkNum3")); //近12個月信用卡繳款狀況出現全額繳清無延遲次數
		//data.put("P19_CC13_PCODE_A_TIMES_SCORE", source.get("scrNum10")); //P_19得分
		
		data.put("MARRIAGE", source.get("marry")); //婚姻狀況
		data.put("CHILDREN", source.get("child")); //扶養子女數
		//data.put("MARRIAGE_SCORE", source.get("scrNum02")); //婚姻狀況得分
		data.put("OCCOUATION_1", source.get("jobType1")); //職業大類
		data.put("OCCOUATION_1_SCORE", source.get("scrNum03")); //職業大類得分
		//data.put("COLL_3", ""); //擔保品所在地的郵遞區號
		//data.put("COLL_3_SCORE", 0); //擔保品所在地的郵遞區號得分
		data.put("EDUCATION", source.get("edu")); //學歷
		//sdata.put("EDUCATION_SCORE", source.get("scrNum02")); //學歷得分
		
		//data.put("N11_INQ3_LN_BANK", 0); //近3個月(不含查詢當日)授信類查詢總家數
		//data.put("N11_INQ3_LN_BANK_SCORE", 0); //N_11得分
		
		data.put("N06_INQ12_NAPP_BANK", source.get("inqQty")); //近12個月新業務申請查詢總家數
		data.put("N06_INQ12_NAPP_BANK_SCORE", source.get("scrNum06")); //N_06得分
	
		//data.put("H01_CC_MAX_MONTH", 0); //當月持有有效信用卡主卡最長持卡月份數
		//data.put("H01_CC_MAX_MONTH_SCORE", 0); //H _01得分
		
		//data.put("D33_LN6_AVG_NOS_AMT", source.get("chkAmt02")); //D33近6個月平均無擔保授信餘額(仟元) 
		//data.put("C01_LN6_TAMT_ADDRATIO", 0); //C01 近6個月授信總餘額的增加率
		
		//data.put("D01_LN_TAMT", 0); //當月之授信總餘額
		//data.put("LN6_6TH_TAMT", 0); //過去第6個月底之授信總餘額
		
		//data.put("D33_C01_SCORE", source.get("scrNum05")); //D33&C01得分
		
		data.put("RATING_DATE", source.get("grdCDate")); //	評等日期
		data.put("DOCSTATUS", "B"); //	文件狀態
		data.put("DATA_SRC_DT", TWNDate.toAD(new Date())); //	上傳資料日期
	
		data.put("R01_CC12_REVOL_RATE", source.get("avgRate01")); //	近12個月信用卡(每筆)循環信用平均使用率
		data.put("R01_CC12_REVOL_RATE_SCORE", source.get("scrNum07")); //	近12個月信用卡(每筆)循環信用平均使用率得分
		data.put("HINCOME_REG", source.get("yFamAmt")); //	夫妻年收入(萬元)
		data.put("HINCOME_REG_SCORE", source.get("scrNum01")); //	夫妻年收入(萬元)得分
		data.put("P69_CC12_DELAY_RC_TIMES", source.get("chkNum2")); //	近12個月信用卡繳款狀況出現不良繳款紀錄或使用循環信用的次數
		data.put("P25_CC6_PCODE_A_TIMES", source.get("chkNum1")); //	近6個月信用卡繳款狀況出現全額繳清無延遲次數
		data.put("P25_CC6_PCODE_A_TIMES_SCORE", source.get("scrNum09")); //	近6個月信用卡繳款狀況出現全額繳清無延遲次數得分
		data.put("MARR_EDU_SCORE", source.get("scrNum02")); //	婚姻_學歷得分
		data.put("P69_P19_SCORE", source.get("scrNum10")); //	P69_P19得分
		
		System.out.println("data= " + data.toString());
		if (dwRkcntrnoService.updateScore(data) <= 0) {
			dwRkcntrnoService.insertScore(data);
		}
	}
}
