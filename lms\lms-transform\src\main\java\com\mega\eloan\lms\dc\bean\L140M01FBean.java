package com.mega.eloan.lms.dc.bean;

import java.io.Serializable;

import com.mega.eloan.lms.dc.util.TextDefine;
import com.mega.eloan.lms.dc.util.Util;

public class L140M01FBean extends BaseBean implements Serializable {

	private static final long serialVersionUID = -5696350731741705583L;

	private String rateSeq = "";// 序號
	private String loanTPList = "";// 授信科目代碼組合授信科目代碼組合
	private String rateDscr = "";// 組成說明字串

	public String getRateSeq() {
		return rateSeq;
	}

	public void setRateSeq(String rateSeq) {
		this.rateSeq = rateSeq;
	}

	public String getLoanTPList() {
		return loanTPList;
	}

	public void setLoanTPList(String loanTPList) {
		this.loanTPList = loanTPList;
	}

	public String getRateDscr() {
		return rateDscr;
	}

	public void setRateDscr(String rateDscr) {
		this.rateDscr = rateDscr;
	}

	@Override
	public String toString() {
		StringBuffer sb = new StringBuffer();
		sb.append(Util.nullToSpace(this.getOid()))
				.append(TextDefine.FILE_DELIM);
		sb.append(Util.nullToSpace(this.getMainId()).trim()).append(
				TextDefine.FILE_DELIM);
		sb.append(Util.nullToSpace(this.getRateSeq()).trim()).append(
				TextDefine.FILE_DELIM);
		sb.append(Util.nullToSpace(this.getLoanTPList()).trim()).append(
				TextDefine.FILE_DELIM);
		sb.append(Util.nullToSpace(this.getRateDscr()).trim()).append(
				TextDefine.FILE_DELIM);
		sb.append(this.getCreator()).append(TextDefine.FILE_DELIM);
		sb.append(this.getCreateTime()).append(TextDefine.FILE_DELIM);
		sb.append(this.getUpdater()).append(TextDefine.FILE_DELIM);
		sb.append(this.getUpdateTime());
		return sb.toString();
	}

}
