package com.mega.eloan.lms.cls.panels;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.common.ClsScoreUtil;
import com.mega.eloan.lms.base.service.ScoreService;

import tw.com.jcs.common.Util;

/**
 * <pre>
 * 個金徵信作業
 * </pre>
 * 
 * @since 2012/10/11
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/10/11,Fantasy,new
 *          </ul>
 */
public class CLS1131S01GPanel extends Panel {

	private static final long serialVersionUID = 1L;
	
	private String varVer;

	private boolean showSDT;

	@Autowired
	ScoreService scoreService;

	/**
	 * @param id
	 */
	public CLS1131S01GPanel(String id) {
		super(id);
	}

	/**
	 * @param id
	 * @param varVer
	 * @param showSDT
	 */
	public CLS1131S01GPanel(String id, String varVer, boolean showSDT) {
		super(id);
		this.varVer = varVer;
		this.showSDT = showSDT;
	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);

		//TITLE
		boolean t_v1_3_v2_0 = false; //房貸1.3跟2.0共用(unknown也先用這個)
		boolean t_v3_0 = false;
		if(Util.equals(varVer, ClsScoreUtil.V3_0_HOUSE_LOAN)){
			t_v3_0 = true;
		}else{
			t_v1_3_v2_0 = true;
		}
		model.addAttribute("FACTOR_TITLE_V1_3_V2_0", t_v1_3_v2_0);
		model.addAttribute("FACTOR_TITLE_V3_0", t_v3_0);
		model.addAttribute("FACTOR_TOTAL_V1_3_V2_0", t_v1_3_v2_0);
		model.addAttribute("FACTOR_TOTAL_V3_0", t_v3_0);
		
		//內容
		boolean v1_3 = false;
		boolean v2_0 = false;
		boolean v2_1 = false;
		boolean v3_0 = false;
		boolean v_unknown = false;
		if(Util.equals(varVer, ClsScoreUtil.V1_3_HOUSE_LOAN)){
			v1_3 = true;
		}else if(Util.equals(varVer, ClsScoreUtil.V2_0_HOUSE_LOAN)){
			v2_0 = true;
		}else if(Util.equals(varVer, ClsScoreUtil.V2_1_HOUSE_LOAN)){
			v2_1 = true;
		}else if(Util.equals(varVer, ClsScoreUtil.V3_0_HOUSE_LOAN)){
			v3_0 = true;
		}else{
			v_unknown = true;
		}
		model.addAttribute("FACTOR_V1_3", v1_3);
		model.addAttribute("FACTOR_V2_0", v2_0);
		model.addAttribute("FACTOR_V2_1", v2_1);
		model.addAttribute("FACTOR_V3_0", v3_0);
		model.addAttribute("FACTOR_V_UNKNOWN", v_unknown);
		model.addAttribute("DOUBLETRACK", showSDT);
		model.addAttribute("DOUBLETRACK2", showSDT);
	}
}
