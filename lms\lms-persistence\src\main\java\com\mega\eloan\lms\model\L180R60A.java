/* 
 * L180R60A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 振興券兌換客戶統計檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L180R60A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class L180R60A extends GenericBean implements IDataObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 分行代號 **/
	@Size(max = 3)
	@Column(name = "CASEBRID", length = 3, columnDefinition = "CHAR(3)")
	private String caseBrId;

	/** 本行/跨行 **/
	@Size(max = 1)
	@Column(name = "DATATYPE", length = 1, columnDefinition = "CHAR(1)")
	private String dataType;

	/** 客戶統編 **/
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 客戶名稱 **/
	@Size(max = 150)
	@Column(name = "CUSTNAME", length = 150, columnDefinition = "VARCHAR(150)")
	private String custName;

	/** 200元券(張) **/
	@Digits(integer = 10, fraction = 0, groups = Check.class)
	@Column(name = "COUPON200", columnDefinition = "DECIMAL(10,0)")
	private BigDecimal coupon200;

	/** 500元券(張) **/
	@Digits(integer = 10, fraction = 0, groups = Check.class)
	@Column(name = "COUPON500", columnDefinition = "DECIMAL(10,0)")
	private BigDecimal coupon500;

	/** 總張數 **/
	@Digits(integer = 10, fraction = 0, groups = Check.class)
	@Column(name = "COUPONTOT", columnDefinition = "DECIMAL(10,0)")
	private BigDecimal couponTot;

	/** 總金額 **/
	@Digits(integer = 17, fraction = 0, groups = Check.class)
	@Column(name = "COUPONAMT", columnDefinition = "DECIMAL(17,0)")
	private BigDecimal couponAmt;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得分行代號 **/
	public String getCaseBrId() {
		return this.caseBrId;
	}

	/** 設定分行代號 **/
	public void setCaseBrId(String value) {
		this.caseBrId = value;
	}

	/** 取得本行/跨行 **/
	public String getDataType() {
		return this.dataType;
	}

	/** 設定本行/跨行 **/
	public void setDataType(String value) {
		this.dataType = value;
	}

	/** 取得客戶統編 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定客戶統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得客戶名稱 **/
	public String getCustName() {
		return this.custName;
	}

	/** 設定客戶名稱 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/** 取得200元券(張) **/
	public BigDecimal getCoupon200() {
		return this.coupon200;
	}

	/** 設定200元券(張) **/
	public void setCoupon200(BigDecimal value) {
		this.coupon200 = value;
	}

	/** 取得500元券(張) **/
	public BigDecimal getCoupon500() {
		return this.coupon500;
	}

	/** 設定500元券(張) **/
	public void setCoupon500(BigDecimal value) {
		this.coupon500 = value;
	}

	/** 取得總張數 **/
	public BigDecimal getCouponTot() {
		return this.couponTot;
	}

	/** 設定總張數 **/
	public void setCouponTot(BigDecimal value) {
		this.couponTot = value;
	}

	/** 取得總金額 **/
	public BigDecimal getCouponAmt() {
		return this.couponAmt;
	}

	/** 設定總金額 **/
	public void setCouponAmt(BigDecimal value) {
		this.couponAmt = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
