/* 
 * C123M01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C123M01A;

/** 海外消金評等表主檔 **/
public interface C123M01ADao extends IGenericDao<C123M01A> {

	C123M01A findByOid(String oid);
	
	List<C123M01A> findByMainId(String mainId);
	
	C123M01A findByUniqueKey(String mainId);

	List<C123M01A> findByIndex01(String mainId);

	List<C123M01A> findByIndex02(String ownBrId, String custId, String dupNo);
	
	List<C123M01A> findLastRecord(String ownBrId, String custId, String dupNo,String oid,String docStatus);

}