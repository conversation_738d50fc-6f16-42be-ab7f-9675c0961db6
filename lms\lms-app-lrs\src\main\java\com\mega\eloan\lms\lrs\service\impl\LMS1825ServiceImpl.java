/* 
 * LMS1825ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lrs.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.core.FlowException;
import tw.com.jcs.flow.service.FlowService;

import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.dao.L182A01ADao;
import com.mega.eloan.lms.dao.L182M01ADao;
import com.mega.eloan.lms.lrs.service.LMS1805Service;
import com.mega.eloan.lms.lrs.service.LMS1825Service;
import com.mega.eloan.lms.model.L182A01A;
import com.mega.eloan.lms.model.L182M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

@Service
public class LMS1825ServiceImpl extends AbstractCapService implements
		LMS1825Service {
	private static Logger logger = LoggerFactory
	.getLogger(LMS1825ServiceImpl.class);

	@Resource
	L182M01ADao l182m01adao;
	
	@Resource
	L182A01ADao l182a01adao;
	
	@Resource
	FlowService flowService;
	
	@Resource
	LMS1805Service lms1805Service;

	@Override
	public void startFlow(String mainOid) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		flowService.start("LMS1825Flow", mainOid, user.getUserId(),
				user.getUnitNo());
	}
	
	@Override
	public void flowAction(String mainOid, GenericBean model,
			boolean setResult, boolean resultType) throws Throwable {
		if (model instanceof L182M01A) {
			save((L182M01A) model);
		}
		try {
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			FlowInstance inst = flowService.createQuery().id(mainOid).query();
			if (inst == null) {
				inst = flowService.start("LMS1825Flow", mainOid,
						user.getUserId(), user.getUnitNo());
			}
			if (setResult) {
				inst.setAttribute("result", resultType ? "to預約單_處理成功" : "to預約單_處理失敗");
			}
			inst.next();
		} catch (FlowException e) {
			Throwable t1 = e;
			while (t1.getCause() != null) {
				t1 = t1.getCause();
			}
			throw t1;
		}
	}
	
	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		if (clazz == L182M01A.class) {
			return (T) l182m01adao.find(oid);
		}
		return null;
	}

	@Override
	public List<L182M01A> findUnProcessedTypCd5() {
		return l182m01adao.findUnProcessedTypCd5(RetrialDocStatusEnum.預約單_未處理.getCode());
	}
	
	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == L182M01A.class) {
			return l182m01adao.findPage(search);
		}
		return null;
	}

	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L182M01A) {
					String oid = ((L182M01A) model).getOid();
					if(user != null){
						((L182M01A) model).setUpdater(user.getUserId());
					}
					((L182M01A) model).setUpdateTime(CapDate.getCurrentTimestamp());

					if (Util.isEmpty(oid)) {
						((L182M01A) model).setCreateTime(CapDate.getCurrentTimestamp());
						if(user != null){
							((L182M01A) model).setCreator(user.getUserId());
							((L182M01A) model).setOwnBrId(user.getUnitNo());
						}
					}
					l182m01adao.save(((L182M01A) model));
					if(Util.isEmpty(oid)){
						startFlow(((L182M01A) model).getOid());
					}
				}else if (model instanceof L182A01A) {
					l182a01adao.save(((L182A01A) model));
				}
			}
		}
	}

	@SuppressWarnings("rawtypes")
	@Override
	public void delete(Class clazz, String oid) {
		if (clazz == L182M01A.class) {
			L182M01A l181m01a = l182m01adao.findByOid(oid);
			l182m01adao.delete(l181m01a);
		}
	}
	
	@Override
	public void deleteMain(String oid){
		L182M01A l182m01a = l182m01adao.findByOid(oid);
		if(l182m01a != null){
			List<L182A01A> l182a01as = l182a01adao.findByMainId(l182m01a.getMainId());
			l182a01adao.delete(l182a01as);
			l182m01adao.delete(l182m01a);
		}
	}
}
