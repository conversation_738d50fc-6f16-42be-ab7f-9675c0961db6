/* 
 * L730M01ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.Date;
import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L730M01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L730M01A;

/** 授信報案考核表主檔 **/
@Repository
public class L730M01ADaoImpl extends LMSJpaDao<L730M01A, String>
	implements L730M01ADao {

	@Override
	public L730M01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public L730M01A findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		return findUniqueOrNone(search);
	}
			
	@Override
	public List<L730M01A> findByDocStatus(String docStatus){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "docStatus", docStatus);
		List<L730M01A> list = createQuery(L730M01A.class, search).getResultList();
		return list;
	}
	
	@Override
	public L730M01A findByUniqueKey(String mainId){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L730M01A> findByIndex01(String mainId){
		ISearch search = createSearchTemplete();
		List<L730M01A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(L730M01A.class, search).getResultList();
		}
		return list;
	}

	@Override
	public List<L730M01A> findByIndex02(String branchId, Date chkYM, String sysType, String chkUnit, String mainId){
		ISearch search = createSearchTemplete();
		List<L730M01A> list = null;
		if (branchId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "branchId", branchId);
		if (chkYM != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "chkYM", chkYM);
		if (sysType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "sysType", sysType);
		if (chkUnit != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "chkUnit", chkUnit);
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(L730M01A.class, search).getResultList();
		}
		return list;
	}
}