/* 
 * L120S25BDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L120S25BDao;
import com.mega.eloan.lms.model.L120S25B;

/** 會計科目CCF對照檔 **/
@Repository
public class L120S25BDaoImpl extends LMSJpaDao<L120S25B, String> implements
		L120S25BDao {

	@Override
	public List<L120S25B> findByAcKey(String acKey) {
		ISearch search = createSearchTemplete();
		List<L120S25B> list = null;
		if (acKey != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "acKey", acKey);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}
}