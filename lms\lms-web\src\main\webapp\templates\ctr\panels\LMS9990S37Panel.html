<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body><!--L999M01A--><!--L999S02A-->
        <th:block th:fragment="panelFragmentBody">
        	<script type="text/javascript">
				require(['pagejs/ctr/LMS9990M09Page'], function(){
					loadScript('pagejs/ctr/LMS9990S37Panel');
				});
			</script>
                <fieldset>
                    <legend>
                        <b><th:block th:text="#{'L999M01AM09.title01'}"><!--一般條款--></th:block></b>
                    </legend>
                    <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tbody>
                            <tr>
                                <td width="35%" class="hd1">
                                  <th:block th:text="#{'L999M01AS37.title01'}"><!--  動用期間--></th:block>&nbsp;&nbsp;
                                </td>
                                <td width="65%">
                                	<th:block th:text="#{'L999M01AS37.content01'}"><!--  自民國--></th:block>
									<input type="text" name="useSDateY" id="useSDateY" size="3" maxlength="3" integer="3"  class="numeric" onblur="addommon(this,3);" />
                                	<th:block th:text="#{'L999M01ASCOMMON.year'}"><!--  年--></th:block>
									<input type="text" name="useSDateM" id="useSDateM" size="2" maxlength="2" integer="2"  class="numeric" onblur="addommon(this,2);" />
                                	<th:block th:text="#{'L999M01ASCOMMON.month'}"><!--  月--></th:block>
									<input type="text" name="useSDateD" id="useSDateD" size="2" maxlength="2" integer="2"  class="numeric" onblur="addommon(this,2);" />
                                	<th:block th:text="#{'L999M01ASCOMMON.date'}"><!--  日--></th:block>&nbsp;
                                	<th:block th:text="#{'L999M01AS37.content02'}"><!--  起至民國--></th:block>
									<input type="text" name="useEDateY" id="useEDateY" size="3" maxlength="3" integer="3"  class="numeric" onblur="addommon(this,3);" />
                                	<th:block th:text="#{'L999M01ASCOMMON.year'}"><!--  年--></th:block>
									<input type="text" name="useEDateM" id="useEDateM" size="2" maxlength="2" integer="2"  class="numeric" onblur="addommon(this,2);" />
                                	<th:block th:text="#{'L999M01ASCOMMON.month'}"><!--  月--></th:block>
									<input type="text" name="useEDateD" id="useEDateD" size="2" maxlength="2" integer="2"  class="numeric" onblur="addommon(this,2);" />
                                	<th:block th:text="#{'L999M01ASCOMMON.date'}"><!--  日--></th:block>
                                	<th:block th:text="#{'L999M01AS37.content03'}"><!--  止--></th:block>
                                </td>
                            </tr>
							<tr>
                                <td width="35%" class="hd1">
                                  <th:block th:text="#{'L999M01AS37.title04'}"><!--額度--></th:block>&nbsp;&nbsp;
                                </td>
                                <td width="65%">
                                	<th:block th:text="#{'L999M01AS37.content05'}"><!--美金--></th:block>
									<input type="text" name="totLoanAmt" id="totLoanAmt" size="10" maxlength="60" maxlengthC="20" />
                                	<th:block th:text="#{'L999M01AS37.content06'}"><!--元或等值其他外幣--></th:block>
                                </td>
                            </tr>
							<tr>
                                <td width="35%" class="hd1">
                                  <th:block th:text="#{'L999M01AS37.title05'}"><!--提供保證金--></th:block>&nbsp;&nbsp;
                                </td>
                                <td width="65%">
                                	<th:block th:text="#{'L999M01AS37.content07'}"><!--按信用狀金額、幣別百分之--></th:block>
									<input type="text" name="guaPercent" id="guaPercent" size="3" maxlength="30" maxlengthC="10" />
                                	<th:block th:text="#{'L999M01AS37.content08'}"><!--提供保證金--></th:block>
                                </td>
                            </tr>
                            <tr>
                                <td width="35%" class="hd1" rowspan=2>
                                  <th:block th:text="#{'L999M01AS37.title02'}"><!--  資料保密--></th:block>&nbsp;&nbsp;
                                </td>
                                <td width="65%">
                                	<th:block th:text="#{'L999M01ASCOMMON.content04'}"><!--立約人--></th:block>
									<label><input type="radio" id="dataUseFlagY" name="dataUseFlag" value="Y"/>
                                	<th:block th:text="#{'L999M01ASCOMMON.content02'}"><!--  同意--></th:block></label>&nbsp;
									<label><input type="radio" id="dataUseFlagN" name="dataUseFlag" value="N"/>
                                	<th:block th:text="#{'L999M01ASCOMMON.content03'}"><!--  不同意--></th:block></label>&nbsp;
                                	<th:block th:text="#{'L999M01AS37.content04'}"><!--在貴行所屬之金融控股公司及其各子公司之客戶資料保密措施下， 貴行得將其持有...--></th:block>
									<br/>
                                	<input type="checkbox" id="dataUseItem" name="dataUseItem"/>
                                </td>
							</tr>
							<tr>
								<td width="65%">
	                            	<th:block th:text="#{'L999M01ASCOMMON.content01'}"><!--連保人--></th:block>
									<label><input type="radio" id="dataUseFlagYGua" name="dataUseFlagGua" value="Y"/>
	                            	<th:block th:text="#{'L999M01ASCOMMON.content02'}"><!--  同意--></th:block></label>&nbsp;
									<label><input type="radio" id="dataUseFlagNGua" name="dataUseFlagGua" value="N"/>
	                            	<th:block th:text="#{'L999M01ASCOMMON.content03'}"><!--  不同意--></th:block></label>&nbsp;
	                            	<th:block th:text="#{'L999M01AS37.content04'}"><!--  在貴行所屬之金融控股公司及其各子公司之客戶資料保密措施下， 貴行得將其持有...--></th:block>
									<br/>
	                            	<input type="checkbox" id="dataUseItemGua" name="dataUseItemGua"/>
								</td>
                            </tr>
							
							<tr>
                                <td width="35%" class="hd1">
                                  <th:block th:text="#{'L999M01AS37.title06'}"><!--前次立約日期--></th:block>&nbsp;&nbsp;
                                </td>
                                <td width="65%">
                                	<th:block th:text="#{'L999M01ASCOMMON.yearType'}"><!--  民國--></th:block>
									<input type="text" name="pSignDateY" id="pSignDateY" size="3" maxlength="3" integer="3"  class="numeric" onblur="addommon(this,3);" />
                                	<th:block th:text="#{'L999M01ASCOMMON.year'}"><!--  年--></th:block>
									<input type="text" name="pSignDateM" id="pSignDateM" size="2" maxlength="2" integer="2"  class="numeric" onblur="addommon(this,2);" />
                                	<th:block th:text="#{'L999M01ASCOMMON.month'}"><!--  月--></th:block>
									<input type="text" name="pSignDateD" id="pSignDateD" size="2" maxlength="2" integer="2"  class="numeric" onblur="addommon(this,2);" />
                                	<th:block th:text="#{'L999M01ASCOMMON.date'}"><!--  日--></th:block>&nbsp;
                                </td>
                            </tr>
                            <tr>
                                <td width="35%" class="hd1">
                                  <th:block th:text="#{'L999M01AS37.title07'}"><!--前次開發信用狀字號--></th:block>&nbsp;&nbsp;
                                </td>
                                <td width="65%">
                                	<input name="pContractWord" id="pContractWord" type="text" size="15" maxlength="30"/>
									<th:block th:text="#{'L999M01AM09.showTitle0301'}"><!--字第 --></th:block>
									<input name="pContractNo" id="pContractNo" type="text" size="20" class="alphanum" maxlength="20"/>
									<th:block th:text="#{'L999M01AM09.showTitle0302'}"><!--號 --></th:block>
                                </td>
                            </tr> 
                        </tbody>
                    </table>
                </fieldset>
		</th:block>
    </body>
</html>
