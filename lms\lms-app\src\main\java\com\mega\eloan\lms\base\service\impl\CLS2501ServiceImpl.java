/* 
 * LMS140MServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.service.impl;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.dao.DocFileDao;
import com.mega.eloan.common.enums.DocAuthTypeEnum;
import com.mega.eloan.common.enums.DocLogEnum;
import com.mega.eloan.common.gwclient.OBSMqGwClient;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.ClsUtility;
import com.mega.eloan.lms.base.common.MISRows;
import com.mega.eloan.lms.base.service.CLS2501Service;
import com.mega.eloan.lms.base.service.FlowNameService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dao.C101S01HDao;
import com.mega.eloan.lms.dao.C101S01UDao;
import com.mega.eloan.lms.dao.C250A01ADao;
import com.mega.eloan.lms.dao.C250M01ADao;
import com.mega.eloan.lms.dao.C250M01EDao;
import com.mega.eloan.lms.mfaloan.service.MisELF516Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C101S01H;
import com.mega.eloan.lms.model.C101S01U;
import com.mega.eloan.lms.model.C250A01A;
import com.mega.eloan.lms.model.C250M01A;
import com.mega.eloan.lms.model.C250M01E;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.core.FlowException;
import tw.com.jcs.flow.service.FlowService;

/**
 * <pre>
 * 可疑代辦案件註記作業
 */
@Service
public class CLS2501ServiceImpl extends AbstractCapService implements
		CLS2501Service {
	private static final Logger logger = LoggerFactory.getLogger(CLS2501ServiceImpl.class);

	@Resource
	FlowNameService flowNameService;
	@Resource
	TempDataService tempDataService;
	@Resource
	OBSMqGwClient obsMqGwClient;
	@Resource
	FlowService flowService;
	@Resource
	MisELF516Service misELF516Service;
	@Resource
	C250A01ADao c250a01aDao;
	@Resource
	C250M01ADao c250m01aDao;
	@Resource
	C250M01EDao c250m01eDao;	
	@Resource
	DocFileDao docFileDao;
	@Resource
	DocLogService docLogService;
	@Resource
	BranchService branchService;
	@Resource
	UserInfoService userInfoService;
	@Resource
	LMSService lmsService;
	@Resource
	DocFileService docFileService;
	@Resource
	CodeTypeService codeTypeService;
	@Resource
	MisdbBASEService misdbBaseService;
	@Resource
	MisdbBASEService misDbService;
	@Resource
	C101S01UDao c101s01uDao;
	@Resource
	C101S01HDao c101s01hDao;
 
	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		if (clazz == C250M01A.class) {
			return c250m01aDao.findByMainId(mainId);
		} else if (clazz == C250M01E.class) {
			return c250m01eDao.findByMainId(mainId);
		}
		return null;
	}

	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof C250M01A) {
					if (Util.isEmpty(((C250M01A) model).getOid())) {
						((C250M01A) model).setCreator(user.getUserId());
						((C250M01A) model).setCreateTime(CapDate.getCurrentTimestamp());
						c250m01aDao.save((C250M01A) model);
						
						flowService.start("CLS2501Flow", ((C250M01A) model).getOid(), user.getUserId(), user.getUnitNo());						
						
						// 新增授權檔
						C250A01A c250a01a = new C250A01A();
						c250a01a.setAuthTime(CapDate.getCurrentTimestamp());
						c250a01a.setAuthType(DocAuthTypeEnum.MODIFY.getCode());
						c250a01a.setAuthUnit(user.getUnitNo());
						c250a01a.setMainId(((C250M01A) model).getMainId());
						c250a01a.setOwner(user.getUserId());
						c250a01a.setOwnUnit(user.getUnitNo());
						c250a01aDao.save(c250a01a);

					} else {
						// 當文件狀態為編製中時文件亂碼才變更

						((C250M01A) model).setUpdater(user.getUserId());
						((C250M01A) model).setUpdateTime(CapDate
								.getCurrentTimestamp());
						c250m01aDao.save((C250M01A) model);
						if (!"Y".equals(SimpleContextHolder.get(EloanConstants.TEMPSAVE_RUN))) {
							tempDataService.deleteByMainId(((C250M01A) model).getMainId());
							docLogService.record(((C250M01A) model).getOid(), DocLogEnum.SAVE);
						}
					}
				} else if (model instanceof C250M01E) {
					((C250M01E) model).setUpdater(user.getUserId());
					((C250M01E) model).setUpdateTime(CapDate.getCurrentTimestamp());
					c250m01eDao.save((C250M01E) model);
				}
			}
		}
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == C250M01A.class) {
			return c250m01aDao.findPage(search);
		}
		return null;
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		if (clazz == C250M01A.class) {
			return (T) c250m01aDao.findByOid(oid);
		}
		return null;
	}

	@Override
	public boolean deleteC250M01As(String[] oids) {
		boolean flag = false;
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<C250M01A> c250m01as = new ArrayList<C250M01A>();
		for (int i = 0, size = oids.length; i < size; i++) {
			C250M01A c250m01a = (C250M01A) findModelByOid(C250M01A.class,
					oids[i]);
			// 設定刪除並非直接刪除 ，只是標記刪除時間
			c250m01a.setDeletedTime(CapDate.getCurrentTimestamp());
			c250m01a.setUpdater(user.getUserId());
			c250m01as.add(c250m01a);
			docLogService.record(c250m01a.getOid(), DocLogEnum.DELETE);
		}
		if (!c250m01as.isEmpty()) {
			c250m01aDao.save(c250m01as);
			flag = true;
		}
		return flag;
	}

	@Override
	public void saveC250M01EList(List<C250M01E> list) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (C250M01E c250m01e : list) {
			c250m01e.setUpdater(user.getUserId());
			c250m01e.setUpdateTime(CapDate.getCurrentTimestamp());
		}
		c250m01eDao.save(list);
	}

	@Override
	public void flowAction(String mainOid, C250M01A model, boolean setResult,
			boolean resultType, boolean upMis) throws Throwable {
		// TODO 要補的
		// Properties pop =
		// MessageBundleScriptCreator.getComponentResource(LMS140MM01Page.class);

		try {
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			FlowInstance inst = flowService.createQuery().id(mainOid).query();
			if (inst == null) {
				inst = flowService.start("CLS2501Flow",
						((C250M01A) model).getOid(), user.getUserId(),
						user.getUnitNo());
			}
			if (setResult) {
				inst.setDeptId(user.getUnitNo());
				inst.setUserId(user.getUserId());
				// inst.setNextDept("200");
				// inst.setNextUser("nextTest")
				// resultType 控制前進還是後退
				// 當有先行動用的狀態 是到03O 非先行動用表示已完成 到05O
				inst.setAttribute("result", resultType ? "核准" : "退回");
				if (resultType) {
					save((C250M01A) model);
					if (upMis) {
						C250M01A c250m01a = (C250M01A) findModelByOid(C250M01A.class, mainOid);
						
						// 簽章欄檔取得人員職稱
						List<C250M01E> c250m01elist = c250m01eDao.findByMainId(c250m01a.getMainId());
						String custId = Util.trim(c250m01a.getCustId());
						// String sDate = CapDate.formatDate(new Date(), UtilConstants.DateFormat.YYYY_MM_DD); // 目前系統時間
						String apprId = "";
						String reCheckId = "";

						for (C250M01E c250m01e : c250m01elist) {
							String StaffJob = Util.trim(c250m01e.getStaffJob());// 取得人員職稱
							String StaffNo = Util.trim(c250m01e.getStaffNo());// 取得行員代碼
							if (Util.equals(StaffJob, "L1")) {// 分行經辦
								apprId = StaffNo;
							} else if (Util.equals(StaffJob, "L4")) {// 分行覆核主管
								reCheckId = StaffNo;
							}
						}
						// 若人員職稱為空值改取c160m01a上的人員資料
						if (Util.isEmpty(apprId)) {
							apprId = c250m01a.getUpdater();
						}
						if (Util.isEmpty(reCheckId)) {
							reCheckId = c250m01a.getApprover();
						}
						
						if (custId.length() == 10) {
							this.upELF516(c250m01a, apprId, reCheckId);
						}
					}
				}
			}
			inst.next();

		} catch (FlowException e) {
			Throwable t1 = e;
			while (t1.getCause() != null) {
				t1 = t1.getCause();
			}
			throw t1;
		}
	}

	private <T> void up_DelThenInsert(MISRows<T> misRows, String schemaName) {
		if (Util.isNotEmpty(misRows.getKeyValues())) {
			int DelCount = misdbBaseService.delete(
					misRows.getKeyMsgFmtParam(schemaName),
					misRows.getKeyValues());
			logger.info("{}=======>{}", misRows.getTableNm(), "Delete:"
					+ DelCount);
			misdbBaseService.insert(misRows.getMsgFmtParam(schemaName),
					misRows.getTypes(), misRows.getValues());
			logger.info("{}=======>{}", misRows.getTableNm(), "Insert");
		} else {
			logger.error("{}=======>{}", misRows.getTableNm(), "EMPTY");
		}
	}

	@Override
	public C250M01E findC250M01E(String mainId, String branchType,
			String branchId, String staffNo, String staffJob) {
		return c250m01eDao.findByUniqueKey(mainId, branchType, branchId,
				staffNo, staffJob);
	}

	@Override
	public void deleteC250M01Es(List<C250M01E> c250m01es, boolean isAll) {
		if (isAll) {
			c250m01eDao.delete(c250m01es);
		} else {
			List<C250M01E> C250M01EsOld = new ArrayList<C250M01E>();
			for (C250M01E c250m01e : c250m01es) {
				String staffJob = c250m01e.getStaffJob();
				if (!("L6".equals(staffJob) || "L7".equals(staffJob))) {
					C250M01EsOld.add(c250m01e);
				}
			}
			c250m01eDao.delete(C250M01EsOld);
		}

	}

	@Override
	public void delete(GenericBean... entity) {
		// Auto-generated method stub(千萬不能刪掉)

	}

	/**
	 * 組上傳ELF516的值
	 * */
	private void  upELF516(C250M01A c250m01a, String apprId, String reCheckId) {
		
		String yyyyMM = "";	// 資料年月
		String cntrNo = ""; // 額度序號
		String LNFLAG = ""; // 疑似代辦案件訊息
		String OTHERMEMO = ""; // 其他可疑情形
		String loanNo = "";
		String branchComm = "";
		String otherDesc = ""; //其他情形

		yyyyMM = Util.trim(c250m01a.getYyyymm());
		cntrNo = Util.trim(c250m01a.getCntrNo());
		LNFLAG = Util.trim(c250m01a.getLnflag());
		loanNo = Util.trim(c250m01a.getLoanNo());		
		//M-110-0006 因需將 MIS.ELF516 匯出，上傳時將「換行字元」改為「空白」
		branchComm = (Util.trimSizeInOS390(Util.trim(ClsUtility.commons_lang3_StringUtils_normalizeSpace(c250m01a.getBranchComm())), 400));
		otherDesc = c250m01a.getOtherDesc();
			
		if ("D".equals(LNFLAG)){	
			OTHERMEMO = Util.trim(ClsUtility.commons_lang3_StringUtils_normalizeSpace(c250m01a.getOthermemo()));
			OTHERMEMO = (Util.trimSizeInOS390(OTHERMEMO, 202));
		}

		String updater = Util.getRightStr(apprId, 5);// 櫃員代號
		String approver = Util.getRightStr(reCheckId, 5);// 主管代號
		misELF516Service.updateC250M01A(LNFLAG, OTHERMEMO, loanNo, updater, approver, branchComm, otherDesc, yyyyMM, cntrNo);
	}
	
	@Override
	public void saveC250M01AForBatchCheck(C250M01A model, String userId, String unitNo){
		
		((C250M01A) model).setCreator(userId);
		((C250M01A) model).setCreateTime(CapDate.getCurrentTimestamp());
		c250m01aDao.save((C250M01A) model);
		
		flowService.start("CLS2501Flow", ((C250M01A) model).getOid(), userId, unitNo);						
		
		// 新增授權檔
		C250A01A c250a01a = new C250A01A();
		c250a01a.setAuthTime(CapDate.getCurrentTimestamp());
		c250a01a.setAuthType(DocAuthTypeEnum.MODIFY.getCode());
		c250a01a.setAuthUnit(unitNo);
		c250a01a.setMainId(((C250M01A) model).getMainId());
		c250a01a.setOwner(userId);
		c250a01a.setOwnUnit(unitNo);
		c250a01aDao.save(c250a01a);
	}
	
	@Override
	public String getEjcicB29InquiryData(String c250m01a_oid){
		
		C250M01A c250m01a = this.c250m01aDao.findByOid(c250m01a_oid);
		
		String b29SrcFileName = c250m01a.getB29SrcFileName();
		String b29SrcOid = c250m01a.getB29SrcOid();
							 
		Object obj = "C101S01U".equals(b29SrcFileName) 
				   ? this.c101s01uDao.findByOid(b29SrcOid)
				   : this.c101s01hDao.findByOid(b29SrcOid);
		
		if(obj != null){
			return "C101S01U".equals(b29SrcFileName) ? ((C101S01U)obj).getHtmlData() : ((C101S01H)obj).getHtmlData();
		}
		
		return "";
	}
	
	@Override
	public List<Map<String, String>> getOverduePaymentRecordByC250M01A(String oid) throws JsonParseException, JsonMappingException, IOException{
		
		C250M01A c250m01a = c250m01aDao.findByOid(oid);
		String overdueJson = c250m01a.getOverDueJson();
		
		if(StringUtils.isNotBlank(overdueJson)){
			
			@SuppressWarnings("rawtypes")
			List<Map> lista = Arrays.asList(new ObjectMapper().readValue(overdueJson, Map[].class));
			List<Map<String, String>> rtnList = new ArrayList<Map<String, String>>();
			for(Map<String, String> m : lista){
				rtnList.add(m);
			}
			
			return rtnList;
		}
		
		return new ArrayList<Map<String, String>>();
	}
}
