package com.mega.eloan.lms.fms.report.impl;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.service.AbstractReportService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.jcs.common.Arithmetic;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.ReportGenerator;

/**
 * <pre>
 * 產生試算PDF
 * </pre>
 * 
 * @since 2017/07/17
 * <AUTHOR>
 * @version <ul>
 *          <li>2017/07/17, EL08034
 *          </ul>
 */
@Service("lms9091r03rptservice")
public class LMS9091R03RptServiceImpl extends AbstractReportService {

	@Override
	public String getReportTemplateFileName() {
		return "report/fms/LMS9091R03_" + LMSUtil.getLocale().toString()+ ".rpt";
	}

	@Override
	public void setReportData(ReportGenerator generator,
			PageParameters params) throws CapException, ParseException {
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();

		try {
			String errMsg = "";
			List<String> errMsg_list = new ArrayList<String>();
			BigDecimal loanAmt = CrsUtil.parseBigDecimal(params
					.getAsDouble("loanAmt"));
			Integer lnYear = Util.parseInt(params.getAsInteger("lnYear"));
			Integer lnMonth = Util.parseInt(params.getAsInteger("lnMonth"));
			BigDecimal rate = CrsUtil.parseBigDecimal(params
					.getAsDouble("rate"));
			BigDecimal cmsVal = CrsUtil.parseBigDecimal(params
					.getAsDouble("cmsVal"));
			
			Integer monthCnt = lnYear * 12 + lnMonth;
			BigDecimal monthAmt = LMSUtil.prod_rmRctAmt_upperLimit(loanAmt, monthCnt);
			BigDecimal msg_monthAmt = monthAmt;
			boolean is_remainder_ok = true;
			
			if(monthCnt==0){
				errMsg_list.add("期數  需>0");
			}else{
				msg_monthAmt = Arithmetic.div(loanAmt, new BigDecimal(monthCnt), 2);
				is_remainder_ok = (loanAmt.remainder(new BigDecimal(monthCnt)).compareTo(BigDecimal.ZERO)==0);
				
				if(is_remainder_ok==false){
					errMsg_list.add("總貸款金額 需為 期數 的倍數(每月撥款金額 需為整數)");
				}	
			}
			BigDecimal monthIntMax = LMSUtil.prod_rmIntMax(monthAmt);

			rptVariableMap.put("loanAmt", ui_num(loanAmt));
			rptVariableMap.put("lnYear", Util.trim(lnYear));
			rptVariableMap.put("lnMonth", Util.trim(lnMonth));
			rptVariableMap.put("rate", ui_num(rate));
			rptVariableMap.put("cmsVal", ui_num(cmsVal));
			
			rptVariableMap.put("monthCnt", Util.trim(monthCnt));
			rptVariableMap.put("monthAmt", ui_num(msg_monthAmt));
			rptVariableMap.put("monthIntMax", ui_num(monthIntMax));
			if(errMsg_list.size()>0){
				errMsg = "錯誤："+StringUtils.join(errMsg_list, " , ");
			}
			rptVariableMap.put("errMsg", errMsg);
			// ==========
			Locale locale = LMSUtil.getLocale();
			generator.setLang(locale);
			
			BigDecimal totalRealGetAmount = BigDecimal.ZERO;
			List<Map<String, String>> listData = listData(loanAmt, rate, monthCnt, monthAmt, monthIntMax, cmsVal);
			for(Map<String, String> map : listData){
				BigDecimal realGetAmount = LMSUtil.nullToZeroBigDecimal(map.get("ReportBean.column07").replaceAll(",", ""));
				totalRealGetAmount = totalRealGetAmount.add(realGetAmount);
			}
			rptVariableMap.put("totalRealGetAmount", ui_num(totalRealGetAmount));
			
			generator.setRowsData(listData);
			generator.setVariableData(rptVariableMap);


		} finally {

		}
	}

	private String ui_num(BigDecimal v) {
		return NumConverter.addComma(LMSUtil.pretty_numStr(v));
	}
	
	private List<Map<String, String>> listData(BigDecimal loanAmt,
			BigDecimal rate, Integer monthCnt, BigDecimal default_amt,
			BigDecimal monthIntMax, BigDecimal cmsVal) {

		List<Map<String, String>> result = new LinkedList<Map<String, String>>();

		BigDecimal current_monthAmt = default_amt;

		BigDecimal v_1200 = new BigDecimal(1200);
		BigDecimal last_sum_loanAmt = BigDecimal.ZERO;
		BigDecimal last_sum_int_hold = BigDecimal.ZERO;
		BigDecimal sum_loanAmt = BigDecimal.ZERO;
		BigDecimal sum_int_hold = BigDecimal.ZERO;
		for (int period = 1; period <= monthCnt; period++) {
			//a+b>c
			if(current_monthAmt.add(last_sum_loanAmt).add(last_sum_int_hold).compareTo(cmsVal)>0){
				current_monthAmt = cmsVal.subtract(last_sum_loanAmt).subtract(last_sum_int_hold);
				if(current_monthAmt.compareTo(BigDecimal.ZERO)<0){
					current_monthAmt = BigDecimal.ZERO;
				}
			}
			BigDecimal period_int = Arithmetic.div(last_sum_loanAmt.multiply(rate), v_1200, 0);
			BigDecimal m_int_charge = BigDecimal.ZERO;
			BigDecimal m_int_hold = BigDecimal.ZERO;

			if (period_int.compareTo(monthIntMax) > 0) {
				m_int_charge = monthIntMax;
			} else {
				m_int_charge = period_int;
			}
			BigDecimal real_get_amt = current_monthAmt.subtract(m_int_charge);
			if(real_get_amt.compareTo(BigDecimal.ZERO)<0){
				//全部掛帳
				if(true){					
					m_int_charge = BigDecimal.ZERO;
				}
				
//				real_get_amt = BigDecimal.ZERO;
				real_get_amt = current_monthAmt.subtract(m_int_charge);
			}
			
			if(true){
				m_int_hold = period_int.subtract(m_int_charge);
			}
			
			sum_loanAmt = sum_loanAmt.add(current_monthAmt);
			sum_int_hold = sum_int_hold.add(m_int_hold);
			
			// ==============
			Map<String, String> map = Util.setColumnMap(15);
			map.put("ReportBean.column01", String.valueOf(period));
			map.put("ReportBean.column02", ui_num(current_monthAmt));
			map.put("ReportBean.column03", ui_num(sum_loanAmt));
			map.put("ReportBean.column04", ui_num(m_int_charge));
			map.put("ReportBean.column05", ui_num(m_int_hold));
			map.put("ReportBean.column06", ui_num(sum_int_hold));			
			map.put("ReportBean.column07", ui_num(real_get_amt));
			// =========
			result.add(map);
			// =========
			last_sum_loanAmt  = sum_loanAmt;
			last_sum_int_hold = sum_int_hold;
//			if(current_monthAmt.compareTo(default_amt)!=0){
//				break;
//			}			
		}

		return result;
	}
	
}
