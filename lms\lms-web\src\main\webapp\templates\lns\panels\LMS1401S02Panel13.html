<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
<body>
	<th:block th:fragment="LMS1401S02Panel13">
		<div id="tabs-c" class="tabs">
			<ul>
				<li id="tab13_1">
					<a href="#tabs-c13">
						<b><th:block th:text="#{'L140S02Tab.13_01'}"><!--本案無追索買方額度資訊--></th:block></b>
					</a>
				</li>
				<li id="tab13_3">
					<a href="#tabs-c13_3">
						<b><th:block th:text="#{'L140S02Tab.13_03'}"><!--本案無追索買方於本行額度彙總--></th:block></b>
					</a>
				</li>
			</ul>
			<div class="tabCtx-warp">
				<!-- 請在tab content 外加上 div.tabCtx-warp -->
				<div id="tabs-c13" class="content">
					<table class="tb2" width="920px" border="0" cellpadding="0" cellspacing="0">
						<tr class="hd1" style="text-align:left; margin-top:0px; padding-top:0px;">
							<td>
								<button type="button" id="newItemChildren1Bt13_1" class="noHideBt">
									<span class="text-only">
										<th:block th:text="#{'button.add'}"><!--新增--></th:block>
									</span>
								</button>
								<button type="button" id="removeGridviewitemChildren13_1" class="noHideBt">
									<span class="text-only">
										<th:block th:text="#{'button.delete'}"><!--刪除--></th:block>
									</span>
								</button>
								<button type="button" id="importExcel13_1" class="noHideBt">
									<span class="text-only">
										<th:block th:text="#{'btn.importExcel'}"><!--匯入EXCEL--></th:block>
									</span>
								</button>
								<button type="button" id="countTotal13_1" class="noHideBt">
									<span class="text-only">
										<th:block th:text="#{'btn.countTotal'}"><!--計算合計--></th:block>
									</span>
								</button>
								<button type="button" id="applyARAppendix13_B" class="noHideBt">
									<span class="text-only">
										<th:block th:text="#{'btn.applyARAppendix'}"><!--引進附表資料--></th:block>
									</span>
								</button>
							</td>
						</tr>
						<tr>
							<td>
								<div id="gridviewitemChildren_13"></div>
							</td>
						</tr>
						<tr class="hd1" style="text-align:left; margin-top:0px; padding-top:0px;">
							<td>
								<!--參考匯率-->
								<span id="refRate13_1" class="field"></span>
							</td>
						</tr>
					</table>
					<textarea cols="100" rows="10%" id="itemDscrH" name="itemDscrH" class="tckeditor" showType="b" t_width="800" t_height="500" showNewLineMessage="Y" distanceWord="44" preview="width:800;height:300"></textarea>
				</div>
				<!--end tabs-c01-->
				<div id="tabs-c13_3" class="content">
					<table class="tb2" width="920px" border="0" cellpadding="0" cellspacing="0">
						<tr class="hd1">
							<td style="text-align:left; margin-top:0px; padding-top:0px;">
								<th:block th:text="#{'L140M02S.dataDate'}">資料日期</th:block>&nbsp;&nbsp;：
								<span id="dataDate13_3" class="field"></span>&nbsp;&nbsp;
							</td>
							<td>
								<th:block th:text="#{'L140M02S.unit'}">單位</th:block>&nbsp;&nbsp;：
								<th:block th:text="#{'L140M02S.twdNt'}">TWD 元</th:block>&nbsp;&nbsp;
							</td>
						</tr>
						<tr class="hd1">
							<td colspan='2'>
								<div id="gridviewitemChildren3_13"></div>
							</td>
						</tr>
						<tr class="hd1" style="text-align:left; margin-top:0px; padding-top:0px;">
							<td colspan='2'>
								<!--參考匯率-->
								<span id="refRate13_3" class="field"></span>
							</td>
						</tr>
					</table>
				</div><!--end tabs-c01_3-->
			</div><!--end class tabCtx-warp-->
		</div><!--end tabs-c-->

		<div id="newItemChildrenBox1_13" style="display:none;"><!-- 登錄本案無追索買方額度資訊 thinkBox -->
			<form id="L140M01SForm1" name="L140M01SForm1">
				<table width="100%" class="tb2" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td width="30%" class="hd1">
							<th:block th:text="#{'L140M01S.itemSeq'}">序號</th:block>&nbsp;&nbsp;
						</td>
						<td width="70%">
							<span id="itemSeq" class="field"></span>
						</td>
					</tr>
					<tr>
						<td width="30%" class="hd1">
							<th:block th:text="#{'L140M01S.custId'}">統一編號</th:block>&nbsp;&nbsp;
						</td>
						<td width="70%">
							<input type="text" id="custId" name="custId" class="upText required" maxlength="10"></input>
						</td>
					</tr>
					<tr>
						<td width="30%" class="hd1">
							<th:block th:text="#{'L140M01S.dupNo'}">重覆序號</th:block>&nbsp;&nbsp;
						</td>
						<td width="70%">
							<input type="text" id="dupNo" name="dupNo" maxlength="1" size="5" class="required"></input>
						</td>
					</tr>
					<tr>
						<td width="30%" class="hd1">
							<th:block th:text="#{'L140M01S.custName'}">客戶名稱</th:block>&nbsp;&nbsp;
						</td>
						<td width="70%">
							<input type="text" id="custName" name="custName" class="required halfText" size="100" maxlength="120" maxlengthC="40"></input><br>
							<button type="button" id="btnApplyCustName13" onClick="applyCustName13()">
								<span class="text-only">
									<th:block th:text="#{'btn.retoContent'}">重新引進</th:block>
								</span>
							</button>
						</td>
					</tr>
					<tr>
						<td class="hd1">
							<th:block th:text="#{'L140M01S.factAmt'}">額度金額</th:block>&nbsp;&nbsp;
						</td>
						<td>
							<select id="curr" name="curr" class="money nodisabled" combokey="Common_Currcy" required></select>
							<input type="text" id="factAmt" name="factAmt" class="numeric required number" size="19" maxlength="22" integer="13" fraction="2"></input>
							<th:block th:text="#{'other.money'}"><!--元--></th:block>
						</td>
					</tr>
					<tr>
						<td width="30%" class="hd1">
							<th:block th:text="#{'L140M01S.custId2'}">共用統一編號</th:block>&nbsp;&nbsp;
						</td>
						<td width="70%">
							<input type="text" id="custId2" name="custId2" class="upText" maxlength="10"></input>
							<th:block th:text="#{'L140M01S.dupNo'}">重覆序號</th:block>&nbsp;&nbsp;
							<input type="text" id="dupNo2" name="dupNo2" maxlength="1" size="5"></input>
						</td>
					</tr>
					<tr>
						<td width="30%" class="hd1">
							<th:block th:text="#{'L140M01S.ratio'}">預支成數（%）</th:block>&nbsp;&nbsp;
						</td>
						<td width="70%">
							<input type="text" id="ratio" name="ratio" class="numeric number" size="19" maxlength="6" integer="3" fraction="2"></input>
						</td>
					</tr>
					<tr>
						<td class="hd1">
							<th:block th:text="#{'L140M01S.country'}">國別</th:block>&nbsp;&nbsp;
						</td>
						<td>
							<select id="country" name="country" combokey="CountryCode" combotype="4" space="true"></select><br>&nbsp;
						</td>
					</tr>
					<tr>
						<td width="30%" class="hd1">
							<th:block th:text="#{'L140M01S.memo1'}">備註1</th:block>&nbsp;&nbsp;
						</td>
						<td width="70%">
							<textarea id="memo1" name="memo1" cols="60" rows="5"></textarea>
						</td>
					</tr>
					<tr>
						<td width="30%" class="hd1">
							<th:block th:text="#{'L140M01S.memo2'}">備註2</th:block>&nbsp;&nbsp;
						</td>
						<td width="70%">
							<textarea id="memo2" name="memo2" cols="60" rows="5"></textarea>
						</td>
					</tr>
				</table>
			</form>
		</div><!-- 登錄本案無追索買方額度資訊 thinkBox END-->

		<div id="loginImportByExl13_1" style="display:none; margin-top:5px;">
			<table border="1" cellpadding="0" cellspacing="0">
				<tr>
					<td>
						<input type="file" id="uploadFileAml" name="uploadFileAml" class="required"></input>
					</td>
					<td>
						※點選右方連結下載EXCEL範本檔:
						<a th:href="@{/img/lms/ARBuyer_01.xls}" target="_blank">
							<th:block th:text="#{'L140M01S.excelDownload'}">下載</th:block>
						</a><br>
						EXCEL格式說明:<br>
						1.欄位說明請參考EXCEL範本檔<br>
						2.EXCEL檔案副檔名必須為.xls。<br>
						3.匯入EXCEL檔案時自第二列開始匯入，第一列預設為標題。<br>
						4.匯入之EXCEL系統不留存，請自行保留。<br>
					</td>
				</tr>
			</table>
		</div>
	</th:block>
</body>
</html>
