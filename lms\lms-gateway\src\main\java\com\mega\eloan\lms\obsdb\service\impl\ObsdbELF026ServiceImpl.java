package com.mega.eloan.lms.obsdb.service.impl;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import com.mega.eloan.common.jdbc.AbstractOBSDBJdbcFactory;
import com.mega.eloan.lms.obsdb.service.ObsdbELF026Service;
import org.springframework.stereotype.Service;



/**
 * <pre>
 * 海外泰行個金徵信	ELF026-泰行個金徵信評等檔
 * </pre>
 */
@Service
public class ObsdbELF026ServiceImpl extends AbstractOBSDBJdbcFactory implements
		ObsdbELF026Service {

	// 簽報書覆核用，將評等資料寫入AS400
	@Override
	public void insertForInside(String br_cd, String cust_key, String subjcode, String cntrno, int chkdate, 
			String custid, String dupno, String noteid, String final_rating_flag,
			int fr, int rating_date, String rating_id, String loan_code, String mowtype,
			int mowver1, int mowver2, BigDecimal loan_period) {

		this.getJdbc(br_cd).update(
				"ELF026.insertForInside",
				new Object[] {br_cd, cust_key, subjcode, cntrno, chkdate, custid, dupno, noteid,
						final_rating_flag, fr, rating_date, rating_id, loan_code, mowtype, mowver1, mowver2, loan_period});

	}
	
	
	// 刪除AS400評等資料
	@Override
	public void delByKey(String br_cd, String cust_key, String subjcode, String cntrno, int chkdate, 
			String custid, String dupno, String noteid) {

		this.getJdbc(br_cd).update(
				"ELF026.delByKey",
				new Object[] {br_cd, cust_key, subjcode, cntrno, chkdate, custid, dupno, noteid});

	}


}
