/* 
 * L120M01H.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Lob;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 授審會／催收會會議決議檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L120M01H", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId","meetingType"}))
public class L120M01H extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * 授審會/催收會<p/>
	 * 授審會|1<br/>
	 *  催收會|2
	 */
	@Column(name="MEETINGTYPE", length=1, columnDefinition="VARCHAR(1)")
	private String meetingType;

	/** 
	 * 案由<p/>
	 * 256個全型字
	 */
	@Column(name="GIST", length=4096, columnDefinition="VARCHAR(4096)")
	private String gist;

	/** 
	 * 決議<p/>
	 * 800個全型字
	 */
	@Column(name="DISPWORD", length=2400, columnDefinition="VARCHAR(2400)")
	private String dispWord;

	/** 
	 * 核准額度較前准
增、減金額<p/>
	 * 256個全型字
	 */
	@Column(name="QUOTADESRC", length=768, columnDefinition="VARCHAR(768)")
	private String quotaDesrc;

	/** 會議決議 **/
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(name="MEETINGNOTE", columnDefinition="CLOB")
	private String meetingNote;

	/** 建立人員號碼 **/
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Date updateTime;

	/**
	 * 負面意見<p/>
	 * 800個全型字
	 */
	@Column(name="NEGOPINION", length=2400, columnDefinition="VARCHAR(2400)")
	private String negOpinion;

	/**
	 * 同意理由<p/>
	 * 1365個全型字
	 */
	@Column(name="CONSENT", length=4096, columnDefinition="VARCHAR(4096)")
	private String consent;

	/**
	 * 另囑<p/>
	 * 1365個全型字
	 */
	@Column(name="ANOTHER", length=4096, columnDefinition="VARCHAR(4096)")
	private String another;

	/**
	 * 授權層級<p/>
	 * 256個全型字
	 */
	@Column(name="AUTHLVLSTR", length=768, columnDefinition="VARCHAR(768)")
	private String authLvlStr;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得授審會/催收會<p/>
	 * 授審會|1<br/>
	 *  催收會|2
	 */
	public String getMeetingType() {
		return this.meetingType;
	}
	/**
	 *  設定授審會/催收會<p/>
	 *  授審會|1<br/>
	 *  催收會|2
	 **/
	public void setMeetingType(String value) {
		this.meetingType = value;
	}

	/** 
	 * 取得案由<p/>
	 * 256個全型字
	 */
	public String getGist() {
		return this.gist;
	}
	/**
	 *  設定案由<p/>
	 *  256個全型字
	 **/
	public void setGist(String value) {
		this.gist = value;
	}

	/** 
	 * 取得決議<p/>
	 * 800個全型字
	 */
	public String getDispWord() {
		return this.dispWord;
	}
	/**
	 *  設定決議<p/>
	 *  800個全型字
	 **/
	public void setDispWord(String value) {
		this.dispWord = value;
	}

	/** 
	 * 取得核准額度較前准
增、減金額<p/>
	 * 256個全型字
	 */
	public String getQuotaDesrc() {
		return this.quotaDesrc;
	}
	/**
	 *  設定核准額度較前准
增、減金額<p/>
	 *  256個全型字
	 **/
	public void setQuotaDesrc(String value) {
		this.quotaDesrc = value;
	}

	/** 取得會議決議 **/
	public String getMeetingNote() {
		return this.meetingNote;
	}
	/** 設定會議決議 **/
	public void setMeetingNote(String value) {
		this.meetingNote = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}

	/**
	 * 取得負面意見<p/>
	 * 800個全型字
	 */
	public String getNegOpinion() {
		return this.negOpinion;
	}
	/**
	 *  設定負面意見<p/>
	 *  800個全型字
	 **/
	public void setNegOpinion(String value) {
		this.negOpinion = value;
	}

	/**
	 * 取得同意理由<p/>
	 * 1365個全型字
	 */
	public String getConsent() {
		return this.consent;
	}
	/**
	 *  設定同意理由<p/>
	 *  1365個全型字
	 **/
	public void setConsent(String value) {
		this.consent = value;
	}

	/**
	 * 取得另囑<p/>
	 * 1365個全型字
	 */
	public String getAnother() {
		return this.another;
	}
	/**
	 *  設定另囑<p/>
	 *  1365個全型字
	 **/
	public void setAnother(String value) {
		this.another = value;
	}

	/**
	 * 取得授權層級<p/>
	 * 256個全型字
	 */
	public String getAuthLvlStr() {
		return this.authLvlStr;
	}
	/**
	 *  設定授權層級<p/>
	 *  256個全型字
	 **/
	public void setAuthLvlStr(String value) {
		this.authLvlStr = value;
	}
}
