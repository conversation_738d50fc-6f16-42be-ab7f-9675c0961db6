package com.mega.eloan.lms.dc.thread;

import com.mega.eloan.lms.dc.action.DXLReject;
import com.mega.eloan.lms.dc.base.DCException;
import com.mega.eloan.lms.dc.conf.ConfigData;

/**
 * <pre>
 * RejectThread
 * </pre>
 * 
 * @since 2013/01/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/01/30,Bang,new
 *          </ul>
 */
public class RejectThread extends Thread {

	private String schema = "";
	private ConfigData config;

	public RejectThread() {
		super();
	}

	/**
	 * Constructor
	 * 
	 * @param schema
	 *            String:目前執行的系統名稱
	 * @param pps
	 *            :Properties
	 */
	public RejectThread(String schema) {
		this.schema = schema;
	}

	public void run() {
		DXLReject dr = new DXLReject();
		dr.setConfigData(config);
		dr.doReject(this.schema);
	}

	public void reject(ConfigData config) {
		try {
			this.setConfig(config);
			this.run();
		} catch (DCException e) {
			throw new DCException("Reject 時產生錯誤...", e);
		}
	}

	/**
	 * set the config
	 * 
	 * @param config
	 *            the config to set
	 */
	public void setConfig(ConfigData config) {
		if (config != null) {
			this.config = config;
		}
	}

}
