package com.mega.eloan.lms.fms.pages;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.common.panels.DocLogPanel;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.model.C360M01A;

import tw.com.jcs.auth.AuthType;


@Controller
@RequestMapping(path = "/fms/cls3601m02/{page}")
public class CLS3601M02Page extends AbstractEloanForm {

	@Autowired
	CLSService clsService;
	
	public CLS3601M02Page() {
		super();
	}

	@Override
	public void  execute(ModelMap model, PageParameters params) {	
		new DocLogPanel("_docLog").processPanelData(model, params);
		
//		String mainOid = params.getString(EloanConstants.MAIN_OID);
//		C360M01A c360m01a = clsService.findC360M01A_oid(mainOid);
						
		addAclLabel(model, new AclLabel("_btnSave", params, getDomainClass(),
				AuthType.Modify	, FlowDocStatusEnum.編製中));
		
		addAclLabel(model, new AclLabel("_btnWAIT_APPROVE", params, getDomainClass(),
				AuthType.Accept, false, FlowDocStatusEnum.待覆核));
		
		renderJsI18N(CLS3601M01Page.class);
	}

	@Override
	public Class<? extends Meta> getDomainClass() {		
		return C360M01A.class;
	}
}
