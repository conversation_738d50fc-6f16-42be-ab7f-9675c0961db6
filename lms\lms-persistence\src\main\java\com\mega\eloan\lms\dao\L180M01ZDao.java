/* 
 * L180M01ZDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L180M01Z;

/** 覆審名單主檔 **/
public interface L180M01ZDao extends IGenericDao<L180M01Z> {

	L180M01Z findByOid(String oid);
	
	List<L180M01Z> findByMainId(String mainId);
	
	L180M01Z findByUniqueKey(Date dataDate, String branchId);

	List<L180M01Z> findByIndex1Z(Date dataDate, String branchId);
}