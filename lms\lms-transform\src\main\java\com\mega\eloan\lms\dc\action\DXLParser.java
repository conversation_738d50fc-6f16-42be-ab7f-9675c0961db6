package com.mega.eloan.lms.dc.action;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.w3c.dom.Document;

import com.mega.eloan.cls.dc.action.ClsGetItemValue;
import com.mega.eloan.cls.dc.util.ClsDXLUtil;
import com.mega.eloan.lms.dc.base.DCException;
import com.mega.eloan.lms.dc.conf.ViewListConfig;
import com.mega.eloan.lms.dc.conf.XMLConfig;
import com.mega.eloan.lms.dc.util.DXLUtil;
import com.mega.eloan.lms.dc.util.TextDefine;
import com.mega.eloan.lms.dc.util.Util;

/**
 * <pre>
 * DXLParser
 * </pre>
 * 
 * @since 2012/12/20
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/20,Bang,new
 *          </ul>
 */
public class DXLParser extends ParserAction {
	private String schema = "";
	protected String mainForm = "";
	protected String logsDirPath = "";
	protected String dxlDirRootPath = "";
	protected String xmlRootPath = ""; // Lms db2xml檔根目錄路徑
	private String OldOutFile = "";// 藉以判斷是否輸出在同一txt檔
	private PrintWriter txtWrite;
	private PrintWriter parserlogs = null;// 輸出log
	private PrintWriter xmlErrorLogs = null;// db2Xml屬性欄位值判斷log
	private PrintWriter dxlErrorLst = null;// dxl踢退清單
	private String OldBranch = "";
	private String OldMF = "";
	private long tt1 = 0;
	private boolean attachFlag = true;// 是否處理附件 true:是

	private void init() {
		this.tt1 = System.currentTimeMillis();
		this.configData = this.getConfigData();
		this.userPath = this.configData.getUserPath();// User當前工作目錄
		this.richTextColumn = this.configData.getRichTxtColName();

		// 2013-01-28
		if (TextDefine.SCHEMA_LMS.equalsIgnoreCase(this.schema)) {
			this.mainForm = this.configData.getLMSMainForm();
			this.logsDirPath = this.configData.getLmsLogsDirPath();// User當前工作目錄\log\logs\執行日期\LMS
			this.dxlDirRootPath = this.configData.getLmsDxlDirRootPath();// homePath\today\LMS
			this.xmlRootPath = this.configData.getLmsXmlRootPath();// userPath\xml\LMS
			this.loadDB2ClobPath = this.configData.getLmsloadDB2DirPath()// User當前工作目錄\load_db2\執行日期\LMS\clob
					+ this.configData.getClobPath();
		} else {
			this.mainForm = this.configData.getCLSMainForm();
			this.logsDirPath = this.configData.getClsLogsDirPath();// User當前工作目錄\log\logs\執行日期\CLS
			this.dxlDirRootPath = this.configData.getClsDxlDirRootPath();// homePath\today\CLS
			this.xmlRootPath = this.configData.getClsXmlRootPath();// userPath\xml\CLS
			this.loadDB2ClobPath = this.configData.getClsloadDB2DirPath()// User當前工作目錄\load_db2\執行日期\CLS\clob
					+ this.configData.getClobPath();
		}
	}

	/**
	 * 初始化必要資訊及執行Parser動作
	 * 
	 * @param threadName
	 *            String
	 * @param schema
	 *            String:目前執行的系統名稱
	 * @param viewListName
	 *            String 2013-01-28 Modify By Bang:加入個金判斷
	 */
	public void doParser(String schema, String viewListName) {
		if (StringUtils.isBlank(schema)) {
			String errmsg = "讀取系統名稱錯誤,未指定要執行的系統名稱,請重新確認...";
			this.logger.error(errmsg);
			throw new DCException(errmsg);
		}
		final String threadName = Thread.currentThread().getName();

		this.schema = schema;

		long t1 = System.currentTimeMillis();
		try {
			this.logger.info("【" + threadName + "】正在初始化" + viewListName
					+ " DXLParser 必要資訊！");

			this.init();

			// 讀取viewList
			this.logger.info("++++++=" + viewListName);
			List<String> viewList = ViewListConfig.getInstance().getViewList(
					viewListName);
			for (int i = 0, size = viewList.size(); i < size; i++) {
				this.logger.info("【" + threadName + "】目前執行" + viewList.get(i));

				try {
					this.parse(viewList.get(i));
				} catch (Exception ex) {
					String errmsg = "執行DXLParser之 parse步驟 時產生錯誤，中斷VIEW_NAME=["
							+ viewList.get(i) + "]的執行作業！==>"
							+ ex.getLocalizedMessage();
					logger.error(errmsg, ex);

					if (this.parserlogs != null) {
						this.parserlogs.println(errmsg);
						ex.printStackTrace(this.parserlogs);
					}
				}
			}

			IOUtils.closeQuietly(txtWrite);
			IOUtils.closeQuietly(xmlErrorLogs);
			IOUtils.closeQuietly(dxlErrorLst);

		} catch (Exception e) {
			String errmsg = "DXLParser 初始化時產生錯誤...";
			this.logger.error(errmsg, e);
			throw new DCException(errmsg, e);
		} finally {
			long cost = System.currentTimeMillis() - t1;
			String msg = new StringBuffer()
					.append("【" + threadName + "】執行 DXLParser 結束時間 :")
					.append(Util.getNowTime()).append(" ,TOTAL TIME===> ")
					.append(Util.millis2minute(cost)).toString();
			this.logger.info(msg);
			if (this.parserlogs != null) {
				this.parserlogs.println(msg);
				IOUtils.closeQuietly(this.parserlogs);
			}
		}
	}

	@SuppressWarnings("unused")
	private void parse(String nsfViewData) {
		long t1 = System.currentTimeMillis();
		String[] str = nsfViewData.split(";");
		String strBrn = str[0].substring(2, 5);// 分行名稱,EX:201
		String viewName = str[1];// View Name ,EX:VLMSDB201B

		// 建立轉出時必要目錄: Branch \ viewName \ HTML & IMAGES &FILES
		String dxlPath = this.getDxlPath(viewName, strBrn);

		// 建立各份行logs
		if (!strBrn.equalsIgnoreCase(this.OldBranch)) {
			this.resetLoggerByBranch(strBrn, viewName);
		}

		try {
			// 讀取與Notes對應的DB2 XML檔並轉換成物件以便和.dxl檔合併.比對
			String[] mainForm = this.mainForm.split(";");// 用IO直接讀xml/LMS/files的方式反而更耗時
			String tmpLxf = "", oldTable = "";
			for (String mf : mainForm) {
				String xmlNamePath = this.xmlRootPath + File.separator + mf;
				String[] xmlNamelist = Util.getSameAttachFile(xmlNamePath,
						".xml");
				if (null == xmlNamelist || xmlNamelist.length == 0) {
					this.parserlogs.println("找不到對應的DB2 XML定義檔 :" + mf);
					continue;
				} else {
					Arrays.sort(xmlNamelist);
					for (String xmlName : xmlNamelist) {
						long t2 = System.currentTimeMillis();
						if (this.logger.isDebugEnabled()) {
							this.logger.debug("##### 處理 " + xmlName + "@"
									+ strBrn + "==BEGIN==");
						}

						String tableName = (xmlName.substring(xmlName
								.indexOf("_")));
						// 取得.dxl列表
						String[] dxllist = Util.getSameAttachFile(dxlPath,
								TextDefine.ATTACH_DXL);
						Arrays.sort(dxllist);
						int idx = 0;
						final int TOTAL_CNT = dxllist.length;
						long t9 = System.currentTimeMillis();
						// 讀取,處理及轉換.dxl
						for (String dxlName : dxllist) {
							// dxlName
							// Ex:FLMS140M01_1F7F7200F6E65C5148257A8A00270244
							String[] value = dxlName.split("_");
							// formName符合的再進來
							if (value[0].equalsIgnoreCase(mf)) {
								// formName相同只有TableName不同時不用再處理一次附件
								if (mf.equalsIgnoreCase(this.OldMF)) {
									if (!tableName.equals(oldTable)) {
										this.attachFlag = false;
									}
								} else {// formName不一樣時要處理附件
									this.attachFlag = true;
								}

								if (!mf.equalsIgnoreCase(this.OldMF)) {
									this.logger
											.info("開始執行【" + strBrn + "】分行 讀取【"
													+ mf + "】,處理及轉換.dxl...");
									this.OldMF = mf;
								}
								if (!xmlName.equalsIgnoreCase(tmpLxf)) {
									this.parserlogs.println("【" + strBrn
											+ "】分行當前Parser的Table Name : "
											+ xmlName);
									tmpLxf = xmlName;
								}
								parseDxl(xmlNamePath, xmlName, dxlPath,
										dxlName, strBrn);
								oldTable = tableName;
							}

							if ((++idx) % DXL_PROC_DEBUG_CNT == 0) {
								/*if (this.logger.isDebugEnabled()) {
									this.logger.debug("##### "
											+ FilenameUtils
													.getBaseName(xmlName)
											+ " 已轉筆數==> " + idx + " / "
											+ TOTAL_CNT + " ==> "
											+ (System.currentTimeMillis() - t9)
											+ " ms");
									t9 = System.currentTimeMillis();
								}*/
							}
						}
						if (this.logger.isDebugEnabled() && false) {
							this.logger.debug("##### 處理 " + xmlName + "@"
									+ strBrn + " COST==>"
									+ (System.currentTimeMillis() - t2));
						}
					}
				}
			}
		} catch (Exception ex) {
			String errmsg = new StringBuffer("處理【").append(strBrn)
					.append("】分行執行 讀取與Notes對應的DB2 XML檔 時產生錯誤:").toString();
			this.parserlogs.println(errmsg + "=>" + ex.getLocalizedMessage());
			this.xmlErrorLogs.println(errmsg + "=>" + ex.getLocalizedMessage());
			throw new DCException(errmsg, ex);

		} finally {
			this.logger.info("@@@@@@@@ [" + strBrn + "][" + viewName
					+ "] parse() TOTAL_COST = "
					+ (System.currentTimeMillis() - t1) + " ms ==> "
					+ nsfViewData);
		}
	}

	private void resetLoggerByBranch(String strBrn, String viewName) {
		if (this.parserlogs != null) {
			long cost = System.currentTimeMillis() - this.tt1;
			String msg = new StringBuffer().append("【").append(this.OldBranch)
					.append("】分行 執行 DXLParser 結束時間 :")
					.append(Util.getNowTime()).append(" ,TOTAL TIME===> ")
					.append(Util.millis2minute(cost)).toString();

			this.logger.info(msg);
			this.parserlogs.println(msg);

			IOUtils.closeQuietly(this.parserlogs);
			IOUtils.closeQuietly(this.xmlErrorLogs);
			IOUtils.closeQuietly(this.dxlErrorLst);

			logger.info("----------");
		}

		try {
			final String LOG_ROOT = this.logsDirPath + File.separator
					+ "PARSER";
			Util.checkDirExist(LOG_ROOT);

			String parserLogName = LOG_ROOT + File.separator
					+ TextDefine.LOG_PARSER + strBrn + "_" + viewName;

			String brnLogPath = parserLogName + TextDefine.ATTACH_LOG;
			this.parserlogs = new PrintWriter(new BufferedWriter(
					new OutputStreamWriter(new FileOutputStream(new File(
							brnLogPath)))), true);
			// 欄位值錯誤清單
			String errLogPath = parserLogName + ".err";
			this.xmlErrorLogs = new PrintWriter(new BufferedWriter(
					new OutputStreamWriter(new FileOutputStream(new File(
							errLogPath)))), true);

			// dxl踢退清單
			String dxlError = this.dxlDirRootPath + File.separator + strBrn
					+ File.separator + viewName + "_dxlError.lst";
			this.dxlErrorLst = new PrintWriter(new BufferedWriter(
					new OutputStreamWriter(new FileOutputStream(new File(
							dxlError)))), true);

			this.OldBranch = strBrn;

			this.parserlogs.println("【" + strBrn + "】分行DXLParser 起始時間 :"
					+ Util.getNowTime() + "\n");

			this.tt1 = System.currentTimeMillis();
		} catch (IOException ex) {
			String errmsg = "【" + strBrn + "】分行執行DXLParser 之parse步驟時產生錯誤！";
			if (this.parserlogs != null) {
				this.parserlogs.println(errmsg + "=>"
						+ ex.getLocalizedMessage());
			}
			if (this.xmlErrorLogs != null) {
				this.xmlErrorLogs.println(errmsg + "=>"
						+ ex.getLocalizedMessage());
			}
			throw new DCException(errmsg, ex);
		}
	}

	/**
	 * 讀取,處理及轉換.dxl
	 * 
	 * @param lmsXmlNamePath
	 *            String :當前notesForm對應之DB2Xml所在目錄位置
	 * @param xmlName
	 *            String :DB2Xml檔名稱
	 * @param dxlPath
	 *            String : .dxl檔存放路徑
	 * @param dxlName
	 *            :.dxl列表中的.dxl檔名
	 * @param strBrn
	 *            :分行名稱
	 */
	private void parseDxl(String lmsXmlNamePath, String xmlName,
			String dxlPath, String dxlName, String strBrn) {
		String dxlXml = "";
		try {
			// 轉成XML
			String[] splitDxlName = dxlName.split(TextDefine.ATTACH_DXL);

			// dxlName Ex:FLMS110M01_00D18FF3C1CABB6A4825775000073543.dxl
			dxlXml = this.readFile(dxlPath + File.separator + dxlName);
			Document domDoc = this.getDomDoc(dxlXml);

			// 同一FormName處理過就不再處理,否則formName下有幾個Table就會跑幾次
			if (this.attachFlag) {
				// 處理附件
				this.saveAttachments(domDoc, splitDxlName[0], this.schema);
				// 處理Rich Text欄位
				// String[] rtColList = this.richTextColumn
				// .split(TextDefine.SYMBOL_SEMICOLON);
				// for (String rtColName : rtColList) {
				// String richText = this.getRichText(dxlXml, rtColName);
				// if (StringUtils.isNotBlank(richText)) {
				// this.processRTF(richText, splitDxlName[0], rtColName,
				// strBrn);
				// }
				// }
			}
			// 讀取,處理及轉換DB2 XML (這裡應該可以另起Thread跑GetItemValue)
			String formName = (splitDxlName[0].split("_"))[0];
			String db2XmlName = (xmlName.split("_"))[0];
			if (formName.equalsIgnoreCase(db2XmlName)) {
				this.combineDxlDb2Xml(lmsXmlNamePath, xmlName, dxlPath,
						dxlName, dxlXml, strBrn, domDoc);
			}
		} catch (Exception e) {
			String errmsg = new StringBuffer().append("【").append(strBrn)
					.append("】分行執行DXLParser 之parseDxl時產生錯誤,dxl檔名:")
					.append(dxlName).append(" ,DB2Xml檔名稱 :").append(xmlName)
					.toString();
			throw new DCException(errmsg + "==>" + e.getLocalizedMessage(), e);
		}
	}

	/**
	 * 讀取,處理及轉換DB2 XML (這裡應該可以另起Thread跑GetItemValue)
	 * 
	 * @param lmsXmlNamePath
	 *            String :當前notesForm對應之DB2Xml所在目錄位置
	 * @param xmlName
	 *            String :DB2Xml檔名稱
	 * @param dxlPath
	 *            String : .dxl檔存放路徑
	 * @param dxlName
	 *            :.dxl列表中的.dxl檔名
	 * @param dxlXml
	 *            String :已轉換為String型態之dxl檔
	 * @param strBrn
	 *            String:分行名稱
	 * @param domDoc
	 *            DOM Document:已轉為DOM Document的.dxl檔
	 */
	private void combineDxlDb2Xml(String lmsXmlNamePath, String xmlName,
			String dxlPath, String dxlName, String dxlXml, String strBrn,
			Document domDoc) {

		ParserDB2XML db2Item = XMLConfig.getInstance().getDB2XmlBySysId(
				this.schema, xmlName);

		// 不同db2Xml時才需要重新產生txt
		String outFile = db2Item.getOutFile();
		String tmpFile = strBrn + "_" + outFile;
		if (!tmpFile.equalsIgnoreCase(this.OldOutFile)) {
			IOUtils.closeQuietly(txtWrite);
			try {
				this.txtWrite = new PrintWriter(new BufferedWriter(
						new OutputStreamWriter(new FileOutputStream(new File(
								this.textPath + File.separator + outFile)),
								TextDefine.ENCODING_UTF8)), true);
				this.OldOutFile = tmpFile;
			} catch (Exception ex) {
				String errmsg = new StringBuffer("【").append(strBrn)
						.append("】分行執行DXLParser 之combineDxlDb2Xml時產生錯誤,dxl檔名:")
						.append(dxlName).append(" ,DB2Xml檔名稱 :")
						.append(xmlName).append(ex.getLocalizedMessage())
						.toString();
				throw new DCException(errmsg, ex);
			}
		}

		if (TextDefine.SCHEMA_LMS.equalsIgnoreCase(this.schema)) {
			// 初始化DXLUtil資訊
			DXLUtil.init();
			// 開始合併
			DXLGetItemValue dgiv = new DXLGetItemValue();
			dgiv.mainProcess(this.txtWrite, db2Item, xmlName, dxlPath, dxlName,
					dxlXml, strBrn, domDoc, this.parserlogs, this.xmlErrorLogs,
					this.dxlErrorLst);
		} else {
			// 初始化ClsDXLUtil資訊
			ClsDXLUtil.init();
			// 開始合併
			ClsGetItemValue clsgiv = new ClsGetItemValue();
			clsgiv.mainProcess(this.txtWrite, db2Item, xmlName, dxlPath,
					dxlName, dxlXml, strBrn, domDoc, this.parserlogs,
					this.xmlErrorLogs, this.dxlErrorLst);

		}
	}

	protected String getDxlPath(String viewName, String strBrn) {
		// 建立轉出時必要目錄: Branch \ viewName \ HTML & IMAGES &FILES
		String dxlPath = this.dxlDirRootPath + File.separator + strBrn
				+ File.separator + viewName;

		this.htmlPath = dxlPath + this.configData.getHtmlPath();
		Util.checkDirExist(this.htmlPath);
		this.imagesPath = dxlPath + this.configData.getImagesPath();
		Util.checkDirExist(this.imagesPath);
		this.filesPath = dxlPath + this.configData.getFilesPath();
		Util.checkDirExist(this.filesPath);
		this.textPath = dxlPath + this.configData.getTextPath();
		Util.checkDirExist(textPath);
		// 2013-03-26 Add by Bang
		Util.checkDirExist(this.loadDB2ClobPath + File.separator + strBrn);

		return dxlPath;
	}

	/**
	 * 匯整所有.err檔成一份文字檔
	 */
	public void combineErr() {
		final String LOG_ROOT = this.logsDirPath + File.separator + "PARSER";
		try {
			String dtFile = LOG_ROOT + File.separator + TextDefine.LOG_PARSER
					+ "AllError";
			PrintWriter writeAllErr = new PrintWriter(new BufferedWriter(
					new OutputStreamWriter(new FileOutputStream(
							new File(dtFile)), TextDefine.ENCODING_MS950)),
					true);
			Collection<File> files = getLogRootDirList(LOG_ROOT);
			for (File file : files) {
				List<String> lines = FileUtils.readLines(file);
				for (String line : lines) {
					writeAllErr.println(line);
				}
			}
			if (writeAllErr != null) {
				IOUtils.closeQuietly(writeAllErr);
			}
		} catch (Exception e) {
			String errmsg = new StringBuffer().append(
					"執行DXLParser 之combineErr時產生錯誤").toString();
			this.parserlogs.println(errmsg + "=>" + e.getLocalizedMessage());
			throw new DCException(errmsg + "==>" + e.getLocalizedMessage(), e);
		}
	}

	/**
	 * 取得\log\logs\執行日期\LMS(CLS)資料夾下附加檔名為"err"之檔案
	 * 
	 * @param loadDb2Path
	 * @return
	 */
	private Collection<File> getLogRootDirList(String logRootPath)
			throws Exception {
		File dir = new File(logRootPath);
		String[] extensions = { "err" };
		Collection<File> files = FileUtils.listFiles(dir, extensions, true);
		return files;
	}

}
