package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C121M01A;
import com.mega.eloan.lms.model.C121M01F;

/** 海外消金評等模型評等資料 **/
public interface C121M01FDao extends IGenericDao<C121M01F> {

	public C121M01F findByOid(String oid);
	public List<C121M01F> findByMainId(String mainId);	
	public List<C121M01F> findByC121M01A(C121M01A meta);
	public C121M01F findByUk(String mainId, String custId, String dupNo);
	public C121M01F findByC120M01A(C120M01A c120m01a);
}