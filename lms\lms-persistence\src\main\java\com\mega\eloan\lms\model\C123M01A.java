/* 
 * C123M01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 海外消金評等表主檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C123M01A", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId"}))
public class C123M01A extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 案件號碼-年度 **/
	@Digits(integer=4, fraction=0, groups = Check.class)
	@Column(name="CASEYEAR", columnDefinition="DECIMAL(4,0)")
	private Integer caseYear;

	/** 案件號碼-分行 **/
	@Size(max=3)
	@Column(name="CASEBRID", length=3, columnDefinition="CHAR(3)")
	private String caseBrId;

	/** 案件號碼-流水號 **/
	@Digits(integer=5, fraction=0, groups = Check.class)
	@Column(name="CASESEQ", columnDefinition="DECIMAL(5,0)")
	private Integer caseSeq;

	/** 評等文件編號 **/
	@Size(max=62)
	@Column(name="CASENO", length=62, columnDefinition="VARCHAR(62)")
	private String caseNo;

	/** 信標分數 **/
	@Size(max=3)
	@Column(name="BEACONSCORE", length=3, columnDefinition="CHAR(3)")
	private String beaconScore;

	/** 債務比 **/
	@Size(max=3)
	@Column(name="DEBTRATIO", length=3, columnDefinition="CHAR(3)")
	private String debtRatio;

	/** 貸款價值比 **/
	@Size(max=3)
	@Column(name="LTVRATIO", length=3, columnDefinition="CHAR(3)")
	private String ltvRatio;

	/** 工作年資 **/
	@Size(max=3)
	@Column(name="EMPLOYYEAR", length=3, columnDefinition="CHAR(3)")
	private String employYear;

	/** 收入穩定性 **/
	@Size(max=3)
	@Column(name="INCOME", length=3, columnDefinition="CHAR(3)")
	private String income;

	/** 業務類型 **/
	@Size(max=3)
	@Column(name="PROPERTYTYPE", length=3, columnDefinition="CHAR(3)")
	private String propertyType;

	/** 綜合評估分數 **/
	@Size(max=3)
	@Column(name="ASSESSMENT", length=3, columnDefinition="CHAR(3)")
	private String assessment;

	/** 綜合評估 **/
	@Size(max=384)
	@Column(name="COMMENTS", length=384, columnDefinition="VARCHAR(384)")
	private String comments;

	
	/** 取得案件號碼-年度 **/
	public Integer getCaseYear() {
		return this.caseYear;
	}
	/** 設定案件號碼-年度 **/
	public void setCaseYear(Integer value) {
		this.caseYear = value;
	}

	/** 取得案件號碼-分行 **/
	public String getCaseBrId() {
		return this.caseBrId;
	}
	/** 設定案件號碼-分行 **/
	public void setCaseBrId(String value) {
		this.caseBrId = value;
	}

	/** 取得案件號碼-流水號 **/
	public Integer getCaseSeq() {
		return this.caseSeq;
	}
	/** 設定案件號碼-流水號 **/
	public void setCaseSeq(Integer value) {
		this.caseSeq = value;
	}

	/** 取得評等文件編號 **/
	public String getCaseNo() {
		return this.caseNo;
	}
	/** 設定評等文件編號 **/
	public void setCaseNo(String value) {
		this.caseNo = value;
	}

	/** 取得信標分數 **/
	public String getBeaconScore() {
		return this.beaconScore;
	}
	/** 設定信標分數 **/
	public void setBeaconScore(String value) {
		this.beaconScore = value;
	}

	/** 取得債務比 **/
	public String getDebtRatio() {
		return this.debtRatio;
	}
	/** 設定債務比 **/
	public void setDebtRatio(String value) {
		this.debtRatio = value;
	}

	/** 取得貸款價值比 **/
	public String getLtvRatio() {
		return this.ltvRatio;
	}
	/** 設定貸款價值比 **/
	public void setLtvRatio(String value) {
		this.ltvRatio = value;
	}

	/** 取得工作年資 **/
	public String getEmployYear() {
		return this.employYear;
	}
	/** 設定工作年資 **/
	public void setEmployYear(String value) {
		this.employYear = value;
	}

	/** 取得收入穩定性 **/
	public String getIncome() {
		return this.income;
	}
	/** 設定收入穩定性 **/
	public void setIncome(String value) {
		this.income = value;
	}

	/** 取得業務類型 **/
	public String getPropertyType() {
		return this.propertyType;
	}
	/** 設定業務類型 **/
	public void setPropertyType(String value) {
		this.propertyType = value;
	}

	/** 取得綜合評估分數 **/
	public String getAssessment() {
		return this.assessment;
	}
	/** 設定綜合評估分數 **/
	public void setAssessment(String value) {
		this.assessment = value;
	}

	/** 取得綜合評估 **/
	public String getComments() {
		return this.comments;
	}
	/** 設定綜合評估 **/
	public void setComments(String value) {
		this.comments = value;
	}
}
