/* 
 * DWRKCNTRNOService.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dw.service;

import java.util.List;
import java.util.Map;

/**
 * <pre>
 * 管理報表-個金 資料
 * </pre>
 * 
 * @since 2013/01/17
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/01/17,Vector,new
 *          </ul>
 */
public interface DWCLSReportService {
	
	/**
	 * 查詢 報案考核表被扣分清單
	 * 
	 * @param brno
	 *            分行代號
	 * @param TWNMonth
	 *            民國年月(YYYMM)
	 * @return UNID,AFCOUNT,CUSTID,DUPNO
	 */
	public List<Map<String, Object>> queryDeductPoint(String brno,String TWNMonth);
}
