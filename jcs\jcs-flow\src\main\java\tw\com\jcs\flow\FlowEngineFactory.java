package tw.com.jcs.flow;

import static tw.com.jcs.flow.FlowConstant.CACHE_SIZE;
import static tw.com.jcs.flow.FlowConstant.DEFINITION_LOCATION;
import static tw.com.jcs.flow.FlowConstant.ID_PROVIDER;
import static tw.com.jcs.flow.FlowConstant.OBJECT_FACTORY;
import static tw.com.jcs.flow.FlowConstant.TABLE_INSTANCE;
import static tw.com.jcs.flow.FlowConstant.TABLE_INSTANCE_HISTORY;
import static tw.com.jcs.flow.FlowConstant.TABLE_SEQUENCE;
import static tw.com.jcs.flow.FlowConstant.TABLE_SEQUENCE_HISTORY;

import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;
import javax.naming.InitialContext;
import javax.naming.NamingException;
import javax.sql.DataSource;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathConstants;
import javax.xml.xpath.XPathExpressionException;
import javax.xml.xpath.XPathFactory;

import org.owasp.esapi.ESAPI;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.w3c.dom.Document;
import org.w3c.dom.Node;

import tw.com.jcs.flow.core.FlowEngineImpl;
import tw.com.jcs.flow.core.FlowException;
import tw.com.jcs.flow.provider.IdProvider;
import tw.com.jcs.flow.provider.ObjectFactory;

/**
 * <pre>
 * FlowEngineFactory
 * </pre>
 * 
 * @since 2023年1月9日
 * <AUTHOR> @version
 *          <ul>
 *          <li>2023年1月9日
 *          <li>2024年10月21日 修正CHECKMARX
 *          </ul>
 */
public class FlowEngineFactory {

    private static final Logger log = LoggerFactory.getLogger(FlowEngineFactory.class);

    DataSource dataSource;

    ObjectFactory objectFactory;

    String configLocation;

    Map<String, String> config;

    XPath xPathEvaluater;

    IdProvider idProvider;

    @Resource
    public void setDataSource(DataSource dataSource) {
        this.dataSource = dataSource;
    }

    public void setConfigLocation(String configLocation) {
        this.configLocation = configLocation;
    }

    public void setConfig(Map<String, String> config) {
        this.config = config;
    }

    public void setObjectFactory(ObjectFactory objectFactory) {
        this.objectFactory = objectFactory;
    }

    /**
     * 載入config
     * 
     * @return
     */
    public FlowEngine buildEngine() {
        try {
            if (config == null) {
                Document doc = getDocument();
                xPathEvaluater = XPathFactory.newInstance().newXPath();
                Node node = getNode("//table", doc);

                config = new HashMap<String, String>();
                config.put(TABLE_INSTANCE, getValue("./instance", node));
                config.put(TABLE_SEQUENCE, getValue("./sequence", node));
                config.put(TABLE_INSTANCE_HISTORY, getValue("./instance-history", node));
                config.put(TABLE_SEQUENCE_HISTORY, getValue("./sequence-history", node));

                config.put(ID_PROVIDER, getValue("//id-provider", doc));
                config.put(CACHE_SIZE, getValue("//cache-size", doc));
                config.put(DEFINITION_LOCATION, getValue("//def-path", doc));
                config.put(OBJECT_FACTORY, getValue("//object-factory", doc));

                if (dataSource == null) {
                    // if the dataSource doesn't be provided, create one.
                    node = getNode("//jdbc", doc);
                    dataSource = createDataSource(node);
                }
            }

            FlowEngineImpl engine = new FlowEngineImpl(dataSource, config, objectFactory, idProvider);
            return engine;
        } catch (Exception e) {
            throw new FlowException("error occured when building Flow Engine : " + e, e);
        }
    }

    /**
     * 建立dataSource
     * 
     * @param node
     * @return
     */
    DataSource createDataSource(Node node) {
        DataSource ds = null;
        String jndi = getValue("./jndi", node);
        if (jndi.length() > 0) {
            ds = findJndiDataSource(jndi);
        }

        return ds;
    }

    /**
     * 檢索jndi
     * 
     * @param jndi
     * @return
     */
    DataSource findJndiDataSource(String jndi) {
        try {
            InitialContext ctx = new InitialContext();
            Object obj = ctx.lookup(jndi);
            if (obj != null && obj instanceof DataSource) {
                return (DataSource) obj;
            }
        } catch (NamingException e) {
            log.debug("can't find jndi name '{}' : {}", jndi, e);
        }
        return null;
    }

    /**
     * 取得文件
     * 
     * @return
     */
    Document getDocument() {
        try {
            InputStream is = Thread.currentThread().getContextClassLoader().getResourceAsStream(configLocation);
            Document doc;
            // 修正Improper_Restriction_of_Stored_XXE_Ref
            // 針對XML的Factory class設定禁用DTD
            DocumentBuilderFactory dfactory = DocumentBuilderFactory.newInstance();
            dfactory.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
            dfactory.setFeature("http://xml.org/sax/features/external-general-entities", false);
            dfactory.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
            doc = dfactory.newDocumentBuilder().parse(is);
            is.close();

            return doc;
        } catch (Exception e) {
            throw new FlowException("can't create XML document '" + configLocation + "' : " + e, e);
        }
    }

    /**
     * 取得xml的值
     * 
     * @param xPath
     * @param parent
     * @return
     */
    String getValue(String xPath, Node parent) {
        try {
            String value = xPathEvaluater.evaluate(xPath, parent);

            if (value == null) {
                return "";
            }
            return value.trim();
        } catch (XPathExpressionException e) {
            throw new FlowException("can't read XPath value for '" + xPath + "' @ " + parent.getNodeName() + " : " + e, e);
        }
    }

    /**
     * 取得節點
     * 
     * @param xPath
     * @param parent
     * @return
     */
    Node getNode(String xPath, Node parent) {
        try {
            return (Node) xPathEvaluater.evaluate(xPath, parent, XPathConstants.NODE);
        } catch (XPathExpressionException e) {
            throw new FlowException("can't get node '" + xPath + "' for '" + parent.getNodeName() + "' : " + e, e);
        }
    }

    /**
     * @param idProvider
     *            the idProvider to set
     */
    public void setIdProvider(IdProvider idProvider) {
        this.idProvider = idProvider;
    }

}
