/**
 * CapAjaxFormResult.java
 *
 * Copyright (c) 2009-2011 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
 */
package tw.com.iisi.cap.response;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import javax.servlet.ServletResponse;

import org.kordamp.json.JSONObject;
import org.kordamp.json.JsonConfig;
import org.kordamp.json.processors.JsonBeanProcessor;
import org.springframework.util.Assert;

import com.iisigroup.cap.component.impl.StringResponse;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapFormatException;
import tw.com.iisi.cap.formatter.ADDateFormatter;
import tw.com.iisi.cap.formatter.ADDateTimeFormatter;

/**
 * <pre>
 * Form Result
 * </pre>
 * 
 * @since 2010/7/21
 * <AUTHOR>
 * @version $Id$
 * @version
 *          <ul>
 *          <li>2010/7/21,iristu,new
 *          <li>2011/2/14,RodesChen,增加add判斷
 *          <li>2011/8/25,RodesChen,change CapAjaxFormResult putAll(Map<String,Object> map) to CapAjaxFormResult putAll(Map<String, ? extends Object> map)
 *          <li>2011/10/17,iristu,set Date/Timestamp reformat
 *          <li>2012/1/11,RodesChen,add containsKey method
 *          <li>2013/3/1,Sunkist,add putAllWithProxy method
 *          </ul>
 */
public class CapAjaxFormResult implements IResult {

    /**
     * JSON物件資料
     */
    private JSONObject resultMap;

    /**
     * 從Bean中取得的鍵值
     */
    private String getKeyFromBean;

    /**
     * 建構子
     */
    public CapAjaxFormResult() {
        resultMap = new JSONObject();
    }

    /**
     * 建構子
     * 
     * @param obj
     *            Object
     */
    public CapAjaxFormResult(Object obj) {
        resultMap = new JSONObject();
        resultMap = JSONObject.fromObject(obj);
    }

    /**
     * 建構子
     * 
     * @param json
     *            JSONObject
     */
    public CapAjaxFormResult(JSONObject json) {
        if (json != null) {
            resultMap = json;
        } else {
            resultMap = new JSONObject();
        }
    }

    /**
     * 放到畫面
     * 
     * @param key
     *            String
     * @param val
     *            String
     * @return this FormResult
     */
    public CapAjaxFormResult set(String key, String val) {
        resultMap.put(key, val);
        return this;
    }

    /**
     * 放到畫面
     * 
     * @param key
     *            String
     * @param val
     *            List<String>
     * @return this FormResult
     */
    public <T extends Object> CapAjaxFormResult set(String key, List<T> val) {
        resultMap.put(key, val);
        return this;
    }

    /**
     * 放到畫面
     * 
     * @param key
     *            String
     * @param val
     *            Date
     * @return this FormResult
     */
    public CapAjaxFormResult set(String key, Date val) {
        try {
            String va = new ADDateFormatter().reformat(val);
            resultMap.put(key, va);
        } catch (CapFormatException e) {
            Assert.isTrue(false, e.getMessage());
        }
        return this;
    }

    /**
     * 放到畫面
     * 
     * @param key
     *            String
     * @param val
     *            Integer
     * @return this FormResult
     */
    public CapAjaxFormResult set(String key, Integer val) {
        resultMap.put(key, val);
        return this;
    }

    /**
     * 放到畫面
     * 
     * @param key
     *            String
     * @param val
     *            Integer
     * @return this FormResult
     */
    public CapAjaxFormResult set(String key, BigDecimal val) {
        resultMap.put(key, val.toString());
        return this;
    }

    /**
     * 放到畫面
     * 
     * @param key
     *            String
     * @param val
     *            Boolean
     * @return this FormResult
     */
    public CapAjaxFormResult set(String key, Boolean val) {
        resultMap.put(key, val);
        return this;
    }

    /**
     * 放到畫面
     * 
     * @param key
     *            String
     * @param val
     *            Timestamp
     * @return this FormResult
     */
    public CapAjaxFormResult set(String key, Timestamp val) {
        try {
            String va = new ADDateTimeFormatter().reformat(val);
            resultMap.put(key, va);
        } catch (CapFormatException e) {
            Assert.isTrue(false, e.getMessage());
        }
        return this;
    }

    /**
     * 放到畫面
     * 
     * @param key
     *            String
     * @param val
     *            FormResult
     * @return this FormResult
     */
    public CapAjaxFormResult set(String key, CapAjaxFormResult val) {
        resultMap.put(key, val.resultMap);
        return this;
    }

    /**
     * 塞值
     * 
     * @param val
     * @return
     */
    public CapAjaxFormResult putAll(CapAjaxFormResult val) {
        resultMap.putAll(val.resultMap);
        return this;
    }

    /**
     * 塞值
     * 
     * @param map
     * @return
     */
    public CapAjaxFormResult putAll(Map<String, ? extends Object> map) {
        return putAllWithProxy(map);
    }

    /**
     * 當前JSON物件轉JSON字串
     * 
     * @return JSON字串
     */
    public String toString() {
        return resultMap.toString();
    }

    /**
     * 移除欄位
     * 
     * @param key
     *            String
     * @return this FormResult
     */
    public CapAjaxFormResult removeField(String key) {
        resultMap.remove(key);
        return this;
    }

    /**
     * 清空Field
     * 
     * @return this FormResult
     */
    public CapAjaxFormResult clearResult() {
        resultMap.clear();
        return this;
    }

    /**
     * put FormResult Map to FormResult
     * 
     * @param m
     *            Map<String, FormResult>
     */
    public void setResultMap(Map<String, CapAjaxFormResult> m) {
        for (Entry<String, CapAjaxFormResult> entry : m.entrySet()) {
            CapAjaxFormResult form = entry.getValue();
            resultMap.put(entry.getKey(), form.resultMap);
        }
    }// ;

    /**
     * put FormResult Map to FormResult
     * 
     * @param m
     *            Map<String, List<FormResult>>
     */
    public void setFormResultMapForList(Map<String, List<String>> m) {
        for (Entry<String, List<String>> entry : m.entrySet()) {
            List<String> list = entry.getValue();
            resultMap.put(entry.getKey(), list);
        }
    }

    /**
     * 取值
     * 
     * @param key
     *            the key
     * @return Object
     */
    public Object get(String key) {
        return resultMap.get(key);
    }

    /**
     * 比對是否存在此key
     * 
     * @param key
     * @return boolean
     */
    public boolean containsKey(String key) {
        return resultMap.containsKey(key);
    }

    /**
     * 取得結果的JSON字串
     * 
     * @return {@code resultMap.toString()}
     */
    @Override
    public String getResult() {
        return resultMap.toString();
    }

    /**
     * 將回傳的JSON字串寫入Log Message
     * 
     * @return {@linkplain #getResult() getResult()}
     */
    @Override
    public String getLogMessage() {
        return getResult();
    }

    /*
     * 將資料放入JSON物件中
     * 
     * @see tw.com.iisi.cap.response.IResult#add(tw.com.iisi.cap.response.IResult)
     */
    @Override
    public void add(IResult result) {
        if (result != null) {
            // JSONObject轉來轉去數值會有誤。若result為CapAjaxFormResult，則直接拿resultMap，避免JSONObject.fromObject()
            if (result instanceof CapAjaxFormResult) {
                CapAjaxFormResult r = (CapAjaxFormResult) result;
                resultMap.putAll(r.resultMap);
            } else {
                JSONObject json = JSONObject.fromObject(result.getResult());
                resultMap.putAll(json);
            }
        }
    }

    /**
     * ehance putAll (use its own jsonConfig)
     * 
     * @param map
     *            data(Map)
     * @param jsonConfig
     *            JsonConfig
     * @param _getKey
     *            use this key to store procesed value
     * @return CapAjaxFormResult
     */
    @SuppressWarnings("unchecked")
    public CapAjaxFormResult putAllWithProxy(Map<String, ? extends Object> map, JsonConfig jsonConfig, String _getKey) {
        getKeyFromBean = _getKey;
        if (jsonConfig == null) {
            jsonConfig = new JsonConfig();
            JsonBeanProcessor processor = new JsDateJsonBeanProcessor();
            jsonConfig.registerJsonBeanProcessor(Date.class, processor);
            jsonConfig.registerJsonBeanProcessor(java.sql.Date.class, processor);
            jsonConfig.registerJsonBeanProcessor(Timestamp.class, processor);
        } // 建立配置文件
        resultMap.putAll(map, jsonConfig);
        Set<String> keySet = resultMap.keySet();
        for (String key : keySet) {
            Object obj = (Object) resultMap.get(key);
            if (obj instanceof JSONObject) {
                JSONObject json = (JSONObject) obj;
                resultMap.element(key, json.opt(getKeyFromBean));
            }
        }
        return this;
    }

    /**
     * 呼叫 {@linkplain #putAllWithProxy(Map, JsonConfig, String) putAllWithProxy}
     * 
     * @param map
     * @param _getKey
     * @return {@code putAllWithProxy(map, null, _getKey)}
     */
    public CapAjaxFormResult putAllWithProxy(Map<String, ? extends Object> map, String _getKey) {
        return putAllWithProxy(map, null, _getKey);
    }

    /**
     * 呼叫 {@linkplain #putAllWithProxy(Map, JsonConfig, String) putAllWithProxy}
     * 
     * @param map
     * @return {@code putAllWithProxy(map, null, "strValFromDate")}
     */
    public CapAjaxFormResult putAllWithProxy(Map<String, ? extends Object> map) {
        return putAllWithProxy(map, null, "strValFromDate");
    }

    /**
     * <pre>
     * json bean processor
     * </pre>
     */
    class JsDateJsonBeanProcessor implements JsonBeanProcessor {

        /**
         * Processes the input bean into a compatible JsDate.<br>
         */
        public JSONObject processBean(Object bean, JsonConfig jsonConfig) {
            JSONObject jsonObject = null;
            if (bean instanceof Timestamp) {
                try {
                    bean = new ADDateTimeFormatter().reformat(bean);
                } catch (CapFormatException e) {
                    jsonObject = new JSONObject(true);
                }
            } else if (bean instanceof Date || bean instanceof Calendar || bean instanceof java.sql.Date) {
                try {
                    bean = new ADDateFormatter().reformat(bean);
                } catch (CapFormatException e) {
                    jsonObject = new JSONObject(true);
                }
            }
            if (bean instanceof String) {
                jsonObject = new JSONObject().element(getKeyFromBean, bean);
            }
            return jsonObject;
        }
    }

    /**
     * 實例化 {@linkplain com.iisigroup.cap.component.impl.StringResponse#StringResponse(String, String, String) StringResponse} 物件
     * 
     * @return new StringResponse
     */
    @Override
    public void respondResult(ServletResponse response) throws CapException {
        new StringResponse("text/plain", "utf-8", getResult()).respond(response);
    }
}
