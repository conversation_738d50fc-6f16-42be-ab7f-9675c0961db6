package com.mega.eloan.lms.fms.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.fms.pages.CLS3001V01Page;
import com.mega.eloan.lms.fms.service.CLS3001Service;
import com.mega.eloan.lms.model.C900S02D;
import com.mega.sso.service.BranchService;

import jxl.Workbook;
import jxl.format.Alignment;
import jxl.format.Border;
import jxl.format.BorderLineStyle;
import jxl.format.CellFormat;
import jxl.format.VerticalAlignment;
import jxl.write.Label;
import jxl.write.NumberFormats;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import jxl.write.WriteException;
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.jcs.common.Util;

@Service
public class CLS3001ServiceImpl extends AbstractCapService implements CLS3001Service {
	
	@Resource
	CLSService clsService;
	
	@Resource
	BranchService branchService;
	
	@Resource
	UserInfoService userInfoService;
	
	//Properties prop_cls3001v01 = MessageBundleScriptCreator.getComponentResource(CLS3001V01Page.class);
	
	@Override
	public void genExcel(ByteArrayOutputStream outputStream, String param_category, String param_caseBrId)
	throws IOException, WriteException {
		Properties prop_cls3001v01 = MessageBundleScriptCreator.getComponentResource(CLS3001V01Page.class);
		
		WritableWorkbook workbook = null;
		WritableSheet sheet1 = null;
		if (true) {
			// ---
			workbook = Workbook.createWorkbook(outputStream);
			sheet1 = workbook.createSheet("明細", 0);

			// ======
			WritableFont headFont_memo = new WritableFont(
					WritableFont.createFont("標楷體"), 12);
			WritableCellFormat cellFormat_memo = new WritableCellFormat(headFont_memo);
			{
				cellFormat_memo.setAlignment(Alignment.LEFT);
				cellFormat_memo.setWrap(false);
			}
			// ======
			WritableFont headFont12 = new WritableFont(
					WritableFont.createFont("標楷體"), 12);
			WritableCellFormat cellFormatL = new WritableCellFormat(headFont12);
			{
				cellFormatL.setAlignment(Alignment.LEFT);
				cellFormatL.setWrap(true);
			}

			WritableCellFormat cellFormatR = new WritableCellFormat(headFont12);
			{
				cellFormatR.setAlignment(Alignment.RIGHT);
				cellFormatL.setWrap(true);
			}

			WritableCellFormat cellFormatL_Border = new WritableCellFormat(
					cellFormatL);
			{
				cellFormatL_Border.setBorder(Border.ALL, BorderLineStyle.THIN);
			}

			WritableCellFormat cellFormatR_Border = new WritableCellFormat(
					cellFormatR);
			{
				cellFormatR_Border.setBorder(Border.ALL, BorderLineStyle.THIN);
			}
			// ======
			WritableCellFormat amtTWDFormat = new WritableCellFormat(headFont12, NumberFormats.FORMAT1);
			if(true){
				amtTWDFormat.setVerticalAlignment(VerticalAlignment.TOP);
				amtTWDFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
			}
			// ======
			WritableCellFormat cellFormat_memo_Border = new WritableCellFormat(
					cellFormat_memo);
			{
				cellFormat_memo_Border.setBorder(Border.ALL, BorderLineStyle.THIN);
			}
			//======
			Map<String, Integer> headerMap = new LinkedHashMap<String, Integer>();
			headerMap.put(prop_cls3001v01.getProperty("C900S02D.caseBrId"), 10);
			headerMap.put(prop_cls3001v01.getProperty("C900S02D.caseBrId.desc"), 16);
			headerMap.put(prop_cls3001v01.getProperty("C900S02D.custId"), 20);
			headerMap.put(prop_cls3001v01.getProperty("C900S02D.custName"), 18);
			headerMap.put(prop_cls3001v01.getProperty("C900S02D.category"), 15);
			headerMap.put(prop_cls3001v01.getProperty("C900S02D.cntrNo"), 20);
			headerMap.put(prop_cls3001v01.getProperty("C900S02D.document_no"), 24);
			headerMap.put(prop_cls3001v01.getProperty("C900S02D.cntrNo"), 20);
			headerMap.put(prop_cls3001v01.getProperty("C900S02D.document_no"), 24);
			headerMap.put(prop_cls3001v01.getProperty("C900S02D.curr"), 10);
			headerMap.put(prop_cls3001v01.getProperty("C900S02D.applyAmt"), 15);
			headerMap.put(prop_cls3001v01.getProperty("C900S02D.approver"), 18);
			headerMap.put(prop_cls3001v01.getProperty("C900S02D.approveTime"), 24);
			
			int totalColSize = headerMap.size();
			
			//======
			List<String[]> rows = new ArrayList<String[]>();
			ISearch search = clsService.getMetaSearch();
			search.addSearchModeParameters(SearchMode.EQUALS, "category", param_category);
			search.addSearchModeParameters(SearchMode.EQUALS, "docStatus",FlowDocStatusEnum.已核准.getCode());		
			search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
			if(true){
				if(Util.isNotEmpty(param_caseBrId)){
					search.addSearchModeParameters(SearchMode.EQUALS, "caseBrId", param_caseBrId);
				}				
			}
			
			
			Page<? extends GenericBean> page = clsService.findPage(
					C900S02D.class, search);
			Map<String, String> map_category = clsService.get_codeTypeWithOrder("C900S02D_category");
			if(true){		
				@SuppressWarnings("unchecked")
				List<C900S02D> list = (List<C900S02D>)page.getContent();
				for(C900S02D c900s02d : list){
					
					String[] arr = new String[totalColSize];
					for (int i_col = 0; i_col < totalColSize; i_col++) {
						arr[i_col] = "";
					}
					int col = 0;
					
					String caseBrId = Util.trim(c900s02d.getCaseBrId());
					arr[col++] = caseBrId;
					arr[col++] = (Util.isNotEmpty(caseBrId)?branchService.getBranchName(caseBrId):"");
					arr[col++] = Util.trim(c900s02d.getCustId());
					arr[col++] = Util.trim(c900s02d.getCustName());
					arr[col++] = LMSUtil.getDesc(map_category, Util.trim(c900s02d.getCategory()));
					arr[col++] = Util.trim(c900s02d.getCntrNo());
					arr[col++] = Util.trim(c900s02d.getDocument_no());
					arr[col++] = Util.trim(c900s02d.getCurr());
				    arr[col++] = LMSUtil.pretty_numStr(c900s02d.getApplyAmt());
				    
				    String approver = Util.trim(c900s02d.getApprover());
				    arr[col++] = approver + " "+(Util.isNotEmpty(approver)?userInfoService.getUserName(approver):"");
				    arr[col++] = Util.getLeftStr(Util.trim(c900s02d.getApproveTime()), 19);
				    // ---				    
					rows.add(arr);	
				}				
			}

			// ==============================
			int rowIdx = 0;
			// ==============================			
			int colIdx = 0;
			for (String h : headerMap.keySet()) {
				int colWidth = headerMap.get(h);
				sheet1.setColumnView(colIdx, colWidth);
				sheet1.addCell(new Label(colIdx, rowIdx, h, cellFormatL_Border));
				// ---
				colIdx++;
			}
			// ==============================
			if(true){
				CellFormat data_fmt = null;
				for (String[] arr : rows) {
					int colLen = arr.length;
					if(true){
						rowIdx++;	
					}						
					
					for (int i_col = 0; i_col < colLen; i_col++) {
						if(i_col==8){
							sheet1.addCell(new jxl.write.Number(i_col, rowIdx, Util.parseDouble(arr[i_col]) , amtTWDFormat));
						}else{
							if(i_col== 6 || i_col== 9 || i_col== 10){
								data_fmt = cellFormat_memo_Border;
							}else{
								data_fmt = cellFormatL_Border;
							}
							sheet1.addCell(new Label(i_col, rowIdx, arr[i_col], data_fmt));	
						}
						
					}			
				}	
			}
			// ==============================
			workbook.write();
			workbook.close();
		}	
	}
}
