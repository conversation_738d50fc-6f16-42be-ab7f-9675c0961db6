/* 
 * LMS1815V04Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;

import tw.com.jcs.auth.AuthType;

/**
 * <pre>
 * 授信簽報書待補件 / 撤件
 * </pre>
 * 
 * @since 2011/11/9
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/11/9,<PERSON>,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms1205v04")
public class LMS1205V04Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		// 設定文件狀態(交易代碼)
		setGridViewStatus(CreditDocStatusEnum.海外_待補件,
				CreditDocStatusEnum.海外_待撤件);

		// 加上Button
		// 主管跟經辦都會出現的按鈕

		// 只有主管出現的按鈕
		if (this.getAuth(AuthType.Accept)) {
			addToButtonPanel(model, LmsButtonEnum.View);
		}
		// 只有經辦出現的按鈕
		if (this.getAuth(AuthType.Modify)) {
            //J-113-0306 Eloan>企業授信>案件簽報書送呈區域中心審核後，若被退件，在「待補件/撤件」中之被撤件之案件，能否設計可以再撈到編製中重新簽報，以增進作業效率
            //撤件案件退回編製中
			// btns.add(CreditButtonEnum.ReBackApproveUnit);
			addToButtonPanel(model, LmsButtonEnum.Modify, LmsButtonEnum.Delete, LmsButtonEnum.ChangeCaseFormat,
					LmsButtonEnum.ChangeVer, LmsButtonEnum.ReBackApproveUnit);
		}
		// 套用哪個i18N檔案
		renderJsI18N(LMS1205V01Page.class);
	}// ;

}
