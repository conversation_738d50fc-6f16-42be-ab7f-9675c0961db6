/* 
 * CLS9041FormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.handler.form;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.UnsupportedEncodingException;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.aspectj.util.FileUtil;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.fms.service.CLS9041M07Service;
import com.mega.eloan.lms.model.C004M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 政策性留學生貸款送保彙報
 * </pre>
 * 
 * @since 2012/11/01
 * <AUTHOR> Lo
 * @version <ul>
 *          <li>2012/11/05,Vector Lo,new
 *          </ul>
 */
@Scope("request")
@Controller("cls9041m07formhandler")
public class CLS9041M07FormHandler extends AbstractFormHandler {

	final String DATE_SPLIT = "-";

	@Resource
	CLS9041M07Service service;

	@Resource
	UserInfoService userinfoservice;

	@Resource
	DocFileService docFileService;

	/**
	 * 新增報送資料
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 * @throws IOException
	 */
	public IResult addC004M01A(PageParameters params)
			throws CapException, IOException {
		// 依本日日期抓資料的日期範圍
		Calendar now = Calendar.getInstance();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		int year = now.get(Calendar.YEAR), month = now.get(Calendar.MONTH) + 1;// 起始為0
		String beginDate, endDate, rptName = null;
		if (month <= 4) {// 1~4月->找去年5/1到10/31
			year--;
			beginDate = year + "-05-01";
			endDate = year + "-10-31";
		} else if (month >= 11) {// 11~12月->找今年5/1到10/31
			beginDate = year + "-05-01";
			endDate = year + "-10-31";
		} else {// 5~10月->找去年11/1到今年4/30			
			beginDate = (year - 1) + "-11-01";
			endDate = year + "-04-30";
		}

		// 開始加
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = IDGenerator.getUUID();
		String rptType = params.getString("rptType");
		C004M01A c004m01a = new C004M01A();
		
		List<Map<String, Object>> data = service.findMisData(beginDate, endDate);
		rptName = "「教育部補助留學生就學貸款」每位留學生補貼息（非利息總額）金額明細";
		// 取得txt資料

		String fileName = TWNDate.toFullAD(new Date()).replace(':', '-')
				+ user.getDutyAgentNo() + ".txt";		
		File tempFile = new File(fileName);
		BufferedWriter bufWriter = new BufferedWriter(new OutputStreamWriter(
				new FileOutputStream(tempFile, true)));
		try{
			for (int i = 0; i < data.size(); i++) {
				Map<String, Object> pivot = data.get(i);
				StringBuffer str = new StringBuffer();
				str.append(parseTWDate(pivot.get("year"))).append(',');// to民國
				str.append(Util.addSpaceWithValue("留學貸款", 11)).append(',');
				str.append(fillString(Util.trim(pivot.get("CNAME")), 27)).append(',');
				str.append(Util.addSpaceWithValue(Util.trim(pivot.get("CUSTID")),11)).append(',');
				str.append(Util.addSpaceWithValue(parseFullTWDate(Util.trim(pivot.get("BIRTH")),DATE_SPLIT),9)).append(',');
				str.append(fillString(Util.trim(pivot.get("ADDR1")), 61)).append(',');
				switch (Util.trim(pivot.get("ICMTYPE")).charAt(0)) {
				case '1':
					str.append(fillString("全額補貼", 11));
					break;
				case '3':
					str.append(fillString("半額補貼", 11));
					break;
				default:
					str.append(fillString("不補貼利息", 11));
				}

				str.append(',').append(Util.addZeroWithValue(Util.trim(pivot.get("TBENEFIT")), 11)).append(',');
				bufWriter.write(str.toString());
				//此寫法在 AIX 上會新增 0A。但在 windows 開啟會不換行
				//bufWriter.newLine();
				bufWriter.write(CapConstants.LINE_BREAK);
			}
			bufWriter.close();
			
			DocFile file = new DocFile();
			file.setMainId(mainId);
			file.setData(FileUtil.readAsByteArray(tempFile));
			file.setCrYear("" + year);
			file.setFieldId("fms");
			file.setTotPages(1);
			file.setSrcFileName(tempFile.getName());
			file.setUploadTime(CapDate.getCurrentTimestamp());
			file.setBranchId(user.getOvUnitNo());
			file.setContentType("text/plain");
			file.setSysId("LMS");

			docFileService.save(file);	
			
			c004m01a.setRptType(rptType);

			c004m01a.setMainId(mainId);
			c004m01a.setUnid(mainId);// 判斷q1從哪個s1產生的依據

			c004m01a.setRptName(rptName);
			c004m01a.setBgnDate(TWNDate.valueOf(beginDate).toDate());
			c004m01a.setEndDate(TWNDate.valueOf(endDate).toDate());
			service.save(c004m01a);
			//=======
			FileUtils.deleteQuietly(tempFile);
		}catch(Exception e){
			FileUtils.deleteQuietly(tempFile);
			//=======
			logger.error(StrUtils.getStackTrace(e));
			throw new CapMessageException(e, getClass());
		}	
		return result;
	}

	private String parseTWDate(Object date) {
		return Util.addZeroWithValue(Util.parseInt(date) - 1911, 3);
	}

	private String parseFullTWDate(String date, String type) {
		if(Util.isEmpty(date)){
			return "";
		}
		String[] split = date.split(type);
		return Util.addZeroWithValue(Util.parseInt(split[0]) - 1911, 3) + type
				+ split[1] + type + split[2];
	}
	
	/**
	 * 刪除資料
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	public IResult deleteC004M01A(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.nullToSpace(params.getString("mainId"));
		List<DocFile> oldFile = docFileService.findByIDAndPid(mainId, null);
		for (DocFile file : oldFile) {
			file.setDeletedTime(CapDate.getCurrentTimestamp());
		}
		service.delete(service.findModelByOid(C004M01A.class,
				params.getString("oid")));
		return result;
	}
	/**
	 * 刪資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	public IResult deleteFile(PageParameters params) {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.nullToSpace(params.getString("oid"));
		service.deleteFile(oid);
		return result;
	}
	
	/**
	 * 同 CLS9041FormHandler 
	 */
	private String fillString(String txt, int len) {
		try {
			int length = txt.getBytes("BIG5").length;
			if (length > len) {
				txt = new String(txt.getBytes("BIG5"), 0, len, "BIG5");
			} else {
				txt = new String(
						(txt + StringUtils.repeat(" ", len)).getBytes("BIG5"),
						0, len, "BIG5");
			}
			return txt;
		} catch (UnsupportedEncodingException e) {
			logger.error(e.toString());
		}
		return StringUtils.repeat(" ", len);
	}
}

