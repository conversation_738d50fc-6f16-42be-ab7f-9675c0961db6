package com.mega.eloan.lms.dc.action;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.w3c.dom.Document;

import com.mega.eloan.lms.dc.base.DCException;
import com.mega.eloan.lms.dc.base.XMLHandler;
import com.mega.eloan.lms.dc.bean.L140M01FBean;
import com.mega.eloan.lms.dc.util.TextDefine;
import com.mega.eloan.lms.dc.util.Util;

/**
 * <pre>
 * Parser140M01F
 * </pre>
 * 
 * @since 2012/12/20
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/20,Bang,new
 *          <li>2013-03-26 Modify by Bang:Clob檔案改為直接寫至loadDB2下
 *          </ul>
 */
public class Parser140M01F extends AbstractLMSCustParser {
	private static final boolean BYPASS_PRECHECK = false;

	// UFO@2013-01-21
	public static final int MAX_LOOP = 10;// 每一筆DXL應最多產生10筆資料
	private XMLHandler xmlHandler = new XMLHandler();
	private static List<String[]> KEY_LIST = new ArrayList<String[]>();

	private static final String[] KEYS = { "T05_Subject", "NT_RateTxt_",
			"USD_RateTxt_", "JPY_RateTxt_", "EUR_RateTxt_", "CNY_RateTxt_",
			"AUD_RateTxt_", "OTH_RateTxt_", "R_RateTxt_" };

	private String BR = "\n";

	static {
		initKeyString();
	}

	/**
	 * @param pid
	 * @param doViewName
	 * @param formGroup
	 */
	public Parser140M01F(String pid, String doViewName, String formGroup) {
		super(pid, doViewName, formGroup);
	}

	/**
	 * 讀取,處理及轉換
	 * 
	 * @param dxlPath
	 *            String : .dxl檔存放路徑
	 * @param dxlName
	 *            :.dxl列表中的.dxl檔名
	 * @param strBrn
	 *            String:分行名稱
	 * @param domDoc
	 *            DOM Document:已轉為DOM Document的.dxl檔
	 */
	@SuppressWarnings("unused")
	protected void transferDXL(String dxlPath, String dxlName, String strBrn,
			Document domDoc, String dxlXml) {
		long t1 = System.currentTimeMillis();
		try {
			String[] k1 = dxlName.split(TextDefine.ATTACH_DXL);// EX:{FLMS120M01_2E3761E1BB971A2B48257A7D00143D9C,.dxl}
			String[] k2 = k1[0].split("_");// EX:{FLMS120M01,2E3761E1BB971A2B48257A7D00143D9C}
			String tmpMainId = "";
			if (k2.length == 2) {
				tmpMainId = k2[1];// 主檔
			} else {
				tmpMainId = k2[2];// 明細檔之UNID
			}

			// 20130417
			// Sandra若CNTRDOCID有值，以CNTRDOCID為140開頭所有的table的mainid；若無值，則取unid為mainid
			// 20130419 Sandra因CNTRDOCID仍會有重覆，建霖mail通知調整使用WEBELOANMAINID
			String cntrDocId = getItemValue(domDoc, "WEBELOANMAINID");
			tmpMainId = cntrDocId.isEmpty() ? tmpMainId : cntrDocId;

			for (int i = 1; i <= MAX_LOOP; i++) {
				if (!BYPASS_PRECHECK && isKeyEmpty(dxlXml, i)) {
					continue;
				}

				if (DEBUG && logger.isDebugEnabled()) {
					logger.debug("@@@@@@@@ DO=>" + dxlName + " :: " + i);
				}

				L140M01FBean L140f = new L140M01FBean();
				L140f.setOid(Util.getOID());
				L140f.setMainId(tmpMainId);
				L140f.setRateSeq(String.valueOf(i));

				// modify by Vance
				// loanTPList中，自DXL取回的值，會有類似<text>A</text><text>B</text></textlist>的值，需改成A|B
				// 在分別要再找LNSubject_A的值放入，以此類推....
				String loanValue = this.getTextList(domDoc, "T05_Subject" + i);

				StringBuilder sb = new StringBuilder();
				String[] tmp = Util.split(loanValue, "|");

				for (String o : tmp) {
					sb.append(Util.nullToSpace(getItemValue(domDoc,
							"LNSubject_" + o)));
					sb.append("|");
				}

				L140f.setLoanTPList(sb.toString().replaceAll("|$", ""));
				if (StringUtils.isEmpty(loanValue)) {
					continue;
				}

				// rateDscr組成欄位:
				// 20130502 Sandra
				// 改用this.xmlHandler.getItemValueByRichText，並加前述字串
				String nt = this.xmlHandler.getItemValueByRichTextType2(domDoc,
						"NT_RateTxt_" + i);
				String usd = this.xmlHandler.getItemValueByRichTextType2(
						domDoc, "USD_RateTxt_" + i);
				String jpy = this.xmlHandler.getItemValueByRichTextType2(
						domDoc, "JPY_RateTxt_" + i);
				String eur = this.xmlHandler.getItemValueByRichTextType2(
						domDoc, "EUR_RateTxt_" + i);
				String cny = this.xmlHandler.getItemValueByRichTextType2(
						domDoc, "CNY_RateTxt_" + i);
				String aud = this.xmlHandler.getItemValueByRichTextType2(
						domDoc, "AUD_RateTxt_" + i);
				String oth = this.xmlHandler.getItemValueByRichTextType2(
						domDoc, "OTH_RateTxt_" + i);
				String r = this.xmlHandler.getItemValueByRichTextType2(domDoc,
						"R_RateTxt_" + i);
				StringBuffer all = new StringBuffer();
				if (nt.length() > 0)
					all.append("新台幣－").append(nt).append(BR);
				if (usd.length() > 0)
					all.append("美　金－").append(usd).append(BR);
				if (jpy.length() > 0)
					all.append("日　幣－").append(jpy).append(BR);
				if (eur.length() > 0)
					all.append("歐　元－").append(eur).append(BR);
				if (cny.length() > 0)
					all.append("人民幣－").append(cny).append(BR);
				if (aud.length() > 0)
					all.append("澳　幣－").append(aud).append(BR);
				if (oth.length() > 0)
					all.append("雜　幣－").append(oth).append(BR);
				if (r.length() > 0)
					all.append(r).append(BR);

				// L140f.setRateDscr(all);

				PrintWriter clobWrite = null;
				// 當loanTPList、rateDscr欄位均為空白時不需產生資料
				String loanTPList = L140f.getLoanTPList();
				// String rateDscr = L140f.getRateDscr();
				String rateDscr = all.toString();
				if (!loanTPList.trim().equals(TextDefine.EMPTY_STRING)
						|| !rateDscr.trim().equals(TextDefine.EMPTY_STRING)) {
					// 2013-02-06 Add by Bang 寫出文字檔(讀入時為 big5 所以這裡必需用ms950)
					if (!rateDscr.trim().equals(TextDefine.EMPTY_STRING)) {
						// 在L140M01F.txt中RateDscr欄位需放稍早DXLParser時所產生的htm所在之路徑
						// Ex:004/FLMS740M01_48256C5500297C9548256D75002F259F_Aterms_Attch.htm
						// TODO:Notes欄位Aterms_Attch雖是richText格式但此Table
						// 並不需要該欄之值,因此L140M01F.txt不需寫入其htm之路徑
						// RateDscr之值若是由多欄位或是非richText型態的text組成,則需另寫成一個htm檔
						// L140M01F.txt中RateDscr欄位值則為此htm檔之路徑
						// EX:004/004_0C93BFA8318B7F6C48256D340014973B_RateDscr_i.htm
						String clobOutFile = strBrn + "_" + L140f.getMainId()
								+ "_RateDscr_" + i + ".htm";

						// 2013-03-26 Modify by Bang:改為直接寫至loadDB2下
						clobWrite = new PrintWriter(
								new BufferedWriter(
										new OutputStreamWriter(
												new FileOutputStream(
														new File(
																this.loadDB2ClobPath
																		+ File.separator
																		+ clobOutFile)),
												this.configData.isOnlineMode() ? TextDefine.ENCODING_UTF8
														: TextDefine.ENCODING_MS950)),
								true);

						clobWrite.println(rateDscr);
						L140f.setRateDscr(strBrn + "/" + clobOutFile);
					}

					this.txtWrite.println(L140f.toString());
					this.parserTotal++;
				}

				IOUtils.closeQuietly(clobWrite);
			}// end of for (int i = 1; i <= MAX_LOOP; i++)
		} catch (Exception e) {
			String errmsg = "【" + strBrn
					+ "】分行執行Parser140M01F 之transferDXL時產生錯誤,dxl檔名:" + dxlName
					+ ",dxlPath=" + dxlPath;
			throw new DCException(errmsg, e);
		} finally {
			if (DEBUG && logger.isDebugEnabled()) {
				logger.debug("@@@@@@@@ TOTAL_COST="
						+ (System.currentTimeMillis() - t1));
			}
		}
	}

	private boolean isKeyEmpty(String xml, int idx) {
		String[] keystr = KEY_LIST.get(idx - 1);
		for (int i = 0, size = keystr.length; i < size; i++) {
			if (xml.indexOf(keystr[i]) == -1 && xml.indexOf(KEYS[i]) != -1) {
				return false;
			}
		}
		return true;
	}

	private static void initKeyString() {
		Logger logger = LoggerFactory.getLogger(Parser140M01F.class);

		for (int i = 1; i <= MAX_LOOP; i++) {
			String[] keystr = new String[KEYS.length];
			for (int j = 0, jsize = KEYS.length; j < jsize; j++) {
				keystr[j] = "<item name='" + KEYS[j] + i + "'><text/></item>";
			}
			KEY_LIST.add(keystr);

			if (logger.isDebugEnabled()) {
				logger.debug(ArrayUtils.toString(keystr));
			}
		}
	}

}
