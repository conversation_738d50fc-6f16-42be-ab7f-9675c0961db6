package com.mega.eloan.lms.batch.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import javax.annotation.Resource;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpSession;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.Application;
import org.apache.wicket.RequestCycle;
import org.apache.wicket.protocol.http.MockHttpServletRequest;
import org.apache.wicket.protocol.http.MockHttpServletResponse;
import org.apache.wicket.protocol.http.WebApplication;
import org.apache.wicket.protocol.http.WebRequest;
import org.apache.wicket.protocol.http.WebRequestCycle;
import org.apache.wicket.protocol.http.WebResponse;
import org.apache.wicket.protocol.http.servlet.ServletWebRequest;
import org.apache.wicket.request.target.component.BookmarkablePageRequestTarget;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.iisigroup.cap.component.impl.CapMvcParameters;
import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.MEGAImageApiEnum;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.MEGAImageService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.batch.service.LmsBatchCommonService;
import com.mega.eloan.lms.cls.constants.ClsConstants;
import com.mega.eloan.lms.cls.pages.CLS1131P02Page;
import com.mega.eloan.lms.cls.pages.CLS1131P03Page;
import com.mega.eloan.lms.cls.report.CLS1141R01RptService;
import com.mega.eloan.lms.cls.service.CLS1141Service;
import com.mega.eloan.lms.dao.C101S04WDao;
import com.mega.eloan.lms.dao.C160M01ADao;
import com.mega.eloan.lms.dao.C160M01BDao;
import com.mega.eloan.lms.dao.C900M01MDao;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C101S04W;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C160M01A;
import com.mega.eloan.lms.model.C160M01B;
import com.mega.eloan.lms.model.C900M01M;
import com.mega.eloan.lms.model.L120M01A;

import tw.com.iisi.cap.annotation.NonTransactional;
import tw.com.iisi.cap.dao.utils.AbstractSearchSetting;
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 文件數位化批次作業
 * </pre>
 * <ul>
 * <li>SLMS-00160 刪除超過天數的文件數位化上傳FTP的本地資料
 * <li>SLMS-00161 放行日在區間內之簽報書列印上傳文件數位化
 * <li>SLMS-00163 放行日在區間內之動審表列印上傳文件數位化
 * </ul>
 * 
 * @since 2022/12/12
 * <AUTHOR>
 * @version <ul>
 *          <li>2022/12/12,009763,new
 *          </ul>
 */
@Service("megaimagebatchserviceimpl")
public class MEGAImageBatchServiceImpl extends AbstractCapService implements WebBatchService {

	private static Logger LOGGER = LoggerFactory.getLogger(MEGAImageBatchServiceImpl.class);

	@Resource
	LmsBatchCommonService lmsBatchCommonService;

	@Resource
	MisdbBASEService misdbBASEService;

	@Resource
	SysParameterService sysParameterService;

	@Resource
	L120M01ADao l120m01aDao;

	@Resource
	C160M01ADao c160m01aDao;

	@Resource
	C160M01BDao c160m01bDao;

	@Resource
	CLS1141Service cls1141Service;

	@Resource
	CLS1141R01RptService cls1141r01rptservice;

	@Resource
	DocFileService docFileService;

	@Resource
	C900M01MDao c900m01mDao;

	@Resource
	MEGAImageService mEGAImageService;

	@Resource
	CLSService clsService;

	@Resource
	CodeTypeService codeTypeService;

	@Resource
	C101S04WDao c101s04wDao;

	private static String MEGAIMAGE_RPTPRINT_DOCFILE_FIELDID = "RPT4MEGAIMAGE";

	@Override
	@NonTransactional
	public JSONObject execute(JSONObject json) {
		JSONObject result = new JSONObject();

		try {

			JSONObject request = json.getJSONObject("request");
			String type = request.optString("type");

			if (Util.equals(type, "1")) {
				// J-111-0223 新增eloan消金文件影像查詢匯入功能
				// 刪除文件數位化上傳FTP的本地資料夾
				String errMsg = doBatchJob0001(request);
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE, "MEGAImageBatchServiceImpl-doBatchJob0001執行失敗！==>" + errMsg);
					return result;
				}
			} else if (Util.equals(type, "2")) {
				// J-111-0223 簽報書文件上傳
				String errMsg = doBatchJob0002(request);
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE, "MEGAImageBatchServiceImpl-doBatchJob0002執行失敗！==>" + errMsg);
					return result;
				}
			} else if (Util.equals(type, "3")) {
				// J-111-0223 動審表文件上傳
				String errMsg = doBatchJob0003(request);
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE, "MEGAImageBatchServiceImpl-doBatchJob0003執行失敗！==>" + errMsg);
					return result;
				}
			} else if (Util.equals(type, "4")) {
				// J-111-0223 刪除文件數位化資料
				String errMsg = doBatchJob0004(request);
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE, "MEGAImageBatchServiceImpl-doBatchJob0004執行失敗！==>" + errMsg);
					return result;
				}
			} else if (Util.equals(type, "5")) {
				// J-111-0223 補上傳文件數位化資料
				String errMsg = doBatchJob0005(request);
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE, "MEGAImageBatchServiceImpl-doBatchJob0005執行失敗！==>" + errMsg);
					return result;
				}
			} else {
				result = WebBatchCode.RC_ERROR;
				result.element(WebBatchCode.P_RESPONSE, "MEGAImageBatchServiceImpl執行失敗！==>" + "傳入參數 type=[" + type
						+ "] 無可對應執行功能!!");
			}

			result = WebBatchCode.RC_SUCCESS;
			result.element(WebBatchCode.P_RESPONSE, "MEGAImageBatchServiceImpl執行成功！");

		} catch (Exception ex) {
			ex.printStackTrace();
			result = WebBatchCode.RC_ERROR;
			result.element(WebBatchCode.P_RESPONSE, "MEGAImageBatchServiceImpl執行失敗！==>" + ex.getLocalizedMessage());

		}

		return result;
	}

	/**
	 * 刪除文件數位化上傳FTP的本地資料夾 刪除超過天數overDays的資料夾
	 * 
	 * @param request
	 * @throws CapException
	 */
	public String doBatchJob0001(JSONObject request) throws CapException {
		// 0 0 0 * * ? 每日00:00 跑批刪除檔案
		// http://127.0.0.1:9081/lms-web/app/scheduler?input={"TIMEOUT":"9999","serviceId":"megaimagebatchserviceimpl",request:{type:"1","overDays":"30"}}
		StringBuffer errMsg = new StringBuffer("");
		int count = 0;
		int overDays = request.getInt("overDays");
		String currentDate = CapDate.getCurrentDate("yyyyMMdd");
		List<File> folders = new ArrayList<File>();
		String uploadDirDefPath = sysParameterService.getParamValue("MEGAIMAGE_UPLOAD_DIR");
		File dir = new File(uploadDirDefPath);
		File[] files = dir.listFiles();
		for (File file : files) {
			String fileName = file.getName();
			if (file.isDirectory() && CapDate.isMatchPattern(fileName, "yyyyMMdd")) {
				// 計算檔案日期超過現在日期幾天
				// 超過設定overDays，則刪除
				int overDataDateDays = CapDate.calculateDays(currentDate, fileName);
				if (overDataDateDays >= overDays) {
					folders.add(file);
				}
			}
		}
		// 刪除實體檔
		deleteFolders(folders);
		count = folders.size();
		LOGGER.info("doBatchJob0001 執行結果 : " + count + "筆。");
		return errMsg.toString();
	}

	/**
	 * 刪除文件夾
	 * 
	 * @param folders
	 */
	private void deleteFolders(final List<File> folders) {
		new Thread() {
			@Override
			public void run() {
				for (File file : folders) {
					if (file.exists()) {
						FileUtils.deleteQuietly(file);
					}
				}
			}
		}.start();
	}

	/**
	 * 抓取簽報書放行日在設定的區間內之簽報書，查詢所需列印
	 * 
	 * @param request
	 * @throws Exception
	 * @throws IOException
	 * @throws FileNotFoundException
	 */
	public String doBatchJob0002(JSONObject request) throws FileNotFoundException, IOException, Exception {
		// 0 0 0 * * ? 每日00:00 跑批執行
		// http://127.0.0.1:9081/lms-web/app/scheduler?input={"TIMEOUT":"9999","serviceId":"megaimagebatchserviceimpl",request:{type:"2","sDate":"2021-05-01","eDate":"2021-05-01"}}
		// http://127.0.0.1:9082/lms-web/app/scheduler?input={"TIMEOUT":"9999","serviceId":"megaimagebatchserviceimpl",request:{type:"2","sDate":"2022-06-11","eDate":"2022-06-11","mainIds":"9f0317eb17604e54b0f7f608e8af953a"}}
		LOGGER.info("抓取簽報書放行日在設定的區間內之簽報書，查詢所需列印request" + request.toString());
		StringBuffer errMsg = new StringBuffer("");
		int count = 0;
		String sDate = Util.trim(request.optString("sDate"));
		String eDate = Util.trim(request.optString("eDate"));
		if (sDate.isEmpty() && eDate.isEmpty()) {
			// 若沒有設定時，預設帶入前一日初~日終
			Calendar cal = Calendar.getInstance();
			cal.add(Calendar.DATE, -1);
			String yesterday = CapDate.formatDate(cal.getTime(), "yyyy-MM-dd");
			sDate = yesterday;
			eDate = yesterday;
		} else {
			if (!CapDate.isMatchPattern(sDate, "yyyy-MM-dd")) {
				errMsg.append("參數[sDate]格式不符yyyy-MM-dd");
			}
			if (!CapDate.isMatchPattern(eDate, "yyyy-MM-dd")) {
				errMsg.append("參數[eDate]格式不符yyyy-MM-dd");
			}
			if (Util.notEquals(errMsg.toString(), "")) {
				return errMsg.toString();
			}
		}
		Map<L120M01A, List<PageParameters>> l120m01aUploadParams = new LinkedHashMap<L120M01A, List<PageParameters>>();
		// 1.取得簽報書放行日在時間起迄日內的資料
		LOGGER.info("取得簽報書放行日在時間起迄日內的資料");
		ISearch pageSetting = l120m01aDao.createSearchTemplete();
		pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS, "docCode", UtilConstants.Casedoc.DocCode.陳復陳述案);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus", CreditDocStatusEnum.海外_已核准.getCode());
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docType", UtilConstants.Casedoc.DocType.個金);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "typCd", UtilConstants.Casedoc.typCd.DBU);
		pageSetting.addSearchModeParameters(SearchMode.BETWEEN, "approveTime", new Object[] { Util.parseDate(sDate),
				Util.parseDate(eDate + " 23:59:59") });
		pageSetting.addOrderBy("caseDate", true);
		pageSetting.addOrderBy("caseNo", false);
		pageSetting.addOrderBy("oid", false);
		pageSetting.setMaxResults(Integer.MAX_VALUE);
		// 特定文件MAINID
		String[] mainIds = Util.trim(request.optString("mainIds")).split(";");
		if (mainIds != null && mainIds.length > 0 && !CapString.isEmpty(mainIds[0])) {
			pageSetting.addSearchModeParameters(SearchMode.IN, "mainId", mainIds);
		}
		// 特定分行
		String[] ownBrIds = Util.trim(request.optString("ownBrIds")).split(";");
		if (ownBrIds != null && ownBrIds.length > 0 && !CapString.isEmpty(ownBrIds[0])) {
			pageSetting.addSearchModeParameters(SearchMode.IN, "ownBrId", ownBrIds);
		}
		List<L120M01A> l120m01as = l120m01aDao.find(pageSetting);
		LOGGER.info("取得上傳參數");
		// 特定表單代碼:用於補檔用
		List<String> formIds = new ArrayList<String>();
		if (Util.trim(request.optString("formIds")).isEmpty()) {
			List<CodeType> codeTypes = codeTypeService.findByCodeTypeList("MEGAIMAGE_FormId_ALL");
			for (CodeType codeType : codeTypes) {
				if (codeType.getCodeValue().startsWith("ECL")) {
					formIds.add(codeType.getCodeValue());
				}
			}
		} else {
			for (String fId : Util.trim(request.optString("formIds")).split(";")) {
				formIds.add(fId);
			}
		}
		for (L120M01A l120m01a : l120m01as) {
			List<PageParameters> paramsList = new ArrayList<PageParameters>();
			// 2.產生簽報書列印檔上傳參數
			paramsList.addAll(getL120M01ARptPrintParams(l120m01a, formIds));
			// 3.ECL00005產生一鍵列印檔上傳參數
			paramsList.addAll(getOnClickPrintParams(l120m01a, formIds));
			// 4.ECL00006取得簽報書相關附件檔上傳參數
			paramsList.addAll(getRelatedFileParams(l120m01a, formIds));
			l120m01aUploadParams.put(l120m01a, paramsList);
		}
		LOGGER.info("上傳與建立上傳紀錄檔");

		// 5.3.上傳與建立上傳紀錄檔
		// 重上傳設定時，讀取C900M01M.OID
		String[] c900m01mOids = Util.trim(request.optString("c900m01mOids")).split(";");
		boolean isReUpload = false;
		if (c900m01mOids != null && c900m01mOids.length > 0 && !CapString.isEmpty(c900m01mOids[0])) {
			isReUpload = true;
		}
		// 5.3.1.有可能為退回修改案件，軟刪除同ownBrIds、mainIds、formId的檔案
		Set<String> ownBrIdList = new HashSet<String>();
		Set<String> mainIdList = new HashSet<String>();
		Set<String> formIdList = new HashSet<String>();
		for (Entry<L120M01A, List<PageParameters>> entry : l120m01aUploadParams.entrySet()) {
			L120M01A l120m01a = entry.getKey();
			ownBrIdList.add(l120m01a.getOwnBrId());
			mainIdList.add(l120m01a.getMainId());
			List<PageParameters> paramsList = entry.getValue();
			for (PageParameters params : paramsList) {
				String formId = params.getString("formId");
				formIdList.add(formId);
			}
		}
		JSONObject json4doBatchJob0004 = new JSONObject();
		if (!ownBrIdList.isEmpty() && !mainIdList.isEmpty() && !formIdList.isEmpty()) {
			json4doBatchJob0004.put("ownBrIds", StringUtils.join(ownBrIdList, ";"));
			json4doBatchJob0004.put("mainIds", StringUtils.join(mainIdList, ";"));
			json4doBatchJob0004.put("formIds", StringUtils.join(formIdList, ";"));
			doBatchJob0004(json4doBatchJob0004);
		}
		// 傳送FTP
		for (Entry<L120M01A, List<PageParameters>> entry : l120m01aUploadParams.entrySet()) {
			L120M01A l120m01a = entry.getKey();
			List<PageParameters> paramsList = entry.getValue();
			for (PageParameters params : paramsList) {
				if (isReUpload) {
					// 重上傳設定
					String formId = params.getString("formId");
					String stakeholderID = params.getString("stakeholderID");
					List<C900M01M> c900m01mReUploads = getC900M01MByParam(c900m01mOids, l120m01a.getMainId(), formId, stakeholderID);
					for (C900M01M c900m01m : c900m01mReUploads) {
						eLoanUploadImageFile(c900m01m, params);
					}
				} else {
					// 例行批次
					eLoanUploadImageFile(l120m01a, params);
				}
			}
		}
		// 刪除檔案
		// 
		for (Entry<L120M01A, List<PageParameters>> entry : l120m01aUploadParams.entrySet()) {
			List<PageParameters> paramsList = entry.getValue();
			for (PageParameters params : paramsList) {
				String docFileOid = params.getString("docFileOid");
				DocFile docFile = docFileService.findByOidAndSysId(docFileOid, docFileService.getSysId());
				if (docFile != null 
						&& MEGAIMAGE_RPTPRINT_DOCFILE_FIELDID.equals(docFile.getFieldId())) {
					docFileService.delete(docFile.getFieldId());
				}
			}
		}
		count = l120m01as.size();
		LOGGER.info("doBatchJob0002 執行結果 : " + count + "筆。");
		return errMsg.toString();
	}

	/**
	 * 抓取動審表放行日在設定的區間內之動審表，查詢所需列印
	 * 
	 * @param request
	 * @throws Exception
	 * @throws IOException
	 * @throws FileNotFoundException
	 */
	public String doBatchJob0003(JSONObject request) throws FileNotFoundException, IOException, Exception {
		// 0 0 0 * * ? 每日00:00 跑批執行
		// http://127.0.0.1:9081/lms-web/app/scheduler?input={"TIMEOUT":"9999","serviceId":"megaimagebatchserviceimpl",request:{type:"3","sDate":"2021-05-01","eDate":"2021-05-01"}}
		// http://127.0.0.1:9082/lms-web/app/scheduler?input={"TIMEOUT":"9999","serviceId":"megaimagebatchserviceimpl",request:{type:"3","sDate":"2022-12-08","eDate":"2022-12-08","mainIds":"b79082a68fa543faa102a8f0a6dab6b2"}}
		StringBuffer errMsg = new StringBuffer("");
		int count = 0;
		String sDate = Util.trim(request.optString("sDate"));
		String eDate = Util.trim(request.optString("eDate"));
		if (sDate.isEmpty() && eDate.isEmpty()) {
			// 若沒有設定時，預設帶入前一日初~日終
			Calendar cal = Calendar.getInstance();
			cal.add(Calendar.DATE, -1);
			String yesterday = CapDate.formatDate(cal.getTime(), "yyyy-MM-dd");
			sDate = yesterday;
			eDate = yesterday;
		} else {
			if (!CapDate.isMatchPattern(sDate, "yyyy-MM-dd")) {
				errMsg.append("參數[sDate]格式不符yyyy-MM-dd");
			}
			if (!CapDate.isMatchPattern(eDate, "yyyy-MM-dd")) {
				errMsg.append("參數[eDate]格式不符yyyy-MM-dd");
			}
			if (Util.notEquals(errMsg.toString(), "")) {
				return errMsg.toString();
			}
		}
		Map<C160M01A, List<PageParameters>> c160m01aUploadParams = new LinkedHashMap<C160M01A, List<PageParameters>>();
		// 1.取得動審表放行日在時間起迄日內的資料
		LOGGER.info("取得動審表放行日在時間起迄日內的資料");
		ISearch pageSetting = c160m01aDao.createSearchTemplete();
		pageSetting.setMaxResults(Integer.MAX_VALUE);
		pageSetting.addOrderBy("custId", true);
		pageSetting.addOrderBy("oid", false);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		pageSetting.addSearchModeParameters(SearchMode.BETWEEN, "approveTime", new Object[] { Util.parseDate(sDate),
				Util.parseDate(eDate + " 23:59:59") });
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "typCd", UtilConstants.Casedoc.typCd.DBU);
		// 特定分行
		String[] ownBrIds = Util.trim(request.optString("ownBrIds")).split(";");
		if (ownBrIds != null && ownBrIds.length > 0 && !CapString.isEmpty(ownBrIds[0])) {
			pageSetting.addSearchModeParameters(SearchMode.IN, "ownBrId", ownBrIds);
		}
		// 特定文件MAINID
		String[] mainIds = Util.trim(request.optString("mainIds")).split(";");
		if (mainIds != null && mainIds.length > 0 && !CapString.isEmpty(mainIds[0])) {
			pageSetting.addSearchModeParameters(SearchMode.IN, "mainId", mainIds);
		}
		// 狀態
		String[] docStatus = Util.trim(request.optString("docStatus")).split(";");
		if (docStatus != null && docStatus.length > 0 && !CapString.isEmpty(docStatus[0])) {
			pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus", docStatus);
		} else {
			pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
					new String[] { CreditDocStatusEnum.先行動用_已覆核.getCode(), CreditDocStatusEnum.先行動用_待覆核.getCode() });
		}
		List<C160M01A> c160m01as = c160m01aDao.find(pageSetting);
		LOGGER.info("取得上傳參數");
		List<String> formIds = new ArrayList<String>();
		if (Util.trim(request.optString("formIds")).isEmpty()) {
			List<CodeType> codeTypes = codeTypeService.findByCodeTypeList("MEGAIMAGE_FormId_ALL");
			for (CodeType codeType : codeTypes) {
				if (codeType.getCodeValue().startsWith("ECL")) {
					formIds.add(codeType.getCodeValue());
				}
			}
		} else {
			for (String fId : Util.trim(request.optString("formIds")).split(";")) {
				formIds.add(fId);
			}
		}
		for (C160M01A c160m01a : c160m01as) {
			List<PageParameters> paramsList = new ArrayList<PageParameters>();
			// 2.取得動審表相關附件檔上傳參數
			paramsList.addAll(getRelatedFileParams(c160m01a, formIds));
			c160m01aUploadParams.put(c160m01a, paramsList);
		}
		LOGGER.info("上傳與建立上傳紀錄檔");
		// 3.上傳與建立上傳紀錄檔
		// 重上傳設定時，讀取C900M01M.OID
		String[] c900m01mOids = Util.trim(request.optString("c900m01mOids")).split(";");
		boolean isReUpload = false;
		if (c900m01mOids != null && c900m01mOids.length > 0 && !CapString.isEmpty(c900m01mOids[0])) {
			isReUpload = true;
		}
		// 5.3.1.有可能為退回修改案件，軟刪除同ownBrIds、mainIds、formId的檔案
		Set<String> ownBrIdList = new HashSet<String>();
		Set<String> mainIdList = new HashSet<String>();
		Set<String> formIdList = new HashSet<String>();
		for (Entry<C160M01A, List<PageParameters>> entry : c160m01aUploadParams.entrySet()) {
			C160M01A c160m01a = entry.getKey();
			ownBrIdList.add(c160m01a.getOwnBrId());
			mainIdList.add(c160m01a.getMainId());
			List<PageParameters> paramsList = entry.getValue();
			for (PageParameters params : paramsList) {
				String formId = params.getString("formId");
				formIdList.add(formId);
			}
		}
		JSONObject json4doBatchJob0004 = new JSONObject();
		if (!ownBrIdList.isEmpty() && !mainIdList.isEmpty() && !formIdList.isEmpty()) {
			json4doBatchJob0004.put("ownBrIds", StringUtils.join(ownBrIdList, ";"));
			json4doBatchJob0004.put("mainIds", StringUtils.join(mainIdList, ";"));
			json4doBatchJob0004.put("formIds", StringUtils.join(formIdList, ";"));
			doBatchJob0004(json4doBatchJob0004);
		}
		// 5.3.2.上傳
		for (Entry<C160M01A, List<PageParameters>> entry : c160m01aUploadParams.entrySet()) {
			C160M01A c160m01a = entry.getKey();
			List<PageParameters> paramsList = entry.getValue();
			for (PageParameters params : paramsList) {
				if (isReUpload) {
					// 重上傳設定
					String formId = params.getString("formId");
					String stakeholderID = params.getString("stakeholderID");
					List<C900M01M> c900m01mReUploads = getC900M01MByParam(c900m01mOids, c160m01a.getMainId(), formId, stakeholderID);
					for (C900M01M c900m01m : c900m01mReUploads) {
						eLoanUploadImageFile(c900m01m, params);
					}
				} else {
					// 例行批次
					eLoanUploadImageFile(c160m01a, params);
				}
			}
		}
		for (Entry<C160M01A, List<PageParameters>> entry : c160m01aUploadParams.entrySet()) {
			List<PageParameters> paramsList = entry.getValue();
			for (PageParameters params : paramsList) {
				// 刪除
				String docFileOid = params.getString("docFileOid");
				DocFile docFile = docFileService.findByOidAndSysId(docFileOid, docFileService.getSysId());
				if (docFile != null &&
						MEGAIMAGE_RPTPRINT_DOCFILE_FIELDID.equals(docFile.getFieldId())) {
					docFileService.delete(docFile.getFieldId());
				}
			}
		}
		count = c160m01as.size();
		LOGGER.info("doBatchJob0003 執行結果 : " + count + "筆。");
		return errMsg.toString();
	}

	/**
	 * 刪除文件數位化資料
	 * 
	 * @param request
	 * @throws Exception
	 * @throws IOException
	 * @throws FileNotFoundException
	 */
	public String doBatchJob0004(JSONObject request) throws FileNotFoundException, IOException, Exception {
		// 0 0 0 * * ? 每日00:00 跑批執行
		// http://127.0.0.1:9081/lms-web/app/scheduler?input={"TIMEOUT":"9999","serviceId":"megaimagebatchserviceimpl",request:{type:"3","sDate":"2021-05-01","eDate":"2021-05-01"}}
		// http://127.0.0.1:9082/lms-web/app/scheduler?input={"TIMEOUT":"9999","serviceId":"megaimagebatchserviceimpl",request:{type:"3","sDate":"2022-12-08","eDate":"2022-12-08","mainIds":"b79082a68fa543faa102a8f0a6dab6b2"}}
		StringBuffer errMsg = new StringBuffer("");
		int count = 0;
		String sDate = Util.trim(request.optString("sDate"));
		String eDate = Util.trim(request.optString("eDate"));

		LOGGER.info("eLoan連動刪除影像");
		ISearch pageSetting = c900m01mDao.createSearchTemplete();
		pageSetting.addOrderBy("caseNo", true);
		pageSetting.setMaxResults(Integer.MAX_VALUE);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		if (!sDate.isEmpty() && !eDate.isEmpty()) {
			Timestamp timestampSDate = CapDate.convertStringToTimestamp(sDate + " 00:00:00");
			Timestamp timestampEDate = CapDate.convertStringToTimestamp(eDate + " 23:59:59");
			pageSetting.addSearchModeParameters(SearchMode.BETWEEN, "updateTime", new Object[] { timestampSDate,
					timestampEDate });
		}
		// 特定分行
		String[] ownBrIds = Util.trim(request.optString("ownBrIds")).split(";");
		if (ownBrIds != null && ownBrIds.length > 0 && !CapString.isEmpty(ownBrIds[0])) {
			pageSetting.addSearchModeParameters(SearchMode.IN, "branch", ownBrIds);
		}
		// 特定文件MAINID
		String[] mainIds = Util.trim(request.optString("mainIds")).split(";");
		if (mainIds != null && mainIds.length > 0 && !CapString.isEmpty(mainIds[0])) {
			pageSetting.addSearchModeParameters(SearchMode.IN, "mainId", mainIds);
		}
		// 特定上傳表單編號 ECL00001;ECL00002;ECL00004;ECL00005
		String[] formIds = Util.trim(request.optString("formIds")).split(";");
		if (formIds != null && formIds.length > 0 && !CapString.isEmpty(formIds[0])) {
			pageSetting.addSearchModeParameters(SearchMode.IN, "formId", formIds);
		}
		// 特定上傳案號
		String[] caseNos = Util.trim(request.optString("caseNos")).split(";");
		if (caseNos != null && caseNos.length > 0 && !CapString.isEmpty(caseNos[0])) {
			pageSetting.addSearchModeParameters(SearchMode.IN, "caseNo", caseNos);
		}
		List<C900M01M> c900m01ms = c900m01mDao.find(pageSetting);
		Map<String, Set<String>> doneCaseNoDocFileOid = new HashMap<String, Set<String>>();
		String respString = "";
		for (C900M01M c900m01m : c900m01ms) {
			String caseNo = c900m01m.getCaseNo();
			Set<String> doneDocFileOids = new HashSet<String>();
			// 已處理案號跳過
			if (doneCaseNoDocFileOid.containsKey(caseNo)) {
				continue;
			}
			List<Map<String, Object>> megaImageList = mEGAImageService.getMEGAImageList(MEGAImageApiEnum.取得影像清單,
					c900m01m.getMainId(), c900m01m.getCaseNo());
			// 軟刪除文件數位化資料
			for (Map<String, Object> map : megaImageList) {
				String docId = CapString.trimNull(map.get("DocId"));
				String docFileOid = CapString.trimNull(map.get("DocFileOid"));
				String formId = CapString.trimNull(map.get("FormId"));
				String scanDateTime = CapString.trimNull(map.get("ScanDateTime"));
				boolean doFlag = true;
				// 1.有特定的formId，如果此筆資料formId不在特定範圍則不處理
				if (formIds.length > 0 && !CapString.isEmpty(formIds[0]) && !Arrays.asList(formIds).contains(formId)) {
					doFlag = false;
				}
				// 2.不在特定時間內的不處理
				// 有設定起訖日時檢查
				if (!sDate.isEmpty() && !eDate.isEmpty()) {
					scanDateTime = scanDateTime.replace("/", "-");
					Timestamp scanTime = CapDate.convertStringToTimestamp(scanDateTime);
					String checkDateTime = CapDate.convertTimestampToString(scanTime, "yyyyMMdd");
					if (CapDate.calculateDays(checkDateTime, sDate.replace("-", "")) < 0
							&& CapDate.calculateDays(checkDateTime, eDate.replace("-", "")) > 0) {
						doFlag = false;
					}
				}
				if (doFlag == false) {
					// 不處理時，跳過
					continue;
				}
				JSONObject resp = new JSONObject();
				try {
					resp = mEGAImageService.eloanCloseImage(docId);
					if (MEGAImageService.連動刪除影像_code.處理成功.equals(resp.optString("code"))) {
						doneDocFileOids.add(docFileOid);
					} else if (MEGAImageService.連動刪除影像_code.處理失敗.equals(resp.optString("code"))) {
						respString += caseNo + ":" + "docId:" + docId + resp.optString("message") + "<br/>";
					}
				} catch (Exception e) {
					respString += caseNo + ":" + "docId:" + docId + "處理失敗" + "<br/>";
				}
			}
			if (!doneDocFileOids.isEmpty()) {
				doneCaseNoDocFileOid.put(caseNo, doneDocFileOids);
			}
		}
		// 更新紀錄檔
		Timestamp now = CapDate.getCurrentTimestamp();
		for (Entry<String, Set<String>> entry : doneCaseNoDocFileOid.entrySet()) {
			ISearch pageSetting2 = c900m01mDao.createSearchTemplete();
			pageSetting2.addOrderBy("caseNo", true);
			pageSetting2.setMaxResults(Integer.MAX_VALUE);
			pageSetting2.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
			if (!sDate.isEmpty() && !eDate.isEmpty()) {
				Timestamp timestampSDate = CapDate.convertStringToTimestamp(sDate + " 00:00:00");
				Timestamp timestampEDate = CapDate.convertStringToTimestamp(eDate + " 23:59:59");
				pageSetting2.addSearchModeParameters(SearchMode.BETWEEN, "updateTime", new Object[] { timestampSDate,
						timestampEDate });
			}
			// 特定分行
			if (ownBrIds != null && ownBrIds.length > 0 && !CapString.isEmpty(ownBrIds[0])) {
				pageSetting2.addSearchModeParameters(SearchMode.IN, "branch", ownBrIds);
			}
			// 特定文件MAINID
			if (mainIds != null && mainIds.length > 0 && !CapString.isEmpty(mainIds[0])) {
				pageSetting2.addSearchModeParameters(SearchMode.IN, "mainId", mainIds);
			}
			// 特定上傳表單編號 ECL00001;ECL00002;ECL00004;ECL00005
			if (formIds != null && formIds.length > 0 && !CapString.isEmpty(formIds[0])) {
				pageSetting2.addSearchModeParameters(SearchMode.IN, "formId", formIds);
			}
			// 特定上傳案號
			pageSetting2.addSearchModeParameters(SearchMode.EQUALS, "caseNo", entry.getKey());
			pageSetting2.addSearchModeParameters(SearchMode.IN, "docFileOid", entry.getValue());
			List<C900M01M> updateC900m01ms = c900m01mDao.find(pageSetting2);
			for (C900M01M c900m01m : updateC900m01ms) {
				c900m01m.setDeletedTime(now);
				clsService.save(c900m01m);
			}
			count = c900m01ms.size();
		}
		LOGGER.info("doBatchJob0004 執行結果 : " + respString);
		LOGGER.info("doBatchJob0004 執行結果 : " + count + "筆。");
		return errMsg.toString();
	}

	/**
	 * 傳送後掃描結果異常時，補上傳文件數位化資料
	 * 
	 * @param request
	 * @throws Exception
	 * @throws IOException
	 * @throws FileNotFoundException
	 */
	public String doBatchJob0005(JSONObject request) throws FileNotFoundException, IOException, Exception {
		// 0 0 0 * * ? 每日00:00 跑批執行
		// http://127.0.0.1:9081/lms-web/app/scheduler?input={"TIMEOUT":"9999","serviceId":"megaimagebatchserviceimpl",request:{type:"5","sDate":"2021-05-01","eDate":"2021-05-01"}}
		// http://127.0.0.1:9082/lms-web/app/scheduler?input={"TIMEOUT":"9999","serviceId":"megaimagebatchserviceimpl",request:{type:"5","sDate":"2023-01-01","eDate":"2023-02-01","caseNos":"OD20220040000658;OD20220040000895;OD20220040000895;OD20220320000413;OD20220320000440;OD20220350000818;PA20221228079365;PA20230104080079;HA20230102079842;HA20230103079987;HA20230103080020;HA20230104080088;HA20230105080296;HA20230106080359;HA20230107080529;HA20230107080604;HA20230107080614;HA20230109080842;HA20230110080857;OD20220360000291;OF20221228000044;PA20221214077033;PA20221227079161;PA20221228079242;PA20221228079365;PA20221230079614;PA20221230079641;PA20221230079673;PA20221231079687;PA20221231079717;PA20221231079736;PA20230103079903;PA20230103079927;PA20230103079948;PA20230103079953;PA20230103079954;PA20230103079965;PA20230103079969;PA20230103079973;PA20230103079993;PA20230103080003;PA20230103080018;PA20230104080038;PA20230104080062;PA20230104080076;PA20230104080079;PA20230104080081;PA20230104080085;PA20230104080087;PA20230104080092;PA20230104080095;PA20230104080096;PA20230104080098;PA20230104080102;PA20230104080142;PA20230104080161;PA20230105080171;PA20230105080174;PA20230105080201;PA20230105080210;PA20230105080218;PA20230105080226;PA20230105080233;PA20230105080237;PA20230105080252;PA20230105080258;PA20230105080268;PA20230105080270;PA20230105080278;PA20230105080313;PA20230105080319;PA20230106080337;PA20230106080344;PA20230106080354;PA20230106080355;PA20230106080357;PA20230106080379;PA20230106080380;PA20230106080390;PA20230106080406;PA20230106080416;PA20230106080427;PA20230106080432;PA20230106080445;PA20230106080471;PA20230106080472;PA20230106080476;PA20230106080487;PA20230107080516;PA20230107080519;PA20230107080530;PA20230107080537;PA20230107080538;PA20230107080543;PA20230107080552;PA20230107080562;PA20230107080565;PA20230107080568;PA20230107080583;PA20230107080592;PA20230108080646;PA20230108080648;PA20230108080649;PA20230108080659;PA20230108080685;PA20230109080705;PA20230109080730;PA20230109080741;PA20230109080749;PA20230109080751;PA20230109080762;PA20230109080776;PA20230109080786;PA20230109080791;PA20230109080792;PA20230109080828;PA20230109080841;PA20230110080868;PA20230110080873;PA20230110080910;PA20230110080912;PA20230110080915;PA20230110080918;PA20230110080919;PA20230110080924;PA20230110080931;PA20230110080937;PA20230110080947;PA2023011008094;PA20230110080972;PA20230111080993;PA20230111081026;PA20230111081038;PA20230111081039;PA20230111081051;PA20230111081060;PA20221228079365;PA20230104080079;PA20230104080146;PA20230106080383;PA20230106080445;OD20222120000216;OF20221221000016;OF20221221000034;OF20221221000037;OF20221226000002"}}
		StringBuffer errMsg = new StringBuffer("");
		int count = 0;
		String sDate = Util.trim(request.optString("sDate"));
		String eDate = Util.trim(request.optString("eDate"));
		if (sDate.isEmpty() && eDate.isEmpty()) {
			// 若沒有設定時，預設帶入前一日初~日終
			Calendar cal = Calendar.getInstance();
			cal.add(Calendar.DATE, -1);
			String yesterday = CapDate.formatDate(cal.getTime(), "yyyy-MM-dd");
			sDate = yesterday;
			eDate = yesterday;
		} else {
			if (!CapDate.isMatchPattern(sDate, "yyyy-MM-dd")) {
				errMsg.append("參數[sDate]格式不符yyyy-MM-dd");
			}
			if (!CapDate.isMatchPattern(eDate, "yyyy-MM-dd")) {
				errMsg.append("參數[eDate]格式不符yyyy-MM-dd");
			}
			if (Util.notEquals(errMsg.toString(), "")) {
				return errMsg.toString();
			}
		}

		LOGGER.info("eLoan補上傳文件數位化資料");
		ISearch pageSetting = c900m01mDao.createSearchTemplete();
		pageSetting.addOrderBy("caseNo", true);
		pageSetting.setMaxResults(Integer.MAX_VALUE);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		Timestamp timestampSDate = CapDate.convertStringToTimestamp(sDate + " 00:00:00");
		Timestamp timestampEDate = CapDate.convertStringToTimestamp(eDate + " 23:59:59");
		pageSetting.addSearchModeParameters(SearchMode.BETWEEN, "updateTime", new Object[] { timestampSDate,
				timestampEDate });
		// 特定分行
		String[] branchs = Util.trim(request.optString("branchs")).split(";");
		if (branchs != null && branchs.length > 0 && !CapString.isEmpty(branchs[0])) {
			pageSetting.addSearchModeParameters(SearchMode.IN, "branchs", branchs);
		}
		// 特定文件MAINID
		String[] mainIds = Util.trim(request.optString("mainIds")).split(";");
		if (mainIds != null && mainIds.length > 0 && !CapString.isEmpty(mainIds[0])) {
			pageSetting.addSearchModeParameters(SearchMode.IN, "mainId", mainIds);
		}
		// 特定上傳表單編號 ECL00001;ECL00002;ECL00004;ECL00005
		String[] formIds = Util.trim(request.optString("formIds")).split(";");
		if (formIds != null && formIds.length > 0 && !CapString.isEmpty(formIds[0])) {
			pageSetting.addSearchModeParameters(SearchMode.IN, "formId", formIds);
		}
		// 特定上傳案號
		String[] caseNos = Util.trim(request.optString("caseNos")).split(";");
		if (caseNos != null && caseNos.length > 0 && !CapString.isEmpty(caseNos[0])) {
			pageSetting.addSearchModeParameters(SearchMode.IN, "caseNo", caseNos);
		}
		// 特定DOCOID
		String[] docFileOids = Util.trim(request.optString("docFileOids")).split(";");
		if (docFileOids != null && docFileOids.length > 0 && !CapString.isEmpty(docFileOids[0])) {
			pageSetting.addSearchModeParameters(SearchMode.IN, "docFileOid", docFileOids);
		}
		List<C900M01M> c900m01ms = c900m01mDao.find(pageSetting);
		Map<String, List<C900M01M>> caseNoC900M01MMap = new HashMap<String, List<C900M01M>>();
		String respString = "";
		// GROUP BY CASENO
		for (C900M01M c900m01m : c900m01ms) {
			List<C900M01M> groupList = new ArrayList<C900M01M>();
			if (caseNoC900M01MMap.containsKey(c900m01m.getCaseNo())) {
				groupList = caseNoC900M01MMap.get(c900m01m.getCaseNo());
			}
			groupList.add(c900m01m);
			caseNoC900M01MMap.put(c900m01m.getCaseNo(), groupList);
		}
		// 比對文件數位化資料，蒐集沒紀錄的
		List<C900M01M> noRecordC900m01ms = new ArrayList<C900M01M>();
		for (Entry<String, List<C900M01M>> entry : caseNoC900M01MMap.entrySet()) {
			String caseNo = entry.getKey();
			List<C900M01M> groupList = entry.getValue();
			// 取得文件數位化資料
			List<Map<String, Object>> megaImageList = mEGAImageService.getMEGAImageList(MEGAImageApiEnum.取得影像清單, "",
					caseNo);
			// 用上傳紀錄去找對應資料
			for (C900M01M c900m01m : groupList) {
				boolean hasRecord = false;
				for (Map<String, Object> map : megaImageList) {
					String docFileOid = CapString.trimNull(map.get("DocFileOid"));
					if (docFileOid.equals(c900m01m.getDocFileOid())) {
						hasRecord = true;
						break;
					}
				}
				// 上傳紀錄在文件數位化沒有資料
				if (hasRecord == false) {
					noRecordC900m01ms.add(c900m01m);
				}
			}
		}
		// 上傳紀錄在文件數位化沒有資料重新上傳
		List<C900M01M> job002C900M01Ms = new ArrayList<C900M01M>();
		List<C900M01M> job003C900M01Ms = new ArrayList<C900M01M>();
		for (C900M01M c900m01m : noRecordC900m01ms) {
			// 1.取得DOCFILE
			DocFile docFile = docFileService.findByOidAndSysId(c900m01m.getDocFileOid(), "LMS");
			File file = null;
			if (docFile != null) {
				file = docFileService.getRealFile(docFile);
			}
			// 實體檔案存在的直接重新上傳
			if (file != null && file.exists()) {
				c900m01m.setUpdateTime(CapDate.getCurrentTimestamp());
				clsService.eLoanUploadImageFile(c900m01m, c900m01m);
				count++;
			} else {
				// 1.ECL ELOAN產的列印文件
				// 判斷是動審表還是簽報書的東西
				String mainId = c900m01m.getMainId();
				L120M01A l120m01a = l120m01aDao.findByMainId(mainId);
				if (l120m01a != null) {
					job002C900M01Ms.add(c900m01m);
					count++;
				} else {
					C160M01A c160m01a = c160m01aDao.findByMainId(mainId);
					if (c160m01a != null) {
						job003C900M01Ms.add(c900m01m);
						count++;
					}
				}
			}
		}
		// 簽報書列印requst
		if (!job002C900M01Ms.isEmpty()) {
			JSONObject doBatchJob002Param = getJobRequest(job002C900M01Ms, new L120M01A());
			errMsg.append(doBatchJob0002(doBatchJob002Param));
		}
		// 動審表列印requst
		if (!job003C900M01Ms.isEmpty()) {
			JSONObject doBatchJob003Param = getJobRequest(job003C900M01Ms, new C160M01A());
			errMsg.append(doBatchJob0003(doBatchJob003Param));
		}

		// 更新紀錄檔
		LOGGER.info("doBatchJob0005 執行結果 : " + respString);
		LOGGER.info("doBatchJob0005 執行結果 : " + count + "筆。");
		return errMsg.toString();
	}

	private List<C900M01M> getC900M01MByParam(String[] c900m01mOids, String mainId, String formId, String stakeholderID) {
		ISearch c900m01mpageSetting = c900m01mDao.createSearchTemplete();
		c900m01mpageSetting.setMaxResults(Integer.MAX_VALUE);
		c900m01mpageSetting.addSearchModeParameters(SearchMode.IN, "oid", c900m01mOids);
		c900m01mpageSetting.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		c900m01mpageSetting.addSearchModeParameters(SearchMode.EQUALS, "formId", formId);
		c900m01mpageSetting.addSearchModeParameters(SearchMode.EQUALS, "stakeholderID", stakeholderID);
		return c900m01mDao.find(c900m01mpageSetting);
	}

	/**
	 * 設定重跑上傳批次參數BY上傳紀錄
	 * 
	 * @param list
	 * @return
	 */
	private JSONObject getJobRequest(List<C900M01M> list, GenericBean bean) {
		Set<String> mainIdList = new HashSet<String>();
		Set<String> formIdList = new HashSet<String>();
		Set<String> c900m01mOidList = new HashSet<String>();
		List<Date> dateList = new ArrayList<Date>();
		for (C900M01M c900m01m : list) {
			mainIdList.add(c900m01m.getMainId());
			formIdList.add(c900m01m.getFormId());
			c900m01mOidList.add(c900m01m.getOid());
			if (bean instanceof L120M01A) {
				L120M01A l120m01a = l120m01aDao.findByMainId(c900m01m.getMainId());
				dateList.add(l120m01a.getApproveTime());
			} else if (bean instanceof C160M01A) {
				C160M01A c160m01a = c160m01aDao.findByMainId(c900m01m.getMainId());
				dateList.add(c160m01a.getApproveTime());
			}
		}
		String sDate = "2000-01-01";
		String eDate = "2099-01-01";
		if (!dateList.isEmpty()) {
			Collections.sort(dateList);
			sDate = CapDate.formatDate(dateList.get(0), "yyyy-MM-dd");
			eDate = CapDate.formatDate(dateList.get(dateList.size() - 1), "yyyy-MM-dd");
		}
		// 設定JSON
		String mainIds = StringUtils.join(mainIdList, ";");
		String formIds = StringUtils.join(formIdList, ";");
		String c900m01mOids = StringUtils.join(c900m01mOidList, ";");
		JSONObject json = new JSONObject();
		json.put("sDate", sDate);
		json.put("eDate", eDate);
		json.put("mainIds", mainIds);
		json.put("formIds", formIds);
		json.put("c900m01mOids", c900m01mOids);
		return json;
	}

	/**
	 * ECL00005產生一鍵列印文件上傳參數
	 * 
	 * @param l120m01as
	 * @return
	 * @throws UnsupportedEncodingException
	 * @throws CapException
	 */
	private List<PageParameters> getOnClickPrintParams(L120M01A l120m01a, List<String> formIds)
			throws UnsupportedEncodingException, CapException {
		List<PageParameters> paramsList = new ArrayList<PageParameters>();
		if (formIds.contains("ECL00005")) {
			// 透過模擬，取得頁面結果， 結果存到DOCFILE傳送文件數位化
			Application application = WebApplication.get();
			HttpSession session = ((WebRequest) RequestCycle.get().getRequest()).getHttpServletRequest().getSession();
			ServletContext context = WebApplication.get().getServletContext();
			MockHttpServletRequest mockReq = new MockHttpServletRequest(application, session, context);
			MockHttpServletResponse mockRes = new MockHttpServletResponse(mockReq);
			mockReq.setCharacterEncoding("utf-8");
			mockRes.setCharacterEncoding("utf-8");
			// 取得個金徵信資料
			List<C120M01A> c120m01as = clsService.findC120M01AByMainId(l120m01a.getMainId());
			for (C120M01A c120m01a : c120m01as) {
				PageParameters params = new CapMvcParameters();
				params.put(EloanConstants.MAIN_ID, c120m01a.getMainId());
				params.put("custId", c120m01a.getCustId());
				params.put("dupNo", c120m01a.getDupNo());
				params.put("isC120M01A", "Y");
				try {
					mockRes.reset();
					WebResponse res = new WebResponse(mockRes);
					ServletWebRequest req = new ServletWebRequest(mockReq);
					RequestCycle cycle = new WebRequestCycle(WebApplication.get(), req, res);
					cycle.request(new BookmarkablePageRequestTarget(CLS1131P03Page.class, params));
				} catch (Exception e) {
					LOGGER.info(params.toString() + " 產生列印檔時發生錯誤");
					LOGGER.error(StrUtils.getStackTrace(e));
					continue;
				}
				String renderHtmlResult = mockRes.getDocument();
				String brNo = c120m01a.getOwnBrId();
				String mainId = c120m01a.getMainId();
				String fieldId = MEGAIMAGE_RPTPRINT_DOCFILE_FIELDID;
				String custId = c120m01a.getCustId();
				String custName = c120m01a.getCustName();
				String rptName = "cls1131p03";
				byte[] data = renderHtmlResult.getBytes("big5");
				String contentType = "html";
				// 設定上傳檔案資訊
				DocFile docFile = createDocFile(brNo, mainId, fieldId, custId, custName, rptName, data, contentType);
				// 存檔
				String docFileOid = docFileService.save(docFile);
				// ECL00005=一鍵列印
				params.put("formId", "ECL00005");
				params.put("stakeholderID", custId);
				params.put("docFileOid", docFileOid);
				params.put("bean", l120m01a);
				paramsList.add(params);
			}
		}
		return paramsList;
	}

	/**
	 * 取得簽報書/動審表相關附件檔上傳參數
	 * 
	 * @param L120M01A
	 *            /C160M01A
	 * @throws CapException
	 * @throws UnsupportedEncodingException
	 */
	private List<PageParameters> getRelatedFileParams(GenericBean bean, List<String> formIds) throws CapException,
			UnsupportedEncodingException {
		List<PageParameters> paramsList = new ArrayList<PageParameters>();
		if (bean instanceof L120M01A) {
			if (formIds.contains("ECL00006")) {
				// 簽報書
				L120M01A l120m01a = (L120M01A) bean;
				// 1.RPA地政士查詢結果
				List<DocFile> docFiles = docFileService.findByIDAndName(l120m01a.getMainId(), "rpa_lms", null);
				for (DocFile docFile : docFiles) {
					if (docFile.getSrcFileName().startsWith("地政士")) {
						PageParameters params = new CapMvcParameters();
						// ECL00006=地政士查詢結果(RPA查詢)
						params.put("formId", "ECL00006");
						params.put("stakeholderID", l120m01a.getCustId());
						params.put("docFileOid", docFile.getOid());
						paramsList.add(params);
					}
				}
			}
		}
		if (bean instanceof C160M01A) {
			// 動審表
			C160M01A c160m01a = (C160M01A) bean;
			// 透過模擬，取得頁面結果， 結果存到DOCFILE傳送文件數位化
			if (formIds.contains("ECL00001")) {
				// 1.銀行法及金控法利害關係人查詢紀錄(RPA查詢)ECL00001
				paramsList.addAll(getC160M01APrintParamsByFormId(c160m01a, "ECL00001"));
			}
			if (formIds.contains("ECL00002")) {
				// 2.司法院網站借款人及保證人是否為受監護或受輔助宣告資料(RPA查詢)ECL00002
				paramsList.addAll(getC160M01APrintParamsByFormId(c160m01a, "ECL00002"));
			}
			if (formIds.contains("ECL00004")) {
				// 4.聯合徵信中心查詢【B29】、【B33】、【B68】資料ECL00004
				paramsList.addAll(getC160M01APrintParamsByFormId(c160m01a, "ECL00004"));
			}
		}
		return paramsList;
	}

	/**
	 * 取得動審表相關列印資料參數
	 * 
	 * @param c160m01a
	 * @param formId
	 * @return
	 * @throws UnsupportedEncodingException
	 */
	private List<PageParameters> getC160M01APrintParamsByFormId(C160M01A c160m01a, String formId)
			throws UnsupportedEncodingException {
		List<PageParameters> paramsList = new ArrayList<PageParameters>();
		List<C160M01B> c160m01bList = this.clsService.findC160M01B_mainId(c160m01a.getMainId());
		Set<String> c160m01bCustIdDupNoSet = new HashSet<String>();
		for (C160M01B c160m01b : c160m01bList) {
			c160m01bCustIdDupNoSet.add(c160m01b.getCustId() + "," + c160m01b.getDupNo() + "," + c160m01b.getCustName());
		}
		// 透過模擬，取得頁面結果， 結果存到DOCFILE傳送文件數位化
		Application application = WebApplication.get();
		HttpSession session = ((WebRequest) RequestCycle.get().getRequest()).getHttpServletRequest().getSession();
		ServletContext context = WebApplication.get().getServletContext();
		MockHttpServletRequest mockReq = new MockHttpServletRequest(application, session, context);
		MockHttpServletResponse mockRes = new MockHttpServletResponse(mockReq);
		mockRes.setCharacterEncoding("utf-8");
		mockRes.setCharacterEncoding("utf-8");
		if ("ECL00001".equals(formId)) {
			// 1.銀行法及金控法利害關係人查詢紀錄(RPA查詢)ECL00001
			for (String custIdDupNo : c160m01bCustIdDupNoSet) {
				String custId = custIdDupNo.split(",")[0];
				String dupNo = custIdDupNo.split(",")[1];
				String custName = custIdDupNo.split(",")[2];
				PageParameters params = new CapMvcParameters();
				params.put(EloanConstants.MAIN_ID, c160m01a.getMainId());
				params.put("custId", custId);
				params.put("dupNo", dupNo);
				params.put("dataType", ClsConstants.C101S01S_dataType.客戶是否為利害關係人資料);
				params.put("isC120M01A", false);
				try {
					mockRes.reset();
					WebResponse res = new WebResponse(mockRes);
					ServletWebRequest req = new ServletWebRequest(mockReq);
					RequestCycle cycle = new WebRequestCycle(WebApplication.get(), req, res);
					cycle.request(new BookmarkablePageRequestTarget(CLS1131P03Page.class, params));
				} catch (Exception e) {
					LOGGER.info(params.toString() + " 產生列印檔時發生錯誤");
					LOGGER.error(StrUtils.getStackTrace(e));
					continue;
				}
				String renderHtmlResult = mockRes.getDocument();
				String brNo = c160m01a.getOwnBrId();
				String mainId = c160m01a.getMainId();
				String fieldId = MEGAIMAGE_RPTPRINT_DOCFILE_FIELDID;
				String rptName = "cls1131p03";
				byte[] data = renderHtmlResult.getBytes("big5");
				String contentType = "html";
				// 設定上傳檔案資訊
				DocFile docFile = createDocFile(brNo, mainId, fieldId, custId, custName, rptName, data, contentType);
				// 存檔
				String docFileOid = docFileService.save(docFile);
				params.put("formId", "ECL00001");
				params.put("stakeholderID", custId);
				params.put("docFileOid", docFileOid);
				params.put("bean", c160m01a);
				paramsList.add(params);
			}
		} else if ("ECL00002".equals(formId)) {
			// 2.司法院網站借款人及保證人是否為受監護或受輔助宣告資料(RPA查詢)ECL00002
			List<C101S04W> c101s04ws = c101s04wDao.findByMainId(c160m01a.getMainId());
			for (C101S04W c101s04w : c101s04ws) {
				if (!CapString.isEmpty(c101s04w.getDocfileoid())) {
					DocFile docFile = docFileService.findByOidAndSysId(c101s04w.getDocfileoid(),
							docFileService.getSysId());
					PageParameters params = new CapMvcParameters();
					params.put("formId", "ECL00002");
					params.put("stakeholderID", c101s04w.getDataCustomerNo());
					params.put("docFileOid", docFile.getOid());
					paramsList.add(params);
				}
			}
		} else if ("ECL00004".equals(formId)) {
			// 4.聯合徵信中心查詢【B29】、【B33】、【B68】資料ECL00004
			for (String custIdDupNo : c160m01bCustIdDupNoSet) {
				String custId = custIdDupNo.split(",")[0];
				String dupNo = custIdDupNo.split(",")[1];
				String custName = custIdDupNo.split(",")[2];
				PageParameters params = new CapMvcParameters();
				params.put(EloanConstants.MAIN_ID, c160m01a.getMainId());
				params.put("custId", custId);
				params.put("dupNo", dupNo);
				params.put("isC160M01A", "Y");
				try {
					mockRes.reset();
					WebResponse res = new WebResponse(mockRes);
					ServletWebRequest req = new ServletWebRequest(mockReq);
					RequestCycle cycle = new WebRequestCycle(WebApplication.get(), req, res);
					cycle.request(new BookmarkablePageRequestTarget(CLS1131P02Page.class, params));
				} catch (Exception e) {
					LOGGER.info(params.toString() + " 產生列印檔時發生錯誤");
					LOGGER.error(StrUtils.getStackTrace(e));
					continue;
				}
				String renderHtmlResult = mockRes.getDocument();
				String brNo = c160m01a.getOwnBrId();
				String mainId = c160m01a.getMainId();
				String fieldId = MEGAIMAGE_RPTPRINT_DOCFILE_FIELDID;
				String rptName = "cls1131p02";
				byte[] data = renderHtmlResult.getBytes("utf-8");
				String contentType = "html";
				// 設定上傳檔案資訊
				DocFile docFile = createDocFile(brNo, mainId, fieldId, custId, custName, rptName, data, contentType);
				// 存檔
				String docFileOid = docFileService.save(docFile);
				params.put("formId", "ECL00004");
				params.put("stakeholderID", custId);
				params.put("docFileOid", docFileOid);
				params.put("bean", c160m01a);
				paramsList.add(params);
			}
		}
		return paramsList;
	}

	/**
	 * 產生簽報書列印檔文件上傳參數
	 * 
	 * @param l120m01as
	 * @return
	 * @throws FileNotFoundException
	 * @throws IOException
	 * @throws Exception
	 */
	private List<PageParameters> getL120M01ARptPrintParams(L120M01A l120m01a, List<String> formIds)
			throws FileNotFoundException, IOException, Exception {
		List<PageParameters> paramsList = new ArrayList<PageParameters>();
		// 2.取得列印選單全選資料列印參數
		List<Map<String, Object>> printReports = queryPrint4CLS1141(l120m01a, "A");
		// 3.取得列印參數
		paramsList.addAll(getAllPrintParams(l120m01a, printReports));
		// 4.取得簡易列印列印參數
		paramsList.add(getSimplePrintPrintParams(l120m01a));
		// 5.依上傳代碼formIds來篩選取得列印檔案
		List<PageParameters> filterParamsList = new ArrayList<PageParameters>();
		for (PageParameters params : paramsList) {
			String formId = params.getString("formId");
			if (formIds.contains(formId)) {
				filterParamsList.add(params);
			}
		}
		for (PageParameters params : filterParamsList) {
			// 5.1.列印
			OutputStream outputStream = null;
			try {
				outputStream = cls1141r01rptservice.generateReport(params);
			} catch (Exception e) {
				LOGGER.info(params.toString() + " 產生列印檔時發生錯誤");
				LOGGER.error(StrUtils.getStackTrace(e));
				continue;
			}
			// 5.2.個別報表編號建立DocFile
			String custId = params.getString("custId");
			String custName = params.getString("custName");
			String rptName = params.getString("rptName");
			String brNo = l120m01a.getOwnBrId();
			String mainId = l120m01a.getMainId();
			String fieldId = MEGAIMAGE_RPTPRINT_DOCFILE_FIELDID;
			byte[] data = ((ByteArrayOutputStream) outputStream).toByteArray();
			// 設定上傳檔案資訊
			DocFile docFile = createDocFile(brNo, mainId, fieldId, custId, custName, rptName, data, "pdf");
			// 存檔
			String docFileOid = docFileService.save(docFile);
			params.put("docFileOid", docFileOid);
		}
		return filterParamsList;
	}

	private PageParameters getSimplePrintPrintParams(L120M01A l120m01a) throws FileNotFoundException, IOException,
			Exception {
		// 簡易列印
		List<String> simplePrintRptOids = new ArrayList<String>();
		List<Map<String, Object>> simplePrintReports = queryPrint4CLS1141(l120m01a, "simplePrint");
		String[] keys = new String[] { "rpt", "oid", "custId", "dupNo", "cntrNo", "refMainId", "keyCustId", "keyDupNo" };
		for (Map<String, Object> map : simplePrintReports) {
			List<String> vals = new ArrayList<String>();
			for (String key : keys) {
				String val = CapString.trimNull(map.get(key));
				vals.add(val);
			}
			String content = StringUtils.join(vals, "^");
			simplePrintRptOids.add(content);
		}
		String allSimplePrintRptOid = StringUtils.join(simplePrintRptOids, "|");
		// ECL00009=簡易列印
		String formId = "ECL00009";
		PageParameters parmas = new CapMvcParameters();
		parmas.put("formId", formId);
		parmas.put("stakeholderID", l120m01a.getCustId());
		parmas.put("mainId", l120m01a.getMainId());
		parmas.put("rptOid", allSimplePrintRptOid);
		parmas.put("fileDownloadName", "CLS1141R01");
		return parmas;
	}

	/**
	 * 取得列印選單資料
	 * 
	 * @param l120m01a
	 * @param printCondition
	 * @return
	 * @throws FileNotFoundException
	 * @throws IOException
	 * @throws Exception
	 */
	private List<Map<String, Object>> queryPrint4CLS1141(L120M01A l120m01a, String printCondition)
			throws FileNotFoundException, IOException, Exception {
		ISearch pageSetting = new AbstractSearchSetting() {
			private static final long serialVersionUID = 1L;
		};
		String mainId = l120m01a.getMainId();
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.setDistinct(true);
		pageSetting.setMaxResults(Integer.MAX_VALUE);
		pageSetting.setFirstResult(0);
		Page<Map<String, Object>> page = cls1141Service.getBorrows(mainId, printCondition, pageSetting);
		List<Map<String, Object>> printReports = page.getContent();
		return printReports;
	}

	/**
	 * 取得簽報書列印參數
	 * 
	 * @param l120m01a
	 * @param printReports
	 * @return
	 * @throws FileNotFoundException
	 * @throws IOException
	 * @throws Exception
	 */
	private List<PageParameters> getAllPrintParams(L120M01A l120m01a, List<Map<String, Object>> printReports)
			throws FileNotFoundException, IOException, Exception {
		String mainId = l120m01a.getMainId();
		String mainCustId = l120m01a.getCustId();
		List<PageParameters> paramsList = new ArrayList<PageParameters>();
		String[] keys = new String[] { "rpt", "oid", "custId", "dupNo", "cntrNo", "refMainId", "keyCustId", "keyDupNo" };
		List<String> contents = new ArrayList<String>();
		for (Map<String, Object> map : printReports) {
			List<String> vals = new ArrayList<String>();
			for (String key : keys) {
				String val = CapString.trimNull(map.get(key));
				vals.add(val);
			}
			String content = StringUtils.join(vals, "^");
			// 1.收集全部一起印的資料
			contents.add(content);
			// 2.個別報表編號列印參數設定
			String custId = CapString.trimNull(map.get("custId"));
			String rptNo = CapString.trimNull(map.get("rptNo"));
			String formId = getFormId(rptNo);
			if (CapString.isEmpty(custId)) {
				custId = mainCustId;
			}
			// 預設為主借款ID
			String stakeholderID = mainCustId;
			// 若不同時，設為該ID
			if (!mainCustId.equals(custId)) {
				stakeholderID = custId;
			}
			PageParameters params = new CapMvcParameters();
			params.put("formId", formId);
			params.put("custId", custId);
			params.put("stakeholderID", stakeholderID);
			params.put("mainId", mainId);
			params.put("rptOid", content);
			params.put("fileDownloadName", rptNo);
			paramsList.add(params);
		}
		// // 3.全部一起印的資料用"|"分隔，rptNo=CLS1141R01，增加到列印MAP
		// String allContents = StringUtils.join(contents, "|");
		// String formId = getFormId("");
		// PageParameters cls1141R01params = new PageParameters();
		// cls1141R01params.put("formId", formId);
		// cls1141R01params.put("stakeholderID", l120m01a.getCustId());
		// cls1141R01params.put("mainId", mainId);
		// cls1141R01params.put("rptOid", allContents);
		// cls1141R01params.put("fileDownloadName", "CLS1141R01");
		// paramsList.add(cls1141R01params);
		return paramsList;
	}

	private String getFormId(String rptNo) {
		// FTP上傳資料
		CodeType formIdCodeType = codeTypeService.findByTypeAndDesc2("MEGAIMAGE_FormId_ALL", rptNo, "zh_TW");
		if (formIdCodeType == null) {
			// 9=其他
			formIdCodeType = codeTypeService.findByTypeAndDesc2("MEGAIMAGE_FormId_ALL", "9", "zh_TW");
		}
		String formId = formIdCodeType.getCodeValue();
		return formId;
	}

	private DocFile createDocFile(String brNo, String mainId, String fieldId, String custId, String custName,
			String rptName, byte[] data, String contentType) {
		// 設定上傳檔案資訊
		DocFile docFile = new DocFile();
		docFile.setBranchId(brNo);
		if ("pdf".equals(contentType)) {
			docFile.setContentType("application/pdf");
		} else if ("html".equals(contentType)) {
			docFile.setContentType("text/html");
		}
		docFile.setMainId(mainId);
		docFile.setPid(mainId);
		docFile.setCrYear(CapDate.getCurrentDate("yyyy"));
		docFile.setFieldId(fieldId);
		docFile.setDeletedTime(null);
		docFile.setUploadTime(CapDate.getCurrentTimestamp());
		docFile.setSrcFileName(custId + " " + custName + rptName + CapDate.getCurrentDate("yyyy-MM-dd") + "."
				+ contentType);
		docFile.setSysId(docFileService.getSysId());
		docFile.setFileSize(data.length);
		docFile.setFileDesc(rptName + "(系統產生)");
		docFile.setData(data);
		return docFile;
	}

	/**
	 * <pre>
	 * J-111-0223 新增eloan消金文件影像查詢匯入功能<BR>
	 * params必要欄位:formId,stakeholderID,docFileOid
	 * </pre>
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws Exception
	 * @throws CapException
	 */
	private void eLoanUploadImageFile(GenericBean bean, PageParameters params) throws CapException {
		String formId = params.getString("formId");
		String stakeholderID = params.getString("stakeholderID");
		String docFileOid = params.getString("docFileOid");
		String mainId = "";
		String applicationDate = "";
		String borrower = "";
		String branch = "";
		String caseNo = "";
		String userCode = "system";
		if (bean instanceof C900M01M) {
			C900M01M c900m01m = (C900M01M) bean;
			mainId = c900m01m.getMainId();
			applicationDate = c900m01m.getApplicationDate();
			borrower = c900m01m.getBorrower();
			branch = c900m01m.getBranch();
			caseNo = c900m01m.getCaseNo();
			userCode = c900m01m.getUserCode();
		}
		if (bean instanceof L120M01A) {
			L120M01A l120m01a = (L120M01A) bean;
			mainId = l120m01a.getMainId();
			applicationDate = CapDate.formatDate(l120m01a.getEndDate(), "yyyyMMdd");
			borrower = l120m01a.getCustId();
			branch = l120m01a.getOwnBrId();
			caseNo = l120m01a.getCaseNo();
		}
		if (bean instanceof C160M01A) {
			C160M01A c160m01a = (C160M01A) bean;
			mainId = c160m01a.getMainId();
			applicationDate = CapDate.formatDate(c160m01a.getApproveTime(), "yyyyMMdd");
			borrower = c160m01a.getCustId();
			branch = c160m01a.getOwnBrId();
			caseNo = c160m01a.getCaseNo();
		}
		C900M01M c900m01m = new C900M01M();
		if (bean instanceof C900M01M) {
			c900m01m = (C900M01M) bean;
		}
		try {
			Map<String, File> realFileMap = new HashMap<String, File>();
			DocFile docFile = docFileService.findByOidAndSysId(docFileOid, docFileService.getSysId());
			if (docFile == null) {
				return;
			}
			File file = docFileService.getRealFile(docFile);
			if (file == null || !file.exists()) {
				try {
					int tryCount = 0;
					// 先睡2後每2秒重試1次，重試10次共 20秒
					while (tryCount < 10 && (file == null || !file.exists())) {
						Thread.sleep(2 * 1000);
						LOGGER.info("Select File count:" + (tryCount + 1));
						file = docFileService.getRealFile(docFile);
						tryCount++;
					}
					LOGGER.info("Select File :" + (file == null || !file.exists()));
				} catch (InterruptedException e) {
					LOGGER.info("Select File :" + (file == null || !file.exists()));
				}
			}
			realFileMap.put(docFileOid, file);
			if (!CapString.isEmpty(formId) && !CapString.isEmpty(docFileOid)) {
				// 上傳指定項目
				c900m01m.setApplicationDate(applicationDate);
				c900m01m.setBorrower(borrower);
				c900m01m.setBranch(branch);
				c900m01m.setCaseNo(caseNo);
				c900m01m.setDocFileOid(docFileOid);
				c900m01m.setFormId(formId);
				c900m01m.setMainId(mainId);
				c900m01m.setStakeholderID(stakeholderID);
				c900m01m.setUpdateTime(CapDate.getCurrentTimestamp());
				c900m01m.setUserCode(userCode);
			}
			// 依上傳紀錄檔內容與文件類型執行上傳文件數位化作業
			clsService.eLoanUploadImageFile(c900m01m, bean);
		} catch (Exception e) {
			LOGGER.error(StrUtils.getStackTrace(e));
			throw new CapException(e, getClass());
		}
	}

}
