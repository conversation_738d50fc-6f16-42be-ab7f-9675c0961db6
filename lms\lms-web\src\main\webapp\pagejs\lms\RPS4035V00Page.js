$(function(){
    var Idtemp = '';
    /***執行輸入查詢ID視窗***/
    openQuery();
    /***本行婉卻記錄LocalGrid***/
    var localgrid = $("#LocalGrid").iGrid({
        handler: 'rps4035gridhandler',
        height: 100,
        width: 260,
        colModel: [{
            colHeader: i18n.rps4035v00["lnunid.rEGDT"], //登錄日期時間
            name: 'regdt',
            align: "center",
            width: 70,
            sortable: true
        }, {
            colHeader: i18n.rps4035v00["lnunid.rEGBR"], //婉卻分行
            name: 'regbr',
            align: "left",
            width: 50
        }, {
            colHeader: i18n.rps4035v00["lnunid.title10"], //lnunid.title10=婉卻業務別
            name: 'clscase',
            align: "left",
            width: 50
        }, {
            colHeader: i18n.rps4035v00["lnunid.rEFUSEDS"], //婉卻說明
            name: 'refuseds',
            align: "left",
            width: 180
        }, {
            colHeader: i18n.rps4035v00["lnunid.statuscd"], //婉卻狀態
            name: 'statuscd',
            align: "left",
            width: 40
        }, {
            colHeader: i18n.rps4035v00["lnunid.rEGTELLER"], //登錄行員代號
            name: 'regteller',
            align: "left",
            width: 55
        }, {
            colHeader: i18n.rps4035v00["lnunid.dUPNO"], //重複序號
            name: 'dupno',
            align: "center",
            width: 60,
            hidden: true
        }, {
            colHeader: i18n.rps4035v00["lnunid.cUSTNM"], //客戶名稱
            name: 'custnm',
            align: "left",
            width: 60,
            hidden: true
        }]
    });
    
    /***金控婉卻記錄HoldingGrid***/
    var holdinggrid = $("#HoldingGrid").iGrid({
        handler: 'rps4035gridhandler',
        height: 100,
        width: 360,
        colModel: [{
            colHeader: i18n.rps4035v00["lnunid02.rEGDT"],//i18n.lnunid02["lnunid02.rEGDT"], //登錄日期時間
            name: 'regdt',
            align: "center",
            width: 60
        }, {
            colHeader: i18n.rps4035v00["lnunid.title11"], //lnunid.title11=卡貸婉卻
            name: 'refusecd',
            align: "right",
            width: 120
        }, {
            colHeader: i18n.rps4035v00["lnunid.rEFUSEDS"],//lnunid.rEFUSEDS=婉卻說明
            name: 'refuseds',
            align: "left",
            width: 180
        }, {
            colHeader: i18n.rps4035v00["lnunid02.dUPNO"], //重複序號
            name: 'dupno',
            align: "center",
            width: 60,
            hidden: true
        }, {
            colHeader: i18n.rps4035v00["lnunid02.cUSTNM"], //客戶名稱
            name: 'custnm',
            align: "left",
            width: 60,
            hidden: true
        }]
    });
});

/***輸入查詢ID視窗***/
function openQuery(){
    $("#borrower-data000").thickbox({ // 使用選取的內容進行彈窗
        //lnunid.title01=請輸入欲查詢婉卻紀錄的客戶統一編號
        title: i18n.rps4035v00['lnunid.title01'],
        width: 400,
        height: 200,
        valign: "bottom",
        align: "center",
        i18n: i18n.def,
        buttons: {
            sure: function(){
                $("#custNm").html("");
                conFirm();
            },
            cancel: function(){
                cancel();
            }
        }
    });
}

/***輸入查詢ID視窗之確定button***/
function conFirm(){
    if ($("#textfield_id").val().length != 10 && $("#textfield_id").val().length != 8) {
        //lnunid.title02= 【注意】客戶統一編號長度有誤
        API.showErrorMessage(i18n.rps4035v00['lnunid.title02']);
        $("#textfield_id").val('');
    } else if ($("#textfield_id").val().length == 0) {
        cancel();
    } else {
        Idtemp = $("#textfield_id").val();
        $.thickbox.close();
        
        $.ajax({ //載入Form資料(查詢日期、查詢人員、統一編號、客戶名稱)
            handler: "rps4035m01formhandler",
            data: {
                formAction: "query",
                custId: Idtemp,
				dupNo:$("#textfield_DupNo").val()
            },
            success: function(obj){
                //表示相同統編 有不同重覆序號的選項
                $("#tabFormtest").injectData(obj);//將讀取好的資料存進前端網頁中
                if (obj.size > 1) {
                    selectDupNo(obj);
                } else {
                    LocalGridStart(DOMPurify.sanitize(obj.dupNo)); //載入LocalGrid	更新畫面				
                    HoldingGridStart(DOMPurify.sanitize(obj.dupNo)); //載入HoldingGrid更新畫面
                    $("#dupNo").html(DOMPurify.sanitize(obj.dupNo));
                    $("#custNm").html(DOMPurify.sanitize(obj.custNm));
                    opendocDocLogView();
                }
                
            },
            error: function(responseData){
            }
        });
        
        
    }
}


function opendocDocLogView(){
    $("#docLogView").thickbox({ // 大thickbox
        //lnunid.title04=婉卻記錄查詢
        title: i18n.rps4035v00['lnunid.title04'],
        width: 800,
        height: 535,
        modal: false,
        buttons: API.createJSON([{
            //lnunid.bt01=資料查詢
            key: i18n.rps4035v00['lnunid.bt01'],
            value: function(){
                $("#textfield_id").val('');
                $("#cUSTNM").val('');
                $("#dUPNO").val('');
                $.thickbox.close();
                openQuery();
                $("#LocalGrid").trigger("reloadGrid");
            }
        }, {
            key: i18n.def['print'],
            value: function(){
            
                $.form.submit({
                    url: "../simple/FileProcessingService",
                    target: "_blank",
                    data: {
                        custId: $("#custId").html(),
                        dupNo: $("#dupNo").html(),
                        custName: $("#custNm").html(),
                        fileDownloadName: "rps4035r01.pdf",
                        serviceName: "rps4035r01rptservice"
                    }
                });
            }
        }, {
            key: i18n.def['close'],
            value: function(){
                $("#textfield_id").val('');
                $.thickbox.close();
            }
        }])
    });
}


function selectDupNo(obj){
    $("#selectPeople").setItems({ 
        item: obj.custList,
        format: "{value} {key}"
    });
    $("#selectPeopleBox").thickbox({
        //lnunid.title09=選擇所輸入統編之借款人
        title: i18n.rps4035v00["lnunid.title09"],
        width: 300,
        height: 150,
        modal: true,
        align: "center",
        valign: "bottom",
        i18n: i18n.def,
        buttons: {
            "sure": function(){
                var dupNo = DOMPurify.sanitize($("#selectPeople").val());
                if (dupNo == "") {
                    return CommonAPI.showMessage(i18n.def["grid.selrow"]);
                }
                var custName = DOMPurify.sanitize($("#selectPeople :selected").text());
                LocalGridStart(dupNo); //載入LocalGrid	更新畫面				
                HoldingGridStart(dupNo); //載入HoldingGrid更新畫面
                $("#dupNo").html(DOMPurify.sanitize(dupNo));
                $("#custNm").html(custName.slice(2));
                $.thickbox.close();
                opendocDocLogView();
            },
            "cancel": function(){
                $.thickbox.close();
            }
        }
    });
}

/***輸入查詢ID視窗之取消button***/
function cancel(){
    //lnunid.title03 = 婉卻記錄查詢之統一編號尚未輸入,要放棄登入嗎
    API.confirmMessage(i18n.rps4035v00['lnunid.title03'], function(b){
        if (b) {
            $.thickbox.close();
            
        } else {
            $("#textfield_id").val('');
        }
    });
}



/***本行婉卻記錄LocalGrid更新畫面***/
function LocalGridStart(dupNo){
    $("#LocalGrid").jqGrid("setGridParam", {
        postData: {
            custId: Idtemp,
            dupNo: dupNo,
            formAction: "queryLocalGrid"
        },
        search: true
    }).trigger("reloadGrid");
}

/***金控婉卻記錄Gird更新畫面***/
function HoldingGridStart(dupNo){
    $("#HoldingGrid").jqGrid("setGridParam", {
        postData: {
            custId: Idtemp,
            dupNo: dupNo,
            formAction: "queryHoldingGrid"
        },
        search: true
    }).trigger("reloadGrid");
}
