package com.mega.eloan.lms.lms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;

/**
 * <pre>
 * 停權解除維護(已覆核)
 * </pre>
 * 
 * @since 2013/1/21
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/1/21,<PERSON>,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms7850v03")
public class LMS7850V03Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		// 設定文件狀態(交易代碼)
		setGridViewStatus(CreditDocStatusEnum.授管處_停權已覆核);
		// 加上Button
		addToButtonPanel(model, LmsButtonEnum.View);
		renderJsI18N(LMS7850V03Page.class);
		model.addAttribute("hasHtml", false);
		model.addAttribute("loadScript", "loadScript('pagejs/lms/LMS7850V03Page');");
	}// ;


}
