/* 
 * CtrConstants.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.ctr.constants;

/**
 * <pre>
 * 約據書  Constants
 * </pre>
 * 
 * @since 2012/8/14
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/8/14,REX,new
 *          </ul>
 */
public interface CtrConstants {

	/**
	 * 約據書XmL檔
	 */
	interface LMS999XMLFile {
		static final String 綜合授信契約書 = "LMS9990W01.xml";
		static final String 綜合授信契約書_直 = "LMS9990W01V.xml";
		static final String 綜合授信契約書_橫式直 = "LMS9990W01HV.xml";
		static final String 連帶保證書 = "LMS9990W02.xml";
		static final String 授信約定書 = "LMS9990W03.xml";
		static final String 中長期契約書 = "LMS9990W04.xml";
		static final String 股東債權同意書 = "LMS9990W05.xml";
		static final String 本票授權契約書 = "LMS9990W06.xml";
		static final String 開發信用狀約定書 = "LMS9990W07.xml";
		static final String 應收帳款承購約定書 = "LMS9990W08.xml";
	}

	/**
	 * 約據書種類
	 */
	interface ContractType {
		static final String 綜合授信契約書 = "01";
		static final String 綜合授信契約書_直 = "08";
		static final String 綜合授信契約書_橫式直 = "09";
		static final String 連帶保證書 = "02";
		static final String 授信約定書 = "03";
		static final String 中長期契約書 = "04";
		static final String 股東債權同意書 = "05";
		static final String 本票授權契約書 = "06";
		static final String 開發信用狀約定書 = "07";
		static final String 應收帳款承購約定書 = "A1";
	}

	/**
	 * 綜合授信契約書借款種類檔 -借款種類
	 */
	interface L999S01BItemType {
		static final String 購料借款 = "A";
		static final String 外銷借款 = "B";
		static final String 營運週轉借款 = "C";
		static final String 貼現 = "D";
		static final String 透支 = "E";
		static final String 委任票據保證 = "F";
		static final String 委任票據承兌 = "G";
		static final String 委任保證 = "H";
	}

	/**
	 * 企金約據書項目描述檔- 借款種類
	 */
	interface L999M01DItemType {
		static final String 特別條款 = "A";
		static final String 修正條款說明 = "B";
		static final String 授信總額度 = "C";
	}

	/**
	 * 中長期契約書授信內容及條件檔
	 */
	interface L999S04BItemType {
		static final String 授信用途 = "A";
		static final String 授信金額 = "B";
		static final String 動用方式及條件 = "C";
		static final String 撥款方式 = "D";
		static final String 償還期限及方式 = "E";
		static final String 利息手續費計付 = "F";
		static final String 違約金及遲延利息計付 = "G";
		static final String 期前清償違約金計付 = "H";
		static final String 承諾費 = "I";
		static final String 其他個別商議條件 = "J";
	}

	/**
	 * 立約人種類
	 * <p/>
	 * 1.兆豐國際商業銀行股份有限公司(甲方)<br/>
	 * 2.借款人(乙方)
	 */
	interface L999M01BType {
		static final String 銀行甲方 = "1";
		static final String 借款人乙方 = "2";
	}

	/**
	 * CodeType
	 */
	interface CodeType {
		static final String 綜合授信契約書借款種類 = "lms9990s01_itemType";
		static final String 企金約據書種類 = "lms9990m01_contractType";
		static final String 個金約據書種類 = "lms9990m01c_contractType";
		static final String 個金約據書類型 = "lms9990m01c_contractKind";
		static final String 兆豐集團公司清單 = "megacompany";
		static final String 台灣法院清單 = "taiwancourt";

	}

	/** 個金約據書 */
	interface Book9990 {
		/**
		 * 約據書XmL檔
		 */
		interface LMS999XMLFile {
			/**
			 * 契約書XmL檔
			 */
			interface kind1 {
				static final String 一般借款契約書含搭配內政部青年安家方案版本 = "LMS9990W01C.xml";
			}

			/**
			 * 切結書XmL檔
			 */
			interface kind2 {
				static final String 無辦理其他政府政策性住宅補貼切結書 = "LMS9990W02C.xml";
				static final String 同意處份設質股票切結書存證信函 = "LMS9990W03C.xml";
				static final String 同意處份設質股票切結書_附同意書 = "LMS9990W04C.xml";
				static final String 打房條款切結書_聯徵有1者 = "LMS9990W05C.xml";
				static final String 打房條款切結書_聯徵無1者 = "LMS9990W06C.xml";
				static final String 優惠貸款未重複借款切結書 = "LMS9990W07C.xml";
				static final String 現職員工及配偶額度未逾1500萬元同意書 = "LMS9990W08C.xml";
				static final String 租賃權益拋棄承諾書_法務處修訂意見_租賃權益拋棄承諾書 = "LMS9990W09C.xml";
				static final String 轉貸款約定書 = "LMS9990W10C.xml";
				static final String 屬自住型房貸_自住切結書_惟不適用央行受限戶 = "LMS9990W11C.xml";
				static final String 屬自住型房貸_換屋切結書_惟不適用央行受限戶 = "LMS9990W12C.xml";
				static final String 兆豐連帶保證書_橫 = "LMS9990W13C.xml";
				static final String 質權設定契約書 = "LMS9990W14C.xml";
			}

			/**
			 * 同意書XmL檔
			 */
			interface kind3 {
				static final String 處份同意書_股票借款 = "LMS9990W15C.xml";
				static final String 同意書_不動產抵押所有人用 = "LMS9990W16C.xml";
				static final String 連保人同意書_連保人續約同意書 = "LMS9990W17C.xml";
				static final String 擔保借款抵押權設定種類約款 = "LMS9990W18C.xml";
				static final String 整批房貸同意書 = "LMS9990W19C.xml";
			}

			/**
			 * 宣告書XmL檔
			 */
			interface kind4 {
				static final String 歡喜理財家或綜合理財房屋貸款 = "LMS9990W20C.xml";
				static final String 理財型貸款或消費性貸款 = "LMS9990W21C.xml";
				static final String 銀行法12條之1 = "LMS9990W22C.xml";
			}

			/**
			 * 借款申請書XmL檔
			 */
			interface kind5 {
				static final String 申請書_利率由季變動改月變動 = "LMS9990W23C.xml";
			}

			/**
			 * 增補條款XmL檔
			 */
			interface kind6 {
				static final String 增補條款_空版 = "LMS9990W24C.xml";
				static final String 增補條款_內文選項 = "LMS9990W25C.xml";
			}

			/**
			 * 其他XmL檔
			 */
			interface kind7 {
				static final String 本票 = "LMS9990W26C.xml";
				static final String 本票授權書 = "LMS9990W27C.xml";
				static final String 個人借款支用書 = "LMS9990W28C.xml";
				static final String 消費金融專用借款申請書及房屋貸款特別提醒事項_A4格式_借款申請書_房屋貸款特別提醒事項 = "LMS9990W29C.xml";
				static final String 房屋貸款撥款委託書 = "LMS9990W30C.xml";
			}

			/**
			 * 政策留貸XmL檔
			 */
			interface kind8 {
				static final String 教育部補助留學生借款契約書 = "LMS9990W31C.xml";
				static final String 兆銀總授管字第5054號_附件9_政策留貸_切結書 = "LMS9990W32C.xml";
				static final String 兆銀總授管字第5054號_附件11_政策留貸延期清償申請書暨切結書 = "LMS9990W33C.xml";
				static final String 兆銀總授管字第5054號_附件13_政策留貸_修畢碩士學位後繼續修讀博士延長貸款期限及寬限期申請書 = "LMS9990W34C.xml";
				static final String 兆銀總授管字第5054號_附件14_政策留貸_授權書 = "LMS9990W35C.xml";
				static final String 個金2686_附件4通知書_留學生本人為申請人用 = "LMS9990W36C.xml";
				static final String 個金2686_附件7通知書_留學生本人為申請人用_英文 = "LMS9990W37C.xml";
				static final String 教育部補助留學生就學貸款申請書每人信用查詢費300元 = "LMS9990W38C.xml";
				static final String 符合教育部採認規定之國外大學院校及其他事項確認查詢單 = "LMS9990W39C.xml";
				static final String 無法於寬限期內完成學業延期清償申請書 = "LMS9990W40C.xml";
			}
		}

		/** 約據書種類 */
		interface contractType {
			static final String 一般 = "A";
			static final String 政策性留學貸款 = "B";
		}

		/** 約據書類型 */
		interface contractKind {
			static final String 契約書含個別商議 = "A";
			static final String 切結書 = "B";
			static final String 同意書 = "C";
			static final String 宣告書 = "D";
			static final String 增補條款含個別商議 = "E";
			static final String 其他 = "F";
		}

		/** 分行/全行 */
		interface CreateType {
			// TODO 待取得類型參數，目前暫時設定成分行：1,全行：2
			static final String 分行 = "1";
			static final String 全行 = "2";
		}

		/** 立約人種類 */
		interface KindType {
			static final String 借款人_甲方 = "2";
			static final String 兆豐國際商業銀行股份有限公司_乙方 = "1";
		}

		/** 產品種類 */
		interface ProdKind {
			// TODO 待Jason建立額度明細表Table後修改
			static final String 產品種類暫定1 = "1";
			static final String 產品種類暫定2 = "2";
			static final String 產品種類暫定3 = "3";
			static final String 產品種類暫定4 = "4";
			static final String 產品種類暫定5 = "5";
			static final String 產品種類暫定6 = "6";
			static final String 產品種類暫定7 = "7";
			static final String 產品種類暫定8 = "8";
			static final String 產品種類暫定9 = "9";
			static final String 產品種類暫定10 = "10";
		}

		/** 契約項目 */
		interface Type {
			static final String 契約金額 = "A";
			static final String 契約金額_政策留貸 = "A01";
			static final String 借款用途 = "B";
			static final String 借款用途_政策留貸 = "B01";
			static final String 動用方式 = "C";
			static final String 動用方式_政策留貸 = "C01";
			static final String 撥款方式 = "D";
			static final String 撥款方式_政策留貸 = "D01";
			static final String 償還辦法 = "E";
			static final String 償還辦法_政策留貸 = "E01";
			static final String 利息計付_限制清償期間 = "F01";
			static final String 利息計付_得隨時清償 = "F02";
			static final String 利息計付_政策留貸 = "F03";
			static final String 違約金及遲延利息 = "G";
			static final String 違約金及遲延利息_政策留貸 = "G01";
			static final String 資料提供 = "H";
			static final String 資料提供_政策留貸 = "H01";
			static final String 服務 = "I";
			static final String 服務_政策留貸 = "I01";
			static final String 管轄法院 = "J";
			static final String 管轄法院_政策留貸 = "J01";
			static final String 申請方式及借款期限_寬限期 = "K";
		}

		/** 性質(相關身份) */
		interface CustPos {
			static final String 共同借款人或共同發票人 = "C";
			static final String 票據債務人_指金融交易之擔保背書 = "E";
			static final String 連帶保證人或擔保品提供人兼連帶保證人 = "G";
			static final String 連帶債務人或擔保品提供人兼連帶債務人 = "L";
			static final String 一般保證人或擔保品提供人兼一般保證人 = "N";
			static final String 擔保品提供人 = "S";
		}

		/** 項次 */
		interface ItemNo {
			static final String 甲 = "1";
			static final String 乙 = "2";
			static final String 丙 = "3";
			static final String 丁 = "4";
			static final String 戊 = "5";
			static final String 己 = "6";
			static final String 庚 = "7";
			static final String 辛 = "8";
			static final String 壬 = "9";
			static final String 癸 = "10";
			static final String 子 = "11";
			static final String 丑 = "12";
			static final String 寅 = "13";
			static final String 卯 = "14";
			static final String 辰 = "15";
			static final String 巳 = "16";
			static final String 午 = "17";
			static final String 未 = "18";
			static final String 申 = "19";
			static final String 酉 = "20";
			static final String 戌 = "21";
			static final String 亥 = "22";
			static final String 刪除 = "99";
		}

		/** 項目 */
		interface itemType {
			static final String 特別條款 = "A";
			static final String 修正條款說明 = "B";
			static final String 個別商議條款 = "C";
		}

		/** 頁籤名稱 */
		interface Page {
			static final int 契約金額 = 2;
			static final int 契約金額_政策留貸 = 10;
			static final int 借款用途 = 3;
			static final int 借款用途_政策留貸 = 11;
			static final int 申請方式及借款期限_寬限期 = 9;
			static final int 動用方式 = 4;
			static final int 動用方式_政策留貸 = 12;
			static final int 撥款方式 = 5;
			static final int 撥款方式_政策留貸 = 18;
			static final int 償還辦法 = 6;
			static final int 償還辦法_政策留貸 = 19;
			static final int 利息計付1 = 701;
			static final int 利息計付2 = 702;
			static final int 利息計付_政策留貸 = 20;
			static final int 違約金及遲延利息 = 13;
			static final int 違約金及遲延利息_政策留貸 = 14;
			static final int 資料提供 = 151;
			static final int 資料提供_政策留貸 = 152;
			static final int 服務 = 161;
			static final int 服務_政策留貸 = 162;
			static final int 管轄法院 = 171;
			static final int 管轄法院_政策留貸 = 172;
		}

		/** Radio名稱 */
		interface RadioName {
			static final String 動用方式Radio = "20Ara";
			static final String 借款用途Radio = "19Ac7";
			static final String 撥款方式Radio = "21Ara";
			static final String 償還辦法Radio = "22Ara";
			static final String 利息計付_限制清償期間Radio = "23Ara";
			static final String 利息計付_得隨時清償Radio = "23Arb";
		}

		/** CheckBoxId */
		interface CheckBoxId {
			static final String 借款用途CheckBox = "19Ac7";
			static final String 動用方式CheckBox = "20Aca";
			static final String 撥款方式CheckBox = "21Aca";
			static final String 償還辦法CheckBox = "22Aca";
			static final String 利息計付_限制清償期間CheckBox = "23Aca";
			static final String 利息計付_得隨時清償CheckBox = "23Acb";
		}
	}
}
