<?xml version="1.0" encoding="UTF-8"?>
 <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:wicket="http://wicket.apache.org/">
    <body>
        <wicket:panel>
        	<div>
        		<span class="text-red"><wicket:message key="title.clsRating">依據本行「消金授信業務信用評等應用細則」第三條：「對於消金授信之借款人、連帶保證人或共同借款人，應分別辦理信用評等，並就其評等結果擇一適用</wicket:message>
				</span>
        	</div>
            <fieldset>
                <legend>
                    <b><wicket:message key="title.custDetial">借款人明細</wicket:message></b>
                </legend>
                <button type="button" id="importLendCollateral">
                    <span class="text-only"><wicket:message key="button.importLendCollateral">引進借保人</wicket:message></span>
                </button>
                <button type="button" id="addLendCollateral">
                    <span class="text-only"><wicket:message key="button.addLendCollateral">新增擔保品提供人</wicket:message></span>
                </button>
                <button type="button" id="setKeyManBt">
                    <span class="text-only"><wicket:message key="title.setMainCust">設定主要借款人</wicket:message></span>
                </button>
				<button type="button" id="setCustShowSeqNum">
                    <span class="text-only"><wicket:message key="button.chgCustShowSeqNum">調整順序</wicket:message></span>
                </button>
				<br/>
				<span class="text-red">※<wicket:message key="button.mome03">借款人基本資料如果重新引進或刪除後要注意額度明細表如有採用到異動的資料，額度明細表要記得重新引進一遍借保人。</wicket:message></span>
                <br/>
                <!-- gird -->
                <div id="gridview">
                </div>
                <br/>
                <span class="text-red"><wicket:message key="CLS1141.082">※備註：C.共同借款人, G.連帶保證人, N.ㄧ般保證人, S.擔保品提供人(於「額度明細表」設定借保人後顯示) </wicket:message></span>
            </fieldset>
			
            <!-- javascript --><script type="text/javascript" src="pagejs/cls/CLSS02CPanel.js?ver=201904"></script>
            <!-- 借保人資訊 -->
            <div id="ClsCustInfo" style="display:none;"/><!-- 借保人引進清單 -->
            <div id="ClsCustList" style="display:none;">
                <input type="text" id="findId" name="findId" maxlength="10" size="10" class="upText" />
                &nbsp;&nbsp;
                <button type="button" id="findIdBt">
                    <span class="text-only"><wicket:message key="button.filter">篩選</wicket:message></span>					
                </button>
				<br/>
				<span class="text-red">※<wicket:message key="button.mome01">1.本清單上所出現的名單，條件為徵信資料中聯徵及票信查詢日皆尚在一個月以內之資料。</wicket:message></span>
                <br/>
				<span class="text-red">※<wicket:message key="button.mome02">2.重新引進後要注意額度明細表如有採用到異動的資料額度明細表要記得重新引進一遍。</wicket:message></span>
                <br/>
                <div id="ClsCustGrid" />
            </div><!-- 選擇主要借款人 -->
            <div id="ClsSelectMainCustBox" style="display:none;">
                <div id="ClsSelectMainCustDiv">
                    <select name="ClsSelectMainCustList" id="ClsSelectMainCustList" />
                </div>
            </div>
			
            <!-- 選擇借款人引進類別-->
            <div id="selectCustTypeBox" style="display:none;">
                <div id="selectCustTypeDiv">
                    <label>
                        <input type="radio" id="selectCustType" name="selectCustType" value="1" checked="checked" />
                        <wicket:message key="CLS1141.098">引進借款人</wicket:message>
                    </label>
                    <br/>
                    <label>
                        <input type="radio" id="selectCustType" name="selectCustType" value="2" />
                        <wicket:message key="CLS1141.099">新增團貸母戶</wicket:message>
                    </label>
                </div>
            </div>
            <!-- 擔保品提供人視窗 -->
            <div id="openCustPosnSBox" style="display:none;">
                <div id="openCustPosnSBoxDiv">
                    <table class="tb2" width="100%">
                        <tr>
                            <td class="hd1" style="width:30%">
                                <wicket:message key="doc.id">統一編號</wicket:message>&nbsp;&nbsp;
                            </td>
                            <td>
                                <span id="custId" /><span id="dupNo" />
                            </td>
                        </tr>
                        <tr>
                            <td class="hd1">
                                <wicket:message key="doc.custName">姓名/名稱</wicket:message>&nbsp;&nbsp;
                            </td>
                            <td>
                                <span id="custName" />
                            </td>
                        </tr>
                        <tr>
                            <td class="hd1">
                                <wicket:message key="C120S01A.ntCode">國別</wicket:message>&nbsp;&nbsp;
                            </td>
                            <td>
                                <span id="ntCode" />
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
			<!-- 輸入國別視窗 -->
			<div id="openNtCodeBox" style="display:none;">
                <div id="openNtCodeBoxDiv">
                    <table class="tb2" width="100%">
                        <tr>
                            <td class="hd1" nowrap >
                                <wicket:message key="C120S01A.ntCode">國別</wicket:message>&nbsp;&nbsp;
                            </td>
                            <td>
                            	<!-- ntCode 給 "dispaly column" 使用 -->
                                <select id="input_ntCode"  name="input_ntCode" data-codetype="CountryCode" space="true" />
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
			
			<div id="_div_custShowSeqNum" style="display: none;">
				<form name='frm_custShowSeqNum' id='frm_custShowSeqNum'>
					<table class='tb2'>
						<thead>
							 <tr class='hd2'>
								<td><wicket:message key="C120M01A.custId" /></td>
								<td><wicket:message key="C120M01A.custName" /></td>
								<td>順序</td> 	
							 </tr>																			
						</thead>					
						<tbody id="_div_custShowSeqNum_content">
                		</tbody>
					</table>					
				</form>
            </div>
        </wicket:panel>
    </body>
</html>