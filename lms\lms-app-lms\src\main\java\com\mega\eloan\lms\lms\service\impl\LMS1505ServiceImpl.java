/* 
 * LMS1505ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.dao.ElsUserDao;
import com.mega.eloan.common.enums.DocAuthTypeEnum;
import com.mega.eloan.common.enums.DocLogEnum;
import com.mega.eloan.common.model.ElsUser;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dao.L150A01ADao;
import com.mega.eloan.lms.dao.L150M01ADao;
import com.mega.eloan.lms.lms.service.LMS1505Service;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L150A01A;
import com.mega.eloan.lms.model.L150M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 小放會LMS1505ServiceImpl
 * </pre>
 * 
 * @since 2011/10/21
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/10/21,Rex,new
 *          <li>2013/06/25,Rex,修改update在按下儲存後才寫入，以免使用這在做tempSave時就更新update
 *          </ul>
 */
@Service
public class LMS1505ServiceImpl extends AbstractCapService implements
		LMS1505Service {
	@Resource
	TempDataService tempDataService;
	@Resource
	L150M01ADao l150m01aDao;

	@Resource
	L150A01ADao l150a01aDao;

	@Resource
	L120M01ADao l120m01aDao;

	@Resource
	ElsUserDao elsUserDao;

	@Resource
	DocLogService docLogService;

	@Override
	public void saveL150m01a(L150M01A model) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		if (Util.isEmpty(model.getOid())) {
			model.setMainId(IDGenerator.getUUID());
			// docLogService.record(model.getOid(), DocLogEnum.CREATE);
			// 新增授權檔
			L150A01A l150a01a = new L150A01A();
			l150a01a.setAuthTime(CapDate.getCurrentTimestamp());
			l150a01a.setAuthType(DocAuthTypeEnum.MODIFY.getCode());
			l150a01a.setAuthUnit(user.getUnitNo());
			l150a01a.setMainId(model.getMainId());
			l150a01a.setOwner(user.getUserId());
			l150a01a.setOwnUnit(user.getUnitNo());
			l150a01aDao.save(l150a01a);
		}

		l150m01aDao.save(model);
		if (!"Y".equals(SimpleContextHolder.get(EloanConstants.TEMPSAVE_RUN))) {
			// start 2013/06/25,Rex,修改update在按下儲存後才寫入，以免使用這在做tempSave時就更新update
			model.setUpdater(user.getUserId());
			model.setUpdateTime(CapDate.getCurrentTimestamp());
			// end 2013/06/25,Rex,修改update在按下儲存後才寫入，以免使用這在做tempSave時就更新update
			tempDataService.deleteByMainId(model.getMainId());
			docLogService.record(model.getOid(), DocLogEnum.SAVE);
		}

	}

	@Override
	public void deleteL150m01aList(String[] oids) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<L150M01A> l150m01as = l150m01aDao.findByOids(oids);
		for (L150M01A l150m01a : l150m01as) {
			l150m01a.setDeletedTime(CapDate.getCurrentTimestamp());
			l150m01a.setUpdater(user.getUserId());
			docLogService.record(l150m01a.getOid(), DocLogEnum.DELETE);
		}

		l150m01aDao.save(l150m01as);
	}

	@Override
	public L150M01A findL150m01aByOid(String oid) {
		return l150m01aDao.find(oid);
	}

	@Override
	public L120M01A findL120M01AByOid(String oid) {
		return l120m01aDao.find(oid);
	}

	@Override
	public Page<L150M01A> findPage(ISearch search) {
		return l150m01aDao.findPage(search);
	}

	@Override
	public Page<Map<String, Object>> findPageby(String brNo, ISearch search) {
		List<Map<String, Object>> elsUserList = new ArrayList<Map<String, Object>>();
		List<ElsUser> elsUsers = elsUserDao.findByBrno(brNo);
		int start = search.getFirstResult();
		int pagNumber = search.getMaxResults();
		int end = start + pagNumber > elsUsers.size() ? start
				+ (elsUsers.size() - start) : start + pagNumber;
		for (int b = start; b < end; b++) {
			ElsUser elsuser = elsUsers.get(b);
			Map<String, Object> data = new HashMap<String, Object>();
			data.put("userId", Util.trim(elsuser.getUserId()));
			data.put("userName", Util.trim(elsuser.getUserName()));
			elsUserList.add(data);
		}

		return new Page<Map<String, Object>>(elsUserList, elsUsers.size(),
				search.getMaxResults(), search.getFirstResult());
	}
}
