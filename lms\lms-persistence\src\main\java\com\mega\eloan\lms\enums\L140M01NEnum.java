/* 
 * L140M01NEnum.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.enums;

/**
 * <pre>
 * L140M01N 利率結構化欄位相關ENUM
 * </pre>
 * 
 * @since 2012/10/24
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/10/24,REX,new
 *          </ul>
 */
public interface L140M01NEnum {

	/**
	 * <pre>
	 * 幣別種類
	 * </pre>
	 * 
	 */
	public enum RateTypeEnum {
		新台幣("1"), 美金("2"), 日幣("3"), 歐元("4"), 人民幣("5"), 澳幣("6"), 港幣("7"), 雜幣("Z");

		private String code;

		private RateTypeEnum(String code) {
			this.code = code;
		}

		public String getCode() {
			return this.code;
		}

		public boolean isEquals(Object other) {
			if ((other instanceof String)) {
				return this.code.equals(other);
			}
			return super.equals(other);
		}

		public static RateTypeEnum getEnum(String code) {
			for (RateTypeEnum enums : values()) {
				if (enums.isEquals(code)) {
					return enums;
				}
			}
			return null;
		}

		public static String coverToCurr(String code) {
			String curr = "";
			switch (getEnum(code)) {
			case 新台幣:
				curr = "TWD";
				break;
			case 美金:
				curr = "USD";
				break;
			case 日幣:
				curr = "JPY";
				break;
			case 歐元:
				curr = "EUR";
				break;
			case 人民幣:
				curr = "CNY";
				break;
			case 澳幣:
				curr = "AUD";
				break;
			case 港幣:
				curr = "HKD";
				break;
			case 雜幣:
				curr = "Z";
				break;
			default:
			}
			return curr;
		}
	}

	/**
	 * <pre>
	 * 適用期間選項
	 * </pre>
	 * 
	 */
	public enum SecNoOpEnum {

		// J-109-0077_05097_B1008 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
		
		全案("1"), 自動用日起迄月("2"), YYYYMMDD("3"), 自簽約日起迄月("4"), 自動用日起至迄日("5"), 自簽約日起至迄日(
				"6"), YYYYMMDD至迄日("7");
		private String code;

		private SecNoOpEnum(String code) {
			this.code = code;
		}

		public String getCode() {
			return this.code;
		}

		public boolean isEquals(Object other) {
			if ((other instanceof String)) {
				return this.code.equals(other);
			}
			return super.equals(other);
		}

		public static SecNoOpEnum getEnum(String code) {
			for (SecNoOpEnum enums : values()) {
				if (enums.isEquals(code)) {
					return enums;
				}
			}
			return null;
		}
	}

	/**
	 * <pre>
	 * 利率方式
	 * </pre>
	 * 
	 */
	public enum RateKindEnum {

		固定利率("1"), 機動利率("2"), 定期浮動("3");
		private String code;

		private RateKindEnum(String code) {
			this.code = code;
		}

		public String getCode() {
			return this.code;
		}

		public boolean isEquals(Object other) {
			if ((other instanceof String)) {
				return this.code.equals(other);
			}
			return super.equals(other);
		}

		public static RateKindEnum getEnum(String code) {
			for (RateKindEnum enums : values()) {
				if (enums.isEquals(code)) {
					return enums;
				}
			}
			return null;
		}
	}

}
