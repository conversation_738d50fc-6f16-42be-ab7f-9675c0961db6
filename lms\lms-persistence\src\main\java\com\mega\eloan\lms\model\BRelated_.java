/* 
 * BRelated_.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

/**
 * <pre>
 * 引用資料關聯記錄
 * </pre>
 * @since  2011/7/21
 * <AUTHOR>
 * @version <ul>
 *           <li>2011/7/21,iristu,new
 *          </ul>
 */
@Generated(value="Dali", date="2011-09-20T14:24:17.000+0800")
@StaticMetamodel(BRelated.class)
public class BRelated_ {
	public static volatile SingularAttribute<BRelated, String> oid;	
	public static volatile SingularAttribute<BRelated, String> docNote;
	public static volatile SingularAttribute<BRelated, String> docType1;
	public static volatile SingularAttribute<BRelated, String> docType2;
	public static volatile SingularAttribute<BRelated, String> mainId1;
	public static volatile SingularAttribute<BRelated, String> mainId2;
	public static volatile SingularAttribute<BRelated, String> tab;
	public static volatile SingularAttribute<BRelated, String> subtab;
	public static volatile SingularAttribute<BRelated, String> relatedflag;
	public static volatile SingularAttribute<BRelated, String> flag;
	public static volatile SingularAttribute<BRelated, String> updater;
	public static volatile SingularAttribute<BRelated, Timestamp> updateTime;
	public static volatile SingularAttribute<BRelated, String> pid1;
}
