package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S01D;

/** 企金銀行法／金控法利害關係人檔 **/
public interface L120S01DDao extends IGenericDao<L120S01D> {

	L120S01D findByOid(String oid);
	
	List<L120S01D> findByMainId(String mainId);
	
	L120S01D findByUniqueKey(String mainId,String custId,String dupNo);
	
	List<L120S01D> findByCustIdDupId(String custId,String DupNo);

	int delModel(String mainId);
}