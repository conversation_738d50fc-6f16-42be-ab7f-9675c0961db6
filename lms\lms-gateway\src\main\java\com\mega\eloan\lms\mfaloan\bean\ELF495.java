package com.mega.eloan.lms.mfaloan.bean;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Digits;

import tw.com.iisi.cap.model.GenericBean;

/**
 * 企金覆審額度檔
 * 
 * 在 DB 並未定義 PK, Unique Index
 * 
 * 
 * 同一個額度, 底下可以包含不同的科目, 再建立不同的帳號 在覆審報告表時, 呈現 額度,科目,sum(帳號的餘額) 所以 1 個額度可能出現
 * N次(依科目的數量)
 * 
 * 而在上傳 ELF495 時,沒有帳號的資訊 所以 1 個額度只上傳 1 次
 * 
 * 把 elf495_rptDocId+elf495_cntrNo 當成 PK
 */
public class ELF495 extends GenericBean {

	/** 分行代號 **/
	@Column(name = "ELF495_BRANCH", length = 3, columnDefinition = "CHAR(3)")
	private String elf495_branch;

	/** 借款人統一編號(PV-IDNO) **/
	@Column(name = "ELF495_CUSTID", length = 10, columnDefinition = "CHAR(10)")
	private String elf495_custId;

	/** 重複序號 **/
	@Column(name = "ELF495_DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String elf495_dupNo;

	/** DBUOBU **/
	@Column(name = "ELF495_DBUOBU", length = 3, columnDefinition = "CHAR(3)")
	private String elf495_dbuObu;

	/** 額度序號 **/
	@Column(name = "ELF495_CNTRNO", length = 12, columnDefinition = "CHAR(12)", nullable = false, unique = true)
	private String elf495_cntrNo;

	/** 授信(動用)期間起日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF495_DURBEG", columnDefinition = "DATE")
	private Date elf495_durBeg;

	/** 授信(動用)期間止日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF495_DUREND", columnDefinition = "DATE")
	private Date elf495_durEnd;

	/** 額度幣別(原幣) **/
	@Column(name = "ELF495_CURR", length = 3, columnDefinition = "CHAR(3)")
	private String elf495_curr;

	/** 額度金額(原幣) **/
	@Digits(integer = 13, fraction = 2)
	@Column(name = "ELF495_QUOTA", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal elf495_quota;

	/** 覆審報告表UNID **/
	@Column(name = "ELF495_RPTDOCID", length = 32, columnDefinition = "CHAR(32)", nullable = false, unique = true)
	private String elf495_rptDocId;

	/** 上次覆審日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF495_LLRDATE", columnDefinition = "DATE")
	private Date elf495_llrDate;

	/** 本次覆審日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF495_LRDATE", columnDefinition = "DATE")
	private Date elf495_lrDate;

	/** 覆審報告表案號 **/
	@Column(name = "ELF495_PROJNO", length = 100, columnDefinition = "CHAR(100)")
	private String elf495_projNo;

	/** 資料年 **/
	@Column(name = "ELF495_DATADTY", length = 3, columnDefinition = "CHAR(3)")
	private String elf495_dataDtY;

	/** 覆審批號 **/
	@Column(name = "ELF495_BATCHNO", length = 3, columnDefinition = "CHAR(3)")
	private String elf495_batchNo;

	/** 覆審序號 **/
	@Column(name = "ELF495_SNO", length = 3, columnDefinition = "CHAR(3)")
	private String elf495_sno;

	/** 資料修改人 **/
	@Column(name = "ELF495_UPDATER", length = 8, columnDefinition = "CHAR(8)")
	private String elf495_updater;

	/** 資料更新日 **/
	@Column(name = "ELF495_TMESTAMP", columnDefinition = "TIMESTAMP")
	private Timestamp elf495_tmestamp;

	/**
	 * 覆審名單類別 J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
	 */
	@Column(name = "ELF495_CTLTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String elf495_ctlType;

	public String getElf495_branch() {
		return elf495_branch;
	}

	public void setElf495_branch(String elf495_branch) {
		this.elf495_branch = elf495_branch;
	}

	public String getElf495_custId() {
		return elf495_custId;
	}

	public void setElf495_custId(String elf495_custId) {
		this.elf495_custId = elf495_custId;
	}

	public String getElf495_dupNo() {
		return elf495_dupNo;
	}

	public void setElf495_dupNo(String elf495_dupNo) {
		this.elf495_dupNo = elf495_dupNo;
	}

	public String getElf495_dbuObu() {
		return elf495_dbuObu;
	}

	public void setElf495_dbuObu(String elf495_dbuObu) {
		this.elf495_dbuObu = elf495_dbuObu;
	}

	public String getElf495_cntrNo() {
		return elf495_cntrNo;
	}

	public void setElf495_cntrNo(String elf495_cntrNo) {
		this.elf495_cntrNo = elf495_cntrNo;
	}

	public Date getElf495_durBeg() {
		return elf495_durBeg;
	}

	public void setElf495_durBeg(Date elf495_durBeg) {
		this.elf495_durBeg = elf495_durBeg;
	}

	public Date getElf495_durEnd() {
		return elf495_durEnd;
	}

	public void setElf495_durEnd(Date elf495_durEnd) {
		this.elf495_durEnd = elf495_durEnd;
	}

	public String getElf495_curr() {
		return elf495_curr;
	}

	public void setElf495_curr(String elf495_curr) {
		this.elf495_curr = elf495_curr;
	}

	public BigDecimal getElf495_quota() {
		return elf495_quota;
	}

	public void setElf495_quota(BigDecimal elf495_quota) {
		this.elf495_quota = elf495_quota;
	}

	public String getElf495_rptDocId() {
		return elf495_rptDocId;
	}

	public void setElf495_rptDocId(String elf495_rptDocId) {
		this.elf495_rptDocId = elf495_rptDocId;
	}

	public Date getElf495_llrDate() {
		return elf495_llrDate;
	}

	public void setElf495_llrDate(Date elf495_llrDate) {
		this.elf495_llrDate = elf495_llrDate;
	}

	public Date getElf495_lrDate() {
		return elf495_lrDate;
	}

	public void setElf495_lrDate(Date elf495_lrDate) {
		this.elf495_lrDate = elf495_lrDate;
	}

	public String getElf495_projNo() {
		return elf495_projNo;
	}

	public void setElf495_projNo(String elf495_projNo) {
		this.elf495_projNo = elf495_projNo;
	}

	public String getElf495_dataDtY() {
		return elf495_dataDtY;
	}

	public void setElf495_dataDtY(String elf495_dataDtY) {
		this.elf495_dataDtY = elf495_dataDtY;
	}

	public String getElf495_batchNo() {
		return elf495_batchNo;
	}

	public void setElf495_batchNo(String elf495_batchNo) {
		this.elf495_batchNo = elf495_batchNo;
	}

	public String getElf495_sno() {
		return elf495_sno;
	}

	public void setElf495_sno(String elf495_sno) {
		this.elf495_sno = elf495_sno;
	}

	public String getElf495_updater() {
		return elf495_updater;
	}

	public void setElf495_updater(String elf495_updater) {
		this.elf495_updater = elf495_updater;
	}

	public Timestamp getElf495_tmestamp() {
		return elf495_tmestamp;
	}

	public void setElf495_tmestamp(Timestamp elf495_tmestamp) {
		this.elf495_tmestamp = elf495_tmestamp;
	}

	public void setElf495_ctlType(String elf495_ctlType) {
		this.elf495_ctlType = elf495_ctlType;
	}

	public String getElf495_ctlType() {
		return elf495_ctlType;
	}
}
