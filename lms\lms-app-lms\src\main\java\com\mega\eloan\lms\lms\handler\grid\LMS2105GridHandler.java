/* 
 *  LMS2105GridHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.handler.grid;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;


import com.iisigroup.cap.component.PageParameters;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.formatter.BranchNameFormatter;
import com.mega.eloan.common.formatter.BranchNameFormatter.ShowTypeEnum;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.lms.pages.LMS2105M01Page;
import com.mega.eloan.lms.lms.service.LMS2105Service;
import com.mega.eloan.lms.model.L210M01A;
import com.mega.eloan.lms.model.L210S01A;
import com.mega.eloan.lms.model.L210S01B;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 修改資料特殊流程
 * </pre>
 * 
 * @since 2012/01/10
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/01/10,REX,new
 *          </ul>
 */
@Scope("request")
@Controller("lms2105gridhandler")
public class LMS2105GridHandler extends AbstractGridHandler {

	@Resource
	LMS2105Service lms2105Service;
	@Resource
	BranchService branchService;

	@Resource
	UserInfoService userInfoService;

	/**
	 * 修改資料特殊流程外部的grid
	 * 
	 * @param pageSetting
	 *            查詢條件
	 * @param params
	 *            前端傳回資料
	 * @param parent
	 *            Component
	 * @return CapGridResult　grid清單
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL210m01a(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String docStatus = Util.nullToSpace(params
				.getString(EloanConstants.DOC_STATUS));

		String[] docStatusArray = docStatus
				.split(UtilConstants.Mark.SPILT_MARK);

		pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
				docStatusArray);// 取得文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l210a01a.authUnit", user.getUnitNo());
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		Page<? extends GenericBean> page = lms2105Service.findPage(
				L210M01A.class, pageSetting);
		List<L210M01A> l210m01as = (List<L210M01A>) page.getContent();
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS2105M01Page.class);

		for (L210M01A l210m01a : l210m01as) {
			l210m01a.setDocStatus(this.getMessage("docStatus."
					+ l210m01a.getDocStatus()));
			l210m01a.setRptId(pop.getProperty("L210M01A.type"));
			l210m01a.setCustId(StrUtils.concat(l210m01a.getCustId(), " ",
					l210m01a.getDupNo()));
			l210m01a.setCaseNo(Util.toSemiCharString(l210m01a.getCaseNo()));
			l210m01a.setApprId(this.getUserName(l210m01a.getApprId()));
		}
		return new CapGridResult(l210m01as, page.getTotalRow());

	}

	/**
	 * 查詢自行聯貸攤貸比率檔
	 * 
	 * @param pageSetting
	 *            查詢條件
	 * @param params
	 *            前端傳回資料
	 * @param parent
	 *            Component
	 * @return grid清單
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL210s01a(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));

		String chgFlag = Util.nullToSpace(params.getString("chgFlag"));

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "chgFlag",
				chgFlag);

		Page<? extends GenericBean> page = lms2105Service.findPage(
				L210S01A.class, pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("shareBrId", new BranchNameFormatter(branchService,
				ShowTypeEnum.ID_Name));
		result.setDataReformatter(dataReformatter);
		List<L210S01A> l210s01as = (List<L210S01A>) page.getContent();
		for (L210S01A l210s01a : l210s01as) {
			if (UtilConstants.Cntrdoc.shareType.以比例計算.equals(l210s01a
					.getShareFlag())) {
				l210s01a.setShowRate(l210s01a.getShareRate1() + "/"
						+ l210s01a.getShareRate2());
			} else {
				l210s01a.setShowRate("");
			}
		}

		return result;

	}

	/**
	 * 查詢　同業聯貸攤貸比率檔
	 * 
	 * @param pageSetting
	 *            　查詢條件
	 * @param params
	 *            　前端傳回資料
	 * @param parent
	 *            　 Component
	 * @return　grid清單
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL210s01b(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		String chgFlag = Util.nullToSpace(params.getString("chgFlag"));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "chgFlag",
				chgFlag);

		Page<? extends GenericBean> page = lms2105Service.findPage(
				L210S01B.class, pageSetting);
		// 要顯示的銀行名稱
		StringBuilder showBranch = new StringBuilder("");

		List<L210S01B> l210s01bs = (List<L210S01B>) page.getContent();
		for (L210S01B l210s01b : l210s01bs) {
			l210s01b.setSlMaster("Y".equals(l210s01b.getSlMaster()) ? getMessage("yes")
					: getMessage("no"));
			showBranch.setLength(0);

			String slBank = l210s01b.getSlBank();
			showBranch.append(slBank).append(" ")
					.append(l210s01b.getSlBankCN());
			String slBranch = Util.trim(l210s01b.getSlBranch());
			if (!Util.isEmpty(slBranch)) {
				showBranch.append("<br/>")
						.append(LMSUtil.trimSlBranch(slBank, slBranch))
						.append(" ").append(l210s01b.getSlBranchCN());
			}

			l210s01b.setSlBankCN(showBranch.toString());
		}
		return new CapGridResult(l210s01bs, page.getTotalRow());

	}

	/**
	 * 取得使用者姓名
	 * 
	 * @param userId
	 *            員編
	 * @return 姓名
	 */
	private String getUserName(String userId) {
		if (Util.isEmpty(userId)) {
			return "";
		}
		String result = userInfoService.getUserName(userId);
		if (Util.isEmpty(result)) {
			return userId;
		} else {
			return result;
		}
	}
}
