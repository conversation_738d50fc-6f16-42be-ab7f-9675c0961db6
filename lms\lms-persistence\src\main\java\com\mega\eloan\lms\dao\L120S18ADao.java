
package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S18A;

/** 同一經濟關係人授信額度歸戶檔 **/
public interface L120S18ADao extends IGenericDao<L120S18A> {

	L120S18A findByOid(String oid);

	List<L120S18A> findByMainId_orderBySeq(String mainId);

	List<L120S18A> findByMainIdAndCustId(String mainId, String custId, String dupNo);
	List<L120S18A> findByMainIdAndCustId2(String mainId, String custId2, String dupNo2);
}