/* 
 * L140M01D_BF.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 額度授信科目限額檔(變更前) **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L140M01D_BF", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "lmtType", "lmtSeq" }))
public class L140M01D_BF extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 限額類型
	 * <p/>
	 * 1科子目限額
	 */
	@Size(max = 1)
	@Column(name = "LMTTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String lmtType;

	/** 序號 **/
	@Digits(integer = 5, fraction = 0, groups = Check.class)
	@Column(name = "LMTSEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer lmtSeq;

	/**
	 * 授信科目組合
	 * <p/>
	 * LimiSubject1_BF(科目限額)(_變更前)<br/>
	 * LimiSubject2_BF(科目限額)(_變更前)<br/>
	 * LimiSubject3_BF(科目限額)(_變更前)<br/>
	 * LimiSubject4_BF(科目限額)(_變更前)<br/>
	 * LimiSubject5_BF(科目限額)(_變更前)<br/>
	 * LimiSubject6_BF(科目限額)(_變更前)<br/>
	 * LimiSubject7_BF(科目限額)(_變更前)<br/>
	 * LimiSubtitle1_BF(子目限額)(_變更前)<br/>
	 * LimiSubtitle2_BF(子目限額)(_變更前)<br/>
	 * LimiSubtitle3_BF(子目限額)(_變更前)<br/>
	 * LimiSubtitle4_BF(子目限額)(_變更前)<br/>
	 * LimiSubtitle5_BF(子目限額)(_變更前)<br/>
	 * LimiSubtitle6_BF(子目限額)(_變更前)<br/>
	 * LimiSubtitle7_BF(子目限額)(_變更前)
	 */
	@Size(max = 300)
	@Column(name = "SUBJECT", length = 300, columnDefinition = "VARCHAR(300)")
	private String subject;

	/**
	 * 限額－幣別
	 * <p/>
	 * LimCurr1_BF<br/>
	 * LimCurr2_BF<br/>
	 * LimCurr3_BF<br/>
	 * LimCurr4_BF<br/>
	 * LimCurr5_BF<br/>
	 * LimCurr6_BF<br/>
	 * LimCurr7_BF<br/>
	 * LimSutCurr1_BF<br/>
	 * LimSutCurr2_BF<br/>
	 * LimSutCurr3_BF<br/>
	 * LimSutCurr4_BF<br/>
	 * LimSutCurr5_BF<br/>
	 * LimSutCurr6_BF<br/>
	 * LimSutCurr7_BF
	 */
	@Size(max = 3)
	@Column(name = "LMTCURR", length = 3, columnDefinition = "CHAR(3)")
	private String lmtCurr;

	/**
	 * 限額－金額
	 * <p/>
	 * LimitAmt1_BF<br/>
	 * LimitAmt2_BF<br/>
	 * LimitAmt3_BF<br/>
	 * LimitAmt4_BF<br/>
	 * LimitAmt5_BF<br/>
	 * LimitAmt6_BF<br/>
	 * LimitAmt7_BF<br/>
	 * SubtitleLimit1_BF<br/>
	 * SubtitleLimit2_BF<br/>
	 * SubtitleLimit3_BF<br/>
	 * SubtitleLimit4_BF<br/>
	 * SubtitleLimit5_BF<br/>
	 * SubtitleLimit6_BF<br/>
	 * SubtitleLimit7_BF
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "LMTAMT", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal lmtAmt;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得限額類型
	 * <p/>
	 * 1科子目限額
	 */
	public String getLmtType() {
		return this.lmtType;
	}

	/**
	 * 設定限額類型
	 * <p/>
	 * 1科子目限額
	 **/
	public void setLmtType(String value) {
		this.lmtType = value;
	}

	/** 取得序號 **/
	public Integer getLmtSeq() {
		return this.lmtSeq;
	}

	/** 設定序號 **/
	public void setLmtSeq(Integer value) {
		this.lmtSeq = value;
	}

	/**
	 * 取得授信科目組合
	 * <p/>
	 * LimiSubject1_BF(科目限額)(_變更前)<br/>
	 * LimiSubject2_BF(科目限額)(_變更前)<br/>
	 * LimiSubject3_BF(科目限額)(_變更前)<br/>
	 * LimiSubject4_BF(科目限額)(_變更前)<br/>
	 * LimiSubject5_BF(科目限額)(_變更前)<br/>
	 * LimiSubject6_BF(科目限額)(_變更前)<br/>
	 * LimiSubject7_BF(科目限額)(_變更前)<br/>
	 * LimiSubtitle1_BF(子目限額)(_變更前)<br/>
	 * LimiSubtitle2_BF(子目限額)(_變更前)<br/>
	 * LimiSubtitle3_BF(子目限額)(_變更前)<br/>
	 * LimiSubtitle4_BF(子目限額)(_變更前)<br/>
	 * LimiSubtitle5_BF(子目限額)(_變更前)<br/>
	 * LimiSubtitle6_BF(子目限額)(_變更前)<br/>
	 * LimiSubtitle7_BF(子目限額)(_變更前)
	 */
	public String getSubject() {
		return this.subject;
	}

	/**
	 * 設定授信科目組合
	 * <p/>
	 * LimiSubject1_BF(科目限額)(_變更前)<br/>
	 * LimiSubject2_BF(科目限額)(_變更前)<br/>
	 * LimiSubject3_BF(科目限額)(_變更前)<br/>
	 * LimiSubject4_BF(科目限額)(_變更前)<br/>
	 * LimiSubject5_BF(科目限額)(_變更前)<br/>
	 * LimiSubject6_BF(科目限額)(_變更前)<br/>
	 * LimiSubject7_BF(科目限額)(_變更前)<br/>
	 * LimiSubtitle1_BF(子目限額)(_變更前)<br/>
	 * LimiSubtitle2_BF(子目限額)(_變更前)<br/>
	 * LimiSubtitle3_BF(子目限額)(_變更前)<br/>
	 * LimiSubtitle4_BF(子目限額)(_變更前)<br/>
	 * LimiSubtitle5_BF(子目限額)(_變更前)<br/>
	 * LimiSubtitle6_BF(子目限額)(_變更前)<br/>
	 * LimiSubtitle7_BF(子目限額)(_變更前)
	 **/
	public void setSubject(String value) {
		this.subject = value;
	}

	/**
	 * 取得限額－幣別
	 * <p/>
	 * LimCurr1_BF<br/>
	 * LimCurr2_BF<br/>
	 * LimCurr3_BF<br/>
	 * LimCurr4_BF<br/>
	 * LimCurr5_BF<br/>
	 * LimCurr6_BF<br/>
	 * LimCurr7_BF<br/>
	 * LimSutCurr1_BF<br/>
	 * LimSutCurr2_BF<br/>
	 * LimSutCurr3_BF<br/>
	 * LimSutCurr4_BF<br/>
	 * LimSutCurr5_BF<br/>
	 * LimSutCurr6_BF<br/>
	 * LimSutCurr7_BF
	 */
	public String getLmtCurr() {
		return this.lmtCurr;
	}

	/**
	 * 設定限額－幣別
	 * <p/>
	 * LimCurr1_BF<br/>
	 * LimCurr2_BF<br/>
	 * LimCurr3_BF<br/>
	 * LimCurr4_BF<br/>
	 * LimCurr5_BF<br/>
	 * LimCurr6_BF<br/>
	 * LimCurr7_BF<br/>
	 * LimSutCurr1_BF<br/>
	 * LimSutCurr2_BF<br/>
	 * LimSutCurr3_BF<br/>
	 * LimSutCurr4_BF<br/>
	 * LimSutCurr5_BF<br/>
	 * LimSutCurr6_BF<br/>
	 * LimSutCurr7_BF
	 **/
	public void setLmtCurr(String value) {
		this.lmtCurr = value;
	}

	/**
	 * 取得限額－金額
	 * <p/>
	 * LimitAmt1_BF<br/>
	 * LimitAmt2_BF<br/>
	 * LimitAmt3_BF<br/>
	 * LimitAmt4_BF<br/>
	 * LimitAmt5_BF<br/>
	 * LimitAmt6_BF<br/>
	 * LimitAmt7_BF<br/>
	 * SubtitleLimit1_BF<br/>
	 * SubtitleLimit2_BF<br/>
	 * SubtitleLimit3_BF<br/>
	 * SubtitleLimit4_BF<br/>
	 * SubtitleLimit5_BF<br/>
	 * SubtitleLimit6_BF<br/>
	 * SubtitleLimit7_BF
	 */
	public BigDecimal getLmtAmt() {
		return this.lmtAmt;
	}

	/**
	 * 設定限額－金額
	 * <p/>
	 * LimitAmt1_BF<br/>
	 * LimitAmt2_BF<br/>
	 * LimitAmt3_BF<br/>
	 * LimitAmt4_BF<br/>
	 * LimitAmt5_BF<br/>
	 * LimitAmt6_BF<br/>
	 * LimitAmt7_BF<br/>
	 * SubtitleLimit1_BF<br/>
	 * SubtitleLimit2_BF<br/>
	 * SubtitleLimit3_BF<br/>
	 * SubtitleLimit4_BF<br/>
	 * SubtitleLimit5_BF<br/>
	 * SubtitleLimit6_BF<br/>
	 * SubtitleLimit7_BF
	 **/
	public void setLmtAmt(BigDecimal value) {
		this.lmtAmt = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
