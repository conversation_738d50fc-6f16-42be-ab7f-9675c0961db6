/* 
 * L180R19HDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L180R19H;

/** 逾期未覆審歷史檔 **/
public interface L180R19HDao extends IGenericDao<L180R19H> {

	L180R19H findByOid(String oid);

	List<L180R19H> findByMainId(String mainId);

	List<L180R19H> findByDocTypeAndDataDate(String docType, Date dataDate);

	List<L180R19H> findByDocTypeDataDateAndBranch(String docType,
			Date dataDate, String branch);

	List<L180R19H> findByDocTypeDataDateBranchAndCustId(String docType,
			Date dataDate, String branch, String custId, String dupNo);

	public List<L180R19H> findByDocTypeDataDateBranchAndCtlType(String docType,
			Date dataDate, String branch, String ctlType);

	public List<L180R19H> findByDocTypeDataDateAndBranchOrderByDataDate(String docType, String cntrNo);
}