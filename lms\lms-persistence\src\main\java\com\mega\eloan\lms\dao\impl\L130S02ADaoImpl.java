/* 
 * L130S02ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.L130S02ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L130S02A;

/** 異常通報表額度控管設定 **/
@Repository
public class L130S02ADaoImpl extends LMSJpaDao<L130S02A, String>
	implements L130S02ADao {

	@Override
	public L130S02A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public L130S02A findByOid_NotDel(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		return findUniqueOrNone(search);
	}
	
	@Override
	public List<L130S02A> findByMainIdSeqNo(String mainId, String seqNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "seqNo", seqNo);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		List<L130S02A> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public L130S02A findByUniqueIdx(String mainId, String seqNo, String ctlType, String ctlItem) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "seqNo", seqNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "ctlType", ctlType);
		search.addSearchModeParameters(SearchMode.EQUALS, "ctlItem", ctlItem);
		//deletedTime==null 留在外面判斷
		return findUniqueOrNone(search);		
	}
	
	
	@Override
	public int deleteByKeyDeletedTime(String mainId, String seqNo, String ctlType, String ctlItem) {
		Query query = entityManager.createNamedQuery("L130S02A.deleteByKeyDeletedTime");
		query.setParameter("MAINID", mainId);
		query.setParameter("SEQNO", seqNo);
		query.setParameter("CTLTYPE", ctlType);
		query.setParameter("CTLITEM", ctlItem);
		return query.executeUpdate();
		
	}
}