package com.mega.eloan.lms.model;

import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.IDataObject;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 人頭戶或代辦案件黑名單 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C900M01J", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId"}))
public class C900M01J extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * 被通報類別(P-疑似代辦案件,I-主管機關通報, B-疑似人頭戶案件)
	 */
	@Size(max=1)
	@Column(name="CATEGORY", length=1, columnDefinition="VARCHAR(1)")
	private String category;

	/** 
	 * 疑似代辦或人頭戶案件訊息(ref C250M01A, C900M01H) <br/>
	 * select codetype, codeValue, codeDesc from com.bcodetype where locale='zh_TW' and codetype in('C250M01A_lnFlag', 'cls260CtlFlagType', 'lnFlag_extend_C250M01A_C900M01H') order by codetype, codeOrder   
	 */
	@Size(max=2)
	@Column(name="LNFLAG", length=2, columnDefinition="CHAR(02)")
	private String lnflag;

	/** 備註 **/
	@Size(max=300)
	@Column(name="MEMO", length=300, columnDefinition="VARCHAR(300)")
	private String memo;

	/** 申請解除人員號碼 **/
	@Size(max=6)
	@Column(name="DCUPDATER", length=6, columnDefinition="CHAR(6)")
	private String dcUpdater;

	/** 申請解除日期 **/
	@Column(name="DCUPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp dcUpdateTime;

	/** 解除說明 **/
	@Size(max=300)
	@Column(name="DCMEMO", length=300, columnDefinition="VARCHAR(300)")
	private String dcMemo;
	
	/** 核准解除人員 **/
	@Size(max=6)
	@Column(name="DCAPPROVER", length=6, columnDefinition="CHAR(6)")
	private String dcApprover;

	/** 核准解除日期 **/
	@Column(name="DCAPPROVETIME", columnDefinition="TIMESTAMP")
	private Timestamp dcApproveTime;

	/** 案件來源 **/
	@Column(name="INTRODUCESRC", length=1, columnDefinition="CHAR(1)")
	private String introduceSrc;

	/** 引介行員代號 **/
	@Column(name="MEGAEMPNO", length=6, columnDefinition="CHAR(6)")
	private String megaEmpNo;
	
	/** 引介房仲代號 **/
	@Column(name="AGNTNO", length=5, columnDefinition="CHAR5)")
	private String agntNo;
	
	/** 引介子公司代號 **/
	@Column(name="MEGACODE", length=5, columnDefinition="CHAR(5)")
	private String megaCode;
	
	/** 引介子公司分支代號 **/
	@Column(name="SUBUNITNO", length=5, columnDefinition="CHAR(5)")
	private String subUnitNo;
	
	/** 引介子公司員工編號 **/
	@Column(name="SUBEMPNO", length=6, columnDefinition="VARCHAR(6)")
	private String subEmpNo;
	
	/** 引介子公司員工姓名 **/
	@Column(name="SUBEMPNM", length=30, columnDefinition="VARCHAR(30)")
	private String subEmpNm;
	
	/** 其它來源說明 **/
	@Column(name="SRCMEMO", length=120, columnDefinition="VARCHAR(120)")
	private String srcMemo;	
	
	/** 案件關係人統編 **/
	@Column(name="RELPARTYID", length=10, columnDefinition="VARCHAR(10)")
	private String relPartyId;
	
	/** 案件關係人名稱 **/
	@Column(name="RELPARTYNAME", length=150, columnDefinition="VARCHAR(150)")
	private String relPartyName;
	
	/** 引介客戶ID/企業統編 */
	@Column(name = "INTROCUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String introCustId;

	/** 引介客戶重複序號 */
	@Column(name = "INTRODUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String introDupNo;
	
	/** 引介客戶名稱/企業名稱 */
	@Column(name = "INTROCUSTNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String introCustName;
	
	/** 引介人姓名 */
	@Column(name = "INTRODUCERNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String introducerName;
	
	/** 房仲證書(明)字號-年 **/
    @Size(max=3)
    @Column(name="LICENSEYEAR", length=3, columnDefinition="VARCHAR(3)")
    private String licenseYear;
    
    /** 房仲證書(明)字號-年登字 **/
    @Size(max=15)
    @Column(name="LICENSEWORD", length=15, columnDefinition="VARCHAR(15)")
    private String licenseWord;
    
	/** 房仲證書(明)字號-編號 **/
	@Size(max=6)
	@Column(name="LICENSENUMBER", length=6, columnDefinition="CHAR(6)")
	private String licenseNumber;
	
	/** 
	 * 取得被通報類別(P-疑似代辦案件,I-主管機關通報, B-疑似人頭戶案件)
	 */
	public String getCategory() {
		return this.category;
	}
	/**
	 *  設定被通報類別(P-疑似代辦案件,I-主管機關通報, B-疑似人頭戶案件)
	 **/
	public void setCategory(String value) {
		this.category = value;
	}

	/** 取得疑似代辦或人頭戶案件訊息 */
	public String getLnflag() {
		return this.lnflag;
	}
	/** 設定疑似代辦或人頭戶案件訊息 */
	public void setLnflag(String value) {
		this.lnflag = value;
	}
	
	/** 取得備註 **/
	public String getMemo() {
		return this.memo;
	}
	/** 設定備註 **/
	public void setMemo(String value) {
		this.memo = value;
	}

	/** 取得申請解除人員號碼 **/
	public String getDcUpdater() {
		return this.dcUpdater;
	}
	/** 設定申請解除人員號碼 **/
	public void setDcUpdater(String value) {
		this.dcUpdater = value;
	}

	/** 取得申請解除日期 **/
	public Timestamp getDcUpdateTime() {
		return this.dcUpdateTime;
	}
	/** 設定申請解除日期 **/
	public void setDcUpdateTime(Timestamp value) {
		this.dcUpdateTime = value;
	}
	
	/** 取得解除說明 **/
	public String getDcMemo() {
		return this.dcMemo;
	}
	/** 設定解除說明 **/
	public void setDcMemo(String value) {
		this.dcMemo = value;
	}

	/** 取得核准解除人員 **/
	public String getDcApprover() {
		return this.dcApprover;
	}
	/** 設定核准解除人員 **/
	public void setDcApprover(String value) {
		this.dcApprover = value;
	}

	/** 取得核准解除日期 **/
	public Timestamp getDcApproveTime() {
		return this.dcApproveTime;
	}
	/** 設定核准解除日期 **/
	public void setDcApproveTime(Timestamp value) {
		this.dcApproveTime = value;
	}
	
	/** 取得案件來源 **/
	public String getIntroduceSrc() {
		return introduceSrc;
	}
	/** 設定案件來源 **/
	public void setIntroduceSrc(String introduceSrc) {
		this.introduceSrc = introduceSrc;
	}

	/** 取得引介行員代號 **/
	public String getMegaEmpNo() {
		return megaEmpNo;
	}
	/** 設定引介行員代號 **/
	public void setMegaEmpNo(String megaEmpNo) {
		this.megaEmpNo = megaEmpNo;
	}
	
	/** 取得引介房仲代號 **/
	public String getAgntNo() {
		return agntNo;
	}
	/** 設定引介房仲代號 **/
	public void setAgntNo(String agntNo) {
		this.agntNo = agntNo;
	}
	
	/** 取得引介子公司代號 **/
	public String getMegaCode() {
		return megaCode;
	}
	/** 設定引介子公司代號 **/
	public void setMegaCode(String megaCode) {
		this.megaCode = megaCode;
	}
	
	/** 取得引介子公司分支代號 **/
	public String getSubUnitNo() {
		return subUnitNo;
	}
	/** 設定引介子公司分支代號 **/
	public void setSubUnitNo(String subUnitNo) {
		this.subUnitNo = subUnitNo;
	}
	
	/** 取得引介子公司員工編號 **/
	public String getSubEmpNo() {
		return subEmpNo;
	}
	/** 設定引介子公司員工編號 **/
	public void setSubEmpNo(String subEmpNo) {
		this.subEmpNo = subEmpNo;
	}
	
	/** 取得引介子公司員工姓名 **/
	public String getSubEmpNm() {
		return subEmpNm;
	}
	/** 設定引介子公司員工姓名 **/
	public void setSubEmpNm(String subEmpNm) {
		this.subEmpNm = subEmpNm;
	}
	
	/** 取得其它來源說明 **/
	public String getSrcMemo() {
		return srcMemo;
	}
	/** 設定其它來源說明 **/
	public void setSrcMemo(String srcMemo) {
		this.srcMemo = srcMemo;
	}
	
	/** 取得案件關係人統編 **/
	public String getRelPartyId() {
		return relPartyId;
	}
	/** 設定案件關係人統編 **/	
	public void setRelPartyId(String relPartyId) {
		this.relPartyId = relPartyId;
	}
	/** 取得案件關係人名稱 **/
	public String getRelPartyName() {
		return relPartyName;
	}
	/** 設定案件關係人名稱 **/
	public void setRelPartyName(String relPartyName) {
		this.relPartyName = relPartyName;
	}
	
	public String getIntroCustName() {
		return introCustName;
	}
	public void setIntroCustName(String introCustName) {
		this.introCustName = introCustName;
	}
	
	public String getIntroCustId() {
		return introCustId;
	}
	public void setIntroCustId(String introCustId) {
		this.introCustId = introCustId;
	}
	
	public String getIntroDupNo() {
		return introDupNo;
	}
	public void setIntroDupNo(String introDupNo) {
		this.introDupNo = introDupNo;
	}
	
	public String getIntroducerName() {
		return introducerName;
	}
	public void setIntroducerName(String introducerName) {
		this.introducerName = introducerName;
	}
	
	public String getLicenseYear() {
		return licenseYear;
	}
	public void setLicenseYear(String licenseYear) {
		this.licenseYear = licenseYear;
	}
	
	public String getLicenseWord() {
		return licenseWord;
	}
	public void setLicenseWord(String licenseWord) {
		this.licenseWord = licenseWord;
	}
	
	public String getLicenseNumber() {
		return licenseNumber;
	}
	public void setLicenseNumber(String licenseNumber) {
		this.licenseNumber = licenseNumber;
	}
	
}
