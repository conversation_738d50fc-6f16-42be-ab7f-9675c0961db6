<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:wicket="http://wicket.apache.org/">
	<body>
		<wicket:extend>
			<script type="text/javascript" src="pagejs/fms/LMS7800M01Page.js?r=20190201"></script>

			<div class="button-menu funcContainer" id="buttonPanel">
				
				<!--編製中 -->
				<wicket:enclosure><span wicket:id="_btnDOC_EDITING" />
	        		<button id="btnSave"> 
	        			<span class="ui-icon ui-icon-jcs-04" />
	        			<wicket:message key="button.save"><!--儲存--></wicket:message>
	        		</button>
					<button id="btnSend" >
	        			<span class="ui-icon ui-icon-jcs-02" />
	        			<wicket:message key="button.send" ><!--呈主管覆核--></wicket:message>
	        		</button>
		        </wicket:enclosure>		
				
				<!--待覆核 -->
				<wicket:enclosure><span wicket:id="_btnWAIT_APPROVE" />
	        		<button id="btnCheck" >
	        			<span class="ui-icon ui-icon-jcs-106" />
	        			<wicket:message key="button.check" ><!--覆核--></wicket:message>
	        		</button>
		        </wicket:enclosure>				
		        
                <button id="btnPrint" class="forview">
                	<span class="ui-icon ui-icon-jcs-03"></span>
					<wicket:message key="button.print"><!--列印--></wicket:message>
				</button>
                <button id="btnExit"  class="forview">
                	<span class="ui-icon ui-icon-jcs-01"></span>
					<wicket:message key="button.exit"><!--離開--></wicket:message>
				</button>
				
            </div>
			<div class="tit2 color-black">
				<wicket:message key="title"></wicket:message> - <span id="showCustId" class="color-blue" />
			</div>
			<div class="tit2 color-black">
				<wicket:message key="cntrNo"></wicket:message>：	<span id="cntrNo" class="color-blue" />
			</div>
			
			<form id="mainPanel">
				<span id="mainId" style="display:none"/>
				<fieldset>
                    <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tbody>
							<tr>
								<td>
									<wicket:message key="L140MM6A.appraiser">原負責經辦</wicket:message>
								</td>
								<td>
									<span id="appraiser"/>
								</td>
								<td>
									<wicket:message key="L140MM6A.infoAppraiser">知會經辦</wicket:message>
								</td>
								<td>
									<select id="infoAppraiser" name="infoAppraiser"/>
								</td>
							</tr>
							<tr>
								<td colspan="4">
									<div id="typeA">
										<span id="edit_A" style="display:none;"/>
										<wicket:message key="L140MM6A.typeA"></wicket:message>
										<div id="div_A" style="display:none;">
											<label><input type="radio" name="checkYN_A" value="Y" class="A"/><wicket:message key="Y"></wicket:message></label>
								            <label><input type="radio" name="checkYN_A" value="N" class="A"/><wicket:message key="N"></wicket:message></label>
											&nbsp;&nbsp;<select id="result_A" name="result_A" style="display:none;"/>
											&nbsp;&nbsp;<select id="select_A" name="select_A" style="display:none;"/>
											&nbsp;&nbsp;<span id="mtitle_A"/><input type="text" name="memo_A" id="memo_A" size="40" maxlength="80" maxlengthC="40" style="display:none;"/>
										</div>
										<br><span id="none_A" style="display:none;" class="color-red"><wicket:message key="noneData"></wicket:message></span>
									</div>
									<div id="typeB">
										<span id="edit_B" style="display:none;"/>
										<wicket:message key="L140MM6A.typeB"></wicket:message>
										<div id="div_B" style="display:none;">
											<label><input type="radio" name="checkYN_B" value="Y" class="B"/><wicket:message key="Y"></wicket:message></label>
								            <label><input type="radio" name="checkYN_B" value="N" class="B"/><wicket:message key="N"></wicket:message></label>
											&nbsp;&nbsp;<select id="result_B" name="result_B" style="display:none;"/>
											&nbsp;&nbsp;<select id="select_B" name="select_B" style="display:none;"/>
											&nbsp;&nbsp;<span id="mtitle_B"/><input type="text" name="memo_B" id="memo_B" size="40" maxlength="80" maxlengthC="40" style="display:none;"/>
										</div>
										<br><span id="none_B" style="display:none;" class="color-red"><wicket:message key="noneData"></wicket:message></span>
									</div>
									<div id="typeC">
										<span id="edit_C" style="display:none;"/>
										<wicket:message key="L140MM6A.typeC"></wicket:message>
										<div id="div_C" style="display:none;">
											<label><input type="radio" name="checkYN_C" value="Y" class="C"/><wicket:message key="Y"></wicket:message></label>
								            <label><input type="radio" name="checkYN_C" value="N" class="C"/><wicket:message key="N"></wicket:message></label>
											&nbsp;&nbsp;<select id="result_C" name="result_C" style="display:none;"/>
											&nbsp;&nbsp;<select id="select_C" name="select_C" style="display:none;"/>
											&nbsp;&nbsp;<span id="mtitle_C"/><input type="text" name="memo_C" id="memo_C" size="40" maxlength="80" maxlengthC="40" style="display:none;"/>
										</div>
										<br><span id="none_C" style="display:none;" class="color-red"><wicket:message key="noneData"></wicket:message></span>
									</div>
								</td>  
							</tr>
                        </tbody>
                    </table>
                </fieldset>
			</form>
			
			<div id="docPanel">
				<fieldset>
                    <legend>
                        <b><wicket:message key="doc.docUpdateLog"><!-- 文件異動紀錄 --></wicket:message></b>
                    </legend>
                    <div class="funcContainer">
                        <div class="funcContainer"><!-- 文件異動紀錄--> <div wicket:id="_docLog" /></div>
                    </div>
                    <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tbody>
                            <tr>
                            	<td width="35%" class="hd1">
                                    <wicket:message key="doc.creator"><!--  文件建立者--></wicket:message>&nbsp;&nbsp;
                                </td>
                                <td width="15%">
                                    <span id='creator'/>(<span id='createTime'/>)
                                </td>
                                <td width="30%" class="hd1">
                                    <wicket:message key="doc.lastUpdater"><!--  最後異動者--></wicket:message>&nbsp;&nbsp;
                                </td>
                                <td width="20%">
                                    <span id='updater'/>(<span id='updateTime'/>)
                                </td>
                            </tr>
                            <tr>
                                <td class="hd1">
                                </td>
                                <td>
                                </td>
                                <td class="hd1">
                                    <wicket:message key="doc.docCode"><!--文件亂碼--></wicket:message>&nbsp;&nbsp;
                                </td>
                                <td>
                                    <span id="randomCode" />
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </fieldset>
				
				<fieldset>
	                <div id="tabs-appr" class="tabs" style='width:99%;'>
	                    <ul>
	                        <li>
	                            <a href="#tabs-appr01"><b><wicket:message key="L140MM6B.title01"></wicket:message></b></a>
	                        </li>
	                    </ul>
						<div class="tabCtx-warp">
	                        <div id="tabs-appr01" class="content">
 							<table width="100%">
                                <tr>
                                    <td width="12%" class="rt">
                                        <b class="text-red">
                                            <wicket:message key="L140MM6B.managerId"><!--經副襄理--></wicket:message>：
                                        </b>
                                    </td>
                                    <td width="12%" class="lt">
                                        <span id="managerId" />
                                    </td>
                                    <td width="12%" class="rt">
                                        <b class="text-red">
                                            <wicket:message key="L140MM6B.bossId"><!-- 授信主管--></wicket:message>：
                                        </b>
                                    </td>
                                    <td width="12%" class="lt">
                                        <span id="bossId" />
                                    </td>
                                    <td width="12%" class="rt">
                                        <b class="text-red">
                                            <wicket:message key="L140MM6B.reCheckId"><!--覆核主管--></wicket:message>：
                                        </b>
                                    </td>
                                    <td width="12%" class="lt">
                                        <span id="reCheckId"/>
                                    </td>
                                    <td width="12%" class="rt">
                                        <b class="text-red">
                                            <wicket:message key="L140MM6B.apprId"><!--  經辦--></wicket:message>：
                                        </b>
                                    </td>
                                    <td width="12%" class="lt">
                                        <span id="showApprId"/>
                                    </td>
                                </tr>
                            </table>
							</div>
	                    </div>
	               </div>				   
	            </fieldset>
			</div>
			
			<div id="openCheckBox" style="display:none"> 
				<div>
				<span id="check1" style="display:none">
				 	<label><input name="checkRadio" type="radio" value="3"><wicket:message key="accept"><!--  核准--></wicket:message></label><br/>
					<label><input name="checkRadio" type="radio" value="1"><wicket:message key="back"><!--  退回經辦修改--></wicket:message></label>
				</span>
				</div>
			</div>
			<div id="selectBossBox"  style="display:none;">
			  <form id="selectBossForm">
	         	<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
	                 <tr>
	            		<td class="hd1" width="60%"><wicket:message key="L140MM6B.selectBoss"><!--  授信主管人數--></wicket:message>&nbsp;&nbsp;</td>
	                    <td width="40%"><select id="numPerson" name="numPerson">
	                    		<option value="1">1</option>
	                    		<option value="2">2</option>
	                            <option value="3">3</option>
								<option value="4">4</option>
	                    		<option value="5">5</option>
	                            <option value="6">6</option>
								<option value="7">7</option>
	                    		<option value="8">8</option>
	                            <option value="9">9</option>
								<option value="10">10</option>
	                    	</select>
						</td>
	                 </tr>
	                 <tr >
	                 	<td class="hd1" ><wicket:message key="L140MM6B.bossId"><!--  授信主管--></wicket:message>&nbsp;&nbsp;</td>
	            		<td >
	            			<div id="bossItem"></div>
	                 	</td>
	                 </tr>
	                 <tr >
	            		<td class="hd1"><wicket:message key="L140MM6B.managerId"><!--經副襄理--></wicket:message>&nbsp;&nbsp;</td>
	                    <td><div id="managerItem"></div></td>
	                 </tr>
	           	 </table>
				</form>
  			</div>
		</wicket:extend>
    </body>
</html>
