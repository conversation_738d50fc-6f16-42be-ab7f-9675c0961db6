package com.mega.eloan.lms.las.service.impl;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapMath;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.node.EndNode;
import tw.com.jcs.flow.service.FlowService;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.DocLogEnum;
import com.mega.eloan.common.model.Meta_;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.lms.base.service.NumberService;
import com.mega.eloan.lms.dao.C120S01BDao;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dao.L120S01ADao;
import com.mega.eloan.lms.dao.L160M01ADao;
import com.mega.eloan.lms.dao.L192M01ADao;
import com.mega.eloan.lms.dao.L192M01BDao;
import com.mega.eloan.lms.dao.L192M01CDao;
import com.mega.eloan.lms.dao.L192S01ADao;
import com.mega.eloan.lms.dao.L192S02ADao;
import com.mega.eloan.lms.las.service.LMS1925Service;
import com.mega.eloan.lms.model.C120S01B;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L160M01A;
import com.mega.eloan.lms.model.L192M01A;
import com.mega.eloan.lms.model.L192M01B;
import com.mega.eloan.lms.model.L192M01C;
import com.mega.eloan.lms.model.L192S01A;
import com.mega.eloan.lms.model.L192S02A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * 授信業務工作底稿基本介面 實作
 * 
 * <AUTHOR>
 * 
 */
@Service
public class LMS1925ServiceImpl extends AbstractCapService implements
		LMS1925Service {

	@Resource
	C120S01BDao c120s01bDao;

	@Resource
	L192M01ADao l192m01aDao;

	@Resource
	L192M01BDao l192m01bDao;

	@Resource
	L192M01CDao l192m01cDao;

	@Resource
	L192S01ADao l192s01aDao;

	@Resource
	L192S02ADao l192s02aDao;

	@Resource
	L160M01ADao l160m01aDao;

	@Resource
	L120M01ADao l120m01aDao;

	@Resource
	TempDataService tempDataService;

	@Resource
	DocLogService docLogService;

	@Resource
	L120S01ADao l120s01aDao;

	@Resource
	FlowService flowService;
	
	@Resource
	NumberService numberService;

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.las.service.LMS1925Service#get1925V01(tw.com.iisi.
	 * cap.dao.utils.ISearch)
	 */
	@Override
	public Page<L192M01A> get1925V01(ISearch search) {
		return l192m01aDao.findPage(search);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.las.service.LMS1925Service#getL192M01A(java.lang.String
	 * )
	 */
	@Override
	public L192M01A getL192M01A(String oid) {
		return l192m01aDao.find(oid);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.las.service.LMS1925Service#saveL192M01A(com.mega.eloan
	 * .lms.model.L192M01A)
	 */
	@Override
	public void saveL192M01A(L192M01A l192m01a) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		l192m01a.setUpdater(user.getUserId());
		l192m01a.setUpdateTime(CapDate.getCurrentTimestamp());
		l192m01aDao.save(l192m01a);

		if (!"Y".equals(SimpleContextHolder.get(EloanConstants.TEMPSAVE_RUN))) {
			tempDataService.deleteByMainId(l192m01a.getMainId());
			docLogService.record(l192m01a.getOid(), DocLogEnum.SAVE);
		}
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.las.service.LMS1925Service#getL192S02A(com.mega.eloan
	 * .lms.model.L192M01A)
	 */
	@Override
	public List<L192S02A> getL192S02A(L192M01A l192m01a) {
		return l192s02aDao.findByL192M01A(l192m01a);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.las.service.LMS1925Service#getL192S02A(tw.com.iisi
	 * .cap.dao.utils.ISearch)
	 */
	@Override
	public Page<L192S02A> getL192S02A(ISearch search) {
		return l192s02aDao.findPage(search);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.las.service.LMS1925Service#saveL192S02A(com.mega.eloan
	 * .lms.model.L192S02A)
	 */
	@Override
	public void saveL192S02A(L192S02A l192s02a) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		l192s02a.setUpdater(user.getUserId());
		l192s02a.setUpdateTime(CapDate.getCurrentTimestamp());
		l192s02aDao.save(l192s02a);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.las.service.LMS1925Service#deleteL192S02A(com.mega
	 * .eloan.lms.model.L192S02A)
	 */
	@Override
	public void deleteL192S02A(L192S02A l192s02a) {
		l192s02aDao.delete(l192s02a);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.las.service.LMS1925Service#getL192S02A(java.lang.String
	 * )
	 */
	@Override
	public L192S02A getL192S02A(String oid) {
		return l192s02aDao.find(oid);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.las.service.LMS1925Service#saveNewDocument(com.mega
	 * .eloan.lms.model.L192M01A)
	 */
	@Override
	public void saveNewDocument(L192M01A l192m01a) throws CapMessageException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		// 加入wp 編號

		Date checkDate = l192m01a.getCheckDate();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy");
		String wpYear = sdf.format(checkDate);

		//BigDecimal wpSeq = l192m01aDao.getWpNo(l192m01a.getOwnBrId(), wpYear);		
		String wpSeq = numberService.getNumberWithMax(L192M01A.class,
				l192m01a.getOwnBrId(), wpYear, 99999);
		l192m01a.setWpYear(new BigDecimal(wpYear));
		l192m01a.setWpBrId(l192m01a.getOwnBrId());

		l192m01a.setWpSeq(CapMath.getBigDecimal(wpSeq));
		l192m01a.setWpNo(wpYear + l192m01a.getWpBrId() + "LN"
				+ Util.addZeroWithValue(wpSeq, 5));

		saveL192M01A(l192m01a);
		flowService.start("LMS1925Flow", l192m01a.getOid(), user.getUserId(),
				user.getUnitNo());
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.las.service.LMS1925Service#deleteL192M01A(java.lang
	 * .String)
	 */
	@Override
	public void deleteL192M01A(String mainOid) {
		L192M01A meta = l192m01aDao.find(mainOid);
		flowService.cancel(meta.getOid());
		l192m01bDao.deleteByMeta(meta);
		l192m01cDao.deleteByMeta(meta);
		l192s01aDao.deleteByMeata(meta);
		l192s02aDao.deleteByMeata(meta);
		l192m01aDao.delete(meta);
		docLogService.record(mainOid, DocLogEnum.DELETE);

	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.las.service.LMS1925Service#findL160M01A(tw.com.iisi
	 * .cap.dao.utils.ISearch)
	 */
	@Override
	public List<L160M01A> findL160M01A(ISearch search) {
		return l160m01aDao.find(search);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.las.service.LMS1925Service#get1925V02(tw.com.iisi.
	 * cap.dao.utils.ISearch)
	 */
	@Override
	public Page<L160M01A> get1925V02(ISearch search) {
		return l160m01aDao.findPage(search);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.las.service.LMS1925Service#getLatestCaseDateById(java
	 * .lang.String, java.lang.String)
	 */
	@Override
	public L120M01A getLatestCaseDateById(String custId, String dupNo) {
		String branch = MegaSSOSecurityContext.getUnitNo();
		ISearch search = l120m01aDao.createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS,
				Meta_.ownBrId.getName(), branch);
		search.addSearchModeParameters(SearchMode.EQUALS,
				Meta_.custId.getName(), custId);
		search.addSearchModeParameters(SearchMode.EQUALS,
				Meta_.dupNo.getName(), dupNo);
		search.addOrderBy("caseDate", true);
		return l120m01aDao.findUniqueOrNone(search);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.las.service.LMS1925Service#getL192S01A(tw.com.iisi
	 * .cap.dao.utils.ISearch)
	 */
	@Override
	public Page<L192S01A> getL192S01A(ISearch search) {
		return l192s01aDao.findPage(search);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.las.service.LMS1925Service#saveL192S01A(java.util.
	 * List)
	 */
	@Override
	public void saveL192S01A(List<L192S01A> l192s01as) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (L192S01A l192s01a : l192s01as) {
			l192s01a.setUpdater(user.getUserId());
			l192s01a.setUpdateTime(new Date());
			l192s01aDao.save(l192s01a);
		}

	}

	@Override
	public void saveL192S02A(List<L192S02A> l192s02as) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (L192S02A l192s02a : l192s02as) {
			l192s02a.setUpdater(user.getUserId());
			l192s02a.setUpdateTime(new Date());
			l192s02aDao.save(l192s02a);
		}

	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.las.service.LMS1925Service#deleteL192S01A(java.lang
	 * .String)
	 */
	@Override
	public void deleteL192S01A(String mainOid) {

		L192M01A meta = l192m01aDao.find(mainOid);
		l192s01aDao.deleteByMeata(meta);

	}

	@Override
	public void deleteL192S02A(String mainOid) {
		L192M01A meta = l192m01aDao.find(mainOid);
		l192s02aDao.deleteByMeata(meta);

	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.las.service.LMS1925Service#reNewL192S01A(java.lang
	 * .String, java.util.List)
	 */
	@Override
	public void reNewL192S01A(String mainOid, List<L192S01A> l192s01as) {
		deleteL192S01A(mainOid);
		if (CollectionUtils.isNotEmpty(l192s01as)) {
			saveL192S01A(l192s01as);
		}
	}

	@Override
	public void reNewL192S02A(String mainOid, List<L192S02A> l192s02as) {
		deleteL192S02A(mainOid);
		if (CollectionUtils.isNotEmpty(l192s02as)) {
			saveL192S02A(l192s02as);
		}
	}

	// @Override
	// public List<L120S01A> getL120S01A(String mainId) {
	// return l120s01aDao.findByMainId(mainId);
	// }

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.las.service.LMS1925Service#reNewL192M01B(java.lang
	 * .String, java.util.List)
	 */
	@Override
	public void reNewL192M01B(String mainOid, List<L192M01B> l192m01bs) {
		deleteL192M01B(mainOid);
		saveL192M01B(l192m01bs);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.las.service.LMS1925Service#deleteL192M01B(java.lang
	 * .String)
	 */
	@Override
	public void deleteL192M01B(String mainOid) {

		L192M01A meta = l192m01aDao.find(mainOid);
		l192m01bDao.deleteByMeta(meta);

	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.las.service.LMS1925Service#saveL192M01B(java.util.
	 * List)
	 */
	@Override
	public void saveL192M01B(List<L192M01B> l192m01bs) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (L192M01B l192m01b : l192m01bs) {
			l192m01b.setUpdater(user.getUserId());
			l192m01b.setUpdateTime(new Date());
			l192m01bDao.save(l192m01b);
		}
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.las.service.LMS1925Service#getL192M01B(tw.com.iisi
	 * .cap.dao.utils.ISearch)
	 */
	@Override
	public Page<L192M01B> getL192M01B(ISearch search) {
		return l192m01bDao.findPage(search);
	}

	@Override
	public L192S01A getL192S01A(String oid) {
		return l192s01aDao.find(oid);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.las.service.LMS1925Service#flowControl(java.lang.String
	 * , java.lang.String)
	 */
	@Override
	public void flowControl(String oid, String action) {
		FlowInstance inst = flowService.createQuery().id(oid).query();
		if (!CapString.isEmpty(action)) {
			inst.setAttribute("action", action);
		}
		inst.next();

		if (!(inst.getNode() instanceof EndNode)) {
			if (inst.getNextNode() instanceof EndNode
					|| !CapString.isEmpty(inst.getNextLog())
					&& DocLogEnum.COMPLETE.isEquals(DocLogEnum.valueOf(inst
							.getNextLog()))) {
				inst.next();
				Assert.notNull(inst);
			}
		}
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.las.service.LMS1925Service#getBorrows(java.lang.String
	 * , tw.com.iisi.cap.dao.utils.ISearch)
	 */
	@Override
	public Page<Map<String, Object>> getBorrows(String ownBrId,
			String docStatus, ISearch search) {
		List<Object[]> metaList = l192m01aDao.getDistinctFinishBorrowId(
				ownBrId, docStatus, search);
		List<Map<String, Object>> beanList = new ArrayList<Map<String, Object>>();
		for (Object[] meta : metaList) {
			Map<String, Object> data = new HashMap<String, Object>();
			data.put("custId", (String) meta[0]);
			data.put("dupNo", (String) meta[1]);
			data.put("custName", (String) meta[2]);
			beanList.add(data);
		}

		return new Page<Map<String, Object>>(beanList,
				l192m01aDao.getCountDistinctFinishBorrowId(ownBrId, docStatus),
				search.getMaxResults(), search.getFirstResult());

	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.las.service.LMS1925Service#saveAndSendDocument(com
	 * .mega.eloan.lms.model.L192M01A)
	 */
	@Override
	public void saveAndSendDocument(L192M01A l192m01a) {
		saveL192M01A(l192m01a);
		flowControl(l192m01a.getOid(), null);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.mega.eloan.lms.las.service.LMS1925Service#
	 * getLatestL192M01byBrNoShtTypeInnerAudit(java.lang.String,
	 * java.lang.String, java.lang.String)
	 */
	@Override
	public L192M01A getLatestL192M01byBrNoShtTypeInnerAudit(String brNo,
			String shtType, String innerAudit) {
		return l192m01aDao.findLatestL192M01byBrNoShtTypeInnerAudit(brNo,
				shtType, innerAudit);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.las.service.LMS1925Service#includeData(com.mega.eloan
	 * .lms.model.L192M01A, java.util.List, com.mega.eloan.lms.model.L192M01C,
	 * java.util.List, java.util.List)
	 */
	@Override
	public void includeData(L192M01A l192m01a, List<L192M01B> l192m01bs,
			L192M01C l192m01c, List<L192S01A> l192s01as,
			List<L192S02A> l192s02as) {

		reNewL192M01B(l192m01a.getOid(), l192m01bs);

		if (l192m01c != null) {
			L192M01C old = l192m01a.getL192m01c();
			old.setCk4Date(l192m01c.getCk4Date());
			old.setCk5Amt(l192m01c.getCk5Amt());
			old.setCk5Date(l192m01c.getCk5Date());
			old.setCk6Date(l192m01c.getCk6Date());
			old.setCk7Date(l192m01c.getCk7Date());
		}

		reNewL192S01A(l192m01a.getOid(), l192s01as);
		// l192s02as 擔保品資料重新引進功能等擔保品上線後再實做
		reNewL192S02A(l192m01a.getOid(), l192s02as);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.las.service.LMS1925Service#getLatestL120S01IByCustId
	 * (java.lang.String, java.lang.String)
	 */
	@Override
	public C120S01B getLatestC120S01BByCustId(String custId, String dupNo) {
		ISearch search = c120s01bDao.createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS,
				Meta_.custId.getName(), custId);
		search.addSearchModeParameters(SearchMode.EQUALS,
				Meta_.dupNo.getName(), dupNo);
		search.addOrderBy("updateTime", true);
		return c120s01bDao.findUniqueOrNone(search);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.las.service.LMS1925Service#printMark(com.mega.eloan
	 * .lms.model.L192M01A, java.lang.String)
	 */
	@Override
	public void printMark(L192M01A l192m01a, String printMark) {
		l192m01a.setCreateBill(printMark);
		l192m01aDao.save(l192m01a);
	}
}
