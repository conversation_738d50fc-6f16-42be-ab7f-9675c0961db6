package com.mega.eloan.lms.fms.handler.form;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URL;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.service.UserInfoService.SignEnum;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.LrsUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.fms.pages.LMS8000M01Page;
import com.mega.eloan.lms.fms.service.LMS8000Service;
import com.mega.eloan.lms.mfaloan.bean.ELF601;
import com.mega.eloan.lms.mfaloan.bean.ELF602;
import com.mega.eloan.lms.mfaloan.service.MisELF517Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.L260M01A;
import com.mega.eloan.lms.model.L260M01B;
import com.mega.eloan.lms.model.L260M01C;
import com.mega.eloan.lms.model.L260M01D;
import com.mega.eloan.lms.model.L260S01A;
import com.mega.eloan.lms.model.L260S01B;
import com.mega.eloan.lms.model.L260S01C;
import com.mega.eloan.lms.model.L260S01D;
import com.mega.eloan.lms.model.L260S01E;
import com.mega.eloan.lms.model.L260S01F;
import com.mega.eloan.lms.obsdb.service.ObsdbELF601Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.core.FlowException;

/**
 * <pre>
 * 貸後管理作業
 * 
 * </pre>
 * 
 * @since 2020
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Scope("request")
@Controller("lms8000m01formhandler")
@DomainClass(L260M01A.class)
public class LMS8000M01FormHandler extends AbstractFormHandler {

	@Resource
	ICustomerService customerSrv;

	@Resource
	BranchService branchService;

	@Resource
	DocFileService docFileService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	MisdbBASEService misdbBASEService;

	@Resource
	MisELF517Service misElf517Service;

	@Resource
	LMSService lmsService;

	@Resource
	LMS8000Service lms8000Service;

	@Resource
	CodeTypeService codetypeservice;

	@Resource
	ObsdbELF601Service obsdbELF601Service;

	@Resource
	UserInfoService userService;

	Properties pop = MessageBundleScriptCreator
			.getComponentResource(LMS8000M01Page.class);

	public IResult echo_custId(PageParameters params)
			throws CapException {
		// 儲存and檢核
		CapAjaxFormResult result = new CapAjaxFormResult();

		result.set("custId", Util.trim(params.getString("custId")));
		result.set("dupNo", Util.trim(params.getString("dupNo")));
		result.set("custName", Util.trim(params.getString("custName")));
		result.set("cntrNo", Util.trim(params.getString("cntrNo")));
		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult newl260m01a(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String brNo = user.getUnitNo();
		boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchService
				.getBranch(brNo).getBrNoFlag());

		L260M01A l260m01a = new L260M01A();
		String l260m01aMainid = "";
		l260m01aMainid = IDGenerator.getUUID();
		l260m01a.setDocStatus(CreditDocStatusEnum.海外_編製中.getCode());
		l260m01a.setOwnBrId(user.getUnitNo());
		l260m01a.setMainId(l260m01aMainid);
		String txCode = Util.trim(params
				.getString(EloanConstants.TRANSACTION_CODE));
		l260m01a.setTxCode(txCode);
		// UPGRADE: 待確認，URL是否正確
		l260m01a.setDocURL(params.getString("docUrl"));
		// l260m01a.setDeletedTime(CapDate.getCurrentTimestamp());
		l260m01a.setCntrNo(Util.trim(params.getString("cntrNo", "")));
		l260m01a.setCustId(Util.trim(params.getString("custId", null)));
		l260m01a.setDupNo(Util.trim(params.getString("dupNo", null)));
		if (Util.isEmpty(Util.trim(params.getString("custName", "")))) {
			String custName = "";
			if (isOverSea) {
				Map<String, Object> custData = obsdbELF601Service
						.findByIdDupNo(brNo,
								Util.trim(params.getString("custId", null)),
								Util.trim(params.getString("dupNo", null)));
				if (custData != null && !custData.isEmpty()) {
					String newCustName = Util.trim(MapUtils.getString(custData,
							"ELFCUST_ENAME"));
					custName = Util.toSemiCharString(newCustName); // 轉半形
					l260m01a.setCustName(custName);
				}
			}
			if (Util.isEmpty(custName)) {
				Map<String, Object> latestData = customerSrv.findByIdDupNo(
						Util.trim(params.getString("custId", null)),
						Util.trim(params.getString("dupNo", null)));
				if (latestData != null && !latestData.isEmpty()) {
					String newCustName = Util.trim(MapUtils.getString(
							latestData, "CNAME"));
					custName = Util.toSemiCharString(newCustName); // 轉半形
					l260m01a.setCustName(custName);
				} else {
					l260m01a.setCustName(null);
				}
			}
		} else {
			l260m01a.setCustName(Util.trim(params.getString("custName", null)));
		}
		String newType = Util.trim(params.getString("newType", ""));
		if (Util.equals("2", newType)) {
			l260m01a.setLoanNo(Util.trim(params.getString("loanNo")));
		} else {
			l260m01a.setLoanNo("");
		}

		lms8000Service.save(l260m01a);

		// ELF601-控制檔 ELF602-紀錄檔
		String fo = ""; // 帳務行員代號
		String ao = ""; // AO行員代號
		List<ELF601> elf601s = null;
		List<ELF602> elf602s = null;
		List<ELF601> elf601sId = null;
		List<ELF602> elf602sId = null;
		if (Util.notEquals(newType, "0")) { // 非ID階層新增
			if (isOverSea) {
				elf601s = obsdbELF601Service.getElf601ByCntrNoLoanNo(
						Util.trim(l260m01a.getOwnBrId()),
						Util.trim(l260m01a.getCntrNo()),
						Util.trim(l260m01a.getLoanNo()));
				elf602s = obsdbELF601Service.getElf602ByCntrNoLoanNo(
						Util.trim(l260m01a.getOwnBrId()),
						Util.trim(l260m01a.getCntrNo()),
						Util.trim(l260m01a.getLoanNo()), false);
			} else {
				List<Map<String, Object>> lnf020List = misdbBASEService
						.findLnf020(Util.trim(l260m01a.getCntrNo()));
				if (lnf020List != null && !lnf020List.isEmpty()
						&& lnf020List.size() > 0) {
					fo = lnf020List.get(0).get("LNF020_FO_STAFFNO").toString();
					ao = lnf020List.get(0).get("LNF020_AO_STAFFNO").toString();
				}

				elf601s = misdbBASEService.getElf601ByCntrNoLoanNo(
						Util.trim(l260m01a.getCntrNo()),
						Util.trim(l260m01a.getLoanNo()),
						Util.trim(l260m01a.getOwnBrId()));
				elf602s = misdbBASEService.getElf602ByCntrNoLoanNo(
						Util.trim(l260m01a.getCntrNo()),
						Util.trim(l260m01a.getLoanNo()), false,
						Util.trim(l260m01a.getOwnBrId()));
			}
		}

		// ID 階層資料 不管newType都要查的到
		if (isOverSea) {
			elf601sId = obsdbELF601Service.getElf601OnlyById(
					Util.trim(l260m01a.getCustId()),
					Util.trim(l260m01a.getDupNo()),
					Util.trim(l260m01a.getOwnBrId()));
			elf602sId = obsdbELF601Service.getElf602OnlyById(
					Util.trim(l260m01a.getCustId()),
					Util.trim(l260m01a.getDupNo()), false,
					Util.trim(l260m01a.getOwnBrId()));
		} else {
			elf601sId = misdbBASEService.getElf601OnlyById(
					Util.trim(l260m01a.getCustId()),
					Util.trim(l260m01a.getDupNo()),
					Util.trim(l260m01a.getOwnBrId()));
			elf602sId = misdbBASEService.getElf602OnlyById(
					Util.trim(l260m01a.getCustId()),
					Util.trim(l260m01a.getDupNo()), false,
					Util.trim(l260m01a.getOwnBrId()));
		}

		List<L260M01C> l260m01cList = new ArrayList<L260M01C>();
		if (elf601s != null && elf601s.size() > 0) {
			this.elf601ToL260M01C(l260m01a.getMainId(), elf601s, l260m01cList,
					false, fo, ao);
		}
		// J-110-0363 By ID
		if (elf601sId != null && elf601sId.size() > 0) {
			this.elf601ToL260M01C(l260m01a.getMainId(), elf601sId,
					l260m01cList, false, fo, ao);
		}
		lms8000Service.saveL260m01cList(l260m01cList);

		List<L260M01D> l260m01dList = new ArrayList<L260M01D>();
		if (elf602s != null && elf602s.size() > 0) {
			this.elf602ToL260M01D(l260m01a.getMainId(), elf602s, l260m01dList,
					false);
		}
		// J-110-0363 By ID
		if (elf602sId != null && elf602sId.size() > 0) {
			this.elf602ToL260M01D(l260m01a.getMainId(), elf602sId,
					l260m01dList, false);
		}
		lms8000Service.saveL260m01dList(l260m01dList);

		// 處理附加檔案複製
		lms8000Service.copyDocFile(l260m01aMainid, false, false, null);

		// 處理理財商品
		// 海外沒有理財商品
		if (!isOverSea) {
			this.findDwFinProd(l260m01aMainid);
		}

		// J-110-0497 餘屋貸款
		// 處理餘屋貸款 海外沒有餘屋貸款
		if (!isOverSea) {
			this.findMis517Data(l260m01aMainid);
		}

		// J-111-0025 實價登錄 海外沒有
		if (!isOverSea) {
			this.findRaspData(l260m01aMainid, l260m01dList);
		}

		// J-112-0307
		// 於eloan貸後管理追蹤檢核表維護功能中，新增一鍵生成公司訪問紀錄表及借戶、負責人、所屬企業集團等關係戶於本行各項業務往來彙總表
		// 公司訪問記錄表，海外沒有
		if (!isOverSea) {
			lms8000Service.findVisitCompData(l260m01dList);
		}
		
		// J-113-0035 為利ESG案件之貸後管控,
		// ELOAN國內外企金系統額度明細表其他敘做條件增加「應注意/承諾/待追蹤/ESG連結條款」的登錄機制
		lms8000Service.findLastESGDataAndSave(l260m01dList);

		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set(EloanConstants.OID, l260m01a.getOid());
		result.set(EloanConstants.MAIN_ID, l260m01a.getMainId());
		result.set(EloanConstants.MAIN_OID, l260m01a.getOid());
		result.set(EloanConstants.MAIN_DOC_STATUS, l260m01a.getDocStatus());
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult deleteUploadFile(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String fid = params.getString("fileOid");
		if (docFileService.clean(fid)) {
			// 刪除實價登錄資料
			String delRaspMsg = this.deleteL260s01cListByRaspAttach(fid);
			if (Util.isNotEmpty(delRaspMsg)) {
				throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, delRaspMsg),
						getClass());
			}

			// EFD0019=INFO|刪除成功|
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
					.getMainMessage(UtilConstants.AJAX_RSP_MSG.刪除成功));
		}
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryDetail(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString("tableOid"));// Util.trim(params.getString(EloanConstants.OID));
		String table = Util.trim(params.getString("table"));

		GenericBean model = null;
		if (Util.equals(table, "L260M01C")) { // 控制檔
			L260M01C l260m01c = lms8000Service.findModelByOid(L260M01C.class,
					oid);
			model = l260m01c;
		} else if (Util.equals(table, "L260M01D")) { // 紀錄檔
			L260M01D l260m01d = lms8000Service.findModelByOid(L260M01D.class,
					oid);
			model = l260m01d;
		}

		if (model == null) {
			if (!Util.isEmpty(oid)) {
				// 第一次啟案自動塞入前案資訊
				// lastData = addL260m01a(lastData);
			} else {
				// 開啟新案帶入起案的分行和目前文件狀態
				result.set(
						"docStatus",
						this.getMessage("docStatus."
								+ CreditDocStatusEnum.海外_編製中.getCode()));
				result.set("ownBrId", user.getUnitNo());
				result.set(
						"ownBrName",
						StrUtils.concat(" ",
								branchService.getBranchName(user.getUnitNo())));
				result.set("docStatusVal", CreditDocStatusEnum.海外_編製中.getCode());
			}
		} else {
			if (!Util.isEmpty(oid)) {
				result = detailResultShow(result, model, page);
			}
		}

		return result;
	}

	/**
	 * 格式化顯示訊息
	 * 
	 * @return
	 * @throws CapException
	 */
	private CapAjaxFormResult detailResultShow(CapAjaxFormResult result,
			GenericBean model, Integer page) throws CapException {
		String mainId = "";
		String modelName = "";
		if (model != null) {
			if (model instanceof L260M01C) {
				modelName = "L260M01C";
				L260M01C l260m01c = (L260M01C) model;
				mainId = Util.nullToSpace(l260m01c.getMainId());
				result = DataParse.toResult(l260m01c, DataParse.Delete,
						new String[] { EloanConstants.MAIN_ID,
								EloanConstants.OID, "cntrNo", "loanNo",
								"checkYN", "unid" });
				result.set("oidL260M01C", CapString.trimNull(l260m01c.getOid()));
				result.set("unidL260M01C", l260m01c.getUnid());
				// result.set(EloanConstants.OID,
				// CapString.trimNull(l260m01c.getOid()));
				// result.set(EloanConstants.MAIN_OID,
				// CapString.trimNull(l260m01c.getOid()));
				// result.set(EloanConstants.MAIN_ID,
				// CapString.trimNull(l260m01c.getMainId()));
			} else if (model instanceof L260M01D) {
				modelName = "L260M01D";
				L260M01D l260m01d = (L260M01D) model;
				mainId = Util.nullToSpace(l260m01d.getMainId());
				result = DataParse.toResult(l260m01d, DataParse.Delete,
						new String[] { EloanConstants.MAIN_ID,
								EloanConstants.OID, "cntrNo", "loanNo",
								"checkYN", "unid" });
				result.set("oidL260M01D", CapString.trimNull(l260m01d.getOid()));
				result.set("unidL260M01D", l260m01d.getUnid());
				// result.set(EloanConstants.OID,
				// CapString.trimNull(l260m01d.getOid()));
				// result.set(EloanConstants.MAIN_OID,
				// CapString.trimNull(l260m01d.getOid()));
				// result.set(EloanConstants.MAIN_ID,
				// CapString.trimNull(l260m01d.getMainId()));
				// J-110-0497 餘屋貸款
				this.setL260S01B(result, l260m01d);

				if (Util.isNotEmpty(l260m01d.getCntrNo())
						&& Util.isNotEmpty(l260m01d.getLoanNo())) {
					result.set("hasLoanNo", "Y");
				}

				// J-110-0548 擔保品謄本
				result.set("hasEland", "N");
				if (lms8000Service.isCaseMark(l260m01d, "01")) {
					result.set("hasEland", "Y");
				}

				// J-111-0025 實價登錄
				result.set("hasRasp", "N");
				if (lms8000Service.isCaseMark(l260m01d, "02")) {
					result.set("hasRasp", "Y");
				}

				// J-111-0496 央行不動產管控
				result.set("hasConstr", "N");
				if (lms8000Service.isCaseMark(l260m01d, "04")) {
					result.set("hasConstr", "Y");
				}
			}
		}

		String keys[] = new String[] { "oidL260M01C", "oidL260M01D",
				"unidL260M01C", "unidL260M01D", "followKind", "followContent" };
		for (String key : keys) {
			if (!result.containsKey(key)) {
				result.set(key, "");
			}
		}

		// J-110-0363 By ID
		if (Util.isNotEmpty(mainId) && Util.isNotEmpty(modelName)) {
			String unid = Util.equals(modelName, "L260M01C") ? result.get(
					"unidL260M01C").toString() : result.get("unidL260M01D")
					.toString();
			L260M01A l260m01a = lms8000Service.findModelByMainId(
					L260M01A.class, mainId);
			if (l260m01a != null) {
				String reStr = lms8000Service.findDataByIdAndM01A(modelName,
						l260m01a, unid);
				if (reStr.length() > 0) {
					result.set("showCantEdit", reStr);
				}
			}
		}

		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL260m01a(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainDocStatus = Util.trim(params
				.getString(EloanConstants.MAIN_DOC_STATUS));
		if (Util.equals(mainDocStatus, CreditDocStatusEnum.海外_呈總行.getCode())) {
			String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
			String custId = Util.trim(params.getString("custId"));
			String dupNo = Util.trim(params.getString("dupNo"));
			String custName = Util.trim(params.getString("custName"));
			String cntrNo = Util.trim(params.getString("cntrNo"));
			String loanNo = Util.trim(params.getString("loanNo"));
			result.set("custId", custId);
			result.set("dupNo", dupNo);
			result.set("custName", custName);
			result.set("cntrNo", cntrNo);
			result.set("loanNo", loanNo);
			result.set(
					"showCustId",
					StrUtils.concat(CapString.trimNull(custId), " ",
							CapString.trimNull(dupNo), " ",
							CapString.trimNull(custName)));
			result.set("docStatusVal", mainDocStatus);
			// result.set(EloanConstants.OID,
			// CapString.trimNull(l260m01a.getOid()));
			// result.set(EloanConstants.MAIN_OID,
			// CapString.trimNull(l260m01a.getOid()));
			result.set(EloanConstants.MAIN_ID, mainId);
			result.set(EloanConstants.MAIN_DOC_STATUS, mainDocStatus);

			String oid = Util.trim(params.getString(EloanConstants.OID));
			if (!Util.isEmpty(oid)) {
				L260M01A l260m01a = lms8000Service.findModelByOid(
						L260M01A.class, oid);
				if (l260m01a == null) {

				} else {
					result.set(EloanConstants.OID,
							CapString.trimNull(l260m01a.getOid()));
					result.set(EloanConstants.MAIN_OID,
							CapString.trimNull(l260m01a.getOid()));
				}
			}
		} else {
			int page = Util.parseInt(params.getString(EloanConstants.PAGE));
			String oid = Util.trim(params.getString(EloanConstants.OID));

			L260M01A lastData = lms8000Service.findModelByOid(L260M01A.class,
					oid);
			if (lastData == null) {
				if (!Util.isEmpty(oid)) {
					// 第一次啟案自動塞入前案資訊
					// lastData = addL260m01a(lastData);
				} else {
					// 開啟新案帶入起案的分行和目前文件狀態
					result.set(
							"docStatus",
							this.getMessage("docStatus."
									+ CreditDocStatusEnum.海外_編製中.getCode()));
					result.set("ownBrId", user.getUnitNo());
					result.set("ownBrName", StrUtils.concat(" ",
							branchService.getBranchName(user.getUnitNo())));
					result.set("docStatusVal",
							CreditDocStatusEnum.海外_編製中.getCode());
				}
			} else {
				if (!Util.isEmpty(oid)) {
					result = formatResultShow(result, lastData, page);
				}
			}

			// result.set("showCustId",
			// StrUtils.concat(CapString.trimNull(lastData.getCustId()), " ",
			// CapString.trimNull(lastData.getDupNo()), " ",
			// CapString.trimNull(lastData.getCustName())));
		}

		Locale locale = null;
		locale = LocaleContextHolder.getLocale();
		if (locale == null)
			locale = Locale.getDefault();
		Map<String, String> wordingMap = codetypeservice.findByCodeType(
				"postLoan_wording", locale.toString());
		ArrayList<String> wording = new ArrayList<String>();
		for (String key : wordingMap.keySet()) {
			wording.add(key + ".　" + wordingMap.get(key));
		}
		result.set("WORDING", StringUtils.join(wording, "<br/>"));

		return result;
	}

	/**
	 * 格式化顯示訊息
	 * 
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	private CapAjaxFormResult formatResultShow(CapAjaxFormResult result,
			L260M01A l260m01a, Integer page) throws CapException {
		String mainId = l260m01a.getMainId();

		switch (page) {
		case 1:
			result = DataParse.toResult(l260m01a);

			List<L260M01B> l260m01bList = (List<L260M01B>) lms8000Service
					.findListByMainId(L260M01B.class, mainId);
			if (!Util.isEmpty(l260m01bList)) { // 取得人員職稱
				StringBuilder bossId = new StringBuilder("");
				for (L260M01B l260m01b : l260m01bList) {
					// 要加上人員代碼
					String type = Util.trim(l260m01b.getStaffJob());
					String userId = Util.trim(l260m01b.getStaffNo());
					String value = Util.trim(lmsService.getUserName(userId));
					if ("L1".equals(type)) { // L1. 分行經辦
						result.set("showApprId", userId + " " + value);
					} else if ("L3".equals(type)) { // L3. 分行授信主管
						bossId.append(bossId.length() > 0 ? "<br/>" : "");
						bossId.append(userId).append(" ").append(value);
					} else if ("L4".equals(type)) { // L4. 分行覆核主管
						result.set("reCheckId", userId + " " + value);
					} else if ("L5".equals(type)) { // L5. 經副襄理
						result.set("managerId", userId + " " + value);
					} else if ("L6".equals(type)) { // L6. 總行經辦
						result.set("mainApprId", userId + " " + value);
					} else if ("L7".equals(type)) { // L7.總行主管
						result.set("mainReCheckId", userId + " " + value);
					}
				}
				result.set("bossId", bossId.toString());
			}
			result.set("ownBrName",
					" " + branchService.getBranchName(l260m01a.getOwnBrId()));

			result.set("creator", lmsService.getUserName(l260m01a.getCreator()));
			result.set("updater", lmsService.getUserName(l260m01a.getUpdater()));
			result.set("createTime",
					Util.nullToSpace(TWNDate.valueOf(l260m01a.getCreateTime())));
			result.set("updateTime",
					Util.nullToSpace(TWNDate.valueOf(l260m01a.getUpdateTime())));
			result.set("docStatus",
					getMessage("docStatus." + l260m01a.getDocStatus()));
			break;
		}// close switch case

		result.set("docStatusVal", l260m01a.getDocStatus());
		result.set("custId", Util.trim(l260m01a.getCustId()));
		result.set("dupNo", Util.trim(l260m01a.getDupNo()));
		result.set("custName", Util.trim(l260m01a.getCustName()));
		result.set("cntrNo", l260m01a.getCntrNo());
		result.set("loanNo", Util.trim(l260m01a.getLoanNo()));
		result.set("rtnModifyReason", Util.trim(l260m01a.getRtnModifyReason()));
		result.set(EloanConstants.OID, CapString.trimNull(l260m01a.getOid()));
		result.set(EloanConstants.MAIN_OID,
				CapString.trimNull(l260m01a.getOid()));
		result.set(EloanConstants.MAIN_ID,
				CapString.trimNull(l260m01a.getMainId()));
		result.set(EloanConstants.MAIN_DOC_STATUS,
				CapString.trimNull(l260m01a.getDocStatus()));
		result.set("showCustId", StrUtils.concat(
				CapString.trimNull(l260m01a.getCustId()), " ",
				CapString.trimNull(l260m01a.getDupNo()), " ",
				CapString.trimNull(l260m01a.getCustName())));

		String cntrNo = Util.trim(l260m01a.getCntrNo());
		Map<String, Object> r = lms8000Service.findLastPrint_L140M01A(cntrNo); // 已核准；
																				// 非不變、取消；
		if (r != null && !r.isEmpty()) {
			result.set("showImpL260M01C", Util.trim(r.get("FORMATTYPE")));
		}

		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult deleteL260M01C(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString("oid", "");
		String errMsg ="";
		if (Util.isNotEmpty(oid)) {
			L260M01C l260m01c = lms8000Service.findModelByOid(L260M01C.class,
					oid);
			if (l260m01c != null) {
				
				Map<String, String> followKindMap = lms8000Service
						.followKindToMap(l260m01c.getFollowKind());
				// J-113-0035 為利ESG案件之貸後管控,
				// ELOAN國內外企金系統額度明細表其他敘做條件增加「應注意/承諾/待追蹤/ESG連結條款」的登錄機制
				// J-113-0349 開放可以自行新增 永續績效目標檢視/其他ESG條件，user手動新增的可以刪除
				if (Util.notEquals(l260m01c.getStatus(), "P")
						&& Util.notEquals(l260m01c.getStatusForShow(), "2")) {
					// 狀態不為已追蹤的，不讓其刪除
					if (followKindMap
							.containsKey(UtilConstants.Lms8000m01_fllowKind.永續績效目標檢視)) {
						errMsg += pop.getProperty("msg.delChk1");
						errMsg += "，";
						errMsg += pop.getProperty("msg.notDel");
						errMsg += "<br/>";
						throw new CapMessageException(errMsg, getClass());
					}

					if (followKindMap
							.containsKey(UtilConstants.Lms8000m01_fllowKind.其他ESG條件)) {
						errMsg += pop.getProperty("msg.delChk2");
						errMsg += "，";
						errMsg += pop.getProperty("msg.notDel");
						errMsg += "<br/>";
						throw new CapMessageException(errMsg, getClass());
					}
				}
		
				// J-110-0363 By ID
				// ID階層才檢查
				if (Util.isEmpty(Util.trim(l260m01c.getCntrNo()))
						&& Util.isEmpty(Util.trim(l260m01c.getLoanNo()))) {
					if (Util.equals(Util.nullToSpace(l260m01c.getUnid()), "new")) {
						// 本次新增的不會有重複問題
					} else {
						L260M01A l260m01a = lms8000Service.findModelByMainId(
								L260M01A.class, l260m01c.getMainId());
						if (l260m01a != null) {
							String reStr = lms8000Service.findDataByIdAndM01A(
									"L260M01C", l260m01a, l260m01c.getUnid());
							if (reStr.length() > 0) {
								throw new CapMessageException(reStr, getClass());
							}
						}
					}
				}
				// P-新增(用來判斷為user新增，上傳中心時轉為N)
				// 因屬user新增 故不更改為 C-解除 狀態
				if (Util.notEquals(l260m01c.getStatus(), "P")) {
					l260m01c.setStatus("C"); // C-解除
					l260m01c.setCheckYN("Y"); // 用來判斷要上傳MIS
				} else {
					l260m01c.setDeletedTime(CapDate.getCurrentTimestamp());
				}
				lms8000Service.save(l260m01c);
			}
		}

		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));

		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult deleteL260M01D(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] oids = params.getStringArray("oids");
		List<L260M01D> l260m01dList = new ArrayList<L260M01D>();

		// J-112-0307
		// 於eloan貸後管理追蹤檢核表維護功能中，新增一鍵生成公司訪問紀錄表及借戶、負責人、所屬企業集團等關係戶於本行各項業務往來彙總表。
		List<L260S01D> l260s01dList = new ArrayList<L260S01D>();
		List<L260S01E> l260s01eList = new ArrayList<L260S01E>();
		List<L260S01F> l260s01fList = new ArrayList<L260S01F>();
		
		String errMsg = "";
		if (oids.length > 0) {
			for (String oid : oids) {
				L260M01D l260m01d = lms8000Service.findModelByOid(
						L260M01D.class, oid);
				if (l260m01d != null) {

					Map<String, String> followKindMap = lms8000Service
							.followKindToMap(l260m01d.getFollowKind());

					if (Util.trim(l260m01d.getUnid()).contains("@68@")) {
						errMsg += pop.getProperty("msg.delChk4");
						errMsg += "，";
						errMsg += pop.getProperty("msg.notDel");
						errMsg += "<br/>";
						continue;
					}
					
					if (Util.trim(l260m01d.getDataFrom()).equals("B")) {
						// 批次產生的
						
						if (followKindMap
								.containsKey(UtilConstants.Lms8000m01_fllowKind.永續績效目標檢視)) {
							// J-113-0035 為利ESG案件之貸後管控,
							// ELOAN國內外企金系統額度明細表其他敘做條件增加「應注意/承諾/待追蹤/ESG連結條款」的登錄機制
							errMsg += pop.getProperty("msg.delChk1");
							errMsg += "，";
							errMsg += pop.getProperty("msg.notDel");
							errMsg += "<br/>";
							continue;
						} else if (followKindMap
								.containsKey(UtilConstants.Lms8000m01_fllowKind.其他ESG條件)) {
							// J-113-0035 為利ESG案件之貸後管控,
							// ELOAN國內外企金系統額度明細表其他敘做條件增加「應注意/承諾/待追蹤/ESG連結條款」的登錄機制
							errMsg += pop.getProperty("msg.delChk2");
							errMsg += "，";
							errMsg += pop.getProperty("msg.notDel");
							errMsg += "<br/>";
							continue;
						} else if (followKindMap
								.containsKey(UtilConstants.Lms8000m01_fllowKind.綠色授信)) {
							// J-112-0418
							// 配合企金處依據企金處112年一般業務查核意見，E-LOAN企金授信管理系統修改增加「綠色授信」註記之管控機制等。
							errMsg += pop.getProperty("msg.delChk3");
							errMsg += "，";
							errMsg += pop.getProperty("msg.notDel");
							errMsg += "<br/>";
							continue;
						} else if (followKindMap
								.containsKey(UtilConstants.Lms8000m01_fllowKind.公司訪問紀錄表限ID階層)) {
							// J-112-0307
							// 於eloan貸後管理追蹤檢核表維護功能中，新增一鍵生成公司訪問紀錄表及借戶、負責人、所屬企業集團等關係戶於本行各項業務往來彙總表。
							errMsg += MessageFormat.format(pop
									.getProperty("msg.delChk5"), CapDate
									.formatDate(l260m01d.getFollowDate(),
											"yyyy-MM-dd"));
							errMsg += "，";
							errMsg += pop.getProperty("msg.notDel");
							errMsg += "<br/>";
							continue;
						}
					} else {
						List<L260S01D> l260s01dListFromDB = (List<L260S01D>) lms8000Service
								.findListByMainIdNotDel(L260S01D.class,
										l260m01d.getOid(), true);
						if (!Util.isEmpty(l260s01dListFromDB)) {
							for (L260S01D l260s01d : l260s01dListFromDB) {
								List<L260S01E> l260s01eListFromDB = (List<L260S01E>) lms8000Service
										.findListByMainIdNotDel(L260S01E.class,
												l260s01d.getOid(), true);						
								if (!Util.isEmpty(l260s01eListFromDB)) {
									for (L260S01E l260s01e : l260s01eListFromDB) {
										l260s01e.setDeletedTime(CapDate
												.getCurrentTimestamp());
										l260s01e.setUpdater(user.getUserId());
										l260s01e.setUpdateTime(CapDate
												.getCurrentTimestamp());
										l260s01eList.add(l260s01e);
									}
								}

								l260s01d.setDeletedTime(CapDate
										.getCurrentTimestamp());
								l260s01d.setUpdater(user.getUserId());
								l260s01d.setUpdateTime(CapDate
										.getCurrentTimestamp());
								l260s01dList.add(l260s01d);
							}
						}
						// J-113-0349  開放可以自行新增  永續績效目標檢視/其他ESG條件，刪除時要一起刪
						List<L260S01F> l260s01fListFromDB = (List<L260S01F>) lms8000Service
						.findListByMainIdNotDel(L260S01F.class,
								l260m01d.getOid(), true);
						if (!Util.isEmpty(l260s01fListFromDB)) {
							for (L260S01F l260s01f : l260s01fListFromDB) {
								l260s01f.setDeletedTime(CapDate
										.getCurrentTimestamp());
								l260s01f.setUpdater(user.getUserId());
								l260s01f.setUpdateTime(CapDate
										.getCurrentTimestamp());
								l260s01fList.add(l260s01f);
							}
						}
					}

					// J-110-0363 By ID
					// ID階層才檢查
					if (Util.isEmpty(Util.trim(l260m01d.getCntrNo()))
							&& Util.isEmpty(Util.trim(l260m01d.getLoanNo()))) {
						if (Util.equals(Util.nullToSpace(l260m01d.getUnid()),
								"new")) {
							// 本次新增的不會有重複問題
						} else {
							L260M01A l260m01a = lms8000Service
									.findModelByMainId(L260M01A.class,
											l260m01d.getMainId());
							if (l260m01a != null) {
								String reStr = lms8000Service
										.findDataByIdAndM01A("L260M01D",
												l260m01a, l260m01d.getUnid());
								if (reStr.length() > 0) {
									throw new CapMessageException(
											UtilConstants.AJAX_RSP_MSG.執行有誤
													+ "，" + reStr, getClass());
								}
							}
						}
					}
					// P-新增(用來判斷為user新增)
					// 因屬user新增 故不更改為 4-已刪除 狀態
					String dataFrom = l260m01d.getDataFrom();
					if (Util.notEquals(dataFrom, "P")) {
						// 4-已刪除
						l260m01d.setHandlingStatus("4");
						l260m01d.setCheckYN("Y"); // 用來判斷要上傳MIS
					} else {
						l260m01d.setDeletedTime(CapDate.getCurrentTimestamp());
					}
					l260m01d.setUpdater(user.getUserId());
					l260m01d.setUpdateTime(CapDate.getCurrentTimestamp());
					l260m01dList.add(l260m01d);
				}
			}
		}
		if (l260m01dList.size() > 0) {
			lms8000Service.saveL260m01dList(l260m01dList);
		}

		// J-112-0307
		// 於eloan貸後管理追蹤檢核表維護功能中，新增一鍵生成公司訪問紀錄表及借戶、負責人、所屬企業集團等關係戶於本行各項業務往來彙總表。
		if (l260s01dList.size() > 0) {
			lms8000Service.saveL260s01dList(l260s01dList);
		}
		if (l260s01eList.size() > 0) {
			lms8000Service.saveL260s01eList(l260s01eList);
		}
		// J-113-0349  開放可以自行新增  永續績效目標檢視/其他ESG條件，刪除時要一起刪
		if (l260s01fList.size() > 0) {
			lms8000Service.saveL260s01fList(l260s01fList);
		}
		
		if (Util.notEquals(errMsg, "")) {
			throw new CapMessageException(UtilConstants.AJAX_RSP_MSG.執行有誤 + "，"
					+ errMsg, getClass());
		}

		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));

		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL260M01C(PageParameters params)
			throws CapException {
		String oid = Util.trim(params.getString("oid"));
		CapAjaxFormResult result = new CapAjaxFormResult();
		String form = Util.trim(params.getString("formDetail"));
		JSONObject jsonData = null;
		String errMsg = "";
		if (Util.isNotEmpty(oid)) {
			L260M01C l260m01c = lms8000Service.findModelByOid(L260M01C.class,
					oid);
			if (l260m01c != null) {
				jsonData = JSONObject.fromObject(form);
				DataParse.toBean(jsonData, l260m01c);
				// J-113-0035 為利ESG案件之貸後管控,
				// ELOAN國內外企金系統額度明細表其他敘做條件增加「應注意/承諾/待追蹤/ESG連結條款」的登錄機制
				// ELF603相關Key值不更新
				jsonData.remove("from606SUid");
				jsonData.remove("from601SApptime");
				jsonData.remove("from601SSeqno");
				
				// P-新增(用來判斷為user新增，上傳中心時轉為N)
				// 因屬user新增 故不更改為 U-修改 狀態
				if (Util.notEquals(l260m01c.getStatus(), "P")) {
					l260m01c.setStatus("U"); // U-修改
				}
				l260m01c.setStatusForShow(lms8000Service.getStatusForShow(
						Util.trim(l260m01c.getStatus()),
						l260m01c.getNextFollowDate()));
				errMsg = this.checkL260M01C(l260m01c, true);
				if (Util.isNotEmpty(errMsg)) {
					l260m01c.setCheckYN("N");
				} else {
					l260m01c.setCheckYN("Y");
				}
				lms8000Service.save(l260m01c);
			}
		}
		if (Util.isNotEmpty(errMsg)) {
			result.set("msg", errMsg);
		} else {
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
					.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));
		}
		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL260M01D(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String oid = Util.trim(params.getString("oid"));
		CapAjaxFormResult result = new CapAjaxFormResult();
		String form = Util.trim(params.getString("formDetail"));

		// J-112-0307
		// 於eloan貸後管理追蹤檢核表維護功能中，新增一鍵生成公司訪問紀錄表及借戶、負責人、所屬企業集團等關係戶於本行各項業務往來彙總表。
		String prtParam = Util.trim(params.getString("prtParam"));

		JSONObject jsonData = null;
		String errMsg = "";
		if (Util.isNotEmpty(oid)) {
			L260M01D l260m01d = lms8000Service.findModelByOid(L260M01D.class,
					oid);
			if (l260m01d != null) {
				jsonData = JSONObject.fromObject(form);
				// J-113-0035 為利ESG案件之貸後管控,
				// ELOAN國內外企金系統額度明細表其他敘做條件增加「應注意/承諾/待追蹤/ESG連結條款」的登錄機制
				// ELF603相關Key值不更新
				jsonData.remove("from602SUid");
				jsonData.remove("from602SApptime");
				jsonData.remove("from602SSeqno");
				
				DataParse.toBean(jsonData, l260m01d);
				
				Map<String, String> followKindMap = lms8000Service
						.followKindToMap(l260m01d.getFollowKind());
				
				// J-110-0497 餘屋貸款
				if (Util.equals(Util.nullToSpace(l260m01d.getCaseMark()), "03")) {
					// 理應只有一筆
					List<L260S01B> l260s01bList = (List<L260S01B>) lms8000Service
							.findListByMainIdNotDel(L260S01B.class,
									l260m01d.getOid(), true);
					if (l260s01bList != null && l260s01bList.size() > 0) {
						L260S01B l260s01b = l260s01bList.get(0);
						if (l260s01b != null) {
							DataParse.toBean(jsonData, l260s01b);
							lms8000Service.save(l260s01b);
						}
					} else {
						L260S01B l260s01b = new L260S01B();
						l260s01b.setMainId(oid); // L260M01D 的 oid
						l260s01b.setCntrNo(Util.nullToSpace(l260m01d
								.getCntrNo()));
						l260s01b.setCreator(user.getUserId());
						l260s01b.setCreateTime(CapDate.getCurrentTimestamp());
						DataParse.toBean(jsonData, l260s01b);
						lms8000Service.save(l260s01b);
					}
				}
				
				// J-113-0035 為利ESG案件之貸後管控,
				// ELOAN國內外企金系統額度明細表其他敘做條件增加「應注意/承諾/待追蹤/ESG連結條款」的登錄機制
				if (followKindMap
						.containsKey(UtilConstants.Lms8000m01_fllowKind.永續績效目標檢視)
						|| followKindMap
								.containsKey(UtilConstants.Lms8000m01_fllowKind.其他ESG條件)) {
					//J-113-0349_11850_B1001 開放可以自行新增  永續績效目標檢視/其他ESG條件, json沒有itemNo_S01F代表是新增
					if(Util.isEmpty(jsonData.get("itemNo_S01F"))){
						List<L260M01D> l260m01dList = new ArrayList<L260M01D>();
						l260m01dList.add(l260m01d);
						//走原始規則,生成L260S01F
						lms8000Service.findLastESGDataAndSave(l260m01dList);
					}else{
						lms8000Service.saveL260s01fList(l260m01d, jsonData);
					}
				}else{
					//J-113-0349_11850_B1001 開放可以自行新增  永續績效目標檢視/其他ESG條件
					//如異動fllowkind 移除 永續績效目標檢視/其他ESG條件, 則要把對應的 L260S01F砍掉
					List<L260S01F> l260s01fList = new ArrayList<L260S01F>();
					List<L260S01F> l260s01fListFromDB = (List<L260S01F>) lms8000Service
							.findListByMainIdNotDel(L260S01F.class,
									l260m01d.getOid(), true);
					if (!Util.isEmpty(l260s01fListFromDB)) {
						for (L260S01F l260s01f : l260s01fListFromDB) {
							l260s01f.setDeletedTime(CapDate
									.getCurrentTimestamp());
							l260s01f.setUpdater(user.getUserId());
							l260s01f.setUpdateTime(CapDate
									.getCurrentTimestamp());
							l260s01fList.add(l260s01f);
						}
					}
					if (l260s01fList.size() > 0) {
						lms8000Service.saveL260s01fList(l260s01fList);
					}
				}
								
				result.putAll(lms8000Service.saveL260S01D(jsonData, l260m01d));

				errMsg = this.checkL260M01D(l260m01d, true);
				if (Util.isNotEmpty(errMsg)) {
					l260m01d.setCheckYN("N");
				} else {
					l260m01d.setCheckYN("Y");
				}
				lms8000Service.save(l260m01d);

			}
		}
		
		if (Util.isNotEmpty(errMsg)) {
			result.set("msg", errMsg);
		} else if (Util.notEquals(prtParam, "")) {
			// J-112-0307
			// 於eloan貸後管理追蹤檢核表維護功能中，新增一鍵生成公司訪問紀錄表及借戶、負責人、所屬企業集團等關係戶於本行各項業務往來彙總表。
		} else {
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
					.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));
		}
		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL260M01A(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", "N"));
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		L260M01A l260m01a = null;
		if (Util.isNotEmpty(oid)) {
			l260m01a = lms8000Service.findModelByOid(L260M01A.class, oid);
			if (l260m01a != null) {
				l260m01a.setDeletedTime(null);

				lms8000Service.save(l260m01a);
				result.set("randomCode", l260m01a.getRandomCode());
			}
		}

		Boolean showMsg = params.getAsBoolean("showMsg", false);
		String errMsg = this.checkSaveData(l260m01a, false);
		if (Util.isEmpty(errMsg)) {
			if (showMsg) {
				errMsg = RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功);
			}
		} else {
			if (showMsg) {

			} else {
				throw new CapMessageException(errMsg, getClass());
			}
		}

		if (showMsg) {
			errMsg += this.checkComVisisVer(l260m01a.getMainId());
		}
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, errMsg);

		result.set(EloanConstants.OID, CapString.trimNull(l260m01a.getOid()));
		result.set(EloanConstants.MAIN_OID,
				CapString.trimNull(l260m01a.getOid()));
		result.set(EloanConstants.MAIN_ID,
				CapString.trimNull(l260m01a.getMainId()));
		result.set(EloanConstants.MAIN_DOC_STATUS,
				CapString.trimNull(l260m01a.getDocStatus()));

		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult checkData(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		// 儲存and檢核
		CapAjaxFormResult result = new CapAjaxFormResult();
		// 查詢所選銀行的甲級主管、乙級主管清單
		SignEnum[] signs = { SignEnum.首長, SignEnum.單位主管, SignEnum.甲級主管,
				SignEnum.乙級主管 };
		Map<String, String> bossList = userInfoService.findByBrnoAndSignId(
				user.getUnitNo(), signs);
		result.set("bossList", new CapAjaxFormResult(bossList));
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult calNextFollowDate(PageParameters params) {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString("oid");
		Integer followCycle = params.getAsInteger("followCycle", 0);
		String followBgnDate = params.getString("followBgnDate", "");
		String followEndDate = params.getString("followEndDate", "");
		boolean showMsg = params.getAsBoolean("showMsg", true);

		result.set("nextFollowDate", "");
		boolean success = false;
		if (Util.isNotEmpty(oid)) {
			L260M01C l260m01c = lms8000Service.findModelByOid(L260M01C.class,
					oid);
			if (l260m01c != null) {
				Date nextDate = this.calNextDate(followCycle, followBgnDate,
						followEndDate);
				if (nextDate != null) {
					l260m01c.setNextFollowDate(nextDate);
					lms8000Service.save(l260m01c);
					result.set("nextFollowDate", l260m01c.getNextFollowDate());
					success = true;
				} else {
					result.set("errMsg", "計算下次追蹤日有誤！");
					l260m01c.setCheckYN("N");
					lms8000Service.save(l260m01c);
				}
			}
		}

		if (success && showMsg) {
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
					.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		}

		return result;
	}

	public Date calNextDate(Integer followCycle, String followBgnDate,
			String followEndDate) {
		Date bgnDate = CapDate.parseDate(followBgnDate);
		// Date endDate = CapDate.parseDate(followEndDate);
		Date nextDate = CapDate.addMonth(bgnDate, followCycle);
		Date nowDate = CapDate.getCurrentTimestamp();
		if (BigDecimal.valueOf(followCycle).compareTo(BigDecimal.ZERO) > 0) {
			while (LMSUtil.cmpDate(nextDate, "<=", nowDate)) {
				nextDate = CapDate.addMonth(nextDate, followCycle);
			}
		} else {
			nextDate = null;
		}

		return nextDate;
	}

	public String checkL260M01C(L260M01C l260m01c, boolean isSave) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String brNo = user.getUnitNo();
		boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchService
				.getBranch(brNo).getBrNoFlag());

		StringBuffer temp = new StringBuffer();
		if (l260m01c.getDeletedTime() != null
				|| Util.equals(l260m01c.getStatus(), "C")) {
			return temp.toString();
		}
		if (isSave) {
			// 儲存時不管 checkYN 是否有值都要檢查
		} else {
			if (Util.isEmpty(l260m01c.getCheckYN())) {
				return temp.toString();
			}
		}

		String byIdFlag = "N";
		if (Util.isEmpty(Util.trim(l260m01c.getCntrNo()))
				&& Util.isEmpty(Util.trim(l260m01c.getLoanNo()))) {
			byIdFlag = "Y";
		}

		if (!isOverSea && Util.notEquals(byIdFlag, "Y")) { // ID階層不需輸入追蹤人員
			if (Util.isEmpty(Util.trim(l260m01c.getStaff()))) {
				// checkSelect=請選擇
				temp.append(temp.length() > 0 ? "<br/>" : "");
				temp.append(pop.getProperty("checkSelect") + "應辦理追蹤人員");
			}

			if (Util.isEmpty(Util.trim(l260m01c.getFo_staffNo()))
					&& Util.equals(Util.trim(l260m01c.getStaff()), "01")) {
				// checkSelect=請選擇
				temp.append(temp.length() > 0 ? "<br/>" : "");
				temp.append(pop.getProperty("checkSelect") + "授信人員");
			}

			if (Util.isEmpty(Util.trim(l260m01c.getAo_staffNo()))
					&& Util.equals(Util.trim(l260m01c.getStaff()), "02")) {
				// checkSelect=請選擇
				temp.append(temp.length() > 0 ? "<br/>" : "");
				temp.append(pop.getProperty("checkSelect") + "AO人員");
			}
		}

		if (Util.isEmpty(Util.trim(l260m01c.getFollowKind()))) {
			// L260M01C.followKind=類別 atLeast=至少一項
			temp.append(temp.length() > 0 ? "<br/>" : "");
			temp.append(pop.getProperty("L260M01C.followKind")
					+ pop.getProperty("atLeast"));
		}

		if (Util.isEmpty(Util.trim(l260m01c.getFollowContent()))) {
			// L260M01C.followContent=追蹤事項通知內容 notEmpty=不得空白
			temp.append(temp.length() > 0 ? "<br/>" : "");
			temp.append(pop.getProperty("L260M01C.followContent")
					+ pop.getProperty("notEmpty"));
		}

		if (Util.isEmpty(Util.trim(l260m01c.getFollowWay()))) {
			// checkSelect=請選擇 L260M01C.followWay=追蹤方式
			temp.append(temp.length() > 0 ? "<br/>" : "");
			temp.append(pop.getProperty("checkSelect")
					+ pop.getProperty("L260M01C.followWay"));
		}

		if (Util.isEmpty(Util.trim(l260m01c.getNextFollowDate()))) {
			// checkNextDate=請選擇或計算下次追蹤日
			temp.append(temp.length() > 0 ? "<br/>" : "");
			temp.append(pop.getProperty("checkNextDate"));
		}

		if (Util.equals(l260m01c.getFollowWay(), "2")) {
			if (Util.isEmpty(Util.trim(l260m01c.getFollowCycle()))) {
				// L260M01C.followCycle=循環追蹤週期 notEmpty=不得空白
				temp.append(temp.length() > 0 ? "<br/>" : "");
				temp.append(pop.getProperty("L260M01C.followCycle")
						+ pop.getProperty("notEmpty"));
			}
			if (Util.isEmpty(Util.trim(l260m01c.getFollowBgnDate()))) {
				// L260M01C.followBgnDate=循環追蹤起日 notEmpty=不得空白
				temp.append(temp.length() > 0 ? "<br/>" : "");
				temp.append(pop.getProperty("L260M01C.followBgnDate")
						+ pop.getProperty("notEmpty"));
			}
		}
		return temp.toString();
	}

	public String checkL260M01D(L260M01D l260m01d, boolean isSave) {
		StringBuffer temp = new StringBuffer();
		if (l260m01d.getDeletedTime() != null
				|| Util.equals(l260m01d.getHandlingStatus(), "4")) {
			return temp.toString();
		}
		if (isSave) {
			// 儲存時不管 checkYN 是否有值都要檢查
		} else {
			if (Util.isEmpty(l260m01d.getCheckYN())) {
				return temp.toString();
			}
		}
		if (Util.isEmpty(Util.trim(l260m01d.getHandlingStatus()))) {
			// checkSelect=請選擇 L260M01D.handlingStatus=辦理狀況
			temp.append(pop.getProperty("checkSelect")
					+ pop.getProperty("L260M01D.handlingStatus"));
		}

		Map<String, String> followKindMap = lms8000Service
				.followKindToMap(l260m01d.getFollowKind());
		
		// 辦理狀況為 3-已完成 才檢核
		if (Util.equals(l260m01d.getHandlingStatus(), "3")) {
			if (Util.isEmpty(Util.trim(l260m01d.getChkDate()))) {
				// L260M01D.chkDate=檢核日期 notEmpty=不得空白
				temp.append(pop.getProperty("L260M01D.chkDate")
						+ pop.getProperty("notEmpty"));
			}

			if (Util.isEmpty(Util.trim(l260m01d.getConformFg()))) {
				// checkSelect=請選擇 L260M01D.conformFg=符合註記
				temp.append(temp.length() > 0 ? "<br/>" : "");
				temp.append(pop.getProperty("checkSelect")
						+ pop.getProperty("L260M01D.conformFg"));
			}

			if (Util.isEmpty(Util.trim(l260m01d.getFollowMemo()))) {
				// L260M01D.followMemo=追蹤說明 notEmpty=不得空白
				temp.append(temp.length() > 0 ? "<br/>" : "");
				temp.append(pop.getProperty("L260M01D.followMemo")
						+ pop.getProperty("notEmpty"));
			}

			if (Util.isEmpty(Util.trim(l260m01d.getFileDesc()))) {
				// L260M01D.fileDesc=證明文件說明 notEmpty=不得空白
				temp.append(temp.length() > 0 ? "<br/>" : "");
				temp.append(pop.getProperty("L260M01D.fileDesc")
						+ pop.getProperty("notEmpty"));
			}

			if (Util.isEmpty(Util.trim(l260m01d.getRepayUnusualFg()))) {
				// L260M01D.repayUnusualFg=還款來源異常註記 notEmpty=不得空白
				temp.append(temp.length() > 0 ? "<br/>" : "");
				temp.append(pop.getProperty("L260M01D.repayUnusualFg")
						+ pop.getProperty("notEmpty"));
			}
			// 還款來源異常註記為Y 需輸入補充資料
			if (Util.equals(l260m01d.getRepayUnusualFg(), "Y")) {
				if (Util.isEmpty(Util.trim(l260m01d.getUnusualDesc()))) {
					// L260M01D.unusualDesc=理由敘述（留存調查記錄） notEmpty=不得空白
					temp.append(temp.length() > 0 ? "<br/>" : "");
					temp.append(pop.getProperty("L260M01D.unusualDesc")
							+ pop.getProperty("notEmpty"));
				}

				if (Util.isEmpty(Util.trim(l260m01d.getIsNotional()))) {
					// checkSelect=請選擇 L260M01D.isNotional=是否承做
					temp.append(temp.length() > 0 ? "<br/>" : "");
					temp.append(pop.getProperty("checkSelect")
							+ pop.getProperty("L260M01D.isNotional"));
				}

				if (Util.isEmpty(Util.trim(l260m01d.getIsAML()))) {
					// checkSelect=請選擇 L260M01D.isAML=是否申報疑似洗錢
					temp.append(temp.length() > 0 ? "<br/>" : "");
					temp.append(pop.getProperty("checkSelect")
							+ pop.getProperty("L260M01D.isAML"));
				}
			}

			// if(Util.isEmpty(Util.trim(l260m01d.getFinProdFg()))){
			// // L260M01D.isFinProd=是否申購理財商品 notEmpty=不得空白
			// temp.append(temp.length() > 0 ? "<br/>" : "");
			// temp.append(pop.getProperty("L260M01D.isFinProd") +
			// pop.getProperty("notEmpty"));
			// }
		}

		// J-110-0497 餘屋貸款
		if (Util.equals(Util.nullToSpace(l260m01d.getCaseMark()), "03")) {
			L260S01B l260s01b = null;
			// 理應只有一筆
			List<L260S01B> l260s01bList = (List<L260S01B>) lms8000Service
					.findListByMainIdNotDel(L260S01B.class, l260m01d.getOid(),
							true);
			if (l260s01bList != null && l260s01bList.size() > 0) {
				l260s01b = l260s01bList.get(0);
				if (l260s01b != null) {
					if (Util.isEmpty(Util.trim(l260s01b.getBuildName()))) {
						// L260S01B.buildName=建案名稱 notEmpty=不得空白
						temp.append(temp.length() > 0 ? "<br/>" : "");
						temp.append(pop.getProperty("L260S01B.buildName")
								+ pop.getProperty("notEmpty"));
					}
					if (Util.isEmpty(Util.trim(l260s01b.getBegForSell()))) {
						// L260S01B.begForSell=初貸餘屋戶數 notEmpty=不得空白
						temp.append(temp.length() > 0 ? "<br/>" : "");
						temp.append(pop.getProperty("L260S01B.begForSell")
								+ pop.getProperty("notEmpty"));
					}
					if (Util.isEmpty(Util.trim(l260s01b.getSoldNumber()))) {
						// L260S01B.soldNumber=已售戶數 notEmpty=不得空白
						temp.append(temp.length() > 0 ? "<br/>" : "");
						temp.append(pop.getProperty("L260S01B.soldNumber")
								+ pop.getProperty("notEmpty"));
					}
					if (Util.isEmpty(Util.trim(l260s01b.getProStatus()))) {
						// L260S01B.proStatus=進度狀態 notEmpty=不得空白
						temp.append(temp.length() > 0 ? "<br/>" : "");
						temp.append(pop.getProperty("L260S01B.proStatus")
								+ pop.getProperty("notEmpty"));
					}
					if (Util.equals(Util.trim(l260s01b.getProStatus()), "B")
							&& Util.isEmpty(Util.trim(l260s01b.getBehindDesc()))) {
						// L260S01B.behindDesc=落後原因 notEmpty=不得空白
						temp.append(temp.length() > 0 ? "<br/>" : "");
						temp.append(pop.getProperty("L260S01B.behindDesc")
								+ pop.getProperty("notEmpty"));
					}
					if (Util.isEmpty(Util.trim(l260s01b.getIsSameCase()))) {
						// checkSelect=請選擇
						// L260S01B.isSameCase=是否與兆豐金控子公司(例如兆票)辦理同一建案待售房屋貸款
						temp.append(temp.length() > 0 ? "<br/>" : "");
						temp.append(pop.getProperty("checkSelect")
								+ pop.getProperty("L260S01B.isSameCase"));
					}
				}
			} else {
				// L260M01D.tab4=餘屋貸款 dataMiss=尚有資料未輸入，請補齊
				temp.append(temp.length() > 0 ? "<br/>" : "");
				temp.append(pop.getProperty("L260M01D.tab4")
						+ pop.getProperty("dataMiss"));
			}
		}

		// J-111-0025 實價登錄
		List<L260S01C> l260s01cList = (List<L260S01C>) lms8000Service
				.findListByMainIdNotDel(L260S01C.class, l260m01d.getOid(), true);
		if (l260s01cList != null && l260s01cList.size() > 0) {
			for (L260S01C l260s01c : l260s01cList) {
				if (Util.notEquals(l260s01c.getCheckYN(), "Y")) {
					temp.append(temp.length() > 0 ? "<br/>" : "");
					temp.append(pop.getProperty("L260M01D.tab5") + "頁籤"
							+ pop.getProperty("dataMiss"));
					break;
				}
			}
		}

		// J-111-0496 央行不動產管控
		if (lms8000Service.isCaseMark(l260m01d, "04")) {
			StringBuffer tab6Str = new StringBuffer();
			if (Util.isEmpty(Util.trim(l260m01d.getBegConstr()))) {
				// checkSelect=請選擇 L260M01D.begConstr=是否已動工興建
				tab6Str.append(tab6Str.length() > 0 ? "<br/>" : "");
				tab6Str.append(pop.getProperty("checkSelect")
						+ pop.getProperty("L260M01D.begConstr"));
			} else {
				if (Util.equals(Util.trim(l260m01d.getBegConstr()), "Y")) {
					if (Util.isEmpty(Util.trim(l260m01d.getActStDate()))) {
						tab6Str.append(tab6Str.length() > 0 ? "<br/>" : "");
						tab6Str.append(pop.getProperty("L260M01D.actStDate")
								+ pop.getProperty("notEmpty"));
					} else {
						Date nowDate = CapDate.getCurrentTimestamp();
						if (LMSUtil.cmpDate(l260m01d.getActStDate(), ">",
								nowDate)) {
							tab6Str.append(tab6Str.length() > 0 ? "<br/>" : "");
							// checkDate5=不得大於今日
							tab6Str.append(pop.getProperty("checkData")
									+ pop.getProperty("L260M01D.actStDate")
									+ pop.getProperty("checkDate5"));
						}
					}
				}
			}
			if (tab6Str.length() > 0) {
				temp.append(temp.length() > 0 ? "<br/>" : "");
				temp.append(pop.getProperty("checkData")
						+ pop.getProperty("L260M01D.tab6") + "頁籤：" + "<br/>"
						+ tab6Str.toString());
			}
		}

		// J-112-0307
		// 於eloan貸後管理追蹤檢核表維護功能中，新增一鍵生成公司訪問紀錄表及借戶、負責人、所屬企業集團等關係戶於本行各項業務往來彙總表。
		boolean haveVisitComp = false;
		boolean haveESG = false;
		if (followKindMap
				.containsKey(UtilConstants.Lms8000m01_fllowKind.公司訪問紀錄表限ID階層)) {
			haveVisitComp = true;
		}

		if (followKindMap
				.containsKey(UtilConstants.Lms8000m01_fllowKind.永續績效目標檢視)
				|| followKindMap
						.containsKey(UtilConstants.Lms8000m01_fllowKind.其他ESG條件)) {
			haveESG = true;
		}
		
		if (haveVisitComp) {
			List<L260S01D> l260s01dList = (List<L260S01D>) lms8000Service
					.findListByMainIdNotDel(L260S01D.class, l260m01d.getOid(),
							true);
			if (Util.isNotEmpty(l260s01dList) && l260s01dList.size() > 0) {
				// check L260S01D
				L260S01D l260s01d = l260s01dList.get(0);
				if (Util.isEmpty(l260s01d.getVisitCompName())) {
					temp.append(temp.length() > 0 ? "<br/>" : "");
					temp.append(pop.getProperty("L260S01D.visitCompName"));
					temp.append(pop.getProperty("notEmpty"));
				}

				if (Util.isEmpty(l260s01d.getVisitWay())) {
					temp.append(temp.length() > 0 ? "<br/>" : "");
					temp.append(pop.getProperty("checkSelect"));
					temp.append(pop.getProperty("L260S01D.visitWay"));
				}

				if (Util.isEmpty(l260s01d.getVisitDt())) {
					temp.append(temp.length() > 0 ? "<br/>" : "");
					temp.append(pop.getProperty("L260S01D.visitDt"));
					temp.append(pop.getProperty("notEmpty"));
				}

				if (Util.isEmpty(l260s01d.getVisitPlace())) {
					temp.append(temp.length() > 0 ? "<br/>" : "");
					temp.append(pop.getProperty("L260S01D.visitPlace"));
					temp.append(pop.getProperty("notEmpty"));
				}

				if (Util.isEmpty(l260s01d.getVisitorJobTitle())) {
					temp.append(temp.length() > 0 ? "<br/>" : "");
					temp.append(pop.getProperty("L260S01D.visitorJobTitle"));
					temp.append(pop.getProperty("notEmpty"));
				}

				if (Util.isEmpty(l260s01d.getVisitorName())) {
					temp.append(temp.length() > 0 ? "<br/>" : "");
					temp.append(pop.getProperty("L260S01D.visitorName"));
					temp.append(pop.getProperty("notEmpty"));
				}

				if (Util.isEmpty(l260s01d.getVisitorPhone())) {
					temp.append(temp.length() > 0 ? "<br/>" : "");
					temp.append(pop.getProperty("L260S01D.visitorPhone"));
					temp.append(pop.getProperty("notEmpty"));
				}

				if (Util.isEmpty(l260s01d.getUnitMgr_S01D())) {
					temp.append(temp.length() > 0 ? "<br/>" : "");
					temp.append(pop.getProperty("checkSelect"));
					temp.append(pop.getProperty("L260S01D.unitMgr"));
				}

				if (Util.isEmpty(l260s01d.getAccountMgr_S01D())) {
					temp.append(temp.length() > 0 ? "<br/>" : "");
					temp.append(pop.getProperty("checkSelect"));
					temp.append(pop.getProperty("L260S01D.accountMgr"));
				}

				// check L260S01E
				List<L260S01E> l260s01eList = (List<L260S01E>) lms8000Service
						.findListByMainIdNotDel(L260S01E.class,
								l260s01d.getOid(), true);

				// 項目：至少勾一結果
				Map<String, String> chkTickMap = new HashMap<String, String>();
				// 項目：結果勾是時，做額外檢核
				Map<String, String> tickYMap = new HashMap<String, String>();
				// 項目：結果勾否時，做額外檢核
				Map<String, String> tickNMap = new HashMap<String, String>();
				// 項目：這一群裡，至少勾一個
				Map<String, String> atLeastTickOneMap = new HashMap<String, String>();
				boolean noCheckFlag = false;
				boolean atLeastTickOneFlag = false;
				String itemSeqShow = "";

				if (LrsUtil.compareRptVersion(l260s01d.getRptId_S01D(), "<=",
						UtilConstants.Lms8000m01_visitComVer.V_O_202308)) {
					// 放入ITEMNO、所屬項次
					chkTickMap.put("A002", "1");
					chkTickMap.put("A003", "1");
					chkTickMap.put("A004", "1");
					chkTickMap.put("A005", "1");
					chkTickMap.put("A006", "1");
					chkTickMap.put("A007", "1");
					chkTickMap.put("A008", "1");
					chkTickMap.put("A009", "1");
					chkTickMap.put("A010", "1");
					chkTickMap.put("A011", "1");
					chkTickMap.put("A012", "1");
					chkTickMap.put("A013", "1");
					chkTickMap.put("A014", "1");

					tickYMap.put("A002", "1");
					tickYMap.put("A003", "1");
					tickYMap.put("A004", "1");
					tickYMap.put("A005", "1");
					tickYMap.put("A006", "1");
					tickYMap.put("A007", "1");
					tickYMap.put("A008", "1");
					tickYMap.put("A009", "1");
					tickYMap.put("A012", "1");
					tickYMap.put("A013", "1");
					tickYMap.put("A014", "1");

					tickNMap.put("A010", "1");
					tickNMap.put("A011", "1");

					atLeastTickOneMap.put("A016", "2");
					atLeastTickOneMap.put("A017", "2");
					atLeastTickOneMap.put("A018", "2");
					atLeastTickOneMap.put("A019", "2");
					atLeastTickOneMap.put("A020", "2");
					atLeastTickOneMap.put("A021", "2");
					atLeastTickOneMap.put("A022", "2");
				}

				for (L260S01E ls260s01e : l260s01eList) {
					String chkResult = Util.trim(ls260s01e.getChkResult());
					String chkText = Util.trim(ls260s01e.getChkText());

					if (chkTickMap.containsKey(ls260s01e.getItemNo())) {
						if (Util.equals(chkResult, "") && !noCheckFlag) {
							String errmsg = pop.getProperty("L260S01E.title3")
									+ chkTickMap.get(ls260s01e.getItemNo())
									+ pop.getProperty("checkMsg06");

							temp.append(temp.length() > 0 ? "<br/>" : "");
							temp.append(errmsg);
							noCheckFlag = true;
						}
					}

					if (tickYMap.containsKey(ls260s01e.getItemNo())) {
						if (Util.equals(chkResult, UtilConstants.DEFAULT.是)
								&& Util.equals(chkText, "")) {
							String errmsg = pop.getProperty("L260S01E.title4")
									+ "：" + ls260s01e.getItemContent() + "，"
									+ pop.getProperty("L260S01E.error4");
							temp.append(temp.length() > 0 ? "<br/>" : "");
							temp.append(errmsg);
						}
					}

					if (tickNMap.containsKey(ls260s01e.getItemNo())) {
						if (Util.equals(chkResult, UtilConstants.DEFAULT.否)
								&& Util.equals(chkText, "")) {
							String errmsg = pop.getProperty("L260S01E.title4")
									+ "：" + ls260s01e.getItemContent() + "，"
									+ pop.getProperty("L260S01E.error4");
							temp.append(temp.length() > 0 ? "<br/>" : "");
							temp.append(errmsg);
						}
					}

				}

				for (L260S01E ls260s01e : l260s01eList) {
					String chkResult = ls260s01e.getChkResult();

					if (atLeastTickOneMap.containsKey(ls260s01e.getItemNo())) {
						itemSeqShow = atLeastTickOneMap.get(ls260s01e
								.getItemNo());
						if (Util.equals(chkResult, UtilConstants.DEFAULT.是)
								|| chkResult.contains("Y")) {
							atLeastTickOneFlag = true;
							break;
						}
					}
				}

				if (!atLeastTickOneFlag) {
					temp.append(temp.length() > 0 ? "<br/>" : "");
					temp.append(pop.getProperty("L260S01E.title3"));
					temp.append(itemSeqShow);
					temp.append(pop.getProperty("checkMsg07"));
				}

			}
		}
				
		// J-113-0035 為利ESG案件之貸後管控,
		// ELOAN國內外企金系統額度明細表其他敘做條件增加「應注意/承諾/待追蹤/ESG連結條款」的登錄機制
		if (haveESG) {
			List<L260S01F> l260s01fList = (List<L260S01F>) lms8000Service
					.findListByMainIdNotDel(L260S01F.class, l260m01d.getOid(),
							true);
			if (Util.isNotEmpty(l260s01fList) && l260s01fList.size() > 0) {
				int optionNotNACount = 0;
				for (L260S01F l260s01f : l260s01fList) {
					String chkResult = Util.trim(l260s01f.getChkResult());
					if (Util.isEmpty(chkResult)) {
						String errmsg = pop.getProperty("checkMsg09");
						temp.append(temp.length() > 0 ? "<br/>" : "");
						temp.append(errmsg);
						optionNotNACount++;
						break;
					}

					if (Util.notEquals(chkResult, "K")) {
						optionNotNACount++;
					}
				}

				if (optionNotNACount == 0) {
					String errmsg = pop.getProperty("checkMsg10");
					temp.append(temp.length() > 0 ? "<br/>" : "");
					temp.append(errmsg);
				}

			}
		}
				
		return temp.toString();
	}

	@SuppressWarnings("unchecked")
	public String checkSaveData(L260M01A l260m01a, boolean detail) {
		String msg = "";
		String mainId = l260m01a.getMainId();

		StringBuilder sb = new StringBuilder("");
		Boolean chkM01c = false;
		List<L260M01C> l260m01cList = (List<L260M01C>) lms8000Service
				.findListByMainIdNotDel(L260M01C.class, mainId, true);
		if (l260m01cList.isEmpty() || l260m01cList == null) {

		} else {
			for (L260M01C l260m01c : l260m01cList) {
				if (Util.equals("N", l260m01c.getCheckYN())) {
					chkM01c = true;
					break;
				}
				if (detail) {
					String errMsg2 = this.checkL260M01C(l260m01c, false);
					if (Util.isNotEmpty(errMsg2)) {
						sb.append(sb.length() > 0 ? "<br/>" : "");
						sb.append(pop.getProperty("L260M01C.title") + "："
								+ errMsg2);
						chkM01c = true;
						break;
					}
				}
			}
		}

		Boolean chkM01d = false;
		List<L260M01D> l260m01dList = (List<L260M01D>) lms8000Service
				.findListByMainIdNotDel(L260M01D.class, mainId, true);
		if (l260m01dList.isEmpty() || l260m01dList == null) {

		} else {
			for (L260M01D l260m01d : l260m01dList) {
				if (Util.equals("N", l260m01d.getCheckYN())) {
					chkM01d = true;
					break;
				}
				if (detail) {
					String errMsg2 = this.checkL260M01D(l260m01d, false);
					if (Util.isNotEmpty(errMsg2)) {
						sb.append(sb.length() > 0 ? "<br/>" : "");
						sb.append(pop.getProperty("L260M01D.title") + "："
								+ errMsg2);
						chkM01c = true;
						break;
					}
				}
			}
		}

		StringBuilder chkSB = new StringBuilder("");
		if (chkM01c) {
			chkSB.append(chkSB.length() > 0 ? "、" : "");
			chkSB.append(pop.getProperty("L260M01C.title"));
		}
		if (chkM01d) {
			chkSB.append(chkSB.length() > 0 ? "、" : "");
			chkSB.append(pop.getProperty("L260M01D.title"));
		}
		if (chkSB.length() > 0) {
			// checkMsg01=尚有{0}未通過檢核，請確認
			msg = MessageFormat.format(pop.getProperty("checkMsg01"),
					chkSB.toString());
		}
		if (sb.length() > 0) {
			if (msg.length() > 0) {
				msg += "<br/>";
			}
			msg += sb.toString();
		}

		return msg;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteL260m01a(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] oids = params.getStringArray("oids");
		if (oids.length > 0) {
			if (lms8000Service.deleteL260m01as(oids)) {
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
						.getMainMessage(UtilConstants.AJAX_RSP_MSG.刪除成功));
			}
		}
		return result;
	}

	/**
	 * J-112-0569 貸後管理已核准案件新增開放退回修改功能 編製完成，退回修改
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult rtnModifyL260m01a(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] oids = params.getStringArray("oids");
		String rtnModifyReason = Util.trim(params
				.getString("rtnModifyReasonStr"));
		if (oids.length > 0) {
			if (Util.isNotEmpty(lms8000Service.rtnModifyL260m01as(oids,
					rtnModifyReason))) {
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
						.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
			}
		}
		return result;
	}

	/*** 呈主管覆核(呈主管 主管覆核 拆2個method) */
	@SuppressWarnings({ "unchecked" })
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult flowAction(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		// 儲存and檢核
		String oid = params.getString(EloanConstants.MAIN_OID);
		L260M01A l260m01a = lms8000Service.findModelByOid(L260M01A.class, oid);
		// String[] formSelectBoss = params.getStringArray("selectBoss");

		if (params.containsKey("flowAction")) {
			if (params.getBoolean("flowAction")) {
				// 覆核時的檢查
			}
		}

		// if (!Util.isEmpty(formSelectBoss)) {
		if (params.containsKey("sendBoss") && params.getBoolean("sendBoss")) {
			// 呈主管時檢查資料
			if (Util.isNotEmpty(oid)) {
				if (l260m01a != null) {
					String errMsg1 = this.checkSaveData(l260m01a, true);										
					if (lms8000Service.cntrNoNotOK(l260m01a)) {
						// J-113-0159 已審核未動用額度可於貸後新增
						if (errMsg1.length() > 0) {
							errMsg1 += "<br/>";
						}
						errMsg1 += pop.getProperty("checkMsg08");
					}
					
					if (Util.isNotEmpty(errMsg1)) {
						throw new CapMessageException(errMsg1, getClass());
					}
				}
			}

			// String manager = Util.trim(params.getString("manager"));
			List<L260M01B> models = (List<L260M01B>) lms8000Service
					.findListByMainId(L260M01B.class, l260m01a.getMainId());
			if (!models.isEmpty()) {
				lms8000Service.deleteL260m01bs(models, false);
			}
			List<L260M01B> l260m01bs = new ArrayList<L260M01B>();
			// for (String people : formSelectBoss) {
			// L260M01B l260m01b = new L260M01B();
			// l260m01b.setCreator(user.getUserId());
			// l260m01b.setCreateTime(CapDate.getCurrentTimestamp());
			// l260m01b.setMainId(l260m01a.getMainId());
			// l260m01b.setBranchType(user.getUnitType());
			// l260m01b.setBranchId(user.getUnitNo());
			// // L1. 分行經辦 L3. 分行授信主管 L4. 分行覆核主管 L5. 經副襄理
			// l260m01b.setStaffJob(UtilConstants.STAFFJOB.授信主管L3);
			// l260m01b.setStaffNo(people);
			// l260m01bs.add(l260m01b);
			// }
			// L260M01B managerL260m01b = new L260M01B();
			// managerL260m01b.setCreator(user.getUserId());
			// managerL260m01b.setCreateTime(CapDate.getCurrentTimestamp());
			// managerL260m01b.setMainId(l260m01a.getMainId());
			// managerL260m01b.setStaffJob(UtilConstants.STAFFJOB.單位授權主管L5);
			// managerL260m01b.setStaffNo(manager);
			// managerL260m01b.setBranchType(user.getUnitType());
			// managerL260m01b.setBranchId(user.getUnitNo());
			// l260m01bs.add(managerL260m01b);
			L260M01B apprL260m01b = new L260M01B();
			apprL260m01b.setCreator(user.getUserId());
			apprL260m01b.setCreateTime(CapDate.getCurrentTimestamp());
			apprL260m01b.setMainId(l260m01a.getMainId());
			apprL260m01b.setStaffJob(UtilConstants.STAFFJOB.經辦L1);
			apprL260m01b.setStaffNo(user.getUserId());
			apprL260m01b.setBranchType(user.getUnitType());
			apprL260m01b.setBranchId(user.getUnitNo());
			l260m01bs.add(apprL260m01b);
			lms8000Service.saveL260m01bList(l260m01bs);
		}

		Boolean upMis = false;

		L260M01B l260m01bL4 = new L260M01B();
		// 如果有這個key值表示是輸入chekDate核准日期 = 核定作業
		if (params.containsKey("checkDate")) {
			l260m01a.setApprover(user.getUserId());
			l260m01a.setApproveTime(CapDate.getCurrentTimestamp());
			upMis = true;
			L260M01B l260m01b = lms8000Service.findL260m01b(
					l260m01a.getMainId(), user.getUnitType(), user.getUnitNo(),
					user.getUserId(), UtilConstants.STAFFJOB.執行覆核主管L4);
			if (l260m01b == null) {
				l260m01b = new L260M01B();
				l260m01b.setCreator(user.getUserId());
				l260m01b.setCreateTime(CapDate.getCurrentTimestamp());
				l260m01b.setMainId(l260m01a.getMainId());
				l260m01b.setStaffJob(UtilConstants.STAFFJOB.執行覆核主管L4);
				l260m01b.setStaffNo(user.getUserId());
				l260m01b.setBranchType(user.getUnitType());
				l260m01b.setBranchId(user.getUnitNo());
			}
			l260m01bL4 = l260m01b;
		}

		if (!Util.isEmpty(l260m01a)) {
			try {
				// 如果有這值表示非呈主管，要檢查覆核主管和文件最後更新者是否相同
				if (params.containsKey("flowAction")) {
					// 退回部檢查
					if (params.getBoolean("flowAction")) {
						L260M01B l260m01b = lms8000Service.findL260m01b(
								l260m01a.getMainId(), user.getUnitType(),
								user.getUnitNo(), user.getUserId(),
								UtilConstants.STAFFJOB.經辦L1);

						if (l260m01b != null) {
							// EFD0053=WARN|覆核人員不可與「經辦人員或其它覆核人員」為同一人|
							throw new CapMessageException(RespMsgHelper.getMessage("EFD0053"), getClass());
						} else {
							lms8000Service.save(l260m01bL4);
							upMis = true;

							// 刪除ID階層同筆資料 - 設定刪除時間
							if (l260m01a != null) {
								if (params.containsKey("M01cUnids")) {
									String[] M01cUnids = params
											.getStringArray("M01cUnids");
									List<Map<String, Object>> m01cList = lms8000Service
											.findDataById(
													"L260M01C",
													Util.trim(l260m01a
															.getOwnBrId()),
													Util.trim(l260m01a
															.getCustId()),
													Util.trim(l260m01a
															.getDupNo()),
													new String[] {
															CreditDocStatusEnum.海外_編製中
																	.getCode(),
															CreditDocStatusEnum.海外_待覆核
																	.getCode() });
									if (m01cList != null && !m01cList.isEmpty()) {
										List<L260M01C> l260m01cList = new ArrayList<L260M01C>();
										for (Map<String, Object> m01cMap : m01cList) {
											String m01aOid = Util
													.nullToSpace(MapUtils
															.getString(m01cMap,
																	"M01AOID"));
											String m01Oid = Util
													.nullToSpace(MapUtils
															.getString(m01cMap,
																	"M01OID"));
											String m01Unid = Util
													.nullToSpace(MapUtils
															.getString(m01cMap,
																	"UNID"));
											if (Util.isNotEmpty(m01aOid)
													&& Util.isNotEmpty(m01Oid)
													&& Util.isNotEmpty(m01Unid)) {
												if (Util.notEquals(m01aOid,
														Util.trim(l260m01a
																.getOid()))
														&& ArrayUtils.contains(
																M01cUnids,
																m01Unid)) {
													L260M01C l260m01c = lms8000Service
															.findModelByOid(
																	L260M01C.class,
																	m01Oid);
													if (l260m01c != null) {
														l260m01c.setDeletedTime(CapDate
																.getCurrentTimestamp());
														l260m01cList
																.add(l260m01c);
													}
												}
											}
										}
										if (!l260m01cList.isEmpty()) {
											lms8000Service
													.saveL260m01cList(l260m01cList);
										}
									}
								}
								if (params.containsKey("M01dUnids")) {
									String[] M01dUnids = params
											.getStringArray("M01dUnids");
									List<Map<String, Object>> m01dList = lms8000Service
											.findDataById(
													"L260M01D",
													Util.trim(l260m01a
															.getOwnBrId()),
													Util.trim(l260m01a
															.getCustId()),
													Util.trim(l260m01a
															.getDupNo()),
													new String[] {
															CreditDocStatusEnum.海外_編製中
																	.getCode(),
															CreditDocStatusEnum.海外_待覆核
																	.getCode() });
									if (m01dList != null && !m01dList.isEmpty()) {
										List<L260M01D> l260m01dList = new ArrayList<L260M01D>();
										for (Map<String, Object> m01dMap : m01dList) {
											String m01aOid = Util
													.nullToSpace(MapUtils
															.getString(m01dMap,
																	"M01AOID"));
											String m01Oid = Util
													.nullToSpace(MapUtils
															.getString(m01dMap,
																	"M01OID"));
											String m01Unid = Util
													.nullToSpace(MapUtils
															.getString(m01dMap,
																	"UNID"));
											if (Util.isNotEmpty(m01aOid)
													&& Util.isNotEmpty(m01Oid)
													&& Util.isNotEmpty(m01Unid)) {
												if (Util.notEquals(m01aOid,
														Util.trim(l260m01a
																.getOid()))
														&& ArrayUtils.contains(
																M01dUnids,
																m01Unid)) {
													L260M01D l260m01d = lms8000Service
															.findModelByOid(
																	L260M01D.class,
																	m01Oid);
													if (l260m01d != null) {
														l260m01d.setDeletedTime(CapDate
																.getCurrentTimestamp());
														l260m01dList
																.add(l260m01d);
													}
												}
											}
										}
										if (!l260m01dList.isEmpty()) {
											lms8000Service
													.saveL260m01dList(l260m01dList);
										}
									}
								}
							}
						}
					}
				}
				lms8000Service.flowAction(l260m01a.getOid(), l260m01a,
						params.containsKey("flowAction"),
						params.getAsBoolean("flowAction", false), upMis);
			} catch (FlowException t1) {
				logger.error(
						"[flowAction] lms7500Service.flowAction FlowException!!",
						t1);
				throw new CapMessageException(RespMsgHelper.getMessage(t1.getMessage()), getClass());
			} catch (Throwable t1) {
				logger.error(
						"[flowAction]  lms7500Service.flowAction EXCEPTION!!",
						t1);
				throw new CapMessageException(t1.getMessage(), getClass());
			}
		}

		return new CapAjaxFormResult();
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult addL260M01C(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String brNo = user.getUnitNo();
		boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchService
				.getBranch(brNo).getBrNoFlag());

		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		String byIdFlag = Util.trim(params.getString("byIdFlag", "N"));

		L260M01A l260m01a = lms8000Service.findModelByOid(L260M01A.class, oid);

		if (l260m01a != null) {
			L260M01C l260m01c = new L260M01C();
			l260m01c.setMainId(l260m01a.getMainId());
			l260m01c.setCreator(user.getUserId());
			l260m01c.setCreateTime(CapDate.getCurrentTimestamp());
			if (Util.equals(byIdFlag, "Y")) {
				l260m01c.setCntrNo("");
				l260m01c.setLoanNo("");
			} else {
				l260m01c.setCntrNo(Util.trim(l260m01a.getCntrNo()));
				l260m01c.setLoanNo(Util.trim(l260m01a.getLoanNo()));
			}
			l260m01c.setUnid("new");
			l260m01c.setLoanKind("LN");
			// followKind
			// followWay
			// followCycle
			// followBgnDate
			// followEndDate
			// nextFollowDate
			// staff
			l260m01c.setFollowContent("");
			// P-新增(用來判斷為user新增，上傳中心時轉為N)
			l260m01c.setStatus("P");
			l260m01c.setCheckYN("N");

			String fo = ""; // 帳務行員代號
			String ao = ""; // AO行員代號
			if (!isOverSea) {
				List<Map<String, Object>> lnf020List = misdbBASEService
						.findLnf020(Util.trim(l260m01a.getCntrNo()));
				if (lnf020List != null && !lnf020List.isEmpty()
						&& lnf020List.size() > 0) {
					fo = lnf020List.get(0).get("LNF020_FO_STAFFNO").toString();
					ao = lnf020List.get(0).get("LNF020_AO_STAFFNO").toString();
				}
			}
			l260m01c.setFo_staffNo(fo);
			l260m01c.setAo_staffNo(ao);

			lms8000Service.save(l260m01c);
			result.set("oidL260M01C", l260m01c.getOid());
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
					.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		} else {
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
					.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行有誤));
		}
		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult addL260M01D(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		String foDate = Util.trim(params.getString("foDate"));
		String byIdFlag = Util.trim(params.getString("byIdFlag", "N"));

		L260M01A l260m01a = lms8000Service.findModelByOid(L260M01A.class, oid);

		if (l260m01a != null) {
			L260M01D l260m01d = new L260M01D();
			l260m01d.setMainId(l260m01a.getMainId());
			l260m01d.setCreator(user.getUserId());
			l260m01d.setCreateTime(CapDate.getCurrentTimestamp());
			if (Util.equals(byIdFlag, "Y")) {
				l260m01d.setCntrNo("");
				l260m01d.setLoanNo("");
			} else {
				l260m01d.setCntrNo(Util.trim(l260m01a.getCntrNo()));
				l260m01d.setLoanNo(Util.trim(l260m01a.getLoanNo()));
			}
			l260m01d.setUnid("new");
			l260m01d.setLoanKind("LN");
			l260m01d.setFollowDate(CapDate.parseDate(foDate));
			l260m01d.setDataSrc("new");
			l260m01d.setFollowKind("");
			l260m01d.setFollowContent("");
			// chkDate
			// conformFg
			// followMemo
			// handlingStatus
			// repayUnusualFg
			// fileDesc
			// unusualDesc
			// isNotional
			// isAML
			l260m01d.setDataFrom("P"); // P:人工 B:中心Batch
			l260m01d.setHandlingStatus("");
			l260m01d.setCheckYN("N");
			lms8000Service.save(l260m01d);
			result.set("oidL260M01D", l260m01d.getOid());
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
					.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		} else {
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
					.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行有誤));
		}
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getPrintL140M01AParam(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString("oid"));
		String cntrNo = "";
		String L140M01A_oid = "";
		String L120M01A_isHeadCheck = "";
		String rptNo = "";
		String cntrCustid = "";
		String cntrDupno = "";

		if (Util.isNotEmpty(oid)) {
			L260M01A l260m01a = lms8000Service.findModelByOid(L260M01A.class,
					oid);

			if (l260m01a != null) {
				cntrNo = Util.trim(l260m01a.getCntrNo());
				Map<String, Object> r = lms8000Service
						.findLastPrint_L140M01A(cntrNo);
				if (r != null && !r.isEmpty()) {
					L140M01A_oid = Util.trim(r.get("OID"));
					L120M01A_isHeadCheck = Util.trim(r.get("ISHEADCHECK"));
					rptNo = Util.equals(L120M01A_isHeadCheck, "Y") ? "R13"
							: "R12";
				}
				cntrCustid = Util.trim(l260m01a.getCustId());
				cntrDupno = Util.trim(l260m01a.getDupNo());
			}
		}

		if (Util.isEmpty(oid) || Util.isEmpty(cntrNo)
				|| Util.isEmpty(L140M01A_oid)
				|| Util.isEmpty(L120M01A_isHeadCheck)) {
			result.set("notProc", "查無資料");// UtilConstants.AJAX_RSP_MSG.執行有誤)
		} else {
			result.set("parStr", rptNo + "^" + L140M01A_oid + "^" + cntrCustid
					+ "^" + cntrDupno + "^" + cntrNo);
		}
		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult queryAndAdd(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String openFrom = params.getString("openFrom");
		String form = params.getString("filterForm");
		JSONObject obj = JSONObject.fromObject(form);
		String custId = Util.trim(Util.nullToSpace(obj.get("custId")));
		String dupNo = Util.trim(Util.nullToSpace(obj.get("dupNo")));
		if (Util.isEmpty(dupNo)) {
			dupNo = "0";
		}
		String cntrNo = Util.trim(Util.nullToSpace(obj.get("cntrNo")));
		String loanNo = Util.trim(Util.nullToSpace(obj.get("loanNo")));
		String status = Util.trim(Util.nullToSpace(obj.get("status")));
		String startDate = Util.trim(Util.nullToSpace(obj.get("startDate")));
		String endDate = Util.trim(Util.nullToSpace(obj.get("endDate")));
		String[] handlingStatus = params.getStringArray("handlingStatus");
		String custName = "";
		String cntrNoBr = "";

		if (Util.equals(openFrom, "lrs")) {
			cntrNo = Util
					.trim(Util.nullToSpace(params.getString("cntrnoList")));
			loanNo = "";
		}
		if (Util.isEmpty(cntrNo)) {
			throw new CapMessageException("額度序號不得為空白！", getClass());
		}

		result.set("cntrNo", cntrNo);
		result.set("loanNo", loanNo);

		String brNo = user.getUnitNo();
		boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchService
				.getBranch(brNo).getBrNoFlag());

		boolean isRs = false; // 由覆審功能發動的查詢
		if (Util.equals(openFrom, "lrs") || Util.equals(openFrom, "crs")) {
			isRs = true;
		}

		if (!custId.isEmpty() && !dupNo.isEmpty()) {
			result.set("custId", custId);
			result.set("dupNo", dupNo);
			if (isOverSea) {
				Map<String, Object> custData = obsdbELF601Service
						.findByIdDupNo(brNo, custId, dupNo);
				if (custData != null && !custData.isEmpty()) {
					String newCustName = Util.trim(MapUtils.getString(custData,
							"ELFCUST_ENAME"));
					custName = Util.toSemiCharString(newCustName); // 轉半形
				}
			}
			if (Util.isEmpty(custName)) {
				Map<String, Object> latestData = customerSrv.findByIdDupNo(
						custId, dupNo);
				if (latestData != null && !latestData.isEmpty()) {
					String newCustName = Util.trim(MapUtils.getString(
							latestData, "CNAME"));
					custName = Util.toSemiCharString(newCustName); // 轉半形
				}
			}
			result.set("custName", custName);

			if (cntrNo.length() < 3) {
				throw new CapMessageException("額度序號有誤，請確認！", getClass());
			}

			if (Util.equals(openFrom, "lrs") || Util.equals(openFrom, "crs")) { // 覆審不限制，因為可能是中心在查分行
			} else {
				// 2021/10/01 金至忠襄理說不得跨分行 所以非該行額度序號不顯示
				if (Util.notEquals(cntrNo.substring(0, 3), user.getUnitNo())) {
					throw new CapMessageException("非本行額度序號不可查詢！", getClass());
				}
			}
			cntrNoBr = cntrNo.substring(0, 3);
			if (!isOverSea) {
				if (misdbBASEService.chkFilterData1(custId, dupNo, cntrNo)) {
					// custId, dupNo, cntrNo 存在 (組合正確)
					// 再檢查放款帳號
					if (Util.isNotEmpty(loanNo)) {
						if (misdbBASEService.chkFilterData2(cntrNo, loanNo)) {

						} else {
							// 不存在
							// checkMsg03=統一編號與額度序號/放款帳號 不匹配，請確認
							throw new CapMessageException(
									pop.getProperty("checkMsg03"), getClass());
						}
					}
				} else {
					// 不存在
					// checkMsg03=統一編號與額度序號/放款帳號 不匹配，請確認
					throw new CapMessageException(
							pop.getProperty("checkMsg03"), getClass());
				}
			} else {
				if (lms8000Service.chkFilterDataDW(brNo, custId, dupNo, cntrNo,
						loanNo)) {

				} else {
					// 不存在
					// checkMsg03_Ovs=統一編號與額度序號/動撥編號(REF) 不匹配，請確認
					throw new CapMessageException(
							pop.getProperty("checkMsg03_Ovs"), getClass());
				}
			}

			// if(misdbBASEService.chkFilterData(custId, dupNo, cntrNo,
			// loanNo)){
			// // custId, dupNo, cntrNo, loanNo 存在 (組合正確)
			// } else {
			// // 不存在
			// throw new CapMessageException("統一編號與額度序號/放款帳號 不匹配，請確認",
			// getClass());
			// }
		} else {
			throw new CapMessageException("ID不得為空白！", getClass());
		}

		// !!!!!!!!!!!!!!!!!!! 先去抓MIS資料 有資料再ADD LMS TABLE
		String l260m01aMainid = "";
		l260m01aMainid = IDGenerator.getUUID();

		String fo = ""; // 帳務行員代號
		String ao = ""; // AO行員代號
		List<ELF601> elf601s = null;
		List<ELF602> elf602s = null;
		List<ELF601> elf601sId = null;
		List<ELF602> elf602sId = null;
		if (isOverSea) {
			elf601s = obsdbELF601Service.getElf601ByFilter(custId, dupNo,
					cntrNoBr, cntrNo, loanNo, status);
			elf602s = obsdbELF601Service.getElf602ByFilter(custId, dupNo,
					cntrNoBr, cntrNo, loanNo, startDate, endDate,
					handlingStatus);
			elf601sId = obsdbELF601Service.getElf601OnlyIdByFilter(custId,
					dupNo, status, cntrNoBr);
			elf602sId = obsdbELF601Service.getElf602OnlyIdByFilter(custId,
					dupNo, startDate, endDate, handlingStatus, cntrNoBr);
		} else {
			List<Map<String, Object>> lnf020List = misdbBASEService
					.findLnf020(cntrNo);
			if (lnf020List != null && !lnf020List.isEmpty()
					&& lnf020List.size() > 0) {
				fo = lnf020List.get(0).get("LNF020_FO_STAFFNO").toString();
				ao = lnf020List.get(0).get("LNF020_AO_STAFFNO").toString();
			}

			elf601s = misdbBASEService.getElf601ByFilter(custId, dupNo, cntrNo,
					loanNo, status, cntrNoBr);
			elf602s = misdbBASEService.getElf602ByFilter(custId, dupNo, cntrNo,
					loanNo, startDate, endDate, handlingStatus, cntrNoBr);
			elf601sId = misdbBASEService.getElf601OnlyIdByFilter(custId, dupNo,
					status, cntrNoBr);
			elf602sId = misdbBASEService.getElf602OnlyIdByFilter(custId, dupNo,
					startDate, endDate, handlingStatus, cntrNoBr);
		}
		List<L260M01C> l260m01cList = new ArrayList<L260M01C>();
		if (elf601s != null && elf601s.size() > 0) {
			this.elf601ToL260M01C(l260m01aMainid, elf601s, l260m01cList, true,
					fo, ao);
		}
		// J-110-0363 By ID
		if (elf601sId != null && elf601sId.size() > 0) {
			this.elf601ToL260M01C(l260m01aMainid, elf601sId, l260m01cList,
					true, fo, ao);
		}
		lms8000Service.saveL260m01cList(l260m01cList);
		lms8000Service.saveL260m01cList(l260m01cList);

		List<L260M01D> l260m01dList = new ArrayList<L260M01D>();
		if (elf602s != null && elf602s.size() > 0) {
			this.elf602ToL260M01D(l260m01aMainid, elf602s, l260m01dList, true);
		}
		// J-110-0363 By ID
		if (elf602sId != null && elf602sId.size() > 0) {
			this.elf602ToL260M01D(l260m01aMainid, elf602sId, l260m01dList, true);
		}
		lms8000Service.saveL260m01dList(l260m01dList);

		// 處理附加檔案複製
		lms8000Service.copyDocFile(l260m01aMainid, true, isRs, null);

		// 處理理財商品
		// 海外沒有理財商品
		if (!isOverSea) {
			this.findDwFinProd(l260m01aMainid);
		}

		// J-110-0497 餘屋貸款
		// 處理餘屋貸款 海外沒有餘屋貸款
		if (!isOverSea) {
			this.findMis517Data(l260m01aMainid);
		}

		// J-112-0307
		// 於eloan貸後管理追蹤檢核表維護功能中，新增一鍵生成公司訪問紀錄表及借戶、負責人、所屬企業集團等關係戶於本行各項業務往來彙總表
		// 公司訪問記錄表，海外沒有
		if (!isOverSea) {
			lms8000Service.findVisitCompData(l260m01dList);
		}
		// J-113-0035 為利ESG案件之貸後管控,
		// ELOAN國內外企金系統額度明細表其他敘做條件增加「應注意/承諾/待追蹤/ESG連結條款」的登錄機制
		lms8000Service.findLastESGDataAndSave(l260m01dList);

		if (l260m01cList.size() <= 0 && l260m01dList.size() <= 0) {
			result.set("hasData", false);
		} else {
			L260M01A l260m01a = new L260M01A();
			l260m01a.setDocStatus(CreditDocStatusEnum.海外_呈總行.getCode());
			l260m01a.setOwnBrId(user.getUnitNo());
			l260m01a.setMainId(l260m01aMainid);
			String txCode = Util.trim(params
					.getString(EloanConstants.TRANSACTION_CODE));
			l260m01a.setTxCode(txCode);
			// l260m01a.setDocURL(LMS8000M01Page.class.getAnnotation(MountPath.class).path());
			l260m01a.setCntrNo(cntrNo);
			l260m01a.setCustId(custId);
			l260m01a.setDupNo(dupNo);
			l260m01a.setCustName(custName);
			l260m01a.setLoanNo(loanNo);
			l260m01a.setDeletedTime(CapDate.getCurrentTimestamp());
			l260m01a.setCreateTime(CapDate.getCurrentTimestamp());
			l260m01a.setCreator(user.getUserId());
			lms8000Service.saveL260m01aTemp(l260m01a);
			//
			// lms8000Service.save(l260m01a);

			result.set("hasData", true);
			result.set(EloanConstants.OID, l260m01a.getOid());
			result.set(EloanConstants.MAIN_ID, l260m01aMainid);
			result.set(EloanConstants.MAIN_OID, l260m01a.getOid());
			result.set(EloanConstants.MAIN_DOC_STATUS,
					CreditDocStatusEnum.海外_呈總行.getCode());
		}

		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult sendToEdit(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String custName = Util.trim(params.getString("custName"));
		String cntrNo = Util.trim(params.getString("cntrNo"));
		String loanNo = Util.trim(params.getString("loanNo"));

		L260M01A l260m01a = lms8000Service.findModelByOid(L260M01A.class, oid);
		L260M01A newL260m01a = new L260M01A();
		if (l260m01a != null) {
			newL260m01a.setMainId(l260m01a.getMainId());
			newL260m01a.setCntrNo(l260m01a.getCntrNo());
			newL260m01a.setCustId(l260m01a.getCustId());
			newL260m01a.setDupNo(l260m01a.getDupNo());
			newL260m01a.setCustName(l260m01a.getCustName());
			newL260m01a.setLoanNo(l260m01a.getLoanNo());
			// delete insert
			lms8000Service.deleteL260m01as(new String[] { oid });
		} else {
			newL260m01a.setMainId(mainId);
			newL260m01a.setCntrNo(cntrNo);
			newL260m01a.setCustId(custId);
			newL260m01a.setDupNo(dupNo);
			newL260m01a.setCustName(custName);
			newL260m01a.setLoanNo(loanNo);
		}
		// l260m01a = new L260M01A();
		newL260m01a.setDocStatus(CreditDocStatusEnum.海外_編製中.getCode());
		newL260m01a.setOwnBrId(user.getUnitNo());
		String txCode = Util.trim(params
				.getString(EloanConstants.TRANSACTION_CODE));
		newL260m01a.setTxCode(txCode);
		// UPGRADE: 待確認，URL是否正確
		newL260m01a.setDocURL(params.getString("docUrl"));

		lms8000Service.save(newL260m01a);

		result.set(EloanConstants.MAIN_ID, newL260m01a.getMainId());

		return result;
	}

	@SuppressWarnings({ "unchecked" })
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult cleanDeletedTime(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		CapAjaxFormResult result = new CapAjaxFormResult();
		// String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String oid = Util.trim(params.getString(EloanConstants.OID));

		L260M01A l260m01a = lms8000Service.findModelByOid(L260M01A.class, oid);
		String mainId = Util.trim(l260m01a.getMainId());

		List<L260M01C> l260m01cs = new ArrayList<L260M01C>();
		List<L260M01C> l260m01clist = (List<L260M01C>) lms8000Service
				.findListByMainId(L260M01C.class, mainId);
		if (l260m01clist != null && l260m01clist.size() > 0) {
			for (L260M01C l260m01c : l260m01clist) {
				l260m01c.setDeletedTime(null);
				l260m01c.setCreator(user.getUserId());
				l260m01c.setUpdater(null);
				l260m01cs.add(l260m01c);
			}
		}
		if (!l260m01cs.isEmpty()) {
			lms8000Service.saveL260m01cList(l260m01cs);
		}

		List<L260M01D> l260m01ds = new ArrayList<L260M01D>();
		List<L260M01D> l260m01dlist = (List<L260M01D>) lms8000Service
				.findListByMainId(L260M01D.class, mainId);
		if (l260m01dlist != null && l260m01dlist.size() > 0) {
			for (L260M01D l260m01d : l260m01dlist) {
				l260m01d.setDeletedTime(null);
				l260m01d.setCreator(user.getUserId());
				l260m01d.setUpdater(null);
				l260m01ds.add(l260m01d);
			}
		}
		if (!l260m01ds.isEmpty()) {
			lms8000Service.saveL260m01dList(l260m01ds);
		}

		return result;
	}

	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult chkL260M01ANotEnd(PageParameters params) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		CapAjaxFormResult result = new CapAjaxFormResult();
		boolean single = params.getBoolean("single");
		String docStatusAry[] = { CreditDocStatusEnum.海外_已核准.getCode(),
				CreditDocStatusEnum.已覆核案件退回紀錄文件狀態.getCode() };
		if (single) {
			String custId = Util.trim(params.getString("custId", ""));
			String dupNo = Util.trim(params.getString("dupNo", ""));
			String cntrNo = Util.trim(params.getString("cntrNo", ""));
			String loanNo = Util.trim(params.getString("loanNo", ""));
			L260M01A l260m01a = lms8000Service.findL260m01a_notEqualsDocstatus(
					custId, dupNo, cntrNo, loanNo, docStatusAry,
					user.getUnitNo());
			if (l260m01a == null) {

			} else {
				result.set("msg", "已存在");
			}
		} else {
			boolean has = false;
			String oid = Util.trim(params.getString(EloanConstants.OID));
			L260M01A l260m01a = lms8000Service.findModelByOid(L260M01A.class,
					oid);
			String mainId = "";
			String custId = "";
			String dupNo = "";
			String cntrNo = "";
			String loanNo = "";
			if (l260m01a != null) {
				mainId = Util.trim(l260m01a.getMainId());
				custId = Util.trim(l260m01a.getCustId());
				dupNo = Util.trim(l260m01a.getDupNo());
				cntrNo = Util.trim(l260m01a.getCntrNo());
				loanNo = Util.trim(l260m01a.getLoanNo());
				L260M01A temp = lms8000Service.findL260m01a_notEqualsDocstatus(
						custId, dupNo, cntrNo, loanNo, docStatusAry,
						user.getUnitNo());
				if (temp != null) {
					has = true;
					result.set("msg", "已存在");
				}
				if (!has) {
					List<L260M01C> l260m01clist = (List<L260M01C>) lms8000Service
							.findListByMainIdNotDel(L260M01C.class, mainId,
									true);
					if (l260m01clist != null && l260m01clist.size() > 0) {
						for (L260M01C l260m01c : l260m01clist) {
							if (has) {
								break;
							}
							cntrNo = l260m01c.getCntrNo();
							loanNo = l260m01c.getLoanNo();
							L260M01A tempC = lms8000Service.findL260m01a_notEqualsDocstatus(
									custId, dupNo, cntrNo, loanNo, docStatusAry,
									user.getUnitNo());
							if (tempC != null) {
								has = true;
								result.set("msg", "已存在");
							}
						}
					}
					if (!has) {
						List<L260M01D> l260m01dlist = (List<L260M01D>) lms8000Service
								.findListByMainIdNotDel(L260M01D.class, mainId,
										true);
						if (l260m01dlist != null && l260m01dlist.size() > 0) {
							for (L260M01D l260m01d : l260m01dlist) {
								if (has) {
									break;
								}
								cntrNo = l260m01d.getCntrNo();
								loanNo = l260m01d.getLoanNo();
								L260M01A tempD = lms8000Service.findL260m01a_notEqualsDocstatus(
										custId, dupNo, cntrNo, loanNo, docStatusAry,
										user.getUnitNo());
								if (tempD != null) {
									has = true;
									result.set("msg", "已存在");
								}
							}
						}
					}
				}
			}
		}

		return result;
	}

	public void elf601ToL260M01C(String mainId, List<ELF601> elf601s,
			List<L260M01C> l260m01cList, boolean setDelTime, String fo,
			String ao) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String brNo = user.getUnitNo();
		boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchService
				.getBranch(brNo).getBrNoFlag());
		
		for (ELF601 elf601 : elf601s) {
			// 控制檔抓未解除
			// setDelTime = true 查詢功能 - 都抓
			if (setDelTime || Util.notEquals(elf601.getElf601_status(), "C")) {
				L260M01C l260m01c = new L260M01C();
				l260m01c.setMainId(mainId);
				l260m01c.setUnid(Util.trim(elf601.getElf601_unid()));
				l260m01c.setCntrNo(Util.trim(elf601.getElf601_cntrno()));
				l260m01c.setLoanNo(Util.trim(elf601.getElf601_loan_no()));
				l260m01c.setLoanKind(Util.trim(elf601.getElf601_loan_kind()));

				l260m01c.setFollowKind(Util.trim(elf601.getElf601_fo_kind()));
				l260m01c.setFollowContent(Util.nullToSpace(elf601
						.getElf601_fo_content()));
				l260m01c.setFollowWay(Util.trim(elf601.getElf601_fo_way()));
				l260m01c.setFollowBgnDate(elf601.getElf601_fo_beg_date());
				l260m01c.setFollowCycle(elf601.getElf601_fo_cycle());
				l260m01c.setFollowEndDate(elf601.getElf601_fo_end_date());
				l260m01c.setStaff(Util.trim(elf601.getElf601_staff()));
				l260m01c.setFo_staffNo(fo);// Util.trim(elf601.getElf601_fo_staffNo()));
				l260m01c.setAo_staffNo(ao);// Util.trim(elf601.getElf601_ao_staffNo()));
				l260m01c.setNextFollowDate(elf601.getElf601_fo_next_date());
				l260m01c.setStatus(Util.trim(elf601.getElf601_status()));
				l260m01c.setStatusForShow(lms8000Service.getStatusForShow(
						Util.trim(elf601.getElf601_status()),
						elf601.getElf601_fo_next_date()));
				l260m01c.setCheckYN("");
				l260m01c.setCreator(user.getUserId());
				l260m01c.setCreateTime(CapDate.getCurrentTimestamp());
				l260m01c.setDeletedTime((setDelTime ? CapDate
						.getCurrentTimestamp() : null));
				l260m01c.setCaseMark(Util.nullToSpace(elf601
						.getElf601_case_mark()));
				
				// J-113-0035 為利ESG案件之貸後管控,
				// ELOAN國內外企金系統額度明細表其他敘做條件增加「應注意/承諾/待追蹤/ESG連結條款」的登錄機制
				l260m01c.setFrom601SUid(Util.nullToSpace(elf601
						.getElf601_suid()));
				l260m01c.setFrom601SApptime(elf601.getElf601_sapptime());
				l260m01c.setFrom601SSeqno(elf601
						.getElf601_sseqno());
				l260m01cList.add(l260m01c);
			}
		}
	}

	public void elf602ToL260M01D(String mainId, List<ELF602> elf602s,
			List<L260M01D> l260m01dList, boolean setDelTime) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String brNo = user.getUnitNo();
		boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchService
				.getBranch(brNo).getBrNoFlag());
		for (ELF602 elf602 : elf602s) {
			// 紀錄檔抓 1未辦理.2辦理中
			// setDelTime = true 查詢功能 - 都抓
			if (setDelTime
					|| (Util.equals(elf602.getElf602_status(), "1") || Util
							.equals(elf602.getElf602_status(), "2"))) {
				L260M01D l260m01d = new L260M01D();
				l260m01d.setMainId(mainId);
				l260m01d.setCntrNo(Util.trim(elf602.getElf602_cntrno()));
				l260m01d.setLoanNo(Util.trim(elf602.getElf602_loan_no()));
				l260m01d.setUnid(Util.trim(elf602.getElf602_unid()));
				l260m01d.setLoanKind(Util.trim(elf602.getElf602_loan_kind()));

				l260m01d.setFollowDate(elf602.getElf602_fo_date());
				l260m01d.setFollowKind(Util.trim(elf602.getElf602_fo_kind()));
				l260m01d.setFollowContent(Util.nullToSpace(elf602
						.getElf602_fo_content()));
				l260m01d.setFollowStaff(Util.nullToSpace(elf602
						.getElf602_staff()));
				l260m01d.setChkDate(elf602.getElf602_chkdate());
				l260m01d.setConformFg(Util.trim(elf602.getElf602_conform_fg()));
				l260m01d.setFollowMemo(Util.trim(elf602.getElf602_fo_memo()));
				l260m01d.setHandlingStatus(Util.trim(elf602.getElf602_status()));
				l260m01d.setDataSrc(Util.trim(elf602.getElf602_datasrc()));
				l260m01d.setRepayUnusualFg(Util.trim(elf602
						.getElf602_unusual_fg()));
				l260m01d.setFileDesc(Util.nullToSpace(elf602
						.getElf602_fileDesc()));
				l260m01d.setUnusualDesc(elf602.getElf602_unusualdesc());
				l260m01d.setIsNotional(elf602.getElf602_isnotional());
				l260m01d.setIsAML(elf602.getElf602_isaml());
				l260m01d.setDataFrom("B"); // P:人工 B:中心Batch
				l260m01d.setCheckYN("");
				l260m01d.setFieldMainId(elf602.getElf602_fieldMainId());
				// 查詢功能 因為列印要用 先抓 ELF602 傳送至維護 要改成使用的經辦
				l260m01d.setCreator((setDelTime ? elf602.getElf602_upd_teller()
						: user.getUserId()));
				l260m01d.setCreateTime(CapDate.getCurrentTimestamp());
				// 查詢功能 因為列印要用 先抓 ELF602 傳送至維護 要改成 null
				l260m01d.setUpdater((setDelTime ? elf602.getElf602_upd_supvno()
						: null));
				l260m01d.setDeletedTime((setDelTime ? CapDate
						.getCurrentTimestamp() : null));

				// 前次維護紀錄
				if (Util.isNotEmpty(Util.nullToSpace(elf602
						.getElf602_fieldMainId()))) {
					L260M01D exL260m01d = lms8000Service.findModelByOid(
							L260M01D.class,
							Util.nullToSpace(elf602.getElf602_fieldMainId()));
					if (exL260m01d != null) {
						l260m01d.setFinProdFg(exL260m01d.getFinProdFg());
					}
				}

				l260m01d.setCaseMark(Util.nullToSpace(elf602
						.getElf602_case_mark()));

				// J-113-0035 為利ESG案件之貸後管控,
				// ELOAN國內外企金系統額度明細表其他敘做條件增加「應注意/承諾/待追蹤/ESG連結條款」的登錄機制
				l260m01d.setFrom602SUid(Util.nullToSpace(elf602
						.getElf602_suid()));
				l260m01d.setFrom602SApptime(elf602.getElf602_sapptime());
				l260m01d.setFrom602SSeqno(elf602
						.getElf602_sseqno());
				l260m01d.setFrom602SESGsunre(Util.nullToSpace(elf602
						.getElf602_sesgsunre()));
				
				l260m01dList.add(l260m01d);
			}
		}
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult changeL260m01dYN(PageParameters params) {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		L260M01D l260m01d = lms8000Service.findModelByOid(L260M01D.class, oid);
		if (l260m01d != null) {
			l260m01d.setCheckYN("N");
			lms8000Service.save(l260m01d);
		}
		return result;
	}

	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult chkNextDt(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		Boolean showMsg = params.getAsBoolean("showMsg", false);
		if (showMsg) {
			// 按 "儲存" 鈕，先不檢查
		} else {
			L260M01A l260m01a = lms8000Service.findModelByOid(L260M01A.class,
					oid);
			if (l260m01a != null) {
				String mainId = Util.trim(l260m01a.getMainId());
				List<L260M01C> l260m01cList = (List<L260M01C>) lms8000Service
						.findListByMainIdNotDel(L260M01C.class, mainId, true);
				if (l260m01cList.isEmpty() || l260m01cList == null) {

				} else {
					HashMap<String, String> orgNextDateMap = new HashMap<String, String>();
					HashMap<String, String> nextDateMap = new HashMap<String, String>();
					HashMap<String, String> msgMap = new HashMap<String, String>();
					boolean overdue = false;
					for (L260M01C l260m01c : l260m01cList) {
						if (l260m01c.getDeletedTime() != null
								|| Util.equals(l260m01c.getStatus(), "C")) {
							continue; // 刪除的不檢查
						}
						if (Util.equals("N", l260m01c.getCheckYN())) {
							result = new CapAjaxFormResult();
							result.set(
									"msg",
									MessageFormat.format(
											pop.getProperty("checkMsg01"),
											pop.getProperty("L260M01C.title")));
							overdue = false;
							break;
						}
						if (Util.isNotEmpty(l260m01c.getCheckYN())) { // 有異動都要檢查
							Date nextFollowDate = l260m01c.getNextFollowDate();
							if (lms8000Service.isFutureDate(nextFollowDate)) {

							} else {
								String followWay = l260m01c.getFollowWay();
								if (Util.equals(followWay, "1")) {
									Calendar cal = Calendar.getInstance();
									cal.add(Calendar.DAY_OF_MONTH, 1);
									String bgDate = CapDate.formatDate(
											cal.getTime(), "yyyy-MM-dd");
									orgNextDateMap.put(l260m01c.getOid(),
											CapDate.formatDate(nextFollowDate,
													"yyyy-MM-dd"));
									nextDateMap.put(l260m01c.getOid(), bgDate);
									msgMap.put(
											l260m01c.getOid(),
											pop.getProperty("L260M01C.nextFollowDate")
													+ CapDate.formatDate(
															nextFollowDate,
															"yyyy-MM-dd")
													+ "　更改為　" + bgDate);
									overdue = true;
								} else if (Util.equals(followWay, "2")) {
									BigDecimal followCycle = l260m01c
											.getFollowCycle();
									String followBgnDate = CapDate.formatDate(
											l260m01c.getFollowBgnDate(),
											"yyyy-MM-dd");
									String followEndDate = CapDate.formatDate(
											l260m01c.getFollowEndDate(),
											"yyyy-MM-dd");
									Date nextDate = this.calNextDate(
											followCycle.intValue(),
											followBgnDate, followEndDate);
									if (nextDate == null) {
										throw new CapMessageException(
												UtilConstants.AJAX_RSP_MSG.執行有誤
														+ "，計算下次追蹤日有誤！",
												getClass());
									}
									orgNextDateMap.put(l260m01c.getOid(),
											CapDate.formatDate(nextFollowDate,
													"yyyy-MM-dd"));
									nextDateMap
											.put(l260m01c.getOid(), CapDate
													.formatDate(nextDate,
															"yyyy-MM-dd"));
									msgMap.put(
											l260m01c.getOid(),
											pop.getProperty("L260M01C.nextFollowDate")
													+ CapDate.formatDate(
															nextFollowDate,
															"yyyy-MM-dd")
													+ "　更改為　"
													+ CapDate.formatDate(
															nextDate,
															"yyyy-MM-dd"));
									overdue = true;
								}
							}
						}
					}
					if (overdue) {
						result.set("msg", pop.getProperty("checkDate4"));
						result.set("overdue", "Y");
						result.set("msgMap", new CapAjaxFormResult(msgMap));
						result.set("nextDateList", new CapAjaxFormResult(
								nextDateMap));
						result.set("orgNextDateList", new CapAjaxFormResult(
								orgNextDateMap));
					}
				}
			}
		}
		return result;
	}

	@SuppressWarnings("unused")
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult changeNextDt(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		// L260M01C.oid^nextDate|L260M01C.oid^nextDate
		String nextDateList = Util
				.nullToSpace(params.getString("nextDateList"));
		String[] dataSplit = nextDateList.split("\\|");
		String m01cOid = "";
		String nextDate = "";
		List<L260M01C> l260m01cList = new ArrayList<L260M01C>();
		for (String temp : dataSplit) {
			String[] tempSplits = temp.split("\\^");
			if (tempSplits.length < 1) {
				m01cOid = "";
			} else {
				m01cOid = tempSplits[0];
			}
			if (tempSplits.length < 2) {
				nextDate = "";
			} else {
				nextDate = tempSplits[1];
			}

			if (Util.isEmpty(m01cOid)) {
				continue;
			} else {
				L260M01C l260m01c = lms8000Service.findModelByOid(
						L260M01C.class, m01cOid);
				if (l260m01c != null) {
					if (Util.isNotEmpty(nextDate)) {
						l260m01c.setNextFollowDate(CapDate.getDate(nextDate,
								UtilConstants.DateFormat.YYYY_MM_DD));
						l260m01cList.add(l260m01c);
					}
				}
			}
		}
		if (!l260m01cList.isEmpty()) {
			lms8000Service.saveL260m01cList(l260m01cList);
		}

		Boolean showMsg = params.getAsBoolean("showMsg", false);
		if (showMsg) {
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
					.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		}

		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult importL260M01D(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String brNo = user.getUnitNo();
		boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchService
				.getBranch(brNo).getBrNoFlag());
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.nullToSpace(params.getString("mainId")); // L260M01A
																		// 的
																		// MAINID
		String[] oids = params.getStringArray("oids"); // ELF602 的 UNID
		List<L260M01D> l260m01dList = new ArrayList<L260M01D>();
		if (oids.length > 0) {
			for (String oid : oids) {
				// 如果是刪除再匯入 要先刪掉原本 HandlingStatus=4 的資料 再 insert
				L260M01D l260m01d = lms8000Service.findL260m01dByMainIdAndUnid(
						mainId, oid);
				if (l260m01d != null) {
					if (Util.equals(
							Util.nullToSpace(l260m01d.getHandlingStatus()), "4")) {
						List<L260M01D> oldM01ds = new ArrayList<L260M01D>();
						oldM01ds.add(l260m01d);
						lms8000Service.deleteL260m01ds(oldM01ds);
					}
				}
				List<ELF602> elf602s = new ArrayList<ELF602>();
				ELF602 elf602 = null;
				if (isOverSea) {
					elf602 = obsdbELF601Service.getElf602ByUnid(brNo, oid);
				} else {
					elf602 = misdbBASEService.getElf602ByUnid(oid);
				}
				if (elf602 != null && Util.isNotEmpty(elf602)) {
					elf602s.add(elf602);
					this.elf602ToL260M01D(mainId, elf602s, l260m01dList, false);
				}
			}
		}
		if (l260m01dList.size() > 0) {
			lms8000Service.saveL260m01dList(l260m01dList);
		}

		// 處理附加檔案複製
		lms8000Service.copyDocFile(mainId, false, false, l260m01dList);

		// J-110-0497 餘屋貸款
		// 處理餘屋貸款 海外沒有餘屋貸款
		if (!isOverSea) {
			this.findMis517Data(mainId);
		}

		// J-111-0025 實價登錄 海外沒有
		if (!isOverSea) {
			this.findRaspData(mainId, l260m01dList);
		}

		// J-112-0307
		// 於eloan貸後管理追蹤檢核表維護功能中，新增一鍵生成公司訪問紀錄表及借戶、負責人、所屬企業集團等關係戶於本行各項業務往來彙總表
		// 公司訪問記錄表，海外沒有
		if (!isOverSea) {
			lms8000Service.findVisitCompData(l260m01dList);
		}
		
		// J-113-0035 為利ESG案件之貸後管控,
		// ELOAN國內外企金系統額度明細表其他敘做條件增加「應注意/承諾/待追蹤/ESG連結條款」的登錄機制
		lms8000Service.findLastESGDataAndSave(l260m01dList);
		// result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
		// .getMainMessage(this.getComponent(),
		// UtilConstants.AJAX_RSP_MSG.執行成功));

		return result;
	}

	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult importFinProdData(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String[] keys = params.getStringArray("keys");
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID)); // L260M01D
																				// 的
																				// oid
		if (Util.isEmpty(mainId)) {
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0025"), getClass());
		}

		List<L260S01A> oldL260s01aList = (List<L260S01A>) lms8000Service
				.findListByMainId(L260S01A.class, mainId);
		if (!oldL260s01aList.isEmpty()) {
			lms8000Service.deleteL260s01aList(oldL260s01aList);
		}
		List<L260S01A> l260s01aList = new ArrayList<L260S01A>();
		List<L260S01A> delL260s01aList = new ArrayList<L260S01A>();
		boolean hasSell = false;
		if (keys.length > 0) {
			for (String keyStr : keys) {
				if (Util.isEmpty(keyStr)) {
					continue;
				}
				String[] keySplits = keyStr.split("\\^");
				// DW key: CUST_ID^CUST_DUP_NO^PRO_TYPE^BANK_PRO_CODE^ACC_NO
				String custId = null;
				String dupNo = null;
				String proType = null;
				String bankProCode = null;
				String accNo = null;
				custId = (keySplits.length < 1 ? "" : keySplits[0]);
				dupNo = (keySplits.length < 2 ? "" : keySplits[1]);
				proType = (keySplits.length < 3 ? "" : keySplits[2]);
				bankProCode = (keySplits.length < 4 ? "" : keySplits[3]);
				accNo = (keySplits.length < 5 ? "" : keySplits[4]);

				// L260S01A delL260s01a =
				// lms8000Service.findL260s01aByUniqueKey(mainId, custId, dupNo,
				// proType, bankProCode, accNo);
				// if (delL260s01a != null) {
				// delL260s01aList.add(delL260s01a);
				// }

				List<Map<String, Object>> dwData = lms8000Service
						.findOTS_DW_LNWM_MNT_ByKey(custId, dupNo, proType,
								bankProCode, accNo);
				for (Map<String, Object> map : dwData) {
					L260S01A l260s01a = new L260S01A();
					this.dwDataToL260S01A(map, l260s01a);
					l260s01a.setMainId(mainId);
					l260s01a.setCreator(user.getUserId());
					l260s01a.setCreateTime(CapDate.getCurrentTimestamp());
					l260s01aList.add(l260s01a);

					String lstSellDt = Util.trim(MapUtils.getString(map,
							"LST_SELL_DT", ""));
					if (Util.notEquals(lstSellDt, "")) {
						if (Util.notEquals(lstSellDt, "0001-01-01")) {
							hasSell = true;
						}
					}
				}
			}
			if (delL260s01aList.size() > 0) {
				lms8000Service.deleteL260s01aList(delL260s01aList);
			}
			if (l260s01aList.size() > 0) {
				lms8000Service.saveL260s01aList(l260s01aList);
			}
		}
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set("msg", hasSell ? pop.getProperty("alertMsg") : "");
		return result;
	}

	public void dwDataToL260S01A(Map<String, Object> map, L260S01A l260s01a) {
		if (map != null && !map.isEmpty()) {
			String custId = Util.trim(MapUtils.getString(map, "CUST_ID", ""));
			String dupNo = Util
					.trim(MapUtils.getString(map, "CUST_DUP_NO", ""));
			String custName = Util
					.trim(MapUtils.getString(map, "CUST_CNM", ""));
			String proType = Util.trim(MapUtils.getString(map, "PRO_TYPE", ""));
			String bankProCode = Util.trim(MapUtils.getString(map,
					"BANK_PRO_CODE", ""));
			String bankProName = Util.trim(MapUtils.getString(map,
					"BANK_PRO_NAME", ""));
			String accNo = Util.trim(MapUtils.getString(map, "ACC_NO", ""));
			String lstBuyDt = Util.trim(MapUtils.getString(map, "LST_BUY_DT",
					""));
			String lstBuyCurCd = Util.trim(MapUtils.getString(map,
					"LST_BUY_CUR_CD", ""));
			String lstBuyAmt = Util.trim(MapUtils.getString(map, "LST_BUY_AMT",
					""));
			String lstBuyBrCd = Util.trim(MapUtils.getString(map,
					"LST_BUY_BR_CD", ""));
			String lstSellDt = Util.trim(MapUtils.getString(map, "LST_SELL_DT",
					""));
			String lstSellCurCd = Util.trim(MapUtils.getString(map,
					"LST_SELL_CUR_CD", ""));
			String lstSellAmt = Util.trim(MapUtils.getString(map,
					"LST_SELL_AMT", ""));
			String lstSellBrCd = Util.trim(MapUtils.getString(map,
					"LST_SELL_BR_CD", ""));
			String tranType = Util.trim(MapUtils
					.getString(map, "TRAN_TYPE", ""));
			String invAmt = Util.trim(MapUtils.getString(map, "INV_AMT", ""));
			String dataDt = Util.trim(MapUtils.getString(map, "DATA_DT", ""));

			l260s01a.setCustId(custId);
			l260s01a.setDupNo(dupNo);
			l260s01a.setCustName(custName);
			l260s01a.setProType(proType);
			l260s01a.setBankProCode(bankProCode);
			l260s01a.setBankProName(bankProName);
			l260s01a.setAccNo(accNo);
			if (Util.notEquals(lstBuyDt, "")) {
				if (Util.equals(lstBuyDt, "0001-01-01")) {
					l260s01a.setLstBuyDt(CapDate.parseDate(CapDate.ZERO_DATE));
				} else {
					lstBuyDt = CapDate.formatDate(Util.parseDate(lstBuyDt),
							"yyyy-MM-dd");
					l260s01a.setLstBuyDt(CapDate.parseDate(lstBuyDt));
				}
			} else {
				l260s01a.setLstBuyDt(CapDate.parseDate(CapDate.ZERO_DATE));
			}
			l260s01a.setLstBuyCurCd(lstBuyCurCd);
			l260s01a.setLstBuyAmt(Util.equals(lstBuyAmt, "") ? BigDecimal.ZERO
					: new BigDecimal(lstBuyAmt));
			l260s01a.setLstBuyBrCd(lstBuyBrCd);
			if (Util.notEquals(lstSellDt, "")) {
				if (Util.equals(lstSellDt, "0001-01-01")) {
					l260s01a.setLstSellDt(CapDate.parseDate(CapDate.ZERO_DATE));
				} else {
					lstSellDt = CapDate.formatDate(Util.parseDate(lstSellDt),
							"yyyy-MM-dd");
					l260s01a.setLstSellDt(CapDate.parseDate(lstSellDt));
				}
			} else {
				l260s01a.setLstSellDt(CapDate.parseDate(CapDate.ZERO_DATE));
			}
			l260s01a.setLstSellCurCd(lstSellCurCd);
			l260s01a.setLstSellAmt(Util.equals(lstSellAmt, "") ? BigDecimal.ZERO
					: new BigDecimal(lstSellAmt));
			l260s01a.setLstSellBrCd(lstSellBrCd);
			l260s01a.setTranType(tranType);
			l260s01a.setInvAmt(Util.equals(invAmt, "") ? BigDecimal.ZERO
					: new BigDecimal(invAmt));
			if (Util.notEquals(dataDt, "")) {
				dataDt = CapDate.formatDate(Util.parseDate(dataDt),
						"yyyy-MM-dd");
				l260s01a.setDataDt(CapDate.parseDate(dataDt));
			} else {
				l260s01a.setDataDt(CapDate.getDate(CapDate
						.getCurrentDate(UtilConstants.DateFormat.YYYY_MM_DD),
						UtilConstants.DateFormat.YYYY_MM_DD));
			}
		}
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL260S01ADetail(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		Map<String, CapAjaxFormResult> codeMap = codetypeservice
				.findByCodeType(new String[] { "postLoan_proType",
						"postLoan_tranType" });

		L260S01A l260s01a = lms8000Service.findModelByOid(L260S01A.class, oid);
		if (l260s01a == null) {
			l260s01a = new L260S01A();
		}

		result = DataParse.toResult(l260s01a, DataParse.Delete, new String[] {
				EloanConstants.OID, EloanConstants.MAIN_ID, "custId", "dupNo",
				"custName", "creator", "createTime", "deletedTime" });
		result.set(
				"custInfo",
				Util.nullToSpace(l260s01a.getCustId()) + "　"
						+ Util.nullToSpace(l260s01a.getDupNo()) + "　"
						+ Util.nullToSpace(l260s01a.getCustName()));
		result.set("proType", Util.nullToSpace(l260s01a.getProType()));
		result.set(
				"proTypeStr",
				Util.nullToSpace(codeMap.get("postLoan_proType").get(
						l260s01a.getProType())));
		result.set("lstBuyBrCdName",
				branchService.getBranchName(l260s01a.getLstBuyBrCd()));
		if (CrsUtil.isNull_or_ZeroDate(l260s01a.getLstBuyDt())) {
			result.set("lstBuyDt", "");
		} else {
			result.set("lstBuyDt",
					Util.nullToSpace(TWNDate.toAD(l260s01a.getLstBuyDt())));
		}
		result.set("lstBuyAmt", NumConverter.addComma(l260s01a.getLstBuyAmt()));
		result.set("lstSellBrCdName",
				branchService.getBranchName(l260s01a.getLstSellBrCd()));
		if (CrsUtil.isNull_or_ZeroDate(l260s01a.getLstSellDt())) {
			result.set("lstSellDt", "");
		} else {
			result.set("lstSellDt",
					Util.nullToSpace(TWNDate.toAD(l260s01a.getLstSellDt())));
		}
		result.set("lstSellAmt",
				NumConverter.addComma(l260s01a.getLstSellAmt()));
		result.set("invAmt", NumConverter.addComma(l260s01a.getInvAmt()));
		result.set("tranType", Util.nullToSpace(l260s01a.getTranType()));
		result.set(
				"tranTypeStr",
				Util.nullToSpace(codeMap.get("postLoan_tranType").get(
						l260s01a.getTranType())));

		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteL260s01a(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] oids = params.getStringArray("oids");
		if (oids.length > 0) {
			if (lms8000Service.deleteL260s01as(oids)) {
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
						.getMainMessage(UtilConstants.AJAX_RSP_MSG.刪除成功));
			}
		}
		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult getLstFinProd(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] oids = params.getStringArray("oids");
		List<L260S01A> l260s01aList = new ArrayList<L260S01A>();
		if (oids.length > 0) {
			for (String oid : oids) {
				L260S01A l260s01a = lms8000Service.findModelByOid(
						L260S01A.class, oid);
				if (l260s01a != null) {
					String custId = Util.nullToSpace(l260s01a.getCustId());
					String custDupNo = Util.nullToSpace(l260s01a.getDupNo());
					String proType = Util.nullToSpace(l260s01a.getProType());
					String accNo = Util.nullToSpace(l260s01a.getAccNo());
					String bankProCode = Util.nullToSpace(l260s01a
							.getBankProCode());
					List<Map<String, Object>> dwData = lms8000Service
							.findOTS_DW_LNWM_MNT_ByKey(custId, custDupNo,
									proType, bankProCode, accNo);
					for (Map<String, Object> map : dwData) {
						this.dwDataToL260S01A(map, l260s01a);
						l260s01aList.add(l260s01a);
					}
				}
			}
			if (l260s01aList.size() > 0) {
				lms8000Service.saveL260s01aList(l260s01aList);
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
						.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
			}
		}
		return result;
	}

	@SuppressWarnings("unchecked")
	public void findDwFinProd(String l260m01aMainid) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		List<L260S01A> l260s01aList = new ArrayList<L260S01A>();
		List<L260M01D> m01dList = (List<L260M01D>) lms8000Service
				.findListByMainId(L260M01D.class, l260m01aMainid);
		if (m01dList != null && m01dList.size() > 0) {
			for (L260M01D l260m01d : m01dList) {
				String unid = "";
				if (Util.equals(Util.trim(l260m01d.getUnid()), "new")) {
					unid = l260m01d.getOid();
				} else {
					unid = Util.trim(l260m01d.getUnid());
				}

				List<Map<String, Object>> dwList = lms8000Service
						.findOTS_DW_LNWM_CFM_ByUnid(unid);
				if (dwList != null && dwList.size() > 0) {
					for (Map<String, Object> map : dwList) {
						L260S01A l260s01a = new L260S01A();
						this.dwDataToL260S01A(map, l260s01a);
						l260s01a.setMainId(l260m01d.getOid());
						l260s01a.setCreator(user.getUserId());
						l260s01a.setCreateTime(CapDate.getCurrentTimestamp());
						l260s01aList.add(l260s01a);
					}
				}
			}
		}
		if (l260s01aList.size() > 0) {
			lms8000Service.saveL260s01aList(l260s01aList);
		}
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult impL260M01C(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String brNo = user.getUnitNo();
		boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchService
				.getBranch(brNo).getBrNoFlag());

		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));

		L260M01A l260m01a = lms8000Service.findModelByOid(L260M01A.class, oid);

		if (l260m01a != null) {
			String cntrNo = Util.trim(l260m01a.getCntrNo());
			Map<String, Object> r = lms8000Service
					.findLastPrint_L140M01A(cntrNo); // 已核准； 非不變、取消；
			String L140M01A_oid = "";
			if (r != null && !r.isEmpty()) {
				L140M01A_oid = Util.trim(r.get("OID"));
				List<Map<String, Object>> list = lms8000Service
						.findL140S09BtoPostLoanByL140M01A(L140M01A_oid);
				if (list != null && list.size() > 0) {
					List<L260M01C> l260m01cList = new ArrayList<L260M01C>();
					String fo = ""; // 帳務行員代號
					String ao = ""; // AO行員代號
					if (!isOverSea) {
						List<Map<String, Object>> lnf020List = misdbBASEService
								.findLnf020(Util.trim(l260m01a.getCntrNo()));
						if (lnf020List != null && !lnf020List.isEmpty()
								&& lnf020List.size() > 0) {
							fo = lnf020List.get(0).get("LNF020_FO_STAFFNO")
									.toString();
							ao = lnf020List.get(0).get("LNF020_AO_STAFFNO")
									.toString();
						}
					}
					for (Map<String, Object> map : list) {
						String cont = Util.nullToSpace(map.get("CONT"));
						cont = Util.toSemiCharString(cont); // 轉半形
						try {
							cont = lms8000Service.cutStrWithUTF8(cont, 600); // 因為前端限制200個字
						} catch (UnsupportedEncodingException e) {
							e.printStackTrace();
							result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
									RespMsgHelper.getMessage(
											UtilConstants.AJAX_RSP_MSG.執行有誤,
											"[String.getBytes]cutStrWithUTF8"));
						}

						L260M01C l260m01c = new L260M01C();
						l260m01c.setMainId(l260m01a.getMainId());
						l260m01c.setCreator(user.getUserId());
						l260m01c.setCreateTime(CapDate.getCurrentTimestamp());
						l260m01c.setCntrNo(Util.trim(l260m01a.getCntrNo()));
						l260m01c.setLoanNo(Util.trim(l260m01a.getLoanNo()));
						l260m01c.setUnid("new");
						l260m01c.setLoanKind("LN");
						l260m01c.setFollowContent(cont);
						// P-新增(用來判斷為user新增，上傳中心時轉為N)
						l260m01c.setStatus("P");
						l260m01c.setCheckYN("N");
						l260m01c.setFo_staffNo(fo);
						l260m01c.setAo_staffNo(ao);
						l260m01cList.add(l260m01c);
					}
					if (l260m01cList != null && l260m01cList.size() > 0) {
						lms8000Service.saveL260m01cList(l260m01cList);
					}
				}
			}

			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
					.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		} else {
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
					.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行有誤));
		}
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getConfirmMsg(PageParameters params)
			throws CapException {
		StringBuffer confirmMsg = new StringBuffer("");
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		L260M01A l260m01a = lms8000Service.findModelByOid(L260M01A.class, oid);
		String mainId = "";
		boolean hasM01cId = false;
		boolean hasM01dId = false;
		List<String> M01cUnid = new ArrayList<String>();
		List<String> M01dUnid = new ArrayList<String>();
		if (l260m01a != null) {
			mainId = Util.nullToSpace(l260m01a.getMainId());
			List<L260M01C> l260m01clist = (List<L260M01C>) lms8000Service
					.findListByMainIdNotDel(L260M01C.class, mainId, true);
			if (l260m01clist != null && l260m01clist.size() > 0) {
				for (L260M01C l260m01c : l260m01clist) {
					if (l260m01c.getDeletedTime() != null) {
						continue;
					}
					if (Util.notEquals(Util.nullToSpace(l260m01c.getCheckYN()),
							"Y")) {
						continue; // 因為有修改的才會更新MIS
					}
					if (Util.equals(Util.nullToSpace(l260m01c.getUnid()), "new")) {
						continue; // 本次人工新增也無須檢核
					}
					if (Util.isEmpty(Util.trim(l260m01c.getCntrNo()))
							&& Util.isEmpty(Util.trim(l260m01c.getLoanNo()))) {
						hasM01cId = true;
						M01cUnid.add(Util.nullToSpace(l260m01c.getUnid()));
					}
				}
			}
			List<L260M01D> l260m01dlist = (List<L260M01D>) lms8000Service
					.findListByMainIdNotDel(L260M01D.class, mainId, true);
			if (l260m01dlist != null && l260m01dlist.size() > 0) {
				for (L260M01D l260m01d : l260m01dlist) {
					if (l260m01d.getDeletedTime() != null) {
						continue;
					}
					if (Util.notEquals(Util.nullToSpace(l260m01d.getCheckYN()),
							"Y")) {
						continue;
					}
					if (Util.equals(Util.nullToSpace(l260m01d.getUnid()), "new")) {
						continue; // 本次人工新增也無須檢核
					}
					if (Util.isEmpty(Util.trim(l260m01d.getCntrNo()))
							&& Util.isEmpty(Util.trim(l260m01d.getLoanNo()))) {
						hasM01dId = true;
						M01dUnid.add(Util.nullToSpace(l260m01d.getUnid()));
					}
				}
			}
		}
		if (hasM01cId || hasM01dId) {
			confirmMsg.append("本次異動含有ID階層資料，此客戶其餘在途維護案之同筆資料將予以刪除。");
		}
		if (hasM01cId) {
			result.set("M01cUnids", M01cUnid);
		}
		if (hasM01dId) {
			result.set("M01dUnids", M01dUnid);
		}

		if (Util.notEquals(confirmMsg.toString(), "")) {
			result.set("msg", confirmMsg.toString());
		}

		return result;
	}

	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult chkDatePeriod(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String bgnDate = Util.trim(params.getString("bgnDate"));
		String endDate = Util.trim(params.getString("endDate"));
		String txnCode = Util.trim(params.getString("txnCode"));

		// 先檢查 ODS 狀態可不可以用
		String[] chkOdsArr = lms8000Service.getODS_Status();
		String chkOdsStatus = chkOdsArr[0];
		String chkOdsMsg = chkOdsArr[1];
		if (Util.notEquals(chkOdsStatus, "Y")) { // ODS 不能用
			throw new CapMessageException(pop.getProperty("odsMsg" + chkOdsMsg,
					UtilConstants.AJAX_RSP_MSG.執行有誤)
					+ "，"
					+ pop.getProperty("odsMsgToBtt"), getClass());
		}

		String[] chkArr = lms8000Service.checkDatePeriod(bgnDate, endDate,
				true, txnCode);
		String chkFlag = chkArr[0];
		String chkMsg = chkArr[1];
		if (Util.equals(chkFlag, "Y")) {
			/*
			 * result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
			 * .getMainMessage(this.getComponent(),
			 * UtilConstants.AJAX_RSP_MSG.執行成功));
			 */
		} else {
			String msg = "";
			if (Util.equals(chkMsg, "4")) {
				msg = MessageFormat.format(pop.getProperty("dateMsg4"),
						chkArr[2]);
			} else {
				msg = pop.getProperty("dateMsg" + chkMsg,
						UtilConstants.AJAX_RSP_MSG.執行有誤);
			}
			result.set("msg", msg);
		}

		return result;
	}

	/**
	 * J-110-0497 餘屋貸款
	 */
	@SuppressWarnings("unchecked")
	public void findMis517Data(String l260m01aMainid) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		List<L260S01B> l260s01bList = new ArrayList<L260S01B>();
		List<L260M01D> m01dList = (List<L260M01D>) lms8000Service
				.findListByMainId(L260M01D.class, l260m01aMainid);
		if (m01dList != null && m01dList.size() > 0) {
			for (L260M01D l260m01d : m01dList) {
				String cntrNo = Util.nullToSpace(l260m01d.getCntrNo());
				String mainId = Util.nullToSpace(l260m01d.getOid());
				// if (Util.isNotEmpty(cntrNo) &&
				// Util.equals(Util.nullToSpace(l260m01d.getCaseMark()), "03"))
				// {
				if (lms8000Service.isCaseMark(l260m01d, "03")) {
					Map<String, Object> elf517DataMap = misElf517Service
							.getByCntrNo(cntrNo);
					String buildName = "";
					Integer begForSell = 0;
					Integer soldNumber = 0;
					String proStatus = "";
					String behindDesc = "";
					String isSameCase = "";
					if (elf517DataMap != null && !elf517DataMap.isEmpty()) {
						buildName = Util.nullToSpace(MapUtils.getString(
								elf517DataMap, "ELF517_BUILD_NAME"));
						begForSell = MapUtils.getInteger(elf517DataMap,
								"ELF517_BEG_FORSELL", 0);
						soldNumber = MapUtils.getInteger(elf517DataMap,
								"ELF517_SOLD_NUMBER", 0);
						proStatus = Util.nullToSpace(MapUtils.getString(
								elf517DataMap, "ELF517_PRO_STATUS"));
						behindDesc = Util.nullToSpace(MapUtils.getString(
								elf517DataMap, "ELF517_BEHIND_DESC"));
						isSameCase = Util.nullToSpace(MapUtils.getString(
								elf517DataMap, "ELF517_ISSAME_CASE"));
					}
					L260S01B l260s01b = new L260S01B();
					l260s01b.setMainId(mainId); // L260M01D 的 oid
					l260s01b.setCntrNo(cntrNo);
					l260s01b.setCreator(user.getUserId());
					l260s01b.setCreateTime(CapDate.getCurrentTimestamp());
					l260s01b.setBuildName(buildName);
					l260s01b.setBegForSell(begForSell);
					l260s01b.setSoldNumber(soldNumber);
					l260s01b.setProStatus(proStatus);
					l260s01b.setBehindDesc(behindDesc);
					l260s01b.setIsSameCase(isSameCase);
					// begForSell >0 則 不能修改N
					l260s01b.setCheckBeg(begForSell > 0 ? "N" : "Y");
					l260s01bList.add(l260s01b);
				}
			}
		}
		if (l260s01bList.size() > 0) {
			lms8000Service.saveL260s01bList(l260s01bList);
		}
	}

	/**
	 * J-110-0497 餘屋貸款
	 */
	private void setL260S01B(CapAjaxFormResult result, L260M01D l260m01d) {
		result.set("buildName", "");
		result.set("begForSell", "");
		result.set("soldNumber", "");
		result.set("proStatus", "");
		result.set("behindDesc", "");
		result.set("isSameCase", "");
		result.set("checkBeg", "");
		if (l260m01d != null) {
			// 理應只有一筆
			List<L260S01B> l260s01bList = (List<L260S01B>) lms8000Service
					.findListByMainIdNotDel(L260S01B.class, l260m01d.getOid(),
							true);
			if (l260s01bList != null && l260s01bList.size() > 0) {
				L260S01B l260s01b = l260s01bList.get(0);
				if (l260s01b != null) {
					result.set("buildName",
							CapString.trimNull(l260s01b.getBuildName()));
					result.set(
							"begForSell",
							(l260s01b.getBegForSell() == null ? "0" : Integer
									.toString(l260s01b.getBegForSell())));
					result.set(
							"soldNumber",
							(l260s01b.getSoldNumber() == null ? "0" : Integer
									.toString(l260s01b.getSoldNumber())));
					result.set("proStatus",
							CapString.trimNull(l260s01b.getProStatus()));
					result.set("behindDesc",
							CapString.trimNull(l260s01b.getBehindDesc()));
					result.set("isSameCase",
							CapString.trimNull(l260s01b.getIsSameCase()));
					result.set("checkBeg",
							CapString.trimNull(l260s01b.getCheckBeg()));
				}
			}
		}
	}

	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getC100s02aUrl(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String c100s02aOid = Util.trim(params.getString("c100s02aOid"));

		String filePath = lms8000Service.findC100s02aUrlByOid(c100s02aOid);
		if (Util.isNotEmpty(filePath)) {
			result.set("filePath", filePath);
		}

		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getC100s02aUrlToAttach(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		boolean done = false;
		String oidL260M01D = Util.trim(params.getString("oidL260M01D", ""));
		String fileMainId = ""; // L260M01D 的 mainId = L260M01A 的 MAINID
		String filePid = ""; // L260M01D 的 oid
		String fileCrYear = CapDate.getCurrentDate("yyyy-MM");
		if (Util.isEmpty(oidL260M01D)) {
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤, "找不到此筆追蹤紀錄"), getClass());
		} else {
			L260M01D l260m01d = lms8000Service.findModelByOid(L260M01D.class,
					oidL260M01D);
			if (l260m01d != null) {
				fileMainId = l260m01d.getMainId();
				filePid = oidL260M01D;
			} else {
				throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, "找不到此筆追蹤紀錄資料"),
						getClass());
			}
		}

		String rowObj = Util.trim(params.getString("rowObj", ""));
		String[] dataSplit = rowObj.split("\\^");
		if (dataSplit != null && dataSplit.length > 0) {
			for (String rowData : dataSplit) {
				String[] rowObjArray = rowData.split(",");
				String c100s02aOid = null;
				String fileDesc = null;
				c100s02aOid = ((rowObjArray.length < 1) ? "" : rowObjArray[0]);
				fileDesc = ((rowObjArray.length < 2) ? "" : rowObjArray[1]);

				InputStream in = null;
				OutputStream outputStream = null;
				ByteArrayOutputStream baos = null;
				String filePath = lms8000Service
						.findC100s02aUrlByOid(c100s02aOid);
				if (Util.isNotEmpty(filePath)) {
					try {
						logger.info("eLand url={}", filePath);
						URL url = new URL(filePath);

						// 只允許http和https的協議通過
						if (url.getProtocol().startsWith("http")
								|| url.getProtocol().startsWith("https")) {
							in = url.openStream();
							byte[] buf = new byte[4 * 1024]; // 4K buffer
							int bytesRead;
							outputStream = new ByteArrayOutputStream();

							in = url.openStream();
							while ((bytesRead = in.read(buf)) != -1) {
								outputStream.write(buf, 0, bytesRead);
							}
							outputStream.flush();
							outputStream.close();
							in.close();

							if (Util.isNotEmpty(fileMainId)
									&& Util.isNotEmpty(filePid)) {
								baos = (ByteArrayOutputStream) outputStream;

								DocFile file = new DocFile();
								file.setMainId(fileMainId);
								file.setPid(filePid);
								file.setData(baos != null ? baos.toByteArray()
										: null);
								file.setFileDesc(fileDesc);
								file.setCrYear(fileCrYear);
								file.setFieldId("postLoanCertified");
								file.setSrcFileName(fileDesc + ".pdf");
								file.setUploadTime(CapDate
										.getCurrentTimestamp());
								file.setBranchId(user.getUnitNo());
								file.setContentType("application/pdf");
								file.setSysId("LMS");
								docFileService.save(file);

								done = true;
							}
						}
					} catch (IOException ioe) {
						logger.error("url={}", filePath);
						logger.error(StrUtils.getStackTrace(ioe));
					} catch (Exception e) {
						logger.error("url={}", filePath);
						logger.error(StrUtils.getStackTrace(e));
					} finally {
						IOUtils.closeQuietly(in);
					}
				} else {
					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, "找不到附件檔案"),
							getClass());
				}
			}
		}

		if (done) {
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
					.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		} else {
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
					RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行有誤) + "，無新增附件。");
		}

		return result;
	}

	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getC101m29Url(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String c101m29Oid = Util.trim(params.getString("c101m29Oid"));

		String filePath = lms8000Service.findC101m29UrlByOid(c101m29Oid);
		if (Util.isNotEmpty(filePath)) {
			result.set("filePath", filePath);
		}

		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getC101m29UrlToAttach(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		boolean done = false;
		String oidL260M01D = Util.trim(params.getString("oidL260M01D", ""));
		String fileMainId = ""; // L260M01D 的 mainId = L260M01A 的 MAINID
		String filePid = ""; // L260M01D 的 oid
		String cntrNo = "";
		String fileCrYear = CapDate.getCurrentDate("yyyy-MM");
		L260M01D l260m01d = null;
		if (Util.isEmpty(oidL260M01D)) {
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤, "找不到此筆追蹤紀錄"), getClass());
		} else {
			l260m01d = lms8000Service.findModelByOid(L260M01D.class,
					oidL260M01D);
			if (l260m01d != null) {
				fileMainId = l260m01d.getMainId();
				filePid = oidL260M01D;
				cntrNo = Util.nullToSpace(l260m01d.getCntrNo());
			} else {
				throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, "找不到此筆追蹤紀錄資料"),
						getClass());
			}
		}

		String rowObj = Util.trim(params.getString("rowObj", ""));
		String[] dataSplit = rowObj.split("\\^");
		if (dataSplit != null && dataSplit.length > 0) {
			for (String rowData : dataSplit) {
				boolean addDone = false;
				String[] rowObjArray = rowData.split(",");
				String c101m29Oid = null;
				String c101m29MainId = null;
				String contractPriceStr = null;
				String branch = null;
				String custId = null;
				String collNo = null;
				String queryDtStr = null;
				String custName = null;
				c101m29Oid = ((rowObjArray.length < 1) ? "" : rowObjArray[0]);
				c101m29MainId = ((rowObjArray.length < 2) ? "" : rowObjArray[1]);
				contractPriceStr = ((rowObjArray.length < 3) ? ""
						: rowObjArray[2]);
				branch = ((rowObjArray.length < 4) ? "" : rowObjArray[3]);
				custId = ((rowObjArray.length < 5) ? "" : rowObjArray[4]);
				collNo = ((rowObjArray.length < 6) ? "" : rowObjArray[5]);
				queryDtStr = ((rowObjArray.length < 7) ? "" : rowObjArray[6]);
				custName = ((rowObjArray.length < 8) ? "" : rowObjArray[7]);
				String fileDesc = branch + "_" + custId + "_" + collNo;


				// J-112-0341 貸後管理追蹤系統需判別由本行估價系統回傳本行實價登入資料庫查詢結果資料是否有查得資料, 有查得者,
				// 始將其產生附件崁入貸後管理追蹤表中, 否則提示查無資料即可
				Map<String, Object> C101m29Map = lms8000Service
						.findC101m29ByOid(c101m29Oid);
				String recvCode = "";
				if (Util.isNotEmpty(C101m29Map)) {
					recvCode = Util.trim(MapUtils.getString(C101m29Map,
							"RECVCODE"));
				}

				if (Util.equals(recvCode, "A4")) {
					throw new CapMessageException(RespMsgHelper.getMessage(
							UtilConstants.AJAX_RSP_MSG.執行有誤,
							"所勾選項目之實價登錄查結果為0筆，該附件不得執行上傳附件功能(請執行開啟按鈕確認)"), getClass());
				}

				String filePath = lms8000Service
						.findC101m29UrlByOid(c101m29Oid);
				if (Util.isNotEmpty(filePath)) {
					List<L260S01C> orgS01cList = null;
					// filePid = oidL260M01D = L260S01C.mainId
					// L260S01C.refMainId = c101m29MainId
					List<L260S01C> l260s01cList = lms8000Service
							.findL260s01cByRaspMainId(filePid, c101m29MainId);
					if (l260s01cList != null && l260s01cList.size() > 0) {
						orgS01cList = l260s01cList;
					}
					this.addRaspFile(filePath, fileMainId, filePid, fileDesc,
							fileCrYear, branch, custId, custName, cntrNo,
							c101m29MainId, collNo, contractPriceStr,
							CapDate.getDate(queryDtStr, "yyyy-MM-dd"),
							l260m01d, orgS01cList);
					done = true;
				} else {
					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, "找不到附件檔案"),
							getClass());
				}
			}
		}

		if (done) {
			this.afterRaspFile(l260m01d); // 更改追蹤紀錄

			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功)
							+ "，請至「實價登錄」頁籤輸入成交行情鑑價相關資訊。");
		} else {
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
					RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行有誤) + "，無新增附件。");
		}

		return result;
	}

	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult chkIsRaspAttach(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String fileOid = Util.trim(params.getString("fileOid"));

		result.set("hasRaspData", "N");

		if (Util.isNotEmpty(fileOid)) {
			List<L260S01C> l260s01cList = lms8000Service
					.findL260s01cByRaspFileOid(fileOid);
			if (l260s01cList != null && l260s01cList.size() > 0) {
				result.set("hasRaspData", "Y");
			}
		}

		return result;
	}

	public String deleteL260s01cListByRaspAttach(String raspFileOid) {
		String result = "";
		if (Util.isNotEmpty(raspFileOid)) {
			List<L260S01C> l260s01cList = lms8000Service
					.findL260s01cByRaspFileOid(raspFileOid);
			if (l260s01cList != null && l260s01cList.size() > 0) {
				if (lms8000Service.deleteL260s01cs(l260s01cList)) {
					L260S01C temp = l260s01cList.get(0);
					if (temp != null) {
						String oidL260M01D = temp.getMainId();
						L260M01D l260m01d = lms8000Service.findModelByOid(
								L260M01D.class, oidL260M01D);
						if (l260m01d != null) {
							// 更改實登查詢狀況
							l260m01d.setRaspStatus(lms8000Service
									.getRaspStatus(l260m01d));
							lms8000Service.save(l260m01d);
						}
					}
				} else {
					result = "刪除實登資料失敗！";
				}
			}
		}
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL260S01CDetail(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));

		L260S01C l260s01c = lms8000Service.findModelByOid(L260S01C.class, oid);
		if (l260s01c == null) {
			l260s01c = new L260S01C();
		}

		result.set("oidL260S01C", Util.nullToSpace(l260s01c.getOid()));
		result.set("collNo", Util.nullToSpace(l260s01c.getCollNo()));
		if (l260s01c.getQueryRaspDate() == null
				|| Util.equals(CapDate.ZERO_DATE,
						TWNDate.toAD(l260s01c.getQueryRaspDate()))) {
			result.set("queryRaspDate", "");
		} else {
			result.set("queryRaspDate",
					Util.nullToSpace(TWNDate.toAD(l260s01c.getQueryRaspDate())));
		}
		result.set("contractPrice",
				Util.nullToSpace(l260s01c.getContractPrice()));
		if (l260s01c.getCntrDate() == null
				|| Util.equals(CapDate.ZERO_DATE,
						TWNDate.toAD(l260s01c.getCntrDate()))) {
			result.set("cntrDate", "");
		} else {
			result.set("cntrDate",
					Util.nullToSpace(TWNDate.toAD(l260s01c.getCntrDate())));
		}
		result.set("raspStat", Util.nullToSpace(l260s01c.getRaspStat()));
		result.set("raspWay", Util.nullToSpace(l260s01c.getRaspWay()));
		if (l260s01c.getRWayDt() == null
				|| Util.equals(CapDate.ZERO_DATE,
						TWNDate.toAD(l260s01c.getRWayDt()))) {
			result.set("rWayDt", "");
		} else {
			result.set("rWayDt",
					Util.nullToSpace(TWNDate.toAD(l260s01c.getRWayDt())));
		}
		result.set("raspAmt", Util.nullToSpace(l260s01c.getRaspAmt()));
		result.set("raspDscr", Util.nullToSpace(l260s01c.getRaspDscr()));

		String fileOid = Util.nullToSpace(l260s01c.getFileOid());
		result.set("fileOid", fileOid);
		result.set("fileName", "");
		DocFile docFile = docFileService.read(fileOid);
		if (docFile != null) {
			result.set("fileName", Util.nullToSpace(docFile.getSrcFileName()));
		}

		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL260S01C(PageParameters params)
			throws CapException {
		String oid = Util.trim(params.getString("oid"));
		CapAjaxFormResult result = new CapAjaxFormResult();
		String form = Util.trim(params.getString("raspForm"));
		JSONObject jsonData = null;

		if (Util.isNotEmpty(oid)) {
			L260S01C l260s01c = lms8000Service.findModelByOid(L260S01C.class,
					oid);
			if (l260s01c != null) {
				jsonData = JSONObject.fromObject(form);
				DataParse.toBean(jsonData, l260s01c);
				l260s01c.setCheckYN("Y");
				lms8000Service.save(l260s01c);
			}
		}

		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));

		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult afterUploadRaspFile(PageParameters params) {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oidL260S01C = params.getString("oidL260S01C", "");
		String newFileOid = params.getString("newFileOid", "");

		L260S01C l260s01c = lms8000Service.findModelByOid(L260S01C.class,
				oidL260S01C);
		if (l260s01c != null && !CapString.isEmpty(l260s01c.getFileOid())) {
			// 刪除已存在舊檔案
			docFileService.clean(l260s01c.getFileOid());
		}

		DocFile docFile = docFileService.findByOidAndSysId(newFileOid, "LMS");
		result.set("fileOid", "");
		result.set("fileName", "");
		if (docFile != null) {
			l260s01c.setFileOid(docFile.getOid());
			result.set("fileOid", docFile.getOid());
			result.set("fileName", Util.nullToSpace(docFile.getSrcFileName()));
			lms8000Service.save(l260s01c);
		}

		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));

		return result;
	}

	/**
	 * J-111-0025 實價登錄
	 */
	@SuppressWarnings("unchecked")
	public void findRaspData(String l260m01aMainid, List<L260M01D> targetList) {
		StringBuffer temp = new StringBuffer();

		List<L260M01D> m01dList = null;
		if (targetList != null && !targetList.isEmpty()) {
			m01dList = targetList;
		} else {
			m01dList = (List<L260M01D>) lms8000Service.findListByMainId(
					L260M01D.class, l260m01aMainid);
		}
		if (m01dList != null && m01dList.size() > 0) {
			for (L260M01D l260m01d : m01dList) {
				if (l260m01d != null) {
					if (lms8000Service.isCaseMark(l260m01d, "02")) {
						String cntrNo = Util.nullToSpace(l260m01d.getCntrNo());
						String datasrc = Util.trim(l260m01d.getDataSrc());
						Date followDate = l260m01d.getFollowDate() == null ? new Date()
								: l260m01d.getFollowDate();
						// 一個月前到一個月內 依據申請日期由大到小
						String bgnDate = TWNDate.toAD(CapDate.addMonth(
								followDate, -1));
						String endDate = TWNDate.toAD(CapDate.addMonth(
								followDate, 1));
						String cntrNoBr = cntrNo.substring(0, 3);
						String fileMainId = l260m01d.getMainId(); // L260M01D 的
																	// mainId =
																	// L260M01A
																	// 的 MAINID
						String filePid = l260m01d.getOid(); // L260M01D 的 oid
						String fileCrYear = CapDate.getCurrentDate("yyyy-MM");

						List<Map<String, Object>> cmsC101m29DataList = lms8000Service
								.findC101m29List(cntrNoBr, cntrNo, datasrc,
										bgnDate, endDate);
						if (cmsC101m29DataList != null
								&& cmsC101m29DataList.size() > 0) {
							for (Map<String, Object> cmsMap : cmsC101m29DataList) {
								String c101m29Oid = Util.nullToSpace(MapUtils
										.getString(cmsMap, "OID"));
								String c101m29MainId = Util
										.nullToSpace(MapUtils.getString(cmsMap,
												"MAINID"));
								String branch = Util.nullToSpace(MapUtils
										.getString(cmsMap, "BRANCH"));
								String custId = Util.nullToSpace(MapUtils
										.getString(cmsMap, "CUSTID"));
								String custName = Util.nullToSpace(MapUtils
										.getString(cmsMap, "CUSTNAME"));
								String collNo = Util.nullToSpace(MapUtils
										.getString(cmsMap, "COLLNO"));
								String contractPrice = Util
										.nullToSpace(MapUtils.getString(cmsMap,
												"CONTRACTPRICE"));
								String qRASPTimeStr = Util.nullToSpace(MapUtils
										.getString(cmsMap, "CREATETIME"));
								Date queryRASPDate = CapDate.getDate(
										qRASPTimeStr, "yyyy-MM-dd");
								String recvCode = Util.nullToSpace(MapUtils
										.getString(cmsMap, "RECVCODE"));
								String filePath = Util.nullToSpace(MapUtils
										.getString(cmsMap, "RECVFILE_URL"));

								String fileDesc = branch + "_" + custId + "_"
										+ collNo;

								if (Util.equals(recvCode, "00")) {
									// 先確認複製前次紀錄的docFile有沒有相同的擔保品附件
									// 用檔名比對
									List<DocFile> files = docFileService
											.findByIDAndPid(fileMainId, filePid);
									for (DocFile oFile : files) {
										String srcFileDesc = fileDesc + ".pdf";
										String oFieldId = Util
												.nullToSpace(oFile.getFieldId());
										String oFileDesc = Util
												.nullToSpace(oFile
														.getFileDesc());
										String oSrcFileName = Util
												.nullToSpace(oFile
														.getSrcFileName());
										if (Util.equals(oFieldId,
												"postLoanCertified")
												&& Util.equals(fileDesc,
														oFileDesc)
												&& Util.equals(srcFileDesc,
														oSrcFileName)) {
											docFileService
													.clean(oFile.getOid());
										}
									}

									this.addRaspFile(filePath, fileMainId,
											filePid, fileDesc, fileCrYear,
											branch, custId, custName, cntrNo,
											c101m29MainId, collNo,
											contractPrice, queryRASPDate,
											l260m01d, null);
								} else if (Util.equals(recvCode, "A3")) {
									temp.append(temp.length() > 0 ? "<br/>"
											: "");
									temp.append(fileDesc);
									// 已查過實登無須再查
								}
							}
						}
						this.afterRaspFile(l260m01d); // 更改追蹤紀錄
					}
				}
			}
		}

		if (temp.length() > 0) {
			temp.insert(0, "下列擔保品【分行_客戶統編_擔保品編號】：" + "<br/>");
			temp.append("<br/>" + "已查詢過實價登錄，無須再查。");
		}
	}

	@SuppressWarnings("unchecked")
	public void addRaspFile(String filePath, String fileMainId, String filePid,
			String fileDesc, String fileCrYear, String branch, String custId,
			String custName, String cntrNo, String c101m29MainId,
			String collNo, String contractPrice, Date queryRASPDate,
			L260M01D l260m01d, List<L260S01C> orgS01cList) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		InputStream in = null;
		OutputStream outputStream = null;
		ByteArrayOutputStream baos = null;

		if (Util.isNotEmpty(filePath)) {
			try {
				logger.info("eMap url={}", filePath);
				URL url = new URL(filePath);

				// 只允許http和https的協議通過
				if (url.getProtocol().startsWith("http")
						|| url.getProtocol().startsWith("https")) {
					in = url.openStream();
					byte[] buf = new byte[4 * 1024]; // 4K buffer
					int bytesRead;
					outputStream = new ByteArrayOutputStream();

					in = url.openStream();
					while ((bytesRead = in.read(buf)) != -1) {
						outputStream.write(buf, 0, bytesRead);
					}
					outputStream.flush();
					outputStream.close();
					in.close();

					if (Util.isNotEmpty(fileMainId) && Util.isNotEmpty(filePid)) {
						baos = (ByteArrayOutputStream) outputStream;

						DocFile file = new DocFile();
						file.setMainId(fileMainId);
						file.setPid(filePid);
						file.setData(baos != null ? baos.toByteArray() : null);
						file.setFileDesc(fileDesc);
						file.setCrYear(fileCrYear);
						file.setFieldId("postLoanCertified");
						file.setSrcFileName(fileDesc + ".pdf");
						file.setUploadTime(CapDate.getCurrentTimestamp());
						file.setBranchId(user.getUnitNo());
						file.setContentType("application/pdf");
						file.setSysId("LMS");
						docFileService.save(file);

						String raspFileOid = file.getOid();
						if (orgS01cList != null && !orgS01cList.isEmpty()) {
							// 重新引進實登附件：1. 刪除原實登附件 2. 更新新附件oid 3.
							// 更新CMS.C101M29相關欄位
							List<L260S01C> updS01cList = new ArrayList<L260S01C>();
							for (L260S01C l260s01c : orgS01cList) {
								// 1. 刪除原實登附件
								String oRaspFileOid = l260s01c.getRaspFileOid();
								DocFile docFile = docFileService
										.findByOidAndSysId(oRaspFileOid, "LMS");
								if (docFile != null) {
									docFileService.clean(docFile.getOid());
								}

								// 2. 更新新附件oid
								l260s01c.setRaspFileOid(raspFileOid);

								// 3. 更新CMS.C101M29相關欄位
								l260s01c.setBranch(branch);
								l260s01c.setCustId(custId);
								l260s01c.setCustName(custName);
								l260s01c.setRefMainId(c101m29MainId);
								l260s01c.setCollNo(collNo);
								l260s01c.setContractPrice(Util
										.parseBigDecimal(contractPrice));
								l260s01c.setQueryRaspDate(queryRASPDate);

								l260s01c.setCheckYN("N");
								updS01cList.add(l260s01c);
							}
							if (updS01cList.size() > 0) {
								lms8000Service.saveL260s01cList(updS01cList);
							}
						} else {
							L260S01C l260s01c = new L260S01C();
							l260s01c.setMainId(filePid);
							l260s01c.setCntrNo(cntrNo);
							l260s01c.setCreator(user.getUserId());
							l260s01c.setCreateTime(CapDate
									.getCurrentTimestamp());

							l260s01c.setRaspFileOid(raspFileOid);

							// From CMS.C101M29
							l260s01c.setBranch(branch);
							l260s01c.setCustId(custId);
							l260s01c.setCustName(custName);
							l260s01c.setRefMainId(c101m29MainId);
							l260s01c.setCollNo(collNo);
							l260s01c.setContractPrice(Util
									.parseBigDecimal(contractPrice));
							l260s01c.setQueryRaspDate(queryRASPDate);

							l260s01c.setCheckYN("N");
							lms8000Service.save(l260s01c);
						}
					}
				}
			} catch (IOException ioe) {
				logger.error("url={}", filePath);
				logger.error(StrUtils.getStackTrace(ioe));
			} catch (Exception e) {
				logger.error("url={}", filePath);
				logger.error(StrUtils.getStackTrace(e));
			} finally {
				IOUtils.closeQuietly(in);
			}
		}
	}

	@SuppressWarnings("unchecked")
	public void afterRaspFile(L260M01D l260m01d) {
		// 更改追蹤紀錄的YN 才一定會進去儲存 檢核實登資料
		l260m01d.setCheckYN("N");
		// 更改實登查詢狀況
		l260m01d.setRaspStatus(lms8000Service.getRaspStatus(l260m01d));
		lms8000Service.save(l260m01d);
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult checkComVisisVer(PageParameters params)
			throws CapException {

		String l260m01aMainId = Util.trim(params
				.getString(EloanConstants.MAIN_ID)); // L260M01D的oid
		if (Util.isEmpty(l260m01aMainId)) {
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0025"), getClass());
		}

		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set("showMessage", this.checkComVisisVer(l260m01aMainId));
		return result;
	}

	// J-112-0307
	// 於eloan貸後管理追蹤檢核表維護功能中，新增一鍵生成公司訪問紀錄表及借戶、負責人、所屬企業集團等關係戶於本行各項業務往來彙總表。
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public String checkComVisisVer(String l260m01aMainId) {

		String msg = "";
		List<L260M01D> l260m01dList = (List<L260M01D>) lms8000Service
				.findListByMainIdNotDel(L260M01D.class, l260m01aMainId, true);

		if (Util.isNotEmpty(l260m01dList)) {
			for (L260M01D l260m01d : l260m01dList) {

				List<L260S01D> l260s01dList = (List<L260S01D>) lms8000Service
						.findListByMainIdNotDel(L260S01D.class,
								l260m01d.getOid(), true);
				if (Util.isNotEmpty(l260s01dList) && l260s01dList.size() > 0) {
					L260S01D l260s01d = l260s01dList.get(0);
					String rptId_S01D = Util.trim(l260s01d.getRptId_S01D());
					// 版本別小於最新版本別時，提示有新的訪問紀錄表
					if (LrsUtil
							.compareRptVersion(
									rptId_S01D,
									"<",
									UtilConstants.Lms8000m01_visitComLastVer.V_O_202308)) {
						msg += "<br/>";
						msg += "公司訪問紀錄表有新版本，可自行至追蹤紀錄，點擊「修改公司訪問紀錄表格式為最新版本」鈕，進行更新。";
					}
				}
			}
		}
		return msg;
	}

	// J-112-0307
	// 於eloan貸後管理追蹤檢核表維護功能中，新增一鍵生成公司訪問紀錄表及借戶、負責人、所屬企業集團等關係戶於本行各項業務往來彙總表。
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteReleationCustomerSummaryFile(PageParameters params) throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();

		String l260m01dOid = Util
				.trim(params.getString(EloanConstants.MAIN_ID)); // L260M01D的oid

		L260M01D l260m01d = lms8000Service.findModelByOid(L260M01D.class,
				l260m01dOid);

		if (Util.isNotEmpty(l260m01d)) {
			// 公司訪問記錄表，重新引進之單位主管、帳戶管理員，
			// 若與現行的不一致，刪除分行先前上傳的「借戶、負責人、所屬企業集團等關係戶於本行各項業務往來彙總表」，
			// 以避免與現行公司訪問記錄表之單位主管、帳戶管理員兩欄位不一致
			final String fileTitle = "借戶、負責人、所屬企業集團等關係戶於本行各項業務往來彙總表";
			final String fileField = "postLoanCertified";
			final String fileName = fileTitle + ".pdf";

			List<DocFile> docFileList = docFileService.findByIDAndPid(
					l260m01d.getMainId(), l260m01d.getOid());
			if (Util.isNotEmpty(docFileList)) {
				for (DocFile docFile : docFileList) {
					if (Util.equals(docFile.getFieldId(), fileField)
							&& Util.equals(docFile.getSrcFileName(), fileName))
						docFileService.clean(docFile.getOid());
				}
			}

		}

		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));

		return result;

	}

	// J-113-0035 為利ESG案件之貸後管控,
	// ELOAN國內外企金系統額度明細表其他敘做條件增加「應注意/承諾/待追蹤/ESG連結條款」的登錄機制
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult showL260s01f(PageParameters params)
			throws CapException {

		String l260m01fMainId = Util.trim(params
				.getString(EloanConstants.MAIN_ID)); // L260M01D的oid
		if (Util.isEmpty(l260m01fMainId)) {
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0025"), getClass());
		}

		CapAjaxFormResult result = new CapAjaxFormResult();
		result.putAll(lms8000Service.showL260s01f(l260m01fMainId));

		return result;
	}

		// J-113-0035 為利ESG案件之貸後管控,
	// ELOAN國內外企金系統額度明細表其他敘做條件增加「應注意/承諾/待追蹤/ESG連結條款」的登錄機制
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult viewESGContent(PageParameters params)
			throws CapException {

		String oid = params.getString("oid");
		String brNo = Util.trim(params.getString("brNo"));
		
		Map<String, Object> map = lms8000Service.findESGData(oid, brNo);

		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set("ESGContent",
				Util.trim(MapUtils.getString(map, "ELF603_CONTENT")));

		return result;
	}

	// J-112-0307
	// 於eloan貸後管理追蹤檢核表維護功能中，新增一鍵生成公司訪問紀錄表及借戶、負責人、所屬企業集團等關係戶於本行各項業務往來彙總表。
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult importComVisitData(PageParameters params)
			throws CapException {

		String l260m01dOid = Util
				.trim(params.getString(EloanConstants.MAIN_ID)); // L260M01D的oid
		if (Util.isEmpty(l260m01dOid)) {
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0025"), getClass());
		}

		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		boolean modCompVisitVerFlag = params.getAsBoolean(
				"modCompVisitVerFlag", false);

		CapAjaxFormResult result = new CapAjaxFormResult();
		result.putAll(lms8000Service.findL260s01d(l260m01dOid, custId, dupNo,
				modCompVisitVerFlag));

		if (modCompVisitVerFlag) {
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
					.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		}

		return result;
	}
	
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult importMgr(PageParameters params)
			throws CapException {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String branch = MegaSSOSecurityContext.getUnitNo();

		Map<String, String> accountManagerList = userService
				.findByBrnoAndSignId(branch, new SignEnum[] { SignEnum.甲級主管,
						SignEnum.乙級主管, SignEnum.首長, SignEnum.單位主管,
						SignEnum.經辦人員 });

		// 取得單位主管
		IBranch ibranch = branchService.getBranch(user.getUnitNo());
		Map<String, String> unitManagerList = new HashMap<String, String>();
		unitManagerList.put(Util.trim(ibranch.getBrnMgr()),
				getPerName(Util.trim(ibranch.getBrnMgr())));

		CapAjaxFormResult result = new CapAjaxFormResult();

		result.set("unitMgr_S01DList", new CapAjaxFormResult(unitManagerList));
		result.set("accountMgr_S01DList", new CapAjaxFormResult(
				accountManagerList));

		return result;
	}

	/**
	 * 依照使用者id傳回對應名稱，若為空值則仍傳回使用者id
	 * 
	 * @param id
	 *            使用者id
	 * @return 空值: 使用者id 非空值: 使用者名稱
	 */
	public String getPerName(String id) {
		return (Util.isNotEmpty(userInfoService.getUserName(id)) ? userInfoService
				.getUserName(id) : UtilConstants.Mark.SPACE);
	}

}
