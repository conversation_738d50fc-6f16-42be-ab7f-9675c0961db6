<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:util="http://www.springframework.org/schema/util"
	xsi:schemaLocation="
http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.0.xsd
http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-2.0.xsd">

	<bean id="simpleOperation" class="tw.com.iisi.cap.operation.simple.SimpleOperation"
		scope="prototype">
		<property name="result" ref="formResult" />
		<property name="name" value="simpleOperation" />
		<property name="ruleMap">
			<util:map map-class="java.util.LinkedHashMap" key-type="java.lang.String"
				value-type="tw.com.iisi.cap.operation.OperationStep">
				<entry key="doActionStep" value-ref="doActionStep" />
			</util:map>
		</property>
	</bean>

	<bean id="eloanOperation" class="tw.com.iisi.cap.operation.simple.SimpleOperation"
		scope="prototype">
		<property name="result" ref="formResult" />
		<property name="name" value="eloanOperation" />
		<property name="ruleMap">
			<util:map map-class="java.util.LinkedHashMap" key-type="java.lang.String"
				value-type="tw.com.iisi.cap.operation.OperationStep">
				<!-- UPGRADETEST OVERRIDE CheckOpenerOpStep 
				<entry key="docCheckOpenerStep" value-ref="docCheckOpenerStep" />
				-->
				<entry key="docLmsCheckOpenerStep" value-ref="docLmsCheckOpenerStep" />
				<entry key="docCheckStatusStep" value-ref="docCheckStatusStep" />
				<entry key="doActionStep" value-ref="doActionStep" />
			</util:map>
		</property>
	</bean>

	<bean id="fileUploadOperation" class="tw.com.iisi.cap.operation.simple.SimpleOperation"
		scope="prototype">
		<property name="result" ref="formResult" />
		<property name="name" value="fileUploadOperation" />
		<property name="ruleMap">
			<util:map map-class="java.util.LinkedHashMap" key-type="java.lang.String"
				value-type="tw.com.iisi.cap.operation.OperationStep">
				<entry key="fileUploadStep" value-ref="fileUploadStep" />
				<entry key="afterUploadStep" value-ref="doActionStep" />
			</util:map>
		</property>
	</bean>

	<bean id="fileDownloadOperation" class="tw.com.iisi.cap.operation.simple.SimpleOperation"
		scope="prototype">
		<property name="result" ref="fileDownloadResult" />
		<property name="name" value="fileDownloadOperation" />
		<property name="ruleMap">
			<util:map map-class="java.util.LinkedHashMap" key-type="java.lang.String"
				value-type="tw.com.iisi.cap.operation.OperationStep">
				<entry key="fileDownloadStep" value-ref="doActionStep" />
			</util:map>
		</property>
	</bean>

	<bean id="megaPDFOperation" class="tw.com.iisi.cap.operation.simple.SimpleOperation"
		scope="prototype">
		<property name="result" ref="megaPdfResult" />
		<property name="name" value="megaPDFOperation" />
		<property name="ruleMap">
			<util:map map-class="java.util.LinkedHashMap" key-type="java.lang.String"
				value-type="tw.com.iisi.cap.operation.OperationStep">
				<entry key="fileDownloadStep" value-ref="doActionStep" />
			</util:map>
		</property>
	</bean>


	<bean id="docCheckStatusStep" class="com.mega.eloan.common.op.step.CheckOpenDocStatusOpStep"
		scope="request" />

	<bean id="docCheckOpenerStep" class="com.mega.eloan.common.op.step.CheckOpenerOpStep"
		scope="request" />
	
	<!-- UPGRADETEST OVERRIDE CheckOpenerOpStep -->
	<bean id="docLmsCheckOpenerStep" class="com.mega.eloan.lms.base.common.op.step.DocLmsCheckOpenerStep"
		scope="request" parent="docCheckOpenerStep"/>

	<bean id="doActionStep" class="tw.com.iisi.cap.operation.step.CapFormHandleOpStep"
		scope="request" />

	<bean id="fileUploadStep" class="tw.com.iisi.cap.operation.step.CapFileUploadOpStep"
		scope="request" >
		<property name="maxRequestSize" value="5242880"/><!-- 5*1024*1024;預設為5MB -->
		<property name="fileSizeLimitErrorCode" value="EFD0063"/>
		<property name="multipartResolver" ref="multipartResolver" />
	</bean>
	
	<!-- [refs#164] 檔案大小限制可能會透過前端指定，所以不在此設定大小限制，改由程式(CapFileUploadOpStep)控制 -->
    <!-- 也由於大小上限並非固定參數，故將此 bean 改為 prototype scope，每次使用到時重新建立，避免不同 session 相互影響 -->    
    <bean id="multipartResolver" class="org.springframework.web.multipart.commons.CommonsMultipartResolver" scope="prototype" >
       <property name="defaultEncoding" value="utf-8" />
    </bean>

	<bean id="formResult" class="tw.com.iisi.cap.response.CapAjaxFormResult"
		scope="prototype" />
		
	<bean id="fileDownloadResult" class="tw.com.iisi.cap.response.CapByteArrayDownloadResult"
		scope="prototype" />

	<bean id="defaultErrorResult" class="com.mega.eloan.common.response.MegaErrorResult"
		scope="prototype" />

	<bean id="megaPdfResult" class="tw.com.iisi.cap.response.CapPDFResult"
		scope="prototype" />
</beans>
