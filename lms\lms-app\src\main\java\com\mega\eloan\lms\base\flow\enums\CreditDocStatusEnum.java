/* 
 * CreditDocStatusEnum.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.flow.enums;

/**
 * <pre>
 * 文件的狀態表(共用)
 * 若要取得各系統使用的文件狀態代碼，請使用其系統所定義之enum
 * 
 * EX: 編製中 010
 * 第一碼：系統碼
 *   0.共用 ,1.擔保品管理系統,2.徵信管理系統,3.企金授信管理系統,4.消金授信管理系統,5.逾放催收管理系統,6.資料建檔
 * 第二碼：類別碼(依據notes文件狀態)
 *   0.待收案件,1.編製中,2.待覆核,3.已核准,4.婉卻	,7.待補件或撤件
 * 第三碼：單位碼&自訂義
 *   0.預設狀態(保留關鍵字),<PERSON><PERSON>分行,<PERSON><PERSON>授管處,C<PERSON>總行,<PERSON><PERSON>區中心,<PERSON><PERSON>海外
 * 
 * </pre>
 * 
 * @since 2011/4/29
 * <AUTHOR> @version <ul>
 *          <li>2011/4/29,,new
 *          <li>2011/8/12,,override toString()以便Enum.toString()可取得code值
 *          </ul>
 */
public enum CreditDocStatusEnum {



	// 營運中心C
	營運中心_審查中("L1C"),
	營運中心_待放行("L2C"), 
	營運中心_待核定("L3C"), 
	營運中心_呈總處("L4C"),
	營運中心_待更正("L5C"),
	營運中心_待分行撤件("L6C"), 
	營運中心_已核准("L7C"),
	營運中心_已婉卻("L8C"),
	營運中心_待陳復("L9C"),
	營運中心_所有提會案件("LAC"), 
	營運中心_陳復案_陳述案("LBC"), 

	營運中心_海外聯貸案_會簽中("LWC"), 
	營運中心_海外聯貸案_待放行("LXC"),
	營運中心_海外聯貸案_已會簽("LYC"),

	// 授管處H
	授管處_已收案件("L10"), 
	授管處_待收案件("L0H"), 
	授管處_審查中("L1H"),
	授管處_已會簽("L2H"), 
	授管處_提授審會("L3H"), 
	授管處_提催收會("L4H"), 
	授管處_提常董會("L5H"),
	授管處_待放行("L6H"),
	授管處_待核定("L7H"),
	授管處_待更正("L8H"), 
	授管處_待分行撤件("L9H"), 
	授管處_已核准("LAH"), 
	授管處_已婉卻("LBH"),
	授管處_待陳復("LCH"),
	授管處_免批覆案件("LDH"),
	授管處_停權編製中("LEH"),
	授管處_停權待覆核("LFH"),
	授管處_停權已覆核("LGH"),

	// 海外O
	海外_編製中("01O"),
	海外_待覆核("02O"), 
	先行動用_已覆核("03O"),
	先行動用_待覆核("04O"), 
	海外_已核准("05O"), 
	海外_婉卻("06O"),
	海外_待補件("07O"), 
	海外_待撤件("0EO"),
	海外_呈授管處("08O"),
	海外_呈營運中心("0FO"), 
	海外_陳復案_陳述案("0BO"),
	海外_呈總行("0CO"), 
	海外_總行待覆核("0DO"),
	海外_提會待登錄("03B"), 
	海外_提會待覆核("04B"),
	泰國_提會待登錄("L3G"),
	泰國_提會待覆核("L4G"),
	海外_總行提會待登錄("L3M"),
	海外_總行提會待覆核("L4M"),
	異常通報("L99"),
	會簽後修改編製中("01K"),
	會簽後修改待覆核("02K"),
	總處營業單位已會簽("03K"),
	總處營業單位待覆核("04K"),
	特殊分行提授審會("05K"),
	特殊分行提催收會("06K"),
	特殊分行提常董會("07K"),
	//J-113-0337  配合本行將於第18屆董事會設置審計委員會替代監查人，請於授審處之授信管理系統→「授信審查」頁面→「案件簽報書」項下增加「提審計委員會」選項。
	特殊分行提審計委員會("08K"),
	//國內簡易行 J
	國內簡易行待覆核("02J"),
	// 改使用中文...(以下英文暫時保留..)
	/** 編製中 **/
	DOC_EDITING("010"),
	/** 待覆核 **/
	WAIT_APPROVE("020"),
	ELOAN電子檔案清理作業文件狀態("DEL"),
	已覆核案件退回紀錄文件狀態("BKL");

	private String code;

	CreditDocStatusEnum(String code) {
		this.code = code;
	}

	public String getCode() {
		return code;
	}

	public boolean isEquals(Object other) {
		if (other instanceof String) {
			return code.equals(other);
		} else {
			return super.equals(other);
		}
	}

	public static CreditDocStatusEnum getEnum(String code) {
		for (CreditDocStatusEnum enums : CreditDocStatusEnum.values()) {
			if (enums.isEquals(code)) {
				return enums;
			}
		}
		return null;
	}

	@Override
	public String toString() {
		return code;
	}

}
