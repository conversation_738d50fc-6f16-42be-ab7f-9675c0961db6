#----------------------
# COMMON-DB JPA PROPERTIES
#----------------------
com.jpa.schema=COM
com.jpa.ddl=false
com.jpa.buildSchema=
com.jpa.runtimeEnhance=supported
com.jpa.agent=false
#\u4E0A\u7248\u6642\u8981\u63DB\u6210\u6709File\u7684\u8A2D\u5B9A
#com.jpa.log=log4j
com.jpa.log=org.apache.openjpa.lib.log.SLF4JLogFactory
#com.jpa.log=File=/eloan/DEV/logs/lms/mega-com-jpa.log, DefaultLevel=TRACE, Runtime=INFO, Tool=INFO, Query=TRACE, SQL=TRACE
com.jpa.log.properties=PrintParameters=True,PrettyPrint=true,PrettyPrintLineLength=80
#com.jpa.cache=false(CacheSize=1000, SoftReferenceSize=0)
com.jpa.cache=true(Types='com.mega.eloan.common.model.CodeType,com.mega.eloan.common.model.SysParameter',EvictionSchedule='0 9 * * *')
com.jpa.cache.query=false
com.jpa.cache.provider=sjvm

#----------------------
# LMS-DB JPA PROPERTIES
#----------------------
lms.jpa.schema=LMS
lms.jpa.ddl=false
lms.jpa.buildSchema=
lms.jpa.runtimeEnhance=supported
lms.jpa.agent=false
#\u4E0A\u7248\u6642\u8981\u63DB\u6210\u6709File\u7684\u8A2D\u5B9A
#lms.jpa.log=log4j
lms.jpa.log=org.apache.openjpa.lib.log.SLF4JLogFactory
#lms.jpa.log=File=/eloan/DEV/logs/lms/mega-lms-jpa.log, DefaultLevel=TRACE, Runtime=INFO, Tool=INFO, Query=TRACE, SQL=TRACE
lms.jpa.log.properties=PrintParameters=True,PrettyPrint=true,PrettyPrintLineLength=80
#lms.jpa.cache=true(CacheSize=0, SoftReferenceSize=0)
#lms.jpa.cache.query=true(CacheSize=0, SoftReferenceSize=0)
lms.jpa.cache=false
lms.jpa.cache.query=false

lms.jpa.cache.provider=sjvm
#ref:https://openjpa.apache.org/builds/2.1.0/apache-openjpa-2.1.0/docs/manual/manual.html#ref_guide_cache_querysql
lms.openjpa.jdbc.QuerySQLCache=true

