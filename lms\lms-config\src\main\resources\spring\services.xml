<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
           http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd">

	<!-- <context:annotation-config /> -->

	<!-- @Service -->
	<context:component-scan base-package="com.mega.eloan.lms.dw.service.impl.**" />
	<context:component-scan base-package="com.mega.eloan.lms.eloandb.service.impl.**" />
	<context:component-scan base-package="com.mega.eloan.lms.mfaloan.service.impl.**" />
	<context:component-scan base-package="com.mega.eloan.lms.obsdb.service.impl.**" />
	
	<context:component-scan base-package="com.mega.eloan.lms.dc.action.**" />

	<context:component-scan base-package="com.mega.eloan.common.log.impl.**" />
	<context:component-scan base-package="com.mega.eloan.**.report.impl.**" />
	<context:component-scan base-package="com.mega.eloan.**.service.impl.**">
		<context:exclude-filter type="regex"
			expression="com.mega.eloan.common.service.impl.DocFileServiceImpl" />
	</context:component-scan>

	<!-- @Controller -->
	<context:component-scan base-package="tw.com.iisi.cap.plugin" />
	<context:component-scan base-package="tw.com.iisi.cap.handler" />
	<context:component-scan base-package="com.mega.eloan.**.handler.**" />

	<!-- @Component -->
	<!-- <context:component-scan base-package="com.mega.eloan.**.flow.**" /> -->

	<!-- Auth -->
	<bean id="authQueryFactory" class="tw.com.jcs.auth.impl.AuthQueryFactoryImpl">
		<property name="dataSource" ref="com-db" />
		<property name="configLocation" value="spring/auth-cfg.xml" />
	</bean>

	<bean id="memberService" class="tw.com.jcs.auth.impl.MemberServiceImpl" />
	<bean id="authService" class="tw.com.jcs.auth.impl.AuthServiceImpl" />
	<bean id="codeItemService" class="tw.com.jcs.auth.impl.CodeItemServiceImpl" />
	<!-- <bean id="branchService" class="tw.com.jcs.auth.impl.BranchServiceImpl"
		/> -->

	<!-- FileUpload -->
	<bean id="docFileService" class="com.mega.eloan.common.service.impl.DocFileServiceImpl">
		<property name="baseDir" value="${docFile.dir}" />
		<property name="docFileDao" ref="docFileDaoImpl" />
		<property name="sysId" value="${systemId}" />
	</bean>


	<bean class="com.mega.eloan.common.response.RespMsgFactoryProcessor">
		<property name="errorCodeService" ref="errorCodeService" />
	</bean>
	
	<bean class="com.mega.eloan.lms.dc.action.ColumnGetDBInfoFactoryProcessor">
		<property name="dbInfo" ref="ColumnGetDBInfo" />
	</bean>

</beans>