package com.mega.eloan.lms.mfaloan.bean;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import tw.com.iisi.cap.model.GenericBean;

public class LNF09G extends GenericBean{

	private static final long serialVersionUID = 1L;

	
	@Column(name="LNF09G_TXN_CODE", length=4, columnDefinition="CHAR(4)",unique = true)
	private String lnf09g_txn_code;

	@Temporal(TemporalType.DATE)
	@Column(name="LNF09G_TXN_DATE", columnDefinition="DATE",unique = true)
	private Date lnf09g_txn_date;

	@Column(name="LNF09G_TXN_TIME", length=12, columnDefinition="CHAR(12)",unique = true)
	private String lnf09g_txn_time;
	
	@Column(name="LNF09G_KEY_1", length=50, columnDefinition="VARCHAR(50)")
	private String lnf09g_key_1;

	@Column(name="LNF09G_KEY_2", length=50, columnDefinition="VARCHAR(50)")
	private String lnf09g_key_2;
	
	@Column(name="LNF09G_KEY_3", length=50, columnDefinition="VARCHAR(50)")
	private String lnf09g_key_3;
	
	@Column(name="LNF09G_KEY_4", length=50, columnDefinition="VARCHAR(50)")
	private String lnf09g_key_4;
	
	@Column(name="LNF09G_DATA_1", length=1000, columnDefinition="VARCHAR(1000)")
	private String lnf09g_data_1;
	
	@Column(name="LNF09G_DATA_2", length=1000, columnDefinition="VARCHAR(1000)")
	private String lnf09g_data_2;
	
	@Column(name="LNF09G_EMPL_NO", length=5, columnDefinition="CHAR(5)")
	private String lnf09g_empl_no;
	
	@Column(name="LNF09G_SUPV_NO", length=5, columnDefinition="CHAR(5)")
	private String lnf09g_supv_no;

	public String getLnf09g_txn_code() {
		return lnf09g_txn_code;
	}

	public void setLnf09g_txn_code(String lnf09g_txn_code) {
		this.lnf09g_txn_code = lnf09g_txn_code;
	}

	public Date getLnf09g_txn_date() {
		return lnf09g_txn_date;
	}

	public void setLnf09g_txn_date(Date lnf09g_txn_date) {
		this.lnf09g_txn_date = lnf09g_txn_date;
	}

	public String getLnf09g_txn_time() {
		return lnf09g_txn_time;
	}

	public void setLnf09g_txn_time(String lnf09g_txn_time) {
		this.lnf09g_txn_time = lnf09g_txn_time;
	}

	public String getLnf09g_key_1() {
		return lnf09g_key_1;
	}

	public void setLnf09g_key_1(String lnf09g_key_1) {
		this.lnf09g_key_1 = lnf09g_key_1;
	}

	public String getLnf09g_key_2() {
		return lnf09g_key_2;
	}

	public void setLnf09g_key_2(String lnf09g_key_2) {
		this.lnf09g_key_2 = lnf09g_key_2;
	}

	public String getLnf09g_key_3() {
		return lnf09g_key_3;
	}

	public void setLnf09g_key_3(String lnf09g_key_3) {
		this.lnf09g_key_3 = lnf09g_key_3;
	}

	public String getLnf09g_key_4() {
		return lnf09g_key_4;
	}

	public void setLnf09g_key_4(String lnf09g_key_4) {
		this.lnf09g_key_4 = lnf09g_key_4;
	}

	public String getLnf09g_data_1() {
		return lnf09g_data_1;
	}

	public void setLnf09g_data_1(String lnf09g_data_1) {
		this.lnf09g_data_1 = lnf09g_data_1;
	}

	public String getLnf09g_data_2() {
		return lnf09g_data_2;
	}

	public void setLnf09g_data_2(String lnf09g_data_2) {
		this.lnf09g_data_2 = lnf09g_data_2;
	}

	public String getLnf09g_empl_no() {
		return lnf09g_empl_no;
	}

	public void setLnf09g_empl_no(String lnf09g_empl_no) {
		this.lnf09g_empl_no = lnf09g_empl_no;
	}

	public String getLnf09g_supv_no() {
		return lnf09g_supv_no;
	}

	public void setLnf09g_supv_no(String lnf09g_supv_no) {
		this.lnf09g_supv_no = lnf09g_supv_no;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}
	
}
