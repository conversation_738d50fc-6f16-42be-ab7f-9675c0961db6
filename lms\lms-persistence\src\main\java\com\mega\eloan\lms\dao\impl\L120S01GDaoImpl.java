package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L120S01GDao;
import com.mega.eloan.lms.model.L120S01G;


/** 企金分析與評估檔 **/
@Repository
public class L120S01GDaoImpl extends LMSJpaDao<L120S01G, String>
	implements L120S01GDao {

	@Override
	public L120S01G findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}
	@Override
	public List<L120S01G> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L120S01G> list = createQuery(L120S01G.class,search).getResultList();
		return list;
	}
	@Override
	public List<L120S01G> findByTypeMainId(String mainId,String dataType) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dataType", dataType);
		List<L120S01G> list = createQuery(L120S01G.class,search).getResultList();
		return list;
	}
	@Override
	public L120S01G findByUniqueKey(String mainId,String custId,String dupNo,String dataType){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "dataType", dataType);
	
		return findUniqueOrNone(search);
	}
	@Override
	public int delModel(String mainId){
		Query query = getEntityManager().createNamedQuery("L120S01G.delModel");
		query.setParameter("MAINID", mainId); //設置參數
		return query.executeUpdate();
	}
	@Override
	public List<L120S01G> findByCustIdDupId(String custId,String DupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", DupNo);
		List<L120S01G> list = createQuery(L120S01G.class,search).getResultList();
		return list;
	}
}