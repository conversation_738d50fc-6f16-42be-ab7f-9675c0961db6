package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.mfaloan.bean.ELF492;
import com.mega.eloan.lms.mfaloan.service.MisELF492Service;

/**
 * <pre>
 * 覆審明細檔
 * </pre>
 * 
 * @since 2013/3/7
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/3/7,EL08034,new
 *          </ul>
 */
@Service
public class MisELF492ServiceImpl extends AbstractMFAloanJdbc implements
		MisELF492Service {

	@Override
	public ELF492 findByPk(String elf492_branch, String elf492_custid,
			String elf492_dupno, String elf492_cntrno, Date elf492_lrdate) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForList(
				"ELF492.selByPk",
				new Object[] { elf492_branch, elf492_custid, elf492_dupno,
						elf492_cntrno, CapDate.parseSQLDate(elf492_lrdate) });
		List<ELF492> list = toELF492(rowData);
		if (list.size() == 1) {
			return list.get(0);
		} else {
			return null;
		}
	}

	@Override
	public Date selMaxLrDateByBrNoCustIdDupNoCntrNo(String brNo, String custId,
			String dupNo, String cntrNo) {
		Map<String, Object> singleRow = this.getJdbc().queryForMap(
				"ELF492.selMaxLrDateByBrNoCustIdDupNoCntrNo",
				new String[] { brNo, custId, dupNo, cntrNo });

		Date r = CapDate.parseDate(Util.trim(MapUtils.getObject(singleRow,
				"ELF492_LRDATE")));
		if (r == null) {
			r = CapDate.parseDate(CapDate.ZERO_DATE);
		}
		return r;
	}

	@Override
	public List<ELF492> selByBrNoCntrNo(String brNo, String cntrNo) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForListWithMax(
				"ELF492.selByBrNoCntrNo", new String[] { brNo, cntrNo });
		return toELF492(rowData);
	}

	@Override
	public Map<String, Object> selStatsDataByBranch_lrDate(
			String elf492_branch, String elf492_lrdate_s, String elf492_lrdate_e) {
		return this.getJdbc().queryForMap(
				"ELF492.selStatsDataByBranch_lrDate",
				new String[] { elf492_branch, elf492_lrdate_s, elf492_lrdate_e,
						elf492_branch, elf492_lrdate_s, elf492_lrdate_e });
	}

	/**
	 * J-111-0554_05097_B1001 Web e-Loan授信修改授信覆審作業系統中之相關事宜
	 * 
	 * @param elf492_branch
	 * @param elf492_lrdate_s
	 * @param elf492_lrdate_e
	 * @param apprId
	 * @return
	 */
	@Override
	public Map<String, Object> selStatsDataByBranch_lrDate_By_ApprId(
			String elf492_branch, String elf492_lrdate_s,
			String elf492_lrdate_e, String elf492_apprId) {
		return this.getJdbc().queryForMap(
				"ELF492.selStatsDataByBranch_lrDate_By_ApprId",
				new String[] { elf492_branch, elf492_lrdate_s, elf492_lrdate_e,
						elf492_apprId, elf492_branch, elf492_lrdate_s,
						elf492_lrdate_e, elf492_apprId });
	}

	@Override
	public ELF492 selMaxUckdDt(String elf492_branch, String elf492_custid,
			String elf492_dupno) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForList(
				"ELF492.selMaxUckdDt",
				new Object[] { elf492_branch, elf492_custid, elf492_dupno });
		List<ELF492> list = toELF492(rowData);
		if (list.size() > 0) {
			return list.get(0);
		} else {
			return null;
		}
	}

	private List<ELF492> toELF492(List<Map<String, Object>> rowData) {
		List<ELF492> list = new ArrayList<ELF492>();
		for (Map<String, Object> row : rowData) {
			ELF492 model = new ELF492();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}
}
