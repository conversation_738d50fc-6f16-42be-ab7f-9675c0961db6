package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.L192S01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;

import com.mega.eloan.lms.model.L192M01A;
import com.mega.eloan.lms.model.L192S01A;

@Repository
public class L192S01ADaoImpl extends LMSJpaDao<L192S01A, String> implements
		L192S01ADao {

	@Override
	public int deleteByMeata(L192M01A meta) {
		Query query = entityManager.createNamedQuery("l192s01a.deleteByMainId");
		query.setParameter("MAINID", meta.getMainId());
		return query.executeUpdate();
	}
	@Override
	public List<L192S01A> findByCntrNo(String CntrNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "quotaNo", CntrNo);
		search.addOrderBy("quotaNo");
		List<L192S01A> list = createQuery(L192S01A.class,search).getResultList();
		
		return list;
	}
}
