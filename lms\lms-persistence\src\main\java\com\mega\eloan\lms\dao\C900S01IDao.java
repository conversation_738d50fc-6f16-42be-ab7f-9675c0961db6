/* 
 * C900S01IDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C900S01I;

/** ID重配號異動檔 **/
public interface C900S01IDao extends IGenericDao<C900S01I> {

	public C900S01I findByOid(String oid);
	
	public List<C900S01I> findByMainId(String mainId);
	
	public C900S01I findByUniqueKey(String mainId, String caseMainId);
}