package com.mega.eloan.lms.fms.handler.form;

import java.io.IOException;
import java.io.InputStream;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.multipart.MultipartFile;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.gwclient.EloanBatchClient;
import com.mega.eloan.common.gwclient.EloanServerBatReqMessage;
import com.mega.eloan.common.response.MegaErrorResult;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.utils.CustomerIdCheckUtil;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import jxl.Cell;
import jxl.CellType;
import jxl.DateCell;
import jxl.Sheet;
import jxl.Workbook;
import jxl.read.biff.BiffException;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.handler.FileUploadHandler;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapMath;
import tw.com.jcs.common.Util;

/**
 * J-113-0490_07623_B1001 公司訪談紀錄表批次調閱
 * 1.建議新增鍵入統編即可調閱分行完成之「公司訪談紀錄表」。
 * 2.建議增列批次列印功能，例如輸入期間及10筆統編，即可產生「公司訪談紀錄表」之PDF檔。
 */
@Scope("request")
@Controller("lms8000fileuploadhandler")
public class LMS8000FileUploadHandler extends FileUploadHandler {
    Logger logger = LoggerFactory.getLogger(getClass());
    @Resource
    EloanBatchClient eloanBatClient;

    /**
     * 上傳檔案後之動作
     *
     * @param params       page參數
     * @param MultipartFile fileItemList
     * @return FormResult
     */
    @Override
	public IResult afterUploaded(PageParameters params) throws CapException {
        MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		MultipartFile uFile = params.getFile(params.getString("fieldId"));
        // 設定上傳檔案資訊
        String fileName = uFile.getName();
        String fieldId = Util.trim(params.getString("fieldId"));
        String oid = params.getString(EloanConstants.OID);

        if (params.containsKey("fileSize")) {
            if (uFile.getSize() > params.getLong("fileSize", 1048576)) {
                // EFD0063=ERROR|上送的檔案已超過$\{fileSize\}M的限制大小，無法執行上傳動作。|
                Map<String, String> msg = new HashMap<String, String>();
                msg.put("fileSize",
                        CapMath.divide(params.getString("fileSize"), "1048576")); // 1M*1024*1024
                MegaErrorResult result = new MegaErrorResult();
				result.putError(params, new CapMessageException(RespMsgHelper.getMessage("EFD0063", msg), getClass()));
                return result;
            }
        }
        logger.debug(uFile.getContentType());

        Workbook workbook = null;
        String errMsg = "";
        InputStream is = null;
        boolean findError = false;

        try {

            is = uFile.getInputStream();
            workbook = Workbook.getWorkbook(is);
            Sheet sheet = workbook.getSheet(0);
            int totalCol = sheet.getColumns();
            if (totalCol == 0) {
                // L120S09a.message14=匯入之黑名單EXCEL格式錯誤。
                throw new CapMessageException("匯入之EXCEL格式錯誤。", getClass());
            }

            List<String> custList = new ArrayList<String>();

            HashMap<String, String> importCustId = new HashMap<String, String>();

            for (int row = 1; row < sheet.getRows(); row++) {
                int column = 0;

                String custId = StringUtils.upperCase(Util.trim(getContents(sheet
                        .getCell(column, row))));
                if (Util.equals(custId, "")) {
                    continue;
                }
                if(CustomerIdCheckUtil.checkCompanyNo(custId) || CustomerIdCheckUtil.checkMegaID(custId)) {
                    custList.add(custId);
                }else{
                    importCustId.put(custId,"第" + row+1 +"筆統編或MegaId檢核不正確!");
                    findError= true;
                }
            }
            if (findError) {
                // L140M01a.message205=資料匯入失敗，錯誤如下:<br>{0}
                StringBuffer allError = new StringBuffer("");

                for (String errCustKey : importCustId.keySet()) {
                    String errCustMsg = importCustId.get(errCustKey);
                    if (Util.notEquals(errCustKey, "")) {
                        allError.append(importCustId).append("：")
                                .append(errCustMsg).append("<br>");
                    }

                }

                workbook.close();

                throw new CapMessageException(MessageFormat.format(
                        "資料匯入失敗，錯誤如下:<br>{0}", allError.toString()), getClass());

            }else{
                EloanServerBatReqMessage req = new EloanServerBatReqMessage();
                req.setUserId(user.getUserId());
                req.setRunType(EloanServerBatReqMessage.RUN_TYPE_QUEUE);
                req.setSchId("SLMS-00202");
                JSONObject paraJson = new JSONObject();
                paraJson.put("unitNo", user.getUnitNo());
                paraJson.put("userId", user.getUserId());
                paraJson.put("custId", StringUtils.join(custList, ','));
                paraJson.put("type", "57");

                StringBuffer batchParams = new StringBuffer();
                batchParams.append("REQUEST=").append(paraJson);
                req.setParams(batchParams.toString());
                logger.debug(ToStringBuilder.reflectionToString(req));
                eloanBatClient.send(req);
            }
        } catch (IOException e) {
            logger.error(e.getMessage(), e);
            throw new CapMessageException("file IO ERROR", getClass());
        } catch (BiffException be) {
            logger.error(be.getMessage(), be);
            throw new CapMessageException("file IO ERROR", getClass());
        } finally {
            if (is != null) {
                try {
                    is.close();
                    if (workbook != null) {
                        workbook.close();
                    }
                } catch (IOException e) {
                    logger.debug("inputStream close Error", getClass());
                }
            }

        }
        CapAjaxFormResult result = new CapAjaxFormResult();
        result.set("success",true);
        return result;
    }

    private String getContents(Cell cell) {
        DateCell dCell = null;
        if (cell.getType() == CellType.DATE) {
            dCell = (DateCell) cell;
            // System.out.println("Value of Date Cell is: " + dCell.getDate());
            // ==> Value of Date Cell is: Thu Apr 22 02:00:00 CEST 2088
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            // System.out.println(sdf.format(dCell.getDate()));
            // ==> 2088-04-22
            return sdf.format(dCell.getDate());
        }
        // possibly manage other types of cell in here if needed for your goals
        // read more:
        // http://www.quicklyjava.com/reading-excel-file-in-java-datatypes/#ixzz2fYIkHdZP
        return cell.getContents();
    }

    @Override
    public String getOperationName() {
        return "fileUploadOperation";
    }
}
