<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <!-- 額度明細表 -->
        <div id="C160M01BThickBox" style="display:none;">
            <div id="C160M01BTab" class="tabs doc-tabs">
                <ul>
                    <li><a href="#C160M01BTab01"><b><th:block th:text="#{'title.tab01'}">文件資訊</th:block></b></a></li>
                    <li><a href="#C160M01BTab02"><b><th:block th:text="#{'title.tab02'}">擔保品</th:block></b></a></li>
                    <li><a href="#C160M01BTab03"><b><th:block th:text="#{'title.tab03'}">借保人</th:block></b></a></li>
                    <li><a href="#C160M01BTab04"><b><th:block th:text="#{'title.tab04'}">產品種類</th:block></b></a></li>
                </ul>
                <div class="tabCtx-warp">
                    <div id="C160M01BTab01">
                        <form id="C160M01BForm" name="C160M01BForm">
                            <table class="tb2" width="100%">
                            	<tr>
                                    <td class="hd2" align="right">
                                        <th:block th:text="#{'C160M01B.custName'}">借款人名稱</th:block>&nbsp;&nbsp;
                                    </td>
                                    <td>
                                        <span id="custId" class="field" ></span>&nbsp;<span id="dupNo" class="field" ></span>&nbsp;<span id="custName" class="field" ></span>&nbsp;
                                    </td>
                                </tr>
                                <tr>
                                    <td width="30%" class="hd2" align="right">
                                        <th:block th:text="#{'C160M01B.cntrNo'}">額度序號</th:block>&nbsp;&nbsp;
                                    </td>
                                    <td width="70%">
                                        <span id="cntrNo" class="field" ></span>&nbsp;
                                    </td>
                                </tr>
								<tr>
                                    <td width="30%" class="hd2" align="right">
                                        <th:block th:text="#{'C160M01B.confirmEmail'}">完成認證Email</th:block>&nbsp;&nbsp;
                                    </td>
                                    <td width="70%">
                                        <span id="confirmEmail" class="field" ></span>&nbsp;
                                    </td>
                                </tr>
                                <tr>
                                    <td width="30%" class="hd2" align="right">
                                        <th:block th:text="#{'C160S01C.approvedAmt'}">核准額度</th:block>&nbsp;&nbsp;
									    <br/>
                                        <button type="button" id="btChangeAmt">
                                            <span class="text-only"><th:block th:text="#{'C160S01C.approvedAmt'}">核准額度</th:block><th:block th:text="#{'C160M01B.change'}">修改</th:block></span>
                                        </button>
                                    </td>
                                    <td width="70%">
                                        <span id="s_loanTotAmt" name="s_loanTotAmt"></span>&nbsp;
										<th:block th:text="#{'other.money'}"></th:block>
										<!--
										<div id="bfLoanTotAmt" style="display:none;">
											<th:block th:text="#{'C160M01B.bfLoanTotAmt'}">原始核准額度</th:block>&nbsp;&nbsp;	
											<span name="bfLoanTotAmt" readonly="true"></span>&nbsp;
											<th:block th:text="#{'other.money'}"></th:block>
										</div>
										-->
                                    </td>
                                </tr>
                                
                                <tr>
                                    <td class="hd2" align="right">
                                        <th:block th:text="#{'C160M01B.blackDesc'}">黑名單</th:block>&nbsp;&nbsp;
                                        <br/>
                                        <button type="button" id="btBlackQuery">
                                            <span class="text-only"><th:block th:text="#{'C160M01B.blackDesc'}">黑名單</th:block><th:block th:text="#{'button.search'}">查詢</th:block></span>
                                        </button>
                                    </td>
                                    <td>
                                        <th:block th:text="#{'queryDate'}">資料查詢日期：</th:block>
                                        <span id="blackDataDate" class="field" ></span>&nbsp;
                                        <br/>
                                        <br/>
                                        <span id="blackDesc" class="field" ></span>&nbsp;
                                    </td>
                                </tr>
                                <tr>
                                    <td class="hd2" align="right">
                                        <th:block th:text="#{'C160M01B.joinMarkDesc'}">共用行銷</th:block>&nbsp;&nbsp;
                                        <br/>
                                        <button type="button" id="btJoinMarkQuery">
                                            <span class="text-only"><th:block th:text="#{'C160M01B.joinMarkDesc'}">共用行銷</th:block><th:block th:text="#{'button.search'}">查詢</th:block></span>
                                        </button>
                                    </td>
                                    <td>
                                        <th:block th:text="#{'queryDate'}">資料查詢日期：</th:block>
                                        <span id="joinMarketingDate" class="field" ></span>&nbsp;
                                        <br/>
                                        <br/>
                                        <span id="joinMarkDesc" class="field" ></span>&nbsp;
                                        <br/>
                                        <span class="text-red"><th:block th:text="#{'C160M01B.joinMarkDescTitle'}">注意資料來源為0024交易維護內容，如有疑問請先用0024交易維護再重新引入。</th:block></span>
                                    </td>
									<td style="display:none;">
										<span id="uid"></span>&nbsp;
									</td>
                                </tr>
								<!-- J-110-0527 消金聯徵查詢結果(1.簽報書查詢結果和2.製作動審表時查詢之結果) 與查詢聯徵日期之欄位-->
								<tr>
                                    <td class="hd2" align="right">
                                        <th:block th:text="#{'C160M01B.jcicResult'}">聯徵查詢結果</th:block>&nbsp;&nbsp;
                                        <br/>
                                        <button type="button" id="btjcicRefresh">
                                            <span class="text-only"><th:block th:text="#{'C160M01B.jcicRefresh'}">聯徵查詢結果更新</th:block></span>
                                        </button>
                                    </td>
                                    <td>
                                    	<span class="text-red"><th:block th:text="#{'C160M01B.jcicReDoMsg'}">動撥日距查詢聯徵日期已超過3個月，請再次查詢聯徵，請至聯徵查詢系統查詢後，點選聯徵查詢結果更新按鈕更新資料</th:block></span>
										<br/>
                                        <div id="jcicGrid" ></div>
                                    </td>
                                </tr>
                            </table>
							
							<!--J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制-->
							<div id="lms160s0301Tab04">
								<div id="L140M01M_clear_land_ctrl">
						        	<fieldset>
						        		<legend>
						                    <strong><th:block th:text="#{'L140M01M.clearLand'}">撥貸未動工興建之空地貸款控管註記</th:block></strong>
						                </legend>
										<tr>
											<td>
												<span class="text-only color-red" style="font-weight:bold;"><th:block th:text="#{'L140M01M.isClearLand'}">本案是否為撥貸逾一年以上未動工興建之空地貸款控對象</th:block></span>
												 
											</td>
									        <td>
					                            <label>
					                                <input type="radio" id="isClearLand" name="isClearLand" value="Y"  readonly="readonly"  disabled="true" />
					                                <th:block th:text="#{'yes'}">是</th:block>
					                            </label>
					                            <label>
					                                <input type="radio"  id="isClearLand" name="isClearLand" value="N"  readonly="readonly"  disabled="true" />
					                                <th:block th:text="#{'no'}">否</th:block>
					                            </label>
												&nbsp;&nbsp;
												 <button type="button" id="btnApplyClearLand">
					                                <span class="text-only"><th:block th:text="#{'button.pullin'}"><!-- 引進--></th:block></span>
					                             </button>
					                        </td>
										</tr>	
										<br/>
										<div id ="showClearLand" style="display:none" >
											<table width="100%" border="2" cellspacing="0" class="tb2">													
												<tr>
													<td class="hd1" style="width:20%">
														<span class="text-only"><th:block th:text="#{'L140M01M.ctlType'}">控管類別</th:block></span>										
													</td>
													<td colspan="3" >	
													    <select name="ctlType" id="ctlType" comboKey="lms7600_ctlType" comboType="2"  space="true" readonly="readonly" class="noEdit" disabled="true">
						                                </select>                                        
												    </td>											
												</tr>
												<tr>
													<td class="hd1" style="width:20%">
														<span class="text-only"><th:block th:text="#{'L140M01M.fstDate'}">初次核定預計動工日</th:block></span>										
													</td>
													<td style="width:30%">	
													     <!--<span id="fstDate" class="field"></span>  --> 
														 <input type='text' id='fstDate' name='fstDate' class='date' size='10' />                                           
												    </td>
													<td class="hd1" style="width:20%">
														<span class="text-only"><th:block th:text="#{'L140M01M.lstDate'}">最新核定(動審)預計動工日</th:block></span>										
													</td>
													<td style="width:30%">	
													     <!--<span id="lstDate" class="field"></span>  -->   
														 <input type='text' id='lstDate' name='lstDate' class='date' size='10' />                                         
												    </td>												
												</tr>	
												 
												<tr>
													<td class="hd1">
														<span class="text-only"><th:block th:text="#{'L140M01M.isChgStDate'}">是否變更預計動工日</th:block></span>										
													</td>
													<td colspan="3">	
													    <label>
						                                    <input type="radio" id="isChgStDate" name="isChgStDate" value="Y"   />
						                                    <th:block th:text="#{'yes'}">是</th:block>
						                                </label>
						                                <label>
						                                    <input type="radio" id="isChgStDate" name="isChgStDate" value="N"  />
						                                    <th:block th:text="#{'no'}">否</th:block>
						                                </label>                                
												    </td>											
												</tr>	
												<tr class="showChgStDate" style="display:none">
													<td class="hd1" style="width:20%">
														<span class="text-only"><th:block th:text="#{'L140M01M.cstDate'}">變更預計動工日</th:block></span>										
													</td>
													<td style="width:30%">	
													     <input type='text' id='cstDate' name='cstDate' class='date' size='10' />                          
												    </td>	
													<td class="hd1" style="width:20%">
														<span class="text-only"><th:block th:text="#{'L140M01M.cstReason'}">變更預計動工日原因</th:block></span>										
													</td>
													<td style="width:30%">	
													    <select name="cstReason" id="cstReason" space="true" comboKey="lms7600_cstReason" comboType="2" >
						                                </select>                                        
												    </td>											
												</tr>		
												<tr class="showChgStDate" style="display:none">
													<td class="hd1">
														<span class="text-only"><th:block th:text="#{'L140M01M.adoptFg'}">輸入本次採行措施</th:block></span>										
													</td>
													<td colspan="3">	
													     <input type="checkbox" name="adoptFg" id="adoptFg" comboKey="lms7600_adoptFg" comboType="2" />                
												    </td>											
												</tr>
												<tr class="showIsChgRate" style="display:none">
													<td class="hd1" >
														<span class="text-only"><th:block th:text="#{'L140M01M.isChgRate'}">是否調降利率</th:block></span>										
													</td>
													<td colspan="3">	
													    <label>
						                                    <input type="radio" id="isChgRate" name="isChgRate" value="Y"   />
						                                    <th:block th:text="#{'yes'}">是</th:block>
						                                </label>
						                                <label>
						                                    <input type="radio" id="isChgRate" name="isChgRate" value="N"   />
						                                    <th:block th:text="#{'no'}">否</th:block>
						                                </label>                                
												    </td>											
												</tr>	
												<tr class="showChgRate" style="display:none">
													<td class="hd1">
														<span class="text-only"><th:block th:text="#{'L140M01M.rateAdd'}">再加減碼幅度</th:block></span>										
													</td>
													<td colspan="3">	
													    <input type="text" id="rateAdd" name="rateAdd" size="8" maxlength="10" class="numeric" fraction="5" integer="4" />％(負號為減碼)                                  
												    </td>											
												</tr>	
												<tr class="showChgRate" style="display:none">
													<td class="hd1" rowspan="2">
														<span class="text-only"><th:block th:text="#{'L140M01M.custRoa'}">借款人ROA</th:block></span>			
														&nbsp;&nbsp;
														 <button type="button" id="btnApplyClearLandRoa">
							                                <span class="text-only"><th:block th:text="#{'button.pullin'}"><!-- 引進--></th:block></span>
							                             </button>							
													</td>
													<td>	
													    <!--<input type="text" id="custRoa" name="custRoa" size="10" maxlength="16" class="numeric number" fraction="2" integer="13" readonly="readonly" class="noEdit" disabled="true"/>-->
														<span id="custRoa" class="field"></span>％                                  
												    </td>	
													<td class="hd1">
														<span class="text-only"><th:block th:text="#{'L140M01M.relRoa'}">關係人ROA</th:block></span>							
													</td>
													<td>	
													    <!--<input type="text" id="relRoa" name="relRoa" size="10" maxlength="16" class="numeric number" fraction="2" integer="13" readonly="readonly" class="noEdit" disabled="true"/>-->
														<span id="relRoa" class="field"></span>％                                            
												    </td>											
												</tr>		
												 
												<tr class="showChgRate" style="display:none">
													<td colspan="3">	
													    <span class="text-only"><th:block th:text="#{'L140M01M.roaDuring'}">ROA查詢期間</th:block></span>	：
													    <span id="roaBgnDate" class="field"></span>～   
														<span id="roaEndDate" class="field"></span>                                   
												    </td>											
												</tr>
												
												<tr class="showIsLegal" style="display:none">
													<td class="hd1">
														<span class="text-only"><th:block th:text="#{'L140M01M.isLegal'}">是否符合本行規定</th:block></span>
														&nbsp;&nbsp;
														 <button type="button" id="btnApplyIsLegal">
							                                <span class="text-only"><th:block th:text="#{'button.pullin'}"><!-- 引進--></th:block></span>
							                             </button>											
													</td>
													<td colspan="3">	
													    <label>
						                                    <input type="radio" id="isLegal" name="isLegal" value="Y" readonly="readonly" class="noEdit" disabled="true" />
						                                    <th:block th:text="#{'yes'}">是</th:block>
						                                </label>
						                                <label>
						                                    <input type="radio" id="isLegal" name="isLegal" value="N" readonly="readonly" class="noEdit" disabled="true" />
						                                    <th:block th:text="#{'no'}">否</th:block>
						                                </label>                                
												    </td>											
												</tr>																			
											</table>                                                
										</div>
									</fieldset>
								</div>
							</div>
							<!--End-- J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制-->
                        </form>
                    </div>
                    <div id="C160M01BTab02">
                        <button type="button" id="btEdit02">
                            <span class="text-only"><th:block th:text="#{'button.edit'}">編輯</th:block></span>
                        </button>
                        <div id="C160S01AGrid" ></div>
                    </div>
                    <div id="C160M01BTab03">
                        <button type="button" id="btPullinAgain03">
                            <span class="text-only"><th:block th:text="#{'button.pullinAgain'}">重新引進</th:block></span>
                        </button>
                        <button type="button" id="btAdd03">
                            <span class="text-only"><th:block th:text="#{'button.add'}">新增</th:block></span>
                        </button>
                        <button type="button" id="btEdit03">
                            <span class="text-only"><th:block th:text="#{'button.edit'}">編輯</th:block></span>
                        </button>
                        <button type="button" id="btDelete03">
                            <span class="text-only"><th:block th:text="#{'button.delete'}">刪除</th:block></span>
                        </button>
                        <div id="C160S01BGrid" ></div>
                    </div>
                    <div id="C160M01BTab04">
                        <button type="button" id="btCopy04">
                            <span class="text-only"><th:block th:text="#{'button.copy1'}">複抄</th:block></span>
                        </button>
                        <button type="button" id="btEdit04">
                            <span class="text-only"><th:block th:text="#{'button.edit'}">編輯</th:block></span>
                        </button>
                        <!--
                        <button type="button" id="btDelete04" >
                        <span class="text-only"><th:block th:text="#{'button.delete'}">刪除</th:block></span>
                        </button>
                        -->
                        <div id="CopyC160S01CThickBox">
                            <div id="C160S01CGrid" ></div>
                        </div>
                    </div>
                </div>
            </div>
        </div><!-- 擔保品明細 -->
        <div id="C160S01AThickBox" style="display:none;">
            <div th:insert="~{cls/panels/CLS1161S02APanel :: panelFragmentBody}"></div>
        </div>
        <!-- 借保人明細 -->
        <div id="C160S01BThickBox" style="display:none;">
            <div th:insert="~{cls/panels/CLS1161S02BPanel :: panelFragmentBody}"></div>
        </div>
        <!-- 產品種類明細 -->
        <div id="C160S01CThickBox" style="display:none;">
            <div th:insert="~{cls/panels/CLS1161S02CPanel :: panelFragmentBody}"></div>
        </div>
        <!-- 借款人資料表 -->
        <div id="BorrowersThickBox" style="display:none;">
            <div id="BorrowersGrid" ></div>
        </div><!-- 帳號資料表-->
        <div id="AccountThickBox" style="display:none;">
            <div id="AccountGrid" ></div>
        </div><!-- 代償轉貸借新還舊明細 -->
        <div id="C160S01FThickBox" style="display:none;">
            <div th:insert="~{cls/panels/CLS1161S02FPanel :: panelFragmentBody}"></div>
        </div>
        <!-- 放款帳號資料表 -->
        <div id="lnf030ThickBox" style="display:none;">
            <div id="lnf030Grid" ></div>
        </div>
        <div id="openInputCustIdByLNF030Box" style="display:none">
            <form action="" id="LNF030BoxForm" name="LNF030BoxForm">
                <table class="tb2" width="100%">
                    <tr>
                        <td class="hd1" style="width:30%">
                            <th:block th:text="#{'doc.id'}">統一編號</th:block>
                        </td>
                        <td>
                            <input type="text" id="LNF030Box_custId" name="LNF030Box_custId" maxlength="10" size="11" class="upText obuText required"/>
                            <input type="text" id="LNF030Box_dupNo" name="LNF030Box_dupNo" maxlength="1" size="1" class="upText obuText required"/>
                        </td>
                    </tr>
                </table>
            </form>
        </div>
		
		<div id="isChangeAmt" style="display:none;">		
			<table class="tb2" width="100%">
				<tr>
         		<td class="hd1" width="50%"><th:block th:text="#{'C160M01B.bfLoanTotAmt'}">原始核准額度</th:block></td>
           		<td width="50%">									   
		        	<input type="text" id="bfLoanTotAmt" name="bfLoanTotAmt" readonly="true"/>&nbsp;
					<th:block th:text="#{'other.money'}"></th:block>					
           		</td>
				</tr>
				
				<tr>
         		<td class="hd1"><th:block th:text="#{'C160M01B.isChangeLoanTotAmt'}">調整後核准額度</th:block></td>
           		<td>									   
		        	<input type="text" id="new_loanTotAmt" name="new_loanTotAmt" size="18" maxlength="19" integer="13" class="numeric required" />
					<th:block th:text="#{'other.money'}"></th:block>					
           		</td>
				</tr>
			</table>
		</div>
    </body>
</html>
