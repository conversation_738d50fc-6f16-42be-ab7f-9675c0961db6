/* 
 * DW_RKSCORE.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.mfaloan.bean;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

import tw.com.iisi.cap.model.GenericBean;


/** 評分變量紀錄 **/
public class DW_RKSCORE extends GenericBean{

	private static final long serialVersionUID = 1L;

	/** 分行別 **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="BR_CD", length=3, columnDefinition="CHAR(3)", nullable=false,unique = true)
	private String br_cd;

	/** NOTES文件編號 **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="NOTEID", length=32, columnDefinition="CHAR(32)", nullable=false,unique = true)
	private String noteid;

	/** 客戶統一編號 **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="CUSTID", length=10, columnDefinition="CHAR(10)", nullable=false,unique = true)
	private String custid;

	/** 重複序號 **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)", nullable=false,unique = true)
	private String dupno;

	/** 評等模型類別 **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="MOWTYPE", length=1, columnDefinition="CHAR(1)", nullable=false,unique = true)
	private String mowtype;

	/** 模型版本-大版 **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="MOWVER1", columnDefinition="DECIMAL(5,0)", nullable=false,unique = true)
	private Integer mowver1;

	/** 模型版本-小版 **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="MOWVER2", columnDefinition="DECIMAL(5,0)", nullable=false,unique = true)
	private Integer mowver2;

	/** JCIC查詢日期 YYYY-MM-DD **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Temporal(TemporalType.DATE)
	@Column(name="JCIC_DATE", columnDefinition="DATE", nullable=false,unique = true)
	private Date jcic_date;

	/** 序號/帳號 **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="ACCT_KEY", length=14, columnDefinition="CHAR(14)", nullable=false,unique = true)
	private String acct_key;
	
	/** 主借款人統一編號(CUSTKEY) **/
	@Column(name="CUST_KEY", length=10, columnDefinition="CHAR(10)")
	private String cust_key;

	/** 相關身分(LNGEFLAG) **/
	@Column(name="LNGEFLAG", length=1, columnDefinition="CHAR(1)")
	private String lngeflag;

	/** 當月循環信用餘額(ΣREVOL_BAL) **/
	@Column(name="CC_REVOL_BAL", columnDefinition="DECIMAL(15,0)")
	private BigDecimal cc_revol_bal;

	/** 當月有動用循環信用之信用卡額度(ΣPERM_LIMIT) **/
	@Column(name="CC_REVOL_PERMIT_LIMIT", columnDefinition="DECIMAL(15,0)")
	private BigDecimal cc_revol_permit_limit;

	/** 當月循環信用使用率(REVOL_RATE) **/
	@Column(name="R10_REVOL_RATE", columnDefinition="DECIMAL(6,0)")
	private BigDecimal r10_revol_rate;

	/** R_10得分 **/
	@Column(name="R10_REVOL_RATE_SCORE", columnDefinition="DECIMAL(6,0)")
	private BigDecimal r10_revol_rate_score;

	/** 當月無擔保授信餘額(仟元) **/
	@Column(name="D07_LN_NOS_TAMT", columnDefinition="DECIMAL(15,0)")
	private BigDecimal d07_ln_nos_tamt;

	/** D07得分 **/
	@Column(name="D07_LN_NOS_TAMT_SCORE", columnDefinition="DECIMAL(6,0)")
	private BigDecimal d07_ln_nos_tamt_score;

	/** 近6個月平均的月信用卡循環信用(元) **/
	@Column(name="D15_CC6_AVG_RC", columnDefinition="DECIMAL(15,0)")
	private BigDecimal d15_cc6_avg_rc;

	/** D_15得分 **/
	@Column(name="D15_CC6_AVG_RC_SCORE", columnDefinition="DECIMAL(6,0)")
	private BigDecimal d15_cc6_avg_rc_score;

	/** 近12個月信用卡繳款狀況出現全額繳清無延遲次數 **/
	@Column(name="P19_CC12_PCODE_A_TIMES", columnDefinition="DECIMAL(3,0)")
	private Integer p19_cc12_pcode_a_times;

	/** 婚姻狀況 **/
	@Column(name="MARRIAGE", columnDefinition="DECIMAL(2,0)")
	private Integer marriage;

	/** 扶養子女數 **/
	@Column(name="CHILDREN", columnDefinition="DECIMAL(2,0)")
	private Integer children;

	/** 職業大類 **/
	@Column(name="OCCUPATION_1", columnDefinition="DECIMAL(4,0)")
	private Integer occupation_1;

	/** 職業大類得分 **/
	@Column(name="OCCUPATION_1_SCORE", columnDefinition="DECIMAL(6,0)")
	private BigDecimal occupation_1_score;

	/** 學歷 **/
	@Column(name="EDUCATION", columnDefinition="DECIMAL(2,0)")
	private Integer education;

	/** 近12個月新業務申請查詢總家數 **/
	@Column(name="N06_INQ12_NAPP_BANK", columnDefinition="DECIMAL(3,0)")
	private Integer n06_inq12_napp_bank;

	/** N06得分 **/
	@Column(name="N06_INQ12_NAPP_BANK_SCORE", columnDefinition="DECIMAL(6,0)")
	private BigDecimal n06_inq12_napp_bank_score;

	/** 評等日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="RATING_DATE", columnDefinition="DATE")
	private Date rating_date;

	/** 文件狀態 **/
	@Column(name="DOCSTATUS", length=2, columnDefinition="CHAR(2)")
	private String docstatus;

	/** 上傳資料日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="DATA_SRC_DT", columnDefinition="DATE")
	private Date data_src_dt;

	/** 近12個月信用卡(每筆)循環信用平均使用率 **/
	@Column(name="R01_CC12_REVOL_RATE", columnDefinition="DECIMAL(10,4)")
	private BigDecimal r01_cc12_revol_rate;

	/** 近12個月信用卡(每筆)循環信用平均使用率得分 **/
	@Column(name="R01_CC12_REVOL_RATE_SCORE", columnDefinition="DECIMAL(10,4)")
	private BigDecimal r01_cc12_revol_rate_score;

	/** 夫妻年收入(萬元) **/
	@Column(name="HINCOME_REG", columnDefinition="DECIMAL(6,0)")
	private BigDecimal hincome_reg;

	/** 夫妻年收入(萬元)得分 **/
	@Column(name="HINCOME_REG_SCORE", columnDefinition="DECIMAL(6,0)")
	private BigDecimal hincome_reg_score;

	/** 近12個月信用卡繳款狀況出現不良繳款紀錄或使用循環信用的次數 **/
	@Column(name="P69_CC12_DELAY_RC_TIMES", columnDefinition="DECIMAL(3,0)")
	private Integer p69_cc12_delay_rc_times;

	/** 近6個月信用卡繳款狀況出現全額繳清無延遲次數 **/
	@Column(name="P25_CC6_PCODE_A_TIMES", columnDefinition="DECIMAL(3,0)")
	private Integer p25_cc6_pcode_a_times;

	/** 近6個月信用卡繳款狀況出現全額繳清無延遲次數得分 **/
	@Column(name="P25_CC6_PCODE_A_TIMES_SCORE", columnDefinition="DECIMAL(6,0)")
	private BigDecimal p25_cc6_pcode_a_times_score;

	/** 婚姻_學歷得分 **/
	@Column(name="MARR_EDU_SCORE", columnDefinition="DECIMAL(6,0)")
	private BigDecimal marr_edu_score;

	/** P69_P19得分 **/
	@Column(name="P69_P19_SCORE", columnDefinition="DECIMAL(6,0)")
	private BigDecimal p69_p19_score;	

	/** 個人年收入（萬元）
	 * 在schema 的定義為 (13,0)，但HINCOME_REG夫妻年收入(萬元) 只有(6,0)    
	 */
	@Column(name="YPAY", columnDefinition="DECIMAL(6,0)")
	private BigDecimal ypay;

	/** 個人年收入（萬元）得分 **/
	@Column(name="YPAY_SCORE", columnDefinition="DECIMAL(7,4)")
	private BigDecimal ypay_score;
	
	/** 年資  **/
	@Column(name="SENIORITY", columnDefinition="DECIMAL(4,2)")
	private BigDecimal seniority;

	/** 年資分數 **/
	@Column(name="SENIORITY_SCORE", columnDefinition="DECIMAL(7,4)")
	private BigDecimal seniority_score;

	/** 學歷分數 **/
	@Column(name="EDUCATION_N_SCORE", columnDefinition="DECIMAL(10,4)")
	private BigDecimal education_n_score;
	
	/** 聯徵查詢月份當時無擔保授信往來家數(排除學生助學貸款(Z)、本行貸款)D63 **/
	@Column(name="D63_LN_NOS_BANK", columnDefinition="DECIMAL(5,2)")
	private BigDecimal d63_ln_nos_bank;

	/** 聯徵查詢月份當時無擔保授信往來家數(排除學生助學貸款(Z)、本行貸款)得分 D63_SCORE**/
	@Column(name="D63_SCORE", columnDefinition="DECIMAL(7,4)")
	private BigDecimal d63_score;

	/** 近6個月信用卡使用循環信用的月份數 A21**/
	@Column(name="A21_CC6_RC_USE_MONTH", columnDefinition="DECIMAL(5,2)")
	private BigDecimal a21_cc6_rc_use_month;

	/** 近6個月信用卡使用循環信用的月份數得分 A21_SCORE**/
	@Column(name="A21_SCORE", columnDefinition="DECIMAL(7,4)")
	private BigDecimal a21_score;
	
	/** 近6個月信用卡使用循環信用的家數 A11 **/
	@Column(name="A11_CC6_RC_USE_BANK", columnDefinition="DECIMAL(5,2)")
	private BigDecimal a11_cc6_rc_use_bank;

	/** 近6個月信用卡使用循環信用的家數得分 A11_SCORE**/
	@Column(name="A11_SCORE", columnDefinition="DECIMAL(7,4)")
	private BigDecimal a11_score;
	
	/** 聯徵查詢月份當時授信繳款記錄小於等於6次旗標 D53 **/
	@Column(name="D53_LN_6_TIMES_FLAG", columnDefinition="DECIMAL(5,2)")
	private BigDecimal d53_ln_6_times_flag;

	/** 聯徵查詢月份當時授信繳款記錄小於等於6次旗標得分 D53_SCORE **/
	@Column(name="D53_SCORE", columnDefinition="DECIMAL(7,4)")
	private BigDecimal d53_score;

	/** 個人年所得(年薪+其他收入)(萬元)  **/
	@Column(name="PINCOME", columnDefinition="DECIMAL(13,0)")
	private BigDecimal pincome;
	
	/** 個人年所得(年薪+其他收入)分數  **/
	@Column(name="PINCOME_SCORE", columnDefinition="DECIMAL(7,4)")
	private BigDecimal pincome_score;
	
	/** 近6個月信用卡繳款狀況出現不良繳款紀錄或使用循環信用之次數  **/
	@Column(name="P68_CC6_DELAY_RC_TIMES", columnDefinition="DECIMAL(5,2)")
	private BigDecimal p68_cc6_delay_rc_times;
	
	/** P68_P19得分  **/
	@Column(name="P68_P19_SCORE", columnDefinition="DECIMAL(7,4)")
	private BigDecimal p68_p19_score;
	
	/** 近12個月新業務申請查詢次數,計算方式-天數別(30天內算1次)  **/
	@Column(name="N18_INQ12_BY30D", columnDefinition="DECIMAL(5,2)")
	private BigDecimal n18_inq12_by30d;
	
	/** N18得分  **/
	@Column(name="N18_SCORE", columnDefinition="DECIMAL(10,4)")
	private BigDecimal n18_score;
	
	/** 近12個月新業務申請查詢次數(排除近三個月本行查詢),計算方式-天數別(30天內算1次)  **/
	@Column(name="N22_INQ12_BY30D", columnDefinition="DECIMAL(5,2)")
	private BigDecimal n22_inq12_by30d;
	
	/** N22得分  **/
	@Column(name="N22_SCORE", columnDefinition="DECIMAL(10,4)")
	private BigDecimal n22_score;
	
	/** 聯徵查詢月份當月無擔保授信餘額(不含學生助學貸款)除以個人月收入  **/
	@Column(name="D07_DIV_PINCOME", columnDefinition="DECIMAL(19,4)")
	private BigDecimal d07_div_pincome;
	
	/** D07_DIV_PINCOME得分  **/
	@Column(name="D07_DIV_PINCOME_SCORE", columnDefinition="DECIMAL(7,4)")
	private BigDecimal d07_div_pincome_score;
	
	/** 從債務(企業關係)授信餘額(仟元)  **/
	@Column(name="Z03_ACC_DEBT_ENTERPRISE", columnDefinition="DECIMAL(17,2)")
	private BigDecimal z03_acc_debt_enterprise;
	
	/** Z03得分  **/
	@Column(name="Z03_SCORE", columnDefinition="DECIMAL(7,4)")
	private BigDecimal z03_score;
	
	/** 卡友貸旗標 */
	@Column(name="C_FLAG", length=1, columnDefinition="CHAR(1)")
	private String c_flag;
	
	/** 個人負債比率分數  **/
	@Column(name="DRATE_SCORE", columnDefinition="DECIMAL(7,4)")
	private BigDecimal drate_score;
	
	/** ----------消金房貸3.0 Start---------- **/
	/**職稱 **/
	@Column(name="DESIGNATION", columnDefinition="CHAR(1)")
	private String designation;
	
	/**職稱得分  **/
	@Column(name="DESIGNATION_SCORE", columnDefinition="DECIMAL(10,4)")
	private BigDecimal designation_score;
	
	/**D42因子 **/
	@Column(name="D42_AVG_LIMIT", columnDefinition="DECIMAL(14,5)")
	private BigDecimal d42_avg_limit;
	
	/**D42得分  **/
	@Column(name="D42_SCORE", columnDefinition="DECIMAL(10,4)")
	private BigDecimal d42_score;
	
	/**P68得分  **/
	@Column(name="P68_SCORE", columnDefinition="DECIMAL(10,4)")
	private BigDecimal p68_score;
	
	/**夫妻負債比(YRATE)得分  **/
	@Column(name="YRATE_SCORE", columnDefinition="DECIMAL(10,4)")
	private BigDecimal yrate_score;
	
	/**N01因子  **/
	@Column(name="N01_INQ3_TOTAL", columnDefinition="DECIMAL(5,2)")
	private BigDecimal n01_inq3_total;
	
	/**N01得分  **/
	@Column(name="N01_SCORE", columnDefinition="DECIMAL(10,4)")
	private BigDecimal n01_score;
	
	/**P19得分  **/
	@Column(name="P19_CC12_PCODE_A_TIMES_SCORE", columnDefinition="DECIMAL(10,4)")
	private BigDecimal p19_cc12_pcode_a_times_score;
	/** ----------消金房貸3.0 End---------- **/
	/** ----------消金非房貸4.0 Start---------- **/
	
	/**N01因子  **/
	@Column(name="P1_O_COUNTS", columnDefinition="DECIMAL(5,2)")
	private BigDecimal p1_o_counts;
	/**N01得分  **/
	@Column(name="P1_SCORE", columnDefinition="DECIMAL(10,4)")
	private BigDecimal p1_score;
	
	/**目前有擔保餘額 ID 歸戶因子  **/
	@Column(name="LOAN_BAL_S_BYID", columnDefinition="DECIMAL(17,2)")
	private BigDecimal loan_bal_s_byid;
	/**目前有擔保餘額 ID 歸戶得分  **/
	@Column(name="LOAN_BAL_S_BYIDSCORE", columnDefinition="DECIMAL(10,4)")
	private BigDecimal loan_bal_s_byidscore;
	
	/** 近12個月信用卡(每筆)循環信用平均使用率 **/
	@Column(name="R01_CC12_MAX_REVOL_RATE", columnDefinition="DECIMAL(10,4)")
	private BigDecimal r01_cc12_max_revol_rate;
	/** 近12個月信用卡(每筆)循環信用平均使用率 **/
	@Column(name="R01_CC12_MAX_REVOL_RATE_SCORE", columnDefinition="DECIMAL(10,4)")
	private BigDecimal r01_cc12_max_revol_rate_score;
	/** ----------消金非房貸4.0 End---------- **/
	
	/** 取得分行別 **/
	public String getBr_cd() {
		return this.br_cd;
	}
	/** 設定分行別 **/
	public void setBr_cd(String value) {
		this.br_cd = value;
	}

	/** 取得NOTES文件編號 **/
	public String getNoteid() {
		return this.noteid;
	}
	/** 設定NOTES文件編號 **/
	public void setNoteid(String value) {
		this.noteid = value;
	}

	/** 取得客戶統一編號 **/
	public String getCustid() {
		return this.custid;
	}
	/** 設定客戶統一編號 **/
	public void setCustid(String value) {
		this.custid = value;
	}

	/** 取得重複序號 **/
	public String getDupno() {
		return this.dupno;
	}
	/** 設定重複序號 **/
	public void setDupno(String value) {
		this.dupno = value;
	}

	/** 取得評等模型類別 **/
	public String getMowtype() {
		return this.mowtype;
	}
	/** 設定評等模型類別 **/
	public void setMowtype(String value) {
		this.mowtype = value;
	}

	/** 取得模型版本-大版 **/
	public Integer getMowver1() {
		return this.mowver1;
	}
	/** 設定模型版本-大版 **/
	public void setMowver1(Integer value) {
		this.mowver1 = value;
	}

	/** 取得模型版本-小版 **/
	public Integer getMowver2() {
		return this.mowver2;
	}
	/** 設定模型版本-小版 **/
	public void setMowver2(Integer value) {
		this.mowver2 = value;
	}

	/** 取得JCIC查詢日期 YYYY-MM-DD **/
	public Date getJcic_date() {
		return this.jcic_date;
	}
	/** 設定JCIC查詢日期 YYYY-MM-DD **/
	public void setJcic_date(Date value) {
		this.jcic_date = value;
	}

	/** 取得主借款人統一編號(CUSTKEY) **/
	public String getCust_key() {
		return this.cust_key;
	}
	/** 設定主借款人統一編號(CUSTKEY) **/
	public void setCust_key(String value) {
		this.cust_key = value;
	}

	/** 取得相關身分(LNGEFLAG) **/
	public String getLngeflag() {
		return this.lngeflag;
	}
	/** 設定相關身分(LNGEFLAG) **/
	public void setLngeflag(String value) {
		this.lngeflag = value;
	}

	/** 取得當月循環信用餘額(ΣREVOL_BAL) **/
	public BigDecimal getCc_revol_bal() {
		return this.cc_revol_bal;
	}
	/** 設定當月循環信用餘額(ΣREVOL_BAL) **/
	public void setCc_revol_bal(BigDecimal value) {
		this.cc_revol_bal = value;
	}

	/** 取得當月有動用循環信用之信用卡額度(ΣPERM_LIMIT) **/
	public BigDecimal getCc_revol_permit_limit() {
		return this.cc_revol_permit_limit;
	}
	/** 設定當月有動用循環信用之信用卡額度(ΣPERM_LIMIT) **/
	public void setCc_revol_permit_limit(BigDecimal value) {
		this.cc_revol_permit_limit = value;
	}

	/** 取得當月循環信用使用率(REVOL_RATE) **/
	public BigDecimal getR10_revol_rate() {
		return this.r10_revol_rate;
	}
	/** 設定當月循環信用使用率(REVOL_RATE) **/
	public void setR10_revol_rate(BigDecimal value) {
		this.r10_revol_rate = value;
	}

	/** 取得R_10得分 **/
	public BigDecimal getR10_revol_rate_score() {
		return this.r10_revol_rate_score;
	}
	/** 設定R_10得分 **/
	public void setR10_revol_rate_score(BigDecimal value) {
		this.r10_revol_rate_score = value;
	}

	/** 取得當月無擔保授信餘額(仟元) **/
	public BigDecimal getD07_ln_nos_tamt() {
		return this.d07_ln_nos_tamt;
	}
	/** 設定當月無擔保授信餘額(仟元) **/
	public void setD07_ln_nos_tamt(BigDecimal value) {
		this.d07_ln_nos_tamt = value;
	}

	/** 取得D07得分 **/
	public BigDecimal getD07_ln_nos_tamt_score() {
		return this.d07_ln_nos_tamt_score;
	}
	/** 設定D07得分 **/
	public void setD07_ln_nos_tamt_score(BigDecimal value) {
		this.d07_ln_nos_tamt_score = value;
	}

	/** 取得近6個月平均的月信用卡循環信用(元) **/
	public BigDecimal getD15_cc6_avg_rc() {
		return this.d15_cc6_avg_rc;
	}
	/** 設定近6個月平均的月信用卡循環信用(元) **/
	public void setD15_cc6_avg_rc(BigDecimal value) {
		this.d15_cc6_avg_rc = value;
	}

	/** 取得D_15得分 **/
	public BigDecimal getD15_cc6_avg_rc_score() {
		return this.d15_cc6_avg_rc_score;
	}
	/** 設定D_15得分 **/
	public void setD15_cc6_avg_rc_score(BigDecimal value) {
		this.d15_cc6_avg_rc_score = value;
	}

	/** 取得近12個月信用卡繳款狀況出現全額繳清無延遲次數 **/
	public Integer getP19_cc12_pcode_a_times() {
		return this.p19_cc12_pcode_a_times;
	}
	/** 設定近12個月信用卡繳款狀況出現全額繳清無延遲次數 **/
	public void setP19_cc12_pcode_a_times(Integer value) {
		this.p19_cc12_pcode_a_times = value;
	}

	/** 取得婚姻狀況 **/
	public Integer getMarriage() {
		return this.marriage;
	}
	/** 設定婚姻狀況 **/
	public void setMarriage(Integer value) {
		this.marriage = value;
	}

	/** 取得扶養子女數 **/
	public Integer getChildren() {
		return this.children;
	}
	/** 設定扶養子女數 **/
	public void setChildren(Integer value) {
		this.children = value;
	}

	/** 取得學歷 **/
	public Integer getEducation() {
		return this.education;
	}
	/** 設定學歷 **/
	public void setEducation(Integer value) {
		this.education = value;
	}

	/** 取得近12個月新業務申請查詢總家數 **/
	public Integer getN06_inq12_napp_bank() {
		return this.n06_inq12_napp_bank;
	}
	/** 設定近12個月新業務申請查詢總家數 **/
	public void setN06_inq12_napp_bank(Integer value) {
		this.n06_inq12_napp_bank = value;
	}

	/** 取得N06得分 **/
	public BigDecimal getN06_inq12_napp_bank_score() {
		return this.n06_inq12_napp_bank_score;
	}
	/** 設定N06得分 **/
	public void setN06_inq12_napp_bank_score(BigDecimal value) {
		this.n06_inq12_napp_bank_score = value;
	}

	/** 取得評等日期 **/
	public Date getRating_date() {
		return this.rating_date;
	}
	/** 設定評等日期 **/
	public void setRating_date(Date value) {
		this.rating_date = value;
	}

	/** 取得文件狀態 **/
	public String getDocstatus() {
		return this.docstatus;
	}
	/** 設定文件狀態 **/
	public void setDocstatus(String value) {
		this.docstatus = value;
	}

	/** 取得上傳資料日期 **/
	public Date getData_src_dt() {
		return this.data_src_dt;
	}
	/** 設定上傳資料日期 **/
	public void setData_src_dt(Date value) {
		this.data_src_dt = value;
	}

	/** 取得近12個月信用卡(每筆)循環信用平均使用率 **/
	public BigDecimal getR01_cc12_revol_rate() {
		return this.r01_cc12_revol_rate;
	}
	/** 設定近12個月信用卡(每筆)循環信用平均使用率 **/
	public void setR01_cc12_revol_rate(BigDecimal value) {
		this.r01_cc12_revol_rate = value;
	}

	/** 取得近12個月信用卡(每筆)循環信用平均使用率得分 **/
	public BigDecimal getR01_cc12_revol_rate_score() {
		return this.r01_cc12_revol_rate_score;
	}
	/** 設定近12個月信用卡(每筆)循環信用平均使用率得分 **/
	public void setR01_cc12_revol_rate_score(BigDecimal value) {
		this.r01_cc12_revol_rate_score = value;
	}

	/** 取得夫妻年收入(萬元) **/
	public BigDecimal getHincome_reg() {
		return this.hincome_reg;
	}
	/** 設定夫妻年收入(萬元) **/
	public void setHincome_reg(BigDecimal value) {
		this.hincome_reg = value;
	}

	/** 取得夫妻年收入(萬元)得分 **/
	public BigDecimal getHincome_reg_score() {
		return this.hincome_reg_score;
	}
	/** 設定夫妻年收入(萬元)得分 **/
	public void setHincome_reg_score(BigDecimal value) {
		this.hincome_reg_score = value;
	}

	/** 取得近12個月信用卡繳款狀況出現不良繳款紀錄或使用循環信用的次數 **/
	public Integer getP69_cc12_delay_rc_times() {
		return this.p69_cc12_delay_rc_times;
	}
	/** 設定近12個月信用卡繳款狀況出現不良繳款紀錄或使用循環信用的次數 **/
	public void setP69_cc12_delay_rc_times(Integer value) {
		this.p69_cc12_delay_rc_times = value;
	}

	/** 取得近6個月信用卡繳款狀況出現全額繳清無延遲次數 **/
	public Integer getP25_cc6_pcode_a_times() {
		return this.p25_cc6_pcode_a_times;
	}
	/** 設定近6個月信用卡繳款狀況出現全額繳清無延遲次數 **/
	public void setP25_cc6_pcode_a_times(Integer value) {
		this.p25_cc6_pcode_a_times = value;
	}

	/** 取得近6個月信用卡繳款狀況出現全額繳清無延遲次數得分 **/
	public BigDecimal getP25_cc6_pcode_a_times_score() {
		return this.p25_cc6_pcode_a_times_score;
	}
	/** 設定近6個月信用卡繳款狀況出現全額繳清無延遲次數得分 **/
	public void setP25_cc6_pcode_a_times_score(BigDecimal value) {
		this.p25_cc6_pcode_a_times_score = value;
	}

	/** 取得婚姻_學歷得分 **/
	public BigDecimal getMarr_edu_score() {
		return this.marr_edu_score;
	}
	/** 設定婚姻_學歷得分 **/
	public void setMarr_edu_score(BigDecimal value) {
		this.marr_edu_score = value;
	}

	/** 取得P69_P19得分 **/
	public BigDecimal getP69_p19_score() {
		return this.p69_p19_score;
	}
	/** 設定P69_P19得分 **/
	public void setP69_p19_score(BigDecimal value) {
		this.p69_p19_score = value;
	}
	
	/** 取得序號/帳號 **/
	public String getAcct_key() {
		return this.acct_key;
	}
	/** 設定序號/帳號 **/
	public void setAcct_key(String value) {
		this.acct_key = value;
	}

	/** 設定個人年收入（萬元） **/
	public void setYpay(BigDecimal ypay) {
		this.ypay = ypay;
	}
	/** 取得個人年收入（萬元） **/
	public BigDecimal getYpay() {
		return ypay;
	}
	/** 設定個人年收入（萬元）分數 **/
	public void setYpay_score(BigDecimal ypay_score) {
		this.ypay_score = ypay_score;
	}
	/** 取得個人年收入（萬元）分數 **/
	public BigDecimal getYpay_score() {
		return ypay_score;
	}
	/** 設定年資 **/
	public void setSeniority(BigDecimal seniority) {
		this.seniority = seniority;
	}
	/** 取得年資 **/
	public BigDecimal getSeniority() {
		return seniority;
	}
	/** 設定年資分數 **/
	public void setSeniority_score(BigDecimal seniority_score) {
		this.seniority_score = seniority_score;
	}
	/** 取得年資分數 **/
	public BigDecimal getSeniority_score() {
		return seniority_score;
	}
	/** 設定學歷分數 **/
	public void setEducation_n_score(BigDecimal education_n_score) {
		this.education_n_score = education_n_score;
	}
	/** 取得學歷分數 **/
	public BigDecimal getEducation_n_score() {
		return education_n_score;
	}
	/** 設定聯徵查詢月份當時無擔保授信往來家數(排除學生助學貸款(Z)、本行貸款) D63 **/
	public void setD63_ln_nos_bank(BigDecimal d63_ln_nos_bank) {
		this.d63_ln_nos_bank = d63_ln_nos_bank;
	}
	/** 取得聯徵查詢月份當時無擔保授信往來家數(排除學生助學貸款(Z)、本行貸款) D63 **/
	public BigDecimal getD63_ln_nos_bank() {
		return d63_ln_nos_bank;
	}
	/** 設定聯徵查詢月份當時無擔保授信往來家數(排除學生助學貸款(Z)、本行貸款)分數  D63_score**/
	public void setD63_score(BigDecimal d63_score) {
		this.d63_score = d63_score;
	}
	/** 取得聯徵查詢月份當時無擔保授信往來家數(排除學生助學貸款(Z)、本行貸款)分數  D63_score**/
	public BigDecimal getD63_score() {
		return d63_score;
	}
	/** 設定近6個月信用卡使用循環信用的月份數 A21 **/
	public void setA21_cc6_rc_use_month(BigDecimal a21_cc6_rc_use_month) {
		this.a21_cc6_rc_use_month = a21_cc6_rc_use_month;
	}
	/** 取得近6個月信用卡使用循環信用的月份數 A21 **/
	public BigDecimal getA21_cc6_rc_use_month() {
		return a21_cc6_rc_use_month;
	}
	/** 設定近6個月信用卡使用循環信用的月份數分數 A21_score **/
	public void setA21_score(BigDecimal a21_score) {
		this.a21_score = a21_score;
	}
	/** 取得近6個月信用卡使用循環信用的月份數分數 A21_score **/
	public BigDecimal getA21_score() {
		return a21_score;
	}
	/** 設定近6個月信用卡使用循環信用的家數 A11 **/
	public void setA11_cc6_rc_use_bank(BigDecimal a11_cc6_rc_use_bank) {
		this.a11_cc6_rc_use_bank = a11_cc6_rc_use_bank;
	}
	/** 取得近6個月信用卡使用循環信用的家數 A11 **/
	public BigDecimal getA11_cc6_rc_use_bank() {
		return a11_cc6_rc_use_bank;
	}
	/** 設定近6個月信用卡使用循環信用的家數分數 A11_score **/
	public void setA11_score(BigDecimal a11_score) {
		this.a11_score = a11_score;
	}
	/** 取得近6個月信用卡使用循環信用的家數分數 A11_score **/
	public BigDecimal getA11_score() {
		return a11_score;
	}
	/** 設定聯徵查詢月份當時授信繳款記錄小於等於6次旗標 D53 **/
	public void setD53_ln_6_times_flag(BigDecimal d53_ln_6_times_flag) {
		this.d53_ln_6_times_flag = d53_ln_6_times_flag;
	}
	/** 取得聯徵查詢月份當時授信繳款記錄小於等於6次旗標 D53 **/
	public BigDecimal getD53_ln_6_times_flag() {
		return d53_ln_6_times_flag;
	}
	/** 設定聯徵查詢月份當時授信繳款記錄小於等於6次旗標分數 D53_score **/
	public void setD53_score(BigDecimal d53_score) {
		this.d53_score = d53_score;
	}
	/** 取得聯徵查詢月份當時授信繳款記錄小於等於6次旗標分數 D53_score **/
	public BigDecimal getD53_score() {
		return d53_score;
	}
	/** 設定職業大類 **/
	public void setOccupation_1(Integer value) {
		this.occupation_1 = value;
	}
	/** 取得職業大類 **/
	public Integer getOccupation_1() {
		return this.occupation_1;
	}
	/** 設定職業大類得分 **/
	public void setOccupation_1_score(BigDecimal value) {
		this.occupation_1_score = value;
	}
	/** 取得職業大類得分 **/
	public BigDecimal getOccupation_1_score() {
		return this.occupation_1_score;
	}
	/** 取得 個人年所得(年薪+其他收入)(萬元)  **/
	public BigDecimal getPincome() {
		return pincome;
	}
	/** 設定 個人年所得(年薪+其他收入)(萬元)  **/
	public void setPincome(BigDecimal pincome) {
		this.pincome = pincome;
	}
	/** 取得  個人年所得(年薪+其他收入)分數  **/
	public BigDecimal getPincome_score() {
		return pincome_score;
	}
	/** 設定 個人年所得(年薪+其他收入)分數  **/
	public void setPincome_score(BigDecimal pincome_score) {
		this.pincome_score = pincome_score;
	}
	/** 取得  近6個月信用卡繳款狀況出現不良繳款紀錄或使用循環信用之次數  **/
	public BigDecimal getP68_cc6_delay_rc_times() {
		return p68_cc6_delay_rc_times;
	}
	/** 設定 近6個月信用卡繳款狀況出現不良繳款紀錄或使用循環信用之次數  **/
	public void setP68_cc6_delay_rc_times(BigDecimal p68_cc6_delay_rc_times) {
		this.p68_cc6_delay_rc_times = p68_cc6_delay_rc_times;
	}
	/** 取得 P68_P19得分  **/
	public BigDecimal getP68_p19_score() {
		return p68_p19_score;
	}
	/** 設定 P68_P19得分  **/
	public void setP68_p19_score(BigDecimal p68_p19_score) {
		this.p68_p19_score = p68_p19_score;
	}
	/** 取得近12個月新業務申請查詢次數,計算方式-天數別(30天內算1次)  **/
	public BigDecimal getN18_inq12_by30d() {
		return n18_inq12_by30d;
	}
	/** 設定近12個月新業務申請查詢次數,計算方式-天數別(30天內算1次)  **/
	public void setN18_inq12_by30d(BigDecimal n18_inq12_by30d) {
		this.n18_inq12_by30d = n18_inq12_by30d;
	}
	/** 取得N18得分  **/
	public BigDecimal getN18_score() {
		return n18_score;
	}
	/** 設定N18得分  **/
	public void setN18_score(BigDecimal n18_score) {
		this.n18_score = n18_score;
	}
	/** 取得近12個月新業務申請查詢次數(排除近三個月本行查詢),計算方式-天數別(30天內算1次)  **/
	public BigDecimal getN22_inq12_by30d() {
		return n22_inq12_by30d;
	}
	/** 設定近12個月新業務申請查詢次數(排除近三個月本行查詢),計算方式-天數別(30天內算1次)  **/
	public void setN22_inq12_by30d(BigDecimal n22_inq12_by30d) {
		this.n22_inq12_by30d = n22_inq12_by30d;
	}
	/** 取得N22得分  **/
	public BigDecimal getN22_score() {
		return n22_score;
	}
	/** 設定N22得分  **/
	public void setN22_score(BigDecimal n22_score) {
		this.n22_score = n22_score;
	}
	/** 取得聯徵查詢月份當月無擔保授信餘額(不含學生助學貸款)除以個人月收入  **/
	public BigDecimal getD07_div_pincome() {
		return d07_div_pincome;
	}
	/** 設定聯徵查詢月份當月無擔保授信餘額(不含學生助學貸款)除以個人月收入  **/
	public void setD07_div_pincome(BigDecimal d07_div_pincome) {
		this.d07_div_pincome = d07_div_pincome;
	}
	/** 取得D07_DIV_PINCOME得分  **/
	public BigDecimal getD07_div_pincome_score() {
		return d07_div_pincome_score;
	}
	/** 設定D07_DIV_PINCOME得分  **/
	public void setD07_div_pincome_score(BigDecimal d07_div_pincome_score) {
		this.d07_div_pincome_score = d07_div_pincome_score;
	}
	/** 取得從債務(企業關係)授信餘額(仟元)  **/
	public BigDecimal getZ03_acc_debt_enterprise() {
		return z03_acc_debt_enterprise;
	}
	/** 設定從債務(企業關係)授信餘額(仟元)  **/
	public void setZ03_acc_debt_enterprise(BigDecimal z03_acc_debt_enterprise) {
		this.z03_acc_debt_enterprise = z03_acc_debt_enterprise;
	}
	/** 取得Z03得分  **/
	public BigDecimal getZ03_score() {
		return z03_score;
	}
	/** 設定Z03得分  **/
	public void setZ03_score(BigDecimal z03_score) {
		this.z03_score = z03_score;
	}

	/** 取得卡友貸旗標 */
	public String getC_flag() {
		return c_flag;
	}
	/** 設定卡友貸旗標 */
	public void setC_flag(String c_flag) {
		this.c_flag = c_flag;
	}
	
	/** 取得個人負債比率分數  **/
	public BigDecimal getDrate_score() {
		return drate_score;
	}
	/** 設定個人負債比率分數  **/
	public void setDrate_score(BigDecimal drate_score) {
		this.drate_score = drate_score;
	}
	/** ----------消金房貸3.0 Start---------- **/
	/** 取得職稱 */
	public String getDesignation() {
		return designation;
	}
	/** 設定職稱 */
	public void setDesignation(String designation) {
		this.designation = designation;
	}
	
	/** 取得職稱得分  **/
	public BigDecimal getDesignation_score() {
		return designation_score;
	}
	/** 設定職稱得分  **/
	public void setDesignation_score(BigDecimal designation_score) {
		this.designation_score = designation_score;
	}
	
	
	/** 取得D42因子  **/
	public BigDecimal getD42_avg_limit() {
		return d42_avg_limit;
	}
	/** 設定D42因子  **/
	public void setD42_avg_limit(BigDecimal d42_avg_limit) {
		this.d42_avg_limit = d42_avg_limit;
	}
	/** 取得D42得分  **/
	public BigDecimal getD42_score() {
		return d42_score;
	}
	/** 設定D42得分  **/
	public void setD42_score(BigDecimal d42_score) {
		this.d42_score = d42_score;
	}
	
	/** 取得P68得分  **/
	public BigDecimal getP68_score() {
		return p68_score;
	}
	/** 設定P68得分  **/
	public void setP68_score(BigDecimal p68_score) {
		this.p68_score = p68_score;
	}
	
	/** 取得夫妻負債比(YRATE)得分  **/
	public BigDecimal getYrate_score() {
		return yrate_score;
	}
	/** 設定夫妻負債比(YRATE)得分  **/
	public void setYrate_score(BigDecimal yrate_score) {
		this.yrate_score = yrate_score;
	}
	
	/** 取得N01因子  **/
	public BigDecimal getN01_inq3_total() {
		return n01_inq3_total;
	}
	/** 設定N01因子  **/
	public void setN01_inq3_total(BigDecimal n01_inq3_total) {
		this.n01_inq3_total = n01_inq3_total;
	}
	/** 取得N01得分  **/
	public BigDecimal getN01_score() {
		return n01_score;
	}
	/** 設定N01得分  **/
	public void setN01_score(BigDecimal n01_score) {
		this.n01_score = n01_score;
	}
	
	/** 取得P19得分  **/
	public BigDecimal getP19_cc12_pcode_a_times_score() {
		return p19_cc12_pcode_a_times_score;
	}
	/** 設定P19得分  **/
	public void setP19_cc12_pcode_a_times_score(BigDecimal p19_cc12_pcode_a_times_score) {
		this.p19_cc12_pcode_a_times_score = p19_cc12_pcode_a_times_score;
	}
	/** ----------消金房貸3.0 End---------- **/
/** ----------消金非房貸4.0 Start---------- **/
	
	/** 取得P1因子  **/
	public BigDecimal getP1_o_counts() {
		return p1_o_counts;
	}
	/** 設定P1因子  **/
	public void setP1_o_counts(BigDecimal p1_o_counts) {
		this.p1_o_counts = p1_o_counts;
	}
	/** 取得P1得分  **/
	public BigDecimal getP1_score() {
		return p1_score;
	}
	/** 設定P1得分  **/
	public void setP1_score(BigDecimal p1_score) {
		this.p1_score = p1_score;
	}
	
	/** 取得目前有擔保餘額 ID 歸戶因子  **/
	public BigDecimal getLoan_bal_s_byid() {
		return loan_bal_s_byid;
	}
	/** 設定目前有擔保餘額 ID 歸戶因子  **/
	public void setLoan_bal_s_byid(BigDecimal loan_bal_s_byid) {
		this.loan_bal_s_byid = loan_bal_s_byid;
	}
	/** 取得目前有擔保餘額 ID 歸戶得分  **/
	public BigDecimal getLoan_bal_s_byidscore() {
		return loan_bal_s_byidscore;
	}
	/** 設定目前有擔保餘額 ID 歸戶得分  **/
	public void setLoan_bal_s_byidscore(BigDecimal loan_bal_s_byidscore) {
		this.loan_bal_s_byidscore = loan_bal_s_byidscore;
	}
	
	/** 取得近12個月信用卡(每筆)循環信用平均使用率 **/
	public BigDecimal getR01_cc12_max_revol_rate() {
		return this.r01_cc12_max_revol_rate;
	}
	/** 設定近12個月信用卡(每筆)循環信用平均使用率 **/
	public void setR01_cc12_max_revol_rate(BigDecimal value) {
		this.r01_cc12_max_revol_rate = value;
	}
	/** 取得近12個月信用卡(每筆)循環信用平均使用率得分 **/
	public BigDecimal getR01_cc12_max_revol_rate_score() {
		return this.r01_cc12_max_revol_rate_score;
	}
	/** 設定近12個月信用卡(每筆)循環信用平均使用率得分 **/
	public void setR01_cc12_max_revol_rate_score(BigDecimal value) {
		this.r01_cc12_max_revol_rate_score = value;
	}
	
	/** ----------消金非房貸4.0 End---------- **/

	
	
}
