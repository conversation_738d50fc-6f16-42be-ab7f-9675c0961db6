/* 
 * L260S01DDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L260S01D;

/** 公司訪問紀錄表主檔  **/
public interface L260S01DDao extends IGenericDao<L260S01D> {

	L260S01D findByOid(String oid);
	
	List<L260S01D> findByMainId(String mainId, boolean notIncDel);
	
	L260S01D findByUniqueKey(String mainId);

	List<L260S01D> findByIndex01(String mainId);
}