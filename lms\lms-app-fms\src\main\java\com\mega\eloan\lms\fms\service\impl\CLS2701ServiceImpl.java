package com.mega.eloan.lms.fms.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.lms.dao.C900M01IDao;
import com.mega.eloan.lms.dao.C900S01IDao;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.fms.service.CLS2701Service;
import com.mega.eloan.lms.model.C900M01I;
import com.mega.eloan.lms.model.C900S01I;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

@Service
public class CLS2701ServiceImpl extends AbstractCapService implements
		CLS2701Service {
	private static final Logger logger = LoggerFactory
			.getLogger(CLS2701ServiceImpl.class);

	@Resource
	L120M01ADao l120m01aDao;
	
	@Resource
	C900M01IDao c900m01iDao;
	
	@Resource
	C900S01IDao c900s01iDao;
	
	@Resource
	TempDataService tempDataService;
	
	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof C900M01I) {
					C900M01I c900m01i = (C900M01I) model;
					if (Util.isNotEmpty(c900m01i.getOid()) ) {
						c900m01i.setUpdater(user.getUserId());
						c900m01i.setUpdateTime(CapDate.getCurrentTimestamp());
					}
					c900m01iDao.save(c900m01i);
					
					if (Util.notEquals("Y", SimpleContextHolder.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(c900m01i.getMainId());
					}
				}else if (model instanceof C900S01I) {
					C900S01I c900s01i = (C900S01I) model;
					if (Util.isNotEmpty(c900s01i.getOid()) ) {
						c900s01i.setUpdater(user.getUserId());
						c900s01i.setUpdateTime(CapDate.getCurrentTimestamp());
					}
					c900s01iDao.save(c900s01i);

				}
			}
		}
	}

	@Override
	public void delete(GenericBean... entity) {
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof C900M01I) {
					C900M01I c900m01i = (C900M01I) model;
					if (Util.isNotEmpty(c900m01i.getOid()) ) {
						//刪除資料
						c900m01iDao.delete(c900m01i);
					}
				}else if (model instanceof C900S01I) {
					C900S01I c900s01i = (C900S01I) model;
					if (Util.isNotEmpty(c900s01i.getOid()) ) {
						//刪除資料
						c900s01iDao.delete(c900s01i);
					}
				}
			}
		}
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == L120M01A.class) {
			return l120m01aDao.findPage(search);
		}else if (clazz == C900M01I.class) {
			return c900m01iDao.findPage(search);
		}else if (clazz == C900S01I.class) {
			return c900s01iDao.findPage(search);
		}
		return null;
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		if (clazz == C900M01I.class) {
			return (T) c900m01iDao.findByOid(oid);
		}else if (clazz == C900S01I.class) {
			return (T) c900s01iDao.findByOid(oid);
		}
		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		// 
		return null;
	}
	
	@Override
	public C900M01I findC900M01I_oid(String oid){
		return c900m01iDao.findByOid(oid);
	}
	
	@Override
	public C900M01I findC900M01I_mainId(String mainId){
		return c900m01iDao.findByMainId(mainId);
	}
	
	@Override
	public C900S01I findC900S01I_oid(String oid){
		return c900s01iDao.findByOid(oid);
	}
	
	@Override
	public C900S01I findC900S01I_mainId_caseMainId(String mainId, String caseMainId){
		return c900s01iDao.findByUniqueKey(mainId, caseMainId);
	}
	
	@Override
	public void saveC900S01I(List<C900S01I> list){
		c900s01iDao.save(list);
	}
	
	@Override
	public List<C900S01I> findC900S01I_mainId(String mainId){
		return c900s01iDao.findByMainId(mainId);
	}
	
	@Override
	public List<L120M01A> findL120M01A_oid(String[] oid_arr){
		return l120m01aDao.findByOids(oid_arr);
	}
}
